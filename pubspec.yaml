name: turing_art
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.8.0

# windows证书签名的SHA1值
signature_sha1: "53b2110e9dacbbfeb86b6d3442d6e1d0c2a7a012"

environment:
  sdk: ^3.3.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  sentry:
    path: local_packages/sentry-dart-8.14.2/dart
  sentry_flutter:
    path: local_packages/sentry-dart-8.14.2/flutter
  flutter:
    sdk: flutter
  flutter_unity_widget: ^2022.2.0 # 引入flutter_unity_widget依赖

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_localizations:
    sdk: flutter
  intl: any
  freezed_annotation: ^2.4.1
  json_annotation: ^4.9.0
  # 添加权限处理包
  pg_permission_handler_nowindows:
    hosted: http://mig-ultra.camera360.com:4000
    version: ^11.3.0
  # 添加路径处理包
  path_provider: ^2.1.4
  # 添加相册管理器
  photo_manager: ^3.0.0
  # 添加设备信息插件
  device_info_plus: ^10.1.2
  # 导航控件
  go_router: ^14.6.2
  # 网络请求
  dio: ^5.5.1
  # Retrofit 网络请求封装
  retrofit: ^4.4.0
  package_info_plus: ^4.2.0
  # 自动更新
  #  auto_updater: ^1.0.0
  # 数据库
  drift: ^2.16.0 # drift 数据库api
  sqlite3_flutter_libs: ^0.5.27 # sqlite
  path: ^1.9.0
  sqflite_common_ffi: ^2.3.3
  shared_preferences: ^2.2.3
  uuid: ^4.3.3
  tuple: ^2.0.2 # 元组支持
  # 图片选择器
  image_picker: ^0.8.9
  file_picker: ^6.1.1
  provider: ^6.0.5
  flutter_smart_dialog: ^4.9.6

  logger: ^2.5.0
  # WebView 相关依赖
  webview_flutter: ^4.7.0
  connectivity_plus: ^5.0.2
  url_launcher: ^6.2.5 # 或最新版本
  disk_space_plus: ^0.2.4

  # 视频播放器
  video_player: ^2.8.2

  pg_desktop_multi_window:
    path: ./plugins/pg_desktop_multi_window

  pg_ops_sdk:
    hosted: http://mig-ultra.camera360.com:4000
    version: ^0.0.23

  desktop_drop: ^0.4.4
  cross_file: ^0.3.3+8 # 需要添加这个依赖来使用 XFile

  lpinyin: ^2.0.3 # 或最新版本
  qr_flutter: ^4.1.0
  webview_windows: ^0.4.0
  yaml: ^3.1.2

  pg_turing_collect_event:
    hosted: http://mig-ultra.camera360.com:4000
    version: ^0.1.5

  cached_network_image: ^3.4.0
  flutter_cache_manager: ^3.4.0
  crypto: ^3.0.3
  ffi: ^2.1.3
  synchronized: ^3.1.0
  #  集合扩展
  collection: ^1.18.0

dev_dependencies:
  sentry_dart_plugin:
    path: local_packages/sentry-dart-plugin-2.4.1
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  freezed: ^2.4.7
  json_serializable: ^6.7.1
  build_runner: ^2.4.8
  # Retrofit 代码生成
  retrofit_generator: ^7.0.8
  drift_dev: ^2.16.0 #drift自动生成代码工具
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
  
  fonts:
    - family: DouyinSans
      fonts:
        - asset: assets/fonts/DouyinSans.otf

  assets:
    - assets/images/login_back/
    - assets/images/login_back_pc/
    - assets/images/
    - assets/icons/
    - assets/static/
    - assets/novice_guid/
    - lib/ui/core/ui/desktop/chat.html
    - pubspec.yaml

flutter_launcher_icons:
  android: true
  ios: true
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 256 # 可选，默认 48
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
  # 基础配置
  image_path: "assets/icons/app_icon.png"

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  project: flutter
  org: pinguo
