# TuringArt 打包脚本说明文档

## 脚本调用关系图

```mermaid
graph LR
    %% 主流程
    A[packager_launcher.dart] --> B[win_package_runner.bat]
    A --> C[win_package_upload.bat]
    
    %% 打包流程
    B --> D[prepare_unity.dart]
    B --> E[windows/packaging/copy_dll.dart]
    B --> F[win_package_signature.bat]
    B --> G[win_vmprotect_encryption]
    B --> H[generate_inno_setup.bat]
    
    %% Flutter相关调用
    B --> L[flutter clean]
    B --> M[flutter pub get]
    B --> N[flutter build windows]
    
    %% 上传流程
    C --> I[nas_config.json]
    C --> J[Git Commands]
    C --> K[企业微信 WebHook]
    
    %% 子模块管理
    A --> O[Git Submodule Commands]
    O --> P[unityoutputwin7]
    O --> Q[unityoutputwin10p]
```

## 主要脚本说明

### 1. packager_launcher.dart
- **功能**：主入口脚本，负责整体打包流程控制
- **主要职责**：
  - 管理子模块（unityoutputwin7/unityoutputwin10p）
  - 调用打包和上传脚本
  - 处理版本号和时间戳
  - 重命名和移动构建产物
  - 签名安装包

### 2. win_package_runner.bat
- **功能**：执行应用程序的编译和打包
- **主要步骤**：
  - Flutter 清理和依赖安装
  - Unity 资源准备
  - DLL 文件复制
  - 应用程序签名
  - 可执行文件加密
  - 生成安装包

### 3. win_package_upload.bat
- **功能**：处理发布和通知
- **主要步骤**：
  - 读取 NAS 配置
  - 上传文件到 NAS
  - 获取 Git 提交记录
  - 发送企业微信通知

## 配置文件

### nas_config.json
- **用途**：存储 NAS 相关配置信息
- **包含字段**：
  - nas_ip：NAS 服务器 IP
  - share_name：共享文件夹名称
  - nas_user：用户名
  - nas_password：密码
  - win7_nas_id：Win7 版本 NAS ID
  - win10p_nas_id：Win10/11 版本 NAS ID

### package_unity_config.json
- **用途**：Unity 打包配置
- **包含字段**：
  - projectName：项目名称
  - isPackage：是否打包
  - isWin7：是否为 Win7 版本
  - isRelease：是否为发布版本
  - sourceDataFile：源数据文件

## 使用说明

1. 运行打包：
```bash
dart packager_launcher.dart [--release]
```

2. 参数说明：
- `--release`：可选参数，指定为发布版本

3. 输出目录：
- 打包文件位于 `dist/{version}/{timestamp}/{platform}/` 目录下

## 注意事项

1. 确保已安装所有必要的工具：
   - Flutter
   - Git
   - Dart
   - 签名工具
   - VMProtect

2. 配置文件要求：
   - nas_config.json 必须正确配置
   - 确保有足够的权限访问 NAS

3. 网络要求：
   - 需要能够访问 NAS
   - 需要能够访问企业微信 API

4. 版本控制：
   - 确保所有子模块都已正确更新
   - 版本号从 pubspec.yaml 中读取 