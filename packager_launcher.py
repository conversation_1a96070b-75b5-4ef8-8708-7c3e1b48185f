#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图灵精修打包发布工具

这个脚本用于自动化构建与发布图灵精修软件包，支持多平台构建、签名和上传。
可以从任意目录调用此脚本，它会自动处理路径问题。

使用示例:
    python packager_launcher.py --release --sign --platform=universal
    python packager_launcher.py --help
"""

import os
import sys
import json
import time
import subprocess
import shutil
import argparse
from pathlib import Path

# 导入企业微信通知模块
try:
    from qy_wechat import BuildNotificationSender
except ImportError:
    print('⚠️ 警告：无法导入企业微信通知模块，将跳过消息发送功能')
    BuildNotificationSender = None


def main():
    # 设置全局变量禁用缓冲
    os.environ["PYTHONUNBUFFERED"] = "1"
    
    # 修复Windows控制台的编码问题
    try:
        # Python 3.7+支持reconfigure方法
        sys.stdout.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
    except AttributeError:
        # 适用于旧版Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        
    # 处理命令行参数
    parser = argparse.ArgumentParser(
        description='图灵精修打包发布工具',
        epilog="""
示例:
  python packager_launcher.py --release                # 构建所有平台的发布版本
  python packager_launcher.py --release --sign         # 构建所有平台的发布版本并签名
  python packager_launcher.py --platform=win7          # 仅构建Windows 7平台的调试版本
  python packager_launcher.py --skip-upload            # 构建后不上传到NAS
        """
    )
    parser.add_argument('--release', action='store_true', help='是否为发布版本，默认为调试版本')
    parser.add_argument('--sign', action='store_true', help='是否签名应用程序')
    parser.add_argument('--encrypt', action='store_true', help='是否加密程序包')
    parser.add_argument('--skip-upload', action='store_true', help='是否跳过上传步骤')
    parser.add_argument('--platform', choices=['all', 'win7', 'universal'], default='all',
                        help='指定构建平台，可选值: win7, universal, all(默认)')
    parser.add_argument('--fast-build', action='store_true', help='启用极速构建模式，直接从Unity构建产物访问文件')
    parser.add_argument('--unity-build-base-path', help='Unity构建产物基础路径（极速构建模式使用）')
    parser.add_argument('--version', action='version', version='%(prog)s 1.0.0')
    
    args = parser.parse_args()
    
    # 获取脚本所在目录的绝对路径，解决调用路径问题
    script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    
    # 切换到脚本所在目录作为工作目录
    os.chdir(script_dir)
    print(f'📂 工作目录: {os.getcwd()}')
    
    is_release = args.release
    do_sign = args.sign
    do_encrypt = args.encrypt
    skip_upload = args.skip_upload
    platform_arg = args.platform.lower()
    fast_build = args.fast_build
    unity_build_base_path = args.unity_build_base_path

    # 打印构建配置信息
    print(f'🔧 构建配置:')
    print(f'   - 版本类型: {"发布版" if is_release else "调试版"}')
    print(f'   - 是否签名: {"是" if do_sign else "否"}')
    print(f'   - 是否加密: {"是" if do_encrypt else "否"}')
    print(f'   - 是否上传: {"否" if skip_upload else "是"}')
    print(f'   - 构建平台: {platform_arg}')
    print(f'   - 极速构建: {"是" if fast_build else "否"}')
    if fast_build and unity_build_base_path:
        print(f'   - Unity构建产物路径: {unity_build_base_path}')

    # 解析版本和生成时间戳
    version = parse_version()
    timestamp = generate_timestamp()
    signature_sha1 = parse_signature_sha1()
    print(f'📌 版本信息: {version}, 时间戳: {timestamp}')

    # 根据平台参数筛选配置
    filtered_configs = []
    configs = [
        {
            'platform': 'universal',  # 标识构建平台，universal表示通用Windows平台
            'config': {
                'projectName': 'turing_art',  # 项目名称，用于生成构建产物
                'isPackage': True,  # 是否打包发布版本
                'isWin7': False,  # 是否为Windows 7平台构建
                'isRelease': is_release,  # 是否为发布版本
                "sourceDataFile": "Retouch_Data"  # Unity资源数据包输出文件夹名称
            },
        },
        {
            'platform': 'win7',  # 标识构建平台，win7表示Windows 7平台
            'config': {
                'projectName': 'turing_art',  # 项目名称
                'isPackage': True,
                'isRelease': is_release,
                'isWin7': True,
                "sourceDataFile": "Retouch_Data"
            },
        }
    ]

    # 根据指定平台筛选配置
    for config in configs:
        if platform_arg == 'all' or config['platform'] == platform_arg:
            filtered_configs.append(config)

    if not filtered_configs:
        print('❌ 错误：无效的平台参数。请使用 --platform=win7 或 --platform=universal 或 --platform=all')
        sys.exit(1)

    # 删除输出包目录
    project_root = os.getcwd()
    dist_dir = os.path.join(project_root, "dist")
    if os.path.exists(dist_dir):
        print(f'🧹 清理输出目录: {dist_dir}')
        try:
            shutil.rmtree(dist_dir)
            print(f'✅ 输出目录已删除')
        except Exception as e:
            print(f'⚠️ 删除输出目录失败: {e}')
            sys.exit(1)
    #清空build目录
    build_dir = os.path.join(project_root, "build")
    if os.path.exists(build_dir):
        print(f'🧹 清理build目录: {build_dir}')
        try:
            shutil.rmtree(build_dir)
            print(f'✅ build目录已删除')
        except Exception as e:
            print(f'⚠️ 删除build目录失败: {e}')
            sys.exit(1)

    # 处理每个配置
    for config in filtered_configs:
        build_platform = config['platform']
        build_type = 'release' if is_release else 'debug'

        # 如果是极速构建模式，添加相关配置
        if fast_build:
            # 极速构建模式：首先检查unity_build_base_path是否存在
            if not unity_build_base_path:
                print(f'❌ 极速构建模式需要提供Unity构建产物基础路径，但未提供')
                sys.exit(1)
            
            print(f'⚡ 极速构建模式：配置Unity构建产物路径')
            
            # 根据平台确定Unity构建产物路径
            if build_platform == 'win7':
                unity_artifacts_path = os.path.join(unity_build_base_path, "Windows_Win7_Intel64")
            else:  # universal (win10)
                unity_artifacts_path = os.path.join(unity_build_base_path, "Windows_Win10_Intel64")
            
            metadata_file_path = os.path.join(unity_artifacts_path, "build_metadata.json")
            
            # 检查路径是否存在
            if not os.path.exists(metadata_file_path):
                print(f'❌ 元数据文件不存在: {metadata_file_path}')
                sys.exit(1)
            
            gameassembly_dll_path = os.path.join(unity_artifacts_path, "GameAssembly.dll")
            if not os.path.exists(gameassembly_dll_path):
                print(f'❌ GameAssembly.dll文件不存在: {gameassembly_dll_path}')
                sys.exit(1)
            
            print(f'✅ Unity构建产物路径验证通过: {unity_artifacts_path}')
            print(f'✅ 元数据文件存在: {metadata_file_path}')
            
            # 添加极速构建配置
            config['config']['fast_build'] = True
            config['config']['unity_artifacts_path'] = unity_artifacts_path
            config['config']['metadata_file_path'] = metadata_file_path
            
            print(f'⚡ 极速构建模式：跳过子库更新')
        else:
            # 正常构建模式：更新子库
            print(f'🔄 开始更新 {build_platform} 对应的子库')
            update_submodules(build_platform, is_release)
            
            is_win7 = config['config']['isWin7']
            # 获取当前脚本所在目录
            current_dir = os.getcwd()
            # 设置metadata_file_path
            base_dir = 'release' if is_release else 'develop'
            metadata_file_path = os.path.join(current_dir, f'unityoutput{"win7" if is_win7 else "win10p"}', base_dir, 'build_metadata.json')
            config['config']['metadata_file_path'] = metadata_file_path

        print(f'🚀 开始构建 {build_platform}')
        generate_config(config['config'])
        output_file_path = run_package_script(version, timestamp, build_platform, build_type, do_encrypt, do_sign, signature_sha1)
        rename_and_move_artifacts(version, timestamp, build_platform, build_type, output_file_path)

        if do_sign:
            signature_for_setup_win_application(version, timestamp, build_platform, build_type, signature_sha1)
        else:
            print('⏭️ 跳过签名步骤（安装包）')

        if not skip_upload:
            # 获取 metadata_file_path（如果存在）
            metadata_file_path = config['config'].get('metadata_file_path')
            run_upload_package_script(version, timestamp, build_platform, build_type, metadata_file_path)
        else:
            print('⏭️ 跳过上传步骤')
        delete_config()

    print('✅ 全部构建完成')


def update_submodules(build_platform, is_release):
    try:
        # 根据平台选择对应的子库目录
        submodule_dir = 'unityoutputwin7' if build_platform == 'win7' else 'unityoutputwin10p'

        print(f'📥 正在更新子库: {submodule_dir}')

        # 检查子模块目录是否存在
        if not os.path.exists(submodule_dir):
            print(f'⚙️ 初始化子模块: {submodule_dir}')
            subprocess.run(['git', 'submodule', 'update', '--init', submodule_dir], check=True)

        print('更新到指定提交')
        # 更新子模块到主仓库中指定的提交
        subprocess.run(['git', 'submodule', 'update', submodule_dir], check=True)

        print(f'✅ 子库更新完成: {submodule_dir}')
    except Exception as e:
        print(f'❌ 子库更新失败: {e}')
        sys.exit(1)


def delete_config():
    try:
        config_file = 'package_unity_config.json'
        if os.path.exists(config_file):
            os.remove(config_file)
            print('🛠  配置文件已删除')
    except Exception as e:
        print(f'❌ 配置文件删除失败: {e}')
        sys.exit(1)


def generate_config(config):
    try:
        with open('package_unity_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f)
        print(f'🛠  配置文件已生成: {json.dumps(config)}')
    except Exception as e:
        print(f'❌ 配置文件生成失败: {e}')
        sys.exit(1)


def run_package_script(version, timestamp, build_platform, build_type, do_encrypt, do_sign, signature_sha1):
    try:
        # 设置环境变量强制使用UTF-8和禁用缓冲
        env = os.environ.copy()
        env["PYTHONIOENCODING"] = "utf-8"
        env["PYTHONUNBUFFERED"] = "1"  # 确保子进程不缓冲输出
        
        process = subprocess.Popen(
            [
                'python',
                'win_package_runner.py',
                version,
                timestamp,
                build_platform,
                build_type,
                str(do_encrypt).lower(),
                str(do_sign).lower(),
                signature_sha1
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env
        )

        # 实时输出进程的标准输出和查找输出文件路径
        output_file_path = None
        for line in process.stdout:
            print(f'[SCRIPT] {line.strip()}')
            # 查找输出路径信息
            if "OUTPUT_FILE:" in line:
                output_file_path = line.strip().split("OUTPUT_FILE:")[1].strip()
                print(f'🔍 获取到输出文件路径: {output_file_path}')
        
        # 获取标准错误
        for line in process.stderr:
            print(f'[ERROR] {line.strip()}')

        exit_code = process.wait()
        if exit_code != 0:
            print(f'❌ 运行win_package_runner脚本失败: exit_code: {exit_code}')
            sys.exit(exit_code)
            
        # 检查是否找到了输出文件路径
        if not output_file_path:
            print(f'❌ 未能获取输出文件路径，请确保win_package_runner.py脚本输出了"OUTPUT_FILE:"标记')
            sys.exit(1)
        
        return output_file_path
    except Exception as e:
        print(f'❌ 运行win_package_runner脚本出现异常: {e}')
        sys.exit(1)


def get_download_url(exe_name, build_platform):
    """
    获取构建产物的下载URL
    
    Args:
        exe_name: 可执行文件名称
        build_platform: 构建平台 (win7 或 universal)
    
    Returns:
        str: 下载URL
    """
    try:
        # 读取NAS配置文件
        script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        nas_config_file = script_dir / "nas_config.json"
        
        if not nas_config_file.exists():
            print(f'❌ NAS配置文件不存在: {nas_config_file}')
            return None
            
        with open(nas_config_file, 'r', encoding='utf-8') as f:
            nas_config = json.load(f)
        
        # 根据平台获取对应的NAS ID
        if build_platform == 'win7':
            config_nas_id = nas_config.get('win7_nas_id', '')
        else:
            config_nas_id = nas_config.get('universal_nas_id', '')
        
        if not config_nas_id:
            print(f'❌ 无法获取 {build_platform} 平台的NAS ID')
            return None
        
        # 构建下载URL（参考win_package_upload.bat中的逻辑）
        download_url = f"http://*************:5000/share.cgi?ssid={config_nas_id}&path=%2F&filename={exe_name}&openfolder=forcedownload&ep="
        
        print(f'📎 生成下载URL: {download_url}')
        return download_url
        
    except Exception as e:
        print(f'❌ 获取下载URL失败: {e}')
        return None


def send_wechat_notification(exe_name, build_platform, metadata_file_path=None):
    """
    发送企业微信构建成功通知
    
    Args:
        exe_name: 可执行文件名称
        build_platform: 构建平台 (win7 或 universal)
        metadata_file_path: 元数据文件路径（可选，如果不提供则使用默认路径）
    """
    if not BuildNotificationSender:
        print('⚠️ 企业微信通知模块不可用，跳过消息发送')
        return

    try:
        print('📱 开始发送企业微信通知...')
        
        # 获取下载URL
        download_url = get_download_url(exe_name, build_platform)
        
        if not download_url:
            print('⚠️ 无法获取下载URL，跳过企业微信通知')
            return
        
        # 准备参数
        script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        
        # 使用传入的 metadata_file_path 或默认路径
        if metadata_file_path:
            build_metadata_path = Path(metadata_file_path)
            print(f'📝 使用指定的元数据文件路径: {build_metadata_path}')
        else:
            # 使用默认路径
            build_metadata_path = script_dir / 'build_metadata.json'
            print(f'📝 使用默认的元数据文件路径: {build_metadata_path}')
        
        main_repo_path = script_dir
        is_win7 = (build_platform == 'win7')
        
        # 如果build_metadata.json不存在，创建一个基本的
        if not build_metadata_path.exists():
            basic_metadata = {
                "git_branch_name": "N/A",
                "recent_commits": []
            }
            with open(build_metadata_path, 'w', encoding='utf-8') as f:
                json.dump(basic_metadata, f, ensure_ascii=False, indent=2)
            print('📝 创建了基本的build_metadata.json文件')
        
        # 创建通知发送器并发送消息
        sender = BuildNotificationSender(
            build_metadata_path=str(build_metadata_path),
            download_url=download_url,
            main_repo_path=str(main_repo_path),
            isWin7=is_win7
        )
        
        success = sender.send_build_success_notification()
        if success:
            print('✅ 企业微信通知发送成功')
        else:
            print('❌ 企业微信通知发送失败')
            
    except Exception as e:
        print(f'❌ 发送企业微信通知时出错: {e}')


def generate_exe_name(version, timestamp, build_platform, build_type):
    """
    生成EXE文件名
    
    Args:
        version: 版本号
        timestamp: 时间戳
        build_platform: 构建平台
        build_type: 构建类型
    
    Returns:
        str: EXE文件名
    """
    return f"turing_art-{version}-{build_platform}-{timestamp}-{build_type}-setup.exe"


def run_upload_package_script(version, timestamp, build_platform, build_type, metadata_file_path=None):
    print(f'🚀 开始上传 {build_platform} 到 NAS')
    try:
        # 获取当前分支
        current_branch = subprocess.check_output(['git', 'rev-parse', '--abbrev-ref', 'HEAD'], text=True).strip()
        print(f'📌 当前分支: {current_branch}')
        
        # 生成EXE文件名
        exe_name = generate_exe_name(version, timestamp, build_platform, build_type)
        
        # 设置环境变量
        env = os.environ.copy()
        env["PYTHONIOENCODING"] = "utf-8"
        env["PYTHONUNBUFFERED"] = "1"
        
        process = subprocess.Popen(
            [
                'win_package_upload.bat',
                version,
                timestamp,
                build_platform,
                build_type,
                current_branch,
                exe_name
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env
        )

        # 实时输出进程的标准输出
        for line in process.stdout:
            print(f'[BATCH] {line.strip()}')
        
        # 获取标准错误
        for line in process.stderr:
            print(f'[ERROR] {line.strip()}')

        exit_code = process.wait()
        if exit_code != 0:
            sys.exit(exit_code)
        
        # 上传成功后发送企业微信消息
        send_wechat_notification(exe_name, build_platform, metadata_file_path)
            
    except Exception as e:
        print(f'❌ 运行上传脚本失败: {e}')
        sys.exit(1)


def archive_artifacts(build_platform):
    try:
        version = parse_version()
        source = Path(f'dist/{version}')
        destination = Path(f'dist/{version}_{build_platform}')

        if source.exists():
            if destination.exists():
                shutil.rmtree(destination)
            shutil.move(source, destination)
            print(f'📦 构建产物已归档至: {destination}')
    except Exception as e:
        print(f'⚠️ 归档过程中发生异常: {e}')


def parse_version():
    try:
        with open('pubspec.yaml', 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip().startswith('version:'):
                    return line.split(':')[1].strip().replace('"', '').replace(' ', '')
        raise Exception('未找到有效版本号')
    except Exception as e:
        print(f'❌ 解析版本号失败: {e}')
        sys.exit(1)


def parse_signature_sha1():
    try:
        with open('pubspec.yaml', 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip().startswith('signature_sha1:'):
                    return line.split(':')[1].strip().replace('"', '')
        raise Exception('未找到有效签名使用的SHA1')
    except Exception as e:
        print(f'❌ 解析签名SHA1失败: {e}')
        sys.exit(1)


def generate_timestamp():
    now = time.localtime()
    return f"{now.tm_year}{now.tm_mon:02d}{now.tm_mday:02d}{now.tm_hour:02d}{now.tm_min:02d}{now.tm_sec:02d}"


def rename_and_move_artifacts(version, timestamp, build_platform, build_type, output_file_path):
    try:
        dest_dir = Path(f'dist/{version}/{timestamp}/{build_platform}')

        # 创建目标目录
        dest_dir.mkdir(parents=True, exist_ok=True)

        # 检查输出文件是否存在
        file_path = Path(output_file_path)
        if not file_path.exists():
            print(f'❌ 输出文件不存在: {output_file_path}')
            sys.exit(1)
            
        # 重命名并移动文件到目标目录
        new_file_name = f'turing_art-{version}-{build_platform}-{timestamp}-{build_type}-setup.exe'
        new_path = dest_dir / new_file_name

        # 处理文件冲突
        if new_path.exists():
            new_path.unlink()

        shutil.copy2(file_path, new_path)
        print(f'✅ 文件已复制: {file_path} → {new_path}')

        # 清理原始文件
        if file_path.exists():
            file_path.unlink()
            print(f'🧹 原始文件已删除: {file_path}')
    except Exception as e:
        print(f'❌ 文件移动失败: {e}')
        sys.exit(1)


def signature_for_setup_win_application(version, timestamp, build_platform, build_type, signature_sha1):
    # 等待文件移动完成
    time.sleep(3)
    # dist目录
    dest_dir = Path(f'dist/{version}/{timestamp}/{build_platform}')
    setup_file_name = f'turing_art-{version}-{build_platform}-{timestamp}-{build_type}-setup.exe'
    setup_full_file_path = dest_dir / setup_file_name

    # 待签名setup.exe是否存在
    if setup_full_file_path.exists():
        try:
            # 设置环境变量
            env = os.environ.copy()
            env["PYTHONIOENCODING"] = "utf-8"
            env["PYTHONUNBUFFERED"] = "1"
            
            process = subprocess.Popen(
                [
                    'win_package_signature.bat',
                    str(setup_full_file_path),
                    signature_sha1
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',
                env=env
            )

            # 实时输出进程的标准输出
            for line in process.stdout:
                print(f'[BATCH] {line.strip()}')
            
            # 获取标准错误
            for line in process.stderr:
                print(f'[ERROR] {line.strip()}')

            exit_code = process.wait()
            if exit_code != 0:
                print('❌ 签名setup.exe 失败！')
        except Exception as e:
            print(f'❌ 签名过程中发生异常: {e}')
    else:
        print(f'❌ 签名setup.exe 文件不存在: {setup_full_file_path}')


if __name__ == "__main__":
    main() 