#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import shutil
import subprocess
import datetime
import traceback
from pathlib import Path

def main(cmd_args=None):
    # 接收参数
    if cmd_args is not None:
        # 被作为模块调用时从传入的参数获取
        args = cmd_args
    else:
        # 被作为脚本调用时从sys.argv获取
        args = sys.argv[1:] if len(sys.argv) > 1 else []
    
    build_type = args[0] if len(args) > 0 else "Release"
    vmp_project_dir = args[1] if len(args) > 1 else ".\\windows\\vmprotect_projects" 
    vmprotect_path = args[2] if len(args) > 2 else "C:\\Program Files\\VMProtect Professional\\VMProtect_Con.exe"

    # 检查构建类型是否有效
    if build_type.lower() not in ["release", "debug"]:
        print(f"错误: 无效的构建类型 \"{build_type}\"，必须是 Release 或 Debug")
        show_usage()
        return 1
    
    output_dir = ".\\build\\windows\\x64\\runner\\Release"
    
    print(f"提示: 将对目录: {output_dir} 中的相关文件进行加密")
    
    # 设置日志和标记文件 - 对所有模式都设置加密标记文件
    encrypt_marker = os.path.join(output_dir, "encrypted_marker")
    
    if build_type.lower() == "debug":
        vmp_log = os.path.join(output_dir, "vmp_process.log")
        print("提示: Debug模式 - 将创建日志文件和加密标记")
    else:
        vmp_log = os.devnull
        print("提示: Release模式 - 将创建简化的加密标记文件")
    
    # 检查输出目录
    if not os.path.exists(output_dir):
        print(f"错误: 输出目录不存在: {output_dir}")
        return 1
    
    # 初始化日志文件 - 仅Debug模式
    if build_type.lower() == "debug":
        with open(vmp_log, "w", encoding="utf-8") as f:
            f.write(f"VMProtect加密日志 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("------------------------------------------\n")
    
    # 检查是否已加密 - 对所有模式都检查
    if os.path.exists(encrypt_marker):
        print("❌ 错误: 检测到加密标记文件，当前目录文件可能已经被加密过！")
        print(f"标记文件: {encrypt_marker}")
        print("如需重新加密，请删除此标记文件后再运行脚本。")
        return 1
    
    # 检查VMProtect路径
    if not os.path.exists(vmprotect_path):
        print(f"错误: 找不到VMProtect程序: {vmprotect_path}")
        return 1
    
    # 检查VMP工程目录
    if not os.path.exists(vmp_project_dir):
        print(f"错误: VMP工程目录不存在: {vmp_project_dir}")
        return 1
    
    # 要处理的VMP工程文件
    vmp_files = ["turing_art.exe.vmp", "UnityPlayer.dll.vmp", "GameAssembly.dll.vmp"]
    
    # 初始化加密文件列表
    encrypted_files = []
    
    print("==========================================")
    print("🛠 VMProtect 批量加密保护工具")
    print("==========================================")
    print(f"构建类型: {build_type}")
    print(f"加密文件所在目录: {output_dir}")
    print(f"VMProtect路径: {vmprotect_path}")
    print(f"VMP工程目录: {vmp_project_dir}")
    print("==========================================")
    
    # 第一步：复制VMP工程文件到输出目录
    print("[1] 正在复制VMP工程文件到输出目录...")
    for vmp_file in vmp_files:
        vmp_file_path = os.path.join(vmp_project_dir, vmp_file)
        if not os.path.exists(vmp_file_path):
            print(f"❌错误: VMP工程文件不存在: {vmp_file_path}")
            return 1
        else:
            print(f"    复制: {vmp_file}")
            try:
                shutil.copy2(vmp_file_path, output_dir)
            except Exception as e:
                print(f"❌错误: 复制文件失败: {vmp_file}, 原因: {str(e)}")
                return 1
    
    # 第二步：加密处理并替换原始文件
    print("[2] 加密处理并替换原始文件...")
    for vmp_file in vmp_files:
        vmp_file_path = os.path.join(output_dir, vmp_file)
        if os.path.exists(vmp_file_path):
            # 获取原始文件名(去掉.vmp后缀)
            original_file = os.path.splitext(vmp_file)[0]
            
            # 确定加密后的文件名
            protected_file = original_file
            if vmp_file.endswith(".vmp"):
                if original_file.endswith(".exe"):
                    protected_file = original_file[:-4] + ".vmp.exe"
                elif original_file.endswith(".dll"):
                    protected_file = original_file[:-4] + ".vmp.dll"
            
            print("------------------------------------------")
            print(f"    🚀 处理: {vmp_file}")
            print(f"    对应原始文件: {original_file}")
            print(f"    预期加密文件: {protected_file}")
            
            # 执行VMProtect加密
            print("    正在调用VMProtect...")
            
            # 在日志文件中记录当前处理的文件信息 - 仅Debug模式
            if build_type.lower() == "debug":
                with open(vmp_log, "a", encoding="utf-8") as f:
                    f.write(f"\n处理文件: {vmp_file}\n")
                    f.write(f"时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("------------------------------------------\n")
            
            # 调用VMProtect执行加密
            try:
                print(f"    [DEBUG] VMProtect命令: {vmprotect_path} {os.path.join(output_dir, vmp_file)}")
                
                vmp_log_mode = "a" if build_type.lower() == "debug" else "w"
                log_file_path = vmp_log if build_type.lower() == "debug" else os.devnull
                print(f"    [DEBUG] 打开日志文件: {log_file_path}, 模式: {vmp_log_mode}")
                
                # 不再重定向输出到文件，而是直接显示在控制台
                try:
                    print("="*50)
                    print("VMProtect输出开始:")
                    print("="*50)
                    
                    # 定义命令
                    cmd = [vmprotect_path, os.path.join(output_dir, vmp_file)]
                    
                    # 设置环境变量
                    env = os.environ.copy()
                    env["PYTHONIOENCODING"] = "gbk"  # 使用GBK编码读取中文
                    env["PYTHONUNBUFFERED"] = "1"
                    
                    # 使用Popen代替run，以实时获取输出
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                        text=True,
                        encoding='gbk',  # 使用GBK解码VMProtect的输出
                        errors='replace',
                        env=env,
                        bufsize=1  # 行缓冲
                    )
                    
                    # 实时读取并显示输出
                    vmp_output = []
                    for line in process.stdout:
                        # 去除行尾的换行符
                        line = line.rstrip()
                        print(line)  # 以GBK编码解析后直接打印
                        vmp_output.append(line)
                        
                        # 如果Debug模式，也把输出写入日志
                        if build_type.lower() == "debug":
                            with open(vmp_log, "a", encoding="utf-8") as f:
                                f.write(f"{line}\n")
                    
                    # 等待进程结束并获取返回码
                    return_code = process.wait()
                    
                    print("="*50)
                    print(f"VMProtect输出结束 (返回码: {return_code})")
                    print("="*50)
                    
                    # 如果Debug模式，也把返回码写入日志
                    if build_type.lower() == "debug":
                        with open(vmp_log, "a", encoding="utf-8") as f:
                            f.write(f"\nVMProtect返回码: {return_code}\n")
                    
                    if return_code != 0:
                        print(f"❌ 错误: VMProtect处理失败: {vmp_file}，错误代码: {return_code}")
                        return 1
                    else:
                        print(f"    [DEBUG] VMProtect执行成功，返回码: {return_code}")
                except subprocess.TimeoutExpired:
                    print(f"❌ 错误: VMProtect执行超时，可能陷入无限等待")
                    return 1
            except Exception as e:
                print(f"❌ 错误: 执行VMProtect失败: {str(e)}")
                traceback.print_exc()
                return 1
            
            # 检查加密后的文件是否存在
            protected_file_path = os.path.join(output_dir, protected_file)
            print(f"    [DEBUG] 检查加密后的文件是否存在: {protected_file_path}")
            
            if not os.path.exists(protected_file_path):
                print(f"❌ 错误: 加密后的文件不存在: {protected_file_path}")
                print(f"    [DEBUG] 输出目录内容:")
                for file in os.listdir(output_dir):
                    print(f"    [DEBUG]   - {file}")
                return 1
            else:
                file_size = os.path.getsize(protected_file_path)
                print(f"    [DEBUG] 加密后的文件存在，大小: {file_size} 字节")
            
            # 删除VMP工程文件
            try:
                print(f"    [DEBUG] 删除VMP工程文件: {vmp_file_path}")
                os.remove(vmp_file_path)
            except Exception as e:
                print(f"❌ 错误: 删除VMP工程文件失败: {vmp_file_path}, 原因: {str(e)}")
                traceback.print_exc()
                return 1
            
            # 替换原始文件
            original_file_path = os.path.join(output_dir, original_file)
            print(f"    替换: {original_file} 使用 {protected_file}")
            
            # 删除原始文件
            try:
                if os.path.exists(original_file_path):
                    print(f"    [DEBUG] 删除原始文件: {original_file_path}")
                    os.remove(original_file_path)
            except Exception as e:
                print(f"❌ 错误: 无法删除原始文件: {original_file_path}, 原因: {str(e)}")
                traceback.print_exc()
                return 1
            
            # 重命名加密后的文件
            try:
                print(f"    [DEBUG] 重命名文件: {protected_file_path} -> {original_file_path}")
                os.rename(protected_file_path, original_file_path)
                print("✅ 成功: 文件已加密并替换原始文件")
                
                # 添加到加密文件列表
                encrypted_files.append(original_file)
            except Exception as e:
                print(f"❌ 错误: 重命名文件失败: {protected_file_path} 到 {original_file_path}, 原因: {str(e)}")
                traceback.print_exc()
                return 1
    
    # 第三步：创建加密标记文件 - 所有模式都创建
    print("[3] 创建加密标记文件...")
    try:
        with open(encrypt_marker, "w", encoding="utf-8") as f:
            if build_type.lower() == "debug":
                # Debug模式 - 创建详细的标记文件
                f.write(f"VMProtect加密于: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("加密文件列表:\n")
                for encrypted_file in encrypted_files:
                    f.write(f"- {encrypted_file}\n")
            else:
                # Release模式 - 创建简化的标记文件
                f.write(f"release: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"✅ 成功: 已创建加密标记文件: {encrypt_marker}")
    except Exception as e:
        print(f"❌ 警告: 无法创建加密标记文件，但加密过程已完成, 原因: {str(e)}")
    
    print("==========================================")
    print("🎉 VMProtect 批量加密保护完成！")
    print("==========================================")
    
    return 0

def show_usage():
    print()
    print("用法: python vmp_encrypt.py [构建类型] [VMP工程目录] [VMProtect路径]")
    print()
    print("参数:")
    print("  [构建类型]     - Release或Debug (默认: Release)")
    print("  [VMP工程目录]  - VMP工程文件所在目录 (默认: .\\windows\\vmprotect_projects)")
    print("  [VMProtect路径] - VMProtect_Con.exe的完整路径")
    print("                   (默认: C:\\Program Files\\VMProtect Professional\\VMProtect_Con.exe)")
    print()
    print("示例:")
    print("  python vmp_encrypt.py")
    print("  python vmp_encrypt.py Release")
    print("  python vmp_encrypt.py Debug .\\my_projects C:\\VMProtect\\VMProtect_Con.exe")
    print()

if __name__ == "__main__":
    # 设置全局变量禁用缓冲
    os.environ["PYTHONUNBUFFERED"] = "1"
    
    # 修复Windows控制台的编码问题
    try:
        # Python 3.7+支持reconfigure方法
        sys.stdout.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
    except AttributeError:
        # 适用于旧版Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)
    
    sys.exit(main())