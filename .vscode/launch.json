{
	// 使用 IntelliSense 了解相关属性。 
	// 悬停以查看现有属性的描述。
	// 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"name": "turingartclient",
			"request": "launch",
			"type": "dart",
			"args": [
				"--dart-define=IS_DEBUG_FLAG=true",
			],
		},
		{
			"name": "turingartclient (profile mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "profile",
			"args": [
				"--dart-define=IS_DEBUG_FLAG=true",
			],
		},
		{
			"name": "turingartclient (release mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "release",
		},
		{
			"name": "Flutter",
			"request": "launch",
			"type": "dart",
			"env": {
				"FLUTTER_SUPPRESS_PLUGIN_WARNINGS": "true"
			}
		},
		{
			"name": "Flutter Debug - Normal Start",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true"
			]
		},
		{
			"name": "Flutter Debug - Import Single File",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--import --name=测试项目 --file=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3601.CR2|sic_2|true"
			]
		},
		{
			"name": "Flutter Debug - Import with Auto Navigate",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--import --project=auto-nav-project --name=自动导航项目 --file=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3570.CR2 --auto-navigate"
			]
		},
		{
			"name": "Flutter Debug - Import Multiple Files",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--import --project=multi-file-project --name=多文件项目 --file=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3601.CR2 --file=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3570.CR2 --file=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3600.CR2"
			]
		},
		{
			"name": "Flutter Debug - Open Existing Project",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--open --project=existing-project-uuid-12345"
			]
		},
		{
			"name": "Flutter Debug - Direct File Path",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3600.CR2"
			]
		},
		{
			"name": "Flutter Debug - Action Import",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--action=import --project=action-project --name=动作项目 --file=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3601.CR2"
			]
		},
		{
			"name": "Flutter Debug - Short Parameters",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--import -p short-param-project -n 短参数项目 -f C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3570.CR2"
			]
		},
		{
			"name": "Flutter Debug - Import Images Without Tapj",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--smart --images=D:/temp/images_without_tapj/AH0A1633.jpg|D:/temp/images_without_tapj/unnamed.jpg"
			]
		},
		{
			"name": "Flutter Debug - Import Images",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--smart --images=D:\\temp\\import_test\\图灵精修\\AH0A4826.jpg|D:\\temp\\import_test\\图灵精修\\AH0A4989.jpg"
			]
		},
		{
			"name": "Flutter Debug - Import TAPJ File",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--tapj test_resouce/白纱.tapj"
			]
		},
		{
			"name": "Flutter Debug - Import TAPJ as New Project",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--tapj test_resouce/白纱.tapj --newProject",
			]
		},
		{
			"name": "Flutter Debug - New Project with Specified ID",
			"request": "launch",
			"type": "dart",
			"program": "lib/main.dart",
			"toolArgs": [
				"--dart-define=IS_DEBUG_FLAG=true",
				"--dart-entrypoint-args=--import --project=new-project-uuid --name=新指定ID项目 --file=C:\\Users\\<USER>\\Downloads\\0512\\0512\\IMG_3601.CR2 --newProject"
			]
		},
		{
			"name": "turingartclient",
			"request": "launch",
			"type": "dart"
		}
	]
}