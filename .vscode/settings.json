{"editor.detectIndentation": false, "editor.insertSpaces": false, "files.insertFinalNewline": false, "editor.formatOnSave": true, "editor.quickSuggestions": {"comments": "off", "strings": "off", "other": "off"}, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.removeUnused": "explicit", "source.fixAll": "explicit"}, "[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off", "editor.defaultFormatter": "Dart-Code.dart-code", "editor.codeActionsOnSave": {"source.fixAll": "explicit"}}, "files.associations": {"string": "cpp"}, "[c]": {"editor.tabSize": 2, "editor.insertSpaces": true}, "[cpp]": {"editor.tabSize": 2, "editor.insertSpaces": true}, "[json]": {"editor.tabSize": 2, "editor.insertSpaces": true}, "[yaml]": {"editor.tabSize": 2, "editor.insertSpaces": true}, "[python]": {"editor.tabSize": 2, "editor.insertSpaces": true}, "[swift]": {"editor.defaultFormatter": "vknabel.vscode-apple-swift-format"}, "CodeMoss.inlineCompletion.triggerMode": "manual", "dart.flutterSdkPath": ".fvm/versions/3.19.6"}