## 0.2.1

* bug fixed

## 0.2.0
* Added the ability to determine whether a created window will be resizable or not
([#101](https://github.com/MixinNetwork/flutter-plugins/issues/101) and [#130](https://github.com/MixinNetwork/flutter-plugins/pull/130))

## 0.1.0

* [BREAK CHANGE] upgrade min flutter version to 3.0.0
* fix macOS memory leak issue. [#123](https://github.com/MixinNetwork/flutter-plugins/issues/123)

## 0.0.2

* [Windows] fix free window_channel_ may cause crash. [#78](https://github.com/MixinNetwork/flutter-plugins/pull/78)
* add getAllSubWindowIds api. [#77](https://github.com/MixinNetwork/flutter-plugins/pull/77)

## 0.0.1

* Initial release. support Linux, macOS, Windows.
