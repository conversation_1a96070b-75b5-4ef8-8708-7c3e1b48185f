#include "window_proc.h"
#include <CommCtrl.h>
#include <dwmapi.h>
#include "log.h"
#include "multi_window_manager.h"
LRESULT CALLBACK main_window_proc(HWND window, UINT message, WPARAM wparam, LPARAM lparam, UINT_PTR subclassID, DWORD_PTR refData){
	switch (message) {

	case WM_CREATE:
	{
		if (window) {
			DWM_WINDOW_CORNER_PREFERENCE preference = DWMWCP_ROUND;
			DwmSetWindowAttribute(window, DWMWA_WINDOW_CORNER_PREFERENCE, &preference, sizeof(preference));
			return 0;
		}
		break;
	}
	case WM_ERASEBKGND:
	{
		return 1;
	}
	case WM_NCHITTEST:
	{
		if (MultiWindowManager().Instance()->GetWindowResizable(window)) {
			return handle_nchittest(window, lparam);
		}
		break;
	}
	case WM_NCCALCSIZE:
	{
		if (wparam) {
			NCCALCSIZE_PARAMS* params = reinterpret_cast<NCCALCSIZE_PARAMS*>(lparam);

			WINDOWPLACEMENT placement = {};
			placement.length = sizeof(WINDOWPLACEMENT);
			GetWindowPlacement(window, &placement);

			if (placement.showCmd == SW_MAXIMIZE) {
				MONITORINFO mi = { sizeof(mi) };
				HMONITOR monitor = MonitorFromWindow(window, MONITOR_DEFAULTTONEAREST);
				if (GetMonitorInfo(monitor, &mi)) {
					params->rgrc[0] = mi.rcWork; 
				}
			}
			else {
				params->rgrc[0] = params->rgrc[0];
			}

			return 0;
		}
		break;
	}
	case WM_GETMINMAXINFO: 
	{
		MINMAXINFO* pmmi = (MINMAXINFO*)lparam;
        SIZE* minSize = MultiWindowManager().Instance()->GetWindowMinimize(window);
        SIZE* maxSize = MultiWindowManager().Instance()->GetWindowMaximize(window);
        bool handled = false;
        if (minSize && minSize->cx > 0 && minSize->cy > 0) {
            pmmi->ptMinTrackSize.x = minSize->cx;
            pmmi->ptMinTrackSize.y = minSize->cy;
            handled = true;
        }
        if (maxSize && maxSize->cx > 0 && maxSize->cy > 0) {
            pmmi->ptMaxTrackSize.x = maxSize->cx;
            pmmi->ptMaxTrackSize.y = maxSize->cy;
            handled = true;
        }
        if (handled) {
            return 0;
        }else{
            break;
        }
	}
	default:
		break;
	}
	return DefSubclassProc(window, message, wparam, lparam);
}

LRESULT CALLBACK flutter_view_proc(HWND window, UINT message, WPARAM wparam, LPARAM lparam, UINT_PTR subclassID, DWORD_PTR refData) {
	switch (message) {
	case WM_NCHITTEST:
	{
		LRESULT result = handle_nchittest(window, lparam);
		if (result != HTCLIENT) {
			return HTTRANSPARENT;
		}
		break;
	}
	}
	return DefSubclassProc(window, message, wparam, lparam);
}

LRESULT handle_nchittest(HWND window, WPARAM lparam)
{
	POINT pt = { GET_X_LPARAM(lparam), GET_Y_LPARAM(lparam) };
	ScreenToClient(window, &pt);
	RECT clientRect;
	GetClientRect(window, &clientRect);
	const int BORDER_WIDTH = 5;

	if (pt.x < clientRect.left + BORDER_WIDTH) {
		if (pt.y < clientRect.top + BORDER_WIDTH)
			return HTTOPLEFT;
		if (pt.y > clientRect.bottom - BORDER_WIDTH)
			return HTBOTTOMLEFT;
		return HTLEFT;
	}
	else if (pt.x > clientRect.right - BORDER_WIDTH) {
		if (pt.y < clientRect.top + BORDER_WIDTH)
			return HTTOPRIGHT;
		if (pt.y > clientRect.bottom - BORDER_WIDTH)
			return HTBOTTOMRIGHT;
		return HTRIGHT;
	}
	else if (pt.y < clientRect.top + BORDER_WIDTH) {
		return HTTOP;
	}
	else if (pt.y > clientRect.bottom - BORDER_WIDTH) {
		return HTBOTTOM;
	}
	return HTCLIENT;
}