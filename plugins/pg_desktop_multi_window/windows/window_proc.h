#include <stdio.h>
#include <windows.h>
#include <windowsx.h>

LRESULT handle_nchittest(HWND window, WPARAM lparam);

LRESULT CALLBACK main_window_proc(HWND window, UINT message, WPARAM wparam, <PERSON><PERSON>AM lparam, UINT_PTR subclassID, DWORD_PTR refData);
LRESULT CALLBACK flutter_view_proc(HWND window, UINT message, WPARAM wparam, <PERSON><PERSON><PERSON> lparam, UINT_PTR subclassID, DWORD_PTR refData);
