#include "log.h"

void Logger::Log(LogLevel level, const wchar_t* format, ...) {
  va_list args;
  va_start(args, format);

  std::wstring formattedMessage = FormatMessage(format, args);
  va_end(args);

  std::wstring fullMessage = L"[" + GetTimestamp() + L"] " + GetLogLevelString(level) + L" " + formattedMessage;
  OutputLogMessage(fullMessage);
}

void Logger::Log(LogLevel level, const wchar_t* format, const std::wstring& file, int line, ...) {
  va_list args;
  va_start(args, line);

  std::wstring formattedMessage = FormatMessage(format, args);
  va_end(args);

  std::wstring fullMessage = L"[" + GetTimestamp() + L"] " + GetLogLevelString(level) + L" (" + file + L":" + std::to_wstring(line) + L") " + formattedMessage;
  OutputLogMessage(fullMessage);
}

std::wstring Logger::GetLogLevelString(LogLevel level) {
  switch (level) {
  case INFO:
    return L"INFO";
  case WARN:
    return L"WARNING";
  case ERR:
    return L"ERROR";
  default:
    return L"UNKNOWN";
  }
}

std::wstring Logger::GetTimestamp() {
  std::time_t now = std::time(nullptr);
  struct tm timeInfo;
  localtime_s(&timeInfo, &now);

  wchar_t timestamp[20];
  wcsftime(timestamp, sizeof(timestamp), L"%Y-%m-%d %H:%M:%S", &timeInfo);

  return timestamp;
}

std::wstring Logger::FormatMessage(const wchar_t* format, va_list args) {
  int length = _vscwprintf(format, args) + 1;  // Get the length of the formatted message
  wchar_t* buffer = new wchar_t[length];

  _vsnwprintf_s(buffer, length, length, format, args);

  std::wstring result(buffer);
  delete[] buffer;

  return result;
}

void Logger::OutputLogMessage(const std::wstring& message) {
  OutputDebugStringW(message.c_str());
}