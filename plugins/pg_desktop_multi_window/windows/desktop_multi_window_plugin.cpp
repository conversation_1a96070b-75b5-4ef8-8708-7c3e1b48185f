#include "include/pg_desktop_multi_window/desktop_multi_window_plugin.h"
#include "multi_window_plugin_internal.h"

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>

#include <map>
#include <memory>
#include <CommCtrl.h>

#include "multi_window_manager.h"
#include "window_proc.h"

namespace {
  HHOOK flutterMainWindowHook;
  HWND flutter_window;
  HWND flutter_child_window;
  LRESULT CALLBACK monitorFlutterWindowsProc(
    _In_ int code,
    _In_ WPARAM wparam,
    _In_ LPARAM lparam)
  {
    if (code == HCBT_CREATEWND)
    {
      auto createParams = reinterpret_cast<CBT_CREATEWND*>(lparam);
      if (!createParams->lpcs->lpCreateParams)
      {
        return 0;
      }

      if (wcscmp(createParams->lpcs->lpszClass, L"FLUTTER_RUNNER_WIN32_WINDOW") == 0)
      {
        flutter_window = (HWND)wparam;
        SetWindowSubclass(flutter_window, main_window_proc, 1, NULL);
      }
      if (wcscmp(createParams->lpcs->lpszClass, L"FLUTTERVIEW") == 0)
      {
        flutter_child_window = (HWND)wparam;
        SetWindowSubclass(flutter_child_window, flutter_view_proc, 1, NULL);
      }
    }
    if ((flutter_window != nullptr) && (flutter_child_window != nullptr))
    {
      UnhookWindowsHookEx(flutterMainWindowHook);
    }
    return 0;
  }
class DesktopMultiWindowPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  DesktopMultiWindowPlugin();

  ~DesktopMultiWindowPlugin() override;

 private:
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

// static
void DesktopMultiWindowPlugin::RegisterWithRegistrar(
    flutter::PluginRegistrarWindows *registrar) {
  auto channel =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          registrar->messenger(), "mixin.one/flutter_multi_window",
          &flutter::StandardMethodCodec::GetInstance());
  
  auto plugin = std::make_unique<DesktopMultiWindowPlugin>();

  channel->SetMethodCallHandler(
      [plugin_pointer = plugin.get()](const auto &call, auto result) {
        plugin_pointer->HandleMethodCall(call, std::move(result));
      });
  registrar->AddPlugin(std::move(plugin));
}

DesktopMultiWindowPlugin::DesktopMultiWindowPlugin() = default;

DesktopMultiWindowPlugin::~DesktopMultiWindowPlugin() = default;

void DesktopMultiWindowPlugin::HandleMethodCall(
    const flutter::MethodCall<flutter::EncodableValue> &method_call,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
  if (method_call.method_name() == "createWindow") {
    auto args = std::get_if<std::string>(method_call.arguments());
    auto window_id = MultiWindowManager::Instance()->Create(args != nullptr ? *args : "");
    result->Success(flutter::EncodableValue(window_id));
    return;
  } else if (method_call.method_name() == "setWindowMaximizeSize") {
    auto arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    auto width = std::get<double_t>(arguments->at(flutter::EncodableValue("width")));
    auto height = std::get<double_t>(arguments->at(flutter::EncodableValue("height")));
    SIZE size = { static_cast<LONG>(width),static_cast<LONG>(height), };
    MultiWindowManager::Instance()->SetWindowMaximizeSize(window_id, &size);
    result->Success();
    return;
  } else if (method_call.method_name() == "setWindowMinimizeSize") {
    auto arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    auto width = std::get<double_t>(arguments->at(flutter::EncodableValue("width")));
    auto height = std::get<double_t>(arguments->at(flutter::EncodableValue("height")));
    SIZE size = { static_cast<LONG>(width),static_cast<LONG>(height), };
    MultiWindowManager::Instance()->SetWindowMinimizeSize(window_id, &size);
    result->Success();
    return;
  } else if (method_call.method_name() == "getActiveWindowId") {
    int64_t activeWindowId = MultiWindowManager::Instance()->GetAcvtieWindowId();
    result->Success(flutter::EncodableValue(activeWindowId));
    return;
  } else if (method_call.method_name() == "maximizeWindow") {
    auto arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    MultiWindowManager::Instance()->MaximizeWindow(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "minimizeWindow") {
    auto arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    MultiWindowManager::Instance()->MinimizeWindow(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "maximizeOrRestoreWindow") {
    auto arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    MultiWindowManager::Instance()->MaximizeOrRestoreWindow(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "restoreWindow") {
    auto arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    MultiWindowManager::Instance()->RestoreWindow(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "show") {
    auto window_id = method_call.arguments()->LongValue();
    MultiWindowManager::Instance()->Show(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "hide") {
    auto window_id = method_call.arguments()->LongValue();
    MultiWindowManager::Instance()->Hide(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "close") {
    auto window_id = method_call.arguments()->LongValue();
    MultiWindowManager::Instance()->Close(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "setSize") {
    auto * arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto winow_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    auto width = std::get<double_t>(arguments->at(flutter::EncodableValue("width")));
    auto height = std::get<double_t>(arguments->at(flutter::EncodableValue("height")));
    MultiWindowManager::Instance()->SetSize(winow_id, width, height);
    result->Success();
    return;
  } else if (method_call.method_name() == "setFrame") {
    auto *arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    auto left = std::get<double_t>(arguments->at(flutter::EncodableValue("left")));
    auto top = std::get<double_t>(arguments->at(flutter::EncodableValue("top")));
    auto width = std::get<double_t>(arguments->at(flutter::EncodableValue("width")));
    auto height = std::get<double_t>(arguments->at(flutter::EncodableValue("height")));
    MultiWindowManager::Instance()->SetFrame(window_id, left, top, width, height);
    result->Success();
    return;
  } else if (method_call.method_name() == "center") {
    auto window_id = method_call.arguments()->LongValue();
    MultiWindowManager::Instance()->Center(window_id);
    result->Success();
    return;
  } else if (method_call.method_name() == "setTitle") {
    auto *arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    auto title = std::get<std::string>(arguments->at(flutter::EncodableValue("title")));
    MultiWindowManager::Instance()->SetTitle(window_id, title);
    result->Success();
    return;
  } else if (method_call.method_name() == "getAllSubWindowIds") {
    auto window_ids = MultiWindowManager::Instance()->GetAllSubWindowIds();
    result->Success(window_ids);
    return;
  } else if (method_call.method_name() == "draggableWindow") {
    auto* argments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = argments->at(flutter::EncodableValue("windowId")).LongValue();
    MultiWindowManager::Instance()->DraggableWindow(window_id);
    return;
  } else if (method_call.method_name() == "resizable") {
    auto* arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    auto window_id = arguments->at(flutter::EncodableValue("windowId")).LongValue();
    bool resizable = std::get<bool>(arguments->at(flutter::EncodableValue("resizable")));
    MultiWindowManager::Instance()->SetResizable(window_id, resizable);
    result->Success();
    return;
  }
  result->NotImplemented();
}

}  // namespace

int InitMainWindowHook()
{
  DWORD threadId = GetCurrentThreadId();
  flutterMainWindowHook = SetWindowsHookEx(WH_CBT, monitorFlutterWindowsProc, NULL, threadId);
  return 0;
}

void DesktopMultiWindowPluginRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {

  InternalMultiWindowPluginRegisterWithRegistrar(registrar);
  // Attach MainWindow for
  if (flutter_window != nullptr) {
    auto channel = WindowChannel::RegisterWithRegistrar(registrar, 0);
    MultiWindowManager::Instance()->AttachFlutterMainWindow(GetAncestor(flutter_window, GA_ROOT),
      std::move(channel));
  }
}

void InternalMultiWindowPluginRegisterWithRegistrar(FlutterDesktopPluginRegistrarRef registrar) {
  DesktopMultiWindowPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}

