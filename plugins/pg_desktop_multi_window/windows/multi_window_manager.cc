//
// Created by yang<PERSON> on 2022/1/11.
//
#include <stdio.h>
#include <windows.h>
#include <commctrl.h>
#include "multi_window_manager.h"

#include <memory>

#include "flutter_window.h"
#pragma comment(lib, "Comctl32.lib")

namespace {
int64_t g_next_id_ = 0;

class FlutterMainWindow : public BaseFlutterWindow {

 public:

  FlutterMainWindow(HWND hwnd, std::unique_ptr<WindowChannel> window_channel)
      : hwnd_(hwnd), channel_(std::move(window_channel)) {
  }

  ~FlutterMainWindow() override = default;

  WindowChannel *GetWindowChannel() override {
    return channel_.get();
  }

 protected:

  HWND GetWindowHandle() override {
    return hwnd_;
  }

 private:

  HWND hwnd_;

  std::unique_ptr<WindowChannel> channel_;

};

}

// static
MultiWindowManager *MultiWindowManager::Instance() {
  static auto manager = std::make_shared<MultiWindowManager>();
  return manager.get();
}

MultiWindowManager::MultiWindowManager() : windows_() {

}

int64_t MultiWindowManager::Create(std::string args) {
  g_next_id_++;
  int64_t id = g_next_id_;

  auto window = std::make_unique<FlutterWindow>(id, std::move(args), shared_from_this());
  auto channel = window->GetWindowChannel();
  channel->SetMethodCallHandler([this](int64_t from_window_id,
                                       int64_t target_window_id,
                                       const std::string &call,
                                       flutter::EncodableValue *arguments,
                                       std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
    HandleWindowChannelCall(from_window_id, target_window_id, call, arguments, std::move(result));
  });
  windows_[id] = std::move(window);
  return id;
}

void MultiWindowManager::AttachFlutterMainWindow(
    HWND main_window_handle,
    std::unique_ptr<WindowChannel> window_channel) {
  if (windows_.count(0) != 0) {
    std::cout << "Error: main window already exists" << std::endl;
    return;
  }
  window_channel->SetMethodCallHandler(
      [this](int64_t from_window_id,
             int64_t target_window_id,
             const std::string &call,
             flutter::EncodableValue *arguments,
             std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
        HandleWindowChannelCall(from_window_id, target_window_id, call, arguments, std::move(result));
      });
  windows_[0] = std::make_unique<FlutterMainWindow>(main_window_handle, std::move(window_channel));
}

void MultiWindowManager::SetWindowMaximizeSize(int64_t id, SIZE* size) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->SetWindowMaximizeSize(size);
  }
}

void MultiWindowManager::SetWindowMinimizeSize(int64_t id, SIZE* size) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->SetWindowMinimizeSize(size);
  }
}

void MultiWindowManager::MaximizeWindow(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->MaximizeWindow();
  }
}

void MultiWindowManager::MinimizeWindow(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->MinimizeWindow();
  }
}

void MultiWindowManager::RestoreWindow(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->RestoreWindow();
  }
}

void MultiWindowManager::MaximizeOrRestoreWindow(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->MaximizeOrRestoreWindow();
  }
}

void MultiWindowManager::Show(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->Show();
  }
}

void MultiWindowManager::Hide(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->Hide();
  }
}

void MultiWindowManager::Close(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->Close();
  }
}

void MultiWindowManager::SetFrame(int64_t id, double_t x, double_t y, double_t width, double_t height) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->SetBounds(x, y, width, height);
  }
}

void MultiWindowManager::SetTitle(int64_t id, const std::string &title) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->SetTitle(title);
  }
}

void MultiWindowManager::SetSize(int64_t id, double_t width, double_t height){
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->SetSize(width, height);
  }
}

void MultiWindowManager::SetTitleBarHidden(int64_t id, bool isHidden) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->SetTitleBarHidden(isHidden);
  }
}

void MultiWindowManager::DraggableWindow(int64_t id){
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->DraggableWindow();
  }
}

void MultiWindowManager::SetResizable(int64_t id, bool resizable) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->SetResizable(resizable);
  }
}

void MultiWindowManager::Center(int64_t id) {
  auto window = windows_.find(id);
  if (window != windows_.end()) {
    window->second->Center();
  }
}

flutter::EncodableList MultiWindowManager::GetAllSubWindowIds() {
  flutter::EncodableList resList = flutter::EncodableList();
  for (auto &window : windows_) {
    if (window.first != 0) {
      resList.push_back(flutter::EncodableValue(window.first));
    }
  }
  return resList;
}

int64_t MultiWindowManager::GetAcvtieWindowId() {
  HWND foregroundWindow = GetForegroundWindow();
  if (IsWindow(foregroundWindow)) {
    for (auto& window : windows_) {
      if (window.second->Equals(foregroundWindow)) {
        return window.first;
      };
    }
  }
  return -1;
}

void MultiWindowManager::OnWindowClose(int64_t id) {
}

void MultiWindowManager::OnWindowDestroy(int64_t id) {
  windows_.erase(id);
}

SIZE* MultiWindowManager::GetWindowMaximize(HWND windowHandler) {
  if (!windowHandler) {
    return nullptr;
  }
  if (IsWindow(windowHandler)) {
    for (auto& window : windows_) {
      if (window.second->Equals(windowHandler)) {
        return window.second->GetWindowMaximizeSize();
      };
    }
  }
  return nullptr;
}

SIZE* MultiWindowManager::GetWindowMinimize(HWND windowHandler) {
  if (!windowHandler) {
    return nullptr;
  }
  if (IsWindow(windowHandler)) {
    for (auto& window : windows_) {
      if (window.second->Equals(windowHandler)) {
        return window.second->GetWindowMinimizeSize();
      };
    }
  }
  return nullptr;
}

bool MultiWindowManager::GetWindowResizable(HWND windowHandler) {
    if (!windowHandler) {
        return true;
    }
    if (IsWindow(windowHandler)) {
        for (auto& window : windows_) {
            if (window.second->Equals(windowHandler)) {
                return window.second->GetWindowResizable();
            };
        }
    }
    return true;
}

void MultiWindowManager::HandleWindowChannelCall(
    int64_t from_window_id,
    int64_t target_window_id,
    const std::string &call,
    flutter::EncodableValue *arguments,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result
) {
  auto target_window_entry = windows_.find(target_window_id);
  if (target_window_entry == windows_.end()) {
    result->Error("-1", "target window not found.");
    return;
  }
  auto target_window_channel = target_window_entry->second->GetWindowChannel();
  if (!target_window_channel) {
    result->Error("-1", "target window channel not found.");
    return;
  }
  target_window_channel->InvokeMethod(from_window_id, call, arguments, std::move(result));
}

