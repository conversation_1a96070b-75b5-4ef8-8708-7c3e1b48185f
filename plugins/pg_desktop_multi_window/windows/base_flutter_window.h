//
// Created by yang<PERSON> on 2022/1/27.
//

#ifndef MULTI_WINDOW_WINDOWS_BASE_FLUTTER_WINDOW_H_
#define MULTI_WINDOW_WINDOWS_BASE_FLUTTER_WINDOW_H_

#include "window_channel.h"

class BaseFlutterWindow {
 protected:
  SIZE window_max_size_;
  SIZE window_min_size_;
  bool windows_resizable_ = true;
 public:

  virtual ~BaseFlutterWindow() = default;

  virtual WindowChannel *GetWindowChannel() = 0;

  void SetWindowMaximizeSize(SIZE* size);
  
  void SetWindowMinimizeSize(SIZE* size);

  SIZE* GetWindowMaximizeSize();

  SIZE* GetWindowMinimizeSize();

  void MaximizeWindow();

  void MinimizeWindow();

  void RestoreWindow();

  void MaximizeOrRestoreWindow();
  
  void Show();

  void Hide();

  void Close();

  void SetTitle(const std::string &title);

  void SetBounds(double_t x, double_t y, double_t width, double_t height);

  void SetSize(double_t width,double_t height);

  void SetTitleBarHidden(bool isHidden);

  void DraggableWindow();

  void Center();

  // 设置窗口是否可以调整大小
  void SetResizable(bool resizable);

  //获取窗口是否可以调整大小
  bool GetWindowResizable();

  bool Equals(HWND window_handle);

 protected:

  virtual HWND GetWindowHandle() = 0;

};

#endif //MULTI_WINDOW_WINDOWS_BASE_FLUTTER_WINDOW_H_
