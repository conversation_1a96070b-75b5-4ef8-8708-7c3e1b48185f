#ifndef LOG_H
#define LOG_H

#include <windows.h>
#include <string>
#include <sstream>
#include <ctime>
#include <cstdarg>  // For va_list

class Logger {
public:
  enum LogLevel {
    INFO,
    WARN,
    ERR
  };

  static void Log(LogLevel level, const wchar_t* format, ...);
  static void Log(LogLevel level, const wchar_t* format, const std::wstring& file, int line, ...);

private:
  static std::wstring GetLogLevelString(LogLevel level);
  static std::wstring GetTimestamp();
  static void OutputLogMessage(const std::wstring& message);
  static std::wstring FormatMessage(const wchar_t* format, va_list args);
};

#endif // LOG_H