//
// Created by yang<PERSON> on 2022/1/11.
//

#ifndef DESKTOP_MULTI_WINDOW_WINDOWS_MULTI_WINDOW_MANAGER_H_
#define DESKTOP_MULTI_WINDOW_WINDOWS_MULTI_WINDOW_MANAGER_H_

#include <cstdint>
#include <string>
#include <map>

#include "base_flutter_window.h"
#include "flutter_window.h"

class MultiWindowManager : public std::enable_shared_from_this<MultiWindowManager>, public FlutterWindowCallback {

 public:
  static MultiWindowManager *Instance();

  MultiWindowManager();

  int64_t Create(std::string args);

  void AttachFlutterMainWindow(HWND main_window_handle, std::unique_ptr<WindowChannel> window_channel);

  void SetWindowMaximizeSize(int64_t id, SIZE* size);

  void SetWindowMinimizeSize(int64_t id, SIZE* size);

  void MaximizeWindow(int64_t id);

  void MinimizeWindow(int64_t id);

  void RestoreWindow(int64_t id);

  void MaximizeOrRestoreWindow(int64_t id);

  void Show(int64_t id);

  void Hide(int64_t id);

  void Close(int64_t id);

  void SetFrame(int64_t id, double_t x, double_t y, double_t width, double_t height);

  void Center(int64_t id);

  void SetTitle(int64_t id, const std::string &title);

  void SetSize(int64_t id, double_t width, double_t height);

  void SetTitleBarHidden(int64_t id, bool isHidden);

  void DraggableWindow(int64_t id);

  // 设置窗口是否可以调整大小
  void SetResizable(int64_t id, bool resizable);

  flutter::EncodableList GetAllSubWindowIds();

  int64_t GetAcvtieWindowId();

  void OnWindowClose(int64_t id) override;

  void OnWindowDestroy(int64_t id) override;

  SIZE* GetWindowMaximize(HWND windowHandler);

  SIZE* GetWindowMinimize(HWND windowHandler);

  bool GetWindowResizable(HWND windowHandler);


 private:

  std::map<int64_t, std::unique_ptr<BaseFlutterWindow>> windows_;

  void HandleWindowChannelCall(
      int64_t from_window_id,
      int64_t target_window_id,
      const std::string &call,
      flutter::EncodableValue *arguments,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result
  );

};

#endif //DESKTOP_MULTI_WINDOW_WINDOWS_MULTI_WINDOW_MANAGER_H_
