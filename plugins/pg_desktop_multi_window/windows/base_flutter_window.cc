//
// Created by yang<PERSON> on 2022/1/27.
//

#include "base_flutter_window.h"
#include <Uxtheme.h>
#include <dwmapi.h>
#pragma comment(lib, "Dwmapi.lib")

namespace {
void CenterRectToMonitor(LPRECT prc) {
  HMONITOR hMonitor;
  MONITORINFO mi;
  RECT rc;
  int w = prc->right - prc->left;
  int h = prc->bottom - prc->top;

  //
  // get the nearest monitor to the passed rect.
  //
  hMonitor = MonitorFromRect(prc, MONITOR_DEFAULTTONEAREST);

  //
  // get the work area or entire monitor rect.
  //
  mi.cbSize = sizeof(mi);
  GetMonitorInfo(hMonitor, &mi);

  rc = mi.rcMonitor;

  prc->left = rc.left + (rc.right - rc.left - w) / 2;
  prc->top = rc.top + (rc.bottom - rc.top - h) / 2;
  prc->right = prc->left + w;
  prc->bottom = prc->top + h;

}

int GetDpiForWindowCompat(HWND hwnd) {
  HMODULE hUser32 = LoadLibrary(TEXT("user32.dll"));
  if (hUser32) {
    typedef UINT(WINAPI* GetDpiForWindow_t)(HWND);
    GetDpiForWindow_t pGetDpiForWindow =
      (GetDpiForWindow_t)GetProcAddress(hUser32, "GetDpiForWindow");

    if (pGetDpiForWindow) {
      UINT dpi = pGetDpiForWindow(hwnd);
      FreeLibrary(hUser32);
      return dpi;
    }
    FreeLibrary(hUser32);
  }

  HDC hdc = GetDC(hwnd);
  int dpi = GetDeviceCaps(hdc, LOGPIXELSX); // ��ȡˮƽ DPI
  ReleaseDC(hwnd, hdc);
  return dpi;
}

std::wstring Utf16FromUtf8(const std::string &string) {
  int size_needed = MultiByteToWideChar(CP_UTF8, 0, string.c_str(), -1, nullptr, 0);
  if (size_needed == 0) {
    return {};
  }
  std::wstring wstrTo(size_needed, 0);
  int converted_length = MultiByteToWideChar(CP_UTF8, 0, string.c_str(), -1, &wstrTo[0], size_needed);
  if (converted_length == 0) {
    return {};
  }
  return wstrTo;
}

}

void BaseFlutterWindow::SetWindowMaximizeSize(SIZE* size) {
  if (size == nullptr) {
    return;
  }
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  UINT dpi = GetDpiForWindowCompat(handle);
  double scale_factor = dpi / 96.0;
  SIZE maxSize = {
    static_cast<LONG>(size->cx * scale_factor),
    static_cast<LONG>(size->cy * scale_factor),
  };
  window_max_size_.cx = maxSize.cx;
  window_max_size_.cy = maxSize.cy;
}

void BaseFlutterWindow::SetWindowMinimizeSize(SIZE* size) {
  if (size == nullptr) {
    return;
  }
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  UINT dpi = GetDpiForWindowCompat(handle);
  double scale_factor = dpi / 96.0;
  SIZE minSize = {
    static_cast<LONG>(size->cx * scale_factor),
    static_cast<LONG>(size->cy * scale_factor),
  };
  window_min_size_.cx = minSize.cx;
  window_min_size_.cy = minSize.cy;
}

SIZE* BaseFlutterWindow::GetWindowMaximizeSize() {
  return &window_max_size_;
}

SIZE* BaseFlutterWindow::GetWindowMinimizeSize() {
  return &window_min_size_;
}

void BaseFlutterWindow::Center() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  RECT rc;
  GetWindowRect(handle, &rc);
  CenterRectToMonitor(&rc);
  SetWindowPos(handle, nullptr, rc.left, rc.top, 0, 0, SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);
}

bool BaseFlutterWindow::Equals(HWND window_handle){
  auto currentHanlde = GetWindowHandle();
  if (!currentHanlde) {
    return false;
  }
  if (currentHanlde == window_handle) {
    return true;
  } else {
    return false;
  }
}

void BaseFlutterWindow::SetBounds(double_t x, double_t y, double_t width, double_t height) {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  MoveWindow(handle, int32_t(x), int32_t(y),
             static_cast<int>(width),
             static_cast<int>(height),
             TRUE);
}

void BaseFlutterWindow::SetSize(double_t width, double_t height){
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  RECT rc;
  GetWindowRect(handle, &rc);
  SetWindowPos(handle, 0, rc.left, rc.top, rc.left + static_cast<int>(width), rc.top + static_cast<int>(height),0);
}

void BaseFlutterWindow::SetTitleBarHidden(bool isHidden) {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  LONG style = GetWindowLong(handle, GWL_STYLE);
  if (isHidden) {
    style &= ~WS_CAPTION;
    style |= WS_MINIMIZEBOX | WS_MAXIMIZEBOX | CS_DBLCLKS | WS_THICKFRAME;
  }
  else {
    style |= WS_CAPTION;
  }
  SetWindowLong(handle, GWL_STYLE, style);
  RECT rc;
  GetWindowRect(handle, &rc);
  SetWindowPos(handle, 0, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER | SWP_FRAMECHANGED | SWP_DRAWFRAME);
}

void BaseFlutterWindow::DraggableWindow(){
  auto handle = GetWindowHandle();
  ReleaseCapture();
  SendMessage(handle, WM_SYSCOMMAND, SC_MOVE | HTCAPTION, 0);
}

void BaseFlutterWindow::SetTitle(const std::string &title) {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  SetWindowText(handle, Utf16FromUtf8(title).c_str());
}

void BaseFlutterWindow::Close() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  PostMessage(handle, WM_SYSCOMMAND, SC_CLOSE, 0);
}

void BaseFlutterWindow::MaximizeWindow() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  PostMessage(handle, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
}

void BaseFlutterWindow::MinimizeWindow() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  PostMessage(handle, WM_SYSCOMMAND, SC_MINIMIZE, 0);
}

void BaseFlutterWindow::RestoreWindow() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  PostMessage(handle, WM_SYSCOMMAND, SC_RESTORE, 0);
}

void BaseFlutterWindow::MaximizeOrRestoreWindow() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  WINDOWPLACEMENT wp;
  wp.length = sizeof(WINDOWPLACEMENT);
  if (GetWindowPlacement(handle, &wp)) {
    if (wp.showCmd == SW_SHOWMAXIMIZED) {
      PostMessage(handle, WM_SYSCOMMAND, SC_RESTORE, 0);
    } else {
      PostMessage(handle, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
    }
  }
}

void BaseFlutterWindow::Show() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  ShowWindow(handle, SW_SHOW);

}

void BaseFlutterWindow::Hide() {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  ShowWindow(handle, SW_HIDE);
}

void BaseFlutterWindow::SetResizable(bool resizable) {
  auto handle = GetWindowHandle();
  if (!handle) {
    return;
  }
  windows_resizable_ = resizable;
  //LONG style = GetWindowLong(handle, GWL_STYLE);
  //if (resizable) {
  //  // 添加可调整大小的窗口样式
  //  style |= WS_THICKFRAME | WS_MAXIMIZEBOX;
  //} else {
  //  // 移除可调整大小的窗口样式
  //  style &= ~(WS_THICKFRAME | WS_MAXIMIZEBOX);
  //}
  //
  //SetWindowLong(handle, GWL_STYLE, style);
  //
  //// 强制重绘窗口边框以应用新样式
  //SetWindowPos(handle, NULL, 0, 0, 0, 0, 
  //             SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER | SWP_FRAMECHANGED);
}

bool BaseFlutterWindow::GetWindowResizable() {
    return windows_resizable_;
}
