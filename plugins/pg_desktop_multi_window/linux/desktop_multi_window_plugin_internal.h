//
// Created by boyan on 2022/1/28.
//

#ifndef DESKTOP_MULTI_WINDOW_LINUX_DESKTOP_MULTI_WINDOW_PLUGIN_INTERNAL_H_
#define DESKTOP_MULTI_WINDOW_LINUX_DESKTOP_MULTI_WINDOW_PLUGIN_INTERNAL_H_

#include "flutter_linux/flutter_linux.h"

void desktop_multi_window_plugin_register_with_registrar_internal(
    FlPluginRegistrar *registrar);

#endif //DESKTOP_MULTI_WINDOW_LINUX_DESKTOP_MULTI_WINDOW_PLUGIN_INTERNAL_H_
