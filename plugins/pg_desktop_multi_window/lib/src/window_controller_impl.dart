import 'dart:io';

import 'package:flutter/services.dart';

import 'channels.dart';
import 'window_controller.dart';

class WindowControllerMainImpl extends WindowController {
  final MethodChannel _channel = multiWindowChannel;

  // the id of this window
  final int _id;

  WindowControllerMainImpl(this._id);

  @override
  int get windowId => _id;

  @override
  Future<void> close() {
    return _channel.invokeMethod('close', _id);
  }

  @override
  Future<void> hide() {
    return _channel.invokeMethod('hide', _id);
  }

  @override
  Future<void> show() {
    return _channel.invokeMethod('show', _id);
  }

  @override
  Future<void> center() {
    return _channel.invokeMethod('center', _id);
  }

  @override
  Future<void> setFrame(Rect frame) {
    return _channel.invokeMethod('setFrame', <String, dynamic>{
      'windowId': _id,
      'left': frame.left,
      'top': frame.top,
      'width': frame.width,
      'height': frame.height,
    });
  }

  @override
  Future<void> setTitle(String title) {
    return _channel.invokeMethod('setTitle', <String, dynamic>{
      'windowId': _id,
      'title': title,
    });
  }

  @override
  Future<void> setSize(Size size) {
    return _channel.invokeMethod('setSize', <String, dynamic>{
      'windowId': _id,
      'width': size.width,
      'height': size.height,
    });
  }
  
  // @override
  // Future<void> setTitleBarHidden(bool isHidden){
  //   return _channel.invokeMethod('setTitleBarHidden',<String, dynamic>{
  //     'windowId': _id,
  //     'isHidden': isHidden,
  //   });
  // }

  @override
  Future<void> draggableWindow() {
    return _channel.invokeMethod('draggableWindow', <String, dynamic>{
      'windowId': _id,
    });
  }

  @override
  Future<void> resizable(bool resizable) {
    if (Platform.isMacOS || Platform.isWindows) {
      return _channel.invokeMethod('resizable', <String, dynamic>{
        'windowId': _id,
        'resizable': resizable,
      });
    } else {
      throw MissingPluginException(
        'This functionality is only available on macOS and Windows',
      );
    }
  }

  @override
  Future<void> setFrameAutosaveName(String name) {
    return _channel.invokeMethod('setFrameAutosaveName', <String, dynamic>{
      'windowId': _id,
      'name': name,
    });
  }

  @override
  Future<void> maximizeWindow() {
    return _channel.invokeMethod('maximizeWindow', <String, dynamic>{
      'windowId': _id,
    });
  }

  @override
  Future<void> minimizeWindow() {
    return _channel.invokeMethod('minimizeWindow', <String, dynamic>{
      'windowId': _id,
    });
  }

  @override
  Future<void> maximizeOrRestoreWindow() {
    return _channel.invokeMethod('maximizeOrRestoreWindow', <String, dynamic>{
      'windowId': _id,
    });
  }

  @override
  Future<void> restoreWindow() {
    return _channel.invokeMethod('restoreWindow', <String, dynamic>{
      'windowId': _id,
    });
  }

  @override
  Future<void> setWindowMaximizeSize(double width, double height) {
    return _channel.invokeMethod('setWindowMaximizeSize',<String, dynamic>{
      'windowId': _id,
      'width':width,
      'height':height,
    });
  }

  @override
  Future<void> setWindowMinimizeSize(double width, double height) {
    return _channel.invokeMethod('setWindowMinimizeSize',<String, dynamic>{
      'windowId': _id,
      'width':width,
      'height':height,
    });
  }
}
