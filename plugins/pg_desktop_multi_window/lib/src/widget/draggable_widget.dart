import 'package:flutter/cupertino.dart';

import '../../desktop_multi_window.dart';

class DraggableWidget extends StatelessWidget {
  final Widget? child;
  final bool doubleClickMaximize;

  DraggableWidget({this.child, this.doubleClickMaximize = true});

  WindowController? windowController;
  @override
  Widget build(BuildContext context) {
    _getActiveWindow();
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onPanStart: (details) async {
        windowController ??= await _getActiveWindow();
        windowController?.draggableWindow();
      },
      onDoubleTap: () async {
        if (doubleClickMaximize) {
          windowController ??= await _getActiveWindow();
          windowController?.maximizeOrRestoreWindow();
        }
      },
      child: child ?? Container(),
    );
  }

  _getActiveWindow() async {
    if (windowController == null) {
      var windowId = await DesktopMultiWindow.getActiveWindowId();
      windowController = WindowController.fromWindowId(windowId ?? -1);
    }
    return windowController;
  }
}
