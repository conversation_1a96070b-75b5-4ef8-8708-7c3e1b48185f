#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图灵精修打包运行脚本

这个脚本是win_package_runner.bat的Python版本，用于构建图灵精修软件包。
"""

import os
import sys
import subprocess
import argparse
import shutil
from pathlib import Path


# 全局设置，确保所有输出是实时的
os.environ["PYTHONUNBUFFERED"] = "1"


def run_command(command, error_message=None, additional_env=None):
    """运行命令并检查结果"""
    print(f"执行命令: {command}")
    
    # 设置环境变量强制使用UTF-8
    env = os.environ.copy()
    env["PYTHONIOENCODING"] = "utf-8"
    env["PYTHONUNBUFFERED"] = "1"  # 确保子进程也不缓冲输出
    
    # 添加额外的环境变量
    if additional_env:
        env.update(additional_env)
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', env=env)
    
    # 输出命令的输出
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    
    # 检查命令执行结果
    if result.returncode != 0:
        if error_message:
            print(f"错误: {error_message}")
        print(f"命令执行失败: {command}, 返回码: {result.returncode}")
        sys.exit(result.returncode)
    return result


def check_flutter_installed():
    """检查Flutter是否已安装"""
    try:
        env = os.environ.copy()
        env["PYTHONIOENCODING"] = "utf-8"
        
        result = subprocess.run(['where', 'flutter'], capture_output=True, text=True, encoding='utf-8', errors='replace', env=env)
        if result.returncode != 0:
            print("错误: Flutter 未安装或未添加到系统路径")
            sys.exit(1)
    except Exception as e:
        print(f"错误: 检查Flutter安装时出错: {e}")
        sys.exit(1)


def main():
    # 解析命令行参数
    if len(sys.argv) < 5:
        print("用法: python win_package_runner.py BUILD_VERSION BUILD_TIMESTAMP BUILD_PLATFORM BUILD_TYPE [DO_ENCRYPT] [DO_SIGN] [SIGNATURE_SHA1]")
        sys.exit(1)
    
    build_version = sys.argv[1]
    build_timestamp = sys.argv[2]
    build_platform = sys.argv[3]
    build_type = sys.argv[4]
    do_encrypt = sys.argv[5].lower() == "true" if len(sys.argv) > 5 else False
    do_sign = sys.argv[6].lower() == "true" if len(sys.argv) > 6 else False
    signature_sha1 = sys.argv[7] if len(sys.argv) > 7 else ""
    
    # 参数校验
    if not build_version:
        print("错误: 缺少版本参数")
        sys.exit(1)
    if not build_platform:
        print("错误: 缺少构建类型参数")
        sys.exit(1)
    
    # 设置路径变量
    exe_name = f"turing_art-{build_version}-windows-setup.exe"
    path_application_exe = "build\\windows\\x64\\runner\\Release\\turing_art.exe"
    backup_path = "build\\windows\\x64\\runner\\ReleaseCopy"
    release_path = "build\\windows\\x64\\runner\\Release"

    
    # 根据构建类型选择配置文件
    config_path = "package_unity_config.json"
    
    # 根据构建类型设置IS_DEBUG变量
    is_debug = "true" if build_type == "debug" else "false"
    
    # 检查必要的命令
    check_flutter_installed()
    
    print("==========================================")
    print(f"开始构建 {build_platform} 版本")
    print("==========================================")

        # 如果ReleaseCopy存在，直接使用
    # if os.path.exists(backup_path):
    #     print("Release备份文件夹已存在，直接使用...")
        
    #     # 将ReleaseCopy内容拷贝回Release文件夹
    #     print("将ReleaseCopy内容拷贝回Release文件夹...")
    #     if os.path.exists(release_path):
    #         print("删除现有Release目录...")
    #         try:
    #             shutil.rmtree(release_path)
    #         except Exception as e:
    #             print(f"Warning: 删除现有Release目录失败，继续执行... {e}")
        
    #     # 创建Release目录并复制文件
    #     print("从ReleaseCopy复制文件到Release文件夹...")
    #     try:
    #         shutil.copytree(backup_path, release_path)
    #         print(f"ReleaseCopy到Release文件夹复制成功: {release_path}")
    #     except Exception as e:
    #         print(f"Error: ReleaseCopy到Release文件夹复制失败: {e}")
    #         sys.exit(1)
        
    #     # 跳转到准备Unity资源步骤
    # else:
    # 正常构建流程
    print("[1] 清理Flutter构建目录...")
    run_command("flutter clean", "Flutter clean 失败")
    
    print("[2] 安装依赖...")
    run_command("flutter pub get", "依赖安装失败")
    
    print("[3] Flutter打包...")
    
    # 准备自定义环境变量传递给CMake
    cmake_env = {
        "IS_WIN7": "true" if build_platform == "win7" else "false",
    }

    run_command(f"flutter build windows --release --dart-define=IS_DEBUG_VALUE={is_debug}",
                "Flutter构建失败", additional_env=cmake_env)
    
    print("[4] 备份Release文件夹到ReleaseCopy...")
    # 如果备份目录存在，先删除
    if os.path.exists(backup_path):
        print("删除现有备份目录...")
        try:
            shutil.rmtree(backup_path)
        except Exception as e:
            print(f"Warning: 删除现有备份目录失败，继续执行... {e}")
    
    # 创建备份目录并复制文件
    print("创建备份目录并复制Release文件夹内容...")
    try:
        shutil.copytree(release_path, backup_path)
        print(f"Release文件夹备份成功: {backup_path}")
    except Exception as e:
        print(f"Error: Release文件夹备份失败: {e}")
        sys.exit(1)
    # [5] 准备Unity资源
    print("[1] 准备Unity资源")
    script_dir = os.path.dirname(os.path.abspath(__file__))
    full_config_path = os.path.join(script_dir, config_path)
    
    try:
        # 动态导入prepare_unity模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("prepare_unity", "prepare_unity.py")
        unity_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(unity_module)
        
        # 调用prepare_unity的main函数，传递配置文件路径参数
        unity_module.main([full_config_path])
        # 如果执行到这里没有抛出异常，则认为成功
        print("Unity资源准备成功")
    except Exception as e:
        print(f"错误: 调用Unity资源准备模块失败: {e}")
        sys.exit(1)
    
    # [6] 拷贝dll资源...
    print("[2] 拷贝dll资源...")
    run_command("dart windows/packaging/copy_dll.dart", "dll资源拷贝失败")
    
    
    # [7] 加密
    print("[4] 加密")
    if do_encrypt:
        encryption_mode = "Debug" if build_type == "debug" else "Release"
        print(f"开始执行加密模块，模式: {encryption_mode}")
        
        # 直接导入加密模块并调用，而不是通过subprocess调用
        try:
            # 动态导入win_vmprotect_encryption模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("win_vmprotect_encryption", "win_vmprotect_encryption.py")
            vmp_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(vmp_module)
            
            # 调用加密模块的main函数，传递参数
            result = vmp_module.main([encryption_mode])
            if result != 0:
                print("错误: 可执行文件加密失败")
                sys.exit(result)
        except Exception as e:
            print(f"错误: 调用加密模块失败: {e}")
            sys.exit(1)
    else:
        print("跳过加密步骤")
    
    # [8] 签名应用程序 exe文件
    print("[8] 签名应用程序 exe文件")
    if do_sign:
        run_command(f"win_package_signature.bat {path_application_exe} {signature_sha1}", "应用程序签名失败")
    else:
        print("跳过签名步骤（应用程序）")
    
    # [9] 构建NSIS setup exe文件
    print("[9] 构建inno setup exe文件")
    try:
        # 动态导入generate_nsis_setup模块
        import importlib.util
        nsis_setup_script = os.path.join("windows", "packaging", "exe", "generate_nsis_setup.py")
        spec = importlib.util.spec_from_file_location("generate_nsis_setup", nsis_setup_script)
        inno_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(inno_module)
        
        # 调用模块的main函数
        result = inno_module.main()
        if result != 0:
            print("错误: NSIS构建失败")
            sys.exit(result)
        
        # 根据generate_nsis_setup.py的代码，输出文件固定为：
        # dist/{version}/turing_art-{version}-windows-setup.exe
        output_file = Path(f"dist/{build_version}/{exe_name}")
        
        if output_file.exists():
            # 输出带特殊标记的文件路径，packager_launcher.py 将解析此行
            print(f"OUTPUT_FILE: {output_file.absolute()}")
        else:
            print(f"错误: 未找到NSIS生成的安装程序文件: {output_file}")
            sys.exit(1)
    except Exception as e:
        print(f"错误: 调用NSIS构建脚本失败: {e}")
        sys.exit(1)
    
    print("==========================================")
    print(f"{build_platform} 版本构建成功！")
    print("==========================================")
    return 0


if __name__ == "__main__":
    # 禁用输出缓冲
    try:
        # Python 3.7+支持reconfigure方法
        sys.stdout.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
    except AttributeError:
        # 适用于旧版Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)
    sys.exit(main()) 