import json
import os
import shutil
import sys

def main(arguments):
    # 默认配置
    config = {
        'projectName': 'turing_art',
        'isPackage': False,
        'isWin7': False,
        'isRelease': False,
        'sourceDataFile': 'Retouch_Data'
    }

    # 如果提供了参数，尝试读取JSON文件
    if arguments:
        try:
            config_file_path = arguments[0]
            if os.path.exists(config_file_path):
                with open(config_file_path, 'r', encoding='utf-8') as config_file:
                    user_config = json.load(config_file)
                    config.update(user_config)
                print(f'从文件加载配置：{json.dumps(config)}')
            else:
                print(f'配置文件不存在：{arguments[0]}')
        except Exception as e:
            print(f'读取配置文件失败：{e}')
    else:
        print(f'使用默认配置：{json.dumps(config)}')

    # 从配置中获取参数
    is_package = config['isPackage']
    is_release = config['isRelease']
    is_win7 = config['isWin7']
    fast_build = config.get('fast_build', False)

    # 获取当前脚本所在目录
    current_dir = os.getcwd()

    # 确定Unity源文件夹路径
    if fast_build and 'unity_artifacts_path' in config:
        # 极速构建模式：直接使用配置中的Unity构建产物路径
        unity_dir = config['unity_artifacts_path']
        print(f'⚡ 极速构建模式：使用Unity构建产物路径: {unity_dir}')
    else:
        # 常规模式：根据isRelease和isWin7确定源文件夹路径
        base_dir = 'release' if is_release else 'develop'
        unity_dir = os.path.join(current_dir, f'unityoutput{"win7" if is_win7 else "win10p"}', base_dir)
        print(f'🔄 常规模式：使用子模块路径: {unity_dir}')
    
    extra_dll_dir = os.path.join(current_dir, 'windows', 'packaging', 'extra')

    # 获取目标文件夹路径
    target_dir = os.path.join(current_dir, 'build', 'windows', 'x64', 'runner', 'Release' if is_package else 'Debug')

    # 检查源目录是否存在
    if not os.path.exists(unity_dir):
        print(f'❌ Unity源目录不存在: {unity_dir}')
        sys.exit(1)

    if not os.path.exists(extra_dll_dir):
        print(f'❌ Extra DLL目录不存在: {extra_dll_dir}')
        sys.exit(1)

    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)
    if not os.path.exists(target_dir):
        print(f'❌ 无法创建目标目录: {target_dir}')
        sys.exit(1)

    print(f'📁 源目录: {unity_dir}')
    print(f'📁 目标目录: {target_dir}')

    try:
        # 复制Unity文件
        print('🔄 开始复制Unity文件...')
        copy_dir_sync(unity_dir, target_dir, is_release)

        # 复制额外DLL文件
        print('🔄 开始复制额外DLL文件...')
        copy_dir_sync(extra_dll_dir, target_dir, is_release)

        # 重命名数据文件夹
        source_data_path = os.path.join(target_dir, config['sourceDataFile'])
        target_data_path = os.path.join(target_dir, f"{config['projectName']}_Data")

        if os.path.exists(source_data_path):
            rename_dir(source_data_path, target_data_path)
            print('✅ 数据文件夹重命名成功')
        else:
            print(f'❌ 源数据文件夹不存在: {source_data_path}')
            sys.exit(1)

        print('✅ 所有文件复制完成')
    except Exception as e:
        print(f'❌ 文件复制过程中发生错误: {e}')
        sys.exit(1)

def rename_dir(old_path, new_path):
    try:
        # 如果源路径和目标路径相同，直接返回
        if old_path == new_path:
            print(f'⏭️ 源路径和目标路径相同，跳过重命名: {old_path}')
            return

        # 如果目标文件夹已存在，先删除它
        if os.path.exists(new_path):
            try:
                shutil.rmtree(new_path)
                print(f'⚠️ 目标文件夹已存在，已删除: {new_path}')
            except PermissionError:
                print(f'⚠️ 无法删除目标文件夹(权限问题): {new_path}')
                # 尝试关闭可能的文件句柄
                try:
                    import subprocess
                    subprocess.run(['taskkill', '/F', '/IM', 'explorer.exe'], capture_output=True)
                    # 给系统一点时间来释放文件句柄
                    import time
                    time.sleep(1)
                    # 重启资源管理器
                    subprocess.Popen(['explorer.exe'])
                    # 再次尝试删除
                    shutil.rmtree(new_path)
                    print(f'✅ 成功删除目标文件夹: {new_path}')
                except Exception as e:
                    print(f'⚠️ 尝试强制关闭文件句柄后仍无法删除目标文件夹: {e}')
                    # 如果无法删除，尝试重命名旧文件夹为备份
                    backup_path = f"{new_path}_bak_{int(time.time())}"
                    if os.path.exists(new_path):
                        try:
                            os.rename(new_path, backup_path)
                            print(f'✅ 已将现有目标文件夹重命名为: {backup_path}')
                        except Exception as backup_error:
                            print(f'❌ 无法备份现有文件夹: {backup_error}')
                            raise
            except Exception as e:
                print(f'⚠️ 删除目标文件夹失败: {e}')
                raise

        # 尝试多次重命名，处理可能的文件占用问题
        max_attempts = 3
        attempt = 0
        success = False
        
        while attempt < max_attempts and not success:
            attempt += 1
            try:
                # 尝试直接重命名
                os.rename(old_path, new_path)
                success = True
                print(f'✅ 文件夹重命名成功: {old_path} -> {new_path}')
            except PermissionError:
                print(f'⚠️ 重命名失败(权限问题)，尝试第 {attempt}/{max_attempts} 次')
                
                if attempt < max_attempts:
                    # 等待一段时间后重试
                    import time
                    time.sleep(2)
                else:
                    # 如果重命名失败，尝试复制内容然后删除源文件夹
                    print('🔄 尝试使用复制/删除方法代替重命名...')
                    try:
                        # 确保目标目录存在
                        os.makedirs(new_path, exist_ok=True)
                        
                        # 复制所有内容
                        for item in os.listdir(old_path):
                            source_item = os.path.join(old_path, item)
                            dest_item = os.path.join(new_path, item)
                            
                            if os.path.isdir(source_item):
                                shutil.copytree(source_item, dest_item)
                            else:
                                shutil.copy2(source_item, dest_item)
                        
                        # 尝试删除源文件夹
                        try:
                            shutil.rmtree(old_path)
                            print(f'✅ 已通过复制/删除方式完成目录迁移: {old_path} -> {new_path}')
                            success = True
                        except Exception as rm_error:
                            print(f'⚠️ 已复制文件，但无法删除源文件夹: {rm_error}')
                            print(f'✅ 目录复制已完成，但源文件夹 {old_path} 仍然存在')
                            success = True
                    except Exception as copy_error:
                        print(f'❌ 复制/删除方法失败: {copy_error}')
                        raise
            except Exception as e:
                print(f'❌ 重命名过程中发生其他错误: {e}')
                if attempt < max_attempts:
                    import time
                    time.sleep(2)
                else:
                    raise
                    
        if not success:
            raise Exception(f"在 {max_attempts} 次尝试后仍无法重命名文件夹")
            
    except Exception as e:
        print(f'❌ 文件夹重命名失败: {e}')
        sys.exit(1)

def copy_dir_sync(source_dir, target_dir, is_release):
    print(f'📁 开始复制目录: {source_dir} -> {target_dir}')

    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)

    try:
        # 统计计数器
        file_count = 0
        dir_count = 0
        skipped_count = 0
        skipped_files = []

        # 遍历源目录中的所有文件和子目录
        for root, dirs, files in os.walk(source_dir):
            # 计算相对路径
            rel_path = os.path.relpath(root, source_dir)
            target_root = os.path.join(target_dir, rel_path) if rel_path != '.' else target_dir
            
            # 创建目标子目录
            os.makedirs(target_root, exist_ok=True)
            dir_count += 1
            
            # 复制文件
            for file in files:
                source_file = os.path.join(root, file)
                target_file = os.path.join(target_root, file)
                
                # 排除.exe文件、build_metadata.json文件和在release模式下排除git_log.txt文件
                if not file.lower().endswith('retouch.exe') and file.lower() != 'build_metadata.json' and not (is_release and file.lower() == 'git_log.txt'):
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_file), exist_ok=True)
                    shutil.copy2(source_file, target_file)
                    file_count += 1
                    
                    # 每复制100个文件显示一次进度
                    if file_count % 100 == 0:
                        print(f'⏱️ 已复制 {file_count} 个文件...')
                else:
                    skipped_count += 1
                    skipped_files.append(source_file)
                    print(f'⏭️ 跳过文件: {source_file}')

        print(f'✅ 目录复制完成: {source_dir}')
        print(f'📊 统计: 复制了 {file_count} 个文件, {dir_count} 个目录, 跳过了 {skipped_count} 个文件')

        if skipped_files:
            print('📋 跳过的文件列表:')
            for file in skipped_files:
                print(f'   - {file}')
    except Exception as e:
        print(f'❌ 目录复制失败: {e}')
        sys.exit(1)

if __name__ == "__main__":
    main(sys.argv[1:])