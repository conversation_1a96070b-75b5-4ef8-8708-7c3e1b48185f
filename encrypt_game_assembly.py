#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import shutil
import subprocess
import datetime
import traceback
from pathlib import Path
import argparse

def encrypt_file(vmprotect_path, vmp_project_file, input_file, output_dir):
    """加密单个文件"""
    print(f"\n处理文件: {input_file}")
    print(f"输出目录: {output_dir}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置文件路径
    target_file = os.path.join(output_dir, "GameAssembly.dll")
    raw_file = os.path.join(output_dir, "GameAssembly_raw.dll")
    vmp_file_path = os.path.join(output_dir, os.path.basename(vmp_project_file))
    
    # 复制源文件到输出目录（保持原始文件名）
    try:
        print(f"复制源文件到输出目录: {target_file}")
        shutil.copy2(input_file, target_file)
    except Exception as e:
        print(f"❌ 错误: 复制源文件失败: {str(e)}")
        return False
    
    # 复制VMP工程文件到输出目录
    try:
        print(f"复制VMP工程文件: {vmp_file_path}")
        shutil.copy2(vmp_project_file, vmp_file_path)
    except Exception as e:
        print(f"❌ 错误: 复制VMP工程文件失败: {str(e)}")
        return False
    
    # 执行VMProtect加密
    try:
        print("="*50)
        print("VMProtect输出开始:")
        print("="*50)
        
        # 切换到输出目录执行VMProtect
        original_dir = os.getcwd()
        os.chdir(output_dir)
        
        try:
            cmd = [vmprotect_path, os.path.basename(vmp_file_path)]
            env = os.environ.copy()
            env["PYTHONIOENCODING"] = "gbk"
            env["PYTHONUNBUFFERED"] = "1"
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='gbk',
                errors='replace',
                env=env,
                bufsize=1
            )
            
            for line in process.stdout:
                line = line.rstrip()
                print(line)
            
            return_code = process.wait()
            
            print("="*50)
            print(f"VMProtect输出结束 (返回码: {return_code})")
            print("="*50)
            
            if return_code != 0:
                print(f"❌ 错误: VMProtect处理失败，错误代码: {return_code}")
                return False
                
        finally:
            # 切回原始目录
            os.chdir(original_dir)
            
    except Exception as e:
        print(f"❌ 错误: 执行VMProtect失败: {str(e)}")
        traceback.print_exc()
        return False
    
    # 检查加密后的文件（尝试多个可能的文件名）
    protected_files = [
        os.path.join(output_dir, "GameAssembly.dll.vmp.dll"),
        os.path.join(output_dir, "GameAssembly.vmp.dll"),
        os.path.join(output_dir, "GameAssembly.dll.vmp")
    ]
    
    protected_file = None
    for file_path in protected_files:
        if os.path.exists(file_path):
            protected_file = file_path
            print(f"找到加密后的文件: {file_path}")
            break
    
    if protected_file is None:
        print("❌ 错误: 未找到加密后的文件，尝试查找的文件:")
        for file_path in protected_files:
            print(f"  - {file_path}")
        print("\n当前目录文件列表:")
        for file in os.listdir(output_dir):
            print(f"  - {file}")
        return False
    
    # 重命名源文件为原始文件
    try:
        if os.path.exists(raw_file):
            os.remove(raw_file)
        os.rename(target_file, raw_file)
        print(f"✅ 成功: 源文件已重命名为: {raw_file}")
    except Exception as e:
        print(f"❌ 错误: 重命名源文件失败: {str(e)}")
        return False
    
    # 重命名加密后的文件为目标文件名
    try:
        os.rename(protected_file, target_file)
        print(f"✅ 成功: 加密文件已保存到: {target_file}")
    except Exception as e:
        print(f"❌ 错误: 重命名加密文件失败: {str(e)}")
        return False
    
    # 清理临时文件
    try:
        os.remove(vmp_file_path)
    except Exception as e:
        print(f"⚠️ 警告: 清理临时文件失败: {str(e)}")
    
    return True

def copy_to_unity_output(encrypted_file, platform, build_type):
    """复制加密后的文件到Unity输出目录"""
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建目标目录路径
    target_dir_name = "unityoutputwin7" if platform == "win7" else "unityoutputwin10p"
    target_dir = os.path.join(script_dir, target_dir_name, build_type)
    
    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)
    
    # 构建目标文件路径
    target_file = os.path.join(target_dir, "GameAssembly.dll")
    
    try:
        print(f"\n复制文件到Unity输出目录:")
        print(f"源文件: {encrypted_file}")
        print(f"目标目录: {target_dir}")
        shutil.copy2(encrypted_file, target_file)
        print(f"✅ 成功: 文件已复制到 {target_file}")
        return True
    except Exception as e:
        print(f"❌ 错误: 复制文件失败: {str(e)}")
        return False

def main():
    # 默认输入目录
    DEFAULT_INPUT_DIR = "D:\\Code\\turing-codes\\sugoi_retouch\\Sugoi-Retouch\\BuildOutput\\BuildOutputSrc"
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description='GameAssembly.dll 加密工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
示例:
  1. 使用默认输入目录和release构建类型:
     python encrypt_game_assembly.py

  2. 指定输入目录:
     python encrypt_game_assembly.py D:\\Codes\\Libraries\\sugoi_retouch\\Sugoi-Retouch\\BuildOutput\\BuildOutputSrc

  3. 指定使用develop构建类型:
     python encrypt_game_assembly.py --build-type develop

  4. 同时指定输入目录和构建类型:
     python encrypt_game_assembly.py D:\\Codes\\Libraries\\sugoi_retouch\\Sugoi-Retouch\\BuildOutput\\BuildOutputSrc --build-type develop

输出目录说明:
  - Win7版本将输出到: <脚本目录>/unityoutputwin7/<build_type>/GameAssembly.dll
  - Win10版本将输出到: <脚本目录>/unityoutputwin10p/<build_type>/GameAssembly.dll
  - build_type 可以是 develop 或 release (默认为 release)
'''
    )
    parser.add_argument('input_dir', 
                      nargs='?',  # 使参数变为可选
                      default=DEFAULT_INPUT_DIR,
                      help=f'输入目录路径，包含 Windows_Win7_Intel64 和 Windows_Win10_Intel64 子目录 (默认: {DEFAULT_INPUT_DIR})')
    parser.add_argument('--build-type', 
                      choices=['develop', 'release'], 
                      default='release',
                      help='构建类型: develop 或 release (默认: release)')
    args = parser.parse_args()
    
    input_dir = args.input_dir
    build_type = args.build_type
    vmprotect_path = "C:\\Program Files\\VMProtect Professional\\VMProtect_Con.exe"
    vmp_project_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "windows", "vmprotect_projects", "GameAssembly.dll.vmp")
    
    # 检查输入目录
    if not os.path.exists(input_dir):
        print(f"❌ 错误: 输入目录不存在: {input_dir}")
        return 1
    
    # 检查VMProtect程序
    if not os.path.exists(vmprotect_path):
        print(f"❌ 错误: 找不到VMProtect程序: {vmprotect_path}")
        return 1
    
    # 检查VMP工程文件
    if not os.path.exists(vmp_project_file):
        print(f"❌ 错误: VMP工程文件不存在: {vmp_project_file}")
        return 1
    
    # 定义要处理的文件路径
    files_to_process = [
        {
            "input": os.path.join(input_dir, "Windows_Win7_Intel64", "GameAssembly.dll"),
            "output": os.path.join(input_dir, "vmprotected", "win7")
        },
        {
            "input": os.path.join(input_dir, "Windows_Win10_Intel64", "GameAssembly.dll"),
            "output": os.path.join(input_dir, "vmprotected", "win10")
        }
    ]
    
    # 检查所有需要处理的文件是否存在
    missing_files = []
    for file_info in files_to_process:
        if not os.path.exists(file_info["input"]):
            missing_files.append(file_info["input"])
    
    if missing_files:
        print("❌ 错误: 以下文件不存在:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return 1
    
    # 清理输出目录
    print("\n清理输出目录...")
    for file_info in files_to_process:
        output_dir = file_info["output"]
        if os.path.exists(output_dir):
            try:
                print(f"删除目录: {output_dir}")
                shutil.rmtree(output_dir)
            except Exception as e:
                print(f"❌ 错误: 清理输出目录失败 {output_dir}: {str(e)}")
                return 1
    
    print("==========================================")
    print("🛠 GameAssembly.dll 加密工具")
    print("==========================================")
    print(f"输入目录: {input_dir}")
    print(f"VMProtect路径: {vmprotect_path}")
    print(f"VMP工程文件: {vmp_project_file}")
    print("==========================================")
    
    success = True
    for file_info in files_to_process:
        if not encrypt_file(vmprotect_path, vmp_project_file, file_info["input"], file_info["output"]):
            success = False
    
    if success:
        print("\n==========================================")
        print("🎉 所有文件加密完成！")
        print("==========================================")
        
        # 复制文件到Unity输出目录
        print(f"\n开始复制文件到Unity输出目录 (构建类型: {build_type})...")
        copy_success = True
        
        # 复制win7文件
        win7_file = os.path.join(files_to_process[0]["output"], "GameAssembly.dll")
        if not copy_to_unity_output(win7_file, "win7", build_type):
            copy_success = False
        
        # 复制win10文件
        win10_file = os.path.join(files_to_process[1]["output"], "GameAssembly.dll")
        if not copy_to_unity_output(win10_file, "win10", build_type):
            copy_success = False
        
        if copy_success:
            print("\n==========================================")
            print("🎉 所有文件复制完成！")
            print("==========================================")
            return 0
        else:
            print("\n==========================================")
            print("❌ 文件复制过程中出现错误！")
            print("==========================================")
            return 1
    else:
        print("\n==========================================")
        print("❌ 加密过程中出现错误！")
        print("==========================================")
        return 1

if __name__ == "__main__":
    # 设置全局变量禁用缓冲
    os.environ["PYTHONUNBUFFERED"] = "1"
    
    # 修复Windows控制台的编码问题
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
    except AttributeError:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)
    
    sys.exit(main()) 