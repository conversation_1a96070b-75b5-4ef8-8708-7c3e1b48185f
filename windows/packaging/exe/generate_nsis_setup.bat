@echo off
echo Starting NSIS build process...

:: Set path variables
set SCRIPT_DIR=%~dp0
:: Remove trailing backslash
IF %SCRIPT_DIR:~-1%==\ SET SCRIPT_DIR=%SCRIPT_DIR:~0,-1%

echo Script directory: %SCRIPT_DIR%
cd %SCRIPT_DIR%

:: Run Dart script to generate NSI file
echo Generating NSI file...
call dart generate_nsi.dart
if %ERRORLEVEL% NEQ 0 (
    echo Failed to generate NSI file!
    pause
    exit /b 1
)

:: Check if NSI file exists
echo Checking NSI file: %SCRIPT_DIR%\setup.nsi
if not exist "%SCRIPT_DIR%\setup.nsi" (
    echo Error: NSI file not found!
    dir "%SCRIPT_DIR%"
    pause
    exit /b 1
)

:: Compile installer using makensis command
echo Compiling installer...
echo Command: makensis "%SCRIPT_DIR%\setup.nsi"

:: Use call command to ensure return to batch file after executing external program
call makensis "%SCRIPT_DIR%\setup.nsi"

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

:: Get project root directory - using absolute path
for %%i in ("%SCRIPT_DIR%\..\..\..") do set PROJECT_ROOT=%%~fi
echo Project root directory: %PROJECT_ROOT%

:: Check if project root directory exists
if not exist "%PROJECT_ROOT%" (
    echo Error: Project root directory not found: %PROJECT_ROOT%
    echo Current directory: %CD%
    pause
    exit /b 1
)

:: Extract version number
echo Extracting version from filename...
for %%f in ("%SCRIPT_DIR%\turing_art-*-windows-setup.exe") do (
    set INSTALLER_FILE=%%~nxf
    goto :found_installer
)

:found_installer
if defined INSTALLER_FILE (
    echo Found installer: %INSTALLER_FILE%
    :: Extract version number from filename
    for /f "tokens=2 delims=-" %%v in ("%INSTALLER_FILE%") do set APP_VERSION=%%v
    echo Version: %APP_VERSION%
) else (
    echo Warning: No installer file found
    set APP_VERSION=unknown
)

:: Create dist folder with version subdirectory
echo Creating dist directory...
set DIST_DIR=%PROJECT_ROOT%\dist\%APP_VERSION%
if not exist "%DIST_DIR%" (
    mkdir "%DIST_DIR%"
    echo Created directory: %DIST_DIR%
) else (
    echo Directory already exists: %DIST_DIR%
)

:: Copy installer to dist directory
echo Copying installer to dist directory...
if defined INSTALLER_FILE (
    copy "%SCRIPT_DIR%\%INSTALLER_FILE%" "%DIST_DIR%\"
    if %ERRORLEVEL% EQU 0 (
        echo Successfully copied %INSTALLER_FILE% to %DIST_DIR%
        :: Delete the original installer file from script directory
        del "%SCRIPT_DIR%\%INSTALLER_FILE%"
        if %ERRORLEVEL% EQU 0 (
            echo Deleted original installer file: %INSTALLER_FILE%
        ) else (
            echo Warning: Failed to delete original installer file
        )
    ) else (
        echo Failed to copy installer to dist directory
        pause
        exit /b 1
    )
) else (
    echo No installer file to copy
    pause
    exit /b 1
)

:: Clean up intermediate files
echo Cleaning up intermediate files...
if exist "%SCRIPT_DIR%\setup.nsi" (
    del "%SCRIPT_DIR%\setup.nsi"
    if %ERRORLEVEL% EQU 0 (
        echo Deleted intermediate file: setup.nsi
    ) else (
        echo Warning: Failed to delete setup.nsi
    )
) else (
    echo setup.nsi not found, skipping cleanup
)

echo NSIS build process completed successfully!
echo Installer location: %DIST_DIR%\%INSTALLER_FILE%