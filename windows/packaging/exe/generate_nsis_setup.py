#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NSIS构建脚本

这个脚本用于生成和编译NSIS安装程序
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, error_message=None):
    """运行命令并检查结果"""
    print(f"执行命令: {command}")
    
    # 设置环境变量强制使用UTF-8
    env = os.environ.copy()
    env["PYTHONIOENCODING"] = "utf-8"
    env["PYTHONUNBUFFERED"] = "1"
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', env=env)
    
    # 输出命令的输出
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    
    # 检查命令执行结果
    if result.returncode != 0:
        if error_message:
            print(f"错误: {error_message}")
        print(f"命令执行失败: {command}, 返回码: {result.returncode}")
        sys.exit(result.returncode)
    return result

def main():
    """主函数"""
    print("开始NSIS构建过程...")
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"脚本目录: {script_dir}")
    
    # 运行Dart脚本生成NSI文件
    print("生成NSI文件...")
    run_command(f"dart {os.path.join(script_dir, 'generate_nsi.dart')}", "生成NSI文件失败")
    
    # 检查NSI文件是否存在
    nsi_file = os.path.join(script_dir, "setup.nsi")
    print(f"检查NSI文件: {nsi_file}")
    if not os.path.exists(nsi_file):
        print("错误: NSI文件未找到!")
        print(f"目录内容: {os.listdir(script_dir)}")
        sys.exit(1)
    
    # 编译安装程序
    print("编译安装程序...")
    print(f"命令: makensis \"{nsi_file}\"")
    run_command(f"makensis \"{nsi_file}\"", "编译失败")
    
    # 获取项目根目录
    project_root = os.path.abspath(os.path.join(script_dir, "..", "..", ".."))
    print(f"项目根目录: {project_root}")
    
    if not os.path.exists(project_root):
        print(f"错误: 项目根目录未找到: {project_root}")
        print(f"当前目录: {os.getcwd()}")
        print(f"脚本目录: {script_dir}")
        sys.exit(1)
    
    # 获取版本号
    print("从文件名中提取版本号...")
    installer_files = list(Path(script_dir).glob("turing_art-*-windows-setup.exe"))
    app_version = "unknown"
    
    if installer_files:
        # 从第一个匹配的文件名中提取版本号
        filename = installer_files[0].name
        try:
            app_version = filename.split("-")[1]
            print(f"找到版本: {app_version}")
        except IndexError:
            print("警告: 无法从文件名中提取版本号")
    else:
        print("警告: 未找到安装程序文件，尝试从setup.nsi中获取版本号...")
        try:
            with open(nsi_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if "!define VERSION" in line:
                        app_version = line.split('"')[1]
                        print(f"从setup.nsi中找到版本: {app_version}")
                        break
        except Exception as e:
            print(f"警告: 从setup.nsi读取版本号失败: {e}")
    
    # 创建dist文件夹（包含版本号子目录）
    print("创建dist目录...")
    dist_dir = os.path.join(project_root, "dist", app_version)
    if not os.path.exists(dist_dir):
        os.makedirs(dist_dir)
        print(f"已创建目录: {dist_dir}")
    else:
        print(f"目录已存在: {dist_dir}")
    
    # 复制安装程序到dist目录
    print("复制安装程序到dist目录...")
    if installer_files:
        installer_file = installer_files[0]
        dest_file = os.path.join(dist_dir, installer_file.name)
        
        import shutil
        shutil.copy2(str(installer_file), dest_file)
        print(f"成功复制 {installer_file.name} 到 {dist_dir}")
        
        # 删除源文件
        try:
            os.remove(str(installer_file))
            print(f"已删除原始安装程序文件: {installer_file.name}")
        except Exception as e:
            print(f"警告: 删除原始文件失败: {e}")
        
        print(f"\n构建完成!")
        print(f"安装程序位置: {dest_file}")
        print(f"版本: {app_version}")
    else:
        print("错误: 未找到安装程序文件")
        sys.exit(1)
    
    # 清理中间文件
    print("清理中间文件...")
    nsi_file = os.path.join(script_dir, "setup.nsi")
    if os.path.exists(nsi_file):
        try:
            os.remove(nsi_file)
            print(f"已删除中间文件: setup.nsi")
        except Exception as e:
            print(f"警告: 删除 setup.nsi 失败: {e}")
    else:
        print("setup.nsi 未找到，跳过清理")
    
    return 0

if __name__ == "__main__":
    # 设置UTF-8编码
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
    except AttributeError:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)

    sys.exit(main())