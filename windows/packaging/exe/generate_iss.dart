import 'dart:io';

import 'package:yaml/yaml.dart';

void main() async {
  // 获取构建类型参数

  // 获取脚本所在目录
  final scriptDir = File(Platform.script.toFilePath()).parent.path;
  // 获取项目根目录
  final projectDir = Directory(scriptDir).parent.parent.parent.path;

  print('脚本目录: $scriptDir');
  print('项目根目录: $projectDir');

  // 读取pubspec.yaml获取版本号
  final pubspecFile = File('$projectDir/pubspec.yaml');
  if (!pubspecFile.existsSync()) {
    print('Error: pubspec.yaml not found at ${pubspecFile.path}');
    exit(1);
  }
  final yamlString = await pubspecFile.readAsString();
  final pubspec = loadYaml(yamlString) as Map<dynamic, dynamic>;
  final version = pubspec['version'] as String;
  print('应用版本: $version');

  // 读取make_config.yaml文件
  final configFile = File('$scriptDir/make_config.yaml');
  if (!configFile.existsSync()) {
    print('Error: make_config.yaml not found at ${configFile.path}');
    exit(1);
  }

  // 解析YAML配置
  final configYamlString = await configFile.readAsString();
  final config = loadYaml(configYamlString) as Map<dynamic, dynamic>;

  // 生成ISS文件内容
  final issContent = generateIssContent(config, version, projectDir, 'Release');

  // 写入ISS文件
  final issFilePath = '${scriptDir}${Platform.pathSeparator}setup.iss';
  print('正在创建ISS文件：$issFilePath');
  final issFile = File(issFilePath);
  await issFile.writeAsString(issContent);
  print('已生成ISS文件: ${issFile.path}');

  // 输出成功信息
  print('ISS文件生成成功: ${issFile.path}');

  // 输出文件内容用于调试
  print(
      '\nISS文件内容预览:\n${issContent.substring(0, issContent.length > 500 ? 500 : issContent.length)}...\n');
}

String generateIssContent(Map<dynamic, dynamic> config, String version,
    String projectDir, String buildType) {
  final appId = config['app_id'] as String;
  final publisher = config['publisher'] as String;
  final publisherUrl = config['publisher_url'] as String;
  final displayName = config['display_name'] as String;
  final installDirName = config['install_dir_name'] as String;

  // 使用完整路径作为图标路径
  final iconPath = '$projectDir\\windows\\runner\\resources\\app_icon.ico';
  print('图标路径: $iconPath');
  final setupIconFile = iconPath;
  final locales = config['locales'] as List<dynamic>;

  // 根据构建类型选择源目录
  final sourceDir = '$projectDir\\build\\windows\\x64\\runner\\$buildType';

  return '''
[Setup]
AppId=$appId
AppVersion=$version
VersionInfoVersion=$version
VersionInfoDescription=$displayName Setup
VersionInfoTextVersion=$version
VersionInfoCopyright=© ${DateTime.now().year} $publisher
AppName=$displayName
AppPublisher=$publisher
AppPublisherURL=$publisherUrl
AppSupportURL=$publisherUrl
AppUpdatesURL=$publisherUrl
DefaultDirName={autopf}\\$installDirName
DisableProgramGroupPage=yes
OutputDir=.
OutputBaseFilename=turing_art-$version-windows-setup
Compression=lzma
SolidCompression=yes
SetupIconFile=$setupIconFile
WizardStyle=modern
PrivilegesRequired=none
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; 强制使用中文界面
LanguageDetectionMethod=none
ShowLanguageDialog=no

[Languages]
Name: "chinesesimplified"; MessagesFile: "compiler:Languages\\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce

[Files]
Source: "$sourceDir\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{autoprograms}\\$displayName"; Filename: "{app}\\turing_art.exe"
Name: "{autodesktop}\\$displayName"; Filename: "{app}\\turing_art.exe"; Tasks: desktopicon

[Messages]
chinesesimplified.WelcomeLabel1=欢迎使用 图灵精修 安装向导
chinesesimplified.WelcomeLabel2=这将在您的计算机上安装 图灵精修 %n%n建议您在继续之前关闭所有其他应用程序。

[Run]
Filename: "{app}\\turing_art.exe"; Description: "{cm:LaunchProgram,$displayName}"; Flags: nowait postinstall skipifsilent
''';
}
