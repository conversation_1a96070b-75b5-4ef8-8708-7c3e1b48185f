# NSIS 安装包构建说明

本文档说明如何使用 NSIS 替代 Inno Setup 来构建 Windows 安装包。

## 前提条件

1. **安装 NSIS**
   - 下载并安装 NSIS: https://nsis.sourceforge.io/Download
   - 确保 `makensis.exe` 在系统 PATH 中
   - 推荐版本: NSIS 3.08 或更高

2. **安装 Dart SDK**
   - 确保 `dart` 命令可用

## 文件说明

### 新增的 NSIS 相关文件

- `generate_nsis.bat` - Windows 批处理脚本，用于构建 NSIS 安装包
- `generate_nsis.py` - Python 版本的构建脚本
- `generate_nsi.dart` - Dart 脚本，用于生成 NSIS 脚本文件 (.nsi)
- `setup.nsi` - 自动生成的 NSIS 安装脚本（构建时生成）

### 保留的配置文件

- `make_config.yaml` - 配置文件，与 Inno Setup 版本兼容

## 使用方法

### 方法 1: 使用批处理脚本（推荐）

```cmd
cd d:\Projects\turingartclient\windows\packaging\exe
generate_nsis.bat
```

### 方法 2: 使用 Python 脚本

```cmd
cd d:\Projects\turingartclient\windows\packaging\exe
python generate_nsis.py
```

### 方法 3: 手动执行步骤

```cmd
# 1. 生成 NSI 文件
dart generate_nsi.dart

# 2. 编译安装程序
makensis setup.nsi
```

## 功能特性

### 保持的原有功能

- ✅ 自动读取版本号从 `pubspec.yaml`
- ✅ 支持中文界面
- ✅ 创建桌面快捷方式（可选）
- ✅ 创建开始菜单快捷方式
- ✅ 完整的卸载功能
- ✅ 注册表写入和清理
- ✅ 先决条件安装（如 VC++ Redistributable）
- ✅ 64位系统检查
- ✅ 已安装版本检测和卸载
- ✅ 现代化安装界面

### NSIS 相比 Inno Setup 的优势

- 🚀 **更小的包大小**: NSIS 生成的安装包通常比 Inno Setup 小 20-30%
- 🔧 **更灵活的脚本**: 支持更复杂的安装逻辑
- 🎨 **更好的界面定制**: 支持更多的界面定制选项
- 📦 **无大小限制**: 没有 Inno Setup 的包大小限制问题
- 🌐 **更好的多语言支持**: 内置更多语言包

## 配置说明

配置文件 `make_config.yaml` 保持不变，支持以下配置项：

```yaml
app_id: 5B599538-42B1-4826-A479-AF079F21A65D  # 应用程序 GUID
publisher: ChengDu Pinguo CaiYun              # 发布者
publisher_url: https://www.tuling.art          # 发布者网址
display_name: 图灵精修                         # 显示名称
create_desktop_icon: true                      # 是否创建桌面图标
install_dir_name: 图灵精修                     # 安装目录名称
setup_icon_file: ../../windows/runner/resources/app_icon.ico  # 安装程序图标
prerequisites:                                 # 先决条件
  - name: Microsoft Visual C++ Redistributable for Visual Studio 2022 x64
    command: ../vcruntime/VC_redist.x64.exe
    args: ["/install", "/quiet", "/norestart"]
include_files:                                 # 额外包含的文件
  - source: "C:/Windows/System32/vcruntime140.dll"
    destination: "../../../build/windows/x64/runner/Release"
build_args:                                    # 构建参数
  dart-define:
    ENV: ${ENV}
locales:                                       # 支持的语言
  - zh
```

## 输出文件

构建成功后，安装包将生成在：
- 本地: `windows/packaging/exe/turing_art-{version}-windows-setup.exe`
- 分发: `dist/turing_art-{version}-windows-setup.exe`

## 故障排除

### 常见问题

1. **makensis 命令未找到**
   - 确保 NSIS 已正确安装
   - 将 NSIS 安装目录添加到系统 PATH

2. **编译失败**
   - 检查 NSI 文件是否正确生成
   - 查看错误信息，通常会指出具体问题

3. **图标文件未找到**
   - 确保 `app_icon.ico` 文件存在于指定路径
   - 检查路径是否正确

4. **先决条件安装失败**
   - 确保先决条件文件（如 VC_redist.x64.exe）存在
   - 检查文件路径是否正确

### 调试技巧

1. **查看生成的 NSI 文件**
   ```cmd
   type setup.nsi
   ```

2. **详细编译输出**
   ```cmd
   makensis /V4 setup.nsi
   ```

3. **测试安装程序**
   - 在虚拟机中测试安装和卸载
   - 检查注册表项是否正确写入和清理

## 迁移指南

### 从 Inno Setup 迁移到 NSIS

1. **停用旧的构建脚本**
   - 不再使用 `generate_inno_setup.bat`
   - 不再使用 `generate_inno_setup.py`
   - 不再使用 `generate_iss.dart`

2. **使用新的构建脚本**
   - 使用 `generate_nsis.bat` 或 `generate_nsis.py`

3. **配置文件无需修改**
   - `make_config.yaml` 保持不变
   - 所有配置项都兼容

4. **更新 CI/CD 脚本**
   - 将构建命令从 `generate_inno_setup.bat` 改为 `generate_nsis.bat`

### 验证迁移结果

- ✅ 安装包大小应该更小
- ✅ 安装过程应该相同
- ✅ 所有功能应该正常工作
- ✅ 卸载应该完全清理

## 技术细节

### NSIS 脚本结构

生成的 NSI 脚本包含以下主要部分：

1. **头部定义**: 应用信息、版本信息
2. **界面配置**: Modern UI 设置
3. **安装页面**: 欢迎、许可、组件选择等
4. **安装部分**: 主程序、桌面快捷方式、先决条件
5. **卸载部分**: 完整的清理逻辑

### 与 Inno Setup 的对比

| 特性 | Inno Setup | NSIS |
|------|------------|------|
| 包大小 | 较大 | 较小 |
| 脚本语法 | Pascal-like | 自定义语法 |
| 界面定制 | 有限 | 丰富 |
| 大小限制 | 有限制 | 无限制 |
| 学习曲线 | 较简单 | 中等 |
| 社区支持 | 良好 | 优秀 |

## 许可证

NSIS 是开源软件，使用 zlib/libpng 许可证。