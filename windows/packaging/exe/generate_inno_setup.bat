@echo off
echo Starting InnoSetup build process...

:: 设置路径变量
set SCRIPT_DIR=%~dp0
:: 移除路径末尾的反斜杠
IF %SCRIPT_DIR:~-1%==\ SET SCRIPT_DIR=%SCRIPT_DIR:~0,-1%

echo Script directory: %SCRIPT_DIR%
cd %SCRIPT_DIR%

:: 运行Dart脚本生成ISS文件
echo Generating ISS file...
call dart generate_iss.dart
if %ERRORLEVEL% NEQ 0 (
    echo Failed to generate ISS file!
    pause
    exit /b 1
)

:: 检查ISS文件是否存在
echo Checking ISS file: %SCRIPT_DIR%\setup.iss
if not exist "%SCRIPT_DIR%\setup.iss" (
    echo Error: ISS file not found!
    dir "%SCRIPT_DIR%"
    pause
    exit /b 1
)

:: 直接使用iscc命令
echo Compiling installer...
echo Command: iscc "%SCRIPT_DIR%\setup.iss"

:: 使用call命令确保执行外部程序后返回到批处理文件
call iscc "%SCRIPT_DIR%\setup.iss"

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

:: 获取项目根目录 - 使用绝对路径
for %%i in ("%SCRIPT_DIR%\..\..\..") do set PROJECT_ROOT=%%~fi
echo Project root directory: %PROJECT_ROOT%

:: 检查项目根目录是否存在
if not exist "%PROJECT_ROOT%" (
    echo Error: Project root directory not found: %PROJECT_ROOT%
    echo Current directory: %CD%
    echo Script directory: %SCRIPT_DIR%
    pause
    exit /b 1
)

:: 获取版本号
echo Extracting version number from filename...
for /f "tokens=2 delims=-" %%a in ('dir /b "%SCRIPT_DIR%\turing_art-*-windows-setup.exe" 2^>nul') do (
    set APP_VERSION=%%a
    echo Found version: %%a
)

if not defined APP_VERSION (
    echo Warning: Could not extract version from filename, checking setup.iss file...
    for /f "tokens=2 delims==" %%a in ('findstr /C:"AppVersion=" "%SCRIPT_DIR%\setup.iss" 2^>nul') do (
        set APP_VERSION=%%a
        echo Found version in setup.iss: %%a
    )
)

if not defined APP_VERSION (
    echo Error: Could not determine version number
    set APP_VERSION=unknown
    echo Using default version: %APP_VERSION%
)

:: 创建dist文件夹
echo Creating dist directory if it doesn't exist...
if not exist "%PROJECT_ROOT%\dist" (
    md "%PROJECT_ROOT%\dist"
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to create dist directory
        pause
        exit /b 1
    )
    echo Created dist directory: %PROJECT_ROOT%\dist
)

:: 创建版本号文件夹
echo Creating version directory: %APP_VERSION%
if not exist "%PROJECT_ROOT%\dist\%APP_VERSION%" (
    md "%PROJECT_ROOT%\dist\%APP_VERSION%"
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to create version directory
        pause
        exit /b 1
    )
    echo Created version directory: %PROJECT_ROOT%\dist\%APP_VERSION%
)

:: 复制安装程序到版本号文件夹
echo Copying installer to version directory...
set "INSTALLER_FILE=turing_art-*-windows-setup.exe"
dir "%SCRIPT_DIR%\%INSTALLER_FILE%" 2>nul
if %ERRORLEVEL% EQU 0 (
    copy /Y "%SCRIPT_DIR%\%INSTALLER_FILE%" "%PROJECT_ROOT%\dist\%APP_VERSION%\"
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to copy installer to version directory
        exit /b 1
    )
    echo Installer copied to: %PROJECT_ROOT%\dist\%APP_VERSION%
) else (
    echo Warning: Installer file not found
    dir "%SCRIPT_DIR%"
)

:: 删除临时文件
echo Cleaning up temporary files...
if exist "%SCRIPT_DIR%\setup.iss" (
    del /F "%SCRIPT_DIR%\setup.iss"
    echo Deleted: %SCRIPT_DIR%\setup.iss
)

if exist "%SCRIPT_DIR%\turing_art-*-windows-setup.exe" (
    del /F "%SCRIPT_DIR%\turing_art-*-windows-setup.exe"
    echo Deleted installer from exe folder
)

echo Installer compilation completed successfully!
