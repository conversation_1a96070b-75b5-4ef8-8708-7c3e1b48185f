app_id: 5B599538-42B1-4826-A479-AF079F21A65D
publisher: ChengDu <PERSON>aiYun
publisher_url: https://www.tuling.art
display_name: 图灵精修
create_desktop_icon: true
install_dir_name: 图灵精修
setup_icon_file: ../../windows/runner/resources/app_icon.ico
prerequisites:
  - name: Microsoft Visual C++ Redistributable for Visual Studio 2022 x64
    command: ../vcruntime/VC_redist.x64.exe
    args: ["/install", "/quiet", "/norestart"]
include_files:
  - source: "C:/Windows/System32/vcruntime140.dll"
    destination: "../../../build/windows/x64/runner/Release"
  - source: "C:/Windows/System32/vcruntime140_1.dll"
    destination: "../../../build/windows/x64/runner/Release"
  - source: "C:/Windows/System32/msvcp140.dll"
    destination: "../../../build/windows/x64/runner/Release"
build_args:
  dart-define:
    ENV: ${ENV}
locales:
  - zh
