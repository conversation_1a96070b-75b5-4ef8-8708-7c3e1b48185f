import 'dart:convert';
import 'dart:io';

import 'package:yaml/yaml.dart';

/// 生成NSIS安装脚本的主函数
void main() async {
  // 获取脚本所在目录
  final scriptDir = File(Platform.script.toFilePath()).parent.path;
  // 获取项目根目录
  final projectDir = Directory(scriptDir).parent.parent.parent.path;

  print('脚本目录: $scriptDir');
  print('项目根目录: $projectDir');

  // 读取pubspec.yaml获取版本号
  final pubspecFile = File('$projectDir/pubspec.yaml');
  if (!pubspecFile.existsSync()) {
    print('Error: pubspec.yaml not found at ${pubspecFile.path}');
    exit(1);
  }
  final yamlString = await pubspecFile.readAsString();
  final pubspec = loadYaml(yamlString) as Map<dynamic, dynamic>;
  final version = pubspec['version'] as String;
  print('应用版本: $version');

  // 读取make_config.yaml文件
  final configFile = File('$scriptDir/make_config.yaml');
  if (!configFile.existsSync()) {
    print('Error: make_config.yaml not found at ${configFile.path}');
    exit(1);
  }

  // 解析YAML配置
  final configYamlString = await configFile.readAsString();
  final config = loadYaml(configYamlString) as Map<dynamic, dynamic>;

  // 生成NSI文件内容
  final nsiContent = generateNsiContent(config, version, projectDir, 'Release');

  // 写入NSI文件
  final nsiFilePath = '${scriptDir}${Platform.pathSeparator}setup.nsi';
  print('正在创建NSI文件：$nsiFilePath');
  final nsiFile = File(nsiFilePath);
  // Write NSI file with UTF-8 BOM
  final utf8Bom = [0xEF, 0xBB, 0xBF];
  final contentBytes = utf8.encode(nsiContent);
  final finalBytes = utf8Bom + contentBytes;
  await nsiFile.writeAsBytes(finalBytes);
  print('已生成NSI文件: ${nsiFile.path}');

  // 输出成功信息
  print('NSI文件生成成功: ${nsiFile.path}');

  // 输出文件内容用于调试
  print(
      '\nNSI文件内容预览:\n${nsiContent.substring(0, nsiContent.length > 500 ? 500 : nsiContent.length)}...\n');
}

/// 生成NSIS脚本内容
///
/// [config] - 配置文件内容
/// [version] - 应用版本号
/// [projectDir] - 项目根目录
/// [buildType] - 构建类型（Release/Debug）
String generateNsiContent(Map<dynamic, dynamic> config, String version,
    String projectDir, String buildType) {
  final appId = config['app_id'] as String;
  final publisher = config['publisher'] as String;
  final publisherUrl = config['publisher_url'] as String;
  final displayName = config['display_name'] as String;
  final installDirName = config['install_dir_name'] as String;
  final createDesktopIcon = config['create_desktop_icon'] as bool? ?? true;

  // 使用完整路径作为图标路径
  final iconPath = '$projectDir\\windows\\runner\\resources\\app_icon.ico';
  print('图标路径: $iconPath');

  // 根据构建类型选择源目录
  final sourceDir = '$projectDir\\build\\windows\\x64\\runner\\$buildType';

  // 处理先决条件
  final prerequisites = config['prerequisites'] as List<dynamic>? ?? [];
  String prerequisitesSection = '';
  if (prerequisites.isNotEmpty) {
    prerequisitesSection =
        generatePrerequisitesSection(prerequisites, projectDir);
  }

  return '''
; NSIS Installation Script - TuringArt
; Auto-generated, do not modify manually

; Set Unicode encoding
Unicode True

; Include Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"
!include "x64.nsh"

; Variables
Var IsUpgrade
Var SkipDirectoryPage

; Application Information
!define APP_NAME "$displayName"
!define APP_VERSION "$version"
!define APP_PUBLISHER "$publisher"
!define APP_URL "$publisherUrl"
!define APP_EXECUTABLE "turing_art.exe"
!define APP_GUID "$appId"
!define INSTALL_DIR_NAME "$installDirName"

; Installer Settings
Name "\${APP_NAME}"
OutFile "turing_art-\${APP_VERSION}-windows-setup.exe"
InstallDir "\$PROGRAMFILES64\\\${INSTALL_DIR_NAME}"
InstallDirRegKey HKLM "Software\\\${APP_NAME}" "InstallLocation"
RequestExecutionLevel admin

; Version Information
VIProductVersion "$version.0"
VIAddVersionKey "ProductName" "\${APP_NAME}"
VIAddVersionKey "ProductVersion" "\${APP_VERSION}"
VIAddVersionKey "CompanyName" "\${APP_PUBLISHER}"
VIAddVersionKey "LegalCopyright" "© ${DateTime.now().year} \${APP_PUBLISHER}"
VIAddVersionKey "FileDescription" "\${APP_NAME} Installer"
VIAddVersionKey "FileVersion" "\${APP_VERSION}"

; Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "$iconPath"
!define MUI_UNICON "$iconPath"
!define MUI_WELCOMEFINISHPAGE_BITMAP "\${NSISDIR}\\Contrib\\Graphics\\Wizard\\nsis3-metro.bmp"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "\${NSISDIR}\\Contrib\\Graphics\\Wizard\\nsis3-metro.bmp"

; Installation Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "$projectDir\\windows\\packaging\\exe\\license.txt"
!define MUI_PAGE_CUSTOMFUNCTION_PRE DirectoryPagePre
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "\$INSTDIR\\\${APP_EXECUTABLE}"
!define MUI_FINISHPAGE_RUN_TEXT "启动 \${APP_NAME}"
!insertmacro MUI_PAGE_FINISH

; Uninstallation Pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Language
!insertmacro MUI_LANGUAGE "SimpChinese"

; Installation Types
InstType "Full Installation"
InstType "Minimal Installation"

; Main Program Component
Section "!\${APP_NAME} (Required)" SecMain
  SectionIn RO 1 2
  
  ; Set output path
  SetOutPath "\$INSTDIR"
  
  ; Copy files
  File /r "$sourceDir\\*"
  
  ; Write registry
  WriteRegStr HKLM "Software\\\${APP_NAME}" "InstallLocation" "\$INSTDIR"
  WriteRegStr HKLM "Software\\\${APP_NAME}" "Version" "\${APP_VERSION}"
  
  ; Write uninstall information
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "DisplayName" "\${APP_NAME}"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "UninstallString" "\$INSTDIR\\uninstall.exe"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "DisplayIcon" "\$INSTDIR\\\${APP_EXECUTABLE}"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "Publisher" "\${APP_PUBLISHER}"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "URLInfoAbout" "\${APP_URL}"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "DisplayVersion" "\${APP_VERSION}"
  WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "NoRepair" 1
  
  ; Create uninstaller
  WriteUninstaller "\$INSTDIR\\uninstall.exe"
  
  ; Create start menu shortcuts
  CreateDirectory "\$SMPROGRAMS\\\${APP_NAME}"
  CreateShortcut "\$SMPROGRAMS\\\${APP_NAME}\\\${APP_NAME}.lnk" "\$INSTDIR\\\${APP_EXECUTABLE}" "" "\$INSTDIR\\\${APP_EXECUTABLE}" 0
  CreateShortcut "\$SMPROGRAMS\\\${APP_NAME}\\Uninstall \${APP_NAME}.lnk" "\$INSTDIR\\uninstall.exe"
SectionEnd

; Desktop Shortcut Component (Auto-installed)
Section "Desktop Shortcut" SecDesktop
  SectionIn RO 1 2  
  ; Create new desktop shortcut in public desktop
  CreateShortcut "\$COMMONDESKTOP\\\${APP_NAME}.lnk" "\$INSTDIR\\\${APP_EXECUTABLE}" "" "\$INSTDIR\\\${APP_EXECUTABLE}" 0
SectionEnd

$prerequisitesSection



; Installation Functions
Function .onInit
  ; Check if 64-bit system
  \${IfNot} \${RunningX64}
    MessageBox MB_OK|MB_ICONSTOP "This program requires 64-bit Windows system."
    Abort
  \${EndIf}
  
  ; Initialize variables
  StrCpy \$IsUpgrade "0"
  StrCpy \$SkipDirectoryPage "0"
  
  ; Check if application is already installed
  ; First check NSIS installation registry
  ReadRegStr \$R0 HKLM "Software\\\${APP_NAME}" "InstallLocation"
  \${If} \$R0 != ""
    ; NSIS installation found
    StrCpy \$IsUpgrade "1"
    StrCpy \$SkipDirectoryPage "1"
    ; Use existing installation directory
    StrCpy \$INSTDIR \$R0
  \${Else}
    ; Check InnoSetup installation registry
    ReadRegStr \$R1 HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}_is1" "InstallLocation"
    \${If} \$R1 != ""
      ; InnoSetup installation found
      StrCpy \$IsUpgrade "1"
      StrCpy \$SkipDirectoryPage "1"
      ; Use existing installation directory
      StrCpy \$INSTDIR \$R1
    \${EndIf}
  \${EndIf}
FunctionEnd

; Function to control directory page display
Function DirectoryPagePre
  ; Skip directory page if this is an upgrade
  \${If} \$SkipDirectoryPage == "1"
    Abort
  \${EndIf}
FunctionEnd

; Uninstall Section
Section "Uninstall"
  ; Delete files
  RMDir /r "\$INSTDIR"
  
  ; Delete shortcuts (both user and public desktop)
  Delete "\$DESKTOP\\\${APP_NAME}.lnk"
  Delete "\$COMMONDESKTOP\\\${APP_NAME}.lnk"
  RMDir /r "\$SMPROGRAMS\\\${APP_NAME}"
  
  ; Delete registry keys
  DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}"
  DeleteRegKey HKLM "Software\\\${APP_NAME}"
SectionEnd
''';
}

/// 生成先决条件安装部分
///
/// [prerequisites] - 先决条件列表
/// [projectDir] - 项目根目录
String generatePrerequisitesSection(
    List<dynamic> prerequisites, String projectDir) {
  final buffer = StringBuffer();

  for (int i = 0; i < prerequisites.length; i++) {
    final prereq = prerequisites[i] as Map<dynamic, dynamic>;
    final name = prereq['name'] as String;
    final command = prereq['command'] as String;
    final args = prereq['args'] as List<dynamic>? ?? [];

    final sectionName = 'SecPrereq$i';
    final commandPath = '$projectDir\\windows\\packaging\\exe\\$command';
    final argsString = args.join(' ');

    buffer.writeln('; $name');
    buffer.writeln('Section "$name" $sectionName');
    buffer.writeln('  SectionIn 1');
    buffer.writeln('  DetailPrint "Installing $name..."');
    String fullCommand = '\"$commandPath\"';
    if (argsString.isNotEmpty) {
      fullCommand += ' $argsString';
    }
    buffer.writeln('  ExecWait \'$fullCommand\'');
    buffer.writeln('SectionEnd');
    buffer.writeln();
  }

  return buffer.toString();
}
