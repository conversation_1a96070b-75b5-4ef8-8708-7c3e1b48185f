#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InnoSetup构建脚本

这个脚本用于生成和编译InnoSetup安装程序
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, error_message=None):
    """运行命令并检查结果"""
    print(f"执行命令: {command}")
    
    # 设置环境变量强制使用UTF-8
    env = os.environ.copy()
    env["PYTHONIOENCODING"] = "utf-8"
    env["PYTHONUNBUFFERED"] = "1"
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', env=env)
    
    # 输出命令的输出
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    
    # 检查命令执行结果
    if result.returncode != 0:
        if error_message:
            print(f"错误: {error_message}")
        print(f"命令执行失败: {command}, 返回码: {result.returncode}")
        sys.exit(result.returncode)
    return result

def main():
    print("开始InnoSetup构建过程...")
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"脚本目录: {script_dir}")
    
    # 运行Dart脚本生成ISS文件
    print("生成ISS文件...")
    run_command(f"dart {os.path.join(script_dir, 'generate_iss.dart')}", "生成ISS文件失败")
    
    # 检查ISS文件是否存在
    iss_file = os.path.join(script_dir, "setup.iss")
    print(f"检查ISS文件: {iss_file}")
    if not os.path.exists(iss_file):
        print("错误: ISS文件未找到!")
        print(f"目录内容: {os.listdir(script_dir)}")
        sys.exit(1)
    
    # 编译安装程序
    print("编译安装程序...")
    print(f"命令: iscc \"{iss_file}\"")
    run_command(f"iscc \"{iss_file}\"", "编译失败")
    
    # 获取项目根目录
    project_root = os.path.abspath(os.path.join(script_dir, "..", "..", ".."))
    print(f"项目根目录: {project_root}")
    
    if not os.path.exists(project_root):
        print(f"错误: 项目根目录未找到: {project_root}")
        print(f"当前目录: {os.getcwd()}")
        print(f"脚本目录: {script_dir}")
        sys.exit(1)
    
    # 获取版本号
    print("从文件名中提取版本号...")
    installer_files = list(Path(script_dir).glob("turing_art-*-windows-setup.exe"))
    app_version = "unknown"
    
    if installer_files:
        # 从第一个匹配的文件名中提取版本号
        filename = installer_files[0].name
        try:
            app_version = filename.split("-")[1]
            print(f"找到版本: {app_version}")
        except IndexError:
            print("警告: 无法从文件名中提取版本号")
    else:
        print("警告: 未找到安装程序文件，尝试从setup.iss中获取版本号...")
        try:
            with open(iss_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if "AppVersion=" in line:
                        app_version = line.split("=")[1].strip()
                        print(f"从setup.iss中找到版本: {app_version}")
                        break
        except Exception as e:
            print(f"警告: 从setup.iss读取版本号失败: {e}")
    
    # 创建dist文件夹
    print("创建dist目录...")
    dist_dir = os.path.join(project_root, "dist")
    
    # 确保dist文件夹存在
    if not os.path.exists(dist_dir):
        os.makedirs(dist_dir)
    
    # 创建版本号文件夹
    version_dir = os.path.join(dist_dir, app_version)
    print(f"创建版本目录: {app_version}")
    if not os.path.exists(version_dir):
        os.makedirs(version_dir)
    
    # 复制安装程序到版本号文件夹
    # 从脚本目录(script_dir)复制 turing_art-*-windows-setup.exe 安装程序文件
    # 到项目根目录下的 dist/版本号(app_version) 文件夹中
    print("复制安装程序到版本目录...")
    if installer_files:
        for installer_file in installer_files:
            dest_file = os.path.join(version_dir, installer_file.name)
            os.replace(installer_file, dest_file)
            print(f"安装程序已复制: 从 {installer_file} 到 {dest_file}")
    else:
        print("警告: 未找到安装程序文件")
        print(f"目录内容: {os.listdir(script_dir)}")
    
    # 删除临时文件
    print("清理临时文件...")
    if os.path.exists(iss_file):
        os.remove(iss_file)
        print(f"已删除: {iss_file}")
    
    print("安装程序编译完成！")
    return 0

if __name__ == "__main__":
    # 确保输出使用UTF-8编码
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr.reconfigure(encoding='utf-8', errors='replace', line_buffering=True)
    except AttributeError:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)
    
    sys.exit(main())