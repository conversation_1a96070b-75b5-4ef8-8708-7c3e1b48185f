import 'dart:io';

void main() async {
  // DLL 文件列表及其源路径
  final dllFiles = [
    'C:\\Windows\\System32\\vcruntime140.dll',
    'C:\\Windows\\System32\\vcruntime140_1.dll',
    'C:\\Windows\\System32\\msvcp140.dll',
  ];

  // 获取当前目录
  final currentDir = Directory.current.path;
  final targetDir = '$currentDir\\build\\windows\\x64\\runner\\Release';
  // 确保目标目录存在
  await Directory(targetDir).create(recursive: true);

  print('开始复制 DLL 文件...');

  // 复制每个 DLL 文件
  for (final sourcePath in dllFiles) {
    final fileName = sourcePath.split('\\').last;
    final targetPath = '$targetDir\\$fileName';

    try {
      await File(sourcePath).copy(targetPath);
      print('成功复制: $fileName');
    } catch (e) {
      print('复制失败 $fileName: $e');
    }
  }

  print('复制完成！');
}
