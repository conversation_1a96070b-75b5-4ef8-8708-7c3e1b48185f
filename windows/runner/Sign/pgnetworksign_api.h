#ifndef KONAN_PGNETWORKSIGN_H
#define KONAN_PGNETWORKSIGN_H
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
typedef bool            pgnetworksign_KBoolean;
#else
typedef _Bool           pgnetworksign_KBoolean;
#endif
typedef unsigned short     pgnetworksign_KChar;
typedef signed char        pgnetworksign_KByte;
typedef short              pgnetworksign_KShort;
typedef int                pgnetworksign_KInt;
typedef long long          pgnetworksign_KLong;
typedef unsigned char      pgnetworksign_KUByte;
typedef unsigned short     pgnetworksign_KUShort;
typedef unsigned int       pgnetworksign_KUInt;
typedef unsigned long long pgnetworksign_KULong;
typedef float              pgnetworksign_KFloat;
typedef double             pgnetworksign_KDouble;
#ifndef _MSC_VER
typedef float __attribute__ ((__vector_size__ (16))) pgnetworksign_KVector128;
#else
#include <xmmintrin.h>
typedef __m128 pgnetworksign_KVector128;
#endif
typedef void*              pgnetworksign_KNativePtr;
struct pgnetworksign_KType;
typedef struct pgnetworksign_KType pgnetworksign_KType;

typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Byte;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Short;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Int;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Long;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Float;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Double;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Char;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Boolean;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Unit;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_UByte;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_UShort;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_UInt;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_ULong;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_Param;
typedef struct {
  pgnetworksign_KNativePtr pinned;
} pgnetworksign_kref_kotlin_Any;

extern void* addParam(const char* key, const char* value);
extern const char* genSign(const char* host, const char* urlPath, const char* method, void* params, pgnetworksign_KInt count, void* jsonBodyData, pgnetworksign_KInt bytes);

typedef struct {
  /* Service functions. */
  void (*DisposeStablePointer)(pgnetworksign_KNativePtr ptr);
  void (*DisposeString)(const char* string);
  pgnetworksign_KBoolean (*IsInstance)(pgnetworksign_KNativePtr ref, const pgnetworksign_KType* type);
  pgnetworksign_kref_kotlin_Byte (*createNullableByte)(pgnetworksign_KByte);
  pgnetworksign_KByte (*getNonNullValueOfByte)(pgnetworksign_kref_kotlin_Byte);
  pgnetworksign_kref_kotlin_Short (*createNullableShort)(pgnetworksign_KShort);
  pgnetworksign_KShort (*getNonNullValueOfShort)(pgnetworksign_kref_kotlin_Short);
  pgnetworksign_kref_kotlin_Int (*createNullableInt)(pgnetworksign_KInt);
  pgnetworksign_KInt (*getNonNullValueOfInt)(pgnetworksign_kref_kotlin_Int);
  pgnetworksign_kref_kotlin_Long (*createNullableLong)(pgnetworksign_KLong);
  pgnetworksign_KLong (*getNonNullValueOfLong)(pgnetworksign_kref_kotlin_Long);
  pgnetworksign_kref_kotlin_Float (*createNullableFloat)(pgnetworksign_KFloat);
  pgnetworksign_KFloat (*getNonNullValueOfFloat)(pgnetworksign_kref_kotlin_Float);
  pgnetworksign_kref_kotlin_Double (*createNullableDouble)(pgnetworksign_KDouble);
  pgnetworksign_KDouble (*getNonNullValueOfDouble)(pgnetworksign_kref_kotlin_Double);
  pgnetworksign_kref_kotlin_Char (*createNullableChar)(pgnetworksign_KChar);
  pgnetworksign_KChar (*getNonNullValueOfChar)(pgnetworksign_kref_kotlin_Char);
  pgnetworksign_kref_kotlin_Boolean (*createNullableBoolean)(pgnetworksign_KBoolean);
  pgnetworksign_KBoolean (*getNonNullValueOfBoolean)(pgnetworksign_kref_kotlin_Boolean);
  pgnetworksign_kref_kotlin_Unit (*createNullableUnit)(void);
  pgnetworksign_kref_kotlin_UByte (*createNullableUByte)(pgnetworksign_KUByte);
  pgnetworksign_KUByte (*getNonNullValueOfUByte)(pgnetworksign_kref_kotlin_UByte);
  pgnetworksign_kref_kotlin_UShort (*createNullableUShort)(pgnetworksign_KUShort);
  pgnetworksign_KUShort (*getNonNullValueOfUShort)(pgnetworksign_kref_kotlin_UShort);
  pgnetworksign_kref_kotlin_UInt (*createNullableUInt)(pgnetworksign_KUInt);
  pgnetworksign_KUInt (*getNonNullValueOfUInt)(pgnetworksign_kref_kotlin_UInt);
  pgnetworksign_kref_kotlin_ULong (*createNullableULong)(pgnetworksign_KULong);
  pgnetworksign_KULong (*getNonNullValueOfULong)(pgnetworksign_kref_kotlin_ULong);

  /* User functions. */
  struct {
    struct {
      struct {
        pgnetworksign_KType* (*_type)(void);
        pgnetworksign_kref_Param (*Param)(const char* key, const char* value);
        const char* (*get_key)(pgnetworksign_kref_Param thiz);
        const char* (*get_value)(pgnetworksign_kref_Param thiz);
        const char* (*component1)(pgnetworksign_kref_Param thiz);
        const char* (*component2)(pgnetworksign_kref_Param thiz);
        pgnetworksign_kref_Param (*copy)(pgnetworksign_kref_Param thiz, const char* key, const char* value);
        pgnetworksign_KBoolean (*equals)(pgnetworksign_kref_Param thiz, pgnetworksign_kref_kotlin_Any other);
        pgnetworksign_KInt (*hashCode)(pgnetworksign_kref_Param thiz);
        const char* (*toString)(pgnetworksign_kref_Param thiz);
      } Param;
      void (*main)();
      void* (*addParam_)(const char* key, const char* value);
      const char* (*genSign_)(const char* host, const char* urlPath, const char* method, void* params, pgnetworksign_KInt count, void* jsonBodyData, pgnetworksign_KInt bytes);
    } root;
  } kotlin;
} pgnetworksign_ExportedSymbols;
extern pgnetworksign_ExportedSymbols* pgnetworksign_symbols(void);
#ifdef __cplusplus
}  /* extern "C" */
#endif
#endif  /* KONAN_PGNETWORKSIGN_H */
