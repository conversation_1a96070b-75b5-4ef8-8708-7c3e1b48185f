﻿#pragma once

// 定义导出宏
#ifdef UNITY_LAUNCHER_EXPORTS
#define UNITY_LAUNCHER_API __declspec(dllexport)
#else
#define UNITY_LAUNCHER_API __declspec(dllimport)
#endif

// 定义调用约定
#ifndef UNITY_API
#ifdef _WIN32
#define UNITY_API __stdcall
#else
#define UNITY_API
#endif
#endif

// DLL 导出函数声明
extern "C" {
    // 启动Unity应用程序
    // @param unityPath Unity应用程序的路径
    // @return 启动成功返回true，失败返回false
    UNITY_LAUNCHER_API bool LaunchUnity(const wchar_t* unityPath);

    // 注册回调函数，用于接收来自Unity的消息
    // @param callback 回调函数指针，接收一个const char*参数
    UNITY_LAUNCHER_API void RegisterMessageCallback(void(*callback)(const char* message));

    // 发送消息到Unity
    // @param gameObjectName 目标GameObject的名称
    // @param methodName 要调用的方法名
    // @param parameter 要传递的参数（字符串）
    UNITY_LAUNCHER_API void SendMessageToUnity(const char* gameObjectName, const char* methodName, const char* parameter);
}

// 可选：添加版本信息
#define UNITY_LAUNCHER_VERSION_MAJOR 1
#define UNITY_LAUNCHER_VERSION_MINOR 0
#define UNITY_LAUNCHER_VERSION_PATCH 0

// 可选：错误代码定义
enum UnityLauncherError {
    UNITY_LAUNCHER_SUCCESS = 0,
    UNITY_LAUNCHER_ERROR_INVALID_PATH = -1,
    UNITY_LAUNCHER_ERROR_LOAD_FAILED = -2,
    UNITY_LAUNCHER_ERROR_FUNCTION_NOT_FOUND = -3
};