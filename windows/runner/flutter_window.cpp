#include "flutter_window.h"

// 添加Common Controls版本定义
#pragma comment(lib, "Comctl32.lib")
#include <commdlg.h>
#include <flutter/flutter_view_controller.h>
#include <flutter/event_channel.h>
#include <flutter/plugin_registrar.h>
#include <flutter/standard_method_codec.h>
#include <flutter/standard_message_codec.h>
#include <sstream>
#include "UnityInterface.h"
#include <ShellAPI.h>
#include <commctrl.h>
#include "json.hpp"
using json = nlohmann::json;

// Unity窗口过程前向声明
LRESULT CALLBACK UnitySubclassProc(HWND hwnd, UINT message, WPARAM wParam,
                                   LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData);

// 非客戶區鼠標事件處理
LRESULT handle_nchittest(HWND window, WPARAM lparam);

#include "flutter/generated_plugin_registrant.h"

#include <optional>
#include <vector>
#include <string>
#include <fstream>
#include <ctime>
#include <windowsx.h>
#include <thread>
#include <chrono>

// 添加日志函数
void LogToFile(const std::string &message)
{
  // 将信息输出到调试窗口
  std::wstring wMessage(message.begin(), message.end());
  OutputDebugStringW(wMessage.c_str());

  // 继续写入日志文件
  std::ofstream logFile("flutter_app.log", std::ios::app);
  time_t now = time(0);
  char dt[26];
  ctime_s(dt, sizeof(dt), &now);
  logFile << dt << ": " << message << std::endl;
  logFile.close();
}

void UnityLogToFile(const std::string &message)
{
  // 将信息输出到调试窗口
  std::wstring wMessage(message.begin(), message.end());
  OutputDebugStringW(wMessage.c_str());

  // 继续写入日志文件
  std::ofstream logFile("unity_message.log", std::ios::app);
  time_t now = time(0);
  char dt[26];
  ctime_s(dt, sizeof(dt), &now);
  logFile << dt << ": " << message << std::endl;
  logFile.close();
}

// 添加一个函数来获取 DLL 加载错误的详细信息
std::string GetLastErrorAsString()
{
  DWORD error = GetLastError();
  if (error == 0)
  {
    return std::string();
  }

  LPSTR messageBuffer = nullptr;
  size_t size = FormatMessageA(
      FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
      NULL,
      error,
      MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
      (LPSTR)&messageBuffer,
      0,
      NULL);

  std::string message(messageBuffer, size);
  LocalFree(messageBuffer);
  return message;
}

// 添加获取主机名的函数
std::string GetSafeHostname()
{
  WCHAR computerName[MAX_COMPUTERNAME_LENGTH + 1];
  DWORD size = MAX_COMPUTERNAME_LENGTH + 1;

  if (GetComputerNameW(computerName, &size))
  {
    try
    {
      // 使用WideCharToMultiByte进行UTF-16到UTF-8的转换
      int utf8Size = WideCharToMultiByte(CP_UTF8, 0, computerName, -1, NULL, 0, NULL, NULL);
      if (utf8Size > 0)
      {
        std::vector<char> utf8Buffer(utf8Size);
        if (WideCharToMultiByte(CP_UTF8, 0, computerName, -1, utf8Buffer.data(), utf8Size, NULL, NULL) > 0)
        {
          return std::string(utf8Buffer.data());
        }
      }

      LogToFile("Failed to convert hostname to UTF-8");
      return "unknown-host";
    }
    catch (const std::exception &e)
    {
      LogToFile(std::string("Error converting hostname: ") + e.what());
      // 转换失败，返回默认值
      return "unknown-host";
    }
  }
  else
  {
    LogToFile("Failed to get computer name: " + GetLastErrorAsString());
    return "unknown-host";
  }
}

// 注册Unity启动函数
void FlutterWindow::RegisterUnityLauncherPlugin()
{
  wchar_t exePath[MAX_PATH];
  GetModuleFileNameW(nullptr, exePath, MAX_PATH);

  // Get exe directory
  wchar_t *lastSlash = wcsrchr(exePath, L'\\');
  if (lastSlash)
  {
    *(lastSlash + 1) = L'\0';
    wcscat_s(exePath, L"unity_launcher.dll");

    unity_launcher_dll_ = LoadLibraryW(exePath);
    if (unity_launcher_dll_)
    {
      launch_unity_func_ = reinterpret_cast<LaunchUnityFunc>(
          GetProcAddress(unity_launcher_dll_, "LaunchUnity"));
      if (!launch_unity_func_)
      {
        DWORD error = GetLastError();
        wchar_t errorMsg[256];
        swprintf_s(errorMsg, L"Failed to get LaunchUnity function address. Error code: %d\n", error);
        OutputDebugStringW(errorMsg);
      }
    }
    else
    {
      DWORD error = GetLastError();
      wchar_t errorMsg[256];
      swprintf_s(errorMsg, L"unity_launcher_dll_ LoadLibraryW error: %d\n", error);
      OutputDebugStringW(errorMsg);
    }
  }

  auto channel = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(),
      "unity_launcher_channel",
      &flutter::StandardMethodCodec::GetInstance());

  channel->SetMethodCallHandler(
      [this](const auto &call,
             std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
      {
        if (call.method_name() == "launchUnity")
        {
          if (unityHWND)
          {
            // 已经加载过Unity时直接忽略
            result->Success();
            return;
          }

          if (!launch_unity_func_)
          {
            result->Error("UNAVAILABLE", "Unity launcher not initialized?");
            return;
          }

          if (const auto *arguments = std::get_if<flutter::EncodableMap>(call.arguments()))
          {
            // 调用新的 LaunchUnity 接口
            try
            {
              const auto &unityPath = std::get<std::string>(
                  arguments->at(flutter::EncodableValue("unityPath")));
              const auto &logPath = std::get<std::string>(
                  arguments->at(flutter::EncodableValue("logPath")));
              HWND parentHwnd = GetHandle();

              // 正确转换unityPath为宽字符串
              int unityPathSize = MultiByteToWideChar(CP_UTF8, 0, unityPath.c_str(), -1, NULL, 0);
              std::wstring unityPathW(unityPathSize, 0);
              MultiByteToWideChar(CP_UTF8, 0, unityPath.c_str(), -1, &unityPathW[0], unityPathSize);
              // 移除字符串末尾的null终止符
              if (!unityPathW.empty() && unityPathW.back() == 0)
              {
                unityPathW.pop_back();
              }

              // 正确转换logPath为宽字符串
              int logPathSize = MultiByteToWideChar(CP_UTF8, 0, logPath.c_str(), -1, NULL, 0);
              std::wstring logPathW(logPathSize, 0);
              MultiByteToWideChar(CP_UTF8, 0, logPath.c_str(), -1, &logPathW[0], logPathSize);
              // 移除字符串末尾的null终止符
              if (!logPathW.empty() && logPathW.back() == 0)
              {
                logPathW.pop_back();
              }

              // 构建Unity启动参数，包含日志文件路径
              std::wstring additionalArgs = L"--logFile \"" + logPathW + L"\"";
              // // -----以下是将路径写入日志中-----
              // LogToFile("--------Unity Path: " + unityPath);
              // LogToFile("--------Log Path: " + logPath);
              // int size_needed = WideCharToMultiByte(CP_UTF8, 0, additionalArgs.c_str(), -1, NULL, 0, NULL, NULL);
              // std::string additionalArgsStr(size_needed, 0);
              // WideCharToMultiByte(CP_UTF8, 0, additionalArgs.c_str(), -1, &additionalArgsStr[0], size_needed, NULL, NULL);
              // // 移除字符串末尾的null终止符
              // if (!additionalArgsStr.empty() && additionalArgsStr.back() == 0)
              //     additionalArgsStr.pop_back();
              // LogToFile("--------Additional Args: " + additionalArgsStr);
              // -----以上是将路径写入日志中------

              shouldFindUnityWindow = true; // 启用持续搜索

              // 创建一个线程来异步启动Unity, 避免阻塞Flutter启动页面，造成无响应的状态
              std::thread unityLaunchThread([this, unityPathW, parentHwnd, additionalArgs]()
                                            {
                bool success = launch_unity_func_(unityPathW.c_str(), parentHwnd,
                                                additionalArgs.c_str());
                if (success)
                {
                  LogToFile("LaunchUnity succeeded in background thread");
                }
                else
                {
                  LogToFile("LaunchUnity failed in background thread");
                  // 如果启动失败，需要重置标志
                  shouldFindUnityWindow = false;
                } });

              // 分离线程，让它在后台运行
              unityLaunchThread.detach();

              // 立即返回成功，不等待Unity启动完成
              LogToFile("Unity launch thread started");
              result->Success(flutter::EncodableValue(true));
            }
            catch (const std::exception &e)
            {
              LogToFile(std::string("Exception during LaunchUnity: ") + e.what());
              result->Error("LAUNCH_UNITY_ERROR", e.what());
            }
            catch (...)
            {
              LogToFile("Unknown exception during LaunchUnity");
              result->Error("LAUNCH_UNITY_ERROR", "Unknown error occurred");
            }
          }
          else
          {
            result->Error("INVALID_ARGUMENT", "Unity path must be a string");
          }
        }
        else if (call.method_name() == "setUnityVisibility")
        {
          if (!unityHWND)
          {
            result->Error("UNAVAILABLE", "Unity window not found");
            return;
          }

          if (const auto *arguments = std::get_if<bool>(call.arguments()))
          {
            unityVisible = *arguments;
            RECT parentRect;
            GetClientRect(GetHandle(), &parentRect);

            if (unityVisible)
            {
              const int BORDER_MARGIN = 2; // 留出2像素的边距用于边框检测

              // 计算Unity窗口的位置和尺寸
              int unityX = BORDER_MARGIN;
              int unityY = ConvertDpToPixel(unityTopMargin);                                 // 顶部额外留出Unity边距
              int unityWidth = parentRect.right - parentRect.left - (BORDER_MARGIN * 2);     // 左右各留出边距
              int unityHeight = parentRect.bottom - parentRect.top - unityY - BORDER_MARGIN; // 底部留出边距

              // 设置Unity窗口位置和尺寸
              SetWindowPos(unityHWND, HWND_TOP,
                           unityX,
                           unityY,
                           unityWidth,
                           unityHeight,
                           SWP_SHOWWINDOW);
              // Enable drag and drop for the window
              DragAcceptFiles(GetHandle(), TRUE);
              LogToFile("try Set Unity window subclass to intercept messages 1");

              // 先尝试移除可能存在的子类化
              BOOL subclassResult = RemoveWindowSubclass(GetHandle(), UnitySubclassProc, 1);
              LogToFile("try Set Unity window subclass to intercept messages 2");
              LogToFile("RemoveWindowSubclass返回值: " + std::to_string(subclassResult));

              // Set Unity window subclass to intercept messages
              subclassResult = SetWindowSubclass(GetHandle(), UnitySubclassProc, 1, (DWORD_PTR)this);
              LogToFile("try Set Unity window subclass to intercept messages 3");
              LogToFile("SetWindowSubclass返回值: " + std::to_string(subclassResult));

              // 记录激活序列
              LogToFile("开始完整的Unity窗口激活序列");

              // 小延迟确保窗口完全显示
              Sleep(100);

              // 执行完整的窗口激活序列
              // 1. 发送激活消息
              SendMessage(unityHWND, WM_ACTIVATE, WA_ACTIVE, 0);
              // 2. 发送焦点消息
              SendMessage(unityHWND, WM_SETFOCUS, 0, 0);

              // 3. 强制重绘Unity窗口
              InvalidateRect(unityHWND, NULL, TRUE);
              UpdateWindow(unityHWND);

              // 4. 最后设置焦点
              LogToFile("设置Unity窗口焦点");
              SetForegroundWindow(unityHWND);
              SetFocus(unityHWND);
            }
            else
            {
              // 当不可见时，移动到父窗口外部（使用父窗口高度的2倍负值）
              SetWindowPos(unityHWND, HWND_TOP,
                           0,
                           -parentRect.bottom * 2, // 使用2倍负值确保完全在可视区域外
                           parentRect.right - parentRect.left,
                           parentRect.bottom - parentRect.top,
                           SWP_NOACTIVATE);

              // Unity窗口不可见，将焦点设回Flutter窗口
              LogToFile("Unity窗口不可见，将焦点设回Flutter窗口");
              HWND flutterHwnd = GetHandle();
              Sleep(50); // add brief delay
              // 禁用拖放
              DragAcceptFiles(flutterHwnd, FALSE);
              // 尝试移除可能存在的子类化
              BOOL subclassResult = RemoveWindowSubclass(flutterHwnd, UnitySubclassProc, 1);
              LogToFile("RemoveWindowSubclass返回值: " + std::to_string(subclassResult));
              SetForegroundWindow(flutterHwnd);
              SetFocus(flutterHwnd);
              // force Flutter window to redraw and activate
              InvalidateRect(flutterHwnd, NULL, TRUE);
              UpdateWindow(flutterHwnd);
              // 发送WM_ACTIVATE消息激活窗口
              SendMessage(flutterHwnd, WM_ACTIVATE, WA_ACTIVE, 0);
              // 发送WM_SETFOCUS消息
              SendMessage(flutterHwnd, WM_SETFOCUS, 0, 0);
            }
            result->Success();
          }
          else
          {
            result->Error("INVALID_ARGUMENT", "Argument must be a boolean");
          }
        }
        else if (call.method_name() == "getUnityVisibility")
        {
          // 返回当前Unity窗口的可见性状态
          result->Success(flutter::EncodableValue(unityVisible));
        }
        else if (call.method_name() == "excludeUnityWindowRegion")
        {
          if (!unityHWND)
          {
            result->Error("UNAVAILABLE", "Unity window not found");
            return;
          }

          if (const auto *arguments = std::get_if<flutter::EncodableMap>(call.arguments()))
          {
            try
            {
              // 获取区域标签参数
              std::string tag = "default";
              if (arguments->count(flutter::EncodableValue("tag")) > 0)
              {
                tag = std::get<std::string>(arguments->at(flutter::EncodableValue("tag")));
              }

              // 区域映射，用于存储各个区域
              static std::map<std::string, HRGN> regionMap;

              // 是否重置
              bool reset = std::get<bool>(arguments->at(flutter::EncodableValue("reset")));
              if (reset)
              {
                // 仅重置特定标签的区域
                if (regionMap.count(tag) > 0)
                {
                  // 删除该标签的区域对象
                  DeleteObject(regionMap[tag]);
                  regionMap.erase(tag);
                  LogToFile("Removed region with tag: " + tag);

                  // 重新计算并应用窗口区域
                  if (regionMap.empty())
                  {
                    // 如果没有区域了，恢复完整窗口
                    SetWindowRgn(unityHWND, NULL, TRUE);
                    LogToFile("Reset to full window region");
                  }
                  else
                  {
                    // 合并剩余的所有区域
                    RECT unityRect;
                    GetClientRect(unityHWND, &unityRect);
                    int unityWidth = unityRect.right - unityRect.left;
                    int unityHeight = unityRect.bottom - unityRect.top;

                    // 创建完整窗口区域
                    HRGN fullRegion = CreateRectRgn(0, 0, unityWidth, unityHeight);
                    // 创建最终区域
                    HRGN finalRegion = CreateRectRgn(0, 0, unityWidth, unityHeight);

                    // 从完整区域中减去每个保存的区域
                    for (const auto &[regionTag, hrgn] : regionMap)
                    {
                      // 获取临时区域用于合并操作
                      HRGN tempRegion = CreateRectRgn(0, 0, 0, 0);
                      CombineRgn(tempRegion, finalRegion, hrgn, RGN_DIFF);

                      // 更新最终区域
                      DeleteObject(finalRegion);
                      finalRegion = tempRegion;
                    }

                    // 应用最终区域
                    SetWindowRgn(unityHWND, finalRegion, TRUE);
                    // 不要删除finalRegion，因为SetWindowRgn接管了它的所有权
                    DeleteObject(fullRegion);
                    LogToFile("Applied combined region with remaining regions: " + std::to_string(regionMap.size()));
                  }
                }
                result->Success();
                return;
              }

              // 获取区域参数
              int x = std::get<int>(arguments->at(flutter::EncodableValue("x")));
              int y = std::get<int>(arguments->at(flutter::EncodableValue("y")));
              int width = std::get<int>(arguments->at(flutter::EncodableValue("width")));
              int height = std::get<int>(arguments->at(flutter::EncodableValue("height")));
              int roundRadius = std::get<int>(arguments->at(flutter::EncodableValue("roundRadius")));
              bool center = std::get<bool>(arguments->at(flutter::EncodableValue("center")));
              int unityTopMarginPixel = ConvertDpToPixel(unityTopMargin);

              // 获取Unity窗口的尺寸
              RECT unityRect;
              GetClientRect(unityHWND, &unityRect);
              int unityWidth = unityRect.right - unityRect.left;
              int unityHeight = unityRect.bottom - unityRect.top;

              // 创建一个复杂区域，包含整个窗口但排除对话框区域
              HRGN excludeRegion;
              if (center)
              {
                int startX = (unityWidth - width) / 2;
                int startY = (unityHeight - height - unityTopMarginPixel) / 2;
                excludeRegion = CreateRoundRectRgn(startX, startY, startX + width, startY + height, roundRadius * 2, roundRadius * 2);
              }
              else
              {
                int startY = y - unityTopMarginPixel;
                excludeRegion = CreateRoundRectRgn(x, startY, x + width, startY + height, roundRadius * 2, roundRadius * 2);
              }

              // 保存当前区域到映射中，如果已存在该标签则先删除旧区域
              if (regionMap.count(tag) > 0)
              {
                DeleteObject(regionMap[tag]);
              }
              regionMap[tag] = excludeRegion;
              LogToFile("Added/Updated region with tag: " + tag);

              // 创建完整窗口区域
              HRGN fullRegion = CreateRectRgn(0, 0, unityWidth, unityHeight);
              // 创建最终区域
              HRGN finalRegion = CreateRectRgn(0, 0, unityWidth, unityHeight);

              // 从完整区域中减去每个保存的区域
              for (const auto &[regionTag, hrgn] : regionMap)
              {
                // 获取临时区域用于合并操作
                HRGN tempRegion = CreateRectRgn(0, 0, 0, 0);
                CombineRgn(tempRegion, finalRegion, hrgn, RGN_DIFF);

                // 更新最终区域
                DeleteObject(finalRegion);
                finalRegion = tempRegion;
              }

              // 设置窗口区域
              SetWindowRgn(unityHWND, finalRegion, TRUE);
              // 不要删除finalRegion，因为SetWindowRgn接管了它的所有权
              DeleteObject(fullRegion);
              LogToFile("Applied combined region with total regions: " + std::to_string(regionMap.size()));

              result->Success();
            }
            catch (const std::exception &e)
            {
              result->Error("ARGUMENT_ERROR", e.what());
            }
          }
          else
          {
            result->Error("INVALID_ARGUMENT", "Arguments must be a map");
          }
        }
        else if (call.method_name() == "resetUnityWindowRegion")
        {
          if (!unityHWND)
          {
            result->Error("UNAVAILABLE", "Unity window not found");
            return;
          }

          // 重置窗口区域（恢复完整窗口）
          static std::map<std::string, HRGN> regionMap;

          // 清空所有区域
          for (const auto &[tag, hrgn] : regionMap)
          {
            DeleteObject(hrgn);
          }
          regionMap.clear();
          LogToFile("Cleared all regions");

          SetWindowRgn(unityHWND, NULL, TRUE);
          LogToFile("Reset to full window region");
          result->Success();
        }
        else if (call.method_name() == "setUnityWindowTransparency")
        {
          if (!unityHWND)
          {
            result->Error("UNAVAILABLE", "Unity window not found");
            return;
          }

          if (const auto *arguments = std::get_if<flutter::EncodableMap>(call.arguments()))
          {
            try
            {
              // 获取透明度参数 (0-255，0为完全透明，255为完全不透明)
              int alphaInt = std::get<int>(arguments->at(flutter::EncodableValue("alpha")));

              // 确保alpha值在有效范围内并转换为BYTE类型
              BYTE alpha = (alphaInt < 0) ? 0 : ((alphaInt > 255) ? 255 : static_cast<BYTE>(alphaInt));

              // 获取当前窗口样式
              LONG style = GetWindowLong(unityHWND, GWL_EXSTYLE);

              // 添加分层窗口样式
              SetWindowLong(unityHWND, GWL_EXSTYLE, style | WS_EX_LAYERED);

              // 设置整个窗口的透明度
              SetLayeredWindowAttributes(unityHWND, 0, alpha, LWA_ALPHA);

              result->Success();
            }
            catch (const std::exception &e)
            {
              result->Error("ARGUMENT_ERROR", e.what());
            }
          }
          else
          {
            result->Error("INVALID_ARGUMENT", "Arguments must be a map");
          }
        }
        else if (call.method_name() == "setUnityWindowToBottom")
        {
          if (!unityHWND)
          {
            result->Error("UNAVAILABLE", "Unity window not found");
            return;
          }

          if (const auto *arguments = std::get_if<bool>(call.arguments()))
          {
            bool toBottom = *arguments;
            HWND insertAfter = toBottom ? HWND_BOTTOM : HWND_TOP;

            RECT parentRect;
            GetClientRect(GetHandle(), &parentRect);
            int topMargin = unityVisible ? ConvertDpToPixel(unityTopMargin) : -parentRect.bottom;

            // 设置窗口Z顺序
            if (SetWindowPos(
                    unityHWND,
                    insertAfter,
                    0,
                    topMargin,
                    parentRect.right - parentRect.left,
                    parentRect.bottom - parentRect.top - (unityVisible ? topMargin : 0),
                    SWP_NOACTIVATE))
            {
              result->Success();
            }
            else
            {
              std::string errorMsg = "Failed to set window Z-order: " + GetLastErrorAsString();
              result->Error("WINDOW_ERROR", errorMsg);
            }
          }
          else
          {
            result->Error("INVALID_ARGUMENT", "Argument must be a boolean");
          }
        }
        else if (call.method_name() == "setFocusToFlutterWindowWhenUnityVisible")
        {
          LogToFile("setFocusToFlutterWindowWhenUnityVisible");

          // 调用setFocusToFlutterWindowWhenUnityVisible时，设置标志，防止自动转移焦点
          if (const auto *arguments = std::get_if<bool>(call.arguments()))
          {
            preventAutoFocusToUnity = *arguments;
          }
          if (preventAutoFocusToUnity)
          {
            LogToFile("setFocusToFlutterWindowWhenUnityVisible preventAutoFocusToUnity true");
            // 获取Flutter窗口句柄
            HWND flutterHwnd = GetHandle();

            // 设置焦点到Flutter窗口
            SetForegroundWindow(flutterHwnd);
            SetFocus(flutterHwnd);

            // 强制重绘和激活
            InvalidateRect(flutterHwnd, NULL, TRUE);
            UpdateWindow(flutterHwnd);
            SendMessage(flutterHwnd, WM_ACTIVATE, WA_ACTIVE, 0);
            SendMessage(flutterHwnd, WM_SETFOCUS, 0, 0);
          }
          result->Success(flutter::EncodableValue(true));
        }
        else
        {
          result->NotImplemented();
        }
      });
}

FlutterWindow *FlutterWindow::instance_ = nullptr;

FlutterWindow::FlutterWindow(const flutter::DartProject &project)
    : project_(project)
{
  instance_ = this; // 在构造函数中设置实例指针
}

FlutterWindow::~FlutterWindow()
{
  instance_ = nullptr; // 在析构函数中清除实例指针
  if (unity_message_dll_)
  {
    FreeLibrary(unity_message_dll_);
    unity_message_dll_ = nullptr;
  }
  DllManager::Cleanup();
}

bool FlutterWindow::OnCreate()
{
  if (!Win32Window::OnCreate())
  {
    return false;
  }

  // 初始化Common Controls
  INITCOMMONCONTROLSEX icex;
  icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
  icex.dwICC = ICC_WIN95_CLASSES;
  InitCommonControlsEx(&icex);

  LogToFile("FlutterWindow::OnCreate()");
  RECT frame = GetClientArea();

  // The size here must match the window dimensions to avoid unnecessary surface
  // creation / destruction in the startup path.
  flutter_controller_ = std::make_unique<flutter::FlutterViewController>(
      frame.right - frame.left, frame.bottom - frame.top, project_);
  // Ensure that basic setup of the controller was successful.
  if (!flutter_controller_->engine() || !flutter_controller_->view())
  {
    return false;
  }
  LogToFile("RegisterPlugins and Unity Method");
  RegisterPlugins(flutter_controller_->engine());
  RegisterUnityLauncherPlugin();
  RegisterMethodChannels();
  RegisterFocusDebugChannel(); // 添加焦点调试通道
  RegisterUnityMessageSender(); // 添加这一行
  SetChildContent(flutter_controller_->view()->GetNativeWindow());

  flutter_controller_->engine()->SetNextFrameCallback([&]()
                                                      { this->Show(); });

  // Flutter can complete the first frame before the "show window" callback is
  // registered. The following call ensures a frame is pending to ensure the
  // window is shown. It is a no-op if the first frame hasn't completed yet.
  flutter_controller_->ForceRedraw();

  LogToFile("FlutterWindow::OnCreate() end");
  return true;
}

// Unity消息处理回调
void FlutterWindow::HandleUnityMessage(const char *message)
{
  if (instance_ && instance_->unity_message_channel_)
  {
    // 使用 PostMessage 将消息发送到主线程
    PostMessage(instance_->GetHandle(), WM_APP + 2, 0, (LPARAM)_strdup(message));
  }
}

void FlutterWindow::RegisterUnityMessageSender()
{
  // 加载 Unity 消息 DLL
  wchar_t exePath[MAX_PATH];
  GetModuleFileNameW(nullptr, exePath, MAX_PATH);
  std::wstring dllPath = std::wstring(exePath);
  dllPath = dllPath.substr(0, dllPath.find_last_of(L"\\")) + L"\\turing_art_Data\\Plugins\\x86_64\\UnityMessageDLL.dll";
  // dllPath = dllPath.substr(0, dllPath.find_last_of(L"\\")) + L"\\UnityMessageDLL.dll";

  char mbDllPath[MAX_PATH];
  WideCharToMultiByte(CP_UTF8, 0, dllPath.c_str(), -1, mbDllPath, MAX_PATH, nullptr, nullptr);

  std::ostringstream oss;
  oss << "try load unity func " << mbDllPath;
  LogToFile(oss.str().c_str());

  unity_message_dll_ = LoadLibraryW(dllPath.c_str());
  if (unity_message_dll_)
  {
    call_unity_func_ = reinterpret_cast<CallUnityFunc>(
        GetProcAddress(unity_message_dll_, "CallUnity"));
  }

  if (!call_unity_func_)
  {
    LogToFile("Failed to load Unity message functions");
    return;
  }

  // 创建与Dart通信的channel
  unity_message_channel_ = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(),
      "unity_message_handler",
      &flutter::StandardMethodCodec::GetInstance());

  // 修改函数指针转换
  typedef void (*RegisterHandlerFunc)(void (*)(const char *));

  auto register_handler = reinterpret_cast<RegisterHandlerFunc>(
      GetProcAddress(unity_message_dll_, "RegisterUnityMessageHandler"));

  if (register_handler)
  {
    register_handler(&FlutterWindow::HandleUnityMessage);
  }
  else
  {
    LogToFile("register_handler not found");
  }

  auto channel = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(),
      "flutter_unity_message_channel",
      &flutter::StandardMethodCodec::GetInstance());

  channel->SetMethodCallHandler(
      [this](const flutter::MethodCall<flutter::EncodableValue> &call,
             std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
      {
        if (call.method_name() == "sendMessage")
        {
          try
          {
            const auto *arguments = std::get_if<flutter::EncodableMap>(call.arguments());
            if (!arguments)
            {
              result->Error("INVALID_ARGUMENTS", "Arguments must be a map");
              return;
            }

            const auto &message = std::get<std::string>(arguments->at(flutter::EncodableValue("message")));

            if (call_unity_func_)
            {
              call_unity_func_(message.c_str());
              UnityLogToFile(message);
              result->Success();
            }
            else
            {
              result->Error("UNITY_CALL_ERROR", "Unity message function not available");
            }
          }
          catch (const std::exception &e)
          {
            result->Error("UNITY_CALL_ERROR", e.what());
          }
        }
        else
        {
          result->NotImplemented();
        }
      });
}

void FlutterWindow::RegisterMethodChannels()
{
  // 注册Unity消息发送通道
  unity_message_channel_ =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          flutter_controller_->engine()->messenger(),
          "unity_message_channel",
          &flutter::StandardMethodCodec::GetInstance());

  // 注册主机名获取通道
  auto hostname_channel =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          flutter_controller_->engine()->messenger(),
          "com.turingart.hostname",
          &flutter::StandardMethodCodec::GetInstance());

  hostname_channel->SetMethodCallHandler(
      [](const auto &call,
         std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
      {
        if (call.method_name() == "getHostname")
        {
          try
          {
            std::string hostname = GetSafeHostname();
            LogToFile("Successfully got hostname: " + hostname);
            result->Success(flutter::EncodableValue(hostname));
          }
          catch (const std::exception &e)
          {
            LogToFile("Exception in getHostname: " + std::string(e.what()));
            result->Error("HOSTNAME_ERROR", e.what());
          }
          catch (...)
          {
            LogToFile("Unknown exception in getHostname");
            result->Error("HOSTNAME_ERROR", "Unknown error occurred");
          }
        }
        else
        {
          result->NotImplemented();
        }
      });

  // 注册Unity消息处理通道
  auto unity_message_handler_channel =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          flutter_controller_->engine()->messenger(),
          "unity_message_handler",
          &flutter::StandardMethodCodec::GetInstance());

  unity_message_handler_channel->SetMethodCallHandler(
      [this](const auto &call,
             std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
      {
        if (call.method_name() == "onUnityMessage")
        {
          if (const auto *arguments = std::get_if<std::string>(call.arguments()))
          {
            // 处理从Unity接收到的消息
            HandleUnityMessage(arguments->c_str());
            result->Success();
          }
          else
          {
            result->Error("INVALID_ARGUMENT", "Unity message must be a string");
          }
        }
        else if (call.method_name() == "setFocusToUnityWindow")
        {
          if (!unityHWND)
          {
            result->Error("UNAVAILABLE", "Unity window not found");
            return;
          }

          // Set focus to Unity window
          BOOL success = SetForegroundWindow(unityHWND);
          if (!success)
          {
            LogToFile("set focus to unity window failed: " + GetLastErrorAsString());
          }
          else
          {
            LogToFile("set focus to unity window success");
          }

          result->Success(flutter::EncodableValue(success));
        }
        else
        {
          result->NotImplemented();
        }
      });
  // 在应用启动时初始化 DLL
  if (!DllManager::Initialize())
  {
    LogToFile("Failed to initialize DLL");
    // 处理初始化失败
  }

  auto channel = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(),
      "com.turingart.pgsign",
      &flutter::StandardMethodCodec::GetInstance());

  channel->SetMethodCallHandler(
      [](const flutter::MethodCall<flutter::EncodableValue> &call,
         std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
      {
        if (call.method_name() == "getSign")
        {
          try
          {
            LogToFile("Starting getSign method call");

            auto addParam = DllManager::GetAddParamFunc();
            auto genSign = DllManager::GetGenSignFunc();

            // 使用 Dependency Walker 检查 DLL 依赖
            HMODULE kernel32 = GetModuleHandleW(L"kernel32.dll");
            if (kernel32 == NULL)
            {
              LogToFile("Failed to get kernel32.dll handle");
            }

            wchar_t fullPath[MAX_PATH];
            GetModuleFileNameW(NULL, fullPath, MAX_PATH);
            std::wstring wDllPath = std::wstring(fullPath);
            wDllPath = wDllPath.substr(0, wDllPath.find_last_of(L"\\")) + L"\\pgnetworksign.dll";

            // 记录尝试加载的 DLL 路径
            char mbDllPath[MAX_PATH];
            size_t convertedChars = 0;
            wcstombs_s(&convertedChars, mbDllPath, sizeof(mbDllPath), wDllPath.c_str(), _TRUNCATE);
            LogToFile("Attempting to load DLL from: " + std::string(mbDllPath));

            // 检查文件是否存在
            DWORD fileAttrs = GetFileAttributesW(wDllPath.c_str());
            if (fileAttrs == INVALID_FILE_ATTRIBUTES)
            {
              LogToFile("DLL file does not exist at the specified path");
            }

            HMODULE hDll = LoadLibraryW(wDllPath.c_str());
            if (hDll == NULL)
            {
              std::string errorDetails = GetLastErrorAsString();
              LogToFile("Detailed error: " + errorDetails);

              // 使用 ANSI 版本的 FormatMessage
              LPSTR lpMsgBuf = nullptr;
              DWORD dw = GetLastError();
              FormatMessageA(
                  FORMAT_MESSAGE_ALLOCATE_BUFFER |
                      FORMAT_MESSAGE_FROM_SYSTEM |
                      FORMAT_MESSAGE_IGNORE_INSERTS,
                  NULL,
                  dw,
                  MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                  (LPSTR)&lpMsgBuf,
                  0,
                  NULL);

              std::string errorMessage(lpMsgBuf);
              LocalFree(lpMsgBuf);

              LogToFile("System error message: " + errorMessage);
              result->Error("DLL_LOAD_ERROR", "Failed to load DLL: " + errorMessage);
              return;
            }

            LogToFile("DLL loaded successfully");

            try
            {
              // 在处理参数前记录日志
              LogToFile("Processing arguments");

              const auto *arguments = std::get_if<flutter::EncodableMap>(call.arguments());
              if (!arguments)
              {
                result->Error("INVALID_ARGUMENTS", "Arguments must be a map");
                return;
              }

              // 获取必要参数
              std::string host = std::get<std::string>(arguments->at(flutter::EncodableValue("host")));
              std::string path = std::get<std::string>(arguments->at(flutter::EncodableValue("path")));
              std::string method = std::get<std::string>(arguments->at(flutter::EncodableValue("method")));
              const auto &headers = std::get<flutter::EncodableMap>(arguments->at(flutter::EncodableValue("headers")));
              const auto &params = std::get<flutter::EncodableMap>(arguments->at(flutter::EncodableValue("params")));
              bool forBody = std::get<bool>(arguments->at(flutter::EncodableValue("forBody")));

              // 计算总参数数量并创建参数数组
              const size_t headerCount = headers.size();
              const size_t paramCount = params.size();
              const size_t totalCount = forBody ? headerCount : (headerCount + paramCount);

              if (totalCount > INT_MAX)
              {
                result->Error("PARAM_COUNT_ERROR", "Too many parameters");
                return;
              }

              std::vector<void *> paramArray;
              paramArray.reserve(totalCount);
              int currentCount = 0;

              // 添加 headers 到参数数组
              for (const auto &header : headers)
              {
                const std::string &headerKey = std::get<std::string>(header.first);
                const std::string &headerValue = std::get<std::string>(header.second);
                void *paramPtr = addParam(headerKey.c_str(), headerValue.c_str());
                paramArray.push_back(paramPtr);
                currentCount++;
              }

              // 处理 JSON body 数据
              std::string jsonStr;
              void *jsonBodyPtr = nullptr;
              int jsonBodySize = 0;

              if (forBody)
              {
                try
                {
                  LogToFile("Creating JSON object");
                  json jsonObj;

                  for (const auto &[key, value] : params)
                  {
                    const auto &paramKey = std::get<std::string>(key);
                    LogToFile("Processing param: " + paramKey);

                    if (const auto *strValue = std::get_if<std::string>(&value))
                    {
                      jsonObj[paramKey] = *strValue;
                    }
                    else if (const auto *intValue = std::get_if<int32_t>(&value))
                    {
                      jsonObj[paramKey] = *intValue;
                    }
                    else
                    {
                      LogToFile("Unsupported value type for key: " + paramKey);
                    }
                  }

                  jsonStr = jsonObj.dump();
                  LogToFile("JSON string generated: " + jsonStr);
                }
                catch (const json::exception &e)
                {
                  LogToFile("JSON error: " + std::string(e.what()));
                  result->Error("JSON_ERROR", e.what());
                  return;
                }
                OutputDebugStringA(jsonStr.c_str());
                OutputDebugStringA("\n");

                jsonBodyPtr = (void *)jsonStr.c_str();
                jsonBodySize = static_cast<int>(jsonStr.length());
              }
              else
              {
                // 处理普通参数
                for (const auto &param : params)
                {
                  const std::string &paramKey = std::get<std::string>(param.first);
                  const std::string &paramValue = std::get<std::string>(param.second);
                  void *paramPtr = addParam(paramKey.c_str(), paramValue.c_str());
                  paramArray.push_back(paramPtr);
                  currentCount++;
                }
              }

              // 在函数调用前后添加异常处理
              try
              {
                char *signResult = genSign(
                    host.c_str(),
                    path.c_str(),
                    method.c_str(),
                    paramArray.data(),
                    static_cast<int>(paramArray.size()),
                    jsonBodyPtr,
                    jsonBodySize);

                if (signResult)
                {
                  std::string resultStr(signResult);
                  LogToFile("Sign result: " + resultStr);
                  result->Success(flutter::EncodableValue(resultStr));
                }
                else
                {
                  LogToFile("genSign returned nullptr");
                  result->Error("SIGN_ERROR", "Failed to generate sign");
                }
              }
              catch (const std::exception &e)
              {
                LogToFile("Exception in genSign: " + std::string(e.what()));
                result->Error("SIGN_ERROR", e.what());
              }
              catch (...)
              {
                LogToFile("Unknown exception in genSign");
                result->Error("SIGN_ERROR", "Unknown error occurred");
              }

              FreeLibrary(hDll);
              LogToFile("DLL unloaded");
            }
            catch (const std::exception &e)
            {
              LogToFile("Top level exception: " + std::string(e.what()));
              result->Error("RUNTIME_ERROR", e.what());
            }
            catch (...)
            {
              LogToFile("Unknown top level exception");
              result->Error("RUNTIME_ERROR", "Unknown error occurred");
            }
          }
          catch (const std::exception &e)
          {
            LogToFile("Top level exception: " + std::string(e.what()));
            result->Error("RUNTIME_ERROR", e.what());
          }
          catch (...)
          {
            LogToFile("Unknown top level exception");
            result->Error("RUNTIME_ERROR", "Unknown error occurred");
          }
        }
        else
        {
          result->NotImplemented();
        }
      });

  // 添加接收外部消息的通道
  external_message_channel_ =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          flutter_controller_->engine()->messenger(),
          "com.turingart.external_message",
          &flutter::StandardMethodCodec::GetInstance());
}

int FlutterWindow::ConvertDpToPixel(int dp)
{
  // 使用更兼容的方式获取DPI
  UINT dpi = 96;
  HDC hdc = GetDC(GetHandle());
  if (hdc)
  {
    dpi = GetDeviceCaps(hdc, LOGPIXELSY);
    ReleaseDC(GetHandle(), hdc);
  }
  return static_cast<int>(dp * (static_cast<float>(dpi) / 96.0f));
}

// 在FlutterWindow类中添加HandleDropFiles方法
void FlutterWindow::HandleDropFiles(WPARAM wParam)
{
  HDROP hDrop = (HDROP)wParam;

  // 获取拖拽文件数量
  UINT fileCount = DragQueryFile(hDrop, 0xFFFFFFFF, NULL, 0);
  if (fileCount == 0)
  {
    DragFinish(hDrop);
    return;
  }

  // 创建文件路径列表
  std::vector<std::string> filePaths;
  for (UINT i = 0; i < fileCount; i++)
  {
    // 获取文件名长度
    UINT pathLength = DragQueryFile(hDrop, i, NULL, 0);
    if (pathLength > 0)
    {
      // 分配缓冲区
      std::vector<TCHAR> pathBuffer(pathLength + 1);
      // 获取文件路径
      DragQueryFile(hDrop, i, pathBuffer.data(), pathLength + 1);
      // 转换为UTF-8字符串
      int utf8Length = WideCharToMultiByte(CP_UTF8, 0, (LPCWCH)pathBuffer.data(), -1, NULL, 0, NULL, NULL);
      if (utf8Length > 0)
      {
        std::vector<char> utf8Buffer(utf8Length);
        WideCharToMultiByte(CP_UTF8, 0, (LPCWCH)pathBuffer.data(), -1, utf8Buffer.data(), utf8Length, NULL, NULL);

        std::string filePath = utf8Buffer.data();
        // 记录接收到的文件或文件夹路径
        LogToFile("收到拖拽文件或文件夹: " + filePath);
        filePaths.push_back(filePath);
      }
    }
  }

  // 释放拖放句柄
  DragFinish(hDrop);

  // 将文件路径列表通过方法通道发送到Flutter（文件夹的处理交给Flutter层）
  if (!filePaths.empty() && flutter_controller_)
  {
    // 直接创建一个EncodableList而不是尝试从EncodableValue中获取
    flutter::EncodableList pathList;
    for (const auto &path : filePaths)
    {
      pathList.push_back(flutter::EncodableValue(path));
    }

    // 使用pathList创建EncodableValue
    flutter::EncodableValue args(pathList);

    // 使用标准消息编码器来编码和发送消息
    const flutter::StandardMessageCodec &codec = flutter::StandardMessageCodec::GetInstance();
    std::unique_ptr<std::vector<uint8_t>> message = codec.EncodeMessage(args);

    if (message && !message->empty())
    {
      flutter_controller_->engine()->messenger()->Send(
          "dev.turingart/unity_dropped_files",
          message->data(),
          message->size());

      LogToFile("发送拖拽文件/文件夹到Flutter: " + std::to_string(filePaths.size()) + " 个项目");
    }
  }
}

LRESULT
FlutterWindow::MessageHandler(HWND hwnd, UINT const message,
                              WPARAM const wparam,
                              LPARAM const lparam) noexcept
{
  // 持续搜索直到找到窗口
  if (shouldFindUnityWindow)
  {
    if (!unityHWND)
    {
      unityHWND = FindWindowExW(hwnd, NULL, L"UnityWndClass", NULL);
    }
    if (unityHWND)
    {
      shouldFindUnityWindow = false; // 找到后停止搜索
      RECT parentRect;
      GetClientRect(GetHandle(), &parentRect);

      if (unityVisible)
      {
        // 为边框检测留出边距，避免Unity窗口覆盖边框区域
        const int BORDER_MARGIN = 2; // 留出2像素的边距用于边框检测

        // 计算Unity窗口的位置和尺寸
        int unityX = BORDER_MARGIN;
        int unityY = ConvertDpToPixel(unityTopMargin);                                 // 顶部额外留出Unity边距
        int unityWidth = parentRect.right - parentRect.left - (BORDER_MARGIN * 2);     // 左右各留出边距
        int unityHeight = parentRect.bottom - parentRect.top - unityY - BORDER_MARGIN; // 底部留出边距

        // 设置Unity窗口位置和尺寸
        SetWindowPos(unityHWND, HWND_TOP,
                     unityX,
                     unityY,
                     unityWidth,
                     unityHeight,
                     SWP_SHOWWINDOW);
      }
      else
      {
        // 不可见时移到父窗口外部（使用父窗口高度的2倍负值）
        SetWindowPos(unityHWND, HWND_TOP,
                     0,
                     -parentRect.bottom * 2, // 使用2倍负值确保完全在可视区域外
                     parentRect.right - parentRect.left,
                     parentRect.bottom - parentRect.top,
                     SWP_NOACTIVATE);
      }
    }
  }

  // 如果 Unity 窗口存在，优先处理输入事件
  if (unityHWND)
  {
    // 记录键盘事件
    if (message == WM_KEYDOWN || message == WM_KEYUP || message == WM_CHAR ||
        message == WM_SYSKEYDOWN || message == WM_SYSKEYUP)
    {
      HWND focusedWindow = GetFocus();
      std::ostringstream logStream;
      logStream << "键盘事件: message=" << std::hex << message
                << ", wparam=" << std::hex << wparam
                << ", 当前焦点窗口=" << std::hex << focusedWindow
                << ", Unity窗口=" << std::hex << unityHWND
                << ", 是否匹配=" << (focusedWindow == unityHWND ? "是" : "否")
                << ", preventAutoFocusToUnity=" << (preventAutoFocusToUnity ? "true" : "false");
      LogToFile(logStream.str());
    }

    // 当Unity窗口可见时，根据preventAutoFocusToUnity状态决定是否转发输入事件
    if (unityVisible)
    {
      switch (message)
      {
      case WM_MOUSEMOVE:
      case WM_LBUTTONDOWN:
      case WM_LBUTTONUP:
      case WM_RBUTTONDOWN:
      case WM_RBUTTONUP:
      case WM_MBUTTONDOWN:
      case WM_MBUTTONUP:
      case WM_MOUSEWHEEL:
      case WM_MOUSEHOVER:
        // 如果设置了阻止自动焦点转移，不转发鼠标事件到Unity，让Flutter处理
        if (preventAutoFocusToUnity)
        {
          LogToFile("preventAutoFocusToUnity为true，不转发鼠标事件到Unity，让Flutter处理");
          // 不转发事件，让Flutter处理
          break;
        }
        else
        {
          // Mouse events sent directly to Unity window
          SendMessage(unityHWND, message, wparam, lparam);
          return 0;
        }
      case WM_KEYDOWN:
      case WM_KEYUP:
      case WM_CHAR:
      case WM_SYSKEYDOWN:
      case WM_SYSKEYUP:
      {
        // 如果设置了阻止自动焦点转移，不转发键盘事件到Unity，让Flutter处理
        if (preventAutoFocusToUnity)
        {
          LogToFile("preventAutoFocusToUnity为true，不转发键盘事件到Unity，让Flutter处理");
          // 不转发事件，让Flutter处理
          break;
        }
        else
        {
          // Log information before sending
          std::ostringstream logStream;
          logStream << "Sending keyboard event to Unity: message=" << std::hex << message
                    << ", wparam=" << std::hex << wparam;
          LogToFile(logStream.str());

          // Use PostMessage instead of SendMessage to avoid blocking
          PostMessage(unityHWND, message, wparam, lparam);

          // Don't return 0 immediately, allow message to continue propagating
          break; // Use break instead of return to allow message to continue
        }
      }
      } // Ensure closing brace for switch statement
    } // Ensure closing brace for if (unityVisible)
  } // Ensure closing brace for if (unityHWND)

  // Give Flutter, including plugins, an opportunity to handle window messages.
  if (flutter_controller_)
  {
    std::optional<LRESULT> result =
        flutter_controller_->HandleTopLevelWindowProc(hwnd, message, wparam,
                                                      lparam);
    if (result)
    {
      return *result;
    }
  }

  switch (message)
  {
  case WM_SIZE:
  {
    if (!unityHWND)
    {
      unityHWND = FindWindowExW(hwnd, NULL, L"UnityWndClass", NULL);
    }
    if (unityHWND)
    {
      if (unityVisible)
      {
        // 获取实际的客户区尺寸
        RECT clientRect;
        GetClientRect(hwnd, &clientRect);
        int width = clientRect.right - clientRect.left;
        int height = clientRect.bottom - clientRect.top;

        // 为边框检测留出边距，避免Unity窗口覆盖边框区域
        const int BORDER_MARGIN = 2; // 留出2像素的边距用于边框检测

        // 计算Unity窗口的位置和尺寸
        int unityX = BORDER_MARGIN;
        int unityY = ConvertDpToPixel(unityTopMargin);     // 顶部额外留出Unity边距
        int unityWidth = width - (BORDER_MARGIN * 2);      // 左右各留出边距
        int unityHeight = height - unityY - BORDER_MARGIN; // 底部留出边距

        // 设置Unity窗口位置和尺寸
        SetWindowPos(unityHWND, HWND_TOP,
                     unityX,
                     unityY,
                     unityWidth,
                     unityHeight,
                     SWP_SHOWWINDOW);
      }
      else
      {
        // 获取实际的客户区尺寸
        RECT clientRect;
        GetClientRect(hwnd, &clientRect);
        int width = clientRect.right - clientRect.left;
        int height = clientRect.bottom - clientRect.top;

        // 不可见时移到父窗口外部
        SetWindowPos(unityHWND, HWND_TOP,
                     0,
                     -height * 2, // 使用2倍负值确保完全在可视区域外
                     width,
                     height,
                     SWP_NOACTIVATE);
      }
    }
    break;
  }
  case WM_ACTIVATE:
  {
    // 窗口激活状态改变
    WORD activationType = LOWORD(wparam);
    LogToFile("WM_ACTIVATE event received, type: " + std::to_string(activationType));

    // 当窗口被激活时（从其他窗口切换回来或从最小化恢复）
    if (activationType == WA_ACTIVE || activationType == WA_CLICKACTIVE)
    {
      LogToFile("Window activated, scheduling focus check");

      // 只有当不阻止自动焦点转移时才发送延迟处理消息
      // preventAutoFocusToUnity为true表示正在展示Flutter dialog
      if (!preventAutoFocusToUnity)
      {
        // 使用PostMessage延迟处理焦点设置，避免干扰窗口激活流程
        PostMessage(hwnd, WM_APP + 1, 0, 0);
      }
      else
      {
        LogToFile("WM_ACTIVATE preventAutoFocusToUnity true - Flutter dialog detected, skipping Unity focus");
      }

      // 重置键盘状态
      BYTE keyboardState[256] = {0};
      GetKeyboardState(keyboardState);
      SetKeyboardState(keyboardState);
    }
    break;
  }
  case WM_APP + 1:
  {
    LogToFile("Processing delayed focus check");

    // 延迟一小段时间，确保窗口已完全激活
    Sleep(100);

    // 如果Unity窗口存在且可见且没有禁止自动焦点转移，设置焦点到Unity窗口
    // preventAutoFocusToUnity为true表示正在展示Flutter dialog
    if (unityHWND && unityVisible && !preventAutoFocusToUnity)
    {
      LogToFile("Setting focus to Unity window after activation");

      // Execute complete window activation sequence
      // 1. Send activation message
      SendMessage(unityHWND, WM_ACTIVATE, WA_ACTIVE, 0);
      // 2. Send focus message
      SendMessage(unityHWND, WM_SETFOCUS, 0, 0);

      // 3. Force redraw Unity window
      InvalidateRect(unityHWND, NULL, TRUE);
      UpdateWindow(unityHWND);

      // 4. Simulate Enter key events to "wake up" Input system without affecting Ctrl+arrow shortcuts
      SendMessage(unityHWND, WM_KEYDOWN, VK_RETURN, 0); // Use Enter key instead of Ctrl
      Sleep(10);                                        // Small delay between down and up
      SendMessage(unityHWND, WM_KEYUP, VK_RETURN, 0);
      Sleep(50); // Ensure key is fully released before any other input

      // 6. Finally set focus
      SetForegroundWindow(unityHWND);
      SetFocus(unityHWND);
    }
    else if (preventAutoFocusToUnity)
    {
      LogToFile("Flutter dialog detected during focus check, skipping Unity focus");
    }
    return 0;
  }
  case WM_APP + 2:
  {
    // 确保在主线程上处理消息
    if (instance_ && instance_->unity_message_channel_)
    {
      char *msg = (char *)lparam;
      instance_->unity_message_channel_->InvokeMethod(
          "onUnityMessage",
          std::make_unique<flutter::EncodableValue>(msg));
      free(msg); // 释放复制的字符串
    }
    return 0;
  }
  case WM_APP + 3:
  {
    LogToFile("Processing delayed focus from WM_SETFOCUS");

    // 较短延迟，用于WM_SETFOCUS的焦点处理
    Sleep(50);

    // 如果Unity窗口存在且可见且没有禁止自动焦点转移，设置焦点到Unity窗口
    if (unityHWND && unityVisible && !preventAutoFocusToUnity)
    {
      LogToFile("Setting focus to Unity window from delayed WM_SETFOCUS");

      // 执行完整的窗口激活序列
      SendMessage(unityHWND, WM_ACTIVATE, WA_ACTIVE, 0);
      SendMessage(unityHWND, WM_SETFOCUS, 0, 0);

      // 模拟Enter键事件唤醒输入系统
      SendMessage(unityHWND, WM_KEYDOWN, VK_RETURN, 0);
      Sleep(10);
      SendMessage(unityHWND, WM_KEYUP, VK_RETURN, 0);
      Sleep(50);

      // 最后设置焦点
      SetForegroundWindow(unityHWND);
      SetFocus(unityHWND);
    }
    return 0;
  }
  case WM_SETFOCUS:
  {
    // 当窗口获得焦点时
    LogToFile("WM_SETFOCUS received");

    // 避免与WM_ACTIVATE的延迟处理冲突，使用延迟处理
    // 如果Unity窗口存在且可见且没有禁止自动焦点转移，延迟设置焦点到Unity窗口
    if (unityHWND && unityVisible && !preventAutoFocusToUnity)
    {
      LogToFile("Scheduling delayed focus to Unity from WM_SETFOCUS");
      // 使用较短的延迟避免与WM_ACTIVATE冲突
      PostMessage(hwnd, WM_APP + 3, 0, 0);
    }
    else
    {
      if (preventAutoFocusToUnity)
      {
        LogToFile("WM_SETFOCUS preventAutoFocusToUnity true - Flutter dialog detected, skipping Unity focus");
      }
    }
    break;
  }
  case WM_FONTCHANGE:
  {
    flutter_controller_->engine()->ReloadSystemFonts();
    break;
  }
  case WM_COPYDATA:
  {
    PCOPYDATASTRUCT pCopyData = (PCOPYDATASTRUCT)lparam;
    if (pCopyData && pCopyData->lpData)
    {
      // 记录接收到的消息
      LogToFile("收到 WM_COPYDATA 消息");

      // 安全地将数据转换为字符串，避免编码问题
      std::string receivedData;
      const char *dataPtr = static_cast<const char *>(pCopyData->lpData);

      // 检查数据长度并安全处理
      if (pCopyData->cbData > 0)
      {
        // 查找null终止符，确保字符串正确结束
        size_t actualLength = strnlen(dataPtr, pCopyData->cbData);
        receivedData = std::string(dataPtr, actualLength);
      }

      LogToFile("接收到的文本数据: " + receivedData);

      // 通过 MethodChannel 发送到 Flutter
      if (flutter_controller_ && external_message_channel_)
      {
        // 调用Flutter端的方法 - 使用简化版本不带回调
        external_message_channel_->InvokeMethod(
            "onExternalMessage",
            std::make_unique<flutter::EncodableValue>(receivedData));
        LogToFile("已发送外部消息到Flutter: " + receivedData);
      }
      else
      {
        LogToFile("Flutter控制器或外部消息通道不可用");
      }

      // 返回 TRUE 表示消息已处理
      return TRUE;
    }
    break;
  }
  }

  return Win32Window::MessageHandler(hwnd, message, wparam, lparam);
}

void FlutterWindow::OnDestroy()
{
  LogToFile("Window is being destroyed, cleaning up resources...");

  // 终止 Unity 进程
  if (unityHWND && IsWindow(unityHWND))
  {
    // 尝试正常关闭 Unity 窗口
    LogToFile("Sending close message to Unity window");
    SendMessage(unityHWND, WM_CLOSE, 0, 0);

    // 给一些时间让 Unity 正常关闭
    Sleep(500);

    // 如果窗口仍然存在，强制终止
    if (IsWindow(unityHWND))
    {
      LogToFile("Unity window still exists, forcing termination");
      DWORD processId = 0;
      GetWindowThreadProcessId(unityHWND, &processId);
      if (processId != 0)
      {
        HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, processId);
        if (hProcess != NULL)
        {
          TerminateProcess(hProcess, 0);
          CloseHandle(hProcess);
          LogToFile("Unity process terminated");
        }
      }
    }

    unityHWND = nullptr;
  }

  // 释放 DLL 资源
  if (unity_launcher_dll_)
  {
    FreeLibrary(unity_launcher_dll_);
    unity_launcher_dll_ = nullptr;
    LogToFile("Unity launcher DLL unloaded");
  }

  if (unity_message_dll_)
  {
    FreeLibrary(unity_message_dll_);
    unity_message_dll_ = nullptr;
    LogToFile("Unity message DLL unloaded");
  }

  // 释放 Flutter 控制器
  if (flutter_controller_)
  {
    flutter_controller_ = nullptr;
    LogToFile("Flutter controller released");
  }

  // 调用父类的 OnDestroy
  Win32Window::OnDestroy();

  LogToFile("Window destroyed, all resources cleaned up");
}

// Unity窗口过程实现
LRESULT CALLBACK UnitySubclassProc(HWND hwnd, UINT message, WPARAM wParam,
                                   LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData)
{
  FlutterWindow *window = reinterpret_cast<FlutterWindow *>(dwRefData);

  // Handle drag and drop files
  if (message == WM_DROPFILES)
  {
    window->HandleDropFiles(wParam);
    return 0;
  }

  // 处理键盘事件，记录日志以便调试
  if (message == WM_KEYDOWN || message == WM_KEYUP)
  {
    // 记录键盘事件
    std::ostringstream logStream;
    logStream << "Unity窗口接收到键盘事件: message=" << std::hex << message
              << ", wparam=" << std::hex << wParam;
    LogToFile(logStream.str());

    // 特殊处理方向键
    if (wParam == VK_LEFT || wParam == VK_RIGHT ||
        wParam == VK_UP || wParam == VK_DOWN)
    {
      LogToFile("Unity窗口接收到方向键: " + std::to_string(wParam));
    }
  }
  if (message == WM_NCHITTEST)
  {
    LRESULT result = handle_nchittest(hwnd, lParam);
    return result;
  }

  return DefSubclassProc(hwnd, message, wParam, lParam);
}

LRESULT handle_nchittest(HWND window, WPARAM lparam)
{
  POINT pt = {GET_X_LPARAM(lparam), GET_Y_LPARAM(lparam)};
  ScreenToClient(window, &pt);
  RECT clientRect;
  GetClientRect(window, &clientRect);
  const int BORDER_WIDTH = 5;

  if (pt.x < clientRect.left + BORDER_WIDTH)
  {
    if (pt.y < clientRect.top + BORDER_WIDTH)
      return HTTOPLEFT;
    if (pt.y > clientRect.bottom - BORDER_WIDTH)
      return HTBOTTOMLEFT;
    return HTLEFT;
  }
  else if (pt.x > clientRect.right - BORDER_WIDTH)
  {
    if (pt.y < clientRect.top + BORDER_WIDTH)
      return HTTOPRIGHT;
    if (pt.y > clientRect.bottom - BORDER_WIDTH)
      return HTBOTTOMRIGHT;
    return HTRIGHT;
  }
  else if (pt.y < clientRect.top + BORDER_WIDTH)
  {
    return HTTOP;
  }
  else if (pt.y > clientRect.bottom - BORDER_WIDTH)
  {
    return HTBOTTOM;
  }
  return HTCLIENT;
}

// 焦点调试功能实现
void FlutterWindow::RegisterFocusDebugChannel()
{
  focus_debug_channel_ = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(),
      "com.turingart.focus_debug",
      &flutter::StandardMethodCodec::GetInstance());

  focus_debug_channel_->SetMethodCallHandler(
      [this](const flutter::MethodCall<flutter::EncodableValue> &call,
             std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
      {
        if (call.method_name() == "getCurrentFocusInfo")
        {
          try
          {
            flutter::EncodableMap focusInfo = GetCurrentFocusInfo();
            result->Success(flutter::EncodableValue(focusInfo));
          }
          catch (const std::exception &e)
          {
            result->Error("FOCUS_DEBUG_ERROR", e.what());
          }
        }
        else if (call.method_name() == "startFocusMonitoring")
        {
          // 启动焦点监控（可以定期发送焦点信息到Flutter）
          LogToFile("Focus monitoring started");
          result->Success();
        }
        else if (call.method_name() == "stopFocusMonitoring")
        {
          // 停止焦点监控
          LogToFile("Focus monitoring stopped");
          result->Success();
        }
        else if (call.method_name() == "forceFocusToFlutter")
        {
          // 强制设置焦点到Flutter窗口
          HWND flutterHwnd = GetHandle();
          SetForegroundWindow(flutterHwnd);
          SetFocus(flutterHwnd);
          LogToFile("Force focus to Flutter window");
          result->Success();
        }
        else if (call.method_name() == "forceFocusToUnity")
        {
          // 强制设置焦点到Unity窗口
          if (unityHWND && IsWindow(unityHWND))
          {
            SetForegroundWindow(unityHWND);
            SetFocus(unityHWND);
            LogToFile("Force focus to Unity window");
            result->Success();
          }
          else
          {
            result->Error("UNITY_NOT_FOUND", "Unity window not found");
          }
        }
        else
        {
          result->NotImplemented();
        }
      });
}

flutter::EncodableMap FlutterWindow::GetCurrentFocusInfo()
{
  flutter::EncodableMap focusInfo;

  // 获取当前焦点窗口
  HWND focusedWindow = GetFocus();
  HWND foregroundWindow = GetForegroundWindow();
  HWND activeWindow = GetActiveWindow();

  // 基本焦点信息
  focusInfo[flutter::EncodableValue("focusedWindow")] =
      flutter::EncodableValue(reinterpret_cast<int64_t>(focusedWindow));
  focusInfo[flutter::EncodableValue("foregroundWindow")] =
      flutter::EncodableValue(reinterpret_cast<int64_t>(foregroundWindow));
  focusInfo[flutter::EncodableValue("activeWindow")] =
      flutter::EncodableValue(reinterpret_cast<int64_t>(activeWindow));

  // Flutter窗口信息
  HWND flutterWindow = GetHandle();
  focusInfo[flutter::EncodableValue("flutterWindow")] =
      flutter::EncodableValue(reinterpret_cast<int64_t>(flutterWindow));
  focusInfo[flutter::EncodableValue("isFlutterFocused")] =
      flutter::EncodableValue(focusedWindow == flutterWindow);

  // Unity窗口信息
  focusInfo[flutter::EncodableValue("unityWindow")] =
      flutter::EncodableValue(reinterpret_cast<int64_t>(unityHWND));
  focusInfo[flutter::EncodableValue("isUnityFocused")] =
      flutter::EncodableValue(unityHWND && focusedWindow == unityHWND);
  focusInfo[flutter::EncodableValue("unityVisible")] =
      flutter::EncodableValue(unityVisible);
  focusInfo[flutter::EncodableValue("preventAutoFocusToUnity")] =
      flutter::EncodableValue(preventAutoFocusToUnity);

  // 窗口标题和类名
  if (focusedWindow)
  {
    focusInfo[flutter::EncodableValue("focusedWindowTitle")] =
        flutter::EncodableValue(GetWindowTitle(focusedWindow));
    focusInfo[flutter::EncodableValue("focusedWindowClass")] =
        flutter::EncodableValue(GetWindowClassName(focusedWindow));
  }

  if (foregroundWindow)
  {
    focusInfo[flutter::EncodableValue("foregroundWindowTitle")] =
        flutter::EncodableValue(GetWindowTitle(foregroundWindow));
    focusInfo[flutter::EncodableValue("foregroundWindowClass")] =
        flutter::EncodableValue(GetWindowClassName(foregroundWindow));
  }

  // 焦点状态分析
  std::string focusStatus = "unknown";
  if (focusedWindow == flutterWindow)
  {
    focusStatus = "flutter";
  }
  else if (unityHWND && focusedWindow == unityHWND)
  {
    focusStatus = "unity";
  }
  else if (focusedWindow == nullptr)
  {
    focusStatus = "none";
  }
  else
  {
    focusStatus = "other";
  }
  focusInfo[flutter::EncodableValue("focusStatus")] = flutter::EncodableValue(focusStatus);

  // 时间戳
  auto now = std::chrono::system_clock::now();
  auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
  focusInfo[flutter::EncodableValue("timestamp")] = flutter::EncodableValue(timestamp);

  return focusInfo;
}

std::string FlutterWindow::GetWindowTitle(HWND hwnd)
{
  if (!hwnd || !IsWindow(hwnd))
    return "";

  wchar_t title[256];
  int length = GetWindowTextW(hwnd, title, sizeof(title) / sizeof(wchar_t));
  if (length == 0)
    return "";

  // 转换为UTF-8
  int utf8Length = WideCharToMultiByte(CP_UTF8, 0, title, length, nullptr, 0, nullptr, nullptr);
  if (utf8Length == 0)
    return "";

  std::string utf8Title(utf8Length, 0);
  WideCharToMultiByte(CP_UTF8, 0, title, length, &utf8Title[0], utf8Length, nullptr, nullptr);
  return utf8Title;
}

std::string FlutterWindow::GetWindowClassName(HWND hwnd)
{
  if (!hwnd || !IsWindow(hwnd))
    return "";

  wchar_t className[256];
  int length = GetClassNameW(hwnd, className, sizeof(className) / sizeof(wchar_t));
  if (length == 0)
    return "";

  // 转换为UTF-8
  int utf8Length = WideCharToMultiByte(CP_UTF8, 0, className, length, nullptr, 0, nullptr, nullptr);
  if (utf8Length == 0)
    return "";

  std::string utf8ClassName(utf8Length, 0);
  WideCharToMultiByte(CP_UTF8, 0, className, length, &utf8ClassName[0], utf8Length, nullptr, nullptr);
  return utf8ClassName;
}
