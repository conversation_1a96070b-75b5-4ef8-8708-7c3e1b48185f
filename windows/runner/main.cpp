﻿#include <flutter/dart_project.h>
#include <flutter/flutter_view_controller.h>
#include <windows.h>
#include <Psapi.h>
#include <TlHelp32.h>
#include <DbgHelp.h>

#pragma comment(lib, "Dbghelp.lib")

#include "flutter_window.h"
#include "utils.h"
#include <pg_desktop_multi_window/desktop_multi_window_plugin.h>
// #include <bitsdojo_window_windows/bitsdojo_window_plugin.h>
// auto bdw = bitsdojo_window_configure(BDW_CUSTOM_FRAME | BDW_HIDE_ON_STARTUP);

// 终止所有子进程
void TerminateChildProcesses(DWORD parentProcessId)
{
  HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
  if (hSnapshot == INVALID_HANDLE_VALUE)
  {
    return;
  }

  PROCESSENTRY32 pe32;
  pe32.dwSize = sizeof(PROCESSENTRY32);

  if (!Process32First(hSnapshot, &pe32))
  {
    CloseHandle(hSnapshot);
    return;
  }

  do
  {
    // 检查是否是当前进程的子进程
    if (pe32.th32ParentProcessID == parentProcessId)
    {
      HANDLE hChildProcess = OpenProcess(PROCESS_TERMINATE, FALSE, pe32.th32ProcessID);
      if (hChildProcess != NULL)
      {
        // 终止子进程
        TerminateProcess(hChildProcess, 0);
        CloseHandle(hChildProcess);
      }
    }
  } while (Process32Next(hSnapshot, &pe32));

  CloseHandle(hSnapshot);
}

// 全局异常处理函数
LONG WINAPI AppUnhandledExceptionFilter(EXCEPTION_POINTERS *exceptionInfo)
{
  // 终止所有子进程
  TerminateChildProcesses(::GetCurrentProcessId());

  // 创建迷你转储文件
  HANDLE hFile = CreateFile(L"crash.dmp", GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
  if (hFile != INVALID_HANDLE_VALUE)
  {
    MINIDUMP_EXCEPTION_INFORMATION exceptionParam;
    exceptionParam.ThreadId = GetCurrentThreadId();
    exceptionParam.ExceptionPointers = exceptionInfo;
    exceptionParam.ClientPointers = FALSE;

    MiniDumpWriteDump(
        GetCurrentProcess(),
        GetCurrentProcessId(),
        hFile,
        MiniDumpNormal,
        &exceptionParam,
        NULL,
        NULL);

    CloseHandle(hFile);
  }

  return EXCEPTION_EXECUTE_HANDLER;
}

auto dwm = InitMainWindowHook();
int APIENTRY wWinMain(_In_ HINSTANCE instance, _In_opt_ HINSTANCE prev,
                      _In_ wchar_t *command_line, _In_ int show_command)
{
  // 设置全局异常处理
  SetUnhandledExceptionFilter(AppUnhandledExceptionFilter);

  // 创建互斥体变量
  HANDLE hMutex;

  // 创建互斥体，防止应用程序多开
  hMutex = CreateMutex(NULL, TRUE, L"TuringArtApplicationMutex");
  if (hMutex != NULL && GetLastError() == ERROR_ALREADY_EXISTS)
  {
    // 互斥体已存在，说明已有一个实例在运行
    // 查找已运行的实例窗口
    // 使用窗口类名和窗口标题查找
    HWND hWnd = FindWindow(L"FLUTTER_RUNNER_WIN32_WINDOW", L"图灵精修");
    if (hWnd != NULL)
    {
      // 如果窗口最小化，则恢复窗口
      if (IsIconic(hWnd))
      {
        ShowWindow(hWnd, SW_RESTORE);
      }
      // 将已有的实例窗口设为前台窗口
      SetForegroundWindow(hWnd);
    }
    // 关闭互斥体句柄
    CloseHandle(hMutex);
    // 退出当前实例
    return EXIT_FAILURE;
  }

  // Attach to console when present (e.g., 'flutter run') or create a
  // new console when running with a debugger.
  if (!::AttachConsole(ATTACH_PARENT_PROCESS) && ::IsDebuggerPresent())
  {
    CreateAndAttachConsole();
  }

  // Initialize COM, so that it is available for use in the library and/or
  // plugins.
  ::CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);

  flutter::DartProject project(L"data");

  std::vector<std::string> command_line_arguments =
      GetCommandLineArguments();

  project.set_dart_entrypoint_arguments(std::move(command_line_arguments));

  FlutterWindow window(project);
  Win32Window::Point origin(10, 10);
  Win32Window::Size size(1285, 800); // 设置为最小尺寸

  if (!window.Create(L"图灵精修", origin, size))
  {
    return EXIT_FAILURE;
  }
  window.SetQuitOnClose(true);

  ::MSG msg;
  while (::GetMessage(&msg, nullptr, 0, 0))
  {
    ::TranslateMessage(&msg);
    ::DispatchMessage(&msg);
  }

  // 终止所有子进程
  TerminateChildProcesses(::GetCurrentProcessId());

  ::CoUninitialize();

  // 释放互斥体
  if (hMutex != NULL)
  {
    CloseHandle(hMutex);
  }

  return EXIT_SUCCESS;
}
