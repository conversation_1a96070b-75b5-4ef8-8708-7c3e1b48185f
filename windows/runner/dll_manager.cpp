﻿#include "dll_manager.h"
#include <fstream>
#include <ctime>

// 静态成员初始化
HMODULE DllManager::hDll = NULL;
AddParamFunc DllManager::addParamFunc = nullptr;
GenSignFunc DllManager::genSignFunc = nullptr;

void DllManager::LogToFile(const std::string &message)
{
    std::ofstream logFile("flutter_app.log", std::ios::app);
    time_t now = time(0);
    char dt[26];
    ctime_s(dt, sizeof(dt), &now);
    logFile << dt << ": " << message << std::endl;
    logFile.close();
}

std::string DllManager::GetLastErrorAsString()
{
    DWORD error = GetLastError();
    if (error == 0)
    {
        return std::string();
    }

    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER |
            FORMAT_MESSAGE_FROM_SYSTEM |
            FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL,
        error,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer,
        0,
        NULL);

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);
    return message;
}

bool DllManager::Initialize()
{
    if (hDll != NULL)
    {
        return true;
    }

    LogToFile("Initializing DLL Manager");

    wchar_t fullPath[MAX_PATH];
    GetModuleFileNameW(NULL, fullPath, MAX_PATH);
    std::wstring wDllPath = std::wstring(fullPath);
    wDllPath = wDllPath.substr(0, wDllPath.find_last_of(L"\\")) + L"\\pgnetworksign.dll";

    // 记录尝试加载的 DLL 路径
    char mbDllPath[MAX_PATH];
    size_t convertedChars = 0;
    wcstombs_s(&convertedChars, mbDllPath, sizeof(mbDllPath), wDllPath.c_str(), _TRUNCATE);
    LogToFile("Attempting to load DLL from: " + std::string(mbDllPath));

    // 检查文件是否存在
    DWORD fileAttrs = GetFileAttributesW(wDllPath.c_str());
    if (fileAttrs == INVALID_FILE_ATTRIBUTES)
    {
        LogToFile("DLL file does not exist at the specified path");
        return false;
    }

    hDll = LoadLibraryW(wDllPath.c_str());
    if (hDll == NULL)
    {
        std::string errorDetails = GetLastErrorAsString();
        LogToFile("Failed to load DLL: " + errorDetails);
        return false;
    }

    addParamFunc = (AddParamFunc)GetProcAddress(hDll, "addParam");
    genSignFunc = (GenSignFunc)GetProcAddress(hDll, "genSign");

    if (!addParamFunc || !genSignFunc)
    {
        LogToFile("Failed to get function addresses");
        FreeLibrary(hDll);
        hDll = NULL;
        return false;
    }

    LogToFile("DLL Manager initialized successfully");
    return true;
}

void DllManager::Cleanup()
{
    if (hDll != NULL)
    {
        LogToFile("Cleaning up DLL Manager");
        FreeLibrary(hDll);
        hDll = NULL;
        addParamFunc = nullptr;
        genSignFunc = nullptr;
        LogToFile("DLL Manager cleanup completed");
    }
}