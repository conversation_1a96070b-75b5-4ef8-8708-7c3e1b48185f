cmake_minimum_required(VERSION 3.14)
project(runner LANGUAGES CXX)

# Define the application target. To change its name, change BINARY_NAME in the
# top-level CMakeLists.txt, not the value here, or `flutter run` will no longer
# work.
#
# Any new source files that you add to the application should be added here.
add_executable(${BINARY_NAME} WIN32
  "flutter_window.cpp"
  "dll_manager.cpp"
  "main.cpp"
  "utils.cpp"
  "win32_window.cpp"
  "${FLUTTER_MANAGED_DIR}/generated_plugin_registrant.cc"
  "Runner.rc"
  "runner.exe.manifest"
)

# Apply the standard set of build settings. This can be removed for applications
# that need different build settings.
apply_standard_settings(${BINARY_NAME})

# Add preprocessor definitions for the build version.
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION=\"${FLUTTER_VERSION}\"")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_MAJOR=${FLUTTER_VERSION_MAJOR}")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_MINOR=${FLUTTER_VERSION_MINOR}")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_PATCH=${FLUTTER_VERSION_PATCH}")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_BUILD=${FLUTTER_VERSION_BUILD}")

# Disable Windows macros that collide with C++ standard library functions.
target_compile_definitions(${BINARY_NAME} PRIVATE "NOMINMAX")

# Add dependency libraries and include directories. Add any application-specific
# dependencies here.
target_link_libraries(${BINARY_NAME} PRIVATE flutter flutter_wrapper_app)
target_link_libraries(${BINARY_NAME} PRIVATE "dwmapi.lib")
target_include_directories(${BINARY_NAME} PRIVATE "${CMAKE_SOURCE_DIR}")

# 复制 DLL 文件到输出目录
add_custom_command(
  TARGET ${BINARY_NAME} POST_BUILD
  COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/Sign/pgnetworksign.dll"
    "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_CURRENT_SOURCE_DIR}/pgcollect/pgcollect.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

# 定义native库的根目录
set(NATIVE_LIB_DIR "${CMAKE_SOURCE_DIR}/../lib/native")

# 读取构建参数环境变量，与顶层CMakeLists.txt保持一致
if(DEFINED ENV{IS_WIN7})
    set(IS_WIN7 $ENV{IS_WIN7})
    message(STATUS "Runner: 构建版本环境变量: ${IS_WIN7}")
endif()

# 检测Windows版本并选择对应的DLL目录
# 优先使用环境变量IS_WIN7，如果没有设置则使用系统版本检测
if(DEFINED IS_WIN7 AND IS_WIN7 STREQUAL "true")
    set(WIN_VERSION "win7")
    message(STATUS "Runner: 使用环境变量指定的 Windows 7 库")
else()
    set(WIN_VERSION "win10")
    message(STATUS "Runner: 使用系统检测的 Windows 10 库")
endif()

# 复制 PGRawDecoderLib.dll 文件到输出目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${NATIVE_LIB_DIR}/raw_decoder/windows/${WIN_VERSION}/PGRawDecoderLib.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

# 复制 raw_decoder bundle 目录中的所有文件到输出目录下的bundle目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        "${NATIVE_LIB_DIR}/raw_decoder/windows/bundle"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>/bundle"
)

# 复制 PGSalientMattersLib.dll 文件到输出目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${NATIVE_LIB_DIR}/salient_matters/windows/${WIN_VERSION}/PGSalientMattersLib.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

# 复制 PGRawConversionLib.dll 文件到输出目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${NATIVE_LIB_DIR}/raw_conversion/windows/${WIN_VERSION}/PGRawConversionLib.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

# 复制 PGImageProcessorLib.dll 文件到输出目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${NATIVE_LIB_DIR}/image_processor/windows/${WIN_VERSION}/PGImageProcessorLib.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

# 复制 libomp140.x86_64.dll 文件到输出目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${NATIVE_LIB_DIR}/basic_plugin/x86_64/libomp140.x86_64.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

# 复制 PGDiskInfoLib.dll 文件到输出目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${NATIVE_LIB_DIR}/disk_info/windows/${WIN_VERSION}/PGDiskInfoLib.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)

# Run the Flutter tool portions of the build. This must not be removed.
add_dependencies(${BINARY_NAME} flutter_assemble)
