#ifndef RUNNER_FLUTTER_WINDOW_H_
#define RUNNER_FLUTTER_WINDOW_H_

#include <flutter/dart_project.h>
#include <flutter/flutter_view_controller.h>
#include <flutter/method_channel.h>
#include <flutter/encodable_value.h>
#include <commctrl.h>

#include <memory>

#include "win32_window.h"
#include "dll_manager.h"
#include "unity_launcher.h"

// A window that does nothing but host a Flutter view.
class FlutterWindow : public Win32Window
{
public:
  // Creates a new FlutterWindow hosting a Flutter view running |project|.
  explicit FlutterWindow(const flutter::DartProject &project);
  virtual ~FlutterWindow();

  // Handle dropped files (for UnitySubclassProc)
  void HandleDropFiles(WPARAM wParam);

protected:
  // Win32Window:
  bool OnCreate() override;
  void OnDestroy() override;
  LRESULT MessageHandler(HWND window, UINT const message, WPARAM const wparam,
                         LPARAM const lparam) noexcept override;

private:
  int ConvertDpToPixel(int dp);
  inline static int unityTopMargin = 44;
  bool unityVisible = false;
  bool shouldFindUnityWindow = false;
  HWND unityHWND = nullptr;
  bool preventAutoFocusToUnity = false;
  void RegisterUnityLauncherPlugin();
  HMODULE unity_launcher_dll_;
  typedef bool (*LaunchUnityFunc)(const wchar_t *unityPath, HWND parentWindow, const wchar_t *additionalArgs);
  LaunchUnityFunc launch_unity_func_;
  // unity message
  void RegisterUnityMessageSender();
  typedef void (*CallUnityFunc)(const char *message);
  CallUnityFunc call_unity_func_ = nullptr;
  HMODULE unity_message_dll_ = nullptr;
  // The project to run.
  flutter::DartProject project_;

  // The Flutter instance hosted by this window.
  std::unique_ptr<flutter::FlutterViewController> flutter_controller_;

  void RegisterMethodChannels();
  void RegisterFocusDebugChannel();

  static FlutterWindow *instance_;
  static void HandleUnityMessage(const char *message);
  std::unique_ptr<flutter::MethodChannel<flutter::EncodableValue>> unity_message_channel_;
  std::unique_ptr<flutter::MethodChannel<flutter::EncodableValue>> external_message_channel_;
  std::unique_ptr<flutter::MethodChannel<flutter::EncodableValue>> focus_debug_channel_;

  // 焦点调试相关方法
  flutter::EncodableMap GetCurrentFocusInfo();
  std::string GetWindowTitle(HWND hwnd);
  std::string GetWindowClassName(HWND hwnd);
};

#endif // RUNNER_FLUTTER_WINDOW_H_
