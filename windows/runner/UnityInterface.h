#pragma once

// 定义回调函数类型
typedef void (*PGMessageEntry)(const char* message);

#ifdef __cplusplus
extern "C" {
#endif

// 导出函数声明
__declspec(dllexport) int CallFromUnityWithReturn(const char* message);
__declspec(dllexport) void OnPGMessageHandlerInitialized(PGMessageEntry callback);
__declspec(dllexport) void CallUnity(const char* message);
__declspec(dllexport) void RegisterUnityMessageHandler(void (*handler)(const char*));
__declspec(dllexport) void CallNativeWinCommon(const char* message);

#ifdef __cplusplus
}
#endif 