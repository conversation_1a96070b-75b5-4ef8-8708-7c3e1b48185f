﻿#ifndef RUNNER_DLL_MANAGER_H_
#define RUNNER_DLL_MANAGER_H_

#include <windows.h>
#include <string>

// DLL 函数类型定义
typedef void *(*AddParamFunc)(const char *key, const char *value);
typedef char *(*GenSignFunc)(const char *host, const char *urlPath, const char *method,
                             void *params, int count, void *jsonBodyData, int bytes);

class DllManager
{
private:
    static HMODULE hDll;
    static AddParamFunc addParamFunc;
    static GenSignFunc genSignFunc;

    // 禁止实例化
    DllManager() = delete;
    ~DllManager() = delete;

    // 日志辅助函数
    static void LogToFile(const std::string &message);
    static std::string GetLastErrorAsString();

public:
    static bool Initialize();
    static void Cleanup();
    static AddParamFunc GetAddParamFunc() { return addParamFunc; }
    static GenSignFunc GetGenSignFunc() { return genSignFunc; }
};

#endif // RUNNER_DLL_MANAGER_H_