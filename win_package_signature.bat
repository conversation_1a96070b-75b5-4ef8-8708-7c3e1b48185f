@echo off 
chcp 65001 > nul

REM 接收签名exe文件路径参数
set BUILD_EXE_PATH=%~1
set SIGNATURE_SHA1=%~2

REM 参数校验
if "%BUILD_EXE_PATH%"=="" (
    echo 错误: 缺少签名路径参数
    exit /b 1
)

REM 参数校验
if "%SIGNATURE_SHA1%"=="" (
    echo 错误: 缺少签名SHA1参数
    exit /b 1
)

REM 检查必要的命令
where signtool >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ==========================================
    echo Error: signtool 未安装或未添加到系统变量路径
    echo Error: %BUILD_EXE_PATH% 签名失败！
    echo ==========================================
    exit /b 
)

echo ==========================================
echo 开始签名 %BUILD_EXE_PATH%
echo ==========================================

signtool sign /v /fd sha256 /sha1 %SIGNATURE_SHA1% /tr http://rfc3161timestamp.globalsign.com/advanced /td sha256 %BUILD_EXE_PATH%
if %ERRORLEVEL% NEQ 0 (
    echo ==========================================
    echo Error: %BUILD_EXE_PATH% 签名失败！
    echo ==========================================
    exit /b
)

echo ==========================================
echo %BUILD_EXE_PATH% 签名成功！
echo ==========================================
exit /b