import 'dart:convert';
import 'dart:io';

void main(List<String> args) async {
  final isRelease = args.contains('--release');
  final doSign = args.contains('--sign');
  final doEncrypt = args.contains('--encrypt');
  final skipUpload = args.contains('--skip-upload');

  // 解析平台参数，如果没有提供则默认为all
  String platformArg = 'all';
  for (final arg in args) {
    if (arg.startsWith('--platform=')) {
      platformArg = arg.split('=')[1].toLowerCase();
      break;
    }
  }

  final version = _parseVersion();
  final timestamp = _generateTimestamp();
  final signature_sha1 = _parseSignatureSHA1();

  // 根据平台参数筛选配置
  final filteredConfigs = [
    {
      'platform': 'universal', // 标识构建平台，universal表示通用Windows平台
      'config': {
        'projectName': 'turing_art', // 项目名称，用于生成构建产物
        'isPackage': true, // 是否打包发布版本，True表示输出到Release目录，False表示输出到Debug目录
        'isWin7': false, // 是否为Windows 7平台构建，True表示针对Win7构建，False表示通用Windows平台
        'isRelease': isRelease, // 是否为发布版本，True表示发布版本，False表示调试版本
        "sourceDataFile": "Retouch_Data" // Unity资源数据包输出文件夹名称，用于后续拷贝
      },
    },
    {
      'platform': 'win7', // 标识构建平台，win7表示Windows 7平台
      'config': {
        'projectName': 'turing_art', // 项目名称，用于生成构建产物
        'isPackage': true, // 是否打包发布版本，True表示输出到Release目录，False表示输出到Debug目录
        'isRelease': isRelease, // 是否为发布版本，True表示发布版本，False表示调试版本
        'isWin7': true, // 是否为Windows 7平台构建，True表示针对Win7构建，False表示通用Windows平台
        "sourceDataFile": "Retouch_Data" // Unity资源数据包输出文件夹名称，用于后续拷贝
      },
    }
  ].where((config) {
    if (platformArg == 'all') return true;
    return config['platform'] == platformArg;
  }).toList();

  if (filteredConfigs.isEmpty) {
    print(
        '❌ 错误：无效的平台参数。请使用 --platform=win7 或 --platform=universal 或 --platform=all');
    exit(1);
  }

  // 清空build目录
  print('🔄 开始清空build目录');
  if (Directory('build').existsSync()) {
    Directory('build').deleteSync(recursive: true);
    print('✅ build目录已清空');
  } else {
    print('⏭️ build目录不存在，无需清空');
  }

  for (final config in filteredConfigs) {
    final buildPlatform = config['platform'] as String;
    final buildType = isRelease ? 'release' : 'debug';

    // Step 0: Update submodules based on platform
    print('🔄 开始更新 $buildPlatform 对应的子库');
    await _updateSubmodules(buildPlatform, isRelease);

    print('🚀 开始构建 $buildPlatform');
    await _generateConfig(config['config'] as Map<String, dynamic>);
    await _runPackageScript(version, timestamp, buildPlatform, buildType,
        doEncrypt, doSign, signature_sha1);
    await _renameAndMoveArtifacts(version, timestamp, buildPlatform, buildType);

    if (doSign) {
      await _signuatureForSetupWinApplication(
          version, timestamp, buildPlatform, buildType, signature_sha1);
    } else {
      print('⏭️ 跳过签名步骤');
    }

    if (!skipUpload) {
      // 获取当前分支
      final currentBranch = await _getCurrentBranch();
      // 生成EXE文件名
      final exeName =
          _generateExeName(version, timestamp, buildPlatform, buildType);
      await _runUploadPackageScript(
          version, timestamp, buildPlatform, buildType, currentBranch, exeName);
    } else {
      print('⏭️ 跳过上传步骤');
    }
    await _deleteConfig();
  }
  print('✅ 全部构建完成');
}

Future<void> _updateSubmodules(String buildPlatform, bool isRelease) async {
  try {
    // 根据平台选择对应的子库目录
    final submoduleDir =
        buildPlatform == 'win7' ? 'unityoutputwin7' : 'unityoutputwin10p';

    print('📥 正在更新子库: $submoduleDir');

    // 检查子模块目录是否存在
    final submoduleDirExists = Directory(submoduleDir).existsSync();
    if (!submoduleDirExists) {
      print('⚙️ 初始化子模块: $submoduleDir');
      await Process.run('git', ['submodule', 'update', '--init', submoduleDir]);
    }

    print('更新到指定提交');
    // 更新子模块到主仓库中指定的提交
    await Process.run('git', ['submodule', 'update', submoduleDir]);

    print('✅ 子库更新完成: $submoduleDir');
  } catch (e) {
    print('❌ 子库更新失败: $e');
    exit(1);
  }
}

Future<void> _deleteConfig() async {
  try {
    File('package_unity_config.json').deleteSync();
    print('🛠  配置文件已删除');
  } catch (e) {
    print('❌ 配置文件删除失败: $e');
    exit(1);
  }
}

Future<void> _generateConfig(Map<String, dynamic> config) async {
  try {
    File('package_unity_config.json').writeAsStringSync(jsonEncode(config));
    print('🛠  配置文件已生成: ${jsonEncode(config)}');
  } catch (e) {
    print('❌ 配置文件生成失败: $e');
    exit(1);
  }
}

Future<void> _runPackageScript(
  String version,
  String timestamp,
  String buildPlatform,
  String buildType,
  bool doEncrypt,
  bool doSign,
  String signatureSHA1,
) async {
  final process = await Process.start(
    'win_package_runner.bat',
    [
      version,
      timestamp,
      buildPlatform,
      buildType,
      doEncrypt.toString(),
      doSign.toString(),
      signatureSHA1,
    ],
  );

  process.stdout
      .transform(utf8.decoder)
      .transform(const LineSplitter())
      .listen((line) => print('[BATCH] $line'));

  process.stderr
      .transform(utf8.decoder)
      .transform(const LineSplitter())
      .listen((line) => print('[ERROR] $line'));

  final exitCode = await process.exitCode;
  if (exitCode != 0) exit(exitCode);
}

Future<void> _runUploadPackageScript(
  String version,
  String timestamp,
  String buildPlatform,
  String buildType,
  String currentBranch,
  String exeName,
) async {
  print('🚀 开始上传 $buildPlatform 到 NAS');
  final process = await Process.start(
    'win_package_upload.bat',
    [
      version,
      timestamp,
      buildPlatform,
      buildType,
      currentBranch,
      exeName,
    ],
  );

  process.stdout
      .transform(utf8.decoder)
      .transform(const LineSplitter())
      .listen((line) => print('[BATCH] $line'));

  process.stderr
      .transform(utf8.decoder)
      .transform(const LineSplitter())
      .listen((line) => print('[ERROR] $line'));

  final exitCode = await process.exitCode;
  if (exitCode != 0) exit(exitCode);
}

Future<void> _archiveArtifacts(String buildPlatform) async {
  try {
    final version = _parseVersion();
    final source = Directory('dist/$version');
    final destination = Directory('dist/${version}_$buildPlatform');

    if (await source.exists()) {
      await source.rename(destination.path);
      print('📦 构建产物已归档至: ${destination.path}');
    }
  } catch (e) {
    print('⚠️ 归档过程中发生异常: $e');
  }
}

String _parseVersion() {
  final lines = File('pubspec.yaml').readAsLinesSync();

  for (final line in lines) {
    if (line.trim().startsWith('version:')) {
      return line.split(':')[1].trim().replaceAll('"', '').replaceAll(' ', '');
    }
  }

  throw Exception('未找到有效版本号');
}

// 新增解析pubspec.yaml方法，用于获取签名SHA1
String _parseSignatureSHA1() {
  final lines = File('pubspec.yaml').readAsLinesSync();

  for (final line in lines) {
    if (line.trim().startsWith('signature_sha1:')) {
      return line.split(':')[1].trim().replaceAll('"', '');
    }
  }
  throw Exception('未找到有效签名使用的SHA1');
}

// 新增时间戳生成方法
String _generateTimestamp() {
  final now = DateTime.now();
  return '${now.year}${now.month.toString().padLeft(2, '0')}'
      '${now.day.toString().padLeft(2, '0')}'
      '${now.hour.toString().padLeft(2, '0')}'
      '${now.minute.toString().padLeft(2, '0')}'
      '${now.second.toString().padLeft(2, '0')}';
}

Future<void> _renameAndMoveArtifacts(
  String version,
  String timestamp,
  String buildPlatform,
  String buildType,
) async {
  try {
    final sourceDir = Directory('dist/$version');
    final destDir = Directory('dist/$version/$timestamp/$buildPlatform');

    // 查找所有符合格式的exe文件（包含子目录）
    final exeFiles = await sourceDir
        .list()
        .where((entity) => entity.path.endsWith('.exe'))
        .toList();

    if (exeFiles.isEmpty) {
      print('⚠️ 未找到可用的EXE文件');
      return;
    }

    // 创建目标目录
    await destDir.create(recursive: true);

    // 移动并重命名文件
    for (final file in exeFiles) {
      if (file is File) {
        final newFileName =
            'turing_art-$version-$buildPlatform-$timestamp-$buildType-setup.exe';
        final newPath = '${destDir.path}/$newFileName';

        // 处理文件冲突
        if (await File(newPath).exists()) {
          await File(newPath).delete();
        }

        await file.copy(newPath);
        print('✅ 文件已复制: ${file.path} → $newPath');
      }
    }

    // 清理原始文件（保留目录结构）
    for (final file in exeFiles) {
      if (file is File) {
        await file.delete();
      }
    }
  } catch (e) {
    print('❌ 文件移动失败: $e');
    exit(1);
  }
}

/// 获取当前Git分支
Future<String> _getCurrentBranch() async {
  try {
    final result =
        await Process.run('git', ['rev-parse', '--abbrev-ref', 'HEAD']);
    if (result.exitCode == 0) {
      final branch = result.stdout.toString().trim();
      print('📌 当前分支: $branch');
      return branch;
    } else {
      print('⚠️ 获取Git分支失败，使用默认值: main');
      return 'main';
    }
  } catch (e) {
    print('⚠️ 获取Git分支异常: $e，使用默认值: main');
    return 'main';
  }
}

/// 生成EXE文件名
String _generateExeName(
    String version, String timestamp, String buildPlatform, String buildType) {
  return 'turing_art-$version-$buildPlatform-$timestamp-$buildType-setup.exe';
}

/// setup签名文件
Future<void> _signuatureForSetupWinApplication(
  String version,
  String timestamp,
  String buildPlatform,
  String buildType,
  String signatureSHA1,
) async {
  // 等待文件移动完成
  await Future.delayed(const Duration(seconds: 3));
  // dist目录
  final destDir = Directory('dist/$version/$timestamp/$buildPlatform');
  final setupFileName =
      'turing_art-$version-$buildPlatform-$timestamp-$buildType-setup.exe';
  final setupFullFilePath = '${destDir.path}/$setupFileName';

  // 待签名setup.exe是否存在
  if (await File(setupFullFilePath).exists()) {
    final process = await Process.start(
        'win_package_signature.bat', [setupFullFilePath, signatureSHA1]);
    process.stdout
        .transform(utf8.decoder)
        .transform(const LineSplitter())
        .listen((line) => print('[BATCH] $line'));
    process.stderr
        .transform(utf8.decoder)
        .transform(const LineSplitter())
        .listen((line) => print('[ERROR] $line'));
    final exitCode = await process.exitCode;
    if (exitCode != 0) print('❌ 签名setup.exe 失败！');
  } else {
    print('❌ 签名setup.exe 文件不存在: $setupFullFilePath');
  }
}
