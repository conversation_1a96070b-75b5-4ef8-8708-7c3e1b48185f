# turing_art

图灵精修（TuringArt）Flutter客户端(turing_art)，使用Flutter + Unity混合开发，编辑核心页面由Unity开发组成

支持的客户端类型：
+ iPad
+ Android Pad
+ MacOS(Working on it)
+ Windows(Working on it)

## WindowsUnity配置方法
+ 初始化/更新unity_binary子库
  - 初始化（首次拉取子仓库时执行）：git submodule update --init
  - 更新：git submodule update --remote
+ 运行前执行拷贝脚本：`dart prepare_unity.dart './debug_unity_config.json'`

## 上手指南
+ [Git提交前缀规范](https://pinguo.pingcode.com/wiki/spaces/RDC/pages/8Be1W4)
+ [编码规范](https://pinguo.pingcode.com/wiki/spaces/RDC/pages/H5KZdG)
+ [官方架构指南](https://docs.flutter.dev/app-architecture)
+ [分支模型](https://pinguo.pingcode.com/wiki/spaces/RDC/pages/hPkBL_)

## 业务页面术语
+ 启动页 (splash_screen)
+ 登录页面 (login_screen)
+ 项目首页 (project_home_screen)
+ 个人中心页面 (profile_screen)
+ Unity编辑页面 (edit_screen)
+ Mac Unity编辑页面 (mac_edit_screen)
+ Windows Unity编辑页面 (win_edit_screen)
+ 版本过期页面 (expired_screen)
+ H5页面 (h5_screen)

## 数据库升级指南
当前项目中使用了[drift](https://drift.simonbinder.eu/)作为数据库框架，可依照以下步骤进行数据库升级操作：
1. 对数据库表结构进行修改
2. 在 `database.dart` 文件中修改 `schemaVersion`，并执行一下命令，导出当前的 schema 文件
```shell

flutter pub run drift_dev schema dump ./lib/datalayer/service/database/database.dart ./lib/datalayer/service/database/drift_schemas/
```
此时会在 `drift_schemas` 目录下生成一个新的 schema 文件，文件名为 `drift_schema_v2.json`，其中的数字为当前的 schema 版本号
3. 生成升级辅助文件
```shell

// 生成升级辅助文件
flutter pub run drift_dev schema steps ./lib/datalayer/service/database/drift_schemas/ ./lib/datalayer/service/database/schema_versions.dart

// 更新 database.g.dart 文件
flutter pub run build_runner build --delete-conflicting-outputs
```

4. 在 `database.dart` 中，`MigrationStrategy` 中的 `onUpgrade` 方法中添加对应的升级逻辑，参考如下：
```dart
MigrationStrategy get migration {
  return MigrationStrategy(
    onUpgrade: stepByStep(
      from1To2: (m, schema) async {
        await m.addColumn(schema.userEntity, schema.userEntity.creatorMobile);
      },
    ),
  );
}
```