﻿@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

REM 设置标题
title VMProtect 批量加密保护工具

REM 接收参数
set BUILD_TYPE=%~1
set VMP_PROJECT_DIR=%~2
set VMPROTECT_PATH=%~3

REM 设置默认值 - 构建类型
if "%BUILD_TYPE%"=="" (
    set "BUILD_TYPE=Release"
    echo 提示: 未指定构建类型，使用默认值: Release
)

REM 检查构建类型是否有效
if /i not "%BUILD_TYPE%"=="Release" if /i not "%BUILD_TYPE%"=="Debug" (
    echo 错误: 无效的构建类型 "%BUILD_TYPE%"，必须是 Release 或 Debug
    call :show_usage
    exit /b 1
)

REM 设置默认值 - VMP工程目录
set "DEFAULT_VMP_DIR=.\windows\vmprotect_projects"
if "%VMP_PROJECT_DIR%"=="" (
    set "VMP_PROJECT_DIR=%DEFAULT_VMP_DIR%"
    echo 提示: 未指定VMP工程目录，使用默认值: %DEFAULT_VMP_DIR%
)

REM 设置默认值 - VMProtect路径
set "DEFAULT_VMPROTECT_PATH=C:\Program Files\VMProtect Professional\VMProtect_Con.exe"
if "%VMPROTECT_PATH%"=="" (
    set "VMPROTECT_PATH=%DEFAULT_VMPROTECT_PATH%"
    echo 提示: 未指定VMProtect路径，使用默认值: %DEFAULT_VMPROTECT_PATH%
)

REM 根据构建类型设置输出目录
if /i "%BUILD_TYPE%"=="Release" (
    set "OUTPUT_DIR=.\build\windows\x64\runner\Release"
) else (
    set "OUTPUT_DIR=.\build\windows\x64\runner\Debug"
)
echo 提示: 将对目录: %OUTPUT_DIR% 中的相关文件进行加密

REM 只在Debug模式下设置日志文件和加密标记文件
if /i "%BUILD_TYPE%"=="Debug" (
    set "ENCRYPT_MARKER=%OUTPUT_DIR%\vmp_encrypted"
    set "VMP_LOG=%OUTPUT_DIR%\vmp_process.log"
    echo 提示: Debug模式 - 将创建日志文件和加密标记
) else (
    set "ENCRYPT_MARKER="
    set "VMP_LOG=nul"
    echo 提示: Release模式 - 不创建日志文件和加密标记
)

REM 检查输出目录是否存在
if not exist "%OUTPUT_DIR%" (
    echo 错误: 输出目录不存在: %OUTPUT_DIR%
    goto :error
)

REM 初始化日志文件 - 只在Debug模式下执行
if /i "%BUILD_TYPE%"=="Debug" (
    echo VMProtect加密日志 - %DATE% %TIME% > "%VMP_LOG%"
    echo ------------------------------------------ >> "%VMP_LOG%"
)

REM 检查是否已经加密过 - 只在Debug模式下检查
if /i "%BUILD_TYPE%"=="Debug" (
    if exist "%ENCRYPT_MARKER%" (
        echo ❌ 错误: 检测到加密标记文件，当前目录文件可能已经被加密过！
        echo 标记文件: %ENCRYPT_MARKER%
        echo 如需重新加密，请删除此标记文件后再运行脚本。
        goto :error
    )
)

REM 检查VMProtect路径
if not exist "%VMPROTECT_PATH%" (
    echo 错误: 找不到VMProtect程序: %VMPROTECT_PATH%
    echo 请安装VMProtect或指定正确的路径
    goto :error
)

REM 检查VMP工程目录
if not exist "%VMP_PROJECT_DIR%" (
    echo 错误: VMP工程目录不存在: %VMP_PROJECT_DIR%
    goto :error
)

REM 要处理的VMP工程文件列表
set "VMP_FILES=turing_art.exe.vmp UnityPlayer.dll.vmp GameAssembly.dll.vmp"

REM 初始化加密文件列表（用于记录到标记文件中）
set "ENCRYPTED_FILES="

echo ==========================================
echo 🛠 VMProtect 批量加密保护工具
echo ==========================================
echo 构建类型: %BUILD_TYPE%
echo 加密文件所在目录: %OUTPUT_DIR%
echo VMProtect路径: %VMPROTECT_PATH%
echo VMP工程目录: %VMP_PROJECT_DIR%
echo ==========================================

REM 第一步：复制VMP工程文件到输出目录
echo [1] 正在复制VMP工程文件到输出目录...
for %%F in (%VMP_FILES%) do (
    if not exist "%VMP_PROJECT_DIR%\%%F" (
        echo ❌错误: VMP工程文件不存在: %VMP_PROJECT_DIR%\%%F
        goto :error
    ) else (
        echo     复制: %%F
        copy /Y "%VMP_PROJECT_DIR%\%%F" "%OUTPUT_DIR%\" >nul
        if !ERRORLEVEL! NEQ 0 (
            echo ❌错误: 复制文件失败: %%F
            goto :error
        )
    )
)

REM 第二步：加密处理并替换原始文件
echo [2] 加密处理并替换原始文件...
for %%F in (%VMP_FILES%) do (
    if exist "%OUTPUT_DIR%\%%F" (
        REM 获取原始文件名(去掉.vmp后缀)
        set "ORIGINAL_FILE=%%~nF"
        
        REM 确定加密后的文件名
        set "PROTECTED_FILE=!ORIGINAL_FILE!"
        if "%%~xF"==".vmp" (
            if "!ORIGINAL_FILE:~-4!"==".exe" (
                set "PROTECTED_FILE=!ORIGINAL_FILE:~0,-4!.vmp.exe"
            ) else if "!ORIGINAL_FILE:~-4!"==".dll" (
                set "PROTECTED_FILE=!ORIGINAL_FILE:~0,-4!.vmp.dll"
            )
        )
        
        echo ------------------------------------------
        echo     🚀 处理: %%F
        echo     对应原始文件: !ORIGINAL_FILE!
        echo     预期加密文件: !PROTECTED_FILE!
        
        REM 执行VMProtect加密 - 仅将VMProtect的输出重定向到日志文件
        echo     正在调用VMProtect...
        
        REM 在日志文件中记录当前处理的文件信息 - 仅Debug模式
        if /i "%BUILD_TYPE%"=="Debug" (
            echo. >> "%VMP_LOG%"
            echo 处理文件: %%F >> "%VMP_LOG%"
            echo 时间: %DATE% %TIME% >> "%VMP_LOG%"
            echo ------------------------------------------ >> "%VMP_LOG%"
        )
        
        REM 根据模式设置输出重定向
        "%VMPROTECT_PATH%" "%OUTPUT_DIR%\%%F" >> "%VMP_LOG%" 2>&1
        set VMP_ERROR=!ERRORLEVEL!
        
        if !VMP_ERROR! NEQ 0 (
            echo ❌ 错误: VMProtect处理失败: %%F，错误代码: !VMP_ERROR!
            goto :error
        )
        
        REM 检查加密后的文件是否存在
        if not exist "%OUTPUT_DIR%\!PROTECTED_FILE!" (
            echo ❌ 错误: 加密后的文件不存在: %OUTPUT_DIR%\!PROTECTED_FILE!
            goto :error
        )
        
        REM 删除VMP工程文件
        del /f /q "%OUTPUT_DIR%\%%F" >nul
        if !ERRORLEVEL! NEQ 0 (
            echo ❌ 错误: 删除VMP工程文件失败: %OUTPUT_DIR%\%%F
            goto :error
        )
        
        REM 替换原始文件
        echo     替换: !ORIGINAL_FILE! 使用 !PROTECTED_FILE!
        
        REM 删除原始文件
        del /f /q "%OUTPUT_DIR%\!ORIGINAL_FILE!" >nul
        if !ERRORLEVEL! NEQ 0 (
            echo ❌ 错误: 无法删除原始文件: %OUTPUT_DIR%\!ORIGINAL_FILE!
            goto :error
        )
        
        REM 重命名加密后的文件
        ren "%OUTPUT_DIR%\!PROTECTED_FILE!" "!ORIGINAL_FILE!" >nul
        if !ERRORLEVEL! NEQ 0 (
            echo ❌ 错误: 重命名文件失败: %OUTPUT_DIR%\!PROTECTED_FILE! 到 !ORIGINAL_FILE!
            goto :error
        ) else (
            echo ✅ 成功: 文件已加密并替换原始文件
            
            REM 收集加密文件名到列表（用于标记文件）- 仅影响Debug模式
            set "ENCRYPTED_FILES=!ENCRYPTED_FILES! !ORIGINAL_FILE!"
        )
    )
)

REM 第三步：创建加密标记文件 - 仅Debug模式
if /i "%BUILD_TYPE%"=="Debug" (
    echo [3] 创建加密标记文件...
    echo VMProtect加密于: %DATE% %TIME% > "%ENCRYPT_MARKER%"
    echo 加密文件列表: >> "%ENCRYPT_MARKER%"
    for %%F in (%ENCRYPTED_FILES%) do (
        echo - %%F >> "%ENCRYPT_MARKER%"
    )

    if !ERRORLEVEL! NEQ 0 (
        echo ❌ 警告: 无法创建加密标记文件，但加密过程已完成
    ) else (
        echo ✅ 成功: 已创建加密标记文件: %ENCRYPT_MARKER%
    )
) else (
    echo [3] Release模式 - 跳过创建加密标记文件
)

echo ==========================================
echo 🎉 VMProtect 批量加密保护完成！
echo ==========================================

exit /b 0

REM 错误处理标签
:error
echo ==========================================
echo ❌ VMProtect 批量加密异常！
echo ==========================================

exit /b 1

REM 显示使用说明的函数
:show_usage
echo.
echo 用法: win_vmprotect_encryption.bat [构建类型] [VMP工程目录] [VMProtect路径]
echo.
echo 参数:
echo   [构建类型]     - Release或Debug (默认: Release)
echo   [VMP工程目录]  - VMP工程文件所在目录 (默认: .\windows\vmprotect_projects)
echo   [VMProtect路径] - VMProtect_Con.exe的完整路径 
echo                   (默认: C:\Program Files\VMProtect Professional\VMProtect_Con.exe)
echo.
echo 示例:
echo   win_vmprotect_encryption.bat
echo   win_vmprotect_encryption.bat Release
echo   win_vmprotect_encryption.bat Debug .\my_projects C:\VMProtect\VMProtect_Con.exe
echo.
exit /b 