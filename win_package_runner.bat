@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

REM ==========================================
REM Windows Flutter 应用打包脚本
REM ==========================================

REM 接收Dart脚本传入参数
set BUILD_VERSION=%~1
set BUILD_TIMESTAMP=%~2
set BUILD_PLATFORM=%~3
set BUILD_TYPE=%~4
set DO_ENCRYPT=%~5
set DO_SIGN=%~6
set SIGNATURE_SHA1=%~7

REM 参数校验
if "%BUILD_VERSION%"=="" (
    echo 错误: 缺少版本参数
    exit /b 1
)
if "%BUILD_PLATFORM%"=="" (
    echo 错误: 缺少构建类型参数
    exit /b 1
)

REM 设置路径变量
set "OUTPUT_ROOT=dist\%BUILD_VERSION%\%BUILD_TIMESTAMP%"
set "EXE_NAME=turing_art-%BUILD_VERSION%-%BUILD_PLATFORM%-windows-setup.exe"
set PATH_APPLICATION_EXE="build\windows\x64\runner\Release\turing_art.exe"
set PATH_SETUP_EXE="dist\%BUILD_VERSION%\%BUILD_TIMESTAMP%\%BUILD_PLATFORM%\turing_art-%BUILD_VERSION%-%BUILD_TIMESTAMP%-%BUILD_TYPE%-setup.exe"
set "RELEASE_PATH=build\windows\x64\runner\Release"
set "BACKUP_PATH=build\windows\x64\runner\ReleaseCopy"

REM 根据构建类型选择配置文件
set "CONFIG_PATH=package_unity_config.json"

REM 根据构建类型设置IS_DEBUG变量
if "%BUILD_TYPE%" == "debug" (
    set "IS_DEBUG=true"
) else (
    set "IS_DEBUG=false"
)

REM 检查必要的命令
where flutter >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: Flutter 未安装或未添加到系统路径
    exit /b 1
)

echo ==========================================
echo 开始构建 %BUILD_PLATFORM% 版本
echo ==========================================

@REM 现在抠图SDK区分了系统版本，先注释使用上一次的构建结果
@REM REM 如果ReleaseCopy存在，直接使用
@REM if exist "%BACKUP_PATH%" (
@REM     echo Release备份文件夹已存在，直接使用...
    
@REM     REM 将ReleaseCopy内容拷贝回Release文件夹
@REM     echo 将ReleaseCopy内容拷贝回Release文件夹...
@REM     if exist "%RELEASE_PATH%" (
@REM         echo 删除现有Release目录...
@REM         rmdir /s /q "%RELEASE_PATH%"
@REM         if %ERRORLEVEL% NEQ 0 (
@REM             echo Warning: 删除现有Release目录失败，继续执行...
@REM         )
@REM     )
    
@REM     REM 创建Release目录并复制文件
@REM     echo 从ReleaseCopy复制文件到Release文件夹...
@REM     xcopy "%BACKUP_PATH%\*" "%RELEASE_PATH%\" /E /I /H /Y
@REM     if %ERRORLEVEL% NEQ 0 (
@REM         echo Error: ReleaseCopy到Release文件夹复制失败
@REM         exit /b %ERRORLEVEL%
@REM     ) else (
@REM         echo ReleaseCopy到Release文件夹复制成功: %RELEASE_PATH%
@REM     )
    
@REM     goto :PREPARE_UNITY
@REM )

echo [1] 清理Flutter构建目录...
call flutter clean
if %ERRORLEVEL% NEQ 0 (
    echo Error: Flutter clean 失败
    exit /b %ERRORLEVEL%
)

echo [2] 安装依赖...
call flutter pub get
if %ERRORLEVEL% NEQ 0 (
    echo Error: 依赖安装失败
    exit /b %ERRORLEVEL%
)

echo [3] Flutter打包...
call flutter build windows --release --dart-define=IS_DEBUG_VALUE=%IS_DEBUG%
if %ERRORLEVEL% NEQ 0 (
    echo Error: Flutter构建失败
    exit /b %ERRORLEVEL%
)

echo [4] 备份Release文件夹到ReleaseCopy...
REM 如果备份目录存在，先删除
if exist "%BACKUP_PATH%" (
    echo 删除现有备份目录...
    rmdir /s /q "%BACKUP_PATH%"
    if %ERRORLEVEL% NEQ 0 (
        echo Warning: 删除现有备份目录失败，继续执行...
    )
)

REM 创建备份目录并复制文件
echo 创建备份目录并复制Release文件夹内容...
xcopy "%RELEASE_PATH%\*" "%BACKUP_PATH%\" /E /I /H /Y
if %ERRORLEVEL% NEQ 0 (
    echo Error: Release文件夹备份失败
    exit /b %ERRORLEVEL%
) else (
    echo Release文件夹备份成功: %BACKUP_PATH%
)

:PREPARE_UNITY
echo [5] 准备Unity资源...
call dart prepare_unity.dart "%~dp0%CONFIG_PATH%"
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

echo [6] 拷贝dll资源...
call dart windows/packaging/copy_dll.dart
if %ERRORLEVEL% NEQ 0 (
    echo Error: dll资源拷贝失败
    exit /b %ERRORLEVEL%
)

echo [7] 加密可执行文件...
if "%DO_ENCRYPT%"=="true" (
    if "%BUILD_TYPE%" == "debug" (
        call win_vmprotect_encryption Debug
    ) else (
        call win_vmprotect_encryption Release
    )
    if %ERRORLEVEL% NEQ 0 (
        echo Error: 可执行文件加密失败
        exit /b %ERRORLEVEL%
    )
) else (
    echo 跳过加密步骤
)

echo [8] 签名应用程序exe文件...
if "%DO_SIGN%"=="true" (
    call win_package_signature.bat %PATH_APPLICATION_EXE% %SIGNATURE_SHA1%
    if %ERRORLEVEL% NEQ 0 (
        echo 错误: 应用程序签名失败
        exit /b %ERRORLEVEL%
    )
) else (
    echo 跳过签名步骤
)

echo [9] 构建NSIS安装包...
call .\windows\packaging\exe\generate_nsis_setup.bat
if %ERRORLEVEL% NEQ 0 (
    echo Error: 打包失败
    exit /b %ERRORLEVEL%
)

echo ==========================================
echo %BUILD_PLATFORM% 版本构建成功！
echo ==========================================
exit /b 0