# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: ./pg_flutter_lints_rules/analysis_options.yaml
analyzer:
  exclude:
    - "build/**"
    - "**/*.g.dart" # 忽略自动生成的代码
    - "**/*.freezed.dart"
    - "lib/generated_plugin_registrant.dart"
