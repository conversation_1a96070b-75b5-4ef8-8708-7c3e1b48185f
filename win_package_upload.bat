@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ==========================================
echo Windows Application Upload Process Started
echo ==========================================

set BUILD_VERSION=%~1
set BUILD_TIMESTAMP=%~2
set BUILD_PLATFORM=%~3
set BUILD_TYPE=%~4
set CURRENT_BRANCH=%~5
set EXE_NAME=%~6


if "%BUILD_VERSION%"=="" (
    echo Error: Missing version parameter
    exit /b 1
)
if "%BUILD_TIMESTAMP%"=="" (
    echo Error: Missing timestamp parameter
    exit /b 1
)
if "%BUILD_PLATFORM%"=="" (
    echo Error: Missing build platform parameter
    exit /b 1
)
if "%BUILD_TYPE%"=="" (
    echo Error: Missing build type parameter
    exit /b 1
)
if "%CURRENT_BRANCH%"=="" (
    echo Warning: Missing branch parameter, using "unknown" as branch name
    set CURRENT_BRANCH=unknown
)
if "%EXE_NAME%"=="" (
    echo Error: Missing exe name parameter
    exit /b 1
)

set "SOURCE_DIR=%~dp0dist\%BUILD_VERSION%\%BUILD_TIMESTAMP%\%BUILD_PLATFORM%"
set "SOURCE_PATH=%SOURCE_DIR%\%EXE_NAME%"

echo [1/4] Checking if file exists...
if not exist "%SOURCE_PATH%" (
    echo Error: EXE file does not exist at "%SOURCE_PATH%"
    exit /b 1
)

echo [2/4] Reading NAS configuration...
set "NAS_CONFIG_FILE=%~dp0nas_config.json"
if not exist "%NAS_CONFIG_FILE%" (
    echo Error: NAS configuration file not found at "%NAS_CONFIG_FILE%"
    exit /b 1
)

for /f "tokens=2 delims=:, " %%a in ('findstr "nas_ip" "%NAS_CONFIG_FILE%"') do set "NAS_IP=%%~a"
for /f "tokens=2 delims=:, " %%a in ('findstr "share_name" "%NAS_CONFIG_FILE%"') do set "SHARE_NAME=%%~a"
for /f "tokens=2 delims=:, " %%a in ('findstr "nas_user" "%NAS_CONFIG_FILE%"') do set "NAS_USER=%%~a"
for /f "tokens=2 delims=:, " %%a in ('findstr "nas_password" "%NAS_CONFIG_FILE%"') do set "NAS_PASSWORD=%%~a"
for /f "tokens=2 delims=:, " %%a in ('findstr "%BUILD_PLATFORM%_nas_id" "%NAS_CONFIG_FILE%"') do set "CONFIG_NAS_ID=%%~a"

set "NAS_IP=!NAS_IP:"=!"
set "SHARE_NAME=!SHARE_NAME:"=!"
set "NAS_USER=!NAS_USER:"=!"
set "NAS_PASSWORD=!NAS_PASSWORD:"=!"
set "CONFIG_NAS_ID=!CONFIG_NAS_ID:"=!"

echo CONFIG_NAS_ID: !CONFIG_NAS_ID!
if "!CONFIG_NAS_ID!"=="" (
    echo Error: No valid NAS_ID found, some functions may not work properly
)

echo NAS IP: !NAS_IP!
echo Share Name: !SHARE_NAME!

echo [3/4] Connecting to NAS share...
REM delete the old connection if it exists
net use \\!NAS_IP!\!SHARE_NAME! /delete /y >nul 2>&1

REM create a new network connection and provide credentials
echo connecting to NAS share...
net use \\!NAS_IP!\!SHARE_NAME! /user:!NAS_USER! !NAS_PASSWORD! /persistent:no
if %errorlevel% neq 0 (
    echo error: cannot connect to NAS share, please check username and password
    exit /b 1
)

echo [4/4] Copying file via SMB share...
if "%BUILD_PLATFORM%"=="win7" (
    set "TARGET_DIR=\\!NAS_IP!\!SHARE_NAME!\TuringArtPackages_win7"
) else (
    set "TARGET_DIR=\\!NAS_IP!\!SHARE_NAME!\TuringArtPackages"
)
set "JSON_DIR=%~dp0dist"

if not exist "%TARGET_DIR%" (
    echo error: target directory %TARGET_DIR% does not exist
    net use \\!NAS_IP! /delete >nul
    exit /b 1
)

robocopy "%SOURCE_DIR%" "%TARGET_DIR%" "%EXE_NAME%" ^
    /NJH /NJS /NDL /NC /NS /R:3 /W:5 /V /IS /IT /NP ^
    /LOG:copy.log /TEE

if %errorlevel% geq 8 (
    echo [ERROR] File transfer failed, error code: %errorlevel%
    echo Possible reasons:
    echo 1. Source file path: %SOURCE_PATH%
    echo 2. Target path permissions: %TARGET_DIR%
    type copy.log
    exit /b 1
) else (
    echo [SUCCESS] File synchronized to: %TARGET_DIR%
)



REM clean up
echo disconnecting NAS...
net use \\!NAS_IP!\!SHARE_NAME! /delete >nul 2>&1
del signature_output.txt

echo ==========================================
echo Upload process completed!
echo ==========================================
exit /b 0
