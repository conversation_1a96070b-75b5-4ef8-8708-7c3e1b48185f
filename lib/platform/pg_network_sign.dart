import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_log.dart';

@Deprecated("此签名类已废弃，请使用Ops Sdk中的PgNetworkSign")
class PGNetworkSign {
  static const MethodChannel _channel = MethodChannel('com.turingart.pgsign');

  static Future<String> getSign({
    required String host,
    required String path,
    required String method,
    required Map<String, String> headers,
    required Map<String, dynamic> params,
    required bool forBody,
    bool isForH5 = false,
  }) async {
    try {
      final result = await _channel.invokeMethod('getSign', {
        'host': host,
        'path': path,
        'method': method,
        'headers': headers,
        'params': params,
        'forBody': forBody
      });
      return result.toString();
    } on PlatformException catch (e) {
      PGLog.d('Failed to get sign: ${e.message}');
      return '';
    }
  }
}
