# FFI 模块

本目录包含所有 Flutter FFI (Foreign Function Interface) 相关的代码，用于与原生 C/C++ 动态库进行交互。

## 目录结构

```
lib/ffi/
├── ffi.dart               # 统一导出文件
├── models/                # 数据模型定义
│   ├── image_processor_model.dart     # 图像处理模型
│   ├── raw_conversion_model.dart      # Raw转换模型
│   ├── raw_decoder_model.dart         # Raw解码器模型
│   └── salient_matters_model.dart     # 主体抠图模型
├── native/                # FFI 绑定层
│   ├── image_processor_bindings.dart  # 图像处理FFI绑定
│   ├── platform_loader.dart           # 平台动态库加载器
│   ├── raw_conversion_bindings.dart   # Raw转换FFI绑定
│   ├── raw_decoder_bindings.dart      # Raw解码器FFI绑定
│   └── salient_matters_bindings.dart  # 主体抠图FFI绑定
├── services/              # 服务层封装
│   ├── image_processor_service.dart   # 图像处理服务
│   ├── raw_conversion_service.dart    # Raw转换服务
│   ├── raw_decoder_service.dart       # Raw解码器服务
│   └── salient_matters_service.dart   # 主体抠图服务
├── docs/                  # FFI 模块文档
│   ├── RAW_CONVERSION.md          # Raw转换使用指南
│   ├── SALIENT_MATTERS.md         # SalientMatters使用指南
│   └── RAW_DECODER.md             # Raw解码器使用指南
└── README.md              # 本文件
```

## 支持的功能

### 1. 图像处理 (Image Processor)
- **绑定层**: `native/image_processor_bindings.dart`
- **服务层**: `services/image_processor_service.dart`
- **模型**: `models/image_processor_model.dart`
- **功能**: 图像解码、编码、缩放、格式转换
- **动态库**: `PGImageProcessorLib.dll` (Windows)

### 2. Raw 图像转换 (Raw Conversion)
- **绑定层**: `native/raw_conversion_bindings.dart`
- **服务层**: `services/raw_conversion_service.dart`
- **模型**: `models/raw_conversion_model.dart`
- **文档**: `docs/RAW_CONVERSION.md`
- **功能**: Raw图像转换和调色处理
- **动态库**: `PGRawConversionLib.dll` (Windows)

### 3. Raw 解码器 (Raw Decoder)
- **绑定层**: `native/raw_decoder_bindings.dart`
- **服务层**: `services/raw_decoder_service.dart`
- **模型**: `models/raw_decoder_model.dart`
- **文档**: `docs/RAW_DECODER.md`
- **功能**: Raw文件解码
- **动态库**: `PGRawDecoderLib.dll` (Windows)

### 4. 主体抠图 (Salient Matters)
- **绑定层**: `native/salient_matters_bindings.dart`
- **服务层**: `services/salient_matters_service.dart`
- **模型**: `models/salient_matters_model.dart`
- **文档**: `docs/SALIENT_MATTERS.md`
- **功能**: AI主体抠图、交互式抠图、蒙版跟踪
- **动态库**: `PGSalientMattersLib.dll` (Windows)

## 使用方式

### 导入模块

```dart
// 统一导入所有FFI模块
import 'package:turing_art/ffi/ffi.dart';

// 或者单独导入需要的模块
import 'package:turing_art/ffi/services/image_processor_service.dart';
import 'package:turing_art/ffi/services/raw_conversion_service.dart';
import 'package:turing_art/ffi/services/raw_decoder_service.dart';
import 'package:turing_art/ffi/services/salient_matters_service.dart';
```

### 快速开始

```dart
// 图像处理示例
await ImageProcessorService.initialize();
await ImageProcessorService.processFile(
  'input.jpg', 
  'output.jpg',
  config: ImageProcessorConfig.thumbnailConfig,
);

// Raw解码示例
await RawDecoderService.initialize();
await RawDecoderService.processFile('input.raw', 'output.jpg');

// 主体抠图示例
final service = SalientMattersService();
await service.initializeFromConfig();
final result = await service.matting(imageData);
```

详细文档请参考：
- **Raw转换**: 查看 `docs/RAW_CONVERSION.md`
- **主体抠图**: 查看 `docs/SALIENT_MATTERS.md`  
- **Raw解码器**: 查看 `docs/RAW_DECODER.md`

## 平台支持

目前所有FFI模块仅支持Windows平台，动态库文件应放置在应用程序的可执行文件目录中。

### 动态库文件

- `PGImageProcessorLib.dll` - 图像处理库
- `PGRawConversionLib.dll` - Raw转换库
- `PGRawDecoderLib.dll` - Raw解码器库
- `PGSalientMattersLib.dll` - 主体抠图库

## 注意事项

1. **内存管理**: 所有FFI调用都需要手动管理内存，请确保正确释放资源
2. **错误处理**: 每个模块都提供了完整的错误码和错误信息
3. **线程安全**: FFI调用不是线程安全的，请在主线程中使用
4. **授权验证**: 某些功能需要有效的许可证密钥

## 架构设计

FFI模块采用三层架构设计：

- **模型层(Models)**: 定义数据结构、错误码和配置类
- **绑定层(Native)**: 直接与C/C++动态库交互的FFI绑定
- **服务层(Services)**: 提供高级API封装，处理业务逻辑和错误处理

## 开发指南

### 添加新的FFI模块

1. 在 `models/` 目录下创建数据模型文件
2. 在 `native/` 目录下创建FFI绑定文件
3. 在 `services/` 目录下创建服务封装文件
4. 在 `ffi.dart` 中添加导出语句
5. 在 `docs/` 目录下添加相应的文档
6. 更新本文档的目录结构和功能列表

### 调试建议

1. 启用详细日志输出 
2. 使用相应的测试页面进行交互式调试
3. 检查动态库文件是否正确加载
4. 验证许可证配置是否正确
5. 检查服务层初始化状态

## 相关文件

- **配置文件**: `lib/config/ai_sdk_config.dart`
- **统一导出**: `lib/ffi/ffi.dart` 