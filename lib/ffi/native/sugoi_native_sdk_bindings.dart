// SugoiNativeSDK FFI绑定文件
// 用于加载和调用SugoiNativeSDK.dll中的函数

import 'dart:ffi';
import 'dart:io';

import '../../../utils/pg_log.dart';

/// SDK_Env_Platform函数的FFI签名定义
/// 返回值类型根据实际情况调整（这里假设返回int）
typedef SdkEnvPlatformNative = Int32 Function();
typedef SdkEnvPlatformDart = int Function();

/// SugoiNativeSDK FFI绑定类
class SugoiNativeSDKBindings {
  static DynamicLibrary? _library;
  static SdkEnvPlatformDart? _sdkEnvPlatform;

  /// 初始化SDK绑定
  /// 加载SugoiNativeSDK.dll并绑定函数
  static bool initialize() {
    try {
      // 尝试从指定路径加载DLL
      final dllPath = _getSugoiNativeSDKPath();
      if (dllPath != null && File(dllPath).existsSync()) {
        _library = DynamicLibrary.open(dllPath);
        PGLog.d('成功从指定路径加载SugoiNativeSDK: $dllPath');
      } else {
        // 如果指定路径不存在，尝试从系统路径加载
        _library = DynamicLibrary.open('SugoiNativeSDK.dll');
        PGLog.d('从系统路径加载SugoiNativeSDK.dll');
      }

      // 绑定SDK_Env_Platform函数
      _sdkEnvPlatform = _library!
          .lookup<NativeFunction<SdkEnvPlatformNative>>('SDK_Env_Platform')
          .asFunction<SdkEnvPlatformDart>();

      PGLog.d('SugoiNativeSDK绑定初始化成功');
      return true;
    } catch (e) {
      PGLog.d('SugoiNativeSDK绑定初始化失败: $e');
      return false;
    }
  }

  /// 获取SugoiNativeSDK.dll的路径
  /// 根据您提供的路径构建完整路径
  static String? _getSugoiNativeSDKPath() {
    try {
      // 获取可执行文件目录
      final executableDir = File(Platform.resolvedExecutable).parent.path;

      // 构建DLL路径：可执行文件目录/turing_art_Data/Plugins/x86_64/SugoiNativeSDK.dll
      final dllPath =
          '$executableDir\\turing_art_Data\\Plugins\\x86_64\\SugoiNativeSDK.dll';

      PGLog.d('尝试加载SugoiNativeSDK路径: $dllPath');
      return dllPath;
    } catch (e) {
      PGLog.d('获取SugoiNativeSDK路径失败: $e');
      return null;
    }
  }

  /// 调用SDK_Env_Platform函数
  /// 返回平台环境信息
  static int? getSdkEnvPlatform() {
    if (_sdkEnvPlatform == null) {
      PGLog.d('SugoiNativeSDK未初始化，请先调用initialize()');
      return null;
    }

    try {
      final result = _sdkEnvPlatform!();
      PGLog.d('SDK_Env_Platform返回值: $result');
      return result;
    } catch (e) {
      PGLog.d('调用SDK_Env_Platform失败: $e');
      return null;
    }
  }

  /// 检查SDK是否已初始化
  static bool get isInitialized => _library != null && _sdkEnvPlatform != null;

  /// 清理资源
  static void dispose() {
    _library = null;
    _sdkEnvPlatform = null;
    PGLog.d('SugoiNativeSDK绑定已清理');
  }
}
