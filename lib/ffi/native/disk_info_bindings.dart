import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:turing_art/ffi/native/universal_platform_loader.dart';
import 'package:turing_art/utils/pg_log.dart';

// FFI函数签名定义
typedef GetDiskSpaceNative = Bool Function(
  Pointer<Utf8> path,
  Pointer<Uint64> totalSpace,
  Pointer<Uint64> freeSpace,
);

typedef GetDiskSpaceDart = bool Function(
  Pointer<Utf8> path,
  Pointer<Uint64> totalSpace,
  Pointer<Uint64> freeSpace,
);

/// DiskInfo FFI绑定类
/// 提供获取磁盘空间信息的native接口
class DiskInfoBindings {
  static DynamicLibrary? _library;
  static GetDiskSpaceDart? _getDiskSpace;
  static bool _initialized = false;

  /// 初始化FFI绑定
  static bool initialize() {
    if (_initialized) return true;

    try {
      // 使用通用平台加载器加载库
      _library = UniversalPlatformLoader.loadLibrary(
        'PGDiskInfo',
        subDirectory: 'disk_info',
      );

      // 查找函数
      _getDiskSpace =
          _library!.lookupFunction<GetDiskSpaceNative, GetDiskSpaceDart>(
        'get_disk_space',
      );

      _initialized = true;
      PGLog.i('DiskInfoBindings initialized successfully');
      return true;
    } catch (e) {
      PGLog.e('Failed to initialize DiskInfoBindings: $e');
      return false;
    }
  }

  /// 检查是否已初始化且可用
  static bool get isAvailable => _initialized && _getDiskSpace != null;

  /// 获取磁盘空间信息
  ///
  /// [path] 磁盘路径，例如 "C:\\" (Windows) 或 "/" (Linux/macOS)
  /// 返回 DiskSpaceInfo 对象，包含总空间和可用空间
  /// 失败时返回 null
  static DiskSpaceInfo? getDiskSpace(String path) {
    if (!isAvailable) {
      PGLog.w('DiskInfoBindings not initialized');
      return null;
    }

    final pathPtr = path.toNativeUtf8();
    final totalSpacePtr = calloc<Uint64>();
    final freeSpacePtr = calloc<Uint64>();

    try {
      final success = _getDiskSpace!(pathPtr, totalSpacePtr, freeSpacePtr);

      if (success) {
        final totalSpace = totalSpacePtr.value;
        final freeSpace = freeSpacePtr.value;

        return DiskSpaceInfo(
          totalSpace: totalSpace,
          freeSpace: freeSpace,
          usedSpace: totalSpace - freeSpace,
        );
      } else {
        PGLog.e('get_disk_space failed for path: $path');
        return null;
      }
    } catch (e) {
      PGLog.e('Error calling get_disk_space: $e');
      return null;
    } finally {
      // 释放内存
      calloc.free(pathPtr);
      calloc.free(totalSpacePtr);
      calloc.free(freeSpacePtr);
    }
  }

  /// 清理资源
  static void dispose() {
    _library = null;
    _getDiskSpace = null;
    _initialized = false;
  }
}

/// 磁盘空间信息数据类
class DiskSpaceInfo {
  /// 总空间（字节）
  final int totalSpace;

  /// 可用空间（字节）
  final int freeSpace;

  /// 已使用空间（字节）
  final int usedSpace;

  const DiskSpaceInfo({
    required this.totalSpace,
    required this.freeSpace,
    required this.usedSpace,
  });

  /// 可用空间百分比 (0.0 - 1.0)
  double get freeSpacePercentage =>
      totalSpace > 0 ? freeSpace / totalSpace : 0.0;

  /// 已使用空间百分比 (0.0 - 1.0)
  double get usedSpacePercentage =>
      totalSpace > 0 ? usedSpace / totalSpace : 0.0;

  /// 格式化显示总空间
  String get formattedTotalSpace => _formatBytes(totalSpace);

  /// 格式化显示可用空间
  String get formattedFreeSpace => _formatBytes(freeSpace);

  /// 格式化显示已使用空间
  String get formattedUsedSpace => _formatBytes(usedSpace);

  /// 将字节数格式化为人类可读的字符串
  static String _formatBytes(int bytes) {
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var value = bytes.toDouble();
    var suffixIndex = 0;

    while (value >= 1024 && suffixIndex < suffixes.length - 1) {
      value /= 1024;
      suffixIndex++;
    }

    return '${value.toStringAsFixed(suffixIndex == 0 ? 0 : 2)} ${suffixes[suffixIndex]}';
  }

  @override
  String toString() {
    return 'DiskSpaceInfo(total: $formattedTotalSpace, free: $formattedFreeSpace, used: $formattedUsedSpace)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DiskSpaceInfo &&
        other.totalSpace == totalSpace &&
        other.freeSpace == freeSpace &&
        other.usedSpace == usedSpace;
  }

  @override
  int get hashCode => Object.hash(totalSpace, freeSpace, usedSpace);
}
