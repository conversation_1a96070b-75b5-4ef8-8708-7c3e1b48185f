import 'dart:ffi';
import 'dart:typed_data';

import 'package:ffi/ffi.dart';
import 'package:turing_art/ffi/models/raw_conversion_model.dart';
import 'package:turing_art/ffi/native/universal_platform_loader.dart';

/// FFI接口类
class RawConversionBindings {
  static DynamicLibrary? _lib;
  static bool _initialized = false;

  // 函数指针
  static late final Pointer<Utf8> Function() _getVersion;
  static late final Pointer<Void> Function() _create;
  static late final int Function(
      Pointer<Void>, Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>) _init;
  static late final int Function(
      Pointer<Void>,
      Pointer<RawConversionConfig>,
      Pointer<Uint8>,
      int,
      int,
      int,
      Pointer<Uint8>,
      Pointer<Int32>,
      int) _process;
  static late final int Function(Pointer<Void>, Pointer<RawConversionConfig>,
      Pointer<Utf8>, Pointer<Utf8>, Pointer<Int32>, int) _processFile;
  static late final int Function(Pointer<Void>) _clear;
  static late final Pointer<Utf8> Function(int) _getErrorString;
  static late final int Function(Pointer<Utf8>) _isSupportedFormat;

  static Pointer<Void>? _handle;

  static bool _loadLibrary() {
    if (_lib != null) {
      return true;
    }

    try {
      _lib = UniversalPlatformLoader.loadLibrary(
        'PGRawConversion',
        subDirectory: 'raw_conversion',
      );

      // 绑定函数
      _bindFunctions();

      return true;
    } catch (e) {
      _lib = null;
      return false;
    }
  }

  /// 绑定所有函数
  static void _bindFunctions() {
    _getVersion = _lib!
        .lookup<NativeFunction<Pointer<Utf8> Function()>>(
            'raw_conversion_get_version')
        .asFunction();

    _create = _lib!
        .lookup<NativeFunction<Pointer<Void> Function()>>(
            'raw_conversion_create')
        .asFunction();

    _init = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(Pointer<Void>, Pointer<Utf8>, Pointer<Utf8>,
                    Pointer<Utf8>)>>('raw_conversion_init')
        .asFunction();

    _process = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<Void>,
                    Pointer<RawConversionConfig>,
                    Pointer<Uint8>,
                    Int32,
                    Int32,
                    Int32,
                    Pointer<Uint8>,
                    Pointer<Int32>,
                    Int32)>>('raw_conversion_process')
        .asFunction();

    _processFile = _lib!
        .lookup<
            NativeFunction<
                Int32 Function(
                    Pointer<Void>,
                    Pointer<RawConversionConfig>,
                    Pointer<Utf8>,
                    Pointer<Utf8>,
                    Pointer<Int32>,
                    Int32)>>('raw_conversion_process_file')
        .asFunction();

    _clear = _lib!
        .lookup<NativeFunction<Int32 Function(Pointer<Void>)>>(
            'raw_conversion_clear')
        .asFunction();

    _getErrorString = _lib!
        .lookup<NativeFunction<Pointer<Utf8> Function(Int32)>>(
            'raw_conversion_get_error_string')
        .asFunction();

    _isSupportedFormat = _lib!
        .lookup<NativeFunction<Int32 Function(Pointer<Utf8>)>>(
            'raw_conversion_is_supported_format')
        .asFunction();
  }

  static String getVersion() {
    if (!_loadLibrary()) {
      return 'Unknown';
    }
    try {
      final versionPtr = _getVersion();
      return versionPtr.toDartString();
    } catch (e) {
      return 'Unknown';
    }
  }

  static RawConversionResult initialize({
    String? key,
    String? userCode,
    String? prodCode,
  }) {
    if (!_loadLibrary()) {
      return const RawConversionResult(
        errorCode: RawConversionErrorCode.initFailed,
        errorMessage: '无法加载动态库',
      );
    }

    try {
      // 创建句柄
      _handle = _create();
      if (_handle == nullptr) {
        return const RawConversionResult(
          errorCode: RawConversionErrorCode.initFailed,
          errorMessage: '创建句柄失败',
        );
      }

      // 准备参数
      final keyPtr = key?.toNativeUtf8() ?? ''.toNativeUtf8();
      final userCodePtr = userCode?.toNativeUtf8() ?? ''.toNativeUtf8();
      final prodCodePtr = prodCode?.toNativeUtf8() ?? ''.toNativeUtf8();

      // 初始化（新的API不再需要ONNX配置）
      final result = _init(_handle!, keyPtr, userCodePtr, prodCodePtr);

      // 清理资源
      malloc.free(keyPtr);
      malloc.free(userCodePtr);
      malloc.free(prodCodePtr);

      final errorCode = RawConversionErrorCode.fromValue(result);
      _initialized = errorCode == RawConversionErrorCode.success;

      return RawConversionResult(
        errorCode: errorCode,
        errorMessage: errorCode != RawConversionErrorCode.success
            ? _getErrorMessage(result)
            : null,
      );
    } catch (e) {
      return RawConversionResult(
        errorCode: RawConversionErrorCode.initFailed,
        errorMessage: '初始化异常: $e',
      );
    }
  }

  static RawConversionResult processFile(
    String inputPath,
    String outputPath, {
    RawConversionConfigData? config,
    bool adjustCustom = false,
  }) {
    if (!_initialized || _handle == nullptr) {
      return const RawConversionResult(
        errorCode: RawConversionErrorCode.initFailed,
        errorMessage: '未初始化',
      );
    }

    try {
      final inputPathPtr = inputPath.toNativeUtf8();
      final outputPathPtr = outputPath.toNativeUtf8();

      // 创建配置结构体
      final configPtr = malloc<RawConversionConfig>();
      final configData = config ?? RawConversionConfigData.defaultConfig;

      configPtr.ref.enableRawLut = configData.enableRawLut ? 1 : 0;
      configPtr.ref.enableDenoise = configData.enableDenoise ? 1 : 0;
      configPtr.ref.enableStdLut = configData.enableStdLut ? 1 : 0;
      configPtr.ref.adjustType = configData.adjustType.value;
      configPtr.ref.threads = configData.threads;
      configPtr.ref.strength = configData.strength;

      final adjustModePtr = malloc<Int32>();
      adjustModePtr.value = 0;

      // 调用新的API（参数顺序已更新）
      final result = _processFile(
        _handle!,
        configPtr,
        inputPathPtr,
        outputPathPtr,
        adjustModePtr,
        adjustCustom ? 1 : 0,
      );

      final adjustMode = adjustModePtr.value;

      // 清理资源
      malloc.free(configPtr);
      malloc.free(inputPathPtr);
      malloc.free(outputPathPtr);
      malloc.free(adjustModePtr);

      final errorCode = RawConversionErrorCode.fromValue(result);

      return RawConversionResult(
        errorCode: errorCode,
        errorMessage: errorCode != RawConversionErrorCode.success
            ? _getErrorMessage(result)
            : null,
        adjustMode: adjustMode,
      );
    } catch (e) {
      return RawConversionResult(
        errorCode: RawConversionErrorCode.unknown,
        errorMessage: '处理异常: $e',
      );
    }
  }

  static RawConversionResult processImageData(
    Uint8List inputData,
    int width,
    int height,
    int channels, {
    RawConversionConfigData? config,
    bool adjustCustom = false,
  }) {
    if (!_initialized || _handle == nullptr) {
      return const RawConversionResult(
        errorCode: RawConversionErrorCode.initFailed,
        errorMessage: '未初始化',
      );
    }

    try {
      // 分配输入数据内存
      final inputPtr = malloc<Uint8>(inputData.length);
      final inputList = inputPtr.asTypedList(inputData.length);
      inputList.setAll(0, inputData);

      // 分配输出数据内存
      final outputSize = width * height * channels;
      final outputPtr = malloc<Uint8>(outputSize);

      // 创建配置结构体
      final configPtr = malloc<RawConversionConfig>();
      final configData = config ?? RawConversionConfigData.defaultConfig;

      configPtr.ref.enableRawLut = configData.enableRawLut ? 1 : 0;
      configPtr.ref.enableDenoise = configData.enableDenoise ? 1 : 0;
      configPtr.ref.enableStdLut = configData.enableStdLut ? 1 : 0;
      configPtr.ref.adjustType = configData.adjustType.value;
      configPtr.ref.threads = configData.threads;
      configPtr.ref.strength = configData.strength;

      final adjustModePtr = malloc<Int32>();
      adjustModePtr.value = 0;

      final result = _process(
        _handle!,
        configPtr,
        inputPtr,
        width,
        height,
        channels,
        outputPtr,
        adjustModePtr,
        adjustCustom ? 1 : 0,
      );

      final adjustMode = adjustModePtr.value;

      // 清理资源
      malloc.free(configPtr);
      malloc.free(inputPtr);
      malloc.free(outputPtr);
      malloc.free(adjustModePtr);

      final errorCode = RawConversionErrorCode.fromValue(result);

      return RawConversionResult(
        errorCode: errorCode,
        errorMessage: errorCode != RawConversionErrorCode.success
            ? _getErrorMessage(result)
            : null,
        adjustMode: adjustMode,
      );
    } catch (e) {
      return RawConversionResult(
        errorCode: RawConversionErrorCode.unknown,
        errorMessage: '处理异常: $e',
      );
    }
  }

  static bool isSupportedFormat(String filePath) {
    if (!_loadLibrary()) {
      return false;
    }
    try {
      final filePathPtr = filePath.toNativeUtf8();
      final result = _isSupportedFormat(filePathPtr);
      malloc.free(filePathPtr);
      return result != 0;
    } catch (e) {
      return false;
    }
  }

  static String _getErrorMessage(int errorCode) {
    try {
      final errorPtr = _getErrorString(errorCode);
      return errorPtr.toDartString();
    } catch (e) {
      return 'Unknown error';
    }
  }

  static bool get isInitialized => _initialized && _handle != nullptr;

  static void dispose() {
    if (_handle != nullptr) {
      try {
        _clear(_handle!);
      } catch (e) {
        // 忽略清理异常
      }
      _handle = nullptr;
    }
    _initialized = false;
  }

  /// 获取库信息
  static Map<String, dynamic> getLibraryInfo() {
    return UniversalPlatformLoader.getLibraryInfo(
      'PGRawConversion',
      subDirectory: 'raw_conversion',
    );
  }
}
