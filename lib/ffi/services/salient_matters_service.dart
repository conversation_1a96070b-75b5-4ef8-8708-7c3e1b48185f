import 'dart:typed_data';

import 'package:turing_art/config/ai_sdk_config.dart';
import 'package:turing_art/ffi/models/salient_matters_model.dart';
import 'package:turing_art/ffi/native/salient_matters_bindings.dart';
import 'package:turing_art/utils/pg_log.dart';

/// SalientMatters服务类
///
/// 提供高级API，管理初始化状态和业务逻辑，参考RawConversion的设计模式
/// 采用静态方法模式，保持API的一致性
class SalientMattersService {
  static bool _isInitialized = false;
  static String? _currentKey;
  static String? _currentUserCode;
  static String? _currentProdCode;

  /// 获取初始化状态
  static bool get isInitialized =>
      _isInitialized && SalientMattersBindings.isInitialized;

  /// 使用配置文件初始化SalientMatters
  ///
  /// 使用 lib/config/ai_sdk_config.dart 中的配置
  /// 返回初始化结果
  static Future<SalientMattersResult> initializeFromConfig() async {
    final config = AISDKDevConfig.activeConfig;
    return initialize(
      key: config['key']!,
      userCode: config['userCode']!,
      prodCode: config['productCode']!,
    );
  }

  /// 初始化SalientMatters
  ///
  /// [key] 许可证密钥
  /// [userCode] 用户编码
  /// [prodCode] 产品编码
  ///
  /// 返回初始化结果
  static Future<SalientMattersResult> initialize({
    required String key,
    required String userCode,
    required String prodCode,
  }) async {
    // 检查是否已经使用相同参数初始化
    if (_isInitialized &&
        _currentKey == key &&
        _currentUserCode == userCode &&
        _currentProdCode == prodCode) {
      PGLog.i('SalientMatters已经使用相同参数初始化');
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.success,
      );
    }

    try {
      // 如果已初始化但参数不同，先释放资源
      if (_isInitialized) {
        dispose();
      }

      final result = SalientMattersBindings.initialize(
        key: key,
        userCode: userCode,
        prodCode: prodCode,
      );

      if (result.isSuccess) {
        _isInitialized = true;
        _currentKey = key;
        _currentUserCode = userCode;
        _currentProdCode = prodCode;
        PGLog.i('SalientMatters初始化成功');
      } else {
        PGLog.e('SalientMatters初始化失败: ${result.errorMessage}');
      }

      return result;
    } catch (e) {
      PGLog.e('SalientMatters初始化异常: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.initFailed,
        errorMessage: e.toString(),
      );
    }
  }

  /// 主体抠图
  ///
  /// [imageData] 输入图像数据
  /// [config] 配置选项，可选
  ///
  /// 返回抠图结果
  static Future<SalientMattersResult> matting(
    ImageData imageData, {
    SalientMattersConfig? config,
  }) async {
    if (!isInitialized) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '请先初始化SalientMatters',
      );
    }

    try {
      // 使用当前静态实例的认证信息构建配置
      final actualConfig = config ??
          SalientMattersConfig(
            type: SalientMattersType.matting,
            key: _currentKey!,
            userCode: _currentUserCode!,
            prodCode: _currentProdCode!,
            outputColor: config?.outputColor ?? const [255, 255, 255, 255],
          );

      return SalientMattersBindings.matting(imageData, config: actualConfig);
    } catch (e) {
      PGLog.e('主体抠图异常: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    }
  }

  /// 交互式抠图
  ///
  /// [imageData] 输入图像数据
  /// [foreStrokes] 前景笔触（可选）
  /// [backStrokes] 背景笔触（可选）
  /// [latestMask] 最新蒙版（可选，用于迭代优化）
  /// [config] 配置选项，可选
  ///
  /// 返回抠图结果
  static Future<SalientMattersResult> interactive(
    ImageData imageData, {
    ImageData? foreStrokes,
    ImageData? backStrokes,
    ImageData? latestMask,
    SalientMattersConfig? config,
  }) async {
    if (!isInitialized) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '请先初始化SalientMatters',
      );
    }

    // 验证输入图像数据
    if (!validateImageData(imageData)) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '输入图像数据无效',
      );
    }

    // 验证可选的图像参数是否与imageData匹配
    if (foreStrokes != null) {
      if (!_validateImageDataMatchesDimensions(foreStrokes, imageData)) {
        return const SalientMattersResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage: '前景笔触图像尺寸与输入图像不匹配',
        );
      }
    }

    if (backStrokes != null) {
      if (!_validateImageDataMatchesDimensions(backStrokes, imageData)) {
        return const SalientMattersResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage: '背景笔触图像尺寸与输入图像不匹配',
        );
      }
    }

    if (latestMask != null) {
      if (!_validateImageDataMatchesDimensions(latestMask, imageData)) {
        return const SalientMattersResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage: '最新蒙版图像尺寸与输入图像不匹配',
        );
      }
    }

    try {
      // 使用当前静态实例的认证信息构建配置
      final actualConfig = config ??
          SalientMattersConfig(
            type: SalientMattersType.interactive,
            key: _currentKey!,
            userCode: _currentUserCode!,
            prodCode: _currentProdCode!,
            outputColor: config?.outputColor ?? const [255, 255, 255, 255],
          );

      return SalientMattersBindings.interactive(
        imageData,
        latestMask,
        foreStrokes,
        backStrokes,
        config: actualConfig,
      );
    } catch (e) {
      PGLog.e('交互式抠图异常: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    }
  }

  /// 蒙版跟踪
  ///
  /// [currentImage] 当前帧图像
  /// [previousImage] 前一帧图像
  /// [previousMask] 前一帧蒙版
  /// [config] 配置选项，可选
  ///
  /// 返回跟踪结果
  static Future<SalientMattersResult> tracking(
    ImageData currentImage,
    ImageData previousImage,
    ImageData previousMask, {
    SalientMattersConfig? config,
  }) async {
    if (!isInitialized) {
      return const SalientMattersResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '请先初始化SalientMatters',
      );
    }

    try {
      // 使用当前静态实例的认证信息构建配置
      final actualConfig = config ??
          SalientMattersConfig(
            type: SalientMattersType.tracking,
            key: _currentKey!,
            userCode: _currentUserCode!,
            prodCode: _currentProdCode!,
            outputColor: config?.outputColor ?? const [255, 255, 255, 255],
          );

      return SalientMattersBindings.tracking(
        currentImage,
        previousImage,
        previousMask,
        config: actualConfig,
      );
    } catch (e) {
      PGLog.e('蒙版跟踪异常: $e');
      return SalientMattersResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    }
  }

  /// 区域主体抠图
  ///
  /// [imageData] 输入的完整图像数据
  /// [region] 要处理的区域信息
  /// [croppedImage] 可选：预裁剪的区域图像，如果为空则内部按region裁剪
  /// [previousFullResult] 可选：上次的完整图像抠图结果，用于回贴结果
  /// [config] 配置选项，可选
  /// [returnCroppedResult] 是否返回裁剪后的区域图像
  /// [returnCroppedMattingResult] 是否返回裁剪区域的主体抠图结果
  ///
  /// 返回区域抠图结果
  static Future<RegionalMattingResult> regionalMatting(
    ImageData imageData,
    RectData region, {
    ImageData? croppedImage,
    ImageData? previousFullResult,
    SalientMattersConfig? config,
    bool returnCroppedResult = false,
    bool returnCroppedMattingResult = false,
  }) async {
    if (!isInitialized) {
      return const RegionalMattingResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '请先初始化SalientMatters',
      );
    }

    // 验证输入图像数据
    if (!validateImageData(imageData)) {
      return const RegionalMattingResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '输入图像数据无效',
      );
    }

    // 验证区域参数
    if (!_validateRegion(imageData, region)) {
      return const RegionalMattingResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '区域参数无效',
      );
    }

    // 验证裁剪图像参数
    if (croppedImage != null) {
      if (!validateImageData(croppedImage)) {
        return const RegionalMattingResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage: '裁剪图像数据无效',
        );
      }
    }

    // 验证完整结果参数
    if (previousFullResult != null) {
      if (!validateImageData(previousFullResult)) {
        return const RegionalMattingResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage: '上次完整结果数据无效',
        );
      }

      if (!_validateImageDataMatchesDimensions(previousFullResult, imageData)) {
        return const RegionalMattingResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage: '上次完整结果尺寸与输入图像不匹配',
        );
      }
    }

    try {
      // 使用当前静态实例的认证信息构建配置
      final actualConfig = config ??
          SalientMattersConfig(
            type: SalientMattersType.matting,
            key: _currentKey!,
            userCode: _currentUserCode!,
            prodCode: _currentProdCode!,
            outputColor: config?.outputColor ?? const [255, 255, 255, 255],
          );

      return SalientMattersBindings.regionalMatting(
        imageData,
        region,
        croppedImage: croppedImage,
        previousFullResult: previousFullResult,
        config: actualConfig,
        returnCroppedResult: returnCroppedResult,
        returnCroppedMattingResult: returnCroppedMattingResult,
      );
    } catch (e) {
      PGLog.e('区域主体抠图异常: $e');
      return RegionalMattingResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    }
  }

  /// 区域交互式抠图
  ///
  /// [croppedImage] 输入的已裁剪区域图像
  /// [region] 要处理的区域信息
  /// [latestMask] 可选：上一次的区域抠图结果
  /// [foreStrokes] 可选：前景交互笔触
  /// [backStrokes] 可选：背景交互笔触
  /// [latestFullMask] 可选：上一次的完整图像抠图结果
  /// [config] 配置选项，可选
  /// [returnRegionalResult] 是否返回区域交互式抠图结果
  ///
  /// 返回区域交互式抠图结果
  static Future<RegionalInteractiveResult> regionalInteractive(
    ImageData croppedImage,
    RectData region, {
    ImageData? latestMask,
    ImageData? foreStrokes,
    ImageData? backStrokes,
    ImageData? latestFullMask,
    SalientMattersConfig? config,
    bool returnRegionalResult = false,
  }) async {
    if (!isInitialized) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '请先初始化SalientMatters',
      );
    }

    // 验证裁剪图像数据
    if (!validateImageData(croppedImage)) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '裁剪图像数据无效',
      );
    }

    // 验证区域参数合理性
    if (region.width <= 0 || region.height <= 0) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '区域尺寸无效',
      );
    }

    // 验证可选的图像参数
    if (latestMask != null && !validateImageData(latestMask)) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '最新蒙版数据无效',
      );
    }

    if (foreStrokes != null && !validateImageData(foreStrokes)) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '前景笔触数据无效',
      );
    }

    if (backStrokes != null && !validateImageData(backStrokes)) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '背景笔触数据无效',
      );
    }

    if (latestFullMask != null && !validateImageData(latestFullMask)) {
      return const RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '完整蒙版数据无效',
      );
    }

    try {
      // 使用当前静态实例的认证信息构建配置
      final actualConfig = config ??
          SalientMattersConfig(
            type: SalientMattersType.interactive,
            key: _currentKey!,
            userCode: _currentUserCode!,
            prodCode: _currentProdCode!,
            outputColor: config?.outputColor ?? const [255, 255, 255, 255],
          );

      return SalientMattersBindings.regionalInteractive(
        croppedImage,
        region,
        latestMask: latestMask,
        foreStrokes: foreStrokes,
        backStrokes: backStrokes,
        latestFullMask: latestFullMask,
        config: actualConfig,
        returnRegionalResult: returnRegionalResult,
      );
    } catch (e) {
      PGLog.e('区域交互式抠图异常: $e');
      return RegionalInteractiveResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    }
  }

  /// 检查服务状态
  static void checkStatus() {
    PGLog.i('=== SalientMatters服务状态检查 ===');
    PGLog.i('服务初始化状态: $_isInitialized');
    PGLog.i('绑定库初始化状态: ${SalientMattersBindings.isInitialized}');
    PGLog.i('整体可用状态: $isInitialized');

    if (_isInitialized) {
      PGLog.i('当前用户编码: $_currentUserCode');
      PGLog.i('当前产品编码: $_currentProdCode');
    }

    final defaultConfig = SalientMattersBindings.getDefaultConfig();
    PGLog.i('默认网络尺寸: ${defaultConfig.netSize}');
    PGLog.i('默认算法类型: ${defaultConfig.type}');
  }

  /// 获取默认配置
  static SalientMattersConfig getDefaultConfig() {
    return SalientMattersBindings.getDefaultConfig();
  }

  /// 释放资源
  static void dispose() {
    if (_isInitialized) {
      try {
        SalientMattersBindings.dispose();
        _isInitialized = false;
        _currentKey = null;
        _currentUserCode = null;
        _currentProdCode = null;
        PGLog.i('SalientMatters服务资源已释放');
      } catch (e) {
        PGLog.e('释放SalientMatters服务资源时出错: $e');
      }
    }
  }

  /// 处理前景和后景的交互蒙版数据
  ///
  /// [strokeMask] 当前涂抹/擦除的路径蒙版，不能为空
  /// [previousForegroundMask] 上一次的前景蒙版数据（单通道），可以为空
  /// [previousBackgroundMask] 上一次的后景蒙版数据（单通道），可以为空
  /// [isPainting] 标记是涂抹(true)还是擦除(false)
  ///
  /// 返回处理结果，包含新的前景和背景蒙版数据
  static Future<ProcessInteractiveMaskResult> processInteractiveMask(
    ImageData strokeMask,
    Uint8List? previousForegroundMask,
    Uint8List? previousBackgroundMask,
    bool isPainting,
  ) async {
    if (!isInitialized) {
      return const ProcessInteractiveMaskResult(
        errorCode: SalientMattersErrorCode.notInitialized,
        errorMessage: '请先初始化SalientMatters',
      );
    }

    // 验证strokeMask参数
    if (!validateImageData(strokeMask)) {
      return const ProcessInteractiveMaskResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '路径蒙版数据无效',
      );
    }

    // 验证previousForegroundMask参数
    if (previousForegroundMask != null) {
      final expectedMaskSize = strokeMask.width * strokeMask.height;
      if (previousForegroundMask.length != expectedMaskSize) {
        return ProcessInteractiveMaskResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage:
              '前景蒙版数据大小不匹配: 期望$expectedMaskSize，实际${previousForegroundMask.length}',
        );
      }
    }

    // 验证previousBackgroundMask参数
    if (previousBackgroundMask != null) {
      final expectedMaskSize = strokeMask.width * strokeMask.height;
      if (previousBackgroundMask.length != expectedMaskSize) {
        return ProcessInteractiveMaskResult(
          errorCode: SalientMattersErrorCode.invalidParam,
          errorMessage:
              '背景蒙版数据大小不匹配: 期望$expectedMaskSize，实际${previousBackgroundMask.length}',
        );
      }
    }

    try {
      return SalientMattersBindings.processInteractiveMask(
        strokeMask,
        previousForegroundMask,
        previousBackgroundMask,
        isPainting,
      );
    } catch (e) {
      PGLog.e('处理交互式蒙版异常: $e');
      return ProcessInteractiveMaskResult(
        errorCode: SalientMattersErrorCode.processFailed,
        errorMessage: e.toString(),
      );
    }
  }

  /// 快速主体抠图
  ///
  /// 使用默认配置进行主体抠图
  static Future<SalientMattersResult> quickMatting(ImageData imageData) {
    return matting(imageData);
  }

  /// 单次交互抠图
  ///
  /// 适用于只需要一次交互的场景
  static Future<SalientMattersResult> oneTimeInteractive(
    ImageData imageData,
    ImageData foreStrokes,
    ImageData backStrokes,
  ) {
    return interactive(
      imageData,
      foreStrokes: foreStrokes,
      backStrokes: backStrokes,
    );
  }

  /// 快速区域抠图
  ///
  /// 使用默认配置进行区域主体抠图
  static Future<RegionalMattingResult> quickRegionalMatting(
    ImageData imageData,
    RectData region,
  ) {
    return regionalMatting(imageData, region);
  }

  /// 验证图像数据
  ///
  /// 检查ImageData是否有效
  static bool validateImageData(ImageData imageData) {
    if (imageData.width <= 0 || imageData.height <= 0) {
      PGLog.e('图像尺寸无效: ${imageData.width}x${imageData.height}');
      return false;
    }

    if (imageData.channels <= 0 || imageData.channels > 4) {
      PGLog.e('图像通道数无效: ${imageData.channels}');
      return false;
    }

    final expectedDataSize =
        imageData.width * imageData.height * imageData.channels;
    if (imageData.data.length != expectedDataSize) {
      PGLog.e('图像数据大小不匹配: 期望$expectedDataSize，实际${imageData.data.length}');
      return false;
    }

    return true;
  }

  /// 验证两个图像数据的尺寸是否匹配
  ///
  /// 检查两个ImageData的宽高是否相同，同时验证各自的数据完整性
  static bool _validateImageDataMatchesDimensions(
      ImageData imageData1, ImageData imageData2) {
    // 首先验证各自的数据完整性
    if (!validateImageData(imageData1) || !validateImageData(imageData2)) {
      return false;
    }

    // 检查尺寸是否匹配
    if (imageData1.width != imageData2.width ||
        imageData1.height != imageData2.height) {
      PGLog.e(
          '图像尺寸不匹配: ${imageData1.width}x${imageData1.height} vs ${imageData2.width}x${imageData2.height}');
      return false;
    }

    return true;
  }

  /// 验证区域数据
  ///
  /// 检查RectData和ImageData是否匹配
  static bool _validateRegion(ImageData imageData, RectData region) {
    if (region.width <= 0 || region.height <= 0) {
      PGLog.e('区域尺寸无效: ${region.width}x${region.height}');
      return false;
    }

    if (region.x < 0 || region.y < 0) {
      PGLog.e('区域位置无效: (${region.x}, ${region.y})');
      return false;
    }

    if (region.x + region.width > imageData.width ||
        region.y + region.height > imageData.height) {
      PGLog.e(
          '区域超出图像范围: 区域(${region.x}, ${region.y}, ${region.width}, ${region.height}) vs 图像(${imageData.width}, ${imageData.height})');
      return false;
    }

    return true;
  }

  /// 检查是否可以使用（已初始化且底层库正常）
  static bool get canUse => isInitialized;
}
