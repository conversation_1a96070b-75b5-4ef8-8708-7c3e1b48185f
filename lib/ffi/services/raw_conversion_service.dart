import 'dart:io';
import 'dart:typed_data';

import 'package:turing_art/config/ai_sdk_config.dart';
import 'package:turing_art/ffi/models/raw_conversion_model.dart';
import 'package:turing_art/ffi/native/raw_conversion_bindings.dart';

/// RawConversion服务类
///
/// 提供高级API，管理初始化状态和业务逻辑，参考SalientMattersService的设计模式
/// 采用静态方法模式，保持与原来的RawConversion类兼容
class RawConversionService {
  static bool _isInitialized = false;

  /// 初始化Raw转换器
  static Future<RawConversionResult> initialize({
    String? key,
    String? userCode,
    String? prodCode,
  }) async {
    final result = RawConversionBindings.initialize(
      key: key,
      userCode: userCode,
      prodCode: prodCode,
    );
    _isInitialized = result.isSuccess;
    return result;
  }

  /// 使用配置文件初始化SalientMatters
  ///
  /// 使用 lib/config/ai_sdk_config.dart 中的配置
  /// 返回初始化结果
  static Future<RawConversionResult> initializeFromConfig() async {
    final config = AISDKDevConfig.activeConfig;
    return initialize(
      key: config['key']!,
      userCode: config['userCode']!,
      prodCode: config['productCode']!,
    );
  }

  /// 处理图像文件
  static Future<RawConversionResult> processFile(
    String inputPath,
    String outputPath, {
    RawConversionConfigData? config,
    bool adjustCustom = true,
  }) async {
    if (!isInitialized) {
      const result = RawConversionResult(
        errorCode: RawConversionErrorCode.initFailed,
        errorMessage: '未初始化',
      );
      return result;
    }

    // 检查输入文件是否存在
    final inputFile = File(inputPath);
    if (!inputFile.existsSync()) {
      const result = RawConversionResult(
        errorCode: RawConversionErrorCode.fileNotFound,
        errorMessage: '输入文件不存在',
      );
      return result;
    }

    // 确保输出目录存在
    final outputFile = File(outputPath);
    final outputDir = outputFile.parent;
    if (!outputDir.existsSync()) {
      await outputDir.create(recursive: true);
    }

    final result = RawConversionBindings.processFile(
      inputPath,
      outputPath,
      config: config,
      adjustCustom: adjustCustom,
    );

    return result;
  }

  /// 处理图像数据
  static Future<RawConversionResult> processImageData(
    Uint8List inputData,
    int width,
    int height,
    int channels, {
    RawConversionConfigData? config,
    bool adjustCustom = true,
  }) async {
    if (!isInitialized) {
      const result = RawConversionResult(
        errorCode: RawConversionErrorCode.initFailed,
        errorMessage: '未初始化',
      );
      return result;
    }

    final result = RawConversionBindings.processImageData(
      inputData,
      width,
      height,
      channels,
      config: config,
      adjustCustom: adjustCustom,
    );

    return result;
  }

  /// 检查文件格式是否支持
  static bool isSupportedFormat(String filePath) {
    return RawConversionBindings.isSupportedFormat(filePath);
  }

  /// 获取版本信息
  static String getVersion() {
    return RawConversionBindings.getVersion();
  }

  /// 获取默认配置
  static RawConversionConfigData getDefaultConfig() {
    return RawConversionConfigData.defaultConfig;
  }

  /// 是否已初始化
  static bool get isInitialized =>
      _isInitialized && RawConversionBindings.isInitialized;

  /// 释放资源
  static void dispose() {
    RawConversionBindings.dispose();
    _isInitialized = false;
  }
}
