import 'dart:io';

import 'package:turing_art/ffi/native/disk_info_bindings.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 磁盘信息服务
/// 提供友好的磁盘空间查询接口
class DiskInfoService {
  static bool _initialized = false;

  /// 初始化服务
  static Future<bool> initialize() async {
    if (_initialized) return true;

    try {
      _initialized = DiskInfoBindings.initialize();
      if (_initialized) {
        PGLog.i('DiskInfoService initialized successfully');
      } else {
        PGLog.e('Failed to initialize DiskInfoService');
      }
      return _initialized;
    } catch (e) {
      PGLog.e('Error initializing DiskInfoService: $e');
      return false;
    }
  }

  /// 检查服务是否可用
  static bool get isAvailable => _initialized && DiskInfoBindings.isAvailable;

  /// 获取指定路径的磁盘空间信息
  ///
  /// [path] 磁盘路径，如果为null则使用当前工作目录
  /// 返回磁盘空间信息，失败时返回null
  static Future<DiskSpaceInfo?> getDiskSpace([String? path]) async {
    if (!isAvailable) {
      await initialize();
      if (!isAvailable) {
        PGLog.w('DiskInfoService not available');
        return null;
      }
    }

    try {
      final targetPath = path ?? _getCurrentDirectoryPath();
      return DiskInfoBindings.getDiskSpace(targetPath);
    } catch (e) {
      PGLog.e('Error getting disk space: $e');
      return null;
    }
  }

  /// 获取当前工作目录的磁盘空间信息
  static Future<DiskSpaceInfo?> getCurrentDiskSpace() async {
    return getDiskSpace();
  }

  /// 获取系统驱动器的磁盘空间信息（Windows: C:\, Unix: /）
  static Future<DiskSpaceInfo?> getSystemDiskSpace() async {
    final systemPath = Platform.isWindows ? 'C:\\' : '/';
    return getDiskSpace(systemPath);
  }

  /// 检查指定路径是否有足够的磁盘空间
  ///
  /// [requiredSpace] 所需空间（字节）
  /// [path] 检查路径，为null时使用当前目录
  /// 返回是否有足够空间，查询失败时返回false
  static Future<bool> hasEnoughSpace(int requiredSpace, [String? path]) async {
    final diskInfo = await getDiskSpace(path);
    if (diskInfo == null) return false;
    return diskInfo.freeSpace >= requiredSpace;
  }

  /// 获取可读的磁盘空间报告
  ///
  /// [path] 检查路径，为null时使用当前目录
  /// 返回格式化的磁盘空间报告字符串
  static Future<String?> getDiskSpaceReport([String? path]) async {
    final diskInfo = await getDiskSpace(path);
    if (diskInfo == null) return null;

    final targetPath = path ?? _getCurrentDirectoryPath();
    return '''
磁盘空间报告 - $targetPath
总空间: ${diskInfo.formattedTotalSpace}
可用空间: ${diskInfo.formattedFreeSpace} (${(diskInfo.freeSpacePercentage * 100).toStringAsFixed(1)}%)
已使用: ${diskInfo.formattedUsedSpace} (${(diskInfo.usedSpacePercentage * 100).toStringAsFixed(1)}%)
''';
  }

  /// 获取当前目录路径
  static String _getCurrentDirectoryPath() {
    try {
      return Directory.current.path;
    } catch (e) {
      PGLog.w('Failed to get current directory: $e');
      return Platform.isWindows ? 'C:\\' : '/';
    }
  }

  /// 将路径标准化为适合native库的格式
  static String normalizePath(String path) {
    if (Platform.isWindows) {
      // Windows路径处理
      var normalized = path.replaceAll('/', '\\');
      if (!normalized.endsWith('\\') &&
          (normalized.length == 2 && normalized.endsWith(':'))) {
        normalized += '\\';
      }
      return normalized;
    } else {
      // Unix系统路径处理
      var normalized = path.replaceAll('\\', '/');
      if (normalized.isEmpty) normalized = '/';
      return normalized;
    }
  }

  /// 获取所有可用驱动器的磁盘空间信息（仅Windows）
  static Future<Map<String, DiskSpaceInfo?>> getAllDrivesSpace() async {
    if (!Platform.isWindows) {
      return {};
    }

    final drives = <String, DiskSpaceInfo?>{};

    // 检查常见的驱动器字母
    for (int i = 65; i <= 90; i++) {
      // A-Z
      final driveLetter = String.fromCharCode(i);
      final drivePath = '$driveLetter:\\';

      // 检查驱动器是否存在
      if (Directory(drivePath).existsSync()) {
        final diskInfo = await getDiskSpace(drivePath);
        if (diskInfo != null) {
          drives[driveLetter] = diskInfo;
        }
      }
    }

    return drives;
  }

  /// 清理服务资源
  static void dispose() {
    DiskInfoBindings.dispose();
    _initialized = false;
  }
}

/// 磁盘空间常量工具类
class DiskSpaceConstants {
  /// 1 KB = 1024 bytes
  static const int kilobyte = 1024;

  /// 1 MB = 1024 * 1024 bytes
  static const int megabyte = 1024 * 1024;

  /// 1 GB = 1024 * 1024 * 1024 bytes
  static const int gigabyte = 1024 * 1024 * 1024;

  /// 1 TB = 1024^4 bytes
  static const int terabyte = 1024 * 1024 * 1024 * 1024;

  /// 推荐的最小可用空间 (1GB)
  static const int recommendedMinFreeSpace = gigabyte;

  /// 警告阈值的可用空间 (500MB)
  static const int warningFreeSpace = 500 * megabyte;

  /// 危险阈值的可用空间 (100MB)
  static const int criticalFreeSpace = 100 * megabyte;
}
