# RAW解码器使用文档

## 概述

本项目集成了PGRawDecoderLib.dll动态库，用于在Flutter应用中处理RAW图像文件。该解码器支持多种RAW格式，并提供丰富的处理选项。

## 功能特性

- ✅ 支持多种RAW格式（CR2, NEF, ARW, DNG等）
- ✅ 可配置输出格式（JPG, JPEG, PNG）
- ✅ 支持位深度设置（8位/16位）
- ✅ 支持图像尺寸限制
- ✅ 内置降噪和锐化功能
- ✅ 曝光补偿调节
- ✅ 缩略图生成
- ✅ 多线程处理支持
- ✅ 完整的错误处理机制

## 快速开始

### 1. 初始化解码器

```dart
import 'package:turing_art/platform/raw_decoder.dart';

// 使用默认设置初始化
final result = await RawDecoder.initialize();
if (result.isSuccess) {
  print('解码器初始化成功');
} else {
  print('初始化失败: ${result.errorMessage}');
}

// 或者使用自定义设置
final customResult = await RawDecoder.initialize(
  numThreads: 4,        // 使用4个线程
  bundlePath: null,     // 使用默认Bundle路径
);
```

### 2. 处理RAW文件

```dart
// 基础使用 - 使用默认设置
final result = await RawDecoder.processRawFile(
  'C:/path/to/input.raw',
  'C:/path/to/output.jpg',
);

if (result.isSuccess) {
  print('处理成功');
} else {
  print('处理失败: ${result.errorMessage}');
}
```

### 3. 使用自定义配置

```dart
// 创建自定义配置
const config = RawDecoderConfig(
  bitDepth: 16,           // 16位深度
  maxDim: 2048,          // 最大尺寸2048像素
  thumbnail: true,        // 生成缩略图
  denoise: true,         // 启用降噪
  clarity: true,         // 启用锐化
  exposure: 0.5,         // +0.5曝光补偿
  outputFormat: 'png',   // 输出PNG格式
);

// 使用配置处理文件
final result = await RawDecoder.processRawFile(
  inputPath,
  outputPath,
  config: config,
);
```

### 4. 批量处理

```dart
final files = ['photo1.raw', 'photo2.raw', 'photo3.raw'];
const batchConfig = RawDecoderConfig(
  bitDepth: 8,
  maxDim: 1920,
  denoise: true,
  outputFormat: 'jpg',
);

for (final file in files) {
  final result = await RawDecoder.processRawFile(
    'input/$file',
    'output/${file.replaceAll('.raw', '.jpg')}',
    config: batchConfig,
  );
  
  if (result.isSuccess) {
    print('✅ $file 处理成功');
  } else {
    print('❌ $file 处理失败: ${result.errorMessage}');
  }
}
```

### 5. 资源管理

```dart
// 检查初始化状态
if (RawDecoder.isInitialized) {
  print('解码器已就绪');
}

// 获取默认配置
final defaultConfig = RawDecoder.getDefaultConfig();
print('默认位深度: ${defaultConfig.bitDepth}');

// 释放资源（在应用退出时）
RawDecoder.dispose();
```

## 配置参数详解

### RawDecoderConfig 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| bitDepth | int | 8 | 输出位深度：8位或16位 |
| maxDim | int | 0 | 最大尺寸限制，0表示原始尺寸 |
| thumbnail | bool | false | 是否生成缩略图 |
| denoise | bool | false | 是否启用降噪 |
| clarity | bool | false | 是否启用锐化 |
| exposure | double | 0.0 | 曝光补偿（-2.0到+2.0） |
| outputFormat | String | 'jpg' | 输出格式：'jpg', 'jpeg', 'png' |

## 错误处理

### 错误码类型

```dart
enum RawDecoderErrorCode {
  success,        // 成功
  initFailed,     // 初始化失败
  notInitialized, // 未初始化
  invalidParam,   // 参数无效
  fileNotFound,   // 文件未找到
  decodeFailed,   // 解码失败
  saveFailed,     // 保存失败
  unknown,        // 未知错误
}
```

### 错误处理示例

```dart
final result = await RawDecoder.processRawFile(inputPath, outputPath);

switch (result.errorCode) {
  case RawDecoderErrorCode.success:
    print('处理成功');
    break;
  case RawDecoderErrorCode.fileNotFound:
    print('输入文件不存在');
    break;
  case RawDecoderErrorCode.notInitialized:
    print('解码器未初始化，请先调用initialize()');
    break;
  default:
    print('处理失败: ${result.errorMessage}');
}
```

## 支持的文件格式

- **Canon**: .CR2, .CRW
- **Nikon**: .NEF, .NRW
- **Sony**: .ARW, .SRF, .SR2
- **Adobe**: .DNG
- **Fujifilm**: .RAF
- **Olympus**: .ORF
- **Panasonic**: .RW2
- **Pentax**: .PEF
- **Generic**: .RAW

## 性能优化建议

### 1. 线程数设置
```dart
// 根据CPU核心数设置线程数
final numCores = Platform.numberOfProcessors;
await RawDecoder.initialize(numThreads: numCores);
```

### 2. 批量处理优化
```dart
// 使用合适的配置减少处理时间
const fastConfig = RawDecoderConfig(
  bitDepth: 8,         // 8位处理更快
  maxDim: 1920,       // 限制输出尺寸
  denoise: false,     // 跳过耗时的降噪
  clarity: false,     // 跳过锐化
);
```

### 3. 内存管理
```dart
// 长时间运行的应用应定期检查内存使用
if (memoryUsage > threshold) {
  // 重新初始化解码器
  RawDecoder.dispose();
  await RawDecoder.initialize();
}
```

## 故障排除

### 常见问题

1. **初始化失败**
   - 确保PGRawDecoderLib.dll文件存在于应用目录
   - 检查Windows系统是否支持该DLL
   - 确认运行时环境正确

2. **文件处理失败**
   - 检查输入文件格式是否支持
   - 确认输出目录有写入权限
   - 验证文件路径正确性

3. **性能问题**
   - 适当调整线程数
   - 考虑降低输出质量设置
   - 监控内存使用情况

### 调试模式

```dart
// 启用详细日志
import 'package:turing_art/utils/pg_log.dart';

// 在处理前检查状态
PGLog.i('解码器状态: ${RawDecoder.isInitialized}');
PGLog.i('默认配置: ${RawDecoder.getDefaultConfig()}');
```

## 示例应用

项目中包含了完整的测试页面：
- 文件：`lib/ui/pages/raw_decoder_test_page.dart`
- 示例：`lib/utils/raw_decoder_example.dart`

可以运行这些示例来了解详细用法。

## 注意事项

1. **平台限制**: 当前仅支持Windows平台
2. **资源管理**: 应用退出前务必调用`RawDecoder.dispose()`
3. **线程安全**: 避免在多个线程中同时调用处理函数
4. **文件大小**: 大文件处理可能需要较长时间，建议显示进度提示
5. **错误处理**: 始终检查返回结果并妥善处理错误情况

## 更新历史

- v1.0.0: 初始版本，支持基础RAW文件处理功能
- 支持多种RAW格式和输出选项
- 提供完整的Flutter FFI封装
- 包含详细的使用示例和文档 