# DiskInfo 磁盘信息模块使用示例

这个模块提供了获取磁盘空间信息的native接口，基于您的`disk_info_api.h`头文件实现。

## 快速开始

### 1. 导入模块

```dart
import 'package:turing_art/ffi/ffi.dart';
```

### 2. 初始化服务

```dart
// 在应用启动时初始化
await DiskInfoService.initialize();
```

### 3. 基本使用

```dart
// 获取当前目录的磁盘空间信息
final diskInfo = await DiskInfoService.getCurrentDiskSpace();
if (diskInfo != null) {
  print('总空间: ${diskInfo.formattedTotalSpace}');
  print('可用空间: ${diskInfo.formattedFreeSpace}');
  print('已使用: ${diskInfo.formattedUsedSpace}');
  print('使用率: ${(diskInfo.usedSpacePercentage * 100).toStringAsFixed(1)}%');
}
```

## 详细API说明

### DiskInfoService 主要方法

#### 获取磁盘空间信息

```dart
// 获取指定路径的磁盘空间
final diskInfo = await DiskInfoService.getDiskSpace('C:\\');

// 获取当前工作目录的磁盘空间
final currentDiskInfo = await DiskInfoService.getCurrentDiskSpace();

// 获取系统驱动器磁盘空间 (Windows: C:\, Unix: /)
final systemDiskInfo = await DiskInfoService.getSystemDiskSpace();
```

#### 检查可用空间

```dart
// 检查是否有足够的磁盘空间（例如需要1GB空间）
final hasSpace = await DiskInfoService.hasEnoughSpace(
  DiskSpaceConstants.gigabyte,
  'C:\\'
);

if (!hasSpace) {
  // 显示磁盘空间不足警告
  showInsufficientSpaceDialog();
}
```

#### 获取格式化报告

```dart
// 获取可读的磁盘空间报告
final report = await DiskInfoService.getDiskSpaceReport('C:\\');
print(report);
// 输出：
// 磁盘空间报告 - C:\
// 总空间: 500.00 GB
// 可用空间: 120.50 GB (24.1%)
// 已使用: 379.50 GB (75.9%)
```

#### Windows特有功能

```dart
// 获取所有驱动器的磁盘空间信息（仅Windows）
final allDrives = await DiskInfoService.getAllDrivesSpace();
allDrives.forEach((drive, info) {
  if (info != null) {
    print('驱动器 $drive: ${info.formattedFreeSpace} 可用');
  }
});
```

### DiskSpaceInfo 数据模型

```dart
class DiskSpaceInfo {
  final int totalSpace;        // 总空间（字节）
  final int freeSpace;         // 可用空间（字节）
  final int usedSpace;         // 已使用空间（字节）
  
  // 百分比属性
  double get freeSpacePercentage;  // 可用空间百分比 (0.0-1.0)
  double get usedSpacePercentage;  // 已使用空间百分比 (0.0-1.0)
  
  // 格式化显示
  String get formattedTotalSpace;  // 格式化总空间
  String get formattedFreeSpace;   // 格式化可用空间
  String get formattedUsedSpace;   // 格式化已使用空间
}
```

### 常量和阈值

```dart
// 使用预定义的空间常量
DiskSpaceConstants.kilobyte      // 1 KB
DiskSpaceConstants.megabyte      // 1 MB
DiskSpaceConstants.gigabyte      // 1 GB
DiskSpaceConstants.terabyte      // 1 TB

// 使用预定义的阈值
DiskSpaceConstants.recommendedMinFreeSpace  // 推荐最小可用空间 (1GB)
DiskSpaceConstants.warningFreeSpace         // 警告阈值 (500MB)
DiskSpaceConstants.criticalFreeSpace        // 危险阈值 (100MB)
```

## 实际应用场景

### 1. 导出前检查磁盘空间

```dart
Future<bool> checkSpaceBeforeExport(int estimatedFileSize) async {
  final diskInfo = await DiskInfoService.getCurrentDiskSpace();
  if (diskInfo == null) {
    // 无法获取磁盘信息，允许继续但显示警告
    showWarningDialog('无法检查磁盘空间，请确保有足够的存储空间');
    return true;
  }
  
  if (diskInfo.freeSpace < estimatedFileSize) {
    // 空间不足
    showInsufficientSpaceDialog(
      '磁盘空间不足\n'
      '需要: ${DiskSpaceInfo._formatBytes(estimatedFileSize)}\n'
      '可用: ${diskInfo.formattedFreeSpace}'
    );
    return false;
  }
  
  if (diskInfo.freeSpace < DiskSpaceConstants.warningFreeSpace) {
    // 空间不多，显示警告
    final proceed = await showConfirmDialog(
      '磁盘空间较少 (${diskInfo.formattedFreeSpace})，是否继续？'
    );
    return proceed ?? false;
  }
  
  return true;
}
```

### 2. 磁盘空间监控Widget

```dart
class DiskSpaceIndicator extends StatefulWidget {
  @override
  _DiskSpaceIndicatorState createState() => _DiskSpaceIndicatorState();
}

class _DiskSpaceIndicatorState extends State<DiskSpaceIndicator> {
  DiskSpaceInfo? _diskInfo;
  
  @override
  void initState() {
    super.initState();
    _updateDiskSpace();
  }
  
  Future<void> _updateDiskSpace() async {
    final diskInfo = await DiskInfoService.getCurrentDiskSpace();
    if (mounted) {
      setState(() {
        _diskInfo = diskInfo;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_diskInfo == null) {
      return Text('检查磁盘空间中...');
    }
    
    final Color color = _diskInfo!.freeSpace < DiskSpaceConstants.criticalFreeSpace
        ? Colors.red
        : _diskInfo!.freeSpace < DiskSpaceConstants.warningFreeSpace
            ? Colors.orange
            : Colors.green;
    
    return Row(
      children: [
        Icon(Icons.storage, color: color),
        SizedBox(width: 8),
        Text(
          '可用空间: ${_diskInfo!.formattedFreeSpace}',
          style: TextStyle(color: color),
        ),
      ],
    );
  }
}
```

### 3. 应用启动时的磁盘空间检查

```dart
class AppInitializer {
  static Future<void> initialize() async {
    // 初始化磁盘信息服务
    await DiskInfoService.initialize();
    
    // 检查磁盘空间
    await _checkDiskSpace();
  }
  
  static Future<void> _checkDiskSpace() async {
    final diskInfo = await DiskInfoService.getCurrentDiskSpace();
    if (diskInfo == null) return;
    
    if (diskInfo.freeSpace < DiskSpaceConstants.criticalFreeSpace) {
      // 显示严重警告
      showCriticalSpaceWarning();
    } else if (diskInfo.freeSpace < DiskSpaceConstants.warningFreeSpace) {
      // 显示一般警告
      showLowSpaceWarning();
    }
  }
}
```

## 注意事项

1. **平台支持**: 目前只支持Windows平台，macOS和Linux平台的native库尚未实现
2. **路径格式**: Windows使用反斜杠路径（如`C:\\`），Unix系统使用正斜杠路径（如`/`）
3. **权限**: 确保应用有足够的权限访问指定路径
4. **错误处理**: 所有方法都会返回`null`或`false`来表示失败，请适当处理
5. **内存管理**: FFI绑定会自动管理native内存，无需手动释放

## 故障排除

如果遇到问题，可以检查：

1. **库文件**: 确保`PGDiskInfo.dll`在正确的位置
2. **初始化**: 确保在使用前调用了`DiskInfoService.initialize()`
3. **日志**: 查看PGLog输出的错误信息
4. **路径**: 确保路径格式正确且存在 