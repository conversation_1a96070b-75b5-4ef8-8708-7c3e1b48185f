# Raw Conversion Flutter 接口封装

这是PGRawConversionLib.dll动态库的Flutter接口封装，提供了完整的Raw图像转换功能。

## 功能特性

- 🎨 Raw图像转换和调色处理
- 🔧 多种配置选项（Raw LUT、去噪、标准LUT等）
- 📱 跨平台支持（Windows、macOS、Linux）
- 🧵 多线程处理支持
- 🛡️ 完整的错误处理

## 快速开始

### 1. 基础使用

```dart
import 'package:turing_art/ffi/native/raw_conversion_service.dart';
import 'package:turing_art/ffi/models/raw_conversion_model.dart';

// 初始化
final result = await RawConversionService.initialize();
if (result.isSuccess) {
  print('初始化成功');
} else {
  print('初始化失败: ${result.errorMessage}');
}

// 处理文件
final processResult = await RawConversionService.processFile(
  'input.jpg',
  'output.jpg',
);

if (processResult.isSuccess) {
  print('处理成功，调色模式: ${processResult.adjustMode}');
} else {
  print('处理失败: ${processResult.errorMessage}');
}

// 释放资源
RawConversionService.dispose();
```

### 2. 带授权信息的初始化

```dart
final result = await RawConversionService.initialize(
  key: 'your_license_key',
  userCode: 'user_code_123',
  prodCode: 'product_code_456',
);
```

### 3. 自定义配置

```dart
final config = RawConversionConfigData(
  enableRawLut: true,
  enableDenoise: false,  // 关闭去噪
  enableStdLut: true,
  adjustType: RawConversionAdjustType.normal,
  threads: 4,  // 使用4个线程
);

final result = await RawConversionService.processFile(
  'input.jpg',
  'output.jpg',
  config: config,
  adjustCustom: true,
);
```

### 4. 处理图像数据

```dart
// 假设有一个Uint8List图像数据
Uint8List imageData = ...;
int width = 1920;
int height = 1080;
int channels = 3;

final result = await RawConversionService.processImageData(
  imageData,
  width,
  height,
  channels,
);
```

## API 参考

### 核心类

#### `RawConversionService`
主要的服务类，提供静态方法进行Raw转换操作。

**主要方法：**
- `initialize()` - 初始化转换器
- `processFile()` - 处理图像文件
- `processImageData()` - 处理图像数据
- `isSupportedFormat()` - 检查文件格式支持
- `getVersion()` - 获取版本信息
- `dispose()` - 释放资源

#### `RawConversionConfigData`
配置数据类，用于设置处理参数。

**属性：**
- `enableRawLut: bool` - 是否启用Raw LUT
- `enableDenoise: bool` - 是否启用去噪
- `enableStdLut: bool` - 是否启用标准LUT
- `adjustType: RawConversionAdjustType` - 调色模式
- `threads: int` - 线程数（-1为自动）

#### `RawConversionResult`
操作结果类，包含错误码、错误信息和调色模式。

**属性：**
- `errorCode: RawConversionErrorCode` - 错误码
- `errorMessage: String?` - 错误信息
- `isSuccess: bool` - 是否成功
- `adjustMode: int?` - 调色模式

### 枚举

#### `RawConversionErrorCode`
错误码枚举：
- `success` - 成功
- `invalidParams` - 无效参数
- `initFailed` - 初始化失败
- `processingFailed` - 处理失败
- `unsupportedFormat` - 不支持的格式
- `fileNotFound` - 文件未找到
- `outOfMemory` - 内存不足
- `unknown` - 未知错误

#### `RawConversionAdjustType`
调色模式枚举：
- `auto` - 自动调色
- `normal` - 普通调色
- `style` - 风格调色

## 完整示例

查看 `lib/utils/raw_conversion_example.dart` 文件获取完整的使用示例，包括：

- 基础初始化和处理
- 带授权信息的初始化
- 自定义配置处理
- 批量文件处理
- 图像数据处理
- 错误处理示例
- 生命周期管理
- 性能测试

## 测试页面

项目包含一个完整的测试页面 `RawConversionTestPage`，可以通过以下方式访问：

1. 在快捷键指南对话框中点击"Raw转换器测试"按钮
2. 或直接导航到该页面

测试页面提供了：
- 交互式的参数配置
- 文件选择和处理
- 实时状态显示
- 示例代码运行

## 注意事项

1. **初始化要求**：在使用任何处理功能之前必须先调用 `initialize()`
2. **资源管理**：使用完毕后应调用 `dispose()` 释放资源
3. **线程安全**：该接口不是线程安全的，请在主线程中使用
4. **文件权限**：确保应用有读写指定文件路径的权限
5. **内存管理**：处理大图像时注意内存使用情况

## 依赖库

该接口依赖以下库：
- PGRawConversionLib.dll（Windows）
- libPGRawConversionLib.dylib（macOS）
- libPGRawConversionLib.so（Linux）

## 构建配置

动态库会在构建时自动复制到输出目录，CMakeLists.txt已包含相应配置：

```cmake
# 复制 PGRawConversionLib.dll 文件到输出目录
add_custom_command(
        TARGET ${BINARY_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_CURRENT_SOURCE_DIR}/RawConversion/PGRawConversionLib.dll"
        "$<TARGET_FILE_DIR:${BINARY_NAME}>"
)
```

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查动态库是否正确复制到输出目录
   - 确认授权信息是否正确（如果使用）
   - 查看详细错误信息

2. **处理失败**
   - 确认文件路径正确且文件存在
   - 检查文件格式是否支持
   - 确保有足够的磁盘空间和内存

3. **性能问题**
   - 调整线程数设置
   - 检查系统资源使用情况
   - 考虑批量处理时的内存管理

### 调试建议

1. 启用详细日志输出
2. 使用测试页面进行交互式调试
3. 运行示例代码验证功能
4. 检查系统事件日志

## 版本历史

- v1.1.0 - API更新，简化初始化流程，移除ONNX配置参数
- v1.0.0 - 初始版本，支持基础Raw转换功能 