# SalientMatters 使用指南

### 第一步：配置许可证信息

打开 `lib/config/ai_sdk_config.dart` 文件，填入您的许可证信息：

```dart
class AISDKConstants {
  /// 许可证密钥 - 请填入您的实际密钥
  static const String licenseKey = 'YOUR_ACTUAL_LICENSE_KEY';
  
  /// 用户编码 - 请填入您的用户编码
  static const String userCode = 'YOUR_USER_CODE';
  
  /// 产品编码 - 请填入您的产品编码
  static const String productCode = 'YOUR_PRODUCT_CODE';
  
  // ... 其他配置保持默认即可
}
```

### 第二步：运行快速示例

```dart
import 'package:turing_art/utils/salient_matters_example.dart';

// 运行快速示例
await SalientMattersExample.quickExample();
```

### 第三步：集成到您的代码

```dart
import 'package:turing_art/platform/salient_matters_manager.dart';

// 1. 初始化
final manager = SalientMattersManager.instance;
final success = await manager.initializeFromConfig();

if (!success) {
  print('初始化失败，请检查许可证配置');
  return;
}

// 2. 准备图像数据（BGR格式）
final imageData = ImageData(
  data: yourBGRImageBytes,
  width: imageWidth,
  height: imageHeight,
  channels: 3,
);

// 3. 执行主体抠图
final result = await manager.matting(imageData);

if (result.isSuccess) {
  final maskData = result.imageData!; // 抠图结果
  print('抠图成功！蒙版大小: ${maskData.length}');
} else {
  print('抠图失败: ${result.errorMessage}');
}

// 4. 清理资源
manager.dispose();
```

## 🔥 新增功能：区域抠图

### 区域主体抠图 (Regional Matting)

用于只对图像的特定区域进行主体抠图，可以提高性能并减少不必要的计算。

```dart
import 'package:turing_art/ffi/services/salient_matters_service.dart';

// 定义要处理的区域
final region = RectData(
  x: 100,        // 左上角 x 坐标
  y: 100,        // 左上角 y 坐标
  width: 300,    // 区域宽度
  height: 400,   // 区域高度
);

// 执行区域主体抠图
final result = await SalientMattersService.regionalMatting(
  imageData,
  region,
  returnCroppedResult: true,           // 是否返回裁剪后的区域图像
  returnCroppedMattingResult: true,    // 是否返回区域抠图结果
);

if (result.isSuccess) {
  print('区域抠图成功！');
  
  // 获取完整图像的抠图结果（RGBA格式）
  final fullResult = result.fullResult;
  
  // 获取裁剪后的区域图像（可选）
  final croppedResult = result.croppedResult;
  
  // 获取区域抠图结果（RGBA格式，区域尺寸）
  final croppedMattingResult = result.croppedMattingResult;
}
```

### 区域交互式抠图 (Regional Interactive)

用于对图像的特定区域进行交互式抠图，支持前景/背景笔触。

```dart
// 准备裁剪后的区域图像
final croppedImage = ImageData(
  data: yourCroppedImageData,
  width: region.width,
  height: region.height,
  channels: 3,
);

// 准备交互笔触（与裁剪图像尺寸一致）
final foreStrokes = ImageData(
  data: yourForegroundStrokes,
  width: region.width,
  height: region.height,
  channels: 1,
);

final backStrokes = ImageData(
  data: yourBackgroundStrokes,
  width: region.width,
  height: region.height,
  channels: 1,
);

// 执行区域交互式抠图
final result = await SalientMattersService.regionalInteractive(
  croppedImage,
  region,
  foreStrokes: foreStrokes,
  backStrokes: backStrokes,
  returnRegionalResult: true,  // 是否返回区域抠图结果
);

if (result.isSuccess) {
  print('区域交互式抠图成功！');
  
  // 获取完整图像尺寸的抠图结果
  final fullResult = result.fullResult;
  
  // 获取区域抠图结果（可选）
  final regionalResult = result.regionalResult;
}
```

## ❓ 常见问题

### 1. 支持哪些图像格式？
输入图像必须是 BGR 格式（蓝-绿-红），3个通道，每个像素用 uint8 表示。

### 2. 如何转换图像格式？
如果您的图像是 RGB 格式，需要转换为 BGR：
```dart
// RGB 转 BGR
for (int i = 0; i < imageData.length; i += 3) {
  final temp = imageData[i]; // R
  imageData[i] = imageData[i + 2]; // R = B
  imageData[i + 2] = temp; // B = R
  // G 保持不变
}
```

### 3. 区域抠图有什么优势？
- **性能优化**：只处理感兴趣的区域，减少计算量
- **内存节省**：避免处理整个大图像
- **精度提升**：针对特定区域可以使用更合适的参数
- **交互友好**：支持局部交互操作

### 4. 配置检查失败怎么办？
在 debug 模式下，应用启动时会自动检查配置。如果看到警告信息，请：
1. 检查 `lib/config/ai_sdk_config.dart` 中的配置
2. 确保已填入真实的许可证信息（不是占位符）
3. 验证许可证密钥的有效性

## 📱 完整工作流程

### 主体抠图完整示例

```dart
import 'package:turing_art/ffi/services/salient_matters_service.dart';

Future<void> performMatting() async {
  try {
    // 1. 初始化
    final initResult = await SalientMattersService.initializeFromConfig();
    if (!initResult.isSuccess) {
      throw Exception('初始化失败: ${initResult.errorMessage}');
    }
    
    // 2. 加载和准备图像
    final imageData = await loadImageFromFile('path/to/image.jpg');
    if (imageData == null) {
      throw Exception('图像加载失败');
    }
    
    // 3. 执行抠图
    final result = await SalientMattersService.matting(imageData);
    if (!result.isSuccess) {
      throw Exception('抠图失败: ${result.errorMessage}');
    }
    
    // 4. 保存结果
    await saveResultToFile(result.imageData!, 'result_mask.png');
    print('抠图完成！结果已保存');
    
  } catch (e) {
    print('处理失败: $e');
  } finally {
    // 5. 清理资源
    SalientMattersService.dispose();
  }
}
```

### 交互式抠图示例

```dart
Future<void> performInteractiveMatting() async {
  try {
    await SalientMattersService.initializeFromConfig();
    
    // 准备原始图像
    final imageData = await loadImageFromFile('image.jpg');
    
    // 准备用户交互数据
    final foregroundStrokes = await loadStrokesFromFile('foreground.png');
    final backgroundStrokes = await loadStrokesFromFile('background.png');
    
    // 执行交互式抠图
    final result = await SalientMattersService.interactive(
      imageData!,
      foreStrokes: foregroundStrokes,
      backStrokes: backgroundStrokes,
    );
    
    if (result.isSuccess) {
      await saveResultToFile(result.imageData!, 'interactive_result.png');
      print('交互式抠图完成！');
    }
    
  } catch (e) {
    print('交互式抠图失败: $e');
  } finally {
    SalientMattersService.dispose();
  }
}
```

### 区域主体抠图示例

```dart
Future<void> performRegionalMatting() async {
  try {
    await SalientMattersService.initializeFromConfig();
    
    // 准备完整图像
    final imageData = await loadImageFromFile('large_image.jpg');
    
    // 定义感兴趣的区域
    final region = RectData(
      x: 200,
      y: 150,
      width: 400,
      height: 500,
    );
    
    // 执行区域主体抠图
    final result = await SalientMattersService.regionalMatting(
      imageData!,
      region,
      returnCroppedResult: true,
      returnCroppedMattingResult: true,
    );
    
    if (result.isSuccess) {
      // 保存完整图像结果
      await saveResultToFile(result.fullResult!, 'regional_full_result.png');
      
      // 保存裁剪后的区域图像（如果需要）
      if (result.croppedResult != null) {
        await saveResultToFile(result.croppedResult!, 'regional_cropped.png');
      }
      
      // 保存区域抠图结果
      if (result.croppedMattingResult != null) {
        await saveResultToFile(result.croppedMattingResult!, 'regional_matting.png');
      }
      
      print('区域主体抠图完成！');
    }
    
  } catch (e) {
    print('区域主体抠图失败: $e');
  } finally {
    SalientMattersService.dispose();
  }
}
```

### 区域交互式抠图示例

```dart
Future<void> performRegionalInteractive() async {
  try {
    await SalientMattersService.initializeFromConfig();
    
    // 定义处理区域
    final region = RectData(x: 100, y: 100, width: 300, height: 300);
    
    // 准备裁剪后的区域图像
    final croppedImage = await loadCroppedImageFromFile('cropped_region.jpg');
    
    // 准备交互笔触（与裁剪图像尺寸一致）
    final foreStrokes = await loadStrokesFromFile('fore_strokes.png');
    final backStrokes = await loadStrokesFromFile('back_strokes.png');
    
    // 可选：上次的完整图像抠图结果
    final latestFullMask = await loadMaskFromFile('previous_full_mask.png');
    
    // 执行区域交互式抠图
    final result = await SalientMattersService.regionalInteractive(
      croppedImage!,
      region,
      foreStrokes: foreStrokes,
      backStrokes: backStrokes,
      latestFullMask: latestFullMask,
      returnRegionalResult: true,
    );
    
    if (result.isSuccess) {
      // 保存完整图像尺寸的结果
      await saveResultToFile(result.fullResult!, 'regional_interactive_full.png');
      
      // 保存区域结果（如果需要）
      if (result.regionalResult != null) {
        await saveResultToFile(result.regionalResult!, 'regional_interactive_region.png');
      }
      
      print('区域交互式抠图完成！');
    }
    
  } catch (e) {
    print('区域交互式抠图失败: $e');
  } finally {
    SalientMattersService.dispose();
  }
}
```

### 交互式蒙版处理示例

```dart
Future<void> performInteractiveMaskProcessing() async {
  try {
    await SalientMattersService.initializeFromConfig();
    
    // 准备用户涂抹/擦除的路径蒙版
    final strokeMask = await loadImageFromFile('stroke_mask.png');
    
    // 可选：准备上一次的前景蒙版数据（单通道）
    final previousForegroundMask = await loadRawMaskData('foreground_mask.bin');
    
    // 可选：准备上一次的背景蒙版数据（单通道）
    final previousBackgroundMask = await loadRawMaskData('background_mask.bin');
    
    // 处理交互式蒙版数据
    final result = await SalientMattersService.processInteractiveMask(
      strokeMask!,
      previousForegroundMask, // 上一次的前景蒙版数据（单通道）
      previousBackgroundMask, // 上一次的背景蒙版数据（单通道）
      true, // 标记是前景涂抹(true)还是背景擦除(false)
    );
    
    if (result.isSuccess) {
      // 获取新的前景和背景蒙版数据
      final foregroundMask = result.foregroundMask;
      final backgroundMask = result.backgroundMask;
      
      print('蒙版处理完成！');
      print('前景蒙版数据长度: ${foregroundMask?.length ?? 0}');
      print('背景蒙版数据长度: ${backgroundMask?.length ?? 0}');
      
      // 保存结果用于下次使用
      if (foregroundMask != null) {
        await saveRawMaskData(foregroundMask, 'new_foreground_mask.bin');
      }
      if (backgroundMask != null) {
        await saveRawMaskData(backgroundMask, 'new_background_mask.bin');
      }
    }
    
  } catch (e) {
    print('交互式蒙版处理失败: $e');
  } finally {
    SalientMattersService.dispose();
  }
}

// 辅助函数：加载原始蒙版数据
Future<Uint8List?> loadRawMaskData(String filePath) async {
  try {
    final file = File(filePath);
    if (await file.exists()) {
      return await file.readAsBytes();
    }
  } catch (e) {
    print('加载蒙版数据失败: $e');
  }
  return null;
}

// 辅助函数：保存原始蒙版数据
Future<void> saveRawMaskData(Uint8List data, String filePath) async {
  try {
    final file = File(filePath);
    await file.writeAsBytes(data);
  } catch (e) {
    print('保存蒙版数据失败: $e');
  }
}
```

## 🔧 配置选项

### 网络尺寸选择
在 `ai_sdk_config.dart` 中可以调整默认网络尺寸：

```dart
/// 默认网络尺寸
/// 较小值(256-384): 速度快，精度稍低
/// 中等值(512): 平衡速度和精度 (推荐)
/// 较大值(768-1024): 精度高，速度较慢
static const int defaultNetSize = 512;
```

### 开发测试配置
如果有测试环境的许可证，可以在 `AISDKDevConfig` 中配置：

```dart
class AISDKDevConfig {
  static const String testLicenseKey = 'YOUR_TEST_KEY';
  static const String testUserCode = 'TEST_USER';
  static const String testProductCode = 'TEST_PRODUCT';
  
  /// 设置为 true 使用测试配置
  static const bool useTestConfig = true;
}
```

## 🎯 高级功能详解

### 区域抠图功能 (Regional Matting)

#### 使用场景
- **大图像处理**：处理高分辨率图像时，只对感兴趣的区域进行抠图
- **性能优化**：减少计算量，提高处理速度
- **局部优化**：对特定区域使用针对性的参数配置
- **内存控制**：避免因处理大图像导致的内存不足

#### 参数说明
- `image`: 输入的完整图像（RGBA或RGB格式）
- `region`: 要处理的区域信息（x, y, width, height）
- `croppedImage`: 可选的预裁剪区域图像，如果为空则内部按region裁剪
- `previousFullResult`: 上次的完整图像抠图结果，用于回贴结果
- `returnCroppedResult`: 是否返回裁剪后的区域图像
- `returnCroppedMattingResult`: 是否返回裁剪区域的主体抠图结果

#### 返回结果
- `fullResult`: 完整图像尺寸的抠图结果（RGBA格式）
- `croppedResult`: 裁剪后的区域图像（可选）
- `croppedMattingResult`: 裁剪区域的主体抠图结果（RGBA格式，区域尺寸）

### 区域交互式抠图功能 (Regional Interactive)

#### 使用场景
- **局部交互**：只对图像的特定区域进行交互式抠图
- **精细调整**：在已有抠图结果的基础上进行局部优化
- **多次迭代**：支持多次交互，逐步完善抠图效果
- **用户体验**：提供更好的交互响应速度

#### 参数说明
- `croppedImage`: 已裁剪的区域图像（RGBA或RGB格式）
- `region`: 区域信息，用于将结果回贴到完整图像
- `latestMask`: 上一次的区域抠图结果（与croppedImage大小一致）
- `foreStrokes`: 前景交互笔触（区域大小）
- `backStrokes`: 背景交互笔触（区域大小）
- `latestFullMask`: 上一次的完整图像抠图结果（完整图像尺寸）
- `returnRegionalResult`: 是否返回区域交互式抠图结果

#### 返回结果
- `fullResult`: 完整图像尺寸的抠图结果（RGBA格式）
- `regionalResult`: 区域交互式抠图结果（RGBA格式，区域尺寸）

### 交互式蒙版处理功能 (ProcessInteractiveMask)

这是一个新增的高级功能，用于精细化处理交互式抠图过程中的前景和背景蒙版数据。

#### 使用场景
- 需要精确控制前景/背景蒙版的生成和合并
- 在交互式抠图过程中保持历史状态
- 实现更精细的涂抹/擦除功能

#### 参数说明
- `strokeMask`: 当前涂抹/擦除的路径蒙版，不能为空
- `previousForegroundMask`: 上一次的前景蒙版数据（单通道），可以为空
- `previousBackgroundMask`: 上一次的背景蒙版数据（单通道），可以为空
- `isPainting`: 标记是涂抹(true)还是擦除(false)

#### 返回结果
- `foregroundMask`: 新的前景蒙版数据（单通道）
- `backgroundMask`: 新的背景蒙版数据（单通道，可能为null）

#### 内存管理注意事项
- 返回的蒙版数据会自动复制到Dart内存中
- 底层C++内存会在函数调用完成后自动释放
- 调用者需要负责管理返回的Dart数据

## 💡 最佳实践

### 1. 区域选择策略
```dart
// 根据图像尺寸自适应选择区域大小
RectData selectOptimalRegion(ImageData image, Point userClick) {
  final regionSize = math.min(image.width, image.height) ~/ 3;
  final x = math.max(0, userClick.x - regionSize ~/ 2);
  final y = math.max(0, userClick.y - regionSize ~/ 2);
  final width = math.min(regionSize, image.width - x);
  final height = math.min(regionSize, image.height - y);
  
  return RectData(x: x, y: y, width: width, height: height);
}
```

### 2. 内存优化
```dart
// 批量处理时及时释放资源
Future<void> batchProcessImages(List<String> imagePaths) async {
  await SalientMattersService.initializeFromConfig();
  
  try {
    for (final path in imagePaths) {
      final imageData = await loadImageFromFile(path);
      if (imageData != null) {
        final result = await SalientMattersService.matting(imageData);
        // 处理结果...
        
        // 及时释放大对象的引用
        imageData = null;
        result = null;
      }
      
      // 每处理几张图片后手动触发垃圾回收
      if (imagePaths.indexOf(path) % 10 == 0) {
        await Future.delayed(Duration(milliseconds: 100));
      }
    }
  } finally {
    SalientMattersService.dispose();
  }
}
```

### 3. 错误处理
```dart
Future<RegionalMattingResult> safeRegionalMatting(
  ImageData image,
  RectData region,
) async {
  try {
    // 验证输入参数
    if (!SalientMattersService.validateImageData(image)) {
      return const RegionalMattingResult(
        errorCode: SalientMattersErrorCode.invalidParam,
        errorMessage: '图像数据无效',
      );
    }
    
    // 执行区域抠图
    return await SalientMattersService.regionalMatting(image, region);
  } catch (e) {
    PGLog.e('区域抠图异常: $e');
    return RegionalMattingResult(
      errorCode: SalientMattersErrorCode.processFailed,
      errorMessage: '处理异常: $e',
    );
  }
}
```

## 📖 更多资源

- 详细文档: `SALIENT_MATTERS_INTEGRATION.md`
- API 参考: `lib/platform/README_SALIENT_MATTERS.md`
- 完整示例: `lib/utils/salient_matters_example.dart`
- 测试代码: `lib/utils/salient_matters_test.dart`

---
