# SugoiNativeSDK 使用指南

本文档介绍如何在Flutter项目中使用SugoiNativeSDK.dll库。

## 概述

SugoiNativeSDK是一个原生DLL库，提供了平台相关的功能。通过Flutter FFI，我们可以在Dart代码中调用其中的函数。

## 功能特性

- 加载SugoiNativeSDK.dll动态库
- 调用SDK_Env_Platform()函数获取平台信息
- 提供友好的Dart API封装
- 自动处理DLL路径和错误处理

## 快速开始

### 1. 导入依赖

```dart
import 'package:turing_art/ffi/ffi.dart';
```

### 2. 初始化SDK

```dart
// 在应用启动时初始化
void initializeApp() async {
  final success = await SugoiNativeSDKService.initialize();
  if (success) {
    print('SugoiNativeSDK初始化成功');
  } else {
    print('SugoiNativeSDK初始化失败');
  }
}
```

### 3. 获取平台信息

```dart
// 获取平台信息数值
final platformValue = await SugoiNativeSDKService.getPlatformInfo();
print('平台值: $platformValue');

// 获取平台信息描述
final platformDesc = await SugoiNativeSDKService.getPlatformDescription();
print('平台描述: $platformDesc');
```

## 完整示例

```dart
import 'package:flutter/material.dart';
import 'package:turing_art/ffi/ffi.dart';

class SugoiSDKDemo extends StatefulWidget {
  @override
  _SugoiSDKDemoState createState() => _SugoiSDKDemoState();
}

class _SugoiSDKDemoState extends State<SugoiSDKDemo> {
  String _status = '未初始化';
  String _platformInfo = '未知';
  Map<String, dynamic>? _sdkInfo;

  @override
  void initState() {
    super.initState();
    _initializeSDK();
  }

  /// 初始化SDK
  Future<void> _initializeSDK() async {
    setState(() {
      _status = '初始化中...';
    });

    try {
      final success = await SugoiNativeSDKService.initialize();
      if (success) {
        setState(() {
          _status = '初始化成功';
        });
        await _loadPlatformInfo();
      } else {
        setState(() {
          _status = '初始化失败';
        });
      }
    } catch (e) {
      setState(() {
        _status = '初始化异常: $e';
      });
    }
  }

  /// 加载平台信息
  Future<void> _loadPlatformInfo() async {
    try {
      final platformDesc = await SugoiNativeSDKService.getPlatformDescription();
      final sdkInfo = SugoiNativeSDKService.getSdkInfo();
      
      setState(() {
        _platformInfo = platformDesc;
        _sdkInfo = sdkInfo;
      });
    } catch (e) {
      setState(() {
        _platformInfo = '获取失败: $e';
      });
    }
  }

  /// 测试SDK功能
  Future<void> _testSDK() async {
    final testResult = await SugoiNativeSDKService.testSdk();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('SDK测试结果'),
        content: SingleChildScrollView(
          child: Text(testResult.toString()),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('SugoiNativeSDK 演示'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'SDK状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SizedBox(height: 8),
                    Text('状态: $_status'),
                    Text('平台信息: $_platformInfo'),
                    Text('可用性: ${SugoiNativeSDKService.isAvailable}'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            if (_sdkInfo != null)
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'SDK详细信息',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      SizedBox(height: 8),
                      ..._sdkInfo!.entries.map(
                        (entry) => Text('${entry.key}: ${entry.value}'),
                      ),
                    ],
                  ),
                ),
              ),
            SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _initializeSDK,
                  child: Text('重新初始化'),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _loadPlatformInfo,
                  child: Text('刷新信息'),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _testSDK,
                  child: Text('测试SDK'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 可选：在页面销毁时清理SDK资源
    // SugoiNativeSDKService.dispose();
    super.dispose();
  }
}
```

## API 参考

### SugoiNativeSDKService

#### 静态方法

- `Future<bool> initialize()` - 初始化SDK服务
- `Future<int?> getPlatformInfo()` - 获取平台信息数值
- `Future<String> getPlatformDescription()` - 获取平台信息描述
- `bool get isAvailable` - 检查服务是否可用
- `Map<String, dynamic> getSdkInfo()` - 获取SDK详细信息
- `Future<Map<String, dynamic>> testSdk()` - 测试SDK功能
- `void dispose()` - 清理服务资源

### SugoiNativeSDKBindings

底层FFI绑定类，通常不需要直接使用。

## 注意事项

1. **DLL路径**: SDK会自动查找`turing_art_Data/Plugins/x86_64/SugoiNativeSDK.dll`
2. **初始化**: 在使用任何SDK功能前，必须先调用`initialize()`
3. **错误处理**: 所有方法都包含适当的错误处理，返回null或false表示失败
4. **平台支持**: 目前主要支持Windows平台
5. **资源管理**: 应用退出时可以调用`dispose()`清理资源

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查DLL文件是否存在于正确路径
   - 确认DLL文件没有被其他程序占用
   - 检查应用是否有足够的权限访问DLL文件

2. **函数调用失败**
   - 确认SDK已正确初始化
   - 检查函数签名是否正确
   - 查看调试输出中的错误信息

3. **路径问题**
   - 确认可执行文件和DLL文件的相对位置正确
   - 检查路径分隔符是否正确（Windows使用反斜杠）

### 调试技巧

1. 使用`getSdkInfo()`获取详细的SDK状态信息
2. 使用`testSdk()`进行全面的功能测试
3. 查看Flutter调试控制台中的日志输出
4. 在Release模式下测试，确保路径正确

## 扩展功能

如果需要调用SugoiNativeSDK中的其他函数，可以：

1. 在`SugoiNativeSDKBindings`中添加新的函数签名
2. 在`SugoiNativeSDKService`中添加对应的高级API
3. 更新导出文件和文档

示例：

```dart
// 在bindings中添加新函数
typedef NewFunctionNative = Int32 Function(Pointer<Utf8> param);
typedef NewFunctionDart = int Function(Pointer<Utf8> param);

// 在service中添加封装
static Future<int?> callNewFunction(String param) async {
  // 实现逻辑
}
```