import 'dart:ffi';
import 'dart:typed_data';

import 'package:ffi/ffi.dart';

/// SalientMatters错误码
enum SalientMattersErrorCode {
  success(0),
  initFailed(-1),
  notInitialized(-2),
  invalidParam(-3),
  fileNotFound(-4),
  processFailed(-5),
  saveFailed(-6),
  unknown(-99);

  const SalientMattersErrorCode(this.value);
  final int value;

  static SalientMattersErrorCode fromValue(int value) {
    return SalientMattersErrorCode.values
        .firstWhere((e) => e.value == value, orElse: () => unknown);
  }
}

/// 算法类型
enum SalientMattersType {
  interactive(0), // 交互式抠图
  matting(1), // 主体抠图
  tracking(2); // 蒙版跟踪

  const SalientMattersType(this.value);
  final int value;
}

/// 处理选项结构体
final class SalientMattersOptions extends Struct {
  @Uint32()
  external int netSize;

  @Int32()
  external int type;

  external Pointer<Utf8> key;
  external Pointer<Utf8> userCode;
  external Pointer<Utf8> prodCode;

  @Array(4)
  external Array<Uint8> outputColor;
}

/// 图像数据结构体
final class SalientMattersImage extends Struct {
  external Pointer<Uint8> data;

  @Int32()
  external int width;

  @Int32()
  external int height;

  @Int32()
  external int channels;

  @Int32()
  external int step;
}

/// 裁剪/贴图区域结构体
final class SalientMattersRect extends Struct {
  @Int32()
  external int x; // 区域的左上角x坐标

  @Int32()
  external int y; // 区域的左上角y坐标

  @Int32()
  external int width; // 区域的宽度

  @Int32()
  external int height; // 区域的高度
}

/// SalientMatters选项配置类
class SalientMattersConfig {
  final int netSize;
  final SalientMattersType type;
  final String key;
  final String userCode;
  final String prodCode;
  final List<int> outputColor; // RGBA值，范围0-255

  const SalientMattersConfig({
    this.netSize = 768,
    this.type = SalientMattersType.matting,
    this.key = '',
    this.userCode = '',
    this.prodCode = '',
    this.outputColor = const [255, 0, 0, 76], // 默认红色+0.3透明度
  });

  /// 获取默认配置
  static const SalientMattersConfig defaultConfig = SalientMattersConfig();
}

/// SalientMatters处理结果
class SalientMattersResult {
  final SalientMattersErrorCode errorCode;
  final String? errorMessage;
  final bool isSuccess;
  final Uint8List? imageData;

  const SalientMattersResult({
    required this.errorCode,
    this.errorMessage,
    this.imageData,
  }) : isSuccess = errorCode == SalientMattersErrorCode.success;

  @override
  String toString() {
    return 'SalientMattersResult(errorCode: $errorCode, errorMessage: $errorMessage, isSuccess: $isSuccess)';
  }
}

/// 区域抠图处理结果
class RegionalMattingResult {
  final SalientMattersErrorCode errorCode;
  final String? errorMessage;
  final bool isSuccess;
  final Uint8List? fullResult; // 完整图像尺寸的抠图结果（RGBA格式）
  final Uint8List? croppedResult; // 裁剪后的区域图像（可选）
  final Uint8List? croppedMattingResult; // 裁剪区域的主体抠图结果（RGBA格式，区域尺寸）

  const RegionalMattingResult({
    required this.errorCode,
    this.errorMessage,
    this.fullResult,
    this.croppedResult,
    this.croppedMattingResult,
  }) : isSuccess = errorCode == SalientMattersErrorCode.success;

  @override
  String toString() {
    return 'RegionalMattingResult(errorCode: $errorCode, errorMessage: $errorMessage, isSuccess: $isSuccess, fullResultSize: ${fullResult?.length}, croppedResultSize: ${croppedResult?.length}, croppedMattingResultSize: ${croppedMattingResult?.length})';
  }
}

/// 区域交互式抠图处理结果
class RegionalInteractiveResult {
  final SalientMattersErrorCode errorCode;
  final String? errorMessage;
  final bool isSuccess;
  final Uint8List? fullResult; // 完整图像尺寸的抠图结果（RGBA格式）
  final Uint8List? regionalResult; // 区域交互式抠图结果（RGBA格式，区域尺寸）

  const RegionalInteractiveResult({
    required this.errorCode,
    this.errorMessage,
    this.fullResult,
    this.regionalResult,
  }) : isSuccess = errorCode == SalientMattersErrorCode.success;

  @override
  String toString() {
    return 'RegionalInteractiveResult(errorCode: $errorCode, errorMessage: $errorMessage, isSuccess: $isSuccess, fullResultSize: ${fullResult?.length}, regionalResultSize: ${regionalResult?.length})';
  }
}

/// 交互式蒙版处理结果
class ProcessInteractiveMaskResult {
  final SalientMattersErrorCode errorCode;
  final String? errorMessage;
  final bool isSuccess;
  final Uint8List? foregroundMask; // 新的前景蒙版数据（单通道）
  final Uint8List? backgroundMask; // 新的背景蒙版数据（单通道），可能为null

  const ProcessInteractiveMaskResult({
    required this.errorCode,
    this.errorMessage,
    this.foregroundMask,
    this.backgroundMask,
  }) : isSuccess = errorCode == SalientMattersErrorCode.success;

  @override
  String toString() {
    return 'ProcessInteractiveMaskResult(errorCode: $errorCode, errorMessage: $errorMessage, isSuccess: $isSuccess, foregroundMaskSize: ${foregroundMask?.length}, backgroundMaskSize: ${backgroundMask?.length})';
  }
}

/// 图像数据类
class ImageData {
  final Uint8List data;
  final int width;
  final int height;
  final int channels;

  const ImageData({
    required this.data,
    required this.width,
    required this.height,
    required this.channels,
  });

  int get step => width * channels;
}

/// 区域矩形类
class RectData {
  final int x;
  final int y;
  final int width;
  final int height;

  const RectData({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  @override
  String toString() {
    return 'RectData(x: $x, y: $y, width: $width, height: $height)';
  }
}
