import 'dart:ffi';

import 'package:ffi/ffi.dart';

// 错误码枚举
class RawDecoderErrorCode {
  static const int success = 0;
  static const int errorInitFailed = -1;
  static const int errorNotInitialized = -2;
  static const int errorInvalidParam = -3;
  static const int errorFileNotFound = -4;
  static const int errorDecodeFailed = -5;
  static const int errorSaveFailed = -6;
  static const int errorUnknown = -99;
}

// 解码选项结构体
final class RawDecoderOptions extends Struct {
  @Int32()
  external int bitDepth;

  @Int32()
  external int maxDim;

  @Int32()
  external int thumbnail;

  @Int32()
  external int denoise;

  @Int32()
  external int clarity;

  @Float()
  external double exposure;

  external Pointer<Utf8> outputFormat;
}
