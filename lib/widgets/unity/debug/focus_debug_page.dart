import 'dart:io';
import 'package:flutter/material.dart';
import 'package:turingart/widgets/unity/debug/focus_debug_widget.dart';
import 'package:turingart/widgets/unity/helper/focus_debug_helper.dart';
import 'package:turingart/widgets/unity/helper/windows_unity_helper.dart';

/// 焦点调试页面
/// 提供完整的焦点调试和测试界面
class FocusDebugPage extends StatefulWidget {
  const FocusDebugPage({Key? key}) : super(key: key);

  @override
  State<FocusDebugPage> createState() => _FocusDebugPageState();
}

class _FocusDebugPageState extends State<FocusDebugPage> {
  final TextEditingController _testInputController = TextEditingController();
  final FocusNode _testInputFocusNode = FocusNode();
  bool _unityVisible = false;

  @override
  void initState() {
    super.initState();
    _loadUnityVisibility();
  }

  @override
  void dispose() {
    _testInputController.dispose();
    _testInputFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadUnityVisibility() async {
    if (Platform.isWindows) {
      final visible = await WindowsUnityHelper.getUnityVisibility();
      setState(() {
        _unityVisible = visible;
      });
    }
  }

  Future<void> _toggleUnityVisibility() async {
    if (!Platform.isWindows) return;

    final newVisibility = !_unityVisible;
    await WindowsUnityHelper.setUnityVisibility(visible: newVisibility);
    setState(() {
      _unityVisible = newVisibility;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('焦点调试工具'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: Platform.isWindows ? _buildWindowsContent() : _buildNonWindowsContent(),
    );
  }

  Widget _buildNonWindowsContent() {
    return const Center(
      child: Card(
        margin: EdgeInsets.all(32),
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.info_outline, size: 64, color: Colors.orange),
              SizedBox(height: 16),
              Text(
                '焦点调试工具',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                '此工具仅在Windows平台可用',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWindowsContent() {
    return Row(
      children: [
        // 左侧控制面板
        SizedBox(
          width: 300,
          child: _buildControlPanel(),
        ),
        // 右侧焦点调试工具
        Expanded(
          child: Container(
            margin: const EdgeInsets.all(8),
            child: const FocusDebugWidget(),
          ),
        ),
      ],
    );
  }

  Widget _buildControlPanel() {
    return Container(
      color: Colors.grey.shade100,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '测试控制面板',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Unity控制
            _buildUnityControls(),
            const SizedBox(height: 16),

            // Flutter输入测试
            _buildFlutterInputTest(),
            const SizedBox(height: 16),

            // 焦点测试场景
            _buildFocusTestScenarios(),
            const SizedBox(height: 16),

            // 快速操作
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildUnityControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Unity控制', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                Text('Unity可见: '),
                Switch(
                  value: _unityVisible,
                  onChanged: (value) => _toggleUnityVisibility(),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await WindowsUnityHelper.setFocusToUnityWindow();
                },
                child: const Text('设置焦点到Unity'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlutterInputTest() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Flutter输入测试', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _testInputController,
              focusNode: _testInputFocusNode,
              decoration: const InputDecoration(
                labelText: '测试输入框',
                hintText: '在这里输入测试文本',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _testInputFocusNode.requestFocus();
                    },
                    child: const Text('获取焦点'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _testInputFocusNode.unfocus();
                    },
                    child: const Text('失去焦点'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFocusTestScenarios() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('焦点测试场景', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _buildTestButton('场景1: Flutter对话框', () async {
              await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
                flutterWindowFocus: true,
              );
              _showTestDialog();
            }),
            _buildTestButton('场景2: Unity输入', () async {
              await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
                flutterWindowFocus: false,
              );
              await WindowsUnityHelper.setFocusToUnityWindow();
            }),
            _buildTestButton('场景3: 窗口切换', () async {
              // 模拟窗口切换场景
              await FocusDebugHelper.forceFocusToFlutter();
              await Future.delayed(const Duration(seconds: 1));
              await FocusDebugHelper.forceFocusToUnity();
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(String text, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade50,
            foregroundColor: Colors.blue.shade700,
          ),
          child: Text(text),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('快速操作', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _buildTestButton('打印焦点信息', () async {
              await FocusDebugHelper.printFocusDebugInfo();
            }),
            _buildTestButton('检查Unity焦点', () async {
              final isUnityFocused = await FocusDebugHelper.isUnityFocused();
              _showSnackBar('Unity焦点状态: ${isUnityFocused ? "有焦点" : "无焦点"}');
            }),
            _buildTestButton('检查Flutter焦点', () async {
              final isFlutterFocused = await FocusDebugHelper.isFlutterFocused();
              _showSnackBar('Flutter焦点状态: ${isFlutterFocused ? "有焦点" : "无焦点"}');
            }),
          ],
        ),
      ),
    );
  }

  void _showTestDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('测试对话框'),
        content: const Text('这是一个测试对话框，用于测试焦点控制。\n'
            '在显示此对话框时，应该阻止自动焦点转移到Unity。'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 对话框关闭后恢复焦点控制
              WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
                flutterWindowFocus: false,
              );
            },
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
