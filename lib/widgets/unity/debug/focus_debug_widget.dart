import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:turingart/widgets/unity/helper/focus_debug_helper.dart';
import 'package:turingart/utils/pg_log.dart';

/// 焦点调试界面组件
/// 提供可视化的焦点状态监控和控制界面
class FocusDebugWidget extends StatefulWidget {
  const FocusDebugWidget({Key? key}) : super(key: key);

  @override
  State<FocusDebugWidget> createState() => _FocusDebugWidgetState();
}

class _FocusDebugWidgetState extends State<FocusDebugWidget> {
  FocusInfo? _currentFocusInfo;
  bool _isMonitoring = false;
  Timer? _refreshTimer;
  final List<String> _focusHistory = [];
  final ScrollController _historyScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    if (Platform.isWindows) {
      _refreshFocusInfo();
    }
  }

  @override
  void dispose() {
    _stopMonitoring();
    _refreshTimer?.cancel();
    _historyScrollController.dispose();
    super.dispose();
  }

  Future<void> _refreshFocusInfo() async {
    final focusInfo = await FocusDebugHelper.getCurrentFocusInfo();
    if (mounted) {
      setState(() {
        _currentFocusInfo = focusInfo;
      });
    }
  }

  void _startMonitoring() async {
    if (_isMonitoring) return;

    await FocusDebugHelper.startFocusMonitoring(
      interval: const Duration(milliseconds: 500),
      onFocusChanged: (focusInfo) {
        if (mounted) {
          setState(() {
            _currentFocusInfo = focusInfo;
          });
          _addToHistory('焦点变化: ${focusInfo.focusStatus} -> ${focusInfo.focusedWindowTitle}');
        }
      },
    );

    setState(() {
      _isMonitoring = true;
    });

    _addToHistory('开始焦点监控');
  }

  void _stopMonitoring() async {
    if (!_isMonitoring) return;

    await FocusDebugHelper.stopFocusMonitoring();
    setState(() {
      _isMonitoring = false;
    });

    _addToHistory('停止焦点监控');
  }

  void _addToHistory(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _focusHistory.add('[$timestamp] $message');
      if (_focusHistory.length > 100) {
        _focusHistory.removeAt(0);
      }
    });

    // 自动滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_historyScrollController.hasClients) {
        _historyScrollController.animateTo(
          _historyScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!Platform.isWindows) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('焦点调试工具仅在Windows平台可用'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和控制按钮
            Row(
              children: [
                const Text(
                  '焦点调试工具',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                ElevatedButton(
                  onPressed: _refreshFocusInfo,
                  child: const Text('刷新'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isMonitoring ? _stopMonitoring : _startMonitoring,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isMonitoring ? Colors.red : Colors.green,
                  ),
                  child: Text(_isMonitoring ? '停止监控' : '开始监控'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 当前焦点状态
            _buildCurrentFocusInfo(),
            const SizedBox(height: 16),

            // 控制按钮
            _buildControlButtons(),
            const SizedBox(height: 16),

            // 焦点历史
            _buildFocusHistory(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentFocusInfo() {
    if (_currentFocusInfo == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('正在获取焦点信息...'),
        ),
      );
    }

    final info = _currentFocusInfo!;
    final statusColor = _getFocusStatusColor(info.focusStatus);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text('当前焦点状态: ', style: TextStyle(fontWeight: FontWeight.bold)),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    info.focusStatus.toUpperCase(),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildInfoRow('Flutter窗口有焦点', info.isFlutterFocused),
            _buildInfoRow('Unity窗口有焦点', info.isUnityFocused),
            _buildInfoRow('Unity窗口可见', info.unityVisible),
            _buildInfoRow('阻止自动焦点转移', info.preventAutoFocusToUnity),
            const SizedBox(height: 8),
            Text('当前焦点窗口: ${info.focusedWindowTitle}'),
            Text('窗口类名: ${info.focusedWindowClass}'),
            Text('前台窗口: ${info.foregroundWindowTitle}'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('焦点控制', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    final success = await FocusDebugHelper.forceFocusToFlutter();
                    _addToHistory('强制设置焦点到Flutter: ${success ? '成功' : '失败'}');
                  },
                  child: const Text('焦点到Flutter'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    final success = await FocusDebugHelper.forceFocusToUnity();
                    _addToHistory('强制设置焦点到Unity: ${success ? '成功' : '失败'}');
                  },
                  child: const Text('焦点到Unity'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    await FocusDebugHelper.printFocusDebugInfo();
                    _addToHistory('打印详细调试信息到控制台');
                  },
                  child: const Text('打印调试信息'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFocusHistory() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text('焦点历史', style: TextStyle(fontWeight: FontWeight.bold)),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _focusHistory.clear();
                      });
                    },
                    child: const Text('清空'),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ListView.builder(
                    controller: _historyScrollController,
                    itemCount: _focusHistory.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        child: Text(
                          _focusHistory[index],
                          style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getFocusStatusColor(String status) {
    switch (status) {
      case 'flutter':
        return Colors.blue;
      case 'unity':
        return Colors.green;
      case 'none':
        return Colors.grey;
      case 'other':
        return Colors.orange;
      default:
        return Colors.red;
    }
  }
}
