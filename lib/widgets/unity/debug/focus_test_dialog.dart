import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/widgets/unity/helper/focus_debug_helper.dart';
import 'package:turing_art/widgets/unity/helper/windows_unity_helper.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 焦点测试对话框
/// 用于测试在显示Flutter对话框时Unity是否正确停止接收键盘事件
class FocusTestDialog extends StatefulWidget {
  const FocusTestDialog({Key? key}) : super(key: key);

  @override
  State<FocusTestDialog> createState() => _FocusTestDialogState();

  static Future<void> showOnUnity() async {
    if (PGDialog.isDialogVisible(DialogTags.wechatGift)) {
      PGLog.d('WechatGiftDialog showOnUnity, but dialog already exist, return');
      return Future.value();
    }
    await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
      flutterWindowFocus: true,
    );

    PGDialog.showCustomDialogOnUnity(
      width: 500,
      height: 820,
      needBlur: false,
      tag: DialogTags.wechatGift,
      child: const FocusTestDialog(),
    );
  }
}

class _FocusTestDialogState extends State<FocusTestDialog> {
  final TextEditingController _inputController = TextEditingController();
  final FocusNode _inputFocusNode = FocusNode();
  final FocusNode _dialogFocusNode = FocusNode();
  FocusInfo? _focusInfo;
  bool _isMonitoring = false;
  int _deleteKeyPressCount = 0;

  @override
  void initState() {
    super.initState();
    _setupDialog();
  }

  @override
  void dispose() {
    _cleanupDialog();
    _inputController.dispose();
    _inputFocusNode.dispose();
    _dialogFocusNode.dispose();
    super.dispose();
  }

  Future<void> _setupDialog() async {
    if (!Platform.isWindows) return;

    // 设置阻止自动焦点转移到Unity
    await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
      flutterWindowFocus: true,
    );

    // 开始监控焦点状态
    await _startFocusMonitoring();

    PGLog.d('焦点测试对话框已设置，Unity应该停止接收键盘事件');
  }

  Future<void> _cleanupDialog() async {
    if (!Platform.isWindows) return;

    // 停止监控
    await _stopFocusMonitoring();

    // 恢复焦点控制
    await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
      flutterWindowFocus: false,
    );

    PGLog.d('焦点测试对话框已清理，恢复Unity焦点控制');
  }

  Future<void> _startFocusMonitoring() async {
    if (_isMonitoring) return;

    await FocusDebugHelper.startFocusMonitoring(
      interval: const Duration(milliseconds: 500),
      onFocusChanged: (focusInfo) {
        if (mounted) {
          setState(() {
            _focusInfo = focusInfo;
          });
        }
      },
    );

    setState(() {
      _isMonitoring = true;
    });
  }

  Future<void> _stopFocusMonitoring() async {
    if (!_isMonitoring) return;

    await FocusDebugHelper.stopFocusMonitoring();
    setState(() {
      _isMonitoring = false;
    });
  }

  bool _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent &&
        event.logicalKey == LogicalKeyboardKey.delete) {
      setState(() {
        _deleteKeyPressCount++;
      });
      PGLog.d('Flutter对话框检测到Delete键按下 (第${_deleteKeyPressCount}次)');
      return true; // 消费这个事件
    }
    return false; // 不消费其他事件
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _dialogFocusNode,
      // onKeyEvent: _handleKeyEvent,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.delete) {
          setState(() {
            _deleteKeyPressCount++;
          });
          PGLog.d('Flutter对话框检测到Delete键按下 (第${_deleteKeyPressCount}次)');
          return KeyEventResult.handled; // 消费这个事件
        }
        return KeyEventResult.ignored; // 不消费其他事件
      },
      autofocus: true,
      child: AlertDialog(
        title: const Text('焦点测试对话框'),
        content: SizedBox(
          width: 500,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '测试说明：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                '1. 此对话框显示时，Unity应该停止接收键盘事件\n'
                '2. 在Unity中按Delete键应该没有反应\n'
                '3. 在下方输入框中输入应该正常工作\n'
                '4. 关闭对话框后Unity应该恢复接收键盘事件\n'
                '5. 特别测试Delete键是否被正确拦截',
                style: TextStyle(fontSize: 13),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade300),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  '⚠️ 重点测试：在此对话框显示期间，在Unity窗口中按Delete键，Unity不应该有任何反应',
                  style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange),
                ),
              ),
              const SizedBox(height: 16),

              // 测试输入框
              TextField(
                controller: _inputController,
                focusNode: _inputFocusNode,
                decoration: const InputDecoration(
                  labelText: '测试输入框',
                  hintText: '在这里输入测试Unity是否收到键盘事件',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  PGLog.d('Flutter输入框收到输入: $value');
                },
              ),
              const SizedBox(height: 16),

              // 焦点状态显示
              if (_focusInfo != null) _buildFocusStatus(),

              const SizedBox(height: 16),

              // 测试按钮
              _buildTestButtons(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () async {
              await _cleanupDialog();
              PGDialog.dismiss(tag: DialogTags.wechatGift);
            },
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () async {
              await FocusDebugHelper.printFocusDebugInfo();
            },
            child: const Text('打印调试信息'),
          ),
        ],
      ),
    );
  }

  Widget _buildFocusStatus() {
    final info = _focusInfo!;

    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前焦点状态：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildStatusRow('焦点状态', info.focusStatus),
            _buildStatusRow('Flutter有焦点', info.isFlutterFocused.toString()),
            _buildStatusRow('Unity有焦点', info.isUnityFocused.toString()),
            _buildStatusRow('Unity可见', info.unityVisible.toString()),
            _buildStatusRow(
                '阻止自动焦点转移', info.preventAutoFocusToUnity.toString()),
            _buildStatusRow(
                'Flutter对话框活动', info.isFlutterDialogActive.toString()),
            _buildStatusRow(
                'Unity应该接收输入', info.shouldUnityReceiveInput.toString(),
                isImportant: true),
            _buildStatusRow('Delete键按下次数', _deleteKeyPressCount.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value,
      {bool isImportant = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: isImportant ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: isImportant
                  ? (value == 'false' ? Colors.green : Colors.red)
                  : Colors.black87,
              fontWeight: isImportant ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestButtons() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        ElevatedButton(
          onPressed: () {
            _inputFocusNode.requestFocus();
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
          child: const Text('焦点到输入框'),
        ),
        ElevatedButton(
          onPressed: () async {
            final success = await FocusDebugHelper.forceFocusToFlutter();
            PGLog.d('强制设置焦点到Flutter: ${success ? '成功' : '失败'}');
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
          child: const Text('强制焦点到Flutter'),
        ),
        ElevatedButton(
          onPressed: () async {
            final success = await FocusDebugHelper.forceFocusToUnity();
            PGLog.d('强制设置焦点到Unity: ${success ? '成功' : '失败'}');
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
          child: const Text('强制焦点到Unity'),
        ),
        ElevatedButton(
          onPressed: () async {
            final focusInfo = await FocusDebugHelper.getCurrentFocusInfo();
            if (focusInfo != null) {
              setState(() {
                _focusInfo = focusInfo;
              });
            }
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
          child: const Text('刷新状态'),
        ),
      ],
    );
  }
}

/// 显示焦点测试对话框的便捷方法
Future<void> showFocusTestDialog(BuildContext context) async {
  return showDialog<void>(
    context: context,
    barrierDismissible: false, // 防止意外关闭
    builder: (BuildContext context) {
      return const FocusTestDialog();
    },
  );
}
