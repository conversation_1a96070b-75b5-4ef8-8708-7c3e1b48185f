import 'package:flutter/material.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';

import 'unity_widget_interface.dart';

class UniversalUnityWidget extends UnityWidgetInterface {
  const UniversalUnityWidget({
    super.key,
    super.onUnityCreated,
    super.onUnityMessage,
  });

  @override
  State<UnityWidgetInterface> createState() => _UniversalUnityWidgetState();
}

class _UniversalUnityWidgetState extends State<UniversalUnityWidget> {
  @override
  Widget build(BuildContext context) {
    return UnityWidget(
      onUnityCreated: (controller) {
        widget.onUnityCreated?.call();
      },
      onUnityMessage: widget.onUnityMessage,
    );
  }
}
