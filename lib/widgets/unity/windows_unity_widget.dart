import 'package:flutter/material.dart';

import '../../routing/router.dart';
import 'helper/windows_unity_helper.dart';
import 'unity_widget_interface.dart';

class WindowsUnityWidget extends UnityWidgetInterface {
  const WindowsUnityWidget({
    super.key,
    super.onUnityCreated,
    super.onUnityMessage,
  });

  @override
  State<UnityWidgetInterface> createState() => _WindowsUnityWidgetState();
}

class _WindowsUnityWidgetState extends State<WindowsUnityWidget>
    with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route != null) {
      routeObserver.subscribe(this, route as PageRoute);
    }
  }

  @override
  void initState() {
    WindowsUnityHelper.setupMessageHandler(_setupUnityMessageHandler);
    WindowsUnityHelper.launchUnity();
    widget.onUnityCreated?.call();
    super.initState();
  }

  @override
  void didPopNext() {
    // 路由状态变更时需要重置messageHandler
    WindowsUnityHelper.setupMessageHandler(_setupUnityMessageHandler);
  }

  @override
  void dispose() {
    // 根据路由状态决定是否清理
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.expand();
  }

  void _setupUnityMessageHandler(dynamic message) {
    widget.onUnityMessage?.call(message);
  }
}
