import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

typedef UnityMessageCallback = void Function(dynamic message);

class WindowsUnityHelper {
  static const _launchUnityPlatform = MethodChannel('unity_launcher_channel');
  static const _defaultDebugPath = r'.\build\windows\x64\runner\Debug';
  static const _defaultReleasePath = r'.';
  static const _handleMessageChannel = MethodChannel('unity_message_handler');

  static bool get _isWindows => Platform.isWindows;

  /// 获取设备像素比例
  static double get _devicePixelRatio => ui.window.devicePixelRatio;

  /// 将DP转换为物理像素
  static int _dpToPx(double dp) {
    return (dp * _devicePixelRatio).round();
  }

  static void setupMessageHandler(UnityMessageCallback onMessage) {
    if (_isWindows) {
      // Windows平台消息通过原生通道转发
      _handleMessageChannel.setMethodCallHandler((call) async {
        if (call.method == 'onUnityMessage') {
          onMessage(call.arguments);
        }
        return null;
      });
    }
  }

  static void clearMessageHandler() {
    _handleMessageChannel.setMethodCallHandler(null);
  }

  static Future<void> launchUnity() async {
    if (_isWindows) {
      const unityPath = kReleaseMode ? _defaultReleasePath : _defaultDebugPath;
      PGLog.d('Launching Unity with path: $unityPath');
      try {
        // 获取统一的日志文件路径
        final logPath = await FileManager().getUnityLogFilePath();

        await _launchUnityPlatform.invokeMethod('launchUnity', {
          'unityPath': unityPath,
          'logPath': logPath,
        });
      } on PlatformException catch (e) {
        PGLog.w("Error launching Unity: '${e.message}'.");
      }
    }
  }

  static Future<void> setUnityVisibility({required bool visible}) async {
    if (_isWindows) {
      try {
        await _launchUnityPlatform.invokeMethod('setUnityVisibility', visible);
      } on PlatformException catch (e) {
        PGLog.w("Failed to set Unity visibility: '${e.message}'.");
      }
    }
  }

  /// 当Unity窗口可见时，设置Flutter窗口的焦点（在unity窗口上，客户端需要输入的时候设置）
  static Future<void> setFocusToFlutterWindowWhenUnityVisible(
      {bool flutterWindowFocus = true}) async {
    if (_isWindows) {
      try {
        await _launchUnityPlatform.invokeMethod(
            'setFocusToFlutterWindowWhenUnityVisible', flutterWindowFocus);
      } on PlatformException catch (e) {
        PGLog.w("Failed to set Unity focus: '${e.message}'.");
      }
    }
  }

  /// 获取Unity窗口的可见性状态
  ///
  /// 返回一个布尔值，表示Unity窗口当前是否可见
  /// 如果Unity窗口不存在或发生错误，将返回false
  static Future<bool> getUnityVisibility() async {
    if (_isWindows) {
      try {
        final result =
            await _launchUnityPlatform.invokeMethod<bool>('getUnityVisibility');
        return result ?? false;
      } on PlatformException catch (e) {
        PGLog.w("获取Unity可见性状态失败: '${e.message}'.");
        return false;
      }
    }
    return false;
  }

  /// 在Unity窗口上显示Flutter对话框
  ///
  /// 此功能仅在Windows平台上可用，在其他平台上调用将抛出异常。
  ///
  /// [dialogBuilder] 对话框构建器函数
  /// [dialogPosition] 对话框位置，相对于屏幕左上角的偏移量（centerInWindow为true时，忽略参数居中显示）
  /// [dialogSize] 对话框大小
  /// [centerInWindow] 是否将对话框居中显示在窗口中，默认为true
  /// [margin] 对话框周围的额外边距，确保完全覆盖对话框，通常为0
  /// [unityAlpha] 显示对话框后Unity窗口的透明度 (0-255)，默认为60%153
  /// [roundRadius] 对话框区域的圆角半径，默认为0
  /// [barrierColor] 对话框背景颜色，默认为透明
  /// [barrierDismissible] 点击背景是否关闭对话框，默认为true
  ///
  /// 返回对话框的结果，类型由对话框决定
  ///
  /// 抛出 [UnsupportedError] 如果在非Windows平台上调用
  static Future<T?> showDialogOnUnity<T>({
    required BuildContext context,
    required Widget Function(BuildContext) dialogBuilder,
    Offset dialogPosition = const Offset(0, 0),
    bool centerInWindow = true,
    required Size dialogSize,
    int margin = 0,
    int unityAlpha = 153,
    double roundRadius = 0,

    /// 60% 透明
    Color barrierColor = Colors.transparent,
    bool barrierDismissible = true,
  }) async {
    if (!_isWindows) {
      // 非Windows平台抛出异常
      PGLog.e("showDialogOnUnity: 此功能仅支持Windows平台");
      throw UnsupportedError('showDialogOnUnity仅在Windows平台上可用');
    }

    // 计算对话框区域（包含边距）
    final x = _dpToPx(dialogPosition.dx - margin);
    final y = _dpToPx(dialogPosition.dy - margin);
    final width = _dpToPx(dialogSize.width + margin * 2);
    final height = _dpToPx(dialogSize.height + margin * 2);
    final roundRadiusPixel = _dpToPx(roundRadius);

    PGLog.d(
        "showDialogOnUnity: 显示对话框，位置=(${dialogPosition.dx}, ${dialogPosition.dy})，大小=(${dialogSize.width}, ${dialogSize.height})");
    PGLog.d(
        "showDialogOnUnity: 排除区域=($x, $y, $width, $height)，设备像素比=$_devicePixelRatio");

    // 设置Unity窗口区域和透明度
    await excludeUnityWindowRegion(
        x: x,
        y: y,
        width: width,
        height: height,
        center: centerInWindow,
        roundRadius: roundRadiusPixel);

    await setUnityWindowTransparency(unityAlpha);

    // 计算对话框的位置
    if (centerInWindow) {
      // 使用LayoutBuilder获取Stack的实际大小，而不是使用MediaQuery
      return showDialog<T>(
        context: context,
        barrierColor: barrierColor,
        barrierDismissible: barrierDismissible,
        builder: (context) => LayoutBuilder(
          builder: (context, constraints) {
            // 获取Stack的实际大小
            final stackSize = Size(constraints.maxWidth, constraints.maxHeight);

            // 计算居中位置
            final centeredPosition = Offset(
              (stackSize.width - dialogSize.width) / 2,
              (stackSize.height - dialogSize.height) / 2,
            );

            PGLog.d(
                "showDialogOnUnity: 居中显示对话框，Stack大小=(${stackSize.width}, ${stackSize.height})");
            PGLog.d(
                "showDialogOnUnity: 对话框居中后位置=(${centeredPosition.dx}, ${centeredPosition.dy})");

            return Stack(
              children: [
                Positioned(
                  left: centeredPosition.dx,
                  top: centeredPosition.dy,
                  child: dialogBuilder(context),
                ),
              ],
            );
          },
        ),
      );
    }

    try {
      // 显示对话框
      PGLog.d("showDialogOnUnity: 开始显示对话框");
      final result = await showDialog<T>(
        context: context,
        barrierColor: barrierColor,
        barrierDismissible: barrierDismissible,
        builder: (context) => Stack(
          children: [
            Positioned(
              left: dialogPosition.dx,
              top: dialogPosition.dy,
              child: dialogBuilder(context),
            ),
          ],
        ),
      );

      PGLog.d("showDialogOnUnity: 对话框已关闭");
      return result;
    } finally {
      // 确保对话框关闭后恢复Unity窗口
      PGLog.d("showDialogOnUnity: 恢复Unity窗口");
      await resetUnityWindowForDialog();
    }
  }

  /// 恢复Unity窗口（在对话框关闭后调用）
  static Future<void> resetUnityWindowForDialog() async {
    if (_isWindows) {
      PGLog.d("resetUnityWindowForDialog: 开始恢复Unity窗口");
      await removeUnityWindowRegion('dialog');
      await setUnityWindowTransparency(255);
      await setUnityWindowToBottom(toBottom: false);
      PGLog.d("resetUnityWindowForDialog: Unity窗口已恢复");
    }
  }

  /// 设置Unity窗口区域，使特定区域透明
  ///
  /// [x] 区域左上角X坐标
  /// [y] 区域左上角Y坐标
  /// [width] 区域宽度
  /// [height] 区域高度
  /// [tag] 区域标签，用于标识不同的挖孔区域，默认为"default"
  /// [roundRadius] 区域圆角半径
  /// [center] 是否将区域居中显示
  /// [reset] 是否重置特定标签的区域，true时会移除该标签的区域
  ///
  /// 返回设置是否成功
  static Future<bool> excludeUnityWindowRegion({
    int x = 0,
    int y = 0,
    required int width,
    required int height,
    String tag = "default",
    int roundRadius = 0,
    bool center = true,
    bool reset = false,
  }) async {
    if (!Platform.isWindows) {
      return false;
    }

    PGLog.d(
        "excludeUnityWindowRegion: tag=$tag, x=$x, y=$y, width=$width, height=$height, reset=$reset");

    try {
      await _launchUnityPlatform.invokeMethod('excludeUnityWindowRegion', {
        'x': x,
        'y': y,
        'width': width,
        'height': height,
        'roundRadius': roundRadius,
        'center': center,
        'reset': reset,
        'tag': tag,
      });
      PGLog.d("excludeUnityWindowRegion: 成功设置Unity窗口区域");
      return true;
    } catch (e) {
      PGLog.e("设置Unity窗口区域失败: $e");
      return false;
    }
  }

  /// 重置Unity窗口区域为完整窗口，移除所有挖孔区域
  static Future<bool> resetUnityWindowRegion() async {
    if (!Platform.isWindows) {
      return false;
    }

    PGLog.d("resetUnityWindowRegion: 重置Unity窗口所有区域");

    try {
      await _launchUnityPlatform.invokeMethod('resetUnityWindowRegion');
      PGLog.d("resetUnityWindowRegion: 成功重置Unity窗口所有区域");
      return true;
    } catch (e) {
      PGLog.e("重置Unity窗口区域失败: $e");
      return false;
    }
  }

  /// 移除特定标签的Unity窗口挖孔区域
  ///
  /// [tag] 要移除的区域标签
  ///
  /// 返回设置是否成功
  static Future<bool> removeUnityWindowRegion(String tag) async {
    if (!Platform.isWindows) {
      return false;
    }

    PGLog.d("removeUnityWindowRegion: 移除区域标签=$tag");

    return excludeUnityWindowRegion(
      width: 0,
      height: 0,
      tag: tag,
      reset: true,
    );
  }

  // 设置Unity窗口透明度
  static Future<bool> setUnityWindowTransparency(int alpha) async {
    if (!Platform.isWindows) {
      return false;
    }

    PGLog.d("setUnityWindowTransparency: alpha=$alpha");

    try {
      await _launchUnityPlatform.invokeMethod('setUnityWindowTransparency', {
        'alpha': alpha,
      });
      PGLog.d("setUnityWindowTransparency: 成功设置Unity窗口透明度");
      return true;
    } catch (e) {
      PGLog.e("设置Unity窗口透明度失败: $e");
      return false;
    }
  }

  /// 设置Unity窗口层级(仅影响鼠标事件传递顺序)
  ///
  /// [toBottom] 为true时将窗口置于底层(HWND_BOTTOM)，为false时将窗口置于顶层(HWND_TOP)
  ///
  /// 注意：即使设置为底层，由于Unity的特殊渲染方式，视觉上可能仍会显示在上层，
  /// 但鼠标事件会按照Z顺序传递（底层窗口的鼠标事件会被上层窗口拦截）
  ///
  /// 返回设置是否成功
  static Future<bool> setUnityWindowToBottom({required bool toBottom}) async {
    if (!Platform.isWindows) {
      return false;
    }

    PGLog.d("setUnityWindowToBottom: toBottom=$toBottom");

    try {
      await _launchUnityPlatform.invokeMethod(
          'setUnityWindowToBottom', toBottom);
      PGLog.d("setUnityWindowToBottom: 成功设置Unity窗口层级");
      return true;
    } catch (e) {
      PGLog.e("设置Unity窗口层级失败: $e");
      return false;
    }
  }

  /// 设置Unity窗口的焦点
  ///
  /// 在用户点击Unity输入框时调用此方法，确保键盘输入能够正确传递到Unity
  static Future<void> setFocusToUnityWindow() async {
    if (_isWindows) {
      try {
        PGLog.d("尝试设置Unity窗口焦点");
        await _handleMessageChannel.invokeMethod('setFocusToUnityWindow');
        PGLog.d("Unity窗口焦点设置方法调用完成");
      } on PlatformException catch (e) {
        PGLog.w("设置Unity窗口焦点失败: '${e.message}'.");
      }
    }
  }
}
