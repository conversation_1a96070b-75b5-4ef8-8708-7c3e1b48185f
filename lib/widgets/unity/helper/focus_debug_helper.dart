import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 焦点调试工具类
/// 用于检查和调试Windows平台上Flutter与Unity之间的焦点状态
class FocusDebugHelper {
  static const MethodChannel _channel = MethodChannel('com.turingart.focus_debug');
  static Timer? _monitoringTimer;
  static bool _isMonitoring = false;

  /// 检查当前焦点状态
  /// 返回详细的焦点信息，包括当前焦点窗口、Unity状态等
  static Future<FocusInfo?> getCurrentFocusInfo() async {
    if (!Platform.isWindows) {
      PGLog.w('Focus debug is only available on Windows platform');
      return null;
    }

    try {
      final result = await _channel.invokeMethod<Map>('getCurrentFocusInfo');
      if (result != null) {
        return FocusInfo.fromMap(Map<String, dynamic>.from(result));
      }
    } on PlatformException catch (e) {
      PGLog.e('Failed to get focus info: ${e.message}');
    }
    return null;
  }

  /// 开始焦点监控
  /// [interval] 监控间隔，默认1秒
  /// [onFocusChanged] 焦点变化回调
  static Future<void> startFocusMonitoring({
    Duration interval = const Duration(seconds: 1),
    Function(FocusInfo)? onFocusChanged,
  }) async {
    if (!Platform.isWindows) return;

    if (_isMonitoring) {
      PGLog.w('Focus monitoring is already running');
      return;
    }

    try {
      await _channel.invokeMethod('startFocusMonitoring');
      _isMonitoring = true;

      FocusInfo? lastFocusInfo;
      _monitoringTimer = Timer.periodic(interval, (timer) async {
        final currentFocusInfo = await getCurrentFocusInfo();
        if (currentFocusInfo != null) {
          // 检查焦点是否发生变化
          if (lastFocusInfo == null || 
              lastFocusInfo!.focusStatus != currentFocusInfo.focusStatus ||
              lastFocusInfo!.focusedWindow != currentFocusInfo.focusedWindow) {
            
            PGLog.d('Focus changed: ${currentFocusInfo.focusStatus} '
                   '(${currentFocusInfo.focusedWindowTitle})');
            
            onFocusChanged?.call(currentFocusInfo);
          }
          lastFocusInfo = currentFocusInfo;
        }
      });

      PGLog.i('Focus monitoring started with ${interval.inMilliseconds}ms interval');
    } on PlatformException catch (e) {
      PGLog.e('Failed to start focus monitoring: ${e.message}');
    }
  }

  /// 停止焦点监控
  static Future<void> stopFocusMonitoring() async {
    if (!Platform.isWindows) return;

    try {
      await _channel.invokeMethod('stopFocusMonitoring');
      _monitoringTimer?.cancel();
      _monitoringTimer = null;
      _isMonitoring = false;
      PGLog.i('Focus monitoring stopped');
    } on PlatformException catch (e) {
      PGLog.e('Failed to stop focus monitoring: ${e.message}');
    }
  }

  /// 强制设置焦点到Flutter窗口
  static Future<bool> forceFocusToFlutter() async {
    if (!Platform.isWindows) return false;

    try {
      await _channel.invokeMethod('forceFocusToFlutter');
      PGLog.d('Forced focus to Flutter window');
      return true;
    } on PlatformException catch (e) {
      PGLog.e('Failed to force focus to Flutter: ${e.message}');
      return false;
    }
  }

  /// 强制设置焦点到Unity窗口
  static Future<bool> forceFocusToUnity() async {
    if (!Platform.isWindows) return false;

    try {
      await _channel.invokeMethod('forceFocusToUnity');
      PGLog.d('Forced focus to Unity window');
      return true;
    } on PlatformException catch (e) {
      PGLog.e('Failed to force focus to Unity: ${e.message}');
      return false;
    }
  }

  /// 检查焦点是否在Unity窗口
  static Future<bool> isUnityFocused() async {
    final focusInfo = await getCurrentFocusInfo();
    return focusInfo?.isUnityFocused ?? false;
  }

  /// 检查焦点是否在Flutter窗口
  static Future<bool> isFlutterFocused() async {
    final focusInfo = await getCurrentFocusInfo();
    return focusInfo?.isFlutterFocused ?? false;
  }

  /// 获取当前焦点状态的简单描述
  static Future<String> getFocusStatusDescription() async {
    final focusInfo = await getCurrentFocusInfo();
    if (focusInfo == null) return 'Unknown';

    switch (focusInfo.focusStatus) {
      case 'flutter':
        return 'Flutter窗口有焦点';
      case 'unity':
        return 'Unity窗口有焦点';
      case 'none':
        return '没有窗口有焦点';
      case 'other':
        return '其他窗口有焦点: ${focusInfo.focusedWindowTitle}';
      default:
        return '未知状态';
    }
  }

  /// 打印详细的焦点调试信息
  static Future<void> printFocusDebugInfo() async {
    final focusInfo = await getCurrentFocusInfo();
    if (focusInfo == null) {
      PGLog.w('Unable to get focus info');
      return;
    }

    PGLog.d('=== Focus Debug Info ===');
    PGLog.d('Focus Status: ${focusInfo.focusStatus}');
    PGLog.d('Flutter Focused: ${focusInfo.isFlutterFocused}');
    PGLog.d('Unity Focused: ${focusInfo.isUnityFocused}');
    PGLog.d('Unity Visible: ${focusInfo.unityVisible}');
    PGLog.d('Prevent Auto Focus: ${focusInfo.preventAutoFocusToUnity}');
    PGLog.d('Focused Window: ${focusInfo.focusedWindowTitle} (${focusInfo.focusedWindowClass})');
    PGLog.d('Foreground Window: ${focusInfo.foregroundWindowTitle} (${focusInfo.foregroundWindowClass})');
    PGLog.d('========================');
  }

  /// 监控状态
  static bool get isMonitoring => _isMonitoring;
}

/// 焦点信息数据类
class FocusInfo {
  final int focusedWindow;
  final int foregroundWindow;
  final int activeWindow;
  final int flutterWindow;
  final int unityWindow;
  final bool isFlutterFocused;
  final bool isUnityFocused;
  final bool unityVisible;
  final bool preventAutoFocusToUnity;
  final bool isFlutterDialogActive;
  final bool shouldUnityReceiveInput;
  final String focusedWindowTitle;
  final String focusedWindowClass;
  final String foregroundWindowTitle;
  final String foregroundWindowClass;
  final String focusStatus;
  final int timestamp;

  const FocusInfo({
    required this.focusedWindow,
    required this.foregroundWindow,
    required this.activeWindow,
    required this.flutterWindow,
    required this.unityWindow,
    required this.isFlutterFocused,
    required this.isUnityFocused,
    required this.unityVisible,
    required this.preventAutoFocusToUnity,
    required this.isFlutterDialogActive,
    required this.shouldUnityReceiveInput,
    required this.focusedWindowTitle,
    required this.focusedWindowClass,
    required this.foregroundWindowTitle,
    required this.foregroundWindowClass,
    required this.focusStatus,
    required this.timestamp,
  });

  factory FocusInfo.fromMap(Map<String, dynamic> map) {
    return FocusInfo(
      focusedWindow: map['focusedWindow'] ?? 0,
      foregroundWindow: map['foregroundWindow'] ?? 0,
      activeWindow: map['activeWindow'] ?? 0,
      flutterWindow: map['flutterWindow'] ?? 0,
      unityWindow: map['unityWindow'] ?? 0,
      isFlutterFocused: map['isFlutterFocused'] ?? false,
      isUnityFocused: map['isUnityFocused'] ?? false,
      unityVisible: map['unityVisible'] ?? false,
      preventAutoFocusToUnity: map['preventAutoFocusToUnity'] ?? false,
      isFlutterDialogActive: map['isFlutterDialogActive'] ?? false,
      shouldUnityReceiveInput: map['shouldUnityReceiveInput'] ?? false,
      focusedWindowTitle: map['focusedWindowTitle'] ?? '',
      focusedWindowClass: map['focusedWindowClass'] ?? '',
      foregroundWindowTitle: map['foregroundWindowTitle'] ?? '',
      foregroundWindowClass: map['foregroundWindowClass'] ?? '',
      focusStatus: map['focusStatus'] ?? 'unknown',
      timestamp: map['timestamp'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'focusedWindow': focusedWindow,
      'foregroundWindow': foregroundWindow,
      'activeWindow': activeWindow,
      'flutterWindow': flutterWindow,
      'unityWindow': unityWindow,
      'isFlutterFocused': isFlutterFocused,
      'isUnityFocused': isUnityFocused,
      'unityVisible': unityVisible,
      'preventAutoFocusToUnity': preventAutoFocusToUnity,
      'isFlutterDialogActive': isFlutterDialogActive,
      'shouldUnityReceiveInput': shouldUnityReceiveInput,
      'focusedWindowTitle': focusedWindowTitle,
      'focusedWindowClass': focusedWindowClass,
      'foregroundWindowTitle': foregroundWindowTitle,
      'foregroundWindowClass': foregroundWindowClass,
      'focusStatus': focusStatus,
      'timestamp': timestamp,
    };
  }

  @override
  String toString() {
    return 'FocusInfo(status: $focusStatus, flutter: $isFlutterFocused, '
           'unity: $isUnityFocused, visible: $unityVisible, '
           'preventAuto: $preventAutoFocusToUnity, '
           'dialogActive: $isFlutterDialogActive, '
           'shouldReceive: $shouldUnityReceiveInput)';
  }
}
