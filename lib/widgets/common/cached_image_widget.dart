import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/config/image_cache_config.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_image_placeholder_widget.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 通用的缓存图片组件
class CachedImageWidget extends StatelessWidget {
  /// 阿里云OSS处理图片的参数，用于区分原图和缩略图的 cacheKey 处理
  static const String _processParameter = 'x-oss-process';

  /// 内存缓存最大边长
  static const int _maxCacheSize = 800;

  /// 图片URL
  final String imageUrl;

  /// 图片宽度
  final double? width;

  /// 图片高度
  final double? height;

  /// 图片适配方式
  final BoxFit? fit;

  /// 占位符组件
  final Widget? placeholder;

  /// 错误时显示的组件
  final Widget? errorWidget;

  /// 是否显示加载进度
  final bool showProgress;

  /// 边框圆角 - 完整的BorderRadius对象
  final BorderRadius? borderRadius;

  /// 圆角半径 - 简单的统一圆角设置
  final double? radius;

  /// 图片透明度
  final double opacity;

  /// 是否启用内存缓存
  final bool memCache;

  /// 是否启用磁盘缓存
  final bool diskCache;

  /// 自定义缓存key，如果为null则使用生成的缓存key
  final String? customCacheKey;

  const CachedImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.showProgress = false,
    this.borderRadius,
    this.radius,
    this.opacity = 1.0,
    this.memCache = true,
    this.diskCache = true,
    this.customCacheKey,
  }) : assert(
          borderRadius == null || radius == null,
          'Cannot provide both borderRadius and radius',
        );

  @override
  Widget build(BuildContext context) {
    const placeholderWidget = AIGCImagePlaceholder();

    // 计算内存缓存尺寸
    int? finalCacheWidth;
    int? finalCacheHeight;

    if (memCache && width != null && height != null) {
      // 根据显示尺寸计算缓存尺寸
      final displayWidth = width!;
      final displayHeight = height!;

      if (displayWidth > _maxCacheSize || displayHeight > _maxCacheSize) {
        final scale = _maxCacheSize /
            (displayWidth > displayHeight ? displayWidth : displayHeight);
        finalCacheWidth = (displayWidth * scale).round();
        finalCacheHeight = (displayHeight * scale).round();
      } else {
        // 对于小图片，使用2倍分辨率以保证清晰度
        finalCacheWidth = (displayWidth * 2).round();
        finalCacheHeight = (displayHeight * 2).round();
      }
    }

    // 生成缓存key - 移除URL中的动态参数以确保缓存一致性
    final cacheKey = customCacheKey ?? _extractCacheKey(imageUrl);

    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: ImageCacheConfig.cacheManager,
      cacheKey: cacheKey,
      memCacheWidth: finalCacheWidth,
      memCacheHeight: finalCacheHeight,
      placeholder: showProgress
          ? (context, url) => placeholderWidget
          : (context, url) => placeholder ?? placeholderWidget,
      errorWidget: (context, url, error) => errorWidget ?? placeholderWidget,
      // 减少动画时间以提升感知速度
      fadeInDuration: const Duration(milliseconds: 150),
      fadeOutDuration: const Duration(milliseconds: 50),
      // 添加图片过滤质量设置
      filterQuality: FilterQuality.medium,
    );

    // 应用透明度
    if (opacity != 1.0) {
      imageWidget = Opacity(
        opacity: opacity,
        child: imageWidget,
      );
    }

    // 应用圆角 - 优先使用borderRadius，其次使用radius
    final effectiveBorderRadius = _getEffectiveBorderRadius();
    if (effectiveBorderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: effectiveBorderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 获取有效的BorderRadius
  BorderRadius? _getEffectiveBorderRadius() {
    if (borderRadius != null) {
      return borderRadius;
    }
    if (radius != null) {
      return BorderRadius.circular(radius!);
    }
    return null;
  }

  /// 由于目前服务端返回的图片 url 可能带上了过期时间和 signature，导致同一张图片在不同时间
  /// 可能返回的图片地址不相同，造成缓存失效，因此这里默认获取图片 url 中唯一不变的部分作为缓
  /// 存 key
  /// eg: 原始地址：
  /// http://turing-aigc.oss-cn-hangzhou.aliyuncs.com/qa/gen/preset/d598114d-bd05-408b-b077-cfa5331f5bb5.png?x-oss-process=image%2Fresize%2Cm_fill%2Cw_200%2Ch_200&OSSAccessKeyId=LTAI5tFsHJW6JZCLGFUH7x9d&Expires=1753514279&Signature=Du%2BI23GeFfsoe0KKVUfX1l8VqAM%3D
  ///
  /// 计算后的 cacheKey：
  /// qa/gen/preset/d598114d-bd05-408b-b077-cfa5331f5bb5.png
  ///
  /// 可通过 ‘file_manager.dart' 中的 _imageCacheDir 查看当前磁盘中缓存的图片
  String _extractCacheKey(String url) {
    try {
      Uri uri = Uri.parse(url);
      String imageId = uri.path;
      // 使用此参数区分原图和
      String? processParam = uri.queryParameters[_processParameter];
      return processParam == null ? imageId : '$imageId-$processParam';
    } catch (e) {
      PGLog.e('获取图片缓存key失败 = $e');
      return url;
    }
  }
}
