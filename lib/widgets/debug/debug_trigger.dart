import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/providers/debug_menu_provider.dart';

class DebugTrigger extends StatefulWidget {
  const DebugTrigger({super.key});

  @override
  State<DebugTrigger> createState() => _DebugTriggerState();
}

class _DebugTriggerState extends State<DebugTrigger> {
  Offset _position = const Offset(0, 100);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: Draggable(
        feedback: _buildTriggerButton(),
        childWhenDragging: const SizedBox(),
        onDragEnd: (details) {
          setState(() {
            // 确保不超出屏幕边界
            final screenSize = MediaQuery.of(context).size;
            const buttonSize = Size(40, 40);

            _position = Offset(
              details.offset.dx.clamp(0, screenSize.width - buttonSize.width),
              details.offset.dy.clamp(0, screenSize.height - buttonSize.height),
            );
          });
        },
        child: _buildTriggerButton(),
      ),
    );
  }

  Widget _buildTriggerButton() {
    return GestureDetector(
      onTap: () {
        context.read<DebugMenuProvider>().toggleVisibility();
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 54, 244, 76),
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }
}
