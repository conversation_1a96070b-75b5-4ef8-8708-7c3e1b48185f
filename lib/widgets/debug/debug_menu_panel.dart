import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/config/env_config.dart';
import 'package:turing_art/providers/debug_menu_provider.dart';
import 'package:turing_art/utils/device_info_util.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class DebugMenuPanel extends StatelessWidget {
  DebugMenuPanel({super.key});
  final portInputController = TextEditingController();
  final ipInputController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Consumer<DebugMenuProvider>(
      builder: (context, debugMenu, _) {
        if (!debugMenu.isVisible) {
          return const SizedBox.shrink();
        }

        final screenWidth = MediaQuery.of(context).size.width;
        final topPadding = MediaQuery.of(context).padding.top;
        const panelWidth = 300.0;

        return Positioned(
          left: (screenWidth - panelWidth) / 2, // 居中显示
          top: topPadding + 16,
          bottom: 16,
          child: Material(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            child: SizedBox(
              width: panelWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 16,
                      right: 0,
                      top: 8,
                      bottom: 8,
                    ),
                    child: _buildHeader(context),
                  ),
                  const Divider(color: Colors.white30, height: 1),
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      children: [
                        const SizedBox(height: 16),
                        _buildEnvironmentSelector(context),
                        const SizedBox(height: 16),
                        _buildProxyConfig(context),
                        const SizedBox(height: 16),
                        _buildDeviceInfo(context),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Debug Menu',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        IconButton(
          padding: EdgeInsets.zero,
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: () {
            context.read<DebugMenuProvider>().toggleVisibility();
          },
        ),
      ],
    );
  }

  Widget _buildEnvironmentSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Environment', style: TextStyle(color: Colors.black)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: Environment.values.map((env) {
            return Consumer<DebugMenuProvider>(
              builder: (context, debugMenu, _) {
                return ChoiceChip(
                  label: Text(env.name),
                  selected: debugMenu.currentEnv == env.name,
                  onSelected: (selected) {
                    if (selected) {
                      _showRestartDialog(context, env.name);
                    }
                  },
                );
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildProxyConfig(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Proxy Config', style: TextStyle(color: Colors.black)),
        const SizedBox(height: 8),
        Consumer<DebugMenuProvider>(
          builder: (context, debugMenu, _) {
            ipInputController.text = debugMenu.proxyIp;
            portInputController.text = debugMenu.proxyPort;
            return Column(
              children: [
                SizedBox(
                  height: 40,
                  child: TextField(
                    controller: ipInputController,
                    style: const TextStyle(fontSize: 12),
                    decoration: const InputDecoration(
                      labelStyle: TextStyle(fontSize: 12),
                      labelText: "Proxy IP",
                      border: OutlineInputBorder(), // 边框
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                SizedBox(
                  height: 40,
                  child: TextField(
                    controller: portInputController,
                    style: const TextStyle(fontSize: 12),
                    decoration: const InputDecoration(
                      labelStyle: TextStyle(fontSize: 12),
                      labelText: "Proxy Port",
                      border: OutlineInputBorder(), // 边框
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    ElevatedButton(
                        onPressed: () {
                          if (ipInputController.text.isEmpty) {
                            PGDialog.showToast("ip地址不能为空");
                            return;
                          }
                          if (portInputController.text.isEmpty) {
                            PGDialog.showToast("端口号不能为空");
                            return;
                          }
                          debugMenu.setProxyConfig(
                              ipInputController.text, portInputController.text);
                          _showProxyRestartDialog(context);
                        },
                        child: const Text("Save Proxy")),
                    const SizedBox(width: 10),
                    ElevatedButton(
                        onPressed: () {
                          ipInputController.text = "";
                          portInputController.text = "";
                          debugMenu.setProxyConfig(
                              ipInputController.text, portInputController.text);
                          _showProxyRestartDialog(context);
                        },
                        child: const Text("Clean Proxy")),
                  ],
                )
              ],
            );
          },
        ),
      ],
    );
  }

  void _showRestartDialog(BuildContext context, String env) {
    SmartDialog.show(
      builder: (_) => AlertDialog(
        title: const Text('切换环境'),
        content: Text('切换到 $env 环境需要重启应用才能生效，是否立即重启？'),
        actions: [
          TextButton(
            onPressed: () {
              // 立即重启
              context
                  .read<DebugMenuProvider>()
                  .setEnvironment(env, restart: true);
            },
            child: const Text('立即关闭'),
          ),
        ],
      ),
    );
  }

  void _showProxyRestartDialog(BuildContext context) {
    SmartDialog.show(
      builder: (_) => AlertDialog(
        title: const Text('保存代理设置'),
        content: const Text('代理设置需要重启应用才能生效，是否立即重启？'),
        actions: [
          TextButton(
            onPressed: () {
              // 立即重启
              context.read<DebugMenuProvider>().restartApp();
            },
            child: const Text('立即关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Device Info', style: TextStyle(color: Colors.black)),
        const SizedBox(height: 8),
        Text(
          'App Version: ${DeviceInfoUtil().appVersion}',
          style: const TextStyle(color: Colors.black54),
        ),
        Text(
          'Device: ${DeviceInfoUtil().model}',
          style: const TextStyle(color: Colors.black54),
        ),
        Text(
          'Network: ${DeviceInfoUtil().network}',
          style: const TextStyle(color: Colors.black54),
        ),
      ],
    );
  }
}
