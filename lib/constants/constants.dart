// 该flag由launch文件内描述，在run时赋值
const bool _isDebugFlag =
    bool.fromEnvironment('IS_DEBUG_FLAG', defaultValue: false);

// 该flag由打包时传入
const _isDebugStr =
    String.fromEnvironment('IS_DEBUG_VALUE', defaultValue: 'false');

/// 是否为调试模式
final bool isDebug = _isDebugFlag || (_isDebugStr.toLowerCase() == 'true');

/// 工作区磁盘空间最小剩余空间阈值
const double workspaceDiskSpaceMinFreeSpace = 1.5 * 1024 * 1024 * 1024;

/// windows 构建平台，例如 "win7"
const buildPlatform =
    String.fromEnvironment('BUILD_PLATFORM', defaultValue: '');
