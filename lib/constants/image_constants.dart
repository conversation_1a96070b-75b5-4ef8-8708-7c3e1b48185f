/// 图片相关常量
class ImageConstants {
  const ImageConstants._(); // 私有构造函数

  /// 支持的图片文件扩展名（和unity一致）
  static const List<String> supportedExtensions = [
    'jpg',
    'jpeg',
    'png',
    'raf', // Raw
    'cr2', // Canon
    'cr3', // Canon
    'nef', // Nikon
    'arw', // Sony
    'dng', // Panasonic, Leica
    '3fr', // Fujifilm
    'rw2' // Panasonic
  ];

  /// 普通图片文件扩展名
  static const List<String> normalImageExtensions = [
    'jpg',
    'jpeg',
    'png',
  ];

  /// AIGC高清大图导出尺寸
  static const int aigcHighQualitySize = 3072;

  /// AIGC预览图尺寸（像素）- 用于预设生成
  static const int aigcPreviewSize = 1440;

  /// AIGC缩略图尺寸（像素）
  static const int aigcThumbnailSize = 256;

  static const int coverSize = 512;

  /// 最大图片尺寸（像素）
  static const double maxDimension = 3000;

  /// 图片质量（1-100）
  static const int quality = 100;

  /// 图片缓冲区大小
  static const int normalBufferSize = 1 * 1024 * 1024; // 1MB

  /// 安全图片缓冲区大小
  static const int safeBufferSize = 50 * 1024 * 1024; // 50MB
}
