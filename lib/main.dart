import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:pg_desktop_multi_window/desktop_multi_window.dart';
import 'package:pg_ops_sdk/api/ops_domains.dart';
import 'package:pg_ops_sdk/config/ops_config.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:turing_art/constants/sentry_constants.dart';
import 'package:turing_art/core/external_message/command_line_args/command_line_message_manager.dart';
import 'package:turing_art/core/manager/device_rating_manager.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/service/database/executor/database_executor.dart';
import 'package:turing_art/datalayer/service/share_preferences/shared_preferences_service.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/config/ops_device_info_provider.dart';
import 'package:turing_art/ops/config/ops_user_info_provider.dart';
import 'package:turing_art/providers/debug_menu_provider.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_collect.dart';
import 'package:turing_art/utils/screen_util.dart';

import 'config/env_config.dart';
import 'config/image_cache_config.dart';
import 'constants/constants.dart';
import 'providers/app_providers.dart';
import 'routing/router.dart';
import 'utils/app_info.dart';
import 'utils/device_info_util.dart';
import 'utils/pg_log.dart';
import 'widgets/debug/debug_menu_panel.dart';
import 'widgets/debug/debug_trigger.dart';

void main(List<String> args) async {
  WidgetsFlutterBinding.ensureInitialized();

  await FileManager().ensureInitialized();

  PGLog.initialize();
  if (args.isNotEmpty) {
    CommandLineMessageManager().setCommandLineArgs(args);
  }
  final prefs = await SharedPreferences.getInstance();
  // 获取 debug 环境
  final debugEnv = prefs.getString('debug_env') ?? 'dev';
  if (isDebug) {
    if (debugEnv == 'dev') {
      EnvConfig.initEnv(Environment.dev);
    } else if (debugEnv == 'qa') {
      EnvConfig.initEnv(Environment.qa);
    } else if (debugEnv == 'prod') {
      EnvConfig.initEnv(Environment.prod);
    }
  } else {
    // release 模式下，强制使用 prod 环境
    EnvConfig.initEnv(Environment.prod);
  }

  // 初始化基础信息
  await AppInfo.init();

  // 初始化图片缓存配置
  await ImageCacheConfig.initialize();

  // 设置状态栏样式
  _setupSystemUI();

  // 初始化SharedPreferencesService
  await SharedPreferencesService.init();

  // 初始化用户行为持久化服务
  await UserPreferencesService.init();

  // 初始化设备评级管理器
  try {
    await DeviceRatingManager.initialize();
    PGLog.i('设备评级管理器已初始化');
  } catch (e) {
    PGLog.e('设备评级管理器初始化失败: $e');
  }

  // 設置窗口最小尺寸
  if (Platform.isWindows) {
    // 确保窗口设置在应用启动前完成
    await WindowController.main().setWindowMinimizeSize(1285, 800);
    await WindowController.main().setWindowMaximizeSize(3840, 2160);
  }

  await DatabaseExecutor.initialize(FileManager().dbPath);

  await SentryFlutter.init(
    (options) {
      options.dsn = SentryConstants.dsn;
      options.tracesSampleRate = isDebug
          ? SentryConstants.percent100
          : SentryConstants.productTracesSampleRate;
      options.profilesSampleRate = isDebug
          ? SentryConstants.percent100
          : SentryConstants.productProfilesSampleRate;
      options.enableNativeCrashHandling = false;
    },
    appRunner: () => runApp(SentryWidget(
      child: MultiProvider(
        providers: AppProviders.providers(prefs),
        child: const MyApp(),
      ),
    )),
  );
}

void _setupSystemUI() {
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarBrightness: Brightness.dark, // iOS 状态栏亮度
    statusBarIconBrightness: Brightness.light, // Android 状态栏图标颜色
    statusBarColor: Colors.transparent, // Android 状态栏背景色
  ));
}

void _initOpsSdk(BuildContext context) async {
  //初始化Ops用戶信息
  OpsConfig().baseUrl = EnvConfig.baseUrl;
  OpsConfig().env = switch (EnvConfig.environment) {
    Environment.dev => UrlEnv.dev,
    Environment.qa => UrlEnv.qa,
    Environment.prod => UrlEnv.prod,
  };
  OpsConfig().deviceInfo = OpsDeviceInfoProvider();
  OpsConfig().debugMode = isDebug;
  OpsConfig().userInfo = OpsUserInfoProvider(
      userRepository: context.read<CurrentUserRepository>());
  var ip = context.read<DebugMenuProvider>().proxyIp;
  var port = context.read<DebugMenuProvider>().proxyPort;
  if (ip.isNotEmpty && port.isNotEmpty) {
    OpsConfig().proxyUrl = "$ip:$port";
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    ScreenUtil().init(context);
    _initOpsSdk(context);

    return MaterialApp.router(
      routerConfig: router,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      title: "TulingArt",
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      builder: (context, child) {
        DeviceInfoUtil().init(context).then((_) {
          // PGCollect初始化时需要DeviceInfoUtil中提供的数据，所以需要等待其异步初始化完成
          PGCollect.init(context);
        });
        child = Stack(
          children: [
            child ?? const SizedBox(),
            if (isDebug) ...[
              const DebugTrigger(),
              DebugMenuPanel(),
            ],
          ],
        );
        return FlutterSmartDialog.init()(context, child);
      },
    );
  }
}
