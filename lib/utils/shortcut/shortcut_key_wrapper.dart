import 'package:flutter/material.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_manager.dart';

/// 快捷键包装器
class ShortcutKeyWrapper extends StatefulWidget {
  final Widget child;

  const ShortcutKeyWrapper({
    super.key,
    required this.child,
  });

  @override
  State<ShortcutKeyWrapper> createState() => _ShortcutKeyWrapperState();
}

class _ShortcutKeyWrapperState extends State<ShortcutKeyWrapper> {
  // 快捷键映射
  late Map<ShortcutActivator, Intent> _shortcuts;

  @override
  void initState() {
    super.initState();
    _shortcuts = ShortcutKeyManager().currentShortcuts;
  }

  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: _shortcuts,
      child: Focus(
        autofocus: true,
        child: widget.child,
      ),
    );
  }
}
