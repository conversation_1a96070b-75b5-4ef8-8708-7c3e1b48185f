import 'package:flutter/material.dart';

/// 快捷键管理器 - 单例模式
class ShortcutKeyManager {
  static final ShortcutKeyManager _instance = ShortcutKeyManager._internal();
  factory ShortcutKeyManager() => _instance;
  ShortcutKeyManager._internal();

  /// 页面级快捷键映射堆栈
  final List<Map<ShortcutActivator, Intent>> _shortcutMapStack = [];

  /// 全局快捷键映射 - 始终有效
  final Map<ShortcutActivator, Intent> _globalShortcuts = {};

  /// 注册页面级快捷键映射
  void pushShortcuts(Map<ShortcutActivator, Intent> shortcuts) {
    _shortcutMapStack.add(shortcuts);
  }

  /// 移除页面级快捷键映射
  void popShortcuts() {
    if (_shortcutMapStack.isNotEmpty) {
      _shortcutMapStack.removeLast();
    }
  }

  /// 移除特定的页面级快捷键映射
  void removeShortcuts(Map<ShortcutActivator, Intent> shortcuts) {
    _shortcutMapStack.remove(shortcuts);
  }

  /// 注册全局快捷键
  void registerGlobalShortcut(ShortcutActivator activator, Intent intent) {
    _globalShortcuts[activator] = intent;
  }

  /// 注销全局快捷键
  void unregisterGlobalShortcut(ShortcutActivator activator) {
    _globalShortcuts.remove(activator);
  }

  /// 获取当前合并的快捷键映射
  Map<ShortcutActivator, Intent> get currentShortcuts {
    // 先添加全局快捷键
    final result = Map<ShortcutActivator, Intent>.from(_globalShortcuts);

    // 从底层到顶层合并页面级快捷键，顶层覆盖底层
    for (final shortcuts in _shortcutMapStack) {
      result.addAll(shortcuts);
    }

    return result;
  }

  // /// 打印当前快捷键堆栈状态 (调试用)
  // void printShortcutState() {
  //   print('======= 快捷键管理器状态 =======');
  //   print('全局快捷键: ${_globalShortcuts.length}个');
  //   print('页面级快捷键堆栈深度: ${_shortcutMapStack.length}');
  //   for (int i = 0; i < _shortcutMapStack.length; i++) {
  //     print('层级 $i: ${_shortcutMapStack[i].length}个快捷键');
  //   }
  //   print('当前生效的快捷键: ${currentShortcuts.length}个');
  //   print('===============================');
  // }
}
