import 'package:flutter/services.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/shortcut/service/shortcut_key_handler.dart';
import 'package:turing_art/utils/shortcut/service/shortcut_key_interface.dart';

/// 快捷键服务
class ShortcutKeyService {
  static final isWindows = AppConstants.isWindows;
  final ShortcutKeyInterface _handler;

  ShortcutKeyService._({required ShortcutKeyInterface handler})
      : _handler = handler;

  factory ShortcutKeyService.forPlatform() {
    final handler =
        isWindows ? WindowsShortcutKeyHandler() : MacOSShortcutKeyHandler();
    return ShortcutKeyService._(handler: handler);
  }

  /// 获取Ctrl键
  LogicalKeyboardKey get ctrlKey => _handler.ctrlKey;

  /// 获取删除键列表
  List<LogicalKeyboardKey> get deleteKeys => _handler.deleteKeys;

  /// 是否按下了Ctrl键
  bool isControlPressed() {
    return _handler.isControlPressed();
  }

  /// 是否按下了Ctrl键和O键
  bool isControlAndOPressed(KeyEvent event) {
    return isControlPressed() &&
        event.logicalKey == LogicalKeyboardKey.keyO &&
        HardwareKeyboard.instance.logicalKeysPressed.length == 2;
  }

  /// 是否按下了删除键（Delete或Backspace）
  bool isDeletePressed(KeyEvent event) {
    return _handler.isDeletePressed(event);
  }
}
