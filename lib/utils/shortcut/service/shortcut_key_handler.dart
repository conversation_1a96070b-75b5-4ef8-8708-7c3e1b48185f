import 'package:flutter/services.dart';
import 'package:turing_art/utils/shortcut/service/shortcut_key_interface.dart';

/// Windows平台快捷键处理
class WindowsShortcutKeyHandler extends ShortcutKeyInterface {
  @override
  LogicalKeyboardKey get ctrlKey => LogicalKeyboardKey.control;

  @override
  bool isControlPressed() {
    return HardwareKeyboard.instance.isControlPressed;
  }

  @override
  bool isDeletePressed(KeyEvent event) {
    return deleteKeys.contains(event.logicalKey);
  }
}

/// Mac平台快捷键处理
class MacOSShortcutKeyHandler extends ShortcutKeyInterface {
  @override
  LogicalKeyboardKey get ctrlKey => LogicalKeyboardKey.meta;

  @override
  bool isControlPressed() {
    return HardwareKeyboard.instance.isMetaPressed;
  }

  @override
  bool isDeletePressed(KeyEvent event) {
    return deleteKeys.contains(event.logicalKey);
  }
}
