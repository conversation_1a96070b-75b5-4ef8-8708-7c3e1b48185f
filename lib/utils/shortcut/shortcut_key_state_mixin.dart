import 'package:flutter/material.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_manager.dart';

/// 快捷键 State 混入
mixin ShortcutKeyStateMixin<T extends StatefulWidget> on State<T> {
  /// 快捷键映射
  late Map<ShortcutActivator, Intent> shortcutMap;

  @override
  void initState() {
    super.initState();
    shortcutMap = defineShortcuts();
    ShortcutKeyManager().pushShortcuts(shortcutMap);
  }

  @override
  void dispose() {
    ShortcutKeyManager().removeShortcuts(shortcutMap);
    super.dispose();
  }

  /// 子类实现此方法定义快捷键
  Map<ShortcutActivator, Intent> defineShortcuts();

  /// 子类实现此方法定义操作
  Map<Type, Action<Intent>> defineActions();
}
