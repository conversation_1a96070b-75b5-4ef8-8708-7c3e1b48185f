import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

import 'pg_log.dart';

/// 判断是否是低版本Windows
/// Windows 8.1	6.3 *
/// Windows Server 2012 R2	6.3 *
/// Windows 8	6.2
/// Windows Server 2012	6.2
/// Windows 7	6.1
Future<bool> isLowLevelWindows() async {
  if (!Platform.isWindows) {
    return false;
  }

  try {
    final windowsInfo = await DeviceInfoPlugin().windowsInfo;
    return windowsInfo.majorVersion == 6;
  } catch (e) {
    PGLog.d('获取 Windows 版本信息失败: $e');
    return false;
  }
}
