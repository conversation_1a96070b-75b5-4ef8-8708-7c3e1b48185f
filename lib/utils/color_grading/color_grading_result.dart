import 'dart:io';

/// 调色处理结果
class ColorGradingResult {
  /// 任务ID
  final String taskId;

  /// 输入文件路径
  final String inputFilePath;

  /// 输出文件路径
  final String outputFilePath;

  /// 是否成功
  final bool isSuccess;

  /// 输出文件（成功时）
  final File? outputFile;

  /// 错误信息（失败时）
  final String? errorMessage;

  /// 处理耗时（毫秒）
  final int? processingDuration;

  ColorGradingResult({
    required this.taskId,
    required this.inputFilePath,
    required this.outputFilePath,
    required this.isSuccess,
    this.outputFile,
    this.errorMessage,
    this.processingDuration,
  });

  /// 成功结果
  factory ColorGradingResult.success({
    required String taskId,
    String? proofingId,
    String? effectCode,
    required String inputFilePath,
    required String outputFilePath,
    required File outputFile,
    int? processingDuration,
  }) {
    return ColorGradingResult(
      taskId: taskId,
      inputFilePath: inputFilePath,
      outputFilePath: outputFilePath,
      isSuccess: true,
      outputFile: outputFile,
      processingDuration: processingDuration,
    );
  }

  /// 失败结果
  factory ColorGradingResult.failure({
    required String taskId,
    String? proofingId,
    String? effectCode,
    required String inputFilePath,
    required String outputFilePath,
    required String errorMessage,
    int? processingDuration,
  }) {
    return ColorGradingResult(
      taskId: taskId,
      inputFilePath: inputFilePath,
      outputFilePath: outputFilePath,
      isSuccess: false,
      errorMessage: errorMessage,
      processingDuration: processingDuration,
    );
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'ColorGradingResult.success(taskId: $taskId, outputFile: ${outputFile?.path}, duration: ${processingDuration}ms)';
    } else {
      return 'ColorGradingResult.failure(taskId: $taskId, error: $errorMessage, duration: ${processingDuration}ms)';
    }
  }
}
