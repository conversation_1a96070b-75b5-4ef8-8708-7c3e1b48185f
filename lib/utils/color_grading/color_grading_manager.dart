import 'dart:async';
import 'dart:io';

import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/utils/color_grading/color_grading_result.dart';
import 'package:turing_art/utils/color_grading/color_grading_task.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 队列中的调色任务信息
class _QueuedColorGradingTask {
  final ColorGradingTask task;
  final Function(String taskId, ColorGradingResult result)? onComplete;
  final Function(String taskId, String error)? onError;
  final Completer<ColorGradingResult> resultCompleter;

  _QueuedColorGradingTask({
    required this.task,
    required this.resultCompleter,
    this.onComplete,
    this.onError,
  });

  /// 清理资源，避免内存泄漏
  void dispose() {
    if (!resultCompleter.isCompleted) {
      resultCompleter.complete(ColorGradingResult.failure(
        taskId: task.taskId,
        inputFilePath: task.inputFilePath,
        outputFilePath: task.outputFilePath,
        errorMessage: '任务被清理',
      ));
    }
  }
}

/// 调色管理器 - 负责管理调色任务的串行处理
///
/// 主要对外API：
/// 1. 单任务调色: processColorGradingFromTask()
/// 2. 批量调色: processMultipleColorGradingFromTasks()
/// 3. 任务管理: cancelTask/removeTask
/// 4. 状态查询: getTask/getAllTasks/hasTask
/// 5. 工具方法: checkColorGradingNeeded
class ColorGradingManager {
  static final ColorGradingManager _instance = ColorGradingManager._internal();

  factory ColorGradingManager() => _instance;

  ColorGradingManager._internal() {
    _initializeAigcServiceStream();
  }

  /// 调色任务列表，使用Map存储，key为taskId
  final Map<String, ColorGradingTask> _colorGradingTasks = {};

  /// 调色任务列表发生变化时的流控制器
  final _tasksStreamController =
      StreamController<Map<String, ColorGradingTask>>.broadcast();

  /// 调色任务列表变化流
  Stream<Map<String, ColorGradingTask>> get tasksStream =>
      _tasksStreamController.stream;

  /// 是否正在处理调色任务
  bool _isProcessing = false;

  /// 等待处理的任务队列
  final List<String> _pendingTasks = [];

  /// 队列中任务的详细信息
  final Map<String, _QueuedColorGradingTask> _queuedTasks = {};

  final AigcService _processingService = AigcService();

  /// AIGC服务结果流监听器
  StreamSubscription<AigcTaskResultMessage>? _aigcResultStreamSubscription;

  /// 初始化AIGC服务流监听
  void _initializeAigcServiceStream() {
    PGLog.d('ColorGradingManager: 初始化AIGC服务任务监听器');

    _aigcResultStreamSubscription =
        _processingService.resultStream.listen((message) {
      PGLog.d(
          'ColorGradingManager: 收到AIGC任务结果 - taskId: ${message.taskId}, taskType: ${message.payload.taskType}, success: ${message.success}');

      // 只处理调色任务类型
      if (message.payload.taskType == AigcTaskType.rawConversion) {
        _handleAigcTaskResult(message);
      }
    });
  }

  /// 处理AIGC任务结果
  void _handleAigcTaskResult(AigcTaskResultMessage message) {
    final taskId = message.taskId;
    final task = _colorGradingTasks[taskId];

    if (task == null) {
      PGLog.w('ColorGradingManager: 收到未知任务的结果，taskId: $taskId');
      return;
    }

    PGLog.d(
        'ColorGradingManager: 处理调色任务结果 - taskId: $taskId, success: ${message.success}');

    // 处理完成后检查是否被取消
    if (task.isCanceled) {
      PGLog.d('调色任务在处理后被取消，删除输出文件: $taskId');
      final outputFile = File(task.outputFilePath);
      if (outputFile.existsSync()) {
        outputFile.deleteSync();
      }
      // 自动清理已取消的任务
      _autoCleanupCompletedTask(taskId);
      return;
    }

    if (message.success) {
      // 任务成功
      final outputFile = File(task.outputFilePath);
      if (outputFile.existsSync()) {
        _colorGradingSuccess(task, outputFile);
      } else {
        _colorGradingFailed(task, '输出文件不存在: ${task.outputFilePath}');
      }
    } else {
      // 任务失败
      final errorMessage = message.errorMessage ?? '调色处理失败';
      _colorGradingFailed(task, errorMessage);
    }

    // 不管成功与否都需要自动清理内存
    Future.delayed(const Duration(seconds: 5), () {
      _autoCleanupCompletedTask(taskId);
    });
  }

  // ==================== 对外API ====================

  /// 单个调色处理（使用Task）
  ///
  /// [task] 调色任务对象
  /// [onComplete] 完成回调 (taskId, result)
  /// [onError] 错误回调 (taskId, error)
  ///
  /// 返回调色结果
  Future<ColorGradingResult> processColorGradingFromTask(
    ColorGradingTask task, {
    Function(String taskId, ColorGradingResult result)? onComplete,
    Function(String taskId, String error)? onError,
  }) async {
    final result = await _addColorGradingTask(
      task: task,
      waitForResult: true,
      onComplete: onComplete,
      onError: onError,
    );
    return result!; // waitForResult=true时，结果不会为null
  }

  /// 批量调色处理（Task数组格式）
  ///
  /// [colorGradingTasks] 调色任务数组
  /// [onComplete] 完成回调 (taskId, result)
  /// [onError] 错误回调 (taskId, error)
  ///
  /// 返回任务ID列表，可通过tasksStream监听状态变化
  Future<List<String>> processMultipleColorGradingFromTasks(
    List<ColorGradingTask> colorGradingTasks, {
    Function(String taskId, ColorGradingResult result)? onComplete,
    Function(String taskId, String error)? onError,
  }) async {
    final taskIds = <String>[];

    for (final task in colorGradingTasks) {
      taskIds.add(task.taskId);

      // 串行处理：等待当前任务完成后再处理下一个任务
      await _addColorGradingTask(
        task: task,
        waitForResult: true, // 等待结果，确保串行
        onComplete: onComplete,
        onError: onError,
      );
    }

    return taskIds;
  }

  /// 取消任务
  ///
  /// [taskId] 任务ID
  /// 返回是否成功取消
  bool cancelTask(String taskId) {
    final task = _colorGradingTasks[taskId];
    if (task != null) {
      // 检查任务状态，只有未开始或处理中的任务才能取消
      final currentStatus = task.status;
      if (currentStatus == ColorGradingTaskStatus.notStarted ||
          currentStatus == ColorGradingTaskStatus.processing) {
        task.setCanceled();

        // 如果任务未开始，可以立即清理
        // 如果任务正在处理中，让处理线程完成后自动清理
        if (currentStatus == ColorGradingTaskStatus.notStarted) {
          _cleanupTask(taskId);
        }
        return true;
      }
    }
    return false;
  }

  /// 移除任务
  ///
  /// [taskId] 任务ID
  /// 返回是否成功移除
  bool removeTask(String taskId) {
    if (_colorGradingTasks.containsKey(taskId)) {
      _cleanupTask(taskId); // 使用统一清理方法
      return true;
    }
    return false;
  }

  /// 获取任务
  ///
  /// [taskId] 任务ID
  /// 返回任务对象，不存在则返回null
  ColorGradingTask? getTask(String taskId) {
    return _colorGradingTasks[taskId];
  }

  /// 获取所有任务
  ///
  /// 返回任务映射的副本
  Map<String, ColorGradingTask> get allTasks =>
      Map.unmodifiable(_colorGradingTasks);

  /// 检查是否存在任务
  ///
  /// [taskId] 任务ID
  /// 返回是否存在
  bool hasTask(String taskId) {
    return _colorGradingTasks.containsKey(taskId);
  }

  /// 检查是否需要调色
  ///
  /// [filePath] 文件路径
  /// 返回需要调色时的LUT文件路径，不需要则返回null
  static String? checkColorGradingNeeded(String filePath) {
    // 如果文件名已经包含'_lut'，则不需要调色
    if (ColorGradingTask.isLutFile(filePath)) {
      return null;
    }

    // 生成LUT文件路径
    final lutFilePath = ColorGradingTask.generateLutFilePath(filePath);

    // 检查LUT文件是否已存在
    final lutFile = File(lutFilePath);
    if (lutFile.existsSync()) {
      return null; // LUT文件已存在，不需要重新调色
    }

    return lutFilePath; // 返回LUT文件路径，表示需要调色
  }

  /// 停止所有调色操作
  void stop() {
    _isProcessing = false;
    // 取消所有等待中的任务
    for (final task in _colorGradingTasks.values) {
      task.setCanceled();
    }
    _colorGradingTasks.clear();
    _pendingTasks.clear();
    // 清理队列任务
    for (final queuedTask in _queuedTasks.values) {
      queuedTask.dispose();
    }
    _queuedTasks.clear();
  }

  /// 释放资源
  void dispose() {
    stop();
    _aigcResultStreamSubscription?.cancel();
    _tasksStreamController.close();
  }

  // ==================== 内部方法 ====================

  /// 添加调色任务到列表
  ///
  /// [task] 调色任务
  /// [waitForResult] 是否等待结果
  /// [onComplete] 完成回调
  /// [onError] 错误回调
  ///
  /// 返回调色结果（当waitForResult=true时）
  Future<ColorGradingResult?> _addColorGradingTask({
    required ColorGradingTask task,
    bool waitForResult = false,
    Function(String taskId, ColorGradingResult result)? onComplete,
    Function(String taskId, String error)? onError,
  }) async {
    final taskId = task.taskId;

    // 检查任务是否已存在
    if (_colorGradingTasks.containsKey(taskId)) {
      final message = '调色任务已存在: $taskId';
      PGLog.w(message);
      onError?.call(taskId, message);
      if (waitForResult) {
        return ColorGradingResult.failure(
          taskId: taskId,
          inputFilePath: task.inputFilePath,
          outputFilePath: task.outputFilePath,
          errorMessage: message,
        );
      }
      return null;
    }

    // 添加任务到列表
    _colorGradingTasks[taskId] = task;
    _notifyTasksChanged();

    PGLog.d('添加调色任务: $taskId');

    // 创建结果完成器（只在需要等待结果时创建）
    final resultCompleter = Completer<ColorGradingResult>();

    // 添加到待处理队列
    _pendingTasks.add(taskId);
    _queuedTasks[taskId] = _QueuedColorGradingTask(
      task: task,
      resultCompleter: resultCompleter,
      onComplete: onComplete,
      onError: onError,
    );

    // 开始处理队列
    _processNextPendingTask();

    // 如果需要等待结果，则返回结果
    if (waitForResult) {
      return await resultCompleter.future;
    }

    return null;
  }

  /// 处理下一个待处理任务
  Future<void> _processNextPendingTask() async {
    // 如果正在处理或没有待处理任务，直接返回
    if (_isProcessing || _pendingTasks.isEmpty) {
      return;
    }

    _isProcessing = true;

    try {
      while (_pendingTasks.isNotEmpty) {
        final taskId = _pendingTasks.removeAt(0);
        final queuedTask = _queuedTasks[taskId];

        if (queuedTask != null) {
          await _processSingleTask(queuedTask);
          _queuedTasks.remove(taskId);
        }
      }
    } finally {
      _isProcessing = false;
    }
  }

  /// 处理单个调色任务（改成提交任务的方式了）
  Future<void> _processSingleTask(_QueuedColorGradingTask queuedTask) async {
    final task = queuedTask.task;
    final taskId = task.taskId;

    PGLog.d('开始处理调色任务: $taskId');

    try {
      // 检查任务是否已被取消
      if (task.isCanceled) {
        PGLog.d('任务已被取消，跳过处理: $taskId');
        return;
      }

      // 确保AIGC服务已初始化
      try {
        await _processingService.initialize();
      } catch (e) {
        PGLog.e('初始化AIGC服务失败: $e');
        _colorGradingFailed(task, '初始化处理服务失败: $e');
        return;
      }

      // 更新任务状态为处理中
      task.setProcessing();
      _notifyTasksChanged();

      // 检查输入文件是否存在
      final inputFile = File(task.inputFilePath);
      if (!inputFile.existsSync()) {
        throw Exception('输入文件不存在: ${task.inputFilePath}');
      }

      // 确保输出目录存在
      final outputFile = File(task.outputFilePath);
      final outputDir = outputFile.parent;
      if (!outputDir.existsSync()) {
        await outputDir.create(recursive: true);
      }

      // 再次检查任务是否已被取消（在文件检查之后，调色处理之前）
      if (task.isCanceled) {
        PGLog.d('调色任务在处理前被取消: $taskId');
        return;
      }

      _processingService.submitTask(
        inputPath: task.inputFilePath,
        outputPath: task.outputFilePath,
        fileId: task.taskId,
        taskType: AigcTaskType.rawConversion,
        executeNow: true,
      );
    } catch (e) {
      PGLog.e('调色任务处理异常: $taskId, 错误: $e');
      _colorGradingFailed(task, '任务处理异常: $e');
    }
  }

  void _colorGradingSuccess(
    ColorGradingTask task,
    File outputFile,
  ) {
    // 调色成功
    task.setCompleted(outputFile);
    _notifyTasksChanged();

    final colorGradingResult = ColorGradingResult.success(
      taskId: task.taskId,
      inputFilePath: task.inputFilePath,
      outputFilePath: task.outputFilePath,
      outputFile: outputFile,
    );

    final queuedTask = _queuedTasks[task.taskId];
    if (queuedTask == null) {
      return;
    }
    queuedTask.onComplete?.call(task.taskId, colorGradingResult);
    if (!queuedTask.resultCompleter.isCompleted) {
      queuedTask.resultCompleter.complete(colorGradingResult);
    }

    PGLog.d('调色任务完成: ${task.taskId}');
  }

  void _colorGradingFailed(
    ColorGradingTask task,
    String errorMessage,
  ) {
    task.setError(errorMessage);
    _notifyTasksChanged();

    final colorGradingResult = ColorGradingResult.failure(
      taskId: task.taskId,
      inputFilePath: task.inputFilePath,
      outputFilePath: task.outputFilePath,
      errorMessage: errorMessage,
    );

    final queuedTask = _queuedTasks[task.taskId];
    if (queuedTask == null) {
      return;
    }

    queuedTask.onError?.call(task.taskId, errorMessage);
    if (!queuedTask.resultCompleter.isCompleted) {
      queuedTask.resultCompleter.complete(colorGradingResult);
    }
  }

  /// 通知任务列表已变化
  void _notifyTasksChanged() {
    if (!_tasksStreamController.isClosed) {
      _tasksStreamController.add(Map.unmodifiable(_colorGradingTasks));
    }
  }

  /// 自动清理已完成的任务
  void _autoCleanupCompletedTask(String taskId) {
    final task = _colorGradingTasks[taskId];
    if (task != null && (task.isCompleted || task.isError || task.isCanceled)) {
      _colorGradingTasks.remove(taskId);
      _pendingTasks.remove(taskId); // 虽然应该已经不在pending中，但保险起见
      // _queuedTasks 在 _processNextPendingTask 中已经清理过了

      PGLog.d('自动清理已完成任务: $taskId (状态: ${task.status})');
      _notifyTasksChanged();
    }
  }

  /// 统一的任务清理方法（立即清理，用于手动操作）
  void _cleanupTask(String taskId) {
    // 清理主任务
    _colorGradingTasks.remove(taskId);

    // 清理队列任务并释放资源
    final queuedTask = _queuedTasks.remove(taskId);
    queuedTask?.dispose();

    // 从待处理队列中移除
    _pendingTasks.remove(taskId);

    PGLog.d('立即清理调色任务: $taskId');
    _notifyTasksChanged();
  }
}
