import 'dart:io';

import 'package:path/path.dart' as path;

/// 调色任务状态
enum ColorGradingTaskStatus {
  notStarted, // 未开始
  processing, // 处理中
  completed, // 已完成
  error, // 错误
  canceled, // 已取消
}

/// 调色任务模型
class ColorGradingTask {
  /// 任务唯一标识（由外部决定格式）
  final String taskId;

  /// 输入文件路径
  final String inputFilePath;

  /// 输出文件路径
  final String outputFilePath;

  /// 任务状态
  ColorGradingTaskStatus _status = ColorGradingTaskStatus.notStarted;

  /// 任务状态
  ColorGradingTaskStatus get status => _status;

  /// 错误信息
  String? _errorMessage;

  /// 错误信息
  String? get errorMessage => _errorMessage;

  /// 输出文件
  File? _outputFile;

  /// 输出文件
  File? get outputFile => _outputFile;

  /// 任务开始时间
  DateTime? _startTime;

  /// 任务结束时间
  DateTime? _endTime;

  /// 处理耗时（毫秒）
  int get processingDuration {
    if (_startTime == null) {
      return 0;
    }
    final endTime = _endTime ?? DateTime.now();
    return endTime.difference(_startTime!).inMilliseconds;
  }

  ColorGradingTask({
    required this.taskId,
    required this.inputFilePath,
    required this.outputFilePath,
  });

  /// 工厂构造函数：从输入文件路径自动生成LUT输出路径
  factory ColorGradingTask.autoLut({
    required String taskId,
    required String inputFilePath,
  }) {
    return ColorGradingTask(
      taskId: taskId,
      inputFilePath: inputFilePath,
      outputFilePath: generateLutFilePath(inputFilePath),
    );
  }

  /// 生成LUT文件路径
  ///
  /// 将原文件名添加 '_lut' 后缀
  /// 例如：'/path/to/image.jpg' -> '/path/to/image_lut.jpg'
  static String generateLutFilePath(String originalFilePath) {
    final directory = path.dirname(originalFilePath);
    final fileName = path.basenameWithoutExtension(originalFilePath);
    final extension = path.extension(originalFilePath);

    return path.join(directory, '${fileName}_lut$extension');
  }

  /// 检查文件名是否包含 '_lut'
  static bool isLutFile(String filePath) {
    final fileName = path.basenameWithoutExtension(filePath);
    return fileName.contains('_lut');
  }

  /// 判断任务是否正在处理
  bool get isProcessing => _status == ColorGradingTaskStatus.processing;

  /// 判断任务是否已完成
  bool get isCompleted => _status == ColorGradingTaskStatus.completed;

  /// 判断任务是否失败
  bool get isError => _status == ColorGradingTaskStatus.error;

  /// 判断任务是否被取消
  bool get isCanceled => _status == ColorGradingTaskStatus.canceled;

  /// 设置任务为处理中状态
  void setProcessing() {
    _status = ColorGradingTaskStatus.processing;
    _startTime = DateTime.now();
    _endTime = null;
    _errorMessage = null;
    _outputFile = null;
  }

  /// 设置任务为完成状态
  void setCompleted(File outputFile) {
    _status = ColorGradingTaskStatus.completed;
    _endTime = DateTime.now();
    _outputFile = outputFile;
    _errorMessage = null;
  }

  /// 设置任务为错误状态
  void setError(String errorMessage) {
    _status = ColorGradingTaskStatus.error;
    _endTime = DateTime.now();
    _errorMessage = errorMessage;
    _outputFile = null;
  }

  /// 设置任务为取消状态
  void setCanceled() {
    _status = ColorGradingTaskStatus.canceled;
    _endTime = DateTime.now();
    _errorMessage = '任务被取消';
    _outputFile = null;
  }

  @override
  String toString() {
    return 'ColorGradingTask(taskId: $taskId, status: $_status, inputFilePath: $inputFilePath, outputFilePath: $outputFilePath)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is ColorGradingTask && other.taskId == taskId;
  }

  @override
  int get hashCode => taskId.hashCode;
}
