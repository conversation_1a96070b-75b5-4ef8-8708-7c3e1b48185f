import 'dart:io';

import 'package:turing_art/utils/url_launcher_util.dart';

/// 应用常量
class AppConstants {
  const AppConstants._(); // 私有构造函数

  /// 是否是桌面端
  static final isDesktop =
      Platform.isMacOS || Platform.isWindows || Platform.isLinux;

  static final isContainSandox =
      Platform.isMacOS || Platform.isIOS || Platform.isAndroid;

  static final isWindows = Platform.isWindows;
  // 是否是win7
  static bool isWin7 = UrlLauncherUtil.shouldUseSystemBrowser();
}
