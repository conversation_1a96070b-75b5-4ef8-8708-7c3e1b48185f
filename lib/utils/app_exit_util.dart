import 'dart:io';

import 'package:flutter/services.dart';
import 'package:pg_desktop_multi_window/desktop_multi_window.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/windows_process_terminator.dart';

/// 应用程序退出工具类
/// 提供简单的API来退出应用程序
class AppExitUtil {
  /// 退出应用程序
  /// 这是一个便捷的静态方法，可以在任何地方调用
  static Future<void> exitApp() async {
    if (Platform.isWindows) {
      // 在Windows上使用原生方法终止进程
      try {
        // 1. 先关闭所有子窗口
        DesktopMultiWindow.getAllSubWindowIds().then((windowIds) {
          for (final id in windowIds) {
            WindowController.fromWindowId(id).close();
          }

          // 2. 使用Windows原生方法终止进程
          bool terminated = WindowsProcessTerminator.terminateCurrentProcess();

          // 3. 如果原生方法失败，尝试其他方法
          if (!terminated) {
            SystemNavigator.pop(animated: true);

            // 最后尝试强制退出
            Future.delayed(const Duration(milliseconds: 500), () {
              exit(0);
            });
          }
        });
      } catch (e) {
        PGLog.d('退出应用时出错: $e');
        // 出错时使用强制退出
        exit(0);
      }
    } else {
      // 其他平台使用标准退出
      SystemNavigator.pop();
    }
  }

  /// 重启应用程序
  /// 先退出，然后由外部启动器重新启动
  static Future<void> restartApp() async {
    await exitApp();
  }
}
