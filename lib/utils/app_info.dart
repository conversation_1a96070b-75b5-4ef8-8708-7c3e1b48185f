import 'package:package_info_plus/package_info_plus.dart';
import 'package:turing_art/config/env_config.dart';

class AppInfo {
  static late PackageInfo _packageInfo;
  static bool _initialized = false;

  // 初始化方法，需要在应用启动时调用
  static Future<void> init() async {
    _packageInfo = await PackageInfo.fromPlatform();
    _initialized = true;
  }

  // 检查是否已初始化
  static bool get isInitialized => _initialized;

  // 获取默认版本号
  static String appVersion = EnvConfig.version;
  static const String defaultBuildNumber = '1';

  // 获取版本号
  static String get version => appVersion;

  // 获取构建号
  static String get buildNumber =>
      _initialized ? _packageInfo.buildNumber : defaultBuildNumber;

  // 获取应用名称
  static String get appName =>
      _initialized ? _packageInfo.appName : 'TulingArt';

  // 获取包名
  static String get packageName => _initialized ? _packageInfo.packageName : '';

  // 获取版权年份
  static String get copyrightYear => DateTime.now().year.toString();

  // 获取公司名称
  static const String companyName = 'Turing Art';

  // 获取过期时间
  static DateTime expiredTime = DateTime(2025, 4, 15);

  // 设置过期时间
  static Future<void> setExpiredTime(DateTime time) async {
    expiredTime = time;
  }
}

// 扩展方法
extension AppInfoExtension on AppInfo {
  // 生成版本号字符串
  static String getVersionString() {
    return 'V${AppInfo.version}';
  }

  // 生成完整版本号字符串（包含构建号）
  static String getFullVersionString({bool includeBeta = true}) {
    if (includeBeta) {
      return 'Beta V${AppInfo.version}+${AppInfo.buildNumber}';
    }
    return 'V${AppInfo.version}+${AppInfo.buildNumber}';
  }

  // 生成版权信息字符串
  static String getCopyrightString() {
    return 'Copyright ©️ ${AppInfo.companyName} ${AppInfo.copyrightYear}';
  }

  // 获取过期时间
  static String getExpiredTimeString() {
    return '该内测版本于 ${AppInfo.expiredTime.year}年${AppInfo.expiredTime.month}月${AppInfo.expiredTime.day}日00:00 过期';
  }

  // 获取过期时间用于首页提示条
  static String getExpiredTimeForTipBar() {
    final year = AppInfo.expiredTime.year.toString().substring(2);
    return '$year年${AppInfo.expiredTime.month}月${AppInfo.expiredTime.day}日';
  }
}
