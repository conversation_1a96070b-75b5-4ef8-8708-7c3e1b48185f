import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/constants/constants.dart';
import 'package:turing_art/datalayer/service/share_preferences/shared_preferences_service.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class DeviceInfoUtil {
  static final DeviceInfoUtil _instance = DeviceInfoUtil._internal();
  factory DeviceInfoUtil() => _instance;
  DeviceInfoUtil._internal();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  bool _initialized = false;
  late NetworkProvider _networkProvider;

  // 缓存设备信息
  String _model = '';
  String _osVersion = '1.0.0';
  String _screenSize = '';
  String _deviceId = '';

  /// 初始化设备信息
  Future<void> init(BuildContext context) async {
    if (_initialized) {
      return;
    }

    try {
      // 确保在 Provider 可用的上下文中获取 NetworkProvider
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _networkProvider = context.read<NetworkProvider>();
      });

      // 获取屏幕尺寸
      final size = MediaQuery.of(context).size;
      _screenSize = '${size.width}*${size.height}';

      // 根据平台获取设备信息
      if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        _model = iosInfo.model;
        _osVersion = iosInfo.systemVersion;
        _deviceId = iosInfo.identifierForVendor ?? '';
        PGLog.i(
            'iOS Device Info: model=$_model, osVersion=$_osVersion, deviceId=$_deviceId');
      } else if (Platform.isMacOS) {
        final macInfo = await _deviceInfo.macOsInfo;
        _model = macInfo.model;
        _osVersion =
            '${macInfo.majorVersion}.${macInfo.minorVersion}.${macInfo.patchVersion}';
        _deviceId = macInfo.systemGUID ?? '';
        PGLog.i(
            'macOS Device Info: model=$_model, osVersion=$_osVersion, deviceId=$_deviceId');
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        _model = androidInfo.model;
        _osVersion = androidInfo.version.release;
        _deviceId = androidInfo.id;
        PGLog.i(
            'Android Device Info: model=$_model, osVersion=$_osVersion, deviceId=$_deviceId');
      } else if (Platform.isWindows) {
        try {
          final windowsInfo = await _deviceInfo.windowsInfo;
          _model = Platform.operatingSystem;
          _osVersion = windowsInfo.buildNumber.toString();
          _deviceId =
              windowsInfo.deviceId.replaceAll('{', '').replaceAll('}', '');

          // 检查设备ID是否有效
          if (_deviceId.isEmpty || _deviceId.contains('0x00000000')) {
            throw Exception('Invalid device ID: $_deviceId');
          }

          PGLog.i(
              'Windows Device Info: model=$_model, osVersion=$_osVersion, deviceId=$_deviceId');
        } catch (windowsError) {
          PGLog.w('Failed to get Windows device info: $windowsError');
          // 使用备用方案
          _model = Platform.operatingSystem;
          _osVersion = Platform.operatingSystemVersion;
          _deviceId = const Uuid().v4();
        }
      }

      _initialized = true;
    } catch (e) {
      PGLog.e('Error initializing device info: $e');
      // 设置默认值
      _model = Platform.operatingSystem;
      _osVersion = Platform.operatingSystemVersion;
      final defaultDeviceId = SharedPreferencesService.getStandbyDeviceId();
      if (defaultDeviceId != '') {
        _deviceId = defaultDeviceId;
      } else {
        _deviceId = const Uuid().v4();
      }
      PGLog.i(
          '使用默认值 _model=$_model, _osVersion=$_osVersion, _deviceId=$_deviceId');
      _initialized = true;
    }
  }

  /// 获取平台标识
  /// iOS/macOS 返回 'iOS'
  /// Android/Windows 返回 'Android'
  String get platform {
    if (Platform.isIOS || Platform.isMacOS) {
      return 'iOS';
    } else {
      return 'Android';
    }
  }

  /// 获取应用版本
  String get appVersion => AppInfo.version;

  /// 获取设备型号
  String get model => _model;

  /// 获取系统版本
  String get osVersion => _osVersion;

  /// 获取屏幕尺寸
  String get screenSize => _screenSize;

  /// 获取语言
  String get language => Platform.localeName;

  /// 获取时区偏移（秒）
  String get utcOffset => DateTime.now().timeZoneOffset.inSeconds.toString();

  /// 获取区域设置
  String get locale => Platform.localeName.replaceAll('-', '_');

  /// 获取网络状态
  String get network => _networkProvider.networkTypeString;

  /// 获取渠道信息
  String get channel {
    if (Platform.isIOS) return 'appstore';
    if (Platform.isMacOS) return 'macappstore';
    if (Platform.isWindows) return 'msstore';
    return 'googleplay'; // Android
  }

  /// 获取初始化时间戳
  String get initStamp =>
      (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();

  /// 是否为调试模式
  bool get isDebugMode => isDebug;

  String get deviceId => _deviceId;
}
