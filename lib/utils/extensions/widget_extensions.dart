import 'package:flutter/material.dart';

/// 扩展GlobalKey以获取关联Widget的Rect
extension GlobalKeyExtension on GlobalKey {
  /// 获取关联Widget在全局坐标系中的边界矩形
  ///
  /// 这个方法使用RenderBox的localToGlobal方法将局部坐标转换为全局坐标
  /// 返回一个包含位置和大小的Rect，如果Widget尚未渲染，则返回null
  Rect? get globalRect {
    final renderObject = currentContext?.findRenderObject();
    if (renderObject != null && renderObject is RenderBox) {
      final position = renderObject.localToGlobal(Offset.zero);
      return Rect.fromLTWH(
        position.dx,
        position.dy,
        renderObject.size.width,
        renderObject.size.height,
      );
    }
    return null;
  }
}
