import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 下载配置类
class DownloadConfig {
  /// 默认下载目录名称
  static const String defaultDownloadDirName = 'downloads';

  /// 默认下载超时时间(毫秒)
  static const int defaultConnectTimeout = 30000;

  /// 默认接收超时时间(毫秒)
  static const int defaultReceiveTimeout = 30000;

  /// 自定义下载目录路径
  static String? _customDownloadDirPath;

  /// 设置自定义下载目录
  /// [customPath] 自定义目录路径
  static Future<void> setCustomDownloadDir(String customPath) async {
    final dir = Directory(customPath);
    if (!dir.existsSync()) {
      await dir.create(recursive: true);
    }
    _customDownloadDirPath = customPath;
    PGLog.d('设置自定义下载目录: $customPath');
  }

  /// 获取下载目录
  /// 如果设置了自定义目录，则使用自定义目录
  /// 否则使用应用文档目录下的默认下载目录
  static Future<String> getDownloadDir() async {
    if (_customDownloadDirPath != null) {
      // 确保目录存在
      final dir = Directory(_customDownloadDirPath!);
      if (!dir.existsSync()) {
        await dir.create(recursive: true);
      }
      return _customDownloadDirPath!;
    }

    // 使用默认应用文档目录
    final appDocDir = await getApplicationDocumentsDirectory();
    final downloadDir = path.join(appDocDir.path, defaultDownloadDirName);

    // 确保目录存在
    final dir = Directory(downloadDir);
    if (!dir.existsSync()) {
      await dir.create(recursive: true);
    }

    return downloadDir;
  }

  /// 重置为默认下载目录（清除自定义目录设置）
  static void resetToDefaultDownloadDirectory() {
    _customDownloadDirPath = null;
    PGLog.d('已重置为默认下载目录');
  }

  /// 获取临时下载目录
  static Future<String> getTempDownloadDir() async {
    if (_customDownloadDirPath != null) {
      final tempDirPath = path.join(_customDownloadDirPath!, 'temp');
      // 确保目录存在
      final dir = Directory(tempDirPath);
      if (!dir.existsSync()) {
        await dir.create(recursive: true);
      }
      return tempDirPath;
    }

    final tempDir = await getTemporaryDirectory();
    final downloadTempDir = path.join(tempDir.path, defaultDownloadDirName);

    // 确保目录存在
    final dir = Directory(downloadTempDir);
    if (!dir.existsSync()) {
      await dir.create(recursive: true);
    }

    return downloadTempDir;
  }

  /// 生成完整的文件路径
  static Future<String> generateFilePath(String fileName) async {
    final downloadDir = await getDownloadDir();
    return path.join(downloadDir, fileName);
  }

  /// 获取当前使用的下载目录路径
  static Future<String> getCurrentDownloadDirPath() async {
    return await getDownloadDir();
  }

  /// 检查是否正在使用自定义下载目录
  static bool isUsingCustomDownloadDir() {
    return _customDownloadDirPath != null;
  }

  /// 获取Dio的连接超时设置
  static Duration get connectTimeout =>
      const Duration(milliseconds: defaultConnectTimeout);

  /// 获取Dio的接收超时设置
  static Duration get receiveTimeout =>
      const Duration(milliseconds: defaultReceiveTimeout);
}
