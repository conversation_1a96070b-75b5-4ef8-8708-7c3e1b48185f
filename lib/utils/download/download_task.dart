import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:turing_art/core/service/disk_space_size/disk_free_space_size_service.dart';
import 'package:turing_art/utils/download/download_status.dart';
import 'package:turing_art/utils/download/download_task_interface.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// DownloadTask - 下载任务类
///
/// 使用示例：
/// ```dart
/// final task = DownloadTask(
///   taskId: 'unique_task_id',
///   url: 'https://example.com/image.jpg',
///   filePath: '/path/to/save/image.jpg',
/// );
///
/// await task.start(
///   onProgress: (progress, speed, remainingTime) {
///     print('下载进度: ${(progress * 100).toStringAsFixed(1)}%');
///   },
///   onComplete: (file) {
///     print('下载完成: ${file.path}');
///   },
///   onError: (error) {
///     print('下载失败: $error');
///   },
/// );
/// ```
///
/// 特性：
/// - ✅ 支持断点续传
/// - ✅ 使用临时文件确保原子性操作
/// - ✅ 自动磁盘空间检查
/// - ✅ 完整的进度和状态回调
/// - ✅ 支持暂停/恢复/取消操作
/// - ✅ 优化了通知机制，仅使用回调函数，性能更佳

// 下载任务
class DownloadTask extends DownloadTaskInterface {
  /// 任务ID（可选，外部指定）
  final String? taskId;

  /// 磁盘空间大小服务
  final DiskFreeSpaceSizeService _diskFreeSpaceSizeService =
      DiskFreeSpaceSizeService.forPlatform();

  // Dio实例，用于执行网络请求
  final Dio _dio = Dio();

  // 下载URL
  String _downloadUrl = '';
  String get downloadUrl => _downloadUrl;

  // 临时文件后缀
  static const String _tempFileSuffix = '.tmp';

  // 最终文件路径和临时文件路径
  String _finalFilePath = '';
  String get finalFilePath => _finalFilePath;
  String _tempFilePath = '';
  String get tempFilePath => _tempFilePath;

  // 下载状态
  DownloadStatus _status = DownloadStatus.notStarted;
  DownloadStatus get status => _status;

  // 下载进度
  double _progress = 0.0;
  double get progress => _progress;

  // 下载速度(bytes/s)
  double _speed = 0.0;
  double get speed => _speed;

  // 已下载大小(bytes)
  int _downloadedBytes = 0;
  int get downloadedBytes => _downloadedBytes;

  // 总大小(bytes)
  int _totalBytes = 0;
  int get totalBytes => _totalBytes;

  // 下载开始时间
  DateTime? _startTime;

  // 上次进度更新时间
  DateTime? _lastProgressUpdate;

  // 上次已下载字节数
  int _lastDownloadedBytes = 0;

  // 取消令牌
  CancelToken? _cancelToken;

  // 是否正在取消下载
  bool _isCancelling = false;

  /// 构造函数
  DownloadTask({
    this.taskId,
    String? url,
    String? filePath,
  }) {
    if (url != null) {
      _downloadUrl = url;
    }
    if (filePath != null) {
      _finalFilePath = filePath;
      _tempFilePath = '$filePath$_tempFileSuffix';
    }
  }

  /// 启动下载（使用预设参数）
  Future<void> start({
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    if (_downloadUrl.isEmpty || _finalFilePath.isEmpty) {
      onError?.call('下载参数未设置，请通过构造函数提供url和filePath');
      return;
    }

    return startDownload(
      _downloadUrl,
      fullFilePath: _finalFilePath,
      onProgress: onProgress,
      onComplete: onComplete,
      onError: onError,
    );
  }

  @override
  Future<void> startDownload(
    String url, {
    required String fullFilePath,
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    // 更新下载配置
    _downloadUrl = url;
    _finalFilePath = fullFilePath;
    _tempFilePath = '$fullFilePath$_tempFileSuffix';

    try {
      // 重置状态（但保留配置信息）
      _resetDownloadStatus();

      // 确保目录存在
      await FileManager().createDirectory(_finalFilePath);

      // 清理可能存在的文件
      await _cleanExistingFiles();

      // 创建取消令牌
      _cancelToken = CancelToken();

      // 记录开始时间
      _startTime = DateTime.now();
      _lastProgressUpdate = _startTime;

      int startBytes = 0;

      // 设置请求头，支持断点续传
      final headers = <String, dynamic>{};
      if (startBytes > 0) {
        headers['Range'] = 'bytes=$startBytes-';
      }

/*
      // 发送HEAD请求获取文件大小
      if (_totalBytes == 0) {
        try {
          final headResponse = await _dio.head(
            _downloadUrl,
            options: Options(headers: headers),
          );
          _totalBytes = _parseContentLength(headResponse.headers) ?? 0;
        } catch (e) {
          PGLog.w('获取文件大小失败: $e');
        }
      }

      /// 检查磁盘空间是否足够
      bool freeSpaceChecked = await _checkDiskFreeSpaceAvailable(_totalBytes);
      if (!freeSpaceChecked) {
        _updateStatus(DownloadStatus.failed);
        PGLog.e('🚫 >>> 下载任务磁盘空间不足！');
        onError?.call('磁盘空间不足，请清理后重试');
        return;
      }
*/
      // 更新状态
      _updateStatus(DownloadStatus.downloading);

      // 开始下载 - 增加安全回调包装
      await _dio.download(
        _downloadUrl,
        _tempFilePath, // 先下载到临时文件
        cancelToken: _cancelToken,
        onReceiveProgress: (received, total) {
          try {
            // 安全的进度更新
            _safeUpdateProgress(received, total, onProgress);
          } catch (e) {
            PGLog.e('进度回调出错: $e');
          }
        },
      );

      // 下载完成，重命名文件
      if (_status != DownloadStatus.canceled && !_isCancelling) {
        final tempFile = File(_tempFilePath);
        if (tempFile.existsSync()) {
          try {
            final finalFile = await tempFile.rename(_finalFilePath);
            PGLog.d('下载完成，重命名文件成功: $finalFile');
            _updateStatus(DownloadStatus.completed);
            _progress = 1.0;
            _safeNotifyCompletion(finalFile, onComplete);
          } catch (e) {
            PGLog.e('下载完成，重命名文件失败: $e');
            _updateStatus(DownloadStatus.failed);
            _safeNotifyError(e.toString(), onError);
          }
        } else {
          PGLog.e('下载完成，临时文件不存在');
          _updateStatus(DownloadStatus.failed);
          _safeNotifyError('下载完成，临时文件不存在', onError);
        }
      }
    } catch (e) {
      // 如果不是取消导致的错误，则标记为失败
      if (_status != DownloadStatus.canceled && !_isCancelling) {
        _updateStatus(DownloadStatus.failed);

        // 安全的错误通知
        _safeNotifyError(e.toString(), onError);

        PGLog.e('下载失败: $e');
      }
    }
  }

  @override
  Future<void> pauseDownload() async {
    if (_status == DownloadStatus.downloading) {
      try {
        // 先更新状态，防止取消操作触发错误处理逻辑
        _updateStatus(DownloadStatus.paused);

        // 确保取消令牌有效
        if (_cancelToken != null && !_cancelToken!.isCancelled) {
          // 记录当前已下载的字节数，确保恢复时能正确计算
          final tempFile = File(_tempFilePath);
          if (tempFile.existsSync()) {
            _downloadedBytes = tempFile.lengthSync();
            _lastDownloadedBytes = _downloadedBytes;
            PGLog.d('暂停下载，已下载字节数: $_downloadedBytes');
          }

          // 取消下载
          _cancelToken!.cancel('用户暂停下载');

          PGLog.d('下载已暂停，已下载: $_downloadedBytes / $_totalBytes');
        }
      } catch (e) {
        PGLog.e('暂停下载时出错: $e');
      }
    }
  }

  @override
  Future<void> resumeDownload({
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    if (_status != DownloadStatus.paused) {
      onError?.call('任务不是暂停状态，无法恢复下载');
      return;
    }

    try {
      final tempFile = File(_tempFilePath);
      int startBytes = 0;

      // 获取已下载的字节数
      if (tempFile.existsSync()) {
        startBytes = tempFile.lengthSync();
        _downloadedBytes = startBytes;
        _lastDownloadedBytes = startBytes;
        PGLog.d('恢复下载，起始字节: $startBytes');
      }

      /// 检查磁盘空间是否足够
      bool freeSpaceChecked =
          await _checkDiskFreeSpaceAvailable(_totalBytes - startBytes);
      if (!freeSpaceChecked) {
        PGLog.e('🚫 >>> 恢复下载任务磁盘空间不足！');
        onError?.call('磁盘空间不足，请清理后重试');
        return;
      }

      // 检查是否已经下载完成
      if (_totalBytes > 0 && startBytes >= _totalBytes) {
        // 已下载完成，直接重命名文件
        final finalFile = await tempFile.rename(_finalFilePath);
        _updateStatus(DownloadStatus.completed);
        _progress = 1.0;
        onComplete?.call(finalFile);
        return;
      }

      // 更新状态
      _updateStatus(DownloadStatus.downloading);

      // 创建新的取消令牌
      _cancelToken = CancelToken();

      // 记录开始时间
      _startTime = DateTime.now();
      _lastProgressUpdate = _startTime;

      // 设置请求头，支持断点续传
      final headers = <String, dynamic>{
        'Range': 'bytes=$startBytes-',
      };

      // 发送初始进度信息
      if (_totalBytes > 0) {
        _progress = startBytes / _totalBytes;

        // 回调初始进度
        onProgress?.call(_progress, '0B/s', '计算中...');
      }

      // 使用IOSink打开文件进行追加写入
      final IOSink fileSink = tempFile.openWrite(mode: FileMode.append);

      try {
        // 使用Dio发送请求，但手动处理响应流
        final response = await _dio.get(
          _downloadUrl,
          options: Options(
            headers: headers,
            responseType: ResponseType.stream,
            receiveTimeout: const Duration(minutes: 30),
          ),
          cancelToken: _cancelToken,
        );

        // 获取响应流
        final responseStream = response.data.stream as Stream<List<int>>;

        // 更新总大小
        if (response.headers.value('content-length') != null) {
          final contentLength =
              int.parse(response.headers.value('content-length')!);
          _totalBytes = startBytes + contentLength;
          PGLog.d('总大小更新为: $_totalBytes');
        }

        // 处理响应流
        int receivedBytes = 0;
        DateTime lastUIUpdateTime = DateTime.now();

        await for (final chunk in responseStream) {
          // 写入文件
          fileSink.add(chunk);

          // 更新接收的字节数
          receivedBytes += chunk.length;
          _downloadedBytes = startBytes + receivedBytes;

          // 计算进度
          if (_totalBytes > 0) {
            _progress = _downloadedBytes / _totalBytes;
            _progress = _progress.clamp(0.0, 1.0);
          }

          // 限制UI更新频率（每500毫秒）
          final now = DateTime.now();
          final timeSinceLastUpdate =
              now.difference(lastUIUpdateTime).inMilliseconds;

          if (timeSinceLastUpdate >= 500) {
            // 计算下载速度
            final duration =
                now.difference(_lastProgressUpdate!).inMilliseconds;
            if (duration > 0) {
              final bytesDownloadedSinceLastUpdate =
                  _downloadedBytes - _lastDownloadedBytes;
              _speed = (bytesDownloadedSinceLastUpdate / duration) * 1000;
            }

            // 更新最后更新时间和已下载字节数
            _lastProgressUpdate = now;
            _lastDownloadedBytes = _downloadedBytes;

            // 安全的进度回调
            try {
              onProgress?.call(
                  _progress, getFormattedSpeed(), getFormattedRemainingTime());
            } catch (e) {
              PGLog.e('进度回调执行失败: $e');
            }

            lastUIUpdateTime = now;
          }

          // 检查是否取消
          if (_cancelToken?.isCancelled ?? false) {
            PGLog.d('下载已取消');
            break;
          }
        }

        // 关闭文件
        await fileSink.flush();
        await fileSink.close();

        // 如果下载完成且未取消，重命名文件
        if (_status == DownloadStatus.downloading) {
          final finalFile = await tempFile.rename(_finalFilePath);

          // 更新状态
          _updateStatus(DownloadStatus.completed);
          _progress = 1.0;

          // 安全的完成回调
          try {
            onComplete?.call(finalFile);
          } catch (e) {
            PGLog.e('完成回调执行失败: $e');
          }
        }
      } finally {
        // 确保文件被关闭
        await fileSink.close().catchError((e) {
          PGLog.e('关闭文件时出错: $e');
        });
      }
    } catch (e) {
      // 只有在状态不是暂停时才报告错误
      if (_status != DownloadStatus.paused) {
        PGLog.e('恢复下载失败: $e');
        _updateStatus(DownloadStatus.failed);
        onError?.call('恢复下载失败: ${e.toString()}');
      }
    }
  }

  @override
  Future<void> cancelDownload() async {
    if (_status == DownloadStatus.downloading ||
        _status == DownloadStatus.paused) {
      _isCancelling = true;

      // 取消下载
      if (_status == DownloadStatus.downloading) {
        _cancelToken?.cancel('取消下载');
      }

      // 更新状态
      _updateStatus(DownloadStatus.canceled);

      // 删除临时文件
      await _cleanExistingFiles(cleanFinalFile: true);

      _isCancelling = false;
    }
  }

  /// 解析Content-Length头
  int? _parseContentLength(Headers headers) {
    final contentRange = headers.value('content-range');
    if (contentRange != null) {
      // 格式: bytes start-end/total
      final match = RegExp(r'bytes \d+-\d+/(\d+)').firstMatch(contentRange);
      if (match != null && match.groupCount >= 1) {
        return int.parse(match.group(1)!);
      }
    }

    final contentLength = headers.value('content-length');
    if (contentLength != null) {
      return int.parse(contentLength);
    }

    return null;
  }

  /// 检查磁盘空间是否足够
  /// @param fileLength 文件长度，单位为字节
  Future<bool> _checkDiskFreeSpaceAvailable(int fileLength) async {
    final freeSpace =
        await _diskFreeSpaceSizeService.getRootDirectoryFreeDiskSpaceSize();
    if (freeSpace == null || freeSpace < 0) {
      return false;
    }
    return freeSpace >= fileLength;
  }

  // 清理已存在的文件
  Future<void> _cleanExistingFiles({bool cleanFinalFile = false}) async {
    try {
      // 清理可能存在的临时文件
      final tempFile = File(_tempFilePath);
      if (tempFile.existsSync()) {
        await tempFile.delete();
        PGLog.d('已删除临时文件: $_tempFilePath');
      }

      // 根据参数决定是否删除最终文件
      if (cleanFinalFile) {
        final finalFile = File(_finalFilePath);
        if (finalFile.existsSync()) {
          await finalFile.delete();
          PGLog.d('已删除最终文件: $_finalFilePath');
        }
      }
    } catch (e) {
      PGLog.e('清理文件时出错: $e');
    }
  }

  // 更新下载状态
  void _updateStatus(DownloadStatus newStatus) {
    try {
      _status = newStatus;
    } catch (e) {
      PGLog.e('更新状态失败: $e');
    }
  }

  // 重置下载状态
  void _resetDownloadStatus() {
    _status = DownloadStatus.notStarted;
    _progress = 0.0;
    _speed = 0.0;
    _downloadedBytes = 0;
    _totalBytes = 0;
    _startTime = null;
    _lastProgressUpdate = null;
    _lastDownloadedBytes = 0;
    _cancelToken = null;
    _isCancelling = false;
  }

  // 安全的进度更新
  void _safeUpdateProgress(
    int received,
    int total,
    Function(double progress, String speed, String remainingTime)? onProgress,
  ) {
    try {
      // 更新已下载和总字节数
      _downloadedBytes = received;
      _totalBytes = total > 0 ? total : 0;

      // 计算进度
      _progress = total > 0 ? received / total : 0;
      _progress = _progress.clamp(0.0, 1.0); // 确保进度在合理范围内

      // 计算下载速度
      final now = DateTime.now();
      if (_lastProgressUpdate != null) {
        final duration = now.difference(_lastProgressUpdate!).inMilliseconds;
        if (duration > 0) {
          // 计算速度（bytes/s）
          final bytesDownloadedSinceLastUpdate =
              received - _lastDownloadedBytes;
          _speed = (bytesDownloadedSinceLastUpdate / duration) * 1000;
          _speed = _speed.clamp(0.0, double.maxFinite); // 防止异常数值
        }
      }

      // 更新最后进度更新时间和字节数
      _lastProgressUpdate = now;
      _lastDownloadedBytes = received;

      // 计算剩余时间
      String remainingTime = _calculateRemainingTime();

      // 格式化速度
      String speedString = _formatSpeed(_speed);

      // 安全的进度回调
      try {
        onProgress?.call(_progress, speedString, remainingTime);
      } catch (e) {
        PGLog.e('进度回调执行失败: $e');
      }
    } catch (e) {
      PGLog.e('更新进度时发生错误: $e');
    }
  }

  // 安全的完成通知
  void _safeNotifyCompletion(
    File file,
    Function(File)? onComplete,
  ) {
    try {
      onComplete?.call(file);
    } catch (e) {
      PGLog.e('完成回调执行失败: $e');
    }
  }

  // 安全的错误通知
  void _safeNotifyError(
    String error,
    Function(String)? onError,
  ) {
    try {
      onError?.call(error);
    } catch (e) {
      PGLog.e('错误回调执行失败: $e');
    }
  }

  // 计算剩余时间
  String _calculateRemainingTime() {
    if (_speed <= 0 || _totalBytes <= 0 || _downloadedBytes >= _totalBytes) {
      return '--:--';
    }

    // 计算剩余字节
    final remainingBytes = _totalBytes - _downloadedBytes;

    // 计算剩余秒数
    final remainingSeconds = (remainingBytes / _speed).round();

    // 格式化为分:秒
    if (remainingSeconds < 60) {
      return '00:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      final minutes = (remainingSeconds / 60).floor();
      final seconds = remainingSeconds % 60;

      if (minutes < 60) {
        return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      } else {
        final hours = (minutes / 60).floor();
        final mins = minutes % 60;
        return '${hours.toString().padLeft(2, '0')}:${mins.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      }
    }
  }

  // 格式化速度
  String _formatSpeed(double bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return '${bytesPerSecond.toStringAsFixed(1)}B/s';
    } else if (bytesPerSecond < 1024 * 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)}KB/s';
    } else if (bytesPerSecond < 1024 * 1024 * 1024) {
      return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)}MB/s';
    } else {
      return '${(bytesPerSecond / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB/s';
    }
  }

  // 格式化文件大小
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  // 获取格式化的已下载大小
  String getFormattedDownloadedSize() {
    return formatFileSize(_downloadedBytes);
  }

  // 获取格式化的总大小
  String getFormattedTotalSize() {
    return formatFileSize(_totalBytes);
  }

  // 获取格式化的速度
  String getFormattedSpeed() {
    return _formatSpeed(_speed);
  }

  // 获取格式化的剩余时间
  String getFormattedRemainingTime() {
    return _calculateRemainingTime();
  }

  // 释放资源
  void dispose() {
    try {
      _cancelToken?.cancel('任务销毁');
    } catch (e) {
      PGLog.e('取消令牌时出错: $e');
    }
  }
}
