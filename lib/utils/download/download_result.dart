import 'dart:io';

/// 下载结果类
///
/// 用于表示下载操作的结果，包含下载URL、文件路径、成功状态和错误信息等
class DownloadResult {
  /// 下载URL
  final String url;

  /// 文件保存路径
  final String filePath;

  /// 下载是否成功
  final bool success;

  /// 错误信息，如果下载失败则包含错误原因
  final String? errorMessage;

  /// 下载的文件对象，如果下载成功则包含文件对象
  final File? file;

  /// 任务ID
  final String taskId;

  /// 构造函数
  DownloadResult({
    required this.url,
    required this.filePath,
    required this.success,
    required this.taskId,
    this.errorMessage,
    this.file,
  });

  /// 创建表示下载成功的结果
  factory DownloadResult.success({
    required String url,
    required String filePath,
    required String taskId,
    required File file,
  }) {
    return DownloadResult(
      url: url,
      filePath: filePath,
      success: true,
      taskId: taskId,
      file: file,
    );
  }

  /// 创建表示下载失败的结果
  factory DownloadResult.failure({
    required String url,
    required String filePath,
    required String taskId,
    required String errorMessage,
  }) {
    return DownloadResult(
      url: url,
      filePath: filePath,
      success: false,
      taskId: taskId,
      errorMessage: errorMessage,
    );
  }
}
