import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/utils/pg_log.dart';

/// 下载工具类
class DownloadUtil {
  /// 根据URL生成唯一的任务ID
  /// 使用MD5算法对URL进行哈希处理，确保任务ID的唯一性
  static String generateTaskId(String url) {
    final bytes = utf8.encode(url);
    final digest = md5.convert(bytes);
    return 'download_${digest.toString()}';
  }

  /// 从URL提取文件名
  /// 如果无法提取，则返回默认文件名
  static String extractFileName(String url, [String defaultName = 'download']) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      if (pathSegments.isNotEmpty) {
        final fileName = pathSegments.last;
        if (fileName.isNotEmpty) {
          return fileName;
        }
      }

      return '$defaultName${_getExtensionFromUrl(url)}';
    } catch (e) {
      PGLog.e('提取文件名失败: $e');
      return '$defaultName${_getExtensionFromUrl(url)}';
    }
  }

  /// 从URL提取文件扩展名
  static String _getExtensionFromUrl(String url) {
    try {
      final fileName = path.basename(url);
      final extension = path.extension(fileName);
      return extension.isNotEmpty ? extension : '';
    } catch (e) {
      return '';
    }
  }
}
