import 'dart:io';

// 单个下载任务接口
abstract class DownloadTaskInterface {
  /// 开始下载
  /// [url] 下载地址
  /// [fullFilePath] 本地文件夹路径全名
  /// [onProgress] 进度回调
  /// [onComplete] 完成回调
  /// [onError] 错误回调
  Future<void> startDownload(
    String url, {
    required String fullFilePath,
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  });

  /// 恢复下载
  /// [onProgress] 进度回调
  /// [onComplete] 完成回调
  /// [onError] 错误回调
  Future<void> resumeDownload({
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  });

  /// 暂停下载
  Future<void> pauseDownload();

  /// 取消下载
  Future<void> cancelDownload();
}
