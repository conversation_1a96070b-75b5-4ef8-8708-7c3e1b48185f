import 'dart:async';
import 'dart:io';

import 'package:turing_art/utils/download/download_result.dart';
import 'package:turing_art/utils/download/download_status.dart';
import 'package:turing_art/utils/download/download_task.dart';
import 'package:turing_art/utils/download/download_util.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 队列中的下载任务信息
class _QueuedDownloadTask {
  final String taskId;
  final String url;
  final String filePath;
  final Function(double, String, String)? onProgress;
  final Function(File)? onComplete;
  final Function(String)? onError;
  final Completer<DownloadResult>? resultCompleter; // 可选，只有串行模式需要

  _QueuedDownloadTask({
    required this.taskId,
    required this.url,
    required this.filePath,
    this.resultCompleter, // 改为可选参数
    this.onProgress,
    this.onComplete,
    this.onError,
  });

  /// 清理资源，避免内存泄漏
  void dispose() {
    if (resultCompleter != null && !resultCompleter!.isCompleted) {
      resultCompleter!.complete(DownloadResult.failure(
        url: url,
        filePath: filePath,
        taskId: taskId,
        errorMessage: '任务被清理',
      ));
    }
  }
}

/// 下载管理器 - 负责管理多个下载任务
///
/// 主要对外API：
/// 1. 单任务下载: download(DownloadTask) - 推荐使用
/// 2. 批量串行下载: downloadSerial(List<DownloadTask>)
/// 3. 批量并发下载: downloadConcurrent(List<DownloadTask>)
/// 4. 任务管理: pauseDownload/resumeDownload/cancelDownload/retryDownload/removeTask
/// 5. 状态查询: getTask/allTasks
/// 6. 配置: setMaxConcurrentDownloads
///
/// 核心架构：
/// - 统一使用DownloadTask对象
/// - 串行模式(maxConcurrent=1)：直接等待，不使用队列
/// - 并发模式(maxConcurrent>1)：使用队列机制管理任务
/// - 使用回调函数进行进度和状态通知，性能更优
class DownloadManager {
  static final DownloadManager _instance = DownloadManager._internal();

  factory DownloadManager() => _instance;

  DownloadManager._internal();

  /// 下载任务列表，使用Map存储，key为任务ID
  final Map<String, DownloadTask> _downloadTasks = {};

  /// 最大并发下载数，默认为3
  int _maxConcurrentDownloads = 3;

  /// 当前正在进行的并发下载数
  int _currentConcurrentDownloads = 0;

  /// 等待下载的任务队列
  final List<String> _pendingDownloadTasks = [];

  /// 队列中任务的详细信息
  final Map<String, _QueuedDownloadTask> _queuedTasks = {};

  // ==================== 对外API ====================

  /// 设置最大并发下载数
  void setMaxConcurrentDownloads(int maxConcurrent) {
    if (maxConcurrent > 0) {
      _maxConcurrentDownloads = maxConcurrent;
      PGLog.d('设置最大并发下载数: $_maxConcurrentDownloads');
    }
  }

  /// 获取最大并发下载数
  int get maxConcurrentDownloads => _maxConcurrentDownloads;

  /// 单个文件下载（推荐使用）
  ///
  /// [task] 下载任务对象
  /// [onProgress] 进度回调
  /// [onComplete] 完成回调
  /// [onError] 错误回调
  ///
  /// 返回下载结果
  Future<DownloadResult> download(
    DownloadTask task, {
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    setMaxConcurrentDownloads(1);
    return await _addSerialDownloadTask(
      task,
      onProgress: onProgress,
      onComplete: onComplete,
      onError: onError,
    );
  }

  /// 批量串行下载（推荐使用）
  ///
  /// [downloadTasks] 下载任务列表
  /// [onProgress] 进度回调 (taskId, progress, speed, remainingTime)
  /// [onComplete] 完成回调 (taskId, file)
  /// [onError] 错误回调 (taskId, error)
  ///
  /// 返回下载结果列表
  Future<List<DownloadResult>> downloadSerial(
    List<DownloadTask> downloadTasks, {
    Function(
            String taskId, double progress, String speed, String remainingTime)?
        onProgress,
    Function(String taskId, File file)? onComplete,
    Function(String taskId, String error)? onError,
  }) async {
    setMaxConcurrentDownloads(1);
    return await _addSerialMultipleDownloads(
      downloadTasks,
      onProgress: onProgress,
      onComplete: onComplete,
      onError: onError,
    );
  }

  /// 批量并发下载（推荐使用）
  ///
  /// [downloadTasks] 下载任务列表
  /// [maxConcurrent] 此次下载的最大并发数，不指定则使用默认值
  /// [onProgress] 进度回调 (taskId, progress, speed, remainingTime)
  /// [onComplete] 完成回调 (taskId, file)
  /// [onError] 错误回调 (taskId, error)
  ///
  /// 返回任务ID列表，通过回调函数获取下载进度和结果
  Future<List<String>> downloadConcurrent(
    List<DownloadTask> downloadTasks, {
    int? maxConcurrent,
    Function(
            String taskId, double progress, String speed, String remainingTime)?
        onProgress,
    Function(String taskId, File file)? onComplete,
    Function(String taskId, String error)? onError,
  }) async {
    // 临时设置并发数
    final originalMaxConcurrent = _maxConcurrentDownloads;
    if (maxConcurrent != null && maxConcurrent > 0) {
      setMaxConcurrentDownloads(maxConcurrent);
    }

    try {
      return _addConcurrentMultipleDownloads(
        downloadTasks,
        onProgress: onProgress,
        onComplete: onComplete,
        onError: onError,
      );
    } finally {
      // 恢复原来的并发数
      if (maxConcurrent != null && maxConcurrent > 0) {
        setMaxConcurrentDownloads(originalMaxConcurrent);
      }
    }
  }

  /// 暂停下载
  Future<bool> pauseDownload(String taskId) async {
    final task = _downloadTasks[taskId];
    if (task != null && task.status == DownloadStatus.downloading) {
      await task.pauseDownload();
      return true;
    }
    return false;
  }

  /// 恢复下载
  Future<bool> resumeDownload(
    String taskId, {
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    final task = _downloadTasks[taskId];
    if (task != null && task.status == DownloadStatus.paused) {
      // 包装回调函数，确保正确处理任务完成
      void onCompleteWrapper(File file) {
        onComplete?.call(file);
        _onTaskCompleted(taskId); // 确保任务完成时处理并发计数
      }

      void onErrorWrapper(String error) {
        onError?.call(error);
        _onTaskCompleted(taskId); // 确保任务失败时处理并发计数
      }

      await task.resumeDownload(
        onProgress: onProgress,
        onComplete: onCompleteWrapper,
        onError: onErrorWrapper,
      );
      return true;
    }
    return false;
  }

  /// 取消下载
  Future<bool> cancelDownload(String taskId) async {
    final task = _downloadTasks[taskId];

    // 从待下载队列中移除（如果存在）
    _pendingDownloadTasks.remove(taskId);

    // 清理队列中保存的参数
    _queuedTasks.remove(taskId);

    if (task != null &&
        (task.status == DownloadStatus.downloading ||
            task.status == DownloadStatus.paused)) {
      await task.cancelDownload();
      _onTaskCompleted(taskId);
      return true;
    }
    return false;
  }

  /// 重试下载
  Future<DownloadResult> retryDownload(
    String taskId, {
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    final task = _downloadTasks[taskId];
    if (task != null &&
        (task.status == DownloadStatus.failed ||
            task.status == DownloadStatus.canceled)) {
      // 获取原始URL和文件路径
      final url = task.downloadUrl;
      final filePath = task.finalFilePath;

      if (url.isEmpty || filePath.isEmpty) {
        const errorMessage = '重试下载失败：缺少URL或文件路径';
        PGLog.e(errorMessage);
        onError?.call(errorMessage);
        return DownloadResult.failure(
          url: url,
          filePath: filePath,
          taskId: taskId,
          errorMessage: errorMessage,
        );
      }

      // 移除旧任务
      await removeTask(taskId);

      // 创建新任务
      final newTask = DownloadTask(
        taskId: taskId,
        url: url,
        filePath: filePath,
      );
      setMaxConcurrentDownloads(1);
      // 目前串行下载
      return await _addSerialDownloadTask(
        newTask,
        onProgress: onProgress,
        onComplete: onComplete,
        onError: onError,
      );
    }

    const errorMessage = '无法重试：任务不存在或状态不允许重试';
    onError?.call(errorMessage);
    return DownloadResult.failure(
      url: task?.downloadUrl ?? '',
      filePath: task?.finalFilePath ?? '',
      taskId: taskId,
      errorMessage: errorMessage,
    );
  }

  /// 移除任务
  Future<bool> removeTask(String taskId) async {
    return await _removeTask(taskId);
  }

  /// 获取特定任务
  DownloadTask? getTask(String taskId) {
    return _downloadTasks[taskId];
  }

  /// 获取当前所有下载任务
  Map<String, DownloadTask> get allTasks => Map.unmodifiable(_downloadTasks);

  /// 处理待下载队列中的下一个任务
  void _processNextPendingDownloads() {
    PGLog.d('开始处理待下载队列中的下一个任务: ${_pendingDownloadTasks.length}');
    // 串行模式不处理队列
    if (_maxConcurrentDownloads <= 1) {
      PGLog.d('异常：串行模式不处理队列');
      return;
    }

    while (_currentConcurrentDownloads < _maxConcurrentDownloads &&
        _pendingDownloadTasks.isNotEmpty) {
      final taskId = _pendingDownloadTasks.removeAt(0);
      final task = _downloadTasks[taskId];
      final taskParams = _queuedTasks[taskId];

      if (task != null &&
          taskParams != null &&
          task.status == DownloadStatus.notStarted) {
        _currentConcurrentDownloads++;
        PGLog.d('从备用队列抽取第一个任务: $taskId，当前并发数: $_currentConcurrentDownloads');

        // 获取保存的参数
        final url = taskParams.url;
        final filePath = taskParams.filePath;
        final onProgress = taskParams.onProgress;
        final onComplete = taskParams.onComplete;
        final onError = taskParams.onError;
        final resultCompleter = taskParams.resultCompleter;

        // 包装回调函数，确保正确处理任务完成
        void onCompleteWrapper(File file) {
          onComplete?.call(file);
          _onTaskCompleted(taskId); // 正确处理任务完成
        }

        void onErrorWrapper(String error) {
          onError?.call(error);
          _onTaskCompleted(taskId); // 正确处理任务完成
        }

        // 启动下载任务
        task
            .start(
          onProgress: onProgress,
          onComplete: onCompleteWrapper, // 使用包装后的回调
          onError: onErrorWrapper, // 使用包装后的回调
        )
            .catchError((error) {
          PGLog.e('从队列启动下载任务失败: $taskId -> $error');
          _onTaskCompleted(taskId); // 确保异常时也处理任务完成
          if (resultCompleter != null && !resultCompleter.isCompleted) {
            resultCompleter.complete(DownloadResult.failure(
              url: url,
              filePath: filePath,
              taskId: taskId,
              errorMessage: error.toString(),
            ));
          }
        }).whenComplete(() {
          // 清理保存的参数
          _queuedTasks.remove(taskId);
        });
      }
    }
  }

  /// 下载任务完成时调用（包括成功、失败、取消）
  void _onTaskCompleted(String taskId) {
    if (_currentConcurrentDownloads > 0) {
      _currentConcurrentDownloads--;
      PGLog.d('下载任务完成: $taskId，当前并发数: $_currentConcurrentDownloads');
    }

    // 只在真正的并发模式下才处理队列（maxConcurrent > 1）
    // 串行模式（maxConcurrent = 1）不使用队列机制
    if (_maxConcurrentDownloads > 1) {
      _processNextPendingDownloads();
    }

    // 自动清理已完成的任务（延迟执行，避免影响当前任务）
    Future.delayed(const Duration(seconds: 5), () {
      _autoCleanupCompletedTask(taskId);
    });
  }

  /// 自动清理单个已完成的任务
  void _autoCleanupCompletedTask(String taskId) {
    final task = _downloadTasks[taskId];
    if (task != null &&
        (task.status == DownloadStatus.completed ||
            task.status == DownloadStatus.failed ||
            task.status == DownloadStatus.canceled)) {
      // 清理任务
      task.dispose();
      _downloadTasks.remove(taskId);
      _pendingDownloadTasks.remove(taskId);
      _queuedTasks.remove(taskId);

      PGLog.d('自动清理已完成任务: $taskId (状态: ${task.status})');
    }
  }

  /// 检查现有任务状态，处理重复任务逻辑
  /// 返回null表示可以继续添加新任务，返回DownloadResult表示应该直接返回该结果
  Future<DownloadResult?> _checkExistingTask(String taskId) async {
    // 检查是否已存在相同ID的任务
    if (!_downloadTasks.containsKey(taskId)) {
      PGLog.d('检查现有任务不存在: $taskId');
      return null; // 不存在，可以继续
    }

    final existingTask = _downloadTasks[taskId]!;

    // 如果任务已失败或已取消，则移除旧任务重新创建
    if (existingTask.status == DownloadStatus.failed ||
        existingTask.status == DownloadStatus.canceled) {
      PGLog.d('检查现有任务已失败或已取消: $taskId');
      await removeTask(taskId);
      return null; // 已清理，可以继续
    } else if (existingTask.status == DownloadStatus.completed) {
      PGLog.d('检查现有任务已完成: $taskId');
      // 如果任务已完成，直接返回成功结果
      final file = File(existingTask.finalFilePath);
      if (file.existsSync()) {
        PGLog.d('检查现有任务文件已经存在: $taskId');
        return DownloadResult.success(
          url: existingTask.downloadUrl,
          filePath: existingTask.finalFilePath,
          taskId: taskId,
          file: file,
        );
      } else {
        PGLog.d('检查现有任务文件不存在: $taskId');
        return DownloadResult.failure(
          url: existingTask.downloadUrl,
          filePath: existingTask.finalFilePath,
          taskId: taskId,
          errorMessage: '任务已完成但文件不存在',
        );
      }
    } else if (existingTask.status == DownloadStatus.paused) {
      PGLog.d('检查现有任务已暂停: $taskId');
      // 如果任务已暂停，则恢复下载
      final result = await resumeDownload(taskId);
      if (result) {
        PGLog.d('检查现有任务已暂停，恢复下载成功: $taskId');
        return DownloadResult.success(
          url: existingTask.downloadUrl,
          filePath: existingTask.finalFilePath,
          taskId: taskId,
          file: File(existingTask.finalFilePath),
        );
      } else {
        PGLog.d('检查现有任务已暂停，恢复下载失败: $taskId');
        return DownloadResult.failure(
          url: existingTask.downloadUrl,
          filePath: existingTask.finalFilePath,
          taskId: taskId,
          errorMessage: '恢复下载失败',
        );
      }
    } else if (existingTask.status == DownloadStatus.downloading) {
      PGLog.d('检查现有任务正在下载中: $taskId');
      // 如果任务正在下载中，直接返回错误
      return DownloadResult.failure(
        url: existingTask.downloadUrl,
        filePath: existingTask.finalFilePath,
        taskId: taskId,
        errorMessage: '任务已存在且正在下载中',
      );
    }

    return null; // 其他情况，可以继续
  }

  /// 执行下载任务的核心逻辑
  /// [task] 要执行的下载任务
  /// 返回下载结果对象
  Future<DownloadResult> _startSerialDownload(
    DownloadTask task, {
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    // 从任务中获取信息
    final taskId = task.taskId ?? DownloadUtil.generateTaskId(task.downloadUrl);
    final resultCompleter = Completer<DownloadResult>();
    // 将任务添加到管理器
    _downloadTasks[taskId] = task;

    // 包装回调函数
    void onCompleteWrapper(File file) {
      onComplete?.call(file);
      _currentConcurrentDownloads = 0; // 串行模式直接重置为0

      if (!resultCompleter.isCompleted) {
        resultCompleter.complete(DownloadResult.success(
          url: task.downloadUrl,
          filePath: task.finalFilePath,
          taskId: taskId,
          file: file,
        ));
      }
    }

    void onErrorWrapper(String error) {
      onError?.call(error);
      _currentConcurrentDownloads = 0; // 串行模式直接重置为0

      if (!resultCompleter.isCompleted) {
        resultCompleter.complete(DownloadResult.failure(
          url: task.downloadUrl,
          filePath: task.finalFilePath,
          taskId: taskId,
          errorMessage: error,
        ));
      }
    }

    try {
      // 启动下载任务（不等待，让回调处理结果）
      task.start(
        onProgress: onProgress,
        onComplete: onCompleteWrapper,
        onError: onErrorWrapper,
      );

      // 等待下载完成
      return await resultCompleter.future;
    } catch (e) {
      final errorMessage = '启动串行下载任务异常: ${e.toString()}';
      PGLog.e(errorMessage);
      _currentConcurrentDownloads = 0; // 异常时也重置

      if (!resultCompleter.isCompleted) {
        resultCompleter.complete(DownloadResult.failure(
          url: task.downloadUrl,
          filePath: task.finalFilePath,
          taskId: taskId,
          errorMessage: errorMessage,
        ));
      }
      return await resultCompleter.future;
    }
  }

  /// 启动并发下载任务（仅启动，不等待完成）
  void _startConcurrentDownload(
    DownloadTask task,
    String taskId, {
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) {
    final downloadUrl = task.downloadUrl;
    final downloadPath = task.finalFilePath;

    // 将任务添加到管理器
    _downloadTasks[taskId] = task;

    // 包装回调函数
    void onCompleteWrapper(File file) {
      onComplete?.call(file);
      _onTaskCompleted(taskId);
    }

    void onErrorWrapper(String error) {
      onError?.call(error);
      _onTaskCompleted(taskId);
    }

    // 检查是否可以立即开始下载
    if (_currentConcurrentDownloads < _maxConcurrentDownloads) {
      _currentConcurrentDownloads++;
      PGLog.d('并发下载立即启动任务: $taskId，当前并发数: $_currentConcurrentDownloads');

      // 异步启动下载任务，不等待完成
      task
          .start(
        onProgress: onProgress,
        onComplete: onCompleteWrapper,
        onError: onErrorWrapper,
      )
          .catchError((error) {
        final errorMessage = '启动并发下载任务异常: ${error.toString()}';
        PGLog.e(errorMessage);
        _onTaskCompleted(taskId);
        onErrorWrapper(errorMessage);
      });
    } else {
      // 添加到待下载队列
      _pendingDownloadTasks.add(taskId);
      PGLog.d('并发下载任务已加入队列: $taskId，队列长度: ${_pendingDownloadTasks.length}');

      // 保存任务参数到队列中（并发模式不需要resultCompleter）
      _queuedTasks[taskId] = _QueuedDownloadTask(
        taskId: taskId,
        url: downloadUrl,
        filePath: downloadPath,
        resultCompleter: null, // 并发模式不需要resultCompleter
        onProgress: onProgress,
        onComplete: onCompleteWrapper,
        onError: onErrorWrapper,
      );
    }
  }

  /// 添加并开始下载任务
  /// 返回下载结果对象
  Future<DownloadResult> _addSerialDownloadTask(
    DownloadTask task, {
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    final taskId = task.taskId ?? DownloadUtil.generateTaskId(task.downloadUrl);

    // 检查现有任务
    final existingResult = await _checkExistingTask(taskId);
    PGLog.d('检查现有任务: $taskId, 是否存在已有任务: ${existingResult != null}');
    if (existingResult != null) {
      // 处理现有任务的情况
      if (existingResult.success) {
        onComplete?.call(existingResult.file!);
      } else {
        onError?.call(existingResult.errorMessage ?? '未知错误');
      }
      return existingResult;
    }

    // 执行新的下载任务
    return await _startSerialDownload(
      task,
      onProgress: onProgress,
      onComplete: onComplete,
      onError: onError,
    );
  }

  /// 添加并批量下载多个文件（串行方式，逐个等待完成）
  /// 直接使用传入的DownloadTask对象
  /// 返回添加的任务下载结果列表
  Future<List<DownloadResult>> _addSerialMultipleDownloads(
    List<DownloadTask> downloadTasks, {
    Function(
            String taskId, double progress, String speed, String remainingTime)?
        onProgress,
    Function(String taskId, File file)? onComplete,
    Function(String taskId, String error)? onError,
  }) async {
    // 保存原来的最大并发数
    final originalMaxConcurrent = _maxConcurrentDownloads;

    // 设置为串行下载（最大并发数为1）
    setMaxConcurrentDownloads(1);

    try {
      final results = <DownloadResult>[];

      for (final task in downloadTasks) {
        final taskId =
            task.taskId ?? DownloadUtil.generateTaskId(task.downloadUrl);

        // 检查现有任务
        final existingResult = await _checkExistingTask(taskId);
        if (existingResult != null) {
          // 处理现有任务的情况
          if (existingResult.success) {
            onComplete?.call(taskId, existingResult.file!);
          } else {
            onError?.call(taskId, existingResult.errorMessage ?? '未知错误');
          }
          results.add(existingResult);
          continue;
        }

        // 执行新的下载任务（串行，需要等待完成）
        final result = await _startSerialDownload(
          task,
          onProgress: onProgress != null
              ? (progress, speed, remainingTime) =>
                  onProgress(taskId, progress, speed, remainingTime)
              : null,
          onComplete:
              onComplete != null ? (file) => onComplete(taskId, file) : null,
          onError: onError != null ? (error) => onError(taskId, error) : null,
        );

        results.add(result);
      }

      return results;
    } finally {
      // 恢复原来的最大并发数
      setMaxConcurrentDownloads(originalMaxConcurrent);
    }
  }

  /// 添加并批量下载多个文件（并发方式，同时启动多个下载）
  /// 直接使用传入的DownloadTask对象
  /// 返回添加的任务ID列表，可以通过回调函数获取下载进度和结果
  Future<List<String>> _addConcurrentMultipleDownloads(
    List<DownloadTask> downloadTasks, {
    Function(
            String taskId, double progress, String speed, String remainingTime)?
        onProgress,
    Function(String taskId, File file)? onComplete,
    Function(String taskId, String error)? onError,
  }) async {
    final taskIds = <String>[];
    PGLog.d('开始添加并批量下载多个文件（并发方式，同时启动多个下载）: ${downloadTasks.length}');
    // 同时启动所有下载任务
    for (final task in downloadTasks) {
      final taskId =
          task.taskId ?? DownloadUtil.generateTaskId(task.downloadUrl);
      taskIds.add(taskId);

      // 检查现有任务
      final existingResult = await _checkExistingTask(taskId);
      PGLog.d('检查现有任务: $taskId, 是否存在已有任务: ${existingResult != null}');
      if (existingResult != null) {
        // 处理现有任务的情况
        if (existingResult.success) {
          onComplete?.call(taskId, existingResult.file!);
        } else {
          onError?.call(taskId, existingResult.errorMessage ?? '未知错误');
        }
        continue; // 跳过已存在的任务
      }
      PGLog.d('检查发现没有已有任务，开始启动并发下载任务: $taskId');
      // 异步启动并发下载任务（不等待完成）
      _startConcurrentDownload(
        task,
        taskId,
        onProgress: onProgress != null
            ? (progress, speed, remainingTime) =>
                onProgress(taskId, progress, speed, remainingTime)
            : null,
        onComplete:
            onComplete != null ? (file) => onComplete(taskId, file) : null,
        onError: onError != null ? (error) => onError(taskId, error) : null,
      );
    }

    // 立即返回任务ID列表，调用者可以通过回调函数获取下载进度和结果
    return taskIds;
  }

  /// 停止所有下载操作
  void stop() {
    // 取消所有下载任务
    _cancelAllDownloads();
    // 清空待下载队列
    _pendingDownloadTasks.clear();
    _queuedTasks.clear(); // 清理队列参数
    _currentConcurrentDownloads = 0;
  }

  /// 释放资源
  void dispose() {
    // 取消所有下载任务
    for (final task in _downloadTasks.values) {
      task.dispose();
    }
    _downloadTasks.clear();
    _pendingDownloadTasks.clear();
    _queuedTasks.clear(); // 清理队列参数
    _currentConcurrentDownloads = 0;
  }

  /// 移除任务
  Future<bool> _removeTask(String taskId) async {
    final task = _downloadTasks[taskId];

    // 从待下载队列中移除（如果存在）
    _pendingDownloadTasks.remove(taskId);

    // 清理队列中保存的参数
    _queuedTasks.remove(taskId);

    if (task != null) {
      // 如果任务正在下载，先取消下载
      if (task.status == DownloadStatus.downloading ||
          task.status == DownloadStatus.paused) {
        await task.cancelDownload();
        _onTaskCompleted(taskId);
      }

      // 释放资源
      task.dispose();

      // 从列表中移除
      _downloadTasks.remove(taskId);

      return true;
    }
    return false;
  }

  /// 取消所有下载任务
  Future<void> _cancelAllDownloads() async {
    final activeTaskIds = <String>[];
    for (final entry in _downloadTasks.entries) {
      if (entry.value.status == DownloadStatus.downloading ||
          entry.value.status == DownloadStatus.paused) {
        activeTaskIds.add(entry.key);
      }
    }
    for (final taskId in activeTaskIds) {
      await cancelDownload(taskId);
    }
  }
}
