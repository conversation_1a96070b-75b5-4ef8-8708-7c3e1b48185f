import 'package:flutter/cupertino.dart';

class ScreenUtil{
  ScreenUtil._internal();
  static final ScreenUtil _instance = ScreenUtil._internal();
  factory ScreenUtil() {
    return _instance;
  }

  late MediaQueryData _mediaQueryData;
  late SizedBox _designSize;


  ///初始化屏幕工具
  ///[context] 上下文
  ///[designSize] 设计稿尺寸
  init(BuildContext context, {SizedBox designSize = const SizedBox(width: 1194, height: 834)}) {
    _mediaQueryData = MediaQuery.of(context);
    _designSize = designSize;
  }

  double setWidth(double width) {
    return width * _mediaQueryData.size.width / (_designSize.width ?? _mediaQueryData.size.width);
  }

  double setHeight(double height) {
    return height * _mediaQueryData.size.height / (_designSize.height ?? _mediaQueryData.size.height);
  }

  double get screenWidth => _mediaQueryData.size.width;

  double get screenHeight => _mediaQueryData.size.height;
}