import 'dart:io';

import 'package:turing_art/utils/pg_log.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

class UrlLauncherUtil {
  /// 使用系统默认浏览器打开URL
  static Future<bool> openInSystemBrowser(String urlString) async {
    final Uri url = Uri.parse(urlString);
    try {
      if (Platform.isWindows) {
        final result = await Process.run('cmd.exe', ['/C', 'start', urlString]);
        return result.exitCode == 0;
      } else if (await url_launcher.canLaunchUrl(url)) {
        return await url_launcher.launchUrl(url);
      } else {
        PGLog.e('Could not launch $url');
        return false;
      }
    } catch (e) {
      PGLog.e('Error launching URL: $e');
      return false;
    }
  }

  /// 检查当前平台是否应该使用系统浏览器
  /// 目前只有win7使用系统浏览器
  static bool shouldUseSystemBrowser() {
    // 如果是windows，并且是win7
    if (Platform.isWindows) {
      // 获取Windows版本信息
      final String osVersion = Platform.operatingSystemVersion.toLowerCase();

      // 检查是否是Windows 7
      // Windows 7版本信息通常包含"6.1"字符串
      if (osVersion.contains('6.1')) {
        PGLog.d('检测到Windows 7系统，使用系统浏览器');
        return true;
      }

      // 其他Windows版本使用内置WebView
      return false;
    }
    return false;
  }
}
