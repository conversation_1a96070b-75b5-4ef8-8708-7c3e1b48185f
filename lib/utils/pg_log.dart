import 'dart:io';

import 'package:logger/logger.dart';
import 'package:turing_art/constants/constants.dart';

import 'file_manager.dart';

/// 日志工具类
class PGLog {
  static bool _initialized = false;

  // 文件日志记录器（用于Info、Warning、Error级别）
  static Logger? _fileLogger;

  /// 初始化日志配置
  static Future<void> initialize() async {
    if (!isDebug) {
      //正式环境就暂时不添加日志了
      return;
    }

    if (_initialized) {
      return;
    }

    // 初始化文件日志记录器
    try {
      final logPath = await FileManager().getFlutterLogFilePath();
      _fileLogger = Logger(
        output: FileOutput(file: File(logPath)),
        printer: PrettyPrinter(),
        filter: ProductionFilter(),
      );
      _initialized = true;
      PGLog.d("日志系统初始化成功");
      PGLog.i("这是信息日志");
      PGLog.w("这是警告日志");
      PGLog.e("这是错误日志");
    } catch (e) {
      PGLog.e("日志系统初始化失败: $e");
      _initialized = true;
    }
  }

  /// 输出Debug级别日志
  static void d(String message) {
    if (_initialized) {
      _fileLogger?.d(message);
    }
  }

  /// 输出Info级别日志
  static void i(String message) {
    if (_initialized) {
      _fileLogger?.i(message);
    }
  }

  /// 输出Warning级别日志
  static void w(String message) {
    if (_initialized) {
      _fileLogger?.w(message);
    }
  }

  /// 输出Error级别日志
  static void e(String message) {
    if (_initialized) {
      _fileLogger?.e(message);
    }
  }
}
