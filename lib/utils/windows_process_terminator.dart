import 'dart:ffi';
import 'dart:io';

import 'package:turing_art/utils/pg_log.dart';

// 定义Windows API函数
typedef TerminateProcessC = Int32 Function(IntPtr hProcess, Uint32 uExitCode);
typedef TerminateProcessDart = int Function(int hProcess, int uExitCode);

typedef OpenProcessC = IntPtr Function(
    Uint32 dwDesiredAccess, Int32 bInheritHandle, Uint32 dwProcessId);
typedef OpenProcessDart = int Function(
    int dwDesiredAccess, int bInheritHandle, int dwProcessId);

typedef GetCurrentProcessIdC = Uint32 Function();
typedef GetCurrentProcessIdDart = int Function();

typedef CloseHandleC = Int32 Function(IntPtr hObject);
typedef CloseHandleDart = int Function(int hObject);

// 定义Windows API常量
const int PROCESS_TERMINATE = 0x0001;

/// Windows进程终止工具类
class WindowsProcessTerminator {
  static final DynamicLibrary _kernel32 = DynamicLibrary.open('kernel32.dll');

  static final _terminateProcess =
      _kernel32.lookupFunction<TerminateProcessC, TerminateProcessDart>(
          'TerminateProcess');
  static final _openProcess =
      _kernel32.lookupFunction<OpenProcessC, OpenProcessDart>('OpenProcess');
  static final _getCurrentProcessId =
      _kernel32.lookupFunction<GetCurrentProcessIdC, GetCurrentProcessIdDart>(
          'GetCurrentProcessId');
  static final _closeHandle =
      _kernel32.lookupFunction<CloseHandleC, CloseHandleDart>('CloseHandle');

  /// 终止当前进程
  static bool terminateCurrentProcess() {
    if (!Platform.isWindows) {
      PGLog.d('仅支持Windows平台');
      return false;
    }

    try {
      // 获取当前进程ID
      final int processId = _getCurrentProcessId();

      // 打开进程
      final int processHandle = _openProcess(PROCESS_TERMINATE, 0, processId);
      if (processHandle == 0) {
        PGLog.d('无法打开进程');
        return false;
      }

      // 终止进程
      final int result = _terminateProcess(processHandle, 0);

      // 关闭句柄
      _closeHandle(processHandle);

      return result != 0;
    } catch (e) {
      PGLog.d('终止进程时出错: $e');
      return false;
    }
  }
}
