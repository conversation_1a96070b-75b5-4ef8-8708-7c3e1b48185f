/// 通用的异步操作状态枚举
enum AsyncStatus {
  idle, // 空闲状态
  loading, // 加载中
  success, // 成功
  error, // 失败
}

/// 通用的异步操作结果类
class AsyncResult<T> {
  final AsyncStatus status;
  final T? data;
  final String? errorMessage;
  final Exception? exception;

  const AsyncResult({
    required this.status,
    this.data,
    this.errorMessage,
    this.exception,
  });

  /// 创建空闲状态
  const AsyncResult.idle() : this(status: AsyncStatus.idle);

  /// 创建加载状态
  const AsyncResult.loading() : this(status: AsyncStatus.loading);

  /// 创建成功状态
  AsyncResult.success(T data) : this(status: AsyncStatus.success, data: data);

  /// 创建错误状态
  AsyncResult.error(String message, [Exception? exception])
      : this(
          status: AsyncStatus.error,
          errorMessage: message,
          exception: exception,
        );

  /// 便利getter方法
  bool get isIdle => status == AsyncStatus.idle;

  bool get isLoading => status == AsyncStatus.loading;

  bool get isSuccess => status == AsyncStatus.success;

  bool get isError => status == AsyncStatus.error;

  /// 类型安全的数据获取
  T? get dataOrNull => isSuccess ? data : null;

  /// 获取错误信息
  String? get errorOrNull => isError ? errorMessage : null;

  /// 模式匹配方法
  R when<R>({
    required R Function() idle,
    required R Function() loading,
    required R Function(T data) success,
    required R Function(String message, Exception? exception) error,
  }) {
    switch (status) {
      case AsyncStatus.idle:
        return idle();
      case AsyncStatus.loading:
        return loading();
      case AsyncStatus.success:
        return success(data as T);
      case AsyncStatus.error:
        return error(errorMessage!, exception);
    }
  }

  /// 可选的模式匹配方法
  R maybeWhen<R>({
    R Function()? idle,
    R Function()? loading,
    R Function(T data)? success,
    R Function(String message, Exception? exception)? error,
    required R Function() orElse,
  }) {
    switch (status) {
      case AsyncStatus.idle:
        return idle?.call() ?? orElse();
      case AsyncStatus.loading:
        return loading?.call() ?? orElse();
      case AsyncStatus.success:
        return success?.call(data as T) ?? orElse();
      case AsyncStatus.error:
        return error?.call(errorMessage!, exception) ?? orElse();
    }
  }

  /// 转换数据类型
  AsyncResult<R> map<R>(R Function(T) mapper) {
    if (isSuccess && data != null) {
      return AsyncResult.success(mapper(data as T));
    }
    return AsyncResult(
      status: status,
      errorMessage: errorMessage,
      exception: exception,
    );
  }

  /// 复制并更新状态
  AsyncResult<T> copyWith({
    AsyncStatus? status,
    T? data,
    String? errorMessage,
    Exception? exception,
  }) {
    return AsyncResult(
      status: status ?? this.status,
      data: data ?? this.data,
      errorMessage: errorMessage ?? this.errorMessage,
      exception: exception ?? this.exception,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is AsyncResult<T> &&
        other.status == status &&
        other.data == data &&
        other.errorMessage == errorMessage &&
        other.exception == exception;
  }

  @override
  int get hashCode {
    return Object.hash(status, data, errorMessage, exception);
  }

  @override
  String toString() {
    return 'AsyncResult(status: $status, data: $data, errorMessage: $errorMessage, exception: $exception)';
  }
}
