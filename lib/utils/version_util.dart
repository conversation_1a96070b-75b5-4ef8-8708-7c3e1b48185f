/// 版本工具类，用于版本比较和版本相关操作
class VersionUtil {
  /// 比较两个版本号，如果当前版本小于指定版本，返回true
  ///
  /// [currentVersion] 当前版本号，格式为 "x.y.z"
  /// [targetVersion] 目标版本号，格式为 "x.y.z"
  ///
  /// 返回当前版本是否小于目标版本
  static bool isVersionLessThan(String currentVersion, String targetVersion) {
    List<int> current = _parseVersion(currentVersion);
    List<int> target = _parseVersion(targetVersion);

    // 比较主版本号
    if (current[0] < target[0]) {
      return true;
    }
    if (current[0] > target[0]) {
      return false;
    }

    // 主版本号相同，比较次版本号
    if (current[1] < target[1]) {
      return true;
    }
    if (current[1] > target[1]) {
      return false;
    }

    // 主版本和次版本都相同，比较修订版本号
    return current[2] < target[2];
  }

  /// 解析版本号为整数列表
  ///
  /// [version] 版本号字符串，格式为 "x.y.z"
  ///
  /// 返回包含三个整数的列表 [major, minor, patch]
  static List<int> _parseVersion(String version) {
    List<String> parts = version.split('.');
    List<int> result = [0, 0, 0]; // 默认 [0, 0, 0]

    for (int i = 0; i < parts.length && i < 3; i++) {
      result[i] = int.tryParse(parts[i]) ?? 0;
    }

    return result;
  }
}
