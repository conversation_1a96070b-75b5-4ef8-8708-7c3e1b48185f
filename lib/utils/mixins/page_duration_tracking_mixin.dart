import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/model.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/routing/router.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 基于路由监听的页面停留时长统计Mixin
mixin RouteAwarePageTrackingMixin<T extends StatefulWidget>
    on State<T>, RouteAware {
  int _pageStartTime = 0;
  bool _isTracking = false;
  bool _isSubscribed = false; // 防止重复订阅

  String? _userId;

  /// 页面名称，子类必须实现
  String get pageName;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 防止重复订阅
    if (!_isSubscribed) {
      final route = ModalRoute.of(context);
      if (route != null) {
        routeObserver.subscribe(this, route);
        _isSubscribed = true;
      }
    }

    _userId ??= context.read<CurrentUserRepository>().user?.effectiveId;
  }

  @override
  void dispose() {
    // 取消 RouteAware 监听
    if (_isSubscribed) {
      routeObserver.unsubscribe(this);
      _isSubscribed = false;
    }
    _stopPageTracking();
    super.dispose();
  }

  // RouteAware 接口实现
  @override
  void didPopNext() {
    // 从其他页面返回到当前页面
    _startPageTracking();
  }

  @override
  void didPush() {
    // 当前页面被推入路由栈
    _startPageTracking();
  }

  @override
  void didPop() {
    // 当前页面被弹出路由栈
    _stopPageTracking();
  }

  @override
  void didPushNext() {
    // 从当前页面跳转到其他页面
    _stopPageTracking();
  }

  /// 开始页面停留时长统计
  void _startPageTracking() {
    if (_isTracking) {
      return;
    }

    _pageStartTime = DateTimeUtil.getCurrentTimestampSec();
    _isTracking = true;
  }

  /// 停止页面停留时长统计
  void _stopPageTracking() {
    if (!_isTracking) {
      return;
    }

    _doStat();
    _isTracking = false;
  }

  /// 统计数据处理
  void _doStat() {
    final startTime = _pageStartTime.toString();
    final duration = DateTimeUtil.getCurrentTimestampSec() - _pageStartTime;
    final userId = _userId ?? '';

    switch (pageName) {
      case AnalyticPageNames.home:
        recordPageHome(
            userId: userId, stTime: startTime, duration: duration.toString());
        break;
      default:
        PGLog.d('页面 $pageName 暂未配置统计接口');
        break;
    }
  }
}

/// 简化版的页面停留时长统计Mixin
mixin SimplePageDurationTrackingMixin<T extends StatefulWidget> on State<T> {
  int _pageStartTime = 0;
  bool _isTracking = false;

  String? _userId;

  /// 页面名称，子类必须实现
  String get pageName;

  @override
  void initState() {
    startPageTracking();
    super.initState();
  }

  @override
  void dispose() {
    stopPageTracking();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _userId ??= context.read<CurrentUserRepository>().user?.effectiveId;
  }

  /// 开始统计
  void startPageTracking() {
    if (_isTracking) {
      return;
    }

    _pageStartTime = DateTimeUtil.getCurrentTimestampSec();
    _isTracking = true;
  }

  /// 停止统计
  void stopPageTracking() {
    if (!_isTracking) {
      return;
    }
    _doStat();
    _isTracking = false;
  }

  void _doStat() {
    final startTime = _pageStartTime.toString();
    final duration = getPageDuration();
    final userId = _userId ?? '';
    switch (pageName) {
      case AnalyticPageNames.login:
        recordPageLogin(
            userId: userId, stTime: startTime, duration: duration.toString());
        break;
      case AnalyticPageNames.edit:
        recordPageEdit(
            userId: userId, stTime: startTime, duration: duration.toString());
        break;
      case AnalyticPageNames.home:
        recordPageHome(
            userId: userId, stTime: startTime, duration: duration.toString());
        break;
      default:
        break;
    }
  }

  /// 获取当前停留时长
  int getPageDuration() {
    if (!_isTracking) {
      return 0;
    }
    return (DateTimeUtil.getCurrentTimestampSec() - _pageStartTime);
  }
}

/// 用于数据埋点统计的页面名称常量，与埋点文档中页面名称保持一致

class AnalyticPageNames {
  // 私有构造函数，防止实例化
  AnalyticPageNames._();

  /// 登录页面
  static const String login = 'page_login';

  /// 编辑页面
  static const String edit = 'page_edit';

  /// 首页/项目主页
  static const String home = 'page_home';
}
