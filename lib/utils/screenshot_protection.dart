import 'dart:io';
import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_log.dart';

class ScreenshotProtection {
  static const _channel = MethodChannel('pg_screenshot');

  static Future<void> enable() async {
    try {
      if (Platform.isIOS) {
        await _channel.invokeMethod('interceptionScreenshot', true);
      } else if (Platform.isAndroid) {
        // 设置 Android 系统级别的截屏保护
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.manual,
          overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
        );
        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(statusBarColor: Color(0x00000000)),
        );

        // 修改这里，使用 WindowManager 来处理截屏保护
        const int secureFlag =
            0x00002000; // WindowManager.LayoutParams.FLAG_SECURE
        await SystemChrome.setSystemUIChangeCallback(
          (systemOverlaysAreVisible) async {
            // 返回 Future<void> 而不是 int
            await SystemChannels.platform.invokeMethod<void>(
              'SystemChrome.setSystemUIChangeCallback',
              {'enabled': true, 'flags': secureFlag},
            );
          },
        );
      }
    } catch (e) {
      PGLog.e('Enable screenshot protection failed: $e');
    }
  }

  static Future<void> disable() async {
    try {
      if (Platform.isIOS) {
        await _channel.invokeMethod('interceptionScreenshot', false);
      } else if (Platform.isAndroid) {
        // 恢复 Android 系统默认设置
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.manual,
          overlays: SystemUiOverlay.values,
        );
        await SystemChrome.setSystemUIChangeCallback(null);
      }
    } catch (e) {
      PGLog.e('Disable screenshot protection failed: $e');
    }
  }
}
