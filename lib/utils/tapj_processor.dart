import 'dart:io';

import 'package:turing_art/core/tapj/n8_tapj_file_manager.dart';
import 'package:turing_art/core/tapj/n8_tapj_models.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/utils/pg_log.dart';

/// Tapj文件处理器工具类
/// 提供可复用的tapj文件处理功能，用于文件选择器、拖拽等场景
class TapjProcessor {
  /// 验证tapj文件格式（检查魔数）
  static bool validateTapjFile(String tapjFilePath) {
    try {
      final file = File(tapjFilePath);
      if (!file.existsSync()) {
        PGLog.w('tapj文件不存在: $tapjFilePath');
        return false;
      }

      // 读取文件头部验证魔数
      final bytes = file.readAsBytesSync();
      if (bytes.length < 4) {
        PGLog.w('tapj文件太小，无法验证魔数: $tapjFilePath');
        return false;
      }

      // 验证魔数是否为 "TAPJ"
      final magicBytes = bytes.sublist(0, 4);
      final magicString = String.fromCharCodes(magicBytes);
      if (magicString != N8TapjFileManager.tapjMagicString) {
        PGLog.w('无效的tapj文件格式，魔数不匹配: $tapjFilePath, 实际魔数: $magicString');
        return false;
      }

      PGLog.d('tapj文件魔数验证通过: $tapjFilePath');
      return true;
    } catch (e) {
      PGLog.e('验证tapj文件时出错: $tapjFilePath, 错误: $e');
      return false;
    }
  }

  /// 读取tapj文件内容
  static Future<N8TapjReadResult?> readTapjFile(String tapjFilePath) async {
    try {
      if (!validateTapjFile(tapjFilePath)) {
        return null;
      }

      return await N8TapjFileManager.readN8TapjFile(tapjFilePath);
    } catch (e) {
      PGLog.e('读取tapj文件失败: $tapjFilePath, 错误: $e');
      return null;
    }
  }

  /// 将tapj文件处理为图片文件结果
  /// 适用于文件选择器场景
  static Future<DealImageFilesResult?> processTapjForImagePicker(
      String tapjFilePath) async {
    try {
      // 创建完整的导入项目数据
      final tapjImportResult = await createImportProjectData(
        tapjFilePath,
        newProject: true,
        autoNavigate: true,
      );

      if (tapjImportResult == null) {
        PGLog.w('无法创建tapj导入数据: $tapjFilePath');
        return null;
      }

      final projectName =
          tapjImportResult.importData.projectName?.isNotEmpty == true
              ? tapjImportResult.importData.projectName!
              : _getProjectNameFromTapjPath(tapjFilePath);

      PGLog.d(
          'tapj工程文件包含${tapjImportResult.importData.allFileList.length}个文件，项目名称: $projectName');

      // 返回包含tapj导入数据的结果
      return DealImageFilesResult.fromTapj(
          tapjImportResult.importData.getAllFiles(),
          projectName,
          tapjImportResult);
    } catch (e) {
      PGLog.e('处理tapj文件失败: $tapjFilePath, 错误: $e');
      return null;
    }
  }

  /// 创建项目导入数据
  /// 将tapj文件转换为ImportProjectData，用于完整的项目导入流程
  static Future<TapjImportResult?> createImportProjectData(
    String tapjFilePath, {
    bool newProject = true,
    bool autoNavigate = true,
  }) async {
    try {
      final result = await readTapjFile(tapjFilePath);
      if (result == null) {
        PGLog.w('无法读取tapj文件: $tapjFilePath');
        return null;
      }

      // 将N8ExportFileInfo转换为FileItem
      final fileList = mapTapjFileToFileItem(result.projectData.fileList);

      final importData = ImportProjectData(
        projectId: result.projectData.rawWorkspaceId,
        projectName: result.projectData.workspaceName,
        allFileList: fileList,
        selectedFileList: fileList,
        autoNavigate: autoNavigate,
        newProject: newProject,
      );

      PGLog.d(
          '从tapj文件创建ImportProjectData: 项目=${importData.projectName}, 文件数=${importData.allFileList.length}');

      return TapjImportResult(
        importData: importData,
        historyFiles: result.historyFiles,
      );
    } catch (e) {
      PGLog.e('创建ImportProjectData失败: $tapjFilePath, 错误: $e');
      return null;
    }
  }

  /// 将tapj文件信息转换为FileItem列表
  ///
  /// [tapjFileList] tapj文件中的文件列表
  /// 返回转换后的FileItem列表
  static List<FileItem> mapTapjFileToFileItem(
    List<N8ExportFileInfo> tapjFileList,
  ) {
    // 如果没有描述文件，直接转换tapj文件信息
    return tapjFileList.map((fileInfo) {
      return FileItem(
        originalPath: fileInfo.originalPath,
        historyId: fileInfo.historyId != 'org' ? fileInfo.historyId : null,
        isSelected: fileInfo.isSelected,
      );
    }).toList();
  }

  /// 从tapj文件路径提取项目名称
  static String _getProjectNameFromTapjPath(String tapjFilePath) {
    final fileName = tapjFilePath.split(RegExp(r'[/\\]')).last;
    return fileName.replaceAll(RegExp(r'\.[^.]*$'), ''); // 移除扩展名
  }

  /// 检查文件是否为tapj格式
  static bool isTapjFile(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    return N8TapjFileManager.supportedTapjExtensions.contains(extension);
  }

  /// 获取tapj文件的项目信息（不读取完整内容）
  static Future<String?> getTapjProjectName(String tapjFilePath) async {
    try {
      final result = await readTapjFile(tapjFilePath);
      if (result == null) {
        return null;
      }

      return result.projectData.workspaceName.isNotEmpty
          ? result.projectData.workspaceName
          : _getProjectNameFromTapjPath(tapjFilePath);
    } catch (e) {
      PGLog.e('获取tapj项目名称失败: $tapjFilePath, 错误: $e');
      return _getProjectNameFromTapjPath(tapjFilePath);
    }
  }
}

/// Tapj导入结果类
/// 包含ImportProjectData和历史文件数据
class TapjImportResult {
  final ImportProjectData importData;
  final Map<String, List<int>> historyFiles;

  TapjImportResult({
    required this.importData,
    required this.historyFiles,
  });
}
