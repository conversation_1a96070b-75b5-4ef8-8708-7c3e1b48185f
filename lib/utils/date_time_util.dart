import 'package:turing_art/datalayer/domain/enums/time_display_format.dart';

/// 日期时间工具类，提供各种日期时间相关的实用函数
class DateTimeUtil {
  /// 将时间戳转换为格式化的日期时间字符串
  ///
  /// [timestamp] 可以是:
  /// - ISO 8601格式的字符串 (如 "2023-01-01T12:00:00Z")
  /// - 10位的Unix时间戳（秒）字符串或整数
  /// - 13位的Unix时间戳（毫秒）字符串或整数
  /// - DateTime对象
  ///
  /// [format] 可选参数，指定输出格式:
  /// - DateFormat.standard: yyyy-MM-dd HH:mm:ss (默认)
  /// - DateFormat.chinese: yyyy年MM月dd日
  /// - DateFormat.slashDateTime: yyyy/MM/dd HH:mm
  ///
  /// 返回格式化后的日期时间字符串
  static String formatDateTime(dynamic timestamp,
      {DateFormat format = DateFormat.standard}) {
    try {
      DateTime dateTime;

      if (timestamp is DateTime) {
        // 直接使用DateTime对象
        dateTime = timestamp;
      } else if (timestamp is int) {
        // 处理整数类型的时间戳
        dateTime = _convertTimestampToDateTime(timestamp);
      } else if (timestamp is String) {
        if (RegExp(r'^\d+$').hasMatch(timestamp)) {
          // 处理纯数字字符串类型的时间戳
          int timeValue = int.parse(timestamp);
          dateTime = _convertTimestampToDateTime(timeValue);
        } else {
          // 处理ISO 8601格式的时间戳
          dateTime = DateTime.parse(timestamp);
        }
      } else {
        throw const FormatException('不支持的时间戳格式');
      }

      String year = dateTime.year.toString();
      String month = dateTime.month.toString().padLeft(2, '0');
      String day = dateTime.day.toString().padLeft(2, '0');
      String hour = dateTime.hour.toString().padLeft(2, '0');
      String minute = dateTime.minute.toString().padLeft(2, '0');
      String second = dateTime.second.toString().padLeft(2, '0');

      // 根据指定格式返回日期时间字符串
      switch (format) {
        case DateFormat.chinese:
          return '$year年$month月$day日';
        case DateFormat.slashDateTime:
          return '$year/$month/$day $hour:$minute';
        case DateFormat.standardNoHour:
          return '$year-$month-$day';
        case DateFormat.standard:
          return '$year-$month-$day $hour:$minute:$second';
      }
    } catch (e) {
      // 如果解析失败，返回原始时间戳
      return timestamp.toString();
    }
  }

  /// 将时间戳转换为DateTime对象
  /// 智能判断是10位（秒）还是13位（毫秒）时间戳
  static DateTime _convertTimestampToDateTime(int timestamp) {
    // 如果时间戳小于等于11位数，认为是秒级时间戳
    if (timestamp < 100000000000) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    } else {
      // 否则认为是毫秒级时间戳
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    }
  }

  /// 获取当前日期字符串
  static String getCurrentDateStr(
      {DateFormat format = DateFormat.standardNoHour}) {
    final now = DateTime.now();
    return formatDateTime(now, format: format);
  }

  /// 获取当前时间的毫秒时间戳（基于本地时间）
  static int getCurrentTimestampMs() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  /// 获取当前时间的秒级时间戳（基于本地时间）
  static int getCurrentTimestampSec() {
    return DateTime.now().millisecondsSinceEpoch ~/ 1000;
  }
}
