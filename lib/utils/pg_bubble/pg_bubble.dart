import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/arrow_position.dart';

/// 通用气泡组件
class PGBubble extends StatelessWidget {
  /// 气泡内容
  final Widget child;

  /// 箭头位置
  final ArrowPosition arrowPosition;

  /// 气泡宽度
  final double width;

  /// 气泡高度
  final double height;

  /// 箭头在边上的偏移量
  /// 如果箭头在左侧或右侧，这是Y轴上的偏移
  /// 如果箭头在顶部或底部，这是X轴上的偏移
  final double arrowOffset;

  /// 气泡背景色
  final Color backgroundColor;

  /// 气泡边框色
  final Color borderColor;

  /// 气泡边框宽度
  final double borderWidth;

  /// 气泡圆角半径
  final double borderRadius;

  /// 箭头宽度
  final double arrowWidth;

  /// 箭头高度
  final double arrowHeight;

  const PGBubble({
    super.key,
    required this.child,
    required this.arrowPosition,
    required this.width,
    required this.height,
    this.arrowOffset = 0,
    this.backgroundColor = const Color(0xCC000000),
    this.borderColor = const Color(0x1AFFFFFF),
    this.borderWidth = 1.0,
    this.borderRadius = 12.0,
    this.arrowWidth = 16.0,
    this.arrowHeight = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: _BubblePainter(
          arrowPosition: arrowPosition,
          arrowOffset: arrowOffset,
          backgroundColor: backgroundColor,
          borderColor: borderColor,
          borderWidth: borderWidth,
          borderRadius: borderRadius,
          arrowWidth: arrowWidth,
          arrowHeight: arrowHeight,
        ),
        child: child,
      ),
    );
  }
}

/// 气泡自定义画笔
class _BubblePainter extends CustomPainter {
  final ArrowPosition arrowPosition;
  final double arrowOffset;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final double arrowWidth;
  final double arrowHeight;

  // 气泡边框画笔
  late final Paint _borderPaint;

  // 气泡背景画笔
  late final Paint _backgroundPaint;

  _BubblePainter({
    required this.arrowPosition,
    required this.arrowOffset,
    required this.backgroundColor,
    required this.borderColor,
    required this.borderWidth,
    required this.borderRadius,
    required this.arrowWidth,
    required this.arrowHeight,
  }) {
    _borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    _backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;
  }

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制气泡主体和箭头
    final Path path = _createBubblePath(size);

    // 绘制背景
    canvas.drawPath(path, _backgroundPaint);

    // 绘制边框
    canvas.drawPath(path, _borderPaint);
  }

  /// 创建气泡路径（包括主体和箭头）
  Path _createBubblePath(Size size) {
    // 根据箭头位置创建不同的路径
    switch (arrowPosition) {
      case ArrowPosition.top:
        return _createTopArrowPath(size);
      case ArrowPosition.bottom:
        return _createBottomArrowPath(size);
      case ArrowPosition.left:
        return _createLeftArrowPath(size);
      case ArrowPosition.right:
        return _createRightArrowPath(size);
    }
  }

  /// 创建箭头在顶部的气泡路径
  Path _createTopArrowPath(Size size) {
    final Path path = Path();
    final double arrowX = _clampArrowOffset(arrowOffset, size.width);

    // 从箭头开始绘制
    path.moveTo(arrowX, 0);
    path.lineTo(arrowX + arrowWidth / 2, arrowHeight);

    // 右上角
    path.lineTo(size.width - borderRadius, arrowHeight);
    path.quadraticBezierTo(
        size.width, arrowHeight, size.width, arrowHeight + borderRadius);

    // 右边
    path.lineTo(size.width, size.height - borderRadius);
    path.quadraticBezierTo(
        size.width, size.height, size.width - borderRadius, size.height);

    // 底边
    path.lineTo(borderRadius, size.height);
    path.quadraticBezierTo(0, size.height, 0, size.height - borderRadius);

    // 左边
    path.lineTo(0, arrowHeight + borderRadius);
    path.quadraticBezierTo(0, arrowHeight, borderRadius, arrowHeight);

    // 回到箭头
    path.lineTo(arrowX - arrowWidth / 2, arrowHeight);
    path.close();

    return path;
  }

  /// 创建箭头在底部的气泡路径
  Path _createBottomArrowPath(Size size) {
    final Path path = Path();
    final double arrowX = _clampArrowOffset(arrowOffset, size.width);

    // 从左上角开始
    path.moveTo(borderRadius, 0);

    // 顶边
    path.lineTo(size.width - borderRadius, 0);
    path.quadraticBezierTo(size.width, 0, size.width, borderRadius);

    // 右边
    path.lineTo(size.width, size.height - arrowHeight - borderRadius);
    path.quadraticBezierTo(size.width, size.height - arrowHeight,
        size.width - borderRadius, size.height - arrowHeight);

    // 到箭头右侧
    path.lineTo(arrowX + arrowWidth / 2, size.height - arrowHeight);
    path.lineTo(arrowX, size.height);
    path.lineTo(arrowX - arrowWidth / 2, size.height - arrowHeight);

    // 左下角
    path.lineTo(borderRadius, size.height - arrowHeight);
    path.quadraticBezierTo(0, size.height - arrowHeight, 0,
        size.height - arrowHeight - borderRadius);

    // 左边
    path.lineTo(0, borderRadius);
    path.quadraticBezierTo(0, 0, borderRadius, 0);

    path.close();

    return path;
  }

  /// 创建箭头在左侧的气泡路径
  Path _createLeftArrowPath(Size size) {
    final Path path = Path();
    final double arrowY = _clampArrowOffset(arrowOffset, size.height);

    // 从箭头开始
    path.moveTo(0, arrowY);
    path.lineTo(arrowHeight, arrowY - arrowWidth / 2);

    // 左上角
    path.lineTo(arrowHeight, borderRadius);
    path.quadraticBezierTo(arrowHeight, 0, arrowHeight + borderRadius, 0);

    // 顶边
    path.lineTo(size.width - borderRadius, 0);
    path.quadraticBezierTo(size.width, 0, size.width, borderRadius);

    // 右边
    path.lineTo(size.width, size.height - borderRadius);
    path.quadraticBezierTo(
        size.width, size.height, size.width - borderRadius, size.height);

    // 底边
    path.lineTo(arrowHeight + borderRadius, size.height);
    path.quadraticBezierTo(
        arrowHeight, size.height, arrowHeight, size.height - borderRadius);

    // 回到箭头
    path.lineTo(arrowHeight, arrowY + arrowWidth / 2);
    path.close();

    return path;
  }

  /// 创建箭头在右侧的气泡路径
  Path _createRightArrowPath(Size size) {
    final Path path = Path();
    final double arrowY = _clampArrowOffset(arrowOffset, size.height);

    // 从左上角开始
    path.moveTo(borderRadius, 0);

    // 顶边
    path.lineTo(size.width - arrowHeight - borderRadius, 0);
    path.quadraticBezierTo(
        size.width - arrowHeight, 0, size.width - arrowHeight, borderRadius);

    // 到箭头上方
    path.lineTo(size.width - arrowHeight, arrowY - arrowWidth / 2);
    path.lineTo(size.width, arrowY);
    path.lineTo(size.width - arrowHeight, arrowY + arrowWidth / 2);

    // 右下角
    path.lineTo(size.width - arrowHeight, size.height - borderRadius);
    path.quadraticBezierTo(size.width - arrowHeight, size.height,
        size.width - arrowHeight - borderRadius, size.height);

    // 底边
    path.lineTo(borderRadius, size.height);
    path.quadraticBezierTo(0, size.height, 0, size.height - borderRadius);

    // 左边
    path.lineTo(0, borderRadius);
    path.quadraticBezierTo(0, 0, borderRadius, 0);

    path.close();

    return path;
  }

  /// 确保箭头偏移量在合理范围内
  double _clampArrowOffset(double offset, double sideLength) {
    // 箭头不能太靠近边缘，至少要留出圆角 + 箭头宽度的一半的距离
    final double minOffset = borderRadius + arrowWidth / 2;
    final double maxOffset = sideLength - minOffset;

    return offset.clamp(minOffset, maxOffset);
  }

  @override
  bool shouldRepaint(_BubblePainter oldDelegate) {
    return oldDelegate.arrowPosition != arrowPosition ||
        oldDelegate.arrowOffset != arrowOffset ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.borderWidth != borderWidth ||
        oldDelegate.borderRadius != borderRadius ||
        oldDelegate.arrowWidth != arrowWidth ||
        oldDelegate.arrowHeight != arrowHeight;
  }
}
