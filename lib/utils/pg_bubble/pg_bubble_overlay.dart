import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/arrow_position.dart';
import 'package:turing_art/utils/pg_bubble/pg_bubble.dart';

/// 通用气泡悬浮层管理类（单个展示，目前不考虑多个）
class PGBubbleOverlay {
  /// 当前显示的悬浮层条目
  static OverlayEntry? _overlayEntry;

  /// 当前显示的气泡标签
  static String? _currentTag;

  /// 检查指定标签的气泡是否可见
  static bool isVisible({String? tag}) {
    return _overlayEntry != null && _currentTag == tag;
  }

  /// 显示通用气泡
  ///
  /// [context] 上下文
  /// [targetRect] 目标区域的矩形范围
  /// [content] 气泡内容Widget
  /// [bubbleWidth] 气泡宽度（包括三角形）
  /// [bubbleHeight] 气泡高度 (包括三角形)
  /// [arrowPosition] 箭头位置 (可选，默认为顶部)
  /// [arrowOffset] 箭头偏移量 (可选)
  /// [backgroundColor] 气泡背景色 (可选)
  /// [borderColor] 气泡边框色 (可选)
  /// [showOverlayBackground] 是否显示半透明背景 (可选)
  /// [highlightTarget] 是否高亮目标区域 (可选)
  /// [dismissOnTap] 点击背景时是否关闭气泡 (可选)
  /// [onDismiss] 气泡关闭时的回调 (可选)
  /// [tag] 气泡的标识标签 (可选)
  static void show({
    required BuildContext context,
    required Rect targetRect,
    required Widget content,
    required double bubbleWidth,
    double? bubbleHeight,
    double? borderRadius,
    ArrowPosition arrowPosition = ArrowPosition.top,
    double arrowOffset = 0,
    Color? backgroundColor = const Color(0xCC000000),
    Color? borderColor = const Color(0x1AFFFFFF),
    bool showOverlayBackground = false,
    bool highlightTarget = false,
    bool dismissOnTap = false,
    VoidCallback? onDismiss,
    String? tag,
  }) {
    // 先移除之前的悬浮层
    dismiss();

    // 保存当前标签
    _currentTag = tag;

    // 使用默认高度如果未指定
    final double actualHeight = bubbleHeight ?? bubbleWidth * 0.6;

    // 计算气泡位置
    final Offset bubblePosition = _calculateBubblePosition(
      targetRect: targetRect,
      arrowPosition: arrowPosition,
      bubbleWidth: bubbleWidth,
      bubbleHeight: actualHeight,
      arrowOffset: arrowOffset,
    );

    // 创建悬浮层
    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 半透明背景层，用于拦截点击事件（可选）
          if (showOverlayBackground)
            Positioned.fill(
              child: GestureDetector(
                onTap: dismissOnTap ? () => dismiss(callback: onDismiss) : null,
                child: Container(
                  color: Colors.black.withOpacity(0.5),
                ),
              ),
            ),

          // 目标区域高亮（可选）
          if (highlightTarget)
            Positioned.fromRect(
              rect: targetRect,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),

          // 气泡
          Positioned(
            left: bubblePosition.dx,
            top: bubblePosition.dy,
            child: Material(
              color: Colors.transparent,
              child: PGBubble(
                arrowPosition: arrowPosition,
                width: bubbleWidth,
                height: actualHeight,
                arrowOffset: arrowOffset,
                backgroundColor: backgroundColor ?? const Color(0xCC000000),
                borderColor: borderColor ?? const Color(0x1AFFFFFF),
                borderRadius: borderRadius ?? 16,
                child: content,
              ),
            ),
          ),
        ],
      ),
    );

    // 显示悬浮层
    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 移除悬浮层
  static void dismiss({VoidCallback? callback, String? tag}) {
    // 如果指定了标签且不匹配当前标签，则不执行操作
    if (tag != null && _currentTag != tag) {
      return;
    }

    _overlayEntry?.remove();
    _overlayEntry = null;
    _currentTag = null;

    if (callback != null) {
      callback();
    }
  }

  /// 计算气泡位置
  static Offset _calculateBubblePosition({
    required Rect targetRect,
    required ArrowPosition arrowPosition,
    required double bubbleWidth,
    required double bubbleHeight,
    required double arrowOffset,
  }) {
    switch (arrowPosition) {
      case ArrowPosition.top:
        // 箭头在顶部，气泡在目标区域下方
        return Offset(
          targetRect.left + (targetRect.width - bubbleWidth) / 2,
          targetRect.bottom + 8, // 留出8px间距
        );

      case ArrowPosition.bottom:
        // 箭头在底部，气泡在目标区域上方
        return Offset(
          targetRect.left + (targetRect.width - bubbleWidth) / 2,
          targetRect.top - bubbleHeight - 8, // 留出8px间距
        );

      case ArrowPosition.left:
        // 箭头在左侧，气泡在目标区域右侧
        return Offset(
          targetRect.right + 8, // 留出8px间距
          targetRect.top + (targetRect.height - bubbleHeight) / 2,
        );

      case ArrowPosition.right:
        // 箭头在右侧，气泡在目标区域左侧
        return Offset(
          targetRect.left - bubbleWidth - 8, // 留出8px间距
          targetRect.top + (targetRect.height - bubbleHeight) / 2,
        );
    }
  }
}
