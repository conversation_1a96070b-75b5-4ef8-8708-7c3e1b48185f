import 'package:dio/dio.dart';
import 'package:turing_art/datalayer/service/api/network_interceptor.dart';

/// 统一的错误处理服务
class ErrorHandler {
  /// 处理异常
  String handleException(Exception exception, [String? fallbackMessage]) {
    // 处理网络层的异常
    if (exception is DioException && exception.error is NetworkException) {
      return (exception.error as NetworkException).message;
    }

    // 处理其他异常类型
    return _handleGenericException(exception, fallbackMessage);
  }

  /// 处理通用异常
  String _handleGenericException(Exception exception, String? fallbackMessage) {
    final exceptionString = exception.toString().toLowerCase();

    // 检查是否是网络相关异常
    if (exceptionString.contains('network') ||
        exceptionString.contains('connection') ||
        exceptionString.contains('socket')) {
      return '网络连接异常，请稍后重试';
    }

    return fallbackMessage ?? '操作失败，请稍后重试';
  }
}
