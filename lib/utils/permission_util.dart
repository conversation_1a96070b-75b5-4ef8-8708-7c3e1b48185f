import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:photo_manager/photo_manager.dart';
// 使用权限桥接文件代替直接导入permission_handler
import 'package:turing_art/utils/permission_bridge.dart';
import 'package:turing_art/utils/pg_log.dart';

enum PermissionType {
  photos, // 相册权限
  file, // 文件权限
}

class PermissionUtil {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// 请求权限
  static Future<bool> requestPermission(PermissionType permissionType) async {
    if (Platform.isWindows) {
      // Windows 平台默认返回true
      return true;
    }

    if (Platform.isIOS || Platform.isAndroid) {
      switch (permissionType) {
        case PermissionType.photos:
          return await _requestPhotosPermission();
        case PermissionType.file:
          return await _requestStoragePermission();
      }
    } else if (Platform.isMacOS) {
      return await _requestMacOSPermission(permissionType);
    }
    return false;
  }

  /// macOS 权限请求
  static Future<bool> _requestMacOSPermission(
      PermissionType permissionType) async {
    try {
      if (permissionType == PermissionType.file) {
        // macOS 使用系统文件选择器，不需要显式权限
        return true;
      }

      // 对于照片权限，使用 photo_manager
      if (permissionType == PermissionType.photos) {
        final permitted = await PhotoManager.requestPermissionExtend();
        return permitted == PermissionState.authorized ||
            permitted == PermissionState.limited;
      }

      return false;
    } catch (e) {
      PGLog.e('macOS权限请求错误: $e');
      return false;
    }
  }

  /// 检查相册权限状态
  static Future<PermissionState> checkPhotosPermissionStatus() async {
    if (Platform.isWindows) {
      // Windows 平台不需要照片权限
      return PermissionState.authorized;
    }

    if (Platform.isIOS || Platform.isAndroid) {
      return await PhotoManager.requestPermissionExtend();
    } else if (Platform.isMacOS) {
      final status = await Permission.photos.status;
      return _convertPermissionStatus(status);
    }
    // 其它平台
    return PermissionState.authorized;
  }

  /// 将 PermissionStatus 转换为 PermissionState
  static PermissionState _convertPermissionStatus(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return PermissionState.authorized;
      case PermissionStatus.denied:
        return PermissionState.denied;
      case PermissionStatus.restricted:
        return PermissionState.restricted;
      case PermissionStatus.limited:
        return PermissionState.limited;
      default:
        return PermissionState.denied;
    }
  }

  /// 检查是否永久拒绝了某个权限
  static Future<bool> isPermanentlyDenied(Permission permission) async {
    if (Platform.isWindows) {
      // Windows 平台不会永久拒绝权限
      return false;
    }

    if (Platform.isIOS || Platform.isAndroid) {
      return await permission.isPermanentlyDenied;
    }
    // macOS 通常不会永久拒绝权限
    return false;
  }

  /// 打开应用设置页面
  static Future<bool> openSettings() async {
    if (Platform.isWindows) {
      // Windows 通过系统设置管理权限
      return false;
    }

    if (Platform.isIOS || Platform.isAndroid || Platform.isMacOS) {
      return await openAppSettings();
    }
    return false;
  }

  /// 检查并请求相册权限
  static Future<bool> _requestPhotosPermission() async {
    if (Platform.isWindows) {
      // Windows 平台不需要照片权限
      return true;
    }

    if (Platform.isIOS || Platform.isAndroid) {
      final PermissionState result =
          await PhotoManager.requestPermissionExtend();
      switch (result) {
        case PermissionState.authorized:
          return true;
        case PermissionState.limited:
          // iOS 14+ 的有限访问模式，返回 true 因为用户仍然可以访问部分照片
          return true;
        case PermissionState.restricted:
        case PermissionState.denied:
          return false;
        case PermissionState.notDetermined:
          // 用户还未做出选择，重新请求权限
          final secondResult = await PhotoManager.requestPermissionExtend();
          return secondResult == PermissionState.authorized ||
              secondResult == PermissionState.limited;
      }
    } else if (Platform.isMacOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    // 其它平台不需要照片权限
    return true;
  }

  /// 检查并请求文件存储权限
  static Future<bool> _requestStoragePermission() async {
    if (Platform.isWindows) {
      // Windows 平台不需要存储权限
      return true;
    }

    if (Platform.isAndroid) {
      try {
        final androidInfo = await _deviceInfo.androidInfo;
        if (androidInfo.version.sdkInt >= 33) {
          // Android 13 及以上版本需要请求特定媒体权限
          Map<Permission, PermissionStatus> statuses = await [
            Permission.photos,
            Permission.videos,
          ].request();
          return statuses.values.every((status) => status.isGranted);
        }
      } catch (e) {
        PGLog.e('获取设备信息失败: $e');
      }
      // 如果获取版本信息失败或者是低版本Android，使用传统存储权限
      final status = await Permission.storage.request();
      return status.isGranted;
    } else if (Platform.isIOS) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    // macOS 通常不需要存储权限
    return true;
  }
}
