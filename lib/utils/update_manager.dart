import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/core/service/disk_space_size/disk_free_space_size_service.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/version/ops_update_version_handler.dart';
import 'package:turing_art/ops/version/ops_update_version_result_model.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 下载状态枚举
enum DownloadStatus {
  /// 未开始
  notStarted,

  /// 下载中
  downloading,

  /// 已暂停
  paused,

  /// 已完成
  completed,

  /// 失败
  failed,

  /// 已取消
  canceled,
}

/// 版本更新管理器
class UpdateManager {
  static final UpdateManager _instance = UpdateManager._internal();
  factory UpdateManager() => _instance;
  UpdateManager._internal();

  late final OpsUpdateVersionHandler handler = OpsUpdateVersionHandler();

  final DiskFreeSpaceSizeService _diskFreeSpaceSizeService =
      DiskFreeSpaceSizeService.forPlatform();

  /// Dio实例，用于下载文件
  final Dio _dio = Dio();

  /// 下载状态
  DownloadStatus _status = DownloadStatus.notStarted;

  // ignore: unnecessary_getters_setters
  DownloadStatus get status => _status;

  // ignore: unnecessary_getters_setters
  set status(DownloadStatus value) {
    _status = value;
    _statusController.add(_status);
  }

  /// 下载进度
  double _progress = 0.0;
  double get progress => _progress;

  /// 下载速度 (bytes/s)
  double _speed = 0.0;
  double get speed => _speed;

  /// 已下载字节数
  int _downloadedBytes = 0;
  int get downloadedBytes => _downloadedBytes;

  /// 总字节数
  int _totalBytes = 0;
  int get totalBytes => _totalBytes;

  /// 剩余时间（秒）
  int _remainingSeconds = 0;
  int get remainingSeconds => _remainingSeconds;

  String get downloadDirPath {
    _initDownloadDirectory();
    return _downloadDir!.path;
  }

  /// 下载开始时间
  DateTime? _startTime;

  /// 上次进度更新时间
  DateTime? _lastProgressTime;

  /// 上次已下载字节数
  int _lastDownloadedBytes = 0;

  /// 取消令牌
  CancelToken? _cancelToken;

  /// 下载目录
  Directory? _downloadDir;

  /// 下载文件路径
  String? _downloadFilePath;

  /// 下载状态流控制器
  final _statusController = StreamController<DownloadStatus>.broadcast();
  Stream<DownloadStatus> get statusStream => _statusController.stream;

  /// 下载进度流控制器
  final _progressController = StreamController<double>.broadcast();
  Stream<double> get progressStream => _progressController.stream;

  /// 下载信息流控制器
  final _downloadInfoController =
      StreamController<Map<String, dynamic>>.broadcast();
  Stream<Map<String, dynamic>> get downloadInfoStream =>
      _downloadInfoController.stream;

  /// 设置下载文件路径（用于恢复下载）
  set downloadFilePath(String? path) {
    _downloadFilePath = path;
  }

  /// 获取当前下载文件路径
  String? get getDownloadFilePath => _downloadFilePath;

  /// 初始化下载目录
  Future<void> _initDownloadDirectory() async {
    try {
      if (_downloadDir != null) {
        return;
      }
      _downloadDir = await FileManager().createUpdateDownloadDir();
      PGLog.d('下载目录初始化成功: ${_downloadDir!.path}');
    } catch (e) {
      PGLog.e('初始化下载目录失败: $e');
      rethrow;
    }
  }

  /// 获取下载目录
  Future<Directory> getDownloadDirectory() async {
    await _initDownloadDirectory();
    return _downloadDir!;
  }

  /// 开始下载
  ///
  /// [url] 下载地址
  /// [filename] 文件名，如果为空则从URL中提取
  /// [onProgress] 进度回调
  /// [onComplete] 完成回调
  /// [onError] 错误回调
  Future<void> startDownload(
    String url, {
    String? filename,
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    if (status == DownloadStatus.downloading) {
      PGLog.w('已有下载任务正在进行中');
      return;
    }

    try {
      await _initDownloadDirectory();

      // 设置最终文件名
      final finalFilename = handler.matchingVersion?.packageName ??
          filename ??
          _getFilenameFromUrl(url);
      final finalFilePath = path.join(_downloadDir!.path, finalFilename);
      // 设置临时文件名（添加.tmp.download后缀）
      final tempFilename = '$finalFilename.tmp.download';
      _downloadFilePath = path.join(_downloadDir!.path, tempFilename);

      // 初始化状态
      _resetDownloadState();
      updateStatus(DownloadStatus.downloading);

      // 创建取消令牌
      _cancelToken = CancelToken();

      // 检查是否存在未完成的下载文件
      final tempFile = File(_downloadFilePath!);
      int startBytes = 0;

      if (tempFile.existsSync()) {
        startBytes = tempFile.lengthSync();
        _downloadedBytes = startBytes;
        _lastDownloadedBytes = startBytes;
      } else {
        await clearDownloadedFile();
        _resetDownloadState();
      }

      // 记录开始时间
      _startTime = DateTime.now();
      _lastProgressTime = _startTime;

      // 设置请求头，支持断点续传
      final headers = <String, dynamic>{};
      if (startBytes > 0) {
        headers['Range'] = 'bytes=$startBytes-';
      }

      // 发送HEAD请求获取文件大小
      if (_totalBytes == 0) {
        try {
          final headResponse = await _dio.head(
            url,
            options: Options(headers: headers),
          );
          _totalBytes = _parseContentLength(headResponse.headers) ?? 0;
        } catch (e) {
          PGLog.w('获取文件大小失败: $e');
        }
      }

      /// 检查磁盘空间是否足够
      bool freeSpaceChecked = await _checkDiskFreeSpaceAvailable(_totalBytes);
      if (!freeSpaceChecked) {
        _resetDownloadState();
        updateStatus(DownloadStatus.notStarted);
        PGLog.e('🚫 >>> 下载更新磁盘空间不足！');
        onError?.call('磁盘空间不足，请清理后重试');
        return;
      }

      // 开始下载
      await _dio.download(
        url,
        _downloadFilePath,
        cancelToken: _cancelToken,
        deleteOnError: false,
        options: Options(
          headers: headers,
          receiveTimeout: const Duration(minutes: 30),
        ),
        onReceiveProgress: (received, total) {
          if (total != -1) {
            _totalBytes = total + startBytes;
          }
          _updateProgress(received, onProgress);
        },
      );

      // 下载完成后，将临时文件重命名为最终文件名
      if (tempFile.existsSync()) {
        final finalFile = tempFile.renameSync(finalFilePath);
        _downloadFilePath = finalFilePath;

        // 更新状态
        updateStatus(DownloadStatus.completed);
        _progress = 1.0;
        _downloadedBytes = finalFile.lengthSync();
        _totalBytes = _downloadedBytes;

        // 发送最终的下载信息，确保UI更新
        final finalDownloadInfo = {
          'progress': 1.0,
          'speed': 0.0,
          'downloadedBytes': _downloadedBytes,
          'totalBytes': _totalBytes,
          'remainingSeconds': 0,
          'formattedSpeed': '0B/s',
          'formattedDownloadedSize': getFormattedDownloadedSize(),
          'formattedTotalSize': getFormattedTotalSize(),
          'formattedRemainingTime': '0秒',
        };
        _downloadInfoController.add(finalDownloadInfo);

        // 通知状态和进度变化
        _statusController.add(status);
        _progressController.add(_progress);

        // 保存下载完成的文件路径
        UserPreferencesService.setDownloadCompletedFile(finalFile.path);

        // 保存当前已下载的版本信息
        if (handler.matchingVersion != null) {
          UserPreferencesService.setDownloadedVersion(
              handler.matchingVersion!.version ?? '');
        }

        // 回调
        onComplete?.call(finalFile);
      }
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.cancel) {
        // 用户取消下载
        updateStatus(DownloadStatus.canceled);
      } else {
        // 下载失败
        updateStatus(DownloadStatus.failed);
        onError?.call('下载失败,稍后重试~');
      }
      PGLog.e('下载失败: $e');
    }
  }

  /// 暂停下载
  Future<void> pauseDownload() async {
    if (status == DownloadStatus.downloading) {
      try {
        // 先更新状态，防止取消操作触发错误处理逻辑
        updateStatus(DownloadStatus.paused);

        // 确保取消令牌有效
        if (_cancelToken != null && !_cancelToken!.isCancelled) {
          // 记录当前已下载的字节数，确保恢复时能正确计算
          if (_downloadFilePath != null) {
            final tempFile = File(_downloadFilePath!);
            if (tempFile.existsSync()) {
              _downloadedBytes = tempFile.lengthSync();
              _lastDownloadedBytes = _downloadedBytes;
              PGLog.d('暂停下载，已下载字节数: $_downloadedBytes');
            }
          }

          // 取消下载
          _cancelToken!.cancel('用户暂停下载');

          // 等待一小段时间确保取消操作完成
          await Future<void>.delayed(const Duration(milliseconds: 50));

          PGLog.d('下载已暂停，已下载: $_downloadedBytes / $_totalBytes');
        }
      } catch (e) {
        PGLog.e('暂停下载时出错: $e');
      }
    }
  }

  /// 检查文件是否完整下载
  bool isFileCompletelyDownloadedSync(File file) {
    try {
      // 检查文件是否存在
      if (!file.existsSync()) {
        return false;
      }

      // 从UserPreferencesService获取已完成的文件路径
      final completedFilePath =
          UserPreferencesService.getDownloadCompletedFile();

      // 如果保存的路径与当前文件路径匹配，则认为下载完成
      if (completedFilePath != null && completedFilePath == file.path) {
        return true;
      }

      // 如果有总大小信息，也可以比较文件大小
      if (_totalBytes > 0) {
        final fileSize = file.lengthSync();
        return fileSize == _totalBytes;
      }

      return false;
    } catch (e) {
      PGLog.e('检查文件完整性失败: $e');
      return false;
    }
  }

  /// 恢复下载
  Future<void> resumeDownload({
    Function(double progress, String speed, String remainingTime)? onProgress,
    Function(File file)? onComplete,
    Function(String error)? onError,
  }) async {
    if (status != DownloadStatus.paused) {
      PGLog.w('当前状态不是已暂停，无法恢复下载');
      return;
    }

    if (_downloadFilePath == null) {
      PGLog.e('下载文件路径为空，无法恢复下载');
      onError?.call('下载文件路径为空，无法恢复下载');
      return;
    }

    try {
      // 获取下载URL
      final url = handler.getDownloadUrl();
      if (url == null) {
        PGLog.e('获取下载URL失败');
        onError?.call('获取下载URL失败');
        return;
      }

      // 检查临时文件是否存在
      final tempFile = File(_downloadFilePath!);
      if (!tempFile.existsSync()) {
        PGLog.e('临时文件不存在，无法恢复下载');
        onError?.call('临时文件不存在，无法恢复下载');
        return;
      }

      // 获取已下载的字节数
      final startBytes = tempFile.lengthSync();
      _downloadedBytes = startBytes;
      _lastDownloadedBytes = startBytes;

      /// 检查磁盘空间是否足够
      bool freeSpaceChecked =
          await _checkDiskFreeSpaceAvailable(_totalBytes - startBytes);
      if (!freeSpaceChecked) {
        PGLog.e('🚫 >>> 恢复下载更新磁盘空间不足！');
        onError?.call('磁盘空间不足，请清理后重试');
        return;
      }

      PGLog.d('恢复下载，从字节位置: $startBytes 开始，总大小: $_totalBytes');

      // 更新状态
      updateStatus(DownloadStatus.downloading);

      // 创建新的取消令牌
      _cancelToken = CancelToken();

      // 记录开始时间
      _startTime = DateTime.now();
      _lastProgressTime = _startTime;

      // 设置请求头，支持断点续传
      final headers = <String, dynamic>{
        'Range': 'bytes=$startBytes-',
      };

      // 获取最终文件名和路径
      // final finalFilename = path.basename(_downloadFilePath!);
      final finalFilePath = _downloadFilePath!.endsWith('.tmp.download')
          ? _downloadFilePath!.substring(0, _downloadFilePath!.length - 13)
          : _downloadFilePath!;

      // 发送初始进度信息，确保UI显示正确的起始进度
      if (_totalBytes > 0) {
        _progress = startBytes / _totalBytes;

        // 发送初始下载信息
        final initialDownloadInfo = {
          'progress': _progress,
          'speed': 0.0,
          'downloadedBytes': _downloadedBytes,
          'totalBytes': _totalBytes,
          'remainingSeconds': 0,
          'formattedSpeed': '0B/s',
          'formattedDownloadedSize': getFormattedDownloadedSize(),
          'formattedTotalSize': getFormattedTotalSize(),
          'formattedRemainingTime': '计算中...',
        };
        _downloadInfoController.add(initialDownloadInfo);
        _progressController.add(_progress);

        // 回调初始进度
        onProgress?.call(
          _progress,
          '0B/s',
          '计算中...',
        );
      }

      // 使用IOSink打开文件进行追加写入
      final IOSink fileSink = tempFile.openWrite(mode: FileMode.append);

      try {
        // 使用Dio发送请求，但手动处理响应流
        final response = await _dio.get(
          url,
          options: Options(
            headers: headers,
            responseType: ResponseType.stream,
            receiveTimeout: const Duration(minutes: 30),
          ),
          cancelToken: _cancelToken,
        );

        // 获取响应流
        final responseStream = response.data.stream as Stream<List<int>>;

        // 计算总大小
        if (response.headers.value('content-length') != null) {
          final contentLength =
              int.parse(response.headers.value('content-length')!);
          _totalBytes = startBytes + contentLength;
          PGLog.d('总大小更新为: $_totalBytes');
        }

        // 处理响应流
        int receivedBytes = 0;
        DateTime lastUIUpdateTime = DateTime.now();

        await for (final chunk in responseStream) {
          // 写入文件
          fileSink.add(chunk);

          // 更新接收的字节数
          receivedBytes += chunk.length;
          _downloadedBytes = startBytes + receivedBytes;

          // 计算进度
          if (_totalBytes > 0) {
            _progress = _downloadedBytes / _totalBytes;
            _progress = _progress.clamp(0.0, 1.0);
          }

          // 限制UI更新频率
          final now = DateTime.now();
          final timeSinceLastUpdate =
              now.difference(lastUIUpdateTime).inMilliseconds;

          if (timeSinceLastUpdate >= 500) {
            // 更新进度信息
            _updateProgressInfo(receivedBytes);

            // 回调
            onProgress?.call(
              _progress,
              getFormattedSpeed(),
              getFormattedRemainingTime(),
            );

            lastUIUpdateTime = now;
          }

          // 检查是否取消
          if (_cancelToken?.isCancelled ?? false) {
            PGLog.d('下载已取消');
            break;
          }
        }

        // 关闭文件
        await fileSink.flush();
        await fileSink.close();

        // 如果下载完成且未取消，重命名文件
        if (status == DownloadStatus.downloading && tempFile.existsSync()) {
          final finalFile = tempFile.renameSync(finalFilePath);
          _downloadFilePath = finalFilePath;

          // 更新状态
          updateStatus(DownloadStatus.completed);
          _progress = 1.0;
          _downloadedBytes = finalFile.lengthSync();
          _totalBytes = _downloadedBytes;

          // 发送最终的下载信息
          final finalDownloadInfo = {
            'progress': 1.0,
            'speed': 0.0,
            'downloadedBytes': _downloadedBytes,
            'totalBytes': _totalBytes,
            'remainingSeconds': 0,
            'formattedSpeed': '0B/s',
            'formattedDownloadedSize': getFormattedDownloadedSize(),
            'formattedTotalSize': getFormattedTotalSize(),
            'formattedRemainingTime': '0秒',
          };
          _downloadInfoController.add(finalDownloadInfo);

          // 通知状态和进度变化
          _statusController.add(status);
          _progressController.add(_progress);

          // 保存下载完成的文件路径
          UserPreferencesService.setDownloadCompletedFile(finalFilePath);

          // 保存当前已下载的版本信息
          if (handler.matchingVersion != null) {
            UserPreferencesService.setDownloadedVersion(
                handler.matchingVersion!.version ?? '');
          }

          // 回调
          onComplete?.call(finalFile);
        }
      } finally {
        // 确保文件被关闭
        await fileSink.close().catchError((e) {
          PGLog.e('关闭文件时出错: $e');
        });
      }
    } catch (e) {
      // 只有在状态不是暂停时才报告错误
      if (status != DownloadStatus.paused) {
        PGLog.e('恢复下载失败: $e');
        updateStatus(DownloadStatus.failed);
        onError?.call('恢复下载失败,稍后重试~');
      }
    }
  }

  /// 取消下载
  Future<void> cancelDownload() async {
    if (status == DownloadStatus.downloading ||
        status == DownloadStatus.paused) {
      _cancelToken?.cancel('用户取消下载');
      updateStatus(DownloadStatus.canceled);

      await clearDownloadedFile();
      _resetDownloadState();
      PGLog.d('下载已取消');
    }
  }

  Future<void> clearDownloadedFile() async {
    // 删除下载的文件
    if (_downloadFilePath != null) {
      await FileManager().deleteDir(path: _downloadFilePath!);
      PGLog.d('已删除下载文件: $_downloadFilePath');
    }
  }

  /// 获取下载文件
  File? getDownloadedFile() {
    if (status == DownloadStatus.completed && _downloadFilePath != null) {
      return File(_downloadFilePath!);
    }
    return null;
  }

  /// 获取格式化的下载速度
  String getFormattedSpeed() {
    if (_speed < 1024) {
      return '${_speed.toStringAsFixed(1)}B/s';
    } else if (_speed < 1024 * 1024) {
      return '${(_speed / 1024).toStringAsFixed(1)}KB/s';
    } else {
      return '${(_speed / (1024 * 1024)).toStringAsFixed(1)}MB/s';
    }
  }

  /// 获取格式化的已下载大小
  String getFormattedDownloadedSize() {
    return _formatFileSize(_downloadedBytes);
  }

  /// 获取格式化的总大小
  String getFormattedTotalSize() {
    return _formatFileSize(_totalBytes);
  }

  /// 获取格式化的剩余时间
  String getFormattedRemainingTime() {
    if (_remainingSeconds < 60) {
      return '$_remainingSeconds秒';
    } else if (_remainingSeconds < 3600) {
      final minutes = (_remainingSeconds / 60).floor();
      final seconds = _remainingSeconds % 60;
      return '$minutes分$seconds秒';
    } else {
      final hours = (_remainingSeconds / 3600).floor();
      final minutes = ((_remainingSeconds % 3600) / 60).floor();
      final seconds = _remainingSeconds % 60;
      return '$hours小时$minutes分$seconds秒';
    }
  }

  /// 清理资源
  void dispose() {
    _cancelToken?.cancel();
    _statusController.close();
    _progressController.close();
    _downloadInfoController.close();
  }

  /// 重置下载状态
  void _resetDownloadState() {
    _progress = 0.0;
    _speed = 0.0;
    _downloadedBytes = 0;
    _totalBytes = 0;
    _remainingSeconds = 0;
    _startTime = null;
    _lastProgressTime = null;
    _lastDownloadedBytes = 0;
    _cancelToken = null;
  }

  /// 更新下载进度
  void _updateProgress(
    int bytesReceived,
    Function(double progress, String speed, String remainingTime)? onProgress,
  ) {
    final now = DateTime.now();

    // 计算当前总下载量
    final currentBytes = _lastDownloadedBytes + bytesReceived;

    // 计算进度
    if (_totalBytes > 0) {
      _progress = currentBytes / _totalBytes;
      _progressController.add(_progress);
    }

    // 每500毫秒更新一次速度和剩余时间
    final timeDiff = now.difference(_lastProgressTime!).inMilliseconds;
    if (timeDiff >= 500) {
      // 计算下载速度 (bytes/s)
      final bytesDiff = currentBytes - _downloadedBytes;
      _speed = bytesDiff / (timeDiff / 1000);

      // 更新已下载字节数
      _downloadedBytes = currentBytes;

      // 计算剩余时间
      if (_speed > 0 && _totalBytes > 0) {
        final remainingBytes = _totalBytes - _downloadedBytes;
        _remainingSeconds = (remainingBytes / _speed).round();
      }

      // 更新最后进度时间
      _lastProgressTime = now;

      // 发送下载信息
      final downloadInfo = {
        'progress': _progress,
        'speed': _speed,
        'downloadedBytes': _downloadedBytes,
        'totalBytes': _totalBytes,
        'remainingSeconds': _remainingSeconds,
        'formattedSpeed': getFormattedSpeed(),
        'formattedDownloadedSize': getFormattedDownloadedSize(),
        'formattedTotalSize': getFormattedTotalSize(),
        'formattedRemainingTime': getFormattedRemainingTime(),
      };
      _downloadInfoController.add(downloadInfo);

      // 回调
      onProgress?.call(
        _progress,
        getFormattedSpeed(),
        getFormattedRemainingTime(),
      );
    }
  }

  /// 从URL中提取文件名
  String _getFilenameFromUrl(String url) {
    final uri = Uri.parse(url);
    final filename = path.basename(uri.path);

    if (filename.isEmpty || !filename.contains('.')) {
      // 如果无法从URL中提取有效文件名，生成一个随机文件名
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final random = Random().nextInt(10000);
      return 'download_${timestamp}_$random.exe';
    }

    return filename;
  }

  /// 解析Content-Length头
  int? _parseContentLength(Headers headers) {
    final contentRange = headers.value('content-range');
    if (contentRange != null) {
      // 格式: bytes start-end/total
      final match = RegExp(r'bytes \d+-\d+/(\d+)').firstMatch(contentRange);
      if (match != null && match.groupCount >= 1) {
        return int.parse(match.group(1)!);
      }
    }

    final contentLength = headers.value('content-length');
    if (contentLength != null) {
      return int.parse(contentLength);
    }

    return null;
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)}GB';
    }
  }

  /// 设置状态为已完成
  Future<void> setStatusCompleted(File file) async {
    updateStatus(DownloadStatus.completed);
    _progress = 1.0;
    _downloadFilePath = file.path;
    _statusController.add(status);
    _progressController.add(_progress);

    // 保存下载完成的文件路径
    UserPreferencesService.setDownloadCompletedFile(file.path);

    // 保存当前已下载的版本信息
    if (handler.matchingVersion != null) {
      UserPreferencesService.setDownloadedVersion(
          handler.matchingVersion!.version ?? '');
    }

    // 获取文件大小
    try {
      _totalBytes = file.lengthSync();
      _downloadedBytes = _totalBytes;

      // 发送下载信息
      final downloadInfo = {
        'progress': _progress,
        'speed': 0.0,
        'downloadedBytes': _downloadedBytes,
        'totalBytes': _totalBytes,
        'remainingSeconds': 0,
        'formattedSpeed': '0B/s',
        'formattedDownloadedSize': getFormattedDownloadedSize(),
        'formattedTotalSize': getFormattedTotalSize(),
        'formattedRemainingTime': '0秒',
      };
      _downloadInfoController.add(downloadInfo);
    } catch (e) {
      PGLog.e('获取文件大小失败: $e');
    }
  }

  /// 更新状态并通知监听器
  void updateStatus(DownloadStatus newStatus) {
    status = newStatus;
    _statusController.add(status);
  }

  /// 更新下载完成状态并通知监听器
  void updateDownloadedCompletedStatusIfNeeded() {
    if (status != DownloadStatus.downloading) {
      _checkDownloadedFileExists();
      _updateDownloadRecordForUserPreferences();
    }
  }

  /// 检查磁盘空间是否足够
  /// @param fileLength 文件长度，单位为字节
  Future<bool> _checkDiskFreeSpaceAvailable(int fileLength) async {
    final freeSpace =
        await _diskFreeSpaceSizeService.getRootDirectoryFreeDiskSpaceSize();
    if (freeSpace == null || freeSpace < 0) {
      return false;
    }
    return freeSpace >= fileLength;
  }

  /// 检查下载记录并更新用户偏好
  void _updateDownloadRecordForUserPreferences() {
    // 检查是否已经下载了这个版本
    final downloadedVersion = UserPreferencesService.getDownloadedVersion();
    final currentVersion = handler.matchingVersion?.version;

    if (downloadedVersion != null && currentVersion != null) {
      // 已经下载了当前版本
      if (downloadedVersion == currentVersion) {
        updateStatus(DownloadStatus.completed);
        _progress = 1.0;
        _statusController.add(status);
        _progressController.add(_progress);
      } else if (status == DownloadStatus.notStarted) {
        _progress = 0.0;
        _statusController.add(status);
        _progressController.add(_progress);
      }
    }
  }

  /// 检查已下载的文件是否存在
  void _checkDownloadedFileExists() {
    // 检查是否有已下载的文件
    final packageName = handler.matchingVersion?.packageName;
    if (packageName != null) {
      // 确保downloadDirPath不为null
      final finalFilePath = path.join(downloadDirPath, packageName);
      final tempFilePath = '$finalFilePath.tmp.download';

      final finalFile = File(finalFilePath);
      final tempFile = File(tempFilePath);

      if (finalFile.existsSync()) {
        // 检查最终文件是否完整
        final completedFilePath =
            UserPreferencesService.getDownloadCompletedFile();

        if (completedFilePath != null && completedFilePath == finalFilePath) {
          // 文件已完整下载
          setStatusCompleted(finalFile);
          PGLog.d('检测到已完成的下载，设置状态为已完成');
        } else {
          // 文件可能不完整，设置为暂停状态
          status = DownloadStatus.paused;
          downloadFilePath = finalFilePath;
          _resetUserPreferencesCompleteStatus();
          PGLog.d('检测到可能未完成的下载，设置状态为已暂停');
        }
      } else if (tempFile.existsSync()) {
        // 存在临时文件，设置为暂停状态
        status = DownloadStatus.paused;
        downloadFilePath = tempFilePath;
        _resetUserPreferencesCompleteStatus();
        PGLog.d('检测到临时下载文件，设置状态为已暂停');
      } else {
        // 文件不存在，重置状态
        status = DownloadStatus.notStarted;
        _resetUserPreferencesCompleteStatus();
        PGLog.d('未检测到下载文件，设置状态为未开始');
      }
    } else {
      // 没有包名信息，无法检查文件
      status = DownloadStatus.notStarted;
      _resetUserPreferencesCompleteStatus();
      PGLog.d('无法获取包名信息，设置状态为未开始');
    }
  }

  /// 检查用户偏好设置记录中是否存在下载文件
  Future<bool> isExistDownloadFileOfUserPreferencesRecords() async {
    // 保存下载完成的文件路径
    final downloadFilePath = UserPreferencesService.getDownloadCompletedFile();
    if (downloadFilePath != null) {
      return await FileManager().fileExists(downloadFilePath);
    }
    return false;
  }

  /// 重置下载状态
  void resetDownloadStatus() {
    status = DownloadStatus.notStarted;
    _resetUserPreferencesCompleteStatus();
  }

  /// 重置用户偏好设置中的下载完成文件
  void _resetUserPreferencesCompleteStatus() {
    UserPreferencesService.setDownloadCompletedFile('');
    UserPreferencesService.setDownloadedVersion('');
  }

  /// 检查是否有新版本
  Future<OpsUpdateVersionResultModel> checkUpdateVersion(
      {required bool isLaunch}) async {
    try {
      bool? isUpdateVersion =
          await handler.fetchUpdateVersionInfo(code: 'updateVersions');

      final isNeedForceUpdate = handler.needsForceUpdate();
      if (isUpdateVersion != null && isUpdateVersion) {
        if (isLaunch) {
          if (isNeedForceUpdate) {
            return OpsUpdateVersionResultModel(
                isUpdateVersion: true, message: '');
          }
          return OpsUpdateVersionResultModel(
              isUpdateVersion: false, message: '');
        } else {
          return OpsUpdateVersionResultModel(
              isUpdateVersion: isUpdateVersion, message: '');
        }
      } else {
        if (isUpdateVersion == null) {
          return OpsUpdateVersionResultModel(
              isUpdateVersion: null, message: '获取版本信息失败');
        } else {
          return OpsUpdateVersionResultModel(
              isUpdateVersion: false, message: isLaunch ? '' : '已经是最新版本了');
        }
      }
    } catch (e) {
      PGLog.e('获取版本信息失败: $e');
      return OpsUpdateVersionResultModel(
          isUpdateVersion: null, message: '获取版本信息失败');
    }
  }

  // 添加一个新方法来更新进度信息，避免代码重复
  void _updateProgressInfo(int receivedBytes) {
    final now = DateTime.now();
    final timeDiff = now.difference(_lastProgressTime!).inMilliseconds / 1000.0;

    if (timeDiff > 0) {
      // 计算下载速度
      final bytesDiff = _downloadedBytes - _lastDownloadedBytes;
      _speed = bytesDiff / timeDiff;

      // 计算剩余时间
      if (_speed > 0 && _totalBytes > 0) {
        final remainingBytes = _totalBytes - _downloadedBytes;
        _remainingSeconds = (remainingBytes / _speed).round();
      }

      // 更新最后进度时间和字节数
      _lastProgressTime = now;
      _lastDownloadedBytes = _downloadedBytes;

      // 发送下载信息
      final downloadInfo = {
        'progress': _progress,
        'speed': _speed,
        'downloadedBytes': _downloadedBytes,
        'totalBytes': _totalBytes,
        'remainingSeconds': _remainingSeconds,
        'formattedSpeed': getFormattedSpeed(),
        'formattedDownloadedSize': getFormattedDownloadedSize(),
        'formattedTotalSize': getFormattedTotalSize(),
        'formattedRemainingTime': getFormattedRemainingTime(),
      };
      _downloadInfoController.add(downloadInfo);
    }
  }
}
