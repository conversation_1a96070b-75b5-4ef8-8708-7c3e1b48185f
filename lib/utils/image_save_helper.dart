import 'dart:io';
import 'dart:typed_data';

import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:turing_art/ffi/models/salient_matters_model.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 图像保存辅助类
class ImageSaveHelper {
  /// 将ImageData保存为PNG文件
  ///
  /// [imageData] 要保存的图像数据
  /// [filePath] 完整的文件保存路径
  ///
  /// 返回保存的文件路径，失败时返回null
  static Future<String?> saveAsPng(ImageData imageData, String filePath) async {
    return _saveImage(imageData, filePath, 'png');
  }

  /// 将单通道ImageData保存为带透明度的红色PNG文件
  ///
  /// [imageData] 要保存的单通道图像数据
  /// [filePath] 完整的文件保存路径
  ///
  /// 规则：蒙版区域红色加0.3透明度；其他区域全透明
  /// 返回保存的文件路径，失败时返回null
  static Future<String?> saveAsPngWithAlpha(
    ImageData imageData,
    String filePath,
  ) async {
    try {
      // 1. 验证输入参数
      if (imageData.data.isEmpty) {
        PGLog.e('图像数据为空');
        return null;
      }

      if (imageData.width <= 0 || imageData.height <= 0) {
        PGLog.e('图像尺寸无效: ${imageData.width}x${imageData.height}');
        return null;
      }

      if (imageData.channels != 1) {
        PGLog.e('saveAsPngWithAlpha只支持单通道图像，当前通道数: ${imageData.channels}');
        return null;
      }

      // 2. 获取保存目录并确保存在
      final directory = Directory(path.dirname(filePath));
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }

      // 3. 创建RGBA图像
      final image = img.Image(
        width: imageData.width,
        height: imageData.height,
        numChannels: 4,
      );

      final data = imageData.data;
      for (int y = 0; y < imageData.height; y++) {
        for (int x = 0; x < imageData.width; x++) {
          final index = y * imageData.width + x;
          if (index < data.length) {
            final gray = data[index];
            // 红色 + 0.3透明度 (alpha = 255 * 0.3 = 76.5 ≈ 77)
            image.setPixel(x, y, img.ColorRgba8(gray, 0, 0, 77));
          }
        }
      }

      // 4. 保存为PNG
      final encodedData = img.encodePng(image);
      final file = File(filePath);
      await file.writeAsBytes(encodedData);

      return filePath;
    } catch (e) {
      PGLog.e('保存带透明度PNG图像失败: $e');
      return null;
    }
  }

  /// 将ImageData保存为JPEG文件
  ///
  /// [imageData] 要保存的图像数据
  /// [filePath] 完整的文件保存路径
  /// [quality] JPEG质量（范围0-100，默认85）
  ///
  /// 返回保存的文件路径，失败时返回null
  static Future<String?> saveAsJpeg(
    ImageData imageData,
    String filePath, {
    int quality = 85,
  }) async {
    return _saveImage(imageData, filePath, 'jpg', quality: quality);
  }

  /// 将ImageData保存为BMP文件
  ///
  /// [imageData] 要保存的图像数据
  /// [filePath] 完整的文件保存路径
  ///
  /// 返回保存的文件路径，失败时返回null
  static Future<String?> saveAsBmp(
    ImageData imageData,
    String filePath,
  ) async {
    return _saveImage(imageData, filePath, 'bmp');
  }

  /// 通用的图像保存方法
  static Future<String?> _saveImage(
    ImageData imageData,
    String filePath,
    String format, {
    int quality = 85,
  }) async {
    try {
      // 1. 验证输入参数
      if (imageData.data.isEmpty) {
        PGLog.e('图像数据为空');
        return null;
      }

      if (imageData.width <= 0 || imageData.height <= 0) {
        PGLog.e('图像尺寸无效: ${imageData.width}x${imageData.height}');
        return null;
      }

      if (imageData.channels < 1 || imageData.channels > 4) {
        PGLog.e('不支持的通道数: ${imageData.channels}');
        return null;
      }

      // 2. 获取保存目录
      final directory = Directory(path.dirname(filePath));
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }

      // 3. 直接使用 ImageData 的数据创建 Image 对象（假设数据已经是标准 RGB/RGBA 格式）
      final image = img.Image.fromBytes(
        width: imageData.width,
        height: imageData.height,
        bytes: imageData.data.buffer,
        numChannels: imageData.channels,
      );

      // 4. 保存图像
      final file = File(filePath);
      Uint8List? encodedData;

      switch (format.toLowerCase()) {
        case 'png':
          encodedData = img.encodePng(image);
          break;
        case 'jpg':
        case 'jpeg':
          encodedData = img.encodeJpg(image, quality: quality);
          break;
        case 'bmp':
          encodedData = img.encodeBmp(image);
          break;
        default:
          PGLog.e('不支持的图像格式: $format');
          return null;
      }

      await file.writeAsBytes(encodedData);

      PGLog.d('图像已保存到: $filePath');
      PGLog.d('文件大小: ${encodedData.length} 字节');

      return filePath;
    } catch (e) {
      PGLog.e('保存图像失败: $e');
      return null;
    }
  }

  /// 转换灰度图像数据
  static img.Image? _convertGrayscaleToImage(ImageData imageData) {
    try {
      final image = img.Image(
        width: imageData.width,
        height: imageData.height,
        numChannels: 1,
      );

      final data = imageData.data;
      for (int y = 0; y < imageData.height; y++) {
        for (int x = 0; x < imageData.width; x++) {
          final index = y * imageData.width + x;
          if (index < data.length) {
            final gray = data[index];
            image.setPixel(x, y, img.ColorRgb8(gray, gray, gray));
          }
        }
      }

      return image;
    } catch (e) {
      PGLog.e('转换灰度图像失败: $e');
      return null;
    }
  }

  /// 转换BGR图像数据到Image对象
  static img.Image? _convertBgrToImage(ImageData imageData) {
    try {
      final image = img.Image(
        width: imageData.width,
        height: imageData.height,
        numChannels: 3,
      );

      final data = imageData.data;
      for (int y = 0; y < imageData.height; y++) {
        for (int x = 0; x < imageData.width; x++) {
          final index = (y * imageData.width + x) * 3;
          if (index + 2 < data.length) {
            // BGR -> RGB
            final b = data[index];
            final g = data[index + 1];
            final r = data[index + 2];
            image.setPixel(x, y, img.ColorRgb8(r, g, b));
          }
        }
      }

      return image;
    } catch (e) {
      PGLog.e('转换BGR图像失败: $e');
      return null;
    }
  }

  /// 转换BGRA图像数据到Image对象
  static img.Image? _convertBgraToImage(ImageData imageData) {
    try {
      final image = img.Image(
        width: imageData.width,
        height: imageData.height,
        numChannels: 4,
      );

      final data = imageData.data;
      for (int y = 0; y < imageData.height; y++) {
        for (int x = 0; x < imageData.width; x++) {
          final index = (y * imageData.width + x) * 4;
          if (index + 3 < data.length) {
            // BGRA -> RGBA
            final b = data[index];
            final g = data[index + 1];
            final r = data[index + 2];
            final a = data[index + 3];
            image.setPixel(x, y, img.ColorRgba8(r, g, b, a));
          }
        }
      }

      return image;
    } catch (e) {
      PGLog.e('转换BGRA图像失败: $e');
      return null;
    }
  }

  /// 获取支持的图像格式列表
  static List<String> getSupportedFormats() {
    return ['png', 'jpg', 'jpeg', 'bmp'];
  }

  /// 验证图像数据完整性
  static bool validateImageData(ImageData imageData) {
    if (imageData.width <= 0 || imageData.height <= 0) {
      PGLog.w('图像尺寸无效: ${imageData.width}x${imageData.height}');
      return false;
    }

    if (imageData.channels < 1 || imageData.channels > 4) {
      PGLog.w('不支持的通道数: ${imageData.channels}');
      return false;
    }

    final expectedSize =
        imageData.width * imageData.height * imageData.channels;
    if (imageData.data.length != expectedSize) {
      PGLog.w('图像数据大小不匹配: 期望$expectedSize，实际${imageData.data.length}');
      return false;
    }

    return true;
  }

  /// 创建缩略图
  ///
  /// [imageData] 原始图像数据
  /// [maxWidth] 最大宽度
  /// [maxHeight] 最大高度
  /// [maintainAspectRatio] 是否保持宽高比
  ///
  /// 返回缩略图的ImageData，失败时返回null
  static ImageData? createThumbnail(
    ImageData imageData,
    int maxWidth,
    int maxHeight, {
    bool maintainAspectRatio = true,
  }) {
    try {
      if (!validateImageData(imageData)) {
        return null;
      }

      // 计算新的尺寸
      int newWidth = maxWidth;
      int newHeight = maxHeight;

      if (maintainAspectRatio) {
        final aspectRatio = imageData.width / imageData.height;
        if (aspectRatio > 1) {
          // 宽图
          newHeight = (maxWidth / aspectRatio).round();
        } else {
          // 高图
          newWidth = (maxHeight * aspectRatio).round();
        }
      }

      // 转换为Image对象
      img.Image? image;
      if (imageData.channels == 1) {
        image = _convertGrayscaleToImage(imageData);
      } else if (imageData.channels == 3) {
        image = _convertBgrToImage(imageData);
      } else if (imageData.channels == 4) {
        image = _convertBgraToImage(imageData);
      }

      if (image == null) return null;

      // 缩放图像
      final resizedImage = img.copyResize(
        image,
        width: newWidth,
        height: newHeight,
        interpolation: img.Interpolation.linear,
      );

      // 转换回ImageData
      return _convertImageToImageData(resizedImage, imageData.channels);
    } catch (e) {
      PGLog.e('创建缩略图失败: $e');
      return null;
    }
  }

  /// 将Image对象转换回ImageData
  static ImageData? _convertImageToImageData(
      img.Image image, int targetChannels) {
    try {
      final width = image.width;
      final height = image.height;
      final dataSize = width * height * targetChannels;
      final data = Uint8List(dataSize);

      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final pixel = image.getPixel(x, y);
          final index = (y * width + x) * targetChannels;

          if (targetChannels == 1) {
            // 转换为灰度
            final gray = ((pixel.r + pixel.g + pixel.b) / 3).round();
            data[index] = gray.clamp(0, 255);
          } else if (targetChannels == 3) {
            // RGB -> BGR
            data[index] = pixel.b.round().clamp(0, 255);
            data[index + 1] = pixel.g.round().clamp(0, 255);
            data[index + 2] = pixel.r.round().clamp(0, 255);
          } else if (targetChannels == 4) {
            // RGBA -> BGRA
            data[index] = pixel.b.round().clamp(0, 255);
            data[index + 1] = pixel.g.round().clamp(0, 255);
            data[index + 2] = pixel.r.round().clamp(0, 255);
            data[index + 3] = pixel.a.round().clamp(0, 255);
          }
        }
      }

      return ImageData(
        data: data,
        width: width,
        height: height,
        channels: targetChannels,
      );
    } catch (e) {
      PGLog.e('转换Image到ImageData失败: $e');
      return null;
    }
  }

  /// 从文件加载图像数据
  ///
  /// [filePath] 图像文件路径
  ///
  /// 返回项目所需格式的ImageData（RGB/RGBA），失败时返回null
  /// 注意：保持标准RGB/RGBA格式，与项目ImageData模型兼容
  static Future<ImageData?> loadImageFromFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        PGLog.e('图像文件不存在: $filePath');
        return null;
      }

      PGLog.d('开始加载图像文件: $filePath');

      // 读取文件字节数据
      final bytes = await file.readAsBytes();
      if (bytes.isEmpty) {
        PGLog.e('图像文件为空: $filePath');
        return null;
      }

      // 使用image包解码图像
      img.Image? image;

      // 根据文件扩展名尝试不同的解码方式
      final extension = filePath.toLowerCase().split('.').last;

      switch (extension) {
        case 'jpg':
        case 'jpeg':
          image = img.decodeJpg(bytes);
          break;
        case 'png':
          image = img.decodePng(bytes);
          break;
        case 'bmp':
          image = img.decodeBmp(bytes);
          break;
        case 'gif':
          image = img.decodeGif(bytes);
          break;
        case 'tiff':
        case 'tif':
          image = img.decodeTiff(bytes);
          break;
        case 'webp':
          image = img.decodeWebP(bytes);
          break;
        default:
          // 尝试自动检测格式
          image = img.decodeImage(bytes);
          break;
      }

      if (image == null) {
        PGLog.e('无法解码图像文件: $filePath');
        return null;
      }

      PGLog.d(
          '图像解码成功: ${image.width}x${image.height}, 通道数: ${image.numChannels}');

      // 直接获取原始像素数据，无需逐像素提取
      final width = image.width;
      final height = image.height;
      final channels = image.numChannels;

      // 使用image包的getBytes方法直接获取字节数据，避免逐像素提取
      // 保持标准RGB/RGBA格式，与项目ImageData模型兼容
      Uint8List data;

      switch (channels) {
        case 1:
          // 灰度图像 - 确保转换为单通道格式
          final convertedImage = image.convert(numChannels: 1);
          data = convertedImage.getBytes();
          break;
        case 3:
          // RGB格式 - 直接获取标准RGB数据
          data = image.getBytes();
          break;
        case 4:
          // RGBA格式 - 直接获取标准RGBA数据
          data = image.getBytes();
          break;
        default:
          // 其他通道数，使用toUint8List作为后备方案
          PGLog.w('不常见的通道数: $channels，使用toUint8List');
          data = image.toUint8List();
          break;
      }

      final imageData = ImageData(
        data: data,
        width: width,
        height: height,
        channels: channels,
      );

      PGLog.d('图像数据加载成功，数据大小: ${imageData.data.length} 字节');

      return imageData;
    } catch (e) {
      PGLog.e('加载图像文件失败: $e');
      return null;
    }
  }

  /// 将Image对象转换为BGR格式的ImageData
  ///
  /// [image] 要转换的Image对象
  ///
  /// 返回BGR格式的ImageData，失败时返回null
  static ImageData? convertImageToBgrImageData(img.Image image) {
    try {
      final width = image.width;
      final height = image.height;
      const channels = 3; // BGR格式，固定3通道
      final dataSize = width * height * channels;
      final data = Uint8List(dataSize);

      // 逐像素转换，从RGBA转换为BGR
      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final pixel = image.getPixel(x, y);
          final index = (y * width + x) * channels;

          // 转换为BGR格式（注意：SalientMatters期望BGR而不是RGB）
          data[index] = pixel.b.round().clamp(0, 255); // B
          data[index + 1] = pixel.g.round().clamp(0, 255); // G
          data[index + 2] = pixel.r.round().clamp(0, 255); // R
        }
      }

      return ImageData(
        data: data,
        width: width,
        height: height,
        channels: channels,
      );
    } catch (e) {
      PGLog.e('转换图像为BGR格式失败: $e');
      return null;
    }
  }

  /// 将Image对象转换为BGRA格式的ImageData
  ///
  /// [image] 要转换的Image对象
  ///
  /// 返回BGRA格式的ImageData，失败时返回null
  static ImageData? convertImageToBgraImageData(img.Image image) {
    try {
      final width = image.width;
      final height = image.height;
      const channels = 4; // BGRA格式，4通道
      final dataSize = width * height * channels;
      final data = Uint8List(dataSize);

      // 逐像素转换，从RGBA转换为BGRA
      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final pixel = image.getPixel(x, y);
          final index = (y * width + x) * channels;

          // 转换为BGRA格式
          data[index] = pixel.b.round().clamp(0, 255); // B
          data[index + 1] = pixel.g.round().clamp(0, 255); // G
          data[index + 2] = pixel.r.round().clamp(0, 255); // R
          data[index + 3] = pixel.a.round().clamp(0, 255); // A
        }
      }

      return ImageData(
        data: data,
        width: width,
        height: height,
        channels: channels,
      );
    } catch (e) {
      PGLog.e('转换图像为BGRA格式失败: $e');
      return null;
    }
  }

  /// 将Image对象转换为灰度格式的ImageData
  ///
  /// [image] 要转换的Image对象
  ///
  /// 返回灰度格式的ImageData，失败时返回null
  static ImageData? convertImageToGrayscaleImageData(img.Image image) {
    try {
      final width = image.width;
      final height = image.height;
      const channels = 1; // 灰度格式，单通道
      final dataSize = width * height * channels;
      final data = Uint8List(dataSize);

      // 逐像素转换为灰度
      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final pixel = image.getPixel(x, y);
          final index = y * width + x;

          // 使用标准灰度转换公式: 0.299*R + 0.587*G + 0.114*B
          final gray = (0.299 * pixel.r + 0.587 * pixel.g + 0.114 * pixel.b)
              .round()
              .clamp(0, 255);
          data[index] = gray;
        }
      }

      return ImageData(
        data: data,
        width: width,
        height: height,
        channels: channels,
      );
    } catch (e) {
      PGLog.e('转换图像为灰度格式失败: $e');
      return null;
    }
  }

  /// 将BGR ImageData转换为Image对象
  ///
  /// [imageData] BGR格式的ImageData
  ///
  /// 返回Image对象，失败时返回null
  static img.Image? convertBgrImageDataToImage(ImageData imageData) {
    try {
      final image = img.Image(
        width: imageData.width,
        height: imageData.height,
        numChannels: 3,
      );

      final data = imageData.data;
      for (int y = 0; y < imageData.height; y++) {
        for (int x = 0; x < imageData.width; x++) {
          final index = (y * imageData.width + x) * 3;
          if (index + 2 < data.length) {
            // BGR -> RGB
            final b = data[index];
            final g = data[index + 1];
            final r = data[index + 2];
            image.setPixel(x, y, img.ColorRgb8(r, g, b));
          }
        }
      }

      return image;
    } catch (e) {
      PGLog.e('转换BGR ImageData为Image失败: $e');
      return null;
    }
  }

  /// 将BGRA ImageData转换为Image对象
  ///
  /// [imageData] BGRA格式的ImageData
  ///
  /// 返回Image对象，失败时返回null
  static img.Image? convertBgraImageDataToImage(ImageData imageData) {
    try {
      final image = img.Image(
        width: imageData.width,
        height: imageData.height,
        numChannels: 4,
      );

      final data = imageData.data;
      for (int y = 0; y < imageData.height; y++) {
        for (int x = 0; x < imageData.width; x++) {
          final index = (y * imageData.width + x) * 4;
          if (index + 3 < data.length) {
            // BGRA -> RGBA
            final b = data[index];
            final g = data[index + 1];
            final r = data[index + 2];
            final a = data[index + 3];
            image.setPixel(x, y, img.ColorRgba8(r, g, b, a));
          }
        }
      }

      return image;
    } catch (e) {
      PGLog.e('转换BGRA ImageData为Image失败: $e');
      return null;
    }
  }

  /// 将灰度ImageData转换为Image对象
  ///
  /// [imageData] 灰度格式的ImageData
  ///
  /// 返回Image对象，失败时返回null
  static img.Image? convertGrayscaleImageDataToImage(ImageData imageData) {
    try {
      final image = img.Image(
        width: imageData.width,
        height: imageData.height,
        numChannels: 1,
      );

      final data = imageData.data;
      for (int y = 0; y < imageData.height; y++) {
        for (int x = 0; x < imageData.width; x++) {
          final index = y * imageData.width + x;
          if (index < data.length) {
            final gray = data[index];
            image.setPixel(x, y, img.ColorRgb8(gray, gray, gray));
          }
        }
      }

      return image;
    } catch (e) {
      PGLog.e('转换灰度ImageData为Image失败: $e');
      return null;
    }
  }

  /// 调整图像尺寸
  ///
  /// [imageData] 原始图像数据
  /// [newWidth] 新的宽度
  /// [newHeight] 新的高度
  /// [interpolation] 插值算法
  ///
  /// 返回调整后的ImageData，失败时返回null
  static ImageData? resizeImageData(
    ImageData imageData,
    int newWidth,
    int newHeight, {
    img.Interpolation interpolation = img.Interpolation.linear,
  }) {
    try {
      // 先转换为Image对象
      img.Image? image;

      if (imageData.channels == 1) {
        image = convertGrayscaleImageDataToImage(imageData);
      } else if (imageData.channels == 3) {
        image = convertBgrImageDataToImage(imageData);
      } else if (imageData.channels == 4) {
        image = convertBgraImageDataToImage(imageData);
      }

      if (image == null) {
        return null;
      }

      // 调整尺寸
      final resizedImage = img.copyResize(
        image,
        width: newWidth,
        height: newHeight,
        interpolation: interpolation,
      );

      // 转换回对应格式的ImageData
      if (imageData.channels == 1) {
        return convertImageToGrayscaleImageData(resizedImage);
      } else if (imageData.channels == 3) {
        return convertImageToBgrImageData(resizedImage);
      } else if (imageData.channels == 4) {
        return convertImageToBgraImageData(resizedImage);
      }

      return null;
    } catch (e) {
      PGLog.e('调整图像尺寸失败: $e');
      return null;
    }
  }

  /// 支持的图像格式检查
  ///
  /// [filePath] 文件路径
  ///
  /// 返回是否支持该格式
  static bool isSupportedImageFormat(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    const supportedFormats = [
      'jpg',
      'jpeg',
      'png',
      'bmp',
      'gif',
      'tiff',
      'tif',
      'webp'
    ];
    return supportedFormats.contains(extension);
  }

  /// 获取图像文件信息（不加载完整图像数据）
  ///
  /// [filePath] 图像文件路径
  ///
  /// 返回包含图像信息的Map，失败时返回null
  static Future<Map<String, dynamic>?> getImageInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        return null;
      }

      final bytes = await file.readAsBytes();
      if (bytes.isEmpty) {
        return null;
      }

      // 尝试解码获取基本信息
      img.Image? image = img.decodeImage(bytes);
      if (image == null) {
        return null;
      }

      return {
        'width': image.width,
        'height': image.height,
        'channels': image.numChannels,
        'fileSize': bytes.length,
        'format': filePath.toLowerCase().split('.').last,
      };
    } catch (e) {
      PGLog.e('获取图像信息失败: $e');
      return null;
    }
  }
}
