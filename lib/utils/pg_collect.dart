import 'package:flutter/cupertino.dart';
import 'package:pg_turing_collect_event/model.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/service/share_preferences/shared_preferences_service.dart';
import 'package:turing_art/utils/screen_util.dart';

import '../config/env_config.dart' as turing_config;
import '../ops/config/ops_device_info_provider.dart';
import '../providers/network_provider.dart';
import 'platform.dart';

/// 埋点库配置初始化
class PGCollect {
  static Future<void> init(BuildContext context) async {
    // 1. 创建环境配置
    final isLowLevel = await isLowLevelWindows();
    final turingEnv = turing_config.EnvConfig.environment;
    final env = PgTuringConfigEnv(
      // 配置使用的网络环境，可选值：test, dev, pre, pro
      environment: switch (turingEnv) {
        turing_config.Environment.dev => Environment.dev,
        turing_config.Environment.qa => Environment.test,
        turing_config.Environment.prod => Environment.pro,
      },
      // 配置是否开启日志
      enableLog: turingEnv != turing_config.Environment.prod,
      // 配置是否开启事件记录(非windows7开启)
      enableRecordEvent: !isLowLevel,
    );

    final deviceInfo = OpsDeviceInfoProvider();

    // ignore: use_build_context_synchronously
    final networkProvider = context.read<NetworkProvider>();

    // 2. 创建请求 header 参数
    final header = PgTuringConfigHeader(
      appId: deviceInfo.getAppId(),
      channel: deviceInfo.getChannel(),
      appVersion: deviceInfo.getAPPVersion(),
      languageCode: deviceInfo.getLanguage(),
      networkState: networkProvider.networkTypeString,
      osVersion: deviceInfo.getOsVersion(),
      locale: deviceInfo.getLocale(),
      model: deviceInfo.getDeviceModel(),
      installTime: int.tryParse(deviceInfo.getInitStamp()) ?? 0,
      width: ScreenUtil().screenWidth.toInt(),
      height: ScreenUtil().screenHeight.toInt(),
      upgradeTime: int.tryParse(deviceInfo.getUpgradeStamp()) ?? 0,
      deviceId: deviceInfo.getDeviceId(),
      userId:  SharedPreferencesService.getUserId(),
      userToken: SharedPreferencesService.getUserToken(),
    );

    // 3. 创建完整配置
    final config = PgTuringConfig(
      env: env,
      header: header,
    );

    // 4. 执行初始化
    PgTuringCollect().initialize(config);
  }
}
