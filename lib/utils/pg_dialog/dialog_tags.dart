/// Dialog tags for managing different dialog instances
class DialogTags {
  // Privacy related tags
  static const String privacyPolicy = 'dialog_privacy_policy';
  static const String userAgreement = 'dialog_user_agreement';
  static const String aboutUs = 'dialog_about_us';
  static const String privacyCheck = 'dialog_privacy_check';

  // Loading related tags
  static const String webviewLoading = 'dialog_webview_loading';

  static const String preset = 'dialog_preset';

  static const String versionUpdate = 'dialog_version_update';

  static const String exitConfirm = 'dialog_exit_confirm';

  static const String purchase = 'dialog_purchase';

  static const String purchaseQRCode = 'dialog_purchase_qrcode';

  static const String purchaseSuccess = 'dialog_purchase_success';

  static const String customService = 'dialog_custom_service';

  static const String projectRename = 'dialog_project_rename';

  static const String winEditProfile = 'dialog_win_edit_profile';

  static const String homeProfile = 'dialog_home_profile';

  static const String newUserBenefit = 'dialog_new_user_benefit';

  static const String wechatGift = 'dialog_wechat_gift';

  static const String versionIntro = 'dialog_version_intro';

  static const String exportHistory = 'dialog_export_history';

  static const String exportList = 'dialog_export_list';

  static const String exportError = 'dialog_export_error';

  static const String loading = 'loading';

  // 兑换码相关标签
  static const String coupon = 'dialog_coupon';

  // 账号管理对话框标签
  static const String employee = 'dialog_employee';

  static const String renameEmployee = 'dialog_rename_employee';

  static const String couponSuccess = 'dialog_coupon_success';

  static const String desktopToast = 'desktop_toast';

  static const String desktopToastDismiss = 'desktop_toast_dismiss';

  static const String loginSelectAccount = 'login_select_account';

  // 添加子账号对话框标签
  static const String addSubAccount = 'dialog_add_sub_account';

  // 充值记录对话框标签
  static const String rechargeRecord = 'dialog_recharge_record';

  // 日期选择器对话框标签
  static const String dateRangeSelector = 'date_range_selector';

  // 导入预设列表对话框标签
  static const String importPresetList = 'dialog_import_preset_list';

  // 设置分辨率对话框标签
  static const String settingResolution = 'dialog_setting_resolution';

  // 创建主题预设对话框标签
  static const String createPreset = 'dialog_create_preset';

  // AIGC 预设详情弹窗
  static const String aigcPresetDetail = "dialog_aigc_preset_detail";

  // AIGC样片详情对话框标签
  static const String aigcSampleDetail = 'dialog_aigc_sample_detail';

  // AIGC PC 删除确认对话框标签
  static const String aigcPcDeleteConfirm = 'dialog_aigc_pc_delete_confirm';

  // AIGC导出路径设置对话框标签
  static const String aigcExportPath = 'dialog_aigc_export_path';

  // AIGC导出路径下拉菜单标签
  static const String aigcExportPathDropdown =
      'dialog_aigc_export_path_dropdown';

  // 放大选项列表对话框标签
  static const String zoomValueList = 'dialog_zoom_value_list';

  // 预设重新生成弹窗
  static const String regeneratePresetEffect = 'dialog_regenerate_preset';

  // AIGC 批量打样弹窗
  static const String aigcBatchSample = 'dialog_aigc_batch_sample';

  // 通用设置弹窗标签
  static const String generalSetting = 'dialog_general_setting';
}
