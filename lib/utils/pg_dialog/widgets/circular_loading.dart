import 'package:flutter/material.dart';
import 'dart:math' as math;

class CircularLoading extends StatefulWidget {
  const CircularLoading({
    super.key,
    this.size = 28.0,
    this.strokeWidth = 2.0,
    this.color = const Color(0xFF00B6DE),
  });

  final double size;
  final double strokeWidth;
  final Color color;

  @override
  State<CircularLoading> createState() => _CircularLoadingState();
}

class _CircularLoadingState extends State<CircularLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _controller.value * 2 * math.pi,
          child: CustomPaint(
            size: Size(widget.size, widget.size),
            painter: _CircularLoadingPainter(
              strokeWidth: widget.strokeWidth,
              color: widget.color,
            ),
          ),
        );
      },
    );
  }
}

class _CircularLoadingPainter extends CustomPainter {
  _CircularLoadingPainter({
    required this.strokeWidth,
    required this.color,
  });

  final double strokeWidth;
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    canvas.drawArc(
      Rect.fromCircle(
        center: Offset(size.width / 2, size.height / 2),
        radius: 14,
      ),
      0,
      1 * math.pi,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(_CircularLoadingPainter oldDelegate) =>
      oldDelegate.strokeWidth != strokeWidth || oldDelegate.color != color;
} 