import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/utils/pg_dialog/extensions/loading_extension.dart';
import 'package:turing_art/widgets/unity/helper/windows_unity_helper.dart';

import '../../ui/core/ui/desktop/title_bar_widget.dart';
import '../pg_log.dart';
import 'dialog_tags.dart';
import 'extensions/dialog_extension.dart';
import 'extensions/toast_extension.dart';
import 'extensions/unity_dialog_extension.dart';

/// 对话框工具类
///
/// 提供了Toast、Loading、对话框等功能
class PGDialog {
  // Toast相关常量
  static const toastPaddingHorizontal = 20.0;
  static const toastPaddingVertical = 12.0;
  static const toastBorderRadius = 8.0;
  static const toastTextColor = Color(0xFF121415);
  static const toastTextSize = 16.0;
  static const toastBackgroundColor = Colors.white;
  static const toastBorderColor = Color(0xFFEBF2F5);

  // 桌面版Toast样式
  static const desktopToastTextColor = Color(0xFFE1E2E5);
  static const desktopToastBackgroundColor = Color(0xFF1B1C1F);
  static final desktopToastBorderColor =
      const Color(0xFFFFFFFF).withOpacity(0.06);
  static const desktopToastTextSize = 12.0;

  // 共享状态变量
  static bool isShowloading = false;

  // 用于跟踪是否正在unity显示toast,防止下个toast来的时候，挖洞区域被更改，导致显示不下或者多余黑块
  static bool isShowToastOnUnity = false;

  // 用于跟踪desktop_toast是否已经被手动关闭
  static bool isDesktopToastDismissed = false;

  // 用于管理toast消息队列
  static final List<String> toastQueue = [];

  // 用于取消当前toast的消失定时器
  static Timer? toastDismissTimer;

  // 用于跟踪在Unity窗口上显示的对话框
  static final Set<String?> unityDialogTags = {};

  // 追踪是否有打开的对话框
  static bool get hasOpenDialog => SmartDialog.checkExist();

  /// 关闭对话框(📢 如果调用dismiss后马上需要调用show相关方法，那就需要await dismiss)
  static Future<void> dismiss({String? tag}) async {
    PGLog.d('PGDialog dismiss tag: $tag');
    // 如果是loading，或者是null，并且没有展示loading， 直接返回
    if (!isShowloading && (tag == null || tag == DialogTags.loading)) {
      PGLog.d('PGDialog dismiss tag: $tag, not show loading, return');
      return;
    }

    // 如果关闭的是desktop_toast，设置标志为true
    if (tag == DialogTags.desktopToast) {
      isDesktopToastDismissed = true;
    }

    await SmartDialog.dismiss(tag: tag);
    if (tag == DialogTags.loading || tag == null) {
      isShowloading = false;
    }
    // 如果是Unity对话框，需要恢复Unity窗口
    if (Platform.isWindows && (tag == null || unityDialogTags.contains(tag))) {
      if (tag != null) {
        unityDialogTags.remove(tag);
      } else {
        // 兼容外部loading，调用dismiss不传tag的情况（外部一定需要showLoading和dismiss成对使用）
        if (unityDialogTags.contains('loading')) {
          unityDialogTags.remove('loading');
        } else {
          // 如果tag为null，关闭所有对话框，清空记录
          unityDialogTags.clear();
        }
      }

      // 如果没有其他Unity对话框，恢复Unity窗口
      if (unityDialogTags.isEmpty) {
        await WindowsUnityHelper.resetUnityWindowForDialog();
        TitleBarWidget.setWindowResizable(resizable: true);
      }
    }
    // 在dismiss方法中添加对toast队列的清理
    if (tag == DialogTags.desktopToast || tag == null) {
      // 标记toast已被手动关闭
      isDesktopToastDismissed = true;
      // 清空toast队列
      toastQueue.clear();
      // 取消定时器
      cancelToastDismissTimer();
    }
  }

  // 取消当前toast的消失定时器
  static void cancelToastDismissTimer() {
    toastDismissTimer?.cancel();
    toastDismissTimer = null;
  }

  // ==================== 转发方法 ====================
  // 以下方法转发到对应的扩展类，保持原有的调用方式

  // Loading相关方法
  static void showLoading({bool usePenetrate = false}) {
    LoadingExtension.showLoading(usePenetrate: usePenetrate);
  }

  // Toast相关方法
  static void showToast(String msg, {bool forUnity = false}) {
    ToastExtension.showToast(msg, forUnity: forUnity);
  }

  // 对话框相关方法
  static void showCustomDialog({
    required Widget child,
    Future<void> Function()? onClose,
    required double width,
    required double height,
    String? tag,
    bool needBlur = false,
    Color? backgroundColor,
    bool forUnity = false,
    bool needGesture = false,
    double radius = 12,
  }) {
    DialogExtension.showCustomDialog(
      child: child,
      onClose: onClose,
      width: width,
      height: height,
      tag: tag,
      needBlur: needBlur,
      backgroundColor: backgroundColor,
      forUnity: forUnity,
      needGesture: needGesture,
      radius: radius,
    );
  }

  static void showPositionedDialog({
    required Widget child,
    required double width,
    required double height,
    required Alignment alignment,
    EdgeInsets? padding,
    String? tag,
    bool clickMaskDismiss = true,
    Color? maskColor = Colors.transparent,
    bool needBlur = false,
    bool forUnity = false,
    Future<void> Function()? onClose,
    bool fullscreenMask = false,
  }) {
    DialogExtension.showPositionedDialog(
      child: child,
      width: width,
      height: height,
      alignment: alignment,
      padding: padding,
      tag: tag,
      clickMaskDismiss: clickMaskDismiss,
      maskColor: maskColor,
      needBlur: needBlur,
      forUnity: forUnity,
      onClose: onClose,
      fullscreenMask: fullscreenMask,
    );
  }

  // 检查特定标签的对话框是否正在显示
  static bool isDialogVisible(String tag) {
    return DialogExtension.isDialogVisible(tag);
  }

  // Unity对话框相关方法
  static Future<void> showToastOnUnity(String msg, {bool isImportant = false}) {
    return UnityDialogExtension.showToastOnUnity(msg, isImportant: isImportant);
  }

  static Future<void> showCustomDialogOnUnity({
    required Widget child,
    Future<void> Function()? onClose,
    required double width,
    required double height,
    String? tag,
    bool needBlur = false,
    bool centerInWindow = true,
    Offset dialogPosition = const Offset(0, 0),
    int margin = 0,
    int unityAlpha = 153,
    double roundRadius = 12,
  }) {
    return UnityDialogExtension.showCustomDialogOnUnity(
      child: child,
      onClose: onClose,
      width: width,
      height: height,
      tag: tag,
      needBlur: needBlur,
      centerInWindow: centerInWindow,
      dialogPosition: dialogPosition,
      margin: margin,
      unityAlpha: unityAlpha,
      roundRadius: roundRadius,
    );
  }
}
