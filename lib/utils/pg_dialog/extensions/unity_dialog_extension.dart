import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/pg_dialog/extensions/dialog_extension.dart';
import 'package:turing_art/utils/pg_dialog/extensions/toast_extension.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/widgets/unity/helper/windows_unity_helper.dart';

/// Unity对话框相关功能扩展
extension UnityDialogExtension on PGDialog {
  /// 在Unity窗口上显示Toast
  /// [msg] 要显示的消息
  /// [isImportant] 是否重要消息，如果为true则加入队列等待显示，如果为false则可能被丢弃
  static Future<void> showToastOnUnity(String msg,
      {bool isImportant = false}) async {
    if (!Platform.isWindows) {
      throw UnsupportedError('showCustomDialogOnUnity仅在Windows平台上可用');
    }

    // 如果正在显示toast
    if (PGDialog.isShowToastOnUnity) {
      // 如果是重要消息，加入队列等待显示
      if (isImportant) {
        PGDialog.toastQueue.add(msg);
      }
      return;
    }

    // 处理重要消息
    await _showSingleToast(msg);
  }

  /// 显示单个Toast消息
  static Future<void> _showSingleToast(String msg) async {
    PGDialog.isShowToastOnUnity = true;

    // 判断Windows 7平台，在Windows 7上使用优化的显示方式
    final bool isWin7 = AppConstants.isWin7;

    // 定义toast在屏幕中居中显示
    const centerInWindow = true;
    const dialogPosition = Offset(0, 0);
    // unity的挖孔区域和实际值toast组件宽高有稍微差异，暂时调整到没有黑边的状态，后期可调整
    const marginX = 0;
    const marginY = 0;
    // 计算文本宽高，使用与showToast相同的样式
    final textStyle = TextStyle(
      color: AppConstants.isDesktop
          ? PGDialog.desktopToastTextColor
          : PGDialog.toastTextColor,
      fontSize: AppConstants.isDesktop
          ? PGDialog.desktopToastTextSize
          : PGDialog.toastTextSize,
      fontFamily: Fonts.defaultFontFamily,
      fontWeight: AppConstants.isDesktop ? Fonts.medium : Fonts.semiBold,
    );

    final textSpan = TextSpan(
      text: msg,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      maxLines: 10, // 限制最大行数
      textAlign: TextAlign.center, // 添加这一行使文本居中对齐
    );

    textPainter.layout(maxWidth: 300); // 限制最大宽度

    // 文本实际宽高 + padding，使用与showToast相同的padding
    final width = textPainter.width + (PGDialog.toastPaddingHorizontal * 2);
    final height = textPainter.height + (PGDialog.toastPaddingVertical * 2);

    // 计算对话框区域（包含边距）
    final devicePixelRatio =
        PlatformDispatcher.instance.views.first.devicePixelRatio;
    final x = ((dialogPosition.dx - marginX) * devicePixelRatio).round();
    final y = ((dialogPosition.dy - marginY) * devicePixelRatio).round();
    final widthPx = ((width + marginX * 2) * devicePixelRatio).round();
    final heightPx = ((height + marginY * 2) * devicePixelRatio).round();
    final roundRadiusPx =
        (PGDialog.toastBorderRadius * devicePixelRatio).round();

    // 在Windows 7上使用不同的方式挖孔，避免闪烁
    if (isWin7) {
      // 先设置区域，然后再显示Toast，减少闪烁
      await WindowsUnityHelper.excludeUnityWindowRegion(
          x: x,
          y: y,
          width: widthPx,
          height: heightPx,
          center: centerInWindow,
          tag: 'toast',
          roundRadius: roundRadiusPx);

      // 等待一小段时间，确保挖孔完成
      await Future.delayed(const Duration(milliseconds: 50));

      // 显示对话框
      ToastExtension.showToast(msg, forUnity: true);
    } else {
      // 在其他平台上使用原有方式
      await WindowsUnityHelper.excludeUnityWindowRegion(
          x: x,
          y: y,
          width: widthPx,
          height: heightPx,
          center: centerInWindow,
          tag: 'toast',
          roundRadius: roundRadiusPx);

      // 显示对话框
      ToastExtension.showToast(msg, forUnity: true);
    }

    // 等待toast显示完成后恢复Unity窗口
    // 获取SmartDialog的显示时间
    final displayTime = SmartDialog.config.toast.displayTime;
    await Future.delayed(displayTime);

    // 直接移除toast区域，不依赖_unityDialogTags
    await WindowsUnityHelper.removeUnityWindowRegion('toast');
    PGDialog.isShowToastOnUnity = false;

    // 检查队列中是否还有重要消息需要排除显示
    if (PGDialog.toastQueue.isNotEmpty) {
      // 如果队列中还有消息，递归调用自身处理下一个消息
      final nextMsg = PGDialog.toastQueue.first;
      PGDialog.toastQueue.removeAt(0);
      await _showSingleToast(nextMsg);
    }
  }

  /// 显示自定义对话框（在Unity窗口上）
  ///
  /// [child] 对话框内容
  /// [width] 对话框宽度
  /// [height] 对话框高度
  /// [onClose] 对话框关闭时的回调
  /// [tag] 对话框标签，用于关闭特定对话框
  /// [needBlur] 是否需要模糊背景
  /// [centerInWindow] 是否将对话框居中显示在Unity窗口中
  /// [dialogPosition] 对话框位置，相对于屏幕左上角的偏移量
  /// [margin] 对话框周围的额外边距
  /// [unityAlpha] Unity窗口的透明度 (0-255)
  /// [roundRadius] 对话框区域的圆角半径
  static Future<void> showCustomDialogOnUnity({
    required Widget child,
    Future<void> Function()? onClose,
    required double width,
    required double height,
    String? tag,
    bool needBlur = false,
    bool centerInWindow = true,
    Offset dialogPosition = const Offset(0, 0),
    int margin = 0,
    int unityAlpha = 153,
    double roundRadius = 12,
  }) async {
    if (!Platform.isWindows) {
      throw UnsupportedError('showCustomDialogOnUnity仅在Windows平台上可用');
    }
    TitleBarWidget.setWindowResizable(resizable: false);

    // 在显示Unity对话框时禁用自动焦点转移，避免焦点竞态条件
    await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
        flutterWindowFocus: true);

    // 计算对话框区域（包含边距,window废弃）
    final devicePixelRatio =
        PlatformDispatcher.instance.views.first.devicePixelRatio;
    final x = ((dialogPosition.dx - margin) * devicePixelRatio).round();
    final y = ((dialogPosition.dy - margin) * devicePixelRatio).round();
    final widthPx = ((width + margin * 2) * devicePixelRatio).round();
    final heightPx = ((height + margin * 2) * devicePixelRatio).round();
    final roundRadiusPx = (roundRadius * devicePixelRatio).round();

    // 设置Unity窗口区域和透明度
    await WindowsUnityHelper.excludeUnityWindowRegion(
        x: x,
        y: y,
        width: widthPx,
        height: heightPx,
        center: centerInWindow,
        tag: "dialog",
        roundRadius: roundRadiusPx);

    await WindowsUnityHelper.setUnityWindowTransparency(unityAlpha);
    await WindowsUnityHelper.setUnityWindowToBottom(toBottom: true);
    // 记录这是一个Unity对话框
    if (tag != null) {
      PGDialog.unityDialogTags.add(tag);
    }

    // 包装onClose回调，确保在对话框关闭时清理Unity窗口和恢复焦点管理
    Future<void> Function()? wrappedOnClose;
    if (onClose != null || tag != null || PGDialog.unityDialogTags.isNotEmpty) {
      wrappedOnClose = () async {
        // 如果没有其他Unity对话框，恢复Unity窗口
        if (tag != null && PGDialog.unityDialogTags.contains(tag)) {
          PGDialog.unityDialogTags.remove(tag);
          if (PGDialog.unityDialogTags.isEmpty) {
            await WindowsUnityHelper.resetUnityWindowForDialog();
            TitleBarWidget.setWindowResizable(resizable: true);

            // 恢复自动焦点转移，允许焦点自动转移到Unity窗口
            await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
                flutterWindowFocus: false);
            PGLog.d('Unity对话框关闭后，恢复自动焦点转移');
          }
        }
        // 保证顺序，最后执行原始回调
        if (onClose != null) {
          await onClose();
        }
      };
    }

    if (centerInWindow) {
      // 显示对话框（使用原有的逻辑）
      DialogExtension.showCustomDialog(
        child: child,
        width: width,
        height: height,
        onClose: wrappedOnClose,
        tag: tag,
        needBlur: needBlur,
        forUnity: true,
      );
    } else {
      DialogExtension.showPositionedDialog(
        child: child,
        width: width,
        height: height,
        alignment: Alignment.topLeft,
        padding: EdgeInsets.only(
          left: dialogPosition.dx,
          top: dialogPosition.dy,
        ),
        onClose: wrappedOnClose,
        tag: tag,
        needBlur: needBlur,
        forUnity: true,
      );
    }
  }
}
