import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_dialog/widgets/circular_loading.dart';

/// 加载指示器相关功能扩展
extension LoadingExtension on PGDialog {
  /// 显示加载指示器
  static void showLoading({bool usePenetrate = false}) {
    if (PGDialog.isShowloading) {
      // 如果正在显示loading，则不重复显示，以免有loading无法关闭导致UI无法操作
      return;
    }
    // 消失其他的toast
    PGDialog.dismiss(tag: DialogTags.desktopToast);
    PGDialog.unityDialogTags.add(DialogTags.loading);
    PGDialog.isShowloading = true;
    SmartDialog.showLoading(
      builder: (_) => Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: const Color(0xFF121415).withOpacity(0.8),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Center(
          child: CircularLoading(
            size: 28,
            strokeWidth: 2,
            color: AppConstants.isDesktop
                ? const Color(0xFFF71561)
                : const Color(0xFF00B6DE),
          ),
        ),
      ),
      maskColor: Colors.transparent,
      controller: SmartDialogController(),
      alignment: Alignment.center,
      useAnimation: true,
      usePenetrate: usePenetrate,
      animationTime: const Duration(milliseconds: 300),
    );
  }
}
