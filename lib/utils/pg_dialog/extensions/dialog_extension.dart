import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 对话框相关功能扩展
extension DialogExtension on PGDialog {
  /// 显示自定义对话框
  static void showCustomDialog({
    required Widget child,
    Future<void> Function()? onClose,
    required double width,
    required double height,
    String? tag,
    bool needBlur = false,
    Color? backgroundColor,
    bool forUnity = false,
    bool needGesture = false,
    double radius = 12,
  }) {
    PGLog.d('PGDialog showCustomDialog tag: $tag');
    final bool isWin7 = AppConstants.isWin7;
    final bool useAnimation = !(isWin7 && forUnity);
    // 在Windows 7上使用StatefulBuilder包装弹窗内容，避免闪烁
    SmartDialog.show(
      builder: (_) => needBlur
          ? Stack(
              children: [
                // 背景层 - 点击时关闭对话框
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () {
                      if (needGesture) {
                        PGDialog.dismiss(tag: tag);
                      }
                    },
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        color: backgroundColor ?? Colors.black.withAlpha(150),
                      ),
                    ),
                  ),
                ),
                // 内容层 - 不响应点击事件
                Center(
                  child: Material(
                    color: Colors.transparent,
                    child: Container(
                      width: width,
                      height: height,
                      decoration: BoxDecoration(
                        color: backgroundColor ?? Colors.black,
                        borderRadius: BorderRadius.circular(radius),
                      ),
                      child: child,
                    ),
                  ),
                ),
              ],
            )
          : Stack(
              children: [
                // 背景层 - 点击时关闭对话框
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () {
                      if (needGesture) {
                        PGDialog.dismiss(tag: tag);
                      }
                    },
                    child: Container(
                      color: backgroundColor ?? Colors.black.withAlpha(150),
                    ),
                  ),
                ),
                // 内容层 - 不响应点击事件
                Center(
                  child: Material(
                    color: Colors.transparent,
                    child: Container(
                      width: width,
                      height: height,
                      decoration: BoxDecoration(
                        // color: contentBgColor ?? Colors.black,
                        borderRadius: BorderRadius.circular(radius),
                      ),
                      child: child,
                    ),
                  ),
                ),
              ],
            ),
      maskColor: Colors.transparent,
      clickMaskDismiss: false,
      onDismiss: onClose,
      controller: SmartDialogController(),
      alignment: Alignment.center,
      useAnimation: useAnimation,
      tag: tag,
      animationType: SmartAnimationType.fade,
      animationTime:
          useAnimation ? const Duration(milliseconds: 300) : Duration.zero,
    );
  }

  /// 显示自定义位置的对话框
  static void showPositionedDialog({
    required Widget child,
    required double width,
    required double height,
    required Alignment alignment,
    EdgeInsets? padding,
    String? tag,
    bool clickMaskDismiss = true,
    Color? maskColor = Colors.transparent,
    bool needBlur = false,
    bool forUnity = false,
    Future<void> Function()? onClose,
    bool fullscreenMask = false,
  }) {
    final bool isWin7 = AppConstants.isWin7;
    final bool useAnimation = !(isWin7 && forUnity);

    // 如果需要全屏遮罩
    if (fullscreenMask) {
      SmartDialog.show(
        builder: (_) => Stack(
          children: [
            // 全屏遮罩层
            Positioned.fill(
              child: clickMaskDismiss
                  ? GestureDetector(
                      onTap: () {
                        PGLog.d('PGDialog全屏遮罩层被点击 - tag: $tag');
                        PGDialog.dismiss(tag: tag);
                      },
                      behavior: HitTestBehavior.translucent,
                      child: Container(
                        color: maskColor,
                      ),
                    )
                  : Container(
                      color: maskColor,
                    ),
            ),
            // 内容层
            Align(
              alignment: alignment,
              child: Padding(
                padding: padding ?? EdgeInsets.zero,
                child: SizedBox(
                  width: width,
                  height: height,
                  child: child,
                ),
              ),
            ),
          ],
        ),
        maskColor: Colors.transparent, // 使用自定义遮罩，所以这里设为透明
        clickMaskDismiss: false, // 由自定义遮罩处理点击事件
        onDismiss: onClose,
        controller: SmartDialogController(),
        alignment: Alignment.center, // 内容已经在Stack中对齐，这里设置为中心
        useAnimation: useAnimation,
        tag: tag,
        animationType: SmartAnimationType.fade,
        animationTime:
            useAnimation ? const Duration(milliseconds: 300) : Duration.zero,
      );
    } else {
      // 原有实现
      SmartDialog.show(
        builder: (_) => StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: padding ?? EdgeInsets.zero,
              child: SizedBox(
                width: width,
                height: height,
                child: child,
              ),
            );
          },
        ),
        maskColor: maskColor,
        clickMaskDismiss: clickMaskDismiss,
        onDismiss: onClose,
        controller: SmartDialogController(),
        alignment: alignment,
        useAnimation: useAnimation,
        tag: tag,
        animationType: SmartAnimationType.fade,
        animationTime:
            useAnimation ? const Duration(milliseconds: 300) : Duration.zero,
      );
    }
  }

  /// 检查特定标签的对话框是否正在显示
  ///
  /// [tag] 对话框的标签
  ///
  /// 返回true表示对话框正在显示，false表示对话框未显示
  static bool isDialogVisible(String tag) {
    // 使用 SmartDialog 检查指定标签的对话框是否存在
    return SmartDialog.checkExist(tag: tag);
  }
}
