import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';

import '../pg_dialog.dart';

/// Toast 相关功能扩展
extension ToastExtension on PGDialog {
  /// 展示Toast
  static void showToast(String msg, {bool forUnity = false}) {
    // 如果是桌面版，且不是在Unity中，使用桌面版Toast
    if (AppConstants.isDesktop && !forUnity) {
      _showDesktopToast(msg);
    } else {
      // 在Windows 7上禁用动画，避免闪烁
      final bool isWin7 = AppConstants.isWin7;
      final bool useAnimation = !isWin7;

      SmartDialog.showToast(
        msg,
        builder: (context) => StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: PGDialog.toastPaddingHorizontal,
                  vertical: PGDialog.toastPaddingVertical),
              decoration: BoxDecoration(
                color: AppConstants.isDesktop
                    ? PGDialog.desktopToastBackgroundColor
                    : PGDialog.toastBackgroundColor,
                borderRadius: BorderRadius.circular(PGDialog.toastBorderRadius),
                border: Border.all(
                    color: AppConstants.isDesktop
                        ? PGDialog.desktopToastBorderColor
                        : PGDialog.toastBorderColor),
              ),
              child: Text(
                msg,
                style: TextStyle(
                  color: AppConstants.isDesktop
                      ? PGDialog.desktopToastTextColor
                      : PGDialog.toastTextColor,
                  fontSize: AppConstants.isDesktop
                      ? PGDialog.desktopToastTextSize
                      : PGDialog.toastTextSize,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                ),
                textAlign: TextAlign.center,
              ),
            );
          },
        ),
        controller: SmartDialogController(),
        displayType: SmartToastType.normal,
        animationType: SmartAnimationType.fade,
        alignment: Alignment.center,
        useAnimation: useAnimation,
        animationTime:
            useAnimation ? const Duration(milliseconds: 300) : Duration.zero,
      );
    }
  }

  /// 桌面版Toast
  /// 位置在屏幕顶部，有出现了消失的位移及透明度变化动画
  static void _showDesktopToast(String msg) {
    // 取消当前的定时器
    PGDialog.cancelToastDismissTimer();

    // 清空之前的队列
    PGDialog.toastQueue.clear();

    // 将新消息添加到队列
    PGDialog.toastQueue.add(msg);

    // 关闭当前显示的所有toast
    SmartDialog.dismiss(tag: DialogTags.loading);
    SmartDialog.dismiss(tag: DialogTags.desktopToast);
    SmartDialog.dismiss(tag: DialogTags.desktopToastDismiss);

    // 立即显示新的toast
    _showToastImmediately(msg);
  }

  /// 立即显示toast，不考虑队列
  static void _showToastImmediately(String msg) {
    // 重置标志，表示toast未被手动关闭
    PGDialog.isDesktopToastDismissed = false;

    // 使用Stack布局实现动画
    SmartDialog.show(
      tag: DialogTags.desktopToast,
      builder: (context) {
        return TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          curve: Curves.easeInOut,
          duration: const Duration(milliseconds: 300), // 出现动画时间0.3秒
          builder: (context, value, child) {
            // 从顶部向下移动到距离顶部84的位置
            // 同时透明度从0变为1
            return Stack(
              children: [
                Positioned(
                  top: (84 - 50) * value + (1 - value) * (-50), // 从-50移动到84-50
                  left: 0,
                  right: 0,
                  child: Opacity(
                    opacity: value,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: PGDialog.toastPaddingHorizontal,
                          vertical: PGDialog.toastPaddingVertical,
                        ),
                        decoration: BoxDecoration(
                          color: PGDialog.desktopToastBackgroundColor,
                          borderRadius:
                              BorderRadius.circular(PGDialog.toastBorderRadius),
                          border: Border.all(
                              color: PGDialog.desktopToastBorderColor),
                        ),
                        child: Text(
                          msg,
                          style: TextStyle(
                            color: PGDialog.desktopToastTextColor,
                            fontSize: PGDialog.desktopToastTextSize,
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.medium,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
      alignment: Alignment.topCenter,
      useAnimation: false,
      maskColor: Colors.transparent,
      clickMaskDismiss: false,
      maskWidget: const SizedBox.shrink(), // 使用空的遮罩组件，不拦截点击事件
    );

    // 设置3秒后执行消失动画
    PGDialog.toastDismissTimer = Timer(const Duration(seconds: 3), () {
      // 如果toast已经被手动关闭，则不执行消失动画
      if (PGDialog.isDesktopToastDismissed) {
        // 从队列中移除当前消息
        if (PGDialog.toastQueue.isNotEmpty) {
          PGDialog.toastQueue.removeAt(0);
        }
        return;
      }

      // 执行消失动画
      _showToastDismissAnimation(msg);
    });
  }

  /// 显示toast消失动画
  static void _showToastDismissAnimation(String msg) {
    // 先关闭原来的toast
    SmartDialog.dismiss(tag: DialogTags.desktopToast);

    // 创建消失动画
    SmartDialog.show(
      tag: DialogTags.desktopToastDismiss,
      builder: (context) {
        return TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 1.0, end: 0.0),
          curve: Curves.easeInOut,
          duration: const Duration(milliseconds: 300), // 消失动画时间0.3秒
          onEnd: () {
            // 动画结束后关闭这个对话框
            SmartDialog.dismiss(tag: DialogTags.desktopToastDismiss);

            // 从队列中移除当前消息
            if (PGDialog.toastQueue.isNotEmpty) {
              PGDialog.toastQueue.removeAt(0);
            }
          },
          builder: (context, value, child) {
            // 从距离顶部84向上移动至完全消失
            // 同时透明度从1变为0
            return Stack(
              children: [
                Positioned(
                  top: (84 - 50) * value + (1 - value) * (-50), // 从84-50移动到-50
                  left: 0,
                  right: 0,
                  child: Opacity(
                    opacity: value,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: PGDialog.toastPaddingHorizontal,
                          vertical: PGDialog.toastPaddingVertical,
                        ),
                        decoration: BoxDecoration(
                          color: PGDialog.desktopToastBackgroundColor,
                          borderRadius:
                              BorderRadius.circular(PGDialog.toastBorderRadius),
                          border: Border.all(
                              color: PGDialog.desktopToastBorderColor),
                        ),
                        child: Text(
                          msg,
                          style: TextStyle(
                            color: PGDialog.desktopToastTextColor,
                            fontSize: PGDialog.desktopToastTextSize,
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.medium,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
      alignment: Alignment.topCenter,
      useAnimation: false,
      maskColor: Colors.transparent,
      clickMaskDismiss: false,
      maskWidget: const SizedBox.shrink(), // 使用空的遮罩组件，不拦截点击事件
    );
  }

  /// 取消当前toast的消失定时器
  static void cancelToastDismissTimer() {
    PGDialog.toastDismissTimer?.cancel();
    PGDialog.toastDismissTimer = null;
  }
}
