import 'dart:io';

import 'package:flutter/material.dart' as material show Colors;
import 'package:flutter/material.dart' hide Colors;
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:turing_art/ui/core/ui/desktop/h5_windows_webview.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/url_launcher_util.dart';

import 'pg_dialog/dialog_tags.dart';

/// 客服服务处理类
/// 用于处理客服相关的功能，如获取聊天HTML路径等
class CustomServiceHandler {
  // 静态缓存，存储HTML路径
  static String? _cachedHtmlPath;

  /// 获取聊天HTML的路径
  /// 带有缓存功能，避免重复创建文件
  static Future<String> getChatHtmlPath() async {
    // 如果缓存中已有路径且文件存在，直接返回
    if (_cachedHtmlPath != null) {
      final file = File(_cachedHtmlPath!.replaceFirst('file://', ''));
      if (await file.exists()) {
        PGLog.d('使用缓存的chat.html路径: $_cachedHtmlPath');
        return _cachedHtmlPath!;
      }
    }

    // 获取应用程序文档目录
    final directory = await getApplicationDocumentsDirectory();
    final filePath = path.join(directory.path, 'chat.html');

    // 检查文件是否存在，如果不存在则从assets复制
    final file = File(filePath);
    if (!await file.exists()) {
      try {
        // 从assets加载HTML内容
        final htmlContent =
            await rootBundle.loadString('lib/ui/core/ui/desktop/chat.html');
        // 写入文件
        await file.writeAsString(htmlContent);
        PGLog.d('成功将chat.html写入到: $filePath');
      } catch (e) {
        PGLog.e('加载chat.html失败: $e');
        // 创建一个简单的备用HTML
        const fallbackHtml = '''
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>在线咨询</title>
  <style>
    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
    h1 { color: #333; }
    p { color: #666; }
  </style>
</head>
<body>
  <h1>在线咨询</h1>
  <p>请联系我们的客服团队获取帮助</p>
  <p>电话: 400-xxx-xxxx</p>
  <p>邮箱: <EMAIL></p>
</body>
</html>
''';
        await file.writeAsString(fallbackHtml);
        PGLog.d('使用备用HTML');
      }
    }

    // 返回file://协议的完整路径并缓存
    _cachedHtmlPath = 'file://${file.path}';
    return _cachedHtmlPath!;
  }

  /// 清除缓存的HTML路径
  static void clearCache() {
    _cachedHtmlPath = null;
    PGLog.d('已清除chat.html路径缓存');
  }

  static void showCustomerService(String title) async {
    if (AppConstants.isWindows) {
      final htmlPath = await CustomServiceHandler.getChatHtmlPath();

      if (AppConstants.isWin7) {
        UrlLauncherUtil.openInSystemBrowser(htmlPath);
      } else {
        if (PGDialog.isDialogVisible(DialogTags.customService)) {
          PGLog.d(
              'CustomServiceHandler showCustomerService, but dialog already exist, return');
          return;
        }
        PGDialog.showCustomDialog(
            width: 500,
            height: 610,
            tag: DialogTags.customService,
            needBlur: false,
            needGesture: true,
            backgroundColor: material.Colors.black.withAlpha(150),
            child: GestureDetector(
              // 阻止点击事件冒泡
              onTap: () {},
              child: H5WindowsWebView(
                url: htmlPath,
                title: '客服',
                backgroundColor: material.Colors.white,
                onClose: () => PGDialog.dismiss(tag: DialogTags.customService),
                forDialog: false,
              ),
            ));
      }
    } else {
      PGDialog.showToast('敬请期待');
    }
  }
}
