/// 文件名处理工具类
/// 提供通用的文件名提取和处理功能
class FileNameUtils {
  /// 从文件名中提取名称部分（去除扩展名）
  ///
  /// [fileName] 完整的文件名，包含扩展名
  /// 返回不含扩展名的文件名部分
  static String extractNameWithoutExtension(String fileName) {
    final lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex > 0) {
      return fileName.substring(0, lastDotIndex);
    }
    return fileName;
  }

  /// 从文件路径中提取文件名（不含路径）
  ///
  /// [filePath] 完整的文件路径
  /// 返回文件名部分（不含路径但可能包含扩展名）
  static String extractFileName(String filePath) {
    // 处理不同平台的路径分隔符
    final lastSlashIndex = filePath.lastIndexOf('/');
    final lastBackslashIndex = filePath.lastIndexOf('\\');
    final lastSeparatorIndex = lastSlashIndex > lastBackslashIndex
        ? lastSlashIndex
        : lastBackslashIndex;

    if (lastSeparatorIndex >= 0) {
      return filePath.substring(lastSeparatorIndex + 1);
    }
    return filePath;
  }

  /// 获取文件的扩展名（不含点号）
  ///
  /// [fileName] 文件名或文件路径
  /// 返回文件扩展名（不含点号），如果没有扩展名则返回空字符串
  static String getFileExtension(String fileName) {
    final lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex > 0 && lastDotIndex < fileName.length - 1) {
      return fileName.substring(lastDotIndex + 1);
    }
    return '';
  }
}
