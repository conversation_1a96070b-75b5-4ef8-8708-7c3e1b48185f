// Windows平台的权限处理模拟实现

// 模拟PermissionStatus枚举
enum PermissionStatus {
  denied,
  granted,
  restricted,
  limited,
  permanentlyDenied,
  provisional,
}

// 模拟Permission类
class Permission {
  static final Permission photos = Permission._();
  static final Permission storage = Permission._();
  static final Permission videos = Permission._();

  Permission._();

  // 模拟请求权限方法，对于Windows平台始终返回granted状态
  Future<PermissionStatus> request() async {
    return PermissionStatus.granted;
  }

  // 模拟检查权限状态方法，对于Windows平台始终返回granted状态
  Future<PermissionStatus> get status async {
    return PermissionStatus.granted;
  }

  // 模拟检查是否永久拒绝
  Future<bool> get isPermanentlyDenied async {
    return false;
  }
}

// 模拟打开应用设置
Future<bool> openAppSettings() async {
  return true;
}
