import 'package:turing_art/datalayer/domain/models/coupon/coupon.dart';
import 'package:turing_art/datalayer/repository/coupon_repository.dart';
import 'package:turing_art/datalayer/service/coupon/coupon_service.dart';

class CouponRepositoryImpl extends CouponRepository {
  final CouponService _couponService;

  CouponRepositoryImpl(this._couponService);

  @override
  Future<Coupon?> applyCoupon(String code) async {
    // 兑换码类型固定为 export_count
    return await _couponService.applyCoupon(code, 'export_count');
  }
}
