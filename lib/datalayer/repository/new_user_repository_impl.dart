import 'package:turing_art/datalayer/domain/models/ops_operation/operation_activity.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/service/ops_operation/ops_operation_service.dart';

class NewUserRepositoryImpl implements NewUserRepository {
  final OpsOperationService _service;

  NewUserRepositoryImpl(this._service);

  @override
  Future<OperationActivity?> getNewUserBenefits() async {
    return await _service.getSingleOperationActivity(OperationType.newUserGift);
  }
}
