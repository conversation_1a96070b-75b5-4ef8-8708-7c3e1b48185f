import 'package:turing_art/datalayer/domain/models/ops_operation/operation_activity.dart';
import 'package:turing_art/datalayer/repository/version_intro_repository.dart';
import 'package:turing_art/datalayer/service/ops_operation/ops_operation_service.dart';

class VersionIntroRepositoryImpl implements VersionIntroRepository {
  /// 版本介绍列表
  List<OperationActivity>? _versionIntroList;

  final OpsOperationService _service;

  VersionIntroRepositoryImpl(this._service);

  @override
  Future<List<OperationActivity>> getVersionIntroInfo() async {
    if (_versionIntroList != null) {
      return _versionIntroList!;
    } else {
      _versionIntroList =
          await _service.getOperationActivities(OperationType.versionIntro);
      return _versionIntroList!;
    }
  }
}
