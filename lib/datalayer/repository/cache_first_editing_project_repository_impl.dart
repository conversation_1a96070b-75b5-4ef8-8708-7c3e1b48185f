import 'dart:async' show unawaited, StreamController;

import 'package:synchronized/synchronized.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/workspace_db_operater.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'current_editing_project_repository.dart';

/// 数据库操作相关的纯函数

/// 文件缓存管理器
/// 负责管理工作区文件的缓存和持久化存储
class WorkspaceFilesCacheManager {
  final Lock _cacheLock = Lock();
  final Map<String, WorkspaceFile> _fileCache = <String, WorkspaceFile>{};
  String? _currentWorkspaceId;
  final DbOperater _dbOperater;

  WorkspaceFilesCacheManager(this._dbOperater);

  /// 回滚文件缓存
  Future<void> _rollbackFileCache(
      String fileId, WorkspaceFile? previousFile) async {
    if (previousFile != null) {
      await _cacheLock.synchronized(() {
        _fileCache[fileId] = previousFile;
      });
    }
  }

  /// 回滚批量文件缓存
  Future<void> _rollbackBatchFileCache(
      Map<String, WorkspaceFile> previousFiles) async {
    await _cacheLock.synchronized(() {
      for (final entry in previousFiles.entries) {
        _fileCache[entry.key] = entry.value;
      }
    });
  }

  /// 加载工作区的文件到缓存
  Future<List<WorkspaceFile>> loadWorkspace(String workspaceId) async {
    return await _cacheLock.synchronized(() async {
      try {
        _currentWorkspaceId = workspaceId;
        _fileCache.clear();

        final files = await _dbOperater.loadFiles(workspaceId);

        for (final file in files) {
          _fileCache[file.fileId] = WorkspaceFile.fromEntity(file);
        }
        return List.unmodifiable(_fileCache.values);
      } catch (e) {
        PGLog.e('Failed to load workspace files: $e');
        return [];
      }
    });
  }

  /// 清空缓存
  Future<void> clear() async {
    await _cacheLock.synchronized(() {
      _currentWorkspaceId = null;
      _fileCache.clear();
    });
  }

  /// 获取所有文件
  Future<List<WorkspaceFile>> getAllFiles() async {
    return await _cacheLock.synchronized(() {
      return List.unmodifiable(_fileCache.values);
    });
  }

  /// 获取单个文件
  Future<WorkspaceFile?> getFile(String fileId) async {
    return await _cacheLock.synchronized(() {
      return _fileCache[fileId];
    });
  }

  /// 添加文件
  Future<List<WorkspaceFile>> addFile(WorkspaceFile file) async {
    return await _cacheLock.synchronized(() async {
      final workspaceId = _currentWorkspaceId;
      if (workspaceId == null) {
        throw StateError('No workspace is currently loaded');
      }

      // 检查文件是否已存在（通过 orgPath）
      final existingFile =
          await _dbOperater.getFileByOrgPath(workspaceId, file.orgPath);
      if (existingFile != null) {
        PGLog.w(
            'File with orgPath ${file.orgPath} already exists in workspace $workspaceId');
        // 返回当前缓存的文件列表，不添加重复文件
        return List.unmodifiable(_fileCache.values);
      }

      // 先更新缓存
      _fileCache[file.fileId] = file;

      // 异步更新数据库
      try {
        await _dbOperater.insertFile(file.toEntity());
      } catch (e) {
        PGLog.e('Failed to add file to database: $e');
        unawaited(_rollbackFileCache(file.fileId, null));
      }
      return List.unmodifiable(_fileCache.values);
    });
  }

  /// 批量添加文件
  Future<List<WorkspaceFile>> batchAddFiles(List<WorkspaceFile> files) async {
    return await _cacheLock.synchronized(() async {
      final workspaceId = _currentWorkspaceId;
      if (workspaceId == null) {
        throw StateError('No workspace is currently loaded');
      }

      // 检查重复文件
      final orgPaths = files.map((file) => file.orgPath).toList();
      final existingFiles =
          await _dbOperater.getFilesByOrgPaths(workspaceId, orgPaths);

      // 创建已存在文件的 orgPath 集合，用于快速查找
      final existingOrgPaths = <String>{};
      for (final existingFile in existingFiles) {
        existingOrgPaths.add(existingFile.orgPath);
      }

      // 过滤出不重复的文件
      final nonDuplicateFiles = <WorkspaceFile>[];
      final duplicateFiles = <WorkspaceFile>[];

      for (final file in files) {
        if (existingOrgPaths.contains(file.orgPath)) {
          duplicateFiles.add(file);
          PGLog.w(
              'File with orgPath ${file.orgPath} already exists in workspace $workspaceId');
        } else {
          nonDuplicateFiles.add(file);
        }
      }

      if (duplicateFiles.isNotEmpty) {
        PGLog.w(
            'Skipping ${duplicateFiles.length} duplicate files in batch add');
      }

      if (nonDuplicateFiles.isEmpty) {
        // 所有文件都是重复的，直接返回当前缓存
        return List.unmodifiable(_fileCache.values);
      }

      // 更新缓存
      final pres = <String, WorkspaceFile>{};
      for (final file in nonDuplicateFiles) {
        final pre = _fileCache[file.fileId];
        if (pre != null) {
          pres[file.fileId] = pre;
        }
        _fileCache[file.fileId] = file;
      }

      // 异步批量更新数据库
      try {
        _dbOperater.insertFiles(
          nonDuplicateFiles.map((file) => file.toEntity()).toList(),
        );
      } catch (e) {
        PGLog.e('Failed to batch add files: $e');
        unawaited(_rollbackBatchFileCache(pres));
      }

      return List.unmodifiable(_fileCache.values);
    });
  }

  /// 更新文件
  Future<List<WorkspaceFile>> updateFile(WorkspaceFile file) async {
    return await _cacheLock.synchronized(() async {
      final workspaceId = _currentWorkspaceId;
      if (workspaceId == null) {
        throw StateError('No workspace is currently loaded');
      }

      final pre = _fileCache[file.fileId];

      // 更新缓存
      _fileCache[file.fileId] = file;

      try {
        _dbOperater.updateFile(file.toEntity());
      } catch (e) {
        PGLog.e('Failed to update file: $e');
        unawaited(_rollbackFileCache(file.fileId, pre));
      }

      return List.unmodifiable(_fileCache.values);
    });
  }

  /// 批量更新文件
  Future<List<WorkspaceFile>> batchUpdateFiles(
      List<WorkspaceFile> files) async {
    return await _cacheLock.synchronized(() async {
      final workspaceId = _currentWorkspaceId;
      if (workspaceId == null) {
        throw StateError('No workspace is currently loaded');
      }

      // 更新缓存
      final pres = <String, WorkspaceFile>{};
      for (final file in files) {
        final pre = _fileCache[file.fileId];
        if (pre != null) {
          pres[file.fileId] = pre;
        }
        _fileCache[file.fileId] = file;
      }

      // 异步更新数据库
      try {
        _dbOperater.updateFiles(
          files.map((file) => file.toEntity()).toList(),
        );
      } catch (e) {
        PGLog.e('Failed to batch update files: $e');
        unawaited(_rollbackBatchFileCache(pres));
      }

      return List.unmodifiable(_fileCache.values);
    });
  }

  /// 删除文件
  Future<List<WorkspaceFile>> deleteFile(String fileId) async {
    return await _cacheLock.synchronized(() async {
      final workspaceId = _currentWorkspaceId;
      if (workspaceId == null) {
        throw StateError('No workspace is currently loaded');
      }

      final pre = _fileCache[fileId];

      // 更新缓存
      _fileCache.remove(fileId);

      // 异步更新数据库
      try {
        _dbOperater.deleteFile(workspaceId, fileId);
      } catch (e) {
        PGLog.e('Failed to delete file: $e');
        unawaited(_rollbackFileCache(fileId, pre));
      }

      return List.unmodifiable(_fileCache.values);
    });
  }

  /// 批量删除文件
  Future<List<WorkspaceFile>> batchDeleteFiles(List<String> fileIds) async {
    return await _cacheLock.synchronized(() async {
      final workspaceId = _currentWorkspaceId;
      if (workspaceId == null) {
        throw StateError('No workspace is currently loaded');
      }

      final pres = <String, WorkspaceFile>{};
      for (final fileId in fileIds) {
        final pre = _fileCache[fileId];
        if (pre != null) {
          pres[fileId] = pre;
        }
        _fileCache.remove(fileId);
      }

      try {
        _dbOperater.deleteFiles(workspaceId, fileIds);
      } catch (e) {
        PGLog.e('Failed to batch delete files: $e');
        unawaited(_rollbackBatchFileCache(pres));
      }

      return List.unmodifiable(_fileCache.values);
    });
  }
}

/// 工作区编辑仓储实现
class CacheFirstEditingProjectRepositoryImpl
    implements CurrentEditingProjectRepository {
  final DbOperater _dbOperater;
  final WorkspaceFilesCacheManager _fileCache;
  final StreamController<Workspace> _workspaceUpdated =
      StreamController<Workspace>.broadcast();
  final Lock _workspaceLock = Lock();
  Workspace? _workspaceCache;

  @override
  Stream<Workspace> get workspaceUpdated => _workspaceUpdated.stream;

  @override
  Workspace? get currentWorkspace => _workspaceCache;

  @override
  bool get isWorkspaceLoaded => _workspaceCache != null;

  String? get _currentWorkspaceId => _workspaceCache?.workspaceId;

  /// 私有setter方法，用于设置workspace缓存并通知监听者
  void _setWorkspaceCache(Workspace? workspace) {
    _workspaceCache = workspace;
    if (workspace != null) {
      _workspaceUpdated.add(workspace);
    }
  }

  CacheFirstEditingProjectRepositoryImpl(this._dbOperater)
      : _fileCache = WorkspaceFilesCacheManager(_dbOperater);

  @override
  Future<Workspace?> switchWorkspace(String workspaceId) async {
    if (workspaceId.isEmpty) {
      PGLog.e('Workspace ID cannot be empty');
      return null;
    }

    return await _workspaceLock.synchronized(() async {
      if (isWorkspaceLoaded) {
        await exitWorkspace();
      }

      try {
        // 在 compute 中初始化数据库
        final workspaceEntity = await _dbOperater.getWorkspaceById(workspaceId);

        if (workspaceEntity == null) {
          throw Exception('Workspace $workspaceId not found in database');
        }

        final files = await _fileCache.loadWorkspace(workspaceId);
        final newWorkspace = Workspace.fromEntity(workspaceEntity, files);
        _setWorkspaceCache(newWorkspace);
        return newWorkspace;
      } catch (e) {
        PGLog.e('Error switching to workspace $workspaceId: $e');
        return null;
      }
    });
  }

  @override
  Future<void> exitWorkspace() async {
    await _workspaceLock.synchronized(() async {
      if (!isWorkspaceLoaded) {
        return;
      }

      try {
        _setWorkspaceCache(null);
        await _fileCache.clear();
      } catch (e) {
        throw Exception('Error exiting workspace: $e');
      }
    });
  }

  @override
  Future<void> updateWorkspace(Workspace workspace) async {
    await _workspaceLock.synchronized(() async {
      if (workspace.workspaceId != _currentWorkspaceId) {
        throw Exception('No workspace is currently loaded');
      }

      final pre = _workspaceCache;
      _setWorkspaceCache(workspace);

      try {
        _dbOperater.updateWorkspaceAndFiles(
          workspace.toEntity(),
          workspace.files.map((f) => f.toEntity()).toList(),
        );
      } catch (e) {
        PGLog.e('Error updating workspace: $e');
        _setWorkspaceCache(pre);
      }
    });
  }

  @override
  Future<List<WorkspaceFile>> getWorkspaceFiles() async {
    if (!isWorkspaceLoaded) {
      throw Exception('No workspace is currently loaded');
    }
    return await _fileCache.getAllFiles();
  }

  @override
  Future<void> addFile(WorkspaceFile file) async {
    await _workspaceLock.synchronized(() async {
      final currentWorkspace = _workspaceCache;
      if (currentWorkspace == null) {
        throw Exception('No workspace is currently loaded');
      }

      final newFiles = await _fileCache.addFile(file);
      final lastEditTime = DateTime.now().millisecondsSinceEpoch;

      final newWorkspace = currentWorkspace.copyWith(
        files: newFiles,
        lastEditTime: lastEditTime,
      );
      _setWorkspaceCache(newWorkspace);

      try {
        _dbOperater.updateWorkspaceAndFiles(
          newWorkspace.toEntity(),
          newFiles.map((f) => f.toEntity()).toList(),
        );
      } catch (e) {
        PGLog.e('Error adding file: $e');
        _setWorkspaceCache(currentWorkspace);
      }
    });
  }

  @override
  Future<void> updateFile(WorkspaceFile file) async {
    await _workspaceLock.synchronized(() async {
      final currentWorkspace = _workspaceCache;
      if (currentWorkspace == null) {
        throw Exception('No workspace is currently loaded');
      }

      final newFiles = await _fileCache.updateFile(file);
      final lastEditTime = DateTime.now().millisecondsSinceEpoch;

      final newWorkspace = currentWorkspace.copyWith(
        files: newFiles,
        lastEditTime: lastEditTime,
      );
      _setWorkspaceCache(newWorkspace);

      try {
        _dbOperater.updateWorkspaceAndFiles(
          newWorkspace.toEntity(),
          [file.toEntity()],
        );
      } catch (e) {
        PGLog.e('Error updating file: $e');
        _setWorkspaceCache(currentWorkspace);
      }
    });
  }

  /// 替换文件接口，因为涉及到一次删除一次新增，避免多次数据抖动更新一次故形成一个接口
  @override
  Future<void> replaceFile(String fileId, WorkspaceFile file) async {
    await _workspaceLock.synchronized(() async {
      final currentWorkspace = _workspaceCache;
      if (currentWorkspace == null) {
        throw Exception('No workspace is currently loaded');
      }

      await _fileCache.deleteFile(fileId);
      final newFiles = await _fileCache.addFile(file);
      final lastEditTime = DateTime.now().millisecondsSinceEpoch;

      final newWorkspace = currentWorkspace.copyWith(
        files: newFiles,
        lastEditTime: lastEditTime,
      );
      _setWorkspaceCache(newWorkspace);

      try {
        _dbOperater.updateWorkspace(
          newWorkspace.toEntity(),
        );
      } catch (e) {
        PGLog.e('Error replacing file: $e');
        _setWorkspaceCache(currentWorkspace);
      }
    });
  }

  @override
  Future<void> deleteFile(String fileId) async {
    await _workspaceLock.synchronized(() async {
      final currentWorkspace = _workspaceCache;
      if (currentWorkspace == null) {
        throw Exception('No workspace is currently loaded');
      }

      final newFiles = await _fileCache.deleteFile(fileId);
      final lastEditTime = DateTime.now().millisecondsSinceEpoch;

      final newWorkspace = currentWorkspace.copyWith(
        files: newFiles,
        lastEditTime: lastEditTime,
      );
      _setWorkspaceCache(newWorkspace);

      try {
        _dbOperater.deleteWorkspaceAndFiles(
          currentWorkspace.toEntity(),
          [fileId],
        );
      } catch (e) {
        PGLog.e('Error deleting file: $e');
        _setWorkspaceCache(currentWorkspace);
      }
    });
  }

  @override
  Future<WorkspaceFile?> getFile(String fileId) async {
    if (!isWorkspaceLoaded) {
      throw Exception('No workspace is currently loaded');
    }
    return await _fileCache.getFile(fileId);
  }

  @override
  Future<bool> isFileExistedInWorkspace(String path) async {
    final workspaceId = _currentWorkspaceId;
    if (workspaceId == null) {
      return false;
    }

    // 检查文件是否已存在（通过 orgPath）
    final existingFile = await _dbOperater.getFileByOrgPath(workspaceId, path);
    if (existingFile != null) {
      PGLog.w(
          'File with orgPath $path already exists in workspace $workspaceId');
      // 返回当前缓存的文件列表，不添加重复文件
      return true;
    }
    return false;
  }

  @override
  Future<void> batchAddFiles(List<WorkspaceFile> files) async {
    await _workspaceLock.synchronized(() async {
      final currentWorkspace = _workspaceCache;
      if (currentWorkspace == null) {
        throw Exception('No workspace is currently loaded');
      }

      final newFiles = await _fileCache.batchAddFiles(files);
      final lastEditTime = DateTime.now().millisecondsSinceEpoch;

      final newWorkspace = currentWorkspace.copyWith(
        files: newFiles,
        lastEditTime: lastEditTime,
      );
      _setWorkspaceCache(newWorkspace);

      try {
        _dbOperater.updateWorkspaceAndFiles(
          newWorkspace.toEntity(),
          newFiles.map((f) => f.toEntity()).toList(),
        );
      } catch (e) {
        PGLog.e('Error updating workspace: $e');
        _setWorkspaceCache(currentWorkspace);
      }
    });
  }

  @override
  Future<void> batchUpdateFiles(List<WorkspaceFile> files) async {
    await _workspaceLock.synchronized(() async {
      final currentWorkspace = _workspaceCache;
      if (currentWorkspace == null) {
        throw Exception('No workspace is currently loaded');
      }

      final newFiles = await _fileCache.batchUpdateFiles(files);
      final lastEditTime = DateTime.now().millisecondsSinceEpoch;

      final newWorkspace = currentWorkspace.copyWith(
        files: newFiles,
        lastEditTime: lastEditTime,
      );
      _setWorkspaceCache(newWorkspace);

      try {
        _dbOperater.updateWorkspaceAndFiles(
          newWorkspace.toEntity(),
          newFiles.map((f) => f.toEntity()).toList(),
        );
      } catch (e) {
        PGLog.e('Error updating workspace: $e');
        _setWorkspaceCache(currentWorkspace);
      }
    });
  }

  @override
  Future<void> batchDeleteFiles(List<String> fileIds) async {
    await _workspaceLock.synchronized(() async {
      final currentWorkspace = _workspaceCache;
      if (currentWorkspace == null) {
        throw Exception('No workspace is currently loaded');
      }

      final newFiles = await _fileCache.batchDeleteFiles(fileIds);
      final lastEditTime = DateTime.now().millisecondsSinceEpoch;

      final newWorkspace = currentWorkspace.copyWith(
        files: newFiles,
        lastEditTime: lastEditTime,
      );
      _setWorkspaceCache(newWorkspace);

      try {
        _dbOperater.deleteWorkspaceAndFiles(
          currentWorkspace.toEntity(),
          fileIds,
        );
      } catch (e) {
        PGLog.e('Error deleting files: $e');
        _setWorkspaceCache(currentWorkspace);
      }
    });
  }
}
