import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'export_records_repository.dart';

class ExportRecordsRepositoryImpl2 implements ExportRecordsRepository {
  ExportRecordsRepositoryImpl2();

  @override
  Future<List<ExportRecord>> fetchExportRecord(
      {bool forceRefresh = false}) async {
    try {
      final filePath = _getTargetFilePath();
      if (filePath == null) {
        return [];
      }

      final file = File(filePath);
      if (!file.existsSync()) {
        return [];
      }

      final content = await file.readAsString();
      if (content.isEmpty) {
        return [];
      }

      return _parseJson(content);
    } on FileSystemException catch (fileError) {
      PGLog.e('文件系统异常: ${fileError.message}');
      return [];
    } catch (e) {
      PGLog.e('异常: $e');
      return [];
    }
  }

  // 新增目标文件路径获取
  String? _getTargetFilePath() {
    final userDir = FileManager().getUserRootDir();
    if (userDir == null) {
      return null;
    }
    return path.join(userDir.path, 'export', 'exportTasks.json');
  }

  List<ExportRecord> _parseJson(String jsonStr) {
    try {
      final List<dynamic> jsonList = jsonDecode(jsonStr) as List;
      return jsonList
          .map((json) {
            try {
              return ExportRecord.fromJson(json);
            } catch (e) {
              PGLog.e('解析单条记录异常: $e, 数据: ${json.toString()}');
              return null;
            }
          })
          .whereType<ExportRecord>()
          .toList();
    } catch (e) {
      PGLog.e('解析JSON异常: $e');
      return [];
    }
  }

  @override
  Future<void> deleteExportRecord(String guid) async {
    // TODO: 修改Json中的记录实现删除导出记录
  }

  @override
  Future<void> updateExportRecord(ExportRecord record) async {
    // TODO: 修改Json中的记录实现更新导出记录
  }

  @override
  Future<List<ExportRecord>> fetchExportRecordsByGuids(
    List<String> guids,
  ) async {
    // TODO: 解析Json中的记录实现获得导出记录
    return [];
  }

  @override
  Future<List<String>> fetchExportTaskFilesFinalPath(
      String exportTaskId) async {
    // TODO: 解析Json中的记录实现获得导出任务的所有目标地址
    return [];
  }
}
