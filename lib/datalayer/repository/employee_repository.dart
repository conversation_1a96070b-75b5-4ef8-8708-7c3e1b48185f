import 'dart:async';

import 'package:turing_art/datalayer/domain/models/api_error/api_error.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_list.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_summary.dart';

/// 子账号信息模型，用于添加子账号
class EmployeeInfo {
  final String nickname;
  final String mobile;
  // 手机区号（例如86 中国大陆的国际电话区号）
  final int cc;

  EmployeeInfo({
    required this.nickname,
    required this.mobile,
    required this.cc,
  });

  Map<String, dynamic> toJson() => {
        'nickname': nickname,
        'mobile': mobile,
        'cc': cc,
      };
}

abstract class EmployeeRepository {
  static String mobileExistMessage = '添加的手机号已存在';

  /// 子账号信息变更事件流
  Stream<String> get employeeInfoChange;

  /// 获取子账号汇总信息
  EmployeeSummary? get usedSummary;

  /// 获取子账号列表
  EmployeeList? get subAcountList;

  /// 获取子账号汇总信息(异步,不等待)
  Future<void> getEmployeeSummary();

  /// 获取子账号列表(异步,不等待)
  Future<void> getEmployeeList();

  /// 添加子账号（支持单个或批量）
  Future<ApiError?> addEmployees({
    required String code,
    required List<EmployeeInfo> employees,
  });

  /// 发送验证码
  void sendVerificationCode();

  /// 更新子账号
  Future<bool> updateEmployee({
    required String id,
    String? nickname,
    required bool enable,
  });
}
