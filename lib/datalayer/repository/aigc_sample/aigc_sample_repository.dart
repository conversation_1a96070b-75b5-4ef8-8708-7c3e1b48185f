import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_status_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_list_export_request.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_request.dart';

abstract class AigcSampleRepository {
  /// 监听数据变更流
  Stream<String> get dataChangeStream;

  /// 创建打样
  Future<AigcSampleModel> createAigcSample(AigcSampleRequest request);

  /// 重新打样
  Future<void> regenerateAigcSample(String proofingId);

  /// 获取打样详情（切换了详情调用此方法获取服务器最新数据）
  Future<AigcSampleModel> getAigcSampleDetail(String proofingId);

  /// 打样详情（停留在某个详情，此详情需要轮询，且轮询后收到数据变更通知需要更新UI，以免重复请求一次）
  AigcSampleModel? getLocalCacheSampleDetail(String proofingId);

  /// 获取打样列表
  Future<List<AigcSampleModel>> getAigcSampleList(String projectId);

  /// 获取打样导出列表
  Future<List<AigcSampleExportProjectModel>> getAigcSampleExportList();

  /// 批量删除打样导出(打样项目id,效果code)
  Future<void> deleteAigcSampleExport(
      List<AigcSampleListExportRequest> request);

  /// 打样生成导出
  Future<void> exportAigcSample(String proofingId, String effectCode);

  /// 删除打样
  Future<void> deleteAigcSample(String proofingId);

  /// 删除打样效果图
  Future<void> deleteAigcSampleEffect(String proofingId, String effectCode);

  /// 轮询打样状态(需要回传项目ID，以免上层已经切换了项目)
  Future<List<AigcPcPresetsLoopModel>> getAigcSampleProofingStatus(
      List<String> ids,
      [String? projectId]);

  /// 获取本地打样状态列表(用于判断是否变更需要刷新列表)
  List<AigcPcPresetsLoopModel> getLocalAigcSampleProofingStatusList(
      String projectId);

  /// 轮询打样导出状态(需要回传项目ID，以免上层已经切换了项目)
  Future<List<AigcSampleExportStatusModel>> getAigcSampleExportStatus(
      List<AigcSampleListExportRequest> requests,
      {bool isMyExportList = false});

  /// 获取本地打样导出状态列表(用于判断是否变更需要刷新列表)
  List<AigcSampleExportStatusModel> getLocalAigcSampleExportStatusList(
      String? projectId);

  /// 获取本地项目列表
  Future<List<AigcSampleProjectModel>> getAigcSampleProjectList();

  /// 更新打样信息
  Future<void> updateAigcSampleInfo(String proofingId, String imageUrl);

  /// 打样导出下载状态上报
  Future<void> updateAigcSampleDownloadStatus(
      List<AigcSampleListExportRequest> requests);
}
