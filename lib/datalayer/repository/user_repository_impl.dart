import 'package:turing_art/datalayer/service/database/dao/user_dao.dart';

import '../domain/models/user/user.dart';
import '../service/database/database.dart';
import 'user_repository.dart';

class UserRepositoryImpl extends UserRepository {
  final DataBase db;

  UserRepositoryImpl({
    required this.db,
  });

  // 使用 LRU 缓存存储用户信息，设置缓存大小为 10
  final Map<String, User> _cache = <String, User>{};

  @override
  Future<List<User>> getAllUser() async {
    // 如果缓存不为空，直接返回缓存数据
    if (_cache.isNotEmpty) {
      return _cache.values.toList();
    }

    // 从数据库查询并更新缓存
    final userEntities = await db.getAllUsers();
    final users = userEntities.map((entity) => User.fromEntity(entity));

    // 更新缓存
    final usersMap = {for (var user in users) user.id: user};
    _cache.addAll(usersMap);

    return users.toList();
  }

  @override
  Future<User?> getUser(String id) async {
    // 先检查缓存
    final cachedUser = _cache[id];
    if (cachedUser != null) {
      return cachedUser;
    }

    // 从数据库查询用户
    final userEntity = await db.getUserById(id);
    if (userEntity == null) {
      return null;
    }

    // 构造用户对象
    final user = User.fromEntity(userEntity);

    // 更新缓存
    _cache[user.id] = user;

    return user;
  }

  @override
  Future<void> addUser(User user) async {
    // 插入数据库
    await db.upsertUser(user.toEntity());
    // 更新缓存
    _cache[user.id] = user;
  }

  @override
  Future<void> updateUser(User user) async {
    // 更新数据库
    await db.upsertUser(user.toEntity());
    // 更新缓存
    _cache[user.id] = user;
  }
}
