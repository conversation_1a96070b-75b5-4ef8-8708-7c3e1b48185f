import 'dart:convert';
import 'dart:io';

import '../domain/models/preset/preset.dart';
import '../domain/models/preset/preset_item.dart';
import 'preset_repositoy.dart';

class PresetRepositoryImpl implements PresetRepository {
  final String filePath;
  final List<Preset> _cachedPresets;

  PresetRepositoryImpl._({
    required this.filePath,
    required List<Preset> presets,
  }) : _cachedPresets = presets;

  static Future<PresetRepositoryImpl> create({
    required String filePath,
  }) async {
    String jsonString;
    final file = File(filePath);
    if (file.existsSync()) {
      jsonString = await file.readAsString();
    } else {
      throw FileSystemException('文件不存在', filePath);
    }

    final presets = _parsePresetsFromJson(jsonString);
    return PresetRepositoryImpl._(
      filePath: filePath,
      presets: presets,
    );
  }

  static List<Preset> _parsePresetsFromJson(String jsonString) {
    final List<dynamic> jsonList = json.decode(jsonString) as List<dynamic>;
    final List<PresetItem> allItems = jsonList
        .map((item) => PresetItem.fromJson(item as Map<String, dynamic>))
        .toList();

    final Map<String, List<PresetItem>> groupedItems = {};
    for (final item in allItems) {
      final category = item.presetCategory;
      groupedItems.putIfAbsent(category, () => []).add(item);
    }

    return groupedItems.entries
        .map((entry) => Preset(
              category: entry.key,
              items: entry.value,
            ))
        .toList();
  }

  @override
  List<Preset> getPresets() {
    return _cachedPresets;
  }

  @override
  Preset? getPresetByCategory(String category) {
    if (_cachedPresets.isEmpty) {
      return null;
    }
    return _cachedPresets.firstWhere(
      (preset) => preset.category == category,
      orElse: () => Preset(category: category, items: []),
    );
  }
}
