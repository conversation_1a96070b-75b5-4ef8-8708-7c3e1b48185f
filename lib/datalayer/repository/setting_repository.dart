import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';

class CacheCleanSettingResult {
  final int maxDays; // 缓存天数
  final int maxSize; // 自动清理缓存大小
  final String cachePath; // 缓存路径
  const CacheCleanSettingResult(
      {required this.maxDays, required this.maxSize, required this.cachePath});
}

class ExportFileSettingResult {
  final bool exportToOriginalFolder; // 是否导出至原图文件夹相同路径
  final String exportFolderSuffix; // 导出文件夹后缀名
  const ExportFileSettingResult(
      {required this.exportToOriginalFolder, required this.exportFolderSuffix});
}

abstract class SettingRepository {
  /// 加载设置配置
  /// 如果本地不存在配置文件，则从应用资源中加载默认配置并保存到本地
  Future<SettingConfig> loadSettingConfig();

  /// 保存设置配置到本地
  Future<void> saveSettingConfig(SettingConfig config);

  /// 获取导出配置
  Future<ExportConfig> getExportConfig(ExportConfig exportConfig);

  /// 获取项目分类配置
  Future<ProjectCreationMode> getProjectCategoryConfig();

  /// 获取导出文件设置
  Future<ExportFileSettingResult> getExportFileSetting();

  /// 获取缓存设置(缓存天数，缓存大小)
  Future<CacheCleanSettingResult> getCacheCleanSetting();

  /// 获取触控板模式开关状态
  Future<bool> getTouchpadModeEnabled();
}
