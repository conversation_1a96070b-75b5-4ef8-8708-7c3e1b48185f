import 'dart:async';

import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/project_db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/workspace_db_operater.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'project_repository.dart';
import 'update_event.dart';

class ProjectRepositoryImpl extends ProjectRepository {
  final Map<String, ProjectInfo> _cache = <String, ProjectInfo>{};

  final DbOperater _dbOperater;

  // 新增事件流控制器
  final _streamController = StreamController<UpdateEvent<String>>.broadcast();

  ProjectRepositoryImpl(this._dbOperater);

  // 新增对外暴露的Stream
  @override
  Stream<UpdateEvent<String>> get dataUpdates => _streamController.stream;

  // 获取所有项目
  @override
  Future<List<ProjectInfo>> getAllProjects() async {
    // 这里需要从数据库中整体同步一次数据，避免缓存数据不一致
    _cache.clear();
    return _getProjectsFromDatabase();
  }

  @override
  Future<List<ProjectInfo>> getUserAllProjects(String userId) {
    _cache.clear();
    return _getProjectsFromDatabase(userId: userId);
  }

  @override
  Future<List<ProjectInfo>> getUserAllProjectsByType(
    String userId,
    ProjectType projectType,
  ) {
    _cache.clear();
    return _getProjectsFromDatabase(userId: userId, projectType: projectType);
  }

  @override
  Future<List<ProjectInfo>> getDeletedProjects() {
    return _getProjectsFromDatabase(isDelete: true);
  }

  @override
  Future<ProjectInfo?> getProjectById(String projectId) async {
    final cachedProject = _cache[projectId];
    if (cachedProject != null) {
      return cachedProject;
    }

    try {
      final ProjectInfo? project;

      final projectEntity = await _dbOperater.getProjectById(projectId);
      if (projectEntity == null) {
        project = null;
      } else {
        final files = await _dbOperater.getFilesByWorkspaceIdWithDefalutSort(
          projectId,
        );
        project = ProjectInfo.fromEntities(projectEntity, files);
      }

      if (project != null) {
        _cache[projectId] = project;
      }
      return project;
    } catch (e) {
      PGLog.e('Failed to get project by id: $e');
      return null;
    }
  }

  @override
  Future<void> addProject(ProjectInfo info) async {
    _cache[info.uuid] = info;
    try {
      // 插入项目
      final workspace = Workspace(
        files: [],
        workspaceId: info.uuid,
        workspaceName: info.name,
        currentFileId: "",
        createTime: info.createdDate?.millisecondsSinceEpoch ?? 0,
        lastEditTime: info.updateDate?.millisecondsSinceEpoch ?? 0,
      );

      await _dbOperater.insertProjectWithWorkspace(
        info.toEntity(),
        workspace.toEntity(),
        [],
      );

      // 通知
      _streamController.add(
        UpdateEvent(
          type: UpdateType.created,
          updated: info.uuid,
        ),
      );
    } catch (e) {
      _cache.remove(info.uuid);
      PGLog.e('Failed to add project: $e');
    }
  }

  @override
  Future<void> updateProject(ProjectInfo info) async {
    // 更新数据库前缓存一下更新前信息，防止db发生写入错误需要回滚
    final preProjectInfo = _cache[info.uuid];
    // 先更新缓存，保证拿数据效率
    _cache[info.uuid] = info;

    try {
      // 异步更新项目
      _dbOperater.updateProject(info.toEntity());
    } catch (e) {
      // 如果有旧信息，则恢复到缓存中
      if (preProjectInfo != null) {
        _cache[info.uuid] = preProjectInfo;
      } else {
        // 如果连旧信息都没有，则清除缓存，保证下次拿取数据和db是同步的
        _cache.remove(info.uuid);
      }
      PGLog.e('Failed to add project: $e');
    }

    // 通知
    _streamController.add(
      UpdateEvent(
        type: UpdateType.created,
        updated: info.uuid,
      ),
    );
  }

  @override
  Future<void> updateWorkspace(Workspace workspace) async {
    await _updateWorkspace(workspace, updateFiles: true);
  }

  @override
  Future<void> updateWorkspaceWithoutFiles(Workspace workspace) async {
    await _updateWorkspace(workspace, updateFiles: false);
  }

  @override
  Future<void> deleteProject(String projectId) async {
    // 防止db发生写入错误需要回滚
    final preProjectInfo = _cache[projectId];
    // 先删除缓存，保证拿数据效率
    _cache.remove(projectId);

    try {
      // 异步删除项目
      _dbOperater.deleteProject(projectId);
    } catch (e) {
      // 如果有旧信息，则恢复到缓存中
      if (preProjectInfo != null) {
        _cache[projectId] = preProjectInfo;
      } else {
        // 如果连旧信息都没有，则清除缓存，保证下次拿取数据和db是同步的
        _cache.remove(projectId);
      }
    }

    // 通知
    _streamController.add(
      UpdateEvent(
        type: UpdateType.deleted,
        updated: projectId,
      ),
    );
  }

  @override
  Future<void> deleteProjects(List<String> projectIds) async {
    if (projectIds.isEmpty) {
      return;
    }

    // 防止db发生写入错误需要回滚
    final preProjectInfos = <String, ProjectInfo>{};
    for (final projectId in projectIds) {
      final preProjectInfo = _cache[projectId];
      if (preProjectInfo != null) {
        preProjectInfos[projectId] = preProjectInfo;
      }
    }

    // 先删除缓存，保证拿数据效率
    for (final projectId in projectIds) {
      _cache.remove(projectId);
    }

    try {
      // 异步删除项目
      _dbOperater.deleteProjects(projectIds);
    } catch (e) {
      // 如果有旧信息，则恢复到缓存中
      for (final entry in preProjectInfos.entries) {
        _cache[entry.key] = entry.value;
      }
      PGLog.e('Failed to delete projects: $e');
      rethrow;
    }

    // 通知每个被删除的项目
    _streamController.add(
      UpdateEvent(
        type: UpdateType.deleted,
        updated: projectIds.join(','),
      ),
    );
  }

  @override
  Future<void> markProjectAsDeleted(String projectId) async {
    final project = await getProjectById(projectId);
    if (project == null) {
      return;
    }
    final newProject = project.copyWith(isDelete: true);
    await updateProject(newProject);
  }

  @override
  Future<void> markProjectsAsDeleted(List<String> projectIds) async {
    for (final projectId in projectIds) {
      await markProjectAsDeleted(projectId);
    }
  }

  @override
  Future<Workspace?> getWorkspaceByProjectId(String projectId) async {
    try {
      final workspaceEntity = await _dbOperater.getWorkspaceById(projectId);
      if (workspaceEntity == null) {
        return null;
      }

      final files = await _getWorkspaceFilesAsync(projectId);
      return Workspace.fromEntity(workspaceEntity, files);
    } catch (e) {
      PGLog.e('Failed to get workspace by project id: $e');
      return null;
    }
  }

  @override
  Future<List<Workspace>> getWorkspacesByProjectIds(
      List<String> projectIds) async {
    try {
      final workspaceEntities = await _dbOperater.getWorkspaceByIds(projectIds);
      if (workspaceEntities.isEmpty) {
        return [];
      }

      final workspaces = <Workspace>[];
      for (final entity in workspaceEntities) {
        // 使用传统的逐个查询方式获取文件
        final files = await _getWorkspaceFilesAsync(entity.workspaceId);
        workspaces.add(Workspace.fromEntity(entity, files));
      }
      return workspaces;
    } catch (e) {
      PGLog.e('Failed to get workspaces by project ids: $e');
      return [];
    }
  }

  @override
  Future<void> renameProject(String projectId, String newName) async {
    final project = await getProjectById(projectId);
    // 更新工作区
    final workspace = await getWorkspaceByProjectId(projectId);
    if (project == null || workspace == null) {
      throw StateError('Project not found');
    }
    final newProject = project.copyWith(name: newName);
    // 更新数据库前缓存一下更新前信息，防止db发生写入错误需要回滚
    final preProjectInfo = _cache[projectId];
    // 先更新缓存，保证拿数据效率
    _cache[projectId] = newProject;

    final newWorkspace = workspace.copyWith(workspaceName: newName);

    try {
      await _dbOperater.updateProjectWithWorkspace(
        newProject.toEntity(),
        newWorkspace.toEntity(),
        [],
      );
    } catch (e) {
      // 如果有旧信息，则恢复到缓存中
      if (preProjectInfo != null) {
        _cache[projectId] = preProjectInfo;
      }
      PGLog.e('Failed to update project: $e');
    }

    // 通知
    _streamController.add(
      UpdateEvent(
        type: UpdateType.updated,
        updated: projectId,
      ),
    );
  }

  @override
  Future<List<WorkspaceFile>> getProjectFiles(String projectId) async {
    return await _getWorkspaceFilesAsync(projectId);
  }

  @override
  Future<ProjectInfo?> syncProject(String projectId) async {
    try {
      ProjectInfo? project;
      final projectEntity = await _dbOperater.getProjectById(projectId);
      if (projectEntity != null) {
        final fileEntities = await _dbOperater
            .getFilesByWorkspaceIdWithDefalutSort(projectEntity.projectId);

        project = ProjectInfo.fromEntities(
          projectEntity,
          fileEntities,
        );
      } else {
        project = null;
      }

      if (project != null) {
        _cache[project.uuid] = project;
      }

      // 通知
      _streamController.add(
        UpdateEvent(
          type: UpdateType.updated,
          updated: projectId,
        ),
      );

      return project;
    } catch (e) {
      // 发生错误时清除缓存，确保下次获取时从数据库重新加载
      _cache.remove(projectId);
      PGLog.e('Failed to sync project from database: $e');
      return null;
    }
  }

  Future<List<WorkspaceFile>> _getWorkspaceFilesAsync(
      String workspaceId) async {
    final fileEntities = await _dbOperater.getFilesByWorkspaceId(workspaceId);
    return fileEntities
        .map((entity) => WorkspaceFile.fromEntity(entity))
        .toList();
  }

  Future<void> _updateWorkspace(
    Workspace workspace, {
    bool updateFiles = true,
  }) async {
    try {
      final preProjectInfo = _cache[workspace.workspaceId] ??
          await getProjectById(workspace.workspaceId);
      if (preProjectInfo == null) {
        PGLog.e('Failed to update project: Project not found');
        return;
      }
      final newProjectInfo = preProjectInfo.copyWith(
        updateDate: DateTime.fromMillisecondsSinceEpoch(workspace.lastEditTime),
      );

      if (updateFiles) {
        // 获取当前工作区文件
        final existingFiles =
            await _getWorkspaceFilesAsync(workspace.workspaceId);

        final existingFileMap = {
          for (var file in existingFiles) file.fileId: file
        };

        final newFileMap = {
          for (var file in workspace.files) file.fileId: file
        };

        // 删除不存在的文件
        final deleteFileIds = <String>[];
        for (var fileId in existingFileMap.keys) {
          if (!newFileMap.containsKey(fileId)) {
            deleteFileIds.add(fileId);
          }
        }

        // 删除不需要的文件
        if (deleteFileIds.isNotEmpty) {
          await _dbOperater.deleteFiles(
            workspace.workspaceId,
            deleteFileIds,
          );
        }

        // 更新工作区
        await _dbOperater.updateProjectWithWorkspace(
          newProjectInfo.toEntity(),
          workspace.toEntity(),
          workspace.files.map((file) => file.toEntity()).toList(),
        );
      } else {
        await _dbOperater.updateProjectWithWorkspace(
          newProjectInfo.toEntity(),
          workspace.toEntity(),
          [],
        );
      }

      // 工作区
      syncProject(workspace.workspaceId);
    } catch (e) {
      PGLog.e('Failed to update project: $e');
    }
  }

  // 私有辅助方法，统一处理项目列表获取逻辑，实现 cache-first 策略
  Future<List<ProjectInfo>> _getProjectsList({
    String? userId,
    ProjectType? projectType,
    bool isDelete = false,
  }) async {
    // 获取所有缓存的项目
    final cachedProjects = _cache.values.toList();

    // 如果缓存不为空，尝试从缓存中筛选数据
    if (cachedProjects.isNotEmpty) {
      final filteredProjects = cachedProjects
          .where((p) => userId == null || p.author == userId)
          .where((p) => projectType == null || p.projectType == projectType)
          .where((p) => p.isDelete == isDelete)
          .toList()
        ..sort((a, b) =>
            b.updateDate?.compareTo(a.updateDate ?? DateTime(0)) ?? 0);

      // 如果筛选后有数据，返回缓存数据
      if (filteredProjects.isNotEmpty) {
        return filteredProjects;
      }
    }

    // 如果缓存为空或筛选后没有数据，从数据库获取
    return _getProjectsFromDatabase(userId: userId, projectType: projectType);
  }

  Future<List<ProjectInfo>> _getProjectsFromDatabase({
    String? userId,
    ProjectType? projectType,
    bool isDelete = false,
  }) async {
    try {
      // 执行数据库操作
      List<ProjectEntityData> projectEntities;

      if (userId != null) {
        if (projectType != null) {
          projectEntities = await _dbOperater.getUserAllProjectsByType(
            userId,
            projectType.value,
            isDelete: isDelete,
          );
        } else {
          projectEntities = await _dbOperater.getUserAllProjects(
            userId,
            isDelete: isDelete,
          );
        }
      } else {
        projectEntities = await _dbOperater.getAllProjects(
          isDelete: isDelete,
        );
      }

      final projects = <ProjectInfo>[];
      for (var project in projectEntities) {
        final files = await _dbOperater.getFilesByWorkspaceIdWithDefalutSort(
          project.projectId,
        );
        projects.add(ProjectInfo.fromEntities(project, files));
      }
      projects.sort(
          (a, b) => b.updateDate?.compareTo(a.updateDate ?? DateTime(0)) ?? 0);

      // 更新缓存
      for (var info in projects) {
        _cache[info.uuid] = info;
      }
      return projects;
    } catch (e) {
      PGLog.e('Failed to get all projects: $e');
      return [];
    }
  }
}
