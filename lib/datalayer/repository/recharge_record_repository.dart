import 'package:turing_art/datalayer/domain/models/recharge_record/recharge_record_model.dart';

/// 充值记录仓库接口
abstract class RechargeRecordRepository {
  /// 获取充值记录
  ///
  /// [page] 页码，默认为1
  /// [pageSize] 每页数量，默认为10
  /// [startTime] 开始时间筛选，可选
  /// [endTime] 结束时间筛选，可选
  Future<RechargeRecordModel?> fetchRechargeRecords({
    int page = 1,
    int pageSize = 10,
    String? bankType,
    String? startTime,
    String? endTime,
  });
}
