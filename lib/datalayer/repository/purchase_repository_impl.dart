import 'package:turing_art/datalayer/domain/enums/order_status.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/datalayer/domain/models/order_dependent/create_order_response.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan.dart';
import 'package:turing_art/datalayer/repository/purchase_repository.dart';
import 'package:turing_art/datalayer/service/purchase/purchase_default_data_service.dart';
import 'package:turing_art/datalayer/service/purchase/purchase_service.dart';
import 'package:turing_art/utils/pg_log.dart';

class PurchaseRepositoryImpl implements PurchaseRepository {
  final PurchaseService _purchaseService;
  final PurchaseDefaultDataService _defaultDataService;
  // 缓存购买套餐列表
  List<PurchasePlan>? _cachedPurchasePlans;
  // 缓存时间
  DateTime? _cacheTime;
  // 缓存有效期（秒）
  static const int _cacheValidDuration = 3600; // 1小时
  // 当前购买套餐
  PurchasePlan? _currentPurchasePlan;
  // 当前订单信息(轮询、UI绘制需要)
  CreateOrderResponse? _currentOrderInfo;

  // 缓存AIGC积分套餐列表
  List<PurchasePlan>? _cachedAigcPointPurchasePlans;
  // 缓存AIGC积分套餐时间
  DateTime? _cacheAigcPointTime;

  PurchaseRepositoryImpl(
    this._purchaseService,
    this._defaultDataService,
  );

  @override
  String? get currentQrUrl => _currentOrderInfo?.qr_url;

  @override
  String? get currentPurchasePrice =>
      '${_currentPurchasePlan?.price.currency} ${_currentPurchasePlan?.price.price}';

  @override
  String? get purchasePriceNum => _currentPurchasePlan?.price.price;

  @override
  String? get currentPurchasePlanName => _currentPurchasePlan?.name;

  @override
  String? get currentPurchasePlanId => _currentPurchasePlan?.id;

  @override
  Future<List<PurchasePlan>> loadPurchaseCards(
      {bool forceRefresh = false}) async {
    // 如果不强制刷新且缓存有效，则返回缓存数据
    if (!forceRefresh && _isCacheValid()) {
      PGLog.d('使用缓存的套餐数据，共 ${_cachedPurchasePlans!.length} 个套餐');
      return _cachedPurchasePlans!;
    }

    // 从服务加载数据
    final plans = await _purchaseService.loadPurchaseCards();

    // 如果从服务获取的数据为空，则使用默认数据
    final resultPlans =
        plans.isEmpty ? _defaultDataService.getDefaultPurchasePlans() : plans;

    // 更新缓存
    _cachedPurchasePlans = resultPlans;
    _cacheTime = DateTime.now();
    PGLog.d('成功加载并缓存 ${resultPlans.length} 个套餐');

    return resultPlans;
  }

  @override
  Future<List<PurchasePlan>> loadAigcPointPurchaseCards(
      {bool forceRefresh = false}) async {
    // 如果不强制刷新且缓存有效，则返回缓存数据
    if (!forceRefresh &&
        _isAigcPointCacheValid() &&
        _cachedAigcPointPurchasePlans != null &&
        _cachedAigcPointPurchasePlans!.isNotEmpty) {
      PGLog.d('使用缓存的AIGC积分套餐数据，共 ${_cachedAigcPointPurchasePlans!.length} 个套餐');
      return _cachedAigcPointPurchasePlans!;
    }

    // 从服务加载数据
    final plans = await _purchaseService.loadAigcPointPurchaseCards();

    // 如果从服务获取的数据为空，则使用默认数据（这里暂时没有默认数据，直接返回空列表）
    final resultPlans = plans;

    // 更新缓存
    _cachedAigcPointPurchasePlans = resultPlans;
    _cacheAigcPointTime = DateTime.now();
    PGLog.d('成功加载并缓存 ${resultPlans.length} 个AIGC积分套餐');

    return resultPlans;
  }

  /// 检查缓存是否有效
  bool _isCacheValid() {
    if (_cachedPurchasePlans == null || _cacheTime == null) {
      return false;
    }

    final now = DateTime.now();
    final difference = now.difference(_cacheTime!).inSeconds;
    return difference < _cacheValidDuration;
  }

  /// 检查AIGC积分套餐缓存是否有效
  bool _isAigcPointCacheValid() {
    if (_cachedAigcPointPurchasePlans == null || _cacheAigcPointTime == null) {
      return false;
    }

    final now = DateTime.now();
    final difference = now.difference(_cacheAigcPointTime!).inSeconds;
    return difference < _cacheValidDuration;
  }

  @override
  void clearCurrentPurchasePlan() {
    // 清理购买计划相关的所有信息
    _currentPurchasePlan = null;
    // 订单信息已经传达给订单状态管理者，可以清理
    _currentOrderInfo = null;
  }

  @override
  Future<String?> createOrder(
      String planId, PaymentChannel paymentChannel) async {
    PGLog.d('创建订单: 选择套餐id:$planId, 支付渠道:$paymentChannel');
    // 首先在普通套餐中查找
    bool isFind = false;
    if (_cachedPurchasePlans != null) {
      try {
        _currentPurchasePlan = _cachedPurchasePlans!.firstWhere(
          (plan) => plan.id == planId,
        );
        isFind = true;
      } catch (e) {
        PGLog.e('未在普通套餐中找到对应ID的套餐: $planId');
        _currentPurchasePlan = null;
      }
    }
    if (_cachedAigcPointPurchasePlans != null && !isFind) {
      try {
        _currentPurchasePlan = _cachedAigcPointPurchasePlans!.firstWhere(
          (plan) => plan.id == planId,
        );
      } catch (e) {
        // 两个列表中都找不到
        PGLog.e('未在AIGC积分套餐中找到对应ID的套餐: $planId');
        _currentPurchasePlan = null;
      }
    }

    if (_currentPurchasePlan == null) {
      PGLog.d('创建订单失败: 没有选择套餐');
      return null;
    }

    try {
      // 调用服务层创建订单（每次都调用创建，不再走缓存，以免服务端不知道切换了支付方式）
      final orderResponse = await _purchaseService.createOrder(paymentChannel,
          _currentPurchasePlan!.purchasePlan, _currentPurchasePlan!.id);

      if (orderResponse != null) {
        _currentOrderInfo = orderResponse;
        PGLog.d('创建订单: 创建成功, 订单id:${orderResponse.orderId}');
        PGLog.d('创建订单: 返回是否是支付宝:${orderResponse.wakeupParam != null}');
        return orderResponse.orderId;
      } else {
        PGLog.d('创建订单失败: 服务返回空数据');
        return null;
      }
    } catch (e) {
      PGLog.d('创建订单异常: $e');
      return null;
    }
  }

  @override
  Future<OrderStatus> checkCurrentOrderStatus(String orderId) async {
    // 验证必要参数（一般不会出现订单ID对不上的情况）
    if (_currentOrderInfo?.orderId != orderId) {
      PGLog.d(
          '检查订单状态失败: 本地订单ID对不上, 本地id:${_currentOrderInfo?.orderId} != 传入id:$orderId');
      return OrderStatus.unknown;
    }

    try {
      // 调用服务层检查订单状态
      final status =
          await _purchaseService.checkOrderStatus(_currentOrderInfo!.orderId);
      if (status == OrderStatus.closed) {
        // 如果订单已失效，则清除当前订单的所有支付渠道信息，等待上层决定啥时候刷新
        _currentOrderInfo = null;
      }
      return status;
    } catch (e) {
      PGLog.d('检查订单状态异常: $e');
      return OrderStatus.unknown;
    }
  }
}
