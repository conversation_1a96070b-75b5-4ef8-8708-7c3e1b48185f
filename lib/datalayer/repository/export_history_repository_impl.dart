import 'package:turing_art/datalayer/domain/models/aigc_export_history/aigc_export_history_model.dart';
import 'package:turing_art/datalayer/domain/models/export_history/export_history_model.dart';
import 'package:turing_art/datalayer/repository/export_history_repository.dart';
import 'package:turing_art/datalayer/service/export_history/export_history_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 导出交易历史记录仓库实现
class ExportHistoryRepositoryImpl implements ExportHistoryRepository {
  final ExportHistoryService _service;

  ExportHistoryRepositoryImpl(this._service);

  @override
  Future<ExportHistoryModel?> fetchExportHistory({
    int page = 1,
    int pageSize = 20,
    int? createdAfter,
    int? createdBefore,
    String? employeeIds,
    int? type,
  }) async {
    try {
      final result = await _service.fetchExportHistory(
        page: page,
        pageSize: pageSize,
        createdAfter: createdAfter,
        createdBefore: createdBefore,
        employeeIds: employeeIds,
        type: type,
      );
      return result;
    } catch (e) {
      PGLog.e('获取导出交易历史记录失败: $e');
      return null;
    }
  }

  @override
  Future<AIGCExportHistoryModel?> fetchAIGCExportHistory({
    int page = 1,
    int pageSize = 20,
    String? startTime,
    String? endTime,
  }) async {
    try {
      final result = await _service.fetchAIGCExportHistory(
        page: page,
        pageSize: pageSize,
        startTime: startTime,
        endTime: endTime,
      );
      return result;
    } catch (e) {
      PGLog.e('获取AIGC导出历史记录失败: $e');
      return null;
    }
  }
}
