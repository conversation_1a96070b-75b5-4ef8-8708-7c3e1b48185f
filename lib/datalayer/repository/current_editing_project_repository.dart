import 'package:turing_art/datalayer/domain/models/workspace/workspace.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';

/// 工作区编辑仓储接口
abstract class CurrentEditingProjectRepository {
  /// 当前工作区，未加载时为 null
  Workspace? get currentWorkspace;

  /// 当前工作区是否已加载
  bool get isWorkspaceLoaded;

  /// 切换工作区
  /// [workspaceId] 工作区ID
  Future<void> switchWorkspace(String workspaceId);

  /// 退出当前工作区
  Future<void> exitWorkspace();

  /// 更新当前工作区
  /// [workspace] 工作区数据
  Future<void> updateWorkspace(Workspace workspace);

  /// 获取工作区中的文件列表
  Future<List<WorkspaceFile>> getWorkspaceFiles();

  /// 添加文件到工作区
  /// [file] 文件信息
  Future<void> addFile(WorkspaceFile file);

  /// 批量添加文件到工作区
  /// [files] 文件列表
  Future<void> batchAddFiles(List<WorkspaceFile> files);

  /// 更新工作区中的文件
  /// [file] 文件信息
  Future<void> updateFile(WorkspaceFile file);

  /// 批量更新工作区中的文件
  /// [files] 文件列表
  Future<void> batchUpdateFiles(List<WorkspaceFile> files);

  /// 替换工作区中的文件
  /// [file] 文件信息
  Future<void> replaceFile(String fileId, WorkspaceFile file);

  /// 从工作区删除文件
  /// [fileId] 文件ID
  Future<void> deleteFile(String fileId);

  /// 批量从工作区删除文件
  /// [fileIds] 文件ID列表
  Future<void> batchDeleteFiles(List<String> fileIds);

  /// 获取工作区中的文件
  /// [fileId] 文件ID
  Future<WorkspaceFile?> getFile(String fileId);

  /// 检查指定路径下的文件是否已经存在于工作区
  /// [path] 文件路径
  Future<bool> isFileExistedInWorkspace(String path);

  /// 工作区更新通知流
  /// 当工作区发生增删改操作时，通过该流推送变更事件
  Stream<Workspace?> get workspaceUpdated;
}
