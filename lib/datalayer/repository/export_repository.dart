import 'package:turing_art/datalayer/domain/models/export/export_models.dart';

import 'export_exception.dart';

/// 照片导出仓库接口
///
/// 主要功能：
/// - 创建导出Token
/// - 导出成功上报
/// - 调整照片参数
/// - 管理Token缓存（数据库持久化）
abstract class ExportRepository {
  /// 获取导出Token，如果数据库中有有效的Token则直接返回，否则创建新的Token
  ///
  /// [userId] 用户ID
  /// [imagePHash] 图片哈希值
  /// [imageName] 图片名称
  /// [projectName] 项目名称
  /// [projectId] 项目ID
  /// [hostname] 主机名
  /// [exportType] 导出类型，默认为retouch
  ///
  /// 返回导出Token，失败抛出 [ExportException]
  Future<ExportToken> getExportToken(
    String userId,
    String imagePHash,
    String imageName,
    String projectName,
    String projectId,
    String hostname, {
    ExportType exportType = ExportType.retouch,
  });

  /// 创建导出Token
  ///
  /// [userId] 用户ID
  /// [images] 要导出的图片列表
  /// [projectName] 项目名称
  /// [projectId] 项目ID
  /// [hostname] 主机名
  /// [exportType] 导出类型，默认为retouch
  ///
  /// 返回导出Token信息，失败抛出 [ExportException]
  Future<ExportInitializeResponse?> createExportToken(
    String userId,
    List<ExportImage> images,
    String projectName,
    String projectId,
    String hostname, {
    ExportType exportType = ExportType.retouch,
  });

  /// 导出成功上报
  ///
  /// [images] 导出成功的图片列表
  /// [projectName] 项目名称
  /// [projectId] 项目ID
  /// [hostname] 主机名
  /// [exportType] 导出类型，默认为retouch
  ///
  /// 成功返回void，失败抛出 [ExportException]
  Future<void> reportExportSuccess(
    List<ExportCompleteImage> images,
    String projectName,
    String projectId,
    String hostname, {
    ExportType exportType = ExportType.retouch,
  });

  /// 调整照片参数
  ///
  /// [tokenId] 导出Token ID
  /// [imagePHash] 图片哈希值
  /// [fields] 参数字段列表
  ///
  /// 返回调整后的参数，失败抛出 [ExportException]
  Future<GenParamsResponse?> adjustPhotoParams(
    String tokenId,
    String imagePHash,
    List<ParamField> fields,
  );

  /// 清除指定图片的Token缓存
  ///
  /// [userId] 用户ID
  /// [imagePHash] 图片哈希值
  /// [exportType] 导出类型
  void clearTokenCache(String userId, String imagePHash, String exportType);

  /// 清除用户所有Token缓存
  ///
  /// [userId] 用户ID
  void clearUserTokenCache(String userId);

  /// 清除所有Token缓存
  void clearAllTokenCache();
}
