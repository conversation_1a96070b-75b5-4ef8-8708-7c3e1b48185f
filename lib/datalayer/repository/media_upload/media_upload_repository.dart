import 'dart:io';

import 'package:turing_art/datalayer/domain/models/media_upload/media_upload_result.dart';

/// 媒体上传仓库接口
abstract class MediaUploadRepository {
  Future<MediaUploadResult> uploadSingleFile(
    File file, {
    Function(double progress)? onProgress,
  });

  Future<List<MediaUploadResult>> uploadMultipleFiles(
    List<File> files, {
    Function(String fileName, double progress)? onFileProgress,
    Function(int completed, int total)? onOverallProgress,
  });
}
