import 'dart:io';

import 'package:turing_art/datalayer/domain/models/media_upload/media_upload_result.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/datalayer/service/media_upload/media_upload_service.dart';

/// 媒体上传管理实现类
class MediaUploadRepositoryImpl implements MediaUploadRepository {
  final MediaUploadService _uploadService;

  MediaUploadRepositoryImpl({
    required MediaUploadService uploadService,
  }) : _uploadService = uploadService;

  /// 上传单个文件
  /// [file] 要上传的文件
  /// [onProgress] 上传进度回调
  /// 返回上传结果，包含文件URL信息
  @override
  Future<MediaUploadResult> uploadSingleFile(
    File file, {
    Function(double progress)? onProgress,
  }) async {
    try {
      final result = await _uploadService.uploadMedia(
        file,
        onProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            onProgress(sent / total);
          }
        },
      );

      return MediaUploadResult(
        success: true,
        fileName: file.path.split('/').last,
        uploadInfo: result,
      );
    } catch (e) {
      return MediaUploadResult(
        success: false,
        fileName: file.path.split('/').last,
        error: e.toString(),
      );
    }
  }

  /// 批量上传文件
  /// [files] 要上传的文件列表
  /// [onFileProgress] 单个文件上传进度回调
  /// [onOverallProgress] 整体上传进度回调
  /// 返回所有文件的上传结果
  @override
  Future<List<MediaUploadResult>> uploadMultipleFiles(
    List<File> files, {
    Function(String fileName, double progress)? onFileProgress,
    Function(int completed, int total)? onOverallProgress,
  }) async {
    final results = <MediaUploadResult>[];
    int completedCount = 0;

    for (final file in files) {
      final fileName = file.path.split('/').last;

      final result = await uploadSingleFile(
        file,
        onProgress: (progress) {
          onFileProgress?.call(fileName, progress);
        },
      );

      results.add(result);
      completedCount++;
      onOverallProgress?.call(completedCount, files.length);
    }

    return results;
  }
}
