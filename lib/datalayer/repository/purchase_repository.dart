import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan.dart';

import '../domain/enums/order_status.dart';
import '../domain/enums/payment_channel.dart';

// 订单创建结果类（临时模拟流程）
class OrderCreationResult {
  final String orderId;
  final double price;

  OrderCreationResult({required this.orderId, required this.price});
}

abstract class PurchaseRepository {
  // 当前订单信息
  String? get currentQrUrl;
  String? get currentPurchasePrice;
  String? get purchasePriceNum;
  String? get currentPurchasePlanId;
  String? get currentPurchasePlanName;

  /// 加载购买套餐列表
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<List<PurchasePlan>> loadPurchaseCards({bool forceRefresh = false});

  /// 加载AIGC积分套餐列表
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<List<PurchasePlan>> loadAigcPointPurchaseCards(
      {bool forceRefresh = false});

  // 清除当前购买计划（外部关闭付款窗口）
  void clearCurrentPurchasePlan();

  // 使用当前购买信息创建订单,成功就返回订单id，否则返回null
  Future<String?> createOrder(String planId, PaymentChannel paymentChannel);

  // 检查订单状态
  Future<OrderStatus> checkCurrentOrderStatus(String orderId);
}
