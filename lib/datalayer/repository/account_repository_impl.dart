import 'dart:async';

// import 'package:turing_art/datalayer/domain/models/account/account.dart';
import 'package:turing_art/datalayer/domain/models/account/account_all.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/service/account/account_service.dart';

class AccountRepositoryImpl extends AccountRepository {
  // 新增事件流控制器
  final _streamController = StreamController<String>.broadcast();

  // 兼容主动和被动变更
  @override
  Stream<String> get userAccountChange => _streamController.stream;

  final AccountService _accountService;
  AccountAll? _accountAll;

  AccountRepositoryImpl(this._accountService);

  @override
  AccountAll? get accountAll => _accountAll;

  @override
  Future<void> clearAccount() async {
    _accountAll = null;
  }

  @override
  Future<void> getAllAccount() async {
    if (_accountAll != null) {
      _streamController.add('account all get from cache');
      return;
    }
    await refreshAllAccount();
  }

  @override
  Future<void> refreshAllAccount() async {
    _accountAll = await _accountService.getAllAccount();
    _streamController.add('account all refreshed');
  }
}
