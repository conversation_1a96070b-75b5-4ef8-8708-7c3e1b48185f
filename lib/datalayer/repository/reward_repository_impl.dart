import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/service/reward/operation_reward_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 活动奖励上报仓库实现类
class RewardRepositoryImpl implements RewardRepository {
  final OperationRewardService _rewardService;
  final CurrentUserRepository _currentUserRepository;

  RewardRepositoryImpl(this._rewardService, this._currentUserRepository);

  @override
  Future<bool> reportNewUserGift(String rewardId) async {
    final storeId = _currentUserRepository.store?.id;
    if (storeId == null) {
      PGLog.e('找不到店铺ID，无法上报活动奖励');
      return false;
    }

    return await _rewardService.reportReward(
      rewardId: rewardId,
      storeId: storeId,
      reason: RewardReportReason.newRegister,
    );
  }

  @override
  Future<bool> reportAddWechatSuccess(String rewardId) async {
    final storeId = _currentUserRepository.store?.id;
    if (storeId == null) {
      PGLog.e('找不到店铺ID，无法上报活动奖励');
      return false;
    }

    return await _rewardService.reportReward(
      rewardId: rewardId,
      storeId: storeId,
      reason: RewardReportReason.addWxwork,
    );
  }
}
