import 'package:turing_art/datalayer/domain/models/recharge_record/recharge_record_model.dart';
import 'package:turing_art/datalayer/repository/recharge_record_repository.dart';
import 'package:turing_art/datalayer/service/recharge_record/recharge_record_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 充值记录仓库实现
class RechargeRecordRepositoryImpl implements RechargeRecordRepository {
  final RechargeRecordService _service;

  RechargeRecordRepositoryImpl(this._service);

  @override
  Future<RechargeRecordModel?> fetchRechargeRecords({
    int page = 1,
    int pageSize = 10,
    String? bankType,
    String? startTime,
    String? endTime,
  }) async {
    try {
      final result = await _service.fetchRechargeRecords(
        page: page,
        pageSize: pageSize,
        bankType: bankType,
        startTime: startTime,
        endTime: endTime,
      );
      return result;
    } catch (e) {
      PGLog.e('获取充值记录失败: $e');
      return null;
    }
  }
}
