import '../domain/models/aigc_export_history/aigc_export_history_model.dart';
import '../domain/models/export_history/export_history_model.dart';

/// 导出交易历史记录仓库
abstract class ExportHistoryRepository {
  /// 获取导出交易历史记录
  ///
  /// [page] 页码，默认为1开始
  /// [pageSize] 每页数量，默认为20
  Future<ExportHistoryModel?> fetchExportHistory({
    int page = 1,
    int pageSize = 20,
    int? createdAfter,
    int? createdBefore,
    String? employeeIds,
    int? type,
  });

  /// 获取AIGC导出历史记录
  ///
  /// [page] 页码，默认为1开始
  /// [pageSize] 每页数量，默认为20
  Future<AIGCExportHistoryModel?> fetchAIGCExportHistory({
    int page = 1,
    int pageSize = 20,
    String? startTime,
    String? endTime,
  });
}
