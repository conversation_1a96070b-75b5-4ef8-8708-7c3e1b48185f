import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';

abstract class AigcPresetsRepository {
  // 获取预设列表 - 使用Stream实现cache-first机制
  Stream<List<AigcPcPresetsDetailResponse>> getAigcPresetsStream();

  // 获取预设详情
  Future<AigcPcPresetsDetailResponse> getAigcPresetsDetail(String presetId);

  // 创建预设
  Future<AigcPcPresetsDetailResponse> createAigcPresets(String name,
      String requirementSupplement, String photoUrl, String maskUrl, int count);

  // 重新生成预设
  Future<AigcPcPresetsDetailResponse> regenerateAigcPresets(
      String presetId, String supplement, int regenerateCount);

  // 删除预设
  Future<void> deleteAigcPresets(String presetId);

  // 删除预设效果图
  Future<void> deleteAigcPresetsEffect(
      String presetId, List<String> effectCodes);

  // 选择预设效果图
  Future<void> selectAigcPresetsEffect(
      String presetId, List<String> effectCode);

  // 轮询预设
  Future<List<AigcPcPresetsLoopModel>> pollAigcPresets(List<String> presetIds);

  // 手动刷新预设数据
  Future<void> refreshPresets();

  // 清理资源
  void dispose();
}
