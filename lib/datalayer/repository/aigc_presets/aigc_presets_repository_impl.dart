import 'dart:async';

import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/service/aigc_presets/aigc_local_data_service.dart';
import 'package:turing_art/datalayer/service/aigc_presets/aigc_presets_service.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/utils/pg_log.dart';

class AigcPresetsRepositoryImpl extends AigcPresetsRepository {
  final AigcPresetsService _aigcPresetsService;
  final AigcLocalDataService _aigcLocalDataService = AigcLocalDataService();

  // 广播StreamController - 用于预设场景数据
  StreamController<List<AigcPcPresetsDetailResponse>>? _presetStreamController;

  // 缓存最新的 preset 数据
  List<AigcPcPresetsDetailResponse>? _cachedPresets;

  // 是否正在加载数据
  bool _isLoadingPresets = false;

  AigcPresetsRepositoryImpl(
    this._aigcPresetsService,
  );

  @override
  Stream<List<AigcPcPresetsDetailResponse>> getAigcPresetsStream() {
    // 创建或返回现有的广播Stream
    _presetStreamController ??=
        StreamController<List<AigcPcPresetsDetailResponse>>.broadcast(
      onListen: () {
        PGLog.d('开始监听预设数据流');
        _loadPresetsData();
      },
      onCancel: () {
        PGLog.d('取消监听预设数据流');
        // 如果没有监听者了，可以选择清理资源
        if (_presetStreamController?.hasListener == false) {
          dispose();
        }
      },
    );

    return _createPresetStateStream(_presetStreamController!.stream);
  }

  /// 由于原始的 Stream 在新的订阅者订阅时不会自动将缓存数据发送给新订阅者，即它是冷流，因此
  /// 这里我们将最新的数据进行缓存，并且为新的订阅者发送缓存数据。
  Stream<List<AigcPcPresetsDetailResponse>> _createPresetStateStream(
      Stream<List<AigcPcPresetsDetailResponse>> originalStream) {
    // 使用 Stream.multi 来解决普通的 Stream 只能有一个监听器，即 onListen 只能被调用一
    // 次的问题。
    return Stream.multi((controller) {
      StreamSubscription<List<AigcPcPresetsDetailResponse>>? subscription;

      // 1. 如果有缓存数据，立即发送给新订阅者
      if (_cachedPresets != null) {
        PGLog.d('向新订阅者发送缓存数据: ${_cachedPresets!.length} 条');
        controller.add(_cachedPresets!);
      }

      // 2. 订阅原始Stream以接收后续更新
      subscription = originalStream.listen(
        (data) {
          if (!controller.isClosed) {
            controller.add(data);
          }
        },
        onError: (error) {
          if (!controller.isClosed) {
            controller.addError(error);
          }
        },
        onDone: () {
          if (!controller.isClosed) {
            controller.close();
          }
        },
      );

      // 3. 处理取消订阅
      controller.onCancel = () {
        subscription?.cancel();
      };
    });
  }

  /// 加载预设数据
  Future<void> _loadPresetsData() async {
    if (_isLoadingPresets) {
      return; // 避免重复加载
    }

    _isLoadingPresets = true;

    try {
      // 1. 首先尝试从本地获取数据并发送（cache-first）
      if (_cachedPresets == null) {
        try {
          final localPresets = await _aigcLocalDataService.getAigcPresets();
          if (localPresets.isNotEmpty) {
            final sortedLocalPresets = _sortPresetsByUpdateTime(localPresets);
            _cachedPresets = sortedLocalPresets;
            PGLog.d('从本地获取预设列表成功: ${localPresets.length}');
            _presetStreamController?.add(sortedLocalPresets);
          }
        } catch (e) {
          PGLog.e('从本地获取预设列表失败: $e');
        }
      } else {
        // 如果有缓存，发送缓存数据（但只在没有其他监听者时发送，避免重复）
        if (_presetStreamController?.hasListener == true) {
          _presetStreamController?.add(_cachedPresets!);
        }
      }

      // 2. 发起网络请求获取最新数据
      await _loadPresetFromServer();
    } finally {
      _isLoadingPresets = false;
    }
  }

  Future<void> _loadPresetFromServer() async {
    try {
      // 1. 从网络中获取预设数据
      final networkPresets = await _aigcPresetsService
          .getAigcPresets(AigcRequestConst.presetScene);

      // 2. 存储到本地
      await _aigcLocalDataService.saveAigcPresets(networkPresets);
      PGLog.d('网络获取预设列表成功，已保存到本地: ${networkPresets.length}');

      // 3. 更新缓存并发送最新的网络数据
      final sortedNetworkPresets = _sortPresetsByUpdateTime(networkPresets);
      _cachedPresets = sortedNetworkPresets;
      _presetStreamController?.add(sortedNetworkPresets);
    } catch (e) {
      PGLog.e('网络获取预设列表失败: $e');

      // 如果网络请求失败且之前没有数据，直接返回空数据
      if (_cachedPresets == null) {
        _presetStreamController?.add(<AigcPcPresetsDetailResponse>[]);
      }
    }
  }

  @override
  Future<void> refreshPresets() async {
    await _loadPresetFromServer();
  }

  @override
  void dispose() {
    _presetStreamController?.close();
    _presetStreamController = null;
  }

  @override
  Future<AigcPcPresetsDetailResponse> getAigcPresetsDetail(
      String presetId) async {
    return _aigcPresetsService.getAigcPresetsDetail(presetId);
  }

  @override
  Future<AigcPcPresetsDetailResponse> createAigcPresets(
      String name,
      String requirementSupplement,
      String photoUrl,
      String maskUrl,
      int count) async {
    return _aigcPresetsService.createAigcPresets(
        name, requirementSupplement, photoUrl, maskUrl, count);
  }

  @override
  Future<AigcPcPresetsDetailResponse> regenerateAigcPresets(
      String presetId, String supplement, int regenerateCount) async {
    final regeneratedPreset = await _aigcPresetsService.regenerateAigcPresets(
        presetId, supplement, regenerateCount);

    // 同步更新缓存数据
    await _syncPresetData(regeneratedPreset);

    return regeneratedPreset;
  }

  @override
  Future<void> deleteAigcPresets(String presetId) async {
    return _aigcPresetsService.deleteAigcPresets(presetId);
  }

  @override
  Future<void> deleteAigcPresetsEffect(
      String presetId, List<String> effectCodes) async {
    return _aigcPresetsService.deleteAigcPresetsEffect(presetId, effectCodes);
  }

  @override
  Future<void> selectAigcPresetsEffect(
      String presetId, List<String> effectCode) async {
    return _aigcPresetsService.selectAigcPresetsEffect(presetId, effectCode);
  }

  @override
  Future<List<AigcPcPresetsLoopModel>> pollAigcPresets(
      List<String> presetIds) async {
    return _aigcPresetsService.pollAigcPresets(presetIds);
  }

  Future<void> _syncPresetData(
      AigcPcPresetsDetailResponse updatedPreset) async {
    if (_cachedPresets == null) {
      PGLog.w('缓存为空，无法更新预设数据');
      return;
    }

    try {
      // 1. 更新内存缓存
      final updatedList =
          List<AigcPcPresetsDetailResponse>.from(_cachedPresets!);
      final index =
          updatedList.indexWhere((preset) => preset.id == updatedPreset.id);

      if (index != -1) {
        // 找到了对应的预设，进行更新
        updatedList[index] = updatedPreset;
        PGLog.d('成功更新预设缓存: ${updatedPreset.name}');
      } else {
        // 没找到对应的预设，添加到列表中
        updatedList.add(updatedPreset);
        PGLog.d('预设不存在于缓存中，已添加: ${updatedPreset.name}');
      }

      // 2. 对更新后的列表进行排序
      final sortedList = _sortPresetsByUpdateTime(updatedList);

      // 3. 更新缓存
      _cachedPresets = sortedList;

      // 4. 更新本地存储
      await _aigcLocalDataService.saveAigcPresets(sortedList);

      // 5. 通过Stream发送更新后的数据
      _presetStreamController?.add(sortedList);
    } catch (e) {
      PGLog.e('更新预设缓存失败: $e');
    }
  }

  /// 根据预设的更新时间进行排序
  List<AigcPcPresetsDetailResponse> _sortPresetsByUpdateTime(
      List<AigcPcPresetsDetailResponse> presets) {
    // 创建一个新的可变列表副本，避免修改原始列表
    final sortableList = List<AigcPcPresetsDetailResponse>.from(presets);

    sortableList.sort((a, b) {
      // 按更新时间降序排序
      return b.updateAt.compareTo(a.updateAt);
    });

    return sortableList;
  }
}
