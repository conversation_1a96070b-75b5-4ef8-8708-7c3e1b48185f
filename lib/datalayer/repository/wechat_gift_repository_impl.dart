import 'package:turing_art/datalayer/domain/models/ops_operation/operation_activity.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/datalayer/service/ops_operation/ops_operation_service.dart';

class WechatGiftRepositoryImpl implements WechatGiftRepository {
  // 企业微信礼包权益列表，登录成功后需要获取，多处使用
  List<OperationActivity>? _wechatGiftList;

  final OpsOperationService _service;

  WechatGiftRepositoryImpl(this._service);

  @override
  Future<List<OperationActivity>> getWechatGiftInfo() async {
    if (_wechatGiftList != null) {
      return _wechatGiftList!;
    } else {
      _wechatGiftList =
          await _service.getOperationActivities(OperationType.wechatGift);
      return _wechatGiftList!;
    }
  }
}
