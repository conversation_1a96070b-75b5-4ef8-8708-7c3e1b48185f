import 'dart:async';

import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/datalayer/domain/models/api_error/api_error.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_list.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_summary.dart';
import 'package:turing_art/datalayer/repository/employee_repository.dart';
import 'package:turing_art/datalayer/service/employee/employee_service.dart';
import 'package:turing_art/utils/pg_log.dart';

class EmployeeRepositoryImpl extends EmployeeRepository {
  final EmployeeService _employeeService;

  // 子账号信息变更事件控制器
  final _streamController = StreamController<String>.broadcast();

  EmployeeList? _subAcountList;
  EmployeeSummary? _usedSummary;

  @override
  Stream<String> get employeeInfoChange => _streamController.stream;

  @override
  EmployeeList? get subAcountList => _subAcountList;

  @override
  EmployeeSummary? get usedSummary => _usedSummary;

  EmployeeRepositoryImpl(this._employeeService);

  @override
  Future<void> getEmployeeSummary() async {
    final employeeSummary = await _employeeService.getEmployeeSummary();
    if (employeeSummary != null) {
      _usedSummary = employeeSummary;
      _streamController.add('employee summary fetched');
    }
  }

  @override
  Future<void> getEmployeeList() async {
    final employeeList = await _employeeService.getEmployeeList();
    if (employeeList != null) {
      // 过滤掉角色为creator的用户
      final allSubAccountUsers = employeeList.users
          .where((user) => user.role != UserRole.creator)
          .toList();

      // 按注册时间排序，最新添加的排在前面
      allSubAccountUsers.sort((a, b) => b.regDateTime.compareTo(a.regDateTime));

      // 创建一个新的EmployeeList对象，包含过滤后的用户列表
      _subAcountList = EmployeeList(
        storeId: employeeList.storeId,
        name: employeeList.name,
        total: employeeList.total,
        available: employeeList.available,
        users: allSubAccountUsers,
      );
      _streamController.add('employee list fetched');
    }
  }

  @override
  Future<ApiError?> addEmployees({
    required String code,
    required List<EmployeeInfo> employees,
  }) async {
    // 将EmployeeInfo列表转换为API所需的数据格式
    final List<Map<String, dynamic>> data =
        employees.map((e) => e.toJson()).toList();

    // 调用服务添加子账号
    final apiError = await _employeeService.addEmployee(
      code: code,
      data: data,
    );

    // 如果添加成功，刷新子账号列表
    if (apiError == null) {
      final count = employees.length;
      PGLog.d('添加${count > 1 ? "批量" : ""}子账号成功，共$count个，刷新列表');
      return null;
    } else {
      final code = apiError.code;
      String message = apiError.message;
      // 400错误码是通用错误码，还需要根据message判断具体错误
      if (code == 400 && message.contains('mobiles exists')) {
        message = message.replaceAll(
            'mobiles exists', EmployeeRepository.mobileExistMessage);
      } else if (code == 10539) {
        message = '验证码已过期';
      } else if (code == 10537) {
        message = '验证码错误';
      } else {
        message = '添加子账号失败';
      }
      return ApiError(
        code: code,
        message: message,
      );
    }
  }

  @override
  void sendVerificationCode() {
    _employeeService.sendVerificationCode();
  }

  @override
  Future<bool> updateEmployee({
    required String id,
    String? nickname,
    required bool enable,
  }) async {
    // 调用服务更新子账号
    final result = await _employeeService.updateEmployee(
      id: id,
      nickname: nickname,
      enable: enable,
    );

    // 如果更新成功，刷新子账号列表
    if (result) {
      PGLog.d('更新子账号成功，刷新列表');
      await getEmployeeList();
    }

    return result;
  }
}
