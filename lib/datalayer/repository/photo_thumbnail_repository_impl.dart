import 'package:turing_art/datalayer/repository/photo_thumbnail_repository.dart';
import 'package:turing_art/datalayer/utils/lru_cache.dart';

class PhotoThumbnailRepositoryImpl implements PhotoThumbnailRepository {
  final _cache = LruCache<String, String>(100); // 娣诲姞娉涘瀷绫诲瀷鍙傛暟

  @override
  Future<String?> fetchThumbnailWithPhotoId(String id) async {
    return await Future.value(_cache.get(id)); // 鍖呰涓哄紓姝ュ€?
  }

  @override
  Future<bool> setThumbnail(String thumbnail, String id) async {
    if (thumbnail.isEmpty) {
      return false; // 娣诲姞绌哄€兼牎楠?
    }
    _cache.put(id, thumbnail);
    return true;
  }
}
