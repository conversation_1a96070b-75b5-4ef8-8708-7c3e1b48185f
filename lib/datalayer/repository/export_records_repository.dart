import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';

/// 导出数据历史记录仓库
abstract class ExportRecordsRepository {
  /// 获得导出数据
  Future<List<ExportRecord>> fetchExportRecord();

  /// 获得导出数据
  Future<List<ExportRecord>> fetchExportRecordsByGuids(List<String> guids);

  /// 删除导出数据
  Future<void> deleteExportRecord(String guid);

  /// 更新导出数据
  Future<void> updateExportRecord(ExportRecord record);

  /// 获得某次导出任务的所有目标地址
  Future<List<String>> fetchExportTaskFilesFinalPath(String exportTaskId);
}
