import 'dart:collection';

class LruCache<K, V> {
  final int _maxSize;
  final LinkedHashMap<K, V> _cache = LinkedHashMap();

  LruCache(this._maxSize);

  bool isEmpty() {
    return _cache.isEmpty;
  }

  /// 获取缓存中的值，如果存在则返回，否则返回 null
  V? get(K key) {
    if (!_cache.containsKey(key)) {
      return null;
    }
    // 访问过的项移到队尾
    _moveToEnd(key);
    return _cache[key];
  }

  List<V> getAll() {
    return _cache.values.toList();
  }

  void putAll(Map<K, V> entries) {
    for (var entry in entries.entries) {
      put(entry.key, entry.value); // 批量添加
    }
  }

  /// 将一个新的值添加到缓存中
  void put(K key, V value) {
    if (_cache.length >= _maxSize) {
      // 缓存已满，移除最少使用的项（即 LinkedHashMap 的头部）
      _cache.remove(_cache.keys.first);
    }
    _cache[key] = value;
    // 将新插入的项移到队尾，表示最近使用过
    _moveToEnd(key);
  }

  /// 删除缓存中的指定项
  void remove(K key) {
    _cache.remove(key);
  }

  /// 清空缓存
  void clear() {
    _cache.clear();
  }

  // 将访问的项移到队尾，表示最近使用过
  void _moveToEnd(K key) {
    final value = _cache.remove(key);
    if (value != null) {
      _cache[key] = value;
    }
  }
}
