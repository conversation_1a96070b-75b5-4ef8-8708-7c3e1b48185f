/// 用户角色枚举
///
/// 定义了用户在系统中可能的角色类型
enum UserRole {
  /// 创作者角色
  creator('creator'),

  /// 员工角色
  employee('employee');

  /// 角色的字符串值
  final String value;

  /// 构造函数
  const UserRole(this.value);

  /// 从字符串值转换为枚举
  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.creator, // 默认为创作者角色
    );
  }

  /// 将枚举转换为字符串
  @override
  String toString() => value;
}
