enum ExportStatus {
  running('running'),
  unexported('unexported'),
  completed('completed'),
  failed('failed');

  const ExportStatus(this.value);
  final String value;

  static ExportStatus fromString(String value) {
    return ExportStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ExportStatus.unexported,
    );
  }

  bool get isRunning => this == ExportStatus.running;
  bool get isUnexported => this == ExportStatus.unexported;
  bool get isCompleted => this == ExportStatus.completed;
  bool get isFailed => this == ExportStatus.failed;
}
