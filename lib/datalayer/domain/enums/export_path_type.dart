/// 导出路径类型枚举
enum ExportPathType {
  /// 原始文件所在文件夹
  original('original', '原始文件所在文件夹'),

  /// 指定文件夹
  custom('custom', '指定文件夹');

  final String name;
  final String description;

  const ExportPathType(this.name, this.description);

  /// 通过名称获取枚举值
  static ExportPathType fromString(String name) {
    return ExportPathType.values.firstWhere(
      (type) => type.name == name,
      orElse: () => ExportPathType.original,
    );
  }
}
