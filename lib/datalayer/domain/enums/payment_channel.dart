enum PaymentChannel {
  wechat('微信'),
  alipay('支付宝'),
  yikatong('一卡通');

  final String description;
  const PaymentChannel(this.description);

  String getApiName() {
    switch (this) {
      case PaymentChannel.wechat:
        return 'wechat';
      case PaymentChannel.alipay:
        return 'ali';
      case PaymentChannel.yikatong:
        // 接口出来了需要修改
        return 'ykt';
      default:
        return '';
    }
  }
}
