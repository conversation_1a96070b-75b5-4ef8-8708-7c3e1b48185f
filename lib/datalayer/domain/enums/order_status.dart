// 订单状态
enum OrderStatus {
  processing('处理中'),
  canceled('订单已取消'),
  completed('订单已完成'),
  refunded('订单已退款'),
  closed('订单已失效'),
  unknown('订单状态未知');

  final String description;
  const OrderStatus(this.description);

  static OrderStatus convertToOrderStatus(String status) {
    // 将输入的状态字符串转换为小写，以便不区分大小写进行比较
    final lowerStatus = status.toLowerCase();

    // 尝试通过名称匹配
    for (var orderStatus in OrderStatus.values) {
      if (orderStatus.name.toLowerCase() == lowerStatus) {
        return orderStatus;
      }
    }

    // 如果没有匹配到，返回unknown
    return OrderStatus.unknown;
  }
}
