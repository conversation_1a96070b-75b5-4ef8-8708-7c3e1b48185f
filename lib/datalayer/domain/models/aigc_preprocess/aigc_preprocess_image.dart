import 'package:flutter/material.dart';

// import '../controllers/isolate_image_list_controller.dart';
// import '../services/task_type.dart';
import 'aigc_preprocess_status.dart';

/// 图片领域模型 - 业务实体
class ImageData {
  final String id;
  final String name;
  final String originalPath;
  final String? thumbnailPath;
  final String? maskPath;
  final String? proofPath;
  final String? exportPath;
  final Color mockColor;

  // 多个处理状态（按任务类型分组）
  final Map<AigcPreProcessTaskType, AigcPreProcessStatus> processStatus;

  const ImageData({
    required this.id,
    required this.name,
    required this.originalPath,
    this.thumbnailPath,
    this.maskPath,
    this.proofPath,
    this.exportPath,
    required this.mockColor,
    this.processStatus = const {},
  });

  bool get hasThumbnail => thumbnailPath != null;

  bool get hasMask => maskPath != null;

  bool get hasProof => proofPath != null;

  bool get hasExport => exportPath != null;

  /// 获取指定任务类型的处理信息
  AigcPreProcessStatus? getProcessingInfo(AigcPreProcessTaskType taskType) {
    return processStatus[taskType];
  }

  /// 是否有任何任务正在处理
  bool get isProcessing =>
      processStatus.values.any((info) => info.isProcessing);

  /// 是否有任何任务等待处理
  bool get isWaiting => processStatus.values.any((info) => info.isWaiting);

  /// 是否有任何任务失败
  bool get isFailed => processStatus.values.any((info) => info.isFailed);

  /// 是否所有任务都已完成
  bool get isAllCompleted =>
      processStatus.isNotEmpty &&
      processStatus.values.every((info) => info.isCompleted);

  /// 获取指定任务类型的处理进度
  double getProgress(AigcPreProcessTaskType taskType) =>
      processStatus[taskType]?.progress ?? 0.0;

  /// 获取指定任务类型的状态描述
  String? getStatus(AigcPreProcessTaskType taskType) =>
      processStatus[taskType]?.status;

  /// 获取指定任务类型的错误信息
  String? getErrorMessage(AigcPreProcessTaskType taskType) =>
      processStatus[taskType]?.errorMessage;

  /// 获取指定任务类型的处理时间
  Duration? getProcessingTime(AigcPreProcessTaskType taskType) =>
      processStatus[taskType]?.processingTime;

  /// 获取当前正在处理的任务类型列表
  List<AigcPreProcessTaskType> get processingTaskTypes => processStatus.entries
      .where((entry) => entry.value.isProcessing)
      .map((entry) => entry.key)
      .toList();

  /// 获取等待处理的任务类型列表
  List<AigcPreProcessTaskType> get waitingTaskTypes => processStatus.entries
      .where((entry) => entry.value.isWaiting)
      .map((entry) => entry.key)
      .toList();

  /// 获取已完成的任务类型列表
  List<AigcPreProcessTaskType> get completedTaskTypes => processStatus.entries
      .where((entry) => entry.value.isCompleted)
      .map((entry) => entry.key)
      .toList();

  /// 获取失败的任务类型列表
  List<AigcPreProcessTaskType> get failedTaskTypes => processStatus.entries
      .where((entry) => entry.value.isFailed)
      .map((entry) => entry.key)
      .toList();

  // 为了向后兼容，保留原有的属性（返回第一个找到的处理信息）
  @Deprecated('使用 getProcessingInfo(TaskType) 或具体的任务类型方法')
  AigcPreProcessStatus? get processingInfo =>
      processStatus.values.isNotEmpty ? processStatus.values.first : null;

  @Deprecated('使用 isProcessing 检查是否有任务正在处理')
  bool get isProcessed => isAllCompleted;

  @Deprecated('使用 getProgress(TaskType) 获取指定任务的进度')
  double get progress => processStatus.values.isNotEmpty
      ? processStatus.values.first.progress
      : 0.0;

  @Deprecated('使用 getStatus(TaskType) 获取指定任务的状态')
  String? get status => processStatus.values.isNotEmpty
      ? processStatus.values.first.status
      : null;

  @Deprecated('使用 getErrorMessage(TaskType) 获取指定任务的错误信息')
  String? get errorMessage => processStatus.values.isNotEmpty
      ? processStatus.values.first.errorMessage
      : null;

  @Deprecated('使用 getProcessingTime(TaskType) 获取指定任务的处理时间')
  Duration? get processingTime => processStatus.values.isNotEmpty
      ? processStatus.values.first.processingTime
      : null;

  /// 复制并更新数据
  ImageData copyWith({
    String? id,
    String? name,
    String? originalPath,
    String? thumbnailPath,
    String? maskPath,
    String? proofPath,
    String? exportPath,
    Color? mockColor,
    Map<AigcPreProcessTaskType, AigcPreProcessStatus>? processStatus,
  }) {
    return ImageData(
      id: id ?? this.id,
      name: name ?? this.name,
      originalPath: originalPath ?? this.originalPath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      maskPath: maskPath ?? this.maskPath,
      proofPath: proofPath ?? this.proofPath,
      exportPath: exportPath ?? this.exportPath,
      mockColor: mockColor ?? this.mockColor,
      processStatus: processStatus ?? this.processStatus,
    );
  }

  /// 更新指定任务类型的处理信息
  ImageData withProcessingInfo(
      AigcPreProcessTaskType taskType, AigcPreProcessStatus info) {
    final newProcessingInfos =
        Map<AigcPreProcessTaskType, AigcPreProcessStatus>.from(processStatus);
    newProcessingInfos[taskType] = info;
    return copyWith(processStatus: newProcessingInfos);
  }

  /// 移除指定任务类型的处理信息
  ImageData withoutProcessingInfo(AigcPreProcessTaskType taskType) {
    final newProcessingInfos =
        Map<AigcPreProcessTaskType, AigcPreProcessStatus>.from(processStatus);
    newProcessingInfos.remove(taskType);
    return copyWith(processStatus: newProcessingInfos);
  }

  /// 移除所有处理信息
  ImageData withoutAllProcessingInfo() {
    return copyWith(processStatus: {});
  }

  /// 批量更新处理信息
  ImageData withProcessingInfos(
      Map<AigcPreProcessTaskType, AigcPreProcessStatus> infos) {
    final newProcessingInfos =
        Map<AigcPreProcessTaskType, AigcPreProcessStatus>.from(processStatus);
    newProcessingInfos.addAll(infos);
    return copyWith(processStatus: newProcessingInfos);
  }

  @override
  String toString() =>
      'ImageData(id: $id, name: $name, processingTasks: ${processStatus.keys.map((t) => t.name).join(", ")})';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageData && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
