enum AigcPreProcessTaskType {
  thumbnail,
  mask,
  export,
  proof, // 打样任务
  rawConversion, // 智能调色任务
}

/// 图像状态枚举
enum AigcPreProcessImageState {
  waiting, // 等待处理
  processing, // 正在处理
  completed, // 处理完成
  failed, // 处理失败
}

/// 图片处理信息 - 值对象
class AigcPreProcessStatus {
  final AigcPreProcessImageState state;
  final double progress;
  final String? status;
  final String? errorMessage;
  final DateTime? submittedAt;
  final Duration? processingTime;
  final String? outputPath;
  final AigcPreProcessTaskType? taskType; // 🎯 添加任务类型字段

  const AigcPreProcessStatus({
    required this.state,
    this.progress = 0.0,
    this.status,
    this.errorMessage,
    this.submittedAt,
    this.processingTime,
    this.outputPath,
    this.taskType, // 🎯 添加任务类型参数
  });

  /// 是否已完成
  bool get isCompleted => state == AigcPreProcessImageState.completed;

  /// 是否正在处理
  bool get isProcessing => state == AigcPreProcessImageState.processing;

  /// 是否失败
  bool get isFailed => state == AigcPreProcessImageState.failed;

  /// 是否等待中
  bool get isWaiting => state == AigcPreProcessImageState.waiting;

  /// 复制并更新状态
  AigcPreProcessStatus copyWith({
    AigcPreProcessImageState? state,
    double? progress,
    String? status,
    String? errorMessage,
    DateTime? submittedAt,
    Duration? processingTime,
    String? outputPath,
    AigcPreProcessTaskType? taskType,
  }) {
    return AigcPreProcessStatus(
      state: state ?? this.state,
      progress: progress ?? this.progress,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      submittedAt: submittedAt ?? this.submittedAt,
      processingTime: processingTime ?? this.processingTime,
      outputPath: outputPath ?? this.outputPath,
      taskType: taskType ?? this.taskType,
    );
  }

  @override
  String toString() =>
      'AigcPreProcessStatus(state: $state, progress: $progress)';
}
