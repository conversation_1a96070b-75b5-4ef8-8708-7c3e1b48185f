// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LoginInfo _$LoginInfoFromJson(Map<String, dynamic> json) {
  return _LoginInfo.fromJson(json);
}

/// @nodoc
mixin _$LoginInfo {
  User get user => throw _privateConstructorUsedError;
  Store get store => throw _privateConstructorUsedError;
  Creator get creator => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LoginInfoCopyWith<LoginInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginInfoCopyWith<$Res> {
  factory $LoginInfoCopyWith(LoginInfo value, $Res Function(LoginInfo) then) =
      _$LoginInfoCopyWithImpl<$Res, LoginInfo>;
  @useResult
  $Res call({User user, Store store, Creator creator});

  $UserCopyWith<$Res> get user;
  $StoreCopyWith<$Res> get store;
  $CreatorCopyWith<$Res> get creator;
}

/// @nodoc
class _$LoginInfoCopyWithImpl<$Res, $Val extends LoginInfo>
    implements $LoginInfoCopyWith<$Res> {
  _$LoginInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? store = null,
    Object? creator = null,
  }) {
    return _then(_value.copyWith(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      store: null == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as Store,
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as Creator,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $StoreCopyWith<$Res> get store {
    return $StoreCopyWith<$Res>(_value.store, (value) {
      return _then(_value.copyWith(store: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CreatorCopyWith<$Res> get creator {
    return $CreatorCopyWith<$Res>(_value.creator, (value) {
      return _then(_value.copyWith(creator: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LoginInfoImplCopyWith<$Res>
    implements $LoginInfoCopyWith<$Res> {
  factory _$$LoginInfoImplCopyWith(
          _$LoginInfoImpl value, $Res Function(_$LoginInfoImpl) then) =
      __$$LoginInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({User user, Store store, Creator creator});

  @override
  $UserCopyWith<$Res> get user;
  @override
  $StoreCopyWith<$Res> get store;
  @override
  $CreatorCopyWith<$Res> get creator;
}

/// @nodoc
class __$$LoginInfoImplCopyWithImpl<$Res>
    extends _$LoginInfoCopyWithImpl<$Res, _$LoginInfoImpl>
    implements _$$LoginInfoImplCopyWith<$Res> {
  __$$LoginInfoImplCopyWithImpl(
      _$LoginInfoImpl _value, $Res Function(_$LoginInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? store = null,
    Object? creator = null,
  }) {
    return _then(_$LoginInfoImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      store: null == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as Store,
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as Creator,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginInfoImpl implements _LoginInfo {
  const _$LoginInfoImpl(
      {required this.user, required this.store, required this.creator});

  factory _$LoginInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginInfoImplFromJson(json);

  @override
  final User user;
  @override
  final Store store;
  @override
  final Creator creator;

  @override
  String toString() {
    return 'LoginInfo(user: $user, store: $store, creator: $creator)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginInfoImpl &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.store, store) || other.store == store) &&
            (identical(other.creator, creator) || other.creator == creator));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, user, store, creator);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginInfoImplCopyWith<_$LoginInfoImpl> get copyWith =>
      __$$LoginInfoImplCopyWithImpl<_$LoginInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginInfoImplToJson(
      this,
    );
  }
}

abstract class _LoginInfo implements LoginInfo {
  const factory _LoginInfo(
      {required final User user,
      required final Store store,
      required final Creator creator}) = _$LoginInfoImpl;

  factory _LoginInfo.fromJson(Map<String, dynamic> json) =
      _$LoginInfoImpl.fromJson;

  @override
  User get user;
  @override
  Store get store;
  @override
  Creator get creator;
  @override
  @JsonKey(ignore: true)
  _$$LoginInfoImplCopyWith<_$LoginInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
