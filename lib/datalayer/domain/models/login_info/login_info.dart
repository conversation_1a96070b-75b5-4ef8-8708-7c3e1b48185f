import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/store/store.dart';
import 'package:turing_art/datalayer/domain/models/user/creator.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';

part 'login_info.freezed.dart';
part 'login_info.g.dart';

@freezed
class LoginInfo with _$LoginInfo {
  const factory LoginInfo({
    required User user,
    required Store store,
    required Creator creator,
  }) = _LoginInfo;

  factory LoginInfo.fromJson(Map<String, dynamic> json) =>
      _$LoginInfoFromJson(json);
}
