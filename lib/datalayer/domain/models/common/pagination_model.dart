import 'package:freezed_annotation/freezed_annotation.dart';

part 'pagination_model.freezed.dart';
part 'pagination_model.g.dart';

/// 通用分页模型
@freezed
class PaginationModel with _$PaginationModel {
  const factory PaginationModel({
    /// 当前页码
    required int page,

    /// 每页条数
    required int pageSize,

    /// 总条数
    required int count,

    /// 总页数
    required int totalPage,
  }) = _PaginationModel;

  factory PaginationModel.fromJson(Map<String, dynamic> json) =>
      _$PaginationModelFromJson(json);
}
