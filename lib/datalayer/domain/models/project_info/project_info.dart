import 'package:drift/drift.dart' hide J<PERSON><PERSON><PERSON>;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

import 'project_version.dart';

part 'project_info.freezed.dart';
part 'project_info.g.dart';

enum ProjectType {
  edit(0), // 编辑工程
  aiGen(1); // AI生成工程

  const ProjectType(this.value);
  final int value;

  /// 从整数值创建ProjectType的工具方法
  static ProjectType projectTypeFromValue(int value) {
    return ProjectType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ProjectType.edit,
    );
  }
}

@freezed
class ProjectInfo with _$ProjectInfo {
  const factory ProjectInfo({
    required String name,
    required String uuid,
    required String version,
    required String author,
    required ExportConfig exportConfig,
    required ProjectType projectType,
    @Default([]) List<String> coverImages,
    @Default(0) int fileCount,
    @Default("") String description,
    DateTime? createdDate,
    DateTime? updateDate,
    @Default(1) int workspaceVersion, // 标识当前工程的版本号
    @Default(false) bool isDelete, // 是否已删除
  }) = _ProjectInfo;
  const ProjectInfo._();

  // 自定义方法，用于给设置自定义默认值
  factory ProjectInfo.create({
    required String name,
    required String uuid,
    required String version,
    required String author,
    ProjectType projectType = ProjectType.edit,
    List<String>? coverImages,
    String? description,
    DateTime? createdDate,
    DateTime? updateDate,
    ExportConfig? exportConfig,
    int? fileCount,
  }) {
    DateTime currentDate = DateTime.now();
    return _ProjectInfo(
      name: name,
      uuid: uuid,
      version: version,
      author: author,
      coverImages: coverImages ?? [],
      fileCount: fileCount ?? 0,
      description: description ?? '',
      createdDate: createdDate ?? currentDate,
      updateDate: updateDate ?? currentDate,
      exportConfig: exportConfig ?? ExportConfig.defaultConfig(),
      workspaceVersion: ProjectVersion.latestVersion,
      projectType: projectType,
    );
  }

  factory ProjectInfo.fromJson(Map<String, dynamic> json) =>
      _$ProjectInfoFromJson(json);

  // 从数据库实体转换为 ProjectInfo
  factory ProjectInfo.fromEntities(
    ProjectEntityData projectEntity,
    List<WorkspaceFileEntityData> subFiles,
  ) {
    final projectType =
        ProjectType.projectTypeFromValue(projectEntity.projectType);
    return _ProjectInfo(
      name: projectEntity.name,
      uuid: projectEntity.projectId,
      version: projectEntity.version,
      author: projectEntity.author,
      coverImages: ProjectInfo.getCoverImages(
        subFiles,
        projectType,
      ),
      fileCount: subFiles.length,
      description: projectEntity.description ?? '',
      createdDate: projectEntity.createdDate,
      updateDate: projectEntity.updateDate,
      exportConfig: ExportConfig(
        outputFolder: projectEntity.outputFolder,
        pathType: projectEntity.outputFolder.isEmpty
            ? ExportPathType.originalFolder
            : ExportPathType.specifyFolder,
        exportFileType: ExportFileType.values.firstWhere(
          (e) => e.index == projectEntity.exportFileType,
          orElse: () => ExportFileType.original,
        ),
        quality: ExportQualityType.values.firstWhere(
          (e) => ExportQualityType.toValue(e) == projectEntity.quality,
          orElse: () => ExportQualityType.high,
        ),
        isReplace: projectEntity.isReplace,
        transferSRGB: projectEntity.transferSRGB,
      ),
      workspaceVersion: projectEntity.workspaceVersion ?? 1,
      projectType: ProjectType.values.firstWhere(
        (e) => e.value == projectEntity.projectType,
        orElse: () => ProjectType.edit,
      ),
      isDelete: projectEntity.isDelete,
    );
  }

  // 转换为数据库实体
  ProjectEntityCompanion toEntity() {
    return ProjectEntityCompanion(
      name: Value(name),
      projectId: Value(uuid),
      version: Value(version),
      author: Value(author),
      coverImages: Value(coverImages.join(',')),
      description: Value(description),
      createdDate: Value(createdDate),
      updateDate: Value(updateDate),
      outputFolder: Value(exportConfig.outputFolder),
      exportFileType: Value(exportConfig.exportFileType.index),
      quality: Value(ExportQualityType.toValue(exportConfig.quality)),
      isReplace: Value(exportConfig.isReplace),
      transferSRGB: Value(
        exportConfig.transferSRGB,
      ),
      workspaceVersion: Value(workspaceVersion),
      projectType: Value(projectType.value),
      isDelete: Value(isDelete),
    );
  }

  // 获取封面图片
  static List<String> getCoverImages(
    List<WorkspaceFileEntityData> files,
    ProjectType type,
  ) {
    final coverImages = <String>[];
    for (final file in files) {
      if (file.isDeleted == false) {
        coverImages.add(file.fileId);
        if (coverImages.length >= 3) {
          break;
        }
      }
    }
    return coverImages;
  }
}
