/// 工程版本管理类
/// 负责管理工程版本与对应的软件版本范围
class ProjectVersion {
  /// 工程版本信息
  /// key: 工程版本号
  /// value: 对应的软件版本列表
  static const Map<int, List<String>> _versionMap = {
    1: ['0.9.0', '0.9.1', '1.0.0', '1.0.1', '1.0.2', '1.1.0', '1.2.0'],
    2: ['1.3.0'],
  };

  /// 获取工程版本对应的软件版本列表
  /// [projectVersion] 工程版本号
  /// 返回对应的软件版本列表，如果找不到对应关系则返回null
  static List<String>? getSoftwareVersions(int projectVersion) {
    return _versionMap[projectVersion];
  }

  /// 获取最新的工程版本号
  static int get latestVersion {
    final versions = _versionMap.keys.toList()..sort();
    return versions.last;
  }
}
