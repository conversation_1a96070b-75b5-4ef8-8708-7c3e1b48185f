// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'project_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProjectInfo _$ProjectInfoFromJson(Map<String, dynamic> json) {
  return _ProjectInfo.fromJson(json);
}

/// @nodoc
mixin _$ProjectInfo {
  String get name => throw _privateConstructorUsedError;
  String get uuid => throw _privateConstructorUsedError;
  String get version => throw _privateConstructorUsedError;
  String get author => throw _privateConstructorUsedError;
  ExportConfig get exportConfig => throw _privateConstructorUsedError;
  ProjectType get projectType => throw _privateConstructorUsedError;
  List<String> get coverImages => throw _privateConstructorUsedError;
  int get fileCount => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  DateTime? get createdDate => throw _privateConstructorUsedError;
  DateTime? get updateDate => throw _privateConstructorUsedError;
  int get workspaceVersion => throw _privateConstructorUsedError; // 标识当前工程的版本号
  bool get isDelete => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ProjectInfoCopyWith<ProjectInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProjectInfoCopyWith<$Res> {
  factory $ProjectInfoCopyWith(
          ProjectInfo value, $Res Function(ProjectInfo) then) =
      _$ProjectInfoCopyWithImpl<$Res, ProjectInfo>;
  @useResult
  $Res call(
      {String name,
      String uuid,
      String version,
      String author,
      ExportConfig exportConfig,
      ProjectType projectType,
      List<String> coverImages,
      int fileCount,
      String description,
      DateTime? createdDate,
      DateTime? updateDate,
      int workspaceVersion,
      bool isDelete});

  $ExportConfigCopyWith<$Res> get exportConfig;
}

/// @nodoc
class _$ProjectInfoCopyWithImpl<$Res, $Val extends ProjectInfo>
    implements $ProjectInfoCopyWith<$Res> {
  _$ProjectInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? uuid = null,
    Object? version = null,
    Object? author = null,
    Object? exportConfig = null,
    Object? projectType = null,
    Object? coverImages = null,
    Object? fileCount = null,
    Object? description = null,
    Object? createdDate = freezed,
    Object? updateDate = freezed,
    Object? workspaceVersion = null,
    Object? isDelete = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      author: null == author
          ? _value.author
          : author // ignore: cast_nullable_to_non_nullable
              as String,
      exportConfig: null == exportConfig
          ? _value.exportConfig
          : exportConfig // ignore: cast_nullable_to_non_nullable
              as ExportConfig,
      projectType: null == projectType
          ? _value.projectType
          : projectType // ignore: cast_nullable_to_non_nullable
              as ProjectType,
      coverImages: null == coverImages
          ? _value.coverImages
          : coverImages // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fileCount: null == fileCount
          ? _value.fileCount
          : fileCount // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updateDate: freezed == updateDate
          ? _value.updateDate
          : updateDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      workspaceVersion: null == workspaceVersion
          ? _value.workspaceVersion
          : workspaceVersion // ignore: cast_nullable_to_non_nullable
              as int,
      isDelete: null == isDelete
          ? _value.isDelete
          : isDelete // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ExportConfigCopyWith<$Res> get exportConfig {
    return $ExportConfigCopyWith<$Res>(_value.exportConfig, (value) {
      return _then(_value.copyWith(exportConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProjectInfoImplCopyWith<$Res>
    implements $ProjectInfoCopyWith<$Res> {
  factory _$$ProjectInfoImplCopyWith(
          _$ProjectInfoImpl value, $Res Function(_$ProjectInfoImpl) then) =
      __$$ProjectInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String uuid,
      String version,
      String author,
      ExportConfig exportConfig,
      ProjectType projectType,
      List<String> coverImages,
      int fileCount,
      String description,
      DateTime? createdDate,
      DateTime? updateDate,
      int workspaceVersion,
      bool isDelete});

  @override
  $ExportConfigCopyWith<$Res> get exportConfig;
}

/// @nodoc
class __$$ProjectInfoImplCopyWithImpl<$Res>
    extends _$ProjectInfoCopyWithImpl<$Res, _$ProjectInfoImpl>
    implements _$$ProjectInfoImplCopyWith<$Res> {
  __$$ProjectInfoImplCopyWithImpl(
      _$ProjectInfoImpl _value, $Res Function(_$ProjectInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? uuid = null,
    Object? version = null,
    Object? author = null,
    Object? exportConfig = null,
    Object? projectType = null,
    Object? coverImages = null,
    Object? fileCount = null,
    Object? description = null,
    Object? createdDate = freezed,
    Object? updateDate = freezed,
    Object? workspaceVersion = null,
    Object? isDelete = null,
  }) {
    return _then(_$ProjectInfoImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      author: null == author
          ? _value.author
          : author // ignore: cast_nullable_to_non_nullable
              as String,
      exportConfig: null == exportConfig
          ? _value.exportConfig
          : exportConfig // ignore: cast_nullable_to_non_nullable
              as ExportConfig,
      projectType: null == projectType
          ? _value.projectType
          : projectType // ignore: cast_nullable_to_non_nullable
              as ProjectType,
      coverImages: null == coverImages
          ? _value._coverImages
          : coverImages // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fileCount: null == fileCount
          ? _value.fileCount
          : fileCount // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updateDate: freezed == updateDate
          ? _value.updateDate
          : updateDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      workspaceVersion: null == workspaceVersion
          ? _value.workspaceVersion
          : workspaceVersion // ignore: cast_nullable_to_non_nullable
              as int,
      isDelete: null == isDelete
          ? _value.isDelete
          : isDelete // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProjectInfoImpl extends _ProjectInfo {
  const _$ProjectInfoImpl(
      {required this.name,
      required this.uuid,
      required this.version,
      required this.author,
      required this.exportConfig,
      required this.projectType,
      final List<String> coverImages = const [],
      this.fileCount = 0,
      this.description = "",
      this.createdDate,
      this.updateDate,
      this.workspaceVersion = 1,
      this.isDelete = false})
      : _coverImages = coverImages,
        super._();

  factory _$ProjectInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProjectInfoImplFromJson(json);

  @override
  final String name;
  @override
  final String uuid;
  @override
  final String version;
  @override
  final String author;
  @override
  final ExportConfig exportConfig;
  @override
  final ProjectType projectType;
  final List<String> _coverImages;
  @override
  @JsonKey()
  List<String> get coverImages {
    if (_coverImages is EqualUnmodifiableListView) return _coverImages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_coverImages);
  }

  @override
  @JsonKey()
  final int fileCount;
  @override
  @JsonKey()
  final String description;
  @override
  final DateTime? createdDate;
  @override
  final DateTime? updateDate;
  @override
  @JsonKey()
  final int workspaceVersion;
// 标识当前工程的版本号
  @override
  @JsonKey()
  final bool isDelete;

  @override
  String toString() {
    return 'ProjectInfo(name: $name, uuid: $uuid, version: $version, author: $author, exportConfig: $exportConfig, projectType: $projectType, coverImages: $coverImages, fileCount: $fileCount, description: $description, createdDate: $createdDate, updateDate: $updateDate, workspaceVersion: $workspaceVersion, isDelete: $isDelete)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProjectInfoImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.uuid, uuid) || other.uuid == uuid) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.author, author) || other.author == author) &&
            (identical(other.exportConfig, exportConfig) ||
                other.exportConfig == exportConfig) &&
            (identical(other.projectType, projectType) ||
                other.projectType == projectType) &&
            const DeepCollectionEquality()
                .equals(other._coverImages, _coverImages) &&
            (identical(other.fileCount, fileCount) ||
                other.fileCount == fileCount) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.updateDate, updateDate) ||
                other.updateDate == updateDate) &&
            (identical(other.workspaceVersion, workspaceVersion) ||
                other.workspaceVersion == workspaceVersion) &&
            (identical(other.isDelete, isDelete) ||
                other.isDelete == isDelete));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      uuid,
      version,
      author,
      exportConfig,
      projectType,
      const DeepCollectionEquality().hash(_coverImages),
      fileCount,
      description,
      createdDate,
      updateDate,
      workspaceVersion,
      isDelete);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProjectInfoImplCopyWith<_$ProjectInfoImpl> get copyWith =>
      __$$ProjectInfoImplCopyWithImpl<_$ProjectInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProjectInfoImplToJson(
      this,
    );
  }
}

abstract class _ProjectInfo extends ProjectInfo {
  const factory _ProjectInfo(
      {required final String name,
      required final String uuid,
      required final String version,
      required final String author,
      required final ExportConfig exportConfig,
      required final ProjectType projectType,
      final List<String> coverImages,
      final int fileCount,
      final String description,
      final DateTime? createdDate,
      final DateTime? updateDate,
      final int workspaceVersion,
      final bool isDelete}) = _$ProjectInfoImpl;
  const _ProjectInfo._() : super._();

  factory _ProjectInfo.fromJson(Map<String, dynamic> json) =
      _$ProjectInfoImpl.fromJson;

  @override
  String get name;
  @override
  String get uuid;
  @override
  String get version;
  @override
  String get author;
  @override
  ExportConfig get exportConfig;
  @override
  ProjectType get projectType;
  @override
  List<String> get coverImages;
  @override
  int get fileCount;
  @override
  String get description;
  @override
  DateTime? get createdDate;
  @override
  DateTime? get updateDate;
  @override
  int get workspaceVersion;
  @override // 标识当前工程的版本号
  bool get isDelete;
  @override
  @JsonKey(ignore: true)
  _$$ProjectInfoImplCopyWith<_$ProjectInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
