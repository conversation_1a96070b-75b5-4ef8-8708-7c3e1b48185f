import 'dart:io';

import '../../../../utils/tapj_processor.dart';

/// 处理图片文件的结果类
/// 用于封装图片文件处理的结果，包含有效文件和项目名称
class DealImageFilesResult {
  /// 有效的图片文件列表
  final List<File> validFiles;

  /// 项目名称
  final String projectName;

  // tapj导入结果（如果是tapj文件）
  final TapjImportResult? tapjImportResult;

  /// 构造函数
  DealImageFilesResult(this.validFiles, this.projectName,
      {this.tapjImportResult});

  /// 创建普通图片文件结果
  DealImageFilesResult.fromImages(this.validFiles, this.projectName)
      : tapjImportResult = null;

  /// 创建tapj文件结果
  DealImageFilesResult.fromTapj(
      this.validFiles, this.projectName, this.tapjImportResult);

  /// 获取文件数量
  int fileCount() {
    return validFiles.length;
  }

  /// 检查是否有有效文件
  bool get hasValidFiles => validFiles.isNotEmpty;

  /// 获取第一个有效文件
  File? get firstFile => validFiles.isNotEmpty ? validFiles.first : null;

  /// 获取最后一个有效文件
  File? get lastFile => validFiles.isNotEmpty ? validFiles.last : null;

  @override
  String toString() {
    return 'DealImageFilesResult(validFiles: ${validFiles.length}, projectName: $projectName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DealImageFilesResult &&
        other.validFiles.length == validFiles.length &&
        other.projectName == projectName;
  }

  @override
  int get hashCode => validFiles.length.hashCode ^ projectName.hashCode;

  /// 是否为tapj导入
  bool get isTapjImport => tapjImportResult != null;

  /// 获取tapj导入数据（如果存在）
  TapjImportResult? get tapjData => tapjImportResult;
}
