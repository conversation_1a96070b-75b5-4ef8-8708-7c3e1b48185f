// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'setting_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SettingItemModel _$SettingItemModelFromJson(Map<String, dynamic> json) {
  return _SettingItemModel.fromJson(json);
}

/// @nodoc
mixin _$SettingItemModel {
  String get key => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get value => throw _privateConstructorUsedError;
  List<SettingChoiceModel> get choices => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SettingItemModelCopyWith<SettingItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingItemModelCopyWith<$Res> {
  factory $SettingItemModelCopyWith(
          SettingItemModel value, $Res Function(SettingItemModel) then) =
      _$SettingItemModelCopyWithImpl<$Res, SettingItemModel>;
  @useResult
  $Res call(
      {String key,
      String title,
      String type,
      String value,
      List<SettingChoiceModel> choices});
}

/// @nodoc
class _$SettingItemModelCopyWithImpl<$Res, $Val extends SettingItemModel>
    implements $SettingItemModelCopyWith<$Res> {
  _$SettingItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? title = null,
    Object? type = null,
    Object? value = null,
    Object? choices = null,
  }) {
    return _then(_value.copyWith(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      choices: null == choices
          ? _value.choices
          : choices // ignore: cast_nullable_to_non_nullable
              as List<SettingChoiceModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingItemModelImplCopyWith<$Res>
    implements $SettingItemModelCopyWith<$Res> {
  factory _$$SettingItemModelImplCopyWith(_$SettingItemModelImpl value,
          $Res Function(_$SettingItemModelImpl) then) =
      __$$SettingItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String key,
      String title,
      String type,
      String value,
      List<SettingChoiceModel> choices});
}

/// @nodoc
class __$$SettingItemModelImplCopyWithImpl<$Res>
    extends _$SettingItemModelCopyWithImpl<$Res, _$SettingItemModelImpl>
    implements _$$SettingItemModelImplCopyWith<$Res> {
  __$$SettingItemModelImplCopyWithImpl(_$SettingItemModelImpl _value,
      $Res Function(_$SettingItemModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? title = null,
    Object? type = null,
    Object? value = null,
    Object? choices = null,
  }) {
    return _then(_$SettingItemModelImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      choices: null == choices
          ? _value._choices
          : choices // ignore: cast_nullable_to_non_nullable
              as List<SettingChoiceModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingItemModelImpl implements _SettingItemModel {
  const _$SettingItemModelImpl(
      {required this.key,
      required this.title,
      required this.type,
      required this.value,
      final List<SettingChoiceModel> choices = const []})
      : _choices = choices;

  factory _$SettingItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingItemModelImplFromJson(json);

  @override
  final String key;
  @override
  final String title;
  @override
  final String type;
  @override
  final String value;
  final List<SettingChoiceModel> _choices;
  @override
  @JsonKey()
  List<SettingChoiceModel> get choices {
    if (_choices is EqualUnmodifiableListView) return _choices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_choices);
  }

  @override
  String toString() {
    return 'SettingItemModel(key: $key, title: $title, type: $type, value: $value, choices: $choices)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingItemModelImpl &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.value, value) || other.value == value) &&
            const DeepCollectionEquality().equals(other._choices, _choices));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, key, title, type, value,
      const DeepCollectionEquality().hash(_choices));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingItemModelImplCopyWith<_$SettingItemModelImpl> get copyWith =>
      __$$SettingItemModelImplCopyWithImpl<_$SettingItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingItemModelImplToJson(
      this,
    );
  }
}

abstract class _SettingItemModel implements SettingItemModel {
  const factory _SettingItemModel(
      {required final String key,
      required final String title,
      required final String type,
      required final String value,
      final List<SettingChoiceModel> choices}) = _$SettingItemModelImpl;

  factory _SettingItemModel.fromJson(Map<String, dynamic> json) =
      _$SettingItemModelImpl.fromJson;

  @override
  String get key;
  @override
  String get title;
  @override
  String get type;
  @override
  String get value;
  @override
  List<SettingChoiceModel> get choices;
  @override
  @JsonKey(ignore: true)
  _$$SettingItemModelImplCopyWith<_$SettingItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
