import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_category.dart';

part 'setting_config.freezed.dart';
part 'setting_config.g.dart';

@freezed
class SettingConfig with _$SettingConfig {
  const factory SettingConfig({
    @Default([]) List<SettingCategory> categories,
  }) = _SettingConfig;

  factory SettingConfig.fromJson(Map<String, dynamic> json) =>
      _$SettingConfigFromJson(json);
}
