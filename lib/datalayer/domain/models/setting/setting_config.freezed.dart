// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'setting_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SettingConfig _$SettingConfigFromJson(Map<String, dynamic> json) {
  return _SettingConfig.fromJson(json);
}

/// @nodoc
mixin _$SettingConfig {
  List<SettingCategory> get categories => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SettingConfigCopyWith<SettingConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingConfigCopyWith<$Res> {
  factory $SettingConfigCopyWith(
          SettingConfig value, $Res Function(SettingConfig) then) =
      _$SettingConfigCopyWithImpl<$Res, SettingConfig>;
  @useResult
  $Res call({List<SettingCategory> categories});
}

/// @nodoc
class _$SettingConfigCopyWithImpl<$Res, $Val extends SettingConfig>
    implements $SettingConfigCopyWith<$Res> {
  _$SettingConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
  }) {
    return _then(_value.copyWith(
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<SettingCategory>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingConfigImplCopyWith<$Res>
    implements $SettingConfigCopyWith<$Res> {
  factory _$$SettingConfigImplCopyWith(
          _$SettingConfigImpl value, $Res Function(_$SettingConfigImpl) then) =
      __$$SettingConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SettingCategory> categories});
}

/// @nodoc
class __$$SettingConfigImplCopyWithImpl<$Res>
    extends _$SettingConfigCopyWithImpl<$Res, _$SettingConfigImpl>
    implements _$$SettingConfigImplCopyWith<$Res> {
  __$$SettingConfigImplCopyWithImpl(
      _$SettingConfigImpl _value, $Res Function(_$SettingConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
  }) {
    return _then(_$SettingConfigImpl(
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<SettingCategory>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingConfigImpl implements _SettingConfig {
  const _$SettingConfigImpl({final List<SettingCategory> categories = const []})
      : _categories = categories;

  factory _$SettingConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingConfigImplFromJson(json);

  final List<SettingCategory> _categories;
  @override
  @JsonKey()
  List<SettingCategory> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  @override
  String toString() {
    return 'SettingConfig(categories: $categories)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingConfigImpl &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_categories));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingConfigImplCopyWith<_$SettingConfigImpl> get copyWith =>
      __$$SettingConfigImplCopyWithImpl<_$SettingConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingConfigImplToJson(
      this,
    );
  }
}

abstract class _SettingConfig implements SettingConfig {
  const factory _SettingConfig({final List<SettingCategory> categories}) =
      _$SettingConfigImpl;

  factory _SettingConfig.fromJson(Map<String, dynamic> json) =
      _$SettingConfigImpl.fromJson;

  @override
  List<SettingCategory> get categories;
  @override
  @JsonKey(ignore: true)
  _$$SettingConfigImplCopyWith<_$SettingConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
