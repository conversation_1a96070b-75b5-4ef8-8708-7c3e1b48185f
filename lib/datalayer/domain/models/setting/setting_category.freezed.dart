// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'setting_category.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SettingCategory _$SettingCategoryFromJson(Map<String, dynamic> json) {
  return _SettingCategory.fromJson(json);
}

/// @nodoc
mixin _$SettingCategory {
  String get title => throw _privateConstructorUsedError;
  String get key => throw _privateConstructorUsedError;
  List<SettingItemModel> get items => throw _privateConstructorUsedError;
  bool get isShowForUser => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SettingCategoryCopyWith<SettingCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingCategoryCopyWith<$Res> {
  factory $SettingCategoryCopyWith(
          SettingCategory value, $Res Function(SettingCategory) then) =
      _$SettingCategoryCopyWithImpl<$Res, SettingCategory>;
  @useResult
  $Res call(
      {String title,
      String key,
      List<SettingItemModel> items,
      bool isShowForUser});
}

/// @nodoc
class _$SettingCategoryCopyWithImpl<$Res, $Val extends SettingCategory>
    implements $SettingCategoryCopyWith<$Res> {
  _$SettingCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? key = null,
    Object? items = null,
    Object? isShowForUser = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<SettingItemModel>,
      isShowForUser: null == isShowForUser
          ? _value.isShowForUser
          : isShowForUser // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingCategoryImplCopyWith<$Res>
    implements $SettingCategoryCopyWith<$Res> {
  factory _$$SettingCategoryImplCopyWith(_$SettingCategoryImpl value,
          $Res Function(_$SettingCategoryImpl) then) =
      __$$SettingCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      String key,
      List<SettingItemModel> items,
      bool isShowForUser});
}

/// @nodoc
class __$$SettingCategoryImplCopyWithImpl<$Res>
    extends _$SettingCategoryCopyWithImpl<$Res, _$SettingCategoryImpl>
    implements _$$SettingCategoryImplCopyWith<$Res> {
  __$$SettingCategoryImplCopyWithImpl(
      _$SettingCategoryImpl _value, $Res Function(_$SettingCategoryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? key = null,
    Object? items = null,
    Object? isShowForUser = null,
  }) {
    return _then(_$SettingCategoryImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<SettingItemModel>,
      isShowForUser: null == isShowForUser
          ? _value.isShowForUser
          : isShowForUser // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingCategoryImpl implements _SettingCategory {
  const _$SettingCategoryImpl(
      {required this.title,
      required this.key,
      final List<SettingItemModel> items = const [],
      this.isShowForUser = true})
      : _items = items;

  factory _$SettingCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingCategoryImplFromJson(json);

  @override
  final String title;
  @override
  final String key;
  final List<SettingItemModel> _items;
  @override
  @JsonKey()
  List<SettingItemModel> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  @JsonKey()
  final bool isShowForUser;

  @override
  String toString() {
    return 'SettingCategory(title: $title, key: $key, items: $items, isShowForUser: $isShowForUser)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingCategoryImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.key, key) || other.key == key) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.isShowForUser, isShowForUser) ||
                other.isShowForUser == isShowForUser));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, key,
      const DeepCollectionEquality().hash(_items), isShowForUser);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingCategoryImplCopyWith<_$SettingCategoryImpl> get copyWith =>
      __$$SettingCategoryImplCopyWithImpl<_$SettingCategoryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingCategoryImplToJson(
      this,
    );
  }
}

abstract class _SettingCategory implements SettingCategory {
  const factory _SettingCategory(
      {required final String title,
      required final String key,
      final List<SettingItemModel> items,
      final bool isShowForUser}) = _$SettingCategoryImpl;

  factory _SettingCategory.fromJson(Map<String, dynamic> json) =
      _$SettingCategoryImpl.fromJson;

  @override
  String get title;
  @override
  String get key;
  @override
  List<SettingItemModel> get items;
  @override
  bool get isShowForUser;
  @override
  @JsonKey(ignore: true)
  _$$SettingCategoryImplCopyWith<_$SettingCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
