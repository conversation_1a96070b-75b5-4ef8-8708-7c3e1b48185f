import 'package:freezed_annotation/freezed_annotation.dart';
import 'setting_choice_model.dart';

part 'setting_item_model.freezed.dart';
part 'setting_item_model.g.dart';

@freezed
class SettingItemModel with _$SettingItemModel {
  const factory SettingItemModel({
    required String key,
    required String title,
    required String type,
    required String value,
    @Default([]) List<SettingChoiceModel> choices,
  }) = _SettingItemModel;

  factory SettingItemModel.fromJson(Map<String, dynamic> json) =>
      _$SettingItemModelFromJson(json);
}
