// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SettingItemModelImpl _$$SettingItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SettingItemModelImpl(
      key: json['key'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      value: json['value'] as String,
      choices: (json['choices'] as List<dynamic>?)
              ?.map(
                  (e) => SettingChoiceModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$SettingItemModelImplToJson(
        _$SettingItemModelImpl instance) =>
    <String, dynamic>{
      'key': instance.key,
      'title': instance.title,
      'type': instance.type,
      'value': instance.value,
      'choices': instance.choices,
    };
