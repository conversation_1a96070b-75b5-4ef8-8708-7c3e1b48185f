import 'package:freezed_annotation/freezed_annotation.dart';

part 'setting_choice_model.freezed.dart';
part 'setting_choice_model.g.dart';

@freezed
class SettingChoiceModel with _$SettingChoiceModel {
  const factory SettingChoiceModel({
    required String title,
    required String value,
    @Default(false) bool isSelected,
    @Default(false) bool isRecommended,
  }) = _SettingChoiceModel;

  factory SettingChoiceModel.fromJson(Map<String, dynamic> json) =>
      _$SettingChoiceModelFromJson(json);
}
