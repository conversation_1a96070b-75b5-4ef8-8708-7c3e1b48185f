// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_category.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SettingCategoryImpl _$$SettingCategoryImplFromJson(
        Map<String, dynamic> json) =>
    _$SettingCategoryImpl(
      title: json['title'] as String,
      key: json['key'] as String,
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => SettingItemModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isShowForUser: json['isShowForUser'] as bool? ?? true,
    );

Map<String, dynamic> _$$SettingCategoryImplToJson(
        _$SettingCategoryImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'key': instance.key,
      'items': instance.items,
      'isShowForUser': instance.isShowForUser,
    };
