// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'setting_choice_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SettingChoiceModel _$SettingChoiceModelFromJson(Map<String, dynamic> json) {
  return _SettingChoiceModel.fromJson(json);
}

/// @nodoc
mixin _$SettingChoiceModel {
  String get title => throw _privateConstructorUsedError;
  String get value => throw _privateConstructorUsedError;
  bool get isSelected => throw _privateConstructorUsedError;
  bool get isRecommended => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SettingChoiceModelCopyWith<SettingChoiceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingChoiceModelCopyWith<$Res> {
  factory $SettingChoiceModelCopyWith(
          SettingChoiceModel value, $Res Function(SettingChoiceModel) then) =
      _$SettingChoiceModelCopyWithImpl<$Res, SettingChoiceModel>;
  @useResult
  $Res call({String title, String value, bool isSelected, bool isRecommended});
}

/// @nodoc
class _$SettingChoiceModelCopyWithImpl<$Res, $Val extends SettingChoiceModel>
    implements $SettingChoiceModelCopyWith<$Res> {
  _$SettingChoiceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? value = null,
    Object? isSelected = null,
    Object? isRecommended = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isRecommended: null == isRecommended
          ? _value.isRecommended
          : isRecommended // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingChoiceModelImplCopyWith<$Res>
    implements $SettingChoiceModelCopyWith<$Res> {
  factory _$$SettingChoiceModelImplCopyWith(_$SettingChoiceModelImpl value,
          $Res Function(_$SettingChoiceModelImpl) then) =
      __$$SettingChoiceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title, String value, bool isSelected, bool isRecommended});
}

/// @nodoc
class __$$SettingChoiceModelImplCopyWithImpl<$Res>
    extends _$SettingChoiceModelCopyWithImpl<$Res, _$SettingChoiceModelImpl>
    implements _$$SettingChoiceModelImplCopyWith<$Res> {
  __$$SettingChoiceModelImplCopyWithImpl(_$SettingChoiceModelImpl _value,
      $Res Function(_$SettingChoiceModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? value = null,
    Object? isSelected = null,
    Object? isRecommended = null,
  }) {
    return _then(_$SettingChoiceModelImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isRecommended: null == isRecommended
          ? _value.isRecommended
          : isRecommended // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingChoiceModelImpl implements _SettingChoiceModel {
  const _$SettingChoiceModelImpl(
      {required this.title,
      required this.value,
      this.isSelected = false,
      this.isRecommended = false});

  factory _$SettingChoiceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingChoiceModelImplFromJson(json);

  @override
  final String title;
  @override
  final String value;
  @override
  @JsonKey()
  final bool isSelected;
  @override
  @JsonKey()
  final bool isRecommended;

  @override
  String toString() {
    return 'SettingChoiceModel(title: $title, value: $value, isSelected: $isSelected, isRecommended: $isRecommended)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingChoiceModelImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected) &&
            (identical(other.isRecommended, isRecommended) ||
                other.isRecommended == isRecommended));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, title, value, isSelected, isRecommended);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingChoiceModelImplCopyWith<_$SettingChoiceModelImpl> get copyWith =>
      __$$SettingChoiceModelImplCopyWithImpl<_$SettingChoiceModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingChoiceModelImplToJson(
      this,
    );
  }
}

abstract class _SettingChoiceModel implements SettingChoiceModel {
  const factory _SettingChoiceModel(
      {required final String title,
      required final String value,
      final bool isSelected,
      final bool isRecommended}) = _$SettingChoiceModelImpl;

  factory _SettingChoiceModel.fromJson(Map<String, dynamic> json) =
      _$SettingChoiceModelImpl.fromJson;

  @override
  String get title;
  @override
  String get value;
  @override
  bool get isSelected;
  @override
  bool get isRecommended;
  @override
  @JsonKey(ignore: true)
  _$$SettingChoiceModelImplCopyWith<_$SettingChoiceModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
