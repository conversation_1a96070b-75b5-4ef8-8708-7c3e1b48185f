import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/common/pagination_model.dart';

part 'recharge_record_model.freezed.dart';
part 'recharge_record_model.g.dart';

/// 充值记录状态
enum RechargeRecordStatus {
  /// 永久
  permanent,

  /// 有效
  valid,

  /// 已过期
  expired;
}

@freezed
class RechargeRecordModel with _$RechargeRecordModel {
  const factory RechargeRecordModel({
    required List<RechargeRecordItem> items,
    required PaginationModel pagination,
  }) = _RechargeRecordModel;

  factory RechargeRecordModel.fromJson(Map<String, dynamic> json) =>
      _$RechargeRecordModelFromJson(json);
}

@freezed
class RechargeRecordItem with _$RechargeRecordItem {
  const factory RechargeRecordItem({
    required String id,
    required String userId,
    required String packageType,
    required String effectiveTime,
    required int total,
    required int used,
    required String expirationTime,
    required String status,
  }) = _RechargeRecordItem;

  factory RechargeRecordItem.fromJson(Map<String, dynamic> json) =>
      _$RechargeRecordItemFromJson(json);

  static RechargeRecordStatus getStatusEnum(String status) {
    if (status == '永久') {
      return RechargeRecordStatus.permanent;
    } else if (status == '有效') {
      return RechargeRecordStatus.valid;
    } else {
      return RechargeRecordStatus.expired;
    }
  }
}
