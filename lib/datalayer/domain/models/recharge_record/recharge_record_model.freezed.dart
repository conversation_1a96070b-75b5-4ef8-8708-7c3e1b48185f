// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recharge_record_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RechargeRecordModel _$RechargeRecordModelFromJson(Map<String, dynamic> json) {
  return _RechargeRecordModel.fromJson(json);
}

/// @nodoc
mixin _$RechargeRecordModel {
  List<RechargeRecordItem> get items => throw _privateConstructorUsedError;
  PaginationModel get pagination => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RechargeRecordModelCopyWith<RechargeRecordModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RechargeRecordModelCopyWith<$Res> {
  factory $RechargeRecordModelCopyWith(
          RechargeRecordModel value, $Res Function(RechargeRecordModel) then) =
      _$RechargeRecordModelCopyWithImpl<$Res, RechargeRecordModel>;
  @useResult
  $Res call({List<RechargeRecordItem> items, PaginationModel pagination});

  $PaginationModelCopyWith<$Res> get pagination;
}

/// @nodoc
class _$RechargeRecordModelCopyWithImpl<$Res, $Val extends RechargeRecordModel>
    implements $RechargeRecordModelCopyWith<$Res> {
  _$RechargeRecordModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? pagination = null,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<RechargeRecordItem>,
      pagination: null == pagination
          ? _value.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as PaginationModel,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationModelCopyWith<$Res> get pagination {
    return $PaginationModelCopyWith<$Res>(_value.pagination, (value) {
      return _then(_value.copyWith(pagination: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RechargeRecordModelImplCopyWith<$Res>
    implements $RechargeRecordModelCopyWith<$Res> {
  factory _$$RechargeRecordModelImplCopyWith(_$RechargeRecordModelImpl value,
          $Res Function(_$RechargeRecordModelImpl) then) =
      __$$RechargeRecordModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<RechargeRecordItem> items, PaginationModel pagination});

  @override
  $PaginationModelCopyWith<$Res> get pagination;
}

/// @nodoc
class __$$RechargeRecordModelImplCopyWithImpl<$Res>
    extends _$RechargeRecordModelCopyWithImpl<$Res, _$RechargeRecordModelImpl>
    implements _$$RechargeRecordModelImplCopyWith<$Res> {
  __$$RechargeRecordModelImplCopyWithImpl(_$RechargeRecordModelImpl _value,
      $Res Function(_$RechargeRecordModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? pagination = null,
  }) {
    return _then(_$RechargeRecordModelImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<RechargeRecordItem>,
      pagination: null == pagination
          ? _value.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as PaginationModel,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RechargeRecordModelImpl implements _RechargeRecordModel {
  const _$RechargeRecordModelImpl(
      {required final List<RechargeRecordItem> items, required this.pagination})
      : _items = items;

  factory _$RechargeRecordModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RechargeRecordModelImplFromJson(json);

  final List<RechargeRecordItem> _items;
  @override
  List<RechargeRecordItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  final PaginationModel pagination;

  @override
  String toString() {
    return 'RechargeRecordModel(items: $items, pagination: $pagination)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RechargeRecordModelImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.pagination, pagination) ||
                other.pagination == pagination));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), pagination);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RechargeRecordModelImplCopyWith<_$RechargeRecordModelImpl> get copyWith =>
      __$$RechargeRecordModelImplCopyWithImpl<_$RechargeRecordModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RechargeRecordModelImplToJson(
      this,
    );
  }
}

abstract class _RechargeRecordModel implements RechargeRecordModel {
  const factory _RechargeRecordModel(
      {required final List<RechargeRecordItem> items,
      required final PaginationModel pagination}) = _$RechargeRecordModelImpl;

  factory _RechargeRecordModel.fromJson(Map<String, dynamic> json) =
      _$RechargeRecordModelImpl.fromJson;

  @override
  List<RechargeRecordItem> get items;
  @override
  PaginationModel get pagination;
  @override
  @JsonKey(ignore: true)
  _$$RechargeRecordModelImplCopyWith<_$RechargeRecordModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RechargeRecordItem _$RechargeRecordItemFromJson(Map<String, dynamic> json) {
  return _RechargeRecordItem.fromJson(json);
}

/// @nodoc
mixin _$RechargeRecordItem {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get packageType => throw _privateConstructorUsedError;
  String get effectiveTime => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  int get used => throw _privateConstructorUsedError;
  String get expirationTime => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RechargeRecordItemCopyWith<RechargeRecordItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RechargeRecordItemCopyWith<$Res> {
  factory $RechargeRecordItemCopyWith(
          RechargeRecordItem value, $Res Function(RechargeRecordItem) then) =
      _$RechargeRecordItemCopyWithImpl<$Res, RechargeRecordItem>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String packageType,
      String effectiveTime,
      int total,
      int used,
      String expirationTime,
      String status});
}

/// @nodoc
class _$RechargeRecordItemCopyWithImpl<$Res, $Val extends RechargeRecordItem>
    implements $RechargeRecordItemCopyWith<$Res> {
  _$RechargeRecordItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? packageType = null,
    Object? effectiveTime = null,
    Object? total = null,
    Object? used = null,
    Object? expirationTime = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      packageType: null == packageType
          ? _value.packageType
          : packageType // ignore: cast_nullable_to_non_nullable
              as String,
      effectiveTime: null == effectiveTime
          ? _value.effectiveTime
          : effectiveTime // ignore: cast_nullable_to_non_nullable
              as String,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      used: null == used
          ? _value.used
          : used // ignore: cast_nullable_to_non_nullable
              as int,
      expirationTime: null == expirationTime
          ? _value.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RechargeRecordItemImplCopyWith<$Res>
    implements $RechargeRecordItemCopyWith<$Res> {
  factory _$$RechargeRecordItemImplCopyWith(_$RechargeRecordItemImpl value,
          $Res Function(_$RechargeRecordItemImpl) then) =
      __$$RechargeRecordItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String packageType,
      String effectiveTime,
      int total,
      int used,
      String expirationTime,
      String status});
}

/// @nodoc
class __$$RechargeRecordItemImplCopyWithImpl<$Res>
    extends _$RechargeRecordItemCopyWithImpl<$Res, _$RechargeRecordItemImpl>
    implements _$$RechargeRecordItemImplCopyWith<$Res> {
  __$$RechargeRecordItemImplCopyWithImpl(_$RechargeRecordItemImpl _value,
      $Res Function(_$RechargeRecordItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? packageType = null,
    Object? effectiveTime = null,
    Object? total = null,
    Object? used = null,
    Object? expirationTime = null,
    Object? status = null,
  }) {
    return _then(_$RechargeRecordItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      packageType: null == packageType
          ? _value.packageType
          : packageType // ignore: cast_nullable_to_non_nullable
              as String,
      effectiveTime: null == effectiveTime
          ? _value.effectiveTime
          : effectiveTime // ignore: cast_nullable_to_non_nullable
              as String,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      used: null == used
          ? _value.used
          : used // ignore: cast_nullable_to_non_nullable
              as int,
      expirationTime: null == expirationTime
          ? _value.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RechargeRecordItemImpl implements _RechargeRecordItem {
  const _$RechargeRecordItemImpl(
      {required this.id,
      required this.userId,
      required this.packageType,
      required this.effectiveTime,
      required this.total,
      required this.used,
      required this.expirationTime,
      required this.status});

  factory _$RechargeRecordItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$RechargeRecordItemImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String packageType;
  @override
  final String effectiveTime;
  @override
  final int total;
  @override
  final int used;
  @override
  final String expirationTime;
  @override
  final String status;

  @override
  String toString() {
    return 'RechargeRecordItem(id: $id, userId: $userId, packageType: $packageType, effectiveTime: $effectiveTime, total: $total, used: $used, expirationTime: $expirationTime, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RechargeRecordItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.packageType, packageType) ||
                other.packageType == packageType) &&
            (identical(other.effectiveTime, effectiveTime) ||
                other.effectiveTime == effectiveTime) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.used, used) || other.used == used) &&
            (identical(other.expirationTime, expirationTime) ||
                other.expirationTime == expirationTime) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, packageType,
      effectiveTime, total, used, expirationTime, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RechargeRecordItemImplCopyWith<_$RechargeRecordItemImpl> get copyWith =>
      __$$RechargeRecordItemImplCopyWithImpl<_$RechargeRecordItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RechargeRecordItemImplToJson(
      this,
    );
  }
}

abstract class _RechargeRecordItem implements RechargeRecordItem {
  const factory _RechargeRecordItem(
      {required final String id,
      required final String userId,
      required final String packageType,
      required final String effectiveTime,
      required final int total,
      required final int used,
      required final String expirationTime,
      required final String status}) = _$RechargeRecordItemImpl;

  factory _RechargeRecordItem.fromJson(Map<String, dynamic> json) =
      _$RechargeRecordItemImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get packageType;
  @override
  String get effectiveTime;
  @override
  int get total;
  @override
  int get used;
  @override
  String get expirationTime;
  @override
  String get status;
  @override
  @JsonKey(ignore: true)
  _$$RechargeRecordItemImplCopyWith<_$RechargeRecordItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
