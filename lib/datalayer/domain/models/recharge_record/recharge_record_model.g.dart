// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recharge_record_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RechargeRecordModelImpl _$$RechargeRecordModelImplFromJson(
        Map<String, dynamic> json) =>
    _$RechargeRecordModelImpl(
      items: (json['items'] as List<dynamic>)
          .map((e) => RechargeRecordItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      pagination:
          PaginationModel.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$RechargeRecordModelImplToJson(
        _$RechargeRecordModelImpl instance) =>
    <String, dynamic>{
      'items': instance.items,
      'pagination': instance.pagination,
    };

_$RechargeRecordItemImpl _$$RechargeRecordItemImplFromJson(
        Map<String, dynamic> json) =>
    _$RechargeRecordItemImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      packageType: json['packageType'] as String,
      effectiveTime: json['effectiveTime'] as String,
      total: (json['total'] as num).toInt(),
      used: (json['used'] as num).toInt(),
      expirationTime: json['expirationTime'] as String,
      status: json['status'] as String,
    );

Map<String, dynamic> _$$RechargeRecordItemImplToJson(
        _$RechargeRecordItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'packageType': instance.packageType,
      'effectiveTime': instance.effectiveTime,
      'total': instance.total,
      'used': instance.used,
      'expirationTime': instance.expirationTime,
      'status': instance.status,
    };
