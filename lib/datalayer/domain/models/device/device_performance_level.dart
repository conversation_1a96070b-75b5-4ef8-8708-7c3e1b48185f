enum DevicePerformanceLevel {
  veryLow(0), // 性能极低
  low(1), // 性能较低
  medium(2), // 性能中等
  high(3), // 性能较高
  veryHigh(4); // 性能极高

  final int value;
  const DevicePerformanceLevel(this.value);

  // 从数值获取枚举值
  static DevicePerformanceLevel fromValue(int value) {
    return DevicePerformanceLevel.values.firstWhere(
      (level) => level.value == value,
      orElse: () => DevicePerformanceLevel.medium,
    );
  }

  // 判断性能是否高于指定等级
  bool isHigherThan(DevicePerformanceLevel other) {
    return value > other.value;
  }

  // 判断性能是否低于指定等级
  bool isLowerThan(DevicePerformanceLevel other) {
    return value < other.value;
  }

  // 获取所有可用的性能等级值
  static List<int> get allValues => values.map((e) => e.value).toList();

  // 获取最小性能等级
  static DevicePerformanceLevel get min =>
      values.reduce((a, b) => a.value < b.value ? a : b);

  // 获取最大性能等级
  static DevicePerformanceLevel get max =>
      values.reduce((a, b) => a.value > b.value ? a : b);

  // 转换为JSON
  int toJson() => value;

  @override
  String toString() => 'DevicePerformanceLevel.$name($value)';
}
