// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_information.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeviceInformationImpl _$$DeviceInformationImplFromJson(
        Map<String, dynamic> json) =>
    _$DeviceInformationImpl(
      deviceLevel: $enumDecodeNullable(
              _$DevicePerformanceLevelEnumMap, json['deviceLevel']) ??
          DevicePerformanceLevel.low,
    );

Map<String, dynamic> _$$DeviceInformationImplToJson(
        _$DeviceInformationImpl instance) =>
    <String, dynamic>{
      'deviceLevel': instance.deviceLevel,
    };

const _$DevicePerformanceLevelEnumMap = {
  DevicePerformanceLevel.veryLow: 'veryLow',
  DevicePerformanceLevel.low: 'low',
  DevicePerformanceLevel.medium: 'medium',
  DevicePerformanceLevel.high: 'high',
  DevicePerformanceLevel.veryHigh: 'veryHigh',
};
