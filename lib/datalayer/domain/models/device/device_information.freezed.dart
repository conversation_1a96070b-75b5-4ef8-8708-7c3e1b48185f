// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_information.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DeviceInformation _$DeviceInformationFromJson(Map<String, dynamic> json) {
  return _DeviceInformation.fromJson(json);
}

/// @nodoc
mixin _$DeviceInformation {
  DevicePerformanceLevel get deviceLevel => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DeviceInformationCopyWith<DeviceInformation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceInformationCopyWith<$Res> {
  factory $DeviceInformationCopyWith(
          DeviceInformation value, $Res Function(DeviceInformation) then) =
      _$DeviceInformationCopyWithImpl<$Res, DeviceInformation>;
  @useResult
  $Res call({DevicePerformanceLevel deviceLevel});
}

/// @nodoc
class _$DeviceInformationCopyWithImpl<$Res, $Val extends DeviceInformation>
    implements $DeviceInformationCopyWith<$Res> {
  _$DeviceInformationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceLevel = null,
  }) {
    return _then(_value.copyWith(
      deviceLevel: null == deviceLevel
          ? _value.deviceLevel
          : deviceLevel // ignore: cast_nullable_to_non_nullable
              as DevicePerformanceLevel,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceInformationImplCopyWith<$Res>
    implements $DeviceInformationCopyWith<$Res> {
  factory _$$DeviceInformationImplCopyWith(_$DeviceInformationImpl value,
          $Res Function(_$DeviceInformationImpl) then) =
      __$$DeviceInformationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DevicePerformanceLevel deviceLevel});
}

/// @nodoc
class __$$DeviceInformationImplCopyWithImpl<$Res>
    extends _$DeviceInformationCopyWithImpl<$Res, _$DeviceInformationImpl>
    implements _$$DeviceInformationImplCopyWith<$Res> {
  __$$DeviceInformationImplCopyWithImpl(_$DeviceInformationImpl _value,
      $Res Function(_$DeviceInformationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceLevel = null,
  }) {
    return _then(_$DeviceInformationImpl(
      deviceLevel: null == deviceLevel
          ? _value.deviceLevel
          : deviceLevel // ignore: cast_nullable_to_non_nullable
              as DevicePerformanceLevel,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceInformationImpl implements _DeviceInformation {
  const _$DeviceInformationImpl(
      {this.deviceLevel = DevicePerformanceLevel.low});

  factory _$DeviceInformationImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceInformationImplFromJson(json);

  @override
  @JsonKey()
  final DevicePerformanceLevel deviceLevel;

  @override
  String toString() {
    return 'DeviceInformation(deviceLevel: $deviceLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceInformationImpl &&
            (identical(other.deviceLevel, deviceLevel) ||
                other.deviceLevel == deviceLevel));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, deviceLevel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceInformationImplCopyWith<_$DeviceInformationImpl> get copyWith =>
      __$$DeviceInformationImplCopyWithImpl<_$DeviceInformationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceInformationImplToJson(
      this,
    );
  }
}

abstract class _DeviceInformation implements DeviceInformation {
  const factory _DeviceInformation({final DevicePerformanceLevel deviceLevel}) =
      _$DeviceInformationImpl;

  factory _DeviceInformation.fromJson(Map<String, dynamic> json) =
      _$DeviceInformationImpl.fromJson;

  @override
  DevicePerformanceLevel get deviceLevel;
  @override
  @JsonKey(ignore: true)
  _$$DeviceInformationImplCopyWith<_$DeviceInformationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
