import 'package:freezed_annotation/freezed_annotation.dart';

import 'device_performance_level.dart';

part 'device_information.freezed.dart';
part 'device_information.g.dart';

@freezed
class DeviceInformation with _$DeviceInformation {
  const factory DeviceInformation({
    @Default(DevicePerformanceLevel.low) DevicePerformanceLevel deviceLevel,
  }) = _DeviceInformation;

  factory DeviceInformation.fromJson(Map<String, dynamic> json) =>
      _$DeviceInformationFromJson(json);
}
