// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_upload_urls.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MediaUploadUrls _$MediaUploadUrlsFromJson(Map<String, dynamic> json) {
  return _MediaUploadUrls.fromJson(json);
}

/// @nodoc
mixin _$MediaUploadUrls {
  String get public => throw _privateConstructorUsedError; // 公网 OSS URL
  String? get internal =>
      throw _privateConstructorUsedError; // 内网 OSS URL（如果有配置）
  String? get custom => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MediaUploadUrlsCopyWith<MediaUploadUrls> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaUploadUrlsCopyWith<$Res> {
  factory $MediaUploadUrlsCopyWith(
          MediaUploadUrls value, $Res Function(MediaUploadUrls) then) =
      _$MediaUploadUrlsCopyWithImpl<$Res, MediaUploadUrls>;
  @useResult
  $Res call({String public, String? internal, String? custom});
}

/// @nodoc
class _$MediaUploadUrlsCopyWithImpl<$Res, $Val extends MediaUploadUrls>
    implements $MediaUploadUrlsCopyWith<$Res> {
  _$MediaUploadUrlsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? public = null,
    Object? internal = freezed,
    Object? custom = freezed,
  }) {
    return _then(_value.copyWith(
      public: null == public
          ? _value.public
          : public // ignore: cast_nullable_to_non_nullable
              as String,
      internal: freezed == internal
          ? _value.internal
          : internal // ignore: cast_nullable_to_non_nullable
              as String?,
      custom: freezed == custom
          ? _value.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MediaUploadUrlsImplCopyWith<$Res>
    implements $MediaUploadUrlsCopyWith<$Res> {
  factory _$$MediaUploadUrlsImplCopyWith(_$MediaUploadUrlsImpl value,
          $Res Function(_$MediaUploadUrlsImpl) then) =
      __$$MediaUploadUrlsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String public, String? internal, String? custom});
}

/// @nodoc
class __$$MediaUploadUrlsImplCopyWithImpl<$Res>
    extends _$MediaUploadUrlsCopyWithImpl<$Res, _$MediaUploadUrlsImpl>
    implements _$$MediaUploadUrlsImplCopyWith<$Res> {
  __$$MediaUploadUrlsImplCopyWithImpl(
      _$MediaUploadUrlsImpl _value, $Res Function(_$MediaUploadUrlsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? public = null,
    Object? internal = freezed,
    Object? custom = freezed,
  }) {
    return _then(_$MediaUploadUrlsImpl(
      public: null == public
          ? _value.public
          : public // ignore: cast_nullable_to_non_nullable
              as String,
      internal: freezed == internal
          ? _value.internal
          : internal // ignore: cast_nullable_to_non_nullable
              as String?,
      custom: freezed == custom
          ? _value.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaUploadUrlsImpl implements _MediaUploadUrls {
  const _$MediaUploadUrlsImpl(
      {required this.public, required this.internal, required this.custom});

  factory _$MediaUploadUrlsImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaUploadUrlsImplFromJson(json);

  @override
  final String public;
// 公网 OSS URL
  @override
  final String? internal;
// 内网 OSS URL（如果有配置）
  @override
  final String? custom;

  @override
  String toString() {
    return 'MediaUploadUrls(public: $public, internal: $internal, custom: $custom)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaUploadUrlsImpl &&
            (identical(other.public, public) || other.public == public) &&
            (identical(other.internal, internal) ||
                other.internal == internal) &&
            (identical(other.custom, custom) || other.custom == custom));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, public, internal, custom);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaUploadUrlsImplCopyWith<_$MediaUploadUrlsImpl> get copyWith =>
      __$$MediaUploadUrlsImplCopyWithImpl<_$MediaUploadUrlsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaUploadUrlsImplToJson(
      this,
    );
  }
}

abstract class _MediaUploadUrls implements MediaUploadUrls {
  const factory _MediaUploadUrls(
      {required final String public,
      required final String? internal,
      required final String? custom}) = _$MediaUploadUrlsImpl;

  factory _MediaUploadUrls.fromJson(Map<String, dynamic> json) =
      _$MediaUploadUrlsImpl.fromJson;

  @override
  String get public;
  @override // 公网 OSS URL
  String? get internal;
  @override // 内网 OSS URL（如果有配置）
  String? get custom;
  @override
  @JsonKey(ignore: true)
  _$$MediaUploadUrlsImplCopyWith<_$MediaUploadUrlsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
