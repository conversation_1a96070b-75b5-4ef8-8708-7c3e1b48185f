// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_upload_urls.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MediaUploadUrlsImpl _$$MediaUploadUrlsImplFromJson(
        Map<String, dynamic> json) =>
    _$MediaUploadUrlsImpl(
      public: json['public'] as String,
      internal: json['internal'] as String?,
      custom: json['custom'] as String?,
    );

Map<String, dynamic> _$$MediaUploadUrlsImplToJson(
        _$MediaUploadUrlsImpl instance) =>
    <String, dynamic>{
      'public': instance.public,
      'internal': instance.internal,
      'custom': instance.custom,
    };
