// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/media_upload/media_upload_urls.dart';

part 'media_upload_info_response.freezed.dart';
part 'media_upload_info_response.g.dart';

/// 获取媒体上传信息响应模型
@freezed
class MediaUploadInfoResponse with _$MediaUploadInfoResponse {
  const factory MediaUploadInfoResponse({
    @JsonKey(name: 'object_name') required String objectName, // 对象名
    @JsonKey(name: 'upload_url') required String uploadUrl, // 带有临时授权的图片上传 URL
    required MediaUploadUrls urls, // 上传成功后返回的 URL 信息
  }) = _MediaUploadInfoResponse;

  factory MediaUploadInfoResponse.fromJson(Map<String, dynamic> json) =>
      _$MediaUploadInfoResponseFromJson(json);
}

/*
{
  "object_name":"xxx/xxx/332f88a1e0579e2cec8c15a78.jpg",//对象名
  "upload_url":"",//带有临时授权的图片上传URL
  "urls":{ // 上传成功后图片的URL
  "public":"",//公网OSS URL
  "internal":"",//内网OSS URL (如果有配置)
  "custom":""//自定义域名URL(如果有配置)
}
*/
