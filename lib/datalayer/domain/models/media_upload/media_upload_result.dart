import 'package:turing_art/datalayer/domain/models/media_upload/media_upload_info_response.dart';

/// 媒体上传结果类
class MediaUploadResult {
  final bool success;
  final String fileName;
  final MediaUploadInfoResponse? uploadInfo;
  final String? error;

  MediaUploadResult({
    required this.success,
    required this.fileName,
    this.uploadInfo,
    this.error,
  });

  /// 获取公网URL
  String? get publicUrl => uploadInfo?.urls.public;

  @override
  String toString() {
    return 'MediaUploadResult{success: $success, fileName: $fileName, error: $error}';
  }
}
