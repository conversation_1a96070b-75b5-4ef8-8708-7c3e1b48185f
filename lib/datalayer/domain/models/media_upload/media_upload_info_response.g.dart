// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_upload_info_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MediaUploadInfoResponseImpl _$$MediaUploadInfoResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MediaUploadInfoResponseImpl(
      objectName: json['object_name'] as String,
      uploadUrl: json['upload_url'] as String,
      urls: MediaUploadUrls.from<PERSON>son(json['urls'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$MediaUploadInfoResponseImplToJson(
        _$MediaUploadInfoResponseImpl instance) =>
    <String, dynamic>{
      'object_name': instance.objectName,
      'upload_url': instance.uploadUrl,
      'urls': instance.urls,
    };
