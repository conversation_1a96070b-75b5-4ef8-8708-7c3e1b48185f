// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_upload_info_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MediaUploadInfoResponse _$MediaUploadInfoResponseFromJson(
    Map<String, dynamic> json) {
  return _MediaUploadInfoResponse.fromJson(json);
}

/// @nodoc
mixin _$MediaUploadInfoResponse {
  @JsonKey(name: 'object_name')
  String get objectName => throw _privateConstructorUsedError; // 对象名
  @JsonKey(name: 'upload_url')
  String get uploadUrl => throw _privateConstructorUsedError; // 带有临时授权的图片上传 URL
  MediaUploadUrls get urls => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MediaUploadInfoResponseCopyWith<MediaUploadInfoResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaUploadInfoResponseCopyWith<$Res> {
  factory $MediaUploadInfoResponseCopyWith(MediaUploadInfoResponse value,
          $Res Function(MediaUploadInfoResponse) then) =
      _$MediaUploadInfoResponseCopyWithImpl<$Res, MediaUploadInfoResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'object_name') String objectName,
      @JsonKey(name: 'upload_url') String uploadUrl,
      MediaUploadUrls urls});

  $MediaUploadUrlsCopyWith<$Res> get urls;
}

/// @nodoc
class _$MediaUploadInfoResponseCopyWithImpl<$Res,
        $Val extends MediaUploadInfoResponse>
    implements $MediaUploadInfoResponseCopyWith<$Res> {
  _$MediaUploadInfoResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? objectName = null,
    Object? uploadUrl = null,
    Object? urls = null,
  }) {
    return _then(_value.copyWith(
      objectName: null == objectName
          ? _value.objectName
          : objectName // ignore: cast_nullable_to_non_nullable
              as String,
      uploadUrl: null == uploadUrl
          ? _value.uploadUrl
          : uploadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      urls: null == urls
          ? _value.urls
          : urls // ignore: cast_nullable_to_non_nullable
              as MediaUploadUrls,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MediaUploadUrlsCopyWith<$Res> get urls {
    return $MediaUploadUrlsCopyWith<$Res>(_value.urls, (value) {
      return _then(_value.copyWith(urls: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MediaUploadInfoResponseImplCopyWith<$Res>
    implements $MediaUploadInfoResponseCopyWith<$Res> {
  factory _$$MediaUploadInfoResponseImplCopyWith(
          _$MediaUploadInfoResponseImpl value,
          $Res Function(_$MediaUploadInfoResponseImpl) then) =
      __$$MediaUploadInfoResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'object_name') String objectName,
      @JsonKey(name: 'upload_url') String uploadUrl,
      MediaUploadUrls urls});

  @override
  $MediaUploadUrlsCopyWith<$Res> get urls;
}

/// @nodoc
class __$$MediaUploadInfoResponseImplCopyWithImpl<$Res>
    extends _$MediaUploadInfoResponseCopyWithImpl<$Res,
        _$MediaUploadInfoResponseImpl>
    implements _$$MediaUploadInfoResponseImplCopyWith<$Res> {
  __$$MediaUploadInfoResponseImplCopyWithImpl(
      _$MediaUploadInfoResponseImpl _value,
      $Res Function(_$MediaUploadInfoResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? objectName = null,
    Object? uploadUrl = null,
    Object? urls = null,
  }) {
    return _then(_$MediaUploadInfoResponseImpl(
      objectName: null == objectName
          ? _value.objectName
          : objectName // ignore: cast_nullable_to_non_nullable
              as String,
      uploadUrl: null == uploadUrl
          ? _value.uploadUrl
          : uploadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      urls: null == urls
          ? _value.urls
          : urls // ignore: cast_nullable_to_non_nullable
              as MediaUploadUrls,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaUploadInfoResponseImpl implements _MediaUploadInfoResponse {
  const _$MediaUploadInfoResponseImpl(
      {@JsonKey(name: 'object_name') required this.objectName,
      @JsonKey(name: 'upload_url') required this.uploadUrl,
      required this.urls});

  factory _$MediaUploadInfoResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaUploadInfoResponseImplFromJson(json);

  @override
  @JsonKey(name: 'object_name')
  final String objectName;
// 对象名
  @override
  @JsonKey(name: 'upload_url')
  final String uploadUrl;
// 带有临时授权的图片上传 URL
  @override
  final MediaUploadUrls urls;

  @override
  String toString() {
    return 'MediaUploadInfoResponse(objectName: $objectName, uploadUrl: $uploadUrl, urls: $urls)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaUploadInfoResponseImpl &&
            (identical(other.objectName, objectName) ||
                other.objectName == objectName) &&
            (identical(other.uploadUrl, uploadUrl) ||
                other.uploadUrl == uploadUrl) &&
            (identical(other.urls, urls) || other.urls == urls));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, objectName, uploadUrl, urls);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaUploadInfoResponseImplCopyWith<_$MediaUploadInfoResponseImpl>
      get copyWith => __$$MediaUploadInfoResponseImplCopyWithImpl<
          _$MediaUploadInfoResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaUploadInfoResponseImplToJson(
      this,
    );
  }
}

abstract class _MediaUploadInfoResponse implements MediaUploadInfoResponse {
  const factory _MediaUploadInfoResponse(
      {@JsonKey(name: 'object_name') required final String objectName,
      @JsonKey(name: 'upload_url') required final String uploadUrl,
      required final MediaUploadUrls urls}) = _$MediaUploadInfoResponseImpl;

  factory _MediaUploadInfoResponse.fromJson(Map<String, dynamic> json) =
      _$MediaUploadInfoResponseImpl.fromJson;

  @override
  @JsonKey(name: 'object_name')
  String get objectName;
  @override // 对象名
  @JsonKey(name: 'upload_url')
  String get uploadUrl;
  @override // 带有临时授权的图片上传 URL
  MediaUploadUrls get urls;
  @override
  @JsonKey(ignore: true)
  _$$MediaUploadInfoResponseImplCopyWith<_$MediaUploadInfoResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
