import 'package:freezed_annotation/freezed_annotation.dart';

part 'media_upload_urls.freezed.dart';
part 'media_upload_urls.g.dart';

@freezed
class MediaUploadUrls with _$MediaUploadUrls {
  const factory MediaUploadUrls({
    required String public, // 公网 OSS URL
    required String? internal, // 内网 OSS URL（如果有配置）
    required String? custom, // 自定义域名 URL（如果有配置）
  }) = _MediaUploadUrls;

  factory MediaUploadUrls.fromJson(Map<String, dynamic> json) =>
      _$MediaUploadUrlsFromJson(json);
}
