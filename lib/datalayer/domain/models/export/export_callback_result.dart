/// 导出Token回调结果数据模型
class ExportTokenCallbackResult {
  /// 是否成功
  final bool success;

  /// 成功时的tokenId
  final String? tokenId;

  /// 失败时的错误码
  final int? errorCode;

  /// 失败时的错误信息
  final String? errorMessage;

  /// 成功构造函数
  ExportTokenCallbackResult.success({required String this.tokenId})
      : success = true,
        errorCode = null,
        errorMessage = null;

  /// 失败构造函数
  ExportTokenCallbackResult.failure({
    required int this.errorCode,
    required String this.errorMessage,
  })  : success = false,
        tokenId = null;

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    if (success) {
      return {
        'success': true,
        'tokenId': tokenId,
      };
    } else {
      return {
        'success': false,
        'errorCode': errorCode,
        'errorMessage': errorMessage,
      };
    }
  }

  /// 从JSON格式创建实例
  factory ExportTokenCallbackResult.fromJson(Map<String, dynamic> json) {
    final success = json['success'] as bool;
    if (success) {
      return ExportTokenCallbackResult.success(
        tokenId: json['tokenId'] as String,
      );
    } else {
      return ExportTokenCallbackResult.failure(
        errorCode: json['errorCode'] as int,
        errorMessage: json['errorMessage'] as String,
      );
    }
  }

  @override
  String toString() {
    if (success) {
      return 'ExportTokenCallbackResult(success: true, tokenId: $tokenId)';
    } else {
      return 'ExportTokenCallbackResult(success: false, errorCode: $errorCode, errorMessage: $errorMessage)';
    }
  }
}
