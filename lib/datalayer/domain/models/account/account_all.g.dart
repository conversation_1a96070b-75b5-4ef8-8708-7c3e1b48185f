// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_all.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AccountAllImpl _$$AccountAllImplFromJson(Map<String, dynamic> json) =>
    _$AccountAllImpl(
      exportInfo:
          AccountInfo.fromJson(json['export_count'] as Map<String, dynamic>),
      aigcInfo: json['aigc_count'] == null
          ? null
          : AccountInfo.fromJson(json['aigc_count'] as Map<String, dynamic>),
      sampleInfo: json['sample_count'] == null
          ? null
          : AccountInfo.fromJson(json['sample_count'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AccountAllImplToJson(_$AccountAllImpl instance) =>
    <String, dynamic>{
      'export_count': instance.exportInfo,
      'aigc_count': instance.aigcInfo,
      'sample_count': instance.sampleInfo,
    };

_$AccountInfoImpl _$$AccountInfoImplFromJson(Map<String, dynamic> json) =>
    _$AccountInfoImpl(
      total: (json['total'] as num?)?.toInt() ?? 0,
      available: (json['available'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$AccountInfoImplToJson(_$AccountInfoImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'available': instance.available,
    };
