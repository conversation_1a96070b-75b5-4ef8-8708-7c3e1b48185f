// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_all.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountAll _$AccountAllFromJson(Map<String, dynamic> json) {
  return _AccountAll.fromJson(json);
}

/// @nodoc
mixin _$AccountAll {
// 导出相关统计
  @JsonKey(name: 'export_count')
  AccountInfo get exportInfo => throw _privateConstructorUsedError; // 积分相关统计
  @JsonKey(name: 'aigc_count')
  AccountInfo? get aigcInfo => throw _privateConstructorUsedError; // 小样相关统计
  @JsonKey(name: 'sample_count')
  AccountInfo? get sampleInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AccountAllCopyWith<AccountAll> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountAllCopyWith<$Res> {
  factory $AccountAllCopyWith(
          AccountAll value, $Res Function(AccountAll) then) =
      _$AccountAllCopyWithImpl<$Res, AccountAll>;
  @useResult
  $Res call(
      {@JsonKey(name: 'export_count') AccountInfo exportInfo,
      @JsonKey(name: 'aigc_count') AccountInfo? aigcInfo,
      @JsonKey(name: 'sample_count') AccountInfo? sampleInfo});

  $AccountInfoCopyWith<$Res> get exportInfo;
  $AccountInfoCopyWith<$Res>? get aigcInfo;
  $AccountInfoCopyWith<$Res>? get sampleInfo;
}

/// @nodoc
class _$AccountAllCopyWithImpl<$Res, $Val extends AccountAll>
    implements $AccountAllCopyWith<$Res> {
  _$AccountAllCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exportInfo = null,
    Object? aigcInfo = freezed,
    Object? sampleInfo = freezed,
  }) {
    return _then(_value.copyWith(
      exportInfo: null == exportInfo
          ? _value.exportInfo
          : exportInfo // ignore: cast_nullable_to_non_nullable
              as AccountInfo,
      aigcInfo: freezed == aigcInfo
          ? _value.aigcInfo
          : aigcInfo // ignore: cast_nullable_to_non_nullable
              as AccountInfo?,
      sampleInfo: freezed == sampleInfo
          ? _value.sampleInfo
          : sampleInfo // ignore: cast_nullable_to_non_nullable
              as AccountInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AccountInfoCopyWith<$Res> get exportInfo {
    return $AccountInfoCopyWith<$Res>(_value.exportInfo, (value) {
      return _then(_value.copyWith(exportInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AccountInfoCopyWith<$Res>? get aigcInfo {
    if (_value.aigcInfo == null) {
      return null;
    }

    return $AccountInfoCopyWith<$Res>(_value.aigcInfo!, (value) {
      return _then(_value.copyWith(aigcInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AccountInfoCopyWith<$Res>? get sampleInfo {
    if (_value.sampleInfo == null) {
      return null;
    }

    return $AccountInfoCopyWith<$Res>(_value.sampleInfo!, (value) {
      return _then(_value.copyWith(sampleInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AccountAllImplCopyWith<$Res>
    implements $AccountAllCopyWith<$Res> {
  factory _$$AccountAllImplCopyWith(
          _$AccountAllImpl value, $Res Function(_$AccountAllImpl) then) =
      __$$AccountAllImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'export_count') AccountInfo exportInfo,
      @JsonKey(name: 'aigc_count') AccountInfo? aigcInfo,
      @JsonKey(name: 'sample_count') AccountInfo? sampleInfo});

  @override
  $AccountInfoCopyWith<$Res> get exportInfo;
  @override
  $AccountInfoCopyWith<$Res>? get aigcInfo;
  @override
  $AccountInfoCopyWith<$Res>? get sampleInfo;
}

/// @nodoc
class __$$AccountAllImplCopyWithImpl<$Res>
    extends _$AccountAllCopyWithImpl<$Res, _$AccountAllImpl>
    implements _$$AccountAllImplCopyWith<$Res> {
  __$$AccountAllImplCopyWithImpl(
      _$AccountAllImpl _value, $Res Function(_$AccountAllImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exportInfo = null,
    Object? aigcInfo = freezed,
    Object? sampleInfo = freezed,
  }) {
    return _then(_$AccountAllImpl(
      exportInfo: null == exportInfo
          ? _value.exportInfo
          : exportInfo // ignore: cast_nullable_to_non_nullable
              as AccountInfo,
      aigcInfo: freezed == aigcInfo
          ? _value.aigcInfo
          : aigcInfo // ignore: cast_nullable_to_non_nullable
              as AccountInfo?,
      sampleInfo: freezed == sampleInfo
          ? _value.sampleInfo
          : sampleInfo // ignore: cast_nullable_to_non_nullable
              as AccountInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountAllImpl extends _AccountAll {
  const _$AccountAllImpl(
      {@JsonKey(name: 'export_count') required this.exportInfo,
      @JsonKey(name: 'aigc_count') this.aigcInfo,
      @JsonKey(name: 'sample_count') this.sampleInfo})
      : super._();

  factory _$AccountAllImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountAllImplFromJson(json);

// 导出相关统计
  @override
  @JsonKey(name: 'export_count')
  final AccountInfo exportInfo;
// 积分相关统计
  @override
  @JsonKey(name: 'aigc_count')
  final AccountInfo? aigcInfo;
// 小样相关统计
  @override
  @JsonKey(name: 'sample_count')
  final AccountInfo? sampleInfo;

  @override
  String toString() {
    return 'AccountAll(exportInfo: $exportInfo, aigcInfo: $aigcInfo, sampleInfo: $sampleInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountAllImpl &&
            (identical(other.exportInfo, exportInfo) ||
                other.exportInfo == exportInfo) &&
            (identical(other.aigcInfo, aigcInfo) ||
                other.aigcInfo == aigcInfo) &&
            (identical(other.sampleInfo, sampleInfo) ||
                other.sampleInfo == sampleInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, exportInfo, aigcInfo, sampleInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountAllImplCopyWith<_$AccountAllImpl> get copyWith =>
      __$$AccountAllImplCopyWithImpl<_$AccountAllImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountAllImplToJson(
      this,
    );
  }
}

abstract class _AccountAll extends AccountAll {
  const factory _AccountAll(
          {@JsonKey(name: 'export_count') required final AccountInfo exportInfo,
          @JsonKey(name: 'aigc_count') final AccountInfo? aigcInfo,
          @JsonKey(name: 'sample_count') final AccountInfo? sampleInfo}) =
      _$AccountAllImpl;
  const _AccountAll._() : super._();

  factory _AccountAll.fromJson(Map<String, dynamic> json) =
      _$AccountAllImpl.fromJson;

  @override // 导出相关统计
  @JsonKey(name: 'export_count')
  AccountInfo get exportInfo;
  @override // 积分相关统计
  @JsonKey(name: 'aigc_count')
  AccountInfo? get aigcInfo;
  @override // 小样相关统计
  @JsonKey(name: 'sample_count')
  AccountInfo? get sampleInfo;
  @override
  @JsonKey(ignore: true)
  _$$AccountAllImplCopyWith<_$AccountAllImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountInfo _$AccountInfoFromJson(Map<String, dynamic> json) {
  return _AccountInfo.fromJson(json);
}

/// @nodoc
mixin _$AccountInfo {
// 总张数
  int get total => throw _privateConstructorUsedError; // 剩余可用张数
  int get available => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AccountInfoCopyWith<AccountInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountInfoCopyWith<$Res> {
  factory $AccountInfoCopyWith(
          AccountInfo value, $Res Function(AccountInfo) then) =
      _$AccountInfoCopyWithImpl<$Res, AccountInfo>;
  @useResult
  $Res call({int total, int available});
}

/// @nodoc
class _$AccountInfoCopyWithImpl<$Res, $Val extends AccountInfo>
    implements $AccountInfoCopyWith<$Res> {
  _$AccountInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? available = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      available: null == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountInfoImplCopyWith<$Res>
    implements $AccountInfoCopyWith<$Res> {
  factory _$$AccountInfoImplCopyWith(
          _$AccountInfoImpl value, $Res Function(_$AccountInfoImpl) then) =
      __$$AccountInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int total, int available});
}

/// @nodoc
class __$$AccountInfoImplCopyWithImpl<$Res>
    extends _$AccountInfoCopyWithImpl<$Res, _$AccountInfoImpl>
    implements _$$AccountInfoImplCopyWith<$Res> {
  __$$AccountInfoImplCopyWithImpl(
      _$AccountInfoImpl _value, $Res Function(_$AccountInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? available = null,
  }) {
    return _then(_$AccountInfoImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      available: null == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountInfoImpl implements _AccountInfo {
  const _$AccountInfoImpl({this.total = 0, this.available = 0});

  factory _$AccountInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountInfoImplFromJson(json);

// 总张数
  @override
  @JsonKey()
  final int total;
// 剩余可用张数
  @override
  @JsonKey()
  final int available;

  @override
  String toString() {
    return 'AccountInfo(total: $total, available: $available)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountInfoImpl &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.available, available) ||
                other.available == available));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, total, available);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountInfoImplCopyWith<_$AccountInfoImpl> get copyWith =>
      __$$AccountInfoImplCopyWithImpl<_$AccountInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountInfoImplToJson(
      this,
    );
  }
}

abstract class _AccountInfo implements AccountInfo {
  const factory _AccountInfo({final int total, final int available}) =
      _$AccountInfoImpl;

  factory _AccountInfo.fromJson(Map<String, dynamic> json) =
      _$AccountInfoImpl.fromJson;

  @override // 总张数
  int get total;
  @override // 剩余可用张数
  int get available;
  @override
  @JsonKey(ignore: true)
  _$$AccountInfoImplCopyWith<_$AccountInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
