// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_all.freezed.dart';
part 'account_all.g.dart';

// 完整账户信息（包含积分等信息）
@freezed
class AccountAll with _$AccountAll {
  const factory AccountAll({
    // 导出相关统计
    @JsonKey(name: 'export_count') required AccountInfo exportInfo,
    // 积分相关统计
    @JsonKey(name: 'aigc_count') AccountInfo? aigcInfo,
    // 小样相关统计
    @JsonKey(name: 'sample_count') AccountInfo? sampleInfo,
  }) = _AccountAll;
  const AccountAll._();

  factory AccountAll.fromJson(Map<String, dynamic> json) =>
      _$AccountAllFromJson(json);

  static List<AccountAll> fromJsonList(List<Map<String, dynamic>> jsonList) =>
      jsonList.map((e) => AccountAll.fromJson(e)).toList();
}

@freezed
class AccountInfo with _$AccountInfo {
  const factory AccountInfo({
    // 总张数
    @Default(0) int total,
    // 剩余可用张数
    @Default(0) int available,
  }) = _AccountInfo;

  factory AccountInfo.fromJson(Map<String, dynamic> json) =>
      _$AccountInfoFromJson(json);
}
