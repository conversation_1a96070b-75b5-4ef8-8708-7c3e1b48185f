import 'package:freezed_annotation/freezed_annotation.dart';

part 'account.freezed.dart';
part 'account.g.dart';

// 账户信息
@freezed
class Account with _$Account {
  const factory Account({
    // 总张数
    required String total,
    // 剩余可用张数
    required String available,
    // 门店ID
    @Default("") String storeId,
    // 门店名称
    @Default("") String storeName,
    // 角色
    @Default("") String role,
    // 创作者名称
    @Default("") String name,
  }) = _Account;
  const Account._();

  factory Account.fromJson(Map<String, dynamic> json) =>
      _$AccountFromJson(json);

  static List<Account> fromJsonList(List<Map<String, dynamic>> jsonList) =>
      jsonList.map((e) => Account.fromJson(e)).toList();
}
