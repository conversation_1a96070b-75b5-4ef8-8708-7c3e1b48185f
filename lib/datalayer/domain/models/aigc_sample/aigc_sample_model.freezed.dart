// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleModel _$AigcSampleModelFromJson(Map<String, dynamic> json) {
  return _AigcSampleModel.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleModel {
// 打样项目ID
  String get id => throw _privateConstructorUsedError; // 打样项目名称
  String get name => throw _privateConstructorUsedError; // 项目Id
  @JsonKey(name: 'project_id')
  String get projectId => throw _privateConstructorUsedError; // 项目名称
  @JsonKey(name: 'project_name')
  String get projectName => throw _privateConstructorUsedError; // 预设id
  @JsonKey(name: 'preset_id')
  String get presetId => throw _privateConstructorUsedError; // 预设名称
  @JsonKey(name: 'preset_name')
  String get presetName => throw _privateConstructorUsedError; // 预设效果图名称
  @JsonKey(name: 'preset_effect_name')
  String get presetEffectName => throw _privateConstructorUsedError; // 打样原图URL
  @JsonKey(name: 'origin_photo_url')
  String get originPhotoUrl =>
      throw _privateConstructorUsedError; // 大图URL（用于再次打样上报）
  @JsonKey(name: 'large_photo_url')
  String get largePhotoUrl =>
      throw _privateConstructorUsedError; // 客户端是否已经上传了3072的大图用于导出
  @JsonKey(name: 'is_large_image_uploaded')
  bool get isLargeImageUploaded =>
      throw _privateConstructorUsedError; // 客户端资源ID
  @JsonKey(name: 'client_resource_id')
  String get clientResourceId => throw _privateConstructorUsedError; // 打样状态
  String get status => throw _privateConstructorUsedError; // 打样效果图列表
  List<AigcSampleEffectModel> get effects =>
      throw _privateConstructorUsedError; // 创建时间
  @JsonKey(name: 'create_at')
  int get createAt => throw _privateConstructorUsedError; // 更新时间
  @JsonKey(name: 'update_at')
  int get updateAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleModelCopyWith<AigcSampleModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleModelCopyWith<$Res> {
  factory $AigcSampleModelCopyWith(
          AigcSampleModel value, $Res Function(AigcSampleModel) then) =
      _$AigcSampleModelCopyWithImpl<$Res, AigcSampleModel>;
  @useResult
  $Res call(
      {String id,
      String name,
      @JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      @JsonKey(name: 'preset_id') String presetId,
      @JsonKey(name: 'preset_name') String presetName,
      @JsonKey(name: 'preset_effect_name') String presetEffectName,
      @JsonKey(name: 'origin_photo_url') String originPhotoUrl,
      @JsonKey(name: 'large_photo_url') String largePhotoUrl,
      @JsonKey(name: 'is_large_image_uploaded') bool isLargeImageUploaded,
      @JsonKey(name: 'client_resource_id') String clientResourceId,
      String status,
      List<AigcSampleEffectModel> effects,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class _$AigcSampleModelCopyWithImpl<$Res, $Val extends AigcSampleModel>
    implements $AigcSampleModelCopyWith<$Res> {
  _$AigcSampleModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? projectId = null,
    Object? projectName = null,
    Object? presetId = null,
    Object? presetName = null,
    Object? presetEffectName = null,
    Object? originPhotoUrl = null,
    Object? largePhotoUrl = null,
    Object? isLargeImageUploaded = null,
    Object? clientResourceId = null,
    Object? status = null,
    Object? effects = null,
    Object? createAt = null,
    Object? updateAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      presetId: null == presetId
          ? _value.presetId
          : presetId // ignore: cast_nullable_to_non_nullable
              as String,
      presetName: null == presetName
          ? _value.presetName
          : presetName // ignore: cast_nullable_to_non_nullable
              as String,
      presetEffectName: null == presetEffectName
          ? _value.presetEffectName
          : presetEffectName // ignore: cast_nullable_to_non_nullable
              as String,
      originPhotoUrl: null == originPhotoUrl
          ? _value.originPhotoUrl
          : originPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      largePhotoUrl: null == largePhotoUrl
          ? _value.largePhotoUrl
          : largePhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isLargeImageUploaded: null == isLargeImageUploaded
          ? _value.isLargeImageUploaded
          : isLargeImageUploaded // ignore: cast_nullable_to_non_nullable
              as bool,
      clientResourceId: null == clientResourceId
          ? _value.clientResourceId
          : clientResourceId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      effects: null == effects
          ? _value.effects
          : effects // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleEffectModel>,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleModelImplCopyWith<$Res>
    implements $AigcSampleModelCopyWith<$Res> {
  factory _$$AigcSampleModelImplCopyWith(_$AigcSampleModelImpl value,
          $Res Function(_$AigcSampleModelImpl) then) =
      __$$AigcSampleModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      @JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      @JsonKey(name: 'preset_id') String presetId,
      @JsonKey(name: 'preset_name') String presetName,
      @JsonKey(name: 'preset_effect_name') String presetEffectName,
      @JsonKey(name: 'origin_photo_url') String originPhotoUrl,
      @JsonKey(name: 'large_photo_url') String largePhotoUrl,
      @JsonKey(name: 'is_large_image_uploaded') bool isLargeImageUploaded,
      @JsonKey(name: 'client_resource_id') String clientResourceId,
      String status,
      List<AigcSampleEffectModel> effects,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class __$$AigcSampleModelImplCopyWithImpl<$Res>
    extends _$AigcSampleModelCopyWithImpl<$Res, _$AigcSampleModelImpl>
    implements _$$AigcSampleModelImplCopyWith<$Res> {
  __$$AigcSampleModelImplCopyWithImpl(
      _$AigcSampleModelImpl _value, $Res Function(_$AigcSampleModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? projectId = null,
    Object? projectName = null,
    Object? presetId = null,
    Object? presetName = null,
    Object? presetEffectName = null,
    Object? originPhotoUrl = null,
    Object? largePhotoUrl = null,
    Object? isLargeImageUploaded = null,
    Object? clientResourceId = null,
    Object? status = null,
    Object? effects = null,
    Object? createAt = null,
    Object? updateAt = null,
  }) {
    return _then(_$AigcSampleModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      presetId: null == presetId
          ? _value.presetId
          : presetId // ignore: cast_nullable_to_non_nullable
              as String,
      presetName: null == presetName
          ? _value.presetName
          : presetName // ignore: cast_nullable_to_non_nullable
              as String,
      presetEffectName: null == presetEffectName
          ? _value.presetEffectName
          : presetEffectName // ignore: cast_nullable_to_non_nullable
              as String,
      originPhotoUrl: null == originPhotoUrl
          ? _value.originPhotoUrl
          : originPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      largePhotoUrl: null == largePhotoUrl
          ? _value.largePhotoUrl
          : largePhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isLargeImageUploaded: null == isLargeImageUploaded
          ? _value.isLargeImageUploaded
          : isLargeImageUploaded // ignore: cast_nullable_to_non_nullable
              as bool,
      clientResourceId: null == clientResourceId
          ? _value.clientResourceId
          : clientResourceId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      effects: null == effects
          ? _value._effects
          : effects // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleEffectModel>,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleModelImpl implements _AigcSampleModel {
  const _$AigcSampleModelImpl(
      {this.id = '',
      this.name = '',
      @JsonKey(name: 'project_id') this.projectId = '',
      @JsonKey(name: 'project_name') this.projectName = '',
      @JsonKey(name: 'preset_id') this.presetId = '',
      @JsonKey(name: 'preset_name') this.presetName = '',
      @JsonKey(name: 'preset_effect_name') this.presetEffectName = '',
      @JsonKey(name: 'origin_photo_url') this.originPhotoUrl = '',
      @JsonKey(name: 'large_photo_url') this.largePhotoUrl = '',
      @JsonKey(name: 'is_large_image_uploaded')
      this.isLargeImageUploaded = false,
      @JsonKey(name: 'client_resource_id') this.clientResourceId = '',
      this.status = '',
      final List<AigcSampleEffectModel> effects = const [],
      @JsonKey(name: 'create_at') this.createAt = 0,
      @JsonKey(name: 'update_at') this.updateAt = 0})
      : _effects = effects;

  factory _$AigcSampleModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleModelImplFromJson(json);

// 打样项目ID
  @override
  @JsonKey()
  final String id;
// 打样项目名称
  @override
  @JsonKey()
  final String name;
// 项目Id
  @override
  @JsonKey(name: 'project_id')
  final String projectId;
// 项目名称
  @override
  @JsonKey(name: 'project_name')
  final String projectName;
// 预设id
  @override
  @JsonKey(name: 'preset_id')
  final String presetId;
// 预设名称
  @override
  @JsonKey(name: 'preset_name')
  final String presetName;
// 预设效果图名称
  @override
  @JsonKey(name: 'preset_effect_name')
  final String presetEffectName;
// 打样原图URL
  @override
  @JsonKey(name: 'origin_photo_url')
  final String originPhotoUrl;
// 大图URL（用于再次打样上报）
  @override
  @JsonKey(name: 'large_photo_url')
  final String largePhotoUrl;
// 客户端是否已经上传了3072的大图用于导出
  @override
  @JsonKey(name: 'is_large_image_uploaded')
  final bool isLargeImageUploaded;
// 客户端资源ID
  @override
  @JsonKey(name: 'client_resource_id')
  final String clientResourceId;
// 打样状态
  @override
  @JsonKey()
  final String status;
// 打样效果图列表
  final List<AigcSampleEffectModel> _effects;
// 打样效果图列表
  @override
  @JsonKey()
  List<AigcSampleEffectModel> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

// 创建时间
  @override
  @JsonKey(name: 'create_at')
  final int createAt;
// 更新时间
  @override
  @JsonKey(name: 'update_at')
  final int updateAt;

  @override
  String toString() {
    return 'AigcSampleModel(id: $id, name: $name, projectId: $projectId, projectName: $projectName, presetId: $presetId, presetName: $presetName, presetEffectName: $presetEffectName, originPhotoUrl: $originPhotoUrl, largePhotoUrl: $largePhotoUrl, isLargeImageUploaded: $isLargeImageUploaded, clientResourceId: $clientResourceId, status: $status, effects: $effects, createAt: $createAt, updateAt: $updateAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.projectName, projectName) ||
                other.projectName == projectName) &&
            (identical(other.presetId, presetId) ||
                other.presetId == presetId) &&
            (identical(other.presetName, presetName) ||
                other.presetName == presetName) &&
            (identical(other.presetEffectName, presetEffectName) ||
                other.presetEffectName == presetEffectName) &&
            (identical(other.originPhotoUrl, originPhotoUrl) ||
                other.originPhotoUrl == originPhotoUrl) &&
            (identical(other.largePhotoUrl, largePhotoUrl) ||
                other.largePhotoUrl == largePhotoUrl) &&
            (identical(other.isLargeImageUploaded, isLargeImageUploaded) ||
                other.isLargeImageUploaded == isLargeImageUploaded) &&
            (identical(other.clientResourceId, clientResourceId) ||
                other.clientResourceId == clientResourceId) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.updateAt, updateAt) ||
                other.updateAt == updateAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      projectId,
      projectName,
      presetId,
      presetName,
      presetEffectName,
      originPhotoUrl,
      largePhotoUrl,
      isLargeImageUploaded,
      clientResourceId,
      status,
      const DeepCollectionEquality().hash(_effects),
      createAt,
      updateAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleModelImplCopyWith<_$AigcSampleModelImpl> get copyWith =>
      __$$AigcSampleModelImplCopyWithImpl<_$AigcSampleModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleModelImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleModel implements AigcSampleModel {
  const factory _AigcSampleModel(
      {final String id,
      final String name,
      @JsonKey(name: 'project_id') final String projectId,
      @JsonKey(name: 'project_name') final String projectName,
      @JsonKey(name: 'preset_id') final String presetId,
      @JsonKey(name: 'preset_name') final String presetName,
      @JsonKey(name: 'preset_effect_name') final String presetEffectName,
      @JsonKey(name: 'origin_photo_url') final String originPhotoUrl,
      @JsonKey(name: 'large_photo_url') final String largePhotoUrl,
      @JsonKey(name: 'is_large_image_uploaded') final bool isLargeImageUploaded,
      @JsonKey(name: 'client_resource_id') final String clientResourceId,
      final String status,
      final List<AigcSampleEffectModel> effects,
      @JsonKey(name: 'create_at') final int createAt,
      @JsonKey(name: 'update_at') final int updateAt}) = _$AigcSampleModelImpl;

  factory _AigcSampleModel.fromJson(Map<String, dynamic> json) =
      _$AigcSampleModelImpl.fromJson;

  @override // 打样项目ID
  String get id;
  @override // 打样项目名称
  String get name;
  @override // 项目Id
  @JsonKey(name: 'project_id')
  String get projectId;
  @override // 项目名称
  @JsonKey(name: 'project_name')
  String get projectName;
  @override // 预设id
  @JsonKey(name: 'preset_id')
  String get presetId;
  @override // 预设名称
  @JsonKey(name: 'preset_name')
  String get presetName;
  @override // 预设效果图名称
  @JsonKey(name: 'preset_effect_name')
  String get presetEffectName;
  @override // 打样原图URL
  @JsonKey(name: 'origin_photo_url')
  String get originPhotoUrl;
  @override // 大图URL（用于再次打样上报）
  @JsonKey(name: 'large_photo_url')
  String get largePhotoUrl;
  @override // 客户端是否已经上传了3072的大图用于导出
  @JsonKey(name: 'is_large_image_uploaded')
  bool get isLargeImageUploaded;
  @override // 客户端资源ID
  @JsonKey(name: 'client_resource_id')
  String get clientResourceId;
  @override // 打样状态
  String get status;
  @override // 打样效果图列表
  List<AigcSampleEffectModel> get effects;
  @override // 创建时间
  @JsonKey(name: 'create_at')
  int get createAt;
  @override // 更新时间
  @JsonKey(name: 'update_at')
  int get updateAt;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleModelImplCopyWith<_$AigcSampleModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
