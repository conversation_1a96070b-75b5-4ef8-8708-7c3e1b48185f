// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_list_export_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleListExportRequest _$AigcSampleListExportRequestFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleListExportRequest.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleListExportRequest {
  @JsonKey(name: 'id')
  String get proofingId => throw _privateConstructorUsedError; // 打样项目ID
  @JsonKey(name: 'effect_code')
  String get effectCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleListExportRequestCopyWith<AigcSampleListExportRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleListExportRequestCopyWith<$Res> {
  factory $AigcSampleListExportRequestCopyWith(
          AigcSampleListExportRequest value,
          $Res Function(AigcSampleListExportRequest) then) =
      _$AigcSampleListExportRequestCopyWithImpl<$Res,
          AigcSampleListExportRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String proofingId,
      @JsonKey(name: 'effect_code') String effectCode});
}

/// @nodoc
class _$AigcSampleListExportRequestCopyWithImpl<$Res,
        $Val extends AigcSampleListExportRequest>
    implements $AigcSampleListExportRequestCopyWith<$Res> {
  _$AigcSampleListExportRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proofingId = null,
    Object? effectCode = null,
  }) {
    return _then(_value.copyWith(
      proofingId: null == proofingId
          ? _value.proofingId
          : proofingId // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleListExportRequestImplCopyWith<$Res>
    implements $AigcSampleListExportRequestCopyWith<$Res> {
  factory _$$AigcSampleListExportRequestImplCopyWith(
          _$AigcSampleListExportRequestImpl value,
          $Res Function(_$AigcSampleListExportRequestImpl) then) =
      __$$AigcSampleListExportRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String proofingId,
      @JsonKey(name: 'effect_code') String effectCode});
}

/// @nodoc
class __$$AigcSampleListExportRequestImplCopyWithImpl<$Res>
    extends _$AigcSampleListExportRequestCopyWithImpl<$Res,
        _$AigcSampleListExportRequestImpl>
    implements _$$AigcSampleListExportRequestImplCopyWith<$Res> {
  __$$AigcSampleListExportRequestImplCopyWithImpl(
      _$AigcSampleListExportRequestImpl _value,
      $Res Function(_$AigcSampleListExportRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proofingId = null,
    Object? effectCode = null,
  }) {
    return _then(_$AigcSampleListExportRequestImpl(
      proofingId: null == proofingId
          ? _value.proofingId
          : proofingId // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleListExportRequestImpl
    implements _AigcSampleListExportRequest {
  const _$AigcSampleListExportRequestImpl(
      {@JsonKey(name: 'id') required this.proofingId,
      @JsonKey(name: 'effect_code') required this.effectCode});

  factory _$AigcSampleListExportRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AigcSampleListExportRequestImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String proofingId;
// 打样项目ID
  @override
  @JsonKey(name: 'effect_code')
  final String effectCode;

  @override
  String toString() {
    return 'AigcSampleListExportRequest(proofingId: $proofingId, effectCode: $effectCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleListExportRequestImpl &&
            (identical(other.proofingId, proofingId) ||
                other.proofingId == proofingId) &&
            (identical(other.effectCode, effectCode) ||
                other.effectCode == effectCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, proofingId, effectCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleListExportRequestImplCopyWith<_$AigcSampleListExportRequestImpl>
      get copyWith => __$$AigcSampleListExportRequestImplCopyWithImpl<
          _$AigcSampleListExportRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleListExportRequestImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleListExportRequest
    implements AigcSampleListExportRequest {
  const factory _AigcSampleListExportRequest(
          {@JsonKey(name: 'id') required final String proofingId,
          @JsonKey(name: 'effect_code') required final String effectCode}) =
      _$AigcSampleListExportRequestImpl;

  factory _AigcSampleListExportRequest.fromJson(Map<String, dynamic> json) =
      _$AigcSampleListExportRequestImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String get proofingId;
  @override // 打样项目ID
  @JsonKey(name: 'effect_code')
  String get effectCode;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleListExportRequestImplCopyWith<_$AigcSampleListExportRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
