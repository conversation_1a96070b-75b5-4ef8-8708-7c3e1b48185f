// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_sample_effect_model.freezed.dart';

part 'aigc_sample_effect_model.g.dart';

@freezed
class AigcSampleEffectModel with _$AigcSampleEffectModel {
  const factory AigcSampleEffectModel({
    // 打样效果图Code
    @JsonKey(name: 'effect_code') required String effectCode,
    // 导出状态
    @Json<PERSON>ey(name: 'export_status') required String exportStatus,
    // 缩略图URL
    @JsonKey(name: 'thumb_url') @Default('') String thumbUrl,
    // 效果图URL
    @JsonKey(name: 'photo_url') @Default('') String photoUrl,
    // 导出效果图URL
    @JsonKey(name: 'export_photo_url') @Default('') String exportPhotoUrl,
    // 错误信息
    @JsonKey(name: 'error_msg') String? errorMsg, // 错误信息
  }) = _AigcSampleEffectModel;

  factory AigcSampleEffectModel.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleEffectModelFromJson(json);
}

extension AigcSampleEffectExtension on AigcSampleEffectModel {
  /// 检测效果图模型是否有状态变更
  /// 检测规则：
  /// 1. exportStatus 变更算变更
  /// 2. thumbUrl 从空字符串变得有值算变更（有值后的字符串变更不算）
  /// 3. photoUrl 从空字符串变得有值算变更（有值后的字符串变更不算）
  bool hasStateChanged(AigcSampleEffectModel? oldModel) {
    if (oldModel == null) {
      return true;
    }

    // 1. 导出状态变更
    if (exportStatus != oldModel.exportStatus) {
      return true;
    }

    // 2. thumbUrl 从空变为有值，不用管，有值后的字符串变更不算，服务端每次会变
    if (oldModel.thumbUrl.isEmpty && thumbUrl.isNotEmpty ||
        oldModel.thumbUrl.isNotEmpty && thumbUrl.isEmpty) {
      return true;
    }

    // 3. photoUrl 从空变为有值，不用管，有值后的字符串变更不算，服务端每次会变
    if (oldModel.photoUrl.isEmpty && photoUrl.isNotEmpty ||
        oldModel.photoUrl.isNotEmpty && photoUrl.isEmpty) {
      return true;
    }

    return false;
  }
}

/*
 {
            "effect_code":"xxx",//打样效果图Code
            "export_status":"running", // 正在导出
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          }
*/
