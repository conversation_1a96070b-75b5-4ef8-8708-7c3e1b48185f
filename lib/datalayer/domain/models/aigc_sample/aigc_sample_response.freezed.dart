// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleResponse _$AigcSampleResponseFromJson(Map<String, dynamic> json) {
  return _AigcSampleResponse.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleResponse {
  List<AigcSampleModel> get proofings => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  @JsonKey(name: 'page_size')
  int get pageSize => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleResponseCopyWith<AigcSampleResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleResponseCopyWith<$Res> {
  factory $AigcSampleResponseCopyWith(
          AigcSampleResponse value, $Res Function(AigcSampleResponse) then) =
      _$AigcSampleResponseCopyWithImpl<$Res, AigcSampleResponse>;
  @useResult
  $Res call(
      {List<AigcSampleModel> proofings,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class _$AigcSampleResponseCopyWithImpl<$Res, $Val extends AigcSampleResponse>
    implements $AigcSampleResponseCopyWith<$Res> {
  _$AigcSampleResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proofings = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      proofings: null == proofings
          ? _value.proofings
          : proofings // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleResponseImplCopyWith<$Res>
    implements $AigcSampleResponseCopyWith<$Res> {
  factory _$$AigcSampleResponseImplCopyWith(_$AigcSampleResponseImpl value,
          $Res Function(_$AigcSampleResponseImpl) then) =
      __$$AigcSampleResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AigcSampleModel> proofings,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class __$$AigcSampleResponseImplCopyWithImpl<$Res>
    extends _$AigcSampleResponseCopyWithImpl<$Res, _$AigcSampleResponseImpl>
    implements _$$AigcSampleResponseImplCopyWith<$Res> {
  __$$AigcSampleResponseImplCopyWithImpl(_$AigcSampleResponseImpl _value,
      $Res Function(_$AigcSampleResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proofings = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_$AigcSampleResponseImpl(
      proofings: null == proofings
          ? _value._proofings
          : proofings // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleResponseImpl implements _AigcSampleResponse {
  const _$AigcSampleResponseImpl(
      {required final List<AigcSampleModel> proofings,
      required this.total,
      required this.page,
      @JsonKey(name: 'page_size') required this.pageSize})
      : _proofings = proofings;

  factory _$AigcSampleResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleResponseImplFromJson(json);

  final List<AigcSampleModel> _proofings;
  @override
  List<AigcSampleModel> get proofings {
    if (_proofings is EqualUnmodifiableListView) return _proofings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_proofings);
  }

  @override
  final int total;
  @override
  final int page;
  @override
  @JsonKey(name: 'page_size')
  final int pageSize;

  @override
  String toString() {
    return 'AigcSampleResponse(proofings: $proofings, total: $total, page: $page, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._proofings, _proofings) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_proofings), total, page, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleResponseImplCopyWith<_$AigcSampleResponseImpl> get copyWith =>
      __$$AigcSampleResponseImplCopyWithImpl<_$AigcSampleResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleResponseImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleResponse implements AigcSampleResponse {
  const factory _AigcSampleResponse(
          {required final List<AigcSampleModel> proofings,
          required final int total,
          required final int page,
          @JsonKey(name: 'page_size') required final int pageSize}) =
      _$AigcSampleResponseImpl;

  factory _AigcSampleResponse.fromJson(Map<String, dynamic> json) =
      _$AigcSampleResponseImpl.fromJson;

  @override
  List<AigcSampleModel> get proofings;
  @override
  int get total;
  @override
  int get page;
  @override
  @JsonKey(name: 'page_size')
  int get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleResponseImplCopyWith<_$AigcSampleResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
