// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_sample_list_export_request.freezed.dart';
part 'aigc_sample_list_export_request.g.dart';

@freezed
class AigcSampleListExportRequest with _$AigcSampleListExportRequest {
  const factory AigcSampleListExportRequest({
    @JsonKey(name: 'id') required String proofingId, // 打样项目ID
    @JsonKey(name: 'effect_code') required String effectCode, // 打样效果图ID
  }) = _AigcSampleListExportRequest;

  factory AigcSampleListExportRequest.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleListExportRequestFromJson(json);
}
