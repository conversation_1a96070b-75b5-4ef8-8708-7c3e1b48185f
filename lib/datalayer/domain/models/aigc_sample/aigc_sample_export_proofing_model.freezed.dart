// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_export_proofing_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleExportProofingModel _$AigcSampleExportProofingModelFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleExportProofingModel.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleExportProofingModel {
// 打样项目id
  String get id => throw _privateConstructorUsedError; // 打样本地(原图)名称
  @JsonKey(name: 'origin_photo_name')
  String get originPhotoName => throw _privateConstructorUsedError; // 本地预设id
  @JsonKey(name: 'preset_id')
  String get presetId => throw _privateConstructorUsedError; // 本地预设名称
  @JsonKey(name: 'preset_name')
  String get presetName => throw _privateConstructorUsedError; // 本地资源id
  @JsonKey(name: 'client_resource_id')
  String get clientResourceId => throw _privateConstructorUsedError; // 导出效果列表
  List<AigcSampleExportEffectModel> get exports =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleExportProofingModelCopyWith<AigcSampleExportProofingModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleExportProofingModelCopyWith<$Res> {
  factory $AigcSampleExportProofingModelCopyWith(
          AigcSampleExportProofingModel value,
          $Res Function(AigcSampleExportProofingModel) then) =
      _$AigcSampleExportProofingModelCopyWithImpl<$Res,
          AigcSampleExportProofingModel>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'origin_photo_name') String originPhotoName,
      @JsonKey(name: 'preset_id') String presetId,
      @JsonKey(name: 'preset_name') String presetName,
      @JsonKey(name: 'client_resource_id') String clientResourceId,
      List<AigcSampleExportEffectModel> exports});
}

/// @nodoc
class _$AigcSampleExportProofingModelCopyWithImpl<$Res,
        $Val extends AigcSampleExportProofingModel>
    implements $AigcSampleExportProofingModelCopyWith<$Res> {
  _$AigcSampleExportProofingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? originPhotoName = null,
    Object? presetId = null,
    Object? presetName = null,
    Object? clientResourceId = null,
    Object? exports = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      originPhotoName: null == originPhotoName
          ? _value.originPhotoName
          : originPhotoName // ignore: cast_nullable_to_non_nullable
              as String,
      presetId: null == presetId
          ? _value.presetId
          : presetId // ignore: cast_nullable_to_non_nullable
              as String,
      presetName: null == presetName
          ? _value.presetName
          : presetName // ignore: cast_nullable_to_non_nullable
              as String,
      clientResourceId: null == clientResourceId
          ? _value.clientResourceId
          : clientResourceId // ignore: cast_nullable_to_non_nullable
              as String,
      exports: null == exports
          ? _value.exports
          : exports // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleExportEffectModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleExportProofingModelImplCopyWith<$Res>
    implements $AigcSampleExportProofingModelCopyWith<$Res> {
  factory _$$AigcSampleExportProofingModelImplCopyWith(
          _$AigcSampleExportProofingModelImpl value,
          $Res Function(_$AigcSampleExportProofingModelImpl) then) =
      __$$AigcSampleExportProofingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'origin_photo_name') String originPhotoName,
      @JsonKey(name: 'preset_id') String presetId,
      @JsonKey(name: 'preset_name') String presetName,
      @JsonKey(name: 'client_resource_id') String clientResourceId,
      List<AigcSampleExportEffectModel> exports});
}

/// @nodoc
class __$$AigcSampleExportProofingModelImplCopyWithImpl<$Res>
    extends _$AigcSampleExportProofingModelCopyWithImpl<$Res,
        _$AigcSampleExportProofingModelImpl>
    implements _$$AigcSampleExportProofingModelImplCopyWith<$Res> {
  __$$AigcSampleExportProofingModelImplCopyWithImpl(
      _$AigcSampleExportProofingModelImpl _value,
      $Res Function(_$AigcSampleExportProofingModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? originPhotoName = null,
    Object? presetId = null,
    Object? presetName = null,
    Object? clientResourceId = null,
    Object? exports = null,
  }) {
    return _then(_$AigcSampleExportProofingModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      originPhotoName: null == originPhotoName
          ? _value.originPhotoName
          : originPhotoName // ignore: cast_nullable_to_non_nullable
              as String,
      presetId: null == presetId
          ? _value.presetId
          : presetId // ignore: cast_nullable_to_non_nullable
              as String,
      presetName: null == presetName
          ? _value.presetName
          : presetName // ignore: cast_nullable_to_non_nullable
              as String,
      clientResourceId: null == clientResourceId
          ? _value.clientResourceId
          : clientResourceId // ignore: cast_nullable_to_non_nullable
              as String,
      exports: null == exports
          ? _value._exports
          : exports // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleExportEffectModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleExportProofingModelImpl
    implements _AigcSampleExportProofingModel {
  const _$AigcSampleExportProofingModelImpl(
      {required this.id,
      @JsonKey(name: 'origin_photo_name') required this.originPhotoName,
      @JsonKey(name: 'preset_id') this.presetId = '',
      @JsonKey(name: 'preset_name') this.presetName = '',
      @JsonKey(name: 'client_resource_id') required this.clientResourceId,
      final List<AigcSampleExportEffectModel> exports = const []})
      : _exports = exports;

  factory _$AigcSampleExportProofingModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AigcSampleExportProofingModelImplFromJson(json);

// 打样项目id
  @override
  final String id;
// 打样本地(原图)名称
  @override
  @JsonKey(name: 'origin_photo_name')
  final String originPhotoName;
// 本地预设id
  @override
  @JsonKey(name: 'preset_id')
  final String presetId;
// 本地预设名称
  @override
  @JsonKey(name: 'preset_name')
  final String presetName;
// 本地资源id
  @override
  @JsonKey(name: 'client_resource_id')
  final String clientResourceId;
// 导出效果列表
  final List<AigcSampleExportEffectModel> _exports;
// 导出效果列表
  @override
  @JsonKey()
  List<AigcSampleExportEffectModel> get exports {
    if (_exports is EqualUnmodifiableListView) return _exports;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exports);
  }

  @override
  String toString() {
    return 'AigcSampleExportProofingModel(id: $id, originPhotoName: $originPhotoName, presetId: $presetId, presetName: $presetName, clientResourceId: $clientResourceId, exports: $exports)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleExportProofingModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.originPhotoName, originPhotoName) ||
                other.originPhotoName == originPhotoName) &&
            (identical(other.presetId, presetId) ||
                other.presetId == presetId) &&
            (identical(other.presetName, presetName) ||
                other.presetName == presetName) &&
            (identical(other.clientResourceId, clientResourceId) ||
                other.clientResourceId == clientResourceId) &&
            const DeepCollectionEquality().equals(other._exports, _exports));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      originPhotoName,
      presetId,
      presetName,
      clientResourceId,
      const DeepCollectionEquality().hash(_exports));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleExportProofingModelImplCopyWith<
          _$AigcSampleExportProofingModelImpl>
      get copyWith => __$$AigcSampleExportProofingModelImplCopyWithImpl<
          _$AigcSampleExportProofingModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleExportProofingModelImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleExportProofingModel
    implements AigcSampleExportProofingModel {
  const factory _AigcSampleExportProofingModel(
      {required final String id,
      @JsonKey(name: 'origin_photo_name') required final String originPhotoName,
      @JsonKey(name: 'preset_id') final String presetId,
      @JsonKey(name: 'preset_name') final String presetName,
      @JsonKey(name: 'client_resource_id')
      required final String clientResourceId,
      final List<AigcSampleExportEffectModel>
          exports}) = _$AigcSampleExportProofingModelImpl;

  factory _AigcSampleExportProofingModel.fromJson(Map<String, dynamic> json) =
      _$AigcSampleExportProofingModelImpl.fromJson;

  @override // 打样项目id
  String get id;
  @override // 打样本地(原图)名称
  @JsonKey(name: 'origin_photo_name')
  String get originPhotoName;
  @override // 本地预设id
  @JsonKey(name: 'preset_id')
  String get presetId;
  @override // 本地预设名称
  @JsonKey(name: 'preset_name')
  String get presetName;
  @override // 本地资源id
  @JsonKey(name: 'client_resource_id')
  String get clientResourceId;
  @override // 导出效果列表
  List<AigcSampleExportEffectModel> get exports;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleExportProofingModelImplCopyWith<
          _$AigcSampleExportProofingModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
