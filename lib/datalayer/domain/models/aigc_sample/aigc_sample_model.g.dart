// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_sample_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcSampleModelImpl _$$AigcSampleModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcSampleModelImpl(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      projectId: json['project_id'] as String? ?? '',
      projectName: json['project_name'] as String? ?? '',
      presetId: json['preset_id'] as String? ?? '',
      presetName: json['preset_name'] as String? ?? '',
      presetEffectName: json['preset_effect_name'] as String? ?? '',
      originPhotoUrl: json['origin_photo_url'] as String? ?? '',
      largePhotoUrl: json['large_photo_url'] as String? ?? '',
      isLargeImageUploaded: json['is_large_image_uploaded'] as bool? ?? false,
      clientResourceId: json['client_resource_id'] as String? ?? '',
      status: json['status'] as String? ?? '',
      effects: (json['effects'] as List<dynamic>?)
              ?.map((e) =>
                  AigcSampleEffectModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createAt: (json['create_at'] as num?)?.toInt() ?? 0,
      updateAt: (json['update_at'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$AigcSampleModelImplToJson(
        _$AigcSampleModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'project_id': instance.projectId,
      'project_name': instance.projectName,
      'preset_id': instance.presetId,
      'preset_name': instance.presetName,
      'preset_effect_name': instance.presetEffectName,
      'origin_photo_url': instance.originPhotoUrl,
      'large_photo_url': instance.largePhotoUrl,
      'is_large_image_uploaded': instance.isLargeImageUploaded,
      'client_resource_id': instance.clientResourceId,
      'status': instance.status,
      'effects': instance.effects,
      'create_at': instance.createAt,
      'update_at': instance.updateAt,
    };
