// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_sample_export_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcSampleExportResponseImpl _$$AigcSampleExportResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcSampleExportResponseImpl(
      projects: (json['projects'] as List<dynamic>)
          .map((e) =>
              AigcSampleExportProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      pageSize: (json['page_size'] as num).toInt(),
    );

Map<String, dynamic> _$$AigcSampleExportResponseImplToJson(
        _$AigcSampleExportResponseImpl instance) =>
    <String, dynamic>{
      'projects': instance.projects,
      'total': instance.total,
      'page': instance.page,
      'page_size': instance.pageSize,
    };
