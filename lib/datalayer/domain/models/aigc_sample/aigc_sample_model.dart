// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_effect_model.dart';

part 'aigc_sample_model.freezed.dart';
part 'aigc_sample_model.g.dart';

@freezed
class AigcSampleModel with _$AigcSampleModel {
  const factory AigcSampleModel({
    // 打样项目ID
    @Default('') String id,
    // 打样项目名称
    @Default('') String name,
    // 项目Id
    @J<PERSON><PERSON>ey(name: 'project_id') @Default('') String projectId,
    // 项目名称
    @JsonKey(name: 'project_name') @Default('') String projectName,
    // 预设id
    @JsonKey(name: 'preset_id') @Default('') String presetId,
    // 预设名称
    @JsonKey(name: 'preset_name') @Default('') String presetName,
    // 预设效果图名称
    @<PERSON><PERSON><PERSON><PERSON>(name: 'preset_effect_name') @Default('') String presetEffectName,
    // 打样原图URL
    @JsonKey(name: 'origin_photo_url') @Default('') String originPhotoUrl,
    // 大图URL（用于再次打样上报）
    @JsonKey(name: 'large_photo_url') @Default('') String largePhotoUrl,
    // 客户端是否已经上传了3072的大图用于导出
    @JsonKey(name: 'is_large_image_uploaded')
    @Default(false)
    bool isLargeImageUploaded,
    // 客户端资源ID
    @JsonKey(name: 'client_resource_id') @Default('') String clientResourceId,
    // 打样状态
    @Default('') String status,
    // 打样效果图列表
    @Default([]) List<AigcSampleEffectModel> effects,
    // 创建时间
    @JsonKey(name: 'create_at') @Default(0) int createAt,
    // 更新时间
    @JsonKey(name: 'update_at') @Default(0) int updateAt,
  }) = _AigcSampleModel;

  factory AigcSampleModel.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleModelFromJson(json);
}

extension AigcSampleExtension on AigcSampleModel {
  String? findUseableThumbnailUrl() {
    if (effects.isEmpty) {
      return null;
    }
    for (var effect in effects) {
      if (effect.thumbUrl.isNotEmpty) {
        return effect.thumbUrl;
      }
    }
    return null;
  }

  /// 检测打样模型是否有状态变更
  /// 检测规则：
  /// 1. status 变更算变更
  /// 2. effects 列表中任何一个 AigcSampleEffectModel 状态变更算变更
  bool hasStateChanged(AigcSampleModel? oldModel) {
    if (oldModel == null) {
      return true;
    }

    // 1. 打样状态变更
    if (status != oldModel.status) {
      return true;
    }

    // 2. 检查 effects 列表变更
    if (effects.length != oldModel.effects.length) {
      return true;
    }

    // 3. 检查每个 effect 的状态变更
    // 构建旧模型的 effect 映射表，提高查找效率
    final oldEffectsMap = <String, AigcSampleEffectModel>{
      for (var effect in oldModel.effects) effect.effectCode: effect
    };

    for (final currentEffect in effects) {
      final oldEffect = oldEffectsMap[currentEffect.effectCode];

      // 如果找不到对应的旧 effect，说明是新增的，算作变更
      if (oldEffect == null) {
        return true;
      }

      // 检查当前 effect 是否有状态变更
      if (currentEffect.hasStateChanged(oldEffect)) {
        return true;
      }
    }

    return false;
  }
}
/*
{
        "id": "67f88a1e0579e2cec8c15afa",//打样项目ID
        "name":"",// 打样项目名称
        "project_id":"",// 项目Id
        "preset_name":"",//预设名称
        "preset_effect_name":"",//预设效果图名称
        "status":"running",
        "effects":[//该打样项目下的效果图列表
          {
            "effect_code":"xxx",//打样效果图Code
            "export_status":"running", // 正在导出
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "export_status":"unexported", // 未导出
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "export_status":"completed", // 已导出
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "thumb_url": null, //缩略图url
            "photo_url":null//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "thumb_url": null, //缩略图url
            "photo_url":null//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "thumb_url": null, //缩略图url
            "photo_url":null//效果图URL
          }
        ],
        "create_at": 1749192432, // 创建时间
        "update_at":1749192432 // 更新时间
      }
*/
