// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_proofing_model.dart';

part 'aigc_sample_export_project_model.freezed.dart';
part 'aigc_sample_export_project_model.g.dart';

@freezed
class AigcSampleExportProjectModel with _$AigcSampleExportProjectModel {
  const factory AigcSampleExportProjectModel({
    // 本地项目id
    @JsonKey(name: 'project_id') required String projectId,
    // 本地项目名称
    @<PERSON><PERSON><PERSON><PERSON>(name: 'project_name') required String projectName,
    // 打样列表
    @Default([]) List<AigcSampleExportProofingModel> proofings,
  }) = _AigcSampleExportProjectModel;

  factory AigcSampleExportProjectModel.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleExportProjectModelFromJson(json);
}
