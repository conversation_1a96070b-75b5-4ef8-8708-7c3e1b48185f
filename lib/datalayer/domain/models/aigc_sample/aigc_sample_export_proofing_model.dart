// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_effect_model.dart';

part 'aigc_sample_export_proofing_model.freezed.dart';
part 'aigc_sample_export_proofing_model.g.dart';

@freezed
class AigcSampleExportProofingModel with _$AigcSampleExportProofingModel {
  const factory AigcSampleExportProofingModel({
    // 打样项目id
    required String id,
    // 打样本地(原图)名称
    @Json<PERSON>ey(name: 'origin_photo_name') required String originPhotoName,
    // 本地预设id
    @Json<PERSON>ey(name: 'preset_id') @Default('') String presetId,
    // 本地预设名称
    @Json<PERSON>ey(name: 'preset_name') @Default('') String presetName,
    // 本地资源id
    @J<PERSON><PERSON>ey(name: 'client_resource_id') required String clientResourceId,
    // 导出效果列表
    @Default([]) List<AigcSampleExportEffectModel> exports,
  }) = _AigcSampleExportProofingModel;

  factory AigcSampleExportProofingModel.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleExportProofingModelFromJson(json);
}
