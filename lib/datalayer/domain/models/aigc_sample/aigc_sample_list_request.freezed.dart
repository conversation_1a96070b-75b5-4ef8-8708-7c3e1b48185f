// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_list_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleListRequest _$AigcSampleListRequestFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleListRequest.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleListRequest {
  @JsonKey(name: 'project_id')
  String get projectId => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  @JsonKey(name: 'page_size')
  int get pageSize => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleListRequestCopyWith<AigcSampleListRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleListRequestCopyWith<$Res> {
  factory $AigcSampleListRequestCopyWith(AigcSampleListRequest value,
          $Res Function(AigcSampleListRequest) then) =
      _$AigcSampleListRequestCopyWithImpl<$Res, AigcSampleListRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class _$AigcSampleListRequestCopyWithImpl<$Res,
        $Val extends AigcSampleListRequest>
    implements $AigcSampleListRequestCopyWith<$Res> {
  _$AigcSampleListRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleListRequestImplCopyWith<$Res>
    implements $AigcSampleListRequestCopyWith<$Res> {
  factory _$$AigcSampleListRequestImplCopyWith(
          _$AigcSampleListRequestImpl value,
          $Res Function(_$AigcSampleListRequestImpl) then) =
      __$$AigcSampleListRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class __$$AigcSampleListRequestImplCopyWithImpl<$Res>
    extends _$AigcSampleListRequestCopyWithImpl<$Res,
        _$AigcSampleListRequestImpl>
    implements _$$AigcSampleListRequestImplCopyWith<$Res> {
  __$$AigcSampleListRequestImplCopyWithImpl(_$AigcSampleListRequestImpl _value,
      $Res Function(_$AigcSampleListRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_$AigcSampleListRequestImpl(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleListRequestImpl implements _AigcSampleListRequest {
  const _$AigcSampleListRequestImpl(
      {@JsonKey(name: 'project_id') required this.projectId,
      required this.page,
      @JsonKey(name: 'page_size') required this.pageSize});

  factory _$AigcSampleListRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleListRequestImplFromJson(json);

  @override
  @JsonKey(name: 'project_id')
  final String projectId;
  @override
  final int page;
  @override
  @JsonKey(name: 'page_size')
  final int pageSize;

  @override
  String toString() {
    return 'AigcSampleListRequest(projectId: $projectId, page: $page, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleListRequestImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, projectId, page, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleListRequestImplCopyWith<_$AigcSampleListRequestImpl>
      get copyWith => __$$AigcSampleListRequestImplCopyWithImpl<
          _$AigcSampleListRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleListRequestImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleListRequest implements AigcSampleListRequest {
  const factory _AigcSampleListRequest(
          {@JsonKey(name: 'project_id') required final String projectId,
          required final int page,
          @JsonKey(name: 'page_size') required final int pageSize}) =
      _$AigcSampleListRequestImpl;

  factory _AigcSampleListRequest.fromJson(Map<String, dynamic> json) =
      _$AigcSampleListRequestImpl.fromJson;

  @override
  @JsonKey(name: 'project_id')
  String get projectId;
  @override
  int get page;
  @override
  @JsonKey(name: 'page_size')
  int get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleListRequestImplCopyWith<_$AigcSampleListRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
