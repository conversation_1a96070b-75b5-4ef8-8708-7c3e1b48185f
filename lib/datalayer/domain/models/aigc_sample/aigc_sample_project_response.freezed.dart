// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_project_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleProjectResponse _$AigcSampleProjectResponseFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleProjectResponse.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleProjectResponse {
  List<AigcSampleProjectModel> get projects =>
      throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  @JsonKey(name: 'page_size')
  int get pageSize => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleProjectResponseCopyWith<AigcSampleProjectResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleProjectResponseCopyWith<$Res> {
  factory $AigcSampleProjectResponseCopyWith(AigcSampleProjectResponse value,
          $Res Function(AigcSampleProjectResponse) then) =
      _$AigcSampleProjectResponseCopyWithImpl<$Res, AigcSampleProjectResponse>;
  @useResult
  $Res call(
      {List<AigcSampleProjectModel> projects,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class _$AigcSampleProjectResponseCopyWithImpl<$Res,
        $Val extends AigcSampleProjectResponse>
    implements $AigcSampleProjectResponseCopyWith<$Res> {
  _$AigcSampleProjectResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projects = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      projects: null == projects
          ? _value.projects
          : projects // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleProjectModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleProjectResponseImplCopyWith<$Res>
    implements $AigcSampleProjectResponseCopyWith<$Res> {
  factory _$$AigcSampleProjectResponseImplCopyWith(
          _$AigcSampleProjectResponseImpl value,
          $Res Function(_$AigcSampleProjectResponseImpl) then) =
      __$$AigcSampleProjectResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AigcSampleProjectModel> projects,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class __$$AigcSampleProjectResponseImplCopyWithImpl<$Res>
    extends _$AigcSampleProjectResponseCopyWithImpl<$Res,
        _$AigcSampleProjectResponseImpl>
    implements _$$AigcSampleProjectResponseImplCopyWith<$Res> {
  __$$AigcSampleProjectResponseImplCopyWithImpl(
      _$AigcSampleProjectResponseImpl _value,
      $Res Function(_$AigcSampleProjectResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projects = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_$AigcSampleProjectResponseImpl(
      projects: null == projects
          ? _value._projects
          : projects // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleProjectModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleProjectResponseImpl implements _AigcSampleProjectResponse {
  const _$AigcSampleProjectResponseImpl(
      {required final List<AigcSampleProjectModel> projects,
      required this.total,
      required this.page,
      @JsonKey(name: 'page_size') required this.pageSize})
      : _projects = projects;

  factory _$AigcSampleProjectResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleProjectResponseImplFromJson(json);

  final List<AigcSampleProjectModel> _projects;
  @override
  List<AigcSampleProjectModel> get projects {
    if (_projects is EqualUnmodifiableListView) return _projects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_projects);
  }

  @override
  final int total;
  @override
  final int page;
  @override
  @JsonKey(name: 'page_size')
  final int pageSize;

  @override
  String toString() {
    return 'AigcSampleProjectResponse(projects: $projects, total: $total, page: $page, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleProjectResponseImpl &&
            const DeepCollectionEquality().equals(other._projects, _projects) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_projects), total, page, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleProjectResponseImplCopyWith<_$AigcSampleProjectResponseImpl>
      get copyWith => __$$AigcSampleProjectResponseImplCopyWithImpl<
          _$AigcSampleProjectResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleProjectResponseImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleProjectResponse implements AigcSampleProjectResponse {
  const factory _AigcSampleProjectResponse(
          {required final List<AigcSampleProjectModel> projects,
          required final int total,
          required final int page,
          @JsonKey(name: 'page_size') required final int pageSize}) =
      _$AigcSampleProjectResponseImpl;

  factory _AigcSampleProjectResponse.fromJson(Map<String, dynamic> json) =
      _$AigcSampleProjectResponseImpl.fromJson;

  @override
  List<AigcSampleProjectModel> get projects;
  @override
  int get total;
  @override
  int get page;
  @override
  @JsonKey(name: 'page_size')
  int get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleProjectResponseImplCopyWith<_$AigcSampleProjectResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
