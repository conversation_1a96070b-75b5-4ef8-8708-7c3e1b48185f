// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleRequest _$AigcSampleRequestFromJson(Map<String, dynamic> json) {
  return _AigcSampleRequest.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleRequest {
// 客户端项目
  @JsonKey(name: 'client_project')
  ClientProject get clientProject => throw _privateConstructorUsedError; // 预设
  Preset get preset => throw _privateConstructorUsedError; // 原图
  @JsonKey(name: 'origin_photo')
  OriginPhoto get originPhoto => throw _privateConstructorUsedError; // 蒙版
  @JsonKey(name: 'mask_image_url')
  String get maskImageUrl => throw _privateConstructorUsedError; // 客户端资源ID
  @JsonKey(name: 'client_resource_id')
  String get clientResourceId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleRequestCopyWith<AigcSampleRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleRequestCopyWith<$Res> {
  factory $AigcSampleRequestCopyWith(
          AigcSampleRequest value, $Res Function(AigcSampleRequest) then) =
      _$AigcSampleRequestCopyWithImpl<$Res, AigcSampleRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'client_project') ClientProject clientProject,
      Preset preset,
      @JsonKey(name: 'origin_photo') OriginPhoto originPhoto,
      @JsonKey(name: 'mask_image_url') String maskImageUrl,
      @JsonKey(name: 'client_resource_id') String clientResourceId});

  $ClientProjectCopyWith<$Res> get clientProject;
  $PresetCopyWith<$Res> get preset;
  $OriginPhotoCopyWith<$Res> get originPhoto;
}

/// @nodoc
class _$AigcSampleRequestCopyWithImpl<$Res, $Val extends AigcSampleRequest>
    implements $AigcSampleRequestCopyWith<$Res> {
  _$AigcSampleRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientProject = null,
    Object? preset = null,
    Object? originPhoto = null,
    Object? maskImageUrl = null,
    Object? clientResourceId = null,
  }) {
    return _then(_value.copyWith(
      clientProject: null == clientProject
          ? _value.clientProject
          : clientProject // ignore: cast_nullable_to_non_nullable
              as ClientProject,
      preset: null == preset
          ? _value.preset
          : preset // ignore: cast_nullable_to_non_nullable
              as Preset,
      originPhoto: null == originPhoto
          ? _value.originPhoto
          : originPhoto // ignore: cast_nullable_to_non_nullable
              as OriginPhoto,
      maskImageUrl: null == maskImageUrl
          ? _value.maskImageUrl
          : maskImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      clientResourceId: null == clientResourceId
          ? _value.clientResourceId
          : clientResourceId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClientProjectCopyWith<$Res> get clientProject {
    return $ClientProjectCopyWith<$Res>(_value.clientProject, (value) {
      return _then(_value.copyWith(clientProject: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PresetCopyWith<$Res> get preset {
    return $PresetCopyWith<$Res>(_value.preset, (value) {
      return _then(_value.copyWith(preset: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $OriginPhotoCopyWith<$Res> get originPhoto {
    return $OriginPhotoCopyWith<$Res>(_value.originPhoto, (value) {
      return _then(_value.copyWith(originPhoto: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AigcSampleRequestImplCopyWith<$Res>
    implements $AigcSampleRequestCopyWith<$Res> {
  factory _$$AigcSampleRequestImplCopyWith(_$AigcSampleRequestImpl value,
          $Res Function(_$AigcSampleRequestImpl) then) =
      __$$AigcSampleRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'client_project') ClientProject clientProject,
      Preset preset,
      @JsonKey(name: 'origin_photo') OriginPhoto originPhoto,
      @JsonKey(name: 'mask_image_url') String maskImageUrl,
      @JsonKey(name: 'client_resource_id') String clientResourceId});

  @override
  $ClientProjectCopyWith<$Res> get clientProject;
  @override
  $PresetCopyWith<$Res> get preset;
  @override
  $OriginPhotoCopyWith<$Res> get originPhoto;
}

/// @nodoc
class __$$AigcSampleRequestImplCopyWithImpl<$Res>
    extends _$AigcSampleRequestCopyWithImpl<$Res, _$AigcSampleRequestImpl>
    implements _$$AigcSampleRequestImplCopyWith<$Res> {
  __$$AigcSampleRequestImplCopyWithImpl(_$AigcSampleRequestImpl _value,
      $Res Function(_$AigcSampleRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientProject = null,
    Object? preset = null,
    Object? originPhoto = null,
    Object? maskImageUrl = null,
    Object? clientResourceId = null,
  }) {
    return _then(_$AigcSampleRequestImpl(
      clientProject: null == clientProject
          ? _value.clientProject
          : clientProject // ignore: cast_nullable_to_non_nullable
              as ClientProject,
      preset: null == preset
          ? _value.preset
          : preset // ignore: cast_nullable_to_non_nullable
              as Preset,
      originPhoto: null == originPhoto
          ? _value.originPhoto
          : originPhoto // ignore: cast_nullable_to_non_nullable
              as OriginPhoto,
      maskImageUrl: null == maskImageUrl
          ? _value.maskImageUrl
          : maskImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      clientResourceId: null == clientResourceId
          ? _value.clientResourceId
          : clientResourceId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleRequestImpl implements _AigcSampleRequest {
  const _$AigcSampleRequestImpl(
      {@JsonKey(name: 'client_project') required this.clientProject,
      required this.preset,
      @JsonKey(name: 'origin_photo') required this.originPhoto,
      @JsonKey(name: 'mask_image_url') required this.maskImageUrl,
      @JsonKey(name: 'client_resource_id') required this.clientResourceId});

  factory _$AigcSampleRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleRequestImplFromJson(json);

// 客户端项目
  @override
  @JsonKey(name: 'client_project')
  final ClientProject clientProject;
// 预设
  @override
  final Preset preset;
// 原图
  @override
  @JsonKey(name: 'origin_photo')
  final OriginPhoto originPhoto;
// 蒙版
  @override
  @JsonKey(name: 'mask_image_url')
  final String maskImageUrl;
// 客户端资源ID
  @override
  @JsonKey(name: 'client_resource_id')
  final String clientResourceId;

  @override
  String toString() {
    return 'AigcSampleRequest(clientProject: $clientProject, preset: $preset, originPhoto: $originPhoto, maskImageUrl: $maskImageUrl, clientResourceId: $clientResourceId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleRequestImpl &&
            (identical(other.clientProject, clientProject) ||
                other.clientProject == clientProject) &&
            (identical(other.preset, preset) || other.preset == preset) &&
            (identical(other.originPhoto, originPhoto) ||
                other.originPhoto == originPhoto) &&
            (identical(other.maskImageUrl, maskImageUrl) ||
                other.maskImageUrl == maskImageUrl) &&
            (identical(other.clientResourceId, clientResourceId) ||
                other.clientResourceId == clientResourceId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, clientProject, preset,
      originPhoto, maskImageUrl, clientResourceId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleRequestImplCopyWith<_$AigcSampleRequestImpl> get copyWith =>
      __$$AigcSampleRequestImplCopyWithImpl<_$AigcSampleRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleRequestImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleRequest implements AigcSampleRequest {
  const factory _AigcSampleRequest(
      {@JsonKey(name: 'client_project')
      required final ClientProject clientProject,
      required final Preset preset,
      @JsonKey(name: 'origin_photo') required final OriginPhoto originPhoto,
      @JsonKey(name: 'mask_image_url') required final String maskImageUrl,
      @JsonKey(name: 'client_resource_id')
      required final String clientResourceId}) = _$AigcSampleRequestImpl;

  factory _AigcSampleRequest.fromJson(Map<String, dynamic> json) =
      _$AigcSampleRequestImpl.fromJson;

  @override // 客户端项目
  @JsonKey(name: 'client_project')
  ClientProject get clientProject;
  @override // 预设
  Preset get preset;
  @override // 原图
  @JsonKey(name: 'origin_photo')
  OriginPhoto get originPhoto;
  @override // 蒙版
  @JsonKey(name: 'mask_image_url')
  String get maskImageUrl;
  @override // 客户端资源ID
  @JsonKey(name: 'client_resource_id')
  String get clientResourceId;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleRequestImplCopyWith<_$AigcSampleRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ClientProject _$ClientProjectFromJson(Map<String, dynamic> json) {
  return _ClientProject.fromJson(json);
}

/// @nodoc
mixin _$ClientProject {
  @JsonKey(name: 'project_id')
  String get projectId => throw _privateConstructorUsedError;
  @JsonKey(name: 'project_name')
  String get projectName => throw _privateConstructorUsedError;
  @JsonKey(name: 'update_at')
  int get updateAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClientProjectCopyWith<ClientProject> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClientProjectCopyWith<$Res> {
  factory $ClientProjectCopyWith(
          ClientProject value, $Res Function(ClientProject) then) =
      _$ClientProjectCopyWithImpl<$Res, ClientProject>;
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class _$ClientProjectCopyWithImpl<$Res, $Val extends ClientProject>
    implements $ClientProjectCopyWith<$Res> {
  _$ClientProjectCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? projectName = null,
    Object? updateAt = null,
  }) {
    return _then(_value.copyWith(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ClientProjectImplCopyWith<$Res>
    implements $ClientProjectCopyWith<$Res> {
  factory _$$ClientProjectImplCopyWith(
          _$ClientProjectImpl value, $Res Function(_$ClientProjectImpl) then) =
      __$$ClientProjectImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class __$$ClientProjectImplCopyWithImpl<$Res>
    extends _$ClientProjectCopyWithImpl<$Res, _$ClientProjectImpl>
    implements _$$ClientProjectImplCopyWith<$Res> {
  __$$ClientProjectImplCopyWithImpl(
      _$ClientProjectImpl _value, $Res Function(_$ClientProjectImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? projectName = null,
    Object? updateAt = null,
  }) {
    return _then(_$ClientProjectImpl(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ClientProjectImpl implements _ClientProject {
  const _$ClientProjectImpl(
      {@JsonKey(name: 'project_id') required this.projectId,
      @JsonKey(name: 'project_name') required this.projectName,
      @JsonKey(name: 'update_at') required this.updateAt});

  factory _$ClientProjectImpl.fromJson(Map<String, dynamic> json) =>
      _$$ClientProjectImplFromJson(json);

  @override
  @JsonKey(name: 'project_id')
  final String projectId;
  @override
  @JsonKey(name: 'project_name')
  final String projectName;
  @override
  @JsonKey(name: 'update_at')
  final int updateAt;

  @override
  String toString() {
    return 'ClientProject(projectId: $projectId, projectName: $projectName, updateAt: $updateAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ClientProjectImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.projectName, projectName) ||
                other.projectName == projectName) &&
            (identical(other.updateAt, updateAt) ||
                other.updateAt == updateAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, projectId, projectName, updateAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ClientProjectImplCopyWith<_$ClientProjectImpl> get copyWith =>
      __$$ClientProjectImplCopyWithImpl<_$ClientProjectImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ClientProjectImplToJson(
      this,
    );
  }
}

abstract class _ClientProject implements ClientProject {
  const factory _ClientProject(
          {@JsonKey(name: 'project_id') required final String projectId,
          @JsonKey(name: 'project_name') required final String projectName,
          @JsonKey(name: 'update_at') required final int updateAt}) =
      _$ClientProjectImpl;

  factory _ClientProject.fromJson(Map<String, dynamic> json) =
      _$ClientProjectImpl.fromJson;

  @override
  @JsonKey(name: 'project_id')
  String get projectId;
  @override
  @JsonKey(name: 'project_name')
  String get projectName;
  @override
  @JsonKey(name: 'update_at')
  int get updateAt;
  @override
  @JsonKey(ignore: true)
  _$$ClientProjectImplCopyWith<_$ClientProjectImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Preset _$PresetFromJson(Map<String, dynamic> json) {
  return _Preset.fromJson(json);
}

/// @nodoc
mixin _$Preset {
  @JsonKey(name: 'preset_id')
  String get presetId => throw _privateConstructorUsedError;
  @JsonKey(name: 'effect_code')
  String get effectCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresetCopyWith<Preset> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresetCopyWith<$Res> {
  factory $PresetCopyWith(Preset value, $Res Function(Preset) then) =
      _$PresetCopyWithImpl<$Res, Preset>;
  @useResult
  $Res call(
      {@JsonKey(name: 'preset_id') String presetId,
      @JsonKey(name: 'effect_code') String effectCode});
}

/// @nodoc
class _$PresetCopyWithImpl<$Res, $Val extends Preset>
    implements $PresetCopyWith<$Res> {
  _$PresetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presetId = null,
    Object? effectCode = null,
  }) {
    return _then(_value.copyWith(
      presetId: null == presetId
          ? _value.presetId
          : presetId // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresetImplCopyWith<$Res> implements $PresetCopyWith<$Res> {
  factory _$$PresetImplCopyWith(
          _$PresetImpl value, $Res Function(_$PresetImpl) then) =
      __$$PresetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'preset_id') String presetId,
      @JsonKey(name: 'effect_code') String effectCode});
}

/// @nodoc
class __$$PresetImplCopyWithImpl<$Res>
    extends _$PresetCopyWithImpl<$Res, _$PresetImpl>
    implements _$$PresetImplCopyWith<$Res> {
  __$$PresetImplCopyWithImpl(
      _$PresetImpl _value, $Res Function(_$PresetImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presetId = null,
    Object? effectCode = null,
  }) {
    return _then(_$PresetImpl(
      presetId: null == presetId
          ? _value.presetId
          : presetId // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresetImpl implements _Preset {
  const _$PresetImpl(
      {@JsonKey(name: 'preset_id') required this.presetId,
      @JsonKey(name: 'effect_code') required this.effectCode});

  factory _$PresetImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresetImplFromJson(json);

  @override
  @JsonKey(name: 'preset_id')
  final String presetId;
  @override
  @JsonKey(name: 'effect_code')
  final String effectCode;

  @override
  String toString() {
    return 'Preset(presetId: $presetId, effectCode: $effectCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresetImpl &&
            (identical(other.presetId, presetId) ||
                other.presetId == presetId) &&
            (identical(other.effectCode, effectCode) ||
                other.effectCode == effectCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, presetId, effectCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresetImplCopyWith<_$PresetImpl> get copyWith =>
      __$$PresetImplCopyWithImpl<_$PresetImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresetImplToJson(
      this,
    );
  }
}

abstract class _Preset implements Preset {
  const factory _Preset(
          {@JsonKey(name: 'preset_id') required final String presetId,
          @JsonKey(name: 'effect_code') required final String effectCode}) =
      _$PresetImpl;

  factory _Preset.fromJson(Map<String, dynamic> json) = _$PresetImpl.fromJson;

  @override
  @JsonKey(name: 'preset_id')
  String get presetId;
  @override
  @JsonKey(name: 'effect_code')
  String get effectCode;
  @override
  @JsonKey(ignore: true)
  _$$PresetImplCopyWith<_$PresetImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OriginPhoto _$OriginPhotoFromJson(Map<String, dynamic> json) {
  return _OriginPhoto.fromJson(json);
}

/// @nodoc
mixin _$OriginPhoto {
  @JsonKey(name: 'file_name')
  String get fileName => throw _privateConstructorUsedError;
  @JsonKey(name: 'file_url')
  String get fileUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OriginPhotoCopyWith<OriginPhoto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OriginPhotoCopyWith<$Res> {
  factory $OriginPhotoCopyWith(
          OriginPhoto value, $Res Function(OriginPhoto) then) =
      _$OriginPhotoCopyWithImpl<$Res, OriginPhoto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'file_name') String fileName,
      @JsonKey(name: 'file_url') String fileUrl});
}

/// @nodoc
class _$OriginPhotoCopyWithImpl<$Res, $Val extends OriginPhoto>
    implements $OriginPhotoCopyWith<$Res> {
  _$OriginPhotoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileName = null,
    Object? fileUrl = null,
  }) {
    return _then(_value.copyWith(
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: null == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OriginPhotoImplCopyWith<$Res>
    implements $OriginPhotoCopyWith<$Res> {
  factory _$$OriginPhotoImplCopyWith(
          _$OriginPhotoImpl value, $Res Function(_$OriginPhotoImpl) then) =
      __$$OriginPhotoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'file_name') String fileName,
      @JsonKey(name: 'file_url') String fileUrl});
}

/// @nodoc
class __$$OriginPhotoImplCopyWithImpl<$Res>
    extends _$OriginPhotoCopyWithImpl<$Res, _$OriginPhotoImpl>
    implements _$$OriginPhotoImplCopyWith<$Res> {
  __$$OriginPhotoImplCopyWithImpl(
      _$OriginPhotoImpl _value, $Res Function(_$OriginPhotoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileName = null,
    Object? fileUrl = null,
  }) {
    return _then(_$OriginPhotoImpl(
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: null == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OriginPhotoImpl implements _OriginPhoto {
  const _$OriginPhotoImpl(
      {@JsonKey(name: 'file_name') required this.fileName,
      @JsonKey(name: 'file_url') required this.fileUrl});

  factory _$OriginPhotoImpl.fromJson(Map<String, dynamic> json) =>
      _$$OriginPhotoImplFromJson(json);

  @override
  @JsonKey(name: 'file_name')
  final String fileName;
  @override
  @JsonKey(name: 'file_url')
  final String fileUrl;

  @override
  String toString() {
    return 'OriginPhoto(fileName: $fileName, fileUrl: $fileUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OriginPhotoImpl &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.fileUrl, fileUrl) || other.fileUrl == fileUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fileName, fileUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OriginPhotoImplCopyWith<_$OriginPhotoImpl> get copyWith =>
      __$$OriginPhotoImplCopyWithImpl<_$OriginPhotoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OriginPhotoImplToJson(
      this,
    );
  }
}

abstract class _OriginPhoto implements OriginPhoto {
  const factory _OriginPhoto(
          {@JsonKey(name: 'file_name') required final String fileName,
          @JsonKey(name: 'file_url') required final String fileUrl}) =
      _$OriginPhotoImpl;

  factory _OriginPhoto.fromJson(Map<String, dynamic> json) =
      _$OriginPhotoImpl.fromJson;

  @override
  @JsonKey(name: 'file_name')
  String get fileName;
  @override
  @JsonKey(name: 'file_url')
  String get fileUrl;
  @override
  @JsonKey(ignore: true)
  _$$OriginPhotoImplCopyWith<_$OriginPhotoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
