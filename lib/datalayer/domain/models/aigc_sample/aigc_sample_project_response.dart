// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_project_model.dart';

part 'aigc_sample_project_response.freezed.dart';
part 'aigc_sample_project_response.g.dart';

@freezed
class AigcSampleProjectResponse with _$AigcSampleProjectResponse {
  const factory AigcSampleProjectResponse({
    required List<AigcSampleProjectModel> projects,
    required int total,
    required int page,
    @JsonKey(name: 'page_size') required int pageSize,
  }) = _AigcSampleProjectResponse;

  factory AigcSampleProjectResponse.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleProjectResponseFromJson(json);
}

/*
{
  "projects": [
    {
      "project_id": "67f88a1e0579e2cec8c15afa",
      "project_name": "test",
      "create_at": 1749192432,
      "update_at": 1749192432
    }
  ]
}
*/
