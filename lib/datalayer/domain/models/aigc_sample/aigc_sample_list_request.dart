// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_sample_list_request.freezed.dart';
part 'aigc_sample_list_request.g.dart';

@freezed
class AigcSampleListRequest with _$AigcSampleListRequest {
  const factory AigcSampleListRequest({
    @JsonKey(name: 'project_id') required String projectId,
    required int page,
    @JsonKey(name: 'page_size') required int pageSize,
  }) = _AigcSampleListRequest;

  factory AigcSampleListRequest.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleListRequestFromJson(json);
}
