// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_sample_export_proofing_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcSampleExportProofingModelImpl
    _$$AigcSampleExportProofingModelImplFromJson(Map<String, dynamic> json) =>
        _$AigcSampleExportProofingModelImpl(
          id: json['id'] as String,
          originPhotoName: json['origin_photo_name'] as String,
          presetId: json['preset_id'] as String? ?? '',
          presetName: json['preset_name'] as String? ?? '',
          clientResourceId: json['client_resource_id'] as String,
          exports: (json['exports'] as List<dynamic>?)
                  ?.map((e) => AigcSampleExportEffectModel.fromJson(
                      e as Map<String, dynamic>))
                  .toList() ??
              const [],
        );

Map<String, dynamic> _$$AigcSampleExportProofingModelImplToJson(
        _$AigcSampleExportProofingModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'origin_photo_name': instance.originPhotoName,
      'preset_id': instance.presetId,
      'preset_name': instance.presetName,
      'client_resource_id': instance.clientResourceId,
      'exports': instance.exports,
    };
