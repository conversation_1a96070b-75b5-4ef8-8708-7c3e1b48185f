// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_sample_export_effect_model.freezed.dart';

part 'aigc_sample_export_effect_model.g.dart';

@freezed
class AigcSampleExportEffectModel with _$AigcSampleExportEffectModel {
  const factory AigcSampleExportEffectModel({
    // 效果code
    @JsonKey(name: 'effect_code') required String effectCode,
    // 打样状态
    required String status,
    // 是否下载
    @Json<PERSON>ey(name: 'is_download') @Default(false) bool isDownload,
    // 打样导出任务生成的高清大图地址
    @JsonKey(name: 'export_photo_url') String? exportPhotoUrl,
    // 创建时间
    @Json<PERSON>ey(name: 'create_at') required int createAt,
    // 更新时间
    @Json<PERSON>ey(name: 'update_at') required int updateAt,
    // 错误信息
    @<PERSON><PERSON><PERSON><PERSON>(name: 'error_msg') @Default('') String errorMsg,
  }) = _AigcSampleExportEffectModel;

  factory AigcSampleExportEffectModel.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleExportEffectModelFromJson(json);
}
