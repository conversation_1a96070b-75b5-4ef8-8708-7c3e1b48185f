// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_export_effect_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleExportEffectModel _$AigcSampleExportEffectModelFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleExportEffectModel.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleExportEffectModel {
// 效果code
  @JsonKey(name: 'effect_code')
  String get effectCode => throw _privateConstructorUsedError; // 打样状态
  String get status => throw _privateConstructorUsedError; // 是否下载
  @JsonKey(name: 'is_download')
  bool get isDownload => throw _privateConstructorUsedError; // 打样导出任务生成的高清大图地址
  @JsonKey(name: 'export_photo_url')
  String? get exportPhotoUrl => throw _privateConstructorUsedError; // 创建时间
  @JsonKey(name: 'create_at')
  int get createAt => throw _privateConstructorUsedError; // 更新时间
  @JsonKey(name: 'update_at')
  int get updateAt => throw _privateConstructorUsedError; // 错误信息
  @JsonKey(name: 'error_msg')
  String get errorMsg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleExportEffectModelCopyWith<AigcSampleExportEffectModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleExportEffectModelCopyWith<$Res> {
  factory $AigcSampleExportEffectModelCopyWith(
          AigcSampleExportEffectModel value,
          $Res Function(AigcSampleExportEffectModel) then) =
      _$AigcSampleExportEffectModelCopyWithImpl<$Res,
          AigcSampleExportEffectModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'effect_code') String effectCode,
      String status,
      @JsonKey(name: 'is_download') bool isDownload,
      @JsonKey(name: 'export_photo_url') String? exportPhotoUrl,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt,
      @JsonKey(name: 'error_msg') String errorMsg});
}

/// @nodoc
class _$AigcSampleExportEffectModelCopyWithImpl<$Res,
        $Val extends AigcSampleExportEffectModel>
    implements $AigcSampleExportEffectModelCopyWith<$Res> {
  _$AigcSampleExportEffectModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? effectCode = null,
    Object? status = null,
    Object? isDownload = null,
    Object? exportPhotoUrl = freezed,
    Object? createAt = null,
    Object? updateAt = null,
    Object? errorMsg = null,
  }) {
    return _then(_value.copyWith(
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      isDownload: null == isDownload
          ? _value.isDownload
          : isDownload // ignore: cast_nullable_to_non_nullable
              as bool,
      exportPhotoUrl: freezed == exportPhotoUrl
          ? _value.exportPhotoUrl
          : exportPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
      errorMsg: null == errorMsg
          ? _value.errorMsg
          : errorMsg // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleExportEffectModelImplCopyWith<$Res>
    implements $AigcSampleExportEffectModelCopyWith<$Res> {
  factory _$$AigcSampleExportEffectModelImplCopyWith(
          _$AigcSampleExportEffectModelImpl value,
          $Res Function(_$AigcSampleExportEffectModelImpl) then) =
      __$$AigcSampleExportEffectModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'effect_code') String effectCode,
      String status,
      @JsonKey(name: 'is_download') bool isDownload,
      @JsonKey(name: 'export_photo_url') String? exportPhotoUrl,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt,
      @JsonKey(name: 'error_msg') String errorMsg});
}

/// @nodoc
class __$$AigcSampleExportEffectModelImplCopyWithImpl<$Res>
    extends _$AigcSampleExportEffectModelCopyWithImpl<$Res,
        _$AigcSampleExportEffectModelImpl>
    implements _$$AigcSampleExportEffectModelImplCopyWith<$Res> {
  __$$AigcSampleExportEffectModelImplCopyWithImpl(
      _$AigcSampleExportEffectModelImpl _value,
      $Res Function(_$AigcSampleExportEffectModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? effectCode = null,
    Object? status = null,
    Object? isDownload = null,
    Object? exportPhotoUrl = freezed,
    Object? createAt = null,
    Object? updateAt = null,
    Object? errorMsg = null,
  }) {
    return _then(_$AigcSampleExportEffectModelImpl(
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      isDownload: null == isDownload
          ? _value.isDownload
          : isDownload // ignore: cast_nullable_to_non_nullable
              as bool,
      exportPhotoUrl: freezed == exportPhotoUrl
          ? _value.exportPhotoUrl
          : exportPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
      errorMsg: null == errorMsg
          ? _value.errorMsg
          : errorMsg // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleExportEffectModelImpl
    implements _AigcSampleExportEffectModel {
  const _$AigcSampleExportEffectModelImpl(
      {@JsonKey(name: 'effect_code') required this.effectCode,
      required this.status,
      @JsonKey(name: 'is_download') this.isDownload = false,
      @JsonKey(name: 'export_photo_url') this.exportPhotoUrl,
      @JsonKey(name: 'create_at') required this.createAt,
      @JsonKey(name: 'update_at') required this.updateAt,
      @JsonKey(name: 'error_msg') this.errorMsg = ''});

  factory _$AigcSampleExportEffectModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AigcSampleExportEffectModelImplFromJson(json);

// 效果code
  @override
  @JsonKey(name: 'effect_code')
  final String effectCode;
// 打样状态
  @override
  final String status;
// 是否下载
  @override
  @JsonKey(name: 'is_download')
  final bool isDownload;
// 打样导出任务生成的高清大图地址
  @override
  @JsonKey(name: 'export_photo_url')
  final String? exportPhotoUrl;
// 创建时间
  @override
  @JsonKey(name: 'create_at')
  final int createAt;
// 更新时间
  @override
  @JsonKey(name: 'update_at')
  final int updateAt;
// 错误信息
  @override
  @JsonKey(name: 'error_msg')
  final String errorMsg;

  @override
  String toString() {
    return 'AigcSampleExportEffectModel(effectCode: $effectCode, status: $status, isDownload: $isDownload, exportPhotoUrl: $exportPhotoUrl, createAt: $createAt, updateAt: $updateAt, errorMsg: $errorMsg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleExportEffectModelImpl &&
            (identical(other.effectCode, effectCode) ||
                other.effectCode == effectCode) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isDownload, isDownload) ||
                other.isDownload == isDownload) &&
            (identical(other.exportPhotoUrl, exportPhotoUrl) ||
                other.exportPhotoUrl == exportPhotoUrl) &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.updateAt, updateAt) ||
                other.updateAt == updateAt) &&
            (identical(other.errorMsg, errorMsg) ||
                other.errorMsg == errorMsg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, effectCode, status, isDownload,
      exportPhotoUrl, createAt, updateAt, errorMsg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleExportEffectModelImplCopyWith<_$AigcSampleExportEffectModelImpl>
      get copyWith => __$$AigcSampleExportEffectModelImplCopyWithImpl<
          _$AigcSampleExportEffectModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleExportEffectModelImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleExportEffectModel
    implements AigcSampleExportEffectModel {
  const factory _AigcSampleExportEffectModel(
          {@JsonKey(name: 'effect_code') required final String effectCode,
          required final String status,
          @JsonKey(name: 'is_download') final bool isDownload,
          @JsonKey(name: 'export_photo_url') final String? exportPhotoUrl,
          @JsonKey(name: 'create_at') required final int createAt,
          @JsonKey(name: 'update_at') required final int updateAt,
          @JsonKey(name: 'error_msg') final String errorMsg}) =
      _$AigcSampleExportEffectModelImpl;

  factory _AigcSampleExportEffectModel.fromJson(Map<String, dynamic> json) =
      _$AigcSampleExportEffectModelImpl.fromJson;

  @override // 效果code
  @JsonKey(name: 'effect_code')
  String get effectCode;
  @override // 打样状态
  String get status;
  @override // 是否下载
  @JsonKey(name: 'is_download')
  bool get isDownload;
  @override // 打样导出任务生成的高清大图地址
  @JsonKey(name: 'export_photo_url')
  String? get exportPhotoUrl;
  @override // 创建时间
  @JsonKey(name: 'create_at')
  int get createAt;
  @override // 更新时间
  @JsonKey(name: 'update_at')
  int get updateAt;
  @override // 错误信息
  @JsonKey(name: 'error_msg')
  String get errorMsg;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleExportEffectModelImplCopyWith<_$AigcSampleExportEffectModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
