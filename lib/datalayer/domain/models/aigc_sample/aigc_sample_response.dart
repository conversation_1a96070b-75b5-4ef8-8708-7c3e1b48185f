// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';

part 'aigc_sample_response.freezed.dart';
part 'aigc_sample_response.g.dart';

@freezed
class AigcSampleResponse with _$AigcSampleResponse {
  const factory AigcSampleResponse({
    required List<AigcSampleModel> proofings,
    required int total,
    required int page,
    @JsonKey(name: 'page_size') required int pageSize,
  }) = _AigcSampleResponse;

  factory AigcSampleResponse.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleResponseFromJson(json);
}

/*
: {
    "proofings": [
      {
        "id": "67f88a1e0579e2cec8c15afa",//打样项目ID
        "name":"",// 打样项目名称
        "preset_name":"",//预设名称
        "preset_effect_name":"",//预设效果图名称
        "status":"running",
        "effects":[//该打样项目下的效果图列表
          {
            "effect_code":"xxx",//打样效果图Code
            "export_status":"running", // 正在导出
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "export_status":"unexported", // 未导出
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "export_status":"completed", // 已导出
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "thumb_url": null, //缩略图url
            "photo_url":null//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "thumb_url": null, //缩略图url
            "photo_url":null//效果图URL
          },
          {
            "effect_code":"xxx",//打样效果图Code
            "thumb_url": null, //缩略图url
            "photo_url":null//效果图URL
          }
        ],
        "create_at": 1749192432, // 创建时间
        "update_at":1749192432 // 更新时间
      }
    ],
    "total": 5,
    "page": 1,
    "page_size": 10
  }
}
*/
