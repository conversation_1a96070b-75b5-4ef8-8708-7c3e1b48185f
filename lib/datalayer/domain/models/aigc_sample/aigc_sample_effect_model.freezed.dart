// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_effect_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleEffectModel _$AigcSampleEffectModelFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleEffectModel.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleEffectModel {
// 打样效果图Code
  @JsonKey(name: 'effect_code')
  String get effectCode => throw _privateConstructorUsedError; // 导出状态
  @JsonKey(name: 'export_status')
  String get exportStatus => throw _privateConstructorUsedError; // 缩略图URL
  @JsonKey(name: 'thumb_url')
  String get thumbUrl => throw _privateConstructorUsedError; // 效果图URL
  @JsonKey(name: 'photo_url')
  String get photoUrl => throw _privateConstructorUsedError; // 导出效果图URL
  @JsonKey(name: 'export_photo_url')
  String get exportPhotoUrl => throw _privateConstructorUsedError; // 错误信息
  @JsonKey(name: 'error_msg')
  String? get errorMsg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleEffectModelCopyWith<AigcSampleEffectModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleEffectModelCopyWith<$Res> {
  factory $AigcSampleEffectModelCopyWith(AigcSampleEffectModel value,
          $Res Function(AigcSampleEffectModel) then) =
      _$AigcSampleEffectModelCopyWithImpl<$Res, AigcSampleEffectModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'effect_code') String effectCode,
      @JsonKey(name: 'export_status') String exportStatus,
      @JsonKey(name: 'thumb_url') String thumbUrl,
      @JsonKey(name: 'photo_url') String photoUrl,
      @JsonKey(name: 'export_photo_url') String exportPhotoUrl,
      @JsonKey(name: 'error_msg') String? errorMsg});
}

/// @nodoc
class _$AigcSampleEffectModelCopyWithImpl<$Res,
        $Val extends AigcSampleEffectModel>
    implements $AigcSampleEffectModelCopyWith<$Res> {
  _$AigcSampleEffectModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? effectCode = null,
    Object? exportStatus = null,
    Object? thumbUrl = null,
    Object? photoUrl = null,
    Object? exportPhotoUrl = null,
    Object? errorMsg = freezed,
  }) {
    return _then(_value.copyWith(
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      exportStatus: null == exportStatus
          ? _value.exportStatus
          : exportStatus // ignore: cast_nullable_to_non_nullable
              as String,
      thumbUrl: null == thumbUrl
          ? _value.thumbUrl
          : thumbUrl // ignore: cast_nullable_to_non_nullable
              as String,
      photoUrl: null == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      exportPhotoUrl: null == exportPhotoUrl
          ? _value.exportPhotoUrl
          : exportPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      errorMsg: freezed == errorMsg
          ? _value.errorMsg
          : errorMsg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleEffectModelImplCopyWith<$Res>
    implements $AigcSampleEffectModelCopyWith<$Res> {
  factory _$$AigcSampleEffectModelImplCopyWith(
          _$AigcSampleEffectModelImpl value,
          $Res Function(_$AigcSampleEffectModelImpl) then) =
      __$$AigcSampleEffectModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'effect_code') String effectCode,
      @JsonKey(name: 'export_status') String exportStatus,
      @JsonKey(name: 'thumb_url') String thumbUrl,
      @JsonKey(name: 'photo_url') String photoUrl,
      @JsonKey(name: 'export_photo_url') String exportPhotoUrl,
      @JsonKey(name: 'error_msg') String? errorMsg});
}

/// @nodoc
class __$$AigcSampleEffectModelImplCopyWithImpl<$Res>
    extends _$AigcSampleEffectModelCopyWithImpl<$Res,
        _$AigcSampleEffectModelImpl>
    implements _$$AigcSampleEffectModelImplCopyWith<$Res> {
  __$$AigcSampleEffectModelImplCopyWithImpl(_$AigcSampleEffectModelImpl _value,
      $Res Function(_$AigcSampleEffectModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? effectCode = null,
    Object? exportStatus = null,
    Object? thumbUrl = null,
    Object? photoUrl = null,
    Object? exportPhotoUrl = null,
    Object? errorMsg = freezed,
  }) {
    return _then(_$AigcSampleEffectModelImpl(
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      exportStatus: null == exportStatus
          ? _value.exportStatus
          : exportStatus // ignore: cast_nullable_to_non_nullable
              as String,
      thumbUrl: null == thumbUrl
          ? _value.thumbUrl
          : thumbUrl // ignore: cast_nullable_to_non_nullable
              as String,
      photoUrl: null == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      exportPhotoUrl: null == exportPhotoUrl
          ? _value.exportPhotoUrl
          : exportPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      errorMsg: freezed == errorMsg
          ? _value.errorMsg
          : errorMsg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleEffectModelImpl implements _AigcSampleEffectModel {
  const _$AigcSampleEffectModelImpl(
      {@JsonKey(name: 'effect_code') required this.effectCode,
      @JsonKey(name: 'export_status') required this.exportStatus,
      @JsonKey(name: 'thumb_url') this.thumbUrl = '',
      @JsonKey(name: 'photo_url') this.photoUrl = '',
      @JsonKey(name: 'export_photo_url') this.exportPhotoUrl = '',
      @JsonKey(name: 'error_msg') this.errorMsg});

  factory _$AigcSampleEffectModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleEffectModelImplFromJson(json);

// 打样效果图Code
  @override
  @JsonKey(name: 'effect_code')
  final String effectCode;
// 导出状态
  @override
  @JsonKey(name: 'export_status')
  final String exportStatus;
// 缩略图URL
  @override
  @JsonKey(name: 'thumb_url')
  final String thumbUrl;
// 效果图URL
  @override
  @JsonKey(name: 'photo_url')
  final String photoUrl;
// 导出效果图URL
  @override
  @JsonKey(name: 'export_photo_url')
  final String exportPhotoUrl;
// 错误信息
  @override
  @JsonKey(name: 'error_msg')
  final String? errorMsg;

  @override
  String toString() {
    return 'AigcSampleEffectModel(effectCode: $effectCode, exportStatus: $exportStatus, thumbUrl: $thumbUrl, photoUrl: $photoUrl, exportPhotoUrl: $exportPhotoUrl, errorMsg: $errorMsg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleEffectModelImpl &&
            (identical(other.effectCode, effectCode) ||
                other.effectCode == effectCode) &&
            (identical(other.exportStatus, exportStatus) ||
                other.exportStatus == exportStatus) &&
            (identical(other.thumbUrl, thumbUrl) ||
                other.thumbUrl == thumbUrl) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.exportPhotoUrl, exportPhotoUrl) ||
                other.exportPhotoUrl == exportPhotoUrl) &&
            (identical(other.errorMsg, errorMsg) ||
                other.errorMsg == errorMsg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, effectCode, exportStatus,
      thumbUrl, photoUrl, exportPhotoUrl, errorMsg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleEffectModelImplCopyWith<_$AigcSampleEffectModelImpl>
      get copyWith => __$$AigcSampleEffectModelImplCopyWithImpl<
          _$AigcSampleEffectModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleEffectModelImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleEffectModel implements AigcSampleEffectModel {
  const factory _AigcSampleEffectModel(
          {@JsonKey(name: 'effect_code') required final String effectCode,
          @JsonKey(name: 'export_status') required final String exportStatus,
          @JsonKey(name: 'thumb_url') final String thumbUrl,
          @JsonKey(name: 'photo_url') final String photoUrl,
          @JsonKey(name: 'export_photo_url') final String exportPhotoUrl,
          @JsonKey(name: 'error_msg') final String? errorMsg}) =
      _$AigcSampleEffectModelImpl;

  factory _AigcSampleEffectModel.fromJson(Map<String, dynamic> json) =
      _$AigcSampleEffectModelImpl.fromJson;

  @override // 打样效果图Code
  @JsonKey(name: 'effect_code')
  String get effectCode;
  @override // 导出状态
  @JsonKey(name: 'export_status')
  String get exportStatus;
  @override // 缩略图URL
  @JsonKey(name: 'thumb_url')
  String get thumbUrl;
  @override // 效果图URL
  @JsonKey(name: 'photo_url')
  String get photoUrl;
  @override // 导出效果图URL
  @JsonKey(name: 'export_photo_url')
  String get exportPhotoUrl;
  @override // 错误信息
  @JsonKey(name: 'error_msg')
  String? get errorMsg;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleEffectModelImplCopyWith<_$AigcSampleEffectModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
