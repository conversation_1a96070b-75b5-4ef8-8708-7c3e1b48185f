// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_export_status_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleExportStatusModel _$AigcSampleExportStatusModelFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleExportStatusModel.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleExportStatusModel {
// 打样项目ID
  String get id => throw _privateConstructorUsedError; // 打样效果图ID
  @JsonKey(name: 'effect_code')
  String get effectCode =>
      throw _privateConstructorUsedError; // 导出状态：running、unexported、completed
  String get status => throw _privateConstructorUsedError; // 导出高清大图地址
  @JsonKey(name: 'export_photo_url')
  String get exportPhotoUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleExportStatusModelCopyWith<AigcSampleExportStatusModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleExportStatusModelCopyWith<$Res> {
  factory $AigcSampleExportStatusModelCopyWith(
          AigcSampleExportStatusModel value,
          $Res Function(AigcSampleExportStatusModel) then) =
      _$AigcSampleExportStatusModelCopyWithImpl<$Res,
          AigcSampleExportStatusModel>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'effect_code') String effectCode,
      String status,
      @JsonKey(name: 'export_photo_url') String exportPhotoUrl});
}

/// @nodoc
class _$AigcSampleExportStatusModelCopyWithImpl<$Res,
        $Val extends AigcSampleExportStatusModel>
    implements $AigcSampleExportStatusModelCopyWith<$Res> {
  _$AigcSampleExportStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? effectCode = null,
    Object? status = null,
    Object? exportPhotoUrl = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      exportPhotoUrl: null == exportPhotoUrl
          ? _value.exportPhotoUrl
          : exportPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleExportStatusModelImplCopyWith<$Res>
    implements $AigcSampleExportStatusModelCopyWith<$Res> {
  factory _$$AigcSampleExportStatusModelImplCopyWith(
          _$AigcSampleExportStatusModelImpl value,
          $Res Function(_$AigcSampleExportStatusModelImpl) then) =
      __$$AigcSampleExportStatusModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'effect_code') String effectCode,
      String status,
      @JsonKey(name: 'export_photo_url') String exportPhotoUrl});
}

/// @nodoc
class __$$AigcSampleExportStatusModelImplCopyWithImpl<$Res>
    extends _$AigcSampleExportStatusModelCopyWithImpl<$Res,
        _$AigcSampleExportStatusModelImpl>
    implements _$$AigcSampleExportStatusModelImplCopyWith<$Res> {
  __$$AigcSampleExportStatusModelImplCopyWithImpl(
      _$AigcSampleExportStatusModelImpl _value,
      $Res Function(_$AigcSampleExportStatusModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? effectCode = null,
    Object? status = null,
    Object? exportPhotoUrl = null,
  }) {
    return _then(_$AigcSampleExportStatusModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      exportPhotoUrl: null == exportPhotoUrl
          ? _value.exportPhotoUrl
          : exportPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleExportStatusModelImpl
    implements _AigcSampleExportStatusModel {
  const _$AigcSampleExportStatusModelImpl(
      {required this.id,
      @JsonKey(name: 'effect_code') required this.effectCode,
      required this.status,
      @JsonKey(name: 'export_photo_url') this.exportPhotoUrl = ""});

  factory _$AigcSampleExportStatusModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AigcSampleExportStatusModelImplFromJson(json);

// 打样项目ID
  @override
  final String id;
// 打样效果图ID
  @override
  @JsonKey(name: 'effect_code')
  final String effectCode;
// 导出状态：running、unexported、completed
  @override
  final String status;
// 导出高清大图地址
  @override
  @JsonKey(name: 'export_photo_url')
  final String exportPhotoUrl;

  @override
  String toString() {
    return 'AigcSampleExportStatusModel(id: $id, effectCode: $effectCode, status: $status, exportPhotoUrl: $exportPhotoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleExportStatusModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.effectCode, effectCode) ||
                other.effectCode == effectCode) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.exportPhotoUrl, exportPhotoUrl) ||
                other.exportPhotoUrl == exportPhotoUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, effectCode, status, exportPhotoUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleExportStatusModelImplCopyWith<_$AigcSampleExportStatusModelImpl>
      get copyWith => __$$AigcSampleExportStatusModelImplCopyWithImpl<
          _$AigcSampleExportStatusModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleExportStatusModelImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleExportStatusModel
    implements AigcSampleExportStatusModel {
  const factory _AigcSampleExportStatusModel(
          {required final String id,
          @JsonKey(name: 'effect_code') required final String effectCode,
          required final String status,
          @JsonKey(name: 'export_photo_url') final String exportPhotoUrl}) =
      _$AigcSampleExportStatusModelImpl;

  factory _AigcSampleExportStatusModel.fromJson(Map<String, dynamic> json) =
      _$AigcSampleExportStatusModelImpl.fromJson;

  @override // 打样项目ID
  String get id;
  @override // 打样效果图ID
  @JsonKey(name: 'effect_code')
  String get effectCode;
  @override // 导出状态：running、unexported、completed
  String get status;
  @override // 导出高清大图地址
  @JsonKey(name: 'export_photo_url')
  String get exportPhotoUrl;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleExportStatusModelImplCopyWith<_$AigcSampleExportStatusModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
