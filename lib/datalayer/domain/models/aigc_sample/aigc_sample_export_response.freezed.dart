// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_export_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleExportResponse _$AigcSampleExportResponseFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleExportResponse.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleExportResponse {
  List<AigcSampleExportProjectModel> get projects =>
      throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  @JsonKey(name: 'page_size')
  int get pageSize => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleExportResponseCopyWith<AigcSampleExportResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleExportResponseCopyWith<$Res> {
  factory $AigcSampleExportResponseCopyWith(AigcSampleExportResponse value,
          $Res Function(AigcSampleExportResponse) then) =
      _$AigcSampleExportResponseCopyWithImpl<$Res, AigcSampleExportResponse>;
  @useResult
  $Res call(
      {List<AigcSampleExportProjectModel> projects,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class _$AigcSampleExportResponseCopyWithImpl<$Res,
        $Val extends AigcSampleExportResponse>
    implements $AigcSampleExportResponseCopyWith<$Res> {
  _$AigcSampleExportResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projects = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      projects: null == projects
          ? _value.projects
          : projects // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleExportProjectModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleExportResponseImplCopyWith<$Res>
    implements $AigcSampleExportResponseCopyWith<$Res> {
  factory _$$AigcSampleExportResponseImplCopyWith(
          _$AigcSampleExportResponseImpl value,
          $Res Function(_$AigcSampleExportResponseImpl) then) =
      __$$AigcSampleExportResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AigcSampleExportProjectModel> projects,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class __$$AigcSampleExportResponseImplCopyWithImpl<$Res>
    extends _$AigcSampleExportResponseCopyWithImpl<$Res,
        _$AigcSampleExportResponseImpl>
    implements _$$AigcSampleExportResponseImplCopyWith<$Res> {
  __$$AigcSampleExportResponseImplCopyWithImpl(
      _$AigcSampleExportResponseImpl _value,
      $Res Function(_$AigcSampleExportResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projects = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_$AigcSampleExportResponseImpl(
      projects: null == projects
          ? _value._projects
          : projects // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleExportProjectModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleExportResponseImpl implements _AigcSampleExportResponse {
  const _$AigcSampleExportResponseImpl(
      {required final List<AigcSampleExportProjectModel> projects,
      required this.total,
      required this.page,
      @JsonKey(name: 'page_size') required this.pageSize})
      : _projects = projects;

  factory _$AigcSampleExportResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleExportResponseImplFromJson(json);

  final List<AigcSampleExportProjectModel> _projects;
  @override
  List<AigcSampleExportProjectModel> get projects {
    if (_projects is EqualUnmodifiableListView) return _projects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_projects);
  }

  @override
  final int total;
  @override
  final int page;
  @override
  @JsonKey(name: 'page_size')
  final int pageSize;

  @override
  String toString() {
    return 'AigcSampleExportResponse(projects: $projects, total: $total, page: $page, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleExportResponseImpl &&
            const DeepCollectionEquality().equals(other._projects, _projects) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_projects), total, page, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleExportResponseImplCopyWith<_$AigcSampleExportResponseImpl>
      get copyWith => __$$AigcSampleExportResponseImplCopyWithImpl<
          _$AigcSampleExportResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleExportResponseImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleExportResponse implements AigcSampleExportResponse {
  const factory _AigcSampleExportResponse(
          {required final List<AigcSampleExportProjectModel> projects,
          required final int total,
          required final int page,
          @JsonKey(name: 'page_size') required final int pageSize}) =
      _$AigcSampleExportResponseImpl;

  factory _AigcSampleExportResponse.fromJson(Map<String, dynamic> json) =
      _$AigcSampleExportResponseImpl.fromJson;

  @override
  List<AigcSampleExportProjectModel> get projects;
  @override
  int get total;
  @override
  int get page;
  @override
  @JsonKey(name: 'page_size')
  int get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleExportResponseImplCopyWith<_$AigcSampleExportResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
