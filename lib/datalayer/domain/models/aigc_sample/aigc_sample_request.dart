// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_sample_request.freezed.dart';
part 'aigc_sample_request.g.dart';

@freezed
class AigcSampleRequest with _$AigcSampleRequest {
  const factory AigcSampleRequest({
    // 客户端项目
    @Json<PERSON>ey(name: 'client_project') required ClientProject clientProject,
    // 预设
    required Preset preset,
    // 原图
    @J<PERSON><PERSON>ey(name: 'origin_photo') required OriginPhoto originPhoto,
    // 蒙版
    @<PERSON><PERSON>ey(name: 'mask_image_url') required String maskImageUrl,
    // 客户端资源ID
    @JsonKey(name: 'client_resource_id') required String clientResourceId,
  }) = _AigcSampleRequest;

  factory AigcSampleRequest.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => {
        'client_project': clientProject.toJson(),
        'preset': preset.toJson(),
        'origin_photo': originPhoto.toJson(),
        'mask_image_url': maskImageUrl,
      };
}

@freezed
class ClientProject with _$ClientProject {
  const factory ClientProject({
    @JsonKey(name: 'project_id') required String projectId,
    @JsonKey(name: 'project_name') required String projectName,
    @JsonKey(name: 'update_at') required int updateAt,
  }) = _ClientProject;

  factory ClientProject.fromJson(Map<String, dynamic> json) =>
      _$ClientProjectFromJson(json);
}

@freezed
class Preset with _$Preset {
  const factory Preset({
    @JsonKey(name: 'preset_id') required String presetId,
    @JsonKey(name: 'effect_code') required String effectCode,
  }) = _Preset;

  factory Preset.fromJson(Map<String, dynamic> json) => _$PresetFromJson(json);
}

@freezed
class OriginPhoto with _$OriginPhoto {
  const factory OriginPhoto({
    @JsonKey(name: 'file_name') required String fileName,
    @JsonKey(name: 'file_url') required String fileUrl,
  }) = _OriginPhoto;

  factory OriginPhoto.fromJson(Map<String, dynamic> json) =>
      _$OriginPhotoFromJson(json);
}
