// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_sample_project_model.freezed.dart';
part 'aigc_sample_project_model.g.dart';

@freezed
class AigcSampleProjectModel with _$AigcSampleProjectModel {
  const factory AigcSampleProjectModel({
    @Json<PERSON>ey(name: 'project_id') required String projectId,
    @JsonKey(name: 'project_name') required String projectName,
    @<PERSON>son<PERSON>ey(name: 'create_at') required int createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'update_at') required int updatedAt,
  }) = _AigcSampleProjectModel;

  factory AigcSampleProjectModel.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleProjectModelFromJson(json);
}

/*
      {
        "project_id": "67f88a1e0579e2cec8c15afa",//本地项目ID
        "project_name":""//本地项目名称,
        "create_at": 1749192432 // 创建时间
        "update_at":1749192432 // 更新时间
      }
*/
