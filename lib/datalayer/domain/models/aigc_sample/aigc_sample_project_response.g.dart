// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_sample_project_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcSampleProjectResponseImpl _$$AigcSampleProjectResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcSampleProjectResponseImpl(
      projects: (json['projects'] as List<dynamic>)
          .map(
              (e) => AigcSampleProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      pageSize: (json['page_size'] as num).toInt(),
    );

Map<String, dynamic> _$$AigcSampleProjectResponseImplToJson(
        _$AigcSampleProjectResponseImpl instance) =>
    <String, dynamic>{
      'projects': instance.projects,
      'total': instance.total,
      'page': instance.page,
      'page_size': instance.pageSize,
    };
