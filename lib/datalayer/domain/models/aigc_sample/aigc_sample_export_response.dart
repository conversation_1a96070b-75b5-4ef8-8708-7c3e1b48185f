// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_project_model.dart';

part 'aigc_sample_export_response.freezed.dart';
part 'aigc_sample_export_response.g.dart';

@freezed
class AigcSampleExportResponse with _$AigcSampleExportResponse {
  const factory AigcSampleExportResponse({
    required List<AigcSampleExportProjectModel> projects,
    required int total,
    required int page,
    @JsonKey(name: 'page_size') required int pageSize,
  }) = _AigcSampleExportResponse;

  factory AigcSampleExportResponse.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleExportResponseFromJson(json);
}
