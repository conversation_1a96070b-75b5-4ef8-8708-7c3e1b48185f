// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_export_project_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleExportProjectModel _$AigcSampleExportProjectModelFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleExportProjectModel.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleExportProjectModel {
// 本地项目id
  @JsonKey(name: 'project_id')
  String get projectId => throw _privateConstructorUsedError; // 本地项目名称
  @JsonKey(name: 'project_name')
  String get projectName => throw _privateConstructorUsedError; // 打样列表
  List<AigcSampleExportProofingModel> get proofings =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleExportProjectModelCopyWith<AigcSampleExportProjectModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleExportProjectModelCopyWith<$Res> {
  factory $AigcSampleExportProjectModelCopyWith(
          AigcSampleExportProjectModel value,
          $Res Function(AigcSampleExportProjectModel) then) =
      _$AigcSampleExportProjectModelCopyWithImpl<$Res,
          AigcSampleExportProjectModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      List<AigcSampleExportProofingModel> proofings});
}

/// @nodoc
class _$AigcSampleExportProjectModelCopyWithImpl<$Res,
        $Val extends AigcSampleExportProjectModel>
    implements $AigcSampleExportProjectModelCopyWith<$Res> {
  _$AigcSampleExportProjectModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? projectName = null,
    Object? proofings = null,
  }) {
    return _then(_value.copyWith(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      proofings: null == proofings
          ? _value.proofings
          : proofings // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleExportProofingModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleExportProjectModelImplCopyWith<$Res>
    implements $AigcSampleExportProjectModelCopyWith<$Res> {
  factory _$$AigcSampleExportProjectModelImplCopyWith(
          _$AigcSampleExportProjectModelImpl value,
          $Res Function(_$AigcSampleExportProjectModelImpl) then) =
      __$$AigcSampleExportProjectModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      List<AigcSampleExportProofingModel> proofings});
}

/// @nodoc
class __$$AigcSampleExportProjectModelImplCopyWithImpl<$Res>
    extends _$AigcSampleExportProjectModelCopyWithImpl<$Res,
        _$AigcSampleExportProjectModelImpl>
    implements _$$AigcSampleExportProjectModelImplCopyWith<$Res> {
  __$$AigcSampleExportProjectModelImplCopyWithImpl(
      _$AigcSampleExportProjectModelImpl _value,
      $Res Function(_$AigcSampleExportProjectModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? projectName = null,
    Object? proofings = null,
  }) {
    return _then(_$AigcSampleExportProjectModelImpl(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      proofings: null == proofings
          ? _value._proofings
          : proofings // ignore: cast_nullable_to_non_nullable
              as List<AigcSampleExportProofingModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleExportProjectModelImpl
    implements _AigcSampleExportProjectModel {
  const _$AigcSampleExportProjectModelImpl(
      {@JsonKey(name: 'project_id') required this.projectId,
      @JsonKey(name: 'project_name') required this.projectName,
      final List<AigcSampleExportProofingModel> proofings = const []})
      : _proofings = proofings;

  factory _$AigcSampleExportProjectModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AigcSampleExportProjectModelImplFromJson(json);

// 本地项目id
  @override
  @JsonKey(name: 'project_id')
  final String projectId;
// 本地项目名称
  @override
  @JsonKey(name: 'project_name')
  final String projectName;
// 打样列表
  final List<AigcSampleExportProofingModel> _proofings;
// 打样列表
  @override
  @JsonKey()
  List<AigcSampleExportProofingModel> get proofings {
    if (_proofings is EqualUnmodifiableListView) return _proofings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_proofings);
  }

  @override
  String toString() {
    return 'AigcSampleExportProjectModel(projectId: $projectId, projectName: $projectName, proofings: $proofings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleExportProjectModelImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.projectName, projectName) ||
                other.projectName == projectName) &&
            const DeepCollectionEquality()
                .equals(other._proofings, _proofings));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, projectId, projectName,
      const DeepCollectionEquality().hash(_proofings));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleExportProjectModelImplCopyWith<
          _$AigcSampleExportProjectModelImpl>
      get copyWith => __$$AigcSampleExportProjectModelImplCopyWithImpl<
          _$AigcSampleExportProjectModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleExportProjectModelImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleExportProjectModel
    implements AigcSampleExportProjectModel {
  const factory _AigcSampleExportProjectModel(
          {@JsonKey(name: 'project_id') required final String projectId,
          @JsonKey(name: 'project_name') required final String projectName,
          final List<AigcSampleExportProofingModel> proofings}) =
      _$AigcSampleExportProjectModelImpl;

  factory _AigcSampleExportProjectModel.fromJson(Map<String, dynamic> json) =
      _$AigcSampleExportProjectModelImpl.fromJson;

  @override // 本地项目id
  @JsonKey(name: 'project_id')
  String get projectId;
  @override // 本地项目名称
  @JsonKey(name: 'project_name')
  String get projectName;
  @override // 打样列表
  List<AigcSampleExportProofingModel> get proofings;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleExportProjectModelImplCopyWith<
          _$AigcSampleExportProjectModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
