// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_list_export_requst.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProofingExportRequest _$ProofingExportRequestFromJson(
    Map<String, dynamic> json) {
  return _ProofingExportRequest.fromJson(json);
}

/// @nodoc
mixin _$ProofingExportRequest {
// ignore: invalid_annotation_target
  @JsonKey(name: 'id')
  String get proofingId => throw _privateConstructorUsedError; // 打样项目ID
// ignore: invalid_annotation_target
  @JsonKey(name: 'effect_code')
  String get effectCode => throw _privateConstructorUsedError;

  /// Serializes this ProofingExportRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProofingExportRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProofingExportRequestCopyWith<ProofingExportRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProofingExportRequestCopyWith<$Res> {
  factory $ProofingExportRequestCopyWith(ProofingExportRequest value,
          $Res Function(ProofingExportRequest) then) =
      _$ProofingExportRequestCopyWithImpl<$Res, ProofingExportRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String proofingId,
      @JsonKey(name: 'effect_code') String effectCode});
}

/// @nodoc
class _$ProofingExportRequestCopyWithImpl<$Res,
        $Val extends ProofingExportRequest>
    implements $ProofingExportRequestCopyWith<$Res> {
  _$ProofingExportRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProofingExportRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proofingId = null,
    Object? effectCode = null,
  }) {
    return _then(_value.copyWith(
      proofingId: null == proofingId
          ? _value.proofingId
          : proofingId // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProofingExportRequestImplCopyWith<$Res>
    implements $ProofingExportRequestCopyWith<$Res> {
  factory _$$ProofingExportRequestImplCopyWith(
          _$ProofingExportRequestImpl value,
          $Res Function(_$ProofingExportRequestImpl) then) =
      __$$ProofingExportRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String proofingId,
      @JsonKey(name: 'effect_code') String effectCode});
}

/// @nodoc
class __$$ProofingExportRequestImplCopyWithImpl<$Res>
    extends _$ProofingExportRequestCopyWithImpl<$Res,
        _$ProofingExportRequestImpl>
    implements _$$ProofingExportRequestImplCopyWith<$Res> {
  __$$ProofingExportRequestImplCopyWithImpl(_$ProofingExportRequestImpl _value,
      $Res Function(_$ProofingExportRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProofingExportRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? proofingId = null,
    Object? effectCode = null,
  }) {
    return _then(_$ProofingExportRequestImpl(
      proofingId: null == proofingId
          ? _value.proofingId
          : proofingId // ignore: cast_nullable_to_non_nullable
              as String,
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProofingExportRequestImpl implements _ProofingExportRequest {
  const _$ProofingExportRequestImpl(
      {@JsonKey(name: 'id') required this.proofingId,
      @JsonKey(name: 'effect_code') required this.effectCode});

  factory _$ProofingExportRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProofingExportRequestImplFromJson(json);

// ignore: invalid_annotation_target
  @override
  @JsonKey(name: 'id')
  final String proofingId;
// 打样项目ID
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: 'effect_code')
  final String effectCode;

  @override
  String toString() {
    return 'ProofingExportRequest(proofingId: $proofingId, effectCode: $effectCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProofingExportRequestImpl &&
            (identical(other.proofingId, proofingId) ||
                other.proofingId == proofingId) &&
            (identical(other.effectCode, effectCode) ||
                other.effectCode == effectCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, proofingId, effectCode);

  /// Create a copy of ProofingExportRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProofingExportRequestImplCopyWith<_$ProofingExportRequestImpl>
      get copyWith => __$$ProofingExportRequestImplCopyWithImpl<
          _$ProofingExportRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProofingExportRequestImplToJson(
      this,
    );
  }
}

abstract class _ProofingExportRequest implements ProofingExportRequest {
  const factory _ProofingExportRequest(
          {@JsonKey(name: 'id') required final String proofingId,
          @JsonKey(name: 'effect_code') required final String effectCode}) =
      _$ProofingExportRequestImpl;

  factory _ProofingExportRequest.fromJson(Map<String, dynamic> json) =
      _$ProofingExportRequestImpl.fromJson;

// ignore: invalid_annotation_target
  @override
  @JsonKey(name: 'id')
  String get proofingId; // 打样项目ID
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: 'effect_code')
  String get effectCode;

  /// Create a copy of ProofingExportRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProofingExportRequestImplCopyWith<_$ProofingExportRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
