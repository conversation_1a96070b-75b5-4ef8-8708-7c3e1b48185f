// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_sample_project_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcSampleProjectModel _$AigcSampleProjectModelFromJson(
    Map<String, dynamic> json) {
  return _AigcSampleProjectModel.fromJson(json);
}

/// @nodoc
mixin _$AigcSampleProjectModel {
  @JsonKey(name: 'project_id')
  String get projectId => throw _privateConstructorUsedError;
  @JsonKey(name: 'project_name')
  String get projectName => throw _privateConstructorUsedError;
  @JsonKey(name: 'create_at')
  int get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'update_at')
  int get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcSampleProjectModelCopyWith<AigcSampleProjectModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcSampleProjectModelCopyWith<$Res> {
  factory $AigcSampleProjectModelCopyWith(AigcSampleProjectModel value,
          $Res Function(AigcSampleProjectModel) then) =
      _$AigcSampleProjectModelCopyWithImpl<$Res, AigcSampleProjectModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      @JsonKey(name: 'create_at') int createdAt,
      @JsonKey(name: 'update_at') int updatedAt});
}

/// @nodoc
class _$AigcSampleProjectModelCopyWithImpl<$Res,
        $Val extends AigcSampleProjectModel>
    implements $AigcSampleProjectModelCopyWith<$Res> {
  _$AigcSampleProjectModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? projectName = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcSampleProjectModelImplCopyWith<$Res>
    implements $AigcSampleProjectModelCopyWith<$Res> {
  factory _$$AigcSampleProjectModelImplCopyWith(
          _$AigcSampleProjectModelImpl value,
          $Res Function(_$AigcSampleProjectModelImpl) then) =
      __$$AigcSampleProjectModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'project_id') String projectId,
      @JsonKey(name: 'project_name') String projectName,
      @JsonKey(name: 'create_at') int createdAt,
      @JsonKey(name: 'update_at') int updatedAt});
}

/// @nodoc
class __$$AigcSampleProjectModelImplCopyWithImpl<$Res>
    extends _$AigcSampleProjectModelCopyWithImpl<$Res,
        _$AigcSampleProjectModelImpl>
    implements _$$AigcSampleProjectModelImplCopyWith<$Res> {
  __$$AigcSampleProjectModelImplCopyWithImpl(
      _$AigcSampleProjectModelImpl _value,
      $Res Function(_$AigcSampleProjectModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
    Object? projectName = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$AigcSampleProjectModelImpl(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      projectName: null == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcSampleProjectModelImpl implements _AigcSampleProjectModel {
  const _$AigcSampleProjectModelImpl(
      {@JsonKey(name: 'project_id') required this.projectId,
      @JsonKey(name: 'project_name') required this.projectName,
      @JsonKey(name: 'create_at') required this.createdAt,
      @JsonKey(name: 'update_at') required this.updatedAt});

  factory _$AigcSampleProjectModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcSampleProjectModelImplFromJson(json);

  @override
  @JsonKey(name: 'project_id')
  final String projectId;
  @override
  @JsonKey(name: 'project_name')
  final String projectName;
  @override
  @JsonKey(name: 'create_at')
  final int createdAt;
  @override
  @JsonKey(name: 'update_at')
  final int updatedAt;

  @override
  String toString() {
    return 'AigcSampleProjectModel(projectId: $projectId, projectName: $projectName, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcSampleProjectModelImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.projectName, projectName) ||
                other.projectName == projectName) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, projectId, projectName, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcSampleProjectModelImplCopyWith<_$AigcSampleProjectModelImpl>
      get copyWith => __$$AigcSampleProjectModelImplCopyWithImpl<
          _$AigcSampleProjectModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcSampleProjectModelImplToJson(
      this,
    );
  }
}

abstract class _AigcSampleProjectModel implements AigcSampleProjectModel {
  const factory _AigcSampleProjectModel(
          {@JsonKey(name: 'project_id') required final String projectId,
          @JsonKey(name: 'project_name') required final String projectName,
          @JsonKey(name: 'create_at') required final int createdAt,
          @JsonKey(name: 'update_at') required final int updatedAt}) =
      _$AigcSampleProjectModelImpl;

  factory _AigcSampleProjectModel.fromJson(Map<String, dynamic> json) =
      _$AigcSampleProjectModelImpl.fromJson;

  @override
  @JsonKey(name: 'project_id')
  String get projectId;
  @override
  @JsonKey(name: 'project_name')
  String get projectName;
  @override
  @JsonKey(name: 'create_at')
  int get createdAt;
  @override
  @JsonKey(name: 'update_at')
  int get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$AigcSampleProjectModelImplCopyWith<_$AigcSampleProjectModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
