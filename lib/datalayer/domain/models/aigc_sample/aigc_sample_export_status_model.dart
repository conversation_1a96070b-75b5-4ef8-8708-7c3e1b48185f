// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_sample_export_status_model.freezed.dart';
part 'aigc_sample_export_status_model.g.dart';

@freezed
class AigcSampleExportStatusModel with _$AigcSampleExportStatusModel {
  const factory AigcSampleExportStatusModel(
      {
      // 打样项目ID
      required String id,
      // 打样效果图ID
      @JsonKey(name: 'effect_code') required String effectCode,
      // 导出状态：running、unexported、completed
      required String status,
      // 导出高清大图地址
      @JsonKey(name: 'export_photo_url')
      @Default("")
      String exportPhotoUrl}) = _AigcSampleExportStatusModel;

  factory AigcSampleExportStatusModel.fromJson(Map<String, dynamic> json) =>
      _$AigcSampleExportStatusModelFromJson(json);
}
