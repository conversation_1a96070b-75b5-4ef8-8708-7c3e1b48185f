// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_sample_export_project_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcSampleExportProjectModelImpl _$$AigcSampleExportProjectModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcSampleExportProjectModelImpl(
      projectId: json['project_id'] as String,
      projectName: json['project_name'] as String,
      proofings: (json['proofings'] as List<dynamic>?)
              ?.map((e) => AigcSampleExportProofingModel.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$AigcSampleExportProjectModelImplToJson(
        _$AigcSampleExportProjectModelImpl instance) =>
    <String, dynamic>{
      'project_id': instance.projectId,
      'project_name': instance.projectName,
      'proofings': instance.proofings,
    };
