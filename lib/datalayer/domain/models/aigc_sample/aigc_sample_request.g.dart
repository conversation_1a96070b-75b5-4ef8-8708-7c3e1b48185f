// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_sample_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcSampleRequestImpl _$$AigcSampleRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcSampleRequestImpl(
      clientProject: ClientProject.fromJson(
          json['client_project'] as Map<String, dynamic>),
      preset: Preset.fromJson(json['preset'] as Map<String, dynamic>),
      originPhoto:
          OriginPhoto.fromJson(json['origin_photo'] as Map<String, dynamic>),
      maskImageUrl: json['mask_image_url'] as String,
      clientResourceId: json['client_resource_id'] as String,
    );

Map<String, dynamic> _$$AigcSampleRequestImplToJson(
        _$AigcSampleRequestImpl instance) =>
    <String, dynamic>{
      'client_project': instance.clientProject,
      'preset': instance.preset,
      'origin_photo': instance.originPhoto,
      'mask_image_url': instance.maskImageUrl,
      'client_resource_id': instance.clientResourceId,
    };

_$ClientProjectImpl _$$ClientProjectImplFromJson(Map<String, dynamic> json) =>
    _$ClientProjectImpl(
      projectId: json['project_id'] as String,
      projectName: json['project_name'] as String,
      updateAt: (json['update_at'] as num).toInt(),
    );

Map<String, dynamic> _$$ClientProjectImplToJson(_$ClientProjectImpl instance) =>
    <String, dynamic>{
      'project_id': instance.projectId,
      'project_name': instance.projectName,
      'update_at': instance.updateAt,
    };

_$PresetImpl _$$PresetImplFromJson(Map<String, dynamic> json) => _$PresetImpl(
      presetId: json['preset_id'] as String,
      effectCode: json['effect_code'] as String,
    );

Map<String, dynamic> _$$PresetImplToJson(_$PresetImpl instance) =>
    <String, dynamic>{
      'preset_id': instance.presetId,
      'effect_code': instance.effectCode,
    };

_$OriginPhotoImpl _$$OriginPhotoImplFromJson(Map<String, dynamic> json) =>
    _$OriginPhotoImpl(
      fileName: json['file_name'] as String,
      fileUrl: json['file_url'] as String,
    );

Map<String, dynamic> _$$OriginPhotoImplToJson(_$OriginPhotoImpl instance) =>
    <String, dynamic>{
      'file_name': instance.fileName,
      'file_url': instance.fileUrl,
    };
