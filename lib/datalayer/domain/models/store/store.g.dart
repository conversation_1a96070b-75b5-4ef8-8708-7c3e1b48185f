// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StoreImpl _$$StoreImplFromJson(Map<String, dynamic> json) => _$StoreImpl(
      id: json['id'] as String,
      tel: json['tel'] as String,
      name: json['name'] as String?,
      address: json['address'] as String?,
      customerSupport: json['customerSupport'] as String?,
      volumeControl: json['volumeControl'] as bool?,
      rewards: json['rewards'] == null
          ? null
          : Rewards.fromJson(json['rewards'] as Map<String, dynamic>),
      capabilities: (json['capabilities'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$StoreImplToJson(_$StoreImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'tel': instance.tel,
      'name': instance.name,
      'address': instance.address,
      'customerSupport': instance.customerSupport,
      'volumeControl': instance.volumeControl,
      'rewards': instance.rewards,
      'capabilities': instance.capabilities,
    };
