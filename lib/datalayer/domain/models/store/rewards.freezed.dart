// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rewards.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Rewards _$RewardsFromJson(Map<String, dynamic> json) {
  return _Rewards.fromJson(json);
}

/// @nodoc
mixin _$Rewards {
  @JsonKey(name: 'add_wxwork')
  Reward? get addWxwork => throw _privateConstructorUsedError;
  @JsonKey(name: 'new_register')
  Reward? get newRegister => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardsCopyWith<Rewards> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardsCopyWith<$Res> {
  factory $RewardsCopyWith(Rewards value, $Res Function(Rewards) then) =
      _$RewardsCopyWithImpl<$Res, Rewards>;
  @useResult
  $Res call(
      {@JsonKey(name: 'add_wxwork') Reward? addWxwork,
      @JsonKey(name: 'new_register') Reward? newRegister});

  $RewardCopyWith<$Res>? get addWxwork;
  $RewardCopyWith<$Res>? get newRegister;
}

/// @nodoc
class _$RewardsCopyWithImpl<$Res, $Val extends Rewards>
    implements $RewardsCopyWith<$Res> {
  _$RewardsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addWxwork = freezed,
    Object? newRegister = freezed,
  }) {
    return _then(_value.copyWith(
      addWxwork: freezed == addWxwork
          ? _value.addWxwork
          : addWxwork // ignore: cast_nullable_to_non_nullable
              as Reward?,
      newRegister: freezed == newRegister
          ? _value.newRegister
          : newRegister // ignore: cast_nullable_to_non_nullable
              as Reward?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RewardCopyWith<$Res>? get addWxwork {
    if (_value.addWxwork == null) {
      return null;
    }

    return $RewardCopyWith<$Res>(_value.addWxwork!, (value) {
      return _then(_value.copyWith(addWxwork: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RewardCopyWith<$Res>? get newRegister {
    if (_value.newRegister == null) {
      return null;
    }

    return $RewardCopyWith<$Res>(_value.newRegister!, (value) {
      return _then(_value.copyWith(newRegister: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RewardsImplCopyWith<$Res> implements $RewardsCopyWith<$Res> {
  factory _$$RewardsImplCopyWith(
          _$RewardsImpl value, $Res Function(_$RewardsImpl) then) =
      __$$RewardsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'add_wxwork') Reward? addWxwork,
      @JsonKey(name: 'new_register') Reward? newRegister});

  @override
  $RewardCopyWith<$Res>? get addWxwork;
  @override
  $RewardCopyWith<$Res>? get newRegister;
}

/// @nodoc
class __$$RewardsImplCopyWithImpl<$Res>
    extends _$RewardsCopyWithImpl<$Res, _$RewardsImpl>
    implements _$$RewardsImplCopyWith<$Res> {
  __$$RewardsImplCopyWithImpl(
      _$RewardsImpl _value, $Res Function(_$RewardsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addWxwork = freezed,
    Object? newRegister = freezed,
  }) {
    return _then(_$RewardsImpl(
      addWxwork: freezed == addWxwork
          ? _value.addWxwork
          : addWxwork // ignore: cast_nullable_to_non_nullable
              as Reward?,
      newRegister: freezed == newRegister
          ? _value.newRegister
          : newRegister // ignore: cast_nullable_to_non_nullable
              as Reward?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RewardsImpl implements _Rewards {
  const _$RewardsImpl(
      {@JsonKey(name: 'add_wxwork') this.addWxwork,
      @JsonKey(name: 'new_register') this.newRegister});

  factory _$RewardsImpl.fromJson(Map<String, dynamic> json) =>
      _$$RewardsImplFromJson(json);

  @override
  @JsonKey(name: 'add_wxwork')
  final Reward? addWxwork;
  @override
  @JsonKey(name: 'new_register')
  final Reward? newRegister;

  @override
  String toString() {
    return 'Rewards(addWxwork: $addWxwork, newRegister: $newRegister)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RewardsImpl &&
            (identical(other.addWxwork, addWxwork) ||
                other.addWxwork == addWxwork) &&
            (identical(other.newRegister, newRegister) ||
                other.newRegister == newRegister));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, addWxwork, newRegister);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RewardsImplCopyWith<_$RewardsImpl> get copyWith =>
      __$$RewardsImplCopyWithImpl<_$RewardsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RewardsImplToJson(
      this,
    );
  }
}

abstract class _Rewards implements Rewards {
  const factory _Rewards(
          {@JsonKey(name: 'add_wxwork') final Reward? addWxwork,
          @JsonKey(name: 'new_register') final Reward? newRegister}) =
      _$RewardsImpl;

  factory _Rewards.fromJson(Map<String, dynamic> json) = _$RewardsImpl.fromJson;

  @override
  @JsonKey(name: 'add_wxwork')
  Reward? get addWxwork;
  @override
  @JsonKey(name: 'new_register')
  Reward? get newRegister;
  @override
  @JsonKey(ignore: true)
  _$$RewardsImplCopyWith<_$RewardsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
