// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'store.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Store _$StoreFromJson(Map<String, dynamic> json) {
  return _Store.fromJson(json);
}

/// @nodoc
mixin _$Store {
  String get id => throw _privateConstructorUsedError;
  String get tel => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get customerSupport => throw _privateConstructorUsedError;
  bool? get volumeControl =>
      throw _privateConstructorUsedError; // 是否开启账号管理（片量共享入口）
// 奖励
  Rewards? get rewards => throw _privateConstructorUsedError; // 能力列表
  List<String> get capabilities => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StoreCopyWith<Store> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoreCopyWith<$Res> {
  factory $StoreCopyWith(Store value, $Res Function(Store) then) =
      _$StoreCopyWithImpl<$Res, Store>;
  @useResult
  $Res call(
      {String id,
      String tel,
      String? name,
      String? address,
      String? customerSupport,
      bool? volumeControl,
      Rewards? rewards,
      List<String> capabilities});

  $RewardsCopyWith<$Res>? get rewards;
}

/// @nodoc
class _$StoreCopyWithImpl<$Res, $Val extends Store>
    implements $StoreCopyWith<$Res> {
  _$StoreCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? tel = null,
    Object? name = freezed,
    Object? address = freezed,
    Object? customerSupport = freezed,
    Object? volumeControl = freezed,
    Object? rewards = freezed,
    Object? capabilities = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      tel: null == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      customerSupport: freezed == customerSupport
          ? _value.customerSupport
          : customerSupport // ignore: cast_nullable_to_non_nullable
              as String?,
      volumeControl: freezed == volumeControl
          ? _value.volumeControl
          : volumeControl // ignore: cast_nullable_to_non_nullable
              as bool?,
      rewards: freezed == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as Rewards?,
      capabilities: null == capabilities
          ? _value.capabilities
          : capabilities // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RewardsCopyWith<$Res>? get rewards {
    if (_value.rewards == null) {
      return null;
    }

    return $RewardsCopyWith<$Res>(_value.rewards!, (value) {
      return _then(_value.copyWith(rewards: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$StoreImplCopyWith<$Res> implements $StoreCopyWith<$Res> {
  factory _$$StoreImplCopyWith(
          _$StoreImpl value, $Res Function(_$StoreImpl) then) =
      __$$StoreImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String tel,
      String? name,
      String? address,
      String? customerSupport,
      bool? volumeControl,
      Rewards? rewards,
      List<String> capabilities});

  @override
  $RewardsCopyWith<$Res>? get rewards;
}

/// @nodoc
class __$$StoreImplCopyWithImpl<$Res>
    extends _$StoreCopyWithImpl<$Res, _$StoreImpl>
    implements _$$StoreImplCopyWith<$Res> {
  __$$StoreImplCopyWithImpl(
      _$StoreImpl _value, $Res Function(_$StoreImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? tel = null,
    Object? name = freezed,
    Object? address = freezed,
    Object? customerSupport = freezed,
    Object? volumeControl = freezed,
    Object? rewards = freezed,
    Object? capabilities = null,
  }) {
    return _then(_$StoreImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      tel: null == tel
          ? _value.tel
          : tel // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      customerSupport: freezed == customerSupport
          ? _value.customerSupport
          : customerSupport // ignore: cast_nullable_to_non_nullable
              as String?,
      volumeControl: freezed == volumeControl
          ? _value.volumeControl
          : volumeControl // ignore: cast_nullable_to_non_nullable
              as bool?,
      rewards: freezed == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as Rewards?,
      capabilities: null == capabilities
          ? _value._capabilities
          : capabilities // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StoreImpl extends _Store {
  const _$StoreImpl(
      {required this.id,
      required this.tel,
      this.name,
      this.address,
      this.customerSupport,
      this.volumeControl,
      this.rewards,
      final List<String> capabilities = const []})
      : _capabilities = capabilities,
        super._();

  factory _$StoreImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoreImplFromJson(json);

  @override
  final String id;
  @override
  final String tel;
  @override
  final String? name;
  @override
  final String? address;
  @override
  final String? customerSupport;
  @override
  final bool? volumeControl;
// 是否开启账号管理（片量共享入口）
// 奖励
  @override
  final Rewards? rewards;
// 能力列表
  final List<String> _capabilities;
// 能力列表
  @override
  @JsonKey()
  List<String> get capabilities {
    if (_capabilities is EqualUnmodifiableListView) return _capabilities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_capabilities);
  }

  @override
  String toString() {
    return 'Store(id: $id, tel: $tel, name: $name, address: $address, customerSupport: $customerSupport, volumeControl: $volumeControl, rewards: $rewards, capabilities: $capabilities)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoreImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.tel, tel) || other.tel == tel) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.customerSupport, customerSupport) ||
                other.customerSupport == customerSupport) &&
            (identical(other.volumeControl, volumeControl) ||
                other.volumeControl == volumeControl) &&
            (identical(other.rewards, rewards) || other.rewards == rewards) &&
            const DeepCollectionEquality()
                .equals(other._capabilities, _capabilities));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      tel,
      name,
      address,
      customerSupport,
      volumeControl,
      rewards,
      const DeepCollectionEquality().hash(_capabilities));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StoreImplCopyWith<_$StoreImpl> get copyWith =>
      __$$StoreImplCopyWithImpl<_$StoreImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoreImplToJson(
      this,
    );
  }
}

abstract class _Store extends Store {
  const factory _Store(
      {required final String id,
      required final String tel,
      final String? name,
      final String? address,
      final String? customerSupport,
      final bool? volumeControl,
      final Rewards? rewards,
      final List<String> capabilities}) = _$StoreImpl;
  const _Store._() : super._();

  factory _Store.fromJson(Map<String, dynamic> json) = _$StoreImpl.fromJson;

  @override
  String get id;
  @override
  String get tel;
  @override
  String? get name;
  @override
  String? get address;
  @override
  String? get customerSupport;
  @override
  bool? get volumeControl;
  @override // 是否开启账号管理（片量共享入口）
// 奖励
  Rewards? get rewards;
  @override // 能力列表
  List<String> get capabilities;
  @override
  @JsonKey(ignore: true)
  _$$StoreImplCopyWith<_$StoreImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
