// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/store/reward.dart';

part 'rewards.freezed.dart';
part 'rewards.g.dart';

@freezed
class Rewards with _$Rewards {
  const factory Rewards({
    @JsonKey(name: 'add_wxwork') Reward? addWxwork,
    @JsonKey(name: 'new_register') Reward? newRegister,
  }) = _Rewards;

  factory Rewards.fromJson(Map<String, dynamic> json) =>
      _$RewardsFromJson(json);
}
