import 'package:freezed_annotation/freezed_annotation.dart';

part 'reward.freezed.dart';
part 'reward.g.dart';

@freezed
class Reward with _$Reward {
  const factory Reward({
    // 下放的 ID(运营活动id)
    required String rewardId,
    // 下放的金額
    required int amount,
    // 下放的理由(运营活动名称分类)
    required String reason,
    // 奖励是否已经发放
    required bool issued,
  }) = _Reward;

  factory Reward.fromJson(Map<String, dynamic> json) => _$RewardFromJson(json);
}
