// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'export_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExportConfig _$ExportConfigFromJson(Map<String, dynamic> json) {
  return _ExportConfig.fromJson(json);
}

/// @nodoc
mixin _$ExportConfig {
// 导出文件夹
  String get outputFolder => throw _privateConstructorUsedError; // 导出路径类型
  ExportPathType get pathType => throw _privateConstructorUsedError; // 导出文件类型
  ExportFileType get exportFileType =>
      throw _privateConstructorUsedError; // 导出质量(如果是jpg，需要指定质量，0~100)
  ExportQualityType get quality =>
      throw _privateConstructorUsedError; // 是否覆盖同名文件；如果不覆盖，会自动重新命名
  bool get isReplace => throw _privateConstructorUsedError; // 是否转换为sRGB颜色空间
  bool get transferSRGB => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportConfigCopyWith<ExportConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportConfigCopyWith<$Res> {
  factory $ExportConfigCopyWith(
          ExportConfig value, $Res Function(ExportConfig) then) =
      _$ExportConfigCopyWithImpl<$Res, ExportConfig>;
  @useResult
  $Res call(
      {String outputFolder,
      ExportPathType pathType,
      ExportFileType exportFileType,
      ExportQualityType quality,
      bool isReplace,
      bool transferSRGB});
}

/// @nodoc
class _$ExportConfigCopyWithImpl<$Res, $Val extends ExportConfig>
    implements $ExportConfigCopyWith<$Res> {
  _$ExportConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? outputFolder = null,
    Object? pathType = null,
    Object? exportFileType = null,
    Object? quality = null,
    Object? isReplace = null,
    Object? transferSRGB = null,
  }) {
    return _then(_value.copyWith(
      outputFolder: null == outputFolder
          ? _value.outputFolder
          : outputFolder // ignore: cast_nullable_to_non_nullable
              as String,
      pathType: null == pathType
          ? _value.pathType
          : pathType // ignore: cast_nullable_to_non_nullable
              as ExportPathType,
      exportFileType: null == exportFileType
          ? _value.exportFileType
          : exportFileType // ignore: cast_nullable_to_non_nullable
              as ExportFileType,
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as ExportQualityType,
      isReplace: null == isReplace
          ? _value.isReplace
          : isReplace // ignore: cast_nullable_to_non_nullable
              as bool,
      transferSRGB: null == transferSRGB
          ? _value.transferSRGB
          : transferSRGB // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportConfigImplCopyWith<$Res>
    implements $ExportConfigCopyWith<$Res> {
  factory _$$ExportConfigImplCopyWith(
          _$ExportConfigImpl value, $Res Function(_$ExportConfigImpl) then) =
      __$$ExportConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String outputFolder,
      ExportPathType pathType,
      ExportFileType exportFileType,
      ExportQualityType quality,
      bool isReplace,
      bool transferSRGB});
}

/// @nodoc
class __$$ExportConfigImplCopyWithImpl<$Res>
    extends _$ExportConfigCopyWithImpl<$Res, _$ExportConfigImpl>
    implements _$$ExportConfigImplCopyWith<$Res> {
  __$$ExportConfigImplCopyWithImpl(
      _$ExportConfigImpl _value, $Res Function(_$ExportConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? outputFolder = null,
    Object? pathType = null,
    Object? exportFileType = null,
    Object? quality = null,
    Object? isReplace = null,
    Object? transferSRGB = null,
  }) {
    return _then(_$ExportConfigImpl(
      outputFolder: null == outputFolder
          ? _value.outputFolder
          : outputFolder // ignore: cast_nullable_to_non_nullable
              as String,
      pathType: null == pathType
          ? _value.pathType
          : pathType // ignore: cast_nullable_to_non_nullable
              as ExportPathType,
      exportFileType: null == exportFileType
          ? _value.exportFileType
          : exportFileType // ignore: cast_nullable_to_non_nullable
              as ExportFileType,
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as ExportQualityType,
      isReplace: null == isReplace
          ? _value.isReplace
          : isReplace // ignore: cast_nullable_to_non_nullable
              as bool,
      transferSRGB: null == transferSRGB
          ? _value.transferSRGB
          : transferSRGB // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportConfigImpl extends _ExportConfig {
  const _$ExportConfigImpl(
      {required this.outputFolder,
      required this.pathType,
      required this.exportFileType,
      required this.quality,
      required this.isReplace,
      required this.transferSRGB})
      : super._();

  factory _$ExportConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportConfigImplFromJson(json);

// 导出文件夹
  @override
  final String outputFolder;
// 导出路径类型
  @override
  final ExportPathType pathType;
// 导出文件类型
  @override
  final ExportFileType exportFileType;
// 导出质量(如果是jpg，需要指定质量，0~100)
  @override
  final ExportQualityType quality;
// 是否覆盖同名文件；如果不覆盖，会自动重新命名
  @override
  final bool isReplace;
// 是否转换为sRGB颜色空间
  @override
  final bool transferSRGB;

  @override
  String toString() {
    return 'ExportConfig(outputFolder: $outputFolder, pathType: $pathType, exportFileType: $exportFileType, quality: $quality, isReplace: $isReplace, transferSRGB: $transferSRGB)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportConfigImpl &&
            (identical(other.outputFolder, outputFolder) ||
                other.outputFolder == outputFolder) &&
            (identical(other.pathType, pathType) ||
                other.pathType == pathType) &&
            (identical(other.exportFileType, exportFileType) ||
                other.exportFileType == exportFileType) &&
            (identical(other.quality, quality) || other.quality == quality) &&
            (identical(other.isReplace, isReplace) ||
                other.isReplace == isReplace) &&
            (identical(other.transferSRGB, transferSRGB) ||
                other.transferSRGB == transferSRGB));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, outputFolder, pathType,
      exportFileType, quality, isReplace, transferSRGB);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportConfigImplCopyWith<_$ExportConfigImpl> get copyWith =>
      __$$ExportConfigImplCopyWithImpl<_$ExportConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportConfigImplToJson(
      this,
    );
  }
}

abstract class _ExportConfig extends ExportConfig {
  const factory _ExportConfig(
      {required final String outputFolder,
      required final ExportPathType pathType,
      required final ExportFileType exportFileType,
      required final ExportQualityType quality,
      required final bool isReplace,
      required final bool transferSRGB}) = _$ExportConfigImpl;
  const _ExportConfig._() : super._();

  factory _ExportConfig.fromJson(Map<String, dynamic> json) =
      _$ExportConfigImpl.fromJson;

  @override // 导出文件夹
  String get outputFolder;
  @override // 导出路径类型
  ExportPathType get pathType;
  @override // 导出文件类型
  ExportFileType get exportFileType;
  @override // 导出质量(如果是jpg，需要指定质量，0~100)
  ExportQualityType get quality;
  @override // 是否覆盖同名文件；如果不覆盖，会自动重新命名
  bool get isReplace;
  @override // 是否转换为sRGB颜色空间
  bool get transferSRGB;
  @override
  @JsonKey(ignore: true)
  _$$ExportConfigImplCopyWith<_$ExportConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
