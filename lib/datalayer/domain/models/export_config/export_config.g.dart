// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'export_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExportConfigImpl _$$ExportConfigImplFromJson(Map<String, dynamic> json) =>
    _$ExportConfigImpl(
      outputFolder: json['outputFolder'] as String,
      pathType: $enumDecode(_$ExportPathTypeEnumMap, json['pathType']),
      exportFileType:
          $enumDecode(_$ExportFileTypeEnumMap, json['exportFileType']),
      quality: $enumDecode(_$ExportQualityTypeEnumMap, json['quality']),
      isReplace: json['isReplace'] as bool,
      transferSRGB: json['transferSRGB'] as bool,
    );

Map<String, dynamic> _$$ExportConfigImplToJson(_$ExportConfigImpl instance) =>
    <String, dynamic>{
      'outputFolder': instance.outputFolder,
      'pathType': _$ExportPathTypeEnumMap[instance.pathType]!,
      'exportFileType': _$ExportFileTypeEnumMap[instance.exportFileType]!,
      'quality': _$ExportQualityTypeEnumMap[instance.quality]!,
      'isReplace': instance.isReplace,
      'transferSRGB': instance.transferSRGB,
    };

const _$ExportPathTypeEnumMap = {
  ExportPathType.specifyFolder: 1,
  ExportPathType.originalFolder: 2,
};

const _$ExportFileTypeEnumMap = {
  ExportFileType.original: 0,
  ExportFileType.png: 1,
  ExportFileType.jpg: 2,
};

const _$ExportQualityTypeEnumMap = {
  ExportQualityType.low: 30,
  ExportQualityType.medium: 60,
  ExportQualityType.high: 90,
  ExportQualityType.lossless: 100,
};
