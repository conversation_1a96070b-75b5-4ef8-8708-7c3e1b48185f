import 'package:freezed_annotation/freezed_annotation.dart';

part 'export_config.freezed.dart';
part 'export_config.g.dart';

enum ExportFileType {
  @JsonValue(0)
  original,
  @JsonValue(1)
  png,
  @JsonValue(2)
  jpg;
}

enum ExportPathType {
  @JsonValue(1)
  specifyFolder,
  @JsonValue(2)
  originalFolder;
}

enum ExportQualityType {
  @JsonValue(30)
  low,
  @JsonValue(60)
  medium,
  @JsonValue(90)
  high,
  @JsonValue(100)
  lossless;

  static int toValue(ExportQualityType quality) {
    switch (quality) {
      case ExportQualityType.low:
        return 30;
      case ExportQualityType.medium:
        return 60;
      case ExportQualityType.high:
        return 90;
      case ExportQualityType.lossless:
        return 100;
    }
  }
}

// 导出配置
@freezed
class ExportConfig with _$ExportConfig {
  const factory ExportConfig({
    // 导出文件夹
    required String outputFolder,
    // 导出路径类型
    required ExportPathType pathType,
    // 导出文件类型
    required ExportFileType exportFileType,
    // 导出质量(如果是jpg，需要指定质量，0~100)
    required ExportQualityType quality,

    // 是否覆盖同名文件；如果不覆盖，会自动重新命名
    required bool isReplace,

    // 是否转换为sRGB颜色空间
    required bool transferSRGB,
  }) = _ExportConfig;

  const ExportConfig._();

  factory ExportConfig.fromJson(Map<String, dynamic> json) =>
      _$ExportConfigFromJson(json);

  factory ExportConfig.defaultConfig() {
    return const ExportConfig(
      outputFolder: '',
      exportFileType: ExportFileType.png,
      quality: ExportQualityType.high,
      isReplace: false,
      transferSRGB: true,
      pathType: ExportPathType.originalFolder,
    );
  }
}
