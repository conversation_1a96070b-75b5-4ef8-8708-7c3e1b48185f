import 'package:freezed_annotation/freezed_annotation.dart';

part 'message_from_unity.freezed.dart';
part 'message_from_unity.g.dart';

// Unity回调的消息,具体返回参数为不同接口单独约定，需要根据Unity对应接口的定义自定义解析
@freezed
class MessageFromUnity with _$MessageFromUnity {
  const factory MessageFromUnity({
    required String method,
    List<dynamic>? args,
    String? taskId,
  }) = _MessageFromUnity;
  const MessageFromUnity._();

  factory MessageFromUnity.fromJson(Map<String, dynamic> json) =>
      _$MessageFromUnityFromJson(json);
}
