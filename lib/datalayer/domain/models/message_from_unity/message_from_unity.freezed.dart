// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_from_unity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessageFromUnity _$MessageFromUnityFromJson(Map<String, dynamic> json) {
  return _MessageFromUnity.fromJson(json);
}

/// @nodoc
mixin _$MessageFromUnity {
  String get method => throw _privateConstructorUsedError;
  List<dynamic>? get args => throw _privateConstructorUsedError;
  String? get taskId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessageFromUnityCopyWith<MessageFromUnity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageFromUnityCopyWith<$Res> {
  factory $MessageFromUnityCopyWith(
          MessageFromUnity value, $Res Function(MessageFromUnity) then) =
      _$MessageFromUnityCopyWithImpl<$Res, MessageFromUnity>;
  @useResult
  $Res call({String method, List<dynamic>? args, String? taskId});
}

/// @nodoc
class _$MessageFromUnityCopyWithImpl<$Res, $Val extends MessageFromUnity>
    implements $MessageFromUnityCopyWith<$Res> {
  _$MessageFromUnityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? method = null,
    Object? args = freezed,
    Object? taskId = freezed,
  }) {
    return _then(_value.copyWith(
      method: null == method
          ? _value.method
          : method // ignore: cast_nullable_to_non_nullable
              as String,
      args: freezed == args
          ? _value.args
          : args // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessageFromUnityImplCopyWith<$Res>
    implements $MessageFromUnityCopyWith<$Res> {
  factory _$$MessageFromUnityImplCopyWith(_$MessageFromUnityImpl value,
          $Res Function(_$MessageFromUnityImpl) then) =
      __$$MessageFromUnityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String method, List<dynamic>? args, String? taskId});
}

/// @nodoc
class __$$MessageFromUnityImplCopyWithImpl<$Res>
    extends _$MessageFromUnityCopyWithImpl<$Res, _$MessageFromUnityImpl>
    implements _$$MessageFromUnityImplCopyWith<$Res> {
  __$$MessageFromUnityImplCopyWithImpl(_$MessageFromUnityImpl _value,
      $Res Function(_$MessageFromUnityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? method = null,
    Object? args = freezed,
    Object? taskId = freezed,
  }) {
    return _then(_$MessageFromUnityImpl(
      method: null == method
          ? _value.method
          : method // ignore: cast_nullable_to_non_nullable
              as String,
      args: freezed == args
          ? _value._args
          : args // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageFromUnityImpl extends _MessageFromUnity {
  const _$MessageFromUnityImpl(
      {required this.method, final List<dynamic>? args, this.taskId})
      : _args = args,
        super._();

  factory _$MessageFromUnityImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageFromUnityImplFromJson(json);

  @override
  final String method;
  final List<dynamic>? _args;
  @override
  List<dynamic>? get args {
    final value = _args;
    if (value == null) return null;
    if (_args is EqualUnmodifiableListView) return _args;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? taskId;

  @override
  String toString() {
    return 'MessageFromUnity(method: $method, args: $args, taskId: $taskId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageFromUnityImpl &&
            (identical(other.method, method) || other.method == method) &&
            const DeepCollectionEquality().equals(other._args, _args) &&
            (identical(other.taskId, taskId) || other.taskId == taskId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, method, const DeepCollectionEquality().hash(_args), taskId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageFromUnityImplCopyWith<_$MessageFromUnityImpl> get copyWith =>
      __$$MessageFromUnityImplCopyWithImpl<_$MessageFromUnityImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageFromUnityImplToJson(
      this,
    );
  }
}

abstract class _MessageFromUnity extends MessageFromUnity {
  const factory _MessageFromUnity(
      {required final String method,
      final List<dynamic>? args,
      final String? taskId}) = _$MessageFromUnityImpl;
  const _MessageFromUnity._() : super._();

  factory _MessageFromUnity.fromJson(Map<String, dynamic> json) =
      _$MessageFromUnityImpl.fromJson;

  @override
  String get method;
  @override
  List<dynamic>? get args;
  @override
  String? get taskId;
  @override
  @JsonKey(ignore: true)
  _$$MessageFromUnityImplCopyWith<_$MessageFromUnityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
