// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workspace.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Workspace _$WorkspaceFromJson(Map<String, dynamic> json) {
  return _Workspace.fromJson(json);
}

/// @nodoc
mixin _$Workspace {
  List<WorkspaceFile> get files => throw _privateConstructorUsedError;
  String get workspaceName => throw _privateConstructorUsedError;
  String get workspaceId => throw _privateConstructorUsedError;
  String? get currentFileId => throw _privateConstructorUsedError;
  int get createTime => throw _privateConstructorUsedError;
  int get lastEditTime => throw _privateConstructorUsedError;
  Map<String, dynamic>? get filterValue => throw _privateConstructorUsedError;
  Map<String, dynamic>? get sortValue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkspaceCopyWith<Workspace> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkspaceCopyWith<$Res> {
  factory $WorkspaceCopyWith(Workspace value, $Res Function(Workspace) then) =
      _$WorkspaceCopyWithImpl<$Res, Workspace>;
  @useResult
  $Res call(
      {List<WorkspaceFile> files,
      String workspaceName,
      String workspaceId,
      String? currentFileId,
      int createTime,
      int lastEditTime,
      Map<String, dynamic>? filterValue,
      Map<String, dynamic>? sortValue});
}

/// @nodoc
class _$WorkspaceCopyWithImpl<$Res, $Val extends Workspace>
    implements $WorkspaceCopyWith<$Res> {
  _$WorkspaceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? files = null,
    Object? workspaceName = null,
    Object? workspaceId = null,
    Object? currentFileId = freezed,
    Object? createTime = null,
    Object? lastEditTime = null,
    Object? filterValue = freezed,
    Object? sortValue = freezed,
  }) {
    return _then(_value.copyWith(
      files: null == files
          ? _value.files
          : files // ignore: cast_nullable_to_non_nullable
              as List<WorkspaceFile>,
      workspaceName: null == workspaceName
          ? _value.workspaceName
          : workspaceName // ignore: cast_nullable_to_non_nullable
              as String,
      workspaceId: null == workspaceId
          ? _value.workspaceId
          : workspaceId // ignore: cast_nullable_to_non_nullable
              as String,
      currentFileId: freezed == currentFileId
          ? _value.currentFileId
          : currentFileId // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      lastEditTime: null == lastEditTime
          ? _value.lastEditTime
          : lastEditTime // ignore: cast_nullable_to_non_nullable
              as int,
      filterValue: freezed == filterValue
          ? _value.filterValue
          : filterValue // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      sortValue: freezed == sortValue
          ? _value.sortValue
          : sortValue // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkspaceImplCopyWith<$Res>
    implements $WorkspaceCopyWith<$Res> {
  factory _$$WorkspaceImplCopyWith(
          _$WorkspaceImpl value, $Res Function(_$WorkspaceImpl) then) =
      __$$WorkspaceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<WorkspaceFile> files,
      String workspaceName,
      String workspaceId,
      String? currentFileId,
      int createTime,
      int lastEditTime,
      Map<String, dynamic>? filterValue,
      Map<String, dynamic>? sortValue});
}

/// @nodoc
class __$$WorkspaceImplCopyWithImpl<$Res>
    extends _$WorkspaceCopyWithImpl<$Res, _$WorkspaceImpl>
    implements _$$WorkspaceImplCopyWith<$Res> {
  __$$WorkspaceImplCopyWithImpl(
      _$WorkspaceImpl _value, $Res Function(_$WorkspaceImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? files = null,
    Object? workspaceName = null,
    Object? workspaceId = null,
    Object? currentFileId = freezed,
    Object? createTime = null,
    Object? lastEditTime = null,
    Object? filterValue = freezed,
    Object? sortValue = freezed,
  }) {
    return _then(_$WorkspaceImpl(
      files: null == files
          ? _value._files
          : files // ignore: cast_nullable_to_non_nullable
              as List<WorkspaceFile>,
      workspaceName: null == workspaceName
          ? _value.workspaceName
          : workspaceName // ignore: cast_nullable_to_non_nullable
              as String,
      workspaceId: null == workspaceId
          ? _value.workspaceId
          : workspaceId // ignore: cast_nullable_to_non_nullable
              as String,
      currentFileId: freezed == currentFileId
          ? _value.currentFileId
          : currentFileId // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      lastEditTime: null == lastEditTime
          ? _value.lastEditTime
          : lastEditTime // ignore: cast_nullable_to_non_nullable
              as int,
      filterValue: freezed == filterValue
          ? _value._filterValue
          : filterValue // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      sortValue: freezed == sortValue
          ? _value._sortValue
          : sortValue // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkspaceImpl extends _Workspace {
  const _$WorkspaceImpl(
      {required final List<WorkspaceFile> files,
      required this.workspaceName,
      required this.workspaceId,
      this.currentFileId,
      required this.createTime,
      required this.lastEditTime,
      final Map<String, dynamic>? filterValue = const {
        'filterType': 64,
        'compareType': 0,
        'starValue': 0,
        'buttonType': 0
      },
      final Map<String, dynamic>? sortValue = const {
        'sortType': 1,
        'sortMode': 0,
        'buttonType': 0
      }})
      : _files = files,
        _filterValue = filterValue,
        _sortValue = sortValue,
        super._();

  factory _$WorkspaceImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkspaceImplFromJson(json);

  final List<WorkspaceFile> _files;
  @override
  List<WorkspaceFile> get files {
    if (_files is EqualUnmodifiableListView) return _files;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_files);
  }

  @override
  final String workspaceName;
  @override
  final String workspaceId;
  @override
  final String? currentFileId;
  @override
  final int createTime;
  @override
  final int lastEditTime;
  final Map<String, dynamic>? _filterValue;
  @override
  @JsonKey()
  Map<String, dynamic>? get filterValue {
    final value = _filterValue;
    if (value == null) return null;
    if (_filterValue is EqualUnmodifiableMapView) return _filterValue;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, dynamic>? _sortValue;
  @override
  @JsonKey()
  Map<String, dynamic>? get sortValue {
    final value = _sortValue;
    if (value == null) return null;
    if (_sortValue is EqualUnmodifiableMapView) return _sortValue;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'Workspace(files: $files, workspaceName: $workspaceName, workspaceId: $workspaceId, currentFileId: $currentFileId, createTime: $createTime, lastEditTime: $lastEditTime, filterValue: $filterValue, sortValue: $sortValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkspaceImpl &&
            const DeepCollectionEquality().equals(other._files, _files) &&
            (identical(other.workspaceName, workspaceName) ||
                other.workspaceName == workspaceName) &&
            (identical(other.workspaceId, workspaceId) ||
                other.workspaceId == workspaceId) &&
            (identical(other.currentFileId, currentFileId) ||
                other.currentFileId == currentFileId) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.lastEditTime, lastEditTime) ||
                other.lastEditTime == lastEditTime) &&
            const DeepCollectionEquality()
                .equals(other._filterValue, _filterValue) &&
            const DeepCollectionEquality()
                .equals(other._sortValue, _sortValue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_files),
      workspaceName,
      workspaceId,
      currentFileId,
      createTime,
      lastEditTime,
      const DeepCollectionEquality().hash(_filterValue),
      const DeepCollectionEquality().hash(_sortValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkspaceImplCopyWith<_$WorkspaceImpl> get copyWith =>
      __$$WorkspaceImplCopyWithImpl<_$WorkspaceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkspaceImplToJson(
      this,
    );
  }
}

abstract class _Workspace extends Workspace {
  const factory _Workspace(
      {required final List<WorkspaceFile> files,
      required final String workspaceName,
      required final String workspaceId,
      final String? currentFileId,
      required final int createTime,
      required final int lastEditTime,
      final Map<String, dynamic>? filterValue,
      final Map<String, dynamic>? sortValue}) = _$WorkspaceImpl;
  const _Workspace._() : super._();

  factory _Workspace.fromJson(Map<String, dynamic> json) =
      _$WorkspaceImpl.fromJson;

  @override
  List<WorkspaceFile> get files;
  @override
  String get workspaceName;
  @override
  String get workspaceId;
  @override
  String? get currentFileId;
  @override
  int get createTime;
  @override
  int get lastEditTime;
  @override
  Map<String, dynamic>? get filterValue;
  @override
  Map<String, dynamic>? get sortValue;
  @override
  @JsonKey(ignore: true)
  _$$WorkspaceImplCopyWith<_$WorkspaceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
