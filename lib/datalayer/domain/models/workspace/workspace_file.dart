import 'package:drift/drift.dart' hide Json<PERSON>ey;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/utils/file_manager.dart';

part 'workspace_file.freezed.dart';
part 'workspace_file.g.dart';

// 工作区文件，一个图片对应一个文件
// 示例：
/* 
    {
      "fileId" : "f94e264c90b0471a97114f481b6aad1b",
      "edited" : true,
      "stars"  : 0,
      "exported" : true,
      "orgPath"  : "/Users/<USER>/Desktop/IDPhoto/org_003.png",  
      "exportTime"  : 0,
      "exportPath"  : "",
      "format"      : ".png",
      "broken"      : false, 
      "lastEditTime" : 0,
      "captureTime"  : 0,
      "size"         : 737291,
      "width"        : 826,
      "height"       : 1156
    }
*/
@freezed
class WorkspaceFile with _$WorkspaceFile {
  const factory WorkspaceFile({
    // 文件ID
    required String fileId,
    // 所在工作区ID
    required String workspaceId,
    // 是否编辑
    required bool edited,
    // 星级
    required int stars,
    // 是否导出
    required bool exported,
    // 原始路径
    required String orgPath,
    // 导出时间
    required int exportTime,
    // 格式
    required String format,
    // 是否损坏
    required bool broken,
    // 最后编辑时间
    required int lastEditTime,
    // 创建时间
    required int createTime,
    // 大小
    required int size,
    // 宽度
    required int width,
    // 高度
    required int height,
    // 角度
    required ExifOrientation orientation,
    // ICC 类型
    required ExifIccProfileType iccType,
    // isRaw
    required bool isRaw,
    // rawPath Path
    required String rawPath,
    // 转换状态
    required bool converted,
    // 文件名
    required String fileName,
    // 是否已图标化
    @Default(false) bool iconized,
    // 是否已中等图标化
    @Default(false) bool midIconized,
    // 是否已删除
    @Default(false) bool isDeleted,
    // 是否超大
    @Default(false) bool isOverSize,
    // 人脸数量
    @Default(-1) int faceCount,
    // 二进制格式
    @Default("") String binFormat,
    // RAW自动曝光
    // ignore: invalid_annotation_target
    @Default(false) @JsonKey(name: "raw_autoExpose") bool rawAutoExpose,
    // RAW自动调整类型
    // ignore: invalid_annotation_target
    @Default(0) @JsonKey(name: "raw_autoAdjustType") int rawAutoAdjustType,
    // 拍摄时间
    @Default(0) int captureTime,
  }) = _WorkspaceFile;
  const WorkspaceFile._();

  factory WorkspaceFile.fromJson(Map<String, dynamic> json) =>
      _$WorkspaceFileFromJson(json);

  // 从数据库实体转换为 WorkspaceFile
  factory WorkspaceFile.fromEntity(WorkspaceFileEntityData entity) {
    // 获取完整路径
    final orgPath = FileManager().getFullPath(entity.orgPath);
    return _WorkspaceFile(
      fileId: entity.fileId,
      workspaceId: entity.workspaceId,
      edited: entity.edited,
      stars: entity.stars,
      exported: entity.exported,
      orgPath: orgPath,
      exportTime: entity.exportTime,
      format: entity.format,
      broken: entity.broken,
      lastEditTime: entity.lastEditTime,
      createTime: entity.createTime,
      size: entity.size,
      width: entity.width,
      height: entity.height,
      orientation: ExifOrientation.fromRawValue(entity.orientation),
      iccType: ExifIccProfileType.fromRawValue(entity.iccType),
      isRaw: entity.isRaw,
      rawPath: entity.rawPath,
      converted: entity.converted,
      fileName: entity.fileName ?? "",
      iconized: entity.iconized ?? false,
      midIconized: entity.midIconized ?? false,
      isDeleted: entity.isDeleted ?? false,
      isOverSize: entity.isOverSize ?? false,
      faceCount: entity.faceCount ?? -1,
      binFormat: entity.binFormat ?? "",
      rawAutoExpose: entity.rawAutoExpose ?? false,
      rawAutoAdjustType: entity.rawAutoAdjustType ?? 0,
      captureTime: entity.captureTime ?? 0,
    );
  }

  // 转换为数据库实体
  WorkspaceFileEntityCompanion toEntity() {
    final relativePath = FileManager().getRelativePath(orgPath);
    return WorkspaceFileEntityCompanion(
      fileId: Value(fileId),
      edited: Value(edited),
      stars: Value(stars),
      exported: Value(exported),
      orgPath: Value(relativePath),
      exportTime: Value(exportTime),
      format: Value(format),
      broken: Value(broken),
      lastEditTime: Value(lastEditTime),
      createTime: Value(createTime),
      size: Value(size),
      width: Value(width),
      height: Value(height),
      workspaceId: Value(workspaceId),
      orientation: Value(orientation.raw),
      iccType: Value(iccType.raw),
      isRaw: Value(isRaw),
      rawPath: Value(rawPath),
      converted: Value(converted),
      fileName: Value(fileName),
      iconized: Value(iconized),
      midIconized: Value(midIconized),
      isDeleted: Value(isDeleted),
      isOverSize: Value(isOverSize),
      faceCount: Value(faceCount),
      binFormat: Value(binFormat),
      rawAutoExpose: Value(rawAutoExpose),
      rawAutoAdjustType: Value(rawAutoAdjustType),
      captureTime: Value(captureTime),
    );
  }
}

enum ExifOrientation {
  @JsonValue(0)
  unknown, // 未知方向或默认
  @JsonValue(1)
  normal, // 无旋转
  @JsonValue(2)
  flipHorizontal, // 水平翻转
  @JsonValue(3)
  rotate180, // 旋转 180 度
  @JsonValue(4)
  flipVertical, // 垂直翻转
  @JsonValue(5)
  transpose, // 顺时针旋转 90 度并垂直翻转
  @JsonValue(6)
  rotate90, // 顺时针旋转 90 度
  @JsonValue(7)
  transverse, // 逆时针旋转 90 度并垂直翻转
  @JsonValue(8)
  rotate270; // 逆时针旋转 90 度

  factory ExifOrientation.fromRawValue(int rawValue) {
    if (rawValue < 0 || rawValue > 8) {
      return ExifOrientation.unknown;
    }
    return values.firstWhere(
      (e) => e.raw == rawValue,
      orElse: () => ExifOrientation.unknown,
    );
  }

  int get raw => index;
}

enum ExifIccProfileType {
  @JsonValue(0)
  unknown,
  @JsonValue(1)
  sRGB,
  @JsonValue(2)
  adobe,
  @JsonValue(3)
  displayP3;

  factory ExifIccProfileType.fromRawValue(int rawValue) {
    return values.firstWhere(
      (e) => e.index == rawValue,
      orElse: () => ExifIccProfileType.unknown,
    );
  }

  int get raw => index;
}
