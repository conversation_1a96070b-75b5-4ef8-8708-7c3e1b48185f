// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workspace_file.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkspaceFile _$WorkspaceFileFromJson(Map<String, dynamic> json) {
  return _WorkspaceFile.fromJson(json);
}

/// @nodoc
mixin _$WorkspaceFile {
// 文件ID
  String get fileId => throw _privateConstructorUsedError; // 所在工作区ID
  String get workspaceId => throw _privateConstructorUsedError; // 是否编辑
  bool get edited => throw _privateConstructorUsedError; // 星级
  int get stars => throw _privateConstructorUsedError; // 是否导出
  bool get exported => throw _privateConstructorUsedError; // 原始路径
  String get orgPath => throw _privateConstructorUsedError; // 导出时间
  int get exportTime => throw _privateConstructorUsedError; // 格式
  String get format => throw _privateConstructorUsedError; // 是否损坏
  bool get broken => throw _privateConstructorUsedError; // 最后编辑时间
  int get lastEditTime => throw _privateConstructorUsedError; // 创建时间
  int get createTime => throw _privateConstructorUsedError; // 大小
  int get size => throw _privateConstructorUsedError; // 宽度
  int get width => throw _privateConstructorUsedError; // 高度
  int get height => throw _privateConstructorUsedError; // 角度
  ExifOrientation get orientation =>
      throw _privateConstructorUsedError; // ICC 类型
  ExifIccProfileType get iccType => throw _privateConstructorUsedError; // isRaw
  bool get isRaw => throw _privateConstructorUsedError; // rawPath Path
  String get rawPath => throw _privateConstructorUsedError; // 转换状态
  bool get converted => throw _privateConstructorUsedError; // 文件名
  String get fileName => throw _privateConstructorUsedError; // 是否已图标化
  bool get iconized => throw _privateConstructorUsedError; // 是否已中等图标化
  bool get midIconized => throw _privateConstructorUsedError; // 是否已删除
  bool get isDeleted => throw _privateConstructorUsedError; // 是否超大
  bool get isOverSize => throw _privateConstructorUsedError; // 人脸数量
  int get faceCount => throw _privateConstructorUsedError; // 二进制格式
  String get binFormat => throw _privateConstructorUsedError; // RAW自动曝光
// ignore: invalid_annotation_target
  @JsonKey(name: "raw_autoExpose")
  bool get rawAutoExpose => throw _privateConstructorUsedError; // RAW自动调整类型
// ignore: invalid_annotation_target
  @JsonKey(name: "raw_autoAdjustType")
  int get rawAutoAdjustType => throw _privateConstructorUsedError; // 拍摄时间
  int get captureTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkspaceFileCopyWith<WorkspaceFile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkspaceFileCopyWith<$Res> {
  factory $WorkspaceFileCopyWith(
          WorkspaceFile value, $Res Function(WorkspaceFile) then) =
      _$WorkspaceFileCopyWithImpl<$Res, WorkspaceFile>;
  @useResult
  $Res call(
      {String fileId,
      String workspaceId,
      bool edited,
      int stars,
      bool exported,
      String orgPath,
      int exportTime,
      String format,
      bool broken,
      int lastEditTime,
      int createTime,
      int size,
      int width,
      int height,
      ExifOrientation orientation,
      ExifIccProfileType iccType,
      bool isRaw,
      String rawPath,
      bool converted,
      String fileName,
      bool iconized,
      bool midIconized,
      bool isDeleted,
      bool isOverSize,
      int faceCount,
      String binFormat,
      @JsonKey(name: "raw_autoExpose") bool rawAutoExpose,
      @JsonKey(name: "raw_autoAdjustType") int rawAutoAdjustType,
      int captureTime});
}

/// @nodoc
class _$WorkspaceFileCopyWithImpl<$Res, $Val extends WorkspaceFile>
    implements $WorkspaceFileCopyWith<$Res> {
  _$WorkspaceFileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
    Object? workspaceId = null,
    Object? edited = null,
    Object? stars = null,
    Object? exported = null,
    Object? orgPath = null,
    Object? exportTime = null,
    Object? format = null,
    Object? broken = null,
    Object? lastEditTime = null,
    Object? createTime = null,
    Object? size = null,
    Object? width = null,
    Object? height = null,
    Object? orientation = null,
    Object? iccType = null,
    Object? isRaw = null,
    Object? rawPath = null,
    Object? converted = null,
    Object? fileName = null,
    Object? iconized = null,
    Object? midIconized = null,
    Object? isDeleted = null,
    Object? isOverSize = null,
    Object? faceCount = null,
    Object? binFormat = null,
    Object? rawAutoExpose = null,
    Object? rawAutoAdjustType = null,
    Object? captureTime = null,
  }) {
    return _then(_value.copyWith(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
      workspaceId: null == workspaceId
          ? _value.workspaceId
          : workspaceId // ignore: cast_nullable_to_non_nullable
              as String,
      edited: null == edited
          ? _value.edited
          : edited // ignore: cast_nullable_to_non_nullable
              as bool,
      stars: null == stars
          ? _value.stars
          : stars // ignore: cast_nullable_to_non_nullable
              as int,
      exported: null == exported
          ? _value.exported
          : exported // ignore: cast_nullable_to_non_nullable
              as bool,
      orgPath: null == orgPath
          ? _value.orgPath
          : orgPath // ignore: cast_nullable_to_non_nullable
              as String,
      exportTime: null == exportTime
          ? _value.exportTime
          : exportTime // ignore: cast_nullable_to_non_nullable
              as int,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      broken: null == broken
          ? _value.broken
          : broken // ignore: cast_nullable_to_non_nullable
              as bool,
      lastEditTime: null == lastEditTime
          ? _value.lastEditTime
          : lastEditTime // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      orientation: null == orientation
          ? _value.orientation
          : orientation // ignore: cast_nullable_to_non_nullable
              as ExifOrientation,
      iccType: null == iccType
          ? _value.iccType
          : iccType // ignore: cast_nullable_to_non_nullable
              as ExifIccProfileType,
      isRaw: null == isRaw
          ? _value.isRaw
          : isRaw // ignore: cast_nullable_to_non_nullable
              as bool,
      rawPath: null == rawPath
          ? _value.rawPath
          : rawPath // ignore: cast_nullable_to_non_nullable
              as String,
      converted: null == converted
          ? _value.converted
          : converted // ignore: cast_nullable_to_non_nullable
              as bool,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      iconized: null == iconized
          ? _value.iconized
          : iconized // ignore: cast_nullable_to_non_nullable
              as bool,
      midIconized: null == midIconized
          ? _value.midIconized
          : midIconized // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSize: null == isOverSize
          ? _value.isOverSize
          : isOverSize // ignore: cast_nullable_to_non_nullable
              as bool,
      faceCount: null == faceCount
          ? _value.faceCount
          : faceCount // ignore: cast_nullable_to_non_nullable
              as int,
      binFormat: null == binFormat
          ? _value.binFormat
          : binFormat // ignore: cast_nullable_to_non_nullable
              as String,
      rawAutoExpose: null == rawAutoExpose
          ? _value.rawAutoExpose
          : rawAutoExpose // ignore: cast_nullable_to_non_nullable
              as bool,
      rawAutoAdjustType: null == rawAutoAdjustType
          ? _value.rawAutoAdjustType
          : rawAutoAdjustType // ignore: cast_nullable_to_non_nullable
              as int,
      captureTime: null == captureTime
          ? _value.captureTime
          : captureTime // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkspaceFileImplCopyWith<$Res>
    implements $WorkspaceFileCopyWith<$Res> {
  factory _$$WorkspaceFileImplCopyWith(
          _$WorkspaceFileImpl value, $Res Function(_$WorkspaceFileImpl) then) =
      __$$WorkspaceFileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String fileId,
      String workspaceId,
      bool edited,
      int stars,
      bool exported,
      String orgPath,
      int exportTime,
      String format,
      bool broken,
      int lastEditTime,
      int createTime,
      int size,
      int width,
      int height,
      ExifOrientation orientation,
      ExifIccProfileType iccType,
      bool isRaw,
      String rawPath,
      bool converted,
      String fileName,
      bool iconized,
      bool midIconized,
      bool isDeleted,
      bool isOverSize,
      int faceCount,
      String binFormat,
      @JsonKey(name: "raw_autoExpose") bool rawAutoExpose,
      @JsonKey(name: "raw_autoAdjustType") int rawAutoAdjustType,
      int captureTime});
}

/// @nodoc
class __$$WorkspaceFileImplCopyWithImpl<$Res>
    extends _$WorkspaceFileCopyWithImpl<$Res, _$WorkspaceFileImpl>
    implements _$$WorkspaceFileImplCopyWith<$Res> {
  __$$WorkspaceFileImplCopyWithImpl(
      _$WorkspaceFileImpl _value, $Res Function(_$WorkspaceFileImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
    Object? workspaceId = null,
    Object? edited = null,
    Object? stars = null,
    Object? exported = null,
    Object? orgPath = null,
    Object? exportTime = null,
    Object? format = null,
    Object? broken = null,
    Object? lastEditTime = null,
    Object? createTime = null,
    Object? size = null,
    Object? width = null,
    Object? height = null,
    Object? orientation = null,
    Object? iccType = null,
    Object? isRaw = null,
    Object? rawPath = null,
    Object? converted = null,
    Object? fileName = null,
    Object? iconized = null,
    Object? midIconized = null,
    Object? isDeleted = null,
    Object? isOverSize = null,
    Object? faceCount = null,
    Object? binFormat = null,
    Object? rawAutoExpose = null,
    Object? rawAutoAdjustType = null,
    Object? captureTime = null,
  }) {
    return _then(_$WorkspaceFileImpl(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
      workspaceId: null == workspaceId
          ? _value.workspaceId
          : workspaceId // ignore: cast_nullable_to_non_nullable
              as String,
      edited: null == edited
          ? _value.edited
          : edited // ignore: cast_nullable_to_non_nullable
              as bool,
      stars: null == stars
          ? _value.stars
          : stars // ignore: cast_nullable_to_non_nullable
              as int,
      exported: null == exported
          ? _value.exported
          : exported // ignore: cast_nullable_to_non_nullable
              as bool,
      orgPath: null == orgPath
          ? _value.orgPath
          : orgPath // ignore: cast_nullable_to_non_nullable
              as String,
      exportTime: null == exportTime
          ? _value.exportTime
          : exportTime // ignore: cast_nullable_to_non_nullable
              as int,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      broken: null == broken
          ? _value.broken
          : broken // ignore: cast_nullable_to_non_nullable
              as bool,
      lastEditTime: null == lastEditTime
          ? _value.lastEditTime
          : lastEditTime // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      orientation: null == orientation
          ? _value.orientation
          : orientation // ignore: cast_nullable_to_non_nullable
              as ExifOrientation,
      iccType: null == iccType
          ? _value.iccType
          : iccType // ignore: cast_nullable_to_non_nullable
              as ExifIccProfileType,
      isRaw: null == isRaw
          ? _value.isRaw
          : isRaw // ignore: cast_nullable_to_non_nullable
              as bool,
      rawPath: null == rawPath
          ? _value.rawPath
          : rawPath // ignore: cast_nullable_to_non_nullable
              as String,
      converted: null == converted
          ? _value.converted
          : converted // ignore: cast_nullable_to_non_nullable
              as bool,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      iconized: null == iconized
          ? _value.iconized
          : iconized // ignore: cast_nullable_to_non_nullable
              as bool,
      midIconized: null == midIconized
          ? _value.midIconized
          : midIconized // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverSize: null == isOverSize
          ? _value.isOverSize
          : isOverSize // ignore: cast_nullable_to_non_nullable
              as bool,
      faceCount: null == faceCount
          ? _value.faceCount
          : faceCount // ignore: cast_nullable_to_non_nullable
              as int,
      binFormat: null == binFormat
          ? _value.binFormat
          : binFormat // ignore: cast_nullable_to_non_nullable
              as String,
      rawAutoExpose: null == rawAutoExpose
          ? _value.rawAutoExpose
          : rawAutoExpose // ignore: cast_nullable_to_non_nullable
              as bool,
      rawAutoAdjustType: null == rawAutoAdjustType
          ? _value.rawAutoAdjustType
          : rawAutoAdjustType // ignore: cast_nullable_to_non_nullable
              as int,
      captureTime: null == captureTime
          ? _value.captureTime
          : captureTime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkspaceFileImpl extends _WorkspaceFile {
  const _$WorkspaceFileImpl(
      {required this.fileId,
      required this.workspaceId,
      required this.edited,
      required this.stars,
      required this.exported,
      required this.orgPath,
      required this.exportTime,
      required this.format,
      required this.broken,
      required this.lastEditTime,
      required this.createTime,
      required this.size,
      required this.width,
      required this.height,
      required this.orientation,
      required this.iccType,
      required this.isRaw,
      required this.rawPath,
      required this.converted,
      required this.fileName,
      this.iconized = false,
      this.midIconized = false,
      this.isDeleted = false,
      this.isOverSize = false,
      this.faceCount = -1,
      this.binFormat = "",
      @JsonKey(name: "raw_autoExpose") this.rawAutoExpose = false,
      @JsonKey(name: "raw_autoAdjustType") this.rawAutoAdjustType = 0,
      this.captureTime = 0})
      : super._();

  factory _$WorkspaceFileImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkspaceFileImplFromJson(json);

// 文件ID
  @override
  final String fileId;
// 所在工作区ID
  @override
  final String workspaceId;
// 是否编辑
  @override
  final bool edited;
// 星级
  @override
  final int stars;
// 是否导出
  @override
  final bool exported;
// 原始路径
  @override
  final String orgPath;
// 导出时间
  @override
  final int exportTime;
// 格式
  @override
  final String format;
// 是否损坏
  @override
  final bool broken;
// 最后编辑时间
  @override
  final int lastEditTime;
// 创建时间
  @override
  final int createTime;
// 大小
  @override
  final int size;
// 宽度
  @override
  final int width;
// 高度
  @override
  final int height;
// 角度
  @override
  final ExifOrientation orientation;
// ICC 类型
  @override
  final ExifIccProfileType iccType;
// isRaw
  @override
  final bool isRaw;
// rawPath Path
  @override
  final String rawPath;
// 转换状态
  @override
  final bool converted;
// 文件名
  @override
  final String fileName;
// 是否已图标化
  @override
  @JsonKey()
  final bool iconized;
// 是否已中等图标化
  @override
  @JsonKey()
  final bool midIconized;
// 是否已删除
  @override
  @JsonKey()
  final bool isDeleted;
// 是否超大
  @override
  @JsonKey()
  final bool isOverSize;
// 人脸数量
  @override
  @JsonKey()
  final int faceCount;
// 二进制格式
  @override
  @JsonKey()
  final String binFormat;
// RAW自动曝光
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "raw_autoExpose")
  final bool rawAutoExpose;
// RAW自动调整类型
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "raw_autoAdjustType")
  final int rawAutoAdjustType;
// 拍摄时间
  @override
  @JsonKey()
  final int captureTime;

  @override
  String toString() {
    return 'WorkspaceFile(fileId: $fileId, workspaceId: $workspaceId, edited: $edited, stars: $stars, exported: $exported, orgPath: $orgPath, exportTime: $exportTime, format: $format, broken: $broken, lastEditTime: $lastEditTime, createTime: $createTime, size: $size, width: $width, height: $height, orientation: $orientation, iccType: $iccType, isRaw: $isRaw, rawPath: $rawPath, converted: $converted, fileName: $fileName, iconized: $iconized, midIconized: $midIconized, isDeleted: $isDeleted, isOverSize: $isOverSize, faceCount: $faceCount, binFormat: $binFormat, rawAutoExpose: $rawAutoExpose, rawAutoAdjustType: $rawAutoAdjustType, captureTime: $captureTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkspaceFileImpl &&
            (identical(other.fileId, fileId) || other.fileId == fileId) &&
            (identical(other.workspaceId, workspaceId) ||
                other.workspaceId == workspaceId) &&
            (identical(other.edited, edited) || other.edited == edited) &&
            (identical(other.stars, stars) || other.stars == stars) &&
            (identical(other.exported, exported) ||
                other.exported == exported) &&
            (identical(other.orgPath, orgPath) || other.orgPath == orgPath) &&
            (identical(other.exportTime, exportTime) ||
                other.exportTime == exportTime) &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.broken, broken) || other.broken == broken) &&
            (identical(other.lastEditTime, lastEditTime) ||
                other.lastEditTime == lastEditTime) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.orientation, orientation) ||
                other.orientation == orientation) &&
            (identical(other.iccType, iccType) || other.iccType == iccType) &&
            (identical(other.isRaw, isRaw) || other.isRaw == isRaw) &&
            (identical(other.rawPath, rawPath) || other.rawPath == rawPath) &&
            (identical(other.converted, converted) ||
                other.converted == converted) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.iconized, iconized) ||
                other.iconized == iconized) &&
            (identical(other.midIconized, midIconized) ||
                other.midIconized == midIconized) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.isOverSize, isOverSize) ||
                other.isOverSize == isOverSize) &&
            (identical(other.faceCount, faceCount) ||
                other.faceCount == faceCount) &&
            (identical(other.binFormat, binFormat) ||
                other.binFormat == binFormat) &&
            (identical(other.rawAutoExpose, rawAutoExpose) ||
                other.rawAutoExpose == rawAutoExpose) &&
            (identical(other.rawAutoAdjustType, rawAutoAdjustType) ||
                other.rawAutoAdjustType == rawAutoAdjustType) &&
            (identical(other.captureTime, captureTime) ||
                other.captureTime == captureTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        fileId,
        workspaceId,
        edited,
        stars,
        exported,
        orgPath,
        exportTime,
        format,
        broken,
        lastEditTime,
        createTime,
        size,
        width,
        height,
        orientation,
        iccType,
        isRaw,
        rawPath,
        converted,
        fileName,
        iconized,
        midIconized,
        isDeleted,
        isOverSize,
        faceCount,
        binFormat,
        rawAutoExpose,
        rawAutoAdjustType,
        captureTime
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkspaceFileImplCopyWith<_$WorkspaceFileImpl> get copyWith =>
      __$$WorkspaceFileImplCopyWithImpl<_$WorkspaceFileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkspaceFileImplToJson(
      this,
    );
  }
}

abstract class _WorkspaceFile extends WorkspaceFile {
  const factory _WorkspaceFile(
      {required final String fileId,
      required final String workspaceId,
      required final bool edited,
      required final int stars,
      required final bool exported,
      required final String orgPath,
      required final int exportTime,
      required final String format,
      required final bool broken,
      required final int lastEditTime,
      required final int createTime,
      required final int size,
      required final int width,
      required final int height,
      required final ExifOrientation orientation,
      required final ExifIccProfileType iccType,
      required final bool isRaw,
      required final String rawPath,
      required final bool converted,
      required final String fileName,
      final bool iconized,
      final bool midIconized,
      final bool isDeleted,
      final bool isOverSize,
      final int faceCount,
      final String binFormat,
      @JsonKey(name: "raw_autoExpose") final bool rawAutoExpose,
      @JsonKey(name: "raw_autoAdjustType") final int rawAutoAdjustType,
      final int captureTime}) = _$WorkspaceFileImpl;
  const _WorkspaceFile._() : super._();

  factory _WorkspaceFile.fromJson(Map<String, dynamic> json) =
      _$WorkspaceFileImpl.fromJson;

  @override // 文件ID
  String get fileId;
  @override // 所在工作区ID
  String get workspaceId;
  @override // 是否编辑
  bool get edited;
  @override // 星级
  int get stars;
  @override // 是否导出
  bool get exported;
  @override // 原始路径
  String get orgPath;
  @override // 导出时间
  int get exportTime;
  @override // 格式
  String get format;
  @override // 是否损坏
  bool get broken;
  @override // 最后编辑时间
  int get lastEditTime;
  @override // 创建时间
  int get createTime;
  @override // 大小
  int get size;
  @override // 宽度
  int get width;
  @override // 高度
  int get height;
  @override // 角度
  ExifOrientation get orientation;
  @override // ICC 类型
  ExifIccProfileType get iccType;
  @override // isRaw
  bool get isRaw;
  @override // rawPath Path
  String get rawPath;
  @override // 转换状态
  bool get converted;
  @override // 文件名
  String get fileName;
  @override // 是否已图标化
  bool get iconized;
  @override // 是否已中等图标化
  bool get midIconized;
  @override // 是否已删除
  bool get isDeleted;
  @override // 是否超大
  bool get isOverSize;
  @override // 人脸数量
  int get faceCount;
  @override // 二进制格式
  String get binFormat;
  @override // RAW自动曝光
// ignore: invalid_annotation_target
  @JsonKey(name: "raw_autoExpose")
  bool get rawAutoExpose;
  @override // RAW自动调整类型
// ignore: invalid_annotation_target
  @JsonKey(name: "raw_autoAdjustType")
  int get rawAutoAdjustType;
  @override // 拍摄时间
  int get captureTime;
  @override
  @JsonKey(ignore: true)
  _$$WorkspaceFileImplCopyWith<_$WorkspaceFileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
