// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workspace_file.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorkspaceFileImpl _$$WorkspaceFileImplFromJson(Map<String, dynamic> json) =>
    _$WorkspaceFileImpl(
      fileId: json['fileId'] as String,
      workspaceId: json['workspaceId'] as String,
      edited: json['edited'] as bool,
      stars: (json['stars'] as num).toInt(),
      exported: json['exported'] as bool,
      orgPath: json['orgPath'] as String,
      exportTime: (json['exportTime'] as num).toInt(),
      format: json['format'] as String,
      broken: json['broken'] as bool,
      lastEditTime: (json['lastEditTime'] as num).toInt(),
      createTime: (json['createTime'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
      orientation: $enumDecode(_$ExifOrientationEnumMap, json['orientation']),
      iccType: $enumDecode(_$ExifIccProfileTypeEnumMap, json['iccType']),
      isRaw: json['isRaw'] as bool,
      rawPath: json['rawPath'] as String,
      converted: json['converted'] as bool,
      fileName: json['fileName'] as String,
      iconized: json['iconized'] as bool? ?? false,
      midIconized: json['midIconized'] as bool? ?? false,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isOverSize: json['isOverSize'] as bool? ?? false,
      faceCount: (json['faceCount'] as num?)?.toInt() ?? -1,
      binFormat: json['binFormat'] as String? ?? "",
      rawAutoExpose: json['raw_autoExpose'] as bool? ?? false,
      rawAutoAdjustType: (json['raw_autoAdjustType'] as num?)?.toInt() ?? 0,
      captureTime: (json['captureTime'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$WorkspaceFileImplToJson(_$WorkspaceFileImpl instance) =>
    <String, dynamic>{
      'fileId': instance.fileId,
      'workspaceId': instance.workspaceId,
      'edited': instance.edited,
      'stars': instance.stars,
      'exported': instance.exported,
      'orgPath': instance.orgPath,
      'exportTime': instance.exportTime,
      'format': instance.format,
      'broken': instance.broken,
      'lastEditTime': instance.lastEditTime,
      'createTime': instance.createTime,
      'size': instance.size,
      'width': instance.width,
      'height': instance.height,
      'orientation': _$ExifOrientationEnumMap[instance.orientation]!,
      'iccType': _$ExifIccProfileTypeEnumMap[instance.iccType]!,
      'isRaw': instance.isRaw,
      'rawPath': instance.rawPath,
      'converted': instance.converted,
      'fileName': instance.fileName,
      'iconized': instance.iconized,
      'midIconized': instance.midIconized,
      'isDeleted': instance.isDeleted,
      'isOverSize': instance.isOverSize,
      'faceCount': instance.faceCount,
      'binFormat': instance.binFormat,
      'raw_autoExpose': instance.rawAutoExpose,
      'raw_autoAdjustType': instance.rawAutoAdjustType,
      'captureTime': instance.captureTime,
    };

const _$ExifOrientationEnumMap = {
  ExifOrientation.unknown: 0,
  ExifOrientation.normal: 1,
  ExifOrientation.flipHorizontal: 2,
  ExifOrientation.rotate180: 3,
  ExifOrientation.flipVertical: 4,
  ExifOrientation.transpose: 5,
  ExifOrientation.rotate90: 6,
  ExifOrientation.transverse: 7,
  ExifOrientation.rotate270: 8,
};

const _$ExifIccProfileTypeEnumMap = {
  ExifIccProfileType.unknown: 0,
  ExifIccProfileType.sRGB: 1,
  ExifIccProfileType.adobe: 2,
  ExifIccProfileType.displayP3: 3,
};
