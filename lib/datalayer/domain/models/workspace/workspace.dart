import 'dart:convert';

import 'package:drift/drift.dart' hide <PERSON><PERSON><PERSON><PERSON>;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'workspace_file.dart';

part 'workspace.freezed.dart';
part 'workspace.g.dart';

// 工作区，一个工程对应一个工作区
// 示例：
/*
 {
    "files" : [
        {
            "fileId" : "3d38275c207d4d0389cb480879ab6a15",
            "edited" : false,
            "stars"  : 0,
            "exported" : false,
            "orgPath"  : "/Users/<USER>/Desktop/IDPhoto/cb88.jpg",   
            "exportTime"  : 0,
            "exportPath"  : "/Users/<USER>/Desktop/IDPhoto/cb88_1.jpg",
            "format"      : ".jpg",
            "broken"      : false, 
            "lastEditTime" : 0,
            "createTime"  : 0,
            "size"         : 5507835,
            "width"        : 4388,
            "height"       : 6582
        },
        {
            "fileId" : "500d9203e2ec422ea1852214fbe9bf6d",
            "edited" : true,
            "stars"  : 0,
            "exported" : false,
            "orgPath"  : "/Users/<USER>/Desktop/IDPhoto/org_002_\u526F\u672C.jpg",  
            "exportTime"  : 0,
            "exportPath"  : "",
            "format"      : ".jpg",
            "broken"      : false, 
            "lastEditTime" : 0,
            "captureTime"  : 0,
            "size"         : 431081,
            "width"        : 1125,
            "height"       : 1500
        }
    ], 
    "workspaceName" : "旅拍1号", 
    "workspaceId"   : "c21f969b5f03d33d43e04f8f136e7682", 
    "currentFileId" : "500d9203e2ec422ea1852214fbe9bf6d",
    "lastEditTime" : 0,
    "createTime"  : 0
}
*/

@freezed
class Workspace with _$Workspace {
  const factory Workspace({
    required List<WorkspaceFile> files,
    required String workspaceName,
    required String workspaceId,
    String? currentFileId,
    required int createTime,
    required int lastEditTime,
    @Default({
      'filterType': 64,
      'compareType': 0,
      'starValue': 0,
      'buttonType': 0,
    })
    Map<String, dynamic>? filterValue,
    @Default({
      'sortType': 1,
      'sortMode': 0,
      'buttonType': 0,
    })
    Map<String, dynamic>? sortValue,
  }) = _Workspace;
  const Workspace._();

  factory Workspace.fromJson(Map<String, dynamic> json) =>
      _$WorkspaceFromJson(json);

  // 尝试解析 FilterValue，如果失败则返回默认值
  static Map<String, dynamic> _parseFilterValue(String? jsonStr) {
    if (jsonStr == null || jsonStr.isEmpty) {
      return const {
        'filterType': 64,
        'compareType': 0,
        'starValue': 0,
        'buttonType': 0,
      };
    }

    try {
      final Map<String, dynamic> jsonMap = jsonDecode(jsonStr);

      return jsonMap;
    } catch (e, stackTrace) {
      PGLog.e('解析 FilterValue 失败: $e\n$stackTrace\nJSON: $jsonStr');
      return const {
        'filterType': 64,
        'compareType': 0,
        'starValue': 0,
        'buttonType': 0,
      };
    }
  }

  // 尝试解析 SortValue，如果失败则返回默认值
  static Map<String, dynamic> _parseSortValue(String? jsonStr) {
    if (jsonStr == null || jsonStr.isEmpty) {
      return const {
        'sortType': 1,
        'sortMode': 0,
        'buttonType': 0,
      };
    }

    try {
      final Map<String, dynamic> jsonMap = jsonDecode(jsonStr);
      return jsonMap;
    } catch (e, stackTrace) {
      PGLog.e('解析 SortValue 失败: $e\n$stackTrace\nJSON: $jsonStr');
      return const {
        'sortType': 1,
        'sortMode': 0,
        'buttonType': 0,
      };
    }
  }

  factory Workspace.fromEntity(
    WorkspaceEntityData entity,
    List<WorkspaceFile> files,
  ) {
    return Workspace(
      workspaceId: entity.workspaceId,
      workspaceName: entity.workspaceName,
      currentFileId: entity.currentFileId,
      createTime: entity.createTime,
      lastEditTime: entity.lastEditTime,
      filterValue: _parseFilterValue(entity.filterValue),
      sortValue: _parseSortValue(entity.sortValue),
      files: files,
    );
  }

  WorkspaceEntityCompanion toEntity() {
    String? encodeFilterValue;
    String? encodeSortValue;

    try {
      encodeFilterValue = jsonEncode(filterValue ??
          const {
            'filterType': 64,
            'compareType': 0,
            'starValue': 0,
            'buttonType': 0,
          });
    } catch (e, stackTrace) {
      PGLog.e('序列化 FilterValue 失败: $e\n$stackTrace');
      encodeFilterValue = jsonEncode(const {
        'filterType': 64,
        'compareType': 0,
        'starValue': 0,
        'buttonType': 0,
      });
    }

    try {
      encodeSortValue = jsonEncode(sortValue ??
          const {
            'sortType': 1,
            'sortMode': 0,
            'buttonType': 0,
          });
    } catch (e, stackTrace) {
      PGLog.e('序列化 SortValue 失败: $e\n$stackTrace');
      encodeSortValue = jsonEncode(const {
        'sortType': 1,
        'sortMode': 0,
        'buttonType': 0,
      });
    }

    return WorkspaceEntityCompanion(
      workspaceId: Value(workspaceId),
      workspaceName: Value(workspaceName),
      currentFileId: Value(currentFileId ?? ""),
      createTime: Value(createTime),
      lastEditTime: Value(lastEditTime),
      projectId: Value(workspaceId),
      filterValue: Value(encodeFilterValue),
      sortValue: Value(encodeSortValue),
    );
  }
}
