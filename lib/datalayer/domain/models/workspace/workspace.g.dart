// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workspace.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorkspaceImpl _$$WorkspaceImplFromJson(Map<String, dynamic> json) =>
    _$WorkspaceImpl(
      files: (json['files'] as List<dynamic>)
          .map((e) => WorkspaceFile.fromJson(e as Map<String, dynamic>))
          .toList(),
      workspaceName: json['workspaceName'] as String,
      workspaceId: json['workspaceId'] as String,
      currentFileId: json['currentFileId'] as String?,
      createTime: (json['createTime'] as num).toInt(),
      lastEditTime: (json['lastEditTime'] as num).toInt(),
      filterValue: json['filterValue'] as Map<String, dynamic>? ??
          const {
            'filterType': 64,
            'compareType': 0,
            'starValue': 0,
            'buttonType': 0
          },
      sortValue: json['sortValue'] as Map<String, dynamic>? ??
          const {'sortType': 1, 'sortMode': 0, 'buttonType': 0},
    );

Map<String, dynamic> _$$WorkspaceImplToJson(_$WorkspaceImpl instance) =>
    <String, dynamic>{
      'files': instance.files,
      'workspaceName': instance.workspaceName,
      'workspaceId': instance.workspaceId,
      'currentFileId': instance.currentFileId,
      'createTime': instance.createTime,
      'lastEditTime': instance.lastEditTime,
      'filterValue': instance.filterValue,
      'sortValue': instance.sortValue,
    };
