// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_tag_icon.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PurchaseTagIconImpl _$$PurchaseTagIconImplFromJson(
        Map<String, dynamic> json) =>
    _$PurchaseTagIconImpl(
      expire: (json['expire'] as num).toInt(),
      ext: json['ext'] as String,
      height: (json['height'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      url: json['url'] as String,
      width: (json['width'] as num).toInt(),
    );

Map<String, dynamic> _$$PurchaseTagIconImplToJson(
        _$PurchaseTagIconImpl instance) =>
    <String, dynamic>{
      'expire': instance.expire,
      'ext': instance.ext,
      'height': instance.height,
      'size': instance.size,
      'url': instance.url,
      'width': instance.width,
    };
