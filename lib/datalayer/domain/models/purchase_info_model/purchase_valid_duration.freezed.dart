// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_valid_duration.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PurchaseValidDuration _$PurchaseValidDurationFromJson(
    Map<String, dynamic> json) {
  return _PurchaseValidDuration.fromJson(json);
}

/// @nodoc
mixin _$PurchaseValidDuration {
  int get begin => throw _privateConstructorUsedError;
  int get end => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PurchaseValidDurationCopyWith<PurchaseValidDuration> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseValidDurationCopyWith<$Res> {
  factory $PurchaseValidDurationCopyWith(PurchaseValidDuration value,
          $Res Function(PurchaseValidDuration) then) =
      _$PurchaseValidDurationCopyWithImpl<$Res, PurchaseValidDuration>;
  @useResult
  $Res call({int begin, int end});
}

/// @nodoc
class _$PurchaseValidDurationCopyWithImpl<$Res,
        $Val extends PurchaseValidDuration>
    implements $PurchaseValidDurationCopyWith<$Res> {
  _$PurchaseValidDurationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? begin = null,
    Object? end = null,
  }) {
    return _then(_value.copyWith(
      begin: null == begin
          ? _value.begin
          : begin // ignore: cast_nullable_to_non_nullable
              as int,
      end: null == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseValidDurationImplCopyWith<$Res>
    implements $PurchaseValidDurationCopyWith<$Res> {
  factory _$$PurchaseValidDurationImplCopyWith(
          _$PurchaseValidDurationImpl value,
          $Res Function(_$PurchaseValidDurationImpl) then) =
      __$$PurchaseValidDurationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int begin, int end});
}

/// @nodoc
class __$$PurchaseValidDurationImplCopyWithImpl<$Res>
    extends _$PurchaseValidDurationCopyWithImpl<$Res,
        _$PurchaseValidDurationImpl>
    implements _$$PurchaseValidDurationImplCopyWith<$Res> {
  __$$PurchaseValidDurationImplCopyWithImpl(_$PurchaseValidDurationImpl _value,
      $Res Function(_$PurchaseValidDurationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? begin = null,
    Object? end = null,
  }) {
    return _then(_$PurchaseValidDurationImpl(
      begin: null == begin
          ? _value.begin
          : begin // ignore: cast_nullable_to_non_nullable
              as int,
      end: null == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PurchaseValidDurationImpl implements _PurchaseValidDuration {
  const _$PurchaseValidDurationImpl({required this.begin, required this.end});

  factory _$PurchaseValidDurationImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseValidDurationImplFromJson(json);

  @override
  final int begin;
  @override
  final int end;

  @override
  String toString() {
    return 'PurchaseValidDuration(begin: $begin, end: $end)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseValidDurationImpl &&
            (identical(other.begin, begin) || other.begin == begin) &&
            (identical(other.end, end) || other.end == end));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, begin, end);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseValidDurationImplCopyWith<_$PurchaseValidDurationImpl>
      get copyWith => __$$PurchaseValidDurationImplCopyWithImpl<
          _$PurchaseValidDurationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseValidDurationImplToJson(
      this,
    );
  }
}

abstract class _PurchaseValidDuration implements PurchaseValidDuration {
  const factory _PurchaseValidDuration(
      {required final int begin,
      required final int end}) = _$PurchaseValidDurationImpl;

  factory _PurchaseValidDuration.fromJson(Map<String, dynamic> json) =
      _$PurchaseValidDurationImpl.fromJson;

  @override
  int get begin;
  @override
  int get end;
  @override
  @JsonKey(ignore: true)
  _$$PurchaseValidDurationImplCopyWith<_$PurchaseValidDurationImpl>
      get copyWith => throw _privateConstructorUsedError;
}
