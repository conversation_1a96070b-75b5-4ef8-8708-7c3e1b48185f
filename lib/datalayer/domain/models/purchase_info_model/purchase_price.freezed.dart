// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_price.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PurchasePrice _$PurchasePriceFromJson(Map<String, dynamic> json) {
  return _PurchasePrice.fromJson(json);
}

/// @nodoc
mixin _$PurchasePrice {
  String get channel => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  String get defaultCurrency => throw _privateConstructorUsedError;
  String get defaultPrice => throw _privateConstructorUsedError;
  String get price => throw _privateConstructorUsedError;
  String get productID => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PurchasePriceCopyWith<PurchasePrice> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchasePriceCopyWith<$Res> {
  factory $PurchasePriceCopyWith(
          PurchasePrice value, $Res Function(PurchasePrice) then) =
      _$PurchasePriceCopyWithImpl<$Res, PurchasePrice>;
  @useResult
  $Res call(
      {String channel,
      String currency,
      String defaultCurrency,
      String defaultPrice,
      String price,
      String productID});
}

/// @nodoc
class _$PurchasePriceCopyWithImpl<$Res, $Val extends PurchasePrice>
    implements $PurchasePriceCopyWith<$Res> {
  _$PurchasePriceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channel = null,
    Object? currency = null,
    Object? defaultCurrency = null,
    Object? defaultPrice = null,
    Object? price = null,
    Object? productID = null,
  }) {
    return _then(_value.copyWith(
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      defaultCurrency: null == defaultCurrency
          ? _value.defaultCurrency
          : defaultCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      defaultPrice: null == defaultPrice
          ? _value.defaultPrice
          : defaultPrice // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String,
      productID: null == productID
          ? _value.productID
          : productID // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchasePriceImplCopyWith<$Res>
    implements $PurchasePriceCopyWith<$Res> {
  factory _$$PurchasePriceImplCopyWith(
          _$PurchasePriceImpl value, $Res Function(_$PurchasePriceImpl) then) =
      __$$PurchasePriceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String channel,
      String currency,
      String defaultCurrency,
      String defaultPrice,
      String price,
      String productID});
}

/// @nodoc
class __$$PurchasePriceImplCopyWithImpl<$Res>
    extends _$PurchasePriceCopyWithImpl<$Res, _$PurchasePriceImpl>
    implements _$$PurchasePriceImplCopyWith<$Res> {
  __$$PurchasePriceImplCopyWithImpl(
      _$PurchasePriceImpl _value, $Res Function(_$PurchasePriceImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channel = null,
    Object? currency = null,
    Object? defaultCurrency = null,
    Object? defaultPrice = null,
    Object? price = null,
    Object? productID = null,
  }) {
    return _then(_$PurchasePriceImpl(
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      defaultCurrency: null == defaultCurrency
          ? _value.defaultCurrency
          : defaultCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      defaultPrice: null == defaultPrice
          ? _value.defaultPrice
          : defaultPrice // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String,
      productID: null == productID
          ? _value.productID
          : productID // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PurchasePriceImpl implements _PurchasePrice {
  const _$PurchasePriceImpl(
      {required this.channel,
      required this.currency,
      required this.defaultCurrency,
      required this.defaultPrice,
      required this.price,
      required this.productID});

  factory _$PurchasePriceImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchasePriceImplFromJson(json);

  @override
  final String channel;
  @override
  final String currency;
  @override
  final String defaultCurrency;
  @override
  final String defaultPrice;
  @override
  final String price;
  @override
  final String productID;

  @override
  String toString() {
    return 'PurchasePrice(channel: $channel, currency: $currency, defaultCurrency: $defaultCurrency, defaultPrice: $defaultPrice, price: $price, productID: $productID)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchasePriceImpl &&
            (identical(other.channel, channel) || other.channel == channel) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.defaultCurrency, defaultCurrency) ||
                other.defaultCurrency == defaultCurrency) &&
            (identical(other.defaultPrice, defaultPrice) ||
                other.defaultPrice == defaultPrice) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.productID, productID) ||
                other.productID == productID));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, channel, currency,
      defaultCurrency, defaultPrice, price, productID);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchasePriceImplCopyWith<_$PurchasePriceImpl> get copyWith =>
      __$$PurchasePriceImplCopyWithImpl<_$PurchasePriceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchasePriceImplToJson(
      this,
    );
  }
}

abstract class _PurchasePrice implements PurchasePrice {
  const factory _PurchasePrice(
      {required final String channel,
      required final String currency,
      required final String defaultCurrency,
      required final String defaultPrice,
      required final String price,
      required final String productID}) = _$PurchasePriceImpl;

  factory _PurchasePrice.fromJson(Map<String, dynamic> json) =
      _$PurchasePriceImpl.fromJson;

  @override
  String get channel;
  @override
  String get currency;
  @override
  String get defaultCurrency;
  @override
  String get defaultPrice;
  @override
  String get price;
  @override
  String get productID;
  @override
  @JsonKey(ignore: true)
  _$$PurchasePriceImplCopyWith<_$PurchasePriceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
