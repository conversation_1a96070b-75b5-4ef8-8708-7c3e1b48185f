import 'package:freezed_annotation/freezed_annotation.dart';

part 'purchase_tag_icon.freezed.dart';
part 'purchase_tag_icon.g.dart';

@freezed
class PurchaseTagIcon with _$PurchaseTagIcon {
  const factory PurchaseTagIcon({
    required int expire,
    required String ext,
    required int height,
    required int size,
    required String url,
    required int width,
  }) = _PurchaseTagIcon;

  factory PurchaseTagIcon.fromJson(Map<String, dynamic> json) =>
      _$PurchaseTagIconFromJson(json);
}
