// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_info_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PurchaseInfoItem _$PurchaseInfoItemFromJson(Map<String, dynamic> json) {
  return _PurchaseInfoItem.fromJson(json);
}

/// @nodoc
mixin _$PurchaseInfoItem {
  PurchaseValidDuration get period => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  List<PurchasePlan> get items => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PurchaseInfoItemCopyWith<PurchaseInfoItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseInfoItemCopyWith<$Res> {
  factory $PurchaseInfoItemCopyWith(
          PurchaseInfoItem value, $Res Function(PurchaseInfoItem) then) =
      _$PurchaseInfoItemCopyWithImpl<$Res, PurchaseInfoItem>;
  @useResult
  $Res call(
      {PurchaseValidDuration period,
      String id,
      String name,
      List<PurchasePlan> items});

  $PurchaseValidDurationCopyWith<$Res> get period;
}

/// @nodoc
class _$PurchaseInfoItemCopyWithImpl<$Res, $Val extends PurchaseInfoItem>
    implements $PurchaseInfoItemCopyWith<$Res> {
  _$PurchaseInfoItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period = null,
    Object? id = null,
    Object? name = null,
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as PurchaseValidDuration,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PurchasePlan>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PurchaseValidDurationCopyWith<$Res> get period {
    return $PurchaseValidDurationCopyWith<$Res>(_value.period, (value) {
      return _then(_value.copyWith(period: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PurchaseInfoItemImplCopyWith<$Res>
    implements $PurchaseInfoItemCopyWith<$Res> {
  factory _$$PurchaseInfoItemImplCopyWith(_$PurchaseInfoItemImpl value,
          $Res Function(_$PurchaseInfoItemImpl) then) =
      __$$PurchaseInfoItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {PurchaseValidDuration period,
      String id,
      String name,
      List<PurchasePlan> items});

  @override
  $PurchaseValidDurationCopyWith<$Res> get period;
}

/// @nodoc
class __$$PurchaseInfoItemImplCopyWithImpl<$Res>
    extends _$PurchaseInfoItemCopyWithImpl<$Res, _$PurchaseInfoItemImpl>
    implements _$$PurchaseInfoItemImplCopyWith<$Res> {
  __$$PurchaseInfoItemImplCopyWithImpl(_$PurchaseInfoItemImpl _value,
      $Res Function(_$PurchaseInfoItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period = null,
    Object? id = null,
    Object? name = null,
    Object? items = null,
  }) {
    return _then(_$PurchaseInfoItemImpl(
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as PurchaseValidDuration,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PurchasePlan>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PurchaseInfoItemImpl implements _PurchaseInfoItem {
  const _$PurchaseInfoItemImpl(
      {required this.period,
      required this.id,
      required this.name,
      required final List<PurchasePlan> items})
      : _items = items;

  factory _$PurchaseInfoItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseInfoItemImplFromJson(json);

  @override
  final PurchaseValidDuration period;
  @override
  final String id;
  @override
  final String name;
  final List<PurchasePlan> _items;
  @override
  List<PurchasePlan> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'PurchaseInfoItem(period: $period, id: $id, name: $name, items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseInfoItemImpl &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, period, id, name,
      const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseInfoItemImplCopyWith<_$PurchaseInfoItemImpl> get copyWith =>
      __$$PurchaseInfoItemImplCopyWithImpl<_$PurchaseInfoItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseInfoItemImplToJson(
      this,
    );
  }
}

abstract class _PurchaseInfoItem implements PurchaseInfoItem {
  const factory _PurchaseInfoItem(
      {required final PurchaseValidDuration period,
      required final String id,
      required final String name,
      required final List<PurchasePlan> items}) = _$PurchaseInfoItemImpl;

  factory _PurchaseInfoItem.fromJson(Map<String, dynamic> json) =
      _$PurchaseInfoItemImpl.fromJson;

  @override
  PurchaseValidDuration get period;
  @override
  String get id;
  @override
  String get name;
  @override
  List<PurchasePlan> get items;
  @override
  @JsonKey(ignore: true)
  _$$PurchaseInfoItemImplCopyWith<_$PurchaseInfoItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
