// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_plan.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PurchasePlan _$PurchasePlanFromJson(Map<String, dynamic> json) {
  return _PurchasePlan.fromJson(json);
}

/// @nodoc
mixin _$PurchasePlan {
  String get categoryId => throw _privateConstructorUsedError;
  bool get continuousSub => throw _privateConstructorUsedError;
  String get productName => throw _privateConstructorUsedError;
  String get fieldCode => throw _privateConstructorUsedError;
  bool get freeTrial => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;
  int get aigcCount => throw _privateConstructorUsedError;
  int get extraCount => throw _privateConstructorUsedError;
  int get aigcExtraCount => throw _privateConstructorUsedError;
  List<String> get platform => throw _privateConstructorUsedError;
  PurchasePrice get price => throw _privateConstructorUsedError;
  int get priority => throw _privateConstructorUsedError;
  String get purchasePlan => throw _privateConstructorUsedError;
  String get subscribeType => throw _privateConstructorUsedError;
  String? get tag => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get trackId => throw _privateConstructorUsedError;
  PurchaseValidDuration get validDuration => throw _privateConstructorUsedError;
  PurchaseTagIcon? get tagIcon => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PurchasePlanCopyWith<PurchasePlan> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchasePlanCopyWith<$Res> {
  factory $PurchasePlanCopyWith(
          PurchasePlan value, $Res Function(PurchasePlan) then) =
      _$PurchasePlanCopyWithImpl<$Res, PurchasePlan>;
  @useResult
  $Res call(
      {String categoryId,
      bool continuousSub,
      String productName,
      String fieldCode,
      bool freeTrial,
      String id,
      String name,
      int count,
      int aigcCount,
      int extraCount,
      int aigcExtraCount,
      List<String> platform,
      PurchasePrice price,
      int priority,
      String purchasePlan,
      String subscribeType,
      String? tag,
      String type,
      String trackId,
      PurchaseValidDuration validDuration,
      PurchaseTagIcon? tagIcon});

  $PurchasePriceCopyWith<$Res> get price;
  $PurchaseValidDurationCopyWith<$Res> get validDuration;
  $PurchaseTagIconCopyWith<$Res>? get tagIcon;
}

/// @nodoc
class _$PurchasePlanCopyWithImpl<$Res, $Val extends PurchasePlan>
    implements $PurchasePlanCopyWith<$Res> {
  _$PurchasePlanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categoryId = null,
    Object? continuousSub = null,
    Object? productName = null,
    Object? fieldCode = null,
    Object? freeTrial = null,
    Object? id = null,
    Object? name = null,
    Object? count = null,
    Object? aigcCount = null,
    Object? extraCount = null,
    Object? aigcExtraCount = null,
    Object? platform = null,
    Object? price = null,
    Object? priority = null,
    Object? purchasePlan = null,
    Object? subscribeType = null,
    Object? tag = freezed,
    Object? type = null,
    Object? trackId = null,
    Object? validDuration = null,
    Object? tagIcon = freezed,
  }) {
    return _then(_value.copyWith(
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      continuousSub: null == continuousSub
          ? _value.continuousSub
          : continuousSub // ignore: cast_nullable_to_non_nullable
              as bool,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      fieldCode: null == fieldCode
          ? _value.fieldCode
          : fieldCode // ignore: cast_nullable_to_non_nullable
              as String,
      freeTrial: null == freeTrial
          ? _value.freeTrial
          : freeTrial // ignore: cast_nullable_to_non_nullable
              as bool,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      aigcCount: null == aigcCount
          ? _value.aigcCount
          : aigcCount // ignore: cast_nullable_to_non_nullable
              as int,
      extraCount: null == extraCount
          ? _value.extraCount
          : extraCount // ignore: cast_nullable_to_non_nullable
              as int,
      aigcExtraCount: null == aigcExtraCount
          ? _value.aigcExtraCount
          : aigcExtraCount // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as List<String>,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as PurchasePrice,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      purchasePlan: null == purchasePlan
          ? _value.purchasePlan
          : purchasePlan // ignore: cast_nullable_to_non_nullable
              as String,
      subscribeType: null == subscribeType
          ? _value.subscribeType
          : subscribeType // ignore: cast_nullable_to_non_nullable
              as String,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      trackId: null == trackId
          ? _value.trackId
          : trackId // ignore: cast_nullable_to_non_nullable
              as String,
      validDuration: null == validDuration
          ? _value.validDuration
          : validDuration // ignore: cast_nullable_to_non_nullable
              as PurchaseValidDuration,
      tagIcon: freezed == tagIcon
          ? _value.tagIcon
          : tagIcon // ignore: cast_nullable_to_non_nullable
              as PurchaseTagIcon?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PurchasePriceCopyWith<$Res> get price {
    return $PurchasePriceCopyWith<$Res>(_value.price, (value) {
      return _then(_value.copyWith(price: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PurchaseValidDurationCopyWith<$Res> get validDuration {
    return $PurchaseValidDurationCopyWith<$Res>(_value.validDuration, (value) {
      return _then(_value.copyWith(validDuration: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PurchaseTagIconCopyWith<$Res>? get tagIcon {
    if (_value.tagIcon == null) {
      return null;
    }

    return $PurchaseTagIconCopyWith<$Res>(_value.tagIcon!, (value) {
      return _then(_value.copyWith(tagIcon: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PurchasePlanImplCopyWith<$Res>
    implements $PurchasePlanCopyWith<$Res> {
  factory _$$PurchasePlanImplCopyWith(
          _$PurchasePlanImpl value, $Res Function(_$PurchasePlanImpl) then) =
      __$$PurchasePlanImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String categoryId,
      bool continuousSub,
      String productName,
      String fieldCode,
      bool freeTrial,
      String id,
      String name,
      int count,
      int aigcCount,
      int extraCount,
      int aigcExtraCount,
      List<String> platform,
      PurchasePrice price,
      int priority,
      String purchasePlan,
      String subscribeType,
      String? tag,
      String type,
      String trackId,
      PurchaseValidDuration validDuration,
      PurchaseTagIcon? tagIcon});

  @override
  $PurchasePriceCopyWith<$Res> get price;
  @override
  $PurchaseValidDurationCopyWith<$Res> get validDuration;
  @override
  $PurchaseTagIconCopyWith<$Res>? get tagIcon;
}

/// @nodoc
class __$$PurchasePlanImplCopyWithImpl<$Res>
    extends _$PurchasePlanCopyWithImpl<$Res, _$PurchasePlanImpl>
    implements _$$PurchasePlanImplCopyWith<$Res> {
  __$$PurchasePlanImplCopyWithImpl(
      _$PurchasePlanImpl _value, $Res Function(_$PurchasePlanImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categoryId = null,
    Object? continuousSub = null,
    Object? productName = null,
    Object? fieldCode = null,
    Object? freeTrial = null,
    Object? id = null,
    Object? name = null,
    Object? count = null,
    Object? aigcCount = null,
    Object? extraCount = null,
    Object? aigcExtraCount = null,
    Object? platform = null,
    Object? price = null,
    Object? priority = null,
    Object? purchasePlan = null,
    Object? subscribeType = null,
    Object? tag = freezed,
    Object? type = null,
    Object? trackId = null,
    Object? validDuration = null,
    Object? tagIcon = freezed,
  }) {
    return _then(_$PurchasePlanImpl(
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      continuousSub: null == continuousSub
          ? _value.continuousSub
          : continuousSub // ignore: cast_nullable_to_non_nullable
              as bool,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      fieldCode: null == fieldCode
          ? _value.fieldCode
          : fieldCode // ignore: cast_nullable_to_non_nullable
              as String,
      freeTrial: null == freeTrial
          ? _value.freeTrial
          : freeTrial // ignore: cast_nullable_to_non_nullable
              as bool,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      aigcCount: null == aigcCount
          ? _value.aigcCount
          : aigcCount // ignore: cast_nullable_to_non_nullable
              as int,
      extraCount: null == extraCount
          ? _value.extraCount
          : extraCount // ignore: cast_nullable_to_non_nullable
              as int,
      aigcExtraCount: null == aigcExtraCount
          ? _value.aigcExtraCount
          : aigcExtraCount // ignore: cast_nullable_to_non_nullable
              as int,
      platform: null == platform
          ? _value._platform
          : platform // ignore: cast_nullable_to_non_nullable
              as List<String>,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as PurchasePrice,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      purchasePlan: null == purchasePlan
          ? _value.purchasePlan
          : purchasePlan // ignore: cast_nullable_to_non_nullable
              as String,
      subscribeType: null == subscribeType
          ? _value.subscribeType
          : subscribeType // ignore: cast_nullable_to_non_nullable
              as String,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      trackId: null == trackId
          ? _value.trackId
          : trackId // ignore: cast_nullable_to_non_nullable
              as String,
      validDuration: null == validDuration
          ? _value.validDuration
          : validDuration // ignore: cast_nullable_to_non_nullable
              as PurchaseValidDuration,
      tagIcon: freezed == tagIcon
          ? _value.tagIcon
          : tagIcon // ignore: cast_nullable_to_non_nullable
              as PurchaseTagIcon?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PurchasePlanImpl implements _PurchasePlan {
  const _$PurchasePlanImpl(
      {required this.categoryId,
      required this.continuousSub,
      this.productName = '',
      required this.fieldCode,
      required this.freeTrial,
      required this.id,
      required this.name,
      this.count = 0,
      this.aigcCount = 0,
      this.extraCount = 0,
      this.aigcExtraCount = 0,
      required final List<String> platform,
      required this.price,
      required this.priority,
      required this.purchasePlan,
      required this.subscribeType,
      required this.tag,
      this.type = '',
      required this.trackId,
      required this.validDuration,
      this.tagIcon})
      : _platform = platform;

  factory _$PurchasePlanImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchasePlanImplFromJson(json);

  @override
  final String categoryId;
  @override
  final bool continuousSub;
  @override
  @JsonKey()
  final String productName;
  @override
  final String fieldCode;
  @override
  final bool freeTrial;
  @override
  final String id;
  @override
  final String name;
  @override
  @JsonKey()
  final int count;
  @override
  @JsonKey()
  final int aigcCount;
  @override
  @JsonKey()
  final int extraCount;
  @override
  @JsonKey()
  final int aigcExtraCount;
  final List<String> _platform;
  @override
  List<String> get platform {
    if (_platform is EqualUnmodifiableListView) return _platform;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_platform);
  }

  @override
  final PurchasePrice price;
  @override
  final int priority;
  @override
  final String purchasePlan;
  @override
  final String subscribeType;
  @override
  final String? tag;
  @override
  @JsonKey()
  final String type;
  @override
  final String trackId;
  @override
  final PurchaseValidDuration validDuration;
  @override
  final PurchaseTagIcon? tagIcon;

  @override
  String toString() {
    return 'PurchasePlan(categoryId: $categoryId, continuousSub: $continuousSub, productName: $productName, fieldCode: $fieldCode, freeTrial: $freeTrial, id: $id, name: $name, count: $count, aigcCount: $aigcCount, extraCount: $extraCount, aigcExtraCount: $aigcExtraCount, platform: $platform, price: $price, priority: $priority, purchasePlan: $purchasePlan, subscribeType: $subscribeType, tag: $tag, type: $type, trackId: $trackId, validDuration: $validDuration, tagIcon: $tagIcon)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchasePlanImpl &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.continuousSub, continuousSub) ||
                other.continuousSub == continuousSub) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.fieldCode, fieldCode) ||
                other.fieldCode == fieldCode) &&
            (identical(other.freeTrial, freeTrial) ||
                other.freeTrial == freeTrial) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.aigcCount, aigcCount) ||
                other.aigcCount == aigcCount) &&
            (identical(other.extraCount, extraCount) ||
                other.extraCount == extraCount) &&
            (identical(other.aigcExtraCount, aigcExtraCount) ||
                other.aigcExtraCount == aigcExtraCount) &&
            const DeepCollectionEquality().equals(other._platform, _platform) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.purchasePlan, purchasePlan) ||
                other.purchasePlan == purchasePlan) &&
            (identical(other.subscribeType, subscribeType) ||
                other.subscribeType == subscribeType) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.trackId, trackId) || other.trackId == trackId) &&
            (identical(other.validDuration, validDuration) ||
                other.validDuration == validDuration) &&
            (identical(other.tagIcon, tagIcon) || other.tagIcon == tagIcon));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        categoryId,
        continuousSub,
        productName,
        fieldCode,
        freeTrial,
        id,
        name,
        count,
        aigcCount,
        extraCount,
        aigcExtraCount,
        const DeepCollectionEquality().hash(_platform),
        price,
        priority,
        purchasePlan,
        subscribeType,
        tag,
        type,
        trackId,
        validDuration,
        tagIcon
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchasePlanImplCopyWith<_$PurchasePlanImpl> get copyWith =>
      __$$PurchasePlanImplCopyWithImpl<_$PurchasePlanImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchasePlanImplToJson(
      this,
    );
  }
}

abstract class _PurchasePlan implements PurchasePlan {
  const factory _PurchasePlan(
      {required final String categoryId,
      required final bool continuousSub,
      final String productName,
      required final String fieldCode,
      required final bool freeTrial,
      required final String id,
      required final String name,
      final int count,
      final int aigcCount,
      final int extraCount,
      final int aigcExtraCount,
      required final List<String> platform,
      required final PurchasePrice price,
      required final int priority,
      required final String purchasePlan,
      required final String subscribeType,
      required final String? tag,
      final String type,
      required final String trackId,
      required final PurchaseValidDuration validDuration,
      final PurchaseTagIcon? tagIcon}) = _$PurchasePlanImpl;

  factory _PurchasePlan.fromJson(Map<String, dynamic> json) =
      _$PurchasePlanImpl.fromJson;

  @override
  String get categoryId;
  @override
  bool get continuousSub;
  @override
  String get productName;
  @override
  String get fieldCode;
  @override
  bool get freeTrial;
  @override
  String get id;
  @override
  String get name;
  @override
  int get count;
  @override
  int get aigcCount;
  @override
  int get extraCount;
  @override
  int get aigcExtraCount;
  @override
  List<String> get platform;
  @override
  PurchasePrice get price;
  @override
  int get priority;
  @override
  String get purchasePlan;
  @override
  String get subscribeType;
  @override
  String? get tag;
  @override
  String get type;
  @override
  String get trackId;
  @override
  PurchaseValidDuration get validDuration;
  @override
  PurchaseTagIcon? get tagIcon;
  @override
  @JsonKey(ignore: true)
  _$$PurchasePlanImplCopyWith<_$PurchasePlanImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
