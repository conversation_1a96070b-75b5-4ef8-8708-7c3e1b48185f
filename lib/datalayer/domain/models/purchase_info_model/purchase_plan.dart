import 'package:freezed_annotation/freezed_annotation.dart';

import 'purchase_price.dart';
import 'purchase_tag_icon.dart';
import 'purchase_valid_duration.dart';

part 'purchase_plan.freezed.dart';
part 'purchase_plan.g.dart';

@freezed
class PurchasePlan with _$PurchasePlan {
  const factory PurchasePlan({
    required String categoryId,
    required bool continuousSub,
    @Default('') String productName,
    required String fieldCode,
    required bool freeTrial,
    required String id,
    required String name,
    @Default(0) int count,
    @Default(0) int aigcCount,
    @Default(0) int extraCount,
    @Default(0) int aigcExtraCount,
    required List<String> platform,
    required PurchasePrice price,
    required int priority,
    required String purchasePlan,
    required String subscribeType,
    required String? tag,
    @Default('') String type,
    required String trackId,
    required PurchaseValidDuration validDuration,
    PurchaseTagIcon? tagIcon,
  }) = _PurchasePlan;

  factory PurchasePlan.fromJson(Map<String, dynamic> json) =>
      _$PurchasePlanFromJson(json);
}
