import 'package:freezed_annotation/freezed_annotation.dart';

part 'purchase_price.freezed.dart';
part 'purchase_price.g.dart';

@freezed
class PurchasePrice with _$PurchasePrice {
  const factory PurchasePrice({
    required String channel,
    required String currency,
    required String defaultCurrency,
    required String defaultPrice,
    required String price,
    required String productID,
  }) = _PurchasePrice;

  factory PurchasePrice.fromJson(Map<String, dynamic> json) =>
      _$PurchasePriceFromJson(json);
}
