// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_tag_icon.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PurchaseTagIcon _$PurchaseTagIconFromJson(Map<String, dynamic> json) {
  return _PurchaseTagIcon.fromJson(json);
}

/// @nodoc
mixin _$PurchaseTagIcon {
  int get expire => throw _privateConstructorUsedError;
  String get ext => throw _privateConstructorUsedError;
  int get height => throw _privateConstructorUsedError;
  int get size => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  int get width => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PurchaseTagIconCopyWith<PurchaseTagIcon> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseTagIconCopyWith<$Res> {
  factory $PurchaseTagIconCopyWith(
          PurchaseTagIcon value, $Res Function(PurchaseTagIcon) then) =
      _$PurchaseTagIconCopyWithImpl<$Res, PurchaseTagIcon>;
  @useResult
  $Res call(
      {int expire, String ext, int height, int size, String url, int width});
}

/// @nodoc
class _$PurchaseTagIconCopyWithImpl<$Res, $Val extends PurchaseTagIcon>
    implements $PurchaseTagIconCopyWith<$Res> {
  _$PurchaseTagIconCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expire = null,
    Object? ext = null,
    Object? height = null,
    Object? size = null,
    Object? url = null,
    Object? width = null,
  }) {
    return _then(_value.copyWith(
      expire: null == expire
          ? _value.expire
          : expire // ignore: cast_nullable_to_non_nullable
              as int,
      ext: null == ext
          ? _value.ext
          : ext // ignore: cast_nullable_to_non_nullable
              as String,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseTagIconImplCopyWith<$Res>
    implements $PurchaseTagIconCopyWith<$Res> {
  factory _$$PurchaseTagIconImplCopyWith(_$PurchaseTagIconImpl value,
          $Res Function(_$PurchaseTagIconImpl) then) =
      __$$PurchaseTagIconImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int expire, String ext, int height, int size, String url, int width});
}

/// @nodoc
class __$$PurchaseTagIconImplCopyWithImpl<$Res>
    extends _$PurchaseTagIconCopyWithImpl<$Res, _$PurchaseTagIconImpl>
    implements _$$PurchaseTagIconImplCopyWith<$Res> {
  __$$PurchaseTagIconImplCopyWithImpl(
      _$PurchaseTagIconImpl _value, $Res Function(_$PurchaseTagIconImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expire = null,
    Object? ext = null,
    Object? height = null,
    Object? size = null,
    Object? url = null,
    Object? width = null,
  }) {
    return _then(_$PurchaseTagIconImpl(
      expire: null == expire
          ? _value.expire
          : expire // ignore: cast_nullable_to_non_nullable
              as int,
      ext: null == ext
          ? _value.ext
          : ext // ignore: cast_nullable_to_non_nullable
              as String,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PurchaseTagIconImpl implements _PurchaseTagIcon {
  const _$PurchaseTagIconImpl(
      {required this.expire,
      required this.ext,
      required this.height,
      required this.size,
      required this.url,
      required this.width});

  factory _$PurchaseTagIconImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseTagIconImplFromJson(json);

  @override
  final int expire;
  @override
  final String ext;
  @override
  final int height;
  @override
  final int size;
  @override
  final String url;
  @override
  final int width;

  @override
  String toString() {
    return 'PurchaseTagIcon(expire: $expire, ext: $ext, height: $height, size: $size, url: $url, width: $width)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseTagIconImpl &&
            (identical(other.expire, expire) || other.expire == expire) &&
            (identical(other.ext, ext) || other.ext == ext) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.width, width) || other.width == width));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, expire, ext, height, size, url, width);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseTagIconImplCopyWith<_$PurchaseTagIconImpl> get copyWith =>
      __$$PurchaseTagIconImplCopyWithImpl<_$PurchaseTagIconImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseTagIconImplToJson(
      this,
    );
  }
}

abstract class _PurchaseTagIcon implements PurchaseTagIcon {
  const factory _PurchaseTagIcon(
      {required final int expire,
      required final String ext,
      required final int height,
      required final int size,
      required final String url,
      required final int width}) = _$PurchaseTagIconImpl;

  factory _PurchaseTagIcon.fromJson(Map<String, dynamic> json) =
      _$PurchaseTagIconImpl.fromJson;

  @override
  int get expire;
  @override
  String get ext;
  @override
  int get height;
  @override
  int get size;
  @override
  String get url;
  @override
  int get width;
  @override
  @JsonKey(ignore: true)
  _$$PurchaseTagIconImplCopyWith<_$PurchaseTagIconImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
