import 'package:freezed_annotation/freezed_annotation.dart';

import 'purchase_info_item.dart';

part 'purchase_info_model.freezed.dart';
part 'purchase_info_model.g.dart';

@freezed
class PurchaseInfoModel with _$PurchaseInfoModel {
  const factory PurchaseInfoModel({
    List<PurchaseInfoItem>? items,
  }) = _PurchaseInfoModel;

  factory PurchaseInfoModel.fromJson(Map<String, dynamic> json) =>
      _$PurchaseInfoModelFromJson(json);
}
