/// 购买套餐类型枚举
enum PurchasePlanType {
  /// 基础版
  basic('basic'),

  /// 专业版
  pro('pro'),

  /// 企业版
  enterprise('enterprise'),

  /// 未知类型
  unknown('unknown');

  final String value;

  const PurchasePlanType(this.value);

  /// 从字符串转换为枚举
  static PurchasePlanType fromString(String? value) {
    if (value == null) {
      return PurchasePlanType.unknown;
    }

    return PurchasePlanType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PurchasePlanType.unknown,
    );
  }
}
