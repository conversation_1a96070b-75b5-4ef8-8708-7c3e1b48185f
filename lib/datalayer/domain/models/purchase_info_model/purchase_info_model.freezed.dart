// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PurchaseInfoModel _$PurchaseInfoModelFromJson(Map<String, dynamic> json) {
  return _PurchaseInfoModel.fromJson(json);
}

/// @nodoc
mixin _$PurchaseInfoModel {
  List<PurchaseInfoItem>? get items => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PurchaseInfoModelCopyWith<PurchaseInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseInfoModelCopyWith<$Res> {
  factory $PurchaseInfoModelCopyWith(
          PurchaseInfoModel value, $Res Function(PurchaseInfoModel) then) =
      _$PurchaseInfoModelCopyWithImpl<$Res, PurchaseInfoModel>;
  @useResult
  $Res call({List<PurchaseInfoItem>? items});
}

/// @nodoc
class _$PurchaseInfoModelCopyWithImpl<$Res, $Val extends PurchaseInfoModel>
    implements $PurchaseInfoModelCopyWith<$Res> {
  _$PurchaseInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PurchaseInfoItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseInfoModelImplCopyWith<$Res>
    implements $PurchaseInfoModelCopyWith<$Res> {
  factory _$$PurchaseInfoModelImplCopyWith(_$PurchaseInfoModelImpl value,
          $Res Function(_$PurchaseInfoModelImpl) then) =
      __$$PurchaseInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PurchaseInfoItem>? items});
}

/// @nodoc
class __$$PurchaseInfoModelImplCopyWithImpl<$Res>
    extends _$PurchaseInfoModelCopyWithImpl<$Res, _$PurchaseInfoModelImpl>
    implements _$$PurchaseInfoModelImplCopyWith<$Res> {
  __$$PurchaseInfoModelImplCopyWithImpl(_$PurchaseInfoModelImpl _value,
      $Res Function(_$PurchaseInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
  }) {
    return _then(_$PurchaseInfoModelImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PurchaseInfoItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PurchaseInfoModelImpl implements _PurchaseInfoModel {
  const _$PurchaseInfoModelImpl({final List<PurchaseInfoItem>? items})
      : _items = items;

  factory _$PurchaseInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseInfoModelImplFromJson(json);

  final List<PurchaseInfoItem>? _items;
  @override
  List<PurchaseInfoItem>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PurchaseInfoModel(items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseInfoModelImpl &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseInfoModelImplCopyWith<_$PurchaseInfoModelImpl> get copyWith =>
      __$$PurchaseInfoModelImplCopyWithImpl<_$PurchaseInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseInfoModelImplToJson(
      this,
    );
  }
}

abstract class _PurchaseInfoModel implements PurchaseInfoModel {
  const factory _PurchaseInfoModel({final List<PurchaseInfoItem>? items}) =
      _$PurchaseInfoModelImpl;

  factory _PurchaseInfoModel.fromJson(Map<String, dynamic> json) =
      _$PurchaseInfoModelImpl.fromJson;

  @override
  List<PurchaseInfoItem>? get items;
  @override
  @JsonKey(ignore: true)
  _$$PurchaseInfoModelImplCopyWith<_$PurchaseInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
