import 'package:freezed_annotation/freezed_annotation.dart';

part 'purchase_valid_duration.freezed.dart';
part 'purchase_valid_duration.g.dart';

// 有效期
@freezed
class PurchaseValidDuration with _$PurchaseValidDuration {
  const factory PurchaseValidDuration({
    required int begin,
    required int end,
  }) = _PurchaseValidDuration;

  factory PurchaseValidDuration.fromJson(Map<String, dynamic> json) =>
      _$PurchaseValidDurationFromJson(json);
}
