import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_valid_duration.dart';

import 'purchase_plan.dart';

part 'purchase_info_item.freezed.dart';
part 'purchase_info_item.g.dart';

@freezed
class PurchaseInfoItem with _$PurchaseInfoItem {
  const factory PurchaseInfoItem({
    required PurchaseValidDuration period,
    required String id,
    required String name,
    required List<PurchasePlan> items,
  }) = _PurchaseInfoItem;

  factory PurchaseInfoItem.fromJson(Map<String, dynamic> json) =>
      _$PurchaseInfoItemFromJson(json);
}
