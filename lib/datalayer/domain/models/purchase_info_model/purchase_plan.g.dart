// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_plan.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PurchasePlanImpl _$$PurchasePlanImplFromJson(Map<String, dynamic> json) =>
    _$PurchasePlanImpl(
      categoryId: json['categoryId'] as String,
      continuousSub: json['continuousSub'] as bool,
      productName: json['productName'] as String? ?? '',
      fieldCode: json['fieldCode'] as String,
      freeTrial: json['freeTrial'] as bool,
      id: json['id'] as String,
      name: json['name'] as String,
      count: (json['count'] as num?)?.toInt() ?? 0,
      aigcCount: (json['aigcCount'] as num?)?.toInt() ?? 0,
      extraCount: (json['extraCount'] as num?)?.toInt() ?? 0,
      aigcExtraCount: (json['aigcExtraCount'] as num?)?.toInt() ?? 0,
      platform:
          (json['platform'] as List<dynamic>).map((e) => e as String).toList(),
      price: PurchasePrice.fromJson(json['price'] as Map<String, dynamic>),
      priority: (json['priority'] as num).toInt(),
      purchasePlan: json['purchasePlan'] as String,
      subscribeType: json['subscribeType'] as String,
      tag: json['tag'] as String?,
      type: json['type'] as String? ?? '',
      trackId: json['trackId'] as String,
      validDuration: PurchaseValidDuration.fromJson(
          json['validDuration'] as Map<String, dynamic>),
      tagIcon: json['tagIcon'] == null
          ? null
          : PurchaseTagIcon.fromJson(json['tagIcon'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$PurchasePlanImplToJson(_$PurchasePlanImpl instance) =>
    <String, dynamic>{
      'categoryId': instance.categoryId,
      'continuousSub': instance.continuousSub,
      'productName': instance.productName,
      'fieldCode': instance.fieldCode,
      'freeTrial': instance.freeTrial,
      'id': instance.id,
      'name': instance.name,
      'count': instance.count,
      'aigcCount': instance.aigcCount,
      'extraCount': instance.extraCount,
      'aigcExtraCount': instance.aigcExtraCount,
      'platform': instance.platform,
      'price': instance.price,
      'priority': instance.priority,
      'purchasePlan': instance.purchasePlan,
      'subscribeType': instance.subscribeType,
      'tag': instance.tag,
      'type': instance.type,
      'trackId': instance.trackId,
      'validDuration': instance.validDuration,
      'tagIcon': instance.tagIcon,
    };
