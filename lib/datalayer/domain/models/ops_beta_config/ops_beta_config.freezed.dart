// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ops_beta_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OpsBetaConfig _$OpsBetaConfigFromJson(Map<String, dynamic> json) {
  return _OpsBetaConfig.fromJson(json);
}

/// @nodoc
mixin _$OpsBetaConfig {
  String get expiredTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OpsBetaConfigCopyWith<OpsBetaConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OpsBetaConfigCopyWith<$Res> {
  factory $OpsBetaConfigCopyWith(
          OpsBetaConfig value, $Res Function(OpsBetaConfig) then) =
      _$OpsBetaConfigCopyWithImpl<$Res, OpsBetaConfig>;
  @useResult
  $Res call({String expiredTime});
}

/// @nodoc
class _$OpsBetaConfigCopyWithImpl<$Res, $Val extends OpsBetaConfig>
    implements $OpsBetaConfigCopyWith<$Res> {
  _$OpsBetaConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expiredTime = null,
  }) {
    return _then(_value.copyWith(
      expiredTime: null == expiredTime
          ? _value.expiredTime
          : expiredTime // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OpsBetaConfigImplCopyWith<$Res>
    implements $OpsBetaConfigCopyWith<$Res> {
  factory _$$OpsBetaConfigImplCopyWith(
          _$OpsBetaConfigImpl value, $Res Function(_$OpsBetaConfigImpl) then) =
      __$$OpsBetaConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String expiredTime});
}

/// @nodoc
class __$$OpsBetaConfigImplCopyWithImpl<$Res>
    extends _$OpsBetaConfigCopyWithImpl<$Res, _$OpsBetaConfigImpl>
    implements _$$OpsBetaConfigImplCopyWith<$Res> {
  __$$OpsBetaConfigImplCopyWithImpl(
      _$OpsBetaConfigImpl _value, $Res Function(_$OpsBetaConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expiredTime = null,
  }) {
    return _then(_$OpsBetaConfigImpl(
      expiredTime: null == expiredTime
          ? _value.expiredTime
          : expiredTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OpsBetaConfigImpl implements _OpsBetaConfig {
  const _$OpsBetaConfigImpl({required this.expiredTime});

  factory _$OpsBetaConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$OpsBetaConfigImplFromJson(json);

  @override
  final String expiredTime;

  @override
  String toString() {
    return 'OpsBetaConfig(expiredTime: $expiredTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OpsBetaConfigImpl &&
            (identical(other.expiredTime, expiredTime) ||
                other.expiredTime == expiredTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, expiredTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OpsBetaConfigImplCopyWith<_$OpsBetaConfigImpl> get copyWith =>
      __$$OpsBetaConfigImplCopyWithImpl<_$OpsBetaConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OpsBetaConfigImplToJson(
      this,
    );
  }
}

abstract class _OpsBetaConfig implements OpsBetaConfig {
  const factory _OpsBetaConfig({required final String expiredTime}) =
      _$OpsBetaConfigImpl;

  factory _OpsBetaConfig.fromJson(Map<String, dynamic> json) =
      _$OpsBetaConfigImpl.fromJson;

  @override
  String get expiredTime;
  @override
  @JsonKey(ignore: true)
  _$$OpsBetaConfigImplCopyWith<_$OpsBetaConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
