import 'package:freezed_annotation/freezed_annotation.dart';

part 'coupon.freezed.dart';
part 'coupon.g.dart';

/// 兑换码信息
@freezed
class Coupon with _$Coupon {
  const factory Coupon({
    /// 兑换码类型
    String? type,

    /// 兑换数量
    int? amount,

    /// 过期时间戳
    String? expiredAt,
  }) = _Coupon;

  const Coupon._();

  factory Coupon.fromJson(Map<String, dynamic> json) => _$CouponFromJson(json);
}
