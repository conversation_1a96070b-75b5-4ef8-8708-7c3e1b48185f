import 'package:freezed_annotation/freezed_annotation.dart';

part 'export_authentication.freezed.dart';
part 'export_authentication.g.dart';

// 导出鉴权，判断用户是否可以导出
// TODO: 具体结构待定
@freezed
class ExportAuthentication with _$ExportAuthentication {
  const factory ExportAuthentication({
    required String uuid,
  }) = _ExportAuthentication;
  const ExportAuthentication._();

  factory ExportAuthentication.fromJson(Map<String, dynamic> json) =>
      _$ExportAuthenticationFromJson(json);
}
