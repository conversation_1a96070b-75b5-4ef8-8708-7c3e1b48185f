// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'export_authentication.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExportAuthentication _$ExportAuthenticationFromJson(Map<String, dynamic> json) {
  return _ExportAuthentication.fromJson(json);
}

/// @nodoc
mixin _$ExportAuthentication {
  String get uuid => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportAuthenticationCopyWith<ExportAuthentication> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportAuthenticationCopyWith<$Res> {
  factory $ExportAuthenticationCopyWith(ExportAuthentication value,
          $Res Function(ExportAuthentication) then) =
      _$ExportAuthenticationCopyWithImpl<$Res, ExportAuthentication>;
  @useResult
  $Res call({String uuid});
}

/// @nodoc
class _$ExportAuthenticationCopyWithImpl<$Res,
        $Val extends ExportAuthentication>
    implements $ExportAuthenticationCopyWith<$Res> {
  _$ExportAuthenticationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
  }) {
    return _then(_value.copyWith(
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportAuthenticationImplCopyWith<$Res>
    implements $ExportAuthenticationCopyWith<$Res> {
  factory _$$ExportAuthenticationImplCopyWith(_$ExportAuthenticationImpl value,
          $Res Function(_$ExportAuthenticationImpl) then) =
      __$$ExportAuthenticationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String uuid});
}

/// @nodoc
class __$$ExportAuthenticationImplCopyWithImpl<$Res>
    extends _$ExportAuthenticationCopyWithImpl<$Res, _$ExportAuthenticationImpl>
    implements _$$ExportAuthenticationImplCopyWith<$Res> {
  __$$ExportAuthenticationImplCopyWithImpl(_$ExportAuthenticationImpl _value,
      $Res Function(_$ExportAuthenticationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
  }) {
    return _then(_$ExportAuthenticationImpl(
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportAuthenticationImpl extends _ExportAuthentication {
  const _$ExportAuthenticationImpl({required this.uuid}) : super._();

  factory _$ExportAuthenticationImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportAuthenticationImplFromJson(json);

  @override
  final String uuid;

  @override
  String toString() {
    return 'ExportAuthentication(uuid: $uuid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportAuthenticationImpl &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, uuid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportAuthenticationImplCopyWith<_$ExportAuthenticationImpl>
      get copyWith =>
          __$$ExportAuthenticationImplCopyWithImpl<_$ExportAuthenticationImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportAuthenticationImplToJson(
      this,
    );
  }
}

abstract class _ExportAuthentication extends ExportAuthentication {
  const factory _ExportAuthentication({required final String uuid}) =
      _$ExportAuthenticationImpl;
  const _ExportAuthentication._() : super._();

  factory _ExportAuthentication.fromJson(Map<String, dynamic> json) =
      _$ExportAuthenticationImpl.fromJson;

  @override
  String get uuid;
  @override
  @JsonKey(ignore: true)
  _$$ExportAuthenticationImplCopyWith<_$ExportAuthenticationImpl>
      get copyWith => throw _privateConstructorUsedError;
}
