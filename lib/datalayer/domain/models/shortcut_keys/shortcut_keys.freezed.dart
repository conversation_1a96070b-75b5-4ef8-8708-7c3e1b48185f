// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shortcut_keys.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ShortcutCategory _$ShortcutCategoryFromJson(Map<String, dynamic> json) {
  return _ShortcutCategory.fromJson(json);
}

/// @nodoc
mixin _$ShortcutCategory {
  String get title => throw _privateConstructorUsedError;
  List<ShortcutGroup> get groups => throw _privateConstructorUsedError;
  bool get isExpanded => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShortcutCategoryCopyWith<ShortcutCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShortcutCategoryCopyWith<$Res> {
  factory $ShortcutCategoryCopyWith(
          ShortcutCategory value, $Res Function(ShortcutCategory) then) =
      _$ShortcutCategoryCopyWithImpl<$Res, ShortcutCategory>;
  @useResult
  $Res call({String title, List<ShortcutGroup> groups, bool isExpanded});
}

/// @nodoc
class _$ShortcutCategoryCopyWithImpl<$Res, $Val extends ShortcutCategory>
    implements $ShortcutCategoryCopyWith<$Res> {
  _$ShortcutCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? groups = null,
    Object? isExpanded = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      groups: null == groups
          ? _value.groups
          : groups // ignore: cast_nullable_to_non_nullable
              as List<ShortcutGroup>,
      isExpanded: null == isExpanded
          ? _value.isExpanded
          : isExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShortcutCategoryImplCopyWith<$Res>
    implements $ShortcutCategoryCopyWith<$Res> {
  factory _$$ShortcutCategoryImplCopyWith(_$ShortcutCategoryImpl value,
          $Res Function(_$ShortcutCategoryImpl) then) =
      __$$ShortcutCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title, List<ShortcutGroup> groups, bool isExpanded});
}

/// @nodoc
class __$$ShortcutCategoryImplCopyWithImpl<$Res>
    extends _$ShortcutCategoryCopyWithImpl<$Res, _$ShortcutCategoryImpl>
    implements _$$ShortcutCategoryImplCopyWith<$Res> {
  __$$ShortcutCategoryImplCopyWithImpl(_$ShortcutCategoryImpl _value,
      $Res Function(_$ShortcutCategoryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? groups = null,
    Object? isExpanded = null,
  }) {
    return _then(_$ShortcutCategoryImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      groups: null == groups
          ? _value._groups
          : groups // ignore: cast_nullable_to_non_nullable
              as List<ShortcutGroup>,
      isExpanded: null == isExpanded
          ? _value.isExpanded
          : isExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShortcutCategoryImpl implements _ShortcutCategory {
  const _$ShortcutCategoryImpl(
      {required this.title,
      final List<ShortcutGroup> groups = const [],
      this.isExpanded = false})
      : _groups = groups;

  factory _$ShortcutCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShortcutCategoryImplFromJson(json);

  @override
  final String title;
  final List<ShortcutGroup> _groups;
  @override
  @JsonKey()
  List<ShortcutGroup> get groups {
    if (_groups is EqualUnmodifiableListView) return _groups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_groups);
  }

  @override
  @JsonKey()
  final bool isExpanded;

  @override
  String toString() {
    return 'ShortcutCategory(title: $title, groups: $groups, isExpanded: $isExpanded)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShortcutCategoryImpl &&
            (identical(other.title, title) || other.title == title) &&
            const DeepCollectionEquality().equals(other._groups, _groups) &&
            (identical(other.isExpanded, isExpanded) ||
                other.isExpanded == isExpanded));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title,
      const DeepCollectionEquality().hash(_groups), isExpanded);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShortcutCategoryImplCopyWith<_$ShortcutCategoryImpl> get copyWith =>
      __$$ShortcutCategoryImplCopyWithImpl<_$ShortcutCategoryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShortcutCategoryImplToJson(
      this,
    );
  }
}

abstract class _ShortcutCategory implements ShortcutCategory {
  const factory _ShortcutCategory(
      {required final String title,
      final List<ShortcutGroup> groups,
      final bool isExpanded}) = _$ShortcutCategoryImpl;

  factory _ShortcutCategory.fromJson(Map<String, dynamic> json) =
      _$ShortcutCategoryImpl.fromJson;

  @override
  String get title;
  @override
  List<ShortcutGroup> get groups;
  @override
  bool get isExpanded;
  @override
  @JsonKey(ignore: true)
  _$$ShortcutCategoryImplCopyWith<_$ShortcutCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ShortcutGroup _$ShortcutGroupFromJson(Map<String, dynamic> json) {
  return _ShortcutGroup.fromJson(json);
}

/// @nodoc
mixin _$ShortcutGroup {
  String get title => throw _privateConstructorUsedError;
  List<ShortcutItem> get shortcuts => throw _privateConstructorUsedError;
  bool get isExpanded => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShortcutGroupCopyWith<ShortcutGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShortcutGroupCopyWith<$Res> {
  factory $ShortcutGroupCopyWith(
          ShortcutGroup value, $Res Function(ShortcutGroup) then) =
      _$ShortcutGroupCopyWithImpl<$Res, ShortcutGroup>;
  @useResult
  $Res call({String title, List<ShortcutItem> shortcuts, bool isExpanded});
}

/// @nodoc
class _$ShortcutGroupCopyWithImpl<$Res, $Val extends ShortcutGroup>
    implements $ShortcutGroupCopyWith<$Res> {
  _$ShortcutGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? shortcuts = null,
    Object? isExpanded = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      shortcuts: null == shortcuts
          ? _value.shortcuts
          : shortcuts // ignore: cast_nullable_to_non_nullable
              as List<ShortcutItem>,
      isExpanded: null == isExpanded
          ? _value.isExpanded
          : isExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShortcutGroupImplCopyWith<$Res>
    implements $ShortcutGroupCopyWith<$Res> {
  factory _$$ShortcutGroupImplCopyWith(
          _$ShortcutGroupImpl value, $Res Function(_$ShortcutGroupImpl) then) =
      __$$ShortcutGroupImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title, List<ShortcutItem> shortcuts, bool isExpanded});
}

/// @nodoc
class __$$ShortcutGroupImplCopyWithImpl<$Res>
    extends _$ShortcutGroupCopyWithImpl<$Res, _$ShortcutGroupImpl>
    implements _$$ShortcutGroupImplCopyWith<$Res> {
  __$$ShortcutGroupImplCopyWithImpl(
      _$ShortcutGroupImpl _value, $Res Function(_$ShortcutGroupImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? shortcuts = null,
    Object? isExpanded = null,
  }) {
    return _then(_$ShortcutGroupImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      shortcuts: null == shortcuts
          ? _value._shortcuts
          : shortcuts // ignore: cast_nullable_to_non_nullable
              as List<ShortcutItem>,
      isExpanded: null == isExpanded
          ? _value.isExpanded
          : isExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShortcutGroupImpl implements _ShortcutGroup {
  const _$ShortcutGroupImpl(
      {required this.title,
      final List<ShortcutItem> shortcuts = const [],
      this.isExpanded = false})
      : _shortcuts = shortcuts;

  factory _$ShortcutGroupImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShortcutGroupImplFromJson(json);

  @override
  final String title;
  final List<ShortcutItem> _shortcuts;
  @override
  @JsonKey()
  List<ShortcutItem> get shortcuts {
    if (_shortcuts is EqualUnmodifiableListView) return _shortcuts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_shortcuts);
  }

  @override
  @JsonKey()
  final bool isExpanded;

  @override
  String toString() {
    return 'ShortcutGroup(title: $title, shortcuts: $shortcuts, isExpanded: $isExpanded)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShortcutGroupImpl &&
            (identical(other.title, title) || other.title == title) &&
            const DeepCollectionEquality()
                .equals(other._shortcuts, _shortcuts) &&
            (identical(other.isExpanded, isExpanded) ||
                other.isExpanded == isExpanded));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title,
      const DeepCollectionEquality().hash(_shortcuts), isExpanded);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShortcutGroupImplCopyWith<_$ShortcutGroupImpl> get copyWith =>
      __$$ShortcutGroupImplCopyWithImpl<_$ShortcutGroupImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShortcutGroupImplToJson(
      this,
    );
  }
}

abstract class _ShortcutGroup implements ShortcutGroup {
  const factory _ShortcutGroup(
      {required final String title,
      final List<ShortcutItem> shortcuts,
      final bool isExpanded}) = _$ShortcutGroupImpl;

  factory _ShortcutGroup.fromJson(Map<String, dynamic> json) =
      _$ShortcutGroupImpl.fromJson;

  @override
  String get title;
  @override
  List<ShortcutItem> get shortcuts;
  @override
  bool get isExpanded;
  @override
  @JsonKey(ignore: true)
  _$$ShortcutGroupImplCopyWith<_$ShortcutGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ShortcutItem _$ShortcutItemFromJson(Map<String, dynamic> json) {
  return _ShortcutItem.fromJson(json);
}

/// @nodoc
mixin _$ShortcutItem {
  String get title => throw _privateConstructorUsedError;
  String get shortcut => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShortcutItemCopyWith<ShortcutItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShortcutItemCopyWith<$Res> {
  factory $ShortcutItemCopyWith(
          ShortcutItem value, $Res Function(ShortcutItem) then) =
      _$ShortcutItemCopyWithImpl<$Res, ShortcutItem>;
  @useResult
  $Res call({String title, String shortcut});
}

/// @nodoc
class _$ShortcutItemCopyWithImpl<$Res, $Val extends ShortcutItem>
    implements $ShortcutItemCopyWith<$Res> {
  _$ShortcutItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? shortcut = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      shortcut: null == shortcut
          ? _value.shortcut
          : shortcut // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShortcutItemImplCopyWith<$Res>
    implements $ShortcutItemCopyWith<$Res> {
  factory _$$ShortcutItemImplCopyWith(
          _$ShortcutItemImpl value, $Res Function(_$ShortcutItemImpl) then) =
      __$$ShortcutItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title, String shortcut});
}

/// @nodoc
class __$$ShortcutItemImplCopyWithImpl<$Res>
    extends _$ShortcutItemCopyWithImpl<$Res, _$ShortcutItemImpl>
    implements _$$ShortcutItemImplCopyWith<$Res> {
  __$$ShortcutItemImplCopyWithImpl(
      _$ShortcutItemImpl _value, $Res Function(_$ShortcutItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? shortcut = null,
  }) {
    return _then(_$ShortcutItemImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      shortcut: null == shortcut
          ? _value.shortcut
          : shortcut // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShortcutItemImpl implements _ShortcutItem {
  const _$ShortcutItemImpl({required this.title, required this.shortcut});

  factory _$ShortcutItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShortcutItemImplFromJson(json);

  @override
  final String title;
  @override
  final String shortcut;

  @override
  String toString() {
    return 'ShortcutItem(title: $title, shortcut: $shortcut)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShortcutItemImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.shortcut, shortcut) ||
                other.shortcut == shortcut));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, shortcut);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShortcutItemImplCopyWith<_$ShortcutItemImpl> get copyWith =>
      __$$ShortcutItemImplCopyWithImpl<_$ShortcutItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShortcutItemImplToJson(
      this,
    );
  }
}

abstract class _ShortcutItem implements ShortcutItem {
  const factory _ShortcutItem(
      {required final String title,
      required final String shortcut}) = _$ShortcutItemImpl;

  factory _ShortcutItem.fromJson(Map<String, dynamic> json) =
      _$ShortcutItemImpl.fromJson;

  @override
  String get title;
  @override
  String get shortcut;
  @override
  @JsonKey(ignore: true)
  _$$ShortcutItemImplCopyWith<_$ShortcutItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
