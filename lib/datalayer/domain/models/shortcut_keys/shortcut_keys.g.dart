// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shortcut_keys.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ShortcutCategoryImpl _$$ShortcutCategoryImplFromJson(
        Map<String, dynamic> json) =>
    _$ShortcutCategoryImpl(
      title: json['title'] as String,
      groups: (json['groups'] as List<dynamic>?)
              ?.map((e) => ShortcutGroup.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isExpanded: json['isExpanded'] as bool? ?? false,
    );

Map<String, dynamic> _$$ShortcutCategoryImplToJson(
        _$ShortcutCategoryImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'groups': instance.groups,
      'isExpanded': instance.isExpanded,
    };

_$ShortcutGroupImpl _$$ShortcutGroupImplFromJson(Map<String, dynamic> json) =>
    _$ShortcutGroupImpl(
      title: json['title'] as String,
      shortcuts: (json['shortcuts'] as List<dynamic>?)
              ?.map((e) => ShortcutItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isExpanded: json['isExpanded'] as bool? ?? false,
    );

Map<String, dynamic> _$$ShortcutGroupImplToJson(_$ShortcutGroupImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'shortcuts': instance.shortcuts,
      'isExpanded': instance.isExpanded,
    };

_$ShortcutItemImpl _$$ShortcutItemImplFromJson(Map<String, dynamic> json) =>
    _$ShortcutItemImpl(
      title: json['title'] as String,
      shortcut: json['shortcut'] as String,
    );

Map<String, dynamic> _$$ShortcutItemImplToJson(_$ShortcutItemImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'shortcut': instance.shortcut,
    };
