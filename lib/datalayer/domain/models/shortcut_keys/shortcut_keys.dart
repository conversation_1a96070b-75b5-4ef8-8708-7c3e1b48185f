import 'package:freezed_annotation/freezed_annotation.dart';

part 'shortcut_keys.freezed.dart';
part 'shortcut_keys.g.dart';

/// 顶层分类（如：全局、图库、精修、人物角色修改等）
@freezed
class ShortcutCategory with _$ShortcutCategory {
  const factory ShortcutCategory({
    required String title,
    @Default([]) List<ShortcutGroup> groups,
    @Default(false) bool isExpanded,
  }) = _ShortcutCategory;

  factory ShortcutCategory.fromJson(Map<String, dynamic> json) =>
      _$ShortcutCategoryFromJson(json);
}

/// 快捷键分组（如：选择、编辑、导航、视图等）
@freezed
class ShortcutGroup with _$ShortcutGroup {
  const factory ShortcutGroup({
    required String title,
    @Default([]) List<ShortcutItem> shortcuts,
    @Default(false) bool isExpanded,
  }) = _ShortcutGroup;

  factory ShortcutGroup.fromJson(Map<String, dynamic> json) =>
      _$ShortcutGroupFromJson(json);
}

/// 具体的快捷键项
@freezed
class ShortcutItem with _$ShortcutItem {
  const factory ShortcutItem({
    required String title,
    required String shortcut,
  }) = _ShortcutItem;

  factory ShortcutItem.fromJson(Map<String, dynamic> json) =>
      _$ShortcutItemFromJson(json);
}
