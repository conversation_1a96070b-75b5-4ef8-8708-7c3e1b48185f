// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'server_time.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ServerTime _$ServerTimeFromJson(Map<String, dynamic> json) {
  return _ServerTime.fromJson(json);
}

/// @nodoc
mixin _$ServerTime {
  String get time => throw _privateConstructorUsedError;
  int get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ServerTimeCopyWith<ServerTime> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ServerTimeCopyWith<$Res> {
  factory $ServerTimeCopyWith(
          ServerTime value, $Res Function(ServerTime) then) =
      _$ServerTimeCopyWithImpl<$Res, ServerTime>;
  @useResult
  $Res call({String time, int timestamp});
}

/// @nodoc
class _$ServerTimeCopyWithImpl<$Res, $Val extends ServerTime>
    implements $ServerTimeCopyWith<$Res> {
  _$ServerTimeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = null,
    Object? timestamp = null,
  }) {
    return _then(_value.copyWith(
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ServerTimeImplCopyWith<$Res>
    implements $ServerTimeCopyWith<$Res> {
  factory _$$ServerTimeImplCopyWith(
          _$ServerTimeImpl value, $Res Function(_$ServerTimeImpl) then) =
      __$$ServerTimeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String time, int timestamp});
}

/// @nodoc
class __$$ServerTimeImplCopyWithImpl<$Res>
    extends _$ServerTimeCopyWithImpl<$Res, _$ServerTimeImpl>
    implements _$$ServerTimeImplCopyWith<$Res> {
  __$$ServerTimeImplCopyWithImpl(
      _$ServerTimeImpl _value, $Res Function(_$ServerTimeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = null,
    Object? timestamp = null,
  }) {
    return _then(_$ServerTimeImpl(
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ServerTimeImpl implements _ServerTime {
  const _$ServerTimeImpl({required this.time, required this.timestamp});

  factory _$ServerTimeImpl.fromJson(Map<String, dynamic> json) =>
      _$$ServerTimeImplFromJson(json);

  @override
  final String time;
  @override
  final int timestamp;

  @override
  String toString() {
    return 'ServerTime(time: $time, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServerTimeImpl &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, time, timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ServerTimeImplCopyWith<_$ServerTimeImpl> get copyWith =>
      __$$ServerTimeImplCopyWithImpl<_$ServerTimeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ServerTimeImplToJson(
      this,
    );
  }
}

abstract class _ServerTime implements ServerTime {
  const factory _ServerTime(
      {required final String time,
      required final int timestamp}) = _$ServerTimeImpl;

  factory _ServerTime.fromJson(Map<String, dynamic> json) =
      _$ServerTimeImpl.fromJson;

  @override
  String get time;
  @override
  int get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$ServerTimeImplCopyWith<_$ServerTimeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
