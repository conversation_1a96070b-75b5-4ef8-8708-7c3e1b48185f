import 'package:drift/drift.dart' hide JsonKey;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    @Default("") String id, // 员工id，需要写进header，作为扣费分发到对应的门店头上
    @Default("") String userId, // 用户id，需要写进header，作为扣费分发到对应的门店头上
    @Default(86) int cc,
    @Default("") String mobile,
    @Default("") String nickname,
    @Default("") String token,
    @Default(0) int tokenExpire,
    @Default("") String tokenEnd,
    @Default(false) bool firstLogin,
    @Default("") String lastLoginTime,
    @Default("") String regDateTime,
    @Default(UserRole.creator)
    UserRole role, // 当前用户在这个门店下的角色，目前有employee和creator
    @Default("0") String used, // 当前用户已使用的片量(计费)，不计费的不纳入
    @Default(true) bool enable, // 是否启用
    @Default("") String lastLoginStoreId, // 最后登录门店ID v1.1.0中新增
  }) = _User;
  const User._();

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  bool get isTokenExpired => DateTime.now().isAfter(DateTime.parse(tokenEnd));

  // 获取用户角色枚举
  UserRole get userRole => role;

  // 从数据库实体转换为 User
  factory User.fromEntity(
    UserEntityData userEntity,
  ) {
    return _User(
      nickname: userEntity.username,
      userId: userEntity.uid,
      mobile: userEntity.phoneNumber,
      token: userEntity.token,
      tokenExpire: userEntity.tokenExpiredDate,
      tokenEnd: userEntity.tokenEnd,
      firstLogin: userEntity.firstLogin == 1,
      lastLoginTime: userEntity.lastLoginTime,
      regDateTime: userEntity.regDateTime,
      role: UserRole.fromString(userEntity.role),
      used: userEntity.used,
      enable: userEntity.enable == 1,
      lastLoginStoreId: userEntity.lastLoginStoreId,
      cc: userEntity.cc,
      id: userEntity.id,
    );
  }

  // 转换为数据库实体
  UserEntityCompanion toEntity() {
    return UserEntityCompanion(
      id: Value(id),
      username: Value(nickname),
      uid: Value(userId),
      phoneNumber: Value(mobile),
      token: Value(token),
      tokenExpiredDate: Value(tokenExpire),
      tokenEnd: Value(tokenEnd),
      firstLogin: Value(firstLogin ? 1 : 0),
      lastLoginTime: Value(lastLoginTime),
      regDateTime: Value(regDateTime),
      role: Value(role.toString()),
      used: Value(used),
      enable: Value(enable ? 1 : 0),
      cc: Value(cc),
      lastLoginStoreId: Value(lastLoginStoreId), // 添加 lastLoginStoreId 字段
    );
  }

  // 获取当前用户有效的ID
  String get effectiveId {
    if (role == UserRole.creator) {
      return userId;
    }
    return id;
  }
}
