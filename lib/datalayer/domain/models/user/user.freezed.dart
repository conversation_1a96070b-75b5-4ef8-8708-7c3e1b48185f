// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  String get id =>
      throw _privateConstructorUsedError; // 员工id，需要写进header，作为扣费分发到对应的门店头上
  String get userId =>
      throw _privateConstructorUsedError; // 用户id，需要写进header，作为扣费分发到对应的门店头上
  int get cc => throw _privateConstructorUsedError;
  String get mobile => throw _privateConstructorUsedError;
  String get nickname => throw _privateConstructorUsedError;
  String get token => throw _privateConstructorUsedError;
  int get tokenExpire => throw _privateConstructorUsedError;
  String get tokenEnd => throw _privateConstructorUsedError;
  bool get firstLogin => throw _privateConstructorUsedError;
  String get lastLoginTime => throw _privateConstructorUsedError;
  String get regDateTime => throw _privateConstructorUsedError;
  UserRole get role =>
      throw _privateConstructorUsedError; // 当前用户在这个门店下的角色，目前有employee和creator
  String get used =>
      throw _privateConstructorUsedError; // 当前用户已使用的片量(计费)，不计费的不纳入
  bool get enable => throw _privateConstructorUsedError; // 是否启用
  String get lastLoginStoreId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {String id,
      String userId,
      int cc,
      String mobile,
      String nickname,
      String token,
      int tokenExpire,
      String tokenEnd,
      bool firstLogin,
      String lastLoginTime,
      String regDateTime,
      UserRole role,
      String used,
      bool enable,
      String lastLoginStoreId});
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? cc = null,
    Object? mobile = null,
    Object? nickname = null,
    Object? token = null,
    Object? tokenExpire = null,
    Object? tokenEnd = null,
    Object? firstLogin = null,
    Object? lastLoginTime = null,
    Object? regDateTime = null,
    Object? role = null,
    Object? used = null,
    Object? enable = null,
    Object? lastLoginStoreId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      cc: null == cc
          ? _value.cc
          : cc // ignore: cast_nullable_to_non_nullable
              as int,
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: null == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      tokenExpire: null == tokenExpire
          ? _value.tokenExpire
          : tokenExpire // ignore: cast_nullable_to_non_nullable
              as int,
      tokenEnd: null == tokenEnd
          ? _value.tokenEnd
          : tokenEnd // ignore: cast_nullable_to_non_nullable
              as String,
      firstLogin: null == firstLogin
          ? _value.firstLogin
          : firstLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      lastLoginTime: null == lastLoginTime
          ? _value.lastLoginTime
          : lastLoginTime // ignore: cast_nullable_to_non_nullable
              as String,
      regDateTime: null == regDateTime
          ? _value.regDateTime
          : regDateTime // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole,
      used: null == used
          ? _value.used
          : used // ignore: cast_nullable_to_non_nullable
              as String,
      enable: null == enable
          ? _value.enable
          : enable // ignore: cast_nullable_to_non_nullable
              as bool,
      lastLoginStoreId: null == lastLoginStoreId
          ? _value.lastLoginStoreId
          : lastLoginStoreId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      int cc,
      String mobile,
      String nickname,
      String token,
      int tokenExpire,
      String tokenEnd,
      bool firstLogin,
      String lastLoginTime,
      String regDateTime,
      UserRole role,
      String used,
      bool enable,
      String lastLoginStoreId});
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? cc = null,
    Object? mobile = null,
    Object? nickname = null,
    Object? token = null,
    Object? tokenExpire = null,
    Object? tokenEnd = null,
    Object? firstLogin = null,
    Object? lastLoginTime = null,
    Object? regDateTime = null,
    Object? role = null,
    Object? used = null,
    Object? enable = null,
    Object? lastLoginStoreId = null,
  }) {
    return _then(_$UserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      cc: null == cc
          ? _value.cc
          : cc // ignore: cast_nullable_to_non_nullable
              as int,
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: null == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      tokenExpire: null == tokenExpire
          ? _value.tokenExpire
          : tokenExpire // ignore: cast_nullable_to_non_nullable
              as int,
      tokenEnd: null == tokenEnd
          ? _value.tokenEnd
          : tokenEnd // ignore: cast_nullable_to_non_nullable
              as String,
      firstLogin: null == firstLogin
          ? _value.firstLogin
          : firstLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      lastLoginTime: null == lastLoginTime
          ? _value.lastLoginTime
          : lastLoginTime // ignore: cast_nullable_to_non_nullable
              as String,
      regDateTime: null == regDateTime
          ? _value.regDateTime
          : regDateTime // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole,
      used: null == used
          ? _value.used
          : used // ignore: cast_nullable_to_non_nullable
              as String,
      enable: null == enable
          ? _value.enable
          : enable // ignore: cast_nullable_to_non_nullable
              as bool,
      lastLoginStoreId: null == lastLoginStoreId
          ? _value.lastLoginStoreId
          : lastLoginStoreId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl extends _User {
  const _$UserImpl(
      {this.id = "",
      this.userId = "",
      this.cc = 86,
      this.mobile = "",
      this.nickname = "",
      this.token = "",
      this.tokenExpire = 0,
      this.tokenEnd = "",
      this.firstLogin = false,
      this.lastLoginTime = "",
      this.regDateTime = "",
      this.role = UserRole.creator,
      this.used = "0",
      this.enable = true,
      this.lastLoginStoreId = ""})
      : super._();

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  @JsonKey()
  final String id;
// 员工id，需要写进header，作为扣费分发到对应的门店头上
  @override
  @JsonKey()
  final String userId;
// 用户id，需要写进header，作为扣费分发到对应的门店头上
  @override
  @JsonKey()
  final int cc;
  @override
  @JsonKey()
  final String mobile;
  @override
  @JsonKey()
  final String nickname;
  @override
  @JsonKey()
  final String token;
  @override
  @JsonKey()
  final int tokenExpire;
  @override
  @JsonKey()
  final String tokenEnd;
  @override
  @JsonKey()
  final bool firstLogin;
  @override
  @JsonKey()
  final String lastLoginTime;
  @override
  @JsonKey()
  final String regDateTime;
  @override
  @JsonKey()
  final UserRole role;
// 当前用户在这个门店下的角色，目前有employee和creator
  @override
  @JsonKey()
  final String used;
// 当前用户已使用的片量(计费)，不计费的不纳入
  @override
  @JsonKey()
  final bool enable;
// 是否启用
  @override
  @JsonKey()
  final String lastLoginStoreId;

  @override
  String toString() {
    return 'User(id: $id, userId: $userId, cc: $cc, mobile: $mobile, nickname: $nickname, token: $token, tokenExpire: $tokenExpire, tokenEnd: $tokenEnd, firstLogin: $firstLogin, lastLoginTime: $lastLoginTime, regDateTime: $regDateTime, role: $role, used: $used, enable: $enable, lastLoginStoreId: $lastLoginStoreId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.cc, cc) || other.cc == cc) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.tokenExpire, tokenExpire) ||
                other.tokenExpire == tokenExpire) &&
            (identical(other.tokenEnd, tokenEnd) ||
                other.tokenEnd == tokenEnd) &&
            (identical(other.firstLogin, firstLogin) ||
                other.firstLogin == firstLogin) &&
            (identical(other.lastLoginTime, lastLoginTime) ||
                other.lastLoginTime == lastLoginTime) &&
            (identical(other.regDateTime, regDateTime) ||
                other.regDateTime == regDateTime) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.used, used) || other.used == used) &&
            (identical(other.enable, enable) || other.enable == enable) &&
            (identical(other.lastLoginStoreId, lastLoginStoreId) ||
                other.lastLoginStoreId == lastLoginStoreId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      cc,
      mobile,
      nickname,
      token,
      tokenExpire,
      tokenEnd,
      firstLogin,
      lastLoginTime,
      regDateTime,
      role,
      used,
      enable,
      lastLoginStoreId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User extends User {
  const factory _User(
      {final String id,
      final String userId,
      final int cc,
      final String mobile,
      final String nickname,
      final String token,
      final int tokenExpire,
      final String tokenEnd,
      final bool firstLogin,
      final String lastLoginTime,
      final String regDateTime,
      final UserRole role,
      final String used,
      final bool enable,
      final String lastLoginStoreId}) = _$UserImpl;
  const _User._() : super._();

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  String get id;
  @override // 员工id，需要写进header，作为扣费分发到对应的门店头上
  String get userId;
  @override // 用户id，需要写进header，作为扣费分发到对应的门店头上
  int get cc;
  @override
  String get mobile;
  @override
  String get nickname;
  @override
  String get token;
  @override
  int get tokenExpire;
  @override
  String get tokenEnd;
  @override
  bool get firstLogin;
  @override
  String get lastLoginTime;
  @override
  String get regDateTime;
  @override
  UserRole get role;
  @override // 当前用户在这个门店下的角色，目前有employee和creator
  String get used;
  @override // 当前用户已使用的片量(计费)，不计费的不纳入
  bool get enable;
  @override // 是否启用
  String get lastLoginStoreId;
  @override
  @JsonKey(ignore: true)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
