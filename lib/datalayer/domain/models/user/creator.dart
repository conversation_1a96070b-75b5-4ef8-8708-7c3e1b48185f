import 'package:drift/drift.dart' hide J<PERSON>Key;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

part 'creator.freezed.dart';
part 'creator.g.dart';

@freezed
class Creator with _$Creator {
  const factory Creator({
    @Default("") String mobile,
  }) = _Creator;

  const Creator._();

  factory Creator.fromJson(Map<String, dynamic> json) =>
      _$CreatorFromJson(json);

  // 从数据库实体转换为 Creator
  static Creator fromEntity(CreatorInfoEntityData entity) {
    return Creator(
      mobile: entity.creatorMobile,
    );
  }

  // 转换为数据库实体
  CreatorInfoEntityCompanion toEntity(String uid) {
    return CreatorInfoEntityCompanion(
      uid: Value(uid),
      creatorMobile: Value(mobile),
    );
  }
}
