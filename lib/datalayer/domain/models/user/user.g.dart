// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: json['id'] as String? ?? "",
      userId: json['userId'] as String? ?? "",
      cc: (json['cc'] as num?)?.toInt() ?? 86,
      mobile: json['mobile'] as String? ?? "",
      nickname: json['nickname'] as String? ?? "",
      token: json['token'] as String? ?? "",
      tokenExpire: (json['tokenExpire'] as num?)?.toInt() ?? 0,
      tokenEnd: json['tokenEnd'] as String? ?? "",
      firstLogin: json['firstLogin'] as bool? ?? false,
      lastLoginTime: json['lastLoginTime'] as String? ?? "",
      regDateTime: json['regDateTime'] as String? ?? "",
      role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']) ??
          UserRole.creator,
      used: json['used'] as String? ?? "0",
      enable: json['enable'] as bool? ?? true,
      lastLoginStoreId: json['lastLoginStoreId'] as String? ?? "",
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'cc': instance.cc,
      'mobile': instance.mobile,
      'nickname': instance.nickname,
      'token': instance.token,
      'tokenExpire': instance.tokenExpire,
      'tokenEnd': instance.tokenEnd,
      'firstLogin': instance.firstLogin,
      'lastLoginTime': instance.lastLoginTime,
      'regDateTime': instance.regDateTime,
      'role': _$UserRoleEnumMap[instance.role]!,
      'used': instance.used,
      'enable': instance.enable,
      'lastLoginStoreId': instance.lastLoginStoreId,
    };

const _$UserRoleEnumMap = {
  UserRole.creator: 'creator',
  UserRole.employee: 'employee',
};
