// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'remote_preset_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RemotePresetItem _$RemotePresetItemFromJson(Map<String, dynamic> json) {
  return _RemotePresetItem.fromJson(json);
}

/// @nodoc
mixin _$RemotePresetItem {
  @JsonKey(name: "_id")
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: "citme")
  int get cTime => throw _privateConstructorUsedError;
  List<RemotePresetData> get data => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get sorted => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RemotePresetItemCopyWith<RemotePresetItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RemotePresetItemCopyWith<$Res> {
  factory $RemotePresetItemCopyWith(
          RemotePresetItem value, $Res Function(RemotePresetItem) then) =
      _$RemotePresetItemCopyWithImpl<$Res, RemotePresetItem>;
  @useResult
  $Res call(
      {@JsonKey(name: "_id") String id,
      @JsonKey(name: "citme") int cTime,
      List<RemotePresetData> data,
      String name,
      int sorted});
}

/// @nodoc
class _$RemotePresetItemCopyWithImpl<$Res, $Val extends RemotePresetItem>
    implements $RemotePresetItemCopyWith<$Res> {
  _$RemotePresetItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cTime = null,
    Object? data = null,
    Object? name = null,
    Object? sorted = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cTime: null == cTime
          ? _value.cTime
          : cTime // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<RemotePresetData>,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      sorted: null == sorted
          ? _value.sorted
          : sorted // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RemotePresetItemImplCopyWith<$Res>
    implements $RemotePresetItemCopyWith<$Res> {
  factory _$$RemotePresetItemImplCopyWith(_$RemotePresetItemImpl value,
          $Res Function(_$RemotePresetItemImpl) then) =
      __$$RemotePresetItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "_id") String id,
      @JsonKey(name: "citme") int cTime,
      List<RemotePresetData> data,
      String name,
      int sorted});
}

/// @nodoc
class __$$RemotePresetItemImplCopyWithImpl<$Res>
    extends _$RemotePresetItemCopyWithImpl<$Res, _$RemotePresetItemImpl>
    implements _$$RemotePresetItemImplCopyWith<$Res> {
  __$$RemotePresetItemImplCopyWithImpl(_$RemotePresetItemImpl _value,
      $Res Function(_$RemotePresetItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cTime = null,
    Object? data = null,
    Object? name = null,
    Object? sorted = null,
  }) {
    return _then(_$RemotePresetItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cTime: null == cTime
          ? _value.cTime
          : cTime // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<RemotePresetData>,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      sorted: null == sorted
          ? _value.sorted
          : sorted // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RemotePresetItemImpl implements _RemotePresetItem {
  const _$RemotePresetItemImpl(
      {@JsonKey(name: "_id") required this.id,
      @JsonKey(name: "citme") required this.cTime,
      required final List<RemotePresetData> data,
      required this.name,
      required this.sorted})
      : _data = data;

  factory _$RemotePresetItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$RemotePresetItemImplFromJson(json);

  @override
  @JsonKey(name: "_id")
  final String id;
  @override
  @JsonKey(name: "citme")
  final int cTime;
  final List<RemotePresetData> _data;
  @override
  List<RemotePresetData> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final String name;
  @override
  final int sorted;

  @override
  String toString() {
    return 'RemotePresetItem(id: $id, cTime: $cTime, data: $data, name: $name, sorted: $sorted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemotePresetItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cTime, cTime) || other.cTime == cTime) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.sorted, sorted) || other.sorted == sorted));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, cTime,
      const DeepCollectionEquality().hash(_data), name, sorted);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RemotePresetItemImplCopyWith<_$RemotePresetItemImpl> get copyWith =>
      __$$RemotePresetItemImplCopyWithImpl<_$RemotePresetItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RemotePresetItemImplToJson(
      this,
    );
  }
}

abstract class _RemotePresetItem implements RemotePresetItem {
  const factory _RemotePresetItem(
      {@JsonKey(name: "_id") required final String id,
      @JsonKey(name: "citme") required final int cTime,
      required final List<RemotePresetData> data,
      required final String name,
      required final int sorted}) = _$RemotePresetItemImpl;

  factory _RemotePresetItem.fromJson(Map<String, dynamic> json) =
      _$RemotePresetItemImpl.fromJson;

  @override
  @JsonKey(name: "_id")
  String get id;
  @override
  @JsonKey(name: "citme")
  int get cTime;
  @override
  List<RemotePresetData> get data;
  @override
  String get name;
  @override
  int get sorted;
  @override
  @JsonKey(ignore: true)
  _$$RemotePresetItemImplCopyWith<_$RemotePresetItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
