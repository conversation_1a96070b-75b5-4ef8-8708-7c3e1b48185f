// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/preset/remote_preset_data.dart';

part 'remote_preset_item.freezed.dart';
part 'remote_preset_item.g.dart';

@freezed
class RemotePresetItem with _$RemotePresetItem {
  const factory RemotePresetItem({
    @JsonKey(name: "_id") required String id,
    @JsonKey(name: "citme") required int cTime,
    required List<RemotePresetData> data,
    required String name,
    required int sorted,
  }) = _RemotePresetItem;

  factory RemotePresetItem.fromJson(Map<String, dynamic> json) =>
      _$RemotePresetItemFromJson(json);
}
