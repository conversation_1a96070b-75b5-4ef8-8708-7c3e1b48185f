// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'preset_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PresetItem _$PresetItemFromJson(Map<String, dynamic> json) {
  return _PresetItem.fromJson(json);
}

/// @nodoc
mixin _$PresetItem {
  String get title => throw _privateConstructorUsedError;
  String get createUser => throw _privateConstructorUsedError;
  String get guid => throw _privateConstructorUsedError;
  String get createTime => throw _privateConstructorUsedError;
  String get updateTime => throw _privateConstructorUsedError;
  int get presetType => throw _privateConstructorUsedError;
  String get presetCategory => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresetItemCopyWith<PresetItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresetItemCopyWith<$Res> {
  factory $PresetItemCopyWith(
          PresetItem value, $Res Function(PresetItem) then) =
      _$PresetItemCopyWithImpl<$Res, PresetItem>;
  @useResult
  $Res call(
      {String title,
      String createUser,
      String guid,
      String createTime,
      String updateTime,
      int presetType,
      String presetCategory});
}

/// @nodoc
class _$PresetItemCopyWithImpl<$Res, $Val extends PresetItem>
    implements $PresetItemCopyWith<$Res> {
  _$PresetItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? createUser = null,
    Object? guid = null,
    Object? createTime = null,
    Object? updateTime = null,
    Object? presetType = null,
    Object? presetCategory = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      createUser: null == createUser
          ? _value.createUser
          : createUser // ignore: cast_nullable_to_non_nullable
              as String,
      guid: null == guid
          ? _value.guid
          : guid // ignore: cast_nullable_to_non_nullable
              as String,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      presetType: null == presetType
          ? _value.presetType
          : presetType // ignore: cast_nullable_to_non_nullable
              as int,
      presetCategory: null == presetCategory
          ? _value.presetCategory
          : presetCategory // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresetItemImplCopyWith<$Res>
    implements $PresetItemCopyWith<$Res> {
  factory _$$PresetItemImplCopyWith(
          _$PresetItemImpl value, $Res Function(_$PresetItemImpl) then) =
      __$$PresetItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      String createUser,
      String guid,
      String createTime,
      String updateTime,
      int presetType,
      String presetCategory});
}

/// @nodoc
class __$$PresetItemImplCopyWithImpl<$Res>
    extends _$PresetItemCopyWithImpl<$Res, _$PresetItemImpl>
    implements _$$PresetItemImplCopyWith<$Res> {
  __$$PresetItemImplCopyWithImpl(
      _$PresetItemImpl _value, $Res Function(_$PresetItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? createUser = null,
    Object? guid = null,
    Object? createTime = null,
    Object? updateTime = null,
    Object? presetType = null,
    Object? presetCategory = null,
  }) {
    return _then(_$PresetItemImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      createUser: null == createUser
          ? _value.createUser
          : createUser // ignore: cast_nullable_to_non_nullable
              as String,
      guid: null == guid
          ? _value.guid
          : guid // ignore: cast_nullable_to_non_nullable
              as String,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      presetType: null == presetType
          ? _value.presetType
          : presetType // ignore: cast_nullable_to_non_nullable
              as int,
      presetCategory: null == presetCategory
          ? _value.presetCategory
          : presetCategory // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresetItemImpl implements _PresetItem {
  const _$PresetItemImpl(
      {required this.title,
      required this.createUser,
      required this.guid,
      required this.createTime,
      required this.updateTime,
      required this.presetType,
      required this.presetCategory});

  factory _$PresetItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresetItemImplFromJson(json);

  @override
  final String title;
  @override
  final String createUser;
  @override
  final String guid;
  @override
  final String createTime;
  @override
  final String updateTime;
  @override
  final int presetType;
  @override
  final String presetCategory;

  @override
  String toString() {
    return 'PresetItem(title: $title, createUser: $createUser, guid: $guid, createTime: $createTime, updateTime: $updateTime, presetType: $presetType, presetCategory: $presetCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresetItemImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.createUser, createUser) ||
                other.createUser == createUser) &&
            (identical(other.guid, guid) || other.guid == guid) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.presetType, presetType) ||
                other.presetType == presetType) &&
            (identical(other.presetCategory, presetCategory) ||
                other.presetCategory == presetCategory));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, createUser, guid,
      createTime, updateTime, presetType, presetCategory);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresetItemImplCopyWith<_$PresetItemImpl> get copyWith =>
      __$$PresetItemImplCopyWithImpl<_$PresetItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresetItemImplToJson(
      this,
    );
  }
}

abstract class _PresetItem implements PresetItem {
  const factory _PresetItem(
      {required final String title,
      required final String createUser,
      required final String guid,
      required final String createTime,
      required final String updateTime,
      required final int presetType,
      required final String presetCategory}) = _$PresetItemImpl;

  factory _PresetItem.fromJson(Map<String, dynamic> json) =
      _$PresetItemImpl.fromJson;

  @override
  String get title;
  @override
  String get createUser;
  @override
  String get guid;
  @override
  String get createTime;
  @override
  String get updateTime;
  @override
  int get presetType;
  @override
  String get presetCategory;
  @override
  @JsonKey(ignore: true)
  _$$PresetItemImplCopyWith<_$PresetItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
