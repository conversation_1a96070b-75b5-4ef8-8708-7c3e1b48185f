// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'remote_preset_data.freezed.dart';
part 'remote_preset_data.g.dart';

@freezed
class RemotePresetData with _$RemotePresetData {
  const factory RemotePresetData({
    @JsonKey(name: "_id") required String id, // 预设 id
    required String cid, // 分类id
    required String name, // 预设名称
    required Map<String, dynamic> param,
    required int ctime,
    required int sorted,
    required int utime,
  }) = _RemotePresetData;

  factory RemotePresetData.fromJson(Map<String, dynamic> json) =>
      _$RemotePresetDataFromJson(json);
}
