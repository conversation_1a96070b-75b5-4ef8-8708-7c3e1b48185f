// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'remote_preset_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RemotePresetData _$RemotePresetDataFromJson(Map<String, dynamic> json) {
  return _RemotePresetData.fromJson(json);
}

/// @nodoc
mixin _$RemotePresetData {
  @JsonKey(name: "_id")
  String get id => throw _privateConstructorUsedError; // 预设 id
  String get cid => throw _privateConstructorUsedError; // 分类id
  String get name => throw _privateConstructorUsedError; // 预设名称
  Map<String, dynamic> get param => throw _privateConstructorUsedError;
  int get ctime => throw _privateConstructorUsedError;
  int get sorted => throw _privateConstructorUsedError;
  int get utime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RemotePresetDataCopyWith<RemotePresetData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RemotePresetDataCopyWith<$Res> {
  factory $RemotePresetDataCopyWith(
          RemotePresetData value, $Res Function(RemotePresetData) then) =
      _$RemotePresetDataCopyWithImpl<$Res, RemotePresetData>;
  @useResult
  $Res call(
      {@JsonKey(name: "_id") String id,
      String cid,
      String name,
      Map<String, dynamic> param,
      int ctime,
      int sorted,
      int utime});
}

/// @nodoc
class _$RemotePresetDataCopyWithImpl<$Res, $Val extends RemotePresetData>
    implements $RemotePresetDataCopyWith<$Res> {
  _$RemotePresetDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cid = null,
    Object? name = null,
    Object? param = null,
    Object? ctime = null,
    Object? sorted = null,
    Object? utime = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cid: null == cid
          ? _value.cid
          : cid // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      param: null == param
          ? _value.param
          : param // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      ctime: null == ctime
          ? _value.ctime
          : ctime // ignore: cast_nullable_to_non_nullable
              as int,
      sorted: null == sorted
          ? _value.sorted
          : sorted // ignore: cast_nullable_to_non_nullable
              as int,
      utime: null == utime
          ? _value.utime
          : utime // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RemotePresetDataImplCopyWith<$Res>
    implements $RemotePresetDataCopyWith<$Res> {
  factory _$$RemotePresetDataImplCopyWith(_$RemotePresetDataImpl value,
          $Res Function(_$RemotePresetDataImpl) then) =
      __$$RemotePresetDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "_id") String id,
      String cid,
      String name,
      Map<String, dynamic> param,
      int ctime,
      int sorted,
      int utime});
}

/// @nodoc
class __$$RemotePresetDataImplCopyWithImpl<$Res>
    extends _$RemotePresetDataCopyWithImpl<$Res, _$RemotePresetDataImpl>
    implements _$$RemotePresetDataImplCopyWith<$Res> {
  __$$RemotePresetDataImplCopyWithImpl(_$RemotePresetDataImpl _value,
      $Res Function(_$RemotePresetDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cid = null,
    Object? name = null,
    Object? param = null,
    Object? ctime = null,
    Object? sorted = null,
    Object? utime = null,
  }) {
    return _then(_$RemotePresetDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cid: null == cid
          ? _value.cid
          : cid // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      param: null == param
          ? _value._param
          : param // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      ctime: null == ctime
          ? _value.ctime
          : ctime // ignore: cast_nullable_to_non_nullable
              as int,
      sorted: null == sorted
          ? _value.sorted
          : sorted // ignore: cast_nullable_to_non_nullable
              as int,
      utime: null == utime
          ? _value.utime
          : utime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RemotePresetDataImpl implements _RemotePresetData {
  const _$RemotePresetDataImpl(
      {@JsonKey(name: "_id") required this.id,
      required this.cid,
      required this.name,
      required final Map<String, dynamic> param,
      required this.ctime,
      required this.sorted,
      required this.utime})
      : _param = param;

  factory _$RemotePresetDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$RemotePresetDataImplFromJson(json);

  @override
  @JsonKey(name: "_id")
  final String id;
// 预设 id
  @override
  final String cid;
// 分类id
  @override
  final String name;
// 预设名称
  final Map<String, dynamic> _param;
// 预设名称
  @override
  Map<String, dynamic> get param {
    if (_param is EqualUnmodifiableMapView) return _param;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_param);
  }

  @override
  final int ctime;
  @override
  final int sorted;
  @override
  final int utime;

  @override
  String toString() {
    return 'RemotePresetData(id: $id, cid: $cid, name: $name, param: $param, ctime: $ctime, sorted: $sorted, utime: $utime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemotePresetDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cid, cid) || other.cid == cid) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._param, _param) &&
            (identical(other.ctime, ctime) || other.ctime == ctime) &&
            (identical(other.sorted, sorted) || other.sorted == sorted) &&
            (identical(other.utime, utime) || other.utime == utime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, cid, name,
      const DeepCollectionEquality().hash(_param), ctime, sorted, utime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RemotePresetDataImplCopyWith<_$RemotePresetDataImpl> get copyWith =>
      __$$RemotePresetDataImplCopyWithImpl<_$RemotePresetDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RemotePresetDataImplToJson(
      this,
    );
  }
}

abstract class _RemotePresetData implements RemotePresetData {
  const factory _RemotePresetData(
      {@JsonKey(name: "_id") required final String id,
      required final String cid,
      required final String name,
      required final Map<String, dynamic> param,
      required final int ctime,
      required final int sorted,
      required final int utime}) = _$RemotePresetDataImpl;

  factory _RemotePresetData.fromJson(Map<String, dynamic> json) =
      _$RemotePresetDataImpl.fromJson;

  @override
  @JsonKey(name: "_id")
  String get id;
  @override // 预设 id
  String get cid;
  @override // 分类id
  String get name;
  @override // 预设名称
  Map<String, dynamic> get param;
  @override
  int get ctime;
  @override
  int get sorted;
  @override
  int get utime;
  @override
  @JsonKey(ignore: true)
  _$$RemotePresetDataImplCopyWith<_$RemotePresetDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
