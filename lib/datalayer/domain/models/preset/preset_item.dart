import 'package:freezed_annotation/freezed_annotation.dart';

part 'preset_item.freezed.dart';
part 'preset_item.g.dart';

// 预设 item model
// 用于所有预设数据展示
@freezed
class PresetItem with _$PresetItem {
  const factory PresetItem({
    required String title,
    required String createUser,
    required String guid,
    required String createTime,
    required String updateTime,
    required int presetType,
    required String presetCategory,
  }) = _PresetItem;

  factory PresetItem.fromJson(Map<String, dynamic> json) =>
      _$PresetItemFromJson(json);
}
