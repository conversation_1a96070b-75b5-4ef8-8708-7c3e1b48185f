// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'export_record.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ExportRecord {
  String get guid => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get showName => throw _privateConstructorUsedError;
  List<String> get exportPaths => throw _privateConstructorUsedError;
  int get exportState => throw _privateConstructorUsedError;
  int get createTime => throw _privateConstructorUsedError;
  String get operateTime => throw _privateConstructorUsedError;
  int get itemCount => throw _privateConstructorUsedError;
  int get successNum => throw _privateConstructorUsedError;
  int? get errorNum => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  bool get isSample => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ExportRecordCopyWith<ExportRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportRecordCopyWith<$Res> {
  factory $ExportRecordCopyWith(
          ExportRecord value, $Res Function(ExportRecord) then) =
      _$ExportRecordCopyWithImpl<$Res, ExportRecord>;
  @useResult
  $Res call(
      {String guid,
      String? name,
      String? showName,
      List<String> exportPaths,
      int exportState,
      int createTime,
      String operateTime,
      int itemCount,
      int successNum,
      int? errorNum,
      String? errorMessage,
      bool isSample});
}

/// @nodoc
class _$ExportRecordCopyWithImpl<$Res, $Val extends ExportRecord>
    implements $ExportRecordCopyWith<$Res> {
  _$ExportRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guid = null,
    Object? name = freezed,
    Object? showName = freezed,
    Object? exportPaths = null,
    Object? exportState = null,
    Object? createTime = null,
    Object? operateTime = null,
    Object? itemCount = null,
    Object? successNum = null,
    Object? errorNum = freezed,
    Object? errorMessage = freezed,
    Object? isSample = null,
  }) {
    return _then(_value.copyWith(
      guid: null == guid
          ? _value.guid
          : guid // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      showName: freezed == showName
          ? _value.showName
          : showName // ignore: cast_nullable_to_non_nullable
              as String?,
      exportPaths: null == exportPaths
          ? _value.exportPaths
          : exportPaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      exportState: null == exportState
          ? _value.exportState
          : exportState // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      operateTime: null == operateTime
          ? _value.operateTime
          : operateTime // ignore: cast_nullable_to_non_nullable
              as String,
      itemCount: null == itemCount
          ? _value.itemCount
          : itemCount // ignore: cast_nullable_to_non_nullable
              as int,
      successNum: null == successNum
          ? _value.successNum
          : successNum // ignore: cast_nullable_to_non_nullable
              as int,
      errorNum: freezed == errorNum
          ? _value.errorNum
          : errorNum // ignore: cast_nullable_to_non_nullable
              as int?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isSample: null == isSample
          ? _value.isSample
          : isSample // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportRecordImplCopyWith<$Res>
    implements $ExportRecordCopyWith<$Res> {
  factory _$$ExportRecordImplCopyWith(
          _$ExportRecordImpl value, $Res Function(_$ExportRecordImpl) then) =
      __$$ExportRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String guid,
      String? name,
      String? showName,
      List<String> exportPaths,
      int exportState,
      int createTime,
      String operateTime,
      int itemCount,
      int successNum,
      int? errorNum,
      String? errorMessage,
      bool isSample});
}

/// @nodoc
class __$$ExportRecordImplCopyWithImpl<$Res>
    extends _$ExportRecordCopyWithImpl<$Res, _$ExportRecordImpl>
    implements _$$ExportRecordImplCopyWith<$Res> {
  __$$ExportRecordImplCopyWithImpl(
      _$ExportRecordImpl _value, $Res Function(_$ExportRecordImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guid = null,
    Object? name = freezed,
    Object? showName = freezed,
    Object? exportPaths = null,
    Object? exportState = null,
    Object? createTime = null,
    Object? operateTime = null,
    Object? itemCount = null,
    Object? successNum = null,
    Object? errorNum = freezed,
    Object? errorMessage = freezed,
    Object? isSample = null,
  }) {
    return _then(_$ExportRecordImpl(
      guid: null == guid
          ? _value.guid
          : guid // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      showName: freezed == showName
          ? _value.showName
          : showName // ignore: cast_nullable_to_non_nullable
              as String?,
      exportPaths: null == exportPaths
          ? _value._exportPaths
          : exportPaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      exportState: null == exportState
          ? _value.exportState
          : exportState // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      operateTime: null == operateTime
          ? _value.operateTime
          : operateTime // ignore: cast_nullable_to_non_nullable
              as String,
      itemCount: null == itemCount
          ? _value.itemCount
          : itemCount // ignore: cast_nullable_to_non_nullable
              as int,
      successNum: null == successNum
          ? _value.successNum
          : successNum // ignore: cast_nullable_to_non_nullable
              as int,
      errorNum: freezed == errorNum
          ? _value.errorNum
          : errorNum // ignore: cast_nullable_to_non_nullable
              as int?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isSample: null == isSample
          ? _value.isSample
          : isSample // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ExportRecordImpl extends _ExportRecord {
  const _$ExportRecordImpl(
      {required this.guid,
      this.name,
      this.showName,
      required final List<String> exportPaths,
      required this.exportState,
      required this.createTime,
      required this.operateTime,
      required this.itemCount,
      required this.successNum,
      this.errorNum,
      this.errorMessage,
      this.isSample = false})
      : _exportPaths = exportPaths,
        super._();

  @override
  final String guid;
  @override
  final String? name;
  @override
  final String? showName;
  final List<String> _exportPaths;
  @override
  List<String> get exportPaths {
    if (_exportPaths is EqualUnmodifiableListView) return _exportPaths;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exportPaths);
  }

  @override
  final int exportState;
  @override
  final int createTime;
  @override
  final String operateTime;
  @override
  final int itemCount;
  @override
  final int successNum;
  @override
  final int? errorNum;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final bool isSample;

  @override
  String toString() {
    return 'ExportRecord(guid: $guid, name: $name, showName: $showName, exportPaths: $exportPaths, exportState: $exportState, createTime: $createTime, operateTime: $operateTime, itemCount: $itemCount, successNum: $successNum, errorNum: $errorNum, errorMessage: $errorMessage, isSample: $isSample)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportRecordImpl &&
            (identical(other.guid, guid) || other.guid == guid) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.showName, showName) ||
                other.showName == showName) &&
            const DeepCollectionEquality()
                .equals(other._exportPaths, _exportPaths) &&
            (identical(other.exportState, exportState) ||
                other.exportState == exportState) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.operateTime, operateTime) ||
                other.operateTime == operateTime) &&
            (identical(other.itemCount, itemCount) ||
                other.itemCount == itemCount) &&
            (identical(other.successNum, successNum) ||
                other.successNum == successNum) &&
            (identical(other.errorNum, errorNum) ||
                other.errorNum == errorNum) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isSample, isSample) ||
                other.isSample == isSample));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      guid,
      name,
      showName,
      const DeepCollectionEquality().hash(_exportPaths),
      exportState,
      createTime,
      operateTime,
      itemCount,
      successNum,
      errorNum,
      errorMessage,
      isSample);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportRecordImplCopyWith<_$ExportRecordImpl> get copyWith =>
      __$$ExportRecordImplCopyWithImpl<_$ExportRecordImpl>(this, _$identity);
}

abstract class _ExportRecord extends ExportRecord {
  const factory _ExportRecord(
      {required final String guid,
      final String? name,
      final String? showName,
      required final List<String> exportPaths,
      required final int exportState,
      required final int createTime,
      required final String operateTime,
      required final int itemCount,
      required final int successNum,
      final int? errorNum,
      final String? errorMessage,
      final bool isSample}) = _$ExportRecordImpl;
  const _ExportRecord._() : super._();

  @override
  String get guid;
  @override
  String? get name;
  @override
  String? get showName;
  @override
  List<String> get exportPaths;
  @override
  int get exportState;
  @override
  int get createTime;
  @override
  String get operateTime;
  @override
  int get itemCount;
  @override
  int get successNum;
  @override
  int? get errorNum;
  @override
  String? get errorMessage;
  @override
  bool get isSample;
  @override
  @JsonKey(ignore: true)
  _$$ExportRecordImplCopyWith<_$ExportRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
