// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_presets_group.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcPresetsGroupImpl _$$AigcPresetsGroupImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcPresetsGroupImpl(
      groupId: json['group_id'] as String,
      supplement: json['requirement_supplement'] as String,
      effectList: (json['effect_list'] as List<dynamic>)
          .map((e) => AigcPresetsEffect.fromJson(e as Map<String, dynamic>))
          .toList(),
      createAt: (json['create_at'] as num).toInt(),
      updateAt: (json['update_at'] as num).toInt(),
    );

Map<String, dynamic> _$$AigcPresetsGroupImplToJson(
        _$AigcPresetsGroupImpl instance) =>
    <String, dynamic>{
      'group_id': instance.groupId,
      'requirement_supplement': instance.supplement,
      'effect_list': instance.effectList,
      'create_at': instance.createAt,
      'update_at': instance.updateAt,
    };
