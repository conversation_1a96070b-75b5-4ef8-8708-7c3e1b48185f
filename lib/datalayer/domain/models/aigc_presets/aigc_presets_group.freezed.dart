// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_presets_group.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcPresetsGroup _$AigcPresetsGroupFromJson(Map<String, dynamic> json) {
  return _AigcPresetsGroup.fromJson(json);
}

/// @nodoc
mixin _$AigcPresetsGroup {
  @JsonKey(name: 'group_id')
  String get groupId => throw _privateConstructorUsedError;
  @JsonKey(name: 'requirement_supplement')
  String get supplement => throw _privateConstructorUsedError; // 创意补充说明
  @JsonKey(name: 'effect_list')
  List<AigcPresetsEffect> get effectList => throw _privateConstructorUsedError;
  @JsonKey(name: 'create_at')
  int get createAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'update_at')
  int get updateAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcPresetsGroupCopyWith<AigcPresetsGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPresetsGroupCopyWith<$Res> {
  factory $AigcPresetsGroupCopyWith(
          AigcPresetsGroup value, $Res Function(AigcPresetsGroup) then) =
      _$AigcPresetsGroupCopyWithImpl<$Res, AigcPresetsGroup>;
  @useResult
  $Res call(
      {@JsonKey(name: 'group_id') String groupId,
      @JsonKey(name: 'requirement_supplement') String supplement,
      @JsonKey(name: 'effect_list') List<AigcPresetsEffect> effectList,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class _$AigcPresetsGroupCopyWithImpl<$Res, $Val extends AigcPresetsGroup>
    implements $AigcPresetsGroupCopyWith<$Res> {
  _$AigcPresetsGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = null,
    Object? supplement = null,
    Object? effectList = null,
    Object? createAt = null,
    Object? updateAt = null,
  }) {
    return _then(_value.copyWith(
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String,
      supplement: null == supplement
          ? _value.supplement
          : supplement // ignore: cast_nullable_to_non_nullable
              as String,
      effectList: null == effectList
          ? _value.effectList
          : effectList // ignore: cast_nullable_to_non_nullable
              as List<AigcPresetsEffect>,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPresetsGroupImplCopyWith<$Res>
    implements $AigcPresetsGroupCopyWith<$Res> {
  factory _$$AigcPresetsGroupImplCopyWith(_$AigcPresetsGroupImpl value,
          $Res Function(_$AigcPresetsGroupImpl) then) =
      __$$AigcPresetsGroupImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'group_id') String groupId,
      @JsonKey(name: 'requirement_supplement') String supplement,
      @JsonKey(name: 'effect_list') List<AigcPresetsEffect> effectList,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class __$$AigcPresetsGroupImplCopyWithImpl<$Res>
    extends _$AigcPresetsGroupCopyWithImpl<$Res, _$AigcPresetsGroupImpl>
    implements _$$AigcPresetsGroupImplCopyWith<$Res> {
  __$$AigcPresetsGroupImplCopyWithImpl(_$AigcPresetsGroupImpl _value,
      $Res Function(_$AigcPresetsGroupImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = null,
    Object? supplement = null,
    Object? effectList = null,
    Object? createAt = null,
    Object? updateAt = null,
  }) {
    return _then(_$AigcPresetsGroupImpl(
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String,
      supplement: null == supplement
          ? _value.supplement
          : supplement // ignore: cast_nullable_to_non_nullable
              as String,
      effectList: null == effectList
          ? _value._effectList
          : effectList // ignore: cast_nullable_to_non_nullable
              as List<AigcPresetsEffect>,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcPresetsGroupImpl implements _AigcPresetsGroup {
  const _$AigcPresetsGroupImpl(
      {@JsonKey(name: 'group_id') required this.groupId,
      @JsonKey(name: 'requirement_supplement') required this.supplement,
      @JsonKey(name: 'effect_list')
      required final List<AigcPresetsEffect> effectList,
      @JsonKey(name: 'create_at') required this.createAt,
      @JsonKey(name: 'update_at') required this.updateAt})
      : _effectList = effectList;

  factory _$AigcPresetsGroupImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcPresetsGroupImplFromJson(json);

  @override
  @JsonKey(name: 'group_id')
  final String groupId;
  @override
  @JsonKey(name: 'requirement_supplement')
  final String supplement;
// 创意补充说明
  final List<AigcPresetsEffect> _effectList;
// 创意补充说明
  @override
  @JsonKey(name: 'effect_list')
  List<AigcPresetsEffect> get effectList {
    if (_effectList is EqualUnmodifiableListView) return _effectList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effectList);
  }

  @override
  @JsonKey(name: 'create_at')
  final int createAt;
  @override
  @JsonKey(name: 'update_at')
  final int updateAt;

  @override
  String toString() {
    return 'AigcPresetsGroup(groupId: $groupId, supplement: $supplement, effectList: $effectList, createAt: $createAt, updateAt: $updateAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPresetsGroupImpl &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.supplement, supplement) ||
                other.supplement == supplement) &&
            const DeepCollectionEquality()
                .equals(other._effectList, _effectList) &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.updateAt, updateAt) ||
                other.updateAt == updateAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, groupId, supplement,
      const DeepCollectionEquality().hash(_effectList), createAt, updateAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPresetsGroupImplCopyWith<_$AigcPresetsGroupImpl> get copyWith =>
      __$$AigcPresetsGroupImplCopyWithImpl<_$AigcPresetsGroupImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcPresetsGroupImplToJson(
      this,
    );
  }
}

abstract class _AigcPresetsGroup implements AigcPresetsGroup {
  const factory _AigcPresetsGroup(
      {@JsonKey(name: 'group_id') required final String groupId,
      @JsonKey(name: 'requirement_supplement') required final String supplement,
      @JsonKey(name: 'effect_list')
      required final List<AigcPresetsEffect> effectList,
      @JsonKey(name: 'create_at') required final int createAt,
      @JsonKey(name: 'update_at')
      required final int updateAt}) = _$AigcPresetsGroupImpl;

  factory _AigcPresetsGroup.fromJson(Map<String, dynamic> json) =
      _$AigcPresetsGroupImpl.fromJson;

  @override
  @JsonKey(name: 'group_id')
  String get groupId;
  @override
  @JsonKey(name: 'requirement_supplement')
  String get supplement;
  @override // 创意补充说明
  @JsonKey(name: 'effect_list')
  List<AigcPresetsEffect> get effectList;
  @override
  @JsonKey(name: 'create_at')
  int get createAt;
  @override
  @JsonKey(name: 'update_at')
  int get updateAt;
  @override
  @JsonKey(ignore: true)
  _$$AigcPresetsGroupImplCopyWith<_$AigcPresetsGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
