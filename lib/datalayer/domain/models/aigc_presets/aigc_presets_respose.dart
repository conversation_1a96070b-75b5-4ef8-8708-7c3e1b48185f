// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';

part 'aigc_presets_respose.freezed.dart';

part 'aigc_presets_respose.g.dart';

/// 主题预设列表响应模型
@freezed
class AigcPcPresetsResponse with _$AigcPcPresetsResponse {
  const factory AigcPcPresetsResponse({
    required List<AigcPcPresetsDetailResponse> presets,
    required int total,
    required int page,
    @JsonKey(name: 'page_size') required int pageSize,
  }) = _AigcPcPresetsResponse;

  factory AigcPcPresetsResponse.fromJson(Map<String, dynamic> json) =>
      _$AigcPcPresetsResponseFrom<PERSON>son(json);
}

/*{
    "presets": [
      {
        "id": "67f88a1e0579e2cec8c15afa",//主题预设ID
        "name":"牧场少年",//主题预设名称
        "status":"completed",
        "effects":[//该预设下的效果图列表（全量预设效果图）
          {
            "effect_code":"xxx",//预设效果图Code
            "name":"创意01",//效果图名称
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//预设效果图Code
            "name":"创意02",//效果图名称
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          },
          {
            "effect_code":"xxx",//预设效果图Code
            "name":"创意03",//效果图名称
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx"//效果图URL
          }
        ],
        "create_at": 1749192432, // 创建时间
        "update_at":1749192432 // 更新时间
      },
    ],
    "total": 20,
    "page": 1,
    "page_size": 10
  }
*/
