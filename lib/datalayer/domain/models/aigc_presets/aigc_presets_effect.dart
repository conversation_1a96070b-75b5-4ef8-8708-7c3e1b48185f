// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_presets_effect.freezed.dart';

part 'aigc_presets_effect.g.dart';

/// 主题预设效果图模型
@freezed
class AigcPresetsEffect with _$AigcPresetsEffect {
  const factory AigcPresetsEffect({
    @Json<PERSON>ey(name: 'effect_code') required String effectCode,
    required String name,
    required String status,
    @Json<PERSON>ey(name: 'thumb_url') String? thumbUrl,
    @Json<PERSON>ey(name: 'photo_url') String? photoUrl,
    @Json<PERSON>ey(name: 'is_in_use', defaultValue: false) required bool isInUse,
    @J<PERSON><PERSON>ey(name: 'error_msg') String? errorMsg, // 错误信息
  }) = _AigcPresetsEffect;

  factory AigcPresetsEffect.fromJson(Map<String, dynamic> json) =>
      _$AigcPresetsEffectFromJson(json);
}

/*
          {
            "effect_code":"xxx",//预设效果图Code
            "name":"创意01",//效果图名称
            "status":"completed",//状态
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx",//效果图URL
            "is_in_use":false//是否在用
          }
*/
