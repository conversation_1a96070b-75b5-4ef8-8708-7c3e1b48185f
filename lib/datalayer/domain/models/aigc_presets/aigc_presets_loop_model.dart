import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_presets_loop_model.freezed.dart';
part 'aigc_presets_loop_model.g.dart';

@freezed
class AigcPcPresetsLoopModel with _$AigcPcPresetsLoopModel {
  const factory AigcPcPresetsLoopModel({
    required String id,
    required String status,
  }) = _AigcPcPresetsLoopModel;

  factory AigcPcPresetsLoopModel.fromJson(Map<String, dynamic> json) =>
      _$AigcPcPresetsLoopModelFromJson(json);
}

/*
[
    {
      "id": "67f88a1e0579e2cec8c15afa",//预设ID
      "status":"running"
    },
    {
      "id": "33f88a1e0579e2cec8c15a79",
      "status":"completed"
    }
  ]
*/
