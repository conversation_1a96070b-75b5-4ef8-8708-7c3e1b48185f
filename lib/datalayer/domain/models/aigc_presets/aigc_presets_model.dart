// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_group.dart';

part 'aigc_presets_model.freezed.dart';

part 'aigc_presets_model.g.dart';

/// 主题预设模型
@freezed
class AigcPresetsModel with _$AigcPresetsModel {
  const factory AigcPresetsModel({
    required String id,
    required String name,
    required String status,
    @JsonKey(name: 'create_at') required int createAt,
    @JsonKey(name: 'update_at') required int updateAt,
    required List<AigcPresetsEffect> effects,
  }) = _AigcPresetsModel;

  factory AigcPresetsModel.fromJson(Map<String, dynamic> json) =>
      _$AigcPresetsModelFromJson(json);

  /// 将返回的原始数据模型转换为 AigcPresetsModel 模型，并且过滤 effect 为当前标记为使用的
  factory AigcPresetsModel.convertResponseToInUsedPresetModel(
      AigcPcPresetsDetailResponse response) {
    final effects = response.effects
        .expand((element) => element.effectList)
        .where((element) => element.isInUse)
        .toList();

    return AigcPresetsModel(
      id: response.id,
      name: response.name,
      status: response.status,
      createAt: response.createAt,
      updateAt: response.updateAt,
      effects: effects,
    );
  }
}

/*
      {
        "id": "67f88a1e0579e2cec8c15afa",//主题预设ID
        "name":"牧场少年",//主题预设名称
        "status":"completed",
        "effects":[//该预设下的效果图列表（全量预设效果图）
          {
            "effect_code":"xxx",//预设效果图Code
            "name":"创意01",//效果图名称
            "status":"completed",//状态
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx",//效果图URL
            "is_in_use":false//是否在用
          },
          {
            "effect_code":"xxx",//预设效果图Code
            "name":"创意02",//效果图名称
            "status":"completed",//状态
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx",//效果图URL
            "is_in_use":false//是否在用
          },
          {
            "effect_code":"xxx",//预设效果图Code
            "name":"创意03",//效果图名称
            "status":"completed",//状态
            "thumb_url": "xxx", //缩略图url
            "photo_url":"xxx",//效果图URL
            "is_in_use":false//是否在用
          }
        ],
        "create_at": 1749192432, // 创建时间
        "update_at":1749192432 // 更新时间
      }
}

 */
