// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';

part 'aigc_presets_group.freezed.dart';

part 'aigc_presets_group.g.dart';

@freezed
class AigcPresetsGroup with _$AigcPresetsGroup {
  const factory AigcPresetsGroup({
    @JsonKey(name: 'group_id') required String groupId,
    @JsonKey(name: 'requirement_supplement')
    required String supplement, // 创意补充说明
    @JsonKey(name: 'effect_list') required List<AigcPresetsEffect> effectList,
    @Json<PERSON>ey(name: 'create_at') required int createAt,
    @JsonKey(name: 'update_at') required int updateAt,
  }) = _AigcPresetsGroup;

  factory AigcPresetsGroup.fromJson(Map<String, dynamic> json) =>
      _$AigcPresetsGroupFromJson(json);
}
