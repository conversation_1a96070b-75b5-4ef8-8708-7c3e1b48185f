// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_presets_effect.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcPresetsEffect _$AigcPresetsEffectFromJson(Map<String, dynamic> json) {
  return _AigcPresetsEffect.fromJson(json);
}

/// @nodoc
mixin _$AigcPresetsEffect {
  @JsonKey(name: 'effect_code')
  String get effectCode => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'thumb_url')
  String? get thumbUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'photo_url')
  String? get photoUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_in_use', defaultValue: false)
  bool get isInUse => throw _privateConstructorUsedError;
  @JsonKey(name: 'error_msg')
  String? get errorMsg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcPresetsEffectCopyWith<AigcPresetsEffect> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPresetsEffectCopyWith<$Res> {
  factory $AigcPresetsEffectCopyWith(
          AigcPresetsEffect value, $Res Function(AigcPresetsEffect) then) =
      _$AigcPresetsEffectCopyWithImpl<$Res, AigcPresetsEffect>;
  @useResult
  $Res call(
      {@JsonKey(name: 'effect_code') String effectCode,
      String name,
      String status,
      @JsonKey(name: 'thumb_url') String? thumbUrl,
      @JsonKey(name: 'photo_url') String? photoUrl,
      @JsonKey(name: 'is_in_use', defaultValue: false) bool isInUse,
      @JsonKey(name: 'error_msg') String? errorMsg});
}

/// @nodoc
class _$AigcPresetsEffectCopyWithImpl<$Res, $Val extends AigcPresetsEffect>
    implements $AigcPresetsEffectCopyWith<$Res> {
  _$AigcPresetsEffectCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? effectCode = null,
    Object? name = null,
    Object? status = null,
    Object? thumbUrl = freezed,
    Object? photoUrl = freezed,
    Object? isInUse = null,
    Object? errorMsg = freezed,
  }) {
    return _then(_value.copyWith(
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      thumbUrl: freezed == thumbUrl
          ? _value.thumbUrl
          : thumbUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isInUse: null == isInUse
          ? _value.isInUse
          : isInUse // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMsg: freezed == errorMsg
          ? _value.errorMsg
          : errorMsg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPresetsEffectImplCopyWith<$Res>
    implements $AigcPresetsEffectCopyWith<$Res> {
  factory _$$AigcPresetsEffectImplCopyWith(_$AigcPresetsEffectImpl value,
          $Res Function(_$AigcPresetsEffectImpl) then) =
      __$$AigcPresetsEffectImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'effect_code') String effectCode,
      String name,
      String status,
      @JsonKey(name: 'thumb_url') String? thumbUrl,
      @JsonKey(name: 'photo_url') String? photoUrl,
      @JsonKey(name: 'is_in_use', defaultValue: false) bool isInUse,
      @JsonKey(name: 'error_msg') String? errorMsg});
}

/// @nodoc
class __$$AigcPresetsEffectImplCopyWithImpl<$Res>
    extends _$AigcPresetsEffectCopyWithImpl<$Res, _$AigcPresetsEffectImpl>
    implements _$$AigcPresetsEffectImplCopyWith<$Res> {
  __$$AigcPresetsEffectImplCopyWithImpl(_$AigcPresetsEffectImpl _value,
      $Res Function(_$AigcPresetsEffectImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? effectCode = null,
    Object? name = null,
    Object? status = null,
    Object? thumbUrl = freezed,
    Object? photoUrl = freezed,
    Object? isInUse = null,
    Object? errorMsg = freezed,
  }) {
    return _then(_$AigcPresetsEffectImpl(
      effectCode: null == effectCode
          ? _value.effectCode
          : effectCode // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      thumbUrl: freezed == thumbUrl
          ? _value.thumbUrl
          : thumbUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isInUse: null == isInUse
          ? _value.isInUse
          : isInUse // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMsg: freezed == errorMsg
          ? _value.errorMsg
          : errorMsg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcPresetsEffectImpl implements _AigcPresetsEffect {
  const _$AigcPresetsEffectImpl(
      {@JsonKey(name: 'effect_code') required this.effectCode,
      required this.name,
      required this.status,
      @JsonKey(name: 'thumb_url') this.thumbUrl,
      @JsonKey(name: 'photo_url') this.photoUrl,
      @JsonKey(name: 'is_in_use', defaultValue: false) required this.isInUse,
      @JsonKey(name: 'error_msg') this.errorMsg});

  factory _$AigcPresetsEffectImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcPresetsEffectImplFromJson(json);

  @override
  @JsonKey(name: 'effect_code')
  final String effectCode;
  @override
  final String name;
  @override
  final String status;
  @override
  @JsonKey(name: 'thumb_url')
  final String? thumbUrl;
  @override
  @JsonKey(name: 'photo_url')
  final String? photoUrl;
  @override
  @JsonKey(name: 'is_in_use', defaultValue: false)
  final bool isInUse;
  @override
  @JsonKey(name: 'error_msg')
  final String? errorMsg;

  @override
  String toString() {
    return 'AigcPresetsEffect(effectCode: $effectCode, name: $name, status: $status, thumbUrl: $thumbUrl, photoUrl: $photoUrl, isInUse: $isInUse, errorMsg: $errorMsg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPresetsEffectImpl &&
            (identical(other.effectCode, effectCode) ||
                other.effectCode == effectCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.thumbUrl, thumbUrl) ||
                other.thumbUrl == thumbUrl) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.isInUse, isInUse) || other.isInUse == isInUse) &&
            (identical(other.errorMsg, errorMsg) ||
                other.errorMsg == errorMsg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, effectCode, name, status,
      thumbUrl, photoUrl, isInUse, errorMsg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPresetsEffectImplCopyWith<_$AigcPresetsEffectImpl> get copyWith =>
      __$$AigcPresetsEffectImplCopyWithImpl<_$AigcPresetsEffectImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcPresetsEffectImplToJson(
      this,
    );
  }
}

abstract class _AigcPresetsEffect implements AigcPresetsEffect {
  const factory _AigcPresetsEffect(
          {@JsonKey(name: 'effect_code') required final String effectCode,
          required final String name,
          required final String status,
          @JsonKey(name: 'thumb_url') final String? thumbUrl,
          @JsonKey(name: 'photo_url') final String? photoUrl,
          @JsonKey(name: 'is_in_use', defaultValue: false)
          required final bool isInUse,
          @JsonKey(name: 'error_msg') final String? errorMsg}) =
      _$AigcPresetsEffectImpl;

  factory _AigcPresetsEffect.fromJson(Map<String, dynamic> json) =
      _$AigcPresetsEffectImpl.fromJson;

  @override
  @JsonKey(name: 'effect_code')
  String get effectCode;
  @override
  String get name;
  @override
  String get status;
  @override
  @JsonKey(name: 'thumb_url')
  String? get thumbUrl;
  @override
  @JsonKey(name: 'photo_url')
  String? get photoUrl;
  @override
  @JsonKey(name: 'is_in_use', defaultValue: false)
  bool get isInUse;
  @override
  @JsonKey(name: 'error_msg')
  String? get errorMsg;
  @override
  @JsonKey(ignore: true)
  _$$AigcPresetsEffectImplCopyWith<_$AigcPresetsEffectImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
