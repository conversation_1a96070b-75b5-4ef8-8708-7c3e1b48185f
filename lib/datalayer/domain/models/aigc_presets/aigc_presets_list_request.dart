// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_presets_list_request.freezed.dart';
part 'aigc_presets_list_request.g.dart';

@freezed
class AigcPresetsListRequest with _$AigcPresetsListRequest {
  const factory AigcPresetsListRequest({
    required int page,
    @JsonKey(name: 'page_size') required int pageSize,
    required String scene,
  }) = _AigcPresetsListRequest;

  factory AigcPresetsListRequest.fromJson(Map<String, dynamic> json) =>
      _$AigcPresetsListRequestFromJson(json);
}
