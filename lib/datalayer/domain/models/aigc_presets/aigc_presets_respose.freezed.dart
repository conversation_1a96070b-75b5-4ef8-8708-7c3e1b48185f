// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_presets_respose.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcPcPresetsResponse _$AigcPcPresetsResponseFromJson(
    Map<String, dynamic> json) {
  return _AigcPcPresetsResponse.fromJson(json);
}

/// @nodoc
mixin _$AigcPcPresetsResponse {
  List<AigcPcPresetsDetailResponse> get presets =>
      throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  @JsonKey(name: 'page_size')
  int get pageSize => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcPcPresetsResponseCopyWith<AigcPcPresetsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPcPresetsResponseCopyWith<$Res> {
  factory $AigcPcPresetsResponseCopyWith(AigcPcPresetsResponse value,
          $Res Function(AigcPcPresetsResponse) then) =
      _$AigcPcPresetsResponseCopyWithImpl<$Res, AigcPcPresetsResponse>;
  @useResult
  $Res call(
      {List<AigcPcPresetsDetailResponse> presets,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class _$AigcPcPresetsResponseCopyWithImpl<$Res,
        $Val extends AigcPcPresetsResponse>
    implements $AigcPcPresetsResponseCopyWith<$Res> {
  _$AigcPcPresetsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presets = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_value.copyWith(
      presets: null == presets
          ? _value.presets
          : presets // ignore: cast_nullable_to_non_nullable
              as List<AigcPcPresetsDetailResponse>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPcPresetsResponseImplCopyWith<$Res>
    implements $AigcPcPresetsResponseCopyWith<$Res> {
  factory _$$AigcPcPresetsResponseImplCopyWith(
          _$AigcPcPresetsResponseImpl value,
          $Res Function(_$AigcPcPresetsResponseImpl) then) =
      __$$AigcPcPresetsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AigcPcPresetsDetailResponse> presets,
      int total,
      int page,
      @JsonKey(name: 'page_size') int pageSize});
}

/// @nodoc
class __$$AigcPcPresetsResponseImplCopyWithImpl<$Res>
    extends _$AigcPcPresetsResponseCopyWithImpl<$Res,
        _$AigcPcPresetsResponseImpl>
    implements _$$AigcPcPresetsResponseImplCopyWith<$Res> {
  __$$AigcPcPresetsResponseImplCopyWithImpl(_$AigcPcPresetsResponseImpl _value,
      $Res Function(_$AigcPcPresetsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presets = null,
    Object? total = null,
    Object? page = null,
    Object? pageSize = null,
  }) {
    return _then(_$AigcPcPresetsResponseImpl(
      presets: null == presets
          ? _value._presets
          : presets // ignore: cast_nullable_to_non_nullable
              as List<AigcPcPresetsDetailResponse>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcPcPresetsResponseImpl implements _AigcPcPresetsResponse {
  const _$AigcPcPresetsResponseImpl(
      {required final List<AigcPcPresetsDetailResponse> presets,
      required this.total,
      required this.page,
      @JsonKey(name: 'page_size') required this.pageSize})
      : _presets = presets;

  factory _$AigcPcPresetsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcPcPresetsResponseImplFromJson(json);

  final List<AigcPcPresetsDetailResponse> _presets;
  @override
  List<AigcPcPresetsDetailResponse> get presets {
    if (_presets is EqualUnmodifiableListView) return _presets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_presets);
  }

  @override
  final int total;
  @override
  final int page;
  @override
  @JsonKey(name: 'page_size')
  final int pageSize;

  @override
  String toString() {
    return 'AigcPcPresetsResponse(presets: $presets, total: $total, page: $page, pageSize: $pageSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPcPresetsResponseImpl &&
            const DeepCollectionEquality().equals(other._presets, _presets) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_presets), total, page, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPcPresetsResponseImplCopyWith<_$AigcPcPresetsResponseImpl>
      get copyWith => __$$AigcPcPresetsResponseImplCopyWithImpl<
          _$AigcPcPresetsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcPcPresetsResponseImplToJson(
      this,
    );
  }
}

abstract class _AigcPcPresetsResponse implements AigcPcPresetsResponse {
  const factory _AigcPcPresetsResponse(
          {required final List<AigcPcPresetsDetailResponse> presets,
          required final int total,
          required final int page,
          @JsonKey(name: 'page_size') required final int pageSize}) =
      _$AigcPcPresetsResponseImpl;

  factory _AigcPcPresetsResponse.fromJson(Map<String, dynamic> json) =
      _$AigcPcPresetsResponseImpl.fromJson;

  @override
  List<AigcPcPresetsDetailResponse> get presets;
  @override
  int get total;
  @override
  int get page;
  @override
  @JsonKey(name: 'page_size')
  int get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$AigcPcPresetsResponseImplCopyWith<_$AigcPcPresetsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
