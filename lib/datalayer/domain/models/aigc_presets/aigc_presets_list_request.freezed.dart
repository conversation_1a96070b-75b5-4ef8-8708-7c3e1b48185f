// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_presets_list_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcPresetsListRequest _$AigcPresetsListRequestFromJson(
    Map<String, dynamic> json) {
  return _AigcPresetsListRequest.fromJson(json);
}

/// @nodoc
mixin _$AigcPresetsListRequest {
  int get page => throw _privateConstructorUsedError;
  @JsonKey(name: 'page_size')
  int get pageSize => throw _privateConstructorUsedError;
  String get scene => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcPresetsListRequestCopyWith<AigcPresetsListRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPresetsListRequestCopyWith<$Res> {
  factory $AigcPresetsListRequestCopyWith(AigcPresetsListRequest value,
          $Res Function(AigcPresetsListRequest) then) =
      _$AigcPresetsListRequestCopyWithImpl<$Res, AigcPresetsListRequest>;
  @useResult
  $Res call({int page, @JsonKey(name: 'page_size') int pageSize, String scene});
}

/// @nodoc
class _$AigcPresetsListRequestCopyWithImpl<$Res,
        $Val extends AigcPresetsListRequest>
    implements $AigcPresetsListRequestCopyWith<$Res> {
  _$AigcPresetsListRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? pageSize = null,
    Object? scene = null,
  }) {
    return _then(_value.copyWith(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      scene: null == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPresetsListRequestImplCopyWith<$Res>
    implements $AigcPresetsListRequestCopyWith<$Res> {
  factory _$$AigcPresetsListRequestImplCopyWith(
          _$AigcPresetsListRequestImpl value,
          $Res Function(_$AigcPresetsListRequestImpl) then) =
      __$$AigcPresetsListRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int page, @JsonKey(name: 'page_size') int pageSize, String scene});
}

/// @nodoc
class __$$AigcPresetsListRequestImplCopyWithImpl<$Res>
    extends _$AigcPresetsListRequestCopyWithImpl<$Res,
        _$AigcPresetsListRequestImpl>
    implements _$$AigcPresetsListRequestImplCopyWith<$Res> {
  __$$AigcPresetsListRequestImplCopyWithImpl(
      _$AigcPresetsListRequestImpl _value,
      $Res Function(_$AigcPresetsListRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? pageSize = null,
    Object? scene = null,
  }) {
    return _then(_$AigcPresetsListRequestImpl(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      scene: null == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcPresetsListRequestImpl implements _AigcPresetsListRequest {
  const _$AigcPresetsListRequestImpl(
      {required this.page,
      @JsonKey(name: 'page_size') required this.pageSize,
      required this.scene});

  factory _$AigcPresetsListRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcPresetsListRequestImplFromJson(json);

  @override
  final int page;
  @override
  @JsonKey(name: 'page_size')
  final int pageSize;
  @override
  final String scene;

  @override
  String toString() {
    return 'AigcPresetsListRequest(page: $page, pageSize: $pageSize, scene: $scene)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPresetsListRequestImpl &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.scene, scene) || other.scene == scene));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, page, pageSize, scene);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPresetsListRequestImplCopyWith<_$AigcPresetsListRequestImpl>
      get copyWith => __$$AigcPresetsListRequestImplCopyWithImpl<
          _$AigcPresetsListRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcPresetsListRequestImplToJson(
      this,
    );
  }
}

abstract class _AigcPresetsListRequest implements AigcPresetsListRequest {
  const factory _AigcPresetsListRequest(
      {required final int page,
      @JsonKey(name: 'page_size') required final int pageSize,
      required final String scene}) = _$AigcPresetsListRequestImpl;

  factory _AigcPresetsListRequest.fromJson(Map<String, dynamic> json) =
      _$AigcPresetsListRequestImpl.fromJson;

  @override
  int get page;
  @override
  @JsonKey(name: 'page_size')
  int get pageSize;
  @override
  String get scene;
  @override
  @JsonKey(ignore: true)
  _$$AigcPresetsListRequestImplCopyWith<_$AigcPresetsListRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
