// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_presets_detail_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcPcPresetsDetailResponse _$AigcPcPresetsDetailResponseFromJson(
    Map<String, dynamic> json) {
  return _AigcPcPresetsDetailResponse.fromJson(json);
}

/// @nodoc
mixin _$AigcPcPresetsDetailResponse {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  List<AigcPresetsGroup> get effects => throw _privateConstructorUsedError;
  @JsonKey(name: 'create_at')
  int get createAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'update_at')
  int get updateAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcPcPresetsDetailResponseCopyWith<AigcPcPresetsDetailResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPcPresetsDetailResponseCopyWith<$Res> {
  factory $AigcPcPresetsDetailResponseCopyWith(
          AigcPcPresetsDetailResponse value,
          $Res Function(AigcPcPresetsDetailResponse) then) =
      _$AigcPcPresetsDetailResponseCopyWithImpl<$Res,
          AigcPcPresetsDetailResponse>;
  @useResult
  $Res call(
      {String id,
      String name,
      String status,
      List<AigcPresetsGroup> effects,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class _$AigcPcPresetsDetailResponseCopyWithImpl<$Res,
        $Val extends AigcPcPresetsDetailResponse>
    implements $AigcPcPresetsDetailResponseCopyWith<$Res> {
  _$AigcPcPresetsDetailResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? status = null,
    Object? effects = null,
    Object? createAt = null,
    Object? updateAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      effects: null == effects
          ? _value.effects
          : effects // ignore: cast_nullable_to_non_nullable
              as List<AigcPresetsGroup>,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPcPresetsDetailResponseImplCopyWith<$Res>
    implements $AigcPcPresetsDetailResponseCopyWith<$Res> {
  factory _$$AigcPcPresetsDetailResponseImplCopyWith(
          _$AigcPcPresetsDetailResponseImpl value,
          $Res Function(_$AigcPcPresetsDetailResponseImpl) then) =
      __$$AigcPcPresetsDetailResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String status,
      List<AigcPresetsGroup> effects,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt});
}

/// @nodoc
class __$$AigcPcPresetsDetailResponseImplCopyWithImpl<$Res>
    extends _$AigcPcPresetsDetailResponseCopyWithImpl<$Res,
        _$AigcPcPresetsDetailResponseImpl>
    implements _$$AigcPcPresetsDetailResponseImplCopyWith<$Res> {
  __$$AigcPcPresetsDetailResponseImplCopyWithImpl(
      _$AigcPcPresetsDetailResponseImpl _value,
      $Res Function(_$AigcPcPresetsDetailResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? status = null,
    Object? effects = null,
    Object? createAt = null,
    Object? updateAt = null,
  }) {
    return _then(_$AigcPcPresetsDetailResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      effects: null == effects
          ? _value._effects
          : effects // ignore: cast_nullable_to_non_nullable
              as List<AigcPresetsGroup>,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcPcPresetsDetailResponseImpl
    implements _AigcPcPresetsDetailResponse {
  const _$AigcPcPresetsDetailResponseImpl(
      {required this.id,
      required this.name,
      required this.status,
      required final List<AigcPresetsGroup> effects,
      @JsonKey(name: 'create_at') required this.createAt,
      @JsonKey(name: 'update_at') required this.updateAt})
      : _effects = effects;

  factory _$AigcPcPresetsDetailResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AigcPcPresetsDetailResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String status;
  final List<AigcPresetsGroup> _effects;
  @override
  List<AigcPresetsGroup> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  @override
  @JsonKey(name: 'create_at')
  final int createAt;
  @override
  @JsonKey(name: 'update_at')
  final int updateAt;

  @override
  String toString() {
    return 'AigcPcPresetsDetailResponse(id: $id, name: $name, status: $status, effects: $effects, createAt: $createAt, updateAt: $updateAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPcPresetsDetailResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.updateAt, updateAt) ||
                other.updateAt == updateAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, status,
      const DeepCollectionEquality().hash(_effects), createAt, updateAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPcPresetsDetailResponseImplCopyWith<_$AigcPcPresetsDetailResponseImpl>
      get copyWith => __$$AigcPcPresetsDetailResponseImplCopyWithImpl<
          _$AigcPcPresetsDetailResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcPcPresetsDetailResponseImplToJson(
      this,
    );
  }
}

abstract class _AigcPcPresetsDetailResponse
    implements AigcPcPresetsDetailResponse {
  const factory _AigcPcPresetsDetailResponse(
          {required final String id,
          required final String name,
          required final String status,
          required final List<AigcPresetsGroup> effects,
          @JsonKey(name: 'create_at') required final int createAt,
          @JsonKey(name: 'update_at') required final int updateAt}) =
      _$AigcPcPresetsDetailResponseImpl;

  factory _AigcPcPresetsDetailResponse.fromJson(Map<String, dynamic> json) =
      _$AigcPcPresetsDetailResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get status;
  @override
  List<AigcPresetsGroup> get effects;
  @override
  @JsonKey(name: 'create_at')
  int get createAt;
  @override
  @JsonKey(name: 'update_at')
  int get updateAt;
  @override
  @JsonKey(ignore: true)
  _$$AigcPcPresetsDetailResponseImplCopyWith<_$AigcPcPresetsDetailResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
