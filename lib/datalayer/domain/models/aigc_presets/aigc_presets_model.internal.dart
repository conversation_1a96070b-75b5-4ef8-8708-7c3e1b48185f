import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.dart';

/// 内部预设模型
extension AigcPresetsModelInternal on AigcPresetsModel {
  /// 内置预设的 id
  static const String internalAIGCPresetId = 'internal_preset';

  static const String internalAIGCEffectId = 'internal_effect';

  static AigcPresetsModel generateInternalPresetModel(
      String thumbUrl, String photoUrl) {
    return AigcPresetsModel(
      id: internalAIGCPresetId,
      name: "原图增强",
      status: "completed",
      createAt: DateTime.now().millisecondsSinceEpoch,
      updateAt: DateTime.now().millisecondsSinceEpoch,
      effects: [
        AigcPresetsEffect(
          effectCode: internalAIGCEffectId,
          name: "原图增强",
          thumbUrl: thumbUrl,
          photoUrl: photoUrl,
          status: "completed",
          isInUse: false,
        ),
      ],
    );
  }
}

extension AigcPresetsEffectInternal on AigcPresetsEffect {
  bool get isInternal =>
      effectCode == AigcPresetsModelInternal.internalAIGCEffectId;
}
