// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_presets_effect.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcPresetsEffectImpl _$$AigcPresetsEffectImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcPresetsEffectImpl(
      effectCode: json['effect_code'] as String,
      name: json['name'] as String,
      status: json['status'] as String,
      thumbUrl: json['thumb_url'] as String?,
      photoUrl: json['photo_url'] as String?,
      isInUse: json['is_in_use'] as bool? ?? false,
      errorMsg: json['error_msg'] as String?,
    );

Map<String, dynamic> _$$AigcPresetsEffectImplToJson(
        _$AigcPresetsEffectImpl instance) =>
    <String, dynamic>{
      'effect_code': instance.effectCode,
      'name': instance.name,
      'status': instance.status,
      'thumb_url': instance.thumbUrl,
      'photo_url': instance.photoUrl,
      'is_in_use': instance.isInUse,
      'error_msg': instance.errorMsg,
    };
