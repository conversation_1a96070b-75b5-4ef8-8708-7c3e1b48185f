// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_presets_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcPresetsModel _$AigcPresetsModelFromJson(Map<String, dynamic> json) {
  return _AigcPresetsModel.fromJson(json);
}

/// @nodoc
mixin _$AigcPresetsModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'create_at')
  int get createAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'update_at')
  int get updateAt => throw _privateConstructorUsedError;
  List<AigcPresetsEffect> get effects => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcPresetsModelCopyWith<AigcPresetsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPresetsModelCopyWith<$Res> {
  factory $AigcPresetsModelCopyWith(
          AigcPresetsModel value, $Res Function(AigcPresetsModel) then) =
      _$AigcPresetsModelCopyWithImpl<$Res, AigcPresetsModel>;
  @useResult
  $Res call(
      {String id,
      String name,
      String status,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt,
      List<AigcPresetsEffect> effects});
}

/// @nodoc
class _$AigcPresetsModelCopyWithImpl<$Res, $Val extends AigcPresetsModel>
    implements $AigcPresetsModelCopyWith<$Res> {
  _$AigcPresetsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? status = null,
    Object? createAt = null,
    Object? updateAt = null,
    Object? effects = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
      effects: null == effects
          ? _value.effects
          : effects // ignore: cast_nullable_to_non_nullable
              as List<AigcPresetsEffect>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPresetsModelImplCopyWith<$Res>
    implements $AigcPresetsModelCopyWith<$Res> {
  factory _$$AigcPresetsModelImplCopyWith(_$AigcPresetsModelImpl value,
          $Res Function(_$AigcPresetsModelImpl) then) =
      __$$AigcPresetsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String status,
      @JsonKey(name: 'create_at') int createAt,
      @JsonKey(name: 'update_at') int updateAt,
      List<AigcPresetsEffect> effects});
}

/// @nodoc
class __$$AigcPresetsModelImplCopyWithImpl<$Res>
    extends _$AigcPresetsModelCopyWithImpl<$Res, _$AigcPresetsModelImpl>
    implements _$$AigcPresetsModelImplCopyWith<$Res> {
  __$$AigcPresetsModelImplCopyWithImpl(_$AigcPresetsModelImpl _value,
      $Res Function(_$AigcPresetsModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? status = null,
    Object? createAt = null,
    Object? updateAt = null,
    Object? effects = null,
  }) {
    return _then(_$AigcPresetsModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createAt: null == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int,
      updateAt: null == updateAt
          ? _value.updateAt
          : updateAt // ignore: cast_nullable_to_non_nullable
              as int,
      effects: null == effects
          ? _value._effects
          : effects // ignore: cast_nullable_to_non_nullable
              as List<AigcPresetsEffect>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcPresetsModelImpl implements _AigcPresetsModel {
  const _$AigcPresetsModelImpl(
      {required this.id,
      required this.name,
      required this.status,
      @JsonKey(name: 'create_at') required this.createAt,
      @JsonKey(name: 'update_at') required this.updateAt,
      required final List<AigcPresetsEffect> effects})
      : _effects = effects;

  factory _$AigcPresetsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcPresetsModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String status;
  @override
  @JsonKey(name: 'create_at')
  final int createAt;
  @override
  @JsonKey(name: 'update_at')
  final int updateAt;
  final List<AigcPresetsEffect> _effects;
  @override
  List<AigcPresetsEffect> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  @override
  String toString() {
    return 'AigcPresetsModel(id: $id, name: $name, status: $status, createAt: $createAt, updateAt: $updateAt, effects: $effects)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPresetsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.updateAt, updateAt) ||
                other.updateAt == updateAt) &&
            const DeepCollectionEquality().equals(other._effects, _effects));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, status, createAt,
      updateAt, const DeepCollectionEquality().hash(_effects));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPresetsModelImplCopyWith<_$AigcPresetsModelImpl> get copyWith =>
      __$$AigcPresetsModelImplCopyWithImpl<_$AigcPresetsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcPresetsModelImplToJson(
      this,
    );
  }
}

abstract class _AigcPresetsModel implements AigcPresetsModel {
  const factory _AigcPresetsModel(
      {required final String id,
      required final String name,
      required final String status,
      @JsonKey(name: 'create_at') required final int createAt,
      @JsonKey(name: 'update_at') required final int updateAt,
      required final List<AigcPresetsEffect> effects}) = _$AigcPresetsModelImpl;

  factory _AigcPresetsModel.fromJson(Map<String, dynamic> json) =
      _$AigcPresetsModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get status;
  @override
  @JsonKey(name: 'create_at')
  int get createAt;
  @override
  @JsonKey(name: 'update_at')
  int get updateAt;
  @override
  List<AigcPresetsEffect> get effects;
  @override
  @JsonKey(ignore: true)
  _$$AigcPresetsModelImplCopyWith<_$AigcPresetsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
