// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_group.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';

part 'aigc_presets_detail_response.freezed.dart';
part 'aigc_presets_detail_response.g.dart';

@freezed
class AigcPcPresetsDetailResponse with _$AigcPcPresetsDetailResponse {
  const factory AigcPcPresetsDetailResponse({
    required String id,
    required String name,
    required String status,
    required List<AigcPresetsGroup> effects,
    @Json<PERSON>ey(name: 'create_at') required int createAt,
    @JsonKey(name: 'update_at') required int updateAt,
  }) = _AigcPcPresetsDetailResponse;

  factory AigcPcPresetsDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$AigcPcPresetsDetailResponseFromJson(json);
}

extension PresetsExtension on AigcPcPresetsDetailResponse {
  /// 获取主题预设的缩略图URL
  String? getThumbnailUrl() {
    if (effects.isEmpty) {
      return null;
    }
    // 获取第一个效果组的缩略图
    final firstGroup = effects.first;
    if (firstGroup.effectList.isEmpty) {
      return null;
    }
    // 获取第一个效果的缩略图
    return firstGroup.effectList.first.thumbUrl;
  }

  /// 获取当前预设中任意不为空的封面图
  String? findUseableThumbnailUrl() {
    if (effects.isEmpty) {
      return null;
    }
    for (var group in effects) {
      for (var effect in group.effectList) {
        if (effect.thumbUrl != null && effect.thumbUrl!.isNotEmpty) {
          return effect.thumbUrl;
        }
      }
    }
    return null;
  }

  /// 检查当前预设中是否有任何效果处于使用中
  bool hasAnyEffectInUsage() {
    // 检查是否有任何效果处于使用中
    return effects
        .any((group) => group.effectList.any((effect) => effect.isInUse));
  }

  /// 获取当前预设中处于使用中的效果数量
  int getEffectInUsageCount() {
    // 计算处于使用中的效果数量
    return effects.fold(
        0,
        (count, group) =>
            count + group.effectList.where((effect) => effect.isInUse).length);
  }

  /// 当前预设是否处于运行中状态
  bool isInRunningStatus() {
    // 检查预设状态是否为运行中
    return status == AigcRequestConst.running;
  }
}

/*
{
   "id": "67f88a1e0579e2cec8c15afa",//主题预设ID
    "name":"牧场少年",//主题预设名称
    "status":"completed",
    "effects":[
      {
        "group_id":"",//效果组1
        "requirement_supplement":"",// 创意补充
        "effect_list":[
            {
              "effect_code":"xxx",// 预设效果图唯一编号
              "is_in_use":true,//当前预设是否可用
              "name":"01",//效果图名称
              "status":"completed",//状态
              "thumb_url": "xxx", //缩略图url
              "photo_url":"xxx" // 效果图url
            },
            {
              "effect_code":"xxx",
              "name":"02",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"03",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"04",
              "is_in_use":true,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"05",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"06",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"07",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"08",
              "is_in_use":true,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"09",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
        ],
      "create_at": 1749192432, // 效果组创建时间
      "update_at":1749192432 // 效果组更新时间
      },
      {
        "group_id":"",//效果组2
        "requirement_supplement":"",// 创意补充
        "effect_list":[
            {
              "effect_code":"xxx",// 预设效果图唯一编号
              "is_in_use":true,//当前预设是否可用
              "name":"10",//效果图名称
              "status":"completed",//状态
              "thumb_url": "xxx", //缩略图url
              "photo_url":"xxx" // 效果图url
            },
            {
              "effect_code":"xxx",
              "name":"11",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            },
            {
              "effect_code":"xxx",
              "name":"12",
              "is_in_use":false,
              "status":"completed",//状态
              "thumb_url": "xxx",
              "photo_url":"xxx"
            }
        ],
      "create_at": 1749192432, // 效果组创建时间
      "update_at":1749192432 // 效果组更新时间
      }
    ],
  "create_at": 1749192432, // 预设创建时间
  "update_at":1749192432 // 预设更新时间
  }
*/
