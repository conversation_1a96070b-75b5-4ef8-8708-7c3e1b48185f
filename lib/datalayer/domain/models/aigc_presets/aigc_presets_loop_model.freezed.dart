// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_presets_loop_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcPcPresetsLoopModel _$AigcPcPresetsLoopModelFromJson(
    Map<String, dynamic> json) {
  return _AigcPcPresetsLoopModel.fromJson(json);
}

/// @nodoc
mixin _$AigcPcPresetsLoopModel {
  String get id => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcPcPresetsLoopModelCopyWith<AigcPcPresetsLoopModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPcPresetsLoopModelCopyWith<$Res> {
  factory $AigcPcPresetsLoopModelCopyWith(AigcPcPresetsLoopModel value,
          $Res Function(AigcPcPresetsLoopModel) then) =
      _$AigcPcPresetsLoopModelCopyWithImpl<$Res, AigcPcPresetsLoopModel>;
  @useResult
  $Res call({String id, String status});
}

/// @nodoc
class _$AigcPcPresetsLoopModelCopyWithImpl<$Res,
        $Val extends AigcPcPresetsLoopModel>
    implements $AigcPcPresetsLoopModelCopyWith<$Res> {
  _$AigcPcPresetsLoopModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPcPresetsLoopModelImplCopyWith<$Res>
    implements $AigcPcPresetsLoopModelCopyWith<$Res> {
  factory _$$AigcPcPresetsLoopModelImplCopyWith(
          _$AigcPcPresetsLoopModelImpl value,
          $Res Function(_$AigcPcPresetsLoopModelImpl) then) =
      __$$AigcPcPresetsLoopModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String status});
}

/// @nodoc
class __$$AigcPcPresetsLoopModelImplCopyWithImpl<$Res>
    extends _$AigcPcPresetsLoopModelCopyWithImpl<$Res,
        _$AigcPcPresetsLoopModelImpl>
    implements _$$AigcPcPresetsLoopModelImplCopyWith<$Res> {
  __$$AigcPcPresetsLoopModelImplCopyWithImpl(
      _$AigcPcPresetsLoopModelImpl _value,
      $Res Function(_$AigcPcPresetsLoopModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? status = null,
  }) {
    return _then(_$AigcPcPresetsLoopModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcPcPresetsLoopModelImpl implements _AigcPcPresetsLoopModel {
  const _$AigcPcPresetsLoopModelImpl({required this.id, required this.status});

  factory _$AigcPcPresetsLoopModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcPcPresetsLoopModelImplFromJson(json);

  @override
  final String id;
  @override
  final String status;

  @override
  String toString() {
    return 'AigcPcPresetsLoopModel(id: $id, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPcPresetsLoopModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPcPresetsLoopModelImplCopyWith<_$AigcPcPresetsLoopModelImpl>
      get copyWith => __$$AigcPcPresetsLoopModelImplCopyWithImpl<
          _$AigcPcPresetsLoopModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcPcPresetsLoopModelImplToJson(
      this,
    );
  }
}

abstract class _AigcPcPresetsLoopModel implements AigcPcPresetsLoopModel {
  const factory _AigcPcPresetsLoopModel(
      {required final String id,
      required final String status}) = _$AigcPcPresetsLoopModelImpl;

  factory _AigcPcPresetsLoopModel.fromJson(Map<String, dynamic> json) =
      _$AigcPcPresetsLoopModelImpl.fromJson;

  @override
  String get id;
  @override
  String get status;
  @override
  @JsonKey(ignore: true)
  _$$AigcPcPresetsLoopModelImplCopyWith<_$AigcPcPresetsLoopModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
