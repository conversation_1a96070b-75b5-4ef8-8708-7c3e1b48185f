// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

import 'ops_operation_model.dart';

part 'ops_operation_response.freezed.dart';
part 'ops_operation_response.g.dart';

/// 运营弹窗响应模型
@freezed
class OpsOperationResponse with _$OpsOperationResponse {
  const factory OpsOperationResponse({
    @Json<PERSON>ey(name: 'new_user_gift')
    @Default([])
    List<OpsOperationModel> newUserGift,
    @JsonKey(name: 'wechat_gift')
    @Default([])
    List<OpsOperationModel> wechatGift,
    @Json<PERSON>ey(name: 'version_introduce')
    @Default([])
    List<OpsOperationModel> versionIntro,
  }) = _OpsOperationResponse;
  const OpsOperationResponse._();

  factory OpsOperationResponse.fromJson(Map<String, dynamic> json) =>
      _$OpsOperationResponseFrom<PERSON>son(json);
}
