// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ops_operation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OpsOperationModelImpl _$$OpsOperationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OpsOperationModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      period: OperationPeriod.fromJson(json['period'] as Map<String, dynamic>),
      activities: (json['activities'] as List<dynamic>)
          .map((e) => OperationActivity.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$OpsOperationModelImplToJson(
        _$OpsOperationModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'period': instance.period,
      'activities': instance.activities,
    };
