// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'operation_activity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OperationActivity _$OperationActivityFromJson(Map<String, dynamic> json) {
  return _OperationActivity.fromJson(json);
}

/// @nodoc
mixin _$OperationActivity {
  String get id => throw _privateConstructorUsedError;
  String get pid => throw _privateConstructorUsedError;
  String get rootId => throw _privateConstructorUsedError;
  String get trackId => throw _privateConstructorUsedError;
  String get fieldCode => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  OperationPeriod get period => throw _privateConstructorUsedError;
  String? get tag => throw _privateConstructorUsedError;
  int? get count =>
      throw _privateConstructorUsedError; // 可选字段 - 企业微信礼包相关 - 微信图片对象
  OperationUrlResource? get wechartUrl =>
      throw _privateConstructorUsedError; // 权益展示名字
  String? get displayName => throw _privateConstructorUsedError; // 标题
  String? get title => throw _privateConstructorUsedError; // 自我介绍
  String? get selfIntroduction => throw _privateConstructorUsedError; // 扫码提示
  String? get scanCodeTip =>
      throw _privateConstructorUsedError; // 可选字段 - 新人礼包相关
  String? get description => throw _privateConstructorUsedError;
  OperationUrlResource? get giftUrl => throw _privateConstructorUsedError;
  OperationUrlResource? get successUrl =>
      throw _privateConstructorUsedError; // 可选字段 - 版本介绍，以后再增加图片视频可复用
  OperationUrlResource? get image => throw _privateConstructorUsedError;
  OperationUrlResource? get video => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OperationActivityCopyWith<OperationActivity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OperationActivityCopyWith<$Res> {
  factory $OperationActivityCopyWith(
          OperationActivity value, $Res Function(OperationActivity) then) =
      _$OperationActivityCopyWithImpl<$Res, OperationActivity>;
  @useResult
  $Res call(
      {String id,
      String pid,
      String rootId,
      String trackId,
      String fieldCode,
      String name,
      OperationPeriod period,
      String? tag,
      int? count,
      OperationUrlResource? wechartUrl,
      String? displayName,
      String? title,
      String? selfIntroduction,
      String? scanCodeTip,
      String? description,
      OperationUrlResource? giftUrl,
      OperationUrlResource? successUrl,
      OperationUrlResource? image,
      OperationUrlResource? video});

  $OperationPeriodCopyWith<$Res> get period;
  $OperationUrlResourceCopyWith<$Res>? get wechartUrl;
  $OperationUrlResourceCopyWith<$Res>? get giftUrl;
  $OperationUrlResourceCopyWith<$Res>? get successUrl;
  $OperationUrlResourceCopyWith<$Res>? get image;
  $OperationUrlResourceCopyWith<$Res>? get video;
}

/// @nodoc
class _$OperationActivityCopyWithImpl<$Res, $Val extends OperationActivity>
    implements $OperationActivityCopyWith<$Res> {
  _$OperationActivityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? pid = null,
    Object? rootId = null,
    Object? trackId = null,
    Object? fieldCode = null,
    Object? name = null,
    Object? period = null,
    Object? tag = freezed,
    Object? count = freezed,
    Object? wechartUrl = freezed,
    Object? displayName = freezed,
    Object? title = freezed,
    Object? selfIntroduction = freezed,
    Object? scanCodeTip = freezed,
    Object? description = freezed,
    Object? giftUrl = freezed,
    Object? successUrl = freezed,
    Object? image = freezed,
    Object? video = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      pid: null == pid
          ? _value.pid
          : pid // ignore: cast_nullable_to_non_nullable
              as String,
      rootId: null == rootId
          ? _value.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String,
      trackId: null == trackId
          ? _value.trackId
          : trackId // ignore: cast_nullable_to_non_nullable
              as String,
      fieldCode: null == fieldCode
          ? _value.fieldCode
          : fieldCode // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as OperationPeriod,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      wechartUrl: freezed == wechartUrl
          ? _value.wechartUrl
          : wechartUrl // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      selfIntroduction: freezed == selfIntroduction
          ? _value.selfIntroduction
          : selfIntroduction // ignore: cast_nullable_to_non_nullable
              as String?,
      scanCodeTip: freezed == scanCodeTip
          ? _value.scanCodeTip
          : scanCodeTip // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      giftUrl: freezed == giftUrl
          ? _value.giftUrl
          : giftUrl // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      successUrl: freezed == successUrl
          ? _value.successUrl
          : successUrl // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      video: freezed == video
          ? _value.video
          : video // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationPeriodCopyWith<$Res> get period {
    return $OperationPeriodCopyWith<$Res>(_value.period, (value) {
      return _then(_value.copyWith(period: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationUrlResourceCopyWith<$Res>? get wechartUrl {
    if (_value.wechartUrl == null) {
      return null;
    }

    return $OperationUrlResourceCopyWith<$Res>(_value.wechartUrl!, (value) {
      return _then(_value.copyWith(wechartUrl: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationUrlResourceCopyWith<$Res>? get giftUrl {
    if (_value.giftUrl == null) {
      return null;
    }

    return $OperationUrlResourceCopyWith<$Res>(_value.giftUrl!, (value) {
      return _then(_value.copyWith(giftUrl: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationUrlResourceCopyWith<$Res>? get successUrl {
    if (_value.successUrl == null) {
      return null;
    }

    return $OperationUrlResourceCopyWith<$Res>(_value.successUrl!, (value) {
      return _then(_value.copyWith(successUrl: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationUrlResourceCopyWith<$Res>? get image {
    if (_value.image == null) {
      return null;
    }

    return $OperationUrlResourceCopyWith<$Res>(_value.image!, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationUrlResourceCopyWith<$Res>? get video {
    if (_value.video == null) {
      return null;
    }

    return $OperationUrlResourceCopyWith<$Res>(_value.video!, (value) {
      return _then(_value.copyWith(video: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OperationActivityImplCopyWith<$Res>
    implements $OperationActivityCopyWith<$Res> {
  factory _$$OperationActivityImplCopyWith(_$OperationActivityImpl value,
          $Res Function(_$OperationActivityImpl) then) =
      __$$OperationActivityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String pid,
      String rootId,
      String trackId,
      String fieldCode,
      String name,
      OperationPeriod period,
      String? tag,
      int? count,
      OperationUrlResource? wechartUrl,
      String? displayName,
      String? title,
      String? selfIntroduction,
      String? scanCodeTip,
      String? description,
      OperationUrlResource? giftUrl,
      OperationUrlResource? successUrl,
      OperationUrlResource? image,
      OperationUrlResource? video});

  @override
  $OperationPeriodCopyWith<$Res> get period;
  @override
  $OperationUrlResourceCopyWith<$Res>? get wechartUrl;
  @override
  $OperationUrlResourceCopyWith<$Res>? get giftUrl;
  @override
  $OperationUrlResourceCopyWith<$Res>? get successUrl;
  @override
  $OperationUrlResourceCopyWith<$Res>? get image;
  @override
  $OperationUrlResourceCopyWith<$Res>? get video;
}

/// @nodoc
class __$$OperationActivityImplCopyWithImpl<$Res>
    extends _$OperationActivityCopyWithImpl<$Res, _$OperationActivityImpl>
    implements _$$OperationActivityImplCopyWith<$Res> {
  __$$OperationActivityImplCopyWithImpl(_$OperationActivityImpl _value,
      $Res Function(_$OperationActivityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? pid = null,
    Object? rootId = null,
    Object? trackId = null,
    Object? fieldCode = null,
    Object? name = null,
    Object? period = null,
    Object? tag = freezed,
    Object? count = freezed,
    Object? wechartUrl = freezed,
    Object? displayName = freezed,
    Object? title = freezed,
    Object? selfIntroduction = freezed,
    Object? scanCodeTip = freezed,
    Object? description = freezed,
    Object? giftUrl = freezed,
    Object? successUrl = freezed,
    Object? image = freezed,
    Object? video = freezed,
  }) {
    return _then(_$OperationActivityImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      pid: null == pid
          ? _value.pid
          : pid // ignore: cast_nullable_to_non_nullable
              as String,
      rootId: null == rootId
          ? _value.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String,
      trackId: null == trackId
          ? _value.trackId
          : trackId // ignore: cast_nullable_to_non_nullable
              as String,
      fieldCode: null == fieldCode
          ? _value.fieldCode
          : fieldCode // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as OperationPeriod,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      wechartUrl: freezed == wechartUrl
          ? _value.wechartUrl
          : wechartUrl // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      selfIntroduction: freezed == selfIntroduction
          ? _value.selfIntroduction
          : selfIntroduction // ignore: cast_nullable_to_non_nullable
              as String?,
      scanCodeTip: freezed == scanCodeTip
          ? _value.scanCodeTip
          : scanCodeTip // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      giftUrl: freezed == giftUrl
          ? _value.giftUrl
          : giftUrl // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      successUrl: freezed == successUrl
          ? _value.successUrl
          : successUrl // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      video: freezed == video
          ? _value.video
          : video // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OperationActivityImpl extends _OperationActivity {
  const _$OperationActivityImpl(
      {required this.id,
      required this.pid,
      required this.rootId,
      required this.trackId,
      required this.fieldCode,
      required this.name,
      required this.period,
      this.tag,
      this.count,
      this.wechartUrl,
      this.displayName,
      this.title,
      this.selfIntroduction,
      this.scanCodeTip,
      this.description,
      this.giftUrl,
      this.successUrl,
      this.image,
      this.video})
      : super._();

  factory _$OperationActivityImpl.fromJson(Map<String, dynamic> json) =>
      _$$OperationActivityImplFromJson(json);

  @override
  final String id;
  @override
  final String pid;
  @override
  final String rootId;
  @override
  final String trackId;
  @override
  final String fieldCode;
  @override
  final String name;
  @override
  final OperationPeriod period;
  @override
  final String? tag;
  @override
  final int? count;
// 可选字段 - 企业微信礼包相关 - 微信图片对象
  @override
  final OperationUrlResource? wechartUrl;
// 权益展示名字
  @override
  final String? displayName;
// 标题
  @override
  final String? title;
// 自我介绍
  @override
  final String? selfIntroduction;
// 扫码提示
  @override
  final String? scanCodeTip;
// 可选字段 - 新人礼包相关
  @override
  final String? description;
  @override
  final OperationUrlResource? giftUrl;
  @override
  final OperationUrlResource? successUrl;
// 可选字段 - 版本介绍，以后再增加图片视频可复用
  @override
  final OperationUrlResource? image;
  @override
  final OperationUrlResource? video;

  @override
  String toString() {
    return 'OperationActivity(id: $id, pid: $pid, rootId: $rootId, trackId: $trackId, fieldCode: $fieldCode, name: $name, period: $period, tag: $tag, count: $count, wechartUrl: $wechartUrl, displayName: $displayName, title: $title, selfIntroduction: $selfIntroduction, scanCodeTip: $scanCodeTip, description: $description, giftUrl: $giftUrl, successUrl: $successUrl, image: $image, video: $video)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OperationActivityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.pid, pid) || other.pid == pid) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            (identical(other.trackId, trackId) || other.trackId == trackId) &&
            (identical(other.fieldCode, fieldCode) ||
                other.fieldCode == fieldCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.wechartUrl, wechartUrl) ||
                other.wechartUrl == wechartUrl) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.selfIntroduction, selfIntroduction) ||
                other.selfIntroduction == selfIntroduction) &&
            (identical(other.scanCodeTip, scanCodeTip) ||
                other.scanCodeTip == scanCodeTip) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.giftUrl, giftUrl) || other.giftUrl == giftUrl) &&
            (identical(other.successUrl, successUrl) ||
                other.successUrl == successUrl) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.video, video) || other.video == video));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        pid,
        rootId,
        trackId,
        fieldCode,
        name,
        period,
        tag,
        count,
        wechartUrl,
        displayName,
        title,
        selfIntroduction,
        scanCodeTip,
        description,
        giftUrl,
        successUrl,
        image,
        video
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OperationActivityImplCopyWith<_$OperationActivityImpl> get copyWith =>
      __$$OperationActivityImplCopyWithImpl<_$OperationActivityImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OperationActivityImplToJson(
      this,
    );
  }
}

abstract class _OperationActivity extends OperationActivity {
  const factory _OperationActivity(
      {required final String id,
      required final String pid,
      required final String rootId,
      required final String trackId,
      required final String fieldCode,
      required final String name,
      required final OperationPeriod period,
      final String? tag,
      final int? count,
      final OperationUrlResource? wechartUrl,
      final String? displayName,
      final String? title,
      final String? selfIntroduction,
      final String? scanCodeTip,
      final String? description,
      final OperationUrlResource? giftUrl,
      final OperationUrlResource? successUrl,
      final OperationUrlResource? image,
      final OperationUrlResource? video}) = _$OperationActivityImpl;
  const _OperationActivity._() : super._();

  factory _OperationActivity.fromJson(Map<String, dynamic> json) =
      _$OperationActivityImpl.fromJson;

  @override
  String get id;
  @override
  String get pid;
  @override
  String get rootId;
  @override
  String get trackId;
  @override
  String get fieldCode;
  @override
  String get name;
  @override
  OperationPeriod get period;
  @override
  String? get tag;
  @override
  int? get count;
  @override // 可选字段 - 企业微信礼包相关 - 微信图片对象
  OperationUrlResource? get wechartUrl;
  @override // 权益展示名字
  String? get displayName;
  @override // 标题
  String? get title;
  @override // 自我介绍
  String? get selfIntroduction;
  @override // 扫码提示
  String? get scanCodeTip;
  @override // 可选字段 - 新人礼包相关
  String? get description;
  @override
  OperationUrlResource? get giftUrl;
  @override
  OperationUrlResource? get successUrl;
  @override // 可选字段 - 版本介绍，以后再增加图片视频可复用
  OperationUrlResource? get image;
  @override
  OperationUrlResource? get video;
  @override
  @JsonKey(ignore: true)
  _$$OperationActivityImplCopyWith<_$OperationActivityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
