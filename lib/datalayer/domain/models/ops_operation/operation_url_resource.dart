import 'package:freezed_annotation/freezed_annotation.dart';

part 'operation_url_resource.freezed.dart';
part 'operation_url_resource.g.dart';

/// 运营URL资源
@freezed
class OperationUrlResource with _$OperationUrlResource {
  const factory OperationUrlResource({
    // 截图(可选,视频资源才有)
    OperationUrlResource? screenshot,
    // 图片
    required String url,
    // 大小
    required int size,
    // 过期时间
    required int expire,
    // 宽度
    required int width,
    // 高度
    required int height,
  }) = _OperationUrlResource;
  const OperationUrlResource._();

  factory OperationUrlResource.fromJson(Map<String, dynamic> json) =>
      _$OperationUrlResourceFromJson(json);
}
