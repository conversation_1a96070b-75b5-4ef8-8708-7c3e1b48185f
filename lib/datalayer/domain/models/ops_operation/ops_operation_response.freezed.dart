// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ops_operation_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OpsOperationResponse _$OpsOperationResponseFromJson(Map<String, dynamic> json) {
  return _OpsOperationResponse.fromJson(json);
}

/// @nodoc
mixin _$OpsOperationResponse {
  @JsonKey(name: 'new_user_gift')
  List<OpsOperationModel> get newUserGift => throw _privateConstructorUsedError;
  @JsonKey(name: 'wechat_gift')
  List<OpsOperationModel> get wechatGift => throw _privateConstructorUsedError;
  @JsonKey(name: 'version_introduce')
  List<OpsOperationModel> get versionIntro =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OpsOperationResponseCopyWith<OpsOperationResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OpsOperationResponseCopyWith<$Res> {
  factory $OpsOperationResponseCopyWith(OpsOperationResponse value,
          $Res Function(OpsOperationResponse) then) =
      _$OpsOperationResponseCopyWithImpl<$Res, OpsOperationResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'new_user_gift') List<OpsOperationModel> newUserGift,
      @JsonKey(name: 'wechat_gift') List<OpsOperationModel> wechatGift,
      @JsonKey(name: 'version_introduce')
      List<OpsOperationModel> versionIntro});
}

/// @nodoc
class _$OpsOperationResponseCopyWithImpl<$Res,
        $Val extends OpsOperationResponse>
    implements $OpsOperationResponseCopyWith<$Res> {
  _$OpsOperationResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newUserGift = null,
    Object? wechatGift = null,
    Object? versionIntro = null,
  }) {
    return _then(_value.copyWith(
      newUserGift: null == newUserGift
          ? _value.newUserGift
          : newUserGift // ignore: cast_nullable_to_non_nullable
              as List<OpsOperationModel>,
      wechatGift: null == wechatGift
          ? _value.wechatGift
          : wechatGift // ignore: cast_nullable_to_non_nullable
              as List<OpsOperationModel>,
      versionIntro: null == versionIntro
          ? _value.versionIntro
          : versionIntro // ignore: cast_nullable_to_non_nullable
              as List<OpsOperationModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OpsOperationResponseImplCopyWith<$Res>
    implements $OpsOperationResponseCopyWith<$Res> {
  factory _$$OpsOperationResponseImplCopyWith(_$OpsOperationResponseImpl value,
          $Res Function(_$OpsOperationResponseImpl) then) =
      __$$OpsOperationResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'new_user_gift') List<OpsOperationModel> newUserGift,
      @JsonKey(name: 'wechat_gift') List<OpsOperationModel> wechatGift,
      @JsonKey(name: 'version_introduce')
      List<OpsOperationModel> versionIntro});
}

/// @nodoc
class __$$OpsOperationResponseImplCopyWithImpl<$Res>
    extends _$OpsOperationResponseCopyWithImpl<$Res, _$OpsOperationResponseImpl>
    implements _$$OpsOperationResponseImplCopyWith<$Res> {
  __$$OpsOperationResponseImplCopyWithImpl(_$OpsOperationResponseImpl _value,
      $Res Function(_$OpsOperationResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newUserGift = null,
    Object? wechatGift = null,
    Object? versionIntro = null,
  }) {
    return _then(_$OpsOperationResponseImpl(
      newUserGift: null == newUserGift
          ? _value._newUserGift
          : newUserGift // ignore: cast_nullable_to_non_nullable
              as List<OpsOperationModel>,
      wechatGift: null == wechatGift
          ? _value._wechatGift
          : wechatGift // ignore: cast_nullable_to_non_nullable
              as List<OpsOperationModel>,
      versionIntro: null == versionIntro
          ? _value._versionIntro
          : versionIntro // ignore: cast_nullable_to_non_nullable
              as List<OpsOperationModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OpsOperationResponseImpl extends _OpsOperationResponse {
  const _$OpsOperationResponseImpl(
      {@JsonKey(name: 'new_user_gift')
      final List<OpsOperationModel> newUserGift = const [],
      @JsonKey(name: 'wechat_gift')
      final List<OpsOperationModel> wechatGift = const [],
      @JsonKey(name: 'version_introduce')
      final List<OpsOperationModel> versionIntro = const []})
      : _newUserGift = newUserGift,
        _wechatGift = wechatGift,
        _versionIntro = versionIntro,
        super._();

  factory _$OpsOperationResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OpsOperationResponseImplFromJson(json);

  final List<OpsOperationModel> _newUserGift;
  @override
  @JsonKey(name: 'new_user_gift')
  List<OpsOperationModel> get newUserGift {
    if (_newUserGift is EqualUnmodifiableListView) return _newUserGift;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_newUserGift);
  }

  final List<OpsOperationModel> _wechatGift;
  @override
  @JsonKey(name: 'wechat_gift')
  List<OpsOperationModel> get wechatGift {
    if (_wechatGift is EqualUnmodifiableListView) return _wechatGift;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_wechatGift);
  }

  final List<OpsOperationModel> _versionIntro;
  @override
  @JsonKey(name: 'version_introduce')
  List<OpsOperationModel> get versionIntro {
    if (_versionIntro is EqualUnmodifiableListView) return _versionIntro;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_versionIntro);
  }

  @override
  String toString() {
    return 'OpsOperationResponse(newUserGift: $newUserGift, wechatGift: $wechatGift, versionIntro: $versionIntro)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OpsOperationResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._newUserGift, _newUserGift) &&
            const DeepCollectionEquality()
                .equals(other._wechatGift, _wechatGift) &&
            const DeepCollectionEquality()
                .equals(other._versionIntro, _versionIntro));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_newUserGift),
      const DeepCollectionEquality().hash(_wechatGift),
      const DeepCollectionEquality().hash(_versionIntro));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OpsOperationResponseImplCopyWith<_$OpsOperationResponseImpl>
      get copyWith =>
          __$$OpsOperationResponseImplCopyWithImpl<_$OpsOperationResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OpsOperationResponseImplToJson(
      this,
    );
  }
}

abstract class _OpsOperationResponse extends OpsOperationResponse {
  const factory _OpsOperationResponse(
      {@JsonKey(name: 'new_user_gift')
      final List<OpsOperationModel> newUserGift,
      @JsonKey(name: 'wechat_gift') final List<OpsOperationModel> wechatGift,
      @JsonKey(name: 'version_introduce')
      final List<OpsOperationModel> versionIntro}) = _$OpsOperationResponseImpl;
  const _OpsOperationResponse._() : super._();

  factory _OpsOperationResponse.fromJson(Map<String, dynamic> json) =
      _$OpsOperationResponseImpl.fromJson;

  @override
  @JsonKey(name: 'new_user_gift')
  List<OpsOperationModel> get newUserGift;
  @override
  @JsonKey(name: 'wechat_gift')
  List<OpsOperationModel> get wechatGift;
  @override
  @JsonKey(name: 'version_introduce')
  List<OpsOperationModel> get versionIntro;
  @override
  @JsonKey(ignore: true)
  _$$OpsOperationResponseImplCopyWith<_$OpsOperationResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
