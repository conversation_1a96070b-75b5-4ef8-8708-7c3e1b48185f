// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'operation_url_resource.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OperationUrlResourceImpl _$$OperationUrlResourceImplFromJson(
        Map<String, dynamic> json) =>
    _$OperationUrlResourceImpl(
      screenshot: json['screenshot'] == null
          ? null
          : OperationUrlResource.fromJson(
              json['screenshot'] as Map<String, dynamic>),
      url: json['url'] as String,
      size: (json['size'] as num).toInt(),
      expire: (json['expire'] as num).toInt(),
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
    );

Map<String, dynamic> _$$OperationUrlResourceImplToJson(
        _$OperationUrlResourceImpl instance) =>
    <String, dynamic>{
      'screenshot': instance.screenshot,
      'url': instance.url,
      'size': instance.size,
      'expire': instance.expire,
      'width': instance.width,
      'height': instance.height,
    };
