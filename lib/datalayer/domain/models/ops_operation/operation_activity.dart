import 'package:freezed_annotation/freezed_annotation.dart';

import 'operation_period.dart';
import 'operation_url_resource.dart';

part 'operation_activity.freezed.dart';
part 'operation_activity.g.dart';

/// 运营活动
@freezed
class OperationActivity with _$OperationActivity {
  const factory OperationActivity({
    required String id,
    required String pid,
    required String rootId,
    required String trackId,
    required String fieldCode,
    required String name,
    required OperationPeriod period,
    String? tag,
    int? count,
    // 可选字段 - 企业微信礼包相关 - 微信图片对象
    OperationUrlResource? wechartUrl,
    // 权益展示名字
    String? displayName,
    // 标题
    String? title,
    // 自我介绍
    String? selfIntroduction,
    // 扫码提示
    String? scanCodeTip,

    // 可选字段 - 新人礼包相关
    String? description,
    OperationUrlResource? giftUrl,
    OperationUrlResource? successUrl,

    // 可选字段 - 版本介绍，以后再增加图片视频可复用
    OperationUrlResource? image,
    OperationUrlResource? video,
  }) = _OperationActivity;
  const OperationActivity._();

  factory OperationActivity.fromJson(Map<String, dynamic> json) =>
      _$OperationActivityFromJson(json);
}
