// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'operation_period.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OperationPeriod _$OperationPeriodFromJson(Map<String, dynamic> json) {
  return _OperationPeriod.fromJson(json);
}

/// @nodoc
mixin _$OperationPeriod {
  int get begin => throw _privateConstructorUsedError;
  int get end => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OperationPeriodCopyWith<OperationPeriod> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OperationPeriodCopyWith<$Res> {
  factory $OperationPeriodCopyWith(
          OperationPeriod value, $Res Function(OperationPeriod) then) =
      _$OperationPeriodCopyWithImpl<$Res, OperationPeriod>;
  @useResult
  $Res call({int begin, int end});
}

/// @nodoc
class _$OperationPeriodCopyWithImpl<$Res, $Val extends OperationPeriod>
    implements $OperationPeriodCopyWith<$Res> {
  _$OperationPeriodCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? begin = null,
    Object? end = null,
  }) {
    return _then(_value.copyWith(
      begin: null == begin
          ? _value.begin
          : begin // ignore: cast_nullable_to_non_nullable
              as int,
      end: null == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OperationPeriodImplCopyWith<$Res>
    implements $OperationPeriodCopyWith<$Res> {
  factory _$$OperationPeriodImplCopyWith(_$OperationPeriodImpl value,
          $Res Function(_$OperationPeriodImpl) then) =
      __$$OperationPeriodImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int begin, int end});
}

/// @nodoc
class __$$OperationPeriodImplCopyWithImpl<$Res>
    extends _$OperationPeriodCopyWithImpl<$Res, _$OperationPeriodImpl>
    implements _$$OperationPeriodImplCopyWith<$Res> {
  __$$OperationPeriodImplCopyWithImpl(
      _$OperationPeriodImpl _value, $Res Function(_$OperationPeriodImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? begin = null,
    Object? end = null,
  }) {
    return _then(_$OperationPeriodImpl(
      begin: null == begin
          ? _value.begin
          : begin // ignore: cast_nullable_to_non_nullable
              as int,
      end: null == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OperationPeriodImpl extends _OperationPeriod {
  const _$OperationPeriodImpl({required this.begin, required this.end})
      : super._();

  factory _$OperationPeriodImpl.fromJson(Map<String, dynamic> json) =>
      _$$OperationPeriodImplFromJson(json);

  @override
  final int begin;
  @override
  final int end;

  @override
  String toString() {
    return 'OperationPeriod(begin: $begin, end: $end)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OperationPeriodImpl &&
            (identical(other.begin, begin) || other.begin == begin) &&
            (identical(other.end, end) || other.end == end));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, begin, end);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OperationPeriodImplCopyWith<_$OperationPeriodImpl> get copyWith =>
      __$$OperationPeriodImplCopyWithImpl<_$OperationPeriodImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OperationPeriodImplToJson(
      this,
    );
  }
}

abstract class _OperationPeriod extends OperationPeriod {
  const factory _OperationPeriod(
      {required final int begin,
      required final int end}) = _$OperationPeriodImpl;
  const _OperationPeriod._() : super._();

  factory _OperationPeriod.fromJson(Map<String, dynamic> json) =
      _$OperationPeriodImpl.fromJson;

  @override
  int get begin;
  @override
  int get end;
  @override
  @JsonKey(ignore: true)
  _$$OperationPeriodImplCopyWith<_$OperationPeriodImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
