import 'package:freezed_annotation/freezed_annotation.dart';

part 'operation_period.freezed.dart';
part 'operation_period.g.dart';

/// 运营活动时间段
@freezed
class OperationPeriod with _$OperationPeriod {
  const factory OperationPeriod({
    required int begin,
    required int end,
  }) = _OperationPeriod;
  const OperationPeriod._();

  factory OperationPeriod.fromJson(Map<String, dynamic> json) =>
      _$OperationPeriodFromJson(json);
}
