// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'operation_url_resource.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OperationUrlResource _$OperationUrlResourceFromJson(Map<String, dynamic> json) {
  return _OperationUrlResource.fromJson(json);
}

/// @nodoc
mixin _$OperationUrlResource {
// 截图(可选,视频资源才有)
  OperationUrlResource? get screenshot =>
      throw _privateConstructorUsedError; // 图片
  String get url => throw _privateConstructorUsedError; // 大小
  int get size => throw _privateConstructorUsedError; // 过期时间
  int get expire => throw _privateConstructorUsedError; // 宽度
  int get width => throw _privateConstructorUsedError; // 高度
  int get height => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OperationUrlResourceCopyWith<OperationUrlResource> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OperationUrlResourceCopyWith<$Res> {
  factory $OperationUrlResourceCopyWith(OperationUrlResource value,
          $Res Function(OperationUrlResource) then) =
      _$OperationUrlResourceCopyWithImpl<$Res, OperationUrlResource>;
  @useResult
  $Res call(
      {OperationUrlResource? screenshot,
      String url,
      int size,
      int expire,
      int width,
      int height});

  $OperationUrlResourceCopyWith<$Res>? get screenshot;
}

/// @nodoc
class _$OperationUrlResourceCopyWithImpl<$Res,
        $Val extends OperationUrlResource>
    implements $OperationUrlResourceCopyWith<$Res> {
  _$OperationUrlResourceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? screenshot = freezed,
    Object? url = null,
    Object? size = null,
    Object? expire = null,
    Object? width = null,
    Object? height = null,
  }) {
    return _then(_value.copyWith(
      screenshot: freezed == screenshot
          ? _value.screenshot
          : screenshot // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      expire: null == expire
          ? _value.expire
          : expire // ignore: cast_nullable_to_non_nullable
              as int,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationUrlResourceCopyWith<$Res>? get screenshot {
    if (_value.screenshot == null) {
      return null;
    }

    return $OperationUrlResourceCopyWith<$Res>(_value.screenshot!, (value) {
      return _then(_value.copyWith(screenshot: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OperationUrlResourceImplCopyWith<$Res>
    implements $OperationUrlResourceCopyWith<$Res> {
  factory _$$OperationUrlResourceImplCopyWith(_$OperationUrlResourceImpl value,
          $Res Function(_$OperationUrlResourceImpl) then) =
      __$$OperationUrlResourceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OperationUrlResource? screenshot,
      String url,
      int size,
      int expire,
      int width,
      int height});

  @override
  $OperationUrlResourceCopyWith<$Res>? get screenshot;
}

/// @nodoc
class __$$OperationUrlResourceImplCopyWithImpl<$Res>
    extends _$OperationUrlResourceCopyWithImpl<$Res, _$OperationUrlResourceImpl>
    implements _$$OperationUrlResourceImplCopyWith<$Res> {
  __$$OperationUrlResourceImplCopyWithImpl(_$OperationUrlResourceImpl _value,
      $Res Function(_$OperationUrlResourceImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? screenshot = freezed,
    Object? url = null,
    Object? size = null,
    Object? expire = null,
    Object? width = null,
    Object? height = null,
  }) {
    return _then(_$OperationUrlResourceImpl(
      screenshot: freezed == screenshot
          ? _value.screenshot
          : screenshot // ignore: cast_nullable_to_non_nullable
              as OperationUrlResource?,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      expire: null == expire
          ? _value.expire
          : expire // ignore: cast_nullable_to_non_nullable
              as int,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OperationUrlResourceImpl extends _OperationUrlResource {
  const _$OperationUrlResourceImpl(
      {this.screenshot,
      required this.url,
      required this.size,
      required this.expire,
      required this.width,
      required this.height})
      : super._();

  factory _$OperationUrlResourceImpl.fromJson(Map<String, dynamic> json) =>
      _$$OperationUrlResourceImplFromJson(json);

// 截图(可选,视频资源才有)
  @override
  final OperationUrlResource? screenshot;
// 图片
  @override
  final String url;
// 大小
  @override
  final int size;
// 过期时间
  @override
  final int expire;
// 宽度
  @override
  final int width;
// 高度
  @override
  final int height;

  @override
  String toString() {
    return 'OperationUrlResource(screenshot: $screenshot, url: $url, size: $size, expire: $expire, width: $width, height: $height)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OperationUrlResourceImpl &&
            (identical(other.screenshot, screenshot) ||
                other.screenshot == screenshot) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.expire, expire) || other.expire == expire) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, screenshot, url, size, expire, width, height);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OperationUrlResourceImplCopyWith<_$OperationUrlResourceImpl>
      get copyWith =>
          __$$OperationUrlResourceImplCopyWithImpl<_$OperationUrlResourceImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OperationUrlResourceImplToJson(
      this,
    );
  }
}

abstract class _OperationUrlResource extends OperationUrlResource {
  const factory _OperationUrlResource(
      {final OperationUrlResource? screenshot,
      required final String url,
      required final int size,
      required final int expire,
      required final int width,
      required final int height}) = _$OperationUrlResourceImpl;
  const _OperationUrlResource._() : super._();

  factory _OperationUrlResource.fromJson(Map<String, dynamic> json) =
      _$OperationUrlResourceImpl.fromJson;

  @override // 截图(可选,视频资源才有)
  OperationUrlResource? get screenshot;
  @override // 图片
  String get url;
  @override // 大小
  int get size;
  @override // 过期时间
  int get expire;
  @override // 宽度
  int get width;
  @override // 高度
  int get height;
  @override
  @JsonKey(ignore: true)
  _$$OperationUrlResourceImplCopyWith<_$OperationUrlResourceImpl>
      get copyWith => throw _privateConstructorUsedError;
}
