// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'operation_activity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OperationActivityImpl _$$OperationActivityImplFromJson(
        Map<String, dynamic> json) =>
    _$OperationActivityImpl(
      id: json['id'] as String,
      pid: json['pid'] as String,
      rootId: json['rootId'] as String,
      trackId: json['trackId'] as String,
      fieldCode: json['fieldCode'] as String,
      name: json['name'] as String,
      period: OperationPeriod.fromJson(json['period'] as Map<String, dynamic>),
      tag: json['tag'] as String?,
      count: (json['count'] as num?)?.toInt(),
      wechartUrl: json['wechartUrl'] == null
          ? null
          : OperationUrlResource.fromJson(
              json['wechartUrl'] as Map<String, dynamic>),
      displayName: json['displayName'] as String?,
      title: json['title'] as String?,
      selfIntroduction: json['selfIntroduction'] as String?,
      scanCodeTip: json['scanCodeTip'] as String?,
      description: json['description'] as String?,
      giftUrl: json['giftUrl'] == null
          ? null
          : OperationUrlResource.fromJson(
              json['giftUrl'] as Map<String, dynamic>),
      successUrl: json['successUrl'] == null
          ? null
          : OperationUrlResource.fromJson(
              json['successUrl'] as Map<String, dynamic>),
      image: json['image'] == null
          ? null
          : OperationUrlResource.fromJson(
              json['image'] as Map<String, dynamic>),
      video: json['video'] == null
          ? null
          : OperationUrlResource.fromJson(
              json['video'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OperationActivityImplToJson(
        _$OperationActivityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'pid': instance.pid,
      'rootId': instance.rootId,
      'trackId': instance.trackId,
      'fieldCode': instance.fieldCode,
      'name': instance.name,
      'period': instance.period,
      'tag': instance.tag,
      'count': instance.count,
      'wechartUrl': instance.wechartUrl,
      'displayName': instance.displayName,
      'title': instance.title,
      'selfIntroduction': instance.selfIntroduction,
      'scanCodeTip': instance.scanCodeTip,
      'description': instance.description,
      'giftUrl': instance.giftUrl,
      'successUrl': instance.successUrl,
      'image': instance.image,
      'video': instance.video,
    };
