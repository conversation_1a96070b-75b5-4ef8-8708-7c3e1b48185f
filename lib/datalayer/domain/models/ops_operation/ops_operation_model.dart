import 'package:freezed_annotation/freezed_annotation.dart';
import 'operation_period.dart';
import 'operation_activity.dart';

part 'ops_operation_model.freezed.dart';
part 'ops_operation_model.g.dart';

/// 运营弹窗模型
@freezed
class OpsOperationModel with _$OpsOperationModel {
  const factory OpsOperationModel({
    required String id,
    required String name,
    required OperationPeriod period,
    required List<OperationActivity> activities,
  }) = _OpsOperationModel;
  const OpsOperationModel._();

  factory OpsOperationModel.fromJson(Map<String, dynamic> json) =>
      _$OpsOperationModelFromJson(json);
}
