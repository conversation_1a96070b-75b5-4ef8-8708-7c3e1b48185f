// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ops_operation_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OpsOperationModel _$OpsOperationModelFromJson(Map<String, dynamic> json) {
  return _OpsOperationModel.fromJson(json);
}

/// @nodoc
mixin _$OpsOperationModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  OperationPeriod get period => throw _privateConstructorUsedError;
  List<OperationActivity> get activities => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OpsOperationModelCopyWith<OpsOperationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OpsOperationModelCopyWith<$Res> {
  factory $OpsOperationModelCopyWith(
          OpsOperationModel value, $Res Function(OpsOperationModel) then) =
      _$OpsOperationModelCopyWithImpl<$Res, OpsOperationModel>;
  @useResult
  $Res call(
      {String id,
      String name,
      OperationPeriod period,
      List<OperationActivity> activities});

  $OperationPeriodCopyWith<$Res> get period;
}

/// @nodoc
class _$OpsOperationModelCopyWithImpl<$Res, $Val extends OpsOperationModel>
    implements $OpsOperationModelCopyWith<$Res> {
  _$OpsOperationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? period = null,
    Object? activities = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as OperationPeriod,
      activities: null == activities
          ? _value.activities
          : activities // ignore: cast_nullable_to_non_nullable
              as List<OperationActivity>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $OperationPeriodCopyWith<$Res> get period {
    return $OperationPeriodCopyWith<$Res>(_value.period, (value) {
      return _then(_value.copyWith(period: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OpsOperationModelImplCopyWith<$Res>
    implements $OpsOperationModelCopyWith<$Res> {
  factory _$$OpsOperationModelImplCopyWith(_$OpsOperationModelImpl value,
          $Res Function(_$OpsOperationModelImpl) then) =
      __$$OpsOperationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      OperationPeriod period,
      List<OperationActivity> activities});

  @override
  $OperationPeriodCopyWith<$Res> get period;
}

/// @nodoc
class __$$OpsOperationModelImplCopyWithImpl<$Res>
    extends _$OpsOperationModelCopyWithImpl<$Res, _$OpsOperationModelImpl>
    implements _$$OpsOperationModelImplCopyWith<$Res> {
  __$$OpsOperationModelImplCopyWithImpl(_$OpsOperationModelImpl _value,
      $Res Function(_$OpsOperationModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? period = null,
    Object? activities = null,
  }) {
    return _then(_$OpsOperationModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as OperationPeriod,
      activities: null == activities
          ? _value._activities
          : activities // ignore: cast_nullable_to_non_nullable
              as List<OperationActivity>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OpsOperationModelImpl extends _OpsOperationModel {
  const _$OpsOperationModelImpl(
      {required this.id,
      required this.name,
      required this.period,
      required final List<OperationActivity> activities})
      : _activities = activities,
        super._();

  factory _$OpsOperationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OpsOperationModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final OperationPeriod period;
  final List<OperationActivity> _activities;
  @override
  List<OperationActivity> get activities {
    if (_activities is EqualUnmodifiableListView) return _activities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_activities);
  }

  @override
  String toString() {
    return 'OpsOperationModel(id: $id, name: $name, period: $period, activities: $activities)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OpsOperationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.period, period) || other.period == period) &&
            const DeepCollectionEquality()
                .equals(other._activities, _activities));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, period,
      const DeepCollectionEquality().hash(_activities));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OpsOperationModelImplCopyWith<_$OpsOperationModelImpl> get copyWith =>
      __$$OpsOperationModelImplCopyWithImpl<_$OpsOperationModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OpsOperationModelImplToJson(
      this,
    );
  }
}

abstract class _OpsOperationModel extends OpsOperationModel {
  const factory _OpsOperationModel(
          {required final String id,
          required final String name,
          required final OperationPeriod period,
          required final List<OperationActivity> activities}) =
      _$OpsOperationModelImpl;
  const _OpsOperationModel._() : super._();

  factory _OpsOperationModel.fromJson(Map<String, dynamic> json) =
      _$OpsOperationModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  OperationPeriod get period;
  @override
  List<OperationActivity> get activities;
  @override
  @JsonKey(ignore: true)
  _$$OpsOperationModelImplCopyWith<_$OpsOperationModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
