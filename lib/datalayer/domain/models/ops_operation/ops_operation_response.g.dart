// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ops_operation_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OpsOperationResponseImpl _$$OpsOperationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$OpsOperationResponseImpl(
      newUserGift: (json['new_user_gift'] as List<dynamic>?)
              ?.map(
                  (e) => OpsOperationModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      wechatGift: (json['wechat_gift'] as List<dynamic>?)
              ?.map(
                  (e) => OpsOperationModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      versionIntro: (json['version_introduce'] as List<dynamic>?)
              ?.map(
                  (e) => OpsOperationModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$OpsOperationResponseImplToJson(
        _$OpsOperationResponseImpl instance) =>
    <String, dynamic>{
      'new_user_gift': instance.newUserGift,
      'wechat_gift': instance.wechatGift,
      'version_introduce': instance.versionIntro,
    };
