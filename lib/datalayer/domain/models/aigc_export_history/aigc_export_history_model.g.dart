// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_export_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AIGCExportHistoryModelImpl _$$AIGCExportHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AIGCExportHistoryModelImpl(
      consumptions: (json['consumptions'] as List<dynamic>)
          .map((e) => AIGCExportHistoryItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      pageSize: (json['page_size'] as num).toInt(),
    );

Map<String, dynamic> _$$AIGCExportHistoryModelImplToJson(
        _$AIGCExportHistoryModelImpl instance) =>
    <String, dynamic>{
      'consumptions': instance.consumptions,
      'total': instance.total,
      'page': instance.page,
      'page_size': instance.pageSize,
    };

_$AIGCExportHistoryItemImpl _$$AIGCExportHistoryItemImplFromJson(
        Map<String, dynamic> json) =>
    _$AIGCExportHistoryItemImpl(
      id: json['id'] as String,
      accountRole: json['account_role'] as String,
      mobile: json['mobile'] as String,
      nickname: json['nickname'] as String?,
      photoName: json['photo_name'] as String,
      hostname: json['hostname'] as String?,
      deviceId: json['device_id'] as String,
      costType: json['cost_type'] as String,
      costValue: (json['cost_value'] as num).toInt(),
      createAt: (json['create_at'] as num).toInt(),
    );

Map<String, dynamic> _$$AIGCExportHistoryItemImplToJson(
        _$AIGCExportHistoryItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'account_role': instance.accountRole,
      'mobile': instance.mobile,
      'nickname': instance.nickname,
      'photo_name': instance.photoName,
      'hostname': instance.hostname,
      'device_id': instance.deviceId,
      'cost_type': instance.costType,
      'cost_value': instance.costValue,
      'create_at': instance.createAt,
    };
