// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_to_unity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessageToUnity _$MessageToUnityFromJson(Map<String, dynamic> json) {
  return _MessageToUnity.fromJson(json);
}

/// @nodoc
mixin _$MessageToUnity {
  String get completed => throw _privateConstructorUsedError;
  String get method => throw _privateConstructorUsedError;
  String? get args => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessageToUnityCopyWith<MessageToUnity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageToUnityCopyWith<$Res> {
  factory $MessageToUnityCopyWith(
          MessageToUnity value, $Res Function(MessageToUnity) then) =
      _$MessageToUnityCopyWithImpl<$Res, MessageToUnity>;
  @useResult
  $Res call({String completed, String method, String? args});
}

/// @nodoc
class _$MessageToUnityCopyWithImpl<$Res, $Val extends MessageToUnity>
    implements $MessageToUnityCopyWith<$Res> {
  _$MessageToUnityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? completed = null,
    Object? method = null,
    Object? args = freezed,
  }) {
    return _then(_value.copyWith(
      completed: null == completed
          ? _value.completed
          : completed // ignore: cast_nullable_to_non_nullable
              as String,
      method: null == method
          ? _value.method
          : method // ignore: cast_nullable_to_non_nullable
              as String,
      args: freezed == args
          ? _value.args
          : args // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessageToUnityImplCopyWith<$Res>
    implements $MessageToUnityCopyWith<$Res> {
  factory _$$MessageToUnityImplCopyWith(_$MessageToUnityImpl value,
          $Res Function(_$MessageToUnityImpl) then) =
      __$$MessageToUnityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String completed, String method, String? args});
}

/// @nodoc
class __$$MessageToUnityImplCopyWithImpl<$Res>
    extends _$MessageToUnityCopyWithImpl<$Res, _$MessageToUnityImpl>
    implements _$$MessageToUnityImplCopyWith<$Res> {
  __$$MessageToUnityImplCopyWithImpl(
      _$MessageToUnityImpl _value, $Res Function(_$MessageToUnityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? completed = null,
    Object? method = null,
    Object? args = freezed,
  }) {
    return _then(_$MessageToUnityImpl(
      completed: null == completed
          ? _value.completed
          : completed // ignore: cast_nullable_to_non_nullable
              as String,
      method: null == method
          ? _value.method
          : method // ignore: cast_nullable_to_non_nullable
              as String,
      args: freezed == args
          ? _value.args
          : args // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageToUnityImpl extends _MessageToUnity {
  const _$MessageToUnityImpl(
      {required this.completed, required this.method, this.args})
      : super._();

  factory _$MessageToUnityImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageToUnityImplFromJson(json);

  @override
  final String completed;
  @override
  final String method;
  @override
  final String? args;

  @override
  String toString() {
    return 'MessageToUnity(completed: $completed, method: $method, args: $args)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageToUnityImpl &&
            (identical(other.completed, completed) ||
                other.completed == completed) &&
            (identical(other.method, method) || other.method == method) &&
            (identical(other.args, args) || other.args == args));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, completed, method, args);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageToUnityImplCopyWith<_$MessageToUnityImpl> get copyWith =>
      __$$MessageToUnityImplCopyWithImpl<_$MessageToUnityImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageToUnityImplToJson(
      this,
    );
  }
}

abstract class _MessageToUnity extends MessageToUnity {
  const factory _MessageToUnity(
      {required final String completed,
      required final String method,
      final String? args}) = _$MessageToUnityImpl;
  const _MessageToUnity._() : super._();

  factory _MessageToUnity.fromJson(Map<String, dynamic> json) =
      _$MessageToUnityImpl.fromJson;

  @override
  String get completed;
  @override
  String get method;
  @override
  String? get args;
  @override
  @JsonKey(ignore: true)
  _$$MessageToUnityImplCopyWith<_$MessageToUnityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
