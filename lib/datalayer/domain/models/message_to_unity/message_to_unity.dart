import 'package:freezed_annotation/freezed_annotation.dart';

part 'message_to_unity.freezed.dart';
part 'message_to_unity.g.dart';

// 与Unity通信的消息, 参考现有项目
@freezed
class MessageToUnity with _$MessageToUnity {
  const factory MessageToUnity({
    required String completed,
    required String method,
    String? args,
  }) = _MessageToUnity;
  const MessageToUnity._();

  factory MessageToUnity.fromJson(Map<String, dynamic> json) =>
      _$MessageToUnityFromJson(json);
}
