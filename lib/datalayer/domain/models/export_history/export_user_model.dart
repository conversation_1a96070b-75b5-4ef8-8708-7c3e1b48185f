import 'package:freezed_annotation/freezed_annotation.dart';

part 'export_user_model.freezed.dart';
part 'export_user_model.g.dart';

@freezed
class ExportUserModel with _$ExportUserModel {
  const factory ExportUserModel({
    required String mobile,
    required String nickname,
    required String role,
  }) = _ExportUserModel;

  factory ExportUserModel.fromJson(Map<String, dynamic> json) =>
      _$ExportUserModelFromJson(json);
}
