// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'export_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExportHistoryModelImpl _$$ExportHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ExportHistoryModelImpl(
      items: (json['items'] as List<dynamic>)
          .map((e) => ExportHistoryItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      pagination:
          PaginationModel.fromJson(json['pagination'] as Map<String, dynamic>),
      users: (json['users'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry(k, ExportUserModel.fromJson(e as Map<String, dynamic>)),
      ),
    );

Map<String, dynamic> _$$ExportHistoryModelImplToJson(
        _$ExportHistoryModelImpl instance) =>
    <String, dynamic>{
      'items': instance.items,
      'pagination': instance.pagination,
      'users': instance.users,
    };

_$ExportHistoryItemImpl _$$ExportHistoryItemImplFromJson(
        Map<String, dynamic> json) =>
    _$ExportHistoryItemImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      storeId: json['storeId'] as String,
      userId: json['userId'] as String,
      platform: json['platform'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      exportNumber: (json['exportNumber'] as num).toInt(),
      sampleNumber: (json['sampleNumber'] as num).toInt(),
      chargeNumber: (json['chargeNumber'] as num).toInt(),
      hostname: json['hostname'] as String,
      deviceId: json['deviceId'] as String,
      clientProjectId: json['clientProjectId'] as String,
    );

Map<String, dynamic> _$$ExportHistoryItemImplToJson(
        _$ExportHistoryItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'storeId': instance.storeId,
      'userId': instance.userId,
      'platform': instance.platform,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'exportNumber': instance.exportNumber,
      'sampleNumber': instance.sampleNumber,
      'chargeNumber': instance.chargeNumber,
      'hostname': instance.hostname,
      'deviceId': instance.deviceId,
      'clientProjectId': instance.clientProjectId,
    };
