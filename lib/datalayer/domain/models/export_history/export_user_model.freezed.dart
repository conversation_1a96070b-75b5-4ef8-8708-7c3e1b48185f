// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'export_user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExportUserModel _$ExportUserModelFromJson(Map<String, dynamic> json) {
  return _ExportUserModel.fromJson(json);
}

/// @nodoc
mixin _$ExportUserModel {
  String get mobile => throw _privateConstructorUsedError;
  String get nickname => throw _privateConstructorUsedError;
  String get role => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportUserModelCopyWith<ExportUserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportUserModelCopyWith<$Res> {
  factory $ExportUserModelCopyWith(
          ExportUserModel value, $Res Function(ExportUserModel) then) =
      _$ExportUserModelCopyWithImpl<$Res, ExportUserModel>;
  @useResult
  $Res call({String mobile, String nickname, String role});
}

/// @nodoc
class _$ExportUserModelCopyWithImpl<$Res, $Val extends ExportUserModel>
    implements $ExportUserModelCopyWith<$Res> {
  _$ExportUserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = null,
    Object? nickname = null,
    Object? role = null,
  }) {
    return _then(_value.copyWith(
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: null == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportUserModelImplCopyWith<$Res>
    implements $ExportUserModelCopyWith<$Res> {
  factory _$$ExportUserModelImplCopyWith(_$ExportUserModelImpl value,
          $Res Function(_$ExportUserModelImpl) then) =
      __$$ExportUserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String mobile, String nickname, String role});
}

/// @nodoc
class __$$ExportUserModelImplCopyWithImpl<$Res>
    extends _$ExportUserModelCopyWithImpl<$Res, _$ExportUserModelImpl>
    implements _$$ExportUserModelImplCopyWith<$Res> {
  __$$ExportUserModelImplCopyWithImpl(
      _$ExportUserModelImpl _value, $Res Function(_$ExportUserModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = null,
    Object? nickname = null,
    Object? role = null,
  }) {
    return _then(_$ExportUserModelImpl(
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: null == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportUserModelImpl implements _ExportUserModel {
  const _$ExportUserModelImpl(
      {required this.mobile, required this.nickname, required this.role});

  factory _$ExportUserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportUserModelImplFromJson(json);

  @override
  final String mobile;
  @override
  final String nickname;
  @override
  final String role;

  @override
  String toString() {
    return 'ExportUserModel(mobile: $mobile, nickname: $nickname, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportUserModelImpl &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.role, role) || other.role == role));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, mobile, nickname, role);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportUserModelImplCopyWith<_$ExportUserModelImpl> get copyWith =>
      __$$ExportUserModelImplCopyWithImpl<_$ExportUserModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportUserModelImplToJson(
      this,
    );
  }
}

abstract class _ExportUserModel implements ExportUserModel {
  const factory _ExportUserModel(
      {required final String mobile,
      required final String nickname,
      required final String role}) = _$ExportUserModelImpl;

  factory _ExportUserModel.fromJson(Map<String, dynamic> json) =
      _$ExportUserModelImpl.fromJson;

  @override
  String get mobile;
  @override
  String get nickname;
  @override
  String get role;
  @override
  @JsonKey(ignore: true)
  _$$ExportUserModelImplCopyWith<_$ExportUserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
