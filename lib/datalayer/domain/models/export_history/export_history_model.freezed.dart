// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'export_history_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExportHistoryModel _$ExportHistoryModelFromJson(Map<String, dynamic> json) {
  return _ExportHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$ExportHistoryModel {
  List<ExportHistoryItem> get items => throw _privateConstructorUsedError;
  PaginationModel get pagination => throw _privateConstructorUsedError;
  Map<String, ExportUserModel> get users => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportHistoryModelCopyWith<ExportHistoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportHistoryModelCopyWith<$Res> {
  factory $ExportHistoryModelCopyWith(
          ExportHistoryModel value, $Res Function(ExportHistoryModel) then) =
      _$ExportHistoryModelCopyWithImpl<$Res, ExportHistoryModel>;
  @useResult
  $Res call(
      {List<ExportHistoryItem> items,
      PaginationModel pagination,
      Map<String, ExportUserModel> users});

  $PaginationModelCopyWith<$Res> get pagination;
}

/// @nodoc
class _$ExportHistoryModelCopyWithImpl<$Res, $Val extends ExportHistoryModel>
    implements $ExportHistoryModelCopyWith<$Res> {
  _$ExportHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? pagination = null,
    Object? users = null,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ExportHistoryItem>,
      pagination: null == pagination
          ? _value.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as PaginationModel,
      users: null == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as Map<String, ExportUserModel>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationModelCopyWith<$Res> get pagination {
    return $PaginationModelCopyWith<$Res>(_value.pagination, (value) {
      return _then(_value.copyWith(pagination: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ExportHistoryModelImplCopyWith<$Res>
    implements $ExportHistoryModelCopyWith<$Res> {
  factory _$$ExportHistoryModelImplCopyWith(_$ExportHistoryModelImpl value,
          $Res Function(_$ExportHistoryModelImpl) then) =
      __$$ExportHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ExportHistoryItem> items,
      PaginationModel pagination,
      Map<String, ExportUserModel> users});

  @override
  $PaginationModelCopyWith<$Res> get pagination;
}

/// @nodoc
class __$$ExportHistoryModelImplCopyWithImpl<$Res>
    extends _$ExportHistoryModelCopyWithImpl<$Res, _$ExportHistoryModelImpl>
    implements _$$ExportHistoryModelImplCopyWith<$Res> {
  __$$ExportHistoryModelImplCopyWithImpl(_$ExportHistoryModelImpl _value,
      $Res Function(_$ExportHistoryModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? pagination = null,
    Object? users = null,
  }) {
    return _then(_$ExportHistoryModelImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ExportHistoryItem>,
      pagination: null == pagination
          ? _value.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as PaginationModel,
      users: null == users
          ? _value._users
          : users // ignore: cast_nullable_to_non_nullable
              as Map<String, ExportUserModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportHistoryModelImpl implements _ExportHistoryModel {
  const _$ExportHistoryModelImpl(
      {required final List<ExportHistoryItem> items,
      required this.pagination,
      required final Map<String, ExportUserModel> users})
      : _items = items,
        _users = users;

  factory _$ExportHistoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportHistoryModelImplFromJson(json);

  final List<ExportHistoryItem> _items;
  @override
  List<ExportHistoryItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  final PaginationModel pagination;
  final Map<String, ExportUserModel> _users;
  @override
  Map<String, ExportUserModel> get users {
    if (_users is EqualUnmodifiableMapView) return _users;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_users);
  }

  @override
  String toString() {
    return 'ExportHistoryModel(items: $items, pagination: $pagination, users: $users)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportHistoryModelImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.pagination, pagination) ||
                other.pagination == pagination) &&
            const DeepCollectionEquality().equals(other._users, _users));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_items),
      pagination,
      const DeepCollectionEquality().hash(_users));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportHistoryModelImplCopyWith<_$ExportHistoryModelImpl> get copyWith =>
      __$$ExportHistoryModelImplCopyWithImpl<_$ExportHistoryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _ExportHistoryModel implements ExportHistoryModel {
  const factory _ExportHistoryModel(
          {required final List<ExportHistoryItem> items,
          required final PaginationModel pagination,
          required final Map<String, ExportUserModel> users}) =
      _$ExportHistoryModelImpl;

  factory _ExportHistoryModel.fromJson(Map<String, dynamic> json) =
      _$ExportHistoryModelImpl.fromJson;

  @override
  List<ExportHistoryItem> get items;
  @override
  PaginationModel get pagination;
  @override
  Map<String, ExportUserModel> get users;
  @override
  @JsonKey(ignore: true)
  _$$ExportHistoryModelImplCopyWith<_$ExportHistoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ExportHistoryItem _$ExportHistoryItemFromJson(Map<String, dynamic> json) {
  return _ExportHistoryItem.fromJson(json);
}

/// @nodoc
mixin _$ExportHistoryItem {
// 主键
  String get id => throw _privateConstructorUsedError; // 名称
  String get name => throw _privateConstructorUsedError; // 门店ID
  String get storeId => throw _privateConstructorUsedError; // 用户ID
  String get userId => throw _privateConstructorUsedError; // 平台
  String get platform => throw _privateConstructorUsedError; // 创建时间
  String get createdAt => throw _privateConstructorUsedError; // 更新时间
  String get updatedAt => throw _privateConstructorUsedError; // 导出张数
  int get exportNumber => throw _privateConstructorUsedError; // 小样张数
  int get sampleNumber => throw _privateConstructorUsedError; // 计费张数
  int get chargeNumber => throw _privateConstructorUsedError; // 主机名
  String get hostname => throw _privateConstructorUsedError; // 设备ID
  String get deviceId => throw _privateConstructorUsedError; // 客户端项目ID
  String get clientProjectId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportHistoryItemCopyWith<ExportHistoryItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportHistoryItemCopyWith<$Res> {
  factory $ExportHistoryItemCopyWith(
          ExportHistoryItem value, $Res Function(ExportHistoryItem) then) =
      _$ExportHistoryItemCopyWithImpl<$Res, ExportHistoryItem>;
  @useResult
  $Res call(
      {String id,
      String name,
      String storeId,
      String userId,
      String platform,
      String createdAt,
      String updatedAt,
      int exportNumber,
      int sampleNumber,
      int chargeNumber,
      String hostname,
      String deviceId,
      String clientProjectId});
}

/// @nodoc
class _$ExportHistoryItemCopyWithImpl<$Res, $Val extends ExportHistoryItem>
    implements $ExportHistoryItemCopyWith<$Res> {
  _$ExportHistoryItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? storeId = null,
    Object? userId = null,
    Object? platform = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? exportNumber = null,
    Object? sampleNumber = null,
    Object? chargeNumber = null,
    Object? hostname = null,
    Object? deviceId = null,
    Object? clientProjectId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      storeId: null == storeId
          ? _value.storeId
          : storeId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
      exportNumber: null == exportNumber
          ? _value.exportNumber
          : exportNumber // ignore: cast_nullable_to_non_nullable
              as int,
      sampleNumber: null == sampleNumber
          ? _value.sampleNumber
          : sampleNumber // ignore: cast_nullable_to_non_nullable
              as int,
      chargeNumber: null == chargeNumber
          ? _value.chargeNumber
          : chargeNumber // ignore: cast_nullable_to_non_nullable
              as int,
      hostname: null == hostname
          ? _value.hostname
          : hostname // ignore: cast_nullable_to_non_nullable
              as String,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      clientProjectId: null == clientProjectId
          ? _value.clientProjectId
          : clientProjectId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportHistoryItemImplCopyWith<$Res>
    implements $ExportHistoryItemCopyWith<$Res> {
  factory _$$ExportHistoryItemImplCopyWith(_$ExportHistoryItemImpl value,
          $Res Function(_$ExportHistoryItemImpl) then) =
      __$$ExportHistoryItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String storeId,
      String userId,
      String platform,
      String createdAt,
      String updatedAt,
      int exportNumber,
      int sampleNumber,
      int chargeNumber,
      String hostname,
      String deviceId,
      String clientProjectId});
}

/// @nodoc
class __$$ExportHistoryItemImplCopyWithImpl<$Res>
    extends _$ExportHistoryItemCopyWithImpl<$Res, _$ExportHistoryItemImpl>
    implements _$$ExportHistoryItemImplCopyWith<$Res> {
  __$$ExportHistoryItemImplCopyWithImpl(_$ExportHistoryItemImpl _value,
      $Res Function(_$ExportHistoryItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? storeId = null,
    Object? userId = null,
    Object? platform = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? exportNumber = null,
    Object? sampleNumber = null,
    Object? chargeNumber = null,
    Object? hostname = null,
    Object? deviceId = null,
    Object? clientProjectId = null,
  }) {
    return _then(_$ExportHistoryItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      storeId: null == storeId
          ? _value.storeId
          : storeId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
      exportNumber: null == exportNumber
          ? _value.exportNumber
          : exportNumber // ignore: cast_nullable_to_non_nullable
              as int,
      sampleNumber: null == sampleNumber
          ? _value.sampleNumber
          : sampleNumber // ignore: cast_nullable_to_non_nullable
              as int,
      chargeNumber: null == chargeNumber
          ? _value.chargeNumber
          : chargeNumber // ignore: cast_nullable_to_non_nullable
              as int,
      hostname: null == hostname
          ? _value.hostname
          : hostname // ignore: cast_nullable_to_non_nullable
              as String,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      clientProjectId: null == clientProjectId
          ? _value.clientProjectId
          : clientProjectId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportHistoryItemImpl implements _ExportHistoryItem {
  const _$ExportHistoryItemImpl(
      {required this.id,
      required this.name,
      required this.storeId,
      required this.userId,
      required this.platform,
      required this.createdAt,
      required this.updatedAt,
      required this.exportNumber,
      required this.sampleNumber,
      required this.chargeNumber,
      required this.hostname,
      required this.deviceId,
      required this.clientProjectId});

  factory _$ExportHistoryItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportHistoryItemImplFromJson(json);

// 主键
  @override
  final String id;
// 名称
  @override
  final String name;
// 门店ID
  @override
  final String storeId;
// 用户ID
  @override
  final String userId;
// 平台
  @override
  final String platform;
// 创建时间
  @override
  final String createdAt;
// 更新时间
  @override
  final String updatedAt;
// 导出张数
  @override
  final int exportNumber;
// 小样张数
  @override
  final int sampleNumber;
// 计费张数
  @override
  final int chargeNumber;
// 主机名
  @override
  final String hostname;
// 设备ID
  @override
  final String deviceId;
// 客户端项目ID
  @override
  final String clientProjectId;

  @override
  String toString() {
    return 'ExportHistoryItem(id: $id, name: $name, storeId: $storeId, userId: $userId, platform: $platform, createdAt: $createdAt, updatedAt: $updatedAt, exportNumber: $exportNumber, sampleNumber: $sampleNumber, chargeNumber: $chargeNumber, hostname: $hostname, deviceId: $deviceId, clientProjectId: $clientProjectId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportHistoryItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.storeId, storeId) || other.storeId == storeId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.exportNumber, exportNumber) ||
                other.exportNumber == exportNumber) &&
            (identical(other.sampleNumber, sampleNumber) ||
                other.sampleNumber == sampleNumber) &&
            (identical(other.chargeNumber, chargeNumber) ||
                other.chargeNumber == chargeNumber) &&
            (identical(other.hostname, hostname) ||
                other.hostname == hostname) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.clientProjectId, clientProjectId) ||
                other.clientProjectId == clientProjectId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      storeId,
      userId,
      platform,
      createdAt,
      updatedAt,
      exportNumber,
      sampleNumber,
      chargeNumber,
      hostname,
      deviceId,
      clientProjectId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportHistoryItemImplCopyWith<_$ExportHistoryItemImpl> get copyWith =>
      __$$ExportHistoryItemImplCopyWithImpl<_$ExportHistoryItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportHistoryItemImplToJson(
      this,
    );
  }
}

abstract class _ExportHistoryItem implements ExportHistoryItem {
  const factory _ExportHistoryItem(
      {required final String id,
      required final String name,
      required final String storeId,
      required final String userId,
      required final String platform,
      required final String createdAt,
      required final String updatedAt,
      required final int exportNumber,
      required final int sampleNumber,
      required final int chargeNumber,
      required final String hostname,
      required final String deviceId,
      required final String clientProjectId}) = _$ExportHistoryItemImpl;

  factory _ExportHistoryItem.fromJson(Map<String, dynamic> json) =
      _$ExportHistoryItemImpl.fromJson;

  @override // 主键
  String get id;
  @override // 名称
  String get name;
  @override // 门店ID
  String get storeId;
  @override // 用户ID
  String get userId;
  @override // 平台
  String get platform;
  @override // 创建时间
  String get createdAt;
  @override // 更新时间
  String get updatedAt;
  @override // 导出张数
  int get exportNumber;
  @override // 小样张数
  int get sampleNumber;
  @override // 计费张数
  int get chargeNumber;
  @override // 主机名
  String get hostname;
  @override // 设备ID
  String get deviceId;
  @override // 客户端项目ID
  String get clientProjectId;
  @override
  @JsonKey(ignore: true)
  _$$ExportHistoryItemImplCopyWith<_$ExportHistoryItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
