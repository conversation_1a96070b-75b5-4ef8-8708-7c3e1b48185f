import 'package:freezed_annotation/freezed_annotation.dart';

part 'base_config.freezed.dart';
part 'base_config.g.dart';

// 公共配置，首次启动Unity时传入
/*
  ui样式/文本
  那些功能需要关闭/隐藏
  自定义滤镜文件夹
  其他配置（最大运行尺寸）
  用户ID ....
*/

@freezed
class BaseConfig with _$BaseConfig {
  const factory BaseConfig({
    required String uuid,
  }) = _BaseConfig;
  const BaseConfig._();
  // TODO: 具体结构待定
  factory BaseConfig.fromJson(Map<String, dynamic> json) =>
      _$BaseConfigFromJson(json);
}
