// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'base_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BaseConfig _$BaseConfigFromJson(Map<String, dynamic> json) {
  return _BaseConfig.fromJson(json);
}

/// @nodoc
mixin _$BaseConfig {
  String get uuid => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BaseConfigCopyWith<BaseConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BaseConfigCopyWith<$Res> {
  factory $BaseConfigCopyWith(
          BaseConfig value, $Res Function(BaseConfig) then) =
      _$BaseConfigCopyWithImpl<$Res, BaseConfig>;
  @useResult
  $Res call({String uuid});
}

/// @nodoc
class _$BaseConfigCopyWithImpl<$Res, $Val extends BaseConfig>
    implements $BaseConfigCopyWith<$Res> {
  _$BaseConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
  }) {
    return _then(_value.copyWith(
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BaseConfigImplCopyWith<$Res>
    implements $BaseConfigCopyWith<$Res> {
  factory _$$BaseConfigImplCopyWith(
          _$BaseConfigImpl value, $Res Function(_$BaseConfigImpl) then) =
      __$$BaseConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String uuid});
}

/// @nodoc
class __$$BaseConfigImplCopyWithImpl<$Res>
    extends _$BaseConfigCopyWithImpl<$Res, _$BaseConfigImpl>
    implements _$$BaseConfigImplCopyWith<$Res> {
  __$$BaseConfigImplCopyWithImpl(
      _$BaseConfigImpl _value, $Res Function(_$BaseConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
  }) {
    return _then(_$BaseConfigImpl(
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BaseConfigImpl extends _BaseConfig {
  const _$BaseConfigImpl({required this.uuid}) : super._();

  factory _$BaseConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$BaseConfigImplFromJson(json);

  @override
  final String uuid;

  @override
  String toString() {
    return 'BaseConfig(uuid: $uuid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BaseConfigImpl &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, uuid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BaseConfigImplCopyWith<_$BaseConfigImpl> get copyWith =>
      __$$BaseConfigImplCopyWithImpl<_$BaseConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BaseConfigImplToJson(
      this,
    );
  }
}

abstract class _BaseConfig extends BaseConfig {
  const factory _BaseConfig({required final String uuid}) = _$BaseConfigImpl;
  const _BaseConfig._() : super._();

  factory _BaseConfig.fromJson(Map<String, dynamic> json) =
      _$BaseConfigImpl.fromJson;

  @override
  String get uuid;
  @override
  @JsonKey(ignore: true)
  _$$BaseConfigImplCopyWith<_$BaseConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
