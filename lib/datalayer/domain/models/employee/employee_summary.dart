import 'package:freezed_annotation/freezed_annotation.dart';

part 'employee_summary.freezed.dart';
part 'employee_summary.g.dart';

// 子账号张数汇总
@freezed
class EmployeeSummary with _$EmployeeSummary {
  const factory EmployeeSummary({
    // 主账号使用张数(计费)
    required String creator,
    // 子账号使用张数(计费)
    required String employee,
  }) = _EmployeeSummary;
  const EmployeeSummary._();

  factory EmployeeSummary.fromJson(Map<String, dynamic> json) =>
      _$EmployeeSummaryFromJson(json);
}
