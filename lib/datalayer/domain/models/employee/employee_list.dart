import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';

part 'employee_list.freezed.dart';
part 'employee_list.g.dart';

// 子账号列表响应
@freezed
class EmployeeList with _$EmployeeList {
  const factory EmployeeList({
    required String storeId,
    // ignore: invalid_annotation_target
    @JsonKey(name: 'Name') @Default("") String name,
    required String total,
    required String available,
    required List<User> users,
  }) = _EmployeeList;
  const EmployeeList._();

  factory EmployeeList.fromJson(Map<String, dynamic> json) =>
      _$EmployeeListFromJson(json);
}
