// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'employee_summary.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EmployeeSummary _$EmployeeSummaryFromJson(Map<String, dynamic> json) {
  return _EmployeeSummary.fromJson(json);
}

/// @nodoc
mixin _$EmployeeSummary {
// 主账号使用张数(计费)
  String get creator => throw _privateConstructorUsedError; // 子账号使用张数(计费)
  String get employee => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EmployeeSummaryCopyWith<EmployeeSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmployeeSummaryCopyWith<$Res> {
  factory $EmployeeSummaryCopyWith(
          EmployeeSummary value, $Res Function(EmployeeSummary) then) =
      _$EmployeeSummaryCopyWithImpl<$Res, EmployeeSummary>;
  @useResult
  $Res call({String creator, String employee});
}

/// @nodoc
class _$EmployeeSummaryCopyWithImpl<$Res, $Val extends EmployeeSummary>
    implements $EmployeeSummaryCopyWith<$Res> {
  _$EmployeeSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creator = null,
    Object? employee = null,
  }) {
    return _then(_value.copyWith(
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String,
      employee: null == employee
          ? _value.employee
          : employee // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EmployeeSummaryImplCopyWith<$Res>
    implements $EmployeeSummaryCopyWith<$Res> {
  factory _$$EmployeeSummaryImplCopyWith(_$EmployeeSummaryImpl value,
          $Res Function(_$EmployeeSummaryImpl) then) =
      __$$EmployeeSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String creator, String employee});
}

/// @nodoc
class __$$EmployeeSummaryImplCopyWithImpl<$Res>
    extends _$EmployeeSummaryCopyWithImpl<$Res, _$EmployeeSummaryImpl>
    implements _$$EmployeeSummaryImplCopyWith<$Res> {
  __$$EmployeeSummaryImplCopyWithImpl(
      _$EmployeeSummaryImpl _value, $Res Function(_$EmployeeSummaryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creator = null,
    Object? employee = null,
  }) {
    return _then(_$EmployeeSummaryImpl(
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String,
      employee: null == employee
          ? _value.employee
          : employee // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EmployeeSummaryImpl extends _EmployeeSummary {
  const _$EmployeeSummaryImpl({required this.creator, required this.employee})
      : super._();

  factory _$EmployeeSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmployeeSummaryImplFromJson(json);

// 主账号使用张数(计费)
  @override
  final String creator;
// 子账号使用张数(计费)
  @override
  final String employee;

  @override
  String toString() {
    return 'EmployeeSummary(creator: $creator, employee: $employee)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmployeeSummaryImpl &&
            (identical(other.creator, creator) || other.creator == creator) &&
            (identical(other.employee, employee) ||
                other.employee == employee));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, creator, employee);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EmployeeSummaryImplCopyWith<_$EmployeeSummaryImpl> get copyWith =>
      __$$EmployeeSummaryImplCopyWithImpl<_$EmployeeSummaryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EmployeeSummaryImplToJson(
      this,
    );
  }
}

abstract class _EmployeeSummary extends EmployeeSummary {
  const factory _EmployeeSummary(
      {required final String creator,
      required final String employee}) = _$EmployeeSummaryImpl;
  const _EmployeeSummary._() : super._();

  factory _EmployeeSummary.fromJson(Map<String, dynamic> json) =
      _$EmployeeSummaryImpl.fromJson;

  @override // 主账号使用张数(计费)
  String get creator;
  @override // 子账号使用张数(计费)
  String get employee;
  @override
  @JsonKey(ignore: true)
  _$$EmployeeSummaryImplCopyWith<_$EmployeeSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
