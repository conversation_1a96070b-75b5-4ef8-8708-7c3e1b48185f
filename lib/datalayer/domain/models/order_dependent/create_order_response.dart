import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_order_response.freezed.dart';
part 'create_order_response.g.dart';

// 创建订单
@freezed
class CreateOrderResponse with _$CreateOrderResponse {
  const CreateOrderResponse._();

  const factory CreateOrderResponse({
    required String qr_url,
    required String orderId,
    required bool continuePay,
    // 微信特有
    WakeupParam? wakeupParam,
    // 支付宝特有
    String? orderStr,
  }) = _CreateOrderResponse;

  factory CreateOrderResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderResponseFromJson(json);
}

// 唤醒参数
@freezed
class WakeupParam with _$WakeupParam {
  const factory WakeupParam({
    required String oid,
    required String appid,
    required String partnerid,
    required String noncestr,
    required String sign,
    required String timestamp,
    required String prepayid,
    required String package,
  }) = _WakeupParam;

  factory WakeupParam.fromJson(Map<String, dynamic> json) =>
      _$WakeupParam<PERSON>romJson(json);
}
