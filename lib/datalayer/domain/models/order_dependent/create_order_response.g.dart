// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_order_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreateOrderResponseImpl _$$CreateOrderResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateOrderResponseImpl(
      qr_url: json['qr_url'] as String,
      orderId: json['orderId'] as String,
      continuePay: json['continuePay'] as bool,
      wakeupParam: json['wakeupParam'] == null
          ? null
          : WakeupParam.fromJson(json['wakeupParam'] as Map<String, dynamic>),
      orderStr: json['orderStr'] as String?,
    );

Map<String, dynamic> _$$CreateOrderResponseImplToJson(
        _$CreateOrderResponseImpl instance) =>
    <String, dynamic>{
      'qr_url': instance.qr_url,
      'orderId': instance.orderId,
      'continuePay': instance.continuePay,
      'wakeupParam': instance.wakeupParam,
      'orderStr': instance.orderStr,
    };

_$WakeupParamImpl _$$WakeupParamImplFromJson(Map<String, dynamic> json) =>
    _$WakeupParamImpl(
      oid: json['oid'] as String,
      appid: json['appid'] as String,
      partnerid: json['partnerid'] as String,
      noncestr: json['noncestr'] as String,
      sign: json['sign'] as String,
      timestamp: json['timestamp'] as String,
      prepayid: json['prepayid'] as String,
      package: json['package'] as String,
    );

Map<String, dynamic> _$$WakeupParamImplToJson(_$WakeupParamImpl instance) =>
    <String, dynamic>{
      'oid': instance.oid,
      'appid': instance.appid,
      'partnerid': instance.partnerid,
      'noncestr': instance.noncestr,
      'sign': instance.sign,
      'timestamp': instance.timestamp,
      'prepayid': instance.prepayid,
      'package': instance.package,
    };
