// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'check_order_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CheckOrderStatusResponse _$CheckOrderStatusResponseFromJson(
    Map<String, dynamic> json) {
  return _CheckOrderStatusResponse.fromJson(json);
}

/// @nodoc
mixin _$CheckOrderStatusResponse {
  String get orderId => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CheckOrderStatusResponseCopyWith<CheckOrderStatusResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckOrderStatusResponseCopyWith<$Res> {
  factory $CheckOrderStatusResponseCopyWith(CheckOrderStatusResponse value,
          $Res Function(CheckOrderStatusResponse) then) =
      _$CheckOrderStatusResponseCopyWithImpl<$Res, CheckOrderStatusResponse>;
  @useResult
  $Res call({String orderId, String status});
}

/// @nodoc
class _$CheckOrderStatusResponseCopyWithImpl<$Res,
        $Val extends CheckOrderStatusResponse>
    implements $CheckOrderStatusResponseCopyWith<$Res> {
  _$CheckOrderStatusResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckOrderStatusResponseImplCopyWith<$Res>
    implements $CheckOrderStatusResponseCopyWith<$Res> {
  factory _$$CheckOrderStatusResponseImplCopyWith(
          _$CheckOrderStatusResponseImpl value,
          $Res Function(_$CheckOrderStatusResponseImpl) then) =
      __$$CheckOrderStatusResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String orderId, String status});
}

/// @nodoc
class __$$CheckOrderStatusResponseImplCopyWithImpl<$Res>
    extends _$CheckOrderStatusResponseCopyWithImpl<$Res,
        _$CheckOrderStatusResponseImpl>
    implements _$$CheckOrderStatusResponseImplCopyWith<$Res> {
  __$$CheckOrderStatusResponseImplCopyWithImpl(
      _$CheckOrderStatusResponseImpl _value,
      $Res Function(_$CheckOrderStatusResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
    Object? status = null,
  }) {
    return _then(_$CheckOrderStatusResponseImpl(
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckOrderStatusResponseImpl extends _CheckOrderStatusResponse {
  const _$CheckOrderStatusResponseImpl(
      {required this.orderId, required this.status})
      : super._();

  factory _$CheckOrderStatusResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckOrderStatusResponseImplFromJson(json);

  @override
  final String orderId;
  @override
  final String status;

  @override
  String toString() {
    return 'CheckOrderStatusResponse(orderId: $orderId, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckOrderStatusResponseImpl &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, orderId, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckOrderStatusResponseImplCopyWith<_$CheckOrderStatusResponseImpl>
      get copyWith => __$$CheckOrderStatusResponseImplCopyWithImpl<
          _$CheckOrderStatusResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckOrderStatusResponseImplToJson(
      this,
    );
  }
}

abstract class _CheckOrderStatusResponse extends CheckOrderStatusResponse {
  const factory _CheckOrderStatusResponse(
      {required final String orderId,
      required final String status}) = _$CheckOrderStatusResponseImpl;
  const _CheckOrderStatusResponse._() : super._();

  factory _CheckOrderStatusResponse.fromJson(Map<String, dynamic> json) =
      _$CheckOrderStatusResponseImpl.fromJson;

  @override
  String get orderId;
  @override
  String get status;
  @override
  @JsonKey(ignore: true)
  _$$CheckOrderStatusResponseImplCopyWith<_$CheckOrderStatusResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
