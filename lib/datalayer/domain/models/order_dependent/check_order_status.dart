import 'package:freezed_annotation/freezed_annotation.dart';

part 'check_order_status.freezed.dart';
part 'check_order_status.g.dart';

// 创建订单
@freezed
class CheckOrderStatusResponse with _$CheckOrderStatusResponse {
  const factory CheckOrderStatusResponse({
    required String orderId,
    required String status,
  }) = _CheckOrderStatusResponse;
  const CheckOrderStatusResponse._();

  factory CheckOrderStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$CheckOrderStatusResponseFromJson(json);
}
