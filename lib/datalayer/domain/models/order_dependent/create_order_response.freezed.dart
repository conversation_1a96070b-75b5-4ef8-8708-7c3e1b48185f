// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_order_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateOrderResponse _$CreateOrderResponseFromJson(Map<String, dynamic> json) {
  return _CreateOrderResponse.fromJson(json);
}

/// @nodoc
mixin _$CreateOrderResponse {
  String get qr_url => throw _privateConstructorUsedError;
  String get orderId => throw _privateConstructorUsedError;
  bool get continuePay => throw _privateConstructorUsedError; // 微信特有
  WakeupParam? get wakeupParam => throw _privateConstructorUsedError; // 支付宝特有
  String? get orderStr => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateOrderResponseCopyWith<CreateOrderResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateOrderResponseCopyWith<$Res> {
  factory $CreateOrderResponseCopyWith(
          CreateOrderResponse value, $Res Function(CreateOrderResponse) then) =
      _$CreateOrderResponseCopyWithImpl<$Res, CreateOrderResponse>;
  @useResult
  $Res call(
      {String qr_url,
      String orderId,
      bool continuePay,
      WakeupParam? wakeupParam,
      String? orderStr});

  $WakeupParamCopyWith<$Res>? get wakeupParam;
}

/// @nodoc
class _$CreateOrderResponseCopyWithImpl<$Res, $Val extends CreateOrderResponse>
    implements $CreateOrderResponseCopyWith<$Res> {
  _$CreateOrderResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? qr_url = null,
    Object? orderId = null,
    Object? continuePay = null,
    Object? wakeupParam = freezed,
    Object? orderStr = freezed,
  }) {
    return _then(_value.copyWith(
      qr_url: null == qr_url
          ? _value.qr_url
          : qr_url // ignore: cast_nullable_to_non_nullable
              as String,
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      continuePay: null == continuePay
          ? _value.continuePay
          : continuePay // ignore: cast_nullable_to_non_nullable
              as bool,
      wakeupParam: freezed == wakeupParam
          ? _value.wakeupParam
          : wakeupParam // ignore: cast_nullable_to_non_nullable
              as WakeupParam?,
      orderStr: freezed == orderStr
          ? _value.orderStr
          : orderStr // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $WakeupParamCopyWith<$Res>? get wakeupParam {
    if (_value.wakeupParam == null) {
      return null;
    }

    return $WakeupParamCopyWith<$Res>(_value.wakeupParam!, (value) {
      return _then(_value.copyWith(wakeupParam: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreateOrderResponseImplCopyWith<$Res>
    implements $CreateOrderResponseCopyWith<$Res> {
  factory _$$CreateOrderResponseImplCopyWith(_$CreateOrderResponseImpl value,
          $Res Function(_$CreateOrderResponseImpl) then) =
      __$$CreateOrderResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String qr_url,
      String orderId,
      bool continuePay,
      WakeupParam? wakeupParam,
      String? orderStr});

  @override
  $WakeupParamCopyWith<$Res>? get wakeupParam;
}

/// @nodoc
class __$$CreateOrderResponseImplCopyWithImpl<$Res>
    extends _$CreateOrderResponseCopyWithImpl<$Res, _$CreateOrderResponseImpl>
    implements _$$CreateOrderResponseImplCopyWith<$Res> {
  __$$CreateOrderResponseImplCopyWithImpl(_$CreateOrderResponseImpl _value,
      $Res Function(_$CreateOrderResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? qr_url = null,
    Object? orderId = null,
    Object? continuePay = null,
    Object? wakeupParam = freezed,
    Object? orderStr = freezed,
  }) {
    return _then(_$CreateOrderResponseImpl(
      qr_url: null == qr_url
          ? _value.qr_url
          : qr_url // ignore: cast_nullable_to_non_nullable
              as String,
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      continuePay: null == continuePay
          ? _value.continuePay
          : continuePay // ignore: cast_nullable_to_non_nullable
              as bool,
      wakeupParam: freezed == wakeupParam
          ? _value.wakeupParam
          : wakeupParam // ignore: cast_nullable_to_non_nullable
              as WakeupParam?,
      orderStr: freezed == orderStr
          ? _value.orderStr
          : orderStr // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateOrderResponseImpl extends _CreateOrderResponse {
  const _$CreateOrderResponseImpl(
      {required this.qr_url,
      required this.orderId,
      required this.continuePay,
      this.wakeupParam,
      this.orderStr})
      : super._();

  factory _$CreateOrderResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateOrderResponseImplFromJson(json);

  @override
  final String qr_url;
  @override
  final String orderId;
  @override
  final bool continuePay;
// 微信特有
  @override
  final WakeupParam? wakeupParam;
// 支付宝特有
  @override
  final String? orderStr;

  @override
  String toString() {
    return 'CreateOrderResponse(qr_url: $qr_url, orderId: $orderId, continuePay: $continuePay, wakeupParam: $wakeupParam, orderStr: $orderStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateOrderResponseImpl &&
            (identical(other.qr_url, qr_url) || other.qr_url == qr_url) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.continuePay, continuePay) ||
                other.continuePay == continuePay) &&
            (identical(other.wakeupParam, wakeupParam) ||
                other.wakeupParam == wakeupParam) &&
            (identical(other.orderStr, orderStr) ||
                other.orderStr == orderStr));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, qr_url, orderId, continuePay, wakeupParam, orderStr);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateOrderResponseImplCopyWith<_$CreateOrderResponseImpl> get copyWith =>
      __$$CreateOrderResponseImplCopyWithImpl<_$CreateOrderResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateOrderResponseImplToJson(
      this,
    );
  }
}

abstract class _CreateOrderResponse extends CreateOrderResponse {
  const factory _CreateOrderResponse(
      {required final String qr_url,
      required final String orderId,
      required final bool continuePay,
      final WakeupParam? wakeupParam,
      final String? orderStr}) = _$CreateOrderResponseImpl;
  const _CreateOrderResponse._() : super._();

  factory _CreateOrderResponse.fromJson(Map<String, dynamic> json) =
      _$CreateOrderResponseImpl.fromJson;

  @override
  String get qr_url;
  @override
  String get orderId;
  @override
  bool get continuePay;
  @override // 微信特有
  WakeupParam? get wakeupParam;
  @override // 支付宝特有
  String? get orderStr;
  @override
  @JsonKey(ignore: true)
  _$$CreateOrderResponseImplCopyWith<_$CreateOrderResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WakeupParam _$WakeupParamFromJson(Map<String, dynamic> json) {
  return _WakeupParam.fromJson(json);
}

/// @nodoc
mixin _$WakeupParam {
  String get oid => throw _privateConstructorUsedError;
  String get appid => throw _privateConstructorUsedError;
  String get partnerid => throw _privateConstructorUsedError;
  String get noncestr => throw _privateConstructorUsedError;
  String get sign => throw _privateConstructorUsedError;
  String get timestamp => throw _privateConstructorUsedError;
  String get prepayid => throw _privateConstructorUsedError;
  String get package => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WakeupParamCopyWith<WakeupParam> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WakeupParamCopyWith<$Res> {
  factory $WakeupParamCopyWith(
          WakeupParam value, $Res Function(WakeupParam) then) =
      _$WakeupParamCopyWithImpl<$Res, WakeupParam>;
  @useResult
  $Res call(
      {String oid,
      String appid,
      String partnerid,
      String noncestr,
      String sign,
      String timestamp,
      String prepayid,
      String package});
}

/// @nodoc
class _$WakeupParamCopyWithImpl<$Res, $Val extends WakeupParam>
    implements $WakeupParamCopyWith<$Res> {
  _$WakeupParamCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oid = null,
    Object? appid = null,
    Object? partnerid = null,
    Object? noncestr = null,
    Object? sign = null,
    Object? timestamp = null,
    Object? prepayid = null,
    Object? package = null,
  }) {
    return _then(_value.copyWith(
      oid: null == oid
          ? _value.oid
          : oid // ignore: cast_nullable_to_non_nullable
              as String,
      appid: null == appid
          ? _value.appid
          : appid // ignore: cast_nullable_to_non_nullable
              as String,
      partnerid: null == partnerid
          ? _value.partnerid
          : partnerid // ignore: cast_nullable_to_non_nullable
              as String,
      noncestr: null == noncestr
          ? _value.noncestr
          : noncestr // ignore: cast_nullable_to_non_nullable
              as String,
      sign: null == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as String,
      prepayid: null == prepayid
          ? _value.prepayid
          : prepayid // ignore: cast_nullable_to_non_nullable
              as String,
      package: null == package
          ? _value.package
          : package // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WakeupParamImplCopyWith<$Res>
    implements $WakeupParamCopyWith<$Res> {
  factory _$$WakeupParamImplCopyWith(
          _$WakeupParamImpl value, $Res Function(_$WakeupParamImpl) then) =
      __$$WakeupParamImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String oid,
      String appid,
      String partnerid,
      String noncestr,
      String sign,
      String timestamp,
      String prepayid,
      String package});
}

/// @nodoc
class __$$WakeupParamImplCopyWithImpl<$Res>
    extends _$WakeupParamCopyWithImpl<$Res, _$WakeupParamImpl>
    implements _$$WakeupParamImplCopyWith<$Res> {
  __$$WakeupParamImplCopyWithImpl(
      _$WakeupParamImpl _value, $Res Function(_$WakeupParamImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oid = null,
    Object? appid = null,
    Object? partnerid = null,
    Object? noncestr = null,
    Object? sign = null,
    Object? timestamp = null,
    Object? prepayid = null,
    Object? package = null,
  }) {
    return _then(_$WakeupParamImpl(
      oid: null == oid
          ? _value.oid
          : oid // ignore: cast_nullable_to_non_nullable
              as String,
      appid: null == appid
          ? _value.appid
          : appid // ignore: cast_nullable_to_non_nullable
              as String,
      partnerid: null == partnerid
          ? _value.partnerid
          : partnerid // ignore: cast_nullable_to_non_nullable
              as String,
      noncestr: null == noncestr
          ? _value.noncestr
          : noncestr // ignore: cast_nullable_to_non_nullable
              as String,
      sign: null == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as String,
      prepayid: null == prepayid
          ? _value.prepayid
          : prepayid // ignore: cast_nullable_to_non_nullable
              as String,
      package: null == package
          ? _value.package
          : package // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WakeupParamImpl implements _WakeupParam {
  const _$WakeupParamImpl(
      {required this.oid,
      required this.appid,
      required this.partnerid,
      required this.noncestr,
      required this.sign,
      required this.timestamp,
      required this.prepayid,
      required this.package});

  factory _$WakeupParamImpl.fromJson(Map<String, dynamic> json) =>
      _$$WakeupParamImplFromJson(json);

  @override
  final String oid;
  @override
  final String appid;
  @override
  final String partnerid;
  @override
  final String noncestr;
  @override
  final String sign;
  @override
  final String timestamp;
  @override
  final String prepayid;
  @override
  final String package;

  @override
  String toString() {
    return 'WakeupParam(oid: $oid, appid: $appid, partnerid: $partnerid, noncestr: $noncestr, sign: $sign, timestamp: $timestamp, prepayid: $prepayid, package: $package)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WakeupParamImpl &&
            (identical(other.oid, oid) || other.oid == oid) &&
            (identical(other.appid, appid) || other.appid == appid) &&
            (identical(other.partnerid, partnerid) ||
                other.partnerid == partnerid) &&
            (identical(other.noncestr, noncestr) ||
                other.noncestr == noncestr) &&
            (identical(other.sign, sign) || other.sign == sign) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.prepayid, prepayid) ||
                other.prepayid == prepayid) &&
            (identical(other.package, package) || other.package == package));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, oid, appid, partnerid, noncestr,
      sign, timestamp, prepayid, package);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WakeupParamImplCopyWith<_$WakeupParamImpl> get copyWith =>
      __$$WakeupParamImplCopyWithImpl<_$WakeupParamImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WakeupParamImplToJson(
      this,
    );
  }
}

abstract class _WakeupParam implements WakeupParam {
  const factory _WakeupParam(
      {required final String oid,
      required final String appid,
      required final String partnerid,
      required final String noncestr,
      required final String sign,
      required final String timestamp,
      required final String prepayid,
      required final String package}) = _$WakeupParamImpl;

  factory _WakeupParam.fromJson(Map<String, dynamic> json) =
      _$WakeupParamImpl.fromJson;

  @override
  String get oid;
  @override
  String get appid;
  @override
  String get partnerid;
  @override
  String get noncestr;
  @override
  String get sign;
  @override
  String get timestamp;
  @override
  String get prepayid;
  @override
  String get package;
  @override
  @JsonKey(ignore: true)
  _$$WakeupParamImplCopyWith<_$WakeupParamImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
