import 'package:turing_art/datalayer/domain/enums/order_status.dart';

// 订单事件类
class OrderEvent {
  final OrderStatus status;
  final String message;
  final String? orderId; // 订单ID
  final String? purchasePlanId; // 购买计划ID
  final String? purchasePlanName; // 购买计划名称
  final String? purchasePlanPrice; // 购买计划价格

  OrderEvent(this.status, this.message,
      {this.orderId,
      this.purchasePlanId,
      this.purchasePlanName,
      this.purchasePlanPrice});
}
