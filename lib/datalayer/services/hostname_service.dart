import 'dart:io';

import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 主机名服务 - 提供安全的主机名获取方法
///
/// 在Windows平台上使用原生方法获取主机名，解决中文主机名导致的UTF-8编码问题
/// 在其他平台上使用dart:io提供的Platform.localHostname
class HostnameService {
  static const MethodChannel _channel = MethodChannel('com.turingart.hostname');

  /// 获取当前设备的主机名
  ///
  /// 返回主机名字符串，如果获取失败返回'unknown-host'
  static Future<String> getHostname() async {
    try {
      if (Platform.isWindows) {
        // 在Windows平台使用原生实现
        final String hostname = await _channel.invokeMethod('getHostname');
        return hostname;
      } else {
        // 在其他平台使用dart:io的实现
        return Platform.localHostname;
      }
    } catch (e) {
      PGLog.d('获取主机名失败: $e');
      return 'unknown-host';
    }
  }
}
