import 'package:turing_art/datalayer/domain/models/aigc_export_history/aigc_export_history_model.dart';
import 'package:turing_art/datalayer/domain/models/export_history/export_history_model.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/api/export_history/export_history_api_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 导出交易历史记录服务接口
abstract class ExportHistoryService {
  /// 获取导出交易历史记录
  ///
  /// [page] 页码，默认为1
  /// [pageSize] 每页数量，默认为20
  Future<ExportHistoryModel?> fetchExportHistory({
    int page = 1,
    int pageSize = 20,
    int? createdAfter,
    int? createdBefore,
    String? employeeIds,
    int? type,
  });

  Future<AIGCExportHistoryModel?> fetchAIGCExportHistory({
    int page = 1,
    int pageSize = 20,
    String? startTime,
    String? endTime,
  });
}

/// 导出交易历史记录服务实现
class ExportHistoryServiceImpl implements ExportHistoryService {
  final ApiClient _apiClient;
  late final ExportHistoryApiService _exportHistoryApiService;

  ExportHistoryServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _exportHistoryApiService = ExportHistoryApiService(dio);
  }

  @override
  Future<ExportHistoryModel?> fetchExportHistory({
    int page = 1,
    int pageSize = 20,
    int? createdAfter,
    int? createdBefore,
    String? employeeIds,
    int? type,
  }) async {
    try {
      final response = await _exportHistoryApiService.fetchExportHistory(
        page: page.toString(),
        pageSize: pageSize.toString(),
        createdAfter: createdAfter,
        createdBefore: createdBefore,
        employeeIds: employeeIds,
        type: type,
      );
      PGLog.d('获取导出交易历史记录成功: $response');
      return response;
    } catch (e) {
      PGLog.e('获取导出交易历史记录失败: $e');
      return null;
    }
  }

  @override
  Future<AIGCExportHistoryModel?> fetchAIGCExportHistory({
    int page = 1,
    int pageSize = 20,
    String? startTime,
    String? endTime,
  }) async {
    try {
      final response = await _exportHistoryApiService.fetchAIGCExportHistory(
        page: page.toString(),
        pageSize: pageSize.toString(),
        startTime: startTime,
        endTime: endTime,
      );
      PGLog.d('获取AIGC导出历史记录成功: $response');
      return response;
    } catch (e) {
      PGLog.e('获取AIGC导出历史记录失败: $e');
      return null;
    }
  }
}
