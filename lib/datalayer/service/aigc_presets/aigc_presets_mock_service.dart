import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.dart';
import 'package:uuid/uuid.dart';

class AigcPresetsMockService {
  static Future<List<AigcPresetsModel>> getAigcMockPresets(String scene) async {
    return [
      AigcPresetsModel(
        name: 'AI 绘画1',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画2',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画3',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画4',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画5',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画6',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画7',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画8',
        status: 'completed',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画9',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        status: 'running',
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
      AigcPresetsModel(
        name: 'AI 绘画10',
        id: const Uuid().v4(),
        createAt: 1749192432,
        updateAt: 1749192432,
        status: 'running',
        effects: const [
          AigcPresetsEffect(
            effectCode: 'effect_code_1',
            name: 'AI 绘画1-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: true,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_2',
            name: 'AI 绘画2-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
          AigcPresetsEffect(
            effectCode: 'effect_code_3',
            name: 'AI 绘画3-1',
            status: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_presets/aigc_presets_1.png',
            isInUse: false,
          ),
        ],
      ),
    ];
  }
}
