import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_respose.dart';

part 'aigc_presets_api_service.g.dart';

/// 主题预设 API 服务接口
@RestApi()
abstract class AigcPcPresetsApiService {
  factory AigcPcPresetsApiService(Dio dio, {String? baseUrl}) =
      _AigcPcPresetsApiService;

  /// 获取主题预设列表
  @GET("/art-aigc/v1/preset/list")
  Future<AigcPcPresetsResponse> getAigcPcPresets({
    @Query('scene') required String scene,
    @Query('page') required int page,
    @Query('page_size') required int pageSize,
  });

  // 创建预设
  @POST("/art-aigc/v1/preset/create")
  Future<AigcPcPresetsDetailResponse> createAigcPcPreset(
      @Body() Map<String, dynamic> request);

  // 获取预设详情
  @GET("/art-aigc/v1/preset/{preset_id}/detail")
  Future<AigcPcPresetsDetailResponse> getAigcPcPresetDetail(
    @Path("preset_id") String presetId,
  );

  // 重新生成预设
  @POST("/art-aigc/v1/preset/{preset_id}/regenerate")
  Future<AigcPcPresetsDetailResponse> regenerateAigcPcPreset(
    @Path("preset_id") String presetId,
    @Body() Map<String, dynamic> params,
  );

  // 删除预设
  @POST("/art-aigc/v1/preset/{preset_id}/delete")
  Future<void> deleteAigcPcPreset(
    @Path("preset_id") String presetId,
  );

  // 预设效果图删除
  @POST("/art-aigc/v1/preset/{preset_id}/effect/delete")
  Future<void> deleteAigcPcPresetEffect(
    @Path("preset_id") String presetId,
    @Body() Map<String, dynamic> effects,
  );

  // 预览效果图选择
  @POST("/art-aigc/v1/preset/{preset_id}/effect/select")
  Future<void> selectAigcPcPresetEffect(
    @Path("preset_id") String presetId,
    @Body() Map<String, dynamic> effects,
  );

  // 轮询预设
  @POST("/art-aigc/v1/preset/status")
  Future<List<AigcPcPresetsLoopModel>> pollAigcPcPreset(
    @Body() Map<String, dynamic> request,
  );
}
