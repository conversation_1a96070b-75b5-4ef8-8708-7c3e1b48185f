import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';

import '../../../utils/file_manager.dart';

class AigcLocalDataService {
  AigcLocalDataService() {
    _fileManager = FileManager();
  }

  late final FileManager _fileManager;

  /// 保存aigc预设至本地文件
  Future<void> saveAigcPresets(
      List<AigcPcPresetsDetailResponse> presets) async {
    final presetsDir = _fileManager.getAigcDirectory(isPresets: true);
    final presetsFile = File(path.join(presetsDir.path, 'presets.json'));
    await presetsFile
        .writeAsString(jsonEncode(presets.map((p) => p.toJson()).toList()));
  }

  /// 读取本地aigc预设列表
  Future<List<AigcPcPresetsDetailResponse>> getAigcPresets() async {
    final presetsDir = _fileManager.getAigcDirectory(isPresets: true);
    final presetsFile = File(path.join(presetsDir.path, 'presets.json'));
    final presets = jsonDecode(presetsFile.readAsStringSync());
    return presets
        .map<AigcPcPresetsDetailResponse>(
            (e) => AigcPcPresetsDetailResponse.fromJson(e))
        .toList();
  }

  // 保存aigc样本至本地文件
  Future<void> saveAigcSamples(
      String projectId, List<AigcSampleModel> samples) async {
    final samplesDir = _fileManager.getAigcDirectory(isPresets: false);
    final samplesFile =
        File(path.join(samplesDir.path, 'samples_$projectId.json'));
    await samplesFile
        .writeAsString(jsonEncode(samples.map((s) => s.toJson()).toList()));
  }

  /// 读取本地aigc样本列表
  Future<List<AigcSampleModel>> getAigcSamples(String projectId) async {
    final samplesDir = _fileManager.getAigcDirectory(isPresets: false);
    final samplesFile =
        File(path.join(samplesDir.path, 'samples_$projectId.json'));
    final samples = jsonDecode(samplesFile.readAsStringSync());
    return samples.map((e) => AigcSampleModel.fromJson(e)).toList();
  }

  Future<String?> getAigcMattingSDKMaskingFilePath(
      String projectId, String fileId) async {
    final tempDir = _fileManager.getAigcMattingDirectory(projectId: projectId);
    final mattingFile =
        File(path.join(tempDir.path, '$fileId\\sdkMasking.png'));
    if (mattingFile.existsSync()) {
      return mattingFile.path;
    }
    return null;
  }

  Future<String?> getAigcMattingDrawMaskingFilePath(
      String projectId, String fileId) async {
    final tempDir = _fileManager.getAigcMattingDirectory(projectId: projectId);
    final mattingFile =
        File(path.join(tempDir.path, '$fileId\\drawMasking.png'));
    if (mattingFile.existsSync()) {
      return mattingFile.path;
    }
    return null;
  }

  Future<void> saveAigcMattingSDKMaskingFile(
      String projectId, String fileId, Uint8List imageDataBytes) async {
    final tempDir = _fileManager.getAigcMattingDirectory(projectId: projectId);
    final mattingFile =
        File(path.join(tempDir.path, '$fileId\\sdkMasking.png'));
    await mattingFile.writeAsBytes(imageDataBytes);
  }

  Future<void> saveAigcMattingDrawMaskingFile(
      String projectId, String fileId, Uint8List imageDataBytes) async {
    final tempDir = _fileManager.getAigcMattingDirectory(projectId: projectId);
    final mattingFile = File(path.join(tempDir.path, 'drawMasking.png'));
    await mattingFile.writeAsBytes(imageDataBytes);
  }
}
