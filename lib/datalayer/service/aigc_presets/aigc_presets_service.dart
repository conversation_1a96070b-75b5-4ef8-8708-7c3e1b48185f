import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.dart';
import 'package:turing_art/datalayer/service/aigc_presets/aigc_presets_api_service.dart';
import 'package:turing_art/datalayer/service/aigc_presets/aigc_presets_mock_service.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/services/hostname_service.dart';

abstract class AigcPresetsService {
  // 获取预设列表
  Future<List<AigcPcPresetsDetailResponse>> getAigcPresets(String scene);

  // 获取Mock预设列表
  Future<List<AigcPresetsModel>> getAigcMockPresets(String scene);

  // 获取预设详情
  Future<AigcPcPresetsDetailResponse> getAigcPresetsDetail(String presetId);

  // 创建预设
  Future<AigcPcPresetsDetailResponse> createAigcPresets(String name,
      String requirementSupplement, String photoUrl, String maskUrl, int count);

  // 重新生成预设
  Future<AigcPcPresetsDetailResponse> regenerateAigcPresets(
      String presetId, String supplement, int regenerateCount);

  // 删除预设
  Future<void> deleteAigcPresets(String presetId);

  // 删除预设效果图
  Future<void> deleteAigcPresetsEffect(
      String presetId, List<String> effectCodes);

  // 轮询预设
  Future<List<AigcPcPresetsLoopModel>> pollAigcPresets(List<String> presetIds);

  // 选择预设效果图
  Future<void> selectAigcPresetsEffect(
      String presetId, List<String> effectCode);
}

class AigcPresetsServiceImpl implements AigcPresetsService {
  final ApiClient _apiClient;
  late final AigcPcPresetsApiService _aigcPcPresetsApiService;

  AigcPresetsServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _aigcPcPresetsApiService = AigcPcPresetsApiService(dio);
  }

  @override
  Future<List<AigcPcPresetsDetailResponse>> getAigcPresets(String scene) async {
    // final request = AigcPresetsListRequest(
    //   page: 1,
    //   pageSize: 10000,
    //   scene: scene,
    // );
    final response = await _aigcPcPresetsApiService.getAigcPcPresets(
      scene: scene,
      page: 1,
      pageSize: 10000,
    );
    List<AigcPcPresetsDetailResponse> presets = response.presets;

    // if (presets.isEmpty) {
    //   presets = await getAigcMockPresets(scene);
    // }
    return presets;
  }

  @override
  Future<List<AigcPresetsModel>> getAigcMockPresets(String scene) async {
    return AigcPresetsMockService.getAigcMockPresets(scene);
  }

  @override
  Future<AigcPcPresetsDetailResponse> getAigcPresetsDetail(
      String presetId) async {
    return _aigcPcPresetsApiService.getAigcPcPresetDetail(presetId);
  }

  @override
  Future<AigcPcPresetsDetailResponse> createAigcPresets(
    String name,
    String requirementSupplement,
    String photoUrl,
    String maskUrl,
    int count,
  ) async {
    // 获取主机名
    final hostname = await HostnameService.getHostname();

    final requestData = {
      'name': name,
      'requirement_supplement': requirementSupplement,
      'photo_url': photoUrl,
      'mask_image_url': maskUrl,
      'count': count,
      'hostname': hostname,
    };

    return _aigcPcPresetsApiService.createAigcPcPreset(requestData);
  }

  @override
  Future<AigcPcPresetsDetailResponse> regenerateAigcPresets(
      String presetId, String supplement, int regenerateCount) async {
    // 获取主机名
    final hostname = await HostnameService.getHostname();

    return _aigcPcPresetsApiService.regenerateAigcPcPreset(presetId, {
      'requirement_supplement': supplement,
      'count': regenerateCount,
      'hostname': hostname,
    });
  }

  @override
  Future<void> deleteAigcPresets(String presetId) async {
    return _aigcPcPresetsApiService.deleteAigcPcPreset(presetId);
  }

  @override
  Future<void> deleteAigcPresetsEffect(
      String presetId, List<String> effectCodes) async {
    return _aigcPcPresetsApiService.deleteAigcPcPresetEffect(
      presetId,
      {
        'effect_codes': effectCodes,
      },
    );
  }

  @override
  Future<List<AigcPcPresetsLoopModel>> pollAigcPresets(
      List<String> presetIds) async {
    return _aigcPcPresetsApiService.pollAigcPcPreset(
      {
        'ids': presetIds,
      },
    );
  }

  @override
  Future<void> selectAigcPresetsEffect(
      String presetId, List<String> effectCodes) async {
    return _aigcPcPresetsApiService.selectAigcPcPresetEffect(
      presetId,
      {
        'effect_codes': effectCodes,
      },
    );
  }
}
