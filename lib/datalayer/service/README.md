# 任务队列系统重构

## 概述

本次重构将原有的AIGC预处理服务拆分为两个独立的包：

1. **通用任务队列系统** (`task_queue_system`) - 业务无关的基于isolate的优先级任务编排和执行系统
2. **AIGC业务处理器** (`aigc_processors`) - 业务相关的枚举定义、任务处理器、编排策略等

## 架构设计

### 通用任务队列系统 (`task_queue_system/`)

这是一个完全业务无关的通用任务队列系统，提供：

#### 核心组件
- **`GenericTask`** - 通用任务抽象类
- **`GenericScheduler<TTask, TQueue>`** - 通用多队列调度器
- **`TaskQueue<T>`** - 泛型优先级队列
- **`WorkerChannel`** - 工作器通信通道
- **`TaskProcessorRegistry`** - 统一的处理器注册表

#### 处理器注册表
`TaskProcessorRegistry` 是系统的核心注册表，提供：
- 处理器注册和获取
- 类型安全的处理器查询
- 调试信息和状态管理

```dart
// 注册处理器
TaskProcessorRegistry.register(processor);

// 获取处理器
final processor = TaskProcessorRegistry.getProcessor('thumbnail');

// 类型安全查询
final aigcProcessor = TaskProcessorRegistry.getProcessorByType<AigcTaskProcessor>();

// 调试信息
print(TaskProcessorRegistry.debugInfo);
```

### AIGC业务处理器 (`aigc_processors/`)

基于通用系统构建的业务实现：

#### 处理器管理
- **`AigcProcessorFactory`** - 简化的处理器工厂，仅负责初始化
- **`AigcTaskProcessor`** - AIGC处理器基类
- **具体处理器** - 各种业务处理器实现

#### 简化的架构
```dart
// 初始化时注册所有处理器
AigcProcessorFactory.initialize();

// 运行时通过统一注册表获取处理器
final processor = TaskProcessorRegistry.getProcessor(processorKey);
```

#### ⚠️ 重要：Isolate处理器初始化

由于Dart的Isolate之间不共享内存，每个工作器Isolate都需要独立初始化处理器：

```dart
// ❌ 错误：在主线程初始化，Isolate中无法访问
AigcProcessorFactory.initialize(); // 主线程
TaskProcessorRegistry.getProcessor(key); // Isolate中 - 找不到处理器！

// ✅ 正确：在每个Isolate中独立初始化
final channel = await WorkerChannel.create(
  workerId,
  initializer: AigcProcessorFactory.initialize, // 在Isolate中执行
);
```

这种设计确保：
1. 每个Isolate都有完整的处理器注册表
2. 避免了"找不到处理器"的运行时错误
3. 保持了Isolate之间的独立性

这种设计的优势：
1. **职责清晰** - 工厂只负责初始化，注册表负责管理
2. **避免重复** - 单一的处理器存储和管理机制
3. **类型安全** - 支持泛型查询和类型检查
4. **易于扩展** - 统一的注册和查询接口
5. **Isolate安全** - 每个Isolate都有独立的处理器注册表

## 使用方法

### 基本使用

```dart
import 'task_queue_system/task_queue_system.dart';
import 'aigc_processors/aigc_processors.dart';

// 创建AIGC服务
final aigcService = AigcService(maxWorkers: 2);

// 初始化服务
await aigcService.initialize();

// 提交任务
await aigcService.requestImage(
  inputPath: '/path/to/image.jpg',
  taskType: TaskType.thumbnail,
  priority: TaskPriority.normalPreview(0),
);

// 监听结果
aigcService.messageStream.listen((message) {
  if (message is GenericTaskResultMessage) {
    print('任务完成: ${message.taskId}');
  }
});
```

### 优先级管理

```dart
// 选中图片的预览任务（最高优先级）
await aigcService.requestImage(
  inputPath: '/path/to/urgent.jpg',
  taskType: TaskType.thumbnail,
  priority: TaskPriority.selectedPreview,
);

// 普通预览任务
await aigcService.requestImage(
  inputPath: '/path/to/normal.jpg',
  taskType: TaskType.thumbnail,
  priority: TaskPriority.normalPreview(0),
);

// 立即执行模式
await aigcService.requestImage(
  inputPath: '/path/to/immediate.jpg',
  taskType: TaskType.mask,
  executeNow: true,
);
```

### 策略切换

```dart
// 切换到打样优先模式
aigcService.enableProofPriorityMode();

// 切换到导出优先模式
aigcService.enableExportPriorityMode();

// 切换到默认模式
aigcService.enableDefaultPriorityMode();
```

### 任务管理

```dart
// 移除指定任务
final removed = aigcService.removeTask(
  inputPath: '/path/to/image.jpg',
  taskType: TaskType.thumbnail,
);

// 根据路径移除所有任务
final removedCount = aigcService.removeTasksByPath('/path/to/image.jpg');

// 根据任务类型移除所有任务
final removedByType = aigcService.removeTasksByType(TaskType.mask);

// 根据优先级范围移除任务
final removedByPriority = aigcService.removeTasksByPriorityRange(
  minPriority: 1000,
  maxPriority: 2000,
);

// 清空所有任务
aigcService.clearAllTasks();

// 检查任务是否存在
final exists = aigcService.hasTask(
  inputPath: '/path/to/image.jpg',
  taskType: TaskType.thumbnail,
);

// 获取任务详细信息
final task = aigcService.getTask(
  inputPath: '/path/to/image.jpg',
  taskType: TaskType.thumbnail,
);

// 获取详细统计信息
final stats = aigcService.getDetailedStats();
```

## 扩展性

### 添加新的任务类型

1. 在`TaskType`枚举中添加新类型
2. 创建对应的处理器类继承`AigcTaskProcessor`
3. 在`AigcProcessorFactory.initialize()`中添加新处理器的注册
4. 在调度策略中配置队列映射

### 添加新的队列类型

1. 在`QueueType`枚举中添加新队列
2. 在调度策略中配置队列优先级
3. 更新任务类型到队列的映射

### 创建自定义调度策略

```dart
class CustomAigcStrategy extends AigcSchedulingStrategy {
  @override
  String get name => '自定义策略';

  @override
  Map<QueueType, int> get queuePriorities => {
    // 自定义队列优先级配置
  };

  @override
  Map<Type, QueueType> get taskTypeToQueue => {
    // 自定义任务类型到队列映射
  };
}
```

## 优势

1. **关注点分离**: 通用系统与业务逻辑完全分离
2. **高度可扩展**: 支持任意类型的任务和处理器
3. **策略可配置**: 动态切换调度策略适应不同场景
4. **性能优化**: 基于isolate的并发处理和优先级队列
5. **类型安全**: 完整的泛型设计确保类型安全
6. **易于测试**: 各组件职责清晰，便于单元测试

## 重构前后对比

### 重构前
- 业务逻辑与队列系统耦合
- 难以扩展新的任务类型
- 调度策略硬编码
- 缺少类型安全

### 重构后
- 完全解耦的设计
- 插件化的处理器架构
- 策略模式的调度系统
- 完整的泛型支持

这个重构为后续的功能扩展和维护提供了坚实的基础。 