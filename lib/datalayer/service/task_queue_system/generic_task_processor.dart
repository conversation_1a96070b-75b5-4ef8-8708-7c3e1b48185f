import 'dart:isolate';

import 'package:turing_art/datalayer/service/task_queue_system/generic_message.dart';

/// 通用任务处理器抽象类
abstract class GenericTaskProcessor<
    T extends GenericTaskMessage<dynamic, dynamic>> {
  /// 处理器标识符
  String get processorKey;

  /// 处理任务的主要方法
  Future<void> process(
    T message,
    SendPort sendToMain,
    String workerId,
  );

  /// 发送进度更新
  void sendProgress(
      T message, String status, double progress, SendPort sendToMain) {
    sendToMain.send(
        message.createProgressMessage(status: status, progress: progress));
  }

  /// 发送成功结果
  void sendSuccess(
      T message, dynamic result, DateTime startTime, SendPort sendToMain) {
    sendToMain.send(message.createSuccessMessage(
      resultData: result,
      processingTime: DateTime.now().difference(startTime),
    ));
  }

  /// 发送失败结果
  void sendError(
      T message, String error, DateTime startTime, SendPort sendToMain) {
    sendToMain.send(message.createFailureMessage(
      errorMessage: error,
      processingTime: DateTime.now().difference(startTime),
    ));
  }
}
