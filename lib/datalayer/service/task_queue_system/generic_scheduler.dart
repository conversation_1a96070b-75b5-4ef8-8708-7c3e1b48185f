import 'package:turing_art/utils/pg_log.dart';

import 'generic_task.dart';
import 'task_queue.dart';

/// 通用调度策略接口
abstract class SchedulingStrategy<TTask extends GenericTask,
    TQueue extends Enum> {
  /// 策略名称（用于调试）
  String get name;

  /// 队列优先级配置（数值越小优先级越高）
  Map<TQueue, int> get queuePriorities;

  /// 任务类型到队列的映射配置
  Map<Type, TQueue> get taskTypeToQueue;

  /// 自定义优先级计算函数（可选）
  int? customPriorityCalculator(TTask task) => null;

  /// 计算任务的有效优先级
  int calculateEffectivePriority(TTask task) {
    // 如果有自定义计算器，优先使用
    final customPriority = customPriorityCalculator(task);
    if (customPriority != null) {
      return customPriority;
    }

    // 默认直接返回任务优先级
    return task.priority;
  }

  /// 获取队列优先级
  int getQueuePriority(TQueue queueType) {
    return queuePriorities[queueType] ?? 999;
  }

  /// 获取任务类型对应的队列
  TQueue getQueueForTaskType(Type taskType) {
    return taskTypeToQueue[taskType] ?? queuePriorities.keys.first;
  }

  /// 获取支持的任务类型
  List<Type> get supportedTaskTypes => taskTypeToQueue.keys.toList();

  /// 获取支持的队列类型
  List<TQueue> get supportedQueueTypes => queuePriorities.keys.toList();
}

/// 通用多队列任务调度器
/// 实现队列和任务类型分离的架构：队列按优先级排序，队列内任务按任务优先级排序
/// 使用策略模式，支持动态切换调度策略
class GenericScheduler<TTask extends GenericTask, TQueue extends Enum> {
  // 使用Map统一管理所有队列（按队列类型分组）
  late final Map<TQueue, TaskQueue<TTask>> _taskQueues;
  late final TaskQueue<TTask> _executionQueue;

  // 当前使用的调度策略
  late SchedulingStrategy<TTask, TQueue> _currentStrategy;

  GenericScheduler(
      {required SchedulingStrategy<TTask, TQueue> initialStrategy}) {
    _currentStrategy = initialStrategy;

    // 初始化所有支持的队列类型
    _taskQueues = <TQueue, TaskQueue<TTask>>{};
    for (final queueType in _currentStrategy.supportedQueueTypes) {
      _taskQueues[queueType] = TaskQueue<TTask>();
    }
    _executionQueue = TaskQueue<TTask>();
  }

  /// 获取指定任务类型的队列长度
  int getTaskCount(Type taskType) {
    final queueType = _currentStrategy.getQueueForTaskType(taskType);
    return _taskQueues[queueType]?.length ?? 0;
  }

  int get totalTaskCount =>
      _taskQueues.values.fold(0, (sum, queue) => sum + queue.length) +
      _executionQueue.length;

  /// 切换调度策略
  void setStrategy(SchedulingStrategy<TTask, TQueue> strategy) {
    if (_currentStrategy != strategy) {
      _currentStrategy = strategy;
    }
  }

  /// 获取当前策略信息
  String get currentStrategyName => _currentStrategy.name;

  /// 添加任务到队列
  void addTask(TTask task, {bool executeNow = false}) {
    final queueType = _currentStrategy.getQueueForTaskType(task.runtimeType);
    final queue = _taskQueues[queueType];

    if (queue == null) {
      PGLog.d('⚠️ 未找到队列类型: $queueType');
      return;
    }

    if (executeNow) {
      // 如果是立即执行，直接加入执行队列
      PGLog.d('⚠️ 立即执行: $task');
      _executionQueue.add(task);
      return;
    }

    if (!queue.add(task)) {
      PGLog.d('⚠️ 任务已存在: $task');
    }
  }

  /// 更新任务优先级
  bool updateTaskPriorityById(String taskId, Type taskType, int newPriority) {
    final queueType = _currentStrategy.getQueueForTaskType(taskType);
    final queue = _taskQueues[queueType];

    if (queue == null) {
      return false;
    }

    bool updated = queue.updatePriority(taskId, newPriority);
    // 按需调度模式下，不需要重新调度执行队列
    // 优先级变化会在下次getNextTask()时自然体现
    return updated;
  }

  /// 获取下一个要执行的任务
  TTask? getNextTask() {
    // 首先检查执行队列是否有任务
    final executionTask = _executionQueue.removeFirst();
    if (executionTask != null) {
      return executionTask;
    }

    // 执行队列为空，从任务队列中选择最高优先级任务
    return _selectNextTaskByPriority();
  }

  /// 选择下一个任务 - 使用当前策略进行优先级计算
  TTask? _selectNextTaskByPriority() {
    // 收集所有非空的候选任务及其有效优先级
    final candidates =
        <({TTask task, int priority, TQueue queueType, int queueOrder})>[];

    for (final entry in _taskQueues.entries) {
      final queueType = entry.key;
      final queue = entry.value;
      final firstTask = queue.first;

      if (firstTask != null) {
        final queueOrder = _currentStrategy.getQueuePriority(queueType);
        candidates.add((
          task: firstTask,
          priority: _currentStrategy.calculateEffectivePriority(firstTask),
          queueType: queueType,
          queueOrder: queueOrder
        ));
      }
    }

    if (candidates.isEmpty) {
      return null;
    }

    // 按队列优先级排序，然后按任务优先级排序
    candidates.sort((a, b) {
      // 首先按队列优先级排序
      final queueComparison = a.queueOrder.compareTo(b.queueOrder);
      if (queueComparison != 0) {
        return queueComparison;
      }

      // 队列优先级相同时，按任务优先级排序
      return a.priority
          .compareTo(b.priority); // ignore: inference_failure_on_return_type
    });

    // 选择最高优先级的任务并从对应队列移除
    final selected = candidates.first;
    final queue = _taskQueues[selected.queueType]!;
    final task = queue.removeFirst();

    if (task != null) {
      PGLog.d(
          '🎯 选择任务: ${task.taskId} (${selected.queueType}, 队列优先级: ${selected.queueOrder}, 任务优先级: ${selected.priority}, 策略: ${_currentStrategy.name})');
    }

    return task;
  }

  /// 根据ID移除任务
  bool removeTaskById(String taskId, Type taskType) {
    final queueType = _currentStrategy.getQueueForTaskType(taskType);
    final queue = _taskQueues[queueType];

    if (queue == null) {
      return false;
    }

    bool removed = _executionQueue.removeById(taskId);
    if (removed) {
      return removed;
    }

    return queue.removeById(taskId);
  }

  /// 根据条件移除任务
  int removeTasksWhere(bool Function(TTask) test) {
    int totalRemoved = 0;

    totalRemoved += _executionQueue.removeWhere(test);

    for (final queue in _taskQueues.values) {
      totalRemoved += queue.removeWhere(test);
    }

    // 按需调度模式下，不需要重新调度执行队列
    return totalRemoved;
  }

  /// 检查任务是否存在
  bool hasTask(String taskId, Type taskType) {
    final queueType = _currentStrategy.getQueueForTaskType(taskType);
    final queue = _taskQueues[queueType];

    if (queue == null) {
      return false;
    }

    return queue.containsId(taskId) || _executionQueue.containsId(taskId);
  }

  /// 获取指定任务
  TTask? getTask(String taskId, Type taskType) {
    final queueType = _currentStrategy.getQueueForTaskType(taskType);
    final queue = _taskQueues[queueType];

    if (queue == null) {
      return null;
    }

    // 先在对应队列中查找
    final task = queue.getById(taskId);
    if (task != null) {
      return task;
    }

    // 再在执行队列中查找
    return _executionQueue.getById(taskId);
  }

  /// 清空所有队列
  void clearAll() {
    for (final queue in _taskQueues.values) {
      queue.clear();
    }
    _executionQueue.clear();
  }

  /// 获取队列状态信息（用于调试）
  Map<TQueue, int> getQueueStats() {
    final stats = <TQueue, int>{};
    for (final entry in _taskQueues.entries) {
      stats[entry.key] = entry.value.length;
    }
    return stats;
  }
}
