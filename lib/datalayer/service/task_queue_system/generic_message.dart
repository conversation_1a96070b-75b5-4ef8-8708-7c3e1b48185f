/// 通用任务消息（主线程 -> 工作器）
abstract class GenericTaskMessage<T, R> {
  final String taskId;
  final String processorKey; // 处理器标识符
  final T payload; // 任务数据载荷

  GenericTaskMessage({
    required this.taskId,
    required this.processorKey,
    required this.payload,
  });

  // ==================== 抽象方法 - 子类需要实现 ====================

  /// 创建结果消息的抽象方法，子类需要提供具体实现
  GenericTaskResultMessage<T, R> createResultMessage({
    required bool success,
    R? resultData,
    String? errorMessage,
    required Duration processingTime,
  });

  /// 创建进度消息的抽象方法，子类需要提供具体实现
  GenericTaskProgressMessage<T> createProgressMessage({
    required String status,
    required double progress,
  });

  // ==================== 通用便捷方法 ====================

  /// 创建成功结果消息（便捷方法）
  GenericTaskResultMessage<T, R> createSuccessMessage({
    required R resultData,
    required Duration processingTime,
  }) {
    return createResultMessage(
      success: true,
      resultData: resultData,
      processingTime: processingTime,
    );
  }

  /// 创建失败结果消息（便捷方法）
  GenericTaskResultMessage<T, R> createFailureMessage({
    required String errorMessage,
    required Duration processingTime,
  }) {
    return createResultMessage(
      success: false,
      errorMessage: errorMessage,
      processingTime: processingTime,
    );
  }

  /// 创建任务开始进度消息
  GenericTaskProgressMessage<T> createStartMessage() {
    return createProgressMessage(
      status: '任务开始处理',
      progress: 0.0,
    );
  }

  /// 创建任务进行中进度消息
  GenericTaskProgressMessage<T> createInProgressMessage({
    required String currentStep,
    required double progress,
  }) {
    return createProgressMessage(
      status: currentStep,
      progress: progress,
    );
  }

  /// 创建任务完成进度消息
  GenericTaskProgressMessage<T> createCompleteMessage() {
    return createProgressMessage(
      status: '任务处理完成',
      progress: 1.0,
    );
  }
}

/// 通用任务结果消息（工作器 -> 主线程）
abstract class GenericTaskResultMessage<T, R> {
  final String taskId;
  final String processorKey;
  final bool success;
  final R? resultData;
  final String? errorMessage;
  final Duration processingTime;
  final T payload; // 原始任务数据

  GenericTaskResultMessage({
    required this.taskId,
    required this.processorKey,
    required this.success,
    this.resultData,
    this.errorMessage,
    required this.processingTime,
    required this.payload,
  });
}

/// 通用任务进度消息（工作器 -> 主线程）
abstract class GenericTaskProgressMessage<T> {
  final String taskId;
  final String processorKey;
  final String status;
  final double progress;
  final T payload;

  GenericTaskProgressMessage({
    required this.taskId,
    required this.processorKey,
    required this.status,
    required this.progress,
    required this.payload,
  });
}
