import 'generic_task_processor.dart';

/// 任务处理器注册表
/// 用于管理和获取不同类型的任务处理器
class TaskProcessorRegistry {
  static final Map<String, GenericTaskProcessor<dynamic>> _processors = {};

  /// 注册处理器
  static void register(GenericTaskProcessor<dynamic> processor) {
    _processors[processor.processorKey] = processor;
  }

  /// 根据处理器键获取处理器
  static GenericTaskProcessor<dynamic>? getProcessor(String processorKey) {
    return _processors[processorKey];
  }

  /// 获取所有已注册的处理器键
  static List<String> get registeredKeys {
    return _processors.keys.toList();
  }

  /// 获取所有已注册的处理器
  static List<GenericTaskProcessor<dynamic>> get registeredProcessors {
    return _processors.values.toList();
  }

  /// 检查是否已注册某个处理器
  static bool isRegistered(String processorKey) {
    return _processors.containsKey(processorKey);
  }

  /// 获取已注册处理器的数量
  static int get registeredCount {
    return _processors.length;
  }

  /// 根据类型获取处理器（泛型支持）
  static T? getProcessorByType<T extends GenericTaskProcessor<dynamic>>() {
    for (final processor in _processors.values) {
      if (processor is T) {
        return processor;
      }
    }
    return null;
  }

  /// 获取所有指定类型的处理器
  static List<T>
      getProcessorsByType<T extends GenericTaskProcessor<dynamic>>() {
    return _processors.values.whereType<T>().toList();
  }

  /// 清空所有注册的处理器
  static void clear() {
    _processors.clear();
  }

  /// 获取注册表状态信息（用于调试）
  static Map<String, String> get debugInfo {
    return Map.fromEntries(
      _processors.entries.map(
        (entry) => MapEntry(entry.key, entry.value.runtimeType.toString()),
      ),
    );
  }
}
