// 处理用户状态变化的类

import '../../../utils/file_manager.dart';
import '../../domain/models/store/store.dart';
import '../../domain/models/user/user.dart';
import '../api/request_header.dart';

class UserStateHandler {
  void handleUserStateChange(User? user, Store? store) {
    if (user != null) {
      // 用户登录后的操作，这里创建用户的根目录必须是唯一的
      FileManager().createUserRootDir(user.effectiveId);
      RequestHeader.updateUserInfo(
        userId: user.userId,
        userToken: user.token,
        storeId: store?.id,
        employeeID: user.id,
      );
    } else {
      // 用户登出后的操作
      FileManager().logout();
      RequestHeader.updateUserInfo(
        userId: null,
        userToken: null,
        storeId: null,
        employeeID: null,
      );
    }
  }
}
