import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan_type.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_price.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_valid_duration.dart';

/// 购买默认数据服务
class PurchaseDefaultDataService {
  /// 获取默认的购买套餐数据
  List<PurchasePlan> getDefaultPurchasePlans() {
    return [
      PurchasePlan(
          categoryId: "test",
          continuousSub: false,
          productName: '体验套餐',
          fieldCode: 'test',
          freeTrial: false,
          id: 'test',
          name: '体验套餐',
          count: 1000,
          extraCount: 0,
          platform: ['iOS', 'android'],
          price: const PurchasePrice(
              channel: 'test',
              currency: '¥',
              defaultCurrency: '¥',
              defaultPrice: '288.00',
              price: '288.00',
              productID: 'test'),
          priority: 1,
          purchasePlan: 'test',
          subscribeType: 'test',
          tag: 'test',
          type: PurchasePlanType.basic.value,
          trackId: 'test',
          tagIcon: null,
          validDuration: const PurchaseValidDuration(begin: 0, end: 0)),
      PurchasePlan(
          categoryId: "test",
          continuousSub: false,
          productName: '专享套餐',
          fieldCode: 'test',
          freeTrial: false,
          id: 'test',
          name: '专享套餐',
          count: 10000,
          extraCount: 0,
          platform: ['iOS', 'android'],
          price: const PurchasePrice(
              channel: 'test',
              currency: '¥',
              defaultCurrency: '¥',
              defaultPrice: '2188.00',
              price: '2188.00',
              productID: 'test'),
          priority: 1,
          purchasePlan: 'test',
          subscribeType: 'test',
          tag: 'test',
          type: PurchasePlanType.pro.value,
          trackId: 'test',
          tagIcon: null,
          validDuration: const PurchaseValidDuration(begin: 0, end: 0)),
      PurchasePlan(
          categoryId: "test",
          continuousSub: false,
          productName: '多门店专享套餐',
          fieldCode: 'test',
          freeTrial: false,
          id: 'test',
          name: '多门店专享套餐',
          count: 30000,
          extraCount: 10000,
          platform: ['iOS', 'android'],
          price: const PurchasePrice(
              channel: 'test',
              currency: '¥',
              defaultCurrency: '¥',
              defaultPrice: '5800.00',
              price: '5800.00',
              productID: 'test'),
          priority: 1,
          purchasePlan: 'test',
          subscribeType: 'test',
          tag: 'test',
          type: PurchasePlanType.enterprise.value,
          trackId: 'test',
          tagIcon: null,
          validDuration: const PurchaseValidDuration(begin: 0, end: 0)),
    ];
  }
}
