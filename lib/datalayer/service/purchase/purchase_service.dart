import 'package:turing_art/datalayer/domain/enums/order_status.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/datalayer/domain/models/order_dependent/create_order_response.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/api/purchase/purchase_api_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 购买服务接口
abstract class PurchaseService {
  /// 加载购买套餐卡片
  ///
  /// 如果 API 请求失败或返回空数据，则返回空列表
  Future<List<PurchasePlan>> loadPurchaseCards();

  /// 加载AIGC积分套餐卡片
  ///
  /// 如果 API 请求失败或返回空数据，则返回空列表
  Future<List<PurchasePlan>> loadAigcPointPurchaseCards();

  /// 创建订单
  ///
  /// [paymentChannel] 支付渠道
  /// [purchasePlan] 购买的套餐计划
  /// [productId] 产品ID
  /// [quantity] 购买数量，默认为1
  ///
  /// 返回创建订单是否成功
  Future<CreateOrderResponse?> createOrder(
      PaymentChannel paymentChannel, String purchasePlan, String productId,
      {int quantity = 1});

  /// 检查订单状态
  ///
  /// [orderId] 订单ID
  ///
  /// 返回订单状态
  Future<OrderStatus> checkOrderStatus(String orderId);
}

/// 购买服务实现类
class PurchaseServiceImpl implements PurchaseService {
  final ApiClient _apiClient;
  late final PurchaseApiService _purchaseApiService;

  PurchaseServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _purchaseApiService = PurchaseApiService(dio);
  }

  @override
  Future<List<PurchasePlan>> loadPurchaseCards() async {
    try {
      // 使用 Retrofit 生成的 API 服务调用接口
      final infoModel = await _purchaseApiService.getPurchaseCards();
      try {
        PGLog.d('API 响应数据: $infoModel');
        final items = infoModel.items;
        if (items == null || items.isEmpty) {
          PGLog.e('没有找到套餐项目');
          return [];
        }

        // 获取第一个套餐组的所有套餐
        final plans = items.first.items;
        if (plans.isEmpty) {
          PGLog.e('套餐列表为空');
          return [];
        }

        PGLog.d('成功加载 ${plans.length} 个套餐');
        return plans;
      } catch (e) {
        PGLog.e('解析套餐数据失败: $e');
        return [];
      }
    } catch (e) {
      PGLog.e('加载套餐数据失败: $e');
      return [];
    }
  }

  @override
  Future<List<PurchasePlan>> loadAigcPointPurchaseCards() async {
    try {
      // 使用 Retrofit 生成的 API 服务调用接口
      final infoModel = await _purchaseApiService.getAigcPointPurchaseCards();
      try {
        PGLog.d('AIGC积分套餐 API 响应数据: $infoModel');
        final items = infoModel.items;
        if (items == null || items.isEmpty) {
          PGLog.e('没有找到AIGC积分套餐项目');
          return [];
        }

        // 获取第一个套餐组的所有套餐
        final plans = items.first.items;
        if (plans.isEmpty) {
          PGLog.e('AIGC积分套餐列表为空');
          return [];
        }

        PGLog.d('成功加载 ${plans.length} 个AIGC积分套餐');
        return plans;
      } catch (e) {
        PGLog.e('解析AIGC积分套餐数据失败: $e');
        return [];
      }
    } catch (e) {
      PGLog.e('加载AIGC积分套餐数据失败: $e');
      return [];
    }
  }

  @override
  Future<CreateOrderResponse?> createOrder(
      PaymentChannel paymentChannel, String purchasePlan, String productId,
      {int quantity = 1}) async {
    try {
      // 准备请求参数
      final parameters = {
        'purchasePlan': purchasePlan,
        'productId': productId,
        'quantity': quantity,
      };

      // 按照键名排序参数
      final sortedData = Map.fromEntries(
          parameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));

      // 获取支付渠道API名称
      final apiName = paymentChannel.getApiName();

      // 调用Retrofit生成的API服务
      final response =
          await _purchaseApiService.createOrder(apiName, sortedData);

      PGLog.d('创建订单响应: $response');
      return response;
    } catch (e) {
      PGLog.e('创建订单异常: $e');
      return null;
    }
  }

  @override
  Future<OrderStatus> checkOrderStatus(String orderId) async {
    try {
      // 调用Retrofit生成的API服务
      final response = await _purchaseApiService.checkOrderStatus(orderId);

      if (response.status.isNotEmpty) {
        return OrderStatus.convertToOrderStatus(response.status);
      } else {
        PGLog.e('检查订单状态失败: 响应中没有status字段');
        return OrderStatus.unknown;
      }
    } catch (e) {
      PGLog.e('检查订单状态异常: $e');
      return OrderStatus.unknown;
    }
  }
}
