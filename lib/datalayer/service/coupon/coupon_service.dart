import 'package:turing_art/datalayer/domain/models/coupon/coupon.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/coupon/coupon_api_service.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 兑换码服务接口
abstract class CouponService {
  /// 应用兑换码
  Future<Coupon?> applyCoupon(String code, String type);
}

/// 兑换码服务实现类
class CouponServiceImpl implements CouponService {
  final ApiClient _apiClient;
  late final CouponApiService _couponApiService;

  CouponServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _couponApiService = CouponApiService(dio);
  }

  @override
  Future<Coupon?> applyCoupon(String code, String type) async {
    final response = await _couponApiService.applyCoupon({
      'code': code,
      'type': type,
    });
    PGLog.d('应用兑换码成功: $response');
    return response;
  }
}
