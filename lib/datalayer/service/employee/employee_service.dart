import 'package:dio/dio.dart';
import 'package:turing_art/datalayer/domain/models/api_error/api_error.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_list.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_summary.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/api/employee/employee_api_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 子账号服务接口
abstract class EmployeeService {
  /// 获取子账号张数汇总
  Future<EmployeeSummary?> getEmployeeSummary();

  /// 获取子账号列表
  Future<EmployeeList?> getEmployeeList();

  /// 添加子账号
  Future<ApiError?> addEmployee({
    required String code,
    required List<Map<String, dynamic>> data,
  });

  /// 发送验证码
  Future<bool> sendVerificationCode();

  /// 更新子账号
  Future<bool> updateEmployee({
    required String id,
    String? nickname,
    required bool enable,
  });
}

/// 子账号服务实现
class EmployeeServiceImpl implements EmployeeService {
  final ApiClient _apiClient;
  late final EmployeeApiService _employeeApiService;

  EmployeeServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _employeeApiService = EmployeeApiService(dio);
  }

  @override
  Future<EmployeeSummary?> getEmployeeSummary() async {
    try {
      final response = await _employeeApiService.getEmployeeSummary();
      PGLog.d('获取子账号张数汇总成功: $response');
      return response;
    } catch (e) {
      PGLog.e('获取子账号张数汇总失败: $e');
      return null;
    }
  }

  @override
  Future<EmployeeList?> getEmployeeList() async {
    try {
      final response = await _employeeApiService.getEmployeeList();
      PGLog.d('获取子账号列表成功: $response');
      return response;
    } catch (e) {
      PGLog.e('获取子账号列表失败: $e');
      return null;
    }
  }

  @override
  Future<ApiError?> addEmployee({
    required String code,
    required List<Map<String, dynamic>> data,
  }) async {
    try {
      final requestData = {
        'code': code,
        'data': data,
      };

      final response = await _employeeApiService.addEmployee(requestData);
      PGLog.d('添加子账号响应: $response');

      // 使用ApiError返回错误信息
      if (response['success'] != null) {
        return null;
      }
      return const ApiError(
        code: -1,
        message: '',
      );
    } catch (e) {
      PGLog.e('添加子账号失败: $e');
      if (e is DioException) {
        return ApiError(
          code: e.response?.data['code'] ?? -1,
          message: e.response?.data['message'] ?? '',
        );
      }
      return const ApiError(
        code: -1,
        message: '',
      );
    }
  }

  @override
  Future<bool> sendVerificationCode() async {
    try {
      final response = await _employeeApiService.sendVerificationCode();
      PGLog.d('发送验证码成功: $response');
      return response;
    } catch (e) {
      PGLog.e('发送验证码失败: $e');
      return false;
    }
  }

  @override
  Future<bool> updateEmployee({
    required String id,
    String? nickname,
    required bool enable,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'id': id,
        'enable': enable,
      };

      // 只有在提供了nickname时才添加到请求数据中
      if (nickname != null) {
        requestData['nickname'] = nickname;
      }

      final response = await _employeeApiService.updateEmployee(requestData);
      PGLog.d('更新子账号成功: $response');
      // 检查响应中的success字段
      return response['success'] != null;
    } catch (e) {
      PGLog.e('更新子账号失败: $e');
      return false;
    }
  }
}
