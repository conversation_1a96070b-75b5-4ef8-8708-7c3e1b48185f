import 'package:turing_art/datalayer/domain/models/recharge_record/recharge_record_model.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/api/recharge_record/recharge_record_api_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 充值记录服务接口
abstract class RechargeRecordService {
  /// 获取充值记录
  ///
  /// [page] 页码，默认为1
  /// [pageSize] 每页数量，默认为10
  /// [startTime] 开始时间筛选，可选
  /// [endTime] 结束时间筛选，可选
  Future<RechargeRecordModel?> fetchRechargeRecords({
    required int page,
    required int pageSize,
    String? bankType,
    String? startTime,
    String? endTime,
  });
}

/// 充值记录服务实现
class RechargeRecordServiceImpl implements RechargeRecordService {
  final ApiClient _apiClient;
  late final RechargeRecordApiService _rechargeRecordApiService;

  RechargeRecordServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _rechargeRecordApiService = RechargeRecordApiService(dio);
  }

  @override
  Future<RechargeRecordModel?> fetchRechargeRecords({
    required int page,
    required int pageSize,
    String? bankType,
    String? startTime,
    String? endTime,
  }) async {
    try {
      final response = await _rechargeRecordApiService.fetchRechargeRecords(
        page: page.toString(),
        pageSize: pageSize.toString(),
        bankType: bankType,
        startedAt: startTime,
        endedAt: endTime,
      );
      PGLog.d('获取充值记录成功: $response');
      return response;
    } catch (e) {
      PGLog.e('获取充值记录失败: $e');
      return null;
    }
  }
}
