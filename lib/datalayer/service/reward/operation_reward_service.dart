import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/api/reward/reward_api_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 活动奖励上报的原因类型
enum RewardReportReason {
  /// 添加微信
  addWxwork('add_wxwork'),

  /// 新人注册
  newRegister('new_register');

  final String value;
  const RewardReportReason(this.value);
}

/// 活动奖励上报服务
class OperationRewardService {
  final ApiClient _apiClient;
  late final RewardApiService _rewardApiService;

  OperationRewardService(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _rewardApiService = RewardApiService(dio);
  }

  /// 上报活动奖励
  ///
  /// [rewardId] 奖励ID
  /// [storeId] 店铺ID
  /// [reason] 上报原因：添加微信(add_wxwork) 或 新人注册(new_register)
  Future<bool> reportReward({
    required String rewardId,
    required String storeId,
    required RewardReportReason reason,
  }) async {
    try {
      final body = {
        'rewardId': rewardId,
        'storeId': storeId,
        'reason': reason.value,
      };

      await _rewardApiService.reportReward(body: body);
      return true;
    } catch (e) {
      // 这里可以选择是否忽略异常，因为这是一个可以忽略接口的上报接口
      PGLog.e('活动奖励上报失败: $e');
      return false;
    }
  }
}
