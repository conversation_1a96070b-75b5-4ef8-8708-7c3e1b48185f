import 'package:turing_art/datalayer/domain/models/ops_operation/ops_operation.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/api/ops_operation_api_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 操作类型常量
class OperationType {
  static const String newUserGift = "new_user_gift"; // 新用户礼包
  static const String wechatGift = "wechat_gift"; // 微信礼包
  static const String versionIntro = "version_introduce"; // 版本介绍
}

/// 操作服务接口
abstract class OpsOperationService {
  /// 根据操作类型获取操作活动列表
  Future<List<OperationActivity>> getOperationActivities(String type);

  /// 获取单个操作活动（适用于新用户礼包等只需要单个活动的场景）
  Future<OperationActivity?> getSingleOperationActivity(String type);
}

/// 操作服务实现
class OpsOperationServiceImpl implements OpsOperationService {
  final ApiClient _apiClient;
  late final OpsOperationApiService _opsOperationApiService;

  OpsOperationServiceImpl(this._apiClient) {
    final dio = DioFactory.createDio(_apiClient);
    _opsOperationApiService = OpsOperationApiService(dio);
  }

  @override
  Future<List<OperationActivity>> getOperationActivities(String type) async {
    try {
      OpsOperationResponse response =
          await _opsOperationApiService.getOperationInfo(type);

      // 根据类型获取对应的操作模型列表
      List<OpsOperationModel> modelList = _getModelListByType(response, type);

      if (modelList.isEmpty) {
        PGLog.d('没有找到符合时间条件的运营活动: $type');
        return [];
      }

      // 筛选符合时间条件的活动
      return _filterValidOperationActivities(modelList);
    } catch (e) {
      PGLog.e('获取操作活动失败: $e');
      return [];
    }
  }

  @override
  Future<OperationActivity?> getSingleOperationActivity(String type) async {
    final activities = await getOperationActivities(type);
    return activities.isNotEmpty ? activities.first : null;
  }

  /// 根据类型获取模型列表
  List<OpsOperationModel> _getModelListByType(
      OpsOperationResponse response, String type) {
    switch (type) {
      case OperationType.newUserGift:
        return response.newUserGift;
      case OperationType.wechatGift:
        return response.wechatGift;
      case OperationType.versionIntro:
        return response.versionIntro;
      default:
        PGLog.d('未知的操作类型: $type');
        return [];
    }
  }

  /// 筛选符合时间条件的活动
  List<OperationActivity> _filterValidOperationActivities(
      List<OpsOperationModel> modelList) {
    final now = DateTime.now();
    List<OperationActivity> validActivities = [];

    // 1. 选择符合时间条件的第一个模型
    OpsOperationModel? selectedModel;
    for (final model in modelList) {
      final beginTime = DateTime.fromMillisecondsSinceEpoch(model.period.begin);

      // 检查开始时间是否小于当前时间
      if (beginTime.isBefore(now)) {
        // 检查结束时间
        if (model.period.end == 0 ||
            DateTime.fromMillisecondsSinceEpoch(model.period.end)
                .isAfter(now)) {
          selectedModel = model;
          break;
        }
      }
    }

    // 如果没有找到合适的模型，返回空列表
    if (selectedModel == null) {
      PGLog.d('没有找到符合时间条件的运营活动');
      return [];
    }

    // 检查选中的模型是否有活动
    if (selectedModel.activities.isEmpty) {
      PGLog.d('选中的运营活动没有可用的子活动');
      return [];
    }

    // 2. 从选中的模型中筛选符合时间条件的活动
    for (final activity in selectedModel.activities) {
      final beginTime =
          DateTime.fromMillisecondsSinceEpoch(activity.period.begin);

      // 检查开始时间是否小于当前时间
      if (beginTime.isBefore(now)) {
        // 检查结束时间
        if (activity.period.end == 0 ||
            DateTime.fromMillisecondsSinceEpoch(activity.period.end)
                .isAfter(now)) {
          validActivities.add(activity);
        }
      }
    }

    return validActivities;
  }
}
