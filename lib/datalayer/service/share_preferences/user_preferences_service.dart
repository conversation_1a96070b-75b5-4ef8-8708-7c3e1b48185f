import 'package:shared_preferences/shared_preferences.dart';
import 'package:turing_art/datalayer/domain/enums/export_path_type.dart';
import 'package:turing_art/datalayer/domain/enums/sort_option.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'shared_preferences_service.dart';

/// 用户偏好设置服务（用于保存用户偏好设置）
class UserPreferencesService {
  static SharedPreferences? _prefs;
  static final Map<String, String> _cache = {};

  // 键值常量
  static const String _sortOptionKey = 'project_sort_option';
  static const String _downloadCompletedFileKey = 'download_completed_file';
  static const String _downloadedVersionKey = 'downloaded_version';
  static const String _hasShownWechatGiftDialogKey =
      'has_shown_wechat_gift_dialog';
  static const String _hasShowNewUserGiftKey = 'has_show_new_user_gift';
  static const String _keyLastLoginEmployeeId = 'last_login_employee_id';
  static const String _keyFirstInstallVersion = 'first_install_version';

  // 导出路径相关键值
  static const String _exportPathTypeKey = 'export_path_type';
  static const String _customExportPathKey = 'custom_export_path';
  static const String _showExportPathDialogKey = 'show_export_path_dialog';

  // 新手引导相关键值
  static const String _hasShownNoviceGuideKey = 'has_shown_novice_guide';
  static const String _hasCompletedNoviceGuideKey =
      'has_completed_novice_guide';

  // 初始化方法
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    // 初始化缓存
    _loadCache();
  }

  // 加载缓存
  static void _loadCache() {
    _cache[_sortOptionKey] = _prefs?.getString(_sortOptionKey) ?? '';
    _cache[_keyLastLoginEmployeeId] =
        _prefs?.getString(_keyLastLoginEmployeeId) ?? '';
    _cache[_exportPathTypeKey] = _prefs?.getString(_exportPathTypeKey) ?? '';
    _cache[_customExportPathKey] =
        _prefs?.getString(_customExportPathKey) ?? '';
  }

  // 获取导出路径类型（不与用户关联）
  static ExportPathType getExportPathType() {
    // 需求修改：默认为并只有自定义（不确定以后是否修改为有原路径，相关逻辑保留只是注释）
    return ExportPathType.custom;
  }

  // 设置导出路径类型（不与用户关联）
  static void setExportPathType(ExportPathType pathType) {
    _cache[_exportPathTypeKey] = pathType.name;
    _prefs?.setString(_exportPathTypeKey, pathType.name);
  }

  // 获取自定义导出路径（不与用户关联）
  static String getCustomExportPath() {
    String path = _cache[_customExportPathKey] ?? '';
    if (path.isEmpty) {
      path = _prefs?.getString(_customExportPathKey) ?? '';
    }
    return path;
  }

  // 设置自定义导出路径（不与用户关联）
  static void setCustomExportPath(String path) {
    _cache[_customExportPathKey] = path;
    _prefs?.setString(_customExportPathKey, path);
  }

  // 获取是否显示导出路径弹窗（不与用户关联）
  static bool getShowExportPathDialog() {
    return _prefs?.getBool(_showExportPathDialogKey) ?? true; // 默认显示弹窗
  }

  // 设置是否显示导出路径弹窗（不与用户关联）
  static void setShowExportPathDialog({required bool show}) {
    _prefs?.setBool(_showExportPathDialogKey, show);
  }

  // 获取排序选项（和用户关联）
  static SortOption getSortOption() {
    final key = _getUserSpecificKey(_sortOptionKey);
    String optionName = _cache[key] ?? '';
    if (optionName.isEmpty) {
      optionName = _prefs?.getString(key) ?? '';
    }

    if (optionName.isEmpty) {
      return SortOption.lastOpened;
    }

    try {
      return SortOption.values.firstWhere(
        (option) => option.name == optionName,
      );
    } catch (e) {
      setSortOption(SortOption.lastOpened);
      return SortOption.lastOpened;
    }
  }

  // 同步获取最后登录的店铺的员工ID
  static String getLastLoginEmployeeId() {
    return _cache[_keyLastLoginEmployeeId] ?? '';
  }

  // 同步设置最后登录的店铺的员工ID
  static void setLastLoginEmployeeId(String employeeId) {
    _cache[_keyLastLoginEmployeeId] = employeeId;
    _prefs?.setString(_keyLastLoginEmployeeId, employeeId);
  }

  // 设置排序选项（和用户关联）
  static void setSortOption(SortOption option) {
    final key = _getUserSpecificKey(_sortOptionKey);
    _cache[key] = option.name;
    _prefs?.setString(key, option.name);
  }

  // 确保数据保存到持久化存储
  static Future<void> ensureSaved() async {
    if (_prefs == null) {
      return;
    }

    for (var entry in _cache.entries) {
      await _prefs!.setString(entry.key, entry.value);
    }
  }

  // 清除用户偏好设置
  static void clear() {
    _cache.clear();
    // 只清除本服务类中定义的key
    _prefs?.remove(_sortOptionKey);
    _prefs?.remove(_downloadCompletedFileKey);
    _prefs?.remove(_downloadedVersionKey);
    _prefs?.remove(_hasShownWechatGiftDialogKey);
    _prefs?.remove(_hasShowNewUserGiftKey);
    _prefs?.remove(_keyLastLoginEmployeeId);
  }

  // 保存下载完成的文件路径
  static void setDownloadCompletedFile(String filePath) {
    _cache[_downloadCompletedFileKey] = filePath;
    _prefs?.setString(_downloadCompletedFileKey, filePath);
  }

  // 获取下载完成的文件路径
  static String? getDownloadCompletedFile() {
    String? filePath = _cache[_downloadCompletedFileKey];
    if (filePath == null || filePath.isEmpty) {
      filePath = _prefs?.getString(_downloadCompletedFileKey);
    }
    return filePath;
  }

  // 清除下载完成的文件路径
  static void clearDownloadCompletedFile() {
    _cache.remove(_downloadCompletedFileKey);
    _prefs?.remove(_downloadCompletedFileKey);
  }

  // 保存已下载的版本
  static void setDownloadedVersion(String version) {
    _cache[_downloadedVersionKey] = version;
    _prefs?.setString(_downloadedVersionKey, version);
  }

  // 获取已下载的版本
  static String? getDownloadedVersion() {
    String? version = _cache[_downloadedVersionKey];
    if (version == null || version.isEmpty) {
      version = _prefs?.getString(_downloadedVersionKey);
    }
    return version;
  }

  // 清除已下载的版本信息
  static void clearDownloadedVersion() {
    _cache.remove(_downloadedVersionKey);
    _prefs?.remove(_downloadedVersionKey);
  }

  // 保存是否已经显示过微信礼包弹窗（和用户关联）
  static void setHasShownWechatGiftDialog({required bool hasShown}) {
    final key = _getUserSpecificKey(_hasShownWechatGiftDialogKey);
    _prefs?.setBool(key, hasShown);
  }

  // 获取是否已经显示过微信礼包弹窗（和用户关联）
  static bool getHasShownWechatGiftDialog() {
    final key = _getUserSpecificKey(_hasShownWechatGiftDialogKey);
    PGLog.d('UserPreferencesService - getHasShownWechatGiftDialog key $key');
    final result = _prefs?.getBool(key) ?? false;
    PGLog.d(
        'UserPreferencesService - getHasShownWechatGiftDialog result $result');
    return result;
  }

  // 生成用户特定的键名
  static String _getUserSpecificKey(String baseKey) {
    final employeeId = SharedPreferencesService.getEmployeeId();
    PGLog.d(
        'UserPreferencesService - _getUserSpecificKey employeeId $employeeId');
    if (employeeId.isEmpty) {
      return baseKey;
    }
    return '${baseKey}_$employeeId';
  }

  // 保存是否已经显示过新用户礼包弹窗（与用户关联）
  static void setHasShowNewUserGift({required bool hasShown}) {
    final key = _getUserSpecificKey(_hasShowNewUserGiftKey);
    _prefs?.setBool(key, hasShown);
  }

  // 获取是否已经显示过新用户礼包弹窗（与用户关联）
  static bool getHasShowNewUserGift() {
    final key = _getUserSpecificKey(_hasShowNewUserGiftKey);
    return _prefs?.getBool(key) ?? false;
  }

  // 保存是否已经显示过新手引导（需求变更：不与用户关联，但是工程会绑定在第一个有资格有机会有demo工程的员工身上）
  static void setHasShownNoviceGuide({required int step}) {
    _prefs?.setBool('${_hasShownNoviceGuideKey}_$step', true);
  }

  // 获取是否已经显示过新手引导（需求变更：不与用户关联，但是工程会绑定在第一个有资格有机会有demo工程的员工身上）
  static bool getHasShownNoviceGuide({required int step}) {
    return _prefs?.getBool('${_hasShownNoviceGuideKey}_$step') ?? false;
  }

  // 保存是否已经完成全部新手引导（需求变更：不与用户关联，但是工程会绑定在第一个有资格有机会有demo工程的员工身上）
  static void setHasCompletedNoviceGuide() {
    _prefs?.setBool(_hasCompletedNoviceGuideKey, true);
  }

  // 获取是否已经完成全部新手引导（需求变更：不与用户关联，但是工程会绑定在第一个有资格有机会有demo工程的员工身上）
  static bool getHasCompletedNoviceGuide() {
    return _prefs?.getBool(_hasCompletedNoviceGuideKey) ?? false;
  }

  // 保存首次安装版本（只在没有记录时保存，升级不会覆盖）
  static void setFirstInstallVersion(String version) {
    // 只有在首次安装时设置，如果已经有值则不更新
    final currentValue = getFirstInstallVersion();
    if (currentValue.isEmpty) {
      _prefs?.setString(_keyFirstInstallVersion, version);
      PGLog.i('UserPreferencesService - 记录首次安装版本: $version');
    }
  }

  // 获取首次安装版本
  static String getFirstInstallVersion() {
    return _prefs?.getString(_keyFirstInstallVersion) ?? '';
  }
}
