import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  static SharedPreferences? _prefs;
  static final Map<String, String> _cache = {};

  static const String _keyUserId = 'user_id';
  static const String _keyEmployeeId = 'employee_id';
  static const String _keyUserToken = 'user_token';
  static const String _keyTokenExpire = 'token_expire';
  static const String _keyFirstLaunchTime = 'first_launch_time';
  static const String _keyBetaExpiryTime = 'beta_expiry_time';

  static const String _standbyDeviceId = 'standby_device_id';

  static const String _keyStore = 'store';
  static const String _keyCreator = 'creator';
  static const String _keyLastLoginVersion = 'last_login_version';

  /// 是否显示AIGC背景图成功提示toast
  static const String _keyHasShowAIGCBackImageSuccessToast =
      'has_shown_aigc_back_image_success_toast';

  /// 设备评级相关的存储键
  static const String _keyDeviceRating = 'device_rating';
  static const String _keyDevicePerformanceInfo = 'device_performance_info';
  static const String _keyDeviceRatingFirstTime = 'device_rating_first_time';
  static const String _keyDeviceRatingMessageSent =
      'device_rating_message_sent';
  static const String _keyUserLoginRatingMessageSentPrefix =
      'user_login_rating_message_sent_';

  /// 缓存清理相关的存储键
  static const String _keyCacheLastTimeCleanup = 'cache_last_time_cleanup';

  // 异步初始化方法
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    // 初始化缓存
    _cache[_keyUserId] = _prefs?.getString(_keyUserId) ?? '';
    _cache[_keyEmployeeId] = _prefs?.getString(_keyEmployeeId) ?? '';
    _cache[_keyUserToken] = _prefs?.getString(_keyUserToken) ?? '';
    _cache[_keyTokenExpire] = _prefs?.getString(_keyTokenExpire) ?? '';
    _cache[_keyFirstLaunchTime] = _prefs?.getString(_keyFirstLaunchTime) ?? '';
    _cache[_keyBetaExpiryTime] = _prefs?.getString(_keyBetaExpiryTime) ?? '';
    _cache[_standbyDeviceId] = _prefs?.getString(_standbyDeviceId) ?? '';
    _cache[_keyStore] = _prefs?.getString(_keyStore) ?? '';
    _cache[_keyCreator] = _prefs?.getString(_keyCreator) ?? '';
    _cache[_keyLastLoginVersion] =
        _prefs?.getString(_keyLastLoginVersion) ?? '';
    _cache[_keyHasShowAIGCBackImageSuccessToast] =
        _prefs?.getBool(_keyHasShowAIGCBackImageSuccessToast)?.toString() ??
            'false';

    // 初始化设备评级相关的缓存
    _cache[_keyDeviceRating] = _prefs?.getString(_keyDeviceRating) ?? '';
    _cache[_keyDevicePerformanceInfo] =
        _prefs?.getString(_keyDevicePerformanceInfo) ?? '';
    _cache[_keyDeviceRatingFirstTime] =
        _prefs?.getBool(_keyDeviceRatingFirstTime)?.toString() ?? 'true';
    _cache[_keyDeviceRatingMessageSent] =
        _prefs?.getBool(_keyDeviceRatingMessageSent)?.toString() ?? 'false';

    // 初始化缓存清理相关的缓存
    _cache[_keyCacheLastTimeCleanup] =
        _prefs?.getString(_keyCacheLastTimeCleanup) ?? '';
  }

  // 同步获取用户ID
  static String getUserId() {
    return _cache[_keyUserId] ?? '';
  }

  // 同步获取员工ID
  static String getEmployeeId() {
    return _cache[_keyEmployeeId] ?? '';
  }

  // 同步获取用户Token
  static String getUserToken() {
    return _cache[_keyUserToken] ?? '';
  }

  // 同步获取用户Token过期时间
  static String getTokenExpire() {
    return _cache[_keyTokenExpire] ?? '';
  }

  // 同步获取过期时间
  static String getBetaExpiryTime() {
    return _cache[_keyBetaExpiryTime] ?? '';
  }

  // 同步获取首次启动时间
  static String getFirstLaunchTime() {
    return _cache[_keyFirstLaunchTime] ?? '';
  }

  static String getStandbyDeviceId() {
    return _cache[_standbyDeviceId] ?? '';
  }

  // 同步获取店铺信息
  static String getStore() {
    return _cache[_keyStore] ?? '';
  }

  // 同步获取创作者信息
  static String getCreator() {
    return _cache[_keyCreator] ?? '';
  }

  // 同步获取最后登录版本
  static String getLastLoginVersion() {
    return _cache[_keyLastLoginVersion] ?? '';
  }

  // 同步设置店铺信息
  static void setStore(String store) {
    _cache[_keyStore] = store;
    _prefs?.setString(_keyStore, store);
  }

  // 同步设置创作者信息
  static void setCreator(String creator) {
    _cache[_keyCreator] = creator;
    _prefs?.setString(_keyCreator, creator);
  }

  // 同步设置最后登录版本
  static void setLastLoginVersion(String version) {
    _cache[_keyLastLoginVersion] = version;
    _prefs?.setString(_keyLastLoginVersion, version);
  }

  // 同步设置过期时间
  static void setBetaExpiryTime(String expiryTime) {
    _cache[_keyBetaExpiryTime] = expiryTime;
    _prefs?.setString(_keyBetaExpiryTime, expiryTime);
  }

  // 同步设置首次启动时间
  static void setFirstLaunchTime(String firstLaunchTime) {
    _cache[_keyFirstLaunchTime] = firstLaunchTime;
    _prefs?.setString(_keyFirstLaunchTime, firstLaunchTime);
  }

  // 设置用户信息 - 立即更新缓存并异步保存
  static void setUserInfo(
    String userId,
    String employeeId,
    String userToken,
    String tokenExpire,
    String lastLoginStoreId,
  ) {
    setUserId(userId);
    setEmployeeId(employeeId);
    setUserToken(userToken);
    setTokenExpire(tokenExpire);
  }

  // 设置用户ID - 立即更新缓存并异步保存
  static void setUserId(String userId) {
    _cache[_keyUserId] = userId;
    _prefs?.setString(_keyUserId, userId); // 异步保存到持久化存储
  }

  // 设置员工ID - 立即更新缓存并异步保存
  static void setEmployeeId(String employeeId) {
    _cache[_keyEmployeeId] = employeeId;
    _prefs?.setString(_keyEmployeeId, employeeId); // 异步保存到持久化存储
  }

  // 设置用户Token - 立即更新缓存并异步保存
  static void setUserToken(String token) {
    _cache[_keyUserToken] = token;
    _prefs?.setString(_keyUserToken, token); // 异步保存到持久化存储
  }

  // 设置用户Token过期时间 - 立即更新缓存并异步保存
  static void setTokenExpire(String tokenExpire) {
    _cache[_keyTokenExpire] = tokenExpire;
    _prefs?.setString(_keyTokenExpire, tokenExpire); // 异步保存到持久化存储
  }

  // 设置备用设备ID - 立即更新缓存并异步保存
  static void setStandbyDeviceId(String standbyDeviceId) {
    _cache[_standbyDeviceId] = standbyDeviceId;
    _prefs?.setString(_standbyDeviceId, standbyDeviceId); // 异步保存到持久化存储
  }

  // 同步清除缓存并异步清除存储
  static void clear() {
    _cache.clear();
    // 只清除本服务类中定义的key
    _prefs?.remove(_keyUserId);
    _prefs?.remove(_keyEmployeeId);
    _prefs?.remove(_keyUserToken);
    _prefs?.remove(_keyTokenExpire);
    _prefs?.remove(_keyFirstLaunchTime);
    _prefs?.remove(_keyBetaExpiryTime);
    _prefs?.remove(_standbyDeviceId);
    _prefs?.remove(_keyStore);
    _prefs?.remove(_keyCreator);
    _prefs?.remove(_keyLastLoginVersion);
    _prefs?.remove(_keyHasShowAIGCBackImageSuccessToast);

    // 清除设备评级相关的存储
    _prefs?.remove(_keyDeviceRating);
    _prefs?.remove(_keyDevicePerformanceInfo);
    _prefs?.remove(_keyDeviceRatingFirstTime);
    _prefs?.remove(_keyDeviceRatingMessageSent);
    // 清除所有手机号相关的用户登录消息状态
    final keys = _prefs?.getKeys() ?? {};
    for (final key in keys) {
      if (key.startsWith(_keyUserLoginRatingMessageSentPrefix)) {
        _prefs?.remove(key);
      }
    }

    // 清除缓存清理相关的存储
    _prefs?.remove(_keyCacheLastTimeCleanup);
  }

  // 检查是否已登录
  static bool get isLoggedIn {
    final userId = getUserId();
    final userToken = getUserToken();
    return userId.isNotEmpty && userToken.isNotEmpty && !isTokenExpired;
  }

  // 检查token是否过期
  static bool get isTokenExpired {
    final tokenExpire = getTokenExpire();
    return tokenExpire.isNotEmpty &&
        DateTime.now().isAfter(DateTime.parse(tokenExpire));
  }

  // 是否需要刷新token
  static bool get needToRefreshToken {
    final userId = getUserId();
    final userToken = getUserToken();
    if (userId.isEmpty || userToken.isEmpty) {
      return false;
    }
    final expiresAt = DateTime.parse(getTokenExpire());
    final now = DateTime.now();
    // 如果token过期时间于当前时间小于7天，则需要刷新token
    if (expiresAt.isAfter(now.add(const Duration(days: 7)))) {
      return false;
    }
    return true;
  }

  // 是否显示AIGC背景图成功提示toast
  static bool get hasShowAIGCBackImageSuccessToast {
    return _cache[_keyHasShowAIGCBackImageSuccessToast] == true.toString();
  }

  // 设置是否显示AIGC背景图成功提示toast
  static void setHasShowAIGCBackImageSuccessToast({required bool hasShow}) {
    _cache[_keyHasShowAIGCBackImageSuccessToast] = hasShow.toString();
    _prefs?.setBool(_keyHasShowAIGCBackImageSuccessToast, hasShow);
  }

  // 设备评级相关的方法

  // 获取设备评级结果
  static String getDeviceRating() {
    return _cache[_keyDeviceRating] ?? '';
  }

  // 设置设备评级结果
  static Future<void> setDeviceRating(String rating) async {
    _cache[_keyDeviceRating] = rating;
    await _prefs?.setString(_keyDeviceRating, rating);
  }

  // 获取设备性能信息
  static String getDevicePerformanceInfo() {
    return _cache[_keyDevicePerformanceInfo] ?? '';
  }

  // 设置设备性能信息
  static Future<void> setDevicePerformanceInfo(String info) async {
    _cache[_keyDevicePerformanceInfo] = info;
    await _prefs?.setString(_keyDevicePerformanceInfo, info);
  }

  // 获取设备评级是否为首次
  static bool getDeviceRatingFirstTime() {
    return _cache[_keyDeviceRatingFirstTime] == 'true';
  }

  // 设置设备评级是否为首次
  static Future<void> setDeviceRatingFirstTime({
    required bool isFirstTime,
  }) async {
    _cache[_keyDeviceRatingFirstTime] = isFirstTime.toString();
    await _prefs?.setBool(_keyDeviceRatingFirstTime, isFirstTime);
  }

  // 获取设备评级消息是否已发送
  static bool getDeviceRatingMessageSent() {
    return _cache[_keyDeviceRatingMessageSent] == 'true';
  }

  // 设置设备评级消息已发送
  static Future<void> setDeviceRatingMessageSent({required bool sent}) async {
    _cache[_keyDeviceRatingMessageSent] = sent.toString();
    await _prefs?.setBool(_keyDeviceRatingMessageSent, sent);
  }

  // 获取指定手机号用户的登录评级消息是否已发送
  static bool getUserLoginRatingMessageSent(String mobile) {
    if (mobile.isEmpty) return false;
    final key = _keyUserLoginRatingMessageSentPrefix + mobile;
    return _prefs?.getBool(key) ?? false;
  }

  // 设置指定手机号用户的登录评级消息已发送
  static Future<void> setUserLoginRatingMessageSent({
    required String mobile,
    required bool sent,
  }) async {
    if (mobile.isEmpty) return;
    final key = _keyUserLoginRatingMessageSentPrefix + mobile;
    await _prefs?.setBool(key, sent);
  }

  // 缓存清理相关的方法

  // 获取最后时间清理时间戳
  static String getCacheLastTimeCleanup() {
    return _cache[_keyCacheLastTimeCleanup] ?? '';
  }

  // 设置最后时间清理时间戳
  static Future<void> setCacheLastTimeCleanup(String timestamp) async {
    _cache[_keyCacheLastTimeCleanup] = timestamp;
    await _prefs?.setString(_keyCacheLastTimeCleanup, timestamp);
  }
}
