import 'package:turing_art/datalayer/domain/models/store/store.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/current_user_store_info/current_user_store_info_api_service.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';

abstract class CurrentUserStoreInfoService {
  // 获取当前用户Store信息
  Future<Store> getCurrentUserStore();
}

class CurrentUserStoreInfoServiceImpl implements CurrentUserStoreInfoService {
  final ApiClient _apiClient;
  late final CurrentUserStoreInfoApiService _currentUserStoreInfoApiService;

  CurrentUserStoreInfoServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _currentUserStoreInfoApiService = CurrentUserStoreInfoApiService(dio);
  }

  @override
  Future<Store> getCurrentUserStore() async {
    return await _currentUserStoreInfoApiService.getCurrentUserStore();
  }
}
