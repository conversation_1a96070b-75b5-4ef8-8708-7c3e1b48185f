import 'dart:io';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/datalayer/service/task_queue_system/generic_task_processor.dart';
import 'package:turing_art/ffi/models/salient_matters_model.dart';
import 'package:turing_art/ffi/services/salient_matters_service.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';
import 'package:turing_art/utils/image_save_helper.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC交互式蒙版生成任务处理器
/// 基于用户的涂抹/擦除交互，生成精细的蒙版结果
class AigcInteractiveMaskProcessor
    extends GenericTaskProcessor<AigcTaskMessage> {
  @override
  String get processorKey => 'interactiveMask';

  @override
  Future<void> process(
    AigcTaskMessage message,
    SendPort sendToMain,
    String workerId,
  ) async {
    final startTime = DateTime.now();

    try {
      PGLog.d(
          '🎨 工作器 $workerId 开始交互式蒙版生成: ${path.basename(message.payload.inputPath)}');

      // 验证任务类型
      if (message.payload.taskType != AigcTaskType.interactiveMask) {
        throw Exception(
            '任务类型不匹配，期望 interactiveMask，实际 ${message.payload.taskType}');
      }

      final request = message.payload.request as AigcInteractiveMaskTaskRequest;

      // 发送开始进度
      sendProgress(message, '开始交互式蒙版生成', 0.0, sendToMain);

      // 执行交互式蒙版处理
      final result = await _processInteractiveMask(
        inputPath: message.payload.inputPath,
        outputPath: message.payload.outputPath,
        request: request,
      );

      // 发送完成进度
      sendProgress(message, '交互式蒙版生成完成', 1.0, sendToMain);

      // 发送成功结果
      sendSuccess(message, result, startTime, sendToMain);
    } catch (e) {
      PGLog.e('交互式蒙版生成失败: $e');
      sendError(message, e.toString(), startTime, sendToMain);
    } finally {
      // 释放SalientMatters服务资源
      // SalientMattersService.dispose();
    }
  }

  /// 处理交互式蒙版生成
  Future<AigcInteractiveMaskTaskResult> _processInteractiveMask({
    required String inputPath,
    required String outputPath,
    required AigcInteractiveMaskTaskRequest request,
  }) async {
    final processStartTime = DateTime.now();

    try {
      // 处理输出路径
      final outputDir = path.dirname(outputPath);
      final outputDirectory = Directory(outputDir);
      if (!outputDirectory.existsSync()) {
        outputDirectory.createSync(recursive: true);
      }

      PGLog.d('开始交互式蒙版处理: $inputPath');

      // 1. 初始化SalientMatters服务
      final initResult = await SalientMattersService.initializeFromConfig();
      if (!initResult.isSuccess) {
        throw Exception('SalientMatters初始化失败: ${initResult.errorMessage}');
      }

      // 2. 从文件加载图像数据
      final imageData = await ImageSaveHelper.loadImageFromFile(inputPath);
      if (imageData == null) {
        throw Exception('无法加载输入图像: $inputPath');
      }

      PGLog.d(
          '成功加载图像: ${imageData.width}x${imageData.height}, 通道数: ${imageData.channels}');

      // 3. 验证图像数据
      if (!SalientMattersService.validateImageData(imageData)) {
        throw Exception('图像数据验证失败');
      }

      // 4. 获取蒙版数据信息
      final mattingMaskDataInfo = request.mattingMaskDataInfo;

      // 5. 检查是否为区域抠图模式
      if (mattingMaskDataInfo.isRegionalMode) {
        PGLog.d('检测到区域抠图模式，开始区域处理...');
        return await _processRegionalInteractiveMask(
          imageData: imageData,
          outputPath: outputPath,
          mattingMaskDataInfo: mattingMaskDataInfo,
          processStartTime: processStartTime,
        );
      }

      // 6. 全图交互式抠图处理（原有逻辑）
      return await _processFullImageInteractiveMask(
        imageData: imageData,
        outputPath: outputPath,
        mattingMaskDataInfo: mattingMaskDataInfo,
        processStartTime: processStartTime,
      );
    } catch (e) {
      PGLog.e('交互式蒙版处理失败: $e');
      throw Exception('交互式蒙版处理失败: $e');
    }
  }

  /// 处理区域交互式抠图
  Future<AigcInteractiveMaskTaskResult> _processRegionalInteractiveMask({
    required ImageData imageData,
    required String outputPath,
    required AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo,
    required DateTime processStartTime,
  }) async {
    final regionalInfo = mattingMaskDataInfo.regionalFrameDataInfo!;

    // 将区域框信息转换为 RectData
    final region = RectData(
      x: regionalInfo.rect.left.toInt(),
      y: regionalInfo.rect.top.toInt(),
      width: regionalInfo.rect.width.toInt(),
      height: regionalInfo.rect.height.toInt(),
    );

    PGLog.d(
        '区域信息: x=${region.x}, y=${region.y}, w=${region.width}, h=${region.height}');

    // 准备区域图像数据
    ImageData? croppedImage;
    if (mattingMaskDataInfo.regionalImageBytes != null) {
      croppedImage = _convertBytesToImageData(
        mattingMaskDataInfo.regionalImageBytes!,
        region.width,
        region.height,
        imageData.channels, // 同原图格式
      );
    }

    // 检查是否有笔触数据来决定使用哪种处理逻辑
    if (mattingMaskDataInfo.currentStrokesBytes == null) {
      // 情况1: 没有笔触数据，使用 regionalMatting
      PGLog.d('没有笔触数据，执行区域主体抠图...');

      // 准备上次的完整结果用于回贴
      ImageData? previousFullResult;
      if (mattingMaskDataInfo.fullMaskBytes != null) {
        previousFullResult = _convertBytesToImageData(
          mattingMaskDataInfo.fullMaskBytes!,
          imageData.width,
          imageData.height,
          4, // RGBA格式
        );
      }

      final regionalStartTime = DateTime.now();

      final regionalMattingResult = await SalientMattersService.regionalMatting(
        imageData,
        region,
        croppedImage: null,
        previousFullResult: previousFullResult,
        returnCroppedResult: true,
        returnCroppedMattingResult: true,
      );

      final regionalEndTime = DateTime.now();
      final regionalDuration = regionalEndTime.difference(regionalStartTime);

      if (!regionalMattingResult.isSuccess) {
        throw Exception('区域主体抠图失败: ${regionalMattingResult.errorMessage}');
      }

      if (regionalMattingResult.fullResult == null) {
        throw Exception('区域主体抠图结果为空');
      }

      // 将结果数据转换为ImageData对象（完整图像尺寸的结果）
      final resultImageData = ImageData(
        data: regionalMattingResult.fullResult!,
        width: imageData.width,
        height: imageData.height,
        channels: 4,
      );

      PGLog.d(
          '区域主体抠图算法完成，结果尺寸: ${resultImageData.width}x${resultImageData.height}, 耗时: ${regionalDuration.inMilliseconds}ms');
      // 更新蒙版数据信息对象
      final updatedMattingMaskDataInfo = AigcMattingMaskDarwPathImageDataInfo(
        regionalFrameDataInfo: mattingMaskDataInfo.regionalFrameDataInfo,
        imagePath: mattingMaskDataInfo.imagePath,
        displayImageBytes: null, // 原图不进行传递，回调的地方不会进行赋值
        previousMaskBytes:
            regionalMattingResult.croppedMattingResult, // 更新为区域抠图结果
        isBrushMode: mattingMaskDataInfo.isBrushMode,
        currentStrokesBytes: null,
      )
        ..foreStrokesBytes = null
        ..backStrokesBytes = null
        ..fullMaskBytes = regionalMattingResult.fullResult
        ..regionalImageBytes =
            regionalMattingResult.croppedResult; // 更新为裁剪后的区域图像

      // 保存结果为PNG文件
      try {
        await updatedMattingMaskDataInfo.saveToDirectory(
            AigcMattingType.regionalSubject, outputPath, resultImageData);
      } catch (e) {
        PGLog.e('保存交互式蒙版失败: $e');
        throw Exception('保存交互式蒙版失败: $e');
      }

      final processEndTime = DateTime.now();
      final totalDuration = processEndTime.difference(processStartTime);

      PGLog.d('区域主体抠图处理总耗时: ${totalDuration.inMilliseconds}ms');

      return AigcInteractiveMaskTaskResult(
        outputPath: outputPath,
        mattingMaskDataInfo: updatedMattingMaskDataInfo,
      );
    } else {
      // 情况2: 有笔触数据，使用 regionalInteractive
      PGLog.d('检测到笔触数据，执行区域交互式抠图...');
      if (croppedImage == null) {
        throw Exception('区域交互式抠图，区域图像数据为空');
      }

      // 准备上次的蒙版数据
      ImageData? latestMask;
      if (mattingMaskDataInfo.previousMaskBytes != null) {
        latestMask = _convertBytesToImageData(
          mattingMaskDataInfo.previousMaskBytes!,
          region.width,
          region.height,
          4, // RGBA格式
        );
      }

      // 准备前景和背景笔触数据（如果有笔触数据）
      ImageData? foreStrokes;
      ImageData? backStrokes;

      if (_isValidStrokeData(mattingMaskDataInfo.currentStrokesBytes)) {
        PGLog.d('开始处理区域笔触数据...');

        // 将笔触数据转换为ImageData（区域尺寸）
        final strokeMask = _convertBytesToImageData(
          mattingMaskDataInfo.currentStrokesBytes!,
          region.width,
          region.height,
          4, // 笔触数据通常是RGBA格式
        );

        // 使用processInteractiveMask预处理前景和背景蒙版
        final processResult =
            await SalientMattersService.processInteractiveMask(
          strokeMask,
          mattingMaskDataInfo.foreStrokesBytes, // 使用上次的前景蒙版数据
          mattingMaskDataInfo.backStrokesBytes, // 使用上次的背景蒙版数据
          mattingMaskDataInfo.isBrushMode, // 涂抹为true，擦除为false
        );

        if (processResult.isSuccess) {
          PGLog.d('区域笔触数据处理成功');

          if (processResult.foregroundMask != null) {
            foreStrokes = _convertBytesToImageData(
              processResult.foregroundMask!,
              region.width,
              region.height,
              1, // processInteractiveMask返回的是单通道蒙版
            );
          }

          if (processResult.backgroundMask != null) {
            backStrokes = _convertBytesToImageData(
              processResult.backgroundMask!,
              region.width,
              region.height,
              1, // processInteractiveMask返回的是单通道蒙版
            );
          }
        } else {
          throw Exception('区域笔触数据处理失败: ${processResult.errorMessage}');
        }
      }

      // 准备完整图像的上次蒙版
      ImageData? latestFullMask;
      if (mattingMaskDataInfo.fullMaskBytes != null) {
        latestFullMask = _convertBytesToImageData(
          mattingMaskDataInfo.fullMaskBytes!,
          imageData.width,
          imageData.height,
          4, // RGBA格式
        );
      }

      // 执行区域交互式抠图
      PGLog.d('开始执行区域交互式抠图算法...');
      final regionalStartTime = DateTime.now();

      final regionalResult = await SalientMattersService.regionalInteractive(
        croppedImage,
        region,
        latestMask: latestMask,
        foreStrokes: foreStrokes,
        backStrokes: backStrokes,
        latestFullMask: latestFullMask,
        returnRegionalResult: true, // 需要区域结果
      );

      final regionalEndTime = DateTime.now();
      final regionalDuration = regionalEndTime.difference(regionalStartTime);

      if (!regionalResult.isSuccess) {
        throw Exception('区域交互式抠图失败: ${regionalResult.errorMessage}');
      }

      if (regionalResult.fullResult == null) {
        throw Exception('区域交互式抠图结果为空');
      }

      // 将结果数据转换为ImageData对象（完整图像尺寸的结果）
      final resultImageData = ImageData(
        data: regionalResult.fullResult!,
        width: imageData.width,
        height: imageData.height,
        channels: 4,
      );

      PGLog.d(
          '区域交互式抠图算法完成，结果尺寸: ${resultImageData.width}x${resultImageData.height}, 耗时: ${regionalDuration.inMilliseconds}ms');

      // 更新蒙版数据信息对象
      final updatedMattingMaskDataInfo = AigcMattingMaskDarwPathImageDataInfo(
        regionalFrameDataInfo: mattingMaskDataInfo.regionalFrameDataInfo,
        imagePath: mattingMaskDataInfo.imagePath,
        displayImageBytes: null, // 原图不进行传递，回调的地方不会进行赋值
        previousMaskBytes: regionalResult.regionalResult, // 更新为区域交互式抠图结果
        isBrushMode: mattingMaskDataInfo.isBrushMode,
        currentStrokesBytes: null,
      )
        ..foreStrokesBytes = foreStrokes?.data
        ..backStrokesBytes = backStrokes?.data
        ..fullMaskBytes = regionalResult.fullResult
        ..regionalImageBytes = mattingMaskDataInfo.regionalImageBytes;

      try {
        await updatedMattingMaskDataInfo.saveToDirectory(
            AigcMattingType.regionalInteractive, outputPath, resultImageData);
      } catch (e) {
        PGLog.e('保存交互式蒙版失败: $e');
        throw Exception('保存交互式蒙版失败: $e');
      }

      final processEndTime = DateTime.now();
      final totalDuration = processEndTime.difference(processStartTime);

      PGLog.d('区域交互式蒙版处理总耗时: ${totalDuration.inMilliseconds}ms');

      return AigcInteractiveMaskTaskResult(
        outputPath: outputPath,
        mattingMaskDataInfo: updatedMattingMaskDataInfo,
      );
    }
  }

  /// 处理全图交互式抠图（原有逻辑）
  Future<AigcInteractiveMaskTaskResult> _processFullImageInteractiveMask({
    required ImageData imageData,
    required String outputPath,
    required AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo,
    required DateTime processStartTime,
  }) async {
    // 4. 准备交互数据
    ImageData? previousMask;
    ImageData? foregroundStrokes;
    ImageData? backgroundStrokes;

    // 处理上次蒙版数据
    if (mattingMaskDataInfo.fullMaskBytes != null) {
      previousMask = _convertBytesToImageData(
        mattingMaskDataInfo.fullMaskBytes!,
        imageData.width,
        imageData.height,
        4, // RGBA格式
      );
    }

    // 5. 处理用户笔触数据（使用processInteractiveMask进行预处理）
    if (_isValidStrokeData(mattingMaskDataInfo.currentStrokesBytes)) {
      PGLog.d('开始处理用户笔触数据...');
      final strokeProcessStartTime = DateTime.now();

      // 将笔触数据转换为ImageData
      final strokeMask = _convertBytesToImageData(
        mattingMaskDataInfo.currentStrokesBytes!,
        imageData.width,
        imageData.height,
        4, // 笔触数据通常是RGBA格式
      );

      // 使用processInteractiveMask预处理前景和背景蒙版
      final processResult = await SalientMattersService.processInteractiveMask(
        strokeMask,
        mattingMaskDataInfo.foreStrokesBytes, // 使用上次的前景蒙版数据
        mattingMaskDataInfo.backStrokesBytes, // 使用上次的背景蒙版数据
        mattingMaskDataInfo.isBrushMode, // 涂抹为true，擦除为false
      );

      final strokeProcessEndTime = DateTime.now();
      final strokeProcessDuration =
          strokeProcessEndTime.difference(strokeProcessStartTime);

      if (processResult.isSuccess) {
        PGLog.d('笔触数据处理成功，耗时: ${strokeProcessDuration.inMilliseconds}ms');

        // 使用处理后的蒙版数据作为前景/背景笔触
        if (processResult.foregroundMask != null) {
          foregroundStrokes = _convertBytesToImageData(
            processResult.foregroundMask!,
            imageData.width,
            imageData.height,
            1, // processInteractiveMask返回的是单通道蒙版
          );
          PGLog.d('前景蒙版数据已生成，大小: ${processResult.foregroundMask!.length} 字节');
        }

        if (processResult.backgroundMask != null) {
          backgroundStrokes = _convertBytesToImageData(
            processResult.backgroundMask!,
            imageData.width,
            imageData.height,
            1, // processInteractiveMask返回的是单通道蒙版
          );
          PGLog.d('背景蒙版数据已生成，大小: ${processResult.backgroundMask!.length} 字节');
        }
      } else {
        PGLog.w('笔触数据处理失败: ${processResult.errorMessage}，将使用原始数据');
        throw Exception('笔触数据处理失败: ${processResult.errorMessage}');
      }
    } else {
      // 如果没有笔触数据，不进行后续的处理
      throw Exception('没有笔触数据');
    }

    // 6. 执行交互式抠图
    PGLog.d('开始执行交互式抠图算法...');
    final interactiveStartTime = DateTime.now();

    final interactiveResult = await SalientMattersService.interactive(imageData,
        foreStrokes: foregroundStrokes,
        backStrokes: backgroundStrokes,
        latestMask: previousMask);

    final interactiveEndTime = DateTime.now();
    final interactiveDuration =
        interactiveEndTime.difference(interactiveStartTime);

    if (!interactiveResult.isSuccess) {
      throw Exception('交互式抠图失败: ${interactiveResult.errorMessage}');
    }

    if (interactiveResult.imageData == null) {
      throw Exception('交互式抠图结果为空');
    }

    // 7. 将结果数据转换为ImageData对象（结果是4通道RGBA）
    final resultImageData = ImageData(
      data: interactiveResult.imageData!,
      width: imageData.width,
      height: imageData.height,
      channels: 4,
    );

    debugPrint(
        '交互式抠图算法完成，结果尺寸: ${resultImageData.width}x${resultImageData.height}, 耗时: ${interactiveDuration.inMilliseconds}ms');

    // final savedPath =
    //     await ImageSaveHelper.saveAsPng(resultImageData, outputPath);
    // 更新蒙版数据信息对象
    final updatedMattingMaskDataInfo = AigcMattingMaskDarwPathImageDataInfo(
      regionalFrameDataInfo: mattingMaskDataInfo.regionalFrameDataInfo,
      imagePath: mattingMaskDataInfo.imagePath,
      displayImageBytes: null, // 原图不进行传递，回调的地方不会进行赋值
      previousMaskBytes: null,
      isBrushMode: mattingMaskDataInfo.isBrushMode,
      currentStrokesBytes: null,
    )
      ..foreStrokesBytes = foregroundStrokes?.data
      ..backStrokesBytes = backgroundStrokes?.data
      ..fullMaskBytes = interactiveResult.imageData
      ..regionalImageBytes = null;

    // 8. 保存结果
    try {
      await updatedMattingMaskDataInfo.saveToDirectory(
          AigcMattingType.fullImageInteractive, outputPath, resultImageData);
    } catch (e) {
      PGLog.e('保存交互式蒙版失败: $e');
      throw Exception('保存交互式蒙版失败: $e');
    }

    final processEndTime = DateTime.now();

    final totalDuration = processEndTime.difference(processStartTime);
    PGLog.d(
        '交互式蒙版处理总耗时: ${totalDuration.inMilliseconds}ms [包含笔触预处理和交互式抠图算法: ${interactiveDuration.inMilliseconds}ms]');

    return AigcInteractiveMaskTaskResult(
      outputPath: outputPath,
      mattingMaskDataInfo: updatedMattingMaskDataInfo,
    );
  }

  /// 将字节数据转换为ImageData
  ImageData _convertBytesToImageData(
    Uint8List bytes,
    int width,
    int height,
    int channels,
  ) {
    return ImageData(
      data: bytes,
      width: width,
      height: height,
      channels: channels,
    );
  }

  /// 验证笔触数据是否有效
  bool _isValidStrokeData(Uint8List? strokeData) {
    return strokeData != null && strokeData.isNotEmpty;
  }

  /// 保存调试图像数据到指定文件夹
  Future<void> _saveDebugImageData(
      ImageData imageData, String filename, int sequence) async {
    try {
      final debugDir =
          Directory(r'C:\Users\<USER>\Desktop\TestPage\$sequence');
      if (!debugDir.existsSync()) {
        debugDir.createSync(recursive: true);
      }

      final debugPath = path.join(debugDir.path, '$filename.png');

      final savedPath = await ImageSaveHelper.saveAsPng(imageData, debugPath);
      if (savedPath != null) {
        PGLog.d('调试图像已保存 [序号: $sequence]: $savedPath');
      } else {
        PGLog.w('调试图像保存失败 [序号: $sequence]: $debugPath');
      }
    } catch (e) {
      PGLog.e('保存调试图像时出错 [序号: $sequence]: $e');
    }
  }
}
