import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_queue.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../task_queue_system/generic_scheduler.dart';
import '../task_queue_system/worker_channel.dart';
import 'models/aigc_scheduling_strategies.dart';
import 'models/aigc_task.dart';
import 'models/aigc_task_priority.dart';
import 'processors/aigc_processor_factory.dart';

/// AIGC预处理服务 - 基于通用任务队列系统的业务实现
class AigcService extends ChangeNotifier {
  final int _maxWorkers;

  final List<WorkerChannel> _workers = [];
  late final GenericScheduler<AigcTask, AigcQueueType> _taskScheduler;

  // 分类消息流 - 使用类型安全的消息
  late final StreamController<AigcTaskResultMessage> _resultController;
  late final StreamController<AigcTaskProgressMessage> _progressController;

  // 正在处理的任务跟踪 - 防止重复提交
  final Map<String, AigcTask> _processingTasks = {};

  // 对外暴露的流
  Stream<AigcTaskResultMessage> get resultStream => _resultController.stream;
  Stream<AigcTaskProgressMessage> get progressStream =>
      _progressController.stream;

  Completer<void>? _initializeCompleter;

  /// 生成任务ID - 确保不同类型任务有不同ID
  static String generateTaskId(String inputPath, AigcTaskType taskType) {
    return '${inputPath.hashCode}_${taskType.name}';
  }

  AigcService({int maxWorkers = 1}) : _maxWorkers = maxWorkers {
    _resultController = StreamController<AigcTaskResultMessage>.broadcast();
    _progressController = StreamController<AigcTaskProgressMessage>.broadcast();

    _taskScheduler = GenericScheduler<AigcTask, AigcQueueType>(
      initialStrategy: AigcSchedulingStrategies.defaultStrategy,
    );
  }

  /// 初始化处理器
  Future<void> initialize() async {
    if (_initializeCompleter != null) {
      return _initializeCompleter!.future;
    }

    // 开始新的初始化
    _initializeCompleter = Completer<void>();

    try {
      PGLog.d('🏭 初始化AIGC服务，创建 $_maxWorkers 个工作器...');

      for (int i = 0; i < _maxWorkers; i++) {
        final workerId = 'aigc_worker_$i';
        final channel = await WorkerChannel.create(
          workerId,
          initializer: AigcProcessorFactory.initialize,
        );

        // 监听工作器消息
        channel.messageStream.listen((message) {
          _handleWorkerMessage(channel, message);
        });

        _workers.add(channel);
      }

      PGLog.d('✅ AIGC服务初始化完成，${_workers.length} 个工作器就绪');
      _initializeCompleter!.complete();
    } catch (e) {
      PGLog.d('❌ 初始化失败: $e');
      _initializeCompleter!.completeError(e);
      rethrow;
    }
  }

  /// 处理工作器消息
  void _handleWorkerMessage(WorkerChannel channel, dynamic message) {
    if (message is AigcTaskResultMessage) {
      final inputPath = message.payload.inputPath;
      PGLog.d(
          '✅ 工作器 ${channel.workerId} 完成任务: $inputPath (${message.processingTime.inMilliseconds}ms)');

      // 从正在处理的跟踪中移除任务
      final taskId = message.taskId;
      _processingTasks.remove(taskId);
      PGLog.d('📋 任务处理完成移除跟踪: $taskId - 剩余正在处理任务数: ${_processingTasks.length}');

      // 转发类型安全的结果消息到业务层
      _resultController.add(message);

      // 检查是否有更多任务需要分配
      _assignTaskToWorker(channel);
    } else if (message is AigcTaskProgressMessage) {
      final currentStep = message.status;
      PGLog.d(
          '📊 工作器 ${channel.workerId} 进度: $currentStep - ${message.progress}%');
      _progressController.add(message);
    } else {
      PGLog.d('❌ 工作器 ${channel.workerId} 发送了未知消息类型: ${message.runtimeType}');
    }
  }

  /// 分配任务给工作器
  void _assignTaskToWorker(WorkerChannel channel) {
    final task = _taskScheduler.getNextTask();
    if (task == null) {
      PGLog.d('😴 队列为空，工作器 ${channel.workerId} 进入空闲状态');
      return;
    }

    // 将任务加入正在处理的跟踪
    _processingTasks[task.taskId] = task;
    PGLog.d(
        '🔄 任务开始处理: ${task.inputPath} (${task.taskType}) - 正在处理任务数: ${_processingTasks.length}');

    // 为兼容通用任务处理器，发送Legacy消息
    final taskMessage = AigcTaskMessage(
      taskId: task.taskId,
      processorKey: task.taskType.name,
      payload: task,
    );

    channel.sendTask(taskMessage);
  }

  /// 创建具体类型的任务
  AigcTask _createTask(
      {required String inputPath,
      required String outputPath,
      required String fileId,
      required AigcTaskType taskType,
      required int priority}) {
    final taskId = generateTaskId(inputPath, taskType);
    final now = DateTime.now();

    switch (taskType) {
      case AigcTaskType.thumbnail:
        return AigcThumbnailTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
      case AigcTaskType.cover:
        return AigcCoverTask.create(
          taskId: taskId,
          inputPath: inputPath,
          outputPath: outputPath,
          fileId: fileId,
          submittedAt: now,
          priority: priority,
        );
      case AigcTaskType.mask:
        return AigcMaskTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
      case AigcTaskType.interactiveMask:
        // 注意：交互式蒙版任务需要额外的参数，这里直接抛出异常
        throw Exception('交互式蒙版任务需要额外的参数，请使用 submitInteractiveMaskTask 方法');
      case AigcTaskType.image:
        return AigcImageTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
      case AigcTaskType.rawConversion:
        return AigcRawConversionTask.create(
            taskId: taskId,
            inputPath: inputPath,
            outputPath: outputPath,
            fileId: fileId,
            submittedAt: now,
            priority: priority);
    }
  }

  /// 统一的图像处理请求 - 智能处理新任务/优先级更新/忽略重复
  Future<void> submitTask({
    required String inputPath,
    required String outputPath,
    required String fileId,
    required AigcTaskType taskType,
    int sortBy = 0, // 图片索引，从0开始，用于按顺序处理
    bool executeNow = false, // 是否立即执行
  }) async {
    // 确保已初始化
    await initialize();

    final taskId = generateTaskId(inputPath, taskType);

    // 检查任务是否正在处理中
    if (_processingTasks.containsKey(taskId)) {
      PGLog.d('⚠️ 任务正在处理中，跳过重复提交: $inputPath ($taskType)');
      return;
    }

    // 根据任务类型和索引计算内部优先级
    final priority = _calculatePriority(taskType, sortBy, executeNow);

    // 尝试更新已存在任务的优先级
    if (_taskScheduler.updateTaskPriorityById(taskId, AigcTask, priority)) {
      PGLog.d('🔄 任务排序已更新: $inputPath (index: $sortBy, priority: $priority)');
      return;
    }
    // 添加新任务
    final task = _createTask(
        inputPath: inputPath,
        outputPath: outputPath,
        fileId: fileId,
        taskType: taskType,
        priority: priority);

    _taskScheduler.addTask(task, executeNow: executeNow);

    final executeNowText = executeNow ? ' [立即执行]' : '';
    PGLog.d(
        '➕ 新任务已加入队列: $inputPath (index: $sortBy, priority: $priority)$executeNowText');

    // 尝试分配给空闲工作器
    final idleWorker = _workers.where((w) => w.isIdle).firstOrNull;
    if (idleWorker != null) {
      PGLog.d('📲 通知空闲工作器处理新任务');
      _assignTaskToWorker(idleWorker);
    } else {
      PGLog.d('⏳ 所有工作器忙碌中，任务已按排序加入队列');
    }
  }

  /// 根据任务类型、索引和执行模式计算内部优先级
  int _calculatePriority(AigcTaskType taskType, int sortBy, bool executeNow) {
    // 如果是立即执行，使用特殊优先级（负数）
    if (executeNow) {
      switch (taskType) {
        case AigcTaskType.thumbnail:
          return AigcTaskSortBy.selectedPreview;
        case AigcTaskType.cover:
          return AigcTaskSortBy.selectedCover;
        case AigcTaskType.mask:
          return AigcTaskSortBy.selectedMask;
        case AigcTaskType.interactiveMask:
          return AigcTaskSortBy.selectedInteractiveMask;
        case AigcTaskType.image:
          return AigcTaskSortBy.selectedImage;
        case AigcTaskType.rawConversion:
          return AigcTaskSortBy.selectedImage; // 使用图像处理类似的优先级
      }
    }

    // 普通任务根据类型和索引计算优先级
    switch (taskType) {
      case AigcTaskType.thumbnail:
        return AigcTaskSortBy.preview(sortBy);
      case AigcTaskType.cover:
        return AigcTaskSortBy.cover(sortBy);
      case AigcTaskType.mask:
        return AigcTaskSortBy.mask(sortBy);
      case AigcTaskType.interactiveMask:
        return AigcTaskSortBy.interactiveMask(sortBy);
      case AigcTaskType.image:
        return AigcTaskSortBy.image(sortBy);
      case AigcTaskType.rawConversion:
        return AigcTaskSortBy.image(sortBy); // 使用图像处理类似的优先级
    }
  }

  /// 专门用于提交交互式蒙版任务的方法
  Future<void> submitInteractiveMaskTask({
    required String inputPath,
    required String outputPath,
    required String fileId,
    required AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo,
    String? modelName,
    double? threshold,
    int sortBy = 0,
    bool executeNow = false,
  }) async {
    // 确保已初始化
    await initialize();

    final taskId = generateTaskId(inputPath, AigcTaskType.interactiveMask);

    // 检查任务是否正在处理中
    if (_processingTasks.containsKey(taskId)) {
      PGLog.d('⚠️ 交互式蒙版任务正在处理中，跳过重复提交: $inputPath');
      return;
    }

    // 计算优先级
    final priority =
        _calculatePriority(AigcTaskType.interactiveMask, sortBy, executeNow);

    // 尝试更新已存在任务的优先级
    if (_taskScheduler.updateTaskPriorityById(taskId, AigcTask, priority)) {
      PGLog.d(
          '🔄 交互式蒙版任务排序已更新: $inputPath (index: $sortBy, priority: $priority)');
      return;
    }

    // 创建交互式蒙版任务
    final task = AigcInteractiveMaskTask.create(
      taskId: taskId,
      inputPath: inputPath,
      outputPath: outputPath,
      fileId: fileId,
      submittedAt: DateTime.now(),
      priority: priority,
      mattingMaskDataInfo: mattingMaskDataInfo,
      modelName: modelName,
      threshold: threshold,
    );

    _taskScheduler.addTask(task, executeNow: executeNow);

    final executeNowText = executeNow ? ' [立即执行]' : '';
    PGLog.d(
        '➕ 新交互式蒙版任务已加入队列: $inputPath (index: $sortBy, priority: $priority)$executeNowText');

    // 尝试分配给空闲工作器
    final idleWorker = _workers.where((w) => w.isIdle).firstOrNull;
    if (idleWorker != null) {
      PGLog.d('📲 通知空闲工作器处理新交互式蒙版任务');
      _assignTaskToWorker(idleWorker);
    } else {
      PGLog.d('⏳ 所有工作器忙碌中，交互式蒙版任务已按排序加入队列');
    }
  }

  /// 移除指定的任务
  bool removeTask({
    required String inputPath,
    required AigcTaskType taskType,
  }) {
    final taskId = generateTaskId(inputPath, taskType);

    // 从队列中移除
    final removed = _taskScheduler.removeTaskById(taskId, AigcTask);

    // 从正在处理的跟踪中移除（如果存在）
    final processingRemoved = _processingTasks.remove(taskId) != null;

    if (removed || processingRemoved) {
      PGLog.d(
          '🗑️ 已移除任务: $inputPath ($taskType) - 队列移除:$removed, 处理中移除:$processingRemoved');
    } else {
      PGLog.d('⚠️ 任务不存在，无法移除: $inputPath ($taskType)');
    }

    return removed || processingRemoved;
  }

  /// 根据输入路径移除所有相关任务
  int removeTasksByPath(String inputPath) {
    // 从队列中移除
    final removedCount = _taskScheduler.removeTasksWhere((task) {
      return task.inputPath == inputPath;
    });

    // 从正在处理的跟踪中移除
    final processingToRemove = _processingTasks.entries
        .where((entry) => entry.value.inputPath == inputPath)
        .map((entry) => entry.key)
        .toList();

    for (final taskId in processingToRemove) {
      _processingTasks.remove(taskId);
    }

    final totalRemoved = removedCount + processingToRemove.length;
    if (totalRemoved > 0) {
      PGLog.d(
          '🗑️ 已移除 $totalRemoved 个任务，路径: $inputPath (队列:$removedCount, 处理中:${processingToRemove.length})');
    } else {
      PGLog.d('⚠️ 未找到路径相关的任务: $inputPath');
    }

    return totalRemoved;
  }

  /// 根据任务类型移除所有任务
  int removeTasksByType(AigcTaskType taskType) {
    // 从队列中移除
    final removedCount = _taskScheduler.removeTasksWhere((task) {
      return task.taskType == taskType;
    });

    // 从正在处理的跟踪中移除
    final processingToRemove = _processingTasks.entries
        .where((entry) => entry.value.taskType == taskType)
        .map((entry) => entry.key)
        .toList();

    for (final taskId in processingToRemove) {
      _processingTasks.remove(taskId);
    }

    final totalRemoved = removedCount + processingToRemove.length;
    if (totalRemoved > 0) {
      PGLog.d(
          '🗑️ 已移除 $totalRemoved 个 ${taskType.displayName} 任务 (队列:$removedCount, 处理中:${processingToRemove.length})');
    } else {
      PGLog.d('⚠️ 未找到 ${taskType.displayName} 类型的任务');
    }

    return totalRemoved;
  }

  /// 根据优先级范围移除任务
  int removeTasksByPriorityRange({
    int? minPriority,
    int? maxPriority,
  }) {
    final removedCount = _taskScheduler.removeTasksWhere((task) {
      final priority = task.priority;

      if (minPriority != null && priority < minPriority) {
        return false;
      }

      if (maxPriority != null && priority > maxPriority) {
        return false;
      }

      return true;
    });

    if (removedCount > 0) {
      final rangeText = '${minPriority ?? '∞'} - ${maxPriority ?? '∞'}';
      PGLog.d('🗑️ 已移除 $removedCount 个优先级在 $rangeText 范围内的任务');
    }

    return removedCount;
  }

  /// 清空所有任务
  void clearAllTasks() {
    _taskScheduler.clearAll();
    _processingTasks.clear();
    PGLog.d('🧹 已清空所有任务队列和正在处理的任务');
  }

  /// 获取指定路径的任务数量（所有类型）
  int getTaskCountByPath(String inputPath) {
    int count = 0;

    // 计算每种任务类型下指定路径的任务数量（队列中的）
    for (final taskType in AigcTaskType.values) {
      if (hasTask(inputPath: inputPath, taskType: taskType)) {
        count++;
      }
    }

    // 计算正在处理中的相同路径任务
    final processingCount = _processingTasks.values
        .where((task) => task.inputPath == inputPath)
        .length;

    return count + processingCount;
  }

  /// 获取指定任务类型的任务数量
  int getTaskCountByType(AigcTaskType taskType) {
    // 队列中的任务数量
    final queueCount = _taskScheduler.getTaskCount(_getTaskTypeClass(taskType));

    // 正在处理的任务数量
    final processingCount = _processingTasks.values
        .where((task) => task.taskType == taskType)
        .length;

    return queueCount + processingCount;
  }

  /// 检查指定任务是否存在（队列中或正在处理中）
  bool hasTask({
    required String inputPath,
    required AigcTaskType taskType,
  }) {
    final taskId = generateTaskId(inputPath, taskType);

    // 检查队列中是否存在
    final inQueue = _taskScheduler.hasTask(taskId, AigcTask);

    // 检查是否正在处理中
    final inProcessing = _processingTasks.containsKey(taskId);

    return inQueue || inProcessing;
  }

  /// 获取所有任务的统计信息
  Map<String, dynamic> getDetailedStats() {
    final queueStats = getQueueStats();
    final typeStats = <String, int>{};

    for (final taskType in AigcTaskType.values) {
      typeStats[taskType.displayName] = getTaskCountByType(taskType);
    }

    return {
      'totalTasks': totalTaskCount,
      'queueStats':
          queueStats.map((key, value) => MapEntry(key.displayName, value)),
      'typeStats': typeStats,
      'currentStrategy': currentStrategyName,
      'workerCount': _workers.length,
      'processingTasks': _processingTasks.length, // 新增：正在处理的任务数
      // 'activeWorkers': _workers.where((w) => !w.isIdle).length,
    };
  }

  /// 根据任务类型获取对应的类类型
  Type _getTaskTypeClass(AigcTaskType taskType) {
    switch (taskType) {
      case AigcTaskType.thumbnail:
        return AigcThumbnailTask;
      case AigcTaskType.cover:
        return AigcThumbnailTask;
      case AigcTaskType.mask:
        return AigcMaskTask;
      case AigcTaskType.interactiveMask:
        return AigcInteractiveMaskTask;
      case AigcTaskType.image:
        return AigcImageTask;
      case AigcTaskType.rawConversion:
        return AigcRawConversionTask;
    }
  }

  void setPriorityStrategy(
      SchedulingStrategy<AigcTask, AigcQueueType> strategy) {
    _taskScheduler.setStrategy(strategy);
  }

  /// 获取当前策略名称
  String get currentStrategyName => _taskScheduler.currentStrategyName;

  /// 获取队列统计信息
  Map<AigcQueueType, int> getQueueStats() => _taskScheduler.getQueueStats();

  /// 获取总任务数量
  int get totalTaskCount => _taskScheduler.totalTaskCount;

  /// 清理资源
  @override
  void dispose() {
    for (final worker in _workers) {
      worker.dispose();
    }
    _workers.clear();
    _processingTasks.clear(); // 清理正在处理的任务跟踪
    _resultController.close();
    _progressController.close();
    super.dispose();
  }
}
