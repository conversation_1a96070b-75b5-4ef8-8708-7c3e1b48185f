import 'aigc_task.dart';

/// 任务排序常量定义
/// 数值越小优先级越高，使用索引作为主要排序依据
class AigcTaskSortBy {
  AigcTaskSortBy._();

  // 特殊排序值（负数 = 最优先）
  static const int selectedPreview = -100; // 选中图片的预览任务
  static const int selectedMask = -99; // 选中图片的蒙版任务
  static const int selectedInteractiveMask = -98; // 选中图片的交互式蒙版任务
  static const int selectedImage = -97; // 选中图片的图像生成任务
  static const int selectedRawConversion = -96; // 选中图片的智能调色任务
  static const int selectedCover = -95; // 选中图片的封面任务

  // 基于索引的排序基数
  static const int previewBase = 10000; // 预览任务基数
  static const int maskBase = 100000; // 蒙版任务基数
  static const int interactiveMaskBase = 200000; // 交互式蒙版任务基数
  static const int imageBase = 300000; // 图像生成任务基数
  static const int rawConversionBase = 400000; // 智能调色任务基数
  static const int coverBase = 500000; // 封面任务基数

  // 最大支持的图片数量（防止排序冲突）
  static const int maxSupportedImages = 10000;

  /// 计算预览任务的排序值
  /// [index] 图片在列表中的索引，从0开始
  static int preview(int index) {
    if (index < 0 || index >= maxSupportedImages) {
      throw ArgumentError('索引 $index 超出有效范围 0-${maxSupportedImages - 1}');
    }
    return previewBase + index;
  }

  /// 计算蒙版任务的排序值
  /// [index] 图片在列表中的索引，从0开始
  static int mask(int index) {
    if (index < 0 || index >= maxSupportedImages) {
      throw ArgumentError('索引 $index 超出有效范围 0-${maxSupportedImages - 1}');
    }
    return maskBase + index;
  }

  /// 计算交互式蒙版任务的排序值
  /// [index] 图片在列表中的索引，从0开始
  static int interactiveMask(int index) {
    if (index < 0 || index >= maxSupportedImages) {
      throw ArgumentError('索引 $index 超出有效范围 0-${maxSupportedImages - 1}');
    }
    return interactiveMaskBase + index;
  }

  /// 计算打样任务的排序值
  /// [index] 图片在列表中的索引，从0开始
  static int image(int index) {
    if (index < 0 || index >= maxSupportedImages) {
      throw ArgumentError('索引 $index 超出有效范围 0-${maxSupportedImages - 1}');
    }
    return imageBase + index;
  }

  /// 计算智能调色任务的排序值
  /// [index] 图片在列表中的索引，从0开始
  static int rawConversion(int index) {
    if (index < 0 || index >= maxSupportedImages) {
      throw ArgumentError('索引 $index 超出有效范围 0-${maxSupportedImages - 1}');
    }
    return rawConversionBase + index;
  }

  /// 计算封面任务的排序值
  /// [index] 图片在列表中的索引，从0开始
  static int cover(int index) {
    if (index < 0 || index >= maxSupportedImages) {
      throw ArgumentError('索引 $index 超出有效范围 0-${maxSupportedImages - 1}');
    }
    return coverBase + index;
  }

  /// 根据任务类型和索引自动计算排序值
  /// [taskType] 任务类型
  /// [index] 图片在列表中的索引，从0开始
  /// [isSelected] 是否为当前选中的图片
  static int auto(AigcTaskType taskType, int index, {bool isSelected = false}) {
    if (isSelected) {
      switch (taskType) {
        case AigcTaskType.thumbnail:
          return selectedPreview;
        case AigcTaskType.cover:
          return selectedCover;
        case AigcTaskType.mask:
          return selectedMask;
        case AigcTaskType.interactiveMask:
          return selectedInteractiveMask;
        case AigcTaskType.image:
          return selectedImage;
        case AigcTaskType.rawConversion:
          return selectedRawConversion;
      }
    } else {
      switch (taskType) {
        case AigcTaskType.thumbnail:
          return preview(index);
        case AigcTaskType.cover:
          return cover(index);
        case AigcTaskType.mask:
          return mask(index);
        case AigcTaskType.interactiveMask:
          return interactiveMask(index);
        case AigcTaskType.image:
          return image(index);
        case AigcTaskType.rawConversion:
          return rawConversion(index);
      }
    }
  }
}

// 保持向后兼容性的别名
typedef AigcTaskPriority = AigcTaskSortBy;

/*
使用示例：

// 简洁的外部调用方式（推荐）
_processingService.submitTask(
  inputPath: imagePath,
  taskType: AigcTaskType.thumbnail,
  sortBy: 5, // 直接传入图片索引（第6张图片）
  executeNow: true, // 立即执行
);

// 普通任务按索引顺序处理
_processingService.submitTask(
  inputPath: imagePath,
  taskType: AigcTaskType.mask,
  sortBy: 2, // 第3张图片
  executeNow: false, // 按队列顺序处理
);

优点：
- 外部调用者只需要关心图片索引，不需要了解复杂的优先级计算
- executeNow参数清晰表达是否立即执行的意图
- 内部自动根据任务类型和索引计算最终优先级

内部优先级计算规则：
- executeNow=true：使用特殊优先级（负数，立即执行）
- executeNow=false：根据任务类型 + 索引计算优先级
  - 预览任务：10000 + index
  - 遮罩任务：100000 + index  
  - 打样任务：1000000 + index
  - 导出任务：10000000 + index

数值越小，优先级越高。
*/
