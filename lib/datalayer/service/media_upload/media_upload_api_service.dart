import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';
import 'package:turing_art/datalayer/domain/models/media_upload/media_upload_info_response.dart';

part 'media_upload_api_service.g.dart';

@RestApi()
abstract class MediaUploadApiService {
  factory MediaUploadApiService(Dio dio, {String? baseUrl}) =
      _MediaUploadApiService;

  @GET('/art-aigc/v1/media/url')
  Future<MediaUploadInfoResponse> getMediaUploadInfo(
    @Query('extension') String extension,
  );
}
