import 'dart:io';

import 'package:dio/dio.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/media_upload/media_upload_info_response.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/media_upload/media_upload_api_service.dart';

abstract class MediaUploadService {
  Future<MediaUploadInfoResponse> uploadMedia(
    File file, {
    Function(int sent, int total)? onProgress,
  });
}

/// 媒体上传服务类，提供文件上传功能
class MediaUploadServiceImpl implements MediaUploadService {
  final ApiClient _apiClient;
  late final MediaUploadApiService _uploadApiService;

  late final Dio _uploadDio; // 用于上传文件的 Dio 实例

  MediaUploadServiceImpl(this._apiClient) {
    final dio = DioFactory.createDio(_apiClient);
    _uploadApiService = MediaUploadApiService(dio);
    _uploadDio = Dio();
  }

  /// 上传文件
  /// 流程是：
  /// 1. 首先请求服务端获取上传信息，包括上传URL和其他必要信息
  /// 2. 通过获取的上传URL上传文件
  @override
  Future<MediaUploadInfoResponse> uploadMedia(
    File file, {
    Function(int sent, int total)? onProgress,
  }) async {
    try {
      // 1. 获取文件扩展名
      final extension =
          path.extension(file.path).replaceAll('.', '').toLowerCase();
      if (extension.isEmpty) {
        throw Exception('无法获取文件扩展名');
      }

      // 2. 获取上传信息
      final uploadInfo = await _getMediaUploadInfo(extension);

      // 3. 上传文件
      final uploadSuccess = await _uploadFile(
          file, extension, uploadInfo.uploadUrl,
          onProgress: onProgress);

      if (!uploadSuccess) {
        throw Exception('文件上传失败');
      }

      return uploadInfo;
    } catch (e) {
      throw Exception('媒体上传流程失败: $e');
    }
  }

  /// 获取媒体上传信息
  /// [extension] 文件扩展名
  Future<MediaUploadInfoResponse> _getMediaUploadInfo(String extension) async {
    try {
      return await _uploadApiService.getMediaUploadInfo(extension);
    } catch (e) {
      throw Exception('获取上传信息失败: $e');
    }
  }

  /// 上传文件到指定的上传URL
  Future<bool> _uploadFile(
    File file,
    String extension,
    String uploadUrl, {
    Function(int sent, int total)? onProgress,
  }) async {
    try {
      if (!file.existsSync()) {
        throw Exception('文件不存在');
      }

      // 现阶段仅支持上传 jpg 和 png 格式的图片
      String contentType = '';
      if (extension == 'jpg' || extension == 'jpeg') {
        contentType = 'image/jpeg';
      } else if (extension == 'png') {
        contentType = 'image/png';
      } else {
        throw Exception('不支持的文件类型: $extension');
      }

      final fileBytes = await file.readAsBytes();

      final response = await _uploadDio.put(
        uploadUrl,
        data: fileBytes,
        onSendProgress: onProgress,
        options: Options(
          headers: {
            'Content-Type': contentType,
          },
        ),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('文件上传失败: $e');
    }
  }
}
