import 'dart:convert';
import 'dart:io';

import 'package:pg_ops_sdk/sign/pg_network_sign.dart';

import '../../../config/env_config.dart';

enum HttpMethod { get, post }

class HeaderTool {
  /// 统一的签名方法
  static String getSign(
    Map<String, String> headers,
    Map<String, dynamic> params,
    String path,
    HttpMethod method, {
    required bool forBody,
  }) {
    if (Platform.isIOS || Platform.isMacOS || Platform.isWindows) {
      // 从完整 URL 中提取 host
      final uri = Uri.parse(EnvConfig.baseUrl);
      final host = uri.host; // 只获取域名部分
      Map<String, dynamic> signParams;
      if (forBody) {
        signParams = {...headers};
      } else {
        signParams = {...headers, ...params};
      }
      return PgNetworkSign().sign(
        host,
        path,
        method == HttpMethod.get ? 'GET' : 'POST',
        signParams,
        forBody ? jsonEncode(params) : null,
      );
    } else {
      // 在其他平台上使用 Dart 实现的签名方法
      if (forBody) {
        return '';
      } else {
        return '';
      }
    }
  }
}
