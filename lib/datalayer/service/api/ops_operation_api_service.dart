import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/ops_operation/ops_operation.dart';

part 'ops_operation_api_service.g.dart';

/// 统一操作活动API服务
/// 合并了新用户礼包、微信礼包和版本介绍等API
@RestApi()
abstract class OpsOperationApiService {
  factory OpsOperationApiService(Dio dio, {String? baseUrl}) =
      _OpsOperationApiService;

  /// 获取操作信息
  ///
  /// codes 运营唯一标识，如 new_user_gift, wechat_gift, version_introduce
  @GET("/v1/operational-positions")
  Future<OpsOperationResponse> getOperationInfo(
    @Query('codes') String codes,
  );
}
