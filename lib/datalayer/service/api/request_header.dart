import '../../../config/env_config.dart';
import '../../../utils/device_info_util.dart';
import 'header_tool.dart';

class RequestHeader {
  // PG 自定义头
  static const String appId = 'PG-AppID';
  static const String platform = 'PG-Platform';
  static const String time = 'PG-Time';
  static const String appVersion = 'PG-AppVersion';
  static const String osVersion = 'PG-OSVersion';
  static const String model = 'PG-Model';
  static const String eid = 'PG-EID';
  static const String fa = 'PG-FA';
  static const String channel = 'PG-Channel';
  static const String initStamp = 'PG-InitStamp';
  static const String upgradeStamp = 'PG-UpgradeStamp';
  static const String screenSize = 'PG-ScreenSize';
  static const String language = 'PG-Language';
  static const String utcOffset = 'PG-UtcOffset';
  static const String network = 'PG-Network';
  static const String ench = 'PG-ENCH';
  static const String encb = 'PG-ENCB';
  static const String locale = 'PG-Locale';
  static const String userId = 'PG-UserID';
  static const String userToken = 'PG-UserToken';
  static const String debug = 'PG-Debug';
  static const String dataEnv = 'PG-DataEnv';
  static const String sign = 'PG-Sign';
  static const String appChannel = 'APP-Channel';
  static const String storeId = 'PG-StoreID';
  static const String employeeID = 'PG-EmployeeID';
  // 缓存的用户信息
  static String? _cachedUserId;
  static String? _cachedUserToken;
  static String? _cachedStoreId;
  static String? _cachedEmployeeID;

  /// 获取默认请求头
  static final Map<String, String> _baseHeaders = {
    appId: EnvConfig.appId,
    platform: DeviceInfoUtil().platform,
    time: DateTime.now().millisecondsSinceEpoch.toString(),
    appVersion: EnvConfig.version,
    osVersion: DeviceInfoUtil().osVersion,
    model: DeviceInfoUtil().model,
    eid: DeviceInfoUtil().deviceId,
    fa: '',
    channel: DeviceInfoUtil().channel,
    initStamp: DeviceInfoUtil().initStamp,
    upgradeStamp: DeviceInfoUtil().initStamp,
    screenSize: DeviceInfoUtil().screenSize,
    language: DeviceInfoUtil().language,
    utcOffset: DeviceInfoUtil().utcOffset,
    network: DeviceInfoUtil().network,
    ench: '0',
    encb: '0',
    locale: DeviceInfoUtil().locale,
    userId: '',
    userToken: '',
    // debug: DeviceInfoUtil().isDebugMode ? '1' : '0',
    dataEnv: EnvConfig.environment.name,
    storeId: _cachedStoreId ?? '',
    employeeID: _cachedEmployeeID ?? '',
  };

  /// 获取带签名的请求头
  static Map<String, dynamic> getSignedHeaders(
    String path,
    HttpMethod method,
    Map<String, dynamic> params, {
    required bool forBody,
    bool isForH5 = false,
  }) {
    final headers = Map<String, String>.from(_baseHeaders);
    // 添加用户信息
    headers[userId] = _cachedUserId ?? '';
    headers[userToken] = _cachedUserToken ?? '';
    headers[storeId] = _cachedStoreId ?? '';
    headers[employeeID] = _cachedEmployeeID ?? '';
    headers[sign] = HeaderTool.getSign(
      headers,
      params,
      path,
      method,
      forBody: forBody,
    );
    return headers;
  }

  /// 更新请求头中的用户信息
  static void updateUserInfo({
    String? userId,
    String? userToken,
    String? storeId,
    String? employeeID,
  }) {
    _cachedUserId = userId;
    _cachedUserToken = userToken;
    _cachedStoreId = storeId;
    _cachedEmployeeID = employeeID;
  }

  /// 更新请求头中的店铺ID(4-18流程变更后，获取手机号所有门店，选择一个门店后，更新店铺ID)
  static void updateStoreId(String storeId) {
    _cachedStoreId = storeId;
  }

  /// 更新PG-Time时间戳
  ///
  /// [customTime] 自定义的时间戳值
  static void updateCustomTime(String customTime) {
    _baseHeaders[time] = customTime;
  }

  /// 获取当前的PG-Time时间戳值
  ///
  /// 返回当前使用的PG-Time值
  static String getCurrentTime() {
    return _baseHeaders[time] ??
        DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// 更新请求头中的店铺ID(4-18流程变更后，获取手机号所有门店，选择一个门店后，更新店铺ID)
  static void updateEmployeeId(String employeeId) {
    _cachedEmployeeID = employeeId;
  }
}
