import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_list.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_summary.dart';

part 'employee_api_service.g.dart';

/// 子账号管理 API 服务
@RestApi()
abstract class EmployeeApiService {
  factory EmployeeApiService(Dio dio) = _EmployeeApiService;

  /// 导出张数汇总
  @GET('/v1/export/summary')
  Future<EmployeeSummary> getEmployeeSummary();

  /// 获取子账号列表
  @GET('/v1/account/employee/list')
  Future<EmployeeList> getEmployeeList();

  /// 添加子账号
  @POST('/v1/account/employee/add')
  Future<dynamic> addEmployee(@Body() Map<String, dynamic> data);

  /// 发送子账号验证码
  @POST('/v1/account/send-2fa-code')
  Future<bool> sendVerificationCode();

  /// 更新子账号
  @POST('/v1/account/employee/update')
  Future<dynamic> updateEmployee(@Body() Map<String, dynamic> data);
}
