import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/coupon/coupon.dart';

part 'coupon_api_service.g.dart';

/// 兑换码相关 API 服务
@RestApi()
abstract class CouponApiService {
  factory CouponApiService(Dio dio) = _CouponApiService;

  /// 应用兑换码
  @POST('/v1/account/coupon/apply')
  Future<Coupon> applyCoupon(@Body() Map<String, dynamic> body);
}
