import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/store/store.dart';

part 'current_user_store_info_api_service.g.dart';

@RestApi()
abstract class CurrentUserStoreInfoApiService {
  factory CurrentUserStoreInfoApiService(Dio dio, {String? baseUrl}) =
      _CurrentUserStoreInfoApiService;

  /// 获取当前用户Store信息
  @GET('/v1/account/info')
  Future<Store> getCurrentUserStore();
}
