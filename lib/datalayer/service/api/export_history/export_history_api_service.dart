import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/aigc_export_history/aigc_export_history_model.dart';
import 'package:turing_art/datalayer/domain/models/export_history/export_history_model.dart';

part 'export_history_api_service.g.dart';

/// 导出交易历史记录 API 服务
@RestApi()
abstract class ExportHistoryApiService {
  factory ExportHistoryApiService(Dio dio) = _ExportHistoryApiService;

  /// 获取导出交易历史记录
  @GET('/v1/export/transactions')
  Future<ExportHistoryModel> fetchExportHistory({
    @Query('page') String? page,
    @Query('pageSize') String? pageSize,
    @Query('createdAfter') int? createdAfter,
    @Query('createdBefore') int? createdBefore,
    @Query('employeeIds') String? employeeIds,
    @Query('type') int? type,
  });

  /// 获取AIGC导出历史记录
  @GET('/art-aigc/v1/points/consumption/list')
  Future<AIGCExportHistoryModel> fetchAIGCExportHistory({
    @Query('page') String? page,
    @Query('page_size') String? pageSize,
    @Query('start_time') String? startTime,
    @Query('end_time') String? endTime,
  });
}
