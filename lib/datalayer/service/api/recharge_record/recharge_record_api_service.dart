import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/recharge_record/recharge_record_model.dart';

part 'recharge_record_api_service.g.dart';

/// 充值记录 API 服务
@RestApi()
abstract class RechargeRecordApiService {
  factory RechargeRecordApiService(Dio dio) = _RechargeRecordApiService;

  /// 获取充值记录
  @GET('/v1/account/amounts/reloads')
  Future<RechargeRecordModel> fetchRechargeRecords({
    @Query('page') String? page,
    @Query('pageSize') String? pageSize,
    @Query('bankType') String? bankType,
    @Query('startedAt') String? startedAt,
    @Query('endAt') String? endedAt,
  });
}
