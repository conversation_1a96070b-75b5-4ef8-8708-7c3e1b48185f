import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/order_dependent/check_order_status.dart';
import 'package:turing_art/datalayer/domain/models/order_dependent/create_order_response.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_info_model.dart';

part 'purchase_api_service.g.dart';

/// 购买相关的 API 服务接口
@RestApi()
abstract class PurchaseApiService {
  factory PurchaseApiService(Dio dio, {String? baseUrl}) = _PurchaseApiService;

  /// 获取购买套餐卡片
  @GET("/v1/product-positions/PhotoRetouch/products")
  Future<PurchaseInfoModel> getPurchaseCards();

  /// 获取AIGC积分套餐卡片
  @GET("/v1/product-positions/AIGC-Retouch/products")
  Future<PurchaseInfoModel> getAigcPointPurchaseCards();

  /// 新增创建订单接口
  @POST('/v1/order/{paymentChannel}-prepay')
  Future<CreateOrderResponse> createOrder(
      @Path('paymentChannel') String paymentChannel,
      @Body() Map<String, dynamic> orderData);

  /// 新增检查订单状态接口
  @GET('/v1/order/get-order-status')
  Future<CheckOrderStatusResponse> checkOrderStatus(
      @Query('orderId') String orderId);
}
