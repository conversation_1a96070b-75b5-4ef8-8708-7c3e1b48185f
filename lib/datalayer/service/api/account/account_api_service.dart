import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/account/account.dart';
import 'package:turing_art/datalayer/domain/models/account/account_all.dart';

part 'account_api_service.g.dart';

/// 账户相关 API 服务
@RestApi()
abstract class AccountApiService {
  factory AccountApiService(Dio dio) = _AccountApiService;

  /// 获取账户信息
  @GET('/v1/account/amounts')
  Future<Account> getAccount();

  /// 获取完整账户信息（包含积分等信息）
  @GET('/v1/account/amounts-all')
  Future<AccountAll> getAllAccount();
}
