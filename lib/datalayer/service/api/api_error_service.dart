import 'package:turing_art/datalayer/domain/models/api_error/api_error.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 登录异常类
class LoginException implements Exception {
  final String message;
  final int? code;

  LoginException(this.message, {this.code});

  @override
  String toString() => code != null
      ? 'LoginException: $message (错误码: $code)'
      : 'LoginException: $message';
}

/// API错误处理服务
class ApiErrorService {
  /// 从API响应中解析错误信息
  static ApiError? parseApiError(ApiResult<dynamic> response) {
    if (response.data is Map<String, dynamic>) {
      final data = response.data as Map<String, dynamic>;
      if (data.containsKey('code') && data.containsKey('message')) {
        return ApiError.fromJson(data);
      }
    }
    return null;
  }

  /// 处理登录相关错误
  ///
  /// [response] API 响应结果
  /// 根据响应内容抛出适当的 LoginException
  static void handleLoginError(ApiResult<dynamic> response) {
    // 尝试从响应中解析API错误
    final apiError = parseApiError(response);

    if (apiError != null) {
      // 根据API错误码判断错误类型
      if (apiError.code == 10537) {
        throw LoginException('验证码错误', code: apiError.code);
      } else {
        throw LoginException(apiError.message, code: apiError.code);
      }
    } else {
      // 无法解析API错误，使用响应中的错误信息
      final errorMessage = response.error ?? '登录失败';
      final errorCode = response.statusCode != -1 ? response.statusCode : null;
      throw LoginException(errorMessage, code: errorCode);
    }
  }

  /// 处理异常，将其转换为 LoginException
  ///
  /// [e] 捕获的异常
  /// [stack] 堆栈信息
  /// [logPrefix] 日志前缀
  static void handleException(dynamic e, StackTrace stack, String logPrefix) {
    PGLog.e('$logPrefix: $e\n$stack');
    // 如果已经是 LoginException，则直接抛出
    if (e is LoginException) {
      throw e;
    }
    // 其他异常转换为 LoginException
    throw LoginException(e.toString());
  }
}
