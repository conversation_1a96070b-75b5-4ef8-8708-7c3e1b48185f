import 'package:dio/dio.dart';

/// 网络状态拦截器，目前主要用于网络异常相关
class NetworkInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 统一处理网络相关错误
    final networkError = _handleNetworkError(err);
    handler.next(networkError);
  }

  /// 处理网络相关错误，返回用户友好的错误信息
  DioException _handleNetworkError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return _createNetworkException(
          error,
          '网络连接异常，请稍后重试',
        );

      default:
        return error;
    }
  }

  /// 创建标准化的网络异常
  DioException _createNetworkException(
      DioException originalError, String message) {
    return DioException(
      requestOptions: originalError.requestOptions,
      response: originalError.response,
      type: originalError.type,
      error: NetworkException(message),
    );
  }
}

/// 自定义网络异常类
class NetworkException implements Exception {
  final String message;

  const NetworkException(this.message);

  @override
  String toString() => 'NetworkException: $message';
}
