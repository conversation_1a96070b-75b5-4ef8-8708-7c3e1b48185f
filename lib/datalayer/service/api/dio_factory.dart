import 'package:dio/dio.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/header_tool.dart';
import 'package:turing_art/datalayer/service/api/network_interceptor.dart';
import 'package:turing_art/datalayer/service/api/request_header.dart';
import 'package:turing_art/datalayer/service/api/response_interceptor.dart';
import 'package:turing_art/utils/pg_log.dart';

/// Dio 实例工厂类，用于创建和配置 Dio 实例
class DioFactory {
  /// 创建一个配置好的 Dio 实例
  ///
  /// [apiClient] API 客户端实例，用于获取基础 URL
  /// [enableLogging] 是否启用日志记录，默认为 true
  /// [enableNetworkInterceptor] 是否启用网络拦截器，默认为 true, 目前主要用于处理网络失败的情况
  static Dio createDio(
    ApiClient apiClient, {
    bool enableLogging = false,
    bool enableNetworkInterceptor = true,
  }) {
    final dio = Dio(BaseOptions(
      baseUrl: apiClient.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // 添加网络拦截器
    if (enableNetworkInterceptor) {
      dio.interceptors.add(NetworkInterceptor());
    }

    // 添加拦截器处理请求头和签名
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 获取签名请求头
        final path = options.path;
        final method = options.method.toLowerCase() == 'get'
            ? HttpMethod.get
            : HttpMethod.post;

        // 确定是查询参数还是请求体数据
        final Map<String, dynamic> dataForSign = method == HttpMethod.get
            ? options.queryParameters
            : (options.data is Map<String, dynamic> ? options.data : {});

        final headers = RequestHeader.getSignedHeaders(
          path,
          method,
          dataForSign,
          forBody: options.data != null,
        );
        PGLog.d('API headers: $headers');
        // 添加签名请求头
        options.headers.addAll(headers);

        return handler.next(options);
      },
      onError: (DioException e, handler) {
        PGLog.e('API 请求错误: ${e.message}');
        return handler.next(e);
      },
    ));

    // 添加响应拦截器，自动处理不同格式的响应
    dio.interceptors.add(ResponseInterceptor());

    // 根据配置添加日志拦截器
    if (enableLogging) {
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
      ));
    }

    return dio;
  }
}
