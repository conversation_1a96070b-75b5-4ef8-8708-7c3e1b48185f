import 'package:dio/dio.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 网络请求 response 拦截器，用于自动处理不同格式的响应
/// 使用的背景是：当前服务端返回的数据格式不统一，有些是标准的 code、message、data 形式，有
/// 一些接口返回的直接是业务数据，没有 code 和 message 字段。因此为了兼容不同的响应格式，添
/// 加了这个拦截器
///
/// 支持的响应格式：
/// 1. 标准格式：{ "code": int, "message": string, "data": any }
/// 2. 直接数据格式：直接返回业务数据
class ResponseInterceptor extends Interceptor {
  @override
  void onResponse(
      Response<dynamic> response, ResponseInterceptorHandler handler) {
    try {
      // 检查响应数据是否为 Map 类型
      if (response.data is Map<String, dynamic>) {
        final Map<String, dynamic> data = response.data;

        // 检查是否为标准的 API 响应格式（包含 code 和 message）
        if (data.containsKey('code') && data.containsKey('message')) {
          _handleStandardResponse(data, response);
        } else {
          // 非标准格式，直接返回原数据
          PGLog.d('ResponseInterceptor: 检测到非标准响应格式，直接返回原数据 ${response.data}');
        }
      } else {
        // 非 Map 类型数据，直接返回
      }

      handler.next(response);
    } catch (e) {
      PGLog.e('ResponseInterceptor error: $e');
      if (e is DioException) {
        handler.reject(e);
      } else {
        handler.reject(
          DioException(
            requestOptions: response.requestOptions,
            error: e,
            type: DioExceptionType.unknown,
          ),
        );
      }
    }
  }

  /// 处理标准响应格式：{ "code": int, "message": string, "data": any }
  void _handleStandardResponse(
    Map<String, dynamic> data,
    Response<dynamic> response,
  ) {
    final int code = data['code'] as int;
    final String message = data['message'] as String;

    // 如果请求成功，直接返回 data 部分
    if (code == 200 || code == 0) {
      if (data.containsKey('data')) {
        response.data = data['data'];
      } else {
        // 如果没有 data 字段，返回空对象
        response.data = {};
        PGLog.d('ResponseInterceptor: 请求成功但无 data 字段，返回空对象');
      }
    } else {
      // 如果请求失败，抛出 DioException
      PGLog.e('ResponseInterceptor: 请求失败 - code: $code, message: $message');
      // throw DioException(
      //   requestOptions: response.requestOptions,
      //   response: response,
      //   type: DioExceptionType.badResponse,
      //   error: ApiException(code: code, message: message),
      // );
    }
  }
}

/// API 异常类
class ApiException implements Exception {
  final int code;
  final String message;

  const ApiException({
    required this.code,
    required this.message,
  });

  @override
  String toString() => 'ApiException(code: $code, message: $message)';
}
