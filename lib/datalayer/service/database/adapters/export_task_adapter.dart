import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 导出任务数据适配器
/// 用于在数据库实体和业务模型之间进行转换
class ExportTaskAdapter {
  /// 将数据库实体转换为业务模型
  static ExportRecord fromEntity(ExportTaskEntityData entity) {
    // 解析exportFileConfig JSON来判断是否为小样导出
    bool isSample = false;
    try {
      if (entity.exportFileConfig.isNotEmpty) {
        final exportFileConfig =
            jsonDecode(entity.exportFileConfig) as Map<String, dynamic>;
        final editorType = exportFileConfig['editorType'] as int?;
        isSample = editorType == 1;
      }
    } catch (e) {
      PGLog.w('解析exportFileConfig失败: ${entity.exportFileConfig}, 错误: $e');
      // 解析失败时默认为false
      isSample = false;
    }

    return ExportRecord(
      guid: entity.guid,
      name: entity.name.isEmpty ? null : entity.name,
      showName: entity.showName.isEmpty ? null : entity.showName,
      exportPaths: entity.exportPaths.split(','),
      exportState: entity.exportState,
      createTime: entity.createTime,
      operateTime: entity.operateTime,
      successNum: entity.successNum,
      itemCount: entity.itemCount,
      errorNum: entity.errorNum,
      errorMessage: entity.errorMessage,
      isSample: isSample, // 从exportFileConfig中解析得到的isSample值
    );
  }

  /// 批量转换数据库实体列表为业务模型列表
  static List<ExportRecord> fromEntityList(
      List<ExportTaskEntityData> entities) {
    return entities.map((entity) => fromEntity(entity)).toList();
  }
}
