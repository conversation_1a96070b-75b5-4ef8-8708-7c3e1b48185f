// 定义工程表
import 'package:drift/drift.dart';

class UserEntity extends Table {
  TextColumn get id => text()(); // 1.1.0新增员工ID，唯一的
  TextColumn get username => text()(); // 用户名
  TextColumn get uid => text()(); // 用户UID，v1.1.0以后专指老板id，多个员工id可能对同一个老板id，不唯一
  TextColumn get phoneNumber => text()(); // 电话号码
  TextColumn get token => text()(); // 认证Token
  IntColumn get tokenExpiredDate => integer()(); // Token过期时间
  TextColumn get tokenEnd => text()(); // 添加 token_end 字段
  IntColumn get firstLogin =>
      integer().withDefault(const Constant(0))(); // 是否首次登录
  TextColumn get lastLoginTime =>
      text().withDefault(const Constant(''))(); // 最后登录时间
  TextColumn get regDateTime =>
      text().withDefault(const Constant(''))(); // 注册时间
  IntColumn get cc => integer().withDefault(const Constant(86))(); // 号码地区
  TextColumn get role => text().withDefault(
      const Constant('creator'))(); // 当前用户在这个门店下的角色，目前有employee和creator
  TextColumn get used =>
      text().withDefault(const Constant('0'))(); // 当前用户已使用的片量(计费)，不计费的不纳入
  IntColumn get enable =>
      integer().withDefault(const Constant(1))(); // 是否启用，1表示启用，0表示禁用
  TextColumn get lastLoginStoreId =>
      text().withDefault(const Constant(''))(); // 最后登录门店ID v1.1.0中新增

  // 1.1.0版本修改主键为员工id为主键，uid在存在子账号的情况下不唯一，只有员工id唯一
  @override
  Set<Column> get primaryKey => {id};
}
