import 'package:drift/drift.dart';

import 'workspace_entity.dart';

/// WorkspaceFileEntity - 工作区文件实体
///
/// 关于 bigint 类型处理：
/// - 使用 Int64Column 处理 BIGINT 类型（推荐）
/// - Int64Column 对应数据库的 BIGINT 类型
/// - 使用 BigInt 类型存储超大数值
/// - 示例：Int64Column get field => int64().nullable()();
class WorkspaceFileEntity extends Table {
  TextColumn get fileId => text()();
  BoolColumn get edited => boolean()();
  IntColumn get stars => integer()();
  BoolColumn get exported => boolean()();
  TextColumn get orgPath => text()();
  IntColumn get exportTime => integer()();
  TextColumn get format => text()();
  BoolColumn get broken => boolean()();
  IntColumn get lastEditTime => integer()();
  IntColumn get createTime => integer()();
  IntColumn get size => integer()();
  IntColumn get width => integer()();
  IntColumn get height => integer()();
  IntColumn get orientation => integer()();
  IntColumn get iccType => integer()();
  BoolColumn get isRaw => boolean()();
  TextColumn get rawPath => text()();
  BoolColumn get converted => boolean()();
  // 外键关联到 WorkspaceEntity
  TextColumn get workspaceId =>
      text().references(WorkspaceEntity, #workspaceId)();

  // v2新增字段
  TextColumn get fileName =>
      text().nullable().withDefault(const Constant(''))();
  BoolColumn get iconized =>
      boolean().nullable().withDefault(const Constant(false))();
  BoolColumn get midIconized =>
      boolean().nullable().withDefault(const Constant(false))();
  IntColumn get captureTime =>
      integer().nullable().withDefault(const Constant(0))();
  BoolColumn get isOverSize =>
      boolean().nullable().withDefault(const Constant(false))();
  IntColumn get faceCount =>
      integer().nullable().withDefault(const Constant(0))();
  TextColumn get binFormat =>
      text().nullable().withDefault(const Constant(''))();
  BoolColumn get rawAutoExpose =>
      boolean().nullable().withDefault(const Constant(false))();
  IntColumn get rawAutoAdjustType =>
      integer().nullable().withDefault(const Constant(0))();
  BoolColumn get isDeleted =>
      boolean().nullable().withDefault(const Constant(false))();

  // v3新增字段
  IntColumn get n8Exported =>
      integer().nullable().withDefault(const Constant(0))();
  Int64Column get n8ExportTime =>
      int64().nullable().withDefault(Constant(BigInt.from(0)))();
  IntColumn get fileExportedState =>
      integer().nullable().withDefault(const Constant(0))();

  // 添加唯一索引 - fileId 作为业务主键应该是唯一的
  @override
  Set<Column> get primaryKey => {fileId};
}
