import 'package:drift/drift.dart';

// 定义导出任务表
class ExportTaskEntity extends Table {
  TextColumn get guid => text()(); // 主键
  TextColumn get userId => text()(); // 用户ID
  TextColumn get cachePath => text()(); // 缓存路径
  TextColumn get exportFileConfig => text()(); // ExportFileConfig json
  TextColumn get name => text()(); // 任务名称
  TextColumn get showName => text()(); // 显示名称
  TextColumn get exportPaths => text()(); // 多个地址连成的字符串，中间用两个$$分割
  IntColumn get exportState =>
      integer().withDefault(const Constant(0))(); // ExportState
  TextColumn get operateTime => text()(); // 操作时间
  IntColumn get createTime => integer()(); // 创建时间
  IntColumn get itemCount => integer().withDefault(const Constant(0))(); // 项目总数
  IntColumn get successNum =>
      integer().withDefault(const Constant(0))(); // 成功数量
  TextColumn get errorMessage =>
      text().nullable().withDefault(const Constant(''))(); // 错误信息
  IntColumn get errorNum =>
      integer().withDefault(const Constant(0))(); // ExportErrorType
  // 添加主键
  @override
  Set<Column> get primaryKey => {guid};
}
