import 'package:drift/drift.dart';

import 'project_entity.dart';

class WorkspaceEntity extends Table {
  TextColumn get workspaceId => text()(); // 对应 project 的 UUID
  TextColumn get workspaceName => text()();
  TextColumn get currentFileId => text()();
  IntColumn get createTime => integer()();
  IntColumn get lastEditTime => integer()();

  // 外键关联到 ProjectEntity
  TextColumn get projectId => text().references(ProjectEntity, #projectId)();

  // 添加唯一索引 - workspaceId 作为业务主键应该是唯一的
  @override
  Set<Column> get primaryKey => {workspaceId};

  // v2新增字段
  TextColumn get filterValue =>
      text().nullable().withDefault(const Constant('{}'))(); // 过滤值Json字符串

  TextColumn get sortValue =>
      text().nullable().withDefault(const Constant('{}'))(); // 排序值Json字符串
}
