import 'package:drift/drift.dart';

// 定义工程表
class ProjectEntity extends Table {
  TextColumn get name => text()(); // 工程名称
  TextColumn get projectId => text()(); // 工程 UUID
  TextColumn get version => text()(); // 工程版本
  TextColumn get author => text()(); // 工程作者
  TextColumn get coverImages => text().nullable()(); // 工程封面图片，存储为逗号分隔的字符串
  TextColumn get description => text().nullable()(); // 工程描述
  DateTimeColumn get createdDate => dateTime().nullable()(); // 创建日期
  DateTimeColumn get updateDate => dateTime().nullable()(); // 更新日期
  IntColumn get projectType =>
      integer().withDefault(const Constant(0))(); // 工程类型 0表示精修工程 1表示aig工程

  // ExportConfig 相关字段
  TextColumn get outputFolder =>
      text().withDefault(const Constant(''))(); // 导出文件夹路径
  IntColumn get exportFileType =>
      integer().withDefault(const Constant(0))(); // 导出文件类型
  IntColumn get quality => integer().withDefault(const Constant(100))(); // 导出质量
  BoolColumn get isReplace =>
      boolean().withDefault(const Constant(false))(); // 是否覆盖同名文件
  BoolColumn get transferSRGB =>
      boolean().withDefault(const Constant(false))(); // 是否转换为sRGB颜色空间

  IntColumn get workspaceVersion =>
      integer().nullable().withDefault(const Constant(1))(); // 默认工程版本号

  BoolColumn get isDelete =>
      boolean().withDefault(const Constant(false))(); // 是否已删除

  // 添加唯一索引
  @override
  Set<Column> get primaryKey => {projectId};
}
