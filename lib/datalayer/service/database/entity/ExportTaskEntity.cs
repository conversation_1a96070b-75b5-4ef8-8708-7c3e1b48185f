namespace SugoiRetouch.DataLayer.Entities
{
	[Table("export_task_entity")]
	public class ExportTaskEntity
	{
		// 按照 ExportTaskData 对象的结构创建
		// 添加主键
		[PrimaryKey]
		public string guid { get; set; }

		public string user_id { get; set; }

		public string cache_path { get; set; }

		// ExportFileConfig json
		public string export_file_config { get; set; }

		public string name { get; set; }

		public string show_name { get; set; }

		// 多个地址连成的字符串，中间用两个$$分割
		public string export_paths { get; set; }

		// ExportState
		public int export_state { get; set; }

		public string operate_time { get; set; }

		public long create_time { get; set; }

		public int item_count { get; set; }

		public int success_num { get; set; }

		public string error_message { get; set; }

		// ExportErrorType
		public int error_num { get; set; }
	}
}