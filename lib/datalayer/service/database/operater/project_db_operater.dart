import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/executor/database_executor.dart';
import 'package:turing_art/datalayer/service/database/operation/project_operations.dart';
import 'package:turing_art/datalayer/service/database/operation/workspace_file_operations.dart';

import 'db_operater.dart';

extension ProjectDbOperater on DbOperater {
  // Project related operations
  Future<List<ProjectEntityData>> getAllProjects({
    bool isDelete = false,
  }) async {
    return DatabaseExecutor.execute<List<ProjectEntityData>>(
      GetAllProjectsOperation().operation,
      {'isDelete': isDelete},
    );
  }

  Future<List<ProjectEntityData>> getUserAllProjects(
    String userId, {
    bool isDelete = false,
  }) async {
    return DatabaseExecutor.execute<List<ProjectEntityData>>(
      GetUserAllProjectsOperation().operation,
      {'userId': userId, 'isDelete': isDelete},
    );
  }

  Future<List<ProjectEntityData>> getUserAllProjectsByType(
      String userId, int projectType,
      {bool isDelete = false}) async {
    return DatabaseExecutor.execute<List<ProjectEntityData>>(
      GetUserAllProjectsByTypeOperation().operation,
      {'userId': userId, 'projectType': projectType, 'isDelete': isDelete},
    );
  }

  Future<ProjectEntityData?> getProjectById(String projectId) async {
    return DatabaseExecutor.execute<ProjectEntityData?>(
      GetProjectByIdOperation().operation,
      {'projectId': projectId},
    );
  }

  /// 根据 projectId 查询工程属于哪个用户
  /// [projectId] 工程ID
  /// 返回用户ID，如果工程不存在则返回 null
  Future<String?> getProjectAuthorById(String projectId) async {
    return DatabaseExecutor.execute<String?>(
      GetProjectAuthorByIdOperation().operation,
      {'projectId': projectId},
    );
  }

  Future<void> insertProject(ProjectEntityCompanion project) async {
    return DatabaseExecutor.execute<void>(
      InsertProjectOperation().operation,
      {'project': project},
    );
  }

  Future<void> updateProject(ProjectEntityCompanion project) async {
    return DatabaseExecutor.execute<void>(
      UpdateProjectOperation().operation,
      {'project': project},
    );
  }

  Future<void> deleteProject(
    String projectId,
  ) async {
    await DatabaseExecutor.execute<void>(
      DeleteProjectAndWorkspaceAndFilesOperation().operation,
      {'projectId': projectId},
    );
  }

  Future<void> deleteProjects(
    List<String> projectIds,
  ) async {
    await DatabaseExecutor.execute<void>(
      DeleteProjectsAndWorkspaceAndFilesOperation().operation,
      {'projectIds': projectIds},
    );
  }

  Future<void> insertProjectWithWorkspace(
    ProjectEntityCompanion project,
    WorkspaceEntityCompanion workspace,
    List<WorkspaceFileEntityCompanion> files,
  ) async {
    await DatabaseExecutor.execute<void>(
      InsertProjectWithWorkspaceOperation().operation,
      {'project': project, 'workspace': workspace, 'files': files},
    );
  }

  Future<void> updateProjectWithWorkspace(
    ProjectEntityCompanion project,
    WorkspaceEntityCompanion workspace,
    List<WorkspaceFileEntityCompanion> files,
  ) async {
    await DatabaseExecutor.execute<void>(
      UpdateProjectWithWorkspaceOperation().operation,
      {'project': project, 'workspace': workspace, 'files': files},
    );
  }

  Future<List<WorkspaceFileEntityData>> getFilesByWorkspaceId(
    String projectId, {
    int sortField = 0,
    int sortOrder = 0,
  }) async {
    return DatabaseExecutor.execute<List<WorkspaceFileEntityData>>(
      GetFilesByWorkspaceIdOperation().operation,
      {
        'workspaceId': projectId,
        'sortField': sortField,
        'sortOrder': sortOrder
      },
    );
  }

  Future<List<WorkspaceFileEntityData>> getFilesByWorkspaceIdWithDefalutSort(
    String projectId,
  ) async {
    return DatabaseExecutor.execute<List<WorkspaceFileEntityData>>(
      GetFilesByWorkspaceIdWithDefaultSortOperation().operation,
      {
        'workspaceId': projectId,
      },
    );
  }
}
