import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/executor/database_executor.dart';
import 'package:turing_art/datalayer/service/database/operation/file_operation_history_operations.dart';

import 'db_operater.dart';

extension FileOperationHistoryDbOperater on DbOperater {
  // ==================== 查询操作 ====================

  /// 获取所有文件操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getAllFileOperationHistory() async {
    return DatabaseExecutor.execute<List<FileOperationHistoryEntityData>>(
      GetAllFileOperationHistoryOperation().operation,
      {},
    );
  }

  /// 根据ID获取单个文件操作历史记录
  Future<FileOperationHistoryEntityData?> getFileOperationHistoryById(
      int id) async {
    return DatabaseExecutor.execute<FileOperationHistoryEntityData?>(
      GetFileOperationHistoryByIdOperation().operation,
      {'id': id},
    );
  }

  /// 根据文件ID获取该文件的所有操作历史记录
  Future<List<FileOperationHistoryEntityData>> getFileOperationHistoryByFileId(
      String fileId) async {
    return DatabaseExecutor.execute<List<FileOperationHistoryEntityData>>(
      GetFileOperationHistoryByFileIdOperation().operation,
      {'fileId': fileId},
    );
  }

  /// 根据文件ID列表批量获取文件操作历史记录
  Future<List<FileOperationHistoryEntityData>> getFileOperationHistoryByFileIds(
      List<String> fileIds) async {
    if (fileIds.isEmpty) {
      return [];
    }
    return DatabaseExecutor.execute<List<FileOperationHistoryEntityData>>(
      GetFileOperationHistoryByFileIdsOperation().operation,
      {'fileIds': fileIds},
    );
  }

  /// 根据时间范围获取文件操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryByTimeRange(
    int startTime,
    int endTime,
  ) async {
    return DatabaseExecutor.execute<List<FileOperationHistoryEntityData>>(
      GetFileOperationHistoryByTimeRangeOperation().operation,
      {'startTime': startTime, 'endTime': endTime},
    );
  }

  /// 根据文件ID和时间范围获取该文件的操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryByFileIdAndTimeRange(
    String fileId,
    int startTime,
    int endTime,
  ) async {
    return DatabaseExecutor.execute<List<FileOperationHistoryEntityData>>(
      GetFileOperationHistoryByFileIdAndTimeRangeOperation().operation,
      {'fileId': fileId, 'startTime': startTime, 'endTime': endTime},
    );
  }

  // ==================== 插入操作 ====================

  /// 插入单个文件操作历史记录
  Future<int> insertFileOperationHistory(
      FileOperationHistoryEntityCompanion entity) async {
    return DatabaseExecutor.execute<int>(
      InsertFileOperationHistoryOperation().operation,
      {'entity': entity},
    );
  }

  /// 批量插入文件操作历史记录
  Future<void> insertFileOperationHistoryBatch(
      List<FileOperationHistoryEntityCompanion> entities) async {
    if (entities.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      InsertFileOperationHistoryBatchOperation().operation,
      {'entities': entities},
    );
  }

  // ==================== 更新操作 ====================

  /// 更新文件操作历史记录
  Future<bool> updateFileOperationHistory(
      FileOperationHistoryEntityCompanion entity) async {
    return DatabaseExecutor.execute<bool>(
      UpdateFileOperationHistoryOperation().operation,
      {'entity': entity},
    );
  }

  /// 批量更新文件操作历史记录
  Future<void> updateFileOperationHistoryBatch(
      List<FileOperationHistoryEntityCompanion> entities) async {
    if (entities.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      UpdateFileOperationHistoryBatchOperation().operation,
      {'entities': entities},
    );
  }

  // ==================== 删除操作 ====================

  /// 根据ID删除文件操作历史记录
  Future<int> deleteFileOperationHistoryById(int id) async {
    return DatabaseExecutor.execute<int>(
      DeleteFileOperationHistoryByIdOperation().operation,
      {'id': id},
    );
  }

  /// 根据文件ID删除该文件的所有操作历史记录
  Future<int> deleteFileOperationHistoryByFileId(String fileId) async {
    return DatabaseExecutor.execute<int>(
      DeleteFileOperationHistoryByFileIdOperation().operation,
      {'fileId': fileId},
    );
  }

  /// 根据文件ID列表批量删除文件操作历史记录
  Future<void> deleteFileOperationHistoryByFileIds(List<String> fileIds) async {
    if (fileIds.isEmpty) {
      return;
    }
    return DatabaseExecutor.execute<void>(
      DeleteFileOperationHistoryByFileIdsOperation().operation,
      {'fileIds': fileIds},
    );
  }

  /// 根据时间范围删除文件操作历史记录
  Future<int> deleteFileOperationHistoryByTimeRange(
      int startTime, int endTime) async {
    return DatabaseExecutor.execute<int>(
      DeleteFileOperationHistoryByTimeRangeOperation().operation,
      {'startTime': startTime, 'endTime': endTime},
    );
  }

  /// 根据文件ID和时间范围删除该文件的操作历史记录
  Future<int> deleteFileOperationHistoryByFileIdAndTimeRange(
    String fileId,
    int startTime,
    int endTime,
  ) async {
    return DatabaseExecutor.execute<int>(
      DeleteFileOperationHistoryByFileIdAndTimeRangeOperation().operation,
      {'fileId': fileId, 'startTime': startTime, 'endTime': endTime},
    );
  }

  /// 删除所有文件操作历史记录
  Future<int> deleteAllFileOperationHistory() async {
    return DatabaseExecutor.execute<int>(
      DeleteAllFileOperationHistoryOperation().operation,
      {},
    );
  }

  // ==================== 统计操作 ====================

  /// 获取文件操作历史记录总数
  Future<int> getFileOperationHistoryCount() async {
    return DatabaseExecutor.execute<int>(
      GetFileOperationHistoryCountOperation().operation,
      {},
    );
  }

  /// 根据文件ID获取该文件的操作历史记录总数
  Future<int> getFileOperationHistoryCountByFileId(String fileId) async {
    return DatabaseExecutor.execute<int>(
      GetFileOperationHistoryCountByFileIdOperation().operation,
      {'fileId': fileId},
    );
  }

  // ==================== 分页操作 ====================

  /// 分页获取文件操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryWithPagination(
    int offset,
    int limit,
  ) async {
    return DatabaseExecutor.execute<List<FileOperationHistoryEntityData>>(
      GetFileOperationHistoryWithPaginationOperation().operation,
      {'offset': offset, 'limit': limit},
    );
  }

  /// 根据文件ID分页获取该文件的操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryByFileIdWithPagination(
    String fileId,
    int offset,
    int limit,
  ) async {
    return DatabaseExecutor.execute<List<FileOperationHistoryEntityData>>(
      GetFileOperationHistoryByFileIdWithPaginationOperation().operation,
      {'fileId': fileId, 'offset': offset, 'limit': limit},
    );
  }

  // ==================== 便捷方法 ====================

  /// 记录文件操作历史（便捷方法）
  Future<int> recordFileOperation({
    required String fileId,
    required String operation,
    String? description,
    Map<String, dynamic>? extraData,
  }) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final data = {
      'operation': operation,
      'description': description,
      ...?extraData,
    };

    final entity = FileOperationHistoryEntityCompanion.insert(
      fileId: fileId,
      createTime: now,
      extraData: Value(jsonEncode(data)),
    );

    return await insertFileOperationHistory(entity);
  }

  /// 批量记录文件操作历史（便捷方法）
  Future<void> recordFileOperationsBatch(
      List<Map<String, dynamic>> operations) async {
    if (operations.isEmpty) {
      return;
    }

    final now = DateTime.now().millisecondsSinceEpoch;
    final entities = operations.map((op) {
      final data = {
        'operation': op['operation'],
        'description': op['description'],
        ...?op['extraData'],
      };

      return FileOperationHistoryEntityCompanion.insert(
        fileId: op['fileId'],
        createTime: op['createTime'] ?? now,
        extraData: Value(jsonEncode(data)),
      );
    }).toList();

    await insertFileOperationHistoryBatch(entities);
  }

  /// 清理指定文件的所有操作历史记录
  Future<int> clearFileOperationHistory(String fileId) async {
    return await deleteFileOperationHistoryByFileId(fileId);
  }

  /// 清理指定时间范围之前的所有操作历史记录
  Future<int> clearFileOperationHistoryBefore(int timestamp) async {
    return await deleteFileOperationHistoryByTimeRange(0, timestamp);
  }
}

/*
使用示例：

// 1. 记录单个文件操作
final dbOperater = DbOperater();
final recordId = await dbOperater.recordFileOperation(
  fileId: 'file_123',
  operation: 'edit',
  description: '用户编辑了图片',
  extraData: {
    'tool': 'crop',
    'parameters': {'x': 100, 'y': 200, 'width': 300, 'height': 400}
  },
);

// 2. 批量记录文件操作
await dbOperater.recordFileOperationsBatch([
  {
    'fileId': 'file_123',
    'operation': 'crop',
    'description': '裁剪图片',
    'extraData': {'x': 100, 'y': 200, 'width': 300, 'height': 400}
  },
  {
    'fileId': 'file_456',
    'operation': 'filter',
    'description': '应用滤镜',
    'extraData': {'filterType': 'vintage', 'intensity': 0.8}
  },
]);

// 3. 查询某个文件的所有操作历史
final history = await dbOperater.getFileOperationHistoryByFileId('file_123');

// 4. 分页查询某个文件的操作历史
final page1 = await dbOperater.getFileOperationHistoryByFileIdWithPagination('file_123', 0, 10);

// 5. 查询时间范围内的操作历史
final startTime = DateTime.now().subtract(Duration(days: 7)).millisecondsSinceEpoch;
final endTime = DateTime.now().millisecondsSinceEpoch;
final recentHistory = await dbOperater.getFileOperationHistoryByTimeRange(startTime, endTime);

// 6. 删除某个文件的所有操作历史
final deletedCount = await dbOperater.clearFileOperationHistory('file_123');

// 7. 监听某个文件的操作历史变化
final stream = await dbOperater.watchFileOperationHistoryByFileId('file_123');
stream.listen((history) {
  print('文件操作历史已更新: ${history.length} 条记录');
});

// 8. 获取操作历史统计信息
final totalCount = await dbOperater.getFileOperationHistoryCount();
final fileHistoryCount = await dbOperater.getFileOperationHistoryCountByFileId('file_123');

// 9. 清理旧的操作历史记录
final oneWeekAgo = DateTime.now().subtract(Duration(days: 7)).millisecondsSinceEpoch;
final cleanedCount = await dbOperater.clearFileOperationHistoryBefore(oneWeekAgo);
*/
