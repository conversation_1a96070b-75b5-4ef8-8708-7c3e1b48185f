import 'package:turing_art/datalayer/service/database/dao/sort_option.dart';
import 'package:turing_art/datalayer/service/database/dao/workspace_dao.dart';
import 'package:turing_art/datalayer/service/database/dao/workspace_file_dao.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

import 'database_operation.dart';

class GetAllWorkspacesOperation
    extends DatabaseOperation<List<WorkspaceEntityData>> {
  @override
  String get operation => 'getAllWorkspaces';

  @override
  Future<List<WorkspaceEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    // 从参数中获取排序字段和顺序，如果没有提供则使用默认值
    final sortFieldValue = params['sortField'] as int? ?? 0;
    final sortOrderValue = params['sortOrder'] as int? ?? 0;

    final sortField = SortField.fromValue(sortFieldValue);
    final sortOrder = SortOrder.fromValue(sortOrderValue);

    return db.getAllWorkspaces(
      sortField: sortField,
      sortOrder: sortOrder,
    );
  }
}

class GetWorkspaceByIdOperation
    extends DatabaseOperation<WorkspaceEntityData?> {
  @override
  String get operation => 'getWorkspaceById';

  @override
  Future<WorkspaceEntityData?> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getWorkspaceById(params['workspaceId']);
  }
}

class GetWorkspaceByIdsOperation
    extends DatabaseOperation<List<WorkspaceEntityData>> {
  @override
  String get operation => 'getWorkspaceByIds';

  @override
  Future<List<WorkspaceEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getWorkspaceByIds(params['workspaceIds']);
  }
}

class InsertWorkspaceOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'insertWorkspace';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.insertWorkspace(params['workspace']);
  }
}

class UpdateWorkspaceOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateWorkspace';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.updateWorkspace(params['workspace']);
  }
}

class DeleteWorkspaceOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteWorkspace';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteWorkspace(params['workspaceId']);
  }
}

class DeleteWorkspacesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteWorkspaces';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteWorkspaces(params['workspaceIds']);
  }
}

class UpdateWorkspaceAndFilesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateWorkspaceAndFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) async {
    await db.transaction(() async {
      await db.updateWorkspace(params['workspace']);
      if ((params['files'] as List).isNotEmpty) {
        await db.insertOrUpdateWorkspaceFiles(params['files']);
      }
    });
  }
}

class DeleteWorkspaceAndFilesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteWorkspaceAndFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) async {
    await db.transaction(() async {
      await db.deleteWorkspaceFiles(params['workspaceId'], params['fileIds']);
      await db.updateWorkspace(params['workspace']);
    });
  }
}
