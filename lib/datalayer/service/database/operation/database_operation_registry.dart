// 操作注册表
// ignore_for_file: strict_raw_type

import 'package:turing_art/datalayer/service/database/operation/export_task_operations.dart';
import 'package:turing_art/datalayer/service/database/operation/file_operation_history_operations.dart';
import 'package:turing_art/datalayer/service/database/operation/project_operations.dart';
import 'package:turing_art/datalayer/service/database/operation/workspace_file_operations.dart';
import 'package:turing_art/datalayer/service/database/operation/workspace_operations.dart';

import 'database_operation.dart';

class DatabaseOperationRegistry {
  static final Map<String, DatabaseOperation> _operations = {};

  static void register(DatabaseOperation operation) {
    _operations[operation.operation] = operation;
  }

  static DatabaseOperation? get(String operation) {
    return _operations[operation];
  }

  static void registerAll() {
    register(GetAllProjectsOperation());
    register(GetUserAllProjectsOperation());
    register(GetUserAllProjectsByTypeOperation());
    register(GetProjectByIdOperation());
    register(GetProjectAuthorByIdOperation());
    register(InsertProjectOperation());
    register(UpdateProjectOperation());
    register(InsertProjectWithWorkspaceOperation());
    register(UpdateProjectWithWorkspaceOperation());
    register(DeleteProjectAndWorkspaceAndFilesOperation());
    register(DeleteProjectsAndWorkspaceAndFilesOperation());
    register(DeleteProjectOperation());
    register(GetAllWorkspacesOperation());
    register(GetWorkspaceByIdOperation());
    register(GetWorkspaceByIdsOperation());
    register(InsertWorkspaceOperation());
    register(UpdateWorkspaceOperation());
    register(DeleteWorkspaceOperation());
    register(DeleteWorkspacesOperation());
    register(UpdateWorkspaceAndFilesOperation());
    register(DeleteWorkspaceAndFilesOperation());
    register(GetFilesByWorkspaceIdOperation());
    register(GetFilesByWorkspaceIdsOperation());
    register(GetFileByWorkspaceIdAndFileIdOperation());
    register(GetFileByWorkspaceIdAndOrgPathOperation());
    register(GetFilesByWorkspaceIdAndOrgPathsOperation());
    register(InsertWorkspaceFileOperation());
    register(InsertWorkspaceFilesOperation());
    register(UpdateWorkspaceFileOperation());
    register(UpdateWorkspaceFilesOperation());
    register(DeleteWorkspaceFileOperation());
    register(DeleteWorkspaceFilesOperation());
    register(DeleteAllWorkspaceFilesOperation());
    register(DeleteAllWorkspacesFilesOperation());
    register(InsertFileOperationHistoryOperation());
    register(InsertFileOperationHistoryBatchOperation());
    register(UpdateFileOperationHistoryOperation());
    register(UpdateFileOperationHistoryBatchOperation());
    register(DeleteFileOperationHistoryByIdOperation());
    register(DeleteFileOperationHistoryByFileIdOperation());
    register(DeleteFileOperationHistoryByFileIdsOperation());
    register(DeleteFileOperationHistoryByTimeRangeOperation());
    register(DeleteFileOperationHistoryByFileIdAndTimeRangeOperation());
    register(DeleteAllFileOperationHistoryOperation());
    register(GetAllFileOperationHistoryOperation());
    register(GetFileOperationHistoryByIdOperation());
    register(GetFileOperationHistoryByFileIdsOperation());
    register(GetFileOperationHistoryByTimeRangeOperation());
    register(GetFileOperationHistoryByFileIdAndTimeRangeOperation());
    register(GetFileOperationHistoryCountOperation());
    register(GetFileOperationHistoryCountByFileIdOperation());
    register(GetFileOperationHistoryWithPaginationOperation());
    register(GetFileOperationHistoryByFileIdWithPaginationOperation());
    register(GetFileOperationHistoryByFileIdOperation());
    register(GetUserAllExportTasksOperation());
    register(UpdateExportTaskOperation());
    register(GetExportTaskOperation());
    register(GetExportTasksOperation());
    register(DeleteExportTaskOperation());
    register(GetExportFilesOperation());
    register(GetAllFilesOperation());
    register(GetFilesByWorkspaceIdWithDefaultSortOperation());
  }
}
