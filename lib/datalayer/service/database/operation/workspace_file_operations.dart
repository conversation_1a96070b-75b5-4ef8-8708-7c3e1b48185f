import 'package:turing_art/datalayer/service/database/dao/sort_option.dart';
import 'package:turing_art/datalayer/service/database/dao/workspace_file_dao.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

import 'database_operation.dart';

class GetFilesByWorkspaceIdOperation
    extends DatabaseOperation<List<WorkspaceFileEntityData>> {
  @override
  String get operation => 'getFilesByWorkspaceId';

  @override
  Future<List<WorkspaceFileEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
// 从参数中获取排序字段和顺序，如果没有提供则使用默认值
    final sortFieldValue = params['sortField'] as int? ?? 0;
    final sortOrderValue = params['sortOrder'] as int? ?? 0;

    final sortField = SortField.fromValue(sortFieldValue);
    final sortOrder = SortOrder.fromValue(sortOrderValue);

    return db.getFilesByWorkspaceId(
      params['workspaceId'],
      sortField: sortField,
      sortOrder: sortOrder,
    );
  }
}

class GetFilesByWorkspaceIdWithDefaultSortOperation
    extends DatabaseOperation<List<WorkspaceFileEntityData>> {
  @override
  String get operation => 'getFilesByWorkspaceId';

  @override
  Future<List<WorkspaceFileEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFilesByWorkspaceWithDefaultSort(
      params['workspaceId'],
    );
  }
}

class GetAllFilesOperation
    extends DatabaseOperation<List<WorkspaceFileEntityData>> {
  @override
  String get operation => 'getAllFiles';

  @override
  Future<List<WorkspaceFileEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    // 从参数中获取排序字段和顺序，如果没有提供则使用默认值
    final sortFieldValue = params['sortField'] as int? ?? 0;
    final sortOrderValue = params['sortOrder'] as int? ?? 0;

    final sortField = SortField.fromValue(sortFieldValue);
    final sortOrder = SortOrder.fromValue(sortOrderValue);

    return db.getAllFiles(
      sortField: sortField,
      sortOrder: sortOrder,
    );
  }
}

class GetFileByWorkspaceIdAndFileIdOperation
    extends DatabaseOperation<WorkspaceFileEntityData?> {
  @override
  String get operation => 'getFileByWorkspaceIdAndFileId';

  @override
  Future<WorkspaceFileEntityData?> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getWorkspaceFile(params['workspaceId'], params['fileId']);
  }
}

class GetFilesByWorkspaceIdsOperation
    extends DatabaseOperation<List<WorkspaceFileEntityData>> {
  @override
  String get operation => 'getFilesByWorkspaceIds';

  @override
  Future<List<WorkspaceFileEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    // 从参数中获取排序字段和顺序，如果没有提供则使用默认值
    final sortFieldValue = params['sortField'] as int? ?? 0;
    final sortOrderValue = params['sortOrder'] as int? ?? 0;

    final sortField = SortField.fromValue(sortFieldValue);
    final sortOrder = SortOrder.fromValue(sortOrderValue);

    return db.getFilesByWorkspaceIds(
      params['workspaceIds'],
      sortField: sortField,
      sortOrder: sortOrder,
    );
  }
}

class InsertWorkspaceFileOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'insertWorkspaceFile';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.insertWorkspaceFile(params['workspaceFile']);
  }
}

class InsertWorkspaceFilesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'insertWorkspaceFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.insertOrUpdateWorkspaceFiles(params['workspaceFiles']);
  }
}

class UpdateWorkspaceFileOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateWorkspaceFile';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.updateWorkspaceFile(params['workspaceFile']);
  }
}

class UpdateWorkspaceFilesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateWorkspaceFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.insertOrUpdateWorkspaceFiles(params['workspaceFiles']);
  }
}

class DeleteWorkspaceFileOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteWorkspaceFile';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteWorkspaceFile(params['workspaceId'], params['fileId']);
  }
}

class DeleteWorkspaceFilesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteWorkspaceFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteWorkspaceFiles(params['workspaceId'], params['fileIds']);
  }
}

class DeleteAllWorkspaceFilesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteAllWorkspaceFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteAllWorkspaceFiles(params['workspaceId']);
  }
}

class DeleteAllWorkspacesFilesOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteAllWorkspacesFiles';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteAllWorkspacesFiles(params['workspaceIds']);
  }
}

class GetFileByWorkspaceIdAndOrgPathOperation
    extends DatabaseOperation<WorkspaceFileEntityData?> {
  @override
  String get operation => 'getFileByWorkspaceIdAndOrgPath';

  @override
  Future<WorkspaceFileEntityData?> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileByWorkspaceIdAndOrgPath(
      params['workspaceId'],
      params['orgPath'],
    );
  }
}

class GetFilesByWorkspaceIdAndOrgPathsOperation
    extends DatabaseOperation<List<WorkspaceFileEntityData>> {
  @override
  String get operation => 'getFilesByWorkspaceIdAndOrgPaths';

  @override
  Future<List<WorkspaceFileEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFilesByWorkspaceIdAndOrgPaths(
      params['workspaceId'],
      params['orgPaths'],
    );
  }
}
