import 'package:turing_art/datalayer/service/database/dao/file_operation_history_dao.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

import 'database_operation.dart';

// 查询操作
class GetAllFileOperationHistoryOperation
    extends DatabaseOperation<List<FileOperationHistoryEntityData>> {
  @override
  String get operation => 'getAllFileOperationHistory';

  @override
  Future<List<FileOperationHistoryEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getAllFileOperationHistory();
  }
}

class GetFileOperationHistoryByIdOperation
    extends DatabaseOperation<FileOperationHistoryEntityData?> {
  @override
  String get operation => 'getFileOperationHistoryById';

  @override
  Future<FileOperationHistoryEntityData?> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryById(params['id']);
  }
}

class GetFileOperationHistoryByFileIdOperation
    extends DatabaseOperation<List<FileOperationHistoryEntityData>> {
  @override
  String get operation => 'getFileOperationHistoryByFileId';

  @override
  Future<List<FileOperationHistoryEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryByFileId(params['fileId']);
  }
}

class GetFileOperationHistoryByFileIdsOperation
    extends DatabaseOperation<List<FileOperationHistoryEntityData>> {
  @override
  String get operation => 'getFileOperationHistoryByFileIds';

  @override
  Future<List<FileOperationHistoryEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryByFileIds(params['fileIds']);
  }
}

class GetFileOperationHistoryByTimeRangeOperation
    extends DatabaseOperation<List<FileOperationHistoryEntityData>> {
  @override
  String get operation => 'getFileOperationHistoryByTimeRange';

  @override
  Future<List<FileOperationHistoryEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryByTimeRange(
      params['startTime'],
      params['endTime'],
    );
  }
}

class GetFileOperationHistoryByFileIdAndTimeRangeOperation
    extends DatabaseOperation<List<FileOperationHistoryEntityData>> {
  @override
  String get operation => 'getFileOperationHistoryByFileIdAndTimeRange';

  @override
  Future<List<FileOperationHistoryEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryByFileIdAndTimeRange(
      params['fileId'],
      params['startTime'],
      params['endTime'],
    );
  }
}

// 插入操作
class InsertFileOperationHistoryOperation extends DatabaseOperation<int> {
  @override
  String get operation => 'insertFileOperationHistory';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.insertFileOperationHistory(params['entity']);
  }
}

class InsertFileOperationHistoryBatchOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'insertFileOperationHistoryBatch';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.insertFileOperationHistoryBatch(params['entities']);
  }
}

// 更新操作
class UpdateFileOperationHistoryOperation extends DatabaseOperation<bool> {
  @override
  String get operation => 'updateFileOperationHistory';

  @override
  Future<bool> execute(DataBase db, Map<String, dynamic> params) {
    return db.updateFileOperationHistory(params['entity']);
  }
}

class UpdateFileOperationHistoryBatchOperation extends DatabaseOperation<void> {
  @override
  String get operation => 'updateFileOperationHistoryBatch';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.updateFileOperationHistoryBatch(params['entities']);
  }
}

// 删除操作
class DeleteFileOperationHistoryByIdOperation extends DatabaseOperation<int> {
  @override
  String get operation => 'deleteFileOperationHistoryById';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteFileOperationHistoryById(params['id']);
  }
}

class DeleteFileOperationHistoryByFileIdOperation
    extends DatabaseOperation<int> {
  @override
  String get operation => 'deleteFileOperationHistoryByFileId';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteFileOperationHistoryByFileId(params['fileId']);
  }
}

class DeleteFileOperationHistoryByFileIdsOperation
    extends DatabaseOperation<void> {
  @override
  String get operation => 'deleteFileOperationHistoryByFileIds';

  @override
  Future<void> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteFileOperationHistoryByFileIds(params['fileIds']);
  }
}

class DeleteFileOperationHistoryByTimeRangeOperation
    extends DatabaseOperation<int> {
  @override
  String get operation => 'deleteFileOperationHistoryByTimeRange';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteFileOperationHistoryByTimeRange(
      params['startTime'],
      params['endTime'],
    );
  }
}

class DeleteFileOperationHistoryByFileIdAndTimeRangeOperation
    extends DatabaseOperation<int> {
  @override
  String get operation => 'deleteFileOperationHistoryByFileIdAndTimeRange';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteFileOperationHistoryByFileIdAndTimeRange(
      params['fileId'],
      params['startTime'],
      params['endTime'],
    );
  }
}

class DeleteAllFileOperationHistoryOperation extends DatabaseOperation<int> {
  @override
  String get operation => 'deleteAllFileOperationHistory';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.deleteAllFileOperationHistory();
  }
}

// 统计操作
class GetFileOperationHistoryCountOperation extends DatabaseOperation<int> {
  @override
  String get operation => 'getFileOperationHistoryCount';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryCount();
  }
}

class GetFileOperationHistoryCountByFileIdOperation
    extends DatabaseOperation<int> {
  @override
  String get operation => 'getFileOperationHistoryCountByFileId';

  @override
  Future<int> execute(DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryCountByFileId(params['fileId']);
  }
}

// 分页操作
class GetFileOperationHistoryWithPaginationOperation
    extends DatabaseOperation<List<FileOperationHistoryEntityData>> {
  @override
  String get operation => 'getFileOperationHistoryWithPagination';

  @override
  Future<List<FileOperationHistoryEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryWithPagination(
      params['offset'],
      params['limit'],
    );
  }
}

class GetFileOperationHistoryByFileIdWithPaginationOperation
    extends DatabaseOperation<List<FileOperationHistoryEntityData>> {
  @override
  String get operation => 'getFileOperationHistoryByFileIdWithPagination';

  @override
  Future<List<FileOperationHistoryEntityData>> execute(
      DataBase db, Map<String, dynamic> params) {
    return db.getFileOperationHistoryByFileIdWithPagination(
      params['fileId'],
      params['offset'],
      params['limit'],
    );
  }
}
