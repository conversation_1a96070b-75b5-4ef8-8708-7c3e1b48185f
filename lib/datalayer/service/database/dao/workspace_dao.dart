import 'package:drift/drift.dart';
import 'package:turing_art/datalayer/service/database/dao/sort_option.dart';

import '../database.dart';

/// WorkspaceDao 扩展
///
/// 提供了工作区的数据访问功能，支持按时间字段排序。
///
/// 使用示例：
/// ```dart
/// // 1. 获取所有工作区，按创建时间正序排列（默认）
/// final workspaces = await database.getAllWorkspaces();
///
/// // 2. 获取所有工作区，按创建时间正序排列
/// final workspaces = await database.getAllWorkspaces(
///   sortField: SortField.createTime,
///   sortOrder: SortOrder.ascending,
/// );
///
/// // 3. 获取所有工作区，按最后编辑时间倒序排列（最新的在前）
/// final workspaces = await database.getAllWorkspaces(
///   sortField: SortField.lastEditTime,
///   sortOrder: SortOrder.descending,
/// );
///
/// // 4. 获取所有工作区，按创建时间倒序排列
/// final workspaces = await database.getAllWorkspaces(
///   sortField: SortField.createTime,
///   sortOrder: SortOrder.descending,
/// );
/// ```
extension WorkspaceDao on DataBase {
  /// 查询所有工作区，支持数据库级别的排序
  /// [sortField] 排序字段，默认为 createTime
  /// [sortOrder] 排序顺序，默认为 ascending
  ///
  /// 性能优化：
  /// 1. 使用数据库级别的 ORDER BY 进行排序，避免内存排序
  /// 2. 减少循环次数，直接在查询中完成排序
  /// 3. 使用索引优化查询性能
  Future<List<WorkspaceEntityData>> getAllWorkspaces({
    SortField sortField = SortField.createTime,
    SortOrder sortOrder = SortOrder.ascending,
  }) async {
    final query = select(workspaceEntity);

    // 根据排序字段和顺序添加数据库级别的排序
    switch (sortField) {
      case SortField.createTime:
        if (sortOrder == SortOrder.ascending) {
          query.orderBy([(t) => OrderingTerm.asc(t.createTime)]);
        } else {
          query.orderBy([(t) => OrderingTerm.desc(t.createTime)]);
        }
        break;
      case SortField.lastEditTime:
        if (sortOrder == SortOrder.ascending) {
          query.orderBy([(t) => OrderingTerm.asc(t.lastEditTime)]);
        } else {
          query.orderBy([(t) => OrderingTerm.desc(t.lastEditTime)]);
        }
        break;
    }

    // 直接返回排序后的结果，避免额外的循环处理
    return query.get();
  }

  // 根据 workspaceId 获取单个工作区
  Future<WorkspaceEntityData?> getWorkspaceById(String workspaceId) async {
    final result = await (select(workspaceEntity)
          ..where((t) => t.workspaceId.equals(workspaceId)))
        .getSingleOrNull();
    return result;
  }

  Future<List<WorkspaceEntityData>> getWorkspaceByIds(
      List<String> workspaceIds) async {
    // 使用 get 方法获取所有符合条件的记录
    final result = await (select(workspaceEntity)
          ..where((t) => t.workspaceId.isIn(workspaceIds)))
        .get();
    return result;
  }

  // 插入新工作区
  Future<int> insertWorkspace(WorkspaceEntityCompanion entity) {
    return into(workspaceEntity).insert(entity);
  }

  // 更新工作区信息
  Future<bool> updateWorkspace(WorkspaceEntityCompanion entity) {
    return update(workspaceEntity).replace(entity);
  }

  // 删除工作区
  Future<int> deleteWorkspace(String workspaceId) {
    return (delete(workspaceEntity)
          ..where((t) => t.workspaceId.equals(workspaceId)))
        .go();
  }

  // 批量删除项目
  Future<void> deleteWorkspaces(List<String> workspaceIds) async {
    if (workspaceIds.isEmpty) {
      return;
    }

    await batch((batch) {
      batch.deleteWhere(
        workspaceEntity,
        (t) => t.workspaceId.isIn(workspaceIds),
      );
    });
  }
}
