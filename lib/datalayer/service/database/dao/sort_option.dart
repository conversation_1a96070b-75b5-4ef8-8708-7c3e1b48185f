/// 排序顺序枚举
enum SortOrder {
  /// 正序（升序）
  ascending(0),

  /// 倒序（降序）
  descending(1);

  const SortOrder(this.value);
  final int value;

  static SortOrder fromValue(int value) {
    return SortOrder.values.firstWhere(
      (e) => e.value == value,
      orElse: () => SortOrder.ascending,
    );
  }
}

/// 排序字段枚举
enum SortField {
  /// 入口时间（创建时间）
  createTime(0),

  /// 最后编辑时间
  lastEditTime(1);

  const SortField(this.value);
  final int value;

  static SortField fromValue(int value) {
    return SortField.values.firstWhere(
      (e) => e.value == value,
      orElse: () => SortField.createTime,
    );
  }
}
