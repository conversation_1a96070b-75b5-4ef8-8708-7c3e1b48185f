import 'package:drift/drift.dart';

import '../database.dart';

extension FileOperationHistoryDao on DataBase {
  // 查询所有文件操作历史记录，按创建时间降序排列
  Future<List<FileOperationHistoryEntityData>>
      getAllFileOperationHistory() async {
    return (select(fileOperationHistoryEntity)
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)]))
        .get();
  }

  // 根据ID查询单个文件操作历史记录
  Future<FileOperationHistoryEntityData?> getFileOperationHistoryById(
      int id) async {
    return (select(fileOperationHistoryEntity)..where((t) => t.id.equals(id)))
        .getSingleOrNull();
  }

  // 根据文件ID查询该文件的所有操作历史记录，按创建时间降序排列
  Future<List<FileOperationHistoryEntityData>> getFileOperationHistoryByFileId(
      String fileId) async {
    return (select(fileOperationHistoryEntity)
          ..where((t) => t.fileId.equals(fileId))
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)]))
        .get();
  }

  // 根据文件ID列表批量查询文件操作历史记录
  Future<List<FileOperationHistoryEntityData>> getFileOperationHistoryByFileIds(
      List<String> fileIds) async {
    if (fileIds.isEmpty) {
      return [];
    }
    return (select(fileOperationHistoryEntity)
          ..where((t) => t.fileId.isIn(fileIds))
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)]))
        .get();
  }

  // 根据时间范围查询文件操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryByTimeRange(
    int startTime,
    int endTime,
  ) async {
    return (select(fileOperationHistoryEntity)
          ..where((t) =>
              t.createTime.isBetween(Constant(startTime), Constant(endTime)))
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)]))
        .get();
  }

  // 根据文件ID和时间范围查询该文件的操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryByFileIdAndTimeRange(
    String fileId,
    int startTime,
    int endTime,
  ) async {
    return (select(fileOperationHistoryEntity)
          ..where((t) =>
              t.fileId.equals(fileId) &
              t.createTime.isBetween(Constant(startTime), Constant(endTime)))
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)]))
        .get();
  }

  // 插入新的文件操作历史记录
  Future<int> insertFileOperationHistory(
      FileOperationHistoryEntityCompanion entity) async {
    return await into(fileOperationHistoryEntity).insert(entity);
  }

  // 批量插入文件操作历史记录
  Future<void> insertFileOperationHistoryBatch(
    List<FileOperationHistoryEntityCompanion> entities,
  ) async {
    if (entities.isEmpty) {
      return;
    }
    await batch((batch) {
      batch.insertAll(fileOperationHistoryEntity, entities);
    });
  }

  // 更新文件操作历史记录
  Future<bool> updateFileOperationHistory(
      FileOperationHistoryEntityCompanion entity) async {
    return await update(fileOperationHistoryEntity).replace(entity);
  }

  // 批量更新文件操作历史记录
  Future<void> updateFileOperationHistoryBatch(
    List<FileOperationHistoryEntityCompanion> entities,
  ) async {
    if (entities.isEmpty) {
      return;
    }
    await batch((batch) {
      batch.insertAll(
        fileOperationHistoryEntity,
        entities,
        mode: InsertMode.insertOrReplace,
      );
    });
  }

  // 根据ID删除文件操作历史记录
  Future<int> deleteFileOperationHistoryById(int id) async {
    return await (delete(fileOperationHistoryEntity)
          ..where((t) => t.id.equals(id)))
        .go();
  }

  // 根据文件ID删除该文件的所有操作历史记录
  Future<int> deleteFileOperationHistoryByFileId(String fileId) async {
    return await (delete(fileOperationHistoryEntity)
          ..where((t) => t.fileId.equals(fileId)))
        .go();
  }

  // 根据文件ID列表批量删除文件操作历史记录
  Future<void> deleteFileOperationHistoryByFileIds(List<String> fileIds) async {
    if (fileIds.isEmpty) {
      return;
    }
    await batch((batch) {
      batch.deleteWhere(
        fileOperationHistoryEntity,
        (t) => t.fileId.isIn(fileIds),
      );
    });
  }

  // 根据时间范围删除文件操作历史记录
  Future<int> deleteFileOperationHistoryByTimeRange(
    int startTime,
    int endTime,
  ) async {
    return await (delete(fileOperationHistoryEntity)
          ..where((t) =>
              t.createTime.isBetween(Constant(startTime), Constant(endTime))))
        .go();
  }

  // 根据文件ID和时间范围删除该文件的操作历史记录
  Future<int> deleteFileOperationHistoryByFileIdAndTimeRange(
    String fileId,
    int startTime,
    int endTime,
  ) async {
    return await (delete(fileOperationHistoryEntity)
          ..where((t) =>
              t.fileId.equals(fileId) &
              t.createTime.isBetween(Constant(startTime), Constant(endTime))))
        .go();
  }

  // 删除所有文件操作历史记录
  Future<int> deleteAllFileOperationHistory() async {
    return await delete(fileOperationHistoryEntity).go();
  }

  // 获取文件操作历史记录总数
  Future<int> getFileOperationHistoryCount() async {
    return await (select(fileOperationHistoryEntity))
        .get()
        .then((list) => list.length);
  }

  // 根据文件ID获取该文件的操作历史记录总数
  Future<int> getFileOperationHistoryCountByFileId(String fileId) async {
    return await (select(fileOperationHistoryEntity)
          ..where((t) => t.fileId.equals(fileId)))
        .get()
        .then((list) => list.length);
  }

  // 分页查询文件操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryWithPagination(
    int offset,
    int limit,
  ) async {
    return (select(fileOperationHistoryEntity)
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)])
          ..limit(limit, offset: offset))
        .get();
  }

  // 根据文件ID分页查询该文件的操作历史记录
  Future<List<FileOperationHistoryEntityData>>
      getFileOperationHistoryByFileIdWithPagination(
    String fileId,
    int offset,
    int limit,
  ) async {
    return (select(fileOperationHistoryEntity)
          ..where((t) => t.fileId.equals(fileId))
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)])
          ..limit(limit, offset: offset))
        .get();
  }

  // 监听指定文件ID的操作历史记录变化
  Stream<List<FileOperationHistoryEntityData>>
      watchFileOperationHistoryByFileId(String fileId) {
    return (select(fileOperationHistoryEntity)
          ..where((t) => t.fileId.equals(fileId))
          ..orderBy([(t) => OrderingTerm.desc(t.createTime)]))
        .watch();
  }
}

/*
使用示例：

// 1. 插入新的文件操作历史记录
final newRecord = FileOperationHistoryEntityCompanion.insert(
  fileId: 'file_123',
  createTime: DateTime.now().millisecondsSinceEpoch,
  extraData: '{"operation": "edit", "description": "用户编辑了图片"}',
);
final id = await database.insertFileOperationHistory(newRecord);

// 2. 查询某个文件的所有操作历史
final history = await database.getFileOperationHistoryByFileId('file_123');

// 3. 批量插入多个操作记录
final records = [
  FileOperationHistoryEntityCompanion.insert(
    fileId: 'file_123',
    createTime: DateTime.now().millisecondsSinceEpoch,
    extraData: '{"operation": "crop", "description": "裁剪图片"}',
  ),
  FileOperationHistoryEntityCompanion.insert(
    fileId: 'file_456',
    createTime: DateTime.now().millisecondsSinceEpoch,
    extraData: '{"operation": "filter", "description": "应用滤镜"}',
  ),
];
await database.insertFileOperationHistoryBatch(records);

// 4. 删除某个文件的所有操作历史
await database.deleteFileOperationHistoryByFileId('file_123');

// 5. 分页查询某个文件的操作历史
final page1 = await database.getFileOperationHistoryByFileIdWithPagination('file_123', 0, 10);

// 6. 监听某个文件的操作历史变化
database.watchFileOperationHistoryByFileId('file_123').listen((history) {
  print('文件操作历史已更新: ${history.length} 条记录');
});
*/
