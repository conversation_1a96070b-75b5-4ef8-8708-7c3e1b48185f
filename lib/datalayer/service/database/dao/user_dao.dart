import '../database.dart';

extension UserDao on DataBase {
  // 查询所有项目
  Future<List<UserEntityData>> getAllUsers() async {
    final userEntities = await select(userEntity).get();
    return userEntities;
  }

  // 根据 employeeId 获取单个项目
  Future<UserEntityData?> getUserById(String id) async {
    final result = await (select(userEntity)..where((t) => t.id.equals(id)))
        .getSingleOrNull();
    return result;
  }

  // 根据 phoneNumber 获取用户列列表
  Future<List<UserEntityData>?> getUserList(
      {required String phoneNumber}) async {
    final result = await (select(userEntity)
          ..where((t) => t.phoneNumber.equals(phoneNumber)))
        .get();
    return result;
  }

  // 删除用户
  Future<int> deleteUser(String id) {
    return (delete(userEntity)..where((t) => t.id.equals(id))).go();
  }

  // 优化后的 upsert 方法
  Future<void> upsertUser(UserEntityCompanion entity) async {
    await into(userEntity).insertOnConflictUpdate(entity);
  }
}
