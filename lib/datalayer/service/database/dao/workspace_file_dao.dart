import 'package:drift/drift.dart';
import 'package:turing_art/datalayer/service/database/dao/sort_option.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

/// WorkspaceFileDao 扩展
///
/// 提供了工作区文件的数据访问功能，支持按时间字段排序。
///
/// 使用示例：
/// ```dart
/// // 1. 获取所有文件，按创建时间正序排列（默认）
/// final files = await database.getAllFiles();
///
/// // 2. 获取所有文件，按创建时间正序排列
/// final files = await database.getAllFiles(
///   sortField: SortField.createTime,
///   sortOrder: SortOrder.ascending,
/// );
///
/// // 3. 获取所有文件，按最后编辑时间倒序排列（最新的在前）
/// final files = await database.getAllFiles(
///   sortField: SortField.lastEditTime,
///   sortOrder: SortOrder.descending,
/// );
///
/// // 4. 获取所有文件，按创建时间倒序排列
/// final files = await database.getAllFiles(
///   sortField: SortField.createTime,
///   sortOrder: SortOrder.descending,
/// );
/// ```
extension WorkspaceFileDao on DataBase {
  /// 查询所有文件，支持数据库级别的排序
  /// [sortField] 排序字段，默认为 createTime
  /// [sortOrder] 排序顺序，默认为 ascending
  ///
  /// 性能优化：
  /// 1. 使用数据库级别的 ORDER BY 进行排序，避免内存排序
  /// 2. 减少循环次数，直接在查询中完成排序
  /// 3. 使用索引优化查询性能
  Future<List<WorkspaceFileEntityData>> getAllFiles({
    SortField sortField = SortField.createTime,
    SortOrder sortOrder = SortOrder.ascending,
  }) async {
    final query = select(workspaceFileEntity);

    // 根据排序字段和顺序添加数据库级别的排序
    switch (sortField) {
      case SortField.createTime:
        if (sortOrder == SortOrder.ascending) {
          query.orderBy([(t) => OrderingTerm.asc(t.createTime)]);
        } else {
          query.orderBy([(t) => OrderingTerm.desc(t.createTime)]);
        }
        break;
      case SortField.lastEditTime:
        if (sortOrder == SortOrder.ascending) {
          query.orderBy([(t) => OrderingTerm.asc(t.lastEditTime)]);
        } else {
          query.orderBy([(t) => OrderingTerm.desc(t.lastEditTime)]);
        }
        break;
    }

    // 直接返回排序后的结果，避免额外的循环处理
    return query.get();
  }

  /// 查询指定工作区的所有文件
  Future<List<WorkspaceFileEntityData>> getFilesByWorkspaceId(
    String workspaceId, {
    SortField sortField = SortField.createTime,
    SortOrder sortOrder = SortOrder.ascending,
  }) async {
    return getFilesByWorkspaceIds(
      [workspaceId],
      sortField: sortField,
      sortOrder: sortOrder,
    );
  }

  /// 查询指定工作区的所有文件返回默认入库顺序
  Future<List<WorkspaceFileEntityData>> getFilesByWorkspaceWithDefaultSort(
    String workspaceId,
  ) async {
    final fileEntities = await (select(workspaceFileEntity)
          ..where((t) => t.workspaceId.equals(workspaceId)))
        .get();
    return fileEntities;
  }

  /// 查询指定工作区列表的所有文件，支持数据库级别的排序
  /// [workspaceIds] 工作区ID列表
  /// [sortField] 排序字段，默认为 createTime
  /// [sortOrder] 排序顺序，默认为 ascending
  ///
  /// 性能优化：
  /// 1. 使用数据库级别的 ORDER BY 进行排序，避免内存排序
  /// 2. 减少循环次数，直接在查询中完成排序
  /// 3. 使用索引优化查询性能
  Future<List<WorkspaceFileEntityData>> getFilesByWorkspaceIds(
    List<String> workspaceIds, {
    SortField sortField = SortField.createTime,
    SortOrder sortOrder = SortOrder.ascending,
  }) async {
    if (workspaceIds.isEmpty) {
      return [];
    }

    final query = select(workspaceFileEntity)
      ..where((t) => t.workspaceId.isIn(workspaceIds));

    // 根据排序字段和顺序添加数据库级别的排序
    switch (sortField) {
      case SortField.createTime:
        if (sortOrder == SortOrder.ascending) {
          query.orderBy([(t) => OrderingTerm.asc(t.createTime)]);
        } else {
          query.orderBy([(t) => OrderingTerm.desc(t.createTime)]);
        }
        break;
      case SortField.lastEditTime:
        if (sortOrder == SortOrder.ascending) {
          query.orderBy([(t) => OrderingTerm.asc(t.lastEditTime)]);
        } else {
          query.orderBy([(t) => OrderingTerm.desc(t.lastEditTime)]);
        }
        break;
    }

    // 直接返回排序后的结果，避免额外的循环处理
    return query.get();
  }

  Future<WorkspaceFileEntityData?> getWorkspaceFile(
    String workspaceId,
    String fileId,
  ) async {
    return (select(workspaceFileEntity)
          ..where((t) =>
              t.workspaceId.equals(workspaceId) & t.fileId.equals(fileId)))
        .getSingleOrNull();
  }

  // 插入新文件到工作区
  Future<void> insertWorkspaceFile(WorkspaceFileEntityCompanion entity) async {
    await into(workspaceFileEntity).insert(entity);
  }

  // 更新工作区文件信息
  Future<void> updateWorkspaceFile(WorkspaceFileEntityCompanion entity) async {
    await update(workspaceFileEntity).replace(entity);
  }

  // 批量更新工作区文件信息
  Future<void> insertOrUpdateWorkspaceFiles(
    List<WorkspaceFileEntityCompanion> entities,
  ) async {
    if (entities.isEmpty) {
      return;
    }

    await batch((batch) {
      // 使用 replaceAll 进行批量替换，这比循环调用 replace 更高效
      batch.insertAll(
        workspaceFileEntity,
        entities,
        mode: InsertMode.insertOrReplace,
      );
    });
  }

  // 删除工作区中的文件
  Future<void> deleteWorkspaceFile(String workspaceId, String fileId) async {
    await (delete(workspaceFileEntity)
          ..where((t) =>
              t.workspaceId.equals(workspaceId) & t.fileId.equals(fileId)))
        .go();
  }

  // 批量删除工作区中的文件
  Future<void> deleteWorkspaceFiles(
    String workspaceId,
    List<String> fileIds,
  ) async {
    if (fileIds.isEmpty) {
      return;
    }
    await batch((batch) {
      // 使用 deleteWhere 一次性删除所有匹配的文件
      batch.deleteWhere(
        workspaceFileEntity,
        (t) => t.workspaceId.equals(workspaceId) & t.fileId.isIn(fileIds),
      );
    });
  }

  // 根据工作区ID删除所有文件
  Future<void> deleteAllWorkspaceFiles(String workspaceId) async {
    await batch((batch) {
      // 使用 deleteWhere 一次性删除工作区的所有文件
      batch.deleteWhere(
        workspaceFileEntity,
        (t) => t.workspaceId.equals(workspaceId),
      );
    });
  }

  // 批量删除工作区中项目文件
  Future<void> deleteAllWorkspacesFiles(List<String> workspaceIds) async {
    if (workspaceIds.isEmpty) {
      return;
    }

    await batch((batch) {
      batch.deleteWhere(
        workspaceFileEntity,
        (t) => t.workspaceId.isIn(workspaceIds),
      );
    });
  }

  // 根据工作区ID和原始路径查询文件
  Future<WorkspaceFileEntityData?> getFileByWorkspaceIdAndOrgPath(
    String workspaceId,
    String orgPath,
  ) async {
    return (select(workspaceFileEntity)
          ..where((t) =>
              t.workspaceId.equals(workspaceId) & t.orgPath.equals(orgPath)))
        .getSingleOrNull();
  }

  // 根据工作区ID和多个原始路径批量查询文件
  Future<List<WorkspaceFileEntityData>> getFilesByWorkspaceIdAndOrgPaths(
    String workspaceId,
    List<String> orgPaths,
  ) async {
    if (orgPaths.isEmpty) {
      return [];
    }
    return (select(workspaceFileEntity)
          ..where((t) =>
              t.workspaceId.equals(workspaceId) & t.orgPath.isIn(orgPaths)))
        .get();
  }
}
