import '../database.dart';

extension CreatorInfoDao on DataBase {
  // 根据 uid 获取单个创建者信息
  Future<CreatorInfoEntityData?> getCreatorInfoById(String uid) async {
    final result = await (select(creatorInfoEntity)
          ..where((t) => t.uid.equals(uid)))
        .getSingleOrNull();
    return result;
  }

  // 删除创建者信息
  Future<int> deleteCreatorInfo(String uid) {
    return (delete(creatorInfoEntity)..where((t) => t.uid.equals(uid))).go();
  }

  // 插入或更新创建者信息
  Future<void> upsertCreatorInfo(CreatorInfoEntityCompanion entity) async {
    await into(creatorInfoEntity).insertOnConflictUpdate(entity);
  }
}
