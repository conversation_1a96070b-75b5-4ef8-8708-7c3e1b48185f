// 数据库操作执行器
import 'dart:async';
import 'dart:isolate';

import 'package:turing_art/datalayer/service/database/handler/database_ioslate_handler.dart';
import 'package:turing_art/datalayer/service/database/message/db_message.dart';
import 'package:turing_art/utils/pg_log.dart';

class DatabaseExecutor {
  static DatabaseIsolateHandler? _handler;
  static bool _isInitialized = false;

  // 初始化数据库连接
  static Future<void> initialize(String dbPath) async {
    if (_isInitialized) {
      return;
    }

    // 创建初始化端口
    final initPort = ReceivePort();

    // 启动数据库 isolate
    final isolate = await Isolate.spawn(
      DatabaseIsolateHandler.main,
      {
        'sendPort': initPort.sendPort,
        'dbPath': dbPath,
      },
      debugName: 'database_isolate',
    );

    // 等待数据库 isolate 发送它的 sendPort
    final sendToDb = await initPort.first as SendPort;
    initPort.close();

    // 创建真正的通信端口
    final receiveFromDb = ReceivePort();
    final messageController = StreamController<dynamic>.broadcast();

    // 创建处理器
    _handler = DatabaseIsolateHandler(
      isolate: isolate,
      sendToDb: sendToDb,
      receiveFromDb: receiveFromDb,
      messageController: messageController,
    );

    // 监听数据库 isolate 发来的消息
    receiveFromDb.listen((message) {
      PGLog.d('DatabaseExecutor - received message: ${message.runtimeType}');
      messageController.add(message);
    });

    // 发送我们的接收端口给数据库 isolate
    sendToDb.send(receiveFromDb.sendPort);

    _isInitialized = true;
    PGLog.d('DatabaseExecutor initialized');
  }

  // 执行数据库操作
  static Future<T> execute<T>(
    String operation,
    Map<String, dynamic> params,
  ) async {
    if (!_isInitialized) {
      throw StateError('DatabaseExecutor not initialized');
    }

    final responsePort = ReceivePort();
    final messageId = DateTime.now().millisecondsSinceEpoch.toString();

    try {
      PGLog.d(
          'DatabaseExecutor - sending message: $messageId, operation: $operation');

      _handler?.sendMessage(DbMessage<T>(
        operation,
        params,
        responsePort.sendPort,
      ));

      final response = await responsePort.first;
      PGLog.d('DatabaseExecutor - received response for message: $messageId');

      if (response is Exception) {
        throw response;
      }

      return response as T;
    } catch (e) {
      PGLog.e('DatabaseExecutor - error for message $messageId: $e');
      rethrow;
    } finally {
      responsePort.close();
    }
  }

  // 关闭数据库连接
  static Future<void> dispose() async {
    if (!_isInitialized) {
      return;
    }

    await _handler?.dispose();
    _handler = null;
    _isInitialized = false;
    PGLog.d('DatabaseExecutor disposed');
  }
}
