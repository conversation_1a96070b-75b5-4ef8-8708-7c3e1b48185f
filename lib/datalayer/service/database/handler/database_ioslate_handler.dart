import 'dart:async';
import 'dart:isolate';

import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/message/db_message.dart';
import 'package:turing_art/datalayer/service/database/operation/database_operation_registry.dart';
import 'package:turing_art/utils/pg_log.dart';

class DatabaseIsolateHandler {
  final Isolate _isolate;
  final SendPort _sendToDb;
  final ReceivePort _receiveFromDb;
  final StreamController<dynamic> _messageController;

  static DataBase? _db;

  const DatabaseIsolateHandler({
    required Isolate isolate,
    required SendPort sendToDb,
    required ReceivePort receiveFromDb,
    required StreamController<dynamic> messageController,
  })  : _isolate = isolate,
        _sendToDb = sendToDb,
        _receiveFromDb = receiveFromDb,
        _messageController = messageController;

  // 数据库 isolate 的入口点
  static void main(Map<String, dynamic> params) {
    PGLog.d('DatabaseIsolateHandler - isolate started');

    final mainSendPort = params['sendPort'] as SendPort;
    final dbPath = params['dbPath'] as String;
    // 注册所有数据库操作
    DatabaseOperationRegistry.registerAll();

    // 创建数据库 isolate 的接收端口
    final receivePort = ReceivePort();

    // 发送我们的 sendPort 给主线程
    mainSendPort.send(receivePort.sendPort);

    // 监听主线程发来的消息
    receivePort.listen((message) async {
      if (message is SendPort) {
        // 接收主线程的 sendPort
        PGLog.d('DatabaseIsolateHandler - connection established');
      } else if (message is DbMessage) {
        // 处理数据库操作
        try {
          _db ??= DataBase(dbPath);

          final operation = DatabaseOperationRegistry.get(message.operation);
          if (operation == null) {
            throw Exception('Unknown operation: ${message.operation}');
          }

          final result = await operation.execute(_db!, message.params);
          message.responsePort.send(result);
        } catch (e) {
          message.responsePort.send(e);
        }
      } else if (message == 'close') {
        // 关闭数据库连接
        await _db?.close();
        receivePort.close();
        PGLog.d('DatabaseIsolateHandler - isolate closed');
      }
    });
  }

  // 发送消息给数据库 isolate
  void sendMessage<T>(DbMessage<T> message) {
    _sendToDb.send(message);
  }

  // 获取消息流
  Stream<dynamic> get messageStream => _messageController.stream;

  // 关闭处理器
  Future<void> dispose() async {
    PGLog.d('DatabaseIsolateHandler - disposing');
    _sendToDb.send('close');
    _receiveFromDb.close();
    _isolate.kill();
    _messageController.close();
  }
}
