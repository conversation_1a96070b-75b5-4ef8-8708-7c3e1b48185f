import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_effect_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';

class AigcSampleMockService {
  static Future<List<AigcSampleModel>> getAigcSampleMockList(
      String projectId) async {
    return [
      const AigcSampleModel(
        id: '1',
        name: '打样1',
        status: 'completed',
        presetName: '打样1',
        presetEffectName: '打样1',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '1',
            exportStatus: 'completed',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const Aig<PERSON><PERSON><PERSON><PERSON>ode<PERSON>(
        id: '2',
        name: '打样1',
        status: 'running',
        presetName: '打样1',
        presetEffectName: '打样2',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const AigcSampleModel(
        id: '3',
        name: '打样1',
        status: 'completed',
        presetName: '打样1',
        presetEffectName: '打样3',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const AigcSampleModel(
        id: '4',
        name: '打样2',
        status: 'running',
        presetName: '打样4',
        presetEffectName: '打样4',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const AigcSampleModel(
        id: '5',
        name: '打样2',
        status: 'completed',
        presetName: '打样2',
        presetEffectName: '打样5',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const AigcSampleModel(
        id: '6',
        name: '打样2',
        status: 'running',
        presetName: '打样2',
        presetEffectName: '打样6',
        isLargeImageUploaded: false,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const AigcSampleModel(
        id: '7',
        name: '打样3',
        status: 'running',
        presetName: '打样2',
        presetEffectName: '打样7',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const AigcSampleModel(
        id: '7',
        name: '打样3',
        status: 'running',
        presetName: '打样3',
        presetEffectName: '打样8',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
      const AigcSampleModel(
        id: '7',
        name: '打样3',
        status: 'completed',
        presetName: '打样3',
        presetEffectName: '打样9',
        isLargeImageUploaded: true,
        effects: [
          AigcSampleEffectModel(
            effectCode: '2',
            exportStatus: 'running',
            thumbUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/thumb_url.png',
            photoUrl:
                'https://turing-art.oss-cn-hangzhou.aliyuncs.com/aigc_sample/photo_url.png',
          ),
        ],
        createAt: 1749192432,
        updateAt: 1749192432,
      ),
    ];
  }
}
