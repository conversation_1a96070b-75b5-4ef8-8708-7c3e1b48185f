import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_status_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_project_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_response.dart';

part 'aigc_sample_api_service.g.dart';

@RestApi()
abstract class AigcSampleApiService {
  factory AigcSampleApiService(Dio dio, {String? baseUrl}) =
      _AigcSampleApiService;

  /// 获取打样列表
  @GET("/art-aigc/v1/proofing/list")
  Future<AigcSampleResponse> getAigcSampleList(
    @Query("page") int page,
    @Query("page_size") int pageSize,
    @Query("project_id") String projectId,
  );

  /// 获取打样详情
  @GET("/art-aigc/v1/proofing/{proofing_id}/detail")
  Future<AigcSampleModel> getAigcSampleDetail(
    @Path("proofing_id") String proofingId,
  );

  /// 创建打样
  @POST("/art-aigc/v1/proofing/create")
  Future<AigcSampleModel> createAigcSample(
    @Body() Map<String, dynamic> request,
  );

  /*
  {
  "client_project":{// 客户端项目
    "project_id":"",
    "project_name":"",
    "update_at":1749192432,// 10位时间戳
  },
  "preset":{// 打样关联的预设
    "preset_id":"",
    "effect_code":""
  },
  "origin_photo":{// 待打样的图片
    "file_name":"",
    "file_url":""
  },
  "mask_image_url":""
  }
  */

  /// 重新打样
  @POST("/art-aigc/v1/proofing/{proofing_id}/regenerate")
  Future<AigcSampleModel> regenerateAigcSample(
    @Path("proofing_id") String proofingId,
    @Body() Map<String, dynamic> request,
  );

  /// 删除打样
  @POST("/art-aigc/v1/proofing/{proofing_id}/delete")
  Future<void> deleteAigcSample(
    @Path("proofing_id") String proofingId,
  );

  /// 删除打样效果图
  @POST("/art-aigc/v1/proofing/{proofing_id}/effect/{effect_code}/delete")
  Future<void> deleteAigcSampleEffect(
    @Path("proofing_id") String proofingId,
    @Path("effect_code") String effectCode,
  );

  /// 打样生成导出
  @POST("/art-aigc/v1/proofing/{proofing_id}/effect/{effect_code}/export")
  Future<void> exportAigcSample(
    @Path("proofing_id") String proofingId,
    @Path("effect_code") String effectCode,
    @Body() Map<String, dynamic> request,
  );

  /// 打样生成列表
  @GET("/art-aigc/v1/proofing/export/list")
  Future<AigcSampleExportResponse> exportAigcSampleList(
    @Query("page") int page,
    @Query("page_size") int pageSize,
  );

  /// 删除打样导出
  @POST("/art-aigc/v1/proofing/export/delete")
  Future<void> deleteAigcSampleExport(
    @Body() Map<String, dynamic> request,
  );

  /// 轮询打样状态
  @POST("/art-aigc/v1/proofing/status")
  Future<List<AigcPcPresetsLoopModel>> getAigcSampleProofingStatus(
    @Body() Map<String, dynamic> request,
  );

  /// 轮询打样导出状态
  @POST("/art-aigc/v1/proofing/export/status")
  Future<List<AigcSampleExportStatusModel>> getAigcSampleExportStatus(
    @Body() Map<String, dynamic> request,
  );

  // 获取本地项目列表
  @GET("/art-aigc/v1/project/list")
  Future<AigcSampleProjectResponse> getAigcSampleProjectList(
    @Query("page") int page,
    @Query("page_size") int pageSize,
  );

  // 打样信息更新接口
  @POST("/art-aigc/v1/proofing/{proofing_id}/update")
  Future<void> updateAigcSampleInfo(
    @Path("proofing_id") String proofingId,
    @Body() Map<String, dynamic> request,
  );

  /// 打样导出下载状态上报接口
  @POST("/art-aigc/v1/proofing/export/download/update")
  Future<void> updateAigcSampleDownloadStatus(
    @Body() Map<String, dynamic> request,
  );
}
