import 'package:turing_art/datalayer/domain/models/account/account.dart';
import 'package:turing_art/datalayer/domain/models/account/account_all.dart';
import 'package:turing_art/datalayer/service/api/account/account_api_service.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 账户服务接口
abstract class AccountService {
  /// 获取账户信息
  Future<Account?> getAccount();

  /// 获取完整账户信息（包含积分等信息）
  Future<AccountAll?> getAllAccount();
}

/// 账户服务实现类
class AccountServiceImpl implements AccountService {
  final ApiClient _apiClient;
  late final AccountApiService _accountApiService;

  AccountServiceImpl(this._apiClient) {
    // 使用 DioFactory 创建配置好的 Dio 实例
    final dio = DioFactory.createDio(_apiClient);
    _accountApiService = AccountApiService(dio);
  }

  @override
  Future<Account?> getAccount() async {
    try {
      // 生成的 API 服务
      final response = await _accountApiService.getAccount();
      PGLog.d('获取账户信息成功: $response');
      return response;
    } catch (e) {
      PGLog.e('获取账户信息失败: $e');
      return null;
    }
  }

  @override
  Future<AccountAll?> getAllAccount() async {
    try {
      // 生成的 API 服务
      final response = await _accountApiService.getAllAccount();
      PGLog.d('获取完整账户信息成功: $response');
      return response;
    } catch (e) {
      PGLog.e('获取完整账户信息失败: $e');
      return null;
    }
  }
}
