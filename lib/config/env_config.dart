import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 环境枚举
enum Environment {
  dev('dev'),
  qa('qa'),
  prod('prod');

  final String name;

  const Environment(this.name);

  bool get isDev => this == Environment.dev;

  bool get isQa => this == Environment.qa;

  bool get isProd => this == Environment.prod;
}

class EnvConfig {
  // 私有构造函数
  EnvConfig._();

  // 当前环境
  static Environment _environment = Environment.dev;

  static Environment get environment => _environment;

  // 基础配置
  static const String appId = 'turing';

  // 默认版本号，用于在无法从 pubspec.yaml 读取时使用
  static const String _defaultVersion = '1.0.0';
  static String _cachedVersion = _defaultVersion;

  // 强制重新登录版本阈值，低于此版本的用户需要重新登录
  static const String forceReLoginVersion = '1.1.0';

  // 获取版本号，优先返回缓存的版本号
  static String get version => _cachedVersion;

  // 从 pubspec.yaml 异步获取应用版本号并缓存
  static Future<String> getAppVersion() async {
    try {
      // 直接从 pubspec.yaml 文件读取版本号
      final String pubspecContent = await rootBundle.loadString('pubspec.yaml');
      // 查找版本号行
      final RegExp versionRegExp = RegExp(r'version:\s*([\d\.]+)');
      final Match? match = versionRegExp.firstMatch(pubspecContent);

      if (match != null && match.groupCount >= 1) {
        _cachedVersion = match.group(1)!;
        return _cachedVersion;
      }
    } catch (e) {
      // 如果读取失败，记录错误并返回默认版本号
      if (enableLog) {
        PGLog.e('无法读取版本号: $e');
      }
    }
    return _defaultVersion;
  }

  // API 配置
  static String get baseUrl => _getBaseUrl();

  static String get appChannel => _getAppChannel();

  static bool get enableLog => !_environment.isProd;

  // 缓存配置
  static Duration get cacheMaxAge => _environment.isProd
      ? const Duration(hours: 24)
      : const Duration(minutes: 5);

  // 初始化环境
  static void initEnv(Environment env) {
    _environment = env;
    _logEnvInfo();
    // 初始化时尝试加载版本号
    getAppVersion();
  }

  // 获取基础 URL
  static String _getBaseUrl() {
    switch (_environment) {
      case Environment.dev:
        return 'https://turing-api-dev.pinguo.cn';
      case Environment.qa:
        return 'https://turing-api-qa.pinguo.cn';
      case Environment.prod:
        return 'https://turing-api.pinguo.cn';
    }
  }

  // 获取渠道信息
  static String _getAppChannel() {
    switch (_environment) {
      case Environment.dev:
        return 'dev';
      case Environment.qa:
        return 'qa';
      case Environment.prod:
        return 'prod';
    }
  }

  // 打印环境信息
  static void _logEnvInfo() {
    if (enableLog) {
      PGLog.d('当前环境: ${_environment.name}');
      PGLog.d('API地址: $baseUrl');
      PGLog.d('App渠道: $appChannel');
    }
  }

  // 获取是否是beta版本
  // 根据版本号第一位是否大于0来判断是否是beta版本
  // 1.1.0 -> true
  // 1.0.0 -> true
  // 0.9.0 -> false
  static bool get isBetaVersion {
    if (_cachedVersion.isEmpty) {
      return false;
    }
    final String version = _cachedVersion.split('.').first;
    return version.isNotEmpty && int.parse(version) < 1;
  }
}
