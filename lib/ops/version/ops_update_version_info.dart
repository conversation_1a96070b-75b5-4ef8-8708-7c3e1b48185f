import 'package:json_annotation/json_annotation.dart';
import 'package:turing_art/ops/version/ops_update_version_platform_info.dart';

part 'ops_update_version_info.g.dart';

/// 版本更新信息模型
@JsonSerializable()
class OpsUpdateVersionInfo {
  final List<OpsUpdateVersionPlatformInfo?>? versions;

  OpsUpdateVersionInfo({required this.versions});

  factory OpsUpdateVersionInfo.fromJson(Map<String, dynamic> json) =>
      _$OpsUpdateVersionInfoFromJson(json);

  Map<String, dynamic> toJson() => _$OpsUpdateVersionInfoToJson(this);
}
