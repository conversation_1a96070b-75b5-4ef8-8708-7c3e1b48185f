import 'package:json_annotation/json_annotation.dart';

part 'ops_update_version_platform_info.g.dart';

@JsonSerializable()
class OpsUpdateVersionPlatformInfo {
  final String? platform;
  final String? version;
  final String? title;
  final String? description;
  final String? packageUrl;
  final String? packageName;
  final bool? isForce;
  final List<String>? forceRange;

  OpsUpdateVersionPlatformInfo({
    this.platform,
    this.version,
    this.title,
    this.description,
    this.packageUrl,
    this.packageName,
    this.isForce,
    this.forceRange,
  });

  factory OpsUpdateVersionPlatformInfo.fromJson(Map<String, dynamic> json) =>
      _$OpsUpdateVersionPlatformInfoFromJson(json);

  Map<String, dynamic> toJson() => _$OpsUpdateVersionPlatformInfoToJson(this);
}
