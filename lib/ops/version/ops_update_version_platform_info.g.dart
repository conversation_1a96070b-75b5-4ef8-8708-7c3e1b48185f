// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ops_update_version_platform_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OpsUpdateVersionPlatformInfo _$OpsUpdateVersionPlatformInfoFromJson(
        Map<String, dynamic> json) =>
    OpsUpdateVersionPlatformInfo(
      platform: json['platform'] as String?,
      version: json['version'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      packageUrl: json['packageUrl'] as String?,
      packageName: json['packageName'] as String?,
      isForce: json['isForce'] as bool?,
      forceRange: (json['forceRange'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$OpsUpdateVersionPlatformInfoToJson(
        OpsUpdateVersionPlatformInfo instance) =>
    <String, dynamic>{
      'platform': instance.platform,
      'version': instance.version,
      'title': instance.title,
      'description': instance.description,
      'packageUrl': instance.packageUrl,
      'packageName': instance.packageName,
      'isForce': instance.isForce,
      'forceRange': instance.forceRange,
    };
