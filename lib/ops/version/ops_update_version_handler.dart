import 'dart:async';
import 'dart:io';

import 'package:pg_ops_sdk/pg_ops_sdk.dart';
import 'package:turing_art/ops/version/ops_update_version_info.dart';
import 'package:turing_art/ops/version/ops_update_version_platform_info.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/update_manager.dart';

/// 版本更新处理器
class OpsUpdateVersionHandler {
  late OpsUpdateVersionPlatformInfo? matchingVersion;

  // 添加一个标志来跟踪请求状态
  bool _isRequestInProgress = false;

  // 添加一个Completer来处理并发请求
  Completer<OpsUpdateVersionInfo?>? _requestCompleter;

  /// 根据当前平台获取匹配的版本信息
  void _updateCurrentMatchingVersion(
      List<OpsUpdateVersionPlatformInfo?>? versions) {
    String platform = '';

    if (Platform.isWindows) {
      // 获取Windows版本
      final windowsVersion = _getWindowsVersion();
      platform = windowsVersion;
    } else if (Platform.isIOS) {
      platform = 'ios';
    } else if (Platform.isAndroid) {
      platform = 'android';
    } else if (Platform.isMacOS) {
      platform = 'macos';
    } else if (Platform.isLinux) {
      platform = 'linux';
    }

    // 查找匹配的版本信息
    matchingVersion = versions?.firstWhere(
      (item) =>
          platform.toLowerCase().contains(item?.platform?.toLowerCase() ?? ""),
    );

    PGLog.d(
        '匹配到的版本信息: ${matchingVersion?.platform} - ${matchingVersion?.version}');
  }

  Future<void> _updateDownloadedStatus() async {
    // 检查是否已经下载了这个版本
    await UpdateManager().getDownloadDirectory();
    UpdateManager().updateDownloadedCompletedStatusIfNeeded();
  }

  /// 获取Windows版本
  String _getWindowsVersion() {
    try {
      // 这里使用简单的方法判断Windows版本
      // 实际应用中可能需要更复杂的逻辑或第三方库
      if (Platform.operatingSystemVersion.contains('Windows 11')) {
        return 'win';
      } else if (Platform.operatingSystemVersion.contains('Windows 10')) {
        return 'win';
      } else if (Platform.operatingSystemVersion.contains('Windows 7')) {
        return 'win-7';
      } else {
        // 默认返回win
        return 'win';
      }
    } catch (e) {
      // 出错时返回默认值
      return 'win';
    }
  }

  /// 检查是否有新版本
  bool hasNewVersion() {
    if (matchingVersion == null) {
      PGLog.d('没有找到匹配的版本信息');
      return false;
    }

    // 获取当前应用版本
    final currentVersion = AppInfo.version;

    // 比较版本号
    return _compareVersions(
            matchingVersion?.version ?? '0.0.0', currentVersion) >
        0;
  }

  /// 检查是否需要强制更新
  bool needsForceUpdate() {
    if (matchingVersion == null) {
      return false;
    }

    // 如果isForce为true，则强制更新
    if (matchingVersion!.isForce ?? false) {
      return true;
    }

    // 获取当前应用版本
    final currentVersion = AppInfo.version;

    // 检查当前版本是否在强制更新范围内
    for (final range in matchingVersion!.forceRange ?? []) {
      // 处理范围格式，例如 "0.9.0~1.0.0"
      if (range.contains('~')) {
        final parts = range.split('~');
        if (parts.length == 2) {
          final minVersion = parts[0];
          final maxVersion = parts[1];

          // 检查当前版本是否在范围内
          if (_compareVersions(currentVersion, minVersion) >= 0 &&
              _compareVersions(currentVersion, maxVersion) < 0) {
            return true;
          }
        }
      }
      // 处理单一版本，例如 "0.9.0"
      else if (currentVersion == range) {
        return true;
      }
    }

    return false;
  }

  /// 获取下载URL
  String? getDownloadUrl() {
    return matchingVersion?.packageUrl;
  }

  /// 获取版本标题
  String? getVersionTitle() {
    return matchingVersion?.title;
  }

  /// 获取版本描述
  String? getVersionDescription() {
    return matchingVersion?.description;
  }

  /// 比较两个版本号
  /// 返回值: 1表示v1>v2, 0表示v1=v2, -1表示v1<v2
  int _compareVersions(String v1, String v2) {
    final v1Parts = v1.split('.').map(int.parse).toList();
    final v2Parts = v2.split('.').map(int.parse).toList();

    // 确保两个列表长度相同
    while (v1Parts.length < v2Parts.length) {
      v1Parts.add(0);
    }
    while (v2Parts.length < v1Parts.length) {
      v2Parts.add(0);
    }

    // 逐个比较版本号的各个部分
    for (int i = 0; i < v1Parts.length; i++) {
      if (v1Parts[i] > v2Parts[i]) {
        return 1;
      } else if (v1Parts[i] < v2Parts[i]) {
        return -1;
      }
    }

    return 0; // 版本号相同
  }

  /// 从服务器获取自定义版本升级数据
  /// 添加防止重复调用限制，必须等到上次结果返回后再执行
  Future<bool?> fetchUpdateVersionInfo({required String code}) async {
    // 如果已经有请求在进行中，返回该请求的结果
    if (_isRequestInProgress) {
      PGLog.d('已有版本信息请求正在进行中，等待结果...');
      return null;
    }

    // 标记请求开始
    _isRequestInProgress = true;
    _requestCompleter = Completer<OpsUpdateVersionInfo?>();

    try {
      PGLog.d('开始获取版本信息: $code');
      final result = await PgOps.v1().getCustomTable<OpsUpdateVersionInfo>(
        code: code,
        fromJson: OpsUpdateVersionInfo.fromJson,
      );

      // 如果获取到数据，更新匹配版本
      if (result.versions != null && result.versions!.isNotEmpty) {
        _updateCurrentMatchingVersion(result.versions!);
        _updateDownloadedStatus();
        PGLog.d('版本信息获取成功');
      } else {
        PGLog.d('获取到的版本信息为空');
        return false;
      }

      // 完成请求
      _requestCompleter?.complete(result);
      return hasNewVersion();
    } catch (e) {
      PGLog.e('API调用失败 - 获取自定义版本升级数据: $e');
      // 出错时也要完成请求
      _requestCompleter?.complete(null);
      return null;
    } finally {
      // 无论成功失败，都标记请求结束
      _isRequestInProgress = false;
      _requestCompleter = null;
    }
  }
}
