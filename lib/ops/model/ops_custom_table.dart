import 'package:json_annotation/json_annotation.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/ops/model/expired_time.dart';

part 'ops_custom_table.g.dart';

@JsonSerializable()
class OpsCustomTable {
  // 过期时间配置
  final ExpiredTime? betaConfig;
  // 支付渠道
  final List<String>? payChannel;
  // 不显示微信礼包的商店ID列表
  final List<String>? noWechatGiftStoreIds;
  // 是否显示微信礼包入口
  final bool? showWechatGiftForAllStore;
  // 新增字段
  final String? sampleKey;

  final List<String>? aigcUserPhoneNumbers;

  OpsCustomTable({
    this.betaConfig,
    this.payChannel,
    this.noWechatGiftStoreIds,
    this.showWechatGiftForAllStore,
    this.aigcUserPhoneNumbers,
    this.sampleKey,
  });

  factory OpsCustomTable.fromJson(Map<String, dynamic> json) =>
      _$OpsCustomTableFromJson(json);

  Map<String, dynamic> toJson() => _$OpsCustomTableToJson(this);

  // 获取支付渠道枚举列表
  List<PaymentChannel>? get paymentChannels => payChannel
      ?.map((e) => PaymentChannel.values.firstWhere(
            (element) => element.name == e,
            orElse: () => throw FormatException('Invalid channel $e'),
          ))
      .toList();
}
