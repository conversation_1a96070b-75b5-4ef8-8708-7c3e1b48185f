import 'package:json_annotation/json_annotation.dart';
import 'package:turing_art/ops/model/aigc_point_item.dart';

part 'aigc_point_config.g.dart';

@JsonSerializable()

/// AIGC积分消耗配置
class AigcPointConfig {
  final String? name;
  final List<AigcPointItem>? points;

  AigcPointConfig({
    this.name,
    this.points,
  });

  factory AigcPointConfig.fromJson(Map<String, dynamic> json) =>
      _$AigcPointConfigFromJson(json);
}
