import 'package:pg_ops_sdk/config/user_info_provider.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';

class OpsUserInfoProvider extends UserInfoProvider {
  final CurrentUserRepository userRepository;
  OpsUserInfoProvider({required this.userRepository});

  @override
  String getUserId() {
    return userRepository.user?.userId ?? "";
  }

  @override
  String getUserToken() {
    return userRepository.user?.token ?? "";
  }
}
