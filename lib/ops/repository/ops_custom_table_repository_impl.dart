import 'package:pg_ops_sdk/pg_ops_sdk.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/ops/model/aigc_point_config.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../model/ops_custom_table.dart';
import 'ops_custom_table_repository.dart';

/// OpsCustomTable 数据仓库实现
class OpsCustomTableRepositoryImpl implements OpsCustomTableRepository {
  // ops表全局使用，不频繁从服务器获取，目前暂定生命周期期间拉取一次，如果有其他需求，再考虑换更新机制
  final Map<String, OpsCustomTable> _cache = {};
  // AIGC积分配置缓存
  AigcPointConfig? _aigcPointConfigCache;

  @override
  Future<OpsCustomTable?> getCustomTable(
      {required String code, bool refresh = false}) async {
    // 检查缓存
    if (!refresh && _cache.containsKey(code)) {
      return _cache[code];
    }

    // 直接从服务器获取最新数据
    final result = await _fetchCustomTable(code: code);
    if (result != null) {
      _cache[code] = result;
    }
    return result;
  }

  void clearCache() {
    _cache.clear();
    _aigcPointConfigCache = null;
  }

  // 获取可用的支付渠道
  @override
  Future<List<PaymentChannel>> getAvailableChannels() async {
    // 默认支付渠道
    List<PaymentChannel> defaultChannels = [
      PaymentChannel.wechat,
      PaymentChannel.alipay,
    ];
    try {
      // 从当前仓库获取支付渠道配置
      final table = await getCustomTable(code: 'AppConfig');

      if (table != null &&
          table.paymentChannels != null &&
          table.paymentChannels!.isNotEmpty) {
        // 直接使用OpsCustomTable的paymentChannels属性
        return table.paymentChannels!;
      }
      return defaultChannels;
    } catch (e) {
      // 使用默认渠道
      return defaultChannels;
    }
  }

  /// 从服务器获取自定义表格数据
  Future<OpsCustomTable?> _fetchCustomTable({required String code}) async {
    try {
      return await PgOps.v1().getCustomTable<OpsCustomTable>(
        code: code,
        fromJson: OpsCustomTable.fromJson,
      );
    } catch (e) {
      PGLog.d('API调用失败 - 获取自定义表格: $e');
      return null;
    }
  }

  @override
  Future<List<String>> getNoWechatGiftStoreIds() async {
    try {
      // 从当前仓库获取支付渠道配置
      final table = await getCustomTable(code: 'AppConfig');

      if (table != null &&
          table.noWechatGiftStoreIds != null &&
          table.noWechatGiftStoreIds!.isNotEmpty) {
        return table.noWechatGiftStoreIds!;
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> getShowWechatGiftForAllStore() async {
    try {
      // 从当前仓库获取支付渠道配置
      final table = await getCustomTable(code: 'AppConfig');

      if (table != null && table.showWechatGiftForAllStore != null) {
        return table.showWechatGiftForAllStore!;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<String>> getAigcUserPhoneNumbers() async {
    try {
      final table = await getCustomTable(code: 'AppConfig');
      if (table != null && table.aigcUserPhoneNumbers != null) {
        return table.aigcUserPhoneNumbers!;
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<String?> getSampleKey() async {
    try {
      final table = await getCustomTable(code: 'AppConfig');
      return table?.sampleKey;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<AigcPointConfig?> getAigcPointConfig() async {
    // 如果有缓存，直接返回
    if (_aigcPointConfigCache != null) {
      return _aigcPointConfigCache;
    }

    try {
      // 从服务器获取AIGC积分配置
      final result = await PgOps.v1().getCustomTable<Map<String, dynamic>>(
        code: 'aigcPointPonfig',
        fromJson: (json) => json,
      );

      if (result.containsKey('aigc_point_config')) {
        // 解析配置
        final config = AigcPointConfig.fromJson(
            result['aigc_point_config'] as Map<String, dynamic>);
        // 缓存结果
        _aigcPointConfigCache = config;
        return config;
      }
      PGLog.d('获取AIGC积分配置失败: 返回数据格式不正确');
      return null;
    } catch (e) {
      PGLog.d('获取AIGC积分配置异常: $e');
      return null;
    }
  }
}
