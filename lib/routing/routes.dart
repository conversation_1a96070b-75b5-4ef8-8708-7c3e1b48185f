// Copyright 2024 The Flutter team. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

abstract final class Routes {
  // 启动页
  static const splash = '/';

  // 登录页
  static const login = '/$loginRelative';
  static const loginRelative = 'login';

  // 登录页(pc)
  static const loginPc = '/$loginPcRelative';
  static const loginPcRelative = 'login_pc';

  // 项目首页
  static const projectHome = '/$projectHomeRelative';
  static const projectHomeRelative = 'project_home';

  // 项目首页(pc)
  static const projectHomePc = '/$projectHomePcRelative';
  static const projectHomePcRelative = 'project_home_pc';

  // 编辑页
  static const edit = '/$editRelative';
  static const editRelative = 'edit';

  // 版本过期页
  static const expired = '/$expiredRelative';
  static const expiredRelative = 'expired';

  // h5页
  static const h5 = '/$h5Relative';
  static const h5Relative = 'h5';

  // 编辑页面(macOs使用)
  static const editMac = '/$editMacRelative';
  static const editMacRelative = 'edit_mac';

  // 编辑页面(windows使用)
  static const editWin = '/$editWinRelative';
  static const editWinRelative = 'edit_win';

  // AI预设
  static const aigcPresets = '/$aigcPresetsRelative';
  static const aigcPresetsRelative = 'aigc_presets';

  // AIGC项目页面
  static const aigcProject = '/$aigcProjectRelative';
  static const aigcProjectRelative = 'aigc_project';

  // AI打样(pc)
  static const aigcSample = '/$aigcSampleRelative';
  static const aigcSampleRelative = 'aigc_sample';

  // AI打样详情(pc)
  static const aigcSampleDetail = '/$aigcSampleDetailRelative';
  static const aigcSampleDetailRelative = 'aigc_sample_detail';

  // AIGC编辑场景
  static const aigcEditing = '/$aigcEditingRelative';
  static const aigcEditingRelative = 'aigc_editing';
}
