import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../routing/routes.dart';
import '../utils/app_constants.dart';

/// NavigatorService 提供导航功能的抽象接口
///
/// 核心功能：
/// - smartPush: 智能推送方法，防止页面无限循环推送
///
/// 使用场景：
/// 在打样中心点击主题预设跳转到预设页面，预设页面又有打样中心入口，
/// 用户来回点击会导致导航栈过深，点击返回按钮需要多次操作才能回到起始页面。
///
/// smartPush解决方案：
/// 1. 检查目标路由是否已存在于当前导航栈中
/// 2. 如果存在，直接pop到该页面位置
/// 3. 如果不存在，正常执行push操作
///
/// 示例：
/// ```dart
/// // 使用智能导航，防止循环推送
/// navigatorService.smartPush('/aigc_sample');
/// ```
abstract class NavigatorService {
  Future<void> navigateToEdit(String projectId);
  Future<void> navigateToAigcEditing(String projectId);
  Future<void> navigateToAigcPresets({String? maskPath, String? previewPath});
  Future<Object?> navigateToAigcSampleDetail(String sampleId);
  void pop();
  void navigateToLogin();
  // 新增智能push方法，防止无限循环
  Future<Object?> smartPush(String route);
}

class GoRouterNavigatorService implements NavigatorService {
  final BuildContext context;

  GoRouterNavigatorService(this.context);

  @override
  Future<void> navigateToEdit(String projectId) async {
    await smartPush('${Routes.edit}?projectId=$projectId');
  }

  @override
  Future<void> navigateToAigcEditing(String projectId) async {
    await smartPush('${Routes.aigcEditing}?projectId=$projectId');
  }

  @override
  Future<void> navigateToAigcPresets(
      {String? maskPath, String? previewPath}) async {
    String route = Routes.aigcPresets;
    List<String> queryParams = [];

    if (maskPath != null && maskPath.isNotEmpty) {
      queryParams.add('maskPath=${Uri.encodeComponent(maskPath)}');
    }
    if (previewPath != null && previewPath.isNotEmpty) {
      queryParams.add('previewPath=${Uri.encodeComponent(previewPath)}');
    }

    if (queryParams.isNotEmpty) {
      route += '?${queryParams.join('&')}';
    }

    await smartPush(route);
  }

  @override
  Future<Object?> navigateToAigcSampleDetail(String sampleId) async {
    return await smartPush('${Routes.aigcSampleDetail}?sampleId=$sampleId');
  }

  @override
  void pop() {
    context.pop();
  }

  @override
  void navigateToLogin() {
    if (AppConstants.isDesktop) {
      context.go(Routes.loginPc);
    } else {
      context.go(Routes.login);
    }
  }

  @override
  Future<Object?> smartPush(String route) async {
    final router = GoRouter.of(context);
    final routerDelegate = router.routerDelegate;

    // 获取当前路由栈
    final currentMatches = routerDelegate.currentConfiguration.matches;

    // 提取目标路由的基础路径（去掉查询参数）
    final targetUri = Uri.parse(route);
    final targetPath = targetUri.path;

    // 检查栈中是否已存在相同的路由路径
    for (int i = 0; i < currentMatches.length; i++) {
      final match = currentMatches[i];
      final matchUri = Uri.parse(match.matchedLocation);
      final matchPath = matchUri.path;

      if (matchPath == targetPath) {
        // 如果找到相同路径，计算需要pop的次数
        final popCount = currentMatches.length - i - 1;

        // 执行pop操作回到目标页面
        for (int j = 0; j < popCount; j++) {
          context.pop();
        }
        return null;
      }
    }

    // 如果栈中不存在该路由，则正常push并等待返回值
    return await context.push(route);
  }
}
