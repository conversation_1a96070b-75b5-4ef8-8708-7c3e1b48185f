import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../utils/app_constants.dart';
import '../routes.dart';
import 'redirect_handler.dart';

class HomeRedirectHandler extends RedirectHandler {
  @override
  Future<String?> handleRedirect(
      BuildContext context, GoRouterState state) async {
    if (AppConstants.isDesktop) {
      return Routes.projectHomePc;
    }
    return Routes.projectHome;
  }
}
