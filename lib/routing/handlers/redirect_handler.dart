import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// 重定向处理器抽象基类
abstract class RedirectHandler {
  RedirectHandler? _nextHandler;

  /// 设置下一个处理器
  void setNext(RedirectHandler handler) {
    _nextHandler = handler;
  }

  /// 处理重定向
  Future<String?> handle(BuildContext context, GoRouterState state) async {
    if (!context.mounted) return null;
    return handleRedirect(context, state);
  }

  /// 具体的重定向逻辑
  Future<String?> handleRedirect(BuildContext context, GoRouterState state);

  /// 传递给下一个处理器
  Future<String?> handleNext(BuildContext context, GoRouterState state) async {
    if (_nextHandler != null && context.mounted) {
      return _nextHandler!.handle(context, state);
    }
    return null;
  }
}
