import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../../core/manager/expiry_manager.dart';
import '../routes.dart';
import 'redirect_handler.dart';

/// 过期检查处理器
class ExpiryRedirectHandler extends RedirectHandler {
  final ExpiryManager _expiryManager;

  ExpiryRedirectHandler(this._expiryManager);

  @override
  Future<String?> handleRedirect(
      BuildContext context, GoRouterState state) async {
    final String location = state.matchedLocation;
    final bool isExpiredScreen = location == Routes.expired;
    PGLog.i('handleRedirect 开始获取是否过期');
    final isExpired = await _expiryManager.isExpired();
    PGLog.i('handleRedirect 是否过期结果= $isExpired');
    if (isExpired && !isExpiredScreen) {
      PGLog.i('handleRedirect 跳转到过期页面');
      return Routes.expired;
    }
    if (isExpired && isExpiredScreen) {
      PGLog.i('handleRedirect 已在过期页面');
      return null;
    }

    if (!context.mounted) {
      PGLog.i('handleRedirect 上下文未挂载');
      return null;
    }
    PGLog.i('handleRedirect 继续');
    return handleNext(context, state);
  }
}
