import 'dart:io';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../routes.dart';
import 'redirect_handler.dart';

/// Windows平台Unity编辑页面处理器
class WinUnityEditRedirectHandler extends RedirectHandler {
  @override
  Future<String?> handleRedirect(
      BuildContext context, GoRouterState state) async {
    if (Platform.isWindows) {
      final projectId = state.uri.queryParameters['projectId'];
      return '${Routes.editWin}?projectId=$projectId';
    }
    return handleNext(context, state);
  }
}
