import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../datalayer/repository/current_user_repository.dart';
import '../../utils/app_constants.dart';
import '../routes.dart';
import 'redirect_handler.dart';

/// 认证检查处理器
class AuthRedirectHandler extends RedirectHandler {
  final CurrentUserRepository _currentUserRepository;

  AuthRedirectHandler(this._currentUserRepository);

  @override
  Future<String?> handleRedirect(
      BuildContext context, GoRouterState state) async {
    final bool loggedIn = _currentUserRepository.isLoggedIn;
    final String location = state.matchedLocation;
    final bool loggingIn =
        location == Routes.login || location == Routes.loginPc;
    final bool isSplash = location == Routes.splash;
    final bool isH5Page = location == Routes.h5;

    if (isSplash || isH5Page) {
      return null;
    }
    if (!loggedIn && !loggingIn) {
      return AppConstants.isDesktop ? Routes.loginPc : Routes.login;
    }
    if (loggedIn && loggingIn) {
      return Routes.projectHome;
    }

    if (!context.mounted) {
      return null;
    }
    return handleNext(context, state);
  }
}
