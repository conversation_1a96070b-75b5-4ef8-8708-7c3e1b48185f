import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../core/manager/expiry_manager.dart';
import '../../datalayer/repository/current_user_repository.dart';
import 'auth_redirect_handler.dart';
import 'expiry_redirect_handler.dart';
import 'home_redirect_handler.dart';
import 'mac_unity_edit_redirect_handler.dart';
import 'win_unity_edit_redirect_handler.dart';

/// 重定向处理器工厂
class RedirectHandlerFactory {
  /// 创建全局重定向处理器
  static Future<String?> Function(BuildContext, GoRouterState)
      createGlobalHandler(
    ExpiryManager expiryManager,
    CurrentUserRepository currentUserRepository,
  ) {
    // 创建处理器实例
    final expiryHandler = ExpiryRedirectHandler(expiryManager);
    final authHandler = AuthRedirectHandler(currentUserRepository);

    // 构建责任链
    expiryHandler.setNext(authHandler);

    // 返回处理函数
    return expiryHandler.handle;
  }

  /// 创建编辑页面重定向处理器
  static Future<String?> Function(BuildContext, GoRouterState)
      createEditHandler() {
    final macHandler = MacUnityEditRedirectHandler();
    final winHandler = WinUnityEditRedirectHandler();

    macHandler.setNext(winHandler);

    return macHandler.handle;
  }

  /// 创建首页重定向处理器
  static Future<String?> Function(BuildContext, GoRouterState)
      createHomeHandler() {
    final homeHandler = HomeRedirectHandler();
    return homeHandler.handle;
  }
}
