import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:io';
// import 'package:process_run/shell.dart';
// import 'package:window_manager/window_manager.dart';

import '../routes.dart';
import 'redirect_handler.dart';

/// Mac平台Unity编辑页面处理器
class MacUnityEditRedirectHandler extends RedirectHandler {
  @override
  Future<String?> handleRedirect(
      BuildContext context, GoRouterState state) async {
    if (Platform.isMacOS) {
      final projectId = state.uri.queryParameters['projectId'];
      return '${Routes.editMac}?projectId=$projectId';
    }
    return handleNext(context, state);
  }
}
