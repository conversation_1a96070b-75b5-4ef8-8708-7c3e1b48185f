import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/login/pc/widgets/login_pc_screen.dart';
import 'package:turing_art/ui/login/widgets/login_screen.dart';

import '../core/manager/expiry_manager.dart';
import '../datalayer/repository/current_user_repository.dart';
import '../ui/aigc_editing/widgets/aigc_editing_scene.dart';
import '../ui/aigc_presets/widget/aigc_pc_presets_screen.dart';
import '../ui/aigc_project/widgets/aigc_pc_project_screen.dart';
import '../ui/aigc_sample/widget/aigc_pc_sample_detail/aigc_sample_detail_screen.dart';
import '../ui/aigc_sample/widget/aigc_pc_sample_screen.dart';
import '../ui/edit/widgets/edit_screen.dart';
import '../ui/edit/widgets/mac_edit_screen.dart';
import '../ui/edit/widgets/win_edit_screen.dart';
import '../ui/expired/widgets/expired_screen.dart';
import '../ui/h5/widgets/h5_screen.dart';
import '../ui/project_home/widgets/pc/project_home_screen_pc.dart';
import '../ui/project_home/widgets/project_home_screen.dart';
import '../ui/splash/widgets/splash_screen.dart';
import 'handlers/redirect_handler_factory.dart';
import 'routes.dart';

final routeObserver = RouteObserver<ModalRoute<void>>();

final router = GoRouter(
  initialLocation: Routes.splash,
  redirect: globalRedirect,
  routes: [
    GoRoute(
      path: Routes.splash,
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: Routes.login,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: Routes.loginPc,
      builder: (context, state) => const LoginPcScreen(),
    ),
    GoRoute(
      path: Routes.projectHome,
      redirect: RedirectHandlerFactory.createHomeHandler(),
      builder: (context, state) => const ProjectHomeScreen(),
    ),
    GoRoute(
      path: Routes.projectHomePc,
      builder: (context, state) => const ProjectHomeScreenPC(),
    ),
    GoRoute(
      path: Routes.edit,
      redirect: RedirectHandlerFactory.createEditHandler(),
      builder: (context, state) {
        final projectId = state.uri.queryParameters['projectId'];
        return EditScreen(projectId: projectId);
      },
    ),
    GoRoute(
      path: Routes.editMac,
      builder: (context, state) {
        final projectId = state.uri.queryParameters['projectId'];
        return MacEditScreen(projectId: projectId);
      },
    ),
    GoRoute(
      path: Routes.editWin,
      builder: (context, state) {
        final projectId = state.uri.queryParameters['projectId'];
        return WinEditScreen(projectId: projectId);
      },
    ),
    GoRoute(
      path: Routes.aigcEditing,
      builder: (context, state) {
        final projectId = state.uri.queryParameters['projectId'];
        return AigcEditingScene(projectId: projectId);
      },
    ),
    GoRoute(
      path: Routes.expired,
      builder: (context, state) => const ExpiredScreen(),
    ),
    GoRoute(
      path: Routes.h5,
      builder: (context, state) {
        final url = state.uri.queryParameters['url'];
        final title = state.uri.queryParameters['title'];
        final popRoute = state.uri.queryParameters['popRoute'];
        return H5Screen(
          url: url ?? '',
          title: title ?? '',
          popRoute: popRoute,
        );
      },
    ),
    GoRoute(
      path: Routes.aigcPresets,
      builder: (context, state) {
        final maskPath = state.uri.queryParameters['maskPath'];
        final previewPath = state.uri.queryParameters['previewPath'];
        return AigcPcPresetsScreen(
          maskPath: maskPath,
          previewPath: previewPath,
        );
      },
    ),
    GoRoute(
      path: Routes.aigcProject,
      builder: (context, state) => const AigcPcProjectScreen(),
    ),
    GoRoute(
      path: Routes.aigcSample,
      builder: (context, state) {
        final projectId = state.uri.queryParameters['projectId'];
        return AigcPcSampleScreen(projectId: projectId ?? '');
      },
    ),
    GoRoute(
      path: Routes.aigcSampleDetail,
      builder: (context, state) {
        final sampleId = state.uri.queryParameters['sampleId'];
        return AigcSampleDetailScreen(sampleId: sampleId ?? '');
      },
    ),
  ],
  observers: [routeObserver],
);

Future<String?> globalRedirect(
    BuildContext context, GoRouterState state) async {
  final expiryManager = context.read<ExpiryManager>();
  final currentUserRepository = context.read<CurrentUserRepository>();
  final globalHandler = RedirectHandlerFactory.createGlobalHandler(
      expiryManager, currentUserRepository);
  return globalHandler(context, state);
}
