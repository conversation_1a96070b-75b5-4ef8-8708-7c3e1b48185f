import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 历史记录文件管理器
/// 负责处理历史文件的写入和元数据管理
class HistoryFileManager {
  final CurrentUserRepository _currentUserRepository;
  final ProjectRepository _projectRepository;

  HistoryFileManager({
    required CurrentUserRepository currentUserRepository,
    required ProjectRepository projectRepository,
  })  : _currentUserRepository = currentUserRepository,
        _projectRepository = projectRepository;

  /// 将历史文件数据写入到指定目录
  /// 注意写入到对应的userId/projectId/fileId/history/historyId/history.json下
  /// 返回fileId到historyId的映射
  Future<Map<String, String>> writeHistoryFilesToDisk(
    Map<String, List<int>> historyFiles,
    List<FileItem> fileList,
    String? projectId,
  ) async {
    Map<String, String> fileIdToHistoryIdMap = {};

    try {
      // 获取当前用户ID
      final userId = _currentUserRepository.user?.effectiveId;
      if (userId == null || userId.isEmpty) {
        PGLog.e('无法获取用户ID，跳过历史文件写入');
        return fileIdToHistoryIdMap;
      }

      // 获取用户根目录
      final userRootDir = FileManager().getUserRootDir();
      if (userRootDir == null) {
        PGLog.e('无法获取用户根目录，跳过历史文件写入');
        return fileIdToHistoryIdMap;
      }

      // 确保项目ID存在
      if (projectId == null || projectId.isEmpty) {
        PGLog.e('项目ID为空，跳过历史文件写入');
        return fileIdToHistoryIdMap;
      }

      PGLog.d('开始写入历史文件数据: ${fileList.length} 个文件');

      int successCount = 0;
      int totalCount = 0;

      // 遍历每个文件项，找到对应的历史记录
      for (final fileItem in fileList) {
        final fileId =
            await _getFileIdFromDatabase(projectId, fileItem.originalPath);
        if (fileId == null || fileId.isEmpty) {
          PGLog.d('无法从数据库查询到文件ID: ${fileItem.originalPath}');
          continue;
        } else {
          PGLog.d('从数据库查询到文件ID: $fileId');
        }

        // 查找该文件对应的历史记录
        final historyId = fileItem.historyId;
        if (historyId == null || historyId.isEmpty || historyId == 'org') {
          // 没有历史记录或为原始文件，跳过
          PGLog.d('${fileItem.originalPath} 没有历史记录或为原始文件，跳过');
          continue;
        }

        // 检查历史文件数据中是否包含该历史ID
        if (!historyFiles.containsKey(historyId)) {
          PGLog.d('历史文件数据中未找到历史ID: $historyId');
          continue;
        }

        totalCount++;

        try {
          // 构建目录路径: userId/projectId/fileId/history/historyId/
          final historyDir = Directory(path.join(
            userRootDir.path,
            projectId,
            fileId,
            'history',
            historyId,
          ));

          // 创建目录（递归创建）
          if (!await historyDir.exists()) {
            await historyDir.create(recursive: true);
            PGLog.d('创建历史目录: ${historyDir.path}');
          }

          // 写入history.json文件
          final historyFile = File(path.join(historyDir.path, 'history.json'));
          final historyData = historyFiles[historyId]!;
          await historyFile.writeAsBytes(historyData);

          // 创建历史记录元数据文件
          await _createHistoryMetadataFile(
              userRootDir.path, projectId, fileId, historyId);

          // 添加到映射中
          fileIdToHistoryIdMap[fileId] = historyId;

          successCount++;
          PGLog.d(
              '成功写入历史文件: ${historyFile.path}, 大小: ${historyData.length} bytes, fileId: $fileId, historyId: $historyId');
        } catch (e) {
          PGLog.e('写入历史文件失败 - fileId: $fileId, historyId: $historyId, 错误: $e');
        }
      }

      PGLog.d(
          '历史文件写入完成: 成功 $successCount/$totalCount, 映射数量: ${fileIdToHistoryIdMap.length}');
    } catch (e) {
      PGLog.e('写入历史文件数据时发生错误: $e');
    }

    return fileIdToHistoryIdMap;
  }

  /// 根据项目ID和原图路径从数据库查询文件ID
  Future<String?> _getFileIdFromDatabase(
      String projectId, String orgPath) async {
    try {
      // 获取项目的所有文件
      final files = await _projectRepository.getProjectFiles(projectId);

      // 根据原图路径查找对应的文件
      for (final file in files) {
        if (file.orgPath == orgPath) {
          return file.fileId;
        }
      }

      // 如果没找到，记录警告
      PGLog.w('未找到匹配的文件: projectId=$projectId, orgPath=$orgPath');
      return null;
    } catch (e) {
      PGLog.e('查询文件ID失败: projectId=$projectId, orgPath=$orgPath, 错误: $e');
      return null;
    }
  }

  /// 创建历史记录元数据文件
  Future<void> _createHistoryMetadataFile(String userRootDir, String projectId,
      String fileId, String historyId) async {
    try {
      // 构建目录路径: userId/projectId/fileId/history/
      final historyDir = Directory(path.join(
        userRootDir,
        projectId,
        fileId,
        'history',
      ));

      // 创建目录（递归创建）
      if (!await historyDir.exists()) {
        await historyDir.create(recursive: true);
        PGLog.d('创建历史目录: ${historyDir.path}');
      }

      // 创建历史记录元数据文件路径
      final historyMetadataFile =
          File(path.join(historyDir.path, 'history.json'));

      // 当前时间格式化
      final currentTime = DateTime.now().toLocal();
      final formattedTime =
          '${currentTime.year}-${currentTime.month.toString().padLeft(2, '0')}-${currentTime.day.toString().padLeft(2, '0')} ${currentTime.hour.toString().padLeft(2, '0')}:${currentTime.minute.toString().padLeft(2, '0')}:${currentTime.second.toString().padLeft(2, '0')}';

      // 检查文件是否已存在
      if (await historyMetadataFile.exists()) {
        return;
      } else {
        // 创建新的历史记录元数据
        Map<String, dynamic> historyMetadata =
            _createNewHistoryMetadata(historyId, formattedTime);
        // 写入文件
        await historyMetadataFile.writeAsString(jsonEncode(historyMetadata));
      }

      PGLog.d('成功创建/更新历史记录元数据文件: ${historyMetadataFile.path}');
    } catch (e) {
      PGLog.e('创建历史记录元数据文件失败: $e');
    }
  }

  /// 创建新的历史记录元数据结构
  Map<String, dynamic> _createNewHistoryMetadata(
      String historyId, String formattedTime) {
    return {
      "currentActiveIndex": 0,
      "dataItems": [
        {
          "guid": historyId,
          "title": "导入",
          "changeInfo": "100",
          "modifyTime": formattedTime,
          "valueData": "",
          "isHasEdited": true,
          "isOnlyLiquefyHasEdited": false,
          "presetId": null
        },
        {
          "guid": "org",
          "title": "原图",
          "changeInfo": "",
          "modifyTime": "2025-07-01 14:16:16",
          "valueData": "",
          "isHasEdited": false,
          "isOnlyLiquefyHasEdited": false,
          "presetId": null
        }
      ]
    };
  }
}
