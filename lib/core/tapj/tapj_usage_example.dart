import 'package:turing_art/utils/pg_log.dart';

import 'n8_tapj_file_manager.dart';

/// TAPJ文件使用示例
class TapjUsageExample {
  /// 读取TAPJ文件示例
  static Future<void> readTapjFileExample(String tapjFilePath) async {
    try {
      PGLog.d('开始读取TAPJ文件: $tapjFilePath');

      // 读取TAPJ文件
      final result = await N8TapjFileManager.readN8TapjFile(tapjFilePath);

      // 输出工程信息
      PGLog.d('工程名称: ${result.projectData.workspaceName}');
      PGLog.d('导出时间: ${result.projectData.exportTime}');
      PGLog.d('文件数量: ${result.projectData.fileList.length}');
      PGLog.d('历史记录文件数量: ${result.historyFiles.length}');

      // 输出文件列表
      for (final fileInfo in result.projectData.fileList) {
        PGLog.d('文件: ${fileInfo.fileName}');
        PGLog.d('  - 原始路径: ${fileInfo.originalPath}');
        PGLog.d('  - 导出路径: ${fileInfo.exportPath}');
        PGLog.d('  - 历史ID: ${fileInfo.historyId}');
        PGLog.d('  - 是否选中: ${fileInfo.isSelected}');
      }

      // 输出历史记录文件信息
      for (final entry in result.historyFiles.entries) {
        PGLog.d('历史记录文件: ${entry.key} (${entry.value.length} 字节)');
      }
    } catch (e) {
      PGLog.d('读取TAPJ文件失败: $e');
    }
  }

  /// 提取TAPJ文件示例
  static Future<void> extractTapjFileExample(
      String tapjFilePath, String extractFolder) async {
    try {
      PGLog.d('开始提取TAPJ文件: $tapjFilePath 到 $extractFolder');

      final success =
          await N8TapjFileManager.extractTapjFile(tapjFilePath, extractFolder);

      if (success) {
        PGLog.d('TAPJ文件提取成功');
      } else {
        PGLog.d('TAPJ文件提取失败');
      }
    } catch (e) {
      PGLog.d('提取TAPJ文件时出错: $e');
    }
  }
}
