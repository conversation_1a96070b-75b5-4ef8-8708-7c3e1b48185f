# N8 TAPJ文件管理器 (Dart版本)

这是基于C#版本的N8 TAPJ文件管理器的Dart实现，用于读取和处理.tapj格式的二进制工程文件。

## 功能特性

- 读取TAPJ文件内容
- 提取TAPJ文件到指定目录
- 创建文件列表JSON
- 完全兼容C#版本的文件格式

## 文件结构

- `n8_tapj_models.dart` - 数据模型定义
- `n8_tapj_file_manager.dart` - 主要的文件管理器
- `tapj_usage_example.dart` - 使用示例
- `README.md` - 使用文档

## TAPJ文件格式

TAPJ文件采用二进制格式，结构如下：

```
[4字节] 魔数标识: "TAPJ"
[4字节] 文件版本: 1
[4字节] 工程JSON数据长度
[变长] 工程JSON数据 (UTF-8编码)
[4字节] 历史记录文件数量
对于每个历史记录文件:
  [4字节] 文件名长度
  [变长] 文件名 (UTF-8编码)
  [4字节] 文件数据长度
  [变长] 文件数据
```

## 使用方法

### 1. 读取TAPJ文件

```dart
import 'package:your_app/core/tapj/n8_tapj_file_manager.dart';

// 读取TAPJ文件
final result = await N8TapjFileManager.readN8TapjFile('path/to/project.tapj');

// 访问工程数据
print('工程名称: ${result.projectData.workspaceName}');
print('文件数量: ${result.projectData.fileList.length}');

// 访问历史记录文件
for (final entry in result.historyFiles.entries) {
  print('历史文件: ${entry.key} (${entry.value.length} 字节)');
}
```

### 2. 提取TAPJ文件

```dart
// 提取TAPJ文件到指定目录
final success = await N8TapjFileManager.extractTapjFile(
  'path/to/project.tapj',
  'path/to/extract/folder'
);

if (success) {
  print('提取成功');
} else {
  print('提取失败');
}
```

### 3. 创建文件列表JSON

```dart
// 创建文件列表JSON（兼容原有格式）
await N8TapjFileManager.createSelectedFilesJson(
  'output/folder',
  'workspace_name',
  projectData,
);
```

## 数据模型

### N8ExportProjectData
工程数据模型，包含：
- `rawWorkspaceId`: 原始工作区ID
- `exportTime`: 导出时间
- `createTime`: 创建时间
- `workspaceName`: 工作区名称
- `fileList`: 文件列表

### N8ExportFileInfo
文件信息模型，包含：
- `fileId`: 文件ID
- `fileName`: 文件名
- `originalPath`: 原始路径
- `historyId`: 历史记录ID
- `exportPath`: 导出路径
- `isSelected`: 是否选中

### N8SelectedFileInfo
选中文件信息模型，包含：
- `originalPath`: 原始路径
- `exportPath`: 导出路径
- `isSelected`: 是否选中

## 错误处理

所有方法都包含适当的错误处理：
- 文件不存在时抛出 `FileSystemException`
- 文件格式错误时抛出 `FormatException`
- 版本不支持时抛出 `FormatException`

## 注意事项

1. 该实现完全兼容C#版本的TAPJ文件格式
2. 使用小端序读取32位整数
3. 所有字符串均使用UTF-8编码
4. 历史记录文件以二进制格式存储
5. 支持生成唯一文件名以避免重名冲突

## 依赖项

- `dart:convert` - JSON和UTF-8编码支持
- `dart:io` - 文件系统操作
- `dart:typed_data` - 字节数组操作
- `package:flutter/foundation.dart` - debugPrint支持
- `package:path/path.dart` - 路径操作 