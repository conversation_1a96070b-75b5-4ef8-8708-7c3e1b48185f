import '../models/native_message.dart';

/// 消息处理器抽象基类
abstract class MessageHandler {
  MessageHandler? _nextHandler;

  /// 设置下一个处理器
  MessageHandler setNext(MessageHandler handler) {
    _nextHandler = handler;
    return handler;
  }

  /// 处理消息
  void handle(NativeMessage message) {
    if (canHandle(message)) {
      handleMessage(message);
    } else {
      _nextHandler?.handle(message);
    }
  }

  /// 判断是否可以处理该消息
  bool canHandle(NativeMessage message);

  /// 具体的消息处理逻辑
  void handleMessage(NativeMessage message);
}
