import '../../../utils/pg_log.dart';
import '../channel/channel_constants.dart';

/// 原生消息监听器
class NativeEventListener {
  static final NativeEventListener _instance = NativeEventListener._internal();
  factory NativeEventListener() => _instance;
  NativeEventListener._internal();

  bool _isInitialized = false;

  /// 初始化监听器
  void initialize() {
    if (_isInitialized) {
      return;
    }

    ChannelConstants.unityFlutterMessageChannel
        .receiveBroadcastStream()
        .listen(_onMessage, onError: _onError);

    _isInitialized = true;
    PGLog.i('Native event listener initialized');
  }

  void _onMessage(dynamic message) {
    try {
      PGLog.i('Received native message: $message');
    } catch (e, _) {
      PGLog.e('Error parsing native message $e');
    }
  }

  void _onError(dynamic error) {
    PGLog.e('Error from native message channel: $error');
  }

  /// 销毁监听器
  void dispose() {
    _isInitialized = false;
  }
}
