import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:turing_art/core/manager/device_rating_level.dart';
import 'package:turing_art/datalayer/service/share_preferences/shared_preferences_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 设备性能信息
class SystemDeviceInfo {
  // CPU 信息
  final String? processorType;
  final int? processorCount;
  final int? processorFrequency;

  // GPU 信息
  final String? graphicsDeviceName;
  final String? graphicsDeviceType;
  final String? graphicsDeviceVendor;
  final String? graphicsDeviceVersion;
  final int? graphicsMemorySize;

  // 内存信息
  final int? systemMemorySize;

  // 其他信息
  final String? deviceName;
  final String? deviceModel;
  final String? operatingSystem;
  final String? operatingSystemFamily;
  final String? deviceType;

  // Unity 相关信息
  final String? unityVersion;
  final String? platform;

  SystemDeviceInfo({
    // 新增的字段
    this.processorType,
    this.processorCount,
    this.processorFrequency,
    this.graphicsDeviceName,
    this.graphicsDeviceType,
    this.graphicsDeviceVendor,
    this.graphicsDeviceVersion,
    this.graphicsMemorySize,
    this.systemMemorySize,
    this.deviceName,
    this.deviceModel,
    this.operatingSystem,
    this.operatingSystemFamily,
    this.deviceType,
    this.unityVersion,
    this.platform,
  });

  Map<String, dynamic> toJson() {
    return {
      'processorType': processorType,
      'processorCount': processorCount,
      'processorFrequency': processorFrequency,
      'graphicsDeviceName': graphicsDeviceName,
      'graphicsDeviceType': graphicsDeviceType,
      'graphicsDeviceVendor': graphicsDeviceVendor,
      'graphicsDeviceVersion': graphicsDeviceVersion,
      'graphicsMemorySize': graphicsMemorySize,
      'systemMemorySize': systemMemorySize,
      'deviceName': deviceName,
      'deviceModel': deviceModel,
      'operatingSystem': operatingSystem,
      'operatingSystemFamily': operatingSystemFamily,
      'deviceType': deviceType,
      'unityVersion': unityVersion,
      'platform': platform,
    };
  }

  factory SystemDeviceInfo.fromJson(Map<String, dynamic> json) {
    return SystemDeviceInfo(
      processorType: json['processorType'],
      processorCount: json['processorCount'],
      processorFrequency: json['processorFrequency'],
      graphicsDeviceName: json['graphicsDeviceName'],
      graphicsDeviceType: json['graphicsDeviceType'],
      graphicsDeviceVendor: json['graphicsDeviceVendor'],
      graphicsDeviceVersion: json['graphicsDeviceVersion'],
      graphicsMemorySize: json['graphicsMemorySize'],
      systemMemorySize: json['systemMemorySize'],
      deviceName: json['deviceName'],
      deviceModel: json['deviceModel'],
      operatingSystem: json['operatingSystem'],
      operatingSystemFamily: json['operatingSystemFamily'],
      deviceType: json['deviceType'],
      unityVersion: json['unityVersion'],
      platform: json['platform'],
    );
  }
}

/// 设备评级结果
class DeviceRatingResult {
  final DeviceRatingLevel ratingLevel;
  final double score;
  final double cpuScore;
  final double memoryScore;
  final double gpuScore;
  final String ratingReason;
  final DateTime ratingTime;

  DeviceRatingResult({
    required this.ratingLevel,
    required this.score,
    required this.cpuScore,
    required this.memoryScore,
    required this.gpuScore,
    required this.ratingReason,
    required this.ratingTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'ratingLevel': ratingLevel.value,
      'score': score,
      'cpuScore': cpuScore,
      'memoryScore': memoryScore,
      'gpuScore': gpuScore,
      'ratingReason': ratingReason,
      'ratingTime': ratingTime.toIso8601String(),
    };
  }

  factory DeviceRatingResult.fromJson(Map<String, dynamic> json) {
    return DeviceRatingResult(
      ratingLevel: DeviceRatingLevel.fromValue(json['ratingLevel']),
      score: json['score'],
      cpuScore: json['cpuScore'] ?? 0.0,
      memoryScore: json['memoryScore'] ?? 0.0,
      gpuScore: json['gpuScore'] ?? 0.0,
      ratingReason: json['ratingReason'],
      ratingTime: DateTime.parse(json['ratingTime']),
    );
  }
}

/// 设备评级管理器
class DeviceRatingManager extends ChangeNotifier {
  static final DeviceRatingManager _instance = DeviceRatingManager._internal();

  // 私有构造函数，禁止外部实例化
  DeviceRatingManager._internal();

  // 单例接口
  static DeviceRatingManager get instance => _instance;

  // 消息发送状态缓存
  static bool _hasDeviceRatingMessageSent = false;

  // 静态便利方法
  static DeviceRatingLevel get currentLevel => _instance._currentRatingLevel;
  static String get currentDisplayName =>
      _instance._currentRatingLevelDisplayName;
  static String get currentRecommendedSetting =>
      _instance._currentRecommendedSetting;
  static double get currentScore => _instance._currentScore;
  static double get currentCpuScore => _instance._currentCpuScore;
  static double get currentMemoryScore => _instance._currentMemoryScore;
  static double get currentGpuScore => _instance._currentGpuScore;
  static String get currentRatingReason => _instance._ratingReason;
  static bool get isAvailable => _instance._isDeviceAvailable;
  static bool get isUnavailable => _instance._isDeviceUnavailable;
  static bool get isHighPerformance => _instance._isHighPerformance;
  static bool get isLowPerformance => _instance._isLowPerformance;
  static bool get isFirstTime => _instance._isFirstTime;
  static SystemDeviceInfo? get deviceInfo => _instance._deviceInfo;
  static DeviceRatingResult? get ratingResult => _instance._ratingResult;

  // 静态初始化方法
  static Future<void> initialize() async {
    await _instance._initialize();
  }

  // 静态评级方法
  static Future<void> recordDeviceInfo(SystemDeviceInfo deviceInfo) async {
    await _instance._recordDevicePerformanceInfo(deviceInfo);
  }

  // 静态重新评级方法
  static Future<void> reRating() async {
    await _instance._reRating();
  }

  // 静态强制重新评级方法
  static Future<void> forceReRating() async {
    await _instance._forceReRating();
  }

  // 消息发送状态管理方法
  static bool get hasDeviceRatingMessageSent => _hasDeviceRatingMessageSent;

  // 检查指定手机号用户的登录消息是否已发送
  static bool hasUserLoginRatingMessageSent(String mobile) {
    return SharedPreferencesService.getUserLoginRatingMessageSent(mobile);
  }

  static Future<void> markDeviceRatingMessageSent() async {
    _hasDeviceRatingMessageSent = true;
    await SharedPreferencesService.setDeviceRatingMessageSent(sent: true);
  }

  static Future<void> markUserLoginRatingMessageSent(String mobile) async {
    await SharedPreferencesService.setUserLoginRatingMessageSent(
        mobile: mobile, sent: true);
  }

  static Future<void> resetMessageSentStatus() async {
    _hasDeviceRatingMessageSent = false;
    await SharedPreferencesService.setDeviceRatingMessageSent(sent: false);
    // 注意: 用户登录消息状态按手机号存储，这里不重置
  }

  // 重置指定手机号的用户登录消息状态（调试用）
  static Future<void> resetUserLoginMessageStatus(String mobile) async {
    if (mobile.isNotEmpty) {
      await SharedPreferencesService.setUserLoginRatingMessageSent(
          mobile: mobile, sent: false);
    }
  }

  // 静态格式化方法
  static String getFormattedSummary() {
    return 'DeviceRating: '
        'Available=$isAvailable '
        'Level=$currentDisplayName '
        'TotalScore=${currentScore.toStringAsFixed(1)} '
        'CPU=${currentCpuScore.toStringAsFixed(1)} '
        'Memory=${currentMemoryScore.toStringAsFixed(1)} '
        'GPU=${currentGpuScore.toStringAsFixed(1)}';
  }

  static String getDetailedSummary() {
    final deviceInfo = _instance._deviceInfo;

    // 基础评级信息
    String summary = 'DeviceDetail: '
        'Available=$isAvailable '
        'Level=$currentDisplayName '
        'TotalScore=${currentScore.toStringAsFixed(1)} '
        'CPU=${currentCpuScore.toStringAsFixed(1)} '
        'Memory=${currentMemoryScore.toStringAsFixed(1)} '
        'GPU=${currentGpuScore.toStringAsFixed(1)} '
        'Reason=$currentRatingReason';

    // 添加详细设备信息
    if (deviceInfo != null) {
      summary += '\n详细设备信息: ';

      // CPU信息
      if (deviceInfo.processorType != null) {
        summary += '\nCPU: ${deviceInfo.processorType}';
      }
      if (deviceInfo.processorCount != null) {
        summary += ' (${deviceInfo.processorCount}核心)';
      }
      if (deviceInfo.processorFrequency != null) {
        summary += ' @${deviceInfo.processorFrequency}MHz';
      }

      // 内存信息
      if (deviceInfo.systemMemorySize != null) {
        final memorySizeGB = deviceInfo.systemMemorySize! / 1024.0;
        summary += '\n内存: ${memorySizeGB.toStringAsFixed(1)}GB';
      }

      // GPU信息
      if (deviceInfo.graphicsDeviceName != null) {
        summary += '\nGPU: ${deviceInfo.graphicsDeviceName}';
      }
      if (deviceInfo.graphicsDeviceVendor != null) {
        summary += ' (${deviceInfo.graphicsDeviceVendor})';
      }
      if (deviceInfo.graphicsMemorySize != null) {
        final gpuMemoryGB = deviceInfo.graphicsMemorySize! / 1024.0;
        summary += ' ${gpuMemoryGB.toStringAsFixed(1)}GB显存';
      }
      if (deviceInfo.graphicsDeviceVersion != null) {
        summary += ' 版本:${deviceInfo.graphicsDeviceVersion}';
      }

      // 操作系统信息
      if (deviceInfo.operatingSystem != null) {
        summary += '\n操作系统: ${deviceInfo.operatingSystem}';
      }
      if (deviceInfo.operatingSystemFamily != null) {
        summary += ' (${deviceInfo.operatingSystemFamily})';
      }

      // 设备信息
      if (deviceInfo.deviceName != null) {
        summary += '\n设备名称: ${deviceInfo.deviceName}';
      }
      if (deviceInfo.deviceModel != null) {
        summary += '\n设备型号: ${deviceInfo.deviceModel}';
      }
      if (deviceInfo.deviceType != null) {
        summary += '\n设备类型: ${deviceInfo.deviceType}';
      }

      // Unity相关信息
      if (deviceInfo.unityVersion != null) {
        summary += '\nUnity版本: ${deviceInfo.unityVersion}';
      }
      if (deviceInfo.platform != null) {
        summary += '\n平台: ${deviceInfo.platform}';
      }
    } else {
      summary += '\n详细设备信息: 设备信息不可用';
    }

    return summary;
  }

  SystemDeviceInfo? _deviceInfo;
  DeviceRatingResult? _ratingResult;
  bool _isFirstTime = true;
  bool _initialized = false;

  /// 初始化设备评级管理器
  Future<void> _initialize() async {
    if (_initialized) return;

    try {
      // 检查是否为首次启动
      _isFirstTime = SharedPreferencesService.getDeviceRatingFirstTime();

      // 检查是否已有评级记录
      final savedRating = SharedPreferencesService.getDeviceRating();
      if (savedRating.isNotEmpty) {
        final json = _parseJson(savedRating);
        if (json.isNotEmpty) {
          _ratingResult = DeviceRatingResult.fromJson(json);
          _isFirstTime = false;
          PGLog.i('设备评级管理器：加载已有评级结果 - ${_ratingResult?.ratingLevel}');
        }
      }

      // 检查是否已有设备信息记录
      final savedDeviceInfo =
          SharedPreferencesService.getDevicePerformanceInfo();
      if (savedDeviceInfo.isNotEmpty) {
        final json = _parseJson(savedDeviceInfo);
        if (json.isNotEmpty) {
          _deviceInfo = SystemDeviceInfo.fromJson(json);
          PGLog.i('设备评级管理器：加载已有设备信息');
        }
      }

      // 从本地存储加载消息发送状态
      _hasDeviceRatingMessageSent =
          SharedPreferencesService.getDeviceRatingMessageSent();

      _initialized = true;
      PGLog.i(
          '设备评级管理器初始化完成，首次启动: $_isFirstTime, 设备消息已发送: $_hasDeviceRatingMessageSent');
    } catch (e) {
      PGLog.e('设备评级管理器初始化失败: $e');
      _initialized = true; // 即使失败也标记为已初始化
    }
  }

  /// 记录设备性能信息（仅在第一次时记录）
  Future<void> _recordDevicePerformanceInfo(SystemDeviceInfo deviceInfo) async {
    if (!_initialized) {
      await _initialize();
    }

    // 仅在第一次时记录设备信息
    if (!_isFirstTime) {
      PGLog.d('设备评级管理器：非首次启动，跳过设备信息记录');
      return;
    }

    try {
      _deviceInfo = deviceInfo;

      // 保存设备信息到本地存储
      await SharedPreferencesService.setDevicePerformanceInfo(
          _stringify(_deviceInfo!.toJson()));

      PGLog.i(
          '设备评级管理器：设备信息已记录 - CPU核心数: ${deviceInfo.processorCount}, 内存: ${deviceInfo.systemMemorySize != null ? '${(deviceInfo.systemMemorySize! / 1073741824).toStringAsFixed(1)}GB' : '未知'}, GPU: ${deviceInfo.graphicsDeviceName}');

      // 立即进行性能评级
      await _performRating();

      notifyListeners();
    } catch (e) {
      PGLog.e('设备评级管理器：记录设备信息失败 - $e');

      // 如果记录失败，创建一个标记为不可用的评级结果
      _ratingResult = DeviceRatingResult(
        ratingLevel: DeviceRatingLevel.unavailable,
        score: 0.0,
        cpuScore: 0.0,
        memoryScore: 0.0,
        gpuScore: 0.0,
        ratingReason: '设备信息记录失败: $e',
        ratingTime: DateTime.now(),
      );

      _isFirstTime = false;
      await SharedPreferencesService.setDeviceRatingFirstTime(
        isFirstTime: false,
      );

      notifyListeners();
    }
  }

  /// 执行设备性能评级
  Future<void> _performRating() async {
    if (_deviceInfo == null) {
      PGLog.w('设备评级管理器：没有设备信息，无法进行评级');

      // 创建一个不可用的评级结果
      _ratingResult = DeviceRatingResult(
        ratingLevel: DeviceRatingLevel.unavailable,
        score: 0.0,
        cpuScore: 0.0,
        memoryScore: 0.0,
        gpuScore: 0.0,
        ratingReason: '设备信息不存在，无法进行评级',
        ratingTime: DateTime.now(),
      );

      _isFirstTime = false;
      await SharedPreferencesService.setDeviceRatingFirstTime(
        isFirstTime: false,
      );
      return;
    }

    try {
      final rating = _calculateDeviceRating(_deviceInfo!);
      _ratingResult = rating;

      // 保存评级结果到本地存储
      await SharedPreferencesService.setDeviceRating(
          _stringify(_ratingResult!.toJson()));

      _isFirstTime = false;
      await SharedPreferencesService.setDeviceRatingFirstTime(
        isFirstTime: false,
      );

      PGLog.i('设备评级管理器：性能评级完成 - ${DeviceRatingManager.getFormattedSummary()}');
    } catch (e) {
      PGLog.e('设备评级管理器：性能评级失败 - $e');

      // 如果评级失败，创建一个不可用的评级结果
      _ratingResult = DeviceRatingResult(
        ratingLevel: DeviceRatingLevel.unavailable,
        score: 0.0,
        cpuScore: 0.0,
        memoryScore: 0.0,
        gpuScore: 0.0,
        ratingReason: '性能评级过程中发生错误: $e',
        ratingTime: DateTime.now(),
      );

      _isFirstTime = false;
      await SharedPreferencesService.setDeviceRatingFirstTime(
        isFirstTime: false,
      );
    }
  }

  /// 计算设备性能评级
  DeviceRatingResult _calculateDeviceRating(SystemDeviceInfo deviceInfo) {
    double score = 0.0;
    String ratingReason = '';
    List<String> reasonDetails = [];

    // CPU评分（30%权重）
    final cpuScore = _calculateCpuScore(deviceInfo);
    score += cpuScore * 0.3;
    reasonDetails.add('CPU评分: ${cpuScore.toStringAsFixed(1)}');

    // 内存评分（35%权重）
    final memoryScore = _calculateMemoryScore(deviceInfo);
    score += memoryScore * 0.35;
    reasonDetails.add('内存评分: ${memoryScore.toStringAsFixed(1)}');

    // GPU评分（35%权重）
    final gpuScore = _calculateGpuScore(deviceInfo);
    score += gpuScore * 0.35;
    reasonDetails.add('GPU评分: ${gpuScore.toStringAsFixed(1)}');

    // 检查是否满足最低内存要求（7G = 7168M）
    bool meetsMemoryRequirement = true;
    final memorySize = deviceInfo.systemMemorySize;
    if (memorySize != null) {
      final memorySizeGB = memorySize / 1024.0; // 内存单位是M，转换为G
      if (memorySizeGB < 7.0) {
        meetsMemoryRequirement = false;
        reasonDetails.add('内存不足7GB (${memorySizeGB.toStringAsFixed(1)}GB)');
      }
    }

    // 确定性能等级
    DeviceRatingLevel ratingLevel;
    if (!meetsMemoryRequirement) {
      // 内存不足7G时，强制设为不可用，但保留评分
      ratingLevel = DeviceRatingLevel.unavailable;
      ratingReason = '内存容量不足7GB，设备不可用';
    } else if (score >= 90) {
      ratingLevel = DeviceRatingLevel.veryHigh;
      ratingReason = '设备性能极高，所有硬件指标都达到顶级水平';
    } else if (score >= 75) {
      ratingLevel = DeviceRatingLevel.high;
      ratingReason = '设备性能较高，能够处理大多数高性能任务';
    } else if (score >= 60) {
      ratingLevel = DeviceRatingLevel.medium;
      ratingReason = '设备性能中等，适合一般的处理任务';
    } else if (score >= 40) {
      ratingLevel = DeviceRatingLevel.low;
      ratingReason = '设备性能较低，建议降低画质设置';
    } else {
      ratingLevel = DeviceRatingLevel.veryLow;
      ratingReason = '设备性能极低，建议使用最低画质设置';
    }

    // 如果评分过低且内存满足要求，可能是因为信息不足
    if (score < 20 && meetsMemoryRequirement) {
      ratingLevel = DeviceRatingLevel.unavailable;
      ratingReason = '设备评分过低，可能是因为关键硬件信息缺失';
    }

    final detailedReason = '$ratingReason (${reasonDetails.join(', ')})';

    return DeviceRatingResult(
      ratingLevel: ratingLevel,
      score: score,
      cpuScore: cpuScore,
      memoryScore: memoryScore,
      gpuScore: gpuScore,
      ratingReason: detailedReason,
      ratingTime: DateTime.now(),
    );
  }

  /// 计算CPU评分
  double _calculateCpuScore(SystemDeviceInfo deviceInfo) {
    double score = 50.0; // 基础分数

    // 处理器核心数评分
    final processorCount = deviceInfo.processorCount;
    if (processorCount != null) {
      if (processorCount >= 16) {
        score += 40;
      } else if (processorCount >= 8) {
        score += 30;
      } else if (processorCount >= 4) {
        score += 20;
      } else if (processorCount >= 2) {
        score += 10;
      }
    }

    // CPU描述信息评分
    final cpuDescription = deviceInfo.processorType;
    if (cpuDescription != null) {
      final description = cpuDescription.toLowerCase();
      if (description.contains('intel')) {
        if (description.contains('i9') || description.contains('i7')) {
          score += 10;
        } else if (description.contains('i5')) {
          score += 5;
        }
      } else if (description.contains('amd')) {
        if (description.contains('ryzen 9') ||
            description.contains('ryzen 7')) {
          score += 10;
        } else if (description.contains('ryzen 5')) {
          score += 5;
        }
      }
    }

    return score.clamp(0, 100);
  }

  /// 计算内存评分
  double _calculateMemoryScore(SystemDeviceInfo deviceInfo) {
    final memorySize = deviceInfo.systemMemorySize;
    if (memorySize == null) return 50.0;

    // 内存大小评分（单位：M）
    final memorySizeGB = memorySize / 1024.0; // 内存单位是M，转换为G

    // 从不同内存级别计算评分，即使低于7G也进行评分
    if (memorySizeGB >= 32) {
      return 100.0;
    } else if (memorySizeGB >= 16) {
      return 85.0;
    } else if (memorySizeGB >= 12) {
      return 70.0;
    } else if (memorySizeGB >= 8) {
      return 60.0;
    } else if (memorySizeGB >= 7) {
      return 50.0; // 刚好达到7G的最低要求
    } else if (memorySizeGB >= 4) {
      return 30.0; // 4-7G给予较低评分
    } else if (memorySizeGB >= 2) {
      return 20.0; // 2-4G给予很低评分
    } else {
      return 10.0; // 小于2G给予最低评分
    }
  }

  /// 计算GPU评分
  double _calculateGpuScore(SystemDeviceInfo deviceInfo) {
    double score = 50.0; // 基础分数

    // GPU内存评分
    final gpuMemorySize = deviceInfo.graphicsMemorySize;
    if (gpuMemorySize != null) {
      final gpuMemoryGB = gpuMemorySize / 1024.0; // GPU内存单位是M，转换为G
      if (gpuMemoryGB >= 8) {
        score += 30;
      } else if (gpuMemoryGB >= 4) {
        score += 20;
      } else if (gpuMemoryGB >= 2) {
        score += 10;
      } else if (gpuMemoryGB >= 1) {
        score += 5; // 1-2G给予较低评分
      }
    }

    // GPU描述信息评分
    final gpuDescription = deviceInfo.graphicsDeviceName;
    if (gpuDescription != null) {
      final description = gpuDescription.toLowerCase();
      if (description.contains('nvidia')) {
        if (description.contains('rtx 40') || description.contains('rtx 30')) {
          score += 20;
        } else if (description.contains('rtx 20') ||
            description.contains('gtx 16')) {
          score += 15;
        } else if (description.contains('gtx')) {
          score += 10;
        }
      } else if (description.contains('amd')) {
        if (description.contains('rx 7') || description.contains('rx 6')) {
          score += 15;
        } else if (description.contains('rx 5')) {
          score += 10;
        }
      }
    }

    return score.clamp(0, 100);
  }

  /// 获取当前设备性能等级
  DeviceRatingLevel get _currentRatingLevel {
    return _ratingResult?.ratingLevel ?? DeviceRatingLevel.unavailable;
  }

  /// 获取当前设备性能等级的中文描述
  String get _currentRatingLevelDisplayName {
    return _currentRatingLevel.displayName;
  }

  /// 获取当前设备性能等级的推荐设置
  String get _currentRecommendedSetting {
    return _currentRatingLevel.recommendedSetting;
  }

  /// 获取当前设备性能评分
  double get _currentScore {
    return _ratingResult?.score ?? 0.0;
  }

  /// 获取评级原因
  String get _ratingReason {
    return _ratingResult?.ratingReason ?? '未进行评级';
  }

  /// 获取CPU评分
  double get _currentCpuScore {
    return _ratingResult?.cpuScore ?? 0.0;
  }

  /// 获取内存评分
  double get _currentMemoryScore {
    return _ratingResult?.memoryScore ?? 0.0;
  }

  /// 获取GPU评分
  double get _currentGpuScore {
    return _ratingResult?.gpuScore ?? 0.0;
  }

  /// 设备是否可用
  bool get _isDeviceAvailable => _currentRatingLevel.isAvailable;

  /// 设备是否不可用
  bool get _isDeviceUnavailable => _currentRatingLevel.isUnavailable;

  /// 设备是否为高性能
  bool get _isHighPerformance =>
      _currentRatingLevel.isHigherThan(DeviceRatingLevel.medium);

  /// 设备是否为低性能
  bool get _isLowPerformance =>
      _currentRatingLevel.isLowerThan(DeviceRatingLevel.medium);

  /// 重新进行评级（调试用）
  Future<void> _reRating() async {
    if (_deviceInfo != null) {
      await _performRating();
      notifyListeners();
    }
  }

  /// 强制重新评级（清除所有数据重新开始）
  Future<void> _forceReRating() async {
    try {
      _isFirstTime = true;
      _ratingResult = null;
      _deviceInfo = null;

      await SharedPreferencesService.setDeviceRatingFirstTime(
        isFirstTime: true,
      );

      PGLog.i('设备评级管理器：已重置，等待重新评级');
      notifyListeners();
    } catch (e) {
      PGLog.e('设备评级管理器：强制重新评级失败 - $e');
    }
  }

  /// 解析JSON字符串
  Map<String, dynamic> _parseJson(String jsonString) {
    try {
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      PGLog.e('解析JSON失败: $e');
      return {};
    }
  }

  /// 将对象转换为JSON字符串
  String _stringify(Map<String, dynamic> data) {
    try {
      return json.encode(data);
    } catch (e) {
      PGLog.e('转换JSON失败: $e');
      return '';
    }
  }
}
