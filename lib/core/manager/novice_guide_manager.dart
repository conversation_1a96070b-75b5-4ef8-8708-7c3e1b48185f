import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 新手引导步骤信息
class NoviceGuideStep {
  final int index;
  final String description;
  final String resourcePath;
  final String resourceId;

  NoviceGuideStep({
    required this.index,
    required this.description,
    required this.resourcePath,
    required this.resourceId,
  });

  factory NoviceGuideStep.fromJson(Map<String, dynamic> json) {
    return NoviceGuideStep(
      index: json['index'] as int,
      description: json['description'] as String,
      resourcePath: json['resourcePath'] as String,
      resourceId: json['resourceId'] as String,
    );
  }
}

/// 新手引导管理器
/// 负责控制新手引导的显示时机和流程
class NoviceGuideManager {
  final CurrentUserRepository _currentUserRepository;
  // 配置文件路径
  static const String _configPath = 'assets/novice_guid/novice_guid.json';
  // 外部使用常量，在不需要解析配置文件时，使用常量
  static const String demoProjectId = '9ed75421-e627-4fe2-a7a6-6e0c7b05a25d';
  // 外部使用常量，在不需要解析配置文件时，使用常量
  static const String demoProjectThumbnailPath =
      'assets/novice_guid/novice_guid_demo_thumbnail.jpg';

  // 新手引导步骤列表
  List<NoviceGuideStep> _steps = [];

  // 总步骤数
  int _totalSteps = 0;

  // 是否已初始化
  bool _isInitialized = false;

  // 初始化Future，避免并发初始化
  Future<void>? _initializationFuture;

  // 是否正在显示引导
  final bool _isShowingGuide = false;

  // 当前选中的工程ID
  String? _selectedProjectId;

  /// 获取是否初始化
  bool get isInitialized => _isInitialized;

  /// 获取是否正在显示引导
  bool get isShowingGuide => _isShowingGuide;

  /// 获取总步骤数
  int get totalSteps => _totalSteps;

  NoviceGuideManager({
    required CurrentUserRepository currentUserRepository,
  }) : _currentUserRepository = currentUserRepository;

  /// 确保初始化完成
  Future<void> _ensureInitialized() async {
    if (_isInitialized) {
      return;
    }

    // 如果已经有初始化任务在进行，等待其完成
    if (_initializationFuture != null) {
      await _initializationFuture!;
      return;
    }

    // 开始初始化
    _initializationFuture = _performInitialization();
    try {
      await _initializationFuture!;
      // 初始化成功后清理Future引用
      _initializationFuture = null;
    } catch (e) {
      // 初始化失败时重置Future，允许重试
      _initializationFuture = null;
      rethrow;
    }
  }

  /// 执行实际的初始化
  Future<void> _performInitialization() async {
    // 如果已完成新手引导，则不需要加载配置
    if (UserPreferencesService.getHasCompletedNoviceGuide()) {
      PGLog.d('NoviceGuideManager - 用户已完成新手引导，无需初始化');
      _isInitialized = true;
      return;
    }

    await _loadConfig();
    _isInitialized = true;
  }

  /// 加载配置文件
  Future<void> _loadConfig() async {
    try {
      final String jsonString = await rootBundle.loadString(_configPath);
      final Map<String, dynamic> config = json.decode(jsonString);

      _totalSteps = config['totalSteps'] as int;

      final List<dynamic> stepsJson = config['steps'] as List<dynamic>;
      _steps = stepsJson.map((step) => NoviceGuideStep.fromJson(step)).toList();

      PGLog.d('NoviceGuideManager - 加载配置成功，共$_totalSteps步');
    } catch (e) {
      PGLog.e('NoviceGuideManager - 加载配置失败: $e');
      // 配置加载失败时使用默认值
      _totalSteps = 2;
      _steps = [
        NoviceGuideStep(
          index: 0,
          description: '双击进入演示项目，快速体验精修功能！',
          resourcePath: 'assets/novice_guid/novice_guid_demo.jpg',
          resourceId: '9ed75421-e627-4fe2-a7a6-6e0c7b05a25d',
        ),
        NoviceGuideStep(
          index: 1,
          description: '点击该预设，体验精修效果！',
          resourcePath: '',
          resourceId: 'ec640114-f269-4b6e-90cc-958c0066a287',
        ),
      ];
    }
  }

  static bool isDemoProject(String projectId) {
    return projectId == NoviceGuideManager.demoProjectId;
  }

  /// 根据步骤获取描述文案
  String getDescription(int step) {
    if (step < 0 || step >= _steps.length) {
      return '';
    }
    return _steps[step].description;
  }

  /// 根据步骤获取资源ID
  String getResourceId(int step) {
    if (step < 0 || step >= _steps.length) {
      return '';
    }
    return _steps[step].resourceId;
  }

  /// 根据步骤获取资源路径
  String getResourcePath(int step) {
    if (step < 0 || step >= _steps.length) {
      return '';
    }
    return _steps[step].resourcePath;
  }

  // 添加新方法，标记步骤已显示
  void markStepShown(int step) {
    if (step >= 0 && step < _totalSteps) {
      UserPreferencesService.setHasShownNoviceGuide(step: step);
    }
  }

  // 添加新方法，标记引导已完成
  void markGuideCompleted() {
    UserPreferencesService.setHasCompletedNoviceGuide();
  }

  // 获取引导步骤的完整信息
  Future<NoviceGuideInfo?> getGuideInfo({
    required int step,
  }) async {
    // 如果已完成新手引导或者完成demo项目步骤，则不需要显示
    if (!await _shouldHandleStep(step)) {
      return null;
    }
    // 确保初始化完成
    await _ensureInitialized();
    return NoviceGuideInfo(
      step: step,
      totalSteps: _totalSteps,
      description: getDescription(step),
      resourcePath: getResourcePath(step),
      resourceId: getResourceId(step),
    );
  }

  /// 检查特定步骤是否需要处理
  /// 返回true表示需要处理该步骤
  Future<bool> _shouldHandleStep(int step) async {
    // 如果已完成整个新手引导，则不需要处理任何步骤
    if (UserPreferencesService.getHasCompletedNoviceGuide()) {
      return false;
    }
    // 没有显示过，则需要确保初始化完成
    await _ensureInitialized();

    // 检查步骤是否有效
    if (step < 0 || step >= _totalSteps) {
      return false;
    }

    // 检查该步骤是否已经完成
    final bool hasShown =
        UserPreferencesService.getHasShownNoviceGuide(step: step);

    return !hasShown;
  }
}

/// Demo项目相关扩展
extension DemoProjectNoviceGuideManager on NoviceGuideManager {
  /// 获取演示项目ID
  /// 直接返回配置中的resourceId
  String getDemoProjectId() {
    if (_steps.isEmpty) {
      return '';
    }

    return getResourceId(0);
  }

  /// 检查并返回是否需要创建演示项目
  Future<bool> shouldCreateDemoProject(List<String> projectIds) async {
    // 是否需要处理demo项目步骤，则不需要创建演示项目
    if (!await shouldHandleDemoProjectStep()) {
      return false;
    }

    // 确保初始化完成
    await _ensureInitialized();

    // 检查是否已经存在演示项目
    final String demoProjectId = getDemoProjectId();
    if (projectIds.contains(demoProjectId)) {
      return false;
    }
    return true;
  }

  /// 检查是否需要处理demo项目相关步骤（第0步）
  Future<bool> shouldHandleDemoProjectStep() async {
    // 对于第一步，还需要检查新人福利是否已显示
    final bool userBenefitsShown =
        UserPreferencesService.getHasShowNewUserGift();
    if (!userBenefitsShown) {
      // 新人福利未显示，则不处理demo项目步骤（以免显示在新人福利弹窗之前）
      return false;
    } else {
      // 新人福利已显示，不是第一次登录，有可能是升级上来的用户也不显示
      final bool isFirstLogin =
          _currentUserRepository.user?.firstLogin ?? false;
      if (!isFirstLogin) {
        return false;
      }
    }
    return await _shouldHandleStep(0);
  }
}

/// 预设相关扩展
extension PresetNoviceGuideManager on NoviceGuideManager {
  /// 检查是否需要处理预设选择步骤（第1步）
  /// 预设选择步骤，需要检查是否是当前工程
  Future<bool> shouldHandlePresetStep() async {
    if (_selectedProjectId == null ||
        _selectedProjectId != NoviceGuideManager.demoProjectId) {
      PGLog.d(
          'NoviceGuideManager - 预设选择步骤，当前工程ID: $_selectedProjectId, 预设引导针对工程ID: ${NoviceGuideManager.demoProjectId}');
      return false;
    }
    return await _shouldHandleStep(1);
  }

  /// 设置当前选中的工程ID(方便预设选择步骤检查是否是当前工程)
  void setSelectedProjectId(String projectId) {
    _selectedProjectId = projectId;
  }
}

/// 新手引导信息类
class NoviceGuideInfo {
  final int step;
  final int totalSteps;
  final String description;
  final String resourcePath;
  final String resourceId;

  NoviceGuideInfo({
    required this.step,
    required this.totalSteps,
    required this.description,
    required this.resourcePath,
    required this.resourceId,
  });
}
