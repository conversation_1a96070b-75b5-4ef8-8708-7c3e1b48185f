import 'dart:async';
import 'dart:collection';

import 'package:synchronized/synchronized.dart';

/// 历史记录节点 - 直接继承LinkedListEntry
final class HistoryNode<T> extends LinkedListEntry<HistoryNode<T>> {
  final String id;
  final T data;

  HistoryNode({
    required this.id,
    required this.data,
  });

  @override
  String toString() {
    return 'HistoryNode{id: $id, data: $data}';
  }
}

/// 历史记录栈 - 使用undo和redo两个栈实现
class HistoryStack<T> {
  // Undo栈：存储可以撤销的操作
  final LinkedList<HistoryNode<T>> _undoStack = LinkedList<HistoryNode<T>>();

  // Redo栈：存储可以重做的操作
  final LinkedList<HistoryNode<T>> _redoStack = LinkedList<HistoryNode<T>>();

  HistoryStack();

  /// 获取当前节点（undo栈的顶部节点）
  HistoryNode<T>? get current => _undoStack.isNotEmpty ? _undoStack.last : null;

  /// 获取头节点（undo栈的底部节点）
  HistoryNode<T>? get head => _undoStack.isNotEmpty ? _undoStack.first : null;

  /// 获取尾节点（undo栈的顶部节点）
  HistoryNode<T>? get tail => _undoStack.isNotEmpty ? _undoStack.last : null;

  /// 检查是否可以前进（redo栈是否有内容）
  bool get canForward => _redoStack.isNotEmpty;

  /// 检查是否可以后退（undo栈是否有内容）
  bool get canBackward => _undoStack.isNotEmpty;

  /// 检查是否存在历史记录
  bool get hasHistory => _undoStack.isNotEmpty || _redoStack.isNotEmpty;

  /// 添加新节点
  void addNode(HistoryNode<T> newNode) {
    _undoStack.add(newNode);
    // 添加新节点时，清空redo栈（因为新的操作会改变历史）
    _redoStack.clear();
  }

  /// 前进到下一个节点（redo操作）
  HistoryNode<T>? goForward() {
    if (_redoStack.isNotEmpty) {
      final node = _redoStack.last;
      _redoStack.remove(node);
      _undoStack.add(node);
      return node;
    }
    return null;
  }

  /// 后退到上一个节点（undo操作）
  HistoryNode<T>? goBackward() {
    if (_undoStack.isNotEmpty) {
      final node = _undoStack.last;
      _undoStack.remove(node);
      _redoStack.add(node);
      return _undoStack.isNotEmpty ? _undoStack.last : null;
    }
    return null;
  }

  /// 跳转到指定节点
  HistoryNode<T>? goToNode(String nodeId) {
    // 在undo栈中查找
    for (final node in _undoStack) {
      if (node.id == nodeId) {
        // 找到节点后，需要将undo栈中该节点之后的所有节点移到redo栈
        _moveNodesToRedoAfter(node);
        return node;
      }
    }

    // 在redo栈中查找
    for (final node in _redoStack) {
      if (node.id == nodeId) {
        // 找到节点后，需要将redo栈中该节点之前的所有节点移到undo栈
        _moveNodesToUndoBefore(node);
        return node;
      }
    }

    return null;
  }

  /// 获取所有节点列表（只返回undo栈中的节点）
  List<HistoryNode<T>> getAllUndoRecords() {
    return _undoStack.toList();
  }

  /// 获取所有节点列表（只返回redo栈中的节点）
  List<HistoryNode<T>> getAllRedoRecords() {
    return _redoStack.toList();
  }

  /// 清空历史记录
  void clear() {
    _undoStack.clear();
    _redoStack.clear();
  }

  /// 将undo栈中指定节点之后的所有节点移到redo栈
  void _moveNodesToRedoAfter(HistoryNode<T> targetNode) {
    final nodesToMove = <HistoryNode<T>>[];
    bool found = false;

    while (_undoStack.isNotEmpty) {
      final node = _undoStack.last;
      _undoStack.remove(node);
      if (found) {
        nodesToMove.add(node);
      } else if (node == targetNode) {
        found = true;
        _undoStack.add(node); // 将目标节点放回undo栈
        break;
      }
    }

    // 将节点按正确顺序添加到redo栈
    for (final node in nodesToMove.reversed) {
      _redoStack.add(node);
    }
  }

  /// 将redo栈中指定节点之前的所有节点移到undo栈
  void _moveNodesToUndoBefore(HistoryNode<T> targetNode) {
    final nodesToMove = <HistoryNode<T>>[];
    bool found = false;

    while (_redoStack.isNotEmpty) {
      final node = _redoStack.last;
      _redoStack.remove(node);
      if (node == targetNode) {
        found = true;
        _undoStack.add(node); // 将目标节点添加到undo栈
        break;
      } else if (found) {
        nodesToMove.add(node);
      }
    }

    // 将节点按正确顺序添加到undo栈
    for (final node in nodesToMove.reversed) {
      _undoStack.add(node);
    }
  }
}

/// 通用历史记录管理器
class HistoryRecordManager<T> {
  // 线程安全锁
  final Lock _lock = Lock();

  // 当前活动的历史记录
  final _currentHistory = HistoryStack<T>();

  /// 获取当前历史记录
  HistoryStack<T>? get currentHistory => _currentHistory;

  /// 添加新的历史记录节点
  Future<void> addHistoryNode({
    required T data,
  }) async {
    await _lock.synchronized(() async {
      // 创建新节点
      final node = HistoryNode<T>(
        id: _generateNodeId(),
        data: data,
      );

      // 添加到历史栈
      _currentHistory.addNode(node);
    });
  }

  /// 前进到下一个历史记录（redo）
  Future<T?> goForward() async {
    return await _lock.synchronized(() async {
      if (_currentHistory.canForward == true) {
        final node = _currentHistory.goForward();
        return node?.data;
      }
      return null;
    });
  }

  /// 后退到上一个历史记录（undo）
  Future<T?> goBackward() async {
    return await _lock.synchronized(() async {
      if (_currentHistory.canBackward == true) {
        final node = _currentHistory.goBackward();
        return node?.data;
      }
      return null;
    });
  }

  /// 跳转到指定节点
  Future<T?> goToNode(String nodeId) async {
    return await _lock.synchronized(() async {
      final node = _currentHistory.goToNode(nodeId);
      return node?.data;
    });
  }

  /// 获取当前节点
  HistoryNode<T>? getCurrentNode() {
    return _currentHistory.current;
  }

  /// 获取所有可undo节点
  List<HistoryNode<T>> getAllUndoRecords() {
    return _currentHistory.getAllUndoRecords();
  }

  /// 获取所有可redo节点
  List<HistoryNode<T>> getAllRedoRecords() {
    return _currentHistory.getAllRedoRecords();
  }

  /// 检查是否可以前进
  bool get canForward => _currentHistory.canForward;

  /// 检查是否可以后退
  bool get canBackward => _currentHistory.canBackward;

  /// 检查是否存在历史记录
  bool get hasHistory => _currentHistory.hasHistory;

  /// 清空历史记录
  Future<void> clearHistory() async {
    await _lock.synchronized(() async {
      _currentHistory.clear();
    });
  }

  /// 生成节点ID
  String _generateNodeId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
