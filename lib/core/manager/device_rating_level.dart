/// 设备评级等级枚举
enum DeviceRatingLevel {
  unavailable(-1), // 不可用
  veryLow(0), // 性能极低
  low(1), // 性能较低
  medium(2), // 性能中等
  high(3), // 性能较高
  veryHigh(4); // 性能极高

  final int value;
  const DeviceRatingLevel(this.value);

  // 从数值获取枚举值
  static DeviceRatingLevel fromValue(int value) {
    return DeviceRatingLevel.values.firstWhere(
      (level) => level.value == value,
      orElse: () => DeviceRatingLevel.unavailable,
    );
  }

  // 判断性能是否高于指定等级
  bool isHigherThan(DeviceRatingLevel other) {
    return value > other.value;
  }

  // 判断性能是否低于指定等级
  bool isLowerThan(DeviceRatingLevel other) {
    return value < other.value;
  }

  // 判断是否为可用等级
  bool get isAvailable => this != DeviceRatingLevel.unavailable;

  // 判断是否为不可用等级
  bool get isUnavailable => this == DeviceRatingLevel.unavailable;

  // 获取所有可用的性能等级值
  static List<int> get allValues => values.map((e) => e.value).toList();

  // 获取所有可用的性能等级（排除不可用等级）
  static List<DeviceRatingLevel> get availableLevels =>
      values.where((level) => level.isAvailable).toList();

  // 获取最小性能等级（不包括不可用）
  static DeviceRatingLevel get minAvailable =>
      availableLevels.reduce((a, b) => a.value < b.value ? a : b);

  // 获取最大性能等级
  static DeviceRatingLevel get max =>
      values.reduce((a, b) => a.value > b.value ? a : b);

  // 转换为JSON
  int toJson() => value;

  // 获取中文描述
  String get displayName {
    switch (this) {
      case DeviceRatingLevel.unavailable:
        return '不可用';
      case DeviceRatingLevel.veryLow:
        return '性能极低';
      case DeviceRatingLevel.low:
        return '性能较低';
      case DeviceRatingLevel.medium:
        return '性能中等';
      case DeviceRatingLevel.high:
        return '性能较高';
      case DeviceRatingLevel.veryHigh:
        return '性能极高';
    }
  }

  // 获取推荐设置描述
  String get recommendedSetting {
    switch (this) {
      case DeviceRatingLevel.unavailable:
        return '设备不可用或信息不足';
      case DeviceRatingLevel.veryLow:
        return '建议使用最低画质设置';
      case DeviceRatingLevel.low:
        return '建议使用较低画质设置';
      case DeviceRatingLevel.medium:
        return '建议使用中等画质设置';
      case DeviceRatingLevel.high:
        return '建议使用较高画质设置';
      case DeviceRatingLevel.veryHigh:
        return '可使用最高画质设置';
    }
  }

  @override
  String toString() => 'DeviceRatingLevel.$name($value)';
}
