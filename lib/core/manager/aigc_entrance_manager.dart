import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';

class AigcEntranceManager {
  final OpsCustomTableRepository _opsCustomTableRepository;
  final CurrentUserRepository _currentUserRepository;

  AigcEntranceManager(
      {required OpsCustomTableRepository opsCustomTableRepository,
      required CurrentUserRepository currentUserRepository})
      : _opsCustomTableRepository = opsCustomTableRepository,
        _currentUserRepository = currentUserRepository;

  /// 判断当前用户是否是AIGC用户
  Future<bool> isAigcUser() async {
    // Debug模式下直接返回true
    if (kDebugMode) {
      return true;
    }

    final phoneNumbers =
        await _opsCustomTableRepository.getAigcUserPhoneNumbers();
    final currentUser = _currentUserRepository.user;
    if (currentUser == null) {
      return false;
    }
    return phoneNumbers.contains(currentUser.mobile);
  }
}
