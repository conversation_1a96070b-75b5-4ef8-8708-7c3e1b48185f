import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../../config/env_config.dart';
import '../../datalayer/domain/models/server_time/server_time.dart';
import '../../datalayer/service/api/header_tool.dart';
import '../../datalayer/service/api/request_header.dart';
import '../../datalayer/service/share_preferences/shared_preferences_service.dart';
import '../../datalayer/service/share_preferences/user_preferences_service.dart';
import '../../ops/repository/ops_custom_table_repository.dart';

class ExpiryManager {
  final ApiClient _apiClient;
  final OpsCustomTableRepository _customTableRepository;
  DateTime? _expiryDate;

  ExpiryManager({
    required ApiClient apiClient,
    required OpsCustomTableRepository customTableRepository,
  })  : _apiClient = apiClient,
        _customTableRepository = customTableRepository;

  Future<void> checkAndSetFirstLaunch() async {
    try {
      final firstLaunchTime = SharedPreferencesService.getFirstLaunchTime();

      if (firstLaunchTime == '') {
        // 首次启动，记录时间
        SharedPreferencesService.setFirstLaunchTime(
            DateTime.now().millisecondsSinceEpoch.toString());

        // 同时记录首次安装版本，使用当前应用版本，方便以后有只针对新版本做处理，升级上来的用户不处理的情况
        final currentVersion = EnvConfig.version;
        UserPreferencesService.setFirstInstallVersion(currentVersion);
        PGLog.i('ExpiryManager - 首次启动，记录首次安装版本: $currentVersion');
      }
      // _expiryDate = await getExpiryDate();
    } catch (e) {
      PGLog.e('Check first launch failed: $e');
    }
  }

  Future<DateTime?> getExpiryDate() async {
    PGLog.i('开始 getExpiryDate');
    try {
      // 1. 从ops数据管理仓库取配置信息
      final appConfig =
          await _customTableRepository.getCustomTable(code: 'AppConfig');

      // 2. 检查配置是否有效
      if (appConfig == null ||
          appConfig.betaConfig == null ||
          appConfig.betaConfig?.expiredTime == null) {
        // 3. 如果服务器配置无效，尝试使用本地缓存的过期时间
        final localExpiryTime = SharedPreferencesService.getBetaExpiryTime();
        if (localExpiryTime != '') {
          PGLog.i('使用本地缓存的过期时间 $localExpiryTime');
          return DateTime.parse(localExpiryTime);
        }
        PGLog.i('检查配置 本地缓存的过期时间无效,返回空');
        return null;
      }

      // 4. 使用服务器配置
      final String opsTime = appConfig.betaConfig!.expiredTime!;
      if (opsTime.isEmpty) {
        PGLog.i('服务器配置 服务器配置的过期时间无效,返回空');
        return null;
      }

      // 5. 缓存到本地
      SharedPreferencesService.setBetaExpiryTime(opsTime);
      final expiredTime = DateTime.parse(opsTime);
      AppInfo.setExpiredTime(expiredTime);
      PGLog.i('服务器配置 获取到过期时间 $expiredTime');
      return expiredTime;
    } catch (e) {
      PGLog.e('Get expiry date failed: $e');
      // 6. 发生错误时，尝试使用本地缓存
      final localExpiryTime = SharedPreferencesService.getBetaExpiryTime();
      if (localExpiryTime != '') {
        final expiredTime = DateTime.parse(localExpiryTime);
        PGLog.i('使用本地缓存的过期时间 $expiredTime');
        return expiredTime;
      }
      PGLog.i('使用本地缓存的过期时间无效');
      return null;
    }
  }

  Future<bool> isExpired() async {
    PGLog.i('开始 isExpired');
    try {
      final serverTime = await _getServerTime();

      // 如果过期时间不存在，再获取一次
      if (_expiryDate == null) {
        PGLog.i('过期时间不存在，再获取一次');
        _expiryDate = await getExpiryDate();
        if (_expiryDate == null) {
          PGLog.i('过期时间再次获取仍然不存在，使用本地缓存');
          // 如果仍然获取失败，使用本地缓存
          final localExpiryTime = SharedPreferencesService.getBetaExpiryTime();
          // 如果本地缓存也不存在
          if (localExpiryTime == '') {
            PGLog.i('本地时间不存在，认为已经过期');
            return true; // 没有任何过期时间信息，认为已过期
          }
          final localExpiryDate = DateTime.parse(localExpiryTime);
          PGLog.i('本地时间存在，和服务器比较 结果= ${serverTime.isAfter(localExpiryDate)}');
          return serverTime.isAfter(localExpiryDate);
        }
      }
      PGLog.i('过期时间存在，和服务器比较 结果= ${serverTime.isAfter(_expiryDate!)}');
      return serverTime.isAfter(_expiryDate!);
    } catch (e) {
      PGLog.i('获取服务器时间失败, 默认返回不过期');
      PGLog.e('Check expiry failed: $e');
      return false; // 出错时默认不过期，保证用户可以使用
    }
  }

  Future<DateTime> _getServerTime() async {
    final headers = RequestHeader.getSignedHeaders(
      '/v1/server-time',
      HttpMethod.get,
      {},
      forBody: false,
    );
    final response = await _apiClient.get<dynamic>(
      '/v1/server-time',
      headers: headers,
    );
    if (response.success) {
      final serverTime =
          ServerTime.fromJson(response.data as Map<String, dynamic>);
      // 将 ServerTime 转换为 DateTime
      return DateTime.parse(serverTime.time);
    }
    // 如果获取失败，返回本地时间
    return DateTime.now();
  }

  Future<Map<String, dynamic>> _getCustomTableData(String tableName) async {
    final data = {'code': 'AppConfig'};
    final headers = RequestHeader.getSignedHeaders(
      '/v1/json-config-show',
      HttpMethod.get,
      data,
      forBody: false,
    );
    final response = await _apiClient.get<dynamic>(
      '/v1/json-config-show',
      headers: headers,
      queryParameters: data,
    );
    if (response.success) {
      final config = response.data as Map<String, dynamic>;
      return config[tableName] as Map<String, dynamic>;
    }
    return {};
  }
}
