import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';

/// 导出路径信息
class ExportPathInfo {
  final String projectId; // 项目ID
  final String projectName; // 项目名称
  final String proofingId; // 打样项目ID，用于组装businessTaskId
  final String sampleName; // 打样项目名字（原图名称）
  final String presetName; // 预设名称
  final String effectCode; // 效果ID

  ExportPathInfo({
    required this.projectId,
    required this.projectName,
    required this.proofingId,
    required this.sampleName,
    required this.presetName,
    required this.effectCode,
  });

  /// 从导出模型创建路径信息（用于导出列表）
  factory ExportPathInfo.fromExportModel(
    AigcSampleExportProjectModel exportProjectModel,
    String proofingId,
    String effectCode,
  ) {
    final profingModel = exportProjectModel.proofings
        .where((element) => element.id == proofingId)
        .first;
    return ExportPathInfo(
      projectId: exportProjectModel.projectId,
      projectName: exportProjectModel.projectName,
      proofingId: proofingId,
      sampleName: profingModel.originPhotoName,
      presetName: profingModel.presetName,
      effectCode: effectCode,
    );
  }

  /// 从样本模型创建路径信息（用于样本详情）
  factory ExportPathInfo.fromSampleModel(
    AigcSampleModel sampleModel,
    String effectCode,
  ) {
    return ExportPathInfo(
      projectId: sampleModel.projectId,
      projectName: sampleModel.projectName,
      proofingId: sampleModel.id,
      sampleName: sampleModel.name,
      presetName: sampleModel.presetName,
      effectCode: effectCode,
    );
  }

  /// 获取文件名（不含路径）
  String get fileName {
    final sampleNameClean = _cleanFileName(sampleName);
    final presetNameClean = _cleanFileName(presetName);

    if (presetNameClean.isEmpty) {
      return '${sampleNameClean}_$effectCode.jpg';
    }
    return '${sampleNameClean}_${presetNameClean}_$effectCode.jpg';
  }

  /// 获取项目文件夹名
  String get projectFolderName {
    final projectNameClean = _cleanFileName(projectName);
    if (projectNameClean.isEmpty) {
      return projectId;
    }
    return '${projectNameClean}_$projectId';
  }

  /// 清理文件/文件夹名，移除不合法字符
  static String _cleanName(String name, {required int maxLength}) {
    if (name.isEmpty) {
      return '';
    }

    // 使用正则表达式一次性替换所有不合法字符，保留字母、数字、中文字符、空格和下划线
    String cleanName = name.replaceAll(RegExp(r'[^\w\u4e00-\u9fa5 _]'), '_');

    // 合并连续的下划线为单个下划线
    cleanName = cleanName.replaceAll(RegExp(r'_+'), '_');

    // 移除首尾的下划线和空格
    cleanName = cleanName.trim().replaceAll(RegExp(r'^_+|_+$'), '');

    // 限制长度
    if (cleanName.length > maxLength) {
      cleanName = cleanName.substring(0, maxLength);
      // 确保截断后不以下划线结尾
      cleanName = cleanName.replaceAll(RegExp(r'_+$'), '');
    }

    return cleanName;
  }

  /// 获取项目文件夹名
  static String getProjectFolderName(String projectName, String projectId) {
    final projectNameClean = _cleanName(projectName, maxLength: 100);

    if (projectNameClean.isEmpty) {
      return projectId;
    }
    return '${projectNameClean}_$projectId';
  }

  /// 获取第一级文件夹名
  static String get firstFolderName {
    return "AIGC场景增强";
  }

  /// 清理文件名，移除不合法字符
  String _cleanFileName(String name) {
    return _cleanName(name, maxLength: 100);
  }
}
