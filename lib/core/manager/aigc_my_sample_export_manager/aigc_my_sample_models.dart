import 'package:turing_art/datalayer/domain/enums/export_path_type.dart';
import 'package:turing_art/utils/download/download_task.dart';

class AigcMySampleExportUIModel {
  final String id; // 项目ID
  final String name; // 项目名字
  final String exportNum; // 已导出/总张数
  final AigcMySampleExportProgressStatus progressStatus; // 导出进度状态
  final DateTime updateTime; // 最近导出更新时间

  AigcMySampleExportUIModel({
    required this.id,
    required this.name,
    required this.exportNum,
    required this.progressStatus,
    required this.updateTime,
  });
}

/// 我的当前设备AIGC样本导出效果UI模型
class AigcMySampleExportEffectUIModel {
  final String effectCode; // 效果代码，用于唯一标识
  final String status;
  final String? exportPhotoUrl;

  AigcMySampleExportEffectUIModel({
    required this.effectCode,
    required this.status,
    this.exportPhotoUrl,
  });
}

/// 我的当前设备AIGC样本导出进度状态
enum AigcMySampleExportProgressStatus {
  processing, // AI超清处理中
  completed, // 已完成
  failed, // 失败
}

/// 下载队列项目
class DownloadQueueItem {
  final String proofingId;
  final String effectCode; // 使用effectCode标识具体效果
  final String exportPhotoUrl;
  final String targetFilePath;

  DownloadQueueItem({
    required this.proofingId,
    required this.effectCode,
    required this.exportPhotoUrl,
    required this.targetFilePath,
  });
}

/// 检查下载队列结果
class CheckDownloadQueueResult {
  final bool needDownload;
  final bool isInTaskQueue;
  final bool isOtherAuthor;
  final List<DownloadTask>? needDownLoadTasks;

  CheckDownloadQueueResult({
    required this.needDownload,
    required this.isInTaskQueue,
    required this.isOtherAuthor,
    this.needDownLoadTasks,
  });
}

/// 调色队列项目
class ColorGradingQueueItem {
  final String proofingId;
  final String effectCode;
  final String inputFilePath;
  final String outputFilePath;

  ColorGradingQueueItem({
    required this.proofingId,
    required this.effectCode,
    required this.inputFilePath,
    required this.outputFilePath,
  });
}

class CheckPathResult {
  ExportPathType pathType; // 导出路径类型
  String resultPath; // 导出路径,预期导出路径不存在时，使用桌面路径作为basePath
  bool isExpectedPathExists; // 预期导出路径是否存在

  CheckPathResult({
    required this.pathType,
    this.resultPath = '',
    this.isExpectedPathExists = true,
  });
}
