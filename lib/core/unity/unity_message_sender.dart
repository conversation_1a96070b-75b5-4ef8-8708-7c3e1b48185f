import 'package:flutter/services.dart';

import '../../../utils/pg_log.dart';
import '../platform/channel/channel_constants.dart';

/// Unity消息发送抽象接口
abstract class UnityMessageSender {
  Future<void> sendMessage(String message);
}

/// Unity消息发送实现
class UnityMessageSenderImpl implements UnityMessageSender {
  @override
  Future<void> sendMessage(String message) async {
    try {
      await ChannelConstants.flutterUnityMessageChannel
          .invokeMethod('sendMessage', {'message': message});
    } on PlatformException catch (e) {
      PGLog.w("Failed to send message to Unity: '${e.message}'.");
    }
  }
}
