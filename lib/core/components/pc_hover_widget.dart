import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

class PcHoverWidget extends StatefulWidget {
  final Widget Function(BuildContext context, bool isHovered) builder;

  const PcHoverWidget({
    super.key,
    required this.builder,
  });

  @override
  State<PcHoverWidget> createState() => _PcHoverWidgetState();
}

class _PcHoverWidgetState extends State<PcHoverWidget> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: widget.builder(context, isHovered),
    );
  }
}
