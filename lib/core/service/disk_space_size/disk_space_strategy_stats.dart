import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 磁盘空间策略执行统计
class DiskSpaceStrategyStats {
  static const String _tagCategory = 'disk_space_strategy';
  static const String _tagDrive = 'drive';
  static const String _tagStrategy = 'strategy';
  static const String _tagSuccess = 'success';
  static const String _tagError = 'error';
  static const String _tagExecutionTime = 'execution_time_ms';
  static const String _tagResultSize = 'result_size_gb';
  static const String _tagCategory2 = 'get_disk_space';

  static void recordAttemptedGetDiskSpace({
    required int executionTimeMs,
  }) {
    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: '执行获取磁盘空间',
          category: _tagCategory2,
          data: {
            _tagExecutionTime: executionTimeMs,
          },
          level: SentryLevel.info,
        ),
      );

      Sentry.captureMessage(
        '执行获取磁盘空间',
        level: SentryLevel.info,
        withScope: (scope) {
          scope.setTag(_tagExecutionTime, executionTimeMs.toString());
        },
      );
    } catch (e) {
      PGLog.e('Sentry上报失败: $e');
    }
  }

  /// 记录策略执行结果
  static void recordStrategyExecution({
    required String drive,
    required String strategyName,
    required bool success,
    required int executionTimeMs,
    double? resultSizeBytes,
    String? errorMessage,
  }) {
    try {
      // 记录到本地日志
      if (success) {
        final resultSizeGb = resultSizeBytes != null
            ? (resultSizeBytes / 1024 / 1024 / 1024).toStringAsFixed(2)
            : 'unknown';
        PGLog.i(
            '📊 策略统计: $strategyName 成功获取 $drive 盘空间: ${resultSizeGb}GB, 耗时: ${executionTimeMs}ms');
      } else {
        PGLog.w(
            '📊 策略统计: $strategyName 获取 $drive 盘空间失败: $errorMessage, 耗时: ${executionTimeMs}ms');
      }

      // 上报到Sentry
      _reportToSentry(
        drive: drive,
        strategyName: strategyName,
        executionTimeMs: executionTimeMs,
        isSuccess: success,
        errorMessage: errorMessage,
      );
    } catch (e) {
      PGLog.e('策略统计记录失败: $e');
    }
  }

  /// 记录策略链执行完成
  static void recordStrategyChainComplete({
    required String drive,
    required List<String> attemptedStrategies,
    required String? successfulStrategy,
    required int totalExecutionTimeMs,
    double? finalResultSizeBytes,
  }) {
    try {
      final resultSizeGb = finalResultSizeBytes != null
          ? (finalResultSizeBytes / 1024 / 1024 / 1024).toStringAsFixed(2)
          : 'unknown';

      if (successfulStrategy != null) {
        PGLog.i(
            '📊 策略链完成: $drive 盘空间获取成功, 成功策略: $successfulStrategy, 最终结果: ${resultSizeGb}GB, 总耗时: ${totalExecutionTimeMs}ms, 尝试策略: ${attemptedStrategies.join(', ')}');
      } else {
        PGLog.e(
            '📊 策略链完成: $drive 盘空间获取失败, 尝试策略: ${attemptedStrategies.join(', ')}, 总耗时: ${totalExecutionTimeMs}ms');
      }

      // 只对失败的情况上报策略链结果到Sentry
      if (successfulStrategy == null) {
        _reportStrategyChainToSentry(
          drive: drive,
          attemptedStrategies: attemptedStrategies,
          totalExecutionTimeMs: totalExecutionTimeMs,
          finalResultSizeBytes: finalResultSizeBytes,
        );
      }
    } catch (e) {
      PGLog.e('策略链统计记录失败: $e');
    }
  }

  /// 记录返回无限大的情况（所有策略都失败）
  static void recordInfiniteResult({
    required String drive,
    required List<String> attemptedStrategies,
    required int totalExecutionTimeMs,
  }) {
    try {
      PGLog.e(
          '📊 策略链失败: $drive 盘空间获取失败，返回无限大值，尝试策略: ${attemptedStrategies.join(', ')}, 总耗时: ${totalExecutionTimeMs}ms');

      // 上报返回无限大的情况到Sentry
      _reportInfiniteResultToSentry(
        drive: drive,
        attemptedStrategies: attemptedStrategies,
        totalExecutionTimeMs: totalExecutionTimeMs,
      );
    } catch (e) {
      PGLog.e('无限大结果统计记录失败: $e');
    }
  }

  /// 上报单个策略失败到Sentry
  static void _reportToSentry({
    required String drive,
    required String strategyName,
    required int executionTimeMs,
    required bool isSuccess,
    String? errorMessage,
  }) {
    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: '磁盘空间策略执行${isSuccess ? '成功' : '失败'}',
          category: _tagCategory,
          data: {
            _tagDrive: drive,
            _tagStrategy: strategyName,
            _tagSuccess: false,
            _tagExecutionTime: executionTimeMs,
            if (errorMessage != null) _tagError: errorMessage,
          },
          level: SentryLevel.warning,
        ),
      );

      Sentry.captureMessage(
        '磁盘空间策略执行${isSuccess ? '成功' : '失败'}',
        level: SentryLevel.warning,
        withScope: (scope) {
          scope.setTag(_tagCategory, 'strategy_failure');
          scope.setTag(_tagDrive, drive);
          scope.setTag(_tagStrategy, strategyName);
          scope.setTag(_tagExecutionTime, executionTimeMs.toString());
          if (errorMessage != null) {
            scope.setContexts('error', {'message': errorMessage});
          }
        },
      );
    } catch (e) {
      PGLog.e('Sentry上报失败: $e');
    }
  }

  /// 上报策略链失败到Sentry
  static void _reportStrategyChainToSentry({
    required String drive,
    required List<String> attemptedStrategies,
    required int totalExecutionTimeMs,
    double? finalResultSizeBytes,
  }) {
    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: '磁盘空间策略链执行失败',
          category: _tagCategory,
          data: {
            _tagDrive: drive,
            'attempted_strategies': attemptedStrategies.join(', '),
            'successful_strategy': 'none',
            _tagSuccess: false,
            _tagExecutionTime: totalExecutionTimeMs,
            if (finalResultSizeBytes != null)
              _tagResultSize: (finalResultSizeBytes / 1024 / 1024 / 1024)
                  .toStringAsFixed(2),
          },
          level: SentryLevel.error,
        ),
      );

      Sentry.captureMessage(
        '磁盘空间获取失败 - 所有策略均失败',
        level: SentryLevel.error,
        withScope: (scope) {
          scope.setTag(_tagCategory, 'chain_failure');
          scope.setTag(_tagDrive, drive);
          scope.setTag(
              'attempted_count', attemptedStrategies.length.toString());
          scope.setTag(_tagExecutionTime, totalExecutionTimeMs.toString());
          scope.setContexts('strategies', {
            'attempted': attemptedStrategies,
            'total_time_ms': totalExecutionTimeMs,
          });
        },
      );
    } catch (e) {
      PGLog.e('策略链Sentry上报失败: $e');
    }
  }

  /// 上报返回无限大的情况到Sentry
  static void _reportInfiniteResultToSentry({
    required String drive,
    required List<String> attemptedStrategies,
    required int totalExecutionTimeMs,
  }) {
    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: '磁盘空间获取失败 - 返回无限大值',
          category: _tagCategory,
          data: {
            _tagDrive: drive,
            'attempted_strategies': attemptedStrategies.join(', '),
            'result': 'infinite',
            _tagSuccess: false,
            _tagExecutionTime: totalExecutionTimeMs,
          },
          level: SentryLevel.error,
        ),
      );

      Sentry.captureMessage(
        '磁盘空间获取失败 - 返回无限大值',
        level: SentryLevel.error,
        withScope: (scope) {
          scope.setTag(_tagCategory, 'infinite_result');
          scope.setTag(_tagDrive, drive);
          scope.setTag(
              'attempted_count', attemptedStrategies.length.toString());
          scope.setTag(_tagExecutionTime, totalExecutionTimeMs.toString());
          scope.setContexts('strategies', {
            'attempted': attemptedStrategies,
            'total_time_ms': totalExecutionTimeMs,
            'result_type': 'infinite',
          });
        },
      );
    } catch (e) {
      PGLog.e('无限大结果Sentry上报失败: $e');
    }
  }
}
