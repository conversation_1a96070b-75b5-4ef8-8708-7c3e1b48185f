import 'dart:io';

import 'package:turing_art/utils/pg_log.dart';

import 'disk_space_strategy_interface.dart';

/// Cmd -> Pwsh 策略
/// 通过cmd调用pwsh获取磁盘空间
class CmdPwshStrategy implements DiskSpaceStrategy {
  @override
  String get strategyName => 'Cmd -> Pwsh';

  @override
  Future<double?> getDiskSpace(String drivePath) async {
    try {
      final driveLetter = _getDriveLetter(drivePath);

      // 使用更安全的参数传递方式
      final result = await Process.run('cmd', [
        '/c',
        'pwsh',
        '-ExecutionPolicy',
        'Bypass',
        '-Command',
        '(Get-PSDrive $driveLetter).Free'
      ]);

      return _parsePowerShellResult(result, 'cmd -> pwsh');
    } catch (e) {
      PGLog.w('Cmd -> Pwsh 策略执行失败: $e');
      return null;
    }
  }

  /// 解析PowerShell执行结果
  double? _parsePowerShellResult(ProcessResult result, String methodName) {
    final stdout = result.stdout?.toString().trim() ?? '';
    final stderr = result.stderr?.toString().trim() ?? '';

    PGLog.d(
        '$methodName 执行结果: exitCode=${result.exitCode}, stdout="$stdout", stderr="$stderr"');

    if (result.exitCode == 0 && stdout.isNotEmpty) {
      final freeSpace = double.tryParse(stdout);

      if (freeSpace != null && freeSpace > 0) {
        PGLog.d(
            '$methodName 获取成功: ${(freeSpace / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');
        return freeSpace;
      } else {
        PGLog.w('$methodName 返回无效值: "$stdout"');
      }
    } else {
      PGLog.w(
          '$methodName 执行失败: exitCode=${result.exitCode}, stderr="$stderr"');
    }

    return null;
  }

  /// 从文件路径中提取磁盘符号
  String _getDriveLetter(String path) {
    if (path.isEmpty) {
      return 'C';
    }

    final normalizedPath = path.replaceAll('/', '\\');
    final regex = RegExp(r'^([A-Za-z]):');
    final match = regex.firstMatch(normalizedPath);

    return match?.group(1)?.toUpperCase() ?? 'C';
  }
}
