import 'dart:io';

import 'package:turing_art/utils/pg_log.dart';

import 'disk_space_strategy_interface.dart';

/// Pwsh.exe 策略
/// 直接调用pwsh.exe获取磁盘空间
class PwshExeStrategy implements DiskSpaceStrategy {
  @override
  String get strategyName => 'Pwsh.exe';

  @override
  Future<double?> getDiskSpace(String drivePath) async {
    try {
      final driveLetter = _getDriveLetter(drivePath);

      final result = await Process.run('pwsh', [
        '-ExecutionPolicy',
        'Bypass',
        '-Command',
        '(Get-PSDrive $driveLetter).Free'
      ]);

      return _parsePowerShellResult(result, 'pwsh');
    } catch (e) {
      PGLog.w('Pwsh.exe 策略执行失败: $e');
      return null;
    }
  }

  /// 解析PowerShell执行结果
  double? _parsePowerShellResult(ProcessResult result, String methodName) {
    final stdout = result.stdout?.toString().trim() ?? '';
    final stderr = result.stderr?.toString().trim() ?? '';

    PGLog.d(
        '$methodName 执行结果: exitCode=${result.exitCode}, stdout="$stdout", stderr="$stderr"');

    if (result.exitCode == 0 && stdout.isNotEmpty) {
      final freeSpace = double.tryParse(stdout);

      if (freeSpace != null && freeSpace > 0) {
        PGLog.d(
            '$methodName 获取成功: ${(freeSpace / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');
        return freeSpace;
      } else {
        PGLog.w('$methodName 返回无效值: "$stdout"');
      }
    } else {
      PGLog.w(
          '$methodName 执行失败: exitCode=${result.exitCode}, stderr="$stderr"');
    }

    return null;
  }

  /// 从文件路径中提取磁盘符号
  String _getDriveLetter(String path) {
    if (path.isEmpty) {
      return 'C';
    }

    final normalizedPath = path.replaceAll('/', '\\');
    final regex = RegExp(r'^([A-Za-z]):');
    final match = regex.firstMatch(normalizedPath);

    return match?.group(1)?.toUpperCase() ?? 'C';
  }
}
