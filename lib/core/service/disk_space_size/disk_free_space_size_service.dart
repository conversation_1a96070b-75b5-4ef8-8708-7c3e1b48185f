import 'package:turing_art/utils/app_constants.dart';

import 'disk_free_space_size_interface.dart';
import 'single_disk_free_space_size_handler.dart';
import 'windows_disk_free_space_size_handler2.dart';

/// 磁盘剩余空间服务
class DiskFreeSpaceSizeService {
  static final isWindows = AppConstants.isWindows;
  final DiskFreeSpaceSizeInterface _handler;

  DiskFreeSpaceSizeService._({required DiskFreeSpaceSizeInterface handler})
      : _handler = handler;

  factory DiskFreeSpaceSizeService.forPlatform() {
    final handler = isWindows
        ? WindowsDiskFreeSpaceSizeHandler2()
        : SingleDiskFreeSpaceSizeHandler();
    return DiskFreeSpaceSizeService._(handler: handler);
  }

  /// 获取单个目录剩余空间大小
  /// [dir] 目录路径
  /// @return 磁盘剩余空间，单位为字节，如果返回值为0，则表示磁盘空间不足
  Future<double?> getDirectoryFreeDiskSpaceSize({required String dir}) {
    return _handler.getDirectoryFreeDiskSpaceSize(dir: dir);
  }

  /// 获取根目录剩余空间大小(移动端、 macOS平台, 如果是window平台调用默认返回C盘可用空间)
  Future<double?> getRootDirectoryFreeDiskSpaceSize() {
    return _handler.getRootDirectoryFreeDiskSpaceSize();
  }
}
