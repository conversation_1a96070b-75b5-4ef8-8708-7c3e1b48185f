import 'package:turing_art/constants/constants.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'disk_free_space_size_interface.dart';
import 'disk_space_strategy_interface.dart';
import 'disk_space_strategy_stats.dart';
import 'native_api_strategy.dart';
import 'powershell_strategy.dart';

/// Windows平台磁盘空间检查工具类
/// 在每次调用时尝试多种方法直到成功
class WindowsDiskFreeSpaceSizeHandler extends DiskFreeSpaceSizeInterface {
  static final List<DiskSpaceStrategy> _strategies = [
    NativeApiStrategy(),
    PowerShellStrategy(), // 现在包含WMI查询功能
  ];

  /// 获取单个目录可用磁盘空间大小
  /// 使用责任链模式，依次尝试各种方法直到成功
  @override
  Future<double?> getDirectoryFreeDiskSpaceSize({required String dir}) async {
    // 从路径中提取磁盘符号并构建标准路径
    final driveLetter = _getDriveLetter(dir);
    final drivePath = '$driveLetter:\\';

    PGLog.i('开始获取磁盘空间: $drivePath');

    final startTime = DateTime.now();
    final List<String> attemptedStrategies = [];
    String? successfulStrategy;
    double? finalResult;

    // 责任链模式：依次尝试各种策略
    for (final strategy in _strategies) {
      final strategyStartTime = DateTime.now();
      attemptedStrategies.add(strategy.strategyName);

      try {
        PGLog.d('尝试使用 ${strategy.strategyName}...');
        final result = await strategy.getDiskSpace(drivePath);

        final strategyExecutionTime =
            DateTime.now().difference(strategyStartTime).inMilliseconds;

        if (result != null && result > 0) {
          // 策略成功
          successfulStrategy = strategy.strategyName;
          finalResult = result;

          PGLog.i(
              '✅ ${strategy.strategyName} 获取成功: ${(result / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');

          // 记录成功统计（只记录本地日志，不上报Sentry）
          DiskSpaceStrategyStats.recordStrategyExecution(
            drive: driveLetter,
            strategyName: strategy.strategyName,
            success: true,
            executionTimeMs: strategyExecutionTime,
            resultSizeBytes: result,
          );

          break; // 成功获取，退出循环
        } else {
          // 策略返回无效结果
          PGLog.w('⚠️ ${strategy.strategyName} 返回无效结果');

          // 记录失败统计（上报到Sentry）
          DiskSpaceStrategyStats.recordStrategyExecution(
            drive: driveLetter,
            strategyName: strategy.strategyName,
            success: false,
            executionTimeMs: strategyExecutionTime,
            errorMessage: '返回无效结果',
          );
        }
      } catch (e) {
        // 策略执行异常
        final strategyExecutionTime =
            DateTime.now().difference(strategyStartTime).inMilliseconds;
        PGLog.w('❌ ${strategy.strategyName} 执行异常: $e');

        // 记录失败统计（上报到Sentry）
        DiskSpaceStrategyStats.recordStrategyExecution(
          drive: driveLetter,
          strategyName: strategy.strategyName,
          success: false,
          executionTimeMs: strategyExecutionTime,
          errorMessage: e.toString(),
        );
      }
    }

    // 计算总执行时间
    final totalExecutionTime =
        DateTime.now().difference(startTime).inMilliseconds;

    // 记录策略链执行完成统计
    DiskSpaceStrategyStats.recordStrategyChainComplete(
      drive: driveLetter,
      attemptedStrategies: attemptedStrategies,
      successfulStrategy: successfulStrategy,
      totalExecutionTimeMs: totalExecutionTime,
      finalResultSizeBytes: finalResult,
    );

    // 返回结果
    if (finalResult != null) {
      return finalResult;
    }

    // 所有策略都失败
    if (isDebug) {
      // 办公室的测试模式下，返回0，便于定位问题
      return 0;
    }

    // 正式环境中返回无穷大，避免阻塞当前流程
    PGLog.e('❌ 所有磁盘空间获取方法都失败,返回最大值避免阻塞当前流程');

    // 记录返回无限大的情况（上报到Sentry）
    DiskSpaceStrategyStats.recordInfiniteResult(
      drive: driveLetter,
      attemptedStrategies: attemptedStrategies,
      totalExecutionTimeMs: totalExecutionTime,
    );

    return double.maxFinite;
  }

  /// 获取根目录的可用磁盘空间大小,单位为字节(windows平台默认是C盘)
  @override
  Future<double?> getRootDirectoryFreeDiskSpaceSize() async {
    final appDir = await FileManager().appDir;
    return await getDirectoryFreeDiskSpaceSize(dir: appDir.path);
  }

  /// 从文件路径中提取磁盘符号
  /// 例如：从"C:\\projects"提取出"C"
  /// @param path 文件路径
  /// @return 磁盘符号，如果无法提取则返回默认值"C"
  String _getDriveLetter(String path) {
    if (path.isEmpty) {
      return 'C'; // 默认返回C盘
    }

    // 处理路径格式，确保使用标准的Windows路径分隔符
    final normalizedPath = path.replaceAll('/', '\\');

    // 检查路径是否包含磁盘符号（如C:）
    final regex = RegExp(r'^([A-Za-z]):');
    final match = regex.firstMatch(normalizedPath);

    if (match != null && match.groupCount >= 1) {
      return match.group(1)!.toUpperCase(); // 返回大写的磁盘符号
    }

    // 如果无法提取，返回默认值
    return 'C';
  }
}
