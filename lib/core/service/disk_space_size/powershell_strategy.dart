import 'dart:io';

import 'package:turing_art/utils/pg_log.dart';

import 'disk_space_strategy_interface.dart';

/// PowerShell策略
class PowerShellStrategy implements DiskSpaceStrategy {
  @override
  String get strategyName => 'PowerShell';

  @override
  Future<double?> getDiskSpace(String drivePath) async {
    // 尝试多种PowerShell调用方式
    final methods = [
      _tryPowerShellExe, // 尝试powershell.exe
      _tryPwshExe, // 尝试pwsh.exe
      _tryCmdPowerShell, // 通过cmd调用powershell
      _tryCmdPwsh, // 通过cmd调用pwsh
      _tryPowerShellWMI, // 使用PowerShell的WMI查询
    ];

    for (final method in methods) {
      try {
        final result = await method(drivePath);
        if (result != null && result > 0) {
          return result;
        }
      } catch (e) {
        PGLog.d('PowerShell方法 ${method.toString()} 失败: $e');
      }
    }

    return null;
  }

  /// 方法1：直接调用powershell.exe
  Future<double?> _tryPowerShellExe(String drivePath) async {
    final driveLetter = _getDriveLetter(drivePath);

    final result = await Process.run('powershell', [
      '-ExecutionPolicy',
      'Bypass',
      '-Command',
      '(Get-PSDrive $driveLetter).Free'
    ]);

    return _parsePowerShellResult(result, 'powershell');
  }

  /// 方法2：直接调用pwsh.exe
  Future<double?> _tryPwshExe(String drivePath) async {
    final driveLetter = _getDriveLetter(drivePath);

    final result = await Process.run('pwsh', [
      '-ExecutionPolicy',
      'Bypass',
      '-Command',
      '(Get-PSDrive $driveLetter).Free'
    ]);

    return _parsePowerShellResult(result, 'pwsh');
  }

  /// 方法3：通过cmd调用powershell
  Future<double?> _tryCmdPowerShell(String drivePath) async {
    final driveLetter = _getDriveLetter(drivePath);

    // 使用更安全的参数传递方式
    final result = await Process.run('cmd', [
      '/c',
      'powershell',
      '-ExecutionPolicy',
      'Bypass',
      '-Command',
      '(Get-PSDrive $driveLetter).Free'
    ]);

    return _parsePowerShellResult(result, 'cmd -> powershell');
  }

  /// 方法4：通过cmd调用pwsh
  Future<double?> _tryCmdPwsh(String drivePath) async {
    final driveLetter = _getDriveLetter(drivePath);

    // 使用更安全的参数传递方式
    final result = await Process.run('cmd', [
      '/c',
      'pwsh',
      '-ExecutionPolicy',
      'Bypass',
      '-Command',
      '(Get-PSDrive $driveLetter).Free'
    ]);

    return _parsePowerShellResult(result, 'cmd -> pwsh');
  }

  /// 方法5：使用PowerShell的WMI查询
  Future<double?> _tryPowerShellWMI(String drivePath) async {
    final driveLetter = _getDriveLetter(drivePath);

    final result = await Process.run('powershell', [
      '-ExecutionPolicy',
      'Bypass',
      '-Command',
      'Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID=\'$driveLetter:\'" | Select-Object -ExpandProperty FreeSpace'
    ]);

    return _parsePowerShellWMIResult(result, 'PowerShell WMI');
  }

  /// 解析PowerShell执行结果
  double? _parsePowerShellResult(ProcessResult result, String methodName) {
    final stdout = result.stdout?.toString().trim() ?? '';
    final stderr = result.stderr?.toString().trim() ?? '';

    PGLog.d(
        '$methodName 执行结果: exitCode=${result.exitCode}, stdout="$stdout", stderr="$stderr"');

    if (result.exitCode == 0 && stdout.isNotEmpty) {
      final freeSpace = double.tryParse(stdout);

      if (freeSpace != null && freeSpace > 0) {
        PGLog.d(
            '$methodName 获取成功: ${(freeSpace / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');
        return freeSpace;
      } else {
        PGLog.w('$methodName 返回无效值: "$stdout"');
      }
    } else {
      PGLog.w(
          '$methodName 执行失败: exitCode=${result.exitCode}, stderr="$stderr"');
    }

    return null;
  }

  double? _parsePowerShellWMIResult(ProcessResult result, String methodName) {
    final stdout = result.stdout?.toString().trim() ?? '';
    final stderr = result.stderr?.toString().trim() ?? '';

    PGLog.d(
        '$methodName 执行结果: exitCode=${result.exitCode}, stdout="$stdout", stderr="$stderr"');

    if (result.exitCode == 0 && stdout.isNotEmpty) {
      final freeSpace = double.tryParse(stdout);
      if (freeSpace != null && freeSpace > 0) {
        PGLog.d(
            '$methodName 获取成功: ${(freeSpace / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');
        return freeSpace;
      }
    }

    PGLog.w('$methodName 执行失败或返回无效值');
    return null;
  }

  /// 从文件路径中提取磁盘符号
  String _getDriveLetter(String path) {
    if (path.isEmpty) {
      return 'C';
    }

    final normalizedPath = path.replaceAll('/', '\\');
    final regex = RegExp(r'^([A-Za-z]):');
    final match = regex.firstMatch(normalizedPath);

    return match?.group(1)?.toUpperCase() ?? 'C';
  }
}
