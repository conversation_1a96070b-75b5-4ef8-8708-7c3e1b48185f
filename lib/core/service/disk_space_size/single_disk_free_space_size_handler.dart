import 'package:disk_space_plus/disk_space_plus.dart';

import 'disk_free_space_size_interface.dart';

/// 单磁盘(移动端、Pad、macOS平台)空间检查工具类
class SingleDiskFreeSpaceSizeHandler extends DiskFreeSpaceSizeInterface {
  /// 检查磁盘空间是否足够（移动平台，以及macOS暂不需要这个方法）
  /// @param filePath 文件路径
  /// @return 磁盘剩余空间，单位为字节，如果返回值为0，则表示磁盘空间不足
  @override
  Future<double?> getDirectoryFreeDiskSpaceSize({required String dir}) async {
    return getRootDirectoryFreeDiskSpaceSize();
  }

  /// 获取单个目录剩余空间大小(移动端、 macOS平台, 如果是window平台调用默认返回C盘可用空间)
  /// @return 磁盘剩余空间，单位为字节，如果返回值为0，则表示磁盘空间不足
  @override
  Future<double?> getRootDirectoryFreeDiskSpaceSize() async {
    final freeSpace = await DiskSpacePlus.getFreeDiskSpace;
    return freeSpace != null ? freeSpace.toDouble() : 0;
  }
}
