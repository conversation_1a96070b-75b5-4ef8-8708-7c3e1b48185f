import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'cmd_powershell_strategy.dart';
import 'cmd_pwsh_strategy.dart';
import 'disk_free_space_size_interface.dart';
import 'disk_space_strategy_interface.dart';
import 'disk_space_strategy_stats.dart';
import 'native_api_strategy.dart';
import 'powershell_exe_strategy.dart';
import 'powershell_wmi_strategy.dart';
import 'pwsh_exe_strategy.dart';

/// Windows平台磁盘空间检查工具类 V2
/// 立即返回结果，后台异步执行所有策略进行统计
class WindowsDiskFreeSpaceSizeHandler2 extends DiskFreeSpaceSizeInterface {
  static final List<DiskSpaceStrategy> _strategies = [
    NativeApiStrategy(),
    PowerShellExeStrategy(),
    PwshExeStrategy(),
    CmdPowerShellStrategy(),
    CmdPwshStrategy(),
    PowerShellWmiStrategy(),
  ];

  /// 获取单个目录可用磁盘空间大小
  /// 立即返回结果，后台异步执行所有策略进行统计
  @override
  Future<double?> getDirectoryFreeDiskSpaceSize({required String dir}) async {
    // 从路径中提取磁盘符号并构建标准路径
    final driveLetter = _getDriveLetter(dir);
    final drivePath = '$driveLetter:\\';

    PGLog.i('开始获取磁盘空间: $drivePath (V2 - 立即返回，后台统计)');

    // 立即返回最大值，避免阻塞流程
    const result = double.maxFinite;
    PGLog.i(
        '✅ 立即返回最大值: ${(result / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');

    // 在后台异步执行所有策略进行统计
    _executeAllStrategiesInBackground(
        drivePath: drivePath, driveLetter: driveLetter);

    return result;
  }

  /// 在后台异步执行所有策略进行统计
  void _executeAllStrategiesInBackground({
    required String drivePath,
    required String driveLetter,
  }) {
    // 为本次任务生成统一的时间戳和任务ID
    final taskStartTime = DateTime.now();

    DiskSpaceStrategyStats.recordAttemptedGetDiskSpace(
      executionTimeMs: taskStartTime.millisecondsSinceEpoch,
    );

    // 使用 unawaited 来避免等待这些异步操作
    for (final strategy in _strategies) {
      _executeStrategyInBackground(
        strategy: strategy,
        drivePath: drivePath,
        driveLetter: driveLetter,
        taskStartTime: taskStartTime,
      );
    }
  }

  /// 在后台异步执行单个策略并统计结果
  Future<void> _executeStrategyInBackground({
    required DiskSpaceStrategy strategy,
    required String drivePath,
    required String driveLetter,
    required DateTime taskStartTime,
  }) async {
    try {
      PGLog.d('后台执行 ${strategy.strategyName}...');
      final result = await strategy.getDiskSpace(drivePath);

      if (result != null && result > 0) {
        // 策略成功
        PGLog.i(
            '✅ ${strategy.strategyName} 后台执行成功: ${(result / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');

        // 记录成功统计（上报Sentry）
        DiskSpaceStrategyStats.recordStrategyExecution(
          drive: driveLetter,
          strategyName: strategy.strategyName,
          success: true,
          executionTimeMs: taskStartTime.millisecondsSinceEpoch,
          resultSizeBytes: result,
        );
      } else {
        // 策略返回无效结果
        PGLog.w('⚠️ ${strategy.strategyName} 后台执行返回无效结果');

        // 记录失败统计（上报到Sentry）
        DiskSpaceStrategyStats.recordStrategyExecution(
          drive: driveLetter,
          strategyName: strategy.strategyName,
          success: false,
          executionTimeMs: taskStartTime.millisecondsSinceEpoch,
          errorMessage: '返回无效结果',
        );
      }
    } catch (e) {
      // 策略执行异常

      PGLog.w('❌ ${strategy.strategyName} 后台执行异常: $e');

      // 记录失败统计（上报到Sentry）
      DiskSpaceStrategyStats.recordStrategyExecution(
        drive: driveLetter,
        strategyName: strategy.strategyName,
        success: false,
        executionTimeMs: taskStartTime.millisecondsSinceEpoch,
        errorMessage: e.toString(),
      );
    }
  }

  /// 获取根目录的可用磁盘空间大小,单位为字节(windows平台默认是C盘)
  @override
  Future<double?> getRootDirectoryFreeDiskSpaceSize() async {
    final appDir = await FileManager().appDir;
    return await getDirectoryFreeDiskSpaceSize(dir: appDir.path);
  }

  /// 从文件路径中提取磁盘符号
  /// 例如：从"C:\\projects"提取出"C"
  /// @param path 文件路径
  /// @return 磁盘符号，如果无法提取则返回默认值"C"
  String _getDriveLetter(String path) {
    if (path.isEmpty) {
      return 'C'; // 默认返回C盘
    }

    // 处理路径格式，确保使用标准的Windows路径分隔符
    final normalizedPath = path.replaceAll('/', '\\');

    // 检查路径是否包含磁盘符号（如C:）
    final regex = RegExp(r'^([A-Za-z]):');
    final match = regex.firstMatch(normalizedPath);

    if (match != null && match.groupCount >= 1) {
      return match.group(1)!.toUpperCase(); // 返回大写的磁盘符号
    }

    // 如果无法提取，返回默认值
    return 'C';
  }
}
