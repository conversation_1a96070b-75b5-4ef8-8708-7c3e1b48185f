import 'package:turing_art/ffi/native/disk_info_bindings.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'disk_space_strategy_interface.dart';

/// 原生API策略
class NativeApiStrategy implements DiskSpaceStrategy {
  @override
  String get strategyName => 'Native API (DiskInfo)';

  @override
  Future<double?> getDiskSpace(String drivePath) async {
    try {
      // 每次调用时都重新初始化，确保可用性
      final initialized = DiskInfoBindings.initialize();

      if (!initialized || !DiskInfoBindings.isAvailable) {
        PGLog.d('原生API不可用');
        return null;
      }

      final diskInfo = DiskInfoBindings.getDiskSpace(drivePath);
      if (diskInfo != null && diskInfo.freeSpace > 0) {
        PGLog.d(
            '原生API获取成功: ${diskInfo.formattedFreeSpace} (${diskInfo.freeSpace} bytes)');
        return diskInfo.freeSpace.toDouble();
      } else {
        PGLog.w('原生API返回无效值: $diskInfo');
        return null;
      }
    } catch (e) {
      PGLog.w('原生API获取失败: $e');
      return null;
    }
  }
}
