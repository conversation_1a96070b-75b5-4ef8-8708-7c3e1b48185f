import 'dart:io';

import 'package:turing_art/utils/pg_log.dart';

import 'disk_space_strategy_interface.dart';

/// PowerShell WMI 策略
/// 使用PowerShell的WMI查询获取磁盘空间
class PowerShellWmiStrategy implements DiskSpaceStrategy {
  @override
  String get strategyName => 'PowerShell WMI';

  @override
  Future<double?> getDiskSpace(String drivePath) async {
    try {
      final driveLetter = _getDriveLetter(drivePath);

      final result = await Process.run('powershell', [
        '-ExecutionPolicy',
        'Bypass',
        '-Command',
        'Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID=\'$driveLetter:\'" | Select-Object -ExpandProperty FreeSpace'
      ]);

      return _parsePowerShellWMIResult(result, 'PowerShell WMI');
    } catch (e) {
      PGLog.w('PowerShell WMI 策略执行失败: $e');
      return null;
    }
  }

  /// 解析PowerShell WMI执行结果
  double? _parsePowerShellWMIResult(ProcessResult result, String methodName) {
    final stdout = result.stdout?.toString().trim() ?? '';
    final stderr = result.stderr?.toString().trim() ?? '';

    PGLog.d(
        '$methodName 执行结果: exitCode=${result.exitCode}, stdout="$stdout", stderr="$stderr"');

    if (result.exitCode == 0 && stdout.isNotEmpty) {
      final freeSpace = double.tryParse(stdout);
      if (freeSpace != null && freeSpace > 0) {
        PGLog.d(
            '$methodName 获取成功: ${(freeSpace / 1024 / 1024 / 1024).toStringAsFixed(2)} GB');
        return freeSpace;
      }
    }

    PGLog.w('$methodName 执行失败或返回无效值');
    return null;
  }

  /// 从文件路径中提取磁盘符号
  String _getDriveLetter(String path) {
    if (path.isEmpty) {
      return 'C';
    }

    final normalizedPath = path.replaceAll('/', '\\');
    final regex = RegExp(r'^([A-Za-z]):');
    final match = regex.firstMatch(normalizedPath);

    return match?.group(1)?.toUpperCase() ?? 'C';
  }
}
