/// 缓存清理模式
enum CacheCleanupMode {
  /// 条件模式 - 根据配置的规则清理
  @Deprecated('使用 full 代替')
  conditional,

  /// 全部清除模式 - 清理所有缓存
  full,
}

/// 缓存清理结果
class CacheCleanupResult {
  final DateTime timestamp;
  final CacheCleanupMode mode;
  final int totalDeletedSize;
  final Map<String, ServiceCleanupResult> serviceResults;
  final String? errorMessage;

  const CacheCleanupResult({
    required this.timestamp,
    required this.mode,
    required this.totalDeletedSize,
    required this.serviceResults,
    this.errorMessage,
  });

  /// 是否成功
  bool get isSuccess => errorMessage == null;

  /// 获取清理的服务数量
  int get serviceCount => serviceResults.length;

  /// 获取指定服务的清理结果
  ServiceCleanupResult? getServiceResult(String serviceName) {
    return serviceResults[serviceName];
  }

  @override
  String toString() {
    return 'CacheCleanupResult{mode: $mode, totalDeletedSize: ${(totalDeletedSize / 1024 / 1024).toStringAsFixed(2)}MB, serviceCount: $serviceCount, isSuccess: $isSuccess}';
  }
}

/// 服务清理结果
class ServiceCleanupResult {
  final String serviceName;
  final int deletedSize;
  final DateTime timestamp;

  const ServiceCleanupResult({
    required this.serviceName,
    required this.deletedSize,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'ServiceCleanupResult{serviceName: $serviceName, deletedSize: ${(deletedSize / 1024 / 1024).toStringAsFixed(2)}MB}';
  }
}
