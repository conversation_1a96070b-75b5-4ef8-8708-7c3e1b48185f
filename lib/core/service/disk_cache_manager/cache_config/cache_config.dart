import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/constants/constants.dart';

part 'cache_config.freezed.dart';
part 'cache_config.g.dart';

// 磁盘空间阈值 5GB - 200GB
const int minCleanupThresholdSize = 5 * 1024 * 1024 * 1024;
const int maxCleanupThresholdSize = 200 * 1024 * 1024 * 1024;

/// 缓存有效时间
enum CacheValidTime {
  /// 3天
  threeDays(3),

  /// 7天
  sevenDays(7),

  /// 15天
  fifteenDays(15),

  /// 30天
  thirtyDays(30);

  const CacheValidTime(this.days);

  final int days;

  factory CacheValidTime.fromDays(int days) {
    return CacheValidTime.values.firstWhere(
      (e) => e.days == days,
      orElse: () => CacheValidTime.threeDays,
    );
  }

  Duration get duration => Duration(days: days);
}

/// 缓存清理配置
@freezed
class CacheConfig with _$CacheConfig {
  const factory CacheConfig({
    /// 清理天数
    @Default(CacheValidTime.threeDays) CacheValidTime cleanupDays,

    /// 清理阈值大小（字节）
    @Default(minCleanupThresholdSize) int cleanupThresholdSize,
  }) = _CacheConfig;
  const CacheConfig._();

  factory CacheConfig.fromJson(Map<String, dynamic> json) =>
      _$CacheConfigFromJson(json);

  /// 从GB创建配置
  factory CacheConfig.fromGB({
    required CacheValidTime cleanupDays,
    required int cleanupThresholdSizeGB,
  }) {
    return CacheConfig(
      cleanupDays: cleanupDays,
      cleanupThresholdSize:
          (cleanupThresholdSizeGB * 1024 * 1024 * 1024 * (isDebug ? 0.1 : 1))
              .toInt(),
    );
  }
}
