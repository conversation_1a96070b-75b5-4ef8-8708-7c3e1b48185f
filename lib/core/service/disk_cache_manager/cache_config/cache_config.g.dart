// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cache_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CacheConfigImpl _$$CacheConfigImplFromJson(Map<String, dynamic> json) =>
    _$CacheConfigImpl(
      cleanupDays:
          $enumDecodeNullable(_$CacheValidTimeEnumMap, json['cleanupDays']) ??
              CacheValidTime.threeDays,
      cleanupThresholdSize: (json['cleanupThresholdSize'] as num?)?.toInt() ??
          minCleanupThresholdSize,
    );

Map<String, dynamic> _$$CacheConfigImplToJson(_$CacheConfigImpl instance) =>
    <String, dynamic>{
      'cleanupDays': _$CacheValidTimeEnumMap[instance.cleanupDays]!,
      'cleanupThresholdSize': instance.cleanupThresholdSize,
    };

const _$CacheValidTimeEnumMap = {
  CacheValidTime.threeDays: 'threeDays',
  CacheValidTime.sevenDays: 'sevenDays',
  CacheValidTime.fifteenDays: 'fifteenDays',
  CacheValidTime.thirtyDays: 'thirtyDays',
};
