import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/core/tapj/n8_tapj_file_manager.dart';
import 'package:turing_art/datalayer/domain/enums/time_display_format.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/file_name_utils.dart';
import 'package:turing_art/utils/permission_util.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/tapj_processor.dart';

import 'image_picker_interface.dart';

/// 图片名称处理器（仅限当前文件内部使用）
/// 负责处理图片项目名称创建相关的功能
class ImageNameProcessor {
  /// 获取默认项目名称（基于当前日期）
  static String getDefaultProjectName() {
    return DateTimeUtil.getCurrentDateStr(format: DateFormat.standardNoHour);
  }

  /// 从文件名创建有效的项目名称
  static String createValidProjectName(String fileName) {
    if (fileName.isEmpty) {
      return getDefaultProjectName();
    }

    final nameWithoutExtension =
        FileNameUtils.extractNameWithoutExtension(fileName);
    return nameWithoutExtension.isNotEmpty
        ? nameWithoutExtension
        : getDefaultProjectName();
  }

  /// 从文件结果中创建项目名称
  static String createProjectNameFromFileResult(FilePickerResult result) {
    if (result.files.isEmpty) {
      PGLog.w('没有选择文件');
      return '';
    }

    return createValidProjectName(result.files.first.name);
  }

  /// 从XFile列表中创建项目名称
  static String createProjectNameFromXFiles(List<XFile> images) {
    if (images.isEmpty) {
      PGLog.w('没有选择图片');
      return '';
    }

    return createValidProjectName(images.first.name);
  }

  /// 从File列表中创建项目名称
  static String createProjectNameFromFiles(List<File> images) {
    if (images.isEmpty) {
      PGLog.w('没有选择图片');
      return '';
    }

    return createValidProjectName(
        path.basenameWithoutExtension(images.first.path));
  }
}

/// 图片选择器处理器基类，包含共用的文件选择逻辑
abstract class BaseImagePickerHandler implements ImagePickerInterface {
  /// 检查文件权限
  Future<bool> checkFilePermission() async {
    final permissionGranted =
        await PermissionUtil.requestPermission(PermissionType.file);
    if (!permissionGranted) {
      PGLog.d('用户未授予文件权限');
    }
    return permissionGranted;
  }

  /// 公共文件选择实现
  Future<DealImageFilesResult?> pickFilesInternal() async {
    try {
      PGLog.d('打开文件选择器');
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ImageConstants.supportedExtensions +
            N8TapjFileManager.supportedTapjExtensions,
        allowMultiple: true,
        withData: false,
        withReadStream: false,
        allowCompression: false,
        lockParentWindow: true,
      );

      if (result == null || result.files.isEmpty) {
        PGLog.d('没有选择文件');
        return null;
      }

      // 处理tapj文件 - 如果有tapj文件则优先处理第一个tapj文件
      File? firstTapjFile;

      // 遍历文件找到第一个tapj文件
      for (final platformFile in result.files) {
        if (platformFile.path == null) continue;

        final file = File(platformFile.path!);
        final extension = platformFile.extension?.toLowerCase() ?? '';

        if (N8TapjFileManager.supportedTapjExtensions.contains(extension)) {
          if (firstTapjFile == null) {
            firstTapjFile = file;
            PGLog.d('找到tapj文件: ${file.path}');
          }
        }
      }

      if (firstTapjFile != null) {
        PGLog.d('选择了tapj文件: ${firstTapjFile.path}');
        // 使用TapjProcessor处理tapj文件
        return await TapjProcessor.processTapjForImagePicker(
            firstTapjFile.path);
      }

      PGLog.d('选择了 ${result.files.length} 个文件');
      final files = result.files
          .where((file) => file.path != null)
          .map((file) => File(file.path!))
          .toList();

      // 提取项目名称
      final projectName =
          ImageNameProcessor.createProjectNameFromFileResult(result);

      return DealImageFilesResult.fromImages(files, projectName);
    } catch (e) {
      PGLog.e('选择文件时出错: $e');
      return null;
    }
  }

  /// 公共文件选择实现
  Future<DealImageFilesResult?> pickImagesFromDirectoryInternal() async {
    try {
      PGLog.d('打开文件夹选择器');
      final selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择包含图片的文件夹',
        lockParentWindow: true,
      );

      if (selectedDirectory == null) {
        PGLog.d('没有选择文件夹');
        return null;
      }

      PGLog.d('选择了文件夹: $selectedDirectory');

      // 遍历文件夹中的所有文件
      final directory = Directory(selectedDirectory);
      final List<File> imageFiles = [];

      await _findImagesInDirectory(directory, imageFiles);

      if (imageFiles.isEmpty) {
        PGLog.d('所选文件夹中没有找到支持的图片');
        return null;
      }

      // 提取项目名称和处理图片文件
      final projectName = path.basename(directory.path);
      PGLog.d('在文件夹 $projectName 中找到了 ${imageFiles.length} 张图片');

      return DealImageFilesResult.fromImages(imageFiles, projectName);
    } catch (e) {
      PGLog.e('选择文件夹时出错: $e');
      return null;
    }
  }

  // 递归查找目录中的所有图片文件
  Future<void> _findImagesInDirectory(
      Directory directory, List<File> imageFiles) async {
    try {
      final entities = directory.listSync(recursive: false, followLinks: false);

      for (final entity in entities) {
        if (entity is File) {
          final extension = entity.path.split('.').last.toLowerCase();
          if (ImageConstants.supportedExtensions.contains(extension)) {
            imageFiles.add(entity);
          }
        } else if (entity is Directory) {
          // 递归遍历子目录
          await _findImagesInDirectory(entity, imageFiles);
        }
      }
    } catch (e) {
      PGLog.e('遍历文件夹时出错: $e');
    }
  }
}

/// 移动端图片选择处理器实现
class MobileImagePickerHandler extends BaseImagePickerHandler {
  final ImagePicker _picker = ImagePicker();

  // 从相册选择图片
  @override
  Future<DealImageFilesResult?> pickImagesFromGallery() async {
    final permissionGranted =
        await PermissionUtil.requestPermission(PermissionType.photos);
    if (!permissionGranted) {
      PGLog.w('用户未授予照片权限');
      return null;
    }
    try {
      PGLog.d('打开系统相册');
      final images = await _picker.pickMultiImage(
        maxWidth: ImageConstants.maxDimension,
        maxHeight: ImageConstants.maxDimension,
        imageQuality: ImageConstants.quality,
      );

      if (images.isEmpty) {
        PGLog.d('没有选择图片');
        return null;
      }

      PGLog.d('选择了 ${images.length} 张图片');

      // 提取项目名称和处理图片文件
      final projectName =
          ImageNameProcessor.createProjectNameFromXFiles(images);
      final imageFiles = images.map((image) => File(image.path)).toList();

      return DealImageFilesResult.fromImages(imageFiles, projectName);
    } catch (e) {
      PGLog.e('选择图片时出错: $e');
      return null;
    }
  }

  // 从文件系统选择图片
  @override
  Future<DealImageFilesResult?> pickImagesFromFiles() async {
    return pickFilesInternal();
  }

  @override
  Future<DealImageFilesResult?> pickImagesFromDirectory() async {
    if (!await checkFilePermission()) {
      PGLog.w('用户未授予文件权限');
      return null;
    }
    return super.pickImagesFromDirectoryInternal();
  }
}

/// PC端图片选择处理器实现
class DesktopImagePickerHandler extends BaseImagePickerHandler {
  // 从相册选择图片 (PC端与从文件选择相同)
  @override
  Future<DealImageFilesResult?> pickImagesFromGallery() {
    return pickImagesFromFiles();
  }

  // 从文件系统选择图片
  @override
  Future<DealImageFilesResult?> pickImagesFromFiles() async {
    if (!await checkFilePermission()) {
      PGLog.w('用户未授予文件权限');
      return null;
    }
    return pickFilesInternal();
  }

  // 从文件夹选择图片
  @override
  Future<DealImageFilesResult?> pickImagesFromDirectory() async {
    if (!await checkFilePermission()) {
      PGLog.w('用户未授予文件权限');
      return null;
    }

    return pickImagesFromDirectoryInternal();
  }
}
