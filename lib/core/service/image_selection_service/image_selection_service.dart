import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';

import '../../../utils/app_constants.dart';
import 'image_picker_handler.dart';
import 'image_picker_interface.dart';

/// 图片选择服务
class ImageSelectionService {
  static final isDesktop = AppConstants.isDesktop;

  final ImagePickerInterface _handler;

  ImageSelectionService._({required ImagePickerInterface handler})
      : _handler = handler;

  /// 从相册选择图片
  Future<DealImageFilesResult?> pickImagesFromGallery() {
    return _handler.pickImagesFromGallery();
  }

  /// 从文件系统选择图片
  Future<DealImageFilesResult?> pickImagesFromFiles() {
    return _handler.pickImagesFromFiles();
  }

  /// 从文件夹选择图片
  Future<DealImageFilesResult?> pickImagesFromDirectory() {
    return _handler.pickImagesFromDirectory();
  }

  /// 根据当前平台获取合理的实现
  factory ImageSelectionService.forPlatform() {
    final handler =
        isDesktop ? DesktopImagePickerHandler() : MobileImagePickerHandler();
    return ImageSelectionService._(handler: handler);
  }
}
