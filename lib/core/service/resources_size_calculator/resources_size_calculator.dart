import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 密封类，表示文件类型
sealed class FileType {
  const FileType();

  /// 普通图片文件
  const factory FileType.normal() = NormalImageFile;

  /// 其他图片文件
  const factory FileType.other() = OtherImageFile;

  /// 不支持的文件
  const factory FileType.unsupported() = UnsupportedFile;

  /// 获取缓冲区大小
  int get bufferSize;
}

/// 普通图片文件
final class NormalImageFile extends FileType {
  const NormalImageFile();

  @override
  int get bufferSize => ImageConstants.normalBufferSize;
}

/// 其他图片文件
final class OtherImageFile extends FileType {
  const OtherImageFile();

  @override
  int get bufferSize => ImageConstants.safeBufferSize;
}

/// 不支持的文件
final class UnsupportedFile extends FileType {
  const UnsupportedFile();

  @override
  int get bufferSize => 0;
}

class ResourcesSizeCalculator {
  static Future<double> calculateResourcesSize(List<File> files) async {
    final startTime = DateTime.now();

    try {
      final result = await compute(_calculateSize, files);

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      PGLog.i(
          '计算资源大小完成，耗时: ${duration.inMilliseconds}ms，总大小: ${result / (1024 * 1024)}MB');

      return result;
    } catch (e) {
      PGLog.e('计算资源大小时发生错误: $e');
      return 0;
    }
  }
}

/// 纯函数，用于计算文件大小
double _calculateSize(List<File> files) {
  double totalSize = 0;
  int supportedFileCount = 0;
  int normalImageCount = 0;
  int otherImageCount = 0;

  for (final file in files) {
    final filePath = file.path.toLowerCase();
    final extension = filePath.split('.').last;

    // 使用密封类处理文件类型
    final fileType = _getFileType(extension);

    // 使用模式匹配处理不同类型的文件
    switch (fileType) {
      case NormalImageFile():
        supportedFileCount++;
        normalImageCount++;
        totalSize += fileType.bufferSize;
      case OtherImageFile():
        supportedFileCount++;
        otherImageCount++;
        totalSize += fileType.bufferSize;
      case UnsupportedFile():
        continue;
    }
  }

  PGLog.i(
      '资源统计：支持的文件数: $supportedFileCount，普通图片: $normalImageCount，其他图片: $otherImageCount');
  return totalSize;
}

/// 根据文件扩展名获取文件类型
FileType _getFileType(String extension) {
  if (!ImageConstants.supportedExtensions.contains(extension)) {
    return const FileType.unsupported();
  }

  if (ImageConstants.normalImageExtensions.contains(extension)) {
    return const FileType.normal();
  }

  return const FileType.other();
}
