import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/core/external_message/command_line_args/command_line_args_processor.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 命令行消息管理器
/// 在应用启动时存储命令行参数，并在合适的时机将其转换为外部消息
class CommandLineMessageManager {
  static final CommandLineMessageManager _instance =
      CommandLineMessageManager._internal();
  factory CommandLineMessageManager() => _instance;
  CommandLineMessageManager._internal();

  List<String>? _commandLineArgs;
  ExternalMessage? _pendingMessage;

  /// 设置命令行参数（在main函数中调用）
  void setCommandLineArgs(List<String> args) {
    _commandLineArgs = args;
    PGLog.d('CommandLineMessageManager - 设置命令行参数: $args');

    // 立即处理命令行参数，转换为外部消息
    _processPendingCommandLineArgs();
  }

  /// 处理待处理的命令行参数
  void _processPendingCommandLineArgs() {
    if (_commandLineArgs == null || _commandLineArgs!.isEmpty) {
      return;
    }

    try {
      final message =
          CommandLineArgsProcessor.processCommandLineArgs(_commandLineArgs!);
      if (message != null) {
        _pendingMessage = message;
        PGLog.d('CommandLineMessageManager - 生成外部消息: ${message.type}');
      }
    } catch (e) {
      PGLog.e('CommandLineMessageManager - 处理命令行参数失败: $e');
    }
  }

  /// 获取待处理的外部消息
  ExternalMessage? getPendingMessage() {
    final message = _pendingMessage;
    _pendingMessage = null; // 获取后清空，避免重复处理
    return message;
  }

  /// 检查是否有待处理的消息
  bool get hasPendingMessage => _pendingMessage != null;

  /// 清空待处理的消息
  void clearPendingMessage() {
    _pendingMessage = null;
  }

  /// 获取原始命令行参数
  List<String>? get commandLineArgs => _commandLineArgs;
}
