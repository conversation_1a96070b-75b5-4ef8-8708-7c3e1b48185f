import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';

import 'external_message_result.dart';

/// 外部消息处理器抽象基类
abstract class ExternalMessageHandler {
  /// 处理器名称
  String get handlerName;

  /// 支持的消息类型
  ExternalMessageType get supportedType;

  /// 处理消息
  Future<ExternalMessageResult> handle(
    BuildContext context,
    ExternalMessage message,
  );

  /// 验证消息数据
  bool validateMessage(ExternalMessage message) {
    return message.type == supportedType && message.data.isNotEmpty;
  }
}
