import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'external_message_base_handler.dart';
import 'external_message_result.dart';

/// 打开项目消息处理器
class OpenProjectMessageHandler extends ExternalMessageHandler {
  final Future<ExternalMessageResult> Function(
    BuildContext context,
    OpenProjectData data,
  ) _onOpenProject;

  OpenProjectMessageHandler({
    required Future<ExternalMessageResult> Function(
      BuildContext context,
      OpenProjectData data,
    ) onOpenProject,
  }) : _onOpenProject = onOpenProject;

  @override
  String get handlerName => 'OpenProjectHandler';

  @override
  ExternalMessageType get supportedType => ExternalMessageType.openProject;

  @override
  Future<ExternalMessageResult> handle(
    BuildContext context,
    ExternalMessage message,
  ) async {
    try {
      if (!validateMessage(message)) {
        return const ExternalMessageError('无效的打开项目消息格式');
      }

      final openData = OpenProjectData.fromMap(message.data);
      if (openData == null) {
        return const ExternalMessageError('无法解析打开项目数据');
      }

      return await _onOpenProject(context, openData);
    } catch (e) {
      PGLog.e('处理打开项目消息失败: $e');
      return ExternalMessageError('处理打开项目消息失败: $e');
    }
  }
}
