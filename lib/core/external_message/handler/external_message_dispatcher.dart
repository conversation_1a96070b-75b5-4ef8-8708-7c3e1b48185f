import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'external_message_base_handler.dart';
import 'external_message_result.dart';

/// 外部消息分发器
class ExternalMessageDispatcher {
  final List<ExternalMessageHandler> _handlers = [];

  /// 注册消息处理器
  void registerHandler(ExternalMessageHandler handler) {
    // 避免重复注册同类型的处理器
    _handlers.removeWhere((h) => h.supportedType == handler.supportedType);
    _handlers.add(handler);
    PGLog.d('注册外部消息处理器: ${handler.handlerName}');
  }

  /// 处理外部消息
  Future<ExternalMessageResult> dispatch(
    BuildContext context,
    ExternalMessage message,
  ) async {
    PGLog.d('分发外部消息: ${message.type.name}');

    final handler =
        _handlers.where((h) => h.supportedType == message.type).firstOrNull;

    if (handler == null) {
      PGLog.w('未找到处理器: ${message.type.name}');
      return ExternalMessageError('不支持的消息类型: ${message.type.name}');
    }

    PGLog.d('使用处理器: ${handler.handlerName}');
    return await handler.handle(context, message);
  }

  /// 获取所有注册的处理器
  List<ExternalMessageHandler> get handlers => List.unmodifiable(_handlers);
}
