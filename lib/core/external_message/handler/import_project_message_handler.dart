import 'package:flutter/material.dart';
import 'package:turing_art/core/tapj/n8_tapj_models.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/tapj_processor.dart';

import 'external_message_base_handler.dart';
import 'external_message_result.dart';

/// 导入项目消息处理器
class ImportProjectMessageHandler extends ExternalMessageHandler {
  final Future<ExternalMessageResult> Function(
    BuildContext context,
    ImportProjectData data,
    Map<String, List<int>>? historyFiles,
  ) _onImportProject;

  ImportProjectMessageHandler({
    required Future<ExternalMessageResult> Function(
      BuildContext context,
      ImportProjectData data,
      Map<String, List<int>>? historyFiles,
    ) onImportProject,
  }) : _onImportProject = onImportProject;

  @override
  String get handlerName => 'ImportProjectHandler';

  @override
  ExternalMessageType get supportedType => ExternalMessageType.importProject;

  @override
  Future<ExternalMessageResult> handle(
    BuildContext context,
    ExternalMessage message,
  ) async {
    try {
      if (!validateMessage(message)) {
        return const ExternalMessageError('无效的导入项目消息格式');
      }

      var dataStr = message.data.toString();
      PGLog.d('导入项目消息数据: $dataStr');

      ImportProjectData? importData;
      Map<String, List<int>>? historyFiles;
      bool newProject = message.data['newProject'] as bool? ?? false;
      bool autoNavigate = message.data['autoNavigate'] as bool? ?? false;

      // 优先处理tapj字段
      if (message.data.containsKey('tapj')) {
        final tapjFilePath = message.data['tapj'] as String?;
        if (tapjFilePath != null && tapjFilePath.isNotEmpty) {
          final result = await _loadImportDataFromTapj(tapjFilePath);
          if (result == null) {
            return const ExternalMessageError('无法从tapj文件加载项目数据');
          }

          // 从tapj结果创建ImportProjectData
          importData =
              _createImportDataFromTapjResult(result, newProject, autoNavigate);
          historyFiles = result.historyFiles;
        }
      } else {
        importData = ImportProjectData.fromMap(message.data);
        if (importData == null) {
          return const ExternalMessageError('无法解析导入项目数据');
        }
      }

      if (importData == null) {
        return const ExternalMessageError('无法获取导入项目数据');
      }

      final validatedData = ImportProjectData(
        projectId: importData.projectId,
        projectName: importData.projectName,
        allFileList: importData.allFileList,
        selectedFileList: importData.selectedFileList,
        autoNavigate: importData.autoNavigate,
        newProject: newProject,
      );

      return await _onImportProject(context, validatedData, historyFiles);
    } catch (e) {
      PGLog.e('处理导入项目消息失败: $e');
      return ExternalMessageError('处理导入项目消息失败: $e');
    }
  }

  /// 从tapj文件加载ImportProjectData
  Future<N8TapjReadResult?> _loadImportDataFromTapj(String tapjFilePath) async {
    // 使用TapjProcessor统一处理tapj文件
    return await TapjProcessor.readTapjFile(tapjFilePath);
  }

  /// 从tapj读取结果创建ImportProjectData
  ImportProjectData _createImportDataFromTapjResult(
      N8TapjReadResult result, bool newProject, bool autoNavigate) {
    // 使用TapjProcessor的合并逻辑来处理tapj文件信息和描述文件信息
    final fileList =
        TapjProcessor.mapTapjFileToFileItem(result.projectData.fileList);

    return ImportProjectData(
      projectId: result.projectData.rawWorkspaceId,
      projectName: result.projectData.workspaceName,
      allFileList: fileList,
      selectedFileList: fileList,
      autoNavigate: autoNavigate,
      newProject: newProject,
    );
  }
}
