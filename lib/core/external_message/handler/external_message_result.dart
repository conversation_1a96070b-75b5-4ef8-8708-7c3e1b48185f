/// 外部消息处理结果
sealed class ExternalMessageResult {
  const ExternalMessageResult();
}

/// 处理成功
class ExternalMessageSuccess extends ExternalMessageResult {
  final String message;
  const ExternalMessageSuccess(this.message);
}

/// 处理失败
class ExternalMessageError extends ExternalMessageResult {
  final String error;
  const ExternalMessageError(this.error);
}

/// 处理中
class ExternalMessageProcessing extends ExternalMessageResult {
  final String message;
  const ExternalMessageProcessing(this.message);
}
