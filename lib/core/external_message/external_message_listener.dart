import 'dart:async';

import 'package:flutter/services.dart';

import '../../utils/pg_log.dart';
import '../platform/channel/channel_constants.dart';

/// 外部消息监听器 - 用于接收从外部应用发送的消息
/// 通过MethodChannel接收底层各个平台实现的接收外部消息方法，最终调用该回调函数
class ExternalMessageListener {
  static final ExternalMessageListener _instance =
      ExternalMessageListener._internal();
  factory ExternalMessageListener() => _instance;
  ExternalMessageListener._internal();

  bool _isInitialized = false;
  Function(String)? _onMessageCallback;

  /// 初始化监听器
  void initialize({Function(String)? onMessage}) {
    if (_isInitialized) {
      PGLog.w('External message listener already initialized');
      return;
    }

    _onMessageCallback = onMessage;

    try {
      PGLog.d('开始初始化外部消息监听器...');
      PGLog.d('通道名称: ${ChannelConstants.externalMessageChannel.name}');

      // 使用MethodChannel设置方法调用处理器
      ChannelConstants.externalMessageChannel
          .setMethodCallHandler(_handleMethodCall);

      _isInitialized = true;
      PGLog.i('External message listener initialized successfully');
    } catch (e, stackTrace) {
      PGLog.e('Failed to initialize external message listener: $e');
      PGLog.e('Stack trace: $stackTrace');
      _isInitialized = false;
    }
  }

  /// 处理方法调用
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    try {
      PGLog.d('收到方法调用: ${call.method}');
      PGLog.d('参数类型: ${call.arguments.runtimeType}');
      PGLog.d('参数内容: ${call.arguments}');

      if (call.method == 'onExternalMessage') {
        String messageText;

        // 直接处理字符串参数
        if (call.arguments is String) {
          messageText = call.arguments as String;
        } else {
          // 如果不是字符串，尝试转换
          messageText = call.arguments?.toString() ?? 'Unknown message';
          PGLog.w('收到非字符串类型的消息，已转换: ${call.arguments.runtimeType}');
        }

        PGLog.i('处理外部消息: $messageText');
        _onMessageCallback?.call(messageText);

        return true;
      } else {
        PGLog.w('未知的方法调用: ${call.method}');
        return false;
      }
    } catch (e, stackTrace) {
      PGLog.e('处理方法调用时出错: $e');
      PGLog.e('堆栈跟踪: $stackTrace');
      return false;
    }
  }

  /// 设置消息回调
  void setMessageCallback(Function(String)? callback) {
    _onMessageCallback = callback;
  }

  /// 销毁监听器
  void dispose() {
    if (_isInitialized) {
      try {
        ChannelConstants.externalMessageChannel.setMethodCallHandler(null);
        _isInitialized = false;
        _onMessageCallback = null;
        PGLog.i('External message listener disposed successfully');
      } catch (e) {
        PGLog.e('Error disposing external message listener: $e');
      }
    }
  }
}
