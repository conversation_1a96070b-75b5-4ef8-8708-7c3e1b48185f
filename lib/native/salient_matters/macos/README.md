# macOS SalientMatters库

此目录用于存放适用于macOS系统的PGSalientMattersLib.dylib文件。

## 使用说明

1. 将适用于macOS的PGSalientMattersLib.dylib文件放置在此目录
2. 构建时Podfile会自动复制此文件到各个配置的输出目录
3. 运行时UniversalPlatformLoader会自动加载此库文件

## 文件要求

- 文件名必须为：`PGSalientMattersLib.dylib`
- 必须支持macOS 10.14+系统版本
- 建议支持x86_64和arm64架构

## 构建集成

此库文件会在以下情况下被自动复制：
- Pod install/update时
- Flutter构建时（通过Podfile配置）

## 注意事项

- 请确保dylib文件已正确签名
- 库文件应当与项目中使用的API接口版本匹配
- 如有更新，请同时更新相关文档和版本信息 