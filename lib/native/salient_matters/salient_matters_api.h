#pragma once

#ifdef _WIN32
    #ifdef SALIENT_MATTERS_EXPORTS
        #define SALIENT_MATTERS_API __declspec(dllexport)
    #else
        #define SALIENT_MATTERS_API __declspec(dllimport)
    #endif
#else
    #define SALIENT_MATTERS_API __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 错误码定义
typedef enum {
    SALIENT_MATTERS_SUCCESS = 0,
    SALIENT_MATTERS_ERROR_INIT_FAILED = -1,
    SALIENT_MATTERS_ERROR_NOT_INITIALIZED = -2,
    SALIENT_MATTERS_ERROR_INVALID_PARAM = -3,
    SALIENT_MATTERS_ERROR_FILE_NOT_FOUND = -4,
    SALIENT_MATTERS_ERROR_PROCESS_FAILED = -5,
    SALIENT_MATTERS_ERROR_SAVE_FAILED = -6,
    SALIENT_MATTERS_ERROR_UNKNOWN = -99
} SalientMattersErrorCode;

// 算法类型
typedef enum {
    SALIENT_TYPE_INTERACTIVE = 0,  // 交互式抠图
    SALIENT_TYPE_MATTING = 1,      // 主体抠图
    SALIENT_TYPE_TRACKING = 2      // 蒙版跟踪
} SalientMattersType;

// 处理选项结构体
typedef struct {
    unsigned int net_size;      // 神经网络输入尺寸，应为32的倍数，默认512
    SalientMattersType type;    // 算法类型
    const char* key;           // 权限密钥
    const char* user_code;     // 用户编码
    const char* prod_code;     // 产品编码
    unsigned char output_color[4]; // 输出颜色 [R, G, B, A]，RGB各通道乘以蒙版值，Alpha直接使用此参数，默认红色+0.3的透明度
} SalientMattersOptions;

// 图像数据结构体
typedef struct {
    unsigned char* data;    // 图像数据指针
    int width;             // 图像宽度
    int height;            // 图像高度
    int channels;          // 通道数
    int step;              // 行字节数
} SalientMattersImage;

// 新增：裁剪/贴图区域结构体
typedef struct {
    int x;      // 区域的左上角x坐标
    int y;      // 区域的左上角y坐标  
    int width;  // 区域的宽度
    int height; // 区域的高度
} SalientMattersRect;

/**
 * 初始化SalientMatters
 * @param key 权限密钥
 * @param user_code 用户编码
 * @param prod_code 产品编码
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_Init(const char* key,
                                           const char* user_code,
                                           const char* prod_code,
                                           char* error_message,
                                           int error_message_size);

/**
 * 执行交互式抠图
 * @param image 输入图像（RGBA格式或者RGB格式）
 * @param latest_mask 上一次的抠图结果，首次可以传入空的mask（单通道或4通道，4通道时取R通道）
 * @param fore_strokes 前景交互笔触（单通道或4通道，4通道时取R通道，255表示前景）
 * @param back_strokes 背景交互笔触（单通道或4通道，4通道时取R通道，255表示背景）
 * @param result 输出抠图结果（RGBA格式，基于options中的output_color参数生成）
 * @param options 处理选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_Interactive(const SalientMattersImage* image,
                                                  const SalientMattersImage* latest_mask,
                                                  const SalientMattersImage* fore_strokes,
                                                  const SalientMattersImage* back_strokes,
                                                  SalientMattersImage* result,
                                                  const SalientMattersOptions* options,
                                                  char* error_message,
                                                  int error_message_size);

/**
 * 执行主体抠图
 * @param image 输入图像（RGBA或RGB格式，内部会自动转换为BGR进行处理）
 * @param result 输出抠图结果（RGBA格式，基于options中的output_color参数生成）
 * @param options 处理选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_Matting(const SalientMattersImage* image,
                                              SalientMattersImage* result,
                                              const SalientMattersOptions* options,
                                              char* error_message,
                                              int error_message_size);

/**
 * 执行蒙版跟踪
 * @param curr_image 当前帧图像（BGR格式）
 * @param prev_image 前一帧图像（BGR格式）
 * @param prev_mask 前一帧蒙版
 * @param result 输出跟踪结果（单通道mask）
 * @param options 处理选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_Tracking(const SalientMattersImage* curr_image,
                                               const SalientMattersImage* prev_image,
                                               const SalientMattersImage* prev_mask,
                                               SalientMattersImage* result,
                                               const SalientMattersOptions* options,
                                               char* error_message,
                                               int error_message_size);

/**
 * 释放SalientMatters资源
 */
SALIENT_MATTERS_API void SalientMatters_Free();

/**
 * 获取默认选项
 * @param options 输出的默认选项
 */
SALIENT_MATTERS_API void SalientMatters_GetDefaultOptions(SalientMattersOptions* options);

/**
 * 检查是否已初始化
 * @return 1表示已初始化，0表示未初始化
 */
SALIENT_MATTERS_API int SalientMatters_IsInitialized();

/**
 * 分配图像内存
 * @param image 图像结构体指针
 * @param width 图像宽度
 * @param height 图像高度
 * @param channels 通道数
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_AllocateImage(SalientMattersImage* image,
                                                    int width,
                                                    int height,
                                                    int channels);

/**
 * 释放图像内存
 * @param image 图像结构体指针
 */
SALIENT_MATTERS_API void SalientMatters_FreeImage(SalientMattersImage* image);

/**
 * 处理前景和后景的交互蒙版数据
 * @param stroke_mask 当前涂抹/擦除的路径蒙版，不能为空
 * @param prev_foreground_mask 上一次的前景蒙版数据（单通道），可以为空
 * @param prev_background_mask 上一次的后景蒙版数据（单通道），可以为空
 * @param is_painting 标记是涂抹(true)还是擦除(false)
 * @param new_foreground_mask 输出的新前景蒙版数据（调用者负责释放内存）
 * @param new_background_mask 输出的新后景蒙版数据（调用者负责释放内存）
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_ProcessInteractiveMasks(const SalientMattersImage* stroke_mask,
                                                               const unsigned char* prev_foreground_mask,
                                                               const unsigned char* prev_background_mask,
                                                               int is_painting,
                                                               unsigned char** new_foreground_mask,
                                                               unsigned char** new_background_mask,
                                                               char* error_message,
                                                               int error_message_size);

/**
 * 执行区域主体抠图
 * @param image 输入的完整图像（RGBA或RGB格式）
 * @param cropped_image 可选：预裁剪的区域图像A，如果为NULL则内部按region裁剪
 * @param previous_full_result 可选：上次的完整图像抠图结果B（RGBA格式），用于回贴结果。
 *                            如果为NULL，则创建一个与输入图像同尺寸的4通道透明图层，
 *                            将区域主体抠图结果粘贴到对应位置
 * @param region 要处理的区域信息
 * @param result 输出抠图结果（RGBA格式，完整图像尺寸）
 * @param cropped_result 可选：输出裁剪后的区域图像，如果传入cropped_image为空且此参数不为NULL，则返回裁剪结果
 * @param cropped_matting_result 可选：输出裁剪区域的主体抠图结果（RGBA格式，区域尺寸）
 * @param options 处理选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_RegionalMatting(const SalientMattersImage* image,
                                                      const SalientMattersImage* cropped_image,
                                                      const SalientMattersImage* previous_full_result,
                                                      const SalientMattersRect* region,
                                                      SalientMattersImage* result,
                                                      SalientMattersImage* cropped_result,
                                                      SalientMattersImage* cropped_matting_result,
                                                      const SalientMattersOptions* options,
                                                      char* error_message,
                                                      int error_message_size);

/**
 * 执行区域交互式抠图
 * @param cropped_image 输入的已裁剪区域图像（RGBA或RGB格式），与latest_mask、fore_strokes、back_strokes尺寸一致
 * @param latest_mask 上一次的区域抠图结果（与cropped_image大小一致）
 * @param fore_strokes 前景交互笔触（区域大小）
 * @param back_strokes 背景交互笔触（区域大小）
 * @param latest_full_mask 上一次的完整图像抠图结果（完整图像尺寸）
 * @param region 要处理的区域信息
 * @param result 输出抠图结果（RGBA格式，完整图像尺寸）
 * @param regional_result 输出区域交互式抠图结果（RGBA格式，区域尺寸），可以为NULL
 * @param options 处理选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return SALIENT_MATTERS_SUCCESS表示成功，其他值表示错误码
 */
SALIENT_MATTERS_API int SalientMatters_RegionalInteractive(const SalientMattersImage* cropped_image,
                                                          const SalientMattersImage* latest_mask,
                                                          const SalientMattersImage* fore_strokes,
                                                          const SalientMattersImage* back_strokes,
                                                          const SalientMattersImage* latest_full_mask,
                                                          const SalientMattersRect* region,
                                                          SalientMattersImage* result,
                                                          SalientMattersImage* regional_result,
                                                          const SalientMattersOptions* options,
                                                          char* error_message,
                                                          int error_message_size);

/**
 * 释放由SalientMatters库分配的内存
 * @param ptr 要释放的内存指针的地址，释放后会被设置为NULL
 */
SALIENT_MATTERS_API void SalientMatters_FreeMemory(unsigned char** ptr);

#ifdef __cplusplus
}
#endif 