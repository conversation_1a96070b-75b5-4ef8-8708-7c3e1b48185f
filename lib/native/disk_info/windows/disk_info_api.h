#ifndef DISK_INFO_API_H
#define DISK_INFO_API_H

#if defined(_WIN32)
    #ifdef DISK_INFO_EXPORTS
        #define DISK_INFO_API __declspec(dllexport)
    #else
        #define DISK_INFO_API __declspec(dllimport)
    #endif
#else
    #define DISK_INFO_API
#endif

#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

DISK_INFO_API bool get_disk_space(const char* path, uint64_t& total_space, uint64_t& free_space);

#ifdef __cplusplus
}
#endif

#endif // DISK_INFO_API_H 