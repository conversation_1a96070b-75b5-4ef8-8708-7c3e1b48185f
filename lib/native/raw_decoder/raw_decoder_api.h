#pragma once

#ifdef _WIN32
    #ifdef RAW_DECODER_EXPORTS
        #define RAW_DECODER_API __declspec(dllexport)
    #else
        #define RAW_DECODER_API __declspec(dllimport)
    #endif
#else
    #define RAW_DECODER_API __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 错误码定义
typedef enum {
    RAW_DECODER_SUCCESS = 0,
    RAW_DECODER_ERROR_INIT_FAILED = -1,
    RAW_DECODER_ERROR_NOT_INITIALIZED = -2,
    RAW_DECODER_ERROR_INVALID_PARAM = -3,
    RAW_DECODER_ERROR_FILE_NOT_FOUND = -4,
    RAW_DECODER_ERROR_DECODE_FAILED = -5,
    RAW_DECODER_ERROR_SAVE_FAILED = -6,
    RAW_DECODER_ERROR_UNKNOWN = -99
} RawDecoderErrorCode;

// 解码选项结构体
typedef struct {
    int bit_depth;           // 位深度，默认8
    int max_dim;            // 最大尺寸，0表示原始尺寸
    int thumbnail;          // 是否生成缩略图，0=否，1=是
    int denoise;            // 是否降噪，0=否，1=是
    int clarity;            // 是否锐化，0=否，1=是
    float exposure;         // 曝光补偿
    const char* output_format;  // 输出格式："jpg", "jpeg", "png"
} RawDecoderOptions;

/**
 * 初始化Raw解码器
 * @param num_threads 线程数，0表示自动检测
 * @param bundle_path Bundle文件路径，可以为NULL使用默认路径
 * @param xt_key 外部传入的key，用于SDK初始化
 * @return RAW_DECODER_SUCCESS表示成功，其他值表示错误码
 */
RAW_DECODER_API int RawDecoder_Init(int num_threads, const char* bundle_path, const char* xt_key);

/**
 * 处理单个RAW文件
 * @param input_file 输入RAW文件路径
 * @param output_file 输出图像文件路径
 * @param options 解码选项，可以为NULL使用默认选项
 * @param error_message 错误信息输出缓冲区，可以为NULL
 * @param error_message_size 错误信息缓冲区大小
 * @return RAW_DECODER_SUCCESS表示成功，其他值表示错误码
 */
RAW_DECODER_API int RawDecoder_Process(const char* input_file, 
                                      const char* output_file,
                                      const RawDecoderOptions* options,
                                      char* error_message,
                                      int error_message_size);

/**
 * 释放Raw解码器资源
 */
RAW_DECODER_API void RawDecoder_Free();

/**
 * 获取默认解码选项
 * @param options 输出的默认选项
 */
RAW_DECODER_API void RawDecoder_GetDefaultOptions(RawDecoderOptions* options);

/**
 * 检查是否已初始化
 * @return 1表示已初始化，0表示未初始化
 */
RAW_DECODER_API int RawDecoder_IsInitialized();

#ifdef __cplusplus
}
#endif 