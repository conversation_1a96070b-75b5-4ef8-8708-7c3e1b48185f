# 跨平台原生库管理

本目录用于管理项目中所有跨平台的原生动态库文件。

## 目录结构

```
lib/native/
├── README.md                           # 本文件
├── salient_matters/                    # SalientMatters库
│   ├── salient_matters_api.h          # 头文件
│   ├── windows/
│   │   ├── win7/
│   │   │   └── PGSalientMattersLib.dll
│   │   └── win10/
│   │       └── PGSalientMattersLib.dll
│   └── macos/
│       └── PGSalientMattersLib.dylib
├── raw_conversion/                     # RawConversion库
│   ├── raw_conversion_api.h           # 头文件
│   ├── windows/
│   │   ├── win7/
│   │   │   └── PGRawConversionLib.dll
│   │   └── win10/
│   │       └── PGRawConversionLib.dll
│   └── macos/
│       └── PGRawConversionLib.dylib
├── raw_decoder/                        # RawDecoder库
│   ├── raw_decoder_api.h
│   └── windows/
│       └── PGRawDecoderLib.dll
└── image_processor/                    # ImageProcessor库
    ├── image_processor_api.h
    └── windows/
        └── PGImageProcessorLib.dll
```

## 命名规范

### 文件命名
- **Windows**: `{LibraryName}Lib.dll`
- **macOS**: `lib{LibraryName}.dylib`
- **Linux**: `lib{LibraryName}.so`

### 目录命名
- 库名使用下划线分隔，如：`salient_matters`
- 平台目录：`windows`, `macos`, `linux`
- Windows子版本：`win7`, `win10`

## 库加载机制

项目使用 `UniversalPlatformLoader` 统一管理跨平台库加载：

### 自动版本检测
- Windows平台会自动检测系统版本
- Windows 10/11 使用 `win10` 目录下的库
- Windows 7/8 使用 `win7` 目录下的库

### 搜索路径优先级
1. `lib/native/{library_name}/{platform_dir}/{library_file}`
2. 可执行文件同目录
3. 当前工作目录（仅Windows）
4. 系统路径

## 使用方法

### 基本用法
```dart
import 'package:turing_art/ffi/native/universal_platform_loader.dart';

// 加载SalientMatters库
final lib = UniversalPlatformLoader.loadLibrary(
  'PGSalientMatters',
  subDirectory: 'salient_matters',
);
```

### 获取库信息
```dart
// 获取详细的库信息用于调试
final info = UniversalPlatformLoader.getLibraryInfo(
  'PGSalientMatters',
  subDirectory: 'salient_matters',
);

print('平台: ${info['platform']}');
print('库文件名: ${info['fileName']}');
print('可以加载: ${info['canLoad']}');
```

### 测试库加载
```dart
import 'package:turing_art/ffi/native/library_test.dart';

// 运行所有库加载测试
LibraryLoadTest.runAllTests();
```

## 构建配置

### Windows (CMakeLists.txt)
项目会根据目标Windows版本自动选择合适的库文件进行打包。

### macOS (Podfile)
Pod安装时会自动复制dylib文件到构建目录。

## 添加新库

1. 在 `lib/native/` 下创建新的库目录
2. 按平台组织库文件
3. 如有必要，添加对应的FFI绑定类
4. 更新构建配置文件

## 注意事项

1. **库文件大小**: 注意库文件大小，避免包体过大
2. **版本兼容性**: 确保库文件与目标平台版本兼容
3. **依赖关系**: 注意库之间的依赖关系
4. **许可证**: 确保所有库文件的许可证合规

## 调试

如果遇到库加载问题：

1. 使用 `LibraryLoadTest.runAllTests()` 进行诊断
2. 检查库文件是否存在于正确路径
3. 验证库文件是否与当前平台兼容
4. 查看日志中的详细错误信息 