import 'package:turing_art/core/service/disk_space_size/disk_free_space_size_service.dart';
import 'package:turing_art/ui/use_case/generate_workspace_disk_space_guard_use_case.dart';
import 'package:turing_art/ui/use_case/open_workspace_disk_space_guard_use_case.dart';

class WorkspaceU<PERSON><PERSON>aseProvider {
  final GenerateWorkspaceDiskSpaceGuardUseCase generateWorkspaceDiskSpaceGuard;
  final OpenWorkspaceDiskSpaceGuardUseCase openWorkspaceDiskSpaceGuard;

  WorkspaceUseCaseProvider()
      : generateWorkspaceDiskSpaceGuard =
            GenerateWorkspaceDiskSpaceGuardUseCase(
          DiskFreeSpaceSizeService.forPlatform(),
        ),
        openWorkspaceDiskSpaceGuard = OpenWorkspaceDiskSpaceGuardUseCase(
          DiskFreeSpaceSizeService.forPlatform(),
        );
}
