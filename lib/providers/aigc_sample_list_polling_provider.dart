import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 打样列表状态轮询提供者
class AigcSampleListPollingProvider extends ChangeNotifier {
  final AigcSampleRepository _repository;

  // 轮询流订阅
  StreamSubscription<int>? _pollingSubscription;

  // 轮询间隔（秒）
  static const int _pollingInterval = 10;

  // 当前需要轮询的ID列表
  List<String> _pollingIds = [];

  // 当前项目ID
  String? _currentProjectId;
  String? get currentProjectId => _currentProjectId;

  // 是否正在轮询
  bool _isPolling = false;
  bool get isPolling => _isPolling;

  AigcSampleListPollingProvider(this._repository);

  /// 开始轮询打样状态
  void startPolling(List<String> ids, [String? projectId]) {
    if (ids.isEmpty) {
      stopPolling();
      return;
    }

    // 如果已经在轮询相同的ID列表，不需要重新开始
    if (_isPolling &&
        _listEquals(_pollingIds, ids) &&
        _currentProjectId == projectId) {
      return;
    }

    // 停止之前的轮询
    stopPolling();

    _pollingIds = List.from(ids);
    _currentProjectId = projectId;
    _isPolling = true;

    // 立即执行一次查询
    _pollProofingStatus();

    // 使用Stream.periodic创建定时轮询流
    _pollingSubscription = Stream.periodic(
            const Duration(seconds: _pollingInterval), (count) => count)
        .listen((_) {
      _pollProofingStatus();
    });

    PGLog.d('开始轮询打样状态，ID列表: $_pollingIds, 项目ID: $_currentProjectId');
    notifyListeners();
  }

  /// 停止轮询打样状态
  void stopPolling() {
    if (_pollingSubscription != null) {
      _pollingSubscription?.cancel();
      _pollingSubscription = null;
      _isPolling = false;
      _pollingIds.clear();
      _currentProjectId = null;
      PGLog.d('停止轮询打样状态');
    }
  }

  /// 轮询打样状态
  Future<void> _pollProofingStatus() async {
    if (_pollingIds.isEmpty) {
      return;
    }

    try {
      final statusList = await _repository.getAigcSampleProofingStatus(
          _pollingIds, _currentProjectId);
      PGLog.d(
          '轮询打样状态成功，ID数量: ${_pollingIds.length}, 项目ID: $_currentProjectId, 状态数量: ${statusList.length}');
      PGLog.d('轮询打样状态成功，状态列表: $statusList');
    } catch (e) {
      PGLog.e('轮询打样状态出错: $e');
    }
  }

  /// 比较两个列表是否相等
  bool _listEquals(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) {
      return false;
    }

    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) {
        return false;
      }
    }

    return true;
  }

  @override
  void dispose() {
    stopPolling();
    super.dispose();
  }
}
