import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_list_export_request.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 打样导出状态轮询提供者
class AigcSampleExportPollingProvider extends ChangeNotifier {
  final AigcSampleRepository _repository;

  // 轮询流订阅
  StreamSubscription<int>? _pollingSubscription;

  // 轮询间隔（秒）
  static const int _pollingInterval = 10;

  // 当前需要轮询的ID列表
  List<AigcSampleListExportRequest> _pollingRequests = [];

  // 当前项目ID
  String? _currentProjectId;
  String? get currentProjectId => _currentProjectId;

  // 是否正在轮询
  bool _isPolling = false;
  bool get isPolling => _isPolling;

  AigcSampleExportPollingProvider(this._repository);

  /// 开始轮询打样导出状态
  void startPolling(List<AigcSampleListExportRequest> requests,
      [String? projectId]) {
    if (requests.isEmpty) {
      stopPolling();
      return;
    }

    // 如果已经在轮询相同的ID列表，不需要重新开始
    if (_isPolling &&
        _listEquals(_pollingRequests, requests) &&
        _currentProjectId == projectId) {
      return;
    }

    // 停止之前的轮询
    stopPolling();

    _pollingRequests = requests;
    _currentProjectId = projectId;
    _isPolling = true;

    // 立即执行一次查询
    _pollExportStatus();

    // 使用Stream.periodic创建定时轮询流
    _pollingSubscription = Stream.periodic(
            const Duration(seconds: _pollingInterval), (count) => count)
        .listen((_) {
      _pollExportStatus();
    });

    PGLog.d('开始轮询打样导出状态，ID列表: $_pollingRequests, 项目ID: $_currentProjectId');
    notifyListeners();
  }

  /// 停止轮询打样导出状态
  void stopPolling() {
    if (_pollingSubscription != null) {
      _pollingSubscription?.cancel();
      _pollingSubscription = null;
      _isPolling = false;
      _pollingRequests.clear();
      _currentProjectId = null;
      PGLog.d('停止轮询打样导出状态');
    }
  }

  /// 轮询打样导出状态
  Future<void> _pollExportStatus() async {
    if (_pollingRequests.isEmpty) {
      return;
    }

    try {
      final statusList = await _repository
          .getAigcSampleExportStatus(_pollingRequests, isMyExportList: false);
      PGLog.d(
          '轮询打样导出状态成功，ID数量: ${_pollingRequests.length}, 项目ID: $_currentProjectId, 状态数量: ${statusList.length}');
    } catch (e) {
      PGLog.e('轮询打样导出状态出错: $e');
    }
  }

  /// 比较两个列表是否相等
  bool _listEquals(List<AigcSampleListExportRequest> list1,
      List<AigcSampleListExportRequest> list2) {
    if (list1.length != list2.length) {
      return false;
    }
    return list1 == list2;
  }

  @override
  void dispose() {
    stopPolling();
    super.dispose();
  }
}
