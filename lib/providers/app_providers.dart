import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:turing_art/config/env_config.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/core/manager/disk_cache_manager/disk_cache_manager_impl.dart';
import 'package:turing_art/core/manager/expiry_manager.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/service/disk_cache_manager/cache_cleanup_with_export_pause.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/core/service/disk_cache_manager/project_disk_service/project_disk_service.dart';
import 'package:turing_art/core/service/disk_cache_manager/remote_image_disk_service/remote_image_disk_service.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/core/unity/unity_message_sender.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/account_repository_impl.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository_impl.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repositoty_impl.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/datalayer/repository/auth_repository_impl.dart';
import 'package:turing_art/datalayer/repository/coupon_repository.dart';
import 'package:turing_art/datalayer/repository/coupon_repository_impl.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository_impl.dart';
import 'package:turing_art/datalayer/repository/employee_repository.dart';
import 'package:turing_art/datalayer/repository/employee_repository_impl.dart';
import 'package:turing_art/datalayer/repository/export_history_repository.dart';
import 'package:turing_art/datalayer/repository/export_history_repository_impl.dart';
import 'package:turing_art/datalayer/repository/export_records_repository.dart';
import 'package:turing_art/datalayer/repository/export_records_repository_impl3.dart';
import 'package:turing_art/datalayer/repository/export_repository.dart';
import 'package:turing_art/datalayer/repository/export_repository_impl.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository_impl.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/repository/new_user_repository_impl.dart';
import 'package:turing_art/datalayer/repository/photo_thumbnail_repository.dart';
import 'package:turing_art/datalayer/repository/photo_thumbnail_repository_impl.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository_impl.dart';
import 'package:turing_art/datalayer/repository/purchase_repository.dart';
import 'package:turing_art/datalayer/repository/purchase_repository_impl.dart';
import 'package:turing_art/datalayer/repository/recharge_record_repository.dart';
import 'package:turing_art/datalayer/repository/recharge_record_repository_impl.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository_impl.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/datalayer/repository/setting_repository_impl.dart';
import 'package:turing_art/datalayer/repository/shortcut_keys_repository.dart';
import 'package:turing_art/datalayer/repository/shortcut_keys_repository_impl.dart';
import 'package:turing_art/datalayer/repository/user_repository.dart';
import 'package:turing_art/datalayer/repository/user_repository_impl.dart';
import 'package:turing_art/datalayer/repository/version_intro_repository.dart';
import 'package:turing_art/datalayer/repository/version_intro_repository_impl.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository_impl.dart';
import 'package:turing_art/datalayer/service/account/account_service.dart';
import 'package:turing_art/datalayer/service/aigc_presets/aigc_presets_service.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/aigc_sample/aigc_sample_service.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/coupon/coupon_service.dart';
import 'package:turing_art/datalayer/service/current_user_store_info/current_user_store_info_service.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/datalayer/service/employee/employee_service.dart';
import 'package:turing_art/datalayer/service/export_history/export_history_service.dart';
import 'package:turing_art/datalayer/service/media_upload/media_upload_service.dart';
import 'package:turing_art/datalayer/service/ops_operation/ops_operation_service.dart';
import 'package:turing_art/datalayer/service/purchase/purchase_default_data_service.dart';
import 'package:turing_art/datalayer/service/purchase/purchase_service.dart';
import 'package:turing_art/datalayer/service/recharge_record/recharge_record_service.dart';
import 'package:turing_art/datalayer/service/reward/operation_reward_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository_impl.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/providers/aigc_my_export_list_polling_provider.dart';
import 'package:turing_art/providers/aigc_sample_detail_polling_provider.dart';
import 'package:turing_art/providers/aigc_sample_export_polling_provider.dart';
import 'package:turing_art/providers/aigc_sample_list_polling_provider.dart';
import 'package:turing_art/providers/debug_menu_provider.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/purchase_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_mask_acquisition_provider.dart';
import 'package:turing_art/ui/export_result/use_case/export_task_events.dart';
import 'package:turing_art/ui/export_result/use_case/export_task_manager.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/ui/export_result/use_case/project_state_export_task_driver.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/setting/provider/current_cache_rule_provider.dart';
import 'package:turing_art/ui/setting/provider/current_device_information_provider.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/shortcut/service/shortcut_key_service.dart';

import 'workspace_provider.dart';

class AppProviders {
  static List<SingleChildWidget> providers(SharedPreferences preferences) => [
        // 第四层：调试菜单提供者(挪动到第一个初始化的位置，因为后面的网络请求会依赖其中的代理配置)
        _debugMenuProvider(preferences),

        // 第一层：核心服务提供者
        _coreProviders(preferences),

        // 第二层：数据仓库服务(数据准备优先)
        _repositoryProviders,

        // 我的打样导出状态轮询提供者（依赖repository，AigcSampleExportManager依赖pollingProvider，所以注入位置只能放这里）
        _myExportListPollingProvider(),

        // 第三层：管理器服务
        _managerProviders,

        // unity
        _unityProviders,

        // 购买状态提供者
        _purchaseStateProvider(),

        // 微信状态提供者
        _wechatAddedStateProvider(),

        // AIGC轮询提供者
        _aigcPollingProviders(),

        // 工作区提供者
        _workspaceProvider(),

        // 快捷键提供者
        _shortcutKeyProviders,

        // useCase
        _useCaseProviders,

        // 导出管理器
        _exportProviders,
      ].expand((element) => element).toList();

  // 核心服务 Providers
  static List<SingleChildWidget> _coreProviders(
          SharedPreferences preferences) =>
      [
        Provider<SharedPreferences>.value(value: preferences),
        Provider<DataBase>(
          create: (_) => DataBase(FileManager().dbPath),
          lazy: false,
        ),
        Provider<ApiClient>(
          create: (context) {
            final ip = context.read<DebugMenuProvider>().proxyIp;
            final port = context.read<DebugMenuProvider>().proxyPort;
            String? proxyUrl;
            if (ip.isNotEmpty && port.isNotEmpty) {
              proxyUrl = "$ip:$port";
            }
            return ApiClient(baseUrl: EnvConfig.baseUrl, proxyUrl: proxyUrl);
          },
          lazy: false,
        ),
        ChangeNotifierProvider<NetworkProvider>(
          create: (_) => NetworkProvider(),
          lazy: false,
        ),
      ];

  // 我的打样导出状态轮询提供者
  static List<SingleChildWidget> _myExportListPollingProvider() => [
        ChangeNotifierProvider<AigcMyExportListPollingProvider>(
          create: (context) => AigcMyExportListPollingProvider(
            context.read<AigcSampleRepository>(),
          ),
        ),
        // 图像处理服务Provider
        ChangeNotifierProvider<AigcService>(
          create: (_) => AigcService(),
          lazy: false, // 立即初始化
        ),
      ];

  // 管理器服务 Providers
  static List<SingleChildWidget> get _managerProviders => [
        Provider<ExpiryManager>(
          create: (context) {
            return ExpiryManager(
              apiClient: context.read<ApiClient>(),
              customTableRepository: context.read<OpsCustomTableRepository>(),
            );
          },
          lazy: false,
        ),
        Provider<NoviceGuideManager>(
          create: (context) => NoviceGuideManager(
            currentUserRepository: context.read<CurrentUserRepository>(),
          ),
        ),
        ChangeNotifierProvider<CurrentDeviceInformationProvider>(
          create: (_) => CurrentDeviceInformationProvider(),
        ),
        ChangeNotifierProvider<ProjectCacheRuleProvider>(
          create: (_) => ProjectCacheRuleProvider(),
        ),
        // 缓存管理器
        Provider<DiskCacheManager>(
          create: (context) {
            final diskCacheManager = DiskCacheManagerImpl();
            // 注册servers
            diskCacheManager.registerService(RemoteImageDiskService());
            diskCacheManager.registerService(ProjectDiskService(
              dbOperater: DbOperater(),
              projectCacheRuleProvider:
                  context.read<ProjectCacheRuleProvider>(),
            ));
            return diskCacheManager;
          },
        ),
      ];

  // 数据仓库服务 Providers
  static List<SingleChildWidget> get _repositoryProviders => [
        // 用户状态
        Provider<CurrentUserRepository>(
          create: (context) {
            return CurrentUserRepositoryImpl(
              db: context.read<DataBase>(),
              currentUserStoreInfoService: CurrentUserStoreInfoServiceImpl(
                context.read<ApiClient>(),
              ),
            );
          },
          lazy: false,
        ),

        // 认证仓库
        ProxyProvider2<ApiClient, DataBase, AuthRepository>(
          update: (context, apiClient, db, _) {
            PGLog.d('ProxyProvider2 update create AuthRepositoryImpl');
            return AuthRepositoryImpl(
              db: db,
              apiClient: apiClient,
            );
          },
          lazy: false,
        ),

        // 媒体仓库
        Provider<MediaRepository>(
          create: (_) => MediaRepositoryImpl(
            dbOperater: DbOperater(),
            fileManager: FileManager(),
          ),
        ),

        // 项目仓库
        Provider<ProjectRepository>(
          create: (context) => ProjectRepositoryImpl(DbOperater()),
          lazy: true,
        ),

        // 导出仓库
        ProxyProvider2<ApiClient, DataBase, ExportRepository>(
          update: (context, apiClient, db, _) {
            PGLog.d('ProxyProvider2 update create ExportRepositoryImpl');
            return ExportRepositoryImpl(
              apiClient: apiClient,
              db: db,
            );
          },
        ),

        // 用户仓库
        Provider<UserRepository>(
          create: (context) => UserRepositoryImpl(
            db: context.read<DataBase>(),
          ),
          lazy: false,
        ),

        // 设置仓库
        Provider<SettingRepository>(
          create: (context) => SettingRepositoryImpl(),
          lazy: false,
        ),

        // 项目状态
        ChangeNotifierProvider<ProjectStateProvider>(
          create: (_) => ProjectStateProvider(),
        ),

        // 快捷键仓库
        Provider<ShortcutKeysRepository>(
          create: (_) => ShortcutKeysRepositoryImpl(),
        ),

        // 套餐仓库
        Provider<PurchaseRepository>(
          create: (context) => PurchaseRepositoryImpl(
            PurchaseServiceImpl(
              context.read<ApiClient>(),
            ),
            PurchaseDefaultDataService(),
          ),
        ),

        // 账户信息
        Provider<AccountRepository>(
          create: (context) => AccountRepositoryImpl(
            AccountServiceImpl(
              context.read<ApiClient>(),
            ),
          ),
        ),

        // 子账号管理仓库
        Provider<EmployeeRepository>(
          create: (context) => EmployeeRepositoryImpl(
            EmployeeServiceImpl(
              context.read<ApiClient>(),
            ),
          ),
        ),

        // 新用户权益仓库
        Provider<NewUserRepository>(
          create: (context) {
            // 创建通用的操作服务
            final opsOperationService = OpsOperationServiceImpl(
              context.read<ApiClient>(),
            );
            return NewUserRepositoryImpl(opsOperationService);
          },
          lazy: false,
        ),

        // 微信福利仓库
        Provider<WechatGiftRepository>(
          create: (context) {
            // 创建通用的操作服务
            final opsOperationService = OpsOperationServiceImpl(
              context.read<ApiClient>(),
            );
            return WechatGiftRepositoryImpl(opsOperationService);
          },
        ),

        // 版本介绍仓库
        Provider<VersionIntroRepository>(
          create: (context) {
            // 创建通用的操作服务
            final opsOperationService = OpsOperationServiceImpl(
              context.read<ApiClient>(),
            );
            return VersionIntroRepositoryImpl(opsOperationService);
          },
        ),

        // 导出明细仓库
        Provider<ExportHistoryRepository>(
          create: (context) => ExportHistoryRepositoryImpl(
            ExportHistoryServiceImpl(
              context.read<ApiClient>(),
            ),
          ),
        ),

        // 充值记录仓库
        Provider<RechargeRecordRepository>(
          create: (context) => RechargeRecordRepositoryImpl(
            RechargeRecordServiceImpl(
              context.read<ApiClient>(),
            ),
          ),
        ),

        // ops自定义表格仓库
        Provider<OpsCustomTableRepository>(
          create: (ref) => OpsCustomTableRepositoryImpl(),
          lazy: false,
        ),

        // 活动奖励上报仓库
        Provider<RewardRepository>(
          create: (context) => RewardRepositoryImpl(
            OperationRewardService(context.read<ApiClient>()),
            context.read<CurrentUserRepository>(),
          ),
          lazy: false,
        ),

        // 优惠券仓库
        Provider<CouponRepository>(
          create: (context) => CouponRepositoryImpl(
            CouponServiceImpl(
              context.read<ApiClient>(),
            ),
          ),
        ),

        // 照片缩略图库
        Provider<PhotoThumbnailRepository>(
          create: (ref) => PhotoThumbnailRepositoryImpl(),
        ),

        // AIGC预设仓库
        Provider<AigcPresetsRepository>(
          create: (context) => AigcPresetsRepositoryImpl(
            AigcPresetsServiceImpl(context.read<ApiClient>()),
          ),
        ),

        // AIGC样本仓库
        Provider<AigcSampleRepository>(
          create: (context) => AigcSampleRepositoryImpl(
            AigcSampleServiceImpl(context.read<ApiClient>()),
          ),
        ),

        Provider<MediaUploadRepository>(
          create: (context) => MediaUploadRepositoryImpl(
            uploadService: MediaUploadServiceImpl(context.read<ApiClient>()),
          ),
        ),

        // AIGC入口管理器
        Provider<AigcEntranceManager>(
          create: (context) => AigcEntranceManager(
            opsCustomTableRepository: context.read<OpsCustomTableRepository>(),
            currentUserRepository: context.read<CurrentUserRepository>(),
          ),
        ),
      ];

  // 添加购买状态提供者
  static List<SingleChildWidget> _purchaseStateProvider() => [
        ChangeNotifierProvider<PurchaseStateProvider>(
          create: (context) => PurchaseStateProvider(
            context.read<PurchaseRepository>(),
            context.read<AccountRepository>(),
          ),
        ),
      ];

  // 添加微信状态提供者
  static List<SingleChildWidget> _wechatAddedStateProvider() => [
        ChangeNotifierProvider<AccountRightsStateProvider>(
          create: (context) => AccountRightsStateProvider(
            context.read<CurrentUserRepository>(),
            context.read<AccountRepository>(),
          ),
        ),
      ];

  // 添加AIGC轮询提供者
  static List<SingleChildWidget> _aigcPollingProviders() => [
        ChangeNotifierProvider<AigcSampleListPollingProvider>(
          create: (context) => AigcSampleListPollingProvider(
            context.read<AigcSampleRepository>(),
          ),
        ),
        ChangeNotifierProvider<AigcSampleDetailPollingProvider>(
          create: (context) => AigcSampleDetailPollingProvider(
            context.read<AigcSampleRepository>(),
          ),
        ),
        ChangeNotifierProvider<AigcSampleExportPollingProvider>(
          create: (context) => AigcSampleExportPollingProvider(
            context.read<AigcSampleRepository>(),
          ),
        ),
        ChangeNotifierProvider<AigcMaskAcquisitionProvider>(
          create: (context) => AigcMaskAcquisitionProvider(),
        ),
      ];

  // 调试菜单提供者
  static List<SingleChildWidget> _debugMenuProvider(
          SharedPreferences preferences) =>
      [
        // 第一步创建没有CurrentUserRepository依赖的DebugMenuProvider
        ChangeNotifierProvider<DebugMenuProvider>(
          create: (_) => DebugMenuProvider(preferences),
          lazy: false,
        ),
      ];

  static List<SingleChildWidget> get _unityProviders => [
        Provider<UnityController>(
          create: (_) => UnityController(
            messageSender: UnityMessageSenderImpl(),
          ),
          lazy: true,
        ),
      ];

  static List<SingleChildWidget> get _shortcutKeyProviders => [
        Provider<ShortcutKeyService>(
          create: (_) => ShortcutKeyService.forPlatform(),
          lazy: true,
        ),
      ];

  static List<SingleChildWidget> get _useCaseProviders => [
        ProxyProvider6<
            CurrentUserRepository,
            ProjectRepository,
            AccountRepository,
            NetworkProvider,
            CurrentDeviceInformationProvider,
            OpsCustomTableRepository,
            UnityUseCaseProvider>(
          update: (context, userRepo, projectRepo, accountRepo, networkProvider,
              currentDeviceInformationProvider, opsCustomTableRepository, _) {
            PGLog.d('ProxyProvider6 update create UnityUseCaseProvider');
            return UnityUseCaseProvider(
              userRepo,
              projectRepo,
              accountRepo,
              networkProvider,
              currentDeviceInformationProvider,
              opsCustomTableRepository,
            );
          },
        ),
        ProxyProvider5<
            MediaRepository,
            ProjectRepository,
            CurrentUserRepository,
            SettingRepository,
            UnityController,
            ProjectUseCaseProvider>(
          create: (context) {
            final projectUseCaseProvider = ProjectUseCaseProvider(
              mediaRepository: context.read<MediaRepository>(),
              projectRepository: context.read<ProjectRepository>(),
              currentUserRepository: context.read<CurrentUserRepository>(),
              settingRepository: context.read<SettingRepository>(),
              unityController: context.read<UnityController>(),
            );
            // 删除已删除项目
            projectUseCaseProvider.deletedProjectGuard.invoke();
            return projectUseCaseProvider;
          },
          update: (
            context,
            mediaRepository,
            projectRepository,
            currentUserRepository,
            settingRepository,
            unityController,
            _,
          ) {
            PGLog.d('ProxyProvider4 update create ProjectUseCaseProvider');
            return ProjectUseCaseProvider(
              mediaRepository: mediaRepository,
              projectRepository: projectRepository,
              currentUserRepository: currentUserRepository,
              settingRepository: settingRepository,
              unityController: unityController,
            );
          },
        ),
        ProxyProvider3<ExportRepository, CurrentUserRepository,
            AccountRepository, ExportUseCaseProvider>(
          update: (context, exportRepository, currentUserRepository,
              accountRepository, _) {
            PGLog.d('ProxyProvider3 update create ExportUseCaseProvider');
            return ExportUseCaseProvider(
              exportRepository: exportRepository,
              currentUserRepository: currentUserRepository,
            );
          },
        ),
        ProxyProvider3<AuthRepository, CurrentUserRepository, AccountRepository,
            AuthUseCaseProvider>(
          update: (context, authRepository, currentUserRepository,
              accountRepository, _) {
            PGLog.d('ProxyProvider3 update create AuthUseCaseProvider');
            return AuthUseCaseProvider(
              authRepository: authRepository,
              customTableRepository: context.read<OpsCustomTableRepository>(),
              currentUserRepository: currentUserRepository,
              accountRepository: accountRepository,
              purchaseStateProvider: context.read<PurchaseStateProvider>(),
              accountRightsStateProvider:
                  context.read<AccountRightsStateProvider>(),
              unityController: context.read<UnityController>(),
              unityUseCaseProvider: context.read<UnityUseCaseProvider>(),
              navigatorService: GoRouterNavigatorService(context),
            );
          },
        ),
      ];

  // 添加工作区提供者
  static List<SingleChildWidget> _workspaceProvider() => [
        Provider<WorkspaceUseCaseProvider>(
          create: (context) => WorkspaceUseCaseProvider(),
        ),
      ];

  static List<SingleChildWidget> get _exportProviders => [
        Provider<ExportTaskEventBus>(
          create: (_) => ExportTaskEventBus(),
          lazy: false,
        ),
        ChangeNotifierProvider<ExportTaskManager>(
          create: (context) => ExportTaskManager(
            unityController: context.read<UnityController>(),
            eventBus: context.read<ExportTaskEventBus>(),
          ),
          lazy: false,
        ),
        ChangeNotifierProvider<CacheCleanupWithExportPause>(
          create: (context) => CacheCleanupWithExportPause(
            cacheManager: context.read<DiskCacheManager>(),
            eventBus: context.read<ExportTaskEventBus>(),
          ),
          lazy: false,
        ),
        ChangeNotifierProvider<ProjectStateExportTaskDriver>(
          create: (context) => ProjectStateExportTaskDriver(
            projectStateProvider: context.read<ProjectStateProvider>(),
            projectRepository: context.read<ProjectRepository>(),
            eventBus: context.read<ExportTaskEventBus>(),
          ),
          lazy: false,
        ),
        ChangeNotifierProvider<AigcMySampleExportManager>(
          create: (context) => AigcMySampleExportManager(
            repository: context.read<AigcSampleRepository>(),
            currentUserRepository: context.read<CurrentUserRepository>(),
            pollingProvider: context.read<AigcMyExportListPollingProvider>(),
            networkProvider: context.read<NetworkProvider>(),
          ),
          lazy: false,
        ),
        ChangeNotifierProvider<ExportProjectProvider>(
          create: (_) => ExportProjectProvider(),
        ),
        ChangeNotifierProvider<ExportTaskStateProvider>(
          create: (_) => ExportTaskStateProvider(),
        ),
        ProxyProvider<CurrentUserRepository, ExportRecordsRepository>(
          update: (context, currentUserRepository, _) {
            PGLog.d('ProxyProvider update create ExportRecordsRepositoryImpl3');
            return ExportRecordsRepositoryImpl3(
              DbOperater(),
              currentUserRepository,
            );
          },
        ),
      ];
}
