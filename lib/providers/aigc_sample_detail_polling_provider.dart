import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 打样详情轮询提供者
class AigcSampleDetailPollingProvider extends ChangeNotifier {
  final AigcSampleRepository _repository;

  // 轮询流订阅
  StreamSubscription<int>? _pollingSubscription;

  // 轮询间隔（秒）
  static const int _pollingInterval = 10;

  // 当前轮询的打样ID
  String? _currentProofingId;

  // 是否正在轮询
  bool _isPolling = false;
  bool get isPolling => _isPolling;

  AigcSampleDetailPollingProvider(this._repository);

  /// 开始轮询打样详情
  void startPolling(String proofingId) {
    // 如果已经在轮询相同的ID，不需要重新开始
    if (_isPolling && _currentProofingId == proofingId) {
      return;
    }

    // 停止之前的轮询
    stopPolling();

    _currentProofingId = proofingId;
    _isPolling = true;

    // 立即执行一次查询
    _pollDetailStatus();

    // 使用Stream.periodic创建定时轮询流
    _pollingSubscription = Stream.periodic(
            const Duration(seconds: _pollingInterval), (count) => count)
        .listen((_) {
      _pollDetailStatus();
    });

    PGLog.d('开始轮询打样详情，ID: $proofingId');
  }

  /// 停止轮询打样详情
  void stopPolling() {
    if (_pollingSubscription != null) {
      _pollingSubscription?.cancel();
      _pollingSubscription = null;
      _isPolling = false;
      _currentProofingId = null;
      PGLog.d('停止轮询打样详情');
    }
  }

  /// 轮询打样详情状态
  Future<void> _pollDetailStatus() async {
    if (_currentProofingId == null) {
      return;
    }

    try {
      //_repository会通知viewModel检查是否变更需要刷新UI
      final detail = await _repository.getAigcSampleDetail(_currentProofingId!);
      PGLog.d('轮询打样详情: $detail');
    } catch (e) {
      PGLog.e('轮询打样详情出错: $e');
    }
  }

  @override
  void dispose() {
    stopPolling();
    super.dispose();
  }
}
