import 'package:flutter/widgets.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 项目状态变化类型
enum ProjectStateChangeType {
  enteringEdit, // 进入编辑状态
  exitingEdit, // 退出编辑状态
}

class ProjectStateProvider extends ChangeNotifier {
  String? _currentProjectId;
  String? _previousProjectId;
  // 记录当前project，是否是由unity支撑的project
  bool? _isUnityEngine;
  ProjectStateChangeType? _lastChangeType;

  String? get currentProjectId => _currentProjectId;

  String? get previousProjectId => _previousProjectId;

  bool get isEditing => _currentProjectId != null;

  bool get isUnityEngine => _isUnityEngine == true;

  ProjectStateChangeType? get lastChangeType => _lastChangeType;

  /// 设置项目ID（进入编辑状态）
  void editProjectId(String? projectId, {bool isUnityEngine = true}) {
    PGLog.d('ProjectStateProvider: editProjectId被调用，项目ID: $projectId');
    PGLog.d('ProjectStateProvider: 当前项目ID: $_currentProjectId');

    if (_currentProjectId != projectId) {
      _previousProjectId = _currentProjectId;
      _currentProjectId = projectId;
      _isUnityEngine = isUnityEngine;

      // 确定变化类型
      if (_previousProjectId == null && _currentProjectId != null) {
        _lastChangeType = ProjectStateChangeType.enteringEdit;
        PGLog.d('ProjectStateProvider: 进入编辑状态，项目ID: $_currentProjectId');
      } else {
        _lastChangeType = ProjectStateChangeType.exitingEdit;
        PGLog.d('ProjectStateProvider: 退出编辑状态，之前项目ID: $_previousProjectId');
      }

      PGLog.d('ProjectStateProvider: 准备调用notifyListeners()');
      notifyListeners();
      PGLog.d('ProjectStateProvider: notifyListeners()调用完成');
    } else {
      PGLog.d('ProjectStateProvider: 项目ID没有变化，不调用notifyListeners()');
    }
  }

  /// 退出编辑状态
  void exitEdit() {
    if (_currentProjectId != null) {
      _previousProjectId = _currentProjectId;
      _currentProjectId = null;
      _isUnityEngine = null;
      _lastChangeType = ProjectStateChangeType.exitingEdit;

      PGLog.d('ProjectStateProvider: 退出编辑状态，之前项目ID: $_previousProjectId');
      notifyListeners();
    }
  }

  /// 获取当前状态信息
  Map<String, dynamic> getStateInfo() {
    return {
      'currentProjectId': _currentProjectId,
      'previousProjectId': _previousProjectId,
      'isEditing': isEditing,
      'lastChangeType': _lastChangeType?.name,
    };
  }
}
