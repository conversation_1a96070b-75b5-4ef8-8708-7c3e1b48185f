import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

// 账户权益状态(新人、微信添加)提供者
class AccountRightsStateProvider extends ChangeNotifier {
  final CurrentUserRepository _currentUserRepository;
  final AccountRepository _accountRepository;

  // 用于记录权益刷新状态，避免重复刷新
  Map<String, bool> refreshMap = {
    'wechatRightIssued': false,
    'newUserRightIssued': false,
  };

  // 轮询流订阅
  StreamSubscription<int>? _pollingSubscription;

  // 轮询间隔（秒）
  static const int _pollingInterval = 10;

  // 是否已经添加客服
  bool get hasCustomerSupport =>
      _currentUserRepository.store?.customerSupport != null &&
      _currentUserRepository.store?.customerSupport?.isNotEmpty == true;

  // 微信权益是否已下发
  bool get wechatRightIssued =>
      _currentUserRepository.store?.rewards?.addWxwork?.issued ?? false;

  // 新人权益是否已下发
  bool get newUserRightIssued =>
      _currentUserRepository.store?.rewards?.newRegister?.issued ?? false;

  // 所有权益是否都已下发
  bool get allRightsIssued => wechatRightIssued && newUserRightIssued;

  AccountRightsStateProvider(
    this._currentUserRepository,
    this._accountRepository,
  );

  // 开始检查账户权益状态
  void startAccountRightCheck() {
    // 如果已经在轮询，先停止
    stopAccountRightCheck();

    // 重置刷新状态
    refreshMap['wechatRightIssued'] = false;
    refreshMap['newUserRightIssued'] = false;

    // 立即执行一次检查
    _checkAccountRight();

    // 使用Stream.periodic创建定时轮询流
    _pollingSubscription = Stream.periodic(
            const Duration(seconds: _pollingInterval), (count) => count)
        .listen((_) {
      _checkAccountRight();
    });

    PGLog.d('开始轮询检查账户权益状态');
  }

  // 停止检查账户权益状态
  void stopAccountRightCheck() {
    if (_pollingSubscription != null) {
      _pollingSubscription?.cancel();
      _pollingSubscription = null;
      PGLog.d('停止轮询检查账户权益状态');
    }
  }

  // 检查账户权益状态
  Future<void> _checkAccountRight() async {
    try {
      // 刷新用户信息
      await _currentUserRepository.refreshStore();

      PGLog.d('是否已添加销售: $hasCustomerSupport');
      PGLog.d('是否已下发微信权益: $wechatRightIssued');
      PGLog.d('是否已下发新人权益: $newUserRightIssued');

      // 检查权益状态并更新refreshMap
      bool wechatStatusChanged =
          (refreshMap['wechatRightIssued'] ?? false) != wechatRightIssued;
      PGLog.d('微信权益状态变化: $wechatStatusChanged');

      bool newUserStatusChanged =
          (refreshMap['newUserRightIssued'] ?? false) != newUserRightIssued;
      PGLog.d('新人权益状态变化: $newUserStatusChanged');

      if (wechatStatusChanged || newUserStatusChanged) {
        refreshMap['wechatRightIssued'] = wechatRightIssued;
        refreshMap['newUserRightIssued'] = newUserRightIssued;

        PGLog.d('开始刷新账户信息');
        // 刷新账户信息，通知UI状态变化
        _accountRepository.refreshAllAccount();
      }

      // 当所有权益都已下发且已刷新过账户信息时，停止轮询
      if (allRightsIssued) {
        PGLog.d('所有权益已下发，停止轮询');
        stopAccountRightCheck();
      }
    } catch (e) {
      PGLog.e('检查账户权益状态出错: $e');
    }
  }

  @override
  void dispose() {
    stopAccountRightCheck();
    super.dispose();
  }
}
