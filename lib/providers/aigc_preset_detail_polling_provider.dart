import 'dart:async';

import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 预设详情轮询服务
class AigcPresetDetailPollingProvider {
  // 轮询间隔（秒）
  static const int _pollingInterval = 10;

  final String _presetId;

  final AigcPresetsRepository _repository;

  StreamSubscription<AigcPcPresetsDetailResponse>? _pollingSubscription;

  // 使用 StreamController 广播轮询结果
  final _pollingResultController =
      StreamController<AigcPcPresetsDetailResponse>.broadcast();

  /// 轮询结果的流
  Stream<AigcPcPresetsDetailResponse> get pollingResultStream =>
      _pollingResultController.stream;

  AigcPresetDetailPollingProvider(
      {required AigcPresetsRepository repository, required String presetId})
      : _repository = repository,
        _presetId = presetId;

  /// 开始轮询
  void startPolling() {
    // 如果已经在轮询同一个 preset，则不重复启动
    if (_pollingSubscription != null) {
      PGLog.d('Polling is already running.');
      return;
    }
    // 停止之前的轮询
    stopPolling();

    PGLog.d('开始轮询预设详情: $_presetId');
    _pollingSubscription =
        Stream.periodic(const Duration(seconds: _pollingInterval))
            .asyncMap((_) {
      return _repository.getAigcPresetsDetail(_presetId);
    }).listen(
      (response) {
        if (!_pollingResultController.isClosed) {
          _pollingResultController.add(response);
        }

        // 如果状态不再是 running，则停止轮询
        if (!response.isInRunningStatus()) {
          stopPolling();
        }
      },
      onError: (e) {
        PGLog.e('轮询预设详情失败: $e');
        if (!_pollingResultController.isClosed) {
          _pollingResultController.addError(e);
        }
        stopPolling(); // 发生错误时停止轮询
      },
      cancelOnError: true, // 发生错误时自动取消订阅
    );
  }

  /// 停止轮询
  void stopPolling() {
    if (_pollingSubscription != null) {
      PGLog.d('停止轮询预设详情: $_presetId');
      _pollingSubscription?.cancel();
      _pollingSubscription = null;
    }
  }

  /// 销毁服务
  void dispose() {
    stopPolling();
    _pollingResultController.close();
  }
}
