import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/enums/order_status.dart';
import 'package:turing_art/datalayer/domain/events/order_event.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/purchase_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

class PurchaseStateProvider extends ChangeNotifier {
  final PurchaseRepository _purchaseRepository;
  final AccountRepository _accountRepository;
  // 订单状态
  OrderStatus _orderStatus = OrderStatus.unknown;

  // 当前订单id
  String? _currentOrderId;

  // 购买计划信息
  String? _purchasePlanId;
  String? _purchasePlanName;
  String? _purchasePlanPrice;

  // 轮询流订阅
  StreamSubscription<int>? _pollingSubscription;

  // 轮询间隔（秒）
  static const int _pollingInterval = 10;

  // 添加事件控制器
  final StreamController<OrderEvent> _orderEventController =
      StreamController<OrderEvent>.broadcast();

  // 对外暴露事件流 （订单状态变更事件，采用事件流方式通知：以免发送通知后马上重置状态，导致UI有的还没有更新）
  Stream<OrderEvent> get orderStatusEvents => _orderEventController.stream;

  PurchaseStateProvider(
    this._purchaseRepository,
    this._accountRepository,
  );

  // 开始检查订单状态
  void startOrderStatusCheck(
    String? orderId, {
    String? purchasePlanId,
    String? purchasePlanName,
    String? purchasePlanPrice,
  }) {
    // 如果已经在轮询，先停止
    _stopOrderStatusCheck();

    _currentOrderId = orderId;
    _purchasePlanId = purchasePlanId;
    _purchasePlanName = purchasePlanName;
    _purchasePlanPrice = purchasePlanPrice;
    _orderStatus = OrderStatus.processing; // 初始状态为待支付

    // 立即执行一次检查
    _checkOrderStatus();

    // 使用Stream.periodic创建定时轮询流
    _pollingSubscription = Stream.periodic(
            const Duration(seconds: _pollingInterval), (count) => count)
        .listen((_) {
      _checkOrderStatus();
    });

    PGLog.d('开始轮询检查订单状态');
  }

  // 停止检查订单状态
  void _stopOrderStatusCheck() {
    if (_pollingSubscription != null) {
      _pollingSubscription?.cancel();
      _pollingSubscription = null;
      PGLog.d('停止轮询检查订单状态');
    }
  }

  // 检查订单状态
  Future<void> _checkOrderStatus() async {
    if (_currentOrderId == null) {
      PGLog.d('检查订单状态失败: 没有订单ID');
      return;
    }
    try {
      final newStatus = await _purchaseRepository
          .checkCurrentOrderStatus(_currentOrderId ?? "");
      // 如果状态有变化，发送事件
      if (newStatus != _orderStatus) {
        PGLog.d('订单状态变更: $_orderStatus -> $newStatus');
        _orderStatus = newStatus;
        if (newStatus == OrderStatus.completed) {
          // 如果是成功，刷新账户信息后再通知，避免通知后UI有的还没有更新，关于第一次购买成功的标志还不正确
          await _accountRepository.refreshAllAccount();
        }
        // 发送事件
        _orderEventController.add(OrderEvent(
          newStatus,
          newStatus.description,
          orderId: _currentOrderId,
          purchasePlanId: _purchasePlanId,
          purchasePlanName: _purchasePlanName,
          purchasePlanPrice: _purchasePlanPrice,
        ));

        // 如果订单已完成/取消/退款/失效，停止轮询
        if (newStatus == OrderStatus.completed ||
            newStatus == OrderStatus.canceled ||
            newStatus == OrderStatus.refunded ||
            newStatus == OrderStatus.closed) {
          // 停止轮询,清理当前订单信息
          _completeOrderProcessing();
        }
      }
    } catch (e) {
      PGLog.e('检查订单状态出错: $e');
    }
  }

  // 订单状态处理完成后的流程
  void _completeOrderProcessing() {
    stopOrderStatusCheckAndClean();
    PGLog.d('订单处理完成，状态已重置，已经刷新账户信息');
  }

  void _cleanOrderInfo() {
    _currentOrderId = null;
    _purchasePlanId = null;
    _purchasePlanName = null;
    _purchasePlanPrice = null;
    _orderStatus = OrderStatus.unknown;
  }

  void stopOrderStatusCheckAndClean() {
    _stopOrderStatusCheck();
    _cleanOrderInfo();
  }

  @override
  void dispose() {
    _orderEventController.close();
    _stopOrderStatusCheck();
    super.dispose();
  }
}
