import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/utils/pg_log.dart';

class ExportProjectProvider extends ChangeNotifier {
  List<ExportRecord> _exportProjectRecords = [];
  List<ExportRecord> get exportProjectRecords => _exportProjectRecords;
  bool _isReloadProjectRecords = false;
  bool get isReloadProjectRecords => _isReloadProjectRecords;

  Future<void> updateExportProjectRecord(String recordJson) async {
    try {
      final record =
          ExportRecord.fromJson(jsonDecode(recordJson) as Map<String, dynamic>);
      _exportProjectRecords = [record];
      notifyListeners();
    } catch (e) {
      PGLog.e('ExportProjectProvider: 更新导出记录失败: $e');
    }
  }

  Future<void> updateExportBatchProjectRecords(String recordJson) async {
    try {
      List<Map<String, dynamic>> data = (jsonDecode(recordJson) as List)
          .map((item) => item as Map<String, dynamic>)
          .toList();
      final records = ExportRecord.fromJsonList(data);
      if (records.isNotEmpty) {
        _exportProjectRecords = records;
        notifyListeners();
      }
    } catch (e) {
      PGLog.e('ExportProjectProvider: 更新导出记录失败: $e');
    }
  }

  void invokeReloadProjectRecords({required bool needReload}) {
    _isReloadProjectRecords = needReload;
    if (_isReloadProjectRecords) {
      clearExportProjectRecords();
      notifyListeners();
    }
  }

  void clearExportProjectRecords() {
    _exportProjectRecords = [];
  }
}
