import 'package:flutter/material.dart';

/// 画布点数据
class CanvasPoint {
  /// 点的位置（相对坐标，0-1范围）
  final Offset offset;

  /// 是否为擦除点
  final bool isErase;

  /// 点的大小（画笔大小）- 固定大小，不受缩放影响
  final double size;

  /// 路径ID，用于区分不同的绘制会话
  final int pathId;

  /// 构造函数
  const CanvasPoint({
    required this.offset,
    required this.isErase,
    required this.size,
    required this.pathId,
  });
}

/// 绘制批次数据
class DrawingBatch {
  /// 批次ID
  final int batchId;

  /// 批次中的所有点
  final List<CanvasPoint> points;

  /// 是否为擦除批次
  bool isErase;

  /// 批次创建时的缩放比例（用于固定画笔大小）
  final double scaleAtCreation;

  /// 批次是否已完成
  bool isCompleted;

  /// 构造函数
  DrawingBatch({
    required this.batchId,
    required this.points,
    required this.isErase,
    required this.scaleAtCreation,
    this.isCompleted = false,
  });

  /// 添加点到批次
  void addPoint(CanvasPoint point) {
    if (!isCompleted) {
      points.add(point);
    }
  }

  /// 完成批次
  void complete() {
    isCompleted = true;
    isErase = false;
  }
}

/// 画布模式枚举
enum CanvasMode {
  /// 浏览模式（可拖拽、缩放）
  view,

  /// 绘制模式（绘制路径）
  draw,

  /// 擦除模式（擦除路径）
  erase,
}

/// 画布状态枚举
enum CanvasActionState {
  /// 无操作
  none,

  /// 正在绘制
  drawing,

  /// 正在擦除
  erasing,

  /// 正在拖拽
  dragging,

  /// 正在缩放
  scaling,
}

/// AIGC绘制路径Provider
///
/// 负责管理路径绘制相关的数据和状态
/// 通过ChangeNotifier模式驱动UI刷新
class AigcPainterPathProvider extends ChangeNotifier {
  /// 绘制批次队列 - 先进先出
  final List<DrawingBatch> _drawingBatches = <DrawingBatch>[];

  /// 当前正在进行的批次
  DrawingBatch? _currentBatch;

  /// 批次ID计数器
  int _batchIdCounter = 0;

  /// 路径ID计数器
  int _pathIdCounter = 0;

  /// 当前画布模式
  CanvasMode _mode = CanvasMode.draw;

  /// 当前画布操作状态
  CanvasActionState _actionState = CanvasActionState.none;

  /// 是否显示用户绘制的路径
  bool _showUserPaths = true;

  /// 画笔颜色
  Color _brushColor = Colors.white;

  /// 获取绘制批次队列（只读）
  List<DrawingBatch> get drawingBatches => List.unmodifiable(_drawingBatches);

  /// 获取当前批次
  DrawingBatch? get currentBatch => _currentBatch;

  /// 获取所有点
  List<CanvasPoint> get points {
    final allPoints = <CanvasPoint>[];
    for (final batch in _drawingBatches) {
      allPoints.addAll(batch.points);
    }
    if (_currentBatch != null) {
      allPoints.addAll(_currentBatch!.points);
    }
    return allPoints;
  }

  /// 获取当前画布模式
  CanvasMode get mode => _mode;

  /// 获取当前画布操作状态
  CanvasActionState get actionState => _actionState;

  /// 获取是否显示用户绘制的路径
  bool get showUserPaths => _showUserPaths;

  /// 获取画笔颜色
  Color get brushColor => _brushColor;

  /// 设置当前画布模式
  void setMode(CanvasMode value) {
    if (_mode != value) {
      _mode = value;
      notifyListeners();
    }
  }

  /// 设置当前画布操作状态
  void setActionState(CanvasActionState value) {
    if (_actionState != value) {
      _actionState = value;
      notifyListeners();
    }
  }

  /// 设置画笔颜色
  void setBrushColor(Color value) {
    if (_brushColor != value) {
      _brushColor = value;
      notifyListeners();
    }
  }

  /// 设置是否显示用户绘制的路径
  void setShowUserPaths({required bool show}) {
    if (_showUserPaths != show) {
      _showUserPaths = show;
      notifyListeners();
    }
  }

  /// 开始新的绘制批次
  void startDrawingBatch(
      {required bool isErase, required double currentScale}) {
    // 创建新的绘制批次
    _batchIdCounter++;
    _pathIdCounter++;

    _currentBatch = DrawingBatch(
      batchId: _batchIdCounter,
      points: <CanvasPoint>[],
      isErase: isErase,
      scaleAtCreation: currentScale,
    );

    _actionState =
        isErase ? CanvasActionState.erasing : CanvasActionState.drawing;
    notifyListeners();
  }

  /// 向当前批次添加点
  void addPointToBatch(CanvasPoint point) {
    if (_currentBatch != null && !_currentBatch!.isCompleted) {
      _currentBatch!.addPoint(point);
      notifyListeners();
    }
  }

  /// 完成当前绘制批次
  void finishCurrentBatch() {
    if (_currentBatch != null) {
      // 如果只有一个点，不保存这个批次（单点不支持）
      if (_currentBatch!.points.length <= 1) {
        _currentBatch = null;
      } else {
        // 完成当前批次并添加到队列
        _currentBatch!.complete();
        _drawingBatches.add(_currentBatch!);
        _currentBatch = null;
      }

      // 重置状态
      _actionState = CanvasActionState.none;
      notifyListeners();
    }
  }

  /// 取消当前绘制批次
  void cancelCurrentBatch() {
    if (_currentBatch != null) {
      _currentBatch = null;
      _actionState = CanvasActionState.none;
      notifyListeners();
    }
  }

  /// 清除所有绘制批次
  void clearAllBatches() {
    _drawingBatches.clear();
    _currentBatch = null;
    _actionState = CanvasActionState.none;
    notifyListeners();
  }

  /// 撤销最后一个绘制批次
  void undoLastBatch() {
    if (_drawingBatches.isNotEmpty) {
      _drawingBatches.removeLast();
      notifyListeners();
    }
  }

  /// 获取下一个路径ID
  int getNextPathId() {
    return ++_pathIdCounter;
  }

  /// 检查是否有任何绘制内容
  bool get hasAnyDrawing => _drawingBatches.isNotEmpty || _currentBatch != null;

  /// 获取总的绘制点数
  int get totalPointCount {
    int count =
        _drawingBatches.fold(0, (sum, batch) => sum + batch.points.length);
    if (_currentBatch != null) {
      count += _currentBatch!.points.length;
    }
    return count;
  }

  /// 获取所有有效的绘制批次
  List<DrawingBatch> getValidBatches() {
    return _drawingBatches
        .where((batch) => batch.isCompleted && batch.points.isNotEmpty)
        .toList();
  }

  /// 获取指定区域内的绘制批次
  List<DrawingBatch> getBatchesInRect(Rect rect) {
    return _drawingBatches.where((batch) {
      return batch.points.any((point) => rect.contains(point.offset));
    }).toList();
  }
}
