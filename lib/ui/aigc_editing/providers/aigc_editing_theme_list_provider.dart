import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';

/// AIGC主题列表Provider
///
/// 遵循Provider模式，负责管理主题列表的数据和业务逻辑
class AigcEditingThemeListProvider extends ChangeNotifier {
  // 选中的预设
  AigcPresetsModel? _selectedPreset;

  // 选中的效果
  AigcPresetsEffect? _selectedEffect;

  // 悬停的效果
  AigcPresetsEffect? _hoverEffect;

  // 图片ID到打样状态的映射
  final Map<String, ProcessState> _imageStates = {};

  AigcPresetsEffect? get selectedEffect => _selectedEffect;

  AigcPresetsEffect? get hoverEffect => _hoverEffect;

  AigcPresetsModel? get selectedPreset => _selectedPreset;

  /// 获取当前处理状态
  /// 基于当前选中图片的状态，如果没有选中图片则返回disabled
  ProcessState fetchProcessState(String fileId) {
    if (fileId.isEmpty) {
      return ProcessState.disabled;
    }
    return getImageState(fileId);
  }

  /// 兼容性方法：获取所有已打样的文件ID列表
  List<String> get sampleRecordsList => _imageStates.keys.toList();

  /// 判断样本记录是否存在（兼容性方法）
  ///
  /// 参数：
  /// - fileId: 样本记录的文件ID
  ///
  /// 返回：
  /// - true: 样本记录存在
  bool isExistSampleRecords(String fileId) {
    return _imageStates.containsKey(fileId);
  }

  /// 获取指定图片的打样状态
  ///
  /// 参数：
  /// - fileId: 图片的文件ID
  ///
  /// 返回：
  /// - ProcessState: 图片的打样状态，如果不存在则返回initial
  ProcessState getImageState(String fileId) {
    bool isHaveEffect = _selectedEffect != null;
    bool isHavePreset = _selectedPreset != null;

    if (isHaveEffect && isHavePreset) {
      return _imageStates[fileId] ?? ProcessState.initial;
    } else {
      return ProcessState.disabled;
    }
  }

  /// 设置图片的打样状态
  ///
  /// 参数：
  /// - fileId: 图片的文件ID
  /// - state: 打样状态
  void setImageState(String fileId, ProcessState state) {
    if (_imageStates[fileId] != state) {
      _imageStates[fileId] = state;
      notifyListeners();
    }
  }

  /// 批量设置图片的打样状态
  ///
  /// 参数：
  /// - states: 图片ID到状态的映射
  void setBatchImageStates(Map<String, ProcessState> states) {
    bool hasChanges = false;
    states.forEach((fileId, state) {
      if (_imageStates[fileId] != state) {
        _imageStates[fileId] = state;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      notifyListeners();
    }
  }

  /// 移除图片的打样状态
  ///
  /// 参数：
  /// - fileId: 图片的文件ID
  void removeImageState(String fileId) {
    if (_imageStates.remove(fileId) != null) {
      notifyListeners();
    }
  }

  /// 添加样本记录（兼容性方法）
  ///
  /// 参数：
  /// - fileId: 样本记录的文件ID
  void addSampleRecord(String fileId) {
    setImageState(fileId, ProcessState.processed);
  }

  /// 设置当前选中的图片ID
  ///
  /// 参数：
  /// - imageId: 当前选中的图片ID
  // void setCurrentImageId(String? imageId) {
  //   if (_currentImageId != imageId) {
  //     _currentImageId = imageId;
  //     notifyListeners();
  //   }
  // }

  /// 设置选中的预设
  ///
  /// 参数：
  /// - preset: 选中的预设
  void setSelectedPreset(AigcPresetsModel? preset) {
    _selectedPreset = preset;
    notifyListeners();
  }

  /// 设置选中的效果
  ///
  /// 参数：
  /// - effect: 选中的效果
  void setSelectedEffect(AigcPresetsEffect? effect) {
    _selectedEffect = effect;
    notifyListeners();
  }

  /// 设置悬停的效果
  ///
  /// 参数：
  /// - effect: 悬停的效果
  void setHoverEffect(AigcPresetsEffect? effect) {
    _hoverEffect = effect;
    notifyListeners();
  }

  void reset() {
    _selectedPreset = null;
    _selectedEffect = null;
    _hoverEffect = null;
    notifyListeners();
  }
}
