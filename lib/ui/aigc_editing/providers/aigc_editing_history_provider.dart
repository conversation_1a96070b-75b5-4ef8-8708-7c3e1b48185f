import 'dart:async';
import 'dart:convert'; // Added for jsonDecode

import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import 'package:turing_art/core/manager/history_record_manager.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/file_operation_history_db_operater.dart';
import 'package:turing_art/ui/aigc_editing/model/history/aigc_editing_history_state.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC编辑历史记录Provider
class AigcEditingHistoryProvider extends ChangeNotifier {
  final DbOperater _dbOperater;

  // 每个图片资源对应一个HistoryRecordManager
  final Map<String, HistoryRecordManager<AigcEditingHistoryState>> _managers =
      {};

  // 当前活动的图片ID
  String? _currentImageId;

  AigcEditingHistoryProvider({
    required DbOperater dbOperater,
  }) : _dbOperater = dbOperater;

  /// 获取当前图片ID
  String? get currentImageId => _currentImageId;

  /// 获取当前图片的历史记录管理器
  HistoryRecordManager<AigcEditingHistoryState>? get currentManager {
    if (_currentImageId == null) {
      return null;
    }
    return _managers[_currentImageId];
  }

  /// 检查当前图片是否可以撤销
  bool get canUndo => currentManager?.canBackward ?? false;

  /// 检查当前图片是否可以重做
  bool get canRedo => currentManager?.canForward ?? false;

  /// 判断针对当前当前图片是否包含历史记录
  bool get hasHistoryForCurrentImage {
    if (_currentImageId == null) {
      return false;
    }
    // 1. 首先判断是否存在回退的历史记录
    final undoRecords =
        currentManager?.currentHistory?.getAllUndoRecords().length ?? 0;
    if (undoRecords > 0) {
      return true;
    }
    // 2. 如果没有回退的历史记录，再判断是否存在前进的历史记录
    final redoRecords =
        currentManager?.currentHistory?.getAllRedoRecords().length ?? 0;
    return redoRecords > 0;
  }

  /// 设置当前编辑的图片
  Future<void> setCurrentImage(String imageId) async {
    if (_currentImageId == imageId) {
      return;
    }

    _currentImageId = imageId;

    // 确保当前图片的历史记录管理器存在
    await _ensureManagerExists(imageId);

    notifyListeners();
  }

  /// 添加编辑历史记录
  Future<void> addHistoryRecord({
    required AigcEditingHistoryState state,
  }) async {
    final currentImageId = _currentImageId;
    if (currentImageId == null) {
      PGLog.w('AigcEditingHistoryProvider: 当前没有选中的图片，无法添加历史记录');
      return;
    }

    final manager = await _ensureManagerExists(currentImageId);

    // 先找到潜在的可能需要被丢弃的历史记录
    final redoNodes = manager.getAllRedoRecords();
    if (redoNodes.isNotEmpty) {
      // 删除这些节点
      final fileIds = redoNodes.map((node) => node.data.fileId).toList();
      _dbOperater.deleteFileOperationHistoryByFileIds(fileIds);
    }

    // 添加新节点进内存
    await manager.addHistoryNode(data: state);

    // 添加新节点进数据库
    _dbOperater
        .insertFileOperationHistory(FileOperationHistoryEntityCompanion.insert(
      fileId: currentImageId,
      createTime: state.timestamp,
      extraData: Value(jsonEncode(state.toJson())),
    ));

    // 通知UI更新
    notifyListeners();
  }

  /// 撤销操作
  Future<AigcEditingHistoryState?> undo() async {
    if (_currentImageId == null) {
      return null;
    }

    final manager = _managers[_currentImageId];
    if (manager == null) {
      return null;
    }

    final previousState = await manager.goBackward();
    // 这里需要通知观察者进行更新，即使 previous 为空的状态下
    notifyListeners();

    return previousState;
  }

  /// 重做操作
  Future<AigcEditingHistoryState?> redo() async {
    if (_currentImageId == null) {
      return null;
    }

    final manager = _managers[_currentImageId];
    if (manager == null) {
      return null;
    }

    final nextState = await manager.goForward();

    // 这里潜在的存在丢弃若干结点，并且新增某一结点的操作

    if (nextState != null) {
      notifyListeners();
    }

    return nextState;
  }

  /// 跳转到指定历史记录
  Future<AigcEditingHistoryState?> goToHistory(String nodeId) async {
    if (_currentImageId == null) {
      return null;
    }

    final manager = _managers[_currentImageId];
    if (manager == null) {
      return null;
    }

    final state = await manager.goToNode(nodeId);

    if (state != null) {
      notifyListeners();
    }

    return state;
  }

  /// 获取当前图片的所有有效历史记录，仅包括可undo 和 当前结点
  List<AigcEditingHistoryState> getCurrentHistoryValidStates() {
    return getCurrentHistoryValidNodes().map((node) => node.data).toList();
  }

  /// 获取当前图片的所有有效历史记录节点，仅包括可undo 和 当前结点
  List<HistoryNode<AigcEditingHistoryState>> getCurrentHistoryValidNodes() {
    if (_currentImageId == null) {
      return [];
    }

    final manager = _managers[_currentImageId];
    if (manager == null) {
      return [];
    }

    return manager.getAllUndoRecords();
  }

  /// 清空当前图片的历史记录
  Future<void> clearCurrentHistory() async {
    final currentImageId = _currentImageId;
    if (currentImageId == null) {
      return;
    }

    final manager = _managers[currentImageId];
    if (manager == null) {
      return;
    }

    await manager.clearHistory();

    // 从数据库删除
    _dbOperater.deleteFileOperationHistoryByFileId(currentImageId);

    notifyListeners();
  }

  /// 清空所有历史记录
  Future<void> clearAllHistory() async {
    // 清空内存中的历史记录
    for (final manager in _managers.values) {
      await manager.clearHistory();
    }
    _managers.clear();

    // 从数据库删除所有历史记录
    _dbOperater.deleteAllFileOperationHistory();

    notifyListeners();
  }

  /// 确保指定图片的历史记录管理器存在
  Future<HistoryRecordManager<AigcEditingHistoryState>> _ensureManagerExists(
    String imageId,
  ) async {
    HistoryRecordManager<AigcEditingHistoryState>? manager = _managers[imageId];
    if (manager != null) {
      // Manager已存在，不需要重新加载历史记录，直接返回
      return manager;
    }

    manager = HistoryRecordManager<AigcEditingHistoryState>();
    _managers[imageId] = manager;

    // 从数据库加载历史记录
    await _loadHistoryFromDatabase(imageId, manager);
    return manager;
  }

  /// 从数据库加载历史记录
  Future<void> _loadHistoryFromDatabase(
    String imageId,
    HistoryRecordManager<AigcEditingHistoryState> manager,
  ) async {
    try {
      // 从数据库获取历史记录
      final historyData =
          await _dbOperater.getFileOperationHistoryByFileId(imageId);
      // 按照创建时间排序（从旧到新）
      historyData.sort((a, b) => a.createTime.compareTo(b.createTime));

      // 添加进内存
      if (historyData.isNotEmpty) {
        for (final data in historyData) {
          final extraData = data.extraData;
          if (extraData != null && extraData.isNotEmpty) {
            try {
              // 解析 JSON 字符串
              final jsonMap = jsonDecode(extraData) as Map<String, dynamic>;
              final state = AigcEditingHistoryState.fromJson(jsonMap);
              await manager.addHistoryNode(data: state);
            } catch (e) {
              PGLog.e('解析历史记录失败: $e');
            }
          }
        }
      }
    } catch (e) {
      PGLog.e('从数据库加载历史记录失败: $e');
    }
  }

  /// 获取指定图片的历史记录管理器
  HistoryRecordManager<AigcEditingHistoryState>? getManager(String imageId) {
    return _managers[imageId];
  }

  /// 获取当前节点
  HistoryNode<AigcEditingHistoryState>? getCurrentNode() {
    return currentManager?.getCurrentNode();
  }

  /// 检查指定图片是否有历史记录
  bool hasHistory(String imageId) {
    final manager = _managers[imageId];
    if (manager == null) {
      return false;
    }
    return manager.hasHistory;
  }
}

extension AigcEditingHistoryProviderExtension on AigcEditingHistoryProvider {
  /// 获取当前图片的所有有效的框选历史记录
  /// 从当前节点开始向前遍历，收集所有regional类型的节点，直到遇到第一个reset类型的节点
  /// 如果当前节点是reset类型，则返回空列表
  List<AigcEditingHistoryState> getCurrentHistoryValidRegionalStates() {
    final validNodes = getCurrentHistoryValidNodes();

    // 如果没有有效节点，返回空列表
    if (validNodes.isEmpty) {
      return [];
    }

    // 如果当前节点是reset类型，返回空列表
    final currentNode = validNodes.last; // 当前节点是最后一个（最新的）
    if (currentNode.data.type == AigcEditingHistoryType.reset) {
      return [];
    }

    final regionalStates = <AigcEditingHistoryState>[];

    // 从当前节点开始向前遍历（从最新到最旧）
    for (int i = validNodes.length - 1; i >= 0; i--) {
      final node = validNodes[i];

      // 如果遇到reset类型的节点，停止遍历
      if (node.data.type == AigcEditingHistoryType.reset) {
        break;
      }

      // 如果是regional类型的节点，添加到结果列表（使用insert(0)保持正序）
      if (node.data.type == AigcEditingHistoryType.regional) {
        regionalStates.insert(0, node.data);
      }
    }

    return regionalStates;
  }

  /// 检查从最近的reset操作到最新记录之间是否存在regional操作
  /// 如果历史记录中没有reset类型的记录，则从最开始查找
  /// 返回true表示存在regional操作，false表示不存在
  bool hasRegionalOperationSinceLastReset() {
    // 获取当前图片的所有有效历史记录状态
    final validStates = getCurrentHistoryValidStates();

    if (validStates.isEmpty) {
      return false;
    }

    // 从最新记录开始向前遍历，查找最近的reset操作
    int resetIndex = -1;
    for (int i = validStates.length - 1; i >= 0; i--) {
      if (validStates[i].type == AigcEditingHistoryType.reset) {
        resetIndex = i;
        break;
      }
    }

    // 确定搜索范围的起始索引
    // 如果找到了reset操作，从reset操作的下一条记录开始搜索
    // 如果没有找到reset操作，从第一条记录开始搜索
    int searchStartIndex = resetIndex == -1 ? 0 : resetIndex + 1;

    // 在指定范围内查找是否存在regional类型的操作
    for (int i = searchStartIndex; i < validStates.length; i++) {
      if (validStates[i].type == AigcEditingHistoryType.regional) {
        return true;
      }
    }

    return false;
  }
}
