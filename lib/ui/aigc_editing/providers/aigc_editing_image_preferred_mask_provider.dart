import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';

import 'aigc_editing_history_provider.dart';
import 'aigc_editing_image_provider.dart';

class AigcEditingImagePreferredMaskProvider {
  AigcEditingImagePreferredMaskProvider({
    required AigcEditingHistoryProvider historyProvider,
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required AigcEditingImageProvider imageProvider,
  })  : _imageProvider = imageProvider,
        _historyProvider = historyProvider;

  final AigcEditingImageProvider _imageProvider;
  final AigcEditingHistoryProvider _historyProvider;

  bool get hasMask => currentImagePreferredMaskPath != null;

  // 获取当前选中图片资源的优先蒙版路径
  String? get currentImagePreferredMaskPath {
    return _getPreferredMaskPath();
  }

  // 获得当前资源的蒙版路径
  String? get currentImageMaskPath {
    return _getMaskPath();
  }

  // 获得当前选中图片资源的交互式蒙版路径
  String? get currentImageInteractiveMaskPath {
    return _getInteractiveMaskPath();
  }

  /// 检查从最近的reset操作到最新记录之间是否存在regional操作
  /// 如果历史记录中没有reset类型的记录，则从最开始查找
  /// 返回true表示存在regional操作，false表示不存在
  bool hasRegionalOperationSinceLastReset() {
    // 获取当前图片的所有有效历史记录状态
    return _historyProvider.hasRegionalOperationSinceLastReset();
  }

  String? _getInteractiveMaskPath() {
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      return null;
    }

    final history = _historyProvider.getCurrentHistoryValidStates().lastOrNull;

    final maskDirectoryPath = history?.maskPath;

    if (maskDirectoryPath != null) {
      final maskFilePath = path.join(
          maskDirectoryPath, MediaResourceConstants.getUniversalMaskFileName());
      if (File(maskFilePath).existsSync()) {
        return maskFilePath;
      }
    }

    return null;
  }

  String? _getMaskPath() {
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      return null;
    }

    final path = selectedImage.maskPath;
    if (path != null && File(path).existsSync()) {
      return path;
    }

    return null;
  }

  String? _getPreferredMaskPath() {
    final interactiveMaskPath = _getInteractiveMaskPath();
    if (interactiveMaskPath != null) {
      return interactiveMaskPath;
    }
    return _getMaskPath();
  }
}
