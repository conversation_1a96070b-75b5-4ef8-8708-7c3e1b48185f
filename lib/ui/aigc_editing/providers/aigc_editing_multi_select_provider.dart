import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item_adapter.dart';

/// AIGC编辑多选事件类型枚举
enum AigcEditingMultiSelectEventType {
  /// 图片多选状态变化事件
  multiSelectionChanged,
}

/// AIGC编辑场景的多选图片Provider
/// 专门管理图片多选相关的状态和逻辑
class AigcEditingMultiSelectProvider extends ChangeNotifier {
  // 实例ID，用于验证单例
  final String _instanceId = DateTime.now().millisecondsSinceEpoch.toString();

  // 存储多选的图片集合
  final Set<AigcPreviewImageItem> _selectedImageSet = <AigcPreviewImageItem>{};

  // 多选模式
  bool _isMultiSelectMode = false;

  // 事件流控制器
  final StreamController<AigcEditingMultiSelectEventType>
      _eventStreamController =
      StreamController<AigcEditingMultiSelectEventType>.broadcast();

  // 依赖项
  late final CurrentEditingProjectRepository _currentEditingProjectRepository;
  late final AigcPreviewImageItemAdapter _aigcPreviewImageItemAdapter;

  /// 初始化依赖项
  void initializeDependencies({
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required AigcPreviewImageItemAdapter aigcPreviewImageItemAdapter,
  }) {
    _currentEditingProjectRepository = currentEditingProjectRepository;
    _aigcPreviewImageItemAdapter = aigcPreviewImageItemAdapter;
  }

  /// 获取事件流
  Stream<AigcEditingMultiSelectEventType> get eventStream =>
      _eventStreamController.stream;

  /// 获取实例ID（用于调试验证单例）
  String get instanceId => _instanceId;

  /// 获取多选的图片列表
  Set<AigcPreviewImageItem> get selectedImages =>
      Set.unmodifiable(_selectedImageSet);

  /// 是否为多选模式
  bool get isMultiSelectMode => _isMultiSelectMode;

  /// 获取多选图片数量
  int get selectedImageCount => _selectedImageSet.length;

  /// 设置多选模式，[withInitialImage] 是否在开启多选模式时自动添加指定的图片，默认为 null
  void setMultiSelectMode({
    required bool enabled,
    AigcPreviewImageItem? withInitialImage,
  }) {
    if (_isMultiSelectMode != enabled) {
      _isMultiSelectMode = enabled;

      if (enabled) {
        // 开启时添加指定的图片
        if (withInitialImage != null) {
          _selectedImageSet.add(withInitialImage);
        }
      } else {
        // 如果退出多选模式，清空多选ID
        _selectedImageSet.clear();
      }

      _eventStreamController
          .add(AigcEditingMultiSelectEventType.multiSelectionChanged);
      notifyListeners();
    }
  }

  /// 多选模式下更新选中的图片集合
  void _updateSelectedItem(void Function() updateAction) {
    final oldSize = _selectedImageSet.length;
    updateAction();
    if (_selectedImageSet.length != oldSize) {
      _eventStreamController
          .add(AigcEditingMultiSelectEventType.multiSelectionChanged);
      notifyListeners();
    }
  }

  /// 多选模式下添加单张照片
  void addSelectedImage(AigcPreviewImageItem item) {
    _updateSelectedItem(() => _selectedImageSet.add(item));
  }

  /// 多选模式下添加多张照片。[overrideMode] 是否覆盖当前已经选中的列表，默认为 false
  void batchSelectedImages({
    required List<AigcPreviewImageItem> items,
    bool overrideMode = false,
  }) {
    _updateSelectedItem(() {
      if (overrideMode) {
        _selectedImageSet.clear();
      }
      _selectedImageSet.addAll(items);
    });
  }

  /// 多选模式下删除选中的照片
  void removeSelectedImage(AigcPreviewImageItem item) {
    _updateSelectedItem(() => _selectedImageSet.remove(item));
  }

  /// 多选模式下切换照片选中状态
  void toggleSelectedImage(AigcPreviewImageItem item) {
    _updateSelectedItem(() {
      if (_selectedImageSet.contains(item)) {
        _selectedImageSet.remove(item);
      } else {
        _selectedImageSet.add(item);
      }
    });
  }

  /// 多选模式下检查照片是否被选中
  bool isItemBeenMultiSelected(AigcPreviewImageItem item) {
    return _selectedImageSet.contains(item);
  }

  /// 清空所有选中的图片
  void clearSelectedImages() {
    if (_selectedImageSet.isNotEmpty) {
      _selectedImageSet.clear();
      _eventStreamController
          .add(AigcEditingMultiSelectEventType.multiSelectionChanged);
      notifyListeners();
    }
  }

  /// 选择所有指定的图片
  void selectAllImages(List<AigcPreviewImageItem> allImages) {
    batchSelectedImages(items: allImages, overrideMode: true);
  }

  /// 刷新选中图片的蒙版路径
  void refreshSelectImageMaskPath() {
    final selectedImagesList = _selectedImageSet.toList();
    for (var index = 0; index < selectedImagesList.length; index++) {
      final file = selectedImagesList[index];
      // 刷新一下数据，确保数据是最新的
      final workspaceId =
          _currentEditingProjectRepository.currentWorkspace?.workspaceId;
      if (workspaceId != null) {
        final refreshedItem = _aigcPreviewImageItemAdapter.refreshMaskPaths(
          file,
          workspaceId,
        );
        // 更新Set中的元素
        _selectedImageSet.remove(file);
        _selectedImageSet.add(refreshedItem);
      }
    }
  }

  @override
  void dispose() {
    _eventStreamController.close();
    super.dispose();
  }
}
