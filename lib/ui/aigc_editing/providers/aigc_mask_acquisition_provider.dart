import 'package:flutter/material.dart';

/// 蒙版获取状态枚举
enum MaskAcquisitionStatus {
  loading, // 正在获取蒙版
  failed,  // 获取蒙版失败
}

/// 蒙版获取状态管理器
/// 负责管理全局的蒙版获取状态
class AigcMaskAcquisitionProvider extends ChangeNotifier {
  /// 蒙版获取状态映射 (fileId -> MaskAcquisitionStatus)
  final Map<String, MaskAcquisitionStatus> _maskAcquisitionStatus = {};

  /// 获取蒙版获取状态
  MaskAcquisitionStatus? getMaskAcquisitionStatus(String fileId) {
    return _maskAcquisitionStatus[fileId];
  }

  /// 设置蒙版获取状态
  void setMaskAcquisitionStatus(String fileId, MaskAcquisitionStatus status) {
    _maskAcquisitionStatus[fileId] = status;
    notifyListeners();
  }

  /// 清除蒙版获取状态
  void clearMaskAcquisitionStatus(String fileId) {
    _maskAcquisitionStatus.remove(fileId);
    notifyListeners();
  }

  /// 清除所有蒙版获取状态
  void clearAllMaskAcquisitionStatus() {
    _maskAcquisitionStatus.clear();
    notifyListeners();
  }

  /// 检查是否有正在进行的蒙版获取任务
  bool get hasLoadingTasks {
    return _maskAcquisitionStatus.values.contains(MaskAcquisitionStatus.loading);
  }

  /// 获取正在加载的文件ID列表
  List<String> get loadingFileIds {
    return _maskAcquisitionStatus.entries
        .where((entry) => entry.value == MaskAcquisitionStatus.loading)
        .map((entry) => entry.key)
        .toList();
  }

  /// 获取失败的文件ID列表
  List<String> get failedFileIds {
    return _maskAcquisitionStatus.entries
        .where((entry) => entry.value == MaskAcquisitionStatus.failed)
        .map((entry) => entry.key)
        .toList();
  }
}