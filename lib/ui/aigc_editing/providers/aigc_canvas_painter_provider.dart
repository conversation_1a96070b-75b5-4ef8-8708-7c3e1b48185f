import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_constant.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC画布绘制器Provider
///
/// 负责管理画布的缩放、平移等变换操作的数据和状态
/// 通过ChangeNotifier模式驱动UI刷新
class AigcCanvasPainterProvider extends ChangeNotifier {
  /// 画布缩放比例
  double _scale = 1.0;

  /// 画布偏移量
  Offset _offset = Offset.zero;

  /// 是否正在拖拽
  bool _isDragging = false;

  /// 是否正在缩放
  bool _isZooming = false;

  /// 画布尺寸
  Size? _canvasSize;

  Size? _defaultCanvasSize;

  /// 图片内容尺寸
  Size? _contentSize;

  /// 获取画布缩放比例
  double get scale => _scale;

  /// 获取画布偏移量
  Offset get offset => _offset;

  /// 获取是否正在拖拽
  bool get isDragging => _isDragging;

  /// 获取是否正在缩放
  bool get isZooming => _isZooming;

  /// 获取画布尺寸
  Size? get canvasSize => _canvasSize;

  /// 获取内容尺寸
  Size? get contentSize => _contentSize;

  /// 用户是否手动缩放过（用于区分自动适配和手动操作）
  bool _hasUserManuallyScaled = false;

  /// 获取用户是否手动缩放过
  bool get hasUserManuallyScaled => _hasUserManuallyScaled;

  set _changeCanvasSize(Size? value) {
    _defaultCanvasSize ??= value;
    _canvasSize = value;
  }

  set _changeScale(double value) {
    _scale = value;
    // 只有在有内容尺寸时才更新画布尺寸
    if (_contentSize != null) {
      // 直接设置画布尺寸，不触发偏移量重置
      final newCanvasSize = Size(
        _contentSize!.width * _scale,
        _contentSize!.height * _scale,
      );
      _canvasSize = newCanvasSize;
    }
  }

  /// 重置默认画布尺寸
  void resetDefaultCanvasSize() {
    _canvasSize = _defaultCanvasSize;
  }

  /// 标记用户手动缩放
  void markUserManuallyScaled() {
    _hasUserManuallyScaled = true;
  }

  /// 重置用户手动缩放标记（在加载新图片时调用）
  void resetUserManuallyScaled() {
    _hasUserManuallyScaled = false;
  }

  /// 设置画布缩放比例
  void setScale(double value) {
    // 限制缩放范围在0.1到5.0之间，并应用5%重叠面积约束
    final constrainedScale = _constrainScale(value);

    if (_scale != constrainedScale) {
      _changeScale = constrainedScale;

      // 缩放后重新约束偏移量，确保保持5%重叠面积
      _offset = _constrainOffset(_offset);

      notifyListeners();
    }
  }

  /// 直接设置画布缩放比例（不触发通知，用于初始化或自动适配）
  void setScaleSilently(double value) {
    // 限制缩放范围在0.1到5.0之间，并应用5%重叠面积约束
    final constrainedScale = _constrainScale(value);

    if (_scale != constrainedScale) {
      _changeScale = constrainedScale;

      // 缩放后重新约束偏移量，确保保持5%重叠面积
      _offset = _constrainOffset(_offset);

      // 不调用 notifyListeners()，避免触发动画
    }
  }

  /// 约束缩放比例，确保在当前偏移量下能保持至少5%重叠面积
  double _constrainScale(double newScale) {
    // 基本范围限制
    final constrainedScale = newScale.clamp(
        AigcConstant.minCanvasScale, AigcConstant.maxCanvasScale);

    if (_canvasSize == null || _contentSize == null) {
      return constrainedScale;
    }

    if (_constrainScaleByOverlapArea(constrainedScale, _offset)) {
      return constrainedScale;
    }

    // 如果不满足要求，返回当前缩放比例（不允许缩放）
    return _scale;
  }

  /// 约束缩放比例，确保在当前偏移量下能保持至少2%重叠面积
  bool _constrainScaleByOverlapArea(double newScale, Offset newOffset) {
    // 计算在当前偏移量下，这个缩放比例是否能保持5%重叠面积
    final scaledContentSize = _contentSize! * newScale;
    final centerX = _defaultCanvasSize!.width / 2 - scaledContentSize.width / 2;
    final centerY =
        _defaultCanvasSize!.height / 2 - scaledContentSize.height / 2;

    final painterLeft = centerX + newOffset.dx;
    final painterTop = centerY + newOffset.dy;
    final painterRight = painterLeft + scaledContentSize.width;
    final painterBottom = painterTop + scaledContentSize.height;

    const canvasLeft = 0.0;
    const canvasTop = 0.0;
    final canvasRight = _defaultCanvasSize!.width;
    final canvasBottom = _defaultCanvasSize!.height;

    // 计算重叠区域
    final overlapLeft = math.max(painterLeft, canvasLeft);
    final overlapTop = math.max(painterTop, canvasTop);
    final overlapRight = math.min(painterRight, canvasRight);
    final overlapBottom = math.min(painterBottom, canvasBottom);

    // 计算重叠面积
    final overlapWidth = math.max(0.0, overlapRight - overlapLeft);
    final overlapHeight = math.max(0.0, overlapBottom - overlapTop);
    final overlapArea = overlapWidth * overlapHeight;

    final painterArea = scaledContentSize.width * scaledContentSize.height;
    final minOverlapArea = painterArea * 0.02;

    // 如果重叠面积满足2%要求
    if (overlapArea >= minOverlapArea) {
      return true;
    }

    return false;
  }

  /// 设置画布偏移量
  void setOffset(Offset value) {
    // 应用平移约束规则
    final constrainedOffset = _constrainOffset(value);

    if (_offset != constrainedOffset) {
      _offset = constrainedOffset;
      notifyListeners();
    }
  }

  /// 设置是否正在拖拽
  void setDragging({required bool isDragging}) {
    if (_isDragging != isDragging) {
      _isDragging = isDragging;
      notifyListeners();
    }
  }

  /// 设置是否正在缩放
  void setZooming({required bool isZooming}) {
    if (_isZooming != isZooming) {
      _isZooming = isZooming;
      notifyListeners();
    }
  }

  /// 设置内容尺寸（根据背景图片尺寸设定）
  void setContentSize(Size size) {
    if (_contentSize != size) {
      _contentSize = size;
      notifyListeners();
    }
  }

  /// 应用缩放变换
  void applyScale(double scaleFactor, Offset focalPoint) {
    final newScale = (_scale * scaleFactor)
        .clamp(AigcConstant.minCanvasScale, AigcConstant.maxCanvasScale);

    if (newScale != _scale) {
      // 计算缩放后的新偏移量，保持焦点位置不变
      final scaleDelta = newScale / _scale;
      final newOffset = focalPoint - (focalPoint - _offset) * scaleDelta;

      _changeScale = newScale;

      // 应用平移约束规则
      _offset = _constrainOffset(newOffset);

      notifyListeners();
    }
  }

  /// 应用偏移变换
  void applyOffset(Offset delta) {
    final newOffset = _offset + delta;

    // 应用平移约束规则
    final constrainedOffset = _constrainOffset(newOffset);

    if (_offset != constrainedOffset) {
      _offset = constrainedOffset;
      notifyListeners();
    }
  }

  /// 约束偏移量，确保重叠面积至少为2%
  Offset _constrainOffset(Offset newOffset) {
    if (_canvasSize == null || _contentSize == null) {
      return newOffset;
    }

    if (_constrainScaleByOverlapArea(_scale, newOffset)) {
      return newOffset;
    }

    // 如果不满足要求，返回当前偏移量（不允许移动）
    return _offset;
  }

  /// 重置变换
  void resetTransform() {
    _scale = 1.0;
    _offset = Offset.zero;
    _hasUserManuallyScaled = false; // 重置用户手动缩放标记
    notifyListeners();
  }

  /// 获取当前的变换矩阵
  Matrix4 getTransformMatrix() {
    return Matrix4.identity()
      ..translate(_offset.dx, _offset.dy)
      ..scale(_scale);
  }

  /// 计算内容在画布中的居中偏移
  Offset calculateCenterOffset() {
    if (_canvasSize == null || _contentSize == null) {
      return Offset.zero;
    }

    final scaledContentSize = _contentSize! * _scale;
    final centerX = (_canvasSize!.width - scaledContentSize.width) / 2;
    final centerY = (_canvasSize!.height - scaledContentSize.height) / 2;

    return Offset(centerX, centerY);
  }

  /// 检查点是否在内容区域内
  bool isPointInContent(Offset point) {
    if (_contentSize == null) {
      return false;
    }

    final contentRect = Rect.fromLTWH(
      _offset.dx,
      _offset.dy,
      _contentSize!.width * _scale,
      _contentSize!.height * _scale,
    );

    return contentRect.contains(point);
  }

  /// 检查图片在给定画布尺寸下是否完全不可见
  bool _isImageCompletelyOutOfBounds(Size canvasSize) {
    if (_contentSize == null) {
      return false;
    }

    final scaledContentSize = _contentSize! * _scale;
    final centerX = canvasSize.width / 2 - scaledContentSize.width / 2;
    final centerY = canvasSize.height / 2 - scaledContentSize.height / 2;

    final imageLeft = centerX + _offset.dx;
    final imageTop = centerY + _offset.dy;
    final imageRight = imageLeft + scaledContentSize.width;
    final imageBottom = imageTop + scaledContentSize.height;

    // 检查图片是否完全在画布外
    return imageRight <= 0 ||
        imageLeft >= canvasSize.width ||
        imageBottom <= 0 ||
        imageTop >= canvasSize.height;
  }

  /// 设置画布尺寸（智能重置版本）
  void setCanvasSizeWithSmartReset(Size size) {
    if (_canvasSize != size) {
      final oldCanvasSize = _canvasSize;
      _changeCanvasSize = size;

      // 只有当图片会完全不可见时才重置偏移量
      if (_contentSize != null && oldCanvasSize != null) {
        if (_isImageCompletelyOutOfBounds(size)) {
          PGLog.d('Image would be completely out of bounds, resetting offset');
          _offset = Offset.zero;
        }
      }

      notifyListeners();
    }
  }
}
