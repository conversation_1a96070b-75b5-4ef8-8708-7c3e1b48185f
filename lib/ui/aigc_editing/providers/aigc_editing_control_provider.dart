import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_constant.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';

/// 编辑器控制器，用于控制编辑器的一些操作
class AigcEditingControlProvider extends ChangeNotifier {
  /// 画笔大小
  double _brushSize = AigcConstant.defaultBrushSize;

  /// 是否显示单个蒙版范围
  bool _isShowSingleMattingScope = true;

  /// 是否初始化蒙版状态
  bool _isInitialMattingState = true;

  bool _isLoadBackgroundImageStatus = false;

  bool _isBackgroundMattingStatus = true;

  /// 蒙版显示模式
  MaskDisplayMode _displayMode = MaskDisplayMode.overlay;

  /// 蒙版背景颜色
  MaskBackgroundColor _maskBackgroundColor = MaskBackgroundColor.black;

  /// 当前智能框选模式是否启动
  bool _isSmartBoxSelectionEnabled = false;

  double get brushSize => _brushSize;

  bool get isShowSingleMattingScope => _isShowSingleMattingScope;

  bool get isInitialMattingState => _isInitialMattingState;

  bool get isEnabledControlArea =>
      _isLoadBackgroundImageStatus && _isBackgroundMattingStatus;

  MaskBackgroundColor get maskBackgroundColor => _maskBackgroundColor;

  MaskDisplayMode get displayMode => _displayMode;

  bool get isInSmartBoxSelectionMode => _isSmartBoxSelectionEnabled;

  void setDefalutControlState() {
    _isInitialMattingState = true;
  }

  /// 判断当前状态是否为默认控制状态
  bool get isDefaultControlState {
    return _isInitialMattingState == true;
  }

  void setDefaultControlState() {
    _isInitialMattingState = true;
    notifyListeners();
  }

  /// 设置画笔大小
  void setBrushSize(double value) {
    _brushSize = value;
    notifyListeners();
  }

  /// 设置是否显示单个蒙版范围
  void setIsShowSingleMattingScope({required bool value}) {
    _isShowSingleMattingScope = value;
    notifyListeners();
  }

  /// 重置蒙版状态
  void resetMattingState() {
    _isInitialMattingState = !_isInitialMattingState;
    notifyListeners();
  }

  /// 设置重置控制状态
  void setResetControlState() {
    resetMattingState();
    // 重置时将蒙版显示模式切换为"显示叠加"
    _displayMode = MaskDisplayMode.overlay;
    notifyListeners();
  }

  /// 设置蒙版背景颜色
  void setMaskBackgroundColor(MaskBackgroundColor value) {
    if (_maskBackgroundColor != value) {
      _maskBackgroundColor = value;
      notifyListeners();
    }
  }

  /// 设置蒙版显示模式
  void setMaskDisplayMode(MaskDisplayMode value) {
    if (_displayMode != value) {
      _displayMode = value;

      // 当切换到纯色背景模式时，默认选择第一个颜色（透明）
      if (value == MaskDisplayMode.pureColor) {
        _maskBackgroundColor = MaskBackgroundColor.transparent;
      } else {
        _maskBackgroundColor = MaskBackgroundColor.black;
      }

      notifyListeners();
    }
  }

  /// 设置是否加载背景图片
  void setIsLoadBackgroundImageStatus({required bool value}) {
    _isLoadBackgroundImageStatus = value;
    notifyListeners();
  }

  /// 设置是否蒙版
  void setIsBackgroundMattingStatus({required bool value}) {
    _isBackgroundMattingStatus = value;
    notifyListeners();
  }

  /// 设置是否启动智能框选模式
  void setSmartBoxModeEnabled({required bool value}) {
    if (_isSmartBoxSelectionEnabled != value) {
      _isSmartBoxSelectionEnabled = value;

      notifyListeners();
    }
  }
}
