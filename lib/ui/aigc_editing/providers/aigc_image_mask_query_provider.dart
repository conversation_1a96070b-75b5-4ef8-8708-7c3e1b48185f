import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_history_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC图片蒙版查询Provider
///
/// 用于查询指定图片的蒙版路径，支持批量查询
/// 不依赖当前选中的图片，可以查询任意图片的蒙版信息
class AigcImageMaskQueryProvider {
  final AigcEditingHistoryProvider _historyProvider;

  AigcImageMaskQueryProvider({
    required AigcEditingHistoryProvider historyProvider,
  }) : _historyProvider = historyProvider;

  /// 获取指定图片的首选蒙版路径
  ///
  /// 参数：
  /// - image: 要查询的图片
  ///
  /// 返回：
  /// - String?: 蒙版路径，如果没有蒙版则返回null
  ///
  /// 优先级：交互式蒙版 > 原始蒙版
  String? getPreferredMaskPath(AigcPreviewImageItem image) {
    // 优先获取交互式蒙版路径
    final interactiveMaskPath = getInteractiveMaskPath(image);
    if (interactiveMaskPath != null) {
      return interactiveMaskPath;
    }

    // 如果没有交互式蒙版，获取原始蒙版路径
    return getOriginalMaskPath(image);
  }

  /// 获取指定图片的交互式蒙版路径
  ///
  /// 参数：
  /// - image: 要查询的图片
  ///
  /// 返回：
  /// - String?: 交互式蒙版路径，如果没有则返回null
  String? getInteractiveMaskPath(AigcPreviewImageItem image) {
    try {
      // 获取指定图片的历史记录管理器
      final manager = _historyProvider.getManager(image.fileId);
      if (manager == null) {
        return null;
      }

      // 获取该图片的所有有效历史记录
      final validNodes = manager.getAllUndoRecords();
      if (validNodes.isEmpty) {
        return null;
      }

      // 获取最新的历史记录
      final latestHistory = validNodes.last.data;
      final maskDirectoryPath = latestHistory.maskPath;

      final maskFilePath = path.join(
        maskDirectoryPath,
        MediaResourceConstants.getUniversalMaskFileName(),
      );

      if (File(maskFilePath).existsSync()) {
        return maskFilePath;
      }

      return null;
    } catch (e) {
      PGLog.e('获取交互式蒙版路径失败: $e');
      return null;
    }
  }

  /// 获取指定图片的原始蒙版路径
  ///
  /// 参数：
  /// - image: 要查询的图片
  ///
  /// 返回：
  /// - String?: 原始蒙版路径，如果没有则返回null
  String? getOriginalMaskPath(AigcPreviewImageItem image) {
    try {
      final maskPath = image.maskPath;
      if (maskPath != null && File(maskPath).existsSync()) {
        return maskPath;
      }
      return null;
    } catch (e) {
      PGLog.e('获取原始蒙版路径失败: $e');
      return null;
    }
  }

  /// 检查指定图片是否有蒙版
  ///
  /// 参数：
  /// - image: 要检查的图片
  ///
  /// 返回：
  /// - bool: 是否有蒙版
  bool hasMask(AigcPreviewImageItem image) {
    return getPreferredMaskPath(image) != null;
  }

  /// 检查指定图片是否有交互式蒙版
  ///
  /// 参数：
  /// - image: 要检查的图片
  ///
  /// 返回：
  /// - bool: 是否有交互式蒙版
  bool hasInteractiveMask(AigcPreviewImageItem image) {
    return getInteractiveMaskPath(image) != null;
  }

  /// 检查指定图片是否有原始蒙版
  ///
  /// 参数：
  /// - image: 要检查的图片
  ///
  /// 返回：
  /// - bool: 是否有原始蒙版
  bool hasOriginalMask(AigcPreviewImageItem image) {
    return getOriginalMaskPath(image) != null;
  }

  /// 检查指定图片从最近的reset操作到最新记录之间是否存在regional操作
  ///
  /// 参数：
  /// - image: 要检查的图片
  ///
  /// 返回：
  /// - bool: 是否存在regional操作
  bool hasRegionalOperationSinceLastReset(AigcPreviewImageItem image) {
    try {
      // 临时切换到指定图片的历史记录上下文
      final originalImageId = _historyProvider.currentImageId;

      // 使用扩展方法需要先设置当前图片ID
      _historyProvider.setCurrentImage(image.fileId);

      final result = _historyProvider.hasRegionalOperationSinceLastReset();

      // 恢复原来的图片ID
      if (originalImageId != null) {
        _historyProvider.setCurrentImage(originalImageId);
      }

      return result;
    } catch (e) {
      PGLog.e('检查regional操作失败: $e');
      return false;
    }
  }

  /// 批量获取多个图片的蒙版信息
  ///
  /// 参数：
  /// - images: 要查询的图片列表
  ///
  /// 返回：
  /// - Map<String, String?>: 图片ID到蒙版路径的映射
  Map<String, String?> getBatchMaskPaths(List<AigcPreviewImageItem> images) {
    final result = <String, String?>{};

    for (final image in images) {
      try {
        result[image.fileId] = getPreferredMaskPath(image);
      } catch (e) {
        PGLog.e('批量获取蒙版路径失败，图片ID: ${image.fileId}, 错误: $e');
        result[image.fileId] = null;
      }
    }

    return result;
  }

  /// 批量检查多个图片是否有蒙版
  ///
  /// 参数：
  /// - images: 要检查的图片列表
  ///
  /// 返回：
  /// - Map<String, bool>: 图片ID到是否有蒙版的映射
  Map<String, bool> getBatchMaskStatus(List<AigcPreviewImageItem> images) {
    final result = <String, bool>{};

    for (final image in images) {
      try {
        result[image.fileId] = hasMask(image);
      } catch (e) {
        PGLog.e('批量检查蒙版状态失败，图片ID: ${image.fileId}, 错误: $e');
        result[image.fileId] = false;
      }
    }

    return result;
  }

  /// 获取指定图片的蒙版详细信息
  ///
  /// 参数：
  /// - image: 要查询的图片
  ///
  /// 返回：
  /// - ImageMaskInfo: 蒙版详细信息
  ImageMaskInfo getMaskInfo(AigcPreviewImageItem image) {
    return ImageMaskInfo(
      imageId: image.fileId,
      preferredMaskPath: getPreferredMaskPath(image),
      interactiveMaskPath: getInteractiveMaskPath(image),
      originalMaskPath: getOriginalMaskPath(image),
      hasMask: hasMask(image),
      hasInteractiveMask: hasInteractiveMask(image),
      hasOriginalMask: hasOriginalMask(image),
      hasRegionalOperation: hasRegionalOperationSinceLastReset(image),
    );
  }

  /// 批量获取多个图片的蒙版详细信息
  ///
  /// 参数：
  /// - images: 要查询的图片列表
  ///
  /// 返回：
  /// - List<ImageMaskInfo>: 蒙版详细信息列表
  List<ImageMaskInfo> getBatchMaskInfo(List<AigcPreviewImageItem> images) {
    return images.map((image) => getMaskInfo(image)).toList();
  }
}

/// 图片蒙版信息模型
class ImageMaskInfo {
  /// 图片ID
  final String imageId;

  /// 首选蒙版路径
  final String? preferredMaskPath;

  /// 交互式蒙版路径
  final String? interactiveMaskPath;

  /// 原始蒙版路径
  final String? originalMaskPath;

  /// 是否有蒙版
  final bool hasMask;

  /// 是否有交互式蒙版
  final bool hasInteractiveMask;

  /// 是否有原始蒙版
  final bool hasOriginalMask;

  /// 是否有regional操作
  final bool hasRegionalOperation;

  ImageMaskInfo({
    required this.imageId,
    this.preferredMaskPath,
    this.interactiveMaskPath,
    this.originalMaskPath,
    required this.hasMask,
    required this.hasInteractiveMask,
    required this.hasOriginalMask,
    required this.hasRegionalOperation,
  });

  @override
  String toString() {
    return 'ImageMaskInfo{'
        'imageId: $imageId, '
        'preferredMaskPath: $preferredMaskPath, '
        'interactiveMaskPath: $interactiveMaskPath, '
        'originalMaskPath: $originalMaskPath, '
        'hasMask: $hasMask, '
        'hasInteractiveMask: $hasInteractiveMask, '
        'hasOriginalMask: $hasOriginalMask, '
        'hasRegionalOperation: $hasRegionalOperation'
        '}';
  }
}
