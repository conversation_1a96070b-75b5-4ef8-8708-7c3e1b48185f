import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/model/handler/aigc_regional_logic_frame.dart';
import 'package:turing_art/ui/aigc_editing/model/handler/touch_chain_handler.dart';
import 'package:turing_art/ui/aigc_editing/model/history/aigc_editing_history_state.dart';
import 'package:turing_art/ui/aigc_editing/model/regional/aigc_regional_frame.dart';

/// 区域框处理器Provider, 切换AigcEditingImageProvider的selectedImage时, 需要重新加载区域框处理器
class AigcRegionalFrameProvider extends ChangeNotifier {
  /// 区域框处理器列表
  final List<TouchChainHandler> _frameHandlers = [];

  /// 当前Canvas显示可见的区域框处理器
  AigcRegionalLogicFrame? get currentLogicFrame => _frameHandlers.isNotEmpty
      ? _frameHandlers.lastOrNull as AigcRegionalLogicFrame
      : null;

  /// 生成区域框处理器
  void _generateLogicFrame(
      Offset startPosition, Offset endPosition, double scale) {
    final handler = AigcRegionalLogicFrame(
        Offset(
            startPosition.dx.floorToDouble(), startPosition.dy.floorToDouble()),
        Offset(endPosition.dx.floorToDouble(), endPosition.dy.floorToDouble()),
        scale);
    if (_frameHandlers.isNotEmpty) {
      // 将新添加的处理器设置为最后一个处理器的下一个处理器, 反向链条
      handler.setNext(_frameHandlers.last);
    }
    _frameHandlers.add(handler);
  }

  /// 添加区域框处理器, 添加一步可以快速响应，暂时不走数据流更新的触发流程，
  /// 从历史记录按钮则走数据流的触发流程
  void addLogicFrame(Offset startPosition, Offset endPosition, double scale) {
    _generateLogicFrame(startPosition, endPosition, scale);
    notifyListeners();
  }

  /// 获取触摸事件处理器
  AigcRegionalLogicFrame? fetchTouchLogicFrame(Offset touchPosition) {
    // TODO: 临时注销，后续使用
    // if (_frameHandlers.isNotEmpty) {
    //   // 从后往前遍历，找到第一个处理触摸事件的处理器
    //   final handler = _frameHandlers.last.handleTouch(touchPosition);
    //   if (handler != null && handler is AigcRegionalLogicFrame) {
    //     return handler;
    //   }
    // }
    // return null;

    // 临时使用，后续删除
    if (_frameHandlers.isNotEmpty) {
      return _frameHandlers.last as AigcRegionalLogicFrame;
    }
    return null;
  }

  /// 静态方法：从历史记录状态提取区域框数据
  static List<Map<String, dynamic>> _extractRegionalFrameData(
      List<AigcEditingHistoryState> states) {
    return states
        .where((state) => state.extraData.containsKey('regionalFrame'))
        .map(
            (state) => state.extraData['regionalFrame'] as Map<String, dynamic>)
        .toList();
  }

  /// 根据历史记录状态生成区域框处理器
  void _generateFrameFromData(List<Map<String, dynamic>> frameDataList) {
    for (final frameData in frameDataList) {
      final regionalFrame = AigcRegionalFrame.fromJson(frameData);
      _generateLogicFrame(regionalFrame.startPosition,
          regionalFrame.endPosition, regionalFrame.scale);
    }
  }

  /// 更新区域框处理器
  Future<void> reloadRegionalFramesFromHistoryStates(
      List<AigcEditingHistoryState> states) async {
    _frameHandlers.clear();

    // 提取可序列化的数据
    final frameDataList = _extractRegionalFrameData(states);

    if (frameDataList.isNotEmpty) {
      _generateFrameFromData(frameDataList);
    }

    // 通知刷新
    notifyListeners();
  }
}
