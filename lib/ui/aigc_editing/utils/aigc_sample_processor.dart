import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.internal.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_request.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/datalayer/service/api/common_error_handler.dart';
import 'package:turing_art/ffi/services/image_processor_service.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 批量处理错误策略
enum BatchErrorStrategy {
  /// 遇到错误时跳过继续执行（默认）
  skipOnError,

  /// 遇到错误时停止整个批量处理
  stopOnError,
}

/// 打样处理器
/// 用于处理单个和批量打样的核心业务逻辑
class AigcSampleProcessor {
  final AigcSampleRepository _aigcSampleRepository;
  final MediaUploadRepository _mediaUploadRepository;
  final CurrentEditingProjectRepository _currentEditingProjectRepository;

  AigcSampleProcessor({
    required AigcSampleRepository aigcSampleRepository,
    required MediaUploadRepository mediaUploadRepository,
    required CurrentEditingProjectRepository currentEditingProjectRepository,
  })  : _aigcSampleRepository = aigcSampleRepository,
        _mediaUploadRepository = mediaUploadRepository,
        _currentEditingProjectRepository = currentEditingProjectRepository;

  /// 创建单个打样
  ///
  /// 参数：
  /// - selectedImage: 选中的图片
  /// - selectedEffect: 选中的创意效果（可为null，表示原图打样）
  /// - presetsModel: 选中的预设模型（可为null，表示原图打样）
  /// - preferredMaskPath: 首选蒙版路径（可为null）
  /// - isOriginal: 是否为原图打样
  /// - isCancelledCallback: 取消检查回调函数，返回true表示操作已被取消
  ///
  /// 返回：
  /// - (bool, String?): 成功状态和错误信息
  Future<(bool, String?)> createSample({
    required AigcPreviewImageItem selectedImage,
    AigcPresetsEffect? selectedEffect,
    AigcPresetsModel? presetsModel,
    String? preferredMaskPath,
    bool isOriginal = false,
    bool Function()? isCancelledCallback, // 取消检查回调
  }) async {
    // 验证参数
    if (!isOriginal && (selectedEffect == null || presetsModel == null)) {
      PGLog.e('不是原图打样，但是创意和主题为空');
      return (false, '未选择主题和创意');
    }

    // 处理effectCode和presetId
    String effectCode = isOriginal ? '' : selectedEffect?.effectCode ?? '';
    if (effectCode == AigcPresetsModelInternal.internalAIGCEffectId) {
      effectCode = '';
    }

    String presetId = isOriginal ? '' : presetsModel!.id;
    if (presetId == AigcPresetsModelInternal.internalAIGCPresetId) {
      presetId = '';
    }

    // 获取当前工作区
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      PGLog.e('工作区为空');
      return (false, '工作区为空');
    }

    // 验证图片路径
    if (selectedImage.previewPath == null ||
        selectedImage.previewPath!.isEmpty) {
      PGLog.e('预览图路径为空');
      return (false, '预览图路径为空');
    }

    try {
      // 检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('单个打样任务被取消：开始前检查');
        return (false, '打样任务已取消');
      }

      // 1. 上传原图（如果需要）
      final originImageResultUrl = await _uploadLargeImageIfNeed(
        workspace.workspaceId,
        selectedImage.fileId,
        selectedImage.highQualityPath ?? '',
      );
      PGLog.d('createSample上传原图成功: $originImageResultUrl');

      // 检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('单个打样任务被取消：上传原图后检查');
        return (false, '打样任务已取消');
      }

      // 2. 上传预览图
      final previewImageUrl =
          await _uploadOriginImage(selectedImage.previewPath!);
      if (previewImageUrl.isEmpty) {
        PGLog.e('上传预览图失败');
        return (false, '上传预览图失败');
      }
      PGLog.d('createSample上传预览图成功: $previewImageUrl');

      // 检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('单个打样任务被取消：上传预览图后检查');
        return (false, '打样任务已取消');
      }

      // 3. 处理并上传遮罩图
      final processedMaskPath = await _processMaskImage(preferredMaskPath);
      final maskImageUrl = await _uploadMaskImage(processedMaskPath ?? '');
      PGLog.d('createSample上传遮罩图成功: $maskImageUrl');

      // 检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('单个打样任务被取消：上传遮罩图后检查');
        return (false, '打样任务已取消');
      }

      // 4. 创建打样请求
      final request = AigcSampleRequest(
        clientProject: ClientProject(
          projectId: workspace.workspaceId,
          projectName: workspace.workspaceName,
          updateAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        ),
        preset: Preset(
          presetId: presetId,
          effectCode: effectCode,
        ),
        originPhoto: OriginPhoto(
          fileName: path.basenameWithoutExtension(selectedImage.originalPath),
          fileUrl: previewImageUrl,
        ),
        maskImageUrl: maskImageUrl,
        clientResourceId: selectedImage.fileId,
      );

      // 检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('单个打样任务被取消：调用API前检查');
        return (false, '打样任务已取消');
      }

      // 5. 调用创建打样接口(错误统一走CommonBusinessErrorHandler)
      final result = await _aigcSampleRepository.createAigcSample(request);

      // 检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('单个打样任务被取消：调用API后检查');
        return (false, '打样任务已取消');
      }

      // 6. 更新样片详情（如果有原图URL）
      if (originImageResultUrl.isNotEmpty) {
        await _aigcSampleRepository.updateAigcSampleInfo(
          result.id,
          originImageResultUrl,
        );
      }

      // 最后检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('单个打样任务被取消：更新样片详情后检查');
        return (false, '打样任务已取消');
      }

      return (true, null);
    } catch (e) {
      PGLog.e('创建打样失败: $e');
      // 使用统一的错误处理器
      final (errorMessage, _) =
          CommonBusinessErrorHandler.handleError(e, '创建打样');
      return (false, errorMessage);
    }
  }

  /// 批量创建打样
  ///
  /// 参数：
  /// - sampleRequests: 批量打样请求列表
  /// - onProgress: 进度回调 (当前索引, 总数, 当前项结果)
  /// - errorStrategy: 错误处理策略，默认为跳过错误继续执行
  /// - isTestMode: 测试模式，每个任务延迟2秒完成，默认全部成功
  /// - isCancelledCallback: 取消检查回调函数，返回true表示操作已被取消
  ///
  /// 返回：
  /// - List<(bool, String?)>: 每个打样的结果列表
  ///
  /// 注意：批量处理为串行执行，根据errorStrategy决定错误处理方式
  Future<List<(bool, String?)>> createBatchSamples({
    required List<BatchSampleRequest> sampleRequests,
    Function(int current, int total, {required bool success, String? error})?
        onProgress,
    BatchErrorStrategy errorStrategy = BatchErrorStrategy.skipOnError,
    bool Function()? isCancelledCallback, // 取消检查回调
  }) async {
    final results = <(bool, String?)>[];
    // 执行实际打样任务
    for (int i = 0; i < sampleRequests.length; i++) {
      // 检查是否被取消
      if (isCancelledCallback?.call() == true) {
        PGLog.d('批量打样：第${i + 1}个任务被取消');
        break;
      }

      final request = sampleRequests[i];
      (bool, String?) result;

      try {
        // 执行单个打样任务，传递取消回调
        result = await createSample(
          selectedImage: request.selectedImage,
          selectedEffect: request.selectedEffect,
          presetsModel: request.presetsModel,
          preferredMaskPath: request.preferredMaskPath,
          isOriginal: request.isOriginal,
          isCancelledCallback: isCancelledCallback,
        );
      } catch (e) {
        // 如果出现异常，记录错误
        PGLog.e('批量打样第${i + 1}个任务异常: $e');
        result = (false, '任务执行异常: $e');
      }

      // 再次检查是否被取消（任务执行后可能状态已改变）
      if (isCancelledCallback?.call() == true) {
        PGLog.d('批量打样：第${i + 1}个任务完成后被取消');
        break;
      }

      results.add(result);

      // 调用进度回调
      onProgress?.call(i + 1, sampleRequests.length,
          success: result.$1, error: result.$2);

      // 根据错误策略处理失败情况
      if (!result.$1) {
        if (errorStrategy == BatchErrorStrategy.stopOnError) {
          PGLog.e('批量打样第${i + 1}个任务失败，根据策略停止执行: ${result.$2}');
          break;
        } else {
          PGLog.w('批量打样第${i + 1}个任务失败，跳过继续执行: ${result.$2}');
        }
      }
    }

    // 输出批量处理结果统计
    final successCount = results.where((r) => r.$1).length;
    final failureCount = results.length - successCount;
    final totalRequests = sampleRequests.length;
    final processedCount = results.length;

    if (processedCount < totalRequests) {
      PGLog.d(
          '批量打样提前终止: 总计$totalRequests个，已处理$processedCount个，成功$successCount个，失败$failureCount个');
    } else {
      PGLog.d('批量打样完成: 总计$totalRequests个，成功$successCount个，失败$failureCount个');
    }

    return results;
  }

  /// 判断是否需要上传大图
  Future<String> _uploadLargeImageIfNeed(
    String projectId,
    String fileId,
    String largeImagePath,
  ) async {
    final (isNeedUpload, largeImageUrl) =
        await _isNeedUploadOriginImage(projectId, fileId);

    if (isNeedUpload && largeImageUrl == null) {
      if (largeImagePath.isEmpty) {
        PGLog.e('createSample上传原图失败，大图路径为空');
        return '';
      }

      final originImageResultUrl = await _uploadOriginImage(largeImagePath);
      if (originImageResultUrl.isEmpty) {
        PGLog.e('createSample上传原图失败');
        return '';
      }
      return originImageResultUrl;
    } else {
      // 不需要上传，已有原图url
      final uri = Uri.parse(largeImageUrl!);
      final originImageResultUrl =
          uri.replace(query: '').toString().replaceAll('?', '');
      PGLog.d('createSample已有原图不需要上传: $originImageResultUrl');
      return originImageResultUrl;
    }
  }

  /// 判断是否需要上传原图
  Future<(bool, String?)> _isNeedUploadOriginImage(
      String projectId, String fileId) async {
    try {
      final samples = await _aigcSampleRepository.getAigcSampleList('');

      AigcSampleModel? targetSample;
      try {
        targetSample = samples.firstWhere(
          (sample) =>
              sample.projectId == projectId &&
              sample.clientResourceId == fileId,
        );
      } catch (e) {
        targetSample = null;
      }

      if (targetSample != null && targetSample.isLargeImageUploaded) {
        return (false, targetSample.largePhotoUrl);
      }

      return (true, null);
    } catch (e) {
      PGLog.e('获取打样列表失败: $e');
      return (true, null);
    }
  }

  /// 上传原图
  Future<String> _uploadOriginImage(String imagePath) async {
    if (imagePath.isEmpty) {
      PGLog.e('未选择图片');
      return '';
    }

    final result =
        await _mediaUploadRepository.uploadSingleFile(File(imagePath));
    return result.success ? (result.publicUrl ?? '') : '';
  }

  /// 上传遮罩图
  Future<String> _uploadMaskImage(String imagePath) async {
    if (imagePath.isEmpty) {
      return '';
    }

    final result =
        await _mediaUploadRepository.uploadSingleFile(File(imagePath));
    return result.success ? (result.publicUrl ?? '') : '';
  }

  /// 处理遮罩图像
  Future<String?> _processMaskImage(String? maskPath) async {
    try {
      if (maskPath == null || maskPath.isEmpty) {
        PGLog.d('遮罩图路径为空，跳过处理');
        return null;
      }

      final maskFile = File(maskPath);
      if (!maskFile.existsSync()) {
        PGLog.e('遮罩图文件不存在: $maskPath');
        return null;
      }

      final maskDirectory = maskFile.parent;
      final uploadMaskPath = path.join(
          maskDirectory.path, MediaResourceConstants.getPsUploadMaskFileName());
      final uploadMaskFile = File(uploadMaskPath);

      if (uploadMaskFile.existsSync()) {
        await uploadMaskFile.delete();
        PGLog.d('删除已存在的uploadMask.png文件: $uploadMaskPath');
      }

      const config = ImageProcessorConfig(
        targetWidth: ImageConstants.aigcPreviewSize,
        targetHeight: ImageConstants.aigcPreviewSize,
        maintainAspect: true,
        quality: 95,
        outputFormat: 'png',
        interpolation: ImageInterpolation.cubic,
      );

      await ImageProcessorService.initialize();
      await ImageProcessorService.processFile(maskPath, uploadMaskPath,
          config: config);

      PGLog.d('遮罩图处理成功: $uploadMaskPath');
      return uploadMaskPath;
    } catch (e) {
      PGLog.e('处理遮罩图失败: $e');
      return null;
    }
  }
}

/// 批量打样请求模型
class BatchSampleRequest {
  final AigcPreviewImageItem selectedImage;
  final AigcPresetsEffect? selectedEffect;
  final AigcPresetsModel? presetsModel;
  final String? preferredMaskPath;
  final bool isOriginal;

  BatchSampleRequest({
    required this.selectedImage,
    this.selectedEffect,
    this.presetsModel,
    this.preferredMaskPath,
    this.isOriginal = false,
  });
}
