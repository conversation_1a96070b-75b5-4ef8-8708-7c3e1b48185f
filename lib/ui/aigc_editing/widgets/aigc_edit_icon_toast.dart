import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// AIGC 编辑页面专用的带图标 Toast
///
/// 这是一个完全独立的Toast系统，使用原生Overlay实现，不依赖SmartDialog。
/// 特点：
/// - 使用Flutter原生Overlay，完全独立于SmartDialog系统
/// - 不受任何第三方Dialog系统影响
/// - 专为AIGC编辑页面优化的样式和动画
/// - 支持自定义颜色、持续时间等参数
class AigcEditIconToast {
  // 存储当前显示的Toast的OverlayEntry和控制器
  static OverlayEntry? _currentOverlay;
  static Timer? _dismissTimer;
  static _ToastWidgetState? _currentToastState;
  static _LoadingToastWidgetState? _currentLoadingToastState;

  /// 展示带图标的Toast
  /// [context] 用于获取Overlay的BuildContext
  /// [icon] 左侧显示的图标（IconData）
  /// [iconPath] 左侧显示的图标路径（String），优先级高于icon
  /// [msg] 右侧显示的文字
  /// [isAutoDismiss] 是否自动消失
  /// [top] Toast距离顶部的位置，默认为84像素
  /// [left] Toast距离左侧的位置，默认为0
  /// [backgroundColor] 背景颜色，默认使用主题色
  /// [textColor] 文字颜色，默认使用主题色
  /// [iconColor] 图标颜色，默认使用主题色
  /// [parentWidget] 父Widget的GlobalKey，用于相对定位
  static void showIconToast(
    BuildContext context,
    String msg, {
    IconData? icon,
    String? iconPath,
    bool isAutoDismiss = true,
    double top = 84,
    double left = 0,
    Color? backgroundColor,
    Color? textColor,
    Color? iconColor,
    GlobalKey? parentWidget,
  }) {
    // 先关闭已存在的Toast
    dismissIconToast();
    Duration duration =
        isAutoDismiss ? const Duration(seconds: 3) : Duration.zero;

    // 获取Overlay
    OverlayState? overlay;

    if (parentWidget?.currentContext != null) {
      // 如果指定了父Widget，尝试从父Widget上下文中查找最近的Overlay
      overlay = Overlay.maybeOf(parentWidget!.currentContext!);

      // 如果没有找到父Widget的Overlay，这意味着父Widget可能不在Overlay的子树中
      // 为了确保Toast只在父Widget范围内显示，我们不应该回退到全局Overlay
      if (overlay == null) {
        // 直接返回，不显示Toast
        return;
      }
    } else {
      // 如果没有指定父Widget，使用当前context的Overlay
      overlay = Overlay.of(context);
    }

    // 创建OverlayEntry
    _currentOverlay = OverlayEntry(
      builder: (context) => _ToastWidget(
        icon: icon,
        iconPath: iconPath,
        msg: msg,
        top: top,
        left: left,
        backgroundColor: backgroundColor,
        textColor: textColor,
        iconColor: iconColor,
        parentWidget: parentWidget,
        onStateCreated: (state) {
          _currentToastState = state;
        },
        onDismissComplete: () {
          _currentOverlay?.remove();
          _currentOverlay = null;
          _currentToastState = null;
        },
      ),
    );

    // 插入到Overlay中
    overlay.insert(_currentOverlay!);
    if (isAutoDismiss) {
      // 设置自动关闭定时器
      _dismissTimer = Timer(duration, () {
        dismissIconToast();
      });
    }
  }

  /// 展示Loading Toast（带旋转图标）
  /// [context] 用于获取Overlay的BuildContext
  /// [msg] 右侧显示的文字
  /// [top] Toast距离顶部的位置，默认为84像素
  /// [left] Toast距离左侧的位置，如果为null则水平居中
  /// [backgroundColor] 背景颜色，默认使用主题色
  /// [textColor] 文字颜色，默认使用主题色
  /// [iconColor] 图标颜色，默认使用主题色
  /// [parentWidget] 父Widget的GlobalKey，用于相对定位
  static void showLoadingToast(
    BuildContext context,
    String msg, {
    double top = 84,
    double left = 0,
    Color? backgroundColor,
    Color? textColor,
    Color? iconColor,
    GlobalKey? parentWidget,
  }) {
    // 先关闭已存在的Toast
    dismissIconToast();

    // 获取Overlay
    OverlayState? overlay;

    if (parentWidget?.currentContext != null) {
      // 如果指定了父Widget，尝试从父Widget上下文中查找最近的Overlay
      overlay = Overlay.maybeOf(parentWidget!.currentContext!);

      // 如果没有找到父Widget的Overlay，这意味着父Widget可能不在Overlay的子树中
      // 为了确保Toast只在父Widget范围内显示，我们不应该回退到全局Overlay
      if (overlay == null) {
        // 直接返回，不显示Toast
        return;
      }
    } else {
      // 如果没有指定父Widget，使用当前context的Overlay
      overlay = Overlay.of(context);
    }

    // 创建OverlayEntry
    _currentOverlay = OverlayEntry(
      builder: (context) => _LoadingToastWidget(
        msg: msg,
        top: top,
        left: left,
        backgroundColor: backgroundColor,
        textColor: textColor,
        iconColor: iconColor,
        parentWidget: parentWidget,
        onStateCreated: (state) {
          _currentLoadingToastState = state;
        },
        onDismissComplete: () {
          _currentOverlay?.remove();
          _currentOverlay = null;
          _currentLoadingToastState = null;
        },
      ),
    );

    // 插入到Overlay中
    overlay.insert(_currentOverlay!);

    // Loading Toast 通常不自动关闭，需要手动调用 dismissIconToast()
  }

  /// 手动关闭Toast
  static void dismissIconToast() {
    _dismissTimer?.cancel();
    _dismissTimer = null;

    // 如果有正在显示的普通Toast，触发消失动画
    if (_currentToastState != null) {
      _currentToastState!.dismiss();
    }
    // 如果有正在显示的Loading Toast，触发消失动画
    else if (_currentLoadingToastState != null) {
      _currentLoadingToastState!.dismiss();
    }
    // 如果没有状态引用，直接移除
    else {
      _currentOverlay?.remove();
      _currentOverlay = null;
    }
  }

  /// 检查是否有正在显示的Toast
  static bool isIconToastVisible() {
    return _currentOverlay != null;
  }
}

/// Toast显示组件
class _ToastWidget extends StatefulWidget {
  final IconData? icon;
  final String? iconPath;
  final String msg;
  final double top;
  final double left;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;
  final GlobalKey? parentWidget;
  final Function(_ToastWidgetState)? onStateCreated;
  final VoidCallback? onDismissComplete;

  const _ToastWidget({
    this.icon,
    this.iconPath,
    required this.msg,
    required this.top,
    required this.left,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.parentWidget,
    this.onStateCreated,
    this.onDismissComplete,
  });

  @override
  State<_ToastWidget> createState() => _ToastWidgetState();
}

class _ToastWidgetState extends State<_ToastWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isDismissing = false;
  Timer? _positionUpdateTimer;
  Timer? _parentCheckTimer;

  // 缓存父Widget的位置信息
  double? _cachedParentLeft;
  double? _cachedParentWidth;

  // 记录初始状态用于路由检测
  String? _initialRoute;
  bool _initialCanPop = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 通知父级状态已创建
    widget.onStateCreated?.call(this);

    // 添加生命周期观察者
    WidgetsBinding.instance.addObserver(this);

    // 记录初始路由和Navigator状态（如果有父Widget的话）
    if (widget.parentWidget?.currentContext != null) {
      try {
        final parentContext = widget.parentWidget!.currentContext!;
        final modalRoute = ModalRoute.of(parentContext);
        final navigator = Navigator.of(parentContext);
        _initialRoute = modalRoute?.settings.name;
        _initialCanPop = navigator.canPop();
      } catch (e) {
        _initialRoute = null;
        _initialCanPop = false;
      }
    }

    // 开始进入动画
    _animationController.forward();

    // 如果有父Widget，启动位置更新定时器和生命周期检查
    if (widget.parentWidget != null) {
      _startPositionUpdateTimer();
      _startParentLifecycleCheck();
    }
  }

  /// 启动位置更新定时器
  void _startPositionUpdateTimer() {
    _positionUpdateTimer =
        Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (mounted && widget.parentWidget != null) {
        _updateParentPosition();
      }
    });
  }

  /// 更新父Widget位置信息
  void _updateParentPosition() {
    if (widget.parentWidget?.currentContext == null) {
      return;
    }

    final RenderBox? parentRenderBox =
        widget.parentWidget!.currentContext!.findRenderObject() as RenderBox?;
    if (parentRenderBox != null && parentRenderBox.hasSize) {
      final Offset parentPosition = parentRenderBox.localToGlobal(Offset.zero);
      final double newParentLeft = parentPosition.dx;
      final double newParentWidth = parentRenderBox.size.width;

      // 只有当位置发生变化时才触发重建
      if (_cachedParentLeft != newParentLeft ||
          _cachedParentWidth != newParentWidth) {
        setState(() {
          _cachedParentLeft = newParentLeft;
          _cachedParentWidth = newParentWidth;
        });
      }
    }
  }

  /// 启动父Widget生命周期检查
  void _startParentLifecycleCheck() {
    _parentCheckTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      // 检查父Widget是否还存在或者页面是否已经变化
      if (_shouldDismissToast()) {
        // 父Widget已被销毁或页面已变化，关闭Toast
        timer.cancel();
        if (!_isDismissing) {
          dismiss();
        }
      }
    });
  }

  /// 判断是否应该关闭Toast
  bool _shouldDismissToast() {
    // 1. 检查父Widget是否还存在
    if (widget.parentWidget?.currentContext == null) {
      return true;
    }

    final parentContext = widget.parentWidget!.currentContext!;

    // 2. 检查父Widget是否还mounted
    if (!parentContext.mounted) {
      return true;
    }

    // 3. 检查父Widget是否还在当前的Widget树中（通过查找RenderObject）
    try {
      final renderObject = parentContext.findRenderObject();
      if (renderObject == null || !renderObject.attached) {
        return true;
      }
    } catch (e) {
      // 如果查找RenderObject出错，说明Widget可能已经被销毁
      return true;
    }

    // 4. 关键检查：对于GoRouter的push导航，检查父Widget的页面是否被遮挡
    try {
      final modalRoute = ModalRoute.of(parentContext);

      // 检查当前路由是否还是最顶层的活跃路由
      if (modalRoute != null && !modalRoute.isCurrent) {
        return true;
      }

      // 检查是否有其他路由覆盖在当前路由之上
      // 通过检查Navigator的路由历史栈
      final navigator = Navigator.of(parentContext);
      final currentRoute = modalRoute;

      // 如果当前有Modal路由但不是最上层的，说明有新页面覆盖了
      if (currentRoute != null) {
        // 获取Navigator的状态，检查是否有其他路由在栈顶
        try {
          // 关键检测：如果初始时不能pop，但现在可以pop，说明有新路由被push了
          final currentCanPop = navigator.canPop();
          if (!_initialCanPop && currentCanPop) {
            return true;
          }

          // 额外检查：如果路由名称发生了变化
          if (_initialRoute != null) {
            final currentRouteName = currentRoute.settings.name;
            if (currentRouteName != _initialRoute) {
              return true;
            }
          }
        } catch (e) {
          // 安全起见，如果检查出错就关闭Toast
          return true;
        }
      }
    } catch (e) {
      // 如果获取路由信息出错，为了安全起见关闭Toast
      return true;
    }

    return false;
  }

  /// 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用进入后台或恢复时，检查Toast是否应该关闭
    if (widget.parentWidget != null && _shouldDismissToast()) {
      if (!_isDismissing) {
        dismiss();
      }
    }
  }

  /// 执行消失动画
  void dismiss() {
    if (_isDismissing) {
      return;
    }
    _isDismissing = true;

    // 停止位置更新定时器
    _positionUpdateTimer?.cancel();
    _positionUpdateTimer = null;

    // 取消父Widget生命周期检查
    _parentCheckTimer?.cancel();
    _parentCheckTimer = null;

    // 执行反向动画
    _animationController.reverse().then((_) {
      // 动画完成后通知父级
      widget.onDismissComplete?.call();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _positionUpdateTimer?.cancel();
    _parentCheckTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果指定了父Widget，在每次build时检查父Widget是否还有效
    if (widget.parentWidget != null && _shouldDismissToast()) {
      // 如果父Widget无效，立即关闭Toast
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_isDismissing) {
          dismiss();
        }
      });
      // 返回空容器，避免显示
      return const SizedBox.shrink();
    }

    // 获取父Widget的位置和尺寸
    double? parentLeft = _cachedParentLeft;
    double? parentWidth = _cachedParentWidth;

    // 如果还没有缓存的位置信息，立即获取一次
    if (parentLeft == null &&
        widget.parentWidget != null &&
        widget.parentWidget!.currentContext != null) {
      final RenderBox? parentRenderBox =
          widget.parentWidget!.currentContext!.findRenderObject() as RenderBox?;
      if (parentRenderBox != null && parentRenderBox.hasSize) {
        final Offset parentPosition =
            parentRenderBox.localToGlobal(Offset.zero);
        parentLeft = parentPosition.dx;
        parentWidth = parentRenderBox.size.width;

        // 更新缓存
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _cachedParentLeft = parentLeft;
              _cachedParentWidth = parentWidth;
            });
          }
        });
      }
    }

    return Positioned(
      top: widget.top,
      left: parentLeft != null ? parentLeft + widget.left : widget.left,
      right: parentWidth != null
          ? (MediaQuery.of(context).size.width -
              (parentLeft! + parentWidth) +
              widget.left)
          : widget.left,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Center(
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: PGDialog.toastPaddingHorizontal,
                  vertical: PGDialog.toastPaddingVertical,
                ),
                decoration: BoxDecoration(
                  color: widget.backgroundColor ??
                      PGDialog.desktopToastBackgroundColor,
                  borderRadius:
                      BorderRadius.circular(PGDialog.toastBorderRadius),
                  border: Border.all(color: PGDialog.desktopToastBorderColor),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.icon != null)
                      Icon(
                        widget.icon!,
                        color:
                            widget.iconColor ?? PGDialog.desktopToastTextColor,
                        size: PGDialog.desktopToastTextSize + 2,
                      ),
                    if (widget.iconPath != null)
                      Image.asset(
                        widget.iconPath!,
                        width: 24,
                        height: 24,
                        color:
                            widget.iconColor ?? PGDialog.desktopToastTextColor,
                      ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        widget.msg,
                        style: TextStyle(
                          color: widget.textColor ??
                              PGDialog.desktopToastTextColor,
                          fontSize: PGDialog.desktopToastTextSize,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.medium,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Loading Toast显示组件（带旋转图标）
class _LoadingToastWidget extends StatefulWidget {
  final String msg;
  final double top;
  final double left;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;
  final GlobalKey? parentWidget;
  final Function(_LoadingToastWidgetState)? onStateCreated;
  final VoidCallback? onDismissComplete;

  const _LoadingToastWidget({
    required this.msg,
    required this.top,
    required this.left,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.parentWidget,
    this.onStateCreated,
    this.onDismissComplete,
  });

  @override
  State<_LoadingToastWidget> createState() => _LoadingToastWidgetState();
}

class _LoadingToastWidgetState extends State<_LoadingToastWidget>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _animationController;
  late AnimationController _rotationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotationAnimation;
  bool _isDismissing = false;
  Timer? _positionUpdateTimer;
  Timer? _parentCheckTimer;

  // 缓存父Widget的位置信息
  double? _cachedParentLeft;
  double? _cachedParentWidth;

  // 记录初始状态用于路由检测
  String? _initialRoute;
  bool _initialCanPop = false;

  @override
  void initState() {
    super.initState();

    // 进入/退出动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 旋转动画控制器
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_rotationController);

    // 通知父级状态已创建
    widget.onStateCreated?.call(this);

    // 添加生命周期观察者
    WidgetsBinding.instance.addObserver(this);

    // 记录初始路由和Navigator状态（如果有父Widget的话）
    if (widget.parentWidget?.currentContext != null) {
      try {
        final parentContext = widget.parentWidget!.currentContext!;
        final modalRoute = ModalRoute.of(parentContext);
        final navigator = Navigator.of(parentContext);
        _initialRoute = modalRoute?.settings.name;
        _initialCanPop = navigator.canPop();
      } catch (e) {
        _initialRoute = null;
        _initialCanPop = false;
      }
    }

    // 开始进入动画
    _animationController.forward();

    // 开始旋转动画（无限循环）
    _rotationController.repeat();

    // 如果有父Widget，启动位置更新定时器和生命周期检查
    if (widget.parentWidget != null) {
      _startPositionUpdateTimer();
      _startParentLifecycleCheck();
    }
  }

  /// 启动位置更新定时器
  void _startPositionUpdateTimer() {
    _positionUpdateTimer =
        Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (mounted && widget.parentWidget != null) {
        _updateParentPosition();
      }
    });
  }

  /// 更新父Widget位置信息
  void _updateParentPosition() {
    if (widget.parentWidget?.currentContext == null) {
      return;
    }

    final RenderBox? parentRenderBox =
        widget.parentWidget!.currentContext!.findRenderObject() as RenderBox?;
    if (parentRenderBox != null && parentRenderBox.hasSize) {
      final Offset parentPosition = parentRenderBox.localToGlobal(Offset.zero);
      final double newParentLeft = parentPosition.dx;
      final double newParentWidth = parentRenderBox.size.width;

      // 只有当位置发生变化时才触发重建
      if (_cachedParentLeft != newParentLeft ||
          _cachedParentWidth != newParentWidth) {
        setState(() {
          _cachedParentLeft = newParentLeft;
          _cachedParentWidth = newParentWidth;
        });
      }
    }
  }

  /// 启动父Widget生命周期检查
  void _startParentLifecycleCheck() {
    _parentCheckTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      // 检查父Widget是否还存在或者页面是否已经变化
      if (_shouldDismissToast()) {
        // 父Widget已被销毁或页面已变化，关闭Toast
        timer.cancel();
        if (!_isDismissing) {
          dismiss();
        }
      }
    });
  }

  /// 判断是否应该关闭Toast
  bool _shouldDismissToast() {
    // 1. 检查父Widget是否还存在
    if (widget.parentWidget?.currentContext == null) {
      return true;
    }

    final parentContext = widget.parentWidget!.currentContext!;

    // 2. 检查父Widget是否还mounted
    if (!parentContext.mounted) {
      return true;
    }

    // 3. 检查父Widget是否还在当前的Widget树中（通过查找RenderObject）
    try {
      final renderObject = parentContext.findRenderObject();
      if (renderObject == null || !renderObject.attached) {
        return true;
      }
    } catch (e) {
      // 如果查找RenderObject出错，说明Widget可能已经被销毁
      return true;
    }

    // 4. 关键检查：对于GoRouter的push导航，检查父Widget的页面是否被遮挡
    try {
      final modalRoute = ModalRoute.of(parentContext);

      // 检查当前路由是否还是最顶层的活跃路由
      if (modalRoute != null && !modalRoute.isCurrent) {
        return true;
      }

      // 检查是否有其他路由覆盖在当前路由之上
      // 通过检查Navigator的路由历史栈
      final navigator = Navigator.of(parentContext);
      final currentRoute = modalRoute;

      // 如果当前有Modal路由但不是最上层的，说明有新页面覆盖了
      if (currentRoute != null) {
        // 获取Navigator的状态，检查是否有其他路由在栈顶
        try {
          // 关键检测：如果初始时不能pop，但现在可以pop，说明有新路由被push了
          final currentCanPop = navigator.canPop();
          if (!_initialCanPop && currentCanPop) {
            return true;
          }

          // 额外检查：如果路由名称发生了变化
          if (_initialRoute != null) {
            final currentRouteName = currentRoute.settings.name;
            if (currentRouteName != _initialRoute) {
              return true;
            }
          }
        } catch (e) {
          // 安全起见，如果检查出错就关闭Toast
          return true;
        }
      }
    } catch (e) {
      // 如果获取路由信息出错，为了安全起见关闭Toast
      return true;
    }

    return false;
  }

  /// 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用进入后台或恢复时，检查Toast是否应该关闭
    if (widget.parentWidget != null && _shouldDismissToast()) {
      if (!_isDismissing) {
        dismiss();
      }
    }
  }

  /// 执行消失动画
  void dismiss() {
    if (_isDismissing) {
      return;
    }
    _isDismissing = true;

    // 停止位置更新定时器
    _positionUpdateTimer?.cancel();
    _positionUpdateTimer = null;

    // 取消父Widget生命周期检查
    _parentCheckTimer?.cancel();
    _parentCheckTimer = null;

    // 停止旋转动画
    _rotationController.stop();

    // 执行反向动画
    _animationController.reverse().then((_) {
      // 动画完成后通知父级
      widget.onDismissComplete?.call();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _positionUpdateTimer?.cancel();
    _parentCheckTimer?.cancel();
    _animationController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果指定了父Widget，在每次build时检查父Widget是否还有效
    if (widget.parentWidget != null && _shouldDismissToast()) {
      // 如果父Widget无效，立即关闭Toast
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_isDismissing) {
          dismiss();
        }
      });
      // 返回空容器，避免显示
      return const SizedBox.shrink();
    }

    // 获取父Widget的位置和尺寸
    double? parentLeft = _cachedParentLeft;
    double? parentWidth = _cachedParentWidth;

    // 如果还没有缓存的位置信息，立即获取一次
    if (parentLeft == null &&
        widget.parentWidget != null &&
        widget.parentWidget!.currentContext != null) {
      final RenderBox? parentRenderBox =
          widget.parentWidget!.currentContext!.findRenderObject() as RenderBox?;
      if (parentRenderBox != null && parentRenderBox.hasSize) {
        final Offset parentPosition =
            parentRenderBox.localToGlobal(Offset.zero);
        parentLeft = parentPosition.dx;
        parentWidth = parentRenderBox.size.width;

        // 更新缓存
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _cachedParentLeft = parentLeft;
              _cachedParentWidth = parentWidth;
            });
          }
        });
      }
    }
    // 处理负数left值的情况，使其能够相对于屏幕右侧定位
    double finalLeft = MediaQuery.of(context).size.width / 2 + widget.left / 2;
    return Positioned(
      top: widget.top,
      left: finalLeft,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Center(
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: PGDialog.toastPaddingHorizontal,
                  vertical: PGDialog.toastPaddingVertical,
                ),
                decoration: BoxDecoration(
                  color: widget.backgroundColor ??
                      PGDialog.desktopToastBackgroundColor,
                  borderRadius:
                      BorderRadius.circular(PGDialog.toastBorderRadius),
                  border: Border.all(color: PGDialog.desktopToastBorderColor),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 旋转的loading图标
                    AnimatedBuilder(
                      animation: _rotationAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _rotationAnimation.value * 2 * 3.14159,
                          child: Image.asset(
                            'assets/icons/aigc_sample_running_icon.png',
                            width: 24,
                            height: 24,
                            color: widget.iconColor ??
                                PGDialog.desktopToastTextColor,
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        widget.msg,
                        style: TextStyle(
                          color: widget.textColor ??
                              PGDialog.desktopToastTextColor,
                          fontSize: PGDialog.desktopToastTextSize,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.medium,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
