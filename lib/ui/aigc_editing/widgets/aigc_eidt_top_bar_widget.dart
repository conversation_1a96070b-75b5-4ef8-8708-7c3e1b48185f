import 'package:flutter/material.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

class AigcEditTopBarWidget extends StatelessWidget {
  final int score;
  final VoidCallback? onBackPressed;
  final VoidCallback? onPurchasePressed;
  final VoidCallback? onPresetPressed;
  final VoidCallback? onExportPressed;
  final VoidCallback? onMakingCenterPressed;
  final bool showBackButton;
  final bool showPurchaseButton;
  final bool showPresetButton;
  final bool showMakingCenterButton;
  final bool showExportButton;
  final Color? backgroundColor;

  const AigcEditTopBarWidget({
    super.key,
    required this.score,
    this.onBackPressed,
    this.onPurchasePressed,
    this.onPresetPressed,
    this.onExportPressed,
    this.onMakingCenterPressed,
    this.showBackButton = true,
    this.showPurchaseButton = true,
    this.showPresetButton = true,
    this.showMakingCenterButton = true,
    this.showExportButton = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 左侧按钮区域 - 设置背景色
          Container(
            height: 44,
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.transparent,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(width: 10),
                // 返回按钮
                if (showBackButton)
                  PlatformMouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: onBackPressed,
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: Image.asset("assets/icons/edit_go_home.png"),
                      ),
                    ),
                  ),
                const SizedBox(width: 10),
              ],
            ),
          ),
          // 中间可拖拽区域 - 保持透明
          const Expanded(child: SizedBox.shrink()),
          // 右侧按钮区域 - 设置背景色
          Container(
            height: 44,
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.transparent,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 积分购买按钮
                if (showPurchaseButton)
                  ScorePurchaseButton(
                    score: score,
                    onPurchasePressed: onPurchasePressed,
                  ),
                if (showPurchaseButton) const SizedBox(width: 16),
                // 预设按钮
                if (showPresetButton)
                  // 主题预设管理
                  _buildActionButton(
                    icon: 'assets/icons/ai_project_preset_icon.png',
                    backgroundColor: const Color(0xFF1F1F1F),
                    borderColor: Colors.transparent,
                    text: '主题预设',
                    width: 96,
                    onTap: () => onPresetPressed?.call(),
                  ),
                if (showPresetButton) const SizedBox(width: 8),
                // 打样中心按钮
                if (showMakingCenterButton)
                  // 打样中心
                  _buildActionButton(
                    icon: 'assets/icons/ai_project_sample_icon.png',
                    backgroundColor: const Color(0xFF1F1F1F),
                    borderColor: Colors.transparent,
                    text: '打样中心',
                    width: 96,
                    onTap: () => onMakingCenterPressed?.call(),
                  ),
                if (showMakingCenterButton) const SizedBox(width: 8),
                // 导出列表按钮
                if (showExportButton)
                  _buildActionButton(
                    icon: 'assets/icons/aigc_project_export_icon.png',
                    backgroundColor: const Color(0xFF1F1F1F),
                    borderColor: Colors.transparent,
                    text: '导出进度',
                    width: 96,
                    onTap: () {
                      onExportPressed?.call();
                    },
                  ),
                if (showExportButton) const SizedBox(width: 0),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建功能按钮
  Widget _buildActionButton({
    required String icon,
    required Color backgroundColor,
    required Color borderColor,
    required VoidCallback onTap,
    String? text,
    double? width,
    Color? iconColor,
  }) {
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: DebounceClickWidget(
        onTap: onTap,
        child: Container(
          width: width,
          height: 32,
          padding: const EdgeInsets.only(
            top: 4,
            right: 7,
            bottom: 4,
            left: 7,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color: borderColor,
              width: 1,
            ),
            color: backgroundColor,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                icon,
                width: 24,
                height: 24,
                color: iconColor,
              ),
              if (text != null) ...[
                const SizedBox(width: 4),
                Text(
                  text,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                    height: 1.0,
                    color: Colors.white,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class ScorePurchaseButton extends StatefulWidget {
  final int score;
  final VoidCallback? onPurchasePressed;

  const ScorePurchaseButton({
    super.key,
    this.score = 0,
    this.onPurchasePressed,
  });

  @override
  State<ScorePurchaseButton> createState() => _ScorePurchaseButtonState();
}

class _ScorePurchaseButtonState extends State<ScorePurchaseButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Container(
        height: 28,
        decoration: BoxDecoration(
          color: _isHovered
              ? const Color(0xFF22EDFF).withOpacity(0.3)
              : const Color(0xFF22EDFF).withOpacity(0.2),
          borderRadius: BorderRadius.circular(5),
          border: Border.all(
            color: const Color(0xFFFFFFFF).withOpacity(0.1),
            width: 0.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(width: 4),
            SizedBox(
              width: 20,
              height: 20,
              child: Image.asset("assets/icons/icon_score.png"),
            ),
            const SizedBox(width: 2),
            Text(
              "${widget.score}",
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                fontFamily: Fonts.defaultFontFamily,
                color: const Color(0xFF22EDFF),
              ),
            ),
            const SizedBox(width: 10),
            Container(
              width: 1,
              height: 12,
              color: const Color(0xFFFFFFFF).withOpacity(0.15),
            ),
            const SizedBox(width: 10),
            GestureDetector(
              onTap: widget.onPurchasePressed,
              child: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: Text(
                  "购买",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    fontFamily: Fonts.defaultFontFamily,
                    color: const Color(0xFF22EDFF),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
