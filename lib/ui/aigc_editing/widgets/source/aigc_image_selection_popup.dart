import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 图片选择类型枚举
enum ImageSelectionType {
  files, // 选择文件
  folder, // 选择文件夹
}

/// 图片选择弹窗组件，显示"选择文件"和"选择文件夹"两个选项
class AigcImageSelectionPopup extends StatelessWidget {
  final void Function(ImageSelectionType type)? onSelection;
  final VoidCallback? onDismiss;

  const AigcImageSelectionPopup({
    super.key,
    this.onSelection,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: 110,
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0x1AFFFFFF),
            width: 0.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMenuItem(
              text: '导入图片',
              onTap: () {
                onDismiss?.call();
                onSelection?.call(ImageSelectionType.files);
              },
            ),
            const SizedBox(height: 4),
            _buildMenuItem(
              text: '导入文件夹',
              onTap: () {
                onDismiss?.call();
                onSelection?.call(ImageSelectionType.folder);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required String text,
    required VoidCallback onTap,
  }) {
    return _MenuItemWidget(
      text: text,
      onTap: onTap,
    );
  }

  /// 显示弹窗
  static void show({
    required BuildContext context,
    required GlobalKey buttonKey,
    void Function(ImageSelectionType type)? onSelection,
  }) {
    final RenderBox? renderBox =
        buttonKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      return;
    }

    final Offset buttonPosition = renderBox.localToGlobal(Offset.zero);

    OverlayEntry? overlayEntry;

    void dismiss() {
      if (overlayEntry != null) {
        overlayEntry!.remove();
        overlayEntry = null;
      }
    }

    overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 透明背景，点击关闭弹窗
          Positioned.fill(
            child: GestureDetector(
              onTap: dismiss,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          // 弹窗内容
          Positioned(
            left: buttonPosition.dx,
            bottom: MediaQuery.of(context).size.height - buttonPosition.dy + 4,
            child: AigcImageSelectionPopup(
              onSelection: onSelection,
              onDismiss: dismiss,
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(overlayEntry!);
  }
}

/// 菜单项组件，支持hover效果
class _MenuItemWidget extends StatefulWidget {
  final String text;
  final VoidCallback onTap;

  const _MenuItemWidget({
    required this.text,
    required this.onTap,
  });

  @override
  State<_MenuItemWidget> createState() => _MenuItemWidgetState();
}

class _MenuItemWidgetState extends State<_MenuItemWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: 102,
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: _isHovered
                ? const Color(0x0DFFFFFF) // 5% 透明度的白色
                : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            widget.text,
            style: TextStyle(
              fontSize: 12,
              color: _isHovered
                  ? Colors.white // hover时为白色
                  : const Color(0x80FFFFFF), // 50% 透明度的白色
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
        ),
      ),
    );
  }
}
