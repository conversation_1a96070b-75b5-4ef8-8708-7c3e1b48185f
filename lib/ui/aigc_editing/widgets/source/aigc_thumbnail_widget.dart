import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_mask_acquisition_provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 智能缩略图Widget - 自动管理加载和清理
class AigcThumbnailWidget extends StatefulWidget {
  final int index;
  final AigcPreviewImageItem imageData;
  final bool isSelected;
  final bool isInMultiSelection;
  final bool isMultiSelectMode;
  final MaskAcquisitionStatus? maskAcquisitionStatus;
  final Function({required bool isControlPressed}) onTap;
  final Function(int index) onThumbnailRequest;
  final Function(int index) onThumbnailRelease;

  const AigcThumbnailWidget({
    super.key,
    required this.index,
    required this.imageData,
    required this.isSelected,
    this.isInMultiSelection = false,
    this.isMultiSelectMode = false,
    this.maskAcquisitionStatus,
    required this.onTap,
    required this.onThumbnailRequest,
    required this.onThumbnailRelease,
  });

  @override
  State<AigcThumbnailWidget> createState() => _AigcThumbnailWidgetState();
}

/// 底部缩略图 widget
class _AigcThumbnailWidgetState extends State<AigcThumbnailWidget>
    with TickerProviderStateMixin {
  // AigcThumbnailViewModel? _viewModel;

  static const double _cornerRadius = 4.0;

  /// 显示的 item 大小
  static const double _itemSize = 64.0;

  // 加载动画控制器
  late AnimationController _loadingController;
  // 蒙版获取动画控制器
  late AnimationController _maskOverlayController;
  late Animation<double> _maskOverlayAnimation;

  @override
  void initState() {
    super.initState();

    _loadingController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();

    // 初始化蒙版获取动画控制器
    _maskOverlayController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600), // 0.6秒一个完整周期
    );

    // 创建透明度动画，从0.2到0再到0.2
    _maskOverlayAnimation = Tween<double>(
      begin: 0.2,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _maskOverlayController,
      curve: Curves.easeInOut,
    ));
    widget.onThumbnailRequest(widget.index);
  }

  @override
  void dispose() {
    _loadingController.dispose();
    _maskOverlayController.dispose();
    super.dispose();
  }

  @override
  void deactivate() {
    super.deactivate();
    widget.onThumbnailRelease(widget.index);
  }

  @override
  Widget build(BuildContext context) {
    return _buildScene();
  }

  _buildScene() {
    Color selectedStrokeColor;
    if (widget.isSelected) {
      selectedStrokeColor = const Color(0xFFF72561);
    } else if (widget.isInMultiSelection && widget.isMultiSelectMode) {
      selectedStrokeColor = const Color(0xFFFF9EB9);
    } else {
      selectedStrokeColor = Colors.transparent;
    }
    return GestureDetector(
      onTap: () {
        // 检测Control键是否按下
        widget.onTap(
          isControlPressed: HardwareKeyboard.instance.isControlPressed,
        );
      },
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        width: _itemSize,
        height: _itemSize,
        child: Stack(
          children: [
            // 缩略图
            ClipRRect(
              borderRadius: BorderRadius.circular(_cornerRadius),
              child: _buildThumbnailContent(),
            ),

            // 蒙版获取状态蒙层
            if (widget.maskAcquisitionStatus != null)
              _buildMaskAcquisitionOverlay(),

            // 构建索引指示器
            _buildIndexIndicator(),
            // 选中状态边框 - 放在最上层确保不被遮挡
            if (widget.isSelected ||
                (widget.isInMultiSelection && widget.isMultiSelectMode)) ...[
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(_cornerRadius),
                    border: Border.all(color: selectedStrokeColor, width: 1),
                  ),
                ),
              ),
              // 内层黑色边框
              Positioned(
                left: 1,
                top: 1,
                right: 1,
                bottom: 1,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(_cornerRadius),
                    border:
                        Border.all(color: const Color(0xFF262626), width: 1),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建缩略图内容
  Widget _buildThumbnailContent() {
    // 如果是已损坏的图片，显示错误占位符
    if (widget.imageData.isDamaged) {
      return _buildErrorPlaceholder();
    }
    // 如果有缩略图路径，显示真实缩略图
    else if (widget.imageData.hasThumbnail &&
        widget.imageData.thumbnailPath != null) {
      return _buildRealThumbnail(widget.imageData.thumbnailPath!);
    }
    // 如果没有缩略图，显示加载占位符
    else {
      return _buildLoadingPlaceholder();
    }
  }

  /// 构建真实缩略图
  Widget _buildRealThumbnail(String thumbnailPath) {
    return SizedBox(
      width: _itemSize,
      height: _itemSize,
      child: Image.file(
        File(thumbnailPath),
        fit: BoxFit.cover,
        filterQuality: FilterQuality.medium,
        errorBuilder: (context, error, stackTrace) {
          // 缩略图加载失败，显示错误占位符
          return _buildErrorPlaceholder();
        },
      ),
    );
  }

  /// 构建加载占位符
  Widget _buildLoadingPlaceholder() {
    return Container(
      width: _itemSize,
      height: _itemSize,
      alignment: Alignment.bottomRight,
      decoration: BoxDecoration(
          color: const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(_cornerRadius)),
      child: Container(
        width: 16,
        height: 16,
        margin: const EdgeInsets.only(right: 5, bottom: 5),
        child: RotationTransition(
          turns: _loadingController,
          child: Image.asset(
            'assets/icons/icon_loading_small.png',
            width: 24,
            height: 24,
          ),
        ),
      ),
    );
  }

  /// 构建错误占位符
  Widget _buildErrorPlaceholder() {
    return Container(
      width: _itemSize,
      height: _itemSize,
      decoration: BoxDecoration(
        color: const Color(0xFF0D0D0D),
        borderRadius: BorderRadius.circular(_cornerRadius),
      ),
      child: Center(
        child: Image.asset(
          'assets/icons/aigc_image_unavailable.png',
          width: 32,
          height: 32,
        ),
      ),
    );
  }

  Positioned _buildIndexIndicator() {
    final index = widget.index + 1;
    double paddingHorizontal;
    if (index >= 10) {
      paddingHorizontal = 2;
    } else {
      paddingHorizontal = 4;
    }
    return Positioned(
        left: 4,
        bottom: 4,
        child: Container(
          padding:
              EdgeInsets.symmetric(horizontal: paddingHorizontal, vertical: 1),
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: const Color(0x99000000),
              borderRadius: BorderRadius.circular(1)),
          child: Text(
            '$index',
            style: TextStyle(
                fontSize: 7,
                color: Colors.white,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: FontWeight.w600),
          ),
        ));
  }

  /// 构建蒙版获取状态蒙层
  Widget _buildMaskAcquisitionOverlay() {
    // 根据状态控制动画
    if (widget.maskAcquisitionStatus == MaskAcquisitionStatus.loading) {
      // 开始呼吸动画
      if (!_maskOverlayController.isAnimating) {
        _maskOverlayController.repeat(reverse: true);
      }
    } else {
      // 停止动画
      _maskOverlayController.stop();
    }
    // 蒙层在选中状态下始终保持2px的边框偏移，确保不遮挡选中边框
    double border = (widget.isSelected ||
            (widget.isInMultiSelection && widget.isMultiSelectMode))
        ? 2
        : 0;
    // 蒙层不遮挡边框，只覆盖图片内容区域
    return Positioned(
      left: border,
      top: border,
      right: border,
      bottom: border,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(3),
        child: widget.maskAcquisitionStatus == MaskAcquisitionStatus.loading
            ? AnimatedBuilder(
                animation: _maskOverlayAnimation,
                builder: (context, child) {
                  return Container(
                    color:
                        Colors.black.withOpacity(_maskOverlayAnimation.value),
                  );
                },
              )
            : Container(
                color: Colors.black.withOpacity(0.6),
                padding: const EdgeInsets.only(left: 10, right: 10),
                child:
                    widget.maskAcquisitionStatus == MaskAcquisitionStatus.failed
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  'assets/icons/aigc_editing_mask_error_icon.png',
                                  width: 16,
                                  height: 16,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '主体识别 失败',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.white,
                                    fontFamily: Fonts.defaultFontFamily,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          )
                        : null,
              ),
      ),
    );
  }
}
