import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 缩略图右键弹窗组件
class AigcThumbnailActionPopup extends StatelessWidget {
  final VoidCallback? onDelete;
  final VoidCallback? onDismiss;

  const AigcThumbnailActionPopup({
    super.key,
    this.onDelete,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: 80,
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0x1AFFFFFF),
            width: 0.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMenuItem(
              text: '删除',
              onTap: () {
                onDismiss?.call();
                onDelete?.call();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required String text,
    required VoidCallback onTap,
  }) {
    return _MenuItemWidget(
      text: text,
      onTap: onTap,
    );
  }

  /// 显示弹窗
  static void show({
    required BuildContext context,
    required Offset position,
    VoidCallback? onDelete,
  }) {
    OverlayEntry? overlayEntry;

    void dismiss() {
      if (overlayEntry != null) {
        overlayEntry!.remove();
        overlayEntry = null;
      }
    }

    overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 透明背景，点击关闭弹窗
          Positioned.fill(
            child: GestureDetector(
              onTap: dismiss,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          // 弹窗内容
          Positioned(
            left: position.dx,
            top: position.dy,
            child: AigcThumbnailActionPopup(
              onDelete: onDelete,
              onDismiss: dismiss,
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(overlayEntry!);
  }
}

/// 菜单项组件，支持hover效果
class _MenuItemWidget extends StatefulWidget {
  final String text;
  final VoidCallback onTap;

  const _MenuItemWidget({
    required this.text,
    required this.onTap,
  });

  @override
  State<_MenuItemWidget> createState() => _MenuItemWidgetState();
}

class _MenuItemWidgetState extends State<_MenuItemWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: 72,
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: _isHovered
                ? const Color(0x0DFFFFFF) // 5% 透明度的白色
                : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            widget.text,
            style: TextStyle(
              fontSize: 12,
              color: _isHovered
                  ? Colors.white // hover时为白色
                  : const Color(0x80FFFFFF), // 50% 透明度的白色
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
        ),
      ),
    );
  }
}
