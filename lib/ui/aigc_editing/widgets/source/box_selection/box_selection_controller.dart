import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/box_selection/box_selection_gesture_handler.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/box_selection/box_selection_state.dart';

/// 框选控制器
class BoxSelectionController {
  final VoidCallback? _onEnterMultiSelectMode;
  final Function(Set<int>, int)? _onSelectionChanged;
  final VoidCallback? _onSelectionEnded;

  final BoxSelectionState _state;

  /// 手势处理器
  late final BoxSelectionGestureHandler _gestureHandler;

  /// 构造函数
  BoxSelectionController({
    VoidCallback? onEnterMultiSelectMode,
    Function(Set<int>, int)? onSelectionChanged,
    VoidCallback? onSelectionEnded,
  })  : _onEnterMultiSelectMode = onEnterMultiSelectMode,
        _onSelectionChanged = onSelectionChanged,
        _onSelectionEnded = onSelectionEnded,
        _state = BoxSelectionState() {
    _gestureHandler = BoxSelectionGestureHandler(
      state: _state,
      onEnterMultiSelectMode: _onEnterMultiSelectMode,
      onSelectionChanged: _onSelectionChanged,
      onSelectionEnded: _onSelectionEnded,
    );
  }

  /// 获取框选状态
  BoxSelectionState get state => _state;

  /// 处理拖拽开始事件
  void handlePanStart(DragStartDetails details) {
    _gestureHandler.handlePanStart(details);
  }

  /// 处理拖拽更新事件
  void handlePanUpdate(DragUpdateDetails details) {
    _gestureHandler.handlePanUpdate(details);
  }

  /// 处理拖拽结束事件
  void handlePanEnd(DragEndDetails details) {
    _gestureHandler.handlePanEnd(details);
  }

  /// 更新框选状态
  void updateSelection({
    required RenderBox? listRenderBox,
    required int itemCount,
    required double itemWidth,
    required double itemHeight,
    required double itemSpacing,
    required double listPadding,
  }) {
    _gestureHandler.updateSelection(
      listRenderBox: listRenderBox,
      itemCount: itemCount,
      itemWidth: itemWidth,
      itemHeight: itemHeight,
      itemSpacing: itemSpacing,
      listPadding: listPadding,
    );
  }

  /// 更新列表滚动偏移
  void updateScrollOffset(double scrollOffset) {
    _gestureHandler.updateScrollOffset(scrollOffset);
  }

  /// 取消框选
  void cancelSelection() {
    _gestureHandler.cancelSelection();
  }

  /// 重置状态
  void reset() {
    _gestureHandler.reset();
  }

  /// 释放资源
  void dispose() {
    _state.dispose();
  }
}
