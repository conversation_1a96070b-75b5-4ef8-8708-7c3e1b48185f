import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/box_selection/box_selection_state.dart';

/// 框选手势处理器 负责处理框选相关的手势事件和逻辑
class BoxSelectionGestureHandler {
  final BoxSelectionState _state;
  final VoidCallback? _onEnterMultiSelectMode;
  final Function(Set<int>, int)? _onSelectionChanged;
  final VoidCallback? _onSelectionEnded;

  /// 是否已经进入了多选模式
  bool _hasEnteredMultiSelectMode = false;

  /// 当前是否切换了选中的图片，在一次框选周期内，只能修改一次
  bool _hasSwitchSelectedImage = false;

  /// 记录列表的滚动偏移
  double _listScrollOffset = 0.0;

  BoxSelectionGestureHandler({
    required BoxSelectionState state,
    VoidCallback? onEnterMultiSelectMode,
    Function(Set<int>, int)? onSelectionChanged,
    VoidCallback? onSelectionEnded,
  })  : _state = state,
        _onEnterMultiSelectMode = onEnterMultiSelectMode,
        _onSelectionChanged = onSelectionChanged,
        _onSelectionEnded = onSelectionEnded;

  /// 处理拖拽开始事件
  void handlePanStart(DragStartDetails details) {
    // 只有在没有按下Ctrl键时才开始框选
    if (!HardwareKeyboard.instance.isControlPressed) {
      _state.startSelection(details.globalPosition);
      _hasEnteredMultiSelectMode = false; // 重置多选模式标记
    }
  }

  /// 处理拖拽更新事件
  void handlePanUpdate(DragUpdateDetails details) {
    if (_state.isSelecting) {
      _state.updateSelection(details.globalPosition);
    }
  }

  /// 处理拖拽结束事件
  void handlePanEnd(DragEndDetails details) {
    if (_state.isSelecting) {
      _state.endSelection();

      // 触发框选结束回调
      _onSelectionEnded?.call();

      _hasSwitchSelectedImage = false;
      _hasEnteredMultiSelectMode = false; // 重置多选模式标记
    }
  }

  /// 计算框选范围内的项目索引
  Set<int> calculateSelectedIndices({
    required RenderBox? listRenderBox,
    required int itemCount,
    required double itemWidth,
    required double itemHeight,
    required double itemSpacing,
    required double listPadding,
  }) {
    final Set<int> selectedIndices = {};

    if (listRenderBox == null || !_state.hasValidSelection) {
      return selectedIndices;
    }

    final tempSelectionRect = _state.getSelectionRect(listRenderBox);
    if (tempSelectionRect == null) {
      return selectedIndices;
    }

    // 因为框选的坐标是相对于屏幕可见区域的，因此需要考虑列表的偏移量
    final selectionRect = tempSelectionRect.shift(Offset(_listScrollOffset, 0));

    // 遍历所有项目，检查是否在框选范围内
    for (int i = 0; i < itemCount; i++) {
      // 计算每个缩略图项目的实际位置
      // ListView布局：padding(8) + item0(64) + margin(2) + item1(64) + margin(2) + ...
      // 布局规则：
      // - 第一个item：left = listPadding (8px)
      // - 后续item：left = listPadding + i * itemWidth + i * 2 (每个item后面有2px margin)

      double itemLeft = listPadding + i * itemWidth;
      if (i > 0) {
        itemLeft += i * 2; // 除第一个item外，每个item都有2px左边距
      }

      final itemRight = itemLeft + itemWidth;
      const itemTop = 0.0;
      final itemBottom = itemHeight;

      final itemRect = Rect.fromLTRB(itemLeft, itemTop, itemRight, itemBottom);

      // 检查框选矩形是否与项目矩形相交
      if (selectionRect.overlaps(itemRect)) {
        selectedIndices.add(i);
      }
    }

    return selectedIndices;
  }

  void updateScrollOffset(double scrollOffset) {
    _listScrollOffset = scrollOffset;
  }

  /// 更新选中状态并触发回调
  void updateSelection({
    required RenderBox? listRenderBox,
    required int itemCount,
    required double itemWidth,
    required double itemHeight,
    required double itemSpacing,
    required double listPadding,
  }) {
    if (!_state.isSelecting) {
      return;
    }

    final selectedIndices = calculateSelectedIndices(
      listRenderBox: listRenderBox,
      itemCount: itemCount,
      itemWidth: itemWidth,
      itemHeight: itemHeight,
      itemSpacing: itemSpacing,
      listPadding: listPadding,
    );

    // 只有当真正有项目被选中时，才进入多选模式
    if (selectedIndices.isNotEmpty && !_hasEnteredMultiSelectMode) {
      _hasEnteredMultiSelectMode = true;
      _onEnterMultiSelectMode?.call();
    }

    _state.updateSelectedIndices(selectedIndices);
    if (selectedIndices.isNotEmpty && _hasSwitchSelectedImage == false) {
      _hasSwitchSelectedImage = true;
      _onSelectionChanged?.call(selectedIndices, selectedIndices.first);
    } else {
      _onSelectionChanged?.call(selectedIndices, -1);
    }
  }

  /// 取消框选
  void cancelSelection() {
    _state.cancelSelection();
    _hasEnteredMultiSelectMode = false;
  }

  /// 重置状态
  void reset() {
    _state.reset();
    _hasEnteredMultiSelectMode = false;
  }
}
