import 'package:flutter/material.dart';

/// 框选状态管理类
/// 负责管理框选过程中的所有状态数据
class BoxSelectionState extends ChangeNotifier {
  /// 是否正在框选
  bool _isSelecting = false;

  /// 框选起始点（全局坐标）
  Offset? _startGlobal;

  /// 框选结束点（全局坐标）
  Offset? _endGlobal;

  /// 当前选中的索引集合
  final Set<int> _selectedIndices = <int>{};

  /// 获取是否正在框选
  bool get isSelecting => _isSelecting;

  /// 获取选中的索引集合
  Set<int> get selectedIndices => Set.unmodifiable(_selectedIndices);

  /// 是否有有效的框选区域
  bool get hasValidSelection => _startGlobal != null && _endGlobal != null;

  /// 获取框选矩形（局部坐标）
  Rect? getSelectionRect(RenderBox? renderBox) {
    if (!hasValidSelection || renderBox == null) {
      return null;
    }

    final startLocal = renderBox.globalToLocal(_startGlobal!);
    final endLocal = renderBox.globalToLocal(_endGlobal!);

    return Rect.fromPoints(startLocal, endLocal);
  }

  /// 开始框选
  void startSelection(Offset globalPosition) {
    _isSelecting = true;
    _startGlobal = globalPosition;
    _endGlobal = globalPosition;
    _selectedIndices.clear();
    notifyListeners();
  }

  /// 更新框选结束点
  void updateSelection(Offset globalPosition) {
    if (!_isSelecting) {
      return;
    }

    _endGlobal = globalPosition;
    notifyListeners();
  }

  /// 结束框选
  void endSelection() {
    _isSelecting = false;
    _startGlobal = null;
    _endGlobal = null;
    notifyListeners();
  }

  /// 取消框选
  void cancelSelection() {
    _isSelecting = false;
    _startGlobal = null;
    _endGlobal = null;
    _selectedIndices.clear();
    notifyListeners();
  }

  /// 更新选中的索引集合
  void updateSelectedIndices(Set<int> indices) {
    _selectedIndices.clear();
    _selectedIndices.addAll(indices);
    notifyListeners();
  }

  /// 重置所有状态
  void reset() {
    _isSelecting = false;
    _startGlobal = null;
    _endGlobal = null;
    _selectedIndices.clear();
    notifyListeners();
  }
}
