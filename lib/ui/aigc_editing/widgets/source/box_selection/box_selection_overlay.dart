import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/box_selection/box_selection_state.dart';

/// 框选覆盖层组件
/// 负责绘制框选矩形的视觉效果
class BoxSelectionOverlay extends StatelessWidget {
  final BoxSelectionState state;
  final RenderBox? listRenderBox;
  final Color borderColor;
  final Color solidColor;
  final double borderWidth;

  const BoxSelectionOverlay({
    super.key,
    required this.state,
    required this.listRenderBox,
    this.borderColor = const Color(0x33000000),
    this.solidColor = const Color(0x33FFFFFF),
    this.borderWidth = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: state,
      builder: (context, child) {
        if (!state.isSelecting ||
            !state.hasValidSelection ||
            listRenderBox == null) {
          return const SizedBox.shrink();
        }

        final selectionRect = state.getSelectionRect(listRenderBox);
        if (selectionRect == null) {
          return const SizedBox.shrink();
        }

        return Positioned(
          left: selectionRect.left,
          top: selectionRect.top,
          width: selectionRect.width,
          height: selectionRect.height,
          child: _BoxSelectionRectangle(
            borderColor: borderColor,
            solidColor: solidColor,
            borderWidth: borderWidth,
          ),
        );
      },
    );
  }
}

/// 框选矩形绘制组件
class _BoxSelectionRectangle extends StatelessWidget {
  final Color borderColor;
  final Color solidColor;
  final double borderWidth;

  const _BoxSelectionRectangle({
    required this.borderColor,
    required this.solidColor,
    required this.borderWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: borderColor,
          width: borderWidth,
        ),
        color: solidColor,
      ),
    );
  }
}

/// 框选容器组件
/// 包装列表组件并提供框选功能
class BoxSelectionContainer extends StatefulWidget {
  final Widget child;
  final BoxSelectionState state;

  const BoxSelectionContainer({
    super.key,
    required this.child,
    required this.state,
  });

  @override
  State<BoxSelectionContainer> createState() => _BoxSelectionContainerState();
}

class _BoxSelectionContainerState extends State<BoxSelectionContainer> {
  final GlobalKey _listKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        KeyedSubtree(
          key: _listKey,
          child: widget.child,
        ),
        BoxSelectionOverlay(
          state: widget.state,
          listRenderBox:
              _listKey.currentContext?.findRenderObject() as RenderBox?,
        ),
      ],
    );
  }
}
