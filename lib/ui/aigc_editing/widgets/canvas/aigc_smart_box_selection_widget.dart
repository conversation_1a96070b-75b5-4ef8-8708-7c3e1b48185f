import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';
import 'package:turing_art/ui/aigc_editing/model/handler/aigc_regional_logic_frame.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_canvas_painter_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_regional_frame_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 用于 AIGC 智能框选时显示框选的边框
class AigcSmartBoxSelectionWidget extends StatefulWidget {
  const AigcSmartBoxSelectionWidget({super.key});

  @override
  State<AigcSmartBoxSelectionWidget> createState() =>
      _AigcSmartBoxSelectionWidgetState();
}

/// 用于内部 Selector 的数据类，包含需要监听的状态
class _RegionalFrameState {
  final AigcRegionalLogicFrame? currentLogicFrame;
  final double scale;

  const _RegionalFrameState({
    required this.currentLogicFrame,
    required this.scale,
  });

  /// 用于转换当前逻辑框到缩放后的矩形区域
  Rect? _convertToScaledRect() {
    if (currentLogicFrame == null || !currentLogicFrame!.isValid) {
      return null;
    }
    // 计算前后缩放值变化的系数，并计算更新之后的点坐标
    final coefficient = scale / currentLogicFrame!.regionalFrame.scale;
    final prevStartPoint = currentLogicFrame!.regionalFrame.startPosition;
    final prevEndPoint = currentLogicFrame!.regionalFrame.endPosition;

    final startPoint = Offset(
      prevStartPoint.dx * coefficient,
      prevStartPoint.dy * coefficient,
    );
    final endPoint = Offset(
      prevEndPoint.dx * coefficient,
      prevEndPoint.dy * coefficient,
    );
    return Rect.fromPoints(startPoint, endPoint);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is _RegionalFrameState &&
        other.currentLogicFrame == currentLogicFrame &&
        other.scale == scale;
  }

  @override
  int get hashCode => Object.hash(currentLogicFrame, scale);
}

class _AigcSmartBoxSelectionWidgetState
    extends State<AigcSmartBoxSelectionWidget> {
  /// 临时框选起始点（拖拽过程中使用）
  Offset? _tempStartPoint;

  /// 临时框选结束点（拖拽过程中使用）
  Offset? _tempEndPoint;

  /// 是否正在框选
  bool _isSelecting = false;

  @override
  Widget build(BuildContext context) {
    return Selector<AigcEditingControlProvider, bool>(
      selector: (context, controlProvider) =>
          controlProvider.isInSmartBoxSelectionMode,
      builder: (context, isInSmartBoxSelectionMode, child) {
        // 1. 首先判断是否处于智能框选状态
        if (!isInSmartBoxSelectionMode) {
          // 如果此时不处于智能框选模式，判断是否存在已经存在的框选区域
          return Selector2<AigcRegionalFrameProvider, AigcCanvasPainterProvider,
              _RegionalFrameState>(
            selector: (context, regionalFrameProvider, canvasPainterProvider) =>
                _RegionalFrameState(
              currentLogicFrame: regionalFrameProvider.currentLogicFrame,
              scale: canvasPainterProvider.scale,
            ),
            builder: (context, regionalFrameState, child) {
              if (regionalFrameState.currentLogicFrame != null) {
                return CustomPaint(
                  painter: _SmartBoxSelectionPainter(
                      isSelecting: false,
                      regionalFrameState: regionalFrameState),
                  size: Size.infinite,
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          );
        }

        // 2.当框选开关开启时，监听鼠标状态，绘制框选区域
        return Listener(
          onPointerDown: _handlePointerDown,
          onPointerMove: _handlePointerMove,
          onPointerUp: _handlePointerUp,
          child: CustomPaint(
            painter: _SmartBoxSelectionPainter(
              // 临时框选状态（拖拽过程中）
              tempStartPoint: _tempStartPoint,
              tempEndPoint: _tempEndPoint,
              isSelecting: _isSelecting,
            ),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  /// 处理指针按下事件
  void _handlePointerDown(PointerDownEvent event) {
    // 只处理左键点击
    if (event.buttons == kPrimaryMouseButton) {
      if (mounted) {
        setState(() {
          _tempStartPoint = event.localPosition;
          _tempEndPoint = event.localPosition;
          _isSelecting = true;
        });
      }
    }
  }

  /// 处理指针移动事件
  void _handlePointerMove(PointerMoveEvent event) {
    if (_isSelecting && _tempStartPoint != null) {
      if (mounted) {
        setState(() {
          _tempEndPoint = event.localPosition;
        });
      }
    }
  }

  /// 处理指针抬起事件
  void _handlePointerUp(PointerUpEvent event) {
    if (_isSelecting &&
        _tempStartPoint != null &&
        _tempEndPoint != null &&
        mounted) {
      // 框选完成，保存到 Provider
      _onSelectionCompleted(_tempStartPoint!, _tempEndPoint!);

      // 清除临时状态
      setState(() {
        _isSelecting = false;
        _tempStartPoint = null;
        _tempEndPoint = null;
      });
    }
  }

  /// 将点位限制在画布范围内
  Offset _clampPointToCanvas(Offset point, Size canvasSize) {
    return Offset(
      point.dx.clamp(0.0, canvasSize.width).floorToDouble(),
      point.dy.clamp(0.0, canvasSize.height).floorToDouble(),
    );
  }

  /// 框选完成回调
  void _onSelectionCompleted(Offset startPoint, Offset endPoint) {
    final canvasProvider = context.read<AigcCanvasPainterProvider>();
    final scale = canvasProvider.scale;
    final canvasSize = canvasProvider.canvasSize;

    // 如果没有画布大小信息，直接使用原始点位
    if (canvasSize == null) {
      PGLog.w('画布大小信息不可用，使用原始点位');
      _processSelection(startPoint, endPoint, scale);
      return;
    }

    // 将点位限制在画布范围内
    final regionalStartPoint = _clampPointToCanvas(startPoint, canvasSize);
    final regionalEndPoint = _clampPointToCanvas(endPoint, canvasSize);

    _processSelection(regionalStartPoint, regionalEndPoint, scale);
  }

  /// 处理选择区域
  void _processSelection(Offset startPoint, Offset endPoint, double scale) {
    // 计算矩形区域
    final rect = Rect.fromPoints(startPoint, endPoint);

    // 只有当矩形有一定大小时才保存（避免误触）
    const minSize = 5.0;
    if (rect.width > minSize && rect.height > minSize) {
      // 保存到 Provider
      context.read<AigcRegionalFrameProvider>().addLogicFrame(
            startPoint,
            endPoint,
            scale,
          );

      context
          .read<AigcEditingControlProvider>()
          .setSmartBoxModeEnabled(value: false);

      _mattingMaskRegionalFrame();
      PGLog.d('智能框选完成并已保存: ${rect.toString()}');
    } else {
      PGLog.d('框选区域太小，已忽略');
    }
  }

  /// 更新蒙版区域框
  void _mattingMaskRegionalFrame() {
    final regionalFrameProvider = context.read<AigcRegionalFrameProvider>();
    final currentLogicFrame = regionalFrameProvider.currentLogicFrame;
    final canvasProvider = context.read<AigcCanvasPainterProvider>();
    final imageProvider = context.read<AigcEditingImageProvider>();
    final position =
        currentLogicFrame?.getRelativePosition(canvasProvider.scale);
    final rect = currentLogicFrame?.getRelativeRect(canvasProvider.scale);
    AigcRegionalFrameDataInfo? regionalFrameDataInfo;
    if (position != null && rect != null) {
      // 将位置和矩形转换为缩放后的位置和矩形, 执行抠图需要还原到原始大小
      regionalFrameDataInfo = AigcRegionalFrameDataInfo(
        position: position,
        rect: rect,
      );
    }
    imageProvider.updateMattingMaskBrushStroke(regionalFrameDataInfo, null,
        isBrushMode: true);
  }
}

/// 智能框选绘制器
class _SmartBoxSelectionPainter extends CustomPainter {
  /// 临时框选起始点（拖拽过程中）
  final Offset? tempStartPoint;

  /// 临时框选结束点（拖拽过程中）
  final Offset? tempEndPoint;

  /// 是否正在框选
  final bool isSelecting;

  /// 持久化的选择框（来自 Provider）
  final _RegionalFrameState? regionalFrameState;

  final boxColor = const Color(0xFF258CF2);

  _SmartBoxSelectionPainter({
    this.tempStartPoint,
    this.tempEndPoint,
    this.isSelecting = false,
    this.regionalFrameState,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 创建红色边框画笔
    final paint = Paint()
      ..color = boxColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 创建半透明填充画笔
    final fillPaint = Paint()
      ..color = boxColor.withOpacity(0.6)
      ..style = PaintingStyle.fill;

    final transformedRect = regionalFrameState?._convertToScaledRect();
    // 1. 绘制持久化的选择框（来自 Provider）
    if (transformedRect != null) {
      canvas.drawRect(transformedRect, paint);
    }

    // 2. 绘制临时框选状态（拖拽过程中，优先级更高）
    if (isSelecting && tempStartPoint != null && tempEndPoint != null) {
      final rect = Rect.fromPoints(tempStartPoint!, tempEndPoint!);

      // 绘制填充
      canvas.drawRect(rect, fillPaint);

      // 绘制边框
      canvas.drawRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _SmartBoxSelectionPainter oldDelegate) {
    return oldDelegate.tempStartPoint != tempStartPoint ||
        oldDelegate.tempEndPoint != tempEndPoint ||
        oldDelegate.isSelecting != isSelecting ||
        oldDelegate.regionalFrameState != regionalFrameState;
  }
}
