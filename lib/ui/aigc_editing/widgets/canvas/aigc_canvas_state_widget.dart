import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 显示模式枚举
enum AigcCanvasStateDisplayMode {
  /// 文本模式 - 显示"等待图片加载..."
  text,

  /// 图片模式 - 显示"图片不可用"图标
  image,

  /// 缺省模式 - 显示"图片以及文字"
  imageText,
}

/// AIGC画布状态组件
///
/// 用于显示画布的不同状态，如加载中或图片不可用
/// 有两种显示模式：文本模式和图片模式
class AigcCanvasStateWidget extends StatelessWidget {
  /// 显示模式
  final AigcCanvasStateDisplayMode displayMode;

  /// 自定义文本（可选）
  final String? customText;

  /// 构造函数
  const AigcCanvasStateWidget({
    super.key,
    this.displayMode = AigcCanvasStateDisplayMode.text,
    this.customText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFF0D0D0D),
      width: double.infinity,
      height: double.infinity,
      child: Center(
        child: _buildContent(),
      ),
    );
  }

  /// 构建内容
  Widget _buildContent() {
    switch (displayMode) {
      case AigcCanvasStateDisplayMode.text:
        return Text(
          customText ?? '等待图片加载...',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
        );
      case AigcCanvasStateDisplayMode.image:
        return Image.asset(
          'assets/icons/aigc_image_unavailable.png',
          width: 80,
          height: 80,
          fit: BoxFit.contain,
        );
      case AigcCanvasStateDisplayMode.imageText:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/icons/lauch_logo.png',
                width: 180,
                height: 180,
                fit: BoxFit.contain,
              ),
              const SizedBox(height: 2),
              Text(
                customText ?? '图灵精修-添加图片以开始',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium,
                ),
              ),
            ],
          ),
        );
    }
  }
}
