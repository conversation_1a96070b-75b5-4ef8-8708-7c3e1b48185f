import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_theme_list_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/model/aigc_batch_sample_model.dart';

class AigcBatchSampleViewModel extends ChangeNotifier {
  final AigcEditingImageProvider imageProvider;
  final AigcEditingThemeListProvider themeListProvider;
  final AigcEditingMultiSelectProvider multiSelectProvider;
  final CurrentEditingProjectRepository currentEditingProjectRepository;
  final AigcPresetsRepository? aigcPresetsRepository;
  final int singleCost;

  bool disposed = false;

  AigcBatchSampleViewModel({
    required this.imageProvider,
    required this.themeListProvider,
    required this.multiSelectProvider,
    required this.currentEditingProjectRepository,
    this.aigcPresetsRepository,
    required this.singleCost,
  }) {
    loadData();
    themeListProvider.addListener(_onThemeListSharedDataChanged);
  }

  @override
  void dispose() {
    disposed = true;
    themeListProvider.removeListener(_onThemeListSharedDataChanged);
    _currentThemeListViewModel?.dispose();
    super.dispose();
  }

  // 样本列表
  final List<AigcBatchSampleModel> _sampleList = [];
  List<AigcBatchSampleModel> get sampleList => _sampleList;

  // 当前选中的样本
  AigcBatchSampleModel? _currentSelectedSample;
  AigcBatchSampleModel? get currentSelectedSample => _currentSelectedSample;

  // 预设列表显示状态
  bool _showPresetList = false;
  bool get showPresetList => _showPresetList;

  // 当前选中的项目索引
  int _selectedItemIndex = -1;
  int get selectedItemIndex => _selectedItemIndex;

  // 预设列表位置
  double _presetListLeft = 0;
  double get presetListLeft => _presetListLeft;

  double _presetListTop = 8;
  double get presetListTop => _presetListTop;
  // 没有蒙版的图片数量
  int _noMaskImageCount = 0;
  int get noMaskImageCount => _noMaskImageCount;

  // 所有打样的总积分, 每个样本的积分是 singleCost * 3
  int get allCostValue => singleCost * _sampleList.length * 3;

  // 当前的主题列表ViewModel
  AigcThemeListViewModel? _currentThemeListViewModel;
  AigcThemeListViewModel? get currentThemeListViewModel =>
      _currentThemeListViewModel;

  // 加载状态
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  /// 设置当前选中的样本
  void setCurrentSelectedSample(AigcBatchSampleModel? value) {
    if (disposed) {
      return;
    }
    _currentSelectedSample = value;
    notifyListeners();
  }

  /// 设置加载状态
  void setLoading({required bool loading}) {
    if (disposed) {
      return;
    }
    _isLoading = loading;
    notifyListeners();
  }

  /// 创建独立的主题列表ViewModel
  AigcThemeListViewModel? createThemeListViewModel(int index) {
    if (aigcPresetsRepository == null) {
      return null;
    }

    final themeListViewModel = AigcThemeListViewModel(
      aigcPresetsRepository: aigcPresetsRepository!,
      themeListProvider: themeListProvider,
      imageProvider: imageProvider,
      multiSelectProvider: multiSelectProvider,
      onCreateClick: () {},
      index: index,
    );

    // 获取当前打样的预设和创意，初始化选中状态
    if (index >= 0 && index < _sampleList.length) {
      final currentSample = _sampleList[index];
      themeListViewModel.initializeSelection(
          currentSample.preset, currentSample.effect);
    }

    return themeListViewModel;
  }

  /// 处理项目重新选择
  bool onItemReselect(int index) {
    if (disposed) {
      return false;
    }

    // 如果点击的是同一个item，则隐藏预设列表
    if (_showPresetList && _selectedItemIndex == index) {
      hidePresetList();
      return false; // 返回false表示隐藏了预设列表
    }

    // 设置当前选中的样本
    if (index >= 0 && index < _sampleList.length) {
      setCurrentSelectedSample(_sampleList[index]);
    }

    // 释放之前的ViewModel并创建新的
    _currentThemeListViewModel?.dispose();
    _currentThemeListViewModel = createThemeListViewModel(index);

    _selectedItemIndex = index;
    // 注意：这里不设置_showPresetList = true，而是在calculatePresetListPosition中设置
    notifyListeners();
    return true; // 返回true表示需要显示预设列表
  }

  /// 显示预设列表
  void showPresetListAt(double left, double top) {
    if (disposed) {
      return;
    }
    _showPresetList = true;
    _presetListLeft = left;
    _presetListTop = top;
    notifyListeners();
  }

  /// 隐藏预设列表
  void hidePresetList() {
    if (disposed) {
      return;
    }
    _showPresetList = false;
    _selectedItemIndex = -1;
    _currentThemeListViewModel?.dispose();
    _currentThemeListViewModel = null;
    notifyListeners();
  }

  /// 计算预设列表位置
  void calculatePresetListPosition({
    required double itemRelativeX,
    required double itemRelativeY,
    required double itemWidth,
    required double itemHeight,
    double dialogWidth = 1280,
    double dialogHeight = 800,
    double presetListWidth = 285,
    double presetListHeight = 398,
    double margin = 8,
  }) {
    // 计算预设列表的X位置
    double left = itemRelativeX + itemWidth + margin; // item右侧 + margin间距

    // 检查右侧是否放得下，如果放不下则放到左侧
    if (left + presetListWidth > dialogWidth) {
      left = itemRelativeX -
          presetListWidth -
          margin; // item左侧 - 预设列表宽度 - margin间距
      // 确保不会超出对话框左边界
      if (left < 0) {
        left = margin; // 最小距离对话框左侧margin
      }
    }

    // 计算预设列表的Y位置 - 与item垂直居中对齐
    // item中心Y坐标
    final double itemCenterY = itemRelativeY + itemHeight / 2;

    // 预设列表默认Y位置（与item垂直居中）
    double top = itemCenterY - presetListHeight / 2;

    // 检查是否超出对话框顶部
    if (top < margin) {
      top = margin;
    }

    // 检查是否超出对话框底部
    if (top + presetListHeight > dialogHeight - margin) {
      top = dialogHeight - presetListHeight - margin;
    }

    showPresetListAt(left, top);
  }

  /// 加载数据
  void loadData() {
    final workspace = currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return;
    }

    _sampleList.clear();
    for (var image in multiSelectProvider.selectedImages) {
      if (image.hasMask) {
        _sampleList.add(AigcBatchSampleModel(
          id: image.fileId,
          projectId: workspace.workspaceId,
          projectName: workspace.workspaceName,
          preset: themeListProvider.selectedPreset,
          effect: themeListProvider.selectedEffect,
          selectedImage: image,
        ));
      } else {
        _noMaskImageCount++;
      }
    }
    notifyListeners();
  }

  /// 监听主题列表共享数据变化
  void _onThemeListSharedDataChanged() {
    if (disposed) {
      return;
    }
    if (_currentSelectedSample != null) {
      // 只有在同时选择了预设和创意时，才更新样本信息
      final selectedPreset = themeListProvider.selectedPreset;
      final selectedEffect = themeListProvider.selectedEffect;

      // 确保预设和创意都存在，并且创意属于当前选中的预设
      if (selectedPreset != null && selectedEffect != null) {
        // 验证创意是否属于当前选中的预设
        final effectBelongsToPreset = selectedPreset.effects
            .any((effect) => effect.effectCode == selectedEffect.effectCode);

        if (effectBelongsToPreset) {
          _currentSelectedSample!.preset = selectedPreset;
          _currentSelectedSample!.effect = selectedEffect;
          notifyListeners();
        }
      }
    }
  }
}
