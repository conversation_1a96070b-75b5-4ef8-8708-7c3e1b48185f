import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';

class AigcBatchSampleModel {
  final String id;
  final String projectId;
  final String projectName;
  AigcPresetsModel? preset;
  AigcPresetsEffect? effect;
  final AigcPreviewImageItem selectedImage;

  AigcBatchSampleModel({
    required this.id,
    required this.projectId,
    required this.projectName,
    required this.preset,
    required this.effect,
    required this.selectedImage,
  });
}
