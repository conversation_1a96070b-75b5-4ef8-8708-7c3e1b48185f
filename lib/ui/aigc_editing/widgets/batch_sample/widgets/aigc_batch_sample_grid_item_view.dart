import 'dart:io';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.internal.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/model/aigc_batch_sample_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

class AigcBatchSampleGridItemView extends StatefulWidget {
  const AigcBatchSampleGridItemView({
    super.key,
    required this.model,
    this.onReselectPressed,
    this.index = 0,
  });

  final Function(int index, GlobalKey itemKey)? onReselectPressed;
  final int index;
  final AigcBatchSampleModel model;

  @override
  State<AigcBatchSampleGridItemView> createState() =>
      _AigcBatchSampleGridItemViewState();
}

class _AigcBatchSampleGridItemViewState
    extends State<AigcBatchSampleGridItemView> {
  final GlobalKey _itemKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Container(
      key: _itemKey,
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildImageArea(),
          _buildTitleArea(),
        ],
      ),
    );
  }

  Widget _buildImageArea() {
    return Padding(
      padding: const EdgeInsets.only(left: 4, right: 4, top: 4),
      child: Container(
        width: double.infinity,
        height: 182,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          children: [
            // 图片组件
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
              ),
              clipBehavior: Clip.antiAlias,
              child: Image.file(
                File(widget.model.selectedImage.previewPath ?? ''),
                fit: BoxFit.contain,
              ),
            ),

            // 编号
            Positioned(
              left: 4,
              bottom: 4,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: const Color(0xFF1F1F1F).withOpacity(0.8),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: const Color(0xFFFFFFFF).withOpacity(0.1),
                    width: 0.5,
                  ),
                ),
                child: Center(
                  child: Text(
                    '${widget.index + 1}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.medium,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleArea() {
    return Padding(
      padding: const EdgeInsets.only(left: 4, right: 4, bottom: 4, top: 4),
      child: Container(
        width: double.infinity,
        height: 32,
        decoration: BoxDecoration(
          color: const Color(0xFF2E2E2E),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
              ),
              clipBehavior: Clip.antiAlias,
              child: _buildEffectImage(),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                _buildTitle(),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium,
                ),
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () {
                  // 重新选择事件
                  widget.onReselectPressed?.call(widget.index, _itemKey);
                },
                icon: Image.asset(
                  'assets/icons/aigc_edit_batch_sample_replace.png',
                  width: 20,
                  height: 20,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 4),
          ],
        ),
      ),
    );
  }

  String _buildTitle() {
    if (widget.model.preset?.id ==
        AigcPresetsModelInternal.internalAIGCPresetId) {
      return widget.model.preset?.name ?? '';
    }
    return '${widget.model.preset?.name ?? ''}${widget.model.effect?.name != null ? ' - 创意${widget.model.effect?.name}' : ''}';
  }

  Widget _buildEffectImage() {
    final String? thumbUrl = widget.model.effect?.thumbUrl;

    if (thumbUrl == null || thumbUrl.isEmpty) {
      return Image.asset(
        'assets/icons/aigc_presets_loading_icon.png',
        width: 32,
        height: 32,
        fit: BoxFit.cover,
      );
    }

    return (thumbUrl.contains('http'))
        ? CachedImageWidget(
            imageUrl: thumbUrl,
            width: 32,
            height: 32,
            fit: BoxFit.cover,
            placeholder: AigcItemAnimation(
              child: Image.asset(
                'assets/icons/aigc_presets_loading_icon.png',
                width: 32,
                height: 32,
                fit: BoxFit.cover,
              ),
            ),
            showProgress: false,
            errorWidget: Image.asset(
              'assets/icons/aigc_presets_loading_icon.png',
              width: 32,
              height: 32,
              fit: BoxFit.cover,
            ),
          )
        : Image.file(
            File(widget.model.selectedImage.previewPath ?? ''),
            width: 32,
            height: 32,
            fit: BoxFit.cover,
          );
  }
}
