import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/view_model/aigc_batch_sample_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/widgets/aigc_batch_sample_grid_item_view.dart';

class AigcBatchSampleGridView extends StatefulWidget {
  const AigcBatchSampleGridView({
    super.key,
    required this.viewModel,
    this.onItemReselectPressed,
  });

  final AigcBatchSampleViewModel viewModel;
  final Function(int index, GlobalKey itemKey)? onItemReselectPressed;

  @override
  State<AigcBatchSampleGridView> createState() =>
      _AigcBatchSampleGridViewState();
}

class _AigcBatchSampleGridViewState extends State<AigcBatchSampleGridView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RawScrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      thickness: 4,
      radius: const Radius.circular(2),
      thumbColor: Colors.white.withOpacity(0.5),
      trackColor: Colors.white.withOpacity(0.1),
      trackBorderColor: Colors.transparent,
      crossAxisMargin: 2,
      mainAxisMargin: 2,
      child: GridView.builder(
        controller: _scrollController,
        padding: widget.viewModel.sampleList.length > 12
            ? const EdgeInsets.only(right: 8)
            : const EdgeInsets.only(right: 0), // 为滚动条留出空间
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 6,
          childAspectRatio: 192.0 / 228.0,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
        ),
        itemCount: widget.viewModel.sampleList.length,
        itemBuilder: (context, index) => AigcBatchSampleGridItemView(
          model: widget.viewModel.sampleList[index],
          index: index,
          onReselectPressed: widget.onItemReselectPressed,
        ),
      ),
    );
  }
}
