import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_theme_list_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/control/aigc_theme_list_widget.dart';

class AigcBatchSamplePresetList extends StatelessWidget {
  const AigcBatchSamplePresetList({
    super.key,
    required this.viewModel,
    required this.onClose,
  });
  final AigcThemeListViewModel viewModel;
  final VoidCallback onClose;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 285,
      height: 398,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: AigcThemeListWidget(
          backgroundColor: const Color(0xFF383838),
          width: 285,
          height: 398,
          title: '主题预设',
          titleColor: Colors.white,
          showAddButton: false,
          viewModel: viewModel,
          forBatchSample: true,
          onClose: onClose,
        ),
      ),
    );
  }
}
