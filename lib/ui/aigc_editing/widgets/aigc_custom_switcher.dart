import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class AigcCustomSwitcher extends StatefulWidget {
  /// 需要切换的选项列表，目前必须包含两个选项
  final List<String> options;

  /// 选中 tab 的索引，由外部传进行传入控制
  final int? selectedIndex;

  /// 初始选中索引，仅在非受控模式下使用，默认为 0
  final int initialIndex;

  final double width;
  final double height;

  final Function(int)? onChanged;

  const AigcCustomSwitcher({
    super.key,
    required this.options,
    this.width = 253,
    this.height = 32,
    this.selectedIndex,
    this.initialIndex = 0,
    this.onChanged,
  }) : assert(options.length == 2, 'Switcher must have exactly 2 options');

  @override
  State<AigcCustomSwitcher> createState() => _AigcCustomSwitcherState();
}

class _AigcCustomSwitcherState extends State<AigcCustomSwitcher>
    with SingleTickerProviderStateMixin {
  late int _internalSelectedIndex;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  double _currentAnimationValue = 0.0;

  /// 获取当前选中索引
  int get currentSelectedIndex =>
      widget.selectedIndex ?? _internalSelectedIndex;

  @override
  void initState() {
    super.initState();
    _internalSelectedIndex = widget.initialIndex;
    _currentAnimationValue = currentSelectedIndex.toDouble();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: _currentAnimationValue,
      end: _currentAnimationValue,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: const Color(0xFF3B3B3B),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Stack(
        children: [
          // 背景选中指示器
          AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              // 使用动画值或当前静态值
              double position = _animationController.isAnimating
                  ? _slideAnimation.value
                  : _currentAnimationValue;

              return _buildSelectedTabBg(position);
            },
          ),
          // 选项按钮 - 扩大点击区域
          Row(
            children: [
              for (int i = 0; i < widget.options.length; i++)
                Expanded(
                  child: GestureDetector(
                    onTap: () => _switchTab(i),
                    child: Container(
                      height: widget.height,
                      // 确保整个区域都可点击
                      color: Colors.transparent,
                      child: Center(
                        child: AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeInOut,
                          style: TextStyle(
                            color: currentSelectedIndex == i
                                ? Colors.white
                                : const Color(0xB3FFFFFF),
                            fontWeight: currentSelectedIndex == i
                                ? FontWeight.w500
                                : FontWeight.w400,
                            fontFamily: Fonts.defaultFontFamily,
                            fontSize: 12,
                          ),
                          child: Text(widget.options[i]),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void didUpdateWidget(AigcCustomSwitcher oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果外部传入的selectedIndex发生变化，则更新状态
    if (widget.selectedIndex != null &&
        widget.selectedIndex != oldWidget.selectedIndex) {
      _updateSelectedIndex(widget.selectedIndex!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateSelectedIndex(int newIndex) {
    // 检查是否需要执行动画
    bool shouldAnimate = _currentAnimationValue != newIndex.toDouble();

    if (shouldAnimate) {
      // 更新动画的起始值和目标值
      _slideAnimation = Tween<double>(
        begin: _currentAnimationValue,
        end: newIndex.toDouble(),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));

      setState(() {
        // 非受控模式：更新内部状态
        if (widget.selectedIndex == null) {
          _internalSelectedIndex = newIndex;
        }
      });

      _animationController.forward().then((_) {
        // 动画完成后更新当前值并重置控制器
        _currentAnimationValue = newIndex.toDouble();
        _animationController.reset();
      });
    }
  }

  Positioned _buildSelectedTabBg(double position) {
    final double indicatorWidth = (widget.width / 2);
    return Positioned(
      left: position * indicatorWidth,
      top: 0,
      child: Container(
        width: indicatorWidth,
        height: widget.height,
        decoration: BoxDecoration(
            color: const Color(0xFF262626),
            border: Border.all(color: const Color(0xFF404040), width: 1),
            borderRadius: BorderRadius.circular(6),
            boxShadow: const [
              BoxShadow(
                color: Color(0x33000000),
                blurRadius: 4,
              ),
            ]),
      ),
    );
  }

  void _switchTab(int index) {
    _updateSelectedIndex(index);

    // 始终触发onChanged回调
    widget.onChanged?.call(index);
  }
}
