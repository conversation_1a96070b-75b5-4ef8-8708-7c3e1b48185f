import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// AIGC 样本页面专用的 Toast
///
/// 这是一个独立的Toast系统，使用原生Overlay实现。
/// 特点：
/// - 使用Flutter原生Overlay，完全独立于SmartDialog系统
/// - 白色背景，黑色文字的专用样式
/// - 支持自定义颜色、持续时间等参数
/// - 完整的进入和退出动画
/// - 当父Widget被销毁时自动关闭Toast
class AigcEditSampleToast {
  // 存储当前显示的Toast的OverlayEntry和控制器
  static OverlayEntry? _currentOverlay;
  static Timer? _dismissTimer;
  static _SampleToastWidgetState? _currentToastState;

  /// 展示Sample Toast
  /// [context] 用于获取Overlay的BuildContext
  /// [msg] 显示的文字
  /// [icon] 左侧显示的图标（IconData）
  /// [iconPath] 左侧显示的图标路径（String），优先级高于icon
  /// [isAutoDismiss] 是否自动消失，默认true
  /// [duration] 自动消失时长，默认3秒
  /// [backgroundColor] 背景颜色，默认白色
  /// [textColor] 文字颜色，默认黑色
  /// [iconColor] 图标颜色，默认黑色
  /// [parentWidget] 父Widget的GlobalKey，用于相对定位
  static void show(
    BuildContext context,
    String msg, {
    IconData? icon,
    String? iconPath = "assets/icons/aigc_editing_toast_icon.png",
    bool isAutoDismiss = true,
    Duration duration = const Duration(seconds: 3),
    Color? backgroundColor,
    Color? textColor,
    Color? iconColor,
    GlobalKey? parentWidget,
  }) {
    // 先关闭已存在的Toast
    dismissSampleToast();

    // 获取Overlay
    OverlayState? overlay;

    if (parentWidget?.currentContext != null) {
      // 如果指定了父Widget，尝试从父Widget上下文中查找最近的Overlay
      overlay = Overlay.maybeOf(parentWidget!.currentContext!);

      // 如果没有找到父Widget的Overlay，这意味着父Widget可能不在Overlay的子树中
      // 为了确保Toast只在父Widget范围内显示，我们不应该回退到全局Overlay
      if (overlay == null) {
        // 直接返回，不显示Toast
        return;
      }
    } else {
      // 如果没有指定父Widget，使用当前context的Overlay
      overlay = Overlay.of(context);
    }

    // 创建OverlayEntry
    _currentOverlay = OverlayEntry(
      builder: (context) => _SampleToastWidget(
        icon: icon,
        iconPath: iconPath,
        msg: msg,
        backgroundColor: backgroundColor ?? Colors.white,
        textColor: textColor ?? const Color(0xFF1a1a1a),
        iconColor: iconColor ?? Colors.black,
        parentWidget: parentWidget,
        onStateCreated: (state) {
          _currentToastState = state;
        },
        onDismissComplete: () {
          _currentOverlay?.remove();
          _currentOverlay = null;
          _currentToastState = null;
        },
      ),
    );

    // 插入到Overlay中
    overlay.insert(_currentOverlay!);

    // 设置自动关闭定时器
    if (isAutoDismiss) {
      _dismissTimer = Timer(duration, () {
        dismissSampleToast();
      });
    }
  }

  /// 手动关闭Toast
  static void dismissSampleToast() {
    _dismissTimer?.cancel();
    _dismissTimer = null;

    // 如果有正在显示的Toast，触发消失动画
    if (_currentToastState != null) {
      _currentToastState!.dismiss();
    }
    // 如果没有状态引用，直接移除
    else {
      _currentOverlay?.remove();
      _currentOverlay = null;
    }
  }

  /// 检查是否有正在显示的Toast
  static bool isSampleToastVisible() {
    return _currentOverlay != null;
  }
}

/// Sample Toast显示组件
class _SampleToastWidget extends StatefulWidget {
  final IconData? icon;
  final String? iconPath;
  final String msg;
  final Color backgroundColor;
  final Color textColor;
  final Color iconColor;
  final GlobalKey? parentWidget;
  final Function(_SampleToastWidgetState)? onStateCreated;
  final VoidCallback? onDismissComplete;

  const _SampleToastWidget({
    this.icon,
    this.iconPath,
    required this.msg,
    required this.backgroundColor,
    required this.textColor,
    required this.iconColor,
    this.parentWidget,
    this.onStateCreated,
    this.onDismissComplete,
  });

  @override
  State<_SampleToastWidget> createState() => _SampleToastWidgetState();
}

class _SampleToastWidgetState extends State<_SampleToastWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isDismissing = false;
  Timer? _parentCheckTimer;
  String? _initialRoute;
  bool _initialCanPop = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 通知父级状态已创建
    widget.onStateCreated?.call(this);

    // 添加生命周期观察者
    WidgetsBinding.instance.addObserver(this);

    // 记录初始路由和Navigator状态（如果有父Widget的话）
    if (widget.parentWidget?.currentContext != null) {
      try {
        final parentContext = widget.parentWidget!.currentContext!;
        final modalRoute = ModalRoute.of(parentContext);
        final navigator = Navigator.of(parentContext);
        _initialRoute = modalRoute?.settings.name;
        _initialCanPop = navigator.canPop();
      } catch (e) {
        _initialRoute = null;
        _initialCanPop = false;
      }
    }

    // 如果指定了父Widget，启动定时器监听父Widget的生命周期
    if (widget.parentWidget != null) {
      _startParentLifecycleCheck();
    }

    // 开始进入动画
    _animationController.forward();
  }

  /// 启动父Widget生命周期检查
  void _startParentLifecycleCheck() {
    _parentCheckTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      // 检查父Widget是否还存在或者页面是否已经变化
      if (_shouldDismissToast()) {
        // 父Widget已被销毁或页面已变化，关闭Toast
        timer.cancel();
        if (!_isDismissing) {
          dismiss();
        }
      }
    });
  }

  /// 判断是否应该关闭Toast
  bool _shouldDismissToast() {
    // 1. 检查父Widget是否还存在
    if (widget.parentWidget?.currentContext == null) {
      return true;
    }

    final parentContext = widget.parentWidget!.currentContext!;

    // 2. 检查父Widget是否还mounted
    if (!parentContext.mounted) {
      return true;
    }

    // 3. 检查父Widget是否还在当前的Widget树中（通过查找RenderObject）
    try {
      final renderObject = parentContext.findRenderObject();
      if (renderObject == null || !renderObject.attached) {
        return true;
      }
    } catch (e) {
      // 如果查找RenderObject出错，说明Widget可能已经被销毁
      return true;
    }

    // 4. 关键检查：对于GoRouter的push导航，检查父Widget的页面是否被遮挡
    try {
      final modalRoute = ModalRoute.of(parentContext);

      // 检查当前路由是否还是最顶层的活跃路由
      if (modalRoute != null && !modalRoute.isCurrent) {
        return true;
      }

      // 检查是否有其他路由覆盖在当前路由之上
      // 通过检查Navigator的路由历史栈
      final navigator = Navigator.of(parentContext);
      final currentRoute = modalRoute;

      // 如果当前有Modal路由但不是最上层的，说明有新页面覆盖了
      if (currentRoute != null) {
        // 获取Navigator的状态，检查是否有其他路由在栈顶
        try {
          // 关键检测：如果初始时不能pop，但现在可以pop，说明有新路由被push了
          final currentCanPop = navigator.canPop();
          if (!_initialCanPop && currentCanPop) {
            return true;
          }

          // 额外检查：如果路由名称发生了变化
          if (_initialRoute != null) {
            final currentRouteName = currentRoute.settings.name;
            if (currentRouteName != _initialRoute) {
              return true;
            }
          }
        } catch (e) {
          // 安全起见，如果检查出错就关闭Toast
          return true;
        }
      }
    } catch (e) {
      // 如果获取路由信息出错，为了安全起见关闭Toast
      return true;
    }

    return false;
  }

  /// 执行消失动画
  void dismiss() {
    if (_isDismissing) {
      return;
    }
    _isDismissing = true;

    // 取消父Widget生命周期检查
    _parentCheckTimer?.cancel();
    _parentCheckTimer = null;

    // 执行反向动画
    _animationController.reverse().then((_) {
      // 动画完成后通知父级
      widget.onDismissComplete?.call();
    });
  }

  /// 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用进入后台或恢复时，检查Toast是否应该关闭
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.resumed) {
      if (widget.parentWidget != null && _shouldDismissToast()) {
        if (!_isDismissing) {
          dismiss();
        }
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _parentCheckTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果指定了父Widget，在每次build时检查父Widget是否还有效
    if (widget.parentWidget != null && _shouldDismissToast()) {
      // 如果父Widget无效，立即关闭Toast
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_isDismissing) {
          dismiss();
        }
      });
      // 返回空容器，避免显示
      return const SizedBox.shrink();
    }

    // 获取父Widget的位置和尺寸
    double? parentLeft;
    double? parentWidth;

    if (widget.parentWidget != null &&
        widget.parentWidget!.currentContext != null) {
      final RenderBox? parentRenderBox =
          widget.parentWidget!.currentContext!.findRenderObject() as RenderBox?;
      if (parentRenderBox != null && parentRenderBox.hasSize) {
        final Offset parentPosition =
            parentRenderBox.localToGlobal(Offset.zero);
        parentLeft = parentPosition.dx;
        parentWidth = parentRenderBox.size.width;
      }
    }

    return Positioned(
      top: 48, // 距离顶部48，与其他Toast一致
      left: (parentLeft ?? 0) + 4,
      right: parentWidth != null
          ? (MediaQuery.of(context).size.width -
              (parentLeft! + parentWidth) +
              4)
          : 4,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              height: 56,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 图标部分
                  if (widget.icon != null)
                    Icon(
                      widget.icon!,
                      color: widget.iconColor,
                      size: 24,
                    ),
                  if (widget.iconPath != null)
                    Image.asset(
                      widget.iconPath!,
                      width: 24,
                      height: 24,
                      color: widget.iconColor,
                    ),
                  // 如果有图标，添加间距
                  if (widget.icon != null || widget.iconPath != null)
                    const SizedBox(width: 8),
                  // 文字部分
                  Flexible(
                    child: Text(
                      widget.msg,
                      style: TextStyle(
                        color: widget.textColor,
                        fontSize: 14,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.medium,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
