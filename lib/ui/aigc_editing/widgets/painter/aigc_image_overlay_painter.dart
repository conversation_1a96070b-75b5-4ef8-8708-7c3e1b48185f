import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_overlay_provider.dart';

/// 图像叠加绘制器
class AigcImageOverlayPainter extends CustomPainter {
  /// 显示模式
  final MaskDisplayMode displayMode;

  /// 是否显示蒙版
  final bool showMask;

  final BuildContext context;

  /// 蒙版背景颜色
  final Color maskBackgroundColor;

  /// 当前缩放比例
  final double currentScale;

  /// 缩放后的画布尺寸
  final Size scaledCanvasSize;

  /// 构造函数
  AigcImageOverlayPainter({
    required this.displayMode,
    required this.showMask,
    required this.context,
    required this.maskBackgroundColor,
    required this.currentScale,
    required this.scaledCanvasSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final provider = context.read<AigcImageOverlayProvider>();
    final backgroundImage = provider.backgroundImage;

    // 如果没有背景图片，不绘制任何内容
    if (backgroundImage == null) {
      return;
    }

    final controlProvider = context.read<AigcEditingControlProvider>();
    final rect = Rect.fromLTWH(
        0, 0, size.width.ceilToDouble(), size.height.ceilToDouble());

    // 眼睛逻辑：关闭小眼睛时显示原图
    if (!controlProvider.isShowSingleMattingScope) {
      // 关闭小眼睛：只显示原图，没有蒙版和纯色背景
      _drawBackgroundImage(canvas, rect);
      return;
    }

    // 眼睛开启状态：根据显示模式进行不同的绘制
    switch (displayMode) {
      case MaskDisplayMode.overlay:
        // 显示叠加：显示原图背景+主体蒙版
        _drawBackgroundImage(canvas, rect);
        _drawMaskImage(canvas, rect);
        break;
      case MaskDisplayMode.pureColor:
        // 纯色背景：仅显示主体和选择的纯色背景，不显示蒙版
        _drawWithPureColorBackground(canvas, size);
        break;
    }
  }

  /// 绘制背景图片
  void _drawBackgroundImage(Canvas canvas, Rect rect) {
    final provider = context.read<AigcImageOverlayProvider>();
    final image = provider.backgroundImage!;
    final imageSize =
        Size(image.width.ceilToDouble(), image.height.ceilToDouble());

    // 绘制背景图片
    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, imageSize.width, imageSize.height),
      rect,
      Paint()..filterQuality = FilterQuality.high,
    );
  }

  /// 绘制蒙层图片
  void _drawMaskImage(Canvas canvas, Rect rect) {
    final provider = context.read<AigcImageOverlayProvider>();
    final maskImage = provider.maskImage;

    // 绘制蒙版图片（如果存在且需要显示）
    if (showMask && maskImage != null) {
      final paint = Paint();
      paint.blendMode = BlendMode.srcOver;
      paint.filterQuality = FilterQuality.high;
      // 设置tintColor
      paint.colorFilter = ColorFilter.mode(
        Colors.red.withOpacity(0.3),
        BlendMode.srcIn,
      );

      // 使用蒙版图片本身的尺寸作为源矩形
      final maskWidth = maskImage.width.ceilToDouble();
      final maskHeight = maskImage.height.ceilToDouble();

      // 直接绘制蒙层，使用蒙版图片本身的尺寸
      canvas.drawImageRect(
        maskImage,
        Rect.fromLTWH(0, 0, maskWidth, maskHeight),
        rect,
        paint,
      );
    }
  }

  /// 使用纯色背景模式绘制
  void _drawWithPureColorBackground(Canvas canvas, Size size) {
    final controlProvider = context.read<AigcEditingControlProvider>();

    final provider = context.read<AigcImageOverlayProvider>();
    final backgroundImage = provider.backgroundImage;
    final maskImage = provider.maskImage;

    if (backgroundImage == null || maskImage == null) {
      if (backgroundImage != null) {
        final rect = Rect.fromLTWH(0, 0, size.width, size.height);
        _drawBackgroundImage(canvas, rect);
      }
      return;
    }

    // 计算图像绘制区域
    final imageSize = Size(
      backgroundImage.width.ceilToDouble(),
      backgroundImage.height.ceilToDouble(),
    );
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 步骤1：绘制纯色背景或格子背景
    if (controlProvider.maskBackgroundColor.color != Colors.transparent) {
      // 使用纯色背景
      canvas.drawRect(
        rect,
        Paint()
          ..color = controlProvider.maskBackgroundColor.color
          ..filterQuality = FilterQuality.high,
      );
    }

    // 步骤2：使用图层混合模式实现蒙版效果，只保留主体部分
    // 保存图层，在这个图层中进行混合操作
    canvas.saveLayer(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Paint()
          ..color = Colors.white
          ..filterQuality = FilterQuality.high);

    // 在图层中绘制原图
    canvas.drawImageRect(
      backgroundImage,
      Rect.fromLTWH(0, 0, imageSize.width, imageSize.height),
      rect,
      Paint()..filterQuality = FilterQuality.high,
    );

    // 使用蒙版进行dstIn混合，只保留蒙版区域的原图
    canvas.drawImageRect(
      maskImage,
      Rect.fromLTWH(0, 0, maskImage.width.ceilToDouble(),
          maskImage.height.ceilToDouble()),
      rect,
      Paint()
        ..blendMode = BlendMode.dstIn
        ..filterQuality = FilterQuality.high,
    );

    // 恢复图层，此时图层内容（只有蒙版区域的原图）会与底层的纯色背景进行正常混合
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant AigcImageOverlayPainter oldDelegate) {
    // final provider = context.read<AigcImageOverlayProvider>();
    // final currentBackgroundImage = provider.backgroundImage;
    // final currentMaskImage = provider.maskImage;

    // try {
    //   final oldProvider = oldDelegate.context.read<AigcImageOverlayProvider>();
    //   final oldBackgroundImage = oldProvider.backgroundImage;
    //   final oldMaskImage = oldProvider.maskImage;

    //   // 检查所有可能触发重绘的条件
    //   final backgroundChanged = oldBackgroundImage != currentBackgroundImage;
    //   final maskChanged = oldMaskImage != currentMaskImage;
    //   final displayModeChanged = oldDelegate.displayMode != displayMode;
    //   final showMaskChanged = oldDelegate.showMask != showMask;
    //   final scaleChanged = oldDelegate.currentScale != currentScale;
    //   final backgroundColorChanged =
    //       oldDelegate.maskBackgroundColor != maskBackgroundColor;
    //   final canvasSizeChanged =
    //       oldDelegate.scaledCanvasSize != scaledCanvasSize;

    //   final shouldRepaint = backgroundChanged ||
    //       maskChanged ||
    //       displayModeChanged ||
    //       showMaskChanged ||
    //       scaleChanged ||
    //       backgroundColorChanged ||
    //       canvasSizeChanged;

    //   if (shouldRepaint) {
    //     debugPrint('ImageOverlayPainter: 重绘原因: ${[
    //       if (backgroundChanged) '背景图片变化',
    //       if (maskChanged) '蒙版图片变化',
    //       if (displayModeChanged) '显示模式变化',
    //       if (showMaskChanged) '蒙版显示状态变化',
    //       if (scaleChanged) '缩放变化',
    //       if (backgroundColorChanged) '背景色变化',
    //       if (canvasSizeChanged) '画布尺寸变化'
    //     ].join(', ')}');
    //   }

    //   return shouldRepaint;
    // } catch (e) {
    //   // 如果无法访问旧的Provider，则总是重绘
    //   debugPrint('shouldRepaint: 无法访问旧Provider，强制重绘 - $e');
    //   return true;
    // }
    return true;
  }
}
