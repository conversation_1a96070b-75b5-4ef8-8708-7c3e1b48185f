import 'dart:ui' as ui;

import 'package:flutter/material.dart';

/// 自定义光标绘制器
class AigcCursorPainter extends CustomPainter {
  final double brushSize;

  AigcCursorPainter(this.brushSize);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = brushSize / 2;

    // 绘制外阴影（黑色，blur值为4）
    final outerShadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.05)
      ..maskFilter = const ui.MaskFilter.blur(ui.BlurStyle.normal, 4.0);

    canvas.drawCircle(center, radius, outerShadowPaint);

    // 绘制圆形边框
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawCircle(center, radius, borderPaint);

    // 绘制内阴影（白色，blur值为30）
    // 使用剪切路径来创建内阴影效果
    canvas.save();

    // 创建圆形剪切路径
    final circlePath = Path()
      ..addOval(Rect.fromCircle(center: center, radius: radius));
    canvas.clipPath(circlePath);

    // 绘制内阴影
    final innerShadowPaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..maskFilter = const ui.MaskFilter.blur(ui.BlurStyle.normal, 30.0);

    // 绘制一个稍小的圆来创建内阴影效果
    canvas.drawCircle(center, radius - 2, innerShadowPaint);

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant AigcCursorPainter oldDelegate) {
    return oldDelegate.brushSize != brushSize;
  }
}

/// 十字光标绘制器
class AigcCrosshairCursorPainter extends CustomPainter {
  /// 十字光标默认大小
  static const double crossCursorSize = 14;

  /// 十字光标中显示提示部分的大小，即绘制白色部分
  static const double crossCursorTipSize = 12;

  final double size;

  AigcCrosshairCursorPainter(this.size);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final strokeHalfSize = this.size / 2;
    const crossHalfSize = crossCursorTipSize / 2;

    // 绘制黑色边框
    final borderPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 4.0
      ..style = PaintingStyle.fill;

    // 绘制水平线边框
    canvas.drawLine(
      Offset(center.dx - strokeHalfSize, center.dy),
      Offset(center.dx + strokeHalfSize, center.dy),
      borderPaint,
    );

    // 绘制垂直线边框
    canvas.drawLine(
      Offset(center.dx, center.dy - strokeHalfSize),
      Offset(center.dx, center.dy + strokeHalfSize),
      borderPaint,
    );

    // 绘制白色十字线
    final crossPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // 绘制水平线
    canvas.drawLine(
      Offset(center.dx - crossHalfSize, center.dy),
      Offset(center.dx + crossHalfSize, center.dy),
      crossPaint,
    );

    // 绘制垂直线
    canvas.drawLine(
      Offset(center.dx, center.dy - crossHalfSize),
      Offset(center.dx, center.dy + crossHalfSize),
      crossPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! AigcCrosshairCursorPainter ||
        oldDelegate.size != size;
  }
}
