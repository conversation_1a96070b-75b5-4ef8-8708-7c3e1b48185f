import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_painter_path_provider.dart';

/// 路径绘制器 - 用于绘制用户绘制的路径
class AigcPathPainter extends CustomPainter {
  /// 绘制批次列表
  final List<DrawingBatch> drawingBatches;

  /// 当前正在绘制的批次
  final DrawingBatch? currentBatch;

  /// 是否显示用户路径
  final bool showUserPaths;

  /// 原始画布尺寸（用于坐标转换）
  final Size? originalCanvasSize;

  final double brushSize;

  /// 构造函数
  const AigcPathPainter({
    required this.drawingBatches,
    required this.currentBatch,
    required this.showUserPaths,
    required this.brushSize,
    this.originalCanvasSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (!showUserPaths) {
      return;
    }

    // 只绘制最后一次绘制的路径
    DrawingBatch? lastBatch;

    // 优先显示当前正在进行的批次
    if (currentBatch != null && currentBatch!.points.isNotEmpty) {
      lastBatch = currentBatch;
    }
    // 否则显示最后一个完成的批次
    else if (drawingBatches.isNotEmpty) {
      lastBatch = drawingBatches.last;
    }

    if (lastBatch != null) {
      _drawBatch(canvas, size, lastBatch);
    }
  }

  /// 绘制单个批次
  void _drawBatch(Canvas canvas, Size size, DrawingBatch batch) {
    if (batch.points.isEmpty) {
      return;
    }

    // 计算缩放比例
    double scaleX = 1.0;
    double scaleY = 1.0;

    if (originalCanvasSize != null &&
        originalCanvasSize!.width > 0 &&
        originalCanvasSize!.height > 0) {
      scaleX = size.width / originalCanvasSize!.width;
      scaleY = size.height / originalCanvasSize!.height;
    }

    final paint = Paint()
      ..color = batch.isErase
          ? const Color(0xFF3F48CC).withOpacity(0.7)
          : const Color(0xFFFF1E1E).withOpacity(0.7)
      ..strokeWidth = brushSize
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final path = Path();
    for (int i = 0; i < batch.points.length; i++) {
      final point = batch.points[i];
      // 应用缩放比例到坐标
      final actualX = point.offset.dx * scaleX;
      final actualY = point.offset.dy * scaleY;

      if (i == 0) {
        path.moveTo(actualX, actualY);
      } else {
        path.lineTo(actualX, actualY);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant AigcPathPainter oldDelegate) {
    return oldDelegate.drawingBatches != drawingBatches ||
        oldDelegate.currentBatch != currentBatch ||
        oldDelegate.showUserPaths != showUserPaths ||
        oldDelegate.originalCanvasSize != originalCanvasSize;
  }
}
