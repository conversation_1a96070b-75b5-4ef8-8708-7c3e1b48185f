import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/model/history/aigc_editing_history_state.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_history_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/control/aigc_navigation_button.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 蒙版操作标题栏组件
/// 包含标题、上一步、下一步、重置按钮和显示/隐藏按钮
class AigcCanvasMaskOperationHeader extends StatelessWidget {
  const AigcCanvasMaskOperationHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      // color: const Color(0xFF1F1F1F),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "主体区域保护",
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          Row(
            children: [
              // 历史记录导航按钮（上一步和下一步）
              Selector<AigcEditingHistoryProvider, bool>(
                selector: (context, historyProvider) =>
                    historyProvider.hasHistoryForCurrentImage,
                builder: (context, historyExists, child) {
                  if (!historyExists) {
                    // 不存在历史记录时，隐藏导航按钮
                    return const SizedBox.shrink();
                  }

                  return Row(
                    children: [
                      // 上一步按钮
                      Selector<AigcEditingHistoryProvider, bool>(
                        selector: (context, historyProvider) =>
                            historyProvider.canUndo,
                        builder: (context, canUndo, child) {
                          return AigcNavigationButton(
                            type: NavigationButtonType.undo,
                            enabled: canUndo,
                            onTap: () {
                              context.read<AigcEditingHistoryProvider>().undo();
                            },
                          );
                        },
                      ),

                      const SizedBox(width: 8),

                      // 下一步按钮
                      Selector<AigcEditingHistoryProvider, bool>(
                        selector: (context, historyProvider) =>
                            historyProvider.canRedo,
                        builder: (context, canRedo, child) {
                          return AigcNavigationButton(
                            type: NavigationButtonType.redo,
                            enabled: canRedo,
                            onTap: () {
                              context.read<AigcEditingHistoryProvider>().redo();
                            },
                          );
                        },
                      ),

                      const SizedBox(width: 8),
                    ],
                  );
                },
              ),

              const SizedBox(width: 8),

              // 重置按钮
              Selector2<AigcEditingControlProvider, AigcEditingHistoryProvider,
                  bool>(
                selector: (context, editingControl, history) {
                  final currentNode = history.getCurrentNode();

                  // 判断重置按钮是否可以点击，要不不是默认默认状态，要么存在非重置的历史记录
                  final enableReset = (currentNode != null &&
                          currentNode.data.type !=
                              AigcEditingHistoryType.reset) ||
                      !editingControl.isDefaultControlState;
                  return enableReset;
                },
                builder: (context, isResetEnabled, child) {
                  return GestureDetector(
                    onTap: isResetEnabled
                        ? () => _showResetConfirmDialog(context)
                        : null,
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: Opacity(
                        opacity: isResetEnabled ? 1.0 : 0.5,
                        child: Image.asset(
                          'assets/icons/icon_reset.png',
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: 8),
              // 显示/隐藏按钮
              Selector<AigcEditingControlProvider, bool>(
                selector: (context, controlProvider) =>
                    controlProvider.isShowSingleMattingScope,
                builder: (context, isShowSingleMattingScope, child) {
                  return GestureDetector(
                    onTap: () {
                      final controlProvider =
                          context.read<AigcEditingControlProvider>();
                      controlProvider.setIsShowSingleMattingScope(
                          value: !isShowSingleMattingScope);
                    },
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: Image.asset(
                        isShowSingleMattingScope
                            ? 'assets/icons/open_eye.png'
                            : 'assets/icons/close_eye.png',
                        width: 24,
                        height: 24,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示重置确认对话框
  void _showResetConfirmDialog(BuildContext context) {
    if (PGDialog.isDialogVisible(DialogTags.aigcPcDeleteConfirm)) {
      // 如果对话框已经显示，直接返回
      return;
    }
    AigcPcDeleteConfirmDialog.show(context,
        title: '是否重置为初始的智能主体识别保护区域', content: '重置后会到图片初始状态', onConfirm: () {
      PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
      _onResetButtonTapped(context);
    });
  }

  /// 处理重置按钮点击事件
  void _onResetButtonTapped(BuildContext context) {
    final controlProvider = context.read<AigcEditingControlProvider>();
    final imageProvider = context.read<AigcEditingImageProvider>();

    // 1. 设置ControlProvider为默认状态
    controlProvider.setDefaultControlState();

    // 2. 触发重置交互式蒙版事件（通过事件机制通知CanvasViewModel）
    imageProvider.triggerResetInteractiveMask();
  }
}
