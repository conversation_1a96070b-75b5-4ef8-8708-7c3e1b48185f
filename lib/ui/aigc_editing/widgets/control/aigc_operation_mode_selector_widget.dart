import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_custom_switcher.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 用于AIGC编辑页中切换抠图模式的选择器组件
class AIGCOperationModeSelector extends StatelessWidget {
  const AIGCOperationModeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Selector<AigcEditingControlProvider, bool>(
        selector: (context, provider) => provider.isInSmartBoxSelectionMode,
        builder: (context, isOperationModeSelected, child) {
          final selectedIndex = isOperationModeSelected ? 1 : 0;

          return Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: AigcCustomSwitcher(
              options: const ['智能画笔', '框选区域'],
              width: 250,
              height: 32,
              selectedIndex: selectedIndex,
              onChanged: (index) {
                context
                    .read<AigcEditingControlProvider>()
                    .setSmartBoxModeEnabled(value: index == 1);
              },
            ),
          );
        });
  }
}

/// 用于显示智能画笔抠图模式的遮罩提示组件
class AIGCSmartBoxMaskWidget extends StatelessWidget {
  const AIGCSmartBoxMaskWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Container(
        alignment: Alignment.topCenter,
        margin: const EdgeInsets.only(top: 96), // 留出标题区域
        decoration: const BoxDecoration(
          color: Color(0xFF1F1F1F),
        ),
        child: Column(
          children: [
            Image.asset('assets/icons/aigc_smart_box_tip.png',
                width: 80, height: 80),
            const SizedBox(height: 16),
            Text('自由框选图片区域主体，精准自动\n识别框选内的主体区域',
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: const Color(0xB3FFFFFF),
                    fontSize: 12,
                    fontFamily: Fonts.defaultFontFamily)),
          ],
        ),
      ),
    );
  }
}
