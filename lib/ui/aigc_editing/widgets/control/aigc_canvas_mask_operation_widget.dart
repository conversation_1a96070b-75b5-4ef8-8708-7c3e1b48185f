import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_custom_switcher.dart';
import 'package:turing_art/ui/aigc_editing/widgets/control/aigc_canvas_mask_operation_header.dart';
import 'package:turing_art/ui/aigc_editing/widgets/control/aigc_operation_mode_selector_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 蒙版操作控件 - 负责主体区域保护、画笔调整、蒙版显示模式设置等操作
///
/// 遵循组合模式（Composite Pattern）和策略模式（Strategy Pattern）
class AigcCanvasMaskOperationWidget extends StatefulWidget {
  const AigcCanvasMaskOperationWidget({
    super.key,
  });

  @override
  State<AigcCanvasMaskOperationWidget> createState() =>
      _AigcCanvasMaskOperationWidgetState();
}

class _AigcCanvasMaskOperationWidgetState
    extends State<AigcCanvasMaskOperationWidget> {
  @override
  Widget build(BuildContext context) {
    return Consumer<AigcEditingControlProvider>(
      builder: (context, controlProvider, child) {
        final isEyeOpen = controlProvider.isShowSingleMattingScope;
        final isInSmartBoxSelectionMode =
            controlProvider.isInSmartBoxSelectionMode;

        return Stack(
          children: [
            // 原有的视图内容
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const AigcCanvasMaskOperationHeader(),
                const AIGCOperationModeSelector(),
                _buildBrushSizeView(),
                _buildTipsView(),
                const SizedBox(height: 16),
                _buildMaskDisplayView(),
              ],
            ),
            // 当眼睛关闭时，在上方显示遮罩
            if (!isEyeOpen)
              Positioned.fill(
                child: Container(
                  margin: const EdgeInsets.only(top: 80), // 留出标题区域
                  decoration: BoxDecoration(
                    color: const Color(0xFF1F1F1F).withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

            if (isInSmartBoxSelectionMode) const AIGCSmartBoxMaskWidget()
          ],
        );
      },
    );
  }

  /// 构建提示视图
  Widget _buildTipsView() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 4),
      width: double.infinity,
      height: 28,
      decoration: BoxDecoration(
        color: const Color(0xFF404040),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(width: 4),
          Image.asset(
            'assets/icons/icon_tips.png',
            width: 20,
            height: 20,
          ),
          const SizedBox(width: 2),
          Text(
            "鼠标左键添加选区，鼠标右键移除选区。",
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              height: 16 / 12,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建画笔大小调整视图
  Widget _buildBrushSizeView() {
    final controlProvider = context.read<AigcEditingControlProvider>();
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 8),
      width: 253,
      height: 48,
      color: const Color(0xFF1F1F1F),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "画笔大小",
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  height: 16 / 12,
                  fontFamily: Fonts.defaultFontFamily,
                ),
              ),
              ChangeNotifierProvider.value(
                value: controlProvider,
                child: Consumer<AigcEditingControlProvider>(
                  builder: (context, controlProvider, child) {
                    return Text(
                      controlProvider.brushSize.toInt().toString(),
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        height: 16 / 12,
                        fontFamily: Fonts.defaultFontFamily,
                      ),
                      textAlign: TextAlign.right,
                    );
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          SliderTheme(
            data: SliderThemeData(
              trackHeight: 1,
              activeTrackColor: Colors.white.withOpacity(0.95),
              inactiveTrackColor: Colors.white.withOpacity(0.2),
              thumbShape: SliderCustomThumbShape(),
              overlayShape: SliderComponentShape.noOverlay,
            ),
            child: ChangeNotifierProvider.value(
              value: controlProvider,
              child: Consumer<AigcEditingControlProvider>(
                builder: (context, provider, child) {
                  return ExcludeFocus(
                    // 排除焦点，完全阻止这个组件及其子组件获得键盘焦点
                    child: Slider(
                      value: provider.brushSize,
                      min: 1,
                      max: 100,
                      onChanged: (value) => provider.setBrushSize(value),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建蒙版显示模式视图
  Widget _buildMaskDisplayView() {
    final controlProvider = context.read<AigcEditingControlProvider>();
    return ChangeNotifierProvider.value(
      value: controlProvider,
      child: Consumer<AigcEditingControlProvider>(
        builder: (context, controlProvider, child) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            width: 253,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "蒙版显示模式",
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    height: 18 / 12,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                ),
                const SizedBox(height: 8),
                _buildSegmentControl(controlProvider),
                // 显示颜色选择器（仅在纯色背景模式下）
                if (controlProvider.displayMode ==
                    MaskDisplayMode.pureColor) ...[
                  const SizedBox(height: 8),
                  _buildColorSelector(),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  /// 蒙版显示模式改变
  void _onDisplayModeChanged(
      AigcEditingControlProvider controlProvider, MaskDisplayMode mode) {
    controlProvider.setMaskDisplayMode(mode);
  }

  /// 构建分段控件
  Widget _buildSegmentControl(AigcEditingControlProvider controlProvider) {
    final selectedIndex =
        controlProvider.displayMode == MaskDisplayMode.overlay ? 0 : 1;
    return AigcCustomSwitcher(
      options: const ['显示叠加', '纯色背景'],
      width: 250,
      height: 32,
      selectedIndex: selectedIndex,
      onChanged: (index) => _onDisplayModeChanged(
        controlProvider,
        MaskDisplayMode.values[index],
      ),
    );
  }

  /// 构建分段按钮
  Widget _buildSegmentButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 30,
          margin: const EdgeInsets.all(1),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF262626) : Colors.transparent,
            borderRadius: BorderRadius.circular(5),
            border: isSelected
                ? Border.all(color: const Color(0xFF404040), width: 1)
                : null,
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ]
                : null,
          ),
          alignment: Alignment.center,
          child: Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.white70,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
              height: 18 / 12,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建颜色选择器
  Widget _buildColorSelector() {
    return SizedBox(
      height: 40,
      child: Row(
        children: [
          Text(
            "背景颜色",
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: _buildColorList(),
          ),
        ],
      ),
    );
  }

  /// 构建颜色列表
  Widget _buildColorList() {
    return SizedBox(
      height: 24,
      child: ListView(
        scrollDirection: Axis.horizontal,
        reverse: true, // 从右向左显示
        children: [
          _buildColorItem(
            color: const Color(0xFFE32636),
            type: MaskBackgroundColor.red,
          ),
          const SizedBox(width: 8),
          _buildColorItem(
            color: const Color(0xFF3F48CC),
            type: MaskBackgroundColor.blue,
          ),
          const SizedBox(width: 8),
          _buildColorItem(
            color: const Color(0xFF00FF40),
            type: MaskBackgroundColor.green,
          ),
          const SizedBox(width: 8),
          _buildColorItem(
            imageAsset: 'assets/icons/aigc_edit_bg_lucency.png',
            type: MaskBackgroundColor.transparent,
          ),
        ],
      ),
    );
  }

  /// 构建颜色项
  Widget _buildColorItem({
    Color? color,
    String? imageAsset,
    required MaskBackgroundColor type,
  }) {
    final controlProvider = context.watch<AigcEditingControlProvider>();
    final isSelected = controlProvider.maskBackgroundColor == type;
    Widget itemChild = color != null
        ? Container(
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
            ),
          )
        : Image.asset(
            imageAsset!,
            fit: BoxFit.cover,
          );

    return ChangeNotifierProvider.value(
      value: controlProvider,
      child: Consumer<AigcEditingControlProvider>(
        builder: (context, controlProvider, child) {
          return GestureDetector(
            onTap: () => controlProvider.setMaskBackgroundColor(type),
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: isSelected
                    ? Border.all(color: Colors.white, width: 1)
                    : null,
              ),
              clipBehavior: Clip.none,
              child: itemChild,
            ),
          );
        },
      ),
    );
  }
}

/// 自定义Slider滑块形状
class SliderCustomThumbShape extends SliderComponentShape {
  // 构造函数中预加载图片
  SliderCustomThumbShape();

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return const Size(4, 16);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final canvas = context.canvas;

    // 绘制深色背景圆形
    final Paint backgroundPaint = Paint()
      ..color = const Color(0xFF1F1F1F)
      ..style = PaintingStyle.fill;

    // 绘制白色边框
    final Paint borderPaint = Paint()
      ..color = Colors.white.withOpacity(0.95)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    // 绘制8像素半径的圆形，带2像素宽的边框
    canvas.drawCircle(center, 6.0, backgroundPaint);
    canvas.drawCircle(center, 6.0, borderPaint);
  }
}
