import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_theme_list_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_hover_container.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

/// 主题选择组件
/// 显示一个可选择主题的网格列表，支持切换到子视图选择创意
class AigcThemeListWidget extends StatefulWidget {
  /// 背景色
  final Color backgroundColor;

  /// 整体宽度
  final double width;

  /// 整体高度
  final double height;

  /// 顶部标题文本
  final String title;

  /// 标题颜色
  final Color titleColor;

  /// 是否显示添加按钮（如果为false则显示关闭按钮）
  final bool showAddButton;

  /// 关闭按钮点击回调
  final VoidCallback? onClose;

  /// 添加按钮点击回调
  final VoidCallback? onAdd;

  /// 创建按钮点击回调（空视图状态使用）
  final VoidCallback? onCreateClick;

  /// 主题列表ViewModel
  final AigcThemeListViewModel viewModel;

  /// 空视图图标资源
  final String emptyIconAsset;

  final double itemWidth = 122.0;
  final double itemHeight = 122.0;

  /// 是否用于批量打样
  final bool forBatchSample;

  const AigcThemeListWidget({
    super.key,
    required this.backgroundColor,
    required this.width,
    required this.height,
    required this.title,
    required this.titleColor,
    required this.showAddButton,
    this.forBatchSample = false,
    this.onClose,
    this.onAdd,
    this.onCreateClick,
    required this.viewModel,
    this.emptyIconAsset = 'assets/icons/aigc_theme_empty.png',
  });

  @override
  State<AigcThemeListWidget> createState() => _AigcThemeListWidgetState();
}

class _AigcThemeListWidgetState extends State<AigcThemeListWidget> {
  // 添加一个标记来缓存创意列表显示状态
  bool _forceShowCreativeList = false;

  // 添加ScrollController实例
  final ScrollController _themeScrollController = ScrollController();
  final ScrollController _creativeScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _themeScrollController.dispose();
    _creativeScrollController.dispose();
    // 确保在组件销毁时重置所有状态
    _forceShowCreativeList = false;
    if (!widget.forBatchSample) {
      widget.viewModel.dispose();
    } else {
      // 在批量打样模式下，检查 ViewModel 是否已经被释放
      try {
        widget.viewModel.hideCreativeList();
        widget.viewModel.selectThemeWithoutNotify(0);
      } catch (e) {
        // 如果 ViewModel 已经被释放，忽略错误
        // 这是正常情况，因为父组件可能已经释放了 ViewModel
      }
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.viewModel,
      child: Consumer<AigcThemeListViewModel>(
          builder: (context, viewModel, child) {
        // 检测图片是否切换，如果切换了则重置UI状态
        if (viewModel.isChangeImageSoucreFileId) {
          // 使用WidgetsBinding.instance.addPostFrameCallback确保在构建完成后重置状态
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _forceShowCreativeList = false;
              });
            }
          });
        }

        // 检查是否需要重置强制显示状态
        // 如果当前选中的主题没有创意，或者ViewModel已经隐藏创意列表，则重置强制显示
        if (_forceShowCreativeList) {
          final selectedTheme = viewModel.selectedTheme;
          if (selectedTheme == null ||
              selectedTheme.effects.isEmpty ||
              !viewModel.showingCreativeList) {
            // 使用WidgetsBinding.instance.addPostFrameCallback确保在构建完成后更新状态
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && _forceShowCreativeList) {
                setState(() {
                  _forceShowCreativeList = false;
                });
              }
            });
          }
        }

        // 使用本地状态来决定是否显示创意列表，优先使用强制显示标记
        final bool shouldShowCreativeList =
            _forceShowCreativeList || viewModel.showingCreativeList;

        return Container(
          width: widget.width,
          height: widget.height,
          color: widget.backgroundColor,
          child: Column(
            children: [
              _buildHeader(viewModel),
              Expanded(
                child: viewModel.themeItems.isEmpty
                    ? _buildEmptyView()
                    : shouldShowCreativeList &&
                            viewModel.selectedThemeIndex != null
                        ? _buildCreativeGrid(viewModel)
                        : _buildThemeGrid(viewModel),
              ),
            ],
          ),
        );
      }),
    );
  }

  /// 构建空视图
  Widget _buildEmptyView() {
    return Center(
      child: SizedBox(
        width: 156,
        height: 138,
        child: Column(
          children: [
            // 空视图图标
            SizedBox(
              width: 156,
              height: 78,
              child: Image.asset(
                widget.emptyIconAsset,
                fit: BoxFit.contain,
              ),
            ),
            // 文本提示
            const SizedBox(height: 8),
            Text(
              "当前暂无主题预设",
              style: TextStyle(
                fontWeight: FontWeight.normal,
                fontFamily: Fonts.defaultFontFamily,
                color: Colors.white54,
                fontSize: 12,
                height: 16 / 12,
              ),
            ),
            // 立即创建按钮
            const SizedBox(height: 8),
            DebounceClickWidget(
              onTap: () {
                widget.viewModel.createTheme();
              },
              child: Container(
                width: 64,
                height: 28,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF22EDFF),
                      Color(0xFF87F5FF),
                    ],
                  ),
                ),
                alignment: Alignment.center,
                child: Text(
                  "立即创建",
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    color: const Color(0xFF1F1F1F),
                    fontSize: 12,
                    height: 16 / 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建顶部标题区域
  Widget _buildHeader(AigcThemeListViewModel viewModel) {
    // 使用本地状态来决定是否显示返回按钮，优先使用强制显示标记
    final bool shouldShowCreativeList =
        _forceShowCreativeList || viewModel.showingCreativeList;

    final title = shouldShowCreativeList && viewModel.selectedThemeIndex != null
        ? viewModel.themeItems[viewModel.selectedThemeIndex!].name
        : widget.title;

    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          // 如果在创意列表中，显示返回按钮
          if (shouldShowCreativeList &&
              viewModel.selectedThemeIndex != null) ...[
            GestureDetector(
              onTap: () {
                setState(() {
                  // 点击返回时立即取消强制显示
                  _forceShowCreativeList = false;
                });

                viewModel.hideCreativeList();
              },
              child: AIGCHoverContainer(
                width: 24,
                height: 24,
                borderRadius: BorderRadius.circular(4),
                child: Image.asset(
                  'assets/icons/white_back.png',
                  width: 24,
                  height: 24,
                ),
              ),
            ),
            const SizedBox(width: 4),
          ],
          // 标题文本
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                color: widget.titleColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                height: 18 / 14,
                fontFamily: Fonts.defaultFontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          DebounceClickWidget(
              onTap:
                  widget.showAddButton ? viewModel.createTheme : widget.onClose,
              child: AIGCHoverContainer(
                width: 24,
                height: 24,
                borderRadius: BorderRadius.circular(4),
                child: Image.asset(
                  widget.showAddButton
                      ? 'assets/icons/icon_add.png'
                      : 'assets/icons/dialog_close.png',
                  width: 24,
                  height: 24,
                ),
              )),
        ],
      ),
    );
  }

  /// 构建主题网格
  Widget _buildThemeGrid(AigcThemeListViewModel viewModel) {
    // 计算每个网格项的尺寸和间距
    const double itemWidth = 122.0;
    const double itemHeight = 122.0;
    const double spacing = 8.0;

    return Padding(
      padding: const EdgeInsets.only(bottom: 1), // 为底部渐变留出空间
      child: Stack(
        children: [
          // 主滚动视图
          RawScrollbar(
            thumbVisibility: true,
            thumbColor: Colors.white.withOpacity(0.2),
            thickness: 4,
            radius: const Radius.circular(2),
            controller: _themeScrollController,
            child: GridView.builder(
              controller: _themeScrollController,
              padding: const EdgeInsets.only(
                  left: 16, right: 16, top: 8, bottom: 40),
              // 底部添加40像素的padding
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: spacing,
                crossAxisSpacing: spacing,
                childAspectRatio: itemWidth / itemHeight,
              ),
              itemCount: viewModel.themeItems.length,
              itemBuilder: (context, index) {
                return _buildThemeItem(viewModel, index);
              },
            ),
          ),
          // 底部渐变 - 使用 IgnorePointer 避免拦截点击事件
          Positioned(
            left: 4,
            right: 4,
            bottom: -1,
            height: 41,
            child: IgnorePointer(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: !widget.forBatchSample
                        ? [
                            const Color(0xFF1F1F1F).withOpacity(0.0),
                            const Color(0xFF1F1F1F),
                          ]
                        : [
                            const Color(0xFF383838).withOpacity(0.0),
                            const Color(0xFF383838),
                          ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个主题项
  Widget _buildThemeItem(AigcThemeListViewModel viewModel, int index) {
    final item = viewModel.themeItems[index];
    final isSelected = viewModel.realSelectedThemeId == item.id;
    final imageUrl =
        item.effects.isNotEmpty ? item.effects.first.thumbUrl ?? '' : '';
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // 修改逻辑，先检查是否有创意项，然后再进行状态变更
          if (item.effects.isNotEmpty) {
            setState(() {
              // 点击主题时设置强制显示创意列表
              _forceShowCreativeList = true;
            });

            // 先选中主题，但不触发通知
            viewModel.selectThemeWithoutNotify(index);

            // 然后显示创意列表
            viewModel.showCreativeList();
          } else {
            // 如果没有创意项，只选中主题
            // 确保取消强制显示创意列表
            setState(() {
              _forceShowCreativeList = false;
            });
            viewModel.selectTheme(index);
          }
        },
        child: Material(
          color: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF262626),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFFFF9EB9)
                    : Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Stack(
                fit: StackFit.expand,
                alignment: Alignment.bottomCenter,
                children: [
                  _buildThemeItemCoverImage(imageUrl, isSelected),
                  // 底部文本
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.0),
                            Colors.black.withOpacity(0.6),
                          ],
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 0),
                        child: Text(
                          item.name,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            height: 17 / 12,
                            fontFamily: Fonts.defaultFontFamily,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThemeItemCoverImage(String imageUrl, bool isSelected) {
    if (imageUrl.isEmpty) {
      return Image.asset('assets/icons/aigc_presets_loading_icon.png');
    }

    if (imageUrl.startsWith('http')) {
      return Container(
        margin: (isSelected) ? const EdgeInsets.all(1) : EdgeInsets.zero,
        child: CachedImageWidget(
          imageUrl: imageUrl,
          width: widget.itemWidth,
          height: widget.itemHeight,
          fit: BoxFit.cover,
          borderRadius: BorderRadius.circular(4),
          placeholder: AigcItemAnimation(
            child: Image.asset(
              'assets/icons/aigc_presets_loading_icon.png',
              width: widget.itemWidth,
              height: widget.itemHeight,
              fit: BoxFit.cover,
            ),
          ),
          showProgress: false,
          errorWidget: Image.asset(
            'assets/icons/aigc_presets_loading_icon.png',
            width: widget.itemWidth,
            height: widget.itemHeight,
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      return Container(
        margin: (isSelected) ? const EdgeInsets.all(1) : EdgeInsets.zero,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.file(
            File(imageUrl),
            width: widget.itemWidth,
            height: widget.itemHeight,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Image.asset(
                'assets/icons/aigc_presets_loading_icon.png',
                width: widget.itemWidth,
                height: widget.itemHeight,
                fit: BoxFit.cover,
              );
            },
          ),
        ),
      );
    }
  }

  /// 构建创意网格
  Widget _buildCreativeGrid(AigcThemeListViewModel viewModel) {
    if (viewModel.selectedThemeIndex == null) {
      return const SizedBox.shrink();
    }

    final selectedTheme = viewModel.themeItems[viewModel.selectedThemeIndex!];
    final creativeItems = selectedTheme.effects;

    // 计算每个网格项的尺寸和间距
    const double spacing = 8.0;

    // 添加一个外层MouseRegion来捕获鼠标离开整个创意列表区域的事件
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      // 当鼠标离开整个创意列表区域时，清除悬停状态
      onExit: (_) {
        viewModel.clearCreativeHover();
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 1), // 修改为0，因为使用了底部padding和渐变
        child: Stack(
          children: [
            // 创意滚动视图
            RawScrollbar(
              thumbVisibility: true,
              thumbColor: Colors.white.withOpacity(0.2),
              thickness: 4,
              radius: const Radius.circular(2),
              controller: _creativeScrollController,
              child: GridView.builder(
                controller: _creativeScrollController,
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 8, bottom: 40),
                // 底部添加40像素的padding
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: spacing,
                  crossAxisSpacing: spacing,
                  childAspectRatio: widget.itemWidth / widget.itemHeight,
                ),
                itemCount: creativeItems.length,
                itemBuilder: (context, index) {
                  return _buildCreativeGridItem(
                      viewModel, viewModel.selectedThemeIndex!, index);
                },
              ),
            ),
            // 底部渐变
            Positioned(
              left: 4,
              right: 4,
              bottom: -1,
              height: 41,
              child: IgnorePointer(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: !widget.forBatchSample
                          ? [
                              const Color(0xFF1F1F1F).withOpacity(0.0),
                              const Color(0xFF1F1F1F),
                            ]
                          : [
                              const Color(0xFF383838).withOpacity(0.0),
                              const Color(0xFF383838),
                            ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建单个创意网格项
  Widget _buildCreativeGridItem(
      AigcThemeListViewModel viewModel, int themeIndex, int creativeIndex) {
    final theme = viewModel.themeItems[themeIndex];
    final creativeItem = theme.effects[creativeIndex];
    final imageUrl = creativeItem.thumbUrl ?? '';
    final isSelected = (viewModel.selectedCreateId == creativeItem.effectCode);

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (event) {
        // 直接调用ViewModel方法，但不触发UI重建
        // 因为我们已经在ViewModel中移除了notifyListeners()调用
        viewModel.setCreativeHover(creativeIndex, event.position);
      },
      onExit: (event) {
        // 直接调用ViewModel方法，但不触发UI重建
        // 确保每次鼠标离开格子时都调用clearCreativeHover
        viewModel.clearCreativeHover();
      },
      child: GestureDetector(
        onTap: () {
          viewModel.selectCreative(themeIndex, creativeIndex);
        },
        child: Material(
          color: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF262626),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFFFF9EB9)
                    : Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  _buildThemeItemCoverImage(imageUrl, isSelected),

                  // 底部文本
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.0),
                            Colors.black.withOpacity(0.6),
                          ],
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 0),
                        child: Text(
                          creativeItem.name,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            height: 17 / 12,
                            fontFamily: Fonts.defaultFontFamily,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
