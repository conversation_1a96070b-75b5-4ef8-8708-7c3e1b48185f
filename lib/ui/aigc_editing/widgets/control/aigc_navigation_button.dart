import 'package:flutter/material.dart';

/// 导航按钮类型枚举
enum NavigationButtonType {
  /// 上一步按钮
  undo,

  /// 下一步按钮
  redo,
}

/// 通用导航按钮组件
/// 支持上一步和下一步操作，包含禁用状态的样式处理
class AigcNavigationButton extends StatelessWidget {
  /// 按钮类型
  final NavigationButtonType type;

  /// 是否启用
  final bool enabled;

  /// 点击回调
  final VoidCallback? onTap;

  /// 按钮大小
  final double size;

  const AigcNavigationButton({
    super.key,
    required this.type,
    required this.enabled,
    this.onTap,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: enabled ? onTap : null,
      child: SizedBox(
        width: size,
        height: size,
        child: Opacity(
          opacity: enabled ? 1.0 : 0.5,
          child: Image.asset(
            _getIconPath(),
            width: size,
            height: size,
          ),
        ),
      ),
    );
  }

  /// 根据按钮类型获取图标路径
  String _getIconPath() {
    switch (type) {
      case NavigationButtonType.undo:
        return 'assets/icons/icon_operation_backward.png';
      case NavigationButtonType.redo:
        return 'assets/icons/icon_operation_forward.png';
    }
  }
}
