import 'package:flutter/material.dart';

/// AIGC Hover 容器组件。当鼠标悬停时显示背景色的容器
class AIGCHoverContainer extends StatefulWidget {
  final Widget child;

  /// 正常状态下的背景色，默认透明
  final Color? backgroundColor;

  /// 悬停时的背景色
  final Color? hoverBackgroundColor;

  /// 容器宽度
  final double? width;

  /// 容器高度
  final double? height;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 外边距
  final EdgeInsetsGeometry? margin;

  /// 边框圆角
  final BorderRadiusGeometry? borderRadius;

  const AIGCHoverContainer({
    super.key,
    required this.child,
    this.backgroundColor,
    this.hoverBackgroundColor = const Color(0x1AFFFFFF),
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
  });

  @override
  State<AIGCHoverContainer> createState() => _AIGCHoverContainerState();
}

class _AIGCHoverContainerState extends State<AIGCHoverContainer> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Container(
        width: widget.width,
        height: widget.height,
        margin: widget.margin,
        padding: widget.padding,
        decoration: BoxDecoration(
          color:
              _isHovered ? widget.hoverBackgroundColor : widget.backgroundColor,
          borderRadius: widget.borderRadius,
        ),
        child: widget.child,
      ),
    );
  }
}
