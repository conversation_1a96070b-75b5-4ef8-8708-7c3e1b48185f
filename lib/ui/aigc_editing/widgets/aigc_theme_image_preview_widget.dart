import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.internal.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

/// 图片预览组件
///
/// 遵循桥接模式，将抽象部分(预览视图)与实现部分(图片加载)分离
/// 将图片的显示与加载逻辑分离，使它们可以独立变化
class AigcThemeImagePreviewWidget extends StatefulWidget {
  /// 要预览的创意项
  final AigcPresetsEffect creativeItem;

  /// 是否可见
  final bool visible;

  /// 图片真实尺寸（用于初始化，可选）
  final Size? imageSize;

  /// 边框圆角，默认4
  final double borderRadius;

  /// 鼠标离开回调
  final VoidCallback? onMouseExit;

  const AigcThemeImagePreviewWidget({
    super.key,
    required this.creativeItem,
    required this.visible,
    this.imageSize,
    this.borderRadius = 4,
    this.onMouseExit,
  });

  @override
  State<AigcThemeImagePreviewWidget> createState() =>
      _AigcThemeImagePreviewWidgetState();
}

class _AigcThemeImagePreviewWidgetState
    extends State<AigcThemeImagePreviewWidget> {
  /// 动态更新的图片尺寸
  Size? _dynamicImageSize;

  @override
  void initState() {
    super.initState();
    // 初始化时使用传入的图片尺寸
    _dynamicImageSize = widget.imageSize;
  }

  /// 图片加载完成时的回调
  void _onImageLoaded(Size imageSize) {
    if (mounted && imageSize != _dynamicImageSize) {
      setState(() {
        _dynamicImageSize = imageSize;
      });
    }
  }

  /// 获取限制可见大小
  ///
  /// 根据图片真实尺寸，将最大边缩放或放大到500，并按比例调整短边
  ///
  /// 返回新的显示尺寸
  Size getLimitVisibleSize() {
    if (_dynamicImageSize == null) {
      // 如果没有尺寸信息，返回默认尺寸
      return const Size(0, 0);
    }

    const double maxSize = 500.0;
    final double originalWidth = _dynamicImageSize!.width;
    final double originalHeight = _dynamicImageSize!.height;

    // 获取最大边
    final double maxDimension =
        originalWidth > originalHeight ? originalWidth : originalHeight;

    // 如果最大边为0，返回默认尺寸
    if (maxDimension == 0) {
      return const Size(0, 0);
    }

    // 计算缩放比例
    final double scale = maxSize / maxDimension;

    // 按比例计算新的宽高
    final double newWidth = originalWidth * scale;
    final double newHeight = originalHeight * scale;

    return Size(newWidth, newHeight);
  }

  /// 获取显示宽度
  double get realWidth {
    final limitSize = getLimitVisibleSize();
    return limitSize.width;
  }

  /// 获取显示高度
  double get realHeight {
    final limitSize = getLimitVisibleSize();
    return limitSize.height;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.visible) {
      return const SizedBox.shrink();
    }

    return MouseRegion(
      // 当鼠标离开预览图时，触发回调
      onExit: (_) {
        if (widget.onMouseExit != null) {
          widget.onMouseExit!();
        }
      },
      child: Container(
        width: realWidth,
        height: realHeight,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Stack(
            children: [
              // 图片加载器 - 实现部分
              if (widget.creativeItem.isInternal)
                _ImageLocalLoader(
                  imageUrl: widget.creativeItem.photoUrl ?? '',
                  onImageLoaded: _onImageLoaded,
                )
              else
                _ImageLoader(
                  imageUrl: widget.creativeItem.photoUrl ?? '',
                  imageSize: Size(realWidth, realHeight),
                  onImageLoaded: _onImageLoaded,
                )
            ],
          ),
        ),
      ),
    );
  }
}

/// 图片加载器组件
///
/// 实现图片的加载、错误处理和加载状态显示
class _ImageLoader extends StatefulWidget {
  final String imageUrl;
  final Size imageSize;
  final void Function(Size) onImageLoaded;

  const _ImageLoader({
    required this.imageUrl,
    required this.imageSize,
    required this.onImageLoaded,
  });

  @override
  State<_ImageLoader> createState() => _ImageLoaderState();
}

class _ImageLoaderState extends State<_ImageLoader> {
  @override
  Widget build(BuildContext context) {
    return CachedImageWidget(
      imageUrl: widget.imageUrl,
      width: widget.imageSize.width,
      height: widget.imageSize.height,
      fit: BoxFit.contain,
      placeholder: AigcItemAnimation(
        child: Image.asset(
          'assets/icons/aigc_presets_loading_icon.png',
          width: widget.imageSize.width,
          height: widget.imageSize.height,
          fit: BoxFit.cover,
        ),
      ),
      showProgress: false,
      errorWidget: Image.asset(
        'assets/icons/aigc_presets_loading_icon.png',
        width: widget.imageSize.width,
        height: widget.imageSize.height,
        fit: BoxFit.cover,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    // 使用 addPostFrameCallback 确保在 build 完成后再执行异步操作
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadImageSize();
    });
  }

  Future<void> _loadImageSize() async {
    try {
      final imageProvider = NetworkImage(widget.imageUrl);
      final ImageStream stream =
          imageProvider.resolve(ImageConfiguration.empty);

      late ImageStreamListener listener;
      listener =
          ImageStreamListener((ImageInfo imageInfo, bool synchronousCall) {
        if (mounted) {
          final realSize = Size(
            imageInfo.image.width.toDouble(),
            imageInfo.image.height.toDouble(),
          );
          widget.onImageLoaded(realSize);
        }
        stream.removeListener(listener);
      });

      stream.addListener(listener);
    } catch (e) {
      // 如果获取尺寸失败，使用默认尺寸
      if (mounted) {
        widget.onImageLoaded(const Size(500, 500));
      }
    }
  }
}

/// 本地图片加载器组件
///
/// 实现本地图片的加载、错误处理和加载状态显示
class _ImageLocalLoader extends StatefulWidget {
  final String imageUrl;
  final void Function(Size) onImageLoaded;

  const _ImageLocalLoader({
    required this.imageUrl,
    required this.onImageLoaded,
  });

  @override
  State<_ImageLocalLoader> createState() => _ImageLocalLoaderState();
}

class _ImageLocalLoaderState extends State<_ImageLocalLoader> {
  @override
  void initState() {
    super.initState();
    // 使用 addPostFrameCallback 确保在 build 完成后再执行异步操作
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadImageAndGetSize();
    });
  }

  Future<void> _loadImageAndGetSize() async {
    try {
      final file = File(widget.imageUrl);
      if (file.existsSync()) {
        final bytes = await file.readAsBytes();
        final codec = await ui.instantiateImageCodec(bytes);
        final frame = await codec.getNextFrame();
        if (mounted) {
          final realSize = Size(
            frame.image.width.toDouble(),
            frame.image.height.toDouble(),
          );
          widget.onImageLoaded(realSize);
        }
      }
    } catch (e) {
      // 如果获取尺寸失败，使用默认尺寸
      if (mounted) {
        widget.onImageLoaded(const Size(500, 500));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Image.file(
      File(widget.imageUrl),
      fit: BoxFit.cover,
      width: double.infinity,
      height: double.infinity,
      errorBuilder: (context, error, stackTrace) {
        return Image.asset(
          'assets/icons/aigc_presets_loading_icon.png',
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
        );
      },
    );
  }
}
