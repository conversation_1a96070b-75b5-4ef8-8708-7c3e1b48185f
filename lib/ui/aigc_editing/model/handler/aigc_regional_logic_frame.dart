import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/model/handler/touch_chain_handler.dart';
import 'package:turing_art/ui/aigc_editing/model/regional/aigc_regional_frame.dart';
import 'package:turing_art/ui/aigc_editing/model/regional/aigc_regional_frame_extension.dart';

/// 区域逻辑框
class AigcRegionalLogicFrame extends TouchChainHandler {
  /// 区域框
  final AigcRegionalFrame _regionFrame;

  /// 区域框位置
  Offset get position => _regionFrame.position;

  /// 区域框矩形
  Rect get rect => _regionFrame.rect;

  double get scale => _regionFrame.scale;

  /// 判断当前矩形是否合法
  bool get isValid => _regionFrame.isValid;

  AigcRegionalFrame get regionalFrame => _regionFrame;

  /// 区域框处理器
  AigcRegionalLogicFrame(Offset startPosition, Offset endPosition, double scale)
      : _regionFrame = AigcRegionalFrame(
          startPosition: startPosition,
          endPosition: endPosition,
          scale: scale,
        );

  /// 获取相对位置
  Offset getRelativePosition(double scale) {
    double factor = scale / _regionFrame.scale;
    return Offset(
      (_regionFrame.position.dx * factor / scale).floorToDouble(),
      (_regionFrame.position.dy * factor / scale).floorToDouble(),
    );
  }

  /// 获取相对矩形
  Rect getRelativeRect(double scale) {
    double factor = scale / _regionFrame.scale;
    return Rect.fromLTWH(
      (_regionFrame.rect.left * factor / scale).floorToDouble(),
      (_regionFrame.rect.top * factor / scale).floorToDouble(),
      (_regionFrame.rect.width * factor / scale).floorToDouble(),
      (_regionFrame.rect.height * factor / scale).floorToDouble(),
    );
  }

  @override
  TouchChainHandler? handleTouch(Offset touchPosition) {
    if (_regionFrame.rect.contains(touchPosition)) {
      return this;
    }
    return handleNext(touchPosition);
  }
}
