import 'package:flutter/material.dart';

/// 区域触摸处理责任链抽象基类
abstract class TouchChainHandler {
  /// 下一个处理器
  TouchChainHandler? _nextHandler;

  /// 设置下一个处理器
  void setNext(TouchChainHandler handler) {
    _nextHandler = handler;
  }

  /// 处理下一个处理器
  TouchChainHandler? handleNext(Offset touchPosition) {
    if (_nextHandler != null) {
      return _nextHandler!.handleTouch(touchPosition);
    }
    return null;
  }

  /// 处理触摸事件
  TouchChainHandler? handleTouch(Offset touchPosition);
}
