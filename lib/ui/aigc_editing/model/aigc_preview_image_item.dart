import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_preview_image_item.freezed.dart';

@freezed
class AigcPreviewImageItem with _$AigcPreviewImageItem {
  const factory AigcPreviewImageItem({
    required String fileId,
    required String originalPath,
    String? thumbnailPath,
    String? previewPath,
    String? highQualityPath,
    String? maskPath,
    @Default(false) bool iconized,
  }) = _AigcPreviewImageItem;

  const AigcPreviewImageItem._();

  bool get hasThumbnail => thumbnailPath != null;
  bool get hasMask => maskPath != null;

  bool get thumbnailExists =>
      thumbnailPath != null && File(thumbnailPath!).existsSync();

  /// 检查图片是否损坏
  /// 判断依据：iconized == true 且 readyToDisplay == false
  bool get isDamaged => iconized && !readyToDisplay;

  /// 检查图片是否准备好显示
  /// 判断依据：thumbnailPath、highQualityPath、previewPath全部存在
  bool get readyToDisplay {
    // 检查缩略图是否存在
    if (thumbnailPath == null || !File(thumbnailPath!).existsSync()) {
      return false;
    }
    // 检查高清图是否存在
    if (highQualityPath == null || !File(highQualityPath!).existsSync()) {
      return false;
    }
    // 检查预览图是否存在
    if (previewPath == null || !File(previewPath!).existsSync()) {
      return false;
    }
    return true;
  }
}
