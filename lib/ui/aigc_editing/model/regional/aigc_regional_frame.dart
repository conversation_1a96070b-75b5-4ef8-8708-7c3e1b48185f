import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'offset_converter.dart';

part 'aigc_regional_frame.freezed.dart';
part 'aigc_regional_frame.g.dart';

@freezed
class AigcRegionalFrame with _$AigcRegionalFrame {
  const factory AigcRegionalFrame({
    // 区域缩放比例
    required double scale,
    // 区域开始位置，也是相对于父视图的左上角
    @OffsetConverter() required Offset startPosition,
    // 区域结束位置
    @OffsetConverter() required Offset endPosition,
  }) = _AigcRegionalFrame;

  factory AigcRegionalFrame.fromJson(Map<String, dynamic> json) =>
      _$AigcRegionalFrameFromJson(json);

  static List<AigcRegionalFrame> fromJsonList(
          List<Map<String, dynamic>> jsonList) =>
      jsonList.map((e) => AigcRegionalFrame.fromJson(e)).toList();
}
