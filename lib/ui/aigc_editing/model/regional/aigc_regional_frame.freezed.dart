// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_regional_frame.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcRegionalFrame _$AigcRegionalFrameFromJson(Map<String, dynamic> json) {
  return _AigcRegionalFrame.fromJson(json);
}

/// @nodoc
mixin _$AigcRegionalFrame {
// 区域缩放比例
  double get scale => throw _privateConstructorUsedError; // 区域开始位置，也是相对于父视图的左上角
  @OffsetConverter()
  Offset get startPosition => throw _privateConstructorUsedError; // 区域结束位置
  @OffsetConverter()
  Offset get endPosition => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcRegionalFrameCopyWith<AigcRegionalFrame> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcRegionalFrameCopyWith<$Res> {
  factory $AigcRegionalFrameCopyWith(
          AigcRegionalFrame value, $Res Function(AigcRegionalFrame) then) =
      _$AigcRegionalFrameCopyWithImpl<$Res, AigcRegionalFrame>;
  @useResult
  $Res call(
      {double scale,
      @OffsetConverter() Offset startPosition,
      @OffsetConverter() Offset endPosition});
}

/// @nodoc
class _$AigcRegionalFrameCopyWithImpl<$Res, $Val extends AigcRegionalFrame>
    implements $AigcRegionalFrameCopyWith<$Res> {
  _$AigcRegionalFrameCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scale = null,
    Object? startPosition = null,
    Object? endPosition = null,
  }) {
    return _then(_value.copyWith(
      scale: null == scale
          ? _value.scale
          : scale // ignore: cast_nullable_to_non_nullable
              as double,
      startPosition: null == startPosition
          ? _value.startPosition
          : startPosition // ignore: cast_nullable_to_non_nullable
              as Offset,
      endPosition: null == endPosition
          ? _value.endPosition
          : endPosition // ignore: cast_nullable_to_non_nullable
              as Offset,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcRegionalFrameImplCopyWith<$Res>
    implements $AigcRegionalFrameCopyWith<$Res> {
  factory _$$AigcRegionalFrameImplCopyWith(_$AigcRegionalFrameImpl value,
          $Res Function(_$AigcRegionalFrameImpl) then) =
      __$$AigcRegionalFrameImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double scale,
      @OffsetConverter() Offset startPosition,
      @OffsetConverter() Offset endPosition});
}

/// @nodoc
class __$$AigcRegionalFrameImplCopyWithImpl<$Res>
    extends _$AigcRegionalFrameCopyWithImpl<$Res, _$AigcRegionalFrameImpl>
    implements _$$AigcRegionalFrameImplCopyWith<$Res> {
  __$$AigcRegionalFrameImplCopyWithImpl(_$AigcRegionalFrameImpl _value,
      $Res Function(_$AigcRegionalFrameImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scale = null,
    Object? startPosition = null,
    Object? endPosition = null,
  }) {
    return _then(_$AigcRegionalFrameImpl(
      scale: null == scale
          ? _value.scale
          : scale // ignore: cast_nullable_to_non_nullable
              as double,
      startPosition: null == startPosition
          ? _value.startPosition
          : startPosition // ignore: cast_nullable_to_non_nullable
              as Offset,
      endPosition: null == endPosition
          ? _value.endPosition
          : endPosition // ignore: cast_nullable_to_non_nullable
              as Offset,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcRegionalFrameImpl implements _AigcRegionalFrame {
  const _$AigcRegionalFrameImpl(
      {required this.scale,
      @OffsetConverter() required this.startPosition,
      @OffsetConverter() required this.endPosition});

  factory _$AigcRegionalFrameImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcRegionalFrameImplFromJson(json);

// 区域缩放比例
  @override
  final double scale;
// 区域开始位置，也是相对于父视图的左上角
  @override
  @OffsetConverter()
  final Offset startPosition;
// 区域结束位置
  @override
  @OffsetConverter()
  final Offset endPosition;

  @override
  String toString() {
    return 'AigcRegionalFrame(scale: $scale, startPosition: $startPosition, endPosition: $endPosition)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcRegionalFrameImpl &&
            (identical(other.scale, scale) || other.scale == scale) &&
            (identical(other.startPosition, startPosition) ||
                other.startPosition == startPosition) &&
            (identical(other.endPosition, endPosition) ||
                other.endPosition == endPosition));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, scale, startPosition, endPosition);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcRegionalFrameImplCopyWith<_$AigcRegionalFrameImpl> get copyWith =>
      __$$AigcRegionalFrameImplCopyWithImpl<_$AigcRegionalFrameImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcRegionalFrameImplToJson(
      this,
    );
  }
}

abstract class _AigcRegionalFrame implements AigcRegionalFrame {
  const factory _AigcRegionalFrame(
          {required final double scale,
          @OffsetConverter() required final Offset startPosition,
          @OffsetConverter() required final Offset endPosition}) =
      _$AigcRegionalFrameImpl;

  factory _AigcRegionalFrame.fromJson(Map<String, dynamic> json) =
      _$AigcRegionalFrameImpl.fromJson;

  @override // 区域缩放比例
  double get scale;
  @override // 区域开始位置，也是相对于父视图的左上角
  @OffsetConverter()
  Offset get startPosition;
  @override // 区域结束位置
  @OffsetConverter()
  Offset get endPosition;
  @override
  @JsonKey(ignore: true)
  _$$AigcRegionalFrameImplCopyWith<_$AigcRegionalFrameImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
