import 'dart:math';

import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/model/regional/aigc_regional_frame.dart';

extension AigcRegionalFrameExtension on AigcRegionalFrame {
  // 区域在父视图的位置，取开始和结束位置中的较小值
  Offset get position => Offset(min(startPosition.dx, endPosition.dx),
      min(startPosition.dy, endPosition.dy));

  // 区域在父视图的矩形框
  Rect get rect => Rect.fromPoints(startPosition, endPosition);

  /// 获取选择框的宽度
  double get width => (endPosition.dx - startPosition.dx).abs();

  /// 获取选择框的高度
  double get height => (endPosition.dy - startPosition.dy).abs();

  /// 判断选择框是否有效（面积大于0）
  bool get isValid => width > 0 && height > 0;
}
