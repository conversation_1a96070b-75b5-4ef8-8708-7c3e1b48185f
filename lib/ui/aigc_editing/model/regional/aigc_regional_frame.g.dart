// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_regional_frame.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcRegionalFrameImpl _$$AigcRegionalFrameImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcRegionalFrameImpl(
      scale: (json['scale'] as num).toDouble(),
      startPosition: const OffsetConverter()
          .fromJson(json['startPosition'] as Map<String, dynamic>),
      endPosition: const OffsetConverter()
          .fromJson(json['endPosition'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AigcRegionalFrameImplToJson(
        _$AigcRegionalFrameImpl instance) =>
    <String, dynamic>{
      'scale': instance.scale,
      'startPosition': const OffsetConverter().toJson(instance.startPosition),
      'endPosition': const OffsetConverter().toJson(instance.endPosition),
    };
