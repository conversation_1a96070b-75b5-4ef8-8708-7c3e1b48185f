// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_preview_image_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AigcPreviewImageItem {
  String get fileId => throw _privateConstructorUsedError;
  String get originalPath => throw _privateConstructorUsedError;
  String? get thumbnailPath => throw _privateConstructorUsedError;
  String? get previewPath => throw _privateConstructorUsedError;
  String? get highQualityPath => throw _privateConstructorUsedError;
  String? get maskPath => throw _privateConstructorUsedError;
  bool get iconized => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AigcPreviewImageItemCopyWith<AigcPreviewImageItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcPreviewImageItemCopyWith<$Res> {
  factory $AigcPreviewImageItemCopyWith(AigcPreviewImageItem value,
          $Res Function(AigcPreviewImageItem) then) =
      _$AigcPreviewImageItemCopyWithImpl<$Res, AigcPreviewImageItem>;
  @useResult
  $Res call(
      {String fileId,
      String originalPath,
      String? thumbnailPath,
      String? previewPath,
      String? highQualityPath,
      String? maskPath,
      bool iconized});
}

/// @nodoc
class _$AigcPreviewImageItemCopyWithImpl<$Res,
        $Val extends AigcPreviewImageItem>
    implements $AigcPreviewImageItemCopyWith<$Res> {
  _$AigcPreviewImageItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
    Object? originalPath = null,
    Object? thumbnailPath = freezed,
    Object? previewPath = freezed,
    Object? highQualityPath = freezed,
    Object? maskPath = freezed,
    Object? iconized = null,
  }) {
    return _then(_value.copyWith(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
      originalPath: null == originalPath
          ? _value.originalPath
          : originalPath // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailPath: freezed == thumbnailPath
          ? _value.thumbnailPath
          : thumbnailPath // ignore: cast_nullable_to_non_nullable
              as String?,
      previewPath: freezed == previewPath
          ? _value.previewPath
          : previewPath // ignore: cast_nullable_to_non_nullable
              as String?,
      highQualityPath: freezed == highQualityPath
          ? _value.highQualityPath
          : highQualityPath // ignore: cast_nullable_to_non_nullable
              as String?,
      maskPath: freezed == maskPath
          ? _value.maskPath
          : maskPath // ignore: cast_nullable_to_non_nullable
              as String?,
      iconized: null == iconized
          ? _value.iconized
          : iconized // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcPreviewImageItemImplCopyWith<$Res>
    implements $AigcPreviewImageItemCopyWith<$Res> {
  factory _$$AigcPreviewImageItemImplCopyWith(_$AigcPreviewImageItemImpl value,
          $Res Function(_$AigcPreviewImageItemImpl) then) =
      __$$AigcPreviewImageItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String fileId,
      String originalPath,
      String? thumbnailPath,
      String? previewPath,
      String? highQualityPath,
      String? maskPath,
      bool iconized});
}

/// @nodoc
class __$$AigcPreviewImageItemImplCopyWithImpl<$Res>
    extends _$AigcPreviewImageItemCopyWithImpl<$Res, _$AigcPreviewImageItemImpl>
    implements _$$AigcPreviewImageItemImplCopyWith<$Res> {
  __$$AigcPreviewImageItemImplCopyWithImpl(_$AigcPreviewImageItemImpl _value,
      $Res Function(_$AigcPreviewImageItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
    Object? originalPath = null,
    Object? thumbnailPath = freezed,
    Object? previewPath = freezed,
    Object? highQualityPath = freezed,
    Object? maskPath = freezed,
    Object? iconized = null,
  }) {
    return _then(_$AigcPreviewImageItemImpl(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
      originalPath: null == originalPath
          ? _value.originalPath
          : originalPath // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailPath: freezed == thumbnailPath
          ? _value.thumbnailPath
          : thumbnailPath // ignore: cast_nullable_to_non_nullable
              as String?,
      previewPath: freezed == previewPath
          ? _value.previewPath
          : previewPath // ignore: cast_nullable_to_non_nullable
              as String?,
      highQualityPath: freezed == highQualityPath
          ? _value.highQualityPath
          : highQualityPath // ignore: cast_nullable_to_non_nullable
              as String?,
      maskPath: freezed == maskPath
          ? _value.maskPath
          : maskPath // ignore: cast_nullable_to_non_nullable
              as String?,
      iconized: null == iconized
          ? _value.iconized
          : iconized // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AigcPreviewImageItemImpl extends _AigcPreviewImageItem {
  const _$AigcPreviewImageItemImpl(
      {required this.fileId,
      required this.originalPath,
      this.thumbnailPath,
      this.previewPath,
      this.highQualityPath,
      this.maskPath,
      this.iconized = false})
      : super._();

  @override
  final String fileId;
  @override
  final String originalPath;
  @override
  final String? thumbnailPath;
  @override
  final String? previewPath;
  @override
  final String? highQualityPath;
  @override
  final String? maskPath;
  @override
  @JsonKey()
  final bool iconized;

  @override
  String toString() {
    return 'AigcPreviewImageItem(fileId: $fileId, originalPath: $originalPath, thumbnailPath: $thumbnailPath, previewPath: $previewPath, highQualityPath: $highQualityPath, maskPath: $maskPath, iconized: $iconized)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcPreviewImageItemImpl &&
            (identical(other.fileId, fileId) || other.fileId == fileId) &&
            (identical(other.originalPath, originalPath) ||
                other.originalPath == originalPath) &&
            (identical(other.thumbnailPath, thumbnailPath) ||
                other.thumbnailPath == thumbnailPath) &&
            (identical(other.previewPath, previewPath) ||
                other.previewPath == previewPath) &&
            (identical(other.highQualityPath, highQualityPath) ||
                other.highQualityPath == highQualityPath) &&
            (identical(other.maskPath, maskPath) ||
                other.maskPath == maskPath) &&
            (identical(other.iconized, iconized) ||
                other.iconized == iconized));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileId, originalPath,
      thumbnailPath, previewPath, highQualityPath, maskPath, iconized);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcPreviewImageItemImplCopyWith<_$AigcPreviewImageItemImpl>
      get copyWith =>
          __$$AigcPreviewImageItemImplCopyWithImpl<_$AigcPreviewImageItemImpl>(
              this, _$identity);
}

abstract class _AigcPreviewImageItem extends AigcPreviewImageItem {
  const factory _AigcPreviewImageItem(
      {required final String fileId,
      required final String originalPath,
      final String? thumbnailPath,
      final String? previewPath,
      final String? highQualityPath,
      final String? maskPath,
      final bool iconized}) = _$AigcPreviewImageItemImpl;
  const _AigcPreviewImageItem._() : super._();

  @override
  String get fileId;
  @override
  String get originalPath;
  @override
  String? get thumbnailPath;
  @override
  String? get previewPath;
  @override
  String? get highQualityPath;
  @override
  String? get maskPath;
  @override
  bool get iconized;
  @override
  @JsonKey(ignore: true)
  _$$AigcPreviewImageItemImplCopyWith<_$AigcPreviewImageItemImpl>
      get copyWith => throw _privateConstructorUsedError;
}
