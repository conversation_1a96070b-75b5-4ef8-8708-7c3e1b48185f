import 'dart:io' as io;
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';
import 'package:turing_art/ffi/models/salient_matters_model.dart';
import 'package:turing_art/utils/image_save_helper.dart';

/// 蒙版绘制路径图片数据信息
class AigcMattingMaskDarwPathImageDataInfo {
  // 文件名常量定义
  static const String kForeStrokesFileName = 'fore_strokes.png';
  static const String kBackStrokesFileName = 'back_strokes.png';
  static const String kPreviousMaskFileName = 'previous_mask.png';
  static const String kCurrentStrokesFileName = 'current_strokes.png';
  static const String kRegionalImageFileName = 'regional_image.jpg';

  /// 区域逻辑框
  AigcRegionalFrameDataInfo? regionalFrameDataInfo;

  /// 图片路径, 作为唯一标识
  String? imagePath;

  /// 当前数据存储的folder路径
  String? currentFolderPath;

  /// 显示图数据
  Uint8List? displayImageBytes;

  /// 展示图蒙版数据
  Uint8List? fullMaskBytes;

  /// 前景笔触图片数据
  Uint8List? foreStrokesBytes;

  /// 背景笔触图片数据
  Uint8List? backStrokesBytes;

  /// 上一次的抠图数据
  Uint8List? previousMaskBytes;

  /// 当前的笔触数据, 用于记录当前的笔触数据
  Uint8List? currentStrokesBytes;

  /// 区域框内Crop的图片数据
  Uint8List? regionalImageBytes;

  /// 是否为涂抹模式（true: 涂抹, false: 擦除）
  bool isBrushMode;

  /// 是否为区域框模式
  bool get isRegionalMode => regionalFrameDataInfo != null;

  AigcMattingMaskDarwPathImageDataInfo({
    this.regionalFrameDataInfo,
    this.imagePath,
    this.foreStrokesBytes,
    this.backStrokesBytes,
    this.displayImageBytes,
    this.previousMaskBytes,
    this.isBrushMode = true,
    this.currentStrokesBytes,
  });

  /// 清除数据
  void clear() {
    regionalFrameDataInfo = null;
    imagePath = null;
    foreStrokesBytes = null;
    backStrokesBytes = null;
    displayImageBytes = null;
    previousMaskBytes = null;
    isBrushMode = true;
    currentStrokesBytes = null;
    regionalImageBytes = null;
    fullMaskBytes = null;
    currentFolderPath = null;
  }

  /// imagePath不一致时, 清除数据
  void clearIfNeeded(String? imagePath) {
    if (this.imagePath == null || imagePath == null) {
      clear();
      return;
    }

    if (this.imagePath != imagePath) {
      clear();
    }
  }

  /// 清理必要的数据
  void clearNecessaryData() {
    foreStrokesBytes = null;
    backStrokesBytes = null;
    previousMaskBytes = null;
    isBrushMode = true;
    currentStrokesBytes = null;
    regionalImageBytes = null;
    fullMaskBytes = null;
    currentFolderPath = null;
    regionalFrameDataInfo = null;
  }

  /// 是否无效
  bool get isInvalid => currentFolderPath == null;

  /// 从文件夹路径读取数据
  Future<void> loadFromDirectory(String directoryPath) async {
    try {
      final directory = io.Directory(directoryPath);
      if (!directory.existsSync()) {
        return;
      }

      clearNecessaryData();
      // 读取全蒙版数据(从maskImage读取，不在这里读取)
      // final fullMaskFile = io.File(path.join(
      //     directoryPath, MediaResourceConstants.getUniversalMaskFileName()));
      // if (fullMaskFile.existsSync()) {
      //   fullMaskBytes = await fullMaskFile.readAsBytes();
      // }

      // 读取前景笔触数据
      final foreStrokesFile =
          io.File(path.join(directoryPath, kForeStrokesFileName));
      if (foreStrokesFile.existsSync()) {
        foreStrokesBytes = await foreStrokesFile.readAsBytes();
      }

      // 读取背景笔触数据
      final backStrokesFile =
          io.File(path.join(directoryPath, kBackStrokesFileName));
      if (backStrokesFile.existsSync()) {
        backStrokesBytes = await backStrokesFile.readAsBytes();
      }

      // 读取上一次抠图数据
      final previousMaskFile =
          io.File(path.join(directoryPath, kPreviousMaskFileName));
      if (previousMaskFile.existsSync()) {
        previousMaskBytes = await previousMaskFile.readAsBytes();
      }

      // 读取区域图片数据
      final regionalImageFile =
          io.File(path.join(directoryPath, kRegionalImageFileName));
      if (regionalImageFile.existsSync()) {
        regionalImageBytes = await regionalImageFile.readAsBytes();
      }
    } catch (e) {
      debugPrint('加载抠图数据失败: $e');
    }
  }

  /// 保存数据到文件夹
  Future<void> saveToDirectory(AigcMattingType mattingType,
      String directoryPath, ImageData? fullMaskImageData) async {
    try {
      final directory = io.Directory(directoryPath);
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }

      switch (mattingType) {
        case AigcMattingType.fullImageSubject:
          // 全图主体抠图：保存全蒙版数据
          if (fullMaskImageData != null) {
            final fullMaskFile = io.File(path.join(directoryPath,
                MediaResourceConstants.getUniversalMaskFileName()));
            await ImageSaveHelper.saveAsPng(
                fullMaskImageData, fullMaskFile.path);
          }
          break;

        case AigcMattingType.fullImageInteractive:
          // 全图交互抠图：保存前景笔触、背景笔触、上一次抠图数据
          if (foreStrokesBytes != null) {
            final foreStrokesFile =
                io.File(path.join(directoryPath, kForeStrokesFileName));
            await foreStrokesFile.writeAsBytes(foreStrokesBytes!);
          }
          if (backStrokesBytes != null) {
            final backStrokesFile =
                io.File(path.join(directoryPath, kBackStrokesFileName));
            await backStrokesFile.writeAsBytes(backStrokesBytes!);
          }
          if (fullMaskImageData != null) {
            final fullMaskFile = io.File(path.join(directoryPath,
                MediaResourceConstants.getUniversalMaskFileName()));
            await ImageSaveHelper.saveAsPng(
                fullMaskImageData, fullMaskFile.path);
          }

          break;

        case AigcMattingType.regionalSubject:
          // 框选主体抠图：保存区域图片数据和全蒙版数据
          if (regionalImageBytes != null) {
            final regionalImageFile =
                io.File(path.join(directoryPath, kRegionalImageFileName));
            await regionalImageFile.writeAsBytes(regionalImageBytes!);
          }
          if (fullMaskImageData != null) {
            final fullMaskFile = io.File(path.join(directoryPath,
                MediaResourceConstants.getUniversalMaskFileName()));
            await ImageSaveHelper.saveAsPng(
                fullMaskImageData, fullMaskFile.path);
          }

          if (previousMaskBytes != null) {
            final previousMaskFile =
                io.File(path.join(directoryPath, kPreviousMaskFileName));
            await previousMaskFile.writeAsBytes(previousMaskBytes!);
          }
          break;

        case AigcMattingType.regionalInteractive:
          // 框选交互抠图：保存区域图片数据、前景笔触、背景笔触、上一次抠图数据
          if (regionalImageBytes != null) {
            final regionalImageFile =
                io.File(path.join(directoryPath, kRegionalImageFileName));
            await regionalImageFile.writeAsBytes(regionalImageBytes!);
          }
          if (foreStrokesBytes != null) {
            final foreStrokesFile =
                io.File(path.join(directoryPath, kForeStrokesFileName));
            await foreStrokesFile.writeAsBytes(foreStrokesBytes!);
          }
          if (backStrokesBytes != null) {
            final backStrokesFile =
                io.File(path.join(directoryPath, kBackStrokesFileName));
            await backStrokesFile.writeAsBytes(backStrokesBytes!);
          }
          if (previousMaskBytes != null) {
            final previousMaskFile =
                io.File(path.join(directoryPath, kPreviousMaskFileName));
            await previousMaskFile.writeAsBytes(previousMaskBytes!);
          }
          if (fullMaskImageData != null) {
            final fullMaskFile = io.File(path.join(directoryPath,
                MediaResourceConstants.getUniversalMaskFileName()));
            await ImageSaveHelper.saveAsPng(
                fullMaskImageData, fullMaskFile.path);
          }

          break;
      }
    } catch (e) {
      debugPrint('保存抠图数据失败: $e');
    }
  }
}

/// 区域框数据信息
class AigcRegionalFrameDataInfo {
  /// 区域框位置
  final Offset position;

  /// 区域框矩形
  final Rect rect;

  /// 构造函数
  AigcRegionalFrameDataInfo({
    required this.position,
    required this.rect,
  });
}

/// 抠图类型枚举
enum AigcMattingType {
  /// 全图主体抠图
  fullImageSubject,

  /// 全图交互抠图
  fullImageInteractive,

  /// 框选主体抠图
  regionalSubject,

  /// 框选交互抠图
  regionalInteractive,
}
