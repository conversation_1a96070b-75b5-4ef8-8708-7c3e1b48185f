// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_editing_history_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AigcEditingHistoryStateImpl _$$AigcEditingHistoryStateImplFromJson(
        Map<String, dynamic> json) =>
    _$AigcEditingHistoryStateImpl(
      fileId: json['fileId'] as String,
      maskPath: json['maskPath'] as String,
      timestamp: (json['timestamp'] as num).toInt(),
      type: $enumDecode(_$AigcEditingHistoryTypeEnumMap, json['type']),
      extraData: json['extraData'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$$AigcEditingHistoryStateImplToJson(
        _$AigcEditingHistoryStateImpl instance) =>
    <String, dynamic>{
      'fileId': instance.fileId,
      'maskPath': instance.maskPath,
      'timestamp': instance.timestamp,
      'type': _$AigcEditingHistoryTypeEnumMap[instance.type]!,
      'extraData': instance.extraData,
    };

const _$AigcEditingHistoryTypeEnumMap = {
  AigcEditingHistoryType.brush: 'brush',
  AigcEditingHistoryType.regional: 'regional',
  AigcEditingHistoryType.reset: 'reset',
};
