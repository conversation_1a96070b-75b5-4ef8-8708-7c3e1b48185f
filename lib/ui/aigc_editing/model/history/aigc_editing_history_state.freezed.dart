// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_editing_history_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AigcEditingHistoryState _$AigcEditingHistoryStateFromJson(
    Map<String, dynamic> json) {
  return _AigcEditingHistoryState.fromJson(json);
}

/// @nodoc
mixin _$AigcEditingHistoryState {
  String get fileId => throw _privateConstructorUsedError;
  String get maskPath => throw _privateConstructorUsedError;
  int get timestamp => throw _privateConstructorUsedError;
  AigcEditingHistoryType get type => throw _privateConstructorUsedError;
  Map<String, dynamic> get extraData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcEditingHistoryStateCopyWith<AigcEditingHistoryState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcEditingHistoryStateCopyWith<$Res> {
  factory $AigcEditingHistoryStateCopyWith(AigcEditingHistoryState value,
          $Res Function(AigcEditingHistoryState) then) =
      _$AigcEditingHistoryStateCopyWithImpl<$Res, AigcEditingHistoryState>;
  @useResult
  $Res call(
      {String fileId,
      String maskPath,
      int timestamp,
      AigcEditingHistoryType type,
      Map<String, dynamic> extraData});
}

/// @nodoc
class _$AigcEditingHistoryStateCopyWithImpl<$Res,
        $Val extends AigcEditingHistoryState>
    implements $AigcEditingHistoryStateCopyWith<$Res> {
  _$AigcEditingHistoryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
    Object? maskPath = null,
    Object? timestamp = null,
    Object? type = null,
    Object? extraData = null,
  }) {
    return _then(_value.copyWith(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
      maskPath: null == maskPath
          ? _value.maskPath
          : maskPath // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AigcEditingHistoryType,
      extraData: null == extraData
          ? _value.extraData
          : extraData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AigcEditingHistoryStateImplCopyWith<$Res>
    implements $AigcEditingHistoryStateCopyWith<$Res> {
  factory _$$AigcEditingHistoryStateImplCopyWith(
          _$AigcEditingHistoryStateImpl value,
          $Res Function(_$AigcEditingHistoryStateImpl) then) =
      __$$AigcEditingHistoryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String fileId,
      String maskPath,
      int timestamp,
      AigcEditingHistoryType type,
      Map<String, dynamic> extraData});
}

/// @nodoc
class __$$AigcEditingHistoryStateImplCopyWithImpl<$Res>
    extends _$AigcEditingHistoryStateCopyWithImpl<$Res,
        _$AigcEditingHistoryStateImpl>
    implements _$$AigcEditingHistoryStateImplCopyWith<$Res> {
  __$$AigcEditingHistoryStateImplCopyWithImpl(
      _$AigcEditingHistoryStateImpl _value,
      $Res Function(_$AigcEditingHistoryStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
    Object? maskPath = null,
    Object? timestamp = null,
    Object? type = null,
    Object? extraData = null,
  }) {
    return _then(_$AigcEditingHistoryStateImpl(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
      maskPath: null == maskPath
          ? _value.maskPath
          : maskPath // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AigcEditingHistoryType,
      extraData: null == extraData
          ? _value._extraData
          : extraData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AigcEditingHistoryStateImpl extends _AigcEditingHistoryState {
  const _$AigcEditingHistoryStateImpl(
      {required this.fileId,
      required this.maskPath,
      required this.timestamp,
      required this.type,
      required final Map<String, dynamic> extraData})
      : _extraData = extraData,
        super._();

  factory _$AigcEditingHistoryStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AigcEditingHistoryStateImplFromJson(json);

  @override
  final String fileId;
  @override
  final String maskPath;
  @override
  final int timestamp;
  @override
  final AigcEditingHistoryType type;
  final Map<String, dynamic> _extraData;
  @override
  Map<String, dynamic> get extraData {
    if (_extraData is EqualUnmodifiableMapView) return _extraData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_extraData);
  }

  @override
  String toString() {
    return 'AigcEditingHistoryState(fileId: $fileId, maskPath: $maskPath, timestamp: $timestamp, type: $type, extraData: $extraData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AigcEditingHistoryStateImpl &&
            (identical(other.fileId, fileId) || other.fileId == fileId) &&
            (identical(other.maskPath, maskPath) ||
                other.maskPath == maskPath) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality()
                .equals(other._extraData, _extraData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fileId, maskPath, timestamp,
      type, const DeepCollectionEquality().hash(_extraData));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AigcEditingHistoryStateImplCopyWith<_$AigcEditingHistoryStateImpl>
      get copyWith => __$$AigcEditingHistoryStateImplCopyWithImpl<
          _$AigcEditingHistoryStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AigcEditingHistoryStateImplToJson(
      this,
    );
  }
}

abstract class _AigcEditingHistoryState extends AigcEditingHistoryState {
  const factory _AigcEditingHistoryState(
          {required final String fileId,
          required final String maskPath,
          required final int timestamp,
          required final AigcEditingHistoryType type,
          required final Map<String, dynamic> extraData}) =
      _$AigcEditingHistoryStateImpl;
  const _AigcEditingHistoryState._() : super._();

  factory _AigcEditingHistoryState.fromJson(Map<String, dynamic> json) =
      _$AigcEditingHistoryStateImpl.fromJson;

  @override
  String get fileId;
  @override
  String get maskPath;
  @override
  int get timestamp;
  @override
  AigcEditingHistoryType get type;
  @override
  Map<String, dynamic> get extraData;
  @override
  @JsonKey(ignore: true)
  _$$AigcEditingHistoryStateImplCopyWith<_$AigcEditingHistoryStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
