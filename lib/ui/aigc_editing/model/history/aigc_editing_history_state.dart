import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:turing_art/ui/aigc_editing/model/regional/aigc_regional_frame.dart';

part 'aigc_editing_history_state.freezed.dart';
part 'aigc_editing_history_state.g.dart';

enum AigcEditingHistoryType {
  brush,
  regional,
  reset,
}

/// 框选类型的历史记录状态
@freezed
class AigcEditingHistoryState with _$AigcEditingHistoryState {
  const factory AigcEditingHistoryState({
    required String fileId,
    required String maskPath,
    required int timestamp,
    required AigcEditingHistoryType type,
    required Map<String, dynamic> extraData,
  }) = _AigcEditingHistoryState;

  const AigcEditingHistoryState._();

  /// 从JSON字符串创建
  factory AigcEditingHistoryState.fromJson(Map<String, dynamic> json) =>
      _$AigcEditingHistoryStateFromJson(json);

  // 从AigcRegionalFrame创建一条历史记录
  factory AigcEditingHistoryState.fromAigcRegionalFrame({
    required String fileId,
    required String maskPath,
    required AigcRegionalFrame regionalFrame,
  }) {
    return AigcEditingHistoryState(
      fileId: fileId,
      maskPath: maskPath,
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: AigcEditingHistoryType.regional,
      extraData: {
        'regionalFrame': regionalFrame.toJson(),
      },
    );
  }

  // 创建一条涂抹历史记录
  factory AigcEditingHistoryState.fromAigcBrushHistoryState({
    required String fileId,
    required String maskPath,
  }) {
    return AigcEditingHistoryState(
      fileId: fileId,
      maskPath: maskPath,
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: AigcEditingHistoryType.brush,
      extraData: {},
    );
  }

  // 创建一条重置历史记录
  factory AigcEditingHistoryState.fromAigcResetHistoryState({
    required String fileId,
    required String maskPath,
  }) {
    return AigcEditingHistoryState(
      fileId: fileId,
      maskPath: maskPath,
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: AigcEditingHistoryType.reset,
      extraData: {},
    );
  }

  /// 获取时间戳的日期时间
  DateTime get dateTime => DateTime.fromMillisecondsSinceEpoch(timestamp);
}
