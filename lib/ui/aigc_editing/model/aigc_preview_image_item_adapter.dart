import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/utils/pg_log.dart';

/// Adapter for converting WorkspaceFile and MediaRepository data into AigcPreviewImageItem
class AigcPreviewImageItemAdapter {
  final MediaRepository _mediaRepository;

  AigcPreviewImageItemAdapter(this._mediaRepository);

  /// Convert a single WorkspaceFile to AigcPreviewImageItem
  AigcPreviewImageItem fromWorkspaceFile(WorkspaceFile file) {
    // Get resource paths from MediaRepository
    final thumbnailPath = _getResourcePath(
      file.workspaceId,
      file.fileId,
      MediaResourceType.minIcon,
    );

    final previewPath = _getResourcePath(
      file.workspaceId,
      file.fileId,
      MediaResourceType.previewResource,
    );

    final highQualityPath = _getResourcePath(
      file.workspaceId,
      file.fileId,
      MediaResourceType.largeResource,
    );

    final maskPath = _getResourcePath(
      file.workspaceId,
      file.fileId,
      MediaResourceType.mask,
    );

    // final interactiveMaskPath = _getResourcePath(
    //   file.workspaceId,
    //   file.fileId,
    //   MediaResourceType.interactiveMask,
    // );

    return AigcPreviewImageItem(
      fileId: file.fileId,
      originalPath: file.orgPath,
      thumbnailPath: thumbnailPath,
      previewPath: previewPath,
      highQualityPath: highQualityPath,
      maskPath: maskPath,
      // interactiveMaskPath: interactiveMaskPath,
      iconized: file.iconized,
    );
  }

  /// Get resource file path if it exists
  String? _getResourcePath(
    String workspaceId,
    String fileId,
    MediaResourceType resourceType,
  ) {
    try {
      final resourceFile = _mediaRepository.getFileResource(
        workspaceId,
        fileId,
        resourceType,
      );

      return resourceFile?.path;
    } catch (e) {
      // Log error but don't throw to avoid breaking the conversion
      PGLog.e('Failed to get resource path for $resourceType: $e');
      return null;
    }
  }

  /// Convert multiple WorkspaceFiles to AigcPreviewImageItems
  List<AigcPreviewImageItem> fromWorkspaceFiles(
    List<WorkspaceFile> files,
  ) {
    return files.map((file) => fromWorkspaceFile(file)).toList();
  }

  /// Refresh mask paths for existing AigcPreviewImageItem
  AigcPreviewImageItem refreshMaskPaths(
    AigcPreviewImageItem item,
    String workspaceId,
  ) {
    final newMaskPath = _getResourcePath(
      workspaceId,
      item.fileId,
      MediaResourceType.mask,
    );

    // final newInteractiveMaskPath = _getResourcePath(
    //   workspaceId,
    //   item.fileId,
    //   MediaResourceType.interactiveMask,
    // );

    return AigcPreviewImageItem(
      fileId: item.fileId,
      originalPath: item.originalPath,
      thumbnailPath: item.thumbnailPath,
      previewPath: item.previewPath,
      highQualityPath: item.highQualityPath,
      maskPath: newMaskPath,
      // interactiveMaskPath: newInteractiveMaskPath,
      iconized: item.iconized,
    );
  }
}
