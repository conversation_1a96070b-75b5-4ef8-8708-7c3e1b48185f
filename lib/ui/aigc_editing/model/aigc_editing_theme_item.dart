/// 主题项数据模型
class ThemeItem {
  /// 主题名称
  final String name;

  /// 主题图片URL
  final String imageUrl;

  /// 创意项列表
  final List<CreativeItem> creativeItems;

  ThemeItem({
    required this.name,
    required this.imageUrl,
    required this.creativeItems,
  });
}

/// 创意项数据模型
class CreativeItem {
  /// 创意图片URL
  final String imageUrl;

  /// 创意ID
  final String id;

  CreativeItem({
    required this.imageUrl,
    required this.id,
  });
}
