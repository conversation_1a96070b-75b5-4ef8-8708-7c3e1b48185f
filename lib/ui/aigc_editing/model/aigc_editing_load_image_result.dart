import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/services.dart';

/// 加载图片结果类
class ImageLoadResult {
  /// 加载是否成功
  final bool success;

  /// 加载的图片
  final ui.Image? image;

  /// 错误信息
  final String? error;

  const ImageLoadResult({
    required this.success,
    this.image,
    this.error,
  });

  /// 创建成功结果
  factory ImageLoadResult.success(ui.Image image) {
    return ImageLoadResult(
      success: true,
      image: image,
    );
  }

  /// 创建失败结果
  factory ImageLoadResult.failure(String error) {
    return ImageLoadResult(
      success: false,
      error: error,
    );
  }

  /// 加载本地图片
  static Future<ImageLoadResult> loadLocalImage(String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        return ImageLoadResult.failure('本地文件不存在: $filePath');
      }

      final Uint8List bytes = await file.readAsBytes();
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();

      return ImageLoadResult.success(frameInfo.image);
    } catch (e) {
      return ImageLoadResult.failure('本地图片加载失败: ${e.toString()}');
    }
  }

  /// 加载资源图片
  static Future<ImageLoadResult> loadAssetImage(String assetPath) async {
    try {
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();

      return ImageLoadResult.success(frameInfo.image);
    } catch (e) {
      return ImageLoadResult.failure('资源图片加载失败: ${e.toString()}');
    }
  }
}
