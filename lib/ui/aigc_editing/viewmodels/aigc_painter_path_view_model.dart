import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/aigc_editing/model/handler/aigc_regional_logic_frame.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_painter_path_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_regional_frame_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/painter/aigc_path_painter.dart';

/// AIGC绘制路径ViewModel
///
/// 负责路径绘制相关的业务逻辑，不继承ChangeNotifier
/// 通过Provider进行数据驱动和UI刷新
class AigcPainterPathViewModel {
  /// Provider实例
  final AigcPainterPathProvider _provider;
  final AigcEditingControlProvider _controlProvider;
  final AigcRegionalFrameProvider _regionalFrameProvider;
  AigcRegionalLogicFrame? _currentLogicFrame;

  AigcRegionalLogicFrame? get currentLogicFrame => _currentLogicFrame;

  /// 构造函数
  AigcPainterPathViewModel({
    required AigcPainterPathProvider provider,
    required AigcEditingControlProvider controlProvider,
    required AigcRegionalFrameProvider regionalFrameProvider,
  })  : _provider = provider,
        _controlProvider = controlProvider,
        _regionalFrameProvider = regionalFrameProvider;

  /// 获取绘制批次队列
  List<DrawingBatch> get drawingBatches => _provider.drawingBatches;

  /// 获取当前批次
  DrawingBatch? get currentBatch => _provider.currentBatch;

  /// 获取所有点
  List<CanvasPoint> get points => _provider.points;

  /// 获取当前画布模式
  CanvasMode get mode => _provider.mode;

  /// 获取当前画布操作状态
  CanvasActionState get actionState => _provider.actionState;

  /// 获取是否显示用户绘制的路径
  bool get showUserPaths => _provider.showUserPaths;

  /// 获取画笔颜色
  Color get brushColor => _provider.brushColor;

  /// 设置画布模式
  void setMode(CanvasMode mode) {
    _provider.setMode(mode);
  }

  /// 设置画笔颜色
  void setBrushColor(Color color) {
    _provider.setBrushColor(color);
  }

  /// 设置是否显示用户绘制的路径
  void setShowUserPaths({required bool show}) {
    _provider.setShowUserPaths(show: show);
  }

  /// 开始绘制
  void startDrawing(Offset position, double currentScale) {
    // 检查点是否包含NaN值
    if (position.dx.isNaN ||
        position.dy.isNaN ||
        position.dx.isInfinite ||
        position.dy.isInfinite) {
      return;
    }

    // 确保状态为无操作状态
    if (_provider.actionState != CanvasActionState.none) {
      return;
    }

    // 开始新的绘制批次
    _provider.startDrawingBatch(isErase: false, currentScale: currentScale);

    // 将当前坐标转换为基于原始尺寸的坐标
    // 由于容器已经应用了缩放，我们需要逆转换回原始坐标系
    final normalizedPosition = _normalizePosition(position, currentScale);

    final canvasPoint = CanvasPoint(
      offset: normalizedPosition,
      isErase: false,
      size: _controlProvider.brushSize,
      pathId: _provider.getNextPathId(),
    );

    _provider.addPointToBatch(canvasPoint);
  }

  /// 继续绘制

  void continueDrawing(Offset position) {
    // 检查点是否包含NaN值
    if (position.dx.isNaN ||
        position.dy.isNaN ||
        position.dx.isInfinite ||
        position.dy.isInfinite) {
      return;
    }

    // 确保在绘制状态且有当前批次
    if (_provider.actionState != CanvasActionState.drawing ||
        _provider.currentBatch == null) {
      return;
    }

    // 将当前坐标转换为基于原始尺寸的坐标
    final normalizedPosition =
        _normalizePosition(position, _provider.currentBatch!.scaleAtCreation);

    // 添加新的点到当前批次
    final canvasPoint = CanvasPoint(
      offset: normalizedPosition,
      isErase: false,
      size: _controlProvider.brushSize,
      pathId: _provider.currentBatch!.batchId,
    );

    _provider.addPointToBatch(canvasPoint);
  }

  /// 标准化位置坐标
  /// 将当前容器坐标转换为基于原始尺寸的坐标
  Offset _normalizePosition(Offset position, double scale) {
    // 由于容器已经应用了缩放，输入的position是缩放后的坐标
    // 我们需要将其转换回原始坐标系
    return Offset(
      position.dx / scale,
      position.dy / scale,
    );
  }

  /// 结束绘制
  void endDrawing() {
    _provider.finishCurrentBatch();
  }

  /// 开始擦除
  void startErasing(Offset position, double currentScale) {
    // 检查点是否包含NaN值
    if (position.dx.isNaN ||
        position.dy.isNaN ||
        position.dx.isInfinite ||
        position.dy.isInfinite) {
      return;
    }

    // 确保状态为无操作状态
    if (_provider.actionState != CanvasActionState.none) {
      return;
    }

    // 开始新的擦除批次
    _provider.startDrawingBatch(isErase: true, currentScale: currentScale);

    // 将当前坐标转换为基于原始尺寸的坐标
    final normalizedPosition = _normalizePosition(position, currentScale);

    // 添加第一个擦除点到批次
    final canvasPoint = CanvasPoint(
      offset: normalizedPosition,
      isErase: true,
      size: _controlProvider.brushSize,
      pathId: _provider.getNextPathId(),
    );

    _provider.addPointToBatch(canvasPoint);
  }

  /// 继续擦除
  void continueErasing(Offset position) {
    // 检查点是否包含NaN值
    if (position.dx.isNaN ||
        position.dy.isNaN ||
        position.dx.isInfinite ||
        position.dy.isInfinite) {
      return;
    }

    // 确保在擦除状态且有当前批次
    if (_provider.actionState != CanvasActionState.erasing ||
        _provider.currentBatch == null) {
      return;
    }

    // 将当前坐标转换为基于原始尺寸的坐标
    final normalizedPosition =
        _normalizePosition(position, _provider.currentBatch!.scaleAtCreation);

    // 添加新的擦除点到当前批次
    final canvasPoint = CanvasPoint(
      offset: normalizedPosition,
      isErase: true,
      size: _controlProvider.brushSize,
      pathId: _provider.currentBatch!.batchId,
    );

    _provider.addPointToBatch(canvasPoint);
  }

  /// 结束擦除
  void endErasing() {
    _provider.finishCurrentBatch();
  }

  /// 取消当前绘制/擦除
  void cancelCurrentOperation() {
    _provider.cancelCurrentBatch();
  }

  /// 清除所有绘制内容
  void clearAllDrawing() {
    _provider.clearAllBatches();
  }

  /// 撤销最后一个绘制批次
  void undoLastBatch() {
    _provider.undoLastBatch();
  }

  /// 添加绘制点到当前批次
  void addPoint(Offset point, {required bool isErase}) {
    // 检查点是否包含NaN值
    if (point.dx.isNaN ||
        point.dy.isNaN ||
        point.dx.isInfinite ||
        point.dy.isInfinite) {
      return;
    }

    // 检查是否有当前批次
    if (_provider.currentBatch == null) {
      return;
    }

    // 检查点是否在背景图片范围内（相对坐标应该在0-1范围内）
    if (point.dx < 0 || point.dx > 1 || point.dy < 0 || point.dy > 1) {
      return;
    }

    // 创建新的点，使用固定画笔大小（不受缩放影响）
    final canvasPoint = CanvasPoint(
      offset: point,
      isErase: isErase,
      size: _controlProvider.brushSize,
      pathId: _provider.currentBatch!.batchId,
    );

    // 添加到当前批次
    _provider.addPointToBatch(canvasPoint);
  }

  /// 检查是否有任何绘制内容
  bool get hasAnyDrawing => _provider.hasAnyDrawing;

  /// 获取总的绘制点数
  int get totalPointCount => _provider.totalPointCount;

  /// 获取所有有效的绘制批次
  List<DrawingBatch> getValidBatches() => _provider.getValidBatches();

  /// 获取指定区域内的绘制批次
  List<DrawingBatch> getBatchesInRect(Rect rect) =>
      _provider.getBatchesInRect(rect);

  /// 获取控制提供者
  AigcEditingControlProvider get controlProvider => _controlProvider;

  /// 显示用户路径
  void showUserPathsAgain() {
    _provider.setShowUserPaths(show: true);
  }

  /// 隐藏用户路径
  void hideUserPaths() {
    _provider.setShowUserPaths(show: false);
  }

  /// 获取相对绘制画笔大小
  double getRelativeDrawImageBrushSize(double brushSize, double scale) {
    return brushSize / scale;
  }

  /// 获取触摸事件处理器
  void updateTouchLogicFrame(Offset position) {
    _currentLogicFrame = _regionalFrameProvider.fetchTouchLogicFrame(position);
  }

  /// 生成透明背景的路径图片
  Future<ui.Image?> generateTransparentPathImage(
      AigcPathPainter painter, Size size, double canvasScale) async {
    // 检查是否需要根据currentTouchHandler的rect进行截取
    if (_currentLogicFrame == null) {
      return await _generateFullPathImage(painter, size);
    } else {
      // return await _generateRegionalPathImage(painter, size);
      return await _generateTempRegionalPathImage(painter, size,
          _currentLogicFrame!.getRelativeRect(canvasScale), canvasScale);
    }
  }

  /// 生成完整路径图片
  Future<ui.Image> _generateFullPathImage(
      AigcPathPainter painter, Size size) async {
    // 如果currentTouchHandler为空，保留现有逻辑
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // 绘制透明背景
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.transparent,
    );

    // 绘制路径
    painter.paint(canvas, size);

    // 完成录制并转换为图像
    final ui.Picture picture = recorder.endRecording();
    return await picture.toImage(size.width.toInt(), size.height.toInt());
  }

  /// 生成临时区域路径图片
  Future<ui.Image?> _generateTempRegionalPathImage(
      AigcPathPainter painter, Size size, Rect rect, double canvasScale) async {
    // 检查是否有绘制路径
    if (painter.drawingBatches.isEmpty && painter.currentBatch == null ||
        _currentLogicFrame == null) {
      return null;
    }

    // 收集所有需要检查的绘制批次
    final List<DrawingBatch> allBatches = [];
    allBatches.addAll(painter.drawingBatches);
    if (painter.currentBatch != null) {
      allBatches.add(painter.currentBatch!);
    }

    // 检查是否有任何路径点与区域框相交
    bool hasIntersection = false;
    for (final batch in allBatches) {
      for (final point in batch.points) {
        final absolutePoint = point.offset;
        // brushSize为直径，计算半径
        final double radius = point.size / canvasScale / 2.0;
        final Rect circleRect =
            Rect.fromCircle(center: absolutePoint, radius: radius);
        if (rect.overlaps(circleRect)) {
          hasIntersection = true;
          break;
        }
      }
      if (hasIntersection) {
        break;
      }
    }

    // 如果没有交集，返回 null
    if (!hasIntersection) {
      return null;
    }

    // 确保裁剪区域在图片范围内
    final Rect clampedRect = Rect.fromLTWH(
      rect.left.clamp(0.0, size.width),
      rect.top.clamp(0.0, size.height),
      (rect.width).clamp(0.0, size.width - rect.left.clamp(0.0, size.width)),
      (rect.height).clamp(0.0, size.height - rect.top.clamp(0.0, size.height)),
    );

    // 如果裁剪区域无效，返回 null
    if (clampedRect.width <= 0 || clampedRect.height <= 0) {
      return null;
    }

    // 先生成完整的图片
    final ui.PictureRecorder fullRecorder = ui.PictureRecorder();
    final Canvas fullCanvas = Canvas(fullRecorder);

    // 绘制透明背景
    fullCanvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.transparent,
    );

    // 绘制路径
    painter.paint(fullCanvas, size);

    // 完成录制并转换为完整图像
    final ui.Picture fullPicture = fullRecorder.endRecording();
    final ui.Image fullImage =
        await fullPicture.toImage(size.width.toInt(), size.height.toInt());

    // 创建裁剪后的图片
    final ui.PictureRecorder cropRecorder = ui.PictureRecorder();
    final Canvas cropCanvas = Canvas(cropRecorder);

    // 绘制透明背景（裁剪尺寸）
    cropCanvas.drawRect(
      Rect.fromLTWH(0, 0, clampedRect.width, clampedRect.height),
      Paint()..color = Colors.transparent,
    );

    // 将完整图片的指定区域绘制到裁剪画布上
    cropCanvas.drawImageRect(
      fullImage,
      clampedRect, // 源区域
      Rect.fromLTWH(0, 0, clampedRect.width, clampedRect.height), // 目标区域
      Paint(),
    );

    // 完成录制并转换为裁剪后的图像
    final ui.Picture cropPicture = cropRecorder.endRecording();
    final ui.Image croppedImage = await cropPicture.toImage(
      clampedRect.width.toInt(),
      clampedRect.height.toInt(),
    );

    // 释放完整图片资源
    fullImage.dispose();

    return croppedImage;
  }

  // /// 生成区域路径图片
  // Future<ui.Image> _generateRegionalPathImage(
  //     AigcPathPainter painter, Size size) async {
  //   // 如果currentTouchHandler不为空，根据其rect范围进行截取
  //   final Rect cropRect =
  //       _currentLogicFrame!.getRelativeRect(_currentLogicFrame!.scale);

  //   // 确保裁剪区域在图片范围内
  //   final Rect clampedRect = Rect.fromLTWH(
  //     cropRect.left.clamp(0.0, size.width),
  //     cropRect.top.clamp(0.0, size.height),
  //     (cropRect.width)
  //         .clamp(0.0, size.width - cropRect.left.clamp(0.0, size.width)),
  //     (cropRect.height)
  //         .clamp(0.0, size.height - cropRect.top.clamp(0.0, size.height)),
  //   );

  //   // 如果裁剪区域无效，返回1x1的透明图片
  //   if (clampedRect.width <= 0 || clampedRect.height <= 0) {
  //     final ui.PictureRecorder recorder = ui.PictureRecorder();
  //     final Canvas canvas = Canvas(recorder);
  //     canvas.drawRect(
  //       const Rect.fromLTWH(0, 0, 1, 1),
  //       Paint()..color = Colors.transparent,
  //     );
  //     final ui.Picture picture = recorder.endRecording();
  //     return await picture.toImage(1, 1);
  //   }

  //   // 先生成完整的图片
  //   final ui.PictureRecorder fullRecorder = ui.PictureRecorder();
  //   final Canvas fullCanvas = Canvas(fullRecorder);

  //   // 绘制透明背景
  //   fullCanvas.drawRect(
  //     Rect.fromLTWH(0, 0, size.width, size.height),
  //     Paint()..color = Colors.transparent,
  //   );

  //   // 绘制路径
  //   painter.paint(fullCanvas, size);

  //   // 完成录制并转换为完整图像
  //   final ui.Picture fullPicture = fullRecorder.endRecording();
  //   final ui.Image fullImage =
  //       await fullPicture.toImage(size.width.toInt(), size.height.toInt());

  //   // 创建裁剪后的图片
  //   final ui.PictureRecorder cropRecorder = ui.PictureRecorder();
  //   final Canvas cropCanvas = Canvas(cropRecorder);

  //   // 绘制透明背景（裁剪尺寸）
  //   cropCanvas.drawRect(
  //     Rect.fromLTWH(0, 0, clampedRect.width, clampedRect.height),
  //     Paint()..color = Colors.transparent,
  //   );

  //   // 将完整图片的指定区域绘制到裁剪画布上
  //   cropCanvas.drawImageRect(
  //     fullImage,
  //     clampedRect, // 源区域
  //     Rect.fromLTWH(0, 0, clampedRect.width, clampedRect.height), // 目标区域
  //     Paint(),
  //   );

  //   // 完成录制并转换为裁剪后的图像
  //   final ui.Picture cropPicture = cropRecorder.endRecording();
  //   final ui.Image croppedImage = await cropPicture.toImage(
  //     clampedRect.width.toInt(),
  //     clampedRect.height.toInt(),
  //   );

  //   // 释放完整图片资源
  //   fullImage.dispose();

  //   return croppedImage;
  // }

  /// 将图片转换为字节数据
  Future<Uint8List?> convertImageToBytes(ui.Image image) async {
    try {
      final byteData =
          await image.toByteData(format: ui.ImageByteFormat.rawRgba);
      if (byteData == null) {
        return null;
      }

      final bytes = byteData.buffer.asUint8List();
      return bytes;
    } catch (e) {
      debugPrint('转换图片时出错: $e');
      return null;
    }
  }
}
