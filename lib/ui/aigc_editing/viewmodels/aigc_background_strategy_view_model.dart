import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_editing_load_image_result.dart';
import 'package:turing_art/ui/aigc_editing/strategies/aigc_background_strategy.dart';

/// 背景策略视图模型
class AigcBackgroundStrategyViewModel {
  AigcBackgroundStrategy _backgroundStrategy =
      SolidColorBackgroundStrategy(Colors.transparent);

  /// 背景颜色
  Color _backgroundColor = Colors.transparent;

  /// 缓存上一次的背景策略类型
  Type? _lastStrategyType;

  /// 缓存上一次的背景颜色（用于SolidColorBackgroundStrategy）
  Color? _lastBackgroundColor;

  /// 缓存上一次的背景图片（用于ImageBackgroundStrategy）
  ui.Image? _lastBackgroundImage;

  /// 背景策略图片
  ui.Image? _backgroundStrategyImage;

  /// 预加载的格子背景图片（同步使用）
  static ui.Image? _preloadedGridImage;

  AigcBackgroundStrategyViewModel(Color color) {
    setBackgroundColor(color);
  }

  /// 预加载格子背景图片（静态方法，应用启动时调用）
  static Future<void> preloadGridImage() async {
    if (_preloadedGridImage == null) {
      final result = await ImageLoadResult.loadAssetImage(
          'assets/icons/icon_canvas_tile.png');
      if (result.success) {
        _preloadedGridImage = result.image;
      }
    }
  }

  AigcBackgroundStrategy get backgroundStrategy => _backgroundStrategy;

  /// 设置背景图片
  set backgroundStrategyImage(ui.Image? image) {
    if (_backgroundStrategyImage != image) {
      _backgroundStrategyImage = image;
      if (_backgroundStrategyImage != null) {
        _backgroundStrategy = ImageBackgroundStrategy(_backgroundStrategyImage!,
            fit: BoxFit.cover, mode: ImageBackgroundMode.tile);
      }
    }
  }

  /// 更新背景策略
  void _updateBackgroundStrategy() {
    if (_backgroundColor != Colors.transparent) {
      _backgroundStrategy = SolidColorBackgroundStrategy(_backgroundColor);
    } else if (_backgroundStrategyImage != null) {
      _backgroundStrategy = ImageBackgroundStrategy(_backgroundStrategyImage!,
          fit: BoxFit.cover, mode: ImageBackgroundMode.tile);
    } else {
      _loadDefaultBackgroundImageAsync();
    }
  }

  /// 强制更新背景策略（公开方法）
  void forceUpdateBackgroundStrategy() {
    _updateBackgroundStrategy();
    // 重置缓存信息，确保下次检查时能触发重绘
    _lastStrategyType = null;
    _lastBackgroundColor = null;
    _lastBackgroundImage = null;
  }

  /// 设置画布背景色
  void setBackgroundColor(Color color) {
    if (color != Colors.transparent) {
      _backgroundColor = color;
      _backgroundStrategyImage = null;
      _updateBackgroundStrategy();
    } else {
      _setDefaultBackgroundImageStrategy();
    }
  }

  /// 设置默认背景图片策略（同步版本）
  void _setDefaultBackgroundImageStrategy() {
    _backgroundColor = Colors.transparent;

    // 使用预加载的格子背景图片
    if (_preloadedGridImage != null) {
      backgroundStrategyImage = _preloadedGridImage;
    } else {
      // 如果预加载失败，异步加载
      _loadDefaultBackgroundImageAsync();
    }

    _updateBackgroundStrategy();
  }

  /// 异步加载默认背景图片策略（备用方案）
  void _loadDefaultBackgroundImageAsync() async {
    final result = await ImageLoadResult.loadAssetImage(
        'assets/icons/icon_canvas_tile.png');
    if (result.success) {
      backgroundStrategyImage = result.image;
      _updateBackgroundStrategy();
    }
  }

  /// 判断背景策略是否发生变化
  bool hasBackgroundStrategyChanged() {
    // 如果背景策略类型发生变化，需要重绘
    if (_lastStrategyType != _backgroundStrategy.runtimeType) {
      _updateLastStrategyInfo();
      return true;
    }

    // 根据不同的背景策略类型，检查参数是否发生变化
    if (_backgroundStrategy is SolidColorBackgroundStrategy) {
      final currentColor =
          (_backgroundStrategy as SolidColorBackgroundStrategy).backgroundColor;
      if (_lastBackgroundColor != currentColor) {
        _updateLastStrategyInfo();
        return true;
      }
    } else if (_backgroundStrategy is ImageBackgroundStrategy) {
      if (_lastBackgroundImage !=
          (_backgroundStrategy as ImageBackgroundStrategy).image) {
        _updateLastStrategyInfo();
        return true;
      }
    }

    return false;
  }

  /// 更新上一次的背景策略信息
  void _updateLastStrategyInfo() {
    _lastStrategyType = _backgroundStrategy.runtimeType;

    if (_backgroundStrategy is SolidColorBackgroundStrategy) {
      _lastBackgroundColor =
          (_backgroundStrategy as SolidColorBackgroundStrategy).backgroundColor;
    } else if (_backgroundStrategy is ImageBackgroundStrategy) {
      _lastBackgroundImage =
          (_backgroundStrategy as ImageBackgroundStrategy).image;
    }
  }
}
