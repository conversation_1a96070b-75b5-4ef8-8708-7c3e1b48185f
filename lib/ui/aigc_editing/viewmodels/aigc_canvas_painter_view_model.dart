import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_constant.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_canvas_painter_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';

/// AIGC画布绘制器ViewModel
///
/// 负责画布绘制器的业务逻辑，不继承ChangeNotifier
/// 通过Provider进行数据驱动和UI刷新
class AigcCanvasPainterViewModel {
  /// Provider实例
  final AigcCanvasPainterProvider _provider;
  final AigcEditingControlProvider _controlProvider;

  /// 上一次的焦点位置（用于拖拽检测）
  Offset? _lastFocalPoint;

  /// 上一次的缩放比例（用于缩放检测）
  double? _lastScale;

  /// 构造函数
  AigcCanvasPainterViewModel({
    required AigcCanvasPainterProvider provider,
    required AigcEditingControlProvider controlProvider,
  })  : _provider = provider,
        _controlProvider = controlProvider;

  /// 获取当前缩放比例
  double get scale => _provider.scale;

  /// 获取当前偏移量
  Offset get offset => _provider.offset;

  /// 获取是否正在拖拽
  bool get isDragging => _provider.isDragging;

  /// 获取是否正在缩放
  bool get isZooming => _provider.isZooming;

  /// 获取画布尺寸
  Size? get canvasSize => _provider.canvasSize;

  /// 获取内容尺寸
  Size? get contentSize => _provider.contentSize;

  /// 处理拖拽开始
  void handleDragStart(Offset position) {
    _provider.setDragging(isDragging: true);
    // 拖拽开始时不需要记录位置，只需要设置状态
  }

  /// 处理拖拽更新
  void handleDragUpdate(Offset delta) {
    if (_provider.isDragging) {
      // 应用增量偏移
      _provider.applyOffset(delta);
    }
  }

  /// 处理拖拽结束
  void handleDragEnd() {
    _provider.setDragging(isDragging: false);
  }

  /// 处理缩放开始
  void handleScaleStart(Offset focalPoint) {
    _provider.setZooming(isZooming: true);
    _lastFocalPoint = focalPoint;
    _lastScale = 1.0;
  }

  /// 处理缩放更新（同时处理拖拽和缩放）
  void handleScaleUpdate(double scale, Offset focalPoint) {
    if (!_provider.isZooming || _lastFocalPoint == null || _lastScale == null) {
      return;
    }

    // 如果缩放比例变化，执行缩放操作
    if ((scale - _lastScale!).abs() > 0.01) {
      // 标记用户手动缩放
      _provider.markUserManuallyScaled();

      final scaleFactor = scale / _lastScale!;
      _provider.applyScale(scaleFactor, focalPoint);
      _lastScale = scale;
    }
    // 如果焦点位置变化但缩放比例不变，执行拖拽操作
    else if ((focalPoint - _lastFocalPoint!).distance > 1.0) {
      final delta = focalPoint - _lastFocalPoint!;
      _provider.applyOffset(delta);
      _lastFocalPoint = focalPoint;
    }
  }

  /// 处理缩放结束
  void handleScaleEnd() {
    _provider.setZooming(isZooming: false);
    _lastFocalPoint = null;
    _lastScale = null;
  }

  /// 处理滚轮缩放
  void handleScrollZoom(double delta, Offset position) {
    // 将滚轮增量转换为缩放因子（交换缩放方向）
    final scaleFactor = delta > 0 ? 0.9 : 1.1;

    // 标记用户手动缩放
    _provider.markUserManuallyScaled();

    // 直接设置缩放比例，不改变偏移量（保持居中）
    final newScale = (_provider.scale * scaleFactor)
        .clamp(AigcConstant.minCanvasScale, AigcConstant.maxCanvasScale);
    _provider.setScale(newScale);
  }

  /// 设置画布尺寸
  void setCanvasSize(Size size) {
    // 使用智能重置版本，只有图片完全不可见时才重置位置
    _provider.setCanvasSizeWithSmartReset(size);
  }

  /// 设置内容尺寸（当背景图片加载完成后调用）
  void setContentSize(Size size) {
    _provider.setContentSize(size);
  }

  /// 重置变换到初始状态
  void resetTransform() {
    _provider.resetTransform();
  }

  /// 获取当前的变换矩阵
  Matrix4 getTransformMatrix() {
    return _provider.getTransformMatrix();
  }

  /// 检查点是否在内容区域内
  bool isPointInContent(Offset point) {
    return _provider.isPointInContent(point);
  }

  /// 将屏幕坐标转换为内容坐标
  Offset screenToContentCoordinate(Offset screenPoint) {
    if (_provider.contentSize == null) {
      return screenPoint;
    }

    // 考虑偏移和缩放的逆变换
    final transformedPoint = (screenPoint - _provider.offset) / _provider.scale;

    // 转换为相对坐标（0-1范围）
    final relativeX = transformedPoint.dx / _provider.contentSize!.width;
    final relativeY = transformedPoint.dy / _provider.contentSize!.height;

    return Offset(relativeX.clamp(0.0, 1.0), relativeY.clamp(0.0, 1.0));
  }

  /// 将内容坐标转换为屏幕坐标
  Offset contentToScreenCoordinate(Offset contentPoint) {
    if (_provider.contentSize == null) {
      return contentPoint;
    }

    // 从相对坐标（0-1范围）转换为实际像素坐标
    final pixelX = contentPoint.dx * _provider.contentSize!.width;
    final pixelY = contentPoint.dy * _provider.contentSize!.height;

    // 应用缩放和偏移变换
    return Offset(pixelX, pixelY) * _provider.scale + _provider.offset;
  }

  /// 计算图像显示区域
  Rect calculateImageDisplayRect() {
    if (_provider.contentSize == null) {
      return Rect.zero;
    }

    final scaledSize = _provider.contentSize! * _provider.scale;
    return Rect.fromLTWH(
      _provider.offset.dx,
      _provider.offset.dy,
      scaledSize.width,
      scaledSize.height,
    );
  }

  /// 获取控制提供者
  AigcEditingControlProvider get controlProvider => _controlProvider;
}
