import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_editing_load_image_result.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item_adapter.dart';
import 'package:turing_art/ui/aigc_editing/model/history/aigc_editing_history_state.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_history_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_preferred_mask_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_overlay_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_mask_acquisition_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_regional_frame_provider.dart';
import 'package:turing_art/ui/aigc_editing/strategies/aigc_background_strategy.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 画布状态事件类型
enum CanvasStateEventType {
  /// 背景图片开始加载
  imageLoadStart,

  /// 背景图片加载成功
  imageLoadSuccess,

  /// 背景图片加载失败
  imageLoadError,
}

/// 历史记录变化处理场景枚举
enum HistoryChangeScenario {
  /// 回退到第一步
  backToFirstStep,

  /// 历史记录变更
  directoryChanged,

  /// 历史记录数据未初始化
  dataInvalid,

  /// 与历史记录无关的行为
  noAction,
}

/// 画布状态事件数据
class CanvasStateEvent {
  final CanvasStateEventType type;
  final String? message;
  final dynamic data;

  const CanvasStateEvent({
    required this.type,
    this.message,
    this.data,
  });
}

/// 画布状态事件回调函数
typedef CanvasStateEventCallback = void Function(CanvasStateEvent event);

/// 画布视图模型
///
/// 遵循MVVM设计模式和策略模式，负责管理画布的数据和业务逻辑
/// 使用观察者模式（通过ChangeNotifier）通知UI状态变化
class AigcEditingCanvasViewModel extends ChangeNotifier {
  /// 背景图片
  String? _backgroundImageUrl;

  /// 画布背景色
  Color _backgroundColor = const Color(0xFF0D0D0D);

  /// 背景图片边界
  Rect? _backgroundImageBounds;

  /// 状态事件回调
  CanvasStateEventCallback? _onStateEvent;

  /// 缓存上一次的背景策略类型
  Type? _lastStrategyType;

  /// 缓存上一次的背景颜色（用于SolidColorBackgroundStrategy）
  Color? _lastBackgroundColor;

  /// 缓存上一次的背景图片（用于ImageBackgroundStrategy）
  ui.Image? _lastBackgroundImage;

  /// 背景策略图片
  ui.Image? _backgroundStrategyImage;

  /// 背景策略
  late AigcBackgroundStrategy _backgroundStrategy;

  /// 获取背景策略
  AigcBackgroundStrategy get backgroundStrategy => _backgroundStrategy;

  final AigcEditingImageProvider _imageProvider;
  final AigcEditingControlProvider _controlProvider;
  final AigcImageOverlayProvider _imageOverlayProvider;
  final AigcService? _processingService;
  final CurrentEditingProjectRepository _currentEditingProjectRepository;
  final AigcPreviewImageItemAdapter _aigcPreviewImageItemAdapter;
  final MediaRepository _mediaRepository;
  final FileManager _fileManager;
  final AigcEditingImagePreferredMaskProvider _preferredMaskProvider;
  final AigcEditingHistoryProvider _historyProvider;
  final AigcRegionalFrameProvider _regionalFrameProvider;
  final AigcMaskAcquisitionProvider _maskAcquisitionProvider;

  // 添加流订阅
  StreamSubscription<AigcTaskResultMessage>? _resultStreamSubscription;
  StreamSubscription<AigcEditingEventType>? _eventStreamSubscription;

  /// 设置背景图片URL
  set backgroundImageUrl(String? url) {
    if (_backgroundImageUrl != url) {
      // 清除蒙版绘制数据
      _imageProvider.mattingMaskImageDataInfo.clearIfNeeded(url);
      _imageProvider.mattingMaskImageDataInfo.imagePath = url;
      _backgroundImageUrl = url;
      _loadBackgroundImage();
      notifyListeners();
    }
  }

  /// 设置状态事件回调
  set onStateEvent(CanvasStateEventCallback? callback) {
    _onStateEvent = callback;
  }

  /// 触发状态事件
  void _triggerStateEvent(CanvasStateEvent event) {
    _onStateEvent?.call(event);
  }

  // 在构造函数中初始化背景策略
  AigcEditingCanvasViewModel._({
    required AigcEditingImageProvider imageProvider,
    required AigcEditingControlProvider controlProvider,
    required AigcImageOverlayProvider imageOverlayProvider,
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required AigcPreviewImageItemAdapter aigcPreviewImageItemAdapter,
    required MediaRepository mediaRepository,
    required FileManager fileManager,
    required AigcEditingImagePreferredMaskProvider preferredMaskProvider,
    required AigcEditingHistoryProvider historyProvider,
    required AigcRegionalFrameProvider regionalFrameProvider,
    required AigcMaskAcquisitionProvider maskAcquisitionProvider,
    AigcService? processingService,
  })  : _imageProvider = imageProvider,
        _controlProvider = controlProvider,
        _imageOverlayProvider = imageOverlayProvider,
        _currentEditingProjectRepository = currentEditingProjectRepository,
        _aigcPreviewImageItemAdapter = aigcPreviewImageItemAdapter,
        _mediaRepository = mediaRepository,
        _fileManager = fileManager,
        _preferredMaskProvider = preferredMaskProvider,
        _processingService = processingService,
        _historyProvider = historyProvider,
        _regionalFrameProvider = regionalFrameProvider,
        _maskAcquisitionProvider = maskAcquisitionProvider {
    _setupSharedDataListener();
    _setupMaskTaskListener();
    // 加载背景图片
    _loadBackgroundStrategyImage('assets/icons/icon_canvas_tile.png');
  }

  /// 工厂方法：通过context创建ViewModel
  /// UI组件调用此方法，不需要知道SharedDataProvider的存在
  static AigcEditingCanvasViewModel create(BuildContext context) {
    return AigcEditingCanvasViewModel._(
      imageProvider: context.read<AigcEditingImageProvider>(),
      controlProvider: context.read<AigcEditingControlProvider>(),
      imageOverlayProvider: context.read<AigcImageOverlayProvider>(),
      currentEditingProjectRepository:
          context.read<CurrentEditingProjectRepository>(),
      aigcPreviewImageItemAdapter: context.read<AigcPreviewImageItemAdapter>(),
      mediaRepository: context.read<MediaRepository>(),
      fileManager: FileManager(),
      processingService: context.read<AigcService>(),
      preferredMaskProvider:
          context.read<AigcEditingImagePreferredMaskProvider>(),
      historyProvider: context.read<AigcEditingHistoryProvider>(),
      regionalFrameProvider: context.read<AigcRegionalFrameProvider>(),
      maskAcquisitionProvider: context.read<AigcMaskAcquisitionProvider>(),
    );
  }

  /// 设置背景策略
  set backgroundStrategy(AigcBackgroundStrategy strategy) {
    if (_backgroundStrategy != strategy) {
      _backgroundStrategy = strategy;
      notifyListeners();
    }
  }

  /// 设置背景图片
  set backgroundStrategyImage(ui.Image? image) {
    if (_backgroundStrategyImage != image) {
      _backgroundStrategyImage = image;
      if (_backgroundStrategyImage != null) {
        _backgroundStrategy = ImageBackgroundStrategy(_backgroundStrategyImage!,
            fit: BoxFit.cover, mode: ImageBackgroundMode.tile);
      }
      notifyListeners();
    }
  }

  /// 更新背景策略
  void _updateBackgroundStrategy() {
    if (_backgroundColor != Colors.transparent) {
      _backgroundStrategy = SolidColorBackgroundStrategy(_backgroundColor);
    } else if (_backgroundStrategyImage != null) {
      _backgroundStrategy = ImageBackgroundStrategy(_backgroundStrategyImage!,
          fit: BoxFit.cover, mode: ImageBackgroundMode.tile);
    }
  }

  /// 设置画布背景色
  set backgroundColor(Color color) {
    if (_backgroundColor != color) {
      _backgroundColor = color;
      _backgroundStrategy = SolidColorBackgroundStrategy(_backgroundColor);
    }
  }

  /// 加载背景策略图片
  Future<void> _loadBackgroundStrategyImage(String assetPath) async {
    try {
      final result = await _loadImage(assetPath);
      if (result.success) {
        backgroundStrategyImage = result.image;
      }
    } catch (e) {
      PGLog.d('加载背景策略图片失败: $e');
    }
  }

  /// 获取背景图片边界
  Rect? get backgroundImageBounds => _backgroundImageBounds;

  /// 设置背景图片边界
  set backgroundImageBounds(Rect? value) {
    // 只比较值而不立即通知，避免在绘制过程中触发UI更新
    bool needUpdate = false;

    if (_backgroundImageBounds == null && value != null) {
      needUpdate = true;
    } else if (_backgroundImageBounds != null && value == null) {
      needUpdate = true;
    } else if (_backgroundImageBounds != null &&
        value != null &&
        (_backgroundImageBounds!.left != value.left ||
            _backgroundImageBounds!.top != value.top ||
            _backgroundImageBounds!.width != value.width ||
            _backgroundImageBounds!.height != value.height)) {
      needUpdate = true;
    }

    if (needUpdate) {
      _backgroundImageBounds = value;
      // 不要在这里调用notifyListeners，因为这可能在绘制过程中被调用
    }
  }

  /// 判断背景策略是否发生变化
  bool hasBackgroundStrategyChanged() {
    if (_lastStrategyType != _backgroundStrategy.runtimeType) {
      _updateLastStrategyInfo();
      return true;
    }

    // 根据不同的背景策略类型，检查参数是否发生变化
    if (_backgroundStrategy is SolidColorBackgroundStrategy) {
      final currentColor =
          (_backgroundStrategy as SolidColorBackgroundStrategy).backgroundColor;
      if (_lastBackgroundColor != currentColor) {
        _updateLastStrategyInfo();
        return true;
      }
    } else if (_backgroundStrategy is ImageBackgroundStrategy) {
      final strategy = _backgroundStrategy as ImageBackgroundStrategy;
      if (_lastBackgroundImage != strategy.image) {
        _updateLastStrategyInfo();
        return true;
      }
    }

    return false;
  }

  /// 更新上一次的背景策略信息
  void _updateLastStrategyInfo() {
    _lastStrategyType = _backgroundStrategy.runtimeType;

    if (_backgroundStrategy is SolidColorBackgroundStrategy) {
      _lastBackgroundColor =
          (_backgroundStrategy as SolidColorBackgroundStrategy).backgroundColor;
    } else if (_backgroundStrategy is ImageBackgroundStrategy) {
      _lastBackgroundImage =
          (_backgroundStrategy as ImageBackgroundStrategy).image;
    }
  }

  /// 设置监听共享数据Provider的变化
  void _setupSharedDataListener() {
    // 监听共享Provider的变化
    _imageProvider.addListener(_onUpdateBackgroundImage);
    _controlProvider.addListener(_onChangedBackgroundStrategy);
    _historyProvider.addListener(_onHistoryStateChanged);

    // 监听事件流
    _setupEventStreamListener();
  }

  /// 设置遮罩任务监听器
  void _setupMaskTaskListener() {
    if (_processingService == null) {
      return;
    }

    _resultStreamSubscription =
        _processingService.resultStream.listen((message) {
      // 从payload中获取任务类型
      final taskType = message.payload.taskType;
      if (taskType == AigcTaskType.mask) {
        _onMaskUpdated(message);
      } else if (taskType == AigcTaskType.interactiveMask) {
        _onInteractiveMaskUpdated(message);
      }

      PGDialog.dismiss();
    });
  }

  /// 设置事件流监听器
  void _setupEventStreamListener() {
    _eventStreamSubscription = _imageProvider.eventStream.listen((eventType) {
      switch (eventType) {
        case AigcEditingEventType.mattingMaskBrushStrokeUpdate:
          _onMattingMaskImageDataInfoChanged();
          break;
        case AigcEditingEventType.resetInteractiveMask:
          _onResetInteractiveMask();
          break;
        default:
          break;
      }
    });
  }

  /// 处理历史记录状态变化
  void _onHistoryStateChanged() {
    final correctMaskPath =
        _preferredMaskProvider.currentImageInteractiveMaskPath;
    final maskDataInfo = _imageProvider.mattingMaskImageDataInfo;

    // 判断处理场景
    final scenario =
        _determineHistoryChangeScenario(correctMaskPath, maskDataInfo);

    switch (scenario) {
      case HistoryChangeScenario.backToFirstStep:
        _handleBackToFirstStep(maskDataInfo);
        break;
      case HistoryChangeScenario.directoryChanged:
        _handleDirectoryChange(correctMaskPath!, maskDataInfo);
        break;
      case HistoryChangeScenario.dataInvalid:
        _handleInvalidData(correctMaskPath!, maskDataInfo);
        break;
      case HistoryChangeScenario.noAction:
        // 无关，无需处理
        break;
    }
  }

  /// 确定历史记录变化的处理场景
  HistoryChangeScenario _determineHistoryChangeScenario(String? correctMaskPath,
      AigcMattingMaskDarwPathImageDataInfo maskDataInfo) {
    final currentMaskFolderPath = maskDataInfo.currentFolderPath;

    // 1: 回退到第一步 (correctMaskPath为空且当前有文件夹路径)
    if (correctMaskPath == null && currentMaskFolderPath != null) {
      return HistoryChangeScenario.backToFirstStep;
    }

    // 2: 历史记录变更 (两个路径都不为空且目录不同)
    if (correctMaskPath != null &&
        currentMaskFolderPath != null &&
        path.dirname(correctMaskPath) != currentMaskFolderPath) {
      return HistoryChangeScenario.directoryChanged;
    }

    // 3: 历史记录数据未初始化 (数据无效且有正确路径)
    if (maskDataInfo.isInvalid && correctMaskPath != null) {
      return HistoryChangeScenario.dataInvalid;
    }

    return HistoryChangeScenario.noAction;
  }

  /// 处理回退到第一步的场景
  void _handleBackToFirstStep(
      AigcMattingMaskDarwPathImageDataInfo maskDataInfo) {
    maskDataInfo.clearNecessaryData();

    final mainMaskPath = _preferredMaskProvider.currentImageMaskPath;
    if (mainMaskPath != null) {
      maskDataInfo.currentFolderPath = path.dirname(mainMaskPath);
    }

    _imageOverlayProvider.loadMaskImage();
  }

  /// 处理历史记录变更的场景
  void _handleDirectoryChange(String correctMaskPath,
      AigcMattingMaskDarwPathImageDataInfo maskDataInfo) {
    final newDirectoryPath = path.dirname(correctMaskPath);

    maskDataInfo.loadFromDirectory(newDirectoryPath);
    maskDataInfo.currentFolderPath = newDirectoryPath;
    maskDataInfo.fullMaskBytes = null;

    _imageOverlayProvider.loadMaskImage();
  }

  /// 处理历史记录数据未初始化的场景
  void _handleInvalidData(String correctMaskPath,
      AigcMattingMaskDarwPathImageDataInfo maskDataInfo) {
    final directoryPath = path.dirname(correctMaskPath);

    maskDataInfo.loadFromDirectory(directoryPath);
    maskDataInfo.currentFolderPath = directoryPath;
    maskDataInfo.fullMaskBytes = null;
  }

  // 当缩略图等资源更新调用
  Future<void> _onMaskUpdated(AigcTaskResultMessage message) async {
    if (!message.success || message.resultData == null) {
      // 🎯 触发蒙版任务失败事件
      _triggerStateEvent(const CanvasStateEvent(
        type: CanvasStateEventType.imageLoadError,
        message: '主体区域生成失败',
      ));
      _controlProvider.setIsBackgroundMattingStatus(value: false);
      return;
    }

    final fileId = message.payload.fileId;

    WorkspaceFile? file =
        await _currentEditingProjectRepository.getFile(fileId);

    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      // 🎯 触发蒙版任务失败事件
      _triggerStateEvent(const CanvasStateEvent(
        type: CanvasStateEventType.imageLoadError,
        message: '主体区域生成失败: 当前工作空间为空',
      ));
      _controlProvider.setIsBackgroundMattingStatus(value: false);
      return;
    }

    // 更新资源存储
    final maskPath = (message.resultData as AigcMaskTaskResult).outputPath;
    if (maskPath == null || maskPath.isEmpty) {
      // 🎯 触发蒙版任务失败事件
      _triggerStateEvent(const CanvasStateEvent(
        type: CanvasStateEventType.imageLoadError,
        message: '主体区域生成失败: 输出路径为空',
      ));
      _controlProvider.setIsBackgroundMattingStatus(value: false);
      return;
    }

    final updatedFile = await _mediaRepository.addOrUpdateFileResource(
      workspace.workspaceId,
      fileId,
      MediaResourceType.mask,
      File(maskPath),
    );
    if (updatedFile == null) {
      // 🎯 触发蒙版任务失败事件
      _triggerStateEvent(const CanvasStateEvent(
        type: CanvasStateEventType.imageLoadError,
        message: '主体区域生成失败: 文件资源更新失败',
      ));
      _controlProvider.setIsBackgroundMattingStatus(value: false);
      return;
    }

    final updatedImage = _aigcPreviewImageItemAdapter.fromWorkspaceFile(file!);

    // 这将触发AigcThumbnailListViewModel中的监听器_onImageSelectionChanged
    // 该监听器会调用_updateImageSelection方法来同步更新其内部的_images列表
    if (updatedImage.fileId == _imageProvider.selectedImage?.fileId) {
      _imageProvider.selectedImage = updatedImage;
    }

    // 🎯 触发蒙版任务成功事件
    _triggerStateEvent(const CanvasStateEvent(
      type: CanvasStateEventType.imageLoadSuccess,
    ));
    _controlProvider.setIsBackgroundMattingStatus(value: true);
  }

  /// 处理交互式蒙版任务结果更新
  Future<void> _onInteractiveMaskUpdated(AigcTaskResultMessage message) async {
    if (!message.success || message.resultData == null) {
      return;
    }

    final fileId = message.payload.fileId;

    WorkspaceFile? file =
        await _currentEditingProjectRepository.getFile(fileId);

    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return;
    }

    final result = message.resultData as AigcInteractiveMaskTaskResult;
    // 更新蒙版资源存储
    final maskPath = result.outputPath;
    if (maskPath != null && maskPath.isNotEmpty) {
      _imageProvider.mattingMaskImageDataInfo.currentFolderPath = maskPath;

      // 更新图片信息
      final updatedImage =
          _aigcPreviewImageItemAdapter.fromWorkspaceFile(file!);

      // 更新蒙版数据信息对象
      final resultDataInfo = result.mattingMaskDataInfo;
      if (resultDataInfo.imagePath ==
          _imageProvider.mattingMaskImageDataInfo.imagePath) {
        if (resultDataInfo.isRegionalMode) {
          if (_imageProvider.mattingMaskImageDataInfo.currentStrokesBytes ==
              null) {
            _imageProvider.mattingMaskImageDataInfo.regionalImageBytes =
                resultDataInfo.regionalImageBytes;
            _imageProvider.mattingMaskImageDataInfo.fullMaskBytes =
                resultDataInfo.fullMaskBytes;
            _imageProvider.mattingMaskImageDataInfo.foreStrokesBytes = null;
            _imageProvider.mattingMaskImageDataInfo.backStrokesBytes = null;
            _imageProvider.mattingMaskImageDataInfo.previousMaskBytes =
                resultDataInfo.previousMaskBytes;

            final regionalFrame = _regionalFrameProvider.currentLogicFrame;
            if (regionalFrame != null) {
              // 框选历史记录
              final state = AigcEditingHistoryState.fromAigcRegionalFrame(
                fileId: fileId,
                maskPath: maskPath,
                regionalFrame: regionalFrame.regionalFrame,
              );
              await _historyProvider.addHistoryRecord(state: state);
            }
          } else {
            _imageProvider.mattingMaskImageDataInfo.fullMaskBytes =
                resultDataInfo.fullMaskBytes;
            _imageProvider.mattingMaskImageDataInfo.foreStrokesBytes =
                resultDataInfo.foreStrokesBytes;
            _imageProvider.mattingMaskImageDataInfo.backStrokesBytes =
                resultDataInfo.backStrokesBytes;
            _imageProvider.mattingMaskImageDataInfo.previousMaskBytes =
                resultDataInfo.previousMaskBytes;
            // 框选涂抹
            final state = AigcEditingHistoryState.fromAigcBrushHistoryState(
              fileId: fileId,
              maskPath: maskPath,
            );
            await _historyProvider.addHistoryRecord(state: state);
          }
        } else {
          _imageProvider.mattingMaskImageDataInfo.foreStrokesBytes =
              resultDataInfo.foreStrokesBytes;
          _imageProvider.mattingMaskImageDataInfo.backStrokesBytes =
              resultDataInfo.backStrokesBytes;
          _imageProvider.mattingMaskImageDataInfo.fullMaskBytes =
              resultDataInfo.fullMaskBytes;
          _imageProvider.mattingMaskImageDataInfo.regionalImageBytes = null;
          _imageProvider.mattingMaskImageDataInfo.previousMaskBytes = null;
          // 直接涂抹
          final state = AigcEditingHistoryState.fromAigcBrushHistoryState(
            fileId: fileId,
            maskPath: maskPath,
          );
          await _historyProvider.addHistoryRecord(state: state);
        }
        _imageProvider.mattingMaskImageDataInfo.currentStrokesBytes = null;
      }

      // 如果是当前选中的图片，更新选中图片信息
      if (updatedImage.fileId == _imageProvider.selectedImage?.fileId) {
        _imageProvider.selectedImage = updatedImage;
        final success = await _imageOverlayProvider.loadMaskImage();
        _controlProvider.setIsBackgroundMattingStatus(value: success);
      }
    }
  }

  void _onChangedBackgroundStrategy() {
    MaskBackgroundColor maskBackgroundColor =
        _controlProvider.maskBackgroundColor;

    if (_backgroundColor != maskBackgroundColor.color) {
      if (maskBackgroundColor == MaskBackgroundColor.transparent) {
        _loadBackgroundStrategyImage('assets/icons/icon_canvas_tile.png');
      } else {
        backgroundColor = maskBackgroundColor.color;
      }
      _backgroundColor = maskBackgroundColor.color;
      _updateBackgroundStrategy();
    }
  }

  /// 当共享数据发生变化时的处理
  void _onUpdateBackgroundImage() {
    backgroundImageUrl = _imageProvider.selectedImage?.previewPath;
    if (_imageProvider.selectedImage?.isDamaged == true) {
      _triggerStateEvent(const CanvasStateEvent(
        type: CanvasStateEventType.imageLoadError,
        message: '图片损坏，请重新选择',
      ));
    }
  }

  /// 当蒙版绘制数据更新时的处理
  void _onMattingMaskImageDataInfoChanged() {
    final mattingMaskDataInfo = _imageProvider.mattingMaskImageDataInfo;
    // 进行交互式抠图任务
    _submitInteractiveMaskTask(mattingMaskDataInfo);
  }

  /// 提交交互式蒙版任务
  Future<void> _submitInteractiveMaskTask(
      AigcMattingMaskDarwPathImageDataInfo mattingMaskDataInfo) async {
    final maskImage = _imageOverlayProvider.maskImage;
    if (mattingMaskDataInfo.currentStrokesBytes == null &&
        mattingMaskDataInfo.isRegionalMode == false) {
      return;
    }

    if (_processingService == null) {
      return;
    }

    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      return;
    }

    // 获取输入路径（原图路径）
    final inputPath = selectedImage.highQualityPath;
    if (inputPath == null || inputPath.isEmpty) {
      return;
    }

    // 生成输出路径
    final workspaceId =
        _currentEditingProjectRepository.currentWorkspace?.workspaceId;
    if (workspaceId == null) {
      return;
    }

    // 创建临时蒙版文件
    final temporaryMaskDir = _fileManager.getTempInteractiveMaskDirectory(
      workspaceId,
      selectedImage.fileId,
    );

    final outputPath = temporaryMaskDir.path;

    try {
      // 🎭 转换背景图片为Uint8List并存储到displayImageBytes（不能为空）
      final backgroundImage = _imageOverlayProvider.backgroundImage;
      Uint8List? previewImageDataBytes;
      if (backgroundImage != null &&
          mattingMaskDataInfo.displayImageBytes == null) {
        previewImageDataBytes =
            await _convertUiImageToUint8List(backgroundImage);
        if (previewImageDataBytes != null) {
          mattingMaskDataInfo.displayImageBytes = previewImageDataBytes;
        }
      }

      // 🎭 转换蒙版图片为Uint8List并存储到fullMaskBytes（可为空）
      if (maskImage != null && mattingMaskDataInfo.fullMaskBytes == null) {
        final fullMaskBytes = await _convertUiImageToUint8List(maskImage);
        if (fullMaskBytes != null) {
          mattingMaskDataInfo.fullMaskBytes = fullMaskBytes;
        }
      }

      // 重新构造mattingMaskDataInfo对象，在特定条件下将fullMaskBytes置为null
      AigcMattingMaskDarwPathImageDataInfo finalMattingMaskDataInfo;
      if (!_preferredMaskProvider.hasRegionalOperationSinceLastReset() &&
          mattingMaskDataInfo.isRegionalMode == true) {
        // 创建新的对象，将fullMaskBytes置为null
        finalMattingMaskDataInfo = AigcMattingMaskDarwPathImageDataInfo(
          regionalFrameDataInfo: mattingMaskDataInfo.regionalFrameDataInfo,
          imagePath: mattingMaskDataInfo.imagePath,
          foreStrokesBytes: mattingMaskDataInfo.foreStrokesBytes,
          backStrokesBytes: mattingMaskDataInfo.backStrokesBytes,
          displayImageBytes: mattingMaskDataInfo.displayImageBytes,
          previousMaskBytes: mattingMaskDataInfo.previousMaskBytes,
          isBrushMode: mattingMaskDataInfo.isBrushMode,
          currentStrokesBytes: mattingMaskDataInfo.currentStrokesBytes,
        )
          ..currentFolderPath = mattingMaskDataInfo.currentFolderPath
          ..regionalImageBytes = mattingMaskDataInfo.regionalImageBytes
          ..fullMaskBytes = null; // 明确将fullMaskBytes设置为null
      } else {
        // 使用原对象
        finalMattingMaskDataInfo = mattingMaskDataInfo;
      }

      // 检查是否有必要的数据
      /*if (finalMattingMaskDataInfo.currentStrokesBytes == null) {
        PGLog.d('Canvas: 没有笔触数据，跳过任务提交');
        return;
      } */

      PGDialog.showLoading();
      // 提交交互式蒙版任务 - 使用新的 API，直接传递 mattingMaskDataInfo 对象
      await _processingService.submitInteractiveMaskTask(
        inputPath: inputPath,
        outputPath: outputPath,
        fileId: selectedImage.fileId,
        mattingMaskDataInfo: finalMattingMaskDataInfo,
        executeNow: true,
      );
    } catch (e) {
      // PGDialog.dismiss();
      PGLog.d('Canvas: 提交交互式蒙版任务失败 - $e');
    }
  }

  /// 将ui.Image转换为Uint8List
  Future<Uint8List?> _convertUiImageToUint8List(ui.Image image) async {
    try {
      // 将ui.Image转换为PNG格式的字节数据
      final byteData =
          await image.toByteData(format: ui.ImageByteFormat.rawRgba);
      if (byteData == null) {
        return null;
      }

      final bytes = byteData.buffer.asUint8List();
      return bytes;
    } catch (e) {
      return null;
    }
  }

  /// 检查点是否在背景图片边界内
  bool isPointInImageBounds(Offset point, [Rect? currentBounds]) {
    // 使用传入的当前边界或存储的边界
    final bounds = currentBounds ?? _backgroundImageBounds;
    if (bounds == null) {
      return false;
    }
    return bounds.contains(point);
  }

  /// 加载背景图片
  Future<void> _loadBackgroundImage() async {
    if (_backgroundImageUrl == null) {
      notifyListeners();
      return;
    }

    notifyListeners();

    try {
      final result = await _loadImage(_backgroundImageUrl!);

      if (result.success) {
        _imageOverlayProvider.reloadBackgroundImageFromPreviewItem(
            isForce: true);

        // 🎭 背景图片加载成功后，自动提交遮罩任务
        if (_processingService != null &&
            _backgroundImageUrl != null &&
            _preferredMaskProvider.hasMask == false) {
          // 查找当前图片资源
          final selectedImage = _imageProvider.selectedImage;
          final workspaceId =
              _currentEditingProjectRepository.currentWorkspace?.workspaceId;
          if (workspaceId == null ||
              selectedImage == null ||
              selectedImage.readyToDisplay == false) {
            return;
          }
          final resourceFile = _mediaRepository.getResourceFilePath(
            workspaceId,
            selectedImage.fileId,
            MediaResourceType.mask,
          );
          final outputPath = resourceFile.path;
          _processingService.submitTask(
            inputPath: selectedImage.highQualityPath!,
            outputPath: outputPath,
            fileId: selectedImage.fileId,
            taskType: AigcTaskType.mask,
            executeNow: true, // 当前展示的图片立即执行
          );
          // 设置蒙版获取状态为加载中
          _maskAcquisitionProvider.setMaskAcquisitionStatus(
              selectedImage.fileId, MaskAcquisitionStatus.loading);

          // 🎯 触发背景图片开始加载事件
          _triggerStateEvent(const CanvasStateEvent(
            type: CanvasStateEventType.imageLoadStart,
            message: 'AI正在自动生成主体保护区域，请稍后...',
          ));
          _controlProvider.setIsBackgroundMattingStatus(value: false);
        }
      } else {
        // 🎯 触发背景图片加载失败事件
        _triggerStateEvent(const CanvasStateEvent(
          type: CanvasStateEventType.imageLoadError,
          message: '主体区域生成失败',
        ));
        _controlProvider.setIsBackgroundMattingStatus(value: false);
      }
    } catch (e) {
      // 🎯 触发背景图片加载异常事件
      _triggerStateEvent(const CanvasStateEvent(
        type: CanvasStateEventType.imageLoadError,
        message: '主体区域生成失败',
      ));
      _controlProvider.setIsBackgroundMattingStatus(value: false);
    } finally {
      notifyListeners();
    }
  }

  /// 加载图片，支持网络、资源和本地文件路径
  Future<ImageLoadResult> _loadImage(String url) async {
    try {
      if (url.startsWith('assets/')) {
        // 资源图片
        return await ImageLoadResult.loadAssetImage(url);
      } else {
        // 本地文件路径
        return await ImageLoadResult.loadLocalImage(url);
      }
    } catch (e) {
      return ImageLoadResult.failure('图片加载失败: ${e.toString()}');
    }
  }

  /// 重置交互式蒙版，
  Future<void> _onResetInteractiveMask() async {
    //TODO 这里需要新生成一步历史记录，并不需要删除蒙版

    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      return;
    }

    final workspaceId =
        _currentEditingProjectRepository.currentWorkspace?.workspaceId;
    if (workspaceId == null) {
      return;
    }

    try {
      // 1. 清除蒙版中间绘制数据
      _imageProvider.mattingMaskImageDataInfo.clearNecessaryData();
      // 2. 添加历史记录,这里将原始的抠图结果作为蒙版传递进历史记录
      final state = AigcEditingHistoryState.fromAigcResetHistoryState(
        fileId: selectedImage.fileId,
        maskPath:
            selectedImage.maskPath != null && selectedImage.maskPath!.isNotEmpty
                ? path.dirname(selectedImage.maskPath!)
                : "",
      );
      await _historyProvider.addHistoryRecord(state: state);

      // 3.刷新蒙版，必须等待历史记录添加完毕后调用
      _imageOverlayProvider.loadMaskImage();
    } catch (e) {
      PGLog.d('CanvasViewModel: 重置交互式蒙版时出错 - $e');
    }
  }

  @override
  void dispose() {
    super.dispose();
    try {
      // 取消流订阅
      _resultStreamSubscription?.cancel();
      _eventStreamSubscription?.cancel();

      // 移除对共享数据Provider的监听，避免内存泄漏
      _imageProvider.removeListener(_onUpdateBackgroundImage);
      _controlProvider.removeListener(_onChangedBackgroundStrategy);
      _historyProvider.removeListener(_onHistoryStateChanged);
    } catch (e) {
      PGLog.d('CanvasViewModel: 重置交互式蒙版时出错 - $e');
    }
  }
}
