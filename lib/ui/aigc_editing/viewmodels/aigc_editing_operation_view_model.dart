import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_editing_theme_item.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_editing_scene_view_model.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_theme_list_view_model.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AigcEditingOperation的ViewModel类
///
/// 遵循MVVM设计模式和工厂模式，负责管理编辑操作的数据和业务逻辑
class AigcEditingOperationViewModel extends ChangeNotifier {
  /// AIGC预设仓库
  final AigcPresetsRepository _aigcPresetsRepository;

  /// 账户仓库
  final AccountRepository _accountRepository;

  /// 主题列表数据
  final List<ThemeItem> _themeItems;

  /// 场景ViewModel - 用于状态共享
  final AigcEditingSceneViewModel _sceneViewModel;

  /// 创建主题回调
  final VoidCallback? onCreateThemeClicked;

  /// 主题列表ViewModel
  late AigcThemeListViewModel themeListViewModel;

  final AigcEditingThemeListProvider _themeListProvider;

  late AigcEditingMultiSelectProvider _multiSelectProvider;

  /// 按钮悬停状态
  bool _isButtonHovered = false;

  /// 当前运行中的操作Completer，用于取消操作
  Completer<(bool, String?)>? _currentOperation;

  /// 构造函数
  AigcEditingOperationViewModel({
    required AigcPresetsRepository aigcPresetsRepository,
    required AccountRepository accountRepository,
    required AigcEditingSceneViewModel sceneViewModel,
    required AigcEditingThemeListProvider themeListProvider,
    required AigcEditingImageProvider imageProvider,
    required AigcEditingMultiSelectProvider multiSelectProvider,
    List<ThemeItem> themeItems = const [],
    this.onCreateThemeClicked,
  })  : _aigcPresetsRepository = aigcPresetsRepository,
        _accountRepository = accountRepository,
        _themeItems = themeItems,
        _sceneViewModel = sceneViewModel,
        _themeListProvider = themeListProvider {
    // 初始化子ViewModel
    themeListViewModel = AigcThemeListViewModel(
      aigcPresetsRepository: _aigcPresetsRepository,
      themeListProvider: _themeListProvider,
      imageProvider: imageProvider,
      multiSelectProvider: multiSelectProvider,
      onCreateClick: onCreateThemeClicked,
    );
    // 监听场景ViewModel的状态变化
    _sceneViewModel.addListener(_onSceneViewModelChanged);
    _themeListProvider.addListener(_onThemeListProviderChanged);
    _multiSelectProvider = multiSelectProvider;
  }

  @override
  void dispose() {
    // 取消正在进行的操作
    _cancelCurrentOperation();
    // 移除监听器，避免内存泄漏
    _sceneViewModel.removeListener(_onSceneViewModelChanged);
    _themeListProvider.removeListener(_onThemeListProviderChanged);
    super.dispose();
  }

  /// 取消当前正在进行的操作
  void _cancelCurrentOperation() {
    if (_currentOperation != null && !_currentOperation!.isCompleted) {
      _currentOperation!.completeError('操作已取消');
      _currentOperation = null;
    }
  }

  /// 公开的取消操作方法，供外部调用
  void cancelCurrentOperation() {
    _cancelCurrentOperation();
  }

  // 场景ViewModel状态变化的回调
  void _onSceneViewModelChanged() {
    // 当场景ViewModel状态变化时，通知监听者
    notifyListeners();
  }

  void _onThemeListProviderChanged() {
    // 当主题列表ViewModel状态变化时，通知监听者
    isButtonHovered = false;
    notifyListeners();
  }

  /// 获取主题列表
  List<ThemeItem> get themeItems => _themeItems;

  /// 获取当前处理状态 - 从场景ViewModel获取
  ProcessState get processState => _sceneViewModel.processState;

  /// 获取消耗积分值 - 从场景ViewModel获取（目前默认3张效果图）
  int getCostValue() {
    // 多选模式下，每个样本的积分 乘以 选中的图片数量
    if (_multiSelectProvider.isMultiSelectMode &&
        _multiSelectProvider.selectedImageCount > 0) {
      return _sceneViewModel.allCostValue *
          _multiSelectProvider.selectedImageCount;
    }
    // 单选模式下，直接返回场景ViewModel的积分
    return _sceneViewModel.allCostValue;
  }

  /// 获取按钮悬停状态
  bool get isButtonHovered => _isButtonHovered;

  /// 设置按钮悬停状态
  set isButtonHovered(bool value) {
    if (_isButtonHovered != value) {
      _isButtonHovered = value;
      notifyListeners();
    }
  }

  /// 处理操作按钮点击
  Future<(bool, String?)> handleOperationButtonClicked() async {
    // 取消之前的操作
    _cancelCurrentOperation();

    // 创建新的操作Completer
    _currentOperation = Completer<(bool, String?)>();

    // 执行异步操作
    _performOperation();

    // 返回操作结果
    return await _currentOperation!.future;
  }

  /// 执行实际的异步操作
  void _performOperation() async {
    try {
      // 调用场景ViewModel的createSample方法
      final result = await _sceneViewModel.createSample(
        isOriginal: themeListViewModel.isSelectedInternalPresetModel(),
      );
      if (result.$1 == true) {
        // 操作成功后刷新账户信息（不等待，避免影响UI流程）
        sampleSuccessRefreshAccount();
      }

      // 如果操作未被取消，完成结果
      if (_currentOperation != null && !_currentOperation!.isCompleted) {
        _currentOperation!.complete(result);
        _currentOperation = null;
      }
    } catch (e) {
      // 如果操作未被取消，完成错误
      if (_currentOperation != null && !_currentOperation!.isCompleted) {
        _currentOperation!.completeError(e);
        _currentOperation = null;
      }
    }
  }

  void sampleSuccessRefreshAccount() {
    _accountRepository.refreshAllAccount().catchError((e) {
      // 刷新失败不影响主流程，只打印日志
      PGLog.e('账户信息刷新失败: $e');
    });
  }
}
