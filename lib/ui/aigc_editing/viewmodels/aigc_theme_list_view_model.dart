import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.internal.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AigcThemeList的ViewModel类
///
/// 遵循MVVM设计模式，负责管理主题列表的数据和业务逻辑
class AigcThemeListViewModel extends ChangeNotifier {
  int _index = -1;

  final AigcPresetsRepository _aigcPresetsRepository;

  final AigcEditingThemeListProvider _themeListProvider;

  final AigcEditingImageProvider _imageProvider;

  final AigcEditingMultiSelectProvider _multiSelectProvider;

  /// 主题数据列表
  List<AigcPresetsModel> _themeItems = [];

  /// 当前选中的主题ID
  String? _selectedThemeId;

  /// 每个主题选中的创意索引（使用主题ID作为key）
  final Map<String, int> _selectedCreativeIndices = {};

  /// 是否在显示子列表
  bool _showingCreativeList = false;

  /// 是否正在加载
  bool _isLoading = false;

  /// 错误信息
  String? _errorMessage;

  /// 当前选中的图片的文件ID
  String? _currentImageSourceFileId;

  /// 是否切换了图片源文件ID
  bool _isChangeImageSoucreFileId = false;

  /// 是否切换了图片源文件ID,读取后重置
  bool get isChangeImageSoucreFileId {
    if (_isChangeImageSoucreFileId) {
      _isChangeImageSoucreFileId = false;
      return true;
    }
    return false;
  }

  /// Stream订阅
  StreamSubscription<List<AigcPcPresetsDetailResponse>>? _presetsSubscription;

  /// 主题变更回调
  Function(AigcPresetsModel)? onThemeChanged;

  /// 创建主题回调
  Function()? onCreateClick;

  /// 工厂方法：通过context创建ViewModel
  /// UI组件调用此方法，不需要知道SharedDataProvider的存在
  static AigcThemeListViewModel create(
      AigcEditingImageProvider imageProvider,
      AigcEditingThemeListProvider themeListProvider,
      AigcPresetsRepository aigcPresetsRepository,
      AigcEditingMultiSelectProvider multiSelectProvider,
      Function(AigcPresetsModel)? onThemeChanged,
      Function()? onCreateClick,
      int? index) {
    return AigcThemeListViewModel(
      aigcPresetsRepository: aigcPresetsRepository,
      imageProvider: imageProvider,
      themeListProvider: themeListProvider,
      multiSelectProvider: multiSelectProvider,
      onThemeChanged: onThemeChanged,
      onCreateClick: onCreateClick,
      index: index,
    );
  }

  /// 构造函数
  AigcThemeListViewModel({
    required AigcPresetsRepository aigcPresetsRepository,
    required AigcEditingImageProvider imageProvider,
    required AigcEditingThemeListProvider themeListProvider,
    required AigcEditingMultiSelectProvider multiSelectProvider,
    this.onThemeChanged,
    this.onCreateClick,
    int? index,
  })  : _aigcPresetsRepository = aigcPresetsRepository,
        _imageProvider = imageProvider,
        _themeListProvider = themeListProvider,
        _multiSelectProvider = multiSelectProvider {
    _index = index ?? -1;
    // 初始化预设数据监听
    _subscribeToPresetsStream();
    _setupImageListener();
  }

  int get index => _index;

  /// 设置索引
  void setIndex(int index) {
    if (_index != index) {
      _index = index;
      _onUpdateDefaultCoverImage();
      notifyListeners();
    }
  }

  /// 根据预设和创意初始化选中状态
  void initializeSelection(
      AigcPresetsModel? preset, AigcPresetsEffect? effect) {
    if (preset != null) {
      // 设置选中的预设
      _selectedThemeId = preset.id;
      _themeListProvider.setSelectedPreset(preset);

      // 如果有创意，设置选中的创意
      if (effect != null) {
        // 找到创意在预设中的索引
        final effectIndex =
            preset.effects.indexWhere((e) => e.effectCode == effect.effectCode);
        if (effectIndex >= 0) {
          _selectedCreativeIndices[preset.id] = effectIndex;
          _themeListProvider.setSelectedEffect(effect);
        }
      }

      notifyListeners();
    }
  }

  /// 获取主题列表
  List<AigcPresetsModel> get themeItems => _themeItems;

  /// 设置主题列表
  set themeItems(List<AigcPresetsModel> value) {
    _themeItems = value;
    notifyListeners();
  }

  /// 获取选中的创意id
  String? get selectedCreateId => _themeListProvider.selectedEffect?.effectCode;

  /// 真正被选中的预设主题id
  String? get realSelectedThemeId {
    final selectedCreativeCode = selectedCreateId;
    if (selectedCreativeCode == null) {
      return null;
    }

    return _getThemeIdByEffectCode(selectedCreativeCode);
  }

  /// 获取当前选中的主题索引
  int? get selectedThemeIndex {
    if (_selectedThemeId == null) {
      return null;
    }
    return _getThemeIndexById(_selectedThemeId!);
  }

  /// 通过主题ID获取主题索引
  int? _getThemeIndexById(String themeId) {
    for (int i = 0; i < _themeItems.length; i++) {
      if (_themeItems[i].id == themeId) {
        return i;
      }
    }
    return null;
  }

  /// 获取当前选中的主题
  AigcPresetsModel? get selectedTheme {
    if (_selectedThemeId == null) {
      return null;
    }
    return getPresetById(_selectedThemeId!);
  }

  /// 是否显示创意列表
  bool get showingCreativeList => _showingCreativeList;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 获取错误信息
  String? get errorMessage => _errorMessage;

  void _subscribeToPresetsStream() {
    // 订阅预设数据流
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    _presetsSubscription =
        _aigcPresetsRepository.getAigcPresetsStream().listen((presets) {
      _isLoading = false;
      notifyListeners();

      _handleThemeItemsUpdate(_convertToThemeItems(presets));
    }, onError: (error) {
      _errorMessage = '监听AIGC预设数据失败: $error';
      PGLog.e('监听AIGC预设数据失败: $error');
      _isLoading = false;
      notifyListeners();
    });
  }

  /// 将原始预设数据转换为主题数据
  List<AigcPresetsModel> _convertToThemeItems(
      List<AigcPcPresetsDetailResponse> originPresets) {
    return originPresets
        .map((e) => AigcPresetsModel.convertResponseToInUsedPresetModel(e))
        // 过滤掉status为running的预设以及未选择创意的预设
        .where((element) =>
            element.status == AigcRequestConst.completed &&
            element.effects.isNotEmpty)
        .toList();
  }

  /// 处理主题数据更新
  void _handleThemeItemsUpdate(List<AigcPresetsModel> themeItems) {
    _themeItems = themeItems;

    if (!_isFirstInternalPresetModel()) {
      _onUpdateDefaultCoverImage();
    }

    // 处理数据更新后的选中状态同步
    _syncSelectionStateAfterDataUpdate();

    _isLoading = false;
    PGLog.d('AIGC预设数据更新成功，共${_themeItems.length}个主题');
    notifyListeners();
  }

  /// 数据更新后同步选中状态
  void _syncSelectionStateAfterDataUpdate() {
    // 检查当前选中的主题是否仍然存在
    if (_selectedThemeId != null) {
      final selectedTheme = getPresetById(_selectedThemeId!);
      if (selectedTheme == null) {
        // 如果选中的主题不存在了，清除选中状态并隐藏创意列表
        _selectedThemeId = null;
        _selectedCreativeIndices.clear();
        if (_showingCreativeList) {
          _showingCreativeList = false;
        }
        return;
      }

      // 检查当前选中的创意是否仍然有效
      final selectedCreativeIndex = _selectedCreativeIndices[_selectedThemeId!];
      if (selectedCreativeIndex != null) {
        if (selectedCreativeIndex >= selectedTheme.effects.length) {
          // 如果选中的创意索引超出范围，重新选择第一个（如果有的话）
          if (selectedTheme.effects.isNotEmpty) {
            _selectedCreativeIndices[_selectedThemeId!] = 0;
            _themeListProvider.setSelectedEffect(selectedTheme.effects[0]);
          } else {
            // 如果没有创意了，移除选中状态并隐藏创意列表
            _selectedCreativeIndices.remove(_selectedThemeId!);
            if (_showingCreativeList) {
              _showingCreativeList = false;
            }
          }
        } else {
          // 选中的创意仍然有效，更新Provider中的状态
          _themeListProvider
              .setSelectedEffect(selectedTheme.effects[selectedCreativeIndex]);
        }
      } else {
        // 如果当前主题还没有选中过创意，并且有创意可选，则默认选中第一个
        if (selectedTheme.effects.isNotEmpty) {
          _selectedCreativeIndices[_selectedThemeId!] = 0;
          _themeListProvider.setSelectedEffect(selectedTheme.effects[0]);
        } else {
          // 如果当前主题没有创意并且正在显示创意列表，则隐藏创意列表
          if (_showingCreativeList) {
            _showingCreativeList = false;
          }
        }
      }

      // 更新Provider中的选中主题
      _themeListProvider.setSelectedPreset(selectedTheme);
    } else {
      // 如果没有选中的主题但正在显示创意列表，则隐藏创意列表
      if (_showingCreativeList) {
        _showingCreativeList = false;
      }
    }
  }

  /// 刷新预设数据
  Future<void> refreshPresetsData() async {
    try {
      // 调用Repository的刷新方法，数据会通过现有的Stream推送
      await _aigcPresetsRepository.refreshPresets();
    } catch (e) {
      PGLog.e("刷新预设数据失败: $e");
    }
  }

  /// 根据预设ID获取预设详情
  AigcPresetsModel? getPresetById(String presetId) {
    try {
      return _themeItems.firstWhere((theme) => theme.id == presetId);
    } catch (e) {
      return null;
    }
  }

  /// 根据效果代码获取效果详情
  AigcPresetsEffect? getEffectByCode(String effectCode) {
    for (final theme in _themeItems) {
      for (final effect in theme.effects) {
        if (effect.effectCode == effectCode) {
          return effect;
        }
      }
    }
    return null;
  }

  /// 选择主题
  void selectTheme(int index) {
    if (index < 0 || index >= _themeItems.length) {
      return;
    }

    final themeId = _themeItems[index].id;
    _selectedThemeId = themeId;
    _themeListProvider.setSelectedPreset(_themeItems[index]);

    // Update: 目前不需要此逻辑
    // 如果这个主题没有选中过创意，则默认选中第一个
    // if (!_selectedCreativeIndices.containsKey(themeId) &&
    //     _themeItems[index].effects.isNotEmpty) {
    //   _selectedCreativeIndices[themeId] = 0;
    //   _themeListProvider.setSelectedEffect(_themeItems[index].effects[0]);
    // }

    // 触发选择回调
    if (onThemeChanged != null) {
      onThemeChanged!(_themeItems[index]);
    }

    notifyListeners();
  }

  // /// 通过主题ID选择主题
  // void selectThemeById(String themeId) {
  //   final index = _getThemeIndexById(themeId);
  //   if (index != null) {
  //     selectTheme(index);
  //   }
  // }

  /// 选择主题但不触发通知
  /// 用于与动画同步，避免状态变化导致界面重建过早
  void selectThemeWithoutNotify(int index) {
    if (index < 0 || index >= _themeItems.length) {
      return;
    }

    final themeId = _themeItems[index].id;
    _selectedThemeId = themeId;
    _themeListProvider.setSelectedPreset(_themeItems[index]);

    // Update: 目前不需要此逻辑
    // // 如果这个主题没有选中过创意，则默认选中第一个
    // if (!_selectedCreativeIndices.containsKey(themeId) &&
    //     _themeItems[index].effects.isNotEmpty) {
    //   _selectedCreativeIndices[themeId] = 0;
    //   _themeListProvider.setSelectedEffect(_themeItems[index].effects[0]);
    // }

    // 触发选择回调
    if (onThemeChanged != null) {
      onThemeChanged!(_themeItems[index]);
    }

    // 不调用notifyListeners()，由后续操作触发通知
  }

  /// 显示创意列表
  void showCreativeList() {
    if (_selectedThemeId != null) {
      final selectedTheme = getPresetById(_selectedThemeId!);
      if (selectedTheme != null && selectedTheme.effects.isNotEmpty) {
        _showingCreativeList = true;
        notifyListeners();
      }
    }
  }

  /// 隐藏创意列表
  void hideCreativeList() {
    _showingCreativeList = false;
    notifyListeners();
  }

  /// 选择创意
  void selectCreative(int themeIndex, int creativeIndex) {
    if (themeIndex < 0 || themeIndex >= _themeItems.length) {
      return;
    }
    if (creativeIndex < 0 ||
        creativeIndex >= _themeItems[themeIndex].effects.length) {
      return;
    }

    final themeId = _themeItems[themeIndex].id;
    _selectedCreativeIndices[themeId] = creativeIndex;

    // 触发创意选择回调
    _themeListProvider
        .setSelectedEffect(_themeItems[themeIndex].effects[creativeIndex]);
    // processState 会自动基于当前图片的状态计算，无需手动设置

    // 通知监听器更新UI
    notifyListeners();
  }

  /// 通过主题ID选择创意
  void selectCreativeById(String themeId, int creativeIndex) {
    final theme = getPresetById(themeId);
    if (theme == null) {
      return;
    }

    if (creativeIndex < 0 || creativeIndex >= theme.effects.length) {
      return;
    }

    _selectedCreativeIndices[themeId] = creativeIndex;

    // 触发创意选择回调
    _themeListProvider.setSelectedEffect(theme.effects[creativeIndex]);

    // 通知监听器更新UI
    notifyListeners();
  }

  /// 设置创意悬停
  void setCreativeHover(int creativeIndex, Offset position) {
    if (_selectedThemeId == null) {
      return;
    }

    final selectedTheme = getPresetById(_selectedThemeId!);
    if (selectedTheme == null) {
      return;
    }

    if (creativeIndex < 0 || creativeIndex >= selectedTheme.effects.length) {
      return;
    }

    // 触发鼠标悬停回调
    _themeListProvider.setHoverEffect(selectedTheme.effects[creativeIndex]);

    // 移除notifyListeners()调用，避免在鼠标悬停时触发整个组件的重建
  }

  /// 清除创意悬停
  void clearCreativeHover() {
    // 触发鼠标离开回调
    _themeListProvider.setHoverEffect(null);

    // 移除notifyListeners()调用，避免在鼠标离开时触发整个组件的重建
  }

  /// 创建主题
  void createTheme() {
    if (onCreateClick != null) {
      onCreateClick!();
    }
  }

  bool isSelectedInternalPresetModel() {
    return selectedTheme?.id == AigcPresetsModelInternal.internalAIGCPresetId;
  }

  @override
  void dispose() {
    _presetsSubscription?.cancel();
    _imageProvider.removeListener(_onUpdateSelectedImage);
    super.dispose();
  }

  /// 设置监听共享数据Provider的变化
  void _setupImageListener() {
    // 监听共享Provider的变化
    _imageProvider.addListener(_onUpdateSelectedImage);
  }

  bool _isFirstInternalPresetModel() {
    if (_themeItems.isEmpty) {
      return false;
    }
    return _themeItems.first.id ==
        AigcPresetsModelInternal.internalAIGCPresetId;
  }

  void _onUpdateSelectedImage() {
    _onUpdateDefaultCoverImage();
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage != null) {
      if (_currentImageSourceFileId != selectedImage.fileId) {
        _currentImageSourceFileId = selectedImage.fileId;

        // 重置所有主题列表相关状态
        _themeListProvider.reset();
        _selectedThemeId = null;
        _selectedCreativeIndices.clear(); // 清空所有主题的创意选中状态
        _showingCreativeList = false; // 隐藏创意列表

        _isChangeImageSoucreFileId = true;
        notifyListeners();
      }
    }
  }

  void _onUpdateDefaultCoverImage() {
    AigcPreviewImageItem? selectedImage = _imageProvider.selectedImage;
    // 如果index不为-1，则说明是多选，使用index对应的图片作为封面
    if (_index != -1 && _multiSelectProvider.selectedImages.length > _index) {
      selectedImage = _multiSelectProvider.selectedImages.elementAt(_index);
    }
    if (selectedImage != null) {
      final thumbnailPath = selectedImage.thumbnailPath ?? "";
      final previewPath = selectedImage.previewPath ?? "";
      final model = AigcPresetsModelInternal.generateInternalPresetModel(
          thumbnailPath, previewPath);
      if (_isFirstInternalPresetModel()) {
        _themeItems.removeAt(0);
      }
      _themeItems.insert(0, model);
      notifyListeners();
    }
  }

  /// 通过 [effectCode] 反查其所在的主题，并返回预设主题 id
  String? _getThemeIdByEffectCode(String effectCode) {
    return _themeItems
        .where((theme) =>
            theme.effects.any((effect) => effect.effectCode == effectCode))
        .firstOrNull
        ?.id;
  }
}
