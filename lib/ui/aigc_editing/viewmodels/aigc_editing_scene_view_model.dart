import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.internal.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_history_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_preferred_mask_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_mask_query_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_regional_frame_provider.dart';
import 'package:turing_art/ui/aigc_editing/utils/aigc_sample_processor.dart';
import 'package:turing_art/ui/aigc_editing/widgets/batch_sample/model/aigc_batch_sample_model.dart';
import 'package:turing_art/utils/pg_log.dart';

class AigcEditingSceneViewModel extends ChangeNotifier {
  final CurrentEditingProjectRepository _currentEditingProjectRepository;
  final AigcSampleRepository _aigcSampleRepository;
  final ProjectStateProvider _projectStateProvider;
  final NavigatorService _navigator;
  final AigcEditingImageProvider _imageProvider; // 共享数据Provider
  final AigcEditingThemeListProvider _themeListProvider;
  final AigcEditingImagePreferredMaskProvider _preferredMaskProvider;
  // 媒体上传管理
  final MediaUploadRepository _mediaUploadRepository;

  final MediaRepository _mediaRepository;

  // 历史记录管理
  final AigcEditingHistoryProvider _aigcEditingHistoryProvider;

  // 区域框Provider
  final AigcRegionalFrameProvider _aigcRegionalFrameProvider;

  // 打样处理器
  late final AigcSampleProcessor _sampleProcessor;

  // 蒙版查询Provider
  late final AigcImageMaskQueryProvider _maskQueryProvider;

  // 打样单个效果消耗的积分
  int? _perSampleEffectCostValue;

  // 已经打样过的图片ID
  final List<String> _processedImageIds = [];

  // 全屏加载状态
  bool _isCreatingSample = false;

  // 打样取消标志（单个打样和批量打样共用）
  bool _isSampleCancelled = false;

  // 添加处理状态和积分消耗的getter
  ProcessState get processState => _themeListProvider
      .fetchProcessState(_imageProvider.selectedImage?.fileId ?? '');

  final OpsCustomTableRepository _customTableRepository;

  // 打样3张效果图消耗的积分
  int get allCostValue => perSampleEffectCostValue * 3;

  // 单个打样消耗的积分, 假设服务端异常本地默认3积分
  int get perSampleEffectCostValue => _perSampleEffectCostValue ?? 3;

  // 获取全屏加载状态
  bool get isCreatingSample => _isCreatingSample;

  // 设置全屏加载状态
  void _setCreatingSampleState(bool isCreating) {
    if (_isCreatingSample != isCreating) {
      _isCreatingSample = isCreating;
      notifyListeners();
    }
  }

  /// 取消打样操作（单个打样和批量打样通用）
  void cancelSample() {
    _isSampleCancelled = true;
    PGLog.d('打样操作已被取消');
  }

  AigcEditingSceneViewModel({
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required AigcSampleRepository aigcSampleRepository,
    required MediaUploadRepository mediaUploadRepository,
    required NavigatorService navigator,
    required ProjectStateProvider projectStateProvider,
    required AigcEditingImageProvider imageProvider, // 注入共享数据Provider
    required AigcEditingThemeListProvider themeListProvider,
    required MediaRepository mediaRepository,
    required AigcEditingHistoryProvider aigcEditingHistoryProvider,
    required AigcRegionalFrameProvider aigcRegionalFrameProvider,
    required AigcEditingImagePreferredMaskProvider preferredMaskProvider,
    required OpsCustomTableRepository customTableRepository,
  })  : _currentEditingProjectRepository = currentEditingProjectRepository,
        _navigator = navigator,
        _projectStateProvider = projectStateProvider,
        _imageProvider = imageProvider,
        _aigcSampleRepository = aigcSampleRepository,
        _mediaUploadRepository = mediaUploadRepository,
        _themeListProvider = themeListProvider,
        _mediaRepository = mediaRepository,
        _aigcEditingHistoryProvider = aigcEditingHistoryProvider,
        _aigcRegionalFrameProvider = aigcRegionalFrameProvider,
        _preferredMaskProvider = preferredMaskProvider,
        _customTableRepository = customTableRepository {
    // 初始化打样处理器
    _sampleProcessor = AigcSampleProcessor(
      aigcSampleRepository: _aigcSampleRepository,
      mediaUploadRepository: _mediaUploadRepository,
      currentEditingProjectRepository: _currentEditingProjectRepository,
    );

    // 初始化蒙版查询Provider
    _maskQueryProvider = AigcImageMaskQueryProvider(
      historyProvider: _aigcEditingHistoryProvider,
    );
    _imageProvider.addListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.addListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.addListener(_onHistoryChanged);
    fetchAigcPointConfig();
  }

  /// 返回
  Future<void> onBackPressed() async {
    try {
      await _currentEditingProjectRepository.exitWorkspace();
      _projectStateProvider.exitEdit();

      // 使用微任务确保在下一个事件循环中执行导航，避免Navigator状态冲突
      await Future.microtask(() {
        _navigator.pop();
      });
    } catch (e) {
      PGLog.e('返回操作失败: $e');
      // 即使出错也要尝试返回，但要安全地执行
      await Future.microtask(() {
        _navigator.pop();
      });
    }
  }

  @override
  void dispose() {
    // 清理缩略图资源
    // releaseAllThumbnails();
    _imageProvider.removeListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.removeListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.removeListener(_onHistoryChanged);
    super.dispose();
  }

  // MARK: 监听选中图片逻辑
  // 添加监听回调方法
  void _onSharedDataChanged() {
    // 检查是否有图片选择变化
    if (_imageProvider.hasImageSelectionChanged) {
      // 获取当前选中的图片
      final selectedImage = _imageProvider.selectedImage;

      if (selectedImage != null) {
        // 同时更新本地的已处理图片ID列表（兼容性）
        final imageState =
            _themeListProvider.getImageState(selectedImage.fileId);
        if (imageState == ProcessState.processed &&
            !_processedImageIds.contains(selectedImage.fileId)) {
          _processedImageIds.add(selectedImage.fileId);
        }

        // 历史记录同步更新对应的容器
        _aigcEditingHistoryProvider.setCurrentImage(selectedImage.fileId);
      }
    }
  }

  void _onHistoryChanged() {
    final historyStates =
        _aigcEditingHistoryProvider.getCurrentHistoryValidRegionalStates();
    _aigcRegionalFrameProvider
        .reloadRegionalFramesFromHistoryStates(historyStates);
  }

  // 判断是否打样过
  bool isProcessedImage(String fileId) {
    return _processedImageIds.contains(fileId);
  }

  // MARK: 创建打样
  // isOriginal 是否为原图打样
  // 如果isOriginal为true，则不使用创意和主题，直接使用原图打样
  // 如果isOriginal为false，则使用创意和主题，进行打样,此时需要判断创意和主题是否存在
  Future<(bool, String?)> createSample({bool isOriginal = false}) async {
    // 重置单个打样取消标志
    _isSampleCancelled = false;

    // 设置全屏加载状态为true
    _setCreatingSampleState(true);

    // 获取当前选中的图片
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      PGLog.e('未选择图片');
      _setCreatingSampleState(false);
      return (false, '未选择图片');
    }

    // 设置图片状态为加载中
    _themeListProvider.setImageState(
        selectedImage.fileId, ProcessState.loading);

    try {
      // 使用打样处理器创建打样，传递取消检查回调
      final result = await _sampleProcessor.createSample(
        selectedImage: selectedImage,
        selectedEffect: _themeListProvider.selectedEffect,
        presetsModel: _themeListProvider.selectedPreset,
        preferredMaskPath: _preferredMaskProvider.currentImagePreferredMaskPath,
        isOriginal: isOriginal,
        isCancelledCallback: () => _isSampleCancelled,
      );

      // 检查是否被取消
      if (_isSampleCancelled) {
        PGLog.d('单个打样被取消，恢复状态');
        // 恢复图片的原始状态
        final originalState = _processedImageIds.contains(selectedImage.fileId)
            ? ProcessState.processed
            : ProcessState.initial;
        _themeListProvider.setImageState(selectedImage.fileId, originalState);
        _setCreatingSampleState(false);
        return (false, '单个打样已取消');
      }

      // 延迟2秒后设置状态
      await Future.delayed(const Duration(seconds: 2));

      if (result.$1) {
        // 成功 - 更新图片状态为已处理
        _themeListProvider.setImageState(
            selectedImage.fileId, ProcessState.processed);
        _processedImageIds.add(selectedImage.fileId);
      } else {
        // 失败 - 恢复图片的原始状态
        final originalState = _processedImageIds.contains(selectedImage.fileId)
            ? ProcessState.processed
            : ProcessState.initial;
        _themeListProvider.setImageState(selectedImage.fileId, originalState);
      }

      _setCreatingSampleState(false);
      return result;
    } catch (e) {
      PGLog.e('创建打样失败: $e');
      // 延迟2秒后恢复图片的原始状态
      await Future.delayed(const Duration(seconds: 2));
      final originalState = _processedImageIds.contains(selectedImage.fileId)
          ? ProcessState.processed
          : ProcessState.initial;
      _themeListProvider.setImageState(selectedImage.fileId, originalState);
      _setCreatingSampleState(false);
      return (false, '创建打样失败: $e');
    }
  }

  /// 批量打样方法
  ///
  /// 参数：
  /// - images: 要打样的图片列表
  /// - onProgress: 进度回调
  /// - errorStrategy: 错误处理策略，默认为跳过错误继续执行
  ///
  /// 返回：
  /// - List<(bool, String?)>: 每个打样的结果列表
  Future<List<(bool, String?)>> createBatchSamples({
    required List<AigcBatchSampleModel> sampleModels,
    Function(int current, int total, {required bool success, String? error})?
        onProgress,
    BatchErrorStrategy errorStrategy = BatchErrorStrategy.skipOnError,
  }) async {
    // 重置取消标志
    _isSampleCancelled = false;

    // 获取所有图片
    final images = sampleModels.map((model) => model.selectedImage).toList();
    // 使用蒙版查询Provider批量获取每个图片的蒙版路径
    final maskPaths = _maskQueryProvider.getBatchMaskPaths(images);

    // 记录蒙版信息统计
    final maskedCount = maskPaths.values.where((path) => path != null).length;
    PGLog.d('批量打样: 总共${images.length}张图片，其中$maskedCount张有蒙版');

    final batchRequests = <BatchSampleRequest>[];
    for (var model in sampleModels) {
      // 判断是否为原图打样
      bool isOriginal = false;
      if (model.effect?.effectCode ==
              AigcPresetsModelInternal.internalAIGCEffectId &&
          model.preset?.id == AigcPresetsModelInternal.internalAIGCPresetId) {
        isOriginal = true;
      }

      final maskPath = maskPaths[model.selectedImage.fileId];
      final batchRequest = BatchSampleRequest(
        selectedImage: model.selectedImage,
        selectedEffect: model.effect,
        presetsModel: model.preset,
        preferredMaskPath: maskPath,
        isOriginal: isOriginal,
      );
      batchRequests.add(batchRequest);
    }

    // 设置全屏加载状态
    _setCreatingSampleState(true);

    // 批量设置所有要打样的图片状态为loading
    final loadingStates = <String, ProcessState>{};
    for (final request in batchRequests) {
      loadingStates[request.selectedImage.fileId] = ProcessState.loading;
    }
    _themeListProvider.setBatchImageStates(loadingStates);
    // processState 会自动反映当前选中图片的状态，无需手动更新

    try {
      // 使用打样处理器进行批量处理，传递取消检查函数
      final results = await _sampleProcessor.createBatchSamples(
        sampleRequests: batchRequests,
        onProgress: onProgress,
        errorStrategy: errorStrategy,
        isCancelledCallback: () => _isSampleCancelled,
      );

      // 检查是否被取消
      if (_isSampleCancelled) {
        PGLog.d('批量打样被取消，恢复图片状态');
        // 恢复所有图片的状态
        final restoreStates = <String, ProcessState>{};
        for (final request in batchRequests) {
          final fileId = request.selectedImage.fileId;
          final originalState = _processedImageIds.contains(fileId)
              ? ProcessState.processed
              : ProcessState.initial;
          restoreStates[fileId] = originalState;
        }
        _themeListProvider.setBatchImageStates(restoreStates);

        // processState 会自动反映当前选中图片的状态，无需手动更新

        _setCreatingSampleState(false);
        return batchRequests.map((req) => (false, '批量打样已取消')).toList();
      }

      // 更新已处理的图片ID列表和状态
      final finalStates = <String, ProcessState>{};
      for (int i = 0; i < batchRequests.length; i++) {
        final fileId = batchRequests[i].selectedImage.fileId;
        if (i < results.length && results[i].$1) {
          // 成功的打样，添加到已处理列表并设置为processed状态
          _processedImageIds.add(fileId);
          finalStates[fileId] = ProcessState.processed;
        } else {
          // 失败的打样，恢复到原来的状态
          final originalState = _processedImageIds.contains(fileId)
              ? ProcessState.processed
              : ProcessState.initial;
          finalStates[fileId] = originalState;
        }
      }

      // 批量更新图片状态
      if (finalStates.isNotEmpty) {
        _themeListProvider.setBatchImageStates(finalStates);
        // processState 会自动反映当前选中图片的状态，无需手动更新
      }

      _setCreatingSampleState(false);
      return results;
    } catch (e) {
      PGLog.e('批量打样失败: $e');

      // 恢复所有图片的状态（从loading恢复到原来的状态）
      final restoreStates = <String, ProcessState>{};
      for (final request in batchRequests) {
        final fileId = request.selectedImage.fileId;
        // 如果图片之前已经打样过，恢复为processed，否则恢复为initial
        final originalState = _processedImageIds.contains(fileId)
            ? ProcessState.processed
            : ProcessState.initial;
        restoreStates[fileId] = originalState;
      }
      _themeListProvider.setBatchImageStates(restoreStates);

      // processState 会自动反映当前选中图片的状态，无需手动更新

      _setCreatingSampleState(false);
      return batchRequests.map((req) => (false, '批量打样失败: $e')).toList();
    }
  }

  Future<bool> onDragDone(DealImageFilesResult file) async {
    if (file.validFiles.isEmpty) {
      PGLog.e('拖拽文件为空');
      return false;
    }
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      PGLog.e('工作区为空');
      return false;
    }

    final files = await _mediaRepository.generateWorkspaceFiles(
      workspace.workspaceId,
      file.validFiles,
    );

    await _currentEditingProjectRepository.batchAddFiles(files);
    return true;
  }

  Future<void> fetchAigcPointConfig() async {
    try {
      final configList = await _customTableRepository.getAigcPointConfig();
      final proofingPoint =
          configList?.points?.firstWhere((e) => e.type == 'proofing').value;
      if (proofingPoint != null && proofingPoint > 0) {
        _perSampleEffectCostValue = proofingPoint;
      }
      notifyListeners();
    } catch (e) {
      // 错误处理
      PGLog.e('获取打样积分配置失败: $e');
    }
  }
}
