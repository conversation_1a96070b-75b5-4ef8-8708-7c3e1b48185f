import 'dart:ui' as ui;

import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_overlay_provider.dart';
import 'package:turing_art/ui/aigc_editing/strategies/aigc_background_strategy.dart';

/// AIGC图像叠加ViewModel
///
/// 负责图像叠加相关的业务逻辑，不继承ChangeNotifier
/// 通过Provider进行数据驱动和UI刷新
///
/// 工作流程：
/// 1. AigcEditingImageProvider 发生 selectedImage 变化时通知监听者
/// 2. AigcImageOverlayProvider 监听到通知后，从路径中加载背景图和蒙层图片
/// 3. 图片加载完成后，AigcImageOverlayProvider 自动发送通知
/// 4. AigcImageOverlayWidget 通过 Consumer 监听并更新视图显示
///
/// 使用方式：
/// 1. 在AigcEditingScene中，AigcImageOverlayProvider已经配置为监听AigcEditingImageProvider
/// 2. 当其他组件更新AigcEditingImageProvider.selectedImage时，
///    AigcImageOverlayProvider会自动接收通知并开始加载相应的图片
/// 3. UI组件通过Consumer<AigcImageOverlayProvider>来监听图片加载状态变化
class AigcImageOverlayViewModel {
  /// Provider实例
  final AigcImageOverlayProvider _provider;
  final AigcEditingControlProvider _controlProvider;

  /// 构造函数
  AigcImageOverlayViewModel({
    required AigcImageOverlayProvider provider,
    required AigcEditingControlProvider controlProvider,
  })  : _provider = provider,
        _controlProvider = controlProvider;

  /// 获取背景图片
  ui.Image? get backgroundImage => _provider.backgroundImage;

  /// 获取蒙版图片
  ui.Image? get maskImage => _provider.maskImage;

  /// 获取是否显示蒙版
  bool get showMask => _provider.showMask;

  /// 获取背景图片加载状态
  bool get isLoadingBackground => _provider.isLoadingBackground;

  /// 获取蒙版图片加载状态
  bool get isLoadingMask => _provider.isLoadingMask;

  /// 获取背景图片加载错误
  String? get backgroundLoadError => _provider.backgroundLoadError;

  /// 获取蒙版图片加载错误
  String? get maskLoadError => _provider.maskLoadError;

  /// 获取背景策略
  AigcBackgroundStrategy? get backgroundStrategy =>
      _provider.backgroundStrategy;

  /// 设置是否显示蒙版
  void setShowMask({required bool value}) {
    _provider.setShowMask(showMask: value);
  }

  /// 设置背景图片
  void setBackgroundImage(ui.Image? image) {
    _provider.setBackgroundImage(image);
  }

  /// 设置蒙版图片
  void setMaskImage(ui.Image? image) {
    _provider.setMaskImage(image);
  }

  /// 获取控制提供者
  AigcEditingControlProvider get controlProvider => _controlProvider;
}
