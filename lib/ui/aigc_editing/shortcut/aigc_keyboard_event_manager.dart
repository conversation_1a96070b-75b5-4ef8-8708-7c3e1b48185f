import 'package:flutter/services.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_key.dart';
import 'package:turing_art/utils/pg_log.dart';

// 键盘事件管理器，使用单例模式进行管理
class KeyboardEventManager {
  static KeyboardEventManager? _instance;

  static KeyboardEventManager get instance =>
      _instance ??= KeyboardEventManager._();

  KeyboardEventManager._();

  final Map<String, ShortcutRegistration> _registrations = {};
  final List<ShortcutRegistration> _sortedRegistrations = [];
  bool _isListening = false;

  // 跟踪按键状态
  final Set<LogicalKeyboardKey> _pressedKeys = <LogicalKeyboardKey>{};

  // 注册快捷键
  void registerShortcut({
    required String id,
    required String widgetId,
    required ShortcutKey shortcut,
    required KeyboardEventHandler handler,
    required String description,
    int priority = 0,
    bool Function()? canExecute,
    KeyboardEventHandler? onKeyUp, // 新增：按键抬起处理器
  }) {
    final registration = ShortcutRegistration(
      id: id,
      widgetId: widgetId,
      shortcut: shortcut,
      handler: handler,
      description: description,
      priority: priority,
      canExecute: canExecute,
      onKeyUp: onKeyUp, // 传递按键抬起处理器
    );

    _registrations[id] = registration;
    _rebuildSortedList();

    PGLog.i('注册快捷键: $id ($widgetId) -> $shortcut ($description)');
  }

  // 注销快捷键
  void unregisterShortcut(String id) {
    if (_registrations.remove(id) != null) {
      _rebuildSortedList();
      PGLog.d('注销快捷键: $id');
    }
  }

  // 根据Widget ID注销所有快捷键
  void unregisterWidgetShortcuts(String widgetId) {
    final toRemove = _registrations.entries
        .where((entry) => entry.value.widgetId == widgetId)
        .map((entry) => entry.key)
        .toList();

    for (final id in toRemove) {
      _registrations.remove(id);
    }

    if (toRemove.isNotEmpty) {
      _rebuildSortedList();
      PGLog.i('注销Widget快捷键: $widgetId (${toRemove.length}个)');
    }
  }

  // 开始监听
  void startListening() {
    if (!_isListening) {
      HardwareKeyboard.instance.addHandler(_handleKeyEvent);
      _isListening = true;
      PGLog.d('全局键盘事件管理器开始监听');
    }
  }

  // 停止监听
  void stopListening() {
    if (_isListening) {
      HardwareKeyboard.instance.removeHandler(_handleKeyEvent);
      _isListening = false;
      PGLog.d('全局键盘事件管理器停止监听');
    }
  }

  // 重建排序列表
  void _rebuildSortedList() {
    _sortedRegistrations.clear();
    _sortedRegistrations.addAll(_registrations.values);
    _sortedRegistrations.sort((a, b) => b.priority.compareTo(a.priority));
  }

  // 处理键盘事件
  bool _handleKeyEvent(KeyEvent event) {
    final currentKey = ShortcutKey(
      key: event.logicalKey,
      ctrl: HardwareKeyboard.instance.isControlPressed,
      shift: HardwareKeyboard.instance.isShiftPressed,
      alt: HardwareKeyboard.instance.isAltPressed,
    );

    if (event is KeyDownEvent) {
      _pressedKeys.add(event.logicalKey);

      // 按优先级处理按下事件
      for (final registration in _sortedRegistrations) {
        if (registration.shortcut == currentKey && registration.isEnabled) {
          try {
            final result = registration.handler();
            PGLog.d(
                '处理快捷键按下: ${registration.id} (${registration.widgetId}) -> $result');

            if (result == KeyEventHandleResult.handled) {
              return true; // 阻止事件传播
            } else if (result == KeyEventHandleResult.consumed) {
              // 继续处理其他同样的快捷键
              continue;
            }
          } catch (e) {
            PGLog.e('快捷键处理异常: ${registration.id} -> $e');
          }
        }
      }
    } else if (event is KeyUpEvent) {
      _pressedKeys.remove(event.logicalKey);

      // 只处理注册了onKeyUp处理器的快捷键的抬起事件
      for (final registration in _sortedRegistrations) {
        if (registration.shortcut == currentKey &&
            registration.isEnabled &&
            registration.onKeyUp != null) {
          try {
            final result = registration.onKeyUp!();
            PGLog.d(
                '处理快捷键抬起: ${registration.id} (${registration.widgetId}) -> $result');

            if (result == KeyEventHandleResult.handled) {
              return true; // 阻止事件传播
            } else if (result == KeyEventHandleResult.consumed) {
              // 继续处理其他同样的快捷键
              continue;
            }
          } catch (e) {
            PGLog.e('快捷键抬起处理异常: ${registration.id} -> $e');
          }
        }
      }
    }

    return false;
  }

  // 检查按键是否被按下
  bool isKeyPressed(LogicalKeyboardKey key) {
    return _pressedKeys.contains(key);
  }

  // 获取所有注册的快捷键信息
  List<ShortcutRegistration> getAllRegistrations() {
    return List.from(_sortedRegistrations);
  }

  // 获取指定Widget的快捷键
  List<ShortcutRegistration> getWidgetRegistrations(String widgetId) {
    return _registrations.values
        .where((reg) => reg.widgetId == widgetId)
        .toList();
  }
}
