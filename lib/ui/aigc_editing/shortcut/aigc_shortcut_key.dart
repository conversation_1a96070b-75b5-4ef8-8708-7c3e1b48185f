import 'package:flutter/services.dart';

// 键盘事件处理结果
enum KeyEventHandleResult {
  handled, // 事件已处理，停止传播
  ignored, // 事件未处理，继续传播
  consumed, // 事件已消费，但允许其他处理器处理
}

// 快捷键定义
class ShortcutKey {
  final LogicalKeyboardKey key;
  final bool ctrl;
  final bool shift;
  final bool alt;

  const ShortcutKey({
    required this.key,
    this.ctrl = false,
    this.shift = false,
    this.alt = false,
  });

  // 预定义常用快捷键
  static const undo = ShortcutKey(key: LogicalKeyboardKey.keyZ, ctrl: true);
  static const redo =
      ShortcutKey(key: LogicalKeyboardKey.keyZ, ctrl: true, shift: true);
  static const delete = ShortcutKey(key: LogicalKeyboardKey.delete);
  static const backspace = ShortcutKey(key: LogicalKeyboardKey.backspace);
  static const leftArrow = ShortcutKey(key: LogicalKeyboardKey.arrowLeft);
  static const rightArrow = ShortcutKey(key: LogicalKeyboardKey.arrowRight);
  static const selectAll =
      ShortcutKey(key: LogicalKeyboardKey.keyA, ctrl: true);
  static const escape = ShortcutKey(key: LogicalKeyboardKey.escape);
  static const space = ShortcutKey(key: LogicalKeyboardKey.space);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is ShortcutKey &&
        other.key == key &&
        other.ctrl == ctrl &&
        other.shift == shift &&
        other.alt == alt;
  }

  @override
  int get hashCode => Object.hash(key, ctrl, shift, alt);

  @override
  String toString() {
    List<String> parts = [];
    if (ctrl) {
      parts.add('Ctrl');
    }
    if (shift) {
      parts.add('Shift');
    }
    if (alt) {
      parts.add('Alt');
    }
    parts.add(key.keyLabel);
    return parts.join('+');
  }
}

// 快捷键处理器
typedef KeyboardEventHandler = KeyEventHandleResult Function();

// 快捷键注册项
class ShortcutRegistration {
  final String id;
  final String widgetId; // 所属Widget的ID
  final ShortcutKey shortcut;
  final KeyboardEventHandler handler;
  final String description;
  final int priority;
  final bool Function()? canExecute; // 条件检查
  final KeyboardEventHandler? onKeyUp; // 按键抬起处理器

  ShortcutRegistration({
    required this.id,
    required this.widgetId,
    required this.shortcut,
    required this.handler,
    required this.description,
    this.priority = 0,
    this.canExecute,
    this.onKeyUp,
  });

  bool get isEnabled => canExecute?.call() ?? true;
}
