# AIGC编辑服务协调器使用指南

## 🎯 概述

`AigcEditingServiceCoordinator` 是一个专门的服务协调器，负责在 `AigcEditingImageProvider` 和 `AigcService` 之间建立自动化的集成。当用户在AIGC编辑场景中选择或多选图片时，协调器会自动调用相应的图片处理服务。

## 🏗️ 架构设计

```
┌─────────────────────────┐    事件流    ┌──────────────────────────┐
│ AigcEditingImageProvider │ ──────────► │ AigcEditingServiceCoordinator │
│ (图片选择状态管理)        │             │ (服务协调器)               │
└─────────────────────────┘             └──────────────────────────┘
                                                      │
                                                      │ 调用服务
                                                      ▼
                                        ┌─────────────────────────┐
                                        │ AigcService             │
                                        │ (图片处理服务)          │
                                        └─────────────────────────┘
```

## 🚀 已完成的集成

### 1. Provider配置

在 `lib/ui/aigc_editing/widgets/aigc_editing_scene.dart` 中已经完成了Provider配置：

```dart
// 添加服务协调器Provider - 使用ProxyProvider处理依赖
ProxyProvider2<AigcService, AigcEditingImageProvider, AigcEditingServiceCoordinator>(
  create: (context) => AigcEditingServiceCoordinator(
    imageProvider: context.read<AigcEditingImageProvider>(),
    aigcService: context.read<AigcService>(),
  ),
  update: (context, aigcService, imageProvider, previous) {
    // 如果之前的协调器存在，先释放资源
    previous?.dispose();
    return AigcEditingServiceCoordinator(
      imageProvider: imageProvider,
      aigcService: aigcService,
    );
  },
  dispose: (_, coordinator) => coordinator?.dispose(),
),
```

### 2. 自动事件处理

协调器会自动监听以下事件：

- **多选图片变化** (`AigcEditingMultiSelectEventType.multiSelectionChanged`)
- **抠图任务完成** (`AigcTaskResultMessage` with `AigcTaskType.mask`)

### 3. 智能任务分发

- **批量处理**：多选图片时，为每张图片提交缩略图生成任务和抠图任务（智能优先级）
- **抠图监听**：自动监听抠图任务完成状态，跟踪批量处理进度

## 📋 功能特性

### ✅ 已实现的功能

1. **自动资源管理**：Provider自动处理协调器的创建和销毁
2. **事件监听**：实时监听图片选择状态变化
3. **智能任务调度**：根据选择类型自动选择合适的处理策略
4. **错误处理**：包含完整的异常捕获和日志记录
5. **性能优化**：批量处理时避免立即执行，减少系统负载
6. **抠图任务监听**：自动监听抠图任务完成状态，跟踪批量处理进度
7. **批量状态管理**：智能管理批量抠图处理状态，提供完成通知

### 📊 日志输出

协调器会输出详细的日志信息：

```
🎯 AigcEditingServiceCoordinator 初始化完成
📸 多选图片变化: 3 张图片被选中
🔄 开始批量处理 3 张图片
🚀 批量抠图处理已开始，共 3 个待完成任务
✅ 已为图片 file_001 提交缩略图任务
📋 已为图片 file_001 提交抠图任务，待完成任务数: 1
✅ 已为图片 file_002 提交缩略图任务
📋 已为图片 file_002 提交抠图任务，待完成任务数: 2
✅ 已为图片 file_003 提交缩略图任务
📋 已为图片 file_003 提交抠图任务，待完成任务数: 3
✅ 抠图任务完成: file_001
✅ 抠图任务完成: file_002
✅ 抠图任务完成: file_003
🎉 批量抠图处理完成！所有图片的抠图任务都已完成
```

## 🔧 自定义配置

### 监听批量抠图状态

协调器提供了API来监听批量抠图的处理状态：

```dart
// 获取批量抠图处理状态
bool isProcessing = coordinator.isBatchMaskProcessing;

// 获取待完成的抠图任务数量
int pendingCount = coordinator.pendingMaskTaskCount;

// 在UI中使用
if (coordinator.isBatchMaskProcessing) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('批量抠图处理中'),
      content: Text('还有 ${coordinator.pendingMaskTaskCount} 个任务待完成...'),
    ),
  );
}
```

### 修改处理逻辑

如果需要自定义处理逻辑，可以修改 `AigcEditingServiceCoordinator` 中的以下方法：

```dart
/// 批量处理图片
Future<void> _processBatchImages(Set<AigcPreviewImageItem> images) async {
  // 在这里添加你的自定义逻辑
  for (final image in images) {
    // 自定义任务类型和参数
    await _aigcService.submitTask(
      inputPath: image.originalPath,
      outputPath: _generateCustomPath(image.originalPath),
      fileId: image.fileId,
      taskType: AigcTaskType.mask, // 可以改为其他任务类型
      executeNow: false,
    );
  }
}
```

### 添加新的事件处理

```dart
void _initialize() {
  _eventSubscription = _imageProvider.eventStream.listen((event) {
    switch (event) {
      case AigcEditingMultiSelectEventType.multiSelectionChanged:
        _handleSelectedImagesChanged();
        break;
      case AigcEditingEventType.imageSelectionChanged:
        _handleSingleImageSelectionChanged();
        break;
      case AigcEditingEventType.mattingMaskBrushStrokeUpdate:
        // 添加新的事件处理
        _handleMaskUpdate();
        break;
      // 可以添加更多事件处理
    }
  });
}
```

## ⚡ 性能考虑

1. **异步处理**：所有图片处理任务都是异步执行，不会阻塞UI
2. **批量优化**：多选时使用低优先级任务，避免系统过载
3. **资源管理**：自动释放Stream订阅，防止内存泄漏
4. **错误隔离**：单个图片处理失败不会影响其他图片

## 🧪 测试建议

### 功能测试

1. **多选测试**：选择多张图片，检查日志输出和任务提交
2. **单选测试**：选择单张图片，验证高优先级任务执行
3. **取消选择测试**：取消图片选择，确认正确的日志输出
4. **混合测试**：在多选和单选之间切换，验证状态正确性

### 性能测试

1. **大量图片测试**：选择大量图片，监控内存使用和响应时间
2. **频繁切换测试**：快速切换图片选择，确保没有内存泄漏
3. **并发测试**：同时进行多个操作，验证线程安全性

## 🔍 故障排除

### 常见问题

1. **协调器未初始化**
   - 检查Provider配置是否正确
   - 确认AigcService在全局Provider中已注册

2. **事件未触发**
   - 验证AigcEditingImageProvider的事件流是否正常
   - 检查事件类型是否匹配

3. **任务提交失败**
   - 检查图片路径是否存在
   - 验证AigcService是否已正确初始化

### 调试技巧

1. **启用详细日志**：协调器已包含详细的PGLog输出
2. **断点调试**：在关键方法设置断点进行调试
3. **事件追踪**：监听eventStream的所有事件类型

## 📚 相关文件

- `lib/ui/aigc_editing/services/aigc_editing_service_coordinator.dart` - 协调器实现
- `lib/ui/aigc_editing/providers/aigc_editing_image_provider.dart` - 图片状态管理
- `lib/ui/aigc_editing/widgets/aigc_editing_scene.dart` - Provider配置
- `lib/datalayer/service/aigc_processors/aigc_service.dart` - 图片处理服务

## 🎉 总结

通过这个服务协调器，你已经成功实现了：

- ✅ 符合Flutter最佳实践的架构设计
- ✅ 自动化的图片处理流程
- ✅ 完整的错误处理和日志记录
- ✅ 灵活的扩展和自定义能力

现在，当用户在AIGC编辑场景中选择图片时，系统会自动调用相应的AigcService方法进行处理，无需手动干预！