import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item_adapter.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_mask_query_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_mask_acquisition_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC编辑服务协调器
/// 负责协调AigcEditingImageProvider和AigcService之间的交互
/// AIGC编辑服务协调器
/// 负责协调图片选择变化和相关服务的调用
class AigcEditingServiceCoordinator extends ChangeNotifier {
  final AigcEditingImageProvider _imageProvider;
  final AigcEditingMultiSelectProvider _multiSelectProvider;
  final AigcService _aigcService;
  final CurrentEditingProjectRepository _currentEditingProjectRepository;
  final MediaRepository _mediaRepository;
  final AigcImageMaskQueryProvider _maskQueryProvider;
  final AigcMaskAcquisitionProvider _maskAcquisitionProvider;
  final AigcPreviewImageItemAdapter _aigcPreviewImageItemAdapter;

  StreamSubscription<AigcEditingEventType>? _eventSubscription;
  StreamSubscription<AigcEditingMultiSelectEventType>?
      _multiSelectEventSubscription;
  StreamSubscription<AigcTaskResultMessage>? _resultStreamSubscription;

  // 批量抠图状态跟踪
  final Set<String> _pendingMaskTasks = <String>{}; // 待完成的抠图任务fileId集合
  bool _isBatchMaskProcessing = false; // 是否正在进行批量抠图

  bool get isBatchMaskProcessing => _isBatchMaskProcessing;

  AigcEditingServiceCoordinator({
    required AigcEditingImageProvider imageProvider,
    required AigcEditingMultiSelectProvider multiSelectProvider,
    required AigcService aigcService,
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required MediaRepository mediaRepository,
    required AigcImageMaskQueryProvider maskQueryProvider,
    required AigcMaskAcquisitionProvider maskAcquisitionProvider,
    required AigcPreviewImageItemAdapter aigcPreviewImageItemAdapter,
  })  : _imageProvider = imageProvider,
        _multiSelectProvider = multiSelectProvider,
        _aigcService = aigcService,
        _currentEditingProjectRepository = currentEditingProjectRepository,
        _mediaRepository = mediaRepository,
        _maskQueryProvider = maskQueryProvider,
        _maskAcquisitionProvider = maskAcquisitionProvider,
        _aigcPreviewImageItemAdapter = aigcPreviewImageItemAdapter {
    _multiSelectProvider.initializeDependencies(
      currentEditingProjectRepository: _currentEditingProjectRepository,
      aigcPreviewImageItemAdapter: _aigcPreviewImageItemAdapter,
    );
    _initialize();
  }

  /// 初始化协调器，开始监听图片选择变化事件
  void _initialize() {
    PGLog.d('🎯 AigcEditingServiceCoordinator 初始化完成');

    _eventSubscription = _imageProvider.eventStream.listen((event) {
      // 处理单选相关事件
      // 暂时不需要特殊处理
    });

    // 监听多选事件
    _multiSelectEventSubscription =
        _multiSelectProvider.eventStream.listen((event) {
      switch (event) {
        case AigcEditingMultiSelectEventType.multiSelectionChanged:
          _handleSelectedImagesChanged();
          break;
      }
    });

    // 设置抠图任务监听器
    _setupMaskTaskListener();
  }

  /// 设置抠图任务监听器
  void _setupMaskTaskListener() {
    _resultStreamSubscription = _aigcService.resultStream.listen((message) {
      // 只处理抠图任务类型的消息
      final taskType = message.payload.taskType;
      if (taskType == AigcTaskType.mask) {
        _onMaskTaskCompleted(message);
      }
    });
  }

  /// 处理抠图任务完成
  void _onMaskTaskCompleted(AigcTaskResultMessage message) {
    final fileId = message.payload.fileId;

    if (message.success) {
      PGLog.d('✅ 抠图任务完成: $fileId');
    } else {
      PGLog.e('❌ 抠图任务失败: $fileId');
    }

    // 从待完成任务集合中移除
    _pendingMaskTasks.remove(fileId);

    // 检查是否所有批量抠图任务都已完成
    if (_isBatchMaskProcessing && _pendingMaskTasks.isEmpty) {
      _onBatchMaskProcessingCompleted();
    }
    // 通知UI更新
    notifyListeners();
  }

  /// 批量抠图处理完成
  void _onBatchMaskProcessingCompleted() {
    _isBatchMaskProcessing = false;
    // 刷新数据
    _multiSelectProvider.refreshSelectImageMaskPath();

    PGLog.d('🎉 批量抠图处理完成！所有图片的抠图任务都已完成');

    // 这里可以添加批量完成后的额外处理逻辑
    // 例如：通知UI更新、显示完成提示等
  }

  /// 获取待完成的抠图任务数量
  int get pendingMaskTaskCount => _pendingMaskTasks.length;

  /// 处理多选图片变化
  void _handleSelectedImagesChanged() {
    final selectedImages = _multiSelectProvider.selectedImages;

    PGLog.d('📸 多选图片变化: ${selectedImages.length} 张图片被选中');

    // 在这里调用AigcService的相关方法
    // 例如：批量处理选中的图片
    if (selectedImages.isNotEmpty) {
      _processBatchImages(selectedImages);
    } else {
      PGLog.d('📸 所有图片已取消选择');
      // 取消选择时重置批量处理状态
      _isBatchMaskProcessing = false;
      _pendingMaskTasks.clear();
      // 立即通知UI更新状态变化
      notifyListeners();
    }
  }

  /// 批量处理图片
  Future<void> _processBatchImages(Set<AigcPreviewImageItem> images) async {
    PGLog.d('🔄 开始批量处理 ${images.length} 张图片');
    // 先清空所有的蒙版获取状态
    _maskAcquisitionProvider.clearAllMaskAcquisitionStatus();

    _pendingMaskTasks.clear();
    // 示例：为每张图片提交缩略图生成任务和抠图任务
    for (final image in images) {
      final preferredMaskPath = _maskQueryProvider.getPreferredMaskPath(image);
      // 如果已经存在蒙版，直接跳过
      if (preferredMaskPath != null) {
        continue;
      }
      // 如果图片损坏，直接跳过
      if (image.isDamaged) {
        continue;
      }

      final workspaceId =
          _currentEditingProjectRepository.currentWorkspace?.workspaceId;
      if (workspaceId == null || image.readyToDisplay == false) {
        return;
      }
      final resourceFile = _mediaRepository.getResourceFilePath(
        workspaceId,
        image.fileId,
        MediaResourceType.mask,
      );
      final outputPath = resourceFile.path;
      // 提交缩略图任务，不等待结果
      _aigcService.submitTask(
        inputPath: image.previewPath ?? image.originalPath,
        outputPath: outputPath,
        fileId: image.fileId,
        taskType: AigcTaskType.mask,
        executeNow: true,
      );
      // 设置蒙版获取状态为加载中
      _maskAcquisitionProvider.setMaskAcquisitionStatus(
          image.fileId, MaskAcquisitionStatus.loading);
      // 添加到待完成的抠图任务集合
      _pendingMaskTasks.add(image.fileId);
      PGLog.d(
          '📋 已为图片 ${image.fileId} 提交抠图任务，待完成任务数: ${_pendingMaskTasks.length}');
    }

    // 如果没有成功提交任何抠图任务，重置批量处理状态
    if (_pendingMaskTasks.isEmpty) {
      _isBatchMaskProcessing = false;

      PGLog.d('⚠️ 没有成功提交任何抠图任务，批量处理状态已重置');
    } else {
      // 开始批量抠图处理
      _isBatchMaskProcessing = true;
      PGLog.d('🚀 批量抠图处理已开始，共 ${_pendingMaskTasks.length} 个待完成任务');
    }
    // 立即通知UI更新状态变化
    notifyListeners();
  }

  /// 释放资源
  @override
  void dispose() {
    PGLog.d('🎯 AigcEditingServiceCoordinator 资源释放');
    _eventSubscription?.cancel();
    _eventSubscription = null;

    _multiSelectEventSubscription?.cancel();
    _multiSelectEventSubscription = null;

    _resultStreamSubscription?.cancel();
    _resultStreamSubscription = null;

    // 清理批量处理状态
    _pendingMaskTasks.clear();
    _isBatchMaskProcessing = false;
    super.dispose();
  }
}
