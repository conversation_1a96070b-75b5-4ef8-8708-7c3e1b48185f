import 'package:flutter/material.dart';

/// 处理状态枚举
enum ProcessState {
  /// 初始状态
  initial,

  /// 已处理状态
  processed,

  /// 加载中状态
  loading,

  /// 禁用状态
  disabled,
}

/// 蒙版显示模式
enum MaskDisplayMode {
  /// 显示叠加
  overlay,

  /// 纯色背景
  pureColor,
}

/// 蒙版背景颜色
enum MaskBackgroundColor {
  /// 透明
  transparent,

  /// 绿色
  green,

  /// 蓝色
  blue,

  /// 红色
  red,

  /// 黑色
  black,
}

extension MaskBackgroundColorExtension on MaskBackgroundColor {
  Color get color {
    return switch (this) {
      MaskBackgroundColor.transparent => Colors.transparent,
      MaskBackgroundColor.green => const Color(0xFF00FF40),
      MaskBackgroundColor.blue => const Color(0xFF3F48CC),
      MaskBackgroundColor.red => const Color(0xFFE32636),
      MaskBackgroundColor.black => const Color(0xFF0D0D0D),
    };
  }
}
