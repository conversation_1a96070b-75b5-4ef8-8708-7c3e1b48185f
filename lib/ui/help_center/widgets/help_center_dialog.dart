import 'package:flutter/material.dart';
import '../../../utils/pg_dialog/pg_dialog.dart';
import '../widgets/help_center_view.dart';
import '../../dialog/shortcut_keys_dialog.dart';
import '../../../utils/pg_log.dart';

class HelpCenterDialog {
  static const _tag = "HelpCenterDialog";

  static show() {
    PGDialog.showPositionedDialog(
      width: 176,
      height: 304,
      alignment: Alignment.bottomRight,
      padding: const EdgeInsets.only(right: 16, bottom: 72),
      tag: _tag,
      needBlur: false,
      child: HelpCenterView(
        onOptionSelected: (option) {
          PGDialog.dismiss(tag: _tag);
          switch (option) {
            case HelpCenterOption.customerService:
              PGLog.d("处理联系售后");
              break;
            case HelpCenterOption.operationGuide:
              PGLog.d("处理操作指南");
              break;
            case HelpCenterOption.newUserGuide:
              PGLog.d("处理新手引导");
              break;
            case HelpCenterOption.faq:
              PGLog.d("处理常见问题");
              break;
            case HelpCenterOption.helpCenter:
              PGLog.d("处理帮助中心");
              break;
            case HelpCenterOption.shortcutKeys:
              PGLog.d("处理快捷键");
              ShortcutKeysDialog.show();
              break;
            case HelpCenterOption.proxySettings:
              PGLog.d("处理代理设置");
              break;
            case HelpCenterOption.networkDiagnostics:
              PGLog.d("处理网络诊断");
              break;
          }
        },
      ),
    );
  }
}
