import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

import '../../../ui/core/themes/fonts.dart';

/// 帮助中心选项类型
enum HelpCenterOption {
  customerService('联系售后'),
  operationGuide('操作指南'),
  newUserGuide('新手引导'),
  faq('常见问题'),
  helpCenter('帮助中心'),
  shortcutKeys('快捷键'),
  proxySettings('代理设置'),
  networkDiagnostics('网络诊断');

  final String title;
  const HelpCenterOption(this.title);
}

class HelpCenterView extends StatefulWidget {
  final Function(HelpCenterOption)? onOptionSelected;

  const HelpCenterView({
    super.key,
    this.onOptionSelected,
  });

  @override
  State<HelpCenterView> createState() => _HelpCenterViewState();
}

class _HelpCenterViewState extends State<HelpCenterView> {
  HelpCenterOption? _hoveredOption;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 8),
          ...HelpCenterOption.values
              .map((option) => _buildOptionButton(option)),
        ],
      ),
    );
  }

  Widget _buildOptionButton(HelpCenterOption option) {
    return GestureDetector(
      onTap: () {
        widget.onOptionSelected?.call(option);
      },
      child: PlatformMouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (_) => setState(() => _hoveredOption = option),
        onExit: (_) => setState(() => _hoveredOption = null),
        child: Container(
          width: 160,
          height: 36,
          padding: const EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: _hoveredOption == option
                ? const Color(0xFF232427).withAlpha(150)
                : Colors.transparent,
          ),
          alignment: Alignment.centerLeft,
          child: Text(
            option.title,
            style: TextStyle(
              color: const Color(0xFFEBF2F5).withAlpha(150),
              fontSize: 12,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
            ),
          ),
        ),
      ),
    );
  }
}
