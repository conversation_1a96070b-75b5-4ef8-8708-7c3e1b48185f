import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/preset/model/preset_category.dart';
import 'package:turing_art/ui/preset/use_case/preset_use_case_provider.dart';
import 'package:turing_art/ui/preset/view_model/preset_import_view_model.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 预设导入列表组件
class PresetImportListWidget extends StatefulWidget {
  const PresetImportListWidget({
    super.key,
  });

  /// 显示预设导入对话框
  static void show() {
    // 使用PGDialog.showCustomDialog展示对话框
    PGDialog.showCustomDialog(
      tag: DialogTags.importPresetList, // 使用一个唯一的tag
      width: 350,
      height: 396,
      child: ChangeNotifierProvider(
        create: (context) => PresetImportViewModel(
            context.read<UnityController>(),
            PresetUseCaseProvider(apiClient: context.read<ApiClient>())),
        child: const PresetImportListWidget(),
      ),
    );
  }

  @override
  State<PresetImportListWidget> createState() => _PresetImportListWidgetState();
}

class _PresetImportListWidgetState extends State<PresetImportListWidget> {
  // 是否全选
  bool _isAllSelected = false;

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadPresets();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PresetImportViewModel>(
      builder: (context, viewModel, child) {
        return Container(
          width: 350,
          height: 396,
          decoration: BoxDecoration(
            color: const Color(0xFF1E1E1E),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              _buildHeader(),
              _buildSelectAllOption(),
              Expanded(
                child: _buildPresetList(),
              ),
              _buildBottomButtons(),
            ],
          ),
        );
      },
    );
  }

  // 构建头部
  Widget _buildHeader() {
    return Container(
      height: 40,
      padding: const EdgeInsets.only(left: 12),
      child: Row(
        children: [
          SizedBox(
            height: 18, // 确保文本高度一致
            child: Text(
              '从咻图AI账号导入',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 14,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Spacer(),
          // 调整关闭按钮位置
          Padding(
            padding: const EdgeInsets.only(right: 12, top: 4),
            child: GestureDetector(
              onTap: () => PGDialog.dismiss(tag: DialogTags.importPresetList),
              child: Image.asset(
                'assets/icons/home_window_close.png',
                width: 16,
                height: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建全选选项
  Widget _buildSelectAllOption() {
    final viewModel = context.read<PresetImportViewModel>();
    return GestureDetector(
      onTap: () {
        setState(() {
          _isAllSelected = !_isAllSelected;
          if (_isAllSelected) {
            // 全选所有可用的预设
            viewModel.selectAll();
          } else {
            // 取消全选
            viewModel.unselectAll();
          }
        });
      },
      child: Container(
        height: 24,
        padding: const EdgeInsets.only(left: 12, top: 8),
        child: Row(
          children: [
            Image.asset(
              _isAllSelected
                  ? 'assets/icons/list_select.png'
                  : 'assets/icons/list_unselect.png',
              width: 16,
              height: 16,
            ),
            const SizedBox(width: 4), // 保持一致的间距
            SizedBox(
              height: 16,
              child: Text(
                '全部预设',
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建预设列表
  Widget _buildPresetList() {
    return Consumer<PresetImportViewModel>(
      builder: (context, viewModel, child) {
        // 检查是否有预设数据
        if (viewModel.presetItems.isEmpty) {
          // 显示空数据占位图
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/icons/home_profile_into_preset.png',
                  width: 32,
                  height: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  '暂无预设',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 12,
                    height: 16 / 12,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFFEBEDF5).withOpacity(0.65),
                  ),
                ),
              ],
            ),
          );
        }

        // 原有的预设列表
        return Padding(
          padding: const EdgeInsets.only(top: 8, bottom: 8),
          child: RawScrollbar(
            thumbColor: Colors.white.withOpacity(0.3),
            radius: const Radius.circular(2),
            thickness: 4,
            controller: _scrollController,
            thumbVisibility: true,
            child: ListView.builder(
              controller: _scrollController,
              itemCount: viewModel.presetItems.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                final category = viewModel.presetItems[index];
                return _buildGroupItem(category);
              },
            ),
          ),
        );
      },
    );
  }

  // 构建分组项
  Widget _buildGroupItem(PresetCategoryUI category) {
    final viewModel = context.read<PresetImportViewModel>();
    final isExpanded = viewModel.isExpanded(category.id);
    final isGroupSelected = _isGroupSelected(category);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分组标题
        GestureDetector(
          onTap: () {
            setState(() {
              viewModel.toggleExpand(category.id);
            });
          },
          child: Container(
            height: 32,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                // 展开/折叠图标
                Transform.rotate(
                  angle: isExpanded ? 90 * (3.1415926 / 180) : 0,
                  child: Image.asset(
                    'assets/icons/page_next.png',
                    width: 16,
                    height: 16,
                  ),
                ),
                const SizedBox(width: 4), // 左间距4
                // 添加分类选择框
                GestureDetector(
                  onTap: () => _toggleGroupSelection(category),
                  child: Image.asset(
                    isGroupSelected
                        ? 'assets/icons/list_select.png'
                        : 'assets/icons/list_unselect.png',
                    width: 16,
                    height: 16,
                  ),
                ),
                const SizedBox(width: 4), // 右间距4
                SizedBox(
                  height: 16,
                  child: Text(
                    category.name,
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // 预设列表
        if (isExpanded)
          ...category.presets.map((preset) => _buildPresetItem(preset)),
      ],
    );
  }

  // 构建预设项
  Widget _buildPresetItem(PresetUI preset) {
    final viewModel = context.read<PresetImportViewModel>();
    final isSelected = viewModel.isPresetSelected(preset.id);

    return GestureDetector(
      onTap: preset.supported
          ? () {
              setState(() {
                if (isSelected) {
                  viewModel.unselectPreset(preset.id);
                } else {
                  viewModel.selectPreset(preset.id);
                }

                // 更新全选状态
                _updateAllSelectedState();
              });
            }
          : null,
      child: Container(
        height: 32,
        padding: const EdgeInsets.only(left: 32, right: 12),
        child: Row(
          children: [
            Image.asset(
              isSelected && preset.supported
                  ? 'assets/icons/list_select.png'
                  : 'assets/icons/list_unselect.png',
              width: 16,
              height: 16,
              color: preset.supported ? null : Colors.white.withOpacity(0.4),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: SizedBox(
                height: 16, // 设置文本高度为16
                child: Text(
                  preset.name,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 12,
                    color: preset.supported
                        ? Colors.white
                        : Colors.white.withOpacity(0.4),
                    fontWeight: FontWeight.w400,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            if (!preset.supported)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: const Color(0xFF333333),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: SizedBox(
                  height: 16, // 设置文本高度为16
                  child: Text(
                    '不兼容',
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 10,
                      color: Colors.white.withOpacity(0.65),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 构建底部按钮
  Widget _buildBottomButtons() {
    final viewModel = context.read<PresetImportViewModel>();
    final hasSelected = viewModel.selectedPresets.isNotEmpty;

    return Container(
      height: 56,
      padding: const EdgeInsets.only(right: 12, bottom: 12),
      alignment: Alignment.bottomRight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // 取消按钮
          SizedBox(
            width: 75,
            height: 32,
            child: TextButton(
              onPressed: () =>
                  PGDialog.dismiss(tag: DialogTags.importPresetList),
              style: TextButton.styleFrom(
                backgroundColor: const Color(0xFF2B2B2B),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                padding: EdgeInsets.zero,
              ),
              child: Text(
                '取消',
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 确认按钮
          Container(
            width: 80,
            height: 40,
            decoration: BoxDecoration(
              color: hasSelected
                  ? const Color(0xFFF72561) // 高亮状态
                  : const Color(0xFFF72561).withOpacity(0.3), // 禁用状态
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton(
              onPressed: hasSelected
                  ? () async {
                      final success = await viewModel.importSelectedPresets();
                      if (success && mounted) {
                        PGDialog.dismiss(tag: DialogTags.importPresetList);
                      }
                    }
                  : null, // 禁用按钮
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                backgroundColor: Colors.transparent,
                disabledForegroundColor: Colors.white.withOpacity(0.5),
              ),
              child: Text(
                "确认",
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  height: 16 / 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 更新全选状态
  void _updateAllSelectedState() {
    final viewModel = context.read<PresetImportViewModel>();
    _isAllSelected = viewModel.isAllSelected();
  }

  void _loadPresets() {
    PGDialog.showLoading();
    Future.microtask(() async {
      await context.read<PresetImportViewModel>().loadPresets();
      PGDialog.dismiss();
    });
  }

  // 检查分组是否被选中（所有可兼容的预设都被选中）
  bool _isGroupSelected(PresetCategoryUI category) {
    final viewModel = context.read<PresetImportViewModel>();
    return viewModel.isAllSelectedInCategory(category.id);
  }

  // 切换分组选择状态
  void _toggleGroupSelection(PresetCategoryUI category) {
    final viewModel = context.read<PresetImportViewModel>();
    final isSelected = _isGroupSelected(category);

    setState(() {
      if (isSelected) {
        // 取消选择该分组的所有预设
        viewModel.unselectAllInCategory(category.id);
      } else {
        // 选择该分组的所有预设
        viewModel.selectAllInCategory(category.id);
      }

      // 更新全选状态
      _updateAllSelectedState();
    });
  }
}
