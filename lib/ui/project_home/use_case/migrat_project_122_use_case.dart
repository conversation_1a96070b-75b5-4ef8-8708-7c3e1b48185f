import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ui/project_home/providers/migrat_project_provider.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

// 迁移工程1至2版本
// 迁移需求背景：
// 将Unity保存的工程Json整体迁移至数据库中存储，放弃Json文件存储，使用数据库存储。
// 在本次迁移过程中，需要将该目标工程的Json数据迁移至数据库中。

// 定义用于在隔离区执行的函数
Future<Map<String, dynamic>> _parseWorkspaceJson(String filePath) async {
  final file = File(filePath);
  final jsonString = await file.readAsString();
  return jsonDecode(jsonString) as Map<String, dynamic>;
}

/// 项目迁移用例：从版本1.2.2迁移到版本2.0.0
class MigratProject122UseCase implements MigratProjectStep {
  final ProjectRepository _projectRepository;

  MigratProject122UseCase(this._projectRepository);

  @override
  int get fromVersion => 1;

  @override
  int get toVersion => 2;

  @override
  Future<bool> invoke(String projectId) async {
    final stopwatch = Stopwatch()..start();
    PGLog.i('开始执行项目迁移: 从版本 $fromVersion 到版本 $toVersion');

    try {
      // 获取项目目录（同步操作）
      final projectDir = FileManager().getProjectDirectory(projectId);
      if (!projectDir.existsSync()) {
        PGLog.w('项目目录不存在: ${projectDir.path}');
        return false;
      }
      PGLog.i('获取项目目录耗时: ${stopwatch.elapsedMilliseconds}ms');

      // 获取项目信息（异步操作）
      final projectInfo = await _projectRepository.getProjectById(projectId);
      if (projectInfo == null) {
        PGLog.w('项目信息不存在: $projectId');
        return false;
      }
      PGLog.i('获取项目信息耗时: ${stopwatch.elapsedMilliseconds}ms');

      // 检查workspace.json文件是否存在
      final workspaceJson = File('${projectDir.path}/workspace.json');
      if (!workspaceJson.existsSync()) {
        PGLog.w('workspace.json文件不存在，跳过迁移');
        return false;
      }
      PGLog.i('检查文件存在耗时: ${stopwatch.elapsedMilliseconds}ms');

      // 在隔离区中读取和解析JSON文件
      final jsonMap = await compute(
        _parseWorkspaceJson,
        workspaceJson.path,
      );
      PGLog.i('读取和解析JSON文件耗时: ${stopwatch.elapsedMilliseconds}ms');

      // 创建Workspace对象并更新版本
      final updatedWorkspace = Workspace.fromJson(jsonMap);
      PGLog.i('创建和更新Workspace对象耗时: ${stopwatch.elapsedMilliseconds}ms');

      // 更新数据库
      await _projectRepository.updateProject(
        projectInfo.copyWith(
          workspaceVersion: toVersion,
        ),
      );

      // 更新数据库
      await _projectRepository.updateWorkspace(
        updatedWorkspace,
      );

      PGLog.i('更新数据库耗时: ${stopwatch.elapsedMilliseconds}ms');

      PGLog.i('迁移完成，总耗时: ${stopwatch.elapsedMilliseconds}ms');
      return true;
    } catch (e, stackTrace) {
      PGLog.e('迁移失败: $e\n$stackTrace');
      return false;
    } finally {
      stopwatch.stop();
    }
  }
}
