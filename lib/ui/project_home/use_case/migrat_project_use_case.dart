import 'package:turing_art/datalayer/domain/models/project_info/project_version.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ui/project_home/providers/migrat_project_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 迁移状态枚举
enum MigrationStatus {
  /// 无需迁移
  none,

  /// 迁移失败
  failed,

  /// 迁移成功
  success,
}

/// 项目迁移用例
/// 负责执行具体的迁移步骤，管理迁移流程
class MigratProjectUseCase {
  final ProjectRepository _projectRepository;
  final MigratProjectProvider _migrationProvider;

  MigratProjectUseCase(this._projectRepository)
      : _migrationProvider = MigratProjectProvider(_projectRepository);

  /// 执行从指定版本到目标版本的迁移
  /// [projectId] 项目ID
  /// 返回迁移是否成功
  Future<MigrationStatus> invoke(String projectId) async {
    final stopwatch = Stopwatch()..start();
    final currentVersion = ProjectVersion.latestVersion;
    try {
      // 首先获取项目信息
      final projectInfo = await _projectRepository.getProjectById(projectId);
      if (projectInfo == null) {
        PGLog.w('项目不存在: $projectId');
        return MigrationStatus.failed;
      }

      // 检查当前版本是否为最新版本
      if (projectInfo.workspaceVersion == currentVersion) {
        PGLog.w(
            '项目当前版本 ${projectInfo.workspaceVersion} 与预期起始版本 $currentVersion 不符');
        return MigrationStatus.none;
      }

      // 获取所有需要的迁移步骤
      final steps = _getMigrationSteps(
        projectInfo.workspaceVersion,
        currentVersion,
      );
      if (steps.isEmpty) {
        PGLog.w(
            '未找到从版本 ${projectInfo.workspaceVersion} 到版本 $currentVersion 的迁移步骤, 证明不需要迁移，直接继续流程');
        return MigrationStatus.none;
      }

      // 按顺序执行每个迁移步骤
      for (final step in steps) {
        PGLog.i('执行迁移步骤: 从版本 ${step.fromVersion} 到版本 ${step.toVersion}');
        final stepStopwatch = Stopwatch()..start();

        final success = await step.invoke(projectId);
        if (!success) {
          PGLog.e('迁移步骤执行失败: 从版本 ${step.fromVersion} 到版本 ${step.toVersion}');
          return MigrationStatus.failed;
        }

        // 验证迁移后的版本
        final updatedProject =
            await _projectRepository.getProjectById(projectId);
        if (updatedProject == null ||
            updatedProject.workspaceVersion != step.toVersion) {
          PGLog.e(
              '迁移步骤执行后版本验证失败: 期望版本 ${step.toVersion}, 实际版本 ${updatedProject?.workspaceVersion}');
          return MigrationStatus.failed;
        }

        PGLog.i('迁移步骤完成，耗时: ${stepStopwatch.elapsedMilliseconds}ms');
      }

      PGLog.i('迁移完成，总耗时: ${stopwatch.elapsedMilliseconds}ms');
      return MigrationStatus.success;
    } catch (e, stackTrace) {
      PGLog.e('迁移过程发生错误: $e\n$stackTrace');
      return MigrationStatus.failed;
    } finally {
      stopwatch.stop();
    }
  }

  /// 获取从指定版本到目标版本所需的所有迁移步骤
  List<MigratProjectStep> _getMigrationSteps(int fromVersion, int toVersion) {
    final allSteps = _migrationProvider.getMigrationSteps();
    final steps = <MigratProjectStep>[];
    var currentVersion = fromVersion;

    while (currentVersion < toVersion) {
      // 查找下一个可用的迁移步骤
      final nextStep = allSteps.firstWhere(
        (step) => step.fromVersion == currentVersion,
        orElse: () => throw Exception('未找到从版本 $currentVersion 的迁移步骤'),
      );

      steps.add(nextStep);
      currentVersion = nextStep.toVersion;

      // 如果已经达到或超过目标版本，结束循环
      if (currentVersion >= toVersion) {
        break;
      }
    }

    return steps;
  }
}
