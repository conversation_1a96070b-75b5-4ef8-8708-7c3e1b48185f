import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:tuple/tuple.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/repository/photo_thumbnail_repository.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

// 请求封面模型
class _ThumbnailRequest {
  final String projectId;
  final String photoId;
  final Completer<String> completer;

  _ThumbnailRequest(this.projectId, this.photoId, this.completer);
}

class CoverThumbnailService extends ChangeNotifier {
  final UnityController _unityController;
  final PhotoThumbnailRepository _photoThumbnailRepository;
  static const String fileName = 'ps_mid_icon';
  static const String pngExtension = '.png';
  static const String sgiExtension = '.sgi';
  static const String thumb = 'thumb';

  // 存储缩略图路径
  final Map<String, String> _thumbnailPaths = {};
  // 存储正在进行的转换任务
  final Map<String, bool> _conversionInProgress = {};

  // 添加一个转换队列
  final Queue<_ThumbnailRequest> _conversionQueue = Queue();
  bool _isProcessingQueue = false;

  CoverThumbnailService(
    this._unityController,
    this._photoThumbnailRepository,
  );

  // 获取缩略图路径
  Future<String> getThumbnailPath(String projectId, String photoId) async {
    if (photoId.isEmpty || projectId.isEmpty) {
      return '';
    }

    final key = '$projectId:$photoId';

    // 先检查缓存
    if (_thumbnailPaths.containsKey(key)) {
      final path = _thumbnailPaths[key]!;
      if (path.isNotEmpty && File(path).existsSync()) {
        return path;
      }
      _thumbnailPaths.remove(key);
    }

    // 检查文件是否已存在
    final thumbnailPath = await _fetchThumbnailWithPhotoId(projectId, photoId);
    if (thumbnailPath.isNotEmpty && File(thumbnailPath).existsSync()) {
      _thumbnailPaths[key] = thumbnailPath;
      notifyListeners();
      return thumbnailPath;
    }

    // 如果需要转换，加入队列
    if (_conversionInProgress[key] != true) {
      final completer = Completer<String>();
      _conversionQueue.add(_ThumbnailRequest(projectId, photoId, completer));
      _conversionInProgress[key] = true;

      // 启动队列处理
      _processQueue();

      // 等待转换完成
      return completer.future;
    }

    return '';
  }

  // 添加队列处理方法
  Future<void> _processQueue() async {
    if (_isProcessingQueue) {
      return;
    }

    _isProcessingQueue = true;

    while (_conversionQueue.isNotEmpty) {
      final request = _conversionQueue.first;
      try {
        final result =
            await _startThumbnailConversion(request.projectId, request.photoId);
        request.completer.complete(result);
      } catch (e) {
        request.completer.complete('');
      } finally {
        _conversionQueue.removeFirst();
        _conversionInProgress.remove('${request.projectId}:${request.photoId}');
      }
    }

    _isProcessingQueue = false;
  }

  // 修改转换方法
  Future<String> _startThumbnailConversion(
      String projectId, String photoId) async {
    final paths = _getThumbnailFilePath(projectId, photoId);
    if (paths == null) {
      return '';
    }

    final args = {
      'workspaceId': projectId,
      'fileId': photoId,
      'encryptedFile': paths.item1,
      'outputFilePath': paths.item2,
    };

    try {
      await _unityController.sendMessageWithResponse<MessageFromUnity>(
        MessageToUnity(
          method: 'GenerateCoverImage',
          args: jsonEncode(args),
          completed: const Uuid().v4(),
        ),
        UnityMessage.onThumbnailConversion,
      );

      // 验证转换是否成功
      if (File(paths.item2).existsSync()) {
        return paths.item2;
      }
    } catch (e) {
      PGLog.e('Thumbnail conversion failed: $e');
    }

    return '';
  }

  @override
  void dispose() {
    _thumbnailPaths.clear();
    _conversionInProgress.clear();
    super.dispose();
  }

  Future<String> _fetchThumbnailWithPhotoId(
    String projectId,
    String photoId,
  ) async {
    try {
      final paths = _getThumbnailFilePath(projectId, photoId);
      if (paths == null) {
        return '';
      }

      // 先检查文件系统
      if (File(paths.item2).existsSync()) {
        _photoThumbnailRepository.setThumbnail(paths.item2, photoId);
        return paths.item2;
      }

      // 再检查缓存
      final thumbnail =
          await _photoThumbnailRepository.fetchThumbnailWithPhotoId(photoId);
      if (thumbnail != null &&
          thumbnail.isNotEmpty &&
          File(thumbnail).existsSync()) {
        return thumbnail;
      }

      return '';
    } catch (e) {
      return '';
    }
  }

  Tuple2<String, String>? _getThumbnailFilePath(
    String projectId,
    String photoId,
  ) {
    final userDir = FileManager().getUserRootDir();
    if (userDir == null) {
      return null;
    }
    final paths = Tuple2(
      path.join(
        userDir.path,
        projectId,
        photoId,
        thumb,
        '$fileName$sgiExtension',
      ),
      path.join(
        userDir.path,
        projectId,
        photoId,
        thumb,
        '$fileName$pngExtension',
      ),
    );
    return paths;
  }
}
