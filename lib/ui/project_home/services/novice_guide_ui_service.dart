import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/arrow_position.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/ui/common/guide_bubble/novice_guide_bubble.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_novice_guide_dialog.dart';
import 'package:turing_art/utils/pg_bubble/pg_bubble_overlay.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 新手引导UI服务
/// 负责处理新手引导相关的UI展示逻辑
class NoviceGuideUIService {
  // 使用静态变量跟踪显示状态
  static bool _isShowingGuide = false;

  // 存储当前显示的项目弹窗OverlayEntry
  static OverlayEntry? _currentDialogOverlayEntry;

  /// 计算对话框位置和大小，确保能完全覆盖目标区域并保持比例（原本item比列290*237）
  static Rect _calculateDialogRect(Rect targetRect) {
    // 计算左上角位置，中心对齐，扩12
    final dialogLeft = targetRect.left - 12;
    final dialogTop = targetRect.top - 12;

    return Rect.fromPoints(
      Offset(dialogLeft, dialogTop),
      Offset(
        dialogLeft + targetRect.width + 24,
        dialogTop + targetRect.height + 24,
      ),
    );
  }

  /// 关闭当前显示的项目弹窗
  static void dismissCurrentDialog() {
    if (_currentDialogOverlayEntry != null) {
      _currentDialogOverlayEntry!.remove();
      _currentDialogOverlayEntry = null;
    }
  }

  /// 显示新手引导
  static Future<void> showGuide({
    required BuildContext context,
    required int step,
    required int totalSteps,
    required ProjectInfo project,
    required Rect targetRect,
    required String description,
    required String coverThumbnailPath,
    required Function(int) markStepShown,
    required Function() markGuideCompleted,
    VoidCallback? onDismiss,
    VoidCallback? onEnterEdit,
  }) async {
    // 检查步骤是否有效
    if (step < 0 || step >= totalSteps) {
      return;
    }

    // 避免重复显示
    if (_isShowingGuide) {
      PGLog.d('新手引导已经在显示中，避免重复显示');
      return;
    }

    _isShowingGuide = true;

    // 第一步显示项目弹窗
    Rect bubbleTargetRect = targetRect;

    // 如果是第一步，显示项目弹窗
    if (step == 0) {
      // 计算弹窗位置和大小
      final dialogRect = _calculateDialogRect(targetRect);
      bubbleTargetRect = dialogRect;

      // 显示项目弹窗
      _currentDialogOverlayEntry = NoviceGuideDialog.showOnView(
          context: context,
          project: project,
          dialogRect: dialogRect,
          coverThumbnailPath: coverThumbnailPath,
          onDoubleTap: () {
            // 关闭项目弹窗
            dismissCurrentDialog();
            // 关闭气泡
            PGBubbleOverlay.dismiss(callback: onDismiss);
            // 执行项目选择逻辑
            onEnterEdit?.call();
          });
    }

    // 显示气泡提示（对所有步骤都执行）
    if (context.mounted) {
      _showGuideBubble(
        context: context,
        step: step,
        totalSteps: totalSteps,
        targetRect: bubbleTargetRect,
        description: description,
        onDismiss: () {
          dismissCurrentDialog(); // 关闭项目弹窗
          _isShowingGuide = false;
          if (onDismiss != null) {
            onDismiss();
          }
        },
        onNext: onEnterEdit,
      );
    }

    // 标记该步骤已显示
    markStepShown(step);

    // 如果是最后一步，标记新手引导已完成
    if (step == totalSteps - 1) {
      markGuideCompleted();
    }
  }

  /// 显示新手引导气泡
  static void _showGuideBubble({
    required BuildContext context,
    required int step,
    required int totalSteps,
    required Rect targetRect,
    required String description,
    required VoidCallback onDismiss,
    VoidCallback? onNext,
  }) {
    // 定义气泡宽高
    const double bubbleWidth = 292;
    const double bubbleHeight = 88;

    // 记录使用的context类型
    PGLog.d(
        '_showGuideBubble使用的context类型: ${context.runtimeType}, hashCode: ${context.hashCode}');

    // 确保在主线程中运行，避免层级问题
    Future.microtask(() {
      if (!context.mounted) {
        PGLog.d('_showGuideBubble context已经不可用');
        return;
      }

      PGLog.d('开始显示气泡，目标区域: $targetRect, 描述: $description');

      PGBubbleOverlay.show(
        context: context,
        targetRect: targetRect,
        bubbleWidth: bubbleWidth,
        bubbleHeight: bubbleHeight,
        arrowPosition: ArrowPosition.left,
        arrowOffset: 28,
        backgroundColor: const Color(0xFF333333),
        borderColor: const Color(0x1AFFFFFF),
        content: Material(
          color: Colors.transparent,
          child: NoviceGuideBubble(
            description: description,
            currentStep: step + 1, // 显示给用户的步骤从1开始
            totalSteps: totalSteps,
            width: bubbleWidth,
            height: bubbleHeight,
            onCancel: () {
              PGLog.d('气泡取消按钮被点击');
              dismissCurrentDialog(); // 关闭项目弹窗
              PGBubbleOverlay.dismiss(callback: onDismiss);
            },
            onNext: () {
              PGLog.d('气泡下一步按钮被点击');
              dismissCurrentDialog(); // 关闭项目弹窗
              PGBubbleOverlay.dismiss(callback: () {
                onDismiss();
                if (onNext != null) {
                  onNext();
                }
              });
            },
            nextText: (step == totalSteps - 1) ? '知道了' : '下一步',
          ),
        ),
        showOverlayBackground: false, // 不显示半透明背景，避免覆盖NoviceGuideDialog
        highlightTarget: false,
        dismissOnTap: false,
        onDismiss: onDismiss,
        tag: 'novice_guide_bubble', // 添加唯一标签
      );

      PGLog.d('气泡已显示');
    });
  }
}
