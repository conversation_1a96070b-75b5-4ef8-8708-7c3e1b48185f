import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ui/project_home/use_case/migrat_project_122_use_case.dart';

/// 迁移步骤的抽象基类
abstract class MigratProjectStep {
  /// 迁移前的版本号
  int get fromVersion;

  /// 迁移后的版本号
  int get toVersion;

  /// 执行迁移
  Future<bool> invoke(String projectId);
}

/// 迁移步骤提供者
class MigratProjectProvider {
  final ProjectRepository _projectRepository;

  MigratProjectProvider(this._projectRepository);

  /// 获取所有迁移步骤
  /// 返回按fromVersion排序的迁移步骤列表
  List<MigratProjectStep> getMigrationSteps() {
    return [
      MigratProject122UseCase(_projectRepository),
      // 在这里添加新的迁移步骤
      // 例如：MigratProject123UseCase(_projectRepository),
    ]..sort((a, b) => a.fromVersion.compareTo(b.fromVersion));
  }

  /// 获取指定版本范围的迁移步骤
  /// [fromVersion] 起始版本
  /// [toVersion] 目标版本
  MigratProjectStep? getMigrationStep(int fromVersion, int toVersion) {
    return getMigrationSteps().firstWhere(
      (step) => step.fromVersion == fromVersion && step.toVersion == toVersion,
      orElse: () => throw Exception('未找到从版本 $fromVersion 到版本 $toVersion 的迁移步骤'),
    );
  }

  /// 获取所有可用的迁移版本范围
  List<Map<String, int>> getAvailableVersionRanges() {
    return getMigrationSteps()
        .map((step) => {
              'from': step.fromVersion,
              'to': step.toVersion,
            })
        .toList();
  }

  /// 检查是否存在从指定版本到目标版本的迁移路径
  bool hasMigrationPath(int fromVersion, int toVersion) {
    final steps = getMigrationSteps();
    if (steps.isEmpty) {
      return false;
    }

    // 如果目标版本小于起始版本，返回false
    if (toVersion < fromVersion) {
      return false;
    }

    // 获取所有可用的起始版本
    final availableFromVersions = steps.map((s) => s.fromVersion).toSet();

    // 检查是否存在从fromVersion到toVersion的路径
    var currentVersion = fromVersion;
    while (currentVersion < toVersion) {
      // 查找下一个可用的迁移步骤
      final nextStep =
          steps.where((step) => step.fromVersion == currentVersion).firstOrNull;

      if (nextStep == null) {
        return false;
      }

      // 更新当前版本
      currentVersion = nextStep.toVersion;

      // 如果已经达到或超过目标版本，返回true
      if (currentVersion >= toVersion) {
        return true;
      }

      // 如果当前版本不在可用版本中，返回false
      if (!availableFromVersions.contains(currentVersion)) {
        return false;
      }
    }

    return currentVersion == toVersion;
  }
}
