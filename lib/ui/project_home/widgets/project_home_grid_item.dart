import 'dart:io';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

import '../../../datalayer/domain/models/project_info/project_info.dart';
import '../../../ui/core/themes/fonts.dart';
import '../../../utils/pg_log.dart';
import 'project_home_more_action_view.dart';

class ProjectHomeGridItem extends StatelessWidget {
  final ProjectInfo project;
  final DateFormat _dateFormat = DateFormat('yyyy.MM.dd HH:mm');
  final double _imageContainerRatio = 188 / 264;
  final Function onDeleteClick;
  final Function onRenameClick;
  final Function(bool) onCheckboxClick;
  final bool isSelected;

  ProjectHomeGridItem({
    super.key,
    required this.project,
    required this.onDeleteClick,
    required this.onRenameClick,
    required this.onCheckboxClick,
    required this.isSelected,
  });

  String _formatDate(int? timestamp) {
    if (timestamp == null) return '';
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return _dateFormat.format(dateTime);
  }

  Widget _buildImage(String path) {
    return Image.file(
      File(path),
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        PGLog.e('加载图片失败: $error');
        return Image.asset(
          '',
          fit: BoxFit.cover,
        );
      },
      // 添加缓存
      cacheWidth: 300,
      // 根据实际尺寸调整
      cacheHeight: 300,
      filterQuality: FilterQuality.high,
    );
  }

  Size _getItemSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = (screenWidth - 12 * 4) / 4;
    return Size(itemWidth, itemWidth * 264 / 280);
  }

  void _showContextMenu(BuildContext context, TapDownDetails details) {
    ProjectHomeMoreActionView.show(
      context: context,
      position: details.globalPosition,
      models: [
        MenuItemModel(
          icon: Icons.edit_outlined,
          text: '重命名',
          textColor: Colors.white,
          onTap: onRenameClick,
        ),
        MenuItemModel(
          icon: Icons.delete_outline,
          text: '删除',
          textColor: const Color(0xFFFF4D4F),
          onTap: onDeleteClick,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final coverImages = project.coverImages;
    final itemSize = _getItemSize(context);
    final imageContainerHeight = itemSize.height * _imageContainerRatio;
    final smallImageWidth = (itemSize.width) / 3 - 1;

    return GestureDetector(
      onSecondaryTapDown: (details) => _showContextMenu(context, details),
      child: PlatformMouseRegion(
        cursor: SystemMouseCursors.click,
        child: Column(
          children: [
            Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF222526),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  height: imageContainerHeight,
                  child: Row(
                    children: [
                      // 左侧大图
                      Expanded(
                        flex: 2,
                        child: SizedBox(
                          height: imageContainerHeight,
                          child: ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              bottomLeft: Radius.circular(16),
                            ),
                            child: coverImages.isNotEmpty
                                ? _buildImage(coverImages[0])
                                : _buildImage(''),
                          ),
                        ),
                      ),
                      // 右侧两张小图
                      SizedBox(
                        width: 1,
                        height: imageContainerHeight,
                        child: const ColoredBox(color: Colors.black),
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(
                          children: [
                            SizedBox(
                              height: imageContainerHeight / 2 - 0.5,
                              child: ClipRRect(
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(16),
                                ),
                                child: coverImages.length > 1
                                    ? _buildImage(coverImages[1])
                                    : const SizedBox.shrink(),
                              ),
                            ),
                            SizedBox(
                              width: smallImageWidth,
                              height: 1,
                              child: const ColoredBox(color: Colors.black),
                            ),
                            SizedBox(
                              height: imageContainerHeight / 2 - 0.5,
                              child: ClipRRect(
                                borderRadius: const BorderRadius.only(
                                  bottomRight: Radius.circular(16),
                                ),
                                child: coverImages.length > 2
                                    ? _buildImage(coverImages[2])
                                    : const SizedBox.shrink(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        padding: const EdgeInsets.only(left: 8, top: 8),
                        child: Text(
                          project.name,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: Fonts.semiBold,
                            fontFamily: Fonts.defaultFontFamily,
                          ),
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        padding: const EdgeInsets.only(left: 8, top: 0),
                        child: Text(
                          _formatDate(
                              project.updateDate?.millisecondsSinceEpoch),
                          style: TextStyle(
                            color: const Color(0xFFEBF2F5).withAlpha(75),
                            fontSize: 12,
                            fontWeight: Fonts.light,
                            fontFamily: Fonts.defaultFontFamily,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                GestureDetector(
                  onTapDown: (details) => _showContextMenu(context, details),
                  child: Container(
                    margin: const EdgeInsets.only(right: 4),
                    child: Image.asset("assets/icons/project_item_more.png"),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
