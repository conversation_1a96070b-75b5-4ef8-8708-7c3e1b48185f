import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../view_models/home_view_model.dart';
import 'file_choose_option.dart';
import 'project_home_create_button.dart';

class ProjectHomeCreateView extends StatefulWidget {
  const ProjectHomeCreateView({
    super.key,
  });

  @override
  State<ProjectHomeCreateView> createState() => _ProjectHomeCreateViewState();
}

class _ProjectHomeCreateViewState extends State<ProjectHomeCreateView>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _backgroundOpacity;
  late final Animation<double> _optionsOpacity;
  late final Animation<Offset> _optionsSlide;
  bool _isExpanded = false;
  bool _forceExpandButton = false;

  @override
  void initState() {
    super.initState();

    // 1. 首先初始化控制器
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    )..addListener(() {
        setState(() {}); // 确保动画更新时重建
      });

    // 2. 创建一个通用的曲线
    final curve = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    // 3. 初始化所有动画
    _backgroundOpacity = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(curve);

    _optionsOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(curve);

    _optionsSlide = Tween<Offset>(
      begin: const Offset(0, 16),
      end: const Offset(0, 0),
    ).animate(curve);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleOptions() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  void _handleOptionSelected() {
    setState(() {
      _isExpanded = false;
      _forceExpandButton = true;
    });
    _controller.reverse();

    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          _forceExpandButton = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 背景层 - 添加 IgnorePointer
        IgnorePointer(
          child: Container(
            color: Colors.black.withOpacity(_backgroundOpacity.value),
          ),
        ),

        // 选项层
        Positioned(
          left: 0,
          right: 0,
          bottom: 128,
          child: Transform.translate(
            offset: _optionsSlide.value,
            child: Opacity(
              opacity: _optionsOpacity.value,
              child: FileChooseOption(
                onOptionSelected: _handleOptionSelected,
                homeViewModel: context.read<HomeViewModel>(),
              ),
            ),
          ),
        ),

        // 按钮层 - 保持可交互
        Positioned(
          left: 0,
          right: 0,
          bottom: 50,
          child: Center(
            child: ProjectHomeCreateButton(
              onTap: _toggleOptions,
              text: '新建项目',
              forceExpand: _forceExpandButton,
            ),
          ),
        ),
      ],
    );
  }
}
