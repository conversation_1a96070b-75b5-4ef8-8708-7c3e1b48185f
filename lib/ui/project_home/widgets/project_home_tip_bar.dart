import 'package:flutter/material.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/pg_log.dart';

class ProjectHomeTipBar extends StatelessWidget {
  const ProjectHomeTipBar({
    super.key,
    required this.onClose,
  });

  final VoidCallback onClose;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 65,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF05D4FF).withAlpha(20),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // 主要内容
          Row(
            children: [
              const SizedBox(width: 12),
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: const Color(0xFF05D4FF).withAlpha(25),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Image.asset(
                  'assets/icons/home_mine_notice.png',
                ),
              ),
              const SizedBox(width: 12),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Pad端内测体验中',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF05D4FF),
                    ),
                  ),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: '该内测版本预计 ',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFFEBF2F5).withAlpha(150),
                          ),
                        ),
                        TextSpan(
                          text: AppInfoExtension.getExpiredTimeForTipBar(),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF05D4FF), // 蓝色
                          ),
                        ),
                        TextSpan(
                          text: ' 过期，届时请联系提供方或邮件咨询 <EMAIL> 以继续使用。',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color:
                                const Color(0xFFEBF2F5).withAlpha(150), // 原有颜色
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          // 关闭按钮
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () {
                PGLog.d('关闭');
                onClose();
              },
              child: Container(
                padding: const EdgeInsets.all(8), // 扩大点击区域
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.close,
                  size: 24,
                  color: const Color(0xFFEBF2F5).withAlpha(75),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
