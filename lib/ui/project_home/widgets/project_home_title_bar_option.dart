import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';

class ProjectHomeTitleBarOption extends StatelessWidget {
  final bool showProgress;
  final bool showLogo;
  final bool showReward;
  final bool showPurchase;
  final bool showProfile;
  final Function? rewardCardClick;
  final Function? purchaseCardClick;
  final Function? profileClick;
  final Function? exportProgressClick;

  final GlobalKey profileButtonKey = GlobalKey();

  ProjectHomeTitleBarOption(
      {super.key,
      this.showProgress = false,
      this.showLogo = false,
      this.showReward = false,
      this.showPurchase = false,
      this.showProfile = false,
      this.rewardCardClick,
      this.purchaseCardClick,
      this.profileClick,
      this.exportProgressClick});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(width: 6),
          if (showLogo) ...[
            SizedBox(
              width: 80,
              height: 32,
              child: Image.asset("assets/icons/text_logo.png"),
            ),
          ],
          Expanded(child: Container()),
          if (showReward) ...[
            DebounceClickWidget(
              onTap: () {
                rewardCardClick?.call();
              },
              child: const RewardCardOption(),
            ),
          ],
          if (showReward && (showPurchase || showProfile || showProgress))
            const SizedBox(width: 12),
          if (showPurchase) ...[
            DebounceClickWidget(
              onTap: () {
                purchaseCardClick?.call();
              },
              child: const PurchaseCardOption(),
            ),
          ],
          if (showPurchase && (showProfile || showProgress))
            const SizedBox(width: 12),
          if (showProfile) ...[
            DebounceClickWidget(
              key: profileButtonKey,
              onTap: () {
                profileClick?.call();
              },
              child: const ProfileTitleOption(),
            ),
          ],
          if (showProfile && showProgress) const SizedBox(width: 12),
          if (showProgress) ...[
            Container(
              width: 1,
              height: 20,
              decoration: const BoxDecoration(color: Color(0x26FFFFFF)),
            ),
            const SizedBox(width: 12),
            DebounceClickWidget(
              onTap: () {
                exportProgressClick?.call();
              },
              child: const ExportProgressTitleOption(),
            ),
          ],
        ],
      );
    });
  }
}

class RewardCardOption extends StatefulWidget {
  const RewardCardOption({super.key});

  @override
  State<RewardCardOption> createState() => _RewardCardOptionState();
}

class _RewardCardOptionState extends State<RewardCardOption> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Container(
        height: 28,
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(6)),
          color: _isHovered
              ? const Color(0xFF251015) // 原色0x26 + 叠加5%白色
              : const Color(0xFFF72651).withOpacity(0.1),
        ),
        child: Text(
          "完善信息，免费获得张数",
          style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              fontSize: 12,
              color: const Color(0xFFF72651)),
        ),
      ),
    );
  }
}

class PurchaseCardOption extends StatefulWidget {
  const PurchaseCardOption({super.key});

  @override
  State<PurchaseCardOption> createState() => _PurchaseCardOptionState();
}

class _PurchaseCardOptionState extends State<PurchaseCardOption> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Container(
        height: 28,
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
        decoration: BoxDecoration(
          color: _isHovered
              ? const Color(0xFF263111) // 原色叠加5%白色
              : const Color(0xFFA8F21D).withOpacity(0.15),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: Image.asset("assets/icons/vip_diamond.png"),
            ),
            const SizedBox(
              width: 1,
            ),
            Text(
              "购买套餐",
              style: TextStyle(
                color: const Color(0xFFA8F21D),
                fontSize: 12,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class ProfileTitleOption extends StatefulWidget {
  const ProfileTitleOption({super.key});

  @override
  State<ProfileTitleOption> createState() => _ProfileTitleOptionState();
}

class _ProfileTitleOptionState extends State<ProfileTitleOption> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Color(0xFF121415),
            ),
          ),
          if (_isHovered)
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0x1AFFFFFF), // 覆盖整个28x28区域
                ),
              ),
            ),
          SizedBox(
            width: 16,
            height: 16,
            child: Image.asset(
              "assets/icons/home_mine_entrance.png",
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }
}

class ExportProgressTitleOption extends StatefulWidget {
  const ExportProgressTitleOption({super.key});

  @override
  State<ExportProgressTitleOption> createState() =>
      _ExportProgressTitleOptionState();
}

class _ExportProgressTitleOptionState extends State<ExportProgressTitleOption> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Container(
        height: 28,
        decoration: BoxDecoration(
          color: _isHovered
              ? const Color(0xFF1E2021) // 原色叠加5%白色
              : const Color(0xFF121415),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 11),
        child: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: Image.asset("assets/icons/home_export_progress.png"),
            ),
            Text(
              "进度",
              style: TextStyle(
                fontSize: 12,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
                color: const Color(0xFFFFFFFF),
              ),
            )
          ],
        ),
      ),
    );
  }
}
