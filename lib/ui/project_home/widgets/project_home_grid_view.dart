import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/dialog/delete_project_dialog.dart';
import 'package:turing_art/ui/dialog/project_rename_dialog.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

import '../../../utils/pg_log.dart';
import '../view_models/home_view_model.dart';
import 'project_home_grid_item.dart';

class ProjectHomeGridView extends StatelessWidget {
  const ProjectHomeGridView({super.key});

  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<HomeViewModel>();

    return GridView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      shrinkWrap: false,
      cacheExtent: 500,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 280 / 264,
      ),
      itemCount: viewModel.projects.length,
      itemBuilder: (context, index) {
        final project = viewModel.projects[index];
        return RepaintBoundary(
          child: GestureDetector(
            onTap: () => viewModel.selectedProject(index),
            child: ProjectHomeGridItem(
              key: ValueKey(project.uuid),
              project: project,
              isSelected: viewModel.queryProjectInBatch(project.uuid),
              onCheckboxClick: (value) {
                PGLog.d('onCheckboxClick: $value');
                viewModel.selectedProject(index);
              },
              onRenameClick: () {
                ProjectRenameDialog.show(
                    defaultInputText: project.name,
                    onConfirm: (name) {
                      ProjectRenameDialog.hide();
                      viewModel.renameProject(
                        project.uuid,
                        name,
                      );
                    });
              },
              onDeleteClick: () {
                DeleteProjectDialog.show(onConfirm: () async {
                  DeleteProjectDialog.hide();
                  PGDialog.showLoading();
                  final status = await viewModel.deleteProjects([project.uuid]);
                  await PGDialog.dismiss();
                  if (status) {
                    PGDialog.showToast('删除项目成功');
                  } else {
                    PGDialog.showToast('删除项目失败，请稍后再试');
                  }
                });
              },
            ),
          ),
        );
      },
    );
  }
}
