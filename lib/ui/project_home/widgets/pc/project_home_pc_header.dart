import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/shortcut/service/shortcut_key_service.dart';

class ProjectHomePcHeader extends StatefulWidget {
  final VoidCallback onNewProject;
  final VoidCallback onBatchEdit;
  final VoidCallback onAiEdit;
  final VoidCallback onExportProgress;
  final double sideMargin;

  const ProjectHomePcHeader({
    super.key,
    required this.sideMargin,
    required this.onNewProject,
    required this.onBatchEdit,
    required this.onAiEdit,
    required this.onExportProgress,
  });

  @override
  State<ProjectHomePcHeader> createState() => _ProjectHomePcHeaderState();
}

class _ProjectHomePcHeaderState extends State<ProjectHomePcHeader>
    with WidgetsBindingObserver, RouteAware {
  bool _createHovered = false;
  bool _batchHovered = false;
  bool _aiHovered = false;
  bool _exportHovered = false;
  final FocusNode _focusNode = FocusNode();
  final RouteObserver<ModalRoute<void>> _routeObserver =
      RouteObserver<ModalRoute<void>>();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 确保初始时获取焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _requestFocus();
    });

    // 添加路由观察
    final navigator = Navigator.of(context);
    navigator.widget.observers.add(_routeObserver);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _requestFocus();
    // 订阅路由观察器
    _routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 当应用恢复活动状态时请求焦点
    if (state == AppLifecycleState.resumed) {
      _requestFocus();
    }
  }

  @override
  void didPopNext() {
    // 当此页面从被覆盖状态恢复（如从另一个页面返回）时调用
    _requestFocus();
  }

  void _requestFocus() {
    if (mounted && !_focusNode.hasFocus) {
      _focusNode.requestFocus();
    }
  }

  @override
  void dispose() {
    _routeObserver.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 使用 FutureBuilder 来处理异步逻辑
    return FutureBuilder<bool>(
      future: context.read<AigcEntranceManager>().isAigcUser(),
      builder: (context, snapshot) {
        final bool showAiButton = snapshot.data ?? false;

        return KeyboardListener(
          focusNode: _focusNode,
          autofocus: true,
          onKeyEvent: (KeyEvent event) {
            if (event is KeyDownEvent) {
              final bool isControlAndOPressed = context
                  .read<ShortcutKeyService>()
                  .isControlAndOPressed(event);
              if (isControlAndOPressed) {
                widget.onNewProject();
              }
            }
          },
          child: SizedBox(
            height: 100,
            width: double.infinity,
            child: Row(
              children: [
                SizedBox(width: widget.sideMargin),
                Expanded(
                  child: PlatformMouseRegion(
                    cursor: SystemMouseCursors.click,
                    onEnter: (_) => setState(() => _createHovered = true),
                    onExit: (_) => setState(() => _createHovered = false),
                    child: _buildButton(
                      onTap: widget.onNewProject,
                      icon: 'assets/icons/home_pc_top_add.png',
                      title: '新建项目',
                      subtitle: '创建项目开始精修照片',
                      iconColor: const Color(0xFFF72561),
                      backgroundColor: _createHovered
                          ? const Color(0xFF434343)
                          : const Color(0xFF2E2E2E),
                    ),
                  ),
                ),
                const SizedBox(width: 30),
                Expanded(
                  child: PlatformMouseRegion(
                    cursor: SystemMouseCursors.click,
                    onEnter: (_) => setState(() => _batchHovered = true),
                    onExit: (_) => setState(() => _batchHovered = false),
                    child: _buildButton(
                      onTap: widget.onBatchEdit,
                      icon: 'assets/icons/home_pc_top_batch.png',
                      title: '批量修图',
                      subtitle: '一次批量完成',
                      iconColor: const Color(0xFF7B5AF4),
                      backgroundColor: _batchHovered
                          ? const Color(0xFF434343)
                          : const Color(0xFF2E2E2E),
                    ),
                  ),
                ),
                if (showAiButton) ...[
                  const SizedBox(width: 30),
                  Expanded(
                    child: PlatformMouseRegion(
                      cursor: SystemMouseCursors.click,
                      onEnter: (_) => setState(() => _aiHovered = true),
                      onExit: (_) => setState(() => _aiHovered = false),
                      child: _buildButton(
                        onTap: widget.onAiEdit,
                        icon: 'assets/icons/home_pc_top_ai.png',
                        title: 'AI场景增强',
                        subtitle: '场景美化，让照片更出彩',
                        iconColor: const Color(0xFF22EDFF),
                        backgroundColor: _aiHovered
                            ? const Color(0xFF434343)
                            : const Color(0xFF2E2E2E),
                        showNewTag: true,
                      ),
                    ),
                  ),
                ],
                const SizedBox(width: 30),
                SizedBox(
                  width: 280,
                  child: PlatformMouseRegion(
                    cursor: SystemMouseCursors.click,
                    onEnter: (_) => setState(() => _exportHovered = true),
                    onExit: (_) => setState(() => _exportHovered = false),
                    child: _buildButton(
                      onTap: widget.onExportProgress,
                      icon: 'assets/icons/home_pc_top_export.png',
                      title: '导出进度',
                      subtitle: '查看导出进度和完成情况',
                      iconColor: const Color(0xFF222526),
                      backgroundColor: _exportHovered
                          ? const Color(0xFF434343)
                          : const Color(0xFF2E2E2E),
                    ),
                  ),
                ),
                SizedBox(width: widget.sideMargin),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildButton({
    required VoidCallback onTap,
    required String icon,
    required String title,
    required String subtitle,
    required Color iconColor,
    required Color backgroundColor,
    bool showNewTag = false,
  }) {
    return DebounceClickWidget(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            height: 100,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                SizedBox(
                  width: 32,
                  height: 32,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: iconColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Image.asset(
                      icon,
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.semiBold,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const SizedBox(height: 3),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: const Color(0xFFEBF2F5).withAlpha(150),
                          fontSize: 12,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.regular,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (showNewTag)
            Positioned(
              top: 12,
              right: 12,
              child: Container(
                width: 32,
                height: 16,
                padding: EdgeInsets.zero,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: const Color(0xFF4A460B),
                ),
                child: Center(
                  child: Text(
                    'NEW',
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.medium,
                      fontSize: 11,
                      height: 14 / 11,
                      letterSpacing: 0,
                      color: const Color(0xFFF7E925),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
