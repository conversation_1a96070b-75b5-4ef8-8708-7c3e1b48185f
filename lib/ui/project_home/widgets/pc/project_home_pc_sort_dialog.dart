import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

import '../../../../datalayer/domain/enums/sort_option.dart';

class ProjectSortDialog extends StatefulWidget {
  final SortOption selectedOption;
  final Function(SortOption) onOptionSelected;

  const ProjectSortDialog({
    super.key,
    required this.selectedOption,
    required this.onOptionSelected,
  });

  @override
  State<ProjectSortDialog> createState() => _ProjectSortDialogState();
}

class _ProjectSortDialogState extends State<ProjectSortDialog> {
  String? _hoveredOption;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000),
            offset: Offset(0, 2),
            blurRadius: 20,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortOption(SortOption.lastOpened),
            _buildSortOption(SortOption.fileName),
            _buildSortOption(SortOption.createTime),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(SortOption option) {
    final bool isSelected = widget.selectedOption == option;
    final bool isHovered = _hoveredOption == option.name;

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _hoveredOption = option.name),
      onExit: (_) => setState(() => _hoveredOption = null),
      child: GestureDetector(
        onTap: () => widget.onOptionSelected(option),
        child: Container(
          height: 40,
          width: 184,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: (!isSelected && isHovered)
                ? const Color(0x0DFFFFFF)
                : Colors.transparent,
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  option.description,
                  style: TextStyle(
                    color: isSelected
                        ? const Color(0xFFFFFFFF)
                        : const Color(0x99EBF2F5),
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: isSelected ? Fonts.medium : Fonts.regular,
                    fontSize: 12,
                  ),
                ),
              ),
              if (isSelected)
                Image.asset(
                  'assets/icons/home_sortOption_selected.png',
                  width: 16,
                  height: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
