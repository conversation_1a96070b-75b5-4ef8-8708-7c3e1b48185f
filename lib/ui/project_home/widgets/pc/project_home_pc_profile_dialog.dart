import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';

class ProjectHomePcProfileDialog extends StatefulWidget {
  // 是否为子账号
  final bool isSubAccount;
  // 是否开启账号管理
  final bool isOpenAccountManagement;
  // 退出登录
  final VoidCallback onLogout;
  // 设置
  final VoidCallback onSettings;
  // 续费充值
  final VoidCallback onRecharge;
  // 我的套餐
  final VoidCallback onCombo;
  // 导出计费明细
  final VoidCallback onExport;
  // 片量共享
  final VoidCallback onShare;
  // 关于我们
  final VoidCallback onAboutUs;
  // 导入咻图AI预设
  final VoidCallback onImportPreset;

  const ProjectHomePcProfileDialog({
    super.key,
    required this.isSubAccount,
    required this.isOpenAccountManagement,
    required this.onLogout,
    required this.onSettings,
    required this.onRecharge,
    required this.onCombo,
    required this.onExport,
    required this.onShare,
    required this.onAboutUs,
    required this.onImportPreset,
  });

  @override
  State<ProjectHomePcProfileDialog> createState() =>
      _ProjectHomePcProfileDialogState();
}

class _ProjectHomePcProfileDialogState
    extends State<ProjectHomePcProfileDialog> {
  String? _hoveredOption;

  // 统一图标构建方法（移除鼠标事件）
  Widget _buildCustomIcon(String text, String assetPath) {
    return Image.asset(
      assetPath,
      width: 24,
      height: 24,
      color: const Color(0xFFEBF2F5),
    );
  }

  // 选项项构建方法（修改文字颜色逻辑）
  Widget _buildOptionItem(
    String text, {
    required Widget icon,
    VoidCallback? onTap,
  }) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _hoveredOption = text),
      onExit: (_) => setState(() => _hoveredOption = null),
      cursor: SystemMouseCursors.click,
      child: DebounceClickWidget(
        onTap: onTap == null
            ? null
            : () async {
                await PGDialog.dismiss(tag: DialogTags.homeProfile);
                onTap();
              },
        child: Container(
          width: 280,
          height: 40,
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: _hoveredOption == text
                ? const Color(0x0DFFFFFF)
                : Colors.transparent,
          ),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            child: Padding(
              padding: const EdgeInsets.only(left: 10),
              child: Row(
                children: [
                  icon,
                  const SizedBox(width: 4),
                  Text(
                    text,
                    style: TextStyle(
                      color: const Color(0x99EBF2F5),
                      fontSize: 12,
                      fontWeight: Fonts.regular,
                      fontFamily: Fonts.defaultFontFamily,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 根据是否为子账号，确定显示不同的选项
    final List<Widget> options = [];

    // 主账号包含所有选项
    if (!widget.isSubAccount) {
      options.addAll([
        _buildOptionItem('续费充值',
            icon: _buildCustomIcon(
                '续费充值', 'assets/icons/home_profile_recharge.png'),
            onTap: widget.onRecharge),
        if (widget.isOpenAccountManagement)
          _buildOptionItem('账号管理',
              icon: _buildCustomIcon(
                  '账号管理', 'assets/icons/home_profile_share.png'),
              onTap: widget.onShare),
        _buildOptionItem('导入 咻图AI 预设',
            icon: _buildCustomIcon(
                '导入 咻图AI 预设', 'assets/icons/home_profile_into_preset.png'),
            onTap: widget.onImportPreset),
        _buildOptionItem('设置',
            icon: _buildCustomIcon('设置', 'assets/icons/home_profile_set.png'),
            onTap: widget.onSettings),
        _buildOptionItem('我的套餐',
            icon:
                _buildCustomIcon('我的套餐', 'assets/icons/home_profile_combo.png'),
            onTap: widget.onCombo),
        _buildOptionItem('导出计费明细',
            icon: _buildCustomIcon(
                '导出计费明细', 'assets/icons/home_profile_export_records.png'),
            onTap: widget.onExport),
        _buildOptionItem('关于我们',
            icon: _buildCustomIcon(
                '关于我们', 'assets/icons/home_profile_aboutUs.png'),
            onTap: widget.onAboutUs),
        _buildOptionItem('退出登录',
            icon: _buildCustomIcon(
                '退出登录', 'assets/icons/home_profile_logout.png'),
            onTap: widget.onLogout),
      ]);
    } else {
      // 子账号只包含部分选项
      options.addAll([
        _buildOptionItem('设置',
            icon: _buildCustomIcon('设置', 'assets/icons/home_profile_set.png'),
            onTap: widget.onSettings),
        _buildOptionItem('关于我们',
            icon: _buildCustomIcon(
                '关于我们', 'assets/icons/home_profile_aboutUs.png'),
            onTap: widget.onAboutUs),
        _buildOptionItem('退出登录',
            icon: _buildCustomIcon(
                '退出登录', 'assets/icons/home_profile_logout.png'),
            onTap: widget.onLogout),
      ]);
    }

    return Container(
      width: 296,
      height: options.length * 40 + 16,
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000),
            offset: Offset(0, 2),
            blurRadius: 20,
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Column(children: options),
            ],
          ),
        ],
      ),
    );
  }
}
