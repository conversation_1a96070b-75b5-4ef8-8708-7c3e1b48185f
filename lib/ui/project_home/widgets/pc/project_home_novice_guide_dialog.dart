import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 新手引导弹窗
/// 展示项目封面、名称和创建时间
class NoviceGuideDialog extends StatelessWidget {
  final ProjectInfo _project;
  final String _coverThumbnailPath;
  final VoidCallback? _onDoubleTap;
  final double _dialogWidth;

  const NoviceGuideDialog({
    super.key,
    required ProjectInfo project,
    required String coverThumbnailPath,
    VoidCallback? onDoubleTap,
    double dialogWidth = 274,
  })  : _project = project,
        _coverThumbnailPath = coverThumbnailPath,
        _onDoubleTap = onDoubleTap,
        _dialogWidth = dialogWidth;

  /// 在普通半透明视图上显示新手引导弹窗（dialog上mask就算是不写手势，也会劫持）
  static OverlayEntry showOnView({
    required BuildContext context,
    required ProjectInfo project,
    required Rect dialogRect,
    required String coverThumbnailPath,
    VoidCallback? onDoubleTap,
  }) {
    PGLog.d(
        '在普通视图上显示新手引导弹窗，位置: left=${dialogRect.left}, top=${dialogRect.top}, 尺寸: ${dialogRect.width}x${dialogRect.height}');

    // 创建一个OverlayEntry
    final overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 半透明背景
          Positioned.fill(
            child: Container(
              color: const Color(0x99000000), // 与PGDialog相同的遮罩颜色
            ),
          ),
          // 弹窗内容
          Positioned(
            left: dialogRect.left,
            top: dialogRect.top,
            child: NoviceGuideDialog(
              project: project,
              coverThumbnailPath: coverThumbnailPath,
              onDoubleTap: onDoubleTap,
              dialogWidth: dialogRect.width,
            ),
          ),
        ],
      ),
    );

    // 将OverlayEntry插入到Overlay中
    Overlay.of(context).insert(overlayEntry);

    // 返回OverlayEntry，以便调用者可以在适当的时候移除它
    return overlayEntry;
  }

  @override
  Widget build(BuildContext context) {
    // 计算封面图片尺寸，宽度为对话框宽度减去左右间距
    final coverWidth = _dialogWidth - 24;
    // 保持图片290:193的宽高比，和item图片区域一样
    final coverHeight = coverWidth * 193 / 290;

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onDoubleTap: () {
          // 执行双击回调
          if (_onDoubleTap != null) {
            _onDoubleTap();
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFF202020), // 更新背景色为#333333
            borderRadius: BorderRadius.circular(12), // border-radius: 12px
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 项目封面区域
                Center(
                  child: SizedBox(
                    width: coverWidth,
                    height: coverHeight,
                    child: _buildProjectCover(coverWidth, coverHeight),
                  ),
                ),

                const SizedBox(height: 12),

                // 项目名称
                Text(
                  _project.name,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                // 创建时间
                Text(
                  DateTimeUtil.formatDateTime(_project.createdDate),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 12,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建项目封面
  Widget _buildProjectCover(double coverWidth, double coverHeight) {
    // 首先检查是否有缩略图路径
    if (_coverThumbnailPath.isNotEmpty &&
        _coverThumbnailPath.startsWith('assets/')) {
      PGLog.d('使用资源缩略图: $_coverThumbnailPath');
      return _buildImageWidget(_coverThumbnailPath, coverWidth, coverHeight,
          isAsset: true);
    } else {
      PGLog.d('没有配置封面图，创建默认黑色');
      return Container(
        color: Colors.black,
        width: coverWidth,
        height: coverHeight,
      );
    }
  }

  // 构建图片Widget
  Widget _buildImageWidget(dynamic source, double width, double height,
      {required bool isAsset}) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Image.asset(
          source,
          fit: BoxFit.cover,
          width: width,
          height: height,
        ));
  }
}
