import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

class ProjectHomePCAddItem extends StatefulWidget {
  final double _imageContainerRatio = 193 / 237;

  const ProjectHomePCAddItem({super.key});

  Size _getItemSize(BuildContext context) {
    return const Size(290, 237);
  }

  @override
  State<ProjectHomePCAddItem> createState() => _ProjectHomePCAddItemState();
}

class _ProjectHomePCAddItemState extends State<ProjectHomePCAddItem> {
  bool _hovered = false;

  @override
  Widget build(BuildContext context) {
    final itemSize = widget._getItemSize(context);
    final imageContainerHeight = itemSize.height * widget._imageContainerRatio;

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _hovered = true),
      onExit: (_) => setState(() => _hovered = false),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: _hovered
                      ? const Color(0xFF2D3031)
                      : const Color(0xFF222526),
                  borderRadius: BorderRadius.circular(8),
                ),
                height: imageContainerHeight,
                child: Center(
                  child: Icon(
                    Icons.add,
                    color: const Color(0xFFEBF2F5).withOpacity(0.6),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
