import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class ProjectHomeEmptyState extends StatelessWidget {
  final VoidCallback onSelectFiles;

  const ProjectHomeEmptyState({
    super.key,
    required this.onSelectFiles,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 360,
        height: 370,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/empty_state_icon.png',
              width: 360,
              height: 230,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 24),
            Text(
              '你可以直接拖拽文件夹（照片）到界面中以完成添加',
              style: TextStyle(
                color: const Color(0x99EBF2F5),
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: FontWeight.w400,
                height: 18 / 14,
                letterSpacing: 0,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '或者',
              style: TextStyle(
                color: const Color(0x99EBF2F5),
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: FontWeight.w400,
                height: 18 / 14,
                letterSpacing: 0,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Container(
              width: 180,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFFF72561),
                borderRadius: BorderRadius.circular(10),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x267F0E2E),
                    offset: Offset(0, 4),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: onSelectFiles,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Text(
                  '选择文件',
                  style: TextStyle(
                    color: const Color(0xFFEBF2F5),
                    fontSize: 14,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
