import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/service/project_cover_generater/project_cover_generater.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/ui/common/project_grid_view/project_images_layout.dart';
import 'package:turing_art/ui/common/project_grid_view/view_model/project_grid_item_view_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/dialog/universal_dialog.dart';
import 'package:turing_art/ui/project_home/view_models/home_view_model.dart';
import 'package:turing_art/ui/project_home/widgets/project_home_more_action_view.dart';
import 'package:turing_art/ui/ui_status/select_project_error_type.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class ProjectHomePCGridItem extends StatefulWidget {
  final ProjectInfo project;
  final DateFormat _dateFormat = DateFormat('yyyy.MM.dd HH:mm');
  final double _imageContainerRatio = 178 / 240;
  final Function onDeleteClick;
  final Function onRenameClick;
  final Function onBatchClick;
  final Function({required bool isChecked}) onCheckboxClick;
  final bool isBatchMode;
  final bool isSelected;

  ProjectHomePCGridItem({
    super.key,
    required this.project,
    required this.onDeleteClick,
    required this.onRenameClick,
    required this.onBatchClick,
    required this.onCheckboxClick,
    required this.isBatchMode,
    required this.isSelected,
  });

  @override
  State<StatefulWidget> createState() {
    return _ProjectHomePCGridItemState();
  }
}

class _ProjectHomePCGridItemState extends State<ProjectHomePCGridItem> {
  bool _hovered = false;
  bool _isMoreHovered = false;
  bool _showMenu = false;
  bool get needBorder => (_hovered && !widget.isBatchMode) || widget.isSelected;

  String _formatDate(int? timestamp) {
    if (timestamp == null) {
      return '';
    }
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return widget._dateFormat.format(dateTime);
  }

  // 使用ProjectImagesLayout构建图片容器
  Widget _buildPCGridImageContainer(BuildContext context) {
    final imageFiles = widget.project.coverImages.take(3).toList();
    final itemSize = _getItemSize(context);
    final imageContainerHeight = itemSize.height * widget._imageContainerRatio;

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1B1C),
        borderRadius: BorderRadius.circular(8),
        border: needBorder
            ? Border.all(
                color: const Color(0xFFF72561),
                width: 2,
              )
            : null,
      ),
      height: imageContainerHeight,
      padding: needBorder ? const EdgeInsets.all(2) : null,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
        ),
        child: Stack(
          children: [
            GestureDetector(
              onSecondaryTapDown: (details) => {
                if (!context.read<HomeViewModel>().isBatchProcessing)
                  {_showContextMenu(context, details)}
              },
              onTap: () {
                if (widget.isBatchMode && widget.project.fileCount == 0) {
                  return;
                }
                _handleItemClick();
              },
              onDoubleTap: () {
                if (widget.isBatchMode && widget.project.fileCount == 0) {
                  return;
                }
                _handleItemClick();
              },
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: _buildCoverImagesLayout(
                  context,
                  imageFiles,
                  itemSize,
                  imageContainerHeight,
                ),
              ),
            ),
            if (widget.isBatchMode && !widget.isSelected)
              // 设置为不可点击,当Container有背景色时，会阻挡点击，因此需要设置为不可点击
              IgnorePointer(
                child: Container(
                  color: Colors.black.withAlpha(100),
                ),
              )
          ],
        ),
      ),
    );
  }

  // 构建封面图片布局
  Widget _buildCoverImagesLayout(BuildContext context, List<String> imageFiles,
      Size itemSize, double imageContainerHeight) {
    if (imageFiles.isEmpty) {
      // 没有封面图片时显示占位符
      return Container(
        width: itemSize.width,
        height: needBorder ? imageContainerHeight - 4 : imageContainerHeight,
        color: const Color(0xFF1A1B1C),
        child: const Center(
          child: Icon(
            Icons.image_outlined,
            color: Color(0xFF666666),
            size: 32,
          ),
        ),
      );
    }

    // 为整个工程创建一个 ViewModel Provider，管理所有封面
    return ChangeNotifierProvider<ProjectGridItemViewModel>(
      create: (context) => ProjectGridItemViewModel(
        (projectId, fileId) async {
          // if (Platform.isMacOS) {
          // mac 暂时使用原本的封面生成方式
          // final path = await context
          //     .read<HomeViewModel>()
          //     .coverThumbnailService
          //     .getThumbnailPath(projectId, fileId);
          // return File(path);
          // }
          return ProjectCoverGenerater(
            aigcService: context.read<AigcService>(),
            mediaRepository: context.read<MediaRepository>(),
          ).getCoverThumbnail(projectId, fileId);
        },
        widget.project.uuid,
        imageFiles, // 传入所有封面文件ID
      ),
      child: Consumer<ProjectGridItemViewModel>(
        builder: (context, viewModel, child) {
          // 获取可用的封面路径
          final availablePaths = viewModel.availableCoverPaths;

          if (availablePaths.isEmpty && viewModel.isLoading) {
            // 所有封面都在加载中
            return Container(
              width: itemSize.width,
              height:
                  needBorder ? imageContainerHeight - 4 : imageContainerHeight,
              color: const Color(0xFF1A1B1C),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF666666)),
                  ),
                ),
              ),
            );
          }

          // 使用 ProjectImagesLayout 显示封面
          return ProjectImagesLayout(
            images: availablePaths,
            projectId: widget.project.uuid,
            width: itemSize.width,
            height:
                needBorder ? imageContainerHeight - 4 : imageContainerHeight,
          );
        },
      ),
    );
  }

  Size _getItemSize(BuildContext context) {
    final layout = context.read<HomeViewModel>();
    return Size(layout.itemWidth, layout.itemHeight);
  }

  void _showContextMenu(BuildContext context, TapDownDetails details) {
    // 直接显示菜单，不等待setState完成
    // 这样可以减少一次重建过程
    ProjectHomeMoreActionView.show(
      context: context,
      position: details.globalPosition,
      models: [
        MenuItemModel(
          icon: Icons.check_box_outlined,
          text: '批量修图',
          textColor: Colors.white,
          onTap: () => widget.onBatchClick(),
        ),
        MenuItemModel(
          icon: Icons.edit_outlined,
          text: '重命名',
          textColor: Colors.white,
          onTap: () => widget.onRenameClick(),
        ),
        MenuItemModel(
          icon: Icons.delete_outline,
          text: '删除',
          textColor: const Color(0xFFFF4D4F),
          onTap: () => widget.onDeleteClick(),
        ),
      ],
      onDismissed: () {
        // 菜单关闭时将_showMenu设置为false
        if (mounted) {
          setState(() {
            _showMenu = false;
          });
        }
      },
    );

    // 菜单显示后再更新状态，这不会影响菜单的显示速度
    if (mounted) {
      setState(() {
        _showMenu = true;
      });
    }
  }

  // 处理点击事件
  Future<void> _handleItemClick() async {
    if (!mounted) {
      return;
    }

    final viewModel = context.read<HomeViewModel>();
    final index = viewModel.projects.indexWhere(
      (element) => element.uuid == widget.project.uuid,
    );
    if (index < 0 || index >= viewModel.projects.length) {
      PGLog.e('无效的项目索引: $index');
      return;
    }
    PGDialog.showLoading();
    final guard = await viewModel.trySelectedProject(index);
    await PGDialog.dismiss();

    if (guard == SelectProjectErrorType.diskSpace) {
      UniversalDialog.show(
        title: '磁盘空间不足',
        content: '您的C盘空间不足！建议清理磁盘后继续使用\n点击确认后可进入项目，有风险导致项目损坏！',
        onConfirm: () {
          if (mounted) {
            viewModel.selectedProject(index);
          }
          UniversalDialog.hide();
        },
        confirmText: '确认风险并进入',
        cancelText: '取消进入',
      );
    } else if (guard == SelectProjectErrorType.migrationFailed) {
      PGDialog.showToast('v2工程迁移异常，请联系工作人员');
      return;
    } else {
      viewModel.selectedProject(index);
    }
  }

  Widget _buildPcGridView(BuildContext context) {
    final itemSize = _getItemSize(context);

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        if (mounted) {
          setState(() => _hovered = true);
        }
      },
      onExit: (_) {
        if (mounted) {
          setState(() => _hovered = false);
        }
      },
      child: SizedBox(
        width: itemSize.width,
        height: itemSize.height,
        child: Column(
          children: [
            Stack(
              children: [
                _buildPCGridImageContainer(context),
                if (widget.isBatchMode)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () {
                        widget.onCheckboxClick(isChecked: !widget.isSelected);
                      },
                      child: Image.asset(
                        widget.isSelected
                            ? 'assets/icons/batch_choice_selected.png'
                            : 'assets/icons/batch_choice_normal.png',
                        width: 20,
                        height: 20,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(
              height: 44,
              width: itemSize.width,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: GestureDetector(
                      onSecondaryTapDown: (details) => {
                        if (!context.read<HomeViewModel>().isBatchProcessing)
                          {_showContextMenu(context, details)}
                      },
                      onTap: () {
                        final viewModel = context.read<HomeViewModel>();
                        viewModel.selectedProject(viewModel.projects.indexWhere(
                          (element) => element.uuid == widget.project.uuid,
                        ));
                      },
                      behavior: HitTestBehavior.opaque,
                      child: SizedBox(
                        height: 44,
                        width: itemSize.width - 40,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 8),
                            SizedBox(
                              height: 16,
                              child: Text(
                                widget.project.name,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: Fonts.semiBold,
                                  fontFamily: Fonts.defaultFontFamily,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(height: 4),
                            SizedBox(
                              height: 16,
                              child: Row(
                                children: [
                                  Text(
                                    '${widget.project.fileCount}张图',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: Fonts.light,
                                      fontFamily: Fonts.defaultFontFamily,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _formatDate(widget.project.updateDate
                                        ?.millisecondsSinceEpoch),
                                    style: TextStyle(
                                      color: const Color(0xFFEBF2F5)
                                          .withAlpha(150),
                                      fontSize: 12,
                                      fontWeight: Fonts.light,
                                      fontFamily: Fonts.defaultFontFamily,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                  // 非批量模式下，鼠标悬停或显示菜单时显示更多按钮
                  if (!widget.isBatchMode && (_hovered || _showMenu))
                    // 更多按钮
                    PlatformMouseRegion(
                      cursor: SystemMouseCursors.click,
                      onEnter: (event) {
                        if (mounted) {
                          setState(() {
                            _isMoreHovered = true;
                          });
                        }
                      },
                      onExit: (event) {
                        if (mounted) {
                          setState(() {
                            _isMoreHovered = false;
                          });
                        }
                      },
                      child: GestureDetector(
                        onTapDown: (details) {
                          _showContextMenu(context, details);
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          margin: const EdgeInsets.only(top: 6),
                          decoration: BoxDecoration(
                            color: _isMoreHovered
                                ? const Color(0xFF1E1F21)
                                : const Color(0xFF121415),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Center(
                            child: Image.asset(
                              "assets/icons/project_item_more.png",
                              width: 24,
                              height: 24,
                              color: const Color(0xFFEBEDF5).withOpacity(0.6),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildPcGridView(context);
  }
}
