import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/components/pc_hover_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/project_home/view_models/home_view_model.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../../../core/widgets/platform_mouse_region.dart';

class ProjectHomePCTopBar extends StatefulWidget {
  final VoidCallback? onRecentClick;
  final VoidCallback? onMultiSelectClick;
  final VoidCallback? onCreateProjectClick;
  final double sideMargin;
  final bool showCreateProjectOnly;
  final GlobalKey<ProjectHomePCTopBarState> topBarKey;
  static final recentButtonKey = GlobalKey();

  const ProjectHomePCTopBar({
    required this.topBarKey,
    this.onRecentClick,
    this.onMultiSelectClick,
    this.onCreateProjectClick,
    required this.sideMargin,
    this.showCreateProjectOnly = false,
  }) : super(key: topBarKey);

  @override
  State<ProjectHomePCTopBar> createState() => ProjectHomePCTopBarState();
}

class ProjectHomePCTopBarState extends State<ProjectHomePCTopBar> {
  bool _isRecentSelected = false;
  bool _isAddHovered = false;

  @override
  void initState() {
    super.initState();
    PGLog.d("RecentButton状态初始化");
  }

  void resetSelectedState() {
    if (mounted) {
      setState(() => _isRecentSelected = false);
      PGLog.d("Recent按钮状态已重置: $_isRecentSelected");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HomeViewModel>(
      builder: (context, viewModel, child) {
        return Container(
          height: widget.showCreateProjectOnly ? 86 : 56,
          color: const Color(0xFF0D0D0D),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(width: widget.sideMargin),
              if (widget.showCreateProjectOnly)
                PlatformMouseRegion(
                  onEnter: (_) => setState(() => _isAddHovered = true),
                  onExit: (_) => setState(() => _isAddHovered = false),
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      widget.onCreateProjectClick?.call();
                      setState(() {
                        _isAddHovered = false;
                      });
                    },
                    child: Container(
                      height: 32,
                      width: 120,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: _isAddHovered
                            ? const Color(0xFFF73069)
                            : const Color(0xFFF72561),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/icons/home_pc_top_add.png',
                            width: 24,
                            height: 24,
                          ),
                          const Text(
                            '新建',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFFFFFFFF),
                              fontFamily: Fonts.fontFamilySF,
                              fontWeight: Fonts.semiBold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else
                TextButton(
                  onPressed: () {},
                  style: ButtonStyle(
                    padding: MaterialStateProperty.all(EdgeInsets.zero),
                    alignment: Alignment.centerLeft,
                    minimumSize: MaterialStateProperty.all(const Size(100, 56)),
                    splashFactory: NoSplash.splashFactory,
                    overlayColor: MaterialStateProperty.all(Colors.transparent),
                  ),
                  child: const Text(
                    '全部项目',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      fontFamily: Fonts.fontFamilySF,
                      fontWeight: Fonts.semiBold,
                    ),
                  ),
                ),
              // TextButton(
              //   onPressed: () {},
              //   style: TextButton.styleFrom(
              //     padding: EdgeInsets.zero,
              //     alignment: Alignment.centerLeft,
              //     minimumSize: const Size(100, 52),
              //   ),
              //   child: Text(
              //     '回收站',
              //     style: TextStyle(
              //       fontSize: 20,
              //       color: const Color(0xFFEBF2F5).withOpacity(0.6),
              //       fontFamily: Fonts.fontFamilySF,
              //       fontWeight: Fonts.semiBold,
              //     ),
              //   ),
              // ),
              const Spacer(),
              PcHoverWidget(
                builder: (context, isHovered) => GestureDetector(
                  key: ProjectHomePCTopBar.recentButtonKey,
                  onTap: () {
                    setState(() => _isRecentSelected = true);
                    widget.onRecentClick?.call();
                  },
                  child: Container(
                    height: 32,
                    width: 120,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    margin: const EdgeInsets.only(right: 16),
                    decoration: BoxDecoration(
                      color: _isRecentSelected || isHovered
                          ? const Color(0xFF434343)
                          : const Color(0xFF2E2E2E),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 2),
                            child: Text(
                              viewModel.currentSortOption.description,
                              style: TextStyle(
                                fontSize: 12,
                                color: const Color(0xFFEBF2F5).withOpacity(0.6),
                                fontFamily: Fonts.fontFamilySF,
                                fontWeight: Fonts.semiBold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: const Color(0xFFEBF2F5).withOpacity(0.6),
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              PcHoverWidget(
                builder: (context, isHovered) => GestureDetector(
                  onTap: () {
                    widget.onMultiSelectClick?.call();
                  },
                  child: Container(
                    height: 32,
                    width: 120,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: isHovered
                          ? const Color(0xFF434343)
                          : const Color(0xFF2E2E2E),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.menu,
                          color: const Color(0xFFEBF2F5).withOpacity(0.6),
                          size: 16,
                        ),
                        const SizedBox(width: 2),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 2),
                          child: Text(
                            '多选',
                            style: TextStyle(
                              fontSize: 12,
                              color: const Color(0xFFEBF2F5).withOpacity(0.6),
                              fontFamily: Fonts.fontFamilySF,
                              fontWeight: Fonts.semiBold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: widget.sideMargin),
            ],
          ),
        );
      },
    );
  }
}
