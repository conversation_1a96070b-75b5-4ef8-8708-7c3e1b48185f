/// GridView布局计算器
/// 用于计算基于屏幕宽度的GridView布局参数
class ProjectHomePCGridCalculator {
  /// 单个GridItem的最小宽度
  static const double minItemWidth = 240;

  /// 单个GridItem的最小高度
  static const double minItemHeight = 240;

  /// 宽高比
  static const double aspectRatio = minItemWidth / minItemHeight;

  /// GridItem之间的固定间距
  static const double itemSpacing = 24;

  /// 固定左右边距
  static const double sideMargin = 16;

  /// 计算GridView布局参数
  ///
  /// [screenWidth] 当前屏幕宽度
  ///
  /// 返回一个包含以下信息的对象：
  /// - itemsPerRow: 每行可以显示的项目数量
  /// - sideMargin: 固定的左右边距
  /// - itemWidth: 计算后的实际项目宽度
  /// - itemHeight: 计算后的实际项目高度
  static GridLayoutInfo calculate(double screenWidth) {
    // 计算可用宽度（屏幕宽度减去左右边距）
    final availableWidth = screenWidth - 318 - (sideMargin * 2);
    if (availableWidth <= 0) {
      return const GridLayoutInfo(
        itemsPerRow: 1,
        sideMargin: sideMargin,
        itemWidth: minItemWidth,
        itemHeight: minItemHeight,
      );
    }

    // 计算每行可以放置的项目数量（向下取整）
    final itemsPerRow =
        ((availableWidth + itemSpacing) / (minItemWidth + itemSpacing))
            .floor()
            .abs();

    if (itemsPerRow <= 0) {
      return const GridLayoutInfo(
        itemsPerRow: 1,
        sideMargin: sideMargin,
        itemWidth: minItemWidth,
        itemHeight: minItemHeight,
      );
    }

    // 计算实际的项目宽度（可用空间平均分配）
    final actualItemWidth =
        (availableWidth - (itemsPerRow - 1) * itemSpacing) / itemsPerRow;

    // 确保实际宽度不小于最小宽度
    final finalItemWidth =
        actualItemWidth > minItemWidth ? actualItemWidth : minItemWidth;

    // 根据宽高比计算高度
    final finalItemHeight = finalItemWidth / aspectRatio;

    return GridLayoutInfo(
      itemsPerRow: itemsPerRow,
      sideMargin: sideMargin,
      itemWidth: finalItemWidth,
      itemHeight: finalItemHeight,
    );
  }
}

/// GridView布局信息
class GridLayoutInfo {
  /// 每行显示的项目数量
  final int itemsPerRow;

  /// 左右两侧的边距
  final double sideMargin;

  /// 计算后的项目宽度
  final double itemWidth;

  /// 计算后的项目高度
  final double itemHeight;

  const GridLayoutInfo({
    required this.itemsPerRow,
    required this.sideMargin,
    required this.itemWidth,
    required this.itemHeight,
  });

  @override
  String toString() =>
      'GridLayoutInfo(itemsPerRow: $itemsPerRow, sideMargin: $sideMargin, itemWidth: $itemWidth, itemHeight: $itemHeight)';
}
