import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:turing_art/datalayer/domain/enums/sort_option.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/routing/router.dart';
import 'package:turing_art/ui/dialog/delete_project_dialog.dart';
import 'package:turing_art/ui/dialog/project_rename_pc_dialog.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/ui/project_home/view_models/home_view_model.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_grid_item.dart';
import 'package:turing_art/utils/extensions/widget_extensions.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class ProjectHomePCGridView extends StatefulWidget {
  // 全局Key，用于获取网格的位置
  static final GlobalKey globalKey = GlobalKey();

  // 保存每个项目项的Key
  static final Map<int, GlobalKey> _itemKeys = {};

  // 获取特定索引项目的Key
  static GlobalKey? getItemKey(int index) {
    return _itemKeys[index];
  }

  /// 获取特定索引项目的位置和大小
  /// 使用Completer和PostFrameCallback确保在布局完成后获取
  static Rect? getItemRect(int index) {
    final key = _itemKeys[index];
    if (key == null) {
      return null;
    }
    return key.globalRect;
  }

  const ProjectHomePCGridView({
    super.key,
    this.onBatchEdit,
  });

  /// 批量修图回调
  final void Function({required int index})? onBatchEdit;

  @override
  State<ProjectHomePCGridView> createState() => _ProjectHomePCGridViewState();
}

class _ProjectHomePCGridViewState extends State<ProjectHomePCGridView>
    with RouteAware {
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didPushNext() {
    super.didPushNext();
    _isActive = false;
  }

  @override
  void didPopNext() {
    super.didPopNext();
    _isActive = true;

    // 获取 ViewModel 实例
    final viewModel = context.read<HomeViewModel>();
    // 返回首页时主动刷新工程和账号信息
    viewModel.loadProjects();
    context.read<ProfileDialogViewModel>().refreshAllAccount();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final temp = ModalRoute.of(context);
    if (temp != null) {
      routeObserver.subscribe(this, temp);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 直接获取 MediaQuery 数据
    final mediaQuery = MediaQuery.of(context);

    // 改用 Selector2 只监听需要的 Provider
    return Selector2<HomeViewModel, ProjectStateProvider,
        Tuple3<ProjectGridData, bool, Size>>(
      // selector 只组合 HomeViewModel 和 ProjectStateProvider 的数据
      selector: (context, homeVM, projectState) => Tuple3(
        ProjectGridData(
            projects: homeVM.projects,
            sortOption: homeVM.currentSortOption,
            isProcessing: homeVM.isProcessing,
            isBatchProcessing: homeVM.isBatchProcessing,
            isCreateingProject: homeVM.isCreateNewProject),
        projectState.isEditing,
        // 直接使用已获取的 mediaQuery
        mediaQuery.size,
      ),
      shouldRebuild: (previous, current) {
        // 如果不活跃，不重建
        if (!_isActive) {
          return false;
        }
        // 如果在创建工程流程中，不重建
        if (current.item1.isCreateingProject) {
          return false;
        }

        // 如果在编辑状态，不重建
        if (current.item2) {
          PGLog.d('当前在编辑状态，不消费数据更新');
          return false;
        }

        // 检查项目数据是否变化
        final projectDataChanged = previous.item1 != current.item1;
        // 检查屏幕尺寸是否变化
        final sizeChanged = previous.item3 != current.item3;

        if (projectDataChanged) {
          PGLog.d('项目数据发生变化，需要重建');
        }
        if (sizeChanged) {
          PGLog.d('屏幕尺寸发生变化，需要重建');
        }

        return projectDataChanged || sizeChanged;
      },
      builder: (context, data, child) {
        final gridData = data.item1;
        final screenSize = data.item3;

        PGLog.d('构建网格视图 - 屏幕宽度: ${screenSize.width}');

        return _buildGrid(
          context,
          gridData: gridData,
          screenSize: screenSize,
        );
      },
    );
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  Widget _buildGrid(
    BuildContext context, {
    required ProjectGridData gridData,
    required Size screenSize,
  }) {
    // 获取屏幕尺寸，并在日志中使用它
    PGLog.d('构建网格 - 屏幕尺寸: ${screenSize.width}x${screenSize.height}');
    final itemsPerRow = context.read<HomeViewModel>().itemsPerRow;

    return SliverGrid(
      key: ProjectHomePCGridView.globalKey,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: itemsPerRow,
        mainAxisSpacing: 30,
        crossAxisSpacing: 24,
        childAspectRatio: 240 / 240,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // 为每个项目创建一个Key
          ProjectHomePCGridView._itemKeys[index] = GlobalKey();

          final project = gridData.projects[index];
          return RepaintBoundary(
            child: ProjectHomePCGridItem(
              key: ProjectHomePCGridView._itemKeys[index],
              project: project,
              isBatchMode: gridData.isBatchProcessing,
              isSelected: context
                  .read<HomeViewModel>()
                  .queryProjectInBatch(project.uuid),
              onBatchClick: () {
                widget.onBatchEdit?.call(index: index);
              },
              onCheckboxClick: ({required bool isChecked}) {
                PGLog.d('onCheckboxClick: $isChecked');
                context.read<HomeViewModel>().selectedProject(index);
              },
              onRenameClick: () {
                PGLog.d('pcgridview onRenameClick');
                ProjectRenamePCDialog.show(
                  currentName: project.name,
                  onConfirm: (name) {
                    context.read<HomeViewModel>().renameProject(
                          project.uuid,
                          name,
                        );
                  },
                );
              },
              onDeleteClick: () {
                PGLog.d('pcgridview onDeleteClick');
                DeleteProjectDialog.show(onConfirm: () async {
                  DeleteProjectDialog.hide();
                  PGDialog.showLoading();
                  final status =
                      await context.read<HomeViewModel>().deleteProjects(
                    [project.uuid],
                  );
                  await PGDialog.dismiss();
                  if (status) {
                    PGDialog.showToast('删除项目成功');
                  } else {
                    PGDialog.showToast('删除项目失败，请稍后再试');
                  }
                });
              },
            ),
          );
        },
        childCount: gridData.projects.length,
      ),
    );
  }
}

// 封装网格数据模型
class ProjectGridData {
  final List<ProjectInfo> projects;
  final SortOption sortOption;
  final bool isProcessing;
  final bool isBatchProcessing;
  final bool isCreateingProject;

  ProjectGridData({
    required this.projects,
    required this.sortOption,
    required this.isProcessing,
    required this.isBatchProcessing,
    required this.isCreateingProject,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProjectGridData &&
          runtimeType == other.runtimeType &&
          projects == other.projects &&
          sortOption == other.sortOption &&
          isProcessing == other.isProcessing &&
          isBatchProcessing == other.isBatchProcessing &&
          isCreateingProject == other.isCreateingProject;

  @override
  int get hashCode =>
      projects.hashCode ^
      sortOption.hashCode ^
      isProcessing.hashCode ^
      isBatchProcessing.hashCode ^
      isCreateingProject.hashCode;
}
