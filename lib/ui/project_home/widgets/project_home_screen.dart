import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/datalayer/repository/photo_thumbnail_repository.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/project_home/widgets/project_home_title_bar_option.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';

import '../../../core/unity/unity_controller.dart';
import '../../../datalayer/repository/auth_repository.dart';
import '../../../datalayer/repository/current_user_repository.dart';
import '../../../datalayer/repository/media_repository.dart';
import '../../../datalayer/repository/project_repository.dart';
import '../../../datalayer/repository/version_intro_repository.dart';
import '../../../routing/navigator_service.dart';
import '../../../utils/pg_dialog/pg_dialog.dart';
import '../../../utils/pg_log.dart';
import '../../../providers/project_state_provider.dart';
import '../../use_case/project/project_usecase_provider.dart';
import '../view_models/home_view_model.dart';
import 'project_home_create_view.dart';
import 'project_home_empty_view.dart';
import 'project_home_grid_view.dart';
import 'project_home_tip_bar.dart';
import 'project_home_top_bar.dart';

/// 项目主页面
class ProjectHomeScreen extends StatefulWidget {
  const ProjectHomeScreen({
    super.key,
    this.title = 'ProjectHomeScreen',
  });

  final String title;

  @override
  State<ProjectHomeScreen> createState() => _ProjectHomeScreenState();
}

class _ProjectHomeScreenState extends State<ProjectHomeScreen> {
  bool _showTipBar = true;
  final double appBarHeight = 80;
  @override
  void initState() {
    super.initState();
    PGLog.d('ProjectHomeScreen - initState');
  }

  @override
  void dispose() {
    PGLog.d('ProjectHomeScreen - dispose');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HomeViewModel(
        GoRouterNavigatorService(context),
        context.read<AuthRepository>(),
        context.read<ProjectRepository>(),
        context.read<ProjectUseCaseProvider>(),
        context.read<UnityUseCaseProvider>(),
        context.read<CurrentUserRepository>(),
        context.read<ProjectStateProvider>(),
        context.read<UnityController>(),
        context.read<PhotoThumbnailRepository>(),
        context.read<VersionIntroRepository>(),
        context.read<WorkspaceUseCaseProvider>(),
        context.read<NoviceGuideManager>(),
        context.read<MediaRepository>(),
      ),
      child: Builder(
        builder: (context) {
          final viewModel = context.watch<HomeViewModel>();
          PGLog.d('ProjectHomeScreen - build');
          // 监听 isWaitingForUnity 状态
          if (viewModel.isWaitingForUnity) {
            PGDialog.showLoading();
          } else {
            PGDialog.dismiss();
          }

          return TitleBarWidget(
              // iPad上这里是否还需要此组件？UI上不显示，所以不做头像回调处理
              funcWidget: ProjectHomeTitleBarOption(),
              child: Scaffold(
                backgroundColor: Colors.black,
                appBar: PreferredSize(
                  preferredSize: Size.fromHeight(appBarHeight),
                  child: const ProjectHomeTopBar(),
                ),
                body: Stack(
                  children: [
                    // 主要内容
                    if (!viewModel.hasProjects)
                      Transform.translate(
                        offset: const Offset(0, -40),
                        child: const Center(
                          child: AnimatedOpacity(
                            opacity: 1.0,
                            duration: Duration(milliseconds: 300),
                            child: ProjectHomeEmptyView(),
                          ),
                        ),
                      )
                    else
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        margin: EdgeInsets.only(
                          top: _showTipBar ? (65 + 16) : 16, // TipBar高度 + 间距
                        ),
                        child: const ProjectHomeGridView(),
                      ),

                    // 创建视图（包含按钮和选项,内部已经处理了图片选择，不需要回调了）
                    const ProjectHomeCreateView(),

                    // 顶部提示条
                    if (_showTipBar) _buildTipBar(),

                    // Unity 预加载视图
                    // if (!UnityManager().isInitialized)
                    //   const PreloadUnityWidget(),
                  ],
                ),
              ));
        },
      ),
    );
  }

  Widget _buildTipBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: AnimatedOpacity(
        opacity: _showTipBar ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        onEnd: () {
          if (!_showTipBar) {
            setState(() {
              // 动画完成后再完全移除
              _showTipBar = false;
            });
          }
        },
        child: _showTipBar
            ? ProjectHomeTipBar(
                onClose: () {
                  setState(() {
                    _showTipBar = false;
                  });
                },
              )
            : const SizedBox.shrink(),
      ),
    );
  }
}
