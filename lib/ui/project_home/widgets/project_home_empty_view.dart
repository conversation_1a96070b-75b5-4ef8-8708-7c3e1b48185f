import 'package:flutter/material.dart';

class ProjectHomeEmptyView extends StatelessWidget {
  const ProjectHomeEmptyView({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/icons/app_icon.png',
            width: 100,
            height: 100,
          ),
          const SizedBox(height: 0),
          const Text(
            '开始精修',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '点击加号按钮开始创建项目。',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: const Color(0xFFEBF2F5).withAlpha(150),
            ),
          ),
        ],
      ),
    );
  }
}
