import 'dart:math';

import 'package:flutter/material.dart';

class ProjectHomeCreateButton extends StatefulWidget {
  final VoidCallback onTap;
  final String text;
  final bool forceExpand;

  const ProjectHomeCreateButton({
    super.key,
    required this.onTap,
    required this.text,
    this.forceExpand = false,
  });

  @override
  State<ProjectHomeCreateButton> createState() =>
      _ProjectHomeCreateButtonState();
}

class _ProjectHomeCreateButtonState extends State<ProjectHomeCreateButton>
    with TickerProviderStateMixin {
  bool isPressed = false;
  bool _clickCreated = false;
  late AnimationController _controller;
  late AnimationController _expandController;
  late AnimationController _gradientController;
  late AnimationController _activeController;
  Animation<double>? _shrinkWidth;
  Animation<double>? _expandWidth;
  Animation<double>? _shrinkRotation;
  Animation<double>? _expandRotation;
  Animation<double>? _iconOpacityAnimation;
  Animation<double>? _textOpacityAnimation;
  Animation<Color?>? _colorAnimation;
  Animation<double>? _iconPositionAnimation;
  Animation<double>? _gradientOpacityAnimation;
  Animation<double>? _expandPositionAnimation;

  final double expandWidth = 180.0;
  final double shrinkWidth = 62.0;
  final double shrinkRotation = 0.125;
  final double expandRotation = 0.0;
  final double iconWidth = 32.0;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _expandController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _gradientController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _activeController = _controller;

    const springCurve = SpringCurve(
      mass: 1.0,
      stiffness: 40.0,
      damping: 8.0,
    );

    _shrinkWidth = Tween<double>(
      begin: expandWidth,
      end: shrinkWidth,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: springCurve,
    ));

    _expandWidth = Tween<double>(
      begin: shrinkWidth,
      end: expandWidth,
    ).animate(CurvedAnimation(
      parent: _expandController,
      curve: springCurve,
    ));

    _shrinkRotation = Tween<double>(
      begin: expandRotation,
      end: shrinkRotation,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: springCurve,
    ));

    _expandRotation = Tween<double>(
      begin: shrinkRotation,
      end: expandRotation,
    ).animate(CurvedAnimation(
      parent: _expandController,
      curve: springCurve,
    ));

    _iconOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.6,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    _textOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutCubicEmphasized,
    ));

    _colorAnimation = ColorTween(
      begin: const Color.fromARGB(255, 0, 220, 212),
      end: const Color.fromARGB(255, 34, 36, 38),
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _iconPositionAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _controller, curve: springCurve));

    _gradientOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _gradientController,
      curve: Curves.linear,
    ));

    _expandPositionAnimation = Tween<double>(
      begin: 1,
      end: 0,
    ).animate(CurvedAnimation(
      parent: _expandController,
      curve: springCurve,
    ));
  }

  void _handleTap() {
    setState(() {
      _clickCreated = true;
      isPressed = !isPressed;
      _activeController = isPressed ? _controller : _expandController;
    });

    if (isPressed) {
      _expandController.reset();
      _controller.forward();
      _gradientController.forward();
    } else {
      _controller.reset();
      _expandController.forward();
      _gradientController.reverse();
    }
    widget.onTap();
  }

  @override
  void didUpdateWidget(ProjectHomeCreateButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.forceExpand && isPressed) {
      _handleExpand();
    }
  }

  void _handleExpand() {
    setState(() {
      isPressed = false;
      _activeController = _expandController;
    });
    _controller.reset();
    _expandController.forward();
    _gradientController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([_activeController, _gradientController]),
        builder: (context, child) {
          final width = !_clickCreated
              ? expandWidth
              : (isPressed
                  ? _shrinkWidth?.value ?? expandWidth
                  : _expandWidth?.value ?? expandWidth);
          final rotation = !_clickCreated
              ? expandRotation
              : (isPressed
                  ? _shrinkRotation?.value ?? expandRotation
                  : _expandRotation?.value ?? expandRotation);
          final textOpacity =
              !_clickCreated ? 1.0 : _textOpacityAnimation?.value ?? 1.0;
          final currentPositionProgress = !_clickCreated
              ? 0.0
              : (isPressed
                  ? _iconPositionAnimation?.value ?? 0.0
                  : _expandPositionAnimation?.value ?? 0.0);
          final int gradientOpacity =
              ((_gradientOpacityAnimation?.value ?? 1.0) * 255).round();
          final startPosition = (width - iconWidth - 8 - 60) / 2;
          final endPosition = width / 2 - iconWidth / 2;
          final iconLeft = startPosition +
              (endPosition - startPosition) * currentPositionProgress;
          return Stack(
            children: [
              Container(
                width: width,
                height: shrinkWidth,
                decoration: BoxDecoration(
                  color: const Color(0xFF222526),
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              Container(
                width: width,
                height: shrinkWidth,
                decoration: BoxDecoration(
                  color: _colorAnimation?.value ?? Colors.transparent,
                  borderRadius: BorderRadius.circular(30),
                  gradient: LinearGradient(
                    colors: [
                      const Color.fromARGB(255, 0, 220, 212)
                          .withAlpha(gradientOpacity),
                      const Color.fromARGB(255, 0, 149, 255)
                          .withAlpha(gradientOpacity),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Stack(
                  fit: StackFit.expand,
                  alignment: Alignment.center,
                  children: [
                    Center(
                      child: Opacity(
                        opacity: textOpacity,
                        child: Container(
                          padding: const EdgeInsets.only(left: 30),
                          child: IntrinsicWidth(
                            child: Text(
                              widget.text,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      left: iconLeft,
                      child: Transform.rotate(
                        angle: rotation * 2 * 3.14159,
                        child: Icon(
                          Icons.add,
                          color: Colors.white
                              .withOpacity(_iconOpacityAnimation?.value ?? 1.0),
                          size: 32,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _expandController.dispose();
    _gradientController.dispose();
    super.dispose();
  }
}

class SpringCurve extends Curve {
  final double mass;
  final double stiffness;
  final double damping;

  const SpringCurve({
    this.mass = 1.0,
    this.stiffness = 100.0,
    this.damping = 10.0,
  });

  @override
  double transform(double t) {
    final omega = sqrt(stiffness / mass);
    final zeta = damping / (2 * sqrt(stiffness * mass));
    final omegaD = omega * sqrt(1.0 - zeta * zeta);
    const A = 1.0;
    final x = exp(-zeta * omega * t) *
        (A * cos(omegaD * t) + (zeta * omega * A) / omegaD * sin(omegaD * t));
    return 1 - x;
  }
}
