import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

import '../../../ui/core/themes/fonts.dart';
import '../../../utils/pg_log.dart';

class MenuItemModel {
  final IconData? icon;
  final String text;
  final Color textColor;
  final Function onTap;
  final bool showDivider; // 是否在此项下方显示分割线
  final bool isSelected; // 是否被选中

  const MenuItemModel({
    this.icon,
    required this.text,
    required this.textColor,
    required this.onTap,
    this.showDivider = false,
    this.isSelected = false,
  });
}

class ProjectHomeMoreActionView {
  static void show({
    required BuildContext context,
    required Offset position,
    required List<MenuItemModel> models,
    VoidCallback? onDismissed,
    double width = 190,
    Color? backgroundColor,
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    final RelativeRect position0 = RelativeRect.fromRect(
      Rect.fromPoints(position, position),
      Offset.zero & overlay.size,
    );

    final double menuHeight = models.length * 40 + 24;

    showMenu(
      context: context,
      position: position0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      constraints: BoxConstraints(
        minWidth: width,
        maxWidth: width,
        minHeight: 90,
        maxHeight: menuHeight,
      ),
      color: backgroundColor ?? const Color(0xFF1F1F1F),
      // 当菜单关闭时触发onDismissed回调
      elevation: 8.0,
      items: _buildMenuItems(models, width),
      surfaceTintColor: backgroundColor ?? const Color(0xFF1F1F1F),
    ).then((value) {
      // 无论菜单如何关闭（点击菜单项、点击外部、返回键等），都会触发此回调
      if (onDismissed != null) {
        onDismissed();
      }
    });
  }

  static List<PopupMenuEntry<dynamic>> _buildMenuItems(
      List<MenuItemModel> models, double width) {
    final List<PopupMenuEntry<dynamic>> items = [];

    for (int i = 0; i < models.length; i++) {
      final model = models[i];

      // 添加菜单项
      items.add(
        PopupMenuItem(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          onTap: () {
            PGLog.d(
                'ProjectHomeMoreActionView - onTap model.text: ${model.text}');
            model.onTap();
          },
          child: _buildMenuItemContent(model, width),
        ),
      );

      // 如果需要显示分割线且不是最后一项
      if (model.showDivider && i < models.length - 1) {
        items.add(
          const PopupMenuDivider(
            height: 17, // 上下各8px间距 + 1px高度
          ),
        );
      }
    }

    return items;
  }

  static Widget _buildMenuItemContent(MenuItemModel model, double width) {
    return _MenuItemContent(model: model, width: width);
  }
}

class _MenuItemContent extends StatefulWidget {
  final MenuItemModel model;
  final double width;

  const _MenuItemContent({required this.model, required this.width});

  @override
  State<_MenuItemContent> createState() => _MenuItemContentState();
}

class _MenuItemContentState extends State<_MenuItemContent> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Container(
        width: widget.width,
        height: 40,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: _isHovered
              ? const Color(0xFFFFFFFF).withOpacity(0.05)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
        ),
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            if (widget.model.icon != null) ...[
              Padding(
                padding: const EdgeInsets.only(left: 6),
                child: Icon(
                  widget.model.icon,
                  size: 16,
                  color: widget.model.textColor,
                ),
              ),
              const SizedBox(width: 6),
            ],
            Text(
              widget.model.text,
              style: TextStyle(
                color: widget.model.isSelected
                    ? const Color(0xFFF72561)
                    : widget.model.textColor,
                fontWeight: Fonts.medium,
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
