import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/shortcut_keys/shortcut_keys.dart';
import 'package:turing_art/datalayer/repository/shortcut_keys_repository.dart';
import 'package:turing_art/ui/shortcut_keys/view_model/shortcut_keys_view_model.dart';

class ShortcutKeysWidget extends StatelessWidget {
  final Function onClose;

  const ShortcutKeysWidget({super.key, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ShortcutKeysViewModel(
        repository: context.read<ShortcutKeysRepository>(),
      ),
      child: _ShortcutKeysContent(onClose: onClose),
    );
  }
}

class _ShortcutKeysContent extends StatelessWidget {
  final Function onClose;

  const _ShortcutKeysContent({required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 700,
      height: 500,
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            '快捷键指南',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () => onClose(),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Consumer<ShortcutKeysViewModel>(
      builder: (context, viewModel, _) {
        if (viewModel.isLoading) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          );
        }

        if (viewModel.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  viewModel.error!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        }

        return Row(
          children: [
            _buildNavigation(viewModel),
            Expanded(
              child: _buildShortcutList(viewModel),
            ),
          ],
        );
      },
    );
  }

  Widget _buildNavigation(ShortcutKeysViewModel viewModel) {
    return SizedBox(
      width: 200,
      child: ListView.builder(
        itemCount: viewModel.categories.length,
        itemBuilder: (context, categoryIndex) {
          final category = viewModel.categories[categoryIndex];
          final isSelected = viewModel.selectedCategoryIndex == categoryIndex;

          return InkWell(
            onTap: () => viewModel.setSelectedCategory(categoryIndex),
            child: Container(
              margin: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 2,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white.withOpacity(0.05)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                category.title,
                style: TextStyle(
                  color:
                      isSelected ? Colors.white : Colors.white.withOpacity(0.6),
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShortcutList(ShortcutKeysViewModel viewModel) {
    final selectedCategory = viewModel.selectedCategory;
    if (selectedCategory == null) {
      return const SizedBox();
    }

    final ScrollController scrollController = ScrollController();

    return Container(
      color: Colors.transparent,
      padding: const EdgeInsets.all(24),
      child: RawScrollbar(
        thumbColor: Colors.white.withOpacity(0.3),
        radius: const Radius.circular(2),
        thickness: 4,
        thumbVisibility: true,
        controller: scrollController,
        child: ListView.builder(
          controller: scrollController,
          itemCount: selectedCategory.groups.length,
          itemBuilder: (context, groupIndex) {
            final group = selectedCategory.groups[groupIndex];
            final isExpanded = viewModel.isGroupExpanded(groupIndex);

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (groupIndex > 0) const SizedBox(height: 32),
                InkWell(
                  onTap: () => viewModel.toggleGroupExpansion(groupIndex),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 20,
                        child: Icon(
                          isExpanded
                              ? Icons.keyboard_arrow_down
                              : Icons.keyboard_arrow_right,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      Text(
                        group.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isExpanded) ...[
                  const SizedBox(height: 16),
                  ...group.shortcuts.map((shortcut) => Padding(
                        padding: const EdgeInsets.only(left: 20, right: 10),
                        child: _buildShortcutItem(shortcut),
                      )),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildShortcutItem(ShortcutItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            item.title,
            style: TextStyle(
              color: const Color(0xFFEBF2F5).withOpacity(0.6),
              fontSize: 14,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A2A),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              item.shortcut,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
