import 'package:flutter/widgets.dart';

import '../../../datalayer/domain/models/shortcut_keys/shortcut_keys.dart';
import '../../../datalayer/repository/shortcut_keys_repository.dart';

class ShortcutKeysViewModel extends ChangeNotifier {
  final ShortcutKeysRepository repository;
  bool _isLoading = true;
  String? _error;
  List<ShortcutCategory> _categories = [];
  int _selectedCategoryIndex = -1;
  // 使用 Map 存储每个分类的展开状态，key 为分类索引，value 为该分类下展开的组索引集合
  final Map<int, Set<int>> _categoryExpandedGroups = {};

  ShortcutKeysViewModel({required this.repository}) {
    _loadShortcuts();
  }

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ShortcutCategory> get categories => _categories;
  int get selectedCategoryIndex => _selectedCategoryIndex;

  // 获取当前选中的分类
  ShortcutCategory? get selectedCategory =>
      _selectedCategoryIndex >= 0 && _selectedCategoryIndex < _categories.length
          ? _categories[_selectedCategoryIndex]
          : null;

  Future<void> _loadShortcuts() async {
    try {
      _isLoading = true;
      notifyListeners();

      _categories = await repository.getShortcutCategories();

      // 设置默认选中第一个分类
      _setDefaultSelectedCategory();

      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _setDefaultSelectedCategory() {
    // 设置默认选中第一个分类
    if (_categories.isNotEmpty) {
      _selectedCategoryIndex = 0;

      // 为每个分类默认展开第一个分组
      for (int i = 0; i < _categories.length; i++) {
        final category = _categories[i];
        if (category.groups.isNotEmpty) {
          // 为该分类创建展开组集合（如果不存在）
          final expandedGroups = _categoryExpandedGroups[i] ??= {};
          // 默认展开第一个组
          expandedGroups.add(0);
        }
      }
    }
  }

  void setSelectedCategory(int index) {
    if (_selectedCategoryIndex != index) {
      _selectedCategoryIndex = index;
      notifyListeners();
    }
  }

  void toggleGroupExpansion(int groupIndex) {
    if (_selectedCategoryIndex < 0) {
      return;
    }

    // 获取当前分类的展开状态集合，如果不存在则创建新的
    final expandedGroups =
        _categoryExpandedGroups[_selectedCategoryIndex] ??= {};

    if (expandedGroups.contains(groupIndex)) {
      expandedGroups.remove(groupIndex);
    } else {
      expandedGroups.add(groupIndex);
    }
    notifyListeners();
  }

  bool isGroupExpanded(int groupIndex) {
    if (_selectedCategoryIndex < 0) {
      return false;
    }
    return _categoryExpandedGroups[_selectedCategoryIndex]
            ?.contains(groupIndex) ??
        false;
  }

  /// 重新加载数据
  Future<void> reload() async {
    await _loadShortcuts();
  }
}
