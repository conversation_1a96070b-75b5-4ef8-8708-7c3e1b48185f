import 'package:flutter/material.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'web_view_dialog.dart';

/// WebViewDialog 使用示例
class WebViewDialogExample extends StatelessWidget {
  const WebViewDialogExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WebView 弹窗示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _showBasicWebViewDialog(context),
              child: const Text('显示基本 WebView 弹窗'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _showCustomizedWebViewDialog(context),
              child: const Text('显示自定义 WebView 弹窗'),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示基本 WebView 弹窗
  void _showBasicWebViewDialog(BuildContext context) {
    WebViewDialog.show(
      context,
      url: 'https://flutter.dev',
      title: 'Flutter 官网',
    );
  }

  /// 显示自定义 WebView 弹窗
  void _showCustomizedWebViewDialog(BuildContext context) {
    WebViewDialog.show(
      context,
      url: 'https://pub.dev',
      title: 'Pub.dev',
      width: 1000,
      height: 700,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, size: 20),
          onPressed: () {
            // 可以通过 GlobalKey 获取 WebViewDialog 的状态来操作 WebView
            // 或者使用其他状态管理方式
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('刷新功能需要通过 GlobalKey 实现')),
            );
          },
          tooltip: '刷新',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(
            minWidth: 36,
            minHeight: 36,
          ),
        ),
      ],
      loadingWidget: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            '正在加载...',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
      onJavaScriptResult: (result) {
        PGLog.d('JavaScript 执行结果: $result');
      },
    );
  }
}
