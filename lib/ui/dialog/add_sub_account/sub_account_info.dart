import 'package:flutter/material.dart';

/// 子账号输入项
class SubAccountItem {
  final TextEditingController nameController;
  final TextEditingController phoneController;
  String phoneErrorText;
  bool nameError;
  bool phoneError;

  SubAccountItem({
    required this.nameController,
    required this.phoneController,
    required this.nameError,
    required this.phoneError,
    required this.phoneErrorText,
  });
}
