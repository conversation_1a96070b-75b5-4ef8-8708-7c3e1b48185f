import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/employee_repository.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/dialog/add_sub_account/add_sub_account_view_model.dart';
import 'package:turing_art/ui/dialog/add_sub_account/sub_account_info.dart';
import 'package:turing_art/ui/login/formatters/phone_number_input_formatter.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 子账号添加对话框
class AddSubAccountDialog extends StatefulWidget {
  const AddSubAccountDialog({
    super.key,
  });

  /// 显示添加子账号对话框
  static void show() {
    // 使用PGDialog来显示自定义对话框，固定高度
    PGDialog.showCustomDialog(
      tag: DialogTags.addSubAccount,
      width: 426,
      height: 568, // 固定高度
      needBlur: true,
      child: const AddSubAccountDialog(),
    );
  }

  @override
  State<AddSubAccountDialog> createState() => _AddSubAccountDialogState();
}

class _AddSubAccountDialogState extends State<AddSubAccountDialog> {
  // 子账号列表
  final List<SubAccountItem> _subAccounts = [];

  // 验证码输入控制器
  final TextEditingController _verificationCodeController =
      TextEditingController();

  // 倒计时控制
  Timer? _countdownTimer;
  int _countdownSeconds = 0;
  bool _isGettingCode = false;
  List<String> _existMobileList = [];

  // 验证码错误状态
  bool _isVerificationCodeError = false;

  // 验证码错误描述
  String _verificationCodeErrorDescription = '验证码为空';

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 初始化至少一个子账号输入项
    _subAccounts.add(SubAccountItem(
      nameController: TextEditingController(),
      phoneController: TextEditingController(),
      nameError: false,
      phoneError: false,
      phoneErrorText: '',
    ));

    // 为第一个子账号添加文本变化监听
    _addTextChangedListeners(_subAccounts[0]);

    // 添加验证码输入监听，清除错误状态
    _verificationCodeController.addListener(() {
      if (_isVerificationCodeError) {
        setState(() {
          _verificationCodeErrorDescription = '';
          _isVerificationCodeError = false;
        });
      }
    });
  }

  @override
  void dispose() {
    // 移除所有监听器
    _verificationCodeController.removeListener(() {});
    _verificationCodeController.dispose();

    for (var account in _subAccounts) {
      account.nameController.removeListener(() {});
      account.phoneController.removeListener(() {});
      account.nameController.dispose();
      account.phoneController.dispose();
    }

    _countdownTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  // 开始验证码倒计时
  void _startCountdown() {
    setState(() {
      _countdownSeconds = 60;
      _isGettingCode = true;
    });

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdownSeconds > 0) {
          _countdownSeconds--;
        } else {
          _isGettingCode = false;
          timer.cancel();
        }
      });
    });
  }

  // 获取验证码
  void _getVerificationCode(BuildContext context) {
    if (_isGettingCode) {
      return;
    }
    context.read<AddSubAccountViewModel>().sendVerificationCode();
    _startCountdown();
    // // 模拟获取验证码成功
    PGDialog.showToast('验证码已发送');
  }

  // 添加文本变化监听器
  void _addTextChangedListeners(SubAccountItem item) {
    item.nameController.addListener(() {
      if (item.nameError) {
        setState(() {
          item.nameError = false;
        });
      }
    });

    item.phoneController.addListener(() {
      if (item.phoneError) {
        setState(() {
          item.phoneError = false;
        });
      }
    });
  }

  // 添加新的子账号输入项
  void _addSubAccount() {
    final newItem = SubAccountItem(
      nameController: TextEditingController(),
      phoneController: TextEditingController(),
      nameError: false,
      phoneError: false,
      phoneErrorText: '',
    );

    // 为新子账号添加文本变化监听
    _addTextChangedListeners(newItem);

    setState(() {
      _subAccounts.add(newItem);
    });

    // 在下一帧渲染完成后滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 滚动到列表底部
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  // 删除子账号输入项
  void _removeSubAccount(int index) {
    if (_subAccounts.length <= 1) {
      return;
    }

    setState(() {
      final item = _subAccounts.removeAt(index);
      // 移除文本监听器
      item.nameController.removeListener(() {});
      item.phoneController.removeListener(() {});
      // 销毁控制器
      item.nameController.dispose();
      item.phoneController.dispose();
    });
  }

  // 检查响应的手机号错误状态
  void _checkResponsePhoneNumberErrorStatus(String phoneNumberErrorText) {
    // 检查所有输入的手机号是否在错误列表中
    for (int i = 0; i < _subAccounts.length; i++) {
      final phoneNumber = _subAccounts[i].phoneController.text;
      if (_existMobileList.contains(phoneNumber)) {
        _subAccounts[i].phoneError = true;
        _subAccounts[i].phoneErrorText = phoneNumberErrorText;
      }
    }
  }

  List<String> _findDuplicates(List<String> list) {
    Set<String> seen = {};
    Set<String> duplicates = {};

    for (String item in list) {
      if (seen.contains(item)) {
        duplicates.add(item);
      } else {
        seen.add(item);
      }
    }

    return duplicates.toList();
  }

  // 验证输入
  bool _validateInputs(BuildContext context) {
    bool isValid = true;

    // 验证子账号输入
    for (int i = 0; i < _subAccounts.length; i++) {
      final item = _subAccounts[i];
      final nameIsEmpty = item.nameController.text.isEmpty;
      final phoneIsInvalid = !context
          .read<AddSubAccountViewModel>()
          .isValidPhoneNumber(item.phoneController.text);

      setState(() {
        item.nameError = nameIsEmpty;
        item.phoneError = phoneIsInvalid;
        item.phoneErrorText = '手机号码不正确';
      });

      if (nameIsEmpty || phoneIsInvalid) {
        isValid = false;
      }
    }

    // 验证手机号是否重复
    _existMobileList = _findDuplicates(
        _subAccounts.map((item) => item.phoneController.text).toList());
    if (_existMobileList.isNotEmpty) {
      setState(() {
        _checkResponsePhoneNumberErrorStatus('手机号重复啦~');
      });
      isValid = false;
    }

    // 验证验证码
    final verificationCode = _verificationCodeController.text;
    final isVerificationCodeValid = verificationCode.length == 4;

    setState(() {
      _isVerificationCodeError = !isVerificationCodeValid;
      _verificationCodeErrorDescription =
          isVerificationCodeValid ? '' : '验证码输入错误';
    });

    if (!isVerificationCodeValid) {
      isValid = false;
    }

    return isValid;
  }

  // 提交添加子账号
  void _confirmAddSubAccounts(BuildContext context) async {
    if (!_validateInputs(context)) {
      return;
    }

    PGDialog.showLoading();
    // 创建子账号信息列表
    final subAccounts = _subAccounts.map((item) {
      final phoneText = item.phoneController.text;
      String mobile;
      int cc;

      // 拆分手机号为区号和真实手机号
      if (phoneText.contains('+')) {
        // 海外格式：区号+手机号
        final parts = phoneText.split('+');
        cc = int.parse(parts[0]);
        mobile = parts[1];
      } else {
        // 大陆格式：默认区号86
        cc = 86;
        mobile = phoneText;
      }

      return EmployeeInfo(
        nickname: item.nameController.text,
        mobile: mobile,
        cc: cc,
      );
    }).toList();

    final apiError = await context.read<AddSubAccountViewModel>().addSubAccount(
          _verificationCodeController.text,
          subAccounts,
        );
    await PGDialog.dismiss();
    if (apiError == null) {
      PGDialog.showToast('添加成功');
      PGDialog.dismiss(tag: DialogTags.addSubAccount);
    } else {
      PGDialog.showToast(apiError.message);
      if (apiError.code == 10539 || apiError.code == 10537) {
        setState(() {
          _verificationCodeErrorDescription = apiError.message;
          _isVerificationCodeError = true;
        });
      } else if (apiError.code == 400 &&
          apiError.message.contains(EmployeeRepository.mobileExistMessage)) {
        // 格式：mobiles exists:***********,***********
        _existMobileList = apiError.message.split(':')[1].split(',');
        setState(() {
          _checkResponsePhoneNumberErrorStatus('添加的手机号已存在');
        });
      }
    }
  }

  // 获取手机号后4位
  String _getPhoneNumberLast4Digits(BuildContext context) {
    return context.read<CurrentUserRepository>().user?.mobile.substring(7) ??
        '';
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<AddSubAccountViewModel>(
      create: (context) => AddSubAccountViewModel(
        employeeRepository: context.read<EmployeeRepository>(),
      ),
      lazy: false,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1B1C1F),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildSubAccountList(),
            _buildAddMoreButton(),
            _buildDivider(),
            _buildVerificationSection(_getPhoneNumberLast4Digits(context)),
            Consumer<AddSubAccountViewModel>(
              builder: (context, viewModel, _) {
                return _buildConfirmButton(context);
              },
            ),
            _buildFooterText(),
          ],
        ),
      ),
    );
  }

  // 构建头部
  Widget _buildHeader() {
    return Container(
      height: 48,
      padding: const EdgeInsets.only(left: 16, right: 12),
      child: Row(
        children: [
          Text(
            '添加子账号',
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              height: 22 / 16,
              color: Colors.white,
            ),
          ),
          const Spacer(),
          SizedBox(
            width: 24,
            height: 24,
            child: IconButton(
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Image.asset(
                'assets/icons/home_window_close.png',
                width: 24,
                height: 24,
              ),
              onPressed: () {
                PGDialog.dismiss(tag: DialogTags.addSubAccount);
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建子账号列表
  Widget _buildSubAccountList() {
    return SizedBox(
      height: 240, // 固定高度为240
      child: RawScrollbar(
        thumbVisibility: true,
        controller: _scrollController,
        thumbColor: Colors.white.withOpacity(0.3),
        radius: const Radius.circular(2),
        thickness: 4,
        child: ListView.builder(
          controller: _scrollController,
          itemCount: _subAccounts.length,
          itemBuilder: (context, index) {
            return _buildSubAccountItem(index);
          },
        ),
      ),
    );
  }

  // 构建子账号输入项
  Widget _buildSubAccountItem(int index) {
    final item = _subAccounts[index];
    final showDeleteButton = _subAccounts.length > 1;

    return SizedBox(
      height: 92, // 明确设置高度
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题行
            SizedBox(
              height: 20, // 标题行固定高度
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '子账号 ${(index + 1).toString().padLeft(2, '0')}',
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      height: 1.0,
                      color: Colors.white,
                    ),
                  ),
                  if (showDeleteButton)
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        icon: Image.asset(
                          'assets/icons/list_delete.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () => _removeSubAccount(index),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            // 输入框行 - 使用IntrinsicHeight确保高度一致
            IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start, // 顶部对齐输入框
                children: [
                  // 名称输入框包装在固定高度容器中
                  SizedBox(
                    width: 193,
                    child: _buildAlignedInputField(
                      controller: item.nameController,
                      hintText: '输入名称',
                      isError: item.nameError,
                      errorText: '请输入名称',
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(30),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 手机号输入框包装在固定高度容器中
                  SizedBox(
                    width: 193,
                    child: _buildAlignedInputField(
                      controller: item.phoneController,
                      hintText: '绑定手机号',
                      isError: item.phoneError,
                      errorText: item.phoneErrorText,
                      keyboardType: TextInputType.text, // 改为text类型以支持"+"字符
                      inputFormatters: [
                        PhoneNumberInputFormatter(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建保持对齐的输入框
  Widget _buildAlignedInputField({
    required TextEditingController controller,
    required String hintText,
    required bool isError,
    required String errorText,
    TextInputType keyboardType = TextInputType.text,
    List<TextInputFormatter>? inputFormatters,
  }) {
    // 定义错误文本的高度
    const double errorTextHeight = 19; // 15px高度 + 4px上边距

    // 检查是否为手机号输入框并且号码已存在
    bool isExistingMobile = false;
    String customErrorText = errorText;

    // 如果是手机号输入框且有值，检查是否已存在
    if (keyboardType == TextInputType.phone &&
        controller.text.isNotEmpty &&
        _existMobileList.contains(controller.text)) {
      isExistingMobile = true;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 输入框总是保持相同的位置
        Container(
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFF2D2E31),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isError || isExistingMobile
                  ? const Color(0xFFF55C44)
                  : Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          // 移除Center包装，使TextField直接填充整个Container
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFFE1E2E5),
              height: 1.0,
            ),
            // 添加文本变化监听，实时检查手机号是否已存在
            onChanged: keyboardType == TextInputType.phone
                ? (value) {
                    if (_existMobileList.contains(value) && !isError) {
                      setState(() {
                        // 查找该手机号所在的所有子账号并标记为错误
                        for (int i = 0; i < _subAccounts.length; i++) {
                          if (_subAccounts[i].phoneController.text == value) {
                            _subAccounts[i].phoneError = true;
                            _subAccounts[i].phoneErrorText = '手机号重复了~';
                          }
                        }
                      });
                    }
                  }
                : null,
            // 使用expands和maxLines使TextField填充整个容器高度
            expands: true,
            maxLines: null,
            // 设置TextAlignVertical.center使文本在容器内垂直居中
            textAlignVertical: TextAlignVertical.center,
            decoration: InputDecoration(
              // 确保内边距合适，内容不会太靠边缘
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              // 确保TextField使用整个可用空间
              isCollapsed: true,
              // 移除边框，因为我们在Container上已经有边框了
              border: InputBorder.none,
              hintText: hintText,
              hintStyle: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
              ),
            ),
          ),
        ),
        // 错误文本或占位容器，确保高度一致
        SizedBox(
          height: errorTextHeight,
          child: (isError || isExistingMobile)
              ? Padding(
                  padding: const EdgeInsets.only(top: 4, left: 2),
                  child: Text(
                    isExistingMobile ? customErrorText : errorText,
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      height: 15 / 11,
                      color: const Color(0xFFF55C44),
                    ),
                  ),
                )
              : const SizedBox.shrink(), // 空占位，保持高度一致
        ),
      ],
    );
  }

  // 构建添加更多按钮
  Widget _buildAddMoreButton() {
    return InkWell(
      onTap: _addSubAccount,
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/list_add.png',
              width: 24,
              height: 24,
            ),
            Text(
              '继续添加',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 15,
                fontWeight: FontWeight.w500,
                height: 20 / 15,
                color: const Color(0xFFE1E2E5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建分割线
  Widget _buildDivider() {
    return Container(
      height: 1,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.white.withOpacity(0.15),
    );
  }

  // 构建验证码部分
  Widget _buildVerificationSection(String phoneNumberLast4Digits) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12),
          Text(
            '主账号认证',
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          RichText(
            text: TextSpan(
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
              ),
              children: [
                const TextSpan(text: '发送验证码到尾号 '),
                TextSpan(
                  text: phoneNumberLast4Digits,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const TextSpan(text: ' 的手机号'),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Consumer<AddSubAccountViewModel>(
            builder: (context, viewModel, _) {
              return _buildVerificationInput(context);
            },
          ),
          // _buildVerificationInput(),
        ],
      ),
    );
  }

  // 构建验证码输入框
  Widget _buildVerificationInput(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFF2D2E31),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: _isVerificationCodeError
                  ? const Color(0xFFF55C44)
                  : Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                // 移除Center包装，使TextField直接填充整个区域
                child: TextField(
                  controller: _verificationCodeController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFFE1E2E5),
                    height: 1.0,
                  ),
                  // 使用expands和maxLines设置来填充整个容器高度
                  expands: true,
                  maxLines: null,
                  textAlignVertical: TextAlignVertical.center,
                  decoration: InputDecoration(
                    // 调整内边距使文本显示适当
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    isCollapsed: true,
                    border: InputBorder.none,
                    hintText: '输入验证码',
                    hintStyle: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFFEBEDF5).withOpacity(0.65),
                    ),
                  ),
                ),
              ),
              Center(
                child: GestureDetector(
                  onTap: _isGettingCode
                      ? null
                      : () => _getVerificationCode(context),
                  child: Container(
                    padding: const EdgeInsets.only(right: 12),
                    child: Text(
                      _isGettingCode
                          ? '${_countdownSeconds}s'
                          : _countdownSeconds == 0 && _isGettingCode == false
                              ? '获取验证码'
                              : '获取验证码',
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: _isGettingCode
                            ? const Color(0xFFEBEDF5).withOpacity(0.65)
                            : const Color(0xFFF72561),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (_isVerificationCodeError)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 2),
            child: Text(
              _verificationCodeErrorDescription,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 11,
                fontWeight: FontWeight.w500,
                height: 15 / 11,
                color: const Color(0xFFF55C44),
              ),
            ),
          ),
      ],
    );
  }

  // 构建确认按钮
  Widget _buildConfirmButton(BuildContext context) {
    // 动态计算顶部边距：有错误显示时为13px，无错误显示时为32px
    final topMargin = _isVerificationCodeError ? 13.0 : 32.0;

    return Container(
      height: 40,
      margin: EdgeInsets.only(left: 16, right: 16, top: topMargin),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _confirmAddSubAccounts(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF72561),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        child: Text(
          '确认添加',
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 20 / 14,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  // 构建底部说明文字
  Widget _buildFooterText() {
    return Container(
      margin: const EdgeInsets.only(left: 45, right: 45, top: 12, bottom: 16),
      child: Text(
        '添加的账号在使用图灵精修时会扣除当前主账户购买的张数。\n张数扣除后不支持找回，请慎重操作。随时可停用。',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: Fonts.defaultFontFamily,
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: const Color(0xFFEBEDF5).withOpacity(0.5),
        ),
      ),
    );
  }
}
