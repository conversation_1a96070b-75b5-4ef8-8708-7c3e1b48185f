import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/core/ui/blur_container.dart';
import 'package:turing_art/ui/dialog/core/animated_dialog.dart';
import 'package:turing_art/ui/dialog/core/input_dialog.dart';

import '../../utils/screen_util.dart';

/// 修改工程名称弹窗
/// 由InputDialog组件实现UI
class ProjectRenameDialog {
  static const _tag = "ProjectRenameDialog";

  /// 展示弹窗
  /// [title] 标题
  /// [defaultInputText] 默认输入文案
  /// [onConfirm] 确认回调 无参数传入时，默认点击事件为关闭弹窗，如果要传入参数，则需要手动处理弹窗关闭
  static show(
      {String title = "项目名称",
      String defaultInputText = "项目名称",
      Function(String)? onConfirm}) {
    SmartDialog.show(
        animationType: SmartAnimationType.fade,
        animationBuilder: (controller, child, param) =>
            AnimatedDialog(controller: controller, child: child),
        maskWidget: BlurContainer(
          blur: 24,
          child: SizedBox(
            width: ScreenUtil().screenWidth,
            height: ScreenUtil().screenHeight,
          ),
        ),
        builder: (context) => InputDialog(
              title: title,
              defaultInputText: defaultInputText,
              onConfirm: onConfirm ??
                  (str) {
                    hide();
                  },
              onClose: hide,
            ),
        tag: _tag);
  }

  /// 隐藏弹窗
  static hide() {
    SmartDialog.dismiss(tag: _tag);
  }
}
