import 'package:flutter/widgets.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../utils/screen_util.dart';
import '../core/ui/blur_container.dart';
import '../shortcut_keys/widget/shortcut_keys_widget.dart';
import 'core/animated_dialog.dart';

/// 修改工程名称弹窗
/// 由InputDialog组件实现UI
class ShortcutKeysDialog {
  static const _tag = "ShortcutKeysDialog";

  /// 展示快捷键弹窗
  static void show() {
    SmartDialog.show(
        animationType: SmartAnimationType.fade,
        animationBuilder: (controller, child, param) =>
            AnimatedDialog(controller: controller, child: child),
        maskWidget: BlurContainer(
          blur: 24,
          child: SizedBox(
            width: ScreenUtil().screenWidth,
            height: ScreenUtil().screenHeight,
          ),
        ),
        builder: (context) => const ShortcutKeysWidget(onClose: hide),
        tag: _tag);
  }

  /// 隐藏弹窗
  static void hide() {
    SmartDialog.dismiss(tag: _tag);
  }
}
