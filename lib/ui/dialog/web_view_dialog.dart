import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:webview_windows/webview_windows.dart';

/// WebView 弹窗
/// 用于在 Windows 平台上显示网页内容的弹窗
class WebViewDialog extends StatefulWidget {
  /// 网页 URL
  final String url;

  /// 弹窗标题
  final String? title;

  /// 弹窗宽度，默认为 800
  final double width;

  /// 弹窗高度，默认为 600
  final double height;

  /// 是否显示关闭按钮，默认为 true
  final bool showCloseButton;

  /// 是否显示标题栏，默认为 true
  final bool showTitleBar;

  /// 加载时的占位组件
  final Widget? loadingWidget;

  /// 初始化失败时的占位组件
  final Widget? errorWidget;

  /// 自定义操作按钮
  final List<Widget>? actions;

  /// JavaScript 执行完成回调
  final Function(String)? onJavaScriptResult;

  const WebViewDialog({
    super.key,
    required this.url,
    this.title,
    this.width = 800,
    this.height = 600,
    this.showCloseButton = true,
    this.showTitleBar = true,
    this.loadingWidget,
    this.errorWidget,
    this.actions,
    this.onJavaScriptResult,
  });

  @override
  State<WebViewDialog> createState() => _WebViewDialogState();

  /// 显示 WebView 弹窗
  static Future<void> show(
    BuildContext context, {
    required String url,
    String? title,
    double width = 800,
    double height = 600,
    bool showCloseButton = true,
    bool showTitleBar = true,
    Widget? loadingWidget,
    Widget? errorWidget,
    List<Widget>? actions,
    Function(String)? onJavaScriptResult,
  }) async {
    // 仅在 Windows 平台上显示 WebView
    if (!Platform.isWindows) {
      PGLog.d('WebViewDialog 仅支持 Windows 平台');
      return;
    }

    // 检查 WebView2 是否可用
    try {
      // 在 webview_windows 0.4.0 中，没有直接的方法检查 WebView2 是否可用
      // 我们尝试创建一个 WebviewController 并初始化它来检查可用性
      final controller = WebviewController();
      await controller.initialize();
      await controller.dispose();
    } catch (e) {
      // WebView2 不可用，显示提示对话框
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('WebView2 不可用'),
            content: const Text(
              '请安装 WebView2 Runtime 以使用网页功能。\n'
              '您可以从 Microsoft 官方网站下载安装：\n'
              'https://developer.microsoft.com/en-us/microsoft-edge/webview2/',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
      return;
    }

    // 显示 WebView 弹窗
    if (context.mounted) {
      await showDialog(
        context: context,
        builder: (context) => WebViewDialog(
          url: url,
          title: title,
          width: width,
          height: height,
          showCloseButton: showCloseButton,
          showTitleBar: showTitleBar,
          loadingWidget: loadingWidget,
          errorWidget: errorWidget,
          actions: actions,
          onJavaScriptResult: onJavaScriptResult,
        ),
      );
    }
  }
}

class _WebViewDialogState extends State<WebViewDialog> {
  final _controller = WebviewController();
  bool _isWebViewAvailable = false;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  Future<void> _initWebView() async {
    try {
      // 初始化 WebView 控制器
      await _controller.initialize();

      // 配置 WebView
      await _configureWebView();

      // 加载 URL
      await _controller.loadUrl(widget.url);

      // 更新状态
      if (mounted) {
        setState(() {
          _isWebViewAvailable = true;
        });
      }
    } on PlatformException catch (e) {
      PGLog.d('WebView 初始化失败: ${e.message}');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '初始化失败: ${e.message}';
        });
      }
    } catch (e) {
      PGLog.d('WebView 初始化失败: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '初始化失败: $e';
        });
      }
    }
  }

  Future<void> _configureWebView() async {
    // 设置 WebView 背景色
    await _controller.setBackgroundColor(Colors.white);

    // 打开开发者工具（webview_windows 0.4.0 中没有 setDevToolsEnabled 方法）
    // 如果需要打开开发者工具，可以使用 openDevTools 方法
    // await _controller.openDevTools();

    // 配置 WebView 事件监听
    _controller.loadingState.listen((state) {
      if (state == LoadingState.navigationCompleted) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    });

    _controller.onLoadError.listen((error) {
      PGLog.d('WebView 加载错误: $error');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '加载错误: $error';
        });
      }
    });

    // 如果需要处理 JavaScript 执行结果
    if (widget.onJavaScriptResult != null) {
      // 在 webview_windows 0.4.0 中，没有 onExecuteJavaScriptResult 事件
      // 使用 executeScript 方法可以直接获取结果
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      elevation: 8.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题栏
            if (widget.showTitleBar) _buildTitleBar(),

            // 内容区域
            Expanded(
              child: _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8.0),
          topRight: Radius.circular(8.0),
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[300]!,
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 标题
          Expanded(
            child: Text(
              widget.title ?? '网页内容',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 操作按钮
          Row(
            children: [
              // 自定义操作按钮
              if (widget.actions != null) ...widget.actions!,

              // 关闭按钮
              if (widget.showCloseButton)
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: '关闭',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    // 发生错误
    if (_hasError) {
      return widget.errorWidget ??
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _hasError = false;
                      _isLoading = true;
                    });
                    _controller.reload();
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
    }

    // WebView 未初始化或正在加载
    if (!_isWebViewAvailable) {
      return widget.loadingWidget ??
          const Center(
            child: CircularProgressIndicator(),
          );
    }

    // 显示 WebView
    return Stack(
      children: [
        // WebView
        Webview(_controller),

        // 加载指示器
        if (_isLoading)
          widget.loadingWidget ??
              const Center(
                child: CircularProgressIndicator(),
              ),
      ],
    );
  }

  /// 执行 JavaScript 代码
  Future<String?> executeScript(String script) async {
    if (_isWebViewAvailable && !_hasError) {
      try {
        final result = await _controller.executeScript(script);
        if (widget.onJavaScriptResult != null) {
          widget.onJavaScriptResult?.call(result ?? '');
        }
        return result;
      } catch (e) {
        PGLog.d('执行 JavaScript 失败: $e');
        rethrow;
      }
    }
    return null;
  }
}
