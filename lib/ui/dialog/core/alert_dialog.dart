// external DeleteProjectDialog
import 'package:flutter/cupertino.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// AlertDialog组件
/// 一个具有两个按钮的弹窗提示组件
/// 此弹窗需要由外部Dialog工具绘制背景或者其他效果
/// 弹窗自身只是一个大小为320*210的弹窗
/// [onConfirm] 确认回调
/// [onCancel] 取消回调
/// [confirmTextColor] 确认按钮文案颜色
/// [cancelTextColor] 取消按钮文案颜色
/// [confirmText] 确认按钮文案
/// [cancelText] 取消按钮文案
/// [title] 标题
/// [content] 内容
class AlertDialog extends StatelessWidget {
  final Function? onConfirm;
  final Function? onCancel;
  final Color confirmTextColor;
  final Color cancelTextColor;
  final String confirmText;
  final String cancelText;
  final String title;
  final String content;

  const AlertDialog(
      {super.key,
      this.onConfirm,
      this.onCancel,
      required this.title,
      required this.content,
      this.confirmText = "确认",
      this.cancelText = "取消",
      this.confirmTextColor = const Color(0xFFE0694F),
      this.cancelTextColor = const Color(0x99EBF2F5)});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320,
      padding: const EdgeInsets.only(top: 24, bottom: 24),
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x33000000),
            offset: Offset(0, 4),
            blurRadius: 40,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              color: const Color(0xFFFFFFFF),
              fontWeight: Fonts.semiBold,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              content,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: const Color(0x99EBF2F5),
                fontWeight: Fonts.regular,
                fontFamily: Fonts.defaultFontFamily,
              ),
            ),
          ),
          const SizedBox(height: 24),
          GestureDetector(
            onTap: () {
              onConfirm?.call();
            },
            child: Container(
              padding:
                  const EdgeInsets.symmetric(vertical: 12, horizontal: 108),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xFF222526)),
              child: Text(confirmText,
                  style: TextStyle(
                    fontSize: 14,
                    color: confirmTextColor,
                    fontWeight: Fonts.semiBold,
                    fontFamily: Fonts.defaultFontFamily,
                  )),
            ),
          ),
          const SizedBox(height: 4),
          if (onCancel != null)
            GestureDetector(
              onTap: () {
                onCancel?.call();
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 108),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0x00FFFFFF)),
                child: Text(cancelText,
                    style: TextStyle(
                      fontSize: 14,
                      color: cancelTextColor,
                      fontWeight: Fonts.semiBold,
                      fontFamily: Fonts.defaultFontFamily,
                    )),
              ),
            )
        ],
      ),
    );
  }
}
