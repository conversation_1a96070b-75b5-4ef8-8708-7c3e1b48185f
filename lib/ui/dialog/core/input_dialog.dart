import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/gradient_button.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_action.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_intent.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_state_mixin.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_wrapper.dart';

/// 输入框弹窗组件UI
/// 此弹窗具有当焦点聚焦时，自动上浮弹窗，以避免键盘遮挡输入框
/// 在使用时请勿在外包裹其他影响尺寸的容器，否则会影响上浮弹窗的效果
/// 弹窗已经自带了背景模糊效果，无需外部dialog工具来实现
/// [title] 标题
/// [defaultInputText] 默认输入文案
/// [onConfirm] 确认回调
class InputDialog extends StatefulWidget {
  final String title;
  final String defaultInputText;
  final Function(String)? onConfirm;
  final Function? onClose;

  final TextEditingController _controller;
  final FocusNode _focusNode;

  InputDialog({
    super.key,
    required this.title,
    this.onConfirm,
    required this.defaultInputText,
    this.onClose,
  })  : _controller = TextEditingController(text: defaultInputText),
        // 在构造函数初始化
        _focusNode = FocusNode();

  @override
  State<InputDialog> createState() => _InputDialogState();
}

class _InputDialogState extends State<InputDialog> with ShortcutKeyStateMixin {
  @override
  Map<ShortcutActivator, Intent> defineShortcuts() {
    return {
      LogicalKeySet(LogicalKeyboardKey.enter): const EnterIntent(),
    };
  }

  @override
  Map<Type, Action<Intent>> defineActions() {
    return {
      EnterIntent: ShortcutKeyAction(
        intent: const EnterIntent(),
        callback: () => widget.onConfirm?.call(widget._controller.text),
      ),
    };
  }

  @override
  Widget build(BuildContext context) {
    widget._controller.text = widget.defaultInputText;
    widget._focusNode.requestFocus();
    return ShortcutKeyWrapper(
      child: Actions(
        actions: defineActions(),
        child: GestureDetector(
          onTap: () {
            widget.onClose?.call();
          },
          child: Scaffold(
              // 使用Scaffold是为了让输入弹窗能够在键盘弹起时自适应布局
              // 由于这个弹窗组件是全屏的，所以SmartDialog无法处理Mask点击事件，又组件自行处理
              backgroundColor: Colors.transparent,
              resizeToAvoidBottomInset: true,
              body: Center(
                child: GestureDetector(
                  onTap: () {
                    //创建一个空的点击事件来阻断父级的点击事件
                  },
                  child: Container(
                    width: 320,
                    height: 210,
                    padding: const EdgeInsets.only(top: 24),
                    decoration: BoxDecoration(
                      color: const Color(0xFF121415),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x33000000),
                          offset: Offset(0, 4),
                          blurRadius: 40,
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 24, right: 24, bottom: 24),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.title,
                            style: TextStyle(
                              fontSize: 16,
                              color: const Color(0xFFFFFFFF),
                              fontWeight: Fonts.semiBold,
                              fontFamily: Fonts.defaultFontFamily,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Stack(
                            children: [
                              CupertinoTextField(
                                controller: widget._controller,
                                focusNode: widget._focusNode,
                                padding: const EdgeInsets.all(16),
                                style: TextStyle(
                                  color: const Color(0xFFFFFFFF),
                                  fontSize: 14,
                                  fontWeight: Fonts.semiBold,
                                  fontFamily: Fonts.defaultFontFamily,
                                ),
                                cursorColor: const Color(0xFFFFFFFF),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF000000),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              Positioned(
                                  right: 0,
                                  top: 0,
                                  bottom: 0,
                                  child: GestureDetector(
                                    onTap: () {
                                      widget._controller.text = "";
                                    },
                                    behavior: HitTestBehavior.opaque,
                                    child: Container(
                                      margin: const EdgeInsets.all(16),
                                      width: 20,
                                      height: 20,
                                      child: Image.asset(
                                          "assets/icons/input_clear.png"),
                                    ),
                                  ))
                            ],
                          ),
                          const SizedBox(height: 24),
                          GestureDetector(
                            onTap: () {
                              widget.onConfirm?.call(widget._controller.text);
                            },
                            child: GradientButton(
                                text: "确认",
                                height: 52,
                                borderRadius: 8,
                                onPressed: () {
                                  //TODO 这个参数在组件内好像没有被调用
                                  // onConfirm?.call(_controller.text);
                                },
                                gradientColors: const [
                                  Color(0xFF00DCD4),
                                  Color(0xFF0095FF),
                                ]),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              )),
        ),
      ),
    );
  }
}
