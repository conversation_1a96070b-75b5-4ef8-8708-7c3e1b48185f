import 'dart:ui';

import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/dialog/core/alert_dialog.dart';
import 'package:turing_art/ui/dialog/core/animated_dialog.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class SingleButtonDialog {
  static const _tag = "SingleButtonDialog";

  /// 展示弹窗
  /// [onConfirm] 确认回调 无参数传入时，默认点击事件为关闭弹窗，如果要传入参数，则需要手动处理弹窗关闭
  static void show({
    required String title,
    required String content,
    // 确认回调
    Function? onConfirm,
    String confirmText = "确认",
  }) {
    SmartDialog.show(
      maskColor: const Color(0x99000000),
      animationType: SmartAnimationType.fade,
      builder: (context) => AlertDialog(
        title: title,
        content: content,
        confirmText: confirmText,
        onConfirm: onConfirm ?? hide,
      ),
      animationTime: const Duration(milliseconds: 300),
      animationBuilder: (controller, child, param) =>
          AnimatedDialog(controller: controller, child: child),
      tag: _tag,
    );
  }

  /// 隐藏弹窗
  static void hide() {
    PGDialog.dismiss(tag: _tag);
  }
}
