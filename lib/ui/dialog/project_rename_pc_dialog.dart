import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_action.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_intent.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_state_mixin.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_wrapper.dart';

/// PC端项目重命名对话框
class ProjectRenamePCDialog {
  /// 展示弹窗
  /// [currentName] 当前项目名称，如果为空则使用默认提示文字
  /// [onConfirm] 确认回调
  static void show({
    String? currentName,
    required Function(String) onConfirm,
  }) {
    PGLog.d('ProjectRenamePCDialog - show');
    PGDialog.showCustomDialog(
      tag: DialogTags.projectRename,
      width: 400,
      height: 200,
      needBlur: false,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF121315),
          borderRadius: BorderRadius.circular(12),
        ),
        child: _ProjectRenameDialogContent(
          currentName: currentName,
          onConfirm: onConfirm,
        ),
      ),
    );
  }

  /// 隐藏弹窗
  static void hide() {
    PGDialog.dismiss(tag: DialogTags.projectRename);
  }
}

class _ProjectRenameDialogContent extends StatefulWidget {
  final String? currentName;
  final Function(String) onConfirm;

  const _ProjectRenameDialogContent({
    this.currentName,
    required this.onConfirm,
  });

  @override
  State<_ProjectRenameDialogContent> createState() =>
      _ProjectRenameDialogContentState();
}

class _ProjectRenameDialogContentState
    extends State<_ProjectRenameDialogContent> with ShortcutKeyStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  // bool _isFocused = false;

  // 默认提示文字
  static const String _defaultHintText = "请输入项目名称";

  @override
  Map<ShortcutActivator, Intent> defineShortcuts() {
    return {
      LogicalKeySet(LogicalKeyboardKey.enter): const EnterIntent(),
    };
  }

  @override
  Map<Type, Action<Intent>> defineActions() {
    return {
      EnterIntent: ShortcutKeyAction(
        intent: const EnterIntent(),
        callback: _onConfirm,
      ),
    };
  }

  @override
  void initState() {
    super.initState();
    // 如果currentName不为空，则使用currentName，否则使用空字符串
    _controller = TextEditingController(text: widget.currentName ?? "");
    _controller.addListener(_onTextChanged);
    _focusNode = FocusNode();
    // 不自动请求焦点，让用户点击输入框时才弹出键盘
    _focusNode.addListener(() {
      // setState(() {
      //   _isFocused = _focusNode.hasFocus;
      // });
    });
    // 使用Future.microtask确保在构建完成后请求焦点
    Future.microtask(() {
      // 请求焦点并将光标放在文本末尾
      _focusNode.requestFocus();
      // 如果有文本，将光标放在文本末尾
      if (_controller.text.isNotEmpty) {
        _controller.selection = TextSelection.fromPosition(
          TextPosition(offset: _controller.text.length),
        );
      }
    });
  }

  void _onTextChanged() {
    setState(() {
      // 文本变化时更新状态
    });
  }

  void _onConfirm() {
    if (_controller.text.isNotEmpty) {
      widget.onConfirm(_controller.text);
      ProjectRenamePCDialog.hide();
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ShortcutKeyWrapper(
      child: Actions(
        actions: defineActions(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // 居中显示标题
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Text(
                    "项目名称",
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () => ProjectRenamePCDialog.hide(),
                    child: Icon(
                      Icons.close,
                      color: const Color(0xFFEBEDF5).withOpacity(0.6),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),

            // 输入框间距
            const SizedBox(height: 2),

            // 输入框
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: SizedBox(
                height: 44,
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  autofocus: true, // 添加自动获取焦点
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  decoration: InputDecoration(
                    hintText: _defaultHintText,
                    hintStyle: TextStyle(
                      color: const Color(0xFFEBEDF5).withOpacity(0.6),
                      fontSize: 16,
                    ),
                    filled: true,
                    fillColor: Colors.transparent,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withOpacity(0.06),
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withOpacity(0.06),
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Colors.white.withOpacity(0.06),
                        width: 1,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    suffixIcon: null, // 移除清空按钮
                  ),
                ),
              ),
            ),

            // 确认按钮
            Padding(
              padding: const EdgeInsets.only(
                  left: 24, right: 24, top: 24, bottom: 24),
              child: InkWell(
                onTap: _controller.text.isNotEmpty
                    ? () {
                        widget.onConfirm(_controller.text);
                        ProjectRenamePCDialog.hide();
                      }
                    : null,
                child: Container(
                  height: 48,
                  decoration: BoxDecoration(
                    color: _controller.text.isNotEmpty
                        ? const Color(0xFFF72561)
                        : const Color(0xFFF72561).withOpacity(0.5), // 禁用状态使用半透明
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      "确认",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
