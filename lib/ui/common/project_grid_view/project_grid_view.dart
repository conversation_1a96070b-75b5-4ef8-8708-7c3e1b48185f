import 'package:flutter/material.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_grid_calculator.dart';

/// 通用projectGridView组件，AIGC和精修项目共用（精修项目暂时还未使用）
class ProjectGridView<T> extends StatelessWidget {
  const ProjectGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.itemWidth = 240.0,
    this.itemHeight = 240.0,
    this.itemSpacing = 24.0,
    this.minCount = 1,
    this.maxCount = 20,
    this.padding,
  });

  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final double itemWidth;
  final double itemHeight;
  final double itemSpacing;
  final int minCount;
  final int maxCount;
  final EdgeInsetsGeometry? padding;

  // 静态装饰，避免每次重建时创建新对象
  static final BoxDecoration _itemDecoration = BoxDecoration(
    color: Colors.transparent,
    borderRadius: BorderRadius.circular(6),
  );

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 使用ProjectHomePCGridCalculator计算网格布局，保持一致的列数计算逻辑
        final screenWidth = MediaQuery.of(context).size.width;
        final layout = ProjectHomePCGridCalculator.calculate(screenWidth);

        // 只使用计算出的列数，其他参数保持原有配置
        final int crossAxisCount = layout.itemsPerRow.clamp(minCount, maxCount);

        return GridView.builder(
          padding: padding,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: itemSpacing,
            mainAxisSpacing: itemSpacing,
            childAspectRatio: itemWidth / itemHeight,
          ),
          itemCount: items.length,
          itemBuilder: (context, index) {
            return Container(
              width: itemWidth,
              height: itemHeight,
              decoration: _itemDecoration,
              child: itemBuilder(context, items[index], index),
            );
          },
        );
      },
    );
  }
}
