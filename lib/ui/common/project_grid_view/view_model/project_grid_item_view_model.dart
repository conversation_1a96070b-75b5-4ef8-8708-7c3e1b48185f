import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:turing_art/utils/pg_log.dart';

enum CoverState {
  loading,
  loaded,
  error,
  notFound,
}

/// 封面生成器函数
typedef CoverThumbnailGenerator = FutureOr<File?> Function(
  String projectId,
  String fileId,
);

class ProjectGridItemViewModel extends ChangeNotifier {
  final CoverThumbnailGenerator _projectCoverGenerater;
  final String _projectId;
  final List<String> _fileIds; // 三张封面的 fileId

  // 每个封面的状态管理
  final Map<String, CoverState> _coverStates = {};
  final Map<String, String?> _coverPaths = {};
  final Map<String, String?> _errorMessages = {};

  // 添加销毁标志
  bool _disposed = false;

  ProjectGridItemViewModel(
    this._projectCoverGenerater,
    this._projectId,
    this._fileIds,
  ) {
    // 异步初始化，不阻塞UI线程
    _initializeCoversAsync();
  }

  // Getters
  String get projectId => _projectId;
  List<String> get fileIds => _fileIds;

  /// 获取指定封面的状态
  CoverState getCoverState(String fileId) =>
      _coverStates[fileId] ?? CoverState.loading;

  /// 获取指定封面的路径
  String? getCoverPath(String fileId) => _coverPaths[fileId];

  /// 获取指定封面的错误信息
  String? getErrorMessage(String fileId) => _errorMessages[fileId];

  /// 获取指定封面的文件
  File? getCoverFile(String fileId) {
    final path = _coverPaths[fileId];
    if (path != null) {
      final file = File(path);
      return file.existsSync() ? file : null;
    }
    return null;
  }

  /// 检查指定封面是否存在
  bool hasCover(String fileId) =>
      getCoverState(fileId) == CoverState.loaded && _coverPaths[fileId] != null;

  /// 整体状态
  bool get isLoading =>
      _coverStates.values.any((state) => state == CoverState.loading);
  bool get hasAnyCover => _coverPaths.values.any((path) => path != null);
  List<String> get availableCoverPaths =>
      _coverPaths.values.where((path) => path != null).cast<String>().toList();

  /// 安全地通知监听器
  void _safeNotifyListeners() {
    if (!_disposed) {
      notifyListeners();
    }
  }

  /// 异步初始化所有封面（不阻塞UI）
  void _initializeCoversAsync() {
    // 立即设置所有封面为加载状态
    for (final fileId in _fileIds) {
      _coverStates[fileId] = CoverState.loading;
    }
    _safeNotifyListeners();

    // 异步处理，不等待完成
    _initializeCovers().catchError((e) {
      PGLog.e('ProjectGridItemViewModel: 异步初始化封面失败 - $e');
    });
  }

  /// 初始化所有封面
  Future<void> _initializeCovers() async {
    try {
      PGLog.d(
          'ProjectGridItemViewModel: 开始初始化封面 - projectId: $_projectId, fileIds: $_fileIds');

      // 并发生成所有封面，但不等待完成
      for (final fileId in _fileIds) {
        _generateCover(fileId).catchError((e) {
          PGLog.e(
              'ProjectGridItemViewModel: 生成封面失败 - fileId: $fileId, error: $e');
        });
      }
    } catch (e) {
      PGLog.e('ProjectGridItemViewModel: 初始化封面失败 - $e');
      // 设置所有封面为错误状态
      for (final fileId in _fileIds) {
        _coverStates[fileId] = CoverState.error;
        _errorMessages[fileId] = e.toString();
      }
      _safeNotifyListeners();
    }
  }

  /// 生成单个封面
  Future<void> _generateCover(String fileId) async {
    try {
      _coverStates[fileId] = CoverState.loading;
      _errorMessages[fileId] = null;
      _safeNotifyListeners();

      // 使用 ProjectCoverGenerater 生成封面
      final coverFile = await _projectCoverGenerater(_projectId, fileId);

      // 检查是否已被销毁
      if (_disposed) {
        PGLog.d(
            'ProjectGridItemViewModel: ViewModel 已被销毁，跳过更新 - fileId: $fileId');
        return;
      }

      if (coverFile != null && coverFile.existsSync()) {
        _coverPaths[fileId] = coverFile.path;
        _coverStates[fileId] = CoverState.loaded;
        PGLog.d(
            'ProjectGridItemViewModel: 封面生成成功 - fileId: $fileId, path: ${coverFile.path}');
      } else {
        _coverStates[fileId] = CoverState.notFound;
        PGLog.w('ProjectGridItemViewModel: 封面生成失败 - fileId: $fileId, 文件不存在');
      }
    } catch (e) {
      if (!_disposed) {
        _coverStates[fileId] = CoverState.error;
        _errorMessages[fileId] = e.toString();
        PGLog.e(
            'ProjectGridItemViewModel: 封面生成异常 - fileId: $fileId, error: $e');
      }
    } finally {
      _safeNotifyListeners();
    }
  }

  /// 手动刷新指定封面
  Future<void> refreshCover(String fileId) async {
    if (_disposed) {
      PGLog.w('ProjectGridItemViewModel: 尝试在已销毁的 ViewModel 上刷新封面');
      return;
    }
    await _generateCover(fileId);
  }

  /// 手动刷新所有封面
  Future<void> refreshAllCovers() async {
    if (_disposed) {
      PGLog.w('ProjectGridItemViewModel: 尝试在已销毁的 ViewModel 上刷新所有封面');
      return;
    }

    // 重置所有封面状态
    for (final fileId in _fileIds) {
      _coverStates[fileId] = CoverState.loading;
      _errorMessages[fileId] = null;
    }
    _safeNotifyListeners();

    // 并发生成所有封面，但不等待完成
    for (final fileId in _fileIds) {
      _generateCover(fileId).catchError((e) {
        PGLog.e(
            'ProjectGridItemViewModel: 刷新封面失败 - fileId: $fileId, error: $e');
      });
    }
  }

  @override
  void dispose() {
    PGLog.d('ProjectGridItemViewModel: 销毁 - projectId: $_projectId');
    _disposed = true;
    super.dispose();
  }
}
