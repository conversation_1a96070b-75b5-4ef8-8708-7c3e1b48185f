import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/service/project_cover_generater/project_cover_generater.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'package:turing_art/ui/common/project_grid_view/project_images_layout.dart';
import 'package:turing_art/ui/common/project_grid_view/view_model/project_grid_item_view_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/project_home/widgets/project_home_more_action_view.dart';

/// 项目网格项UI模型
class ProjectUIModel {
  final List<String> coverImages; // 前三张封面图
  final int totalPhotos; // 总照片数
  final String title; // 标题
  final String time; // 时间

  ProjectUIModel({
    required this.coverImages,
    required this.totalPhotos,
    required this.title,
    required this.time,
  });
}

/// 右键菜单配置
class MenuConfig {
  final bool showBatchEdit;
  final bool showRename;
  final bool showDelete;

  const MenuConfig({
    this.showBatchEdit = true,
    this.showRename = true,
    this.showDelete = true,
  });
}

/// 通用项目网格项组件
class ProjectGridItem extends StatefulWidget {
  final ProjectUIModel uiModel;
  final String projectId; // 用于操作的项目ID
  final VoidCallback? onTap;
  final VoidCallback? onBatchEdit;
  final VoidCallback? onRename;
  final VoidCallback? onDelete;
  final double imageContainerRatio;
  final MenuConfig menuConfig;
  final bool showBatchEdit;

  const ProjectGridItem({
    super.key,
    required this.uiModel,
    required this.projectId,
    this.onTap,
    this.onBatchEdit,
    this.onRename,
    this.onDelete,
    this.imageContainerRatio = 240 / 178,
    this.menuConfig = const MenuConfig(),
    this.showBatchEdit = true,
  });

  @override
  State<ProjectGridItem> createState() => _ProjectGridItemState();
}

class _ProjectGridItemState extends State<ProjectGridItem> {
  final ValueNotifier<bool> _hoveredNotifier = ValueNotifier(false);
  final ValueNotifier<bool> _isMoreHoveredNotifier = ValueNotifier(false);
  final ValueNotifier<bool> _showMenuNotifier = ValueNotifier(false);

  @override
  void dispose() {
    _hoveredNotifier.dispose();
    _isMoreHoveredNotifier.dispose();
    _showMenuNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final itemSize = Size(constraints.maxWidth, constraints.maxHeight);
        final imageContainerHeight =
            itemSize.height / widget.imageContainerRatio;

        return ChangeNotifierProvider<ProjectGridItemViewModel>(
          key: ValueKey(widget.uiModel.coverImages.join('_')),
          create: (context) => ProjectGridItemViewModel(
            (projectId, fileId) {
              return ProjectCoverGenerater(
                aigcService: context.read<AigcService>(),
                mediaRepository: context.read<MediaRepository>(),
              ).getCoverThumbnail(projectId, fileId);
            },
            widget.projectId,
            widget.uiModel.coverImages,
          ),
          child: PlatformMouseRegion(
            cursor: SystemMouseCursors.click,
            onEnter: (_) => _hoveredNotifier.value = true,
            onExit: (_) => _hoveredNotifier.value = false,
            child: SizedBox(
              width: itemSize.width,
              height: itemSize.height,
              child: Column(
                children: [
                  _buildImageContainer(imageContainerHeight, itemSize.width),
                  _buildInfoSection(
                    itemSize.width,
                    itemSize.height - imageContainerHeight,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建图片容器
  Widget _buildImageContainer(double height, double width) {
    return ValueListenableBuilder<bool>(
      valueListenable: _hoveredNotifier,
      builder: (context, hovered, child) {
        return Container(
          decoration: BoxDecoration(
            color: const Color(0xFF222526),
            borderRadius: BorderRadius.circular(6),
            border: hovered
                ? Border.all(
                    color: const Color(0xFFF72561),
                    width: 2,
                  )
                : null,
          ),
          height: height,
          padding: hovered ? const EdgeInsets.all(2) : null,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: DebounceClickWidget(
              onSecondaryTapDown: _showContextMenu,
              onTap: widget.onTap,
              child: _buildCoverContent(
                  hovered ? width - 4 : width, hovered ? height - 4 : height),
            ),
          ),
        );
      },
    );
  }

  /// 构建封面内容
  Widget _buildCoverContent(double width, double height) {
    return Consumer<ProjectGridItemViewModel>(
      builder: (context, viewModel, child) {
        // 获取可用的封面路径
        final availablePaths = viewModel.availableCoverPaths;

        if (availablePaths.isEmpty && viewModel.isLoading) {
          // 所有封面都在加载中
          return Container(
            width: width,
            height: height,
            color: const Color(0xFF1A1B1C),
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF666666)),
                ),
              ),
            ),
          );
        }

        // 使用 ProjectImagesLayout 显示封面
        return ProjectImagesLayout(
          images: availablePaths,
          projectId: widget.projectId,
          width: width,
          height: height,
        );
      },
    );
  }

  /// 构建信息区域
  Widget _buildInfoSection(double width, double height) {
    return SizedBox(
      height: height,
      width: width,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: DebounceClickWidget(
              onSecondaryTapDown: _showContextMenu,
              onTap: widget.onTap,
              behavior: HitTestBehavior.opaque,
              child: SizedBox(
                height: height,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 12),
                    Text(
                      widget.uiModel.title,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.medium,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          '${widget.uiModel.totalPhotos}张',
                          style: TextStyle(
                            color: const Color(0xFFB2B2B2),
                            fontSize: 12,
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.regular,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            widget.uiModel.time,
                            style: TextStyle(
                              color: const Color(0xFFB2B2B2),
                              fontSize: 12,
                              fontFamily: Fonts.defaultFontFamily,
                              fontWeight: Fonts.regular,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          // 鼠标悬停或显示菜单时显示更多按钮
          ValueListenableBuilder<bool>(
            valueListenable: _hoveredNotifier,
            builder: (context, hovered, child) {
              return ValueListenableBuilder<bool>(
                valueListenable: _showMenuNotifier,
                builder: (context, showMenu, child) {
                  if (!hovered && !showMenu) {
                    return const SizedBox(width: 34); // 保持布局稳定，包含间距
                  }

                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(width: 2),
                      ValueListenableBuilder<bool>(
                        valueListenable: _isMoreHoveredNotifier,
                        builder: (context, isMoreHovered, child) {
                          return PlatformMouseRegion(
                            cursor: SystemMouseCursors.click,
                            onEnter: (_) => _isMoreHoveredNotifier.value = true,
                            onExit: (_) => _isMoreHoveredNotifier.value = false,
                            child: GestureDetector(
                              onTapDown: _showContextMenu,
                              child: Container(
                                width: 32,
                                height: 32,
                                margin: const EdgeInsets.only(top: 6),
                                decoration: BoxDecoration(
                                  color: isMoreHovered
                                      ? const Color(0xFF1E1F21)
                                      : const Color(0xFF121415),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Center(
                                  child: Image.asset(
                                    "assets/icons/project_item_more.png",
                                    width: 24,
                                    height: 24,
                                    color: const Color(0xFFEBEDF5)
                                        .withOpacity(0.6),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  /// 显示右键菜单
  void _showContextMenu(TapDownDetails details) {
    final List<MenuItemModel> menuItems = [];

    if (widget.menuConfig.showBatchEdit && widget.onBatchEdit != null) {
      menuItems.add(MenuItemModel(
        icon: Icons.check_box_outlined,
        text: '批量修图',
        textColor: Colors.white,
        onTap: widget.onBatchEdit!,
      ));
    }

    if (widget.menuConfig.showRename && widget.onRename != null) {
      menuItems.add(MenuItemModel(
        icon: Icons.edit_outlined,
        text: '重命名',
        textColor: Colors.white,
        onTap: widget.onRename!,
      ));
    }

    if (widget.menuConfig.showDelete && widget.onDelete != null) {
      menuItems.add(MenuItemModel(
        icon: Icons.delete_outline,
        text: '删除',
        textColor: const Color(0xFFFF4D4F),
        onTap: widget.onDelete!,
      ));
    }

    if (menuItems.isNotEmpty) {
      // 直接显示菜单，不等待重建完成
      // 这样可以减少一次重建过程
      ProjectHomeMoreActionView.show(
        context: context,
        position: details.globalPosition,
        models: menuItems,
        onDismissed: () {
          // 菜单关闭时将_showMenu设置为false
          _showMenuNotifier.value = false;
        },
      );

      // 菜单显示后再更新状态，这不会影响菜单的显示速度
      _showMenuNotifier.value = true;
    }
  }
}
