import 'dart:io';

import 'package:flutter/material.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 项目图片布局组件
/// 支持1张、2张、3张及以上图片的不同布局
class ProjectImagesLayout extends StatelessWidget {
  final List<String> images;
  final String projectId;
  final double width;
  final double height;

  const ProjectImagesLayout({
    super.key,
    required this.images,
    required this.projectId,
    required this.width,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (images.isEmpty) {
      return _buildPlaceholder();
    } else if (images.length == 1) {
      return _buildSingleImage(context, images[0]);
    } else if (images.length == 2) {
      return _buildTwoImages(context);
    } else {
      return _buildThreeOrMoreImages(context);
    }
  }

  /// 构建单张图片布局
  Widget _buildSingleImage(BuildContext context, String imageId) {
    return SizedBox(
      width: width,
      height: height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: _buildImage(context, imageId, true),
      ),
    );
  }

  /// 构建两张图片布局
  Widget _buildTwoImages(BuildContext context) {
    return Row(
      children: [
        // 左侧图片
        Expanded(
          child: SizedBox(
            height: height,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                bottomLeft: Radius.circular(6),
              ),
              child: _buildImage(context, images[0], true),
            ),
          ),
        ),
        // 中间分隔线
        Container(
          width: 2,
          height: height,
          color: Colors.black,
        ),
        // 右侧图片
        Expanded(
          child: SizedBox(
            height: height,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(6),
                bottomRight: Radius.circular(6),
              ),
              child: _buildImage(context, images[1], true),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建三张及以上图片布局
  Widget _buildThreeOrMoreImages(BuildContext context) {
    return Row(
      children: [
        // 左侧大图 (占总宽度的 150/240)
        Expanded(
          flex: 150,
          child: SizedBox(
            height: height,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                bottomLeft: Radius.circular(6),
              ),
              child: _buildImage(context, images[0], true),
            ),
          ),
        ),
        // 中间分隔线
        Container(
          width: 2,
          height: height,
          color: Colors.black,
        ),
        // 右侧两张小图 (占总宽度的 88/240，即 240-150-2)
        Expanded(
          flex: 88,
          child: Column(
            children: [
              // 上方小图
              Expanded(
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(6),
                  ),
                  child: images.length > 1
                      ? _buildImage(context, images[1], false)
                      : _buildPlaceholder(),
                ),
              ),
              // 分隔线
              Container(
                width: double.infinity,
                height: 2,
                color: Colors.black,
              ),
              // 下方小图
              Expanded(
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(6),
                  ),
                  child: images.length > 2
                      ? _buildImage(context, images[2], false)
                      : _buildPlaceholder(),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建单个图片
  Widget _buildImage(BuildContext context, String coverPath, bool isLarge) {
    // 根据当前布局和宽度计算合适的缓存宽度
    int cacheWidth = isLarge ? 300 : 150;

    return SizedBox.expand(
      child: Image.file(
        File(coverPath),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        cacheWidth: cacheWidth,
        filterQuality: FilterQuality.medium,
        frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
          return AnimatedOpacity(
            opacity: frame != null ? 1 : 0,
            duration: const Duration(milliseconds: 200),
            child: child,
          );
        },
        errorBuilder: (context, error, stackTrace) {
          PGLog.e('图片加载失败: $error\n$stackTrace');
          return _buildPlaceholder();
        },
      ),
    );
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    if (projectId == NoviceGuideManager.demoProjectId) {
      PGLog.i('加载本地demo项目缩略图');
      return Image.asset(
        NoviceGuideManager.demoProjectThumbnailPath,
        fit: BoxFit.cover,
        cacheWidth: 300,
        filterQuality: FilterQuality.medium,
      );
    }
    return Container(color: const Color(0xFF1A1B1C));
  }
}
