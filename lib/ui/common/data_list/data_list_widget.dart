import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/app_constants.dart';

/// 通用数据列表组件
///
/// 封装列表的加载状态、空状态显示和滚动条
/// 支持自定义Item构建器
class DataListWidget<T> extends StatelessWidget {
  /// 是否正在加载
  final bool isLoading;

  /// 数据列表
  final List<T> items;

  /// 自定义Item构建器
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// 列表高度
  final double height;

  /// 列表内边距
  final EdgeInsetsGeometry? padding;

  /// 空状态文本
  final String emptyText;

  /// 每项的固定高度，如果提供，可以提高列表性能
  final double? itemExtent;

  /// 自定义加载指示器
  final Widget? loadingIndicator;

  /// 自定义空状态Widget
  final Widget? emptyWidget;

  const DataListWidget({
    super.key,
    required this.isLoading,
    required this.items,
    required this.itemBuilder,
    required this.height,
    this.padding,
    this.emptyText = '暂无数据',
    this.itemExtent,
    this.loadingIndicator,
    this.emptyWidget,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: loadingIndicator ??
            CircularProgressIndicator(
              color: AppConstants.isDesktop
                  ? const Color(0xFFF71561) // 和loading保持一致
                  : const Color(0xFF00B6DE),
            ),
      );
    }

    if (items.isEmpty) {
      return Center(
        child: emptyWidget ??
            Text(
              emptyText,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
                fontSize: 14,
                fontWeight: Fonts.medium,
              ),
            ),
      );
    }

    final ScrollController scrollController = ScrollController();

    return RawScrollbar(
      thumbVisibility: true,
      trackVisibility: false,
      thickness: 4,
      radius: const Radius.circular(16),
      thumbColor: const Color(0xFF676767),
      controller: scrollController,
      child: SizedBox(
        height: height,
        child: ListView.builder(
          controller: scrollController,
          padding: padding,
          itemCount: items.length,
          itemExtent: itemExtent,
          itemBuilder: (context, index) {
            return itemBuilder(context, items[index], index);
          },
        ),
      ),
    );
  }
}
