import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 带有悬浮提示的文本组件
///
/// 当文本发生溢出时，鼠标悬停将显示一个悬浮提示窗口
class ContainOverFlowTipTextWidget extends StatefulWidget {
  /// 要显示的文本
  final String text;

  /// 文本样式
  final TextStyle style;

  /// 最大宽度，如果为null则使用父组件约束的最大宽度
  final double? maxWidth;

  /// 最大行数
  final int maxLines;

  /// 溢出处理方式
  final TextOverflow overflow;

  /// 提示框偏移量
  final Offset tooltipOffset;

  const ContainOverFlowTipTextWidget({
    super.key,
    required this.text,
    required this.style,
    this.maxWidth,
    this.maxLines = 1,
    this.overflow = TextOverflow.ellipsis,
    this.tooltipOffset = const Offset(0, 4), // 默认和文本框左对齐正下方
  });

  @override
  State<ContainOverFlowTipTextWidget> createState() =>
      _ContainOverFlowTipTextWidgetState();
}

class _ContainOverFlowTipTextWidgetState
    extends State<ContainOverFlowTipTextWidget> {
  /// 悬浮提示窗口
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    // 确保组件销毁时移除悬浮窗
    _removeTooltip();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 创建Text组件以便重用
    final textWidget = Text(
      widget.text,
      style: widget.style,
      overflow: widget.overflow,
      maxLines: widget.maxLines,
    );

    return LayoutBuilder(builder: (context, constraints) {
      final actualMaxWidth = widget.maxWidth ?? constraints.maxWidth;

      // 检测文本是否会溢出
      final bool willOverflow = _willTextOverflow(
        widget.text,
        widget.style,
        actualMaxWidth,
        maxLines: widget.maxLines,
      );

      // 如果不会溢出，直接返回文本组件
      if (!willOverflow) {
        return textWidget;
      }

      // 会溢出时添加悬浮提示
      return MouseRegion(
        onHover: (event) {
          // 鼠标悬停时显示自定义悬浮提示
          _showTooltip(context, event.position);
        },
        onExit: (_) {
          // 鼠标离开时关闭悬浮提示
          _removeTooltip();
        },
        child: textWidget,
      );
    });
  }

  /// 检测文本是否会溢出
  bool _willTextOverflow(
    String text,
    TextStyle style,
    double maxWidth, {
    int maxLines = 1,
  }) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: maxLines,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: double.infinity);

    return textPainter.width > maxWidth;
  }

  /// 显示提示
  void _showTooltip(BuildContext context, Offset position) {
    // 移除旧的提示（如果存在）
    _removeTooltip();

    // 获取当前位置信息
    final RenderBox box = context.findRenderObject() as RenderBox;
    final Offset boxOffset = box.localToGlobal(Offset.zero);

    // 创建浮窗
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: boxOffset.dx + widget.tooltipOffset.dx,
        top: boxOffset.dy + box.size.height + widget.tooltipOffset.dy,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              heightFactor: 1,
              child: Text(
                widget.text,
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium,
                  fontSize: 12,
                  height: 16 / 12, // 行高16px
                  color: const Color(0xFF0C0C0D),
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // 显示提示
    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 移除提示
  void _removeTooltip() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }
}
