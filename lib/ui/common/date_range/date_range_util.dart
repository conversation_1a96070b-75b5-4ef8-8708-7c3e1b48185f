/// 只针对时间输入框的日期时间工具类，提供日期选择器所需的功能
class DateRangeUtil {
  /// 验证日期字符串是否符合yyyy-MM-dd格式
  /// 返回true表示格式正确，false表示格式错误
  static bool isValidDateFormat(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) {
      return false;
    }

    // 正则表达式匹配yyyy-MM-dd格式
    final RegExp dateRegex = RegExp(r'^\d{4}-\d{2}-\d{2}$');
    if (!dateRegex.hasMatch(dateStr)) {
      return false;
    }

    try {
      // 尝试解析日期，检查是否是有效日期
      final parts = dateStr.split('-');
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final day = int.parse(parts[2]);

      if (month < 1 || month > 12) {
        return false;
      }

      // 获取指定月份的最大天数
      final daysInMonth = DateTime(year, month + 1, 0).day;
      if (day < 1 || day > daysInMonth) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 将yyyy-MM-dd格式的日期字符串转换为DateTime对象
  /// 如果格式不正确或日期无效，返回null
  static DateTime? parseDate(String? dateStr) {
    if (!isValidDateFormat(dateStr)) {
      return null;
    }

    try {
      final parts = dateStr!.split('-');
      return DateTime(
        int.parse(parts[0]),
        int.parse(parts[1]),
        int.parse(parts[2]),
      );
    } catch (e) {
      return null;
    }
  }

  /// 将日期格式化为yyyy-MM-dd字符串
  static String formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 格式化日期输入字符串，自动添加分隔符
  /// 例如将"20230809"格式化为"2023-08-09"
  /// [value] 输入值
  /// [oldValue] 修改前的值，用于判断是否为删除操作
  static String formatDateInput(String value, {String? oldValue}) {
    // 检查是否是删除操作
    final bool isDeleteOperation = oldValue != null &&
        oldValue.length > value.length &&
        oldValue.startsWith(value);

    // 如果是删除操作，特殊处理以自动调整分隔符
    if (isDeleteOperation) {
      // 特殊情况处理: 当删除到分隔符位置时
      if (oldValue.endsWith('-')) {
        // 例如oldValue="2023-09-" 再删除一次应该变成"2023-0"
        return value.substring(0, value.length - 1);
      }
      return value;
    }

    // 非删除操作，进行常规格式化
    String formatted = value.replaceAll(RegExp(r'[^0-9\-]'), '');

    // 拆分字符串，只保留数字
    String digitsOnly = formatted.replaceAll('-', '');

    // 限制长度为8位数字
    if (digitsOnly.length > 8) {
      digitsOnly = digitsOnly.substring(0, 8);
    }

    // 重新构建格式化字符串
    String result = '';

    // 添加年份分隔符
    if (digitsOnly.length >= 4) {
      result = '${digitsOnly.substring(0, 4)}-';

      // 添加月份分隔符
      if (digitsOnly.length >= 6) {
        result += '${digitsOnly.substring(4, 6)}-';

        // 添加剩余日期数字
        if (digitsOnly.length > 6) {
          result += digitsOnly.substring(6);
        }
      } else if (digitsOnly.length > 4) {
        // 只有月份的部分数字
        result += digitsOnly.substring(4);
      }
    } else {
      // 不足4位，直接显示
      result = digitsOnly;
    }

    return result;
  }

  /// 将yyyy-MM-dd格式的日期字符串转换为时间戳(秒)
  /// [date] 格式为yyyy-MM-dd的日期字符串
  /// [isEndOfDay] 如果为true，返回当天23:59:59的时间戳；否则返回当天00:00:00的时间戳
  /// 如果转换失败，返回null
  static int? dateStringToTimestamp(String? date, {bool isEndOfDay = false}) {
    if (date == null || date.isEmpty) {
      return null;
    }

    try {
      if (!isValidDateFormat(date)) {
        return null;
      }

      final parts = date.split('-');
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final day = int.parse(parts[2]);

      final DateTime dateTime = isEndOfDay
          ? DateTime(year, month, day, 23, 59, 59)
          : DateTime(year, month, day);

      return dateTime.millisecondsSinceEpoch ~/ 1000;
    } catch (e) {
      return null;
    }
  }
}
