import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/common/date_range/select_date/view_model/select_date_range_view_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 日期网格组件，显示单个月份的日期网格
class DateGridWidget extends StatelessWidget {
  /// 是否是左侧的日历
  final bool isLeft;

  const DateGridWidget({
    super.key,
    required this.isLeft,
  });

  @override
  Widget build(BuildContext context) {
    return Selector<DateRangeViewModel, _DateGridState>(
      selector: (_, viewModel) => _DateGridState(
        focusedDay:
            isLeft ? viewModel.focusedDayLeft : viewModel.focusedDayRight,
        startDate: viewModel.startDate,
        endDate: viewModel.endDate,
        hoveredDay: viewModel.hoveredDay,
      ),
      builder: (context, state, _) {
        final viewModel = context.read<DateRangeViewModel>();

        // 获取当前显示的月份
        final month =
            isLeft ? viewModel.focusedDayLeft : viewModel.focusedDayRight;

        // 计算当月第一天是星期几
        final firstDayOfMonth = DateTime(month.year, month.month, 1);
        final firstWeekday = firstDayOfMonth.weekday % 7;

        // 计算上个月显示的天数
        final daysFromPreviousMonth = firstWeekday;

        // 固定行数（周数）为6行
        const int weeksCount = 6;

        return Column(
          children: List.generate(weeksCount, (weekIndex) {
            return Container(
              height: 32,
              width: 298,
              margin: EdgeInsets.only(
                  top: weekIndex > 0 ? 4 : 8, left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(7, (dayIndex) {
                  // 计算日期信息
                  final dateInfo = viewModel.calculateDateGridInfo(
                    month,
                    weekIndex,
                    dayIndex,
                    daysFromPreviousMonth,
                  );

                  // 计算样式
                  var style = viewModel.calculateDateCellStyle(
                    dateInfo.date,
                    isCurrentMonth: dateInfo.isCurrentMonth,
                  );

                  // 计算选中状态
                  final selectionInfo = viewModel.calculateDateSelectionInfo(
                    dateInfo.date,
                    isCurrentMonth: dateInfo.isCurrentMonth,
                  );

                  // 是否是范围选中的开始或结束
                  final isRangeStartOrEnd = selectionInfo.isSelected &&
                      !selectionInfo.isSingleSelected &&
                      viewModel.startDate != null &&
                      viewModel.endDate != null &&
                      !viewModel.isSameDay(
                          viewModel.startDate!, viewModel.endDate!);

                  return Expanded(
                    child: MouseRegion(
                      onEnter: dateInfo.isCurrentMonth
                          ? (_) => viewModel.onDayHovered(dateInfo.date)
                          : null,
                      onExit: dateInfo.isCurrentMonth
                          ? (_) => viewModel.onDayHovered(null)
                          : null,
                      child: GestureDetector(
                        onTap: dateInfo.isCurrentMonth
                            ? () => viewModel.onDaySelected(dateInfo.date)
                            : null,
                        child: Container(
                          height: 32,
                          decoration: BoxDecoration(
                            color: style.rowBackgroundColor,
                            borderRadius: style.rowBorderRadius,
                          ),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // 日期范围背景(只针对开始时间和结束时间做特殊处理，为了上下和左或者右间距相等)
                              if (isRangeStartOrEnd)
                                Positioned.fill(
                                  child: Container(
                                    margin: selectionInfo.isStartDate
                                        ? const EdgeInsets.only(left: 4)
                                        : selectionInfo.isEndDate
                                            ? const EdgeInsets.only(right: 4)
                                            : EdgeInsets.zero,
                                    decoration: BoxDecoration(
                                      color: style.cellBackgroundColor,
                                      borderRadius: style.rowBorderRadius,
                                    ),
                                  ),
                                ),
                              // 选中背景圆
                              if (selectionInfo.isSelected)
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: style.cellBackgroundColor,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              // 日期文本
                              Text(
                                dateInfo.dayText,
                                style: TextStyle(
                                  fontFamily: Fonts.defaultFontFamily,
                                  fontWeight: Fonts.semiBold,
                                  fontSize: 12,
                                  height: 16 / 12,
                                  color: style.textColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            );
          }),
        );
      },
    );
  }
}

/// 日期网格状态，用于Selector监听
class _DateGridState {
  final DateTime focusedDay;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime? hoveredDay;

  _DateGridState({
    required this.focusedDay,
    this.startDate,
    this.endDate,
    this.hoveredDay,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    if (other is! _DateGridState) {
      return false;
    }
    return other.focusedDay.year == focusedDay.year &&
        other.focusedDay.month == focusedDay.month &&
        _isSameDay(other.startDate, startDate) &&
        _isSameDay(other.endDate, endDate) &&
        _isSameDay(other.hoveredDay, hoveredDay);
  }

  bool _isSameDay(DateTime? a, DateTime? b) {
    if (a == null && b == null) {
      return true;
    }
    if (a == null || b == null) {
      return false;
    }
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  @override
  int get hashCode =>
      focusedDay.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      hoveredDay.hashCode;
}
