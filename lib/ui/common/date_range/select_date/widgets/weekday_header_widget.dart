import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 星期标题组件，显示星期几的标题行
class WeekdayHeaderWidget extends StatelessWidget {
  const WeekdayHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // 星期几标题
    final weekdayNames = ['日', '一', '二', '三', '四', '五', '六'];

    return Container(
      height: 32,
      width: 298,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: List.generate(7, (index) {
          return Expanded(
            child: Container(
              width: 24,
              height: 24,
              alignment: Alignment.center,
              child: Text(
                weekdayNames[index],
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                  fontSize: 12,
                  height: 16 / 12,
                  color: const Color(0xA6EBEDF5),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
