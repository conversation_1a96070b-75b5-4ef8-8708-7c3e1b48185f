import 'package:flutter/material.dart';
import 'package:turing_art/ui/common/date_range/select_date/widgets/date_grid_widget.dart';
import 'package:turing_art/ui/common/date_range/select_date/widgets/date_navigator_widget.dart';
import 'package:turing_art/ui/common/date_range/select_date/widgets/weekday_header_widget.dart';

/// 月历组件，显示单个月份的日历
class MonthCalendarWidget extends StatelessWidget {
  /// 是否是左侧的日历（影响导航按钮的位置）
  final bool isLeft;

  const MonthCalendarWidget({
    super.key,
    required this.isLeft,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 330,
      margin: const EdgeInsets.only(top: 13, bottom: 13),
      decoration: const BoxDecoration(
        color: Color(0xFF1F1F1F),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 月份年份和切换按钮区域
          DateNavigatorWidget(isLeft: isLeft),

          // 星期标题
          const WeekdayHeaderWidget(),

          // 分割线
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 298,
            height: 1,
            color: const Color(0x0FFFFFFF),
          ),

          // 日期网格
          DateGridWidget(
            isLeft: isLeft,
          ),
        ],
      ),
    );
  }
}
