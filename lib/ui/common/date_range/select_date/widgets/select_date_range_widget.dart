import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/common/date_range/select_date/view_model/select_date_range_view_model.dart';
import 'package:turing_art/ui/common/date_range/select_date/widgets/month_calendar_widget.dart';

/// 日期范围选择组件，可单独使用
///
/// 可以传入开始时间和结束时间，支持显示日期范围
/// 支持选择和取消选择日期，支持显示两个月的日历
class SelectDateRangeWidget extends StatefulWidget {
  /// 初始开始日期
  final DateTime? _initialStartDate;

  /// 初始结束日期
  final DateTime? _initialEndDate;

  /// 初始聚焦日期
  final DateTime? _initialFocusedDay;

  /// 日期范围选择回调
  final Function(DateTime? startDate, DateTime? endDate) onDateRangeSelected;

  /// 日期范围选择完成回调
  final Function()? onDateRangeCompleted;

  const SelectDateRangeWidget({
    super.key,
    DateTime? initialFocusedDay,
    DateTime? initialStartDate,
    DateTime? initialEndDate,
    required this.onDateRangeSelected,

    /// 日期范围选择完成回调
    this.onDateRangeCompleted,
  })  : _initialFocusedDay = initialFocusedDay,
        _initialStartDate = initialStartDate,
        _initialEndDate = initialEndDate;

  @override
  SelectDateRangeWidgetState createState() => SelectDateRangeWidgetState();
}

class SelectDateRangeWidgetState extends State<SelectDateRangeWidget> {
  late DateRangeViewModel _viewModel;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        _viewModel = DateRangeViewModel(
          initialStartDate: widget._initialStartDate,
          initialEndDate: widget._initialEndDate,
          initialFocusedDay: widget._initialFocusedDay ?? DateTime.now(),
          onDateRangeChanged: (startDate, endDate) {
            widget.onDateRangeSelected(startDate, endDate);
            // 当开始时间和结束时间都有效触发完成回调,完成具体是否隐藏由外部决定
            if (startDate != null && endDate != null) {
              widget.onDateRangeCompleted?.call();
            }
          },
        );
        return _viewModel;
      },
      child: Container(
        width: 672,
        height: 330,
        decoration: BoxDecoration(
          color: const Color(0xFF1B1C1F),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0x0FFFFFFF),
            width: 1,
          ),
          boxShadow: const [
            BoxShadow(
              color: Color(0x40000000),
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // 左侧月份日历
            const MonthCalendarWidget(isLeft: true),

            // 中间分割线
            Container(
              width: 1,
              height: 304,
              margin: const EdgeInsets.symmetric(vertical: 13),
              color: const Color(0x0FFFFFFF),
            ),

            // 右侧月份日历
            const MonthCalendarWidget(isLeft: false),
          ],
        ),
      ),
    );
  }
}
