import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/common/date_range/select_date/view_model/select_date_range_view_model.dart';

/// 月份导航组件，显示年月文本和导航按钮
class DateNavigatorWidget extends StatelessWidget {
  /// 是否是左侧的日历（影响导航按钮的位置）
  final bool isLeft;

  const DateNavigatorWidget({
    super.key,
    required this.isLeft,
  });

  @override
  Widget build(BuildContext context) {
    return Selector<DateRangeViewModel, DateTime>(
      selector: (_, viewModel) =>
          isLeft ? viewModel.focusedDayLeft : viewModel.focusedDayRight,
      builder: (context, focusedDay, _) {
        return SizedBox(
          height: 40,
          width: 330,
          child: Row(
            mainAxisAlignment:
                isLeft ? MainAxisAlignment.start : MainAxisAlignment.end,
            children: isLeft
                ? [
                    // 左侧月份导航（从左到右）
                    const SizedBox(width: 16),
                    // 双箭头按钮（年）
                    GestureDetector(
                      onTap: () =>
                          context.read<DateRangeViewModel>().previousYearLeft(),
                      child: Container(
                        width: 16,
                        height: 16,
                        alignment: Alignment.center,
                        child: Image.asset(
                          'assets/icons/date_select_pre_year.png',
                          width: 16,
                          height: 16,
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    // 单箭头按钮（月）
                    GestureDetector(
                      onTap: () => context
                          .read<DateRangeViewModel>()
                          .previousMonthLeft(),
                      child: Container(
                        width: 16,
                        height: 16,
                        alignment: Alignment.center,
                        child: Image.asset(
                          'assets/icons/date_select_pre_month.png',
                          width: 16,
                          height: 16,
                        ),
                      ),
                    ),
                    // 年月文本
                    const SizedBox(
                      width: 75.5,
                    ),
                    Text(
                      '${focusedDay.year} 年 ${focusedDay.month}月',
                      style: const TextStyle(
                        fontFamily: 'PingFang SC',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        height: 20 / 16,
                        letterSpacing: 0,
                        color: Color(0xA6EBEDF5),
                      ),
                    ),
                  ]
                : [
                    // 右侧月份导航（从右到左）
                    // 年月文本
                    Text(
                      '${focusedDay.year} 年 ${focusedDay.month}月',
                      style: const TextStyle(
                        fontFamily: 'PingFang SC',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        height: 20 / 16,
                        letterSpacing: 0,
                        color: Color(0xA6EBEDF5),
                      ),
                    ),
                    const SizedBox(
                      width: 75.5,
                    ),
                    // 单箭头按钮（月）
                    GestureDetector(
                      onTap: () =>
                          context.read<DateRangeViewModel>().nextMonthRight(),
                      child: Container(
                        width: 16,
                        height: 16,
                        alignment: Alignment.center,
                        child: Image.asset(
                          'assets/icons/date_select_next_month.png',
                          width: 16,
                          height: 16,
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    // 双箭头按钮（年）
                    GestureDetector(
                      onTap: () =>
                          context.read<DateRangeViewModel>().nextYearRight(),
                      child: Container(
                        width: 16,
                        height: 16,
                        alignment: Alignment.center,
                        child: Image.asset(
                          'assets/icons/date_select_next_year.png',
                          width: 16,
                          height: 16,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
          ),
        );
      },
    );
  }
}
