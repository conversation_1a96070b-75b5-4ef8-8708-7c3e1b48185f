import 'package:flutter/material.dart';
import 'package:turing_art/ui/common/date_range/date_range_util.dart';

/// 日期单元格样式
class DateCellStyle {
  final Color textColor;
  final Color? cellBackgroundColor;
  final Color? rowBackgroundColor;
  final BorderRadius? rowBorderRadius;

  DateCellStyle({
    required this.textColor,
    this.cellBackgroundColor,
    this.rowBackgroundColor,
    this.rowBorderRadius,
  });
}

/// 日期范围ViewModel，处理日期范围选择的核心逻辑
class DateRangeViewModel extends ChangeNotifier {
  // 开始日期
  DateTime? _startDate;
  DateTime? get startDate => _startDate;

  // 结束日期
  DateTime? _endDate;
  DateTime? get endDate => _endDate;

  // 悬停日期
  DateTime? _hoveredDay;
  DateTime? get hoveredDay => _hoveredDay;

  // 聚焦日期 - 左侧日历
  late DateTime _focusedDayLeft;
  DateTime get focusedDayLeft => _focusedDayLeft;

  // 聚焦日期 - 右侧日历
  late DateTime _focusedDayRight;
  DateTime get focusedDayRight => _focusedDayRight;

  // 日期变化回调
  Function(DateTime? startDate, DateTime? endDate)? onDateRangeChanged;

  // 构造函数
  DateRangeViewModel({
    DateTime? initialStartDate,
    DateTime? initialEndDate,
    required DateTime initialFocusedDay,
    this.onDateRangeChanged,
  }) {
    _startDate = initialStartDate;
    _endDate = initialEndDate;
    _focusedDayLeft = initialFocusedDay;
    _focusedDayRight = DateTime(
      initialFocusedDay.year,
      initialFocusedDay.month + 1,
      1,
    );
  }

  // 判断两个日期是否为同一天
  bool isSameDay(DateTime? a, DateTime? b) {
    if (a == null || b == null) {
      return false;
    }
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  // 检查日期是否为今天
  bool timeIsToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  // 处理日期点击
  void onDaySelected(DateTime selectedDay) {
    // 标准化日期，只保留年月日
    final normalizedDay =
        DateTime(selectedDay.year, selectedDay.month, selectedDay.day);

    // 如果已经选了两个日期（包括开始和结束日期相同的情况）
    if (_startDate != null && _endDate != null) {
      // 从新开始选择，清除之前的日期范围，并设置新的开始日期
      _startDate = normalizedDay;
      _endDate = null;
      notifyListeners();
      return; // 不触发回调，因为只选择了一个日期
    }

    // 如果只选了开始日期，还没有选结束日期
    else if (_startDate != null && _endDate == null) {
      // 如果选择了早于开始日期的日期，则交换
      if (normalizedDay.isBefore(_startDate!)) {
        _endDate = _startDate;
        _startDate = normalizedDay;
      }
      // 否则，将选择的日期设为结束日期
      else {
        _endDate = normalizedDay;
      }

      // 调用回调函数，因为已经选择了两个日期
      if (onDateRangeChanged != null) {
        onDateRangeChanged!(_startDate, _endDate);
      }
    }

    // 第一次选择（没有开始日期）
    else {
      _startDate = normalizedDay;
      _endDate = null; // 确保结束日期为空
      // 不触发回调，因为只选择了一个日期
    }

    notifyListeners();
  }

  // 处理鼠标悬停
  void onDayHovered(DateTime? day) {
    _hoveredDay = day;
    notifyListeners();
  }

  // 上一年（左侧日历）
  void previousYearLeft() {
    _focusedDayLeft =
        DateTime(_focusedDayLeft.year - 1, _focusedDayLeft.month, 1);
    _focusedDayRight =
        DateTime(_focusedDayRight.year - 1, _focusedDayRight.month, 1);
    notifyListeners();
  }

  // 上一月（左侧日历）
  void previousMonthLeft() {
    _focusedDayLeft =
        DateTime(_focusedDayLeft.year, _focusedDayLeft.month - 1, 1);
    _focusedDayRight =
        DateTime(_focusedDayRight.year, _focusedDayRight.month - 1, 1);
    notifyListeners();
  }

  // 下一年（右侧日历）
  void nextYearRight() {
    _focusedDayLeft =
        DateTime(_focusedDayLeft.year + 1, _focusedDayLeft.month, 1);
    _focusedDayRight =
        DateTime(_focusedDayRight.year + 1, _focusedDayRight.month, 1);
    notifyListeners();
  }

  // 下一月（右侧日历）
  void nextMonthRight() {
    _focusedDayLeft =
        DateTime(_focusedDayLeft.year, _focusedDayLeft.month + 1, 1);
    _focusedDayRight =
        DateTime(_focusedDayRight.year, _focusedDayRight.month + 1, 1);
    notifyListeners();
  }

  // 清除日期范围
  void clearDateRange() {
    _startDate = null;
    _endDate = null;
    if (onDateRangeChanged != null) {
      onDateRangeChanged!(null, null);
    }
    notifyListeners();
  }

  // 格式化日期为文本
  String? formatStartDate() {
    return _startDate != null ? DateRangeUtil.formatDate(_startDate!) : null;
  }

  // 格式化日期为文本
  String? formatEndDate() {
    return _endDate != null ? DateRangeUtil.formatDate(_endDate!) : null;
  }

  // 计算日期单元格样式
  DateCellStyle calculateDateCellStyle(DateTime date,
      {bool isCurrentMonth = false}) {
    // 基础文字颜色：非当月和当月不同
    Color baseTextColor =
        isCurrentMonth ? const Color(0xA6EBEDF5) : const Color(0x66EBEDF5);

    // 判断当前日期的特殊状态
    final bool isToday = timeIsToday(date);
    final bool isStartDate = startDate != null && isSameDay(date, startDate);
    final bool isEndDate = endDate != null && isSameDay(date, endDate);
    final bool isSingleSelected = isStartDate && isEndDate; // 单选情况

    // 判断是否在日期范围内
    bool isInRange = false;
    if (startDate != null && endDate != null) {
      isInRange = date.isAfter(startDate!) && date.isBefore(endDate!);
    }

    final bool isHovered = hoveredDay != null && isSameDay(date, hoveredDay);

    // 设置文字颜色
    Color textColor = baseTextColor;
    // 只有属于当前月时间才计算规则
    if (isCurrentMonth) {
      if (isToday) {
        textColor = const Color(0xFFFFFFFF); // 当日颜色为白色
      }
      if (isStartDate || isEndDate) {
        textColor = isToday ? const Color(0xFFFFFFFF) : const Color(0xFFE1E2E5);
      }
      if (isHovered && !isStartDate && !isEndDate) {
        textColor = const Color(0xFFF72561); // 悬停颜色为粉红色
      }
    }

    // 设置日期容器和背景
    Color? rowBackgroundColor;
    Color? cellBackgroundColor;
    BorderRadius? rowBorderRadius;

    if (isCurrentMonth) {
      // 如果是单选情况背景色为透明，开始日期和结束日期背景色为透明，需要单独控件保持上下和左或者右的间距
      if (isSingleSelected || isStartDate || isEndDate) {
        rowBackgroundColor = Colors.transparent;
        cellBackgroundColor = const Color(0x0DFFFFFF);
      }

      // 如果是范围选择
      if ((isStartDate || isEndDate || isInRange) &&
          startDate != null &&
          endDate != null) {
        // 设置行背景
        if (isInRange) {
          rowBackgroundColor = const Color(0x0DFFFFFF);
        }

        // 设置开始和结束日期的单元格背景
        if (isStartDate || isEndDate) {
          // 设置行的圆角
          if (isStartDate) {
            rowBorderRadius =
                const BorderRadius.horizontal(left: Radius.circular(16));
          } else if (isEndDate) {
            rowBorderRadius =
                const BorderRadius.horizontal(right: Radius.circular(16));
          }
        }
      }
    }

    return DateCellStyle(
      textColor: textColor,
      cellBackgroundColor: cellBackgroundColor, // 24px文字背景的填充
      rowBackgroundColor: rowBackgroundColor, // 32px行高的填充
      rowBorderRadius: rowBorderRadius,
    );
  }

  /// 计算日期网格中的日期信息
  DateGridInfo calculateDateGridInfo(
      DateTime month, int weekIndex, int dayIndex, int daysFromPreviousMonth) {
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final lastMonth = DateTime(month.year, month.month - 1, 1);
    final daysInLastMonth = DateTime(month.year, month.month, 0).day;

    final dayNumber = weekIndex * 7 + dayIndex - daysFromPreviousMonth;
    DateTime date;
    bool isCurrentMonth = true;
    String dayText;

    if (dayNumber < 0) {
      // 上个月的日期
      final lastMonthDay = daysInLastMonth + dayNumber + 1;
      date = DateTime(lastMonth.year, lastMonth.month, lastMonthDay);
      dayText = '$lastMonthDay';
      isCurrentMonth = false;
    } else if (dayNumber >= daysInMonth) {
      // 下个月的日期
      final nextMonthDay = dayNumber - daysInMonth + 1;
      date = DateTime(month.year, month.month + 1, nextMonthDay);
      dayText = '$nextMonthDay';
      isCurrentMonth = false;
    } else {
      // 当前月的日期
      date = DateTime(month.year, month.month, dayNumber + 1);
      dayText = '${dayNumber + 1}';
      isCurrentMonth = true;
    }

    return DateGridInfo(
      date: date,
      dayText: dayText,
      isCurrentMonth: isCurrentMonth,
    );
  }

  /// 计算日期单元格的选中状态
  DateSelectionInfo calculateDateSelectionInfo(
    DateTime date, {
    bool isCurrentMonth = true,
  }) {
    final bool isStartDate = startDate != null && isSameDay(date, startDate);
    final bool isEndDate = endDate != null && isSameDay(date, endDate);
    // 只有当开始日期和结束日期都存在，并且是同一天时，才认为是单选
    final bool isSingleSelected =
        isStartDate && isEndDate && startDate != null && endDate != null;
    final bool isSelected = ((isStartDate || isEndDate) && isCurrentMonth);

    return DateSelectionInfo(
      isStartDate: isStartDate,
      isEndDate: isEndDate,
      isSingleSelected: isSingleSelected,
      isSelected: isSelected,
    );
  }
}

/// 日期网格信息
class DateGridInfo {
  final DateTime date;
  final String dayText;
  final bool isCurrentMonth;

  DateGridInfo({
    required this.date,
    required this.dayText,
    required this.isCurrentMonth,
  });
}

/// 日期选择信息
class DateSelectionInfo {
  final bool isStartDate;
  final bool isEndDate;
  final bool isSingleSelected;
  final bool isSelected;

  DateSelectionInfo({
    required this.isStartDate,
    required this.isEndDate,
    required this.isSingleSelected,
    required this.isSelected,
  });
}
