import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/common/date_range/date_range_util.dart';
import 'package:turing_art/ui/common/date_range/input_date/view_model/input_date_range_view_model.dart';
import 'package:turing_art/ui/common/date_range/select_date/widgets/select_date_range_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 日期范围输入+选择组件
///
/// 提供开始日期和结束日期的输入，支持手动输入和日期选择器选择
class InputDateRangeWidget extends StatefulWidget {
  /// 日期变更回调
  final Function(String? startDate, String? endDate) onDateRangeChanged;

  const InputDateRangeWidget({
    super.key,
    required this.onDateRangeChanged,
  });

  @override
  State<InputDateRangeWidget> createState() => _InputDateRangeWidgetState();
}

class _InputDateRangeWidgetState extends State<InputDateRangeWidget> {
  // 添加一个GlobalKey来获取输入控件的位置
  final GlobalKey _inputContainerKey = GlobalKey();

  // 创建ViewModel
  late InputDateRangeViewModel _viewModel;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        _viewModel = InputDateRangeViewModel(
          onDateRangeChanged: widget.onDateRangeChanged,
        );
        return _viewModel;
      },
      child:
          Consumer<InputDateRangeViewModel>(builder: (context, viewModel, _) {
        return MouseRegion(
          onEnter: (_) => viewModel.isWidgetHovered = true,
          onExit: (_) => viewModel.isWidgetHovered = false,
          child: Container(
            key: _inputContainerKey,
            width: 314,
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: viewModel.isWidgetHovered
                    ? const Color(0x66EBEDF5) // hover状态边框颜色
                    : const Color(0x0FFFFFFF), // 默认边框颜色
                width: 1,
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                // 开始时间输入框
                SizedBox(
                  width: 110,
                  child: Stack(
                    alignment: Alignment.centerRight,
                    children: [
                      TextField(
                        controller: viewModel.startTimeController,
                        focusNode: viewModel.startTimeFocusNode,
                        maxLines: null, // expands设置后，maxLines必须设置为null
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.regular,
                          fontSize: 12,
                          height: 16 / 12,
                          color: const Color(0xFFEBEDF5),
                        ),
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                          border: InputBorder.none,
                          hintText: '开始时间',
                          hintStyle: TextStyle(
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.regular,
                            fontSize: 12,
                            height: 16 / 12,
                            color: const Color(0x66EBEDF5),
                          ),
                        ),
                        cursorColor: const Color(0xFFF72561),
                        textAlign: TextAlign.left,
                        textAlignVertical: TextAlignVertical.center,
                        expands: true, // 允许填充父控件
                        onChanged: (value) {
                          final formatted =
                              viewModel.handleStartTimeInputChange(value);
                          // expands: true后enter键不会回调onSubmitted，所以需要手动触发
                          if (value.endsWith('\n')) {
                            if (viewModel.submitTime(formatted)) {
                              // 收起日期选择器
                              PGDialog.dismiss(
                                  tag: DialogTags.dateRangeSelector);
                              // 让输入框失去焦点
                              viewModel.startTimeFocusNode.unfocus();
                            } else {
                              // 输入无效，弹出toast
                              PGDialog.showToast('参考格式:2025-03-02');
                            }
                          }
                        },
                        onTap: () {
                          _showDateRangeSelector(context);
                        },
                      ),
                    ],
                  ),
                ),

                // 中间连接线
                Container(
                  width: 8,
                  height: 2,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: const Color(0x66EBEDF5),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // 结束时间输入框
                SizedBox(
                  width: 110,
                  child: Stack(
                    alignment: Alignment.centerRight,
                    children: [
                      TextField(
                        controller: viewModel.endTimeController,
                        focusNode: viewModel.endTimeFocusNode,
                        maxLines: null, // expands设置后，maxLines必须设置为null
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.regular,
                          fontSize: 12,
                          height: 16 / 12,
                          color: const Color(0xFFEBEDF5),
                        ),
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                          border: InputBorder.none,
                          hintText: '结束时间',
                          hintStyle: TextStyle(
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.regular,
                            fontSize: 12,
                            height: 16 / 12,
                            color: const Color(0x66EBEDF5),
                          ),
                        ),
                        cursorColor: const Color(0xFFF72561),
                        textAlign: TextAlign.left,
                        textAlignVertical: TextAlignVertical.center,
                        expands: true, // 允许填充父控件
                        onChanged: (value) {
                          final formatted =
                              viewModel.handleEndTimeInputChange(value);
                          // expands: true后enter键不会回调onSubmitted，所以需要手动触发
                          if (value.endsWith('\n')) {
                            if (viewModel.submitTime(formatted)) {
                              // 收起日期选择器
                              PGDialog.dismiss(
                                  tag: DialogTags.dateRangeSelector);
                              // 让输入框失去焦点
                              viewModel.endTimeFocusNode.unfocus();
                            } else {
                              // 输入无效，弹出toast
                              PGDialog.showToast('参考格式:2025-03-02');
                            }
                          }
                        },
                        onTap: () {
                          _showDateRangeSelector(context);
                        },
                      ),
                    ],
                  ),
                ),

                const Spacer(),

                // 日历图标或清除全部按钮
                viewModel.hasFilter && viewModel.isWidgetHovered
                    ? GestureDetector(
                        onTap: viewModel.clearFilter,
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: const Color(0xFFEBEDF5).withOpacity(0.4),
                        ),
                      )
                    : GestureDetector(
                        onTap: () {
                          // 直接显示日期选择器
                          _showDateRangeSelector(context);
                        },
                        child: Image.asset(
                          'assets/icons/date_select.png',
                          width: 16,
                          height: 16,
                        ),
                      ),
              ],
            ),
          ),
        );
      }),
    );
  }

// 显示带日期范围的日期选择器
  Future<void> _showDateRangeSelector(BuildContext context) async {
    // 获取开始和结束日期（如果有效，显示在日历上）
    final DateTime? startDate =
        DateRangeUtil.parseDate(_viewModel.startTimeController.text);
    final DateTime? endDate =
        DateRangeUtil.parseDate(_viewModel.endTimeController.text);

    // 初始日历日期：优先使用开始日期，如果没有则使用结束日期，如果都没有则使用当前日期
    final initialDate = startDate ?? endDate ?? DateTime.now();

    // 获取输入组件的RenderBox
    final RenderBox? renderBox =
        _inputContainerKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      return;
    }

    // 计算弹窗应该显示的位置
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    // 在输入组件下方4px的位置显示，左对齐
    final dialogLeft = position.dx;
    final dialogTop = position.dy + size.height;

    // 使用PGDialog.showPositionedDialog显示日期范围选择器
    PGDialog.showPositionedDialog(
      tag: DialogTags.dateRangeSelector, // 使用标签区分不同弹窗
      width: 672,
      height: 330,
      alignment: Alignment.topLeft, // 使用左上角对齐
      padding: EdgeInsets.only(
        left: dialogLeft,
        top: dialogTop + 4, // 距离输入框底部4px
      ),
      child: SelectDateRangeWidget(
        initialStartDate: startDate,
        initialEndDate: endDate,
        initialFocusedDay: initialDate,
        onDateRangeSelected: (DateTime? newStartDate, DateTime? newEndDate) {
          // 更新日期到文本框
          String? startDateText;
          String? endDateText;

          if (newStartDate != null) {
            startDateText = DateRangeUtil.formatDate(newStartDate);
          }

          if (newEndDate != null) {
            endDateText = DateRangeUtil.formatDate(newEndDate);
          }

          // 通知ViewModel更新日期范围
          _viewModel.updateDateRange(startDateText, endDateText);
        },
        onDateRangeCompleted: () {
          // 收起日期选择器（和设计师确认）
          PGDialog.dismiss(tag: DialogTags.dateRangeSelector);
        },
      ),
    );
  }
}
