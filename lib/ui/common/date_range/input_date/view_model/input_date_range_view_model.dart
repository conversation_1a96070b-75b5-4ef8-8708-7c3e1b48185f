import 'package:flutter/material.dart';
import 'package:turing_art/ui/common/date_range/date_range_util.dart';

/// 日期范围输入ViewModel，处理日期范围输入的核心逻辑
class InputDateRangeViewModel extends ChangeNotifier {
  // 文本编辑控制器
  final TextEditingController startTimeController = TextEditingController();
  final TextEditingController endTimeController = TextEditingController();

  // 焦点控制
  final FocusNode startTimeFocusNode = FocusNode();
  final FocusNode endTimeFocusNode = FocusNode();

  // 整个组件的悬停状态
  bool _isWidgetHovered = false;
  bool get isWidgetHovered => _isWidgetHovered;
  set isWidgetHovered(bool value) {
    _isWidgetHovered = value;
    // 如果悬停状态改变，则更新边框UI和按钮是清除还是日历
    notifyListeners();
  }

  // 日期变更回调
  final Function(String? startDate, String? endDate) onDateRangeChanged;

  // 上一次输入值缓存
  String? _lastEndTimeValue;
  String? _lastStartTimeValue;

  // 构造函数
  InputDateRangeViewModel({required this.onDateRangeChanged});

  // 验证开始时间
  bool validateStartOrEndTime(String text) {
    // 可为空，开始时间为空，表示结束时间以前的范围；结束时间为空，表示开始时间以后的范围
    if (text.isEmpty) {
      return true;
    }

    if (!DateRangeUtil.isValidDateFormat(text)) {
      // '参照格式:2025-03-02';
      return false;
    }
    return true;
  }

  // 清除筛选
  void clearFilter() {
    startTimeController.clear();
    endTimeController.clear();

    // 回调
    onDateRangeChanged(null, null);
  }

  // 处理时间输入变化(返回格式化后的值)
  String handleTimeInputChange(String value, {required bool isStartTime}) {
    final oldValue =
        isStartTime == true ? _lastStartTimeValue : _lastEndTimeValue;

    if (isStartTime) {
      _lastStartTimeValue = value;
    } else {
      _lastEndTimeValue = value;
    }

    // 使用的格式化方法自动添加分隔符，传递oldValue以支持删除操作检测
    final formatted = DateRangeUtil.formatDateInput(value, oldValue: oldValue);

    // 获取要更新的控制器
    final controller = isStartTime ? startTimeController : endTimeController;

    // 更新文本字段，保持光标在末尾
    controller.value = TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );

    return formatted;
  }

  // 处理开始时间输入变化(返回格式化后的值) - 保留以供向后兼容
  String handleStartTimeInputChange(String value) {
    return handleTimeInputChange(value, isStartTime: true);
  }

  // 处理结束时间输入变化(返回格式化后的值) - 保留以供向后兼容
  String handleEndTimeInputChange(String value) {
    return handleTimeInputChange(value, isStartTime: false);
  }

  // 提交时间（返回是否提交成功）
  bool submitTime(String text) {
    if (validateStartOrEndTime(text)) {
      // 值是有效状态，更新并通知外部日期变更
      onDateRangeChanged(
          startTimeController.text.isNotEmpty ? startTimeController.text : null,
          endTimeController.text.isNotEmpty ? endTimeController.text : null);
      return true;
    }
    return false;
  }

  // 日历选择后更新输入日期
  void updateDateRange(String? startDate, String? endDate) {
    if (startDate != null) {
      startTimeController.text = startDate;
    } else {
      startTimeController.clear();
    }

    if (endDate != null) {
      endTimeController.text = endDate;
    } else {
      endTimeController.clear();
    }

    // 通知外部日期变更
    onDateRangeChanged(startDate, endDate);
  }

  // 判断是否有过滤器
  bool get hasFilter =>
      startTimeController.text.isNotEmpty || endTimeController.text.isNotEmpty;

  // 判断是否有焦点
  bool get hasFocus => startTimeFocusNode.hasFocus || endTimeFocusNode.hasFocus;

  // 释放资源
  @override
  void dispose() {
    startTimeController.dispose();
    endTimeController.dispose();
    startTimeFocusNode.dispose();
    endTimeFocusNode.dispose();
    super.dispose();
  }
}
