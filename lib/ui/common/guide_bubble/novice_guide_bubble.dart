import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 新手引导气泡内容组件
class NoviceGuideBubble extends StatelessWidget {
  /// 气泡内容描述
  final String description;

  /// 当前步骤
  final int currentStep;

  /// 总步骤数
  final int totalSteps;

  /// 取消回调
  final VoidCallback? onCancel;

  /// 下一步回调
  final VoidCallback? onNext;

  /// 标题文本
  final String cancelText;

  /// 确认文本
  final String nextText;

  /// 气泡宽度（可选）
  final double? width;

  /// 气泡高度（可选）
  final double? height;

  const NoviceGuideBubble({
    super.key,
    required this.description,
    required this.currentStep,
    required this.totalSteps,
    this.onCancel,
    this.onNext,
    this.cancelText = '取消',
    this.nextText = '下一步',
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 描述文本
            ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: (width != null) ? width! - 32 : double.infinity,
              ),
              child: Text(
                description,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium,
                  height: 20 / 14, // line-height: 20px
                ),
              ),
            ),

            const Spacer(),

            // 底部区域：当前步骤/总步骤 + 按钮
            Row(
              children: [
                // 当前步骤/总步骤
                Padding(
                  padding: const EdgeInsets.only(bottom: 14),
                  child: Text(
                    '$currentStep/$totalSteps',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      height: 16 / 12, // line-height: 16px
                    ),
                  ),
                ),

                const Spacer(),

                // 取消按钮
                if (onCancel != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 8, bottom: 8),
                    child: _buildButton(
                      text: cancelText,
                      onPressed: onCancel,
                      isOutlined: true,
                    ),
                  ),

                // 下一步按钮
                if (onNext != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 8, bottom: 8),
                    child: _buildButton(
                      text: nextText,
                      onPressed: onNext,
                      isOutlined: false,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建按钮
  Widget _buildButton({
    required String text,
    required VoidCallback? onPressed,
    required bool isOutlined,
  }) {
    return SizedBox(
      width: 64,
      height: 28,
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          backgroundColor:
              isOutlined ? Colors.transparent : const Color(0xFFF72561),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
            side: isOutlined
                ? BorderSide(color: Colors.white.withOpacity(0.1), width: 1)
                : BorderSide.none,
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isOutlined ? Colors.white.withOpacity(0.7) : Colors.white,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}
