import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/routing/router.dart';
import 'package:turing_art/ui/common/disk_cache_guard/view_model/disk_cache_guard_view_model.dart';
import 'package:turing_art/ui/setting/provider/current_cache_rule_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 磁盘缓存守卫组件
///
/// 该组件没有任何尺寸，主要用于监听路由变化并执行磁盘缓存相关的操作
/// 实现了 RouteAware 接口，能够感知路由的 push、pop 等操作
class DiskCacheGuardWidget extends StatelessWidget {
  const DiskCacheGuardWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => DiskCacheGuardViewModel(
        context.read<DiskCacheManager>(),
        context.read<SettingRepository>(),
        context.read<ProjectCacheRuleProvider>(),
      ),
      child: const _DiskCacheGuardListener(),
    );
  }
}

/// 内部的路由监听组件
class _DiskCacheGuardListener extends StatefulWidget {
  const _DiskCacheGuardListener();

  @override
  State<_DiskCacheGuardListener> createState() =>
      _DiskCacheGuardListenerState();
}

class _DiskCacheGuardListenerState extends State<_DiskCacheGuardListener>
    with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 订阅路由观察者
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    // 取消订阅路由观察者
    routeObserver.unsubscribe(this);
    PGLog.d('DiskCacheGuardWidget - dispose');
    super.dispose();
  }

  // 当页面被弹出时（当前页面重新变为活跃）
  @override
  void didPopNext() {
    // 通过 context 访问 ViewModel，此时 Provider 已经创建
    if (mounted) {
      final viewModel = context.read<DiskCacheGuardViewModel>();
      viewModel.onPageActive();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DiskCacheGuardViewModel>(
      builder: (context, viewModel, child) {
        // 返回一个没有尺寸的组件
        return const SizedBox.shrink();
      },
    );
  }
}
