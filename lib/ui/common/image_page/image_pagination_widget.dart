import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class ImagePaginationWidget extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;
  final double? width;

  const ImagePaginationWidget({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.onPrevious,
    this.onNext,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final containerWidth = width ?? 94.0;
    final canGoPrevious = currentPage > 1;
    final canGoNext = currentPage < totalPages;

    return Container(
      width: containerWidth,
      height: 32,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: const Color(0xFF383838),
      ),
      padding: const EdgeInsets.only(left: 0, right: 0, top: 6, bottom: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 上一页按钮
          GestureDetector(
            onTap: canGoPrevious ? onPrevious : null,
            child: Container(
              width: 20,
              height: 20,
              color: Colors.transparent,
              padding:
                  const EdgeInsets.only(left: 10, right: 0, top: 0, bottom: 0),
              child: Icon(
                Icons.arrow_back_ios,
                size: 10,
                color: canGoPrevious ? Colors.white : Colors.grey,
              ),
            ),
          ),

          // 页码
          Expanded(
            child: Center(
              child: Text(
                '$currentPage/$totalPages',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium,
                ),
              ),
            ),
          ),

          // 下一页按钮
          GestureDetector(
            onTap: canGoNext ? onNext : null,
            child: Container(
              width: 20,
              height: 20,
              color: Colors.transparent,
              padding:
                  const EdgeInsets.only(left: 0, right: 10, top: 0, bottom: 0),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 10,
                color: canGoNext ? Colors.white : Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
