import 'package:flutter/material.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 帮助按钮组件
class HelpButtonWidget extends StatelessWidget {
  final VoidCallback? showHelpBubble;
  final VoidCallback? hideHelpBubble;

  const HelpButtonWidget({
    super.key,
    this.showHelpBubble,
    this.hideHelpBubble,
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        if (showHelpBubble != null) {
          PGLog.d('showHelpBubble');
          showHelpBubble!();
        }
      },
      onExit: (_) {
        if (hideHelpBubble != null) {
          PGLog.d('hideHelpBubble');
          hideHelpBubble!();
        }
      },
      child: Image.asset(
        'assets/icons/export_qustion.png',
        width: 24,
        height: 24,
      ),
    );
  }
}
