import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 帮助信息list气泡提示组件，用于显示标题和内容项列表
class HelpInfoListBubbleWidget extends StatelessWidget {
  /// 气泡标题
  final String title;

  /// 气泡内容项列表
  final List<String> contentItems;

  const HelpInfoListBubbleWidget({
    super.key,
    required this.title,
    required this.contentItems,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.only(top: 16, left: 12, right: 12, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 12,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
                fontWeight: Fonts.semiBold,
              ),
            ),
            const SizedBox(height: 8),
            ...contentItems.map(_buildTooltipItem),
          ],
        ),
      ),
    );
  }

  Widget _buildTooltipItem(String text) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Text(
        text,
        style: TextStyle(
          fontFamily: Fonts.defaultFontFamily,
          fontSize: 12,
          color: const Color(0xFFEBEDF5).withOpacity(0.65),
          height: 16 / 12,
          fontWeight: Fonts.semiBold,
        ),
      ),
    );
  }
}
