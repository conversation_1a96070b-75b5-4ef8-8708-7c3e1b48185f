import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/arrow_position.dart';
import 'package:turing_art/ui/common/title_bar_with_help_tip/help_button_widget.dart';
import 'package:turing_art/ui/common/title_bar_with_help_tip/helpinfo_list_bubble_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/extensions/widget_extensions.dart';
import 'package:turing_art/utils/pg_bubble/pg_bubble_overlay.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 标题栏组件，包含标题、帮助按钮和关闭按钮
class TitleBarWithHelpBubbleWidget extends StatefulWidget {
  final String title;
  final String tooltipTitle;
  final List<String> tooltipContentItems;
  final String dialogTag;

  const TitleBarWithHelpBubbleWidget({
    super.key,
    required this.title,
    required this.tooltipTitle,
    required this.tooltipContentItems,
    required this.dialogTag,
  });

  @override
  State<TitleBarWithHelpBubbleWidget> createState() =>
      _TitleBarWithHelpBubbleWidgetState();
}

class _TitleBarWithHelpBubbleWidgetState
    extends State<TitleBarWithHelpBubbleWidget> {
  final GlobalKey _helpButtonKey = GlobalKey();

  String get bubbleTag => 'helpBubble_${widget.dialogTag}';

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: Stack(
        children: [
          // 标题居中
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 14,
                    height: 20 / 14,
                    color: const Color(0xFFEBEDF5).withOpacity(0.65),
                  ),
                ),
                SizedBox(
                  key: _helpButtonKey,
                  width: 24,
                  height: 24,
                  child: Center(
                    child: HelpButtonWidget(
                      showHelpBubble: () {
                        if (!PGBubbleOverlay.isVisible(tag: bubbleTag)) {
                          PGBubbleOverlay.show(
                            context: context,
                            targetRect: _helpButtonKey.globalRect ?? Rect.zero,
                            arrowPosition: ArrowPosition.top,
                            arrowOffset: 154,
                            content: HelpInfoListBubbleWidget(
                              title: widget.tooltipTitle,
                              contentItems: widget.tooltipContentItems,
                            ),
                            bubbleWidth: 307,
                            bubbleHeight: 114,
                            backgroundColor: const Color(0xFF1B1C1F),
                            borderColor: const Color(0x0FFFFFFF),
                            tag: bubbleTag,
                          );
                        }
                      },
                      hideHelpBubble: () {
                        PGBubbleOverlay.dismiss(tag: bubbleTag);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 关闭按钮
          Positioned(
            right: 16,
            top: 0,
            bottom: 0,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  PGBubbleOverlay.dismiss(tag: bubbleTag);
                  PGDialog.dismiss(tag: widget.dialogTag);
                },
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
