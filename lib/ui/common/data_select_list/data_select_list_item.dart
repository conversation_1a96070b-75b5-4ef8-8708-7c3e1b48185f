/// 数据选择列表项模型
class DataSelectListItem<T> {
  /// 名称
  final String title;

  final T data;

  /// 是否被选中
  bool isSelected;

  DataSelectListItem({
    required this.title,
    this.isSelected = false,
    required this.data,
  });

  /// 复制并修改选中状态
  DataSelectListItem<T> copyWith({
    required T data,
    bool? isSelected,
  }) {
    return DataSelectListItem<T>(
      title: title,
      data: data,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

/// 数据选择列表配置
class DataSelectListConfig<T> {
  /// 默认文本提示
  final String defaultText;

  /// 选择单位(人/个等)
  final String unit;

  /// 控件宽度
  final double width;

  /// 控件高度
  final double height;

  /// 数据源数组
  final List<DataSelectListItem<T>> dataSource;

  const DataSelectListConfig({
    required this.defaultText,
    required this.unit,
    required this.width,
    required this.height,
    required this.dataSource,
  });
}
