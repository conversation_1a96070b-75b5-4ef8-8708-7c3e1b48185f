import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

import 'data_select_list_item.dart';

class DataSelectListWidget<T> extends StatefulWidget {
  final DataSelectListConfig<T> config;
  final Function(List<DataSelectListItem<T>>)? onSelectionChanged;

  const DataSelectListWidget({
    super.key,
    required this.config,
    this.onSelectionChanged,
  });

  @override
  State<DataSelectListWidget<T>> createState() =>
      _DataSelectListWidgetState<T>();
}

class _DataSelectListWidgetState<T> extends State<DataSelectListWidget<T>> {
  bool _isExpanded = false;
  late List<DataSelectListItem<T>> _dataSource;

  @override
  void initState() {
    super.initState();
    _dataSource = widget.config.dataSource
        .map((item) => item.copyWith(data: item.data as T))
        .toList();
  }

  /// 获取选中的数据数量
  int get _selectedCount => _dataSource.where((item) => item.isSelected).length;

  /// 是否全选
  bool get _isAllSelected =>
      _dataSource.isNotEmpty && _selectedCount == _dataSource.length;

  /// 获取显示文本
  String get _displayText {
    if (_isExpanded) {
      return '${widget.config.defaultText}筛选';
    } else if (_selectedCount > 0) {
      return '已选择$_selectedCount${widget.config.unit}';
    } else {
      return widget.config.defaultText;
    }
  }

  /// 切换展开状态
  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  /// 确认选择
  void _confirmSelection() {
    setState(() {
      _isExpanded = false;
    });
    widget.onSelectionChanged?.call(_dataSource);
  }

  /// 切换全选状态
  void _toggleSelectAll() {
    setState(() {
      bool shouldSelectAll = !_isAllSelected;
      for (var item in _dataSource) {
        item.isSelected = shouldSelectAll;
      }
    });
  }

  /// 切换单个项目选中状态
  void _toggleItemSelection(int index) {
    setState(() {
      _dataSource[index].isSelected = !_dataSource[index].isSelected;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.config.width,
      decoration: BoxDecoration(
        color: const Color(0xFF1B1C1F),
        border: Border.all(
          color: Colors.white.withOpacity(0.06),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: _isExpanded
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          if (_isExpanded) ...[
            _buildSelectAllSection(),
            _buildDataList(),
          ],
        ],
      ),
    );
  }

  /// 构建头部区域
  Widget _buildHeader() {
    return GestureDetector(
      onTap: _isExpanded ? null : _toggleExpanded,
      child: Container(
        height: widget.config.height,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            Expanded(
              child: Text(
                _displayText,
                style: TextStyle(
                  color: const Color(0xFFEBEDF5).withOpacity(0.65),
                  fontSize: 14,
                  height: 18 / 14,
                  fontWeight: FontWeight.w400,
                  fontFamily: Fonts.defaultFontFamily,
                ),
              ),
            ),
            const SizedBox(width: 6),
            if (_isExpanded)
              GestureDetector(
                onTap: _confirmSelection,
                child: Text(
                  '确认',
                  style: TextStyle(
                    color: _selectedCount > 0
                        ? const Color(0xFFF72561)
                        : const Color(0xFFEBEDF5).withOpacity(0.4),
                    fontSize: 14,
                    height: 18 / 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                ),
              )
            else
              SizedBox(
                width: 16,
                height: 16,
                child: Image.asset(
                  'assets/icons/icon_list_down.png',
                  width: 16,
                  height: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建全选区域
  Widget _buildSelectAllSection() {
    return SizedBox(
      height: 40,
      width: widget.config.width,
      child: Column(
        children: [
          // 分割线
          Container(
            height: 1,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            color: Colors.white.withOpacity(0.06),
          ),
          // 全选内容
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: _toggleSelectAll,
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: Image.asset(
                        _getSelectAllIcon(),
                        width: 16,
                        height: 16,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _isAllSelected ? '已全选' : '未全选',
                    style: TextStyle(
                      color: _isAllSelected
                          ? const Color(0xFFE1E2E5)
                          : const Color(0xFFEBEDF5).withOpacity(0.65),
                      fontSize: 12,
                      height: 16 / 12,
                      fontWeight: FontWeight.w400,
                      fontFamily: Fonts.defaultFontFamily,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取全选按钮图标
  String _getSelectAllIcon() {
    if (_selectedCount == 0) {
      return 'assets/icons/list_unselect.png';
    } else if (_isAllSelected) {
      return 'assets/icons/list_select.png';
    } else {
      return 'assets/icons/list_select_part.png';
    }
  }

  /// 构建数据列表
  Widget _buildDataList() {
    return ConstrainedBox(
      constraints: const BoxConstraints(
        minHeight: 0,
        maxHeight: 240,
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _dataSource.length,
        itemBuilder: (context, index) {
          final item = _dataSource[index];
          return _buildDataItem(item, index);
        },
      ),
    );
  }

  /// 构建单个数据项
  Widget _buildDataItem(DataSelectListItem item, int index) {
    return GestureDetector(
      onTap: () => _toggleItemSelection(index),
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: Image.asset(
                item.isSelected
                    ? 'assets/icons/list_select.png'
                    : 'assets/icons/list_unselect.png',
                width: 16,
                height: 16,
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                item.title,
                style: TextStyle(
                  color: item.isSelected
                      ? const Color(0xFFE1E2E5)
                      : const Color(0xFFEBEDF5).withOpacity(0.65),
                  fontSize: 14,
                  height: 18 / 14,
                  fontWeight: FontWeight.w400,
                  fontFamily: Fonts.defaultFontFamily,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
