import 'package:turing_art/core/tapj/history_file_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_chain.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handlers.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';

/// 创建工程normal完整说明：
/// 1. 检查磁盘空间：
/// 2. 创建工程：
/// 3. 上报工程信息：
/// 4. unity 初始化检查（外部）：
/// 5. 设置工作区：
/// 6. 导入图片：
/// 7. 切换工作区：
/// 8. 跳转编辑页面（UI层调用）：
/// 项目处理链工厂
class ProjectCreateSelectHandlerFactory {
  ProjectCreateSelectHandlerFactory({
    required ProjectUseCaseProvider projectUseCase,
    required UnityUseCaseProvider unityUseCase,
    required WorkspaceUseCaseProvider workspaceUseCase,
    required ProjectStateProvider projectStateProvider,
    required UnityController unityController,
    required CurrentUserRepository currentUserRepository,
    required MediaRepository mediaRepository,
    required ProjectRepository projectRepository,
  })  : _projectUseCase = projectUseCase,
        _unityUseCase = unityUseCase,
        _workspaceUseCase = workspaceUseCase,
        _projectStateProvider = projectStateProvider,
        _unityController = unityController,
        _currentUserRepository = currentUserRepository,
        _projectRepository = projectRepository;

  final ProjectUseCaseProvider _projectUseCase;
  final UnityUseCaseProvider _unityUseCase;
  final WorkspaceUseCaseProvider _workspaceUseCase;
  final ProjectStateProvider _projectStateProvider;
  final UnityController _unityController;
  final CurrentUserRepository _currentUserRepository;
  final ProjectRepository _projectRepository;

  /// 创建精修项目处理链
  /// 1. 检查磁盘空间
  /// 2. 创建工程
  /// 3. 上报工程信息
  ProjectHandlerChain createRetouchedProjectChain() {
    final chain = ProjectHandlerChain();

    // 添加处理器
    chain
        // 1. 检查磁盘空间
        .addHandler(DiskSpaceCheckHandler(_workspaceUseCase))

        // 2. 创建工程
        .addHandler(CreateProjectHandler(_projectUseCase))

        // 3. 上报工程信息
        .addHandler(ReportProjectInfoHandler(_currentUserRepository));

    return chain;
  }

  /// 创建只创建工程处理链(用于demo)
  /// 1. 创建工程
  ProjectHandlerChain createOnlyCreateProjectChain() {
    final chain = ProjectHandlerChain();

    // 添加处理器
    chain
        // 1. 创建工程
        .addHandler(CreateProjectHandler(_projectUseCase));

    return chain;
  }

  /// 创建工程和存储文件处理链(用于AIGC工程)
  /// 1. 创建工程
  /// 2. 存储文件
  ProjectHandlerChain createCreateAigcProjectChain() {
    final chain = ProjectHandlerChain();

    // 添加处理器
    chain
        // 1. 检查磁盘空间
        .addHandler(DiskSpaceCheckHandler(_workspaceUseCase))
        // 2. 创建工程
        .addHandler(CreateAigcProjectHandler(_projectUseCase))
        // 2. 存储上报信息
        .addHandler(ReportProjectInfoHandler(_currentUserRepository));

    return chain;
  }

  /// 创建Unity初始化后到切换工作区的链，最后一步nav跳转在UI层实现
  /// 这个链在Unity初始化完成后使用，从设置工作区开始
  ProjectHandlerChain createSetWorkspaceAndImportImagesChain() {
    final chain = ProjectHandlerChain();

    // 创建历史文件管理器
    final historyFileManager = HistoryFileManager(
      currentUserRepository: _currentUserRepository,
      projectRepository: _projectRepository,
    );

    // 添加Unity初始化后的处理器
    chain
        // 5. 设置工作区
        .addHandler(SetupWorkspaceHandler(_unityController, _unityUseCase))

        // 6. 导入图片
        .addHandler(ImportImagesHandler(
            _unityController, _unityUseCase, _projectRepository));

    return chain;
  }

  /// 创建选择项目后切换工作区处理链
  /// 用于处理选择已有项目的流程
  ProjectHandlerChain createSelectProjectSwitchWorkspaceChain() {
    final chain = ProjectHandlerChain();

    // 添加处理器
    chain
        // 7. 切换工作区
        .addHandler(SwitchWorkspaceHandler(
      _projectStateProvider,
      _unityController,
      _unityUseCase,
    ));

    return chain;
  }

  /// 创建Unity初始化后到切换工作区完成的链（最后一步nav跳转在UI层实现）
  ProjectHandlerChain createAfterInitUnityToSwitchWorkspaceChain() {
    final chain = ProjectHandlerChain();

    // 创建历史文件管理器
    final historyFileManager = HistoryFileManager(
      currentUserRepository: _currentUserRepository,
      projectRepository: _projectRepository,
    );

    // 添加Unity初始化后的处理器
    chain
        // 5. 设置工作区
        .addHandler(SetupWorkspaceHandler(_unityController, _unityUseCase))

        // 6. 导入图片
        .addHandler(ImportImagesHandler(
            _unityController, _unityUseCase, _projectRepository))

        // 7. 写入历史文件
        .addHandler(WriteHistoryFilesHandler(historyFileManager))

        // 9. 切换工作区
        .addHandler(SwitchWorkspaceHandler(
          _projectStateProvider,
          _unityController,
          _unityUseCase,
        ))
        // 8. 导入历史预设
        .addHandler(
            ImportPresetFromHistoryHandler(_unityController, _unityUseCase));

    return chain;
  }
}
