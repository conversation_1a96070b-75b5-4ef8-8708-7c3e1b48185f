import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/widgets/overlay/drag_forbidden_cursor_overlay.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 鼠标事件处理Widget
/// 负责处理鼠标进入、悬停、离开等事件，并管理鼠标状态
class MouseEventHandlerWidget extends StatefulWidget {
  const MouseEventHandlerWidget({
    super.key,
    required this.child,
    this.onMousePositionChanged,
    this.onMouseWindowStateChanged,
    this.canProcessFiles,
    this.showDragForbiddenOverlay = true,
    this.isProcessingFiles,
  });

  final Widget child;
  final Function(Offset position)? onMousePositionChanged;
  final Function({required bool isInWindow})? onMouseWindowStateChanged;
  final bool Function()? canProcessFiles;
  final bool showDragForbiddenOverlay;
  final bool Function()? isProcessingFiles;

  @override
  State<MouseEventHandlerWidget> createState() =>
      _MouseEventHandlerWidgetState();
}

class _MouseEventHandlerWidgetState extends State<MouseEventHandlerWidget> {
  // 鼠标状态
  Offset _mousePosition = Offset.zero;
  bool _isMouseInWindow = false;

  // 拖拽覆盖层显示状态
  bool _isDragOverlayVisible = false;
  bool _shouldShowOverlay = false;

  @override
  void dispose() {
    _hideDragForbiddenOverlay();
    super.dispose();
  }

  void _handleMousePositionChanged(Offset position) {
    if (mounted) {
      _mousePosition = position;

      // 处理拖拽禁止图标显示逻辑
      if (widget.showDragForbiddenOverlay && widget.canProcessFiles != null) {
        // 如果正在处理文件，这时候调用canProcessFiles，因为有loading，就会显示出禁用图标，所以不准确，需要添加isProcessingFiles一起判断是否显示禁用图标
        final isProcessingFiles = widget.isProcessingFiles?.call() ?? false;
        final canProcessFiles = widget.canProcessFiles!();
        _shouldShowOverlay = isProcessingFiles ? false : !canProcessFiles;

        if (_shouldShowOverlay && _isMouseInWindow) {
          if (!_isDragOverlayVisible) {
            _isDragOverlayVisible = true;
            DragForbiddenCursorOverlay.show();
          }
          DragForbiddenCursorOverlay.updatePosition(position);
        } else if (_isDragOverlayVisible) {
          _hideDragForbiddenOverlay();
        }
      }

      // 调用外部回调
      widget.onMousePositionChanged?.call(position);
    }
  }

  void _handleMouseWindowStateChanged(bool isInWindow) {
    if (mounted) {
      _isMouseInWindow = isInWindow;

      // 处理拖拽禁止图标显示逻辑
      if (widget.showDragForbiddenOverlay) {
        DragForbiddenCursorOverlay.setMouseInWindow(isInWindow: isInWindow);

        if (!isInWindow) {
          PGLog.d('鼠标离开窗口');
          _hideDragForbiddenOverlay();
        } else {
          PGLog.d('鼠标进入窗口');
          if (widget.canProcessFiles != null) {
            // 如果正在处理文件，不显示禁用图标
            final isProcessingFiles = widget.isProcessingFiles?.call() ?? false;
            final canProcessFiles = widget.canProcessFiles!();
            _shouldShowOverlay = isProcessingFiles ? false : !canProcessFiles;

            if (_shouldShowOverlay) {
              if (!_isDragOverlayVisible) {
                _isDragOverlayVisible = true;
                DragForbiddenCursorOverlay.show();
              }
              // 更新位置到当前鼠标位置
              DragForbiddenCursorOverlay.updatePosition(_mousePosition);
            }
          }
        }
      }

      // 调用外部回调
      widget.onMouseWindowStateChanged?.call(isInWindow: isInWindow);
    }
  }

  /// 隐藏拖拽禁止图标
  void _hideDragForbiddenOverlay() {
    if (_isDragOverlayVisible) {
      _isDragOverlayVisible = false;
      DragForbiddenCursorOverlay.hide();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      onEnter: (event) {
        if (mounted) {
          _handleMousePositionChanged(event.position);
          _handleMouseWindowStateChanged(true);
        }
      },
      onHover: (isHovering) {
        if (mounted && isHovering) {
          _handleMouseWindowStateChanged(true);
        }
      },
      onExit: (event) {
        if (mounted) {
          _handleMouseWindowStateChanged(false);
        }
      },
      child: widget.child,
    );
  }
}
