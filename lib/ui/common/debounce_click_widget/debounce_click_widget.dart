import 'package:flutter/material.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 防重复点击的 GestureDetector 包装器
///
/// 使用方法：
/// ```dart
/// DebounceClickWidget(
///   onTap: () => doSomething(),
///   coolDownMs: 500, // 可选，默认500ms
///   child: YourWidget(),
/// )
/// ```
class DebounceClickWidget extends StatefulWidget {
  final VoidCallback? onTap;
  final Widget child;
  final int coolDownMs;
  final HitTestBehavior behavior;
  final GestureTapDownCallback? onTapDown;
  final GestureTapUpCallback? onTapUp;
  final GestureTapCancelCallback? onTapCancel;
  final GestureTapCallback? onSecondaryTap;
  final GestureTapDownCallback? onSecondaryTapDown;
  final String? debugLabel; // 用于调试日志

  const DebounceClickWidget({
    super.key,
    required this.child,
    this.onTap,
    this.coolDownMs = 500,
    this.behavior = HitTestBehavior.opaque,
    this.onTapDown,
    this.onTapUp,
    this.onTapCancel,
    this.onSecondaryTap,
    this.onSecondaryTapDown,
    this.debugLabel,
  });

  @override
  State<DebounceClickWidget> createState() => _DebounceClickWidgetState();
}

class _DebounceClickWidgetState extends State<DebounceClickWidget> {
  DateTime? _lastClickTime;

  bool _canClick() {
    final now = DateTime.now();
    if (_lastClickTime != null) {
      final difference = now.difference(_lastClickTime!);
      if (difference.inMilliseconds < widget.coolDownMs) {
        PGLog.d('${widget.debugLabel}: 点击过于频繁，忽略本次点击');
        return false;
      }
    }
    _lastClickTime = now;
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        PGLog.d(
            '识别为单击，curenttimepoint: ${DateTime.now().millisecondsSinceEpoch}');
        if (_canClick()) {
          widget.onTap?.call();
        }
      },
      onDoubleTap: () {
        PGLog.d(
            '识别为双击，curenttimepoint: ${DateTime.now().millisecondsSinceEpoch}');
        if (_canClick()) {
          widget.onTap?.call();
        }
      },
      onTapDown: widget.onTapDown,
      onTapUp: widget.onTapUp,
      onTapCancel: widget.onTapCancel,
      onSecondaryTap: widget.onSecondaryTap,
      onSecondaryTapDown: widget.onSecondaryTapDown,
      behavior: widget.behavior,
      child: widget.child,
    );
  }
}

/// 简化版本的防重复点击按钮
/// 专门用于简单的点击场景
class DebounceButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final int coolDownMs;
  final String? debugLabel;

  const DebounceButton({
    super.key,
    required this.child,
    this.onPressed,
    this.coolDownMs = 500,
    this.debugLabel,
  });

  @override
  Widget build(BuildContext context) {
    return DebounceClickWidget(
      onTap: onPressed,
      coolDownMs: coolDownMs,
      debugLabel: debugLabel,
      child: child,
    );
  }
}
