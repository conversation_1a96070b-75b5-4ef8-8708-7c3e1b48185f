import 'package:flutter/material.dart';

/// 通用分页管理ViewModel
class PaginationViewModel extends ChangeNotifier {
  /// 当前页码
  int _currentPage = 1;
  int get currentPage => _currentPage;

  /// 总页数
  int _totalPage;
  int get totalPage => _totalPage;

  /// 页码变更回调
  final Future<void> Function(int page)? _onPageChanged;

  PaginationViewModel({
    int initialPage = 1,
    int totalPage = 1,
    Future<void> Function(int page)? onPageChanged,
  })  : _currentPage = initialPage,
        _totalPage = totalPage,
        _onPageChanged = onPageChanged;

  /// 设置总页数
  void setTotalPage(int totalPage) {
    if (totalPage < 0 || totalPage == _totalPage) {
      return;
    }
    _totalPage = totalPage;
    // 总页数变更，一定重置当前页为1，避免超出总页数
    _currentPage = 1;
    notifyListeners();
  }

  /// 设置当前页
  void setCurrentPage(int page) {
    if (page < 1 || page > _totalPage || page == _currentPage) {
      return;
    }
    _currentPage = page;
    notifyListeners();
  }

  /// 跳转到指定页
  Future<void> goToPage(int page) async {
    if (page < 1 || page > _totalPage || page == _currentPage) {
      return;
    }
    _currentPage = page;
    notifyListeners();

    await _onPageChanged?.call(_currentPage);
  }

  /// 上一页
  Future<void> previousPage() async {
    if (_currentPage > 1) {
      await goToPage(_currentPage - 1);
    }
  }

  /// 下一页
  Future<void> nextPage() async {
    if (_currentPage < _totalPage) {
      await goToPage(_currentPage + 1);
    }
  }

  /// 获取显示的页码列表
  List<int> getPageNumbers() {
    final List<int> pageNumbers = [];

    // 总页数小于等于9，显示所有页码
    if (_totalPage <= 9) {
      for (int i = 1; i <= _totalPage; i++) {
        pageNumbers.add(i);
      }
      return pageNumbers;
    }

    // 当前页在前4页
    if (_currentPage <= 4) {
      // 显示1-7页，然后是省略号，然后是最后一页
      for (int i = 1; i <= 7; i++) {
        pageNumbers.add(i);
      }
      pageNumbers.add(-1); // 用-1表示省略号
      pageNumbers.add(_totalPage);
      return pageNumbers;
    }

    // 当前页在后4页
    if (_currentPage > _totalPage - 4) {
      // 显示第一页，然后是省略号，然后是最后7页
      pageNumbers.add(1);
      pageNumbers.add(-1); // 用-1表示省略号
      for (int i = _totalPage - 6; i <= _totalPage; i++) {
        pageNumbers.add(i);
      }
      return pageNumbers;
    }

    // 当前页在中间
    // 显示第一页，然后是省略号，然后是当前页前后各2页，然后是省略号，然后是最后一页
    pageNumbers.add(1);
    pageNumbers.add(-1); // 用-1表示省略号
    for (int i = _currentPage - 2; i <= _currentPage + 2; i++) {
      pageNumbers.add(i);
    }
    pageNumbers.add(-1); // 用-1表示省略号
    pageNumbers.add(_totalPage);
    return pageNumbers;
  }
}
