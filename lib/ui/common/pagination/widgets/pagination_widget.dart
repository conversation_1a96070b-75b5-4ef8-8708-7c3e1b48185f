import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/common/pagination/view_model/pagination_view_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 分页组件全局Key，可以用于获取PaginationWidget的State
class PaginationWidgetKey extends GlobalObjectKey<_PaginationWidgetState> {
  const PaginationWidgetKey(super.value);
}

class PaginationWidget extends StatefulWidget {
  /// 初始页码
  final int _initialPage;

  /// 总页数
  final int _initialTotalPage;

  /// 页码变更回调
  final Future<void> Function(int page) _onPageChanged;

  const PaginationWidget({
    super.key,
    int initialPage = 1,
    int initialTotalPage = 1,
    required Future<void> Function(int page) onPageChanged,
  })  : _initialPage = initialPage,
        _initialTotalPage = initialTotalPage,
        _onPageChanged = onPageChanged;

  @override
  State<PaginationWidget> createState() => _PaginationWidgetState();
}

class _PaginationWidgetState extends State<PaginationWidget> {
  // 保存一个引用，用于方法调用
  late PaginationViewModel _paginationViewModel;

  // 当页面重建时，PaginationWidget 接收到新的 initialTotalPage，但是 StatefulWidget 的 State 对象已经存在，不会重新创建 PaginationViewModel
  @override
  void didUpdateWidget(PaginationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当widget更新且totalPage发生变化时，更新PaginationViewModel的totalPage
    if (oldWidget._initialTotalPage != widget._initialTotalPage) {
      _paginationViewModel.setTotalPage(widget._initialTotalPage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        final viewModel = PaginationViewModel(
          initialPage: widget._initialPage,
          totalPage: widget._initialTotalPage,
          onPageChanged: widget._onPageChanged,
        );
        // 保存引用
        _paginationViewModel = viewModel;
        return viewModel;
      },
      child: Consumer<PaginationViewModel>(
        builder: (context, viewModel, child) {
          final pageNumbers = viewModel.getPageNumbers();

          if (pageNumbers.isEmpty) {
            return const SizedBox.shrink();
          }

          return Container(
            width: 1060,
            height: 60,
            decoration: const BoxDecoration(
              color: Color(0xFF1B1C1F),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 上一页按钮
                _buildPageButton(
                  context,
                  icon: 'assets/icons/page_pre.png',
                  onTap: viewModel.previousPage,
                  isEnabled: viewModel.currentPage > 1,
                ),

                // 页码按钮
                ...pageNumbers.asMap().entries.map((entry) {
                  final int i = entry.key;
                  final int pageNumber = entry.value;

                  if (pageNumber == -1) {
                    // 省略号
                    return _buildPageButton(
                      context,
                      icon: 'assets/icons/page_more.png',
                      onTap: () {
                        // 获取当前省略号前后的页码
                        final prevPage = i > 0 ? pageNumbers[i - 1] : 1;
                        final nextPage = i < pageNumbers.length - 1
                            ? pageNumbers[i + 1]
                            : viewModel.totalPage;

                        // 计算中间页码 (前一位+后一位)/2 并向上取整
                        final middlePage = ((prevPage + nextPage) / 2).ceil();

                        // 跳转到计算出的页码
                        viewModel.goToPage(middlePage);
                      },
                      isEnabled: true,
                    );
                  } else {
                    // 页码
                    return _buildPageNumberButton(
                      context,
                      pageNumber: pageNumber,
                      isSelected: pageNumber == viewModel.currentPage,
                      onTap: () => viewModel.goToPage(pageNumber),
                    );
                  }
                }).take(9), // 限制显示的页码数量，避免超出宽度

                // 下一页按钮
                _buildPageButton(
                  context,
                  icon: 'assets/icons/page_next.png',
                  onTap: viewModel.nextPage,
                  isEnabled: viewModel.currentPage < viewModel.totalPage,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // 构建页码按钮
  Widget _buildPageNumberButton(
    BuildContext context, {
    required int pageNumber,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32,
        height: 32,
        margin: const EdgeInsets.only(right: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: isSelected ? Colors.white : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Colors.white
                : const Color(0xFFEBEDF5).withOpacity(0.4),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            '$pageNumber',
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.medium,
              fontSize: 12,
              color: isSelected
                  ? const Color(0xFF0C0C0D)
                  : const Color(0xFFE1E2E5),
            ),
          ),
        ),
      ),
    );
  }

  // 构建图标按钮（上一页、下一页、省略号）
  Widget _buildPageButton(
    BuildContext context, {
    required String icon,
    required VoidCallback onTap,
    required bool isEnabled,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        width: 32,
        height: 32,
        margin: const EdgeInsets.only(right: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: const Color(0xFFEBEDF5).withOpacity(0.4),
            width: 1,
          ),
        ),
        child: Center(
          child: Image.asset(
            icon,
            width: 16,
            height: 16,
            color: isEnabled ? null : const Color(0xFFEBEDF5).withOpacity(0.4),
          ),
        ),
      ),
    );
  }

  /// 外部逻辑改变页码，更新当前页码
  void updateCurrentPage(int page) {
    _paginationViewModel.setCurrentPage(page);
  }
}
