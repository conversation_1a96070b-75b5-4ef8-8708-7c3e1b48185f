import 'package:desktop_drop/desktop_drop.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/ui/common/drag_event_handler/view_model/drag_event_view_model.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 拖拽事件处理Widget
/// 负责处理文件拖拽进入、更新、离开、完成等事件
/// 使用Provider模式管理状态，返回DealImageFilesResult而不是XFile
class DragEventHandlerWidget extends StatefulWidget {
  const DragEventHandlerWidget({
    super.key,
    required this.child,
    this.onDragEntered,
    this.onDragUpdated,
    this.onDragExited,
    this.onDragDone,
    this.canProcessFiles,
    this.showDragForbiddenOverlay = true,
    this.isSupportMultiProject = true,
    this.onProcessingStateChanged,
  });

  final Widget child;
  final Function(Offset position)? onDragEntered;
  final Function(Offset position)? onDragUpdated;
  final VoidCallback? onDragExited;
  final Function(List<DealImageFilesResult> result)? onDragDone;
  final bool Function()? canProcessFiles;
  final bool showDragForbiddenOverlay;
  final bool isSupportMultiProject;
  final Function({required bool isProcessing})? onProcessingStateChanged;

  @override
  State<DragEventHandlerWidget> createState() => _DragEventHandlerWidgetState();
}

class _DragEventHandlerWidgetState extends State<DragEventHandlerWidget> {
  bool _lastEnabled = true;

  @override
  Widget build(BuildContext context) {
    return Consumer<DragEventHandlerViewModel>(
      builder: (context, viewModel, child) {
        // 记录 enable 状态变化
        if (_lastEnabled != viewModel.isEnabled) {
          PGLog.d(
              'DragEventHandlerWidget - enable 状态变化: $_lastEnabled -> ${viewModel.isEnabled}');
          _lastEnabled = viewModel.isEnabled;
        }

        PGLog.d(
            'DragEventHandlerWidget - 构建， viewModel.isEnabled: ${viewModel.isEnabled}');
        return DropTarget(
          enable: viewModel.isEnabled,
          onDragEntered: (details) {
            PGLog.d(
                'DragEventHandlerWidget - 拖拽进入事件，enable: ${viewModel.isEnabled}');
            if (mounted) {
              // 调用ViewModel处理拖拽进入
              viewModel.onDragEntered(
                  details.localPosition, widget.canProcessFiles);

              // 调用外部回调
              widget.onDragEntered?.call(details.localPosition);
            }
          },
          onDragUpdated: (details) {
            if (mounted) {
              // 调用ViewModel处理拖拽更新
              viewModel.onDragUpdated(
                  details.localPosition, widget.canProcessFiles);

              // 调用外部回调
              widget.onDragUpdated?.call(details.localPosition);
            }
          },
          onDragExited: (details) {
            PGLog.d(
                'DragEventHandlerWidget - 拖拽离开事件，enable: ${viewModel.isEnabled}');
            if (mounted) {
              // 调用ViewModel处理拖拽离开
              viewModel.onDragExited();

              // 调用外部回调
              widget.onDragExited?.call();
            }
          },
          onDragDone: (details) async {
            // shouldShowOverlay是否显示禁用图标，不显示禁用图标的时候才处理拖拽完成事件，以免没有必须要的处理和loading
            if (mounted && !viewModel.shouldShowOverlay) {
              viewModel.setIsProcessFilesTag(isProcessFiles: true);
              // 通知上层开始处理文件
              widget.onProcessingStateChanged?.call(isProcessing: true);

              // 调用ViewModel处理拖拽完成，获取DealImageFilesResult
              final startTime = DateTime.now().millisecondsSinceEpoch;
              PGLog.d(
                  'DragEventHandlerWidget - 开始处理拖拽完成事件，time: $startTime, 共${details.files.length}个文件');
              PGDialog.showLoading();
              final result = await viewModel.onDragDone(context, details.files,
                  isSupportMultiProject: widget.isSupportMultiProject);
              await PGDialog.dismiss();

              PGLog.d(
                  'DragEventHandlerWidget - 处理拖拽完成事件完成，time: ${DateTime.now().millisecondsSinceEpoch},耗时: ${DateTime.now().millisecondsSinceEpoch - startTime}ms');
              // 如果有结果，调用外部回调
              if (result.isNotEmpty) {
                widget.onDragDone?.call(result);
              }

              viewModel.setIsProcessFilesTag(isProcessFiles: false);
              // 通知上层完成处理文件
              widget.onProcessingStateChanged?.call(isProcessing: false);
            }
          },
          child: widget.child,
        );
      },
    );
  }
}
