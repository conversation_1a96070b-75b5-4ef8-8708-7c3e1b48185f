import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/routing/router.dart';
import 'package:turing_art/ui/common/drag_event_handler/view_model/drag_event_view_model.dart';
import 'package:turing_art/ui/common/drag_event_handler/widget/drag_event_handler_widget.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 路由感知的拖拽处理组件
/// 内部包含路由感知逻辑，自动管理拖拽状态
/// 可以独立使用，不需要额外的路由感知代码
class RouteAwareDragHandlerWidget extends StatefulWidget {
  const RouteAwareDragHandlerWidget({
    super.key,
    required this.child,
    this.onDragEntered,
    this.onDragUpdated,
    this.onDragExited,
    this.onDragDone,
    this.canProcessFiles,
    this.showDragForbiddenOverlay = true,
    this.isSupportMultiProject = true,
    this.onProcessingStateChanged,
  });

  final Widget child;
  final Function(Offset position)? onDragEntered;
  final Function(Offset position)? onDragUpdated;
  final VoidCallback? onDragExited;
  final Function(List<DealImageFilesResult> result)? onDragDone;
  final bool Function()? canProcessFiles;
  final bool showDragForbiddenOverlay;
  // 是否支持多项目拖拽(默认支持,ai编辑页面不支持)
  final bool isSupportMultiProject;
  // 处理文件状态改变回调
  final Function({required bool isProcessing})? onProcessingStateChanged;

  @override
  State<RouteAwareDragHandlerWidget> createState() =>
      _RouteAwareDragHandlerWidgetState();
}

class _RouteAwareDragHandlerWidgetState
    extends State<RouteAwareDragHandlerWidget> with RouteAware {
  // 当前页面是否处于活跃状态
  bool _isCurrentRoute = true;
  String? _currentRouteName;

  // 创建 ViewModel 实例，避免在路由回调中访问 Provider
  late final DragEventHandlerViewModel _dragViewModel;

  @override
  void initState() {
    super.initState();
    // 在 initState 中创建 ViewModel，确保在路由回调中可以直接访问
    _dragViewModel = DragEventHandlerViewModel();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 获取当前路由名称
    final route = ModalRoute.of(context);
    if (route != null) {
      _currentRouteName = route.settings.name;
      PGLog.d('${widget.runtimeType} - 初始化路由感知，当前路由: $_currentRouteName');
    }

    // 订阅路由观察者
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    // 取消订阅路由观察者
    routeObserver.unsubscribe(this);
    // 释放 ViewModel 资源
    _dragViewModel.dispose();
    super.dispose();
  }

  // 当页面被推入时（当前页面变为非活跃）
  @override
  void didPushNext() {
    _isCurrentRoute = false;
    _updateDragState();
    PGLog.d(
        '${widget.runtimeType} - didPushNext: 页面变为非活跃状态，路由: $_currentRouteName');
  }

  // 当页面被弹出时（当前页面重新变为活跃）
  @override
  void didPopNext() {
    _isCurrentRoute = true;
    _updateDragState();
    PGLog.d(
        '${widget.runtimeType} - didPopNext: 页面重新变为活跃状态，路由: $_currentRouteName');
  }

  // 当页面被弹出时
  @override
  void didPop() {
    _isCurrentRoute = false;
    _updateDragState();
    PGLog.d('${widget.runtimeType} - didPop: 页面被弹出，路由: $_currentRouteName');
  }

  /// 更新拖拽状态
  void _updateDragState() {
    if (_isCurrentRoute) {
      _dragViewModel.enable();
      PGLog.d('${widget.runtimeType} - 启用拖拽功能，路由: $_currentRouteName');
    } else {
      _dragViewModel.disable();
      PGLog.d('${widget.runtimeType} - 禁用拖拽功能，路由: $_currentRouteName');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用 ChangeNotifierProvider.value 避免重建，直接使用已创建的 ViewModel
    return ChangeNotifierProvider.value(
      value: _dragViewModel,
      child: DragEventHandlerWidget(
        canProcessFiles: () {
          final canProcess =
              _isCurrentRoute && (widget.canProcessFiles?.call() ?? true);
          PGLog.d(
              '${widget.runtimeType} - canProcessFiles: $canProcess, isCurrentRoute: $_isCurrentRoute, 路由: $_currentRouteName');
          return canProcess;
        },
        showDragForbiddenOverlay: widget.showDragForbiddenOverlay,
        isSupportMultiProject: widget.isSupportMultiProject,
        onProcessingStateChanged: widget.onProcessingStateChanged,
        onDragEntered: (position) {
          PGLog.d(
              '${widget.runtimeType} - 拖拽进入，路由: $_currentRouteName, 位置: $position');
          widget.onDragEntered?.call(position);
        },
        onDragUpdated: (position) {
          widget.onDragUpdated?.call(position);
        },
        onDragExited: () {
          PGLog.d('${widget.runtimeType} - 拖拽离开，路由: $_currentRouteName');
          widget.onDragExited?.call();
        },
        onDragDone: (result) {
          PGLog.d(
              '${widget.runtimeType} - 拖拽完成，路由: $_currentRouteName, 项目数: ${result.length}');
          widget.onDragDone?.call(result);
        },
        child: widget.child,
      ),
    );
  }
}
