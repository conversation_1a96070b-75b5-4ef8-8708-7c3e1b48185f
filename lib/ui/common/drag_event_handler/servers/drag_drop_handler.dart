import 'dart:io';

import 'package:cross_file/cross_file.dart';
import 'package:flutter/foundation.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/core/tapj/n8_tapj_file_manager.dart';
import 'package:turing_art/datalayer/domain/enums/time_display_format.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/file_name_utils.dart';
import 'package:turing_art/utils/permission_util.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/tapj_processor.dart';

import 'drag_drop_interface.dart';

/// 拖放文件处理参数类（用于compute isolate）
class DragDropProcessParams {
  final List<XFile> files;
  final bool isMultiProject;

  DragDropProcessParams({
    required this.files,
    required this.isMultiProject,
  });
}

/// 拖放文件信息类（仅限当前文件内部使用）
/// 存储拖放过程中收集的文件信息
class DragFilesInfo {
  /// 第一个文件夹的名称（可能用作项目名）
  String? firstDirName;

  /// 第一个有效文件的名称（可能用作项目名）
  String? firstFileName;

  /// 确定的项目名称
  String? projectName;

  /// 是否是顶层调用标记
  bool isTopLevel = true;
}

/// 拖放名称处理器（仅限当前文件内部使用）
/// 负责处理拖放文件的项目名称生成
class DragNameProcessor {
  /// 获取默认项目名称（基于当前日期）
  static String getDefaultProjectName() {
    return DateTimeUtil.getCurrentDateStr(format: DateFormat.standardNoHour);
  }

  /// 确定最终的项目名称
  /// 优先级：1. 文件名 2. 文件夹名 3. 当前日期
  static void determineProjectName(DragFilesInfo info) {
    if (!info.isTopLevel) {
      return;
    }

    if (info.firstFileName != null && info.firstFileName!.isNotEmpty) {
      info.projectName = info.firstFileName;
    } else if (info.firstDirName != null && info.firstDirName!.isNotEmpty) {
      info.projectName = info.firstDirName;
    } else {
      info.projectName = getDefaultProjectName();
    }
  }
}

/// 桌面端拖放处理器
class DesktopDragDropHandler implements DragDropInterface {
  /// 检查Mac权限并处理tapj文件（在compute前的预处理）
  /// 返回null表示需要继续处理，返回结果表示已处理完成
  Future<DealImageFilesResult?> _preProcessCheck(List<XFile> files) async {
    // 检查Mac文件权限
    if (Platform.isMacOS) {
      final permissionGranted =
          await PermissionUtil.requestPermission(PermissionType.file);
      if (!permissionGranted) {
        PGLog.d('用户未授予文件权限');
        return null;
      }
    }

    // 检查是否有tapj文件，优先处理
    for (final xFile in files) {
      final file = File(xFile.path);

      // 检查当前文件是否为tapj文件
      if (TapjProcessor.isTapjFile(file.path)) {
        PGLog.d('在拖拽文件中发现tapj文件: ${file.path}');
        return await TapjProcessor.processTapjForImagePicker(file.path);
      }

      // 如果是文件夹，查找第一层目录中的tapj文件
      if (await FileSystemEntity.isDirectory(file.path)) {
        final tapjFilePath = await _findTapjInDirectory(file.path);
        if (tapjFilePath != null) {
          PGLog.d('在拖拽文件夹中发现tapj文件: $tapjFilePath');
          return await TapjProcessor.processTapjForImagePicker(tapjFilePath);
        }
      }
    }

    return null; // 表示需要继续后续处理
  }

  /// 查找文件夹第一层目录中的tapj文件
  /// 返回第一个找到的tapj文件路径，如果没有找到返回null
  Future<String?> _findTapjInDirectory(String directoryPath) async {
    try {
      final dir = Directory(directoryPath);
      if (!await dir.exists()) {
        return null;
      }

      // 只遍历第一层目录，不递归
      await for (final entity in dir.list(recursive: false)) {
        if (entity is File) {
          final filePath = entity.path;
          if (TapjProcessor.isTapjFile(filePath)) {
            PGLog.d('在目录第一层找到tapj文件: $filePath');
            return filePath;
          }
        }
      }

      return null;
    } catch (e) {
      PGLog.e('查找目录中的tapj文件时出错: $directoryPath, 错误: $e');
      return null;
    }
  }

  /// 处理拖放文件为单个项目
  @override
  Future<DealImageFilesResult?> processDroppedFilesForSingleProject(
      List<XFile> files) async {
    try {
      PGLog.d('DesktopDragDropHandler - processDroppedFilesForSingleProject');

      // 预处理检查（权限和tapj文件）
      final preProcessResult = await _preProcessCheck(files);
      if (preProcessResult != null) {
        // tapj文件已处理完成，直接返回结果
        return preProcessResult;
      }

      // 使用compute在后台线程处理文件
      final params = DragDropProcessParams(
        files: files,
        isMultiProject: false,
      );

      return await compute(_processFilesInIsolate, params);
    } catch (e) {
      PGLog.e('处理单项目拖放文件时出错: $e');
      return null;
    }
  }

  /// 处理拖放文件为多个项目
  @override
  Future<List<DealImageFilesResult>> processDroppedFilesForMultiProject(
      List<XFile> files) async {
    try {
      PGLog.d('DesktopDragDropHandler - processDroppedFilesForMultiProject');

      // 预处理检查（权限和tapj文件）
      final preProcessResult = await _preProcessCheck(files);
      if (preProcessResult != null) {
        // tapj文件已处理完成，直接返回结果
        return [preProcessResult];
      }

      // 先进行文件分组
      final fileGroups = _groupFilesForMultiProject(files);
      final results = <DealImageFilesResult>[];

      // 为每个文件组调用单项目处理方法，复用现有逻辑
      for (final fileGroup in fileGroups) {
        final result = await processDroppedFilesForSingleProject(fileGroup);
        if (result != null) {
          results.add(result);
        }
      }

      return results;
    } catch (e) {
      PGLog.e('处理多项目拖放文件时出错: $e');
      return [];
    }
  }

  /// 按照多项目规则分组文件
  List<List<XFile>> _groupFilesForMultiProject(List<XFile> files) {
    final groups = <List<XFile>>[];
    final looseFiles = <XFile>[];

    for (var xFile in files) {
      try {
        final fileSystemEntity = File(xFile.path);
        final fileStat = fileSystemEntity.statSync();

        if (fileStat.type == FileSystemEntityType.directory) {
          // 每个文件夹作为一个独立的项目组
          groups.add([xFile]);
        } else if (fileStat.type == FileSystemEntityType.file) {
          // 收集散落的文件
          looseFiles.add(xFile);
        }
      } catch (e) {
        // 单个文件访问失败时，记录日志但继续处理其他文件
        PGLog.e('访问文件失败，跳过: ${xFile.path}, 错误: $e');
        continue;
      }
    }

    // 如果有散落的文件，作为一个项目组
    if (looseFiles.isNotEmpty) {
      groups.add(looseFiles);
    }

    return groups;
  }

  /// Isolate中的文件处理逻辑（静态方法，供compute调用）
  static DealImageFilesResult? _processFilesInIsolate(
      DragDropProcessParams params) {
    try {
      final validFiles = <File>[];
      final fileInfo = DragFilesInfo();

      // 递归处理文件和文件夹
      _processItemsInIsolate(params.files, validFiles, fileInfo);

      // 确定项目名称
      DragNameProcessor.determineProjectName(fileInfo);

      // 返回处理结果
      return validFiles.isNotEmpty
          ? DealImageFilesResult.fromImages(
              validFiles, fileInfo.projectName ?? '')
          : null;
    } catch (e) {
      // 在isolate中不能使用PGLog，静默处理错误
      return null;
    }
  }

  /// 在Isolate中递归处理拖放的文件和文件夹
  static void _processItemsInIsolate(
    List<XFile> items,
    List<File> validFiles,
    DragFilesInfo fileInfo,
  ) {
    for (var xFile in items) {
      try {
        final fileSystemEntity = File(xFile.path);
        final fileStat = fileSystemEntity.statSync();

        if (fileStat.type == FileSystemEntityType.directory) {
          // 处理文件夹
          _processDirectoryInIsolate(xFile, validFiles, fileInfo);
        } else if (fileStat.type == FileSystemEntityType.file) {
          // 处理文件
          _processFileInIsolate(xFile, validFiles, fileInfo);
        }
      } catch (e) {
        // 单个文件/文件夹出错时，跳过继续处理其他文件
        // 在isolate中无法使用PGLog，但可以继续处理其他文件
        continue;
      }
    }
  }

  /// 在Isolate中处理单个文件夹
  static void _processDirectoryInIsolate(
      XFile xFile, List<File> validFiles, DragFilesInfo fileInfo) {
    try {
      // 只在顶层调用中记录第一个文件夹名称
      if (fileInfo.isTopLevel && fileInfo.firstDirName == null) {
        fileInfo.firstDirName = xFile.path.split(Platform.pathSeparator).last;
      }

      // 递归处理文件夹中的内容
      final dir = Directory(xFile.path);
      final entities = dir.listSync();

      // 将目录中的文件转换为XFile列表，过滤隐藏文件
      final subItems = entities
          .map((entity) => XFile(entity.path,
              name: entity.path.split(Platform.pathSeparator).last))
          .toList();

      // 保存原始的顶层状态
      final bool wasTopLevel = fileInfo.isTopLevel;

      // 设置为非顶层状态进行递归处理
      fileInfo.isTopLevel = false;

      // 递归处理子项目，直接使用现有的fileInfo对象
      _processItemsInIsolate(
        subItems,
        validFiles,
        fileInfo,
      );

      // 恢复原始的顶层状态，继续处理下一个同级别文件夹
      fileInfo.isTopLevel = wasTopLevel;
    } catch (e) {
      // 文件夹访问失败时，跳过该文件夹，继续处理其他文件
      // 可能的异常：文件夹被删除、移动、权限不足等
      return;
    }
  }

  /// 在Isolate中处理单个文件
  static void _processFileInIsolate(
      XFile xFile, List<File> validFiles, DragFilesInfo fileInfo) {
    try {
      // 检查文件扩展名是否为支持的图片格式或tapj格式
      final extension = xFile.name.split('.').last.toLowerCase();
      if (ImageConstants.supportedExtensions.contains(extension) ||
          N8TapjFileManager.supportedTapjExtensions.contains(extension)) {
        // 在添加到列表前，简单验证文件是否仍然存在
        final file = File(xFile.path);
        if (file.existsSync()) {
          validFiles.add(file);

          // 只在顶层调用中记录第一个有效文件的名称（不含扩展名）
          if (fileInfo.isTopLevel && fileInfo.firstFileName == null) {
            fileInfo.firstFileName =
                FileNameUtils.extractNameWithoutExtension(xFile.name);
          }
        }
      }
    } catch (e) {
      // 文件访问失败时，跳过该文件，继续处理其他文件
      // 可能的异常：文件被删除、移动、权限不足等
      return;
    }
  }
}

/// 移动端拖放处理器（暂时空实现）
class MobileDragDropHandler implements DragDropInterface {
  @override
  Future<DealImageFilesResult?> processDroppedFilesForSingleProject(
      List<XFile> files) async {
    // 移动端暂不支持拖放
    return null;
  }

  @override
  Future<List<DealImageFilesResult>> processDroppedFilesForMultiProject(
      List<XFile> files) async {
    // 移动端暂不支持拖放
    return [];
  }
}
