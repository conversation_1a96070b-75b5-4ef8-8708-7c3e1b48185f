import 'dart:io';

import 'package:flutter/material.dart';

class Fonts {
  // 系统字体名称 - 这些是实际系统字体的精确名称
  static const String fontFamilyPF = 'PingFang SC'; // iOS/macOS 系统字体
  static const String microsoftYaHei = 'Microsoft YaHei'; // Windows 系统字体
  static const String sansSerif = 'sans-serif'; // Android 默认无衬线字体
  static const String fontFamilySF = 'SF-Pro';

  // 获取当前平台的默认字体
  static String get defaultFontFamily {
    if (Platform.isWindows) {
      return microsoftYaHei;
    } else if (Platform.isIOS || Platform.isMacOS) {
      return fontFamilyPF;
    } else if (Platform.isAndroid) {
      return sansSerif;
    } else {
      // 其他平台使用默认字体
      return 'Roboto';
    }
  }

  // 字重
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
}
