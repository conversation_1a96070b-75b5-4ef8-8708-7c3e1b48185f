import 'package:flutter/material.dart';
import 'package:turing_art/utils/error_handler.dart';

/// 基础 ViewModel，提供统一的错误处理
abstract class BaseViewModel extends ChangeNotifier {
  final ErrorHandler _errorHandler;

  BaseViewModel(this._errorHandler);

  /// 统一的异常处理方法
  String handleException(Exception exception, [String? fallbackMessage]) {
    return _errorHandler.handleException(exception, fallbackMessage);
  }
}
