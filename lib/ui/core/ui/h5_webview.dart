import 'dart:io' show Platform;

import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../utils/pg_log.dart';

class H5WebView extends StatefulWidget {
  const H5WebView({
    super.key,
    required this.url,
    this.title = '',
    this.onClose,
    this.backgroundColor = const Color(0xFF121415),
    this.titleColor = Colors.white,
    this.closeIconColor = Colors.white,
    this.forDialog = false,
  });

  final String url;
  final String title;
  final VoidCallback? onClose;
  final Color backgroundColor;
  final Color titleColor;
  final Color closeIconColor;
  final bool forDialog;

  @override
  State<H5WebView> createState() => _H5WebViewState();
}

class _H5WebViewState extends State<H5WebView> {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _isDisposed = false;
  @override
  void initState() {
    super.initState();
    _initWebView();
    if (_isLoading) {
      PGLog.d('initState');
    }
  }

  String get _userAgent {
    if (Platform.isAndroid) {
      return 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36';
    } else if (Platform.isIOS) {
      return 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1';
    } else if (Platform.isMacOS) {
      return 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15';
    } else if (Platform.isWindows) {
      return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
    } else {
      return 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
    }
  }

  Future<void> _initWebView() async {
    if (_isDisposed) return;

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(widget.backgroundColor)
      ..setUserAgent(_userAgent)
      ..enableZoom(true)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            if (!_isDisposed) {
              setState(() => _isLoading = true);
              PGLog.d('onPageStarted');
            }
          },
          onPageFinished: (String url) {
            if (!_isDisposed) {
              setState(() => _isLoading = false);
              PGLog.d('onPageFinished');
            }
          },
          onWebResourceError: (WebResourceError error) {
            PGLog.e('WebView error: ${error.description}');
            if (!_isDisposed) {
              setState(() => _isLoading = false);
              PGLog.d('onWebResourceError');
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      );

    if (!_isDisposed) {
      try {
        await _controller.loadRequest(Uri.parse(widget.url));
      } catch (e) {
        PGLog.e('Failed to load URL: $e');
        if (!_isDisposed) {
          setState(() => _isLoading = false);
          PGLog.d('onWebResourceError');
        }
      }
    }
  }

  @override
  void dispose() {
    if (!_isDisposed) {
      _isDisposed = true;

      // 在 iOS 上使用特定的清理逻辑
      if (Platform.isIOS) {
        // 立即清理 WebView
        try {
          // 加载一个空白页面并禁用 JavaScript
          _controller.setJavaScriptMode(JavaScriptMode.disabled);
          _controller.loadRequest(Uri.parse('about:blank'));

          // 移除所有 cookies 和缓存
          _controller.clearCache();
          _controller.clearLocalStorage();
        } catch (e) {
          PGLog.e('WebView cleanup error: $e');
        }
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: widget.forDialog ? BorderRadius.circular(12) : null,
      ),
      clipBehavior: Clip.hardEdge,
      child: Column(
        children: [
          // 标题栏
          Container(
            height: 58,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(128),
                  offset: const Offset(0, 1),
                  blurRadius: 4,
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 居中标题
                Center(
                  child: Text(
                    widget.title,
                    style: TextStyle(
                      color: widget.titleColor,
                      fontSize: 16,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.semiBold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // 右侧关闭按钮
                Positioned(
                  right: 0,
                  top: 15,
                  child: Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: const Color(0xFF222526),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: IconButton(
                      onPressed: () {
                        PGLog.d('onPressedClose');
                        widget.onClose?.call();
                      },
                      icon: Icon(
                        Icons.close,
                        color: widget.closeIconColor.withAlpha(128),
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      iconSize: 20,
                      splashRadius: 15,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // WebView 内容
          Expanded(
            child: Stack(
              fit: StackFit.expand,
              children: [
                ClipRRect(
                  borderRadius: widget.forDialog
                      ? const BorderRadius.only(
                          bottomLeft: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        )
                      : BorderRadius.zero,
                  child: WebViewWidget(controller: _controller),
                ),
                // 底部渐变遮罩，仅在弹窗模式下显示
                if (widget.forDialog)
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    height: 48,
                    child: Container(
                      decoration: BoxDecoration(
                          gradient: LinearGradient(
                        begin: const Alignment(0, -1),
                        end: const Alignment(0, 1),
                        colors: [
                          widget.backgroundColor.withAlpha(0),
                          widget.backgroundColor.withAlpha(255),
                        ],
                      )),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
