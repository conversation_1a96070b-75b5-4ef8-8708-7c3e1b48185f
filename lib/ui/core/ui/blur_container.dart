import 'dart:ui';

import 'package:flutter/material.dart';

class BlurContainer extends StatelessWidget {
  const BlurContainer(
      {super.key,
      required this.child,
      this.width,
      this.height,
      this.borderRadius = 12.0,
      this.blur = 40.0,
      this.backgroundColor = const Color(0xFF121316),
      this.backgroundOpacity = 0.3,
      this.padding,
      this.borderWidth = 0.0,
      this.borderColor = Colors.transparent});

  final Widget child;
  final double? width;
  final double? height;
  final double borderRadius;
  final double blur;
  final Color backgroundColor;
  final double backgroundOpacity;
  final EdgeInsetsGeometry? padding;
  final double borderWidth;
  final Color borderColor;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: blur,
          sigmaY: blur,
        ),
        child: Container(
          width: width,
          height: height,
          padding: padding,
          decoration: BoxDecoration(
            color: backgroundColor.withAlpha((backgroundOpacity * 255).round()),
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              width: borderWidth,
              color: borderColor,
            ),
          ),
          child: child,
        ),
      ),
    );
  }
}
