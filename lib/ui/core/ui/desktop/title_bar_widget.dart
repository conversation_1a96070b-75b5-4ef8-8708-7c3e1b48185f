import 'package:flutter/material.dart';
import 'package:pg_desktop_multi_window/desktop_multi_window.dart';
import 'package:pg_desktop_multi_window/desktop_multi_window_widget.dart';
import 'package:turing_art/ui/exit_confirm/widgets/exit_confirm_dialog.dart';
import 'package:turing_art/utils/app_constants.dart';

class TitleBarWidget extends StatelessWidget {
  const TitleBarWidget(
      {required this.child, super.key, this.funcWidget, this.backgroundColor});
  final Widget child;
  final Widget? funcWidget;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    if (AppConstants.isDesktop) {
      return Column(
        children: <Widget>[
          Container(
            color: backgroundColor ?? const Color(0xFF020203),
            height: 44,
            child: Stack(
              fit: StackFit.expand,
              children: <Widget>[
                // 添加一个 DraggableWidget 作为背景
                Positioned.fill(
                  child: DraggableWidget(
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),
                // 功能按钮区域，不受 DraggableWidget 的影响
                if (funcWidget != null)
                  Positioned(
                    top: 0,
                    bottom: 0,
                    left: 0,
                    right: 144,
                    child: funcWidget!,
                  ),
                // 窗口控制按钮
                Positioned(
                  top: 0,
                  bottom: 0,
                  right: 10,
                  child: _buildWindowControlWidget(),
                ),
                // 底部新增一条线
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Colors.white.withOpacity(0.1),
                    height: 1,
                  ),
                )
              ],
            ),
          ),
          // 防止溢出
          Expanded(
            child: SizedBox(
              width: double.infinity,
              child: child,
            ),
          ),
        ],
      );
    } else {
      return child;
    }
  }

  Row _buildWindowControlWidget() {
    return Row(
      children: <Widget>[
        GestureDetector(
          child: SizedBox(
            width: 24,
            height: 24,
            child: Image.asset("assets/icons/home_window_minimize.png"),
          ),
          onTap: () async {
            final int? windowId = await DesktopMultiWindow.getActiveWindowId();
            WindowController.fromWindowId(windowId ?? -1).minimizeWindow();
          },
        ),
        const SizedBox(
          width: 20,
        ),
        GestureDetector(
          child: SizedBox(
            width: 24,
            height: 24,
            child: Image.asset("assets/icons/home_window_maximize.png"),
          ),
          onTap: () async {
            final int? windowId = await DesktopMultiWindow.getActiveWindowId();
            WindowController.fromWindowId(windowId ?? -1)
                .maximizeOrRestoreWindow();
          },
        ),
        const SizedBox(
          width: 20,
        ),
        GestureDetector(
          child: SizedBox(
            width: 24,
            height: 24,
            child: Image.asset("assets/icons/home_window_close.png"),
          ),
          onTap: () async {
            // var windowId = await DesktopMultiWindow.getActiveWindowId();
            // WindowController.fromWindowId(windowId ?? -1).close();
            // 点击关闭，需要展示退出确认弹窗
            ExitConfirmDialog.show();
          },
        ),
      ],
    );
  }

  /// 设置窗口是否可调整大小
  static Future<void> setWindowResizable({required bool resizable}) async {
    final int? windowId = await DesktopMultiWindow.getActiveWindowId();
    WindowController.fromWindowId(windowId ?? -1).resizable(resizable);
  }
}
