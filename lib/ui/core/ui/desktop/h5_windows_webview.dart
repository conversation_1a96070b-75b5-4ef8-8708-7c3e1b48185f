import 'package:flutter/material.dart';
import 'package:webview_windows/webview_windows.dart';

import '../../../../utils/pg_log.dart';

class H5WindowsWebView extends StatefulWidget {
  const H5WindowsWebView({
    super.key,
    required this.url,
    this.title = '',
    this.onClose,
    this.backgroundColor = Colors.transparent,
    this.titleColor = Colors.white,
    this.closeIconColor = Colors.white,
    this.forDialog = false,
  });

  final String url;
  final String title;
  final VoidCallback? onClose;
  final Color backgroundColor;
  final Color titleColor;
  final Color closeIconColor;
  final bool forDialog;

  @override
  State<H5WindowsWebView> createState() => _H5WindowsWebViewState();
}

class _H5WindowsWebViewState extends State<H5WindowsWebView> {
  final _controller = WebviewController();
  bool _isWebViewAvailable = false;
  bool _isPageLoaded = false;
  bool _hasError = false;
  final String _errorMessage = '加载失败，请重试！';

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  String get _userAgent {
    return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
  }

  Future<void> _initWebView() async {
    try {
      // 初始化WebView控制器
      await _controller.initialize();

      // 在初始化后立即设置背景色
      await _controller.setBackgroundColor(widget.backgroundColor);

      // 配置WebView
      await _configureWebView();

      // 在加载URL前注入CSS使背景透明
      await _controller.loadUrl(widget.url);

      // 页面加载完成后执行JavaScript确保背景透明
      _controller.loadingState.listen((state) {
        if (state == LoadingState.navigationCompleted) {
          _controller.executeScript(
              "document.body.style.backgroundColor='transparent'; document.documentElement.style.backgroundColor='transparent';");

          // 添加一个延迟，确保页面内容完全渲染后再隐藏loading
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted) {
              setState(() {
                _isPageLoaded = true;
              });
            }
          });
        }
      });

      // 更新状态，表示WebView已初始化
      if (mounted) {
        setState(() {
          _isWebViewAvailable = true;
        });
      }
    } catch (e) {
      PGLog.e('WebView初始化失败: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
        });
      }
    }
  }

  Future<void> _configureWebView() async {
    // 设置WebView背景色
    await _controller.setBackgroundColor(widget.backgroundColor);

    // 设置用户代理
    await _controller.setUserAgent(_userAgent);

    _controller.onLoadError.listen((error) {
      PGLog.e('WebView加载错误: $error');
      if (mounted) {
        setState(() {
          _hasError = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildWebViewContent() {
    // 发生错误
    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isPageLoaded = false;
                });
                _controller.reload();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    // WebView未初始化或页面未完全加载
    if (!_isWebViewAvailable || !_isPageLoaded) {
      return const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFF72561),
        ),
      );
    }

    // 显示WebView
    return Stack(
      fit: StackFit.expand,
      children: [
        // 使用传入的背景色包裹WebView
        Container(
          color: widget.backgroundColor,
          child: ClipRRect(
            borderRadius: widget.forDialog
                ? const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  )
                : BorderRadius.zero,
            child: Theme(
              data: ThemeData(
                scaffoldBackgroundColor: widget.backgroundColor,
                colorScheme: ColorScheme.dark(
                  background: widget.backgroundColor,
                  surface: widget.backgroundColor,
                ),
              ),
              child: Webview(_controller),
            ),
          ),
        ),
        // 底部渐变遮罩，仅在弹窗模式下显示
        if (widget.forDialog)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            height: 48,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: const Alignment(0, -1),
                  end: const Alignment(0, 1),
                  colors: [
                    widget.backgroundColor.withAlpha(0),
                    widget.backgroundColor.withAlpha(255),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// 执行JavaScript代码
  Future<String?> executeScript(String script) async {
    if (_isWebViewAvailable && !_hasError) {
      try {
        final result = await _controller.executeScript(script);
        return result;
      } catch (e) {
        PGLog.e('执行JavaScript失败: $e');
        return null;
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      clipBehavior: Clip.hardEdge,
      child: Column(
        children: [
          // WebView 内容
          Expanded(
            child: _buildWebViewContent(),
          ),
        ],
      ),
    );
  }
}
