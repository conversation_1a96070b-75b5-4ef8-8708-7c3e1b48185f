import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class GradientButton extends StatelessWidget {
  const GradientButton({
    super.key,
    required this.text,
    this.onPressed,
    required this.gradientColors,
    this.width,
    this.height = 56,
    this.borderRadius = 28.0,
    this.overlayColor = const Color(0x80000000),
    this.disabled = false,
    this.fontSize = 16,
  });

  final String text;
  final VoidCallback? onPressed;
  final List<Color> gradientColors;
  final double? width;
  final double height;
  final double borderRadius;
  final Color overlayColor;
  final bool disabled;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Stack(
        children: [
          Center(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white,
                fontSize: fontSize,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
              ),
            ),
          ),
          if (disabled)
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(borderRadius),
                color: overlayColor,
              ),
            ),
        ],
      ),
    );
  }
}
