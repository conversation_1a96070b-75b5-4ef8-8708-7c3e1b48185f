import 'package:flutter/cupertino.dart';

class GradientProgress extends StatefulWidget {
  final ValueNotifier<double> value;
  final bool animate;

  const GradientProgress({
    super.key,
    required this.value,
    this.animate = true, // 默认启用动画
  });

  @override
  State<StatefulWidget> createState() => ExportProgressState();
}

class ExportProgressState extends State<GradientProgress>
    with SingleTickerProviderStateMixin {
  late Tween<double> _lengthTween;
  late AnimationController _lengthAnimationController;

  @override
  void initState() {
    super.initState();
    _lengthTween = Tween(begin: 0.0, end: 1.0);
    _lengthAnimationController = AnimationController(
      duration: widget.animate
          ? const Duration(milliseconds: 1000) // 保留动画配置
          : Duration.zero, // 禁用动画时设为0时长,
      vsync: this,
    );
    // 直接设置当前值而非使用动画
    _lengthAnimationController.value = widget.value.value;

    widget.value.addListener(() {
      if (widget.animate) {
        _lengthAnimationController.animateTo(widget.value.value,
            curve: Curves.easeInOutQuad);
      } else {
        // 直接更新值不触发动画
        _lengthAnimationController.value = widget.value.value;
      }
    });
  }

  @override
  Widget build(BuildContext context) => CustomPaint(
        painter: _GradientProgressPainter(
            lengthAnimation: _lengthTween.animate(
              CurvedAnimation(
                parent: _lengthAnimationController,
                curve: Curves.easeInOutQuad,
              ),
            ),
            colors: const [Color(0xFF0095FF), Color(0xFF00DCD4)]),
      );
}

class _GradientProgressPainter extends CustomPainter {
  final Paint _paint = Paint();
  final Paint _bgPaint = Paint();
  final Animation<double> lengthAnimation;
  final List<Color> colors;
  final double strokeWidth;
  late LinearGradient gradient;

  _GradientProgressPainter({
    this.strokeWidth = 2,
    required this.lengthAnimation,
    required this.colors,
  }) : super(repaint: lengthAnimation) {
    gradient = LinearGradient(
      colors: colors,
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    );
  }

  @override
  void paint(Canvas canvas, Size size) {
    _paint.strokeWidth = strokeWidth;
    _paint.strokeCap = StrokeCap.round;
    _bgPaint.strokeWidth = strokeWidth;
    _bgPaint.strokeCap = StrokeCap.round;
    _bgPaint.color = const Color(0x26EBF2F5);
    _paint.shader =
        gradient.createShader(Rect.fromLTWH(0, 0, size.width, strokeWidth));
    canvas.drawLine(Offset(0, size.height / 2),
        Offset(size.width, size.height / 2), _bgPaint);
    canvas.drawLine(Offset(0, size.height / 2),
        Offset(size.width * lengthAnimation.value, size.height / 2), _paint);
  }

  @override
  bool shouldRepaint(covariant _GradientProgressPainter oldDelegate) {
    return (oldDelegate.lengthAnimation.value != lengthAnimation.value);
  }
}
