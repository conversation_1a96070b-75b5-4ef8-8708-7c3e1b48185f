import 'dart:math' as math;
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/blur_container.dart';
import 'package:turing_art/utils/screen_util.dart';

/// 全屏加载组件
class FullScreenLoadingDialog {
  /// 显示全屏加载组件
  /// [value] 加载进度 范围值:0.0-1.0 类型为ValueNotifier
  /// [text] 加载文案
  static void show(ValueNotifier<double> value,
      {String? text, Function? onCancelTap}) {
    SmartDialog.show(
        builder: (context) => _FullScreenLoadingWidget(
              onCancelTap: onCancelTap ?? hide,
              value: value,
              text: text,
            ),
        tag: "FullScreenLoading",
        animationType: SmartAnimationType.centerFade_otherSlide,
        maskColor: Colors.transparent);
  }

  /// 隐藏全屏加载组件
  static void hide() {
    SmartDialog.dismiss(tag: "FullScreenLoading");
  }
}

class _FullScreenLoadingWidget extends StatefulWidget {
  final ValueNotifier<double> value;
  final String? text;
  final Function onCancelTap;

  const _FullScreenLoadingWidget(
      {super.key, required this.value, this.text, required this.onCancelTap});

  @override
  State<StatefulWidget> createState() {
    return _FullScreenLoadingWidgetState();
  }
}

class _FullScreenLoadingWidgetState extends State<_FullScreenLoadingWidget>
    with TickerProviderStateMixin {
  late Tween<double> _lengthTween;
  late AnimationController _lengthAnimationController;

  @override
  void initState() {
    super.initState();
    _lengthTween = Tween(begin: 0.0, end: 1.0);
    _lengthAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _lengthAnimationController.animateTo(widget.value.value,
        curve: Curves.easeInOutQuad);
    widget.value.addListener(() {
      _lengthAnimationController.animateTo(widget.value.value,
          curve: Curves.easeInOutQuad);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: BlurContainer(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight,
          blur: 24,
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0x00121415),
                  Color(0xff121415),
                ],
              ),
            ),
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned(
                  top: ScreenUtil().screenHeight * 0.38,
                  left: 0,
                  right: 0,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 80,
                        height: 80,
                        child: CustomPaint(
                          painter: _CircleProgressPainter(
                              lengthAnimation: _lengthTween.animate(
                                CurvedAnimation(
                                  parent: _lengthAnimationController,
                                  curve: Curves.easeInOutQuad,
                                ),
                              ),
                              color: const Color(0xff05D4FF)),
                        ),
                      ),
                      if (widget.text != null)
                        Padding(
                          padding: EdgeInsets.only(top: 29),
                          child: Text(
                            widget.text!,
                            style: TextStyle(
                                color: Color(0xffffffff),
                                fontSize: 16,
                                fontFamily: Fonts.defaultFontFamily,
                                fontWeight: Fonts.semiBold),
                          ),
                        )
                    ],
                  ),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 51,
                  child: UnconstrainedBox(
                    child: GestureDetector(
                      onTap: () {
                        widget.onCancelTap();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 14, horizontal: 36),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(55),
                          border:
                              Border.all(color: Color(0x14ffffff), width: 1),
                        ),
                        child: Text(
                          "取消",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: Fonts.defaultFontFamily,
                              fontWeight: Fonts.semiBold),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          )),
    );
  }
}

class _CircleProgressPainter extends CustomPainter {
  final Paint _paint = Paint();
  final Paint _bgPaint = Paint();
  late Path _path;
  final Size widgetSize;
  final Animation<double> lengthAnimation;
  final Color color;
  final double width;

  _CircleProgressPainter({
    this.width = 6,
    this.widgetSize = const Size(80, 80),
    required this.lengthAnimation,
    required this.color,
  }) : super(repaint: lengthAnimation) {
    _path = Path()
      ..addArc(Rect.fromLTWH(0, 0, widgetSize.width, widgetSize.height),
          -math.pi / 2, math.pi * 2);
  }

  @override
  void paint(Canvas canvas, Size size) {
    _paint.color = color;
    _paint.strokeWidth = width;
    _paint.style = PaintingStyle.stroke;
    _paint.strokeCap = StrokeCap.round;
    _paint.strokeJoin = StrokeJoin.round;
    PathMetrics pathMetrics = _path.computeMetrics();
    for (var element in pathMetrics) {
      Path targetPath;
      targetPath =
          element.extractPath(0, lengthAnimation.value * element.length);

      _bgPaint.color = const Color(0x26FFFFFF);
      _bgPaint.strokeWidth = width;
      _bgPaint.style = PaintingStyle.stroke;
      _bgPaint.strokeCap = StrokeCap.round;
      canvas.drawPath(element.extractPath(0, element.length), _bgPaint);
      canvas.drawPath(targetPath, _paint);
    }
  }

  @override
  bool shouldRepaint(_CircleProgressPainter oldDelegate) {
    return (oldDelegate.lengthAnimation.value != lengthAnimation.value);
  }
}
