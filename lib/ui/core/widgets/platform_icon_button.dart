import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/app_constants.dart';

import '../../../providers/project_state_provider.dart';

/// 平台感知的图标按钮组件
///
/// 在 Windows 7 上使用自定义实现，避免闪烁问题
/// 在其他平台上使用标准的 IconButton
class PlatformIconButton extends StatefulWidget {
  /// 图标
  final Widget icon;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 图标大小
  final double? iconSize;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 约束
  final BoxConstraints? constraints;

  /// 颜色
  final Color? color;

  /// 焦点颜色
  final Color? focusColor;

  /// 悬停颜色
  final Color? hoverColor;

  /// 高亮颜色
  final Color? highlightColor;

  /// 飞溅颜色
  final Color? splashColor;

  /// 禁用颜色
  final Color? disabledColor;

  /// 是否强制使用自定义实现
  final bool forceUseCustom;

  /// 是否强制使用标准 IconButton
  final bool forceUseStandard;

  /// 工具提示
  final String? tooltip;

  /// 是否强制使用InkWell
  final bool forceUseInkWell;

  /// 构造函数
  const PlatformIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.iconSize,
    this.padding,
    this.constraints,
    this.color,
    this.focusColor,
    this.hoverColor,
    this.highlightColor,
    this.splashColor,
    this.disabledColor,
    this.tooltip,
    this.forceUseCustom = false,
    this.forceUseStandard = false,
    this.forceUseInkWell = false,
  });

  @override
  State<PlatformIconButton> createState() => _PlatformIconButtonState();
}

class _PlatformIconButtonState extends State<PlatformIconButton> {
  // 记录鼠标悬停状态
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    // 使用 PlatformMouseRegion 的判断逻辑来决定是否使用自定义实现
    final useCustom = widget.forceUseCustom || _shouldUseCustom(context);

    // 如果强制使用标准 IconButton 或不需要自定义实现，则使用标准 IconButton
    if (widget.forceUseStandard || !useCustom) {
      return IconButton(
        icon: widget.icon,
        onPressed: widget.onPressed,
        iconSize: widget.iconSize,
        padding: widget.padding,
        constraints: widget.constraints,
        color: widget.color,
        focusColor: widget.focusColor,
        hoverColor: widget.hoverColor,
        highlightColor: widget.highlightColor,
        splashColor: widget.splashColor,
        disabledColor: widget.disabledColor,
        tooltip: widget.tooltip,
      );
    }

    // 在 Windows 7 上使用自定义实现，避免闪烁问题
    // 参考_buildOptionItem方法的实现
    return Tooltip(
      message: widget.tooltip ?? '',
      child: PlatformMouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        onTap: widget.onPressed,
        cursor: widget.onPressed != null
            ? SystemMouseCursors.click
            : SystemMouseCursors.basic,
        child: Container(
          padding: widget.padding ?? const EdgeInsets.all(8.0),
          constraints: widget.constraints ?? const BoxConstraints(),
          // 使用悬停状态来改变背景颜色，避免闪烁
          color: _isHovered && widget.onPressed != null
              ? Colors.black.withOpacity(0.04)
              : Colors.transparent,
          child: IconTheme.merge(
            data: IconThemeData(
              size: widget.iconSize,
              color: widget.onPressed != null
                  ? (widget.color ?? Theme.of(context).iconTheme.color)
                  : (widget.disabledColor ?? Theme.of(context).disabledColor),
            ),
            child: widget.icon,
          ),
        ),
      ),
    );
  }

  /// 判断是否应该使用自定义实现
  ///
  /// 使用与 PlatformMouseRegion 相同的逻辑
  bool _shouldUseCustom(BuildContext context) {
    // 直接使用与 PlatformMouseRegion 相同的逻辑，避免创建临时组件
    // 检查是否是 Windows 7 或者被强制使用 InkWell
    final bool isEdit = context.read<ProjectStateProvider>().isEditing;
    return (AppConstants.isWin7 || widget.forceUseInkWell) && isEdit;
  }
}
