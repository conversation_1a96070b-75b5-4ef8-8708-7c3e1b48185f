import 'package:flutter/material.dart';

/// 主题选择组件
/// 显示一个可选择主题的网格列表，支持展开子视图选择创意
class AigcThemeList extends StatefulWidget {
  /// 背景色
  final Color backgroundColor;

  /// 整体宽度
  final double width;

  /// 整体高度
  final double height;

  /// 顶部标题文本
  final String title;

  /// 标题颜色
  final Color titleColor;

  /// 是否显示添加按钮（如果为false则显示关闭按钮）
  final bool showAddButton;

  /// 关闭按钮点击回调
  final VoidCallback? onClose;

  /// 添加按钮点击回调
  final VoidCallback? onAdd;

  /// 创建按钮点击回调（空视图状态使用）
  final VoidCallback? onCreateClick;

  /// 主题数据列表
  final List<ThemeItem> themeItems;

  /// 当选择主题变化时的回调
  final Function(ThemeItem)? onThemeChanged;

  /// 当选择创意变化时的回调
  final Function(ThemeItem, CreativeItem)? onCreativeChanged;

  /// 空视图图标资源
  final String emptyIconAsset;

  const AigcThemeList({
    super.key,
    required this.backgroundColor,
    required this.width,
    required this.height,
    required this.title,
    required this.titleColor,
    required this.showAddButton,
    this.onClose,
    this.onAdd,
    this.onCreateClick,
    required this.themeItems,
    this.onThemeChanged,
    this.onCreativeChanged,
    this.emptyIconAsset = 'assets/icons/aigc_theme_empty.png',
  });

  @override
  State<AigcThemeList> createState() => _AigcThemeListState();
}

class _AigcThemeListState extends State<AigcThemeList> {
  // 当前选中的主题索引
  int? _selectedThemeIndex;

  // 每个主题选中的创意索引（默认为0）
  final Map<int, int> _selectedCreativeIndices = {};

  @override
  void initState() {
    super.initState();
    // 默认选中第一个主题（如果有的话）
    if (widget.themeItems.isNotEmpty) {
      setState(() {
        _selectedThemeIndex = 0;
        _selectedCreativeIndices[0] = 0; // 默认选中第一个创意
      });

      // 触发选中回调
      if (widget.onThemeChanged != null) {
        widget.onThemeChanged!(widget.themeItems[0]);
      }

      if (widget.onCreativeChanged != null &&
          widget.themeItems[0].creativeItems.isNotEmpty) {
        widget.onCreativeChanged!(
            widget.themeItems[0], widget.themeItems[0].creativeItems[0]);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      color: widget.backgroundColor,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: widget.themeItems.isEmpty
                ? _buildEmptyView()
                : Stack(
                    children: [
                      _buildThemeGrid(),
                      if (_selectedThemeIndex != null) _buildCreativeSection(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建空视图
  Widget _buildEmptyView() {
    return Center(
      child: SizedBox(
        width: 156,
        height: 128,
        child: Column(
          children: [
            // 空视图图标
            SizedBox(
              width: 156,
              height: 78,
              child: Image.asset(
                widget.emptyIconAsset,
                fit: BoxFit.contain,
              ),
            ),
            // 文本提示
            const SizedBox(height: 8),
            const Text(
              "当前暂无主题预设",
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                color: Colors.white54,
                fontSize: 12,
                height: 16 / 12,
                fontWeight: FontWeight.normal,
              ),
            ),
            // 立即创建按钮
            const SizedBox(height: 10),
            GestureDetector(
              onTap: widget.onCreateClick,
              child: Container(
                width: 64,
                height: 28,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF22EDFF),
                      Color(0xFF87F5FF),
                    ],
                  ),
                ),
                alignment: Alignment.center,
                child: const Text(
                  "立即创建",
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    color: Color(0xFF1F1F1F),
                    fontSize: 12,
                    height: 16 / 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建顶部标题区域
  Widget _buildHeader() {
    return Container(
      height: 40,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          const SizedBox(width: 16),
          Text(
            widget.title,
            style: TextStyle(
              color: widget.titleColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              height: 18 / 14,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: widget.showAddButton ? widget.onAdd : widget.onClose,
            child: Padding(
              padding: const EdgeInsets.only(right: 16),
              child: SizedBox(
                width: 24,
                height: 24,
                child: Image.asset(
                  widget.showAddButton
                      ? 'assets/icons/icon_add.png'
                      : 'assets/icons/dialog_close.png',
                  width: 24,
                  height: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主题网格
  Widget _buildThemeGrid() {
    // 计算每个网格项的尺寸和间距
    const double itemWidth = 122.0;
    const double itemHeight = 122.0;
    const double spacing = 8.0;

    return Padding(
      padding: const EdgeInsets.only(bottom: 40), // 为底部渐变留出空间
      child: Stack(
        children: [
          // 主滚动视图
          RawScrollbar(
            thumbVisibility: true,
            thumbColor: Colors.white.withOpacity(0.2),
            thickness: 4,
            radius: const Radius.circular(2),
            controller: ScrollController(),
            child: GridView.builder(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: spacing,
                crossAxisSpacing: spacing,
                childAspectRatio: itemWidth / itemHeight,
              ),
              itemCount: widget.themeItems.length,
              itemBuilder: (context, index) {
                return _buildThemeItem(index);
              },
            ),
          ),
          // 底部渐变
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            height: 40,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF262626).withOpacity(0.0),
                    const Color(0xFF262626).withOpacity(0.2),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个主题项
  Widget _buildThemeItem(int index) {
    final item = widget.themeItems[index];
    final isSelected = _selectedThemeIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          // 如果点击当前已选中项，则取消选择
          if (_selectedThemeIndex == index) {
            _selectedThemeIndex = null;
          } else {
            _selectedThemeIndex = index;

            // 如果这个主题没有选中过创意，则默认选中第一个
            if (!_selectedCreativeIndices.containsKey(index) &&
                item.creativeItems.isNotEmpty) {
              _selectedCreativeIndices[index] = 0;
            }
          }
        });

        // 触发选择回调
        if (_selectedThemeIndex != null && widget.onThemeChanged != null) {
          widget.onThemeChanged!(item);

          // 如果有创意项并且已选择，触发创意选择回调
          final creativeIndex = _selectedCreativeIndices[index];
          if (creativeIndex != null &&
              item.creativeItems.isNotEmpty &&
              widget.onCreativeChanged != null) {
            widget.onCreativeChanged!(item, item.creativeItems[creativeIndex]);
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected
                ? const Color(0xFFFF9EB9)
                : Colors.white.withOpacity(0.1),
            width: 1,
          ),
          image: DecorationImage(
            image: NetworkImage(item.imageUrl),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            // 底部文本
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                item.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  height: 17 / 12,
                  fontFamily: Fonts.defaultFontFamily,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建创意选择部分
  Widget _buildCreativeSection() {
    if (_selectedThemeIndex == null) {
      return const SizedBox.shrink();
    }

    final selectedTheme = widget.themeItems[_selectedThemeIndex!];
    final creativeItems = selectedTheme.creativeItems;
    if (creativeItems.isEmpty) {
      return const SizedBox.shrink();
    }

    // 计算气泡位置
    final itemCount = widget.themeItems.length;
    final isOddItemCount = itemCount % 2 == 1;
    final selectedRow = _selectedThemeIndex! ~/ 2;
    final isLastRow = isOddItemCount
        ? _selectedThemeIndex! >= itemCount - 1
        : _selectedThemeIndex! >= itemCount - 2;

    // 找到选中项在Grid中的位置
    final isLeftItem = _selectedThemeIndex! % 2 == 0;

    // 计算三角形指示器的位置
    final triangleLeft = isLeftItem ? 55.0 : 185.0;

    // 确保为子视图列表创建一个独立的滚动控制器
    final ScrollController creativeScrollController = ScrollController();

    return Positioned(
      top: 130 + (selectedRow * 130), // 根据选中行的位置调整
      left: 16,
      right: 16,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 三角形指示器
          Container(
            margin: EdgeInsets.only(left: triangleLeft),
            width: 12,
            height: 6,
            decoration: const BoxDecoration(
              color: Color(0xFF404040),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(2),
                topRight: Radius.circular(2),
              ),
            ),
            child: CustomPaint(
              painter: TrianglePainter(),
            ),
          ),
          // 气泡内容
          Container(
            height: 132,
            decoration: BoxDecoration(
              color: const Color(0xFF404040),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // "创意选择"文本
                const Padding(
                  padding: EdgeInsets.only(left: 12, top: 17),
                  child: Text(
                    "创意选择",
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      height: 16 / 12,
                      fontFamily: Fonts.defaultFontFamily,
                    ),
                  ),
                ),
                // 创意横向滚动列表
                Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 20),
                  child: SizedBox(
                    height: 70,
                    child: Stack(
                      children: [
                        RawScrollbar(
                          thumbVisibility: true,
                          trackVisibility: true,
                          thickness: 4,
                          thumbColor: Colors.white.withOpacity(0.2),
                          trackColor: Colors.transparent,
                          radius: const Radius.circular(2),
                          controller: creativeScrollController,
                          child: ListView.builder(
                            controller: creativeScrollController,
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            itemCount: creativeItems.length,
                            itemBuilder: (context, index) {
                              return _buildCreativeItem(
                                  _selectedThemeIndex!, index);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个创意项
  Widget _buildCreativeItem(int themeIndex, int creativeIndex) {
    final theme = widget.themeItems[themeIndex];
    final creativeItem = theme.creativeItems[creativeIndex];
    final isSelected = _selectedCreativeIndices[themeIndex] == creativeIndex;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedCreativeIndices[themeIndex] = creativeIndex;
          });

          // 触发创意选择回调
          if (widget.onCreativeChanged != null) {
            widget.onCreativeChanged!(theme, creativeItem);
          }
        },
        child: Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isSelected
                  ? const Color(0xFFFF9EB9)
                  : Colors.white.withOpacity(0.1),
              width: 1,
            ),
            image: DecorationImage(
              image: NetworkImage(creativeItem.imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }
}

/// 自定义三角形指示器画笔
class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF404040)
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(0, 6)
      ..lineTo(12, 6)
      ..lineTo(6, 0)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 主题项数据模型
class ThemeItem {
  /// 主题名称
  final String name;

  /// 主题图片URL
  final String imageUrl;

  /// 创意项列表
  final List<CreativeItem> creativeItems;

  ThemeItem({
    required this.name,
    required this.imageUrl,
    required this.creativeItems,
  });
}

/// 创意项数据模型
class CreativeItem {
  /// 创意图片URL
  final String imageUrl;

  /// 创意ID
  final String id;

  CreativeItem({
    required this.imageUrl,
    required this.id,
  });
}

/// 导入字体常量
class Fonts {
  static const String defaultFontFamily = 'PingFang SC';
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
}
