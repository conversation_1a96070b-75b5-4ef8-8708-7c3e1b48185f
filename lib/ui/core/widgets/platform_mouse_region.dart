import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 平台感知的鼠标区域组件
///
/// 在 Windows 7 上使用 InkWell 实现鼠标悬停效果，避免闪烁问题
/// 在 Windows 10/11 及其他平台上使用标准的 MouseRegion
class PlatformMouseRegion extends StatelessWidget {
  /// 子组件
  final Widget child;

  /// 鼠标进入回调
  final PointerEnterEventListener? onEnter;

  /// 鼠标退出回调
  final PointerExitEventListener? onExit;

  /// 鼠标悬停回调（仅在使用 InkWell 时有效）
  final ValueChanged<bool>? onHover;

  /// 点击回调
  final VoidCallback? onTap;

  /// 鼠标指针样式
  final MouseCursor cursor;

  /// 是否强制使用 InkWell
  final bool forceUseInkWell;

  /// 是否强制使用 MouseRegion
  final bool forceUseMouseRegion;

  /// 构造函数
  const PlatformMouseRegion({
    super.key,
    required this.child,
    this.onEnter,
    this.onExit,
    this.onHover,
    this.onTap,
    this.cursor = SystemMouseCursors.click,
    this.forceUseInkWell = false,
    this.forceUseMouseRegion = false,
  });

  @override
  Widget build(BuildContext context) {
    // 判断是否使用 InkWell
    final useInkWell = forceUseInkWell || _shouldUseInkWell(context);

    // 如果强制使用 MouseRegion 或不是 Windows 7，则使用 MouseRegion
    if (forceUseMouseRegion || !useInkWell) {
      return MouseRegion(
        onEnter: onEnter,
        onExit: onExit,
        cursor: cursor,
        child: GestureDetector(
          onTap: onTap,
          child: child,
        ),
      );
    }

    // 在 Windows 7 上使用 InkWell，禁用水波纹效果
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onHover: (isHovering) {
          if (onHover != null) {
            onHover!(isHovering);
          }

          // 同时调用 onEnter 和 onExit 以保持一致性
          if (isHovering) {
            if (onEnter != null) {
              onEnter!(const PointerEnterEvent());
            }
          } else {
            if (onExit != null) {
              onExit!(const PointerExitEvent());
            }
          }
        },
        onTap: onTap,
        mouseCursor: cursor,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        hoverColor: Colors.transparent,
        child: child,
      ),
    );
  }

  /// 判断是否应该使用 InkWell
  ///
  /// 在 Windows 7 上返回 true，其他平台返回 false
  bool _shouldUseInkWell(BuildContext context) {
    if (!kIsWeb && Platform.isWindows) {
      // 获取 Windows 版本信息
      try {
        // 尝试获取 Windows 版本
        final osVersion = Platform.operatingSystemVersion.toLowerCase();

        // 检查是否是 Windows 7, 并且是在编辑页面
        if (osVersion.contains('windows 7') ||
            osVersion.contains('windows nt 6.1')) {
          final projectStateProvider = context.read<ProjectStateProvider>();
          // 当前是否处于使用 Unity 引擎的编辑状态，如果是 win7 上的 Unity project，则
          // 使用 InkWell 实现鼠标悬停效果，防止闪烁问题；否则仍然使用 MouseRegion
          final bool isInUnityEdit = projectStateProvider.isEditing &&
              projectStateProvider.isUnityEngine;

          if (isInUnityEdit) {
            return true;
          }
        }
      } catch (e) {
        PGLog.d('无法获取 Windows 版本信息: $e');
        return false;
      }
    }

    // 默认在其他平台使用 MouseRegion
    return false;
  }
}

/// 扩展方法，用于从 MouseRegion 转换为 PlatformMouseRegion
extension MouseRegionToPlatformMouseRegion on MouseRegion {
  /// 将 MouseRegion 转换为 PlatformMouseRegion
  PlatformMouseRegion toPlatformMouseRegion({
    VoidCallback? onTap,
    ValueChanged<bool>? onHover,
    bool forceUseInkWell = false,
    bool forceUseMouseRegion = false,
  }) {
    return PlatformMouseRegion(
      onEnter: onEnter,
      onExit: onExit,
      cursor: cursor,
      onTap: onTap,
      onHover: onHover,
      forceUseInkWell: forceUseInkWell,
      forceUseMouseRegion: forceUseMouseRegion,
      child: child ?? const SizedBox.shrink(),
    );
  }
}
