import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 拖拽禁止光标悬浮层
///
/// 用于显示拖拽文件时的禁止图标，跟随鼠标移动
/// 当有对话框打开时，自动显示禁止图标
class DragForbiddenCursorOverlay {
  /// 单例实例
  static final DragForbiddenCursorOverlay _instance =
      DragForbiddenCursorOverlay._internal();

  /// 工厂构造函数
  factory DragForbiddenCursorOverlay() {
    return _instance;
  }

  /// 私有构造函数
  DragForbiddenCursorOverlay._internal();

  /// 鼠标位置监听 - 采用懒加载模式
  StreamController<Offset> get _getPositionController {
    if (_positionController == null || _positionController!.isClosed) {
      _positionController = StreamController<Offset>.broadcast();
    }
    return _positionController!;
  }

  StreamController<Offset>? _positionController;

  /// 当前鼠标位置
  Offset _currentPosition = Offset.zero;

  /// 是否已显示
  bool _isShowing = false;

  /// 是否鼠标在响应区域内
  bool _isMouseInWindow = false;

  /// 拖拽图标的SmartDialog标签
  static const String _dragCursorTag = 'drag_forbidden_cursor_overlay';

  /// 显示拖拽禁止图标悬浮层
  ///
  /// 如果对话框已打开，则显示禁止图标
  static void show() {
    _instance._show();
  }

  /// 隐藏拖拽禁止图标悬浮层
  static void hide() {
    _instance._hide();
  }

  /// 更新鼠标位置
  static void updatePosition(Offset position) {
    _instance._updatePosition(position);
  }

  /// 设置鼠标在窗口内状态
  static void setMouseInWindow({required bool isInWindow}) {
    _instance._isMouseInWindow = isInWindow;
    // 如果鼠标不在窗口内，立即隐藏图标
    if (!isInWindow && _instance._isShowing) {
      _instance._hide();
    }
  }

  /// 内部方法：显示悬浮层
  void _show() {
    // 如果鼠标不在窗口内，则不显示
    if (!_isMouseInWindow) {
      return;
    }

    if (_isShowing) {
      // 已经在显示，不需要重复创建
      return;
    }

    // 确保之前的 StreamController 已经关闭，防止资源泄漏
    if (_positionController != null && !_positionController!.isClosed) {
      _positionController!.close();
      _positionController = null;
    }

    PGLog.d('显示拖拽禁止图标悬浮层');
    _isShowing = true;

    // 使用SmartDialog显示悬浮层（禁止自带动画，以免出现反方向飞入的现象）
    SmartDialog.show(
      tag: _dragCursorTag,
      alignment: Alignment.topLeft,
      useAnimation: false,
      animationTime: Duration.zero,
      builder: (_) {
        return StreamBuilder<Offset>(
          stream: _getPositionController.stream,
          initialData: _currentPosition,
          builder: (context, snapshot) {
            final position = snapshot.data ?? Offset.zero;

            // 检查位置是否在屏幕范围内
            final screenSize = MediaQuery.of(context).size;
            bool isPositionOutOfBounds = position.dx < 0 ||
                position.dy < 0 ||
                position.dx > screenSize.width ||
                position.dy > screenSize.height;

            // 如果位置超出边界，则不显示图标
            if (isPositionOutOfBounds) {
              return const SizedBox.shrink();
            }

            // 确保x坐标不为负数
            final x = position.dx - 12 < 0 ? 0 : position.dx - 12;
            // 确保y坐标不为负数
            final y = position.dy - 12 < 0 ? 0 : position.dy - 12;

            PGLog.d(
                '设置禁止图标位置: x=$x, y=$y (原始: ${position.dx}, ${position.dy})');

            return SizedBox(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Positioned(
                    left: x.toDouble(),
                    top: y.toDouble(),
                    child: IgnorePointer(
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: const BoxDecoration(
                          color: Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.block,
                            size: 20,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
      backType: SmartBackType.block,
      maskColor: Colors.transparent,
      clickMaskDismiss: false,
    );
  }

  /// 内部方法：隐藏悬浮层
  void _hide() {
    if (!_isShowing) {
      return;
    }

    PGLog.d('隐藏拖拽禁止图标悬浮层');
    _isShowing = false;

    // 关闭SmartDialog悬浮层
    SmartDialog.dismiss(tag: _dragCursorTag);

    // 关闭鼠标位置监听
    if (_positionController != null && !_positionController!.isClosed) {
      _positionController!.close();
      _positionController = null;
    }
  }

  /// 内部方法：更新鼠标位置
  void _updatePosition(Offset position) {
    if (!_isShowing || !_isMouseInWindow) {
      return;
    }

    // 当 StreamController 已关闭时不尝试发送事件
    if (_positionController != null && !_positionController!.isClosed) {
      _currentPosition = position;
      _getPositionController.add(position);
    } else if (_isShowing) {
      // 如果处于显示状态但 StreamController 已关闭，需要重新打开
      _currentPosition = position;
      _show();
    }
  }
}
