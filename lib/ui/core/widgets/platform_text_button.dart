import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/app_constants.dart';

import '../../../providers/project_state_provider.dart';

/// 平台感知的文本按钮组件
///
/// 在 Windows 7 上使用自定义实现，避免闪烁问题
/// 在其他平台上使用标准的 TextButton
class PlatformTextButton extends StatefulWidget {
  /// 子组件
  final Widget child;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 按钮样式
  final ButtonStyle? style;

  /// 是否自动聚焦
  final bool autofocus;

  /// 裁剪行为
  final Clip clipBehavior;

  /// 是否强制使用自定义实现
  final bool forceUseCustom;

  /// 是否强制使用标准 TextButton
  final bool forceUseStandard;

  /// 是否强制使用InkWell
  final bool forceUseInkWell;

  /// 构造函数
  const PlatformTextButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.autofocus = false,
    this.clipBehavior = Clip.none,
    this.forceUseCustom = false,
    this.forceUseStandard = false,
    this.forceUseInkWell = false,
  });

  @override
  State<PlatformTextButton> createState() => _PlatformTextButtonState();
}

class _PlatformTextButtonState extends State<PlatformTextButton> {
  // 记录鼠标悬停状态
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    // 使用与 PlatformMouseRegion 相同的判断逻辑来决定是否使用自定义实现
    final useCustom = widget.forceUseCustom || _shouldUseCustom(context);

    // 如果强制使用标准 TextButton 或不需要自定义实现，则使用标准 TextButton
    if (widget.forceUseStandard || !useCustom) {
      return TextButton(
        onPressed: widget.onPressed,
        style: widget.style,
        autofocus: widget.autofocus,
        clipBehavior: widget.clipBehavior,
        child: widget.child,
      );
    }

    // 在 Windows 7 上使用自定义实现，避免闪烁问题
    // 提取按钮样式
    final ButtonStyle effectiveStyle = widget.style ?? TextButton.styleFrom();
    final MaterialStateProperty<Color?>? backgroundColor =
        effectiveStyle.backgroundColor;
    final MaterialStateProperty<EdgeInsetsGeometry?>? padding =
        effectiveStyle.padding;
    final MaterialStateProperty<OutlinedBorder?>? shape = effectiveStyle.shape;
    final MaterialStateProperty<MouseCursor?>? mouseCursor =
        effectiveStyle.mouseCursor;

    // 获取当前状态下的样式值
    final Set<MaterialState> states =
        widget.onPressed != null ? {} : {MaterialState.disabled};

    final Color? bgColor = backgroundColor?.resolve(states);
    final EdgeInsetsGeometry paddingValue = padding?.resolve(states) ??
        const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0);
    final OutlinedBorder shapeValue = shape?.resolve(states) ??
        const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(4.0)),
        );
    final MouseCursor cursor = mouseCursor?.resolve(states) ??
        (widget.onPressed != null
            ? SystemMouseCursors.click
            : SystemMouseCursors.basic);

    // 参考_buildOptionItem方法的实现
    return PlatformMouseRegion(
      cursor: cursor,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      onTap: widget.onPressed,
      child: Container(
        padding: paddingValue,
        decoration: BoxDecoration(
          color: _isHovered && widget.onPressed != null
              ? (bgColor ?? Colors.transparent).withOpacity(0.08)
              : bgColor,
          borderRadius: shapeValue is RoundedRectangleBorder
              ? BorderRadius.circular(
                  (shapeValue.borderRadius as BorderRadius).topLeft.x)
              : null,
        ),
        clipBehavior: widget.clipBehavior,
        child: Center(
          child: DefaultTextStyle(
            style: Theme.of(context).textTheme.labelLarge ?? const TextStyle(),
            textAlign: TextAlign.center,
            child: widget.child,
          ),
        ),
      ),
    );
  }

  /// 判断是否应该使用自定义实现
  ///
  /// 使用与 PlatformMouseRegion 相同的逻辑
  bool _shouldUseCustom(BuildContext context) {
    // 直接使用与 PlatformMouseRegion 相同的逻辑，避免创建临时组件
    // 检查是否是 Windows 7 或者被强制使用 InkWell
    final bool isEdit = context.read<ProjectStateProvider>().isEditing;
    return (AppConstants.isWin7 || widget.forceUseInkWell) && isEdit;
  }
}
