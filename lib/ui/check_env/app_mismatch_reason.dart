/// 表示检查Windows平台运行图灵精修是否匹配的结果，此类为抽象基类，匹配结果的具体类型由子类表示
abstract class CheckAppMatchResult {
  const CheckAppMatchResult();
}

/// 匹配正常
class CheckAppOK extends CheckAppMatchResult {
  const CheckAppOK();
}

/// 匹配不正常，具体类型由子类表示
abstract class CheckAppNotOK extends CheckAppMatchResult {
  const CheckAppNotOK();
}

/// 匹配不正常，需要下载 Windows 7 版本的app
class NeedDownloadWin7App extends CheckAppNotOK {
  const NeedDownloadWin7App();
}

/// 匹配不正常，需要下载 Windows 10 版本的app
class NeedDownloadWin10App extends CheckAppNotOK {
  const NeedDownloadWin10App();
}

/// 匹配不正常，当前设备性能不足
class PoorPerformance extends CheckAppNotOK {
  const PoorPerformance({
    this.ram,
    this.vram,
  });

  final int? ram; // 内存大小
  final int? vram; // 显存大小
}
