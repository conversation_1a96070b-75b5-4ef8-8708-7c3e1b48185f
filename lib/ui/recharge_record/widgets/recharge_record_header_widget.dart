import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class RechargeRecordHeaderWidget extends StatelessWidget {
  final bool isAIGCType;

  const RechargeRecordHeaderWidget({
    super.key,
    this.isAIGCType = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1028,
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0x0FFFFFFF),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          _buildHeaderCell('套餐类型', width: 224),
          const SizedBox(width: 24),
          _buildHeaderCell('生效时间', width: 216),
          const SizedBox(width: 24),
          _buildHeaderCell(isAIGCType ? '获得AI积分' : '获得张数', width: 122),
          const SizedBox(width: 24),
          _buildHeaderCell(isAIGCType ? '已使用AI积分' : '已使用张数', width: 122),
          const SizedBox(width: 24),
          _buildHeaderCell('过期时间', width: 216),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, {required double width}) {
    return SizedBox(
      width: width,
      height: 48,
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          text,
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 14,
            height: 16 / 14,
            color: const Color(0xFFE1E2E5),
          ),
        ),
      ),
    );
  }
}
