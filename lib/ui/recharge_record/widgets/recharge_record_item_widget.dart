import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/recharge_record/recharge_record_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class RechargeRecordItemWidget extends StatelessWidget {
  final RechargeRecordItem item;

  const RechargeRecordItemWidget({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1028,
      height: 48,
      decoration: const BoxDecoration(
        color: Colors.transparent,
        border: Border(
          bottom: BorderSide(
            color: Color(0x0FFFFFFF),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 16),
          _buildItemCell(item.packageType, width: 224),
          const SizedBox(width: 24),
          _buildItemCell(item.effectiveTime, width: 216),
          const SizedBox(width: 24),
          _buildItemCell(item.total.toString(), width: 122),
          const SizedBox(width: 24),
          _buildItemCell(item.used.toString(), width: 122),
          const SizedBox(width: 24),
          _buildExpirationCell(item),
        ],
      ),
    );
  }

  Widget _buildItemCell(String text, {required double width}) {
    return SizedBox(
      width: width,
      height: 48,
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          text,
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 12,
            height: 16 / 12,
            color: const Color(0xA6EBEDF5),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildExpirationCell(RechargeRecordItem item) {
    final itemStatus = RechargeRecordItem.getStatusEnum(item.status);
    // 如果是永久有效，只显示永久文本
    if (itemStatus == RechargeRecordStatus.permanent) {
      return SizedBox(
        width: 216,
        height: 48,
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            '永久',
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              fontSize: 12,
              height: 16 / 12,
              color: const Color(0xA6EBEDF5),
            ),
          ),
        ),
      );
    }

    // 否则显示状态和过期时间
    return SizedBox(
      width: 216,
      height: 48,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            item.status,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              fontSize: 12,
              height: 16 / 12,
              color: itemStatus == RechargeRecordStatus.valid
                  ? const Color(0xFFA8F21D) // 有效
                  : const Color(0xFFF55C44), // 已过期
            ),
          ),
          Text(
            item.expirationTime,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              fontSize: 12,
              height: 16 / 12,
              color: const Color(0xA6EBEDF5),
            ),
          ),
        ],
      ),
    );
  }
}
