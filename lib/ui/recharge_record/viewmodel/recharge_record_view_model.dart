import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/recharge_record/recharge_record_model.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/recharge_record_repository.dart';
import 'package:turing_art/ui/common/date_range/date_range_util.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

// 充值记录类型
enum RechargeRecordType {
  exportCount,
  aigcCount,
  sampleCount,
}

extension RechargeRecordTypeExtension on RechargeRecordType {
  String get name {
    switch (this) {
      case RechargeRecordType.exportCount:
        return 'export_count';
      case RechargeRecordType.aigcCount:
        return 'aigc_count';
      case RechargeRecordType.sampleCount:
        return 'sample_count';
    }
  }
}

class RechargeRecordViewModel extends ChangeNotifier {
  final RechargeRecordRepository _repository;
  final AccountRepository _accountRepository;

  // 获取总剩余张数
  String getTotalRemaining(RechargeRecordType bankType) {
    switch (bankType) {
      case RechargeRecordType.exportCount:
        return _accountRepository.accountAll?.exportInfo.available.toString() ??
            '0';
      case RechargeRecordType.aigcCount:
        return _accountRepository.accountAll?.aigcInfo?.available.toString() ??
            '0';
      case RechargeRecordType.sampleCount:
        return _accountRepository.accountAll?.sampleInfo?.available
                .toString() ??
            '0';
    }
  }

  // 获取总张数
  String getTotalAmount(RechargeRecordType bankType) {
    switch (bankType) {
      case RechargeRecordType.exportCount:
        return _accountRepository.accountAll?.exportInfo.total.toString() ??
            '0';
      case RechargeRecordType.aigcCount:
        return _accountRepository.accountAll?.aigcInfo?.total.toString() ?? '0';
      case RechargeRecordType.sampleCount:
        return _accountRepository.accountAll?.sampleInfo?.total.toString() ??
            '0';
    }
  }

  // 充值记录数据
  RechargeRecordModel? _rechargeRecordModel;
  RechargeRecordModel? get rechargeRecordModel => _rechargeRecordModel;

  // 是否正在加载
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // 错误状态
  String? _errorMessage;
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;

  // 时间筛选(给服务端时间戳)
  String? _startTime;
  String? get startTime => _startTime;

  String? _endTime;
  String? get endTime => _endTime;

  // 防止重复转换，重复调用接口(UI输入的yyyy-MM-dd格式的日期字符串)
  String? _formattedStartTime;
  String? _formattedEndTime;

  // 页码信息
  int _currentPage = 1;
  int get currentPage => _currentPage;

  RechargeRecordType _bankType = RechargeRecordType.exportCount;
  RechargeRecordType get bankType => _bankType;

  final int _pageSize = 10;

  // 用户账户变更事件流
  late StreamSubscription<String> _userAccountChangeSubscription;

  // 初始化：获取第一页数据
  RechargeRecordViewModel(
    this._repository,
    this._accountRepository,
  ) {
    // Account变化监听
    _initialSubscription();
    // 获取数据
    initData();
  }

  Future<void> setBankType(RechargeRecordType value) async {
    if (_bankType == value) {
      return;
    }
    _bankType = value;
    _currentPage = 1;
    _startTime = null;
    _endTime = null;
    await fetchRechargeRecords(_currentPage);
    // 通知监听器更新UI
    notifyListeners();
  }

  Future<void> initData() async {
    // 拉取默认页1的数据
    _currentPage = 1;
    // 刷新账户信息(可能充值成功，或者兑换成功/失效，导致账户信息变化)
    _accountRepository.refreshAllAccount();
    await fetchRechargeRecords(_currentPage);
  }

  void _initialSubscription() {
    _userAccountChangeSubscription = _accountRepository.userAccountChange
        .listen(_handleUserAccountChangeEvent);
  }

  void _handleUserAccountChangeEvent(String event) {
    notifyListeners();
  }

  // 判断时间是否变化,time是UI输入的和选择的日期字符串（都是格式化后yyyy-MM-dd格式字符串）
  bool _hasTimeChanged(String? time, {bool isStartTime = true}) {
    if (isStartTime) {
      if (time != _formattedStartTime) {
        _formattedStartTime = time;
        return true;
      }
      return false;
    } else {
      if (time != _formattedEndTime) {
        _formattedEndTime = time;
        return true;
      }
      return false;
    }
  }

  // 设置开始/结束时间
  // 参数可以是：UI输入的yyyy-MM-dd格式的日期字符串
  Future<bool> setDataTime(String? startTime, String? endTime) async {
    final startTimestampChanged = _hasTimeChanged(startTime, isStartTime: true);
    final endTimestampChanged = _hasTimeChanged(endTime, isStartTime: false);
    if (!startTimestampChanged && !endTimestampChanged) {
      return false;
    }
    // 有一个时间变化，则进行转换并调用接口
    if (startTimestampChanged) {
      _startTime = _getTimestamp(startTime, isStartTime: true);
    }
    if (endTimestampChanged) {
      _endTime = _getTimestamp(endTime, isStartTime: false);
    }
    // 重置当前页为默认页
    _currentPage = 1;
    await fetchRechargeRecords(_currentPage);
    // 通知监听器更新UI
    notifyListeners();
    return true;
  }

  String? _getTimestamp(String? time, {bool isStartTime = true}) {
    if (time == null || time.isEmpty) {
      return null;
    }
    return DateRangeUtil.dateStringToTimestamp(time,
            isEndOfDay: isStartTime ? false : true)
        ?.toString();
  }

  // 获取充值记录数据
  Future<void> fetchRechargeRecords(int page) async {
    _isLoading = true;
    _errorMessage = null; // 重置错误信息
    _currentPage = page;
    notifyListeners();

    try {
      final result = await _repository.fetchRechargeRecords(
        page: page,
        pageSize: _pageSize,
        bankType: bankType.name,
        startTime: _startTime,
        endTime: _endTime,
      );

      if (result != null) {
        _rechargeRecordModel = result;
      } else {
        _rechargeRecordModel = null;
        _errorMessage = '加载数据失败';
      }
    } catch (e) {
      PGLog.e('fetchRechargeRecords error: $e');
      _rechargeRecordModel = null;
      _errorMessage = '网络异常，请稍后重试';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 清除错误状态
  void clearError() {
    _errorMessage = null;
  }

  // 显示错误提示
  void showErrorToast() {
    if (_errorMessage != null) {
      PGDialog.showToast(_errorMessage!);
      clearError();
    }
  }

  @override
  void dispose() {
    _userAccountChangeSubscription.cancel();
    super.dispose();
  }
}
