import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/export_history/viewModel/export_history_view_model.dart';

class ExportHistoryHeaderWidget extends StatelessWidget {
  final ExportHistoryType exportHistoryType;

  const ExportHistoryHeaderWidget({
    super.key,
    required this.exportHistoryType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1B1C1F),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.06),
            width: 1,
          ),
        ),
      ),
      child: SizedBox(
        width: 1162,
        height: 48,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: _buildHeaderColumns(),
        ),
      ),
    );
  }

  List<Widget> _buildHeaderColumns() {
    if (exportHistoryType == ExportHistoryType.aigc) {
      return _buildAIGCHeaderColumns();
    } else {
      return _buildExportHeaderColumns();
    }
  }

  List<Widget> _buildAIGCHeaderColumns() {
    return [
      const SizedBox(width: 16),

      // 账号类型
      SizedBox(
        width: 160,
        child: _buildHeaderText('账号类型'),
      ),

      const SizedBox(width: 24),

      // 照片名称
      SizedBox(
        width: 200,
        child: _buildHeaderText('照片名称'),
      ),

      const SizedBox(width: 24),

      // 时间
      SizedBox(
        width: 160,
        child: _buildHeaderText('时间'),
      ),

      const SizedBox(width: 24),

      // 设备名称
      SizedBox(
        width: 106,
        child: _buildHeaderText('设备名称'),
      ),

      const SizedBox(width: 24),

      // 设备ID
      SizedBox(
        width: 160,
        child: _buildHeaderText('设备ID'),
      ),

      const SizedBox(width: 24),

      // 消耗类型
      SizedBox(
        width: 100,
        child: _buildHeaderText('消耗类型'),
      ),

      const SizedBox(width: 24),

      // 消耗积分
      SizedBox(
        width: 100,
        child: _buildHeaderText('消耗积分'),
      ),
    ];
  }

  List<Widget> _buildExportHeaderColumns() {
    // 根据导出历史类型决定设备名称列的宽度
    final deviceNameWidth =
        exportHistoryType == ExportHistoryType.sample ? 160.0 : 106.0;

    return [
      const SizedBox(width: 16),

      // 账号类型
      SizedBox(
        width: 160,
        child: _buildHeaderText('账号类型'),
      ),

      const SizedBox(width: 24),

      // 项目名称
      SizedBox(
        width: 200,
        child: _buildHeaderText('项目名称'),
      ),

      const SizedBox(width: 24),

      // 导出时间
      SizedBox(
        width: 160,
        child: _buildHeaderText('导出时间'),
      ),

      const SizedBox(width: 24),

      // 设备名称
      SizedBox(
        width: deviceNameWidth,
        child: _buildHeaderText('设备名称'),
      ),

      const SizedBox(width: 24),

      // 设备ID
      SizedBox(
        width: 160,
        child: _buildHeaderText('设备ID'),
      ),

      const SizedBox(width: 24),

      // 导出张数
      SizedBox(
        width: 100,
        child: _buildHeaderText('导出张数'),
      ),

      // 只有在非小样模式下才显示计费张数列
      if (exportHistoryType != ExportHistoryType.sample) ...[
        const SizedBox(width: 24),

        // 计费张数
        SizedBox(
          width: 100,
          child: _buildHeaderText('计费张数'),
        ),
      ],
    ];
  }

  Widget _buildHeaderText(String text) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        text,
        style: TextStyle(
          fontFamily: Fonts.defaultFontFamily,
          fontSize: 14,
          color: const Color(0xFFE1E2E5),
        ),
      ),
    );
  }
}
