import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/export_history/export_history_model.dart';
import 'package:turing_art/datalayer/domain/models/export_history/export_user_model.dart';
import 'package:turing_art/ui/common/contain_over_flow_text/contain_over_flow_text_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/export_history/viewModel/export_history_view_model.dart';
import 'package:turing_art/utils/date_time_util.dart';

class ExportHistoryItemWidget extends StatefulWidget {
  final ExportHistoryItem item;
  final int index; // 添加索引参数，用于确定奇偶行
  final Map<String, ExportUserModel>? users; // 用户信息字典
  final ExportHistoryType exportHistoryType; // 导出历史类型

  const ExportHistoryItemWidget({
    super.key,
    required this.item,
    required this.index,
    this.users,
    required this.exportHistoryType,
  });

  @override
  State<ExportHistoryItemWidget> createState() =>
      _ExportHistoryItemWidgetState();
}

class _ExportHistoryItemWidgetState extends State<ExportHistoryItemWidget> {
  @override
  Widget build(BuildContext context) {
    // 格式化时间
    final String timeStr = DateTimeUtil.formatDateTime(widget.item.updatedAt);
    // 根据导出历史类型决定设备名称列的宽度
    final deviceNameWidth =
        widget.exportHistoryType == ExportHistoryType.sample ? 160.0 : 106.0;

    return Container(
      decoration: BoxDecoration(
        // 奇数行透明，偶数行有背景色
        color:
            widget.index.isEven ? Colors.transparent : const Color(0xFF292929),
      ),
      height: 52,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(width: 16),

          // 账号类型（精修导出时显示，暂时显示为空）
          SizedBox(
            width: 160,
            child: _buildAccountTypeColumn(),
          ),

          const SizedBox(width: 24),

          // 项目名称
          SizedBox(
            width: 200,
            child: Row(
              children: [
                Image.asset(
                  'assets/icons/export_document.png',
                  width: 16,
                  height: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildProjectName(),
                ),
              ],
            ),
          ),

          const SizedBox(width: 24),

          // 导出时间
          SizedBox(
            width: 160,
            child: Text(
              timeStr,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 12,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const SizedBox(width: 24),

          // 设备名称
          SizedBox(
            width: deviceNameWidth,
            child: _buildDeviceName(),
          ),

          const SizedBox(width: 24),

          // 设备ID
          SizedBox(
            width: 160,
            child: _buildDeviceId(),
          ),

          const SizedBox(width: 24),

          // 导出张数
          SizedBox(
            width: 100,
            child: Text(
              '${widget.exportHistoryType == ExportHistoryType.sample ? widget.item.sampleNumber : widget.item.exportNumber}',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 12,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
              ),
            ),
          ),

          // 只有在非小样模式下才显示计费张数列
          if (widget.exportHistoryType != ExportHistoryType.sample) ...[
            const SizedBox(width: 24),

            // 计费张数
            SizedBox(
              width: 100,
              child: Text(
                '${widget.item.chargeNumber}',
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 12,
                  color: const Color(0xFFEBEDF5).withOpacity(0.65),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 构建账号类型列（从users字典中根据userId查找用户信息）
  Widget _buildAccountTypeColumn() {
    // 根据userId从users字典中查找用户信息
    final ExportUserModel? user = widget.users?[widget.item.userId];

    if (user == null) {
      // 如果找不到用户信息，显示空内容
      return Container(
        width: 160,
        height: 52,
        alignment: Alignment.centerLeft,
        child: const SizedBox(),
      );
    }

    final isMainAccount = user.role == 'creator';
    final backgroundColor =
        isMainAccount ? const Color(0xFF002346) : const Color(0xFF493500);

    return SizedBox(
      width: 160,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),

          // 第一行：账号类型标签和名称
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 账号类型标签
              Container(
                width: 38,
                height: 16,
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                alignment: Alignment.center,
                child: Text(
                  user.role == 'creator' ? '主账号' : '子账号',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 10,
                    color: Colors.white,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),

              const SizedBox(width: 4),

              // 名称
              Expanded(
                child: SizedBox(
                  width: 118,
                  height: 16,
                  child: Text(
                    user.role == 'creator' ? '' : user.nickname,
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 12,
                      color: const Color(0xFFEBEDF5).withOpacity(0.65),
                      fontWeight: FontWeight.w400,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 4),

          // 第二行：手机号
          SizedBox(
            height: 16,
            child: Text(
              user.mobile,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 12,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
                fontWeight: FontWeight.w400,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 构建带悬浮提示的项目名称
  Widget _buildProjectName() {
    final textStyle = TextStyle(
      fontFamily: Fonts.defaultFontFamily,
      fontSize: 12,
      color: const Color(0xFFEBEDF5).withOpacity(0.65),
    );

    // 使用统一溢出文本组件
    return ContainOverFlowTipTextWidget(
      text: widget.item.name,
      style: textStyle,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  // 构建带悬浮提示的设备名称
  Widget _buildDeviceName() {
    final textStyle = TextStyle(
      fontFamily: Fonts.defaultFontFamily,
      fontSize: 12,
      color: const Color(0xFFEBEDF5).withOpacity(0.65),
    );

    return ContainOverFlowTipTextWidget(
      text: widget.item.hostname,
      style: textStyle,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  // 构建带悬浮提示的设备ID
  Widget _buildDeviceId() {
    final textStyle = TextStyle(
      fontFamily: Fonts.defaultFontFamily,
      fontSize: 12,
      color: const Color(0xFFEBEDF5).withOpacity(0.65),
    );

    return ContainOverFlowTipTextWidget(
      text: widget.item.deviceId,
      style: textStyle,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }
}
