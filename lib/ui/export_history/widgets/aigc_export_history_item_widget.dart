import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_export_history/aigc_export_history_model.dart';
import 'package:turing_art/ui/common/contain_over_flow_text/contain_over_flow_text_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/date_time_util.dart';

class AIGCExportHistoryItemWidget extends StatelessWidget {
  final AIGCExportHistoryItem item;
  final int index;

  const AIGCExportHistoryItemWidget({
    super.key,
    required this.item,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    // 格式化时间
    final String timeStr = DateTimeUtil.formatDateTime(item.createAt);

    return Container(
      decoration: BoxDecoration(
        // 奇数行透明，偶数行有背景色
        color: index.isEven ? const Color(0xFF1E1E1E) : const Color(0xFF292929),
      ),
      height: 52,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(width: 16),

          // 账号类型
          SizedBox(
            width: 160,
            child: _buildAccountTypeColumn(),
          ),

          const SizedBox(width: 24),

          // 照片名称
          SizedBox(
            width: 200,
            child: _buildPhotoName(),
          ),

          const SizedBox(width: 24),

          // 时间
          SizedBox(
            width: 160,
            child: _buildText(timeStr),
          ),

          const SizedBox(width: 24),

          // 设备名称
          SizedBox(
            width: 106,
            child: _buildDeviceName(),
          ),

          const SizedBox(width: 24),

          // 设备ID
          SizedBox(
            width: 160,
            child: _buildDeviceId(),
          ),

          const SizedBox(width: 24),

          // 消耗类型
          SizedBox(
            width: 100,
            child: _buildText(item.costType),
          ),

          const SizedBox(width: 24),

          // 消耗积分
          SizedBox(
            width: 100,
            child: _buildText('${item.costValue}'),
          ),
        ],
      ),
    );
  }

  // 构建账号类型列
  Widget _buildAccountTypeColumn() {
    final isMainAccount = item.accountRole == '主账号';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),

        // 第一行：账号类型标签和名称
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 账号类型标签
            Container(
              width: 38,
              height: 16,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: isMainAccount
                    ? const Color(0xFF002346)
                    : const Color(0xFF493500),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                item.accountRole,
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 10,
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),

            const SizedBox(width: 4),

            // 名称
            Expanded(
              child: SizedBox(
                width: 118,
                height: 16,
                child: Text(
                  item.nickname ?? '',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 12,
                    color: const Color(0xFFEBEDF5).withOpacity(0.65),
                    fontWeight: FontWeight.w400,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // 第二行：手机号
        SizedBox(
          height: 16,
          child: Text(
            item.mobile,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontSize: 12,
              color: const Color(0xFFEBEDF5).withOpacity(0.65),
              fontWeight: FontWeight.w400,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // 构建普通文本
  Widget _buildText(String text) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        text,
        style: TextStyle(
          fontFamily: Fonts.defaultFontFamily,
          fontSize: 12,
          color: const Color(0xFFEBEDF5).withOpacity(0.65),
          fontWeight: FontWeight.w400,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  // 构建带悬浮提示的照片名称
  Widget _buildPhotoName() {
    final textStyle = TextStyle(
      fontFamily: Fonts.defaultFontFamily,
      fontSize: 12,
      color: const Color(0xFFEBEDF5).withOpacity(0.65),
      fontWeight: FontWeight.w400,
    );

    return Align(
      alignment: Alignment.centerLeft,
      child: ContainOverFlowTipTextWidget(
        text: item.photoName,
        style: textStyle,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  // 构建带悬浮提示的设备名称
  Widget _buildDeviceName() {
    final textStyle = TextStyle(
      fontFamily: Fonts.defaultFontFamily,
      fontSize: 12,
      color: const Color(0xFFEBEDF5).withOpacity(0.65),
      fontWeight: FontWeight.w400,
    );

    return Align(
      alignment: Alignment.centerLeft,
      child: ContainOverFlowTipTextWidget(
        text: item.hostname ?? '',
        style: textStyle,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  // 构建带悬浮提示的设备ID
  Widget _buildDeviceId() {
    final textStyle = TextStyle(
      fontFamily: Fonts.defaultFontFamily,
      fontSize: 12,
      color: const Color(0xFFEBEDF5).withOpacity(0.65),
      fontWeight: FontWeight.w400,
    );

    return Align(
      alignment: Alignment.centerLeft,
      child: ContainOverFlowTipTextWidget(
        text: item.deviceId,
        style: textStyle,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }
}
