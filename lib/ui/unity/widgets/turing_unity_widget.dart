import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/ui/unity/constant/turing_unity_constant.dart';

import '../../../core/unity/unity_controller.dart';
import '../../../datalayer/domain/models/message_from_unity/message_from_unity.dart';
import '../../../utils/pg_log.dart';
import '../../../widgets/unity/universal_unity_widget.dart';
import '../../../widgets/unity/windows_unity_widget.dart';

typedef OnCreatedCallback = void Function(UnityController controller);
typedef OnActionCallback = void Function(
  TuringActions action,
  MessageFromUnity message,
);
typedef OnUnityMessageCallback = void Function(MessageFromUnity message);
typedef OnDebugMessageCallback = void Function(String message);
typedef OnEngineErrorCallback = void Function(Object error);
typedef UnityMessageCallback = void Function(dynamic message);

class TuringUnityWidget extends StatefulWidget {
  const TuringUnityWidget({
    super.key,
    onCreatedCallback,
    onActionCallback,
    onDebugMessageCallback,
    onEngineErrorCallback,
    onUnityMessageCallback,
  })  : _onCreatedCallback = onCreatedCallback,
        _onActionCallback = onActionCallback,
        _onDebugMessageCallback = onDebugMessageCallback,
        _onEngineErrorCallback = onEngineErrorCallback,
        _onUnityMessageCallback = onUnityMessageCallback;

  final OnCreatedCallback? _onCreatedCallback;

  final OnActionCallback? _onActionCallback;

  final OnUnityMessageCallback? _onUnityMessageCallback;

  final OnDebugMessageCallback? _onDebugMessageCallback;

  final OnEngineErrorCallback? _onEngineErrorCallback;

  @override
  State<TuringUnityWidget> createState() => _TuringUnityWidgetState();
}

class _TuringUnityWidgetState extends State<TuringUnityWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildUnityWidget();
  }

  Widget _buildUnityWidget() {
    if (defaultTargetPlatform == TargetPlatform.windows) {
      return WindowsUnityWidget(
        onUnityCreated: () {
          PGLog.d('UnityWidgetService onUnityCreated');
          widget._onCreatedCallback?.call(context.read<UnityController>());
        },
        onUnityMessage: (message) {
          // 限制message打印长度，避免日志过长
          const int maxLength = 200;
          final truncatedMessage = message.length > maxLength
              ? '${message.substring(0, maxLength)}...'
              : message;
          PGLog.d('UnityWidgetService onUnityMessage: $truncatedMessage');
          widget._onDebugMessageCallback?.call(message);
          try {
            if (context.mounted) {
              if (message is String) {
                final Map<String, dynamic> messageMap = json.decode(message);
                final messageObj = MessageFromUnity.fromJson(messageMap);
                _handleMessageFromUnity(context, messageObj);
              }
            } else {
              widget._onEngineErrorCallback?.call(Error());
            }
          } catch (e) {
            widget._onEngineErrorCallback?.call(e);
            final error = 'Failed to parse Unity message: $e';
            PGLog.e(error);
          }
        },
      );
    } else {
      return UniversalUnityWidget(
        onUnityCreated: () {
          PGLog.d('UnityWidgetService onUnityCreated');
          widget._onCreatedCallback?.call(context.read<UnityController>());
        },
        onUnityMessage: (message) {
          // 限制message打印长度，避免日志过长
          const int maxLength = 200;
          final truncatedMessage = message.length > maxLength
              ? '${message.substring(0, maxLength)}...'
              : message;
          PGLog.d('UnityWidgetService onUnityMessage: $truncatedMessage');
          widget._onDebugMessageCallback?.call(message);
          try {
            if (message is String) {
              final Map<String, dynamic> messageMap = json.decode(message);
              final messageObj = MessageFromUnity.fromJson(messageMap);
              _handleMessageFromUnity(context, messageObj);
            }
          } catch (e) {
            widget._onEngineErrorCallback?.call(e);
            final error = 'Failed to parse Unity message: $e';
            PGLog.e(error);
          }
        },
      );
    }
  }

  /// 处理消息回调
  void _handleMessageFromUnity(
    BuildContext context,
    MessageFromUnity messageObj,
  ) {
    if (messageObj.method == UnityMessage.exportButtonClicked.value) {
      /// 导出事件捕获
      widget._onActionCallback?.call(TuringActions.export, messageObj);
    } else if (messageObj.method == UnityMessage.homeButtonClicked.value) {
      /// 返回事件捕获
      widget._onActionCallback?.call(TuringActions.home, messageObj);
    } else {
      /// 其他事件转发至UnityController处理
      final processed =
          context.read<UnityController>().handleResponse(messageObj);
      if (!processed) {
        /// 该事件UnityController未处理，转发至外部处理
        widget._onUnityMessageCallback?.call(messageObj);
      }
    }
  }
}
