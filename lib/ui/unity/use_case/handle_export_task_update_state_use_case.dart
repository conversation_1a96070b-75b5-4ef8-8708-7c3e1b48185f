import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

class HandleExportTaskUpdateStateUseCase {
  final ExportTaskStateProvider _exportTaskStateProvider;

  HandleExportTaskUpdateStateUseCase(this._exportTaskStateProvider);

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    PGLog.d('HandleExportTaskUpdateStateUseCase: 收到Unity消息: ${message.method}');
    final args = message.args;
    if (args == null || args.isEmpty) {
      PGLog.e('HandleExportTaskUpdateStateUseCase: 参数为空');
      return;
    }

    try {
      final jsonString = args[0] as String;
      await _exportTaskStateProvider.updateExportTaskState(jsonString);
    } catch (e) {
      PGLog.d('HandleExportProjectUseCase: 处理导出失败错误: $e');
    }
  }
}
