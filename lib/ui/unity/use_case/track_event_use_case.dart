import 'package:pg_turing_collect_event/model.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理从Unity发送过来的事件埋点消息的UseCase
class HandleTrackEventUseCase {

  HandleTrackEventUseCase();

  Future<void> invoke(MessageFromUnity message) async {
    final args = message.args;
    if (args == null || args.length < 2) {
      PGLog.e('HandleTrackEventUseCase: 接收到的消息参数不正确: ${message.args}');
      return;
    }
    try {
      final eventId = args[0] as String?;
      final event = args[1] as String?;
      PGLog.d('接收的埋点事件id: $eventId, 事件: $event');

      if (eventId != null && event != null) {
        PgTuringCollect().trackEvent(eventId, event);
      }
    } catch (e) {
      PGLog.e('HandleTrackEventUseCase: 处理事件埋点失败: $e');
    }
  }
}
