import 'dart:convert';

import 'package:uuid/uuid.dart';

import '../../../datalayer/domain/models/message_to_unity/message_to_unity.dart';
import '../../../datalayer/repository/project_repository.dart';
import '../../../utils/pg_log.dart';

// 导出设置的Json字符串
class GenerateSetupExportConfigUnityMessageUseCase {
  final ProjectRepository _repository;

  GenerateSetupExportConfigUnityMessageUseCase(this._repository);

  Future<MessageToUnity?> invoke(String projectId) async {
    try {
      final project = await _repository.getProjectById(projectId);
      if (project == null) return null;
      final exportConfig = project.exportConfig;
      final exportConfigObj = exportConfig.toJson();
      String json = jsonEncode(exportConfigObj);
      MessageToUnity msg = MessageToUnity(
        method: 'SetupExportConfig',
        args: json, // 使用 JSON 字符串作为参数
        completed: const Uuid().v4(),
      );
      return msg;
    } catch (e) {
      PGLog.e('generate setup export config message use case error : $e');
      return null;
    }
  }
}
