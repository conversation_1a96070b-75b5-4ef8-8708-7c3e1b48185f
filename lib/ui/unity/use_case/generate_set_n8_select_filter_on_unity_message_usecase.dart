import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

/// 生成设置N8选版过滤器开关的Unity消息
class GenerateSetN8SelectFilterOnUnityMessageUseCase {
  GenerateSetN8SelectFilterOnUnityMessageUseCase();

  /// 生成设置N8选版过滤器开关的Unity消息
  ///
  /// [workspaceId] 工作区ID
  /// [isOn] 过滤器开关状态
  ///
  /// 返回MessageToUnity消息
  Future<MessageToUnity?> invoke(String workspaceId, bool isOn) async {
    try {
      final jsonParams = jsonEncode({
        'workspaceId': workspaceId,
        'isOn': isOn,
      });

      final message = MessageToUnity(
        method: 'SetN8SelectFilterOn',
        args: jsonParams,
        completed: const Uuid().v4(),
      );

      return message;
    } catch (e) {
      PGLog.e('generate set n8 select filter on message use case error : $e');
      return null;
    }
  }
}
