// 创建Unity登录接口的Json字符串

import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class GenerateLoginUnityMessageUseCase {
  final CurrentUserRepository _currentUserRepository;

  GenerateLoginUnityMessageUseCase(this._currentUserRepository);

  Future<MessageToUnity?> invoke() async {
    try {
      final userRootDir = FileManager().getUserRootDir();
      if (userRootDir == null) {
        return null;
      }

      final currentUser = _currentUserRepository.user;
      if (currentUser == null) {
        return null;
      }
      final couldExportSample =
          _currentUserRepository.store?.couldExportSample() ?? false;
      // 使用 effectiveId 作为 userId，这一点非常重要
      MessageToUnity msg = MessageToUnity(
        method: 'Login',
        args: jsonEncode({
          "userName": currentUser.nickname,
          "userId": currentUser.effectiveId,
          "folder": userRootDir.path,
          "dbPath": FileManager().dbPath,
          "mobile": currentUser.mobile,
          "couldExportSample": couldExportSample,
        }),
        completed: const Uuid().v4(),
      );
      return msg;
    } catch (e) {
      PGLog.e('generate login message use case error : $e');
      return null;
    }
  }
}
