import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理获取加密LUT参数消息的UseCase
class HandleEncryptedLutParamsUseCase {
  final UnityController _unityController;
  final ExportUseCaseProvider _exportUseCaseProvider;

  HandleEncryptedLutParamsUseCase(
    this._unityController,
    this._exportUseCaseProvider,
  );

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    final taskId = message.taskId!;
    try {
      // 解析 JSON 字符串
      final String jsonStr = message.args![0] as String;
      final Map<String, dynamic> data =
          json.decode(jsonStr) as Map<String, dynamic>;

      // 提取所有参数
      final String clientTime = data['clientTime'] as String;
      final String path = data['path'] as String;
      final String hash = data['hash'] as String;
      final String exportToken = data['exportToken'] as String;
      final String encryptedPresetData = data['encryptedPresetData'] as String;
      final String encryptedAdjustData = data['encryptedAdjustData'] as String;

      if (hash.isEmpty) {
        throw Exception('接收Unity待生成Lut的图片hash为空');
      }

      if (encryptedAdjustData.isEmpty) {
        throw Exception('接收Unity加密LUT参数为空');
      }

      // 调用导出参数调整用例
      final result = await _exportUseCaseProvider.adjustExportParams.invoke(
        hash,
        path,
        exportToken,
        encryptedPresetData,
        encryptedAdjustData,
        customPGTime: clientTime,
      );

      // 返回结果给Unity
      if (result != null) {
        // 从result.fields中提取对应的字段值
        String encryptedPresetValue = '';
        String encryptedLutValue = '';

        // 遍历fields找到对应字段
        for (var field in result.fields) {
          if (field.field == 'paramsAesEncrypted') {
            encryptedPresetValue = field.value;
          } else if (field.field == 'lutParamsEncrypted') {
            encryptedLutValue = field.value;
          }
        }

        if (encryptedLutValue.isEmpty) {
          throw Exception('服务端返回加密LUT图为空');
        }

        // 按照Unity的EncryptedLut结构构造返回数据
        final encryptedLutResponse = {
          'encryptedPresetData': encryptedPresetValue,
          'encryptedLut': encryptedLutValue
        };

        // 将结果转为JSON字符串并发送
        final responseJson = json.encode(encryptedLutResponse);
        _unityController.sendTriggerCallback(taskId, responseJson);
        PGLog.d(
            '发送加密LUT参数到Unity成功: ${responseJson.length > 50 ? "${responseJson.substring(0, 50)}..." : responseJson}');
      } else {
        // 返回空结果
        final emptyResult = {'encryptedPresetData': '', 'encryptedLut': ''};
        _unityController.sendTriggerCallback(taskId, json.encode(emptyResult));
        PGLog.e('调整导出参数返回null');
      }
    } catch (e) {
      PGLog.e('处理加密LUT参数请求失败: $e');
      final emptyResult = {'encryptedPresetData': '', 'encryptedLut': ''};
      _unityController.sendTriggerCallback(taskId, json.encode(emptyResult));
    }
  }
}
