import 'dart:convert';

import 'package:turing_art/config/env_config.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理获取动态加密参数消息的UseCase
class HandleDynamicEncryptionParamsUseCase {
  final UnityController _unityController;
  final CurrentUserRepository _currentUserRepository;

  HandleDynamicEncryptionParamsUseCase(
    this._unityController,
    this._currentUserRepository,
  );

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    final taskId = message.taskId!;
    try {
      // 获取时间戳并保存
      final time = DateTime.now().millisecondsSinceEpoch;

      PGLog.d("request client time=$time");

      final userId = await _currentUserRepository.getUserId();
      if (userId?.isEmpty ?? true) {
        // 当前用户未登录
        _unityController.sendTriggerCallback(
            taskId,
            json.encode({
              'userId': '',
              'appVersion': '',
              'clientTime': 0,
              'envType': 0, // 出错时默认使用开发环境
            }));
        return;
      }

      // 获取正确的appVersion
      final appVersion = EnvConfig.version;

      // 将Environment枚举转换为int值：0=DEV开发环境, 1=QA测试环境, 2=PROD生产环境
      int envType;
      switch (EnvConfig.environment) {
        case Environment.dev:
          envType = 0;
        case Environment.qa:
          envType = 1;
        case Environment.prod:
          envType = 2;
      }

      // 构造动态加密参数
      final params = json.encode({
        'userId': userId, // 使用当前用户ID
        'appVersion': appVersion, // 使用系统配置的版本号
        'clientTime': time, // 当前时间戳
        'envType': envType, // 环境类型的整数表示
      });

      _unityController.sendTriggerCallback(taskId, params);
    } catch (e) {
      PGLog.e('处理动态加密参数请求失败: $e');
      _unityController.sendTriggerCallback(
          taskId,
          json.encode({
            'userId': '',
            'appVersion': '',
            'clientTime': 0,
            'envType': 0, // 出错时默认使用开发环境
          }));
    }
  }
}
