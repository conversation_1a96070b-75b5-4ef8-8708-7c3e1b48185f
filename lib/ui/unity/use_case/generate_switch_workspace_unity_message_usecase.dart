import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

// 切换工作区
class GenerateSwitchWorkspaceUnityMessageUseCase {
  GenerateSwitchWorkspaceUnityMessageUseCase(this._repository);
  final ProjectRepository _repository;

  Future<MessageToUnity?> invoke(
    String projectId, {
    bool isMigrated = false,
  }) async {
    try {
      if (projectId.isEmpty) {
        return null;
      }
      final workspace = await _repository.getWorkspaceByProjectId(projectId);
      if (workspace == null) {
        return null;
      }
      final msg = MessageToUnity(
        method: 'SwitchWorkspace',
        args: jsonEncode({
          "workspaceId": workspace.workspaceId,
          "currentFileId": workspace.currentFileId ?? '',
          "isMigrated": isMigrated,
        }),
        completed: const Uuid().v4(),
      );
      return msg;
    } catch (e) {
      PGLog.e('generate switch workspace message use case error : $e');
      return null;
    }
  }
}
