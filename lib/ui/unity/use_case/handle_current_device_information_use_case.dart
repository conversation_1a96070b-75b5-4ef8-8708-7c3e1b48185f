import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/ui/setting/provider/current_device_information_provider.dart';

class HandleCurrentDeviceInformationUseCase {
  final CurrentDeviceInformationProvider _currentDeviceInformationProvider;

  HandleCurrentDeviceInformationUseCase(
    this._currentDeviceInformationProvider,
  );

  /// 执行处理
  void invoke(MessageFromUnity message) {
    _currentDeviceInformationProvider.onCurrentDeviceInformationUpdate(message);
  }
}
