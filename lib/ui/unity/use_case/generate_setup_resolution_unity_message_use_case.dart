import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:uuid/uuid.dart';

class GenerateSetupResolutionUnityMessageUseCase {
  GenerateSetupResolutionUnityMessageUseCase();

  MessageToUnity invoke(int previewSize) {
    return MessageToUnity(
      method: 'SetupPreviewConfig',
      args: jsonEncode({
        "previewSize": previewSize,
      }),
      completed: const Uuid().v4(),
    );
  }
}
