import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:turing_art/core/service/disk_space_size/disk_free_space_size_service.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/repository/export_exception.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 获取缓存磁盘空间用例
///
/// 该用例封装了获取缓存磁盘空间的过程
class GetCacheDiskSpaceUseCase {
  final UnityController _unityController;

  /// 创建 [GetCacheDiskSpaceUseCase] 实例
  GetCacheDiskSpaceUseCase(this._unityController);

  /// 获取当前设备缓存磁盘空间
  ///
  /// 返回磁盘空间，失败返回0 [ExportException]
  Future<void> invoke(MessageFromUnity message) async {
    if (message.args?.isEmpty == true) {
      return;
    }

    final taskId = message.taskId;
    if (taskId == null) {
      return;
    }

    try {
      // 解析 JSON 字符串获取 path
      final jsonStr = message.args?[0];
      if (jsonStr == null) {
        _unityController.sendTriggerCallback(
          taskId,
          json.encode(
            {'size': '0'},
          ),
        );
        PGLog.e('获取缓存磁盘空间失败: 参数错误 ${message.args}');
        return;
      }

      // 处理 JSON 字符串中的反斜杠
      final normalizedJsonStr = jsonStr.replaceAll('\\', '\\\\');

      final Map<String, dynamic> jsonMap = json.decode(normalizedJsonStr);
      final path = jsonMap['path'] as String?;

      if (path == null) {
        _unityController.sendTriggerCallback(
          taskId,
          json.encode(
            {'size': '0'},
          ),
        );
        PGLog.e('获取缓存磁盘空间失败: 参数错误 ${message.args}');
        return;
      }

      // 从路径中提取根目录
      final rootDir = '${path.split('\\')[0]}\\';

      // 在子线程中获取设备的磁盘大小
      final totalDiskSpace = await compute(
        _getDiskSpaceInBackground,
        rootDir,
      );

      _unityController.sendTriggerCallback(
        taskId,
        json.encode(
          {'size': '${totalDiskSpace ?? 0}'},
        ),
      );
      PGLog.i('获取缓存磁盘空间成功: ${totalDiskSpace ?? 0}');
    } catch (e) {
      PGLog.e('获取缓存磁盘空间失败: $e');
      _unityController.sendTriggerCallback(taskId, '0');
    }
  }
}

/// 在后台线程中获取磁盘空间
Future<double?> _getDiskSpaceInBackground(String rootDir) async {
  try {
    final time = DateTime.now();
    final size = await DiskFreeSpaceSizeService.forPlatform()
        .getDirectoryFreeDiskSpaceSize(dir: rootDir);
    final endTime = DateTime.now();
    PGLog.i('获取磁盘空间耗时: ${endTime.difference(time)}');
    return size;
  } catch (e) {
    PGLog.e('后台获取磁盘空间失败: $e');
    return 0;
  }
}
