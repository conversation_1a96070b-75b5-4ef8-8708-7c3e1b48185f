import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/ui/dialog/single_button_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class HandleExportFailedUseCase {
  final ExportProjectProvider _exportProjectProvider;
  HandleExportFailedUseCase(this._exportProjectProvider);

  Future<void> invoke(MessageFromUnity message) async {
    PGLog.d('HandleExportFailedUseCase: 收到Unity消息: ${message.method}');
    final args = message.args;
    if (args == null || args.isEmpty) {
      PGLog.e('HandleExportFailedUseCase: 参数为空');
      return;
    }

    try {
      final jsonString = args[0] as String;
      final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;

      final errorNum = jsonMap['errorNum'] as int?;
      final errorMessage = jsonMap['errorMessage'] as String?;

      _exportProjectProvider.updateExportProjectRecord(jsonString);

      if (errorNum == null) {
        PGLog.e('HandleExportFailedUseCase: 导出失败记录为空');
        return;
      }

      // 使用ExportErrorType.fromValue来转换错误码
      final errorType = ExportErrorType.fromValue(errorNum);

      // 根据错误类型显示对应的错误提示
      if (errorType == ExportErrorType.authFailed ||
          errorType == ExportErrorType.diskSpaceNotEnough) {
        SingleButtonDialog.show(
          title: '导出失败',
          content: errorMessage ?? errorType.message,
        );
      } else {
        PGLog.d('HandleExportFailedUseCase: 导出失败，错误类型: ${errorType.name}');
      }
    } catch (e) {
      PGLog.e('HandleExportFailedUseCase: 处理导出失败错误: $e');
    }
  }
}
