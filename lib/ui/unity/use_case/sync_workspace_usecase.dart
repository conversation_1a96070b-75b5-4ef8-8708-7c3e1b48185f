// 切换工作区
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

class SyncWorkspaceUseCase {
  SyncWorkspaceUseCase(this._projectRepository);
  final ProjectRepository _projectRepository;

  Future<bool> invoke(MessageFromUnity message) async {
    try {
      final List<String> args =
          (message.args ?? []).map((e) => e.toString()).toList();
      final nowTime = DateTime.now().millisecondsSinceEpoch;
      PGLog.d('OnSyncWorkspace: $nowTime $args');

      if (args.isEmpty) {
        return false;
      }

      // 在 isolate 中处理 JSON 解析
      final List<Map<String, dynamic>> workspaces =
          await compute<List<String>, List<Map<String, dynamic>>>(
        (List<String> jsonArgs) {
          final List<Map<String, dynamic>> result = [];
          for (var arg in jsonArgs) {
            try {
              final jsonArray = jsonDecode(arg) as List;
              if (jsonArray.isEmpty) {
                continue;
              }
              for (var workspaceJson in jsonArray) {
                result.add(workspaceJson as Map<String, dynamic>);
              }
            } catch (e) {
              PGLog.e('JSON parsing error: $e');
            }
          }
          return result;
        },
        args,
      );

      // 在主线程中处理数据库操作
      for (var workspaceJson in workspaces) {
        final workspace = Workspace.fromJson(workspaceJson);
        await _projectRepository.updateWorkspaceWithoutFiles(workspace);
        final endTime = DateTime.now().millisecondsSinceEpoch;
        PGLog.d('OnSyncWorkspace: 工作区数据同步成功: ${endTime - nowTime}ms');
        PGLog.d('OnSyncWorkspace: 更新项目信息结束时间: $endTime');
      }
      return true;
    } catch (e) {
      PGLog.e('OnSyncWorkspace error: $e');
      return false;
    }
  }

  // // 获取前三张图片的绝对路径
  // List<String> _getTopThreeCoverImages(ProjectInfo project) {
  //   final workSpace = project.workspace;
  //   final files = workSpace.files;
  //   if (files.isEmpty) {
  //     return [];
  //   }

  //   // 按更新时间排序
  //   final sortedFiles = List.of(files)
  //     ..sort((a, b) => b.lastEditTime.compareTo(a.lastEditTime));

  //   // 获取前三张图片的相对路径
  //   return sortedFiles.take(3).map((e) => e.orgPath).toList();
  // }
}
