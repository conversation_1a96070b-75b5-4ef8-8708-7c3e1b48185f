import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:uuid/uuid.dart';

// 设置控制配置的Json字符串
class GenerateSetupControlConfigUnityMessageUseCase {
  GenerateSetupControlConfigUnityMessageUseCase();

  MessageToUnity invoke({required bool isMagicControl}) {
    return MessageToUnity(
      method: 'SetUserInputDeviceMode',
      args: isMagicControl ? 'tablet' : 'mouse',
      completed: const Uuid().v4(),
    );
  }
}
