// 创建导入工作区登录接口的Json字符串
import 'dart:convert';

import 'package:uuid/uuid.dart';

import '../../../datalayer/domain/models/message_to_unity/message_to_unity.dart';
import '../../../datalayer/repository/project_repository.dart';
import '../../../utils/pg_log.dart';

class GenerateImportWorkspacesUnityMessageUseCase {
  final ProjectRepository _repository;

  GenerateImportWorkspacesUnityMessageUseCase(this._repository);

  Future<MessageToUnity?> invoke(String projectId) async {
    try {
      final workspace = await _repository.getWorkspaceByProjectId(projectId);
      if (workspace == null) {
        return null;
      }
      Map<String, dynamic> workspaceObj = workspace.toJson();
      if (workspaceObj.isEmpty) {
        return null;
      }
      String json = jsonEncode([workspaceObj]);
      MessageToUnity msg = MessageToUnity(
        method: 'ImportWorkspaces',
        args: json, // 使用 JSON 字符串作为参数
        completed: const Uuid().v4(),
      );
      return msg;
    } catch (e) {
      PGLog.e('generate import workspaces message use case error : $e');
      return null;
    }
  }
}
