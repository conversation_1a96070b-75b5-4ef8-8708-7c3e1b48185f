import 'dart:convert';

import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理新手引导消息的UseCase
class HandleNoviceGuideUseCase {
  final UnityController _unityController;
  final NoviceGuideManager _noviceGuideManager;

  HandleNoviceGuideUseCase(
    this._unityController,
    this._noviceGuideManager,
  );

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    final taskId = message.taskId!;
    try {
      // 从消息参数中获取步骤索引
      if (message.args == null || message.args!.isEmpty) {
        PGLog.e('新手引导消息参数为空');
        _sendErrorResponse(taskId);
        return;
      }
      // 解析 JSON 字符串
      final String jsonStr = message.args![0] as String;
      final Map<String, dynamic> data =
          json.decode(jsonStr) as Map<String, dynamic>;
      final int stepIndex = data['stepIndex'] as int;
      PGLog.d('收到新手引导请求，步骤索引: $stepIndex');

      final shouldShow = await _noviceGuideManager.shouldHandlePresetStep();
      if (!shouldShow) {
        PGLog.d('预设选择步骤不需要显示');
        _sendErrorResponse(taskId);
        return;
      }

      // 获取新手引导信息
      final guideInfo = await _noviceGuideManager.getGuideInfo(step: stepIndex);

      if (guideInfo == null) {
        PGLog.d('步骤 $stepIndex 的新手引导信息不存在或不需要显示');
        _sendErrorResponse(taskId);
        return;
      }

      // 构造返回的JSON数据(目前只有1步预设需要显示)
      final responseData = {
        'currentStepShouldShow': true,
        'totalSteps': guideInfo.totalSteps,
        'description': guideInfo.description,
        'resourceId': guideInfo.resourceId,
      };

      final responseJson = json.encode(responseData);

      // 发送回调给Unity
      _unityController.sendTriggerCallback(taskId, responseJson);

      PGLog.d('成功发送新手引导信息: $responseJson');
    } catch (e) {
      PGLog.e('处理新手引导消息失败: $e');
      _sendErrorResponse(taskId);
    }
  }

  /// 发送错误响应
  void _sendErrorResponse(String taskId) {
    final errorResponse = json.encode({
      'currentStepShouldShow': false,
      'totalSteps': 0,
      'description': '',
      'resourceId': '',
    });
    _unityController.sendTriggerCallback(taskId, errorResponse);
  }
}
