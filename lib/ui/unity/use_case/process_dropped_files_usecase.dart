import 'dart:io';

import 'package:cross_file/cross_file.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理拖拽文件的UseCase
///
/// 负责将[XFile]转换为[File]并进行基础处理
class ProcessDroppedFilesUseCase {
  ProcessDroppedFilesUseCase();

  /// 处理拖拽的XFile文件，转换为File列表
  Future<List<File>?> invoke(List<XFile> xFiles) async {
    try {
      PGLog.d('ProcessDroppedFilesUseCase: 处理${xFiles.length}个拖拽文件');
      // 转换XFile为File
      List<File> files = xFiles.map((xFile) => File(xFile.path)).toList();
      return files;
    } catch (e) {
      PGLog.d('ProcessDroppedFilesUseCase处理拖拽文件出错: $e');
      return null;
    }
  }
}
