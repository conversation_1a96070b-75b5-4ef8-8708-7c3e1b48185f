import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理获取自定义表配置本地密钥消息的UseCase
class HandleLocalKeyUseCase {
  final UnityController _unityController;
  final OpsCustomTableRepository _opsCustomTableRepository;

  HandleLocalKeyUseCase(this._unityController, this._opsCustomTableRepository);

  Future<void> invoke(MessageFromUnity message) async {
    final taskId = message.taskId!;
    try {
      final localKey = await _opsCustomTableRepository.getSampleKey();
      if (localKey == null || localKey.isEmpty) {
        throw Exception('获取本地localKey为空，请检查环境配置！');
      }
      _unityController.sendTriggerCallback(taskId, localKey);
      PGLog.d('HandleLocalKeyUseCase: 已返回本地密钥');
    } catch (e) {
      PGLog.e('HandleLocalKeyUseCase: 处理本地密钥消息失败: $e');
      _unityController.sendTriggerCallback(
          taskId, json.encode({'localKey': ''}));
    }
  }
}
