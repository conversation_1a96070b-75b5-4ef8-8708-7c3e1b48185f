import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理新手引导完成状态消息的UseCase
class HandleNoviceGuideCompletedUseCase {
  final NoviceGuideManager _noviceGuideManager;

  HandleNoviceGuideCompletedUseCase(
    this._noviceGuideManager,
  );

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    try {
      // 从消息参数中获取步骤索引和成功状态
      if (message.args == null || message.args!.length < 2) {
        PGLog.e('新手引导完成状态消息参数不足');
        return;
      }

      final int stepIndex = message.args![0] as int;
      final bool completed = message.args![1] as bool;
      final String resourceId = message.args![2] as String;

      PGLog.d(
          '收到新手引导完成状态，步骤索引: $stepIndex, 是否成功: $completed, 资源ID: $resourceId');

      // 如果成功展示，标记步骤已显示
      if (completed) {
        _noviceGuideManager.markStepShown(stepIndex);
        PGLog.d('标记新手引导步骤 $stepIndex 已显示');

        // 检查是否是最后一步，如果是则标记引导已完成
        final totalSteps = _noviceGuideManager.totalSteps;
        if (stepIndex == totalSteps - 1) {
          _noviceGuideManager.markGuideCompleted();
          PGLog.d('新手引导已全部完成');
        }
      } else {
        PGLog.d('新手引导步骤 $stepIndex 展示失败，不标记为已显示');
      }
    } catch (e) {
      PGLog.e('处理新手引导完成状态消息失败: $e');
    }
  }
}
