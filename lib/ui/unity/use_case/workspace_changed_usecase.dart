import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

enum WorkspaceChangeType {
  add(0),
  delete(1);

  final int value;
  const WorkspaceChangeType(this.value);

  static WorkspaceChangeType fromValue(int value) {
    return WorkspaceChangeType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => WorkspaceChangeType.add,
    );
  }
}

class WorkspaceChangedUseCase {
  WorkspaceChangedUseCase(this._projectRepository);
  final ProjectRepository _projectRepository;

  Future<bool> invoke(MessageFromUnity message) async {
    try {
      final List<String> args =
          (message.args ?? []).map((e) => e.toString()).toList();
      final nowTime = DateTime.now().millisecondsSinceEpoch;
      PGLog.d('OnSyncWorkspace: $nowTime $args');

      if (args.isEmpty) {
        return false;
      }

      // 解析工作区变更信息
      final changeJson = jsonDecode(args[0]) as Map<String, dynamic>;
      final workspaceId = changeJson['workspaceId'] as String;

      // 强制从数据库同步项目数据
      final project = await _projectRepository.syncProject(workspaceId);
      if (project == null) {
        PGLog.e('OnSyncWorkspace: 未找到对应的项目信息: $workspaceId');
        return false;
      }

      final endTime = DateTime.now().millisecondsSinceEpoch;
      PGLog.d('OnSyncWorkspace: 工作区数据同步成功: ${endTime - nowTime}ms');
      PGLog.d('OnSyncWorkspace: 更新项目信息结束时间: $endTime');
      return true;
    } catch (e) {
      PGLog.e('OnSyncWorkspace error: $e');
      return false;
    }
  }
}
