import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/ui/setting/provider/current_cache_rule_provider.dart';

class HandleProjectCacheRuleUseCase {
  final ProjectCacheRuleProvider _projectCacheRuleProvider;

  HandleProjectCacheRuleUseCase(
    this._projectCacheRuleProvider,
  );

  /// 执行处理
  void invoke(MessageFromUnity message) {
    _projectCacheRuleProvider.onProjectCacheRuleUpdate(message);
  }
}
