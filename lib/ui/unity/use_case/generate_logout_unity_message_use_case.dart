import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:uuid/uuid.dart';

class GenerateLogoutUnityMessageUseCase {
  final CurrentUserRepository _currentUserRepository;

  GenerateLogoutUnityMessageUseCase(this._currentUserRepository);

  MessageToUnity? invoke() {
    if (!_currentUserRepository.isLoggedIn) {
      return null;
    }
    return MessageToUnity(
      method: 'Logout',
      args: "",
      completed: const Uuid().v4(),
    );
  }
}
