import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

/// 生成从历史记录导入预设的Unity消息UseCase
class GenerateImportPresetFromHistoryUnityMessageUseCase {
  GenerateImportPresetFromHistoryUnityMessageUseCase();

  /// 执行生成消息
  /// [projectId] 项目ID
  /// [fileIdToHistoryIdMap] fileId到historyId的映射
  Future<MessageToUnity?> invoke(
      String projectId, Map<String, String> fileIdToHistoryIdMap) async {
    try {
      if (fileIdToHistoryIdMap.isEmpty) {
        PGLog.w('fileIdToHistoryIdMap is empty, skip ImportPresetFromHistory');
        return null;
      }

      // 构建包含projectId和映射的JSON结构
      final jsonData = {
        'projectId': projectId,
        'fileIdToHistoryIdMap': fileIdToHistoryIdMap,
      };

      // 将数据转换为JSON字符串
      String json = jsonEncode(jsonData);

      MessageToUnity msg = MessageToUnity(
        method: 'ImportPresetFromHistory',
        args: json, // 使用 JSON 字符串作为参数
        completed: const Uuid().v4(),
      );

      PGLog.d('ImportPresetFromHistory message generated: $json');
      return msg;
    } catch (e) {
      PGLog.e('generate import preset from history message use case error: $e');
      return null;
    }
  }
}
