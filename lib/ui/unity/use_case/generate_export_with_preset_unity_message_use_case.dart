import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/domain/models/preset/preset_item.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class GenerateExportWithPresetUnityMessageUseCase {
  final ProjectRepository _repository;

  GenerateExportWithPresetUnityMessageUseCase(
    this._repository,
  );

  Future<MessageToUnity?> invoke(
    List<String> projectIds,
    PresetItem preset,
    String exportPath,
  ) async {
    try {
      final workspaces =
          await _repository.getWorkspacesByProjectIds(projectIds);
      if (workspaces.isEmpty) {
        return null;
      }

      final exportConfig = ExportConfig.defaultConfig().copyWith(
        outputFolder: exportPath,
        pathType: exportPath.isEmpty
            ? ExportPathType.originalFolder
            : ExportPathType.specifyFolder,
        isReplace: false,
        exportFileType: ExportFileType.original,
      );

      final args = {
        'preset': preset.toJson(),
        'workspaces': workspaces.map((e) => e.toJson()).toList(),
        'exportConfig': exportConfig.toJson(),
      };
      String json = jsonEncode(args);

      MessageToUnity msg = MessageToUnity(
        method: 'ExportWithPreset',
        args: json, // 使用 JSON 字符串作为参数
        completed: const Uuid().v4(),
      );
      return msg;
    } catch (e) {
      PGLog.e('generate import workspaces message use case error : $e');
      return null;
    }
  }
}
