import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/core/service/image_selection_service/image_selection_service.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'import_images_to_unity_usecase.dart';

/// 处理打开图片选择器消息的UseCase
class HandleOpenImagePickerUseCase {
  final ImageSelectionService _imageSelectionService;
  final ImportImagesToUnityUseCase _importImagesToUnityUseCase;
  final UnityController _unityController;

  HandleOpenImagePickerUseCase(
    this._imageSelectionService,
    this._unityController,
    this._importImagesToUnityUseCase,
  );

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    final taskId = message.taskId!;

    if (message.args?.isEmpty == true) {
      _unityController.sendTriggerCallback(
        taskId,
        json.encode(
          {'files': []},
        ),
      );
      return;
    }

    try {
      final jsonStr = message.args?[0];
      if (jsonStr == null) {
        PGLog.e('编辑页中请求打开导入图片参数为空');
        _unityController.sendTriggerCallback(
          taskId,
          json.encode(
            {'files': []},
          ),
        );
        return;
      }
      final Map<String, dynamic> jsonMap = json.decode(jsonStr);
      final type = jsonMap['type'] as String?;

      DealImageFilesResult? selectedResult;
      switch (type) {
        case 'files':
          // 从相册选择图片
          final dealImage =
              await _imageSelectionService.pickImagesFromGallery();
          selectedResult = dealImage;
          break;
        case 'folder':
          // 从文件夹选择图片

          final dealImage =
              await _imageSelectionService.pickImagesFromDirectory();
          selectedResult = dealImage;
          break;
        default:
          PGLog.e('编辑页中请求打开导入图片参数错误，args = ${message.args}');
          return;
      }

      // 如果用户选择了文件，则调用回调函数处理这些文件
      if (selectedResult == null || selectedResult.validFiles.isEmpty) {
        PGLog.d('用户未选择文件或取消了选择');
        // 因为在unity侧该消息为挂起，所以一定要回调，否则Unity侧会一直等待
        _unityController.sendTriggerCallback(
          taskId,
          json.encode(
            {'files': []},
          ),
        );
      } else {
        PGLog.d('用户选择了 ${selectedResult.validFiles.length} 个文件');
        final messageToUnity = await _importImagesToUnityUseCase.invoke(
          selectedResult,
          "",
        );
        _unityController.sendTriggerCallback(
          taskId,
          messageToUnity?.args ?? '',
        );
      }
    } catch (e) {
      PGLog.e('处理打开图片选择器消息时出错: $e');
    }
  }
}
