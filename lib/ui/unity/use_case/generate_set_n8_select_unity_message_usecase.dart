import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

/// 生成设置N8选版结果的Unity消息
class GenerateSetN8SelectUnityMessageUseCase {
  GenerateSetN8SelectUnityMessageUseCase();

  /// 生成设置N8选版结果的Unity消息
  ///
  /// [workspaceId] 工作区ID
  /// [fileIdValues] 文件ID与选版结果的映射，key为fileId，value为选版结果
  ///
  /// 返回MessageToUnity消息
  Future<MessageToUnity?> invoke(
      String workspaceId, Map<String, bool> fileIdValues) async {
    try {
      final jsonParams = jsonEncode({
        'workspaceId': workspaceId,
        'fileIdValues': fileIdValues,
      });

      final message = MessageToUnity(
        method: 'SetN8Select',
        args: json<PERSON>arams,
        completed: const Uuid().v4(),
      );

      return message;
    } catch (e) {
      PGLog.e('generate set n8 select message use case error : $e');
      return null;
    }
  }
}
