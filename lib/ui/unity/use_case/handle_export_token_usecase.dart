import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export/export_callback_result.dart';
import 'package:turing_art/datalayer/domain/models/export/export_models.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/repository/export_exception.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ui/common/global_vars.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 处理获取导出Token消息的UseCase
class HandleExportTokenUseCase {
  final UnityController _unityController;
  final ExportUseCaseProvider _exportUseCaseProvider;
  final ProjectRepository _projectRepository;

  HandleExportTokenUseCase(
    this._unityController,
    this._exportUseCaseProvider,
    this._projectRepository,
  );

  /// 执行处理
  Future<void> invoke(MessageFromUnity message) async {
    if (message.args == null || message.args!.isEmpty) {
      return;
    }

    final taskId = message.taskId!;
    try {
      // 解析 JSON 字符串
      final String jsonStr = message.args![0] as String;
      final Map<String, dynamic> imageData =
          json.decode(jsonStr) as Map<String, dynamic>;
      final imagePath = imageData['path'] as String;
      final imageHash = imageData['hash'] as String;
      final workspaceId = imageData['workspaceId'] as String;
      final exportTypeStr = imageData['exportType'] as String?;
      ExportType exportType = ExportType.retouch;
      if (exportTypeStr == 'sample') {
        exportType = ExportType.sample;
      }

      // 获取项目信息
      final project = await _projectRepository.getProjectById(workspaceId);
      if (project == null) {
        throw Exception('项目不存在: $workspaceId');
      }

      final token = await _exportUseCaseProvider.getExportToken.invoke(
        imageHash,
        imagePath,
        project.name,
        workspaceId,
        exportType: exportType,
      );

      // 构造成功回调结果
      final successResult =
          ExportTokenCallbackResult.success(tokenId: token.tokenId);
      final resultJson = json.encode(successResult.toJson());
      _unityController.sendTriggerCallback(taskId, resultJson);
    } catch (e) {
      PGLog.e('处理导出Token请求失败: $e');

      // 构造失败回调结果
      ExportTokenCallbackResult failureResult;
      if (e is ExportException) {
        failureResult = ExportTokenCallbackResult.failure(
          errorCode: e.code ?? -1,
          errorMessage: e.message,
        );
        // 统一修改为客户端进行详细错误提示
        if (GlobalVars.isWinEditScreenActive) {
          PGDialog.showToastOnUnity(e.message, isImportant: true);
        } else {
          PGDialog.showToast(e.message);
        }
      } else {
        failureResult = ExportTokenCallbackResult.failure(
          errorCode: -1,
          errorMessage: e.toString(),
        );
      }

      final resultJson = json.encode(failureResult.toJson());
      _unityController.sendTriggerCallback(taskId, resultJson);
    }
  }
}
