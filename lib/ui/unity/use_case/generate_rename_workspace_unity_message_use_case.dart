import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

// 工作区更名
class GenerateRenameWorkspaceUnityMessageUseCase {
  GenerateRenameWorkspaceUnityMessageUseCase(this._repository);
  final ProjectRepository _repository;

  Future<MessageToUnity?> invoke(String projectId) async {
    try {
      if (projectId.isEmpty) {
        return null;
      }
      final projectInfo = await _repository.getProjectById(projectId);
      if (projectInfo == null) {
        return null;
      }
      final msg = MessageToUnity(
        method: 'ModifyWorkspace',
        args: jsonEncode({
          "workspaceId": projectInfo.uuid,
          "workspaceName": projectInfo.name
        }),
        completed: const Uuid().v4(),
      );
      return msg;
    } catch (e) {
      PGLog.e('generate rename workspace message use case error : $e');
      return null;
    }
  }
}
