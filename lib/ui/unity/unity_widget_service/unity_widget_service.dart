import '../../../utils/app_constants.dart';
import '../../../widgets/unity/unity_widget_interface.dart';
import '../../../widgets/unity/universal_unity_widget.dart';
import '../../../widgets/unity/windows_unity_widget.dart';

class UnityWidgetService {
  static UnityWidgetInterface createUnityWidget({
    Function()? onUnityCreated,
    Function(dynamic)? onUnityMessage,
  }) {
    return AppConstants.isDesktop
        ? WindowsUnityWidget(
            onUnityCreated: onUnityCreated,
            onUnityMessage: onUnityMessage,
          )
        : UniversalUnityWidget(
            onUnityCreated: onUnityCreated,
            onUnityMessage: onUnityMessage,
          );
  }
}
