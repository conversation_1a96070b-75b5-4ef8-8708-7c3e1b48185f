import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

class SaveResolutionUseCase {
  final SettingRepository _settingRepository;
  final UnityUseCaseProvider _unityUseCaseProvider;
  final UnityController _unityController;

  SaveResolutionUseCase({
    required SettingRepository settingRepository,
    required UnityUseCaseProvider unityUseCaseProvider,
    required UnityController unityController,
  })  : _settingRepository = settingRepository,
        _unityUseCaseProvider = unityUseCaseProvider,
        _unityController = unityController;

  // 保存设置
  Future<void> invoke(SettingConfig config) async {
    try {
      // 在后台执行IO操作
      _settingRepository.saveSettingConfig(config);
      // 在主线程执行Unity相关操作
      await _refreshCurrentProjectResolutionSetting(config);
    } catch (e) {
      PGLog.e('Error saving settings: $e');
      rethrow;
    }
  }

  //刷新当前工程的分辨率设置
  Future<void> _refreshCurrentProjectResolutionSetting(
    SettingConfig config,
  ) async {
    try {
      // 找到预览设置类别中的预览尺寸设置项
      final previewSizeItem = config.categories
          .firstWhere(
            (category) => category.key == SettingCategoryConstant.preview,
          )
          .items
          .firstWhere(
            (item) => item.key == SettingKeyConstant.previewSize,
          );

      // 找到当前选中的选项
      final selectedChoice = previewSizeItem.choices.firstWhere(
        (choice) => choice.isSelected,
        orElse: () => previewSizeItem.choices.first,
      );

      // 获取当前选中的预览尺寸
      final previewSize = int.tryParse(selectedChoice.value);
      if (previewSize == null) {
        return;
      }

      // 创建并发送消息到Unity
      final message = _unityUseCaseProvider
          .createSetupResolutionHandler()
          .invoke(previewSize);
      await _unityController.sendMessage(message);
    } catch (e) {
      PGLog.e('Error refreshing current project resolution setting: $e');
    }
  }
}
