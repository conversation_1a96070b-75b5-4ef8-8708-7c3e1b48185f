import 'package:turing_art/datalayer/domain/models/device/device_performance_level.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_config.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/ui/setting/provider/current_device_information_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

class FetchSettingsUseCase {
  final SettingRepository _settingRepository;
  final CurrentDeviceInformationProvider _deviceInformationProvider;

  // 性能等级到推荐分辨率的映射
  static const Map<DevicePerformanceLevel, Resolution> _resolutionMap = {
    DevicePerformanceLevel.veryLow: Resolution.low,
    DevicePerformanceLevel.low: Resolution.medium,
    DevicePerformanceLevel.medium: Resolution.medium,
    DevicePerformanceLevel.high: Resolution.high,
    DevicePerformanceLevel.veryHigh: Resolution.high,
  };

  FetchSettingsUseCase({
    required SettingRepository settingRepository,
    required CurrentDeviceInformationProvider deviceInformationProvider,
  })  : _settingRepository = settingRepository,
        _deviceInformationProvider = deviceInformationProvider;

  Future<SettingConfig> invoke() async {
    final config = await _settingRepository.loadSettingConfig();
    return updateResolutionRecommendations(config);
  }

  // 更新分辨率推荐状态
  SettingConfig updateResolutionRecommendations(SettingConfig config) {
    PGLog.i(
        'Starting resolution recommendations update - Device level: ${_deviceInformationProvider.deviceInformation.deviceLevel}, Recommended resolution: ${_getRecommendedResolution()}');

    final updatedCategories = config.categories.map((cat) {
      if (cat.key == SettingCategoryConstant.preview) {
        final updatedItems = cat.items.map((item) {
          if (item.key == SettingKeyConstant.previewSize) {
            // 检查是否有已选中的选项
            final hasSelectedChoice =
                item.choices.any((choice) => choice.isSelected);

            // 更新选项的选中状态和标题
            final updatedChoices = item.choices.map((choice) {
              final choiceValue =
                  int.tryParse(choice.value) ?? Resolution.medium.value;
              final isChoiceRecommended = _isRecommendedResolution(choiceValue);
              PGLog.i(
                  'Preview size option - Value: $choiceValue, Recommended: $isChoiceRecommended, Selected: ${choice.isSelected}');

              // 如果已有选中项，保持其选中状态；否则，将推荐项设为选中
              final shouldBeSelected = hasSelectedChoice
                  ? choice.isSelected // 保持原有选中状态
                  : isChoiceRecommended; // 如果没有选中项，则选中推荐项

              return choice.copyWith(
                isSelected: shouldBeSelected,
                isRecommended: isChoiceRecommended,
              );
            }).toList();

            return item.copyWith(
              choices: updatedChoices,
            );
          }
          return item;
        }).toList();
        return cat.copyWith(items: updatedItems);
      }
      return cat;
    }).toList();

    return config.copyWith(categories: updatedCategories);
  }

  // 获取推荐分辨率
  int _getRecommendedResolution() {
    return _resolutionMap[
                _deviceInformationProvider.deviceInformation.deviceLevel]
            ?.value ??
        Resolution.medium.value;
  }

  // 判断分辨率是否为推荐值
  bool _isRecommendedResolution(int resolution) {
    return resolution == _getRecommendedResolution();
  }
}
