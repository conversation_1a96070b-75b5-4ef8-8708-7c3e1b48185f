import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/domain/models/device/device_information.dart';
import 'package:turing_art/datalayer/domain/models/device/device_performance_level.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/utils/pg_log.dart';

class CurrentDeviceInformationProvider extends ChangeNotifier {
  DeviceInformation _deviceInformation = const DeviceInformation(
    deviceLevel: DevicePerformanceLevel.low,
  );

  DeviceInformation get deviceInformation => _deviceInformation;

  void onCurrentDeviceInformationUpdate(MessageFromUnity message) {
    try {
      // 解析 JSON 字符串
      final String jsonStr = message.args![0] as String;
      final Map<String, dynamic> data =
          json.decode(jsonStr) as Map<String, dynamic>;
      final level = data['deviceLevel'] as int;
      final deviceLevel = DevicePerformanceLevel.fromValue(level);
      // data 直接format至 DeviceInformation
      final temp = DeviceInformation(deviceLevel: deviceLevel);
      if (temp.deviceLevel != _deviceInformation.deviceLevel) {
        _deviceInformation = temp;
        notifyListeners();
      }
    } catch (e) {
      PGLog.e(
          'CurrentDeviceInformationProvider.onCurrentDeviceInformationUpdate: $e');
    }
  }
}
