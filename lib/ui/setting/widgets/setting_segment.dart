import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class SettingSegment extends StatefulWidget {
  final List<String> segments;
  final Function(int) onSegmentSelected;
  final int initialSelection;
  final Color backgroundColor;
  final Color selectedBackgroundColor;
  final Color textColor;
  final Color selectedTextColor;
  final double height;
  final double borderRadius;
  final FontWeight fontWeight;
  final FontWeight selectedFontWeight;
  final double fontSize;

  const SettingSegment({
    super.key,
    required this.segments,
    required this.onSegmentSelected,
    this.initialSelection = 0,
    this.backgroundColor = const Color(0xFF1A1D1E),
    this.selectedBackgroundColor = const Color(0xFF222526),
    this.textColor = const Color(0xFF8E8E8E),
    this.selectedTextColor = Colors.white,
    this.height = 36,
    this.borderRadius = 8,
    this.fontWeight = Fonts.regular,
    this.selectedFontWeight = Fonts.medium,
    this.fontSize = 14,
  });

  @override
  State<SettingSegment> createState() => _SettingSegmentState();
}

class _SettingSegmentState extends State<SettingSegment> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialSelection;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: 390,
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final segmentWidth = constraints.maxWidth / widget.segments.length;
          return Stack(
            children: [
              AnimatedPositioned(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                left: _selectedIndex * segmentWidth,
                child: Container(
                  width: segmentWidth,
                  height: widget.height - 4,
                  decoration: BoxDecoration(
                    color: widget.selectedBackgroundColor,
                    borderRadius:
                        BorderRadius.circular(widget.borderRadius - 2),
                  ),
                ),
              ),
              Row(
                children: List.generate(
                  widget.segments.length,
                  (index) => GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      setState(() {
                        _selectedIndex = index;
                      });
                      widget.onSegmentSelected(index);
                    },
                    child: Container(
                      width: segmentWidth,
                      height: widget.height - 4,
                      alignment: Alignment.center,
                      child: AnimatedDefaultTextStyle(
                        duration: const Duration(milliseconds: 200),
                        style: TextStyle(
                          color: _selectedIndex == index
                              ? widget.selectedTextColor
                              : widget.textColor,
                          fontSize: widget.fontSize,
                          fontWeight: _selectedIndex == index
                              ? widget.selectedFontWeight
                              : widget.fontWeight,
                          fontFamily: Fonts.defaultFontFamily,
                        ),
                        child: Text(
                          widget.segments[index],
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
