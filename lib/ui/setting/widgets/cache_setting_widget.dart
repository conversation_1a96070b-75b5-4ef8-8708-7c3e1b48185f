import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_category.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/setting/view_models/setting_view_model.dart';
import 'package:turing_art/ui/setting/widgets/setting_dropdown_widget.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 缓存设置组件
class CacheSettingWidget extends StatefulWidget {
  final Function(SettingItemModel) onItemChanged;

  const CacheSettingWidget({
    super.key,
    required this.onItemChanged,
  });

  @override
  State<CacheSettingWidget> createState() => _CacheSettingWidgetState();
}

class _CacheSettingWidgetState extends State<CacheSettingWidget> {
  //late FolderPickerService _folderPickerService;
  TextEditingController? _cacheSizeController;
  FocusNode? _cacheSizeFocusNode;
  SettingCategory? _category;
  SettingItemModel? _cacheDaysItem;
  SettingItemModel? _autoCleanSizeItem;
  SettingItemModel? _cachePathItem;
  Future<String>? _cacheSizeFuture;

  @override
  void initState() {
    super.initState();
    //_folderPickerService = FolderPickerService.forPlatform();
    _cacheSizeFocusNode = FocusNode();
    _cacheSizeFocusNode!.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    if (!_cacheSizeFocusNode!.hasFocus) {
      // 失去焦点时验证输入
      _validateAndUpdateCacheSize(_cacheSizeController?.text ?? '');
    }
  }

  void _initializeData(SettingViewModel viewModel) {
    if (_category != null &&
        _cacheDaysItem != null &&
        _autoCleanSizeItem != null &&
        _cachePathItem != null &&
        _cacheSizeFuture != null) {
      return;
    }
    _category = viewModel.getCategory(SettingCategoryConstant.cache);

    _cacheDaysItem = viewModel.getSettingItem(
      SettingCategoryConstant.cache,
      SettingKeyConstant.cacheDays,
    );

    _autoCleanSizeItem = viewModel.getSettingItem(
      SettingCategoryConstant.cache,
      SettingKeyConstant.autoCleanSize,
    );

    _cachePathItem = viewModel.getSettingItem(
      SettingCategoryConstant.cache,
      SettingKeyConstant.cachePath,
    );

    _cacheSizeController ??=
        TextEditingController(text: _autoCleanSizeItem!.value);

    // 只在第一次初始化时创建缓存大小Future
    _cacheSizeFuture ??= viewModel.getCurrentCacheSize();
  }

  void _onSelectionChanged(int index, SettingViewModel viewModel) {
    if (_cacheDaysItem == null ||
        index < 0 ||
        index >= _cacheDaysItem!.choices.length) {
      return;
    }

    final selectedValue = _cacheDaysItem!.choices[index].value;
    viewModel.updateChoiceSelection(
      SettingCategoryConstant.cache,
      _cacheDaysItem!.key,
      selectedValue,
    );
  }

  @override
  void dispose() {
    _cacheSizeController?.dispose();
    _cacheSizeFocusNode?.removeListener(_onFocusChange);
    _cacheSizeFocusNode?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 434),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 缓存管理区域（release-1.8.0 隐藏）
          // _buildCacheManagementSection(),
          // const SizedBox(height: 16),
          // 缓存大小区域
          _buildCacheSizeSection(),
          const SizedBox(height: 16),
          // 更改缓存路径区域
          _buildCachePathSection(),
        ],
      ),
    );
  }

  /// 构建缓存管理区域
  Widget _buildCacheManagementSection() {
    return SizedBox(
      width: 434,
      height: 104,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              const SizedBox(height: 8),
              // 标题
              SizedBox(
                width: 58,
                height: 32,
                child: Text(
                  '缓存管理',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 14,
                    height: 16 / 14,
                    color: Colors.white.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
            ],
          ),
          const SizedBox(width: 42),
          // 设置区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: 缓存天数
                _buildCacheDaysRow(),
                const SizedBox(height: 16),
                // Row 2: 自动清理缓存
                _buildAutoCleanRow(),
                const SizedBox(height: 8),
                // Row 3: 提示文字
                _buildHintText(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建缓存天数行
  Widget _buildCacheDaysRow() {
    return SizedBox(
      height: 32,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 58,
            height: 18,
            child: Text(
              '缓存天数',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
                fontSize: 14,
                height: 18 / 14,
                color: Colors.white,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          const SizedBox(width: 6),
          Consumer<SettingViewModel>(
            builder: (context, viewModel, child) {
              _initializeData(viewModel);
              if (_cacheDaysItem == null) {
                return const SizedBox.shrink();
              }
              final selectedIndex =
                  viewModel.getSelectedIndexByItem(_cacheDaysItem!);
              return SettingDropdownWidget(
                choices: _cacheDaysItem!.choices,
                selectedIndex: selectedIndex,
                onSelectionChanged: (index) =>
                    _onSelectionChanged(index, viewModel),
                width: 150,
                height: 32,
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建自动清理缓存行
  Widget _buildAutoCleanRow() {
    return SizedBox(
      height: 32,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '自动清理缓存 ≥',
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.medium,
              fontSize: 14,
              height: 18 / 14,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          // 输入框
          Container(
            width: 75,
            height: 32,
            padding: const EdgeInsets.only(left: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF2B2B2B),
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                color: const Color(0x1AFFFFFF),
                width: 1,
              ),
            ),
            child: Consumer<SettingViewModel>(
              builder: (context, viewModel, child) {
                _initializeData(viewModel);
                if (_autoCleanSizeItem == null ||
                    _cacheSizeController == null) {
                  return const SizedBox.shrink();
                }
                return Center(
                  child: TextField(
                    controller: _cacheSizeController,
                    focusNode: _cacheSizeFocusNode,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                      fontSize: 12,
                      color: Colors.white,
                    ),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    onChanged: (value) {
                      // 不需要实时调用onItemChanged存储数据，也不需要实时验证
                    },
                    onSubmitted: (value) {
                      _validateAndUpdateCacheSize(value);
                    },
                    onEditingComplete: () {
                      _validateAndUpdateCacheSize(_cacheSizeController!.text);
                    },
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 8),
          // 单位
          SizedBox(
            height: 18,
            child: Text(
              'GB',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
                fontSize: 14,
                height: 18 / 14,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建提示文字
  Widget _buildHintText() {
    return SizedBox(
      width: 286,
      height: 16,
      child: Text(
        '当缓存大于等于所填数字，将自动清理超出的缓存。',
        style: TextStyle(
          fontFamily: Fonts.defaultFontFamily,
          fontWeight: Fonts.regular,
          fontSize: 12,
          height: 16 / 12,
          color: const Color(0x80FFFFFF),
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  /// 构建缓存大小区域
  Widget _buildCacheSizeSection() {
    return SizedBox(
      width: 402,
      height: 64,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 58,
            height: 16,
            child: Text(
              '缓存大小',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
                fontSize: 14,
                height: 16 / 14,
                color: Colors.white.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 42),
          // 大小显示
          Consumer<SettingViewModel>(
            builder: (context, viewModel, child) {
              _initializeData(viewModel);

              return FutureBuilder<String>(
                future: _cacheSizeFuture,
                builder: (context, snapshot) {
                  // 如果正在清理缓存或正在获取缓存大小，显示loading
                  if (viewModel.isClearingCache ||
                      snapshot.connectionState == ConnectionState.waiting) {
                    return SizedBox(
                      height: 18,
                      width: 18,
                      child: Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white.withOpacity(0.7)),
                        ),
                      ),
                    );
                  } else if (snapshot.hasError) {
                    return SizedBox(
                      height: 18,
                      child: Text(
                        '错误',
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.medium,
                          fontSize: 14,
                          height: 18 / 14,
                          color: Colors.red,
                        ),
                      ),
                    );
                  } else {
                    return SizedBox(
                      height: 18,
                      child: Text(
                        snapshot.data ?? '0.00 MB',
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.medium,
                          fontSize: 14,
                          height: 18 / 14,
                          color: Colors.white,
                        ),
                      ),
                    );
                  }
                },
              );
            },
          ),
          const SizedBox(width: 30),
          // 清理缓存按钮
          _buildClearCacheButton(),
        ],
      ),
    );
  }

  /// 构建清理缓存按钮
  Widget _buildClearCacheButton() {
    return Consumer<SettingViewModel>(
      builder: (context, viewModel, child) {
        final isClearing = viewModel.isClearingCache;
        final isGettingCacheSize = viewModel.isGettingCacheSize;

        return FutureBuilder<String>(
          future: _cacheSizeFuture,
          builder: (context, snapshot) {
            final isDisabled = isClearing || isGettingCacheSize;

            return InkWell(
              onTap: isDisabled ? null : _handleClearCache,
              child: Container(
                width: 86,
                height: 32,
                padding: const EdgeInsets.only(
                  top: 7,
                  bottom: 7,
                  left: 8,
                  right: 10,
                ),
                decoration: BoxDecoration(
                  color: isDisabled
                      ? const Color(0xFF2B2B2B).withOpacity(0.5)
                      : const Color(0xFF2B2B2B),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/icons/setting_cache_clear.png',
                      width: 16,
                      height: 16,
                    ),
                    const SizedBox(width: 2),
                    // 清理缓存文字
                    Text(
                      isClearing ? '清理中...' : '清理缓存',
                      style: TextStyle(
                        fontFamily: 'PingFang SC',
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        height: 18 / 12,
                        color: isDisabled
                            ? Colors.white.withOpacity(0.5)
                            : Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 构建更改缓存路径区域
  Widget _buildCachePathSection() {
    /*
    return SizedBox(
      height: 32,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '更改缓存路径',
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              fontSize: 14,
              height: 16 / 14,
              color: Colors.white.withOpacity(0.7),
            ),
          ),
          const SizedBox(width: 16),
          // 路径按钮
          Expanded(
            child: Consumer<SettingViewModel>(
              builder: (context, viewModel, child) {
                _initializeData(viewModel);
                final displayPath =
                    _cachePathItem?.value ?? '我的电脑/下载/图灵精修_联机拍摄';
                return InkWell(
                  onTap: () => _cachePathItem != null
                      ? _handleChangePathClick(_cachePathItem!)
                      : null,
                  child: Container(
                    height: 32,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2B2B2B),
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(
                        color: const Color(0x1AFFFFFF),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            displayPath,
                            style: TextStyle(
                              fontFamily: Fonts.defaultFontFamily,
                              fontWeight: Fonts.regular,
                              fontSize: 12,
                              height: 16 / 12,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.left,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        // 文件查看按钮
                        Image.asset(
                          'assets/icons/aigc_sample_detail_check_path.png',
                          width: 20,
                          height: 20,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
    */
    return const SizedBox.shrink();
  }

  /// 处理清理缓存点击
  void _handleClearCache() {
    PGLog.d('清理缓存点击');
    final viewModel = context.read<SettingViewModel>();
    viewModel.clearCache().then((success) {
      if (success) {
        PGDialog.showToast('清理缓存操作成功');
        // 清理缓存完成后，刷新缓存大小
        if (mounted) {
          setState(() {
            _cacheSizeFuture = viewModel.getCurrentCacheSize();
          });
        }
      } else {
        PGDialog.showToast('清理缓存操作失败');
      }
    });
  }

  /// 处理更改路径点击
  /*
  void _handleChangePathClick(SettingItemModel item) async {
    try {
      final selectedDirectory = await _folderPickerService.pickFolder(
        dialogTitle: '选择缓存路径',
        initialDirectory: item.value.isNotEmpty ? item.value : null,
        lockParentWindow: true,
      );

      if (selectedDirectory != null) {
        final updatedItem = item.copyWith(value: selectedDirectory);
        widget.onItemChanged(updatedItem);
      }
    } catch (e) {
      PGLog.e('选择缓存路径失败: $e');
    }
  }
*/
  /// 验证并更新缓存大小
  void _validateAndUpdateCacheSize(String value) {
    if (value.isEmpty) {
      return;
    }

    final intValue = int.tryParse(value);
    if (intValue == null) {
      // 如果不是数字，恢复到默认值
      _cacheSizeController?.text = CacheSizeConstant.minSize.toString();
      return;
    }

    // 去掉前导零，直接使用解析后的数字转换回字符串
    String validatedValue = intValue.toString();

    // 限制在5-200之间
    if (intValue < CacheSizeConstant.minSize) {
      validatedValue = CacheSizeConstant.minSize.toString();
    } else if (intValue > CacheSizeConstant.maxSize) {
      validatedValue = CacheSizeConstant.maxSize.toString();
    }

    if (validatedValue != value) {
      _cacheSizeController?.text = validatedValue;
      _cacheSizeController?.selection = TextSelection.collapsed(
        offset: validatedValue.length,
      );
    }

    // 更新设置
    final updatedItem = _autoCleanSizeItem!.copyWith(value: validatedValue);
    widget.onItemChanged(updatedItem);
  }
}
