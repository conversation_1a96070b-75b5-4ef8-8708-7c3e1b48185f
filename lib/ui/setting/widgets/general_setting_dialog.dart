import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/service/disk_cache_manager/disk_cache_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_category.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/setting/provider/current_device_information_provider.dart';
import 'package:turing_art/ui/setting/use_case/fetch_settings_use_case.dart';
import 'package:turing_art/ui/setting/use_case/save_resolution_use_case.dart';
import 'package:turing_art/ui/setting/use_case/save_setting_use_case.dart';
import 'package:turing_art/ui/setting/view_models/setting_view_model.dart';
import 'package:turing_art/ui/setting/widgets/cache_setting_widget.dart';
import 'package:turing_art/ui/setting/widgets/radio_setting_widget.dart';
import 'package:turing_art/ui/setting/widgets/resolution_setting_widget.dart';
import 'package:turing_art/ui/setting/widgets/toggle_setting_widget.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class GeneralSettingDialog extends StatefulWidget {
  const GeneralSettingDialog({
    super.key,
  });

  static bool isShowingInUnityDialog = false;

  static Future<void> showOnUnity() async {
    if (PGDialog.isDialogVisible(DialogTags.generalSetting)) {
      PGLog.d(
          'GeneralSettingDialog showOnUnity, but dialog already exist, return');
      return;
    }
    isShowingInUnityDialog = true;
    await PGDialog.showCustomDialogOnUnity(
      width: 620,
      height: 500,
      needBlur: false,
      tag: DialogTags.generalSetting,
      child: _buildDialog(),
    );
  }

  static void show() {
    isShowingInUnityDialog = false;
    PGDialog.showCustomDialog(
      width: 620,
      height: 500,
      needBlur: false,
      tag: DialogTags.generalSetting,
      child: _buildDialog(),
    );
  }

  static Widget _buildDialog() {
    return MultiProvider(
      providers: [
        Provider(
          create: (context) {
            return SaveSettingUseCase(
              getCurrentProjectId: () =>
                  context.read<ProjectStateProvider>().currentProjectId,
              settingRepository: context.read<SettingRepository>(),
              projectRepository: context.read<ProjectRepository>(),
              unityUseCaseProvider: context.read<UnityUseCaseProvider>(),
              unityController: context.read<UnityController>(),
              diskCacheManager: context.read<DiskCacheManager>(),
            );
          },
        ),
        Provider(
          create: (context) {
            return FetchSettingsUseCase(
              settingRepository: context.read<SettingRepository>(),
              deviceInformationProvider:
                  context.read<CurrentDeviceInformationProvider>(),
            );
          },
        ),
        Provider(
          create: (context) {
            return SaveResolutionUseCase(
              settingRepository: context.read<SettingRepository>(),
              unityUseCaseProvider: context.read<UnityUseCaseProvider>(),
              unityController: context.read<UnityController>(),
            );
          },
        ),
        ChangeNotifierProvider(
          create: (context) => SettingViewModel(
            context.read<FetchSettingsUseCase>(),
            context.read<SaveSettingUseCase>(),
            context.read<SaveResolutionUseCase>(),
            context.read<DiskCacheManager>(),
          ),
        ),
      ],
      child: const GeneralSettingDialog(),
    );
  }

  @override
  State<GeneralSettingDialog> createState() => _GeneralSettingDialogState();
}

class _GeneralSettingDialogState extends State<GeneralSettingDialog> {
  ValueNotifier<SettingCategory>? _selectedCategoryNotifier;

  @override
  void initState() {
    super.initState();
    // 延迟初始化，等待 ViewModel 加载完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSelectedCategory();
    });
  }

  void _initializeSelectedCategory() {
    final viewModel = context.read<SettingViewModel>();
    if (!viewModel.isLoading && viewModel.categories.isNotEmpty) {
      _selectedCategoryNotifier ??= ValueNotifier(viewModel.categories.first);
    }
  }

  @override
  void dispose() {
    _selectedCategoryNotifier?.dispose();
    super.dispose();
  }

  void _selectCategory(SettingCategory category) {
    _selectedCategoryNotifier?.value = category;
  }

  Widget _buildCategoryItem({
    required String title,
    required SettingCategory category,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => _selectCategory(category),
      child: Container(
        width: 170,
        height: 36,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2B2B2B) : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            title,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
              height: 20 / 14,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentArea(SettingViewModel viewModel) {
    // 如果数据还在加载中，显示空内容
    if (viewModel.isLoading) {
      return const SizedBox.shrink();
    }

    // 确保在数据加载完成后初始化默认选中的分类
    if (_selectedCategoryNotifier == null && viewModel.categories.isNotEmpty) {
      _selectedCategoryNotifier = ValueNotifier(viewModel.categories.first);
    }

    // 如果 _selectedCategoryNotifier 还未初始化，返回空内容
    if (_selectedCategoryNotifier == null) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder<SettingCategory>(
      valueListenable: _selectedCategoryNotifier!,
      builder: (context, selectedCategory, child) {
        // 根据分类 key 来决定显示哪个组件
        switch (selectedCategory.key) {
          case SettingCategoryConstant.projectCreation:
            return _createProjectCategoryContent();
          case SettingCategoryConstant.preview:
            return const ResolutionSettingWidget();
          case SettingCategoryConstant.cache:
            return _buildCacheCategoryContent(selectedCategory);
          case SettingCategoryConstant.touchpadMode:
            return _buildTouchpadModeCategoryContent();
          default:
            return const SizedBox.shrink();
        }
      },
    );
  }

// 创建项目分类内容
  Widget _createProjectCategoryContent() {
    // 使用新的通用单选组件
    final settingItem = context.read<SettingViewModel>().getSettingItem(
          SettingCategoryConstant.projectCreation,
          SettingKeyConstant.projectCreationMode,
        );
    if (settingItem != null) {
      return RadioSettingWidget(
        categoryKey: SettingCategoryConstant.projectCreation,
        settingItem: settingItem,
      );
    }
    return const SizedBox.shrink();
  }

  // 构建缓存设置内容
  Widget _buildCacheCategoryContent(SettingCategory category) {
    return CacheSettingWidget(
      onItemChanged: (updatedItem) {
        final settingViewModel = context.read<SettingViewModel>();
        settingViewModel.updateSetting(updatedItem.key, updatedItem.value);
      },
    );
  }

  // 构建触控板模式设置内容
  Widget _buildTouchpadModeCategoryContent() {
    final settingItem = context.read<SettingViewModel>().getSettingItem(
          SettingCategoryConstant.touchpadMode,
          SettingKeyConstant.touchpadModeEnabled,
        );
    if (settingItem != null) {
      return ToggleSettingWidget(
        categoryKey: SettingCategoryConstant.touchpadMode,
        settingItem: settingItem,
      );
    }
    return const SizedBox.shrink();
  }

  Future<void> _onSave() async {
    if (_selectedCategoryNotifier == null) {
      return;
    }
    try {
      final selectedCategory = _selectedCategoryNotifier!.value;
      final settingViewModel = context.read<SettingViewModel>();

      // 根据分类 key 来决定保存逻辑
      if (selectedCategory.key == SettingCategoryConstant.preview) {
        await settingViewModel.saveResolution();
      } else {
        await settingViewModel.saveSetting();
      }

      // 保存设置成功后不用提示，只有unity中需要提示
      if (GeneralSettingDialog.isShowingInUnityDialog) {
        PGDialog.showToastOnUnity('设置成功');
      }
    } catch (e) {
      PGLog.e('Error saving settings: $e');
      // 保存设置失败后提示
      if (GeneralSettingDialog.isShowingInUnityDialog) {
        PGDialog.showToastOnUnity('设置失败');
      } else {
        PGDialog.showToast('设置失败');
      }
    } finally {
      PGDialog.dismiss(tag: DialogTags.generalSetting);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 620,
      height: 500,
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x00000026),
            blurRadius: 16,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 头部
          Container(
            width: 620,
            height: 48,
            padding: const EdgeInsets.only(left: 16, top: 15, bottom: 15),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Color(0x1AFFFFFF),
                  width: 1,
                ),
              ),
            ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "设置",
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 14,
                  fontWeight: Fonts.medium,
                  color: Colors.white,
                  height: 18 / 14,
                ),
              ),
            ),
          ),

          // 主体内容
          Expanded(
            child: Row(
              children: [
                // 左边功能条目区
                Container(
                  width: 186,
                  height: 388,
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Color(0x1AFFFFFF),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Consumer<SettingViewModel>(
                    builder: (context, viewModel, child) {
                      if (viewModel.isLoading) {
                        return const SizedBox.shrink();
                      }
                      return ValueListenableBuilder<SettingCategory>(
                        valueListenable: _selectedCategoryNotifier!,
                        builder: (context, selectedCategory, child) {
                          return Column(
                            children: viewModel.categories
                                .map(
                                  (category) => _buildCategoryItem(
                                    title: category.title,
                                    category: category,
                                    isSelected:
                                        selectedCategory.key == category.key,
                                  ),
                                )
                                .toList(),
                          );
                        },
                      );
                    },
                  ),
                ),

                // 右边内容区域
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Consumer<SettingViewModel>(
                      builder: (context, viewModel, child) {
                        return _buildContentArea(viewModel);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 底部按钮区
          Container(
            width: 620,
            height: 64,
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Color(0x1AFFFFFF),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // 取消按钮
                GestureDetector(
                  onTap: () => PGDialog.dismiss(tag: DialogTags.generalSetting),
                  child: Container(
                    width: 80,
                    height: 32,
                    padding: const EdgeInsets.symmetric(
                      vertical: 7,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2B2B2B),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "取消",
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Colors.white.withOpacity(0.7),
                        height: 18 / 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // 保存设置按钮
                GestureDetector(
                  onTap: _onSave,
                  child: Container(
                    width: 80,
                    height: 32,
                    padding: const EdgeInsets.symmetric(
                      vertical: 7,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF72561),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "保存设置",
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                        height: 18 / 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
