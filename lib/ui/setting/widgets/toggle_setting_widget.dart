import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_item_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/setting/view_models/setting_view_model.dart';

/// 触控板模式开关设置组件
class ToggleSettingWidget extends StatefulWidget {
  final String categoryKey;
  final SettingItemModel settingItem;

  const ToggleSettingWidget({
    super.key,
    required this.categoryKey,
    required this.settingItem,
  });

  @override
  State<ToggleSettingWidget> createState() => _ToggleSettingWidgetState();
}

class _ToggleSettingWidgetState extends State<ToggleSettingWidget> {
  late ValueNotifier<bool> _isEnabledNotifier;

  @override
  void initState() {
    super.initState();
    // 初始化开关状态，从设置值中获取
    final viewModel = context.read<SettingViewModel>();
    final settingItem = viewModel.getSettingItem(
      widget.categoryKey,
      widget.settingItem.key,
    );
    final isEnabled = settingItem?.value == SwitchState.on;
    _isEnabledNotifier = ValueNotifier(isEnabled);
  }

  @override
  void dispose() {
    _isEnabledNotifier.dispose();
    super.dispose();
  }

  void _toggleSwitch(bool value) {
    _isEnabledNotifier.value = value;

    // 更新设置
    final settingViewModel = context.read<SettingViewModel>();
    settingViewModel.updateSetting(
      widget.settingItem.key,
      value ? SwitchState.on : SwitchState.off,
    );
  }

  Widget _buildToggleSwitch() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isEnabledNotifier,
      builder: (context, isEnabled, child) {
        return GestureDetector(
          onTap: () => _toggleSwitch(!isEnabled),
          child: Container(
            width: 44,
            height: 24,
            decoration: BoxDecoration(
              color:
                  isEnabled ? const Color(0xFFF72561) : const Color(0x66EBEDF5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Stack(
              children: [
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  left: isEnabled ? 22 : 2,
                  top: 2,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          widget.settingItem.title,
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 14,
            color: Colors.white.withOpacity(0.7),
            height: 16 / 14,
          ),
        ),

        const SizedBox(height: 16),

        // 开关
        _buildToggleSwitch(),
      ],
    );
  }
}
