import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/dialog/add_sub_account/add_sub_account_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 账号管理头部组件
class EmployeeHeader extends StatelessWidget {
  const EmployeeHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 22),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "关联账户",
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.medium,
              fontSize: 14,
              color: Colors.white,
            ),
          ),
          GestureDetector(
            onTap: () async {
              PGLog.d('点击添加子账号按钮');
              AddSubAccountDialog.show();
            },
            child: Container(
              width: 120,
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0xFFF72561),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(
                child: Text(
                  "+ 添加子账号",
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
