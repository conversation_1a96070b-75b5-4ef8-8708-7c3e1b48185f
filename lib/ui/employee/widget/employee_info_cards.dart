import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_list.dart';
import 'package:turing_art/datalayer/domain/models/employee/employee_summary.dart';
import 'package:turing_art/ui/employee/widget/employee_info_card.dart';

/// 账号管理卡片组件容器
class EmployeeInfoCards extends StatelessWidget {
  final EmployeeSummary? summary;
  final EmployeeList? employeeList;

  const EmployeeInfoCards({
    super.key,
    this.summary,
    this.employeeList,
  });

  @override
  Widget build(BuildContext context) {
    // 获取数据
    String available = "0";
    String total = "0";
    String creatorUsed = "0";
    String employeeUsed = "0";

    if (employeeList != null) {
      available = employeeList!.available;
      total = employeeList!.total;
    }

    if (summary != null) {
      creatorUsed = summary!.creator;
      employeeUsed = summary!.employee;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
      child: Row(
        children: [
          Expanded(
            child: EmployeeInfoCard(
              title: "剩余张数/总数",
              value: "$available/$total",
              backgroundColor: const Color(0x14FFFFFF),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: EmployeeInfoCard(
              title: "主账号使用张数",
              value: creatorUsed,
              backgroundColor: const Color(0x14FFFFFF),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: EmployeeInfoCard(
              title: "子账号使用张数",
              value: employeeUsed,
              backgroundColor: const Color(0x14FFFFFF),
            ),
          ),
        ],
      ),
    );
  }
}
