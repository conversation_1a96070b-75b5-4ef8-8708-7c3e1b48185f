import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 修改子账号名称弹窗
class RenameEmployeeDialog extends StatefulWidget {
  final String initialName;
  final Function(String) onConfirm;

  const RenameEmployeeDialog({
    super.key,
    required this.initialName,
    required this.onConfirm,
  });

  static Future<void> show(
    BuildContext context, {
    required String initialName,
    required Function(String) onConfirm,
  }) async {
    PGDialog.showCustomDialog(
      width: 400,
      height: 200,
      needBlur: false,
      tag: DialogTags.renameEmployee,
      child: RenameEmployeeDialog(
        initialName: initialName,
        onConfirm: onConfirm,
      ),
    );
  }

  @override
  State<RenameEmployeeDialog> createState() => _RenameEmployeeDialogState();
}

class _RenameEmployeeDialogState extends State<RenameEmployeeDialog> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialName);
    // 在初始化后自动获取焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(FocusNode());
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 400,
      height: 200,
      decoration: BoxDecoration(
        color: const Color(0xFF1B1C1F),
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x33000000),
            blurRadius: 40,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题和关闭按钮
          SizedBox(
            height: 56,
            child: Stack(
              children: [
                // 标题
                Positioned.fill(
                  child: Center(
                    child: Text(
                      "修改名称",
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.medium,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // 关闭按钮
                Positioned(
                  top: 16,
                  right: 16,
                  child: GestureDetector(
                    onTap: () {
                      PGDialog.dismiss(tag: DialogTags.renameEmployee);
                    },
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 输入框
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Container(
              width: 352,
              height: 44,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFFFFFFFF).withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Center(
                child: TextField(
                  controller: _controller,
                  autofocus: true,
                  textAlignVertical: TextAlignVertical.center,
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                    hintText: '请输入账号名称',
                    hintStyle: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                      fontSize: 14,
                      color: const Color(0xA6EBEDF5),
                    ),
                    border: InputBorder.none,
                  ),
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 14,
                    color: const Color(0xFFE1E2E5),
                  ),
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(30),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // 确认按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: GestureDetector(
              onTap: () {
                final name = _controller.text.trim();
                if (name.isNotEmpty) {
                  widget.onConfirm(name);
                  PGDialog.dismiss(tag: DialogTags.renameEmployee);
                } else {
                  PGDialog.showToast('请输入名称');
                }
              },
              child: Container(
                width: 352,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFFF72561),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    "确认",
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.semiBold,
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
