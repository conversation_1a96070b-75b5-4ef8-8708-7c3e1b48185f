import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 账号管理空状态组件
class EmployeeEmptyState extends StatelessWidget {
  const EmployeeEmptyState({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "共享账号",
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.medium,
            fontSize: 16,
            color: const Color(0xFFE1E2E5),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          "添加子账号可共享主账号套餐张数",
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 12,
            color: const Color(0xA6EBEDF5),
          ),
        ),
      ],
    );
  }
}
