import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/enums/time_display_format.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/ui/common/contain_over_flow_text/contain_over_flow_text_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/employee/view_model/employee_dialog_view_model.dart';
import 'package:turing_art/ui/employee/widget/rename_employee_dialog.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 账号列表组件
class EmployeeAccountList extends StatefulWidget {
  const EmployeeAccountList({
    super.key,
  });

  @override
  State<EmployeeAccountList> createState() => _EmployeeAccountListState();
}

class _EmployeeAccountListState extends State<EmployeeAccountList> {
  @override
  Widget build(BuildContext context) {
    return Consumer<EmployeeDialogViewModel>(builder: (context, viewModel, _) {
      final employeeList = viewModel.employeeList;
      if (employeeList == null || employeeList.users.isEmpty) {
        return Container();
      }

      return RawScrollbar(
        thumbVisibility: true,
        trackVisibility: false,
        thickness: 4,
        radius: const Radius.circular(16),
        thumbColor: const Color(0xFF676767),
        controller: viewModel.scrollController,
        child: ListView.builder(
          controller: viewModel.scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: employeeList.users.length,
          itemBuilder: (context, index) {
            final user = employeeList.users[index];
            return _buildAccountItem(context, viewModel, user, index);
          },
        ),
      );
    });
  }

  // 构建账号列表项
  Widget _buildAccountItem(BuildContext context,
      EmployeeDialogViewModel viewModel, User user, int index) {
    // 格式化添加日期
    final formattedDate = DateTimeUtil.formatDateTime(
      user.regDateTime,
      format: DateFormat.slashDateTime,
    );

    return Container(
      width: 1110,
      height: 56,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: index.isOdd ? Colors.transparent : const Color(0xFF212326),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const SizedBox(width: 16),
          // 账号名称
          SizedBox(
            width: 208,
            child: _buildOverflowTextWithTooltip(user.nickname),
          ),
          const SizedBox(width: 32),

          // 手机号
          SizedBox(
            width: 114,
            child: Text(
              user.mobile,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
                fontSize: 14,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 46),

          // 添加日期
          SizedBox(
            width: 144,
            child: Text(
              formattedDate,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
                fontSize: 14,
                color: Colors.white,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          const SizedBox(width: 16),

          // 使用张数
          SizedBox(
            width: 144,
            child: Text(
              user.used,
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
                fontSize: 14,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 16),

          // 状态
          _buildStatusButton(user.enable),
          const SizedBox(width: 88),

          // 修改名称按钮
          GestureDetector(
            onTap: () => _showRenameDialog(context, viewModel, user),
            child: Container(
              width: 80,
              height: 32,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white.withOpacity(0.08),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2), // 微调文本位置
                child: Text(
                  "修改名称",
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 14,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // 停用/启用按钮
          GestureDetector(
            onTap: () => _toggleEmployeeStatus(viewModel, user),
            child: Container(
              width: 80,
              height: 32,
              decoration: BoxDecoration(
                border: Border.all(
                  color: user.enable
                      ? Colors.white.withOpacity(0.08)
                      : Colors.transparent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
                color:
                    user.enable ? Colors.transparent : const Color(0xFFF72561),
              ),
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2), // 微调文本位置
                child: Text(
                  user.enable ? "停用" : "启用",
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 14,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建状态按钮
  Widget _buildStatusButton(bool isEnabled) {
    return Container(
      width: 62,
      height: 32,
      decoration: BoxDecoration(
        color: isEnabled
            ? const Color(0xFFF72561).withOpacity(0.2)
            : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(bottom: 2), // 微调文本位置
        child: Text(
          isEnabled ? "使用中" : "已停用",
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.medium,
            fontSize: 14,
            color: isEnabled ? const Color(0xFFF72561) : Colors.white,
          ),
        ),
      ),
    );
  }

  // 构建带悬浮提示的溢出文本
  Widget _buildOverflowTextWithTooltip(String text) {
    final textStyle = TextStyle(
      fontFamily: Fonts.defaultFontFamily,
      fontWeight: Fonts.regular,
      fontSize: 14,
      color: Colors.white,
    );

    return ContainOverFlowTipTextWidget(
      text: text,
      style: textStyle,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  // 显示修改名称对话框
  void _showRenameDialog(
      BuildContext context, EmployeeDialogViewModel viewModel, User user) {
    RenameEmployeeDialog.show(
      context,
      initialName: user.nickname,
      onConfirm: (newName) {
        viewModel.updateEmployeeName(user, newName).then((value) {
          final tip = value ? '修改成功' : '修改失败';
          PGDialog.showToast(tip);
        });
      },
    );
  }

  // 切换员工状态
  void _toggleEmployeeStatus(EmployeeDialogViewModel viewModel, User user) {
    viewModel.toggleEmployeeStatus(user).then((value) {
      final tip = value ? '切换状态成功' : '切换状态失败';
      PGDialog.showToast(tip);
    });
  }
}
