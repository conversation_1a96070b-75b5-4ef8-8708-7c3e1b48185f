import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 账号管理信息卡片组件
class EmployeeInfoCard extends StatelessWidget {
  final String title;
  final String value;
  final Color backgroundColor;

  const EmployeeInfoCard({
    super.key,
    required this.title,
    required this.value,
    required this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 93,
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 数字区域，高34，垂直居中
          SizedBox(
            height: 34,
            child: Center(
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    fontSize: 24,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),

          // 头部和数字区域底部距离2
          const SizedBox(height: 2),

          // 描述区域，高17，垂直居中
          SizedBox(
            height: 17,
            child: Center(
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 12,
                    color: const Color(0xA6EBEDF5),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
