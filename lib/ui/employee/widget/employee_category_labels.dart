import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 账号管理分类标签组件
class EmployeeCategoryLabels extends StatelessWidget {
  const EmployeeCategoryLabels({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildDivider(),
          const SizedBox(height: 22),
          Row(
            children: [
              _buildCategoryLabel("账号名称", 240),
              _buildCategoryLabel("登录验证手机", 160),
              _buildCategoryLabel("添加日期", 160),
              _buildCategoryLabel("使用张数", 160),
              _buildCategoryLabel("状态", 150),
              _buildCategoryLabel("操作", 240),
            ],
          ),
          const SizedBox(height: 12),
        ],
      ),
    );
  }

  // 构建分割线
  Widget _buildDivider() {
    return Container(
      height: 1,
      color: Colors.white.withOpacity(0.08),
    );
  }

  // 构建单个分类标签
  Widget _buildCategoryLabel(String label, double width) {
    return SizedBox(
      width: width,
      height: 16,
      child: Row(
        children: [
          Container(
            width: 1,
            height: 16,
            color: Colors.white.withOpacity(0.08),
          ),
          const SizedBox(width: 16),
          Text(
            label,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              fontSize: 12,
              color: Colors.white.withOpacity(0.65),
            ),
          ),
        ],
      ),
    );
  }
}
