sealed class EditProcessFilesUiStatus {
  const EditProcessFilesUiStatus();

  /// 成功状态
  const factory EditProcessFilesUiStatus.success() = SuccessStatus;

  /// 错误状态
  const factory EditProcessFilesUiStatus.error({
    required EditProcessFilesErrorType errorType,
  }) = ErrorStatus;
}

/// 成功状态
final class SuccessStatus extends EditProcessFilesUiStatus {
  const SuccessStatus();
}

/// 错误状态
final class ErrorStatus extends EditProcessFilesUiStatus {
  final EditProcessFilesErrorType errorType;

  const ErrorStatus({required this.errorType});

  String get message {
    switch (errorType) {
      case EditProcessFilesErrorType.diskSpace:
        return '检测到您的C盘空间不足\n建议您清理磁盘空间后继续使用图灵精修';
      case EditProcessFilesErrorType.other:
        return '其他错误';
    }
  }
}

/// 处理文件错误类型
enum EditProcessFilesErrorType {
  diskSpace, // 磁盘空间不足
  other, // 其他错误
}
