import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/ui/unity/widgets/turing_unity_widget.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 预加载 Windows 平台的 Unity 组件
///
/// [onUnityCreated] - Unity 创建完成后的回调函数
/// [onUnityReady] - Unity 完全准备就绪后的回调函数
Widget preloadDesktopUnity({
  Function(dynamic sender)? onUnityCreated,
  Function()? onUnityReady,
  Function(MessageFromUnity message)? onUnityMessageCallback,
}) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      // 暂时不需要实时打印unity消息
      SizedBox(
        width: 10,
        height: 10,
        child: Offstage(
          offstage: true,
          child: TuringUnityWidget(
            onCreatedCallback: (sender) {
              PGLog.d('PreloadDesktopUnity - Unity created on Windows');
              // 将 Unity 创建完成的事件传递出去
              if (onUnityCreated != null) {
                onUnityCreated(sender);
              }

              // 等待 Unity 完全初始化完成
              if (onUnityReady != null) {
                sender.initialized.then((_) {
                  PGLog.d(
                      'PreloadDesktopUnity - Unity is fully initialized now');
                  onUnityReady();
                }).catchError((error) {
                  PGLog.e(
                      'PreloadDesktopUnity - Unity initialization error: $error');
                });
              }
            },
            onDebugMessageCallback: (message) {
              PGLog.d(
                  'PreloadDesktopUnity - onDebugMessageCallback message is $message');
            },
            onUnityMessageCallback: (message) {
              PGLog.d(
                  'PreloadDesktopUnity - onUnityMessageCallback message is $message');
              if (onUnityMessageCallback != null) {
                onUnityMessageCallback(message);
              }
            },
          ),
        ),
      ),
    ],
  );
}
