import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../../core/ui/h5_webview.dart';

class H5Screen extends StatelessWidget {
  const H5Screen({
    super.key, 
    this.url,
    this.title = '',
    this.popRoute,
  });

  final String? url;
  final String title;
  final String? popRoute;

  @override
  Widget build(BuildContext context) {
    if (url == null || url!.isEmpty) {
      return const Scaffold(
        body: Center(
          child: Text('Invalid URL'),
        ),
      );
    }

    // 设置状态栏颜色
    final backgroundColor = const Color(0xFF121415);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: backgroundColor,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ));

    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        
        if (popRoute != null) {
          context.go(popRoute!);
        } else if (context.canPop()) {
          context.pop();
        }
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: SafeArea(
          child: H5WebView(
            url: url!,
            title: title,
            backgroundColor: backgroundColor,
            titleColor: Colors.white,
            closeIconColor: Colors.white,
            onClose: () {
              if (popRoute != null) {
                context.go(popRoute!);
              } else if (context.canPop()) {
                context.pop();
              }
            },
          ),
        ),
      ),
    );
  }
}
