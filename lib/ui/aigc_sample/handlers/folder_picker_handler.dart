import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'folder_picker_interface.dart';

/// 桌面端文件夹选择处理器实现
class DesktopFolderPickerHandler implements FolderPickerInterface {
  @override
  Future<String?> pickFolder({
    String? dialogTitle,
    String? initialDirectory,
    bool lockParentWindow = true,
  }) async {
    try {
      PGLog.d('DesktopFolderPickerHandler: 开始选择文件夹');
      PGLog.d('DesktopFolderPickerHandler: 对话框标题: $dialogTitle');
      PGLog.d('DesktopFolderPickerHandler: 初始目录: $initialDirectory');

      final selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: dialogTitle,
        initialDirectory: initialDirectory,
        lockParentWindow: lockParentWindow,
      );

      if (selectedDirectory != null && selectedDirectory.isNotEmpty) {
        PGLog.d('DesktopFolderPickerHandler: 用户选择了文件夹: $selectedDirectory');
        return selectedDirectory;
      } else {
        PGLog.d('DesktopFolderPickerHandler: 用户取消了选择');
        return null;
      }
    } catch (e) {
      PGLog.e('DesktopFolderPickerHandler: 选择文件夹时出错: $e');

      PGLog.e('DesktopFolderPickerHandler: ${_getPlatformAdvice()}');

      return null;
    }
  }

  String _getPlatformAdvice() {
    switch (defaultTargetPlatform) {
      case TargetPlatform.windows:
        return '确保Windows文件资源管理器可用';
      case TargetPlatform.macOS:
        return '可能需要在系统偏好设置中授予文件访问权限';
      case TargetPlatform.linux:
        return '确保系统安装了合适的文件管理器（如nautilus、dolphin等）';
      default:
        return '当前平台可能不完全支持文件夹选择';
    }
  }
}

/// 移动端文件夹选择处理器实现（空实现）
class MobileFolderPickerHandler implements FolderPickerInterface {
  @override
  Future<String?> pickFolder({
    String? dialogTitle,
    String? initialDirectory,
    bool lockParentWindow = true,
  }) async {
    PGLog.w('MobileFolderPickerHandler: 移动端不支持文件夹选择');
    return null;
  }
}
