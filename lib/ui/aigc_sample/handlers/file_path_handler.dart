import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'file_path_interface.dart';

/// 桌面端文件路径处理器实现
class DesktopFilePathHandler implements FilePathInterface {
  @override
  Future<String> getDesktopPath() async {
    try {
      if (Platform.isWindows) {
        return _getWindowsDesktopPath();
      } else if (Platform.isMacOS) {
        return _getMacOSDesktopPath();
      } else if (Platform.isLinux) {
        return _getLinuxDesktopPath();
      } else {
        // 不支持的平台返回临时目录
        final tempDir = await getTemporaryDirectory();
        return tempDir.path;
      }
    } catch (e) {
      PGLog.e("获取桌面路径失败: $e");
      // 获取失败时返回临时目录
      final tempDir = await getTemporaryDirectory();
      return tempDir.path;
    }
  }

  @override
  Future<bool> openFolder(String filePath) async {
    if (filePath.isEmpty) {
      PGLog.w('DesktopFilePathHandler: 路径为空，无法打开');
      return false;
    }
    // 检查文件夹或者文件路径是否存在
    if (!File(filePath).existsSync()) {
      if (!Directory(filePath).existsSync()) {
        PGLog.w('DesktopFilePathHandler: 路径不存在: $filePath');
        return false;
      }
    }

    try {
      await _openFolderByPlatform(filePath);
      PGLog.d('DesktopFilePathHandler: 成功打开文件夹: $filePath');
      return true;
    } catch (e) {
      PGLog.e('DesktopFilePathHandler: 打开文件夹失败: $e');
      return false;
    }
  }

  /// 根据平台打开文件夹
  Future<void> _openFolderByPlatform(String path) async {
    switch (defaultTargetPlatform) {
      case TargetPlatform.windows:
        // Windows: 区分文件和文件夹处理
        if (FileSystemEntity.isFileSync(path)) {
          // 如果是文件，打开文件夹并选中该文件
          await Process.run('explorer', ['/select,', path],
              stdoutEncoding: const SystemEncoding(),
              stderrEncoding: const SystemEncoding());
        } else {
          // 如果是文件夹，在新窗口中打开并使用资源管理器视图
          await Process.run('explorer', ['/n,', '/e,', path],
              stdoutEncoding: const SystemEncoding(),
              stderrEncoding: const SystemEncoding());
        }
        break;
      case TargetPlatform.macOS:
        // macOS: 使用open
        await Process.run('open', [path]);
        break;
      case TargetPlatform.linux:
        // Linux: 使用xdg-open
        await Process.run('xdg-open', [path]);
        break;
      default:
        throw UnsupportedError('当前平台不支持打开文件夹: ${defaultTargetPlatform.name}');
    }
  }

  /// 获取Windows桌面路径
  String _getWindowsDesktopPath() {
    // 首选方案：使用USERPROFILE环境变量
    final userProfile = Platform.environment['USERPROFILE'] ?? '';
    if (userProfile.isNotEmpty) {
      return '$userProfile\\Desktop';
    }

    // 备用方案1：使用HOMEDRIVE和HOMEPATH环境变量
    final homeDrive = Platform.environment['HOMEDRIVE'] ?? '';
    final homePath = Platform.environment['HOMEPATH'] ?? '';
    if (homeDrive.isNotEmpty && homePath.isNotEmpty) {
      return '$homeDrive$homePath\\Desktop';
    }

    // 备用方案2：使用当前目录
    return '${Directory.current.path}\\Desktop';
  }

  /// 获取macOS桌面路径
  String _getMacOSDesktopPath() {
    // 首选方案：使用HOME环境变量
    final homeDir = Platform.environment['HOME'] ?? '';
    if (homeDir.isNotEmpty) {
      return '$homeDir/Desktop';
    }

    // 备用方案：使用当前目录
    return '${Directory.current.path}/Desktop';
  }

  /// 获取Linux桌面路径
  String _getLinuxDesktopPath() {
    // 首选方案：使用HOME环境变量
    final homeDir = Platform.environment['HOME'] ?? '';
    if (homeDir.isNotEmpty) {
      return '$homeDir/Desktop';
    }

    // 备用方案1：使用XDG_DESKTOP_DIR环境变量
    final xdgDesktopDir = Platform.environment['XDG_DESKTOP_DIR'] ?? '';
    if (xdgDesktopDir.isNotEmpty) {
      return xdgDesktopDir;
    }

    // 备用方案2：使用当前目录
    return '${Directory.current.path}/Desktop';
  }

  @override
  bool isHiddenFile(File file) {
    try {
      final fileName = file.path.split(Platform.pathSeparator).last;

      if (Platform.isWindows) {
        // Windows系统：检查文件属性
        return _isWindowsHiddenFile(file);
      } else if (Platform.isMacOS || Platform.isLinux) {
        // Unix-like系统（macOS/Linux）：以点开头的文件为隐藏文件
        return fileName.startsWith('.');
      } else {
        // 其他平台默认以点开头判断
        return fileName.startsWith('.');
      }
    } catch (e) {
      PGLog.e('判断隐藏文件失败: $e');
      return false;
    }
  }

  /// 检查Windows系统下的隐藏文件
  bool _isWindowsHiddenFile(File file) {
    try {
      // Windows系统特有的隐藏文件检查
      // 使用 attrib 命令检查文件属性
      final result = Process.runSync('attrib', [file.path]);
      if (result.exitCode == 0) {
        final output = result.stdout.toString();
        // 如果输出包含 'H' 标志，则表示是隐藏文件
        return output.trim().startsWith('H') || output.contains(' H ');
      }

      return false;
    } catch (e) {
      PGLog.e('检查Windows隐藏文件属性失败: $e');
      // 发生错误时，回退到文件名检查
      final fileName = file.path.split(Platform.pathSeparator).last;
      return fileName.startsWith('.');
    }
  }
}

/// 移动端文件路径处理器实现
class MobileFilePathHandler implements FilePathInterface {
  @override
  Future<String> getDesktopPath() async {
    // 移动端没有桌面路径的概念，返回应用文档目录
    PGLog.w('MobileFilePathHandler: 移动端没有桌面路径的概念');
    return '';
  }

  @override
  Future<bool> openFolder(String path) async {
    PGLog.w('MobileFilePathHandler: 移动端不支持打开文件夹');
    return false;
  }

  @override
  bool isHiddenFile(File file) {
    PGLog.w('MobileFilePathHandler: 移动端不支持判断隐藏文件');
    return false;
  }
}
