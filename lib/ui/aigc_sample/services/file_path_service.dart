import 'dart:io';

import 'package:turing_art/ui/aigc_sample/handlers/file_path_handler.dart';
import 'package:turing_art/ui/aigc_sample/handlers/file_path_interface.dart';
import 'package:turing_art/utils/app_constants.dart';

/// 文件路径服务
/// 用于处理各种文件路径相关的功能，包括获取系统路径和打开文件夹
class FilePathService {
  static final bool isDesktop = AppConstants.isDesktop;

  final FilePathInterface _handler;

  FilePathService._({required FilePathInterface handler}) : _handler = handler;

  /// 获取桌面路径
  Future<String> getDesktopPath() async {
    return await _handler.getDesktopPath();
  }

  /// 打开文件夹
  /// 使用系统文件管理器打开指定路径的文件夹
  Future<bool> openFolder(String path) {
    return _handler.openFolder(path);
  }

  /// 判断文件是否为隐藏文件
  /// [file] 要判断的文件对象
  /// 返回 bool 表示是否为隐藏文件
  bool isHiddenFile(File file) {
    return _handler.isHiddenFile(file);
  }

  /// 根据当前平台获取合理的实现
  factory FilePathService.forPlatform() {
    final handler =
        isDesktop ? DesktopFilePathHandler() : MobileFilePathHandler();
    return FilePathService._(handler: handler);
  }
}
