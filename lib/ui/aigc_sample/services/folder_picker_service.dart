import 'package:turing_art/utils/app_constants.dart';

import '../handlers/folder_picker_handler.dart';
import '../handlers/folder_picker_interface.dart';

/// 文件夹选择服务
class FolderPickerService {
  static final bool isDesktop = AppConstants.isDesktop;

  final FolderPickerInterface _handler;

  FolderPickerService._({required FolderPickerInterface handler})
      : _handler = handler;

  /// 选择文件夹
  ///
  /// [dialogTitle] 对话框标题
  /// [initialDirectory] 初始目录路径
  /// [lockParentWindow] 是否锁定父窗口（仅桌面平台）
  ///
  /// 返回选择的文件夹路径，如果取消选择则返回null
  Future<String?> pickFolder({
    String? dialogTitle,
    String? initialDirectory,
    bool lockParentWindow = true,
  }) {
    return _handler.pickFolder(
      dialogTitle: dialogTitle,
      initialDirectory: initialDirectory,
      lockParentWindow: lockParentWindow,
    );
  }

  /// 根据当前平台获取合理的实现
  factory FolderPickerService.forPlatform() {
    final handler =
        isDesktop ? DesktopFolderPickerHandler() : MobileFolderPickerHandler();
    return FolderPickerService._(handler: handler);
  }
}
