import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/export_status.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_effect_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

class AigcSampleEffectListWidget extends StatefulWidget {
  final AigcSampleModel sampleModel;
  final ValueChanged<int>? onItemSelected;
  final ValueChanged<String>? onItemDeleted;
  final int selectedIndex;

  const AigcSampleEffectListWidget({
    super.key,
    required this.sampleModel,
    this.onItemSelected,
    this.onItemDeleted,
    this.selectedIndex = 0,
  });

  @override
  State<AigcSampleEffectListWidget> createState() =>
      _AigcSampleEffectListWidgetState();
}

class _AigcSampleEffectListWidgetState
    extends State<AigcSampleEffectListWidget> {
  final ValueNotifier<int> _hoveredIndexNotifier = ValueNotifier<int>(-1);
  final ScrollController _scrollController = ScrollController();
  int _previousEffectsCount = 0;

  @override
  void initState() {
    super.initState();
    _previousEffectsCount = widget.sampleModel.effects.length;
  }

  @override
  void didUpdateWidget(AigcSampleEffectListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检查是否有新的效果图添加
    final currentEffectsCount = widget.sampleModel.effects.length;
    if (currentEffectsCount > _previousEffectsCount) {
      // 有新的效果图添加，滚动到最后
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
    _previousEffectsCount = currentEffectsCount;
  }

  @override
  void dispose() {
    _hoveredIndexNotifier.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 330,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.only(left: 4, right: 0), // 为滚动条预留4px空间
      child: _buildEffectsList(),
    );
  }

  Widget _buildEffectsList() {
    final effects = widget.sampleModel.effects;

    // 将effects按每行3个分组
    final List<List<AigcSampleEffectModel>> rows = [];
    for (int i = 0; i < effects.length; i += 3) {
      final row = effects.skip(i).take(3).toList();
      rows.add(row);
    }

    if (rows.isEmpty) {
      return const Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            color: Color(0x66FFFFFF),
            fontSize: 14,
          ),
        ),
      );
    }

    return RawScrollbar(
      thumbVisibility: true,
      trackVisibility: false,
      thickness: 4,
      radius: const Radius.circular(16),
      thumbColor: const Color(0xFF676767),
      controller: _scrollController,
      child: Padding(
        padding: const EdgeInsets.only(right: 4), // 内容区域右侧padding 4px，与滚动条保持距离
        child: ListView.builder(
          controller: _scrollController,
          shrinkWrap: true,
          itemCount: rows.length,
          itemBuilder: (context, rowIndex) {
            final row = rows[rowIndex];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  for (int i = 0; i < row.length; i++) ...[
                    SizedBox(
                      width: 100,
                      height: 100,
                      child: _buildEffectItem(
                        row[i],
                        rowIndex * 3 + i,
                      ),
                    ),
                    if (i < row.length - 1) const SizedBox(width: 8),
                  ],
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEffectItem(AigcSampleEffectModel effect, int index) {
    final isSelected = index == widget.selectedIndex;

    return ValueListenableBuilder<int>(
      valueListenable: _hoveredIndexNotifier,
      builder: (context, hoveredIndex, _) {
        final isHovered = index == hoveredIndex;
        final exportStatus = ExportStatus.fromString(effect.exportStatus);

        return PlatformMouseRegion(
          onEnter: (_) => _hoveredIndexNotifier.value = index,
          onExit: (_) => _hoveredIndexNotifier.value = -1,
          child: GestureDetector(
            onTap: () => widget.onItemSelected?.call(index),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: (isSelected || isHovered)
                    ? Border.all(color: Colors.white, width: 1)
                    : null,
              ),
              child: Stack(
                children: [
                  // 图片
                  Positioned.fill(
                    child: Container(
                      margin: (isSelected || isHovered)
                          ? const EdgeInsets.all(2)
                          : EdgeInsets.zero,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(
                          (isSelected || isHovered) ? 4 : 6,
                        ),
                        child: CachedImageWidget(
                          imageUrl: effect.thumbUrl.isNotEmpty
                              ? effect.thumbUrl
                              : effect.photoUrl,
                          placeholder: AigcItemAnimation(
                            child: Image.asset(
                              'assets/icons/aigc_presets_loading_icon.png',
                              fit: BoxFit.cover,
                              width: 100,
                              height: 100,
                            ),
                          ),
                          errorWidget: AigcItemAnimation(
                            child: Image.asset(
                              'assets/icons/aigc_presets_loading_icon.png',
                              fit: BoxFit.cover,
                              width: 100,
                              height: 100,
                            ),
                          ),
                          fit: BoxFit.cover,
                          width: 100,
                          height: 100,
                        ),
                      ),
                    ),
                  ),

                  // 删除按钮
                  if ((isHovered) &&
                      effect.thumbUrl.isNotEmpty &&
                      effect.photoUrl.isNotEmpty)
                    Positioned(
                      right: 5,
                      bottom: 5,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: const Color(0xFF1F1F1F).withOpacity(0.8),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: const Color(0xFFFFFFFF).withOpacity(0.1),
                            width: 0.5,
                          ),
                        ),
                        child: GestureDetector(
                          onTap: () =>
                              widget.onItemDeleted?.call(effect.effectCode),
                          child: Image.asset(
                            'assets/icons/aigc_sample_delete_icon.png',
                            width: 20,
                            height: 20,
                          ),
                        ),
                      ),
                    ),

                  // 已导出标识
                  if (exportStatus.isCompleted || exportStatus.isRunning)
                    Positioned(
                      top: 5,
                      right: 5,
                      child: Container(
                        width: 44,
                        height: 24,
                        decoration: BoxDecoration(
                          color: const Color(0xFFE6E6E6),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Colors.white,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 2,
                          vertical: 3,
                        ),
                        child: Center(
                          child: Text(
                            exportStatus.isCompleted ? '已导出' : '导出中',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 12,
                              fontFamily: Fonts.defaultFontFamily,
                              fontWeight: Fonts.medium,
                              height: 16 / 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPresetTitle() {
    return SizedBox(
      width: 316,
      height: 28,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '主题预设',
            style: TextStyle(
              color: const Color(0x80FFFFFF),
              fontSize: 12,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.light,
              height: 16 / 12,
            ),
          ),
          Text(
            widget.sampleModel.presetName.isNotEmpty
                ? '${widget.sampleModel.presetName}-${widget.sampleModel.presetEffectName}'
                : '原图增强',
            style: TextStyle(
              color: const Color(0xB2FFFFFF),
              fontSize: 12,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              height: 16 / 12,
            ),
          ),
        ],
      ),
    );
  }
}
