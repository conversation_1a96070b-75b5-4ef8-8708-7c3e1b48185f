import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

/// 缩放选择器
class AigcSampleSelectScaleWidget extends StatefulWidget {
  final String initialScale;
  final double? currentScale; // 新增：当前实际的缩放比例
  final ValueChanged<double>? onScaleChanged;

  const AigcSampleSelectScaleWidget({
    super.key,
    this.initialScale = '100%',
    this.currentScale,
    this.onScaleChanged,
  });

  @override
  State<AigcSampleSelectScaleWidget> createState() =>
      _AigcSampleSelectScaleWidgetState();
}

class _AigcSampleSelectScaleWidgetState
    extends State<AigcSampleSelectScaleWidget>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  String _selectedScale = '100%';
  int _hoveredIndex = -1;

  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  final List<(String, double)> _scaleOptions = [
    ('合适', 1.0),
    ('50%', 0.5),
    ('100%', 1.0),
    ('200%', 2.0),
    ('300%', 3.0),
    ('400%', 4.0),
    ('500%', 5.0),
    ('800%', 8.0),
    ('1000%', 10.0),
  ];

  @override
  void initState() {
    super.initState();
    _selectedScale = widget.initialScale;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: -1.5708 * 2.0, // 折叠状态：朝下 (90度，从左转到下)
      end: 0, // 展开状态：朝上 (-90度，从左转到上)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(AigcSampleSelectScaleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当外部scale发生变化时，更新显示的文本
    if (widget.currentScale != oldWidget.currentScale &&
        widget.currentScale != null) {
      _updateScaleText(widget.currentScale!);
    }
  }

  /// 根据实际的缩放比例更新显示文本
  void _updateScaleText(double scale) {
    final percentage = (scale * 100).round();
    setState(() {
      _selectedScale = '$percentage%';
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _selectScale((String, double) scale) {
    setState(() {
      _selectedScale = scale.$1;
      _isExpanded = false;
      _hoveredIndex = -1;
    });
    _animationController.reverse();
    widget.onScaleChanged?.call(scale.$2);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 108,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 展开状态的列表
          if (_isExpanded) _buildExpandedList(),

          // 间距
          if (_isExpanded) const SizedBox(height: 2),

          // 折叠状态的控件
          _buildCollapsedWidget(),
        ],
      ),
    );
  }

  Widget _buildExpandedList() {
    return Container(
      width: 108,
      decoration: BoxDecoration(
        color: const Color(0xFF2B2B2B),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(_scaleOptions.length, (index) {
          final scale = _scaleOptions[index];
          return _buildScaleItem(scale, index);
        }),
      ),
    );
  }

  Widget _buildScaleItem((String scale, double scaleValue) scale, int index) {
    final isHovered = _hoveredIndex == index;

    return PlatformMouseRegion(
      onEnter: (_) {
        setState(() {
          _hoveredIndex = index;
        });
      },
      onExit: (_) {
        setState(() {
          _hoveredIndex = -1;
        });
      },
      child: GestureDetector(
        onTap: () => _selectScale(scale),
        child: Container(
          width: 108,
          height: 24,
          margin: EdgeInsets.only(
            top: index == 0 ? 2 : 0,
            bottom: index == _scaleOptions.length - 1 ? 2 : 2,
            left: 2,
            right: 2,
          ),
          decoration: BoxDecoration(
            color:
                isHovered ? Colors.white.withOpacity(0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: const EdgeInsets.only(left: 8),
              child: Text(
                scale.$1,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.85),
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: FontWeight.w400,
                  height: 16 / 12,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCollapsedWidget() {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: Container(
        width: 108,
        height: 32,
        decoration: BoxDecoration(
          color: const Color(0xFF383838),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 左侧图标
            const SizedBox(width: 4),
            Image.asset(
              'assets/icons/aigc_sample_scale.png',
              width: 24,
              height: 24,
            ),

            // 缩放文本
            const SizedBox(width: 2),
            Expanded(
              child: Text(
                _selectedScale,
                style: TextStyle(
                  color: const Color(0xFF909191),
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: FontWeight.w600,
                  height: 16 / 12,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(width: 2),

            // 右侧箭头图标
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationAnimation.value, // 直接使用弧度值
                  child: Image.asset(
                    'assets/icons/icon_arrows_up.png',
                    width: 16,
                    height: 16,
                  ),
                );
              },
            ),
            const SizedBox(width: 4),
          ],
        ),
      ),
    );
  }
}
