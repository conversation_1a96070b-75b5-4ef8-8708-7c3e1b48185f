import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_effect_model.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_key.dart';
import 'package:turing_art/ui/aigc_editing/shortcut/aigc_shortcut_manager_mixin.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_image_preview_view_model.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_sample_detail_view_model.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_detail/aigc_sample_select_scale_widget.dart';
import 'package:turing_art/ui/common/image_page/image_pagination_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

class AigcImagePreviewWidget extends StatefulWidget {
  final AigcSampleEffectModel effect;
  final String originalUrl;
  final int currentIndex;
  final int totalPage;
  final double parentWidth;
  final double parentHeight;
  final ValueChanged<int>? onIndexChanged;

  const AigcImagePreviewWidget({
    super.key,
    required this.effect,
    required this.originalUrl,
    this.currentIndex = 0,
    required this.totalPage,
    required this.parentWidth,
    required this.parentHeight,
    this.onIndexChanged,
  });

  @override
  State<AigcImagePreviewWidget> createState() => _AigcImagePreviewWidgetState();
}

class _AigcImagePreviewWidgetState extends State<AigcImagePreviewWidget>
    with ShortcutManagerMixin {
  late int _currentIndex;

  // 使用ValueNotifier管理是否显示原图的状态
  final ValueNotifier<bool> _isShowingOriginalNotifier =
      ValueNotifier<bool>(false);

  // ViewModel 实例
  late AigcImagePreviewViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex;
    _viewModel = AigcImagePreviewViewModel();

    // 注册空格键快捷键
    _registerShortcuts();
  }

  @override
  void dispose() {
    // 释放ValueNotifier资源
    _isShowingOriginalNotifier.dispose();
    _viewModel.dispose();
    super.dispose();
  }

  // 注册快捷键
  void _registerShortcuts() {
    registerShortcutWithKeyUp(
      id: 'space_compare',
      shortcut: ShortcutKey.space,
      handler: _handleSpaceKeyDown,
      onKeyUp: _handleSpaceKeyUp,
      description: '空格键对比原图',
      additionalCheck: () => mounted && widget.originalUrl.isNotEmpty,
    );
  }

  // 处理空格键按下事件
  KeyEventHandleResult _handleSpaceKeyDown() {
    _showOriginalImage();
    return KeyEventHandleResult.handled;
  }

  // 处理空格键抬起事件
  KeyEventHandleResult _handleSpaceKeyUp() {
    _hideOriginalImage();
    return KeyEventHandleResult.handled;
  }

  @override
  void didUpdateWidget(AigcImagePreviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentIndex != oldWidget.currentIndex) {
      _currentIndex = widget.currentIndex;
      // 页面切换时重置缩放和位置
      _viewModel.resetTransform();
    }
  }

  // 处理滚轮缩放
  void _handleScroll(PointerSignalEvent event, BoxConstraints constraints) {
    _viewModel.setParentConstraints(constraints);
    _viewModel.handleScroll(event);
  }

  /// 处理拖拽
  void _handlePanUpdate(DragUpdateDetails details, BoxConstraints constraints) {
    _viewModel.setParentConstraints(constraints);
    _viewModel.handlePanUpdate(details);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Stack(
            children: [
              // 图片预览区域 - 支持缩放和拖拽
              Positioned.fill(
                child: Listener(
                  onPointerSignal: (event) => _handleScroll(event, constraints),
                  child: GestureDetector(
                    onPanUpdate: (details) =>
                        _handlePanUpdate(details, constraints),
                    child: ValueListenableBuilder<bool>(
                      valueListenable: _isShowingOriginalNotifier,
                      builder: (context, isShowingOriginal, _) {
                        return AnimatedBuilder(
                          animation: _viewModel,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: _viewModel.offset,
                              child: Transform.scale(
                                scale: _viewModel.scale,
                                child: _buildImageView(isShowingOriginal),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),

              // 页码组件
              Positioned(
                bottom: 16,
                left: 0,
                right: 0,
                child: Center(
                  child: ImagePaginationWidget(
                    currentPage: _currentIndex + 1,
                    totalPages: widget.totalPage,
                    onPrevious: previousImage,
                    onNext: nextImage,
                  ),
                ),
              ),

              Positioned(
                bottom: 16,
                left: 16,
                child: AnimatedBuilder(
                  animation: _viewModel,
                  builder: (context, child) {
                    return AigcSampleSelectScaleWidget(
                      initialScale: '${(_viewModel.scale * 100).round()}%',
                      currentScale: _viewModel.scale,
                      onScaleChanged: (scale) {
                        _viewModel.scale = scale;
                      },
                    );
                  },
                ),
              ),

              // 对比按钮
              Positioned(
                bottom: 16,
                right: 16,
                child: _buildCompareButton(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTipView(String tip) {
    return Container(
      width: 957,
      height: 957,
      color: Colors.transparent,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/aigc_credit_star_new.png',
              width: 32,
              height: 32,
              fit: BoxFit.fitWidth,
            ),
            const SizedBox(height: 12),
            Text(
              tip,
              style: TextStyle(
                color: Colors.white.withOpacity(0.35),
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 修改为接收isShowingOriginal参数
  Widget _buildImageView(bool isShowingOriginal) {
    // 如果显示原图，则使用originalUrl，否则使用photoUrl
    final String displayUrl =
        isShowingOriginal ? widget.originalUrl : widget.effect.photoUrl;
    if (displayUrl.isEmpty) {
      return _buildTipView('正在打样中，请稍后...');
    }
    return SizedBox.expand(
      child: CachedImageWidget(
        imageUrl: displayUrl,
        radius: 8.0,
        placeholder: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.transparent,
          child: Center(
            child: Image.asset(
              'assets/icons/aigc_credit_star_new.png',
              width: 32,
              height: 32,
              fit: BoxFit.fitWidth,
            ),
          ),
        ),
        errorWidget: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.transparent,
          child: Center(
            child: Text(
              '预览图加载失败',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
              ),
            ),
          ),
        ),
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildCompareButton() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isShowingOriginalNotifier,
      builder: (context, isShowingOriginal, _) {
        return PlatformMouseRegion(
          onEnter: (_) {},
          onExit: (_) {},
          child: GestureDetector(
            onTapDown: (_) async {
              // 检查网络连接状态
              final viewModel = context.read<AigcSampleDetailViewModel>();
              final isConnected = await viewModel.checkNetworkConnection();
              if (!isConnected) {
                PGDialog.showToast('网络错误，请检查网络连接');
                return;
              }
              _isShowingOriginalNotifier.value = true;
            },
            onTapUp: (_) {
              _isShowingOriginalNotifier.value = false;
            },
            onTapCancel: () {
              _isShowingOriginalNotifier.value = false;
            },
            child: Container(
              width: 32,
              height: 32,
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isShowingOriginal
                    ? const Color(0xFF0D0D0D)
                    : const Color(0xFF383838),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Image.asset(
                'assets/icons/aigc_sample_detail_compar.png',
                color: isShowingOriginal ? Colors.white : Colors.white,
                width: 32,
                height: 24,
              ),
            ),
          ),
        );
      },
    );
  }

  void previousImage() {
    widget.onIndexChanged?.call(_currentIndex - 1);
  }

  void nextImage() {
    widget.onIndexChanged?.call(_currentIndex + 1);
  }

  // 显示原图的方法
  void _showOriginalImage() async {
    // 检查网络连接状态
    final viewModel = context.read<AigcSampleDetailViewModel>();
    final isConnected = await viewModel.checkNetworkConnection();
    if (!isConnected) {
      PGDialog.showToast('网络错误，请检查网络连接');
      return;
    }
    _isShowingOriginalNotifier.value = true;
  }

  // 隐藏原图的方法
  void _hideOriginalImage() {
    _isShowingOriginalNotifier.value = false;
  }
}
