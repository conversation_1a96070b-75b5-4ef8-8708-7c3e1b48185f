import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

class AigcCreditBubbleContent extends StatelessWidget {
  final String text;

  const AigcCreditBubbleContent({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.only(left: 4, right: 4, top: 8, bottom: 18),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 积分图标
            Image.asset(
              'assets/icons/aigc_credit_star.png',
              width: 20,
              height: 20,
              color: const Color(0xFF22EDFF),
            ),
            // 文字
            _buildRichText(),
          ],
        ),
      ),
    );
  }

  Widget _buildRichText() {
    // 解析文本，提取数字部分并高亮显示
    final RegExp numberRegex = RegExp(r'\d+');
    final List<TextSpan> spans = [];

    int lastMatchEnd = 0;
    for (final match in numberRegex.allMatches(text)) {
      // 添加数字前的文本
      if (match.start > lastMatchEnd) {
        spans.add(TextSpan(
          text: text.substring(lastMatchEnd, match.start),
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.medium,
            height: 16 / 12,
          ),
        ));
      }

      // 添加高亮的数字
      spans.add(TextSpan(
        text: match.group(0),
        style: TextStyle(
          color: const Color(0xFF22EDFF),
          fontSize: 12,
          fontFamily: Fonts.defaultFontFamily,
          fontWeight: Fonts.medium,
          height: 16 / 12,
        ),
      ));

      lastMatchEnd = match.end;
    }

    // 添加剩余的文本
    if (lastMatchEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastMatchEnd),
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontFamily: Fonts.defaultFontFamily,
          fontWeight: Fonts.medium,
          height: 16 / 12,
        ),
      ));
    }

    return RichText(
      text: TextSpan(children: spans),
      textAlign: TextAlign.center,
    );
  }
}
