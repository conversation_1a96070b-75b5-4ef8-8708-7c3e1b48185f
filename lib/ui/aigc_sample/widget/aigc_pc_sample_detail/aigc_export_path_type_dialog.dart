import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/export_path_type.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

// 下拉菜单弹窗组件
class AigcExportPathDropdownDialog extends StatefulWidget {
  final ExportPathType currentPathType;
  final ValueChanged<ExportPathType> onPathTypeChanged;
  final double width;

  const AigcExportPathDropdownDialog({
    super.key,
    required this.currentPathType,
    required this.onPathTypeChanged,
    required this.width,
  });

  static void show({
    required BuildContext context,
    required Offset position,
    required double width,
    required ExportPathType currentPathType,
    required ValueChanged<ExportPathType> onPathTypeChanged,
  }) {
    const double itemHeight = 44;
    const int itemCount = 2; // ExportPathType有2个选项
    const double height = itemHeight * itemCount + 2;

    PGDialog.showPositionedDialog(
      tag: DialogTags.aigcExportPathDropdown,
      width: width,
      height: height,
      alignment: Alignment.topLeft,
      padding: EdgeInsets.only(
        left: position.dx,
        top: position.dy,
      ),
      clickMaskDismiss: true,
      maskColor: Colors.transparent,
      child: AigcExportPathDropdownDialog(
        currentPathType: currentPathType,
        onPathTypeChanged: onPathTypeChanged,
        width: width,
      ),
    );
  }

  @override
  State<AigcExportPathDropdownDialog> createState() =>
      _AigcExportPathDropdownDialogState();
}

class _AigcExportPathDropdownDialogState
    extends State<AigcExportPathDropdownDialog> {
  // 使用ValueNotifier管理每个选项的悬浮状态
  final ValueNotifier<ExportPathType?> _hoveredItemNotifier =
      ValueNotifier<ExportPathType?>(null);

  @override
  void dispose() {
    _hoveredItemNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      decoration: BoxDecoration(
        color: const Color(0xFF2B2B2B),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFFFFFFF).withOpacity(0.1),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(4), // 弹窗内容整体缩进4px
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDropdownItem(ExportPathType.original),
            _buildDropdownItem(ExportPathType.custom),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownItem(ExportPathType pathType) {
    final bool isSelected = widget.currentPathType == pathType;

    return ValueListenableBuilder<ExportPathType?>(
      valueListenable: _hoveredItemNotifier,
      builder: (context, hoveredItem, _) {
        final bool isHovered = hoveredItem == pathType;

        return PlatformMouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) => _hoveredItemNotifier.value = pathType,
          onExit: (_) => _hoveredItemNotifier.value = null,
          child: GestureDetector(
            onTap: () {
              PGDialog.dismiss(tag: DialogTags.aigcExportPathDropdown);
              widget.onPathTypeChanged(pathType);
            },
            child: Container(
              width: widget.width - 8, // 弹窗宽度减去左右各4px的缩进
              height: 40, // 44px减去上下各2px的缩进
              padding: const EdgeInsets.symmetric(horizontal: 8), // 内部左右8px间距
              decoration: BoxDecoration(
                color: isHovered
                    ? const Color(0xFFFFFFFF).withOpacity(0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  pathType.description,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 14,
                    height: 1.29,
                    color: isSelected
                        ? const Color(0xFFF72561)
                        : const Color(0xFFFFFFFF).withOpacity(0.85),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
