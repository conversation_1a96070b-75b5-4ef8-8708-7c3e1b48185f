import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_models.dart';
import 'package:turing_art/datalayer/domain/enums/export_path_type.dart';
import 'package:turing_art/datalayer/domain/enums/export_status.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_sample_detail_view_model.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class AigcExportPathDialog extends StatefulWidget {
  final AigcSampleDetailViewModel viewModel;
  final VoidCallback onExport;

  const AigcExportPathDialog({
    super.key,
    required this.viewModel,
    required this.onExport,
  });

  static void show(
    BuildContext context,
    AigcSampleDetailViewModel viewModel,
    VoidCallback onExport,
  ) {
    if (PGDialog.isDialogVisible(DialogTags.aigcExportPath)) {
      return;
    }

    PGDialog.showCustomDialog(
      width: 480,
      height: 316,
      tag: DialogTags.aigcExportPath,
      needBlur: false,
      child: ChangeNotifierProvider.value(
        value: viewModel,
        child: AigcExportPathDialog(
          viewModel: viewModel,
          onExport: onExport,
        ),
      ),
    );
  }

  @override
  State<AigcExportPathDialog> createState() => _AigcExportPathDialogState();
}

class _AigcExportPathDialogState extends State<AigcExportPathDialog> {
  // 使用ValueNotifier替代布尔状态变量
  final ValueNotifier<bool> _cancelButtonHoveredNotifier =
      ValueNotifier<bool>(false);
  final ValueNotifier<bool> _exportButtonHoveredNotifier =
      ValueNotifier<bool>(false);

  // 添加GlobalKey用于准确定位下拉选择框
  final GlobalKey _dropdownKey = GlobalKey();

  @override
  void dispose() {
    _cancelButtonHoveredNotifier.dispose();
    _exportButtonHoveredNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击外部关闭下拉菜单
        PGDialog.dismiss(tag: DialogTags.aigcExportPathDropdown);
      },
      child: Container(
        width: 480,
        height: 316,
        decoration: BoxDecoration(
          color: const Color(0xFF1E1E1E),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFFFFFFFF).withOpacity(0.1),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部标题栏
              _buildTitleBar(),

              // 提示文本
              Padding(
                padding: const EdgeInsets.only(left: 16, top: 20),
                child: Text(
                  'AI作图完成会自动保存至本地，请选择路径',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.semiBold,
                    fontSize: 16,
                    height: 1.25,
                    color: const Color(0xFFFFFFFF).withOpacity(0.85),
                  ),
                ),
              ),

              // 分割线
              Container(
                margin: const EdgeInsets.only(top: 20),
                width: 480,
                height: 1,
                color: const Color(0xFFFFFFFF).withOpacity(0.1),
              ),

              // 导出路径选择
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
                child: Row(
                  children: [
                    SizedBox(
                      width: 40,
                      height: 16,
                      child: Center(
                        child: Text(
                          '导出至',
                          style: TextStyle(
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.regular,
                            fontSize: 12,
                            height: 1.33,
                            color: const Color(0xFFFFFFFF).withOpacity(0.5),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(child: _buildPathDropdown()),
                  ],
                ),
              ),

              // 查看路径按钮
              Padding(
                padding: const EdgeInsets.only(left: 80, right: 16, top: 16),
                child: _buildViewPathButton(),
              ),

              // 底部分割线
              const Spacer(),
              Container(
                width: 480,
                height: 1,
                color: const Color(0xFFFFFFFF).withOpacity(0.1),
              ),

              // 底部按钮
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _buildCancelButton(),
                    const SizedBox(width: 12),
                    _buildExportButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 顶部标题栏
  Widget _buildTitleBar() {
    return Stack(
      children: [
        Container(
          width: 480,
          height: 40,
          color: const Color(0xFF141414),
        ),
        Center(
          child: Container(
            width: 132,
            height: 40,
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
            decoration: const BoxDecoration(
              color: Color(0xFF1E1E1E),
            ),
            child: Center(
              child: Text(
                '导出路径设置',
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                  fontSize: 14,
                  height: 1.29,
                  color: const Color(0xFFFFFFFF).withOpacity(0.85),
                ),
              ),
            ),
          ),
        ),
        Positioned(
          top: 12,
          right: 12,
          child: GestureDetector(
            onTap: () => PGDialog.dismiss(tag: DialogTags.aigcExportPath),
            child: Icon(
              Icons.close,
              size: 16,
              color: const Color(0xFFFFFFFF).withOpacity(0.6),
            ),
          ),
        ),
      ],
    );
  }

  // 路径下拉选择框（根据type变化刷新text,需求修改为不让选择，只显示）
  Widget _buildPathDropdown() {
    return GestureDetector(
      onTap: () {
        //_showDropdownMenu(context, widget.viewModel.getExportPathType());
      },
      child: Container(
        key: _dropdownKey,
        height: 44,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFFFFFFFF).withOpacity(0.1),
            width: 0.5,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Selector<AigcSampleDetailViewModel, ExportPathType>(
                selector: (_, viewModel) => viewModel.getExportPathType(),
                builder: (context, currentPathType, _) {
                  return Text(
                    currentPathType.description,
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                      fontSize: 14,
                      height: 1.29,
                      color: const Color(0xFFFFFFFF).withOpacity(0.85),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  );
                },
              ),
            ),
            // const SizedBox(width: 8),
            // Icon(
            //   Icons.keyboard_arrow_down,
            //   size: 16,
            //   color: const Color(0xFFFFFFFF).withOpacity(0.6),
            // ),
          ],
        ),
      ),
    );
  }

  // 显示下拉菜单
  /*
  void _showDropdownMenu(BuildContext context, ExportPathType currentPathType) {
    // 获取下拉选择框的RenderBox
    final RenderBox? renderBox =
        _dropdownKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      return;
    }

    // 计算下拉选择框的全局位置
    final Offset globalPosition = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    // 计算下拉菜单的位置（与下拉选择框完全对齐）
    final Offset menuPosition = Offset(
      globalPosition.dx, // 与下拉选择框左边缘对齐
      globalPosition.dy + size.height + 4, // 在下拉选择框下方，留4px间距
    );

    // 下拉菜单的宽度与下拉选择框保持一致
    final double menuWidth = size.width;

    AigcExportPathDropdownDialog.show(
      context: context,
      position: menuPosition,
      width: menuWidth,
      currentPathType: currentPathType,
      onPathTypeChanged: (ExportPathType newPathType) {
        widget.viewModel.setExportPathType(newPathType);
      },
    );
  }
*/
  // 查看路径按钮（路径类型和自定义路径变化刷新text）
  Widget _buildViewPathButton() {
    return GestureDetector(
      onTap: _openOrChangeExportFolder,
      child: Container(
        width: 384,
        height: 44,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFFFFFFFF).withOpacity(0.1),
            width: 0.5,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Selector<AigcSampleDetailViewModel, String>(
                selector: (_, viewModel) =>
                    '${viewModel.getExportPathType().name}_${UserPreferencesService.getCustomExportPath()}',
                builder: (context, pathKey, _) {
                  return FutureBuilder<CheckPathResult>(
                    future: widget.viewModel.getActualExportPath(),
                    builder: (context, snapshot) {
                      final exportPath = snapshot.data?.resultPath ?? '';
                      return Text(
                        exportPath,
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.regular,
                          fontSize: 14,
                          height: 1.29,
                          color: const Color(0xFFFFFFFF).withOpacity(0.5),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                  );
                },
              ),
            ),
            const SizedBox(width: 8),
            Image.asset(
              'assets/icons/aigc_sample_detail_check_path.png',
              width: 16,
              height: 16,
              color: const Color(0xFFFFFFFF).withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }

  // 查看或更改导出文件夹
  void _openOrChangeExportFolder() async {
    final (_, needTip) = await widget.viewModel.openOrChangeExportFolder();
    if (needTip.isNotEmpty) {
      PGDialog.showToast(needTip);
    }
  }

  // 取消按钮
  Widget _buildCancelButton() {
    return ValueListenableBuilder<bool>(
      valueListenable: _cancelButtonHoveredNotifier,
      builder: (context, isHovered, _) {
        return PlatformMouseRegion(
          onEnter: (_) => _cancelButtonHoveredNotifier.value = true,
          onExit: (_) => _cancelButtonHoveredNotifier.value = false,
          child: GestureDetector(
            onTap: () => PGDialog.dismiss(tag: DialogTags.aigcExportPath),
            child: Container(
              width: 122,
              height: 32,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
              decoration: BoxDecoration(
                color: isHovered
                    ? const Color(0xFF3B3B3B)
                    : const Color(0xFF2B2B2B),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.semiBold,
                    fontSize: 12,
                    height: 1.33,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // 导出按钮
  Widget _buildExportButton() {
    final exportStatus = widget.viewModel.currentExportStatus;
    final exportText = exportStatus == ExportStatus.completed ? '查看路径' : '导出';

    return ValueListenableBuilder<bool>(
      valueListenable: _exportButtonHoveredNotifier,
      builder: (context, isHovered, _) {
        return PlatformMouseRegion(
          onEnter: (_) => _exportButtonHoveredNotifier.value = true,
          onExit: (_) => _exportButtonHoveredNotifier.value = false,
          child: GestureDetector(
            onTap: () async {
              // 导出前处理路径检查和初始化
              await widget.viewModel.handleExportWithPathCheck();
              // 必须await，可能马上会有下载弹窗show出来，需要等待dismiss完成
              await PGDialog.dismiss(tag: DialogTags.aigcExportPath);
              widget.onExport();
            },
            child: Container(
              width: 122,
              height: 32,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
              decoration: BoxDecoration(
                color: isHovered
                    ? const Color(0xFFF72561).withOpacity(0.8)
                    : const Color(0xFFF72561),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  exportText,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.semiBold,
                    fontSize: 12,
                    height: 1.33,
                    color: const Color(0xFFE1E1E1),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
