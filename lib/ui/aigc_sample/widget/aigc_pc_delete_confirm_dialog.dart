import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class AigcPcDeleteConfirmDialog extends StatelessWidget {
  // 标题
  final String? title;

  // 内容
  final String? content;

  // 确认回调
  final VoidCallback? onConfirm;

  // 取消的回调
  final VoidCallback? onCancel;

  const AigcPcDeleteConfirmDialog({
    super.key,
    this.title,
    this.content,
    this.onConfirm,
    this.onCancel,
  });

  static void show(
    BuildContext context, {
    String? title,
    String? content,
    VoidCallback? onCancel,
    required VoidCallback onConfirm,
  }) {
    PGDialog.showCustomDialog(
      width: 480,
      height: 190,
      radius: 10,
      tag: DialogTags.aigcPcDeleteConfirm,
      child: AigcPcDeleteConfirmDialog(
        onConfirm: onConfirm,
        onCancel: onCancel,
        title: title,
        content: content,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 480,
      height: 190,
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(10),
      ),
      child: // 左右距离父视图16
          Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            SizedBox(
              height: 32,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title ?? '确定删除?',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.medium,
                    ),
                  ),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close, color: Colors.white70),
                      iconSize: 20,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        onCancel?.call();
                        PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              content ?? '删除后将无法找回。',
              style: TextStyle(
                color: const Color(0xFFB0B0B0),
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
              ),
            ),
            const SizedBox(height: 44),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  style: TextButton.styleFrom(
                    backgroundColor: const Color(0xFF383838),
                    minimumSize: const Size(88, 40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  onPressed: () {
                    onCancel?.call();
                    PGDialog.dismiss(
                      tag: DialogTags.aigcPcDeleteConfirm,
                    );
                  },
                  child: Text(
                    '取消',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 14,
                      fontWeight: Fonts.regular,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                TextButton(
                  style: TextButton.styleFrom(
                    backgroundColor: const Color(0xFFF72561),
                    minimumSize: const Size(88, 40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  onPressed: () {
                    onConfirm?.call();
                  },
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: Fonts.defaultFontFamily,
                      fontSize: 14,
                      fontWeight: Fonts.regular,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
