import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_presets/utils/grid_calculator.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_sample_view_model.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_grid_item_view.dart';

class AigcPcSampleGridView extends StatelessWidget {
  const AigcPcSampleGridView({super.key, required this.viewModel});
  final AigcSampleViewModel viewModel;

  // 定义网格项的固定宽高
  static const double minItemWidth = 186.0; // 最小宽度
  static const double minItemHeight = 232.0; // 最小高度
  static const double aspectRatio = minItemWidth / minItemHeight; // 宽高比
  static const double itemSpacing = 16.0; // 固定间距
  static const int maxColumns = 8; // 最大列数

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;

        // 使用修改后的GridCalculator方法计算网格布局
        final gridLayout = GridCalculator.calculateGridLayout(
          containerWidth: availableWidth,
          minItemWidth: minItemWidth,
          minItemHeight: minItemHeight,
          spacing: itemSpacing,
          maxColumns: maxColumns,
        );

        final int columns = gridLayout[0];
        final double itemWidth = gridLayout[1];
        final double itemHeight = gridLayout[2];

        return GridView.builder(
          padding: const EdgeInsets.only(top: 16, bottom: 16),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            crossAxisSpacing: itemSpacing,
            mainAxisSpacing: itemSpacing,
            childAspectRatio: aspectRatio,
          ),
          itemCount: viewModel.aigcSampleList.length,
          itemBuilder: (context, index) {
            return Container(
              width: itemWidth,
              height: itemHeight,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: AigcPcSampleGridItemView(
                model: viewModel.aigcSampleList[index],
                width: itemWidth,
                height: itemHeight,
                onDelete: (id) {
                  viewModel.deleteAigcSample(id);
                },
              ),
            );
          },
        );
      },
    );
  }
}
