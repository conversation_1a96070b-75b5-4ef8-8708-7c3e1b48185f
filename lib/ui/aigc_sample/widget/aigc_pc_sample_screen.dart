import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/providers/aigc_sample_export_polling_provider.dart';
import 'package:turing_art/providers/aigc_sample_list_polling_provider.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_eidt_top_bar_widget.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_sample_view_model.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_grid_view.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_sample_top_list_view.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog2.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class AigcPcSampleScreen extends StatefulWidget {
  const AigcPcSampleScreen({super.key, required this.projectId});

  final String projectId;

  @override
  State<AigcPcSampleScreen> createState() => _AigcPcSampleScreenState();
}

class _AigcPcSampleScreenState extends State<AigcPcSampleScreen> {
  final ValueNotifier<bool> _hoveredBackNotifier = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    // 在页面初始化完成后检查网络连接
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkNetworkConnection();
    });
  }

  /// 检查网络连接状态
  Future<void> _checkNetworkConnection() async {
    try {
      final networkProvider = context.read<NetworkProvider>();
      final isConnected = await networkProvider.isConnected();

      if (!isConnected && mounted) {
        PGDialog.showToast('网络错误，请检查网络连接');
      }
    } catch (e) {
      PGLog.e('检查网络连接失败: $e');
    }
  }

  @override
  void dispose() {
    _hoveredBackNotifier.dispose();
    // 不需要在这里释放viewModel，因为它是由Provider管理的
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽度
    final screenWidth = MediaQuery.of(context).size.width;

    // 计算水平边距
    // 当屏幕宽度 <= 1920 时，边距固定为 160
    // 当屏幕宽度 > 1920 时，边距动态计算，保证内容区域宽度为 1600
    final double horizontalPadding =
        screenWidth <= 1920 ? 160.0 : (screenWidth - 1600) / 2;

    const double topBarHeight = 56.0;
    const double tabBarHeight = 48.0;

    return ChangeNotifierProvider(
      create: (context) {
        final viewModel = AigcSampleViewModel(
          repository: context.read<AigcSampleRepository>(),
          accountRepository: context.read<AccountRepository>(),
          pollingProvider: context.read<AigcSampleListPollingProvider>(),
          exportPollingProvider:
              context.read<AigcSampleExportPollingProvider>(),
          exportManager: context.read<AigcMySampleExportManager>(),
        );
        return viewModel;
      },
      child: Consumer<AigcSampleViewModel>(
        builder: (context, viewModel, _) {
          return Scaffold(
            backgroundColor: const Color(0xFF0D0D0D),
            body: TitleBarWidget(
              backgroundColor: const Color(0xFF0D0D0D),
              funcWidget: AigcEditTopBarWidget(
                score: 0,
                backgroundColor: const Color(0xFF0D0D0D),
                showPurchaseButton: false,
                showMakingCenterButton: false,
                showBackButton: false,
                onPresetPressed: () {
                  // 处理预设点击事件
                  final navigator = GoRouterNavigatorService(context);
                  navigator.navigateToAigcPresets(
                    maskPath: '',
                    previewPath: '',
                  );
                },
                onExportPressed: () {
                  // 处理导出点击事件，默认选中AIGC导出
                  ExportListDialog2.show(defaultExportType: ExportType.aigc);
                },
              ),
              child: Column(
                children: [
                  // TopBar区域
                  Container(
                    height: topBarHeight,
                    margin: EdgeInsets.symmetric(horizontal: horizontalPadding),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ValueListenableBuilder<bool>(
                          valueListenable: _hoveredBackNotifier,
                          builder: (context, isHovered, _) {
                            return PlatformMouseRegion(
                              onEnter: (_) => _hoveredBackNotifier.value = true,
                              onExit: (_) => _hoveredBackNotifier.value = false,
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.of(context).pop();
                                },
                                child: Container(
                                  height: 32,
                                  decoration: BoxDecoration(
                                    color: isHovered
                                        ? Colors.white.withOpacity(0.05)
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 2),
                                  child: Row(
                                    children: [
                                      Image.asset(
                                        'assets/icons/aigc_presets_back_icon.png',
                                        color: Colors.white,
                                        width: 24,
                                        height: 24,
                                      ),
                                      const SizedBox(width: 1),
                                      Text(
                                        'AI 打样中心',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontFamily: Fonts.defaultFontFamily,
                                          fontWeight: Fonts.medium,
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  // Content区域
                  Expanded(
                    child: Container(
                      margin:
                          EdgeInsets.symmetric(horizontal: horizontalPadding),
                      color: const Color(0xFF0D0D0D),
                      child: Column(
                        children: [
                          // 自定义Tab切换栏
                          SizedBox(
                              height: tabBarHeight,
                              width: double.infinity,
                              child: Stack(
                                children: [
                                  Container(
                                    width: double.infinity,
                                    height: tabBarHeight,
                                    decoration: BoxDecoration(
                                      border: Border(
                                        bottom: BorderSide(
                                          color: Colors.white
                                              .withOpacity(0.1), // 白色透明度0.1
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      // 已生成标签
                                      _buildTabItem(0, '已完成'),
                                      const SizedBox(width: 32), // 间距32
                                      // 正在生成标签
                                      _buildTabItem(1, '正在处理'),
                                    ],
                                  ),
                                ],
                              )),
                          const SizedBox(height: 12),
                          Container(
                            width: double.infinity,
                            height: 40,
                            alignment: Alignment.centerLeft,
                            child: AigcPcSampleTopListView(
                              items: viewModel.currentTabProjectList
                                  .map((e) => e.projectName)
                                  .toList(),
                              selectedIndex: viewModel.selectedProjectIndex,
                              onItemTap: (index) {
                                setState(() {
                                  viewModel.setSelectedProjectIndex(index);
                                });
                              },
                            ),
                          ),
                          const SizedBox(height: 0),
                          // 内容区域
                          Expanded(
                            child: _buildTabContent(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabItem(int index, String title) {
    return Consumer<AigcSampleViewModel>(
      builder: (context, viewModel, _) {
        final isSelected = viewModel.selectedTab == index;
        PGLog.d('构建Tab项: $title, index: $index, 当前选中: $isSelected');

        return InkWell(
          onTap: () {
            // 直接调用viewModel的方法，不需要setState
            viewModel.setSelectedTab(index);
          },
          child: Container(
            height: 48,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: isSelected ? Colors.white : Colors.transparent,
                  width: 2,
                ),
              ),
            ),
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : Colors.white.withOpacity(0.5),
                      fontSize: 14,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: isSelected ? Fonts.semiBold : Fonts.medium,
                    ),
                  ),
                  // 为已完成Tab添加右侧圆点
                  if (index == 0 &&
                      viewModel.newCompletedSampleList.isNotEmpty &&
                      viewModel.selectedTab != 0)
                    Container(
                      width: 8,
                      height: 8,
                      margin: const EdgeInsets.only(left: 0, bottom: 8),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  // 为正在处理Tab添加右侧个数文本
                  if (index == 1 && viewModel.runningSampleList.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(left: 4, top: 2),
                      width: 16,
                      height: 16,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${viewModel.runningSampleList.length}',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 12,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.semiBold,
                          height: 1.0,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTabContent() {
    return Consumer<AigcSampleViewModel>(
      builder: (context, viewModel, _) {
        PGLog.d(
            '构建Tab内容，当前选中Tab: ${viewModel.selectedTab}, 列表项数量: ${viewModel.aigcSampleList.length}');
        return Stack(
          children: [
            AigcPcSampleGridView(viewModel: viewModel),
            if (viewModel.aigcSampleList.isEmpty) _buildEmptyContent(viewModel),
          ],
        );
      },
    );
  }

  Widget _buildEmptyContent(AigcSampleViewModel viewModel) {
    return Center(
      child: SizedBox(
        width: 260,
        height: 184,
        child: Column(
          children: [
            Image.asset(
              'assets/icons/aigc_sample_empty_icon.png',
              width: 260,
              height: 130,
            ),
            const SizedBox(height: 10),
            SizedBox(
              width: 260,
              height: 18,
              child: Center(
                child: Text(
                  '当前暂无${viewModel.selectedTab == 0 ? '已完成' : '正在处理'}的项目',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 14,
                    fontWeight: Fonts.medium,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
