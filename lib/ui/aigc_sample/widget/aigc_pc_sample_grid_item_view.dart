import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_sample_view_model.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

class AigcPcSampleGridItemView extends StatefulWidget {
  const AigcPcSampleGridItemView({
    super.key,
    required this.model,
    this.width,
    this.height,
    this.onDelete,
  });

  final AigcSampleModel model;
  final double? width;
  final double? height;
  // 删除回调
  final void Function(String id)? onDelete;

  @override
  State<AigcPcSampleGridItemView> createState() =>
      _AigcPcSampleGridItemViewState();
}

class _AigcPcSampleGridItemViewState extends State<AigcPcSampleGridItemView> {
  bool onHover = false;

  @override
  Widget build(BuildContext context) {
    // 获取父容器传入的宽高，如果未传入则使用默认值
    final itemWidth = widget.width ?? 186.0;
    final imageSize = itemWidth; // 图片宽高相等，为正方形
    final textWidth = itemWidth;

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (event) {
        PGLog.d('鼠标进入: ${widget.model.name}');
        setState(() {
          onHover = true;
        });
      },
      onExit: (event) {
        PGLog.d('鼠标离开: ${widget.model.name}');
        setState(() {
          onHover = false;
        });
      },
      child: GestureDetector(
        onTap: () async {
          PGLog.d('点击打样: ${widget.model.name}');
          final buildContext = context;
          final networkProvider = context.read<NetworkProvider>();
          if (!await networkProvider.isConnected()) {
            PGDialog.showToast('网络错误，请检查网络连接');
            return;
          }
          // 导航到详情页面
          if (buildContext.mounted) {
            final navigator = GoRouterNavigatorService(buildContext);
            final result =
                await navigator.navigateToAigcSampleDetail(widget.model.id);
            if (buildContext.mounted && result == 'refresh') {
              final viewModel = buildContext.read<AigcSampleViewModel>();
              // 删除新增的已完成样本（只清除新完成标记，不触发全局刷新）
              viewModel.deleteNewCompletedSample(widget.model);
              viewModel.loadSampleList(refresh: true);
            }
          }
        },
        child: Column(
          children: [
            Stack(
              children: [
                // 图片容器
                Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: _buildCoverImage(widget.model, imageSize),
                  ),
                ),
                if (widget.model.status == AigcRequestConst.completed &&
                    context
                        .read<AigcSampleViewModel>()
                        .newCompletedSampleList
                        .any((e) => e.id == widget.model.id)) ...[
                  _buildNewCircleIcon(),
                  Positioned(
                    right: 0,
                    top: 0,
                    child: _buildNewIcon(),
                  ),
                ],
                // 左下角文案
                Positioned(
                  left: 8,
                  bottom: 8,
                  child: _buildCreativeLabel(widget.model),
                ),

                // 右下角删除按钮，仅在完成tab下显示
                if (widget.model.status != AigcRequestConst.running && onHover)
                  Positioned(
                    right: 8,
                    bottom: 8,
                    child: _buildDeleteButton(context),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: textWidth,
              height: 18,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  widget.model.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    color: const Color(0xFFFFFFFF),
                  ),
                  textAlign: TextAlign.left,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(height: 2),
            SizedBox(
              width: textWidth,
              height: 18,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  //  model.updateAt 转为日期 和时分
                  widget.model.status == AigcRequestConst.failed
                      ? '生成失败'
                      : widget.model.status == AigcRequestConst.running
                          ? '生成中...'
                          : formatDate(widget.model.updateAt),
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    color: const Color(0xFFFFFFFF).withOpacity(0.5),
                  ),
                  textAlign: TextAlign.left,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverImage(AigcSampleModel model, double imageSize) {
    final thumbnailUrl = model.findUseableThumbnailUrl();
    // 如果预设状态为失败，则显示失败图标
    if (model.status == AigcRequestConst.failed) {
      return Image.asset(
        'assets/icons/aigc_presets_error_icon.png',
        width: imageSize,
        height: imageSize,
        fit: BoxFit.cover,
      );
    }
    return thumbnailUrl != null && thumbnailUrl.isNotEmpty
        ? CachedImageWidget(
            imageUrl: thumbnailUrl,
            width: imageSize,
            height: imageSize,
            fit: BoxFit.cover,
            placeholder: AigcItemAnimation(
              child: Image.asset(
                'assets/icons/aigc_presets_loading_icon.png',
                width: imageSize,
                height: imageSize,
                fit: BoxFit.cover,
              ),
            ),
            showProgress: false,
            errorWidget: Image.asset(
              'assets/icons/aigc_presets_loading_icon.png',
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
            ),
          )
        : AigcItemAnimation(
            child: Image.asset(
              'assets/icons/aigc_presets_loading_icon.png',
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
            ),
          );
  }

  // new标签显示时特殊样式
  Widget _buildNewCircleIcon() {
    return Container(
      width: widget.width,
      height: widget.width,
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          'assets/icons/aigc_sample_new_circle_icon.png',
          width: widget.width,
          height: widget.width,
        ),
      ),
    );
  }

  // 构建导出loading样式
  Widget _buildCreativeLabel(AigcSampleModel model) {
    // 判断是否正在导出
    var isExport = false;
    for (var effect in model.effects) {
      if (effect.exportStatus == AigcRequestConst.running) {
        isExport = true;
        break;
      }
    }
    // 如果正在导出，则显示导出状态
    if (!isExport) {
      return const SizedBox.shrink();
    }
    return Container(
      height: 24,
      padding: const EdgeInsets.symmetric(horizontal: 0),
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F).withOpacity(0.8),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 添加旋转的loading图标，距离左侧为2
          const Padding(
            padding: EdgeInsets.only(left: 2),
            child: _RotatingImage(
              imagePath: 'assets/icons/aigc_sample_running_icon.png',
              size: 20,
            ),
          ),
          // 固定间距
          const SizedBox(width: 4),
          // 原有的文本，距离右侧为4
          Padding(
            padding: const EdgeInsets.only(right: 4),
            child: Text(
              "正在导出",
              style: TextStyle(
                color: const Color(0xFFFFFFFF),
                fontSize: 12,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewIcon() {
    return Image.asset(
      'assets/icons/aigc_sample_new_icon.png',
      width: 53,
      height: 42,
    );
  }

  Widget _buildDeleteButton(BuildContext context) {
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // 删除操作
          AigcPcDeleteConfirmDialog.show(
            context,
            title: '确定删除?',
            content: '删除后将无法找回。',
            onConfirm: () {
              // 删除操作
              PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
              widget.onDelete?.call(widget.model.id);
            },
          );
        },
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 底部模糊层
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: const Color(0xFF1F1F1F),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.1),
                      width: 0.5,
                    ),
                  ),
                ),
              ),
            ),
            // 按钮内容
            Center(
              child: Image.asset(
                'assets/icons/aigc_sample_delete_icon.png',
                width: 24,
                height: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return "$month-$day $hour:$minute";
  }
}

// 创建一个旋转图像组件
class _RotatingImage extends StatefulWidget {
  const _RotatingImage({
    required this.imagePath,
    required this.size,
  });

  final String imagePath;
  final double size;

  @override
  State<_RotatingImage> createState() => _RotatingImageState();
}

class _RotatingImageState extends State<_RotatingImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: Image.asset(
        widget.imagePath,
        width: widget.size,
        height: widget.size,
      ),
    );
  }
}
