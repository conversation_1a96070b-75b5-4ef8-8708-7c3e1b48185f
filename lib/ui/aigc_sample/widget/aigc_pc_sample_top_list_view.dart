import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

class AigcPcSampleTopListView extends StatefulWidget {
  const AigcPcSampleTopListView({
    super.key,
    required this.items,
    required this.onItemTap,
    this.selectedIndex = 0,
  });

  final List<String> items;
  final Function(int index) onItemTap;
  final int selectedIndex;

  @override
  State<AigcPcSampleTopListView> createState() =>
      _AigcPcSampleTopListViewState();
}

class _AigcPcSampleTopListViewState extends State<AigcPcSampleTopListView> {
  final List<bool> _hoveredStates = [];
  final ScrollController _scrollController = ScrollController();
  bool _canScrollLeft = false;
  bool _canScrollRight = false;
  double _containerWidth = 0;

  @override
  void initState() {
    super.initState();
    _initHoveredStates();
    _scrollController.addListener(_updateScrollButtonStates);
    // 延迟检查滚动状态，确保布局完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateScrollButtonStates();
    });
  }

  @override
  void didUpdateWidget(AigcPcSampleTopListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items.length != widget.items.length) {
      _initHoveredStates();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateScrollButtonStates();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateScrollButtonStates);
    _scrollController.dispose();
    super.dispose();
  }

  void _initHoveredStates() {
    _hoveredStates.clear();
    for (int i = 0; i < widget.items.length; i++) {
      _hoveredStates.add(false);
    }
  }

  void _updateScrollButtonStates() {
    if (!mounted || !_scrollController.hasClients) {
      return;
    }

    setState(() {
      _canScrollLeft = _scrollController.offset > 0;
      _canScrollRight =
          _scrollController.offset < _scrollController.position.maxScrollExtent;
    });
  }

  void _scrollLeft() {
    if (!_canScrollLeft) {
      return;
    }

    final double scrollDistance = _containerWidth / 2;
    final double targetOffset = (_scrollController.offset - scrollDistance)
        .clamp(0.0, _scrollController.position.maxScrollExtent);

    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _scrollRight() {
    if (!_canScrollRight) {
      return;
    }

    final double scrollDistance = _containerWidth / 2;
    final double targetOffset = (_scrollController.offset + scrollDistance)
        .clamp(0.0, _scrollController.position.maxScrollExtent);

    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 32,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 检查容器宽度是否发生变化
          final newWidth = constraints.maxWidth;
          final widthChanged = _containerWidth != newWidth;
          _containerWidth = newWidth;

          // 如果宽度发生变化，需要重新计算滚动状态
          if (widthChanged) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _updateScrollButtonStates();
            });
          }

          return Stack(
            children: [
              // 主要内容区域
              Listener(
                onPointerSignal: (pointerSignal) {
                  // 处理鼠标滚轮事件，转换为水平滚动
                  if (pointerSignal is PointerScrollEvent) {
                    final double scrollDelta = pointerSignal.scrollDelta.dy;
                    _scrollController.animateTo(
                      _scrollController.offset + scrollDelta,
                      duration: const Duration(milliseconds: 100),
                      curve: Curves.easeOut,
                    );
                  }
                },
                child: SingleChildScrollView(
                  controller: _scrollController,
                  scrollDirection: Axis.horizontal,
                  physics: const ClampingScrollPhysics(),
                  child: Container(
                    height: 32,
                    alignment: Alignment.topLeft,
                    child: Row(
                      children: List.generate(
                        widget.items.length,
                        (index) => Padding(
                          padding: EdgeInsets.only(
                            right: index < widget.items.length - 1 ? 8 : 0,
                          ),
                          child: _buildItem(index),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // 左侧渐变蒙层和按钮
              if (_canScrollLeft)
                Positioned(
                  left: 0,
                  top: 0,
                  bottom: 0,
                  child: Container(
                    width: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          const Color(0xFF0D0D0D),
                          const Color(0xFF0D0D0D).withOpacity(0.0),
                        ],
                      ),
                    ),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 0),
                        child: PlatformMouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: _scrollLeft,
                            child: Container(
                              width: 28,
                              height: 28,
                              decoration: BoxDecoration(
                                color: const Color(0xFF2E2E2E),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: const Image(
                                image: AssetImage(
                                  'assets/icons/aigc_sample_list_scroll_left.png',
                                ),
                                width: 24,
                                height: 24,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

              // 右侧渐变蒙层和按钮
              if (_canScrollRight)
                Positioned(
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Container(
                    width: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          const Color(0xFF0D0D0D).withOpacity(0.0),
                          const Color(0xFF0D0D0D),
                        ],
                      ),
                    ),
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 0),
                        child: PlatformMouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: _scrollRight,
                            child: Container(
                              width: 28,
                              height: 28,
                              decoration: BoxDecoration(
                                color: const Color(0xFF2E2E2E),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: const Image(
                                image: AssetImage(
                                  'assets/icons/aigc_sample_list_scroll_right.png',
                                ),
                                width: 24,
                                height: 24,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildItem(int index) {
    final bool isSelected = index == widget.selectedIndex;
    final bool isHovered = _hoveredStates[index];

    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _hoveredStates[index] = true),
      onExit: (_) => setState(() => _hoveredStates[index] = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => widget.onItemTap(index),
        child: Container(
          height: 32,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF2E2E2E)
                : isHovered
                    ? const Color(0xFF2A2A2A)
                    : const Color(0xFF1F1F1F),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isSelected
                  ? Colors.white.withOpacity(0.1)
                  : Colors.transparent,
              width: 0.5,
            ),
          ),
          child: Center(
            child: Text(
              widget.items[index],
              style: TextStyle(
                color:
                    isSelected ? Colors.white : Colors.white.withOpacity(0.5),
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: isSelected ? Fonts.semiBold : Fonts.medium,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
