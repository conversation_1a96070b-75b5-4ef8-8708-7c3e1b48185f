import 'dart:math' as math;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_constant.dart';

/// AIGC图片预览ViewModel
/// 负责管理图片的缩放、拖拽和约束逻辑
class AigcImagePreviewViewModel extends ChangeNotifier {
  // 缩放和拖拽相关状态
  double _scale = 1.0;
  Offset _offset = Offset.zero;

  // 缩放范围限制
  static const double _minScale = 0.5;
  static const double _maxScale = AigcConstant.maxCanvasScale;

  // 容器尺寸
  static const double _containerWidth = 957.0;
  static const double _containerHeight = 957.0;

  // 缓存父视图约束，用于约束计算
  BoxConstraints? _parentConstraints;

  set scale(double value) {
    _scale = value;
    notifyListeners();
  }

  /// 获取当前缩放比例
  double get scale => _scale;

  /// 获取当前偏移量
  Offset get offset => _offset;

  /// 设置父视图约束
  void setParentConstraints(BoxConstraints constraints) {
    _parentConstraints = constraints;
  }

  /// 重置缩放和位置到默认状态
  void resetTransform() {
    _scale = 1.0;
    _offset = Offset.zero;
    notifyListeners();
  }

  /// 处理滚轮缩放
  void handleScroll(PointerSignalEvent event) {
    if (event is PointerScrollEvent) {
      final double delta = event.scrollDelta.dy;
      final double scaleFactor = delta > 0 ? 0.9 : 1.1;
      final double newScale =
          (_scale * scaleFactor).clamp(_minScale, _maxScale);

      if (newScale != _scale) {
        // 判断是放大还是缩小
        final bool isScalingDown = newScale < _scale;

        if (isScalingDown) {
          // 缩小时需要检查是否满足5%重叠面积要求
          if (_checkOverlapArea(
              _offset,
              newScale,
              _parentConstraints?.maxWidth ?? 957,
              _parentConstraints?.maxHeight ?? 957)) {
            // 满足要求，应用缩小
            _scale = newScale;
            notifyListeners();
          }
        } else {
          // 放大时不需要检查重叠面积，直接应用
          _scale = newScale;
          notifyListeners();
        }
      }
    }
  }

  /// 处理拖拽
  void handlePanUpdate(DragUpdateDetails details) {
    final Offset newOffset = _offset + details.delta;
    final constrainedOffset = _constrainOffset(newOffset, _scale);

    if (_offset != constrainedOffset) {
      _offset = constrainedOffset;
      notifyListeners();
    }
  }

  /// 约束偏移量，确保图片与父视图相交面积不少于5%
  Offset _constrainOffset(Offset offset, double scale) {
    // 使用缓存的约束
    if (_parentConstraints == null) {
      return offset; // 如果没有约束信息，返回原偏移量
    }

    // 使用父视图的实际可用空间
    final double parentWidth = _parentConstraints!.maxWidth;
    final double parentHeight = _parentConstraints!.maxHeight;

    // 检查新偏移量是否满足5%重叠面积要求
    final bool isValid =
        _checkOverlapArea(offset, scale, parentWidth, parentHeight);

    if (isValid) {
      return offset;
    }
    return _offset;
  }

  /// 检查在给定偏移量和缩放比例下是否满足5%重叠面积要求
  bool _checkOverlapArea(Offset newOffset, double newScale, double parentWidth,
      double parentHeight) {
    // 计算缩放后的图片尺寸
    final scaledContentSize =
        Size(_containerWidth * newScale, _containerHeight * newScale);

    // 计算图片在父视图中的位置（居中对齐）
    final centerX = parentWidth / 2 - scaledContentSize.width / 2;
    final centerY = parentHeight / 2 - scaledContentSize.height / 2;

    // 计算图片的实际边界
    final imageLeft = centerX + newOffset.dx;
    final imageTop = centerY + newOffset.dy;
    final imageRight = imageLeft + scaledContentSize.width;
    final imageBottom = imageTop + scaledContentSize.height;

    // 父视图边界
    const parentLeft = 0.0;
    const parentTop = 0.0;
    final parentRight = parentWidth;
    final parentBottom = parentHeight;

    // 计算重叠区域
    final overlapLeft = math.max(imageLeft, parentLeft);
    final overlapTop = math.max(imageTop, parentTop);
    final overlapRight = math.min(imageRight, parentRight);
    final overlapBottom = math.min(imageBottom, parentBottom);

    // 计算重叠面积
    final overlapWidth = math.max(0.0, overlapRight - overlapLeft);
    final overlapHeight = math.max(0.0, overlapBottom - overlapTop);
    final overlapArea = overlapWidth * overlapHeight;

    // 计算父视图总面积和最小重叠面积（5%）
    final parentArea = parentWidth * parentHeight;
    final minOverlapArea = parentArea * 0.05;

    // 检查重叠面积是否满足5%要求
    final result = overlapArea >= minOverlapArea;
    return result;
  }
}
