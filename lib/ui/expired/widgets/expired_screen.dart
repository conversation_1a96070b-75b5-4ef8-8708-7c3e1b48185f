import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

import '../../../utils/app_info.dart';
import '../../core/ui/blur_container.dart';
import '../../login/widgets/login_background/login_background.dart';

class ExpiredScreen extends StatefulWidget {
  const ExpiredScreen({super.key});

  @override
  State<ExpiredScreen> createState() => _ExpiredScreenState();
}

class _ExpiredScreenState extends State<ExpiredScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LoginBackground(
        child: Center(
          child: BlurContainer(
            width: double.infinity,
            height: double.infinity,
            backgroundColor: Colors.black,
            backgroundOpacity: 0.4,
            blur: 24,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(),
                const SizedBox(height: 24),
                Text('请连接网络以继续使用图灵精修',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.semiBold,
                    )),
                const SizedBox(height: 8),
                Text('连接网络仍然无法使用“图灵精修”？',
                    style: TextStyle(
                      color: const Color(0xFFEBF2F5).withAlpha(150),
                      fontSize: 16,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                    )),
                const SizedBox(height: 2),
                Text('请联系提供方或邮件咨询 *******************以继续使用。',
                    style: TextStyle(
                      color: const Color(0xFFEBF2F5).withAlpha(150),
                      fontSize: 16,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                    )),
                const Spacer(),
                Text(
                    '${AppInfoExtension.getCopyrightString()} | ${AppInfoExtension.getVersionString()}',
                    style: TextStyle(
                      color: const Color(0xFFEBF2F5).withAlpha(150),
                      fontSize: 10,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                    )),
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
