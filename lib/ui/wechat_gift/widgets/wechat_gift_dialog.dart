import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/ops_operation/operation_activity.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class WechatGiftDialog extends StatefulWidget {
  // 微信福利列表（有item才会弹起此弹窗）
  final List<OperationActivity> wechatGiftList;

  const WechatGiftDialog({
    super.key,
    required this.wechatGiftList,
  });

  /// public方法：显示对话框
  static void show(List<OperationActivity> wechatGiftList) {
    if (PGDialog.isDialogVisible(DialogTags.wechatGift)) {
      PGLog.d('WechatGiftDialog show, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: 403,
      height: 520,
      needBlur: false,
      tag: DialogTags.wechatGift,
      child: WechatGiftDialog(
        wechatGiftList: wechatGiftList,
      ),
    );
  }

  /// public方法：在Unity窗口上显示对话框
  static void showOnUnity(List<OperationActivity> wechatGiftList) {
    if (PGDialog.isDialogVisible(DialogTags.wechatGift)) {
      PGLog.d('WechatGiftDialog showOnUnity, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialogOnUnity(
      width: 403,
      height: 520,
      needBlur: false,
      tag: DialogTags.wechatGift,
      child: WechatGiftDialog(
        wechatGiftList: wechatGiftList,
      ),
    );
  }

  @override
  State<WechatGiftDialog> createState() => _WechatGiftDialogState();
}

class _WechatGiftDialogState extends State<WechatGiftDialog> {
  @override
  Widget build(BuildContext context) {
    final mainGift = widget.wechatGiftList.first;
    final equityList = widget.wechatGiftList.sublist(1);

    return Center(
      child: Container(
        width: 403,
        height: 520,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            stops: [0.4976, 1.0206],
            colors: [
              Color(0xFF171717),
              Color(0xFF1D1D1D),
            ],
            transform: GradientRotation(307.5 * 3.14159 / 180),
          ),
          borderRadius: BorderRadius.circular(0),
        ),
        child: Stack(
          clipBehavior: Clip.hardEdge,
          children: [
            Positioned(
              top: 40,
              left: 179,
              child: Image.asset(
                'assets/icons/wechat_back_logo.png',
                width: 324,
                height: 607,
              ),
            ),

            // 主内容
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 57),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 40),

                  // 标题
                  SizedBox(
                    height: 56,
                    child: ShaderMask(
                      shaderCallback: (Rect bounds) {
                        return const LinearGradient(
                          colors: [
                            Colors.white,
                            Color(0xFFFFCDDB),
                            Colors.white
                          ],
                          stops: [0.034, 0.5054, 1.0262],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ).createShader(bounds);
                      },
                      child: Text(
                        widget.wechatGiftList.first.title ?? '',
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.bold,
                          fontSize: 24,
                          height: 28 / 24,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // 权益列表
                  SizedBox(
                    height: (16 + 4) *
                        equityList.length.toDouble(), // 权益+主要内容 = activityList
                    child: ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: equityList.length,
                      itemBuilder: (context, index) {
                        final equity = equityList[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: SizedBox(
                            height: 16,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Image.asset(
                                  'assets/icons/wechat_equity_icon.png',
                                  width: 12,
                                  height: 12,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    equity.count != null && equity.count! > 0
                                        ? '${equity.displayName ?? ''} * ${equity.count}'
                                        : equity.displayName ?? '',
                                    style: TextStyle(
                                      fontFamily: Fonts.defaultFontFamily,
                                      fontWeight: Fonts.regular,
                                      fontSize: 12,
                                      height: 18 / 12,
                                      color: const Color(0xFFE1E2E5),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  const SizedBox(height: 40),
                  SizedBox(
                    width: 120,
                    height: 120,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: mainGift.wechartUrl != null &&
                              mainGift.wechartUrl!.url.isNotEmpty
                          ? Image.network(
                              mainGift.wechartUrl!.url,
                              width: 120,
                              height: 120,
                              fit: BoxFit.cover,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) {
                                  return child;
                                }
                                return const Center(
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Icon(Icons.error,
                                      color: Color(0xFFE1E2E5)),
                                );
                              },
                            )
                          : Container(
                              color: const Color(0xFF2A2A2A),
                              child: const Center(
                                child: Icon(Icons.qr_code,
                                    color: Color(0xFFE1E2E5)),
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),

            // 二维码介绍区域
            Positioned(
              bottom: 44,
              left: 32,
              child: SizedBox(
                width: 326,
                height: 85,
                child: Stack(
                  children: [
                    Image.asset(
                      'assets/icons/wechat_intro_back.png',
                      width: 326,
                      height: 73,
                      fit: BoxFit.fill,
                    ),
                    Positioned(
                      left: 25,
                      top: 23,
                      child: Text(
                        mainGift.selfIntroduction ?? '',
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.regular,
                          fontSize: 12,
                          color: const Color(0xFFE1E2E5).withOpacity(0.65),
                        ),
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 40,
                      child: Text(
                        mainGift.scanCodeTip ?? '',
                        style: const TextStyle(
                          fontFamily: Fonts.fontFamilyPF,
                          fontWeight: Fonts.semiBold,
                          fontSize: 12,
                          color: Color(0xFFE1E2E5),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            Positioned(
              top: 16,
              right: 16,
              child: GestureDetector(
                onTap: () {
                  PGDialog.dismiss(tag: DialogTags.wechatGift);
                },
                child: Image.asset(
                  'assets/icons/home_window_close.png',
                  width: 24,
                  height: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
