import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/login/select_account/model/login_account_model.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 登录选择账号对话框
class LoginSelectAccountDialog extends StatefulWidget {
  final Function(LoginAccountModel) onSelectAccount;
  final List<LoginAccountModel> _accounts;
  final String _lastLoginStoreId;
  List<LoginAccountModel> get accounts => _accounts;

  const LoginSelectAccountDialog({
    super.key,
    required this.onSelectAccount,
    required List<LoginAccountModel> accounts,
    required String lastLoginStoreId,
  })  : _accounts = accounts,
        _lastLoginStoreId = lastLoginStoreId;

  /// 显示登录选择账号对话框
  static void show({
    required String lastLoginStoreId,
    required List<LoginAccountModel> accounts,
    required Function(LoginAccountModel) onSelectAccount,
  }) {
    // 初始高度使用最小值
    double dialogHeight = _calculateDialogHeight(accounts.length);
    // 创建对话框
    Widget widget = _buildDialog(lastLoginStoreId, accounts, onSelectAccount);
    // 显示对话框
    PGDialog.showCustomDialog(
      tag: DialogTags.loginSelectAccount,
      width: 328,
      needBlur: true,
      height: dialogHeight,
      child: widget,
      needGesture: true,
    );
  }

  /// 计算对话框高度
  static double _calculateDialogHeight(int accountCount) {
    // 顶部标题和说明的高度
    const double headerHeight = 97.0;

    // 每个列表项的高度和间距
    const double itemHeight = 72.0;
    const double itemSpacing = 12.0;

    // 计算列表高度: 每个item高度 * 数量 + 间距 * (数量-1)
    final double listHeight = itemHeight * accountCount +
        (accountCount > 1 ? (accountCount - 1) * itemSpacing : 0);

    // 底部间距
    const double bottomPadding = 24.0;

    // 总高度
    final double totalHeight = headerHeight + listHeight + bottomPadding;

    // 限制在最小和最大高度之间
    return totalHeight.clamp(277.0, 481.0);
  }

  static Widget _buildDialog(
      String lastLoginStoreId,
      List<LoginAccountModel> accounts,
      Function(LoginAccountModel) onSelectAccount) {
    return LoginSelectAccountDialog(
      lastLoginStoreId: lastLoginStoreId,
      accounts: accounts,
      onSelectAccount: onSelectAccount,
    );
  }

  @override
  State<LoginSelectAccountDialog> createState() =>
      _LoginSelectAccountDialogState();
}

class _LoginSelectAccountDialogState extends State<LoginSelectAccountDialog> {
  final ScrollController _scrollController = ScrollController();
  bool _showGradient = false;
  int? _hoveredIndex;

  // 添加一个标志变量，用于防止多次点击
  bool _isProcessingSelection = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_updateGradientVisibility);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateGradientVisibility();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateGradientVisibility);
    _scrollController.dispose();
    super.dispose();
  }

  void _updateGradientVisibility() {
    final bool shouldShowGradient = _scrollController.hasClients &&
        _scrollController.position.pixels <
            _scrollController.position.maxScrollExtent;

    if (shouldShowGradient != _showGradient) {
      setState(() {
        _showGradient = shouldShowGradient;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 328,
      decoration: BoxDecoration(
        color: const Color(0xFF121315),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildAccountList(),
              ),
            ],
          ),
          Positioned(
            top: 12,
            right: 12,
            child: GestureDetector(
              onTap: () => PGDialog.dismiss(tag: DialogTags.loginSelectAccount),
              child: SizedBox(
                width: 24,
                height: 24,
                child: Image.asset(
                  'assets/icons/dialog_close.png',
                  width: 24,
                  height: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 24, right: 24),
      child: Column(
        children: [
          Container(
            height: 28,
            width: double.infinity,
            alignment: Alignment.center,
            child: Text(
              '选择登录账号',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: const Color(0xFFE1E2E5),
              ),
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 17,
            width: double.infinity,
            alignment: Alignment.center,
            child: Text(
              '您的手机号绑定了多个账号，请选择：',
              style: TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFEBEDF5).withOpacity(0.65),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountList() {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 24),
      child: Stack(
        children: [
          RawScrollbar(
            thumbVisibility: widget.accounts.length > 5,
            thumbColor: const Color(0xFFEBEDF5).withOpacity(0.15),
            radius: const Radius.circular(2),
            thickness: 4,
            controller: _scrollController,
            child: ListView.separated(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              itemCount: widget.accounts.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                return _buildAccountItem(widget.accounts[index], index);
              },
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            height: 30,
            child: Visibility(
              visible: _showGradient,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF121315).withOpacity(0),
                      const Color(0xFF121315),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountItem(LoginAccountModel account, int index) {
    final bool isHovered = _hoveredIndex == index;
    final bool isLastLogin = account.storeId == widget._lastLoginStoreId;

    final bool useHighlightStyle = isHovered;

    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredIndex = index),
      onExit: (_) => setState(() => _hoveredIndex = null),
      child: GestureDetector(
        onTap: _isProcessingSelection
            ? null // 如果正在处理选择，则禁用点击
            : () {
                // 设置处理标志，防止多次点击
                setState(() {
                  _isProcessingSelection = true;
                });

                // 延迟关闭对话框和回调，给用户视觉反馈
                Future.delayed(const Duration(milliseconds: 500), () {
                  // 如果组件已经销毁，不执行后续操作
                  if (!mounted) return;

                  PGDialog.dismiss(tag: DialogTags.loginSelectAccount);
                  widget.onSelectAccount(account);
                });
              },
        child: Container(
          height: 72,
          decoration: BoxDecoration(
            color: useHighlightStyle
                ? const Color(0xFF1B1C1F)
                : const Color(0xFF121315),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: useHighlightStyle
                  ? const Color(0xFFF72561)
                  : const Color(0xFF2A2D30),
              width: 1.0,
            ),
            boxShadow: useHighlightStyle
                ? [
                    BoxShadow(
                      color: const Color(0xFFF72561).withOpacity(0.3),
                      offset: const Offset(0, 0),
                      blurRadius: 6,
                      spreadRadius: 1,
                    )
                  ]
                : null,
          ),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: SizedBox(
                          height: 20,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                account.nickname,
                                style: TextStyle(
                                  fontFamily: Fonts.defaultFontFamily,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                  color: const Color(0xFFE1E2E5),
                                ),
                              ),
                              const SizedBox(width: 7),
                              Container(
                                height: 20,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 2),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  border: Border.all(
                                    color: const Color(0xFFEBEDF5)
                                        .withOpacity(0.2),
                                    width: 0.5,
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  account.type.description,
                                  style: TextStyle(
                                    fontFamily: Fonts.defaultFontFamily,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w400,
                                    color: const Color(0xFFEBEDF5)
                                        .withOpacity(0.8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: SizedBox(
                          height: 16,
                          child: Text(
                            '剩余片量 ${account.remainingQuota}',
                            style: TextStyle(
                              fontFamily: Fonts.defaultFontFamily,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xFFEBEDF5).withOpacity(0.8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (isLastLogin)
                    Container(
                      alignment: Alignment.center,
                      child: Text(
                        '上次登录',
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFFEBEDF5).withOpacity(0.65),
                        ),
                      ),
                    ),
                  SizedBox(width: isLastLogin ? 2 : 0),
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: Image.asset(
                      'assets/icons/page_next.png',
                      width: 20,
                      height: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
