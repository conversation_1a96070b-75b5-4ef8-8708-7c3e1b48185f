/// 账号类型
enum AccountType {
  // 主账号
  primary,
  // 子账号
  restricted,
}

// 扩展AccountType枚举
extension AccountTypeExtension on AccountType {
  String get description => this == AccountType.primary ? '主账号' : '子账号';
}

/// 登录账号模型
class LoginAccountModel {
  final String nickname; // 昵称/手机号
  final AccountType type; // 账号类型
  final String remainingQuota; // 剩余片量
  final String totalQuota; // 总片量
  final String storeId; // 店铺ID
  LoginAccountModel({
    required this.nickname,
    required this.type,
    required this.remainingQuota,
    required this.totalQuota,
    required this.storeId,
  });
}
