import 'package:flutter/material.dart';
import 'package:turing_art/utils/app_info.dart';

import '../../../core/themes/fonts.dart';

class LoginHeaderPc extends StatelessWidget {
  const LoginHeaderPc({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 48,
          child: Image.asset('assets/icons/launch_title.png'),
        ),
        const SizedBox(height: 2),
        Text(
          AppInfoExtension.getVersionString(),
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: Color(0xFFE1E2E5),
            fontFamily: Fonts.fontFamilySF,
            fontSize: 12,
            fontWeight: Fonts.medium,
          ),
        ),
      ],
    );
  }
}
