import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/ui/h5_webview.dart';

import '../../../../constants/assets.dart';
import '../../../../utils/app_constants.dart';
import '../../../../utils/pg_dialog/dialog_tags.dart';
import '../../../../utils/pg_dialog/pg_dialog.dart';
import '../../../../utils/pg_log.dart';
import '../../../../utils/url_launcher_util.dart';
import '../../../core/themes/fonts.dart';
import '../../widgets/privacy/privacy_const.dart';

class PrivacyPolicyCheckPc extends StatelessWidget {
  const PrivacyPolicyCheckPc({
    super.key,
    required this.isChecked,
    required this.onCheckedChanged,
  });

  final bool isChecked;
  final ValueChanged<bool> onCheckedChanged;

  void _handlePrivacyPolicyTap() {
    PGLog.d('点击了隐私政策');

    if (AppConstants.isWindows) {
      UrlLauncherUtil.openInSystemBrowser(PrivacyConst.privacyPolicyUrl);
    } else {
      // 如果对话框已经存在，则不显示(防止快速多次点击)
      if (PGDialog.isDialogVisible(DialogTags.privacyPolicy)) {
        PGLog.d(
            'PrivacyPolicyCheckPc showPrivacyPolicy, but dialog already exist, return');
        return;
      }
      PGDialog.showCustomDialog(
          width: PrivacyConst.privacyPolicyWidth,
          height: PrivacyConst.privacyPolicyHeight,
          tag: DialogTags.privacyPolicy,
          needBlur: true,
          child: H5WebView(
            url: PrivacyConst.privacyPolicyUrl,
            title: '隐私政策',
            onClose: () => PGDialog.dismiss(tag: DialogTags.privacyPolicy),
            forDialog: true,
          ));
    }
  }

  void _handleUserAgreementTap() {
    PGLog.d('点击了用户协议');
    if (AppConstants.isWindows) {
      UrlLauncherUtil.openInSystemBrowser(PrivacyConst.userAgreementUrl);
    } else {
      // 如果对话框已经存在，则不显示(防止快速多次点击)
      if (PGDialog.isDialogVisible(DialogTags.userAgreement)) {
        PGLog.d(
            'PrivacyPolicyCheckPc showUserAgreement, but dialog already exist, return');
        return;
      }
      PGDialog.showCustomDialog(
          width: PrivacyConst.privacyPolicyWidth,
          height: PrivacyConst.privacyPolicyHeight,
          tag: DialogTags.userAgreement,
          needBlur: true,
          child: H5WebView(
            url: PrivacyConst.userAgreementUrl,
            title: '用户协议',
            onClose: () => PGDialog.dismiss(tag: DialogTags.userAgreement),
            forDialog: true,
          ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () => onCheckedChanged(!isChecked),
          child: Container(
            width: 15,
            height: 15,
            decoration: BoxDecoration(
              color: isChecked ? const Color(0xFFF72561) : Colors.transparent,
              borderRadius: BorderRadius.circular(3),
              border: isChecked
                  ? null
                  : Border.all(
                      color: const Color.fromARGB(156, 235, 242, 245),
                      width: 1.5,
                    ),
            ),
            child: isChecked
                ? Image.asset(
                    Assets.loginCheckIcon,
                    width: 18,
                    height: 18,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 4),
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: '已仔细阅读并同意',
                style: TextStyle(
                  color: const Color(0xFFEBF2F5).withOpacity(0.6),
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                  fontSize: 10,
                ),
              ),
              TextSpan(
                text: '《用户使用协议》',
                style: TextStyle(
                  color: const Color(0xFFE1E2E5),
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 10,
                  fontWeight: Fonts.semiBold,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = _handleUserAgreementTap,
              ),
              TextSpan(
                text: '与',
                style: TextStyle(
                  color: const Color(0xFFEBF2F5).withOpacity(0.6),
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                  fontSize: 10,
                ),
              ),
              TextSpan(
                text: '《隐私政策》',
                style: TextStyle(
                  color: const Color(0xFFE1E2E5),
                  fontFamily: Fonts.defaultFontFamily,
                  fontSize: 10,
                  fontWeight: Fonts.semiBold,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = _handlePrivacyPolicyTap,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
