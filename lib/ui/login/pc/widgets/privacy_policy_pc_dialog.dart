import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/h5_webview.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/login/widgets/privacy/privacy_const.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/url_launcher_util.dart';

class PrivacyPolicyPcDialog extends StatefulWidget {
  const PrivacyPolicyPcDialog({
    super.key,
    required this.onAgree,
    required this.onDisagree,
  });

  final VoidCallback onAgree;
  final VoidCallback onDisagree;

  @override
  State<PrivacyPolicyPcDialog> createState() => _PrivacyPolicyPcDialogState();
}

class _PrivacyPolicyPcDialogState extends State<PrivacyPolicyPcDialog> {
  bool _hoverdAgree = false;
  bool _hoverdDisagree = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const SizedBox(height: 30),
          // 标题
          Text(
            '用户协议及隐私政策',
            style: TextStyle(
              color: const Color(0xFFE1E2E5),
              fontSize: 16,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.semiBold,
            ),
          ),
          const SizedBox(height: 8),
          // 副标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text.rich(
              TextSpan(
                text: '已阅读并同意',
                style: TextStyle(
                  color: const Color(0xFFEBEDF5).withOpacity(0.65),
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                ),
                children: [
                  TextSpan(
                    text: '《隐私政策》',
                    style: TextStyle(
                      color: const Color(0xFFF72561),
                      fontSize: 14,
                      fontWeight: Fonts.regular,
                      fontFamily: Fonts.defaultFontFamily,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (AppConstants.isWindows) {
                          UrlLauncherUtil.openInSystemBrowser(
                              PrivacyConst.privacyPolicyUrl);
                        } else {
                          // 如果对话框已经存在，则不显示(防止快速多次点击)
                          if (PGDialog.isDialogVisible(
                              DialogTags.privacyPolicy)) {
                            PGLog.d(
                                'PrivacyPolicyPcDialog showPrivacyPolicy, but dialog already exist, return');
                            return;
                          }
                          PGDialog.showCustomDialog(
                              width: PrivacyConst.privacyPolicyWidth,
                              height: PrivacyConst.privacyPolicyHeight,
                              tag: DialogTags.privacyPolicy,
                              needBlur: true,
                              child: H5WebView(
                                url: PrivacyConst.privacyPolicyUrl,
                                title: '隐私政策',
                                onClose: () => PGDialog.dismiss(
                                    tag: DialogTags.privacyPolicy),
                                forDialog: true,
                              ));
                        }
                      },
                  ),
                  const TextSpan(text: '与'),
                  TextSpan(
                    text: '《用户协议》',
                    style: TextStyle(
                      color: const Color(0xFFF72561),
                      fontSize: 14,
                      fontWeight: Fonts.regular,
                      fontFamily: Fonts.defaultFontFamily,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (AppConstants.isWindows) {
                          UrlLauncherUtil.openInSystemBrowser(
                              PrivacyConst.userAgreementUrl);
                        } else {
                          // 如果对话框已经存在，则不显示(防止快速多次点击)
                          if (PGDialog.isDialogVisible(
                              DialogTags.userAgreement)) {
                            PGLog.d(
                                'PrivacyPolicyPcDialog showUserAgreement, but dialog already exist, return');
                            return;
                          }
                          PGDialog.showCustomDialog(
                              width: PrivacyConst.privacyPolicyWidth,
                              height: PrivacyConst.privacyPolicyHeight,
                              tag: DialogTags.userAgreement,
                              needBlur: true,
                              child: H5WebView(
                                url: PrivacyConst.userAgreementUrl,
                                title: '用户协议',
                                onClose: () => PGDialog.dismiss(
                                    tag: DialogTags.userAgreement),
                                forDialog: true,
                              ));
                        }
                      },
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 30),
          // 按钮区域
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 同意按钮
                GestureDetector(
                  onTap: () {
                    PGDialog.dismiss(tag: DialogTags.privacyCheck);
                    widget.onAgree();
                  },
                  child: PlatformMouseRegion(
                    cursor: SystemMouseCursors.click,
                    onEnter: (_) => setState(() {
                      _hoverdAgree = true;
                    }),
                    onExit: (_) => setState(() {
                      _hoverdAgree = false;
                    }),
                    child: Container(
                      width: double.infinity,
                      height: 42,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: _hoverdAgree
                              ? const Color(0xFFF73069)
                              : const Color(0xFFF72561)),
                      alignment: Alignment.center,
                      child: Text(
                        '同意并登录',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.medium,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                // 不同意按钮
                GestureDetector(
                  onTap: () {
                    PGDialog.dismiss(tag: DialogTags.privacyCheck);
                    widget.onDisagree();
                  },
                  child: PlatformMouseRegion(
                    cursor: SystemMouseCursors.click,
                    onEnter: (_) => setState(() {
                      _hoverdDisagree = true;
                    }),
                    onExit: (_) => setState(() {
                      _hoverdDisagree = false;
                    }),
                    child: Container(
                      width: double.infinity,
                      height: 42,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: _hoverdDisagree
                            ? const Color(0xFF26272A)
                            : const Color(0xFF1B1C1F),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        '不同意',
                        style: TextStyle(
                          color: const Color.fromRGBO(251, 242, 245, 0.6),
                          fontSize: 14,
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.regular,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
