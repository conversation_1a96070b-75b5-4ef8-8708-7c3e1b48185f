import 'package:flutter/material.dart';

class LoginBackgroundColumnPc extends StatefulWidget {
  const LoginBackgroundColumnPc({
    super.key,
    required this.images,
    required this.scrollDown,
    this.duration = const Duration(seconds: 400), // 滚动动画时长，增加一倍使滚动速度减慢
    required this.opacity, // 不透明度动画参数
  });

  final List<String> images;
  final bool scrollDown;
  final Duration duration;
  final Animation<double> opacity; // 使用外部传入的不透明度动画

  @override
  State<LoginBackgroundColumnPc> createState() =>
      _LoginBackgroundColumnPcState();
}

class _LoginBackgroundColumnPcState extends State<LoginBackgroundColumnPc>
    with SingleTickerProviderStateMixin {
  late final AnimationController _scrollController; // 只控制滚动动画

  @override
  void initState() {
    super.initState();
    // 创建滚动动画控制器
    _scrollController = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    // 滚动动画一直循环
    _scrollController.repeat();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final totalHeight = constraints.maxHeight;
        final columnWidth = constraints.maxWidth;

        // 计算每个图片的高度，考虑到上下间距为24
        // 确保图片比例为3:4（宽:高=3:4）
        // 如果宽度是3，那么高度应该是4
        final itemWidth = columnWidth;
        final itemHeight = (itemWidth * 4) / 3; // 保持3:4的比例

        // 计算需要多少张图片才能填满整个高度（考虑间距）
        final itemCountNeeded = (totalHeight / (itemHeight + 24)).ceil() + 1;

        // 创建完整的图片列表，确保有足够的图片填满整个高度
        final List<String> allImages = [];
        for (int i = 0; i < itemCountNeeded; i++) {
          allImages.addAll(widget.images);
        }

        return Container(
          width: constraints.maxWidth,
          height: totalHeight,
          color: Colors.black, // 添加黑色背景色
          child: ClipRect(
            child: AnimatedBuilder(
              animation: _scrollController, // 仅监听滚动动画
              builder: (context, child) {
                // 反转动画方向
                final value = !widget.scrollDown
                    ? _scrollController.value
                    : (1 - _scrollController.value);

                // 计算总内容高度（所有图片加上间距）
                final totalContentHeight =
                    allImages.length * itemHeight + (allImages.length - 1) * 24;
                // 计算动画偏移量
                final offset = value * (totalContentHeight - totalHeight);

                return Opacity(
                  opacity: widget.opacity.value, // 使用外部传入的不透明度动画
                  child: Stack(
                    children: List.generate(
                      allImages.length,
                      (index) => Positioned(
                        top: (index * itemHeight) +
                            (index * 24) -
                            offset, // 每个图片之间的间距为24像素
                        left: 0,
                        right: 0,
                        height: itemHeight,
                        child: Image.asset(
                          allImages[index],
                          fit: BoxFit.cover,
                          gaplessPlayback: true, // 防止图片加载时闪烁
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
