import 'package:flutter/material.dart';

import 'login_background_column_pc.dart';

class LoginBackPc extends StatefulWidget {
  const LoginBackPc({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  State<LoginBackPc> createState() => _LoginBackPcState();
}

class _LoginBackPcState extends State<LoginBackPc>
    with TickerProviderStateMixin {
  late final AnimationController _animationController;
  final List<Animation<double>> _opacityAnimations = [];

  // 总循环时长为16秒
  static const totalDuration = Duration(seconds: 16);
  // 每列渐变时间为2.5秒
  static const fadeDuration = Duration(milliseconds: 2500);
  // 每列间隔0.15秒
  static const columnDelay = Duration(milliseconds: 150);

  List<String> _getImagesForColumn(int column) {
    return [
      'assets/images/login_back_pc/login_back_Image_pc_$column-1.png',
      'assets/images/login_back_pc/login_back_Image_pc_$column-2.png',
      'assets/images/login_back_pc/login_back_Image_pc_$column-3.png',
    ];
  }

  @override
  void initState() {
    super.initState();

    // 创建主动画控制器，总时长为16秒
    _animationController = AnimationController(
      vsync: this,
      duration: totalDuration,
    );

    // 为6列创建不同的渐变动画
    for (int i = 0; i < 6; i++) {
      // 计算每列的渐现开始时间（秒）
      final fadeInStartTime = i * columnDelay.inMilliseconds / 1000.0;
      // 计算每列的渐隐开始时间（秒）
      // 调整渐隐开始时间，使最后一列的渐隐结束时间接近总循环时间
      // 最后一列(i=5)的渐隐结束时间应为: (13.35 + 5*0.15) + 2.5 = 16秒
      final fadeOutStartTime =
          13.35 + (i * columnDelay.inMilliseconds / 1000.0);

      // 转换为动画时间线上的比例，并确保权重为正值
      final fadeInStart = fadeInStartTime / totalDuration.inSeconds;
      final fadeInEnd =
          (fadeInStartTime + fadeDuration.inSeconds) / totalDuration.inSeconds;
      final fadeOutStart = fadeOutStartTime / totalDuration.inSeconds;
      final fadeOutEnd =
          (fadeOutStartTime + fadeDuration.inSeconds) / totalDuration.inSeconds;

      // 创建区间动画，确保每个区间的权重都大于0
      final opacityTween = TweenSequence<double>([
        // 初始透明度为0
        if (fadeInStart > 0)
          TweenSequenceItem(
            tween: Tween<double>(begin: 0.0, end: 0.0),
            weight: fadeInStart,
          ),
        // 淡入阶段
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          weight: fadeInEnd - fadeInStart,
        ),
        // 保持可见
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.0, end: 1.0),
          weight: fadeOutStart - fadeInEnd,
        ),
        // 淡出阶段
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.0, end: 0.0),
          weight: fadeOutEnd - fadeOutStart,
        ),
        // 保持透明直到循环结束
        if (fadeOutEnd < 1.0)
          TweenSequenceItem(
            tween: Tween<double>(begin: 0.0, end: 0.0),
            weight: 1.0 - fadeOutEnd,
          ),
      ]);

      _opacityAnimations.add(
        opacityTween.animate(_animationController),
      );
    }

    // 启动循环动画
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          color: Colors.black,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Row(
                children: [
                  for (int i = 0; i < 6; i++) ...[
                    Expanded(
                      child: LoginBackgroundColumnPc(
                        images: _getImagesForColumn(i + 1),
                        scrollDown: i % 2 == 0, // 奇数列向下滚动，偶数列向上滚动
                        opacity: _opacityAnimations[i], // 直接传递不透明度动画
                      ),
                    ),
                    if (i < 5) const SizedBox(width: 24), // 列间距，最后一列后不需要
                  ],
                ],
              );
            },
          ),
        ),
        // 渐变层
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withOpacity(0.7), // 中间增加一个半透明黑色
                Colors.black,
              ],
              stops: const [0.0, 1.0], // 控制渐变位置
            ),
          ),
        ),
        // 内容层
        widget.child,
      ],
    );
  }
}
