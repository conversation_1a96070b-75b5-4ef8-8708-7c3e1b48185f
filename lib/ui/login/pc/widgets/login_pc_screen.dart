import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:pg_turing_collect_event/collect/customaction/login_action.dart'
    as login_action;
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/service/api/request_header.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/routing/routes.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/blur_container.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/core/ui/gradient_button.dart';
import 'package:turing_art/ui/core/ui/scale_button.dart';
import 'package:turing_art/ui/login/formatters/phone_number_input_formatter.dart';
import 'package:turing_art/ui/login/login_view_model/login_view_model.dart';
import 'package:turing_art/ui/login/pc/widgets/login_back/login_back_pc.dart';
import 'package:turing_art/ui/login/pc/widgets/login_hedaer_pc.dart';
import 'package:turing_art/ui/login/pc/widgets/privacy_policy_check_pc.dart';
import 'package:turing_art/ui/login/pc/widgets/privacy_policy_pc_dialog.dart';
import 'package:turing_art/ui/login/select_account/login_select_account_dialog.dart';
import 'package:turing_art/ui/login/select_account/model/login_account_model.dart';
import 'package:turing_art/ui/login/view_models/button_state_provider.dart';
import 'package:turing_art/ui/login/view_models/countdown_state_provider.dart';
import 'package:turing_art/ui/login/widgets/countdown_button.dart';
import 'package:turing_art/ui/login/widgets/privacy/privacy_const.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/version_update/widgets/version_update_dialog.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/mixins/page_duration_tracking_mixin.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_action.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_intent.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_state_mixin.dart';
import 'package:turing_art/utils/shortcut/shortcut_key_wrapper.dart';
import 'package:turing_art/utils/update_manager.dart';

class LoginPcScreen extends StatelessWidget {
  const LoginPcScreen({super.key, this.title = 'LoginPcScreen'});
  final String title;

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
            create: (_) => LoginViewModel(context.read<AuthRepository>(),
                context.read<AuthUseCaseProvider>().loginUseCase)),
        ChangeNotifierProvider(create: (_) => ButtonStateProvider()),
        ChangeNotifierProvider(create: (_) => CountdownStateProvider()),
        ChangeNotifierProvider(create: (_) => NetworkProvider()),
      ],
      child: _LoginPcScreenContent(title: title),
    );
  }
}

class _LoginPcScreenContent extends StatefulWidget {
  const _LoginPcScreenContent({required this.title});

  final String title;

  @override
  State<_LoginPcScreenContent> createState() => _LoginPcScreenContentState();
}

class _LoginPcScreenContentState extends State<_LoginPcScreenContent>
    with ShortcutKeyStateMixin, SimplePageDurationTrackingMixin {
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _codeFocusNode = FocusNode();
  bool _isPrivacyChecked = false;
  bool _isPhoneFocused = false;
  bool _isCodeFocused = false;
  bool _isPhoneNumberValid = true; // 添加手机号码验证状态
  bool _isCodeValid = true; // 添加验证码验证状态

  @override
  String get pageName => AnalyticPageNames.login;

  @override
  void initState() {
    super.initState();
    // 添加保护逻辑，进入登录页面时先隐藏loading对话框
    // 解决退出登录时可能存在的loading导致页面无法响应点击的问题
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PGDialog.dismiss();
    });
    _phoneNumberController.addListener(_updateButtonState);
    _codeController.addListener(_updateButtonState);

    // 添加焦点监听器
    _phoneFocusNode.addListener(() {
      setState(() {
        _isPhoneFocused = _phoneFocusNode.hasFocus;

        // 当手机号输入框失去焦点时，验证手机号
        if (!_phoneFocusNode.hasFocus) {
          _validatePhoneNumber();
        }
      });
    });

    _codeFocusNode.addListener(() {
      setState(() {
        _isCodeFocused = _codeFocusNode.hasFocus;

        // 当验证码输入框失去焦点时，验证验证码
        if (!_codeFocusNode.hasFocus) {
          _validateCode();
        }
      });
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateButtonState();
    });

    // 使用Future.microtask来避免在initState中直接调用异步方法
    Future.microtask(() async {
      await UpdateManager().checkUpdateVersion(isLaunch: true).then((result) {
        if (result.isUpdateVersion != null &&
            result.isUpdateVersion == true &&
            mounted) {
          VersionUpdateDialog.show(context);
        }
      });
    });

    login_action.recordLoginAction(
        userId: '', action: login_action.Action.login_show);
  }

  // 验证手机号格式
  void _validatePhoneNumber() {
    final phoneNumber = _phoneNumberController.text;
    setState(() {
      if (phoneNumber.isEmpty) {
        _isPhoneNumberValid = true;
        return;
      }

      // 如果包含"+"，则为海外格式
      if (phoneNumber.contains('+')) {
        final parts = phoneNumber.split('+');
        _isPhoneNumberValid =
            parts.length == 2 && parts[0].isNotEmpty && parts[1].isNotEmpty;
      } else {
        // 大陆地区手机号，11位数字
        _isPhoneNumberValid = phoneNumber.length == 11 &&
            RegExp(r'^1\d{10}$').hasMatch(phoneNumber);
      }
    });
  }

  // 验证验证码是否为有效的4位数字
  void _validateCode() {
    final code = _codeController.text;
    setState(() {
      _isCodeValid = code.isEmpty ||
          (code.length == 4 && RegExp(r'^\d{4}$').hasMatch(code));
    });
  }

  void _updateButtonState() {
    if (!mounted) {
      return; // 防止组件已销毁时调用
    }
    final buttonState = context.read<ButtonStateProvider>();

    // 检查验证码是否已输入4位，如果是则启用登录按钮
    final code = _codeController.text;
    final phoneNumber = _phoneNumberController.text;

    // 如果验证码已输入4位数字且手机号不为空，则启用登录按钮
    // 不需要勾选隐私政策
    if (code.length == 4 &&
        RegExp(r'^\d{4}$').hasMatch(code) &&
        phoneNumber.isNotEmpty) {
      buttonState.setDisabled(false);
      return;
    }

    // 否则使用原有的验证逻辑
    final validationResult = context.read<LoginViewModel>().validateInputs(
          phoneNumber: phoneNumber,
          code: code,
          isPrivacyChecked: _isPrivacyChecked,
        );

    buttonState.setDisabled(!validationResult.isValid);
  }

  @override
  void dispose() {
    _phoneNumberController.removeListener(_updateButtonState);
    _codeController.removeListener(_updateButtonState);
    _phoneNumberController.dispose();
    _codeController.dispose();
    _phoneFocusNode.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  @override
  Map<ShortcutActivator, Intent> defineShortcuts() {
    return {
      LogicalKeySet(LogicalKeyboardKey.enter): const EnterIntent(),
    };
  }

  @override
  Map<Type, Action<Intent>> defineActions() {
    return {
      EnterIntent: ShortcutKeyAction(
        intent: const EnterIntent(),
        callback: _handleLogin,
      ),
    };
  }

  // 获取账户列表
  Future<(String, List<LoginAccountModel>?)> _getAccountList(
      String mobile, String code) async {
    final loginViewModel = context.read<LoginViewModel>();
    final tuple = await loginViewModel.checkAccountList(
      mobile,
      code,
    );
    return (
      tuple.$1,
      tuple.$2
          ?.map((e) => LoginAccountModel(
                nickname: e.storeName,
                type: e.role == 'creator'
                    ? AccountType.primary
                    : AccountType.restricted,
                remainingQuota: e.available,
                totalQuota: e.total,
                storeId: e.storeId,
              ))
          .toList()
    );
  }

  void _handleLogin() async {
    final loginViewModel = context.read<LoginViewModel>();
    final validationResult = loginViewModel.validateInputs(
      phoneNumber: _phoneNumberController.text,
      code: _codeController.text,
      isPrivacyChecked: _isPrivacyChecked,
    );

    if (!validationResult.isValid) {
      switch (validationResult.error!) {
        case ValidationError.emptyPhoneNumber:
          PGDialog.showToast('请输入手机号');
          return;
        case ValidationError.invalidPhoneNumber:
          PGDialog.showToast('请输入正确的手机号');
          return;
        case ValidationError.emptyCode:
          PGDialog.showToast('请输入验证码');
          return;
        case ValidationError.invalidCode:
          PGDialog.showToast('请输入正确的验证码');
          return;
        case ValidationError.privacyNotAgreed:
          // 如果对话框已经存在，则不显示(防止快速多次点击)
          if (PGDialog.isDialogVisible(DialogTags.privacyCheck)) {
            PGLog.d(
                'LoginPcScreen showPrivacyPolicyDialog, but dialog already exist, return');
            return;
          }
          PGDialog.showCustomDialog(
            width: PrivacyConst.checkDialogWidthPc,
            height: PrivacyConst.checkDialogHeightPc,
            tag: DialogTags.privacyCheck,
            child: PrivacyPolicyPcDialog(
              onAgree: () {
                setState(() {
                  _isPrivacyChecked = true;
                });
                _updateButtonState();
                _handleLogin();
              },
              onDisagree: () {
                // 用户不同意隐私政策的处理
              },
            ),
          );
          return;
      }
    }

    if (!validationResult.isValid) {
      return;
    }
    PGDialog.showLoading();
    // 获取账户列表
    final accountResult = await _getAccountList(
      _phoneNumberController.text,
      _codeController.text,
    );
    PGDialog.dismiss(tag: DialogTags.loading);
    if (accountResult.$1.isEmpty) {
      final accountList = accountResult.$2;
      if (accountList != null) {
        final lastLoginStoreId = await loginViewModel
            .getLastLoginStoreIdByPhoneNumber(_phoneNumberController.text);
        // 如果新注册账户，则直接登录,不需要storeId
        if (accountList.isEmpty) {
          final success = await loginViewModel.login(
            _phoneNumberController.text,
            _codeController.text,
            lastLoginStoreId,
          );
          if (success && mounted) {
            _handleLoginSuccess();
          }
          return;
        }
        LoginSelectAccountDialog.show(
          lastLoginStoreId: lastLoginStoreId,
          accounts: accountList,
          onSelectAccount: (account) async {
            if (mounted) {
              RequestHeader.updateStoreId(account.storeId);
              final success = await loginViewModel.login(
                _phoneNumberController.text,
                _codeController.text,
                account.storeId,
              );

              if (success && mounted) {
                _handleLoginSuccess();
              }
            }
          },
        );
      } else {
        PGDialog.showToast('登录失败，请稍后再试');
      }
    } else {
      RequestHeader.updateStoreId(accountResult.$1);
      final success = await loginViewModel.login(
        _phoneNumberController.text,
        _codeController.text,
        accountResult.$1,
      );

      if (success && mounted) {
        _handleLoginSuccess();
      }
    }
  }

  /// 登录成功后跳转
  void _handleLoginSuccess() {
    if (mounted) {
      _recordLoginSuccess();
      context.go(Routes.projectHome);
    }
  }

  void _recordLoginSuccess() {
    // 获取当前登录用户的ID进行事件埋点
    final currentUserRepository = context.read<CurrentUserRepository>();
    currentUserRepository.getUserId().then((userId) {
      if (userId != null && userId.isNotEmpty) {
        login_action.recordLoginAction(
            userId: userId, action: login_action.Action.login_suc);
      }
    }).catchError((e) {
      PGLog.e('记录登录成功事件失败: $e');
    });
  }

  @override
  Widget build(BuildContext context) {
    return ShortcutKeyWrapper(
      child: Actions(
        actions: defineActions(),
        child: Consumer<LoginViewModel>(
          builder: (context, viewModel, child) {
            final messageState = viewModel.messageState;
            if (messageState != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  PGDialog.showToast(messageState.message ?? '');
                  viewModel.clearMessage();
                }
              });
            }
            final isLoading = viewModel.isLoading;
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                if (isLoading) {
                  PGDialog.showLoading();
                } else {
                  PGDialog.dismiss(tag: DialogTags.loading);
                }
              }
            });

            return Scaffold(
              body: TitleBarWidget(
                child: LoginBackPc(
                  child: SafeArea(
                    child: Stack(
                      children: [
                        // 主要内容区域
                        Center(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.only(bottom: 80),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const LoginHeaderPc(),
                                const SizedBox(height: 32),
                                _buildPhoneInput(),
                                const SizedBox(height: 4),
                                _buildNumberTips(!_isPhoneNumberValid),
                                const SizedBox(height: 9),
                                _buildCodeInput(),
                                const SizedBox(height: 4),
                                _buildCodeTips(!_isCodeValid),
                                const SizedBox(height: 25),
                                _buildLoginButton(),
                                const SizedBox(height: 60), // 增加底部空间，避免与版权信息重叠
                              ],
                            ),
                          ),
                        ),
                        // 版权信息固定在底部
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 30,
                          child: Text(
                            AppInfoExtension.getCopyrightString(),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: const Color(0xFFEBEDF5).withOpacity(0.4),
                              fontFamily: Fonts.fontFamilySF,
                              fontSize: 12,
                              fontWeight: Fonts.regular,
                              height: 1,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPhoneInput() {
    return BlurContainer(
      width: 285,
      height: 42,
      backgroundColor:
          _isPhoneFocused ? const Color(0xFF333333) : const Color(0xFF2E2F33),
      backgroundOpacity: 0.6,
      borderRadius: 6,
      borderWidth: 1,
      borderColor: _isPhoneFocused
          ? Colors.white.withOpacity(0.8)
          : const Color(0xFFFFFFFF).withOpacity(0.06),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: TextField(
              controller: _phoneNumberController,
              focusNode: _phoneFocusNode,
              keyboardType: TextInputType.text, // 改为text类型以支持"+"字符
              textAlign: TextAlign.left,
              textAlignVertical: TextAlignVertical.center,
              style: TextStyle(
                color: const Color(0xFFE1E2E5),
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
              ),
              decoration: InputDecoration(
                hintText: '输入手机号',
                hintTextDirection: TextDirection.ltr,
                hintStyle: TextStyle(
                  color: const Color(0xFFEBEDF5).withOpacity(0.65),
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                ),
                border: InputBorder.none,
                isDense: true,
              ),
              cursorColor: Colors.white, // 设置光标颜色为白色
              inputFormatters: [
                PhoneNumberInputFormatter(),
              ],
              onEditingComplete: () {
                // 当编辑完成时验证手机号
                _validatePhoneNumber();
                // 移动焦点到验证码输入框
                FocusScope.of(context).requestFocus(_codeFocusNode);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCodeInput() {
    return GestureDetector(
      // 添加手势检测器，点击整个容器时聚焦到输入框
      onTap: () {
        FocusScope.of(context).requestFocus(_codeFocusNode);
      },
      child: BlurContainer(
        width: 285,
        height: 42,
        backgroundColor:
            _isCodeFocused ? const Color(0xFF333333) : const Color(0xFF2E2F33),
        backgroundOpacity: 0.6,
        borderRadius: 6,
        borderWidth: 1,
        borderColor: _isCodeFocused
            ? Colors.white.withOpacity(0.8)
            : const Color(0xFFFFFFFF).withOpacity(0.06),
        child: Row(
          children: [
            const SizedBox(width: 16),
            Expanded(
              child: GestureDetector(
                // 确保点击输入区域时聚焦到输入框
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  FocusScope.of(context).requestFocus(_codeFocusNode);
                },
                child: TextField(
                  controller: _codeController,
                  focusNode: _codeFocusNode,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.left,
                  textAlignVertical: TextAlignVertical.center,
                  maxLength: 4,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                  style: TextStyle(
                    color: const Color(0xFFE1E2E5),
                    fontSize: 14,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.semiBold,
                    height: 1.0,
                  ),
                  decoration: InputDecoration(
                    hintText: '输入验证码',
                    hintTextDirection: TextDirection.ltr,
                    hintStyle: TextStyle(
                      color: const Color(0xFFEBEDF5).withOpacity(0.65),
                      fontSize: 14,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                    ),
                    border: InputBorder.none,
                    counterText: '',
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                  cursorColor: Colors.white, // 设置光标颜色为白色
                  onEditingComplete: () {
                    // 当编辑完成时验证验证码
                    _validateCode();
                    // 移动焦点到登录按钮
                    FocusScope.of(context).unfocus();
                  },
                  onChanged: (value) {
                    // 当输入内容变化时，只更新按钮状态，不验证验证码
                    // 验证码验证只在输入完成或失去焦点时进行
                    _updateButtonState();
                  },
                ),
              ),
            ),
            CountdownButton(
              textColor: const Color(0xFFF72561),
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
              fontSize: 14,
              onPressed: () async {
                // 在异步操作前先获取所需的对象，避免在异步操作后直接使用context
                // final networkProvider = context.read<NetworkProvider>();
                final loginViewModel = context.read<LoginViewModel>();
                final phoneNumber = _phoneNumberController.text;

                // if (!await networkProvider.isConnected()) {
                //   PGDialog.showToast('网络错误，请检查网络连接');
                //   throw Exception('网络错误，请检查网络连接');
                // }

                // 直接调用LoginViewModel的getLoginCode方法
                // 该方法会处理所有验证逻辑并在验证失败时抛出异常
                return loginViewModel.getLoginCode(phoneNumber);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginButton() {
    return Column(
      children: [
        Consumer<ButtonStateProvider>(
          builder: (context, buttonState, _) {
            return ScaleButton(
              disabled: buttonState.isDisabled,
              radius: 12,
              onPressed: () => _handleLogin(),
              child: GradientButton(
                width: 285,
                height: 42,
                borderRadius: 6,
                text: '登录',
                fontSize: 14,
                disabled: buttonState.isDisabled,
                gradientColors: const [
                  Color(0xFFF72561),
                  Color(0xFFF72561),
                ],
              ),
            );
          },
        ),
        const SizedBox(height: 16),
        PrivacyPolicyCheckPc(
          isChecked: _isPrivacyChecked,
          onCheckedChanged: (value) {
            setState(() {
              _isPrivacyChecked = value;
            });
            _updateButtonState();
          },
        ),
      ],
    );
  }

  Widget _buildNumberTips(bool show) {
    return show
        ? SizedBox(
            width: 285,
            child: Row(children: [
              const SizedBox(width: 4),
              Text(
                '请输入正确的手机号',
                textAlign: TextAlign.left,
                style: TextStyle(
                  color: const Color(0xFFF55C44),
                  fontSize: 10,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                ),
              ),
            ]),
          )
        : const SizedBox(height: 14);
  }

  Widget _buildCodeTips(bool show) {
    return show
        ? SizedBox(
            width: 285,
            child: Row(children: [
              const SizedBox(width: 4),
              Text(
                '请输入正确的验证码',
                textAlign: TextAlign.left,
                style: TextStyle(
                  color: const Color(0xFFF55C44),
                  fontSize: 10,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                ),
              ),
            ]),
          )
        : const SizedBox(height: 14);
  }
}
