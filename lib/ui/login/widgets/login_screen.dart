import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/service/api/request_header.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/login/formatters/phone_number_input_formatter.dart';
import 'package:turing_art/ui/login/select_account/login_select_account_dialog.dart';
import 'package:turing_art/ui/login/select_account/model/login_account_model.dart';

import '../../../datalayer/repository/auth_repository.dart';
import '../../../providers/network_provider.dart';
import '../../../routing/routes.dart';
import '../../../ui/core/themes/fonts.dart';
import '../../../ui/profile/use_case/auth_usecase_provider.dart';
import '../../../utils/pg_dialog/dialog_tags.dart';
import '../../../utils/pg_dialog/pg_dialog.dart';
import '../../../utils/pg_log.dart';
import '../../core/ui/blur_container.dart';
import '../../core/ui/gradient_button.dart';
import '../../core/ui/scale_button.dart';
import '../login_view_model/login_view_model.dart';
import '../view_models/button_state_provider.dart';
import '../view_models/countdown_state_provider.dart';
import 'countdown_button.dart';
import 'login_background/login_background.dart';
import 'login_header.dart';
import 'privacy/privacy_const.dart';
import 'privacy/privacy_policy_check.dart';
import 'privacy/privacy_policy_dialog.dart';
import 'verification_code_input.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key, this.title = 'LoginScreen'});
  final String title;

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
            create: (_) => LoginViewModel(context.read<AuthRepository>(),
                context.read<AuthUseCaseProvider>().loginUseCase)),
        ChangeNotifierProvider(create: (_) => ButtonStateProvider()),
        ChangeNotifierProvider(create: (_) => CountdownStateProvider()),
        ChangeNotifierProvider(create: (_) => NetworkProvider()),
      ],
      child: _LoginScreenContent(title: title),
    );
  }
}

class _LoginScreenContent extends StatefulWidget {
  const _LoginScreenContent({required this.title});

  final String title;

  @override
  State<_LoginScreenContent> createState() => _LoginScreenContentState();
}

class _LoginScreenContentState extends State<_LoginScreenContent> {
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  bool _isPrivacyChecked = false;

  @override
  void initState() {
    super.initState();
    _phoneNumberController.addListener(_updateButtonState);
    _codeController.addListener(_updateButtonState);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateButtonState();
    });
  }

  void _updateButtonState() {
    if (!mounted) {
      return;
    } // 防止组件已销毁时调用

    final buttonState = context.read<ButtonStateProvider>();
    final validationResult = context.read<LoginViewModel>().validateInputs(
          phoneNumber: _phoneNumberController.text,
          code: _codeController.text,
          isPrivacyChecked: _isPrivacyChecked,
        );

    buttonState.setDisabled(!validationResult.isValid);
  }

  @override
  void dispose() {
    _phoneNumberController.removeListener(_updateButtonState);
    _codeController.removeListener(_updateButtonState);
    _phoneNumberController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  // 获取账户列表
  Future<(String, List<LoginAccountModel>?)> _getAccountList(
      String mobile, String code) async {
    final loginViewModel = context.read<LoginViewModel>();
    final tuple = await loginViewModel.checkAccountList(
      mobile,
      code,
    );
    return (
      tuple.$1,
      tuple.$2
          ?.map((e) => LoginAccountModel(
                nickname: e.storeName,
                type: e.role == 'creator'
                    ? AccountType.primary
                    : AccountType.restricted,
                remainingQuota: e.available,
                totalQuota: e.total,
                storeId: e.storeId,
              ))
          .toList()
    );
  }

  void _handleLogin(BuildContext context) async {
    // final networkProvider = context.read<NetworkProvider>();
    final loginViewModel = context.read<LoginViewModel>();
    // if (!await networkProvider.isConnected()) {
    //   PGDialog.showToast('网络错误，请检查网络连接');
    //   return;
    // }
    final validationResult = loginViewModel.validateInputs(
      phoneNumber: _phoneNumberController.text,
      code: _codeController.text,
      isPrivacyChecked: _isPrivacyChecked,
    );

    if (!validationResult.isValid) {
      switch (validationResult.error!) {
        case ValidationError.emptyPhoneNumber:
          PGDialog.showToast('请输入手机号');
          return;
        case ValidationError.invalidPhoneNumber:
          PGDialog.showToast('请输入正确的手机号');
          return;
        case ValidationError.emptyCode:
          PGDialog.showToast('请输入验证码');
          return;
        case ValidationError.invalidCode:
          PGDialog.showToast('请输入正确的验证码');
          return;
        case ValidationError.privacyNotAgreed:
          // 如果对话框已经存在，则不显示(防止快速多次点击)
          if (PGDialog.isDialogVisible(DialogTags.privacyCheck)) {
            PGLog.d(
                'LoginScreen showPrivacyPolicyDialog, but dialog already exist, return');
            return;
          }
          PGDialog.showCustomDialog(
            width: PrivacyConst.checkDialogWidth,
            height: PrivacyConst.checkDialogHeight,
            tag: DialogTags.privacyCheck,
            child: PrivacyPolicyDialog(
              onAgree: () {
                setState(() {
                  _isPrivacyChecked = true;
                });
                _updateButtonState();
                _handleLogin(context);
              },
              onDisagree: () {
                // 用户不同意隐私政策的处理
              },
            ),
          );
          return;
      }
    }

    if (!validationResult.isValid) {
      return;
    }
    PGDialog.showLoading();
    // 获取账户列表
    final accountResult = await _getAccountList(
      _phoneNumberController.text,
      _codeController.text,
    );
    PGDialog.dismiss(tag: DialogTags.loading);
    if (accountResult.$1.isEmpty) {
      final accountList = accountResult.$2;
      if (accountList != null) {
        final lastLoginStoreId = await loginViewModel
            .getLastLoginStoreIdByPhoneNumber(_phoneNumberController.text);
        // 如果新注册账户，则直接登录,不需要storeId
        if (accountList.isEmpty) {
          final success = await loginViewModel.login(
            _phoneNumberController.text,
            _codeController.text,
            lastLoginStoreId,
          );
          if (success) {
            _jumpToProjectHomePage();
          }
          return;
        }
        LoginSelectAccountDialog.show(
          lastLoginStoreId: lastLoginStoreId,
          accounts: accountList,
          onSelectAccount: (account) async {
            if (mounted) {
              RequestHeader.updateStoreId(account.storeId);
              final success = await loginViewModel.login(
                _phoneNumberController.text,
                _codeController.text,
                account.storeId,
              );

              if (success) {
                _jumpToProjectHomePage();
              }
            }
          },
        );
      } else {
        PGDialog.showToast('登录失败，请稍后再试');
      }
    } else {
      RequestHeader.updateStoreId(accountResult.$1);
      final success = await loginViewModel.login(
        _phoneNumberController.text,
        _codeController.text,
        accountResult.$1,
      );

      if (success) {
        _jumpToProjectHomePage();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginViewModel>(
      builder: (context, viewModel, child) {
        final messageState = viewModel.messageState;
        if (messageState != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            PGDialog.showToast(messageState.message ?? '');
            viewModel.clearMessage();
          });
        }
        final isLoading = viewModel.isLoading;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            if (isLoading) {
              PGDialog.showLoading();
            } else {
              PGDialog.dismiss(tag: DialogTags.loading);
            }
          }
        });

        return Scaffold(
          body: TitleBarWidget(
            child: LoginBackground(
              child: SafeArea(
                child: Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.only(bottom: 80),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const LoginHeader(),
                        const SizedBox(height: 32),
                        _buildPhoneInput(),
                        const SizedBox(height: 16),
                        _buildCodeInput(),
                        const SizedBox(height: 32),
                        _buildLoginButton(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _jumpToProjectHomePage() {
    if (mounted) {
      context.go(Routes.projectHome);
    }
  }

  Widget _buildPhoneInput() {
    return BlurContainer(
      width: 400,
      height: 56,
      borderRadius: 12,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.only(left: 24),
            alignment: Alignment.center,
            child: Text(
              '+86',
              style: TextStyle(
                color: const Color(0x8fEBF2F5),
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _phoneNumberController,
              keyboardType: TextInputType.text, // 改为text类型以支持"+"字符
              textAlign: TextAlign.left,
              textAlignVertical: TextAlignVertical.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
              ),
              decoration: InputDecoration(
                hintText: '请输入手机号',
                hintTextDirection: TextDirection.ltr,
                hintStyle: TextStyle(
                  color: const Color(0x8fEBF2F5),
                  fontSize: 16,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 16.5),
                isDense: true,
              ),
              inputFormatters: [
                PhoneNumberInputFormatter(),
              ],
            ),
          ),
          CountdownButton(
            onPressed: () async {
              // 在异步操作前先获取所需的对象，避免在异步操作后直接使用context
              // final networkProvider = context.read<NetworkProvider>();
              final loginViewModel = context.read<LoginViewModel>();
              final phoneNumber = _phoneNumberController.text;

              // if (!await networkProvider.isConnected()) {
              //   PGDialog.showToast('网络错误，请检查网络连接');
              //   throw Exception('网络错误，请检查网络连接');
              // }

              // 直接调用LoginViewModel的getLoginCode方法
              // 该方法会处理所有验证逻辑并在验证失败时抛出异常
              return loginViewModel.getLoginCode(phoneNumber);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCodeInput() {
    return VerificationCodeInput(
      onChanged: (code) {
        setState(() {
          _codeController.text = code;
        });
        _updateButtonState();
      },
      onCompleted: (code) {
        // 可以在这里处理验证码输入完成的逻辑
        PGLog.d('Verification code completed: $code');
      },
    );
  }

  Widget _buildLoginButton() {
    return Column(
      children: [
        Consumer<ButtonStateProvider>(
          builder: (context, buttonState, _) {
            return ScaleButton(
              disabled: buttonState.isDisabled,
              radius: 12,
              onPressed: () => _handleLogin(context),
              child: GradientButton(
                width: 400,
                height: 56,
                borderRadius: 12,
                text: '登录',
                disabled: buttonState.isDisabled,
                gradientColors: const [
                  Color(0xFF0095FF),
                  Color(0xFF00DCD4),
                ],
              ),
            );
          },
        ),
        const SizedBox(height: 16),
        PrivacyPolicyCheck(
          isChecked: _isPrivacyChecked,
          onCheckedChanged: (value) {
            setState(() {
              _isPrivacyChecked = value;
            });
            _updateButtonState();
          },
        ),
      ],
    );
  }
}
