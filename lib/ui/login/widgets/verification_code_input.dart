import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../ui/core/themes/fonts.dart';
import '../../core/ui/blur_container.dart';

class VerificationCodeInput extends StatefulWidget {
  const VerificationCodeInput({
    super.key,
    required this.onCompleted,
    this.length = 4,
    this.onChanged,
  });

  final Function(String) onCompleted;
  final Function(String)? onChanged;
  final int length;

  @override
  State<VerificationCodeInput> createState() => _VerificationCodeInputState();
}

class _VerificationCodeInputState extends State<VerificationCodeInput> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  late List<String> _codes;

  @override
  void initState() {
    super.initState();
    _controllers =
        List.generate(widget.length, (index) => TextEditingController());
    _focusNodes = List.generate(widget.length, (index) => FocusNode());
    _codes = List.filled(widget.length, '');
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  void _updateVerificationCode() {
    final code = _codes.join();
    widget.onChanged?.call(code);
    if (code.length == widget.length) {
      widget.onCompleted(code);
    }
  }

  void _handleBackspace(int index) {
    if (_controllers[index].text.isNotEmpty) {
      setState(() {
        _controllers[index].clear();
        _codes[index] = '';
      });
      _updateVerificationCode();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 400,
      height: 62,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(widget.length, (index) {
          return BlurContainer(
            width: 62,
            height: 62,
            borderRadius: 12,
            child: KeyboardListener(
              focusNode: FocusNode(),
              onKeyEvent: (event) {
                if (event is KeyDownEvent &&
                    event.logicalKey == LogicalKeyboardKey.backspace) {
                  _handleBackspace(index);
                }
              },
              child: TextField(
                controller: _controllers[index],
                focusNode: _focusNodes[index],
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                maxLength: 1,
                showCursor: true,
                cursorColor: Colors.white,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                ),
                decoration: const InputDecoration(
                  counterText: '',
                  border: InputBorder.none,
                  contentPadding:
                      EdgeInsets.only(left: 14, right: 10, top: 14, bottom: 14),
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    final lastChar = value.characters.last;
                    setState(() {
                      _codes[index] = lastChar;
                      _controllers[index].text = lastChar;
                      _controllers[index].selection =
                          const TextSelection.collapsed(offset: 1);
                    });

                    if (index < widget.length - 1) {
                      _focusNodes[index + 1].requestFocus();
                    }
                  } else {
                    setState(() {
                      _codes[index] = '';
                    });

                    if (index > 0) {
                      _focusNodes[index - 1].requestFocus();
                    }
                  }
                  _updateVerificationCode();
                },
                onTap: () {
                  if (_controllers[index].text.isNotEmpty) {
                    _controllers[index].selection = TextSelection.collapsed(
                      offset: _controllers[index].text.length,
                    );
                  }
                },
              ),
            ),
          );
        }),
      ),
    );
  }
}
