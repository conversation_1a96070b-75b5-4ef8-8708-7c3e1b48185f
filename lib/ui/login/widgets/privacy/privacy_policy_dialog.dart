import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/gradient_button.dart';
import 'package:turing_art/ui/core/ui/h5_webview.dart';
import 'package:turing_art/ui/core/ui/scale_button.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/url_launcher_util.dart';

import 'privacy_const.dart';

class PrivacyPolicyDialog extends StatelessWidget {
  const PrivacyPolicyDialog({
    super.key,
    required this.onAgree,
    required this.onDisagree,
  });

  final VoidCallback onAgree;
  final VoidCallback onDisagree;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const SizedBox(height: 24),
          // 标题
          Text(
            '用户协议及隐私政策',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.semiBold,
            ),
          ),
          const SizedBox(height: 8),
          // 副标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text.rich(
              TextSpan(
                text: '已阅读并同意',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.regular,
                ),
                children: [
                  TextSpan(
                    text: '《隐私政策》',
                    style: TextStyle(
                      color: const Color(0xFF00B6DE),
                      fontSize: 14,
                      fontWeight: Fonts.regular,
                      fontFamily: Fonts.defaultFontFamily,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (UrlLauncherUtil.shouldUseSystemBrowser()) {
                          UrlLauncherUtil.openInSystemBrowser(
                              'https://www.baidu.com');
                        } else {
                          // 如果对话框已经存在，则不显示(防止快速多次点击)
                          if (PGDialog.isDialogVisible(
                              DialogTags.privacyPolicy)) {
                            PGLog.d(
                                'PrivacyPolicyDialog showPrivacyPolicy, but dialog already exist, return');
                            return;
                          }
                          PGDialog.showCustomDialog(
                            width: PrivacyConst.privacyPolicyWidth,
                            height: PrivacyConst.privacyPolicyHeight,
                            tag: DialogTags.privacyPolicy,
                            needBlur: true,
                            child: H5WebView(
                              url: 'https://www.baidu.com',
                              title: '隐私政策',
                              onClose: () => PGDialog.dismiss(
                                  tag: DialogTags.privacyPolicy),
                              forDialog: true,
                            ),
                          );
                        }
                      },
                  ),
                  const TextSpan(text: '与'),
                  TextSpan(
                    text: '《用户协议》',
                    style: TextStyle(
                      color: const Color(0xFF00B6DE),
                      fontSize: 14,
                      fontWeight: Fonts.regular,
                      fontFamily: Fonts.defaultFontFamily,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (UrlLauncherUtil.shouldUseSystemBrowser()) {
                          UrlLauncherUtil.openInSystemBrowser(
                              'https://www.baidu.com');
                        } else {
                          PGDialog.showCustomDialog(
                            width: PrivacyConst.privacyPolicyWidth,
                            height: PrivacyConst.privacyPolicyHeight,
                            tag: DialogTags.userAgreement,
                            needBlur: true,
                            child: H5WebView(
                              url: 'https://www.baidu.com',
                              title: '用户协议',
                              onClose: () => PGDialog.dismiss(
                                  tag: DialogTags.userAgreement),
                              forDialog: true,
                            ),
                          );
                        }
                      },
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          // 按钮区域
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 同意按钮
                ScaleButton(
                  radius: 12,
                  onPressed: () {
                    PGDialog.dismiss(tag: DialogTags.privacyCheck);
                    onAgree();
                  },
                  child: const GradientButton(
                    width: double.infinity,
                    height: 52,
                    text: '同意并登录',
                    borderRadius: 12,
                    fontSize: 14,
                    gradientColors: [
                      Color(0xFF0095FF),
                      Color(0xFF00DCD4),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                // 不同意按钮
                TextButton(
                  onPressed: () {
                    PGDialog.dismiss(tag: DialogTags.privacyCheck);
                    onDisagree();
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: const Color.fromRGBO(251, 242, 245, 0.6),
                    textStyle: TextStyle(
                      fontSize: 14,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.regular,
                    ),
                    minimumSize: const Size(0, 20),
                  ),
                  child: const Text('不同意'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
