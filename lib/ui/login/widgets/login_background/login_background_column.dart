import 'package:flutter/material.dart';

class LoginBackgroundColumn extends StatefulWidget {
  const LoginBackgroundColumn({
    super.key,
    required this.images,
    required this.scrollDown,
    this.duration = const Duration(seconds: 20),
  });

  final List<String> images;
  final bool scrollDown;
  final Duration duration;

  @override
  State<LoginBackgroundColumn> createState() => _LoginBackgroundColumnState();
}

class _LoginBackgroundColumnState extends State<LoginBackgroundColumn>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final totalHeight = constraints.maxHeight;
        final itemHeight = totalHeight / 2;
        
        // 创建完整的图片列表，实现完美循环
        final allImages = [
          ...widget.images,
          ...widget.images,
          widget.images.first,
        ];
        
        return SizedBox(
          width: constraints.maxWidth,
          height: totalHeight,
          child: ClipRect(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                // 反转一二四列的动画方向
                final value = !widget.scrollDown
                    ? _controller.value
                    : (1 - _controller.value);
                    
                return Stack(
                  children: List.generate(
                    allImages.length,
                    (index) => Positioned(
                      top: (index * itemHeight) - (value * itemHeight * widget.images.length),
                      left: 0,
                      right: 0,
                      height: itemHeight,
                      child: Image.asset(
                        allImages[index],
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
} 