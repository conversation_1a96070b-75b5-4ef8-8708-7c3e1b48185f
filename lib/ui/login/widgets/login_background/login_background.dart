import 'package:flutter/material.dart';
import 'login_background_column.dart';
import 'gradient_blur_container.dart';

class LoginBackground extends StatelessWidget {
  const LoginBackground({
    super.key,
    required this.child,
  });

  final Widget child;

  List<String> _getImagesForColumn(int column) {
    return [
      'assets/images/login_back/login_back_Image$column-1.jpg',
      'assets/images/login_back/login_back_Image$column-2.jpg',
      'assets/images/login_back/login_back_Image$column-3.jpg',
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 背景动画层
        Row(
          children: [
            Expanded(
              child: LoginBackgroundColumn(
                images: _getImagesForColumn(1),
                scrollDown: true,
              ),
            ),
            Expanded(
              child: LoginBackgroundColumn(
                images: _getImagesForColumn(2),
                scrollDown: false,
              ),
            ),
            Expanded(
              child: LoginBackgroundColumn(
                images: _getImagesForColumn(3),
                scrollDown: true,
              ),
            ),
            Expanded(
              child: LoginBackgroundColumn(
                images: _getImagesForColumn(4),
                scrollDown: false,
              ),
            ),
          ],
        ),
        // 顶部渐变模糊层
        const Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: GradientBlurContainer(
            height: 68,
            blurBegin: 40.0,
            blurEnd: 0.0,
            direction: BlurDirection.vertical,
            alignment: Alignment.topCenter,
          ),
        ),
        // 内容层
        child,
      ],
    );
  }
}
