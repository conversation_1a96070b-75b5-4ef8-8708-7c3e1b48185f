import 'package:flutter/material.dart';

class GradientBlurContainer extends StatelessWidget {
  const GradientBlurContainer({
    super.key,
    this.width,
    this.height,
    this.blurBegin = 1.0,
    this.blurEnd = 0.0,
    this.direction = BlurDirection.vertical,
    this.alignment = Alignment.topCenter,
  });

  final double? width;
  final double? height;
  final double blurBegin;
  final double blurEnd;
  final BlurDirection direction;
  final Alignment alignment;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: alignment,
          end: direction == BlurDirection.vertical
              ? Alignment(alignment.x, -alignment.y)
              : Alignment(-alignment.x, alignment.y),
          colors: [
            Colors.black.withAlpha(150),
            Colors.transparent,
          ],
        ),
      ),
    );
  }
}

enum BlurDirection {
  vertical,
  horizontal,
}
