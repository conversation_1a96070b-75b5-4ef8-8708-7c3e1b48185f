import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../ui/core/themes/fonts.dart';
import '../view_models/countdown_state_provider.dart';

class CountdownButton extends StatefulWidget {
  const CountdownButton({
    super.key,
    required this.onPressed,
    this.countdownDuration = 60,
    this.normalText = '获取验证码',
    this.countdownText = '重新发送',
    this.textColor = const Color(0xFF00B6DE),
    this.fontFamily = Fonts.fontFamilyPF,
    this.fontWeight = Fonts.semiBold,
    this.fontSize = 16,
  });

  final Future<void> Function() onPressed;
  final int countdownDuration;
  final String normalText;
  final String countdownText;
  final Color textColor;
  final String fontFamily;
  final FontWeight fontWeight;
  final double fontSize;

  @override
  State<CountdownButton> createState() => _CountdownButtonState();
}

class _CountdownButtonState extends State<CountdownButton> {
  late int _secondsRemaining;
  Timer? _timer;

  // 用于限制点击频率
  DateTime? _lastClickTime;
  bool _isClickable = true;

  @override
  void initState() {
    super.initState();
    _secondsRemaining = widget.countdownDuration;
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  // 检查是否可以点击
  bool _canClick() {
    if (!_isClickable) return false;

    final now = DateTime.now();
    if (_lastClickTime != null) {
      final difference = now.difference(_lastClickTime!);
      if (difference.inMilliseconds < 1000) {
        return false;
      }
    }

    _lastClickTime = now;
    return true;
  }

  void _startCountdown() {
    final provider = context.read<CountdownStateProvider>();
    provider.startCountdown();
    _secondsRemaining = widget.countdownDuration;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_secondsRemaining > 0) {
          _secondsRemaining--;
        } else {
          provider.stopCountdown();
          timer.cancel();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CountdownStateProvider>(
      builder: (context, provider, _) {
        final bool isEnabled = !provider.isCountingDown;

        return GestureDetector(
          onTap: isEnabled
              ? () async {
                  // 检查是否可以点击
                  if (!_canClick()) {
                    return;
                  }

                  // 点击时暂时禁用按钮，防止多次点击
                  setState(() {
                    _isClickable = false;
                  });

                  try {
                    await widget.onPressed();
                    _startCountdown();
                  } catch (e) {
                    // 如果获取验证码失败，不开始倒计时
                  } finally {
                    // 无论成功失败，1秒后恢复可点击状态
                    Future.delayed(const Duration(seconds: 1), () {
                      if (mounted) {
                        setState(() {
                          _isClickable = true;
                        });
                      }
                    });
                  }
                }
              : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              provider.isCountingDown
                  ? '${widget.countdownText}(${_secondsRemaining}s)'
                  : widget.normalText,
              style: TextStyle(
                color: provider.isCountingDown
                    ? const Color(0xFFEBEDF5).withOpacity(0.4)
                    : widget.textColor,
                fontSize: widget.fontSize,
                fontFamily: widget.fontFamily,
                fontWeight: widget.fontWeight,
              ),
            ),
          ),
        );
      },
    );
  }
}
