import 'package:flutter/material.dart';

import '../../../config/env_config.dart';
import '../../../ui/core/themes/fonts.dart';

class LoginHeader extends StatefulWidget {
  const LoginHeader({
    super.key,
  });

  @override
  State<LoginHeader> createState() => _LoginHeaderState();
}

class _LoginHeaderState extends State<LoginHeader> {
  String _version = 'Beta';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    // 使用 EnvConfig 获取版本号
    final version = await EnvConfig.getAppVersion();
    if (mounted) {
      setState(() {
        _version = 'Beta V$version';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'TulingArt',
              style: TextStyle(
                fontFamily: 'SFProText',
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              height: 20,
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
              decoration: BoxDecoration(
                color: const Color(0xFF9FEF00),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _version,
                style: const TextStyle(
                  color: Colors.black,
                  fontFamily: 'SFProText',
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          '手机验证码登录',
          style: TextStyle(
            color: Colors.white,
            fontFamily: Fonts.defaultFontFamily,
            fontSize: 32,
            fontWeight: Fonts.semiBold,
          ),
        ),
      ],
    );
  }
}
