import 'package:flutter/cupertino.dart';

import '../../../utils/app_info.dart';
import '../../core/themes/fonts.dart';

class ProfileTipsView extends StatelessWidget {
  const ProfileTipsView({super.key});

  @override
  Widget build(BuildContext context) => Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0x1405D4FF),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: const Color(0xFF05D4FF).withAlpha(25),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Image.asset(
                'assets/icons/home_mine_notice.png',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Pad端内测体验中',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF05D4FF),
                    fontFamily: Fonts.fontFamilySF,
                    fontWeight: Fonts.semiBold,
                  ),
                ),
                RichText(
                  maxLines: 2,
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: '该内测版本预计 ',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0x99EBF2F5),
                          fontFamily: Fonts.fontFamilySF,
                          fontWeight: Fonts.regular,
                        ),
                      ),
                      TextSpan(
                        text: AppInfoExtension.getExpiredTimeForTipBar(),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF05D4FF),
                          fontFamily: Fonts.fontFamilySF,
                          fontWeight: Fonts.regular,
                        ),
                      ),
                      const TextSpan(
                        text: ' 过期，届时请联系提供方或邮件咨询 <EMAIL> 以继续使用。',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0x99EBF2F5),
                          fontFamily: Fonts.fontFamilySF,
                          fontWeight: Fonts.regular,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )),
          ],
        ),
      );
}
