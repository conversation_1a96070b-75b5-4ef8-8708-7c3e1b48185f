import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/blur_container.dart';
import 'package:turing_art/ui/dialog/core/animated_dialog.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/ui/profile/widgets/profile_tips_view.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/screen_util.dart';

class ProfileDialog extends StatelessWidget {
  const ProfileDialog({super.key});

  static void show(BuildContext context) {
    SmartDialog.show(
      builder: (_) => ChangeNotifierProvider(
        create: (_) => ProfileDialogViewModel(
          context.read<CurrentUserRepository>(),
          context.read<AccountRepository>(),
          context.read<WechatGiftRepository>(),
          context.read<OpsCustomTableRepository>(),
          context.read<AccountRightsStateProvider>(),
          context.read<RewardRepository>(),
          context.read<NewUserRepository>(),
          context.read<AuthUseCaseProvider>(),
        ),
        child: const ProfileDialog(),
      ),
      animationBuilder: (controller, child, animationParam) =>
          AnimatedDialog(controller: controller, child: child),
      maskWidget: BlurContainer(
        blur: 24,
        backgroundColor: const Color(0x99000000),
        child: SizedBox(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var viewModel = context.read<ProfileDialogViewModel>();
    viewModel.tryLoadUserProfile();
    return Consumer<ProfileDialogViewModel>(
      builder: (context, viewModel, _) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
                width: 420,
                height: 400,
                decoration: BoxDecoration(
                  color: const Color(0xff121415),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x33000000),
                      offset: Offset(0, 4),
                      blurRadius: 40,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 24),
                    // 头像
                    Container(
                      width: 80,
                      height: 80,
                      clipBehavior: Clip.hardEdge,
                      decoration: const BoxDecoration(
                          shape: BoxShape.circle, color: Color(0xff222526)),
                      child: Image.asset(
                          "assets/icons/profile_default_avater.png"),
                    ),
                    const SizedBox(height: 8),
                    // 用户昵称
                    Text(
                      viewModel.nickname ?? "",
                      style: const TextStyle(
                        color: Color(0xFFFFFFFF),
                        fontSize: 16,
                        fontFamily: Fonts.fontFamilySF,
                        fontWeight: Fonts.semiBold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const ProfileTipsView(),
                    const SizedBox(height: 12),
                    Container(
                      height: 52,
                      padding: const EdgeInsets.all(16),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "导出剩余张数",
                            style: TextStyle(
                              color: Color(0x99EBF2F5),
                              fontSize: 14,
                              fontFamily: Fonts.fontFamilySF,
                              fontWeight: Fonts.regular,
                            ),
                          ),
                          //TODO 未实现导出剩余张数逻辑
                          Text(
                            "无限",
                            style: TextStyle(
                              color: Color(0xFF05D4FF),
                              fontSize: 14,
                              fontFamily: Fonts.fontFamilySF,
                              fontWeight: Fonts.medium,
                            ),
                          )
                        ],
                      ),
                    ),

                    // 关于我们按钮
                    GestureDetector(
                      onTap: () {
                        //TODO 未实现关于我们跳转逻辑
                      },
                      child: Container(
                        width: 420,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.all(16),
                        decoration: const BoxDecoration(
                          border: Border.symmetric(
                            horizontal: BorderSide(
                              color: Color(0x14FFFFFF),
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Text(
                          '关于我们',
                          style: TextStyle(
                              color: const Color(0xFFFFFFFF),
                              fontSize: 14,
                              fontFamily: Fonts.defaultFontFamily,
                              fontWeight: Fonts.medium),
                        ),
                      ),
                    ),
                    //退出登录按钮
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () async {
                        await viewModel.logout();
                        PGDialog.dismiss();
                      },
                      child: Container(
                        width: 420,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          '退出登录',
                          style: TextStyle(
                              color: const Color(0xFFE0694F),
                              fontSize: 14,
                              fontFamily: Fonts.defaultFontFamily,
                              fontWeight: Fonts.medium),
                        ),
                      ),
                    ),
                  ],
                )),
            Positioned(
              right: 16,
              top: 16,
              width: 30,
              height: 30,
              child: GestureDetector(
                onTap: () => PGDialog.dismiss(),
                child: Image.asset("assets/icons/profile_close_dialog.png"),
              ),
            ),
            //保持卡片居中，卡片下方吸附文字
            Positioned(
              bottom: -30,
              left: 0,
              right: 0,
              child: Text(
                  "${AppInfoExtension.getCopyrightString()} | ${AppInfoExtension.getVersionString()}",
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Color(0x99EBF2F5),
                    fontSize: 10,
                    fontFamily: Fonts.fontFamilySF,
                    fontWeight: Fonts.regular,
                  )),
            )
          ],
        );
      },
    );
  }
}
