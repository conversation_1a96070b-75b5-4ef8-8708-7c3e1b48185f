import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/providers/purchase_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 退出登录用例
class LogoutUseCase {
  final AuthRepository _authRepository;
  final CurrentUserRepository _currentUserRepository;
  final AccountRepository _accountRepository;
  final PurchaseStateProvider? _purchaseStateProvider;
  final AccountRightsStateProvider? _accountRightsStateProvider;
  final UnityController? _unityController;
  final UnityUseCaseProvider? _unityUseCaseProvider;

  LogoutUseCase(
    this._authRepository,
    this._currentUserRepository,
    this._accountRepository, {
    PurchaseStateProvider? purchaseStateProvider,
    AccountRightsStateProvider? accountRightsStateProvider,
    UnityController? unityController,
    UnityUseCaseProvider? unityUseCaseProvider,
    NavigatorService? navigatorService,
  })  : _purchaseStateProvider = purchaseStateProvider,
        _accountRightsStateProvider = accountRightsStateProvider,
        _unityController = unityController,
        _unityUseCaseProvider = unityUseCaseProvider;

  /// 执行退出登录(UI层调用路由跳转，需要依赖context)
  /// 返回退出登录是否成功
  Future<bool> invoke() async {
    final curUid = await _currentUserRepository.getUserId();
    if (curUid == null) {
      return false;
    }
    try {
      // 1. 登出Unity
      await _logoutUnity();

      // 2. 登出服务器
      await _authRepository.logOut(curUid);

      // 3. 清理用户信息
      await _currentUserRepository.clearCurrentUser();

      // 4. 清理账户信息
      await _accountRepository.clearAccount();

      // 5. 停止订单监听和清理信息
      _purchaseStateProvider?.stopOrderStatusCheckAndClean();

      // 6. 停止账户权益状态检查
      _accountRightsStateProvider?.stopAccountRightCheck();
      return true;
    } catch (e) {
      PGLog.e('退出登录失败: $e');
      return false;
    }
  }

  /// 登出Unity
  Future<bool> _logoutUnity() async {
    if (_unityController != null && _unityUseCaseProvider != null) {
      final message = _unityUseCaseProvider.logout.invoke();
      if (message != null) {
        return await _unityController.sendMessage(message);
      }
    }
    return Future.value(false);
  }
}
