import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ui/export_result/use_case/delete_workspace_use_case.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 删除工程，这里处理的非常草率
/// 后续流程中需要针对当前正在删除的工程判断是否正在导出，删除后还需要考虑回滚预扣费相关操作
class DeleteProjectUseCase2 {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;
  final DeleteWorkspaceUseCase _deleteWorkspace;

  DeleteProjectUseCase2(
    this._projectRepository,
    this._currentUserRepository,
    this._deleteWorkspace,
  );

  Future<bool> invoke(String projectId) async {
    try {
      final user = _currentUserRepository.user;
      if (user == null) {
        PGLog.e('用户未登录');
        return false;
      }

      _deleteWorkspace.invoke([projectId]);

      // 删除项目文件夹
      FileManager().deleteProjectDirectory(projectId);

      // 删除项目数据库
      _projectRepository.deleteProject(projectId);

      return true;
    } catch (e) {
      PGLog.e('删除项目失败: $e');
      return false;
    }
  }
}
