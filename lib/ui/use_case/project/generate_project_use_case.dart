import 'dart:io';

import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class GenerateProjectUseCase {
  final MediaRepository _mediaRepository;
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;
  final SettingRepository _settingRepository;

  GenerateProjectUseCase(
    this._mediaRepository,
    this._projectRepository,
    this._currentUserRepository,
    this._settingRepository,
  );

  Future<ProjectInfo?> invoke(
    List<File> files,
    String? customProjectId,
    String? customProjectName,
    ProjectType projectType,
  ) async {
    try {
      final user = _currentUserRepository.user;
      if (user == null) {
        PGLog.e('用户未登录');
        return null;
      }

      // 创建新项目，如果提供了自定义ID则使用，否则生成新的UUID
      final projectId = customProjectId ?? const Uuid().v4();

      // 如果提供了自定义名称则使用，否则使用当前日期
      final projectName = customProjectName ?? DateTimeUtil.getCurrentDateStr();

      final project = ProjectInfo.create(
        name: projectName,
        uuid: projectId,
        version: AppInfo.version,
        author: user.effectiveId,
        exportConfig: await _createExportConfig(projectId),
        projectType: projectType,
      );

      // 创建项目目录
      await FileManager().createProjectDirectory(projectId);

      // 保存项目到数据库
      await _saveProjectToDatabase(project);

      final workspaceFiles =
          await _mediaRepository.generateWorkspaceFiles(projectId, files);

      // 创建工作区
      final workspace = await _createWorkspace(
        projectId,
        project.name,
        workspaceFiles,
      );

      await _projectRepository.updateWorkspace(workspace);

      return project;
    } catch (e) {
      PGLog.e('创建项目失败: $e');
      return null;
    }
  }

  // 创建工作区
  Future<Workspace> _createWorkspace(
    String projectId,
    String workspaceName,
    List<WorkspaceFile> workspaceFiles,
  ) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    return Workspace(
      files: workspaceFiles,
      workspaceName: workspaceName,
      workspaceId: projectId,
      currentFileId: '', //
      createTime: now,
      lastEditTime: now,
    );
  }

  // 创建导出配置
  Future<ExportConfig> _createExportConfig(String projectId) async {
    try {
      final exportDir = FileManager().getExportDirectory(projectId);
      final exportConfig = ExportConfig.defaultConfig().copyWith(
        outputFolder: exportDir.path,
        isReplace: false,
      );
      // 注入全局设置中的导出配置
      return await _settingRepository.getExportConfig(exportConfig);
    } catch (e) {
      PGLog.e('创建导出配置失败: $e');
      rethrow;
    }
  }

  // 保存项目到数据库
  Future<void> _saveProjectToDatabase(ProjectInfo project) async {
    try {
      // 保存到数据库
      await _projectRepository.addProject(project);
      PGLog.d('项目保存到数据库成功: ${project.uuid}');
    } catch (e) {
      PGLog.e('保存项目到数据库失败: $e');
      rethrow;
    }
  }
}
