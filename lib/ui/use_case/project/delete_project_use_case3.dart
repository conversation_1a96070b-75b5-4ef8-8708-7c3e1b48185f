import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/ui/export_result/use_case/delete_workspace_use_case.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 删除工程，这里处理的非常草率
/// 后续流程中需要针对当前正在删除的工程判断是否正在导出，删除后还需要考虑回滚预扣费相关操作
class DeleteProjectUseCase3 {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;
  final DeleteWorkspaceUseCase _deleteWorkspace;

  DeleteProjectUseCase3(
    this._projectRepository,
    this._currentUserRepository,
    this._deleteWorkspace,
  );

  Future<bool> invoke(List<String> projectIds) async {
    try {
      final user = _currentUserRepository.user;
      if (user == null) {
        PGLog.e('用户未登录');
        return false;
      }

      // 调用Unity删除工作区（内部核心为处理导出逻辑数据）
      _deleteWorkspace.invoke(projectIds);

      // 删除项目数据库，并快速通知UI响应
      _projectRepository.markProjectsAsDeleted(projectIds);

      // 异步删除物理文件，避免阻塞主线程
      _deleteProjectFilesInBackground(projectIds);

      return true;
    } catch (e) {
      PGLog.e('删除项目失败: $e');
      return false;
    }
  }

  /// 在后台线程中删除项目文件
  void _deleteProjectFilesInBackground(List<String> projectIds) {
    Future.microtask(() async {
      try {
        // 等待所有文件删除完成
        await _deleteAllProjectFiles(projectIds);

        // 所有文件删除完成后，再进行数据库删除
        await _projectRepository.deleteProjects(projectIds);

        PGLog.d('项目删除完成，共删除 ${projectIds.length} 个项目');
      } catch (e) {
        PGLog.e('后台文件删除过程中发生异常: $e');
      }
    });
  }

  /// 删除所有项目文件，等待所有删除操作完成
  Future<void> _deleteAllProjectFiles(List<String> projectIds) async {
    final futures = <Future<void>>[];

    for (final projectId in projectIds) {
      final future = _deleteSingleProjectFile(projectId);
      futures.add(future);
    }

    // 等待所有文件删除操作完成
    await Future.wait(futures);
  }

  /// 删除单个项目文件
  Future<void> _deleteSingleProjectFile(String projectId) async {
    try {
      await FileManager().deleteProjectDirectory(projectId);
      PGLog.d('项目目录删除成功: $projectId');
    } catch (e) {
      PGLog.e('项目目录删除失败: $projectId, 错误: $e');
      // 可以选择重新抛出异常或者继续处理，这里选择继续处理
    }
  }
}
