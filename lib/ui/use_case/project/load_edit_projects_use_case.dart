import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:turing_art/datalayer/domain/enums/sort_option.dart';

/// 加载项目列表的UseCase
class LoadEditProjectsUseCase {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;

  LoadEditProjectsUseCase(
    this._projectRepository,
    this._currentUserRepository,
  );

  /// 获取项目列表
  Future<List<ProjectInfo>> invoke() async {
    try {
      final User? user = _currentUserRepository.user;
      if (user == null) {
        return <ProjectInfo>[];
      } else {
        return await _getAllProjects(ProjectType.edit);
      }
    } on Exception catch (e) {
      PGLog.e('加载项目失败: $e');
      return <ProjectInfo>[];
    }
  }

  /// 获取所有项目并按排序选项排序
  Future<List<ProjectInfo>> _getAllProjects(ProjectType projectType) async {
    final List<ProjectInfo> projects = await _projectRepository
        .getUserAllProjectsByType(
      _currentUserRepository.user?.effectiveId ?? '',
      projectType,
    )
        .then((List<ProjectInfo> projects) {
      return projects;
    });
    // 从 UserPreferencesService 加载排序选项(只对编辑工程有效)
    final sortOption = UserPreferencesService.getSortOption();

    switch (sortOption) {
      case SortOption.createTime:
        projects.sort((ProjectInfo a, ProjectInfo b) =>
            (b.createdDate ?? DateTime.now())
                .compareTo(a.createdDate ?? DateTime.now()));
        break;
      case SortOption.fileName:
        projects.sort((ProjectInfo a, ProjectInfo b) {
          // 获取完整拼音用于排序(兼容中文和英文)
          final String aPinyin = PinyinHelper.getPinyin(a.name, separator: '');
          final String bPinyin = PinyinHelper.getPinyin(b.name, separator: '');
          return aPinyin.compareTo(bPinyin);
        });
        break;
      case SortOption.lastOpened:
        projects.sort((ProjectInfo a, ProjectInfo b) =>
            (b.updateDate ?? DateTime.now())
                .compareTo(a.updateDate ?? DateTime.now()));
        break;
    }

    return projects;
  }
}
