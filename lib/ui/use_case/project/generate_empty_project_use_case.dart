import 'package:turing_art/datalayer/domain/models/export_config/export_config.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/utils/app_info.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class GenerateEmptyProjectUseCase {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;
  final SettingRepository _settingRepository;

  GenerateEmptyProjectUseCase(
    this._projectRepository,
    this._currentUserRepository,
    this._settingRepository,
  );

  Future<ProjectInfo?> invoke(
    String? projectId,
    String? projectName,
    ProjectType? projectType,
  ) async {
    try {
      final user = _currentUserRepository.user;
      if (user == null) {
        PGLog.e('用户未登录');
        return null;
      }

      // 创建新项目
      final projectIdResult = projectId ?? const Uuid().v4();
      final projectNameResult =
          projectName ?? DateTimeUtil.formatDateTime(DateTime.now());
      final project = ProjectInfo.create(
        name: projectNameResult,
        uuid: projectIdResult,
        version: AppInfo.version,
        author: user.effectiveId,
        exportConfig: await _createExportConfig(projectIdResult),
        projectType: projectType ?? ProjectType.edit,
      );

      // 创建项目目录
      await FileManager().createProjectDirectory(projectIdResult);

      // 保存项目到数据库
      await _projectRepository.addProject(project);
      return project;
    } catch (e) {
      PGLog.e('创建项目失败: $e');
      return null;
    }
  }

  // 创建导出配置
  Future<ExportConfig> _createExportConfig(String projectId) async {
    try {
      final exportDir = FileManager().getExportDirectory(projectId);
      final exportConfig = ExportConfig.defaultConfig().copyWith(
        outputFolder: exportDir.path,
        isReplace: false,
      );
      // 注入全局设置中的导出配置
      return await _settingRepository.getExportConfig(exportConfig);
    } catch (e) {
      PGLog.e('创建导出配置失败: $e');
      rethrow;
    }
  }
}
