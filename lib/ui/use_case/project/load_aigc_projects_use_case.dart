import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 加载项目列表的UseCase
class LoadAigcProjectsUseCase {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;

  LoadAigcProjectsUseCase(
    this._projectRepository,
    this._currentUserRepository,
  );

  /// 获取项目列表
  Future<List<ProjectInfo>> invoke() async {
    try {
      final User? user = _currentUserRepository.user;
      if (user == null) {
        return <ProjectInfo>[];
      } else {
        return await _getAllProjects(ProjectType.aiGen);
      }
    } on Exception catch (e) {
      PGLog.e('加载项目失败: $e');
      return <ProjectInfo>[];
    }
  }

  /// 获取所有项目并按排序选项排序
  Future<List<ProjectInfo>> _getAllProjects(ProjectType projectType) async {
    final List<ProjectInfo> projects = await _projectRepository
        .getUserAllProjectsByType(
      _currentUserRepository.user?.effectiveId ?? '',
      projectType,
    )
        .then((List<ProjectInfo> projects) {
      return projects;
    });
    return projects;
  }
}
