import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 检查项目是否可以覆盖的用例
class CheckProjectOverwriteUseCase {
  final ProjectRepository _projectRepository;
  final CurrentUserRepository _currentUserRepository;

  CheckProjectOverwriteUseCase(
    this._projectRepository,
    this._currentUserRepository,
  );

  /// 检查是否可以覆盖项目
  /// 当projectId不为空且属于当前用户已有的工程时返回true
  Future<bool> invoke(String? projectId) async {
    if (projectId == null || projectId.isEmpty) {
      PGLog.d('项目ID为空，不能覆盖');
      return false;
    }

    try {
      // 获取所有项目
      final List<ProjectInfo> projects =
          await _projectRepository.getAllProjects();

      // 获取当前用户ID
      final String? currentUserId = _currentUserRepository.user?.effectiveId;
      if (currentUserId == null) {
        PGLog.d('当前用户ID为空，不能覆盖');
        return false;
      }

      // 检查是否存在相同ID的项目且属于当前用户
      final bool exists = projects.any(
        (project) =>
            project.uuid == projectId && project.author == currentUserId,
      );

      PGLog.d('项目覆盖检查结果: projectId=$projectId, exists=$exists');
      return exists;
    } catch (e) {
      PGLog.e('检查项目覆盖状态时发生错误: $e');
      return false;
    }
  }
}
