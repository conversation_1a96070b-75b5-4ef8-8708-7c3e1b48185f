import 'dart:io';

import 'package:turing_art/constants/constants.dart';
import 'package:turing_art/core/service/disk_space_size/disk_free_space_size_service.dart';
import 'package:turing_art/core/service/resources_size_calculator/resources_size_calculator.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 工作区磁盘空间检查用例
class GenerateWorkspaceDiskSpaceGuardUseCase {
  final DiskFreeSpaceSizeService _diskSpaceService;
  static const double _minRequiredSpace =
      workspaceDiskSpaceMinFreeSpace; // 1.5GB in bytes

  GenerateWorkspaceDiskSpaceGuardUseCase(this._diskSpaceService);

  /// 检查添加文件后是否有足够的磁盘空间
  /// [files] 要添加的文件列表
  /// @return true 表示空间足够，false 表示空间不足
  Future<bool> invoke({required List<File> files}) async {
    final startTime = DateTime.now();

    try {
      // 先计算文件大小
      final filesSize =
          await ResourcesSizeCalculator.calculateResourcesSize(files);
      PGLog.i('要添加的文件总大小: ${filesSize / (1024 * 1024)}MB');

      // 获取磁盘剩余空间
      final freeSpace =
          await _diskSpaceService.getRootDirectoryFreeDiskSpaceSize();
      if (freeSpace == null) {
        PGLog.e('无法获取工作区目录的剩余空间');
        return false;
      }
      PGLog.i('工作区剩余空间: ${freeSpace / (1024 * 1024)}MB');

      // 检查剩余空间是否足够
      final remainingSpace = freeSpace - filesSize;
      PGLog.i('添加文件后剩余空间: ${remainingSpace / (1024 * 1024)}MB');
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      if (remainingSpace < _minRequiredSpace) {
        PGLog.w(
            '空间不足，需要至少 ${_minRequiredSpace / (1024 * 1024 * 1024)}GB 的剩余空间,耗时: ${duration.inMilliseconds}ms');
        return false;
      }
      PGLog.i('磁盘空间检查完成，耗时: ${duration.inMilliseconds}ms');

      return true;
    } catch (e) {
      PGLog.e('检查磁盘空间时发生错误: $e');
      return false;
    }
  }
}
