import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_presets/utils/preset_image_preloader.dart';
import 'package:turing_art/ui/aigc_presets/widget/preview_image_widget.dart';

/// 预览功能的作用域
class PresetPreviewScope extends StatefulWidget {
  final Widget child;

  const PresetPreviewScope({
    super.key,
    required this.child,
  });

  @override
  State<PresetPreviewScope> createState() => _PresetPreviewScopeState();

  /// 获取最近的预览控制器
  static PresetPreviewController? of(BuildContext context) {
    return context
        .dependOnInheritedWidgetOfExactType<_PresetPreviewInherited>()
        ?.controller;
  }
}

class _PresetPreviewScopeState extends State<PresetPreviewScope> {
  late final PresetPreviewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = PresetPreviewController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _PresetPreviewInherited(
      controller: _controller,
      child: widget.child,
    );
  }
}

class _PresetPreviewInherited extends InheritedWidget {
  final PresetPreviewController controller;

  const _PresetPreviewInherited({
    required this.controller,
    required super.child,
  });

  @override
  bool updateShouldNotify(_PresetPreviewInherited oldWidget) {
    return controller != oldWidget.controller;
  }
}

/// 预览请求事件
class _PreviewEvent {
  final String? imageUrl; // null 表示隐藏预览
  final BuildContext? context;
  final GlobalKey? itemKey;

  _PreviewEvent({
    this.imageUrl,
    this.context,
    this.itemKey,
  });

  bool get isHide => imageUrl == null;
  bool get isShow => imageUrl != null;
}

/// 预览控制器
class PresetPreviewController {
  OverlayEntry? _previewOverlay;

  /// 图片预加载器
  late final PresetImagePreloader _imagePreloader;

  // Stream 控制器
  late final StreamController<_PreviewEvent> _eventController;
  late final StreamSubscription<dynamic> _subscription;

  PresetPreviewController() {
    _imagePreloader = PresetImagePreloader();
    _eventController = StreamController<_PreviewEvent>.broadcast();
    _setupStream();
  }

  void _setupStream() {
    _subscription = _eventController.stream
        .distinct((prev, curr) => prev.imageUrl == curr.imageUrl)
        .transform(_debounceTransformer(const Duration(milliseconds: 200)))
        .listen(_handlePreviewEvent);
  }

  /// 自定义防抖动转换器
  StreamTransformer<T, T> _debounceTransformer<T>(Duration duration) {
    return StreamTransformer<T, T>.fromBind((stream) {
      Timer? debounceTimer;
      late StreamController<T> controller;
      StreamSubscription<T>? subscription;

      controller = StreamController<T>(
        onListen: () {
          subscription = stream.listen(
            (T data) {
              debounceTimer?.cancel();
              debounceTimer = Timer(duration, () {
                controller.add(data);
              });
            },
            onError: controller.addError,
            onDone: controller.close,
          );
        },
        onCancel: () {
          debounceTimer?.cancel();
          subscription?.cancel();
        },
      );

      return controller.stream;
    });
  }

  void _handlePreviewEvent(_PreviewEvent event) {
    if (event.isHide) {
      _removePreviewImmediately();
    } else if (event.isShow) {
      _showPreviewImmediately(event.context!, event.imageUrl!, event.itemKey!);
    }
  }

  /// 显示预览
  void showPreview(BuildContext context, String imageUrl, GlobalKey itemKey) {
    _eventController.add(_PreviewEvent(
      imageUrl: imageUrl,
      context: context,
      itemKey: itemKey,
    ));
  }

  /// 立即显示预览
  void _showPreviewImmediately(
      BuildContext context, String imageUrl, GlobalKey itemKey) {
    _removePreviewImmediately();

    final RenderBox? itemRenderBox =
        itemKey.currentContext?.findRenderObject() as RenderBox?;
    if (itemRenderBox == null) {
      return;
    }

    final Offset itemPosition = itemRenderBox.localToGlobal(Offset.zero);
    final Size itemSize = itemRenderBox.size;

    _previewOverlay = OverlayEntry(
      builder: (context) =>
          _buildPreviewOverlay(imageUrl, itemPosition, itemSize),
    );
    Overlay.of(context).insert(_previewOverlay!);
  }

  /// 移除预览
  void removePreview() {
    _eventController.add(_PreviewEvent()); // 发送隐藏事件
  }

  /// 立即移除预览
  void _removePreviewImmediately() {
    _previewOverlay?.remove();
    _previewOverlay = null;
  }

  /// 强制立即移除预览
  void forceRemovePreview() {
    _removePreviewImmediately();
  }

  Widget _buildPreviewOverlay(
      String imageUrl, Offset itemPosition, Size itemSize) {
    final double previewLeft = itemPosition.dx + itemSize.width + 28;
    final double previewTop = itemPosition.dy;

    return Positioned(
      left: previewLeft,
      top: previewTop,
      child: PreviewImageWidget(
          imageUrl: imageUrl, imagePreloader: _imagePreloader),
    );
  }

  void dispose() {
    _subscription.cancel();
    _eventController.close();
    _removePreviewImmediately();
  }
}
