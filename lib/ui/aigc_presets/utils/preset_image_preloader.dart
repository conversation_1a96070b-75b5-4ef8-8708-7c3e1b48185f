import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

/// 用于预加载网络图片并获取图片尺寸信息
class PresetImagePreloader {
  Size? _cachedImageSize;

  bool _isLoadingImageSize = false;

  Completer<Size?>? _loadingCompleter;

  Size? get cachedImageSize => _cachedImageSize;

  bool get hasImageSize => _cachedImageSize != null;

  /// 预加载图片并获取尺寸
  Future<Size?> preloadImageSize(String imageUrl) async {
    // 如果已经缓存了尺寸，直接返回
    if (_cachedImageSize != null) {
      return _cachedImageSize;
    }

    // 如果正在加载，等待加载完成
    if (_isLoadingImageSize && _loadingCompleter != null) {
      return await _loadingCompleter!.future;
    }

    _isLoadingImageSize = true;
    _loadingCompleter = Completer<Size?>();

    try {
      final ImageProvider imageProvider = CachedNetworkImageProvider(imageUrl);
      final ImageStream stream =
          imageProvider.resolve(ImageConfiguration.empty);

      late ImageStreamListener listener;
      listener = ImageStreamListener(
        (ImageInfo info, bool synchronousCall) {
          final size = Size(
            info.image.width.toDouble(),
            info.image.height.toDouble(),
          );

          // 缓存尺寸
          _cachedImageSize = size;
          _isLoadingImageSize = false;

          stream.removeListener(listener);

          if (!_loadingCompleter!.isCompleted) {
            _loadingCompleter!.complete(size);
          }
        },
        onError: (exception, stackTrace) {
          _isLoadingImageSize = false;
          stream.removeListener(listener);

          if (!_loadingCompleter!.isCompleted) {
            _loadingCompleter!.complete(null);
          }
        },
      );

      stream.addListener(listener);

      return await _loadingCompleter!.future;
    } catch (e) {
      _isLoadingImageSize = false;

      if (!_loadingCompleter!.isCompleted) {
        _loadingCompleter!.complete(null);
      }

      return null;
    }
  }

  /// 清除缓存的图片尺寸
  void clearCache() {
    _cachedImageSize = null;
    _isLoadingImageSize = false;
    _loadingCompleter = null;
  }
}
