/// 网格布局计算工具类
class GridCalculator {
  /// 计算网格布局的列数和每个项目的尺寸
  ///
  /// [containerWidth] 容器宽度
  /// [minItemWidth] 最小项目宽度
  /// [minItemHeight] 最小项目高度
  /// [spacing] 固定间距
  /// [maxColumns] 最大列数
  /// 返回 [列数, 项目宽度, 项目高度]
  static List<dynamic> calculateGridLayout({
    required double containerWidth,
    required double minItemWidth,
    required double minItemHeight,
    required double spacing,
    required int maxColumns,
  }) {
    // 计算宽高比
    final double aspectRatio = minItemWidth / minItemHeight;

    // 计算最大可能的列数
    // 方程: containerWidth = columns * itemWidth + (columns - 1) * spacing
    // 假设itemWidth = minItemWidth，求解最大可能的columns
    int maxPossibleColumns =
        ((containerWidth + spacing) / (minItemWidth + spacing)).floor();

    // 确保列数不超过maxColumns
    int columns =
        maxPossibleColumns > maxColumns ? maxColumns : maxPossibleColumns;

    // 确保至少有1列
    columns = columns < 1 ? 1 : columns;

    // 根据列数和固定间距计算项目宽度
    // 方程: itemWidth = (containerWidth - (columns - 1) * spacing) / columns
    double itemWidth;
    if (columns == 1) {
      // 只有一列时，宽度不超过容器宽度
      itemWidth = containerWidth < minItemWidth ? minItemWidth : containerWidth;
    } else {
      double availableWidth = containerWidth - ((columns - 1) * spacing);
      itemWidth = availableWidth / columns;

      // 如果计算出的宽度小于最小宽度，则减少列数重新计算
      while (itemWidth < minItemWidth && columns > 1) {
        columns--;
        availableWidth = containerWidth - ((columns - 1) * spacing);
        itemWidth = availableWidth / columns;
      }

      // 如果计算出的宽度仍小于最小宽度（只有一列的情况），则使用最小宽度
      if (itemWidth < minItemWidth) {
        itemWidth = minItemWidth;
      }
    }

    // 根据宽高比计算高度
    double itemHeight = itemWidth / aspectRatio;

    return [columns, itemWidth, itemHeight];
  }

  /// 根据固定间距和列数计算项目宽度
  ///
  /// [containerWidth] 容器宽度
  /// [columns] 列数
  /// [spacing] 固定间距
  /// [minItemWidth] 最小项目宽度
  /// 返回不小于最小宽度的项目宽度
  static double calculateItemWidthWithFixedSpacing({
    required double containerWidth,
    required int columns,
    required double spacing,
    required double minItemWidth,
  }) {
    if (columns <= 1) return containerWidth.clamp(minItemWidth, containerWidth);

    // 总间距宽度
    final double totalSpacingWidth = spacing * (columns - 1);

    // 剩余给items的宽度
    final double availableWidthForItems = containerWidth - totalSpacingWidth;

    // 平均分配给每个item的宽度
    double itemWidth = availableWidthForItems / columns;

    // 确保不小于最小宽度
    return itemWidth >= minItemWidth ? itemWidth : minItemWidth;
  }

  /// 计算网格布局的每行项目数
  ///
  /// [containerWidth] 容器宽度
  /// [itemWidth] 项目宽度
  /// [spacing] 项目之间的间距
  /// [minCount] 最小列数
  /// [maxCount] 最大列数
  static int calculateGridCount({
    required double containerWidth,
    required double itemWidth,
    required double spacing,
    int minCount = 1,
    int maxCount = 10,
  }) {
    // 计算可能的最大列数
    final double availableWidth =
        containerWidth + spacing; // 加上spacing是因为最后一项的右侧不需要spacing
    final double itemTotalWidth = itemWidth + spacing;

    // 计算可容纳的列数
    int count = (availableWidth / itemTotalWidth).floor();

    // 确保列数在最小和最大范围内
    count = count.clamp(minCount, maxCount);

    return count;
  }

  /// 计算实际项目尺寸以适应容器
  ///
  /// [containerWidth] 容器宽度
  /// [itemCount] 项目数量
  /// [spacing] 项目间距
  /// [minItemWidth] 最小项目宽度（可选）
  static double calculateActualItemWidth({
    required double containerWidth,
    required int itemCount,
    required double spacing,
    double? minItemWidth,
  }) {
    if (itemCount <= 1) {
      return minItemWidth != null
          ? minItemWidth.clamp(minItemWidth, containerWidth)
          : containerWidth;
    }

    final double totalSpacing = spacing * (itemCount - 1);
    final double itemWidth = (containerWidth - totalSpacing) / itemCount;

    // 如果提供了最小宽度，确保计算结果不小于最小宽度
    if (minItemWidth != null) {
      return itemWidth > minItemWidth ? itemWidth : minItemWidth;
    }

    return itemWidth;
  }

  /// 计算实际间距使两端对齐
  ///
  /// [containerWidth] 容器宽度
  /// [itemWidth] 项目宽度
  /// [itemCount] 项目数量
  /// [minSpacing] 最小间距
  static double calculateSpacingForEdgeAlignment({
    required double containerWidth,
    required double itemWidth,
    required int itemCount,
    required double minSpacing,
  }) {
    if (itemCount <= 1) return 0;

    // 计算总项目宽度
    final double totalItemsWidth = itemWidth * itemCount;

    // 计算剩余可用于间距的宽度
    final double availableSpacingWidth = containerWidth - totalItemsWidth;

    // 计算每个间隙的间距（项目数量-1个间隙）
    final double spacing = availableSpacingWidth / (itemCount - 1);

    // 确保间距不小于最小值
    return spacing < minSpacing ? minSpacing : spacing;
  }
}
