import 'package:flutter/material.dart';

/// AIGC 预设展开状态管理 Provider
class AigcPresetExpandProvider extends ChangeNotifier {
  // 主弹窗展开状态
  bool _isMainExpanded = false;

  // 重新生成区域展开状态
  bool _isRegenerateExpanded = false;

  /// 主弹窗是否展开
  bool get isMainExpanded => _isMainExpanded;

  /// 重新生成区域是否展开
  bool get isRegenerateExpanded => _isRegenerateExpanded;

  /// 切换主弹窗展开状态
  void toggleMainExpanded() {
    _isMainExpanded = !_isMainExpanded;
    notifyListeners();
  }

  /// 切换重新生成区域展开状态
  void toggleRegenerateExpanded() {
    _isRegenerateExpanded = !_isRegenerateExpanded;
    notifyListeners();
  }

  void toggleRegenerateExpandedWithoutAnimation() {
    _isRegenerateExpanded = !_isRegenerateExpanded;
  }

  /// 重置所有状态
  void reset() {
    _isMainExpanded = false;
    _isRegenerateExpanded = false;
    notifyListeners();
  }
}
