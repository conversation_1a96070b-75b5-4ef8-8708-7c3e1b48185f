import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_loop_model.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 轮询管理器 - 封装轮询相关逻辑
class _PollingManager {
  Timer? _timer;
  bool _isPolling = false;
  final Duration _interval;
  final Future<void> Function() _pollFunction;
  final bool Function() _shouldContinuePolling;

  _PollingManager({
    required Duration interval,
    required Future<void> Function() pollFunction,
    required bool Function() shouldContinuePolling,
  })  : _interval = interval,
        _pollFunction = pollFunction,
        _shouldContinuePolling = shouldContinuePolling;

  bool get isPolling => _isPolling;

  void start() {
    if (_isPolling) {
      return;
    }

    _isPolling = true;
    PGLog.d("启动预设轮询服务");

    // 立即执行一次轮询
    _pollFunction();

    // 设置定时器定期轮询
    _timer = Timer.periodic(_interval, (_) async {
      if (_shouldContinuePolling()) {
        await _pollFunction();
      } else {
        stop();
      }
    });
  }

  void stop() {
    if (!_isPolling) {
      return;
    }

    _timer?.cancel();
    _timer = null;
    _isPolling = false;
    PGLog.d("停止预设轮询服务");
  }

  void dispose() {
    stop();
  }
}

class AigcPresetsViewModel extends ChangeNotifier {
  // 所有预设
  List<AigcPcPresetsDetailResponse> _presets = [];

  List<AigcPcPresetsDetailResponse> _completedPresets = [];
  List<AigcPcPresetsDetailResponse> _runningPresets = [];

  // 预设仓库
  final AigcPresetsRepository _repository;
  final AccountRepository _accountRepository;

  // 当前选中的tab
  int _selectedTab = 0;

  int get selectedTab => _selectedTab;

  // 轮询管理器
  late final _PollingManager _pollingManager;

  bool get isPolling => _pollingManager.isPolling;

  // Stream订阅管理
  StreamSubscription<List<AigcPcPresetsDetailResponse>>? _presetsSubscription;

  // 创建预设弹窗相关状态
  String? _maskPath;
  String? _previewPath;
  bool _shouldShowCreateDialog = false;

  // 创建预设弹窗相关getter
  String? get maskPath => _maskPath;

  String? get previewPath => _previewPath;

  bool get shouldShowCreateDialog => _shouldShowCreateDialog;

  AigcPresetsViewModel(
      {required AigcPresetsRepository repository,
      required AccountRepository accountRepository})
      : _repository = repository,
        _accountRepository = accountRepository {
    // 初始化轮询管理器
    _pollingManager = _PollingManager(
      interval: const Duration(seconds: 10),
      pollFunction: _pollRunningPresets,
      shouldContinuePolling: () => _runningPresets.isNotEmpty,
    );

    _subscribeToPresetsStream();
  }

  void _subscribeToPresetsStream() {
    // 订阅预设数据流
    _presetsSubscription = _repository.getAigcPresetsStream().listen((presets) {
      _handlePresetsUpdate(presets);
    }, onError: (error) {
      PGLog.e("监听预设数据失败: $error");
      _presets = [];
      _completedPresets = [];
      _runningPresets = [];
      notifyListeners();
    });
  }

  // 设置创建预设弹窗的参数
  void setCreatePresetParams(String? maskPath, String? previewPath) {
    if (maskPath != null &&
        previewPath != null &&
        maskPath.isNotEmpty &&
        previewPath.isNotEmpty) {
      _maskPath = maskPath;
      _previewPath = previewPath;
      _shouldShowCreateDialog = true;
      notifyListeners();
    }
  }

  // 重置创建预设弹窗的状态
  void resetCreatePresetDialogState() {
    _shouldShowCreateDialog = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _presetsSubscription?.cancel();
    _pollingManager.dispose();
    super.dispose();
  }

  /// 处理预设数据更新
  void _handlePresetsUpdate(List<AigcPcPresetsDetailResponse> presets) {
    _presets = presets;
    // 检查是否有失败状态，如果有则刷新账户信息
    _refreshAccountIfFailed();

    _updatePresetLists();

    // 如果有运行中的预设，自动启动轮询
    if (_runningPresets.isNotEmpty) {
      startPolling();
    }

    // 通知监听器更新UI
    notifyListeners();
  }

  /// 检查是否有失败状态，后台发生退钱的情况，需要实时同步账户信息
  void _refreshAccountIfFailed() {
    bool hasFailure =
        _presets.any((preset) => preset.status == AigcRequestConst.failed);
    if (hasFailure) {
      _accountRepository.refreshAllAccount().catchError((e) {
        PGLog.e('刷新账户信息失败: $e');
      });
    }
  }

  /// 更新预设列表分类
  void _updatePresetLists() {
    _completedPresets =
        _presets.where((e) => e.status != AigcRequestConst.running).toList();

    _runningPresets =
        _presets.where((e) => e.status == AigcRequestConst.running).toList();
  }

  void setSelectedTab(int index, {bool loadData = true}) {
    if (_selectedTab != index) {
      _selectedTab = index;
      notifyListeners(); // 立即通知UI更新
    }

    if (loadData) {
      refreshPresetsData();
    }

    // 如果切换了tab则再启动一次轮询
    if (_runningPresets.isNotEmpty) {
      startPolling();
    }
  }

  // 添加presets getter
  List<AigcPcPresetsDetailResponse> get presets => _presets;

  List<AigcPcPresetsDetailResponse> get completedPresets => _completedPresets;

  List<AigcPcPresetsDetailResponse> get runningPresets => _runningPresets;

  //Mark: 轮询相关方法

  /// 启动轮询服务
  void startPolling() {
    _pollingManager.start();
  }

  /// 停止轮询服务
  void stopPolling() {
    _pollingManager.stop();
  }

  /// 执行轮询
  Future<void> _pollRunningPresets() async {
    if (_runningPresets.isEmpty) {
      stopPolling();
      return;
    }

    try {
      // 获取所有运行中预设的ID
      final List<String> runningPresetIds =
          _runningPresets.map((preset) => preset.id).toList();

      // 调用API获取最新状态
      final List<AigcPcPresetsLoopModel> pollingResults =
          await _repository.pollAigcPresets(runningPresetIds);

      // 处理轮询结果
      _handlePollingResults(pollingResults);
    } catch (e) {
      PGLog.e("轮询预设失败: $e");
    }
  }

  /// 处理轮询结果
  void _handlePollingResults(List<AigcPcPresetsLoopModel> results) {
    bool needRefresh = false;
    PGLog.d(
        "_handlePollingResults轮询结果: ${results.first.id} ${results.first.status}");
    for (var result in results) {
      if (result.status != AigcRequestConst.running) {
        PGLog.d("轮询结果改变: ${result.id} ${result.status}");
        needRefresh = true;
        break;
      }
    }
    // 如果有状态变化，触发数据刷新
    if (needRefresh) {
      refreshPresetsData();
    }

    // 如果结果中没有running，则停止轮询
    if (results.every((result) => result.status != AigcRequestConst.running)) {
      stopPolling();
    }
  }

  /// 内部刷新预设数据
  Future<void> refreshPresetsData() async {
    try {
      // 调用Repository的刷新方法，数据会通过现有的Stream推送
      await _repository.refreshPresets();
    } catch (e) {
      PGLog.e("刷新预设数据失败: $e");
    }
  }
}
