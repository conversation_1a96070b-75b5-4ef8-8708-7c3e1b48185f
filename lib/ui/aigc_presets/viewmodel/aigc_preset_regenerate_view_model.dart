import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/service/api/common_error_handler.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/ui/aigc_presets/config/aigc_config.dart';
import 'package:turing_art/ui/core/base_view_model.dart';
import 'package:turing_art/utils/common_state.dart';
import 'package:turing_art/utils/error_handler.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 重新生成预设弹窗的 ViewModel
class AIGCPresetRegenerateViewModel extends BaseViewModel {
  final String _presetId;
  final AigcPresetsRepository _repository;
  final OpsCustomTableRepository _customTableRepository;
  int _regenerateCost = 1;

  final TextEditingController _supplementController = TextEditingController();

  /// 重新生成张数
  int _regenerateCount = AigcConfig.defaultRegenerateCount;

  /// 重新生成状态，用于控制页面刷新
  AsyncResult<AigcPcPresetsDetailResponse> _regenerateStatus =
      const AsyncResult.idle();

  /// 是否正在加载
  bool get isLoading => _regenerateStatus.isLoading;

  /// 重新生成张数
  int get regenerateCount => _regenerateCount;

  /// 创意补充输入控制器
  TextEditingController get supplementController => _supplementController;

  /// 重新生成结果
  AsyncResult<AigcPcPresetsDetailResponse> get regenerateResult =>
      _regenerateStatus;

  int get regenerateCost => (_regenerateCost * _regenerateCount);

  AIGCPresetRegenerateViewModel({
    required String presetId,
    required AigcPresetsRepository repository,
    required OpsCustomTableRepository customTableRepository,
  })  : _presetId = presetId,
        _repository = repository,
        _customTableRepository = customTableRepository,
        super(ErrorHandler()) {
    _fetchAigcPointConfig();
  }

  Future<void> _fetchAigcPointConfig() async {
    try {
      final config = await _customTableRepository.getAigcPointConfig();
      _regenerateCost =
          config?.points?.firstWhere((e) => e.type == 'preset').value ?? 1;
      notifyListeners();
    } catch (e) {
      PGLog.e('Failed to fetch aigc point config: $e');
    }
  }

  /// 增加再次生成张数
  void increaseRegenerateCount() {
    if (_regenerateCount < AigcConfig.maxGenerateCount) {
      _setGenerateCount(_regenerateCount + 3);
    }
  }

  /// 减少再次生成张数
  void decreaseRegenerateCount() {
    if (_regenerateCount > AigcConfig.minGenerateCount) {
      _setGenerateCount(_regenerateCount - 3);
    }
  }

  /// 设置生成张数
  void _setGenerateCount(int count) {
    if (_regenerateCount != count) {
      _regenerateCount = count;
      notifyListeners();
    }
  }

  void resetStatus() {
    _supplementController.clear();
    _regenerateStatus = const AsyncResult.idle();
    notifyListeners();
  }

  /// 开始重新生成
  Future<void> startRegenerate() async {
    if (_regenerateStatus.isLoading) {
      return;
    }

    final supplement = _supplementController.text.trim();

    // 设置加载状态
    _regenerateStatus = const AsyncResult.loading();
    notifyListeners();

    try {
      PGLog.d(
          '开始重新生成 - 预设ID: $_presetId, 生成张数: $_regenerateCount, 创意补充: $supplement');

      final response = await _repository.regenerateAigcPresets(
          _presetId, supplement, regenerateCount);

      PGLog.d('重新生成成功');

      // 设置成功状态
      _regenerateStatus = AsyncResult.success(response);
      notifyListeners();
    } catch (e) {
      PGLog.e('重新生成失败: $e');

      // 使用统一的错误处理器
      final (errorMessage, _) =
          CommonBusinessErrorHandler.handleError(e, '重新生成预设');
      _setErrorStatus(
          errorMessage, e is Exception ? e : Exception(e.toString()));
    }
  }

  /// 设置错误状态，包含网络检测
  void _setErrorStatus(String message, Exception exception) {
    final finalMessage = handleException(exception, message);
    _regenerateStatus = AsyncResult.error(finalMessage, exception);
    notifyListeners();
  }

  @override
  void dispose() {
    _supplementController.dispose();
    super.dispose();
  }
}
