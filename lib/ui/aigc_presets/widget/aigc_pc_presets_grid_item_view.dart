import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_presets_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_preset_detail_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

class AigcPcPresetsGridItemView extends StatelessWidget {
  const AigcPcPresetsGridItemView({
    super.key,
    required this.model,
    this.width,
    this.height,
  });

  final AigcPcPresetsDetailResponse model;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    // 获取父容器传入的宽高，如果未传入则使用默认值
    final itemWidth = width ?? 186.0;
    final imageSize = itemWidth; // 图片宽高相等，为正方形
    final textWidth = itemWidth;

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          PGLog.d('点击预设: ${model.name}');

          final viewModel = context.read<AigcPresetsViewModel>();

          // 打开预设详情对话框
          AigcPresetDetailDialog.show(
            context,
            presetId: model.id,
            initialData: model,
            onRefresh: () {
              viewModel.refreshPresetsData();
            },
          );
        },
        child: Column(
          children: [
            Stack(
              children: [
                // 图片容器
                Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: _buildCoverImage(model, imageSize),
                  ),
                ),
                if (!model.hasAnyEffectInUsage())
                  Container(
                    width: imageSize,
                    height: imageSize,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.4),
                    ),
                  ),
                if (model.status == AigcRequestConst.completed)
                  // 左下角文案
                  Positioned(
                    left: 8,
                    bottom: 8,
                    child: _buildCreativeLabel(model),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: textWidth,
              height: 18,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  model.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    color: const Color(0xFFFFFFFF),
                  ),
                  textAlign: TextAlign.left,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(height: 2),
            SizedBox(
              width: textWidth,
              height: 18,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  //  model.updateAt 转为日期 和时分
                  model.status == AigcRequestConst.failed
                      ? '生成失败'
                      : model.status == AigcRequestConst.completed
                          ? formatDate(model.updateAt)
                          : '生成中...',
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.medium,
                    color: const Color(0xFFFFFFFF).withOpacity(0.5),
                  ),
                  textAlign: TextAlign.left,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverImage(AigcPcPresetsDetailResponse model, double imageSize) {
    final thumbnailUrl = model.findUseableThumbnailUrl();
    // 如果预设状态为失败，则显示失败图标
    if (model.status == AigcRequestConst.failed) {
      return Image.asset(
        'assets/icons/aigc_presets_error_icon.png',
        width: imageSize,
        height: imageSize,
        fit: BoxFit.cover,
      );
    }
    // 如果预设中存在失败的效果，则显示失败图标
    var hasFailedEffect = model.effects.any((effect) => effect.effectList
        .any((effect) => effect.status == AigcRequestConst.failed));
    if (hasFailedEffect) {
      return Image.asset(
        'assets/icons/aigc_presets_error_icon.png',
        width: imageSize,
        height: imageSize,
        fit: BoxFit.cover,
      );
    }
    return thumbnailUrl != null && thumbnailUrl.isNotEmpty
        ? CachedImageWidget(
            imageUrl: thumbnailUrl,
            width: imageSize,
            height: imageSize,
            fit: BoxFit.cover,
            placeholder: AigcItemAnimation(
              child: Image.asset(
                'assets/icons/aigc_presets_loading_icon.png',
                width: imageSize,
                height: imageSize,
                fit: BoxFit.cover,
              ),
            ),
            showProgress: false,
            errorWidget: Image.asset(
              'assets/icons/aigc_presets_loading_icon.png',
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
            ),
          )
        : AigcItemAnimation(
            child: Image.asset(
              'assets/icons/aigc_presets_loading_icon.png',
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
            ),
          );
  }

  // 构建创意标签
  Widget _buildCreativeLabel(AigcPcPresetsDetailResponse model) {
    // 检查是否有选中的创意
    bool hasSelectedCreative = model.hasAnyEffectInUsage();

    // 获取创意数量
    int creativeCount = model.getEffectInUsageCount();

    // 设置标签文本和样式
    String labelText = hasSelectedCreative ? "$creativeCount-创意" : "未选择创意";

    // 根据不同状态设置不同样式
    if (hasSelectedCreative) {
      // X个创意样式：背景色为E6E6E6，边框为白色，高度24
      return Container(
        height: 24,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: const Color(0xFFE6E6E6),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: Colors.white,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            labelText,
            style: TextStyle(
              color: const Color(0xFF1F1F1F),
              fontSize: 12,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.medium,
            ),
          ),
        ),
      );
    } else {
      // 未选择创意样式：背景色为1F1F1F，0.8透明度，边框为白色0.1透明度，高度24
      return Container(
        height: 24,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: const Color(0xFF1F1F1F).withOpacity(0.8),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            labelText,
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.medium,
            ),
          ),
        ),
      );
    }
  }

  String formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return "$month-$day $hour:$minute";
  }
}
