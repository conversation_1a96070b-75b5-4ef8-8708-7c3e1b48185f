import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_presets/utils/preset_image_preloader.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

/// 预览图片组件，支持根据图片尺寸自动缩放
class PreviewImageWidget extends StatefulWidget {
  final String imageUrl;
  final PresetImagePreloader imagePreloader; // 图片预加载器

  const PreviewImageWidget({
    super.key,
    required this.imageUrl,
    required this.imagePreloader,
  });

  @override
  State<PreviewImageWidget> createState() => _PreviewImageWidgetState();
}

class _PreviewImageWidgetState extends State<PreviewImageWidget> {
  static const double _maxPreviewImageSize = 400.0; // 预览图片最大尺寸

  Size? _imageSize;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImageSize();
  }

  /// 加载图片并获取其尺寸
  Future<void> _loadImageSize() async {
    // 获取预加载器实例
    PresetImagePreloader preloader = widget.imagePreloader;

    // 优先从预加载器缓存中获取尺寸
    if (preloader.hasImageSize) {
      if (mounted) {
        setState(() {
          _imageSize = preloader.cachedImageSize;
          _isLoading = false;
        });
      }
      return;
    }

    // 如果有预加载器，使用预加载器的方法
    try {
      final size = await preloader.preloadImageSize(widget.imageUrl);
      if (mounted) {
        setState(() {
          _imageSize = size;
          _isLoading = false;
          _hasError = size == null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    }
  }

  /// 计算预览尺寸，最大400x400px，保持宽高比
  Size _calculatePreviewSize() {
    if (_imageSize == null) {
      return const Size(_maxPreviewImageSize, _maxPreviewImageSize); // 默认尺寸
    }

    final double imageWidth = _imageSize!.width;
    final double imageHeight = _imageSize!.height;

    // 如果图片尺寸都小于等于最大尺寸，直接使用原尺寸
    if (imageWidth <= _maxPreviewImageSize &&
        imageHeight <= _maxPreviewImageSize) {
      return _imageSize!;
    }

    // 计算缩放比例
    final double widthRatio = _maxPreviewImageSize / imageWidth;
    final double heightRatio = _maxPreviewImageSize / imageHeight;
    final double scale = widthRatio < heightRatio ? widthRatio : heightRatio;

    return Size(
      imageWidth * scale,
      imageHeight * scale,
    );
  }

  @override
  Widget build(BuildContext context) {
    final Size previewSize = _calculatePreviewSize();

    return Container(
      width: previewSize.width,
      height: previewSize.height,
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: const Color(0x33FFFFFF),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000),
            blurRadius: 20,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: _isLoading
            ? _buildLoadingWidget(previewSize)
            : _hasError
                ? _buildErrorWidget(previewSize)
                : CachedImageWidget(
                    imageUrl: widget.imageUrl,
                    width: previewSize.width,
                    height: previewSize.height,
                    fit: BoxFit.cover,
                    placeholder: _buildLoadingWidget(previewSize),
                    errorWidget: _buildErrorWidget(previewSize),
                  ),
      ),
    );
  }

  Widget _buildLoadingWidget(Size size) {
    return Container(
      width: size.width,
      height: size.height,
      color: const Color(0xFF1F1F1F),
      child: const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFF72561),
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildErrorWidget(Size size) {
    return Container(
      width: size.width,
      height: size.height,
      color: const Color(0xFF363636),
      child: Image.asset(
        'assets/icons/aigc_credit_start_medium.png',
        width: 32,
        height: 32,
      ),
    );
  }
}
