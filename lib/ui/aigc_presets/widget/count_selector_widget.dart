import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 可复用的计数选择器组件
class CountSelectorWidget extends StatelessWidget {
  /// 当前计数值
  final int count;

  /// 减少计数的回调
  final VoidCallback? onDecrease;

  /// 增加计数的回调
  final VoidCallback? onIncrease;

  /// 组件宽度
  final double? width;

  /// 组件高度
  final double height;

  /// 最小值限制
  final int? minValue;

  /// 最大值限制
  final int? maxValue;

  const CountSelectorWidget({
    super.key,
    required this.count,
    this.onDecrease,
    this.onIncrease,
    this.width,
    this.height = 32,
    this.minValue,
    this.maxValue,
  });

  @override
  Widget build(BuildContext context) {
    // 判断是否可以减少
    final canDecrease = minValue == null || count > minValue!;
    // 判断是否可以增加
    final canIncrease = maxValue == null || count < maxValue!;

    return Container(
      height: height,
      width: width,
      constraints: width == null ? const BoxConstraints(minWidth: 72) : null,
      decoration: BoxDecoration(
        color: const Color(0xFF383838),
        borderRadius: BorderRadius.circular(6),
      ),
      padding: const EdgeInsets.all(4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 减少按钮
          GestureDetector(
            onTap: canDecrease ? onDecrease : null,
            child: Opacity(
              opacity: canDecrease ? 1.0 : 0.4,
              child: Image.asset(
                'assets/icons/aigc_icon_minus.png',
                width: 24,
                height: 24,
              ),
            ),
          ),

          // 计数显示
          Text(
            '$count',
            style: TextStyle(
              color: const Color(0xFFFFFFFF),
              fontSize: 14,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),

          // 增加按钮
          GestureDetector(
            onTap: canIncrease ? onIncrease : null,
            child: Opacity(
              opacity: canIncrease ? 1.0 : 0.3,
              child: Image.asset(
                'assets/icons/aigc_icon_add.png',
                width: 24,
                height: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
