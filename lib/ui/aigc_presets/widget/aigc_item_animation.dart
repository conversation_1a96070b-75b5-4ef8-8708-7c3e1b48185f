import 'package:flutter/material.dart';

class AigcItemAnimation extends StatefulWidget {
  /// 要添加动画效果的子组件
  final Widget child;

  /// 动画持续时间
  final Duration duration;

  /// 动画是否自动开始
  final bool autoPlay;

  /// 动画循环次数，0表示无限循环
  final int repeatCount;

  /// 动画图标大小，默认为父视图的一半大小
  final double? iconSize;

  const AigcItemAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1500),
    this.autoPlay = true,
    this.repeatCount = 0,
    this.iconSize,
  });

  @override
  State<AigcItemAnimation> createState() => _AigcItemAnimationState();
}

class _AigcItemAnimationState extends State<AigcItemAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int _currentRepeatCount = 0;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    // 创建从上到下的动画
    _animation = Tween<double>(begin: -1.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    // 监听动画状态
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _currentRepeatCount++;

        // 检查是否需要继续重复动画
        if (widget.repeatCount == 0 ||
            _currentRepeatCount < widget.repeatCount) {
          _controller.reset();
          _controller.forward();
        }
      }
    });

    // 如果设置为自动播放，则启动动画
    if (widget.autoPlay) {
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 手动启动动画
  void play() {
    if (!_controller.isAnimating) {
      _currentRepeatCount = 0;
      _controller.reset();
      _controller.forward();
    }
  }

  /// 停止动画
  void stop() {
    if (_controller.isAnimating) {
      _controller.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取父视图的大小
        final parentWidth = constraints.maxWidth;
        final parentHeight = constraints.maxHeight;

        // 计算图标大小，默认为父视图宽度的一半
        final iconWidth = widget.iconSize ?? parentWidth;
        final iconHeight = widget.iconSize ?? parentHeight;

        return Stack(
          children: [
            // 子组件
            widget.child,

            // 动画效果
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                // 计算动画图标的位置
                return Positioned.fill(
                  child: ClipRect(
                    child: FractionalTranslation(
                      translation: Offset(0, _animation.value),
                      child: Opacity(
                        opacity: (1.0 - _animation.value.abs()) * 0.8,
                        child: Center(
                          child: Image.asset(
                            'assets/icons/aigc_item_animation_icon.png',
                            width: iconWidth,
                            height: iconHeight,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}

/// 提供一个全局访问动画控制器的方法
class AigcItemAnimationController {
  final _AigcItemAnimationState _state;

  AigcItemAnimationController(this._state);

  void play() => _state.play();
  void stop() => _state.stop();
}

/// 扩展AigcItemAnimation，提供获取控制器的方法
extension AigcItemAnimationStateExtension on AigcItemAnimation {
  AigcItemAnimationController getController(BuildContext context) {
    final state = context.findAncestorStateOfType<_AigcItemAnimationState>();
    if (state == null) {
      throw Exception('未找到AigcItemAnimation的State');
    }
    return AigcItemAnimationController(state);
  }
}
