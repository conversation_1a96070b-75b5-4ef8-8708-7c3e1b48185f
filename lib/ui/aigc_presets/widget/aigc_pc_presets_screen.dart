import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/routing/routes.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_eidt_top_bar_widget.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_presets_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_create_preset_dialog.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_pc_presets_grid_view.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog2.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class AigcPcPresetsScreen extends StatefulWidget {
  final String? maskPath;
  final String? previewPath;

  const AigcPcPresetsScreen({super.key, this.maskPath, this.previewPath});

  @override
  State<AigcPcPresetsScreen> createState() => _AigcPcPresetsScreenState();
}

class _AigcPcPresetsScreenState extends State<AigcPcPresetsScreen> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        final viewModel = AigcPresetsViewModel(
          repository: context.read<AigcPresetsRepository>(),
          accountRepository: context.read<AccountRepository>(),
        );

        // 如果有传入参数，则设置到ViewModel中
        if (widget.maskPath != null &&
            widget.previewPath != null &&
            widget.maskPath!.isNotEmpty &&
            widget.previewPath!.isNotEmpty) {
          viewModel.setCreatePresetParams(
              widget.maskPath!, widget.previewPath!);
        }

        return viewModel;
      },
      child: const _AigcPcPresetsContent(),
    );
  }

  @override
  void initState() {
    super.initState();
    // 在页面初始化完成后检查网络连接
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkNetworkConnection();
    });
  }

  /// 检查网络连接状态
  Future<void> _checkNetworkConnection() async {
    try {
      final networkProvider = context.read<NetworkProvider>();
      final isConnected = await networkProvider.isConnected();

      if (!isConnected && mounted) {
        PGDialog.showToast('网络错误，请检查网络连接');
      }
    } catch (e) {
      PGLog.e('检查网络连接失败: $e');
    }
  }
}

class _AigcPcPresetsContent extends StatefulWidget {
  const _AigcPcPresetsContent();

  @override
  State<_AigcPcPresetsContent> createState() => _AigcPcPresetsContentState();
}

class _AigcPcPresetsContentState extends State<_AigcPcPresetsContent> {
  final ValueNotifier<bool> _hoveredBackNotifier = ValueNotifier<bool>(false);

  @override
  void dispose() {
    _hoveredBackNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽度
    final screenWidth = MediaQuery.of(context).size.width;

    // 计算水平边距
    // 当屏幕宽度 <= 1920 时，边距固定为 160
    // 当屏幕宽度 > 1920 时，边距动态计算，保证内容区域宽度为 1600
    final double horizontalPadding =
        screenWidth <= 1920 ? 160.0 : (screenWidth - 1600) / 2;

    const double topBarHeight = 56.0;
    const double tabBarHeight = 48.0;

    return Consumer<AigcPresetsViewModel>(
      builder: (context, presetsModel, _) {
        // 使用Selector监听是否需要显示创建预设弹窗
        return Selector<AigcPresetsViewModel, bool>(
          selector: (_, viewModel) => viewModel.shouldShowCreateDialog,
          builder: (context, shouldShowDialog, _) {
            if (shouldShowDialog) {
              // 延迟执行，等待页面完全加载
              Future.delayed(const Duration(milliseconds: 200), () {
                if (mounted) {
                  // 重置弹窗状态，避免重复显示
                  presetsModel.resetCreatePresetDialogState();
                  _showCreatePresetDialog(presetsModel);
                }
              });
            }

            return Scaffold(
              backgroundColor: const Color(0xFF0D0D0D),
              body: TitleBarWidget(
                backgroundColor: const Color(0xFF0D0D0D),
                funcWidget: AigcEditTopBarWidget(
                  score: 0,
                  backgroundColor: const Color(0xFF0D0D0D),
                  showBackButton: false,
                  showPurchaseButton: false,
                  showPresetButton: false, // 在预设页面隐藏预设按钮
                  onExportPressed: () {
                    // 处理导出点击事件，默认选中AIGC导出
                    ExportListDialog2.show(defaultExportType: ExportType.aigc);
                  },
                  onMakingCenterPressed: () {
                    // 处理打样中心点击事件
                    final navigator = GoRouterNavigatorService(context);
                    navigator.smartPush(Routes.aigcSample);
                  },
                ),
                child: Column(
                  children: [
                    // TopBar区域
                    Container(
                      height: topBarHeight,
                      margin:
                          EdgeInsets.symmetric(horizontal: horizontalPadding),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildBackButton(),
                        ],
                      ),
                    ),

                    // Content区域
                    Expanded(
                      child: Container(
                        margin:
                            EdgeInsets.symmetric(horizontal: horizontalPadding),
                        color: const Color(0xFF0D0D0D),
                        child: Column(
                          children: [
                            // 自定义Tab切换栏
                            SizedBox(
                                height: tabBarHeight,
                                width: double.infinity,
                                child: Stack(
                                  children: [
                                    Container(
                                      width: double.infinity,
                                      height: tabBarHeight,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          bottom: BorderSide(
                                            color: Colors.white
                                                .withOpacity(0.1), // 白色透明度0.1
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        // 已生成标签
                                        _buildTabItem(0, '已完成'),
                                        const SizedBox(width: 32), // 间距32
                                        // 正在生成标签
                                        _buildTabItem(1, '正在处理'),
                                      ],
                                    ),
                                  ],
                                )),
                            // 内容区域
                            Expanded(
                              child: _buildTabContent(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 构建返回按钮，使用ValueListenableBuilder监听悬停状态
  Widget _buildBackButton() {
    return ValueListenableBuilder<bool>(
      valueListenable: _hoveredBackNotifier,
      builder: (context, isHovered, _) {
        return PlatformMouseRegion(
          onEnter: (_) => _hoveredBackNotifier.value = true,
          onExit: (_) => _hoveredBackNotifier.value = false,
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 32,
              decoration: BoxDecoration(
                color: isHovered
                    ? Colors.white.withOpacity(0.05)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 2),
              child: Row(
                children: [
                  Image.asset(
                    'assets/icons/aigc_presets_back_icon.png',
                    color: Colors.white,
                    width: 24,
                    height: 24,
                  ),
                  const SizedBox(width: 1),
                  Text(
                    '主题预设管理',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.medium,
                    ),
                  ),
                  const SizedBox(width: 10),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTabItem(int index, String title) {
    return Consumer<AigcPresetsViewModel>(
      builder: (context, presetsModel, _) {
        final bool isSelected = presetsModel.selectedTab == index;

        return InkWell(
          onTap: () {
            presetsModel.setSelectedTab(index, loadData: false);
          },
          child: Container(
            height: 48,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: isSelected ? Colors.white : Colors.transparent,
                  width: 2,
                ),
              ),
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  color:
                      isSelected ? Colors.white : Colors.white.withOpacity(0.5),
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: isSelected ? Fonts.semiBold : Fonts.medium,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTabContent() {
    return Consumer<AigcPresetsViewModel>(
      builder: (context, presetsModel, _) => Stack(
        children: [
          AigcPcPresetsGridView(viewModel: presetsModel),
          if ((presetsModel.selectedTab == 0 &&
                  presetsModel.completedPresets.isEmpty) ||
              (presetsModel.selectedTab == 1 &&
                  presetsModel.runningPresets.isEmpty))
            _buildEmptyContent(presetsModel),
        ],
      ),
    );
  }

  Widget _buildEmptyContent(AigcPresetsViewModel viewModel) {
    return Center(
      child: SizedBox(
        width: 260,
        height: 184,
        child: Column(
          children: [
            Image.asset(
              'assets/icons/aigc_presets_empty_icon.png',
              width: 260,
              height: 130,
            ),
            const SizedBox(height: 10),
            SizedBox(
              width: 260,
              height: 18,
              child: Center(
                child: Text(
                  viewModel.selectedTab == 0 ? '当前暂无已生成的主题' : '当前暂无正在生成的主题',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontFamily: Fonts.defaultFontFamily,
                    fontSize: 14,
                    fontWeight: Fonts.medium,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  /// 显示创建预设弹窗
  void _showCreatePresetDialog(AigcPresetsViewModel viewModel) {
    final maskPath = viewModel.maskPath;
    final previewPath = viewModel.previewPath;

    if (maskPath != null && previewPath != null) {
      AIGCCreatePresetDialog.show(
        context,
        imagePath: previewPath,
        maskPath: maskPath,
        onCreateSuccess: () {
          // 在页面context中处理创建成功回调
          _handleCreateSuccess();
        },
      );
    }
  }

  /// 处理创建预设成功
  void _handleCreateSuccess() {
    if (mounted) {
      final presetsModel = context.read<AigcPresetsViewModel>();

      // 创建成功后刷新账户信息（不等待，避免影响UI流程）
      context.read<AccountRepository>().refreshAllAccount().catchError((e) {
        PGLog.e('账户信息刷新失败: $e');
      });

      // 先刷新数据，再切换tab
      // 这样可以确保新创建的预设被加载后再切换到"正在生成"tab
      presetsModel.refreshPresetsData().then((_) {
        // 数据加载完成后再切换tab，不重复加载数据
        presetsModel.setSelectedTab(1, loadData: false);
      });
    }
  }
}
