import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/ui/aigc_presets/config/aigc_config.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/create_preset_viewmodel.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_create_preset_input_field.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_preset_confirm_button.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/common_state.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 创建 AIGC 预设弹窗
class AIGCCreatePresetDialog extends StatefulWidget {
  /// 预设生成参考图路径
  final String imagePath;

  /// 蒙版图路径
  final String maskPath;

  /// 是否处于加载状态
  final bool isLoading;

  /// 创建完成的回调
  final VoidCallback? onCreateSuccess;

  const AIGCCreatePresetDialog({
    super.key,
    required this.imagePath,
    required this.maskPath,
    required this.isLoading,
    this.onCreateSuccess,
  });

  @override
  State<AIGCCreatePresetDialog> createState() => _AIGCCreatePresetDialogState();

  /// 显示创建主题预设对话框, 需要传入两张图，一张是 [imagePath]，表示原图路径，另外一张是
  /// [maskPath]，表示蒙版图路径
  static void show(
    BuildContext context, {
    required String imagePath,
    required String maskPath,
    VoidCallback? onCreateSuccess,
  }) {
    if (PGDialog.isDialogVisible(DialogTags.createPreset)) {
      PGLog.d('CreatePresetDialog show, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: 648,
      height: 384,
      needBlur: false,
      tag: DialogTags.createPreset,
      child: ChangeNotifierProvider(
        create: (_) => CreatePresetViewModel(
          repository: context.read<AigcPresetsRepository>(),
          uploadRepository: context.read<MediaUploadRepository>(),
          customTableRepository: context.read<OpsCustomTableRepository>(),
        ),
        child: _CreatePresetDialogWrapper(
          imagePath: imagePath,
          maskPath: maskPath,
          onCreateSuccess: onCreateSuccess,
        ),
      ),
      radius: 16,
    );
  }
}

/// 状态监听包装器，处理创建状态变化的副作用
class _CreatePresetDialogWrapper extends StatelessWidget {
  final String imagePath;
  final String maskPath;
  final VoidCallback? onCreateSuccess;

  const _CreatePresetDialogWrapper({
    required this.imagePath,
    required this.maskPath,
    this.onCreateSuccess,
  });

  @override
  Widget build(BuildContext context) {
    return Selector<CreatePresetViewModel,
        ({AsyncResult<void> createStatus, bool isLoading})>(
      selector: (_, viewModel) => (
        createStatus: viewModel.createStatus,
        isLoading: viewModel.isLoading,
      ),
      builder: (context, data, child) {
        // 监听状态变化并处理副作用
        data.createStatus.maybeWhen(
            success: (_) {
              // 成功状态，关闭弹窗并调用回调
              WidgetsBinding.instance.addPostFrameCallback((_) {
                PGDialog.dismiss(tag: DialogTags.createPreset);
                onCreateSuccess?.call();
              });
            },
            error: (message, exception) {
              // 错误状态，记录日志
              PGDialog.showToast(message);
            },
            orElse: () {});

        return AIGCCreatePresetDialog(
          imagePath: imagePath,
          maskPath: maskPath,
          isLoading: data.isLoading,
          onCreateSuccess: onCreateSuccess,
        );
      },
    );
  }
}

class _AIGCCreatePresetDialogState extends State<AIGCCreatePresetDialog> {
  // 图片上传区域的尺寸常量
  static const double _imageAreaSize = 336.0;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // 关闭按钮
          _buildCloseButton(),

          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Row(children: [
              // 左侧图片上传样片区域
              _buildImageUploadArea(),
              const SizedBox(width: 24),
              Expanded(
                child: _buildFromArea(),
              )
            ]),
          ),

          // 透明遮罩层 - 在loading时拦截所有交互
          if (widget.isLoading)
            Positioned.fill(
              child: Container(
                color: Colors.transparent, // 完全透明
              ),
            ),
        ],
      ),
    );
  }

  Column _buildFromArea() {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      // 标题
      Text(
        '主题预设创建',
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          fontFamily: Fonts.defaultFontFamily,
        ),
      ),
      const SizedBox(height: 16),

      Text('名称',
          style: TextStyle(
            color: const Color(0xB3FFFFFF),
            fontSize: 12,
            fontFamily: Fonts.defaultFontFamily,
          )),
      const SizedBox(height: 4),

      Consumer<CreatePresetViewModel>(
        builder: (context, viewModel, child) {
          return AIGCCreatePresetInputField(
            hintText: '主题预设命名',
            width: 240,
            maxLength: 16,
            controller: viewModel.nameController,
          );
        },
      ),
      const SizedBox(height: 16),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('创意补充',
              style: TextStyle(
                color: const Color(0xB3FFFFFF),
                fontSize: 12,
                fontFamily: Fonts.defaultFontFamily,
              )),
          Selector<CreatePresetViewModel, bool>(
              selector: (context, viewModel) => viewModel.isSupplementExpanded,
              builder: (context, isExpanded, child) {
                return GestureDetector(
                  onTap: () {
                    // 切换创意补充 section 的展开状态
                    context
                        .read<CreatePresetViewModel>()
                        .toggleSupplementExpanded();
                  },
                  child: Image.asset(
                    isExpanded
                        ? 'assets/icons/aigc_icon_minus.png'
                        : 'assets/icons/aigc_icon_add.png',
                    width: 24,
                    height: 24,
                    color: const Color(0xFFFFFFFF),
                  ),
                );
              }),
        ],
      ),

      // 根据展开状态显示或隐藏创意补充的详细内容
      Selector<CreatePresetViewModel, bool>(
        selector: (context, viewModel) => viewModel.isSupplementExpanded,
        builder: (context, isExpanded, child) {
          return AnimatedOpacity(
            duration: const Duration(milliseconds: 250),
            opacity: isExpanded ? 1.0 : 0.0,
            child: isExpanded
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 2),
                      Text('AI会根据您的补充，完善创意生成',
                          style: TextStyle(
                            color: const Color(0x80FFFFFF),
                            fontSize: 11,
                            fontFamily: Fonts.defaultFontFamily,
                          )),
                      const SizedBox(height: 4),
                      AIGCCreatePresetInputField(
                        hintText: '输入创意补充词并以逗号隔开',
                        width: 240,
                        controller: context
                            .read<CreatePresetViewModel>()
                            .promptController,
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          );
        },
      ),

      const Spacer(),

      Center(
          child: Text('将生成 ${AigcConfig.defaultGenerateCount} 张创意效果',
              style: TextStyle(
                color: const Color(0xB3FFFFFF),
                fontSize: 12,
                fontFamily: Fonts.defaultFontFamily,
              ))),
      const SizedBox(height: 8),
      // 开始创建按钮
      _buildStartButton(),
    ]);
  }

  Widget _buildStartButton() {
    return Selector<CreatePresetViewModel,
        ({bool isFormValid, int requiredCredits, bool isLoading})>(
      selector: (context, viewModel) => (
        isFormValid: viewModel.isFormValid,
        requiredCredits: viewModel.requiredCredits,
        isLoading: viewModel.isLoading,
      ),
      builder: (context, data, child) {
        // 根据状态确定按钮状态
        AIGCPresetButtonState buttonState;
        if (data.isLoading) {
          buttonState = AIGCPresetButtonState.loading;
        } else if (data.isFormValid) {
          buttonState = AIGCPresetButtonState.enabled;
        } else {
          buttonState = AIGCPresetButtonState.disabled;
        }

        return AIGCPresetConfirmButton(
          state: buttonState,
          text: '开始创建',
          iconPath: 'assets/icons/aigc_credit_star.png',
          suffixText: '${data.requiredCredits}',
          onTap: () {
            // 开始创建预设
            context
                .read<CreatePresetViewModel>()
                .startCreate(widget.imagePath, widget.maskPath);
          },
        );
      },
    );
  }

  Widget _buildImageUploadArea() {
    return Container(
      width: _imageAreaSize,
      height: _imageAreaSize,
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
        color: Colors.transparent,
      ),
      child: _buildSelectedImageView(File(widget.imagePath)),
    );
  }

  Widget _buildSelectedImageView(File selectedImage) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: SizedBox(
        width: _imageAreaSize,
        height: _imageAreaSize,
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: _imageAreaSize,
              maxHeight: _imageAreaSize,
            ),
            child: Image.file(
              selectedImage,
              fit: BoxFit.contain, // 保持宽高比，不裁剪
              errorBuilder: (context, error, stackTrace) {
                // 图片加载失败时的占位符
                return Container(
                  width: _imageAreaSize,
                  height: _imageAreaSize,
                  color: Colors.grey[800],
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image,
                        size: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 8),
                      Text(
                        '图片加载失败',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: 12,
      right: 12,
      child: GestureDetector(
        onTap: () => PGDialog.dismiss(tag: DialogTags.createPreset),
        child: SizedBox(
          width: 24,
          height: 24,
          child: Image.asset('assets/icons/dialog_close.png',
              width: 24, height: 24),
        ),
      ),
    );
  }
}
