import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 错误消息监听器 - 独立组件，避免影响主UI
class ErrorMessageListener extends StatelessWidget {
  const ErrorMessageListener({super.key});

  @override
  Widget build(BuildContext context) {
    return Selector<AIGCPresetDetailViewModel, String?>(
      selector: (_, viewModel) => viewModel.errorMessage,
      builder: (context, errorMessage, child) {
        if (errorMessage != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            PGDialog.showToast(errorMessage);
            context.read<AIGCPresetDetailViewModel>().clearError();
          });
        }
        return const SizedBox.shrink();
      },
    );
  }
}
