import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/ui/aigc_presets/config/aigc_config.dart';
import 'package:turing_art/ui/aigc_presets/providers/aigc_preset_expand_provider.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_regenerate_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_create_preset_input_field.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_preset_confirm_button.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_constants.dart';
import 'package:turing_art/ui/aigc_presets/widget/count_selector_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/common_state.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// 预设详情右侧展开内容组件
class PresetDetailExpandedSideContent extends StatefulWidget {
  const PresetDetailExpandedSideContent({super.key});

  @override
  State<PresetDetailExpandedSideContent> createState() =>
      _PresetDetailExpandedSideContent();
}

class _PresetDetailExpandedSideContent
    extends State<PresetDetailExpandedSideContent> {
  @override
  Widget build(BuildContext context) {
    return Selector<AIGCPresetRegenerateViewModel,
        AsyncResult<AigcPcPresetsDetailResponse>>(
      selector: (_, viewModel) => viewModel.regenerateResult,
      builder: (context, result, child) {
        // 监听状态变化，处理成功和错误情况
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _handleStatusChange(result);
        });

        return _buildAnimatedContent(context, result.isLoading);
      },
    );
  }

  void _handleStatusChange(AsyncResult<AigcPcPresetsDetailResponse> result) {
    // 使用通用状态的模式匹配
    result.maybeWhen(
      success: (data) {
        // 重新生成预设成功后刷新账户信息（不等待，避免影响UI流程）
        context.read<AccountRepository>().refreshAllAccount().catchError((e) {
          debugPrint('账户信息刷新失败: $e');
        });

        // 更新数据
        context.read<AIGCPresetDetailViewModel>().updateWithNewData(data);
        // 开始折叠动画
        _collapseAndReset();
      },
      error: (message, exception) {
        PGDialog.showToast(message);
      },
      orElse: () {
        // 对于 loading 和 idle 状态不需要特殊处理
      },
    );
  }

  Widget _buildAnimatedContent(BuildContext context, bool isLoading) {
    return Selector<AigcPresetExpandProvider, bool>(
      selector: (_, provider) => provider.isRegenerateExpanded,
      builder: (context, isExpanded, child) {
        return AnimatedContainer(
          duration: const Duration(
              milliseconds: PresetDetailConstants.animationDurationMs),
          curve: Curves.easeInOut,
          width: PresetDetailConstants.expandedSideWidth,
          height: isExpanded
              ? PresetDetailConstants.expandedSideExpandedHeight
              : PresetDetailConstants.expandedSideCollapsedHeight,
          decoration: BoxDecoration(
            color: const Color(0xFF1F1F1F),
            borderRadius:
                BorderRadius.circular(PresetDetailConstants.containerRadius),
            boxShadow: const [
              BoxShadow(
                color: Color(0x4D000000),
                blurRadius: 20,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              // 关闭按钮
              _buildCloseButton(),

              // 主要内容
              Container(
                padding: const EdgeInsets.only(left: 16, top: 14, right: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                    const _RegenerateTitleWidget(),
                    const SizedBox(height: 14),

                    // 创意补充标题行
                    const _RegenerateCreativeSectionHeader(),

                    // 创意补充内容区域 - 带动画展开
                    _buildExpandableContentArea(isExpanded),

                    const SizedBox(height: 12),

                    // 生成张数选择器
                    const _RegenerateGenerateCountSelector(),

                    const SizedBox(height: 16),

                    // 确认按钮
                    const _RegenerateConfirmButton(),
                  ],
                ),
              ),

              if (isLoading) ...[
                Positioned.fill(child: Container(color: Colors.transparent)),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildExpandableContentArea(bool isExpanded) {
    return AnimatedContainer(
      duration: const Duration(
          milliseconds: PresetDetailConstants.animationDurationMs),
      curve: Curves.easeInOut,
      height: isExpanded ? 32 : 1, // 展开时显示输入框高度，折叠时只显示分割线
      child: ClipRect(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          child: isExpanded
              ? Container(
                  key: const ValueKey('expanded'),
                  padding: const EdgeInsets.only(top: 0),
                  child: AIGCCreatePresetInputField(
                    hintText: '输入创意补充词并以逗号隔开',
                    width: 248,
                    height: 32,
                    controller: context
                        .read<AIGCPresetRegenerateViewModel>()
                        .supplementController,
                  ),
                )
              : const SizedBox(
                  key: ValueKey('collapsed'),
                  height: 1,
                  child: Divider(
                    color: Color(0x1AFFFFFF),
                    height: 1,
                    thickness: 1,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: 12,
      right: 12,
      child: GestureDetector(
        onTap: () => _collapseAndReset(),
        child: Image.asset(
          'assets/icons/dialog_close.png',
          width: 24,
          height: 24,
        ),
      ),
    );
  }

  void _collapseAndReset() {
    context.read<AIGCPresetRegenerateViewModel>().resetStatus();
    final expandProvider = context.read<AigcPresetExpandProvider>();
    if (expandProvider.isRegenerateExpanded) {
      expandProvider.toggleRegenerateExpandedWithoutAnimation();
    }
    expandProvider.toggleMainExpanded();
  }
}

/// 标题组件
class _RegenerateTitleWidget extends StatelessWidget {
  const _RegenerateTitleWidget();

  @override
  Widget build(BuildContext context) {
    const textColor = Color(0xFFFFFFFF);

    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        '再次生成',
        style: TextStyle(
          fontSize: 14,
          color: textColor,
          fontWeight: FontWeight.w500,
          fontFamily: Fonts.defaultFontFamily,
        ),
      ),
    );
  }
}

/// 创意补充标题行组件
class _RegenerateCreativeSectionHeader extends StatelessWidget {
  const _RegenerateCreativeSectionHeader();

  @override
  Widget build(BuildContext context) {
    const textColor = Color(0xFFFFFFFF);

    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '创意补充',
            style: TextStyle(
              fontSize: 12,
              color: textColor,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          Selector<AigcPresetExpandProvider, bool>(
            selector: (_, provider) => provider.isRegenerateExpanded,
            builder: (context, isExpanded, child) {
              return GestureDetector(
                onTap: () {
                  context
                      .read<AigcPresetExpandProvider>()
                      .toggleRegenerateExpanded();
                  if (!isExpanded) {
                    context.read<AIGCPresetRegenerateViewModel>().resetStatus();
                  }
                },
                child: Image.asset(
                  isExpanded
                      ? 'assets/icons/aigc_icon_minus.png'
                      : 'assets/icons/aigc_icon_add.png',
                  width: 24,
                  height: 24,
                  color: const Color(0xFFFFFFFF),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// 生成张数选择器组件
class _RegenerateGenerateCountSelector extends StatelessWidget {
  const _RegenerateGenerateCountSelector();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 32,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '生成张数',
            style: TextStyle(
              fontSize: 12,
              color: const Color(0xFFFFFFFF),
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          Selector<AIGCPresetRegenerateViewModel, int>(
            selector: (_, viewModel) => viewModel.regenerateCount,
            builder: (context, count, child) {
              final viewModel = context.read<AIGCPresetRegenerateViewModel>();

              return CountSelectorWidget(
                count: count,
                onDecrease: () => viewModel.decreaseRegenerateCount(),
                onIncrease: () => viewModel.increaseRegenerateCount(),
                minValue: AigcConfig.minGenerateCount,
                maxValue: AigcConfig.maxGenerateCount,
              );
            },
          ),
        ],
      ),
    );
  }
}

/// 确认按钮组件
class _RegenerateConfirmButton extends StatelessWidget {
  const _RegenerateConfirmButton();

  @override
  Widget build(BuildContext context) {
    return Selector<AIGCPresetRegenerateViewModel,
        ({bool isLoading, int credit})>(
      selector: (_, viewModel) => (
        isLoading: viewModel.isLoading,
        credit: (viewModel.regenerateCost),
      ),
      builder: (context, data, child) {
        final status = data.isLoading
            ? AIGCPresetButtonState.loading
            : AIGCPresetButtonState.enabled;
        return AIGCPresetConfirmButton(
          state: status,
          text: '提交',
          iconPath: 'assets/icons/aigc_credit_star.png',
          suffixText: '${data.credit}',
          onTap: () {
            context.read<AIGCPresetRegenerateViewModel>().startRegenerate();
          },
        );
      },
    );
  }
}
