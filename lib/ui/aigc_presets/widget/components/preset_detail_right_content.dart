import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_bottom_actions.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_list_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 右侧内容组件 - 独立管理状态
class PresetDetailRightContent extends StatelessWidget {
  final VoidCallback onExit;

  const PresetDetailRightContent({
    super.key,
    required this.onExit,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 关闭按钮
        _buildCloseButton(),

        Column(
          children: [
            _buildTopHintSection(),
            _buildDivider(),

            // 创意图片网格
            Expanded(
              child: PresetDetailListWidget(
                onExit: onExit,
              ),
            ),

            _buildDivider(),
            // 底部操作区域
            PresetDetailBottomActions(onExit: onExit),
          ],
        )
      ],
    );
  }

  Widget _buildTopHintSection() {
    return Container(
        height: 48,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        alignment: Alignment.centerLeft,
        child: Text(
          '"已勾选"表示在「主题列表」中显示该创意效果。',
          style: TextStyle(
            color: const Color(0xB3FFFFFF),
            fontSize: 12,
            fontFamily: Fonts.defaultFontFamily,
          ),
        ));
  }

  Widget _buildDivider() {
    return Container(height: 1, color: const Color(0x1AFFFFFF));
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: 12,
      right: 12,
      child: GestureDetector(
        onTap: () => onExit.call(),
        child: SizedBox(
          width: 24,
          height: 24,
          child: Image.asset('assets/icons/dialog_close.png',
              width: 24, height: 24),
        ),
      ),
    );
  }
}
