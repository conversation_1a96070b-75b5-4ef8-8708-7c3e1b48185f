class PresetDetailConstants {
  /// 预设详情列表显示的图片组件大小
  static const double imageContainerSize = 137.0;

  /// 预设详情弹窗左侧显示的封面图片大小
  static const double coverImageSize = 160.0;

  // ========== 弹窗尺寸常量 ==========
  /// 弹窗实际宽度
  static const double dialogWidth = 938.0;

  /// 弹窗实际高度
  static const double dialogHeight = 611.0;

  /// 主内容区域收起状态宽度
  static const double collapsedWidth = 654.0;

  // ========== 右侧展开区域尺寸常量 ==========
  /// 展开侧边区域宽度
  static const double expandedSideWidth = 280.0;

  /// 展开侧边区域收起高度
  static const double expandedSideCollapsedHeight = 204.0;

  /// 展开侧边区域展开高度
  static const double expandedSideExpandedHeight = 236.0;

  // ========== 动画和间距常量 ==========
  /// 动画持续时间（毫秒）
  static const int animationDurationMs = 250;

  /// 右侧区域与主内容的间距
  static const double sideContentSpacing = 4.0;

  /// 容器圆角半径
  static const double containerRadius = 16.0;
}
