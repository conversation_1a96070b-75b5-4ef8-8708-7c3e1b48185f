import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_operation_buttons.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

/// 左侧内容组件 - 独立管理状态
class PresetDetailLeftContent extends StatelessWidget {
  final double coverImageSize;
  final VoidCallback? onExit;

  const PresetDetailLeftContent({
    super.key,
    required this.coverImageSize,
    this.onExit,
  });

  @override
  Widget build(BuildContext context) {
    return Selector<
        AIGCPresetDetailViewModel,
        ({
          AIGCPresetDetailUI? presetDetail,
          String firstItemImageUrl,
          int totalCount,
          int selectedCount,
        })>(
      selector: (_, viewModel) => (
        presetDetail: viewModel.presetDetail,
        firstItemImageUrl: viewModel.effects.firstOrNull?.thumbUrl ?? '',
        totalCount: viewModel.totalCount,
        selectedCount: viewModel.selectedCount,
      ),
      builder: (context, data, child) {
        if (data.presetDetail == null) {
          return const SizedBox();
        }

        return Column(
          children: [
            _buildCoverImage(data.presetDetail!, data.firstItemImageUrl),

            // 显示预设信息
            const SizedBox(height: 8),
            _buildLeftText(
                data.presetDetail!.name,
                TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium,
                )),
            const SizedBox(height: 2),

            // 显示预设创建信息
            _buildLeftText(
                data.presetDetail!.createTime,
                TextStyle(
                  color: const Color(0xB3FFFFFF),
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                )),

            const SizedBox(height: 8),
            _buildInfoRow('创意生成数量', '${data.totalCount}'),
            _buildInfoRow('选中创意', '${data.selectedCount}'),
            const Spacer(),
            IconTextButton(
              image: 'assets/icons/aigc_delete.png',
              text: '删除主题',
              textColor: const Color(0xB3FFFFFF),
              borderWidth: 1,
              middleSpace: 2,
              tailSpace: 6,
              onTap: () async {
                AigcPcDeleteConfirmDialog.show(
                  context,
                  title: '确定删除此预设吗？',
                  content: '删除后将无法找回。',
                  onConfirm: () async {
                    try {
                      await context
                          .read<AIGCPresetDetailViewModel>()
                          .deletePreset();
                    } finally {
                      PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
                      PGDialog.dismiss(tag: DialogTags.aigcPresetDetail);
                      onExit?.call();
                    }
                  },
                );
              },
            ),
            const SizedBox(height: 8),
          ],
        );
      },
    );
  }

  Widget _buildLeftText(String text, TextStyle textStyle) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(text, style: textStyle),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return SizedBox(
      height: 28,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: const Color(0x80FFFFFF),
              fontSize: 12,
              fontFamily: Fonts.defaultFontFamily,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: const Color(0xB3FFFFFF),
              fontSize: 12,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.regular,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoverImage(AIGCPresetDetailUI data, String coverUrl) {
    if (data.status == AigcRequestConst.failed) {
      return Container(
          width: coverImageSize,
          height: coverImageSize,
          decoration: BoxDecoration(
            color: const Color(0xFF262626),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Image.asset('assets/icons/icon_exclamation_mark.png',
              width: 32, height: 32));
    } else {
      return CachedImageWidget(
          imageUrl: coverUrl,
          width: coverImageSize,
          height: coverImageSize,
          radius: 6.4,
          fit: BoxFit.cover);
    }
  }
}
