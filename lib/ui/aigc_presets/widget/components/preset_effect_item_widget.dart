import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/constants/constants.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/ui/aigc_presets/widgets/preset_preview_scope.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

/// 单个创意项目组件
class PresetEffectItemWidget extends StatefulWidget {
  final String groupId;
  final AIGCPresetEffectUI effectUI;
  final double creativeItemSize;

  const PresetEffectItemWidget({
    super.key,
    required this.groupId,
    required this.effectUI,
    required this.creativeItemSize,
  });

  @override
  State<PresetEffectItemWidget> createState() => _PresetEffectItemWidgetState();
}

class _PresetEffectItemWidgetState extends State<PresetEffectItemWidget> {
  late final GlobalKey _itemKey;
  late final ValueNotifier<bool> _isHoveredNotifier;

  @override
  void initState() {
    super.initState();
    _itemKey = GlobalKey();
    _isHoveredNotifier = ValueNotifier<bool>(false);
  }

  @override
  void dispose() {
    _isHoveredNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildEffectItem(context, widget.effectUI);
  }

  Widget _buildEffectItem(BuildContext context, AIGCPresetEffectUI effectUI) {
    final hasImageUrl =
        effectUI.photoUrl != null && effectUI.photoUrl!.isNotEmpty;

    return PlatformMouseRegion(
      onEnter: (_) {
        _isHoveredNotifier.value = true;
        if (hasImageUrl) {
          final previewController = PresetPreviewScope.of(context);
          previewController?.showPreview(context, effectUI.photoUrl!, _itemKey);
        }
      },
      onExit: (_) {
        _isHoveredNotifier.value = false;
        final previewController = PresetPreviewScope.of(context);
        previewController?.removePreview();
      },
      child: GestureDetector(
        onTap: () {
          if (effectUI.status != AigcRequestConst.completed) {
            // 仅生成成功的效果可被选中
            return;
          }
          context
              .read<AIGCPresetDetailViewModel>()
              .toggleCreativeItemSelection(widget.groupId, effectUI.effectCode);
        },
        child: Container(
          key: _itemKey,
          child: Stack(
            children: [
              // 图片容器
              _buildImageContainer(effectUI, hasImageUrl),
              // 选中按钮
              if (hasImageUrl) _buildSelectionButton(context, effectUI),
              // 删除按钮
              ValueListenableBuilder<bool>(
                valueListenable: _isHoveredNotifier,
                builder: (context, isHovered, child) {
                  return isHovered && hasImageUrl
                      ? _buildDeleteButton(context)
                      : const SizedBox.shrink();
                },
              ),

              // 测试包中：直接显示错误信息
              if (isDebug &&
                  effectUI.errorMsg != null &&
                  effectUI.errorMsg!.isNotEmpty)
                _buildErrorMsgTIp(effectUI),
            ],
          ),
        ),
      ),
    );
  }

  /// 将错误信息展示出来
  Positioned _buildErrorMsgTIp(AIGCPresetEffectUI effectUI) {
    return Positioned.fill(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          effectUI.errorMsg!,
          style: const TextStyle(
            color: Colors.red,
            fontSize: 10,
          ),
          textAlign: TextAlign.center,
          maxLines: 10,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _getImageWidget(
      AIGCPresetEffectUI effectUI, bool hasImageUrl, bool selected) {
    if (effectUI.status == AigcRequestConst.failed) {
      return _buildFailedPlaceholder();
    } else if (hasImageUrl) {
      return _buildImageWidget(effectUI, selected);
    } else {
      return _buildAnimationPlaceholder();
    }
  }

  /// 构建效果生成失败的占位图
  Widget _buildFailedPlaceholder() {
    return Container(
        width: widget.creativeItemSize,
        height: widget.creativeItemSize,
        decoration: BoxDecoration(
          color: const Color(0xFF262626),
          borderRadius: BorderRadius.circular(8),
        ),
        child:
            Image.asset('assets/icons/icon_exclamation_mark.png', scale: 1.2));
  }

  /// 构建图片容器
  Widget _buildImageContainer(AIGCPresetEffectUI effectUI, bool hasImageUrl) {
    return SizedBox(
      width: widget.creativeItemSize,
      height: widget.creativeItemSize,
      child: Stack(
        children: [
          _getImageWidget(effectUI, hasImageUrl, effectUI.isSelected),

          // 边框层 - 只在选中时显示
          if (effectUI.isSelected) ...[
            // 外层红色边框
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFF72561), width: 2),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAnimationPlaceholder() {
    return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AigcItemAnimation(
          child: Image.asset(
            'assets/icons/aigc_presets_loading_icon.png',
            width: widget.creativeItemSize,
            height: widget.creativeItemSize,
            fit: BoxFit.cover,
          ),
        ));
  }

  Widget _buildImageWidget(AIGCPresetEffectUI effectUI, bool selected) {
    final margin = selected ? const EdgeInsets.all(3) : EdgeInsets.zero;
    final radius = selected ? 5.0 : 8.0;
    return Container(
        margin: margin,
        child: CachedImageWidget(
          imageUrl: effectUI.thumbUrl ?? '',
          width: widget.creativeItemSize,
          height: widget.creativeItemSize,
          radius: radius,
          fit: BoxFit.cover,
        ));
  }

  /// 构建选中按钮
  Widget _buildSelectionButton(
      BuildContext context, AIGCPresetEffectUI effectUI) {
    final isSelected = effectUI.isSelected;

    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF72561) : const Color(0x99000000),
          border: isSelected
              ? null
              : Border.all(
                  color: const Color(0xCCFFFFFF),
                  width: 1.5,
                ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: effectUI.isSelected
            ? Image.asset('assets/icons/aigc_option_checked.png',
                width: 20, height: 20, color: Colors.white)
            : null,
      ),
    );
  }

  /// 构建删除按钮
  Widget _buildDeleteButton(BuildContext context) {
    return Positioned(
      bottom: 8,
      right: 8,
      child: GestureDetector(
        onTap: () {
          // 先移除预览overlay
          final previewController = PresetPreviewScope.of(context);
          previewController?.forceRemovePreview();

          // 弹出确认删除弹窗
          _showDeleteConfirmDialog(context);
        },
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: const Color(0xCC1F1F1F),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: const Color(0x1AFFFFFF),
              width: 0.5,
            ),
          ),
          child: Image.asset(
            'assets/icons/aigc_delete.png',
            width: 20,
            height: 20,
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmDialog(BuildContext context) {
    // 弹出确认删除弹窗
    AigcPcDeleteConfirmDialog.show(
      context,
      title: '确定删除该创意效果？',
      content: '删除后将无法找回。',
      onConfirm: () async {
        PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
        // 执行删除操作
        await context
            .read<AIGCPresetDetailViewModel>()
            .deleteEffect(widget.groupId, widget.effectUI.effectCode);
      },
    );
  }
}
