import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_group_item_widget.dart';

/// 创意网格组件
class PresetDetailListWidget extends StatefulWidget {
  final VoidCallback onExit;

  const PresetDetailListWidget({
    super.key,
    required this.onExit,
  });

  @override
  State<PresetDetailListWidget> createState() => _PresetDetailListWidgetState();
}

class _PresetDetailListWidgetState extends State<PresetDetailListWidget> {
  late final ScrollController _scrollController;

  // Stream方式监听滚动指令
  StreamSubscription<bool>? _scrollSubscription;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    // Stream方式 - 监听滚动指令流
    _subscribeToScrollStream();
  }

  /// 订阅滚动指令流
  void _subscribeToScrollStream() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final viewModel = context.read<AIGCPresetDetailViewModel>();
        _scrollSubscription = viewModel.scrollToBottomStream.listen((_) {
          if (!mounted) {
            return;
          }
          _scrollToBottom();
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _scrollSubscription?.cancel(); // 取消Stream订阅
    super.dispose();
  }

  /// 滚动到列表底部
  void _scrollToBottom() {
    if (!_scrollController.hasClients) {
      return;
    }

    final currentMaxExtent = _scrollController.position.maxScrollExtent;

    _scrollController.animateTo(
      currentMaxExtent,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    //   .then((_) {
    // // 滑动完成
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (_scrollController.hasClients) {
    //     final newMaxExtent = _scrollController.position.maxScrollExtent;
    //     // 如果发现还有更多内容，再次滚动
    //     if (newMaxExtent > currentMaxExtent) {
    //       _scrollController.animateTo(
    //         newMaxExtent,
    //         duration: const Duration(milliseconds: 200),
    //         curve: Curves.easeOut,
    //       );
    //     }
    //   }
    // });
    // );
  }

  @override
  Widget build(BuildContext context) {
    return Selector<AIGCPresetDetailViewModel, List<AIGCPresetGroupUI>>(
      selector: (_, viewModel) => viewModel.groups,
      builder: (context, presetGroups, child) {
        if (presetGroups.isEmpty) {
          widget.onExit.call();
        }
        return _buildScrollableList(presetGroups);
      },
    );
  }

  Widget _buildScrollableList(List<AIGCPresetGroupUI> presetGroups) {
    return ScrollbarTheme(
      data: _buildScrollbarTheme(),
      child: Scrollbar(
        controller: _scrollController,
        thumbVisibility: false, // 不始终显示，只在需要时显示
        trackVisibility: false,
        child: ListView.separated(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemBuilder: (context, index) {
            return PresetDetailGroupItemWidget(
              groupUI: presetGroups[index],
            );
          },
          separatorBuilder: (context, index) => const SizedBox(height: 16),
          itemCount: presetGroups.length,
        ),
      ),
    );
  }

  ScrollbarThemeData _buildScrollbarTheme() {
    return ScrollbarThemeData(
      // 滚动条拖拽区域的颜色
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return const Color(0x40FFFFFF);
        }
        if (states.contains(MaterialState.dragged)) {
          return const Color(0x4DFFFFFF);
        }
        return const Color(0x33FFFFFF);
      }),
      // 滚动轨道的颜色
      trackColor: MaterialStateProperty.all(const Color(0x0DFFFFFF)),
      // 滚动条边框
      trackBorderColor: MaterialStateProperty.all(Colors.transparent),
      // 滚动条厚度
      thickness: MaterialStateProperty.all(4),
      // 滚动条圆角
      radius: const Radius.circular(2),
      crossAxisMargin: 6,
      // 滚动条长度
      minThumbLength: 135,
    );
  }
}
