import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 用于创建预设的输入框组件
class AIGCCreatePresetInputField extends StatelessWidget {
  final String hintText;
  final double? width;
  final double? height;
  final int? maxLength; // 最大输入长度
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;

  const AIGCCreatePresetInputField({
    super.key,
    required this.hintText,
    this.width = 100,
    this.height = 32,
    this.maxLength,
    this.controller,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final hintTextStyle = TextStyle(
      color: const Color(0x80FFFFFF),
      fontSize: 12,
      fontFamily: Fonts.defaultFontFamily,
      // height: 1.0, // 重要：控制行高
    );
    final textStyle = TextStyle(
      color: Colors.white,
      fontSize: 12,
      fontFamily: Fonts.defaultFontFamily,
      // height: 1.0, // 重要：控制行高
    );

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: const Color(0xFF383838),
        borderRadius: BorderRadius.circular(6),
      ),
      child: TextField(
        maxLength: maxLength,
        maxLengthEnforcement: maxLength != null
            // 允许输入未完成时临时超出限制
            ? MaxLengthEnforcement.truncateAfterCompositionEnds
            : null,
        controller: controller,
        onChanged: onChanged,
        cursorColor: Colors.white,
        style: textStyle,
        // 修复光标位置问题：点击时确保光标在末尾
        onTap: () {
          if (controller != null && controller!.text.isNotEmpty) {
            controller!.selection = TextSelection.collapsed(
              offset: controller!.text.length,
            );
          }
        },
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintTextStyle,
          counterText: '',
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 0,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(
              color: Color(0x80FFFFFF),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }
}
