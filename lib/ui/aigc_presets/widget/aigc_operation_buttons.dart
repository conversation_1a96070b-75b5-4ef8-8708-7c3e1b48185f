import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

/// 左侧带图标的文本按钮组件
class IconTextButton extends StatefulWidget {
  final String text;
  final String image;
  final Color textColor;
  final Color? backgroundColor;
  final Color? hoverBackgroundColor;
  final double borderWidth;
  final double middleSpace;
  final double tailSpace;
  final VoidCallback? onTap;

  const IconTextButton({
    super.key,
    required this.text,
    required this.image,
    this.textColor = const Color(0xFFFFFFFF),
    this.backgroundColor,
    this.hoverBackgroundColor,
    this.middleSpace = 0,
    this.tailSpace = 0,
    this.borderWidth = 0,
    this.onTap,
  });

  @override
  State<StatefulWidget> createState() => _IconTextButtonState();
}

class _IconTextButtonState extends State<IconTextButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final normalBackgroundColor = widget.backgroundColor ?? Colors.transparent;

    final hoverBackgroundColor = widget.hoverBackgroundColor ??
        _calculateHoverColor(normalBackgroundColor);

    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: _isHovered ? hoverBackgroundColor : normalBackgroundColor,
            borderRadius: BorderRadius.circular(6),
            // 根据borderWidth设置边框
            border: widget.borderWidth <= 0
                ? null
                : Border.all(
                    color: const Color(0xFF404040),
                    width: widget.borderWidth,
                  ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                widget.image,
                width: 24,
                height: 24,
                color: widget.textColor,
              ),
              SizedBox(width: widget.middleSpace),
              Text(
                widget.text,
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.medium,
                ),
              ),
              SizedBox(width: widget.tailSpace),
            ],
          ),
        ),
      ),
    );
  }

  Color _calculateHoverColor(Color backgroundColor) {
    // 如果背景色是透明的，hover 时显示半透明的白色
    if (backgroundColor == Colors.transparent) {
      return Colors.white.withOpacity(0.1);
    }
    // 否则增加亮度或降低透明度
    final HSLColor hslColor = HSLColor.fromColor(backgroundColor);
    if (hslColor.alpha < 1.0) {
      return backgroundColor
          .withOpacity((hslColor.alpha + 0.2).clamp(0.0, 1.0));
    } else {
      return hslColor
          .withLightness((hslColor.lightness + 0.15).clamp(0.0, 1.0))
          .toColor();
    }
  }
}

/// 自定义按钮组件，支持自定义宽度、背景色和内容
class CustomButton extends StatefulWidget {
  final double width;
  final double? height;
  final Color backgroundColor;
  final Color? hoverBackgroundColor;
  final Color? disabledBackgroundColor;
  final bool enabled;
  final Widget? child;
  final VoidCallback? onTap;

  const CustomButton({
    super.key,
    this.width = 120,
    this.height,
    required this.backgroundColor,
    this.hoverBackgroundColor,
    this.disabledBackgroundColor,
    this.child,
    this.onTap,
    this.enabled = true,
  });

  @override
  State<StatefulWidget> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    // 计算当前应该使用的背景色
    Color currentBackgroundColor;

    if (!widget.enabled) {
      // 禁用状态：使用自定义禁用色或默认禁用色
      currentBackgroundColor = widget.disabledBackgroundColor ??
          _calculateDisabledColor(widget.backgroundColor);
    } else if (_isHovered) {
      // 启用且悬停状态：使用悬停色
      final defaultHoverColor = widget.hoverBackgroundColor ??
          _calculateHoverColor(widget.backgroundColor);
      currentBackgroundColor = defaultHoverColor;
    } else {
      // 启用且正常状态：使用正常背景色
      currentBackgroundColor = widget.backgroundColor;
    }

    return PlatformMouseRegion(
      onEnter: widget.enabled ? (_) => setState(() => _isHovered = true) : null,
      onExit: widget.enabled ? (_) => setState(() => _isHovered = false) : null,
      child: GestureDetector(
        onTap: widget.enabled ? widget.onTap : null,
        child: Container(
          alignment: Alignment.center,
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: currentBackgroundColor,
            borderRadius: BorderRadius.circular(6),
          ),
          child: widget.child ?? const SizedBox.shrink(),
        ),
      ),
    );
  }

  /// 计算悬停状态的背景色
  Color _calculateHoverColor(Color backgroundColor) {
    final HSLColor hslColor = HSLColor.fromColor(backgroundColor);
    if (hslColor.alpha < 1.0) {
      // 如果原色有透明度，hover 时降低透明度（更不透明）
      return backgroundColor
          .withOpacity((hslColor.alpha + 0.2).clamp(0.0, 1.0));
    } else {
      // 如果原色不透明，增加 15% 的亮度
      return hslColor
          .withLightness((hslColor.lightness + 0.15).clamp(0.0, 1.0))
          .toColor();
    }
  }

  /// 计算禁用状态的背景色
  Color _calculateDisabledColor(Color backgroundColor) {
    final HSLColor hslColor = HSLColor.fromColor(backgroundColor);
    // 禁用状态：降低饱和度和亮度，增加透明度
    return hslColor
        .withSaturation((hslColor.saturation * 0.3).clamp(0.0, 1.0))
        .withLightness((hslColor.lightness * 0.6).clamp(0.0, 1.0))
        .toColor()
        .withOpacity(0.5);
  }
}
