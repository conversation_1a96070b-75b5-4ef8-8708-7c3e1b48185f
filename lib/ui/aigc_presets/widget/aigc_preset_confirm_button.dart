import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

/// 按钮样式常量
class _ButtonConstants {
  // 默认尺寸常量
  static const double defaultWidth = 248.0;
  static const double defaultHeight = 40.0;
  static const double borderRadius = 8.0;
  static const double borderWidth = 1.0;

  // 图标尺寸
  static const double loadingIconSize = 24.0;
  static const double suffixIconSize = 20.0;

  // 间距
  static const double iconSpacing = 4.0;

  // 动画
  static const Duration rotationDuration = Duration(seconds: 1);

  // 颜色常量
  static const Color primaryColor = Color(0xFFF72561);
  static const Color disabledTextColor = Color(0x59FFFFFF); // 35%透明度白色
  static const Color loadingBackgroundColor = Color(0xFF333333);
  static const Color disabledBackgroundColor = Color(0xFF404040);
  static const Color borderStartColor = Color(0x33FFFFFF);
  static const Color borderEndColor = Color(0x00FFFFFF);

  // Hover 状态颜色
  static const Color hoverStartColor = Color(0xCCF72561); // 80%透明度
  static const Color normalStartColor = Color(0x4DF72561); // 30%透明度
  static const Color hoverShadowColor = Color(0x4DF72561);
  static const Color normalShadowColor = Color(0x1AF72561);
}

/// 按钮状态枚举
enum AIGCPresetButtonState {
  /// 启用状态 - 可以点击和交互
  enabled,

  /// 禁用状态 - 不可点击，显示为灰色
  disabled,

  /// 加载状态 - 显示loading动画，不可点击
  loading,
}

/// 按钮样式配置类
class _ButtonStyleConfig {
  final Gradient? gradient;
  final Color? backgroundColor;
  final BoxBorder? border;
  final List<BoxShadow>? boxShadow;

  const _ButtonStyleConfig({
    this.gradient,
    this.backgroundColor,
    this.border,
    this.boxShadow,
  });
}

/// 封装了 AIGC 提交按钮的 widget，包括三种状态：
/// - loading 状态，中间显示转圈
/// - disable 状态，灰色背景
/// - enable 状态，显示红色渐变，支持 hover
class AIGCPresetConfirmButton extends StatefulWidget {
  final AIGCPresetButtonState state;
  final String text;
  final String? iconPath;
  final String? suffixText;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  const AIGCPresetConfirmButton({
    super.key,
    required this.text,
    this.state = AIGCPresetButtonState.enabled,
    this.iconPath,
    this.suffixText,
    this.width,
    this.height,
    this.onTap,
  });

  @override
  State<AIGCPresetConfirmButton> createState() =>
      _AIGCPresetConfirmButtonState();
}

class _AIGCPresetConfirmButtonState extends State<AIGCPresetConfirmButton>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _rotationController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: _ButtonConstants.rotationDuration,
      vsync: this,
    );
  }

  @override
  void didUpdateWidget(AIGCPresetConfirmButton oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 根据状态控制旋转动画
    if (widget.state == AIGCPresetButtonState.loading) {
      _rotationController.repeat();
    } else {
      _rotationController.stop();
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 根据状态确定是否可以交互
    final isInteractive = widget.state == AIGCPresetButtonState.enabled;
    final isLoading = widget.state == AIGCPresetButtonState.loading;

    // 根据状态确定文字以及 icon 颜色
    final labelColor =
        isInteractive ? Colors.white : _ButtonConstants.disabledTextColor;

    // 获取按钮样式配置
    final buttonStyle = _getButtonStyle();

    return PlatformMouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: isInteractive ? widget.onTap : null,
        child: Container(
          width: widget.width ?? _ButtonConstants.defaultWidth,
          height: widget.height ?? _ButtonConstants.defaultHeight,
          decoration: BoxDecoration(
            gradient: buttonStyle.gradient,
            color: buttonStyle.backgroundColor,
            borderRadius: BorderRadius.circular(_ButtonConstants.borderRadius),
            border: buttonStyle.border,
            boxShadow: buttonStyle.boxShadow,
          ),
          child: Center(
            child: isLoading
                ? _buildLoadingContent()
                : _buildTextContent(labelColor),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return SizedBox(
      width: _ButtonConstants.loadingIconSize,
      height: _ButtonConstants.loadingIconSize,
      child: AnimatedBuilder(
        animation: _rotationController,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationController.value * 2.0 * math.pi,
            child: Image.asset(
              'assets/icons/icon_loading.png',
              width: _ButtonConstants.loadingIconSize,
              height: _ButtonConstants.loadingIconSize,
              color: Colors.white,
            ),
          );
        },
      ),
    );
  }

  Row _buildTextContent(Color labelColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          widget.text,
          style: TextStyle(
            color: labelColor,
            fontSize: 14,
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (widget.iconPath != null) ...[
          const SizedBox(width: _ButtonConstants.iconSpacing),
          Image.asset(
            widget.iconPath!,
            width: _ButtonConstants.suffixIconSize,
            height: _ButtonConstants.suffixIconSize,
            color: labelColor,
            colorBlendMode: BlendMode.modulate,
          ),
        ],
        if (widget.suffixText != null) ...[
          Text(
            widget.suffixText!,
            style: TextStyle(
              color: labelColor,
              fontSize: 14,
            ),
          ),
        ],
      ],
    );
  }

  _ButtonStyleConfig _getButtonStyle() {
    switch (widget.state) {
      case AIGCPresetButtonState.loading:
        return const _ButtonStyleConfig(
          backgroundColor: _ButtonConstants.loadingBackgroundColor,
        );

      case AIGCPresetButtonState.enabled:
        final startColor = _isHovered
            ? _ButtonConstants.hoverStartColor
            : _ButtonConstants.normalStartColor;
        final shadowColor = _isHovered
            ? _ButtonConstants.hoverShadowColor
            : _ButtonConstants.normalShadowColor;

        return _ButtonStyleConfig(
          gradient: LinearGradient(
            colors: [startColor, _ButtonConstants.primaryColor],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          border: const GradientBorder(
            gradient: LinearGradient(
              colors: [
                _ButtonConstants.borderStartColor,
                _ButtonConstants.borderEndColor,
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            width: _ButtonConstants.borderWidth,
          ),
          boxShadow: [
            BoxShadow(
              color: shadowColor,
              blurRadius: 20,
              offset: const Offset(0, 5),
            ),
          ],
        );

      case AIGCPresetButtonState.disabled:
        return const _ButtonStyleConfig(
          backgroundColor: _ButtonConstants.disabledBackgroundColor,
        );
    }
  }
}

// 自定义渐变边框
class GradientBorder extends Border {
  final Gradient gradient;
  final double width;

  const GradientBorder({
    required this.gradient,
    required this.width,
  }) : super();

  @override
  void paint(
    Canvas canvas,
    Rect rect, {
    TextDirection? textDirection,
    BoxShape shape = BoxShape.rectangle,
    BorderRadius? borderRadius,
  }) {
    final RRect rrect = borderRadius != null
        ? borderRadius.toRRect(rect)
        : RRect.fromRectAndRadius(rect, const Radius.circular(8));

    final Paint paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = width
      ..shader = gradient.createShader(rect);

    canvas.drawRRect(rrect, paint);
  }

  @override
  BorderSide get top => BorderSide.none;

  @override
  BorderSide get bottom => BorderSide.none;

  @override
  BorderSide get left => BorderSide.none;

  @override
  BorderSide get right => BorderSide.none;
}
