import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/coupon/coupon.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/coupon_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

typedef OnCouponRedeemSuccess = void Function(Coupon coupon);

class CouponDialogViewModel extends ChangeNotifier {
  final CouponRepository _couponRepository;
  final AccountRepository _accountRepository;
  // 兑换码输入控制器
  final TextEditingController codeController = TextEditingController();

  // 加载状态
  bool isLoading = false;

  // 错误信息
  String? errorMessage;

  // 兑换成功回调
  OnCouponRedeemSuccess? onCouponRedeemSuccess;

  CouponDialogViewModel(this._couponRepository, this._accountRepository) {
    // 添加文本输入监听器，当输入变化时清除错误信息
    codeController.addListener(_clearLastRequestInfo);
  }

  // 输入变化时的回调函数，清空上次请求错误信息
  void _clearLastRequestInfo() {
    if (errorMessage != null) {
      errorMessage = null;
      notifyListeners();
    }
  }

  // 检查输入框是否有内容，用于控制按钮状态
  bool get hasInput => codeController.text.isNotEmpty;

  // 应用兑换码
  Future<void> applyCoupon() async {
    // 清空上次请求错误信息
    _clearLastRequestInfo();

    final code = codeController.text.trim();
    if (code.isEmpty) {
      errorMessage = '请输入兑换码';
      notifyListeners();
      return;
    }

    try {
      isLoading = true;
      errorMessage = null;
      notifyListeners();

      final result = await _couponRepository.applyCoupon(code);

      if (result != null) {
        // 兑换成功刷新账户信息
        _accountRepository.refreshAllAccount();
        PGLog.d('兑换成功并刷新账户: $result');

        // 通过回调通知兑换成功
        if (onCouponRedeemSuccess != null) {
          onCouponRedeemSuccess!(result);
        }
      } else {
        errorMessage = '兑换失败，请检查兑换码是否正确';
        notifyListeners();
      }
    } catch (e) {
      PGLog.e('兑换码应用异常: $e');
      // 处理 DioException 类型的异常
      if (e is DioException) {
        final response = e.response;
        if (response != null && response.data is Map) {
          final code = response.data['code'];
          // 根据错误代码设置相应的错误信息
          switch (code) {
            case 404:
              errorMessage = '无效的兑换码';
              break;
            case 410:
              errorMessage = '兑换码已过期';
              break;
            case 411:
              errorMessage = '兑换码已使用';
              break;
            default:
              errorMessage = response.data['message'] ?? '兑换失败，请稍后再试';
              break;
          }
        } else {
          errorMessage = '网络请求失败，请检查网络连接';
        }
      } else {
        // 处理其他类型的异常
        errorMessage = e.toString();
      }
      notifyListeners();
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // 移除监听器以避免内存泄漏
    codeController.removeListener(_clearLastRequestInfo);
    codeController.dispose();
    super.dispose();
  }
}
