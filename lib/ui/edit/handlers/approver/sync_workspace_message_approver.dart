import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';

import 'unity_message_approver.dart';

class SyncWorkspaceMessageApprover extends UnityMessageApprover {
  @override
  String get method => 'OnSyncWorkspace';

  // final ProjectRepository _projectRepository;
  // SyncWorkspaceMessageApprover(this._projectRepository);

  @override
  Future<void> handle(MessageFromUnity message) async {
    // try {
    //   final args = message.args ?? [];
    //   final nowTime = DateTime.now().millisecondsSinceEpoch;
    //   PGLog.d('OnSyncWorkspace: $nowTime $args');

    //   if (args.isEmpty) {
    //     return;
    //   }

    //   // 遍历每个参数，每个参数都是一个JSON数组字符串
    //   for (var arg in args) {
    //     // 解析JSON数组字符串
    //     final jsonArray = jsonDecode(arg) as List;
    //     if (jsonArray.isEmpty) {
    //       continue;
    //     }

    //     // 遍历并解析每个工作区数据
    //     for (var workspaceJson in jsonArray) {
    //       final workspace = Workspace.fromJson(workspaceJson);
    //       // 根据workspaceId（即projectId）获取项目信息
    //       final project =
    //           await _projectRepository.getProjectById(workspace.workspaceId);
    //       if (project != null) {
    //         // 更新项目中的工作区数据
    //         ProjectInfo temp = project.copyWith(
    //           workspace: workspace,
    //           // unity返回的时间和实际时间少几分钟，暂时他们没有时间查找，就保持刚刚进编辑的更新后的时间就行
    //           // updateDate: RetouchTime.fromRetouchTime(workspace.lastEditTime),
    //         );
    //         final covers = _getTopThreeCoverImages(temp);
    //         temp = temp.copyWith(coverImages: covers);
    //         // 保存更新后的项目信息
    //         await _projectRepository.updateProject(temp);
    //         final endTime = DateTime.now().millisecondsSinceEpoch;
    //         PGLog.d('OnSyncWorkspace: 工作区数据同步成功: ${endTime - nowTime}ms');
    //         PGLog.d('OnSyncWorkspace: 更新项目信息结束时间: $endTime');
    //       } else {
    //         PGLog.e('OnSyncWorkspace: 未找到对应的项目信息');
    //       }
    //     }
    //   }
    // } catch (e) {
    //   PGLog.e('OnSyncWorkspace handle error: $e');
    // }
  }

  // 获取前三张图片的绝对路径
  // List<String> _getTopThreeCoverImages(ProjectInfo project) {
  // return [];
  //   final workSpace = project.workspace;
  //   final files = workSpace.files;
  //   if (files.isEmpty) {
  //     return [];
  //   }

  //   // 按更新时间排序
  //   final sortedFiles = List.of(files)
  //     ..sort((a, b) => b.lastEditTime.compareTo(a.lastEditTime));

  //   // 获取前三张图片的相对路径
  //   return sortedFiles.take(3).map((e) => e.orgPath).toList();
  // }
}
