import '../../../../datalayer/domain/models/message_from_unity/message_from_unity.dart';
import '../../../../utils/pg_log.dart';
import 'unity_message_approver.dart';

class OnEngineErrorMessageApprover extends UnityMessageApprover {
  OnEngineErrorMessageApprover();

  @override
  String get method => 'OnEngineError';

  @override
  void handle(MessageFromUnity message) {
    final args = message.args;
    PGLog.d('OnEngineError: $args');
  }
}
