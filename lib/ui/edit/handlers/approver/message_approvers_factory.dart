import 'package:turing_art/datalayer/repository/project_repository.dart';

import 'on_engine_error_message_approver.dart';
import 'on_method_completed_approver.dart';
import 'sync_workspace_message_approver.dart';
import 'unity_message_approver.dart';

/// 消息处理器工厂类
class MessageApproversFactory {
  /// 创建编辑场景的Unity消息处理器链
  static UnityMessageApprover createEditMessageApproverChain(
      ProjectRepository projectRepository) {
    // 创建处理消息的实例
    final onEngineErrorHandler = OnEngineErrorMessageApprover();
    final onMethodCompletedHandler = OnMethodCompletedApprover();
    final syncWorkspaceHandler = SyncWorkspaceMessageApprover();

    // 组装责任链
    onEngineErrorHandler
        .setNext(onMethodCompletedHandler)
        .setNext(syncWorkspaceHandler);

    return onEngineErrorHandler;
  }

  /// 创建初始化Unity场景的Unity消息处理器链
  static UnityMessageApprover createInitializeMessageApproverChain() {
    // 创建处理消息的实例
    // 创建处理消息的实例
    final onEngineErrorHandler = OnEngineErrorMessageApprover();
    final onMethodCompletedHandler = OnMethodCompletedApprover();

    // 组装责任链
    onEngineErrorHandler.setNext(onMethodCompletedHandler);

    return onEngineErrorHandler;
  }
}
