import '../../../../datalayer/domain/models/message_from_unity/message_from_unity.dart';

/// Unity消息处理器抽象基类
abstract class UnityMessageApprover {
  // 消息名称
  String get method;

  // 下一个环节处理对象
  UnityMessageApprover? nextApprover;

  // 设置下一个处理器
  UnityMessageApprover setNext(UnityMessageApprover approver) {
    nextApprover = approver;
    return approver;
  }

  // 处理消息模板实现
  void invoke(MessageFromUnity message) {
    if (message.method != method) {
      nextApprover?.invoke(message);
      return;
    }

    handle(message);
  }

  // 具体的处理方式实现
  void handle(MessageFromUnity message);
}
