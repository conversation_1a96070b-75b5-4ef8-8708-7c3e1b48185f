// class OnEngineInitializedMessageApprover extends UnityMessageApprover {
//
//   OnEngineInitializedMessageApprover({
//   }) : _unityManager ?? UnityManager();
//
//   @override
//   String get method => 'OnEngineInitialized';
//
//   @override
//   void handle(UnityFromUnity message) {
//     final args = message.args;
//     PGLog.d('OnEngineInitialized: $args');
//     _unityManager.onEngineInitialized();
//   }
// }
