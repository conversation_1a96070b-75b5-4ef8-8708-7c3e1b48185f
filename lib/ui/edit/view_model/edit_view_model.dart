import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/core/external_message/external_message_manager.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/ui/dialog/import_project_choice_dialog.dart';
import 'package:turing_art/ui/edit/services/edit_action_service.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/ui/ui_status/edit_process_files_ui_status.dart';
import 'package:turing_art/ui/unity/use_case/get_cache_disk_use_cache.dart';
import 'package:turing_art/ui/unity/use_case/handle_dynamic_encryption_params_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_encrypted_lut_params_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_completed_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_project_use_case.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_task_update_state_use_case.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_token_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_local_key_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_novice_guide_completed_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_novice_guide_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_open_image_picker_usecase.dart';
import 'package:turing_art/ui/unity/use_case/track_event_use_case.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class EditViewModel extends ChangeNotifier {
  final ProjectRepository _projectRepository;
  final UnityController _unityController;
  final UnityUseCaseProvider _unityUseCaseProvider;
  final ExportUseCaseProvider _exportUseCaseProvider;
  final NoviceGuideManager _noviceGuideManager;
  final String projectId;
  final ValueNotifier<String> receivedMessageNotifier =
      ValueNotifier<String>('');
  final ExportProjectProvider _exportProjectProvider;
  final ExportTaskStateProvider _exportTaskStateProvider;
  final EditActionService _editActionService;

  // 消息处理器
  late final HandleExportTokenUseCase _exportTokenHandler;
  late final HandleDynamicEncryptionParamsUseCase _dynamicEncryptionHandler;
  late final HandleEncryptedLutParamsUseCase _encryptedLutHandler;
  late final HandleExportCompletedUseCase _exportCompletedHandler;
  late final GetCacheDiskSpaceUseCase _getCacheDiskSpaceHandler;
  late final HandleExportProjectUseCase _exportProjectHandler;
  late final HandleExportTaskUpdateStateUseCase _exportTaskUpdateStateHandler;
  late final HandleNoviceGuideUseCase _noviceGuideHandler;
  late final HandleNoviceGuideCompletedUseCase _noviceGuideCompletedHandler;
  late final HandleOpenImagePickerUseCase _openImagePickerHandler;
  late final HandleLocalKeyUseCase _getLocalKeyHandler;
  late final HandleTrackEventUseCase _trackEventHandler;

  // 异常提示事件控制器
  final StreamController<EditProcessFilesUiStatus> _exceptionController =
      StreamController<EditProcessFilesUiStatus>.broadcast();

  // 对外暴露事件流 （订单状态变更事件，采用事件流方式通知：以免发送通知后马上重置状态，导致UI有的还没有更新）
  Stream<EditProcessFilesUiStatus> get exceptionEvents =>
      _exceptionController.stream;

  // 外部消息管理器
  late final ExternalMessageManager _externalMessageManager;

  EditViewModel(
    this.projectId,
    this._projectRepository,
    this._unityController,
    this._unityUseCaseProvider,
    this._exportUseCaseProvider,
    this._exportProjectProvider,
    this._exportTaskStateProvider,
    this._noviceGuideManager,
    this._editActionService,
  ) {
    // 初始化消息处理器
    _initMessageHandlersUseCase();
    // 初始化外部消息管理器
    _initExternalMessageManager();
    _init();
  }

  /// 初始化消息处理器
  void _initMessageHandlersUseCase() {
    _exportTokenHandler = _unityUseCaseProvider.createExportTokenHandler(
        _unityController, _exportUseCaseProvider);
    _dynamicEncryptionHandler =
        _unityUseCaseProvider.createDynamicEncryptionHandler(_unityController);
    _encryptedLutHandler = _unityUseCaseProvider.createEncryptedLutHandler(
        _unityController, _exportUseCaseProvider);
    _exportCompletedHandler = _unityUseCaseProvider
        .createExportCompletedHandler(_exportUseCaseProvider);
    _getCacheDiskSpaceHandler =
        _unityUseCaseProvider.createGetCacheDiskSpaceHandler(_unityController);
    _exportProjectHandler = _unityUseCaseProvider
        .createExportProjectHandler(_exportProjectProvider);
    _noviceGuideHandler = _unityUseCaseProvider.createNoviceGuideHandler(
        _unityController, _noviceGuideManager);
    _noviceGuideCompletedHandler =
        _unityUseCaseProvider.createGuideCompletedHandler(_noviceGuideManager);
    _openImagePickerHandler =
        _unityUseCaseProvider.createOpenImagePickerHandler(_unityController);
    _getLocalKeyHandler =
        _unityUseCaseProvider.createLocalKeyHandler(_unityController);
    _exportTaskUpdateStateHandler = _unityUseCaseProvider
        .createExportTaskUpdateStateHandler(_exportTaskStateProvider);
    _trackEventHandler = _unityUseCaseProvider.createTrackEventHandler();
  }

  /// 初始化外部消息管理器
  void _initExternalMessageManager() {
    _externalMessageManager = ExternalMessageManager();

    // 初始化消息管理器
    _externalMessageManager.initialize(
      onMessageResult: (result) {
        // 处理消息结果，在编辑页面简单显示Toast通知
        PGLog.d('编辑页面收到外部消息处理结果: $result');
      },
      onMessageReceived: () {
        // 有新消息到达时通知UI
        PGLog.d('编辑页面收到新的外部消息');
        // 显示编辑页面协作请求弹窗
        _showEditPageCollaborationDialog();
      },
      identifier: 'EditViewModel',
    );

    // 编辑页面不注册处理器，只通过监听器接收通知
    // 让首页正常处理所有业务消息
  }

  /// 检查是否有待处理的外部消息
  bool get hasPendingExternalMessages =>
      _externalMessageManager.hasPendingExternalMessages;

  /// 处理所有等待中的外部消息（在有context可用时调用）
  Future<void> processPendingExternalMessages(BuildContext context) async {
    await _externalMessageManager.processPendingMessages(context);
  }

  void _init() {
    _unityController.setUnityVisibility(visible: true);
    // 监听拖拽文件
    _unityController.onDropFiles.listen((files) {
      if (files.isEmpty) {
        return;
      }
      _processDroppedFilesInEdit(DealImageFilesResult.fromImages(files, ""));
    });
  }

  // 获取项目信息
  Future<ProjectInfo?> getProjectInfo() async {
    return await _projectRepository.getProjectById(projectId);
  }

  Future<void> onUnityMessage(MessageFromUnity message) async {
    final nowTime = DateTime.now().millisecondsSinceEpoch;
    PGLog.d('EditViewModel: $nowTime 收到Unity消息: ${message.method}');
    // 直接处理不同类型的消息
    if (message.method == UnityMessage.syncWorkspace.value) {
      // 同步工作区,和金老师、靖哥确认不用await
      _unityUseCaseProvider.syncWorkspace.invoke(message);
    } else if (message.method == UnityMessage.workspaceChanged.value) {
      // 工作区文件发生改变
      _unityUseCaseProvider.workspaceChanged.invoke(message);
    } else if (message.method == UnityMessage.getExportTokenAsync.value) {
      await _exportTokenHandler.invoke(message);
    } else if (message.method ==
        UnityMessage.getDynamicEncryptionParams.value) {
      await _dynamicEncryptionHandler.invoke(message);
    } else if (message.method == UnityMessage.getEncryptedLutParams.value) {
      await _encryptedLutHandler.invoke(message);
    } else if (message.method == UnityMessage.onExportItemCompleted.value) {
      await _exportCompletedHandler.invoke(message);
    } else if (message.method == UnityMessage.onExportItemUpdated.value) {
      await _exportProjectHandler.invoke(message);
    } else if (message.method == UnityMessage.onReloadExportItems.value) {
      await _exportProjectHandler.invokeReloadProjectRecords(message);
    } else if (message.method == UnityMessage.onExportBatchItemsUpdated.value) {
      await _exportProjectHandler.invokeBatch(message);
    } else if (message.method == UnityMessage.getCacheDiskSpaceAsync.value) {
      await _getCacheDiskSpaceHandler.invoke(message);
    } else if (message.method == UnityMessage.openExportListDialog.value) {
      await _exportProjectHandler.openExportListDialog();
      _editActionService.statExportProgress();
    } else if (message.method == UnityMessage.getProjectGuideStepInfo.value) {
      await _noviceGuideHandler.invoke(message);
    } else if (message.method == UnityMessage.onProjectGuideStepInfo.value) {
      await _noviceGuideCompletedHandler.invoke(message);
    } else if (message.method == UnityMessage.onRequestImportImages.value) {
      await _openImagePickerHandler.invoke(message);
    } else if (message.method == UnityMessage.getLocalKey.value) {
      await _getLocalKeyHandler.invoke(message);
    } else if (message.method == UnityMessage.onExportTaskStateChanged.value) {
      await _exportTaskUpdateStateHandler.invoke(message);
    } else if (message.method == UnityMessage.onTrackEvent.value) {
      await _trackEventHandler.invoke(message);
    } else {
      PGLog.d('未处理的Unity消息类型: ${message.method}');
    }
  }

  void onDebugMessageReceived(String message) {
    receivedMessageNotifier.value = message;
  }

  /// 在编辑页面处理拖拽的文件
  Future<void> _processDroppedFilesInEdit(
    DealImageFilesResult fileResult,
  ) async {
    try {
      final files = fileResult.validFiles;
      PGLog.d('编辑页面接收到 ${files.length} 个拖拽文件');

      // 调用 ImportImagesToUnityUseCase 处理图片文件并获取要发送的消息
      final nowTime = DateTime.now().millisecondsSinceEpoch;
      // 检查磁盘空间
      /*
      1.3.0 保证拖动速度，暂时取消在unity中拖动文件时检查磁盘空间
      if (!await _workspaceUseCase.generateWorkspaceDiskSpaceGuard
          .invoke(files: files)) {
        PGLog.d('编辑页面拖拽文件超过磁盘空间，不发送消息');
        _exceptionController.add(
          const EditProcessFilesUiStatus.error(
            errorType: EditProcessFilesErrorType.diskSpace,
          ),
        );
        return;
      }
      */

      final message = await _unityUseCaseProvider.importImagesToUnity
          .invoke(fileResult, projectId);
      final endTime = DateTime.now().millisecondsSinceEpoch;
      PGLog.d(
          'EditViewModel: 处理拖拽文件importImagesToUnity时间: ${endTime - nowTime}ms');

      // 如果生成了有效的消息，则发送给Unity
      if (message != null) {
        await _unityController.sendMessage(message);
        final endTime1 = DateTime.now().millisecondsSinceEpoch;
        PGLog.d('EditViewModel: 发送消息到Unity耗时: ${endTime1 - endTime}ms');
        PGLog.d('EditViewModel: 发送了导入图片消息到Unity: $endTime1');
      } else {
        PGLog.d('EditViewModel: 未找到有效的图片文件，未发送消息');
        _exceptionController.add(
          const EditProcessFilesUiStatus.error(
            errorType: EditProcessFilesErrorType.other,
          ),
        );
      }
    } catch (e) {
      PGLog.d('处理编辑页面拖拽文件出错: $e');
      _exceptionController.add(
        const EditProcessFilesUiStatus.error(
          errorType: EditProcessFilesErrorType.other,
        ),
      );
    }
  }

  @override
  void dispose() {
    _unityController.setUnityVisibility(visible: false);
    _exitWorkspace();
    receivedMessageNotifier.dispose();
    // 清理外部消息管理器中当前 ViewModel 的回调
    _externalMessageManager.removeCallbacks('EditViewModel');
    super.dispose();
  }

  // 退出当前工作区
  void _exitWorkspace() {
    final message = _unityUseCaseProvider.exitWorkspace.invoke();
    _unityController.sendMessage(message);
  }

  /// 显示编辑页面协作请求弹窗
  void _showEditPageCollaborationDialog() {
    // 使用延迟来确保UI已经准备好
    Future.delayed(const Duration(milliseconds: 500), () {
      // 使用ImportProjectChoiceDialog显示协作请求弹窗
      ImportProjectChoiceDialog.showEditPageCollaboration(
        onProcessNow: () async {
          PGLog.d('用户选择立即处理协作请求');
          // 实现立即处理逻辑，同home点击跳转至首页
          await _editActionService.handleBackToHome(projectId);
          PGDialog.dismiss(tag: 'edit_page_collaboration_dialog');
        },
        onProcessLater: () {
          PGLog.d('用户选择稍后处理协作请求');
          PGDialog.dismiss(tag: 'edit_page_collaboration_dialog');
        },
      );
    });
  }
}
