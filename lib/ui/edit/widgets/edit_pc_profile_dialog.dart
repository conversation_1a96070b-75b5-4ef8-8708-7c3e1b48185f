import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/enums/user_role.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_integral_info_widget.dart';
import 'package:turing_art/ui/purchase/view_models/purchase_view_model.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';

class ProjectEditPcProfileDialog extends StatefulWidget {
  final VoidCallback onLogout;
  final VoidCallback onSettings;
  final VoidCallback onCheckUpdate;
  final Function(PurchaseTabType?) onPurchase;
  final VoidCallback onExport;
  final VoidCallback onAboutUs;
  final VoidCallback onCompleteAccountInfo;
  final bool isAigcUser;

  const ProjectEditPcProfileDialog({
    super.key,
    required this.onLogout,
    required this.onSettings,
    required this.onCheckUpdate,
    required this.onPurchase,
    required this.onExport,
    required this.onAboutUs,
    required this.onCompleteAccountInfo,
    required this.isAigcUser,
  });

  @override
  State<ProjectEditPcProfileDialog> createState() =>
      _ProjectEditPcProfileDialogState();
}

class _ProjectEditPcProfileDialogState
    extends State<ProjectEditPcProfileDialog> {
  String? _hoveredOption;

  @override
  Widget build(BuildContext context) {
    return Consumer<ProfileDialogViewModel>(
      builder: (context, profileViewModel, child) {
        final isSubAccount = profileViewModel.role == UserRole.employee;

        // 根据是否为子账号，确定选项列表
        final options = isSubAccount
            ? [
                _buildOptionItem('设置', onTap: widget.onSettings),
                _buildOptionItem('关于我们', onTap: widget.onAboutUs),
                _buildOptionItem('退出登录', onTap: widget.onLogout),
              ]
            : [
                _buildOptionItem('续费充值', onTap: () => widget.onPurchase(null)),
                _buildOptionItem('导出计费明细', onTap: widget.onExport),
                _buildOptionItem('设置', onTap: widget.onSettings),
                _buildOptionItem('关于我们', onTap: widget.onAboutUs),
                _buildOptionItem('退出登录', onTap: widget.onLogout),
              ];

        final dialogHeight = widget.isAigcUser
            ? options.length * 36.0 + 160.0 + 16.0
            : options.length * 36.0 + 116.0 + 16.0; // 非AIGC用户减去44px积分卡片高度

        return Container(
          width: 176,
          height: dialogHeight,
          decoration: BoxDecoration(
            color: const Color(0xFF121415),
            borderRadius: BorderRadius.circular(12),
            boxShadow: const [
              BoxShadow(
                color: Color(0x4D000000),
                offset: Offset(0, 2),
                blurRadius: 20,
              ),
            ],
          ),
          child: Stack(
            children: [
              // 最底层Logo
              _buildLogoBackground(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 用户信息卡片
                  const SizedBox(height: 8),
                  _buildUserInfoCard(profileViewModel),
                  // 功能列表
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Column(
                      children: options,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLogoBackground() {
    return Positioned(
      top: 58,
      right: 8,
      child: Opacity(
        opacity: 0.15,
        child: Container(
          width: 43,
          height: 80,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/icons/profile_back_logo.png'),
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard(ProfileDialogViewModel profileViewModel) {
    final isSubAccount = profileViewModel.role == UserRole.employee;
    final cardHeight = widget.isAigcUser ? 160.0 : 116.0; // 非AIGC用户减去44px高度

    return Container(
      width: 160,
      height: cardHeight,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: const Color(0x4D000000),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: const Color(0x0FFFFFFF),
          width: 0.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 手机号和子账号标签
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  profileViewModel.mobile ?? '',
                  style: const TextStyle(
                    color: Color(0xFFE1E2E5),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 4),
                // 子账号标签
                if (isSubAccount)
                  Container(
                    width: 40,
                    height: 20,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: const Color(0xA6EBEDF5), // #EBEDF5 65%
                        width: 0.5,
                      ),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
                    child: Center(
                      child: Text(
                        '子账号',
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: FontWeight.w400,
                          fontSize: 10,
                          height: 1.0,
                          letterSpacing: 0,
                          color: const Color(0xA6EBEDF5), // #EBEDF5 65%
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            // 套餐账号信息（只在子账号时显示）
            if (isSubAccount)
              Padding(
                padding: const EdgeInsets.only(top: 6),
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 20,
                      decoration: BoxDecoration(
                        color: const Color(0xFF1B1C1F),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 3),
                      child: Center(
                        child: Text(
                          '套餐账号',
                          style: TextStyle(
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.regular,
                            fontSize: 10,
                            height: 14 / 10,
                            letterSpacing: 0,
                            color: const Color(0xA6EBEDF5), // #EBEDF5 65%
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 2),
                    Text(
                      profileViewModel.mainAccountMobile ?? '',
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.medium,
                        fontSize: 12,
                        height: 18 / 12,
                        letterSpacing: 0,
                        color: const Color(0xA6EBEDF5), // #EBEDF5 65%
                      ),
                    ),
                  ],
                ),
              ),
            if (profileViewModel.isFinishInit &&
                profileViewModel.getNeedShowWechatGiftDialog())
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: PlatformMouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: DebounceClickWidget(
                    onTap: () async {
                      // 窗口关闭(在unity窗口上必须等待，否则层级不正确)
                      await PGDialog.dismiss(tag: DialogTags.winEditProfile);
                      widget.onCompleteAccountInfo();
                    },
                    child: const Text(
                      '完善信息，免费获得张数',
                      style: TextStyle(
                        color: Color(0xFFF72651),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
            const Spacer(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '${profileViewModel.editExportInfo?.available ?? 0}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      ' / ${profileViewModel.editExportInfo?.total ?? 0}',
                      style: const TextStyle(
                        color: Color(0xFFEBF2F5),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                const Text(
                  '剩余张数/总张数',
                  style: TextStyle(
                    color: Color(0xFFEBF2F5),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 10),
                // 只有AIGC用户才显示积分卡片组件
                if (widget.isAigcUser)
                  if (profileViewModel.aigcInfo == null)
                    const SizedBox.shrink()
                  else
                    ProjectHomeIntegralInfoWidget(
                      integralCount: profileViewModel.integralCount,
                      isNeedPurchase: !isSubAccount,
                      onBuyIntegralClick: () async {
                        // 窗口关闭
                        await PGDialog.dismiss(tag: DialogTags.winEditProfile);
                        // 调用购买回调，传递AI积分类型
                        widget.onPurchase(PurchaseTabType.aigcPoint);
                      },
                    ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionItem(String text, {VoidCallback? onTap}) {
    final isHovered = _hoveredOption == text;
    final isActive = onTap != null;

    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _hoveredOption = text),
      onExit: (_) => setState(() => _hoveredOption = null),
      child: DebounceClickWidget(
        onTap: onTap == null
            ? null
            : () async {
                // 窗口关闭
                await PGDialog.dismiss(tag: DialogTags.winEditProfile);
                onTap();
              },
        child: Container(
          width: 160,
          height: 36,
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: isHovered && isActive
                ? const Color(0x0DFFFFFF) // #FFFFFF0D
                : Colors.transparent,
          ),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  text,
                  style: TextStyle(
                    color: isActive
                        ? (isHovered
                            ? const Color(0xFFFFFFFF) // 悬停/点击时颜色
                            : const Color(0x99EBF2F5)) // 正常状态
                        : const Color(0xFFEBF2F5).withOpacity(0.6),
                    fontSize: 12,
                    fontWeight:
                        isHovered && isActive ? Fonts.semiBold : Fonts.regular,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
