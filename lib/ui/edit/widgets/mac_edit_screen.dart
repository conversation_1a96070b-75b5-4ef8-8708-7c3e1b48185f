import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../providers/project_state_provider.dart';
import '../../../utils/pg_log.dart';

class MacEditScreen extends StatefulWidget {
  final String? projectId;
  const MacEditScreen({super.key, this.projectId});

  @override
  State<MacEditScreen> createState() => _MacEditScreenState();
}

class _MacEditScreenState extends State<MacEditScreen> {
  bool _isLoading = true;
  String? _errorMessage;
  ProjectStateProvider? _projectStateProvider;
  @override
  void initState() {
    super.initState();
    _startUnityProcess();
  }

  @override
  void dispose() {
    _clearProjectState();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _projectStateProvider = context.read<ProjectStateProvider>();
  }

  void _clearProjectState() {
    _projectStateProvider?.exitEdit();
  }

  Future<void> _startUnityProcess() async {
    try {
      const unityPath = '/Applications/Unity/Unity.app/Contents/MacOS/Unity';
      if (!File(unityPath).existsSync()) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Unity应用不存在';
        });
        return;
      }

      final process = await Process.start(
        unityPath,
        [
          '-projectPath',
          '/path/to/your/project',
          '-executeMethod',
          'EditorScript.YourMethod',
          '-projectId',
          widget.projectId ?? '',
        ],
      );

      // 监听标准输出
      process.stdout.listen(
        (data) => PGLog.d('Unity stdout: ${String.fromCharCodes(data)}'),
        onError: (error) => PGLog.d('Unity stdout error: $error'),
        onDone: () => PGLog.d('Unity stdout done'),
      );

      // 监听标准错误
      process.stderr.listen(
        (data) => PGLog.d('Unity stderr: ${String.fromCharCodes(data)}'),
        onError: (error) => PGLog.d('Unity stderr error: $error'),
        onDone: () => PGLog.d('Unity stderr done'),
      );

      // 监听进程退出
      final exitCode = await process.exitCode;
      PGLog.d('Unity process exited with code: $exitCode');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      PGLog.d('启动Unity失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = '启动Unity失败: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Unity编辑器'),
      ),
      backgroundColor: Colors.yellow,
      body: Center(
        child: _isLoading
            ? const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在启动Unity编辑器...'),
                ],
              )
            : _errorMessage != null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _isLoading = true;
                            _errorMessage = null;
                          });
                          _startUnityProcess();
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  )
                : const Text('Unity已启动'),
      ),
    );
  }
}
