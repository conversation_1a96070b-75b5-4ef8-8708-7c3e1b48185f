import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// 预设文件工具类，提供预设相关的文件路径
class PresetUtils {
  /// 目前不支持导入的预设类型
  static List<String> unsupportedPresetKeys = ['cosmetic', 'lutFilters'];

  /// 获取预设数据的本地存储路径
  static Future<String> getPresetDataFilePath() async {
    final dir = await getApplicationDocumentsDirectory();
    return path.join(dir.path, 'preset', 'preset_data.json');
  }
}
