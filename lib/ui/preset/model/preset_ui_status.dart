import 'package:flutter/cupertino.dart';

import '../../../datalayer/domain/models/preset/preset.dart';
import '../../../datalayer/domain/models/preset/preset_item.dart';

class PresetUiStatus {
  final ValueNotifier<List<Preset>> presets;

  final ValueNotifier<int> currentPresetIdx = ValueNotifier(0);

  final ValueNotifier<PresetItem?> currentPresetItem = ValueNotifier(null);

  PresetUiStatus({
    required List<Preset> presets,
  }) : this.presets = ValueNotifier(presets);
}
