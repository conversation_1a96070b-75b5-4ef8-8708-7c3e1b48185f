import 'dart:convert';

import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/ui/preset/utils/preset_utils.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class GenerateImportPresetMessageUseCase {
  /// 导入预设
  Future<MessageToUnity?> invoke(List<String> selectedPresets) async {
    try {
      final presetFilePath = await PresetUtils.getPresetDataFilePath();
      final args = {
        'xiuTuPresetFile': presetFilePath,
        'presetIds': selectedPresets,
      };

      final json = jsonEncode(args);
      final msg = MessageToUnity(
          completed: const Uuid().v4(),
          method: "ConvertXiuTuPresets",
          args: json);

      return msg;
    } catch (e) {
      PGLog.e('组装导入预设数据给Unity失败: $e');
      return null;
    }
  }
}
