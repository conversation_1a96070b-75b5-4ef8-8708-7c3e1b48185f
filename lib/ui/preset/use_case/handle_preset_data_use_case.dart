import 'dart:convert';
import 'dart:io';

import 'package:turing_art/datalayer/domain/models/preset/remote_preset_item.dart';
import 'package:turing_art/ui/preset/model/preset_category.dart';
import 'package:turing_art/ui/preset/utils/preset_utils.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 用于处理从服务端获取的预设数据 useCase
class HandlePresetDataUseCase {
  Future<List<PresetCategoryUI>> invoke(String data) async {
    try {
      if (data.isEmpty) {
        PGLog.e('获取预设数据失败: 返回数据为空');
        return [];
      }
      // 1. 首先将原始预设数据保存到本地
      _savePresetResponse(data);
      // 2. 解析预设数据
      final List<dynamic> jsonList = jsonDecode(data);
      final list =
          jsonList.map((json) => RemotePresetItem.fromJson(json)).toList();

      PGLog.i('获取远端预设数据成功，共有${list.length}个分类');

      return _transformPresets(list);
    } catch (e) {
      // 处理解析错误
      PGLog.e('获取预设数据异常, $e');
      return [];
    }
  }

  // 保存原始的预设数据到本地
  Future<bool> _savePresetResponse(String response) async {
    try {
      final filePath = await PresetUtils.getPresetDataFilePath();
      final file = File(filePath);
      if (!file.parent.existsSync()) {
        await file.parent.create(recursive: true);
      }
      // 将响应数据保存到本地文件
      await file.writeAsString(response);
      return true;
    } catch (e) {
      PGLog.e('保存预设数据异常: $e');
      return false;
    }
  }

  // 将原始的预设数据转换为 UI 需要的数据
  Future<List<PresetCategoryUI>> _transformPresets(
      List<RemotePresetItem> list) async {
    final unsupportedPresets = PresetUtils.unsupportedPresetKeys;

    return list.map((presetItem) {
      final presetUIList = presetItem.data.map((presetData) {
        // 获取咻图的预设参数
        final params = presetData.param;
        // 如果当前参数中包含了不支持的效果，则目前不支持导入
        final supported = !hasAnyKeyInList(params, unsupportedPresets);

        return PresetUI(
          id: presetData.id,
          name: presetData.name,
          categoryId: presetData.cid,
          sorted: presetData.sorted,
          supported: supported,
        );
      }).toList();

      return PresetCategoryUI(
          id: presetItem.id, name: presetItem.name, presets: presetUIList);
    }).toList();
  }

  bool hasAnyKeyInList(Map<String, dynamic> map, List<String> keyList) {
    return map.keys.any((key) => keyList.contains(key));
  }
}
