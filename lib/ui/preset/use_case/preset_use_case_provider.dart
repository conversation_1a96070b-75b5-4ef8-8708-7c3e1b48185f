import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/ui/preset/use_case/fetch_remote_preset_use_case.dart';
import 'package:turing_art/ui/preset/use_case/generate_import_preset_message_use_case.dart';
import 'package:turing_art/ui/preset/use_case/handle_preset_data_use_case.dart';

class PresetUseCaseProvider {
  final FetchRemotePresetUseCase fetchRemotePresetUseCase;
  final HandlePresetDataUseCase handlePresetDataUseCase;
  final GenerateImportPresetMessageUseCase generateImportPresetMessageUseCase;

  PresetUseCaseProvider({
    required ApiClient apiClient,
  })  : fetchRemotePresetUseCase = FetchRemotePresetUseCase(apiClient),
        handlePresetDataUseCase = HandlePresetDataUseCase(),
        generateImportPresetMessageUseCase =
            GenerateImportPresetMessageUseCase();
}
