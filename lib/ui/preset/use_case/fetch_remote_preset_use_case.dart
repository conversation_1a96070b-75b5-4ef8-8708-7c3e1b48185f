import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/dio_factory.dart';
import 'package:turing_art/datalayer/service/api/preset/preset_import_api_service.dart';

/// 获取远端预设数据
class FetchRemotePresetUseCase {
  final ApiClient _apiClient;
  late final PresetImportApiService _presetImportApiService;

  FetchRemotePresetUseCase(this._apiClient) {
    // 创建API服务实例
    final dio = DioFactory.createDio(_apiClient);
    _presetImportApiService = PresetImportApiService(dio);
  }

  Future<String> invoke() async {
    return await _presetImportApiService.getPresetData();
  }
}
