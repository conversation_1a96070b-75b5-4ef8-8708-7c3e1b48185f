import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/core/unity/unity_exception.dart';
import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/domain/models/preset/preset.dart';
import 'package:turing_art/datalayer/domain/models/preset/preset_item.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class PresetDataService {
  final UnityController _unityController;

  PresetDataService(
    UnityController unityController,
  ) : _unityController = unityController;

  Future<List<Preset>> fetchPresets() async {
    try {
      final result =
          await _unityController.sendMessageWithResponse<MessageFromUnity>(
        MessageToUnity(
          method: 'RequestPresets',
          args: "",
          completed: const Uuid().v4(),
        ),
        UnityMessage.gotPresets,
      );

      PGLog.d('FetchPresetDataUseCase原始数据: ${result.toJson()}'); // 记录完整原始数据

      if (result.args == null) {
        PGLog.e('预设数据格式异常: args为空');
        return [];
      }

      final rawData = result.args!;
      if (rawData.isEmpty || rawData[0] is! String) {
        PGLog.e('预设数据结构异常: 首元素非Json字符串类型');
        return [];
      }

      final jsonString = rawData[0] as String;
      final presetsData = jsonDecode(jsonString) as Map<String, dynamic>;

      // 关键字段校验
      if (!presetsData.containsKey('inner') ||
          !presetsData.containsKey('user')) {
        PGLog.e('预设数据缺少必要字段: ${presetsData.keys}');
        return [];
      }

      return _fromUnityData(presetsData);
    } on UnityException catch (e) {
      PGLog.e('Unity异常: ${e.message}');
    } on TypeError catch (e) {
      PGLog.e('类型转换错误: ${e.toString()}\n${e.stackTrace}');
    } catch (e, stack) {
      PGLog.e('获取预设未知错误: $e\n$stack');
    }
    return [];
  }

  // 新增高效的手动分组实现
  List<Preset> _fromUnityData(Map<String, dynamic> json) {
    // 合并数据源并转换类型
    final mergedItems = <Map<String, dynamic>>[
      ...(json['inner'] as List).cast<Map<String, dynamic>>(),
      ...(json['user'] as List).cast<Map<String, dynamic>>(),
    ];

    // 手动实现分组逻辑
    final categoryGroups = <String, List<Map<String, dynamic>>>{};
    for (final item in mergedItems) {
      final category = item['presetCategory'] as String;
      categoryGroups.putIfAbsent(category, () => []).add(item);
    }

    // 转换为 Preset 列表
    final presets = categoryGroups.entries.map((entry) {
      return Preset(
        category: entry.key,
        items: entry.value.map((item) => PresetItem.fromJson(item)).toList(),
      );
    }).toList();

    return presets;
  }
}
