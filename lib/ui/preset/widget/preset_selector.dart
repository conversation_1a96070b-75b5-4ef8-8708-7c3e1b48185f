import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

import '../model/preset_ui_status.dart';

class PresetSelector extends StatefulWidget {
  final PresetUiStatus presetUiStatus;
  final VoidCallback onTap;
  final String displayText;

  const PresetSelector({
    super.key,
    required this.presetUiStatus,
    required this.onTap,
    required this.displayText,
  });

  @override
  State<PresetSelector> createState() => _PresetSelectorState();
}

class _PresetSelectorState extends State<PresetSelector> {
  bool _hovered = false;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: PlatformMouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (_) => setState(() => _hovered = true),
        onExit: (_) => setState(() => _hovered = false),
        child: Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: _hovered
                ? const Color(0xFFFFFFFF).withAlpha(20)
                : const Color(0xFFFFFFFF).withAlpha(12),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  widget.displayText,
                  style: TextStyle(
                    fontSize: 12,
                    color: const Color(0xFFEBF2F5).withOpacity(0.6),
                  ),
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: const Color(0xFFEBF2F5).withOpacity(0.6),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
