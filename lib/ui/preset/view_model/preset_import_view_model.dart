import 'package:flutter/foundation.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/ui/preset/model/preset_category.dart';
import 'package:turing_art/ui/preset/use_case/preset_use_case_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class PresetImportViewModel extends ChangeNotifier {
  final PresetUseCaseProvider _presetUseCaseProvider;
  final UnityController _unityController;
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<String> errorMessage = ValueNotifier('');

  // 是否已初始化（防止重复加载）
  bool _hasInitialized = false;

  bool get hasInitialized => _hasInitialized;

  // 所有预设数据
  List<PresetCategoryUI> _presetItems = [];

  List<PresetCategoryUI> get presetItems => _presetItems;

  // 已展开的分类ID
  final Set<String> _expandedCategoryIds = {};

  // 已选中的预设ID列表
  final Set<String> _selectedPresetIds = {};

  List<PresetUI> get selectedPresets {
    final List<PresetUI> result = [];
    for (final item in _presetItems) {
      for (final preset in item.presets) {
        if (_selectedPresetIds.contains(preset.id)) {
          result.add(preset);
        }
      }
    }
    return result;
  }

  PresetImportViewModel(
    this._unityController,
    this._presetUseCaseProvider,
  );

  /// 加载预设数据
  Future<void> loadPresets() async {
    isLoading.value = true;
    errorMessage.value = '';
    try {
      // 从服务器获取预设数据
      final response =
          await _presetUseCaseProvider.fetchRemotePresetUseCase.invoke();
      final presets =
          await _presetUseCaseProvider.handlePresetDataUseCase.invoke(response);

      if (presets.isEmpty) {
        errorMessage.value = '没有可导入的预设';
        return;
      }

      _presetItems = presets;

      // 默认展开第一个分类
      if (presets.isNotEmpty) {
        _expandedCategoryIds.add(presets.first.id);
      }

      _hasInitialized = true;
      notifyListeners();
    } catch (e) {
      PGLog.e('加载预设异常: $e');
      errorMessage.value = '加载预设失败，请稍后重试';
    } finally {
      isLoading.value = false;
    }
  }

  /// 检查分类是否展开
  bool isExpanded(String categoryId) {
    return _expandedCategoryIds.contains(categoryId);
  }

  /// 切换分类展开/折叠状态
  void toggleExpand(String categoryId) {
    if (_expandedCategoryIds.contains(categoryId)) {
      _expandedCategoryIds.remove(categoryId);
    } else {
      _expandedCategoryIds.add(categoryId);
    }
    notifyListeners();
  }

  /// 获取指定分类下的预设列表
  List<PresetUI> getPresetsForCategory(String categoryId) {
    if (categoryId.isEmpty) {
      return [];
    }

    final category = _findCategoryById(categoryId);
    return category.presets;
  }

  /// 检查预设是否被选中
  bool isPresetSelected(String presetId) {
    return _selectedPresetIds.contains(presetId);
  }

  /// 检查是否所有预设都被选中
  bool isAllSelected([String? categoryId]) {
    if (categoryId != null && categoryId.isNotEmpty) {
      // 检查指定分类下所有支持的预设是否全部被选中
      final category = _findCategoryById(categoryId);

      // 获取分类中支持的预设
      final supportedPresets =
          category.presets.where((p) => p.supported).toList();
      if (supportedPresets.isEmpty) {
        return false;
      }

      return supportedPresets
          .every((preset) => _selectedPresetIds.contains(preset.id));
    } else {
      // 检查所有支持的预设是否全部被选中
      int totalSupportedPresets = 0;
      final List<String> allSupportedPresetIds = [];

      for (final item in _presetItems) {
        final supportedPresets =
            item.presets.where((p) => p.supported).toList();
        totalSupportedPresets += supportedPresets.length;
        allSupportedPresetIds.addAll(supportedPresets.map((p) => p.id));
      }

      // 确保所有已选中的预设都是支持的且所有支持的预设都被选中
      return totalSupportedPresets > 0 &&
          allSupportedPresetIds.every((id) => _selectedPresetIds.contains(id));
    }
  }

  /// 选择所有预设
  void selectAll() {
    // 首先清空已选中的预设
    _selectedPresetIds.clear();
    // 只选择支持的预设
    for (final item in _presetItems) {
      for (final preset in item.presets) {
        if (preset.supported) {
          _selectedPresetIds.add(preset.id);
        }
      }
    }
    notifyListeners();
  }

  /// 取消选择所有预设
  void unselectAll() {
    _selectedPresetIds.clear();
    notifyListeners();
  }

  /// 检查分类下所有支持的预设是否都被选中
  bool isAllSelectedInCategory(String categoryId) {
    if (categoryId.isEmpty) {
      return false;
    }
    final category = _findCategoryById(categoryId);
    final compatiblePresets =
        category.presets.where((p) => p.supported).toList();
    if (compatiblePresets.isEmpty) return false;

    return compatiblePresets
        .every((preset) => _selectedPresetIds.contains(preset.id));
  }

  /// 选择分类下的所有预设
  void selectAllInCategory(String categoryId) {
    if (categoryId.isEmpty) {
      return;
    }

    final category = _findCategoryById(categoryId);

    // 只选择支持的预设
    for (final preset in category.presets) {
      if (preset.supported) {
        _selectedPresetIds.add(preset.id);
      }
    }

    notifyListeners();
  }

  /// 取消选择分类下的所有预设
  void unselectAllInCategory(String categoryId) {
    if (categoryId.isEmpty) {
      return;
    }

    final category = _findCategoryById(categoryId);

    for (final preset in category.presets) {
      _selectedPresetIds.remove(preset.id);
    }

    notifyListeners();
  }

  /// 选择预设
  void selectPreset(String presetId) {
    // 确保只能选择支持的预设
    for (final category in _presetItems) {
      final preset = category.presets.firstWhere((p) => p.id == presetId,
          orElse: () => PresetUI(
              id: '', name: '', categoryId: '', sorted: 0, supported: false));

      if (preset.id.isNotEmpty && preset.supported) {
        _selectedPresetIds.add(presetId);
        notifyListeners();
        return;
      }
    }
  }

  /// 导入选中的预设
  Future<bool> importSelectedPresets() async {
    if (_selectedPresetIds.isEmpty) {
      PGDialog.showToast('请至少选择一个预设');
      return false;
    }
    final msg = await _presetUseCaseProvider.generateImportPresetMessageUseCase
        .invoke(_selectedPresetIds.toList(growable: false));
    if (msg == null) {
      PGDialog.showToast('导入预设失败');
      return false;
    }

    _unityController.sendMessage(msg);
    PGDialog.showToast('导入预设成功');
    return true;
  }

  /// 取消选择预设
  void unselectPreset(String presetId) {
    _selectedPresetIds.remove(presetId);
    notifyListeners();
  }

  /// 根据分类ID获取分类
  PresetCategoryUI _findCategoryById(String categoryId) {
    return _presetItems.firstWhere(
      (item) => item.id == categoryId,
      orElse: () => PresetCategoryUI(
        id: '',
        name: '',
        presets: [],
      ),
    );
  }
}
