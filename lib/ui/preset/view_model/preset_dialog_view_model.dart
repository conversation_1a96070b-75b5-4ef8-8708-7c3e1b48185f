import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../../../datalayer/domain/models/preset/preset_item.dart';
import '../model/preset_ui_status.dart';
import '../service/preset_data_service.dart';

class PresetDialogViewModel extends ChangeNotifier {
  final ValueNotifier<bool> isLoading = ValueNotifier(true);

  /// 预设状态
  final PresetUiStatus _presetUiStatus;
  final PresetDataService _presetDataService;
  PresetUiStatus get presetUiStatus => _presetUiStatus;

  PresetItem? get currentPresetItem => _presetUiStatus.currentPresetItem.value;

  String? _customSavePath;
  String? get customSavePath => _customSavePath;

  final String _defaultSavePath = '';
  String get defaultSavePath => _defaultSavePath;

  PresetDialogViewModel(
    UnityController unityController,
  )   : _presetDataService = PresetDataService(unityController),
        _presetUiStatus = PresetUiStatus(presets: []) {
    initializePresets();
  }

  /// 切换预设分类
  void onPresetCategoryChanged(int idx) {
    if (idx < 0 ||
        idx >= _presetUiStatus.presets.value.length ||
        idx == _presetUiStatus.currentPresetIdx.value) {
      return;
    }

    _presetUiStatus.currentPresetIdx.value = idx;
    notifyListeners();
  }

  /// 选中某预设
  void onPresetItemChanged(int idx) {
    final category = _presetUiStatus.currentPresetIdx.value;
    final preset = _presetUiStatus.presets.value[category];

    if (idx < 0 || idx >= preset.items.length) {
      return;
    }

    _presetUiStatus.currentPresetItem.value = preset.items[idx];
    notifyListeners();
  }

  String? getSavePath() {
    if (_customSavePath != null) {
      return _customSavePath;
    }
    return _defaultSavePath;
  }

  /// 获取当前选中的预设文本
  String get selectedPresetText {
    if (_presetUiStatus.presets.value.isEmpty) {
      return '选择一个预设';
    }
    final currentPreset =
        _presetUiStatus.presets.value[_presetUiStatus.currentPresetIdx.value];
    final currentPresetItem = _presetUiStatus.currentPresetItem.value;

    if (currentPresetItem == null) {
      return '选择一个预设';
    }
    return '${currentPreset.category}-${currentPresetItem.title}';
  }

  /// 选择预设分类
  void selectPreset(int index) {
    _presetUiStatus.currentPresetIdx.value = index;
    final preset = _presetUiStatus.presets.value[index];
    if (preset.items.isNotEmpty) {
      _presetUiStatus.currentPresetItem.value = preset.items.first;
    }
    notifyListeners();
  }

  /// 选择预设项
  void selectPresetItem(PresetItem item) {
    _presetUiStatus.currentPresetItem.value = item;
    notifyListeners();
  }

  /// 初始化预设数据
  void initializePresets() async {
    isLoading.value = true;
    final presets = await _presetDataService.fetchPresets();
    _presetUiStatus.presets.value = presets;
    if (presets.isNotEmpty) {
      _presetUiStatus.currentPresetIdx.value = 0;
      // 暂时不选中任何一个默认的预设
      // if (presets[0].items.isNotEmpty) {
      //   _presetUiStatus.currentPresetItem.value = presets[0].items.first;
      // }
    }
    isLoading.value = false;
  }

  Future<void> selectCustomSavePath() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择保存位置',
        lockParentWindow: true,
      );

      if (selectedDirectory != null) {
        _customSavePath = selectedDirectory;
        notifyListeners();
      }
    } catch (e) {
      PGLog.d('Error selecting directory: $e');
    }
  }
}
