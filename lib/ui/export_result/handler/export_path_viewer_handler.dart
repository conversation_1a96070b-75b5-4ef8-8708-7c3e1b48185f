import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_path_info.dart';
import 'package:turing_art/ui/aigc_sample/services/file_path_service.dart';
import 'package:turing_art/ui/dialog/universal_dialog.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 导出路径查看处理器
class ExportPathViewerHandler {
  final AigcMySampleExportManager _aigcMySampleExportManager;
  final FilePathService _filePathService;

  ExportPathViewerHandler(
    this._aigcMySampleExportManager,
    this._filePathService,
  );

  /// 处理AIGC导出路径查看
  /// [exportPathInfo] 导出路径信息,打样详情需要传
  /// [largeResultPhotoUrl] 别人做好的高清大图，在打样详情需要传
  Future<void> handleAigcExportPathView(String projectId,
      ExportPathInfo? exportPathInfo, String? largeResultPhotoUrl) async {
    PGLog.d("AIGC导出点击了搜索");
    try {
      final checkPathResult = await _aigcMySampleExportManager
          .getProofingExportFilePath(projectId, exportPathInfo);
      // 目标路径(导出面板是basePath/projectFolderName; 打样详情是basePath/projectFolderName/fileName)
      final targetPath = checkPathResult.resultPath;
      PGLog.d(
          'AIGC导出路径: $targetPath, 是否是预期路径: ${checkPathResult.isExpectedPathExists}');

      final hasDownloadableFiles = await _handleNeedDownload(
          projectId,
          checkPathResult.isExpectedPathExists,
          targetPath,
          largeResultPhotoUrl,
          exportPathInfo);
      // 如果需要下载，则不打开文件夹，如果不需要下载已有文件，则打开文件夹
      if (hasDownloadableFiles) {
        return;
      }

      // 正常打开文件夹流程（1.预期文件存在，则打开预期路径，2.预期文件不存在，但是已经下载好了也打开桌面路径）
      final success = await _filePathService.openFolder(targetPath);
      if (!success) {
        // 打开文件夹失败，提示
        PGDialog.showToast('打开导出文件夹失败');
      }
      PGLog.d('AIGC导出打开文件夹结果: ${success ? '成功' : '失败'}, 路径: $targetPath');
    } catch (e) {
      PGLog.e('AIGC导出打开文件夹失败: $e');
      PGDialog.showToast('打开文件夹失败');
    }
  }

  /// 处理需要下载的情况
  /// proofingId 打样id
  /// isExpectedPathExists 是否是预期路径
  /// effectCode 效果码
  /// targetPath 目标路径
  /// largrResultPhotoUrl 别人做好的高清大图
  Future<bool> _handleNeedDownload(
      String projectId,
      bool isExpectedPathExists,
      String targetPath,
      String? largeResultPhotoUrl,
      ExportPathInfo? exportPathInfo) async {
    final checkDownloadQueueResult =
        await _aigcMySampleExportManager.hasDownloadablePendingFiles(
            projectId, largeResultPhotoUrl, targetPath, exportPathInfo);
    // 检查任务是否在下载队列中
    final isInTaskQueue = checkDownloadQueueResult.isInTaskQueue;
    PGLog.d(
        'AIGC导出下载队列检查结果: 需要下载: ${checkDownloadQueueResult.needDownload}, 是否在下载队列中: $isInTaskQueue');
    if (checkDownloadQueueResult.needDownload && !isInTaskQueue) {
      // 只用提示到项目文件夹层
      final projectFloder = Directory(path.dirname(targetPath));
      final downloadContent = !isExpectedPathExists
          ? '预期路径丢失，是否需要导出到${projectFloder.path}？'
          : '文件中有尚未下载的效果，是否需要下载？';
      // 显示二次确认弹窗
      UniversalDialog.show(
        title: '文件下载确认',
        content: downloadContent,
        confirmText: '确认',
        cancelText: '取消',
        onConfirm: () async {
          UniversalDialog.hide();
          // 下载别人的,一定是打样详情，一定要传大图地址
          if (checkDownloadQueueResult.isOtherAuthor) {
            if (largeResultPhotoUrl != null && largeResultPhotoUrl.isNotEmpty) {
              // 添加到下载队列
              await _aigcMySampleExportManager.addToDownloadQueue(
                  checkDownloadQueueResult.needDownLoadTasks ?? []);
              // 下载别人的需要提示
              PGDialog.showToast('文件正在下载中,请稍后点击查看');
            } else {
              // 别人的打样项目，没有原图URL，无法下载
              PGDialog.showToast('等待创建者上传原图，无法下载');
            }
          } else {
            // 添加到下载队列(自己的打样项目，有记录，只需要打样id)
            await _aigcMySampleExportManager.addToDownloadQueue(
                checkDownloadQueueResult.needDownLoadTasks ?? []);
          }
        },
        onCancel: () async {
          UniversalDialog.hide();
          // 不是预期的路径，用户不下载则删除目标路径
          if (!isExpectedPathExists || exportPathInfo != null) {
            // 如果原图路径丢失或者打样详情，则删除目标路径，如果是空文件夹
            await checkAndDeleteEmptyFolder(targetPath);
            PGLog.d('不是预期的路径或者打样详情，用户不下载则删除目标路径: $targetPath');
          }
          // 不管是否预期的路径，在导出面板用户可以不下载则只是打开文件夹
          if (exportPathInfo == null) {
            // 导出面板，才打开路径还是可以查看，如果打开失败才提示
            final success = await _filePathService.openFolder(targetPath);
            if (!success) {
              PGDialog.showToast('打开导出文件夹失败');
            }
          }
        },
      );
    } else if (checkDownloadQueueResult.needDownload && isInTaskQueue) {
      // 如果需要下载，但是任务已经在下载队列中，则提示
      PGDialog.showToast('文件正在下载中,请稍后点击查看');
    }
    return checkDownloadQueueResult.needDownload;
  }

  /// 检查并删除空文件夹
  Future<void> checkAndDeleteEmptyFolder(String folderPath) async {
    try {
      final directory = Directory(path.dirname(folderPath));
      if (directory.existsSync()) {
        final contents = directory.listSync();
        if (contents.isEmpty) {
          await directory.delete();
          PGLog.d('删除空文件夹: $folderPath');
        } else {
          PGLog.d('文件夹不为空，不删除: $folderPath');
        }
      } else {
        PGLog.d('文件夹不存在: $folderPath');
      }
    } catch (e) {
      PGLog.e('检查并删除空文件夹失败: $e');
    }
  }
}
