import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/export_records_repository.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/use_case/export_use_case_provider.dart';
import 'package:turing_art/ui/export_result/view_model/export_view_model.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_item.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

@Deprecated("该类已废弃，请使用ExportListDialog2实现相关功能")
class ExportListDialog extends StatefulWidget {
  final ExportType? defaultExportType;

  const ExportListDialog({super.key, this.defaultExportType});

  /// 显示导出历史弹窗
  static void show({ExportType? defaultExportType}) {
    if (PGDialog.isDialogVisible(DialogTags.exportList)) {
      PGLog.d(
          'PGDialog showCustomDialog tag: ${DialogTags.exportList}, already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: 900,
      height: 648,
      needBlur: false,
      needGesture: false,
      tag: DialogTags.exportList,
      child: ExportListDialog(defaultExportType: defaultExportType),
    );
  }

  /// 在Unity上显示导出列表弹窗
  static Future<void> showOnUnity() async {
    if (PGDialog.isDialogVisible(DialogTags.exportList)) {
      PGLog.d('ExportListDialog showOnUnity, but dialog already exist, return');
      return;
    }
    await PGDialog.showCustomDialogOnUnity(
      width: 900,
      height: 648,
      needBlur: false,
      tag: DialogTags.exportList,
      child: const ExportListDialog(),
    );
  }

  @override
  State<ExportListDialog> createState() => _ExportListDialogState();
}

class _ExportListDialogState extends State<ExportListDialog> {
  // 常量定义
  static const double _exportTypeSelectorWidth = 126.0; // 导出类型选择器宽度
  static const double _dropdownItemHeight = 28.0; // 下拉菜单项高度

  bool _isExporting = true; // 默认显示"正在导出"标签页
  bool _hasAutoSwitched = false; // 标记是否已经执行过自动切换逻辑
  // 为两个列表分别创建独立的选择集合
  final Set<String> _exportingSelectedItems = {}; // 存储"正在导出"列表的选中项GUID
  final Set<String> _completedSelectedItems = {}; // 存储"导出完成"列表的选中项GUID
  // 存储每个项目的悬停状态
  final Map<String, bool> _hoveredItems = {};
  ExportViewModel? viewModel;
  // 添加两个ScrollController，分别用于两个标签页
  final ScrollController _exportingScrollController = ScrollController();
  final ScrollController _completedScrollController = ScrollController();

  bool _isExportTypeDropdownVisible = false; // 导出类型下拉菜单是否可见
  final LayerLink _layerLink = LayerLink(); // 用于下拉菜单定位
  OverlayEntry? _overlayEntry; // 下拉菜单的overlay入口

  @override
  void dispose() {
    // 释放控制器资源
    _exportingScrollController.dispose();
    _completedScrollController.dispose();
    // 清理overlay，但不调用setState
    _cleanupOverlay();
    super.dispose();
  }

  // 获取当前活动的选择集合
  Set<String> get _activeSelectedItems =>
      _isExporting ? _exportingSelectedItems : _completedSelectedItems;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
        create: (_) => ExportViewModel(
              ExportUseCaseProvider(
                unityController: context.read<UnityController>(),
              ),
              context.read<AigcMySampleExportManager>(),
              context.read<CurrentUserRepository>(),
              context.read<ExportRecordsRepository>(),
              defaultExportType: widget.defaultExportType,
            ),
        child: OrientationBuilder(builder: (context, orientation) {
          return Container(
            width: 900,
            height: 648,
            decoration: BoxDecoration(
              color: const Color(0xFF1E1E1E),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: Consumer<ExportProjectProvider>(
                      builder: (context, exportProjectProvider, _) {
                        final viewModel = context.read<ExportViewModel>();
                        viewModel.updateExportRecords(
                          exportProjectProvider.exportProjectRecords,
                        );

                        // 检查是否需要刷新列表
                        if (exportProjectProvider.isReloadProjectRecords) {
                          // 重置刷新标志
                          exportProjectProvider.invokeReloadProjectRecords(
                              needReload: false);
                          // 调用刷新方法
                          Future.microtask(
                              () => viewModel.handleReloadExportItems());
                        }
                        return Consumer<ExportViewModel>(
                          builder: (context, viewModel, _) {
                            this.viewModel = viewModel;

                            // 根据当前选择的导出类型选择不同的数据源
                            final List<ExportUiStatus> dataSource =
                                viewModel.selectedExportType == ExportType.aigc
                                    ? viewModel.aigcItems
                                    : viewModel.exportUiStatus;

                            // 首先根据导出类型过滤数据，然后根据当前标签页过滤数据
                            final typeFilteredItems = dataSource
                                .where((status) =>
                                    status.exportType ==
                                    viewModel.selectedExportType)
                                .toList();

                            final exportingItems = typeFilteredItems
                                .where((status) =>
                                    status.state != ExportState.finish)
                                .toList();

                            final completedItems = typeFilteredItems
                                .where((status) =>
                                    status.state == ExportState.finish)
                                .toList();

                            // 当"正在导出"数据为空且"导出完成"有数据时，仅在首次打开时自动切换到"导出完成"标签
                            if (!_hasAutoSwitched &&
                                _isExporting &&
                                exportingItems.isEmpty &&
                                viewModel.isFirstLoaded) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (mounted) {
                                  setState(() {
                                    _isExporting = false;
                                    _hasAutoSwitched = true;
                                  });
                                }
                              });
                            }

                            // 当前标签页的数据是否为空
                            final bool isCurrentListEmpty = _isExporting
                                ? exportingItems.isEmpty
                                : completedItems.isEmpty;

                            // 如果当前列表为空，只显示空视图
                            if (isCurrentListEmpty) {
                              return Column(
                                children: [
                                  _buildBatchOperations(context, viewModel,
                                      forEmpty: true),
                                  const SizedBox(height: 16),
                                  Expanded(
                                    child: _buildEmptyView(),
                                  ),
                                  const SizedBox(height: 32),
                                ],
                              );
                            }

                            // 否则显示完整列表界面，包括批量操作和表头
                            return Column(
                              children: [
                                _buildBatchOperations(context, viewModel),
                                _isExporting
                                    ? _buildExportingListHeader(viewModel)
                                    : _buildCompletedListHeader(),
                                Expanded(
                                  child: IndexedStack(
                                    index: _isExporting ? 0 : 1,
                                    children: [
                                      // 正在导出的列表
                                      _buildExportingList(
                                          exportingItems, viewModel),
                                      // 导出完成的列表
                                      _buildCompletedList(
                                          completedItems, viewModel),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        }));
  }

  Widget _buildHeader() {
    return Container(
      height: 40,
      color: const Color(0xFF171717),
      child: Stack(
        children: [
          // 分段按钮
          Center(
            child: Container(
              width: 246,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF171717),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  _buildSegmentButton(true, "正在导出"),
                  _buildSegmentButton(false, "导出完成"),
                ],
              ),
            ),
          ),
          // 关闭按钮
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () => _closeDialog(),
              child: Container(
                width: 40,
                height: 40,
                alignment: Alignment.center,
                child: Image.asset(
                  "assets/icons/home_window_close.png",
                  width: 16,
                  height: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSegmentButton(bool isExporting, String text) {
    final isSelected = _isExporting == isExporting;

    return GestureDetector(
      onTap: () {
        if (!isSelected && mounted) {
          setState(() {
            _isExporting = isExporting;
            _hasAutoSwitched = true; // 用户手动切换后，标记已切换，避免后续自动切换
            // 不再清空选中项，保留每个列表的选择状态
          });
        }
      },
      child: Container(
        width: 123,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF1E1E1E) : const Color(0xFF171717),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontFamily: "PingFang SC",
              fontSize: 14,
              fontWeight: FontWeight.w400,
              height: 20 / 14,
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.5),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBatchOperations(BuildContext context, ExportViewModel viewModel,
      {bool forEmpty = false}) {
    return FutureBuilder<bool>(
      future: context.read<AigcEntranceManager>().isAigcUser(),
      builder: (context, snapshot) {
        final bool isAigcUser = snapshot.data ?? false;
        return Container(
          height: 60,
          color: const Color(0xFF1E1E1E),
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: Row(
            children: [
              if (isAigcUser) ...[
                // 导出类型选择器
                CompositedTransformTarget(
                  link: _layerLink,
                  child: GestureDetector(
                    onTap: _toggleExportTypeDropdown,
                    child: Container(
                      width: _exportTypeSelectorWidth,
                      height: 32,
                      decoration: BoxDecoration(
                        color: const Color(0xFF2B2B2B),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: const Color(0xFFFFFFFF).withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _getExportTypeDisplayName(
                                viewModel.selectedExportType),
                            style: const TextStyle(
                              fontFamily: "PingFang SC",
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 32),
                          Transform.rotate(
                            angle: _isExportTypeDropdownVisible ? 3.14159 : 0,
                            child: const Icon(
                              Icons.keyboard_arrow_down,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ] else ...[
                const SizedBox(),
              ],
              const SizedBox(width: 16),
              if (!forEmpty) ...[
                // 选择计数（根据当前标签页显示对应的选择数量）
                if (_activeSelectedItems.isNotEmpty)
                  Text(
                    "选择${_activeSelectedItems.length}项",
                    style: const TextStyle(
                      fontFamily: "PingFang SC",
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  )
                else
                  const SizedBox(),

                const Spacer(),

                // 批量操作按钮组
                Row(
                  children: [
                    if (_isExporting &&
                        viewModel.selectedExportType ==
                            ExportType.retouching) ...[
                      _buildBatchButton(
                          _activeSelectedItems.isNotEmpty ? "暂停所选" : "暂停全部",
                          "assets/icons/export_pause.png", () {
                        _batchPauseAll();
                      }),
                      const SizedBox(width: 8),
                      _buildBatchButton(
                          _activeSelectedItems.isNotEmpty ? "开始所选" : "开始全部",
                          "assets/icons/export_continue.png", () {
                        _batchStartAll();
                      }),
                      const SizedBox(width: 8),
                    ],
                    if (!(viewModel.selectedExportType == ExportType.aigc &&
                        _isExporting)) ...[
                      _buildBatchButton(
                          _activeSelectedItems.isNotEmpty ? "删除所选" : "删除全部",
                          "assets/icons/export_delete.png", () {
                        _batchDeleteAll();
                      }),
                    ] else ...[
                      const SizedBox(),
                    ],
                  ],
                ),
              ] else ...[
                const SizedBox(),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildBatchButton(String text, String iconPath, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 88,
        height: 24,
        decoration: BoxDecoration(
          color: const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              iconPath,
              width: 16,
              height: 16,
            ),
            const SizedBox(width: 4),
            Text(
              text,
              style: const TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFF909191),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExportingListHeader(ExportViewModel viewModel) {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 32),
      decoration: const BoxDecoration(
        color: Color(0xFF1E1E1E),
        border: Border(
          bottom: BorderSide(
            color: Color(0x1AFFFFFF), // 白色透明度10%
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 如果导出类型为AIGC且正在导出，则不显示选择框
          if (!(viewModel.selectedExportType == ExportType.aigc &&
              _isExporting)) ...[
            SizedBox(
              width: 16,
              child: GestureDetector(
                onTap: _toggleSelectAll,
                child: Image.asset(
                  _isAllSelected()
                      ? "assets/icons/list_select.png"
                      : "assets/icons/list_unselect.png",
                  width: 16,
                  height: 16,
                ),
              ),
            ),
          ] else ...[
            const SizedBox(width: 16),
          ],
          const SizedBox(
            width: 8,
          ),
          const SizedBox(
            width: 276,
            child: Text(
              "项目名称",
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(
            width: 31,
          ),
          const SizedBox(
            width: 58,
            child: Text(
              "导出张数",
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(
            width: 58,
          ),
          const SizedBox(
            width: 57,
            child: Text(
              "导出状态",
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(
            width: 255,
          ),
          const SizedBox(
            width: 30,
            child: Text(
              "操作",
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletedListHeader() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 32),
      decoration: const BoxDecoration(
        color: Color(0xFF1E1E1E),
        border: Border(
          bottom: BorderSide(
            color: Color(0x1AFFFFFF), // 白色透明度10%
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            child: GestureDetector(
              onTap: _toggleSelectAll,
              child: Image.asset(
                _isAllSelected()
                    ? "assets/icons/list_select.png"
                    : "assets/icons/list_unselect.png",
                width: 16,
                height: 16,
              ),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          const SizedBox(
            width: 276,
            child: Text(
              "项目名称",
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(
            width: 31,
          ),
          const SizedBox(
            width: 58,
            child: Text(
              "导出张数",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(
            width: 117,
          ),
          const SizedBox(
            width: 58,
            child: Text(
              "导出时间",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(
            width: 163,
          ),
          const SizedBox(
            width: 30,
            child: Text(
              "操作",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportingList(
      List<ExportUiStatus> items, ExportViewModel viewModel) {
    return RawScrollbar(
      controller: _exportingScrollController,
      thumbVisibility: true,
      thickness: 6.0,
      radius: const Radius.circular(3.0),
      thumbColor: const Color(0x66FFFFFF),
      child: ListView.builder(
        controller: _exportingScrollController,
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          // 传递对应的选择集合
          return _buildExportItem(item, viewModel, _exportingSelectedItems);
        },
      ),
    );
  }

  Widget _buildCompletedList(
      List<ExportUiStatus> items, ExportViewModel viewModel) {
    return RawScrollbar(
      controller: _completedScrollController,
      thumbVisibility: true,
      thickness: 6.0,
      radius: const Radius.circular(3.0),
      thumbColor: const Color(0x66FFFFFF),
      child: ListView.builder(
        controller: _completedScrollController,
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          // 传递对应的选择集合
          return _buildExportItem(item, viewModel, _completedSelectedItems);
        },
      ),
    );
  }

  Widget _buildExportItem(ExportUiStatus item, ExportViewModel viewModel,
      Set<String> selectedItems) {
    final isSelected = selectedItems.contains(item.guid);
    final isHovered = _hoveredItems[item.guid] ?? false;

    return MouseRegion(
      onEnter: (_) {
        if (mounted) {
          setState(() {
            _hoveredItems[item.guid] = true;
          });
        }
      },
      onExit: (_) {
        if (mounted) {
          setState(() {
            _hoveredItems[item.guid] = false;
          });
        }
      },
      child: ExportListItem(
        item: item,
        isSelected: isSelected,
        isHovered: isHovered,
        isExporting: _isExporting,
        onSelectChanged: (selected) {
          if (mounted) {
            setState(() {
              if (selected) {
                selectedItems.add(item.guid);
              } else {
                selectedItems.remove(item.guid);
              }
            });
          }
        },
        onDelete: () async {
          if (item.exportType == ExportType.aigc) {
            AigcPcDeleteConfirmDialog.show(
              context,
              title: '确定删除此导出记录吗？',
              content: '删除后将无法找回。',
              onConfirm: () async {
                if (mounted) {
                  PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
                  await viewModel.onDeleteClick(item.guid);
                  setState(() {
                    selectedItems.remove(item.guid);
                    _hoveredItems.remove(item.guid);
                  });
                }
              },
            );
          } else {
            await viewModel.onDeleteClick(item.guid);
            setState(() {
              selectedItems.remove(item.guid);
              _hoveredItems.remove(item.guid);
            });
          }
        },
        onSearch: () async {
          await viewModel.onSearchClick(item.guid);
          if (mounted) {
            setState(() {
              // 触发重绘，但不改变悬停状态
            });
          }
        },
        onPause: (guid) async {
          await viewModel.onPauseClick(guid);
          if (mounted) {
            setState(() {
              // 强制刷新UI，确保按钮状态立即更新
            });
          }
        },
        onContinue: (guid) async {
          await viewModel.onContinueClick(guid);
          if (mounted) {
            setState(() {
              // 强制刷新UI，确保按钮状态立即更新
            });
          }
        },
        onRefresh: () async {
          await viewModel.onRefreshClick(item.guid);
          if (mounted) {
            setState(() {});
          }
        },
        onN8Layout: () async {
          // 处理N8排版按钮点击
          await viewModel.onN8LayoutClick(item.guid);
          if (mounted) {
            setState(() {});
          }
        },
      ),
    );
  }

  Widget _buildEmptyView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          "assets/icons/export_list_empty.png",
          width: 94,
          height: 94,
        ),
        const Text(
          "暂无导出项目",
          style: TextStyle(
            fontFamily: "PingFang SC",
            fontSize: 14,
            fontWeight: FontWeight.w600,
            height: 18 / 14,
            color: Color(0x80FFFFFF), // 白色透明度50%
          ),
        ),
      ],
    );
  }

  void _closeDialog() {
    // 先保存对provider的引用，避免在组件销毁后访问context
    _clearExportingSelectedItems();

    // 关闭对话框
    PGDialog.dismiss(tag: DialogTags.exportList);
  }

  void _clearExportingSelectedItems() {
    // 先保存对provider的引用，避免在组件销毁后访问context
    final exportProjectProvider =
        mounted ? context.read<ExportProjectProvider>() : null;
    // 如果provider存在，则清除记录
    exportProjectProvider?.clearExportProjectRecords();
  }

  void _toggleSelectAll() {
    // 根据当前选择的导出类型选择正确的数据源
    final List<ExportUiStatus> dataSource =
        viewModel?.selectedExportType == ExportType.aigc
            ? viewModel?.aigcItems ?? []
            : viewModel?.exportUiStatus ?? [];

    final filteredStatus = dataSource.where((status) {
      // 首先根据导出类型过滤
      if (status.exportType != viewModel?.selectedExportType) {
        return false;
      }
      // 然后根据当前标签页过滤
      if (_isExporting) {
        return status.state != ExportState.finish;
      } else {
        return status.state == ExportState.finish;
      }
    }).toList();

    if (mounted) {
      setState(() {
        if (_isAllSelected()) {
          // 清空当前活动的选择集合
          _activeSelectedItems.clear();
        } else {
          // 填充当前活动的选择集合
          _activeSelectedItems.addAll(filteredStatus.map((item) => item.guid));
        }
      });
    }
  }

  bool _isAllSelected() {
    // 根据当前选择的导出类型选择正确的数据源
    final List<ExportUiStatus> dataSource =
        viewModel?.selectedExportType == ExportType.aigc
            ? viewModel?.aigcItems ?? []
            : viewModel?.exportUiStatus ?? [];

    final filteredStatus = dataSource.where((status) {
      // 首先根据导出类型过滤
      if (status.exportType != viewModel?.selectedExportType) {
        return false;
      }
      // 然后根据当前标签页过滤
      if (_isExporting) {
        return status.state != ExportState.finish;
      } else {
        return status.state == ExportState.finish;
      }
    }).toList();

    return filteredStatus.isNotEmpty &&
        filteredStatus
            .every((item) => _activeSelectedItems.contains(item.guid));
  }

  void _batchPauseAll() {
    // 如果有选中项，则操作选中项；否则操作全部
    List<String> targetItems = _activeSelectedItems.isEmpty
        ? viewModel?.exportUiStatus
                .where((status) =>
                    status.exportType == viewModel?.selectedExportType &&
                    status.state != ExportState.finish)
                .map((item) => item.guid)
                .toList() ??
            []
        : _activeSelectedItems.toList();

    if (targetItems.isEmpty) {
      return;
    }
    viewModel?.onPauseBatchClick(targetItems);
  }

  void _batchStartAll() {
    // 如果有选中项，则操作选中项；否则操作全部
    List<String> targetItems = _activeSelectedItems.isEmpty
        ? viewModel?.exportUiStatus
                .where((status) =>
                    status.exportType == viewModel?.selectedExportType &&
                    status.state != ExportState.finish)
                .map((item) => item.guid)
                .toList() ??
            []
        : _activeSelectedItems.toList();

    if (targetItems.isEmpty) {
      return;
    }
    viewModel?.onContinueBatchClick(targetItems);
  }

  void _batchDeleteAll() async {
    // 如果有选中项，则操作选中项；否则操作全部
    var items = viewModel?.exportUiStatus;
    if (viewModel?.selectedExportType == ExportType.aigc) {
      items = viewModel?.aigcItems;
    }

    List<String> targetItems = _activeSelectedItems.isEmpty
        ? (_isExporting
            ? items
                    ?.where((status) =>
                        status.exportType == viewModel?.selectedExportType &&
                        status.state != ExportState.finish)
                    .map((item) => item.guid)
                    .toList() ??
                []
            : items
                    ?.where((status) =>
                        status.exportType == viewModel?.selectedExportType &&
                        status.state == ExportState.finish)
                    .map((item) => item.guid)
                    .toList() ??
                [])
        : _activeSelectedItems.toList();

    if (targetItems.isEmpty) {
      return;
    }
    if (viewModel?.selectedExportType == ExportType.aigc) {
      AigcPcDeleteConfirmDialog.show(
        context,
        title: '确定删除所选导出记录吗？',
        content: '删除后将无法找回。',
        onConfirm: () async {
          PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
          await viewModel?.onDeleteBatchClick(targetItems);
        },
      );
    } else {
      await viewModel?.onDeleteBatchClick(targetItems);
    }
    _clearExportingSelectedItems();

    if (mounted) {
      setState(() {
        // 只清空当前活动的选择集合
        _activeSelectedItems.clear();
      });
    }
  }

  /// 切换导出类型下拉菜单
  void _toggleExportTypeDropdown() {
    if (_isExportTypeDropdownVisible) {
      _hideExportTypeDropdown();
    } else {
      _showExportTypeDropdown();
    }
  }

  /// 显示导出类型下拉菜单
  void _showExportTypeDropdown() {
    if (_overlayEntry != null) {
      return;
    }
    // 计算下拉菜单的高度：类型个数 * 28 + 上下边距12
    final dropdownHeight = ExportType.values.length * _dropdownItemHeight + 12;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: _hideExportTypeDropdown, // 点击外部区域关闭下拉菜单
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.transparent,
          child: Stack(
            children: [
              Positioned(
                width: _exportTypeSelectorWidth, // 与选择器宽度一致
                height: dropdownHeight, // 根据类型个数计算高度
                child: CompositedTransformFollower(
                  link: _layerLink,
                  showWhenUnlinked: false,
                  offset: const Offset(0, 34),
                  child: GestureDetector(
                    onTap: () {
                      // 阻止事件冒泡到外层
                    },
                    child: Material(
                      color: Colors.transparent,
                      child: Container(
                        width: _exportTypeSelectorWidth, // 确保容器宽度与选择器一致
                        height: dropdownHeight, // 确保容器高度正确
                        decoration: BoxDecoration(
                          color: const Color(0xFF2B2B2B),
                          borderRadius: BorderRadius.circular(4),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 6), // 上下6px边距
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: ExportType.values.map((type) {
                              return GestureDetector(
                                onTap: () => _selectExportType(type),
                                child: Container(
                                  width: _exportTypeSelectorWidth,
                                  height: _dropdownItemHeight, // 固定每个类型的高度为28
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  decoration: BoxDecoration(
                                    color: viewModel?.selectedExportType == type
                                        ? const Color(0xFFFFFFFF)
                                            .withOpacity(0.2)
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    children: [
                                      Text(
                                        _getExportTypeDisplayName(type),
                                        style: TextStyle(
                                          fontFamily: "PingFang SC",
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          color: viewModel
                                                      ?.selectedExportType ==
                                                  type
                                              ? const Color(0xFFFFFFFF)
                                              : Colors.white.withOpacity(0.5),
                                        ),
                                      ),
                                      const Spacer(),
                                      if (viewModel?.selectedExportType == type)
                                        const Icon(
                                          Icons.check,
                                          size: 16,
                                          color: Color(0xFFFFFFFF),
                                        ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isExportTypeDropdownVisible = true;
    });
  }

  /// 清理overlay（不调用setState）
  void _cleanupOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    // 不调用setState，因为组件可能正在被销毁
  }

  /// 隐藏导出类型下拉菜单
  void _hideExportTypeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    if (mounted) {
      setState(() {
        _isExportTypeDropdownVisible = false;
      });
    }
  }

  /// 选择导出类型
  void _selectExportType(ExportType type) {
    setState(() {
      viewModel?.selectedExportType = type;
    });
    _hideExportTypeDropdown();
    // 清空当前选择，因为切换类型后数据会改变
    _activeSelectedItems.clear();
  }

  /// 获取导出类型的显示名称
  String _getExportTypeDisplayName(ExportType type) {
    switch (type) {
      case ExportType.retouching:
        return "精修导出";
      case ExportType.aigc:
        return "AI智能增强";
    }
  }
}
