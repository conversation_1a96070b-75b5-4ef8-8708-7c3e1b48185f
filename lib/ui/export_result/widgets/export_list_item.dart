import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/widgets/export_status_widget.dart';

class ExportListItem extends StatelessWidget {
  final ExportUiStatus item;
  final bool isSelected;
  final bool isHovered;
  final ValueChanged<bool> onSelectChanged;
  final VoidCallback onDelete;
  final VoidCallback onSearch;
  final Function(String)? onPause;
  final Function(String)? onContinue;
  final VoidCallback? onRefresh;
  final VoidCallback? onN8Layout;
  final bool isExporting;

  const ExportListItem({
    super.key,
    required this.item,
    required this.isSelected,
    required this.isHovered,
    required this.onSelectChanged,
    required this.onDelete,
    required this.onSearch,
    required this.isExporting,
    this.onPause,
    this.onContinue,
    this.onRefresh,
    this.onN8Layout,
  });

  // 构建操作按钮列表
  List<Widget> _buildActionButtons() {
    List<Widget> buttons = [];

    // 暂停/继续按钮（仅在导出中状态显示）
    if (isExporting &&
        item.state == ExportState.error &&
        item.exportType == ExportType.aigc) {
      buttons.add(
        GestureDetector(
          onTap: onRefresh,
          child: Container(
            width: 16,
            height: 16,
            alignment: Alignment.center,
            child: Image.asset(
              "assets/icons/export_refresh.png",
              width: 16,
              height: 16,
            ),
          ),
        ),
      );
    }

    if (isExporting && item.exportType != ExportType.aigc) {
      buttons.add(
        GestureDetector(
          onTap: () async {
            if (item.state == ExportState.pause ||
                item.state == ExportState.error ||
                item.state == ExportState.ioError) {
              onContinue?.call(item.guid);
            } else {
              onPause?.call(item.guid);
            }
          },
          child: Container(
            width: 16,
            height: 16,
            alignment: Alignment.center,
            child: Image.asset(
              item.state == ExportState.pause ||
                      item.state == ExportState.error ||
                      item.state == ExportState.ioError
                  ? "assets/icons/export_continue.png"
                  : "assets/icons/export_pause.png",
              width: 16,
              height: 16,
            ),
          ),
        ),
      );
    }

    // 查看按钮
    buttons.add(
      GestureDetector(
        onTap: onSearch,
        child: Container(
          width: 16,
          height: 16,
          alignment: Alignment.center,
          child: Image.asset(
            "assets/icons/export_search.png",
            width: 16,
            height: 16,
          ),
        ),
      ),
    );

    // 删除按钮
    if (!(item.exportType == ExportType.aigc && isExporting)) {
      buttons.add(
        GestureDetector(
          onTap: onDelete,
          child: Container(
            width: 16,
            height: 16,
            alignment: Alignment.center,
            child: Image.asset(
              "assets/icons/export_delete.png",
              width: 16,
              height: 16,
            ),
          ),
        ),
      );
    }

    // N8排版按钮 - 只在导出完成页面时占据位置，且根据showN8Button字段决定是否显示
    if (!isExporting) {
      buttons.add(
        (onN8Layout != null && item.showN8Button)
            ? GestureDetector(
                onTap: onN8Layout,
                child: Container(
                  width: 46,
                  height: 24,
                  alignment: Alignment.center,
                  child: Image.asset(
                    "assets/icons/ic_export_n8.png",
                    width: 46,
                    height: 24,
                  ),
                ),
              )
            : const SizedBox(
                width: 46,
                height: 24,
                // 透明占位符，占据位置但不可见
              ),
      );
    }

    // 在按钮之间添加20dp间距
    List<Widget> buttonsWithSpacing = [];
    for (int i = 0; i < buttons.length; i++) {
      buttonsWithSpacing.add(buttons[i]);
      if (i < buttons.length - 1) {
        buttonsWithSpacing.add(const SizedBox(width: 20));
      }
    }

    return buttonsWithSpacing;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 32),
      decoration: BoxDecoration(
        color: isHovered ? Colors.white.withOpacity(0.05) : Colors.transparent,
        border: const Border(
          bottom: BorderSide(
            color: Color(0x1AFFFFFF),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 选择框
          if (!(item.exportType == ExportType.aigc && isExporting)) ...[
            SizedBox(
              width: 16,
              child: GestureDetector(
                onTap: () => onSelectChanged(!isSelected),
                child: Image.asset(
                  isSelected
                      ? "assets/icons/list_select.png"
                      : "assets/icons/list_unselect.png",
                  width: 16,
                  height: 16,
                ),
              ),
            ),
          ] else ...[
            const SizedBox(width: 16),
          ],

          const SizedBox(width: 8),

          // 项目名称 - 减少宽度以避免overflow
          SizedBox(
            width: 220,
            child: Text(
              item.projectName,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: isHovered
                    ? Colors.white.withOpacity(0.85)
                    : Colors.white.withOpacity(0.5),
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 1,
            ),
          ),
          const SizedBox(width: 24),

          // 导出张数 - 数字始终与标题左对齐
          SizedBox(
            width: 100,
            child: Row(
              children: [
                // 小样标识区域 - 固定宽度
                SizedBox(
                  width: 42, // 给小样标识预留的宽度
                  child: item.isSample
                      ? Image.asset(
                          "assets/icons/ic_export_sample.png",
                          width: 32,
                          height: 16,
                        )
                      : null, // 没有小样标识时为空，但仍然占据空间
                ),
                // 导出张数 - 始终在固定位置
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "${item.successCount}/${item.totalCount}",
                      style: TextStyle(
                        fontFamily: "PingFang SC",
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: isHovered
                            ? Colors.white.withOpacity(0.85)
                            : Colors.white.withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 40),

          // 状态区域 - 减少宽度
          SizedBox(
            width: 200,
            child: ExportStatusWidget(
              item: item,
              isHovered: isHovered,
              isExporting: isExporting,
            ),
          ),
          const SizedBox(width: 16),

          const SizedBox(width: 44),

          // 操作按钮区域 - 使用Expanded来避免overflow
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // 使用辅助方法构建按钮列表
                ..._buildActionButtons(),
              ],
            ),
          ),
          // 如果是AIGC的正在导出，增加部分边距，保证操作按钮位置正确
          if (isExporting && item.exportType == ExportType.aigc)
            const SizedBox(width: 32),
        ],
      ),
    );
  }
}
