import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';

/// 导出状态组件
class ExportStatusWidget extends StatelessWidget {
  final ExportUiStatus item;
  final bool isHovered;
  final bool isExporting;

  const ExportStatusWidget({
    super.key,
    required this.item,
    required this.isHovered,
    required this.isExporting,
  });

  @override
  Widget build(BuildContext context) {
    if (isExporting) {
      return _buildExportingStatus();
    } else {
      return _buildCompletedStatus();
    }
  }

  /// 构建正在导出的状态显示
  Widget _buildExportingStatus() {
    // 根据导出类型显示不同的状态
    switch (item.exportType) {
      case ExportType.retouching:
        return _buildRetouchingStatus();
      case ExportType.aigc:
        return _buildAigcStatus();
    }
  }

  /// 构建精修导出状态
  Widget _buildRetouchingStatus() {
    String statusText = item.failedReason ?? "";
    Color statusColor = Colors.white;

    switch (item.state) {
      case ExportState.processing:
        statusText = "正在导出";
        statusColor = Colors.white;
        break;
      case ExportState.error:
      case ExportState.ioError:
        statusText = "导出失败";
        statusColor = const Color(0xFFE0694F);
        break;
      case ExportState.pause:
      case ExportState.pausing:
        statusText = "已暂停";
        statusColor = Colors.white;
        break;
      case ExportState.wait:
        statusText = "等待中";
        statusColor = Colors.white;
        break;
      default:
        statusText = "未知状态";
        statusColor = Colors.white;
    }

    return Row(
      children: [
        if (item.state == ExportState.processing ||
            item.state == ExportState.pause)
          SizedBox(
            width: 140, // 减少进度条宽度以适应新的布局
            child: ClipRRect(
              borderRadius: BorderRadius.circular(2),
              child: LinearProgressIndicator(
                value: item.progress.value.isFinite ? item.progress.value : 0.0,
                backgroundColor: const Color(0xFF333333),
                valueColor:
                    const AlwaysStoppedAnimation<Color>(Color(0xFFFF5C8A)),
                minHeight: 4,
              ),
            ),
          ),
        if (item.state == ExportState.processing ||
            item.state == ExportState.pause)
          const SizedBox(width: 8), // 增加进度条和百分比之间的间距
        if (item.state == ExportState.processing ||
            item.state == ExportState.pause)
          SizedBox(
            width: 50, // 略微减少百分比区域宽度
            child: Text(
              item.state == ExportState.processing
                  ? "${(item.progress.value.isFinite ? (item.progress.value * 100).toInt() : 0)}%"
                  : statusText,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: isHovered
                    ? const Color(0xFFFFFFFF).withOpacity(0.85)
                    : const Color(0xFFFFFFFF).withOpacity(0.5),
              ),
              textAlign: TextAlign.right,
            ),
          ),
        if (item.state == ExportState.error ||
            item.state == ExportState.ioError ||
            item.state == ExportState.wait)
          SizedBox(
            width: 56,
            child: statusText.isNotEmpty
                ? Text(
                    statusText,
                    style: TextStyle(
                      fontFamily: "PingFang SC",
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: statusColor,
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 1,
                  )
                : const SizedBox(),
          ),
      ],
    );
  }

  /// 构建AIGC导出状态（可以与精修状态有所不同）
  Widget _buildAigcStatus() {
    String statusText = item.failedReason ?? "";
    Color statusColor = Colors.white;

    switch (item.state) {
      case ExportState.processing:
        statusText = "AI超清处理中..."; // AIGC特有的状态文本
        statusColor = const Color(0xFFFFFFFF); // AIGC特有的颜色
        break;
      case ExportState.error:
      case ExportState.ioError:
        statusText = "失败"; // AIGC特有的错误文本
        statusColor = const Color(0xFFFFFFFF).withOpacity(0.7);
        break;
      case ExportState.wait:
        statusText = "排队中"; // AIGC特有的等待状态
        statusColor = const Color(0xFFFFFFFF).withOpacity(0.7);
        break;
      case ExportState.finish:
        statusText = "已完成";
        statusColor = Colors.white;
        break;
      default:
        statusText = "未知状态";
        statusColor = Colors.white;
    }

    return Row(
      children: [
        if (item.state == ExportState.processing ||
            item.state == ExportState.finish)
          SizedBox(
            width: 100,
            child: Text(
              statusText,
              style: TextStyle(
                fontFamily: "PingFang SC",
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: isHovered
                    ? const Color(0xFFFFFFFF).withOpacity(0.85)
                    : const Color(0xFFFFFFFF).withOpacity(0.5),
              ),
              textAlign: TextAlign.left,
            ),
          ),
        if (item.state == ExportState.error ||
            item.state == ExportState.ioError ||
            item.state == ExportState.wait)
          SizedBox(
            width: 100,
            child: statusText.isNotEmpty
                ? Text(
                    statusText,
                    style: TextStyle(
                      fontFamily: "PingFang SC",
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: statusColor,
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 1,
                    textAlign: TextAlign.left,
                  )
                : const SizedBox(),
          ),
      ],
    );
  }

  /// 构建已完成状态显示
  Widget _buildCompletedStatus() {
    String formattedTime = "";
    if (item.successTime != null) {
      final DateTime dateTime =
          DateTime.fromMillisecondsSinceEpoch(item.successTime!);
      formattedTime = DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
    }

    return Text(
      formattedTime,
      style: TextStyle(
        fontFamily: "PingFang SC",
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isHovered
            ? Colors.white.withOpacity(0.85)
            : Colors.white.withOpacity(0.5),
      ),
    );
  }
}
