import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/export_records_repository.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/gradient_progress.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/use_case/export_use_case_provider.dart';
import 'package:turing_art/ui/export_result/view_model/export_view_model.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/screen_util.dart';

class ExportDialog extends StatelessWidget {
  const ExportDialog({super.key});

  /// 显示导出历史弹窗
  static void show() {
    // 和问师确认这种有关闭按钮的出现后，必须点击关闭后才可以操作其他空白处
    if (PGDialog.isDialogVisible(DialogTags.exportList)) {
      PGLog.d(
          'PGDialog showCustomDialog tag: ${DialogTags.exportList}, already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: ScreenUtil().setWidth(800),
      height: ScreenUtil().setHeight(500),
      needBlur: false,
      tag: DialogTags.exportList,
      child: const ExportDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ExportViewModel(
        ExportUseCaseProvider(
          unityController: context.read<UnityController>(),
        ),
        context.read<AigcMySampleExportManager>(),
        context.read<CurrentUserRepository>(),
        context.read<ExportRecordsRepository>(),
      ),
      child: OrientationBuilder(builder: (context, orientation) {
        return Container(
          width: ScreenUtil().setWidth(800),
          height: ScreenUtil().setHeight(500),
          decoration: BoxDecoration(
            color: const Color(0xff121415),
            borderRadius: BorderRadius.circular(12),
            boxShadow: const [
              BoxShadow(
                color: Color(0x33000000),
                offset: Offset(0, 4),
                blurRadius: 40,
              ),
            ],
          ),
          child: Stack(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "导出进度",
                      style: TextStyle(
                        fontWeight: Fonts.semiBold,
                        fontFamily: Fonts.defaultFontFamily,
                        fontSize: 16,
                        color: const Color(0xffFFFFFF),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xff222526),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildTableTitleText("项目名称"),
                          ),
                          _buildTableVerticalDivider(),
                          SizedBox(
                            width: 100,
                            child: _buildTableTitleText("导出张数"),
                          ),
                          _buildTableVerticalDivider(),
                          SizedBox(
                            width: 260,
                            child: _buildTableTitleText("状态"),
                          ),
                          _buildTableVerticalDivider(),
                          SizedBox(
                            width: 152,
                            child: _buildTableTitleText("操作"),
                          ),
                        ],
                      ),
                    ),
                    Consumer<ExportProjectProvider>(
                        builder: (context, exportProjectProvider, _) {
                      final viewModel = context.read<ExportViewModel>();
                      viewModel.updateExportRecords(
                          exportProjectProvider.exportProjectRecords);

                      // 检查是否需要刷新列表
                      if (exportProjectProvider.isReloadProjectRecords) {
                        // 重置刷新标志
                        exportProjectProvider.invokeReloadProjectRecords(
                            needReload: false);
                        // 调用刷新方法
                        Future.microtask(
                            () => viewModel.handleReloadExportItems());
                      }

                      return Consumer<ExportViewModel>(
                          builder: (context, viewModel, _) {
                        if (viewModel.exportUiStatus.isEmpty) {
                          return Center(
                            child: _buildEmptyView(),
                          );
                        } else {
                          return Expanded(
                              child: ScrollConfiguration(
                            behavior: _ScrollBehavior(),
                            child: ScrollbarTheme(
                              data: ScrollbarThemeData(
                                  thumbColor: MaterialStateProperty.all(
                                      const Color.fromRGBO(
                                          235, 237, 245, 0.15)),
                                  thickness: MaterialStateProperty.all(4),
                                  radius: const Radius.circular(14)),
                              child: ListView.builder(
                                  padding: const EdgeInsets.only(top: 4),
                                  itemCount: viewModel.exportUiStatus.length,
                                  itemBuilder: (BuildContext context,
                                          int index) =>
                                      _ExportItemView(
                                        uiStatus:
                                            viewModel.exportUiStatus[index],
                                        onDeleteClick: viewModel.onDeleteClick,
                                        onContinueClick:
                                            viewModel.onContinueClick,
                                        onPauseClick: viewModel.onPauseClick,
                                        onSearchClick: viewModel.onSearchClick,
                                      )),
                            ),
                          ));
                        }
                      });
                    }),
                  ],
                ),
              ),
              Positioned(
                right: 16,
                top: 16,
                width: 30,
                height: 30,
                child: GestureDetector(
                  onTap: () => _closeClosure(context),
                  child: Image.asset("assets/icons/profile_close_dialog.png"),
                ),
              ),
              // const PreloadUnityWidget(),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildTableTitleText(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: Fonts.medium,
          fontSize: 12,
          fontFamily: Fonts.defaultFontFamily,
          color: const Color(0x99EBF2F5),
        ),
      ),
    );
  }

  Widget _buildTableVerticalDivider() {
    return const VerticalDivider(
      width: 0.5,
      color: Color(0xff000000),
    );
  }

  Widget _buildEmptyView() {
    return Container(
      margin: EdgeInsets.only(top: ScreenUtil().setHeight(139)),
      child: Text(
        "暂无导出项目",
        style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontSize: 14,
            fontWeight: Fonts.semiBold,
            color: const Color(0x4DEBF2F5)),
      ),
    );
  }

  void _closeClosure(BuildContext context) {
    if (context.mounted) {
      final exportProjectProvider = context.read<ExportProjectProvider>();
      exportProjectProvider.clearExportProjectRecords();
      PGDialog.dismiss(tag: DialogTags.exportList);
    }
  }
}

class _ExportItemView extends StatefulWidget {
  final ExportUiStatus uiStatus;
  final Function(String)? onPauseClick;
  final Function(String)? onContinueClick;
  final Function(String)? onSearchClick;
  final Function(String)? onDeleteClick;

  const _ExportItemView({
    required this.uiStatus,
    this.onContinueClick,
    this.onSearchClick,
    this.onDeleteClick,
    this.onPauseClick,
  });

  @override
  State<StatefulWidget> createState() => _ExportItemState();
}

class _ExportItemState extends State<_ExportItemView> {
  // Add timestamps to track last click time for each button
  int _lastPauseClickTime = 0;
  int _lastSearchClickTime = 0;
  int _lastDeleteClickTime = 0;

  // Debounce duration in milliseconds
  final int _debounceTime = 500;

  // Helper method to check if enough time has passed since last click
  bool _canClick(int lastClickTime) {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    return currentTime - lastClickTime >= _debounceTime;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 52,
      child: Row(
        children: [
          Expanded(
              child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildText(widget.uiStatus.projectName),
          )),
          Container(
            width: 100,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildText(
                "${widget.uiStatus.successCount}/${widget.uiStatus.totalCount}"),
          ),
          Container(
            width: 260,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    _buildStatusIcon(),
                    const SizedBox(width: 4),
                    _buildStatusText(),
                  ],
                ),
                const SizedBox(width: 8),
                if (widget.uiStatus.state == ExportState.processing ||
                    widget.uiStatus.state == ExportState.pause ||
                    widget.uiStatus.state == ExportState.wait)
                  Expanded(
                      child: GradientProgress(
                    value: widget.uiStatus.progress,
                    animate: false,
                  )),
                const SizedBox(width: 8),
                _buildStatusContentText()
              ],
            ),
          ),
          Container(
            width: 152,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildOptionList(),
          ),
        ],
      ),
    );
  }

  Widget _buildText(String text) {
    return Text(
      text,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        fontWeight: Fonts.regular,
        fontSize: 12,
        fontFamily: Fonts.defaultFontFamily,
        color: const Color(0xFFFFFFFF),
      ),
    );
  }

  Widget _buildStatusIcon() {
    String statusIcon = "";
    switch (widget.uiStatus.state) {
      case ExportState.processing:
        statusIcon = "assets/icons/export_exporting_status.png";
        break;
      case ExportState.error:
      case ExportState.ioError:
        statusIcon = "assets/icons/export_failed_status.png";
        break;
      case ExportState.pause:
      case ExportState.pausing:
      case ExportState.wait:
        statusIcon = "assets/icons/export_pause_status.png";
        break;
      case ExportState.finish:
        statusIcon = "assets/icons/export_done_status.png";
        break;
    }
    return SizedBox(
      width: 16,
      height: 16,
      child: Image.asset(statusIcon),
    );
  }

  Widget _buildStatusText() {
    String text = "";
    Color textColor = const Color(0xFFFFFFFF);
    switch (widget.uiStatus.state) {
      case ExportState.processing:
        textColor = const Color(0xFFFFFFFF);
        text = "正在导出";
        break;
      case ExportState.error:
      case ExportState.ioError:
        textColor = const Color(0xFFE0694F);
        text = "导出失败";
        break;
      case ExportState.pause:
      case ExportState.pausing:
        textColor = const Color(0xFFFFFFFF);
        text = "已暂停";
        break;
      case ExportState.finish:
        textColor = const Color(0x99EBF2F5);
        text = "已完成";
        break;
      case ExportState.wait:
        textColor = const Color(0xFFFFFFFF);
        text = "等待中";
        break;
    }
    return SizedBox(
      width: 50,
      child: Text(
        text,
        style: TextStyle(
            color: textColor,
            fontWeight: Fonts.regular,
            fontFamily: Fonts.defaultFontFamily,
            fontSize: 12),
      ),
    );
  }

  Widget _buildStatusContentText() {
    String content = "";
    switch (widget.uiStatus.state) {
      case ExportState.processing:
      case ExportState.pausing:
      case ExportState.pause:
      case ExportState.wait:
        content = "${(widget.uiStatus.progress.value * 100).toInt()}%";
        break;
      case ExportState.finish:
        content = _timeFormat(widget.uiStatus.successTime ?? 0);
      case ExportState.error:
      case ExportState.ioError:
        content = widget.uiStatus.failedReason ?? "";
    }
    return Container(
      constraints: const BoxConstraints(minWidth: 28),
      child: Text(
        content,
        textAlign: TextAlign.end,
        style: TextStyle(
          color: const Color(0x99EBF2F5),
          fontFamily: Fonts.defaultFontFamily,
          fontWeight: Fonts.regular,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildOptionList() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Visibility(
            visible: widget.uiStatus.state != ExportState.finish,
            maintainState: true,
            maintainAnimation: true,
            maintainSize: true,
            child: GestureDetector(
              onTap: () {
                if (!_canClick(_lastPauseClickTime)) {
                  return;
                }
                _lastPauseClickTime = DateTime.now().millisecondsSinceEpoch;

                if (widget.uiStatus.state == ExportState.pause ||
                    widget.uiStatus.state == ExportState.error ||
                    widget.uiStatus.state == ExportState.ioError ||
                    widget.uiStatus.state == ExportState.pausing) {
                  widget.onContinueClick?.call(widget.uiStatus.guid);
                } else {
                  widget.onPauseClick?.call(widget.uiStatus.guid);
                }
              },
              behavior: HitTestBehavior.opaque,
              child: SizedBox(
                //用来扩大点击区域
                width: 32,
                height: 32,
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: Image.asset(
                      (widget.uiStatus.state == ExportState.pause ||
                              widget.uiStatus.state == ExportState.error ||
                              widget.uiStatus.state == ExportState.ioError)
                          ? "assets/icons/export_continue.png"
                          : "assets/icons/export_pause.png"),
                ),
              ),
            )),
        GestureDetector(
          onTap: () {
            if (!_canClick(_lastSearchClickTime)) {
              return;
            }
            _lastSearchClickTime = DateTime.now().millisecondsSinceEpoch;

            widget.onSearchClick?.call(widget.uiStatus.guid);
          },
          behavior: HitTestBehavior.opaque,
          child: SizedBox(
            //用来扩大点击区域
            width: 32,
            height: 32,
            child: SizedBox(
              width: 20,
              height: 20,
              child: Image.asset("assets/icons/export_search.png"),
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            if (!_canClick(_lastDeleteClickTime)) {
              return;
            }
            _lastDeleteClickTime = DateTime.now().millisecondsSinceEpoch;

            widget.onDeleteClick?.call(widget.uiStatus.guid);
          },
          behavior: HitTestBehavior.opaque,
          child: SizedBox(
            //用来扩大点击区域
            width: 32,
            height: 32,
            child: SizedBox(
              width: 20,
              height: 20,
              child: Image.asset("assets/icons/export_delete.png"),
            ),
          ),
        ),
      ],
    );
  }

  String _timeFormat(int timestamp) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat('yyyy.MM.dd HH:mm').format(dateTime);
  }
}

class _ScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        // etc.
      };
}
