import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/ui/export_result/use_case/fetch_export_report_use_case.dart'; // 导入ExportRecordWithJson
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_log.dart';

// 用于在 isolate 中执行的数据类
class _ExportData {
  final String filePath;

  _ExportData(this.filePath);
}

// 用于在 isolate 中执行的数据类（包含原始JSON）
class _ExportDataWithJson {
  final String filePath;

  _ExportDataWithJson(this.filePath);
}

// 在 isolate 中执行的函数
Future<List<ExportRecord>> _parseExportData(_ExportData data) async {
  try {
    final file = File(data.filePath);
    if (!file.existsSync()) {
      return [];
    }

    final content = await file.readAsString();
    if (content.isEmpty) {
      return [];
    }

    final List<dynamic> jsonList = jsonDecode(content) as List;
    return jsonList
        .map((json) {
          try {
            return ExportRecord.fromJson(json);
          } catch (e) {
            PGLog.e('解析单条记录异常: $e, 数据: ${json.toString()}');
            return null;
          }
        })
        .whereType<ExportRecord>()
        .toList()
        .reversed
        .toList();
  } catch (e) {
    PGLog.e('解析数据异常: $e');
    return [];
  }
}

// 在 isolate 中执行的函数（包含原始JSON）
Future<List<ExportRecordWithJson>> _parseExportDataWithJson(
    _ExportDataWithJson data) async {
  try {
    final file = File(data.filePath);
    if (!file.existsSync()) {
      return [];
    }

    final content = await file.readAsString();
    if (content.isEmpty) {
      return [];
    }

    final List<dynamic> jsonList = jsonDecode(content) as List;
    return jsonList
        .map((json) {
          try {
            final originalJson = json as Map<String, dynamic>;
            final record = ExportRecord.fromJson(originalJson);
            return ExportRecordWithJson(record, originalJson);
          } catch (e) {
            PGLog.e('解析单条记录异常: $e, 数据: ${json.toString()}');
            return null;
          }
        })
        .whereType<ExportRecordWithJson>()
        .toList()
        .reversed
        .toList();
  } catch (e) {
    PGLog.e('解析数据异常: $e');
    return [];
  }
}

class FetchExportReportUseCase2 {
  FetchExportReportUseCase2();

  Future<List<ExportRecord>> invoke() async {
    try {
      final filePath = _getTargetFilePath();
      if (filePath == null) {
        return [];
      }

      // 使用 compute 在 isolate 中执行文件读取和 JSON 解析
      return await compute(_parseExportData, _ExportData(filePath));
    } catch (e) {
      PGLog.e('异常: $e');
      return [];
    }
  }

  /// 获取包含原始JSON数据的ExportRecord列表
  Future<List<ExportRecordWithJson>> invokeWithJson() async {
    try {
      final filePath = _getTargetFilePath();
      if (filePath == null) {
        return [];
      }

      // 使用 compute 在 isolate 中执行文件读取和 JSON 解析
      return await compute(
          _parseExportDataWithJson, _ExportDataWithJson(filePath));
    } catch (e) {
      PGLog.e('异常: $e');
      return [];
    }
  }

  // 新增目标文件路径获取
  String? _getTargetFilePath() {
    final userDir = FileManager().getUserRootDir();
    if (userDir == null) {
      return null;
    }
    return path.join(userDir.path, 'export', 'exportTasks.json');
  }
}
