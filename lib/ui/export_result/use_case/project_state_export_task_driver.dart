import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/ui/export_result/use_case/export_task_events.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 项目状态导出任务驱动器
/// 监听ProjectStateProvider的变化，并驱动ExportTaskEventBus发布相应的事件
class ProjectStateExportTaskDriver extends ChangeNotifier {
  final ProjectStateProvider _projectStateProvider;
  final ProjectRepository _projectRepository;
  final ExportTaskEventBus _eventBus;

  ProjectStateExportTaskDriver({
    required ProjectStateProvider projectStateProvider,
    required ProjectRepository projectRepository,
    required ExportTaskEventBus eventBus,
  })  : _projectStateProvider = projectStateProvider,
        _projectRepository = projectRepository,
        _eventBus = eventBus {
    // 监听ProjectStateProvider的变化
    PGLog.d('ProjectStateExportTaskDriver: 开始添加ProjectStateProvider监听器');
    _projectStateProvider.addListener(_onProjectStateChanged);
    PGLog.d('ProjectStateExportTaskDriver: ProjectStateProvider监听器添加成功');
    PGLog.d('ProjectStateExportTaskDriver 初始化成功');
  }

  /// 项目状态变化回调 - 将状态变化转换为事件
  void _onProjectStateChanged() {
    try {
      PGLog.d('ProjectStateExportTaskDriver: 收到ProjectStateProvider通知');

      final changeType = _projectStateProvider.lastChangeType;
      final projectId = _projectStateProvider.currentProjectId;

      PGLog.d(
          'ProjectStateExportTaskDriver: 变化类型: $changeType, 项目ID: $projectId');

      if (changeType == ProjectStateChangeType.enteringEdit) {
        PGLog.d('ProjectStateExportTaskDriver: 检测到进入编辑状态');
        // 检查是否是AIGC工程，如果是则发布暂停事件
        if (projectId != null) {
          _checkAndPauseForAigcProject(projectId);
        }
      } else if (changeType == ProjectStateChangeType.exitingEdit) {
        PGLog.d('ProjectStateExportTaskDriver: 检测到退出编辑状态');
        // 发布恢复事件
        _eventBus.resumePausedExportTasks(
          source: ExportTaskEventSource.projectStateChange,
          metadata: {
            'projectId': projectId,
            'changeType': changeType.toString(),
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      } else {
        PGLog.d('ProjectStateExportTaskDriver: 未知的变化类型: $changeType');
      }
    } catch (e) {
      PGLog.e('处理项目状态变化失败: $e');
    }
  }

  /// 检查是否为AIGC项目并发布暂停事件
  Future<void> _checkAndPauseForAigcProject(String projectId) async {
    try {
      final project = await _projectRepository.getProjectById(projectId);
      if (project != null && project.projectType == ProjectType.aiGen) {
        PGLog.d('检测到进入AIGC编辑状态，发布暂停导出任务事件');
        await _eventBus.pauseAllExportTasks(
          source: ExportTaskEventSource.projectStateChange,
          metadata: {
            'projectId': projectId,
            'projectType': project.projectType.toString(),
            'reason': 'entering_aigc_edit',
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      } else {
        PGLog.d('进入非AIGC项目，不暂停导出任务');
      }
    } catch (e) {
      PGLog.e('检查项目类型失败: $e');
    }
  }

  @override
  void dispose() {
    try {
      _projectStateProvider.removeListener(_onProjectStateChanged);
      PGLog.d('ProjectStateExportTaskDriver 已销毁');
    } catch (e) {
      PGLog.e('销毁 ProjectStateExportTaskDriver 失败: $e');
    }
    super.dispose();
  }
}
