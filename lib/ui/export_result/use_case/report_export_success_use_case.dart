import 'package:turing_art/datalayer/domain/models/export/export_models.dart';
import 'package:turing_art/datalayer/repository/export_exception.dart';
import 'package:turing_art/datalayer/repository/export_repository.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 报告导出成功用例
///
/// 该用例封装了向服务器报告导出成功的过程。
class ReportExportSuccessUseCase {
  final ExportRepository _exportRepository;

  /// 创建 [ReportExportSuccessUseCase] 实例
  ///
  /// exportRepository 导出仓库，用于报告导出成功
  ReportExportSuccessUseCase(this._exportRepository);

  /// 报告导出成功
  ///
  /// [imagePHash] 图片哈希值
  /// [tokenId] 导出Token ID
  /// [projectName] 项目名称
  /// [projectId] 项目ID
  /// [hostname] 主机名
  /// [exportType] 导出类型，默认为retouch
  ///
  /// 成功返回void，失败抛出 [ExportException]
  Future<void> invoke(
    String imagePHash,
    String tokenId,
    String projectName,
    String projectId,
    String hostname, {
    ExportType exportType = ExportType.retouch,
  }) async {
    try {
      PGLog.d(
          '开始报告导出成功，图片: $imagePHash, TokenID: $tokenId, 导出类型: ${exportType.name}');

      // 构建导出成功图片对象
      final image = ExportCompleteImage(
        imagePHash: imagePHash,
        tokenId: tokenId,
      );

      // 调用仓库方法报告导出成功
      await _exportRepository.reportExportSuccess(
        [image],
        projectName,
        projectId,
        hostname,
        exportType: exportType,
      );

      PGLog.d('导出成功报告完成');
    } catch (e) {
      PGLog.e('报告导出成功失败: $e');
      if (e is ExportException) {
        rethrow;
      }
      throw ExportException('报告导出成功失败: $e');
    }
  }

  /// 批量报告导出成功
  ///
  /// [images] 导出成功的图片列表
  /// [projectName] 项目名称
  /// [projectId] 项目ID
  /// [hostname] 主机名
  /// [exportType] 导出类型，默认为retouch
  ///
  /// 成功返回void，失败抛出 [ExportException]
  Future<void> invokeMultiple(
    List<ExportCompleteImage> images,
    String projectName,
    String projectId,
    String hostname, {
    ExportType exportType = ExportType.retouch,
  }) async {
    try {
      PGLog.d('开始批量报告导出成功，${images.length} 张图片');

      // 调用仓库方法报告导出成功
      await _exportRepository.reportExportSuccess(
        images,
        projectName,
        projectId,
        hostname,
        exportType: exportType,
      );

      PGLog.d('批量导出成功报告完成');
    } catch (e) {
      PGLog.e('批量报告导出成功失败: $e');
      if (e is ExportException) {
        rethrow;
      }
      throw ExportException('批量报告导出成功失败: $e');
    }
  }
}
