import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class DeleteWorkspaceUseCase {
  final UnityController _unityController;

  DeleteWorkspaceUseCase(
    this._unityController,
  );

  Future<void> invoke(List<String> workspaceIds) async {
    try {
      await _unityController.sendMessage(
        MessageToUnity(
          method: 'DeleteWorkspace',
          args: jsonEncode(workspaceIds),
          completed: const Uuid().v4(),
        ),
      );
    } catch (e) {
      PGLog.e('删除工作区失败: $e');
    }
  }
}
