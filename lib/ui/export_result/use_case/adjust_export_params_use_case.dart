import 'package:turing_art/datalayer/domain/models/export/export_models.dart';
import 'package:turing_art/datalayer/repository/export_exception.dart';
import 'package:turing_art/datalayer/repository/export_repository.dart';
import 'package:turing_art/datalayer/service/api/request_header.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 调整导出参数用例
///
/// 该用例封装了调整导出参数的过程，需要Token进行参数调整请求。
class AdjustExportParamsUseCase {
  final ExportRepository _exportRepository;

  /// 创建 [AdjustExportParamsUseCase] 实例
  ///
  /// [_exportRepository] 导出仓库，用于调整参数
  AdjustExportParamsUseCase(this._exportRepository);

  /// 获取当前的PG-Time时间戳
  ///
  /// 返回当前时间戳字符串，用于加解密参数
  String getCurrentPGTime() {
    return RequestHeader.getCurrentTime();
  }

  /// 调整导出参数
  ///
  /// [imagePHash] 图片哈希值
  /// [imageName] 图片名称
  /// [tokenId] 导出Token对象
  /// [paramsAesEncrypted] 加密的参数数据
  /// [lutParamsEncrypted] 加密的LUT参数数据
  /// [customPGTime] 可选参数，用于覆盖请求中的PG-Time值
  ///
  /// 返回调整后的参数，失败抛出 [ExportException]
  Future<GenParamsResponse?> invoke(
    String imagePHash,
    String imageName,
    String tokenId,
    String paramsAesEncrypted,
    String lutParamsEncrypted, {
    String? customPGTime,
  }) async {
    try {
      PGLog.d('开始调整导出参数，图片: $imagePHash，TokenID: $tokenId');

      // 如果提供了自定义PG-Time，则更新RequestHeader中的时间戳
      if (customPGTime != null) {
        _updatePGTime(customPGTime);
      }

      // 构建参数字段列表
      final fields = <ParamField>[
        ParamField(field: 'paramsAesEncrypted', value: paramsAesEncrypted),
        ParamField(field: 'lutParamsEncrypted', value: lutParamsEncrypted),
      ];

      // 调用仓库方法调整参数
      final params = await _exportRepository.adjustPhotoParams(
        tokenId,
        imagePHash,
        fields,
      );

      PGLog.d('参数调整成功: ${params?.fields.length} 个字段');
      return params;
    } catch (e) {
      PGLog.e('参数调整失败: $e');
      if (e is ExportException) {
        rethrow;
      }
      throw ExportException('参数调整失败: $e');
    }
  }

  /// 更新RequestHeader中的PG-Time值
  ///
  /// [customPGTime] 自定义的PG-Time时间戳
  void _updatePGTime(String customPGTime) {
    RequestHeader.updateCustomTime(customPGTime);
  }
}
