import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:uuid/uuid.dart';

class PauseExportTaskUseCase {
  final UnityController _unityController;

  PauseExportTaskUseCase(
    this._unityController,
  );

  Future<void> invoke(List<ExportRecord> records) async {
    final completed = const Uuid().v4();
    final message = MessageToUnity(
      method: 'PauseExportTask',
      args: jsonEncode(records.map((e) => e.toJson()).toList()),
      completed: completed,
    );
    // 等待消息处理完成
    await _unityController.sendMessage(message);
  }

  Future<void> pauseBatchExportTask(List<String> ids) async {
    final args = {
      'action': 'pause',
      'ids': ids,
    };

    final json = jsonEncode(args);
    final message = MessageToUnity(
      method: 'BatchInvokeExport',
      args: json,
      completed: const Uuid().v4(),
    );
    // 等待消息处理完成
    await _unityController.sendMessage(message);
  }
}
