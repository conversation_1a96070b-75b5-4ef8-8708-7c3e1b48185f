import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

/// 恢复指定导出任务的UseCase
///
/// 当用户退出AIGC编辑场景时，需要恢复之前暂停的特定导出任务
class ResumeExportTasksUseCase {
  final UnityController _unityController;

  ResumeExportTasksUseCase(this._unityController);

  /// 执行恢复指定导出任务的操作
  ///
  /// [taskIds] 要恢复的导出任务ID列表
  /// 返回恢复的任务数量
  Future<int> invoke(List<String> taskIds) async {
    try {
      if (taskIds.isEmpty) {
        PGLog.d('没有需要恢复的导出任务');
        return 0;
      }

      PGLog.d('开始恢复指定的导出任务: $taskIds');

      // 批量恢复指定的任务
      await _resumeBatchTasks(taskIds);

      PGLog.d('成功恢复 ${taskIds.length} 个导出任务');
      return taskIds.length;
    } catch (e) {
      PGLog.e('恢复指定导出任务失败: $e');
      rethrow;
    }
  }

  /// 批量恢复任务
  Future<void> _resumeBatchTasks(List<String> taskIds) async {
    final args = {
      'action': 'continue',
      'ids': taskIds,
    };

    final json = jsonEncode(args);
    final message = MessageToUnity(
      method: 'BatchInvokeExport',
      args: json,
      completed: const Uuid().v4(),
    );

    // 等待消息处理完成
    await _unityController.sendMessage(message);
  }
}
