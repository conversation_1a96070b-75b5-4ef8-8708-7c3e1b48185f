import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/ui/export_result/use_case/fetch_export_report_use_case2.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

/// 恢复所有导出任务的UseCase
///
/// 当用户退出精修场景时，需要恢复之前暂停的导出任务
/// 让导出任务继续执行
class ResumeAllExportTasksUseCase {
  final UnityController _unityController;
  final FetchExportReportUseCase2 _fetchExportReport;

  ResumeAllExportTasksUseCase(
    this._unityController,
    this._fetchExportReport,
  );

  /// 执行恢复所有导出任务的操作
  ///
  /// 返回恢复的任务数量
  Future<int> invoke() async {
    try {
      PGLog.d('开始恢复所有导出任务');

      // 获取所有导出任务
      final allTasks = await _fetchExportReport.invoke();

      // 筛选出暂停状态的任务
      final pausedTasks = allTasks.where((task) {
        final state = ExportState.fromCode(task.exportState);
        return state == ExportState.pause;
      }).toList();

      if (pausedTasks.isEmpty) {
        PGLog.d('没有暂停的导出任务需要恢复');
        return 0;
      }

      PGLog.d('找到 ${pausedTasks.length} 个暂停的导出任务，开始恢复');

      // 批量恢复所有暂停的任务
      final taskIds = pausedTasks.map((task) => task.guid).toList();
      await _resumeBatchTasks(taskIds);

      PGLog.d('成功恢复 ${pausedTasks.length} 个导出任务');
      return pausedTasks.length;
    } catch (e) {
      PGLog.e('恢复所有导出任务失败: $e');
      rethrow;
    }
  }

  /// 批量恢复任务
  Future<void> _resumeBatchTasks(List<String> taskIds) async {
    final args = {
      'action': 'continue',
      'ids': taskIds,
    };

    final json = jsonEncode(args);
    final message = MessageToUnity(
      method: 'BatchInvokeExport',
      args: json,
      completed: const Uuid().v4(),
    );

    // 等待消息处理完成
    await _unityController.sendMessage(message);
  }

  /// 模拟恢复操作（用于测试）
  Future<int> invokeMock() async {
    try {
      PGLog.d('模拟恢复所有导出任务');

      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 500));

      // 模拟恢复了3个任务
      const mockResumedCount = 3;
      PGLog.d('模拟成功恢复 $mockResumedCount 个导出任务');

      return mockResumedCount;
    } catch (e) {
      PGLog.e('模拟恢复所有导出任务失败: $e');
      rethrow;
    }
  }
}
