import 'package:turing_art/datalayer/domain/models/export/export_models.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/export_exception.dart';
import 'package:turing_art/datalayer/repository/export_repository.dart';
import 'package:turing_art/datalayer/services/hostname_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 获取导出Token用例
///
/// 该用例封装了获取Token的过程，包括从缓存获取和创建新Token。
class GetExportTokenUseCase {
  final ExportRepository _exportRepository;
  final CurrentUserRepository _currentUserRepository;

  /// 创建 [GetExportTokenUseCase] 实例
  ///
  /// exportRepository 导出仓库，用于获取Token
  /// currentUserRepository 当前用户仓库，用于获取用户ID
  GetExportTokenUseCase(this._exportRepository, this._currentUserRepository);

  /// 获取导出Token
  ///
  /// imagePHash 图片哈希值
  /// imageName 图片名称
  /// projectName 项目名称
  /// projectId 项目ID
  /// exportType 导出类型，默认为retouch
  ///
  /// 返回导出Token，失败抛出 [ExportException]
  Future<ExportToken> invoke(
    String imagePHash,
    String imageName,
    String projectName,
    String projectId, {
    ExportType exportType = ExportType.retouch,
  }) async {
    try {
      // 获取当前用户ID
      final userId = _currentUserRepository.user?.effectiveId ?? 'default_user';

      PGLog.d(
          '开始获取导出Token，用户: $userId, 图片: $imagePHash, 项目: $projectName, 导出类型: ${exportType.name}');

      // 获取主机名（使用安全的方法）
      final hostname = await HostnameService.getHostname();
      PGLog.d('主机名: $hostname');

      // 获取Token（内部会处理缓存逻辑）
      final token = await _exportRepository.getExportToken(
        userId,
        imagePHash,
        imageName,
        projectName,
        projectId,
        hostname, // 使用安全获取的主机名
        exportType: exportType,
      );

      PGLog.d('成功获取Token: ${token.tokenId}, 过期时间: ${token.expireAt}');
      return token;
    } catch (e) {
      PGLog.e('获取导出Token失败: $e');
      if (e is ExportException) {
        rethrow;
      }
      throw ExportException('获取导出Token失败: $e');
    }
  }
}
