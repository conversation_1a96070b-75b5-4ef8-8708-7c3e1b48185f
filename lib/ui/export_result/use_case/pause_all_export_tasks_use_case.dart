import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/ui/export_result/use_case/fetch_export_report_use_case2.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

/// 暂停所有导出任务的UseCase
///
/// 当用户进入精修场景时，需要暂停所有正在进行的导出任务
/// 以避免资源冲突和性能问题
class PauseAllExportTasksUseCase {
  final UnityController _unityController;
  final FetchExportReportUseCase2 _fetchExportReport;

  PauseAllExportTasksUseCase(
    this._unityController,
    this._fetchExportReport,
  );

  /// 执行暂停所有导出任务的操作
  ///
  /// 返回暂停的任务数量
  Future<List<ExportRecord>> invoke() async {
    try {
      PGLog.d('开始暂停所有导出任务');

      // 获取所有导出任务
      final allTasks = await _fetchExportReport.invoke();

      // 筛选出正在进行的任务（等待中、处理中、暂停中）
      final activeTasks = allTasks.where((task) {
        final state = ExportState.fromCode(task.exportState);
        return state == ExportState.wait ||
            state == ExportState.processing ||
            state == ExportState.pausing;
      }).toList();

      if (activeTasks.isEmpty) {
        PGLog.d('没有正在进行的导出任务需要暂停');
        return [];
      }

      PGLog.d('找到 ${activeTasks.length} 个正在进行的导出任务，开始暂停');

      // 批量暂停所有活动任务
      final taskIds = activeTasks.map((task) => task.guid).toList();
      await _pauseBatchTasks(taskIds);

      PGLog.d('成功暂停 ${activeTasks.length} 个导出任务');
      return activeTasks;
    } catch (e) {
      PGLog.e('暂停所有导出任务失败: $e');
      rethrow;
    }
  }

  /// 批量暂停任务
  Future<void> _pauseBatchTasks(List<String> taskIds) async {
    final args = {
      'action': 'pause',
      'ids': taskIds,
    };

    final json = jsonEncode(args);
    final message = MessageToUnity(
      method: 'BatchInvokeExport',
      args: json,
      completed: const Uuid().v4(),
    );

    // 等待消息处理完成
    await _unityController.sendMessage(message);
  }

  /// 模拟暂停操作（用于测试）
  Future<int> invokeMock() async {
    try {
      PGLog.d('模拟暂停所有导出任务');

      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 500));

      // 模拟暂停了3个任务
      const mockPausedCount = 3;
      PGLog.d('模拟成功暂停 $mockPausedCount 个导出任务');

      return mockPausedCount;
    } catch (e) {
      PGLog.e('模拟暂停所有导出任务失败: $e');
      rethrow;
    }
  }
}
