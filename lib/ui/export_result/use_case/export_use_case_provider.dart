import 'package:turing_art/core/unity/unity_controller.dart';

import 'delete_export_task_use_case.dart';
import 'fetch_export_report_use_case2.dart';
import 'pause_all_export_tasks_use_case.dart';
import 'pause_export_task_use_case.dart';
import 'resume_all_export_tasks_use_case.dart';
import 'resume_export_tasks_use_case.dart';
import 'start_export_task_use_case.dart';

class ExportUseCaseProvider {
  final FetchExportReportUseCase2 fetchExportReport;
  final DeleteExportTaskUseCase deleteExportTask;
  final PauseExportTaskUseCase pauseExportTask;
  final StartExportTaskUseCase startExportTask;
  final PauseAllExportTasksUseCase pauseAllExportTasks;
  final ResumeAllExportTasksUseCase resumeAllExportTasks;
  final ResumeExportTasksUseCase resumeExportTasks;

  ExportUseCaseProvider({
    required UnityController unityController,
  })  : fetchExportReport = FetchExportReportUseCase2(),
        deleteExportTask = DeleteExportTaskUseCase(
          unityController,
        ),
        pauseExportTask = PauseExportTaskUseCase(
          unityController,
        ),
        startExportTask = StartExportTaskUseCase(
          unityController,
        ),
        pauseAllExportTasks = PauseAllExportTasksUseCase(
          unityController,
          FetchExportReportUseCase2(),
        ),
        resumeAllExportTasks = ResumeAllExportTasksUseCase(
          unityController,
          FetchExportReportUseCase2(),
        ),
        resumeExportTasks = ResumeExportTasksUseCase(
          unityController,
        );
}
