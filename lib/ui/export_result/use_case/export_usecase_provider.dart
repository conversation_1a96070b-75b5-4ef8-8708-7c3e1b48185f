import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/export_repository.dart';

import 'adjust_export_params_use_case.dart';
import 'get_export_token_use_case.dart';
import 'report_export_success_use_case.dart';

/// 导出相关用例的提供者
///
/// 这个类集中管理与导出功能相关的所有用例，
/// 便于依赖注入和统一管理。
class ExportUseCaseProvider {
  /// 获取导出Token用例
  final GetExportTokenUseCase getExportToken;

  /// 调整导出参数用例
  final AdjustExportParamsUseCase adjustExportParams;

  /// 报告导出成功用例
  final ReportExportSuccessUseCase reportExportSuccess;

  /// 创建 [ExportUseCaseProvider] 实例
  ///
  /// [exportRepository] 导出仓库
  /// [currentUserRepository] 当前用户仓库
  ExportUseCaseProvider({
    required ExportRepository exportRepository,
    required CurrentUserRepository currentUserRepository,
  })  : getExportToken =
            GetExportTokenUseCase(exportRepository, currentUserRepository),
        adjustExportParams = AdjustExportParamsUseCase(exportRepository),
        reportExportSuccess = ReportExportSuccessUseCase(exportRepository);
}
