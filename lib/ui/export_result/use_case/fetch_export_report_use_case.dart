import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/core/unity/unity_exception.dart';
import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

// 用于包含ExportRecord和原始JSON数据的类
class ExportRecordWithJson {
  final ExportRecord record;
  final Map<String, dynamic> originalJson;

  ExportRecordWithJson(this.record, this.originalJson);
}

class FetchExportReportUseCase {
  final UnityController _unityController;
  FetchExportReportUseCase(this._unityController);

  Future<List<ExportRecord>> invoke() async {
    try {
      final result =
          await _unityController.sendMessageWithResponse<MessageFromUnity>(
        MessageToUnity(
          method: 'RequestPresets',
          args: "",
          completed: const Uuid().v4(),
        ),
        UnityMessage.gotExportResult,
      );

      PGLog.d('fetchExportRecord原始数据: ${result.toJson()}'); // 记录完整原始数据

      if (result.args == null) {
        PGLog.e('预设数据格式异常: args为空');
        return [];
      }

      final rawData = result.args!;
      if (rawData.isEmpty || rawData[0] is! String) {
        PGLog.e('预设数据结构异常: 首元素非Json字符串类型');
        return [];
      }

      final jsonString = rawData[0] as String;
      return _parseJson(jsonString);
    } on UnityException catch (e) {
      PGLog.e('Unity异常: ${e.message}');
    } on TypeError catch (e) {
      PGLog.e('类型转换错误: ${e.toString()}\n${e.stackTrace}');
    } catch (e, stack) {
      PGLog.e('获取预设未知错误: $e\n$stack');
    }
    return [];
  }

  /// 获取包含原始JSON数据的ExportRecord列表
  Future<List<ExportRecordWithJson>> invokeWithJson() async {
    try {
      final result =
          await _unityController.sendMessageWithResponse<MessageFromUnity>(
        MessageToUnity(
          method: 'RequestPresets',
          args: "",
          completed: const Uuid().v4(),
        ),
        UnityMessage.gotExportResult,
      );

      PGLog.d('fetchExportRecord原始数据: ${result.toJson()}'); // 记录完整原始数据

      if (result.args == null) {
        PGLog.e('预设数据格式异常: args为空');
        return [];
      }

      final rawData = result.args!;
      if (rawData.isEmpty || rawData[0] is! String) {
        PGLog.e('预设数据结构异常: 首元素非Json字符串类型');
        return [];
      }

      final jsonString = rawData[0] as String;
      return _parseJsonWithOriginal(jsonString);
    } on UnityException catch (e) {
      PGLog.e('Unity异常: ${e.message}');
    } on TypeError catch (e) {
      PGLog.e('类型转换错误: ${e.toString()}\n${e.stackTrace}');
    } catch (e, stack) {
      PGLog.e('获取预设未知错误: $e\n$stack');
    }
    return [];
  }

  List<ExportRecord> _parseJson(String jsonStr) {
    try {
      final List<dynamic> jsonList = jsonDecode(jsonStr) as List;
      return jsonList
          .map((json) {
            try {
              return ExportRecord.fromJson(json);
            } catch (e) {
              PGLog.e('解析单条记录异常: $e, 数据: ${json.toString()}');
              return null;
            }
          })
          .whereType<ExportRecord>()
          .toList();
    } catch (e) {
      PGLog.e('解析JSON异常: $e');
      return [];
    }
  }

  List<ExportRecordWithJson> _parseJsonWithOriginal(String jsonStr) {
    try {
      final List<dynamic> jsonList = jsonDecode(jsonStr) as List;
      return jsonList
          .map((json) {
            try {
              final originalJson = json as Map<String, dynamic>;
              final record = ExportRecord.fromJson(originalJson);
              return ExportRecordWithJson(record, originalJson);
            } catch (e) {
              PGLog.e('解析单条记录异常: $e, 数据: ${json.toString()}');
              return null;
            }
          })
          .whereType<ExportRecordWithJson>()
          .toList();
    } catch (e) {
      PGLog.e('解析JSON异常: $e');
      return [];
    }
  }
}
