import 'dart:convert';

import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:uuid/uuid.dart';

class DeleteExportTaskUseCase {
  final UnityController _unityController;

  DeleteExportTaskUseCase(
    this._unityController,
  );

  Future<void> invoke(List<ExportRecord> records) async {
    try {
      await _unityController.sendMessage(
        MessageToUnity(
          method: 'DeleteExportTask',
          args: jsonEncode(records.map((e) => e.toJson()).toList()),
          completed: const Uuid().v4(),
        ),
      );
    } catch (e) {
      PGLog.e('删除导出任务失败: $e');
    }
  }

  Future<void> deleteBatchExportTask(List<String> ids) async {
    final args = {
      'action': 'delete',
      'ids': ids,
    };

    final json = jsonEncode(args);
    final message = MessageToUnity(
      method: 'BatchInvokeExport',
      args: json,
      completed: const Uuid().v4(),
    );
    // 等待消息处理完成
    await _unityController.sendMessage(message);
  }
}
