import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/model.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/enums/order_status.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/datalayer/domain/events/order_event.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/purchase_repository.dart';
import 'package:turing_art/providers/purchase_state_provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/purchase/view_models/purchase_qrcode_view_model.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_collect_info.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class PurchaseQRCodeDialog extends StatefulWidget {
  final List<PaymentChannel> payChannelList;
  final PurchaseCollectInfo collectInfo;

  const PurchaseQRCodeDialog({
    super.key,
    required this.payChannelList,
    required this.collectInfo,
  });

  @override
  State<PurchaseQRCodeDialog> createState() => _PurchaseQRCodeDialogState();

  /// 创建对话框内容
  static Widget _createDialogContent(List<PaymentChannel> payChannelList,
      String orderId, PurchaseCollectInfo collectInfo) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => PurchaseQRCodeViewModel(
            context.read<PurchaseRepository>(),
            payChannelList.first, // 传入第一个支付渠道，和上一个订单保持一致
            orderId, // 上个弹窗产生的订单ID
            context.read<PurchaseStateProvider>(), // 传入 PurchaseStateProvider
          ),
        ),
      ],
      child: PurchaseQRCodeDialog(
        payChannelList: payChannelList,
        collectInfo: collectInfo,
      ),
    );
  }

  /// public方法：显示对话框（传入支付渠道列表，以免等待绘制）
  static void show(List<PaymentChannel> payChannelList, String orderId,
      PurchaseCollectInfo collectInfo) {
    if (PGDialog.isDialogVisible(DialogTags.purchaseQRCode)) {
      PGLog.d('PurchaseQRCodeDialog show, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: 400,
      height: 500,
      needBlur: false,
      tag: DialogTags.purchaseQRCode,
      child: _createDialogContent(payChannelList, orderId, collectInfo),
    );
  }

  /// public方法：在Unity窗口上显示对话框
  static void showOnUnity(List<PaymentChannel> payChannelList, String orderId,
      PurchaseCollectInfo collectInfo) {
    if (PGDialog.isDialogVisible(DialogTags.purchaseQRCode)) {
      PGLog.d(
          'PurchaseQRCodeDialog showOnUnity, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialogOnUnity(
      width: 400,
      height: 500,
      needBlur: false,
      tag: DialogTags.purchaseQRCode,
      child: _createDialogContent(payChannelList, orderId, collectInfo),
    );
  }
}

class _PurchaseQRCodeDialogState extends State<PurchaseQRCodeDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _indicatorController;
  late Animation<double> _indicatorPosition;

  // 是否正在切换渠道 使用 ValueNotifier 局部重建
  final ValueNotifier<bool> _isChangingChannelNotifier =
      ValueNotifier<bool>(false);

  // 订单是否失效 使用 ValueNotifier 局部重建
  final ValueNotifier<bool> _isOrderClosedNotifier = ValueNotifier<bool>(false);

  // 订阅订单状态变更事件
  late StreamSubscription<OrderEvent> _orderStatusChangeSubscription;

  @override
  void initState() {
    super.initState();

    _indicatorController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _indicatorPosition = Tween<double>(begin: 0, end: 0).animate(
      CurvedAnimation(
        parent: _indicatorController,
        curve: Curves.easeInOut,
      ),
    );

    // 订阅订单事件
    _orderStatusChangeSubscription = context
        .read<PurchaseStateProvider>()
        .orderStatusEvents
        .listen(_handleOrderStatusChangeEvent);
  }

  // 处理订单事件
  void _handleOrderStatusChangeEvent(OrderEvent event) {
    if (!mounted) {
      PGLog.d('mounted == false');
      return;
    }

    // 只有当状态是closed时才通知监听器更新UI
    if (event.status == OrderStatus.closed) {
      _isOrderClosedNotifier.value = true;
    } else if (_isOrderClosedNotifier.value &&
        event.status != OrderStatus.closed) {
      // 如果之前是closed，现在不是closed，也需要更新
      _isOrderClosedNotifier.value = false;
    }

    // 处理订单完成、取消、退款事件
    if (event.status == OrderStatus.completed ||
        event.status == OrderStatus.canceled ||
        event.status == OrderStatus.refunded) {
      PGLog.d('purchase_QRCode_dialog _handleOrderEvent ${event.status}');
      // 关闭弹窗
      context
          .read<PurchaseQRCodeViewModel>()
          .closeQrCodeDialogWithCleanPurchasePlan();
    }
  }

  @override
  void dispose() {
    _orderStatusChangeSubscription.cancel();
    _indicatorController.dispose();
    _isOrderClosedNotifier.dispose(); // 释放ValueNotifier资源
    _isChangingChannelNotifier.dispose(); // 释放新增的ValueNotifier资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints.tight(const Size(400, 500)),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: const Color(0xFF121315),
            borderRadius: BorderRadius.circular(12),
            boxShadow: const [
              BoxShadow(
                color: Color(0x33000000),
                blurRadius: 40,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // 1. 关闭按钮
              Positioned(
                top: 16,
                right: 16,
                child: GestureDetector(
                  onTap: () {
                    final viewModel = context.read<PurchaseQRCodeViewModel>();
                    _reportPurchaseEvent(PageAction.close_scan_pay,
                        widget.collectInfo, viewModel.currentOrderId);
                    // 关闭弹窗
                    viewModel.closeQrCodeDialogWithCleanPurchasePlan();
                  },
                  child: Image.asset(
                    'assets/icons/home_window_close.png',
                    width: 24,
                    height: 24,
                  ),
                ),
              ),

              // 2. 当前渠道描述 - 切换渠道变更使用Consumer监听渠道变化
              Positioned(
                top: 56,
                left: 0,
                right: 0,
                child: Center(
                  child: Consumer<PurchaseQRCodeViewModel>(
                    builder: (context, viewModel, _) => Text(
                      "${viewModel.currentSelectedChannel.description}扫一扫",
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.semiBold,
                        fontSize: 16,
                        height: 1.4,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),

              // 3. 渠道按钮集合 可能发生变更，使用Consumer监听渠道变化
              Positioned(
                top: 98, // 56 + 22 + 20
                left: 20,
                right: 20,
                child: Consumer<PurchaseQRCodeViewModel>(
                  builder: (context, viewModel, _) =>
                      _buildChannelButtons(viewModel),
                ),
              ),

              // 4. 二维码 - 使用ValueListenableBuilder监听状态变化
              Positioned(
                top: 154, // 98 + 32 + 24
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    width: 216,
                    height: 216,
                    color: Colors.transparent,
                    // 使用嵌套的ValueListenableBuilder监听两个状态（订单是否失效，是否正在切换渠道）
                    child: ValueListenableBuilder<bool>(
                      valueListenable: _isOrderClosedNotifier,
                      builder: (context, isOrderClosed, _) {
                        return ValueListenableBuilder<bool>(
                          valueListenable: _isChangingChannelNotifier,
                          builder: (context, isChangingChannel, _) {
                            return Consumer<PurchaseQRCodeViewModel>(
                              builder: (context, viewModel, _) {
                                // 传递两个状态给二维码构建方法
                                return _buildQrCodeWidget(
                                  viewModel,
                                  isOrderClosed,
                                  isChangingChannel,
                                );
                              },
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),

              // 5. 应付金额 - 不需要使用Consumer监听价格变化
              Positioned(
                top: 382, // 154 + 216 + 12
                left: 0,
                right: 0,
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "应付金额：",
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.semiBold,
                          fontSize: 16,
                          height: 1.4,
                          color: const Color(0x99EBEDF5),
                        ),
                      ),
                      Text(
                        context
                                .read<PurchaseQRCodeViewModel>()
                                .currentPlanPrice ??
                            '',
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: Fonts.semiBold,
                          fontSize: 20,
                          height: 1.4,
                          color: const Color(0xFFF72561),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 6. 支付协议提示
              const Positioned(
                top: 414, // 382 + 28 + 4
                left: 0,
                right: 0,
                child: Center(
                  child: Text(
                    "支付即表示同意用户使用协议",
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      height: 1.21,
                      color: Color(0x4DEBEDF5),
                    ),
                  ),
                ),
              ),

              // 7. 当前账号 - 直接从CurrentUserRepository获取
              Positioned(
                bottom: 24,
                left: 0,
                right: 0,
                child: Center(
                  child: Text(
                    "当前账号：${context.read<CurrentUserRepository>().user?.mobile ?? ''}",
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      height: 1.21,
                      color: Color(0x99EBEDF5),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChannelButtons(PurchaseQRCodeViewModel viewModel) {
    final channelCount = widget.payChannelList.length;
    final buttonWidth = 360 / channelCount;

    return SizedBox(
      width: 360,
      height: 32,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 渠道按钮
          Row(
            mainAxisSize: MainAxisSize.max,
            children: List.generate(
              channelCount,
              (index) {
                final channel = widget.payChannelList[index];
                // 使用StatefulBuilder避免鼠标悬停时的闪烁
                return StatefulBuilder(
                  builder: (context, setState) {
                    return PlatformMouseRegion(
                      onTap: () => _selectChannel(channel, index, viewModel),
                      cursor: SystemMouseCursors.click,
                      child: SizedBox(
                        width: buttonWidth,
                        height: 20,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/icons/pay_channel_${channel.name}.png',
                              width: 16,
                              height: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              channel.description,
                              style: TextStyle(
                                color:
                                    viewModel.currentSelectedChannel == channel
                                        ? Colors.white
                                        : const Color(0x99EBEDF5),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),

          // 底部线条
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: 360,
              height: 1.5,
              decoration: BoxDecoration(
                color: const Color(0x26EBEDF5),
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),

          // 选中指示条
          AnimatedBuilder(
            animation: _indicatorController,
            builder: (context, child) {
              return Align(
                alignment: Alignment.bottomLeft,
                child: Transform.translate(
                  offset: Offset(_indicatorPosition.value, 0),
                  child: Container(
                    width: buttonWidth,
                    height: 1.5,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1E2E5),
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // 更新二维码构建方法，接收isChangingChannel参数
  Widget _buildQrCodeWidget(PurchaseQRCodeViewModel viewModel,
      bool isOrderClosed, bool isChangingChannel) {
    // 使用传入的isOrderClosed替代直接检查_currentOrderStatus
    if (isOrderClosed) {
      return Stack(
        children: [
          // 保留原始二维码但显示为失效状态
          viewModel.currentQrUrl != null && viewModel.currentQrUrl!.isNotEmpty
              ? Image.network(
                  viewModel.currentQrUrl!,
                  width: 216,
                  height: 216,
                  fit: BoxFit.cover,
                  opacity: const AlwaysStoppedAnimation(0.1), // 透明度设为10%
                )
              : const SizedBox.expand(
                  child: ColoredBox(color: Colors.white),
                ),

          // 居中的文字和按钮
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "支付已失效",
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 14,
                    color: const Color(0xFFE1E2E5),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "请刷新后扫码支付",
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 14,
                    color: const Color(0xFFE1E2E5),
                  ),
                ),
                const SizedBox(height: 20),
                GestureDetector(
                  onTap: () async {
                    // 显示加载中 - 使用ValueNotifier更新
                    _isChangingChannelNotifier.value = true;

                    // 刷新订单 - 调用createOrder
                    final success = await viewModel.createOrder(
                        viewModel.purchaseRepository.currentPurchasePlanId ??
                            "");

                    // 刷新状态 - 使用ValueNotifier更新
                    _isChangingChannelNotifier.value = false;

                    // 更新订单状态通知器
                    if (success) {
                      _isOrderClosedNotifier.value = false;
                    }

                    // 处理失败情况
                    if (!success) {
                      PGDialog.showToast('刷新支付二维码失败，请重试');
                    }
                  },
                  child: Container(
                    width: 60,
                    height: 23,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1E2E5),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "立即刷新",
                      style: TextStyle(
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.medium,
                        fontSize: 12,
                        color: const Color(0xFF121315),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    final qrUrl = viewModel.currentQrUrl;
    if (qrUrl == null || qrUrl.isEmpty) {
      //切换渠道或者第一次创建时候，会触发loading
      return isChangingChannel
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF121315),
              ),
            )
          : const SizedBox.shrink(); // 非loading状态,订单创建失败，占位,需要和设计师确认新UI后修改
    }

    return Image.network(
      qrUrl,
      width: 216,
      height: 216,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return child;
        }
        return Center(
          child: CircularProgressIndicator(
            color: const Color(0xFF121315),
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                : null,
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                "二维码加载失败",
                style: TextStyle(
                  color: Color(0xFF121315),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  // 重新加载二维码
                  viewModel.refreshCurrentQrCode();
                },
                child: const Text(
                  "点击重试",
                  style: TextStyle(
                    color: Color(0xFFF72561),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _updateIndicatorPosition(int index) {
    if (index < 0) {
      return;
    }

    final channelCount = widget.payChannelList.length;
    final buttonWidth = 360 / channelCount;

    setState(() {
      _indicatorPosition = Tween<double>(
        begin: _indicatorPosition.value,
        end: index * buttonWidth,
      ).animate(
        CurvedAnimation(
          parent: _indicatorController,
          curve: Curves.easeInOut,
        ),
      );

      _indicatorController.reset();
      _indicatorController.forward();
    });
  }

  void _selectChannel(PaymentChannel channel, int index,
      PurchaseQRCodeViewModel viewModel) async {
    if (viewModel.currentSelectedChannel == channel) {
      return;
    }

    // 使用ValueNotifier替代setState
    _isChangingChannelNotifier.value = true;

    // 更新动画
    _updateIndicatorPosition(index);

    // 调用创建订单
    final success = await viewModel.setSelectedChannel(channel);

    // 使用ValueNotifier替代setState
    _isChangingChannelNotifier.value = false;

    // 只处理错误情况，成功会刷新对应UI
    if (!success) {
      // 添加错误提示 （目前和产品确认只是错误提示，后期UI设计出来需要优化）
      PGDialog.showToast('切换支付渠道失败，请重试');
    }
  }

  /// 上报购买页面事件
  void _reportPurchaseEvent(
      PageAction action, PurchaseCollectInfo collectInfo, String? orderId) {
    recordPayActionLog(
        userId: collectInfo.userId ?? '',
        payActionId: collectInfo.timestamp,
        pageStyle: 'no_use',
        pageAction: action,
        sourceType: collectInfo.sourceType,
        itemId: collectInfo.productId,
        itemName: collectInfo.productName,
        itemPrice: collectInfo.productPrice,
        orderId: orderId);
  }
}
