import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:pg_turing_collect_event/collect/pagecost/page_buy.dart';
import 'package:pg_turing_collect_event/collect/pay_action_log.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/aigc_entrance_manager.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/purchase_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/purchase/view_models/purchase_view_model.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_collect_info.dart';
import 'package:turing_art/utils/custom_service_handler.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'purchase_card.dart';
import 'purchase_custom_service_dialog.dart';

// 将 PurchaseViewModel 传递给付款二维码对话框，payActionId用于埋点
typedef ReadyToPayCallBack = void Function(List<PaymentChannel> payChannelList,
    String orderId, PurchaseCollectInfo collectInfo);

class PurchaseDialog extends StatefulWidget {
  // 订单创建成功回调，让父组件处理弹出付款二维码
  final ReadyToPayCallBack onOrderCreated;

  // 使用静态变量来跟踪全局状态，确保多个实例之间共享
  static bool _processingPayment = false;
  // 来源页面，用于埋点
  final SourceType sourceType;
  // 默认选中的tab
  final PurchaseTabType? defaultTab;
  // 是否需要tab切换
  final bool needTabSwitch;

  const PurchaseDialog({
    super.key,
    required this.onOrderCreated,
    required this.sourceType,
    this.defaultTab,
    this.needTabSwitch = true,
  });

  @override
  State<PurchaseDialog> createState() => _PurchaseDialogState();

  static Future<void> show(BuildContext context,
      ReadyToPayCallBack onOrderCreated, SourceType sourceType,
      {PurchaseTabType? defaultTab}) async {
    if (PGDialog.isDialogVisible(DialogTags.purchase)) {
      PGLog.d('PurchaseDialog show, but dialog already exist, return');
      return;
    }
    PGDialog.showLoading();
    // await检查用户是否是 aigcUser（有缓存，不会每次都请求）,即使是外部传入的是aigcTab，也不显示aigcTab，以免布局不正确
    final aigcEntranceManager = context.read<AigcEntranceManager>();
    final isAigcUser = await aigcEntranceManager.isAigcUser();
    // 马上需要showLoading，所以需要await先dismiss
    await PGDialog.dismiss();

    // 如果不是 aigcUser，强制 defaultTab 为 retouch
    final effectiveDefaultTab =
        isAigcUser ? defaultTab : PurchaseTabType.retouch;

    PGDialog.showCustomDialog(
      width: 1000,
      height: 697,
      needBlur: false,
      tag: DialogTags.purchase,
      child: PurchaseDialog(
          onOrderCreated: onOrderCreated,
          sourceType: sourceType,
          defaultTab: effectiveDefaultTab,
          needTabSwitch: isAigcUser),
    );
  }

  static Future<void> showOnUnity(BuildContext context,
      ReadyToPayCallBack onOrderCreated, SourceType sourceType,
      {PurchaseTabType? defaultTab}) async {
    PGDialog.showLoading();
    // await检查用户是否是 aigcUser（有缓存，不会每次都请求）,即使是外部传入的是aigcTab，也不显示aigcTab，以免布局不正确
    final aigcEntranceManager = context.read<AigcEntranceManager>();
    final isAigcUser = await aigcEntranceManager.isAigcUser();
    // 马上需要showLoading，所以需要await先dismiss
    await PGDialog.dismiss();

    // 如果不是 aigcUser，强制 defaultTab 为 retouch
    final effectiveDefaultTab =
        isAigcUser ? defaultTab : PurchaseTabType.retouch;

    if (PGDialog.isDialogVisible(DialogTags.purchase)) {
      PGLog.d('PurchaseDialog showOnUnity, but dialog already exist, return');
      return;
    }
    await PGDialog.showCustomDialogOnUnity(
      width: 1000,
      height: 697,
      needBlur: false,
      tag: DialogTags.purchase,
      child: PurchaseDialog(
          onOrderCreated: onOrderCreated,
          sourceType: sourceType,
          defaultTab: effectiveDefaultTab,
          needTabSwitch: isAigcUser),
    );
  }

  static Future<String> getChatHtmlPath() async {
    // 获取应用程序文档目录
    final directory = await getApplicationDocumentsDirectory();
    final filePath = path.join(directory.path, 'chat.html');

    // 检查文件是否存在，如果不存在则从assets复制
    final file = File(filePath);
    if (!file.existsSync()) {
      try {
        // 从assets加载HTML内容
        final htmlContent =
            await rootBundle.loadString('lib/ui/core/ui/desktop/chat.html');
        // 写入文件
        await file.writeAsString(htmlContent);
        PGLog.d('成功将chat.html写入到: $filePath');
      } catch (e) {
        PGLog.e('加载chat.html失败: $e');
        // 创建一个简单的备用HTML
        const fallbackHtml = '''
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>在线咨询</title>
  <style>
    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
    h1 { color: #333; }
    p { color: #666; }
  </style>
</head>
<body>
  <h1>在线咨询</h1>
  <p>请联系我们的客服团队获取帮助</p>
  <p>电话: 400-xxx-xxxx</p>
  <p>邮箱: <EMAIL></p>
</body>
</html>
''';
        await file.writeAsString(fallbackHtml);
        PGLog.d('使用备用HTML');
      }
    }

    // 返回file://协议的完整路径
    return 'file://${file.path}';
  }
}

class _PurchaseDialogState extends State<PurchaseDialog> {
  // 埋点时间戳
  int timestamp = 0;

  // 用于埋点记录用户id
  String? _userId;

  // 使用ValueNotifier来管理hover状态，避免大范围重建
  final ValueNotifier<PurchasePlan?> _hoveredCardNotifier = ValueNotifier(null);
  final ValueNotifier<bool> _isCustomHoveredNotifier = ValueNotifier(false);

  // 卡片滑动控制器
  final ScrollController _cardScrollController = ScrollController();

  // 存储viewModel引用，避免在回调中使用context.read
  PurchaseViewModel? _viewModel;

  @override
  void initState() {
    super.initState();
    timestamp = DateTimeUtil.getCurrentTimestampSec();
    _reportPurchaseEvent(PageAction.page_show);

    // 添加滚动监听器
    _cardScrollController.addListener(_onScrollChanged);
  }

  @override
  void dispose() {
    final duration = DateTimeUtil.getCurrentTimestampSec() - timestamp;
    recordPageBuy(
        userId: _userId ?? '',
        stTime: timestamp.toString(),
        duration: duration.toString());
    _hoveredCardNotifier.dispose();
    _isCustomHoveredNotifier.dispose();
    _cardScrollController.removeListener(_onScrollChanged);
    _cardScrollController.dispose();
    super.dispose();
  }

  /// 滚动位置变化回调
  void _onScrollChanged() {
    if (_viewModel != null && _cardScrollController.hasClients) {
      final scrollOffset = _cardScrollController.offset;
      const cardWidth = 220.0; // 卡片宽度
      const cardSpacing = 16.0; // 卡片间距
      _viewModel!
          .updatePageByScrollPosition(scrollOffset, cardWidth, cardSpacing);
    }
  }

  /// 滚动到指定页面
  void _scrollToPage(int page) {
    if (_viewModel == null || !_cardScrollController.hasClients) {
      return;
    }

    _viewModel!.setIsJumpPageAnimation(value: true);

    const cardWidth = 220.0;
    const cardSpacing = 16.0;
    final targetOffset =
        _viewModel!.getScrollOffsetForPage(page, cardWidth, cardSpacing);
    final currentOffset = _cardScrollController.offset;

    // 如果目标位置和当前位置相同，直接结束
    if ((targetOffset - currentOffset).abs() < 1.0) {
      _viewModel!.setIsJumpPageAnimation(value: false);
      return;
    }

    _cardScrollController
        .animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    )
        .then((_) {
      _viewModel!.setIsJumpPageAnimation(value: false);
    }).catchError((error) {
      _viewModel!.setIsJumpPageAnimation(value: false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => PurchaseViewModel(
        context.read<PurchaseRepository>(),
        context.read<AccountRepository>(),
        context.read<OpsCustomTableRepository>(),
        defaultTab: widget.defaultTab,
      ),
      child: Container(
        width: 1000,
        height: 697,
        decoration: BoxDecoration(
          border: Border.all(
            color: const Color(0x1AFFFFFF),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // 背景色组件 - 单独监听tab变化
              _buildBackgroundWidget(),

              // 内容组件 - 包含所有其他子组件
              _buildContentWidget(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建背景组件
  Widget _buildBackgroundWidget() {
    return Selector<PurchaseViewModel, PurchaseTabType>(
      selector: (context, viewModel) => viewModel.currentTab,
      builder: (context, currentTab, _) {
        final isAigc = currentTab == PurchaseTabType.aigcPoint;

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: isAigc
                  ? [const Color(0xFF0D0D0D), const Color(0xFF111314)]
                  : [const Color(0xFF0D0D0D), const Color(0xFF141311)],
            ),
          ),
        );
      },
    );
  }

  /// 构建内容组件
  Widget _buildContentWidget() {
    return Stack(
      children: [
        // 主要内容区域 - 使用SingleChildScrollView解决溢出问题
        Positioned(
          top: 12,
          left: 0,
          right: 0,
          bottom: 0,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Tab切换区域
                _buildTabSection(),
                const SizedBox(height: 28),
                // 标题区域
                _buildTitleSection(),
                const SizedBox(height: 8),
                // 副标题区域
                _buildSubtitleSection(),
                const SizedBox(height: 22),
                // 套餐卡片区域
                _buildPackageCardsSection(),
                // 分页控件（仅在超过4个卡片时显示）
                _buildPaginationControlsSectionIfNeeded(),
                // 底部说明文字
                _buildBottomDescriptionSection(),
                const SizedBox(height: 24),
                // 联系商务按钮
                _buildContactServiceSection(),
              ],
            ),
          ),
        ),
        // 关闭按钮 - 独立布局
        Positioned(
          top: 16,
          right: 16,
          child: GestureDetector(
            onTap: () {
              _reportPurchaseEvent(PageAction.close_sku_list);
              PGDialog.dismiss(tag: DialogTags.purchase);
            },
            child: PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              child: Container(
                width: 24,
                height: 24,
                alignment: Alignment.center,
                child: Icon(
                  Icons.close,
                  color: Colors.white.withAlpha(150),
                  size: 24,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建Tab切换区域
  Widget _buildTabSection() {
    // 如果不需要tab切换，则不显示
    if (!widget.needTabSwitch) {
      return const SizedBox.shrink();
    }
    return Center(
      child: Container(
        width: 166,
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 2),
        decoration: BoxDecoration(
          color: const Color(0xFF1F1F1F),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Selector<PurchaseViewModel, PurchaseTabType>(
          selector: (context, viewModel) => viewModel.currentTab,
          builder: (context, currentTab, _) => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildTabButton(
                '精修套餐',
                PurchaseTabType.retouch,
                currentTab,
              ),
              _buildTabButton(
                'AI积分',
                PurchaseTabType.aigcPoint,
                currentTab,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建单个Tab按钮
  Widget _buildTabButton(
    String title,
    PurchaseTabType tabType,
    PurchaseTabType currentTab,
  ) {
    final isSelected = currentTab == tabType;

    return GestureDetector(
      onTap: () {
        _viewModel?.switchTab(tabType);
      },
      child: Container(
        width: 80,
        height: 28,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF333333) : Colors.transparent,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: isSelected ? Fonts.semiBold : Fonts.regular,
              fontSize: 14,
              height: 18 / 14,
              color: isSelected
                  ? const Color(0xFFFFFFFF)
                  : const Color(0xB2FFFFFF),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建标题区域
  Widget _buildTitleSection() {
    return Selector<PurchaseViewModel, String>(
      selector: (context, viewModel) => viewModel.currentTabTitle,
      builder: (context, title, _) => Consumer<PurchaseViewModel>(
        builder: (context, viewModel, _) {
          final isAigc = viewModel.currentTab == PurchaseTabType.aigcPoint;

          return SizedBox(
            height: 45,
            child: ShaderMask(
              shaderCallback: (bounds) {
                if (isAigc) {
                  return const LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [Color(0xFFACDCF7), Color(0xFFF2FAFF)],
                  ).createShader(bounds);
                } else {
                  return const LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [Color(0xFFDACEAD), Color(0xFFFFF9EA)],
                  ).createShader(bounds);
                }
              },
              child: Text(
                title,
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                  fontSize: 32,
                  height: 1.0,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建副标题区域
  Widget _buildSubtitleSection() {
    return Selector<PurchaseViewModel, String>(
      selector: (context, viewModel) => viewModel.currentTabSubtitle,
      builder: (context, subtitle, _) => Consumer<PurchaseViewModel>(
        builder: (context, viewModel, _) {
          final isAigc = viewModel.currentTab == PurchaseTabType.aigcPoint;

          if (isAigc) {
            return SizedBox(
              height: 20,
              child: Text(
                subtitle,
                style: TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                  fontSize: 14,
                  height: 1.0,
                  color: const Color(0xFFFFFFFF),
                ),
                textAlign: TextAlign.center,
              ),
            );
          } else {
            // 精修套餐的副标题需要特殊处理
            return SizedBox(
              height: 20,
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.semiBold,
                    fontSize: 14,
                    height: 1.0,
                  ),
                  children: const [
                    TextSpan(
                      text: '片量买多少就能用多少，',
                      style: TextStyle(color: Color(0xFFFFFFFF)),
                    ),
                    TextSpan(
                      text: '到手永不过期',
                      style: TextStyle(color: Color(0xFFDACEAD)),
                    ),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }

  /// 构建套餐卡片区域
  Widget _buildPackageCardsSection() {
    return Consumer<PurchaseViewModel>(
      builder: (context, viewModel, _) {
        _viewModel = viewModel; // 存储viewModel引用
        final packages = viewModel.currentTabPackages;

        if (packages.isEmpty) {
          return const SizedBox(
            height: 310,
            child: Center(
              child: Text(
                '正在加载套餐信息...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ),
          );
        }

        return Column(
          children: [
            SizedBox(
              height: 310,
              child: packages.length <= 4
                  ? _buildStaticCards(packages, viewModel)
                  : _buildScrollableCards(packages, viewModel),
            ),
          ],
        );
      },
    );
  }

  /// 构建静态卡片（<=4张）
  Widget _buildStaticCards(
      List<PurchasePlan> packages, PurchaseViewModel viewModel) {
    return Column(
      children: [
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            for (int i = 0; i < packages.length; i++) ...[
              if (i > 0) const SizedBox(width: 16),
              _buildPackageCard(packages[i], viewModel, i),
            ],
          ],
        ),
      ],
    );
  }

  /// 构建可滑动卡片（>4张）
  Widget _buildScrollableCards(
      List<PurchasePlan> packages, PurchaseViewModel viewModel) {
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(
        dragDevices: {
          PointerDeviceKind.touch,
          PointerDeviceKind.mouse, // 启用鼠标拖拽
        },
        scrollbars: false, // 隐藏滚动条（可选）
      ),
      child: SingleChildScrollView(
        controller: _cardScrollController,
        scrollDirection: Axis.horizontal,
        physics: const ClampingScrollPhysics(),
        // 动态计算padding，确保最后一页时右对齐
        padding: const EdgeInsets.only(
          left: 24,
          // 如果是最后一页且不满4个卡片，调整右侧padding确保对齐
          right: 24,
        ),
        child: Column(
          children: [
            const SizedBox(height: 10),
            Row(
              children: [
                for (int i = 0; i < packages.length; i++) ...[
                  if (i > 0) const SizedBox(width: 16),
                  _buildPackageCard(packages[i], viewModel, i),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationControlsSectionIfNeeded() {
    // 分页控件（仅在超过4个卡片时显示）
    return Consumer<PurchaseViewModel>(
      builder: (context, viewModel, _) {
        if (viewModel.currentTabPackages.length > 4) {
          return Column(
            children: [
              const SizedBox(height: 12),
              _buildPaginationControls(viewModel),
              const SizedBox(height: 16),
            ],
          );
        }
        return const SizedBox(height: 60);
      },
    );
  }

  /// 构建分页控件
  Widget _buildPaginationControls(PurchaseViewModel viewModel) {
    return Selector<PurchaseViewModel, Map<String, dynamic>>(
      selector: (context, vm) => {
        'currentPage': vm.currentPage,
        'totalPages': vm.totalPages,
        'canGoPrevious': vm.canGoPrevious,
        'canGoNext': vm.canGoNext,
      },
      builder: (context, pageState, _) {
        final canGoPrevious = pageState['canGoPrevious'] as bool;
        final canGoNext = pageState['canGoNext'] as bool;

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 上一页按钮
            GestureDetector(
              onTap: canGoPrevious
                  ? () {
                      viewModel.previousPage();
                      _scrollToPage(viewModel.currentPage);
                    }
                  : null,
              child: PlatformMouseRegion(
                cursor: canGoPrevious
                    ? SystemMouseCursors.click
                    : SystemMouseCursors.basic,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(0xFF1F1F1F),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Center(
                    child: Transform.rotate(
                      angle: 3.14,
                      child: Icon(
                        Icons.play_arrow,
                        size: 10,
                        color: canGoPrevious
                            ? Colors.white
                            : const Color(0xFF464646),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(width: 20), // 两个按钮之间的间距

            // 下一页按钮
            GestureDetector(
              onTap: canGoNext
                  ? () {
                      viewModel.nextPage();
                      _scrollToPage(viewModel.currentPage);
                    }
                  : null,
              child: PlatformMouseRegion(
                cursor: canGoNext
                    ? SystemMouseCursors.click
                    : SystemMouseCursors.basic,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(0xFF1F1F1F),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.play_arrow,
                      size: 10,
                      color: canGoNext ? Colors.white : const Color(0xFF464646),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建单个套餐卡片
  Widget _buildPackageCard(
      PurchasePlan model, PurchaseViewModel viewModel, int index) {
    return GestureDetector(
      onTap: () async {
        _onBuyNowPressed(model, viewModel);
      },
      onDoubleTap: () async {
        _onBuyNowPressed(model, viewModel);
      },
      child: SizedBox(
        width: 220,
        height: 300,
        child: PlatformMouseRegion(
          onEnter: (_) => _hoveredCardNotifier.value = model,
          onExit: (_) => _hoveredCardNotifier.value = null,
          cursor: SystemMouseCursors.click,
          child: ValueListenableBuilder<PurchasePlan?>(
            valueListenable: _hoveredCardNotifier,
            builder: (context, hoveredCard, _) {
              final bool isHovered = hoveredCard == model;

              return PurchaseCard(
                model: model,
                viewModel: viewModel,
                isHovered: isHovered,
                cardIndex: index,
                onBuyNow: () => _onBuyNowPressed(model, viewModel),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 构建底部说明区域
  Widget _buildBottomDescriptionSection() {
    return Selector<PurchaseViewModel, PurchaseTabType>(
      selector: (context, viewModel) => viewModel.currentTab,
      builder: (context, currentTab, _) {
        String description;
        if (currentTab == PurchaseTabType.aigcPoint) {
          description =
              'AI积分说明：积分可用于AIGC功能的使用，不同功能消耗积分数量不同。具体消耗规则请查看相关功能说明。郑重承诺，购买的积分永不过期。';
        } else {
          description =
              '具体剩余张数可在「个人中心」-「我的套餐」中查看。套餐"张数"消耗规则：以实际导出照片张数进行计费。一张照片导出后再次导出不重复计费。';
        }

        return SizedBox(
          width: 900,
          height: 48,
          child: RichText(
            textAlign: TextAlign.left,
            text: TextSpan(
              style: TextStyle(
                color: Colors.white.withOpacity(0.5), // 白色50%透明度
                fontSize: 14,
                height: 24 / 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.regular,
              ),
              children: [
                TextSpan(text: description),
                TextSpan(
                  text: '由于本产品服务为付费软件许可使用服务，一经许可即不支持退款，请审慎、理性消费；使用',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5), // 白色50%透明度
                    fontSize: 14,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                  ),
                ),
                TextSpan(
                  text: '兑换代码',
                  style: TextStyle(
                    color: const Color(0xFF057FFA),
                    fontSize: 14,
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      _viewModel?.onRedeemCodePressed();
                    },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建联系商务区域
  Widget _buildContactServiceSection() {
    return GestureDetector(
      onTap: () {
        PurchaseCustomServiceDialog.show(
          onPurchaseConsult: () async {
            PGLog.d('购买咨询');
            CustomServiceHandler.showCustomerService('购买咨询');
            _reportPurchaseEvent(PageAction.help_purchase);
          },
          onTechSupport: () async {
            PGLog.d('技术支持');
            CustomServiceHandler.showCustomerService('技术支持');
            _reportPurchaseEvent(PageAction.help_support);
          },
          onMarketConsult: () async {
            PGLog.d('市场咨询');
            CustomServiceHandler.showCustomerService('市场咨询');
            _reportPurchaseEvent(PageAction.help_market);
          },
        );
      },
      child: PlatformMouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (_) => _isCustomHoveredNotifier.value = true,
        onExit: (_) => _isCustomHoveredNotifier.value = false,
        child: ValueListenableBuilder<bool>(
          valueListenable: _isCustomHoveredNotifier,
          builder: (context, isHovered, _) {
            return Container(
              width: 86,
              height: 32,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: isHovered
                    ? const Color(0xFFFFFFFF).withOpacity(0.13)
                    : const Color(0xFFFFFFFF).withOpacity(0.08),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/icons/purchase_custom_service.png',
                    fit: BoxFit.cover,
                    width: 24,
                    height: 24,
                  ),
                  const SizedBox(width: 2),
                  SizedBox(
                    width: 52,
                    child: Text(
                      '联系商务',
                      style: TextStyle(
                        color: const Color(0xB2FFFFFF),
                        fontSize: 12,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.regular,
                        height: 16 / 12,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _onBuyNowPressed(PurchasePlan model, PurchaseViewModel viewModel) async {
    // 使用静态变量检查是否已经在处理支付
    if (PurchaseDialog._processingPayment) {
      return;
    }
    // 设置标志，防止重复处理
    PurchaseDialog._processingPayment = true;
    _reportPurchaseEvent(
      PageAction.pay_click,
      model.id,
      model.productName,
      model.price.price,
    );
    try {
      // 立即购买当前选择的套餐
      final orderId = await viewModel.onBuyNowPressed(model.id);
      if (orderId != null) {
        _reportPurchaseEvent(
          PageAction.create_order,
          model.id,
          model.productName,
          model.price.price,
          orderId,
        );
        final payChannelList = viewModel.availableChannels;
        // 关闭商品弹窗（兼容在unity窗口上必须等待，否则层级不正确）
        await PGDialog.dismiss(tag: DialogTags.purchase);

        // 调用回调函数，传递可支付渠道列表，方便马上创建支付UI
        widget.onOrderCreated(
            payChannelList, orderId, _getPurchaseCollectInfo(model));
      } else {
        // 显示错误提示
        PGDialog.showToast('创建订单失败');
      }
    } finally {
      // 无论成功失败，最后都重置标志
      PurchaseDialog._processingPayment = false;
    }
  }

  PurchaseCollectInfo _getPurchaseCollectInfo(PurchasePlan model) {
    _userId ??= context.read<CurrentUserRepository>().user?.effectiveId;

    return PurchaseCollectInfo(
        timestamp: timestamp.toString(),
        sourceType: widget.sourceType,
        userId: _userId,
        productId: model.id,
        productName: model.productName,
        productPrice: model.price.price);
  }

  /// 上报购买页面事件
  void _reportPurchaseEvent(PageAction action,
      [String? itemId, String? itemName, String? itemPrice, String? orderId]) {
    _userId ??= context.read<CurrentUserRepository>().user?.effectiveId;

    recordPayActionLog(
        userId: _userId ?? '',
        payActionId: timestamp.toString(),
        pageStyle: 'no_use',
        pageAction: action,
        sourceType: widget.sourceType,
        itemId: itemId,
        itemName: itemName,
        itemPrice: itemPrice,
        orderId: orderId);
  }
}
