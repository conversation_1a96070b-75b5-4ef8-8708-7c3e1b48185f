import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/purchase/view_models/purchase_view_model.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_card_color_config.dart';
import 'package:turing_art/datalayer/domain/enums/purchase_aigc_card_type.dart';

/// 购买卡片组件 - 新设计规范实现
class PurchaseCard extends StatelessWidget {
  final PurchasePlan model;
  final PurchaseViewModel viewModel;
  final bool isHovered;
  final int cardIndex;
  final VoidCallback onBuyNow;

  const PurchaseCard({
    super.key,
    required this.model,
    required this.viewModel,
    required this.isHovered,
    required this.cardIndex,
    required this.onBuyNow,
  });

  @override
  Widget build(BuildContext context) {
    final isAigc = viewModel.currentTab == PurchaseTabType.aigcPoint;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      transform: Matrix4.translationValues(0, isHovered ? -10 : 0, 0),
      width: 220,
      height: 300,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: isAigc ? null : _getRetouchCardGradient(),
      ),
      child: Stack(
        clipBehavior: Clip.antiAlias,
        children: [
          // AI积分背景图片
          if (isAigc) _buildAigcBackgroundImage(),

          // 精修套餐右下角图标
          if (!isAigc) _buildRetouchBackgroundIcon(),

          // 卡片主要内容
          Padding(
            padding:
                const EdgeInsets.only(top: 20, left: 16, right: 16, bottom: 16),
            child: _buildCardContent(isAigc),
          ),

          // 顶部标签
          if (model.tagIcon != null && model.tagIcon!.url.isNotEmpty)
            _buildTopTag(),
        ],
      ),
    );
  }

  /// 获取精修套餐卡片渐变
  LinearGradient _getRetouchCardGradient() {
    final config = _getRetouchConfig();
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: config.backgroundColors,
      stops: config.backgroundColorStops,
    );
  }

  /// 构建AI积分背景图片
  Widget _buildAigcBackgroundImage() {
    final config = _getAigcConfig();
    return Positioned.fill(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          config.backgroundImagePath,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  /// 构建精修套餐右下角背景图标
  Widget _buildRetouchBackgroundIcon() {
    return Positioned(
      bottom: 0,
      right: 0,
      child: Image.asset(
        'assets/icons/purchase_retouch_bg.png',
        fit: BoxFit.cover,
      ),
    );
  }

  /// 获取精修套餐卡片配置
  RetouchCardConfig _getRetouchConfig() {
    final configIndex = cardIndex % 4;
    return PurchaseCardColorConfig.retouchConfigs[configIndex];
  }

  /// 获取AI积分卡片配置
  AigcCardConfig _getAigcConfig() {
    // 根据卡片索引确定档位：第4个(索引3)用低档，第5个(索引4)用中档，以此类推
    final cardType = PurchaseAigcCardType.fromString(model.type);
    return PurchaseCardColorConfig.aigcConfigs[cardType.level];
  }

  /// 构建顶部标签
  Widget _buildTopTag() {
    return Positioned(
      top: 0,
      right: 0,
      child: Image.network(
        model.tagIcon!.url,
        height: 30,
        fit: BoxFit.fitHeight,
        errorBuilder: (context, error, stackTrace) => const SizedBox.shrink(),
      ),
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent(bool isAigc) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 套餐名称
        _buildPackageName(isAigc),
        const SizedBox(height: 16),

        // 数量显示
        _buildQuantityRow(isAigc),

        // 加赠和总计（如果有）
        if (_hasExtraCount()) ...[
          const SizedBox(height: 8),
          _buildExtraAndTotalRow(isAigc),
        ],

        const Spacer(),

        // 价格
        _buildPriceRow(isAigc),
        const SizedBox(height: 12),

        // 购买按钮
        _buildBuyButton(isAigc),
        const SizedBox(height: 12),

        // 单价和永不过期
        _buildBottomText(isAigc),
      ],
    );
  }

  /// 构建套餐名称
  Widget _buildPackageName(bool isAigc) {
    final packageName = model.productName;

    if (isAigc) {
      final config = _getAigcConfig();
      return Row(
        children: [
          // 图标也使用配置的颜色
          ShaderMask(
            shaderCallback: (bounds) => _createGradient(config.titleColor,
                    stops: config.titleColorStops)
                .createShader(bounds),
            child: Image.asset(
              'assets/icons/aigc_credit_star.png',
              width: 20,
              height: 20,
              color: Colors.white,
              errorBuilder: (context, error, stackTrace) =>
                  const SizedBox.shrink(),
            ),
          ),
          const SizedBox(width: 2),
          _buildGradientText(
            '积分',
            config.titleColor,
            TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.semiBold,
              fontSize: 16,
              height: 20 / 16,
            ),
            stops: config.titleColorStops,
          ),
        ],
      );
    }

    return Text(
      packageName,
      style: TextStyle(
        fontFamily: Fonts.defaultFontFamily,
        fontWeight: Fonts.semiBold,
        fontSize: 16,
        height: 20 / 16,
        color: const Color(0xFFFFFFFF),
      ),
    );
  }

  /// 构建数量显示行
  Widget _buildQuantityRow(bool isAigc) {
    final quantity = isAigc ? model.aigcCount : model.count;
    final unit = isAigc ? '点' : '张';

    if (isAigc) {
      final config = _getAigcConfig();
      return Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          _buildGradientText(
            '$quantity',
            config.totalCountColor,
            const TextStyle(
              fontFamily: 'DouyinSans',
              fontWeight: FontWeight.bold,
              fontSize: 32,
              height: 1.0,
            ),
            stops: config.totalCountColorStops,
          ),
          const SizedBox(width: 2),
          _buildGradientText(
            unit,
            config.totalCountUnitColor,
            TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.semiBold,
              fontSize: 14,
              height: 20 / 14,
            ),
            stops: config.totalCountUnitColorStops,
          ),
        ],
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          '$quantity',
          style: const TextStyle(
            fontFamily: 'DouyinSans',
            fontWeight: FontWeight.bold,
            fontSize: 32,
            height: 1.0,
            color: Color(0xFFFFFFFF),
          ),
        ),
        const SizedBox(width: 2),
        Text(
          unit,
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.semiBold,
            fontSize: 14,
            height: 20 / 14,
            color: const Color(0xFFFFFFFF),
          ),
        ),
      ],
    );
  }

  /// 检查是否有加赠
  bool _hasExtraCount() {
    return viewModel.getExtraCountDisplayText(model) != null;
  }

  /// 构建加赠和总计行
  Widget _buildExtraAndTotalRow(bool isAigc) {
    final extraText = viewModel.getExtraCountDisplayText(model);
    final totalText = viewModel.getTotalCountDisplayText(model);

    // 计算加赠张数容器的宽度
    double extraTextWidth = 0;
    if (extraText != null) {
      final extraTextStyle = TextStyle(
        fontFamily: Fonts.defaultFontFamily,
        fontWeight: Fonts.regular,
        fontSize: 12,
        height: 18 / 12,
        color: const Color(0xFFFFFFFF),
      );
      final extraTextPainter = TextPainter(
        text: TextSpan(text: extraText, style: extraTextStyle),
        textDirection: TextDirection.ltr,
      );
      extraTextPainter.layout();
      extraTextWidth = extraTextPainter.width + 17;
    }

    // 计算总计文字实际宽度
    double totalTextWidth = 0;
    TextStyle? totalTextStyle;
    if (totalText != null) {
      totalTextStyle = TextStyle(
        fontFamily: Fonts.defaultFontFamily,
        fontWeight: Fonts.semiBold,
        fontSize: 12,
        height: 18 / 12,
        color: isAigc ? const Color(0xFFFFFFFF) : const Color(0xFF1A1A1A),
      );
      final totalTextPainter = TextPainter(
        text: TextSpan(text: totalText, style: totalTextStyle),
        textDirection: TextDirection.ltr,
      );
      totalTextPainter.layout();
      totalTextWidth = totalTextPainter.width + 20;
    }

    return SizedBox(
      height: 22,
      child: Stack(
        children: [
          // 加赠张数
          if (extraText != null)
            Positioned(
              left: 0,
              top: 0,
              child: Container(
                key: const ValueKey('extra_count_container'),
                height: 22,
                width: extraTextWidth,
                padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0x26FFFFFF),
                  borderRadius: BorderRadius.circular(0),
                ),
                child: Text(
                  extraText,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: Fonts.regular,
                    fontSize: 12,
                    height: 18 / 12,
                    color: const Color(0xFFFFFFFF),
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
            ),

          // 总计张数（梯形背景）
          if (totalText != null && totalTextStyle != null)
            Positioned(
              left: extraText != null ? extraTextWidth - 4 : 0,
              top: 0,
              child: _buildTrapezoidBackground(
                  totalText, totalTextWidth, totalTextStyle, isAigc),
            ),
        ],
      ),
    );
  }

  /// 构建梯形背景的总计
  Widget _buildTrapezoidBackground(
      String text, double textWidth, TextStyle textStyle, bool isAigc) {
    final gradient = isAigc
        ? const LinearGradient(
            colors: [Color(0xFF2A2B33), Color(0xFF2A2B33)],
          )
        : _getBuyButtonGradient(false);
    return ClipPath(
      clipper: TrapezoidClipper(),
      child: Container(
        height: 22,
        width: textWidth,
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
        decoration: BoxDecoration(
          gradient: gradient,
        ),
        child: Center(
          child: Text(
            text,
            style: textStyle,
          ),
        ),
      ),
    );
  }

  /// 格式化价格显示
  String _formatPrice(double price) {
    String formatted = price.toStringAsFixed(2);
    if (formatted.endsWith('.00')) {
      return formatted.substring(0, formatted.length - 3);
    }
    if (formatted.endsWith('0') && formatted.contains('.')) {
      return formatted.substring(0, formatted.length - 1);
    }
    return formatted;
  }

  /// 构建价格行
  Widget _buildPriceRow(bool isAigc) {
    final price = double.tryParse(model.price.price) ?? 0.0;

    if (isAigc) {
      final config = _getAigcConfig();
      return SizedBox(
        height: 28,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            // 使用ShaderMask让货币符号和价格共享同一个渐变
            ShaderMask(
              shaderCallback: (bounds) => _createGradient(config.priceColor,
                      stops: config.priceColorStops)
                  .createShader(bounds),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    model.price.currency,
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.bold,
                      fontSize: 12,
                      height: 28 / 12,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatPrice(price),
                    style: const TextStyle(
                      fontFamily: 'DouyinSans',
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                      height: 28 / 24,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: 28,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            model.price.currency,
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.bold,
              fontSize: 12,
              height: 28 / 12,
              color: const Color(0xFFFFFFFF),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            _formatPrice(price),
            style: const TextStyle(
              fontFamily: 'DouyinSans',
              fontWeight: FontWeight.bold,
              fontSize: 24,
              height: 28 / 24,
              color: Color(0xFFFFFFFF),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建购买按钮
  Widget _buildBuyButton(bool isAigc) {
    if (isAigc) {
      final config = _getAigcConfig();

      if (config.purchaseButtonBorderWidth > 0 &&
          config.purchaseButtonBorderColor != null) {
        // 有边框的按钮（高档）
        return Container(
          width: 188,
          height: 36,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            gradient: _createGradient(config.purchaseButtonBorderColor!,
                stops: config.purchaseButtonBorderColorStops),
          ),
          child: Container(
            margin: EdgeInsets.all(config.purchaseButtonBorderWidth),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(6 - config.purchaseButtonBorderWidth),
              gradient: _createGradient(config.purchaseButtonBgColor,
                  stops: config.purchaseButtonBgColorStops),
            ),
            child: Center(
              child: _buildGradientText(
                '立即购买',
                config.purchaseButtonTitleColor,
                TextStyle(
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                  fontSize: 12,
                  height: 16 / 12,
                ),
                stops: config.purchaseButtonTitleColorStops,
              ),
            ),
          ),
        );
      } else {
        // 无边框的按钮（低档、中档）
        return Container(
          width: 188,
          height: 36,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            gradient: _createGradient(config.purchaseButtonBgColor,
                stops: config.purchaseButtonBgColorStops),
          ),
          child: Center(
            child: _buildGradientText(
              '立即购买',
              config.purchaseButtonTitleColor,
              TextStyle(
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
                fontSize: 12,
                height: 16 / 12,
              ),
              stops: config.purchaseButtonTitleColorStops,
            ),
          ),
        );
      }
    } else {
      // 精修套餐按钮
      return Container(
        width: 188,
        height: 36,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          gradient: _getBuyButtonGradient(isAigc),
        ),
        child: Center(
          child: Text(
            '立即购买',
            style: TextStyle(
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.semiBold,
              fontSize: 12,
              height: 16 / 12,
              color: const Color(0xFF1A1A1A),
            ),
          ),
        ),
      );
    }
  }

  /// 获取购买按钮渐变
  Gradient _getBuyButtonGradient(bool isAigc) {
    if (isAigc) {
      return const LinearGradient(
        colors: [Color(0xFF2A2B33), Color(0xFF2A2B33)],
      );
    } else {
      // 精修套餐按钮渐变，使用配置
      final config = _getRetouchConfig();
      return LinearGradient(
        begin: Alignment.centerRight,
        end: Alignment.centerLeft,
        colors: config.buttonColors,
        stops: config.buttonColorStops,
      );
    }
  }

  /// 构建底部文字
  Widget _buildBottomText(bool isAigc) {
    final perPriceText = viewModel.getPerPriceDisplayText(model);

    return Center(
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: TextStyle(
            fontFamily: Fonts.defaultFontFamily,
            fontWeight: Fonts.regular,
            fontSize: 12,
            height: 18 / 12,
            color: const Color(0xCCFFFFFF),
          ),
          children: [
            if (!isAigc) TextSpan(text: perPriceText),
            if (!isAigc) const TextSpan(text: ' · '),
            const TextSpan(text: '永不过期'),
          ],
        ),
      ),
    );
  }

  /// 创建渐变效果
  Gradient _createGradient(List<Color> colors, {List<double>? stops}) {
    if (colors.length == 1) {
      return LinearGradient(colors: [colors[0], colors[0]]);
    } else {
      return LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: colors,
        stops: stops,
      );
    }
  }

  /// 构建渐变文字
  Widget _buildGradientText(String text, List<Color> colors, TextStyle style,
      {List<double>? stops}) {
    if (colors.length == 1) {
      return Text(
        text,
        style: style.copyWith(color: colors[0]),
      );
    } else {
      return ShaderMask(
        shaderCallback: (bounds) => LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: colors,
          stops: stops,
        ).createShader(bounds),
        child: Text(
          text,
          style: style.copyWith(color: Colors.white),
        ),
      );
    }
  }
}

/// 梯形裁剪器
class TrapezoidClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    const skew = 4.0;

    path.moveTo(skew, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
