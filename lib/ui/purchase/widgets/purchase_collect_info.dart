import 'package:pg_turing_collect_event/model.dart';

/// 用于购买页面埋点信息传递
/// 由于购买页面在一次生命周期内需要共用一个 pay_action_id，因此从 sku 页面进入到支付页
/// 面时需要传递一些参数
class PurchaseCollectInfo {
  final String timestamp;
  final SourceType sourceType;
  final String? userId;
  final String? productId;
  final String? productName;
  final String? productPrice;

  PurchaseCollectInfo({
    required this.timestamp,
    required this.sourceType,
    this.userId,
    this.productId,
    this.productName,
    this.productPrice,
  });
}
