import 'package:flutter/material.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

class PurchaseCustomServiceDialog extends StatelessWidget {
  final VoidCallback? onPurchaseConsult;
  final VoidCallback? onTechSupport;
  final VoidCallback? onMarketConsult;

  const PurchaseCustomServiceDialog({
    super.key,
    this.onPurchaseConsult,
    this.onTechSupport,
    this.onMarketConsult,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 底层透明图层，用于响应手势
        Positioned.fill(
          child: GestureDetector(
            onTap: () {
              // 点击后关闭对话框
              PGDialog.dismiss(tag: DialogTags.customService);
            },
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
        // 中间的弹窗内容
        Positioned(
          left: 0,
          right: 0,
          top: MediaQuery.of(context).size.height / 2 -
              148 / 2 +
              140, // 中心点向下偏移140像素
          child: Center(
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                // 主内容
                Container(
                  width: 140,
                  height: 148,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF121315),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildServiceItem(
                        icon: const AssetImage(
                            'assets/icons/purchase_custom_service.png'),
                        title: '购买咨询',
                        onTap: () {
                          PGDialog.dismiss(tag: DialogTags.customService);
                          onPurchaseConsult?.call();
                        },
                      ),
                      _buildServiceItem(
                        icon: const AssetImage(
                            'assets/icons/purchase_custom_suport.png'),
                        title: '技术支持',
                        onTap: () {
                          PGDialog.dismiss(tag: DialogTags.customService);
                          onTechSupport?.call();
                        },
                      ),
                      _buildServiceItem(
                        icon: const AssetImage(
                            'assets/icons/purchase_custom_chat.png'),
                        title: '市场咨询',
                        onTap: () {
                          PGDialog.dismiss(tag: DialogTags.customService);
                          onMarketConsult?.call();
                        },
                      ),
                    ],
                  ),
                ),
                // 底部倒三角
                Positioned(
                  bottom: -8, // 位于容器底部外侧，高度调整为8
                  left: 0,
                  right: 0,
                  child: Center(
                    child: CustomPaint(
                      size: const Size(16, 8), // 宽度16，高度8
                      painter: TrianglePainter(
                        color: const Color(0xFF121315),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildServiceItem({
    required AssetImage icon,
    required String title,
    VoidCallback? onTap,
  }) {
    return HoverItem(
      builder: (context, isHovered) {
        return GestureDetector(
          onTap: onTap,
          child: Container(
            height: 44,
            decoration: BoxDecoration(
              color: isHovered
                  ? Colors.white.withOpacity(0.05)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  icon.assetName,
                  fit: BoxFit.cover,
                  width: 24,
                  height: 24,
                ),
                const SizedBox(width: 4),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: const Color(0xFFEBEDF5).withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static void show({
    VoidCallback? onPurchaseConsult,
    VoidCallback? onTechSupport,
    VoidCallback? onMarketConsult,
  }) {
    PGDialog.showCustomDialog(
      width: double.infinity, // 全屏宽度
      height: double.infinity, // 全屏高度
      needBlur: false,
      tag: DialogTags.customService,
      backgroundColor: Colors.black.withOpacity(0.01),
      child: PurchaseCustomServiceDialog(
        onPurchaseConsult: onPurchaseConsult,
        onTechSupport: onTechSupport,
        onMarketConsult: onMarketConsult,
      ),
    );
  }
}

/// 绘制倒三角形状
class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, size.height) // 底部中点
      ..lineTo(0, 0) // 左上角
      ..lineTo(size.width, 0) // 右上角
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 悬停检测小部件
class HoverItem extends StatefulWidget {
  final Widget Function(BuildContext context, bool isHovered) builder;

  const HoverItem({super.key, required this.builder});

  @override
  State<HoverItem> createState() => _HoverItemState();
}

class _HoverItemState extends State<HoverItem> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: widget.builder(context, _isHovered),
    );
  }
}
