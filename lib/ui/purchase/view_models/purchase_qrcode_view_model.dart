import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/datalayer/repository/purchase_repository.dart';
import 'package:turing_art/providers/purchase_state_provider.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class PurchaseQRCodeViewModel extends ChangeNotifier {
  final PurchaseRepository _purchaseRepository;
  final PurchaseStateProvider _purchaseStateProvider;
  PaymentChannel _selectedChannel;
  // 当前订单id(从上一级组件传入,失效后刷新)
  String _currentOrderId = '';

  // 和上一级组件传入的支付渠道一致
  PurchaseQRCodeViewModel(
    this._purchaseRepository,
    this._selectedChannel,
    this._currentOrderId,
    this._purchaseStateProvider,
  ) {
    PGLog.d('创建了新的 PurchaseQRCodeViewModel 实例');

    // 创建完实例，开始已有订单状态轮询
    if (_currentOrderId.isNotEmpty) {
      _startOrderStatusCheck();
    }
  }

  // 添加 getter 以便外部访问当前选择的支付渠道
  PaymentChannel get currentSelectedChannel => _selectedChannel;

  // 获取当前二维码URL
  String? get currentQrUrl => _purchaseRepository.currentQrUrl;

  // 获取当前套餐价格
  String? get currentPlanPrice => _purchaseRepository.currentPurchasePrice;

  // 添加 getter 以便外部访问PurchaseRepository
  PurchaseRepository get purchaseRepository => _purchaseRepository;

  // 获取订单id
  String? get currentOrderId => _currentOrderId;

  // 创建订单(公开方法，可以在订单失效时重新创建)
  Future<bool> createOrder(String planId) async {
    final orderId =
        await _purchaseRepository.createOrder(planId, _selectedChannel);
    // 如果创建订单成功，开始轮询订单状态
    if (orderId != null) {
      _currentOrderId = orderId;
      _startOrderStatusCheck();
    }
    return orderId != null;
  }

  // 切换支付渠道
  Future<bool> setSelectedChannel(PaymentChannel channel) async {
    _selectedChannel = channel;
    // 通知UI开始loading
    notifyListeners();
    // 切换支付渠道时重新创建订单
    final isSuccess =
        await createOrder(_purchaseRepository.currentPurchasePlanId ?? "");
    // 切换支付渠道时，通知所有监听者（Consumer）更新UI
    notifyListeners();
    return isSuccess;
  }

  // 开始订单状态检查
  void _startOrderStatusCheck() {
    // 尝试获取当前的购买计划信息并开始轮询
    final purchasePlanId = _purchaseRepository.currentPurchasePlanId;

    // 获取价格信息
    String? purchasePrice = _purchaseRepository.purchasePriceNum;
    String? purchaseName = _purchaseRepository.currentPurchasePlanName;

    _purchaseStateProvider.startOrderStatusCheck(
      _currentOrderId,
      purchasePlanId: purchasePlanId,
      purchasePlanName: purchaseName,
      purchasePlanPrice: purchasePrice,
    );
  }

  // 关闭支付窗口时候调用清除购买计划
  void closeQrCodeDialogWithCleanPurchasePlan() {
    PGLog.d('关闭支付窗口: 清除购买计划');
    // 清除购买计划所有相关缓存
    _purchaseRepository.clearCurrentPurchasePlan();

    // 关闭弹窗
    PGDialog.dismiss(tag: DialogTags.purchaseQRCode);
  }

  // 只是网络原因导致的二维码图片加载失败，需要重新load当前二维码
  Future<void> refreshCurrentQrCode() async {
    try {
      // 通知UI更新
      notifyListeners();
    } catch (e) {
      PGLog.d('刷新二维码失败: $e');
    }
  }
}
