import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan.dart';
import 'package:turing_art/datalayer/domain/models/purchase_info_model/purchase_plan_type.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/purchase_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/ui/coupon/widget/coupon_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

enum PurchaseTabType {
  retouch, // 精修套餐
  aigcPoint, // AI积分
}

class PurchaseViewModel extends ChangeNotifier {
  final PurchaseRepository _purchaseRepository;
  final AccountRepository _accountRepository;
  final OpsCustomTableRepository _opsCustomTableRepository;

  PurchaseViewModel(this._purchaseRepository, this._accountRepository,
      this._opsCustomTableRepository,
      {PurchaseTabType? defaultTab}) {
    PGLog.d('创建了新的 PurchaseViewModel 实例');
    if (defaultTab != null) {
      _currentTab = defaultTab;
    }
    _currentTab == PurchaseTabType.retouch
        ? _loadPackages()
        : loadAigcPointPackages();
    _getAvailableChannels();
    // 增加刷新账户信息时机（QA期望）
    _refreshAccount();
  }

  // 当前选中的tab
  PurchaseTabType _currentTab = PurchaseTabType.retouch;
  PurchaseTabType get currentTab => _currentTab;

  // 当前选中的支付渠道
  PaymentChannel _selectedChannel = PaymentChannel.wechat;
  PaymentChannel get selectedChannel => _selectedChannel;

  List<PurchasePlan>? _packages;
  List<PurchasePlan> get packages => _packages ?? const [];

  // AIGC积分套餐
  List<PurchasePlan>? _aigcPointPackages;
  List<PurchasePlan> get aigcPointPackages => _aigcPointPackages ?? const [];

  List<PaymentChannel> _availableChannels = [];
  List<PaymentChannel> get availableChannels => _availableChannels;
  // 是否正在加载
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // 分页相关
  int _currentPage = 1;
  final int _itemsPerPage = 4;
  int get currentPage => _currentPage;
  int get itemsPerPage => _itemsPerPage;

  /// 获取总页数
  int get totalPages {
    final totalItems = currentTabPackages.length;
    return (totalItems / _itemsPerPage).ceil();
  }

  /// 是否可以上一页
  bool get canGoPrevious => _currentPage > 1;

  /// 是否可以下一页
  bool get canGoNext => _currentPage < totalPages;

  /// 区分是点击动画中还是滑动吸附
  bool _isJumpPageAnimation = false;

  void setIsJumpPageAnimation({required bool value}) {
    _isJumpPageAnimation = value;
  }

  /// 上一页
  void previousPage() {
    if (canGoPrevious) {
      _currentPage = _currentPage - 1;
      // 不和手动拖动混一起后需要自己通知UI
      notifyListeners();
    }
  }

  /// 下一页
  void nextPage() {
    if (canGoNext) {
      _currentPage++;
      notifyListeners();
    }
  }

  /// 根据滚动位置更新页码
  void updatePageByScrollPosition(
      double scrollOffset, double cardWidth, double cardSpacing) {
    // 点击了上一页下一页的page已经定值，不需要计算并更新
    if (currentTabPackages.length <= _itemsPerPage || _isJumpPageAnimation) {
      return;
    }

    final cardWithSpacing = cardWidth + cardSpacing;
    final totalCards = currentTabPackages.length;

    // 计算当前可见区域能显示多少个卡片
    const visibleAreaWidth = 1000.0; // 可见区域宽度

    // 计算第一个可见卡片的索引
    final firstVisibleCardIndex = (scrollOffset / cardWithSpacing).floor();

    // 计算最后一个可见卡片的索引
    final lastVisibleCardIndex =
        ((scrollOffset + visibleAreaWidth) / cardWithSpacing).floor();

    int newPage;

    // 特殊处理：如果最后一个卡片已经可见，或者滚动位置接近最后一页的右对齐位置
    if (lastVisibleCardIndex > totalCards) {
      newPage = totalPages;
    } else {
      // 检查是否接近最后一页的右对齐位置
      final lastPageItemCount = totalCards % _itemsPerPage;
      if (lastPageItemCount != 0) {
        // 使用提取的公共方法计算右对齐偏移量
        final rightAlignedOffset =
            _calculateLastPageRightAlignedOffset(cardWidth, cardSpacing);

        // 如果当前滚动位置接近或超过右对齐位置（允许一定误差）
        if (scrollOffset >= rightAlignedOffset - 50) {
          // 50px的误差范围
          newPage = totalPages;
        } else {
          // 正常情况：基于第一个可见卡片计算页码
          newPage = (firstVisibleCardIndex / _itemsPerPage).floor() + 1;
        }
      } else {
        // 正常情况：基于第一个可见卡片计算页码
        newPage = (firstVisibleCardIndex / _itemsPerPage).floor() + 1;
      }
    }

    final clampedPage = newPage.clamp(1, totalPages);
    if (_currentPage != clampedPage) {
      _currentPage = clampedPage;
      notifyListeners();
    }
  }

  /// 获取指定页面的滚动偏移量
  double getScrollOffsetForPage(
      int page, double cardWidth, double cardSpacing) {
    if (currentTabPackages.length <= _itemsPerPage) {
      return 0;
    }

    final cardWithSpacing = cardWidth + cardSpacing;
    final totalCards = currentTabPackages.length;

    // 如果是最后一页且不满4个卡片，需要特殊处理
    if (page == totalPages) {
      final lastPageItemCount = totalCards % _itemsPerPage;
      if (lastPageItemCount != 0) {
        final offset =
            _calculateLastPageRightAlignedOffset(cardWidth, cardSpacing);
        return offset;
      }
    }

    // 正常情况：计算到达指定页面需要的滚动偏移量
    final normalOffset = (page - 1) * _itemsPerPage * cardWithSpacing;
    return normalOffset;
  }

  /// 重置页码（切换tab时调用）
  void _resetPage() {
    _currentPage = 1;
  }

  /// 计算最后一页右对齐的滚动偏移量
  double _calculateLastPageRightAlignedOffset(
      double cardWidth, double cardSpacing) {
    final cardWithSpacing = cardWidth + cardSpacing;
    final totalCards = currentTabPackages.length;

    const visibleAreaWidth = 1000.0; // 可见区域宽度
    const leftPadding = 24.0;

    // 最后一个卡片的右边界位置
    final lastCardRightPosition = totalCards * cardWithSpacing - cardSpacing;

    // 计算让最后一个卡片右对齐所需的滚动偏移量
    final rightAlignedOffset =
        lastCardRightPosition - visibleAreaWidth + leftPadding * 2;

    // 确保偏移量不为负数
    return rightAlignedOffset.clamp(0.0, double.infinity);
  }

  /// 切换tab
  void switchTab(PurchaseTabType tab) {
    if (_currentTab != tab) {
      _currentTab = tab;
      _resetPage(); // 切换tab时重置页码
      notifyListeners();
    }
  }

  /// 获取当前tab的标题
  String get currentTabTitle {
    switch (_currentTab) {
      case PurchaseTabType.retouch:
        return '图灵精修套餐 无套路更实惠';
      case PurchaseTabType.aigcPoint:
        return '解锁更多创造力';
    }
  }

  /// 获取当前tab的副标题
  String get currentTabSubtitle {
    switch (_currentTab) {
      case PurchaseTabType.retouch:
        return '片量买多少就能用多少，到手永不过期没烦恼';
      case PurchaseTabType.aigcPoint:
        return '图灵精修AI积分 · 永不过期';
    }
  }

  /// 获取当前tab的套餐列表
  List<PurchasePlan> get currentTabPackages {
    switch (_currentTab) {
      case PurchaseTabType.retouch:
        if (packages.isEmpty) {
          _loadPackages();
        }
        return packages;
      case PurchaseTabType.aigcPoint:
        if (aigcPointPackages.isEmpty) {
          loadAigcPointPackages();
        }
        return aigcPointPackages;
      default:
        return [];
    }
  }

  /// 加载套餐数据
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<void> _loadPackages({bool forceRefresh = false}) async {
    if (_isLoading) {
      return;
    }
    _isLoading = true;

    try {
      _packages = await _purchaseRepository.loadPurchaseCards(
          forceRefresh: forceRefresh);
    } catch (e) {
      PGLog.d('加载套餐数据失败: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 加载AIGC积分套餐数据
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<void> loadAigcPointPackages({bool forceRefresh = false}) async {
    if (_isLoading) {
      return;
    }
    _isLoading = true;

    try {
      _aigcPointPackages = await _purchaseRepository.loadAigcPointPurchaseCards(
          forceRefresh: forceRefresh);
    } catch (e) {
      PGLog.d('加载AIGC积分套餐数据失败: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 刷新套餐数据，强制从服务器获取最新数据
  Future<void> refreshPackages() async {
    await _loadPackages(forceRefresh: true);
  }

  /// 刷新AIGC积分套餐数据，强制从服务器获取最新数据
  Future<void> refreshAigcPointPackages() async {
    await loadAigcPointPackages(forceRefresh: true);
  }

  // 获取当前所有可用的支付渠道
  Future<void> _getAvailableChannels() async {
    _availableChannels = await _opsCustomTableRepository.getAvailableChannels();
    // 默认选中第一个
    _selectedChannel = _availableChannels.first;
  }

  // 刷新账户信息
  void _refreshAccount() {
    _accountRepository.refreshAllAccount().catchError((e) {
      // 刷新失败不影响主流程，只打印日志
      PGLog.e('账户信息刷新失败: $e');
    });
  }

// 立即购买，默认创建第一个可用渠道类型的支付订单
  Future<String?> onBuyNowPressed(String purchasePlanId) async {
    try {
      // 默认可用订单第一个
      _selectedChannel = availableChannels.first;
      // 调用仓库层创建订单
      final orderId = await _purchaseRepository.createOrder(
          purchasePlanId, _selectedChannel);
      PGLog.d('创建订单结果: $orderId');
      return orderId;
    } catch (e) {
      PGLog.d('创建订单失败: $e');
      return null;
    }
  }

  void onContactServicePressed() {
    // TODO: 处理联系客服逻辑
  }

  void onRedeemCodePressed() {
    // 需求确认不关闭商品弹窗，就不区分是否是unity
    CouponDialog.show();
  }

  String getBackImageName(PurchasePlan model) {
    if (model.type == PurchasePlanType.basic.value) {
      return 'assets/icons/purchase_ty_bg.png';
    } else if (model.type == PurchasePlanType.pro.value) {
      return 'assets/icons/purchase_zs_bg.png';
    } else if (model.type == PurchasePlanType.enterprise.value) {
      return 'assets/icons/purchase_cz_bg.png';
    }
    return 'assets/icons/purchase_cz_bg.png';
  }

  double getPerPrice(PurchasePlan model) {
    // 将价格字符串转换为数值
    double priceValue = double.tryParse(model.price.price) ?? 0.0;

    // 针对AI积分套餐使用aigcCount
    if (_currentTab == PurchaseTabType.aigcPoint) {
      return model.aigcCount > 0
          ? priceValue / (model.aigcCount + model.aigcExtraCount)
          : 0.0;
    }

    // 精修套餐使用count
    return model.count > 0
        ? priceValue / (model.count + model.extraCount)
        : 0.0;
  }

  /// 获取积分数量显示文字
  String getCountDisplayText(PurchasePlan model) {
    if (_currentTab == PurchaseTabType.aigcPoint) {
      return '${model.aigcCount}点';
    }
    return '${model.count}张';
  }

  /// 获取额外积分数量显示文字
  String? getExtraCountDisplayText(PurchasePlan model) {
    if (_currentTab == PurchaseTabType.aigcPoint && model.aigcExtraCount > 0) {
      return '加赠${model.aigcExtraCount}点';
    } else if (_currentTab == PurchaseTabType.retouch && model.extraCount > 0) {
      return '加赠${model.extraCount}张';
    }
    return null;
  }

  /// 获取总计显示文字
  String? getTotalCountDisplayText(PurchasePlan model) {
    if (_currentTab == PurchaseTabType.aigcPoint && model.aigcExtraCount > 0) {
      return '总计${model.aigcCount + model.aigcExtraCount}点';
    } else if (_currentTab == PurchaseTabType.retouch && model.extraCount > 0) {
      return '总计${model.count + model.extraCount}张';
    }
    return null;
  }

  /// 获取单价显示文字
  String getPerPriceDisplayText(PurchasePlan model) {
    if (_currentTab == PurchaseTabType.aigcPoint) {
      return '每积分约${getPerPrice(model).toStringAsFixed(2)}元';
    }
    return '每张约${getPerPrice(model).toStringAsFixed(2)}元';
  }
}
