import 'package:flutter/material.dart';
import 'package:pg_turing_collect_event/collect/customaction/popup_action.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/version_update/view_models/version_update_view_model.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:pg_turing_collect_event/collect/customaction/popup_action.dart'
    as collect;
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/update_manager.dart';

class VersionUpdateDialog extends StatefulWidget {
  const VersionUpdateDialog({
    super.key,
    this.onDownloadComplete,
    this.onDownloadError,
  });

  final VoidCallback? onDownloadComplete;
  final Function(String error)? onDownloadError;

  /// 显示版本更新对话框
  static void show(
    BuildContext context, {
    VoidCallback? onDownloadComplete,
    Function(String error)? onDownloadError,
  }) {
    if (PGDialog.isDialogVisible(DialogTags.versionUpdate)) {
      PGLog.d('VersionUpdateDialog show, but dialog already exist, return');
      return;
    }
    // 显示对话框
    PGDialog.showCustomDialog(
      width: 600,
      height: _estimatWidgetHeight(),
      tag: DialogTags.versionUpdate,
      needBlur: true,
      // 使用ChangeNotifierProvider.value提供已初始化的ViewModel
      child: ChangeNotifierProvider.value(
        value: VersionUpdateViewModel(),
        child: VersionUpdateDialog(
          onDownloadComplete: onDownloadComplete,
          onDownloadError: onDownloadError,
        ),
      ),
    );
  }

  /// 计算对话框高度
  static double _estimatWidgetHeight() {
    double contentHeight = 512.0;
    String contentStr =
        UpdateManager().handler.matchingVersion?.description ?? '';
    if (contentStr.isNotEmpty) {
      final updateFeatures =
          VersionUpdateDialog.parseUpdateFeatures(contentStr);
      contentHeight =
          VersionUpdateDialog.calculateContentHeight(updateFeatures) + 251;
    }
    return contentHeight;
  }

  // 解析更新内容
  static List<String> parseUpdateFeatures(String description) {
    // 按换行符分割描述文本
    final features = description.split('\n');

    // 移除空行
    return features.where((feature) => feature.trim().isNotEmpty).toList();
  }

  // 计算内容高度
  static double calculateContentHeight(List<String> features) {
    // 每行文本的估计高度（包括行间距）
    const double lineHeight = 24.0; // 14px字体 + 8px底部间距

    // 计算总高度
    double totalHeight = features.length * lineHeight;

    // 限制在48到264之间
    return totalHeight.clamp(48.0, 264.0);
  }

  @override
  State<VersionUpdateDialog> createState() => _VersionUpdateDialogState();
}

class _VersionUpdateDialogState extends State<VersionUpdateDialog> {
  final ScrollController _scrollController = ScrollController();

  // 在_VersionUpdateDialogState类中添加一个变量来跟踪是否为强制更新
  bool get _isForceUpdate => UpdateManager().handler.needsForceUpdate();

  // 添加一个状态变量来跟踪是否显示渐变
  bool _showGradient = false;

  // 用于埋点事件中记录用户id
  String? _userId;

  @override
  void initState() {
    super.initState();
    // 添加滚动监听器
    _scrollController.addListener(_updateGradientVisibility);

    // 添加延迟检查，确保在布局完成后检查是否需要显示渐变
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateGradientVisibility();
    });

    _userId ??= context.read<CurrentUserRepository>().user?.effectiveId;;
    recordEvent(collect.Action.show);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateGradientVisibility);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取ViewModel
    final viewModel = context.read<VersionUpdateViewModel>();

    // 解析更新内容
    final updateFeatures =
        VersionUpdateDialog.parseUpdateFeatures(viewModel.contentStr);

    // 计算内容高度
    final contentHeight =
        VersionUpdateDialog.calculateContentHeight(updateFeatures);

    // 计算整个对话框的高度
    final dialogHeight = contentHeight + 251;

    return Container(
      width: 600,
      height: dialogHeight,
      padding: const EdgeInsets.only(left: 16, right: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF121415),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTop(context),
          const SizedBox(height: 8),
          _buildContent(context),
          const SizedBox(height: 8),
          _buildDownloadInfo(context),
          const SizedBox(height: 24),
          _buildBottomBar(context),
        ],
      ),
    );
  }

  // 顶部编辑栏
  Widget _buildTop(BuildContext context) {
    return SizedBox(
      height: 56,
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            '更新',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.semiBold,
            ),
          ),
          const Spacer(),
          // 强制更新时不显示关闭按钮
          if (!_isForceUpdate)
            SizedBox(
              width: 24,
              height: 24,
              child: IconButton(
                icon: Icon(Icons.close, color: Colors.white.withAlpha(150)),
                onPressed: () {
                  recordEvent(collect.Action.close);
                  PGDialog.dismiss(tag: DialogTags.versionUpdate);
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                iconSize: 24,
              ),
            ),
        ],
      ),
    );
  }

  // 内容区
  Widget _buildContent(BuildContext context) {
    final viewModel = context.read<VersionUpdateViewModel>();

    // 解析更新内容
    final updateFeatures =
        VersionUpdateDialog.parseUpdateFeatures(viewModel.contentStr);

    // 计算内容高度（原始高度，不受限制）
    final rawContentHeight = updateFeatures.length * 24.0; // 每行24像素高

    // 判断内容是否需要滚动 - 使用原始高度与最大高度比较
    final bool needsScrolling = rawContentHeight > 264;

    return SizedBox(
      width: 552,
      height: 69 + VersionUpdateDialog.calculateContentHeight(updateFeatures),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(viewModel.title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
              )),
          const SizedBox(height: 8),
          // 使用Stack包装ConstrainedBox和渐变层
          Stack(
            children: [
              // 使用ConstrainedBox限制高度
              ConstrainedBox(
                constraints: const BoxConstraints(
                  minHeight: 48,
                  maxHeight: 264,
                ),
                child: Theme(
                  data: ThemeData(
                    scrollbarTheme: ScrollbarThemeData(
                      thickness: MaterialStateProperty.all(4),
                      radius: const Radius.circular(2),
                      thumbColor: MaterialStateProperty.all(
                        const Color(0xFFEBEDF5).withOpacity(0.15),
                      ),
                    ),
                  ),
                  child: RawScrollbar(
                    controller: _scrollController,
                    thickness: 4,
                    radius: const Radius.circular(2),
                    thumbVisibility: needsScrolling,
                    thumbColor: const Color(0xFFEBEDF5).withOpacity(0.15),
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          for (final feature in updateFeatures)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Text(
                                feature,
                                style: TextStyle(
                                  color: const Color(0xFFEBF2F5).withAlpha(150),
                                  fontSize: 14,
                                  fontFamily: Fonts.defaultFontFamily,
                                  fontWeight: Fonts.regular,
                                ),
                              ),
                            ),
                          // 添加额外的底部空间，确保最后一行文本不会被渐变遮挡
                          if (needsScrolling) const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // 底部渐变层 - 根据_showGradient显示或隐藏
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                height: 30,
                child: Visibility(
                  visible: _showGradient,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0xFF121415).withOpacity(0),
                          const Color(0xFF121415),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    final viewModel = context.read<VersionUpdateViewModel>();

    // 如果是强制更新且正在下载中，不显示任何按钮
    if (_isForceUpdate && viewModel.status == DownloadStatus.downloading) {
      return const SizedBox(height: 56);
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 取消按钮 - 如果是强制更新则隐藏
        if (!_isForceUpdate)
          SizedBox(
            width: 80,
            height: 40,
            child: TextButton(
              onPressed: () {
                if (viewModel.status == DownloadStatus.downloading) {
                  viewModel.pauseDownload();
                }
                recordEvent(collect.Action.close);
                PGDialog.dismiss(tag: DialogTags.versionUpdate);
              },
              style: TextButton.styleFrom(
                backgroundColor: const Color(0xFF1B1E1F),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                viewModel.status == DownloadStatus.downloading
                    ? '取消更新'
                    : '稍后更新',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: Fonts.defaultFontFamily,
                  fontWeight: Fonts.semiBold,
                ),
              ),
            ),
          ),

        if (!_isForceUpdate) const SizedBox(width: 12),

        // 确认按钮
        SizedBox(
          width: 80,
          height: 40,
          child: TextButton(
            onPressed: () async {
              recordEvent(collect.Action.click);
              if (viewModel.status == DownloadStatus.downloading) {
                // 后台更新
                PGDialog.dismiss(tag: DialogTags.versionUpdate);
              } else if (viewModel.status == DownloadStatus.paused) {
                // 继续更新
                viewModel.resumeDownload();
              } else if (viewModel.status == DownloadStatus.completed) {
                // 立即安装
                final file = UpdateManager().getDownloadedFile();
                if (file != null) {
                  await viewModel.installUpdate(file);
                }
              } else {
                // 立即更新
                await FileManager()
                    .deleteDir(path: viewModel.updateDownloadPath);
                viewModel.startDownload(viewModel.downloadUrl);
              }
            },
            style: TextButton.styleFrom(
              backgroundColor: const Color(0xFFF72561),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              viewModel.status == DownloadStatus.downloading
                  ? '后台更新'
                  : viewModel.status == DownloadStatus.paused
                      ? '继续更新'
                      : viewModel.status == DownloadStatus.completed
                          ? '立即安装'
                          : '立即更新',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDownloadInfo(BuildContext context) {
    final viewModel = context.watch<VersionUpdateViewModel>();
    // 根据状态显示不同的下载信息
    String statusText = switch (viewModel.status) {
      DownloadStatus.notStarted => "未开始",
      DownloadStatus.downloading => "下载中",
      DownloadStatus.paused => "已暂停",
      DownloadStatus.completed => "已完成",
      DownloadStatus.failed => "下载出错",
      DownloadStatus.canceled => "已取消",
    };

    return SizedBox(
      width: 552,
      height: 30,
      child: Column(
        children: [
          SizedBox(
            height: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (viewModel.status == DownloadStatus.downloading ||
                    viewModel.status == DownloadStatus.completed)
                  Text(
                      viewModel.status == DownloadStatus.downloading
                          ? '已下载：${viewModel.downloadedSize}/${viewModel.totalSize}'
                          : '已完成',
                      style: TextStyle(
                        color: const Color(0xFFEBF2F5).withAlpha(150),
                        fontSize: 12,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.regular,
                      )),
                const Spacer(),
                if (viewModel.status == DownloadStatus.downloading)
                  Text('下载速度：${viewModel.speed}',
                      style: TextStyle(
                        color: const Color(0xFFEBF2F5).withAlpha(150),
                        fontSize: 12,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.regular,
                      )),
                const SizedBox(width: 8),
                if (viewModel.status == DownloadStatus.downloading)
                  Text(
                      viewModel.status == DownloadStatus.downloading
                          ? '大约剩余：${viewModel.remainingTime}'
                          : statusText,
                      style: TextStyle(
                        color: const Color(0xFFEBF2F5).withAlpha(150),
                        fontSize: 12,
                        fontFamily: Fonts.defaultFontFamily,
                        fontWeight: Fonts.regular,
                      )),
              ],
            ),
          ),
          const SizedBox(height: 5),
          if (viewModel.status == DownloadStatus.downloading ||
              viewModel.status == DownloadStatus.completed)
            SizedBox(
                height: 5,
                child: Stack(
                  children: [
                    // 背景
                    Container(
                      width: 552,
                      height: 5,
                      decoration: BoxDecoration(
                        color: const Color(0xFF181C1F),
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    // 进度
                    FractionallySizedBox(
                      widthFactor: viewModel.progress,
                      child: Container(
                        width: 552,
                        height: 5,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF72561),
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                  ],
                ))
        ],
      ),
    );
  }

  // 更新渐变可见性的方法
  void _updateGradientVisibility() {
    // 如果可以滚动且不在底部，显示渐变
    final bool shouldShowGradient = _scrollController.hasClients &&
        _scrollController.position.pixels <
            _scrollController.position.maxScrollExtent;

    if (shouldShowGradient != _showGradient) {
      setState(() {
        _showGradient = shouldShowGradient;
      });
    }
  }

  void recordEvent(collect.Action action) {
    collect.recordPopupAction(
        subElementId: collect.SubElementId.popup_upgrade,
        userId: _userId ?? '',
        action: action);
  }
}
