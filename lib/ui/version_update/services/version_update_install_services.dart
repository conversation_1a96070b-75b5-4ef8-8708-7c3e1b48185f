import 'dart:io';

import 'package:turing_art/ui/version_update/handler/version_update_install_handler.dart';
import 'package:turing_art/ui/version_update/handler/version_update_install_interface.dart';
import 'package:turing_art/utils/app_constants.dart';

/// 版本更新安装服务
/// 用于安装更新
class VersionUpdateInstallServices {
  static final isWindows = AppConstants.isWindows;
  final VersionUpdateInstallInterface _handler;

  VersionUpdateInstallServices._({
    required VersionUpdateInstallInterface handler,
  }) : _handler = handler;

  factory VersionUpdateInstallServices.forPlatform() {
    VersionUpdateInstallInterface handler =
        WindowsVersionUpdateInstallHandler();
    if (Platform.isMacOS) {
      handler = MacOSVersionUpdateInstallHandler();
    } else if (Platform.isIOS) {
      handler = IOSVersionUpdateInstallHandler();
    } else if (Platform.isAndroid) {
      handler = AndroidVersionUpdateInstallHandler();
    }
    return VersionUpdateInstallServices._(handler: handler);
  }

  /// 安装更新
  Future<(bool, String)> installUpdate(String filePath) async {
    return await _handler.installUpdate(filePath);
  }
}
