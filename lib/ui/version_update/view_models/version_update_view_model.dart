import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:turing_art/ui/version_update/services/version_update_install_services.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/update_manager.dart';

// 版本更新ViewModel
class VersionUpdateViewModel extends ChangeNotifier {
  final UpdateManager _updateManager = UpdateManager();

  // 版本更新安装服务
  final VersionUpdateInstallServices _versionUpdateInstallServices =
      VersionUpdateInstallServices.forPlatform();

  String get versionName =>
      _updateManager.handler.matchingVersion?.version ?? '';
  String get contentStr =>
      _updateManager.handler.matchingVersion?.description ?? '';
  String get title => _updateManager.handler.matchingVersion?.title ?? '';

  // 获取取消按钮文本
  String get cancelTitle {
    if (_updateManager.status == DownloadStatus.downloading) {
      return '取消更新';
    } else if (_updateManager.status == DownloadStatus.paused) {
      return '取消更新';
    } else if (_updateManager.status == DownloadStatus.completed) {
      return '稍后安装';
    }
    return '稍后更新';
  }

  // 获取确认按钮文本
  String get confirmTitle {
    if (_updateManager.status == DownloadStatus.downloading) {
      return '后台更新';
    } else if (_updateManager.status == DownloadStatus.paused) {
      return '继续更新';
    } else if (_updateManager.status == DownloadStatus.completed) {
      return '立即安装';
    }
    return '立即更新';
  }

  // 下载状态
  DownloadStatus get status => _updateManager.status;

  // 下载进度
  double get progress => _updateManager.progress;

  // 下载速度
  String get speed => _updateManager.getFormattedSpeed();

  // 已下载大小
  String get downloadedSize => _updateManager.getFormattedDownloadedSize();

  // 总大小
  String get totalSize => _updateManager.getFormattedTotalSize();

  // 剩余时间
  String get remainingTime => _updateManager.getFormattedRemainingTime();

  // 下载信息流
  Stream<Map<String, dynamic>> get downloadInfoStream =>
      _updateManager.downloadInfoStream;

  // 下载状态流
  Stream<DownloadStatus> get statusStream => _updateManager.statusStream;

  // 下载进度流
  Stream<double> get progressStream => _updateManager.progressStream;

  // 订阅
  StreamSubscription<Map<String, dynamic>>? _infoSubscription;

  // 下载URL
  String get downloadUrl => _updateManager.handler.getDownloadUrl() ?? '';

  // 是否强制更新
  bool get isForceUpdate => _updateManager.handler.needsForceUpdate();

  // 添加公共方法获取下载路径
  String get updateDownloadPath => _updateManager.downloadDirPath;

  // 添加公共方法获取包名
  String? get packageName =>
      _updateManager.handler.matchingVersion?.packageName;

  VersionUpdateViewModel() {
    // 监听下载信息变化
    _infoSubscription = downloadInfoStream.listen((info) {
      notifyListeners();
    });
  }

  // 开始下载
  Future<bool> startDownload(String url) async {
    try {
      await _updateManager.startDownload(
        url,
        onProgress: (progress, speed, remainingTime) {
          // 进度更新时的回调
          notifyListeners();
        },
        onComplete: (file) async {
          // 下载完成时的回调
          PGLog.d('下载完成: ${file.path}');

          // 强制更新UI
          notifyListeners();

          // 延迟一小段时间再次通知，确保UI更新
          Future.delayed(const Duration(milliseconds: 100), () {
            notifyListeners();
          });
        },
        onError: (error) {
          // 下载错误时的回调
          PGLog.e('下载错误: $error');
          notifyListeners();

          // 显示错误提示
          PGDialog.showToast(error);
          return false;
        },
      );
      return true;
    } catch (e) {
      PGLog.e('启动下载失败: $e');
      PGDialog.showToast('启动下载失败');
      return false;
    }
  }

  // 暂停下载
  Future<void> pauseDownload() async {
    await _updateManager.pauseDownload();
    notifyListeners();
  }

  // 恢复下载
  Future<void> resumeDownload() async {
    await _updateManager.resumeDownload(
      onProgress: (progress, speed, remainingTime) {
        notifyListeners();
      },
      onComplete: (file) {
        PGLog.d('下载完成: ${file.path}');
        notifyListeners();
        // _installUpdate(file);
      },
      onError: (error) {
        PGLog.e('下载错误: $error');
        notifyListeners();
        PGDialog.showToast('下载失败!');
      },
    );
  }

  // 取消下载
  Future<void> cancelDownload() async {
    // 如果正在下载，先暂停下载
    if (_updateManager.status == DownloadStatus.downloading) {
      await pauseDownload();
    }

    // 关闭更新对话框
    PGDialog.dismiss(tag: DialogTags.versionUpdate);
  }

  // 添加公共方法安装更新
  Future<void> installUpdate(File file) async {
    // 先检查文件是否存在
    final isExistDownloadFile = await _checkUpdateDownloadFileExist();
    if (!isExistDownloadFile) {
      PGDialog.showToast('安装包不存在,请重新下载~');
      _resetDownloadFileStatus();
      notifyListeners();
      return;
    }
    final (bool isSuccess, String errorMsg) =
        await _versionUpdateInstallServices.installUpdate(file.path);
    if (!isSuccess) {
      PGDialog.showToast(errorMsg);
      _resetDownloadFileStatus();
      notifyListeners();
    }
  }

  /// 检查更新下载文件是否存在
  Future<bool> _checkUpdateDownloadFileExist() async {
    return await _updateManager.isExistDownloadFileOfUserPreferencesRecords();
  }

  /// 重置下载状态
  void _resetDownloadFileStatus() {
    _updateManager.resetDownloadStatus();
  }

  // /// 判断文件是否是Windows平台的exe执行文件
  // bool _isWindowsExecutable(String filePath) {
  //   try {
  //     // 检查文件扩展名
  //     if (!filePath.toLowerCase().endsWith('.exe')) {
  //       return false;
  //     }

  //     // 检查文件是否存在
  //     final file = File(filePath);
  //     if (!file.existsSync()) {
  //       return false;
  //     }

  //     // 读取文件头部字节，检查是否是有效的PE文件
  //     // PE文件头部应该以"MZ"开头（DOS头部）
  //     final bytes = file.openSync().readSync(2);
  //     if (bytes.length >= 2 && bytes[0] == 77 && bytes[1] == 90) {
  //       // 'M' = 77, 'Z' = 90
  //       return true;
  //     }

  //     return false;
  //   } catch (e) {
  //     PGLog.e('检查Windows可执行文件失败: $e');
  //     return false;
  //   }
  // }

  @override
  void dispose() {
    _infoSubscription?.cancel();
    super.dispose();
  }
}
