import 'dart:io';

import 'package:turing_art/ui/version_update/handler/version_update_install_interface.dart';
import 'package:turing_art/utils/app_exit_util.dart';
import 'package:turing_art/utils/pg_log.dart';

/// Windows平台更新安装处理器
/// 用于Windows平台的更新安装逻辑
class WindowsVersionUpdateInstallHandler
    implements VersionUpdateInstallInterface {
  @override
  Future<(bool, String)> installUpdate(String filePath) async {
    // Windows平台安装逻辑
    if (!File(filePath).existsSync()) {
      PGLog.e('安装包不存在: $filePath');
      return (false, '安装包不存在');
    }
    if (_isWindowsExecutable(filePath)) {
      try {
        // 执行安装程序
        await Process.start(filePath, [], runInShell: true);
        // // 退出当前应用
        AppExitUtil.exitApp();
      } catch (e) {
        PGLog.e('启动安装程序失败: $e');
        return (false, '启动安装程序失败');
      }
    }
    return (false, '安装包存在异常，将重新下载或联系官方服务~');
  }

  /// 判断文件是否是Windows平台的exe执行文件
  bool _isWindowsExecutable(String filePath) {
    try {
      // 检查文件扩展名
      if (!filePath.toLowerCase().endsWith('.exe')) {
        return false;
      }

      // 检查文件是否存在
      final file = File(filePath);
      if (!file.existsSync()) {
        return false;
      }

      // 读取文件头部字节，检查是否是有效的PE文件
      // PE文件头部应该以"MZ"开头（DOS头部）
      final bytes = file.openSync().readSync(2);
      if (bytes.length >= 2 && bytes[0] == 77 && bytes[1] == 90) {
        // 'M' = 77, 'Z' = 90
        return true;
      }

      return false;
    } catch (e) {
      PGLog.e('检查Windows可执行文件失败: $e');
      return false;
    }
  }
}

/// macOS平台更新安装处理器
/// 用于macOS平台的更新安装逻辑
/// 注意：macOS平台需要使用DMG文件进行安装
class MacOSVersionUpdateInstallHandler
    implements VersionUpdateInstallInterface {
  @override
  Future<(bool, String)> installUpdate(String filePath) async {
    // macOS平台安装逻辑
    try {
      // 打开DMG文件
      await Process.start('open', [filePath], runInShell: true);
      return (true, '');
    } catch (e) {
      PGLog.e('打开安装包失败: $e');
      return (false, '打开安装包失败: $e');
    }
  }
}

/// iOS平台更新安装处理器
/// 用于iOS平台的更新安装逻辑
class IOSVersionUpdateInstallHandler implements VersionUpdateInstallInterface {
  @override
  Future<(bool, String)> installUpdate(String filePath) async {
    // iOS平台安装逻辑
    try {
      // 跳转到App Store进行安装
      await Process.start('open', ['-a', 'App Store', filePath],
          runInShell: true);
      return (true, '');
    } catch (e) {
      PGLog.e('打开安装包失败: $e');
      return (false, '打开安装包失败: $e');
    }
  }
}

/// Android平台更新安装处理器
/// 用于Android平台的更新安装逻辑
/// 注意：Android平台需要使用APK文件进行安装
class AndroidVersionUpdateInstallHandler
    implements VersionUpdateInstallInterface {
  @override
  Future<(bool, String)> installUpdate(String filePath) async {
    // Android平台安装逻辑
    try {
      // TODO: 添加Android平台安装逻辑
      return (true, '');
    } catch (e) {
      PGLog.e('启动安装程序失败: $e');
      return (false, '启动安装程序失败');
    }
  }
}
