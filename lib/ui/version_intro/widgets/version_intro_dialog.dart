import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/ops_operation/operation_activity.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class VersionIntroDialog extends StatefulWidget {
  // 版本介绍列表
  final List<OperationActivity> versionIntroList;

  const VersionIntroDialog({
    super.key,
    required this.versionIntroList,
  });

  /// 显示对话框
  static void show(List<OperationActivity> versionIntroList) {
    if (PGDialog.isDialogVisible(DialogTags.versionIntro)) {
      PGLog.d('VersionIntroDialog show, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialog(
      width: 750,
      height: 460,
      needBlur: false,
      tag: DialogTags.versionIntro,
      child: VersionIntroDialog(
        versionIntroList: versionIntroList,
      ),
    );
  }

  /// 在Unity窗口上显示对话框
  static void showOnUnity(List<OperationActivity> versionIntroList) {
    if (PGDialog.isDialogVisible(DialogTags.versionIntro)) {
      PGLog.d(
          'VersionIntroDialog showOnUnity, but dialog already exist, return');
      return;
    }
    PGDialog.showCustomDialogOnUnity(
      width: 750,
      height: 460,
      needBlur: false,
      tag: DialogTags.versionIntro,
      child: VersionIntroDialog(
        versionIntroList: versionIntroList,
      ),
    );
  }

  @override
  State<VersionIntroDialog> createState() => _VersionIntroDialogState();
}

class _VersionIntroDialogState extends State<VersionIntroDialog> {
  int _selectedIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  // 处理选择新的版本介绍
  void _selectNewIntro(int index) {
    if (index == _selectedIndex) {
      PGLog.d('已经选中了当前版本介绍');
      return;
    }

    setState(() {
      _selectedIndex = index;
    });

    // 滚动到选中的项
    _scrollToSelectedItem();
  }

  // 滚动到选中的项
  void _scrollToSelectedItem() {
    // 延迟执行，确保在布局完成后滚动
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        // 计算需要滚动的位置
        const itemHeight = 40.0; // 每个项的高度
        const containerHeight = 298.0; // 列表容器高度
        final targetPosition = _selectedIndex * itemHeight;
        final currentPosition = _scrollController.position.pixels;
        final maxScrollExtent = _scrollController.position.maxScrollExtent;

        // 计算选中项在当前视口中的位置
        final itemTopInViewport = targetPosition - currentPosition;
        final itemBottomInViewport = itemTopInViewport + itemHeight;

        // 如果项目完全可见，则不需要滚动
        if (itemTopInViewport >= 0 && itemBottomInViewport <= containerHeight) {
          return;
        }

        // 计算最优滚动位置
        double optimalPosition;

        if (itemTopInViewport < 0) {
          // 项目在视口上方，需要向上滚动
          optimalPosition = targetPosition;
        } else {
          // 项目在视口下方，需要向下滚动
          optimalPosition = targetPosition - containerHeight + itemHeight;
        }

        // 确保不超过最大滚动范围
        optimalPosition = optimalPosition.clamp(0.0, maxScrollExtent);

        // 只有当需要滚动的距离超过阈值时才进行滚动
        final scrollDistance = (optimalPosition - currentPosition).abs();
        if (scrollDistance > 5.0) {
          _scrollController.animateTo(
            optimalPosition,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });
  }

  // 点击下一项
  void _onNextButtonPressed() {
    // -2,是因为第一个activity是标题，不是item
    if (_selectedIndex < widget.versionIntroList.length - 2) {
      _selectNewIntro(_selectedIndex + 1);
    } else {
      // 已经是最后一项，关闭对话框
      PGDialog.dismiss(tag: DialogTags.versionIntro);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLastItem = _selectedIndex == widget.versionIntroList.length - 2;
    // 当前选中的item
    final titleItem = widget.versionIntroList.first;
    final mainList = widget.versionIntroList.sublist(1);

    final currentItem = mainList[_selectedIndex];

    return Container(
      width: 750,
      height: 460,
      decoration: BoxDecoration(
        color: const Color(0xFF1B1C1F),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF33353B),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.2),
            offset: const Offset(0, 4),
            blurRadius: 40,
          ),
        ],
      ),
      child: Row(
        children: [
          // 左侧媒体区域
          Expanded(
            child: Container(
              width: 530,
              height: 460,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
              ),
              child: _buildMediaContent(currentItem),
            ),
          ),

          // 右侧内容区域
          Container(
            width: 220,
            height: 460,
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(
                  color: Color(0xFF33353B),
                  width: 1,
                ),
              ),
            ),
            child: Stack(
              children: [
                // 关闭按钮
                Positioned(
                  top: 12,
                  left: 184,
                  child: GestureDetector(
                    onTap: () {
                      PGDialog.dismiss(tag: DialogTags.versionIntro);
                    },
                    child: Image.asset(
                      'assets/icons/home_window_close.png',
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),

                // 标题
                Positioned(
                  top: 44,
                  left: 24,
                  child: Text(
                    titleItem.title ?? '',
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: Fonts.medium,
                      fontSize: 18,
                      height: 22 / 18,
                      color: const Color(0xFFFFFFFF),
                    ),
                  ),
                ),

                // 内容列表
                Positioned(
                  top: 82,
                  left: 0,
                  child: _buildListContent(mainList),
                ),

                // 下一项按钮
                Positioned(
                  top: 412,
                  left: 16,
                  child: GestureDetector(
                    onTap: _onNextButtonPressed,
                    child: Container(
                      width: 188,
                      height: 32,
                      decoration: BoxDecoration(
                        color: isLastItem
                            ? const Color(0xFFF72561)
                            : const Color(0xFF33353B),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: Text(
                          isLastItem ? '知道了' : '下一项',
                          style: TextStyle(
                            fontFamily: Fonts.defaultFontFamily,
                            fontWeight: Fonts.medium,
                            fontSize: 12,
                            color: const Color(0xFFFFFFFF),
                            height: 1,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建列表内容
  Widget _buildListContent(List<OperationActivity> mainList) {
    return SizedBox(
      width: 220,
      height: 298,
      child: RawScrollbar(
        thumbVisibility: true,
        trackVisibility: false,
        thickness: 4,
        radius: const Radius.circular(20),
        thumbColor: const Color(0xFFEBEDF5).withOpacity(0.4),
        controller: _scrollController,
        child: ListView.builder(
          controller: _scrollController,
          padding: EdgeInsets.zero,
          itemCount: mainList.length,
          itemBuilder: (context, index) {
            final item = mainList[index];
            final isSelected = index == _selectedIndex;

            return GestureDetector(
              onTap: () {
                _selectNewIntro(index);
              },
              child: Container(
                width: 220,
                height: 40,
                padding: const EdgeInsets.only(
                  top: 7,
                  right: 10,
                  bottom: 7,
                  left: 24,
                ),
                decoration: BoxDecoration(
                  gradient: isSelected
                      ? LinearGradient(
                          colors: [
                            const Color(0xFF33353B),
                            const Color(0xFF33353B).withOpacity(0.0),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        )
                      : null,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.title ?? item.name,
                        style: TextStyle(
                          fontFamily: Fonts.defaultFontFamily,
                          fontWeight: isSelected ? Fonts.medium : Fonts.regular,
                          fontSize: 14,
                          color: isSelected
                              ? const Color(0xFFFFFFFF)
                              : const Color(0xFFFFFFFF).withOpacity(0.65),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildMediaContent(OperationActivity currentItem) {
    // 创建边框圆角裁剪
    const borderRadius = BorderRadius.only(
      topLeft: Radius.circular(8),
      bottomLeft: Radius.circular(8),
    );

    // 显示图片，如果有的话
    if (currentItem.image != null && currentItem.image!.url.isNotEmpty) {
      return ClipRRect(
        borderRadius: borderRadius,
        child: Container(
          width: 530,
          height: 460,
          color: Colors.transparent,
          child: Image.network(
            currentItem.image!.url,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) {
                return child;
              }
              return const Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Color(0xFFF72561),
                    strokeWidth: 2.0,
                  ),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Icon(Icons.error, color: Colors.white),
              );
            },
          ),
        ),
      );
    }

    // 如果没有图片或视频，显示默认背景
    return ClipRRect(
      borderRadius: borderRadius,
      child: Container(
        width: 530,
        height: 460,
        color: Colors.transparent,
        child: const Center(
          child: Text(
            '暂无内容',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
