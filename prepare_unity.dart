import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as p;

void main(List<String> arguments) {
  // 默认配置
  Map<String, dynamic> config = {
    'projectName': 'turing_art',
    'isPackage': false,
    'isWin7': false,
    'isRelease': false,
    'sourceDataFile': 'Retouch_Data'
  };

  // 如果提供了参数，尝试读取JSON文件
  if (arguments.isNotEmpty) {
    try {
      final configFile = File(arguments[0]);
      if (configFile.existsSync()) {
        final jsonContent = configFile.readAsStringSync();
        Map<String, dynamic> userConfig = json.decode(jsonContent);
        config.addAll(userConfig);
        print('从文件加载配置：${json.encode(config)}');
      } else {
        print('配置文件不存在：${arguments[0]}');
      }
    } catch (e) {
      print('读取配置文件失败：$e');
    }
  } else {
    print('使用默认配置：${json.encode(config)}');
  }

  // 从配置中获取参数
  final isPackage = config['isPackage'] as bool;
  final isRelease = config['isRelease'] as bool;
  final isWin7 = config['isWin7'] as bool;

  // 获取当前脚本所在目录
  final currentDir = Directory.current;

  // 根据isRelease和isWin7确定源文件夹路径
  final baseDir = isRelease ? 'release' : 'develop';
  final unityDir = Directory(p.join(
      currentDir.path, 'unityoutput${isWin7 ? 'win7' : 'win10p'}', baseDir));
  final extraDllDir =
      Directory(p.join(currentDir.path, 'windows', 'packaging', 'extra'));

  // 获取目标文件夹路径
  final targetDir = Directory(p.join(currentDir.path, 'build', 'windows', 'x64',
      'runner', isPackage ? 'Release' : 'Debug'));

  // 检查源目录是否存在
  if (!unityDir.existsSync()) {
    print('❌ Unity源目录不存在: ${unityDir.path}');
    exit(1);
  }

  if (!extraDllDir.existsSync()) {
    print('❌ Extra DLL目录不存在: ${extraDllDir.path}');
    exit(1);
  }

  // 确保目标目录存在
  targetDir.createSync(recursive: true);
  if (!targetDir.existsSync()) {
    print('❌ 无法创建目标目录: ${targetDir.path}');
    exit(1);
  }

  print('📁 源目录: ${unityDir.path}');
  print('📁 目标目录: ${targetDir.path}');

  try {
    // 复制Unity文件
    print('🔄 开始复制Unity文件...');
    copyDirSync(unityDir, targetDir, isRelease);

    // 复制额外DLL文件
    print('🔄 开始复制额外DLL文件...');
    copyDirSync(extraDllDir, targetDir, isRelease);

    // 重命名数据文件夹
    final sourceDataPath = p.join(targetDir.path, config['sourceDataFile']);
    final targetDataPath =
        p.join(targetDir.path, '${config['projectName']}_Data');

    if (Directory(sourceDataPath).existsSync()) {
      rename(sourceDataPath, targetDataPath);
      print('✅ 数据文件夹重命名成功');
    } else {
      print('❌ 源数据文件夹不存在: $sourceDataPath');
      exit(1);
    }

    print('✅ 所有文件复制完成');
  } catch (e) {
    print('❌ 文件复制过程中发生错误: $e');
    exit(1);
  }
}

void rename(String oldPath, String newPath) {
  try {
    // 如果源路径和目标路径相同，直接返回
    if (oldPath == newPath) {
      print('⏭️ 源路径和目标路径相同，跳过重命名: $oldPath');
      return;
    }

    // 如果目标文件夹已存在，先删除它
    if (Directory(newPath).existsSync()) {
      Directory(newPath).deleteSync(recursive: true);
      print('⚠️ 目标文件夹已存在，已删除: $newPath');
    }

    // 执行重命名
    Directory(oldPath).renameSync(newPath);
    print('✅ 文件夹重命名成功: $oldPath -> $newPath');
  } catch (e) {
    print('❌ 文件夹重命名失败: $e');
    exit(1);
  }
}

void copyDirSync(Directory sourceDir, Directory targetDir, bool isRelease) {
  print('📁 开始复制目录: ${sourceDir.path} -> ${targetDir.path}');

  // 确保目标目录存在
  targetDir.createSync(recursive: true);

  try {
    // 统计计数器
    int fileCount = 0;
    int dirCount = 0;
    int skippedCount = 0;
    List<String> skippedFiles = [];

    // 复制所有文件和子目录
    sourceDir.listSync(recursive: true).forEach((FileSystemEntity entity) {
      final relativePath = p.relative(entity.path, from: sourceDir.path);
      final newPath = p.join(targetDir.path, relativePath);

      if (entity is File) {
        // 排除.exe文件和在release模式下排除git_log.txt文件
        if (!entity.path.toLowerCase().endsWith('retouch.exe') &&
            !(isRelease &&
                p.basename(entity.path).toLowerCase() == 'git_log.txt')) {
          // 确保目标目录存在
          Directory(p.dirname(newPath)).createSync(recursive: true);
          entity.copySync(newPath);
          fileCount++;

          // 不再打印每个文件路径，而是每复制100个文件显示一次进度
          if (fileCount % 100 == 0) {
            print('⏱️ 已复制 $fileCount 个文件...');
          }
        } else {
          skippedCount++;
          skippedFiles.add(entity.path);
          print('⏭️ 跳过文件: ${entity.path}');
        }
      } else if (entity is Directory) {
        // 在目标路径创建文件夹
        Directory(newPath).createSync(recursive: true);
        dirCount++;
      }
    });

    print('✅ 目录复制完成: ${sourceDir.path}');
    print('📊 统计: 复制了 $fileCount 个文件, $dirCount 个目录, 跳过了 $skippedCount 个文件');

    if (skippedFiles.isNotEmpty) {
      print('📋 跳过的文件列表:');
      for (var file in skippedFiles) {
        print('   - $file');
      }
    }
  } catch (e) {
    print('❌ 目录复制失败: $e');
    exit(1);
  }
}
