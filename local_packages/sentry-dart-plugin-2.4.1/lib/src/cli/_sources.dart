// FILE GENERATED BY scripts/update-cli.sh - DO NOT MODIFY BY HAND

import 'package:sentry_dart_plugin/src/cli/sources.dart';

import 'host_platform.dart';

const _version = '2.41.1';

final currentCLISources = {
  HostPlatform.darwinUniversal: CLISource(
    'sentry-cli-Darwin-universal',
    _version,
    '4b6703bb6160accb7d3552128945357910ff273fde5d5a8aa4d1668958bb9472',
  ),
  HostPlatform.linuxAarch64: CLISource(
    'sentry-cli-Linux-aarch64',
    _version,
    '****************************************************************',
  ),
  HostPlatform.linuxArmv7: CLISource(
    'sentry-cli-Linux-armv7',
    _version,
    '****************************************************************',
  ),
  HostPlatform.linux64bit: CLISource(
    'sentry-cli-Linux-x86_64',
    _version,
    '****************************************************************',
  ),
  HostPlatform.windows32bit: CLISource(
    'sentry-cli-Windows-i686.exe',
    _version,
    '8a25bfb2c550a067966dc330ce386da56a9343cf8cefebc096b7ee4c915e759f',
  ),
  HostPlatform.windows64bit: CLISource(
    'sentry-cli-Windows-x86_64.exe',
    _version,
    '34878a7f30c3da8861455a2e8a8cfbd5a861649e11fe88432e9b610c83650896',
  ),
};
