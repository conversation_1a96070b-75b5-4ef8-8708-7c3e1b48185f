# Contributing to sentry-dart-plugin

We love pull requests from everyone. 
We suggest opening an issue to discuss bigger changes before investing on a big PR.

# Requirements

The project currently requires you run Dart version >= `2.12.0`.

# Run

To build:

```shell
dart compile aot-snapshot bin/sentry_dart_plugin.dart
```

To run:

```shell
dart run

// or

dartaotruntime bin/sentry_dart_plugin.aot
```

# CI

Build is automatically run against branches and pull requests via GH Actions.
