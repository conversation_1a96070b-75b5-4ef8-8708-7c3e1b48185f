name: Update Dependencies

on:
  # Run every day.
  schedule:
    - cron: '0 3 * * *'
  # And on on every PR merge so we get the updated dependencies ASAP, and to make sure the changelog doesn't conflict.
  push:
    branches:
      - main

jobs:
  cli:
    uses: getsentry/github-workflows/.github/workflows/updater.yml@v2
    with:
      path: scripts/update-cli.sh
      name: CLI
      pr-strategy: update
    secrets:
      api-token: ${{ secrets.CI_DEPLOY_KEY }}
