name: '<PERSON><PERSON> Dart Plugin'
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
jobs:
  build:
    name: 'Format, Fix, Analyze and Build'
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        sdk: [stable, beta]

    steps:
      - uses: dart-lang/setup-dart@v1
        with:
          sdk: ${{ matrix.sdk }}

      - uses: actions/checkout@v4

      - name: 'Pub Get'
        run: |
          dart pub get

      - name: 'Format, Fix & Analyze'
        run: |
          dart format .
          dart fix --apply
          dart analyze --fatal-infos

      # actions/checkout fetches only a single commit in a detached HEAD state. Therefore
      # we need to pass the current branch, otherwise we can't commit the changes.
      # GITHUB_HEAD_REF is the name of the head branch. GitHub Actions only sets this for PRs.
      - run: scripts/commit-formatted-code.sh $GITHUB_HEAD_REF
        if: env.GITHUB_HEAD_REF != null

      - run: dart test --exclude-tags integration

      - name: Build
        run: |
          dart compile aot-snapshot bin/sentry_dart_plugin.dart

  package-analysis:
    if: ${{ !startsWith(github.ref, 'refs/heads/release/') }}
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4
      - uses: axel-op/dart-package-analyzer@7a6c3c66bce78d82b729a1ffef2d9458fde6c8d2 #v3
        id: analysis
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
      - name: 'Check scores'
        env:
          TOTAL: ${{ steps.analysis.outputs.total }}
          TOTAL_MAX: ${{ steps.analysis.outputs.total_max }}
        run: |
          PERCENTAGE=$(( $TOTAL * 100 / $TOTAL_MAX ))
          if (( $PERCENTAGE < 60 ))
          then
            echo Score too low!
            exit 1
          fi
