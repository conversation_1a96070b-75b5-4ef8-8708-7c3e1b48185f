default_platform(:ios)

platform :ios do
  desc "Build perf-test app without Sentry"
  lane :build_perf_test_app_plain do
    setup_ci

    sync_code_signing(
      type: "development",
      readonly: true,
      app_identifier: ["io.sentry.dart.perfTestAppPlain"]
    )

    build_app(
      workspace: "perf-test-app-plain/ios/Runner.xcworkspace",
      scheme: "Runner",
      include_bitcode: false,
      include_symbols: false,
      export_method: "development",
      export_team_id: CredentialsManager::AppfileConfig.try_fetch_value(:team_id),
      output_name: "test-app-plain.ipa",
      skip_build_archive: true,
      archive_path: "perf-test-app-plain/build/ios/archive/Runner.xcarchive",
    )

    delete_keychain(name: "fastlane_tmp_keychain") unless is_ci
  end

  desc "Build perf-test app with Sentry"
  lane :build_perf_test_app_with_sentry do
    setup_ci

    sync_code_signing(
      type: "development",
      readonly: true,
      app_identifier: ["io.sentry.dart.perfTestAppWithSentry"]
    )

    build_app(
      workspace: "perf-test-app-with-sentry/ios/Runner.xcworkspace",
      scheme: "Runner",
      include_bitcode: false,
      include_symbols: false,
      export_method: "development",
      export_team_id: CredentialsManager::AppfileConfig.try_fetch_value(:team_id),
      output_name: "test-app-sentry.ipa",
      skip_build_archive: true,
      archive_path: "perf-test-app-with-sentry/build/ios/archive/Runner.xcarchive",
    )

    delete_keychain(name: "fastlane_tmp_keychain") unless is_ci
  end

end
