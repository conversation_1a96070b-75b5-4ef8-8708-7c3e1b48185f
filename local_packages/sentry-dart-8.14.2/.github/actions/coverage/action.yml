name: Dart tests
description: Run Dart Tests and collect coverage
inputs:
  directory:
    description: The directory to run tests in
    required: false
    default: ''
  coverage:
    description: Codecov name
    required: false
    default: ''
  min-coverage:
    description: Minimum coverage percentage
    required: false
    default: '0'
  token:
    description: Codecov token
    required: true

runs:
  using: composite

  steps:
    - uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # pin@v3
      if: ${{ inputs.coverage != '' }}
      with:
        name: ${{ inputs.coverage != '' }}
        files: ./${{ inputs.directory }}/coverage/lcov.info
        token: ${{ inputs.token }}

    - uses: VeryGoodOpenSource/very_good_coverage@c953fca3e24a915e111cc6f55f03f756dcb3964c # pin@v3.0.0
      if: ${{ inputs.coverage != '' }}
      with:
        path: './${{ inputs.directory }}/coverage/lcov.info'
        min_coverage: ${{ inputs.min-coverage }}
        exclude: 'lib/src/native/cocoa/binding.dart'
