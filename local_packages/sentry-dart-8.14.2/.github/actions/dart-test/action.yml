name: Dart tests
description: Run Dart tests
inputs:
  directory:
    description: The directory to run tests in
    required: false
    default: ''
  web:
    description: Whether to run tests for web
    required: false
    default: 'true'

runs:
  using: composite

  steps:
    - uses: dart-lang/setup-dart@f0ead981b4d9a35b37f30d36160575d60931ec30 # pin@v1
      with:
        sdk: ${{ matrix.sdk }}

    - run: dart pub get
      shell: bash
      working-directory: ${{ inputs.directory }}

    - name: Test VM
      working-directory: ${{ inputs.directory }}
      shell: bash
      run: |
        testCmd="dart test -p vm --test-randomize-ordering-seed=random --chain-stack-traces"
        if ${{ (matrix.os == 'ubuntu' && matrix.sdk == 'stable' && 'true') || 'false' }} ; then
          $testCmd --coverage=coverage
          dart run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --packages=.dart_tool/package_config.json --report-on=lib
        else
          $testCmd
        fi

    - name: Test dart2js
      if: ${{ inputs.web == 'true' }}
      run: dart test -p chrome --test-randomize-ordering-seed=random --chain-stack-traces
      shell: bash
      working-directory: ${{ inputs.directory }}

    - name: Test dart2wasm
      if: ${{ inputs.web == 'true' && (matrix.sdk == 'stable' || matrix.sdk == 'beta') && runner.os != 'Windows' }}
      run: dart test -p chrome --compiler dart2wasm --test-randomize-ordering-seed=random --chain-stack-traces
      shell: bash
      working-directory: ${{ inputs.directory }}
