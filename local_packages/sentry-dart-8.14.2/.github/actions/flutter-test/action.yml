name: Flutter tests
description: Run Flutter tests
inputs:
  directory:
    description: The directory to run tests in
    required: false
    default: ''

runs:
  using: composite

  steps:
    - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
      with:
        channel: ${{ matrix.sdk }}

    - uses: actions/setup-java@v4
      if: ${{ matrix.target == 'android' }}
      with:
        java-version: '17'
        distribution: 'adopt'

    # Install required dependencies for Flutter on Linux on Ubuntu
    - name: "Setup Linux"
      if: matrix.target == 'linux'
      shell: bash
      run: |
        sudo apt-get update -qq
        sudo apt-get install -qq -y cmake dbus libblkid-dev libgtk-3-dev liblzma-dev ninja-build pkg-config xvfb libcurl4-openssl-dev network-manager upower

    - run: flutter pub get
      shell: bash
      working-directory: ${{ inputs.directory }}

    - name: Test VM
      if: matrix.target == 'linux' || matrix.target == 'windows' || matrix.target == 'macos'
      working-directory: ${{ inputs.directory }}
      shell: bash
      run: |
        testCmd="flutter test ${{ (inputs.directory == 'isar' && '-j 1') || '' }} --test-randomize-ordering-seed=random"
        if ${{ (matrix.target == 'linux' && matrix.sdk == 'stable' && 'true') || 'false' }} ; then
          $testCmd --coverage
          if [[ '${{ inputs.directory }}' == 'flutter' ]] ; then
            dart run remove_from_coverage -f coverage/lcov.info -r 'binding.dart'
          fi
        else
          $testCmd
        fi

    - name: Test web (${{ matrix.target }})
      if: matrix.target == 'js' || matrix.target == 'wasm'
      working-directory: ${{ inputs.directory }}
      shell: bash
      run: |
        flutter test --platform chrome ${{ (matrix.target == 'wasm' && '--wasm') || '' }} --test-randomize-ordering-seed=random --exclude-tags canvasKit
        flutter test --platform chrome ${{ (matrix.target == 'wasm' && '--wasm') || '' }} --test-randomize-ordering-seed=random --tags canvasKit
