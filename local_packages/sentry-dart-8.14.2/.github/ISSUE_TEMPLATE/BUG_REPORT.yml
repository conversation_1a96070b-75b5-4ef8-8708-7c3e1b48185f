name: 🐞 Bug Report
description: Tell us about something that's not working the way we (probably) intend.
labels: ["Platform: Dart", "bug"]
type: Bug
body:
  - type: dropdown
    id: environment
    attributes:
      label: Platform
      description: Which platforms does this bug affect?
      multiple: true
      options:
        - Dart
        - Dart Web
        - Flutter Mobile iOS
        - Flutter Mobile Android
        - Flutter Desktop Windows
        - Flutter Desktop Linux
        - Flutter Web
        - Flutter Wasm
    validations:
      required: true

  - type: dropdown
    id: obfuscate
    attributes:
      label: Obfuscation
      description: Is `obfuscate` enabled?
      options:
        - Enabled
        - Disabled
    validations:
      required: true

  - type: dropdown
    id: split-debug-info
    attributes:
      label: Debug Info
      description: Is `split-debug-info` enabled?
      options:
        - Enabled
        - Disabled
    validations:
      required: true

  - type: textarea
    id: doctor
    attributes:
      label: Doctor
      description: Output of the command `flutter doctor -v` or <PERSON><PERSON>'s version?
      placeholder: |-
        1. foo
        2. bar
        3. baz
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: Version
      description: Which version of Sentry do you use?
      placeholder: 7.0.0 ← should look like this
    validations:
      required: true

  - type: textarea
    id: repro
    attributes:
      label: Steps to Reproduce
      description: How can we see what you're seeing? Specific is terrific.
      placeholder: |-
        1. foo
        2. bar
        3. baz
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Result
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Result
      description: Logs? Screenshots? Yes, please.
    validations:
      required: true

  - type: dropdown
    id: submit-a-pr
    attributes:
      label: Are you willing to submit a PR?
      description: We accept contributions!
      options:
        - "Yes"
        - "No"

  - type: markdown
    attributes:
      value: |-
        ## Thanks 🙏
        Check our [triage docs](https://open.sentry.io/triage/) for what to expect next.
