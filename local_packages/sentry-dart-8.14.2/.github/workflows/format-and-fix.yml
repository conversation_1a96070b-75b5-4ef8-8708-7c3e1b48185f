on:
  workflow_dispatch:

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  format-and-fix:
    name: Format & fix code
    if: ${{ !startsWith(github.ref, 'refs/heads/release/') }}
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        package: [
          {name: dart, sdk: dart},
          {name: dio, sdk: dart},
          {name: file, sdk: dart},
          {name: flutter, sdk: flutter},
          {name: logging, sdk: dart},
          {name: sqflite, sdk: flutter},
        ]
    defaults:
      run:
        working-directory: ${{ matrix.package.name }}
    steps:
      - uses: actions/checkout@v4
      - uses: dart-lang/setup-dart@e51d8e571e22473a2ddebf0ef8a2123f0ab2c02c # pin@v1
        if: ${{ matrix.package.sdk == 'dart' }}
      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        if: ${{ matrix.package.sdk == 'flutter' }}

      - run: ${{ matrix.package.sdk }} pub get

      - run: dart format .

      - run: dart fix --apply

        # Source: https://stackoverflow.com/a/58035262
      - name: Extract branch name
        shell: bash
        run: echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
        id: extract_branch

        # actions/checkout fetches only a single commit in a detached HEAD state. Therefore
        # we need to pass the current branch, otherwise we can't commit the changes.
        # GITHUB_HEAD_REF is the name of the head branch. GitHub Actions only sets this for PRs.
      - name: Commit & push
        run: ./scripts/commit-code.sh ${{ steps.extract_branch.outputs.branch }} "Format & fix code"
