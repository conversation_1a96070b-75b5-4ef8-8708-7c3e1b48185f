name: sentry-hive
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - '!**/*.md'
      - '!**/class-diagram.svg'
      - '.github/workflows/hive.yml'
      - '.github/workflows/analyze.yml'
      - '.github/actions/dart-test/**'
      - '.github/actions/coverage/**'
      - 'dart/**'
      - 'hive/**'

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  build:
    name: '${{ matrix.os }} | ${{ matrix.sdk }}'
    runs-on: ${{ matrix.os }}-latest
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        os: [macos, ubuntu, windows]
        sdk: [stable, beta]

    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/dart-test
        with:
          directory: hive
          web: false

      - uses: ./.github/actions/coverage
        if: runner.os == 'Linux' && matrix.sdk == 'stable'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: hive
          coverage: sentry_hive
          min-coverage: 55

  analyze:
    uses: ./.github/workflows/analyze.yml
    with:
      package: hive
      panaThreshold: 90
