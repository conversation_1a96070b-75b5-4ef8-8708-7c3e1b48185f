name: e2e-sentry-dart
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - "!**/*.md"
      - "!**/class-diagram.svg"
      - ".github/workflows/e2e_dart.yml"
      - '.github/workflows/analyze.yml'
      - "dart/**"
      - "e2e_test/**"

env:
  SENTRY_AUTH_TOKEN_E2E: ${{ secrets.SENTRY_AUTH_TOKEN_E2E }}
  SENTRY_DIST: 1

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  build:
    name: E2E
    runs-on: "ubuntu-latest"
    timeout-minutes: 30
    defaults:
      run:
        working-directory: ./e2e_test
    strategy:
      fail-fast: false
      matrix:
        sdk: [stable, beta]
    steps:
      - uses: dart-lang/setup-dart@e51d8e571e22473a2ddebf0ef8a2123f0ab2c02c # pin@v1
        with:
          sdk: ${{ matrix.sdk }}
      - uses: actions/checkout@v4
      - name: Run
        if: env.SENTRY_AUTH_TOKEN != null
        run: |
          dart pub get
          dart run --define=SENTRY_ENVIRONMENT=e2e

  analyze:
    uses: ./.github/workflows/analyze.yml
    with:
      package: e2e_test
      panaThreshold: 0
