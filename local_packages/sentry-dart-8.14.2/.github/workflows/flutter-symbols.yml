name: Flutter symbols collection
on:
  schedule:
    # Run once an hour. It takes just a couple of minutes because of status caching.
    - cron: '10 * * * *'
  workflow_dispatch:
    inputs:
      flutter_version:
        description: Flutter version, can be either a specific version (3.17.0) or a wildcard (3.2.*)
        required: false
        type: string
        default: '3.*.*'

defaults:
  run:
    working-directory: scripts/flutter_symbol_collector

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: dart-lang/setup-dart@e51d8e571e22473a2ddebf0ef8a2123f0ab2c02c # pin@v1

      - run: dart pub get

      - run: dart test

  run:
    needs: [test]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: dart-lang/setup-dart@e51d8e571e22473a2ddebf0ef8a2123f0ab2c02c # pin@v1

      - run: dart pub get

      - name: Download status cache of previously processed files
        run: |
          gh run download --name 'flutter-symbol-collector-database' --dir .cache
          grep -r "" .cache
        continue-on-error: true
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - run: dart run bin/flutter_symbol_collector.dart --version=${{ inputs.flutter_version || '3.*.*' }}
        timeout-minutes: 300
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - name: Upload updated status cache of processed files
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: flutter-symbol-collector-database
          path: scripts/flutter_symbol_collector/.cache
          include-hidden-files: true
          overwrite: true
          retention-days: 90
          if-no-files-found: error
