name: sentry-drift
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - '!**/*.md'
      - '!**/class-diagram.svg'
      - '.github/workflows/drift.yml'
      - '.github/workflows/analyze.yml'
      - '.github/actions/flutter-test/**'
      - '.github/actions/coverage/**'
      - 'dart/**'
      - 'drift/**'

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  build:
    name: '${{ matrix.target }} | ${{ matrix.sdk }}'
    runs-on: ${{ matrix.target == 'linux' && 'ubuntu' || matrix.target }}-latest
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        target: [macos, linux, windows]
        sdk: [stable, beta]

    steps:
      - uses: actions/checkout@v4

      - name: Install libsqlite3
        if: matrix.target == 'linux'
        run: sudo apt-get -y install libsqlite3-dev

      - uses: ./.github/actions/flutter-test
        with:
          directory: drift

      - uses: ./.github/actions/coverage
        if: matrix.target == 'linux' && matrix.sdk == 'stable'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: drift
          coverage: sentry_drift
          min-coverage: 80

  analyze:
    uses: ./.github/workflows/analyze.yml
    with:
      package: drift
      sdk: flutter
