name: diagrams
on:
  workflow_dispatch:

jobs:
  diagrams:
    runs-on: ubuntu-latest
    name: "Create class diagrams of all packages"
    steps:
      - uses: dart-lang/setup-dart@e51d8e571e22473a2ddebf0ef8a2123f0ab2c02c # pin@v1
        with:
          sdk: stable

      - uses: actions/checkout@v4

      - name: dependencies
        run: |
          dart pub global activate lakos
          sudo apt update
          sudo apt install graphviz

      - name: dart
        working-directory: ./dart
        run: lakos . -i "{test/**,example/**,example_web/**}" | dot -Tsvg -o class-diagram.svg

      - name: flutter
        working-directory: ./flutter
        run: lakos . -i "{test/**,example/**}" | dot -Tsvg -o class-diagram.svg

      - name: dio
        working-directory: ./dio
        run: lakos . -i "{test/**,example/**}" | dot -Tsvg -o class-diagram.svg

      - name: file
        working-directory: ./file
        run: lakos . -i "{test/**,example/**}" | dot -Tsvg -o class-diagram.svg

      - name: sqflite
        working-directory: ./sqflite
        run: lakos . -i "{test/**,example/**}" | dot -Tsvg -o class-diagram.svg

      - name: logging
        working-directory: ./logging
        run: lakos . -i "{test/**,example/**}" | dot -Tsvg -o class-diagram.svg

      - name: hive
        working-directory: ./hive
        run: lakos . -i "{test/**,example/**}" | dot -Tsvg -o class-diagram.svg

      - name: isar
        working-directory: ./isar
        run: lakos . -i "{test/**,example/**}" | dot -Tsvg -o class-diagram.svg

        # Source: https://stackoverflow.com/a/58035262
      - name: Extract branch name
        shell: bash
        run: echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
        id: extract_branch

        # actions/checkout fetches only a single commit in a detached HEAD state. Therefore
        # we need to pass the current branch, otherwise we can't commit the changes.
        # GITHUB_HEAD_REF is the name of the head branch. GitHub Actions only sets this for PRs.
      - name: Commit & push
        run: ./scripts/commit-code.sh ${{ steps.extract_branch.outputs.branch }} "Update class diagrams"
