name: Changes In High Risk Code
on:
  pull_request:

# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  files-changed:
    name: Detect changed files
    runs-on: ubuntu-latest
    # Map a step output to a job output
    outputs:
      high_risk_code: ${{ steps.changes.outputs.high_risk_code }}
      high_risk_code_files: ${{ steps.changes.outputs.high_risk_code_files }}
    steps:
      - uses: actions/checkout@v4
      - name: Get changed files
        id: changes
        uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        with:
          token: ${{ github.token }}
          filters: .github/file-filters.yml

          # Enable listing of files matching each filter.
          # Paths to files will be available in `${FILTER_NAME}_files` output variable.
          list-files: csv

  validate-high-risk-code:
    if: needs.files-changed.outputs.high_risk_code == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - name: Comment on PR to notify of changes in high risk files
        uses: actions/github-script@v7
        env:
          high_risk_code: ${{ needs.files-changed.outputs.high_risk_code_files }}
        with:
          script: |
            const highRiskFiles = process.env.high_risk_code;
            const fileList = highRiskFiles.split(',').map(file => `- [ ] ${file}`).join('\n');
            
            // Get existing comments
            const comments = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo
            });
            
            // Check if we already have a high risk code comment
            const hasExistingComment = comments.data.some(comment => 
              comment.body.includes('🚨 Detected changes in high risk code 🚨')
            );
            
            // Only create comment if we don't already have one
            if (!hasExistingComment) {
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `### 🚨 Detected changes in high risk code 🚨 \n High-risk code has higher potential to break the SDK and may be hard to test. To prevent severe bugs, apply the rollout process for releasing such changes and be extra careful when changing and reviewing these files:\n ${fileList}`
              });
            }
