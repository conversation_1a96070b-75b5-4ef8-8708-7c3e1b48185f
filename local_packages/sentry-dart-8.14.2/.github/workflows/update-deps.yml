name: Update Dependencies

on:
  # Run every day.
  schedule:
    - cron: '0 3 * * *'
  # And on on every PR merge so we get the updated dependencies ASAP, and to make sure the changelog doesn't conflict.
  push:
    branches:
      - main

jobs:
  android:
    uses: getsentry/github-workflows/.github/workflows/updater.yml@v2
    with:
      path: flutter/scripts/update-android.sh
      name: Android SDK
    secrets:
      api-token: ${{ secrets.CI_DEPLOY_KEY }}

  cocoa:
    uses: getsentry/github-workflows/.github/workflows/updater.yml@v2
    with:
      path: flutter/scripts/update-cocoa.sh
      name: Cocoa SDK
      runs-on: macos-latest
    secrets:
      api-token: ${{ secrets.CI_DEPLOY_KEY }}

  native:
    uses: getsentry/github-workflows/.github/workflows/updater.yml@v2
    with:
      path: flutter/scripts/update-native.sh
      name: Native SDK
    secrets:
      api-token: ${{ secrets.CI_DEPLOY_KEY }}

  metrics-flutter:
    uses: getsentry/github-workflows/.github/workflows/updater.yml@v2
    with:
      path: metrics/flutter.properties
      name: Flutter SDK (metrics)
      changelog-entry: false
      pr-strategy: update
    secrets:
      api-token: ${{ secrets.CI_DEPLOY_KEY }}

  symbol-collector:
    uses: getsentry/github-workflows/.github/workflows/updater.yml@v2
    with:
      path: scripts/update-symbol-collector.sh
      name: Symbol collector CLI
      changelog-entry: false
      pr-strategy: update
    secrets:
      api-token: ${{ secrets.CI_DEPLOY_KEY }}
