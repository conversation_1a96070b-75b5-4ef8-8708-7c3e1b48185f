name: Flutter Web Example GH-Pages

on:
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    env:
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
      - uses: bluefireteam/flutter-gh-pages@cf4a9312849577dbfd9df8f3d63d12ef6b09898e # pin@v9
        with:
          workingDir: flutter/example
          customArgs: --source-maps
          webRenderer: canvaskit
          baseHref: "/sentry-dart/"

      - name: Upload source maps
        run: |
          cd flutter/example
          flutter pub get
          dart run sentry_dart_plugin
