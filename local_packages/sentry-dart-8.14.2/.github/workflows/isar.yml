name: sentry-isar
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - '!**/*.md'
      - '!**/class-diagram.svg'
      - '.github/workflows/isar.yml'
      - '.github/workflows/analyze.yml'
      - '.github/actions/flutter-test/**'
      - '.github/actions/coverage/**'
      - 'dart/**'
      - 'isar/**'

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  build:
    name: '${{ matrix.target }} | ${{ matrix.sdk }}'
    runs-on: ${{ matrix.target == 'linux' && 'ubuntu' || matrix.target }}-latest
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        target: [macos, linux, windows]
        sdk: [stable, beta]

    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/flutter-test
        with:
          directory: isar

      - uses: ./.github/actions/coverage
        if: matrix.target == 'linux' && matrix.sdk == 'stable'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: isar
          coverage: sentry_isar
          min-coverage: 55

  analyze:
    uses: ./.github/workflows/analyze.yml
    with:
      package: isar
      sdk: flutter
