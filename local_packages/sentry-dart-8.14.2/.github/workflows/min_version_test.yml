name: min version test
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - "!**/*.md"
      - "!**/class-diagram.svg"
      - ".github/workflows/min_version_test.yml"
      - "dart/**"
      - "flutter/**"
      - "min_version_test/**"

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  build-android:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: "adopt"
          java-version: "11"

      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          flutter-version: "3.0.0"

      - name: Build Android
        run: |
          cd min_version_test
          flutter pub get
          flutter build appbundle

  build-ios:
    runs-on: macos-latest
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v4

      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          flutter-version: "3.0.0"

      - uses: ruby/setup-ruby@277ba2a127aba66d45bad0fa2dc56f80dbfedffa # pin@v1.222.0
        with:
          ruby-version: '3.1.2' # https://github.com/flutter/flutter/issues/109385#issuecomment-1212614125

      - name: Uninstall existing CocoaPods and install globally
        run: |
          gem uninstall cocoapods -a
          sudo gem install cocoapods
          echo "$(which pod)"

      - name: Build iOS
        run: |
          cd min_version_test
          flutter pub get
          cd ios
          pod repo update
          pod install
          cd ..
          flutter build ios --no-codesign

  build-web:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v4

      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          flutter-version: "3.0.0"

      - name: Build web
        run: |
          cd min_version_test
          flutter pub get
          flutter build web --source-maps
