on:
  workflow_call:
    inputs:
      package:
        required: true
        type: string
      sdk:
        required: false
        type: string
        default: dart
      panaThreshold:
        description: Minumum percentage of Dart Package Analyzer score that must be achieved.
        required: false
        type: number
        default: 100

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  analyze:
    if: ${{ !startsWith(github.ref, 'refs/heads/release/') }}
    runs-on: ubuntu-latest
    timeout-minutes: 20
    defaults:
      run:
        working-directory: ${{ inputs.package }}
    steps:
      - uses: actions/checkout@v4
      - uses: dart-lang/setup-dart@e51d8e571e22473a2ddebf0ef8a2123f0ab2c02c # pin@v1
        if: ${{ inputs.sdk == 'dart' }}
      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        if: ${{ inputs.sdk == 'flutter' }}

      - run: ${{ inputs.sdk }} pub get
      - run: ${{ inputs.sdk }} pub get
        if: ${{ inputs.package == 'flutter' }}
        working-directory: flutter/microbenchmarks
      - run: dart format --set-exit-if-changed ./

      - name: dart analyze
        uses: invertase/github-action-dart-analyzer@e981b01a458d0bab71ee5da182e5b26687b7101b # pin@v3.0.0
        with:
          annotate: true
          fatal-infos: true
          fatal-warnings: true
          annotate-only: false
          working-directory: ${{ inputs.package }}

      - run: dart doc --dry-run

      - name: Run publish validation
        run: |
          dart pub get --directory ../scripts/publish_validation
          dart run ../scripts/publish_validation/bin/publish_validation.dart --executable ${{ inputs.sdk }}

  package-analysis:
    # `axel-op/dart-package-analyzer` is using `flutter pub upgrade` instead of `get`,
    # which ignores pubspec.yaml `dependency_overrides`. Because of that, all `release/*` branches are failing,
    # because the package cannot find the "about to be released" version of our sentry-dart package that it depends on.
    if: ${{ !startsWith(github.ref, 'refs/heads/release/') && inputs.panaThreshold > 0 }}
    runs-on: ubuntu-20.04
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4
      - name: Apply dependency override
        if: ${{ inputs.package == 'flutter' }}
        working-directory: ${{ inputs.package }}
        run: |
          sed -i.bak 's|sentry:.*|sentry:\n    path: /github/workspace/dart|g' pubspec.yaml
      - uses: axel-op/dart-package-analyzer@56afb7e6737bd2b7cee05382ae7f0e8111138080 # pin@v3
        id: analysis
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
          relativePath: ${{ inputs.package }}
      - name: Check scores
        env:
          TOTAL: ${{ steps.analysis.outputs.total }}
          TOTAL_MAX: ${{ steps.analysis.outputs.total_max }}
        run: |
          PERCENTAGE=$(( $TOTAL * 100 / $TOTAL_MAX ))
          if (( $PERCENTAGE < ${{ inputs.panaThreshold }} ))
          then
            echo "Score too low ($PERCENTAGE % is less than the expected ${{ inputs.panaThreshold }} %)!"
            exit 1
          fi
