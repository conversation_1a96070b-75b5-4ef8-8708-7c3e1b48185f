name: sentry-flutter
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - '.github/workflows/flutter.yml'
      - '.github/workflows/analyze.yml'
      - '.github/actions/flutter-test/**'
      - '.github/actions/coverage/**'
      - '!**/*.md'
      - '!**/class-diagram.svg'
      - 'dart/**'
      - 'flutter/**'
      - 'metrics/flutter.properties'

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  build:
    name: '${{ matrix.target }} | ${{ matrix.os }} | ${{ matrix.sdk }}'
    runs-on: ${{ matrix.os }}-latest
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        target: [android, ios, macos, linux, windows, js, wasm]
        sdk: [stable, beta]
        include:
          - target: android
            os: ubuntu
          - target: ios
            os: macos
          - target: macos
            os: macos
          - target: linux
            os: ubuntu
          - target: windows
            os: windows
          - target: js
            os: ubuntu
          - target: wasm
            os: ubuntu

    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/flutter-test
        with:
          directory: flutter

      - uses: ./.github/actions/coverage
        if: matrix.target == 'linux' && matrix.sdk == 'stable'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: flutter
          coverage: sentry_flutter
          min-coverage: 90

      - name: Build example for ${{ matrix.target }}
        # The example currently doesn't support compiling for WASM. Should be OK once we add package:web in v9.
        if: matrix.target != 'wasm'
        working-directory: flutter/example
        shell: bash
        run: |
          flutter config --enable-windows-desktop
          flutter config --enable-macos-desktop
          flutter config --enable-linux-desktop
          TARGET=${{ matrix.target }}
          flutter pub get
          case $TARGET in
          ios)
          flutter build ios --no-codesign
          ;;
          macos)
          flutter build macos
          ;;
          android)
          flutter build appbundle
          ;;
          js)
          flutter build web
          ;;
          linux)
          flutter build linux
          ;;
          windows)
          flutter build windows
          ;;
          esac

  spm:
    name: "SPM"
    runs-on: macos-15
    timeout-minutes: 30
    defaults:
      run:
        shell: bash
        working-directory: flutter/example
    strategy:
      fail-fast: false
      matrix:
        target: [ios, macos]

    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          channel: main
      - run: flutter upgrade
      - run: flutter config --enable-swift-package-manager
      - name: Run on iOS
        if: matrix.target == 'ios'
        run: flutter build ios --no-codesign
      - name: Run on macOS
        if: matrix.target == 'macos'
        run: flutter build macos

  analyze:
    uses: ./.github/workflows/analyze.yml
    with:
      package: flutter
      sdk: flutter
      panaThreshold: 87

  pod-lint:
    runs-on: macos-latest
    timeout-minutes: 20
    defaults:
      run:
        working-directory: ./flutter
    steps:
      - uses: actions/checkout@v4
      # https://github.com/CocoaPods/CocoaPods/issues/5275#issuecomment-315461879
      - run: pod lib lint ios/sentry_flutter.podspec --configuration=Debug --skip-import-validation --allow-warnings --verbose

  swift-lint:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    defaults:
      run:
        working-directory: ./flutter
    steps:
      - uses: actions/checkout@v4
      - uses: norio-nomura/action-swiftlint@9f4dcd7fd46b4e75d7935cf2f4df406d5cae3684 # pin@3.2.1
        with:
          args: --strict

  ktlint:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    defaults:
      run:
        working-directory: ./flutter
    steps:
      - uses: actions/checkout@v4

      - name: ktlint
        uses: ScaCap/action-ktlint@26c5e9b625966139d9956cbbb6217375480d4e14 # pin@1.9.0
        with:
          github_token: ${{ secrets.github_token }}
          reporter: github-pr-review
          android: true
          fail_on_error: true

  detekt:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4
      # To recreate baseline run: detekt -i flutter/android,flutter/example/android -b flutter/config/detekt-bl.xml -cb
      - uses: natiginfo/action-detekt-all@45229fbbe47eaff1160b6c956d7ffe14dc23c206 # pin@1.23.8
        with:
          args: -i flutter/android,flutter/example/android --baseline flutter/config/detekt-bl.xml --jvm-target 1.8 --build-upon-default-config --all-rules
