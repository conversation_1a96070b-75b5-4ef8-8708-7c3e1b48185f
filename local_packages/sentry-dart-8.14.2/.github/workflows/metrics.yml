name: SDK metrics

on:
  push:
    paths:
      - .github/workflows/metrics.yml
      - dart/**
      - flutter/**
      - metrics/**
      - "!**/*.md"
    branches-ignore:
      - deps/**
      - dependabot/**
    tags-ignore: ["**"]

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  metrics:
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.host }}
    timeout-minutes: 30
    strategy:
      # We want that the matrix keeps running, default is to cancel all jobs if a single fails.
      fail-fast: false
      matrix:
        include:
          - platform: ios
            name: iOS
            appPlain: test-app-plain.ipa
            host: macos-latest
          - platform: android
            name: Android
            appPlain: metrics/perf-test-app-plain/build/app/outputs/apk/release/app-arm64-v8a-release.apk
            host: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      # Let's stick to an explicit version and update manually because a version change may affect results.
      # If it would update implicitly it could confuse people to think the change is actually caused by the PR.
      # Instead, we use Updater (update-deps.yml) to create PRs for explicit Flutter SDK update.
      - name: Read configured Flutter SDK version
        id: conf
        run: |
          version=$(grep "version" metrics/flutter.properties | cut -d'=' -f2 | xargs)
          echo "flutter=$version" >> "$GITHUB_OUTPUT"

      - name: Install Flutter v${{ steps.conf.outputs.flutter }}
        uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          flutter-version: ${{ steps.conf.outputs.flutter }}

      - uses: actions/setup-java@v4
        if: ${{ matrix.platform == 'android' }}
        with:
          java-version: "17"
          distribution: "adopt"

      - run: ./metrics/prepare.sh

      - uses: actions/cache@v4
        id: app-plain-cache
        with:
          path: ${{ matrix.appPlain }}
          key: ${{ github.workflow }}-${{ github.job }}-appplain-${{ matrix.platform }}-${{ hashFiles('metrics/perf-test-app-plain/pubspec.yaml') }}

      - name: Build
        run: ./metrics/build.sh ${{ matrix.platform }}
        env:
          # Necessary to build an iOS .ipa (using fastlane)
          APP_STORE_CONNECT_KEY_ID: ${{ secrets.APP_STORE_CONNECT_KEY_ID }}
          APP_STORE_CONNECT_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
          APP_STORE_CONNECT_KEY: ${{ secrets.APP_STORE_CONNECT_KEY }}
          FASTLANE_KEYCHAIN_PASSWORD: ${{ secrets.FASTLANE_KEYCHAIN_PASSWORD }}
          MATCH_GIT_PRIVATE_KEY: ${{ secrets.MATCH_GIT_PRIVATE_KEY }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          MATCH_USERNAME: ${{ secrets.MATCH_USERNAME }}
          APP_PLAIN: ${{ matrix.appPlain }}

      - name: Collect apps metrics
        uses: getsentry/action-app-sdk-overhead-metrics@v1
        with:
          name: ${{ matrix.name }}
          config: ./metrics/metrics-${{ matrix.platform }}.yml
          sauce-user: ${{ secrets.SAUCE_USERNAME }}
          sauce-key: ${{ secrets.SAUCE_ACCESS_KEY }}

  metrics-dart:
    name: Console
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0

      - name: create dart sample apps
        working-directory: ./metrics
        run: ./prepare-dart.sh

      - name: build dart sample apps
        working-directory: ./metrics
        run: ./build-dart.sh

      - name: Set file diff max threshold
        run: echo "THRESHOLD=13100000" >> $GITHUB_ENV # 1,31 MB

      - name: Compare executable size
        working-directory: ./metrics
        run: ./compare_sizes.sh perf_test_console_plain.bin perf_test_console_sentry.bin $THRESHOLD
