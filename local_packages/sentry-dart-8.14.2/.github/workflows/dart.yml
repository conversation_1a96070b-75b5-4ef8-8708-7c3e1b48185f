name: sentry-dart
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - '!**/*.md'
      - '!**/class-diagram.svg'
      - '.github/workflows/dart.yml'
      - '.github/workflows/analyze.yml'
      - '.github/actions/dart-test/**'
      - '.github/actions/coverage/**'
      - 'dart/**'

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  build:
    name: '${{ matrix.os }} | ${{ matrix.sdk }}'
    runs-on: ${{ matrix.os }}-latest
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu]
        sdk:
          - '2.18'
          - '2.19'
          - '3.0'
          - '3.1'
          - '3.2'
          - '3.3'
          - '3.4'
          - '3.5'
          - stable
          - beta
        include:
          - os: windows
            sdk: stable
          - os: macos
            sdk: stable
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/dart-test
        with:
          directory: dart

      - uses: ./.github/actions/coverage
        if: runner.os == 'Linux' && matrix.sdk == 'stable'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: dart
          coverage: sentry
          min-coverage: 85

      - name: Build example
        working-directory: dart/example
        run: |
          dart pub get
          dart compile aot-snapshot bin/example.dart

  analyze:
    uses: ./.github/workflows/analyze.yml
    with:
      package: dart
      panaThreshold: 87
