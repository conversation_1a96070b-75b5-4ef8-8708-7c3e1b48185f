name: flutter native & integration test
on:
  push:
    branches:
      - main
      - release/**
  pull_request:
    paths:
      - '!**/*.md'
      - '!**/class-diagram.svg'
      - '.github/workflows/flutter_test.yml'
      - 'dart/**'
      - 'flutter/**'

env:
  SENTRY_AUTH_TOKEN_E2E: ${{ secrets.SENTRY_AUTH_TOKEN_E2E }}

jobs:
  cancel-previous-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # pin@0.12.1
        with:
          access_token: ${{ github.token }}

  test-android:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    defaults:
      run:
        working-directory: ./flutter/example
    strategy:
      fail-fast: false
      matrix:
        sdk: [stable, beta]
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: Enable KVM group perms
        run: |
          echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
          sudo udevadm control --reload-rules
          sudo udevadm trigger --name-match=kvm

      - uses: actions/setup-java@v4
        with:
          distribution: 'adopt'
          java-version: '17'

      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          channel: ${{ matrix.sdk }}

      - run: flutter pub get

      - name: Gradle cache
        uses: gradle/gradle-build-action@ac2d340dc04d9e1113182899e983b5400c17cda1 # pin@v3.0.0

      # TODO: fix emulator caching, in ubuntu-latest emulator won't boot: https://github.com/ReactiveCircus/android-emulator-runner/issues/278

      - name: build apk
        working-directory: ./flutter/example/android
        run: flutter build apk --debug --target-platform=android-x64

      - name: launch android emulator & run android native test
        uses: reactivecircus/android-emulator-runner@62dbb605bba737720e10b196cb4220d374026a6d #pin@v2.33.0
        with:
          working-directory: ./flutter/example/android
          api-level: 31
          profile: Nexus 6
          arch: x86_64
          force-avd-creation: false
          avd-name: avd-x86_64-31
          emulator-options: -no-snapshot-save -no-window -accel on -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
          disable-animations: true
          script: ./gradlew testDebugUnitTest

      - name: launch android emulator & run android integration test
        uses: reactivecircus/android-emulator-runner@62dbb605bba737720e10b196cb4220d374026a6d #pin@v2.33.0
        with:
          working-directory: ./flutter/example
          api-level: 31
          profile: Nexus 6
          arch: x86_64
          force-avd-creation: false
          avd-name: avd-x86_64-31
          emulator-options: -no-snapshot-save -no-window -accel on -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
          disable-animations: true
          script: flutter test integration_test/all.dart --dart-define SENTRY_AUTH_TOKEN_E2E=$SENTRY_AUTH_TOKEN_E2E --verbose

      - name: launch android emulator & run android integration test in profile mode
        uses: reactivecircus/android-emulator-runner@62dbb605bba737720e10b196cb4220d374026a6d #pin@v2.33.0
        with:
          working-directory: ./flutter/example
          api-level: 31
          profile: Nexus 6
          arch: x86_64
          force-avd-creation: false
          avd-name: avd-x86_64-31
          emulator-options: -no-snapshot-save -no-window -accel on -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
          disable-animations: true
          script: flutter drive --driver=integration_test/test_driver/driver.dart --target=integration_test/sentry_widgets_flutter_binding_test.dart --profile -d emulator-5554

  cocoa:
    name: '${{ matrix.target }} | ${{ matrix.sdk }}'
    runs-on: macos-latest-xlarge
    timeout-minutes: 30
    defaults:
      run:
        working-directory: ./flutter/example
    strategy:
      fail-fast: false
      matrix:
        target: [ios, macos]
        sdk: [stable, beta]
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          channel: ${{ matrix.sdk }}

      - run: flutter pub get

      - run: pod install
        working-directory: ./flutter/example/${{ matrix.target }}

      - name: prepare test device
        id: device
        run: |
          case "${{ matrix.target }}" in
            ios)
            device=$(xcrun simctl create sentryPhone com.apple.CoreSimulator.SimDeviceType.iPhone-14 com.apple.CoreSimulator.SimRuntime.iOS-17-0)
            xcrun simctl boot ${device}
            echo "platform=iOS Simulator,id=${device}" >> "$GITHUB_OUTPUT"
            ;;
            macos)
            device="macos"
            echo "platform=OS X" >> "$GITHUB_OUTPUT"
            ;;
            esac
          echo "name=${device}" >> "$GITHUB_OUTPUT"

      - name: run integration test
        # Disable flutter integration tests for iOS for now (https://github.com/getsentry/sentry-dart/issues/1605#issuecomment-1695809346)
        if: ${{ matrix.target != 'ios' }}
        run: |
          flutter test -d "${{ steps.device.outputs.name }}" integration_test/all.dart --dart-define SENTRY_AUTH_TOKEN_E2E=$SENTRY_AUTH_TOKEN_E2E --verbose
          flutter drive --driver=integration_test/test_driver/driver.dart --target=integration_test/sentry_widgets_flutter_binding_test.dart --profile -d "${{ steps.device.outputs.name }}"

      - name: run native test
        # We only have the native unit test package in the iOS xcodeproj at the moment.
        # Should be OK because it will likely be removed after switching to FFI (see https://github.com/getsentry/sentry-dart/issues/1444).
        if: ${{ matrix.target != 'macos' }}
        working-directory: ./flutter/example/${{ matrix.target }}
        run: xcodebuild test -workspace Runner.xcworkspace -scheme Runner -configuration Debug -destination "platform=${{ steps.device.outputs.platform }}" -allowProvisioningUpdates CODE_SIGNING_ALLOWED=NO

  test-web:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    defaults:
      run:
        working-directory: ./flutter/example
    strategy:
      fail-fast: false
      matrix:
        sdk: [ "stable", "beta" ]
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: Install Chrome Browser
        uses: browser-actions/setup-chrome@c785b87e244131f27c9f19c1a33e2ead956ab7ce # pin@v1.7.3
        with:
          chrome-version: stable
      - run: chrome --version

      - uses: subosito/flutter-action@f2c4f6686ca8e8d6e6d0f28410eeef506ed66aff # pin@v2.18.0
        with:
          channel: ${{ matrix.sdk }}

      - name: flutter upgrade
        run: flutter upgrade

      - name: flutter pub get
        run: flutter pub get

      - name: Install Xvfb and dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y xvfb
          sudo apt-get -y install xorg xvfb gtk2-engines-pixbuf
          sudo apt-get -y install dbus-x11 xfonts-base xfonts-100dpi xfonts-75dpi xfonts-cyrillic xfonts-scalable
          sudo apt-get -y install imagemagick x11-apps

      - name: Setup ChromeDriver
        uses: nanasess/setup-chromedriver@e93e57b843c0c92788f22483f1a31af8ee48db25 # pin@2.3.0

      - name: Start Xvfb and run tests
        run: |
          # Start Xvfb with specific screen settings
          Xvfb -ac :99 -screen 0 1280x1024x16 &
          export DISPLAY=:99
          
          # Start ChromeDriver
          chromedriver --port=4444 &
          
          # Wait for services to start
          sleep 5

          # Run the tests
          flutter drive \
            --driver=integration_test/test_driver/driver.dart \
            --target=integration_test/web_sdk_test.dart \
            -d chrome