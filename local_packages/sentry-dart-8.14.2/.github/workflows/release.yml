name: release
on:
  workflow_dispatch:
    inputs:
      version:
        description: Version to release
        required: true
      force:
        description: Force a release even when there are release-blockers (optional)
        required: false
      merge_target:
        description: Target branch to merge into. Uses the default branch as a fallback (optional)
        required: false

jobs:
  release:
    runs-on: ubuntu-latest
    name: "Release a new version"
    steps:
      - name: Get auth token
        id: token
        uses: actions/create-github-app-token@21cfef2b496dd8ef5b904c159339626a10ad380e # v1.11.6
        with:
          app-id: ${{ vars.SENTRY_RELEASE_BOT_CLIENT_ID }}
          private-key: ${{ secrets.SENTRY_RELEASE_BOT_PRIVATE_KEY }}
      - name: Check out current commit (${{ github.sha }})
        uses: actions/checkout@v4
        with:
          token: ${{ steps.token.outputs.token }}
          fetch-depth: 0
      - name: Prepare release
        uses: getsentry/action-prepare-release@v1
        env:
          GITHUB_TOKEN: ${{ steps.token.outputs.token }}
        with:
          version: ${{ github.event.inputs.version }}
          force: ${{ github.event.inputs.force }}
          merge_target: ${{ github.event.inputs.merge_target }}
