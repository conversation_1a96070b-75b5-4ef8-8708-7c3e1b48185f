version: 2
updates:
  - package-ecosystem: pub
    directory: /dart
    schedule:
      interval: weekly
    open-pull-requests-limit: 2
    versioning-strategy: increase-if-necessary

  - package-ecosystem: pub
    directory: /flutter
    schedule:
      interval: weekly
    open-pull-requests-limit: 2
    versioning-strategy: increase-if-necessary

  - package-ecosystem: pub
    directory: /dio
    schedule:
      interval: weekly
    open-pull-requests-limit: 2
    versioning-strategy: increase-if-necessary

  - package-ecosystem: pub
    directory: /file
    schedule:
      interval: weekly
    open-pull-requests-limit: 2
    versioning-strategy: increase-if-necessary

  - package-ecosystem: pub
    directory: /logging
    schedule:
      interval: weekly
    open-pull-requests-limit: 2
    versioning-strategy: increase-if-necessary

  - package-ecosystem: pub
    directory: /sqflite
    schedule:
      interval: weekly
    open-pull-requests-limit: 2
    versioning-strategy: increase-if-necessary

  - package-ecosystem: github-actions
    directory: /
    schedule:
        interval: weekly
