<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Pages: 1 -->
<svg width="7170pt" height="1468pt"
 viewBox="0.00 0.00 7170.00 1468.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1464)">
<polygon fill="white" stroke="transparent" points="-4,4 -4,-1464 7166,-1464 7166,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster~</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M20,-8C20,-8 7142,-8 7142,-8 7148,-8 7154,-14 7154,-20 7154,-20 7154,-1440 7154,-1440 7154,-1446 7148,-1452 7142,-1452 7142,-1452 20,-1452 20,-1452 14,-1452 8,-1446 8,-1440 8,-1440 8,-20 8,-20 8,-14 14,-8 20,-8"/>
<text text-anchor="middle" x="3581" y="-1437.6" font-family="Arial Black" font-size="13.00">dart</text>
</g>
<g id="clust2" class="cluster">
<title>cluster~/lib</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M28,-16C28,-16 7134,-16 7134,-16 7140,-16 7146,-22 7146,-28 7146,-28 7146,-1410 7146,-1410 7146,-1416 7140,-1422 7134,-1422 7134,-1422 28,-1422 28,-1422 22,-1422 16,-1416 16,-1410 16,-1410 16,-28 16,-28 16,-22 22,-16 28,-16"/>
<text text-anchor="middle" x="3581" y="-1407.6" font-family="Arial Black" font-size="13.00">lib</text>
</g>
<g id="clust3" class="cluster">
<title>cluster~/lib/src</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M36,-24C36,-24 7020,-24 7020,-24 7026,-24 7032,-30 7032,-36 7032,-36 7032,-1380 7032,-1380 7032,-1386 7026,-1392 7020,-1392 7020,-1392 36,-1392 36,-1392 30,-1392 24,-1386 24,-1380 24,-1380 24,-36 24,-36 24,-30 30,-24 36,-24"/>
<text text-anchor="middle" x="3528" y="-1377.6" font-family="Arial Black" font-size="13.00">src</text>
</g>
<g id="clust4" class="cluster">
<title>cluster~/lib/src/environment</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M437,-414C437,-414 679,-414 679,-414 685,-414 691,-420 691,-426 691,-426 691,-620 691,-620 691,-626 685,-632 679,-632 679,-632 437,-632 437,-632 431,-632 425,-626 425,-620 425,-620 425,-426 425,-426 425,-420 431,-414 437,-414"/>
<text text-anchor="middle" x="558" y="-617.6" font-family="Arial Black" font-size="13.00">environment</text>
</g>
<g id="clust5" class="cluster">
<title>cluster~/lib/src/protocol</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M468,-32C468,-32 5186,-32 5186,-32 5192,-32 5198,-38 5198,-44 5198,-44 5198,-240 5198,-240 5198,-246 5192,-252 5186,-252 5186,-252 468,-252 468,-252 462,-252 456,-246 456,-240 456,-240 456,-44 456,-44 456,-38 462,-32 468,-32"/>
<text text-anchor="middle" x="2827" y="-237.6" font-family="Arial Black" font-size="13.00">protocol</text>
</g>
<g id="clust6" class="cluster">
<title>cluster~/lib/src/event_processor</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M3546,-770C3546,-770 4736,-770 4736,-770 4742,-770 4748,-776 4748,-782 4748,-782 4748,-944 4748,-944 4748,-950 4742,-956 4736,-956 4736,-956 3546,-956 3546,-956 3540,-956 3534,-950 3534,-944 3534,-944 3534,-782 3534,-782 3534,-776 3540,-770 3546,-770"/>
<text text-anchor="middle" x="4141" y="-941.6" font-family="Arial Black" font-size="13.00">event_processor</text>
</g>
<g id="clust7" class="cluster">
<title>cluster~/lib/src/event_processor/exception</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M4263,-778C4263,-778 4728,-778 4728,-778 4734,-778 4740,-784 4740,-790 4740,-790 4740,-914 4740,-914 4740,-920 4734,-926 4728,-926 4728,-926 4263,-926 4263,-926 4257,-926 4251,-920 4251,-914 4251,-914 4251,-790 4251,-790 4251,-784 4257,-778 4263,-778"/>
<text text-anchor="middle" x="4495.5" y="-911.6" font-family="Arial Black" font-size="13.00">exception</text>
</g>
<g id="clust8" class="cluster">
<title>cluster~/lib/src/event_processor/enricher</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M3786,-778C3786,-778 4231,-778 4231,-778 4237,-778 4243,-784 4243,-790 4243,-790 4243,-914 4243,-914 4243,-920 4237,-926 4231,-926 4231,-926 3786,-926 3786,-926 3780,-926 3774,-920 3774,-914 3774,-914 3774,-790 3774,-790 3774,-784 3780,-778 3786,-778"/>
<text text-anchor="middle" x="4008.5" y="-911.6" font-family="Arial Black" font-size="13.00">enricher</text>
</g>
<g id="clust9" class="cluster">
<title>cluster~/lib/src/platform</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M292,-260C292,-260 517,-260 517,-260 523,-260 529,-266 529,-272 529,-272 529,-394 529,-394 529,-400 523,-406 517,-406 517,-406 292,-406 292,-406 286,-406 280,-400 280,-394 280,-394 280,-272 280,-272 280,-266 286,-260 292,-260"/>
<text text-anchor="middle" x="404.5" y="-391.6" font-family="Arial Black" font-size="13.00">platform</text>
</g>
<g id="clust10" class="cluster">
<title>cluster~/lib/src/sentry_attachment</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M5560,-558C5560,-558 5714,-558 5714,-558 5720,-558 5726,-564 5726,-570 5726,-570 5726,-694 5726,-694 5726,-700 5720,-706 5714,-706 5714,-706 5560,-706 5560,-706 5554,-706 5548,-700 5548,-694 5548,-694 5548,-570 5548,-570 5548,-564 5554,-558 5560,-558"/>
<text text-anchor="middle" x="5637" y="-691.6" font-family="Arial Black" font-size="13.00">sentry_attachment</text>
</g>
<g id="clust11" class="cluster">
<title>cluster~/lib/src/http_client</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M5243,-1100C5243,-1100 5563,-1100 5563,-1100 5569,-1100 5575,-1106 5575,-1112 5575,-1112 5575,-1306 5575,-1306 5575,-1312 5569,-1318 5563,-1318 5563,-1318 5243,-1318 5243,-1318 5237,-1318 5231,-1312 5231,-1306 5231,-1306 5231,-1112 5231,-1112 5231,-1106 5237,-1100 5243,-1100"/>
<text text-anchor="middle" x="5403" y="-1303.6" font-family="Arial Black" font-size="13.00">http_client</text>
</g>
<g id="clust12" class="cluster">
<title>cluster~/lib/src/transport</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M1022,-486C1022,-486 1347,-486 1347,-486 1353,-486 1359,-492 1359,-498 1359,-498 1359,-840 1359,-840 1359,-846 1353,-852 1347,-852 1347,-852 1022,-852 1022,-852 1016,-852 1010,-846 1010,-840 1010,-840 1010,-498 1010,-498 1010,-492 1016,-486 1022,-486"/>
<text text-anchor="middle" x="1184.5" y="-837.6" font-family="Arial Black" font-size="13.00">transport</text>
</g>
<g id="clust13" class="cluster">
<title>cluster~/lib/src/utils</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M5824,-32C5824,-32 7000,-32 7000,-32 7006,-32 7012,-38 7012,-44 7012,-44 7012,-166 7012,-166 7012,-172 7006,-178 7000,-178 7000,-178 5824,-178 5824,-178 5818,-178 5812,-172 5812,-166 5812,-166 5812,-44 5812,-44 5812,-38 5818,-32 5824,-32"/>
<text text-anchor="middle" x="6412" y="-163.6" font-family="Arial Black" font-size="13.00">utils</text>
</g>
<g id="clust14" class="cluster">
<title>cluster~/lib/src/client_reports</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M1411,-332C1411,-332 1607,-332 1607,-332 1613,-332 1619,-338 1619,-344 1619,-344 1619,-694 1619,-694 1619,-700 1613,-706 1607,-706 1607,-706 1411,-706 1411,-706 1405,-706 1399,-700 1399,-694 1399,-694 1399,-344 1399,-344 1399,-338 1405,-332 1411,-332"/>
<text text-anchor="middle" x="1509" y="-691.6" font-family="Arial Black" font-size="13.00">client_reports</text>
</g>
<!-- /lib/src/noop_hub.dart -->
<g id="node1" class="node">
<title>/lib/src/noop_hub.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3110.5,-1072C3110.5,-1072 3051.5,-1072 3051.5,-1072 3045.5,-1072 3039.5,-1066 3039.5,-1060 3039.5,-1060 3039.5,-1048 3039.5,-1048 3039.5,-1042 3045.5,-1036 3051.5,-1036 3051.5,-1036 3110.5,-1036 3110.5,-1036 3116.5,-1036 3122.5,-1042 3122.5,-1048 3122.5,-1048 3122.5,-1060 3122.5,-1060 3122.5,-1066 3116.5,-1072 3110.5,-1072"/>
<text text-anchor="middle" x="3081" y="-1050.5" font-family="Arial" font-size="15.00">noop_hub</text>
</g>
<!-- /lib/src/sentry_client.dart -->
<g id="node19" class="node">
<title>/lib/src/sentry_client.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2855.5,-1000C2855.5,-1000 2778.5,-1000 2778.5,-1000 2772.5,-1000 2766.5,-994 2766.5,-988 2766.5,-988 2766.5,-976 2766.5,-976 2766.5,-970 2772.5,-964 2778.5,-964 2778.5,-964 2855.5,-964 2855.5,-964 2861.5,-964 2867.5,-970 2867.5,-976 2867.5,-976 2867.5,-988 2867.5,-988 2867.5,-994 2861.5,-1000 2855.5,-1000"/>
<text text-anchor="middle" x="2817" y="-978.5" font-family="Arial" font-size="15.00">sentry_client</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/sentry_client.dart -->
<g id="edge5" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/sentry_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3039.46,-1041.99C2995.99,-1030.46 2927.3,-1012.25 2877.74,-999.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2878.35,-995.65 2867.78,-996.47 2876.55,-1002.41 2878.35,-995.65"/>
</g>
<!-- /lib/src/sentry_options.dart -->
<g id="node67" class="node">
<title>/lib/src/sentry_options.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3394,-530C3394,-530 3304,-530 3304,-530 3298,-530 3292,-524 3292,-518 3292,-518 3292,-506 3292,-506 3292,-500 3298,-494 3304,-494 3304,-494 3394,-494 3394,-494 3400,-494 3406,-500 3406,-506 3406,-506 3406,-518 3406,-518 3406,-524 3400,-530 3394,-530"/>
<text text-anchor="middle" x="3349" y="-508.5" font-family="Arial" font-size="15.00">sentry_options</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge6" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3082.01,-1035.75C3087.01,-965.81 3115.06,-700.31 3258,-558 3267.36,-548.69 3279.03,-540.97 3290.86,-534.71"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3292.51,-537.8 3299.9,-530.21 3289.39,-531.53 3292.51,-537.8"/>
</g>
<!-- /lib/src/hint.dart -->
<g id="node78" class="node">
<title>/lib/src/hint.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3446,-676C3446,-676 3416,-676 3416,-676 3410,-676 3404,-670 3404,-664 3404,-664 3404,-652 3404,-652 3404,-646 3410,-640 3416,-640 3416,-640 3446,-640 3446,-640 3452,-640 3458,-646 3458,-652 3458,-652 3458,-664 3458,-664 3458,-670 3452,-676 3446,-676"/>
<text text-anchor="middle" x="3431" y="-654.5" font-family="Arial" font-size="15.00">hint</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge1" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3122.63,-1038.61C3164.99,-1023.91 3225.12,-1002.83 3230,-1000 3251.27,-987.67 3249.95,-974.87 3272,-964 3287.48,-956.37 3297.46,-967.86 3310,-956 3391.32,-879.04 3314.99,-805.16 3380,-714 3383.3,-709.37 3385.89,-709.94 3390,-706 3397.12,-699.18 3404.36,-691.26 3410.74,-683.89"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3413.47,-686.08 3417.27,-676.19 3408.13,-681.55 3413.47,-686.08"/>
</g>
<!-- /lib/src/tracing.dart -->
<g id="node80" class="node">
<title>/lib/src/tracing.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4474,-676C4474,-676 4436,-676 4436,-676 4430,-676 4424,-670 4424,-664 4424,-664 4424,-652 4424,-652 4424,-646 4430,-640 4436,-640 4436,-640 4474,-640 4474,-640 4480,-640 4486,-646 4486,-652 4486,-652 4486,-664 4486,-664 4486,-670 4480,-676 4474,-676"/>
<text text-anchor="middle" x="4455" y="-654.5" font-family="Arial" font-size="15.00">tracing</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge8" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3122.69,-1038.66C3125.5,-1037.75 3128.29,-1036.85 3131,-1036 3186.16,-1018.69 3202.56,-1022.07 3256,-1000 3293.51,-984.51 3303.62,-980.45 3336,-956 3428.79,-885.92 3414.13,-818.09 3520,-770 3585.48,-740.26 3771.48,-757.52 3843,-750 4058.94,-727.3 4315.42,-683.71 4413.8,-666.38"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4414.67,-669.78 4423.91,-664.59 4413.46,-662.88 4414.67,-669.78"/>
</g>
<!-- /lib/src/protocol.dart -->
<g id="node86" class="node">
<title>/lib/src/protocol.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2904,-304C2904,-304 2858,-304 2858,-304 2852,-304 2846,-298 2846,-292 2846,-292 2846,-280 2846,-280 2846,-274 2852,-268 2858,-268 2858,-268 2904,-268 2904,-268 2910,-268 2916,-274 2916,-280 2916,-280 2916,-292 2916,-292 2916,-298 2910,-304 2904,-304"/>
<text text-anchor="middle" x="2881" y="-282.5" font-family="Arial" font-size="15.00">protocol</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge3" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3039.36,-1052.55C2766.49,-1049.51 1238.72,-1031.2 1034,-1000 963.21,-989.21 929.05,-1007.21 879,-956 855.17,-931.62 802,-693.09 802,-659 802,-659 802,-659 802,-439 802,-381.43 824.31,-359.3 875,-332 918,-308.84 2584.2,-306.06 2633,-304 2703.85,-301.01 2785.92,-294.85 2835.62,-290.83"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2836.03,-294.31 2845.71,-290 2835.46,-287.33 2836.03,-294.31"/>
</g>
<!-- /lib/src/sentry_user_feedback.dart -->
<g id="node97" class="node">
<title>/lib/src/sentry_user_feedback.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1861,-602C1861,-602 1721,-602 1721,-602 1715,-602 1709,-596 1709,-590 1709,-590 1709,-578 1709,-578 1709,-572 1715,-566 1721,-566 1721,-566 1861,-566 1861,-566 1867,-566 1873,-572 1873,-578 1873,-578 1873,-590 1873,-590 1873,-596 1867,-602 1861,-602"/>
<text text-anchor="middle" x="1791" y="-580.5" font-family="Arial" font-size="15.00">sentry_user_feedback</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge7" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3039.2,-1052.39C2753.39,-1048.02 1096.32,-1020.02 1013,-956 927.33,-890.17 907.57,-783.85 990,-714 1004.31,-701.88 1649.29,-716.24 1665,-706 1691.99,-688.4 1673.31,-663.83 1695,-640 1707.32,-626.46 1723.77,-615.39 1739.57,-606.81"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1741.49,-609.76 1748.76,-602.05 1738.27,-603.54 1741.49,-609.76"/>
</g>
<!-- /lib/src/scope.dart -->
<g id="node117" class="node">
<title>/lib/src/scope.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3074,-822C3074,-822 3042,-822 3042,-822 3036,-822 3030,-816 3030,-810 3030,-810 3030,-798 3030,-798 3030,-792 3036,-786 3042,-786 3042,-786 3074,-786 3074,-786 3080,-786 3086,-792 3086,-798 3086,-798 3086,-810 3086,-810 3086,-816 3080,-822 3074,-822"/>
<text text-anchor="middle" x="3058" y="-800.5" font-family="Arial" font-size="15.00">scope</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/scope.dart -->
<g id="edge4" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/scope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3079.42,-1035.95C3075.5,-993.66 3065.29,-883.61 3060.52,-832.2"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3064,-831.8 3059.59,-822.17 3057.03,-832.45 3064,-831.8"/>
</g>
<!-- /lib/src/hub.dart -->
<g id="node141" class="node">
<title>/lib/src/hub.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5132,-602C5132,-602 5102,-602 5102,-602 5096,-602 5090,-596 5090,-590 5090,-590 5090,-578 5090,-578 5090,-572 5096,-566 5102,-566 5102,-566 5132,-566 5132,-566 5138,-566 5144,-572 5144,-578 5144,-578 5144,-590 5144,-590 5144,-596 5138,-602 5132,-602"/>
<text text-anchor="middle" x="5117" y="-580.5" font-family="Arial" font-size="15.00">hub</text>
</g>
<!-- /lib/src/noop_hub.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge2" class="edge">
<title>/lib/src/noop_hub.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3122.62,-1037.94C3149.9,-1027.15 3181.94,-1012.39 3190,-1000 3259.34,-893.37 3132.47,-821.78 3200,-714 3237.37,-654.36 3271.73,-657.12 3340,-640 3382.6,-629.32 4877.4,-637.27 4921,-632 4977.41,-625.19 5041.11,-608.11 5080.16,-596.53"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5081.2,-599.87 5089.77,-593.64 5079.19,-593.17 5081.2,-599.87"/>
</g>
<!-- /lib/src/sentry_event_like.dart -->
<g id="node2" class="node">
<title>/lib/src/sentry_event_like.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M413.5,-222C413.5,-222 304.5,-222 304.5,-222 298.5,-222 292.5,-216 292.5,-210 292.5,-210 292.5,-198 292.5,-198 292.5,-192 298.5,-186 304.5,-186 304.5,-186 413.5,-186 413.5,-186 419.5,-186 425.5,-192 425.5,-198 425.5,-198 425.5,-210 425.5,-210 425.5,-216 419.5,-222 413.5,-222"/>
<text text-anchor="middle" x="359" y="-200.5" font-family="Arial" font-size="15.00">sentry_event_like</text>
</g>
<!-- /lib/src/sentry_event_like.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge9" class="edge">
<title>/lib/src/sentry_event_like.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M388.55,-222.01C405.5,-232.93 427.34,-246.13 449,-252 478.6,-260.02 2626.38,-258.31 2657,-260 2719.18,-263.44 2790.31,-271.7 2835.53,-277.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.22,-281.29 2845.6,-279.17 2836.17,-274.35 2835.22,-281.29"/>
</g>
<!-- /lib/src/version.dart -->
<g id="node3" class="node">
<title>/lib/src/version.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3048.5,-458C3048.5,-458 3007.5,-458 3007.5,-458 3001.5,-458 2995.5,-452 2995.5,-446 2995.5,-446 2995.5,-434 2995.5,-434 2995.5,-428 3001.5,-422 3007.5,-422 3007.5,-422 3048.5,-422 3048.5,-422 3054.5,-422 3060.5,-428 3060.5,-434 3060.5,-434 3060.5,-446 3060.5,-446 3060.5,-452 3054.5,-458 3048.5,-458"/>
<text text-anchor="middle" x="3028" y="-436.5" font-family="Arial" font-size="15.00">version</text>
</g>
<!-- /lib/src/utils.dart -->
<g id="node4" class="node">
<title>/lib/src/utils.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M434,-148C434,-148 404,-148 404,-148 398,-148 392,-142 392,-136 392,-136 392,-124 392,-124 392,-118 398,-112 404,-112 404,-112 434,-112 434,-112 440,-112 446,-118 446,-124 446,-124 446,-136 446,-136 446,-142 440,-148 434,-148"/>
<text text-anchor="middle" x="419" y="-126.5" font-family="Arial" font-size="15.00">utils</text>
</g>
<!-- /lib/src/sentry_span_context.dart -->
<g id="node5" class="node">
<title>/lib/src/sentry_span_context.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5877.5,-602C5877.5,-602 5746.5,-602 5746.5,-602 5740.5,-602 5734.5,-596 5734.5,-590 5734.5,-590 5734.5,-578 5734.5,-578 5734.5,-572 5740.5,-566 5746.5,-566 5746.5,-566 5877.5,-566 5877.5,-566 5883.5,-566 5889.5,-572 5889.5,-578 5889.5,-578 5889.5,-590 5889.5,-590 5889.5,-596 5883.5,-602 5877.5,-602"/>
<text text-anchor="middle" x="5812" y="-580.5" font-family="Arial" font-size="15.00">sentry_span_context</text>
</g>
<!-- /lib/sentry.dart -->
<g id="node143" class="node">
<title>/lib/sentry.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M7086,-822C7086,-822 7052,-822 7052,-822 7046,-822 7040,-816 7040,-810 7040,-810 7040,-798 7040,-798 7040,-792 7046,-786 7052,-786 7052,-786 7086,-786 7086,-786 7092,-786 7098,-792 7098,-798 7098,-798 7098,-810 7098,-810 7098,-816 7092,-822 7086,-822"/>
<text text-anchor="middle" x="7069" y="-800.5" font-family="Arial" font-size="15.00">sentry</text>
</g>
<!-- /lib/src/sentry_span_context.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge10" class="edge">
<title>/lib/src/sentry_span_context.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5835.85,-602.21C5852.4,-613.06 5875.45,-626.11 5898,-632 5917.78,-637.17 6617.77,-627.57 6634,-640 6674.33,-670.88 6626.59,-716.77 6665,-750 6719.03,-796.75 6939.34,-802.72 7029.87,-803.16"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.99,-806.66 7040,-803.19 7030.01,-799.66 7029.99,-806.66"/>
</g>
<!-- /lib/src/environment/_web_environment_variables.dart -->
<g id="node6" class="node">
<title>/lib/src/environment/_web_environment_variables.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M651,-530C651,-530 465,-530 465,-530 459,-530 453,-524 453,-518 453,-518 453,-506 453,-506 453,-500 459,-494 465,-494 465,-494 651,-494 651,-494 657,-494 663,-500 663,-506 663,-506 663,-518 663,-518 663,-524 657,-530 651,-530"/>
<text text-anchor="middle" x="558" y="-508.5" font-family="Arial" font-size="15.00">_web_environment_variables</text>
</g>
<!-- /lib/src/environment/keys.dart -->
<g id="node7" class="node">
<title>/lib/src/environment/keys.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M480,-458C480,-458 450,-458 450,-458 444,-458 438,-452 438,-446 438,-446 438,-434 438,-434 438,-428 444,-422 450,-422 450,-422 480,-422 480,-422 486,-422 492,-428 492,-434 492,-434 492,-446 492,-446 492,-452 486,-458 480,-458"/>
<text text-anchor="middle" x="465" y="-436.5" font-family="Arial" font-size="15.00">keys</text>
</g>
<!-- /lib/src/environment/_web_environment_variables.dart&#45;&gt;/lib/src/environment/keys.dart -->
<g id="edge12" class="edge">
<title>/lib/src/environment/_web_environment_variables.dart&#45;&gt;/lib/src/environment/keys.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M535.01,-493.7C523.19,-484.8 508.61,-473.82 495.82,-464.2"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="497.82,-461.32 487.72,-458.1 493.61,-466.91 497.82,-461.32"/>
</g>
<!-- /lib/src/environment/environment_variables.dart -->
<g id="node9" class="node">
<title>/lib/src/environment/environment_variables.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M671.5,-458C671.5,-458 528.5,-458 528.5,-458 522.5,-458 516.5,-452 516.5,-446 516.5,-446 516.5,-434 516.5,-434 516.5,-428 522.5,-422 528.5,-422 528.5,-422 671.5,-422 671.5,-422 677.5,-422 683.5,-428 683.5,-434 683.5,-434 683.5,-446 683.5,-446 683.5,-452 677.5,-458 671.5,-458"/>
<text text-anchor="middle" x="600" y="-436.5" font-family="Arial" font-size="15.00">environment_variables</text>
</g>
<!-- /lib/src/environment/_web_environment_variables.dart&#45;&gt;/lib/src/environment/environment_variables.dart -->
<g id="edge11" class="edge">
<title>/lib/src/environment/_web_environment_variables.dart&#45;&gt;/lib/src/environment/environment_variables.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M568.38,-493.7C573.26,-485.56 579.19,-475.69 584.58,-466.7"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="587.59,-468.48 589.74,-458.1 581.59,-464.88 587.59,-468.48"/>
</g>
<!-- /lib/src/environment/_io_environment_variables.dart -->
<g id="node8" class="node">
<title>/lib/src/environment/_io_environment_variables.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M643,-602C643,-602 473,-602 473,-602 467,-602 461,-596 461,-590 461,-590 461,-578 461,-578 461,-572 467,-566 473,-566 473,-566 643,-566 643,-566 649,-566 655,-572 655,-578 655,-578 655,-590 655,-590 655,-596 649,-602 643,-602"/>
<text text-anchor="middle" x="558" y="-580.5" font-family="Arial" font-size="15.00">_io_environment_variables</text>
</g>
<!-- /lib/src/environment/_io_environment_variables.dart&#45;&gt;/lib/src/environment/_web_environment_variables.dart -->
<g id="edge13" class="edge">
<title>/lib/src/environment/_io_environment_variables.dart&#45;&gt;/lib/src/environment/_web_environment_variables.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M558,-565.7C558,-557.98 558,-548.71 558,-540.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="561.5,-540.1 558,-530.1 554.5,-540.1 561.5,-540.1"/>
</g>
<!-- /lib/src/environment/_io_environment_variables.dart&#45;&gt;/lib/src/environment/keys.dart -->
<g id="edge15" class="edge">
<title>/lib/src/environment/_io_environment_variables.dart&#45;&gt;/lib/src/environment/keys.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M489.69,-565.94C471.79,-558.03 454.56,-546.56 444,-530 431.88,-510.99 439.66,-485.69 449,-466.92"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="452.13,-468.49 453.78,-458.03 445.97,-465.18 452.13,-468.49"/>
</g>
<!-- /lib/src/environment/_io_environment_variables.dart&#45;&gt;/lib/src/environment/environment_variables.dart -->
<g id="edge14" class="edge">
<title>/lib/src/environment/_io_environment_variables.dart&#45;&gt;/lib/src/environment/environment_variables.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M626.31,-565.94C644.21,-558.03 661.44,-546.56 672,-530 682.52,-513.51 681.48,-503.1 672,-486 667.07,-477.1 659.57,-469.73 651.24,-463.71"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="652.98,-460.67 642.69,-458.14 649.15,-466.54 652.98,-460.67"/>
</g>
<!-- /lib/src/platform_checker.dart -->
<g id="node79" class="node">
<title>/lib/src/platform_checker.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M659,-376C659,-376 551,-376 551,-376 545,-376 539,-370 539,-364 539,-364 539,-352 539,-352 539,-346 545,-340 551,-340 551,-340 659,-340 659,-340 665,-340 671,-346 671,-352 671,-352 671,-364 671,-364 671,-370 665,-376 659,-376"/>
<text text-anchor="middle" x="605" y="-354.5" font-family="Arial" font-size="15.00">platform_checker</text>
</g>
<!-- /lib/src/environment/environment_variables.dart&#45;&gt;/lib/src/platform_checker.dart -->
<g id="edge16" class="edge">
<title>/lib/src/environment/environment_variables.dart&#45;&gt;/lib/src/platform_checker.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M601.08,-421.64C601.73,-411.3 602.57,-397.94 603.3,-386.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="606.8,-386.25 603.93,-376.05 599.82,-385.81 606.8,-386.25"/>
</g>
<!-- /lib/src/propagation_context.dart -->
<g id="node10" class="node">
<title>/lib/src/propagation_context.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2945,-376C2945,-376 2817,-376 2817,-376 2811,-376 2805,-370 2805,-364 2805,-364 2805,-352 2805,-352 2805,-346 2811,-340 2817,-340 2817,-340 2945,-340 2945,-340 2951,-340 2957,-346 2957,-352 2957,-352 2957,-364 2957,-364 2957,-370 2951,-376 2945,-376"/>
<text text-anchor="middle" x="2881" y="-354.5" font-family="Arial" font-size="15.00">propagation_context</text>
</g>
<!-- /lib/src/sentry_baggage.dart -->
<g id="node60" class="node">
<title>/lib/src/sentry_baggage.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2981,-896C2981,-896 2881,-896 2881,-896 2875,-896 2869,-890 2869,-884 2869,-884 2869,-872 2869,-872 2869,-866 2875,-860 2881,-860 2881,-860 2981,-860 2981,-860 2987,-860 2993,-866 2993,-872 2993,-872 2993,-884 2993,-884 2993,-890 2987,-896 2981,-896"/>
<text text-anchor="middle" x="2931" y="-874.5" font-family="Arial" font-size="15.00">sentry_baggage</text>
</g>
<!-- /lib/src/propagation_context.dart&#45;&gt;/lib/src/sentry_baggage.dart -->
<g id="edge18" class="edge">
<title>/lib/src/propagation_context.dart&#45;&gt;/lib/src/sentry_baggage.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2867.35,-376.2C2860.05,-386.51 2851.8,-400.26 2848,-414 2842.78,-432.85 2844.99,-438.68 2848,-458 2853.17,-491.19 2848.89,-504.71 2871,-530 2890.1,-551.85 2914.45,-533.49 2930,-558 2937.74,-570.2 2933.52,-774.58 2931.7,-849.64"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2928.2,-849.65 2931.45,-859.74 2935.2,-849.83 2928.2,-849.65"/>
</g>
<!-- /lib/src/propagation_context.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge17" class="edge">
<title>/lib/src/propagation_context.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2881,-339.7C2881,-331.98 2881,-322.71 2881,-314.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2884.5,-314.1 2881,-304.1 2877.5,-314.1 2884.5,-314.1"/>
</g>
<!-- /lib/src/sentry_trace_origins.dart -->
<g id="node11" class="node">
<title>/lib/src/sentry_trace_origins.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5426,-530C5426,-530 5298,-530 5298,-530 5292,-530 5286,-524 5286,-518 5286,-518 5286,-506 5286,-506 5286,-500 5292,-494 5298,-494 5298,-494 5426,-494 5426,-494 5432,-494 5438,-500 5438,-506 5438,-506 5438,-518 5438,-518 5438,-524 5432,-530 5426,-530"/>
<text text-anchor="middle" x="5362" y="-508.5" font-family="Arial" font-size="15.00">sentry_trace_origins</text>
</g>
<!-- /lib/src/sentry_isolate_extension.dart -->
<g id="node12" class="node">
<title>/lib/src/sentry_isolate_extension.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5056.5,-1216C5056.5,-1216 4899.5,-1216 4899.5,-1216 4893.5,-1216 4887.5,-1210 4887.5,-1204 4887.5,-1204 4887.5,-1192 4887.5,-1192 4887.5,-1186 4893.5,-1180 4899.5,-1180 4899.5,-1180 5056.5,-1180 5056.5,-1180 5062.5,-1180 5068.5,-1186 5068.5,-1192 5068.5,-1192 5068.5,-1204 5068.5,-1204 5068.5,-1210 5062.5,-1216 5056.5,-1216"/>
<text text-anchor="middle" x="4978" y="-1194.5" font-family="Arial" font-size="15.00">sentry_isolate_extension</text>
</g>
<!-- /lib/src/hub_adapter.dart -->
<g id="node96" class="node">
<title>/lib/src/hub_adapter.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4977.5,-1072C4977.5,-1072 4900.5,-1072 4900.5,-1072 4894.5,-1072 4888.5,-1066 4888.5,-1060 4888.5,-1060 4888.5,-1048 4888.5,-1048 4888.5,-1042 4894.5,-1036 4900.5,-1036 4900.5,-1036 4977.5,-1036 4977.5,-1036 4983.5,-1036 4989.5,-1042 4989.5,-1048 4989.5,-1048 4989.5,-1060 4989.5,-1060 4989.5,-1066 4983.5,-1072 4977.5,-1072"/>
<text text-anchor="middle" x="4939" y="-1050.5" font-family="Arial" font-size="15.00">hub_adapter</text>
</g>
<!-- /lib/src/sentry_isolate_extension.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge21" class="edge">
<title>/lib/src/sentry_isolate_extension.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4958.96,-1179.84C4949.82,-1170.3 4939.83,-1157.62 4935,-1144 4927.96,-1124.14 4929.54,-1100.21 4932.57,-1082.16"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4936.02,-1082.78 4934.46,-1072.3 4929.14,-1081.46 4936.02,-1082.78"/>
</g>
<!-- /lib/src/sentry_isolate.dart -->
<g id="node136" class="node">
<title>/lib/src/sentry_isolate.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5056.5,-1144C5056.5,-1144 4971.5,-1144 4971.5,-1144 4965.5,-1144 4959.5,-1138 4959.5,-1132 4959.5,-1132 4959.5,-1120 4959.5,-1120 4959.5,-1114 4965.5,-1108 4971.5,-1108 4971.5,-1108 5056.5,-1108 5056.5,-1108 5062.5,-1108 5068.5,-1114 5068.5,-1120 5068.5,-1120 5068.5,-1132 5068.5,-1132 5068.5,-1138 5062.5,-1144 5056.5,-1144"/>
<text text-anchor="middle" x="5014" y="-1122.5" font-family="Arial" font-size="15.00">sentry_isolate</text>
</g>
<!-- /lib/src/sentry_isolate_extension.dart&#45;&gt;/lib/src/sentry_isolate.dart -->
<g id="edge19" class="edge">
<title>/lib/src/sentry_isolate_extension.dart&#45;&gt;/lib/src/sentry_isolate.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4986.9,-1179.7C4991,-1171.73 4995.95,-1162.1 5000.49,-1153.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5003.74,-1154.6 5005.2,-1144.1 4997.52,-1151.4 5003.74,-1154.6"/>
</g>
<!-- /lib/src/sentry_isolate_extension.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge20" class="edge">
<title>/lib/src/sentry_isolate_extension.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4887.49,-1196.35C4679.52,-1193.06 4152.77,-1174.38 3727,-1072 3491.47,-1015.37 3165.6,-903.9 3316,-714 3325.64,-701.83 3337.2,-714.79 3350,-706 3379.27,-685.9 3363.65,-656.67 3395,-640 3413.71,-630.05 4899.96,-634.54 4921,-632 4977.41,-625.18 5041.11,-608.11 5080.16,-596.53"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5081.2,-599.87 5089.77,-593.64 5079.19,-593.16 5081.2,-599.87"/>
</g>
<!-- /lib/src/sentry_item_type.dart -->
<g id="node13" class="node">
<title>/lib/src/sentry_item_type.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2287.5,-602C2287.5,-602 2180.5,-602 2180.5,-602 2174.5,-602 2168.5,-596 2168.5,-590 2168.5,-590 2168.5,-578 2168.5,-578 2168.5,-572 2174.5,-566 2180.5,-566 2180.5,-566 2287.5,-566 2287.5,-566 2293.5,-566 2299.5,-572 2299.5,-578 2299.5,-578 2299.5,-590 2299.5,-590 2299.5,-596 2293.5,-602 2287.5,-602"/>
<text text-anchor="middle" x="2234" y="-580.5" font-family="Arial" font-size="15.00">sentry_item_type</text>
</g>
<!-- /lib/src/diagnostic_logger.dart -->
<g id="node14" class="node">
<title>/lib/src/diagnostic_logger.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3600.5,-458C3600.5,-458 3491.5,-458 3491.5,-458 3485.5,-458 3479.5,-452 3479.5,-446 3479.5,-446 3479.5,-434 3479.5,-434 3479.5,-428 3485.5,-422 3491.5,-422 3491.5,-422 3600.5,-422 3600.5,-422 3606.5,-422 3612.5,-428 3612.5,-434 3612.5,-434 3612.5,-446 3612.5,-446 3612.5,-452 3606.5,-458 3600.5,-458"/>
<text text-anchor="middle" x="3546" y="-436.5" font-family="Arial" font-size="15.00">diagnostic_logger</text>
</g>
<!-- /lib/src/diagnostic_logger.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge23" class="edge">
<title>/lib/src/diagnostic_logger.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3503.74,-458.11C3477.09,-467.95 3442.25,-480.36 3412.35,-490.69"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3411.14,-487.4 3402.83,-493.97 3413.42,-494.02 3411.14,-487.4"/>
</g>
<!-- /lib/src/diagnostic_logger.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge22" class="edge">
<title>/lib/src/diagnostic_logger.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3523.46,-421.89C3489.59,-397.23 3422.64,-352.4 3358,-332 3278.24,-306.83 3029.46,-293.43 2926.41,-288.85"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.46,-285.35 2916.32,-288.41 2926.16,-292.34 2926.46,-285.35"/>
</g>
<!-- /lib/src/sentry_traces_sampler.dart -->
<g id="node15" class="node">
<title>/lib/src/sentry_traces_sampler.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5692.5,-530C5692.5,-530 5547.5,-530 5547.5,-530 5541.5,-530 5535.5,-524 5535.5,-518 5535.5,-518 5535.5,-506 5535.5,-506 5535.5,-500 5541.5,-494 5547.5,-494 5547.5,-494 5692.5,-494 5692.5,-494 5698.5,-494 5704.5,-500 5704.5,-506 5704.5,-506 5704.5,-518 5704.5,-518 5704.5,-524 5698.5,-530 5692.5,-530"/>
<text text-anchor="middle" x="5620" y="-508.5" font-family="Arial" font-size="15.00">sentry_traces_sampler</text>
</g>
<!-- /lib/src/sentry_traces_sampler.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge24" class="edge">
<title>/lib/src/sentry_traces_sampler.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5704.73,-529.63C5705.83,-529.76 5706.92,-529.88 5708,-530 5765.79,-536.31 6710.35,-519.61 6754,-558 6818.86,-615.03 6723.82,-688.05 6784,-750 6817.44,-784.42 6959.22,-796.97 7029.37,-801.13"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.4,-804.64 7039.58,-801.71 7029.79,-797.65 7029.4,-804.64"/>
</g>
<!-- /lib/src/invalid_sentry_trace_header_exception.dart -->
<g id="node16" class="node">
<title>/lib/src/invalid_sentry_trace_header_exception.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5646,-148C5646,-148 5394,-148 5394,-148 5388,-148 5382,-142 5382,-136 5382,-136 5382,-124 5382,-124 5382,-118 5388,-112 5394,-112 5394,-112 5646,-112 5646,-112 5652,-112 5658,-118 5658,-124 5658,-124 5658,-136 5658,-136 5658,-142 5652,-148 5646,-148"/>
<text text-anchor="middle" x="5520" y="-126.5" font-family="Arial" font-size="15.00">invalid_sentry_trace_header_exception</text>
</g>
<!-- /lib/src/sentry.dart -->
<g id="node17" class="node">
<title>/lib/src/sentry.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4788,-1144C4788,-1144 4754,-1144 4754,-1144 4748,-1144 4742,-1138 4742,-1132 4742,-1132 4742,-1120 4742,-1120 4742,-1114 4748,-1108 4754,-1108 4754,-1108 4788,-1108 4788,-1108 4794,-1108 4800,-1114 4800,-1120 4800,-1120 4800,-1132 4800,-1132 4800,-1138 4794,-1144 4788,-1144"/>
<text text-anchor="middle" x="4771" y="-1122.5" font-family="Arial" font-size="15.00">sentry</text>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/noop_hub.dart -->
<g id="edge34" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/noop_hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4741.88,-1125.23C4551.27,-1126.56 3478.92,-1131.36 3132.8,-1071.98"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3133.23,-1068.5 3122.77,-1070.2 3132.01,-1075.39 3133.23,-1068.5"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/environment/environment_variables.dart -->
<g id="edge27" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/environment/environment_variables.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4741.82,-1124.42C4593.18,-1121.47 3905.19,-1108 3342,-1100 3306.38,-1099.49 804.12,-1095.1 777,-1072 726.77,-1029.22 718.91,-540 681,-486 674.56,-476.83 665.63,-469.31 656.03,-463.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="657.5,-460.04 647.09,-458.05 653.99,-466.09 657.5,-460.04"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/sentry_client.dart -->
<g id="edge36" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/sentry_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4741.97,-1124.75C4528.38,-1122.85 3203.82,-1109.62 3030,-1072 2966.64,-1058.29 2898.3,-1026.37 2856.58,-1004.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2858.06,-1001.55 2847.58,-1000 2854.81,-1007.75 2858.06,-1001.55"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge37" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4775.75,-1107.96C4778.4,-1097.65 4781.47,-1084.17 4783,-1072 4799.78,-938.71 4864.43,-865.56 4770,-770 4734.87,-734.45 4595.13,-759.14 4546,-750 4358.74,-715.15 4316.88,-685.25 4134,-632 4022.74,-599.6 3997.88,-579.41 3884,-558 3717.72,-526.74 3518.27,-517.18 3416.29,-514.27"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.22,-510.77 3406.13,-513.99 3416.03,-517.77 3416.22,-510.77"/>
</g>
<!-- /lib/src/event_processor/deduplication_event_processor.dart -->
<g id="node68" class="node">
<title>/lib/src/event_processor/deduplication_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3752.5,-896C3752.5,-896 3553.5,-896 3553.5,-896 3547.5,-896 3541.5,-890 3541.5,-884 3541.5,-884 3541.5,-872 3541.5,-872 3541.5,-866 3547.5,-860 3553.5,-860 3553.5,-860 3752.5,-860 3752.5,-860 3758.5,-860 3764.5,-866 3764.5,-872 3764.5,-872 3764.5,-884 3764.5,-884 3764.5,-890 3758.5,-896 3752.5,-896"/>
<text text-anchor="middle" x="3653" y="-874.5" font-family="Arial" font-size="15.00">deduplication_event_processor</text>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/event_processor/deduplication_event_processor.dart -->
<g id="edge28" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/event_processor/deduplication_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4741.99,-1124.43C4619.49,-1121.12 4134.12,-1099.18 3770,-956 3737.09,-943.06 3703.82,-919.97 3681.21,-902.44"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3683.11,-899.48 3673.09,-896.03 3678.77,-904.98 3683.11,-899.48"/>
</g>
<!-- /lib/src/event_processor/exception/exception_event_processor.dart -->
<g id="node69" class="node">
<title>/lib/src/event_processor/exception/exception_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4465,-822C4465,-822 4289,-822 4289,-822 4283,-822 4277,-816 4277,-810 4277,-810 4277,-798 4277,-798 4277,-792 4283,-786 4289,-786 4289,-786 4465,-786 4465,-786 4471,-786 4477,-792 4477,-798 4477,-798 4477,-810 4477,-810 4477,-816 4471,-822 4465,-822"/>
<text text-anchor="middle" x="4377" y="-800.5" font-family="Arial" font-size="15.00">exception_event_processor</text>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/event_processor/exception/exception_event_processor.dart -->
<g id="edge30" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/event_processor/exception/exception_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4772.85,-1107.98C4778.2,-1054.95 4791.18,-894.22 4756,-860 4745.05,-849.34 4495.89,-855.44 4481,-852 4458.27,-846.76 4434.45,-836.39 4415.35,-826.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4416.87,-823.57 4406.38,-822.07 4413.64,-829.79 4416.87,-823.57"/>
</g>
<!-- /lib/src/event_processor/enricher/enricher_event_processor.dart -->
<g id="node73" class="node">
<title>/lib/src/event_processor/enricher/enricher_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3970,-822C3970,-822 3804,-822 3804,-822 3798,-822 3792,-816 3792,-810 3792,-810 3792,-798 3792,-798 3792,-792 3798,-786 3804,-786 3804,-786 3970,-786 3970,-786 3976,-786 3982,-792 3982,-798 3982,-798 3982,-810 3982,-810 3982,-816 3976,-822 3970,-822"/>
<text text-anchor="middle" x="3887" y="-800.5" font-family="Arial" font-size="15.00">enricher_event_processor</text>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/event_processor/enricher/enricher_event_processor.dart -->
<g id="edge26" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/event_processor/enricher/enricher_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4769.57,-1107.92C4764.66,-1049.98 4748.6,-864.22 4744,-860 4733.83,-850.66 4260.77,-853.01 4247,-852 4160.84,-845.7 4063.76,-832.68 3992.14,-821.93"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3992.61,-818.47 3982.2,-820.43 3991.57,-825.39 3992.61,-818.47"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge29" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4741.95,-1124.54C4642.4,-1122.61 4306.11,-1113.24 4032,-1072 3805.56,-1037.93 3729.17,-1068.99 3530,-956 3474.23,-924.36 3453.96,-911.48 3430,-852 3407.63,-796.46 3416.98,-724.42 3424.66,-685.97"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3428.11,-686.57 3426.76,-676.06 3421.26,-685.12 3428.11,-686.57"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge39" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4779.45,-1107.97C4784.46,-1097.65 4790.83,-1084.17 4796,-1072 4816.08,-1024.75 4801.35,-1000.95 4837,-964 4843.36,-957.41 4850.93,-963.63 4856,-956 4879.01,-921.35 4866.23,-797.47 4835,-770 4801.96,-740.94 4680.27,-762.22 4638,-750 4609.38,-741.73 4539.56,-705.15 4494.91,-680.96"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4496.52,-677.85 4486.06,-676.15 4493.18,-684 4496.52,-677.85"/>
</g>
<!-- /lib/src/integration.dart -->
<g id="node85" class="node">
<title>/lib/src/integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4960,-676C4960,-676 4898,-676 4898,-676 4892,-676 4886,-670 4886,-664 4886,-664 4886,-652 4886,-652 4886,-646 4892,-640 4898,-640 4898,-640 4960,-640 4960,-640 4966,-640 4972,-646 4972,-652 4972,-652 4972,-664 4972,-664 4972,-670 4966,-676 4960,-676"/>
<text text-anchor="middle" x="4929" y="-654.5" font-family="Arial" font-size="15.00">integration</text>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/integration.dart -->
<g id="edge33" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4787.69,-1107.79C4796.78,-1097.85 4807.81,-1084.81 4816,-1072 4844.49,-1027.43 4825.51,-998.03 4866,-964 4876.56,-955.12 4886.13,-965.65 4896,-956 4964.19,-889.34 4942.53,-844.4 4956,-750 4958.26,-734.16 4959.62,-729.59 4956,-714 4953.7,-704.11 4949.36,-693.95 4944.83,-685.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4947.77,-683.17 4939.91,-676.07 4941.62,-686.52 4947.77,-683.17"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge35" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4741.82,-1124.42C4593.18,-1121.49 3905.19,-1108.08 3342,-1100 3307.24,-1099.5 868.15,-1090.94 839,-1072 784.3,-1036.46 762,-867.12 762,-805 762,-805 762,-805 762,-439 762,-391.09 739.38,-364.04 775,-332 813.38,-297.48 2581.43,-306.15 2633,-304 2703.85,-301.04 2785.92,-294.88 2835.62,-290.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2836.03,-294.32 2845.71,-290.01 2835.46,-287.34 2836.03,-294.32"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge32" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4800.22,-1110.5C4822.92,-1100.5 4854.95,-1087.06 4882.56,-1075.92"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4884.15,-1079.05 4892.12,-1072.08 4881.54,-1072.56 4884.15,-1079.05"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge38" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4741.82,-1124.42C4593.18,-1121.5 3905.19,-1108.12 3342,-1100 3307.7,-1099.51 896.12,-1096.4 872,-1072 758.87,-957.58 792.86,-804.36 926,-714 942.02,-703.13 1608.21,-718.49 1623,-706 1645.78,-686.77 1614.98,-662.09 1635,-640 1643.69,-630.41 1650.69,-636.01 1663,-632 1687.72,-623.94 1714.78,-614.15 1737.63,-605.59"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1739.02,-608.81 1747.15,-602.01 1736.55,-602.26 1739.02,-608.81"/>
</g>
<!-- /lib/src/run_zoned_guarded_integration.dart -->
<g id="node106" class="node">
<title>/lib/src/run_zoned_guarded_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5406,-750C5406,-750 5202,-750 5202,-750 5196,-750 5190,-744 5190,-738 5190,-738 5190,-726 5190,-726 5190,-720 5196,-714 5202,-714 5202,-714 5406,-714 5406,-714 5412,-714 5418,-720 5418,-726 5418,-726 5418,-738 5418,-738 5418,-744 5412,-750 5406,-750"/>
<text text-anchor="middle" x="5304" y="-728.5" font-family="Arial" font-size="15.00">run_zoned_guarded_integration</text>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/run_zoned_guarded_integration.dart -->
<g id="edge25" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/run_zoned_guarded_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4800.19,-1120.58C4889.17,-1106.27 5151,-1057.83 5151,-983 5151,-983 5151,-983 5151,-877 5151,-819.16 5209.71,-777.72 5254.39,-754.56"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5256.04,-757.65 5263.4,-750.03 5252.89,-751.39 5256.04,-757.65"/>
</g>
<!-- /lib/src/sentry.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge31" class="edge">
<title>/lib/src/sentry.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4791.53,-1107.81C4824.35,-1079.33 4888.82,-1019.24 4927,-956 5005.32,-826.28 4967.57,-763.76 5055,-640 5063.2,-628.39 5074.31,-617.55 5084.86,-608.61"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5087.22,-611.2 5092.76,-602.17 5082.8,-605.77 5087.22,-611.2"/>
</g>
<!-- /lib/src/sentry_envelope_item.dart -->
<g id="node18" class="node">
<title>/lib/src/sentry_envelope_item.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2443.5,-676C2443.5,-676 2304.5,-676 2304.5,-676 2298.5,-676 2292.5,-670 2292.5,-664 2292.5,-664 2292.5,-652 2292.5,-652 2292.5,-646 2298.5,-640 2304.5,-640 2304.5,-640 2443.5,-640 2443.5,-640 2449.5,-640 2455.5,-646 2455.5,-652 2455.5,-652 2455.5,-664 2455.5,-664 2455.5,-670 2449.5,-676 2443.5,-676"/>
<text text-anchor="middle" x="2374" y="-654.5" font-family="Arial" font-size="15.00">sentry_envelope_item</text>
</g>
<!-- /lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge42" class="edge">
<title>/lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2455.9,-640.56C2457.27,-640.37 2458.64,-640.18 2460,-640 2473.14,-638.27 2569.95,-641.68 2579,-632 2601.46,-607.98 2601.66,-581.84 2579,-558 2536.53,-513.33 2083.21,-550.25 2025,-530 1881.73,-480.17 1886.39,-391.84 1747,-332 1593.06,-265.91 1543.7,-276.63 1377,-260 1360.32,-258.34 181.81,-263.89 170,-252 149.33,-231.19 151.63,-208.87 170,-186 196.08,-153.54 318.28,-139.08 381.26,-133.7"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="381.89,-137.16 391.57,-132.86 381.31,-130.19 381.89,-137.16"/>
</g>
<!-- /lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_item_type.dart -->
<g id="edge44" class="edge">
<title>/lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_item_type.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2331.07,-639.95C2325.27,-637.41 2319.45,-634.72 2314,-632 2299.1,-624.56 2283.19,-615.47 2269.52,-607.27"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2271.25,-604.23 2260.89,-602.04 2267.62,-610.22 2271.25,-604.23"/>
</g>
<!-- /lib/src/sentry_envelope_item_header.dart -->
<g id="node66" class="node">
<title>/lib/src/sentry_envelope_item_header.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2544,-602C2544,-602 2350,-602 2350,-602 2344,-602 2338,-596 2338,-590 2338,-590 2338,-578 2338,-578 2338,-572 2344,-566 2350,-566 2350,-566 2544,-566 2544,-566 2550,-566 2556,-572 2556,-578 2556,-578 2556,-590 2556,-590 2556,-596 2550,-602 2544,-602"/>
<text text-anchor="middle" x="2447" y="-580.5" font-family="Arial" font-size="15.00">sentry_envelope_item_header</text>
</g>
<!-- /lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_envelope_item_header.dart -->
<g id="edge45" class="edge">
<title>/lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_envelope_item_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2391.3,-639.94C2400.6,-630.77 2412.22,-619.31 2422.43,-609.23"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2424.91,-611.7 2429.57,-602.19 2419.99,-606.72 2424.91,-611.7"/>
</g>
<!-- /lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge41" class="edge">
<title>/lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2335.43,-639.86C2332.92,-637.54 2330.72,-634.93 2329,-632 2312.32,-603.65 2311.71,-585.98 2329,-558 2443.32,-373.03 2725.15,-310.84 2835.58,-293.15"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2836.38,-296.57 2845.72,-291.58 2835.3,-289.65 2836.38,-296.57"/>
</g>
<!-- /lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="node88" class="node">
<title>/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5685.5,-602C5685.5,-602 5568.5,-602 5568.5,-602 5562.5,-602 5556.5,-596 5556.5,-590 5556.5,-590 5556.5,-578 5556.5,-578 5556.5,-572 5562.5,-566 5568.5,-566 5568.5,-566 5685.5,-566 5685.5,-566 5691.5,-566 5697.5,-572 5697.5,-578 5697.5,-578 5697.5,-590 5697.5,-590 5697.5,-596 5691.5,-602 5685.5,-602"/>
<text text-anchor="middle" x="5627" y="-580.5" font-family="Arial" font-size="15.00">sentry_attachment</text>
</g>
<!-- /lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="edge43" class="edge">
<title>/lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2455.53,-653.62C2558.91,-649.52 2742.64,-642.78 2900,-640 2936.83,-639.35 5516.62,-642.23 5552,-632 5568.59,-627.2 5585.07,-617.56 5598.33,-608.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5600.55,-610.96 5606.56,-602.24 5596.42,-605.32 5600.55,-610.96"/>
</g>
<!-- /lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge46" class="edge">
<title>/lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2292.3,-647.72C2252.13,-643.1 2203.02,-637.37 2159,-632 2065.12,-620.55 1957.86,-606.77 1883.46,-597.1"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1883.55,-593.58 1873.18,-595.77 1882.65,-600.53 1883.55,-593.58"/>
</g>
<!-- /lib/src/client_reports/client_report.dart -->
<g id="node128" class="node">
<title>/lib/src/client_reports/client_report.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1578.5,-530C1578.5,-530 1503.5,-530 1503.5,-530 1497.5,-530 1491.5,-524 1491.5,-518 1491.5,-518 1491.5,-506 1491.5,-506 1491.5,-500 1497.5,-494 1503.5,-494 1503.5,-494 1578.5,-494 1578.5,-494 1584.5,-494 1590.5,-500 1590.5,-506 1590.5,-506 1590.5,-518 1590.5,-518 1590.5,-524 1584.5,-530 1578.5,-530"/>
<text text-anchor="middle" x="1541" y="-508.5" font-family="Arial" font-size="15.00">client_report</text>
</g>
<!-- /lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/client_reports/client_report.dart -->
<g id="edge40" class="edge">
<title>/lib/src/sentry_envelope_item.dart&#45;&gt;/lib/src/client_reports/client_report.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2455.9,-640.58C2457.27,-640.38 2458.64,-640.19 2460,-640 2471.59,-638.42 2557.05,-640.58 2565,-632 2587.36,-607.88 2587.92,-581.59 2565,-558 2528.83,-520.77 1713.02,-537.68 1600.51,-530.35"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1600.84,-526.86 1590.55,-529.36 1600.15,-533.83 1600.84,-526.86"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/version.dart -->
<g id="edge62" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/version.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2817.81,-963.87C2821.09,-901.58 2835.44,-688.17 2880,-640 2889.37,-629.87 2898.92,-640.23 2910,-632 2967.66,-589.16 3003.46,-509.01 3018.99,-467.47"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3022.29,-468.62 3022.41,-458.03 3015.71,-466.23 3022.29,-468.62"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_baggage.dart -->
<g id="edge47" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_baggage.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2844.48,-963.92C2848.11,-961.38 2851.7,-958.7 2855,-956 2874.42,-940.08 2894.28,-919.78 2908.83,-904.03"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2911.77,-906 2915.92,-896.25 2906.6,-901.28 2911.77,-906"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge57" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2867.53,-964.88C2869.03,-964.57 2870.53,-964.27 2872,-964 2886.24,-961.4 2991.56,-966.02 3002,-956 3016.98,-941.61 3015.09,-789.92 3021,-770 3024.77,-757.3 3119.45,-566.01 3130,-558 3142.15,-548.78 3221.92,-533.92 3281.7,-523.82"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3282.58,-527.22 3291.86,-522.12 3281.42,-520.32 3282.58,-527.22"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge50" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2867.8,-979.81C2951.03,-977.44 3109.32,-971.01 3130,-956 3167.41,-928.85 3142.84,-897.4 3170,-860 3228.51,-779.41 3251.9,-760.44 3340,-714 3352.21,-707.57 3357.63,-712.11 3370,-706 3382.39,-699.88 3394.72,-691.04 3405.02,-682.68"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3407.4,-685.24 3412.8,-676.13 3402.9,-679.89 3407.4,-685.24"/>
</g>
<!-- /lib/src/sentry_trace_context_header.dart -->
<g id="node84" class="node">
<title>/lib/src/sentry_trace_context_header.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2834,-602C2834,-602 2646,-602 2646,-602 2640,-602 2634,-596 2634,-590 2634,-590 2634,-578 2634,-578 2634,-572 2640,-566 2646,-566 2646,-566 2834,-566 2834,-566 2840,-566 2846,-572 2846,-578 2846,-578 2846,-590 2846,-590 2846,-596 2840,-602 2834,-602"/>
<text text-anchor="middle" x="2740" y="-580.5" font-family="Arial" font-size="15.00">sentry_trace_context_header</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart -->
<g id="edge51" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2804.27,-963.98C2802.68,-961.39 2801.2,-958.69 2800,-956 2771.9,-892.87 2763,-874.1 2763,-805 2763,-805 2763,-805 2763,-731 2763,-689.18 2753.68,-641.43 2746.9,-612.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2750.27,-611.3 2744.54,-602.39 2743.47,-612.93 2750.27,-611.3"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge54" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.16,-981.17C2467.2,-982.05 949.8,-985.1 912,-956 891.88,-940.51 862,-758.39 862,-733 862,-733 862,-733 862,-439 862,-318.19 1274.93,-345.31 1395,-332 1668.51,-301.69 2358.1,-316.52 2633,-304 2703.84,-300.77 2785.91,-294.68 2835.61,-290.74"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2836.02,-294.22 2845.71,-289.93 2835.46,-287.24 2836.02,-294.22"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="edge48" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2867.71,-974.36C2893.52,-971.07 2925.41,-967.13 2954,-964 2972.64,-961.96 3021.7,-965.27 3038,-956 3127.01,-905.36 3097.35,-837.91 3174,-770 3212.13,-736.22 3229.3,-738.43 3274,-714 3280.63,-710.38 3282.46,-709.78 3289,-706 3337.07,-678.26 3341.67,-655.35 3395,-640 3452.58,-623.42 5494.45,-648.66 5552,-632 5568.59,-627.2 5585.07,-617.55 5598.33,-608.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5600.55,-610.96 5606.56,-602.23 5596.42,-605.31 5600.55,-610.96"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge52" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.24,-981.15C2479.23,-981.92 1071.25,-984.25 1037,-956 1003.38,-928.27 1045.76,-892.74 1017,-860 1009.86,-851.87 1000.27,-860.82 994,-852 972.88,-822.3 990.16,-806.24 994,-770 996.68,-744.69 986.3,-730.12 1006,-714 1020.81,-701.88 1677.81,-714.43 1695,-706 1735.24,-686.27 1763.85,-640.74 1778.87,-611.5"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1782.14,-612.78 1783.44,-602.27 1775.86,-609.68 1782.14,-612.78"/>
</g>
<!-- /lib/src/transport/noop_transport.dart -->
<g id="node107" class="node">
<title>/lib/src/transport/noop_transport.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1339,-822C1339,-822 1245,-822 1245,-822 1239,-822 1233,-816 1233,-810 1233,-810 1233,-798 1233,-798 1233,-792 1239,-786 1245,-786 1245,-786 1339,-786 1339,-786 1345,-786 1351,-792 1351,-798 1351,-798 1351,-810 1351,-810 1351,-816 1345,-822 1339,-822"/>
<text text-anchor="middle" x="1292" y="-800.5" font-family="Arial" font-size="15.00">noop_transport</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/noop_transport.dart -->
<g id="edge60" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/noop_transport.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.15,-979.34C2677.08,-976.22 2487.07,-968.64 2327,-956 1893.88,-921.81 1759.47,-1010.64 1355,-852 1341.33,-846.64 1328.03,-837.48 1317.19,-828.67"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1319.31,-825.88 1309.42,-822.07 1314.78,-831.21 1319.31,-825.88"/>
</g>
<!-- /lib/src/transport/data_category.dart -->
<g id="node110" class="node">
<title>/lib/src/transport/data_category.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1298,-530C1298,-530 1210,-530 1210,-530 1204,-530 1198,-524 1198,-518 1198,-518 1198,-506 1198,-506 1198,-500 1204,-494 1210,-494 1210,-494 1298,-494 1298,-494 1304,-494 1310,-500 1310,-506 1310,-506 1310,-518 1310,-518 1310,-524 1304,-530 1298,-530"/>
<text text-anchor="middle" x="1254" y="-508.5" font-family="Arial" font-size="15.00">data_category</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge66" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.05,-981.17C2474.77,-982.05 1032.07,-984.92 997,-956 963.37,-928.27 999.81,-897.14 977,-860 974.02,-855.15 969.47,-857.13 967,-852 926,-766.7 939.52,-721.87 987,-640 1037.58,-552.78 1090.08,-557.34 1187.87,-531.62"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1189.05,-534.93 1197.8,-528.95 1187.23,-528.17 1189.05,-534.93"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart -->
<g id="node112" class="node">
<title>/lib/src/transport/rate_limiter.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1097.5,-750C1097.5,-750 1030.5,-750 1030.5,-750 1024.5,-750 1018.5,-744 1018.5,-738 1018.5,-738 1018.5,-726 1018.5,-726 1018.5,-720 1024.5,-714 1030.5,-714 1030.5,-714 1097.5,-714 1097.5,-714 1103.5,-714 1109.5,-720 1109.5,-726 1109.5,-726 1109.5,-738 1109.5,-738 1109.5,-744 1103.5,-750 1097.5,-750"/>
<text text-anchor="middle" x="1064" y="-728.5" font-family="Arial" font-size="15.00">rate_limiter</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/rate_limiter.dart -->
<g id="edge53" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/rate_limiter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.23,-981.18C2495.54,-982.01 1231.44,-984.36 1154,-956 1090.84,-932.87 1096.08,-892.15 1037,-860 1026.75,-854.42 1018.88,-861.42 1012,-852 990.51,-822.56 995.29,-802.39 1012,-770 1014.57,-765.01 1018.16,-760.55 1022.26,-756.61"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1024.67,-759.15 1030.09,-750.05 1020.18,-753.79 1024.67,-759.15"/>
</g>
<!-- /lib/src/transport/http_transport.dart -->
<g id="node115" class="node">
<title>/lib/src/transport/http_transport.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1119,-822C1119,-822 1033,-822 1033,-822 1027,-822 1021,-816 1021,-810 1021,-810 1021,-798 1021,-798 1021,-792 1027,-786 1033,-786 1033,-786 1119,-786 1119,-786 1125,-786 1131,-792 1131,-798 1131,-798 1131,-810 1131,-810 1131,-816 1125,-822 1119,-822"/>
<text text-anchor="middle" x="1076" y="-800.5" font-family="Arial" font-size="15.00">http_transport</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/http_transport.dart -->
<g id="edge59" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/transport/http_transport.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.32,-980.69C2518.58,-979.08 1444.19,-971.25 1375,-956 1263.46,-931.41 1238.83,-909.25 1140,-852 1128.05,-845.08 1115.64,-836.37 1104.98,-828.34"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1106.9,-825.4 1096.83,-822.09 1102.64,-830.96 1106.9,-825.4"/>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/scope.dart -->
<g id="edge55" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/scope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2867.53,-980.92C2919.35,-979.78 2995.33,-974.81 3017,-956 3035.56,-939.88 3048.37,-871.08 3054.28,-832.15"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3057.78,-832.43 3055.77,-822.03 3050.85,-831.41 3057.78,-832.43"/>
</g>
<!-- /lib/src/event_processor.dart -->
<g id="node118" class="node">
<title>/lib/src/event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3822,-750C3822,-750 3718,-750 3718,-750 3712,-750 3706,-744 3706,-738 3706,-738 3706,-726 3706,-726 3706,-720 3712,-714 3718,-714 3718,-714 3822,-714 3822,-714 3828,-714 3834,-720 3834,-726 3834,-726 3834,-738 3834,-738 3834,-744 3828,-750 3822,-750"/>
<text text-anchor="middle" x="3770" y="-728.5" font-family="Arial" font-size="15.00">event_processor</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/event_processor.dart -->
<g id="edge49" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2867.72,-979.92C2951.4,-977.73 3114.62,-971.55 3170,-956 3343.39,-907.3 3357.85,-822.88 3530,-770 3598.53,-748.95 3621.49,-761.78 3696.12,-749.98"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3696.7,-753.43 3705.99,-748.33 3695.55,-746.53 3696.7,-753.43"/>
</g>
<!-- /lib/src/utils/isolate_utils.dart -->
<g id="node123" class="node">
<title>/lib/src/utils/isolate_utils.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6082,-148C6082,-148 6012,-148 6012,-148 6006,-148 6000,-142 6000,-136 6000,-136 6000,-124 6000,-124 6000,-118 6006,-112 6012,-112 6012,-112 6082,-112 6082,-112 6088,-112 6094,-118 6094,-124 6094,-124 6094,-136 6094,-136 6094,-142 6088,-148 6082,-148"/>
<text text-anchor="middle" x="6047" y="-126.5" font-family="Arial" font-size="15.00">isolate_utils</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/utils/isolate_utils.dart -->
<g id="edge61" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/utils/isolate_utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2769.64,-963.95C2709.93,-939.05 2615,-886.63 2615,-805 2615,-805 2615,-805 2615,-657 2615,-611.93 2627.28,-587.27 2593,-558 2554.31,-524.97 2184.58,-545.11 2136,-530 1973.04,-479.33 1964.32,-398.15 1807,-332 1689.74,-282.7 1656.12,-276.53 1530,-260 1512.83,-257.75 295.21,-264.29 283,-252 262.32,-231.19 262.27,-206.76 283,-186 297,-171.98 5972.38,-184.79 5991,-178 6003.67,-173.38 6015.47,-164.5 6024.94,-155.69"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6027.66,-157.92 6032.31,-148.4 6022.74,-152.94 6027.66,-157.92"/>
</g>
<!-- /lib/src/client_reports/client_report_recorder.dart -->
<g id="node131" class="node">
<title>/lib/src/client_reports/client_report_recorder.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1559,-602C1559,-602 1419,-602 1419,-602 1413,-602 1407,-596 1407,-590 1407,-590 1407,-578 1407,-578 1407,-572 1413,-566 1419,-566 1419,-566 1559,-566 1559,-566 1565,-566 1571,-572 1571,-578 1571,-578 1571,-590 1571,-590 1571,-596 1565,-602 1559,-602"/>
<text text-anchor="middle" x="1489" y="-580.5" font-family="Arial" font-size="15.00">client_report_recorder</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart -->
<g id="edge64" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.14,-981.16C2476.98,-981.99 1051.66,-984.58 1017,-956 983.38,-928.27 1024,-894.21 997,-860 991.83,-853.45 984.47,-859.05 980,-852 960.47,-821.23 976.78,-806.3 980,-770 982.23,-744.82 985.11,-738.8 990,-714 996.51,-680.99 980.03,-661.39 1006,-640 1023.03,-625.97 1381.68,-637.67 1403,-632 1421.83,-626.99 1441.02,-617.05 1456.49,-607.61"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1458.55,-610.44 1465.13,-602.14 1454.81,-604.53 1458.55,-610.44"/>
</g>
<!-- /lib/src/client_reports/discard_reason.dart -->
<g id="node132" class="node">
<title>/lib/src/client_reports/discard_reason.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1598.5,-376C1598.5,-376 1503.5,-376 1503.5,-376 1497.5,-376 1491.5,-370 1491.5,-364 1491.5,-364 1491.5,-352 1491.5,-352 1491.5,-346 1497.5,-340 1503.5,-340 1503.5,-340 1598.5,-340 1598.5,-340 1604.5,-340 1610.5,-346 1610.5,-352 1610.5,-352 1610.5,-364 1610.5,-364 1610.5,-370 1604.5,-376 1598.5,-376"/>
<text text-anchor="middle" x="1551" y="-354.5" font-family="Arial" font-size="15.00">discard_reason</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart -->
<g id="edge65" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.06,-981.07C2473.14,-981.38 1015.18,-981.61 977,-956 932.27,-925.99 882,-786.86 882,-733 882,-733 882,-733 882,-511 882,-498.26 836.4,-513.24 1438,-414 1459.77,-410.41 1466.66,-414.54 1487,-406 1500.47,-400.34 1513.76,-391.31 1524.7,-382.67"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1527.07,-385.26 1532.58,-376.21 1522.63,-379.85 1527.07,-385.26"/>
</g>
<!-- /lib/src/sentry_stack_trace_factory.dart -->
<g id="node133" class="node">
<title>/lib/src/sentry_stack_trace_factory.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3324.5,-376C3324.5,-376 3151.5,-376 3151.5,-376 3145.5,-376 3139.5,-370 3139.5,-364 3139.5,-364 3139.5,-352 3139.5,-352 3139.5,-346 3145.5,-340 3151.5,-340 3151.5,-340 3324.5,-340 3324.5,-340 3330.5,-340 3336.5,-346 3336.5,-352 3336.5,-352 3336.5,-364 3336.5,-364 3336.5,-370 3330.5,-376 3324.5,-376"/>
<text text-anchor="middle" x="3238" y="-354.5" font-family="Arial" font-size="15.00">sentry_stack_trace_factory</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_stack_trace_factory.dart -->
<g id="edge58" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_stack_trace_factory.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2815.44,-963.8C2815.26,-961.19 2815.1,-958.52 2815,-956 2813.3,-913.37 2813.11,-902.62 2815,-860 2816.78,-819.88 2817.42,-809.77 2823,-770 2831.17,-711.73 2822.17,-692.87 2848,-640 2850.07,-635.75 2853.33,-636.42 2855,-632 2889.29,-541.38 2789.86,-484.8 2856,-414 2864.37,-405.05 2953.84,-407.49 2966,-406 3024.87,-398.79 3090.4,-387.57 3142.39,-377.89"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3143.08,-381.32 3152.26,-376.04 3141.79,-374.44 3143.08,-381.32"/>
</g>
<!-- /lib/src/sentry_envelope.dart -->
<g id="node138" class="node">
<title>/lib/src/sentry_envelope.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2283,-750C2283,-750 2181,-750 2181,-750 2175,-750 2169,-744 2169,-738 2169,-738 2169,-726 2169,-726 2169,-720 2175,-714 2181,-714 2181,-714 2283,-714 2283,-714 2289,-714 2295,-720 2295,-726 2295,-726 2295,-738 2295,-738 2295,-744 2289,-750 2283,-750"/>
<text text-anchor="middle" x="2232" y="-728.5" font-family="Arial" font-size="15.00">sentry_envelope</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_envelope.dart -->
<g id="edge63" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_envelope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2766.37,-980.65C2723.66,-979.01 2661.43,-973.42 2610,-956 2468.7,-908.14 2322.43,-803.05 2261.44,-756.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2263.47,-753.4 2253.41,-750.06 2259.19,-758.94 2263.47,-753.4"/>
</g>
<!-- /lib/src/sentry_exception_factory.dart -->
<g id="node140" class="node">
<title>/lib/src/sentry_exception_factory.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3429,-458C3429,-458 3269,-458 3269,-458 3263,-458 3257,-452 3257,-446 3257,-446 3257,-434 3257,-434 3257,-428 3263,-422 3269,-422 3269,-422 3429,-422 3429,-422 3435,-422 3441,-428 3441,-434 3441,-434 3441,-446 3441,-446 3441,-452 3435,-458 3429,-458"/>
<text text-anchor="middle" x="3349" y="-436.5" font-family="Arial" font-size="15.00">sentry_exception_factory</text>
</g>
<!-- /lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_exception_factory.dart -->
<g id="edge56" class="edge">
<title>/lib/src/sentry_client.dart&#45;&gt;/lib/src/sentry_exception_factory.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2824,-963.83C2825.03,-961.21 2826.06,-958.54 2827,-956 2842.72,-913.71 2843.49,-901.99 2860,-860 2916.93,-715.22 2917.38,-657.47 3037,-558 3097.92,-507.34 3182.97,-477.39 3248.88,-460.5"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3250.03,-463.83 3258.88,-458 3248.33,-457.03 3250.03,-463.83"/>
</g>
<!-- /lib/src/protocol/span_id.dart -->
<g id="node20" class="node">
<title>/lib/src/protocol/span_id.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3741,-222C3741,-222 3697,-222 3697,-222 3691,-222 3685,-216 3685,-210 3685,-210 3685,-198 3685,-198 3685,-192 3691,-186 3697,-186 3697,-186 3741,-186 3741,-186 3747,-186 3753,-192 3753,-198 3753,-198 3753,-210 3753,-210 3753,-216 3747,-222 3741,-222"/>
<text text-anchor="middle" x="3719" y="-200.5" font-family="Arial" font-size="15.00">span_id</text>
</g>
<!-- /lib/src/protocol/sentry_response.dart -->
<g id="node21" class="node">
<title>/lib/src/protocol/sentry_response.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3055,-222C3055,-222 2951,-222 2951,-222 2945,-222 2939,-216 2939,-210 2939,-210 2939,-198 2939,-198 2939,-192 2945,-186 2951,-186 2951,-186 3055,-186 3055,-186 3061,-186 3067,-192 3067,-198 3067,-198 3067,-210 3067,-210 3067,-216 3061,-222 3055,-222"/>
<text text-anchor="middle" x="3003" y="-200.5" font-family="Arial" font-size="15.00">sentry_response</text>
</g>
<!-- /lib/src/protocol/contexts.dart -->
<g id="node41" class="node">
<title>/lib/src/protocol/contexts.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M525.5,-148C525.5,-148 476.5,-148 476.5,-148 470.5,-148 464.5,-142 464.5,-136 464.5,-136 464.5,-124 464.5,-124 464.5,-118 470.5,-112 476.5,-112 476.5,-112 525.5,-112 525.5,-112 531.5,-112 537.5,-118 537.5,-124 537.5,-124 537.5,-136 537.5,-136 537.5,-142 531.5,-148 525.5,-148"/>
<text text-anchor="middle" x="501" y="-126.5" font-family="Arial" font-size="15.00">contexts</text>
</g>
<!-- /lib/src/protocol/sentry_response.dart&#45;&gt;/lib/src/protocol/contexts.dart -->
<g id="edge67" class="edge">
<title>/lib/src/protocol/sentry_response.dart&#45;&gt;/lib/src/protocol/contexts.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2938.65,-187.19C2935.74,-186.73 2932.84,-186.33 2930,-186 2897.13,-182.17 577.49,-190.88 547,-178 536.05,-173.37 526.4,-164.73 518.8,-156.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="521.46,-153.84 512.42,-148.32 516.04,-158.27 521.46,-153.84"/>
</g>
<!-- /lib/src/utils/iterable_extension.dart -->
<g id="node124" class="node">
<title>/lib/src/utils/iterable_extension.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6433.5,-148C6433.5,-148 6320.5,-148 6320.5,-148 6314.5,-148 6308.5,-142 6308.5,-136 6308.5,-136 6308.5,-124 6308.5,-124 6308.5,-118 6314.5,-112 6320.5,-112 6320.5,-112 6433.5,-112 6433.5,-112 6439.5,-112 6445.5,-118 6445.5,-124 6445.5,-124 6445.5,-136 6445.5,-136 6445.5,-142 6439.5,-148 6433.5,-148"/>
<text text-anchor="middle" x="6377" y="-126.5" font-family="Arial" font-size="15.00">iterable_extension</text>
</g>
<!-- /lib/src/protocol/sentry_response.dart&#45;&gt;/lib/src/utils/iterable_extension.dart -->
<g id="edge68" class="edge">
<title>/lib/src/protocol/sentry_response.dart&#45;&gt;/lib/src/utils/iterable_extension.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3067.35,-187.18C3070.26,-186.73 3073.16,-186.33 3076,-186 3120.47,-180.83 6255.89,-190.04 6299,-178 6316.4,-173.14 6333.81,-163.25 6347.75,-153.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6349.8,-156.63 6355.95,-148.01 6345.77,-150.91 6349.8,-156.63"/>
</g>
<!-- /lib/src/protocol/sentry_thread.dart -->
<g id="node22" class="node">
<title>/lib/src/protocol/sentry_thread.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2333.5,-222C2333.5,-222 2248.5,-222 2248.5,-222 2242.5,-222 2236.5,-216 2236.5,-210 2236.5,-210 2236.5,-198 2236.5,-198 2236.5,-192 2242.5,-186 2248.5,-186 2248.5,-186 2333.5,-186 2333.5,-186 2339.5,-186 2345.5,-192 2345.5,-198 2345.5,-198 2345.5,-210 2345.5,-210 2345.5,-216 2339.5,-222 2333.5,-222"/>
<text text-anchor="middle" x="2291" y="-200.5" font-family="Arial" font-size="15.00">sentry_thread</text>
</g>
<!-- /lib/src/protocol/sentry_stack_trace.dart -->
<g id="node44" class="node">
<title>/lib/src/protocol/sentry_stack_trace.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M827.5,-148C827.5,-148 708.5,-148 708.5,-148 702.5,-148 696.5,-142 696.5,-136 696.5,-136 696.5,-124 696.5,-124 696.5,-118 702.5,-112 708.5,-112 708.5,-112 827.5,-112 827.5,-112 833.5,-112 839.5,-118 839.5,-124 839.5,-124 839.5,-136 839.5,-136 839.5,-142 833.5,-148 827.5,-148"/>
<text text-anchor="middle" x="768" y="-126.5" font-family="Arial" font-size="15.00">sentry_stack_trace</text>
</g>
<!-- /lib/src/protocol/sentry_thread.dart&#45;&gt;/lib/src/protocol/sentry_stack_trace.dart -->
<g id="edge69" class="edge">
<title>/lib/src/protocol/sentry_thread.dart&#45;&gt;/lib/src/protocol/sentry_stack_trace.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2236.35,-187.63C2233.2,-187.01 2230.07,-186.46 2227,-186 1958.44,-145.78 1117.39,-134.36 850.28,-131.7"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="849.92,-128.2 839.89,-131.6 849.85,-135.2 849.92,-128.2"/>
</g>
<!-- /lib/src/protocol/sentry_stack_frame.dart -->
<g id="node23" class="node">
<title>/lib/src/protocol/sentry_stack_frame.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M715.5,-76C715.5,-76 590.5,-76 590.5,-76 584.5,-76 578.5,-70 578.5,-64 578.5,-64 578.5,-52 578.5,-52 578.5,-46 584.5,-40 590.5,-40 590.5,-40 715.5,-40 715.5,-40 721.5,-40 727.5,-46 727.5,-52 727.5,-52 727.5,-64 727.5,-64 727.5,-70 721.5,-76 715.5,-76"/>
<text text-anchor="middle" x="653" y="-54.5" font-family="Arial" font-size="15.00">sentry_stack_frame</text>
</g>
<!-- /lib/src/protocol/sentry_transaction_info.dart -->
<g id="node24" class="node">
<title>/lib/src/protocol/sentry_transaction_info.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3655,-222C3655,-222 3507,-222 3507,-222 3501,-222 3495,-216 3495,-210 3495,-210 3495,-198 3495,-198 3495,-192 3501,-186 3507,-186 3507,-186 3655,-186 3655,-186 3661,-186 3667,-192 3667,-198 3667,-198 3667,-210 3667,-210 3667,-216 3661,-222 3655,-222"/>
<text text-anchor="middle" x="3581" y="-200.5" font-family="Arial" font-size="15.00">sentry_transaction_info</text>
</g>
<!-- /lib/src/protocol/sentry_span.dart -->
<g id="node25" class="node">
<title>/lib/src/protocol/sentry_span.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4770,-222C4770,-222 4696,-222 4696,-222 4690,-222 4684,-216 4684,-210 4684,-210 4684,-198 4684,-198 4684,-192 4690,-186 4696,-186 4696,-186 4770,-186 4770,-186 4776,-186 4782,-192 4782,-198 4782,-198 4782,-210 4782,-210 4782,-216 4776,-222 4770,-222"/>
<text text-anchor="middle" x="4733" y="-200.5" font-family="Arial" font-size="15.00">sentry_span</text>
</g>
<!-- /lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge74" class="edge">
<title>/lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4683.97,-187.77C4680.62,-187.07 4677.27,-186.47 4674,-186 4644.97,-181.87 478,-191.55 452,-178 443.17,-173.4 436.21,-165.23 431,-156.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="433.99,-155.16 426.03,-148.17 427.89,-158.6 433.99,-155.16"/>
</g>
<!-- /lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge73" class="edge">
<title>/lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4739.39,-222.07C4751.94,-258.66 4775.71,-346.19 4741,-406 4659.75,-546.01 4499.8,-423.31 4410,-558 4391.76,-585.37 4393.32,-603.65 4410,-632 4411.53,-634.6 4413.44,-636.95 4415.6,-639.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4413.7,-642.02 4423.73,-645.42 4418,-636.5 4413.7,-642.02"/>
</g>
<!-- /lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge71" class="edge">
<title>/lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4709.93,-222.09C4697.59,-232.89 4681.88,-245.92 4665,-252 4580.67,-282.36 3197.02,-284.8 2926.59,-284.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.35,-281.49 2916.36,-284.99 2926.36,-288.49 2926.35,-281.49"/>
</g>
<!-- /lib/src/sentry_tracer.dart -->
<g id="node93" class="node">
<title>/lib/src/sentry_tracer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5769.5,-148C5769.5,-148 5688.5,-148 5688.5,-148 5682.5,-148 5676.5,-142 5676.5,-136 5676.5,-136 5676.5,-124 5676.5,-124 5676.5,-118 5682.5,-112 5688.5,-112 5688.5,-112 5769.5,-112 5769.5,-112 5775.5,-112 5781.5,-118 5781.5,-124 5781.5,-124 5781.5,-136 5781.5,-136 5781.5,-142 5775.5,-148 5769.5,-148"/>
<text text-anchor="middle" x="5729" y="-126.5" font-family="Arial" font-size="15.00">sentry_tracer</text>
</g>
<!-- /lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/sentry_tracer.dart -->
<g id="edge72" class="edge">
<title>/lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/sentry_tracer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4782.15,-187.59C4785.13,-186.97 4788.09,-186.43 4791,-186 4839.14,-178.87 5620.94,-193.72 5667,-178 5680.96,-173.24 5694.28,-163.99 5704.98,-154.95"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5707.47,-157.41 5712.62,-148.15 5702.82,-152.19 5707.47,-157.41"/>
</g>
<!-- /lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge70" class="edge">
<title>/lib/src/protocol/sentry_span.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4748.37,-222.31C4759.11,-233.06 4774.44,-245.95 4791,-252 4812.14,-259.72 5180.24,-243.93 5196,-260 5277.86,-343.49 5180.74,-497.05 5136.61,-557.55"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5133.65,-555.66 5130.51,-565.78 5139.27,-559.83 5133.65,-555.66"/>
</g>
<!-- /lib/src/protocol/breadcrumb.dart -->
<g id="node26" class="node">
<title>/lib/src/protocol/breadcrumb.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4653.5,-222C4653.5,-222 4580.5,-222 4580.5,-222 4574.5,-222 4568.5,-216 4568.5,-210 4568.5,-210 4568.5,-198 4568.5,-198 4568.5,-192 4574.5,-186 4580.5,-186 4580.5,-186 4653.5,-186 4653.5,-186 4659.5,-186 4665.5,-192 4665.5,-198 4665.5,-198 4665.5,-210 4665.5,-210 4665.5,-216 4659.5,-222 4653.5,-222"/>
<text text-anchor="middle" x="4617" y="-200.5" font-family="Arial" font-size="15.00">breadcrumb</text>
</g>
<!-- /lib/src/protocol/breadcrumb.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge75" class="edge">
<title>/lib/src/protocol/breadcrumb.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4568.49,-187.7C4565.3,-187.03 4562.12,-186.45 4559,-186 4530.77,-181.92 477.29,-191.18 452,-178 443.17,-173.4 436.21,-165.23 431,-156.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="433.99,-155.16 426.03,-148.17 427.89,-158.6 433.99,-155.16"/>
</g>
<!-- /lib/src/protocol/breadcrumb.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge76" class="edge">
<title>/lib/src/protocol/breadcrumb.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4594.24,-222.08C4582.12,-232.87 4566.68,-245.91 4550,-252 4471.51,-280.68 3185.78,-284.46 2926.37,-284.93"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.17,-281.43 2916.18,-284.95 2926.18,-288.43 2926.17,-281.43"/>
</g>
<!-- /lib/src/protocol/sentry_request.dart -->
<g id="node27" class="node">
<title>/lib/src/protocol/sentry_request.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3189,-222C3189,-222 3097,-222 3097,-222 3091,-222 3085,-216 3085,-210 3085,-210 3085,-198 3085,-198 3085,-192 3091,-186 3097,-186 3097,-186 3189,-186 3189,-186 3195,-186 3201,-192 3201,-198 3201,-198 3201,-210 3201,-210 3201,-216 3195,-222 3189,-222"/>
<text text-anchor="middle" x="3143" y="-200.5" font-family="Arial" font-size="15.00">sentry_request</text>
</g>
<!-- /lib/src/utils/http_sanitizer.dart -->
<g id="node119" class="node">
<title>/lib/src/utils/http_sanitizer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6558,-148C6558,-148 6476,-148 6476,-148 6470,-148 6464,-142 6464,-136 6464,-136 6464,-124 6464,-124 6464,-118 6470,-112 6476,-112 6476,-112 6558,-112 6558,-112 6564,-112 6570,-118 6570,-124 6570,-124 6570,-136 6570,-136 6570,-142 6564,-148 6558,-148"/>
<text text-anchor="middle" x="6517" y="-126.5" font-family="Arial" font-size="15.00">http_sanitizer</text>
</g>
<!-- /lib/src/protocol/sentry_request.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart -->
<g id="edge78" class="edge">
<title>/lib/src/protocol/sentry_request.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3201.3,-187.31C3204.23,-186.8 3207.14,-186.36 3210,-186 3254.7,-180.36 6411.26,-192.25 6454,-178 6468.13,-173.29 6481.67,-164.05 6492.55,-155"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6495.1,-157.42 6500.31,-148.19 6490.49,-152.15 6495.1,-157.42"/>
</g>
<!-- /lib/src/protocol/sentry_request.dart&#45;&gt;/lib/src/utils/iterable_extension.dart -->
<g id="edge77" class="edge">
<title>/lib/src/protocol/sentry_request.dart&#45;&gt;/lib/src/utils/iterable_extension.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3201.3,-187.31C3204.23,-186.8 3207.14,-186.36 3210,-186 3252.57,-180.63 6257.68,-189.54 6299,-178 6316.4,-173.14 6333.81,-163.25 6347.75,-153.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6349.8,-156.63 6355.95,-148.01 6345.77,-150.91 6349.8,-156.63"/>
</g>
<!-- /lib/src/protocol/sentry_level.dart -->
<g id="node28" class="node">
<title>/lib/src/protocol/sentry_level.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2909,-222C2909,-222 2837,-222 2837,-222 2831,-222 2825,-216 2825,-210 2825,-210 2825,-198 2825,-198 2825,-192 2831,-186 2837,-186 2837,-186 2909,-186 2909,-186 2915,-186 2921,-192 2921,-198 2921,-198 2921,-210 2921,-210 2921,-216 2915,-222 2909,-222"/>
<text text-anchor="middle" x="2873" y="-200.5" font-family="Arial" font-size="15.00">sentry_level</text>
</g>
<!-- /lib/src/protocol/sdk_version.dart -->
<g id="node29" class="node">
<title>/lib/src/protocol/sdk_version.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M644,-222C644,-222 572,-222 572,-222 566,-222 560,-216 560,-210 560,-210 560,-198 560,-198 560,-192 566,-186 572,-186 572,-186 644,-186 644,-186 650,-186 656,-192 656,-198 656,-198 656,-210 656,-210 656,-216 650,-222 644,-222"/>
<text text-anchor="middle" x="608" y="-200.5" font-family="Arial" font-size="15.00">sdk_version</text>
</g>
<!-- /lib/src/protocol/sentry_package.dart -->
<g id="node50" class="node">
<title>/lib/src/protocol/sentry_package.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M666,-148C666,-148 568,-148 568,-148 562,-148 556,-142 556,-136 556,-136 556,-124 556,-124 556,-118 562,-112 568,-112 568,-112 666,-112 666,-112 672,-112 678,-118 678,-124 678,-124 678,-136 678,-136 678,-142 672,-148 666,-148"/>
<text text-anchor="middle" x="617" y="-126.5" font-family="Arial" font-size="15.00">sentry_package</text>
</g>
<!-- /lib/src/protocol/sdk_version.dart&#45;&gt;/lib/src/protocol/sentry_package.dart -->
<g id="edge79" class="edge">
<title>/lib/src/protocol/sdk_version.dart&#45;&gt;/lib/src/protocol/sentry_package.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M610.13,-185.94C611.16,-177.72 612.42,-167.66 613.57,-158.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="617.05,-158.8 614.82,-148.44 610.11,-157.93 617.05,-158.8"/>
</g>
<!-- /lib/src/protocol/sentry_trace_header.dart -->
<g id="node30" class="node">
<title>/lib/src/protocol/sentry_trace_header.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2665.5,-222C2665.5,-222 2534.5,-222 2534.5,-222 2528.5,-222 2522.5,-216 2522.5,-210 2522.5,-210 2522.5,-198 2522.5,-198 2522.5,-192 2528.5,-186 2534.5,-186 2534.5,-186 2665.5,-186 2665.5,-186 2671.5,-186 2677.5,-192 2677.5,-198 2677.5,-198 2677.5,-210 2677.5,-210 2677.5,-216 2671.5,-222 2665.5,-222"/>
<text text-anchor="middle" x="2600" y="-200.5" font-family="Arial" font-size="15.00">sentry_trace_header</text>
</g>
<!-- /lib/src/protocol/sentry_trace_header.dart&#45;&gt;/lib/src/invalid_sentry_trace_header_exception.dart -->
<g id="edge80" class="edge">
<title>/lib/src/protocol/sentry_trace_header.dart&#45;&gt;/lib/src/invalid_sentry_trace_header_exception.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2677.72,-186.94C2680.51,-186.59 2683.27,-186.27 2686,-186 2760.27,-178.62 5299.27,-189.61 5373,-178 5405.67,-172.85 5441.02,-161.74 5468.86,-151.61"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5470.22,-154.84 5478.38,-148.09 5467.78,-148.28 5470.22,-154.84"/>
</g>
<!-- /lib/src/protocol/sentry_trace_header.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge81" class="edge">
<title>/lib/src/protocol/sentry_trace_header.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2618.28,-222.22C2633.58,-232.37 2655.78,-244.64 2677,-252 2729.44,-270.19 2793.42,-278.57 2835.71,-282.27"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.46,-285.76 2845.71,-283.09 2836.03,-278.79 2835.46,-285.76"/>
</g>
<!-- /lib/src/protocol/sentry_transaction.dart -->
<g id="node31" class="node">
<title>/lib/src/protocol/sentry_transaction.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2492,-222C2492,-222 2376,-222 2376,-222 2370,-222 2364,-216 2364,-210 2364,-210 2364,-198 2364,-198 2364,-192 2370,-186 2376,-186 2376,-186 2492,-186 2492,-186 2498,-186 2504,-192 2504,-198 2504,-198 2504,-210 2504,-210 2504,-216 2498,-222 2492,-222"/>
<text text-anchor="middle" x="2434" y="-200.5" font-family="Arial" font-size="15.00">sentry_transaction</text>
</g>
<!-- /lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge84" class="edge">
<title>/lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2363.89,-187.25C2360.56,-186.77 2357.25,-186.35 2354,-186 2327.73,-183.18 475.41,-190.23 452,-178 443.17,-173.39 436.21,-165.22 431,-156.98"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="433.99,-155.15 426.03,-148.16 427.9,-158.59 433.99,-155.15"/>
</g>
<!-- /lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge82" class="edge">
<title>/lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2448.47,-222.1C2462.25,-232.76 2483.25,-245.63 2504,-252 2532.73,-260.82 2609.05,-257.51 2639,-260 2707.37,-265.69 2786.76,-274.73 2835.57,-280.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.47,-283.75 2845.8,-281.39 2836.25,-276.8 2835.47,-283.75"/>
</g>
<!-- /lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/sentry_tracer.dart -->
<g id="edge83" class="edge">
<title>/lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/sentry_tracer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2504.09,-187.12C2507.09,-186.69 2510.07,-186.32 2513,-186 2556.55,-181.31 5625.5,-192.01 5667,-178 5680.97,-173.28 5694.3,-164.04 5705,-154.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5707.49,-157.46 5712.63,-148.19 5702.84,-152.23 5707.49,-157.46"/>
</g>
<!-- /lib/src/sentry_measurement.dart -->
<g id="node134" class="node">
<title>/lib/src/sentry_measurement.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5352,-148C5352,-148 5218,-148 5218,-148 5212,-148 5206,-142 5206,-136 5206,-136 5206,-124 5206,-124 5206,-118 5212,-112 5218,-112 5218,-112 5352,-112 5352,-112 5358,-112 5364,-118 5364,-124 5364,-124 5364,-136 5364,-136 5364,-142 5358,-148 5352,-148"/>
<text text-anchor="middle" x="5285" y="-126.5" font-family="Arial" font-size="15.00">sentry_measurement</text>
</g>
<!-- /lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/sentry_measurement.dart -->
<g id="edge85" class="edge">
<title>/lib/src/protocol/sentry_transaction.dart&#45;&gt;/lib/src/sentry_measurement.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2504.09,-187.12C2507.09,-186.69 2510.07,-186.32 2513,-186 2550.13,-181.99 5165.9,-187.57 5202,-178 5220.47,-173.11 5239.14,-163.08 5254.09,-153.54"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5256.02,-156.46 5262.43,-148.02 5252.16,-150.62 5256.02,-156.46"/>
</g>
<!-- /lib/src/protocol/sentry_id.dart -->
<g id="node32" class="node">
<title>/lib/src/protocol/sentry_id.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M529.5,-222C529.5,-222 476.5,-222 476.5,-222 470.5,-222 464.5,-216 464.5,-210 464.5,-210 464.5,-198 464.5,-198 464.5,-192 470.5,-186 476.5,-186 476.5,-186 529.5,-186 529.5,-186 535.5,-186 541.5,-192 541.5,-198 541.5,-198 541.5,-210 541.5,-210 541.5,-216 535.5,-222 529.5,-222"/>
<text text-anchor="middle" x="503" y="-200.5" font-family="Arial" font-size="15.00">sentry_id</text>
</g>
<!-- /lib/src/protocol/sentry_view_hierarchy_element.dart -->
<g id="node33" class="node">
<title>/lib/src/protocol/sentry_view_hierarchy_element.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3083.5,-148C3083.5,-148 2880.5,-148 2880.5,-148 2874.5,-148 2868.5,-142 2868.5,-136 2868.5,-136 2868.5,-124 2868.5,-124 2868.5,-118 2874.5,-112 2880.5,-112 2880.5,-112 3083.5,-112 3083.5,-112 3089.5,-112 3095.5,-118 3095.5,-124 3095.5,-124 3095.5,-136 3095.5,-136 3095.5,-142 3089.5,-148 3083.5,-148"/>
<text text-anchor="middle" x="2982" y="-126.5" font-family="Arial" font-size="15.00">sentry_view_hierarchy_element</text>
</g>
<!-- /lib/src/protocol/sentry_culture.dart -->
<g id="node34" class="node">
<title>/lib/src/protocol/sentry_culture.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2794.5,-222C2794.5,-222 2707.5,-222 2707.5,-222 2701.5,-222 2695.5,-216 2695.5,-210 2695.5,-210 2695.5,-198 2695.5,-198 2695.5,-192 2701.5,-186 2707.5,-186 2707.5,-186 2794.5,-186 2794.5,-186 2800.5,-186 2806.5,-192 2806.5,-198 2806.5,-198 2806.5,-210 2806.5,-210 2806.5,-216 2800.5,-222 2794.5,-222"/>
<text text-anchor="middle" x="2751" y="-200.5" font-family="Arial" font-size="15.00">sentry_culture</text>
</g>
<!-- /lib/src/protocol/sentry_geo.dart -->
<g id="node35" class="node">
<title>/lib/src/protocol/sentry_geo.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2206.5,-222C2206.5,-222 2139.5,-222 2139.5,-222 2133.5,-222 2127.5,-216 2127.5,-210 2127.5,-210 2127.5,-198 2127.5,-198 2127.5,-192 2133.5,-186 2139.5,-186 2139.5,-186 2206.5,-186 2206.5,-186 2212.5,-186 2218.5,-192 2218.5,-198 2218.5,-198 2218.5,-210 2218.5,-210 2218.5,-216 2212.5,-222 2206.5,-222"/>
<text text-anchor="middle" x="2173" y="-200.5" font-family="Arial" font-size="15.00">sentry_geo</text>
</g>
<!-- /lib/src/protocol/debug_meta.dart -->
<g id="node36" class="node">
<title>/lib/src/protocol/debug_meta.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2097,-222C2097,-222 2021,-222 2021,-222 2015,-222 2009,-216 2009,-210 2009,-210 2009,-198 2009,-198 2009,-192 2015,-186 2021,-186 2021,-186 2097,-186 2097,-186 2103,-186 2109,-192 2109,-198 2109,-198 2109,-210 2109,-210 2109,-216 2103,-222 2097,-222"/>
<text text-anchor="middle" x="2059" y="-200.5" font-family="Arial" font-size="15.00">debug_meta</text>
</g>
<!-- /lib/src/protocol/debug_meta.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge86" class="edge">
<title>/lib/src/protocol/debug_meta.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2067.03,-222.34C2076.63,-233.11 2092.23,-246 2109,-252 2136.72,-261.93 2609.61,-258.21 2639,-260 2707.48,-264.18 2786.84,-273.7 2835.6,-279.71"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.48,-283.22 2845.83,-280.98 2836.33,-276.28 2835.48,-283.22"/>
</g>
<!-- /lib/src/protocol/mechanism.dart -->
<g id="node37" class="node">
<title>/lib/src/protocol/mechanism.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4881.5,-222C4881.5,-222 4812.5,-222 4812.5,-222 4806.5,-222 4800.5,-216 4800.5,-210 4800.5,-210 4800.5,-198 4800.5,-198 4800.5,-192 4806.5,-186 4812.5,-186 4812.5,-186 4881.5,-186 4881.5,-186 4887.5,-186 4893.5,-192 4893.5,-198 4893.5,-198 4893.5,-210 4893.5,-210 4893.5,-216 4887.5,-222 4881.5,-222"/>
<text text-anchor="middle" x="4847" y="-200.5" font-family="Arial" font-size="15.00">mechanism</text>
</g>
<!-- /lib/src/protocol/sentry_app.dart -->
<g id="node38" class="node">
<title>/lib/src/protocol/sentry_app.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1841.5,-222C1841.5,-222 1774.5,-222 1774.5,-222 1768.5,-222 1762.5,-216 1762.5,-210 1762.5,-210 1762.5,-198 1762.5,-198 1762.5,-192 1768.5,-186 1774.5,-186 1774.5,-186 1841.5,-186 1841.5,-186 1847.5,-186 1853.5,-192 1853.5,-198 1853.5,-198 1853.5,-210 1853.5,-210 1853.5,-216 1847.5,-222 1841.5,-222"/>
<text text-anchor="middle" x="1808" y="-200.5" font-family="Arial" font-size="15.00">sentry_app</text>
</g>
<!-- /lib/src/protocol/sentry_gpu.dart -->
<g id="node39" class="node">
<title>/lib/src/protocol/sentry_gpu.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1732.5,-222C1732.5,-222 1665.5,-222 1665.5,-222 1659.5,-222 1653.5,-216 1653.5,-210 1653.5,-210 1653.5,-198 1653.5,-198 1653.5,-192 1659.5,-186 1665.5,-186 1665.5,-186 1732.5,-186 1732.5,-186 1738.5,-186 1744.5,-192 1744.5,-198 1744.5,-198 1744.5,-210 1744.5,-210 1744.5,-216 1738.5,-222 1732.5,-222"/>
<text text-anchor="middle" x="1699" y="-200.5" font-family="Arial" font-size="15.00">sentry_gpu</text>
</g>
<!-- /lib/src/protocol/sentry_trace_context.dart -->
<g id="node40" class="node">
<title>/lib/src/protocol/sentry_trace_context.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4054.5,-222C4054.5,-222 3921.5,-222 3921.5,-222 3915.5,-222 3909.5,-216 3909.5,-210 3909.5,-210 3909.5,-198 3909.5,-198 3909.5,-192 3915.5,-186 3921.5,-186 3921.5,-186 4054.5,-186 4054.5,-186 4060.5,-186 4066.5,-192 4066.5,-198 4066.5,-198 4066.5,-210 4066.5,-210 4066.5,-216 4060.5,-222 4054.5,-222"/>
<text text-anchor="middle" x="3988" y="-200.5" font-family="Arial" font-size="15.00">sentry_trace_context</text>
</g>
<!-- /lib/src/protocol/sentry_trace_context.dart&#45;&gt;/lib/src/propagation_context.dart -->
<g id="edge88" class="edge">
<title>/lib/src/protocol/sentry_trace_context.dart&#45;&gt;/lib/src/propagation_context.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3963.91,-222.01C3946.93,-232.93 3923.17,-246.13 3900,-252 3881.43,-256.7 2534.5,-246.42 2521,-260 2507.21,-273.87 2508.22,-289.2 2521,-304 2555.66,-344.14 2699.68,-354.42 2794.72,-356.75"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2794.84,-360.25 2804.91,-356.97 2794.99,-353.26 2794.84,-360.25"/>
</g>
<!-- /lib/src/protocol/sentry_trace_context.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge89" class="edge">
<title>/lib/src/protocol/sentry_trace_context.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3955.87,-222.05C3937.51,-232.84 3913.92,-245.87 3891,-252 3796.86,-277.19 3110.59,-283.46 2926.35,-284.72"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.17,-281.22 2916.2,-284.79 2926.22,-288.22 2926.17,-281.22"/>
</g>
<!-- /lib/src/protocol/sentry_trace_context.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge87" class="edge">
<title>/lib/src/protocol/sentry_trace_context.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4012.08,-222.03C4029.06,-232.96 4052.82,-246.16 4076,-252 4094.91,-256.76 6869.88,-246.55 6884,-260 6899.63,-274.89 6889,-335.42 6889,-357 6889,-659 6889,-659 6889,-659 6889,-679.32 6885.15,-734.26 6898,-750 6929.84,-789.02 6989.87,-799.88 7029.66,-802.59"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.63,-806.09 7039.8,-803.14 7030.01,-799.1 7029.63,-806.09"/>
</g>
<!-- /lib/src/protocol/contexts.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge90" class="edge">
<title>/lib/src/protocol/contexts.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M491.19,-148.13C484.04,-158.81 473.3,-171.68 460,-178 436.51,-189.17 244.08,-167.29 226,-186 205.62,-207.09 205.29,-231.22 226,-252 231.94,-257.96 2639.6,-259.54 2648,-260 2713.52,-263.61 2788.98,-272.6 2835.84,-278.75"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.59,-282.25 2845.96,-280.1 2836.51,-275.31 2835.59,-282.25"/>
</g>
<!-- /lib/src/protocol/debug_image.dart -->
<g id="node42" class="node">
<title>/lib/src/protocol/debug_image.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1474.5,-222C1474.5,-222 1391.5,-222 1391.5,-222 1385.5,-222 1379.5,-216 1379.5,-210 1379.5,-210 1379.5,-198 1379.5,-198 1379.5,-192 1385.5,-186 1391.5,-186 1391.5,-186 1474.5,-186 1474.5,-186 1480.5,-186 1486.5,-192 1486.5,-198 1486.5,-198 1486.5,-210 1486.5,-210 1486.5,-216 1480.5,-222 1474.5,-222"/>
<text text-anchor="middle" x="1433" y="-200.5" font-family="Arial" font-size="15.00">debug_image</text>
</g>
<!-- /lib/src/protocol/sentry_baggage_header.dart -->
<g id="node43" class="node">
<title>/lib/src/protocol/sentry_baggage_header.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M841.5,-222C841.5,-222 686.5,-222 686.5,-222 680.5,-222 674.5,-216 674.5,-210 674.5,-210 674.5,-198 674.5,-198 674.5,-192 680.5,-186 686.5,-186 686.5,-186 841.5,-186 841.5,-186 847.5,-186 853.5,-192 853.5,-198 853.5,-198 853.5,-210 853.5,-210 853.5,-216 847.5,-222 841.5,-222"/>
<text text-anchor="middle" x="764" y="-200.5" font-family="Arial" font-size="15.00">sentry_baggage_header</text>
</g>
<!-- /lib/src/protocol/sentry_baggage_header.dart&#45;&gt;/lib/src/sentry_baggage.dart -->
<g id="edge91" class="edge">
<title>/lib/src/protocol/sentry_baggage_header.dart&#45;&gt;/lib/src/sentry_baggage.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M791.1,-222.02C810.13,-232.94 836.63,-246.14 862,-252 927.72,-267.17 2009.72,-247.53 2076,-260 2121.54,-268.57 2825.09,-522.61 2855,-558 2896.17,-606.7 2888.94,-775.19 2914.37,-850.23"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2911.17,-851.65 2918,-859.75 2917.71,-849.16 2911.17,-851.65"/>
</g>
<!-- /lib/src/protocol/sentry_stack_trace.dart&#45;&gt;/lib/src/protocol/sentry_stack_frame.dart -->
<g id="edge92" class="edge">
<title>/lib/src/protocol/sentry_stack_trace.dart&#45;&gt;/lib/src/protocol/sentry_stack_frame.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M739.87,-111.88C724.83,-102.72 706.13,-91.34 689.94,-81.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="691.6,-78.4 681.24,-76.19 687.96,-84.38 691.6,-78.4"/>
</g>
<!-- /lib/src/protocol/sentry_runtime.dart -->
<g id="node45" class="node">
<title>/lib/src/protocol/sentry_runtime.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1233,-222C1233,-222 1141,-222 1141,-222 1135,-222 1129,-216 1129,-210 1129,-210 1129,-198 1129,-198 1129,-192 1135,-186 1141,-186 1141,-186 1233,-186 1233,-186 1239,-186 1245,-192 1245,-198 1245,-198 1245,-210 1245,-210 1245,-216 1239,-222 1233,-222"/>
<text text-anchor="middle" x="1187" y="-200.5" font-family="Arial" font-size="15.00">sentry_runtime</text>
</g>
<!-- /lib/src/protocol/sentry_device.dart -->
<g id="node46" class="node">
<title>/lib/src/protocol/sentry_device.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4417.5,-222C4417.5,-222 4332.5,-222 4332.5,-222 4326.5,-222 4320.5,-216 4320.5,-210 4320.5,-210 4320.5,-198 4320.5,-198 4320.5,-192 4326.5,-186 4332.5,-186 4332.5,-186 4417.5,-186 4417.5,-186 4423.5,-186 4429.5,-192 4429.5,-198 4429.5,-198 4429.5,-210 4429.5,-210 4429.5,-216 4423.5,-222 4417.5,-222"/>
<text text-anchor="middle" x="4375" y="-200.5" font-family="Arial" font-size="15.00">sentry_device</text>
</g>
<!-- /lib/src/protocol/sentry_device.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge93" class="edge">
<title>/lib/src/protocol/sentry_device.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4393.91,-222.05C4405.82,-232.02 4421.94,-244.14 4438,-252 4558.1,-310.76 4647,-225.63 4728,-332 4747.93,-358.17 4749.99,-381.54 4728,-406 4639.53,-504.39 3681,-511.19 3416.35,-511.18"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.12,-507.68 3406.11,-511.18 3416.11,-514.68 3416.12,-507.68"/>
</g>
<!-- /lib/src/protocol/sentry_message.dart -->
<g id="node47" class="node">
<title>/lib/src/protocol/sentry_message.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4290.5,-222C4290.5,-222 4187.5,-222 4187.5,-222 4181.5,-222 4175.5,-216 4175.5,-210 4175.5,-210 4175.5,-198 4175.5,-198 4175.5,-192 4181.5,-186 4187.5,-186 4187.5,-186 4290.5,-186 4290.5,-186 4296.5,-186 4302.5,-192 4302.5,-198 4302.5,-198 4302.5,-210 4302.5,-210 4302.5,-216 4296.5,-222 4290.5,-222"/>
<text text-anchor="middle" x="4239" y="-200.5" font-family="Arial" font-size="15.00">sentry_message</text>
</g>
<!-- /lib/src/protocol/sdk_info.dart -->
<g id="node48" class="node">
<title>/lib/src/protocol/sdk_info.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4145,-222C4145,-222 4097,-222 4097,-222 4091,-222 4085,-216 4085,-210 4085,-210 4085,-198 4085,-198 4085,-192 4091,-186 4097,-186 4097,-186 4145,-186 4145,-186 4151,-186 4157,-192 4157,-198 4157,-198 4157,-210 4157,-210 4157,-216 4151,-222 4145,-222"/>
<text text-anchor="middle" x="4121" y="-200.5" font-family="Arial" font-size="15.00">sdk_info</text>
</g>
<!-- /lib/src/protocol/max_body_size.dart -->
<g id="node49" class="node">
<title>/lib/src/protocol/max_body_size.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3879,-222C3879,-222 3783,-222 3783,-222 3777,-222 3771,-216 3771,-210 3771,-210 3771,-198 3771,-198 3771,-192 3777,-186 3783,-186 3783,-186 3879,-186 3879,-186 3885,-186 3891,-192 3891,-198 3891,-198 3891,-210 3891,-210 3891,-216 3885,-222 3879,-222"/>
<text text-anchor="middle" x="3831" y="-200.5" font-family="Arial" font-size="15.00">max_body_size</text>
</g>
<!-- /lib/src/protocol/sentry_event.dart -->
<g id="node51" class="node">
<title>/lib/src/protocol/sentry_event.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4538.5,-222C4538.5,-222 4459.5,-222 4459.5,-222 4453.5,-222 4447.5,-216 4447.5,-210 4447.5,-210 4447.5,-198 4447.5,-198 4447.5,-192 4453.5,-186 4459.5,-186 4459.5,-186 4538.5,-186 4538.5,-186 4544.5,-186 4550.5,-192 4550.5,-198 4550.5,-198 4550.5,-210 4550.5,-210 4550.5,-216 4544.5,-222 4538.5,-222"/>
<text text-anchor="middle" x="4499" y="-200.5" font-family="Arial" font-size="15.00">sentry_event</text>
</g>
<!-- /lib/src/protocol/sentry_event.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge96" class="edge">
<title>/lib/src/protocol/sentry_event.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4447.29,-187.57C4444.16,-186.95 4441.05,-186.42 4438,-186 4410.58,-182.22 476.55,-190.79 452,-178 443.17,-173.4 436.21,-165.23 431,-156.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="433.99,-155.16 426.03,-148.17 427.89,-158.6 433.99,-155.16"/>
</g>
<!-- /lib/src/throwable_mechanism.dart -->
<g id="node63" class="node">
<title>/lib/src/throwable_mechanism.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4982,-304C4982,-304 4840,-304 4840,-304 4834,-304 4828,-298 4828,-292 4828,-292 4828,-280 4828,-280 4828,-274 4834,-268 4840,-268 4840,-268 4982,-268 4982,-268 4988,-268 4994,-274 4994,-280 4994,-280 4994,-292 4994,-292 4994,-298 4988,-304 4982,-304"/>
<text text-anchor="middle" x="4911" y="-282.5" font-family="Arial" font-size="15.00">throwable_mechanism</text>
</g>
<!-- /lib/src/protocol/sentry_event.dart&#45;&gt;/lib/src/throwable_mechanism.dart -->
<g id="edge95" class="edge">
<title>/lib/src/protocol/sentry_event.dart&#45;&gt;/lib/src/throwable_mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4515.05,-222.23C4526.22,-232.95 4542.1,-245.83 4559,-252 4582.5,-260.58 4759.15,-257.13 4784,-260 4797.29,-261.53 4811.27,-263.71 4824.9,-266.14"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4824.28,-269.58 4834.75,-267.95 4825.55,-262.7 4824.28,-269.58"/>
</g>
<!-- /lib/src/protocol/sentry_event.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge94" class="edge">
<title>/lib/src/protocol/sentry_event.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4475.29,-222.08C4462.53,-232.88 4446.27,-245.91 4429,-252 4356.21,-277.65 3174.5,-283.8 2926.42,-284.83"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.3,-281.33 2916.32,-284.87 2926.33,-288.33 2926.3,-281.33"/>
</g>
<!-- /lib/src/protocol/sentry_operating_system.dart -->
<g id="node52" class="node">
<title>/lib/src/protocol/sentry_operating_system.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3392.5,-222C3392.5,-222 3231.5,-222 3231.5,-222 3225.5,-222 3219.5,-216 3219.5,-210 3219.5,-210 3219.5,-198 3219.5,-198 3219.5,-192 3225.5,-186 3231.5,-186 3231.5,-186 3392.5,-186 3392.5,-186 3398.5,-186 3404.5,-192 3404.5,-198 3404.5,-198 3404.5,-210 3404.5,-210 3404.5,-216 3398.5,-222 3392.5,-222"/>
<text text-anchor="middle" x="3312" y="-200.5" font-family="Arial" font-size="15.00">sentry_operating_system</text>
</g>
<!-- /lib/src/protocol/sentry_user.dart -->
<g id="node53" class="node">
<title>/lib/src/protocol/sentry_user.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5177.5,-222C5177.5,-222 5106.5,-222 5106.5,-222 5100.5,-222 5094.5,-216 5094.5,-210 5094.5,-210 5094.5,-198 5094.5,-198 5094.5,-192 5100.5,-186 5106.5,-186 5106.5,-186 5177.5,-186 5177.5,-186 5183.5,-186 5189.5,-192 5189.5,-198 5189.5,-198 5189.5,-210 5189.5,-210 5189.5,-216 5183.5,-222 5177.5,-222"/>
<text text-anchor="middle" x="5142" y="-200.5" font-family="Arial" font-size="15.00">sentry_user</text>
</g>
<!-- /lib/src/protocol/sentry_user.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge97" class="edge">
<title>/lib/src/protocol/sentry_user.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5155.07,-222.35C5164.32,-233.12 5177.76,-246.01 5193,-252 5214.99,-260.65 6876.1,-244.58 6894,-260 6927.05,-288.47 6909,-313.38 6909,-357 6909,-659 6909,-659 6909,-659 6909,-699.51 6889.55,-717.71 6914,-750 6940.95,-785.6 6993.15,-797.47 7029.48,-801.32"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.47,-804.83 7039.74,-802.25 7030.09,-797.86 7029.47,-804.83"/>
</g>
<!-- /lib/src/protocol/sentry_exception.dart -->
<g id="node54" class="node">
<title>/lib/src/protocol/sentry_exception.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1623,-222C1623,-222 1517,-222 1517,-222 1511,-222 1505,-216 1505,-210 1505,-210 1505,-198 1505,-198 1505,-192 1511,-186 1517,-186 1517,-186 1623,-186 1623,-186 1629,-186 1635,-192 1635,-198 1635,-198 1635,-210 1635,-210 1635,-216 1629,-222 1623,-222"/>
<text text-anchor="middle" x="1570" y="-200.5" font-family="Arial" font-size="15.00">sentry_exception</text>
</g>
<!-- /lib/src/protocol/sentry_exception.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge98" class="edge">
<title>/lib/src/protocol/sentry_exception.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1582.52,-222.2C1595.21,-233.05 1615,-246.1 1635,-252 1661.75,-259.89 2611.16,-258.4 2639,-260 2707.49,-263.94 2786.85,-273.53 2835.61,-279.63"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.48,-283.14 2845.83,-280.91 2836.35,-276.19 2835.48,-283.14"/>
</g>
<!-- /lib/src/protocol/span_status.dart -->
<g id="node55" class="node">
<title>/lib/src/protocol/span_status.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1349,-222C1349,-222 1275,-222 1275,-222 1269,-222 1263,-216 1263,-210 1263,-210 1263,-198 1263,-198 1263,-192 1269,-186 1275,-186 1275,-186 1349,-186 1349,-186 1355,-186 1361,-192 1361,-198 1361,-198 1361,-210 1361,-210 1361,-216 1355,-222 1349,-222"/>
<text text-anchor="middle" x="1312" y="-200.5" font-family="Arial" font-size="15.00">span_status</text>
</g>
<!-- /lib/src/protocol/sentry_transaction_name_source.dart -->
<g id="node56" class="node">
<title>/lib/src/protocol/sentry_transaction_name_source.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1098.5,-222C1098.5,-222 883.5,-222 883.5,-222 877.5,-222 871.5,-216 871.5,-210 871.5,-210 871.5,-198 871.5,-198 871.5,-192 877.5,-186 883.5,-186 883.5,-186 1098.5,-186 1098.5,-186 1104.5,-186 1110.5,-192 1110.5,-198 1110.5,-198 1110.5,-210 1110.5,-210 1110.5,-216 1104.5,-222 1098.5,-222"/>
<text text-anchor="middle" x="991" y="-200.5" font-family="Arial" font-size="15.00">sentry_transaction_name_source</text>
</g>
<!-- /lib/src/protocol/sentry_view_hierarchy.dart -->
<g id="node57" class="node">
<title>/lib/src/protocol/sentry_view_hierarchy.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5064.5,-222C5064.5,-222 4923.5,-222 4923.5,-222 4917.5,-222 4911.5,-216 4911.5,-210 4911.5,-210 4911.5,-198 4911.5,-198 4911.5,-192 4917.5,-186 4923.5,-186 4923.5,-186 5064.5,-186 5064.5,-186 5070.5,-186 5076.5,-192 5076.5,-198 5076.5,-198 5076.5,-210 5076.5,-210 5076.5,-216 5070.5,-222 5064.5,-222"/>
<text text-anchor="middle" x="4994" y="-200.5" font-family="Arial" font-size="15.00">sentry_view_hierarchy</text>
</g>
<!-- /lib/src/protocol/sentry_view_hierarchy.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy_element.dart -->
<g id="edge99" class="edge">
<title>/lib/src/protocol/sentry_view_hierarchy.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy_element.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4911.35,-187.01C4908.2,-186.63 4905.07,-186.29 4902,-186 4852.14,-181.32 3148.15,-187.62 3099,-178 3073.18,-172.95 3045.81,-162.28 3024.04,-152.38"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3025.3,-149.11 3014.76,-148.05 3022.35,-155.45 3025.3,-149.11"/>
</g>
<!-- /lib/src/protocol/sentry_browser.dart -->
<g id="node58" class="node">
<title>/lib/src/protocol/sentry_browser.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1978.5,-222C1978.5,-222 1883.5,-222 1883.5,-222 1877.5,-222 1871.5,-216 1871.5,-210 1871.5,-210 1871.5,-198 1871.5,-198 1871.5,-192 1877.5,-186 1883.5,-186 1883.5,-186 1978.5,-186 1978.5,-186 1984.5,-186 1990.5,-192 1990.5,-198 1990.5,-198 1990.5,-210 1990.5,-210 1990.5,-216 1984.5,-222 1978.5,-222"/>
<text text-anchor="middle" x="1931" y="-200.5" font-family="Arial" font-size="15.00">sentry_browser</text>
</g>
<!-- /lib/src/protocol/dsn.dart -->
<g id="node59" class="node">
<title>/lib/src/protocol/dsn.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3465,-222C3465,-222 3435,-222 3435,-222 3429,-222 3423,-216 3423,-210 3423,-210 3423,-198 3423,-198 3423,-192 3429,-186 3435,-186 3435,-186 3465,-186 3465,-186 3471,-186 3477,-192 3477,-198 3477,-198 3477,-210 3477,-210 3477,-216 3471,-222 3465,-222"/>
<text text-anchor="middle" x="3450" y="-200.5" font-family="Arial" font-size="15.00">dsn</text>
</g>
<!-- /lib/src/sentry_baggage.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge102" class="edge">
<title>/lib/src/sentry_baggage.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2935.14,-859.92C2949.5,-804.72 3002.89,-630.54 3120,-558 3168.72,-527.82 3233.51,-517.26 3281.74,-513.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3282,-517.33 3291.76,-513.22 3281.57,-510.34 3282,-517.33"/>
</g>
<!-- /lib/src/sentry_baggage.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge101" class="edge">
<title>/lib/src/sentry_baggage.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2928.86,-859.59C2920.72,-793.45 2892.02,-561.25 2890,-558 2878.17,-538.94 2862.28,-547.3 2848,-530 2809.96,-483.91 2807.79,-464.59 2796,-406 2789.51,-373.76 2779.04,-360.18 2796,-332 2804.99,-317.07 2820.77,-306.84 2836.31,-299.95"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2837.94,-303.07 2845.9,-296.08 2835.32,-296.58 2837.94,-303.07"/>
</g>
<!-- /lib/src/sentry_baggage.dart&#45;&gt;/lib/src/scope.dart -->
<g id="edge100" class="edge">
<title>/lib/src/sentry_baggage.dart&#45;&gt;/lib/src/scope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2993.14,-863.16C3001.14,-860.12 3008.98,-856.44 3016,-852 3024.83,-846.42 3032.92,-838.46 3039.58,-830.68"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3042.69,-832.41 3046.23,-822.42 3037.23,-828.02 3042.69,-832.41"/>
</g>
<!-- /lib/src/exception_stacktrace_extractor.dart -->
<g id="node61" class="node">
<title>/lib/src/exception_stacktrace_extractor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6120.5,-602C6120.5,-602 5919.5,-602 5919.5,-602 5913.5,-602 5907.5,-596 5907.5,-590 5907.5,-590 5907.5,-578 5907.5,-578 5907.5,-572 5913.5,-566 5919.5,-566 5919.5,-566 6120.5,-566 6120.5,-566 6126.5,-566 6132.5,-572 6132.5,-578 6132.5,-578 6132.5,-590 6132.5,-590 6132.5,-596 6126.5,-602 6120.5,-602"/>
<text text-anchor="middle" x="6020" y="-580.5" font-family="Arial" font-size="15.00">exception_stacktrace_extractor</text>
</g>
<!-- /lib/src/exception_stacktrace_extractor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge104" class="edge">
<title>/lib/src/exception_stacktrace_extractor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5948.1,-565.97C5931.72,-562.74 5914.33,-559.8 5898,-558 5645.41,-530.13 3794.54,-516.05 3416.37,-513.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.16,-509.94 3406.14,-513.38 3416.11,-516.94 3416.16,-509.94"/>
</g>
<!-- /lib/src/exception_stacktrace_extractor.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge103" class="edge">
<title>/lib/src/exception_stacktrace_extractor.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5936.99,-565.94C5923.95,-563.27 5910.61,-560.55 5898,-558 5835.81,-545.41 5814.11,-559.63 5758,-530 5733.26,-516.94 5738.25,-498.04 5713,-486 5615.67,-439.59 5578.35,-475.83 5472,-458 5382.24,-442.95 5361.78,-428.96 5272,-414 5241.11,-408.85 5230.01,-420 5202,-406 5161.5,-385.76 5173.4,-350.32 5132,-332 5027.57,-285.79 3237.46,-286.45 2926.78,-286.91"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.35,-283.41 2916.35,-286.93 2926.36,-290.41 2926.35,-283.41"/>
</g>
<!-- /lib/src/isolate_error_integration.dart -->
<g id="node62" class="node">
<title>/lib/src/isolate_error_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3384,-1288C3384,-1288 3230,-1288 3230,-1288 3224,-1288 3218,-1282 3218,-1276 3218,-1276 3218,-1264 3218,-1264 3218,-1258 3224,-1252 3230,-1252 3230,-1252 3384,-1252 3384,-1252 3390,-1252 3396,-1258 3396,-1264 3396,-1264 3396,-1276 3396,-1276 3396,-1282 3390,-1288 3384,-1288"/>
<text text-anchor="middle" x="3307" y="-1266.5" font-family="Arial" font-size="15.00">isolate_error_integration</text>
</g>
<!-- /lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/sentry_isolate_extension.dart -->
<g id="edge107" class="edge">
<title>/lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/sentry_isolate_extension.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3396.25,-1265.26C3682.38,-1253.28 4575.64,-1215.86 4877.31,-1203.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4877.52,-1206.71 4887.36,-1202.8 4877.23,-1199.72 4877.52,-1206.71"/>
</g>
<!-- /lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge108" class="edge">
<title>/lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3217.66,-1267.17C2798.34,-1257.87 1037.4,-1210.77 839,-1072 760.49,-1017.09 793.66,-955.59 800,-860 802.67,-819.73 792.53,-805.34 812,-770 858.75,-685.14 894.76,-669.65 987,-640 1006.89,-633.61 1345.66,-643.66 1363,-632 1392.74,-612.01 1365.28,-578.02 1395,-558 1434.95,-531.08 2942.36,-516.51 3281.65,-513.56"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3281.85,-517.06 3291.82,-513.47 3281.79,-510.06 3281.85,-517.06"/>
</g>
<!-- /lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/integration.dart -->
<g id="edge106" class="edge">
<title>/lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3311.57,-1251.82C3328.94,-1189.76 3396.71,-974.79 3530,-860 3616.32,-785.66 3658.09,-791.28 3770,-770 3801.93,-763.93 4917.21,-773.18 4940,-750 4956.39,-733.32 4950.34,-705.81 4942.21,-685.39"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4945.33,-683.8 4938.14,-676.02 4938.91,-686.58 4945.33,-683.8"/>
</g>
<!-- /lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge105" class="edge">
<title>/lib/src/isolate_error_integration.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3305.02,-1251.98C3302.11,-1225.36 3297,-1172.23 3297,-1127 3297,-1127 3297,-1127 3297,-1053 3297,-926.81 3257.53,-895.11 3274,-770 3282.73,-703.65 3289.54,-668.7 3350,-640 3369.71,-630.64 4899.34,-634.62 4921,-632 4977.41,-625.19 5041.11,-608.11 5080.16,-596.53"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5081.2,-599.87 5089.77,-593.64 5079.19,-593.16 5081.2,-599.87"/>
</g>
<!-- /lib/src/throwable_mechanism.dart&#45;&gt;/lib/src/protocol/mechanism.dart -->
<g id="edge109" class="edge">
<title>/lib/src/throwable_mechanism.dart&#45;&gt;/lib/src/protocol/mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4897.11,-267.64C4888.33,-256.66 4876.84,-242.29 4867.07,-230.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4869.62,-227.67 4860.64,-222.05 4864.15,-232.05 4869.62,-227.67"/>
</g>
<!-- /lib/src/type_check_hint.dart -->
<g id="node64" class="node">
<title>/lib/src/type_check_hint.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6188.5,-1362C6188.5,-1362 6087.5,-1362 6087.5,-1362 6081.5,-1362 6075.5,-1356 6075.5,-1350 6075.5,-1350 6075.5,-1338 6075.5,-1338 6075.5,-1332 6081.5,-1326 6087.5,-1326 6087.5,-1326 6188.5,-1326 6188.5,-1326 6194.5,-1326 6200.5,-1332 6200.5,-1338 6200.5,-1338 6200.5,-1350 6200.5,-1350 6200.5,-1356 6194.5,-1362 6188.5,-1362"/>
<text text-anchor="middle" x="6138" y="-1340.5" font-family="Arial" font-size="15.00">type_check_hint</text>
</g>
<!-- /lib/src/http_client/failed_request_client.dart -->
<g id="node99" class="node">
<title>/lib/src/http_client/failed_request_client.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5380.5,-1288C5380.5,-1288 5251.5,-1288 5251.5,-1288 5245.5,-1288 5239.5,-1282 5239.5,-1276 5239.5,-1276 5239.5,-1264 5239.5,-1264 5239.5,-1258 5245.5,-1252 5251.5,-1252 5251.5,-1252 5380.5,-1252 5380.5,-1252 5386.5,-1252 5392.5,-1258 5392.5,-1264 5392.5,-1264 5392.5,-1276 5392.5,-1276 5392.5,-1282 5386.5,-1288 5380.5,-1288"/>
<text text-anchor="middle" x="5316" y="-1266.5" font-family="Arial" font-size="15.00">failed_request_client</text>
</g>
<!-- /lib/src/type_check_hint.dart&#45;&gt;/lib/src/http_client/failed_request_client.dart -->
<g id="edge110" class="edge">
<title>/lib/src/type_check_hint.dart&#45;&gt;/lib/src/http_client/failed_request_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M6075.45,-1342.63C5909.83,-1341.41 5468.71,-1336.49 5405,-1318 5387.28,-1312.86 5369.41,-1302.91 5354.25,-1293.49"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5355.99,-1290.44 5345.67,-1288.04 5352.24,-1296.35 5355.99,-1290.44"/>
</g>
<!-- /lib/src/sentry_client_stub.dart -->
<g id="node65" class="node">
<title>/lib/src/sentry_client_stub.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3265.5,-1072C3265.5,-1072 3152.5,-1072 3152.5,-1072 3146.5,-1072 3140.5,-1066 3140.5,-1060 3140.5,-1060 3140.5,-1048 3140.5,-1048 3140.5,-1042 3146.5,-1036 3152.5,-1036 3152.5,-1036 3265.5,-1036 3265.5,-1036 3271.5,-1036 3277.5,-1042 3277.5,-1048 3277.5,-1048 3277.5,-1060 3277.5,-1060 3277.5,-1066 3271.5,-1072 3265.5,-1072"/>
<text text-anchor="middle" x="3209" y="-1050.5" font-family="Arial" font-size="15.00">sentry_client_stub</text>
</g>
<!-- /lib/src/sentry_client_stub.dart&#45;&gt;/lib/src/sentry_client.dart -->
<g id="edge111" class="edge">
<title>/lib/src/sentry_client_stub.dart&#45;&gt;/lib/src/sentry_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3140.13,-1037.81C3137.05,-1037.18 3133.99,-1036.58 3131,-1036 3043.33,-1019.06 2941.3,-1002.4 2877.93,-992.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2878.13,-988.9 2867.71,-990.8 2877.05,-995.81 2878.13,-988.9"/>
</g>
<!-- /lib/src/sentry_client_stub.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge112" class="edge">
<title>/lib/src/sentry_client_stub.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3277.87,-1051.25C3569.89,-1043.48 4693.14,-1010.18 4744,-956 4800.58,-895.73 4800.61,-830.24 4744,-770 4719.45,-743.88 4457.33,-756.04 4422,-750 4113.71,-697.34 4051.84,-618.52 3745,-558 3630.82,-535.48 3495.73,-523.13 3416.5,-517.3"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.4,-513.79 3406.17,-516.56 3415.89,-520.77 3416.4,-513.79"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/version.dart -->
<g id="edge122" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/version.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3291.98,-498.57C3228.55,-484.73 3127.52,-462.7 3070.61,-450.29"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3071.03,-446.8 3060.51,-448.09 3069.53,-453.64 3071.03,-446.8"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/environment/environment_variables.dart -->
<g id="edge119" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/environment/environment_variables.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3291.9,-509.55C2945.77,-500.73 1128.98,-454.47 693.76,-443.39"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="693.82,-439.89 683.73,-443.13 693.64,-446.89 693.82,-439.89"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/diagnostic_logger.dart -->
<g id="edge118" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/diagnostic_logger.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3391.29,-493.88C3417.92,-484.05 3452.7,-471.66 3482.56,-461.34"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3483.76,-464.63 3492.07,-458.07 3481.48,-458.01 3483.76,-464.63"/>
</g>
<!-- /lib/src/noop_client.dart -->
<g id="node82" class="node">
<title>/lib/src/noop_client.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2945,-458C2945,-458 2877,-458 2877,-458 2871,-458 2865,-452 2865,-446 2865,-446 2865,-434 2865,-434 2865,-428 2871,-422 2877,-422 2877,-422 2945,-422 2945,-422 2951,-422 2957,-428 2957,-434 2957,-434 2957,-446 2957,-446 2957,-452 2951,-458 2945,-458"/>
<text text-anchor="middle" x="2911" y="-436.5" font-family="Arial" font-size="15.00">noop_client</text>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/noop_client.dart -->
<g id="edge120" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/noop_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3291.87,-504.06C3220.17,-495.02 3093.54,-478.01 2986,-458 2979.88,-456.86 2973.51,-455.58 2967.17,-454.23"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2967.81,-450.79 2957.29,-452.09 2966.32,-457.63 2967.81,-450.79"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/transport/noop_transport.dart -->
<g id="edge121" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/transport/noop_transport.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3291.63,-515C3051.91,-523.4 2142.56,-555.41 2140,-558 2109.99,-588.35 2165.19,-719.83 2135,-750 2104.34,-780.65 1396.96,-759.1 1355,-770 1345.51,-772.46 1335.84,-776.58 1327.03,-781.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1325.32,-778.02 1318.19,-785.85 1328.65,-784.18 1325.32,-778.02"/>
</g>
<!-- /lib/src/client_reports/noop_client_report_recorder.dart -->
<g id="node129" class="node">
<title>/lib/src/client_reports/noop_client_report_recorder.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1599.5,-676C1599.5,-676 1418.5,-676 1418.5,-676 1412.5,-676 1406.5,-670 1406.5,-664 1406.5,-664 1406.5,-652 1406.5,-652 1406.5,-646 1412.5,-640 1418.5,-640 1418.5,-640 1599.5,-640 1599.5,-640 1605.5,-640 1611.5,-646 1611.5,-652 1611.5,-652 1611.5,-664 1611.5,-664 1611.5,-670 1605.5,-676 1599.5,-676"/>
<text text-anchor="middle" x="1509" y="-654.5" font-family="Arial" font-size="15.00">noop_client_report_recorder</text>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/client_reports/noop_client_report_recorder.dart -->
<g id="edge115" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/client_reports/noop_client_report_recorder.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3291.83,-513.48C3009.63,-515.97 1775.59,-528.53 1700,-558 1653.33,-576.2 1659.57,-609.14 1615,-632 1612.1,-633.49 1609.11,-634.89 1606.05,-636.21"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1604.71,-632.98 1596.71,-639.93 1607.29,-639.48 1604.71,-632.98"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart -->
<g id="edge114" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3291.99,-513.6C3008.28,-516.65 1756.1,-531.26 1584,-558 1575.52,-559.32 1566.69,-561.23 1557.98,-563.42"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1556.99,-560.06 1548.21,-565.99 1558.77,-566.83 1556.99,-560.06"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/sentry_stack_trace_factory.dart -->
<g id="edge117" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/sentry_stack_trace_factory.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3291.8,-496.67C3271.74,-488.49 3251.25,-476.13 3239,-458 3224.77,-436.94 3223.6,-407.38 3226.88,-386.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3230.35,-386.52 3228.88,-376.03 3223.49,-385.15 3230.35,-386.52"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/src/sentry_exception_factory.dart -->
<g id="edge116" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/src/sentry_exception_factory.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3343.08,-493.7C3342.29,-485.98 3342.06,-476.71 3342.4,-468.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3345.9,-468.32 3343.1,-458.1 3338.92,-467.84 3345.9,-468.32"/>
</g>
<!-- /lib/src/sentry_options.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge113" class="edge">
<title>/lib/src/sentry_options.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3406.18,-513.49C3808.66,-516.96 6204.26,-538.39 6356,-558 6506.74,-577.48 6576.62,-536.58 6688,-640 6725.7,-675 6684.77,-716.72 6724,-750 6769.5,-788.59 6949.12,-799.17 7029.61,-802"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.73,-805.51 7039.84,-802.34 7029.96,-798.51 7029.73,-805.51"/>
</g>
<!-- /lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge126" class="edge">
<title>/lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3541.28,-860.39C3539.85,-860.25 3538.42,-860.12 3537,-860 3520.56,-858.58 3251.51,-863.83 3240,-852 3185.93,-796.46 3244.65,-647.9 3300,-558 3304.7,-550.37 3311.06,-543.22 3317.63,-536.98"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3320.23,-539.35 3325.36,-530.08 3315.57,-534.12 3320.23,-539.35"/>
</g>
<!-- /lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge124" class="edge">
<title>/lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3541.27,-860.5C3539.84,-860.33 3538.42,-860.16 3537,-860 3526.26,-858.77 3447.51,-859.78 3440,-852 3434.35,-846.14 3432.09,-738.12 3431.33,-686.3"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3434.83,-686.1 3431.19,-676.15 3427.83,-686.2 3434.83,-686.1"/>
</g>
<!-- /lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge125" class="edge">
<title>/lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3541.28,-860.38C3539.85,-860.25 3538.42,-860.12 3537,-860 3519.17,-858.48 3230.51,-860.93 3215,-852 3115.59,-794.74 3113.95,-741.58 3080,-632 3051.29,-539.33 3109.93,-501.96 3069,-414 3041.69,-355.32 2971.99,-319.65 2925.56,-301.62"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.63,-298.28 2916.03,-298.04 2924.16,-304.84 2926.63,-298.28"/>
</g>
<!-- /lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/event_processor.dart -->
<g id="edge123" class="edge">
<title>/lib/src/event_processor/deduplication_event_processor.dart&#45;&gt;/lib/src/event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3666.88,-859.92C3687.38,-834.69 3725.86,-787.33 3749.58,-758.14"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3752.55,-760.03 3756.14,-750.06 3747.11,-755.62 3752.55,-760.03"/>
</g>
<!-- /lib/src/event_processor/exception/exception_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge128" class="edge">
<title>/lib/src/event_processor/exception/exception_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4331.32,-785.99C4213.31,-742.57 3888.01,-626.31 3609,-558 3544.1,-542.11 3468.76,-529.72 3415.94,-522"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.41,-518.53 3406.02,-520.57 3415.41,-525.46 3416.41,-518.53"/>
</g>
<!-- /lib/src/event_processor/exception/exception_event_processor.dart&#45;&gt;/lib/src/event_processor.dart -->
<g id="edge127" class="edge">
<title>/lib/src/event_processor/exception/exception_event_processor.dart&#45;&gt;/lib/src/event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4319.9,-785.93C4297.39,-779.88 4271.17,-773.67 4247,-770 4080.23,-744.68 4035.62,-768.89 3868,-750 3860.28,-749.13 3852.24,-748.04 3844.24,-746.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3844.58,-743.34 3834.16,-745.25 3843.49,-750.26 3844.58,-743.34"/>
</g>
<!-- /lib/src/event_processor/exception/web_exception_event_processor.dart -->
<g id="node70" class="node">
<title>/lib/src/event_processor/exception/web_exception_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4482.5,-896C4482.5,-896 4271.5,-896 4271.5,-896 4265.5,-896 4259.5,-890 4259.5,-884 4259.5,-884 4259.5,-872 4259.5,-872 4259.5,-866 4265.5,-860 4271.5,-860 4271.5,-860 4482.5,-860 4482.5,-860 4488.5,-860 4494.5,-866 4494.5,-872 4494.5,-872 4494.5,-884 4494.5,-884 4494.5,-890 4488.5,-896 4482.5,-896"/>
<text text-anchor="middle" x="4377" y="-874.5" font-family="Arial" font-size="15.00">web_exception_event_processor</text>
</g>
<!-- /lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge131" class="edge">
<title>/lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4259.44,-860.98C4255.24,-860.61 4251.09,-860.29 4247,-860 4223.53,-858.36 3420.19,-864.09 3400,-852 3313.6,-800.27 3308.72,-740.07 3320,-640 3323.96,-604.84 3333.91,-565.22 3341.05,-539.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3344.44,-540.62 3343.82,-530.04 3337.71,-538.7 3344.44,-540.62"/>
</g>
<!-- /lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/event_processor/exception/exception_event_processor.dart -->
<g id="edge132" class="edge">
<title>/lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/event_processor/exception/exception_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4377,-859.94C4377,-851.81 4377,-841.88 4377,-832.7"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4380.5,-832.44 4377,-822.44 4373.5,-832.44 4380.5,-832.44"/>
</g>
<!-- /lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge129" class="edge">
<title>/lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4259.44,-860.99C4255.24,-860.62 4251.09,-860.29 4247,-860 4236.37,-859.25 3488.37,-858.58 3480,-852 3466.87,-841.68 3445.64,-736.95 3436.02,-686.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3439.44,-685.47 3434.15,-676.29 3432.56,-686.76 3439.44,-685.47"/>
</g>
<!-- /lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge130" class="edge">
<title>/lib/src/event_processor/exception/web_exception_event_processor.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4259.44,-860.98C4255.24,-860.61 4251.09,-860.28 4247,-860 4234.18,-859.11 3332.7,-859.11 3322,-852 3232.13,-792.28 3168.63,-506.75 3130,-406 3117.57,-373.59 3131.06,-354.93 3105,-332 3079,-309.12 2984.24,-296.52 2926.62,-290.81"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.61,-287.3 2916.32,-289.83 2925.95,-294.27 2926.61,-287.3"/>
</g>
<!-- /lib/src/event_processor/exception/io_exception_event_processor.dart -->
<g id="node71" class="node">
<title>/lib/src/event_processor/exception/io_exception_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4719.5,-896C4719.5,-896 4524.5,-896 4524.5,-896 4518.5,-896 4512.5,-890 4512.5,-884 4512.5,-884 4512.5,-872 4512.5,-872 4512.5,-866 4518.5,-860 4524.5,-860 4524.5,-860 4719.5,-860 4719.5,-860 4725.5,-860 4731.5,-866 4731.5,-872 4731.5,-872 4731.5,-884 4731.5,-884 4731.5,-890 4725.5,-896 4719.5,-896"/>
<text text-anchor="middle" x="4622" y="-874.5" font-family="Arial" font-size="15.00">io_exception_event_processor</text>
</g>
<!-- /lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge135" class="edge">
<title>/lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4512.48,-860.8C4509.29,-860.51 4506.12,-860.24 4503,-860 4421.79,-853.78 3849.89,-867.84 3770,-852 3650.27,-828.26 3602.64,-834.97 3515,-750 3476.7,-712.87 3496.24,-684.62 3467,-640 3440.99,-600.3 3402.25,-561.33 3376.33,-537.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3378.46,-534.45 3368.73,-530.26 3373.73,-539.6 3378.46,-534.45"/>
</g>
<!-- /lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/event_processor/exception/exception_event_processor.dart -->
<g id="edge136" class="edge">
<title>/lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/event_processor/exception/exception_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4512.43,-860.35C4501.72,-857.9 4491.09,-855.12 4481,-852 4459.79,-845.44 4437.22,-835.62 4418.54,-826.64"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4419.96,-823.44 4409.44,-822.19 4416.88,-829.73 4419.96,-823.44"/>
</g>
<!-- /lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge133" class="edge">
<title>/lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4512.48,-860.79C4509.29,-860.5 4506.12,-860.23 4503,-860 4489.66,-859 3551.52,-858.79 3540,-852 3508.13,-833.22 3462.65,-733.8 3441.98,-685.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3445.2,-684.07 3438.07,-676.22 3438.75,-686.8 3445.2,-684.07"/>
</g>
<!-- /lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge134" class="edge">
<title>/lib/src/event_processor/exception/io_exception_event_processor.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4512.48,-860.79C4509.29,-860.5 4506.12,-860.23 4503,-860 4488.83,-858.94 3492.29,-859.13 3480,-852 3392.19,-801.06 3336.55,-722.99 3395,-640 3491.49,-503 3750,-680.57 3750,-513 3750,-513 3750,-513 3750,-439 3750,-290.72 3579.21,-362.02 3434,-332 3248.29,-293.61 3021.17,-287.65 2926.16,-286.95"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.06,-283.45 2916.04,-286.89 2926.02,-290.45 2926.06,-283.45"/>
</g>
<!-- /lib/src/event_processor/enricher/io_enricher_event_processor.dart -->
<g id="node72" class="node">
<title>/lib/src/event_processor/enricher/io_enricher_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3979.5,-896C3979.5,-896 3794.5,-896 3794.5,-896 3788.5,-896 3782.5,-890 3782.5,-884 3782.5,-884 3782.5,-872 3782.5,-872 3782.5,-866 3788.5,-860 3794.5,-860 3794.5,-860 3979.5,-860 3979.5,-860 3985.5,-860 3991.5,-866 3991.5,-872 3991.5,-872 3991.5,-884 3991.5,-884 3991.5,-890 3985.5,-896 3979.5,-896"/>
<text text-anchor="middle" x="3887" y="-874.5" font-family="Arial" font-size="15.00">io_enricher_event_processor</text>
</g>
<!-- /lib/src/event_processor/enricher/io_enricher_event_processor.dart&#45;&gt;/lib/src/event_processor/enricher/enricher_event_processor.dart -->
<g id="edge138" class="edge">
<title>/lib/src/event_processor/enricher/io_enricher_event_processor.dart&#45;&gt;/lib/src/event_processor/enricher/enricher_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3887,-859.94C3887,-851.81 3887,-841.88 3887,-832.7"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3890.5,-832.44 3887,-822.44 3883.5,-832.44 3890.5,-832.44"/>
</g>
<!-- /lib/src/event_processor/enricher/io_enricher_event_processor.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge137" class="edge">
<title>/lib/src/event_processor/enricher/io_enricher_event_processor.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3991.6,-860.74C3994.43,-860.47 3997.23,-860.22 4000,-860 4164.81,-846.78 4578.68,-854.56 4744,-852 5670.91,-837.65 6805.5,-811.23 7029.54,-805.94"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.89,-809.43 7039.8,-805.69 7029.72,-802.43 7029.89,-809.43"/>
</g>
<!-- /lib/src/event_processor/enricher/enricher_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge140" class="edge">
<title>/lib/src/event_processor/enricher/enricher_event_processor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3880.13,-785.85C3870.76,-763.13 3853.9,-724.67 3843,-714 3784.61,-656.83 3586.61,-586.43 3510,-558 3479.69,-546.75 3445.27,-536.77 3416.05,-529.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.92,-525.69 3406.36,-526.57 3415.16,-532.47 3416.92,-525.69"/>
</g>
<!-- /lib/src/event_processor/enricher/enricher_event_processor.dart&#45;&gt;/lib/src/event_processor.dart -->
<g id="edge139" class="edge">
<title>/lib/src/event_processor/enricher/enricher_event_processor.dart&#45;&gt;/lib/src/event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3858.38,-785.88C3843.08,-776.72 3824.06,-765.34 3807.58,-755.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3809.11,-752.32 3798.73,-750.19 3805.52,-758.33 3809.11,-752.32"/>
</g>
<!-- /lib/src/event_processor/enricher/web_enricher_event_processor.dart -->
<g id="node74" class="node">
<title>/lib/src/event_processor/enricher/web_enricher_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4222.5,-896C4222.5,-896 4021.5,-896 4021.5,-896 4015.5,-896 4009.5,-890 4009.5,-884 4009.5,-884 4009.5,-872 4009.5,-872 4009.5,-866 4015.5,-860 4021.5,-860 4021.5,-860 4222.5,-860 4222.5,-860 4228.5,-860 4234.5,-866 4234.5,-872 4234.5,-872 4234.5,-884 4234.5,-884 4234.5,-890 4228.5,-896 4222.5,-896"/>
<text text-anchor="middle" x="4122" y="-874.5" font-family="Arial" font-size="15.00">web_enricher_event_processor</text>
</g>
<!-- /lib/src/event_processor/enricher/web_enricher_event_processor.dart&#45;&gt;/lib/src/event_processor/enricher/enricher_event_processor.dart -->
<g id="edge142" class="edge">
<title>/lib/src/event_processor/enricher/web_enricher_event_processor.dart&#45;&gt;/lib/src/event_processor/enricher/enricher_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4066.31,-859.94C4032.19,-849.48 3988.35,-836.05 3952.56,-825.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3953.29,-821.65 3942.7,-822.07 3951.23,-828.34 3953.29,-821.65"/>
</g>
<!-- /lib/src/event_processor/enricher/web_enricher_event_processor.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge141" class="edge">
<title>/lib/src/event_processor/enricher/web_enricher_event_processor.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4234.7,-861.04C4238.85,-860.66 4242.96,-860.31 4247,-860 4357.14,-851.65 4633.56,-854.01 4744,-852 5670.87,-835.17 6805.49,-810.72 7029.53,-805.86"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.88,-809.35 7039.8,-805.64 7029.73,-802.35 7029.88,-809.35"/>
</g>
<!-- /lib/src/platform/platform.dart -->
<g id="node75" class="node">
<title>/lib/src/platform/platform.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M494.5,-304C494.5,-304 447.5,-304 447.5,-304 441.5,-304 435.5,-298 435.5,-292 435.5,-292 435.5,-280 435.5,-280 435.5,-274 441.5,-268 447.5,-268 447.5,-268 494.5,-268 494.5,-268 500.5,-268 506.5,-274 506.5,-280 506.5,-280 506.5,-292 506.5,-292 506.5,-298 500.5,-304 494.5,-304"/>
<text text-anchor="middle" x="471" y="-282.5" font-family="Arial" font-size="15.00">platform</text>
</g>
<!-- /lib/src/platform/_web_platform.dart -->
<g id="node76" class="node">
<title>/lib/src/platform/_web_platform.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M391.5,-376C391.5,-376 300.5,-376 300.5,-376 294.5,-376 288.5,-370 288.5,-364 288.5,-364 288.5,-352 288.5,-352 288.5,-346 294.5,-340 300.5,-340 300.5,-340 391.5,-340 391.5,-340 397.5,-340 403.5,-346 403.5,-352 403.5,-352 403.5,-364 403.5,-364 403.5,-370 397.5,-376 391.5,-376"/>
<text text-anchor="middle" x="346" y="-354.5" font-family="Arial" font-size="15.00">_web_platform</text>
</g>
<!-- /lib/src/platform/_web_platform.dart&#45;&gt;/lib/src/platform/platform.dart -->
<g id="edge143" class="edge">
<title>/lib/src/platform/_web_platform.dart&#45;&gt;/lib/src/platform/platform.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M376.58,-339.88C393.07,-330.64 413.62,-319.13 431.34,-309.21"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="433.29,-312.13 440.3,-304.19 429.87,-306.02 433.29,-312.13"/>
</g>
<!-- /lib/src/platform/_io_platform.dart -->
<g id="node77" class="node">
<title>/lib/src/platform/_io_platform.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M508.5,-376C508.5,-376 433.5,-376 433.5,-376 427.5,-376 421.5,-370 421.5,-364 421.5,-364 421.5,-352 421.5,-352 421.5,-346 427.5,-340 433.5,-340 433.5,-340 508.5,-340 508.5,-340 514.5,-340 520.5,-346 520.5,-352 520.5,-352 520.5,-364 520.5,-364 520.5,-370 514.5,-376 508.5,-376"/>
<text text-anchor="middle" x="471" y="-354.5" font-family="Arial" font-size="15.00">_io_platform</text>
</g>
<!-- /lib/src/platform/_io_platform.dart&#45;&gt;/lib/src/platform/platform.dart -->
<g id="edge144" class="edge">
<title>/lib/src/platform/_io_platform.dart&#45;&gt;/lib/src/platform/platform.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M471,-339.7C471,-331.98 471,-322.71 471,-314.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="474.5,-314.1 471,-304.1 467.5,-314.1 474.5,-314.1"/>
</g>
<!-- /lib/src/hint.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="edge145" class="edge">
<title>/lib/src/hint.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3458.39,-656.42C3574.27,-654.01 4035.72,-644.61 4415,-640 4446.58,-639.62 5521.68,-640.83 5552,-632 5568.58,-627.17 5585.06,-617.52 5598.33,-608.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5600.54,-610.94 5606.55,-602.21 5596.41,-605.28 5600.54,-610.94"/>
</g>
<!-- /lib/src/platform_checker.dart&#45;&gt;/lib/src/platform/platform.dart -->
<g id="edge146" class="edge">
<title>/lib/src/platform_checker.dart&#45;&gt;/lib/src/platform/platform.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M572.22,-339.88C554.37,-330.55 532.1,-318.92 513,-308.94"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="514.39,-305.72 503.91,-304.19 511.15,-311.92 514.39,-305.72"/>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_span_context.dart -->
<g id="edge149" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_span_context.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4486.22,-655.41C4556.17,-651.93 4730.77,-643.63 4877,-640 4900.69,-639.41 5707.13,-638.2 5730,-632 5747.97,-627.13 5766.11,-617.34 5780.75,-607.95"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5783,-610.67 5789.38,-602.21 5779.12,-604.84 5783,-610.67"/>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/invalid_sentry_trace_header_exception.dart -->
<g id="edge152" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/invalid_sentry_trace_header_exception.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4447.62,-639.63C4439.66,-617.81 4430.53,-580.52 4450,-558 4478.2,-525.39 4599.47,-537.09 4642,-530 4762.81,-509.87 5086.57,-486.35 5179,-406 5189.33,-397.02 5278.7,-193.73 5290,-186 5305.3,-175.54 5354.85,-181.73 5373,-178 5404.2,-171.58 5438.13,-160.99 5465.52,-151.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5467.03,-154.66 5475.3,-148.04 5464.71,-148.06 5467.03,-154.66"/>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart -->
<g id="edge155" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4423.89,-650.9C4403.07,-647.09 4375,-642.44 4350,-640 4312.89,-636.38 3219.76,-600.6 2856.26,-588.78"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2856.35,-585.28 2846.24,-588.45 2856.12,-592.27 2856.35,-585.28"/>
</g>
<!-- /lib/src/sentry_measurement_unit.dart -->
<g id="node89" class="node">
<title>/lib/src/sentry_measurement_unit.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5481.5,-76C5481.5,-76 5314.5,-76 5314.5,-76 5308.5,-76 5302.5,-70 5302.5,-64 5302.5,-64 5302.5,-52 5302.5,-52 5302.5,-46 5308.5,-40 5314.5,-40 5314.5,-40 5481.5,-40 5481.5,-40 5487.5,-40 5493.5,-46 5493.5,-52 5493.5,-52 5493.5,-64 5493.5,-64 5493.5,-70 5487.5,-76 5481.5,-76"/>
<text text-anchor="middle" x="5398" y="-54.5" font-family="Arial" font-size="15.00">sentry_measurement_unit</text>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_measurement_unit.dart -->
<g id="edge154" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_measurement_unit.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4454.21,-639.73C4453.46,-614.41 4453.79,-569.02 4465,-558 4491.64,-531.82 4763.07,-535.61 4800,-530 4946.27,-507.79 4988.91,-516.02 5125,-458 5163.33,-441.66 5170.52,-432.09 5203,-406 5230.51,-383.9 5399.42,-199.54 5432,-186 5450.37,-178.37 5776.09,-192.22 5790,-178 5810.51,-157.03 5809.3,-134.09 5790,-112 5780.57,-101.21 5616.7,-81.95 5503.85,-69.86"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5504.09,-66.36 5493.78,-68.78 5503.35,-73.32 5504.09,-66.36"/>
</g>
<!-- /lib/src/sentry_transaction_context.dart -->
<g id="node92" class="node">
<title>/lib/src/sentry_transaction_context.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4343.5,-602C4343.5,-602 4170.5,-602 4170.5,-602 4164.5,-602 4158.5,-596 4158.5,-590 4158.5,-590 4158.5,-578 4158.5,-578 4158.5,-572 4164.5,-566 4170.5,-566 4170.5,-566 4343.5,-566 4343.5,-566 4349.5,-566 4355.5,-572 4355.5,-578 4355.5,-578 4355.5,-590 4355.5,-590 4355.5,-596 4349.5,-602 4343.5,-602"/>
<text text-anchor="middle" x="4257" y="-580.5" font-family="Arial" font-size="15.00">sentry_transaction_context</text>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_transaction_context.dart -->
<g id="edge147" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_transaction_context.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4423.77,-647.62C4416.27,-644.91 4408.39,-642.12 4401,-640 4383.57,-635 4378.34,-637.28 4361,-632 4338.29,-625.08 4313.88,-615.25 4294.33,-606.37"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4295.7,-603.14 4285.15,-602.1 4292.74,-609.49 4295.7,-603.14"/>
</g>
<!-- /lib/src/sentry_span_interface.dart -->
<g id="node94" class="node">
<title>/lib/src/sentry_span_interface.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3676,-750C3676,-750 3536,-750 3536,-750 3530,-750 3524,-744 3524,-738 3524,-738 3524,-726 3524,-726 3524,-720 3530,-714 3536,-714 3536,-714 3676,-714 3676,-714 3682,-714 3688,-720 3688,-726 3688,-726 3688,-738 3688,-738 3688,-744 3682,-750 3676,-750"/>
<text text-anchor="middle" x="3606" y="-728.5" font-family="Arial" font-size="15.00">sentry_span_interface</text>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_span_interface.dart -->
<g id="edge150" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_span_interface.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4423.99,-660.14C4321.19,-663.95 3983.41,-678.14 3706,-714 3703.49,-714.32 3700.94,-714.67 3698.37,-715.04"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3697.73,-711.6 3688.37,-716.56 3698.78,-718.52 3697.73,-711.6"/>
</g>
<!-- /lib/src/sentry_traces_sampling_decision.dart -->
<g id="node98" class="node">
<title>/lib/src/sentry_traces_sampling_decision.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4699.5,-602C4699.5,-602 4486.5,-602 4486.5,-602 4480.5,-602 4474.5,-596 4474.5,-590 4474.5,-590 4474.5,-578 4474.5,-578 4474.5,-572 4480.5,-566 4486.5,-566 4486.5,-566 4699.5,-566 4699.5,-566 4705.5,-566 4711.5,-572 4711.5,-578 4711.5,-578 4711.5,-590 4711.5,-590 4711.5,-596 4705.5,-602 4699.5,-602"/>
<text text-anchor="middle" x="4593" y="-580.5" font-family="Arial" font-size="15.00">sentry_traces_sampling_decision</text>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_traces_sampling_decision.dart -->
<g id="edge156" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_traces_sampling_decision.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4486.31,-640.66C4505.57,-630.61 4530.48,-617.62 4551.38,-606.71"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4553.12,-609.75 4560.37,-602.02 4549.88,-603.55 4553.12,-609.75"/>
</g>
<!-- /lib/src/noop_sentry_span.dart -->
<g id="node104" class="node">
<title>/lib/src/noop_sentry_span.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4404,-376C4404,-376 4288,-376 4288,-376 4282,-376 4276,-370 4276,-364 4276,-364 4276,-352 4276,-352 4276,-346 4282,-340 4288,-340 4288,-340 4404,-340 4404,-340 4410,-340 4416,-346 4416,-352 4416,-352 4416,-364 4416,-364 4416,-370 4410,-376 4404,-376"/>
<text text-anchor="middle" x="4346" y="-354.5" font-family="Arial" font-size="15.00">noop_sentry_span</text>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/noop_sentry_span.dart -->
<g id="edge151" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/noop_sentry_span.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4423.79,-647.92C4403.61,-640.95 4381.36,-632.44 4381,-632 4350.61,-594.55 4340.82,-447.7 4342.37,-386.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4345.87,-386.29 4342.76,-376.16 4338.87,-386.01 4345.87,-386.29"/>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_measurement.dart -->
<g id="edge153" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_measurement.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4434.77,-640C4432.84,-637.52 4431.17,-634.84 4430,-632 4417.41,-601.61 4409.28,-583.54 4430,-558 4455.83,-526.17 5119.72,-431.26 5152,-406 5173.4,-389.25 5245.8,-223.02 5273.81,-157.42"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5277.06,-158.72 5277.76,-148.15 5270.62,-155.98 5277.06,-158.72"/>
</g>
<!-- /lib/src/sentry_sampling_context.dart -->
<g id="node139" class="node">
<title>/lib/src/sentry_sampling_context.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4900,-602C4900,-602 4742,-602 4742,-602 4736,-602 4730,-596 4730,-590 4730,-590 4730,-578 4730,-578 4730,-572 4736,-566 4742,-566 4742,-566 4900,-566 4900,-566 4906,-566 4912,-572 4912,-578 4912,-578 4912,-590 4912,-590 4912,-596 4906,-602 4900,-602"/>
<text text-anchor="middle" x="4821" y="-580.5" font-family="Arial" font-size="15.00">sentry_sampling_context</text>
</g>
<!-- /lib/src/tracing.dart&#45;&gt;/lib/src/sentry_sampling_context.dart -->
<g id="edge148" class="edge">
<title>/lib/src/tracing.dart&#45;&gt;/lib/src/sentry_sampling_context.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M4486.14,-657.45C4535.05,-657.18 4631.83,-653.47 4711,-632 4732.5,-626.17 4755.09,-616.08 4774.26,-606.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4775.97,-609.79 4783.38,-602.23 4772.87,-603.52 4775.97,-609.79"/>
</g>
<!-- /lib/src/exception_cause.dart -->
<g id="node81" class="node">
<title>/lib/src/exception_cause.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6301,-304C6301,-304 6197,-304 6197,-304 6191,-304 6185,-298 6185,-292 6185,-292 6185,-280 6185,-280 6185,-274 6191,-268 6197,-268 6197,-268 6301,-268 6301,-268 6307,-268 6313,-274 6313,-280 6313,-280 6313,-292 6313,-292 6313,-298 6307,-304 6301,-304"/>
<text text-anchor="middle" x="6249" y="-282.5" font-family="Arial" font-size="15.00">exception_cause</text>
</g>
<!-- /lib/src/noop_isolate_error_integration.dart -->
<g id="node83" class="node">
<title>/lib/src/noop_isolate_error_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4919.5,-750C4919.5,-750 4724.5,-750 4724.5,-750 4718.5,-750 4712.5,-744 4712.5,-738 4712.5,-738 4712.5,-726 4712.5,-726 4712.5,-720 4718.5,-714 4724.5,-714 4724.5,-714 4919.5,-714 4919.5,-714 4925.5,-714 4931.5,-720 4931.5,-726 4931.5,-726 4931.5,-738 4931.5,-738 4931.5,-744 4925.5,-750 4919.5,-750"/>
<text text-anchor="middle" x="4822" y="-728.5" font-family="Arial" font-size="15.00">noop_isolate_error_integration</text>
</g>
<!-- /lib/src/noop_isolate_error_integration.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge159" class="edge">
<title>/lib/src/noop_isolate_error_integration.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4822.35,-713.8C4823.78,-692.79 4829.64,-657.85 4852,-640 4866.65,-628.3 4923.39,-645.87 4936,-632 4958.13,-607.67 4958.92,-581.58 4936,-558 4922.55,-544.17 3717.92,-520.09 3416.69,-514.29"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.42,-510.78 3406.35,-514.09 3416.28,-517.78 3416.42,-510.78"/>
</g>
<!-- /lib/src/noop_isolate_error_integration.dart&#45;&gt;/lib/src/integration.dart -->
<g id="edge158" class="edge">
<title>/lib/src/noop_isolate_error_integration.dart&#45;&gt;/lib/src/integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4858.59,-713.92C4863.19,-711.42 4867.77,-708.75 4872,-706 4882.56,-699.13 4893.41,-690.61 4902.77,-682.72"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4905.27,-685.19 4910.57,-676.01 4900.71,-679.88 4905.27,-685.19"/>
</g>
<!-- /lib/src/noop_isolate_error_integration.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge157" class="edge">
<title>/lib/src/noop_isolate_error_integration.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4845.19,-713.85C4847.69,-711.4 4850.03,-708.76 4852,-706 4870.2,-680.45 4852.12,-659.1 4877,-640 4891,-629.25 4938.68,-635.38 4956,-632 4999.38,-623.54 5047.93,-608.59 5080.25,-597.82"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5081.38,-601.13 5089.74,-594.62 5079.15,-594.5 5081.38,-601.13"/>
</g>
<!-- /lib/src/sentry_trace_context_header.dart&#45;&gt;/lib/src/protocol/sentry_id.dart -->
<g id="edge160" class="edge">
<title>/lib/src/sentry_trace_context_header.dart&#45;&gt;/lib/src/protocol/sentry_id.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2666.34,-565.99C2651.09,-562.94 2635.07,-560.07 2620,-558 2529.31,-545.52 2292.81,-566.83 2209,-530 2049.56,-459.93 2086.54,-325.08 1925,-260 1889.6,-245.74 586.3,-266.52 551,-252 539.77,-247.38 529.73,-238.74 521.78,-230.12"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="524.25,-227.63 515.08,-222.32 518.94,-232.19 524.25,-227.63"/>
</g>
<!-- /lib/src/sentry_trace_context_header.dart&#45;&gt;/lib/src/sentry_baggage.dart -->
<g id="edge161" class="edge">
<title>/lib/src/sentry_trace_context_header.dart&#45;&gt;/lib/src/sentry_baggage.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2749.1,-602.21C2754.72,-612.98 2761.97,-627.2 2768,-640 2811.76,-732.92 2784.84,-781.99 2860,-852 2860.69,-852.64 2861.4,-853.27 2862.12,-853.88"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2860.25,-856.85 2870.42,-859.83 2864.33,-851.16 2860.25,-856.85"/>
</g>
<!-- /lib/src/sentry_trace_context_header.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge162" class="edge">
<title>/lib/src/sentry_trace_context_header.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2846.08,-570.81C2971.41,-556.4 3176.16,-532.87 3281.58,-520.75"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3282.24,-524.2 3291.78,-519.58 3281.44,-517.24 3282.24,-524.2"/>
</g>
<!-- /lib/src/integration.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge164" class="edge">
<title>/lib/src/integration.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4951.08,-639.87C4953.06,-637.44 4954.77,-634.81 4956,-632 4969.17,-601.86 4978.93,-581.58 4956,-558 4942.37,-543.98 3719.88,-520 3416.53,-514.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.2,-510.75 3406.13,-514.06 3416.06,-517.75 3416.2,-510.75"/>
</g>
<!-- /lib/src/integration.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge163" class="edge">
<title>/lib/src/integration.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4972.13,-640.48C5004.8,-627.97 5049.21,-610.96 5080.21,-599.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5081.69,-602.27 5089.77,-595.43 5079.18,-595.73 5081.69,-602.27"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/sentry_event_like.dart -->
<g id="edge194" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/sentry_event_like.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.84,-280.87C2798.56,-274.88 2712.52,-264.07 2639,-260 2608.38,-258.31 460.6,-260.02 431,-252 413.24,-247.18 395.34,-237.44 381.69,-228.06"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="383.52,-225.06 373.37,-222.01 379.4,-230.72 383.52,-225.06"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/span_id.dart -->
<g id="edge196" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/span_id.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.15,-285.12C3060.92,-285.41 3606.57,-284.32 3676,-252 3686.33,-247.19 3695.32,-238.64 3702.36,-230.13"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3705.2,-232.18 3708.5,-222.12 3699.64,-227.92 3705.2,-232.18"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_response.dart -->
<g id="edge178" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_response.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2907.17,-267.84C2925.02,-256.13 2948.86,-240.5 2968.29,-227.76"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2970.43,-230.54 2976.87,-222.13 2966.59,-224.69 2970.43,-230.54"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_thread.dart -->
<g id="edge193" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_thread.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.94,-280.36C2800.41,-274.53 2718.43,-264.73 2648,-260 2615.51,-257.82 2385.81,-262.56 2355,-252 2340.76,-247.12 2327.03,-237.85 2315.96,-228.83"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2317.92,-225.9 2308.05,-222.05 2313.37,-231.22 2317.92,-225.9"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_stack_frame.dart -->
<g id="edge187" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_stack_frame.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.76,-280.36C2797.76,-274.28 2709.06,-263.95 2633,-260 2624.52,-259.56 193.99,-258.01 188,-252 67.12,-130.73 403.53,-82.04 568.16,-65.87"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="568.65,-69.33 578.27,-64.89 567.98,-62.37 568.65,-69.33"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_transaction_info.dart -->
<g id="edge203" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_transaction_info.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.11,-284.93C3028.33,-284.44 3377.27,-280.57 3486,-252 3506.42,-246.63 3527.62,-236.63 3544.8,-227.23"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3546.91,-230.07 3553.9,-222.11 3543.47,-223.97 3546.91,-230.07"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_span.dart -->
<g id="edge199" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_span.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.3,-285C3161.04,-284.89 4597.04,-282.94 4683,-252 4696.09,-247.29 4708.48,-238.39 4717.85,-229.59"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4720.47,-231.92 4724.96,-222.32 4715.47,-227.03 4720.47,-231.92"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/breadcrumb.dart -->
<g id="edge166" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/breadcrumb.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.02,-284.96C3150.83,-284.6 4487.91,-281.27 4568,-252 4580.93,-247.27 4593.12,-238.37 4602.31,-229.58"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4604.89,-231.95 4609.28,-222.31 4599.83,-227.11 4604.89,-231.95"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_request.dart -->
<g id="edge177" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_request.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.09,-283.71C2956.14,-281.19 3022.96,-273.78 3076,-252 3090.07,-246.22 3104.08,-237.06 3115.63,-228.36"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3117.84,-231.07 3123.57,-222.15 3113.53,-225.55 3117.84,-231.07"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_level.dart -->
<g id="edge184" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_level.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2879.26,-267.64C2878.23,-257.3 2876.89,-243.94 2875.72,-232.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2879.18,-231.65 2874.71,-222.05 2872.22,-232.35 2879.18,-231.65"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sdk_version.dart -->
<g id="edge180" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sdk_version.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.1C2800.45,-274 2718.48,-263.91 2648,-260 2620.5,-258.47 690.9,-261.36 665,-252 652.06,-247.32 639.95,-238.31 630.25,-229.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="632.63,-226.85 623.02,-222.39 627.75,-231.87 632.63,-226.85"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_trace_header.dart -->
<g id="edge200" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_trace_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.84,-281.94C2807.73,-278.08 2745.93,-269.67 2695,-252 2677.26,-245.85 2658.84,-236.27 2642.89,-227.36"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2644.24,-224.1 2633.82,-222.22 2640.79,-230.19 2644.24,-224.1"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_transaction.dart -->
<g id="edge197" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_transaction.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-279.93C2801.82,-274.4 2724.1,-265.58 2657,-260 2627.05,-257.51 2550.73,-260.82 2522,-252 2504.66,-246.67 2487.14,-236.81 2472.23,-227.49"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2474.09,-224.52 2463.78,-222.1 2470.33,-230.43 2474.09,-224.52"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_id.dart -->
<g id="edge183" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_id.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.1C2800.45,-274 2718.48,-263.91 2648,-260 2618.92,-258.39 577.95,-263.06 551,-252 539.76,-247.39 529.73,-238.76 521.77,-230.13"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="524.25,-227.64 515.08,-222.33 518.94,-232.2 524.25,-227.64"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy_element.dart -->
<g id="edge205" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy_element.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.1C2800.45,-273.99 2718.48,-263.89 2648,-260 2639.8,-259.55 288.8,-257.82 283,-252 262.29,-231.22 262.41,-206.89 283,-186 288.51,-180.41 840.15,-178.14 848,-178 1612,-164.18 2529.73,-142.13 2857.88,-134.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2858.45,-137.56 2868.36,-133.82 2858.28,-130.56 2858.45,-137.56"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_culture.dart -->
<g id="edge192" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_culture.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.71,-268.34C2835.92,-263.38 2825.4,-257.74 2816,-252 2804.28,-244.85 2791.94,-236.23 2781.2,-228.33"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2783.08,-225.37 2772.97,-222.2 2778.9,-230.98 2783.08,-225.37"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_geo.dart -->
<g id="edge190" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_geo.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.95,-280.27C2800.43,-274.34 2718.45,-264.44 2648,-260 2624.65,-258.53 2248.83,-260.41 2227,-252 2214.68,-247.25 2203.28,-238.35 2194.17,-229.56"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2196.56,-227.01 2187.07,-222.3 2191.55,-231.9 2196.56,-227.01"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/debug_meta.dart -->
<g id="edge170" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/debug_meta.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2846,-279.37C2801.9,-273.35 2724.21,-264.1 2657,-260 2627.61,-258.21 2154.72,-261.93 2127,-252 2113.41,-247.13 2100.58,-237.74 2089.66,-228.63"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2091.93,-225.96 2082.07,-222.1 2087.37,-231.27 2091.93,-225.96"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/mechanism.dart -->
<g id="edge174" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.01,-285.06C3168.17,-285.41 4699.8,-286.1 4791,-252 4803.63,-247.28 4815.42,-238.38 4824.9,-229.58"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4827.61,-231.83 4832.28,-222.32 4822.7,-226.84 4827.61,-231.83"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_app.dart -->
<g id="edge165" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_app.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.17C2800.44,-274.14 2718.47,-264.13 2648,-260 2626.2,-258.72 1882.41,-259.76 1862,-252 1849.66,-247.31 1838.26,-238.42 1829.14,-229.62"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1831.54,-227.06 1822.06,-222.34 1826.53,-231.95 1831.54,-227.06"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_gpu.dart -->
<g id="edge173" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_gpu.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.16C2800.44,-274.12 2718.47,-264.09 2648,-260 2623.18,-258.56 1776.25,-260.82 1753,-252 1740.65,-247.32 1729.25,-238.42 1720.14,-229.62"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1722.54,-227.07 1713.05,-222.35 1717.52,-231.95 1722.54,-227.07"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_trace_context.dart -->
<g id="edge198" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_trace_context.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.26,-284.83C3085.81,-283.83 3811.96,-277.97 3909,-252 3927.98,-246.92 3947.42,-237.11 3962.4,-227.76"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3964.67,-230.46 3971.11,-222.05 3960.83,-224.61 3964.67,-230.46"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/contexts.dart -->
<g id="edge168" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/contexts.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.1C2800.45,-273.99 2718.48,-263.89 2648,-260 2639.54,-259.53 212.98,-258 207,-252 186.29,-231.22 186.59,-207.07 207,-186 216.79,-175.9 447.29,-184.02 460,-178 469.98,-173.27 478.52,-164.85 485.18,-156.43"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="488.15,-158.29 491.19,-148.14 482.48,-154.18 488.15,-158.29"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/debug_image.dart -->
<g id="edge169" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/debug_image.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.14C2800.44,-274.07 2718.47,-264.02 2648,-260 2616.05,-258.18 1526.34,-262.19 1496,-252 1481.88,-247.26 1468.34,-238.01 1457.46,-228.97"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1459.52,-226.12 1449.69,-222.17 1454.91,-231.39 1459.52,-226.12"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_baggage_header.dart -->
<g id="edge202" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_baggage_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.11C2800.45,-274.02 2718.48,-263.93 2648,-260 2598.47,-257.24 910.35,-263.11 862,-252 840.39,-247.03 817.97,-236.72 800.03,-227.02"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="801.53,-223.85 791.09,-222.04 798.12,-229.96 801.53,-223.85"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_stack_trace.dart -->
<g id="edge188" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_stack_trace.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.1C2800.45,-273.99 2718.48,-263.89 2648,-260 2639.73,-259.54 269.84,-257.86 264,-252 243.29,-231.22 243.46,-206.94 264,-186 272.23,-177.61 675.68,-181.16 687,-178 704.77,-173.04 722.69,-163.24 737.15,-153.87"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="739.33,-156.63 745.67,-148.14 735.42,-150.82 739.33,-156.63"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_runtime.dart -->
<g id="edge186" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_runtime.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.12C2800.45,-274.04 2718.47,-263.97 2648,-260 2609.34,-257.82 1290.89,-263.77 1254,-252 1239.08,-247.24 1224.58,-237.86 1212.91,-228.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1214.9,-225.83 1204.95,-222.19 1210.46,-231.24 1214.9,-225.83"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_device.dart -->
<g id="edge171" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_device.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.26,-284.79C3129.8,-283.41 4243.01,-275.15 4311,-252 4325.25,-247.15 4338.98,-237.89 4350.05,-228.86"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4352.64,-231.24 4357.96,-222.08 4348.09,-225.93 4352.64,-231.24"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_message.dart -->
<g id="edge175" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_message.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.16,-285.23C3109.97,-286.33 4043.77,-289.66 4166,-252 4182.03,-247.06 4197.93,-237.53 4210.77,-228.34"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4213.22,-230.87 4219.14,-222.08 4209.03,-225.27 4213.22,-230.87"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sdk_info.dart -->
<g id="edge179" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sdk_info.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.23,-284.77C3108.1,-283.42 4020.6,-275.76 4075,-252 4085.89,-247.24 4095.54,-238.58 4103.14,-229.97"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4105.88,-232.15 4109.53,-222.2 4100.48,-227.7 4105.88,-232.15"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/max_body_size.dart -->
<g id="edge191" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/max_body_size.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.06,-284.83C3069.86,-283.94 3680.76,-278.72 3762,-252 3777.33,-246.96 3792.37,-237.42 3804.48,-228.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3806.69,-230.95 3812.36,-222 3802.35,-225.46 3806.69,-230.95"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_package.dart -->
<g id="edge185" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_package.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.1C2800.45,-273.99 2718.48,-263.89 2648,-260 2639.67,-259.54 250.89,-257.91 245,-252 224.29,-231.22 224.54,-207.02 245,-186 256.67,-174.02 530.05,-183.03 546,-178 561.69,-173.05 577.16,-163.52 589.63,-154.32"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="591.97,-156.95 597.76,-148.08 587.7,-151.4 591.97,-156.95"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_event.dart -->
<g id="edge181" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_event.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.13,-284.89C3141.22,-284.05 4372.65,-278.2 4447,-252 4460.4,-247.28 4473.18,-238.38 4482.91,-229.59"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4485.63,-231.82 4490.31,-222.32 4480.72,-226.83 4485.63,-231.82"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_operating_system.dart -->
<g id="edge176" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_operating_system.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.27,-285.25C2977.25,-284.86 3105.87,-280.36 3210,-252 3231.57,-246.13 3254.27,-236.09 3272.77,-226.8"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3274.47,-229.86 3281.76,-222.17 3271.27,-223.63 3274.47,-229.86"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_user.dart -->
<g id="edge189" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_user.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.06,-284.63C3196,-281.69 5058.43,-261.77 5085,-252 5097.92,-247.25 5110.02,-238.22 5119.73,-229.34"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5122.21,-231.8 5126.96,-222.33 5117.34,-226.78 5122.21,-231.8"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_exception.dart -->
<g id="edge182" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_exception.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.6,-279.23C2801.44,-273.12 2724.02,-263.85 2657,-260 2629.16,-258.4 1679.75,-259.89 1653,-252 1636.44,-247.11 1620.02,-237.33 1606.09,-227.94"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1607.98,-224.99 1597.76,-222.2 1604.01,-230.76 1607.98,-224.99"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/span_status.dart -->
<g id="edge195" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/span_status.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.13C2800.44,-274.06 2718.47,-263.99 2648,-260 2612.56,-257.99 1403.43,-263.96 1370,-252 1356.9,-247.31 1344.6,-238.3 1334.72,-229.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1337,-226.75 1327.35,-222.39 1332.17,-231.82 1337,-226.75"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_transaction_name_source.dart -->
<g id="edge201" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_transaction_name_source.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.96,-280.12C2800.45,-274.03 2718.48,-263.96 2648,-260 2563.24,-255.24 1203.56,-266.97 1120,-252 1091.42,-246.88 1060.82,-235.99 1036.61,-225.98"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1037.89,-222.72 1027.32,-222.05 1035.17,-229.17 1037.89,-222.72"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy.dart -->
<g id="edge204" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.29,-284.88C3177.39,-283.92 4801.82,-276.97 4902,-252 4922.12,-246.98 4942.82,-236.92 4959.49,-227.38"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4961.47,-230.27 4968.3,-222.17 4957.91,-224.25 4961.47,-230.27"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_browser.dart -->
<g id="edge167" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/sentry_browser.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2845.95,-280.19C2800.44,-274.19 2718.46,-264.21 2648,-260 2612.06,-257.85 2034.34,-262.82 2000,-252 1984.61,-247.15 1969.56,-237.63 1957.46,-228.42"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1959.59,-225.65 1949.59,-222.16 1955.23,-231.12 1959.59,-225.65"/>
</g>
<!-- /lib/src/protocol.dart&#45;&gt;/lib/src/protocol/dsn.dart -->
<g id="edge172" class="edge">
<title>/lib/src/protocol.dart&#45;&gt;/lib/src/protocol/dsn.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2916.11,-284.48C3027.66,-282.59 3369.2,-274.96 3413,-252 3422.15,-247.2 3429.77,-239.04 3435.68,-230.85"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3438.63,-232.73 3441.2,-222.45 3432.78,-228.88 3438.63,-232.73"/>
</g>
<!-- /lib/src/sentry_attachment/io_sentry_attachment.dart -->
<g id="node87" class="node">
<title>/lib/src/sentry_attachment/io_sentry_attachment.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5705.5,-676C5705.5,-676 5568.5,-676 5568.5,-676 5562.5,-676 5556.5,-670 5556.5,-664 5556.5,-664 5556.5,-652 5556.5,-652 5556.5,-646 5562.5,-640 5568.5,-640 5568.5,-640 5705.5,-640 5705.5,-640 5711.5,-640 5717.5,-646 5717.5,-652 5717.5,-652 5717.5,-664 5717.5,-664 5717.5,-670 5711.5,-676 5705.5,-676"/>
<text text-anchor="middle" x="5637" y="-654.5" font-family="Arial" font-size="15.00">io_sentry_attachment</text>
</g>
<!-- /lib/src/sentry_attachment/io_sentry_attachment.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="edge206" class="edge">
<title>/lib/src/sentry_attachment/io_sentry_attachment.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5634.63,-639.94C5633.49,-631.72 5632.09,-621.66 5630.81,-612.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5634.26,-611.86 5629.42,-602.44 5627.33,-612.83 5634.26,-611.86"/>
</g>
<!-- /lib/src/sentry_attachment/sentry_attachment.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge208" class="edge">
<title>/lib/src/sentry_attachment/sentry_attachment.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5672.55,-565.98C5688.25,-557.66 5704.11,-545.93 5713,-530 5722.53,-512.93 5724.84,-501.56 5713,-486 5690.03,-455.82 5088.54,-321.72 5055,-304 5028.23,-289.86 5031.42,-270.44 5003,-260 4988.17,-254.55 464.55,-261.6 452,-252 452,-252 436.06,-193.54 426.33,-157.89"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="429.67,-156.81 423.66,-148.08 422.91,-158.65 429.67,-156.81"/>
</g>
<!-- /lib/src/sentry_attachment/sentry_attachment.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy.dart -->
<g id="edge207" class="edge">
<title>/lib/src/sentry_attachment/sentry_attachment.dart&#45;&gt;/lib/src/protocol/sentry_view_hierarchy.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5676.51,-565.99C5720.24,-550.92 5777.21,-531.12 5778,-530 5789.31,-514.05 5789.97,-501.47 5778,-486 5747.65,-446.78 5704.53,-493.59 5670,-458 5656.04,-443.61 5675.3,-426.96 5660,-414 5636.56,-394.15 5544.53,-426.88 5522,-406 5497.65,-383.45 5535.78,-355.15 5512,-332 5428.71,-250.94 5338.82,-381.27 5252,-304 5237.02,-290.67 5257.35,-272.91 5242,-260 5228.63,-248.76 5101.86,-256.56 5085,-252 5065.3,-246.68 5044.98,-236.68 5028.55,-227.28"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5030.24,-224.21 5019.85,-222.14 5026.68,-230.24 5030.24,-224.21"/>
</g>
<!-- /lib/src/exception_cause_extractor.dart -->
<g id="node90" class="node">
<title>/lib/src/exception_cause_extractor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6335,-602C6335,-602 6163,-602 6163,-602 6157,-602 6151,-596 6151,-590 6151,-590 6151,-578 6151,-578 6151,-572 6157,-566 6163,-566 6163,-566 6335,-566 6335,-566 6341,-566 6347,-572 6347,-578 6347,-578 6347,-590 6347,-590 6347,-596 6341,-602 6335,-602"/>
<text text-anchor="middle" x="6249" y="-580.5" font-family="Arial" font-size="15.00">exception_cause_extractor</text>
</g>
<!-- /lib/src/exception_cause_extractor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge211" class="edge">
<title>/lib/src/exception_cause_extractor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M6185.44,-565.94C6170.94,-562.7 6155.52,-559.78 6141,-558 6001.68,-540.95 3828.77,-517.91 3416.31,-513.68"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.07,-510.18 3406.04,-513.58 3416,-517.18 3416.07,-510.18"/>
</g>
<!-- /lib/src/exception_cause_extractor.dart&#45;&gt;/lib/src/exception_cause.dart -->
<g id="edge210" class="edge">
<title>/lib/src/exception_cause_extractor.dart&#45;&gt;/lib/src/exception_cause.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M6249,-565.81C6249,-516.56 6249,-374.64 6249,-314.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6252.5,-314.24 6249,-304.24 6245.5,-314.24 6252.5,-314.24"/>
</g>
<!-- /lib/src/exception_cause_extractor.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge209" class="edge">
<title>/lib/src/exception_cause_extractor.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M6177.7,-565.95C5945.52,-510.49 5223.11,-338.39 5172,-332 4941.7,-303.23 3227.6,-289.52 2926.19,-287.32"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.09,-283.82 2916.07,-287.25 2926.04,-290.82 2926.09,-283.82"/>
</g>
<!-- /lib/src/noop_sentry_client.dart -->
<g id="node91" class="node">
<title>/lib/src/noop_sentry_client.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1011,-1072C1011,-1072 893,-1072 893,-1072 887,-1072 881,-1066 881,-1060 881,-1060 881,-1048 881,-1048 881,-1042 887,-1036 893,-1036 893,-1036 1011,-1036 1011,-1036 1017,-1036 1023,-1042 1023,-1048 1023,-1048 1023,-1060 1023,-1060 1023,-1066 1017,-1072 1011,-1072"/>
<text text-anchor="middle" x="952" y="-1050.5" font-family="Arial" font-size="15.00">noop_sentry_client</text>
</g>
<!-- /lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_client.dart -->
<g id="edge215" class="edge">
<title>/lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1023.12,-1050.33C1322.3,-1039.1 2476.21,-995.79 2755.75,-985.3"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2756.23,-988.78 2766.1,-984.91 2755.97,-981.79 2756.23,-988.78"/>
</g>
<!-- /lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge212" class="edge">
<title>/lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1023.14,-1052.96C1333.68,-1052.52 2572.06,-1047.74 2958,-1000 3047.03,-988.99 3078.34,-1002.61 3155,-956 3214.98,-919.53 3298.49,-751.22 3358,-714 3370.43,-706.23 3377.5,-713.65 3390,-706 3399.05,-700.46 3407.18,-692.28 3413.76,-684.29"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.7,-686.2 3420.04,-676.15 3411.16,-681.93 3416.7,-686.2"/>
</g>
<!-- /lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart -->
<g id="edge217" class="edge">
<title>/lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1023.36,-1046.33C1252.47,-1023.08 1987.07,-934.27 2547,-706 2609.21,-680.64 2673.85,-635.65 2710.29,-608.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2712.65,-610.86 2718.5,-602.03 2708.42,-605.29 2712.65,-610.86"/>
</g>
<!-- /lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge213" class="edge">
<title>/lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M939.45,-1035.97C931.94,-1025.66 922.27,-1012.18 914,-1000 870.1,-935.33 847.99,-924.59 819,-852 786.6,-770.89 782,-746.34 782,-659 782,-659 782,-659 782,-439 782,-390.62 765.02,-363.19 802,-332 840.88,-299.21 2582.18,-306.13 2633,-304 2703.85,-301.03 2785.92,-294.87 2835.62,-290.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2836.03,-294.32 2845.71,-290.01 2835.46,-287.34 2836.03,-294.32"/>
</g>
<!-- /lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge218" class="edge">
<title>/lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M939.19,-1035.81C932.52,-1025.88 924.95,-1012.84 921,-1000 915.23,-981.27 918.67,-975.59 918,-956 914.27,-846.5 876.83,-779.05 965,-714 979.98,-702.95 1618.38,-714.38 1635,-706 1667.86,-689.43 1656.87,-663.72 1685,-640 1700.86,-626.63 1720.51,-615.31 1738.58,-606.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1740.31,-609.53 1747.85,-602.09 1737.31,-603.2 1740.31,-609.53"/>
</g>
<!-- /lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/scope.dart -->
<g id="edge214" class="edge">
<title>/lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/scope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1023.07,-1053.3C1325.68,-1054.23 2508.13,-1054.24 2876,-1000 2949.81,-989.12 2988.88,-1012.16 3038,-956 3053.67,-938.08 3057.19,-870.66 3057.9,-832.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3061.4,-832.29 3058.04,-822.24 3054.4,-832.19 3061.4,-832.29"/>
</g>
<!-- /lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_envelope.dart -->
<g id="edge216" class="edge">
<title>/lib/src/noop_sentry_client.dart&#45;&gt;/lib/src/sentry_envelope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M944.53,-1035.92C929.37,-998.28 900.17,-907.74 947,-860 955.09,-851.75 1351.47,-852.85 1363,-852 1716.94,-825.88 1805.37,-810.98 2155,-750 2156.25,-749.78 2157.51,-749.56 2158.78,-749.33"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2159.47,-752.76 2168.64,-747.46 2158.16,-745.88 2159.47,-752.76"/>
</g>
<!-- /lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/sentry_trace_origins.dart -->
<g id="edge219" class="edge">
<title>/lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/sentry_trace_origins.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4355.52,-569.74C4390.09,-565.46 4429.21,-561.04 4465,-558 4762.02,-532.73 5115.67,-520.11 5275.72,-515.33"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5276.09,-518.82 5285.98,-515.03 5275.88,-511.83 5276.09,-518.82"/>
</g>
<!-- /lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/sentry_baggage.dart -->
<g id="edge221" class="edge">
<title>/lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/sentry_baggage.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4158.28,-587C3913.44,-592.41 3291.6,-609.04 3203,-640 3156.08,-656.4 3055.01,-733.75 3021,-770 2991.64,-801.3 3000.14,-821.45 2970,-852 2969.58,-852.43 2969.14,-852.86 2968.7,-853.28"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2966.42,-850.63 2961.03,-859.75 2970.93,-855.98 2966.42,-850.63"/>
</g>
<!-- /lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge222" class="edge">
<title>/lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4300.8,-602.1C4323.85,-611.95 4352.58,-623.95 4379,-632 4393.5,-636.42 4399.53,-635.55 4414.14,-638.92"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4413.31,-642.32 4423.87,-641.47 4415.08,-635.55 4413.31,-642.32"/>
</g>
<!-- /lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge220" class="edge">
<title>/lib/src/sentry_transaction_context.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4255.27,-565.68C4249.54,-518.49 4226.5,-387.44 4144,-332 4092.52,-297.4 3146.18,-288.81 2926.35,-287.28"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.16,-283.78 2916.14,-287.21 2926.12,-290.78 2926.16,-283.78"/>
</g>
<!-- /lib/src/sentry_tracer_finish_status.dart -->
<g id="node105" class="node">
<title>/lib/src/sentry_tracer_finish_status.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5791.5,-76C5791.5,-76 5618.5,-76 5618.5,-76 5612.5,-76 5606.5,-70 5606.5,-64 5606.5,-64 5606.5,-52 5606.5,-52 5606.5,-46 5612.5,-40 5618.5,-40 5618.5,-40 5791.5,-40 5791.5,-40 5797.5,-40 5803.5,-46 5803.5,-52 5803.5,-52 5803.5,-64 5803.5,-64 5803.5,-70 5797.5,-76 5791.5,-76"/>
<text text-anchor="middle" x="5705" y="-54.5" font-family="Arial" font-size="15.00">sentry_tracer_finish_status</text>
</g>
<!-- /lib/src/sentry_tracer.dart&#45;&gt;/lib/src/sentry_tracer_finish_status.dart -->
<g id="edge224" class="edge">
<title>/lib/src/sentry_tracer.dart&#45;&gt;/lib/src/sentry_tracer_finish_status.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5723.07,-111.7C5720.39,-103.9 5717.17,-94.51 5714.2,-85.83"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5717.42,-84.43 5710.86,-76.1 5710.8,-86.7 5717.42,-84.43"/>
</g>
<!-- /lib/src/utils/sample_rate_format.dart -->
<g id="node120" class="node">
<title>/lib/src/utils/sample_rate_format.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6727.5,-148C6727.5,-148 6600.5,-148 6600.5,-148 6594.5,-148 6588.5,-142 6588.5,-136 6588.5,-136 6588.5,-124 6588.5,-124 6588.5,-118 6594.5,-112 6600.5,-112 6600.5,-112 6727.5,-112 6727.5,-112 6733.5,-112 6739.5,-118 6739.5,-124 6739.5,-124 6739.5,-136 6739.5,-136 6739.5,-142 6733.5,-148 6727.5,-148"/>
<text text-anchor="middle" x="6664" y="-126.5" font-family="Arial" font-size="15.00">sample_rate_format</text>
</g>
<!-- /lib/src/sentry_tracer.dart&#45;&gt;/lib/src/utils/sample_rate_format.dart -->
<g id="edge225" class="edge">
<title>/lib/src/sentry_tracer.dart&#45;&gt;/lib/src/utils/sample_rate_format.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5750.44,-148.1C5767.28,-160.91 5792,-176.7 5817,-182 5837.46,-186.34 6549.54,-186.34 6570,-182 6592.22,-177.29 6614.57,-165.29 6631.9,-154.01"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6634.14,-156.71 6640.47,-148.21 6630.23,-150.91 6634.14,-156.71"/>
</g>
<!-- /lib/src/sentry_tracer.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge223" class="edge">
<title>/lib/src/sentry_tracer.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5750.69,-148.22C5765.8,-159.08 5786.94,-172.13 5808,-178 5823.06,-182.2 6922.18,-175.76 6934,-186 6967.64,-215.13 6949,-240.5 6949,-285 6949,-659 6949,-659 6949,-659 6949,-700.23 6942.27,-717.02 6967,-750 6982.47,-770.63 7008.31,-783.94 7030.11,-792.06"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.19,-795.44 7039.79,-795.43 7031.5,-788.83 7029.19,-795.44"/>
</g>
<!-- /lib/src/sentry_span_interface.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge227" class="edge">
<title>/lib/src/sentry_span_interface.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3688.49,-713.94C3963.78,-678.38 4298.33,-664.16 4413.71,-660.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4413.99,-663.72 4423.86,-659.88 4413.75,-656.72 4413.99,-663.72"/>
</g>
<!-- /lib/src/sentry_span_interface.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge226" class="edge">
<title>/lib/src/sentry_span_interface.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3580.58,-713.91C3552.87,-695.38 3507.1,-665.1 3467,-640 3307.76,-540.33 3230.19,-554.4 3115,-406 3093.24,-377.97 3112.11,-354.9 3085,-332 3061.43,-312.09 2979.07,-298.77 2926.48,-292.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.74,-288.58 2916.39,-290.82 2925.88,-295.52 2926.74,-288.58"/>
</g>
<!-- /lib/src/noop_origin.dart -->
<g id="node95" class="node">
<title>/lib/src/noop_origin.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2612,-304C2612,-304 2542,-304 2542,-304 2536,-304 2530,-298 2530,-292 2530,-292 2530,-280 2530,-280 2530,-274 2536,-268 2542,-268 2542,-268 2612,-268 2612,-268 2618,-268 2624,-274 2624,-280 2624,-280 2624,-292 2624,-292 2624,-298 2618,-304 2612,-304"/>
<text text-anchor="middle" x="2577" y="-282.5" font-family="Arial" font-size="15.00">noop_origin</text>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry.dart -->
<g id="edge232" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4903.85,-1072.1C4876.38,-1084.07 4838.36,-1099.86 4809.97,-1111.06"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4808.4,-1107.92 4800.36,-1114.82 4810.94,-1114.44 4808.4,-1107.92"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry_client.dart -->
<g id="edge233" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4888.39,-1051.38C4688.35,-1044.99 3933.41,-1020.79 3312,-1000 3155.44,-994.76 2971.04,-988.37 2877.7,-985.12"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2877.71,-981.62 2867.59,-984.77 2877.46,-988.61 2877.71,-981.62"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge235" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4942.99,-1035.83C4953.54,-985.62 4976.18,-838.28 4897,-770 4864.18,-741.7 4732.03,-782.18 4703,-750 4636.17,-675.93 4732.54,-669.13 4820,-640 4841.36,-632.89 4905.72,-648.54 4921,-632 4932.16,-619.92 4932.46,-569.79 4921,-558 4907.69,-544.3 3715.75,-520.15 3416.54,-514.3"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.34,-510.8 3406.28,-514.1 3416.21,-517.8 3416.34,-510.8"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge228" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4925.47,-1035.73C4910.55,-1016.52 4886,-984.45 4866,-956 4809.14,-875.13 4829.49,-819.65 4744,-770 4713,-752 4457.74,-752.75 4422,-750 4049.61,-721.34 3599.5,-676.2 3468.45,-662.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3468.47,-659.33 3458.17,-661.79 3467.76,-666.29 3468.47,-659.33"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge236" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4936.48,-1035.75C4927.77,-977.11 4898.28,-788.03 4878,-770 4842.97,-738.85 4709.46,-771.88 4668,-750 4649.58,-740.28 4655.38,-725.48 4638,-714 4594.43,-685.2 4535.24,-671.02 4496.12,-664.37"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4496.57,-660.9 4486.14,-662.77 4495.45,-667.81 4496.57,-660.9"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge230" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4932.99,-1035.7C4913.05,-979.39 4847.63,-803.04 4799,-770 4754.18,-739.55 4608.53,-763.29 4556,-750 4517.41,-740.23 4512.31,-724.78 4474,-714 4448.53,-706.83 4440.44,-713.27 4415,-706 4346.14,-686.32 4337.49,-657.34 4268,-640 4255.14,-636.79 4158.45,-641.29 4149,-632 4111.16,-594.78 4139,-566.08 4139,-513 4139,-513 4139,-513 4139,-439 4139,-268.67 3938.97,-360.27 3771,-332 3606.02,-304.23 3084.55,-291.26 2926.71,-287.91"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.49,-284.4 2916.42,-287.69 2926.34,-291.4 2926.49,-284.4"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge234" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4908.7,-1035.93C4881.03,-1018.93 4840.78,-990.44 4816,-956 4764.23,-884.04 4815.63,-822.22 4744,-770 4713.24,-747.58 4439.92,-753.34 4402,-750 3985.19,-713.33 3884.63,-665.76 3467,-640 3176.88,-622.1 2449.03,-651.34 2159,-632 2064.78,-625.72 1957.79,-611.22 1883.55,-599.97"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1883.71,-596.46 1873.3,-598.41 1882.66,-603.38 1883.71,-596.46"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/scope.dart -->
<g id="edge231" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/scope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4888.27,-1053.52C4704.17,-1054.54 4054.5,-1050.78 3530,-956 3363.56,-925.92 3174.4,-852.99 3095.57,-820.76"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3096.65,-817.42 3086.07,-816.86 3093.99,-823.9 3096.65,-817.42"/>
</g>
<!-- /lib/src/hub_adapter.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge229" class="edge">
<title>/lib/src/hub_adapter.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4945.7,-1035.89C4953.23,-1016.55 4965.75,-984.11 4976,-956 5026.95,-816.29 5024.8,-775.53 5086,-640 5090.35,-630.36 5095.83,-620.18 5100.96,-611.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5104.09,-612.81 5106.12,-602.42 5098.05,-609.27 5104.09,-612.81"/>
</g>
<!-- /lib/src/sentry_user_feedback.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge237" class="edge">
<title>/lib/src/sentry_user_feedback.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1797.39,-565.76C1816.07,-518.19 1875.62,-385.06 1975,-332 2039.55,-297.54 2559.94,-308.06 2633,-304 2703.8,-300.07 2785.88,-294.18 2835.6,-290.47"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.99,-293.95 2845.7,-289.71 2835.46,-286.97 2835.99,-293.95"/>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/throwable_mechanism.dart -->
<g id="edge245" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/throwable_mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5243.38,-1251.95C5194.56,-1239.88 5137.59,-1224.41 5129,-1216 5103.48,-1190.99 5071,-1044.9 5071,-983 5071,-983 5071,-983 5071,-439 5071,-377.22 5007.52,-332.96 4960.53,-308.66"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4961.88,-305.43 4951.37,-304.07 4958.74,-311.68 4961.88,-305.43"/>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/type_check_hint.dart -->
<g id="edge239" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/type_check_hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5330.43,-1288.04C5344.38,-1298.82 5365.81,-1311.85 5387,-1318 5450.53,-1336.44 5889.31,-1341.38 6065.3,-1342.62"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6065.47,-1346.12 6075.5,-1342.69 6065.52,-1339.12 6065.47,-1346.12"/>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge238" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5239.3,-1252.62C5237.86,-1252.4 5236.42,-1252.2 5235,-1252 5131.41,-1237.75 3357,-1231.56 3357,-1127 3357,-1127 3357,-1127 3357,-1053 3357,-900.29 3376.7,-862.33 3413,-714 3415.29,-704.64 3418.39,-694.57 3421.37,-685.64"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3424.7,-686.74 3424.64,-676.14 3418.08,-684.46 3424.7,-686.74"/>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge242" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5239.32,-1252.53C5237.87,-1252.34 5236.43,-1252.16 5235,-1252 4868.21,-1209.9 3942.95,-1229.42 3574,-1216 3417.34,-1210.3 876.24,-1172.58 756,-1072 689.19,-1016.12 722,-966.1 722,-879 722,-879 722,-879 722,-439 722,-389.73 712.09,-362.23 751,-332 792.29,-299.92 2580.76,-306.18 2633,-304 2703.85,-301.05 2785.92,-294.88 2835.62,-290.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2836.03,-294.32 2845.71,-290.02 2835.46,-287.34 2836.03,-294.32"/>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge244" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5239.29,-1252.69C5237.85,-1252.45 5236.42,-1252.22 5235,-1252 5156.2,-1239.79 4931.48,-1275.14 4878,-1216 4842.37,-1176.59 4885.62,-1114.29 4915.4,-1079.89"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4918.22,-1081.97 4922.25,-1072.17 4912.99,-1077.32 4918.22,-1081.97"/>
</g>
<!-- /lib/src/http_client/sentry_http_client.dart -->
<g id="node100" class="node">
<title>/lib/src/http_client/sentry_http_client.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5361,-1216C5361,-1216 5251,-1216 5251,-1216 5245,-1216 5239,-1210 5239,-1204 5239,-1204 5239,-1192 5239,-1192 5239,-1186 5245,-1180 5251,-1180 5251,-1180 5361,-1180 5361,-1180 5367,-1180 5373,-1186 5373,-1192 5373,-1192 5373,-1204 5373,-1204 5373,-1210 5367,-1216 5361,-1216"/>
<text text-anchor="middle" x="5306" y="-1194.5" font-family="Arial" font-size="15.00">sentry_http_client</text>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/http_client/sentry_http_client.dart -->
<g id="edge246" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/http_client/sentry_http_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5307.61,-1251.7C5305.71,-1243.98 5304.16,-1234.71 5303.28,-1226.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5306.77,-1225.82 5302.55,-1216.1 5299.78,-1226.33 5306.77,-1225.82"/>
</g>
<!-- /lib/src/http_client/sentry_http_client_error.dart -->
<g id="node103" class="node">
<title>/lib/src/http_client/sentry_http_client_error.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5554.5,-1216C5554.5,-1216 5403.5,-1216 5403.5,-1216 5397.5,-1216 5391.5,-1210 5391.5,-1204 5391.5,-1204 5391.5,-1192 5391.5,-1192 5391.5,-1186 5397.5,-1180 5403.5,-1180 5403.5,-1180 5554.5,-1180 5554.5,-1180 5560.5,-1180 5566.5,-1186 5566.5,-1192 5566.5,-1192 5566.5,-1204 5566.5,-1204 5566.5,-1210 5560.5,-1216 5554.5,-1216"/>
<text text-anchor="middle" x="5479" y="-1194.5" font-family="Arial" font-size="15.00">sentry_http_client_error</text>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/http_client/sentry_http_client_error.dart -->
<g id="edge241" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/http_client/sentry_http_client_error.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5355.87,-1251.88C5378.16,-1242.31 5406.12,-1230.3 5429.76,-1220.15"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5431.45,-1223.23 5439.26,-1216.07 5428.69,-1216.8 5431.45,-1223.23"/>
</g>
<!-- /lib/src/utils/tracing_utils.dart -->
<g id="node126" class="node">
<title>/lib/src/utils/tracing_utils.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6842,-148C6842,-148 6770,-148 6770,-148 6764,-148 6758,-142 6758,-136 6758,-136 6758,-124 6758,-124 6758,-118 6764,-112 6770,-112 6770,-112 6842,-112 6842,-112 6848,-112 6854,-118 6854,-124 6854,-124 6854,-136 6854,-136 6854,-142 6848,-148 6842,-148"/>
<text text-anchor="middle" x="6806" y="-126.5" font-family="Arial" font-size="15.00">tracing_utils</text>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/utils/tracing_utils.dart -->
<g id="edge240" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/utils/tracing_utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5392.62,-1252.57C5393.75,-1252.38 5394.88,-1252.19 5396,-1252 5510.05,-1233.02 6429,-1242.62 6429,-1127 6429,-1127 6429,-1127 6429,-285 6429,-172.48 6552.91,-213.54 6662,-186 6699.22,-176.6 6712.45,-192.48 6748,-178 6760.69,-172.83 6772.79,-163.91 6782.63,-155.23"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6785.05,-157.75 6790,-148.38 6780.29,-152.62 6785.05,-157.75"/>
</g>
<!-- /lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge243" class="edge">
<title>/lib/src/http_client/failed_request_client.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5246.88,-1251.94C5187.37,-1232.62 5111,-1194.65 5111,-1127 5111,-1127 5111,-1127 5111,-731 5111,-689.61 5113.44,-641.67 5115.2,-612.35"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5118.72,-612.27 5115.84,-602.07 5111.73,-611.84 5118.72,-612.27"/>
</g>
<!-- /lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge249" class="edge">
<title>/lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5238.77,-1180.71C5237.5,-1180.46 5236.25,-1180.23 5235,-1180 5172.2,-1168.55 4995.08,-1189.2 4950,-1144 4941.55,-1135.53 4939.28,-1105.16 4938.82,-1082.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4942.32,-1081.98 4938.72,-1072.02 4935.32,-1082.05 4942.32,-1081.98"/>
</g>
<!-- /lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/http_client/failed_request_client.dart -->
<g id="edge251" class="edge">
<title>/lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/http_client/failed_request_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5314.34,-1216.1C5316.24,-1223.79 5317.8,-1233.05 5318.7,-1241.67"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5315.21,-1241.98 5319.44,-1251.7 5322.19,-1241.46 5315.21,-1241.98"/>
</g>
<!-- /lib/src/http_client/breadcrumb_client.dart -->
<g id="node101" class="node">
<title>/lib/src/http_client/breadcrumb_client.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5555,-1144C5555,-1144 5439,-1144 5439,-1144 5433,-1144 5427,-1138 5427,-1132 5427,-1132 5427,-1120 5427,-1120 5427,-1114 5433,-1108 5439,-1108 5439,-1108 5555,-1108 5555,-1108 5561,-1108 5567,-1114 5567,-1120 5567,-1120 5567,-1132 5567,-1132 5567,-1138 5561,-1144 5555,-1144"/>
<text text-anchor="middle" x="5497" y="-1122.5" font-family="Arial" font-size="15.00">breadcrumb_client</text>
</g>
<!-- /lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/http_client/breadcrumb_client.dart -->
<g id="edge250" class="edge">
<title>/lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/http_client/breadcrumb_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5352.72,-1179.88C5379.3,-1170.14 5412.76,-1157.87 5440.77,-1147.61"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5442.24,-1150.8 5450.43,-1144.07 5439.83,-1144.22 5442.24,-1150.8"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart -->
<g id="node102" class="node">
<title>/lib/src/http_client/tracing_client.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5371,-1144C5371,-1144 5291,-1144 5291,-1144 5285,-1144 5279,-1138 5279,-1132 5279,-1132 5279,-1120 5279,-1120 5279,-1114 5285,-1108 5291,-1108 5291,-1108 5371,-1108 5371,-1108 5377,-1108 5383,-1114 5383,-1120 5383,-1120 5383,-1132 5383,-1132 5383,-1138 5377,-1144 5371,-1144"/>
<text text-anchor="middle" x="5331" y="-1122.5" font-family="Arial" font-size="15.00">tracing_client</text>
</g>
<!-- /lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/http_client/tracing_client.dart -->
<g id="edge247" class="edge">
<title>/lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/http_client/tracing_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5312.18,-1179.7C5315,-1171.81 5318.39,-1162.3 5321.52,-1153.55"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5324.82,-1154.7 5324.89,-1144.1 5318.23,-1152.34 5324.82,-1154.7"/>
</g>
<!-- /lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge248" class="edge">
<title>/lib/src/http_client/sentry_http_client.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5253.97,-1179.84C5202.38,-1159.15 5131,-1118.97 5131,-1055 5131,-1055 5131,-1055 5131,-731 5131,-689.45 5125.31,-641.56 5121.19,-612.29"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5124.6,-611.43 5119.7,-602.03 5117.67,-612.43 5124.6,-611.43"/>
</g>
<!-- /lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge252" class="edge">
<title>/lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5497,-1107.66C5497,-1059.58 5497,-920.56 5497,-805 5497,-805 5497,-805 5497,-731 5497,-619.87 5523.7,-566.42 5447,-486 5347.31,-381.47 5271.73,-442.61 5132,-406 5009.98,-374.03 4982.85,-350.01 4858,-332 4661.32,-303.63 3203.37,-289.77 2926.37,-287.38"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.31,-283.88 2916.28,-287.29 2926.25,-290.88 2926.31,-283.88"/>
</g>
<!-- /lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge254" class="edge">
<title>/lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5431.46,-1107.96C5418.45,-1104.99 5404.84,-1102.16 5392,-1100 5252.55,-1076.58 5086.75,-1063.92 4999.55,-1058.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4999.77,-1054.92 4989.57,-1057.79 4999.33,-1061.91 4999.77,-1054.92"/>
</g>
<!-- /lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart -->
<g id="edge256" class="edge">
<title>/lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5511.89,-1107.61C5519.35,-1097.82 5527.7,-1084.98 5532,-1072 5544.46,-1034.39 5537,-1022.62 5537,-983 5537,-983 5537,-983 5537,-731 5537,-626.33 5468.04,-334.07 5542,-260 5613.89,-188.01 6358.97,-214.35 6454,-178 6467.71,-172.76 6481.02,-163.62 6491.85,-154.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6494.28,-157.32 6499.61,-148.16 6489.73,-151.99 6494.28,-157.32"/>
</g>
<!-- /lib/src/utils/url_details.dart -->
<g id="node121" class="node">
<title>/lib/src/utils/url_details.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6992,-76C6992,-76 6932,-76 6932,-76 6926,-76 6920,-70 6920,-64 6920,-64 6920,-52 6920,-52 6920,-46 6926,-40 6932,-40 6932,-40 6992,-40 6992,-40 6998,-40 7004,-46 7004,-52 7004,-52 7004,-64 7004,-64 7004,-70 6998,-76 6992,-76"/>
<text text-anchor="middle" x="6962" y="-54.5" font-family="Arial" font-size="15.00">url_details</text>
</g>
<!-- /lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/utils/url_details.dart -->
<g id="edge255" class="edge">
<title>/lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/utils/url_details.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5542.79,-1108C5552.03,-1105 5561.76,-1102.16 5571,-1100 5762.69,-1055.29 6449,-1179.84 6449,-983 6449,-983 6449,-983 6449,-803 6449,-491.39 6499.19,-316.85 6782,-186 6805.21,-175.26 6994.24,-196.39 7012,-178 7032.38,-156.9 7022.83,-139.26 7012,-112 7007.71,-101.2 7000.06,-91.32 6992.01,-83.1"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6994.25,-80.4 6984.6,-76.04 6989.42,-85.47 6994.25,-80.4"/>
</g>
<!-- /lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge253" class="edge">
<title>/lib/src/http_client/breadcrumb_client.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5494.3,-1107.61C5488.79,-1076.46 5473.99,-1010.11 5442,-964 5356.48,-840.76 5263.65,-875.18 5181,-750 5152.95,-707.51 5169.06,-686.8 5149,-640 5144.8,-630.2 5139.22,-619.98 5133.9,-611.04"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5136.73,-608.96 5128.51,-602.26 5130.76,-612.62 5136.73,-608.96"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/sentry_trace_origins.dart -->
<g id="edge260" class="edge">
<title>/lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/sentry_trace_origins.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5377.23,-1107.91C5393.81,-1099.54 5411.08,-1087.79 5422,-1072 5444.82,-1039.01 5437,-1023.11 5437,-983 5437,-983 5437,-983 5437,-877 5437,-804.52 5455.65,-785.18 5442,-714 5429.41,-648.31 5395.62,-576.75 5376.22,-539.27"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5379.14,-537.29 5371.39,-530.07 5372.94,-540.55 5379.14,-537.29"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge261" class="edge">
<title>/lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5295.21,-1107.95C5288.57,-1105.1 5281.63,-1102.31 5275,-1100 5227.77,-1083.52 5208.71,-1098.03 5166,-1072 5012.51,-978.46 5057.02,-851.87 4897,-770 4819.83,-730.52 4783.21,-782.85 4703,-750 4679.3,-740.29 4680.84,-725.59 4658,-714 4605.51,-687.36 4538.63,-672.46 4496.3,-665.08"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4496.79,-661.61 4486.35,-663.41 4495.63,-668.52 4496.79,-661.61"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge259" class="edge">
<title>/lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5350.09,-1107.77C5375.42,-1082.91 5417,-1034.3 5417,-983 5417,-983 5417,-983 5417,-877 5417,-840.71 5443.52,-746.31 5427,-714 5423.63,-707.41 5217.35,-561.8 5211,-558 5013.79,-440.07 4969.29,-386.23 4746,-332 4564.41,-287.9 3194.45,-286.74 2926.41,-286.94"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.26,-283.44 2916.26,-286.95 2926.26,-290.44 2926.26,-283.44"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge258" class="edge">
<title>/lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5297.25,-1108C5290.05,-1104.93 5282.39,-1102.05 5275,-1100 5176.2,-1072.57 5147.54,-1086.27 5046,-1072 5031.02,-1069.89 5014.89,-1067.43 4999.81,-1065.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4999.99,-1061.53 4989.56,-1063.42 4998.89,-1068.45 4999.99,-1061.53"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart -->
<g id="edge263" class="edge">
<title>/lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5383.12,-1113.22C5407.18,-1105.3 5434.27,-1092.44 5452,-1072 5480.34,-1039.33 5477,-922.25 5477,-879 5477,-879 5477,-879 5477,-583 5477,-539.83 5478.89,-528.62 5472,-486 5462.66,-428.2 5437,-417.55 5437,-359 5437,-359 5437,-359 5437,-285 5437,-217.89 5488.24,-209.76 5551,-186 5597.92,-168.23 6406.46,-194.01 6454,-178 6468.12,-173.24 6481.65,-164 6492.53,-154.95"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6495.08,-157.38 6500.3,-148.16 6490.47,-152.11 6495.08,-157.38"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/utils/tracing_utils.dart -->
<g id="edge262" class="edge">
<title>/lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/utils/tracing_utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5383.08,-1110.02C5415.81,-1099.57 5453.88,-1085.21 5465,-1072 5482.45,-1051.27 5517,-949.07 5517,-805 5517,-805 5517,-805 5517,-731 5517,-609.48 5505.33,-579.47 5502,-458 5500.46,-402.02 5482.94,-384.66 5502,-332 5516.18,-292.83 5525.07,-279.27 5562,-260 5732.46,-171.07 6232.03,-196.6 6424,-186 6459.96,-184.01 6714.21,-190.44 6748,-178 6761.05,-173.19 6773.35,-164.16 6783.24,-155.28"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6785.77,-157.71 6790.62,-148.28 6780.95,-152.63 6785.77,-157.71"/>
</g>
<!-- /lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge257" class="edge">
<title>/lib/src/http_client/tracing_client.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5303.5,-1107.89C5257.75,-1078.45 5171,-1018 5171,-983 5171,-983 5171,-983 5171,-877 5171,-770.52 5161.27,-743.43 5136,-640 5133.7,-630.59 5130.46,-620.51 5127.3,-611.58"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5130.55,-610.28 5123.83,-602.09 5123.98,-612.69 5130.55,-610.28"/>
</g>
<!-- /lib/src/noop_sentry_span.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge266" class="edge">
<title>/lib/src/noop_sentry_span.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4275.74,-355.27C3997.43,-348.3 2980.48,-321.85 2837,-304 2744.96,-292.55 2725.21,-269.98 2633,-260 2616.12,-258.17 199.99,-264.03 188,-252 167.29,-231.22 169.82,-209.02 188,-186 211.74,-155.94 322.75,-140.54 381.81,-134.36"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="382.26,-137.83 391.86,-133.34 381.56,-130.86 382.26,-137.83"/>
</g>
<!-- /lib/src/noop_sentry_span.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge265" class="edge">
<title>/lib/src/noop_sentry_span.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4350.4,-376.16C4357.72,-429.67 4366.78,-592.3 4399,-632 4399.19,-632.24 4405.75,-634.82 4414.36,-638.24"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4413.29,-641.59 4423.88,-642.05 4415.89,-635.09 4413.29,-641.59"/>
</g>
<!-- /lib/src/noop_sentry_span.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge264" class="edge">
<title>/lib/src/noop_sentry_span.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4296.3,-339.92C4285.14,-336.73 4273.26,-333.83 4262,-332 3996.1,-288.84 3133.81,-286.77 2926.25,-286.92"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.02,-283.42 2916.02,-286.93 2926.02,-290.42 2926.02,-283.42"/>
</g>
<!-- /lib/src/sentry_tracer_finish_status.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge267" class="edge">
<title>/lib/src/sentry_tracer_finish_status.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5803.64,-75.56C5805.1,-75.71 5806.56,-75.86 5808,-76 5874.83,-82.48 6961.6,-72.64 7016,-112 7050.85,-137.22 7049,-159.98 7049,-203 7049,-659 7049,-659 7049,-659 7049,-699.96 7057.04,-746.85 7062.92,-775.7"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7059.57,-776.75 7065.05,-785.82 7066.42,-775.31 7059.57,-776.75"/>
</g>
<!-- /lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/throwable_mechanism.dart -->
<g id="edge272" class="edge">
<title>/lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/throwable_mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5299.84,-713.83C5283.64,-650.94 5218.51,-431.71 5076,-332 5054.84,-317.2 5029.02,-307.26 5004.15,-300.59"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5004.86,-297.16 4994.3,-298.1 5003.14,-303.95 5004.86,-297.16"/>
</g>
<!-- /lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge271" class="edge">
<title>/lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5236.79,-713.8C5232.93,-711.52 5229.29,-708.93 5226,-706 5201.93,-684.58 5210.15,-668.95 5196,-640 5177.92,-603.03 5187.94,-579.73 5153,-558 5077.99,-511.35 3734.78,-511.97 3416.31,-512.78"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.29,-509.28 3406.3,-512.8 3416.31,-516.28 3416.29,-509.28"/>
</g>
<!-- /lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/integration.dart -->
<g id="edge269" class="edge">
<title>/lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5215.13,-713.94C5142.65,-700.02 5042.69,-680.83 4982.12,-669.2"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4982.5,-665.71 4972.02,-667.26 4981.18,-672.58 4982.5,-665.71"/>
</g>
<!-- /lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge270" class="edge">
<title>/lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5282.75,-713.91C5280.02,-711.37 5277.37,-708.7 5275,-706 5223.16,-646.86 5242.16,-603.48 5178,-558 5027.09,-451.03 4912.68,-580.4 4774,-458 4729.3,-418.55 4776.22,-367.06 4728,-332 4690.42,-304.68 3206.97,-289.94 2926.5,-287.4"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.31,-283.9 2916.28,-287.31 2926.25,-290.9 2926.31,-283.9"/>
</g>
<!-- /lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge268" class="edge">
<title>/lib/src/run_zoned_guarded_integration.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5217.3,-713.82C5213.3,-711.52 5209.5,-708.93 5206,-706 5181.29,-685.32 5194.81,-666.16 5176,-640 5167.79,-628.59 5156.94,-617.66 5146.79,-608.61"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5149.08,-605.95 5139.22,-602.06 5144.5,-611.25 5149.08,-605.95"/>
</g>
<!-- /lib/src/transport/noop_transport.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge274" class="edge">
<title>/lib/src/transport/noop_transport.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1318.19,-785.87C1329.11,-779.65 1342.22,-773.34 1355,-770 1393.56,-759.94 2042.89,-777.22 2072,-750 2085.7,-737.19 2098.66,-431.97 2104,-414 2115.73,-374.55 2112.41,-354.3 2147,-332 2192.46,-302.69 2579.02,-307.41 2633,-304 2703.77,-299.54 2785.86,-293.8 2835.59,-290.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.96,-293.74 2845.69,-289.54 2835.47,-286.76 2835.96,-293.74"/>
</g>
<!-- /lib/src/transport/transport.dart -->
<g id="node111" class="node">
<title>/lib/src/transport/transport.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1338.5,-750C1338.5,-750 1285.5,-750 1285.5,-750 1279.5,-750 1273.5,-744 1273.5,-738 1273.5,-738 1273.5,-726 1273.5,-726 1273.5,-720 1279.5,-714 1285.5,-714 1285.5,-714 1338.5,-714 1338.5,-714 1344.5,-714 1350.5,-720 1350.5,-726 1350.5,-726 1350.5,-738 1350.5,-738 1350.5,-744 1344.5,-750 1338.5,-750"/>
<text text-anchor="middle" x="1312" y="-728.5" font-family="Arial" font-size="15.00">transport</text>
</g>
<!-- /lib/src/transport/noop_transport.dart&#45;&gt;/lib/src/transport/transport.dart -->
<g id="edge275" class="edge">
<title>/lib/src/transport/noop_transport.dart&#45;&gt;/lib/src/transport/transport.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1296.94,-785.7C1299.17,-777.9 1301.85,-768.51 1304.33,-759.83"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1307.73,-760.68 1307.11,-750.1 1301,-758.76 1307.73,-760.68"/>
</g>
<!-- /lib/src/transport/noop_transport.dart&#45;&gt;/lib/src/sentry_envelope.dart -->
<g id="edge273" class="edge">
<title>/lib/src/transport/noop_transport.dart&#45;&gt;/lib/src/sentry_envelope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1318.19,-785.85C1329.11,-779.63 1342.21,-773.32 1355,-770 1441.07,-747.68 2066.74,-760.75 2155,-750 2156.26,-749.85 2157.53,-749.68 2158.81,-749.51"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2159.37,-752.96 2168.72,-747.98 2158.3,-746.04 2159.37,-752.96"/>
</g>
<!-- /lib/src/transport/rate_limit_parser.dart -->
<g id="node108" class="node">
<title>/lib/src/transport/rate_limit_parser.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1137,-676C1137,-676 1033,-676 1033,-676 1027,-676 1021,-670 1021,-664 1021,-664 1021,-652 1021,-652 1021,-646 1027,-640 1033,-640 1033,-640 1137,-640 1137,-640 1143,-640 1149,-646 1149,-652 1149,-652 1149,-664 1149,-664 1149,-670 1143,-676 1137,-676"/>
<text text-anchor="middle" x="1085" y="-654.5" font-family="Arial" font-size="15.00">rate_limit_parser</text>
</g>
<!-- /lib/src/transport/rate_limit_parser.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge276" class="edge">
<title>/lib/src/transport/rate_limit_parser.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1090.2,-639.74C1098.39,-612.98 1113.99,-563.91 1120,-558 1125.43,-552.66 1157.11,-541.91 1188.3,-532.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1189.4,-535.59 1197.94,-529.31 1187.35,-528.89 1189.4,-535.59"/>
</g>
<!-- /lib/src/transport/rate_limit.dart -->
<g id="node114" class="node">
<title>/lib/src/transport/rate_limit.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1194.5,-602C1194.5,-602 1141.5,-602 1141.5,-602 1135.5,-602 1129.5,-596 1129.5,-590 1129.5,-590 1129.5,-578 1129.5,-578 1129.5,-572 1135.5,-566 1141.5,-566 1141.5,-566 1194.5,-566 1194.5,-566 1200.5,-566 1206.5,-572 1206.5,-578 1206.5,-578 1206.5,-590 1206.5,-590 1206.5,-596 1200.5,-602 1194.5,-602"/>
<text text-anchor="middle" x="1168" y="-580.5" font-family="Arial" font-size="15.00">rate_limit</text>
</g>
<!-- /lib/src/transport/rate_limit_parser.dart&#45;&gt;/lib/src/transport/rate_limit.dart -->
<g id="edge277" class="edge">
<title>/lib/src/transport/rate_limit_parser.dart&#45;&gt;/lib/src/transport/rate_limit.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1104.67,-639.94C1115.34,-630.68 1128.71,-619.09 1140.4,-608.94"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1142.92,-611.39 1148.18,-602.19 1138.34,-606.1 1142.92,-611.39"/>
</g>
<!-- /lib/src/transport/noop_encode.dart -->
<g id="node109" class="node">
<title>/lib/src/transport/noop_encode.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1222.5,-750C1222.5,-750 1139.5,-750 1139.5,-750 1133.5,-750 1127.5,-744 1127.5,-738 1127.5,-738 1127.5,-726 1127.5,-726 1127.5,-720 1133.5,-714 1139.5,-714 1139.5,-714 1222.5,-714 1222.5,-714 1228.5,-714 1234.5,-720 1234.5,-726 1234.5,-726 1234.5,-738 1234.5,-738 1234.5,-744 1228.5,-750 1222.5,-750"/>
<text text-anchor="middle" x="1181" y="-728.5" font-family="Arial" font-size="15.00">noop_encode</text>
</g>
<!-- /lib/src/transport/transport.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge279" class="edge">
<title>/lib/src/transport/transport.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1350.75,-714.74C1351.83,-714.47 1352.92,-714.22 1354,-714 1395.37,-705.49 1693.52,-718.03 1734,-706 1787.2,-690.19 1788.31,-660.2 1840,-640 1857.7,-633.08 1867.12,-643.81 1882,-632 1996.24,-541.33 1894.58,-414.28 2015,-332 2071.75,-293.22 2564.37,-307.91 2633,-304 2703.8,-299.97 2785.88,-294.11 2835.6,-290.43"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.98,-293.91 2845.7,-289.68 2835.47,-286.93 2835.98,-293.91"/>
</g>
<!-- /lib/src/transport/transport.dart&#45;&gt;/lib/src/sentry_envelope.dart -->
<g id="edge278" class="edge">
<title>/lib/src/transport/transport.dart&#45;&gt;/lib/src/sentry_envelope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1350.63,-732C1619.87,-732 1889.11,-732 2158.35,-732"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2158.74,-735.5 2168.74,-732 2158.74,-728.5 2158.74,-735.5"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/sentry_envelope_item.dart -->
<g id="edge283" class="edge">
<title>/lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/sentry_envelope_item.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1109.73,-715.59C1112.51,-714.97 1115.28,-714.43 1118,-714 1225.23,-697.07 1986.86,-715.49 2095,-706 2158.45,-700.43 2229.21,-688.44 2283.59,-677.97"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2284.4,-681.38 2293.55,-676.03 2283.07,-674.51 2284.4,-681.38"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge281" class="edge">
<title>/lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1109.73,-715.59C1112.51,-714.97 1115.28,-714.43 1118,-714 1130.64,-712 2028.76,-713.66 2039,-706 2094.06,-664.82 2029.5,-599.91 2084,-558 2085.5,-556.85 3020.21,-524.39 3281.73,-515.33"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3281.98,-518.82 3291.85,-514.98 3281.73,-511.83 3281.98,-518.82"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/transport/rate_limit_parser.dart -->
<g id="edge280" class="edge">
<title>/lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/transport/rate_limit_parser.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1068.98,-713.94C1071.4,-705.63 1074.37,-695.44 1077.09,-686.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1080.47,-687.02 1079.91,-676.44 1073.75,-685.06 1080.47,-687.02"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge285" class="edge">
<title>/lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1109.52,-716.1C1112.38,-715.34 1115.22,-714.64 1118,-714 1142.08,-708.5 1152.61,-719.94 1173,-706 1228.59,-668.01 1246.14,-583.9 1251.6,-540.34"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1255.1,-540.52 1252.74,-530.19 1248.14,-539.74 1255.1,-540.52"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/transport/rate_limit.dart -->
<g id="edge284" class="edge">
<title>/lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/transport/rate_limit.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1109.57,-716.28C1112.41,-715.47 1115.24,-714.71 1118,-714 1135.56,-709.49 1145.94,-719.53 1158,-706 1180.53,-680.72 1178.72,-639.51 1174.25,-612.32"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1177.65,-611.49 1172.37,-602.31 1170.77,-612.78 1177.65,-611.49"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart -->
<g id="edge286" class="edge">
<title>/lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1109.74,-715.61C1112.51,-714.99 1115.28,-714.44 1118,-714 1131.77,-711.77 1610.22,-715.94 1620,-706 1711.09,-613.45 1668.29,-530.03 1610,-414 1604,-402.05 1594.27,-391.37 1584.42,-382.66"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1586.49,-379.83 1576.57,-376.12 1582.01,-385.21 1586.49,-379.83"/>
</g>
<!-- /lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/sentry_envelope.dart -->
<g id="edge282" class="edge">
<title>/lib/src/transport/rate_limiter.dart&#45;&gt;/lib/src/sentry_envelope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1097.78,-750.11C1107,-754.18 1117.16,-757.91 1127,-760 1194.28,-774.28 1677.25,-761.98 1746,-760 1892.51,-755.78 2063.57,-744.43 2158.66,-737.54"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2159.03,-741.03 2168.75,-736.81 2158.52,-734.04 2159.03,-741.03"/>
</g>
<!-- /lib/src/transport/encode.dart -->
<g id="node113" class="node">
<title>/lib/src/transport/encode.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1202.5,-822C1202.5,-822 1161.5,-822 1161.5,-822 1155.5,-822 1149.5,-816 1149.5,-810 1149.5,-810 1149.5,-798 1149.5,-798 1149.5,-792 1155.5,-786 1161.5,-786 1161.5,-786 1202.5,-786 1202.5,-786 1208.5,-786 1214.5,-792 1214.5,-798 1214.5,-798 1214.5,-810 1214.5,-810 1214.5,-816 1208.5,-822 1202.5,-822"/>
<text text-anchor="middle" x="1182" y="-800.5" font-family="Arial" font-size="15.00">encode</text>
</g>
<!-- /lib/src/transport/rate_limit.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge287" class="edge">
<title>/lib/src/transport/rate_limit.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1189.26,-565.7C1200.09,-556.88 1213.42,-546.03 1225.16,-536.47"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1227.44,-539.13 1232.99,-530.1 1223.02,-533.7 1227.44,-539.13"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge294" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1102.32,-786C1113.48,-779.72 1126.91,-773.32 1140,-770 1190.36,-757.21 2034.08,-782.01 2075,-750 2144.63,-695.53 2057.79,-613.01 2127,-558 2129.85,-555.74 3025.85,-524.28 3281.59,-515.35"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3281.98,-518.84 3291.85,-514.99 3281.73,-511.84 3281.98,-518.84"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/noop_client.dart -->
<g id="edge292" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/noop_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1102.32,-786C1113.48,-779.72 1126.91,-773.33 1140,-770 1189.28,-757.47 2015.9,-782.51 2055,-750 2122.02,-694.27 2031.45,-617.66 2095,-558 2205.07,-454.65 2688,-442.18 2854.41,-440.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2854.93,-444.49 2864.9,-440.93 2854.88,-437.49 2854.93,-444.49"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge293" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1102.67,-785.97C1113.77,-779.76 1127.07,-773.43 1140,-770 1236.78,-744.29 1965.78,-774.32 2039,-706 2069.82,-677.24 2071.5,-360.02 2103,-332 2147.06,-292.8 2574.14,-307.57 2633,-304 2703.78,-299.7 2785.87,-293.92 2835.59,-290.33"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2835.97,-293.81 2845.69,-289.59 2835.47,-286.83 2835.97,-293.81"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/noop_encode.dart -->
<g id="edge291" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/noop_encode.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1101.69,-785.88C1115.29,-776.81 1132.17,-765.55 1146.86,-755.76"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1148.84,-758.65 1155.22,-750.19 1144.95,-752.82 1148.84,-758.65"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge290" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1104.86,-785.91C1115.59,-780.15 1128.07,-774.14 1140,-770 1184.06,-754.72 1212.51,-785.28 1243,-750 1269.51,-719.32 1261.75,-595.66 1256.75,-540.18"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1260.22,-539.67 1255.8,-530.04 1253.25,-540.32 1260.22,-539.67"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/transport.dart -->
<g id="edge296" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/transport.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1104.48,-785.91C1115.28,-780.08 1127.9,-774.03 1140,-770 1190.89,-753.06 1206.89,-762.67 1259,-750 1260.47,-749.64 1261.95,-749.27 1263.45,-748.87"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1264.71,-752.16 1273.4,-746.09 1262.82,-745.42 1264.71,-752.16"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/rate_limiter.dart -->
<g id="edge297" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/transport/rate_limiter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1073.03,-785.7C1071.71,-777.98 1070.12,-768.71 1068.65,-760.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1072.07,-759.37 1066.93,-750.1 1065.17,-760.55 1072.07,-759.37"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart -->
<g id="edge288" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1103.38,-785.93C1114.37,-779.88 1127.4,-773.67 1140,-770 1243.31,-739.89 1550.52,-788.17 1620,-706 1638.94,-683.6 1638.84,-662.48 1620,-640 1606.95,-624.43 1594.16,-638.74 1575,-632 1557.45,-625.83 1539.21,-616.36 1524.07,-607.53"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1525.45,-604.28 1515.07,-602.16 1521.86,-610.29 1525.45,-604.28"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart -->
<g id="edge289" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1102.67,-785.99C1113.78,-779.79 1127.08,-773.46 1140,-770 1231.77,-745.45 1476.75,-779.65 1567,-750 1600.49,-739 1608.98,-731.81 1633,-706 1700.08,-633.93 1690.37,-579.33 1659,-486 1647.79,-452.63 1648.85,-440.78 1626,-414 1621.84,-409.12 1619.24,-409.7 1614,-406 1603.29,-398.44 1591.74,-389.93 1581.49,-382.26"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1583.33,-379.26 1573.24,-376.04 1579.12,-384.85 1583.33,-379.26"/>
</g>
<!-- /lib/src/transport/http_transport.dart&#45;&gt;/lib/src/sentry_envelope.dart -->
<g id="edge295" class="edge">
<title>/lib/src/transport/http_transport.dart&#45;&gt;/lib/src/sentry_envelope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1102.32,-785.99C1113.48,-779.71 1126.91,-773.32 1140,-770 1247.19,-742.85 2024.94,-760.69 2135,-750 2142.72,-749.25 2150.74,-748.23 2158.73,-747.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2159.44,-750.5 2168.8,-745.52 2158.38,-743.58 2159.44,-750.5"/>
</g>
<!-- /lib/src/recursive_exception_cause_extractor.dart -->
<g id="node116" class="node">
<title>/lib/src/recursive_exception_cause_extractor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4707.5,-376C4707.5,-376 4466.5,-376 4466.5,-376 4460.5,-376 4454.5,-370 4454.5,-364 4454.5,-364 4454.5,-352 4454.5,-352 4454.5,-346 4460.5,-340 4466.5,-340 4466.5,-340 4707.5,-340 4707.5,-340 4713.5,-340 4719.5,-346 4719.5,-352 4719.5,-352 4719.5,-364 4719.5,-364 4719.5,-370 4713.5,-376 4707.5,-376"/>
<text text-anchor="middle" x="4587" y="-354.5" font-family="Arial" font-size="15.00">recursive_exception_cause_extractor</text>
</g>
<!-- /lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/throwable_mechanism.dart -->
<g id="edge299" class="edge">
<title>/lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/throwable_mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4665.85,-339.97C4713.07,-329.76 4773.24,-316.76 4822.14,-306.2"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4823.05,-309.58 4832.08,-304.05 4821.57,-302.74 4823.05,-309.58"/>
</g>
<!-- /lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge298" class="edge">
<title>/lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4537.23,-376.03C4505.48,-386.25 4463.25,-398.65 4425,-406 4047.93,-478.45 3586.4,-502.16 3416.18,-508.76"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3415.97,-505.27 3406.11,-509.14 3416.24,-512.26 3415.97,-505.27"/>
</g>
<!-- /lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/exception_cause.dart -->
<g id="edge300" class="edge">
<title>/lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/exception_cause.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4719.5,-351.42C5052.46,-337.4 5916.04,-301.02 6174.72,-290.13"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6174.91,-293.62 6184.76,-289.71 6174.62,-286.63 6174.91,-293.62"/>
</g>
<!-- /lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge301" class="edge">
<title>/lib/src/recursive_exception_cause_extractor.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4499.04,-339.98C4479.66,-336.81 4459.19,-333.9 4440,-332 4134.8,-301.79 3149.01,-289.81 2926.09,-287.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.09,-283.95 2916.06,-287.35 2926.02,-290.95 2926.09,-283.95"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/propagation_context.dart -->
<g id="edge304" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/propagation_context.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3054.95,-785.91C3046.84,-744 3021.76,-634.02 2970,-558 2932.07,-502.29 2883.81,-519.39 2856,-458 2845.33,-434.44 2854.94,-405.58 2865.18,-385.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2868.39,-386.54 2870.02,-376.08 2862.21,-383.24 2868.39,-386.54"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge308" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3063.21,-785.98C3074.63,-749.34 3102.26,-664.35 3120,-640 3123.35,-635.4 3126.14,-636.18 3130,-632 3156.91,-602.83 3147.66,-581.01 3180,-558 3209.71,-536.86 3248.73,-525.65 3281.79,-519.71"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3282.56,-523.13 3291.84,-518.03 3281.4,-516.23 3282.56,-523.13"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge303" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3086.04,-792.18C3152.6,-766.48 3320.11,-701.81 3394.47,-673.1"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3395.84,-676.33 3403.9,-669.46 3393.31,-669.8 3395.84,-676.33"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge305" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3048.81,-785.78C3043.75,-775.62 3037.75,-762.37 3034,-750 2995.88,-624.13 3006.98,-587.84 2986,-458 2976.96,-402.02 2998.98,-378.13 2966,-332 2956.11,-318.16 2940.63,-308.21 2925.58,-301.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.89,-298 2916.32,-297.29 2924.14,-304.44 2926.89,-298"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="edge307" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3080.24,-785.9C3128.14,-750.28 3245.97,-669.03 3360,-640 3419.01,-624.98 5493.51,-648.93 5552,-632 5568.59,-627.2 5585.07,-617.56 5598.33,-608.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5600.55,-610.96 5606.56,-602.23 5596.42,-605.31 5600.55,-610.96"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/sentry_tracer.dart -->
<g id="edge310" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/sentry_tracer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3044.86,-785.9C3003.92,-732.82 2879.98,-573.53 2855,-558 2762.85,-500.71 2011.08,-354.79 1905,-332 1739.08,-296.35 1698.9,-276.61 1530,-260 1521.38,-259.15 289.1,-258.14 283,-252 262.32,-231.19 262.27,-206.76 283,-186 296.21,-172.77 5649.28,-183.97 5667,-178 5680.97,-173.29 5694.3,-164.05 5705,-155"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5707.5,-157.46 5712.63,-148.19 5702.84,-152.24 5707.5,-157.46"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/sentry_span_interface.dart -->
<g id="edge309" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/sentry_span_interface.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3086.03,-797.92C3124.97,-790.97 3197.67,-778.42 3260,-770 3345.74,-758.42 3443.82,-748.22 3513.62,-741.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3514.08,-744.95 3523.7,-740.51 3513.41,-737.98 3514.08,-744.95"/>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/event_processor.dart -->
<g id="edge302" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3086.3,-797.31C3125.24,-789.72 3197.62,-776.5 3260,-770 3450.07,-750.19 3502.62,-775.7 3695.45,-750.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3696.2,-753.48 3705.63,-748.66 3695.25,-746.54 3696.2,-753.48"/>
</g>
<!-- /lib/src/scope_observer.dart -->
<g id="node137" class="node">
<title>/lib/src/scope_observer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5175.5,-304C5175.5,-304 5076.5,-304 5076.5,-304 5070.5,-304 5064.5,-298 5064.5,-292 5064.5,-292 5064.5,-280 5064.5,-280 5064.5,-274 5070.5,-268 5076.5,-268 5076.5,-268 5175.5,-268 5175.5,-268 5181.5,-268 5187.5,-274 5187.5,-280 5187.5,-280 5187.5,-292 5187.5,-292 5187.5,-298 5181.5,-304 5175.5,-304"/>
<text text-anchor="middle" x="5126" y="-282.5" font-family="Arial" font-size="15.00">scope_observer</text>
</g>
<!-- /lib/src/scope.dart&#45;&gt;/lib/src/scope_observer.dart -->
<g id="edge306" class="edge">
<title>/lib/src/scope.dart&#45;&gt;/lib/src/scope_observer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3057.36,-785.92C3056.68,-741.58 3061.25,-621.85 3126,-558 3180.66,-504.1 3209.62,-508.54 3283,-486 3354.94,-463.9 3390.32,-503.85 3450,-458 3467.03,-444.91 3451.98,-425.69 3470,-414 3484.66,-404.49 4710.72,-408.58 4728,-406 4855.48,-386.94 4998.95,-336.49 5073.95,-307.77"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5075.56,-310.9 5083.63,-304.04 5073.04,-304.37 5075.56,-310.9"/>
</g>
<!-- /lib/src/event_processor.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge311" class="edge">
<title>/lib/src/event_processor.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3705.93,-715.98C3702.91,-715.31 3699.92,-714.64 3697,-714 3615.56,-696.1 3519.72,-676.69 3468.23,-666.39"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3468.73,-662.92 3458.24,-664.4 3467.36,-669.79 3468.73,-662.92"/>
</g>
<!-- /lib/src/event_processor.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge312" class="edge">
<title>/lib/src/event_processor.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3770,-713.92C3770,-686.66 3770,-631.64 3770,-585 3770,-585 3770,-585 3770,-439 3770,-390.98 3790.41,-364.43 3755,-332 3724.19,-303.78 3100.83,-290.83 2926.15,-287.75"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.07,-284.24 2916.01,-287.57 2925.95,-291.24 2926.07,-284.24"/>
</g>
<!-- /lib/src/utils/http_sanitizer.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge313" class="edge">
<title>/lib/src/utils/http_sanitizer.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M6500.3,-148.16C6488.52,-159 6471.72,-172.04 6454,-178 6427.09,-187.05 5460.1,-181.94 5432,-186 5326.75,-201.22 5307.2,-236.44 5202,-252 5172.78,-256.32 3248.17,-280.43 2926.39,-284.44"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.01,-280.94 2916.05,-284.57 2926.1,-287.94 2926.01,-280.94"/>
</g>
<!-- /lib/src/utils/http_sanitizer.dart&#45;&gt;/lib/src/utils/url_details.dart -->
<g id="edge314" class="edge">
<title>/lib/src/utils/http_sanitizer.dart&#45;&gt;/lib/src/utils/url_details.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M6570.31,-113.8C6573.24,-113.14 6576.15,-112.54 6579,-112 6723.68,-84.62 6767.39,-109.21 6909.79,-77.34"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6910.8,-80.7 6919.76,-75.06 6909.23,-73.88 6910.8,-80.7"/>
</g>
<!-- /lib/src/utils/url_details.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge315" class="edge">
<title>/lib/src/utils/url_details.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M7004.47,-73.89C7022.48,-82.33 7042.04,-94.73 7054,-112 7077.34,-145.7 7069,-162.01 7069,-203 7069,-659 7069,-659 7069,-659 7069,-699.57 7069,-746.56 7069,-775.54"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7065.5,-775.71 7069,-785.71 7072.5,-775.71 7065.5,-775.71"/>
</g>
<!-- /lib/src/utils/http_header_utils.dart -->
<g id="node122" class="node">
<title>/lib/src/utils/http_header_utils.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6991.5,-148C6991.5,-148 6884.5,-148 6884.5,-148 6878.5,-148 6872.5,-142 6872.5,-136 6872.5,-136 6872.5,-124 6872.5,-124 6872.5,-118 6878.5,-112 6884.5,-112 6884.5,-112 6991.5,-112 6991.5,-112 6997.5,-112 7003.5,-118 7003.5,-124 7003.5,-124 7003.5,-136 7003.5,-136 7003.5,-142 6997.5,-148 6991.5,-148"/>
<text text-anchor="middle" x="6938" y="-126.5" font-family="Arial" font-size="15.00">http_header_utils</text>
</g>
<!-- /lib/src/utils/_web_get_isolate_name.dart -->
<g id="node125" class="node">
<title>/lib/src/utils/_web_get_isolate_name.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M6278,-148C6278,-148 6124,-148 6124,-148 6118,-148 6112,-142 6112,-136 6112,-136 6112,-124 6112,-124 6112,-118 6118,-112 6124,-112 6124,-112 6278,-112 6278,-112 6284,-112 6290,-118 6290,-124 6290,-124 6290,-136 6290,-136 6290,-142 6284,-148 6278,-148"/>
<text text-anchor="middle" x="6201" y="-126.5" font-family="Arial" font-size="15.00">_web_get_isolate_name</text>
</g>
<!-- /lib/src/utils/tracing_utils.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge316" class="edge">
<title>/lib/src/utils/tracing_utils.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M6821.16,-148.03C6831.73,-158.66 6846.81,-171.52 6863,-178 6885.96,-187.19 6955.84,-169.21 6974,-186 7006.68,-216.21 6989,-240.5 6989,-285 6989,-659 6989,-659 6989,-659 6989,-699.99 6983.45,-714.53 7004,-750 7010.74,-761.63 7021.07,-771.77 7031.53,-779.97"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.75,-783.01 7039.87,-786.13 7033.9,-777.38 7029.75,-783.01"/>
</g>
<!-- /lib/src/utils/_io_get_isolate_name.dart -->
<g id="node127" class="node">
<title>/lib/src/utils/_io_get_isolate_name.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5970,-148C5970,-148 5832,-148 5832,-148 5826,-148 5820,-142 5820,-136 5820,-136 5820,-124 5820,-124 5820,-118 5826,-112 5832,-112 5832,-112 5970,-112 5970,-112 5976,-112 5982,-118 5982,-124 5982,-124 5982,-136 5982,-136 5982,-142 5976,-148 5970,-148"/>
<text text-anchor="middle" x="5901" y="-126.5" font-family="Arial" font-size="15.00">_io_get_isolate_name</text>
</g>
<!-- /lib/src/client_reports/client_report.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge318" class="edge">
<title>/lib/src/client_reports/client_report.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1491.43,-494.37C1346.25,-446.58 908.51,-309.2 533,-260 519.64,-258.25 57.45,-261.61 48,-252 -52.91,-149.41 269.01,-133.76 381.6,-131.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="381.89,-134.9 391.82,-131.22 381.76,-127.9 381.89,-134.9"/>
</g>
<!-- /lib/src/client_reports/discarded_event.dart -->
<g id="node130" class="node">
<title>/lib/src/client_reports/discarded_event.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1579,-458C1579,-458 1477,-458 1477,-458 1471,-458 1465,-452 1465,-446 1465,-446 1465,-434 1465,-434 1465,-428 1471,-422 1477,-422 1477,-422 1579,-422 1579,-422 1585,-422 1591,-428 1591,-434 1591,-434 1591,-446 1591,-446 1591,-452 1585,-458 1579,-458"/>
<text text-anchor="middle" x="1528" y="-436.5" font-family="Arial" font-size="15.00">discarded_event</text>
</g>
<!-- /lib/src/client_reports/client_report.dart&#45;&gt;/lib/src/client_reports/discarded_event.dart -->
<g id="edge317" class="edge">
<title>/lib/src/client_reports/client_report.dart&#45;&gt;/lib/src/client_reports/discarded_event.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1537.79,-493.7C1536.35,-485.98 1534.63,-476.71 1533.04,-468.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1536.44,-467.3 1531.18,-458.1 1529.56,-468.58 1536.44,-467.3"/>
</g>
<!-- /lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge319" class="edge">
<title>/lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1417.25,-639.98C1410.6,-637.65 1404.12,-635 1398,-632 1349.5,-608.21 1303.16,-565 1276.69,-537.72"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1278.89,-534.95 1269.45,-530.13 1273.83,-539.78 1278.89,-534.95"/>
</g>
<!-- /lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/client_reports/client_report.dart -->
<g id="edge320" class="edge">
<title>/lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/client_reports/client_report.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1572.61,-639.94C1575.38,-637.62 1577.89,-634.99 1580,-632 1599.01,-605.16 1591.49,-588.81 1580,-558 1577.32,-550.81 1572.91,-544 1568,-537.96"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1570.39,-535.38 1561.12,-530.24 1565.16,-540.04 1570.39,-535.38"/>
</g>
<!-- /lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart -->
<g id="edge321" class="edge">
<title>/lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/client_reports/client_report_recorder.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1504.26,-639.94C1501.95,-631.63 1499.12,-621.44 1496.53,-612.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1499.89,-611.14 1493.84,-602.44 1493.15,-613.01 1499.89,-611.14"/>
</g>
<!-- /lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart -->
<g id="edge322" class="edge">
<title>/lib/src/client_reports/noop_client_report_recorder.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1583.12,-639.76C1586.01,-637.47 1588.67,-634.9 1591,-632 1621.36,-594.19 1617.26,-459.31 1600,-414 1595.67,-402.63 1587.79,-392.12 1579.6,-383.39"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1581.95,-380.79 1572.4,-376.21 1577.01,-385.75 1581.95,-380.79"/>
</g>
<!-- /lib/src/client_reports/discarded_event.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge324" class="edge">
<title>/lib/src/client_reports/discarded_event.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1464.78,-457.15C1421.35,-468.25 1363.83,-482.94 1319.84,-494.18"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1318.93,-490.8 1310.11,-496.67 1320.66,-497.58 1318.93,-490.8"/>
</g>
<!-- /lib/src/client_reports/discarded_event.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart -->
<g id="edge323" class="edge">
<title>/lib/src/client_reports/discarded_event.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1532.99,-421.64C1535.99,-411.19 1539.88,-397.67 1543.28,-385.86"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1546.7,-386.63 1546.1,-376.05 1539.97,-384.69 1546.7,-386.63"/>
</g>
<!-- /lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge325" class="edge">
<title>/lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1530.68,-565.99C1542,-562.75 1554.32,-559.81 1566,-558 1737.2,-531.4 2977.24,-516.8 3281.68,-513.65"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3281.73,-517.15 3291.69,-513.55 3281.65,-510.15 3281.73,-517.15"/>
</g>
<!-- /lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge329" class="edge">
<title>/lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1431.81,-565.97C1398.03,-555.9 1355.13,-543.12 1319.93,-532.64"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1320.81,-529.25 1310.22,-529.75 1318.81,-535.96 1320.81,-529.25"/>
</g>
<!-- /lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/client_reports/client_report.dart -->
<g id="edge326" class="edge">
<title>/lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/client_reports/client_report.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1501.85,-565.7C1508.03,-557.39 1515.54,-547.28 1522.32,-538.14"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1525.14,-540.22 1528.29,-530.1 1519.52,-536.04 1525.14,-540.22"/>
</g>
<!-- /lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/client_reports/discarded_event.dart -->
<g id="edge327" class="edge">
<title>/lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/client_reports/discarded_event.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1483.19,-565.76C1477.45,-545.75 1471,-512.15 1482,-486 1485.23,-478.32 1490.48,-471.3 1496.29,-465.21"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1499.03,-467.43 1503.86,-458 1494.2,-462.37 1499.03,-467.43"/>
</g>
<!-- /lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart -->
<g id="edge328" class="edge">
<title>/lib/src/client_reports/client_report_recorder.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1477.72,-565.88C1457.56,-532.88 1420.57,-459.73 1456,-414 1464.71,-402.75 1474.14,-412.1 1487,-406 1500,-399.83 1513.04,-390.88 1523.93,-382.44"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1526.18,-385.12 1531.79,-376.14 1521.8,-379.66 1526.18,-385.12"/>
</g>
<!-- /lib/src/sentry_stack_trace_factory.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge332" class="edge">
<title>/lib/src/sentry_stack_trace_factory.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3241.42,-376.03C3241.57,-397.15 3240.37,-433.38 3257,-458 3266,-471.33 3279.46,-481.53 3293.35,-489.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3292,-492.5 3302.49,-493.98 3295.21,-486.28 3292,-492.5"/>
</g>
<!-- /lib/src/sentry_stack_trace_factory.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge331" class="edge">
<title>/lib/src/sentry_stack_trace_factory.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3162.71,-339.95C3150.13,-337.22 3137.21,-334.48 3125,-332 3055.83,-317.97 2975.25,-303.47 2926.2,-294.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.68,-291.37 2916.23,-293.09 2925.47,-298.27 2926.68,-291.37"/>
</g>
<!-- /lib/src/sentry_stack_trace_factory.dart&#45;&gt;/lib/src/noop_origin.dart -->
<g id="edge330" class="edge">
<title>/lib/src/sentry_stack_trace_factory.dart&#45;&gt;/lib/src/noop_origin.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3139.49,-347.79C3087.84,-343 3023.51,-337.1 2966,-332 2820.26,-319.08 2781.98,-329.97 2638,-304 2636.72,-303.77 2635.43,-303.52 2634.13,-303.27"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2634.6,-299.79 2624.09,-301.06 2633.1,-306.62 2634.6,-299.79"/>
</g>
<!-- /lib/src/sentry_measurement.dart&#45;&gt;/lib/src/sentry_measurement_unit.dart -->
<g id="edge333" class="edge">
<title>/lib/src/sentry_measurement.dart&#45;&gt;/lib/src/sentry_measurement_unit.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5312.64,-111.88C5327.42,-102.72 5345.79,-91.34 5361.7,-81.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5363.59,-84.43 5370.25,-76.19 5359.91,-78.48 5363.59,-84.43"/>
</g>
<!-- /lib/src/sentry_envelope_header.dart -->
<g id="node135" class="node">
<title>/lib/src/sentry_envelope_header.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2018.5,-676C2018.5,-676 1861.5,-676 1861.5,-676 1855.5,-676 1849.5,-670 1849.5,-664 1849.5,-664 1849.5,-652 1849.5,-652 1849.5,-646 1855.5,-640 1861.5,-640 1861.5,-640 2018.5,-640 2018.5,-640 2024.5,-640 2030.5,-646 2030.5,-652 2030.5,-652 2030.5,-664 2030.5,-664 2030.5,-670 2024.5,-676 2018.5,-676"/>
<text text-anchor="middle" x="1940" y="-654.5" font-family="Arial" font-size="15.00">sentry_envelope_header</text>
</g>
<!-- /lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge337" class="edge">
<title>/lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1934.45,-639.67C1927.46,-619.2 1914.24,-584.56 1897,-558 1822.85,-443.74 1811.02,-394.51 1690,-332 1460.78,-213.6 1371.43,-277.03 1114,-260 1099.78,-259.06 98.04,-262.12 88,-252 67.34,-231.18 69.03,-208.38 88,-186 125.28,-142.02 302.79,-133.17 381.4,-131.42"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="381.88,-134.91 391.81,-131.21 381.74,-127.91 381.88,-134.91"/>
</g>
<!-- /lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/protocol/sdk_version.dart -->
<g id="edge335" class="edge">
<title>/lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/protocol/sdk_version.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1953.49,-639.93C1954.93,-637.39 1956.17,-634.71 1957,-632 1966.67,-600.57 1958.76,-590.84 1957,-558 1949.88,-424.98 2023.81,-349.33 1925,-260 1912.02,-248.26 681.45,-257.97 665,-252 652.06,-247.31 639.95,-238.29 630.25,-229.4"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="632.64,-226.83 623.02,-222.38 627.76,-231.86 632.64,-226.83"/>
</g>
<!-- /lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/protocol/sentry_id.dart -->
<g id="edge334" class="edge">
<title>/lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/protocol/sentry_id.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1941.81,-639.59C1947.34,-575.89 1956.77,-354.32 1833,-260 1818.84,-249.21 567.47,-258.78 551,-252 539.77,-247.38 529.73,-238.74 521.78,-230.12"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="524.25,-227.63 515.08,-222.32 518.94,-232.19 524.25,-227.63"/>
</g>
<!-- /lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart -->
<g id="edge336" class="edge">
<title>/lib/src/sentry_envelope_header.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2030.61,-651.99C2099.75,-648.29 2197.36,-643.34 2283,-640 2345.64,-637.56 2502.97,-641.08 2565,-632 2603.67,-626.34 2646.02,-615.1 2679.38,-605.04"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2680.44,-608.37 2688.98,-602.1 2678.39,-601.68 2680.44,-608.37"/>
</g>
<!-- /lib/src/sentry_isolate.dart&#45;&gt;/lib/src/throwable_mechanism.dart -->
<g id="edge338" class="edge">
<title>/lib/src/sentry_isolate.dart&#45;&gt;/lib/src/throwable_mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5016.99,-1107.8C5022.12,-1077.32 5032.38,-1011.8 5036,-956 5047.5,-778.59 5039.16,-730.46 4996,-558 4973.31,-467.31 4937.61,-362.32 4920.61,-313.94"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4923.81,-312.5 4917.18,-304.24 4917.21,-314.83 4923.81,-312.5"/>
</g>
<!-- /lib/src/sentry_isolate.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge339" class="edge">
<title>/lib/src/sentry_isolate.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5013.81,-1107.95C5012.65,-1017.9 5005.27,-600.27 4968,-558 4807.92,-376.43 4614.54,-578.77 4445,-406 4421.14,-381.68 4451.75,-353.1 4425,-332 4364.68,-284.41 3174.64,-285.96 2926.21,-286.81"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.08,-283.31 2916.1,-286.85 2926.11,-290.31 2926.08,-283.31"/>
</g>
<!-- /lib/src/sentry_isolate.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge341" class="edge">
<title>/lib/src/sentry_isolate.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4995.46,-1107.7C4986.2,-1099.05 4974.84,-1088.45 4964.74,-1079.03"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4967.02,-1076.37 4957.33,-1072.1 4962.25,-1081.49 4967.02,-1076.37"/>
</g>
<!-- /lib/src/sentry_isolate.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge340" class="edge">
<title>/lib/src/sentry_isolate.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5030.71,-1107.81C5053.35,-1082.62 5091,-1033.2 5091,-983 5091,-983 5091,-983 5091,-731 5091,-689.04 5101.54,-641.33 5109.2,-612.2"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5112.63,-612.92 5111.86,-602.35 5105.87,-611.09 5112.63,-612.92"/>
</g>
<!-- /lib/src/scope_observer.dart&#45;&gt;/lib/src/protocol/breadcrumb.dart -->
<g id="edge342" class="edge">
<title>/lib/src/scope_observer.dart&#45;&gt;/lib/src/protocol/breadcrumb.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5068.99,-267.99C5055.96,-264.75 5042.09,-261.81 5029,-260 4990.03,-254.61 4711.93,-265.56 4675,-252 4661.94,-247.21 4649.64,-238.17 4639.76,-229.3"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4642.04,-226.64 4632.38,-222.3 4637.22,-231.72 4642.04,-226.64"/>
</g>
<!-- /lib/src/scope_observer.dart&#45;&gt;/lib/src/protocol/sentry_user.dart -->
<g id="edge343" class="edge">
<title>/lib/src/scope_observer.dart&#45;&gt;/lib/src/protocol/sentry_user.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5129.47,-267.64C5131.56,-257.19 5134.27,-243.67 5136.63,-231.86"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5140.06,-232.54 5138.59,-222.05 5133.2,-231.17 5140.06,-232.54"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge349" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2168.78,-731.59C2063.59,-731.91 1861.96,-729.4 1840,-706 1819.93,-684.61 1821.3,-662.6 1840,-640 1852.11,-625.36 1869.89,-646.64 1882,-632 1902.97,-606.66 1896.32,-587.61 1882,-558 1879.04,-551.88 1694.97,-410.65 1690,-406 1657.61,-375.68 1663.05,-351.1 1623,-332 1571.87,-307.62 1170.48,-308.32 1114,-304 914.51,-288.72 865.76,-271.13 666,-260 657.73,-259.54 75.81,-257.89 70,-252 49.4,-231.12 50.94,-208.3 70,-186 109.81,-139.42 300.03,-132.05 381.66,-131.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="382.01,-134.55 391.97,-130.96 381.94,-127.55 382.01,-134.55"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_item_type.dart -->
<g id="edge346" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_item_type.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2232.23,-713.97C2232.57,-689.04 2233.21,-642.37 2233.62,-612.54"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2237.12,-612.42 2233.76,-602.38 2230.13,-612.33 2237.12,-612.42"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_envelope_item.dart -->
<g id="edge352" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_envelope_item.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2265.65,-713.94C2285.23,-704.01 2310.1,-691.4 2331.07,-680.77"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2332.76,-683.83 2340.1,-676.19 2329.6,-677.59 2332.76,-683.83"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge347" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2224.8,-713.95C2215.69,-694.06 2198.32,-661.18 2175,-640 2169.12,-634.65 2163.18,-638.76 2159,-632 2141.71,-604.02 2136.19,-581.69 2159,-558 2161.45,-555.45 3030.3,-524.34 3281.64,-515.39"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3281.87,-518.89 3291.74,-515.03 3281.62,-511.89 3281.87,-518.89"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart -->
<g id="edge348" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_trace_context_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2295.48,-729.38C2342.65,-726.92 2408.22,-720.86 2464,-706 2509.9,-693.78 2629.53,-638.02 2695.21,-606.64"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2696.99,-609.67 2704.5,-602.19 2693.97,-603.35 2696.99,-609.67"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge345" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2181.05,-713.96C2177.42,-711.63 2174.01,-708.98 2171,-706 2107.05,-642.54 2109,-603.1 2109,-513 2109,-513 2109,-513 2109,-439 2109,-364.48 2669.9,-306.56 2835.5,-291.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2836.19,-294.54 2845.82,-290.13 2835.54,-287.57 2836.19,-294.54"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="edge350" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2295.08,-726.34C2358.7,-721.56 2459.7,-713.74 2547,-706 2846.37,-679.44 2919.84,-655.2 3220,-640 3252.35,-638.36 5520.89,-641 5552,-632 5568.59,-627.2 5585.07,-617.56 5598.33,-608.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5600.55,-610.96 5606.56,-602.23 5596.42,-605.31 5600.55,-610.96"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge353" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2168.73,-731.08C2061.02,-730.63 1851.06,-727.04 1825,-706 1797.3,-683.64 1791.1,-640.63 1790.24,-612.38"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1793.74,-612.31 1790.14,-602.35 1786.74,-612.38 1793.74,-612.31"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/client_reports/client_report.dart -->
<g id="edge344" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/client_reports/client_report.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2168.97,-729.51C2055.3,-726.51 1824.51,-718.91 1790,-706 1739.93,-687.27 1621.16,-584.63 1567.79,-537.13"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1569.89,-534.32 1560.1,-530.27 1565.23,-539.54 1569.89,-534.32"/>
</g>
<!-- /lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_envelope_header.dart -->
<g id="edge351" class="edge">
<title>/lib/src/sentry_envelope.dart&#45;&gt;/lib/src/sentry_envelope_header.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2169,-715.47C2125.02,-704.62 2065.99,-690.06 2018.87,-678.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2019.56,-675.01 2009.01,-676.02 2017.88,-681.81 2019.56,-675.01"/>
</g>
<!-- /lib/src/sentry_sampling_context.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge355" class="edge">
<title>/lib/src/sentry_sampling_context.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4761.05,-565.97C4747.65,-562.78 4733.42,-559.86 4720,-558 4590.51,-540.05 3674.49,-519.79 3416.48,-514.38"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.21,-510.88 3406.14,-514.17 3416.06,-517.88 3416.21,-510.88"/>
</g>
<!-- /lib/src/sentry_sampling_context.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge354" class="edge">
<title>/lib/src/sentry_sampling_context.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4798.8,-602.23C4780.51,-612.66 4754.01,-625.22 4729,-632 4649.14,-653.66 4551.35,-657.25 4496.7,-657.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4496.36,-653.95 4486.36,-657.45 4496.36,-660.95 4496.36,-653.95"/>
</g>
<!-- /lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/throwable_mechanism.dart -->
<g id="edge360" class="edge">
<title>/lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/throwable_mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3415.17,-421.99C3428.3,-419.01 3442.04,-416.17 3455,-414 3965.11,-328.47 4586.02,-298.63 4817.84,-290.02"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4818.06,-293.51 4827.92,-289.65 4817.8,-286.51 4818.06,-293.51"/>
</g>
<!-- /lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge358" class="edge">
<title>/lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3354.9,-458.1C3355.7,-465.79 3355.94,-475.05 3355.6,-483.67"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3352.11,-483.48 3354.92,-493.7 3359.09,-483.96 3352.11,-483.48"/>
</g>
<!-- /lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge357" class="edge">
<title>/lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3354.43,-421.96C3360.76,-398.4 3367.69,-356.08 3345,-332 3316.64,-301.9 3037.75,-291.18 2926.49,-288.08"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.35,-284.57 2916.26,-287.8 2926.16,-291.57 2926.35,-284.57"/>
</g>
<!-- /lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/recursive_exception_cause_extractor.dart -->
<g id="edge356" class="edge">
<title>/lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/recursive_exception_cause_extractor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3410.8,-421.98C3425.2,-418.7 3440.55,-415.74 3455,-414 3562.01,-401.1 4318.35,-421.61 4425,-406 4461.08,-400.72 4500.39,-389.46 4531.25,-379.28"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4532.59,-382.52 4540.96,-376.02 4530.37,-375.88 4532.59,-382.52"/>
</g>
<!-- /lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/sentry_stack_trace_factory.dart -->
<g id="edge359" class="edge">
<title>/lib/src/sentry_exception_factory.dart&#45;&gt;/lib/src/sentry_stack_trace_factory.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3325.19,-421.84C3309.09,-410.24 3287.65,-394.78 3270.06,-382.11"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3271.93,-379.14 3261.77,-376.13 3267.84,-384.82 3271.93,-379.14"/>
</g>
<!-- /lib/src/hub.dart&#45;&gt;/lib/src/propagation_context.dart -->
<g id="edge361" class="edge">
<title>/lib/src/hub.dart&#45;&gt;/lib/src/propagation_context.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5089.65,-576.2C4978.76,-548.99 4551.4,-447.87 4193,-414 3957.82,-391.78 3365.12,-428.89 3130,-406 3073.47,-400.5 3010.63,-388.59 2962.22,-378.14"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2962.94,-374.71 2952.43,-376 2961.45,-381.55 2962.94,-374.71"/>
</g>
<!-- /lib/src/hub.dart&#45;&gt;/lib/src/sentry_traces_sampler.dart -->
<g id="edge366" class="edge">
<title>/lib/src/hub.dart&#45;&gt;/lib/src/sentry_traces_sampler.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5144.04,-579.24C5214.73,-569.4 5407.64,-542.55 5525.02,-526.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5525.67,-529.66 5535.09,-524.82 5524.7,-522.73 5525.67,-529.66"/>
</g>
<!-- /lib/src/hub.dart&#45;&gt;/lib/src/sentry_tracer.dart -->
<g id="edge365" class="edge">
<title>/lib/src/hub.dart&#45;&gt;/lib/src/sentry_tracer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5143.19,-565.84C5190.88,-532.28 5287,-452.98 5287,-359 5287,-359 5287,-359 5287,-285 5287,-215.86 5342.3,-210.37 5407,-186 5461.09,-165.62 5612.5,-197.26 5667,-178 5680.9,-173.09 5694.22,-163.82 5704.93,-154.8"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5707.41,-157.28 5712.57,-148.03 5702.77,-152.04 5707.41,-157.28"/>
</g>
<!-- /lib/src/hub.dart&#45;&gt;/lib/src/transport/data_category.dart -->
<g id="edge362" class="edge">
<title>/lib/src/hub.dart&#45;&gt;/lib/src/transport/data_category.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5089.83,-578.5C5052.09,-572.48 4981.58,-562.09 4921,-558 4139.2,-505.22 2176.98,-580.09 1395,-530 1370.57,-528.43 1343.87,-525.53 1320.35,-522.55"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1320.49,-519.04 1310.13,-521.23 1319.6,-525.98 1320.49,-519.04"/>
</g>
<!-- /lib/src/hub.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart -->
<g id="edge364" class="edge">
<title>/lib/src/hub.dart&#45;&gt;/lib/src/client_reports/discard_reason.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5089.9,-577.86C5060.1,-572.35 5010.78,-563.64 4968,-558 4839.18,-541.02 4806.25,-543.32 4677,-530 4207.33,-481.61 4092.4,-440.76 3621,-414 3593.17,-412.42 1640.44,-414.83 1614,-406 1599.87,-401.28 1586.33,-392.04 1575.46,-382.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1577.52,-380.14 1567.69,-376.19 1572.91,-385.41 1577.52,-380.14"/>
</g>
<!-- /lib/src/hub.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge363" class="edge">
<title>/lib/src/hub.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M5144.14,-588.96C5211.59,-598.59 5392.31,-623.09 5544,-632 5558.28,-632.84 6562.66,-631.27 6574,-640 6614.15,-670.92 6565.54,-717.01 6604,-750 6636.08,-777.52 6923.28,-795.32 7029.63,-801.02"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7029.76,-804.53 7039.93,-801.56 7030.13,-797.54 7029.76,-804.53"/>
</g>
<!-- /lib/src/origin.dart -->
<g id="node142" class="node">
<title>/lib/src/origin.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2656,-1362C2656,-1362 2626,-1362 2626,-1362 2620,-1362 2614,-1356 2614,-1350 2614,-1350 2614,-1338 2614,-1338 2614,-1332 2620,-1326 2626,-1326 2626,-1326 2656,-1326 2656,-1326 2662,-1326 2668,-1332 2668,-1338 2668,-1338 2668,-1350 2668,-1350 2668,-1356 2662,-1362 2656,-1362"/>
<text text-anchor="middle" x="2641" y="-1340.5" font-family="Arial" font-size="15.00">origin</text>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/utils.dart -->
<g id="edge399" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.88,-798.49C7011.47,-792.5 6969.23,-779.06 6946,-750 6920.31,-717.86 6929,-700.14 6929,-659 6929,-659 6929,-659 6929,-285 6929,-270.77 6921.69,-266.44 6909,-260 6739.71,-174.07 5391.8,-190.27 5202,-186 5185.51,-185.63 466.63,-185.62 452,-178 443.17,-173.4 436.2,-165.24 431,-156.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="433.99,-155.16 426.03,-148.17 427.89,-158.6 433.99,-155.16"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_trace_origins.dart -->
<g id="edge398" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_trace_origins.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.79,-802.21C6970.55,-799.7 6799.34,-789.53 6761,-750 6731,-719.07 6766.19,-586.64 6734,-558 6708.92,-535.69 5559.5,-532.12 5526,-530 5500.77,-528.4 5473.42,-525.88 5448.43,-523.27"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5448.55,-519.76 5438.24,-522.18 5447.81,-526.72 5448.55,-519.76"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry.dart -->
<g id="edge374" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.9,-807.88C6967.64,-815.17 6774.26,-835.52 6614,-860 6382.11,-895.43 6326.8,-920.02 6095,-956 5588.17,-1034.68 5458.44,-1032.53 4950,-1100 4901.87,-1106.39 4846.53,-1114.19 4810.28,-1119.36"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4809.53,-1115.93 4800.13,-1120.81 4810.52,-1122.86 4809.53,-1115.93"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_envelope_item.dart -->
<g id="edge376" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_envelope_item.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7056.4,-785.81C7050.89,-779.67 7043.88,-773.42 7036,-770 7034.72,-769.44 5428.4,-714.03 5427,-714 4975.53,-705.26 3846.42,-717.27 3395,-706 3051.98,-697.43 2644.62,-674.99 2465.83,-664.52"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2465.87,-661.02 2455.68,-663.93 2465.46,-668.01 2465.87,-661.02"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_client.dart -->
<g id="edge377" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.84,-807.57C6842.96,-824.74 5687.43,-923.38 4744,-956 4693.63,-957.74 3031.33,-956.1 2877.82,-963.78"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2877.48,-960.29 2867.77,-964.55 2878.01,-967.27 2877.48,-960.29"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_baggage.dart -->
<g id="edge391" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_baggage.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.81,-805.74C6842.72,-810.71 5686.12,-839.37 4744,-852 4204.48,-859.23 4069.48,-850.57 3530,-860 3339.86,-863.32 3115.85,-870.57 3003.29,-874.44"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3002.95,-870.95 2993.08,-874.79 3003.19,-877.95 3002.95,-870.95"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/exception_stacktrace_extractor.dart -->
<g id="edge394" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/exception_stacktrace_extractor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7040,-802.97C6959.88,-802.27 6737.96,-796.14 6685,-750 6646.7,-716.64 6694.25,-670.98 6654,-640 6642.73,-631.33 6155.96,-634.71 6142,-632 6115.19,-626.8 6086.64,-616.1 6063.89,-606.23"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6065.09,-602.93 6054.53,-602.07 6062.24,-609.33 6065.09,-602.93"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/throwable_mechanism.dart -->
<g id="edge379" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/throwable_mechanism.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.9,-800.18C6985.05,-794.26 6869.48,-778.62 6843,-750 6815.21,-719.96 6829,-699.92 6829,-659 6829,-659 6829,-659 6829,-439 6829,-414.99 6831.99,-347.9 6814,-332 6777.38,-299.64 5103.76,-307.26 5055,-304 5038.67,-302.91 5021.35,-301.28 5004.59,-299.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5004.57,-295.92 4994.25,-298.28 5003.79,-302.88 5004.57,-295.92"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/type_check_hint.dart -->
<g id="edge390" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/type_check_hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7080.54,-822.13C7099.94,-852.66 7137,-919.09 7137,-981 7137,-1199 7137,-1199 7137,-1199 7137,-1252.08 7165.61,-1281.58 7127,-1318 7110.36,-1333.7 6436.12,-1340.58 6210.61,-1342.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6210.51,-1338.95 6200.53,-1342.53 6210.56,-1345.95 6210.51,-1338.95"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_options.dart -->
<g id="edge378" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.92,-801.85C6969.11,-798.51 6790.2,-786.56 6745,-750 6704.19,-716.99 6738.09,-678.12 6702,-640 6641.57,-576.16 6607.78,-577.26 6522,-558 6443.56,-540.39 3869.02,-517.47 3416.56,-513.57"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3416.29,-510.07 3406.26,-513.49 3416.23,-517.07 3416.29,-510.07"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/hint.dart -->
<g id="edge389" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/hint.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7056.4,-785.82C7050.88,-779.68 7043.87,-773.43 7036,-770 6980.76,-745.92 6545.37,-781.51 6494,-750 6478.4,-740.43 6489.76,-723.31 6474,-714 6461.69,-706.72 4429.29,-706.37 4415,-706 4045.82,-696.47 3599.36,-669.58 3468.64,-661.39"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3468.58,-657.88 3458.38,-660.75 3468.14,-664.87 3468.58,-657.88"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/platform_checker.dart -->
<g id="edge370" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/platform_checker.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.8,-801.25C6977.86,-796.91 6836.36,-783.48 6804,-750 6775.51,-720.53 6789,-699.99 6789,-659 6789,-659 6789,-659 6789,-511 6789,-422.96 3709.03,-415.51 3621,-414 3126.4,-405.54 1889.42,-421.58 1395,-406 1132.75,-397.73 822.14,-375.65 681.26,-364.96"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="681.25,-361.45 671.01,-364.18 680.71,-368.43 681.25,-361.45"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/tracing.dart -->
<g id="edge388" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/tracing.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7056.39,-785.83C7050.88,-779.69 7043.87,-773.44 7036,-770 6982.81,-746.77 6563.45,-780.39 6514,-750 6498.41,-740.42 6509.75,-723.32 6494,-714 6474.67,-702.56 4899.42,-707.3 4877,-706 4736.78,-697.87 4571.51,-675.8 4496.16,-665.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4496.53,-661.57 4486.14,-663.61 4495.54,-668.5 4496.53,-661.57"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/exception_cause.dart -->
<g id="edge393" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/exception_cause.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7040,-803.63C6998.79,-802.82 6923.88,-794.79 6884,-750 6856.74,-719.39 6869,-699.99 6869,-659 6869,-659 6869,-659 6869,-439 6869,-390.98 6889.4,-364.44 6854,-332 6811.84,-293.37 6412.66,-311.13 6323.44,-304.48"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6323.67,-300.98 6313.36,-303.43 6322.95,-307.94 6323.67,-300.98"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/integration.dart -->
<g id="edge381" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7056.39,-785.85C7050.87,-779.71 7043.86,-773.45 7036,-770 6939.88,-727.78 6663.21,-784.34 6564,-750 6538.12,-741.04 6540.03,-722.51 6514,-714 6488.39,-705.62 5570.92,-707.14 5544,-706 5335.89,-697.2 5088.96,-674.64 4982.36,-664.31"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4982.48,-660.8 4972.19,-663.32 4981.8,-667.77 4982.48,-660.8"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/protocol.dart -->
<g id="edge371" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/protocol.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.94,-800.85C6981.19,-795.87 6851.39,-781.51 6822,-750 6794.13,-720.13 6809,-699.86 6809,-659 6809,-659 6809,-659 6809,-439 6809,-275.23 5502.6,-339.45 5339,-332 4364.85,-287.66 3171.64,-286.62 2926.34,-286.91"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2926.03,-283.41 2916.03,-286.92 2926.04,-290.41 2926.03,-283.41"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart -->
<g id="edge385" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_attachment/sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.68,-801.53C6946.61,-796.58 6662.3,-779.18 6634,-750 6616.91,-732.38 6642.05,-656.63 6624,-640 6614.57,-631.31 5713.29,-635.63 5701,-632 5684.6,-627.15 5668.33,-617.51 5655.25,-608.2"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5657.26,-605.34 5647.14,-602.2 5653.09,-610.96 5657.26,-605.34"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/exception_cause_extractor.dart -->
<g id="edge392" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/exception_cause_extractor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.74,-802.75C6962.15,-801.48 6753.69,-793.97 6704,-750 6666.05,-716.42 6713.89,-671.26 6674,-640 6659.87,-628.93 6368.48,-636.06 6351,-632 6328.74,-626.83 6305.49,-616.54 6286.81,-606.91"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6288.24,-603.7 6277.76,-602.11 6284.96,-609.89 6288.24,-603.7"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/hub_adapter.dart -->
<g id="edge369" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/hub_adapter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.87,-807.9C6806.83,-831.1 5248.28,-987.14 4999.42,-1034.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4998.63,-1031.38 4989.51,-1036.77 5000,-1038.24 4998.63,-1031.38"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_user_feedback.dart -->
<g id="edge386" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_user_feedback.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.69,-802.57C6947.49,-800.8 6664.76,-791.98 6585,-750 6566.32,-740.17 6572.78,-723.65 6554,-714 6390.48,-629.96 5913.65,-648.44 5730,-640 5531.82,-630.9 2356.98,-644.81 2159,-632 2064.77,-625.9 1957.78,-611.37 1883.55,-600.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1883.71,-596.56 1873.29,-598.5 1882.65,-603.47 1883.71,-596.56"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/http_client/sentry_http_client.dart -->
<g id="edge383" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/http_client/sentry_http_client.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.97,-807.73C6996.29,-812.8 6912.07,-826.4 6849,-860 6724.85,-926.14 6742.97,-1015.84 6614,-1072 6364.98,-1180.44 5670.13,-1138.92 5383.08,-1180"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5382.42,-1176.56 5373.04,-1181.5 5383.45,-1183.48 5382.42,-1176.56"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/http_client/sentry_http_client_error.dart -->
<g id="edge384" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/http_client/sentry_http_client_error.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.79,-813.24C6979.94,-832.1 6849,-884.4 6849,-981 6849,-1055 6849,-1055 6849,-1055 6849,-1170.85 6717.73,-1121.94 6604,-1144 6381.49,-1187.17 5814.88,-1155.74 5576.68,-1179.34"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5576.21,-1175.87 5566.63,-1180.39 5576.94,-1182.83 5576.21,-1175.87"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/run_zoned_guarded_integration.dart -->
<g id="edge367" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/run_zoned_guarded_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7056.44,-785.72C7050.93,-779.57 7043.91,-773.34 7036,-770 6996.1,-753.18 5569.42,-753.44 5428.3,-750.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5428.39,-746.57 5418.27,-749.7 5428.13,-753.57 5428.39,-746.57"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/transport/transport.dart -->
<g id="edge380" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/transport/transport.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.8,-802.49C6842.71,-799.1 5686.06,-779.46 4744,-770 4651.81,-769.07 1579.53,-768.3 1360.82,-750.45"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1361.08,-746.96 1350.74,-749.28 1360.27,-753.91 1361.08,-746.96"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/scope.dart -->
<g id="edge372" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/scope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7061.85,-822.05C7055.21,-835.61 7043.8,-852.43 7027,-856 7004.94,-860.68 3801.54,-856.81 3779,-856 3517.44,-846.58 3203.53,-818 3096.16,-807.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3096.42,-804.23 3086.13,-806.76 3095.75,-811.2 3096.42,-804.23"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/event_processor.dart -->
<g id="edge382" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.98,-802.38C6745.72,-796.14 4303.48,-744.32 3844.28,-734.58"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3844.19,-731.07 3834.12,-734.36 3844.05,-738.07 3844.19,-731.07"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart -->
<g id="edge395" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/utils/http_sanitizer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.96,-791.69C7021.62,-783.03 6999.01,-769.27 6986,-750 6962.99,-715.89 6969,-700.14 6969,-659 6969,-659 6969,-659 6969,-285 6969,-262.97 6979.81,-201.34 6964,-186 6948.65,-171.1 6599.21,-185.03 6579,-178 6565.07,-173.16 6551.75,-163.9 6541.05,-154.87"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6543.21,-152.11 6533.41,-148.09 6538.56,-157.34 6543.21,-152.11"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/utils/url_details.dart -->
<g id="edge396" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/utils/url_details.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7072.95,-785.82C7078.78,-758.93 7089,-705.19 7089,-659 7089,-659 7089,-659 7089,-203 7089,-161.39 7092.85,-144.61 7067,-112 7053.43,-94.88 7032.86,-82.81 7013.51,-74.56"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7014.74,-71.28 7004.15,-70.82 7012.14,-77.78 7014.74,-71.28"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/utils/http_header_utils.dart -->
<g id="edge397" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/utils/http_header_utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7059.41,-785.82C7054.12,-775.68 7047.87,-762.43 7044,-750 7031.81,-710.87 7029,-699.99 7029,-659 7029,-659 7029,-659 7029,-285 7029,-240.5 7037.16,-224 7014,-186 7011.21,-181.42 6991.14,-166.97 6972.33,-154.04"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6974.07,-150.99 6963.84,-148.24 6970.13,-156.77 6974.07,-150.99"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/utils/tracing_utils.dart -->
<g id="edge387" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/utils/tracing_utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7053.69,-786C7045.55,-776.12 7036.01,-763.08 7030,-750 7012.67,-712.28 7009,-700.51 7009,-659 7009,-659 7009,-659 7009,-285 7009,-240.11 7022.82,-215.51 6989,-186 6967.86,-167.55 6889.09,-188.31 6863,-178 6850.2,-172.94 6838.12,-163.86 6828.4,-155.03"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="6830.78,-152.46 6821.14,-148.07 6825.94,-157.52 6830.78,-152.46"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/scope_observer.dart -->
<g id="edge373" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/scope_observer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.68,-799.43C6988.61,-792.6 6886.55,-776.01 6863,-750 6835.53,-719.67 6849,-699.92 6849,-659 6849,-659 6849,-659 6849,-439 6849,-414.99 6851.91,-347.99 6834,-332 6818.5,-318.16 5520.23,-294.05 5197.84,-288.27"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5197.75,-284.77 5187.69,-288.09 5197.62,-291.77 5197.75,-284.77"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/sentry_envelope.dart -->
<g id="edge375" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/sentry_envelope.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.98,-802.58C6670.29,-797.23 2882.69,-742.42 2305.56,-734.06"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2305.35,-730.56 2295.3,-733.92 2305.25,-737.56 2305.35,-730.56"/>
</g>
<!-- /lib/sentry.dart&#45;&gt;/lib/src/hub.dart -->
<g id="edge368" class="edge">
<title>/lib/sentry.dart&#45;&gt;/lib/src/hub.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7039.73,-803.47C6951.1,-804.32 6687.79,-802.27 6624,-750 6584.21,-717.39 6629.53,-670.35 6588,-640 6576.29,-631.44 5558.48,-632.85 5544,-632 5399.42,-623.51 5228.47,-600.85 5154.36,-590.41"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5154.54,-586.9 5144.14,-588.96 5153.55,-593.83 5154.54,-586.9"/>
</g>
<!-- /lib/sentry_io.dart -->
<g id="node144" class="node">
<title>/lib/sentry_io.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M7105.5,-1288C7105.5,-1288 7052.5,-1288 7052.5,-1288 7046.5,-1288 7040.5,-1282 7040.5,-1276 7040.5,-1276 7040.5,-1264 7040.5,-1264 7040.5,-1258 7046.5,-1252 7052.5,-1252 7052.5,-1252 7105.5,-1252 7105.5,-1252 7111.5,-1252 7117.5,-1258 7117.5,-1264 7117.5,-1264 7117.5,-1276 7117.5,-1276 7117.5,-1282 7111.5,-1288 7105.5,-1288"/>
<text text-anchor="middle" x="7079" y="-1266.5" font-family="Arial" font-size="15.00">sentry_io</text>
</g>
<!-- /lib/sentry_io.dart&#45;&gt;/lib/src/sentry_isolate_extension.dart -->
<g id="edge402" class="edge">
<title>/lib/sentry_io.dart&#45;&gt;/lib/src/sentry_isolate_extension.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7040.2,-1268.46C6845.46,-1265.63 5955.11,-1251.26 5227,-1216 5178.21,-1213.64 5124.11,-1210.06 5078.84,-1206.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5078.95,-1203.29 5068.72,-1206.06 5078.44,-1210.27 5078.95,-1203.29"/>
</g>
<!-- /lib/sentry_io.dart&#45;&gt;/lib/src/sentry_attachment/io_sentry_attachment.dart -->
<g id="edge401" class="edge">
<title>/lib/sentry_io.dart&#45;&gt;/lib/src/sentry_attachment/io_sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7040.46,-1260.59C6918.49,-1231.49 6549,-1123.19 6549,-879 6549,-879 6549,-879 6549,-803 6549,-710.37 5809.12,-734.55 5721,-706 5703.36,-700.28 5685.23,-690.69 5670.35,-681.66"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5671.87,-678.48 5661.54,-676.15 5668.16,-684.42 5671.87,-678.48"/>
</g>
<!-- /lib/sentry_io.dart&#45;&gt;/lib/src/sentry_isolate.dart -->
<g id="edge403" class="edge">
<title>/lib/sentry_io.dart&#45;&gt;/lib/src/sentry_isolate.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7040.21,-1266.56C6845.52,-1254.27 5955.35,-1197.64 5227,-1144 5177.27,-1140.34 5121.04,-1135.83 5078.85,-1132.38"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5078.96,-1128.88 5068.71,-1131.55 5078.39,-1135.86 5078.96,-1128.88"/>
</g>
<!-- /lib/sentry_io.dart&#45;&gt;/lib/sentry.dart -->
<g id="edge400" class="edge">
<title>/lib/sentry_io.dart&#45;&gt;/lib/sentry.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M7078.01,-1251.96C7076.56,-1225.31 7074,-1172.14 7074,-1127 7074,-1127 7074,-1127 7074,-981 7074,-928.59 7071.76,-867.44 7070.28,-832.77"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="7073.76,-832.32 7069.83,-822.48 7066.77,-832.63 7073.76,-832.32"/>
</g>
</g>
</svg>
