# `package:sentry` example

The example in this directory throws an error and sends it to Sentry.io. Use it
as a source of example code, or to smoke-test your Sentry.io configuration.

To use the example, create a Sentry.io account and get a DSN for your project.
In the `main.dart` file, replace the `dsn` value with the one you got from
Sentry.io. Then run the following command :


```sh
dart pub get
dart run
```
