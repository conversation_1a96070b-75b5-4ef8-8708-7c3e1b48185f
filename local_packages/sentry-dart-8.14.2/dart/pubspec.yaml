name: sentry
version: 8.14.2
description: >
  A crash reporting library for Dart that sends crash reports to Sentry.io.
  This library supports Dart VM and Web. For Flutter consider sentry_flutter instead.
homepage: https://docs.sentry.io/platforms/dart/
repository: https://github.com/getsentry/sentry-dart
issue_tracker: https://github.com/getsentry/sentry-dart/issues
documentation: https://docs.sentry.io/platforms/dart/

environment:
  sdk: '>=2.17.0 <4.0.0'

platforms:
  android:
  ios:
  macos:
  linux:
  windows:
  web:

dependencies:
  http: '>=0.13.0 <2.0.0'
  meta: ^1.3.0
  stack_trace: ^1.10.0
  uuid: '>=3.0.0 <5.0.0'
  collection: ^1.16.0

dev_dependencies:
  build_runner: ^2.3.0
  mockito: ^5.1.0
  lints: '>=2.0.0'
  test: ^1.21.1
  yaml: ^3.1.0 # needed for version match (code and pubspec)
  coverage: ^1.3.0
  intl: '>=0.17.0 <1.0.0'
  version: ^3.0.2
