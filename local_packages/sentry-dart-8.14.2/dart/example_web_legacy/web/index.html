<!DOCTYPE html>

<html>
<head>
    <!--
      If you are serving your web app in a path other than the root, change the
      href value below to reflect the base path you are serving from.

      The path provided below has to start and end with a slash "/" in order for
      it to work correctly.

      For more details:
      * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

      This is a placeholder for base href that will be replaced by the value of
      the `--base-href` argument provided to `flutter build`.
    -->
    <base href="$FLUTTER_BASE_HREF">

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="scaffolded-by" content="https://github.com/dart-lang/stagehand">
    <title>dart_web</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="favicon.ico">

    <style>
      .bloc {
        display: flex;
        margin: 1rem;
      }

      .result {
        padding-left: 1rem;
        color: green;
        display: none;
      }
    </style>

    <script defer src="main.dart.js"></script>
</head>

<body>

<div id="output"></div>

<div class="bloc">
    <button id="btEvent">Capture Event</button>
    <div id="eventResult" class="result">Captured</div>
</div>

<div class="bloc">
    <button id="btMessage">Capture Message</button>
    <div id="messageResult" class="result">Captured</div>
</div>

<div class="bloc">
    <button id="btException">Capture Exception</button>
    <div id="exceptionResult" class="result">Captured</div>
</div>

<div class="bloc">
  <button id="btUnhandledException">Capture Unhandled Exception</button>
  <div id="unhandledResult" class="result">Captured</div>
</div>

</body>
</html>
