include: package:lints/recommended.yaml

analyzer:
  exclude:
    - example/** # the example has its own 'analysis_options.yaml'
    - test/*.mocks.dart
  errors:
    # treat missing required parameters as a warning (not a hint)
    missing_required_param: error
    # treat missing returns as a warning (not a hint)
    missing_return: error
    # allow having TODOs in the code
    todo: ignore
    # allow self-reference to deprecated members (we do this because otherwise we have
    # to annotate every member in every test, assert, etc, when we deprecate something)
    deprecated_member_use_from_same_package: warning

linter:
  rules:
    prefer_relative_imports: true
    unnecessary_brace_in_string_interps: true
    prefer_function_declarations_over_variables: false
    no_leading_underscores_for_local_identifiers: false
    avoid_renaming_method_parameters: false
    unawaited_futures: true
