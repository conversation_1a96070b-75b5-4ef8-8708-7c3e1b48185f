<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Pages: 1 -->
<svg width="5182pt" height="524pt"
 viewBox="0.00 0.00 5182.00 524.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 520)">
<polygon fill="white" stroke="transparent" points="-4,4 -4,-520 5178,-520 5178,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster~</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M20,-8C20,-8 5154,-8 5154,-8 5160,-8 5166,-14 5166,-20 5166,-20 5166,-496 5166,-496 5166,-502 5160,-508 5154,-508 5154,-508 20,-508 20,-508 14,-508 8,-502 8,-496 8,-496 8,-20 8,-20 8,-14 14,-8 20,-8"/>
<text text-anchor="middle" x="2587" y="-493.6" font-family="Arial Black" font-size="13.00">flutter</text>
</g>
<g id="clust2" class="cluster">
<title>cluster~/lib</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M28,-16C28,-16 5146,-16 5146,-16 5152,-16 5158,-22 5158,-28 5158,-28 5158,-466 5158,-466 5158,-472 5152,-478 5146,-478 5146,-478 28,-478 28,-478 22,-478 16,-472 16,-466 16,-466 16,-28 16,-28 16,-22 22,-16 28,-16"/>
<text text-anchor="middle" x="2587" y="-463.6" font-family="Arial Black" font-size="13.00">lib</text>
</g>
<g id="clust3" class="cluster">
<title>cluster~/lib/src</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M184,-24C184,-24 5026,-24 5026,-24 5032,-24 5038,-30 5038,-36 5038,-36 5038,-436 5038,-436 5038,-442 5032,-448 5026,-448 5026,-448 184,-448 184,-448 178,-448 172,-442 172,-436 172,-436 172,-36 172,-36 172,-30 178,-24 184,-24"/>
<text text-anchor="middle" x="2605" y="-433.6" font-family="Arial Black" font-size="13.00">src</text>
</g>
<g id="clust4" class="cluster">
<title>cluster~/lib/src/renderer</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M1466,-32C1466,-32 1839,-32 1839,-32 1845,-32 1851,-38 1851,-44 1851,-44 1851,-176 1851,-176 1851,-182 1845,-188 1839,-188 1839,-188 1466,-188 1466,-188 1460,-188 1454,-182 1454,-176 1454,-176 1454,-44 1454,-44 1454,-38 1460,-32 1466,-32"/>
<text text-anchor="middle" x="1652.5" y="-173.6" font-family="Arial Black" font-size="13.00">renderer</text>
</g>
<g id="clust5" class="cluster">
<title>cluster~/lib/src/screenshot</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M3310,-32C3310,-32 3703,-32 3703,-32 3709,-32 3715,-38 3715,-44 3715,-44 3715,-94 3715,-94 3715,-100 3709,-106 3703,-106 3703,-106 3310,-106 3310,-106 3304,-106 3298,-100 3298,-94 3298,-94 3298,-44 3298,-44 3298,-38 3304,-32 3310,-32"/>
<text text-anchor="middle" x="3506.5" y="-91.6" font-family="Arial Black" font-size="13.00">screenshot</text>
</g>
<g id="clust6" class="cluster">
<title>cluster~/lib/src/user_interaction</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M4668,-32C4668,-32 4884,-32 4884,-32 4890,-32 4896,-38 4896,-44 4896,-44 4896,-176 4896,-176 4896,-182 4890,-188 4884,-188 4884,-188 4668,-188 4668,-188 4662,-188 4656,-182 4656,-176 4656,-176 4656,-44 4656,-44 4656,-38 4662,-32 4668,-32"/>
<text text-anchor="middle" x="4776" y="-173.6" font-family="Arial Black" font-size="13.00">user_interaction</text>
</g>
<g id="clust7" class="cluster">
<title>cluster~/lib/src/event_processor</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M204,-196C204,-196 1809,-196 1809,-196 1815,-196 1821,-202 1821,-208 1821,-208 1821,-258 1821,-258 1821,-264 1815,-270 1809,-270 1809,-270 204,-270 204,-270 198,-270 192,-264 192,-258 192,-258 192,-208 192,-208 192,-202 198,-196 204,-196"/>
<text text-anchor="middle" x="1006.5" y="-255.6" font-family="Arial Black" font-size="13.00">event_processor</text>
</g>
<g id="clust8" class="cluster">
<title>cluster~/lib/src/navigation</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M4686,-196C4686,-196 4872,-196 4872,-196 4878,-196 4884,-202 4884,-208 4884,-208 4884,-258 4884,-258 4884,-264 4878,-270 4872,-270 4872,-270 4686,-270 4686,-270 4680,-270 4674,-264 4674,-258 4674,-258 4674,-208 4674,-208 4674,-202 4680,-196 4686,-196"/>
<text text-anchor="middle" x="4779" y="-255.6" font-family="Arial Black" font-size="13.00">navigation</text>
</g>
<g id="clust9" class="cluster">
<title>cluster~/lib/src/view_hierarchy</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M4408,-114C4408,-114 4636,-114 4636,-114 4642,-114 4648,-120 4648,-126 4648,-126 4648,-362 4648,-362 4648,-368 4642,-374 4636,-374 4636,-374 4408,-374 4408,-374 4402,-374 4396,-368 4396,-362 4396,-362 4396,-126 4396,-126 4396,-120 4402,-114 4408,-114"/>
<text text-anchor="middle" x="4522" y="-359.6" font-family="Arial Black" font-size="13.00">view_hierarchy</text>
</g>
<g id="clust10" class="cluster">
<title>cluster~/lib/src/jvm</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M204,-32C204,-32 308,-32 308,-32 314,-32 320,-38 320,-44 320,-44 320,-176 320,-176 320,-182 314,-188 308,-188 308,-188 204,-188 204,-188 198,-188 192,-182 192,-176 192,-176 192,-44 192,-44 192,-38 198,-32 204,-32"/>
<text text-anchor="middle" x="256" y="-173.6" font-family="Arial Black" font-size="13.00">jvm</text>
</g>
<g id="clust11" class="cluster">
<title>cluster~/lib/src/native</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M3938,-114C3938,-114 4376,-114 4376,-114 4382,-114 4388,-120 4388,-126 4388,-126 4388,-288 4388,-288 4388,-294 4382,-300 4376,-300 4376,-300 3938,-300 3938,-300 3932,-300 3926,-294 3926,-288 3926,-288 3926,-126 3926,-126 3926,-120 3932,-114 3938,-114"/>
<text text-anchor="middle" x="4157" y="-285.6" font-family="Arial Black" font-size="13.00">native</text>
</g>
<g id="clust12" class="cluster">
<title>cluster~/lib/src/native/cocoa</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M4312,-196C4312,-196 4368,-196 4368,-196 4374,-196 4380,-202 4380,-208 4380,-208 4380,-258 4380,-258 4380,-264 4374,-270 4368,-270 4368,-270 4312,-270 4312,-270 4306,-270 4300,-264 4300,-258 4300,-258 4300,-208 4300,-208 4300,-202 4306,-196 4312,-196"/>
<text text-anchor="middle" x="4340" y="-255.6" font-family="Arial Black" font-size="13.00">cocoa</text>
</g>
<g id="clust13" class="cluster">
<title>cluster~/lib/src/integrations</title>
<path fill="none" stroke="black" stroke-width="2.6" d="M1855,-196C1855,-196 3892,-196 3892,-196 3898,-196 3904,-202 3904,-208 3904,-208 3904,-362 3904,-362 3904,-368 3898,-374 3892,-374 3892,-374 1855,-374 1855,-374 1849,-374 1843,-368 1843,-362 1843,-362 1843,-208 1843,-208 1843,-202 1849,-196 1855,-196"/>
<text text-anchor="middle" x="2873.5" y="-359.6" font-family="Arial Black" font-size="13.00">integrations</text>
</g>
<!-- /lib/sentry_flutter.dart -->
<g id="node1" class="node">
<title>/lib/sentry_flutter.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5138,-158C5138,-158 5058,-158 5058,-158 5052,-158 5046,-152 5046,-146 5046,-146 5046,-134 5046,-134 5046,-128 5052,-122 5058,-122 5058,-122 5138,-122 5138,-122 5144,-122 5150,-128 5150,-134 5150,-134 5150,-146 5150,-146 5150,-152 5144,-158 5138,-158"/>
<text text-anchor="middle" x="5098" y="-136.5" font-family="Arial" font-size="15.00">sentry_flutter</text>
</g>
<!-- /lib/src/sentry_flutter.dart -->
<g id="node7" class="node">
<title>/lib/src/sentry_flutter.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1975,-418C1975,-418 1895,-418 1895,-418 1889,-418 1883,-412 1883,-406 1883,-406 1883,-394 1883,-394 1883,-388 1889,-382 1895,-382 1895,-382 1975,-382 1975,-382 1981,-382 1987,-388 1987,-394 1987,-394 1987,-406 1987,-406 1987,-412 1981,-418 1975,-418"/>
<text text-anchor="middle" x="1935" y="-396.5" font-family="Arial" font-size="15.00">sentry_flutter</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/sentry_flutter.dart -->
<g id="edge3" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.81,-148.62C5013.2,-155.83 4972.36,-169.68 4945,-196 4909.08,-230.56 4939.92,-266.5 4903,-300 4816.87,-378.15 4767.03,-356.86 4652,-374 4516.55,-394.19 2390.56,-398.32 1997.3,-398.92"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1997.25,-395.42 1987.25,-398.93 1997.26,-402.42 1997.25,-395.42"/>
</g>
<!-- /lib/src/screenshot/sentry_screenshot_widget.dart -->
<g id="node8" class="node">
<title>/lib/src/screenshot/sentry_screenshot_widget.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3485.5,-76C3485.5,-76 3318.5,-76 3318.5,-76 3312.5,-76 3306.5,-70 3306.5,-64 3306.5,-64 3306.5,-52 3306.5,-52 3306.5,-46 3312.5,-40 3318.5,-40 3318.5,-40 3485.5,-40 3485.5,-40 3491.5,-40 3497.5,-46 3497.5,-52 3497.5,-52 3497.5,-64 3497.5,-64 3497.5,-70 3491.5,-76 3485.5,-76"/>
<text text-anchor="middle" x="3402" y="-54.5" font-family="Arial" font-size="15.00">sentry_screenshot_widget</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_widget.dart -->
<g id="edge8" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_widget.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.64,-130.65C5005.82,-124.72 4949.61,-117.26 4900,-114 4861.36,-111.46 3543.83,-114.27 3506,-106 3483.04,-100.98 3459.04,-90.53 3439.88,-80.75"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3441.37,-77.58 3430.89,-76.03 3438.12,-83.78 3441.37,-77.58"/>
</g>
<!-- /lib/src/screenshot/sentry_screenshot_quality.dart -->
<g id="node9" class="node">
<title>/lib/src/screenshot/sentry_screenshot_quality.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3694.5,-76C3694.5,-76 3527.5,-76 3527.5,-76 3521.5,-76 3515.5,-70 3515.5,-64 3515.5,-64 3515.5,-52 3515.5,-52 3515.5,-46 3521.5,-40 3527.5,-40 3527.5,-40 3694.5,-40 3694.5,-40 3700.5,-40 3706.5,-46 3706.5,-52 3706.5,-52 3706.5,-64 3706.5,-64 3706.5,-70 3700.5,-76 3694.5,-76"/>
<text text-anchor="middle" x="3611" y="-54.5" font-family="Arial" font-size="15.00">sentry_screenshot_quality</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_quality.dart -->
<g id="edge9" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_quality.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.64,-130.66C5005.82,-124.74 4949.61,-117.29 4900,-114 4834.03,-109.63 3774.45,-120.75 3710,-106 3688.2,-101.01 3665.56,-90.7 3647.43,-81"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3648.84,-77.78 3638.4,-76.02 3645.46,-83.91 3648.84,-77.78"/>
</g>
<!-- /lib/src/flutter_sentry_attachment.dart -->
<g id="node11" class="node">
<title>/lib/src/flutter_sentry_attachment.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4844,-418C4844,-418 4680,-418 4680,-418 4674,-418 4668,-412 4668,-406 4668,-406 4668,-394 4668,-394 4668,-388 4674,-382 4680,-382 4680,-382 4844,-382 4844,-382 4850,-382 4856,-388 4856,-394 4856,-394 4856,-406 4856,-406 4856,-412 4850,-418 4844,-418"/>
<text text-anchor="middle" x="4762" y="-396.5" font-family="Arial" font-size="15.00">flutter_sentry_attachment</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/flutter_sentry_attachment.dart -->
<g id="edge5" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/flutter_sentry_attachment.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5093.28,-158.3C5080.06,-203 5038.1,-322.48 4955,-374 4938.12,-384.46 4885.7,-379.51 4866,-382 4865.9,-382.01 4865.8,-382.03 4865.69,-382.04"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4865.51,-378.53 4856.06,-383.31 4866.43,-385.47 4865.51,-378.53"/>
</g>
<!-- /lib/src/binding_wrapper.dart -->
<g id="node12" class="node">
<title>/lib/src/binding_wrapper.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5018,-76C5018,-76 4916,-76 4916,-76 4910,-76 4904,-70 4904,-64 4904,-64 4904,-52 4904,-52 4904,-46 4910,-40 4916,-40 4916,-40 5018,-40 5018,-40 5024,-40 5030,-46 5030,-52 5030,-52 5030,-64 5030,-64 5030,-70 5024,-76 5018,-76"/>
<text text-anchor="middle" x="4967" y="-54.5" font-family="Arial" font-size="15.00">binding_wrapper</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/binding_wrapper.dart -->
<g id="edge11" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/binding_wrapper.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5075.55,-121.84C5057.41,-109.97 5031.54,-94.08 5009.55,-81.25"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5011.12,-78.11 5000.71,-76.13 5007.61,-84.17 5011.12,-78.11"/>
</g>
<!-- /lib/src/user_interaction/sentry_user_interaction_widget.dart -->
<g id="node13" class="node">
<title>/lib/src/user_interaction/sentry_user_interaction_widget.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4876,-158C4876,-158 4676,-158 4676,-158 4670,-158 4664,-152 4664,-146 4664,-146 4664,-134 4664,-134 4664,-128 4670,-122 4676,-122 4676,-122 4876,-122 4876,-122 4882,-122 4888,-128 4888,-134 4888,-134 4888,-146 4888,-146 4888,-152 4882,-158 4876,-158"/>
<text text-anchor="middle" x="4776" y="-136.5" font-family="Arial" font-size="15.00">sentry_user_interaction_widget</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/user_interaction/sentry_user_interaction_widget.dart -->
<g id="edge10" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/user_interaction/sentry_user_interaction_widget.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.97,-140C4996.79,-140 4947.61,-140 4898.42,-140"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4898.26,-136.5 4888.26,-140 4898.26,-143.5 4898.26,-136.5"/>
</g>
<!-- /lib/src/sentry_asset_bundle.dart -->
<g id="node21" class="node">
<title>/lib/src/sentry_asset_bundle.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M5017.5,-418C5017.5,-418 4886.5,-418 4886.5,-418 4880.5,-418 4874.5,-412 4874.5,-406 4874.5,-406 4874.5,-394 4874.5,-394 4874.5,-388 4880.5,-382 4886.5,-382 4886.5,-382 5017.5,-382 5017.5,-382 5023.5,-382 5029.5,-388 5029.5,-394 5029.5,-394 5029.5,-406 5029.5,-406 5029.5,-412 5023.5,-418 5017.5,-418"/>
<text text-anchor="middle" x="4952" y="-396.5" font-family="Arial" font-size="15.00">sentry_asset_bundle</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/sentry_asset_bundle.dart -->
<g id="edge6" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/sentry_asset_bundle.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5099.57,-158.07C5102.48,-200.72 5103.81,-312.86 5045,-374 5042.94,-376.15 5040.71,-378.12 5038.35,-379.92"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5036.34,-377.05 5029.88,-385.45 5040.17,-382.91 5036.34,-377.05"/>
</g>
<!-- /lib/src/navigation/sentry_navigator_observer.dart -->
<g id="node22" class="node">
<title>/lib/src/navigation/sentry_navigator_observer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4864,-240C4864,-240 4694,-240 4694,-240 4688,-240 4682,-234 4682,-228 4682,-228 4682,-216 4682,-216 4682,-210 4688,-204 4694,-204 4694,-204 4864,-204 4864,-204 4870,-204 4876,-210 4876,-216 4876,-216 4876,-228 4876,-228 4876,-234 4870,-240 4864,-240"/>
<text text-anchor="middle" x="4779" y="-218.5" font-family="Arial" font-size="15.00">sentry_navigator_observer</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/navigation/sentry_navigator_observer.dart -->
<g id="edge2" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/navigation/sentry_navigator_observer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.8,-155.43C4996.11,-168.31 4920.63,-187.29 4862.58,-201.52"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4861.57,-198.16 4852.69,-203.94 4863.23,-204.96 4861.57,-198.16"/>
</g>
<!-- /lib/src/sentry_flutter_options.dart -->
<g id="node23" class="node">
<title>/lib/src/sentry_flutter_options.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2850.5,-158C2850.5,-158 2713.5,-158 2713.5,-158 2707.5,-158 2701.5,-152 2701.5,-146 2701.5,-146 2701.5,-134 2701.5,-134 2701.5,-128 2707.5,-122 2713.5,-122 2713.5,-122 2850.5,-122 2850.5,-122 2856.5,-122 2862.5,-128 2862.5,-134 2862.5,-134 2862.5,-146 2862.5,-146 2862.5,-152 2856.5,-158 2850.5,-158"/>
<text text-anchor="middle" x="2782" y="-136.5" font-family="Arial" font-size="15.00">sentry_flutter_options</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge4" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.93,-156.87C5004.47,-169.38 4944.69,-185.36 4891,-192 4850.39,-197.02 3458.88,-193.67 3418,-192 3223.39,-184.06 2996.13,-162.5 2872.84,-149.77"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2873,-146.27 2862.69,-148.72 2872.28,-153.23 2873,-146.27"/>
</g>
<!-- /lib/src/integrations/on_error_integration.dart -->
<g id="node38" class="node">
<title>/lib/src/integrations/on_error_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3686,-240C3686,-240 3558,-240 3558,-240 3552,-240 3546,-234 3546,-228 3546,-228 3546,-216 3546,-216 3546,-210 3552,-204 3558,-204 3558,-204 3686,-204 3686,-204 3692,-204 3698,-210 3698,-216 3698,-216 3698,-228 3698,-228 3698,-234 3692,-240 3686,-240"/>
<text text-anchor="middle" x="3622" y="-218.5" font-family="Arial" font-size="15.00">on_error_integration</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/integrations/on_error_integration.dart -->
<g id="edge7" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/integrations/on_error_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.64,-156.86C5006.06,-168 4950.16,-181.96 4900,-188 4768.39,-203.85 3838.14,-176.67 3707,-196 3698.58,-197.24 3689.82,-199.11 3681.28,-201.29"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3680.11,-197.98 3671.37,-203.96 3681.94,-204.74 3680.11,-197.98"/>
</g>
<!-- /lib/src/integrations/load_release_integration.dart -->
<g id="node41" class="node">
<title>/lib/src/integrations/load_release_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3883.5,-240C3883.5,-240 3728.5,-240 3728.5,-240 3722.5,-240 3716.5,-234 3716.5,-228 3716.5,-228 3716.5,-216 3716.5,-216 3716.5,-210 3722.5,-204 3728.5,-204 3728.5,-204 3883.5,-204 3883.5,-204 3889.5,-204 3895.5,-210 3895.5,-216 3895.5,-216 3895.5,-228 3895.5,-228 3895.5,-234 3889.5,-240 3883.5,-240"/>
<text text-anchor="middle" x="3806" y="-218.5" font-family="Arial" font-size="15.00">load_release_integration</text>
</g>
<!-- /lib/sentry_flutter.dart&#45;&gt;/lib/src/integrations/load_release_integration.dart -->
<g id="edge1" class="edge">
<title>/lib/sentry_flutter.dart&#45;&gt;/lib/src/integrations/load_release_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M5045.64,-156.83C5006.06,-167.97 4950.16,-181.92 4900,-188 4792.12,-201.08 4030.01,-184.05 3922,-196 3909.56,-197.38 3896.49,-199.51 3883.79,-201.96"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3882.82,-198.58 3873.7,-203.98 3884.2,-205.45 3882.82,-198.58"/>
</g>
<!-- /lib/src/renderer/renderer.dart -->
<g id="node2" class="node">
<title>/lib/src/renderer/renderer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1602,-76C1602,-76 1552,-76 1552,-76 1546,-76 1540,-70 1540,-64 1540,-64 1540,-52 1540,-52 1540,-46 1546,-40 1552,-40 1552,-40 1602,-40 1602,-40 1608,-40 1614,-46 1614,-52 1614,-52 1614,-64 1614,-64 1614,-70 1608,-76 1602,-76"/>
<text text-anchor="middle" x="1577" y="-54.5" font-family="Arial" font-size="15.00">renderer</text>
</g>
<!-- /lib/src/renderer/unknown_renderer.dart -->
<g id="node3" class="node">
<title>/lib/src/renderer/unknown_renderer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1830.5,-158C1830.5,-158 1713.5,-158 1713.5,-158 1707.5,-158 1701.5,-152 1701.5,-146 1701.5,-146 1701.5,-134 1701.5,-134 1701.5,-128 1707.5,-122 1713.5,-122 1713.5,-122 1830.5,-122 1830.5,-122 1836.5,-122 1842.5,-128 1842.5,-134 1842.5,-134 1842.5,-146 1842.5,-146 1842.5,-152 1836.5,-158 1830.5,-158"/>
<text text-anchor="middle" x="1772" y="-136.5" font-family="Arial" font-size="15.00">unknown_renderer</text>
</g>
<!-- /lib/src/renderer/unknown_renderer.dart&#45;&gt;/lib/src/renderer/renderer.dart -->
<g id="edge12" class="edge">
<title>/lib/src/renderer/unknown_renderer.dart&#45;&gt;/lib/src/renderer/renderer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1730.17,-121.84C1698.8,-108.97 1655.86,-91.35 1623.43,-78.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1624.74,-74.8 1614.15,-74.24 1622.08,-81.28 1624.74,-74.8"/>
</g>
<!-- /lib/src/renderer/io_renderer.dart -->
<g id="node4" class="node">
<title>/lib/src/renderer/io_renderer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1671.5,-158C1671.5,-158 1602.5,-158 1602.5,-158 1596.5,-158 1590.5,-152 1590.5,-146 1590.5,-146 1590.5,-134 1590.5,-134 1590.5,-128 1596.5,-122 1602.5,-122 1602.5,-122 1671.5,-122 1671.5,-122 1677.5,-122 1683.5,-128 1683.5,-134 1683.5,-134 1683.5,-146 1683.5,-146 1683.5,-152 1677.5,-158 1671.5,-158"/>
<text text-anchor="middle" x="1637" y="-136.5" font-family="Arial" font-size="15.00">io_renderer</text>
</g>
<!-- /lib/src/renderer/io_renderer.dart&#45;&gt;/lib/src/renderer/renderer.dart -->
<g id="edge13" class="edge">
<title>/lib/src/renderer/io_renderer.dart&#45;&gt;/lib/src/renderer/renderer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1623.98,-121.64C1615.75,-110.66 1604.97,-96.29 1595.82,-84.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1598.59,-81.95 1589.79,-76.05 1592.99,-86.15 1598.59,-81.95"/>
</g>
<!-- /lib/src/renderer/html_renderer.dart -->
<g id="node5" class="node">
<title>/lib/src/renderer/html_renderer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1560,-158C1560,-158 1474,-158 1474,-158 1468,-158 1462,-152 1462,-146 1462,-146 1462,-134 1462,-134 1462,-128 1468,-122 1474,-122 1474,-122 1560,-122 1560,-122 1566,-122 1572,-128 1572,-134 1572,-134 1572,-146 1572,-146 1572,-152 1566,-158 1560,-158"/>
<text text-anchor="middle" x="1517" y="-136.5" font-family="Arial" font-size="15.00">html_renderer</text>
</g>
<!-- /lib/src/renderer/html_renderer.dart&#45;&gt;/lib/src/renderer/renderer.dart -->
<g id="edge14" class="edge">
<title>/lib/src/renderer/html_renderer.dart&#45;&gt;/lib/src/renderer/renderer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1530.02,-121.64C1538.25,-110.66 1549.03,-96.29 1558.18,-84.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1561.01,-86.15 1564.21,-76.05 1555.41,-81.95 1561.01,-86.15"/>
</g>
<!-- /lib/src/version.dart -->
<g id="node6" class="node">
<title>/lib/src/version.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1820.5,-344C1820.5,-344 1779.5,-344 1779.5,-344 1773.5,-344 1767.5,-338 1767.5,-332 1767.5,-332 1767.5,-320 1767.5,-320 1767.5,-314 1773.5,-308 1779.5,-308 1779.5,-308 1820.5,-308 1820.5,-308 1826.5,-308 1832.5,-314 1832.5,-320 1832.5,-320 1832.5,-332 1832.5,-332 1832.5,-338 1826.5,-344 1820.5,-344"/>
<text text-anchor="middle" x="1800" y="-322.5" font-family="Arial" font-size="15.00">version</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge15" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1987.2,-398.86C2368.08,-397.8 4706.41,-390.6 4777,-374 4850.14,-356.8 4868.82,-344.98 4929,-300 4979.15,-262.52 4977.55,-237.98 5024,-196 5036.47,-184.73 5051.16,-173.45 5064.15,-164.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5066.28,-166.85 5072.4,-158.2 5062.22,-161.14 5066.28,-166.85"/>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/renderer/renderer.dart -->
<g id="edge21" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/renderer/renderer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1882.95,-382.66C1881.62,-382.42 1880.31,-382.2 1879,-382 1840.14,-376.12 501.95,-384.23 464,-374 407.1,-358.66 405.55,-324.59 349,-308 331.81,-302.96 200.35,-312.98 188,-300 156.14,-266.51 156.29,-229.63 188,-196 198.38,-184.99 308.91,-189.22 324,-188 795.1,-149.91 1363.84,-84.19 1529.57,-64.64"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1530.38,-68.07 1539.9,-63.42 1529.56,-61.12 1530.38,-68.07"/>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/version.dart -->
<g id="edge27" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/version.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1882.88,-383C1881.58,-382.65 1880.28,-382.32 1879,-382 1861.42,-377.58 1854.57,-383.29 1839,-374 1830.08,-368.68 1822.27,-360.55 1816.04,-352.54"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1818.82,-350.41 1810.13,-344.35 1813.14,-354.51 1818.82,-350.41"/>
</g>
<!-- /lib/src/file_system_transport.dart -->
<g id="node10" class="node">
<title>/lib/src/file_system_transport.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M328,-344C328,-344 192,-344 192,-344 186,-344 180,-338 180,-332 180,-332 180,-320 180,-320 180,-314 186,-308 192,-308 192,-308 328,-308 328,-308 334,-308 340,-314 340,-320 340,-320 340,-332 340,-332 340,-338 334,-344 328,-344"/>
<text text-anchor="middle" x="260" y="-322.5" font-family="Arial" font-size="15.00">file_system_transport</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/file_system_transport.dart -->
<g id="edge26" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/file_system_transport.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1882.95,-382.66C1881.62,-382.42 1880.31,-382.2 1879,-382 1837.11,-375.67 395.2,-383.84 354,-374 333.34,-369.06 312.02,-358.9 294.93,-349.28"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="296.3,-346.02 285.89,-344.02 292.78,-352.08 296.3,-346.02"/>
</g>
<!-- /lib/src/event_processor/flutter_exception_event_processor.dart -->
<g id="node16" class="node">
<title>/lib/src/event_processor/flutter_exception_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1308,-240C1308,-240 1086,-240 1086,-240 1080,-240 1074,-234 1074,-228 1074,-228 1074,-216 1074,-216 1074,-210 1080,-204 1086,-204 1086,-204 1308,-204 1308,-204 1314,-204 1320,-210 1320,-216 1320,-216 1320,-228 1320,-228 1320,-234 1314,-240 1308,-240"/>
<text text-anchor="middle" x="1197" y="-218.5" font-family="Arial" font-size="15.00">flutter_exception_event_processor</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/flutter_exception_event_processor.dart -->
<g id="edge17" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/flutter_exception_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1882.93,-382.77C1881.61,-382.49 1880.3,-382.24 1879,-382 1825.98,-372.33 1811.27,-382.18 1758,-374 1635.47,-355.17 1609.67,-325.88 1487,-308 1452.21,-302.93 1362.69,-310.03 1329,-300 1291.08,-288.71 1252.54,-264.44 1226.97,-246.15"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1228.96,-243.27 1218.82,-240.2 1224.84,-248.92 1228.96,-243.27"/>
</g>
<!-- /lib/src/event_processor/flutter_enricher_event_processor.dart -->
<g id="node17" class="node">
<title>/lib/src/event_processor/flutter_enricher_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M424,-240C424,-240 212,-240 212,-240 206,-240 200,-234 200,-228 200,-228 200,-216 200,-216 200,-210 206,-204 212,-204 212,-204 424,-204 424,-204 430,-204 436,-210 436,-216 436,-216 436,-228 436,-228 436,-234 430,-240 424,-240"/>
<text text-anchor="middle" x="318" y="-218.5" font-family="Arial" font-size="15.00">flutter_enricher_event_processor</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/flutter_enricher_event_processor.dart -->
<g id="edge25" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/flutter_enricher_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1882.95,-382.66C1881.62,-382.42 1880.31,-382.2 1879,-382 1755.42,-363.21 878.09,-389.1 754,-374 613.82,-356.94 575.61,-353.7 445,-300 410.44,-285.79 374.31,-263 349.46,-245.89"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="351.37,-242.95 341.17,-240.1 347.36,-248.69 351.37,-242.95"/>
</g>
<!-- /lib/src/event_processor/platform_exception_event_processor.dart -->
<g id="node18" class="node">
<title>/lib/src/event_processor/platform_exception_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M705.5,-240C705.5,-240 466.5,-240 466.5,-240 460.5,-240 454.5,-234 454.5,-228 454.5,-228 454.5,-216 454.5,-216 454.5,-210 460.5,-204 466.5,-204 466.5,-204 705.5,-204 705.5,-204 711.5,-204 717.5,-210 717.5,-216 717.5,-216 717.5,-228 717.5,-228 717.5,-234 711.5,-240 705.5,-240"/>
<text text-anchor="middle" x="586" y="-218.5" font-family="Arial" font-size="15.00">platform_exception_event_processor</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/platform_exception_event_processor.dart -->
<g id="edge18" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/platform_exception_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1882.95,-382.67C1881.62,-382.43 1880.31,-382.2 1879,-382 1797.84,-369.5 1221.75,-381.71 1140,-374 954.35,-356.49 902.89,-361.94 727,-300 688.31,-286.37 647.53,-262.99 619.84,-245.56"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="621.64,-242.55 611.33,-240.12 617.87,-248.45 621.64,-242.55"/>
</g>
<!-- /lib/src/event_processor/android_platform_exception_event_processor.dart -->
<g id="node19" class="node">
<title>/lib/src/event_processor/android_platform_exception_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1044,-240C1044,-240 748,-240 748,-240 742,-240 736,-234 736,-228 736,-228 736,-216 736,-216 736,-210 742,-204 748,-204 748,-204 1044,-204 1044,-204 1050,-204 1056,-210 1056,-216 1056,-216 1056,-228 1056,-228 1056,-234 1050,-240 1044,-240"/>
<text text-anchor="middle" x="896" y="-218.5" font-family="Arial" font-size="15.00">android_platform_exception_event_processor</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/android_platform_exception_event_processor.dart -->
<g id="edge16" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/event_processor/android_platform_exception_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1882.94,-382.69C1881.62,-382.44 1880.3,-382.21 1879,-382 1792.95,-368.33 1573.66,-383.06 1487,-374 1330.86,-357.67 1295.29,-331.01 1140,-308 1106.84,-303.09 1097.34,-308.84 1065,-300 1018.24,-287.21 968.1,-262.88 934.69,-244.98"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="936.19,-241.81 925.73,-240.12 932.85,-247.96 936.19,-241.81"/>
</g>
<!-- /lib/src/view_hierarchy/view_hierarchy_integration.dart -->
<g id="node25" class="node">
<title>/lib/src/view_hierarchy/view_hierarchy_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4607,-344C4607,-344 4437,-344 4437,-344 4431,-344 4425,-338 4425,-332 4425,-332 4425,-320 4425,-320 4425,-314 4431,-308 4437,-308 4437,-308 4607,-308 4607,-308 4613,-308 4619,-314 4619,-320 4619,-320 4619,-332 4619,-332 4619,-338 4613,-344 4607,-344"/>
<text text-anchor="middle" x="4522" y="-322.5" font-family="Arial" font-size="15.00">view_hierarchy_integration</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/view_hierarchy/view_hierarchy_integration.dart -->
<g id="edge28" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/view_hierarchy/view_hierarchy_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1987.22,-399.1C2334.35,-399.7 4298.23,-401.81 4421,-374 4443.11,-368.99 4466.14,-358.72 4484.62,-349.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4486.42,-352.05 4493.57,-344.23 4483.1,-345.89 4486.42,-352.05"/>
</g>
<!-- /lib/src/native/native_scope_observer.dart -->
<g id="node31" class="node">
<title>/lib/src/native/native_scope_observer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4093.5,-240C4093.5,-240 3946.5,-240 3946.5,-240 3940.5,-240 3934.5,-234 3934.5,-228 3934.5,-228 3934.5,-216 3934.5,-216 3934.5,-210 3940.5,-204 3946.5,-204 3946.5,-204 4093.5,-204 4093.5,-204 4099.5,-204 4105.5,-210 4105.5,-216 4105.5,-216 4105.5,-228 4105.5,-228 4105.5,-234 4099.5,-240 4093.5,-240"/>
<text text-anchor="middle" x="4020" y="-218.5" font-family="Arial" font-size="15.00">native_scope_observer</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/native/native_scope_observer.dart -->
<g id="edge20" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/native/native_scope_observer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1987.09,-399.01C2296.09,-399.01 3876.33,-397.82 3920,-374 3968.74,-347.42 3997.82,-285.67 4011.17,-249.91"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4014.59,-250.74 4014.67,-240.14 4008,-248.38 4014.59,-250.74"/>
</g>
<!-- /lib/src/native/sentry_native_channel.dart -->
<g id="node32" class="node">
<title>/lib/src/native/sentry_native_channel.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4278,-240C4278,-240 4136,-240 4136,-240 4130,-240 4124,-234 4124,-228 4124,-228 4124,-216 4124,-216 4124,-210 4130,-204 4136,-204 4136,-204 4278,-204 4278,-204 4284,-204 4290,-210 4290,-216 4290,-216 4290,-228 4290,-228 4290,-234 4284,-240 4278,-240"/>
<text text-anchor="middle" x="4207" y="-218.5" font-family="Arial" font-size="15.00">sentry_native_channel</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/native/sentry_native_channel.dart -->
<g id="edge23" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/native/sentry_native_channel.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1987.25,-398.98C2294.73,-398.8 3858.32,-396.73 3957,-374 4032.17,-356.68 4048.89,-341.37 4114,-300 4138.4,-284.5 4163.49,-263.36 4181.45,-247.15"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4184.16,-249.42 4189.18,-240.09 4179.44,-244.25 4184.16,-249.42"/>
</g>
<!-- /lib/src/native/sentry_native.dart -->
<g id="node33" class="node">
<title>/lib/src/native/sentry_native.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4075,-158C4075,-158 3993,-158 3993,-158 3987,-158 3981,-152 3981,-146 3981,-146 3981,-134 3981,-134 3981,-128 3987,-122 3993,-122 3993,-122 4075,-122 4075,-122 4081,-122 4087,-128 4087,-134 4087,-134 4087,-146 4087,-146 4087,-152 4081,-158 4075,-158"/>
<text text-anchor="middle" x="4034" y="-136.5" font-family="Arial" font-size="15.00">sentry_native</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/native/sentry_native.dart -->
<g id="edge22" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/native/sentry_native.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1987,-398.81C2297.12,-397.63 3889.8,-390.75 3908,-374 3937.23,-347.09 3897.72,-224.89 3925,-196 3941.05,-179 3955.66,-197.56 3977,-188 3989.25,-182.51 4001.02,-173.65 4010.64,-165.1"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4013.26,-167.44 4018.19,-158.06 4008.49,-162.32 4013.26,-167.44"/>
</g>
<!-- /lib/src/integrations/screenshot_integration.dart -->
<g id="node44" class="node">
<title>/lib/src/integrations/screenshot_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2007,-344C2007,-344 1863,-344 1863,-344 1857,-344 1851,-338 1851,-332 1851,-332 1851,-320 1851,-320 1851,-314 1857,-308 1863,-308 1863,-308 2007,-308 2007,-308 2013,-308 2019,-314 2019,-320 2019,-320 2019,-332 2019,-332 2019,-338 2013,-344 2007,-344"/>
<text text-anchor="middle" x="1935" y="-322.5" font-family="Arial" font-size="15.00">screenshot_integration</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/integrations/screenshot_integration.dart -->
<g id="edge19" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/integrations/screenshot_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1935,-381.94C1935,-373.81 1935,-363.88 1935,-354.7"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1938.5,-354.44 1935,-344.44 1931.5,-354.44 1938.5,-354.44"/>
</g>
<!-- /lib/src/integrations/integrations.dart -->
<g id="node45" class="node">
<title>/lib/src/integrations/integrations.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2817,-344C2817,-344 2747,-344 2747,-344 2741,-344 2735,-338 2735,-332 2735,-332 2735,-320 2735,-320 2735,-314 2741,-308 2747,-308 2747,-308 2817,-308 2817,-308 2823,-308 2829,-314 2829,-320 2829,-320 2829,-332 2829,-332 2829,-338 2823,-344 2817,-344"/>
<text text-anchor="middle" x="2782" y="-322.5" font-family="Arial" font-size="15.00">integrations</text>
</g>
<!-- /lib/src/sentry_flutter.dart&#45;&gt;/lib/src/integrations/integrations.dart -->
<g id="edge24" class="edge">
<title>/lib/src/sentry_flutter.dart&#45;&gt;/lib/src/integrations/integrations.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1987.03,-394.58C2136.82,-381.84 2568.37,-345.16 2724.64,-331.88"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2725.3,-335.33 2734.97,-331 2724.71,-328.36 2725.3,-335.33"/>
</g>
<!-- /lib/src/binding_wrapper.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge29" class="edge">
<title>/lib/src/binding_wrapper.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4989.4,-76.13C5007.53,-87.99 5033.4,-103.88 5055.4,-116.72"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5053.83,-119.86 5064.24,-121.84 5057.34,-113.8 5053.83,-119.86"/>
</g>
<!-- /lib/src/user_interaction/sentry_user_interaction_widget.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge30" class="edge">
<title>/lib/src/user_interaction/sentry_user_interaction_widget.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4888.26,-140C4937.44,-140 4986.63,-140 5035.81,-140"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5035.97,-143.5 5045.97,-140 5035.97,-136.5 5035.97,-143.5"/>
</g>
<!-- /lib/src/user_interaction/user_interaction_widget.dart -->
<g id="node14" class="node">
<title>/lib/src/user_interaction/user_interaction_widget.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4851,-76C4851,-76 4701,-76 4701,-76 4695,-76 4689,-70 4689,-64 4689,-64 4689,-52 4689,-52 4689,-46 4695,-40 4701,-40 4701,-40 4851,-40 4851,-40 4857,-40 4863,-46 4863,-52 4863,-52 4863,-64 4863,-64 4863,-70 4857,-76 4851,-76"/>
<text text-anchor="middle" x="4776" y="-54.5" font-family="Arial" font-size="15.00">user_interaction_widget</text>
</g>
<!-- /lib/src/user_interaction/sentry_user_interaction_widget.dart&#45;&gt;/lib/src/user_interaction/user_interaction_widget.dart -->
<g id="edge32" class="edge">
<title>/lib/src/user_interaction/sentry_user_interaction_widget.dart&#45;&gt;/lib/src/user_interaction/user_interaction_widget.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4776,-121.64C4776,-111.3 4776,-97.94 4776,-86.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4779.5,-86.05 4776,-76.05 4772.5,-86.05 4779.5,-86.05"/>
</g>
<!-- /lib/src/widget_utils.dart -->
<g id="node35" class="node">
<title>/lib/src/widget_utils.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4619,-76C4619,-76 4549,-76 4549,-76 4543,-76 4537,-70 4537,-64 4537,-64 4537,-52 4537,-52 4537,-46 4543,-40 4549,-40 4549,-40 4619,-40 4619,-40 4625,-40 4631,-46 4631,-52 4631,-52 4631,-64 4631,-64 4631,-70 4625,-76 4619,-76"/>
<text text-anchor="middle" x="4584" y="-54.5" font-family="Arial" font-size="15.00">widget_utils</text>
</g>
<!-- /lib/src/user_interaction/sentry_user_interaction_widget.dart&#45;&gt;/lib/src/widget_utils.dart -->
<g id="edge31" class="edge">
<title>/lib/src/user_interaction/sentry_user_interaction_widget.dart&#45;&gt;/lib/src/widget_utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4695.52,-121.92C4680.73,-117.57 4665.65,-112.3 4652,-106 4638.26,-99.66 4624.31,-90.57 4612.65,-82.07"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4614.7,-79.24 4604.6,-76.04 4610.5,-84.84 4614.7,-79.24"/>
</g>
<!-- /lib/src/event_processor/screenshot_event_processor.dart -->
<g id="node15" class="node">
<title>/lib/src/event_processor/screenshot_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1535.5,-240C1535.5,-240 1350.5,-240 1350.5,-240 1344.5,-240 1338.5,-234 1338.5,-228 1338.5,-228 1338.5,-216 1338.5,-216 1338.5,-210 1344.5,-204 1350.5,-204 1350.5,-204 1535.5,-204 1535.5,-204 1541.5,-204 1547.5,-210 1547.5,-216 1547.5,-216 1547.5,-228 1547.5,-228 1547.5,-234 1541.5,-240 1535.5,-240"/>
<text text-anchor="middle" x="1443" y="-218.5" font-family="Arial" font-size="15.00">screenshot_event_processor</text>
</g>
<!-- /lib/src/event_processor/screenshot_event_processor.dart&#45;&gt;/lib/src/renderer/renderer.dart -->
<g id="edge35" class="edge">
<title>/lib/src/event_processor/screenshot_event_processor.dart&#45;&gt;/lib/src/renderer/renderer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1442.55,-203.68C1442.2,-177.12 1442.98,-127.94 1453,-114 1471.06,-88.88 1503.24,-75.06 1530.29,-67.55"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1531.17,-70.94 1539.98,-65.06 1529.43,-64.16 1531.17,-70.94"/>
</g>
<!-- /lib/src/event_processor/screenshot_event_processor.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_widget.dart -->
<g id="edge33" class="edge">
<title>/lib/src/event_processor/screenshot_event_processor.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_widget.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1509.86,-204C1524.94,-200.78 1540.94,-197.85 1556,-196 1687.94,-179.79 1722.73,-201.29 1855,-188 2044.46,-168.96 2087.76,-135.13 2277,-114 2650.2,-72.32 3096.54,-62.22 3296.29,-59.78"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3296.53,-63.28 3306.49,-59.66 3296.45,-56.28 3296.53,-63.28"/>
</g>
<!-- /lib/src/event_processor/screenshot_event_processor.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge34" class="edge">
<title>/lib/src/event_processor/screenshot_event_processor.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1509.24,-203.95C1524.5,-200.69 1540.73,-197.75 1556,-196 1635.59,-186.85 2196.99,-192.16 2277,-188 2422.16,-180.45 2590.1,-163.09 2691.08,-151.72"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2691.7,-155.17 2701.24,-150.57 2690.91,-148.21 2691.7,-155.17"/>
</g>
<!-- /lib/src/event_processor/flutter_enricher_event_processor.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge36" class="edge">
<title>/lib/src/event_processor/flutter_enricher_event_processor.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M392.19,-203.96C409.43,-200.68 427.78,-197.73 445,-196 522.94,-188.18 1776.7,-190.21 1855,-188 2161.88,-179.35 2525.39,-157.55 2691.29,-146.96"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2691.69,-150.44 2701.45,-146.31 2691.25,-143.45 2691.69,-150.44"/>
</g>
<!-- /lib/src/event_processor/android_platform_exception_event_processor.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge37" class="edge">
<title>/lib/src/event_processor/android_platform_exception_event_processor.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M994.48,-203.98C1017.55,-200.68 1042.08,-197.71 1065,-196 1171.23,-188.07 4794.21,-200.5 4900,-188 4946.06,-182.56 4996.94,-170.34 5035.59,-159.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5036.96,-162.98 5045.65,-156.92 5035.08,-156.24 5036.96,-162.98"/>
</g>
<!-- /lib/src/jvm/jvm_exception.dart -->
<g id="node28" class="node">
<title>/lib/src/jvm/jvm_exception.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M300,-158C300,-158 212,-158 212,-158 206,-158 200,-152 200,-146 200,-146 200,-134 200,-134 200,-128 206,-122 212,-122 212,-122 300,-122 300,-122 306,-122 312,-128 312,-134 312,-134 312,-146 312,-146 312,-152 306,-158 300,-158"/>
<text text-anchor="middle" x="256" y="-136.5" font-family="Arial" font-size="15.00">jvm_exception</text>
</g>
<!-- /lib/src/event_processor/android_platform_exception_event_processor.dart&#45;&gt;/lib/src/jvm/jvm_exception.dart -->
<g id="edge38" class="edge">
<title>/lib/src/event_processor/android_platform_exception_event_processor.dart&#45;&gt;/lib/src/jvm/jvm_exception.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M783.59,-203.99C764.39,-201.22 744.63,-198.46 726,-196 582.29,-177.03 413.4,-158.08 322.31,-148.14"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="322.46,-144.63 312.14,-147.03 321.7,-151.59 322.46,-144.63"/>
</g>
<!-- /lib/src/jvm/jvm_frame.dart -->
<g id="node29" class="node">
<title>/lib/src/jvm/jvm_frame.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M281.5,-76C281.5,-76 218.5,-76 218.5,-76 212.5,-76 206.5,-70 206.5,-64 206.5,-64 206.5,-52 206.5,-52 206.5,-46 212.5,-40 218.5,-40 218.5,-40 281.5,-40 281.5,-40 287.5,-40 293.5,-46 293.5,-52 293.5,-52 293.5,-64 293.5,-64 293.5,-70 287.5,-76 281.5,-76"/>
<text text-anchor="middle" x="250" y="-54.5" font-family="Arial" font-size="15.00">jvm_frame</text>
</g>
<!-- /lib/src/event_processor/android_platform_exception_event_processor.dart&#45;&gt;/lib/src/jvm/jvm_frame.dart -->
<g id="edge39" class="edge">
<title>/lib/src/event_processor/android_platform_exception_event_processor.dart&#45;&gt;/lib/src/jvm/jvm_frame.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M795.12,-203.94C772.44,-200.73 748.45,-197.81 726,-196 711.19,-194.81 201.43,-198.59 191,-188 167.92,-164.57 177.88,-144.16 191,-114 196.29,-101.84 205.74,-91.15 215.54,-82.51"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="217.92,-85.09 223.41,-76.03 213.47,-79.68 217.92,-85.09"/>
</g>
<!-- /lib/src/event_processor/native_app_start_event_processor.dart -->
<g id="node20" class="node">
<title>/lib/src/event_processor/native_app_start_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M1800.5,-240C1800.5,-240 1577.5,-240 1577.5,-240 1571.5,-240 1565.5,-234 1565.5,-228 1565.5,-228 1565.5,-216 1565.5,-216 1565.5,-210 1571.5,-204 1577.5,-204 1577.5,-204 1800.5,-204 1800.5,-204 1806.5,-204 1812.5,-210 1812.5,-216 1812.5,-216 1812.5,-228 1812.5,-228 1812.5,-234 1806.5,-240 1800.5,-240"/>
<text text-anchor="middle" x="1689" y="-218.5" font-family="Arial" font-size="15.00">native_app_start_event_processor</text>
</g>
<!-- /lib/src/event_processor/native_app_start_event_processor.dart&#45;&gt;/lib/src/native/sentry_native.dart -->
<g id="edge40" class="edge">
<title>/lib/src/event_processor/native_app_start_event_processor.dart&#45;&gt;/lib/src/native/sentry_native.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1776.74,-203.94C1797.1,-200.66 1818.74,-197.71 1839,-196 1868.59,-193.5 3949.07,-198.09 3977,-188 3989.95,-183.32 4002.05,-174.31 4011.75,-165.42"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4014.25,-167.87 4018.98,-158.39 4009.37,-162.85 4014.25,-167.87"/>
</g>
<!-- /lib/src/navigation/sentry_navigator_observer.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge41" class="edge">
<title>/lib/src/navigation/sentry_navigator_observer.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4841.4,-203.94C4897.65,-189.43 4979.81,-168.86 5035.95,-155.23"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5036.88,-158.61 5045.78,-152.85 5035.23,-151.8 5036.88,-158.61"/>
</g>
<!-- /lib/src/navigation/sentry_navigator_observer.dart&#45;&gt;/lib/src/native/sentry_native.dart -->
<g id="edge42" class="edge">
<title>/lib/src/navigation/sentry_navigator_observer.dart&#45;&gt;/lib/src/native/sentry_native.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4704.13,-203.95C4687.08,-200.71 4668.99,-197.78 4652,-196 4622.05,-192.86 4138.91,-196.45 4110,-188 4093.27,-183.11 4076.58,-173.46 4063.12,-164.16"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4064.94,-161.16 4054.77,-158.16 4060.85,-166.84 4064.94,-161.16"/>
</g>
<!-- /lib/src/sentry_flutter_options.dart&#45;&gt;/lib/src/renderer/renderer.dart -->
<g id="edge44" class="edge">
<title>/lib/src/sentry_flutter_options.dart&#45;&gt;/lib/src/renderer/renderer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2701.07,-133.63C2469.16,-118.23 1807.21,-74.28 1624.32,-62.14"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1624.43,-58.64 1614.22,-61.47 1623.96,-65.63 1624.43,-58.64"/>
</g>
<!-- /lib/src/sentry_flutter_options.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_quality.dart -->
<g id="edge45" class="edge">
<title>/lib/src/sentry_flutter_options.dart&#45;&gt;/lib/src/screenshot/sentry_screenshot_quality.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2862.62,-136.13C3044.86,-129.54 3475.18,-113.32 3506,-106 3528.71,-100.61 3552.57,-90.34 3571.8,-80.78"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3573.52,-83.83 3580.84,-76.16 3570.34,-77.59 3573.52,-83.83"/>
</g>
<!-- /lib/src/sentry_flutter_options.dart&#45;&gt;/lib/src/binding_wrapper.dart -->
<g id="edge43" class="edge">
<title>/lib/src/sentry_flutter_options.dart&#45;&gt;/lib/src/binding_wrapper.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2862.64,-134.89C2981.98,-129.02 3212.68,-118.44 3409,-114 3429.7,-113.53 4880.27,-112.29 4900,-106 4914.92,-101.24 4929.42,-91.87 4941.09,-82.73"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4943.54,-85.25 4949.05,-76.19 4939.1,-79.84 4943.54,-85.25"/>
</g>
<!-- /lib/src/widgets_binding_observer.dart -->
<g id="node24" class="node">
<title>/lib/src/widgets_binding_observer.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3595.5,-158C3595.5,-158 3430.5,-158 3430.5,-158 3424.5,-158 3418.5,-152 3418.5,-146 3418.5,-146 3418.5,-134 3418.5,-134 3418.5,-128 3424.5,-122 3430.5,-122 3430.5,-122 3595.5,-122 3595.5,-122 3601.5,-122 3607.5,-128 3607.5,-134 3607.5,-134 3607.5,-146 3607.5,-146 3607.5,-152 3601.5,-158 3595.5,-158"/>
<text text-anchor="middle" x="3513" y="-136.5" font-family="Arial" font-size="15.00">widgets_binding_observer</text>
</g>
<!-- /lib/src/widgets_binding_observer.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge46" class="edge">
<title>/lib/src/widgets_binding_observer.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3607.73,-155.18C3692.25,-167.91 3819.51,-185.09 3931,-192 3984.23,-195.3 4838.07,-198.55 4891,-192 4940.6,-185.87 4995.4,-171.76 5036.14,-159.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5037.34,-163.08 5045.93,-156.87 5035.34,-156.37 5037.34,-163.08"/>
</g>
<!-- /lib/src/view_hierarchy/view_hierarchy_integration.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge47" class="edge">
<title>/lib/src/view_hierarchy/view_hierarchy_integration.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4619.26,-308.38C4620.51,-308.25 4621.76,-308.12 4623,-308 4637.66,-306.55 4876.74,-309.5 4888,-300 4924.04,-269.59 4877.08,-230.71 4909,-196 4941.16,-161.02 4994.13,-147.82 5035.83,-143.05"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5036.3,-146.52 5045.9,-142.03 5035.6,-139.55 5036.3,-146.52"/>
</g>
<!-- /lib/src/view_hierarchy/view_hierarchy_event_processor.dart -->
<g id="node27" class="node">
<title>/lib/src/view_hierarchy/view_hierarchy_event_processor.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4627.5,-240C4627.5,-240 4416.5,-240 4416.5,-240 4410.5,-240 4404.5,-234 4404.5,-228 4404.5,-228 4404.5,-216 4404.5,-216 4404.5,-210 4410.5,-204 4416.5,-204 4416.5,-204 4627.5,-204 4627.5,-204 4633.5,-204 4639.5,-210 4639.5,-216 4639.5,-216 4639.5,-228 4639.5,-228 4639.5,-234 4633.5,-240 4627.5,-240"/>
<text text-anchor="middle" x="4522" y="-218.5" font-family="Arial" font-size="15.00">view_hierarchy_event_processor</text>
</g>
<!-- /lib/src/view_hierarchy/view_hierarchy_integration.dart&#45;&gt;/lib/src/view_hierarchy/view_hierarchy_event_processor.dart -->
<g id="edge48" class="edge">
<title>/lib/src/view_hierarchy/view_hierarchy_integration.dart&#45;&gt;/lib/src/view_hierarchy/view_hierarchy_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4522,-307.7C4522,-291.92 4522,-268.36 4522,-250.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4525.5,-250.05 4522,-240.05 4518.5,-250.05 4525.5,-250.05"/>
</g>
<!-- /lib/src/view_hierarchy/sentry_tree_walker.dart -->
<g id="node26" class="node">
<title>/lib/src/view_hierarchy/sentry_tree_walker.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4627.5,-158C4627.5,-158 4508.5,-158 4508.5,-158 4502.5,-158 4496.5,-152 4496.5,-146 4496.5,-146 4496.5,-134 4496.5,-134 4496.5,-128 4502.5,-122 4508.5,-122 4508.5,-122 4627.5,-122 4627.5,-122 4633.5,-122 4639.5,-128 4639.5,-134 4639.5,-134 4639.5,-146 4639.5,-146 4639.5,-152 4633.5,-158 4627.5,-158"/>
<text text-anchor="middle" x="4568" y="-136.5" font-family="Arial" font-size="15.00">sentry_tree_walker</text>
</g>
<!-- /lib/src/view_hierarchy/sentry_tree_walker.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge49" class="edge">
<title>/lib/src/view_hierarchy/sentry_tree_walker.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4591.24,-158.24C4609.14,-170.93 4635.12,-186.51 4661,-192 4686,-197.31 4865.64,-195.14 4891,-192 4940.6,-185.87 4995.4,-171.76 5036.14,-159.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5037.34,-163.08 5045.93,-156.87 5035.34,-156.37 5037.34,-163.08"/>
</g>
<!-- /lib/src/view_hierarchy/sentry_tree_walker.dart&#45;&gt;/lib/src/widget_utils.dart -->
<g id="edge50" class="edge">
<title>/lib/src/view_hierarchy/sentry_tree_walker.dart&#45;&gt;/lib/src/widget_utils.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4571.47,-121.64C4573.56,-111.19 4576.27,-97.67 4578.63,-85.86"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4582.06,-86.54 4580.59,-76.05 4575.2,-85.17 4582.06,-86.54"/>
</g>
<!-- /lib/src/view_hierarchy/view_hierarchy_event_processor.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge51" class="edge">
<title>/lib/src/view_hierarchy/view_hierarchy_event_processor.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4611.35,-203.94C4630.63,-200.82 4650.94,-197.93 4670,-196 4771.76,-185.68 4798.63,-201.66 4900,-188 4945.96,-181.81 4996.85,-169.61 5035.53,-159.19"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5036.86,-162.45 5045.59,-156.45 5035.02,-155.7 5036.86,-162.45"/>
</g>
<!-- /lib/src/view_hierarchy/view_hierarchy_event_processor.dart&#45;&gt;/lib/src/view_hierarchy/sentry_tree_walker.dart -->
<g id="edge52" class="edge">
<title>/lib/src/view_hierarchy/view_hierarchy_event_processor.dart&#45;&gt;/lib/src/view_hierarchy/sentry_tree_walker.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4531.98,-203.64C4538.17,-192.87 4546.24,-178.85 4553.17,-166.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4556.25,-168.46 4558.2,-158.05 4550.18,-164.97 4556.25,-168.46"/>
</g>
<!-- /lib/src/jvm/jvm_exception.dart&#45;&gt;/lib/src/jvm/jvm_frame.dart -->
<g id="edge53" class="edge">
<title>/lib/src/jvm/jvm_exception.dart&#45;&gt;/lib/src/jvm/jvm_frame.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M254.7,-121.64C253.92,-111.3 252.92,-97.94 252.04,-86.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="255.52,-85.76 251.28,-76.05 248.54,-86.28 255.52,-85.76"/>
</g>
<!-- /lib/src/native/method_channel_helper.dart -->
<g id="node30" class="node">
<title>/lib/src/native/method_channel_helper.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4283,-158C4283,-158 4131,-158 4131,-158 4125,-158 4119,-152 4119,-146 4119,-146 4119,-134 4119,-134 4119,-128 4125,-122 4131,-122 4131,-122 4283,-122 4283,-122 4289,-122 4295,-128 4295,-134 4295,-134 4295,-146 4295,-146 4295,-152 4289,-158 4283,-158"/>
<text text-anchor="middle" x="4207" y="-136.5" font-family="Arial" font-size="15.00">method_channel_helper</text>
</g>
<!-- /lib/src/native/native_scope_observer.dart&#45;&gt;/lib/src/native/sentry_native.dart -->
<g id="edge54" class="edge">
<title>/lib/src/native/native_scope_observer.dart&#45;&gt;/lib/src/native/sentry_native.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4023.04,-203.64C4024.85,-193.3 4027.19,-179.94 4029.24,-168.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4032.74,-168.5 4031.02,-158.05 4025.84,-167.3 4032.74,-168.5"/>
</g>
<!-- /lib/src/native/sentry_native_channel.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge55" class="edge">
<title>/lib/src/native/sentry_native_channel.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4259.22,-203.89C4271.18,-200.66 4283.93,-197.74 4296,-196 4428.85,-176.8 4766.78,-204.49 4900,-188 4946.03,-182.3 4996.91,-170.09 5035.57,-159.54"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5036.93,-162.8 5045.63,-156.76 5035.06,-156.05 5036.93,-162.8"/>
</g>
<!-- /lib/src/native/sentry_native_channel.dart&#45;&gt;/lib/src/native/method_channel_helper.dart -->
<g id="edge57" class="edge">
<title>/lib/src/native/sentry_native_channel.dart&#45;&gt;/lib/src/native/method_channel_helper.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4207,-203.64C4207,-193.3 4207,-179.94 4207,-168.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4210.5,-168.05 4207,-158.05 4203.5,-168.05 4210.5,-168.05"/>
</g>
<!-- /lib/src/native/sentry_native_channel.dart&#45;&gt;/lib/src/native/sentry_native.dart -->
<g id="edge56" class="edge">
<title>/lib/src/native/sentry_native_channel.dart&#45;&gt;/lib/src/native/sentry_native.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4140.98,-203.91C4127.45,-199.3 4113.55,-193.92 4101,-188 4086.55,-181.18 4071.42,-172.24 4059.09,-163.99"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4060.87,-160.96 4050.66,-158.14 4056.88,-166.72 4060.87,-160.96"/>
</g>
<!-- /lib/src/native/sentry_native.dart&#45;&gt;/lib/sentry_flutter.dart -->
<g id="edge58" class="edge">
<title>/lib/src/native/sentry_native.dart&#45;&gt;/lib/sentry_flutter.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4054.58,-158.18C4070.78,-171.02 4094.63,-186.83 4119,-192 4139.98,-196.45 4869.72,-194.63 4891,-192 4940.6,-185.87 4995.4,-171.76 5036.14,-159.79"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="5037.34,-163.08 5045.93,-156.87 5035.34,-156.37 5037.34,-163.08"/>
</g>
<!-- /lib/src/native/sentry_native.dart&#45;&gt;/lib/src/native/sentry_native_channel.dart -->
<g id="edge59" class="edge">
<title>/lib/src/native/sentry_native.dart&#45;&gt;/lib/src/native/sentry_native_channel.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M4066.41,-158.14C4082.04,-167.78 4101.04,-179.53 4119,-188 4128.51,-192.49 4138.8,-196.66 4148.89,-200.43"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4147.88,-203.78 4158.47,-203.91 4150.27,-197.21 4147.88,-203.78"/>
</g>
<!-- /lib/src/native/cocoa/binding.dart -->
<g id="node34" class="node">
<title>/lib/src/native/cocoa/binding.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M4360,-240C4360,-240 4320,-240 4320,-240 4314,-240 4308,-234 4308,-228 4308,-228 4308,-216 4308,-216 4308,-210 4314,-204 4320,-204 4320,-204 4360,-204 4360,-204 4366,-204 4372,-210 4372,-216 4372,-216 4372,-228 4372,-228 4372,-234 4366,-240 4360,-240"/>
<text text-anchor="middle" x="4340" y="-218.5" font-family="Arial" font-size="15.00">binding</text>
</g>
<!-- /lib/src/integrations/load_image_list_integration.dart -->
<g id="node36" class="node">
<title>/lib/src/integrations/load_image_list_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2868.5,-240C2868.5,-240 2695.5,-240 2695.5,-240 2689.5,-240 2683.5,-234 2683.5,-228 2683.5,-228 2683.5,-216 2683.5,-216 2683.5,-210 2689.5,-204 2695.5,-204 2695.5,-204 2868.5,-204 2868.5,-204 2874.5,-204 2880.5,-210 2880.5,-216 2880.5,-216 2880.5,-228 2880.5,-228 2880.5,-234 2874.5,-240 2868.5,-240"/>
<text text-anchor="middle" x="2782" y="-218.5" font-family="Arial" font-size="15.00">load_image_list_integration</text>
</g>
<!-- /lib/src/integrations/load_image_list_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge60" class="edge">
<title>/lib/src/integrations/load_image_list_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2782,-203.64C2782,-193.3 2782,-179.94 2782,-168.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2785.5,-168.05 2782,-158.05 2778.5,-168.05 2785.5,-168.05"/>
</g>
<!-- /lib/src/integrations/load_contexts_integration.dart -->
<g id="node37" class="node">
<title>/lib/src/integrations/load_contexts_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3073.5,-240C3073.5,-240 2910.5,-240 2910.5,-240 2904.5,-240 2898.5,-234 2898.5,-228 2898.5,-228 2898.5,-216 2898.5,-216 2898.5,-210 2904.5,-204 2910.5,-204 2910.5,-204 3073.5,-204 3073.5,-204 3079.5,-204 3085.5,-210 3085.5,-216 3085.5,-216 3085.5,-228 3085.5,-228 3085.5,-234 3079.5,-240 3073.5,-240"/>
<text text-anchor="middle" x="2992" y="-218.5" font-family="Arial" font-size="15.00">load_contexts_integration</text>
</g>
<!-- /lib/src/integrations/load_contexts_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge61" class="edge">
<title>/lib/src/integrations/load_contexts_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2947.21,-203.94C2914.73,-191.56 2870.63,-174.77 2836.17,-161.64"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2837.31,-158.33 2826.72,-158.04 2834.82,-164.87 2837.31,-158.33"/>
</g>
<!-- /lib/src/integrations/on_error_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge62" class="edge">
<title>/lib/src/integrations/on_error_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3570.75,-203.92C3559.72,-200.82 3548.06,-197.95 3537,-196 3480.86,-186.12 3465.87,-191.91 3409,-188 3218.07,-174.88 2994.58,-157.69 2872.68,-148.15"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2872.89,-144.66 2862.64,-147.37 2872.34,-151.64 2872.89,-144.66"/>
</g>
<!-- /lib/src/integrations/widgets_binding_integration.dart -->
<g id="node39" class="node">
<title>/lib/src/integrations/widgets_binding_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3292,-240C3292,-240 3116,-240 3116,-240 3110,-240 3104,-234 3104,-228 3104,-228 3104,-216 3104,-216 3104,-210 3110,-204 3116,-204 3116,-204 3292,-204 3292,-204 3298,-204 3304,-210 3304,-216 3304,-216 3304,-228 3304,-228 3304,-234 3298,-240 3292,-240"/>
<text text-anchor="middle" x="3204" y="-218.5" font-family="Arial" font-size="15.00">widgets_binding_integration</text>
</g>
<!-- /lib/src/integrations/widgets_binding_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge63" class="edge">
<title>/lib/src/integrations/widgets_binding_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3131.33,-203.96C3118.9,-201.19 3106.09,-198.44 3094,-196 3019.82,-181.04 2935.25,-166.27 2872.89,-155.81"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2873.13,-152.31 2862.68,-154.11 2871.97,-159.21 2873.13,-152.31"/>
</g>
<!-- /lib/src/integrations/widgets_binding_integration.dart&#45;&gt;/lib/src/widgets_binding_observer.dart -->
<g id="edge64" class="edge">
<title>/lib/src/integrations/widgets_binding_integration.dart&#45;&gt;/lib/src/widgets_binding_observer.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3269.91,-203.94C3319.03,-191.22 3386.2,-173.83 3437.48,-160.55"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3438.39,-163.93 3447.19,-158.04 3436.63,-157.16 3438.39,-163.93"/>
</g>
<!-- /lib/src/integrations/native_app_start_integration.dart -->
<g id="node40" class="node">
<title>/lib/src/integrations/native_app_start_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M3516,-240C3516,-240 3334,-240 3334,-240 3328,-240 3322,-234 3322,-228 3322,-228 3322,-216 3322,-216 3322,-210 3328,-204 3334,-204 3334,-204 3516,-204 3516,-204 3522,-204 3528,-210 3528,-216 3528,-216 3528,-228 3528,-228 3528,-234 3522,-240 3516,-240"/>
<text text-anchor="middle" x="3425" y="-218.5" font-family="Arial" font-size="15.00">native_app_start_integration</text>
</g>
<!-- /lib/src/integrations/native_app_start_integration.dart&#45;&gt;/lib/src/event_processor/native_app_start_event_processor.dart -->
<g id="edge67" class="edge">
<title>/lib/src/integrations/native_app_start_integration.dart&#45;&gt;/lib/src/event_processor/native_app_start_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3407.88,-240.14C3386.18,-261.43 3346.45,-294.99 3304,-304 3284.22,-308.2 1867.78,-308.2 1848,-304 1801.13,-294.05 1753.01,-266.32 1722.16,-245.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1723.8,-242.72 1713.55,-240.02 1719.87,-248.52 1723.8,-242.72"/>
</g>
<!-- /lib/src/integrations/native_app_start_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge65" class="edge">
<title>/lib/src/integrations/native_app_start_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3354.44,-203.94C3340.75,-201 3326.47,-198.19 3313,-196 3159.47,-171 2979.02,-155.14 2872.93,-147.18"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2873.13,-143.69 2862.9,-146.44 2872.61,-150.67 2873.13,-143.69"/>
</g>
<!-- /lib/src/integrations/native_app_start_integration.dart&#45;&gt;/lib/src/native/sentry_native.dart -->
<g id="edge66" class="edge">
<title>/lib/src/integrations/native_app_start_integration.dart&#45;&gt;/lib/src/native/sentry_native.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3490.95,-203.97C3505.99,-200.73 3521.97,-197.8 3537,-196 3561.28,-193.1 3954.07,-196.47 3977,-188 3989.91,-183.23 4002.01,-174.2 4011.72,-165.32"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="4014.2,-167.79 4018.95,-158.32 4009.33,-162.76 4014.2,-167.79"/>
</g>
<!-- /lib/src/integrations/load_release_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge68" class="edge">
<title>/lib/src/integrations/load_release_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M3747.53,-203.97C3734.31,-200.75 3720.26,-197.83 3707,-196 3641.38,-186.95 3475.16,-191.37 3409,-188 3217.87,-178.28 2994.45,-159.83 2872.62,-149.16"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2872.86,-145.67 2862.59,-148.28 2872.24,-152.65 2872.86,-145.67"/>
</g>
<!-- /lib/src/integrations/flutter_error_integration.dart -->
<g id="node42" class="node">
<title>/lib/src/integrations/flutter_error_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2012.5,-240C2012.5,-240 1863.5,-240 1863.5,-240 1857.5,-240 1851.5,-234 1851.5,-228 1851.5,-228 1851.5,-216 1851.5,-216 1851.5,-210 1857.5,-204 1863.5,-204 1863.5,-204 2012.5,-204 2012.5,-204 2018.5,-204 2024.5,-210 2024.5,-216 2024.5,-216 2024.5,-228 2024.5,-228 2024.5,-234 2018.5,-240 2012.5,-240"/>
<text text-anchor="middle" x="1938" y="-218.5" font-family="Arial" font-size="15.00">flutter_error_integration</text>
</g>
<!-- /lib/src/integrations/flutter_error_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge69" class="edge">
<title>/lib/src/integrations/flutter_error_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1994.34,-203.95C2006.95,-200.76 2020.34,-197.85 2033,-196 2140.36,-180.32 2168.71,-194.81 2277,-188 2422.07,-178.88 2590.04,-161.98 2691.05,-151.12"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2691.65,-154.58 2701.21,-150.03 2690.9,-147.62 2691.65,-154.58"/>
</g>
<!-- /lib/src/integrations/debug_print_integration.dart -->
<g id="node43" class="node">
<title>/lib/src/integrations/debug_print_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2203.5,-240C2203.5,-240 2054.5,-240 2054.5,-240 2048.5,-240 2042.5,-234 2042.5,-228 2042.5,-228 2042.5,-216 2042.5,-216 2042.5,-210 2048.5,-204 2054.5,-204 2054.5,-204 2203.5,-204 2203.5,-204 2209.5,-204 2215.5,-210 2215.5,-216 2215.5,-216 2215.5,-228 2215.5,-228 2215.5,-234 2209.5,-240 2203.5,-240"/>
<text text-anchor="middle" x="2129" y="-218.5" font-family="Arial" font-size="15.00">debug_print_integration</text>
</g>
<!-- /lib/src/integrations/debug_print_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge70" class="edge">
<title>/lib/src/integrations/debug_print_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2187.71,-203.99C2199.65,-200.98 2212.17,-198.13 2224,-196 2311.19,-180.33 2556.46,-159.14 2690.95,-148.2"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2691.55,-151.66 2701.24,-147.37 2690.99,-144.69 2691.55,-151.66"/>
</g>
<!-- /lib/src/integrations/screenshot_integration.dart&#45;&gt;/lib/src/event_processor/screenshot_event_processor.dart -->
<g id="edge71" class="edge">
<title>/lib/src/integrations/screenshot_integration.dart&#45;&gt;/lib/src/event_processor/screenshot_event_processor.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1850.74,-308.43C1849.48,-308.28 1848.24,-308.14 1847,-308 1814.85,-304.48 1586.78,-309.95 1556,-300 1522.55,-289.18 1489.99,-265.11 1468.48,-246.75"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1470.63,-243.98 1460.8,-240.03 1466.02,-249.24 1470.63,-243.98"/>
</g>
<!-- /lib/src/integrations/screenshot_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge72" class="edge">
<title>/lib/src/integrations/screenshot_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M1853.9,-307.97C1848.45,-305.57 1844.24,-302.92 1842,-300 1827.94,-281.66 1825.81,-212.49 1842,-196 1850.47,-187.38 2264.93,-188.67 2277,-188 2422.13,-179.92 2590.08,-162.72 2691.07,-151.52"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2691.68,-154.97 2701.23,-150.39 2690.91,-148.01 2691.68,-154.97"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/load_image_list_integration.dart -->
<g id="edge76" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/load_image_list_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2782,-307.7C2782,-291.92 2782,-268.36 2782,-250.09"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2785.5,-250.05 2782,-240.05 2778.5,-250.05 2785.5,-250.05"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/load_contexts_integration.dart -->
<g id="edge75" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/load_contexts_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2829.15,-308.95C2830.44,-308.62 2831.73,-308.3 2833,-308 2857.47,-302.22 2865.76,-309.6 2889,-300 2918.74,-287.71 2947.64,-264.85 2967.22,-247.22"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2969.78,-249.62 2974.76,-240.27 2965.04,-244.47 2969.78,-249.62"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/on_error_integration.dart -->
<g id="edge80" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/on_error_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2829.1,-308.73C2830.41,-308.46 2831.71,-308.22 2833,-308 2871.57,-301.52 3500.59,-314.28 3537,-300 3563.85,-289.47 3587.52,-266.21 3603.02,-248"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3605.91,-250 3609.55,-240.05 3600.5,-245.55 3605.91,-250"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/widgets_binding_integration.dart -->
<g id="edge81" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/widgets_binding_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2829.11,-308.76C2830.42,-308.48 2831.71,-308.23 2833,-308 2890.13,-297.83 3038.93,-318.29 3094,-300 3126.56,-289.18 3158.05,-265.35 3178.93,-247.04"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3181.29,-249.62 3186.39,-240.34 3176.61,-244.42 3181.29,-249.62"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/native_app_start_integration.dart -->
<g id="edge78" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/native_app_start_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2829.11,-308.74C2830.41,-308.47 2831.71,-308.22 2833,-308 2885.58,-299.02 3262.19,-316.25 3313,-300 3346.28,-289.36 3378.54,-265.26 3399.81,-246.84"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3402.26,-249.35 3407.41,-240.1 3397.61,-244.12 3402.26,-249.35"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/load_release_integration.dart -->
<g id="edge77" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/load_release_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2829.1,-308.72C2830.41,-308.46 2831.71,-308.22 2833,-308 2880.89,-300 3661.14,-315.97 3707,-300 3737.05,-289.54 3765.05,-265.93 3783.55,-247.59"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="3786.38,-249.71 3790.87,-240.11 3781.38,-244.81 3786.38,-249.71"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/flutter_error_integration.dart -->
<g id="edge74" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/flutter_error_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2734.75,-325.17C2580.6,-325.46 2098.4,-324.41 2033,-300 2004.11,-289.22 1977.42,-265.88 1959.74,-247.71"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="1962.14,-245.16 1952.73,-240.29 1957.05,-249.96 1962.14,-245.16"/>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/debug_print_integration.dart -->
<g id="edge73" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/debug_print_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2734.57,-324.57C2608.42,-323.19 2271.03,-317.86 2224,-300 2195.17,-289.05 2168.47,-265.74 2150.77,-247.62"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2153.17,-245.07 2143.75,-240.22 2148.1,-249.88 2153.17,-245.07"/>
</g>
<!-- /lib/src/integrations/widgets_flutter_binding_integration.dart -->
<g id="node46" class="node">
<title>/lib/src/integrations/widgets_flutter_binding_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2468.5,-240C2468.5,-240 2245.5,-240 2245.5,-240 2239.5,-240 2233.5,-234 2233.5,-228 2233.5,-228 2233.5,-216 2233.5,-216 2233.5,-210 2239.5,-204 2245.5,-204 2245.5,-204 2468.5,-204 2468.5,-204 2474.5,-204 2480.5,-210 2480.5,-216 2480.5,-216 2480.5,-228 2480.5,-228 2480.5,-234 2474.5,-240 2468.5,-240"/>
<text text-anchor="middle" x="2357" y="-218.5" font-family="Arial" font-size="15.00">widgets_flutter_binding_integration</text>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/widgets_flutter_binding_integration.dart -->
<g id="edge82" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/widgets_flutter_binding_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2734.85,-323.46C2664.18,-320.73 2534.34,-313.97 2490,-300 2452.27,-288.12 2413.64,-264.18 2387.8,-246.13"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2389.73,-243.2 2379.55,-240.26 2385.67,-248.91 2389.73,-243.2"/>
</g>
<!-- /lib/src/integrations/native_sdk_integration.dart -->
<g id="node47" class="node">
<title>/lib/src/integrations/native_sdk_integration.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M2653,-240C2653,-240 2511,-240 2511,-240 2505,-240 2499,-234 2499,-228 2499,-228 2499,-216 2499,-216 2499,-210 2505,-204 2511,-204 2511,-204 2653,-204 2653,-204 2659,-204 2665,-210 2665,-216 2665,-216 2665,-228 2665,-228 2665,-234 2659,-240 2653,-240"/>
<text text-anchor="middle" x="2582" y="-218.5" font-family="Arial" font-size="15.00">native_sdk_integration</text>
</g>
<!-- /lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/native_sdk_integration.dart -->
<g id="edge79" class="edge">
<title>/lib/src/integrations/integrations.dart&#45;&gt;/lib/src/integrations/native_sdk_integration.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" stroke-dasharray="5,2" d="M2734.91,-319.04C2715.35,-315.33 2692.86,-309.4 2674,-300 2647.65,-286.87 2622.35,-264.77 2604.97,-247.6"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2607.09,-244.76 2597.57,-240.11 2602.11,-249.68 2607.09,-244.76"/>
</g>
<!-- /lib/src/integrations/widgets_flutter_binding_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge83" class="edge">
<title>/lib/src/integrations/widgets_flutter_binding_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2447.39,-203.99C2519.32,-190.45 2619.5,-171.59 2691.46,-158.04"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2692.25,-161.46 2701.43,-156.17 2690.95,-154.58 2692.25,-161.46"/>
</g>
<!-- /lib/src/integrations/native_sdk_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart -->
<g id="edge84" class="edge">
<title>/lib/src/integrations/native_sdk_integration.dart&#45;&gt;/lib/src/sentry_flutter_options.dart</title>
<path fill="none" stroke="black" stroke-width="1.3" d="M2624.91,-203.84C2655.62,-191.55 2697.14,-174.95 2729.78,-161.89"/>
<polygon fill="black" stroke="black" stroke-width="1.3" points="2731.18,-165.1 2739.17,-158.13 2728.58,-158.6 2731.18,-165.1"/>
</g>
<!-- /lib/sentry_flutter_web.dart -->
<g id="node48" class="node">
<title>/lib/sentry_flutter_web.dart</title>
<path fill="Lavender" stroke="black" stroke-width="1.3" d="M152,-418C152,-418 36,-418 36,-418 30,-418 24,-412 24,-406 24,-406 24,-394 24,-394 24,-388 30,-382 36,-382 36,-382 152,-382 152,-382 158,-382 164,-388 164,-394 164,-394 164,-406 164,-406 164,-412 158,-418 152,-418"/>
<text text-anchor="middle" x="94" y="-396.5" font-family="Arial" font-size="15.00">sentry_flutter_web</text>
</g>
</g>
</svg>
