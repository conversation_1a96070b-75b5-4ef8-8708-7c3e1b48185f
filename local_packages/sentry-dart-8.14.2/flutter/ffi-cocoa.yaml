name: SentryCocoa
description: Sentry Cocoa SDK FFI binding.
language: objc
output: lib/src/native/cocoa/binding.dart
headers:
  entry-points:
    - ./temp/Sentry.framework/PrivateHeaders/PrivateSentrySDKOnly.h
    - ./temp/Sentry.framework/Headers/Sentry-Swift.h
compiler-opts:
  - -DSENTRY_TARGET_PROFILING_SUPPORTED=1
  - -DCOCOAPODS=1
  - '-I./temp/Sentry.framework/Headers/'
  - '-I./temp/Sentry.framework/PrivateHeaders/'
exclude-all-by-default: true
objc-interfaces:
  include:
    - PrivateSentrySDKOnly
    - SentryId
  module:
    'SentryId': 'Sentry'
