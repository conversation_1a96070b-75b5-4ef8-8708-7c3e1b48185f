name: sentry_flutter
version: 8.14.2
description: Sentry SDK for Flutter. This package aims to support different Flutter targets by relying on the many platforms supported by <PERSON><PERSON> with native SDKs.
homepage: https://docs.sentry.io/platforms/flutter/
repository: https://github.com/getsentry/sentry-dart
issue_tracker: https://github.com/getsentry/sentry-dart/issues
documentation: https://docs.sentry.io/platforms/flutter/

environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: '>=3.0.0'

platforms:
  android:
  ios:
  macos:
  linux:
  windows:
  web:

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  sentry:
    path: ../dart
  package_info_plus: '>=1.0.0'
  meta: ^1.3.0
  ffi: ^2.0.0
  file: '>=6.1.4'
  collection: ^1.16.0

dev_dependencies:
  build_runner: ^2.4.2
  flutter_test:
    sdk: flutter
  mockito: ^5.1.0
  yaml: ^3.1.0 # needed for version match (code and pubspec)
  flutter_lints: '>=4.0.0'
  remove_from_coverage: ^2.0.0
  http: ^1.2.2 # check if js sdk cdn bundle exists
  flutter_localizations:
    sdk: flutter
  ffigen:
    git:
      url: https://github.com/getsentry/ffigen
      ref: 6aa2c2642f507eab3df83373189170797a9fa5e7

flutter:
  plugin:
    platforms:
      android:
        pluginClass: SentryFlutterPlugin
        package: io.sentry.flutter
      ios:
        pluginClass: SentryFlutterPlugin
      macos:
        pluginClass: SentryFlutterPlugin
      web:
        pluginClass: SentryFlutterWeb
        fileName: sentry_flutter_web.dart
        # Note, we cannot use `ffiPlugin: true` on Linux and windows because flutter won't add `target_link_libraries()`
        # so sentry-native won't even build during the build process (since it doesn't need to).
      linux:
        pluginClass: SentryFlutterPlugin
      windows:
        pluginClass: SentryFlutterPlugin
