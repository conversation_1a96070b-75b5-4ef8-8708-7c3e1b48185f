# Run with `dart ffigen --config ffi-native.yaml`.
name: SentryNative
description: Sentry Native SDK FFI binding.
output: lib/src/native/c/binding.dart
headers:
  entry-points:
    - ./temp/sentry-native.h
exclude-all-by-default: true
functions:
  include:
    - sentry_init
    - sentry_close
    - sentry_options_new
    - sentry_options_set_dsn
    - sentry_options_set_debug
    - sentry_options_set_environment
    - sentry_options_set_release
    - sentry_options_set_auto_session_tracking
    - sentry_options_set_dist
    - sentry_options_set_max_breadcrumbs
    - sentry_options_set_handler_path
    - sentry_set_user
    - sentry_remove_user
    - sentry_add_breadcrumb
    - sentry_set_context
    - sentry_remove_context
    - sentry_set_extra
    - sentry_remove_extra
    - sentry_set_tag
    - sentry_remove_tag
    - sentry_get_modules_list
    - sentry_value_get_type
    - sentry_value_get_length
    - sentry_value_get_by_index
    - sentry_value_decref
    - sentry_value_set_by_key
    - sentry_value_get_by_key
    - sentry_value_remove_by_key
    - sentry_value_is_null
    - sentry_value_is_true
    - sentry_value_new_null
    - sentry_value_new_int32
    - sentry_value_new_double
    - sentry_value_new_string
    - sentry_value_new_list
    - sentry_value_new_object
    - sentry_value_new_bool
    - sentry_value_append
    - sentry_value_as_int32
    - sentry_value_as_double
    - sentry_value_as_string
    - sentry_value_as_list
    - sentry_value_as_object
    # For tests only:
    - sentry_sdk_version
    - sentry_sdk_name
    - sentry_options_free
    - sentry_options_get_dsn
    - sentry_options_get_debug
    - sentry_options_get_environment
    - sentry_options_get_release
    - sentry_options_get_auto_session_tracking
    - sentry_options_get_dist
    - sentry_options_get_max_breadcrumbs
  rename:
    'sentry_(.*)': '$1'
structs:
  dependency-only: opaque
unions:
  dependency-only: opaque
comments:
  style: any
  length: full
preamble: |
  // ignore_for_file: unused_field
