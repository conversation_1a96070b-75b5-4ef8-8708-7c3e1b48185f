<?xml version="1.0" ?>
<SmellBaseline>
  <ManuallySuppressedIssues></ManuallySuppressedIssues>
  <CurrentIssues>
    <ID>CyclomaticComplexMethod:SentryFlutterPlugin.kt$SentryFlutterPlugin$override fun onMethodCall(call: <PERSON><PERSON><PERSON>, result: Result)</ID>
    <ID>LongMethod:SentryFlutter.kt$SentryFlutter$fun updateOptions( options: SentryAndroidOptions, data: Map&lt;String, Any&gt;, )</ID>
    <ID>MagicNumber:MainActivity.kt$MainActivity$6_000</ID>
    <ID>TooGenericExceptionCaught:MainActivity.kt$MainActivity$e: Exception</ID>
    <ID>TooGenericExceptionThrown:MainActivity.kt$MainActivity$throw Exception("Catch this java exception thrown from Kotlin thread!")</ID>
    <ID>TooGenericExceptionThrown:MainActivity.kt$MainActivity$throw RuntimeException("Catch this java exception!")</ID>
    <ID>TooGenericExceptionThrown:MainActivity.kt$MainActivity$throw RuntimeException("Catch this platform exception!")</ID>
    <ID>TooManyFunctions:SentryFlutterPlugin.kt$SentryFlutterPlugin : FlutterPluginMethodCallHandlerActivityAware</ID>
  </CurrentIssues>
</SmellBaseline>
