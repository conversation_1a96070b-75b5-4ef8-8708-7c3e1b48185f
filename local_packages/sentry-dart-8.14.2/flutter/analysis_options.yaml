include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - test/*.mocks.dart
    - microbenchmarks/**
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true
  errors:
    # treat missing required parameters as a warning (not a hint)
    missing_required_param: error
    # treat missing returns as a warning (not a hint)
    missing_return: error
    # allow having TODOs in the code
    todo: ignore
    # allow self-reference to deprecated members (we do this because otherwise we have
    # to annotate every member in every test, assert, etc, when we deprecate something)
    deprecated_member_use_from_same_package: warning
    # ignore sentry/path on pubspec as we change it on deployment
    invalid_dependency: ignore

linter:
  rules:
    prefer_relative_imports: true
    unnecessary_brace_in_string_interps: true
    implementation_imports: true
    prefer_const_literals_to_create_immutables: false
    prefer_const_constructors: false
    library_private_types_in_public_api: false
    no_leading_underscores_for_local_identifiers: false
    prefer_function_declarations_over_variables: false
    unawaited_futures: true
