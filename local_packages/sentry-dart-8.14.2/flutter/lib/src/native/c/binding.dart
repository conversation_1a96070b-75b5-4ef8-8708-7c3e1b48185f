// ignore_for_file: unused_field

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
import 'dart:ffi' as ffi;

/// Sentry Native SDK FFI binding.
class SentryNative {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  SentryNative(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  SentryNative.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  /// Decrements the reference count on the value.
  void value_decref(
    sentry_value_u value,
  ) {
    return _value_decref(
      value,
    );
  }

  late final _value_decrefPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(sentry_value_u)>>(
          'sentry_value_decref');
  late final _value_decref =
      _value_decrefPtr.asFunction<void Function(sentry_value_u)>();

  /// Creates a null value.
  sentry_value_u value_new_null() {
    return _value_new_null();
  }

  late final _value_new_nullPtr =
      _lookup<ffi.NativeFunction<sentry_value_u Function()>>(
          'sentry_value_new_null');
  late final _value_new_null =
      _value_new_nullPtr.asFunction<sentry_value_u Function()>();

  /// Creates a new 32-bit signed integer value.
  sentry_value_u value_new_int32(
    int value,
  ) {
    return _value_new_int32(
      value,
    );
  }

  late final _value_new_int32Ptr =
      _lookup<ffi.NativeFunction<sentry_value_u Function(ffi.Int32)>>(
          'sentry_value_new_int32');
  late final _value_new_int32 =
      _value_new_int32Ptr.asFunction<sentry_value_u Function(int)>();

  /// Creates a new double value.
  sentry_value_u value_new_double(
    double value,
  ) {
    return _value_new_double(
      value,
    );
  }

  late final _value_new_doublePtr =
      _lookup<ffi.NativeFunction<sentry_value_u Function(ffi.Double)>>(
          'sentry_value_new_double');
  late final _value_new_double =
      _value_new_doublePtr.asFunction<sentry_value_u Function(double)>();

  /// Creates a new boolean value.
  sentry_value_u value_new_bool(
    int value,
  ) {
    return _value_new_bool(
      value,
    );
  }

  late final _value_new_boolPtr =
      _lookup<ffi.NativeFunction<sentry_value_u Function(ffi.Int)>>(
          'sentry_value_new_bool');
  late final _value_new_bool =
      _value_new_boolPtr.asFunction<sentry_value_u Function(int)>();

  /// Creates a new null terminated string.
  sentry_value_u value_new_string(
    ffi.Pointer<ffi.Char> value,
  ) {
    return _value_new_string(
      value,
    );
  }

  late final _value_new_stringPtr = _lookup<
          ffi.NativeFunction<sentry_value_u Function(ffi.Pointer<ffi.Char>)>>(
      'sentry_value_new_string');
  late final _value_new_string = _value_new_stringPtr
      .asFunction<sentry_value_u Function(ffi.Pointer<ffi.Char>)>();

  /// Creates a new list value.
  sentry_value_u value_new_list() {
    return _value_new_list();
  }

  late final _value_new_listPtr =
      _lookup<ffi.NativeFunction<sentry_value_u Function()>>(
          'sentry_value_new_list');
  late final _value_new_list =
      _value_new_listPtr.asFunction<sentry_value_u Function()>();

  /// Creates a new object.
  sentry_value_u value_new_object() {
    return _value_new_object();
  }

  late final _value_new_objectPtr =
      _lookup<ffi.NativeFunction<sentry_value_u Function()>>(
          'sentry_value_new_object');
  late final _value_new_object =
      _value_new_objectPtr.asFunction<sentry_value_u Function()>();

  /// Returns the type of the value passed.
  int value_get_type(
    sentry_value_u value,
  ) {
    return _value_get_type(
      value,
    );
  }

  late final _value_get_typePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(sentry_value_u)>>(
          'sentry_value_get_type');
  late final _value_get_type =
      _value_get_typePtr.asFunction<int Function(sentry_value_u)>();

  /// Sets a key to a value in the map.
  ///
  /// This moves the ownership of the value into the map.  The caller does not
  /// have to call `sentry_value_decref` on it.
  int value_set_by_key(
    sentry_value_u value,
    ffi.Pointer<ffi.Char> k,
    sentry_value_u v,
  ) {
    return _value_set_by_key(
      value,
      k,
      v,
    );
  }

  late final _value_set_by_keyPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(sentry_value_u, ffi.Pointer<ffi.Char>,
              sentry_value_u)>>('sentry_value_set_by_key');
  late final _value_set_by_key = _value_set_by_keyPtr.asFunction<
      int Function(sentry_value_u, ffi.Pointer<ffi.Char>, sentry_value_u)>();

  /// This removes a value from the map by key.
  int value_remove_by_key(
    sentry_value_u value,
    ffi.Pointer<ffi.Char> k,
  ) {
    return _value_remove_by_key(
      value,
      k,
    );
  }

  late final _value_remove_by_keyPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(sentry_value_u,
              ffi.Pointer<ffi.Char>)>>('sentry_value_remove_by_key');
  late final _value_remove_by_key = _value_remove_by_keyPtr
      .asFunction<int Function(sentry_value_u, ffi.Pointer<ffi.Char>)>();

  /// Appends a value to a list.
  ///
  /// This moves the ownership of the value into the list.  The caller does not
  /// have to call `sentry_value_decref` on it.
  int value_append(
    sentry_value_u value,
    sentry_value_u v,
  ) {
    return _value_append(
      value,
      v,
    );
  }

  late final _value_appendPtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(sentry_value_u, sentry_value_u)>>(
      'sentry_value_append');
  late final _value_append = _value_appendPtr
      .asFunction<int Function(sentry_value_u, sentry_value_u)>();

  /// Looks up a value in a map by key.  If missing a null value is returned.
  /// The returned value is borrowed.
  sentry_value_u value_get_by_key(
    sentry_value_u value,
    ffi.Pointer<ffi.Char> k,
  ) {
    return _value_get_by_key(
      value,
      k,
    );
  }

  late final _value_get_by_keyPtr = _lookup<
      ffi.NativeFunction<
          sentry_value_u Function(sentry_value_u,
              ffi.Pointer<ffi.Char>)>>('sentry_value_get_by_key');
  late final _value_get_by_key = _value_get_by_keyPtr.asFunction<
      sentry_value_u Function(sentry_value_u, ffi.Pointer<ffi.Char>)>();

  /// Looks up a value in a list by index.  If missing a null value is returned.
  /// The returned value is borrowed.
  sentry_value_u value_get_by_index(
    sentry_value_u value,
    int index,
  ) {
    return _value_get_by_index(
      value,
      index,
    );
  }

  late final _value_get_by_indexPtr = _lookup<
          ffi
          .NativeFunction<sentry_value_u Function(sentry_value_u, ffi.Size)>>(
      'sentry_value_get_by_index');
  late final _value_get_by_index = _value_get_by_indexPtr
      .asFunction<sentry_value_u Function(sentry_value_u, int)>();

  /// Returns the length of the given map or list.
  ///
  /// If an item is not a list or map the return value is 0.
  int value_get_length(
    sentry_value_u value,
  ) {
    return _value_get_length(
      value,
    );
  }

  late final _value_get_lengthPtr =
      _lookup<ffi.NativeFunction<ffi.Size Function(sentry_value_u)>>(
          'sentry_value_get_length');
  late final _value_get_length =
      _value_get_lengthPtr.asFunction<int Function(sentry_value_u)>();

  /// Converts a value into a 32bit signed integer.
  int value_as_int32(
    sentry_value_u value,
  ) {
    return _value_as_int32(
      value,
    );
  }

  late final _value_as_int32Ptr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(sentry_value_u)>>(
          'sentry_value_as_int32');
  late final _value_as_int32 =
      _value_as_int32Ptr.asFunction<int Function(sentry_value_u)>();

  /// Converts a value into a double value.
  double value_as_double(
    sentry_value_u value,
  ) {
    return _value_as_double(
      value,
    );
  }

  late final _value_as_doublePtr =
      _lookup<ffi.NativeFunction<ffi.Double Function(sentry_value_u)>>(
          'sentry_value_as_double');
  late final _value_as_double =
      _value_as_doublePtr.asFunction<double Function(sentry_value_u)>();

  /// Returns the value as c string.
  ffi.Pointer<ffi.Char> value_as_string(
    sentry_value_u value,
  ) {
    return _value_as_string(
      value,
    );
  }

  late final _value_as_stringPtr = _lookup<
          ffi.NativeFunction<ffi.Pointer<ffi.Char> Function(sentry_value_u)>>(
      'sentry_value_as_string');
  late final _value_as_string = _value_as_stringPtr
      .asFunction<ffi.Pointer<ffi.Char> Function(sentry_value_u)>();

  /// Returns `true` if the value is boolean true.
  int value_is_true(
    sentry_value_u value,
  ) {
    return _value_is_true(
      value,
    );
  }

  late final _value_is_truePtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(sentry_value_u)>>(
          'sentry_value_is_true');
  late final _value_is_true =
      _value_is_truePtr.asFunction<int Function(sentry_value_u)>();

  /// Returns `true` if the value is null.
  int value_is_null(
    sentry_value_u value,
  ) {
    return _value_is_null(
      value,
    );
  }

  late final _value_is_nullPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(sentry_value_u)>>(
          'sentry_value_is_null');
  late final _value_is_null =
      _value_is_nullPtr.asFunction<int Function(sentry_value_u)>();

  /// Creates a new options struct.
  /// Can be freed with `sentry_options_free`.
  ffi.Pointer<sentry_options_s> options_new() {
    return _options_new();
  }

  late final _options_newPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<sentry_options_s> Function()>>(
          'sentry_options_new');
  late final _options_new =
      _options_newPtr.asFunction<ffi.Pointer<sentry_options_s> Function()>();

  /// Deallocates previously allocated sentry options.
  void options_free(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_free(
      opts,
    );
  }

  late final _options_freePtr = _lookup<
          ffi.NativeFunction<ffi.Void Function(ffi.Pointer<sentry_options_s>)>>(
      'sentry_options_free');
  late final _options_free = _options_freePtr
      .asFunction<void Function(ffi.Pointer<sentry_options_s>)>();

  /// Sets the DSN.
  void options_set_dsn(
    ffi.Pointer<sentry_options_s> opts,
    ffi.Pointer<ffi.Char> dsn,
  ) {
    return _options_set_dsn(
      opts,
      dsn,
    );
  }

  late final _options_set_dsnPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Pointer<ffi.Char>)>>('sentry_options_set_dsn');
  late final _options_set_dsn = _options_set_dsnPtr.asFunction<
      void Function(ffi.Pointer<sentry_options_s>, ffi.Pointer<ffi.Char>)>();

  /// Gets the DSN.
  ffi.Pointer<ffi.Char> options_get_dsn(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_get_dsn(
      opts,
    );
  }

  late final _options_get_dsnPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<sentry_options_s>)>>('sentry_options_get_dsn');
  late final _options_get_dsn = _options_get_dsnPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(ffi.Pointer<sentry_options_s>)>();

  /// Sets the release.
  void options_set_release(
    ffi.Pointer<sentry_options_s> opts,
    ffi.Pointer<ffi.Char> release,
  ) {
    return _options_set_release(
      opts,
      release,
    );
  }

  late final _options_set_releasePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Pointer<ffi.Char>)>>('sentry_options_set_release');
  late final _options_set_release = _options_set_releasePtr.asFunction<
      void Function(ffi.Pointer<sentry_options_s>, ffi.Pointer<ffi.Char>)>();

  /// Gets the release.
  ffi.Pointer<ffi.Char> options_get_release(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_get_release(
      opts,
    );
  }

  late final _options_get_releasePtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<sentry_options_s>)>>('sentry_options_get_release');
  late final _options_get_release = _options_get_releasePtr.asFunction<
      ffi.Pointer<ffi.Char> Function(ffi.Pointer<sentry_options_s>)>();

  /// Sets the environment.
  void options_set_environment(
    ffi.Pointer<sentry_options_s> opts,
    ffi.Pointer<ffi.Char> environment,
  ) {
    return _options_set_environment(
      opts,
      environment,
    );
  }

  late final _options_set_environmentPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Pointer<ffi.Char>)>>('sentry_options_set_environment');
  late final _options_set_environment = _options_set_environmentPtr.asFunction<
      void Function(ffi.Pointer<sentry_options_s>, ffi.Pointer<ffi.Char>)>();

  /// Gets the environment.
  ffi.Pointer<ffi.Char> options_get_environment(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_get_environment(
      opts,
    );
  }

  late final _options_get_environmentPtr = _lookup<
          ffi.NativeFunction<
              ffi.Pointer<ffi.Char> Function(ffi.Pointer<sentry_options_s>)>>(
      'sentry_options_get_environment');
  late final _options_get_environment = _options_get_environmentPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(ffi.Pointer<sentry_options_s>)>();

  /// Sets the dist.
  void options_set_dist(
    ffi.Pointer<sentry_options_s> opts,
    ffi.Pointer<ffi.Char> dist,
  ) {
    return _options_set_dist(
      opts,
      dist,
    );
  }

  late final _options_set_distPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Pointer<ffi.Char>)>>('sentry_options_set_dist');
  late final _options_set_dist = _options_set_distPtr.asFunction<
      void Function(ffi.Pointer<sentry_options_s>, ffi.Pointer<ffi.Char>)>();

  /// Gets the dist.
  ffi.Pointer<ffi.Char> options_get_dist(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_get_dist(
      opts,
    );
  }

  late final _options_get_distPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<sentry_options_s>)>>('sentry_options_get_dist');
  late final _options_get_dist = _options_get_distPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(ffi.Pointer<sentry_options_s>)>();

  /// Enables or disables debug printing mode.
  void options_set_debug(
    ffi.Pointer<sentry_options_s> opts,
    int debug,
  ) {
    return _options_set_debug(
      opts,
      debug,
    );
  }

  late final _options_set_debugPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Int)>>('sentry_options_set_debug');
  late final _options_set_debug = _options_set_debugPtr
      .asFunction<void Function(ffi.Pointer<sentry_options_s>, int)>();

  /// Returns the current value of the debug flag.
  int options_get_debug(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_get_debug(
      opts,
    );
  }

  late final _options_get_debugPtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(ffi.Pointer<sentry_options_s>)>>(
      'sentry_options_get_debug');
  late final _options_get_debug = _options_get_debugPtr
      .asFunction<int Function(ffi.Pointer<sentry_options_s>)>();

  /// Sets the number of breadcrumbs being tracked and attached to events.
  ///
  /// Defaults to 100.
  void options_set_max_breadcrumbs(
    ffi.Pointer<sentry_options_s> opts,
    int max_breadcrumbs,
  ) {
    return _options_set_max_breadcrumbs(
      opts,
      max_breadcrumbs,
    );
  }

  late final _options_set_max_breadcrumbsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Size)>>('sentry_options_set_max_breadcrumbs');
  late final _options_set_max_breadcrumbs = _options_set_max_breadcrumbsPtr
      .asFunction<void Function(ffi.Pointer<sentry_options_s>, int)>();

  /// Gets the number of breadcrumbs being tracked and attached to events.
  int options_get_max_breadcrumbs(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_get_max_breadcrumbs(
      opts,
    );
  }

  late final _options_get_max_breadcrumbsPtr = _lookup<
          ffi.NativeFunction<ffi.Size Function(ffi.Pointer<sentry_options_s>)>>(
      'sentry_options_get_max_breadcrumbs');
  late final _options_get_max_breadcrumbs = _options_get_max_breadcrumbsPtr
      .asFunction<int Function(ffi.Pointer<sentry_options_s>)>();

  /// Enables or disables automatic session tracking.
  ///
  /// Automatic session tracking is enabled by default and is equivalent to calling
  /// `sentry_start_session` after startup.
  /// There can only be one running session, and the current session will always be
  /// closed implicitly by `sentry_close`, when starting a new session with
  /// `sentry_start_session`, or manually by calling `sentry_end_session`.
  void options_set_auto_session_tracking(
    ffi.Pointer<sentry_options_s> opts,
    int val,
  ) {
    return _options_set_auto_session_tracking(
      opts,
      val,
    );
  }

  late final _options_set_auto_session_trackingPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Int)>>('sentry_options_set_auto_session_tracking');
  late final _options_set_auto_session_tracking =
      _options_set_auto_session_trackingPtr
          .asFunction<void Function(ffi.Pointer<sentry_options_s>, int)>();

  /// Returns true if automatic session tracking is enabled.
  int options_get_auto_session_tracking(
    ffi.Pointer<sentry_options_s> opts,
  ) {
    return _options_get_auto_session_tracking(
      opts,
    );
  }

  late final _options_get_auto_session_trackingPtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(ffi.Pointer<sentry_options_s>)>>(
      'sentry_options_get_auto_session_tracking');
  late final _options_get_auto_session_tracking =
      _options_get_auto_session_trackingPtr
          .asFunction<int Function(ffi.Pointer<sentry_options_s>)>();

  /// Sets the path to the crashpad handler if the crashpad backend is used.
  ///
  /// The path defaults to the `crashpad_handler`/`crashpad_handler.exe`
  /// executable, depending on platform, which is expected to be present in the
  /// same directory as the app executable.
  ///
  /// It is recommended that library users set an explicit handler path, depending
  /// on the directory/executable structure of their app.
  ///
  /// `path` is assumed to be in platform-specific filesystem path encoding.
  /// API Users on windows are encouraged to use `sentry_options_set_handler_pathw`
  /// instead.
  void options_set_handler_path(
    ffi.Pointer<sentry_options_s> opts,
    ffi.Pointer<ffi.Char> path,
  ) {
    return _options_set_handler_path(
      opts,
      path,
    );
  }

  late final _options_set_handler_pathPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<sentry_options_s>,
              ffi.Pointer<ffi.Char>)>>('sentry_options_set_handler_path');
  late final _options_set_handler_path =
      _options_set_handler_pathPtr.asFunction<
          void Function(
              ffi.Pointer<sentry_options_s>, ffi.Pointer<ffi.Char>)>();

  /// Initializes the Sentry SDK with the specified options.
  ///
  /// This takes ownership of the options.  After the options have been set
  /// they cannot be modified any more.
  /// Depending on the configured transport and backend, this function might not be
  /// fully thread-safe.
  /// Returns 0 on success.
  int init(
    ffi.Pointer<sentry_options_s> options,
  ) {
    return _init(
      options,
    );
  }

  late final _initPtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(ffi.Pointer<sentry_options_s>)>>(
      'sentry_init');
  late final _init =
      _initPtr.asFunction<int Function(ffi.Pointer<sentry_options_s>)>();

  /// Shuts down the sentry client and forces transports to flush out.
  ///
  /// Returns 0 on success.
  ///
  /// Note that this does not uninstall any crash handler installed by our
  /// backends, which will still process crashes after `sentry_close()`, except
  /// when using `crashpad` on Linux or the `inproc` backend.
  ///
  /// Further note that this function will block the thread it was called from
  /// until the sentry background worker has finished its work or it timed out,
  /// whichever comes first.
  int close() {
    return _close();
  }

  late final _closePtr =
      _lookup<ffi.NativeFunction<ffi.Int Function()>>('sentry_close');
  late final _close = _closePtr.asFunction<int Function()>();

  /// This will lazily load and cache a list of all the loaded libraries.
  ///
  /// Returns a new reference to an immutable, frozen list.
  /// The reference must be released with `sentry_value_decref`.
  sentry_value_u get_modules_list() {
    return _get_modules_list();
  }

  late final _get_modules_listPtr =
      _lookup<ffi.NativeFunction<sentry_value_u Function()>>(
          'sentry_get_modules_list');
  late final _get_modules_list =
      _get_modules_listPtr.asFunction<sentry_value_u Function()>();

  /// Adds the breadcrumb to be sent in case of an event.
  void add_breadcrumb(
    sentry_value_u breadcrumb,
  ) {
    return _add_breadcrumb(
      breadcrumb,
    );
  }

  late final _add_breadcrumbPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(sentry_value_u)>>(
          'sentry_add_breadcrumb');
  late final _add_breadcrumb =
      _add_breadcrumbPtr.asFunction<void Function(sentry_value_u)>();

  /// Sets the specified user.
  void set_user(
    sentry_value_u user,
  ) {
    return _set_user(
      user,
    );
  }

  late final _set_userPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(sentry_value_u)>>(
          'sentry_set_user');
  late final _set_user =
      _set_userPtr.asFunction<void Function(sentry_value_u)>();

  /// Removes a user.
  void remove_user() {
    return _remove_user();
  }

  late final _remove_userPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('sentry_remove_user');
  late final _remove_user = _remove_userPtr.asFunction<void Function()>();

  /// Sets a tag.
  void set_tag(
    ffi.Pointer<ffi.Char> key,
    ffi.Pointer<ffi.Char> value,
  ) {
    return _set_tag(
      key,
      value,
    );
  }

  late final _set_tagPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>>('sentry_set_tag');
  late final _set_tag = _set_tagPtr.asFunction<
      void Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();

  /// Removes the tag with the specified key.
  void remove_tag(
    ffi.Pointer<ffi.Char> key,
  ) {
    return _remove_tag(
      key,
    );
  }

  late final _remove_tagPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'sentry_remove_tag');
  late final _remove_tag =
      _remove_tagPtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  /// Sets extra information.
  void set_extra(
    ffi.Pointer<ffi.Char> key,
    sentry_value_u value,
  ) {
    return _set_extra(
      key,
      value,
    );
  }

  late final _set_extraPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Char>, sentry_value_u)>>('sentry_set_extra');
  late final _set_extra = _set_extraPtr
      .asFunction<void Function(ffi.Pointer<ffi.Char>, sentry_value_u)>();

  /// Removes the extra with the specified key.
  void remove_extra(
    ffi.Pointer<ffi.Char> key,
  ) {
    return _remove_extra(
      key,
    );
  }

  late final _remove_extraPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'sentry_remove_extra');
  late final _remove_extra =
      _remove_extraPtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  /// Sets a context object.
  void set_context(
    ffi.Pointer<ffi.Char> key,
    sentry_value_u value,
  ) {
    return _set_context(
      key,
      value,
    );
  }

  late final _set_contextPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Char>, sentry_value_u)>>('sentry_set_context');
  late final _set_context = _set_contextPtr
      .asFunction<void Function(ffi.Pointer<ffi.Char>, sentry_value_u)>();

  /// Removes the context object with the specified key.
  void remove_context(
    ffi.Pointer<ffi.Char> key,
  ) {
    return _remove_context(
      key,
    );
  }

  late final _remove_contextPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'sentry_remove_context');
  late final _remove_context =
      _remove_contextPtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  /// Sentry SDK version.
  ffi.Pointer<ffi.Char> sdk_version() {
    return _sdk_version();
  }

  late final _sdk_versionPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Char> Function()>>(
          'sentry_sdk_version');
  late final _sdk_version =
      _sdk_versionPtr.asFunction<ffi.Pointer<ffi.Char> Function()>();

  /// Sentry SDK name set during build time.
  /// Deprecated: Please use sentry_options_get_sdk_name instead.
  ffi.Pointer<ffi.Char> sdk_name() {
    return _sdk_name();
  }

  late final _sdk_namePtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Char> Function()>>(
          'sentry_sdk_name');
  late final _sdk_name =
      _sdk_namePtr.asFunction<ffi.Pointer<ffi.Char> Function()>();
}

/// Represents a sentry protocol value.
///
/// The members of this type should never be accessed.  They are only here
/// so that alignment for the type can be properly determined.
///
/// Values must be released with `sentry_value_decref`.  This lowers the
/// internal refcount by one.  If the refcount hits zero it's freed.  Some
/// values like primitives have no refcount (like null) so operations on
/// those are no-ops.
///
/// In addition values can be frozen.  Some values like primitives are always
/// frozen but lists and dicts are not and can be frozen on demand.  This
/// automatically happens for some shared values in the event payload like
/// the module list.
class sentry_value_u extends ffi.Union {
  @ffi.Uint64()
  external int _bits;

  @ffi.Double()
  external double _double;
}

/// Type of a sentry value.
abstract class sentry_value_type_t {
  static const int SENTRY_VALUE_TYPE_NULL = 0;
  static const int SENTRY_VALUE_TYPE_BOOL = 1;
  static const int SENTRY_VALUE_TYPE_INT32 = 2;
  static const int SENTRY_VALUE_TYPE_DOUBLE = 3;
  static const int SENTRY_VALUE_TYPE_STRING = 4;
  static const int SENTRY_VALUE_TYPE_LIST = 5;
  static const int SENTRY_VALUE_TYPE_OBJECT = 6;
}

/// The Sentry Client Options.
///
/// See https://docs.sentry.io/platforms/native/configuration/
class sentry_options_s extends ffi.Opaque {}
