minVersion: 1.21.0
changelogPolicy: auto
artifactProvider:
  name: none
targets:
  - name: pub-dev
    # This is temporarily needed because we keep the package:web dependency implicit
    # See https://github.com/getsentry/sentry-dart/pull/2113 for more context
    skipValidation: true
    packages:
      dart:
      flutter:
      logging:
      dio:
      file:
      sqflite:
      hive:
      drift:
      isar:
      link:
  - name: github
  - name: registry
    sdks:
      pub:sentry:
      pub:sentry_flutter:
      pub:sentry_logging:
      pub:sentry_dio:
      pub:sentry_file:
      pub:sentry_sqflite:
      pub:sentry_drift:
      pub:sentry_hive:
      pub:sentry_isar:
      # TODO: after we published link we need to add it to the registry repo and then uncomment here
      # pub:sentry_link: