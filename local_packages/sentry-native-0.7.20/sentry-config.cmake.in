@PACKAGE_INIT@
include(CMakeFindDependencyMacro)

set(SENTRY_BACKEND @SENTRY_BACKEND@)
set(SENTRY_TRANSPORT @SENTRY_TRANSPORT@)
set(SENTRY_BUILD_SHARED_LIBS @SENTRY_BUILD_SHARED_LIBS@)
set(SENTRY_LINK_PTHREAD @SENTRY_LINK_PTHREAD@)
set(SENTRY_TRANSPORT_COMPRESSION @SENTRY_TRANSPORT_COMPRESSION@)
set(SENTRY_BREAKPAD_SYSTEM @SENTRY_BREAKPAD_SYSTEM@)
set(CRASHPAD_ZLIB_SYSTEM @CRASHPAD_ZLIB_SYSTEM@)

if(NOT SENTRY_BUILD_SHARED_LIBS)
    if(SENTRY_TRANSPORT_COMPRESSION OR CRASHPAD_ZLIB_SYSTEM)
    	find_dependency(ZLIB)
    endif()
    if(SENTRY_BACKEND STREQUAL "breakpad" AND SENTRY_BREAKPAD_SYSTEM)
    	find_dependency(PkgConfig)
    	pkg_check_modules(BREAKPAD REQUIRED IMPORTED_TARGET breakpad-client)
    endif()
    if(SENTRY_TRANSPORT STREQUAL "curl")
    	find_dependency(CURL COMPONENTS AsynchDNS)
    endif()
    if(SENTRY_LINK_PTHREAD)
        set(THREADS_PREFER_PTHREAD_FLAG ON)
    	find_dependency(Threads)
    endif()
endif()

include("${CMAKE_CURRENT_LIST_DIR}/sentry_crashpad-targets.cmake" OPTIONAL)
include("${CMAKE_CURRENT_LIST_DIR}/sentry-targets.cmake")