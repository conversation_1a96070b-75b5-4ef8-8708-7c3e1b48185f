minVersion: 0.23.1
changelogPolicy: auto
targets:
  - name: github
  - name: registry
    sdks:
      github:getsentry/sentry-native:
      maven:io.sentry:sentry-native-ndk:
  - name: gcs
    includeNames: /^(sentry-native\.zip)$/
    bucket: sentry-sdk-assets
    paths:
      - path: /sentry-native/{{version}}/
        metadata:
          cacheControl: public, max-age=2592000
      - path: /sentry-native/latest/
        metadata:
          cacheControl: public, max-age=600
  - name: maven
    includeNames: /^(sentry-native-ndk-).*\.zip$/
    mavenCliPath: scripts/mvnw
    mavenSettingsPath: scripts/settings.xml
    mavenRepoId: ossrh
    mavenRepoUrl: https://oss.sonatype.org/service/local/staging/deploy/maven2/
    android:
      distDirRegex: /^(sentry-native-ndk).*$/
      fileReplaceeRegex: /\d+\.\d+\.\d+(-\w+(\.\d+)?)?(-SNAPSHOT)?/
      fileReplacerStr: release.aar
requireNames:
  - /^sentry-native.zip$/
