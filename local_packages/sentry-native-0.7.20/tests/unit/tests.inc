XX(assert_sdk_name)
XX(assert_sdk_user_agent)
XX(assert_sdk_version)
XX(background_worker)
XX(basic_consent_tracking)
XX(basic_function_transport)
XX(basic_function_transport_transaction)
XX(basic_function_transport_transaction_ts)
XX(basic_http_request_preparation_for_event)
XX(basic_http_request_preparation_for_event_with_attachment)
XX(basic_http_request_preparation_for_minidump)
XX(basic_http_request_preparation_for_transaction)
XX(basic_http_request_preparation_for_user_feedback)
XX(basic_spans)
XX(basic_tracing_context)
XX(basic_transaction)
XX(basic_write_envelope_to_file)
XX(bgworker_flush)
XX(breadcrumb_without_type_or_message_still_valid)
XX(build_id_parser)
XX(check_version)
XX(child_spans)
XX(child_spans_ts)
XX(concurrent_init)
XX(concurrent_uninit)
XX(count_sampled_events)
XX(crash_marker)
XX(crashed_last_run)
XX(custom_logger)
XX(discarding_before_send)
XX(distributed_headers)
XX(distributed_headers_invalid_spanid)
XX(distributed_headers_invalid_traceid)
XX(drop_unfinished_spans)
XX(dsn_auth_header_custom_user_agent)
XX(dsn_auth_header_invalid_dsn)
XX(dsn_auth_header_no_user_agent)
XX(dsn_auth_header_null_dsn)
XX(dsn_parsing_complete)
XX(dsn_parsing_invalid)
XX(dsn_store_url_custom_agent)
XX(dsn_store_url_with_path)
XX(dsn_store_url_without_path)
XX(dsn_with_ending_forward_slash_will_be_cleaned)
XX(dsn_with_non_http_scheme_is_invalid)
XX(dsn_without_project_id_is_invalid)
XX(dsn_without_url_scheme_is_invalid)
XX(empty_transport)
XX(exception_without_type_or_value_still_valid)
XX(fuzz_json)
XX(init_failure)
XX(internal_uuid_api)
XX(invalid_dsn)
XX(invalid_proxy)
XX(iso_time)
XX(lazy_attachments)
XX(message_with_null_text_is_valid)
XX(module_addr)
XX(module_finder)
XX(mpack_newlines)
XX(mpack_removed_tags)
XX(multiple_inits)
XX(multiple_transactions)
XX(options_sdk_name_custom)
XX(options_sdk_name_defaults)
XX(options_sdk_name_invalid)
XX(os)
XX(os_release_non_existent_files)
XX(os_releases_snapshot)
XX(overflow_spans)
XX(page_allocator)
XX(path_basics)
XX(path_current_exe)
XX(path_directory)
XX(path_from_str_n_wo_null_termination)
XX(path_from_str_null)
XX(path_joining_unix)
XX(path_joining_windows)
XX(path_relative_filename)
XX(procmaps_parser)
XX(rate_limit_parsing)
XX(recursive_paths)
XX(sampling_before_send)
XX(sampling_decision)
XX(sampling_transaction)
XX(sentry__value_span_new_requires_unfinished_parent)
XX(serialize_envelope)
XX(session_basics)
XX(set_tag_allows_null_tag_and_value)
XX(set_tag_cuts_value_at_length_200)
XX(slice)
XX(span_data)
XX(span_data_n)
XX(span_tagging)
XX(span_tagging_n)
XX(spans_on_scope)
XX(symbolizer)
XX(task_queue)
XX(thread_without_name_still_valid)
XX(transaction_name_backfill_on_finish)
XX(transactions_skip_before_send)
XX(transport_sampling_transactions)
XX(txn_data)
XX(txn_data_n)
XX(txn_name)
XX(txn_name_n)
XX(txn_tagging)
XX(txn_tagging_n)
XX(uninitialized)
XX(unsampled_spans)
XX(unwinder)
XX(update_from_header_no_sampled_flag)
XX(update_from_header_null_ctx)
XX(url_parsing_complete)
XX(url_parsing_invalid)
XX(url_parsing_partial)
XX(user_feedback_is_valid)
XX(user_feedback_with_null_args)
XX(uuid_api)
XX(uuid_v4)
XX(value_bool)
XX(value_double)
XX(value_freezing)
XX(value_get_by_null_key)
XX(value_int32)
XX(value_json_deeply_nested)
XX(value_json_escaping)
XX(value_json_invalid_doubles)
XX(value_json_locales)
XX(value_json_parsing)
XX(value_json_surrogates)
XX(value_list)
XX(value_null)
XX(value_object)
XX(value_object_merge)
XX(value_object_merge_nested)
XX(value_remove_by_null_key)
XX(value_ringbuffer)
XX(value_set_by_null_key)
XX(value_set_stacktrace)
XX(value_string)
XX(value_string_n)
XX(value_unicode)
XX(value_wrong_type)
XX(write_envelope_to_file_null)
XX(write_envelope_to_invalid_path)
