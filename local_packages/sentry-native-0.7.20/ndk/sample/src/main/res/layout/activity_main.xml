<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <Button
            android:id="@+id/init_ndk_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Init NDK"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/capture_message_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Capture Message"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/trigger_native_crash_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Trigger Crash"
            tools:ignore="HardcodedText" />

    </LinearLayout>

</ScrollView>
