# Daemons heap size
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1536m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseParallelGC
org.gradle.caching=true
org.gradle.parallel=true

# AndroidX required by AGP >= 3.6.x
android.useAndroidX=true

# Required by AGP >= 8.0.x
android.defaults.buildfeatures.buildconfig=true

# Release information, used for maven publishing
versionName=0.7.20

# disable renderscript, it's enabled by default
android.defaults.buildfeatures.renderscript=false

# disable shader compilation, it's enabled by default
android.defaults.buildfeatures.shaders=false

# disable aidl files, it's enabled by default
android.defaults.buildfeatures.aidl=false

# disable Resource Values generation
android.defaults.buildfeatures.resvalues=false

# disable automatically adding Kotlin stdlib to compile dependencies
kotlin.stdlib.default.dependency=false

# TODO: Enable Prefab https://android-developers.googleblog.com/2020/02/native-dependencies-in-android-studio-40.html
# android.enablePrefab=true
# android.prefabVersion=1.0.0

# publication pom properties
POM_NAME=Sentry SDK
POM_DESCRIPTION=SDK for sentry.io
POM_URL=https://github.com/getsentry/sentry-native
POM_SCM_URL=https://github.com/getsentry/sentry-native
POM_SCM_CONNECTION=scm:git:git://github.com/getsentry/sentry-native.git
POM_SCM_DEV_CONNECTION=scm:git:ssh://**************/getsentry/sentry-native.git

POM_LICENCE_NAME=MIT
POM_LICENCE_URL=http://www.opensource.org/licenses/mit-license.php

POM_DEVELOPER_ID=getsentry
POM_DEVELOPER_NAME=Sentry Team and Contributors
POM_DEVELOPER_URL=https://github.com/getsentry/

POM_ARTIFACT_ID=sentry-native-ndk

systemProp.org.gradle.internal.http.socketTimeout=120000

android.nonTransitiveRClass=true

android.suppressUnsupportedCompileSdk=34
