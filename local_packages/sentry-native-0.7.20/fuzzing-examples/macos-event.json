{"event_id": "05020bb6-ad4b-4496-e304-edd5ffde22a8", "timestamp": "2021-03-24T09:46:08.321Z", "level": "fatal", "exception": {"values": [{"type": "SIGSEGV", "value": "<PERSON>g<PERSON><PERSON>", "mechanism": {"type": "signalhandler", "synthetic": true, "handled": false, "meta": {"signal": {"name": "SIGSEGV", "number": 11}}}, "stacktrace": {"frames": []}}]}, "platform": "native", "release": "test-example-release", "environment": "development", "user": {"id": 42, "username": "some_name"}, "transaction": "test-transaction", "sdk": {"name": "sentry.native", "version": "0.4.8", "packages": [{"name": "github:getsentry/sentry-native", "version": "0.4.8"}], "integrations": ["inproc"]}, "tags": {"expected-tag": "some value"}, "extra": {"extra stuff": "some value", "…unicode key…": "őá…–🤮🚀¿ 한글 테스트"}, "contexts": {"os": {"name": "macOS", "version": "11.2.3", "build": "20D91", "kernel_version": "20.3.0"}, "runtime": {"type": "runtime", "name": "testing-runtime"}}, "breadcrumbs": [{"timestamp": "2021-03-24T09:46:08.320Z", "message": "default level is info"}, {"timestamp": "2021-03-24T09:46:08.321Z", "type": "http", "message": "debug crumb", "category": "example!", "level": "debug"}, {"timestamp": "2021-03-24T09:46:08.321Z", "message": "lf\ncrlf\r\nlf\n...", "category": "something else"}], "debug_meta": {"images": [{"code_file": "/Users/<USER>/Coding/sentry-native/build/sentry_example", "image_addr": "0x1060b2000", "image_size": 16384, "debug_id": "4aedfa58-b326-3ae2-8731-371955b73541", "type": "macho"}, {"code_file": "/Users/<USER>/Coding/sentry-native/build/libsentry.dylib", "image_addr": "0x1060c7000", "image_size": 114688, "debug_id": "249fbfb0-4f9a-3989-bd28-a175d6a7f3d7", "type": "macho"}, {"code_file": "/usr/lib/libSystem.B.dylib", "image_addr": "0x7fff2a7e5000", "image_size": 8192, "debug_id": "83503ce0-32b1-36db-a4f0-3cc6b7bcf50a", "type": "macho"}]}}