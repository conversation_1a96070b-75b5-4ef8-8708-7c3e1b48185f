<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Gaming.Desktop.x64'">
    <LibraryPath>$(Console_SdkLibPath);$(LibraryPath)</LibraryPath>
    <IncludePath>$(Console_SdkIncludeRoot);$(IncludePath)</IncludePath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Gaming.Desktop.x64'">
    <LibraryPath>$(Console_SdkLibPath);$(LibraryPath)</LibraryPath>
    <IncludePath>$(Console_SdkIncludeRoot);$(IncludePath)</IncludePath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Gaming.Xbox.XboxOne.x64'">
    <ReferencePath>$(Console_SdkLibPath);$(Console_SdkWindowsMetadataPath)</ReferencePath>
    <LibraryPath>$(Console_SdkLibPath)</LibraryPath>
    <IncludePath>$(Console_SdkIncludeRoot)</IncludePath>
    <ExecutablePath>$(Console_SdkRoot)bin;$(Console_SdkToolPath);$(ExecutablePath)</ExecutablePath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Gaming.Xbox.XboxOne.x64'">
    <ReferencePath>$(Console_SdkLibPath);$(Console_SdkWindowsMetadataPath)</ReferencePath>
    <LibraryPath>$(Console_SdkLibPath)</LibraryPath>
    <IncludePath>$(Console_SdkIncludeRoot)</IncludePath>
    <ExecutablePath>$(Console_SdkRoot)bin;$(Console_SdkToolPath);$(ExecutablePath)</ExecutablePath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Gaming.Xbox.Scarlett.x64'">
    <ReferencePath>$(Console_SdkLibPath);$(Console_SdkWindowsMetadataPath)</ReferencePath>
    <LibraryPath>$(Console_SdkLibPath)</LibraryPath>
    <IncludePath>$(Console_SdkIncludeRoot)</IncludePath>
    <ExecutablePath>$(Console_SdkRoot)bin;$(Console_SdkToolPath);$(ExecutablePath)</ExecutablePath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Gaming.Xbox.Scarlett.x64'">
    <ReferencePath>$(Console_SdkLibPath);$(Console_SdkWindowsMetadataPath)</ReferencePath>
    <LibraryPath>$(Console_SdkLibPath)</LibraryPath>
    <IncludePath>$(Console_SdkIncludeRoot)</IncludePath>
    <ExecutablePath>$(Console_SdkRoot)bin;$(Console_SdkToolPath);$(ExecutablePath)</ExecutablePath>
  </PropertyGroup>

</Project>
