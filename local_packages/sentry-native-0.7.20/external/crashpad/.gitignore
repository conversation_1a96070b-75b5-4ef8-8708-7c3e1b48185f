# Copyright 2014 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep sorted

*.Makefile
*.ninja
*.pyc
*.target.mk
*.xcodeproj
*~
.*.sw?
.DS_Store
.cache
.gdb_history
.gdbinit
/Makefile
/build/fuchsia
/out
/third_party/edo/edo
/third_party/fuchsia-gn-sdk
/third_party/fuchsia/.cipd
/third_party/fuchsia/clang
/third_party/fuchsia/qemu
/third_party/fuchsia/sdk
/third_party/googletest/googletest
/third_party/gyp/gyp
/third_party/libfuzzer
/third_party/linux/.cipd
/third_party/linux/clang
/third_party/linux/sysroot
/third_party/gyp/gyp
/xcodebuild
tags
