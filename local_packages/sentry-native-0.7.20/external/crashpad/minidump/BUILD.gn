# Copyright 2015 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../build/crashpad_buildconfig.gni")

crashpad_static_library("minidump") {
  sources = [
    "minidump_annotation_writer.cc",
    "minidump_annotation_writer.h",
    "minidump_byte_array_writer.cc",
    "minidump_byte_array_writer.h",
    "minidump_context_writer.cc",
    "minidump_context_writer.h",
    "minidump_crashpad_info_writer.cc",
    "minidump_crashpad_info_writer.h",
    "minidump_exception_writer.cc",
    "minidump_exception_writer.h",
    "minidump_file_writer.cc",
    "minidump_file_writer.h",
    "minidump_handle_writer.cc",
    "minidump_handle_writer.h",
    "minidump_memory_info_writer.cc",
    "minidump_memory_info_writer.h",
    "minidump_memory_writer.cc",
    "minidump_memory_writer.h",
    "minidump_misc_info_writer.cc",
    "minidump_misc_info_writer.h",
    "minidump_module_crashpad_info_writer.cc",
    "minidump_module_crashpad_info_writer.h",
    "minidump_module_writer.cc",
    "minidump_module_writer.h",
    "minidump_rva_list_writer.cc",
    "minidump_rva_list_writer.h",
    "minidump_simple_string_dictionary_writer.cc",
    "minidump_simple_string_dictionary_writer.h",
    "minidump_stream_writer.cc",
    "minidump_stream_writer.h",
    "minidump_string_writer.cc",
    "minidump_string_writer.h",
    "minidump_system_info_writer.cc",
    "minidump_system_info_writer.h",
    "minidump_thread_id_map.cc",
    "minidump_thread_id_map.h",
    "minidump_thread_name_list_writer.cc",
    "minidump_thread_name_list_writer.h",
    "minidump_thread_writer.cc",
    "minidump_thread_writer.h",
    "minidump_unloaded_module_writer.cc",
    "minidump_unloaded_module_writer.h",
    "minidump_user_extension_stream_data_source.cc",
    "minidump_user_extension_stream_data_source.h",
    "minidump_user_stream_writer.cc",
    "minidump_user_stream_writer.h",
    "minidump_writable.cc",
    "minidump_writable.h",
    "minidump_writer_util.cc",
    "minidump_writer_util.h",
  ]

  public_configs = [ "..:crashpad_config" ]

  public_deps = [
    ":format",
    "../compat",
  ]

  deps = [
    "../snapshot",
    "$mini_chromium_source_parent:base",
    "../util",
  ]

  if (crashpad_is_win) {
    cflags = [
      "/wd4201",  # nonstandard extension used : nameless struct/union
      "/wd4324",  # 'struct' : structure was padded due to __declspec(align())
    ]
  }
}

# :format is the only part of minidump that snapshot may depend on.
static_library("format") {
  sources = [
    "minidump_context.h",
    "minidump_extensions.cc",
    "minidump_extensions.h",
  ]

  public_configs = [ "..:crashpad_config" ]

  public_deps = [ "../compat" ]

  deps = [
    "../snapshot:context",
    "$mini_chromium_source_parent:base",
    "../util",
  ]
}

static_library("test_support") {
  testonly = true

  sources = [
    "test/minidump_byte_array_writer_test_util.cc",
    "test/minidump_byte_array_writer_test_util.h",
    "test/minidump_context_test_util.cc",
    "test/minidump_context_test_util.h",
    "test/minidump_file_writer_test_util.cc",
    "test/minidump_file_writer_test_util.h",
    "test/minidump_memory_writer_test_util.cc",
    "test/minidump_memory_writer_test_util.h",
    "test/minidump_rva_list_test_util.cc",
    "test/minidump_rva_list_test_util.h",
    "test/minidump_string_writer_test_util.cc",
    "test/minidump_string_writer_test_util.h",
    "test/minidump_user_extension_stream_util.cc",
    "test/minidump_user_extension_stream_util.h",
    "test/minidump_writable_test_util.cc",
    "test/minidump_writable_test_util.h",
  ]

  public_configs = [ "..:crashpad_config" ]

  public_deps = [ ":minidump" ]

  deps = [
    "../snapshot:test_support",
    "../test",
    "../third_party/googletest:googletest",
    "$mini_chromium_source_parent:base",
    "../util",
  ]

  if (crashpad_is_win) {
    cflags = [ "/wd4201" ]  # nonstandard extension used : nameless struct/union
  }
}

source_set("minidump_test") {
  testonly = true

  sources = [
    "minidump_annotation_writer_test.cc",
    "minidump_byte_array_writer_test.cc",
    "minidump_context_writer_test.cc",
    "minidump_crashpad_info_writer_test.cc",
    "minidump_exception_writer_test.cc",
    "minidump_file_writer_test.cc",
    "minidump_handle_writer_test.cc",
    "minidump_memory_info_writer_test.cc",
    "minidump_memory_writer_test.cc",
    "minidump_misc_info_writer_test.cc",
    "minidump_module_crashpad_info_writer_test.cc",
    "minidump_module_writer_test.cc",
    "minidump_rva_list_writer_test.cc",
    "minidump_simple_string_dictionary_writer_test.cc",
    "minidump_string_writer_test.cc",
    "minidump_system_info_writer_test.cc",
    "minidump_thread_id_map_test.cc",
    "minidump_thread_name_list_writer_test.cc",
    "minidump_thread_writer_test.cc",
    "minidump_unloaded_module_writer_test.cc",
    "minidump_user_stream_writer_test.cc",
    "minidump_writable_test.cc",
  ]

  configs += [ "../build:crashpad_is_in_fuchsia" ]

  deps = [
    ":test_support",
    "../snapshot:test_support",
    "../test",
    "../third_party/googletest:googletest",
    "$mini_chromium_source_parent:base",
    "../util",
  ]

  if (crashpad_is_win) {
    cflags = [ "/wd4201" ]  # nonstandard extension used : nameless struct/union
  }
}
