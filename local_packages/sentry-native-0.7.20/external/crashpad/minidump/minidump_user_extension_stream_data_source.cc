// Copyright 2017 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "minidump/minidump_user_extension_stream_data_source.h"

namespace crashpad {

MinidumpUserExtensionStreamDataSource::MinidumpUserExtensionStreamDataSource(
    uint32_t stream_type)
    : stream_type_(static_cast<MinidumpStreamType>(stream_type)) {}

MinidumpUserExtensionStreamDataSource::
    ~MinidumpUserExtensionStreamDataSource() {}

}  // namespace crashpad
