# Copyright 2019 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

*.S text eol=lf
*.asm text eol=lf
*.c text eol=lf
*.cc text eol=lf
*.cmx text eol=lf
*.css text eol=lf
*.defs text eol=lf
*.doxy text eol=lf
*.gn text eol=lf
*.gni text eol=lf
*.go text eol=lf
*.gyp text eol=lf
*.gypi text eol=lf
*.h text eol=lf
*.m text eol=lf
*.md text eol=lf
*.mm text eol=lf
*.pem text eol=lf
*.plist text eol=lf
*.proctype text eol=lf
*.py text eol=lf
*.sh text eol=lf
*.sym text eol=lf
*.txt text eol=lf
*.yaml text eol=lf
.clang-format text eol=lf
.gitattributes text eol=lf
.gitignore text eol=lf
.vpython text eol=lf
/AUTHORS text eol=lf
/CONTRIBUTORS text eol=lf
/LICENSE text eol=lf
/codereview.settings text eol=lf
APPLE_LICENSE text eol=lf
COPYING.LIB text eol=lf
DEPS text eol=lf
README text eol=lf
README.crashpad text eol=lf

*.dat binary
*.dll binary
*.ico binary
*.obj binary
*.png binary
*.so binary
