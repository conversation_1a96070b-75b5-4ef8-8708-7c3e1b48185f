# Copyright 2022 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This is a vpython "spec" file.
#
# It describes patterns for python wheel dependencies of the python scripts.
#
# Read more about `vpython` and how to modify this file here:
#   https://chromium.googlesource.com/infra/infra/+/master/doc/users/vpython.md

# This is needed for snapshot/win/end_to_end_test.py.
wheel: <
  name: "infra/python/wheels/pywin32/${vpython_platform}"
  version: "version:300"
  match_tag: <
    platform: "win32"
  >
  match_tag: <
    platform: "win_amd64"
  >
>
