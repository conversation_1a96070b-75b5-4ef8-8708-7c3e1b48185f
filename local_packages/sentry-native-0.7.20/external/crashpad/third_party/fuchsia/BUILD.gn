# Copyright 2018 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../build/crashpad_buildconfig.gni")

if (crashpad_is_in_fuchsia) {
  group("fuchsia") {
    public_deps = [
      "//sdk/lib/fdio",
      "//zircon/system/ulib/zx",
    ]
  }
} else if (crashpad_is_in_chromium) {
  group("fuchsia") {
    public_deps = [
      "//third_party/fuchsia-sdk/sdk/pkg/fdio",
      "//third_party/fuchsia-sdk/sdk/pkg/zx",
    ]
  }
} else {
  group("fuchsia") {
    public_deps = [
      "//third_party/fuchsia/sdk/$host_os-amd64/pkg/fdio",
      "//third_party/fuchsia/sdk/$host_os-amd64/pkg/zx",
    ]
  }
}
