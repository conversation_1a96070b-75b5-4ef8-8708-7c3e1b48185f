/*
 * Copyright (c) 2002,2000 Apple Computer, Inc. All rights reserved.
 *
 * @APPLE_OSREFERENCE_LICENSE_HEADER_START@
 * 
 * This file contains Original Code and/or Modifications of Original Code
 * as defined in and that are subject to the Apple Public Source License
 * Version 2.0 (the 'License'). You may not use this file except in
 * compliance with the License. The rights granted to you under the License
 * may not be used to create, or enable the creation or redistribution of,
 * unlawful or unlicensed copies of an Apple operating system, or to
 * circumvent, violate, or enable the circumvention or violation of, any
 * terms of an Apple operating system software license agreement.
 * 
 * Please obtain a copy of the License at
 * http://www.opensource.apple.com/apsl/ and read it before using this file.
 * 
 * The Original Code and all software distributed under the License are
 * distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
 * EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
 * INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
 * Please see the License for the specific language governing rights and
 * limitations under the License.
 * 
 * @APPLE_OSREFERENCE_LICENSE_HEADER_END@
 */
/*
 * @OSF_COPYRIGHT@
 */
/* 
 * Mach Operating System
 * Copyright (c) 1991,1990,1989,1988 Carnegie Mellon University
 * All Rights Reserved.
 * 
 * Permission to use, copy, modify and distribute this software and its
 * documentation is hereby granted, provided that both the copyright
 * notice and this permission notice appear in all copies of the
 * software, derivative works or modified versions, and any portions
 * thereof, and that both notices appear in supporting documentation.
 * 
 * CARNEGIE MELLON ALLOWS FREE USE OF THIS SOFTWARE IN ITS "AS IS"
 * CONDITION.  CARNEGIE MELLON DISCLAIMS ANY LIABILITY OF ANY KIND FOR
 * ANY DAMAGES WHATSOEVER RESULTING FROM THE USE OF THIS SOFTWARE.
 * 
 * Carnegie Mellon requests users of this software to return to
 * 
 *  Software Distribution Coordinator  or  <EMAIL>
 *  School of Computer Science
 *  Carnegie Mellon University
 *  Pittsburgh PA 15213-3890
 * 
 * any improvements or extensions that they make and grant Carnegie Mellon
 * the rights to redistribute these changes.
 */
/*
 */
/*
 *	Mach kernel standard interface type declarations
 */

#ifndef	_MACH_STD_TYPES_DEFS_
#define _MACH_STD_TYPES_DEFS_

/* from ISO/IEC 988:1999 spec */
/* 7.18.1.1 Exact-width integer types */

type int8_t = MACH_MSG_TYPE_INTEGER_8;
type uint8_t = MACH_MSG_TYPE_INTEGER_8;
type int16_t = MACH_MSG_TYPE_INTEGER_16;
type uint16_t = MACH_MSG_TYPE_INTEGER_16;
type int32_t = MACH_MSG_TYPE_INTEGER_32;
type uint32_t = MACH_MSG_TYPE_INTEGER_32;
type int64_t = MACH_MSG_TYPE_INTEGER_64;
type uint64_t = MACH_MSG_TYPE_INTEGER_64;

/*
 * Legacy fixed-length Mach types which should
 * be replaced with the Standard types from above.
 */
type int32 = int32_t;
type unsigned32 = uint32_t;
type int64 = int64_t;
type unsigned64 = uint64_t;

/*
 * Other fixed length Mach types.
 */
type char = MACH_MSG_TYPE_CHAR;
type boolean_t = MACH_MSG_TYPE_BOOLEAN;

#include <mach/machine/machine_types.defs>

type kern_return_t = int;

type pointer_t = ^array[] of MACH_MSG_TYPE_BYTE
	ctype: vm_offset_t;


type mach_port_t = MACH_MSG_TYPE_COPY_SEND;
type mach_port_array_t = array[] of mach_port_t;

type mach_port_name_t = MACH_MSG_TYPE_PORT_NAME;
type mach_port_name_array_t = array[] of mach_port_name_t;

type mach_port_right_t = natural_t;

type mach_port_type_t = natural_t;
type mach_port_type_array_t = array[] of mach_port_type_t;

type mach_port_urefs_t = natural_t;
type mach_port_delta_t = integer_t;
type mach_port_seqno_t = natural_t;
type mach_port_mscount_t = unsigned;
type mach_port_msgcount_t = unsigned;
type mach_port_rights_t = unsigned;
type mach_msg_id_t = integer_t;
type mach_msg_size_t = natural_t;
type mach_msg_type_name_t = unsigned;
type mach_msg_options_t = integer_t;

type mach_port_move_receive_t =		MACH_MSG_TYPE_MOVE_RECEIVE
	ctype: mach_port_t;
type mach_port_copy_send_t =		MACH_MSG_TYPE_COPY_SEND
	ctype: mach_port_t;
type mach_port_make_send_t =		MACH_MSG_TYPE_MAKE_SEND
	ctype: mach_port_t;
type mach_port_move_send_t =		MACH_MSG_TYPE_MOVE_SEND
	ctype: mach_port_t;
type mach_port_make_send_once_t =	MACH_MSG_TYPE_MAKE_SEND_ONCE
	ctype: mach_port_t;
type mach_port_move_send_once_t =	MACH_MSG_TYPE_MOVE_SEND_ONCE
	ctype: mach_port_t;

type mach_port_receive_t =		MACH_MSG_TYPE_PORT_RECEIVE
	ctype: mach_port_t;
type mach_port_send_t =			MACH_MSG_TYPE_PORT_SEND
	ctype: mach_port_t;
type mach_port_send_once_t =		MACH_MSG_TYPE_PORT_SEND_ONCE
	ctype: mach_port_t;

type mach_port_poly_t = polymorphic
	ctype: mach_port_t;

import <mach/std_types.h>;
import <mach/mig.h>;

#endif	/* _MACH_STD_TYPES_DEFS_ */

/* vim: set ft=c : */
