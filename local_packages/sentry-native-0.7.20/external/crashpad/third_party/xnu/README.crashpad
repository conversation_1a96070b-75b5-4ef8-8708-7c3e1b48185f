Name: XNU
Short Name: xnu
URL: https://opensource.apple.com/source/xnu/
URL: https://opensource.apple.com/tarballs/xnu/
Version: 6153.11.26 (from macOS 10.15.0)
License: APSL 2.0
License File: APPLE_LICENSE
Security Critical: no
Shipped: yes

Description:
XNU is the operating system kernel used on macOS and other Apple systems.

Local Modifications:
 - EXTERNAL_HEADERS/mach-o/loader.h is present. Its #includes of
   <mach/machine/thread_status.h> and <architecture/byte_order.h> have been
   commented out as unnecessary. Note that its #includes of <mach/machine.h> and
   <mach/vm_prot.h> have been retained but these headers have not been provided.
   External headers must be made available to provide the cpu_type_t,
   cpu_subtype_t, and vm_prot_t types.
 - osfmk/mach/exc.defs and osfmk/mach/mach_exc.defs are present, to fill in
   for <mach/exc.defs> and <mach/mach_exc.defs> on iOS, where they are missing.
   The .defs files they depend on, <mach/mach_types.defs>,
   <mach/machine/machine_types.defs>, and <mach/std_types.defs> are also
   included.
 - Anything not listed above is omitted.
