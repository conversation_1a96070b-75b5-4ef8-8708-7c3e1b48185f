# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../build/crashpad_buildconfig.gni")
import("../../build/test.gni")

if (crashpad_is_in_chromium) {
  group("googletest") {
    testonly = true
    public_deps = [ "//testing/gtest" ]
  }
  group("googlemock") {
    testonly = true
    public_deps = [ "//testing/gmock" ]
  }
} else if (crashpad_is_in_dart || crashpad_is_in_fuchsia) {
  group("googletest") {
    testonly = true
    public_deps = [ "//third_party/googletest:gtest" ]
  }
  group("googlemock") {
    testonly = true
    public_deps = [ "//third_party/googletest:gmock" ]
  }
} else if (crashpad_is_standalone || crashpad_is_external) {
  if (crashpad_is_standalone) {
    googletest_dir = "googletest"
    mini_chromium_dir = "//third_party/mini_chromium/mini_chromium"
  } else if (crashpad_is_external) {
    googletest_dir = "../../../../googletest"
    mini_chromium_dir = "//../../mini_chromium/mini_chromium"
  }

  config("googletest_private_config") {
    visibility = [ ":*" ]
    include_dirs = [ "$googletest_dir/googletest" ]
    defines = [ "GUNIT_NO_GOOGLE3=1" ]
  }

  config("googletest_public_config") {
    include_dirs = [ "$googletest_dir/googletest/include" ]
  }

  static_library("googletest") {
    testonly = true
    sources = [
      "$googletest_dir/googletest/include/gtest/gtest-assertion-result.h",
      "$googletest_dir/googletest/include/gtest/gtest-death-test.h",
      "$googletest_dir/googletest/include/gtest/gtest-matchers.h",
      "$googletest_dir/googletest/include/gtest/gtest-message.h",
      "$googletest_dir/googletest/include/gtest/gtest-param-test.h",
      "$googletest_dir/googletest/include/gtest/gtest-printers.h",
      "$googletest_dir/googletest/include/gtest/gtest-spi.h",
      "$googletest_dir/googletest/include/gtest/gtest-test-part.h",
      "$googletest_dir/googletest/include/gtest/gtest-typed-test.h",
      "$googletest_dir/googletest/include/gtest/gtest.h",
      "$googletest_dir/googletest/include/gtest/gtest_pred_impl.h",
      "$googletest_dir/googletest/include/gtest/gtest_prod.h",
      "$googletest_dir/googletest/include/gtest/internal/custom/gtest-port.h",
      "$googletest_dir/googletest/include/gtest/internal/custom/gtest-printers.h",
      "$googletest_dir/googletest/include/gtest/internal/custom/gtest.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-death-test-internal.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-filepath.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-internal.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-param-util.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-port-arch.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-port.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-string.h",
      "$googletest_dir/googletest/include/gtest/internal/gtest-type-util.h",
      "$googletest_dir/googletest/src/gtest-all.cc",
      "$googletest_dir/googletest/src/gtest-assertion-result.cc",
      "$googletest_dir/googletest/src/gtest-death-test.cc",
      "$googletest_dir/googletest/src/gtest-filepath.cc",
      "$googletest_dir/googletest/src/gtest-internal-inl.h",
      "$googletest_dir/googletest/src/gtest-matchers.cc",
      "$googletest_dir/googletest/src/gtest-port.cc",
      "$googletest_dir/googletest/src/gtest-printers.cc",
      "$googletest_dir/googletest/src/gtest-test-part.cc",
      "$googletest_dir/googletest/src/gtest-typed-test.cc",
      "$googletest_dir/googletest/src/gtest.cc",
    ]
    sources -= [ "$googletest_dir/googletest/src/gtest-all.cc" ]
    public_configs = [ ":googletest_public_config" ]
    configs -= [ "$mini_chromium_dir/build/config:Wexit_time_destructors" ]
    configs += [ ":googletest_private_config" ]
    if (crashpad_is_fuchsia) {
      deps = [ "../fuchsia" ]
    }
  }

  static_library("googletest_main") {
    # Tests outside of this file should use ../../test:googletest_main instead.
    visibility = [ ":*" ]

    testonly = true
    sources = [ "$googletest_dir/googletest/src/gtest_main.cc" ]
    deps = [ ":googletest" ]
  }

  test("gtest_all_test") {
    sources = [
      "$googletest_dir/googletest/test/googletest-death-test-test.cc",
      "$googletest_dir/googletest/test/googletest-filepath-test.cc",
      "$googletest_dir/googletest/test/googletest-message-test.cc",
      "$googletest_dir/googletest/test/googletest-options-test.cc",
      "$googletest_dir/googletest/test/googletest-port-test.cc",
      "$googletest_dir/googletest/test/googletest-test-part-test.cc",
      "$googletest_dir/googletest/test/gtest-typed-test2_test.cc",
      "$googletest_dir/googletest/test/gtest-typed-test_test.cc",
      "$googletest_dir/googletest/test/gtest-typed-test_test.h",
      "$googletest_dir/googletest/test/gtest_main_unittest.cc",
      "$googletest_dir/googletest/test/gtest_pred_impl_unittest.cc",
      "$googletest_dir/googletest/test/gtest_prod_test.cc",
      "$googletest_dir/googletest/test/gtest_skip_test.cc",
      "$googletest_dir/googletest/test/gtest_unittest.cc",
      "$googletest_dir/googletest/test/production.cc",
      "$googletest_dir/googletest/test/production.h",
    ]

    if (!crashpad_is_win) {
      # TODO: Fix error C2015: too many characters in constant. As this error
      # cannot be suppressed, removing the test on Windows. See
      # https://chromium-review.googlesource.com/c/crashpad/crashpad/+/2855854/2
      # for details.
      sources +=
          [ "$googletest_dir/googletest/test/googletest-printers-test.cc" ]
    }
    configs -= [ "$mini_chromium_dir/build/config:Wexit_time_destructors" ]
    configs += [ ":googletest_private_config" ]
    deps = [
      ":googletest",
      ":googletest_main",
    ]

    if (crashpad_is_win) {
      cflags = [ "/wd4702" ]  # unreachable code
    }
  }

  test("gtest_environment_test") {
    sources = [ "$googletest_dir/googletest/test/gtest_environment_test.cc" ]
    configs += [ ":googletest_private_config" ]
    deps = [ ":googletest" ]
  }

  test("gtest_listener_test") {
    sources = [ "$googletest_dir/googletest/test/googletest-listener-test.cc" ]
    deps = [ ":googletest" ]
  }

  test("gtest_macro_stack_footprint_test") {
    sources = [ "$googletest_dir/googletest/test/gtest_test_macro_stack_footprint_test.cc" ]
    deps = [ ":googletest" ]
  }

  test("gtest_no_test") {
    sources = [ "$googletest_dir/googletest/test/gtest_no_test_unittest.cc" ]
    deps = [ ":googletest" ]
  }

  test("gtest_param_test") {
    sources = [
      "$googletest_dir/googletest/test/googletest-param-test-test.cc",
      "$googletest_dir/googletest/test/googletest-param-test-test.h",
      "$googletest_dir/googletest/test/googletest-param-test2-test.cc",
    ]
    configs -= [ "$mini_chromium_dir/build/config:Wexit_time_destructors" ]
    configs += [ ":googletest_private_config" ]
    deps = [ ":googletest" ]
  }

  test("gtest_premature_exit_test") {
    sources = [ "$googletest_dir/googletest/test/gtest_premature_exit_test.cc" ]
    deps = [ ":googletest" ]
  }

  test("gtest_repeat_test") {
    sources = [ "$googletest_dir/googletest/test/gtest_repeat_test.cc" ]
    configs += [ ":googletest_private_config" ]
    deps = [ ":googletest" ]
  }

  test("gtest_skip_in_environment_setup_test") {
    sources = [
      "$googletest_dir/googletest/test/gtest_skip_in_environment_setup_test.cc",
    ]
    deps = [ ":googletest" ]
  }

  test("gtest_sole_header_test") {
    sources = [ "$googletest_dir/googletest/test/gtest_sole_header_test.cc" ]
    deps = [
      ":googletest",
      ":googletest_main",
    ]
  }

  test("gtest_stress_test") {
    sources = [ "$googletest_dir/googletest/test/gtest_stress_test.cc" ]
    configs += [ ":googletest_private_config" ]
    deps = [ ":googletest" ]
  }

  test("gtest_unittest_api_test") {
    sources = [ "$googletest_dir/googletest/test/gtest-unittest-api_test.cc" ]
    deps = [ ":googletest" ]
  }

  group("googletest_all_tests") {
    testonly = true
    deps = [
      ":gtest_all_test",
      ":gtest_environment_test",
      ":gtest_listener_test",
      ":gtest_macro_stack_footprint_test",
      ":gtest_no_test",
      ":gtest_param_test",
      ":gtest_premature_exit_test",
      ":gtest_repeat_test",
      ":gtest_skip_in_environment_setup_test",
      ":gtest_sole_header_test",
      ":gtest_stress_test",
      ":gtest_unittest_api_test",
    ]
  }

  config("googlemock_private_config") {
    visibility = [ ":*" ]
    include_dirs = [ "$googletest_dir/googlemock" ]
  }

  config("googlemock_public_config") {
    include_dirs = [ "$googletest_dir/googlemock/include" ]
  }

  static_library("googlemock") {
    testonly = true
    sources = [
      "$googletest_dir/googlemock/include/gmock/gmock-actions.h",
      "$googletest_dir/googlemock/include/gmock/gmock-cardinalities.h",
      "$googletest_dir/googlemock/include/gmock/gmock-function-mocker.h",
      "$googletest_dir/googlemock/include/gmock/gmock-matchers.h",
      "$googletest_dir/googlemock/include/gmock/gmock-more-actions.h",
      "$googletest_dir/googlemock/include/gmock/gmock-more-matchers.h",
      "$googletest_dir/googlemock/include/gmock/gmock-nice-strict.h",
      "$googletest_dir/googlemock/include/gmock/gmock-spec-builders.h",
      "$googletest_dir/googlemock/include/gmock/gmock.h",
      "$googletest_dir/googlemock/include/gmock/internal/custom/gmock-generated-actions.h",
      "$googletest_dir/googlemock/include/gmock/internal/custom/gmock-matchers.h",
      "$googletest_dir/googlemock/include/gmock/internal/custom/gmock-port.h",
      "$googletest_dir/googlemock/include/gmock/internal/gmock-internal-utils.h",
      "$googletest_dir/googlemock/include/gmock/internal/gmock-port.h",
      "$googletest_dir/googlemock/include/gmock/internal/gmock-pp.h",
      "$googletest_dir/googlemock/src/gmock-all.cc",
      "$googletest_dir/googlemock/src/gmock-cardinalities.cc",
      "$googletest_dir/googlemock/src/gmock-internal-utils.cc",
      "$googletest_dir/googlemock/src/gmock-matchers.cc",
      "$googletest_dir/googlemock/src/gmock-spec-builders.cc",
      "$googletest_dir/googlemock/src/gmock.cc",
    ]
    sources -= [ "$googletest_dir/googlemock/src/gmock-all.cc" ]
    public_configs = [ ":googlemock_public_config" ]
    configs -= [ "$mini_chromium_dir/build/config:Wexit_time_destructors" ]
    configs += [ ":googlemock_private_config" ]
    deps = [ ":googletest" ]
  }

  static_library("googlemock_main") {
    # Tests outside of this file should use ../../test:googlemock_main instead.
    visibility = [ ":*" ]
    testonly = true
    sources = [ "$googletest_dir/googlemock/src/gmock_main.cc" ]
    deps = [
      ":googlemock",
      ":googletest",
    ]
  }

  test("gmock_all_test") {
    sources = [
      "$googletest_dir/googlemock/test/gmock-actions_test.cc",
      "$googletest_dir/googlemock/test/gmock-cardinalities_test.cc",
      "$googletest_dir/googlemock/test/gmock-function-mocker_test.cc",
      "$googletest_dir/googlemock/test/gmock-internal-utils_test.cc",
      "$googletest_dir/googlemock/test/gmock-matchers-arithmetic_test.cc",
      "$googletest_dir/googlemock/test/gmock-matchers-comparisons_test.cc",
      "$googletest_dir/googlemock/test/gmock-matchers-containers_test.cc",
      "$googletest_dir/googlemock/test/gmock-matchers-misc_test.cc",
      "$googletest_dir/googlemock/test/gmock-matchers_test.h",
      "$googletest_dir/googlemock/test/gmock-more-actions_test.cc",
      "$googletest_dir/googlemock/test/gmock-nice-strict_test.cc",
      "$googletest_dir/googlemock/test/gmock-port_test.cc",
      "$googletest_dir/googlemock/test/gmock-pp-string_test.cc",
      "$googletest_dir/googlemock/test/gmock-pp_test.cc",
      "$googletest_dir/googlemock/test/gmock-spec-builders_test.cc",
      "$googletest_dir/googlemock/test/gmock_test.cc",
    ]
    configs += [
      ":googlemock_private_config",
      ":googletest_private_config",
    ]
    deps = [
      ":googlemock",
      ":googlemock_main",
      ":googletest",
    ]

    if (crashpad_is_clang) {
      cflags_cc = [
        # googletest/googlemock/test/gmock-function-mocker_test.cc does not
        # always use the override modifier with MOCK_METHOD.
        "-Wno-inconsistent-missing-override",

        # For googlemock/test/gmock-matchers-misc_test.cc comparison of
        # integers of different signs.
        "-Wno-sign-compare",
      ]
    }

    if (crashpad_is_win) {
      cflags = [
        # TODO: Correct SDK in vc\tools\msvc\14.14.26428\include\functional
        "/wd4789",  # VAR of size N bytes will be overrun

        # For googlemock/test/gmock-matchers-misc_test.cc comparison of
        # integers of different signs.
        "/wd4018",
      ]
    }
  }

  test("gmock_link_test") {
    sources = [
      "$googletest_dir/googlemock/test/gmock_link2_test.cc",
      "$googletest_dir/googlemock/test/gmock_link_test.cc",
      "$googletest_dir/googlemock/test/gmock_link_test.h",
    ]
    configs += [ ":googlemock_private_config" ]
    deps = [
      ":googlemock",
      ":googlemock_main",
      ":googletest",
    ]
  }

  test("gmock_stress_test") {
    sources = [ "$googletest_dir/googlemock/test/gmock_stress_test.cc" ]
    configs -= [ "$mini_chromium_dir/build/config:Wexit_time_destructors" ]
    configs += [ ":googlemock_private_config" ]
    deps = [
      ":googlemock",
      ":googletest",
    ]
  }

  group("googlemock_all_tests") {
    testonly = true
    deps = [
      ":gmock_all_test",
      ":gmock_link_test",
      ":gmock_stress_test",
    ]
  }
}
