# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../build/crashpad_buildconfig.gni")

group("base") {
  if (crashpad_is_in_chromium) {
    public_deps = [ "//base" ]
  } else if (crashpad_is_standalone) {
    public_deps = [ "mini_chromium/base" ]
  } else if (crashpad_is_in_fuchsia) {
    public_deps = [ mini_chromium_import_root + "/base" ]
  } else if (crashpad_is_external) {
    public_deps = [ "../../../../mini_chromium/mini_chromium/base" ]
  } else if (crashpad_is_in_dart) {
    public_deps = [ "//third_party/mini_chromium/mini_chromium/base" ]
  }
}

group("base_test_support") {
  testonly = true

  if (crashpad_is_in_chromium) {
    public_deps = [ "//base/test:test_support" ]
  }
}

group("build") {
  if (crashpad_is_in_chromium) {
    # Chromium has no build target.
  } else if (crashpad_is_standalone) {
    public_deps = [ "mini_chromium/build" ]
  } else if (crashpad_is_in_fuchsia) {
    public_deps = [ mini_chromium_import_root + "/build" ]
  } else if (crashpad_is_external) {
    public_deps = [ "../../../../mini_chromium/mini_chromium/build" ]
  } else if (crashpad_is_in_dart) {
    public_deps = [ "//third_party/mini_chromium/mini_chromium/build" ]
  }
}

group("chromeos_buildflags") {
  if (crashpad_is_in_chromium) {
    public_deps = [ "//build:chromeos_buildflags" ]
  } else if (crashpad_is_standalone) {
    public_deps = [ "mini_chromium/build:chromeos_buildflags" ]
  } else if (crashpad_is_in_fuchsia) {
    public_deps = [ mini_chromium_import_root + "/build:chromeos_buildflags" ]
  } else if (crashpad_is_external) {
    public_deps = [ "../../../../mini_chromium/mini_chromium/build:chromeos_buildflags" ]
  } else if (crashpad_is_in_dart) {
    public_deps = [ "//third_party/mini_chromium/mini_chromium/build:chromeos_buildflags" ]
  }
}
