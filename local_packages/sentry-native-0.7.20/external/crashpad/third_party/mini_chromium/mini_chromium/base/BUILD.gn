# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("../build/platform.gni")

static_library("base") {
  sources = [
    "atomicops.h",
    "atomicops_internals_atomicword_compat.h",
    "atomicops_internals_portable.h",
    "auto_reset.h",
    "bit_cast.h",
    "check.h",
    "check_op.h",
    "compiler_specific.h",
    "containers/checked_iterators.h",
    "containers/dynamic_extent.h",
    "containers/span.h",
    "containers/util.h",
    "debug/alias.cc",
    "debug/alias.h",
    "files/file_path.cc",
    "files/file_path.h",
    "files/file_util.h",
    "files/scoped_file.cc",
    "files/scoped_file.h",
    "format_macros.h",
    "immediate_crash.h",
    "logging.cc",
    "logging.h",
    "memory/free_deleter.h",
    "memory/page_size.h",
    "memory/raw_ptr_exclusion.h",
    "memory/scoped_policy.h",
    "metrics/histogram_functions.h",
    "metrics/histogram_macros.h",
    "metrics/persistent_histogram_allocator.h",
    "notreached.h",
    "numerics/basic_ops_impl.h",
    "numerics/byte_conversions.h",
    "numerics/checked_math.h",
    "numerics/checked_math_impl.h",
    "numerics/clamped_math.h",
    "numerics/clamped_math_impl.h",
    "numerics/safe_conversions.h",
    "numerics/safe_conversions_arm_impl.h",
    "numerics/safe_conversions_impl.h",
    "numerics/safe_math.h",
    "numerics/safe_math_arm_impl.h",
    "numerics/safe_math_clang_gcc_impl.h",
    "numerics/safe_math_shared_impl.h",
    "process/memory.cc",
    "process/memory.h",
    "rand_util.cc",
    "rand_util.h",
    "scoped_clear_last_error.h",
    "scoped_generic.h",
    "strings/pattern.cc",
    "strings/pattern.h",
    "strings/strcat.cc",
    "strings/strcat.h",
    "strings/strcat_internal.h",
    "strings/string_number_conversions.cc",
    "strings/string_number_conversions.h",
    "strings/string_piece.h",
    "strings/string_util.h",
    "strings/stringprintf.cc",
    "strings/stringprintf.h",
    "strings/sys_string_conversions.h",
    "strings/utf_string_conversion_utils.cc",
    "strings/utf_string_conversion_utils.h",
    "strings/utf_string_conversions.cc",
    "strings/utf_string_conversions.h",
    "synchronization/condition_variable.h",
    "synchronization/lock.cc",
    "synchronization/lock.h",
    "synchronization/lock_impl.h",
    "sys_byteorder.h",
    "template_util.h",
    "third_party/icu/icu_utf.cc",
    "third_party/icu/icu_utf.h",
    "threading/thread_local_storage.cc",
    "threading/thread_local_storage.h",
    "types/cxx23_to_underlying.h",
    "types/to_address.h",
  ]

  if (mini_chromium_is_posix || mini_chromium_is_fuchsia) {
    sources += [
      "files/file_util_posix.cc",
      "memory/page_size_posix.cc",
      "posix/eintr_wrapper.h",
      "posix/safe_strerror.cc",
      "posix/safe_strerror.h",
      "strings/string_util_posix.h",
      "synchronization/condition_variable_posix.cc",
      "synchronization/lock_impl_posix.cc",
      "threading/thread_local_storage_posix.cc",
    ]
  }

  if (mini_chromium_is_apple) {
    sources += [
      "apple/bridging.h",
      "apple/foundation_util.h",
      "apple/foundation_util.mm",
      "apple/mach_logging.cc",
      "apple/mach_logging.h",
      "apple/scoped_cftyperef.h",
      "apple/scoped_mach_port.cc",
      "apple/scoped_mach_port.h",
      "apple/scoped_mach_vm.cc",
      "apple/scoped_mach_vm.h",
      "apple/scoped_nsautorelease_pool.h",
      "apple/scoped_nsautorelease_pool.mm",
      "apple/scoped_typeref.h",
      "strings/sys_string_conversions_mac.mm",
    ]
    frameworks = [
      "CoreFoundation.framework",
      "Foundation.framework",
      "Security.framework",
    ]
  }
  if (mini_chromium_is_mac) {
    sources += [
      "mac/close_nocancel.cc",
      "mac/scoped_ioobject.h",
      "mac/scoped_launch_data.h",
    ]
    frameworks += [
      "ApplicationServices.framework",
      "IOKit.framework",
    ]
  } else if (mini_chromium_is_ios) {
    frameworks += [
      "CoreGraphics.framework",
      "CoreText.framework",
    ]
  } else if (mini_chromium_is_win) {
    sources += [
      "memory/page_size_win.cc",
      "scoped_clear_last_error_win.cc",
      "strings/string_util_win.cc",
      "strings/string_util_win.h",
      "synchronization/lock_impl_win.cc",
      "threading/thread_local_storage_win.cc",
    ]
    libs = [ "advapi32.lib" ]
  } else if (mini_chromium_is_fuchsia) {
    sources += [
      "fuchsia/fuchsia_logging.cc",
      "fuchsia/fuchsia_logging.h",
    ]

    if (defined(is_fuchsia_tree) && is_fuchsia_tree) {
      deps = [ "//zircon/system/ulib/syslog" ]
    } else {
      deps = [ "//third_party/fuchsia/sdk/$host_os-amd64/pkg/syslog" ]
    }
  }

  public_configs = [ "../build:mini_chromium_config" ]

  public_deps = [ "../build" ]

  if (mini_chromium_is_apple) {
    public_configs += [ "../build/config:apple_enable_arc" ]
  }

  if (mini_chromium_is_android) {
    libs = [ "log" ]
  }
}
