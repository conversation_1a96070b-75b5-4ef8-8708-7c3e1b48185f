// Copyright 2023 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef MINI_CHROMIUM_BASE_MEMORY_RAW_PTR_EXCLUSION_H_
#define MINI_CHROMIUM_BASE_MEMORY_RAW_PTR_EXCLUSION_H_

// Not provided in mini_chromuim, as it's part of PartitionAlloc.
#ifndef RAW_PTR_EXCLUSION
#define RAW_PTR_EXCLUSION
#endif

#endif  // MINI_CHROMIUM_BASE_MEMORY_RAW_PTR_EXCLUSION_H_
