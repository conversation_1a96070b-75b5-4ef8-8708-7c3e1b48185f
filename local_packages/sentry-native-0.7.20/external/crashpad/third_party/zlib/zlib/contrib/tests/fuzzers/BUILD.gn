# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//testing/libfuzzer/fuzzer_test.gni")

# root BUILD depends on this target. Needed for package discovery
group("fuzzers") {
}

fuzzer_test("zlib_uncompress_fuzzer") {
  sources = [ "uncompress_fuzzer.cc" ]
  deps = [ "../../../:zlib" ]
}

fuzzer_test("zlib_inflate_fuzzer") {
  sources = [ "inflate_fuzzer.cc" ]
  deps = [ "../../../:zlib" ]
}

fuzzer_test("zlib_inflate_with_header_fuzzer") {
  sources = [ "inflate_with_header_fuzzer.cc" ]
  deps = [ "../../../:zlib" ]
}

fuzzer_test("zlib_streaming_inflate_fuzzer") {
  sources = [ "streaming_inflate_fuzzer.cc" ]
  deps = [ "../../../:zlib" ]
  libfuzzer_options = [ "max_len=256000" ]
}

fuzzer_test("zlib_deflate_set_dictionary_fuzzer") {
  sources = [ "deflate_set_dictionary_fuzzer.cc" ]
  deps = [ "../../../:zlib" ]
}

fuzzer_test("zlib_deflate_fuzzer") {
  sources = [ "deflate_fuzzer.cc" ]
  deps = [ "../../../:zlib" ]
}
