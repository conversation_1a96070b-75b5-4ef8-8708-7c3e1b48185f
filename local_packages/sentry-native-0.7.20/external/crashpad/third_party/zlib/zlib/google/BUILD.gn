# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build_overrides/build.gni")

if (build_with_chromium && is_ios) {
  import("//build/config/ios/bundle_data_from_filelist.gni")
}

if (build_with_chromium) {
  static_library("zip") {
    sources = [
      "redact.h",
      "zip.cc",
      "zip.h",
      "zip_internal.cc",
      "zip_internal.h",
      "zip_reader.cc",
      "zip_reader.h",
      "zip_writer.cc",
      "zip_writer.h",
    ]
    deps = [
      "..:minizip",
      "//base",
      "//base:i18n",
    ]
  }

  static_library("compression_utils") {
    sources = [
      "compression_utils.cc",
      "compression_utils.h",
    ]
    deps = [
      "..",
      "//base",
    ]
    public_deps = [ ":compression_utils_portable" ]
  }

  if (is_ios) {
    bundle_data_from_filelist("zlib_pak_bundle_data") {
      testonly = true
      filelist_name = "test_data.filelist"
    }
  }
}

# This allows other users of Chromium's zlib library, but don't use Chromium's
# //base, to reuse some boilerplate code.
static_library("compression_utils_portable") {
  sources = [
    "compression_utils_portable.cc",
    "compression_utils_portable.h",
  ]
  public_deps = [ ".." ]
}
