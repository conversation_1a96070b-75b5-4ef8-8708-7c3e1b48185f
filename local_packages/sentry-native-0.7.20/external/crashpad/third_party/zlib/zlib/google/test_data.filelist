# Copyright 2023 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
# NOTE: this file is generated by build/ios/update_bundle_filelist.py
#       If it requires updating, you should get a presubmit error with
#       instructions on how to regenerate. Otherwise, do not edit.
test/data/Different Encryptions.zip
test/data/Empty Dir Same Name As File.zip
test/data/Mixed Paths.zip
test/data/Parent Dir Same Name As File.zip
test/data/README.md
test/data/Repeated Dir Name.zip
test/data/Repeated File Name With Different Cases.zip
test/data/Repeated File Name.zip
test/data/SJIS Bug 846195.zip
test/data/Windows Special Names.zip
test/data/Wrong CRC.zip
test/data/create_test_zip.sh
test/data/empty.zip
test/data/evil.zip
test/data/evil_via_absolute_file_name.zip
test/data/evil_via_invalid_utf8.zip
test/data/test.zip
test/data/test/foo.txt
test/data/test/foo/bar.txt
test/data/test/foo/bar/.hidden
test/data/test/foo/bar/baz.txt
test/data/test/foo/bar/quux.txt
test/data/test_encrypted.zip
test/data/test_mismatch_size.zip
test/data/test_nocompress.zip
test/data/test_posix_permissions.zip
