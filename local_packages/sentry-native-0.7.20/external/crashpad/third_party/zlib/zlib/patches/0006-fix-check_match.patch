From 8304bdda5293ffd5b3efce8e4f54904b387029d6 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 23 Sep 2020 16:36:38 +0200
Subject: [PATCH] Avoid crashing in check_match when prev_match == -1

prev_match can be set to -1 after sliding the window. In that case, the
window has slid past the first byte of the last match, which means it
cannot be compared in check_match.

This would cause zlib to crash on some inputs to deflate when built
with ZLIB_DEBUG enabled.

Check for this situation and avoid crashing by not trying to compare
the first byte.

Bug: 1113142
---
 third_party/zlib/deflate.c | 8 +++++++-
 1 file changed, 7 insertions(+), 1 deletion(-)

diff --git a/third_party/zlib/deflate.c b/third_party/zlib/deflate.c
index cfdd2f46b230..d70732ec6fc2 100644
--- a/third_party/zlib/deflate.c
+++ b/third_party/zlib/deflate.c
@@ -2060,7 +2060,13 @@ local block_state deflate_slow(s, flush)
             uInt max_insert = s->strstart + s->lookahead - MIN_MATCH;
             /* Do not insert strings in hash table beyond this. */
 
-            check_match(s, s->strstart-1, s->prev_match, s->prev_length);
+            if (s->prev_match == -1) {
+                /* The window has slid one byte past the previous match,
+                 * so the first byte cannot be compared. */
+                check_match(s, s->strstart, s->prev_match+1, s->prev_length-1);
+            } else {
+                check_match(s, s->strstart-1, s->prev_match, s->prev_length);
+            }
 
             _tr_tally_dist(s, s->strstart -1 - s->prev_match,
                            s->prev_length - MIN_MATCH, bflush);
-- 
2.28.0.681.g6f77f65b4e-goog

