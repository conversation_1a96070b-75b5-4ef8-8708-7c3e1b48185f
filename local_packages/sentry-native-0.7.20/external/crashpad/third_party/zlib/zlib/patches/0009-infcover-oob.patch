From 75690b2683667be5535ac6243438115dc9c40f6a Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Wed, 16 Mar 2022 16:38:36 -0700
Subject: [PATCH] Fix out of bounds in infcover.c.

---
 test/infcover.c | 4 +++-
 1 file changed, 3 insertions(+), 1 deletion(-)

diff --git a/test/infcover.c b/test/infcover.c
index 2be01646c..a6d83693c 100644
--- a/test/infcover.c
+++ b/test/infcover.c
@@ -373,7 +373,9 @@ local void cover_support(void)
     mem_setup(&strm);
     strm.avail_in = 0;
     strm.next_in = Z_NULL;
-    ret = inflateInit_(&strm, ZLIB_VERSION - 1, (int)sizeof(z_stream));
+    char versioncpy[] = ZLIB_VERSION;
+    versioncpy[0] -= 1;
+    ret = inflateInit_(&strm, versioncpy, (int)sizeof(z_stream));
                                                 assert(ret == Z_VERSION_ERROR);
     mem_done(&strm, "wrong version");
 
