# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../build/crashpad_buildconfig.gni")

if (crashpad_is_in_chromium || crashpad_is_in_fuchsia || crashpad_is_in_dart) {
  zlib_source = "external"
} else if (!crashpad_is_win && !crashpad_is_fuchsia) {
  zlib_source = "system"
} else if (crashpad_is_standalone) {
  zlib_source = "embedded"
} else if (crashpad_is_external) {
  zlib_source = "external_with_embedded_build"
}

config("zlib_config") {
  if (zlib_source == "external") {
    defines = [ "CRASHPAD_ZLIB_SOURCE_EXTERNAL" ]
  } else if (zlib_source == "system") {
    defines = [ "CRASHPAD_ZLIB_SOURCE_SYSTEM" ]
  } else if (zlib_source == "embedded") {
    defines = [ "CRASHPAD_ZLIB_SOURCE_EMBEDDED" ]
    include_dirs = [ "zlib" ]
  } else if (zlib_source == "external_with_embedded_build") {
    defines = [ "CRASHPAD_ZLIB_SOURCE_EXTERNAL_WITH_EMBEDDED_BUILD" ]
    include_dirs = [ "../../../../zlib/src" ]
  }
}

config("Wno-sign-compare") {
  cflags = [ "-Wno-sign-compare" ]
}

if (zlib_source == "external") {
  group("zlib") {
    public_configs = [ ":zlib_config" ]
    public_deps = [ "//third_party/zlib" ]
  }
} else if (zlib_source == "system") {
  source_set("zlib") {
    public_configs = [ ":zlib_config" ]
    libs = [ "z" ]
  }
} else if (zlib_source == "embedded" ||
           zlib_source == "external_with_embedded_build") {
  static_library("zlib") {
    if (zlib_source == "embedded") {
      zlib_dir = "zlib"
    } else if (zlib_source == "external_with_embedded_build") {
      zlib_dir = "../../../../zlib/src"
    }
    sources = [
      "$zlib_dir/adler32.c",
      "$zlib_dir/compress.c",
      "$zlib_dir/crc32.c",
      "$zlib_dir/crc32.h",
      "$zlib_dir/deflate.c",
      "$zlib_dir/deflate.h",
      "$zlib_dir/gzclose.c",
      "$zlib_dir/gzguts.h",
      "$zlib_dir/gzlib.c",
      "$zlib_dir/gzread.c",
      "$zlib_dir/gzwrite.c",
      "$zlib_dir/infback.c",
      "$zlib_dir/inffast.c",
      "$zlib_dir/inffast.h",
      "$zlib_dir/inffixed.h",
      "$zlib_dir/inflate.c",
      "$zlib_dir/inflate.h",
      "$zlib_dir/inftrees.c",
      "$zlib_dir/inftrees.h",
      "$zlib_dir/trees.c",
      "$zlib_dir/trees.h",
      "$zlib_dir/uncompr.c",
      "$zlib_dir/zconf.h",
      "$zlib_dir/zlib.h",
      "$zlib_dir/zutil.c",
      "$zlib_dir/zutil.h",
      "zlib_crashpad.h",
    ]

    cflags = []
    defines = [ "HAVE_STDARG_H" ]
    public_configs = [ ":zlib_config" ]

    if (crashpad_is_win) {
      cflags += [
        "/wd4131",  # uses old-style declarator
        "/wd4244",  # conversion from 't1' to 't2', possible loss of data
        "/wd4245",  # conversion from 't1' to 't2', signed/unsigned mismatch
        "/wd4267",  # conversion from 'size_t' to 't', possible loss of data
        "/wd4324",  # structure was padded due to alignment specifier
        "/wd4702",  # unreachable code
      ]
      if (current_cpu == "arm64" && !crashpad_is_clang) {
        # Select code path for clang in zlib to avoid using MSVC x86/x64
        # intrinsics for Windows ARM64.
        # TODO: https://crashpad.chromium.org/bug/267
        defines += [ "__clang__" ]
      }
    } else {
      defines += [
        "HAVE_HIDDEN",
        "HAVE_UNISTD_H",
      ]
    }

    if (crashpad_is_fuchsia) {
      # Fuchsia build's default warnings include -Wsign-compare (indirectly)
      configs += [ ":Wno-sign-compare" ]
    }

    if (crashpad_is_standalone) {
      configs -= [ "//third_party/mini_chromium/mini_chromium/build/config:Wimplicit_fallthrough" ]
    } else if (crashpad_is_external) {
      configs -= [ "//../../mini_chromium/mini_chromium/build/config:Wimplicit_fallthrough" ]
    }

    if (zlib_source == "embedded") {
      sources += [ "$zlib_dir/chromeconf.h" ]

      if (current_cpu == "x86" || current_cpu == "x64") {
        sources += [
          "$zlib_dir/crc_folding.c",
        ]
        if (!crashpad_is_win || crashpad_is_clang) {
          cflags += [
            "-msse4.2",
            "-mpclmul",
          ]
        }
        if (crashpad_is_clang) {
          cflags += [ "-Wno-incompatible-pointer-types" ]
        }
      }
    }
  }
}
