# Copyright 2019 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../build/crashpad_buildconfig.gni")

config("lss_config") {
  if (crashpad_is_in_chromium) {
    defines = [ "CRASHPAD_LSS_SOURCE_EXTERNAL" ]
  } else if (crashpad_is_in_fuchsia) {
    defines = [ "CRASHPAD_LSS_SOURCE_FUCHSIA" ]
  } else {
    defines = [ "CRASHPAD_LSS_SOURCE_EMBEDDED" ]
  }
}

source_set("lss") {
  public_configs = [ ":lss_config" ]

  sources = [ "lss.h" ]
}
