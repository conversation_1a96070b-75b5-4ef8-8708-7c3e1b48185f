# Copyright 2022 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../../build/crashpad_buildconfig.gni")

assert(crashpad_is_win)

# Allows other projects (e.g. Chrome) to wrap this as a dll.
source_set("crashpad_wer_handler") {
  sources = [
    "crashpad_wer.cc",
    "crashpad_wer.h",
  ]
  deps = [ "../../../util:util_registration_protocol" ]
}

crashpad_loadable_module("crashpad_wer") {
  sources = [
    "crashpad_wer.def",
    "crashpad_wer_main.cc",
  ]
  deps = [ ":crashpad_wer_handler" ]
  configs = [ "../../../:crashpad_config" ]
}

source_set("crashpad_wer_test") {
  testonly = true
  sources = [ "crashpad_wer_module_unittest.cc" ]
  deps = [
    ":crashpad_wer",
    ":crashpad_wer_handler",
    "../../../client:client",
    "../../../test:test",
    "../../../third_party/googletest:googletest",
    "../../../util:util_registration_protocol",
  ]
}
