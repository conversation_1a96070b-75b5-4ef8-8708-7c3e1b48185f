# Copyright 2019 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This should be a .cc file, which would allow its attributes to be controlled
# by the *.cc pattern in the root .gitattributes file, but it’s named with a
# .cpp extension instead. This file needs to be built with VC++6, a vintage 1998
# compiler, which might not understand .cc to mean C++.  Rather than setting
# attributes globally for .cpp files, which are undesirable (.cc should be used
# in its place), provide a file-specific mapping here.
/z7_test.cpp text eol=lf
