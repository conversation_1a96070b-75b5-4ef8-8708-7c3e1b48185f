if(@CRASHPAD_ZLIB_SYSTEM@)
    include(CMakeFindDependencyMacro)
    find_dependency(ZLIB)
    target_include_directories(crashpad::zlib INTERFACE ${ZLIB_INCLUDE_DIRS})
    target_compile_definitions(crashpad::zlib INTERFACE ${ZLIB_COMPILE_DEFINITIONS})
    target_link_libraries(crashpad::zlib INTERFACE ${ZLIB_LIBRARIES})
endif()

include("${CMAKE_CURRENT_LIST_DIR}/crashpad-targets.cmake")

@PACKAGE_INIT@