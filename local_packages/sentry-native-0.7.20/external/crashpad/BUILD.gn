# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("build/crashpad_buildconfig.gni")
import("build/test.gni")
import("util/net/tls.gni")

config("crashpad_config") {
  include_dirs = [
    ".",
    root_gen_dir,
  ]
}

if (crashpad_is_in_chromium || crashpad_is_in_fuchsia) {
  test("crashpad_tests") {
    deps = [
      "client:client_test",
      "minidump:minidump_test",
      "snapshot:snapshot_test",
      "test:googlemock_main",
      "test:test_test",
      "util:util_test",
    ]
    if (!crashpad_is_ios && !crashpad_is_fuchsia) {
      deps += [ "handler:handler_test" ]
    }
    if (crashpad_is_in_fuchsia) {
      # TODO(fuchsia:46559): Fix the leaks and remove this.
      deps += [ "//build/config/sanitizers:suppress-lsan.DO-NOT-USE-THIS" ]
      # TODO(fxbug.dev/42059784): Remove this once the underlying issue is
      # addressed.
      exclude_toolchain_tags = [ "hwasan" ]
    }
    if (crashpad_is_android) {
      use_raw_android_executable = true

      copy("crashpad_test_data") {
        testonly = true
        sources = [
          "test/test_paths_test_data_root.txt",
          "util/net/testdata/ascii_http_body.txt",
          "util/net/testdata/binary_http_body.dat",
        ]

        outputs = [ "$root_out_dir/crashpad_test_data/{{source}}" ]
      }

      deps += [ ":crashpad_test_data" ]

      extra_dist_files = [
        "$root_out_dir/crashpad_handler",
        "$root_out_dir/crashpad_test_test_multiprocess_exec_test_child",
        "$root_out_dir/crashpad_test_data",
      ]
    }
  }

  if (crashpad_is_in_fuchsia) {
    import("//build/components.gni")
    fuchsia_test_component("crashpad-test-component") {
      manifest = "test/fuchsia_crashpad_tests.cml"
      deps = [
        ":crashpad-test-resources",
        ":crashpad_tests",
        "snapshot:crashpad_snapshot_test_both_dt_hash_styles",
        "snapshot:crashpad_snapshot_test_module",
        "snapshot:crashpad_snapshot_test_module_large",
        "snapshot:crashpad_snapshot_test_module_small",
        "test:crashpad_test_test_multiprocess_exec_test_child",
        "util:http_transport_test_server",
      ]
    }

    fuchsia_test_package("crashpad-test") {
      test_components = [ ":crashpad-test-component" ]

      deps = [
        "//src/connectivity/network/dns:component",
        "//src/connectivity/network/netstack:component",
      ]

      test_specs = {
        log_settings = {
          max_severity = "FATAL"
        }
      }
    }

    _resource_files = [
      "test/test_paths_test_data_root.txt",
      "util/net/testdata/ascii_http_body.txt",
      "util/net/testdata/binary_http_body.dat",
    ]
    if (crashpad_use_boringssl_for_http_transport_socket) {
      _resource_files += [
        "util/net/testdata/crashpad_util_test_cert.pem",
        "util/net/testdata/crashpad_util_test_key.pem",
      ]
    }

    _resources = []
    foreach(resource_file, _resource_files) {
      _resource_file_target = string_replace(resource_file, "/", "_")
      resource("${_resource_file_target}") {
        sources = [ "${resource_file}" ]
        outputs = [ "data/${resource_file}" ]
      }
      _resources += [ ":${_resource_file_target}" ]
    }

    group("crashpad-test-resources") {
      deps = _resources
    }

    group("tests") {
      testonly = true

      deps = [ ":crashpad-test" ]
    }
  }
} else if (crashpad_is_standalone || crashpad_is_external) {
  test("crashpad_client_test") {
    deps = [
      "client:client_test",
      "test:googlemock_main",
    ]
  }

  test("crashpad_handler_test") {
    deps = [
      "handler:handler_test",
      "test:googletest_main",
    ]
    if (crashpad_is_ios || crashpad_is_fuchsia) {
      deps -= [ "handler:handler_test" ]
    }
  }

  test("crashpad_minidump_test") {
    deps = [
      "minidump:minidump_test",
      "test:googletest_main",
    ]
  }

  test("crashpad_snapshot_test") {
    deps = [
      "snapshot:snapshot_test",
      "test:googlemock_main",
    ]
  }

  test("crashpad_test_test") {
    deps = [
      "test:googlemock_main",
      "test:test_test",
    ]
  }

  test("crashpad_util_test") {
    deps = [
      "test:googlemock_main",
      "util:util_test",
    ]
  }
}

if (crashpad_is_ios) {
  group("ios_xcuitests") {
    testonly = true
    deps = [ "test/ios:all_tests" ]
  }
}
