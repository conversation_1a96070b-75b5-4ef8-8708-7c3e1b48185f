<!--
Copyright 2015 The Crashpad Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

# Crashpad

[Crashpad](https://crashpad.chromium.org/) is a crash-reporting system.

## Documentation

 * [Project status](doc/status.md)
 * [Developing Crashpad](doc/developing.md): instructions for getting the source
   code, building, testing, and contributing to the project.
 * [Crashpad interface documentation](https://crashpad.chromium.org/doxygen/)
 * [Crashpad tool man pages](doc/man.md)
 * [Crashpad overview design](doc/overview_design.md)

## Source Code

Crashpad’s source code is hosted in a Git repository at
https://chromium.googlesource.com/crashpad/crashpad.

## Other Links

 * Bugs can be reported at the [Crashpad issue
   tracker](https://crashpad.chromium.org/bug/).
 * The [Crashpad bots](https://ci.chromium.org/p/crashpad/g/main/console)
   perform automated builds and tests.
 * [crashpad-dev](https://groups.google.com/a/chromium.org/group/crashpad-dev)
   is the Crashpad developers’ mailing list.

## Sentry modifications

See [README.getsentry.md](README.getsentry.md) for more information on the
changes, and on maintaining the fork.

Generating patch:

    git format-patch --stdout master...HEAD > getsentry.patch
