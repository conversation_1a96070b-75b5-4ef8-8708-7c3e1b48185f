# Copyright 2015 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../build/crashpad_buildconfig.gni")

config("compat_config") {
  include_dirs = []

  if (crashpad_is_apple) {
    include_dirs += [ "mac" ]
  }

  if (crashpad_is_ios) {
    include_dirs += [ "ios" ]
  }

  if (crashpad_is_linux || crashpad_is_android) {
    include_dirs += [ "linux" ]
  }

  if (crashpad_is_android) {
    include_dirs += [ "android" ]
  }

  if (crashpad_is_win) {
    include_dirs += [ "win" ]
  } else {
    include_dirs += [ "non_win" ]
  }
}

template("compat_target") {
  if (crashpad_is_apple) {
    # There are no sources to compile, which doesn’t mix will with a
    # static_library.
    group(target_name) {
      forward_variables_from(invoker, "*")
      not_needed([ "configs" ])
    }
  } else {
    crashpad_static_library(target_name) {
      forward_variables_from(invoker, "*", [ "configs" ])
      if (!defined(configs)) {
        configs = []
      }
      if (defined(invoker.configs)) {
        configs += invoker.configs
      }
    }
  }
}

compat_target("compat") {
  sources = []

  if (crashpad_is_apple) {
    sources += [
      "mac/Availability.h",
      "mac/AvailabilityVersions.h",
      "mac/kern/exc_resource.h",
      "mac/mach-o/loader.h",
      "mac/mach/i386/thread_state.h",
      "mac/mach/mach.h",
      "mac/sys/resource.h",
    ]
  } else {
    sources += [ "non_mac/mach/mach.h" ]
  }

  if (crashpad_is_ios) {
    sources += [
      "ios/mach/exc.defs",
      "ios/mach/mach_exc.defs",
      "ios/mach/mach_types.defs",
      "ios/mach/machine/machine_types.defs",
      "ios/mach/std_types.defs",
    ]
  }

  if (crashpad_is_linux || crashpad_is_android) {
    sources += [
      "linux/signal.h",
      "linux/sys/mman.h",
      "linux/sys/mman_memfd_create.cc",
      "linux/sys/ptrace.h",
      "linux/sys/user.h",
    ]
  }

  if (crashpad_is_android) {
    sources += [
      "android/dlfcn_internal.cc",
      "android/dlfcn_internal.h",
      "android/elf.h",
      "android/linux/elf.h",
      "android/linux/prctl.h",
      "android/linux/ptrace.h",
      "android/sched.h",
      "android/sys/epoll.cc",
      "android/sys/epoll.h",
      "android/sys/mman.h",
      "android/sys/mman_mmap.cc",
      "android/sys/syscall.h",
      "android/sys/user.h",
    ]
  }

  if (crashpad_is_win) {
    sources += [
      "win/getopt.h",
      "win/strings.cc",
      "win/strings.h",
      "win/sys/types.h",
      "win/time.cc",
      "win/time.h",
      "win/winbase.h",
      "win/winnt.h",
      "win/winternl.h",
    ]
  } else {
    sources += [
      "non_win/dbghelp.h",
      "non_win/minwinbase.h",
      "non_win/timezoneapi.h",
      "non_win/verrsrc.h",
      "non_win/windows.h",
      "non_win/winnt.h",
    ]
  }

  public_configs = [
    ":compat_config",
    "..:crashpad_config",
  ]

  if (!crashpad_is_win) {
    public_deps = [ "$mini_chromium_source_parent:base" ]
  }

  deps = [ "../util:no_cfi_icall" ]

  if (crashpad_is_win) {
    deps += [ "../third_party/getopt" ]
  }
}
