@AUTO_GEN_COMMENT@

@SERIALIZED_LIT_PARAMS@

import os
import site

config.cxx_under_test           = "@CMAKE_CXX_COMPILER@"
config.project_obj_root         = "@CMAKE_BINARY_DIR@"
config.install_root             = "@CMAKE_BINARY_DIR@"
config.libunwind_src_root       = "@LIBUNWIND_SOURCE_DIR@"
config.libunwind_obj_root       = "@LIBUNWIND_BINARY_DIR@"
config.abi_library_root         = "@LIBUNWIND_LIBRARY_DIR@"
config.libcxx_src_root          = "@LIBUNWIND_LIBCXX_PATH@"
config.libunwind_headers        = "@LIBUNWIND_SOURCE_DIR@/include"
config.cxx_library_root         = "@LIBUNWIND_LIBCXX_LIBRARY_PATH@"
config.llvm_unwinder            = True
config.builtins_library         = "@LIBUNWIND_BUILTINS_LIBRARY@"
config.enable_threads           = @LIBUNWIND_ENABLE_THREADS@
config.target_info              = "@LIBUNWIND_TARGET_INFO@"
config.test_linker_flags        = "@LIBUNWIND_TEST_LINKER_FLAGS@"
config.test_compiler_flags      = "@LIBUNWIND_TEST_COMPILER_FLAGS@"
config.executor                 = "@LIBUNWIND_EXECUTOR@"
config.libunwind_shared         = @LIBUNWIND_ENABLE_SHARED@
config.enable_shared            = @LIBCXX_ENABLE_SHARED@
config.arm_ehabi                = @LIBUNWIND_USES_ARM_EHABI@
config.host_triple              = "@LLVM_HOST_TRIPLE@"
config.sysroot                  = "@LIBUNWIND_SYSROOT@"
config.gcc_toolchain            = "@LIBUNWIND_GCC_TOOLCHAIN@"
config.cxx_ext_threads          = @LIBUNWIND_BUILD_EXTERNAL_THREAD_LIBRARY@
config.x86_cet                  = @LIBUNWIND_ENABLE_CET@

site.addsitedir(os.path.join(config.libunwind_src_root, 'test'))
site.addsitedir(os.path.join(config.libcxx_src_root, 'utils'))

# name: The name of this test suite.
config.name = 'libunwind'

# suffixes: A list of file extensions to treat as test files.
config.suffixes = ['.cpp', '.s']

# test_source_root: The root path where tests are located.
config.test_source_root = os.path.join(config.libunwind_src_root, 'test')

# Allow expanding substitutions that are based on other substitutions
config.recursiveExpansionLimit = 10

# Infer the test_exec_root from the build directory.
config.test_exec_root = os.path.join(config.libunwind_obj_root, 'test')

import libcxx.test.format
config.test_format = libcxx.test.format.CxxStandardLibraryTest()

lit_config.note('Using configuration variant: libunwind')
import libunwind.test.config
configuration = libunwind.test.config.Configuration(lit_config, config)
configuration.configure()
configuration.print_config_info()
