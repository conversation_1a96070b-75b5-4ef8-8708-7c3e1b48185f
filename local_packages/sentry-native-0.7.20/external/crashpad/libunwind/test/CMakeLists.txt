include(AddLLVM) # for add_lit_testsuite
macro(pythonize_bool var)
  if (${var})
    set(${var} True)
  else()
    set(${var} False)
  endif()
endmacro()

if (NOT DEFINED LIBCXX_ENABLE_SHARED)
  set(LIBCXX_ENABLE_SHARED ON)
endif()

pythonize_bool(LIBUNWIND_BUILD_32_BITS)
pythonize_bool(LIBUNWIND_ENABLE_CET)
pythonize_bool(LIBCXX_ENABLE_SHARED)
pythonize_bool(LIBUNWIND_ENABLE_SHARED)
pythonize_bool(LIBUNWIND_ENABLE_THREADS)
pythonize_bool(LIBUNWIND_USES_ARM_EHABI)
pythonize_bool(LIBUNWIND_USE_COMPILER_RT)
pythonize_bool(LIBUNWIND_BUILD_EXTERNAL_THREAD_LIBRARY)
set(LIBUNWIND_TARGET_INFO "libcxx.test.target_info.LocalTI" CACHE STRING
    "TargetInfo to use when setting up test environment.")
set(LIBUNWIND_EXECUTOR "${Python3_EXECUTABLE} ${LIBUNWIND_LIBCXX_PATH}/utils/run.py" CACHE STRING
    "Executor to use when running tests.")

set(AUTO_GEN_COMMENT "## Autogenerated by libunwind configuration.\n# Do not edit!")
set(SERIALIZED_LIT_PARAMS "# Lit parameters serialized here for llvm-lit to pick them up\n")

macro(serialize_lit_param param value)
  string(APPEND SERIALIZED_LIT_PARAMS "config.${param} = ${value}\n")
endmacro()

serialize_lit_param(enable_experimental False)

if (LLVM_USE_SANITIZER)
  serialize_lit_param(use_sanitizer "\"${LLVM_USE_SANITIZER}\"")
endif()

if (LIBUNWIND_TARGET_TRIPLE)
  serialize_lit_param(target_triple "\"${LIBUNWIND_TARGET_TRIPLE}\"")
endif()

if (LIBUNWIND_BUILD_32_BITS)
  serialize_lit_param(enable_32bit True)
endif()

foreach(param IN LISTS LIBUNWIND_TEST_PARAMS)
  string(REGEX REPLACE "(.+)=(.+)" "\\1" name "${param}")
  string(REGEX REPLACE "(.+)=(.+)" "\\2" value "${param}")
  serialize_lit_param("${name}" "\"${value}\"")
endforeach()

configure_lit_site_cfg(
  "${LIBUNWIND_TEST_CONFIG}"
  ${CMAKE_CURRENT_BINARY_DIR}/lit.site.cfg
  MAIN_CONFIG "${CMAKE_CURRENT_SOURCE_DIR}/lit.cfg.py")

add_lit_testsuite(check-unwind "Running libunwind tests"
  ${CMAKE_CURRENT_BINARY_DIR}
  DEPENDS unwind ${LIBUNWIND_TEST_DEPS})
