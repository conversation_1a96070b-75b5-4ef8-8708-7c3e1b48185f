# Copyright 2020 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../../build/crashpad_buildconfig.gni")

if (crashpad_is_in_chromium) {
  import("//build/config/ios/rules.gni")
} else if (crashpad_is_standalone) {
  import("//third_party/mini_chromium/mini_chromium/build/ios/rules.gni")
}

source_set("app_shared_sources") {
  testonly = true
  sources = [ "cptest_shared_object.h" ]
  configs += [ "../../..:crashpad_config" ]
  deps = [ "../../../build:apple_enable_arc" ]
  frameworks = [ "UIKit.framework" ]
}

static_library("app_host_sources") {
  testonly = true
  sources = [
    "cptest_application_delegate.h",
    "cptest_application_delegate.mm",
    "cptest_crash_view_controller.h",
    "cptest_crash_view_controller.mm",
    "handler_forbidden_allocators.cc",
    "handler_forbidden_allocators.h",
    "main.mm",
  ]
  configs += [ "../../..:crashpad_config" ]
  deps = [
    ":app_shared_sources",
    "../../../build:apple_enable_arc",
    "../../../client",
    "../../../snapshot",
    "../../../test",
    "../../../third_party/edo",
  ]
  frameworks = [
    "Foundation.framework",
    "UIKit.framework",
  ]
}

# TODO(justincohen): Codesign crashy_initializer.so so it can run on devices.
bundle_data("crashy_module_bundle") {
  testonly = true
  sources =
      [ "$root_out_dir/crashpad_snapshot_test_module_crashy_initializer.so" ]
  outputs = [ "{{bundle_contents_dir}}/crashpad_snapshot_test_module_crashy_initializer.so" ]
  public_deps =
      [ "../../../snapshot:crashpad_snapshot_test_module_crashy_initializer" ]
}

ios_app_bundle("ios_crash_xcuitests") {
  testonly = true
  info_plist = "Info.plist"
  if (crashpad_is_in_chromium) {
    bundle_identifier = shared_bundle_id_for_test_apps
  }
  deps = [
    ":app_host_sources",
    ":crashy_module_bundle",
  ]
}
