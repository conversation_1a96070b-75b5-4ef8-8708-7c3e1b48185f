# Copyright 2020 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../build/crashpad_buildconfig.gni")

if (crashpad_is_in_chromium) {
  import("//build/config/ios/ios_test_runner_xcuitest.gni")
  import("//build/config/ios/rules.gni")
} else if (crashpad_is_standalone) {
  import("//third_party/mini_chromium/mini_chromium/build/ios/rules.gni")
}

group("all_tests") {
  testonly = true
  deps = [
    ":ios_crash_xcuitests_module",
    "host:ios_crash_xcuitests",
  ]
}

source_set("google_test_runner_shared_headers") {
  testonly = true
  sources = [ "cptest_google_test_runner_delegate.h" ]
}

source_set("google_test_runner") {
  testonly = true
  sources = [ "cptest_google_test_runner.mm" ]
  configs += [ "../..:crashpad_config" ]
  deps = [
    "../$mini_chromium_source_parent:base",
    "../../build:apple_enable_arc",
    "../../build:ios_xctest",
    "../../test/ios:google_test_runner_shared_headers",
  ]
  frameworks = [ "UIKit.framework" ]
}

source_set("google_test_setup") {
  testonly = true
  sources = [
    "google_test_setup.h",
    "google_test_setup.mm",
  ]
  configs += [ "../..:crashpad_config" ]
  deps = [
    ":google_test_runner_shared_headers",
    "../$mini_chromium_source_parent:base",
    "../../build:apple_enable_arc",
    "../../third_party/googletest:googletest",
  ]
  frameworks = [ "UIKit.framework" ]
}

source_set("xcuitests") {
  testonly = true
  sources = [ "crash_type_xctest.mm" ]
  configs += [
    "../..:crashpad_config",
    "../../build:crashpad_is_in_chromium",
  ]

  deps = [
    "../../build:apple_enable_arc",
    "../../build:ios_xctest",
    "../../client:common",
    "../../test/ios/host:app_shared_sources",
    "../../third_party/edo",
    "../../util",
  ]
}

ios_test_runner_xcuitest("ios_crash_xcuitests_module") {
  xcode_test_application_name = "ios_crash_xcuitests"
  deps = [ ":xcuitests" ]
  data_deps = [ "host:ios_crash_xcuitests" ]
}
