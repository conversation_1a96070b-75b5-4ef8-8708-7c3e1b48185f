// Copyright 2017 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "test/hex_string.h"

#include "base/strings/stringprintf.h"

namespace crashpad {
namespace test {

std::string BytesToHexString(const void* bytes, size_t length) {
  const unsigned char* bytes_c = reinterpret_cast<const unsigned char*>(bytes);

  std::string hex_string;
  hex_string.reserve(length * 2);
  for (size_t index = 0; index < length; ++index) {
    hex_string.append(base::StringPrintf("%02x", bytes_c[index]));
  }

  return hex_string;
}

}  // namespace test
}  // namespace crashpad
