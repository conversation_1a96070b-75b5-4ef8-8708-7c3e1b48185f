// Copyright 2015 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "snapshot/win/memory_map_region_snapshot_win.h"

namespace crashpad {
namespace internal {

MemoryMapRegionSnapshotWin::MemoryMapRegionSnapshotWin(
    const MEMORY_BASIC_INFORMATION64& mbi)
    : memory_info_() {
  memory_info_.BaseAddress = mbi.BaseAddress;
  memory_info_.AllocationBase = mbi.AllocationBase;
  memory_info_.AllocationProtect = mbi.AllocationProtect;
  memory_info_.RegionSize = mbi.RegionSize;
  memory_info_.State = mbi.State;
  memory_info_.Protect = mbi.Protect;
  memory_info_.Type = mbi.Type;
}

MemoryMapRegionSnapshotWin::~MemoryMapRegionSnapshotWin() {
}

const MINIDUMP_MEMORY_INFO& MemoryMapRegionSnapshotWin::AsMinidumpMemoryInfo()
    const {
  return memory_info_;
}

}  // namespace internal
}  // namespace crashpad
