This is the App Engine app that serves https://crashpad.chromium.org/.

To work on this app, obtain the following packages:

 - Go, from https://golang.org/dl/. This is only necessary for local development
   and testing. The directory containing the “go” executable, such as
   /usr/local/go/bin, must appear in $PATH. It does not appear critical for the
   Go version used to match the Go runtime version used (for example, these
   instructions were tested with Go 1.14 locally but a Go 1.11 runtime when
   deployed), but if problems are encountered, it would be wise to use the same
   version for both local development and AppEngine deployment.
 - The Google Cloud SDK (gcloud CLI) from
   https://cloud.google.com/sdk/docs/install-sdk. This is necessary for both
   local development and for AppEngine deployment. Unpacking this package
   produces a google-cloud-sdk directory, whose bin child directory may be
   added to $PATH for convenience, although this is not strictly necessary.

The commands in this README are expected to be run from the directory containing
it.

To test locally:

% go get -d ./src/crashpad-home
% python3 …/google-cloud-sdk/bin/dev_appserver.py src/crashpad-home

dev_appserver.py must be invoked using Python 3, but internally will use Python
2, and a Python 2 interpreter must be available in the PATH as python2.

Look for the “Starting module "default" running at: http://localhost:8080” line,
which tells you the URL of the local running instance of the app. Test
http://localhost:8080/ to ensure that it works.

It would be good to test http://localhost:8080/doxygen as well, but it may fail
with HTTP status 500 and the following error returned as the HTTP response body
because memcache seems to not be available in the local dev_appserver
environment:

service bridge HTTP failed: Post "http://appengine.googleapis.internal:10001/rpc_http": dial tcp: lookup appengine.googleapis.internal: no such host

The /doxygen URL can be tested in a verison of the app that’s been deployed
before traffic has been migrated to it by visiting the staged deployed version
from the App Engine console.

To deploy:

% version=$(git rev-parse --short=12 HEAD)
% [[ -n "$(git status --porcelain)" ]] && version+=-dirty
% …/google-cloud-sdk/bin/gcloud app deploy \
      --project=crashpad-home --version="${version}" --no-promote \
      "$(pwd)/src/crashpad-home"

(Note: the $(pwd) is necessary for “gcloud app deploy” to recognize that the
application is in GOPATH, putting it into “GOPATH mode”. This normally happens
correctly on its own even with a relative path, but will fail for relative
paths when $(pwd) is a symbolic link. Using an absolute path here will save you
from this frustration, freeing you up to undoubtedly experience other
frustrations.)

Activate a newly-deployed version by visiting the App Engine console at
https://console.cloud.google.com/appengine/versions?project=crashpad-home,
selecting it, and choosing “Migrate Traffic”. It is also possible to delete old
versions from this page when they are no longer needed.
