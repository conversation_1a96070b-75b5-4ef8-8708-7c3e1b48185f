name: Build

on:
  push:
    branches:
      - getsentry
  pull_request:

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Ubuntu (GCC 13.3.0)
            platform: ubuntu-24.04
            CC: gcc
            CXX: g++
          - name: Ubuntu (GCC 11.4.0)
            platform: ubuntu-22.04
            CC: gcc
            CXX: g++
          - name: Ubuntu (GCC 9.4.0)
            platform: ubuntu-20.04
            CC: gcc
            CXX: g++
          - name: Ubuntu (clang 18.1.3)
            platform: ubuntu-24.04
            CC: clang
            CXX: clang++
          - name: Ubuntu (clang 14.0.0)
            platform: ubuntu-22.04
            CC: clang
            CXX: clang++
          - name: Windows (x64)
            platform: windows-latest
          - name: Windows (arm64)
            platform: windows-latest
            CMAKE_DEFINES: -DCMAKE_TOOLCHAIN_FILE=cmake/toolchains/win_arm64.cmake -A arm64
          - name: LLVM-MINGW (x64)
            platform: windows-latest
            MINGW: 1
            MINGW_PKG_PREFIX: x86_64-w64-mingw32
            MINGW_ASM_MASM_COMPILER: llvm-ml;-m64
            CMAKE_DEFINES: -DCRASHPAD_ZLIB_SYSTEM=OFF -DCRASHPAD_BUILD_TOOLS=OFF -G Ninja
          - name: LLVM-MINGW (arm64)
            platform: windows-latest
            MINGW: 1
            MINGW_PKG_PREFIX: aarch64-w64-mingw32
            CMAKE_DEFINES: -DCRASHPAD_ZLIB_SYSTEM=OFF -DCRASHPAD_BUILD_TOOLS=OFF -DCMAKE_TOOLCHAIN_FILE=cmake/toolchains/mingw_arm64.cmake -G Ninja
          - name: macOS
            platform: macos-latest
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.platform }}
    env:
      CMAKE_DEFINES: ${{ matrix.CMAKE_DEFINES }}
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"

      - name: Installing Linux Dependencies
        if: ${{ runner.os == 'Linux' }}
        run: |
          sudo apt update
          sudo apt install clang zlib1g-dev libcurl4-openssl-dev libssl-dev libunwind-dev pkg-config

      - name: Installing LLVM-MINGW Dependencies
        if: ${{ runner.os == 'Windows' && matrix.MINGW == '1' }}
        shell: powershell
        env:
          MINGW_PKG_PREFIX: ${{ matrix.MINGW_PKG_PREFIX }}
          MINGW_ASM_MASM_COMPILER: ${{ matrix.MINGW_ASM_MASM_COMPILER }}
        run: . "cmake\scripts\install-llvm-mingw.ps1"

      - name: Build crashpad
        shell: bash
        run: |
          [ "${{ matrix.CC }}" ] && export CC="${{ matrix.CC }}"
          [ "${{ matrix.CXX }}" ] && export CXX="${{ matrix.CXX }}"
          echo "CMAKE_DEFINES=${CMAKE_DEFINES}"
          cmake -B cmake-build -D CRASHPAD_BUILD_TOOLS=On ${CMAKE_DEFINES}
          cmake --build cmake-build --parallel

      - name: Build crashpad with client-side stack traces
        shell: bash
        run: |
          [ "${{ matrix.CC }}" ] && export CC="${{ matrix.CC }}"
          [ "${{ matrix.CXX }}" ] && export CXX="${{ matrix.CXX }}"
          echo "CMAKE_DEFINES=${CMAKE_DEFINES}"
          cmake -B cmake-build-stacks -D CRASHPAD_ENABLE_STACKTRACE=ON ${CMAKE_DEFINES}
          cmake --build cmake-build-stacks --parallel

  build-ios:
    name: 'iOS'
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"
      - run: |
          cmake -B crashpad-xcode -GXcode -DCMAKE_SYSTEM_NAME=iOS
          xcodebuild build -project crashpad-xcode/crashpad.xcodeproj
