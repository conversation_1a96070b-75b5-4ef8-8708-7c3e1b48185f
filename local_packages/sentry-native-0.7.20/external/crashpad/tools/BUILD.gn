# Copyright 2015 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../build/crashpad_buildconfig.gni")

source_set("tool_support") {
  sources = [
    "tool_support.cc",
    "tool_support.h",
  ]

  public_configs = [ "..:crashpad_config" ]

  deps = [ "$mini_chromium_source_parent:base" ]
}

crashpad_executable("dump_minidump_annotations") {
  sources = [ "dump_minidump_annotations.cc" ]

  deps = [
    ":tool_support",
    "../client",
    "../snapshot",
    "../util",
  ]

  if (crashpad_is_win) {
    cflags = [ "/wd4201" ]  # nonstandard extension used : nameless struct/union
  }
}

if (!crashpad_is_ios && !crashpad_is_fuchsia) {
  crashpad_executable("crashpad_database_util") {
    sources = [ "crashpad_database_util.cc" ]

    deps = [
      ":tool_support",
      "$mini_chromium_source_parent:base",
      "../build:default_exe_manifest_win",
      "../client",
      "../compat",
      "../util",
    ]
  }

  crashpad_executable("crashpad_http_upload") {
    sources = [ "crashpad_http_upload.cc" ]

    deps = [
      ":tool_support",
      "$mini_chromium_source_parent:base",
      "../build:default_exe_manifest_win",
      "../compat",
      "../util",
      "../util:net",
    ]
  }
}

crashpad_executable("base94_encoder") {
  sources = [ "base94_encoder.cc" ]
  deps = [
    ":tool_support",
    "$mini_chromium_source_parent:base",
    "../build:default_exe_manifest_win",
    "../util",
  ]
}

if (!crashpad_is_fuchsia && !crashpad_is_ios) {
  crashpad_executable("generate_dump") {
    sources = [ "generate_dump.cc" ]

    deps = [
      ":tool_support",
      "$mini_chromium_source_parent:base",
      "../build:default_exe_manifest_win",
      "../compat",
      "../minidump",
      "../snapshot",
      "../util",
    ]

    if (crashpad_is_mac) {
      # This would be better as a config so that it could be shared with
      # exception_port_tool, but configs can’t alter “inputs”.
      # https://crbug.com/781858.
      inputs = [ "mac/sectaskaccess_info.plist" ]
      ldflags = [
        "-sectcreate",
        "__TEXT",
        "__info_plist",
        rebase_path(inputs[0], root_build_dir),
      ]
    }

    if (crashpad_is_win) {
      cflags =
          [ "/wd4201" ]  # nonstandard extension used : nameless struct/union
    }
  }
}

if (crashpad_is_mac || crashpad_is_fuchsia) {
  crashpad_executable("run_with_crashpad") {
    sources = [ "run_with_crashpad.cc" ]

    deps = [
      ":tool_support",
      "$mini_chromium_source_parent:base",
      "../client",
      "../compat",
      "../util",
    ]
  }
}

if (crashpad_is_mac) {
  crashpad_executable("catch_exception_tool") {
    sources = [ "mac/catch_exception_tool.cc" ]

    deps = [
      ":tool_support",
      "$mini_chromium_source_parent:base",
      "../compat",
      "../util",
    ]
  }

  crashpad_executable("exception_port_tool") {
    sources = [ "mac/exception_port_tool.cc" ]

    # This would be better as a config so that it could be shared with
    # generate_dump, but configs can’t alter “inputs”. https://crbug.com/781858.
    inputs = [ "mac/sectaskaccess_info.plist" ]
    ldflags = [
      "-sectcreate",
      "__TEXT",
      "__info_plist",
      rebase_path(inputs[0], root_build_dir),
    ]

    deps = [
      ":tool_support",
      "$mini_chromium_source_parent:base",
      "../compat",
      "../util",
    ]
  }

  crashpad_executable("on_demand_service_tool") {
    sources = [ "mac/on_demand_service_tool.mm" ]

    frameworks = [
      "CoreFoundation.framework",
      "Foundation.framework",
    ]

    deps = [
      ":tool_support",
      "$mini_chromium_source_parent:base",
      "../build:apple_enable_arc",
      "../compat",
      "../util",
    ]
  }
}
