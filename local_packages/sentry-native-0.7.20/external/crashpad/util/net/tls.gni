# Copyright 2018 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../../build/crashpad_buildconfig.gni")

declare_args() {
  if (crashpad_is_fuchsia || crashpad_is_android) {
    crashpad_http_transport_impl = "socket"
  } else if (crashpad_is_linux) {
    crashpad_http_transport_impl = "libcurl"
  } else {
    crashpad_http_transport_impl = ""
  }

  # TODO(scottmg): https://crbug.com/crashpad/266 fuchsia:DX-690: BoringSSL
  # was removed from the Fuchsia SDK. Re-enable it when we have a way to acquire
  # a BoringSSL lib again.
  crashpad_use_boringssl_for_http_transport_socket =
      crashpad_is_in_fuchsia || (crashpad_is_linux && crashpad_is_in_chromium)
}
