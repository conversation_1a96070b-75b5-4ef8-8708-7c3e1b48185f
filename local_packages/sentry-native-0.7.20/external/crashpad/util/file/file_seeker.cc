// Copyright 2015 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "util/file/file_seeker.h"

#include "base/logging.h"

namespace crashpad {

FileOffset FileSeekerInterface::SeekGet() {
  return Seek(0, SEEK_CUR);
}

bool FileSeekerInterface::SeekSet(FileOffset offset) {
  FileOffset rv = Seek(offset, SEEK_SET);
  if (rv < 0) {
    // Seek() will have logged its own error.
    return false;
  } else if (rv != offset) {
    LOG(ERROR) << "SeekSet(): expected " << offset << ", observed " << rv;
    return false;
  }
  return true;
}

}  // namespace crashpad
