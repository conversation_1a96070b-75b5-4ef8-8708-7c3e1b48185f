// Copyright 2014 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "util/win/scoped_handle.h"

#include "base/check.h"
#include "util/file/file_io.h"

namespace crashpad {
namespace internal {

void ScopedFileHANDLECloseTraits::Free(HANDLE handle) {
  CheckedCloseFile(handle);
}

void ScopedKernelHANDLECloseTraits::Free(HANDLE handle) {
  PCHECK(CloseHandle(handle)) << "CloseHandle";
}

void ScopedSearchHANDLECloseTraits::Free(HANDLE handle) {
  PCHECK(FindClose(handle)) << "FindClose";
}

void ScopedVectoredExceptionRegistrationCloseTraits::Free(PVOID handle) {
  PCHECK(::RemoveVectoredExceptionHandler(handle));
}

}  // namespace internal
}  // namespace crashpad
