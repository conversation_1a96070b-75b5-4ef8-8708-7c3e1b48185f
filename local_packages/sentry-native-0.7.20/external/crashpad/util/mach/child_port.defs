// Copyright 2014 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <mach/mach_types.defs>
#include <mach/std_types.defs>

// child_port provides an interface for port rights to be transferred between
// tasks. Its expected usage is for processes to be able to pass port rights
// across IPC boundaries. A child process may wish to give its parent a copy of
// a send right to its own task port, or a parent process may wish to give a
// receive right to a child process that implements a server.
//
// This Mach subsystem defines the lowest-level interface for these rights to be
// transferred. Most users will not user this interface directly, but will use
// ChildPortHandshake, which builds on this interface by providing client and
// server implementations, along with a protocol for establishing communication
// in a parent-child process relationship.
subsystem child_port 10011;

serverprefix handle_;

type child_port_server_t = mach_port_t;
type child_port_token_t = uint64_t;

import "util/mach/child_port_types.h";

// Sends a Mach port right across an IPC boundary.
//
// server[in]: The server to send the port right to.
// token[in]: A random opaque token, generated by the server and communicated to
//     the client through some secure means such as a shared pipe. The client
//     includes the token in its request to prove its authenticity to the
//     server. This parameter is necessary for instances where the server must
//     publish its service broadly, such as via the bootstrap server. When this
//     is done, anyone with access to the bootstrap server will be able to gain
//     rights to communicate with |server|, and |token| serves as a shared
//     secret allowing the server to verify that it has received a request from
//     the intended client. |server| will reject requests with an invalid
//     |token|.
// port[in]: A port right to transfer to the server.
//
// Return value: As this is a “simpleroutine”, the server does not respond to
//     the client request, and the client does not block waiting for a response
//     after sending its request. The return value is MACH_MSG_SUCCESS if the
//     request was queued for the server, without any indication of whether the
//     server considered the request valid or took any action. On data
//     validation or mach_msg() failure, another code will be returned
//     indicating the nature of the error.
simpleroutine child_port_check_in(server: child_port_server_t;
                                  token: child_port_token_t;
                                  port: mach_port_poly_t);
