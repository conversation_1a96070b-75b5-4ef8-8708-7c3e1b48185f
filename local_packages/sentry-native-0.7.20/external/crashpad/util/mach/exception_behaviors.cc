// Copyright 2014 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "util/mach/exception_behaviors.h"

namespace crashpad {

bool ExceptionBehaviorHasState(exception_behavior_t behavior) {
  const exception_behavior_t basic_behavior = ExceptionBehaviorBasic(behavior);
  return basic_behavior == EXCEPTION_STATE ||
         basic_behavior == EXCEPTION_STATE_IDENTITY;
}

bool ExceptionBehaviorHasIdentity(exception_behavior_t behavior) {
  const exception_behavior_t basic_behavior = ExceptionBehaviorBasic(behavior);
  return basic_behavior == EXCEPTION_DEFAULT ||
         basic_behavior == EXCEPTION_STATE_IDENTITY;
}

bool ExceptionBehaviorHasMachExceptionCodes(exception_behavior_t behavior) {
  return (behavior & MACH_EXCEPTION_CODES) != 0;
}

exception_behavior_t ExceptionBehaviorBasic(exception_behavior_t behavior) {
  return behavior & ~MACH_EXCEPTION_CODES;
}

}  // namespace crashpad
