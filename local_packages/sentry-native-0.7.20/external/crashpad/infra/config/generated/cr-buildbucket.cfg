# Auto-generated by lucicfg.
# Do not modify manually.
#
# For the schema of this file, see BuildbucketCfg message:
#   https://config.luci.app/schemas/projects:buildbucket.cfg

buckets {
  name: "ci"
  acls {
    role: WRITER
    group: "project-crashpad-admins"
  }
  acls {
    group: "all"
  }
  acls {
    role: SCHEDULER
    identity: "user:<EMAIL>"
  }
  swarming {
    builders {
      name: "crashpad_fuchsia_arm64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_fuchsia_arm64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_fuchsia_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_fuchsia_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_arm64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_arm64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_linux_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "linux"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_linux_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "linux"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_mac_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "mac"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_mac"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_mac_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "mac"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_mac"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_win_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Windows-10"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$depot_tools/windows_sdk": {'
        '    "version": "uploaded:2024-01-11"'
        '  },'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "win"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_win_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Windows-10"
      dimensions: "pool:luci.flex.ci"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$depot_tools/windows_sdk": {'
        '    "version": "uploaded:2024-01-11"'
        '  },'
        '  "$gatekeeper": {'
        '    "group": "client.crashpad"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "win"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
  }
}
buckets {
  name: "try"
  acls {
    role: WRITER
    group: "project-crashpad-admins"
  }
  acls {
    role: WRITER
    group: "service-account-crashpad-cq"
  }
  acls {
    group: "all"
  }
  acls {
    role: SCHEDULER
    group: "project-crashpad-tryjob-access"
  }
  acls {
    role: SCHEDULER
    group: "service-account-cq"
  }
  swarming {
    builders {
      name: "crashpad_fuchsia_arm64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_fuchsia_arm64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_fuchsia_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_fuchsia_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "fuchsia"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_arm64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_arm64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_cpu": "arm64",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_ios_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "ios"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_ios"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_linux_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "linux"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_linux_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Ubuntu-22.04"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "linux"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_mac_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "mac"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_mac"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_mac_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cpu:x86-64"
      dimensions: "os:Mac-13|Mac-14"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "mac"'
        '}'
      execution_timeout_secs: 10800
      caches {
        name: "osx_sdk_mac"
        path: "osx_sdk"
      }
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_win_x64_dbg"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Windows-10"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$depot_tools/windows_sdk": {'
        '    "version": "uploaded:2024-01-11"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Debug",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "win"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
    builders {
      name: "crashpad_win_x64_rel"
      swarming_host: "chromium-swarm.appspot.com"
      dimensions: "cores:8"
      dimensions: "cpu:x86-64"
      dimensions: "os:Windows-10"
      dimensions: "pool:luci.flex.try"
      exe {
        cipd_package: "infra/recipe_bundles/chromium.googlesource.com/chromium/tools/build"
        cipd_version: "refs/heads/main"
        cmd: "luciexe"
      }
      properties:
        '{'
        '  "$depot_tools/windows_sdk": {'
        '    "version": "uploaded:2024-01-11"'
        '  },'
        '  "$kitchen": {'
        '    "devshell": true,'
        '    "git_auth": true'
        '  },'
        '  "config": "Release",'
        '  "recipe": "crashpad/build",'
        '  "target_os": "win"'
        '}'
      execution_timeout_secs: 10800
      build_numbers: YES
      service_account: "<EMAIL>"
      experiments {
        key: "luci.recipes.use_python3"
        value: 100
      }
    }
  }
}
