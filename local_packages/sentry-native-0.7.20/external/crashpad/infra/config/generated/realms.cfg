# Auto-generated by lucicfg.
# Do not modify manually.
#
# For the schema of this file, see RealmsCfg message:
#   https://config.luci.app/schemas/projects:realms.cfg

realms {
  name: "@root"
  bindings {
    role: "role/buildbucket.reader"
    principals: "group:all"
  }
  bindings {
    role: "role/configs.reader"
    principals: "group:all"
  }
  bindings {
    role: "role/logdog.reader"
    principals: "group:all"
  }
  bindings {
    role: "role/logdog.writer"
    principals: "group:luci-logdog-chromium-writers"
  }
  bindings {
    role: "role/scheduler.owner"
    principals: "group:project-crashpad-admins"
  }
  bindings {
    role: "role/scheduler.reader"
    principals: "group:all"
  }
}
realms {
  name: "ci"
  bindings {
    role: "role/buildbucket.builderServiceAccount"
    principals: "user:<EMAIL>"
  }
  bindings {
    role: "role/buildbucket.owner"
    principals: "group:project-crashpad-admins"
  }
  bindings {
    role: "role/buildbucket.triggerer"
    principals: "user:<EMAIL>"
  }
}
realms {
  name: "try"
  bindings {
    role: "role/buildbucket.builderServiceAccount"
    principals: "user:<EMAIL>"
  }
  bindings {
    role: "role/buildbucket.owner"
    principals: "group:project-crashpad-admins"
    principals: "group:service-account-crashpad-cq"
  }
  bindings {
    role: "role/buildbucket.triggerer"
    principals: "group:project-crashpad-tryjob-access"
    principals: "group:service-account-cq"
  }
}
