# Auto-generated by lucicfg.
# Do not modify manually.
#
# For the schema of this file, see Project message:
#   https://config.luci.app/schemas/projects:luci-milo.cfg

consoles {
  id: "main"
  name: "Crashpad Main Console"
  repo_url: "https://chromium.googlesource.com/crashpad/crashpad"
  refs: "regexp:refs/heads/main"
  manifest_name: "REVISION"
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_fuchsia_arm64_dbg"
    category: "fuchsia|arm64"
    short_name: "dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_fuchsia_arm64_rel"
    category: "fuchsia|arm64"
    short_name: "rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_fuchsia_x64_dbg"
    category: "fuchsia|x64"
    short_name: "dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_fuchsia_x64_rel"
    category: "fuchsia|x64"
    short_name: "rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_ios_arm64_dbg"
    category: "ios|arm64"
    short_name: "dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_ios_arm64_rel"
    category: "ios|arm64"
    short_name: "rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_ios_x64_dbg"
    category: "ios|x64"
    short_name: "dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_ios_x64_rel"
    category: "ios|x64"
    short_name: "rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_linux_x64_dbg"
    category: "linux|x64"
    short_name: "dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_linux_x64_rel"
    category: "linux|x64"
    short_name: "rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_mac_x64_dbg"
    category: "mac|x64"
    short_name: "dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_mac_x64_rel"
    category: "mac|x64"
    short_name: "rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_win_x64_dbg"
    category: "win|x64"
    short_name: "dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.ci/crashpad_win_x64_rel"
    category: "win|x64"
    short_name: "rel"
  }
}
consoles {
  id: "try"
  name: "Crashpad Try Builders"
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_fuchsia_arm64_dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_fuchsia_arm64_rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_fuchsia_x64_dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_fuchsia_x64_rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_ios_arm64_dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_ios_arm64_rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_ios_x64_dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_ios_x64_rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_linux_x64_dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_linux_x64_rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_mac_x64_dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_mac_x64_rel"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_win_x64_dbg"
  }
  builders {
    name: "buildbucket/luci.crashpad.try/crashpad_win_x64_rel"
  }
  builder_view_only: true
}
logo_url: "https://storage.googleapis.com/chrome-infra-public/logo/crashpad-logo.svg"
