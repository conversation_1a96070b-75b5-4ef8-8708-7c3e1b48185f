# Auto-generated by lucicfg.
# Do not modify manually.
#
# For the schema of this file, see ProjectConfig message:
#   https://config.luci.app/schemas/projects:luci-scheduler.cfg

job {
  id: "crashpad_fuchsia_arm64_dbg"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_fuchsia_arm64_dbg"
  }
}
job {
  id: "crashpad_fuchsia_arm64_rel"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_fuchsia_arm64_rel"
  }
}
job {
  id: "crashpad_fuchsia_x64_dbg"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_fuchsia_x64_dbg"
  }
}
job {
  id: "crashpad_fuchsia_x64_rel"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_fuchsia_x64_rel"
  }
}
job {
  id: "crashpad_ios_arm64_dbg"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_ios_arm64_dbg"
  }
}
job {
  id: "crashpad_ios_arm64_rel"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_ios_arm64_rel"
  }
}
job {
  id: "crashpad_ios_x64_dbg"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_ios_x64_dbg"
  }
}
job {
  id: "crashpad_ios_x64_rel"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_ios_x64_rel"
  }
}
job {
  id: "crashpad_linux_x64_dbg"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_linux_x64_dbg"
  }
}
job {
  id: "crashpad_linux_x64_rel"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_linux_x64_rel"
  }
}
job {
  id: "crashpad_mac_x64_dbg"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_mac_x64_dbg"
  }
}
job {
  id: "crashpad_mac_x64_rel"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_mac_x64_rel"
  }
}
job {
  id: "crashpad_win_x64_dbg"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_win_x64_dbg"
  }
}
job {
  id: "crashpad_win_x64_rel"
  realm: "ci"
  acl_sets: "ci"
  buildbucket {
    server: "cr-buildbucket.appspot.com"
    bucket: "ci"
    builder: "crashpad_win_x64_rel"
  }
}
trigger {
  id: "master-gitiles-trigger"
  realm: "ci"
  acl_sets: "ci"
  triggers: "crashpad_fuchsia_arm64_dbg"
  triggers: "crashpad_fuchsia_arm64_rel"
  triggers: "crashpad_fuchsia_x64_dbg"
  triggers: "crashpad_fuchsia_x64_rel"
  triggers: "crashpad_ios_arm64_dbg"
  triggers: "crashpad_ios_arm64_rel"
  triggers: "crashpad_ios_x64_dbg"
  triggers: "crashpad_ios_x64_rel"
  triggers: "crashpad_linux_x64_dbg"
  triggers: "crashpad_linux_x64_rel"
  triggers: "crashpad_mac_x64_dbg"
  triggers: "crashpad_mac_x64_rel"
  triggers: "crashpad_win_x64_dbg"
  triggers: "crashpad_win_x64_rel"
  gitiles {
    repo: "https://chromium.googlesource.com/crashpad/crashpad"
    refs: "regexp:refs/heads/main"
  }
}
acl_sets {
  name: "ci"
  acls {
    role: OWNER
    granted_to: "group:project-crashpad-admins"
  }
  acls {
    granted_to: "group:all"
  }
}
