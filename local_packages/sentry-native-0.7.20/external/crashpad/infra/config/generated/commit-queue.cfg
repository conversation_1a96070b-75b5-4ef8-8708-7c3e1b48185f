# Auto-generated by lucicfg.
# Do not modify manually.
#
# For the schema of this file, see Config message:
#   https://config.luci.app/schemas/projects:commit-queue.cfg

cq_status_host: "chromium-cq-status.appspot.com"
submit_options {
  max_burst: 4
  burst_delay {
    seconds: 480
  }
}
config_groups {
  name: "crashpad"
  gerrit {
    url: "https://chromium-review.googlesource.com"
    projects {
      name: "crashpad/crashpad"
      ref_regexp: "refs/heads/.+"
    }
  }
  verifiers {
    gerrit_cq_ability {
      committer_list: "project-crashpad-tryjob-access"
      dry_run_access_list: "project-crashpad-tryjob-access"
    }
    tryjob {
      builders {
        name: "crashpad/try/crashpad_fuchsia_arm64_dbg"
      }
      builders {
        name: "crashpad/try/crashpad_fuchsia_arm64_rel"
      }
      builders {
        name: "crashpad/try/crashpad_fuchsia_x64_dbg"
      }
      builders {
        name: "crashpad/try/crashpad_fuchsia_x64_rel"
      }
      builders {
        name: "crashpad/try/crashpad_ios_arm64_dbg"
      }
      builders {
        name: "crashpad/try/crashpad_ios_arm64_rel"
      }
      builders {
        name: "crashpad/try/crashpad_ios_x64_dbg"
      }
      builders {
        name: "crashpad/try/crashpad_ios_x64_rel"
      }
      builders {
        name: "crashpad/try/crashpad_linux_x64_dbg"
      }
      builders {
        name: "crashpad/try/crashpad_linux_x64_rel"
      }
      builders {
        name: "crashpad/try/crashpad_mac_x64_dbg"
      }
      builders {
        name: "crashpad/try/crashpad_mac_x64_rel"
      }
      builders {
        name: "crashpad/try/crashpad_win_x64_dbg"
      }
      builders {
        name: "crashpad/try/crashpad_win_x64_rel"
      }
      retry_config {
        single_quota: 1
        global_quota: 2
        failure_weight: 1
        transient_failure_weight: 1
        timeout_weight: 2
      }
    }
  }
}
