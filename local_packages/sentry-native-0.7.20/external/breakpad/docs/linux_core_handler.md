# How To Use Breakpad As a Coredump Handler on Linux

This document presents a way to use Breakpad in order to generate
minidumps system wide on Linux.

Please refer to [Linux starter guide](./linux_starter_guide.md) if
instead you want to integrate breakpad into your application.

## Motivation

When working on an embedded system, disk and memory space is often
limited and when a process crashes it must be restarted as soon as
possible. Sometime saving a full coredump takes to much time or
consumes too much space.

## Breakpad Core Handler

In such case the program `core_handler` can be use to generate
minidumps instead of coredumps. `core_handler` reads the firsts
sections of the coredump (where the various threads are described)
generated by Linux from the standard input and then directly reads
`/proc/<pid>/mem` to reconstruct the stacktraces.

One can test it with:

```
# echo "|/usr/libexec/core_handler %P /var/lib/minidump/%e-%i.md" >
                /proc/sys/kernel/core_pattern
# echo 1 > /proc/sys/kernel/core_pipe_limit
```

Be aware that a real world integration would likely require further
customization and so `core_handler` can be wrapped into a script (for
example to change the permission of the minidump file or to signal the
presence of the minidump to another service).

Please refer to
[core(5)](https://man7.org/linux/man-pages/man5/core.5.html) for more
details.
