Google Breakpad for Android
===========================

This document explains how to use the Google Breakpad client library
on Android, and later generate valid stack traces from the minidumps
it generates.

This release supports ARM, x86 and MIPS based Android systems.
This release requires NDK release r11c or higher.

I. Building the client library:
===============================

The Android client is built as a static library that you can
link into your own Android native code. There are two ways to
build it:

I.1. Building with ndk-build:
-----------------------------

If you're using the ndk-build build system, you can follow
these simple steps:

  1/ Include android/google_breakpad/Android.mk from your own
     project's Android.mk

     This can be done either directly, or using ndk-build's
     import-module feature.

  2/ Link the library to one of your modules by using:

     LOCAL_STATIC_LIBRARIES += breakpad_client

NOTE: The client library requires a C++ STL implementation,
      which you can select with APP_STL in your Application.mk

      It has been tested succesfully with both STLport and GNU libstdc++


I.2. Building with a standalone Android toolchain:
--------------------------------------------------

All you need to do is configure your build with the right 'host'
value, and disable the processor and tools, as in:

  $GOOGLE_BREAKPAD_PATH/configure --host=arm-linux-androideabi \
                                  --disable-processor \
                                  --disable-tools
  make -j4

The library will be under src/client/linux/libbreakpad_client.a

You can also use 'make check' to run the test suite on a connected
Android device. This requires the Android 'adb' tool to be in your
path.

II. Using the client library in Android:
========================================

The usage instructions are very similar to the Linux ones that are
found at https://chromium.googlesource.com/breakpad/breakpad/+/master/docs/linux_starter_guide.md

1/ You need to include "client/linux/handler/exception_handler.h" from a C++
   source file.

2/ If you're not using ndk-build, you also need to:

   - add the following to your compiler include search paths:
       $GOOGLE_BREAKPAD_PATH/src
       $GOOGLE_BREAKPAD_PATH/src/common/android/include

   - add -llog to your linker flags

   Note that ndk-build does that for your automatically.

3/ Keep in mind that there is no /tmp directory on Android.

   If you use the library from a regular Android applications, specify a
   path under your app-specific storage directory. An alternative is to
   store them on the SDCard, but this requires a specific permission.

For a concrete example, see the sample test application under
android/sample_app. See its README for more information.


III. Getting a stack trace on the host:
=======================================

This process is similar to other platforms, but here's a quick example:

1/ Retrieve the minidumps on your development machine.

2/ Dump the symbols for your native libraries with the 'dump_syms' tool.
   This first requires building the host version of Google Breakpad, then
   calling:

      dump_syms $PROJECT_PATH/obj/local/$ABI/libfoo.so > libfoo.so.sym

3/ Create the symbol directory hierarchy.

   The first line of the generated libfoo.so.sym will have a "MODULE"
   entry that carries a hexadecimal version number, e.g.:

     MODULE Linux arm D51B4A5504974FA6ECC1869CAEE3603B0 test_google_breakpad

   Note: The second field could be either 'Linux' or 'Android'.

   Extract the version number, and a 'symbol' directory, for example:

      $PROJECT_PATH/symbols/libfoo.so/$VERSION/

   Copy/Move your libfoo.sym file there.

4/ Invoke minidump_stackwalk to create the stack trace:

     minidump_stackwalk $MINIDUMP_FILE $PROJECT_PATH/symbols

Note that various helper scripts can be found on the web to automate these
steps.

IV. Verifying the Android build library:
========================================

If you modify Google Breakpad and want to check that it still works correctly
on Android, please run the android/run-checks.sh script which will do all
necessary verifications for you. This includes:

  - Rebuilding the full host binaries.
  - Rebuilding the full Android binaries with configure/make.
  - Rebuilding the client library unit tests, and running them on a device.
  - Rebuilding the client library with ndk-build.
  - Building, installing and running a test crasher program on a device.
  - Extracting the corresponding minidump, dumping the test program symbols
    and generating a stack trace.
  - Checking the generated stack trace for valid source locations.

For more details, please run:

  android/run-checks.sh --help-all
