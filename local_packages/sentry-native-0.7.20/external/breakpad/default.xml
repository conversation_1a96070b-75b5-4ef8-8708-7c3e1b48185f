<?xml version='1.0' encoding='UTF-8'?>
<!-- AUTOGENERATED BY deps-to-manifest.py; DO NOT EDIT -->
<manifest>

  <default revision='refs/heads/main'
           remote='chromium'
           sync-c='true'
           sync-j='8' />

  <remote  name='github'
           fetch='https://github.com/'
           review='' />

  <remote  name='chromium'
           fetch='https://chromium.googlesource.com/'
           review='https://chromium-review.googlesource.com' />

  <project path='src'
           name='breakpad/breakpad'
           revision='refs/heads/main'
           remote='chromium' />

  <project path='src/src/testing'
           name='google/googletest.git'
           revision='refs/tags/release-1.11.0'
           remote='github' />

  <project path='src/src/third_party/lss'
           name='linux-syscall-support/'
           revision='9719c1e1e676814c456b55f5f070eabad6709d31'
           remote='chromium' />

  <project path='src/src/third_party/protobuf/protobuf'
           name='google/protobuf.git'
           revision='cb6dd4ef5f82e41e06179dcd57d3b1d9246ad6ac'
           remote='github' />

</manifest>
