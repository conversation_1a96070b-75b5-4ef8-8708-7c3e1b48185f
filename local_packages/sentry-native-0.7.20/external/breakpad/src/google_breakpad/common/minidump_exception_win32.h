/* Copyright 2006 Google LLC
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *
 *     * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above
 * copyright notice, this list of conditions and the following disclaimer
 * in the documentation and/or other materials provided with the
 * distribution.
 *     * Neither the name of Google LLC nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF <PERSON><PERSON><PERSON>NTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON><PERSON>NTIA<PERSON> DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. */

/* minidump_exception_win32.h: Definitions of exception codes for
 * Win32 platform
 *
 * (This is C99 source, please don't corrupt it with C++.)
 *
 * Author: Mark Mentovai
 * Split into its own file: Neal Sidhwaney */


#ifndef GOOGLE_BREAKPAD_COMMON_MINIDUMP_EXCEPTION_WIN32_H__
#define GOOGLE_BREAKPAD_COMMON_MINIDUMP_EXCEPTION_WIN32_H__

#include <stddef.h>

#include "google_breakpad/common/breakpad_types.h"


/* For (MDException).exception_code.  These values come from WinBase.h
 * and WinNT.h (names beginning with EXCEPTION_ are in WinBase.h,
 * they are STATUS_ in WinNT.h). */
typedef enum {
  MD_EXCEPTION_CODE_WIN_CONTROL_C                = 0x40010005,
      /* DBG_CONTROL_C */
  MD_EXCEPTION_CODE_WIN_GUARD_PAGE_VIOLATION     = 0x80000001,
      /* EXCEPTION_GUARD_PAGE */
  MD_EXCEPTION_CODE_WIN_DATATYPE_MISALIGNMENT    = 0x80000002,
      /* EXCEPTION_DATATYPE_MISALIGNMENT */
  MD_EXCEPTION_CODE_WIN_BREAKPOINT               = 0x80000003,
      /* EXCEPTION_BREAKPOINT */
  MD_EXCEPTION_CODE_WIN_SINGLE_STEP              = 0x80000004,
      /* EXCEPTION_SINGLE_STEP */
  MD_EXCEPTION_CODE_WIN_ACCESS_VIOLATION         = 0xc0000005,
      /* EXCEPTION_ACCESS_VIOLATION */
  MD_EXCEPTION_CODE_WIN_IN_PAGE_ERROR            = 0xc0000006,
      /* EXCEPTION_IN_PAGE_ERROR */
  MD_EXCEPTION_CODE_WIN_INVALID_HANDLE           = 0xc0000008,
      /* EXCEPTION_INVALID_HANDLE */
  MD_EXCEPTION_CODE_WIN_ILLEGAL_INSTRUCTION      = 0xc000001d,
      /* EXCEPTION_ILLEGAL_INSTRUCTION */
  MD_EXCEPTION_CODE_WIN_NONCONTINUABLE_EXCEPTION = 0xc0000025,
      /* EXCEPTION_NONCONTINUABLE_EXCEPTION */
  MD_EXCEPTION_CODE_WIN_INVALID_DISPOSITION      = 0xc0000026,
      /* EXCEPTION_INVALID_DISPOSITION */
  MD_EXCEPTION_CODE_WIN_ARRAY_BOUNDS_EXCEEDED    = 0xc000008c,
      /* EXCEPTION_BOUNDS_EXCEEDED */
  MD_EXCEPTION_CODE_WIN_FLOAT_DENORMAL_OPERAND   = 0xc000008d,
      /* EXCEPTION_FLT_DENORMAL_OPERAND */
  MD_EXCEPTION_CODE_WIN_FLOAT_DIVIDE_BY_ZERO     = 0xc000008e,
      /* EXCEPTION_FLT_DIVIDE_BY_ZERO */
  MD_EXCEPTION_CODE_WIN_FLOAT_INEXACT_RESULT     = 0xc000008f,
      /* EXCEPTION_FLT_INEXACT_RESULT */
  MD_EXCEPTION_CODE_WIN_FLOAT_INVALID_OPERATION  = 0xc0000090,
      /* EXCEPTION_FLT_INVALID_OPERATION */
  MD_EXCEPTION_CODE_WIN_FLOAT_OVERFLOW           = 0xc0000091,
      /* EXCEPTION_FLT_OVERFLOW */
  MD_EXCEPTION_CODE_WIN_FLOAT_STACK_CHECK        = 0xc0000092,
      /* EXCEPTION_FLT_STACK_CHECK */
  MD_EXCEPTION_CODE_WIN_FLOAT_UNDERFLOW          = 0xc0000093,
      /* EXCEPTION_FLT_UNDERFLOW */
  MD_EXCEPTION_CODE_WIN_INTEGER_DIVIDE_BY_ZERO   = 0xc0000094,
      /* EXCEPTION_INT_DIVIDE_BY_ZERO */
  MD_EXCEPTION_CODE_WIN_INTEGER_OVERFLOW         = 0xc0000095,
      /* EXCEPTION_INT_OVERFLOW */
  MD_EXCEPTION_CODE_WIN_PRIVILEGED_INSTRUCTION   = 0xc0000096,
      /* EXCEPTION_PRIV_INSTRUCTION */
  MD_EXCEPTION_CODE_WIN_STACK_OVERFLOW           = 0xc00000fd,
      /* EXCEPTION_STACK_OVERFLOW */
  MD_EXCEPTION_CODE_WIN_BAD_FUNCTION_TABLE       = 0xc00000ff,
      /* EXCEPTION_BAD_FUNCTION_TABLE */
  MD_EXCEPTION_CODE_WIN_POSSIBLE_DEADLOCK        = 0xc0000194,
      /* EXCEPTION_POSSIBLE_DEADLOCK */
  MD_EXCEPTION_CODE_WIN_STACK_BUFFER_OVERRUN     = 0xc0000409,
      /* STATUS_STACK_BUFFER_OVERRUN */
  MD_EXCEPTION_CODE_WIN_HEAP_CORRUPTION          = 0xc0000374,
      /* STATUS_HEAP_CORRUPTION */
  MD_EXCEPTION_OUT_OF_MEMORY                     = 0xe0000008,
      /* Exception thrown by Chromium allocators to indicate OOM.
	     See base/process/memory.h in Chromium for rationale. */
  MD_EXCEPTION_CODE_WIN_UNHANDLED_CPP_EXCEPTION  = 0xe06d7363,
      /* Per http://support.microsoft.com/kb/185294,
         generated by Visual C++ compiler */
  MD_EXCEPTION_CODE_WIN_SIMULATED                = 0x0517a7ed
      /* Fake exception code used by Crashpad's
         CrashpadClient::DumpWithoutCrash. */
} MDExceptionCodeWin;


/* For (MDException).exception_information[2], when (MDException).exception_code
 * is MD_EXCEPTION_CODE_WIN_IN_PAGE_ERROR. This describes the underlying reason
 * for the error. These values come from ntstatus.h.
 *
 * The content of this enum was created from ntstatus.h in the 8.1 SDK with
 *
 * egrep '#define [A-Z_0-9]+\s+\(\(NTSTATUS\)0xC[0-9A-F]+L\)' ntstatus.h
 * | tr -d '\r'
 * | sed -r 's@#define ([A-Z_0-9]+)\s+\(\(NTSTATUS\)(0xC[0-9A-F]+)L\).*@\2 \1@'
 * | sort
 * | sed -r 's@(0xC[0-9A-F]+) ([A-Z_0-9]+)@  MD_NTSTATUS_WIN_\2 = \1,@'
 *
 * With easy copy to clipboard with
 * | xclip -selection c  # on linux
 * | clip  # on windows
 * | pbcopy  # on mac
 *
 * and then the last comma manually removed. */
typedef enum {
  MD_NTSTATUS_WIN_STATUS_UNSUCCESSFUL = 0xC0000001,
  MD_NTSTATUS_WIN_STATUS_NOT_IMPLEMENTED = 0xC0000002,
  MD_NTSTATUS_WIN_STATUS_INVALID_INFO_CLASS = 0xC0000003,
  MD_NTSTATUS_WIN_STATUS_INFO_LENGTH_MISMATCH = 0xC0000004,
  MD_NTSTATUS_WIN_STATUS_ACCESS_VIOLATION = 0xC0000005,
  MD_NTSTATUS_WIN_STATUS_IN_PAGE_ERROR = 0xC0000006,
  MD_NTSTATUS_WIN_STATUS_PAGEFILE_QUOTA = 0xC0000007,
  MD_NTSTATUS_WIN_STATUS_INVALID_HANDLE = 0xC0000008,
  MD_NTSTATUS_WIN_STATUS_BAD_INITIAL_STACK = 0xC0000009,
  MD_NTSTATUS_WIN_STATUS_BAD_INITIAL_PC = 0xC000000A,
  MD_NTSTATUS_WIN_STATUS_INVALID_CID = 0xC000000B,
  MD_NTSTATUS_WIN_STATUS_TIMER_NOT_CANCELED = 0xC000000C,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER = 0xC000000D,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_DEVICE = 0xC000000E,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_FILE = 0xC000000F,
  MD_NTSTATUS_WIN_STATUS_INVALID_DEVICE_REQUEST = 0xC0000010,
  MD_NTSTATUS_WIN_STATUS_END_OF_FILE = 0xC0000011,
  MD_NTSTATUS_WIN_STATUS_WRONG_VOLUME = 0xC0000012,
  MD_NTSTATUS_WIN_STATUS_NO_MEDIA_IN_DEVICE = 0xC0000013,
  MD_NTSTATUS_WIN_STATUS_UNRECOGNIZED_MEDIA = 0xC0000014,
  MD_NTSTATUS_WIN_STATUS_NONEXISTENT_SECTOR = 0xC0000015,
  MD_NTSTATUS_WIN_STATUS_MORE_PROCESSING_REQUIRED = 0xC0000016,
  MD_NTSTATUS_WIN_STATUS_NO_MEMORY = 0xC0000017,
  MD_NTSTATUS_WIN_STATUS_CONFLICTING_ADDRESSES = 0xC0000018,
  MD_NTSTATUS_WIN_STATUS_NOT_MAPPED_VIEW = 0xC0000019,
  MD_NTSTATUS_WIN_STATUS_UNABLE_TO_FREE_VM = 0xC000001A,
  MD_NTSTATUS_WIN_STATUS_UNABLE_TO_DELETE_SECTION = 0xC000001B,
  MD_NTSTATUS_WIN_STATUS_INVALID_SYSTEM_SERVICE = 0xC000001C,
  MD_NTSTATUS_WIN_STATUS_ILLEGAL_INSTRUCTION = 0xC000001D,
  MD_NTSTATUS_WIN_STATUS_INVALID_LOCK_SEQUENCE = 0xC000001E,
  MD_NTSTATUS_WIN_STATUS_INVALID_VIEW_SIZE = 0xC000001F,
  MD_NTSTATUS_WIN_STATUS_INVALID_FILE_FOR_SECTION = 0xC0000020,
  MD_NTSTATUS_WIN_STATUS_ALREADY_COMMITTED = 0xC0000021,
  MD_NTSTATUS_WIN_STATUS_ACCESS_DENIED = 0xC0000022,
  MD_NTSTATUS_WIN_STATUS_BUFFER_TOO_SMALL = 0xC0000023,
  MD_NTSTATUS_WIN_STATUS_OBJECT_TYPE_MISMATCH = 0xC0000024,
  MD_NTSTATUS_WIN_STATUS_NONCONTINUABLE_EXCEPTION = 0xC0000025,
  MD_NTSTATUS_WIN_STATUS_INVALID_DISPOSITION = 0xC0000026,
  MD_NTSTATUS_WIN_STATUS_UNWIND = 0xC0000027,
  MD_NTSTATUS_WIN_STATUS_BAD_STACK = 0xC0000028,
  MD_NTSTATUS_WIN_STATUS_INVALID_UNWIND_TARGET = 0xC0000029,
  MD_NTSTATUS_WIN_STATUS_NOT_LOCKED = 0xC000002A,
  MD_NTSTATUS_WIN_STATUS_PARITY_ERROR = 0xC000002B,
  MD_NTSTATUS_WIN_STATUS_UNABLE_TO_DECOMMIT_VM = 0xC000002C,
  MD_NTSTATUS_WIN_STATUS_NOT_COMMITTED = 0xC000002D,
  MD_NTSTATUS_WIN_STATUS_INVALID_PORT_ATTRIBUTES = 0xC000002E,
  MD_NTSTATUS_WIN_STATUS_PORT_MESSAGE_TOO_LONG = 0xC000002F,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_MIX = 0xC0000030,
  MD_NTSTATUS_WIN_STATUS_INVALID_QUOTA_LOWER = 0xC0000031,
  MD_NTSTATUS_WIN_STATUS_DISK_CORRUPT_ERROR = 0xC0000032,
  MD_NTSTATUS_WIN_STATUS_OBJECT_NAME_INVALID = 0xC0000033,
  MD_NTSTATUS_WIN_STATUS_OBJECT_NAME_NOT_FOUND = 0xC0000034,
  MD_NTSTATUS_WIN_STATUS_OBJECT_NAME_COLLISION = 0xC0000035,
  MD_NTSTATUS_WIN_STATUS_PORT_DISCONNECTED = 0xC0000037,
  MD_NTSTATUS_WIN_STATUS_DEVICE_ALREADY_ATTACHED = 0xC0000038,
  MD_NTSTATUS_WIN_STATUS_OBJECT_PATH_INVALID = 0xC0000039,
  MD_NTSTATUS_WIN_STATUS_OBJECT_PATH_NOT_FOUND = 0xC000003A,
  MD_NTSTATUS_WIN_STATUS_OBJECT_PATH_SYNTAX_BAD = 0xC000003B,
  MD_NTSTATUS_WIN_STATUS_DATA_OVERRUN = 0xC000003C,
  MD_NTSTATUS_WIN_STATUS_DATA_LATE_ERROR = 0xC000003D,
  MD_NTSTATUS_WIN_STATUS_DATA_ERROR = 0xC000003E,
  MD_NTSTATUS_WIN_STATUS_CRC_ERROR = 0xC000003F,
  MD_NTSTATUS_WIN_STATUS_SECTION_TOO_BIG = 0xC0000040,
  MD_NTSTATUS_WIN_STATUS_PORT_CONNECTION_REFUSED = 0xC0000041,
  MD_NTSTATUS_WIN_STATUS_INVALID_PORT_HANDLE = 0xC0000042,
  MD_NTSTATUS_WIN_STATUS_SHARING_VIOLATION = 0xC0000043,
  MD_NTSTATUS_WIN_STATUS_QUOTA_EXCEEDED = 0xC0000044,
  MD_NTSTATUS_WIN_STATUS_INVALID_PAGE_PROTECTION = 0xC0000045,
  MD_NTSTATUS_WIN_STATUS_MUTANT_NOT_OWNED = 0xC0000046,
  MD_NTSTATUS_WIN_STATUS_SEMAPHORE_LIMIT_EXCEEDED = 0xC0000047,
  MD_NTSTATUS_WIN_STATUS_PORT_ALREADY_SET = 0xC0000048,
  MD_NTSTATUS_WIN_STATUS_SECTION_NOT_IMAGE = 0xC0000049,
  MD_NTSTATUS_WIN_STATUS_SUSPEND_COUNT_EXCEEDED = 0xC000004A,
  MD_NTSTATUS_WIN_STATUS_THREAD_IS_TERMINATING = 0xC000004B,
  MD_NTSTATUS_WIN_STATUS_BAD_WORKING_SET_LIMIT = 0xC000004C,
  MD_NTSTATUS_WIN_STATUS_INCOMPATIBLE_FILE_MAP = 0xC000004D,
  MD_NTSTATUS_WIN_STATUS_SECTION_PROTECTION = 0xC000004E,
  MD_NTSTATUS_WIN_STATUS_EAS_NOT_SUPPORTED = 0xC000004F,
  MD_NTSTATUS_WIN_STATUS_EA_TOO_LARGE = 0xC0000050,
  MD_NTSTATUS_WIN_STATUS_NONEXISTENT_EA_ENTRY = 0xC0000051,
  MD_NTSTATUS_WIN_STATUS_NO_EAS_ON_FILE = 0xC0000052,
  MD_NTSTATUS_WIN_STATUS_EA_CORRUPT_ERROR = 0xC0000053,
  MD_NTSTATUS_WIN_STATUS_FILE_LOCK_CONFLICT = 0xC0000054,
  MD_NTSTATUS_WIN_STATUS_LOCK_NOT_GRANTED = 0xC0000055,
  MD_NTSTATUS_WIN_STATUS_DELETE_PENDING = 0xC0000056,
  MD_NTSTATUS_WIN_STATUS_CTL_FILE_NOT_SUPPORTED = 0xC0000057,
  MD_NTSTATUS_WIN_STATUS_UNKNOWN_REVISION = 0xC0000058,
  MD_NTSTATUS_WIN_STATUS_REVISION_MISMATCH = 0xC0000059,
  MD_NTSTATUS_WIN_STATUS_INVALID_OWNER = 0xC000005A,
  MD_NTSTATUS_WIN_STATUS_INVALID_PRIMARY_GROUP = 0xC000005B,
  MD_NTSTATUS_WIN_STATUS_NO_IMPERSONATION_TOKEN = 0xC000005C,
  MD_NTSTATUS_WIN_STATUS_CANT_DISABLE_MANDATORY = 0xC000005D,
  MD_NTSTATUS_WIN_STATUS_NO_LOGON_SERVERS = 0xC000005E,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_LOGON_SESSION = 0xC000005F,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_PRIVILEGE = 0xC0000060,
  MD_NTSTATUS_WIN_STATUS_PRIVILEGE_NOT_HELD = 0xC0000061,
  MD_NTSTATUS_WIN_STATUS_INVALID_ACCOUNT_NAME = 0xC0000062,
  MD_NTSTATUS_WIN_STATUS_USER_EXISTS = 0xC0000063,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_USER = 0xC0000064,
  MD_NTSTATUS_WIN_STATUS_GROUP_EXISTS = 0xC0000065,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_GROUP = 0xC0000066,
  MD_NTSTATUS_WIN_STATUS_MEMBER_IN_GROUP = 0xC0000067,
  MD_NTSTATUS_WIN_STATUS_MEMBER_NOT_IN_GROUP = 0xC0000068,
  MD_NTSTATUS_WIN_STATUS_LAST_ADMIN = 0xC0000069,
  MD_NTSTATUS_WIN_STATUS_WRONG_PASSWORD = 0xC000006A,
  MD_NTSTATUS_WIN_STATUS_ILL_FORMED_PASSWORD = 0xC000006B,
  MD_NTSTATUS_WIN_STATUS_PASSWORD_RESTRICTION = 0xC000006C,
  MD_NTSTATUS_WIN_STATUS_LOGON_FAILURE = 0xC000006D,
  MD_NTSTATUS_WIN_STATUS_ACCOUNT_RESTRICTION = 0xC000006E,
  MD_NTSTATUS_WIN_STATUS_INVALID_LOGON_HOURS = 0xC000006F,
  MD_NTSTATUS_WIN_STATUS_INVALID_WORKSTATION = 0xC0000070,
  MD_NTSTATUS_WIN_STATUS_PASSWORD_EXPIRED = 0xC0000071,
  MD_NTSTATUS_WIN_STATUS_ACCOUNT_DISABLED = 0xC0000072,
  MD_NTSTATUS_WIN_STATUS_NONE_MAPPED = 0xC0000073,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_LUIDS_REQUESTED = 0xC0000074,
  MD_NTSTATUS_WIN_STATUS_LUIDS_EXHAUSTED = 0xC0000075,
  MD_NTSTATUS_WIN_STATUS_INVALID_SUB_AUTHORITY = 0xC0000076,
  MD_NTSTATUS_WIN_STATUS_INVALID_ACL = 0xC0000077,
  MD_NTSTATUS_WIN_STATUS_INVALID_SID = 0xC0000078,
  MD_NTSTATUS_WIN_STATUS_INVALID_SECURITY_DESCR = 0xC0000079,
  MD_NTSTATUS_WIN_STATUS_PROCEDURE_NOT_FOUND = 0xC000007A,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_FORMAT = 0xC000007B,
  MD_NTSTATUS_WIN_STATUS_NO_TOKEN = 0xC000007C,
  MD_NTSTATUS_WIN_STATUS_BAD_INHERITANCE_ACL = 0xC000007D,
  MD_NTSTATUS_WIN_STATUS_RANGE_NOT_LOCKED = 0xC000007E,
  MD_NTSTATUS_WIN_STATUS_DISK_FULL = 0xC000007F,
  MD_NTSTATUS_WIN_STATUS_SERVER_DISABLED = 0xC0000080,
  MD_NTSTATUS_WIN_STATUS_SERVER_NOT_DISABLED = 0xC0000081,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_GUIDS_REQUESTED = 0xC0000082,
  MD_NTSTATUS_WIN_STATUS_GUIDS_EXHAUSTED = 0xC0000083,
  MD_NTSTATUS_WIN_STATUS_INVALID_ID_AUTHORITY = 0xC0000084,
  MD_NTSTATUS_WIN_STATUS_AGENTS_EXHAUSTED = 0xC0000085,
  MD_NTSTATUS_WIN_STATUS_INVALID_VOLUME_LABEL = 0xC0000086,
  MD_NTSTATUS_WIN_STATUS_SECTION_NOT_EXTENDED = 0xC0000087,
  MD_NTSTATUS_WIN_STATUS_NOT_MAPPED_DATA = 0xC0000088,
  MD_NTSTATUS_WIN_STATUS_RESOURCE_DATA_NOT_FOUND = 0xC0000089,
  MD_NTSTATUS_WIN_STATUS_RESOURCE_TYPE_NOT_FOUND = 0xC000008A,
  MD_NTSTATUS_WIN_STATUS_RESOURCE_NAME_NOT_FOUND = 0xC000008B,
  MD_NTSTATUS_WIN_STATUS_ARRAY_BOUNDS_EXCEEDED = 0xC000008C,
  MD_NTSTATUS_WIN_STATUS_FLOAT_DENORMAL_OPERAND = 0xC000008D,
  MD_NTSTATUS_WIN_STATUS_FLOAT_DIVIDE_BY_ZERO = 0xC000008E,
  MD_NTSTATUS_WIN_STATUS_FLOAT_INEXACT_RESULT = 0xC000008F,
  MD_NTSTATUS_WIN_STATUS_FLOAT_INVALID_OPERATION = 0xC0000090,
  MD_NTSTATUS_WIN_STATUS_FLOAT_OVERFLOW = 0xC0000091,
  MD_NTSTATUS_WIN_STATUS_FLOAT_STACK_CHECK = 0xC0000092,
  MD_NTSTATUS_WIN_STATUS_FLOAT_UNDERFLOW = 0xC0000093,
  MD_NTSTATUS_WIN_STATUS_INTEGER_DIVIDE_BY_ZERO = 0xC0000094,
  MD_NTSTATUS_WIN_STATUS_INTEGER_OVERFLOW = 0xC0000095,
  MD_NTSTATUS_WIN_STATUS_PRIVILEGED_INSTRUCTION = 0xC0000096,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_PAGING_FILES = 0xC0000097,
  MD_NTSTATUS_WIN_STATUS_FILE_INVALID = 0xC0000098,
  MD_NTSTATUS_WIN_STATUS_ALLOTTED_SPACE_EXCEEDED = 0xC0000099,
  MD_NTSTATUS_WIN_STATUS_INSUFFICIENT_RESOURCES = 0xC000009A,
  MD_NTSTATUS_WIN_STATUS_DFS_EXIT_PATH_FOUND = 0xC000009B,
  MD_NTSTATUS_WIN_STATUS_DEVICE_DATA_ERROR = 0xC000009C,
  MD_NTSTATUS_WIN_STATUS_DEVICE_NOT_CONNECTED = 0xC000009D,
  MD_NTSTATUS_WIN_STATUS_DEVICE_POWER_FAILURE = 0xC000009E,
  MD_NTSTATUS_WIN_STATUS_FREE_VM_NOT_AT_BASE = 0xC000009F,
  MD_NTSTATUS_WIN_STATUS_MEMORY_NOT_ALLOCATED = 0xC00000A0,
  MD_NTSTATUS_WIN_STATUS_WORKING_SET_QUOTA = 0xC00000A1,
  MD_NTSTATUS_WIN_STATUS_MEDIA_WRITE_PROTECTED = 0xC00000A2,
  MD_NTSTATUS_WIN_STATUS_DEVICE_NOT_READY = 0xC00000A3,
  MD_NTSTATUS_WIN_STATUS_INVALID_GROUP_ATTRIBUTES = 0xC00000A4,
  MD_NTSTATUS_WIN_STATUS_BAD_IMPERSONATION_LEVEL = 0xC00000A5,
  MD_NTSTATUS_WIN_STATUS_CANT_OPEN_ANONYMOUS = 0xC00000A6,
  MD_NTSTATUS_WIN_STATUS_BAD_VALIDATION_CLASS = 0xC00000A7,
  MD_NTSTATUS_WIN_STATUS_BAD_TOKEN_TYPE = 0xC00000A8,
  MD_NTSTATUS_WIN_STATUS_BAD_MASTER_BOOT_RECORD = 0xC00000A9,
  MD_NTSTATUS_WIN_STATUS_INSTRUCTION_MISALIGNMENT = 0xC00000AA,
  MD_NTSTATUS_WIN_STATUS_INSTANCE_NOT_AVAILABLE = 0xC00000AB,
  MD_NTSTATUS_WIN_STATUS_PIPE_NOT_AVAILABLE = 0xC00000AC,
  MD_NTSTATUS_WIN_STATUS_INVALID_PIPE_STATE = 0xC00000AD,
  MD_NTSTATUS_WIN_STATUS_PIPE_BUSY = 0xC00000AE,
  MD_NTSTATUS_WIN_STATUS_ILLEGAL_FUNCTION = 0xC00000AF,
  MD_NTSTATUS_WIN_STATUS_PIPE_DISCONNECTED = 0xC00000B0,
  MD_NTSTATUS_WIN_STATUS_PIPE_CLOSING = 0xC00000B1,
  MD_NTSTATUS_WIN_STATUS_PIPE_CONNECTED = 0xC00000B2,
  MD_NTSTATUS_WIN_STATUS_PIPE_LISTENING = 0xC00000B3,
  MD_NTSTATUS_WIN_STATUS_INVALID_READ_MODE = 0xC00000B4,
  MD_NTSTATUS_WIN_STATUS_IO_TIMEOUT = 0xC00000B5,
  MD_NTSTATUS_WIN_STATUS_FILE_FORCED_CLOSED = 0xC00000B6,
  MD_NTSTATUS_WIN_STATUS_PROFILING_NOT_STARTED = 0xC00000B7,
  MD_NTSTATUS_WIN_STATUS_PROFILING_NOT_STOPPED = 0xC00000B8,
  MD_NTSTATUS_WIN_STATUS_COULD_NOT_INTERPRET = 0xC00000B9,
  MD_NTSTATUS_WIN_STATUS_FILE_IS_A_DIRECTORY = 0xC00000BA,
  MD_NTSTATUS_WIN_STATUS_NOT_SUPPORTED = 0xC00000BB,
  MD_NTSTATUS_WIN_STATUS_REMOTE_NOT_LISTENING = 0xC00000BC,
  MD_NTSTATUS_WIN_STATUS_DUPLICATE_NAME = 0xC00000BD,
  MD_NTSTATUS_WIN_STATUS_BAD_NETWORK_PATH = 0xC00000BE,
  MD_NTSTATUS_WIN_STATUS_NETWORK_BUSY = 0xC00000BF,
  MD_NTSTATUS_WIN_STATUS_DEVICE_DOES_NOT_EXIST = 0xC00000C0,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_COMMANDS = 0xC00000C1,
  MD_NTSTATUS_WIN_STATUS_ADAPTER_HARDWARE_ERROR = 0xC00000C2,
  MD_NTSTATUS_WIN_STATUS_INVALID_NETWORK_RESPONSE = 0xC00000C3,
  MD_NTSTATUS_WIN_STATUS_UNEXPECTED_NETWORK_ERROR = 0xC00000C4,
  MD_NTSTATUS_WIN_STATUS_BAD_REMOTE_ADAPTER = 0xC00000C5,
  MD_NTSTATUS_WIN_STATUS_PRINT_QUEUE_FULL = 0xC00000C6,
  MD_NTSTATUS_WIN_STATUS_NO_SPOOL_SPACE = 0xC00000C7,
  MD_NTSTATUS_WIN_STATUS_PRINT_CANCELLED = 0xC00000C8,
  MD_NTSTATUS_WIN_STATUS_NETWORK_NAME_DELETED = 0xC00000C9,
  MD_NTSTATUS_WIN_STATUS_NETWORK_ACCESS_DENIED = 0xC00000CA,
  MD_NTSTATUS_WIN_STATUS_BAD_DEVICE_TYPE = 0xC00000CB,
  MD_NTSTATUS_WIN_STATUS_BAD_NETWORK_NAME = 0xC00000CC,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_NAMES = 0xC00000CD,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_SESSIONS = 0xC00000CE,
  MD_NTSTATUS_WIN_STATUS_SHARING_PAUSED = 0xC00000CF,
  MD_NTSTATUS_WIN_STATUS_REQUEST_NOT_ACCEPTED = 0xC00000D0,
  MD_NTSTATUS_WIN_STATUS_REDIRECTOR_PAUSED = 0xC00000D1,
  MD_NTSTATUS_WIN_STATUS_NET_WRITE_FAULT = 0xC00000D2,
  MD_NTSTATUS_WIN_STATUS_PROFILING_AT_LIMIT = 0xC00000D3,
  MD_NTSTATUS_WIN_STATUS_NOT_SAME_DEVICE = 0xC00000D4,
  MD_NTSTATUS_WIN_STATUS_FILE_RENAMED = 0xC00000D5,
  MD_NTSTATUS_WIN_STATUS_VIRTUAL_CIRCUIT_CLOSED = 0xC00000D6,
  MD_NTSTATUS_WIN_STATUS_NO_SECURITY_ON_OBJECT = 0xC00000D7,
  MD_NTSTATUS_WIN_STATUS_CANT_WAIT = 0xC00000D8,
  MD_NTSTATUS_WIN_STATUS_PIPE_EMPTY = 0xC00000D9,
  MD_NTSTATUS_WIN_STATUS_CANT_ACCESS_DOMAIN_INFO = 0xC00000DA,
  MD_NTSTATUS_WIN_STATUS_CANT_TERMINATE_SELF = 0xC00000DB,
  MD_NTSTATUS_WIN_STATUS_INVALID_SERVER_STATE = 0xC00000DC,
  MD_NTSTATUS_WIN_STATUS_INVALID_DOMAIN_STATE = 0xC00000DD,
  MD_NTSTATUS_WIN_STATUS_INVALID_DOMAIN_ROLE = 0xC00000DE,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_DOMAIN = 0xC00000DF,
  MD_NTSTATUS_WIN_STATUS_DOMAIN_EXISTS = 0xC00000E0,
  MD_NTSTATUS_WIN_STATUS_DOMAIN_LIMIT_EXCEEDED = 0xC00000E1,
  MD_NTSTATUS_WIN_STATUS_OPLOCK_NOT_GRANTED = 0xC00000E2,
  MD_NTSTATUS_WIN_STATUS_INVALID_OPLOCK_PROTOCOL = 0xC00000E3,
  MD_NTSTATUS_WIN_STATUS_INTERNAL_DB_CORRUPTION = 0xC00000E4,
  MD_NTSTATUS_WIN_STATUS_INTERNAL_ERROR = 0xC00000E5,
  MD_NTSTATUS_WIN_STATUS_GENERIC_NOT_MAPPED = 0xC00000E6,
  MD_NTSTATUS_WIN_STATUS_BAD_DESCRIPTOR_FORMAT = 0xC00000E7,
  MD_NTSTATUS_WIN_STATUS_INVALID_USER_BUFFER = 0xC00000E8,
  MD_NTSTATUS_WIN_STATUS_UNEXPECTED_IO_ERROR = 0xC00000E9,
  MD_NTSTATUS_WIN_STATUS_UNEXPECTED_MM_CREATE_ERR = 0xC00000EA,
  MD_NTSTATUS_WIN_STATUS_UNEXPECTED_MM_MAP_ERROR = 0xC00000EB,
  MD_NTSTATUS_WIN_STATUS_UNEXPECTED_MM_EXTEND_ERR = 0xC00000EC,
  MD_NTSTATUS_WIN_STATUS_NOT_LOGON_PROCESS = 0xC00000ED,
  MD_NTSTATUS_WIN_STATUS_LOGON_SESSION_EXISTS = 0xC00000EE,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_1 = 0xC00000EF,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_2 = 0xC00000F0,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_3 = 0xC00000F1,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_4 = 0xC00000F2,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_5 = 0xC00000F3,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_6 = 0xC00000F4,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_7 = 0xC00000F5,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_8 = 0xC00000F6,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_9 = 0xC00000F7,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_10 = 0xC00000F8,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_11 = 0xC00000F9,
  MD_NTSTATUS_WIN_STATUS_INVALID_PARAMETER_12 = 0xC00000FA,
  MD_NTSTATUS_WIN_STATUS_REDIRECTOR_NOT_STARTED = 0xC00000FB,
  MD_NTSTATUS_WIN_STATUS_REDIRECTOR_STARTED = 0xC00000FC,
  MD_NTSTATUS_WIN_STATUS_STACK_OVERFLOW = 0xC00000FD,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_PACKAGE = 0xC00000FE,
  MD_NTSTATUS_WIN_STATUS_BAD_FUNCTION_TABLE = 0xC00000FF,
  MD_NTSTATUS_WIN_STATUS_VARIABLE_NOT_FOUND = 0xC0000100,
  MD_NTSTATUS_WIN_STATUS_DIRECTORY_NOT_EMPTY = 0xC0000101,
  MD_NTSTATUS_WIN_STATUS_FILE_CORRUPT_ERROR = 0xC0000102,
  MD_NTSTATUS_WIN_STATUS_NOT_A_DIRECTORY = 0xC0000103,
  MD_NTSTATUS_WIN_STATUS_BAD_LOGON_SESSION_STATE = 0xC0000104,
  MD_NTSTATUS_WIN_STATUS_LOGON_SESSION_COLLISION = 0xC0000105,
  MD_NTSTATUS_WIN_STATUS_NAME_TOO_LONG = 0xC0000106,
  MD_NTSTATUS_WIN_STATUS_FILES_OPEN = 0xC0000107,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_IN_USE = 0xC0000108,
  MD_NTSTATUS_WIN_STATUS_MESSAGE_NOT_FOUND = 0xC0000109,
  MD_NTSTATUS_WIN_STATUS_PROCESS_IS_TERMINATING = 0xC000010A,
  MD_NTSTATUS_WIN_STATUS_INVALID_LOGON_TYPE = 0xC000010B,
  MD_NTSTATUS_WIN_STATUS_NO_GUID_TRANSLATION = 0xC000010C,
  MD_NTSTATUS_WIN_STATUS_CANNOT_IMPERSONATE = 0xC000010D,
  MD_NTSTATUS_WIN_STATUS_IMAGE_ALREADY_LOADED = 0xC000010E,
  MD_NTSTATUS_WIN_STATUS_ABIOS_NOT_PRESENT = 0xC000010F,
  MD_NTSTATUS_WIN_STATUS_ABIOS_LID_NOT_EXIST = 0xC0000110,
  MD_NTSTATUS_WIN_STATUS_ABIOS_LID_ALREADY_OWNED = 0xC0000111,
  MD_NTSTATUS_WIN_STATUS_ABIOS_NOT_LID_OWNER = 0xC0000112,
  MD_NTSTATUS_WIN_STATUS_ABIOS_INVALID_COMMAND = 0xC0000113,
  MD_NTSTATUS_WIN_STATUS_ABIOS_INVALID_LID = 0xC0000114,
  MD_NTSTATUS_WIN_STATUS_ABIOS_SELECTOR_NOT_AVAILABLE = 0xC0000115,
  MD_NTSTATUS_WIN_STATUS_ABIOS_INVALID_SELECTOR = 0xC0000116,
  MD_NTSTATUS_WIN_STATUS_NO_LDT = 0xC0000117,
  MD_NTSTATUS_WIN_STATUS_INVALID_LDT_SIZE = 0xC0000118,
  MD_NTSTATUS_WIN_STATUS_INVALID_LDT_OFFSET = 0xC0000119,
  MD_NTSTATUS_WIN_STATUS_INVALID_LDT_DESCRIPTOR = 0xC000011A,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_NE_FORMAT = 0xC000011B,
  MD_NTSTATUS_WIN_STATUS_RXACT_INVALID_STATE = 0xC000011C,
  MD_NTSTATUS_WIN_STATUS_RXACT_COMMIT_FAILURE = 0xC000011D,
  MD_NTSTATUS_WIN_STATUS_MAPPED_FILE_SIZE_ZERO = 0xC000011E,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_OPENED_FILES = 0xC000011F,
  MD_NTSTATUS_WIN_STATUS_CANCELLED = 0xC0000120,
  MD_NTSTATUS_WIN_STATUS_CANNOT_DELETE = 0xC0000121,
  MD_NTSTATUS_WIN_STATUS_INVALID_COMPUTER_NAME = 0xC0000122,
  MD_NTSTATUS_WIN_STATUS_FILE_DELETED = 0xC0000123,
  MD_NTSTATUS_WIN_STATUS_SPECIAL_ACCOUNT = 0xC0000124,
  MD_NTSTATUS_WIN_STATUS_SPECIAL_GROUP = 0xC0000125,
  MD_NTSTATUS_WIN_STATUS_SPECIAL_USER = 0xC0000126,
  MD_NTSTATUS_WIN_STATUS_MEMBERS_PRIMARY_GROUP = 0xC0000127,
  MD_NTSTATUS_WIN_STATUS_FILE_CLOSED = 0xC0000128,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_THREADS = 0xC0000129,
  MD_NTSTATUS_WIN_STATUS_THREAD_NOT_IN_PROCESS = 0xC000012A,
  MD_NTSTATUS_WIN_STATUS_TOKEN_ALREADY_IN_USE = 0xC000012B,
  MD_NTSTATUS_WIN_STATUS_PAGEFILE_QUOTA_EXCEEDED = 0xC000012C,
  MD_NTSTATUS_WIN_STATUS_COMMITMENT_LIMIT = 0xC000012D,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_LE_FORMAT = 0xC000012E,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_NOT_MZ = 0xC000012F,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_PROTECT = 0xC0000130,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_WIN_16 = 0xC0000131,
  MD_NTSTATUS_WIN_STATUS_LOGON_SERVER_CONFLICT = 0xC0000132,
  MD_NTSTATUS_WIN_STATUS_TIME_DIFFERENCE_AT_DC = 0xC0000133,
  MD_NTSTATUS_WIN_STATUS_SYNCHRONIZATION_REQUIRED = 0xC0000134,
  MD_NTSTATUS_WIN_STATUS_DLL_NOT_FOUND = 0xC0000135,
  MD_NTSTATUS_WIN_STATUS_OPEN_FAILED = 0xC0000136,
  MD_NTSTATUS_WIN_STATUS_IO_PRIVILEGE_FAILED = 0xC0000137,
  MD_NTSTATUS_WIN_STATUS_ORDINAL_NOT_FOUND = 0xC0000138,
  MD_NTSTATUS_WIN_STATUS_ENTRYPOINT_NOT_FOUND = 0xC0000139,
  MD_NTSTATUS_WIN_STATUS_CONTROL_C_EXIT = 0xC000013A,
  MD_NTSTATUS_WIN_STATUS_LOCAL_DISCONNECT = 0xC000013B,
  MD_NTSTATUS_WIN_STATUS_REMOTE_DISCONNECT = 0xC000013C,
  MD_NTSTATUS_WIN_STATUS_REMOTE_RESOURCES = 0xC000013D,
  MD_NTSTATUS_WIN_STATUS_LINK_FAILED = 0xC000013E,
  MD_NTSTATUS_WIN_STATUS_LINK_TIMEOUT = 0xC000013F,
  MD_NTSTATUS_WIN_STATUS_INVALID_CONNECTION = 0xC0000140,
  MD_NTSTATUS_WIN_STATUS_INVALID_ADDRESS = 0xC0000141,
  MD_NTSTATUS_WIN_STATUS_DLL_INIT_FAILED = 0xC0000142,
  MD_NTSTATUS_WIN_STATUS_MISSING_SYSTEMFILE = 0xC0000143,
  MD_NTSTATUS_WIN_STATUS_UNHANDLED_EXCEPTION = 0xC0000144,
  MD_NTSTATUS_WIN_STATUS_APP_INIT_FAILURE = 0xC0000145,
  MD_NTSTATUS_WIN_STATUS_PAGEFILE_CREATE_FAILED = 0xC0000146,
  MD_NTSTATUS_WIN_STATUS_NO_PAGEFILE = 0xC0000147,
  MD_NTSTATUS_WIN_STATUS_INVALID_LEVEL = 0xC0000148,
  MD_NTSTATUS_WIN_STATUS_WRONG_PASSWORD_CORE = 0xC0000149,
  MD_NTSTATUS_WIN_STATUS_ILLEGAL_FLOAT_CONTEXT = 0xC000014A,
  MD_NTSTATUS_WIN_STATUS_PIPE_BROKEN = 0xC000014B,
  MD_NTSTATUS_WIN_STATUS_REGISTRY_CORRUPT = 0xC000014C,
  MD_NTSTATUS_WIN_STATUS_REGISTRY_IO_FAILED = 0xC000014D,
  MD_NTSTATUS_WIN_STATUS_NO_EVENT_PAIR = 0xC000014E,
  MD_NTSTATUS_WIN_STATUS_UNRECOGNIZED_VOLUME = 0xC000014F,
  MD_NTSTATUS_WIN_STATUS_SERIAL_NO_DEVICE_INITED = 0xC0000150,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_ALIAS = 0xC0000151,
  MD_NTSTATUS_WIN_STATUS_MEMBER_NOT_IN_ALIAS = 0xC0000152,
  MD_NTSTATUS_WIN_STATUS_MEMBER_IN_ALIAS = 0xC0000153,
  MD_NTSTATUS_WIN_STATUS_ALIAS_EXISTS = 0xC0000154,
  MD_NTSTATUS_WIN_STATUS_LOGON_NOT_GRANTED = 0xC0000155,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_SECRETS = 0xC0000156,
  MD_NTSTATUS_WIN_STATUS_SECRET_TOO_LONG = 0xC0000157,
  MD_NTSTATUS_WIN_STATUS_INTERNAL_DB_ERROR = 0xC0000158,
  MD_NTSTATUS_WIN_STATUS_FULLSCREEN_MODE = 0xC0000159,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_CONTEXT_IDS = 0xC000015A,
  MD_NTSTATUS_WIN_STATUS_LOGON_TYPE_NOT_GRANTED = 0xC000015B,
  MD_NTSTATUS_WIN_STATUS_NOT_REGISTRY_FILE = 0xC000015C,
  MD_NTSTATUS_WIN_STATUS_NT_CROSS_ENCRYPTION_REQUIRED = 0xC000015D,
  MD_NTSTATUS_WIN_STATUS_DOMAIN_CTRLR_CONFIG_ERROR = 0xC000015E,
  MD_NTSTATUS_WIN_STATUS_FT_MISSING_MEMBER = 0xC000015F,
  MD_NTSTATUS_WIN_STATUS_ILL_FORMED_SERVICE_ENTRY = 0xC0000160,
  MD_NTSTATUS_WIN_STATUS_ILLEGAL_CHARACTER = 0xC0000161,
  MD_NTSTATUS_WIN_STATUS_UNMAPPABLE_CHARACTER = 0xC0000162,
  MD_NTSTATUS_WIN_STATUS_UNDEFINED_CHARACTER = 0xC0000163,
  MD_NTSTATUS_WIN_STATUS_FLOPPY_VOLUME = 0xC0000164,
  MD_NTSTATUS_WIN_STATUS_FLOPPY_ID_MARK_NOT_FOUND = 0xC0000165,
  MD_NTSTATUS_WIN_STATUS_FLOPPY_WRONG_CYLINDER = 0xC0000166,
  MD_NTSTATUS_WIN_STATUS_FLOPPY_UNKNOWN_ERROR = 0xC0000167,
  MD_NTSTATUS_WIN_STATUS_FLOPPY_BAD_REGISTERS = 0xC0000168,
  MD_NTSTATUS_WIN_STATUS_DISK_RECALIBRATE_FAILED = 0xC0000169,
  MD_NTSTATUS_WIN_STATUS_DISK_OPERATION_FAILED = 0xC000016A,
  MD_NTSTATUS_WIN_STATUS_DISK_RESET_FAILED = 0xC000016B,
  MD_NTSTATUS_WIN_STATUS_SHARED_IRQ_BUSY = 0xC000016C,
  MD_NTSTATUS_WIN_STATUS_FT_ORPHANING = 0xC000016D,
  MD_NTSTATUS_WIN_STATUS_BIOS_FAILED_TO_CONNECT_INTERRUPT = 0xC000016E,
  MD_NTSTATUS_WIN_STATUS_PARTITION_FAILURE = 0xC0000172,
  MD_NTSTATUS_WIN_STATUS_INVALID_BLOCK_LENGTH = 0xC0000173,
  MD_NTSTATUS_WIN_STATUS_DEVICE_NOT_PARTITIONED = 0xC0000174,
  MD_NTSTATUS_WIN_STATUS_UNABLE_TO_LOCK_MEDIA = 0xC0000175,
  MD_NTSTATUS_WIN_STATUS_UNABLE_TO_UNLOAD_MEDIA = 0xC0000176,
  MD_NTSTATUS_WIN_STATUS_EOM_OVERFLOW = 0xC0000177,
  MD_NTSTATUS_WIN_STATUS_NO_MEDIA = 0xC0000178,
  MD_NTSTATUS_WIN_STATUS_NO_SUCH_MEMBER = 0xC000017A,
  MD_NTSTATUS_WIN_STATUS_INVALID_MEMBER = 0xC000017B,
  MD_NTSTATUS_WIN_STATUS_KEY_DELETED = 0xC000017C,
  MD_NTSTATUS_WIN_STATUS_NO_LOG_SPACE = 0xC000017D,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_SIDS = 0xC000017E,
  MD_NTSTATUS_WIN_STATUS_LM_CROSS_ENCRYPTION_REQUIRED = 0xC000017F,
  MD_NTSTATUS_WIN_STATUS_KEY_HAS_CHILDREN = 0xC0000180,
  MD_NTSTATUS_WIN_STATUS_CHILD_MUST_BE_VOLATILE = 0xC0000181,
  MD_NTSTATUS_WIN_STATUS_DEVICE_CONFIGURATION_ERROR = 0xC0000182,
  MD_NTSTATUS_WIN_STATUS_DRIVER_INTERNAL_ERROR = 0xC0000183,
  MD_NTSTATUS_WIN_STATUS_INVALID_DEVICE_STATE = 0xC0000184,
  MD_NTSTATUS_WIN_STATUS_IO_DEVICE_ERROR = 0xC0000185,
  MD_NTSTATUS_WIN_STATUS_DEVICE_PROTOCOL_ERROR = 0xC0000186,
  MD_NTSTATUS_WIN_STATUS_BACKUP_CONTROLLER = 0xC0000187,
  MD_NTSTATUS_WIN_STATUS_LOG_FILE_FULL = 0xC0000188,
  MD_NTSTATUS_WIN_STATUS_TOO_LATE = 0xC0000189,
  MD_NTSTATUS_WIN_STATUS_NO_TRUST_LSA_SECRET = 0xC000018A,
  MD_NTSTATUS_WIN_STATUS_NO_TRUST_SAM_ACCOUNT = 0xC000018B,
  MD_NTSTATUS_WIN_STATUS_TRUSTED_DOMAIN_FAILURE = 0xC000018C,
  MD_NTSTATUS_WIN_STATUS_TRUSTED_RELATIONSHIP_FAILURE = 0xC000018D,
  MD_NTSTATUS_WIN_STATUS_EVENTLOG_FILE_CORRUPT = 0xC000018E,
  MD_NTSTATUS_WIN_STATUS_EVENTLOG_CANT_START = 0xC000018F,
  MD_NTSTATUS_WIN_STATUS_TRUST_FAILURE = 0xC0000190,
  MD_NTSTATUS_WIN_STATUS_MUTANT_LIMIT_EXCEEDED = 0xC0000191,
  MD_NTSTATUS_WIN_STATUS_NETLOGON_NOT_STARTED = 0xC0000192,
  MD_NTSTATUS_WIN_STATUS_ACCOUNT_EXPIRED = 0xC0000193,
  MD_NTSTATUS_WIN_STATUS_POSSIBLE_DEADLOCK = 0xC0000194,
  MD_NTSTATUS_WIN_STATUS_NETWORK_CREDENTIAL_CONFLICT = 0xC0000195,
  MD_NTSTATUS_WIN_STATUS_REMOTE_SESSION_LIMIT = 0xC0000196,
  MD_NTSTATUS_WIN_STATUS_EVENTLOG_FILE_CHANGED = 0xC0000197,
  MD_NTSTATUS_WIN_STATUS_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT = 0xC0000198,
  MD_NTSTATUS_WIN_STATUS_NOLOGON_WORKSTATION_TRUST_ACCOUNT = 0xC0000199,
  MD_NTSTATUS_WIN_STATUS_NOLOGON_SERVER_TRUST_ACCOUNT = 0xC000019A,
  MD_NTSTATUS_WIN_STATUS_DOMAIN_TRUST_INCONSISTENT = 0xC000019B,
  MD_NTSTATUS_WIN_STATUS_FS_DRIVER_REQUIRED = 0xC000019C,
  MD_NTSTATUS_WIN_STATUS_IMAGE_ALREADY_LOADED_AS_DLL = 0xC000019D,
  MD_NTSTATUS_WIN_STATUS_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING = 0xC000019E,
  MD_NTSTATUS_WIN_STATUS_SHORT_NAMES_NOT_ENABLED_ON_VOLUME = 0xC000019F,
  MD_NTSTATUS_WIN_STATUS_SECURITY_STREAM_IS_INCONSISTENT = 0xC00001A0,
  MD_NTSTATUS_WIN_STATUS_INVALID_LOCK_RANGE = 0xC00001A1,
  MD_NTSTATUS_WIN_STATUS_INVALID_ACE_CONDITION = 0xC00001A2,
  MD_NTSTATUS_WIN_STATUS_IMAGE_SUBSYSTEM_NOT_PRESENT = 0xC00001A3,
  MD_NTSTATUS_WIN_STATUS_NOTIFICATION_GUID_ALREADY_DEFINED = 0xC00001A4,
  MD_NTSTATUS_WIN_STATUS_INVALID_EXCEPTION_HANDLER = 0xC00001A5,
  MD_NTSTATUS_WIN_STATUS_DUPLICATE_PRIVILEGES = 0xC00001A6,
  MD_NTSTATUS_WIN_STATUS_NOT_ALLOWED_ON_SYSTEM_FILE = 0xC00001A7,
  MD_NTSTATUS_WIN_STATUS_REPAIR_NEEDED = 0xC00001A8,
  MD_NTSTATUS_WIN_STATUS_QUOTA_NOT_ENABLED = 0xC00001A9,
  MD_NTSTATUS_WIN_STATUS_NO_APPLICATION_PACKAGE = 0xC00001AA,
  MD_NTSTATUS_WIN_STATUS_NETWORK_OPEN_RESTRICTION = 0xC0000201,
  MD_NTSTATUS_WIN_STATUS_NO_USER_SESSION_KEY = 0xC0000202,
  MD_NTSTATUS_WIN_STATUS_USER_SESSION_DELETED = 0xC0000203,
  MD_NTSTATUS_WIN_STATUS_RESOURCE_LANG_NOT_FOUND = 0xC0000204,
  MD_NTSTATUS_WIN_STATUS_INSUFF_SERVER_RESOURCES = 0xC0000205,
  MD_NTSTATUS_WIN_STATUS_INVALID_BUFFER_SIZE = 0xC0000206,
  MD_NTSTATUS_WIN_STATUS_INVALID_ADDRESS_COMPONENT = 0xC0000207,
  MD_NTSTATUS_WIN_STATUS_INVALID_ADDRESS_WILDCARD = 0xC0000208,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_ADDRESSES = 0xC0000209,
  MD_NTSTATUS_WIN_STATUS_ADDRESS_ALREADY_EXISTS = 0xC000020A,
  MD_NTSTATUS_WIN_STATUS_ADDRESS_CLOSED = 0xC000020B,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_DISCONNECTED = 0xC000020C,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_RESET = 0xC000020D,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_NODES = 0xC000020E,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_ABORTED = 0xC000020F,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_TIMED_OUT = 0xC0000210,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NO_RELEASE = 0xC0000211,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NO_MATCH = 0xC0000212,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_RESPONDED = 0xC0000213,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_INVALID_ID = 0xC0000214,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_INVALID_TYPE = 0xC0000215,
  MD_NTSTATUS_WIN_STATUS_NOT_SERVER_SESSION = 0xC0000216,
  MD_NTSTATUS_WIN_STATUS_NOT_CLIENT_SESSION = 0xC0000217,
  MD_NTSTATUS_WIN_STATUS_CANNOT_LOAD_REGISTRY_FILE = 0xC0000218,
  MD_NTSTATUS_WIN_STATUS_DEBUG_ATTACH_FAILED = 0xC0000219,
  MD_NTSTATUS_WIN_STATUS_SYSTEM_PROCESS_TERMINATED = 0xC000021A,
  MD_NTSTATUS_WIN_STATUS_DATA_NOT_ACCEPTED = 0xC000021B,
  MD_NTSTATUS_WIN_STATUS_NO_BROWSER_SERVERS_FOUND = 0xC000021C,
  MD_NTSTATUS_WIN_STATUS_VDM_HARD_ERROR = 0xC000021D,
  MD_NTSTATUS_WIN_STATUS_DRIVER_CANCEL_TIMEOUT = 0xC000021E,
  MD_NTSTATUS_WIN_STATUS_REPLY_MESSAGE_MISMATCH = 0xC000021F,
  MD_NTSTATUS_WIN_STATUS_MAPPED_ALIGNMENT = 0xC0000220,
  MD_NTSTATUS_WIN_STATUS_IMAGE_CHECKSUM_MISMATCH = 0xC0000221,
  MD_NTSTATUS_WIN_STATUS_LOST_WRITEBEHIND_DATA = 0xC0000222,
  MD_NTSTATUS_WIN_STATUS_CLIENT_SERVER_PARAMETERS_INVALID = 0xC0000223,
  MD_NTSTATUS_WIN_STATUS_PASSWORD_MUST_CHANGE = 0xC0000224,
  MD_NTSTATUS_WIN_STATUS_NOT_FOUND = 0xC0000225,
  MD_NTSTATUS_WIN_STATUS_NOT_TINY_STREAM = 0xC0000226,
  MD_NTSTATUS_WIN_STATUS_RECOVERY_FAILURE = 0xC0000227,
  MD_NTSTATUS_WIN_STATUS_STACK_OVERFLOW_READ = 0xC0000228,
  MD_NTSTATUS_WIN_STATUS_FAIL_CHECK = 0xC0000229,
  MD_NTSTATUS_WIN_STATUS_DUPLICATE_OBJECTID = 0xC000022A,
  MD_NTSTATUS_WIN_STATUS_OBJECTID_EXISTS = 0xC000022B,
  MD_NTSTATUS_WIN_STATUS_CONVERT_TO_LARGE = 0xC000022C,
  MD_NTSTATUS_WIN_STATUS_RETRY = 0xC000022D,
  MD_NTSTATUS_WIN_STATUS_FOUND_OUT_OF_SCOPE = 0xC000022E,
  MD_NTSTATUS_WIN_STATUS_ALLOCATE_BUCKET = 0xC000022F,
  MD_NTSTATUS_WIN_STATUS_PROPSET_NOT_FOUND = 0xC0000230,
  MD_NTSTATUS_WIN_STATUS_MARSHALL_OVERFLOW = 0xC0000231,
  MD_NTSTATUS_WIN_STATUS_INVALID_VARIANT = 0xC0000232,
  MD_NTSTATUS_WIN_STATUS_DOMAIN_CONTROLLER_NOT_FOUND = 0xC0000233,
  MD_NTSTATUS_WIN_STATUS_ACCOUNT_LOCKED_OUT = 0xC0000234,
  MD_NTSTATUS_WIN_STATUS_HANDLE_NOT_CLOSABLE = 0xC0000235,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_REFUSED = 0xC0000236,
  MD_NTSTATUS_WIN_STATUS_GRACEFUL_DISCONNECT = 0xC0000237,
  MD_NTSTATUS_WIN_STATUS_ADDRESS_ALREADY_ASSOCIATED = 0xC0000238,
  MD_NTSTATUS_WIN_STATUS_ADDRESS_NOT_ASSOCIATED = 0xC0000239,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_INVALID = 0xC000023A,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_ACTIVE = 0xC000023B,
  MD_NTSTATUS_WIN_STATUS_NETWORK_UNREACHABLE = 0xC000023C,
  MD_NTSTATUS_WIN_STATUS_HOST_UNREACHABLE = 0xC000023D,
  MD_NTSTATUS_WIN_STATUS_PROTOCOL_UNREACHABLE = 0xC000023E,
  MD_NTSTATUS_WIN_STATUS_PORT_UNREACHABLE = 0xC000023F,
  MD_NTSTATUS_WIN_STATUS_REQUEST_ABORTED = 0xC0000240,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_ABORTED = 0xC0000241,
  MD_NTSTATUS_WIN_STATUS_BAD_COMPRESSION_BUFFER = 0xC0000242,
  MD_NTSTATUS_WIN_STATUS_USER_MAPPED_FILE = 0xC0000243,
  MD_NTSTATUS_WIN_STATUS_AUDIT_FAILED = 0xC0000244,
  MD_NTSTATUS_WIN_STATUS_TIMER_RESOLUTION_NOT_SET = 0xC0000245,
  MD_NTSTATUS_WIN_STATUS_CONNECTION_COUNT_LIMIT = 0xC0000246,
  MD_NTSTATUS_WIN_STATUS_LOGIN_TIME_RESTRICTION = 0xC0000247,
  MD_NTSTATUS_WIN_STATUS_LOGIN_WKSTA_RESTRICTION = 0xC0000248,
  MD_NTSTATUS_WIN_STATUS_IMAGE_MP_UP_MISMATCH = 0xC0000249,
  MD_NTSTATUS_WIN_STATUS_INSUFFICIENT_LOGON_INFO = 0xC0000250,
  MD_NTSTATUS_WIN_STATUS_BAD_DLL_ENTRYPOINT = 0xC0000251,
  MD_NTSTATUS_WIN_STATUS_BAD_SERVICE_ENTRYPOINT = 0xC0000252,
  MD_NTSTATUS_WIN_STATUS_LPC_REPLY_LOST = 0xC0000253,
  MD_NTSTATUS_WIN_STATUS_IP_ADDRESS_CONFLICT1 = 0xC0000254,
  MD_NTSTATUS_WIN_STATUS_IP_ADDRESS_CONFLICT2 = 0xC0000255,
  MD_NTSTATUS_WIN_STATUS_REGISTRY_QUOTA_LIMIT = 0xC0000256,
  MD_NTSTATUS_WIN_STATUS_PATH_NOT_COVERED = 0xC0000257,
  MD_NTSTATUS_WIN_STATUS_NO_CALLBACK_ACTIVE = 0xC0000258,
  MD_NTSTATUS_WIN_STATUS_LICENSE_QUOTA_EXCEEDED = 0xC0000259,
  MD_NTSTATUS_WIN_STATUS_PWD_TOO_SHORT = 0xC000025A,
  MD_NTSTATUS_WIN_STATUS_PWD_TOO_RECENT = 0xC000025B,
  MD_NTSTATUS_WIN_STATUS_PWD_HISTORY_CONFLICT = 0xC000025C,
  MD_NTSTATUS_WIN_STATUS_PLUGPLAY_NO_DEVICE = 0xC000025E,
  MD_NTSTATUS_WIN_STATUS_UNSUPPORTED_COMPRESSION = 0xC000025F,
  MD_NTSTATUS_WIN_STATUS_INVALID_HW_PROFILE = 0xC0000260,
  MD_NTSTATUS_WIN_STATUS_INVALID_PLUGPLAY_DEVICE_PATH = 0xC0000261,
  MD_NTSTATUS_WIN_STATUS_DRIVER_ORDINAL_NOT_FOUND = 0xC0000262,
  MD_NTSTATUS_WIN_STATUS_DRIVER_ENTRYPOINT_NOT_FOUND = 0xC0000263,
  MD_NTSTATUS_WIN_STATUS_RESOURCE_NOT_OWNED = 0xC0000264,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_LINKS = 0xC0000265,
  MD_NTSTATUS_WIN_STATUS_QUOTA_LIST_INCONSISTENT = 0xC0000266,
  MD_NTSTATUS_WIN_STATUS_FILE_IS_OFFLINE = 0xC0000267,
  MD_NTSTATUS_WIN_STATUS_EVALUATION_EXPIRATION = 0xC0000268,
  MD_NTSTATUS_WIN_STATUS_ILLEGAL_DLL_RELOCATION = 0xC0000269,
  MD_NTSTATUS_WIN_STATUS_LICENSE_VIOLATION = 0xC000026A,
  MD_NTSTATUS_WIN_STATUS_DLL_INIT_FAILED_LOGOFF = 0xC000026B,
  MD_NTSTATUS_WIN_STATUS_DRIVER_UNABLE_TO_LOAD = 0xC000026C,
  MD_NTSTATUS_WIN_STATUS_DFS_UNAVAILABLE = 0xC000026D,
  MD_NTSTATUS_WIN_STATUS_VOLUME_DISMOUNTED = 0xC000026E,
  MD_NTSTATUS_WIN_STATUS_WX86_INTERNAL_ERROR = 0xC000026F,
  MD_NTSTATUS_WIN_STATUS_WX86_FLOAT_STACK_CHECK = 0xC0000270,
  MD_NTSTATUS_WIN_STATUS_VALIDATE_CONTINUE = 0xC0000271,
  MD_NTSTATUS_WIN_STATUS_NO_MATCH = 0xC0000272,
  MD_NTSTATUS_WIN_STATUS_NO_MORE_MATCHES = 0xC0000273,
  MD_NTSTATUS_WIN_STATUS_NOT_A_REPARSE_POINT = 0xC0000275,
  MD_NTSTATUS_WIN_STATUS_IO_REPARSE_TAG_INVALID = 0xC0000276,
  MD_NTSTATUS_WIN_STATUS_IO_REPARSE_TAG_MISMATCH = 0xC0000277,
  MD_NTSTATUS_WIN_STATUS_IO_REPARSE_DATA_INVALID = 0xC0000278,
  MD_NTSTATUS_WIN_STATUS_IO_REPARSE_TAG_NOT_HANDLED = 0xC0000279,
  MD_NTSTATUS_WIN_STATUS_PWD_TOO_LONG = 0xC000027A,
  MD_NTSTATUS_WIN_STATUS_STOWED_EXCEPTION = 0xC000027B,
  MD_NTSTATUS_WIN_STATUS_REPARSE_POINT_NOT_RESOLVED = 0xC0000280,
  MD_NTSTATUS_WIN_STATUS_DIRECTORY_IS_A_REPARSE_POINT = 0xC0000281,
  MD_NTSTATUS_WIN_STATUS_RANGE_LIST_CONFLICT = 0xC0000282,
  MD_NTSTATUS_WIN_STATUS_SOURCE_ELEMENT_EMPTY = 0xC0000283,
  MD_NTSTATUS_WIN_STATUS_DESTINATION_ELEMENT_FULL = 0xC0000284,
  MD_NTSTATUS_WIN_STATUS_ILLEGAL_ELEMENT_ADDRESS = 0xC0000285,
  MD_NTSTATUS_WIN_STATUS_MAGAZINE_NOT_PRESENT = 0xC0000286,
  MD_NTSTATUS_WIN_STATUS_REINITIALIZATION_NEEDED = 0xC0000287,
  MD_NTSTATUS_WIN_STATUS_ENCRYPTION_FAILED = 0xC000028A,
  MD_NTSTATUS_WIN_STATUS_DECRYPTION_FAILED = 0xC000028B,
  MD_NTSTATUS_WIN_STATUS_RANGE_NOT_FOUND = 0xC000028C,
  MD_NTSTATUS_WIN_STATUS_NO_RECOVERY_POLICY = 0xC000028D,
  MD_NTSTATUS_WIN_STATUS_NO_EFS = 0xC000028E,
  MD_NTSTATUS_WIN_STATUS_WRONG_EFS = 0xC000028F,
  MD_NTSTATUS_WIN_STATUS_NO_USER_KEYS = 0xC0000290,
  MD_NTSTATUS_WIN_STATUS_FILE_NOT_ENCRYPTED = 0xC0000291,
  MD_NTSTATUS_WIN_STATUS_NOT_EXPORT_FORMAT = 0xC0000292,
  MD_NTSTATUS_WIN_STATUS_FILE_ENCRYPTED = 0xC0000293,
  MD_NTSTATUS_WIN_STATUS_WMI_GUID_NOT_FOUND = 0xC0000295,
  MD_NTSTATUS_WIN_STATUS_WMI_INSTANCE_NOT_FOUND = 0xC0000296,
  MD_NTSTATUS_WIN_STATUS_WMI_ITEMID_NOT_FOUND = 0xC0000297,
  MD_NTSTATUS_WIN_STATUS_WMI_TRY_AGAIN = 0xC0000298,
  MD_NTSTATUS_WIN_STATUS_SHARED_POLICY = 0xC0000299,
  MD_NTSTATUS_WIN_STATUS_POLICY_OBJECT_NOT_FOUND = 0xC000029A,
  MD_NTSTATUS_WIN_STATUS_POLICY_ONLY_IN_DS = 0xC000029B,
  MD_NTSTATUS_WIN_STATUS_VOLUME_NOT_UPGRADED = 0xC000029C,
  MD_NTSTATUS_WIN_STATUS_REMOTE_STORAGE_NOT_ACTIVE = 0xC000029D,
  MD_NTSTATUS_WIN_STATUS_REMOTE_STORAGE_MEDIA_ERROR = 0xC000029E,
  MD_NTSTATUS_WIN_STATUS_NO_TRACKING_SERVICE = 0xC000029F,
  MD_NTSTATUS_WIN_STATUS_SERVER_SID_MISMATCH = 0xC00002A0,
  MD_NTSTATUS_WIN_STATUS_DS_NO_ATTRIBUTE_OR_VALUE = 0xC00002A1,
  MD_NTSTATUS_WIN_STATUS_DS_INVALID_ATTRIBUTE_SYNTAX = 0xC00002A2,
  MD_NTSTATUS_WIN_STATUS_DS_ATTRIBUTE_TYPE_UNDEFINED = 0xC00002A3,
  MD_NTSTATUS_WIN_STATUS_DS_ATTRIBUTE_OR_VALUE_EXISTS = 0xC00002A4,
  MD_NTSTATUS_WIN_STATUS_DS_BUSY = 0xC00002A5,
  MD_NTSTATUS_WIN_STATUS_DS_UNAVAILABLE = 0xC00002A6,
  MD_NTSTATUS_WIN_STATUS_DS_NO_RIDS_ALLOCATED = 0xC00002A7,
  MD_NTSTATUS_WIN_STATUS_DS_NO_MORE_RIDS = 0xC00002A8,
  MD_NTSTATUS_WIN_STATUS_DS_INCORRECT_ROLE_OWNER = 0xC00002A9,
  MD_NTSTATUS_WIN_STATUS_DS_RIDMGR_INIT_ERROR = 0xC00002AA,
  MD_NTSTATUS_WIN_STATUS_DS_OBJ_CLASS_VIOLATION = 0xC00002AB,
  MD_NTSTATUS_WIN_STATUS_DS_CANT_ON_NON_LEAF = 0xC00002AC,
  MD_NTSTATUS_WIN_STATUS_DS_CANT_ON_RDN = 0xC00002AD,
  MD_NTSTATUS_WIN_STATUS_DS_CANT_MOD_OBJ_CLASS = 0xC00002AE,
  MD_NTSTATUS_WIN_STATUS_DS_CROSS_DOM_MOVE_FAILED = 0xC00002AF,
  MD_NTSTATUS_WIN_STATUS_DS_GC_NOT_AVAILABLE = 0xC00002B0,
  MD_NTSTATUS_WIN_STATUS_DIRECTORY_SERVICE_REQUIRED = 0xC00002B1,
  MD_NTSTATUS_WIN_STATUS_REPARSE_ATTRIBUTE_CONFLICT = 0xC00002B2,
  MD_NTSTATUS_WIN_STATUS_CANT_ENABLE_DENY_ONLY = 0xC00002B3,
  MD_NTSTATUS_WIN_STATUS_FLOAT_MULTIPLE_FAULTS = 0xC00002B4,
  MD_NTSTATUS_WIN_STATUS_FLOAT_MULTIPLE_TRAPS = 0xC00002B5,
  MD_NTSTATUS_WIN_STATUS_DEVICE_REMOVED = 0xC00002B6,
  MD_NTSTATUS_WIN_STATUS_JOURNAL_DELETE_IN_PROGRESS = 0xC00002B7,
  MD_NTSTATUS_WIN_STATUS_JOURNAL_NOT_ACTIVE = 0xC00002B8,
  MD_NTSTATUS_WIN_STATUS_NOINTERFACE = 0xC00002B9,
  MD_NTSTATUS_WIN_STATUS_DS_RIDMGR_DISABLED = 0xC00002BA,
  MD_NTSTATUS_WIN_STATUS_DS_ADMIN_LIMIT_EXCEEDED = 0xC00002C1,
  MD_NTSTATUS_WIN_STATUS_DRIVER_FAILED_SLEEP = 0xC00002C2,
  MD_NTSTATUS_WIN_STATUS_MUTUAL_AUTHENTICATION_FAILED = 0xC00002C3,
  MD_NTSTATUS_WIN_STATUS_CORRUPT_SYSTEM_FILE = 0xC00002C4,
  MD_NTSTATUS_WIN_STATUS_DATATYPE_MISALIGNMENT_ERROR = 0xC00002C5,
  MD_NTSTATUS_WIN_STATUS_WMI_READ_ONLY = 0xC00002C6,
  MD_NTSTATUS_WIN_STATUS_WMI_SET_FAILURE = 0xC00002C7,
  MD_NTSTATUS_WIN_STATUS_COMMITMENT_MINIMUM = 0xC00002C8,
  MD_NTSTATUS_WIN_STATUS_REG_NAT_CONSUMPTION = 0xC00002C9,
  MD_NTSTATUS_WIN_STATUS_TRANSPORT_FULL = 0xC00002CA,
  MD_NTSTATUS_WIN_STATUS_DS_SAM_INIT_FAILURE = 0xC00002CB,
  MD_NTSTATUS_WIN_STATUS_ONLY_IF_CONNECTED = 0xC00002CC,
  MD_NTSTATUS_WIN_STATUS_DS_SENSITIVE_GROUP_VIOLATION = 0xC00002CD,
  MD_NTSTATUS_WIN_STATUS_PNP_RESTART_ENUMERATION = 0xC00002CE,
  MD_NTSTATUS_WIN_STATUS_JOURNAL_ENTRY_DELETED = 0xC00002CF,
  MD_NTSTATUS_WIN_STATUS_DS_CANT_MOD_PRIMARYGROUPID = 0xC00002D0,
  MD_NTSTATUS_WIN_STATUS_SYSTEM_IMAGE_BAD_SIGNATURE = 0xC00002D1,
  MD_NTSTATUS_WIN_STATUS_PNP_REBOOT_REQUIRED = 0xC00002D2,
  MD_NTSTATUS_WIN_STATUS_POWER_STATE_INVALID = 0xC00002D3,
  MD_NTSTATUS_WIN_STATUS_DS_INVALID_GROUP_TYPE = 0xC00002D4,
  MD_NTSTATUS_WIN_STATUS_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN = 0xC00002D5,
  MD_NTSTATUS_WIN_STATUS_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN = 0xC00002D6,
  MD_NTSTATUS_WIN_STATUS_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER = 0xC00002D7,
  MD_NTSTATUS_WIN_STATUS_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER = 0xC00002D8,
  MD_NTSTATUS_WIN_STATUS_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER = 0xC00002D9,
  MD_NTSTATUS_WIN_STATUS_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER = 0xC00002DA,
  MD_NTSTATUS_WIN_STATUS_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER = 0xC00002DB,
  MD_NTSTATUS_WIN_STATUS_DS_HAVE_PRIMARY_MEMBERS = 0xC00002DC,
  MD_NTSTATUS_WIN_STATUS_WMI_NOT_SUPPORTED = 0xC00002DD,
  MD_NTSTATUS_WIN_STATUS_INSUFFICIENT_POWER = 0xC00002DE,
  MD_NTSTATUS_WIN_STATUS_SAM_NEED_BOOTKEY_PASSWORD = 0xC00002DF,
  MD_NTSTATUS_WIN_STATUS_SAM_NEED_BOOTKEY_FLOPPY = 0xC00002E0,
  MD_NTSTATUS_WIN_STATUS_DS_CANT_START = 0xC00002E1,
  MD_NTSTATUS_WIN_STATUS_DS_INIT_FAILURE = 0xC00002E2,
  MD_NTSTATUS_WIN_STATUS_SAM_INIT_FAILURE = 0xC00002E3,
  MD_NTSTATUS_WIN_STATUS_DS_GC_REQUIRED = 0xC00002E4,
  MD_NTSTATUS_WIN_STATUS_DS_LOCAL_MEMBER_OF_LOCAL_ONLY = 0xC00002E5,
  MD_NTSTATUS_WIN_STATUS_DS_NO_FPO_IN_UNIVERSAL_GROUPS = 0xC00002E6,
  MD_NTSTATUS_WIN_STATUS_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED = 0xC00002E7,
  MD_NTSTATUS_WIN_STATUS_MULTIPLE_FAULT_VIOLATION = 0xC00002E8,
  MD_NTSTATUS_WIN_STATUS_CURRENT_DOMAIN_NOT_ALLOWED = 0xC00002E9,
  MD_NTSTATUS_WIN_STATUS_CANNOT_MAKE = 0xC00002EA,
  MD_NTSTATUS_WIN_STATUS_SYSTEM_SHUTDOWN = 0xC00002EB,
  MD_NTSTATUS_WIN_STATUS_DS_INIT_FAILURE_CONSOLE = 0xC00002EC,
  MD_NTSTATUS_WIN_STATUS_DS_SAM_INIT_FAILURE_CONSOLE = 0xC00002ED,
  MD_NTSTATUS_WIN_STATUS_UNFINISHED_CONTEXT_DELETED = 0xC00002EE,
  MD_NTSTATUS_WIN_STATUS_NO_TGT_REPLY = 0xC00002EF,
  MD_NTSTATUS_WIN_STATUS_OBJECTID_NOT_FOUND = 0xC00002F0,
  MD_NTSTATUS_WIN_STATUS_NO_IP_ADDRESSES = 0xC00002F1,
  MD_NTSTATUS_WIN_STATUS_WRONG_CREDENTIAL_HANDLE = 0xC00002F2,
  MD_NTSTATUS_WIN_STATUS_CRYPTO_SYSTEM_INVALID = 0xC00002F3,
  MD_NTSTATUS_WIN_STATUS_MAX_REFERRALS_EXCEEDED = 0xC00002F4,
  MD_NTSTATUS_WIN_STATUS_MUST_BE_KDC = 0xC00002F5,
  MD_NTSTATUS_WIN_STATUS_STRONG_CRYPTO_NOT_SUPPORTED = 0xC00002F6,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_PRINCIPALS = 0xC00002F7,
  MD_NTSTATUS_WIN_STATUS_NO_PA_DATA = 0xC00002F8,
  MD_NTSTATUS_WIN_STATUS_PKINIT_NAME_MISMATCH = 0xC00002F9,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_LOGON_REQUIRED = 0xC00002FA,
  MD_NTSTATUS_WIN_STATUS_KDC_INVALID_REQUEST = 0xC00002FB,
  MD_NTSTATUS_WIN_STATUS_KDC_UNABLE_TO_REFER = 0xC00002FC,
  MD_NTSTATUS_WIN_STATUS_KDC_UNKNOWN_ETYPE = 0xC00002FD,
  MD_NTSTATUS_WIN_STATUS_SHUTDOWN_IN_PROGRESS = 0xC00002FE,
  MD_NTSTATUS_WIN_STATUS_SERVER_SHUTDOWN_IN_PROGRESS = 0xC00002FF,
  MD_NTSTATUS_WIN_STATUS_NOT_SUPPORTED_ON_SBS = 0xC0000300,
  MD_NTSTATUS_WIN_STATUS_WMI_GUID_DISCONNECTED = 0xC0000301,
  MD_NTSTATUS_WIN_STATUS_WMI_ALREADY_DISABLED = 0xC0000302,
  MD_NTSTATUS_WIN_STATUS_WMI_ALREADY_ENABLED = 0xC0000303,
  MD_NTSTATUS_WIN_STATUS_MFT_TOO_FRAGMENTED = 0xC0000304,
  MD_NTSTATUS_WIN_STATUS_COPY_PROTECTION_FAILURE = 0xC0000305,
  MD_NTSTATUS_WIN_STATUS_CSS_AUTHENTICATION_FAILURE = 0xC0000306,
  MD_NTSTATUS_WIN_STATUS_CSS_KEY_NOT_PRESENT = 0xC0000307,
  MD_NTSTATUS_WIN_STATUS_CSS_KEY_NOT_ESTABLISHED = 0xC0000308,
  MD_NTSTATUS_WIN_STATUS_CSS_SCRAMBLED_SECTOR = 0xC0000309,
  MD_NTSTATUS_WIN_STATUS_CSS_REGION_MISMATCH = 0xC000030A,
  MD_NTSTATUS_WIN_STATUS_CSS_RESETS_EXHAUSTED = 0xC000030B,
  MD_NTSTATUS_WIN_STATUS_PASSWORD_CHANGE_REQUIRED = 0xC000030C,
  MD_NTSTATUS_WIN_STATUS_PKINIT_FAILURE = 0xC0000320,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_SUBSYSTEM_FAILURE = 0xC0000321,
  MD_NTSTATUS_WIN_STATUS_NO_KERB_KEY = 0xC0000322,
  MD_NTSTATUS_WIN_STATUS_HOST_DOWN = 0xC0000350,
  MD_NTSTATUS_WIN_STATUS_UNSUPPORTED_PREAUTH = 0xC0000351,
  MD_NTSTATUS_WIN_STATUS_EFS_ALG_BLOB_TOO_BIG = 0xC0000352,
  MD_NTSTATUS_WIN_STATUS_PORT_NOT_SET = 0xC0000353,
  MD_NTSTATUS_WIN_STATUS_DEBUGGER_INACTIVE = 0xC0000354,
  MD_NTSTATUS_WIN_STATUS_DS_VERSION_CHECK_FAILURE = 0xC0000355,
  MD_NTSTATUS_WIN_STATUS_AUDITING_DISABLED = 0xC0000356,
  MD_NTSTATUS_WIN_STATUS_PRENT4_MACHINE_ACCOUNT = 0xC0000357,
  MD_NTSTATUS_WIN_STATUS_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER = 0xC0000358,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_WIN_32 = 0xC0000359,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_WIN_64 = 0xC000035A,
  MD_NTSTATUS_WIN_STATUS_BAD_BINDINGS = 0xC000035B,
  MD_NTSTATUS_WIN_STATUS_NETWORK_SESSION_EXPIRED = 0xC000035C,
  MD_NTSTATUS_WIN_STATUS_APPHELP_BLOCK = 0xC000035D,
  MD_NTSTATUS_WIN_STATUS_ALL_SIDS_FILTERED = 0xC000035E,
  MD_NTSTATUS_WIN_STATUS_NOT_SAFE_MODE_DRIVER = 0xC000035F,
  MD_NTSTATUS_WIN_STATUS_ACCESS_DISABLED_BY_POLICY_DEFAULT = 0xC0000361,
  MD_NTSTATUS_WIN_STATUS_ACCESS_DISABLED_BY_POLICY_PATH = 0xC0000362,
  MD_NTSTATUS_WIN_STATUS_ACCESS_DISABLED_BY_POLICY_PUBLISHER = 0xC0000363,
  MD_NTSTATUS_WIN_STATUS_ACCESS_DISABLED_BY_POLICY_OTHER = 0xC0000364,
  MD_NTSTATUS_WIN_STATUS_FAILED_DRIVER_ENTRY = 0xC0000365,
  MD_NTSTATUS_WIN_STATUS_DEVICE_ENUMERATION_ERROR = 0xC0000366,
  MD_NTSTATUS_WIN_STATUS_MOUNT_POINT_NOT_RESOLVED = 0xC0000368,
  MD_NTSTATUS_WIN_STATUS_INVALID_DEVICE_OBJECT_PARAMETER = 0xC0000369,
  MD_NTSTATUS_WIN_STATUS_MCA_OCCURED = 0xC000036A,
  MD_NTSTATUS_WIN_STATUS_DRIVER_BLOCKED_CRITICAL = 0xC000036B,
  MD_NTSTATUS_WIN_STATUS_DRIVER_BLOCKED = 0xC000036C,
  MD_NTSTATUS_WIN_STATUS_DRIVER_DATABASE_ERROR = 0xC000036D,
  MD_NTSTATUS_WIN_STATUS_SYSTEM_HIVE_TOO_LARGE = 0xC000036E,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMPORT_OF_NON_DLL = 0xC000036F,
  MD_NTSTATUS_WIN_STATUS_NO_SECRETS = 0xC0000371,
  MD_NTSTATUS_WIN_STATUS_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY = 0xC0000372,
  MD_NTSTATUS_WIN_STATUS_FAILED_STACK_SWITCH = 0xC0000373,
  MD_NTSTATUS_WIN_STATUS_HEAP_CORRUPTION = 0xC0000374,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_WRONG_PIN = 0xC0000380,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_CARD_BLOCKED = 0xC0000381,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_CARD_NOT_AUTHENTICATED = 0xC0000382,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_NO_CARD = 0xC0000383,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_NO_KEY_CONTAINER = 0xC0000384,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_NO_CERTIFICATE = 0xC0000385,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_NO_KEYSET = 0xC0000386,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_IO_ERROR = 0xC0000387,
  MD_NTSTATUS_WIN_STATUS_DOWNGRADE_DETECTED = 0xC0000388,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_CERT_REVOKED = 0xC0000389,
  MD_NTSTATUS_WIN_STATUS_ISSUING_CA_UNTRUSTED = 0xC000038A,
  MD_NTSTATUS_WIN_STATUS_REVOCATION_OFFLINE_C = 0xC000038B,
  MD_NTSTATUS_WIN_STATUS_PKINIT_CLIENT_FAILURE = 0xC000038C,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_CERT_EXPIRED = 0xC000038D,
  MD_NTSTATUS_WIN_STATUS_DRIVER_FAILED_PRIOR_UNLOAD = 0xC000038E,
  MD_NTSTATUS_WIN_STATUS_SMARTCARD_SILENT_CONTEXT = 0xC000038F,
  MD_NTSTATUS_WIN_STATUS_PER_USER_TRUST_QUOTA_EXCEEDED = 0xC0000401,
  MD_NTSTATUS_WIN_STATUS_ALL_USER_TRUST_QUOTA_EXCEEDED = 0xC0000402,
  MD_NTSTATUS_WIN_STATUS_USER_DELETE_TRUST_QUOTA_EXCEEDED = 0xC0000403,
  MD_NTSTATUS_WIN_STATUS_DS_NAME_NOT_UNIQUE = 0xC0000404,
  MD_NTSTATUS_WIN_STATUS_DS_DUPLICATE_ID_FOUND = 0xC0000405,
  MD_NTSTATUS_WIN_STATUS_DS_GROUP_CONVERSION_ERROR = 0xC0000406,
  MD_NTSTATUS_WIN_STATUS_VOLSNAP_PREPARE_HIBERNATE = 0xC0000407,
  MD_NTSTATUS_WIN_STATUS_USER2USER_REQUIRED = 0xC0000408,
  MD_NTSTATUS_WIN_STATUS_STACK_BUFFER_OVERRUN = 0xC0000409,
  MD_NTSTATUS_WIN_STATUS_NO_S4U_PROT_SUPPORT = 0xC000040A,
  MD_NTSTATUS_WIN_STATUS_CROSSREALM_DELEGATION_FAILURE = 0xC000040B,
  MD_NTSTATUS_WIN_STATUS_REVOCATION_OFFLINE_KDC = 0xC000040C,
  MD_NTSTATUS_WIN_STATUS_ISSUING_CA_UNTRUSTED_KDC = 0xC000040D,
  MD_NTSTATUS_WIN_STATUS_KDC_CERT_EXPIRED = 0xC000040E,
  MD_NTSTATUS_WIN_STATUS_KDC_CERT_REVOKED = 0xC000040F,
  MD_NTSTATUS_WIN_STATUS_PARAMETER_QUOTA_EXCEEDED = 0xC0000410,
  MD_NTSTATUS_WIN_STATUS_HIBERNATION_FAILURE = 0xC0000411,
  MD_NTSTATUS_WIN_STATUS_DELAY_LOAD_FAILED = 0xC0000412,
  MD_NTSTATUS_WIN_STATUS_AUTHENTICATION_FIREWALL_FAILED = 0xC0000413,
  MD_NTSTATUS_WIN_STATUS_VDM_DISALLOWED = 0xC0000414,
  MD_NTSTATUS_WIN_STATUS_HUNG_DISPLAY_DRIVER_THREAD = 0xC0000415,
  MD_NTSTATUS_WIN_STATUS_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE = 0xC0000416,
  MD_NTSTATUS_WIN_STATUS_INVALID_CRUNTIME_PARAMETER = 0xC0000417,
  MD_NTSTATUS_WIN_STATUS_NTLM_BLOCKED = 0xC0000418,
  MD_NTSTATUS_WIN_STATUS_DS_SRC_SID_EXISTS_IN_FOREST = 0xC0000419,
  MD_NTSTATUS_WIN_STATUS_DS_DOMAIN_NAME_EXISTS_IN_FOREST = 0xC000041A,
  MD_NTSTATUS_WIN_STATUS_DS_FLAT_NAME_EXISTS_IN_FOREST = 0xC000041B,
  MD_NTSTATUS_WIN_STATUS_INVALID_USER_PRINCIPAL_NAME = 0xC000041C,
  MD_NTSTATUS_WIN_STATUS_FATAL_USER_CALLBACK_EXCEPTION = 0xC000041D,
  MD_NTSTATUS_WIN_STATUS_ASSERTION_FAILURE = 0xC0000420,
  MD_NTSTATUS_WIN_STATUS_VERIFIER_STOP = 0xC0000421,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_POP_STACK = 0xC0000423,
  MD_NTSTATUS_WIN_STATUS_INCOMPATIBLE_DRIVER_BLOCKED = 0xC0000424,
  MD_NTSTATUS_WIN_STATUS_HIVE_UNLOADED = 0xC0000425,
  MD_NTSTATUS_WIN_STATUS_COMPRESSION_DISABLED = 0xC0000426,
  MD_NTSTATUS_WIN_STATUS_FILE_SYSTEM_LIMITATION = 0xC0000427,
  MD_NTSTATUS_WIN_STATUS_INVALID_IMAGE_HASH = 0xC0000428,
  MD_NTSTATUS_WIN_STATUS_NOT_CAPABLE = 0xC0000429,
  MD_NTSTATUS_WIN_STATUS_REQUEST_OUT_OF_SEQUENCE = 0xC000042A,
  MD_NTSTATUS_WIN_STATUS_IMPLEMENTATION_LIMIT = 0xC000042B,
  MD_NTSTATUS_WIN_STATUS_ELEVATION_REQUIRED = 0xC000042C,
  MD_NTSTATUS_WIN_STATUS_NO_SECURITY_CONTEXT = 0xC000042D,
  MD_NTSTATUS_WIN_STATUS_PKU2U_CERT_FAILURE = 0xC000042F,
  MD_NTSTATUS_WIN_STATUS_BEYOND_VDL = 0xC0000432,
  MD_NTSTATUS_WIN_STATUS_ENCOUNTERED_WRITE_IN_PROGRESS = 0xC0000433,
  MD_NTSTATUS_WIN_STATUS_PTE_CHANGED = 0xC0000434,
  MD_NTSTATUS_WIN_STATUS_PURGE_FAILED = 0xC0000435,
  MD_NTSTATUS_WIN_STATUS_CRED_REQUIRES_CONFIRMATION = 0xC0000440,
  MD_NTSTATUS_WIN_STATUS_CS_ENCRYPTION_INVALID_SERVER_RESPONSE = 0xC0000441,
  MD_NTSTATUS_WIN_STATUS_CS_ENCRYPTION_UNSUPPORTED_SERVER = 0xC0000442,
  MD_NTSTATUS_WIN_STATUS_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE = 0xC0000443,
  MD_NTSTATUS_WIN_STATUS_CS_ENCRYPTION_NEW_ENCRYPTED_FILE = 0xC0000444,
  MD_NTSTATUS_WIN_STATUS_CS_ENCRYPTION_FILE_NOT_CSE = 0xC0000445,
  MD_NTSTATUS_WIN_STATUS_INVALID_LABEL = 0xC0000446,
  MD_NTSTATUS_WIN_STATUS_DRIVER_PROCESS_TERMINATED = 0xC0000450,
  MD_NTSTATUS_WIN_STATUS_AMBIGUOUS_SYSTEM_DEVICE = 0xC0000451,
  MD_NTSTATUS_WIN_STATUS_SYSTEM_DEVICE_NOT_FOUND = 0xC0000452,
  MD_NTSTATUS_WIN_STATUS_RESTART_BOOT_APPLICATION = 0xC0000453,
  MD_NTSTATUS_WIN_STATUS_INSUFFICIENT_NVRAM_RESOURCES = 0xC0000454,
  MD_NTSTATUS_WIN_STATUS_INVALID_SESSION = 0xC0000455,
  MD_NTSTATUS_WIN_STATUS_THREAD_ALREADY_IN_SESSION = 0xC0000456,
  MD_NTSTATUS_WIN_STATUS_THREAD_NOT_IN_SESSION = 0xC0000457,
  MD_NTSTATUS_WIN_STATUS_INVALID_WEIGHT = 0xC0000458,
  MD_NTSTATUS_WIN_STATUS_REQUEST_PAUSED = 0xC0000459,
  MD_NTSTATUS_WIN_STATUS_NO_RANGES_PROCESSED = 0xC0000460,
  MD_NTSTATUS_WIN_STATUS_DISK_RESOURCES_EXHAUSTED = 0xC0000461,
  MD_NTSTATUS_WIN_STATUS_NEEDS_REMEDIATION = 0xC0000462,
  MD_NTSTATUS_WIN_STATUS_DEVICE_FEATURE_NOT_SUPPORTED = 0xC0000463,
  MD_NTSTATUS_WIN_STATUS_DEVICE_UNREACHABLE = 0xC0000464,
  MD_NTSTATUS_WIN_STATUS_INVALID_TOKEN = 0xC0000465,
  MD_NTSTATUS_WIN_STATUS_SERVER_UNAVAILABLE = 0xC0000466,
  MD_NTSTATUS_WIN_STATUS_FILE_NOT_AVAILABLE = 0xC0000467,
  MD_NTSTATUS_WIN_STATUS_DEVICE_INSUFFICIENT_RESOURCES = 0xC0000468,
  MD_NTSTATUS_WIN_STATUS_PACKAGE_UPDATING = 0xC0000469,
  MD_NTSTATUS_WIN_STATUS_NOT_READ_FROM_COPY = 0xC000046A,
  MD_NTSTATUS_WIN_STATUS_FT_WRITE_FAILURE = 0xC000046B,
  MD_NTSTATUS_WIN_STATUS_FT_DI_SCAN_REQUIRED = 0xC000046C,
  MD_NTSTATUS_WIN_STATUS_OBJECT_NOT_EXTERNALLY_BACKED = 0xC000046D,
  MD_NTSTATUS_WIN_STATUS_EXTERNAL_BACKING_PROVIDER_UNKNOWN = 0xC000046E,
  MD_NTSTATUS_WIN_STATUS_DATA_CHECKSUM_ERROR = 0xC0000470,
  MD_NTSTATUS_WIN_STATUS_INTERMIXED_KERNEL_EA_OPERATION = 0xC0000471,
  MD_NTSTATUS_WIN_STATUS_TRIM_READ_ZERO_NOT_SUPPORTED = 0xC0000472,
  MD_NTSTATUS_WIN_STATUS_TOO_MANY_SEGMENT_DESCRIPTORS = 0xC0000473,
  MD_NTSTATUS_WIN_STATUS_INVALID_OFFSET_ALIGNMENT = 0xC0000474,
  MD_NTSTATUS_WIN_STATUS_INVALID_FIELD_IN_PARAMETER_LIST = 0xC0000475,
  MD_NTSTATUS_WIN_STATUS_OPERATION_IN_PROGRESS = 0xC0000476,
  MD_NTSTATUS_WIN_STATUS_INVALID_INITIATOR_TARGET_PATH = 0xC0000477,
  MD_NTSTATUS_WIN_STATUS_SCRUB_DATA_DISABLED = 0xC0000478,
  MD_NTSTATUS_WIN_STATUS_NOT_REDUNDANT_STORAGE = 0xC0000479,
  MD_NTSTATUS_WIN_STATUS_RESIDENT_FILE_NOT_SUPPORTED = 0xC000047A,
  MD_NTSTATUS_WIN_STATUS_COMPRESSED_FILE_NOT_SUPPORTED = 0xC000047B,
  MD_NTSTATUS_WIN_STATUS_DIRECTORY_NOT_SUPPORTED = 0xC000047C,
  MD_NTSTATUS_WIN_STATUS_IO_OPERATION_TIMEOUT = 0xC000047D,
  MD_NTSTATUS_WIN_STATUS_SYSTEM_NEEDS_REMEDIATION = 0xC000047E,
  MD_NTSTATUS_WIN_STATUS_APPX_INTEGRITY_FAILURE_CLR_NGEN = 0xC000047F,
  MD_NTSTATUS_WIN_STATUS_SHARE_UNAVAILABLE = 0xC0000480,
  MD_NTSTATUS_WIN_STATUS_APISET_NOT_HOSTED = 0xC0000481,
  MD_NTSTATUS_WIN_STATUS_APISET_NOT_PRESENT = 0xC0000482,
  MD_NTSTATUS_WIN_STATUS_DEVICE_HARDWARE_ERROR = 0xC0000483,
  MD_NTSTATUS_WIN_STATUS_INVALID_TASK_NAME = 0xC0000500,
  MD_NTSTATUS_WIN_STATUS_INVALID_TASK_INDEX = 0xC0000501,
  MD_NTSTATUS_WIN_STATUS_THREAD_ALREADY_IN_TASK = 0xC0000502,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_BYPASS = 0xC0000503,
  MD_NTSTATUS_WIN_STATUS_UNDEFINED_SCOPE = 0xC0000504,
  MD_NTSTATUS_WIN_STATUS_INVALID_CAP = 0xC0000505,
  MD_NTSTATUS_WIN_STATUS_NOT_GUI_PROCESS = 0xC0000506,
  MD_NTSTATUS_WIN_STATUS_FAIL_FAST_EXCEPTION = 0xC0000602,
  MD_NTSTATUS_WIN_STATUS_IMAGE_CERT_REVOKED = 0xC0000603,
  MD_NTSTATUS_WIN_STATUS_DYNAMIC_CODE_BLOCKED = 0xC0000604,
  MD_NTSTATUS_WIN_STATUS_PORT_CLOSED = 0xC0000700,
  MD_NTSTATUS_WIN_STATUS_MESSAGE_LOST = 0xC0000701,
  MD_NTSTATUS_WIN_STATUS_INVALID_MESSAGE = 0xC0000702,
  MD_NTSTATUS_WIN_STATUS_REQUEST_CANCELED = 0xC0000703,
  MD_NTSTATUS_WIN_STATUS_RECURSIVE_DISPATCH = 0xC0000704,
  MD_NTSTATUS_WIN_STATUS_LPC_RECEIVE_BUFFER_EXPECTED = 0xC0000705,
  MD_NTSTATUS_WIN_STATUS_LPC_INVALID_CONNECTION_USAGE = 0xC0000706,
  MD_NTSTATUS_WIN_STATUS_LPC_REQUESTS_NOT_ALLOWED = 0xC0000707,
  MD_NTSTATUS_WIN_STATUS_RESOURCE_IN_USE = 0xC0000708,
  MD_NTSTATUS_WIN_STATUS_HARDWARE_MEMORY_ERROR = 0xC0000709,
  MD_NTSTATUS_WIN_STATUS_THREADPOOL_HANDLE_EXCEPTION = 0xC000070A,
  MD_NTSTATUS_WIN_STATUS_THREADPOOL_SET_EVENT_ON_COMPLETION_FAILED = 0xC000070B,
  MD_NTSTATUS_WIN_STATUS_THREADPOOL_RELEASE_SEMAPHORE_ON_COMPLETION_FAILED = 0xC000070C,
  MD_NTSTATUS_WIN_STATUS_THREADPOOL_RELEASE_MUTEX_ON_COMPLETION_FAILED = 0xC000070D,
  MD_NTSTATUS_WIN_STATUS_THREADPOOL_FREE_LIBRARY_ON_COMPLETION_FAILED = 0xC000070E,
  MD_NTSTATUS_WIN_STATUS_THREADPOOL_RELEASED_DURING_OPERATION = 0xC000070F,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_RETURNED_WHILE_IMPERSONATING = 0xC0000710,
  MD_NTSTATUS_WIN_STATUS_APC_RETURNED_WHILE_IMPERSONATING = 0xC0000711,
  MD_NTSTATUS_WIN_STATUS_PROCESS_IS_PROTECTED = 0xC0000712,
  MD_NTSTATUS_WIN_STATUS_MCA_EXCEPTION = 0xC0000713,
  MD_NTSTATUS_WIN_STATUS_CERTIFICATE_MAPPING_NOT_UNIQUE = 0xC0000714,
  MD_NTSTATUS_WIN_STATUS_SYMLINK_CLASS_DISABLED = 0xC0000715,
  MD_NTSTATUS_WIN_STATUS_INVALID_IDN_NORMALIZATION = 0xC0000716,
  MD_NTSTATUS_WIN_STATUS_NO_UNICODE_TRANSLATION = 0xC0000717,
  MD_NTSTATUS_WIN_STATUS_ALREADY_REGISTERED = 0xC0000718,
  MD_NTSTATUS_WIN_STATUS_CONTEXT_MISMATCH = 0xC0000719,
  MD_NTSTATUS_WIN_STATUS_PORT_ALREADY_HAS_COMPLETION_LIST = 0xC000071A,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_RETURNED_THREAD_PRIORITY = 0xC000071B,
  MD_NTSTATUS_WIN_STATUS_INVALID_THREAD = 0xC000071C,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_RETURNED_TRANSACTION = 0xC000071D,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_RETURNED_LDR_LOCK = 0xC000071E,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_RETURNED_LANG = 0xC000071F,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_RETURNED_PRI_BACK = 0xC0000720,
  MD_NTSTATUS_WIN_STATUS_CALLBACK_RETURNED_THREAD_AFFINITY = 0xC0000721,
  MD_NTSTATUS_WIN_STATUS_DISK_REPAIR_DISABLED = 0xC0000800,
  MD_NTSTATUS_WIN_STATUS_DS_DOMAIN_RENAME_IN_PROGRESS = 0xC0000801,
  MD_NTSTATUS_WIN_STATUS_DISK_QUOTA_EXCEEDED = 0xC0000802,
  MD_NTSTATUS_WIN_STATUS_CONTENT_BLOCKED = 0xC0000804,
  MD_NTSTATUS_WIN_STATUS_BAD_CLUSTERS = 0xC0000805,
  MD_NTSTATUS_WIN_STATUS_VOLUME_DIRTY = 0xC0000806,
  MD_NTSTATUS_WIN_STATUS_DISK_REPAIR_UNSUCCESSFUL = 0xC0000808,
  MD_NTSTATUS_WIN_STATUS_CORRUPT_LOG_OVERFULL = 0xC0000809,
  MD_NTSTATUS_WIN_STATUS_CORRUPT_LOG_CORRUPTED = 0xC000080A,
  MD_NTSTATUS_WIN_STATUS_CORRUPT_LOG_UNAVAILABLE = 0xC000080B,
  MD_NTSTATUS_WIN_STATUS_CORRUPT_LOG_DELETED_FULL = 0xC000080C,
  MD_NTSTATUS_WIN_STATUS_CORRUPT_LOG_CLEARED = 0xC000080D,
  MD_NTSTATUS_WIN_STATUS_ORPHAN_NAME_EXHAUSTED = 0xC000080E,
  MD_NTSTATUS_WIN_STATUS_PROACTIVE_SCAN_IN_PROGRESS = 0xC000080F,
  MD_NTSTATUS_WIN_STATUS_ENCRYPTED_IO_NOT_POSSIBLE = 0xC0000810,
  MD_NTSTATUS_WIN_STATUS_CORRUPT_LOG_UPLEVEL_RECORDS = 0xC0000811,
  MD_NTSTATUS_WIN_STATUS_FILE_CHECKED_OUT = 0xC0000901,
  MD_NTSTATUS_WIN_STATUS_CHECKOUT_REQUIRED = 0xC0000902,
  MD_NTSTATUS_WIN_STATUS_BAD_FILE_TYPE = 0xC0000903,
  MD_NTSTATUS_WIN_STATUS_FILE_TOO_LARGE = 0xC0000904,
  MD_NTSTATUS_WIN_STATUS_FORMS_AUTH_REQUIRED = 0xC0000905,
  MD_NTSTATUS_WIN_STATUS_VIRUS_INFECTED = 0xC0000906,
  MD_NTSTATUS_WIN_STATUS_VIRUS_DELETED = 0xC0000907,
  MD_NTSTATUS_WIN_STATUS_BAD_MCFG_TABLE = 0xC0000908,
  MD_NTSTATUS_WIN_STATUS_CANNOT_BREAK_OPLOCK = 0xC0000909,
  MD_NTSTATUS_WIN_STATUS_BAD_KEY = 0xC000090A,
  MD_NTSTATUS_WIN_STATUS_BAD_DATA = 0xC000090B,
  MD_NTSTATUS_WIN_STATUS_NO_KEY = 0xC000090C,
  MD_NTSTATUS_WIN_STATUS_FILE_HANDLE_REVOKED = 0xC0000910,
  MD_NTSTATUS_WIN_STATUS_WOW_ASSERTION = 0xC0009898,
  MD_NTSTATUS_WIN_STATUS_INVALID_SIGNATURE = 0xC000A000,
  MD_NTSTATUS_WIN_STATUS_HMAC_NOT_SUPPORTED = 0xC000A001,
  MD_NTSTATUS_WIN_STATUS_AUTH_TAG_MISMATCH = 0xC000A002,
  MD_NTSTATUS_WIN_STATUS_INVALID_STATE_TRANSITION = 0xC000A003,
  MD_NTSTATUS_WIN_STATUS_INVALID_KERNEL_INFO_VERSION = 0xC000A004,
  MD_NTSTATUS_WIN_STATUS_INVALID_PEP_INFO_VERSION = 0xC000A005,
  MD_NTSTATUS_WIN_STATUS_IPSEC_QUEUE_OVERFLOW = 0xC000A010,
  MD_NTSTATUS_WIN_STATUS_ND_QUEUE_OVERFLOW = 0xC000A011,
  MD_NTSTATUS_WIN_STATUS_HOPLIMIT_EXCEEDED = 0xC000A012,
  MD_NTSTATUS_WIN_STATUS_PROTOCOL_NOT_SUPPORTED = 0xC000A013,
  MD_NTSTATUS_WIN_STATUS_FASTPATH_REJECTED = 0xC000A014,
  MD_NTSTATUS_WIN_STATUS_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED = 0xC000A080,
  MD_NTSTATUS_WIN_STATUS_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR = 0xC000A081,
  MD_NTSTATUS_WIN_STATUS_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR = 0xC000A082,
  MD_NTSTATUS_WIN_STATUS_XML_PARSE_ERROR = 0xC000A083,
  MD_NTSTATUS_WIN_STATUS_XMLDSIG_ERROR = 0xC000A084,
  MD_NTSTATUS_WIN_STATUS_WRONG_COMPARTMENT = 0xC000A085,
  MD_NTSTATUS_WIN_STATUS_AUTHIP_FAILURE = 0xC000A086,
  MD_NTSTATUS_WIN_STATUS_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS = 0xC000A087,
  MD_NTSTATUS_WIN_STATUS_DS_OID_NOT_FOUND = 0xC000A088,
  MD_NTSTATUS_WIN_STATUS_INCORRECT_ACCOUNT_TYPE = 0xC000A089,
  MD_NTSTATUS_WIN_STATUS_HASH_NOT_SUPPORTED = 0xC000A100,
  MD_NTSTATUS_WIN_STATUS_HASH_NOT_PRESENT = 0xC000A101,
  MD_NTSTATUS_WIN_STATUS_SECONDARY_IC_PROVIDER_NOT_REGISTERED = 0xC000A121,
  MD_NTSTATUS_WIN_STATUS_GPIO_CLIENT_INFORMATION_INVALID = 0xC000A122,
  MD_NTSTATUS_WIN_STATUS_GPIO_VERSION_NOT_SUPPORTED = 0xC000A123,
  MD_NTSTATUS_WIN_STATUS_GPIO_INVALID_REGISTRATION_PACKET = 0xC000A124,
  MD_NTSTATUS_WIN_STATUS_GPIO_OPERATION_DENIED = 0xC000A125,
  MD_NTSTATUS_WIN_STATUS_GPIO_INCOMPATIBLE_CONNECT_MODE = 0xC000A126,
  MD_NTSTATUS_WIN_STATUS_CANNOT_SWITCH_RUNLEVEL = 0xC000A141,
  MD_NTSTATUS_WIN_STATUS_INVALID_RUNLEVEL_SETTING = 0xC000A142,
  MD_NTSTATUS_WIN_STATUS_RUNLEVEL_SWITCH_TIMEOUT = 0xC000A143,
  MD_NTSTATUS_WIN_STATUS_RUNLEVEL_SWITCH_AGENT_TIMEOUT = 0xC000A145,
  MD_NTSTATUS_WIN_STATUS_RUNLEVEL_SWITCH_IN_PROGRESS = 0xC000A146,
  MD_NTSTATUS_WIN_STATUS_NOT_APPCONTAINER = 0xC000A200,
  MD_NTSTATUS_WIN_STATUS_NOT_SUPPORTED_IN_APPCONTAINER = 0xC000A201,
  MD_NTSTATUS_WIN_STATUS_INVALID_PACKAGE_SID_LENGTH = 0xC000A202,
  MD_NTSTATUS_WIN_STATUS_APP_DATA_NOT_FOUND = 0xC000A281,
  MD_NTSTATUS_WIN_STATUS_APP_DATA_EXPIRED = 0xC000A282,
  MD_NTSTATUS_WIN_STATUS_APP_DATA_CORRUPT = 0xC000A283,
  MD_NTSTATUS_WIN_STATUS_APP_DATA_LIMIT_EXCEEDED = 0xC000A284,
  MD_NTSTATUS_WIN_STATUS_APP_DATA_REBOOT_REQUIRED = 0xC000A285,
  MD_NTSTATUS_WIN_STATUS_OFFLOAD_READ_FLT_NOT_SUPPORTED = 0xC000A2A1,
  MD_NTSTATUS_WIN_STATUS_OFFLOAD_WRITE_FLT_NOT_SUPPORTED = 0xC000A2A2,
  MD_NTSTATUS_WIN_STATUS_OFFLOAD_READ_FILE_NOT_SUPPORTED = 0xC000A2A3,
  MD_NTSTATUS_WIN_STATUS_OFFLOAD_WRITE_FILE_NOT_SUPPORTED = 0xC000A2A4,
  MD_NTSTATUS_WIN_DBG_NO_STATE_CHANGE = 0xC0010001,
  MD_NTSTATUS_WIN_DBG_APP_NOT_IDLE = 0xC0010002,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_STRING_BINDING = 0xC0020001,
  MD_NTSTATUS_WIN_RPC_NT_WRONG_KIND_OF_BINDING = 0xC0020002,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_BINDING = 0xC0020003,
  MD_NTSTATUS_WIN_RPC_NT_PROTSEQ_NOT_SUPPORTED = 0xC0020004,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_RPC_PROTSEQ = 0xC0020005,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_STRING_UUID = 0xC0020006,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_ENDPOINT_FORMAT = 0xC0020007,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_NET_ADDR = 0xC0020008,
  MD_NTSTATUS_WIN_RPC_NT_NO_ENDPOINT_FOUND = 0xC0020009,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_TIMEOUT = 0xC002000A,
  MD_NTSTATUS_WIN_RPC_NT_OBJECT_NOT_FOUND = 0xC002000B,
  MD_NTSTATUS_WIN_RPC_NT_ALREADY_REGISTERED = 0xC002000C,
  MD_NTSTATUS_WIN_RPC_NT_TYPE_ALREADY_REGISTERED = 0xC002000D,
  MD_NTSTATUS_WIN_RPC_NT_ALREADY_LISTENING = 0xC002000E,
  MD_NTSTATUS_WIN_RPC_NT_NO_PROTSEQS_REGISTERED = 0xC002000F,
  MD_NTSTATUS_WIN_RPC_NT_NOT_LISTENING = 0xC0020010,
  MD_NTSTATUS_WIN_RPC_NT_UNKNOWN_MGR_TYPE = 0xC0020011,
  MD_NTSTATUS_WIN_RPC_NT_UNKNOWN_IF = 0xC0020012,
  MD_NTSTATUS_WIN_RPC_NT_NO_BINDINGS = 0xC0020013,
  MD_NTSTATUS_WIN_RPC_NT_NO_PROTSEQS = 0xC0020014,
  MD_NTSTATUS_WIN_RPC_NT_CANT_CREATE_ENDPOINT = 0xC0020015,
  MD_NTSTATUS_WIN_RPC_NT_OUT_OF_RESOURCES = 0xC0020016,
  MD_NTSTATUS_WIN_RPC_NT_SERVER_UNAVAILABLE = 0xC0020017,
  MD_NTSTATUS_WIN_RPC_NT_SERVER_TOO_BUSY = 0xC0020018,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_NETWORK_OPTIONS = 0xC0020019,
  MD_NTSTATUS_WIN_RPC_NT_NO_CALL_ACTIVE = 0xC002001A,
  MD_NTSTATUS_WIN_RPC_NT_CALL_FAILED = 0xC002001B,
  MD_NTSTATUS_WIN_RPC_NT_CALL_FAILED_DNE = 0xC002001C,
  MD_NTSTATUS_WIN_RPC_NT_PROTOCOL_ERROR = 0xC002001D,
  MD_NTSTATUS_WIN_RPC_NT_UNSUPPORTED_TRANS_SYN = 0xC002001F,
  MD_NTSTATUS_WIN_RPC_NT_UNSUPPORTED_TYPE = 0xC0020021,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_TAG = 0xC0020022,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_BOUND = 0xC0020023,
  MD_NTSTATUS_WIN_RPC_NT_NO_ENTRY_NAME = 0xC0020024,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_NAME_SYNTAX = 0xC0020025,
  MD_NTSTATUS_WIN_RPC_NT_UNSUPPORTED_NAME_SYNTAX = 0xC0020026,
  MD_NTSTATUS_WIN_RPC_NT_UUID_NO_ADDRESS = 0xC0020028,
  MD_NTSTATUS_WIN_RPC_NT_DUPLICATE_ENDPOINT = 0xC0020029,
  MD_NTSTATUS_WIN_RPC_NT_UNKNOWN_AUTHN_TYPE = 0xC002002A,
  MD_NTSTATUS_WIN_RPC_NT_MAX_CALLS_TOO_SMALL = 0xC002002B,
  MD_NTSTATUS_WIN_RPC_NT_STRING_TOO_LONG = 0xC002002C,
  MD_NTSTATUS_WIN_RPC_NT_PROTSEQ_NOT_FOUND = 0xC002002D,
  MD_NTSTATUS_WIN_RPC_NT_PROCNUM_OUT_OF_RANGE = 0xC002002E,
  MD_NTSTATUS_WIN_RPC_NT_BINDING_HAS_NO_AUTH = 0xC002002F,
  MD_NTSTATUS_WIN_RPC_NT_UNKNOWN_AUTHN_SERVICE = 0xC0020030,
  MD_NTSTATUS_WIN_RPC_NT_UNKNOWN_AUTHN_LEVEL = 0xC0020031,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_AUTH_IDENTITY = 0xC0020032,
  MD_NTSTATUS_WIN_RPC_NT_UNKNOWN_AUTHZ_SERVICE = 0xC0020033,
  MD_NTSTATUS_WIN_EPT_NT_INVALID_ENTRY = 0xC0020034,
  MD_NTSTATUS_WIN_EPT_NT_CANT_PERFORM_OP = 0xC0020035,
  MD_NTSTATUS_WIN_EPT_NT_NOT_REGISTERED = 0xC0020036,
  MD_NTSTATUS_WIN_RPC_NT_NOTHING_TO_EXPORT = 0xC0020037,
  MD_NTSTATUS_WIN_RPC_NT_INCOMPLETE_NAME = 0xC0020038,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_VERS_OPTION = 0xC0020039,
  MD_NTSTATUS_WIN_RPC_NT_NO_MORE_MEMBERS = 0xC002003A,
  MD_NTSTATUS_WIN_RPC_NT_NOT_ALL_OBJS_UNEXPORTED = 0xC002003B,
  MD_NTSTATUS_WIN_RPC_NT_INTERFACE_NOT_FOUND = 0xC002003C,
  MD_NTSTATUS_WIN_RPC_NT_ENTRY_ALREADY_EXISTS = 0xC002003D,
  MD_NTSTATUS_WIN_RPC_NT_ENTRY_NOT_FOUND = 0xC002003E,
  MD_NTSTATUS_WIN_RPC_NT_NAME_SERVICE_UNAVAILABLE = 0xC002003F,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_NAF_ID = 0xC0020040,
  MD_NTSTATUS_WIN_RPC_NT_CANNOT_SUPPORT = 0xC0020041,
  MD_NTSTATUS_WIN_RPC_NT_NO_CONTEXT_AVAILABLE = 0xC0020042,
  MD_NTSTATUS_WIN_RPC_NT_INTERNAL_ERROR = 0xC0020043,
  MD_NTSTATUS_WIN_RPC_NT_ZERO_DIVIDE = 0xC0020044,
  MD_NTSTATUS_WIN_RPC_NT_ADDRESS_ERROR = 0xC0020045,
  MD_NTSTATUS_WIN_RPC_NT_FP_DIV_ZERO = 0xC0020046,
  MD_NTSTATUS_WIN_RPC_NT_FP_UNDERFLOW = 0xC0020047,
  MD_NTSTATUS_WIN_RPC_NT_FP_OVERFLOW = 0xC0020048,
  MD_NTSTATUS_WIN_RPC_NT_CALL_IN_PROGRESS = 0xC0020049,
  MD_NTSTATUS_WIN_RPC_NT_NO_MORE_BINDINGS = 0xC002004A,
  MD_NTSTATUS_WIN_RPC_NT_GROUP_MEMBER_NOT_FOUND = 0xC002004B,
  MD_NTSTATUS_WIN_EPT_NT_CANT_CREATE = 0xC002004C,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_OBJECT = 0xC002004D,
  MD_NTSTATUS_WIN_RPC_NT_NO_INTERFACES = 0xC002004F,
  MD_NTSTATUS_WIN_RPC_NT_CALL_CANCELLED = 0xC0020050,
  MD_NTSTATUS_WIN_RPC_NT_BINDING_INCOMPLETE = 0xC0020051,
  MD_NTSTATUS_WIN_RPC_NT_COMM_FAILURE = 0xC0020052,
  MD_NTSTATUS_WIN_RPC_NT_UNSUPPORTED_AUTHN_LEVEL = 0xC0020053,
  MD_NTSTATUS_WIN_RPC_NT_NO_PRINC_NAME = 0xC0020054,
  MD_NTSTATUS_WIN_RPC_NT_NOT_RPC_ERROR = 0xC0020055,
  MD_NTSTATUS_WIN_RPC_NT_SEC_PKG_ERROR = 0xC0020057,
  MD_NTSTATUS_WIN_RPC_NT_NOT_CANCELLED = 0xC0020058,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_ASYNC_HANDLE = 0xC0020062,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_ASYNC_CALL = 0xC0020063,
  MD_NTSTATUS_WIN_RPC_NT_PROXY_ACCESS_DENIED = 0xC0020064,
  MD_NTSTATUS_WIN_RPC_NT_COOKIE_AUTH_FAILED = 0xC0020065,
  MD_NTSTATUS_WIN_RPC_NT_NO_MORE_ENTRIES = 0xC0030001,
  MD_NTSTATUS_WIN_RPC_NT_SS_CHAR_TRANS_OPEN_FAIL = 0xC0030002,
  MD_NTSTATUS_WIN_RPC_NT_SS_CHAR_TRANS_SHORT_FILE = 0xC0030003,
  MD_NTSTATUS_WIN_RPC_NT_SS_IN_NULL_CONTEXT = 0xC0030004,
  MD_NTSTATUS_WIN_RPC_NT_SS_CONTEXT_MISMATCH = 0xC0030005,
  MD_NTSTATUS_WIN_RPC_NT_SS_CONTEXT_DAMAGED = 0xC0030006,
  MD_NTSTATUS_WIN_RPC_NT_SS_HANDLES_MISMATCH = 0xC0030007,
  MD_NTSTATUS_WIN_RPC_NT_SS_CANNOT_GET_CALL_HANDLE = 0xC0030008,
  MD_NTSTATUS_WIN_RPC_NT_NULL_REF_POINTER = 0xC0030009,
  MD_NTSTATUS_WIN_RPC_NT_ENUM_VALUE_OUT_OF_RANGE = 0xC003000A,
  MD_NTSTATUS_WIN_RPC_NT_BYTE_COUNT_TOO_SMALL = 0xC003000B,
  MD_NTSTATUS_WIN_RPC_NT_BAD_STUB_DATA = 0xC003000C,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_ES_ACTION = 0xC0030059,
  MD_NTSTATUS_WIN_RPC_NT_WRONG_ES_VERSION = 0xC003005A,
  MD_NTSTATUS_WIN_RPC_NT_WRONG_STUB_VERSION = 0xC003005B,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_PIPE_OBJECT = 0xC003005C,
  MD_NTSTATUS_WIN_RPC_NT_INVALID_PIPE_OPERATION = 0xC003005D,
  MD_NTSTATUS_WIN_RPC_NT_WRONG_PIPE_VERSION = 0xC003005E,
  MD_NTSTATUS_WIN_RPC_NT_PIPE_CLOSED = 0xC003005F,
  MD_NTSTATUS_WIN_RPC_NT_PIPE_DISCIPLINE_ERROR = 0xC0030060,
  MD_NTSTATUS_WIN_RPC_NT_PIPE_EMPTY = 0xC0030061,
  MD_NTSTATUS_WIN_STATUS_PNP_BAD_MPS_TABLE = 0xC0040035,
  MD_NTSTATUS_WIN_STATUS_PNP_TRANSLATION_FAILED = 0xC0040036,
  MD_NTSTATUS_WIN_STATUS_PNP_IRQ_TRANSLATION_FAILED = 0xC0040037,
  MD_NTSTATUS_WIN_STATUS_PNP_INVALID_ID = 0xC0040038,
  MD_NTSTATUS_WIN_STATUS_IO_REISSUE_AS_CACHED = 0xC0040039,
  MD_NTSTATUS_WIN_STATUS_CTX_WINSTATION_NAME_INVALID = 0xC00A0001,
  MD_NTSTATUS_WIN_STATUS_CTX_INVALID_PD = 0xC00A0002,
  MD_NTSTATUS_WIN_STATUS_CTX_PD_NOT_FOUND = 0xC00A0003,
  MD_NTSTATUS_WIN_STATUS_CTX_CLOSE_PENDING = 0xC00A0006,
  MD_NTSTATUS_WIN_STATUS_CTX_NO_OUTBUF = 0xC00A0007,
  MD_NTSTATUS_WIN_STATUS_CTX_MODEM_INF_NOT_FOUND = 0xC00A0008,
  MD_NTSTATUS_WIN_STATUS_CTX_INVALID_MODEMNAME = 0xC00A0009,
  MD_NTSTATUS_WIN_STATUS_CTX_RESPONSE_ERROR = 0xC00A000A,
  MD_NTSTATUS_WIN_STATUS_CTX_MODEM_RESPONSE_TIMEOUT = 0xC00A000B,
  MD_NTSTATUS_WIN_STATUS_CTX_MODEM_RESPONSE_NO_CARRIER = 0xC00A000C,
  MD_NTSTATUS_WIN_STATUS_CTX_MODEM_RESPONSE_NO_DIALTONE = 0xC00A000D,
  MD_NTSTATUS_WIN_STATUS_CTX_MODEM_RESPONSE_BUSY = 0xC00A000E,
  MD_NTSTATUS_WIN_STATUS_CTX_MODEM_RESPONSE_VOICE = 0xC00A000F,
  MD_NTSTATUS_WIN_STATUS_CTX_TD_ERROR = 0xC00A0010,
  MD_NTSTATUS_WIN_STATUS_CTX_LICENSE_CLIENT_INVALID = 0xC00A0012,
  MD_NTSTATUS_WIN_STATUS_CTX_LICENSE_NOT_AVAILABLE = 0xC00A0013,
  MD_NTSTATUS_WIN_STATUS_CTX_LICENSE_EXPIRED = 0xC00A0014,
  MD_NTSTATUS_WIN_STATUS_CTX_WINSTATION_NOT_FOUND = 0xC00A0015,
  MD_NTSTATUS_WIN_STATUS_CTX_WINSTATION_NAME_COLLISION = 0xC00A0016,
  MD_NTSTATUS_WIN_STATUS_CTX_WINSTATION_BUSY = 0xC00A0017,
  MD_NTSTATUS_WIN_STATUS_CTX_BAD_VIDEO_MODE = 0xC00A0018,
  MD_NTSTATUS_WIN_STATUS_CTX_GRAPHICS_INVALID = 0xC00A0022,
  MD_NTSTATUS_WIN_STATUS_CTX_NOT_CONSOLE = 0xC00A0024,
  MD_NTSTATUS_WIN_STATUS_CTX_CLIENT_QUERY_TIMEOUT = 0xC00A0026,
  MD_NTSTATUS_WIN_STATUS_CTX_CONSOLE_DISCONNECT = 0xC00A0027,
  MD_NTSTATUS_WIN_STATUS_CTX_CONSOLE_CONNECT = 0xC00A0028,
  MD_NTSTATUS_WIN_STATUS_CTX_SHADOW_DENIED = 0xC00A002A,
  MD_NTSTATUS_WIN_STATUS_CTX_WINSTATION_ACCESS_DENIED = 0xC00A002B,
  MD_NTSTATUS_WIN_STATUS_CTX_INVALID_WD = 0xC00A002E,
  MD_NTSTATUS_WIN_STATUS_CTX_WD_NOT_FOUND = 0xC00A002F,
  MD_NTSTATUS_WIN_STATUS_CTX_SHADOW_INVALID = 0xC00A0030,
  MD_NTSTATUS_WIN_STATUS_CTX_SHADOW_DISABLED = 0xC00A0031,
  MD_NTSTATUS_WIN_STATUS_RDP_PROTOCOL_ERROR = 0xC00A0032,
  MD_NTSTATUS_WIN_STATUS_CTX_CLIENT_LICENSE_NOT_SET = 0xC00A0033,
  MD_NTSTATUS_WIN_STATUS_CTX_CLIENT_LICENSE_IN_USE = 0xC00A0034,
  MD_NTSTATUS_WIN_STATUS_CTX_SHADOW_ENDED_BY_MODE_CHANGE = 0xC00A0035,
  MD_NTSTATUS_WIN_STATUS_CTX_SHADOW_NOT_RUNNING = 0xC00A0036,
  MD_NTSTATUS_WIN_STATUS_CTX_LOGON_DISABLED = 0xC00A0037,
  MD_NTSTATUS_WIN_STATUS_CTX_SECURITY_LAYER_ERROR = 0xC00A0038,
  MD_NTSTATUS_WIN_STATUS_TS_INCOMPATIBLE_SESSIONS = 0xC00A0039,
  MD_NTSTATUS_WIN_STATUS_TS_VIDEO_SUBSYSTEM_ERROR = 0xC00A003A,
  MD_NTSTATUS_WIN_STATUS_MUI_FILE_NOT_FOUND = 0xC00B0001,
  MD_NTSTATUS_WIN_STATUS_MUI_INVALID_FILE = 0xC00B0002,
  MD_NTSTATUS_WIN_STATUS_MUI_INVALID_RC_CONFIG = 0xC00B0003,
  MD_NTSTATUS_WIN_STATUS_MUI_INVALID_LOCALE_NAME = 0xC00B0004,
  MD_NTSTATUS_WIN_STATUS_MUI_INVALID_ULTIMATEFALLBACK_NAME = 0xC00B0005,
  MD_NTSTATUS_WIN_STATUS_MUI_FILE_NOT_LOADED = 0xC00B0006,
  MD_NTSTATUS_WIN_STATUS_RESOURCE_ENUM_USER_STOP = 0xC00B0007,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_INVALID_NODE = 0xC0130001,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_EXISTS = 0xC0130002,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_JOIN_IN_PROGRESS = 0xC0130003,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_NOT_FOUND = 0xC0130004,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_LOCAL_NODE_NOT_FOUND = 0xC0130005,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NETWORK_EXISTS = 0xC0130006,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NETWORK_NOT_FOUND = 0xC0130007,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NETINTERFACE_EXISTS = 0xC0130008,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NETINTERFACE_NOT_FOUND = 0xC0130009,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_INVALID_REQUEST = 0xC013000A,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_INVALID_NETWORK_PROVIDER = 0xC013000B,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_DOWN = 0xC013000C,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_UNREACHABLE = 0xC013000D,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_NOT_MEMBER = 0xC013000E,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_JOIN_NOT_IN_PROGRESS = 0xC013000F,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_INVALID_NETWORK = 0xC0130010,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NO_NET_ADAPTERS = 0xC0130011,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_UP = 0xC0130012,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_PAUSED = 0xC0130013,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NODE_NOT_PAUSED = 0xC0130014,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NO_SECURITY_CONTEXT = 0xC0130015,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NETWORK_NOT_INTERNAL = 0xC0130016,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_POISONED = 0xC0130017,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_NON_CSV_PATH = 0xC0130018,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_VOLUME_NOT_LOCAL = 0xC0130019,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_READ_OPLOCK_BREAK_IN_PROGRESS = 0xC0130020,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_AUTO_PAUSE_ERROR = 0xC0130021,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_REDIRECTED = 0xC0130022,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_NOT_REDIRECTED = 0xC0130023,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_VOLUME_DRAINING = 0xC0130024,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_SNAPSHOT_CREATION_IN_PROGRESS = 0xC0130025,
  MD_NTSTATUS_WIN_STATUS_CLUSTER_CSV_VOLUME_DRAINING_SUCCEEDED_DOWNLEVEL = 0xC0130026,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_OPCODE = 0xC0140001,
  MD_NTSTATUS_WIN_STATUS_ACPI_STACK_OVERFLOW = 0xC0140002,
  MD_NTSTATUS_WIN_STATUS_ACPI_ASSERT_FAILED = 0xC0140003,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_INDEX = 0xC0140004,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_ARGUMENT = 0xC0140005,
  MD_NTSTATUS_WIN_STATUS_ACPI_FATAL = 0xC0140006,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_SUPERNAME = 0xC0140007,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_ARGTYPE = 0xC0140008,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_OBJTYPE = 0xC0140009,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_TARGETTYPE = 0xC014000A,
  MD_NTSTATUS_WIN_STATUS_ACPI_INCORRECT_ARGUMENT_COUNT = 0xC014000B,
  MD_NTSTATUS_WIN_STATUS_ACPI_ADDRESS_NOT_MAPPED = 0xC014000C,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_EVENTTYPE = 0xC014000D,
  MD_NTSTATUS_WIN_STATUS_ACPI_HANDLER_COLLISION = 0xC014000E,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_DATA = 0xC014000F,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_REGION = 0xC0140010,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_ACCESS_SIZE = 0xC0140011,
  MD_NTSTATUS_WIN_STATUS_ACPI_ACQUIRE_GLOBAL_LOCK = 0xC0140012,
  MD_NTSTATUS_WIN_STATUS_ACPI_ALREADY_INITIALIZED = 0xC0140013,
  MD_NTSTATUS_WIN_STATUS_ACPI_NOT_INITIALIZED = 0xC0140014,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_MUTEX_LEVEL = 0xC0140015,
  MD_NTSTATUS_WIN_STATUS_ACPI_MUTEX_NOT_OWNED = 0xC0140016,
  MD_NTSTATUS_WIN_STATUS_ACPI_MUTEX_NOT_OWNER = 0xC0140017,
  MD_NTSTATUS_WIN_STATUS_ACPI_RS_ACCESS = 0xC0140018,
  MD_NTSTATUS_WIN_STATUS_ACPI_INVALID_TABLE = 0xC0140019,
  MD_NTSTATUS_WIN_STATUS_ACPI_REG_HANDLER_FAILED = 0xC0140020,
  MD_NTSTATUS_WIN_STATUS_ACPI_POWER_REQUEST_FAILED = 0xC0140021,
  MD_NTSTATUS_WIN_STATUS_SXS_SECTION_NOT_FOUND = 0xC0150001,
  MD_NTSTATUS_WIN_STATUS_SXS_CANT_GEN_ACTCTX = 0xC0150002,
  MD_NTSTATUS_WIN_STATUS_SXS_INVALID_ACTCTXDATA_FORMAT = 0xC0150003,
  MD_NTSTATUS_WIN_STATUS_SXS_ASSEMBLY_NOT_FOUND = 0xC0150004,
  MD_NTSTATUS_WIN_STATUS_SXS_MANIFEST_FORMAT_ERROR = 0xC0150005,
  MD_NTSTATUS_WIN_STATUS_SXS_MANIFEST_PARSE_ERROR = 0xC0150006,
  MD_NTSTATUS_WIN_STATUS_SXS_ACTIVATION_CONTEXT_DISABLED = 0xC0150007,
  MD_NTSTATUS_WIN_STATUS_SXS_KEY_NOT_FOUND = 0xC0150008,
  MD_NTSTATUS_WIN_STATUS_SXS_VERSION_CONFLICT = 0xC0150009,
  MD_NTSTATUS_WIN_STATUS_SXS_WRONG_SECTION_TYPE = 0xC015000A,
  MD_NTSTATUS_WIN_STATUS_SXS_THREAD_QUERIES_DISABLED = 0xC015000B,
  MD_NTSTATUS_WIN_STATUS_SXS_ASSEMBLY_MISSING = 0xC015000C,
  MD_NTSTATUS_WIN_STATUS_SXS_PROCESS_DEFAULT_ALREADY_SET = 0xC015000E,
  MD_NTSTATUS_WIN_STATUS_SXS_EARLY_DEACTIVATION = 0xC015000F,
  MD_NTSTATUS_WIN_STATUS_SXS_INVALID_DEACTIVATION = 0xC0150010,
  MD_NTSTATUS_WIN_STATUS_SXS_MULTIPLE_DEACTIVATION = 0xC0150011,
  MD_NTSTATUS_WIN_STATUS_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY = 0xC0150012,
  MD_NTSTATUS_WIN_STATUS_SXS_PROCESS_TERMINATION_REQUESTED = 0xC0150013,
  MD_NTSTATUS_WIN_STATUS_SXS_CORRUPT_ACTIVATION_STACK = 0xC0150014,
  MD_NTSTATUS_WIN_STATUS_SXS_CORRUPTION = 0xC0150015,
  MD_NTSTATUS_WIN_STATUS_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE = 0xC0150016,
  MD_NTSTATUS_WIN_STATUS_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME = 0xC0150017,
  MD_NTSTATUS_WIN_STATUS_SXS_IDENTITY_DUPLICATE_ATTRIBUTE = 0xC0150018,
  MD_NTSTATUS_WIN_STATUS_SXS_IDENTITY_PARSE_ERROR = 0xC0150019,
  MD_NTSTATUS_WIN_STATUS_SXS_COMPONENT_STORE_CORRUPT = 0xC015001A,
  MD_NTSTATUS_WIN_STATUS_SXS_FILE_HASH_MISMATCH = 0xC015001B,
  MD_NTSTATUS_WIN_STATUS_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT = 0xC015001C,
  MD_NTSTATUS_WIN_STATUS_SXS_IDENTITIES_DIFFERENT = 0xC015001D,
  MD_NTSTATUS_WIN_STATUS_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT = 0xC015001E,
  MD_NTSTATUS_WIN_STATUS_SXS_FILE_NOT_PART_OF_ASSEMBLY = 0xC015001F,
  MD_NTSTATUS_WIN_STATUS_ADVANCED_INSTALLER_FAILED = 0xC0150020,
  MD_NTSTATUS_WIN_STATUS_XML_ENCODING_MISMATCH = 0xC0150021,
  MD_NTSTATUS_WIN_STATUS_SXS_MANIFEST_TOO_BIG = 0xC0150022,
  MD_NTSTATUS_WIN_STATUS_SXS_SETTING_NOT_REGISTERED = 0xC0150023,
  MD_NTSTATUS_WIN_STATUS_SXS_TRANSACTION_CLOSURE_INCOMPLETE = 0xC0150024,
  MD_NTSTATUS_WIN_STATUS_SMI_PRIMITIVE_INSTALLER_FAILED = 0xC0150025,
  MD_NTSTATUS_WIN_STATUS_GENERIC_COMMAND_FAILED = 0xC0150026,
  MD_NTSTATUS_WIN_STATUS_SXS_FILE_HASH_MISSING = 0xC0150027,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONAL_CONFLICT = 0xC0190001,
  MD_NTSTATUS_WIN_STATUS_INVALID_TRANSACTION = 0xC0190002,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NOT_ACTIVE = 0xC0190003,
  MD_NTSTATUS_WIN_STATUS_TM_INITIALIZATION_FAILED = 0xC0190004,
  MD_NTSTATUS_WIN_STATUS_RM_NOT_ACTIVE = 0xC0190005,
  MD_NTSTATUS_WIN_STATUS_RM_METADATA_CORRUPT = 0xC0190006,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NOT_JOINED = 0xC0190007,
  MD_NTSTATUS_WIN_STATUS_DIRECTORY_NOT_RM = 0xC0190008,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONS_UNSUPPORTED_REMOTE = 0xC019000A,
  MD_NTSTATUS_WIN_STATUS_LOG_RESIZE_INVALID_SIZE = 0xC019000B,
  MD_NTSTATUS_WIN_STATUS_REMOTE_FILE_VERSION_MISMATCH = 0xC019000C,
  MD_NTSTATUS_WIN_STATUS_CRM_PROTOCOL_ALREADY_EXISTS = 0xC019000F,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_PROPAGATION_FAILED = 0xC0190010,
  MD_NTSTATUS_WIN_STATUS_CRM_PROTOCOL_NOT_FOUND = 0xC0190011,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_SUPERIOR_EXISTS = 0xC0190012,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_REQUEST_NOT_VALID = 0xC0190013,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NOT_REQUESTED = 0xC0190014,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_ALREADY_ABORTED = 0xC0190015,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_ALREADY_COMMITTED = 0xC0190016,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_INVALID_MARSHALL_BUFFER = 0xC0190017,
  MD_NTSTATUS_WIN_STATUS_CURRENT_TRANSACTION_NOT_VALID = 0xC0190018,
  MD_NTSTATUS_WIN_STATUS_LOG_GROWTH_FAILED = 0xC0190019,
  MD_NTSTATUS_WIN_STATUS_OBJECT_NO_LONGER_EXISTS = 0xC0190021,
  MD_NTSTATUS_WIN_STATUS_STREAM_MINIVERSION_NOT_FOUND = 0xC0190022,
  MD_NTSTATUS_WIN_STATUS_STREAM_MINIVERSION_NOT_VALID = 0xC0190023,
  MD_NTSTATUS_WIN_STATUS_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION = 0xC0190024,
  MD_NTSTATUS_WIN_STATUS_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT = 0xC0190025,
  MD_NTSTATUS_WIN_STATUS_CANT_CREATE_MORE_STREAM_MINIVERSIONS = 0xC0190026,
  MD_NTSTATUS_WIN_STATUS_HANDLE_NO_LONGER_VALID = 0xC0190028,
  MD_NTSTATUS_WIN_STATUS_LOG_CORRUPTION_DETECTED = 0xC0190030,
  MD_NTSTATUS_WIN_STATUS_RM_DISCONNECTED = 0xC0190032,
  MD_NTSTATUS_WIN_STATUS_ENLISTMENT_NOT_SUPERIOR = 0xC0190033,
  MD_NTSTATUS_WIN_STATUS_FILE_IDENTITY_NOT_PERSISTENT = 0xC0190036,
  MD_NTSTATUS_WIN_STATUS_CANT_BREAK_TRANSACTIONAL_DEPENDENCY = 0xC0190037,
  MD_NTSTATUS_WIN_STATUS_CANT_CROSS_RM_BOUNDARY = 0xC0190038,
  MD_NTSTATUS_WIN_STATUS_TXF_DIR_NOT_EMPTY = 0xC0190039,
  MD_NTSTATUS_WIN_STATUS_INDOUBT_TRANSACTIONS_EXIST = 0xC019003A,
  MD_NTSTATUS_WIN_STATUS_TM_VOLATILE = 0xC019003B,
  MD_NTSTATUS_WIN_STATUS_ROLLBACK_TIMER_EXPIRED = 0xC019003C,
  MD_NTSTATUS_WIN_STATUS_TXF_ATTRIBUTE_CORRUPT = 0xC019003D,
  MD_NTSTATUS_WIN_STATUS_EFS_NOT_ALLOWED_IN_TRANSACTION = 0xC019003E,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONAL_OPEN_NOT_ALLOWED = 0xC019003F,
  MD_NTSTATUS_WIN_STATUS_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE = 0xC0190040,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_REQUIRED_PROMOTION = 0xC0190043,
  MD_NTSTATUS_WIN_STATUS_CANNOT_EXECUTE_FILE_IN_TRANSACTION = 0xC0190044,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONS_NOT_FROZEN = 0xC0190045,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_FREEZE_IN_PROGRESS = 0xC0190046,
  MD_NTSTATUS_WIN_STATUS_NOT_SNAPSHOT_VOLUME = 0xC0190047,
  MD_NTSTATUS_WIN_STATUS_NO_SAVEPOINT_WITH_OPEN_FILES = 0xC0190048,
  MD_NTSTATUS_WIN_STATUS_SPARSE_NOT_ALLOWED_IN_TRANSACTION = 0xC0190049,
  MD_NTSTATUS_WIN_STATUS_TM_IDENTITY_MISMATCH = 0xC019004A,
  MD_NTSTATUS_WIN_STATUS_FLOATED_SECTION = 0xC019004B,
  MD_NTSTATUS_WIN_STATUS_CANNOT_ACCEPT_TRANSACTED_WORK = 0xC019004C,
  MD_NTSTATUS_WIN_STATUS_CANNOT_ABORT_TRANSACTIONS = 0xC019004D,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NOT_FOUND = 0xC019004E,
  MD_NTSTATUS_WIN_STATUS_RESOURCEMANAGER_NOT_FOUND = 0xC019004F,
  MD_NTSTATUS_WIN_STATUS_ENLISTMENT_NOT_FOUND = 0xC0190050,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONMANAGER_NOT_FOUND = 0xC0190051,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONMANAGER_NOT_ONLINE = 0xC0190052,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION = 0xC0190053,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NOT_ROOT = 0xC0190054,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_OBJECT_EXPIRED = 0xC0190055,
  MD_NTSTATUS_WIN_STATUS_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION = 0xC0190056,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_RESPONSE_NOT_ENLISTED = 0xC0190057,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_RECORD_TOO_LONG = 0xC0190058,
  MD_NTSTATUS_WIN_STATUS_NO_LINK_TRACKING_IN_TRANSACTION = 0xC0190059,
  MD_NTSTATUS_WIN_STATUS_OPERATION_NOT_SUPPORTED_IN_TRANSACTION = 0xC019005A,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_INTEGRITY_VIOLATED = 0xC019005B,
  MD_NTSTATUS_WIN_STATUS_TRANSACTIONMANAGER_IDENTITY_MISMATCH = 0xC019005C,
  MD_NTSTATUS_WIN_STATUS_RM_CANNOT_BE_FROZEN_FOR_SNAPSHOT = 0xC019005D,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_MUST_WRITETHROUGH = 0xC019005E,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NO_SUPERIOR = 0xC019005F,
  MD_NTSTATUS_WIN_STATUS_EXPIRED_HANDLE = 0xC0190060,
  MD_NTSTATUS_WIN_STATUS_TRANSACTION_NOT_ENLISTED = 0xC0190061,
  MD_NTSTATUS_WIN_STATUS_LOG_SECTOR_INVALID = 0xC01A0001,
  MD_NTSTATUS_WIN_STATUS_LOG_SECTOR_PARITY_INVALID = 0xC01A0002,
  MD_NTSTATUS_WIN_STATUS_LOG_SECTOR_REMAPPED = 0xC01A0003,
  MD_NTSTATUS_WIN_STATUS_LOG_BLOCK_INCOMPLETE = 0xC01A0004,
  MD_NTSTATUS_WIN_STATUS_LOG_INVALID_RANGE = 0xC01A0005,
  MD_NTSTATUS_WIN_STATUS_LOG_BLOCKS_EXHAUSTED = 0xC01A0006,
  MD_NTSTATUS_WIN_STATUS_LOG_READ_CONTEXT_INVALID = 0xC01A0007,
  MD_NTSTATUS_WIN_STATUS_LOG_RESTART_INVALID = 0xC01A0008,
  MD_NTSTATUS_WIN_STATUS_LOG_BLOCK_VERSION = 0xC01A0009,
  MD_NTSTATUS_WIN_STATUS_LOG_BLOCK_INVALID = 0xC01A000A,
  MD_NTSTATUS_WIN_STATUS_LOG_READ_MODE_INVALID = 0xC01A000B,
  MD_NTSTATUS_WIN_STATUS_LOG_METADATA_CORRUPT = 0xC01A000D,
  MD_NTSTATUS_WIN_STATUS_LOG_METADATA_INVALID = 0xC01A000E,
  MD_NTSTATUS_WIN_STATUS_LOG_METADATA_INCONSISTENT = 0xC01A000F,
  MD_NTSTATUS_WIN_STATUS_LOG_RESERVATION_INVALID = 0xC01A0010,
  MD_NTSTATUS_WIN_STATUS_LOG_CANT_DELETE = 0xC01A0011,
  MD_NTSTATUS_WIN_STATUS_LOG_CONTAINER_LIMIT_EXCEEDED = 0xC01A0012,
  MD_NTSTATUS_WIN_STATUS_LOG_START_OF_LOG = 0xC01A0013,
  MD_NTSTATUS_WIN_STATUS_LOG_POLICY_ALREADY_INSTALLED = 0xC01A0014,
  MD_NTSTATUS_WIN_STATUS_LOG_POLICY_NOT_INSTALLED = 0xC01A0015,
  MD_NTSTATUS_WIN_STATUS_LOG_POLICY_INVALID = 0xC01A0016,
  MD_NTSTATUS_WIN_STATUS_LOG_POLICY_CONFLICT = 0xC01A0017,
  MD_NTSTATUS_WIN_STATUS_LOG_PINNED_ARCHIVE_TAIL = 0xC01A0018,
  MD_NTSTATUS_WIN_STATUS_LOG_RECORD_NONEXISTENT = 0xC01A0019,
  MD_NTSTATUS_WIN_STATUS_LOG_RECORDS_RESERVED_INVALID = 0xC01A001A,
  MD_NTSTATUS_WIN_STATUS_LOG_SPACE_RESERVED_INVALID = 0xC01A001B,
  MD_NTSTATUS_WIN_STATUS_LOG_TAIL_INVALID = 0xC01A001C,
  MD_NTSTATUS_WIN_STATUS_LOG_FULL = 0xC01A001D,
  MD_NTSTATUS_WIN_STATUS_LOG_MULTIPLEXED = 0xC01A001E,
  MD_NTSTATUS_WIN_STATUS_LOG_DEDICATED = 0xC01A001F,
  MD_NTSTATUS_WIN_STATUS_LOG_ARCHIVE_NOT_IN_PROGRESS = 0xC01A0020,
  MD_NTSTATUS_WIN_STATUS_LOG_ARCHIVE_IN_PROGRESS = 0xC01A0021,
  MD_NTSTATUS_WIN_STATUS_LOG_EPHEMERAL = 0xC01A0022,
  MD_NTSTATUS_WIN_STATUS_LOG_NOT_ENOUGH_CONTAINERS = 0xC01A0023,
  MD_NTSTATUS_WIN_STATUS_LOG_CLIENT_ALREADY_REGISTERED = 0xC01A0024,
  MD_NTSTATUS_WIN_STATUS_LOG_CLIENT_NOT_REGISTERED = 0xC01A0025,
  MD_NTSTATUS_WIN_STATUS_LOG_FULL_HANDLER_IN_PROGRESS = 0xC01A0026,
  MD_NTSTATUS_WIN_STATUS_LOG_CONTAINER_READ_FAILED = 0xC01A0027,
  MD_NTSTATUS_WIN_STATUS_LOG_CONTAINER_WRITE_FAILED = 0xC01A0028,
  MD_NTSTATUS_WIN_STATUS_LOG_CONTAINER_OPEN_FAILED = 0xC01A0029,
  MD_NTSTATUS_WIN_STATUS_LOG_CONTAINER_STATE_INVALID = 0xC01A002A,
  MD_NTSTATUS_WIN_STATUS_LOG_STATE_INVALID = 0xC01A002B,
  MD_NTSTATUS_WIN_STATUS_LOG_PINNED = 0xC01A002C,
  MD_NTSTATUS_WIN_STATUS_LOG_METADATA_FLUSH_FAILED = 0xC01A002D,
  MD_NTSTATUS_WIN_STATUS_LOG_INCONSISTENT_SECURITY = 0xC01A002E,
  MD_NTSTATUS_WIN_STATUS_LOG_APPENDED_FLUSH_FAILED = 0xC01A002F,
  MD_NTSTATUS_WIN_STATUS_LOG_PINNED_RESERVATION = 0xC01A0030,
  MD_NTSTATUS_WIN_STATUS_VIDEO_HUNG_DISPLAY_DRIVER_THREAD = 0xC01B00EA,
  MD_NTSTATUS_WIN_STATUS_FLT_NO_HANDLER_DEFINED = 0xC01C0001,
  MD_NTSTATUS_WIN_STATUS_FLT_CONTEXT_ALREADY_DEFINED = 0xC01C0002,
  MD_NTSTATUS_WIN_STATUS_FLT_INVALID_ASYNCHRONOUS_REQUEST = 0xC01C0003,
  MD_NTSTATUS_WIN_STATUS_FLT_DISALLOW_FAST_IO = 0xC01C0004,
  MD_NTSTATUS_WIN_STATUS_FLT_INVALID_NAME_REQUEST = 0xC01C0005,
  MD_NTSTATUS_WIN_STATUS_FLT_NOT_SAFE_TO_POST_OPERATION = 0xC01C0006,
  MD_NTSTATUS_WIN_STATUS_FLT_NOT_INITIALIZED = 0xC01C0007,
  MD_NTSTATUS_WIN_STATUS_FLT_FILTER_NOT_READY = 0xC01C0008,
  MD_NTSTATUS_WIN_STATUS_FLT_POST_OPERATION_CLEANUP = 0xC01C0009,
  MD_NTSTATUS_WIN_STATUS_FLT_INTERNAL_ERROR = 0xC01C000A,
  MD_NTSTATUS_WIN_STATUS_FLT_DELETING_OBJECT = 0xC01C000B,
  MD_NTSTATUS_WIN_STATUS_FLT_MUST_BE_NONPAGED_POOL = 0xC01C000C,
  MD_NTSTATUS_WIN_STATUS_FLT_DUPLICATE_ENTRY = 0xC01C000D,
  MD_NTSTATUS_WIN_STATUS_FLT_CBDQ_DISABLED = 0xC01C000E,
  MD_NTSTATUS_WIN_STATUS_FLT_DO_NOT_ATTACH = 0xC01C000F,
  MD_NTSTATUS_WIN_STATUS_FLT_DO_NOT_DETACH = 0xC01C0010,
  MD_NTSTATUS_WIN_STATUS_FLT_INSTANCE_ALTITUDE_COLLISION = 0xC01C0011,
  MD_NTSTATUS_WIN_STATUS_FLT_INSTANCE_NAME_COLLISION = 0xC01C0012,
  MD_NTSTATUS_WIN_STATUS_FLT_FILTER_NOT_FOUND = 0xC01C0013,
  MD_NTSTATUS_WIN_STATUS_FLT_VOLUME_NOT_FOUND = 0xC01C0014,
  MD_NTSTATUS_WIN_STATUS_FLT_INSTANCE_NOT_FOUND = 0xC01C0015,
  MD_NTSTATUS_WIN_STATUS_FLT_CONTEXT_ALLOCATION_NOT_FOUND = 0xC01C0016,
  MD_NTSTATUS_WIN_STATUS_FLT_INVALID_CONTEXT_REGISTRATION = 0xC01C0017,
  MD_NTSTATUS_WIN_STATUS_FLT_NAME_CACHE_MISS = 0xC01C0018,
  MD_NTSTATUS_WIN_STATUS_FLT_NO_DEVICE_OBJECT = 0xC01C0019,
  MD_NTSTATUS_WIN_STATUS_FLT_VOLUME_ALREADY_MOUNTED = 0xC01C001A,
  MD_NTSTATUS_WIN_STATUS_FLT_ALREADY_ENLISTED = 0xC01C001B,
  MD_NTSTATUS_WIN_STATUS_FLT_CONTEXT_ALREADY_LINKED = 0xC01C001C,
  MD_NTSTATUS_WIN_STATUS_FLT_NO_WAITER_FOR_REPLY = 0xC01C0020,
  MD_NTSTATUS_WIN_STATUS_FLT_REGISTRATION_BUSY = 0xC01C0023,
  MD_NTSTATUS_WIN_STATUS_MONITOR_NO_DESCRIPTOR = 0xC01D0001,
  MD_NTSTATUS_WIN_STATUS_MONITOR_UNKNOWN_DESCRIPTOR_FORMAT = 0xC01D0002,
  MD_NTSTATUS_WIN_STATUS_MONITOR_INVALID_DESCRIPTOR_CHECKSUM = 0xC01D0003,
  MD_NTSTATUS_WIN_STATUS_MONITOR_INVALID_STANDARD_TIMING_BLOCK = 0xC01D0004,
  MD_NTSTATUS_WIN_STATUS_MONITOR_WMI_DATABLOCK_REGISTRATION_FAILED = 0xC01D0005,
  MD_NTSTATUS_WIN_STATUS_MONITOR_INVALID_SERIAL_NUMBER_MONDSC_BLOCK = 0xC01D0006,
  MD_NTSTATUS_WIN_STATUS_MONITOR_INVALID_USER_FRIENDLY_MONDSC_BLOCK = 0xC01D0007,
  MD_NTSTATUS_WIN_STATUS_MONITOR_NO_MORE_DESCRIPTOR_DATA = 0xC01D0008,
  MD_NTSTATUS_WIN_STATUS_MONITOR_INVALID_DETAILED_TIMING_BLOCK = 0xC01D0009,
  MD_NTSTATUS_WIN_STATUS_MONITOR_INVALID_MANUFACTURE_DATE = 0xC01D000A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NOT_EXCLUSIVE_MODE_OWNER = 0xC01E0000,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INSUFFICIENT_DMA_BUFFER = 0xC01E0001,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_DISPLAY_ADAPTER = 0xC01E0002,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ADAPTER_WAS_RESET = 0xC01E0003,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_DRIVER_MODEL = 0xC01E0004,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PRESENT_MODE_CHANGED = 0xC01E0005,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PRESENT_OCCLUDED = 0xC01E0006,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PRESENT_DENIED = 0xC01E0007,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CANNOTCOLORCONVERT = 0xC01E0008,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DRIVER_MISMATCH = 0xC01E0009,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PRESENT_REDIRECTION_DISABLED = 0xC01E000B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PRESENT_UNOCCLUDED = 0xC01E000C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_WINDOWDC_NOT_AVAILABLE = 0xC01E000D,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_WINDOWLESS_PRESENT_DISABLED = 0xC01E000E,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_VIDEO_MEMORY = 0xC01E0100,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CANT_LOCK_MEMORY = 0xC01E0101,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ALLOCATION_BUSY = 0xC01E0102,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_TOO_MANY_REFERENCES = 0xC01E0103,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_TRY_AGAIN_LATER = 0xC01E0104,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_TRY_AGAIN_NOW = 0xC01E0105,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ALLOCATION_INVALID = 0xC01E0106,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_UNSWIZZLING_APERTURE_UNAVAILABLE = 0xC01E0107,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_UNSWIZZLING_APERTURE_UNSUPPORTED = 0xC01E0108,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CANT_EVICT_PINNED_ALLOCATION = 0xC01E0109,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_ALLOCATION_USAGE = 0xC01E0110,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CANT_RENDER_LOCKED_ALLOCATION = 0xC01E0111,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ALLOCATION_CLOSED = 0xC01E0112,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_ALLOCATION_INSTANCE = 0xC01E0113,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_ALLOCATION_HANDLE = 0xC01E0114,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_WRONG_ALLOCATION_DEVICE = 0xC01E0115,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ALLOCATION_CONTENT_LOST = 0xC01E0116,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_GPU_EXCEPTION_ON_DEVICE = 0xC01E0200,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDPN_TOPOLOGY = 0xC01E0300,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_VIDPN_TOPOLOGY_NOT_SUPPORTED = 0xC01E0301,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_VIDPN_TOPOLOGY_CURRENTLY_NOT_SUPPORTED = 0xC01E0302,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDPN = 0xC01E0303,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDEO_PRESENT_SOURCE = 0xC01E0304,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDEO_PRESENT_TARGET = 0xC01E0305,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_VIDPN_MODALITY_NOT_SUPPORTED = 0xC01E0306,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDPN_SOURCEMODESET = 0xC01E0308,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDPN_TARGETMODESET = 0xC01E0309,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_FREQUENCY = 0xC01E030A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_ACTIVE_REGION = 0xC01E030B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_TOTAL_REGION = 0xC01E030C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDEO_PRESENT_SOURCE_MODE = 0xC01E0310,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDEO_PRESENT_TARGET_MODE = 0xC01E0311,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PINNED_MODE_MUST_REMAIN_IN_SET = 0xC01E0312,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PATH_ALREADY_IN_TOPOLOGY = 0xC01E0313,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MODE_ALREADY_IN_MODESET = 0xC01E0314,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDEOPRESENTSOURCESET = 0xC01E0315,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDEOPRESENTTARGETSET = 0xC01E0316,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_SOURCE_ALREADY_IN_SET = 0xC01E0317,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_TARGET_ALREADY_IN_SET = 0xC01E0318,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDPN_PRESENT_PATH = 0xC01E0319,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_RECOMMENDED_VIDPN_TOPOLOGY = 0xC01E031A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGESET = 0xC01E031B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGE = 0xC01E031C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_FREQUENCYRANGE_NOT_IN_SET = 0xC01E031D,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_FREQUENCYRANGE_ALREADY_IN_SET = 0xC01E031F,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_STALE_MODESET = 0xC01E0320,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITOR_SOURCEMODESET = 0xC01E0321,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITOR_SOURCE_MODE = 0xC01E0322,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_RECOMMENDED_FUNCTIONAL_VIDPN = 0xC01E0323,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MODE_ID_MUST_BE_UNIQUE = 0xC01E0324,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_EMPTY_ADAPTER_MONITOR_MODE_SUPPORT_INTERSECTION = 0xC01E0325,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_VIDEO_PRESENT_TARGETS_LESS_THAN_SOURCES = 0xC01E0326,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PATH_NOT_IN_TOPOLOGY = 0xC01E0327,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ADAPTER_MUST_HAVE_AT_LEAST_ONE_SOURCE = 0xC01E0328,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ADAPTER_MUST_HAVE_AT_LEAST_ONE_TARGET = 0xC01E0329,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITORDESCRIPTORSET = 0xC01E032A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITORDESCRIPTOR = 0xC01E032B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MONITORDESCRIPTOR_NOT_IN_SET = 0xC01E032C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MONITORDESCRIPTOR_ALREADY_IN_SET = 0xC01E032D,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MONITORDESCRIPTOR_ID_MUST_BE_UNIQUE = 0xC01E032E,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDPN_TARGET_SUBSET_TYPE = 0xC01E032F,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_RESOURCES_NOT_RELATED = 0xC01E0330,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_SOURCE_ID_MUST_BE_UNIQUE = 0xC01E0331,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_TARGET_ID_MUST_BE_UNIQUE = 0xC01E0332,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_AVAILABLE_VIDPN_TARGET = 0xC01E0333,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MONITOR_COULD_NOT_BE_ASSOCIATED_WITH_ADAPTER = 0xC01E0334,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_VIDPNMGR = 0xC01E0335,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_ACTIVE_VIDPN = 0xC01E0336,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_STALE_VIDPN_TOPOLOGY = 0xC01E0337,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MONITOR_NOT_CONNECTED = 0xC01E0338,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_SOURCE_NOT_IN_TOPOLOGY = 0xC01E0339,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_PRIMARYSURFACE_SIZE = 0xC01E033A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VISIBLEREGION_SIZE = 0xC01E033B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_STRIDE = 0xC01E033C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_PIXELFORMAT = 0xC01E033D,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_COLORBASIS = 0xC01E033E,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_PIXELVALUEACCESSMODE = 0xC01E033F,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_TARGET_NOT_IN_TOPOLOGY = 0xC01E0340,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_DISPLAY_MODE_MANAGEMENT_SUPPORT = 0xC01E0341,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE = 0xC01E0342,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CANT_ACCESS_ACTIVE_VIDPN = 0xC01E0343,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_PATH_IMPORTANCE_ORDINAL = 0xC01E0344,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_PATH_CONTENT_GEOMETRY_TRANSFORMATION = 0xC01E0345,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PATH_CONTENT_GEOMETRY_TRANSFORMATION_NOT_SUPPORTED = 0xC01E0346,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_GAMMA_RAMP = 0xC01E0347,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_GAMMA_RAMP_NOT_SUPPORTED = 0xC01E0348,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MULTISAMPLING_NOT_SUPPORTED = 0xC01E0349,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MODE_NOT_IN_MODESET = 0xC01E034A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_VIDPN_TOPOLOGY_RECOMMENDATION_REASON = 0xC01E034D,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_PATH_CONTENT_TYPE = 0xC01E034E,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_COPYPROTECTION_TYPE = 0xC01E034F,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_UNASSIGNED_MODESET_ALREADY_EXISTS = 0xC01E0350,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_SCANLINE_ORDERING = 0xC01E0352,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_TOPOLOGY_CHANGES_NOT_ALLOWED = 0xC01E0353,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_AVAILABLE_IMPORTANCE_ORDINALS = 0xC01E0354,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INCOMPATIBLE_PRIVATE_FORMAT = 0xC01E0355,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MODE_PRUNING_ALGORITHM = 0xC01E0356,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITOR_CAPABILITY_ORIGIN = 0xC01E0357,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGE_CONSTRAINT = 0xC01E0358,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MAX_NUM_PATHS_REACHED = 0xC01E0359,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CANCEL_VIDPN_TOPOLOGY_AUGMENTATION = 0xC01E035A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_CLIENT_TYPE = 0xC01E035B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CLIENTVIDPN_NOT_SET = 0xC01E035C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_SPECIFIED_CHILD_ALREADY_CONNECTED = 0xC01E0400,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CHILD_DESCRIPTOR_NOT_SUPPORTED = 0xC01E0401,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NOT_A_LINKED_ADAPTER = 0xC01E0430,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_LEADLINK_NOT_ENUMERATED = 0xC01E0431,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CHAINLINKS_NOT_ENUMERATED = 0xC01E0432,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ADAPTER_CHAIN_NOT_READY = 0xC01E0433,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CHAINLINKS_NOT_STARTED = 0xC01E0434,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_CHAINLINKS_NOT_POWERED_ON = 0xC01E0435,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INCONSISTENT_DEVICE_LINK_STATE = 0xC01E0436,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NOT_POST_DEVICE_DRIVER = 0xC01E0438,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ADAPTER_ACCESS_NOT_EXCLUDED = 0xC01E043B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_NOT_SUPPORTED = 0xC01E0500,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_COPP_NOT_SUPPORTED = 0xC01E0501,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_UAB_NOT_SUPPORTED = 0xC01E0502,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_INVALID_ENCRYPTED_PARAMETERS = 0xC01E0503,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_NO_PROTECTED_OUTPUTS_EXIST = 0xC01E0505,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_INTERNAL_ERROR = 0xC01E050B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_INVALID_HANDLE = 0xC01E050C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PVP_INVALID_CERTIFICATE_LENGTH = 0xC01E050E,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_SPANNING_MODE_ENABLED = 0xC01E050F,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_THEATER_MODE_ENABLED = 0xC01E0510,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PVP_HFS_FAILED = 0xC01E0511,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_INVALID_SRM = 0xC01E0512,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_HDCP = 0xC01E0513,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_ACP = 0xC01E0514,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_CGMSA = 0xC01E0515,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_HDCP_SRM_NEVER_SET = 0xC01E0516,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_RESOLUTION_TOO_HIGH = 0xC01E0517,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_ALL_HDCP_HARDWARE_ALREADY_IN_USE = 0xC01E0518,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_PROTECTED_OUTPUT_NO_LONGER_EXISTS = 0xC01E051A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_PROTECTED_OUTPUT_DOES_NOT_HAVE_COPP_SEMANTICS = 0xC01E051C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_INVALID_INFORMATION_REQUEST = 0xC01E051D,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_DRIVER_INTERNAL_ERROR = 0xC01E051E,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_PROTECTED_OUTPUT_DOES_NOT_HAVE_OPM_SEMANTICS = 0xC01E051F,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_SIGNALING_NOT_SUPPORTED = 0xC01E0520,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_OPM_INVALID_CONFIGURATION_REQUEST = 0xC01E0521,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_I2C_NOT_SUPPORTED = 0xC01E0580,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_I2C_DEVICE_DOES_NOT_EXIST = 0xC01E0581,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_I2C_ERROR_TRANSMITTING_DATA = 0xC01E0582,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_I2C_ERROR_RECEIVING_DATA = 0xC01E0583,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DDCCI_VCP_NOT_SUPPORTED = 0xC01E0584,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DDCCI_INVALID_DATA = 0xC01E0585,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DDCCI_MONITOR_RETURNED_INVALID_TIMING_STATUS_BYTE = 0xC01E0586,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DDCCI_INVALID_CAPABILITIES_STRING = 0xC01E0587,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MCA_INTERNAL_ERROR = 0xC01E0588,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DDCCI_INVALID_MESSAGE_COMMAND = 0xC01E0589,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DDCCI_INVALID_MESSAGE_LENGTH = 0xC01E058A,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DDCCI_INVALID_MESSAGE_CHECKSUM = 0xC01E058B,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_PHYSICAL_MONITOR_HANDLE = 0xC01E058C,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MONITOR_NO_LONGER_EXISTS = 0xC01E058D,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_ONLY_CONSOLE_SESSION_SUPPORTED = 0xC01E05E0,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_DISPLAY_DEVICE_CORRESPONDS_TO_NAME = 0xC01E05E1,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_DISPLAY_DEVICE_NOT_ATTACHED_TO_DESKTOP = 0xC01E05E2,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_MIRRORING_DEVICES_NOT_SUPPORTED = 0xC01E05E3,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INVALID_POINTER = 0xC01E05E4,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_NO_MONITORS_CORRESPOND_TO_DISPLAY_DEVICE = 0xC01E05E5,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_PARAMETER_ARRAY_TOO_SMALL = 0xC01E05E6,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_INTERNAL_ERROR = 0xC01E05E7,
  MD_NTSTATUS_WIN_STATUS_GRAPHICS_SESSION_TYPE_CHANGE_IN_PROGRESS = 0xC01E05E8,
  MD_NTSTATUS_WIN_STATUS_FVE_LOCKED_VOLUME = 0xC0210000,
  MD_NTSTATUS_WIN_STATUS_FVE_NOT_ENCRYPTED = 0xC0210001,
  MD_NTSTATUS_WIN_STATUS_FVE_BAD_INFORMATION = 0xC0210002,
  MD_NTSTATUS_WIN_STATUS_FVE_TOO_SMALL = 0xC0210003,
  MD_NTSTATUS_WIN_STATUS_FVE_FAILED_WRONG_FS = 0xC0210004,
  MD_NTSTATUS_WIN_STATUS_FVE_BAD_PARTITION_SIZE = 0xC0210005,
  MD_NTSTATUS_WIN_STATUS_FVE_FS_NOT_EXTENDED = 0xC0210006,
  MD_NTSTATUS_WIN_STATUS_FVE_FS_MOUNTED = 0xC0210007,
  MD_NTSTATUS_WIN_STATUS_FVE_NO_LICENSE = 0xC0210008,
  MD_NTSTATUS_WIN_STATUS_FVE_ACTION_NOT_ALLOWED = 0xC0210009,
  MD_NTSTATUS_WIN_STATUS_FVE_BAD_DATA = 0xC021000A,
  MD_NTSTATUS_WIN_STATUS_FVE_VOLUME_NOT_BOUND = 0xC021000B,
  MD_NTSTATUS_WIN_STATUS_FVE_NOT_DATA_VOLUME = 0xC021000C,
  MD_NTSTATUS_WIN_STATUS_FVE_CONV_READ_ERROR = 0xC021000D,
  MD_NTSTATUS_WIN_STATUS_FVE_CONV_WRITE_ERROR = 0xC021000E,
  MD_NTSTATUS_WIN_STATUS_FVE_OVERLAPPED_UPDATE = 0xC021000F,
  MD_NTSTATUS_WIN_STATUS_FVE_FAILED_SECTOR_SIZE = 0xC0210010,
  MD_NTSTATUS_WIN_STATUS_FVE_FAILED_AUTHENTICATION = 0xC0210011,
  MD_NTSTATUS_WIN_STATUS_FVE_NOT_OS_VOLUME = 0xC0210012,
  MD_NTSTATUS_WIN_STATUS_FVE_KEYFILE_NOT_FOUND = 0xC0210013,
  MD_NTSTATUS_WIN_STATUS_FVE_KEYFILE_INVALID = 0xC0210014,
  MD_NTSTATUS_WIN_STATUS_FVE_KEYFILE_NO_VMK = 0xC0210015,
  MD_NTSTATUS_WIN_STATUS_FVE_TPM_DISABLED = 0xC0210016,
  MD_NTSTATUS_WIN_STATUS_FVE_TPM_SRK_AUTH_NOT_ZERO = 0xC0210017,
  MD_NTSTATUS_WIN_STATUS_FVE_TPM_INVALID_PCR = 0xC0210018,
  MD_NTSTATUS_WIN_STATUS_FVE_TPM_NO_VMK = 0xC0210019,
  MD_NTSTATUS_WIN_STATUS_FVE_PIN_INVALID = 0xC021001A,
  MD_NTSTATUS_WIN_STATUS_FVE_AUTH_INVALID_APPLICATION = 0xC021001B,
  MD_NTSTATUS_WIN_STATUS_FVE_AUTH_INVALID_CONFIG = 0xC021001C,
  MD_NTSTATUS_WIN_STATUS_FVE_DEBUGGER_ENABLED = 0xC021001D,
  MD_NTSTATUS_WIN_STATUS_FVE_DRY_RUN_FAILED = 0xC021001E,
  MD_NTSTATUS_WIN_STATUS_FVE_BAD_METADATA_POINTER = 0xC021001F,
  MD_NTSTATUS_WIN_STATUS_FVE_OLD_METADATA_COPY = 0xC0210020,
  MD_NTSTATUS_WIN_STATUS_FVE_REBOOT_REQUIRED = 0xC0210021,
  MD_NTSTATUS_WIN_STATUS_FVE_RAW_ACCESS = 0xC0210022,
  MD_NTSTATUS_WIN_STATUS_FVE_RAW_BLOCKED = 0xC0210023,
  MD_NTSTATUS_WIN_STATUS_FVE_NO_AUTOUNLOCK_MASTER_KEY = 0xC0210024,
  MD_NTSTATUS_WIN_STATUS_FVE_MOR_FAILED = 0xC0210025,
  MD_NTSTATUS_WIN_STATUS_FVE_NO_FEATURE_LICENSE = 0xC0210026,
  MD_NTSTATUS_WIN_STATUS_FVE_POLICY_USER_DISABLE_RDV_NOT_ALLOWED = 0xC0210027,
  MD_NTSTATUS_WIN_STATUS_FVE_CONV_RECOVERY_FAILED = 0xC0210028,
  MD_NTSTATUS_WIN_STATUS_FVE_VIRTUALIZED_SPACE_TOO_BIG = 0xC0210029,
  MD_NTSTATUS_WIN_STATUS_FVE_INVALID_DATUM_TYPE = 0xC021002A,
  MD_NTSTATUS_WIN_STATUS_FVE_VOLUME_TOO_SMALL = 0xC0210030,
  MD_NTSTATUS_WIN_STATUS_FVE_ENH_PIN_INVALID = 0xC0210031,
  MD_NTSTATUS_WIN_STATUS_FVE_FULL_ENCRYPTION_NOT_ALLOWED_ON_TP_STORAGE = 0xC0210032,
  MD_NTSTATUS_WIN_STATUS_FVE_WIPE_NOT_ALLOWED_ON_TP_STORAGE = 0xC0210033,
  MD_NTSTATUS_WIN_STATUS_FVE_NOT_ALLOWED_ON_CSV_STACK = 0xC0210034,
  MD_NTSTATUS_WIN_STATUS_FVE_NOT_ALLOWED_ON_CLUSTER = 0xC0210035,
  MD_NTSTATUS_WIN_STATUS_FVE_NOT_ALLOWED_TO_UPGRADE_WHILE_CONVERTING = 0xC0210036,
  MD_NTSTATUS_WIN_STATUS_FVE_WIPE_CANCEL_NOT_APPLICABLE = 0xC0210037,
  MD_NTSTATUS_WIN_STATUS_FVE_EDRIVE_DRY_RUN_FAILED = 0xC0210038,
  MD_NTSTATUS_WIN_STATUS_FVE_SECUREBOOT_DISABLED = 0xC0210039,
  MD_NTSTATUS_WIN_STATUS_FVE_SECUREBOOT_CONFIG_CHANGE = 0xC021003A,
  MD_NTSTATUS_WIN_STATUS_FVE_DEVICE_LOCKEDOUT = 0xC021003B,
  MD_NTSTATUS_WIN_STATUS_FVE_VOLUME_EXTEND_PREVENTS_EOW_DECRYPT = 0xC021003C,
  MD_NTSTATUS_WIN_STATUS_FVE_NOT_DE_VOLUME = 0xC021003D,
  MD_NTSTATUS_WIN_STATUS_FVE_PROTECTION_DISABLED = 0xC021003E,
  MD_NTSTATUS_WIN_STATUS_FVE_PROTECTION_CANNOT_BE_DISABLED = 0xC021003F,
  MD_NTSTATUS_WIN_STATUS_FWP_CALLOUT_NOT_FOUND = 0xC0220001,
  MD_NTSTATUS_WIN_STATUS_FWP_CONDITION_NOT_FOUND = 0xC0220002,
  MD_NTSTATUS_WIN_STATUS_FWP_FILTER_NOT_FOUND = 0xC0220003,
  MD_NTSTATUS_WIN_STATUS_FWP_LAYER_NOT_FOUND = 0xC0220004,
  MD_NTSTATUS_WIN_STATUS_FWP_PROVIDER_NOT_FOUND = 0xC0220005,
  MD_NTSTATUS_WIN_STATUS_FWP_PROVIDER_CONTEXT_NOT_FOUND = 0xC0220006,
  MD_NTSTATUS_WIN_STATUS_FWP_SUBLAYER_NOT_FOUND = 0xC0220007,
  MD_NTSTATUS_WIN_STATUS_FWP_NOT_FOUND = 0xC0220008,
  MD_NTSTATUS_WIN_STATUS_FWP_ALREADY_EXISTS = 0xC0220009,
  MD_NTSTATUS_WIN_STATUS_FWP_IN_USE = 0xC022000A,
  MD_NTSTATUS_WIN_STATUS_FWP_DYNAMIC_SESSION_IN_PROGRESS = 0xC022000B,
  MD_NTSTATUS_WIN_STATUS_FWP_WRONG_SESSION = 0xC022000C,
  MD_NTSTATUS_WIN_STATUS_FWP_NO_TXN_IN_PROGRESS = 0xC022000D,
  MD_NTSTATUS_WIN_STATUS_FWP_TXN_IN_PROGRESS = 0xC022000E,
  MD_NTSTATUS_WIN_STATUS_FWP_TXN_ABORTED = 0xC022000F,
  MD_NTSTATUS_WIN_STATUS_FWP_SESSION_ABORTED = 0xC0220010,
  MD_NTSTATUS_WIN_STATUS_FWP_INCOMPATIBLE_TXN = 0xC0220011,
  MD_NTSTATUS_WIN_STATUS_FWP_TIMEOUT = 0xC0220012,
  MD_NTSTATUS_WIN_STATUS_FWP_NET_EVENTS_DISABLED = 0xC0220013,
  MD_NTSTATUS_WIN_STATUS_FWP_INCOMPATIBLE_LAYER = 0xC0220014,
  MD_NTSTATUS_WIN_STATUS_FWP_KM_CLIENTS_ONLY = 0xC0220015,
  MD_NTSTATUS_WIN_STATUS_FWP_LIFETIME_MISMATCH = 0xC0220016,
  MD_NTSTATUS_WIN_STATUS_FWP_BUILTIN_OBJECT = 0xC0220017,
  MD_NTSTATUS_WIN_STATUS_FWP_TOO_MANY_CALLOUTS = 0xC0220018,
  MD_NTSTATUS_WIN_STATUS_FWP_NOTIFICATION_DROPPED = 0xC0220019,
  MD_NTSTATUS_WIN_STATUS_FWP_TRAFFIC_MISMATCH = 0xC022001A,
  MD_NTSTATUS_WIN_STATUS_FWP_INCOMPATIBLE_SA_STATE = 0xC022001B,
  MD_NTSTATUS_WIN_STATUS_FWP_NULL_POINTER = 0xC022001C,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_ENUMERATOR = 0xC022001D,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_FLAGS = 0xC022001E,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_NET_MASK = 0xC022001F,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_RANGE = 0xC0220020,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_INTERVAL = 0xC0220021,
  MD_NTSTATUS_WIN_STATUS_FWP_ZERO_LENGTH_ARRAY = 0xC0220022,
  MD_NTSTATUS_WIN_STATUS_FWP_NULL_DISPLAY_NAME = 0xC0220023,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_ACTION_TYPE = 0xC0220024,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_WEIGHT = 0xC0220025,
  MD_NTSTATUS_WIN_STATUS_FWP_MATCH_TYPE_MISMATCH = 0xC0220026,
  MD_NTSTATUS_WIN_STATUS_FWP_TYPE_MISMATCH = 0xC0220027,
  MD_NTSTATUS_WIN_STATUS_FWP_OUT_OF_BOUNDS = 0xC0220028,
  MD_NTSTATUS_WIN_STATUS_FWP_RESERVED = 0xC0220029,
  MD_NTSTATUS_WIN_STATUS_FWP_DUPLICATE_CONDITION = 0xC022002A,
  MD_NTSTATUS_WIN_STATUS_FWP_DUPLICATE_KEYMOD = 0xC022002B,
  MD_NTSTATUS_WIN_STATUS_FWP_ACTION_INCOMPATIBLE_WITH_LAYER = 0xC022002C,
  MD_NTSTATUS_WIN_STATUS_FWP_ACTION_INCOMPATIBLE_WITH_SUBLAYER = 0xC022002D,
  MD_NTSTATUS_WIN_STATUS_FWP_CONTEXT_INCOMPATIBLE_WITH_LAYER = 0xC022002E,
  MD_NTSTATUS_WIN_STATUS_FWP_CONTEXT_INCOMPATIBLE_WITH_CALLOUT = 0xC022002F,
  MD_NTSTATUS_WIN_STATUS_FWP_INCOMPATIBLE_AUTH_METHOD = 0xC0220030,
  MD_NTSTATUS_WIN_STATUS_FWP_INCOMPATIBLE_DH_GROUP = 0xC0220031,
  MD_NTSTATUS_WIN_STATUS_FWP_EM_NOT_SUPPORTED = 0xC0220032,
  MD_NTSTATUS_WIN_STATUS_FWP_NEVER_MATCH = 0xC0220033,
  MD_NTSTATUS_WIN_STATUS_FWP_PROVIDER_CONTEXT_MISMATCH = 0xC0220034,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_PARAMETER = 0xC0220035,
  MD_NTSTATUS_WIN_STATUS_FWP_TOO_MANY_SUBLAYERS = 0xC0220036,
  MD_NTSTATUS_WIN_STATUS_FWP_CALLOUT_NOTIFICATION_FAILED = 0xC0220037,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_AUTH_TRANSFORM = 0xC0220038,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_CIPHER_TRANSFORM = 0xC0220039,
  MD_NTSTATUS_WIN_STATUS_FWP_INCOMPATIBLE_CIPHER_TRANSFORM = 0xC022003A,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_TRANSFORM_COMBINATION = 0xC022003B,
  MD_NTSTATUS_WIN_STATUS_FWP_DUPLICATE_AUTH_METHOD = 0xC022003C,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_TUNNEL_ENDPOINT = 0xC022003D,
  MD_NTSTATUS_WIN_STATUS_FWP_L2_DRIVER_NOT_READY = 0xC022003E,
  MD_NTSTATUS_WIN_STATUS_FWP_KEY_DICTATOR_ALREADY_REGISTERED = 0xC022003F,
  MD_NTSTATUS_WIN_STATUS_FWP_KEY_DICTATION_INVALID_KEYING_MATERIAL = 0xC0220040,
  MD_NTSTATUS_WIN_STATUS_FWP_CONNECTIONS_DISABLED = 0xC0220041,
  MD_NTSTATUS_WIN_STATUS_FWP_INVALID_DNS_NAME = 0xC0220042,
  MD_NTSTATUS_WIN_STATUS_FWP_STILL_ON = 0xC0220043,
  MD_NTSTATUS_WIN_STATUS_FWP_IKEEXT_NOT_RUNNING = 0xC0220044,
  MD_NTSTATUS_WIN_STATUS_FWP_TCPIP_NOT_READY = 0xC0220100,
  MD_NTSTATUS_WIN_STATUS_FWP_INJECT_HANDLE_CLOSING = 0xC0220101,
  MD_NTSTATUS_WIN_STATUS_FWP_INJECT_HANDLE_STALE = 0xC0220102,
  MD_NTSTATUS_WIN_STATUS_FWP_CANNOT_PEND = 0xC0220103,
  MD_NTSTATUS_WIN_STATUS_FWP_DROP_NOICMP = 0xC0220104,
  MD_NTSTATUS_WIN_STATUS_NDIS_CLOSING = 0xC0230002,
  MD_NTSTATUS_WIN_STATUS_NDIS_BAD_VERSION = 0xC0230004,
  MD_NTSTATUS_WIN_STATUS_NDIS_BAD_CHARACTERISTICS = 0xC0230005,
  MD_NTSTATUS_WIN_STATUS_NDIS_ADAPTER_NOT_FOUND = 0xC0230006,
  MD_NTSTATUS_WIN_STATUS_NDIS_OPEN_FAILED = 0xC0230007,
  MD_NTSTATUS_WIN_STATUS_NDIS_DEVICE_FAILED = 0xC0230008,
  MD_NTSTATUS_WIN_STATUS_NDIS_MULTICAST_FULL = 0xC0230009,
  MD_NTSTATUS_WIN_STATUS_NDIS_MULTICAST_EXISTS = 0xC023000A,
  MD_NTSTATUS_WIN_STATUS_NDIS_MULTICAST_NOT_FOUND = 0xC023000B,
  MD_NTSTATUS_WIN_STATUS_NDIS_REQUEST_ABORTED = 0xC023000C,
  MD_NTSTATUS_WIN_STATUS_NDIS_RESET_IN_PROGRESS = 0xC023000D,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_PACKET = 0xC023000F,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_DEVICE_REQUEST = 0xC0230010,
  MD_NTSTATUS_WIN_STATUS_NDIS_ADAPTER_NOT_READY = 0xC0230011,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_LENGTH = 0xC0230014,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_DATA = 0xC0230015,
  MD_NTSTATUS_WIN_STATUS_NDIS_BUFFER_TOO_SHORT = 0xC0230016,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_OID = 0xC0230017,
  MD_NTSTATUS_WIN_STATUS_NDIS_ADAPTER_REMOVED = 0xC0230018,
  MD_NTSTATUS_WIN_STATUS_NDIS_UNSUPPORTED_MEDIA = 0xC0230019,
  MD_NTSTATUS_WIN_STATUS_NDIS_GROUP_ADDRESS_IN_USE = 0xC023001A,
  MD_NTSTATUS_WIN_STATUS_NDIS_FILE_NOT_FOUND = 0xC023001B,
  MD_NTSTATUS_WIN_STATUS_NDIS_ERROR_READING_FILE = 0xC023001C,
  MD_NTSTATUS_WIN_STATUS_NDIS_ALREADY_MAPPED = 0xC023001D,
  MD_NTSTATUS_WIN_STATUS_NDIS_RESOURCE_CONFLICT = 0xC023001E,
  MD_NTSTATUS_WIN_STATUS_NDIS_MEDIA_DISCONNECTED = 0xC023001F,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_ADDRESS = 0xC0230022,
  MD_NTSTATUS_WIN_STATUS_NDIS_PAUSED = 0xC023002A,
  MD_NTSTATUS_WIN_STATUS_NDIS_INTERFACE_NOT_FOUND = 0xC023002B,
  MD_NTSTATUS_WIN_STATUS_NDIS_UNSUPPORTED_REVISION = 0xC023002C,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_PORT = 0xC023002D,
  MD_NTSTATUS_WIN_STATUS_NDIS_INVALID_PORT_STATE = 0xC023002E,
  MD_NTSTATUS_WIN_STATUS_NDIS_LOW_POWER_STATE = 0xC023002F,
  MD_NTSTATUS_WIN_STATUS_NDIS_REINIT_REQUIRED = 0xC0230030,
  MD_NTSTATUS_WIN_STATUS_NDIS_NOT_SUPPORTED = 0xC02300BB,
  MD_NTSTATUS_WIN_STATUS_NDIS_OFFLOAD_POLICY = 0xC023100F,
  MD_NTSTATUS_WIN_STATUS_NDIS_OFFLOAD_CONNECTION_REJECTED = 0xC0231012,
  MD_NTSTATUS_WIN_STATUS_NDIS_OFFLOAD_PATH_REJECTED = 0xC0231013,
  MD_NTSTATUS_WIN_STATUS_NDIS_DOT11_AUTO_CONFIG_ENABLED = 0xC0232000,
  MD_NTSTATUS_WIN_STATUS_NDIS_DOT11_MEDIA_IN_USE = 0xC0232001,
  MD_NTSTATUS_WIN_STATUS_NDIS_DOT11_POWER_STATE_INVALID = 0xC0232002,
  MD_NTSTATUS_WIN_STATUS_NDIS_PM_WOL_PATTERN_LIST_FULL = 0xC0232003,
  MD_NTSTATUS_WIN_STATUS_NDIS_PM_PROTOCOL_OFFLOAD_LIST_FULL = 0xC0232004,
  MD_NTSTATUS_WIN_STATUS_TPM_ERROR_MASK = 0xC0290000,
  MD_NTSTATUS_WIN_STATUS_TPM_AUTHFAIL = 0xC0290001,
  MD_NTSTATUS_WIN_STATUS_TPM_BADINDEX = 0xC0290002,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_PARAMETER = 0xC0290003,
  MD_NTSTATUS_WIN_STATUS_TPM_AUDITFAILURE = 0xC0290004,
  MD_NTSTATUS_WIN_STATUS_TPM_CLEAR_DISABLED = 0xC0290005,
  MD_NTSTATUS_WIN_STATUS_TPM_DEACTIVATED = 0xC0290006,
  MD_NTSTATUS_WIN_STATUS_TPM_DISABLED = 0xC0290007,
  MD_NTSTATUS_WIN_STATUS_TPM_DISABLED_CMD = 0xC0290008,
  MD_NTSTATUS_WIN_STATUS_TPM_FAIL = 0xC0290009,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_ORDINAL = 0xC029000A,
  MD_NTSTATUS_WIN_STATUS_TPM_INSTALL_DISABLED = 0xC029000B,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_KEYHANDLE = 0xC029000C,
  MD_NTSTATUS_WIN_STATUS_TPM_KEYNOTFOUND = 0xC029000D,
  MD_NTSTATUS_WIN_STATUS_TPM_INAPPROPRIATE_ENC = 0xC029000E,
  MD_NTSTATUS_WIN_STATUS_TPM_MIGRATEFAIL = 0xC029000F,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_PCR_INFO = 0xC0290010,
  MD_NTSTATUS_WIN_STATUS_TPM_NOSPACE = 0xC0290011,
  MD_NTSTATUS_WIN_STATUS_TPM_NOSRK = 0xC0290012,
  MD_NTSTATUS_WIN_STATUS_TPM_NOTSEALED_BLOB = 0xC0290013,
  MD_NTSTATUS_WIN_STATUS_TPM_OWNER_SET = 0xC0290014,
  MD_NTSTATUS_WIN_STATUS_TPM_RESOURCES = 0xC0290015,
  MD_NTSTATUS_WIN_STATUS_TPM_SHORTRANDOM = 0xC0290016,
  MD_NTSTATUS_WIN_STATUS_TPM_SIZE = 0xC0290017,
  MD_NTSTATUS_WIN_STATUS_TPM_WRONGPCRVAL = 0xC0290018,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_PARAM_SIZE = 0xC0290019,
  MD_NTSTATUS_WIN_STATUS_TPM_SHA_THREAD = 0xC029001A,
  MD_NTSTATUS_WIN_STATUS_TPM_SHA_ERROR = 0xC029001B,
  MD_NTSTATUS_WIN_STATUS_TPM_FAILEDSELFTEST = 0xC029001C,
  MD_NTSTATUS_WIN_STATUS_TPM_AUTH2FAIL = 0xC029001D,
  MD_NTSTATUS_WIN_STATUS_TPM_BADTAG = 0xC029001E,
  MD_NTSTATUS_WIN_STATUS_TPM_IOERROR = 0xC029001F,
  MD_NTSTATUS_WIN_STATUS_TPM_ENCRYPT_ERROR = 0xC0290020,
  MD_NTSTATUS_WIN_STATUS_TPM_DECRYPT_ERROR = 0xC0290021,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_AUTHHANDLE = 0xC0290022,
  MD_NTSTATUS_WIN_STATUS_TPM_NO_ENDORSEMENT = 0xC0290023,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_KEYUSAGE = 0xC0290024,
  MD_NTSTATUS_WIN_STATUS_TPM_WRONG_ENTITYTYPE = 0xC0290025,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_POSTINIT = 0xC0290026,
  MD_NTSTATUS_WIN_STATUS_TPM_INAPPROPRIATE_SIG = 0xC0290027,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_KEY_PROPERTY = 0xC0290028,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_MIGRATION = 0xC0290029,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_SCHEME = 0xC029002A,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_DATASIZE = 0xC029002B,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_MODE = 0xC029002C,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_PRESENCE = 0xC029002D,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_VERSION = 0xC029002E,
  MD_NTSTATUS_WIN_STATUS_TPM_NO_WRAP_TRANSPORT = 0xC029002F,
  MD_NTSTATUS_WIN_STATUS_TPM_AUDITFAIL_UNSUCCESSFUL = 0xC0290030,
  MD_NTSTATUS_WIN_STATUS_TPM_AUDITFAIL_SUCCESSFUL = 0xC0290031,
  MD_NTSTATUS_WIN_STATUS_TPM_NOTRESETABLE = 0xC0290032,
  MD_NTSTATUS_WIN_STATUS_TPM_NOTLOCAL = 0xC0290033,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_TYPE = 0xC0290034,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_RESOURCE = 0xC0290035,
  MD_NTSTATUS_WIN_STATUS_TPM_NOTFIPS = 0xC0290036,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_FAMILY = 0xC0290037,
  MD_NTSTATUS_WIN_STATUS_TPM_NO_NV_PERMISSION = 0xC0290038,
  MD_NTSTATUS_WIN_STATUS_TPM_REQUIRES_SIGN = 0xC0290039,
  MD_NTSTATUS_WIN_STATUS_TPM_KEY_NOTSUPPORTED = 0xC029003A,
  MD_NTSTATUS_WIN_STATUS_TPM_AUTH_CONFLICT = 0xC029003B,
  MD_NTSTATUS_WIN_STATUS_TPM_AREA_LOCKED = 0xC029003C,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_LOCALITY = 0xC029003D,
  MD_NTSTATUS_WIN_STATUS_TPM_READ_ONLY = 0xC029003E,
  MD_NTSTATUS_WIN_STATUS_TPM_PER_NOWRITE = 0xC029003F,
  MD_NTSTATUS_WIN_STATUS_TPM_FAMILYCOUNT = 0xC0290040,
  MD_NTSTATUS_WIN_STATUS_TPM_WRITE_LOCKED = 0xC0290041,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_ATTRIBUTES = 0xC0290042,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_STRUCTURE = 0xC0290043,
  MD_NTSTATUS_WIN_STATUS_TPM_KEY_OWNER_CONTROL = 0xC0290044,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_COUNTER = 0xC0290045,
  MD_NTSTATUS_WIN_STATUS_TPM_NOT_FULLWRITE = 0xC0290046,
  MD_NTSTATUS_WIN_STATUS_TPM_CONTEXT_GAP = 0xC0290047,
  MD_NTSTATUS_WIN_STATUS_TPM_MAXNVWRITES = 0xC0290048,
  MD_NTSTATUS_WIN_STATUS_TPM_NOOPERATOR = 0xC0290049,
  MD_NTSTATUS_WIN_STATUS_TPM_RESOURCEMISSING = 0xC029004A,
  MD_NTSTATUS_WIN_STATUS_TPM_DELEGATE_LOCK = 0xC029004B,
  MD_NTSTATUS_WIN_STATUS_TPM_DELEGATE_FAMILY = 0xC029004C,
  MD_NTSTATUS_WIN_STATUS_TPM_DELEGATE_ADMIN = 0xC029004D,
  MD_NTSTATUS_WIN_STATUS_TPM_TRANSPORT_NOTEXCLUSIVE = 0xC029004E,
  MD_NTSTATUS_WIN_STATUS_TPM_OWNER_CONTROL = 0xC029004F,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_RESOURCES = 0xC0290050,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_INPUT_DATA0 = 0xC0290051,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_INPUT_DATA1 = 0xC0290052,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_ISSUER_SETTINGS = 0xC0290053,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_TPM_SETTINGS = 0xC0290054,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_STAGE = 0xC0290055,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_ISSUER_VALIDITY = 0xC0290056,
  MD_NTSTATUS_WIN_STATUS_TPM_DAA_WRONG_W = 0xC0290057,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_HANDLE = 0xC0290058,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_DELEGATE = 0xC0290059,
  MD_NTSTATUS_WIN_STATUS_TPM_BADCONTEXT = 0xC029005A,
  MD_NTSTATUS_WIN_STATUS_TPM_TOOMANYCONTEXTS = 0xC029005B,
  MD_NTSTATUS_WIN_STATUS_TPM_MA_TICKET_SIGNATURE = 0xC029005C,
  MD_NTSTATUS_WIN_STATUS_TPM_MA_DESTINATION = 0xC029005D,
  MD_NTSTATUS_WIN_STATUS_TPM_MA_SOURCE = 0xC029005E,
  MD_NTSTATUS_WIN_STATUS_TPM_MA_AUTHORITY = 0xC029005F,
  MD_NTSTATUS_WIN_STATUS_TPM_PERMANENTEK = 0xC0290061,
  MD_NTSTATUS_WIN_STATUS_TPM_BAD_SIGNATURE = 0xC0290062,
  MD_NTSTATUS_WIN_STATUS_TPM_NOCONTEXTSPACE = 0xC0290063,
  MD_NTSTATUS_WIN_STATUS_TPM_COMMAND_BLOCKED = 0xC0290400,
  MD_NTSTATUS_WIN_STATUS_TPM_INVALID_HANDLE = 0xC0290401,
  MD_NTSTATUS_WIN_STATUS_TPM_DUPLICATE_VHANDLE = 0xC0290402,
  MD_NTSTATUS_WIN_STATUS_TPM_EMBEDDED_COMMAND_BLOCKED = 0xC0290403,
  MD_NTSTATUS_WIN_STATUS_TPM_EMBEDDED_COMMAND_UNSUPPORTED = 0xC0290404,
  MD_NTSTATUS_WIN_STATUS_TPM_RETRY = 0xC0290800,
  MD_NTSTATUS_WIN_STATUS_TPM_NEEDS_SELFTEST = 0xC0290801,
  MD_NTSTATUS_WIN_STATUS_TPM_DOING_SELFTEST = 0xC0290802,
  MD_NTSTATUS_WIN_STATUS_TPM_DEFEND_LOCK_RUNNING = 0xC0290803,
  MD_NTSTATUS_WIN_STATUS_TPM_COMMAND_CANCELED = 0xC0291001,
  MD_NTSTATUS_WIN_STATUS_TPM_TOO_MANY_CONTEXTS = 0xC0291002,
  MD_NTSTATUS_WIN_STATUS_TPM_NOT_FOUND = 0xC0291003,
  MD_NTSTATUS_WIN_STATUS_TPM_ACCESS_DENIED = 0xC0291004,
  MD_NTSTATUS_WIN_STATUS_TPM_INSUFFICIENT_BUFFER = 0xC0291005,
  MD_NTSTATUS_WIN_STATUS_TPM_PPI_FUNCTION_UNSUPPORTED = 0xC0291006,
  MD_NTSTATUS_WIN_STATUS_PCP_ERROR_MASK = 0xC0292000,
  MD_NTSTATUS_WIN_STATUS_PCP_DEVICE_NOT_READY = 0xC0292001,
  MD_NTSTATUS_WIN_STATUS_PCP_INVALID_HANDLE = 0xC0292002,
  MD_NTSTATUS_WIN_STATUS_PCP_INVALID_PARAMETER = 0xC0292003,
  MD_NTSTATUS_WIN_STATUS_PCP_FLAG_NOT_SUPPORTED = 0xC0292004,
  MD_NTSTATUS_WIN_STATUS_PCP_NOT_SUPPORTED = 0xC0292005,
  MD_NTSTATUS_WIN_STATUS_PCP_BUFFER_TOO_SMALL = 0xC0292006,
  MD_NTSTATUS_WIN_STATUS_PCP_INTERNAL_ERROR = 0xC0292007,
  MD_NTSTATUS_WIN_STATUS_PCP_AUTHENTICATION_FAILED = 0xC0292008,
  MD_NTSTATUS_WIN_STATUS_PCP_AUTHENTICATION_IGNORED = 0xC0292009,
  MD_NTSTATUS_WIN_STATUS_PCP_POLICY_NOT_FOUND = 0xC029200A,
  MD_NTSTATUS_WIN_STATUS_PCP_PROFILE_NOT_FOUND = 0xC029200B,
  MD_NTSTATUS_WIN_STATUS_PCP_VALIDATION_FAILED = 0xC029200C,
  MD_NTSTATUS_WIN_STATUS_PCP_DEVICE_NOT_FOUND = 0xC029200D,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_HYPERCALL_CODE = 0xC0350002,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_HYPERCALL_INPUT = 0xC0350003,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_ALIGNMENT = 0xC0350004,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_PARAMETER = 0xC0350005,
  MD_NTSTATUS_WIN_STATUS_HV_ACCESS_DENIED = 0xC0350006,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_PARTITION_STATE = 0xC0350007,
  MD_NTSTATUS_WIN_STATUS_HV_OPERATION_DENIED = 0xC0350008,
  MD_NTSTATUS_WIN_STATUS_HV_UNKNOWN_PROPERTY = 0xC0350009,
  MD_NTSTATUS_WIN_STATUS_HV_PROPERTY_VALUE_OUT_OF_RANGE = 0xC035000A,
  MD_NTSTATUS_WIN_STATUS_HV_INSUFFICIENT_MEMORY = 0xC035000B,
  MD_NTSTATUS_WIN_STATUS_HV_PARTITION_TOO_DEEP = 0xC035000C,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_PARTITION_ID = 0xC035000D,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_VP_INDEX = 0xC035000E,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_PORT_ID = 0xC0350011,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_CONNECTION_ID = 0xC0350012,
  MD_NTSTATUS_WIN_STATUS_HV_INSUFFICIENT_BUFFERS = 0xC0350013,
  MD_NTSTATUS_WIN_STATUS_HV_NOT_ACKNOWLEDGED = 0xC0350014,
  MD_NTSTATUS_WIN_STATUS_HV_ACKNOWLEDGED = 0xC0350016,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_SAVE_RESTORE_STATE = 0xC0350017,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_SYNIC_STATE = 0xC0350018,
  MD_NTSTATUS_WIN_STATUS_HV_OBJECT_IN_USE = 0xC0350019,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_PROXIMITY_DOMAIN_INFO = 0xC035001A,
  MD_NTSTATUS_WIN_STATUS_HV_NO_DATA = 0xC035001B,
  MD_NTSTATUS_WIN_STATUS_HV_INACTIVE = 0xC035001C,
  MD_NTSTATUS_WIN_STATUS_HV_NO_RESOURCES = 0xC035001D,
  MD_NTSTATUS_WIN_STATUS_HV_FEATURE_UNAVAILABLE = 0xC035001E,
  MD_NTSTATUS_WIN_STATUS_HV_INSUFFICIENT_BUFFER = 0xC0350033,
  MD_NTSTATUS_WIN_STATUS_HV_INSUFFICIENT_DEVICE_DOMAINS = 0xC0350038,
  MD_NTSTATUS_WIN_STATUS_HV_INVALID_LP_INDEX = 0xC0350041,
  MD_NTSTATUS_WIN_STATUS_HV_NOT_PRESENT = 0xC0351000,
  MD_NTSTATUS_WIN_STATUS_IPSEC_BAD_SPI = 0xC0360001,
  MD_NTSTATUS_WIN_STATUS_IPSEC_SA_LIFETIME_EXPIRED = 0xC0360002,
  MD_NTSTATUS_WIN_STATUS_IPSEC_WRONG_SA = 0xC0360003,
  MD_NTSTATUS_WIN_STATUS_IPSEC_REPLAY_CHECK_FAILED = 0xC0360004,
  MD_NTSTATUS_WIN_STATUS_IPSEC_INVALID_PACKET = 0xC0360005,
  MD_NTSTATUS_WIN_STATUS_IPSEC_INTEGRITY_CHECK_FAILED = 0xC0360006,
  MD_NTSTATUS_WIN_STATUS_IPSEC_CLEAR_TEXT_DROP = 0xC0360007,
  MD_NTSTATUS_WIN_STATUS_IPSEC_AUTH_FIREWALL_DROP = 0xC0360008,
  MD_NTSTATUS_WIN_STATUS_IPSEC_THROTTLE_DROP = 0xC0360009,
  MD_NTSTATUS_WIN_STATUS_IPSEC_DOSP_BLOCK = 0xC0368000,
  MD_NTSTATUS_WIN_STATUS_IPSEC_DOSP_RECEIVED_MULTICAST = 0xC0368001,
  MD_NTSTATUS_WIN_STATUS_IPSEC_DOSP_INVALID_PACKET = 0xC0368002,
  MD_NTSTATUS_WIN_STATUS_IPSEC_DOSP_STATE_LOOKUP_FAILED = 0xC0368003,
  MD_NTSTATUS_WIN_STATUS_IPSEC_DOSP_MAX_ENTRIES = 0xC0368004,
  MD_NTSTATUS_WIN_STATUS_IPSEC_DOSP_KEYMOD_NOT_ALLOWED = 0xC0368005,
  MD_NTSTATUS_WIN_STATUS_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES = 0xC0368006,
  MD_NTSTATUS_WIN_STATUS_VID_DUPLICATE_HANDLER = 0xC0370001,
  MD_NTSTATUS_WIN_STATUS_VID_TOO_MANY_HANDLERS = 0xC0370002,
  MD_NTSTATUS_WIN_STATUS_VID_QUEUE_FULL = 0xC0370003,
  MD_NTSTATUS_WIN_STATUS_VID_HANDLER_NOT_PRESENT = 0xC0370004,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_OBJECT_NAME = 0xC0370005,
  MD_NTSTATUS_WIN_STATUS_VID_PARTITION_NAME_TOO_LONG = 0xC0370006,
  MD_NTSTATUS_WIN_STATUS_VID_MESSAGE_QUEUE_NAME_TOO_LONG = 0xC0370007,
  MD_NTSTATUS_WIN_STATUS_VID_PARTITION_ALREADY_EXISTS = 0xC0370008,
  MD_NTSTATUS_WIN_STATUS_VID_PARTITION_DOES_NOT_EXIST = 0xC0370009,
  MD_NTSTATUS_WIN_STATUS_VID_PARTITION_NAME_NOT_FOUND = 0xC037000A,
  MD_NTSTATUS_WIN_STATUS_VID_MESSAGE_QUEUE_ALREADY_EXISTS = 0xC037000B,
  MD_NTSTATUS_WIN_STATUS_VID_EXCEEDED_MBP_ENTRY_MAP_LIMIT = 0xC037000C,
  MD_NTSTATUS_WIN_STATUS_VID_MB_STILL_REFERENCED = 0xC037000D,
  MD_NTSTATUS_WIN_STATUS_VID_CHILD_GPA_PAGE_SET_CORRUPTED = 0xC037000E,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_NUMA_SETTINGS = 0xC037000F,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_NUMA_NODE_INDEX = 0xC0370010,
  MD_NTSTATUS_WIN_STATUS_VID_NOTIFICATION_QUEUE_ALREADY_ASSOCIATED = 0xC0370011,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_MEMORY_BLOCK_HANDLE = 0xC0370012,
  MD_NTSTATUS_WIN_STATUS_VID_PAGE_RANGE_OVERFLOW = 0xC0370013,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_MESSAGE_QUEUE_HANDLE = 0xC0370014,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_GPA_RANGE_HANDLE = 0xC0370015,
  MD_NTSTATUS_WIN_STATUS_VID_NO_MEMORY_BLOCK_NOTIFICATION_QUEUE = 0xC0370016,
  MD_NTSTATUS_WIN_STATUS_VID_MEMORY_BLOCK_LOCK_COUNT_EXCEEDED = 0xC0370017,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_PPM_HANDLE = 0xC0370018,
  MD_NTSTATUS_WIN_STATUS_VID_MBPS_ARE_LOCKED = 0xC0370019,
  MD_NTSTATUS_WIN_STATUS_VID_MESSAGE_QUEUE_CLOSED = 0xC037001A,
  MD_NTSTATUS_WIN_STATUS_VID_VIRTUAL_PROCESSOR_LIMIT_EXCEEDED = 0xC037001B,
  MD_NTSTATUS_WIN_STATUS_VID_STOP_PENDING = 0xC037001C,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_PROCESSOR_STATE = 0xC037001D,
  MD_NTSTATUS_WIN_STATUS_VID_EXCEEDED_KM_CONTEXT_COUNT_LIMIT = 0xC037001E,
  MD_NTSTATUS_WIN_STATUS_VID_KM_INTERFACE_ALREADY_INITIALIZED = 0xC037001F,
  MD_NTSTATUS_WIN_STATUS_VID_MB_PROPERTY_ALREADY_SET_RESET = 0xC0370020,
  MD_NTSTATUS_WIN_STATUS_VID_MMIO_RANGE_DESTROYED = 0xC0370021,
  MD_NTSTATUS_WIN_STATUS_VID_INVALID_CHILD_GPA_PAGE_SET = 0xC0370022,
  MD_NTSTATUS_WIN_STATUS_VID_RESERVE_PAGE_SET_IS_BEING_USED = 0xC0370023,
  MD_NTSTATUS_WIN_STATUS_VID_RESERVE_PAGE_SET_TOO_SMALL = 0xC0370024,
  MD_NTSTATUS_WIN_STATUS_VID_MBP_ALREADY_LOCKED_USING_RESERVED_PAGE = 0xC0370025,
  MD_NTSTATUS_WIN_STATUS_VID_MBP_COUNT_EXCEEDED_LIMIT = 0xC0370026,
  MD_NTSTATUS_WIN_STATUS_VID_SAVED_STATE_CORRUPT = 0xC0370027,
  MD_NTSTATUS_WIN_STATUS_VID_SAVED_STATE_UNRECOGNIZED_ITEM = 0xC0370028,
  MD_NTSTATUS_WIN_STATUS_VID_SAVED_STATE_INCOMPATIBLE = 0xC0370029,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DATABASE_FULL = 0xC0380001,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_CONFIGURATION_CORRUPTED = 0xC0380002,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_CONFIGURATION_NOT_IN_SYNC = 0xC0380003,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_CONFIG_UPDATE_FAILED = 0xC0380004,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_CONTAINS_NON_SIMPLE_VOLUME = 0xC0380005,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_DUPLICATE = 0xC0380006,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_DYNAMIC = 0xC0380007,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_ID_INVALID = 0xC0380008,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_INVALID = 0xC0380009,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_LAST_VOTER = 0xC038000A,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_LAYOUT_INVALID = 0xC038000B,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_LAYOUT_NON_BASIC_BETWEEN_BASIC_PARTITIONS = 0xC038000C,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_LAYOUT_NOT_CYLINDER_ALIGNED = 0xC038000D,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_LAYOUT_PARTITIONS_TOO_SMALL = 0xC038000E,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_LAYOUT_PRIMARY_BETWEEN_LOGICAL_PARTITIONS = 0xC038000F,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_LAYOUT_TOO_MANY_PARTITIONS = 0xC0380010,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_MISSING = 0xC0380011,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_NOT_EMPTY = 0xC0380012,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_NOT_ENOUGH_SPACE = 0xC0380013,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_REVECTORING_FAILED = 0xC0380014,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_SECTOR_SIZE_INVALID = 0xC0380015,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_SET_NOT_CONTAINED = 0xC0380016,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_USED_BY_MULTIPLE_MEMBERS = 0xC0380017,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DISK_USED_BY_MULTIPLE_PLEXES = 0xC0380018,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DYNAMIC_DISK_NOT_SUPPORTED = 0xC0380019,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_EXTENT_ALREADY_USED = 0xC038001A,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_EXTENT_NOT_CONTIGUOUS = 0xC038001B,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_EXTENT_NOT_IN_PUBLIC_REGION = 0xC038001C,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_EXTENT_NOT_SECTOR_ALIGNED = 0xC038001D,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_EXTENT_OVERLAPS_EBR_PARTITION = 0xC038001E,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_EXTENT_VOLUME_LENGTHS_DO_NOT_MATCH = 0xC038001F,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_FAULT_TOLERANT_NOT_SUPPORTED = 0xC0380020,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_INTERLEAVE_LENGTH_INVALID = 0xC0380021,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MAXIMUM_REGISTERED_USERS = 0xC0380022,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MEMBER_IN_SYNC = 0xC0380023,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MEMBER_INDEX_DUPLICATE = 0xC0380024,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MEMBER_INDEX_INVALID = 0xC0380025,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MEMBER_MISSING = 0xC0380026,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MEMBER_NOT_DETACHED = 0xC0380027,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MEMBER_REGENERATING = 0xC0380028,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_ALL_DISKS_FAILED = 0xC0380029,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NO_REGISTERED_USERS = 0xC038002A,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NO_SUCH_USER = 0xC038002B,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NOTIFICATION_RESET = 0xC038002C,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NUMBER_OF_MEMBERS_INVALID = 0xC038002D,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NUMBER_OF_PLEXES_INVALID = 0xC038002E,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_DUPLICATE = 0xC038002F,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_ID_INVALID = 0xC0380030,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_INVALID = 0xC0380031,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_NAME_INVALID = 0xC0380032,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_OFFLINE = 0xC0380033,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_HAS_QUORUM = 0xC0380034,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_WITHOUT_QUORUM = 0xC0380035,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PARTITION_STYLE_INVALID = 0xC0380036,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PARTITION_UPDATE_FAILED = 0xC0380037,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_IN_SYNC = 0xC0380038,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_INDEX_DUPLICATE = 0xC0380039,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_INDEX_INVALID = 0xC038003A,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_LAST_ACTIVE = 0xC038003B,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_MISSING = 0xC038003C,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_REGENERATING = 0xC038003D,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_TYPE_INVALID = 0xC038003E,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_NOT_RAID5 = 0xC038003F,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_NOT_SIMPLE = 0xC0380040,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_STRUCTURE_SIZE_INVALID = 0xC0380041,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_TOO_MANY_NOTIFICATION_REQUESTS = 0xC0380042,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_TRANSACTION_IN_PROGRESS = 0xC0380043,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_UNEXPECTED_DISK_LAYOUT_CHANGE = 0xC0380044,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_CONTAINS_MISSING_DISK = 0xC0380045,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_ID_INVALID = 0xC0380046,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_LENGTH_INVALID = 0xC0380047,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_LENGTH_NOT_SECTOR_SIZE_MULTIPLE = 0xC0380048,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_NOT_MIRRORED = 0xC0380049,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_NOT_RETAINED = 0xC038004A,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_OFFLINE = 0xC038004B,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_RETAINED = 0xC038004C,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NUMBER_OF_EXTENTS_INVALID = 0xC038004D,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_DIFFERENT_SECTOR_SIZE = 0xC038004E,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_BAD_BOOT_DISK = 0xC038004F,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_CONFIG_OFFLINE = 0xC0380050,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_CONFIG_ONLINE = 0xC0380051,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NOT_PRIMARY_PACK = 0xC0380052,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PACK_LOG_UPDATE_FAILED = 0xC0380053,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NUMBER_OF_DISKS_IN_PLEX_INVALID = 0xC0380054,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NUMBER_OF_DISKS_IN_MEMBER_INVALID = 0xC0380055,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_VOLUME_MIRRORED = 0xC0380056,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PLEX_NOT_SIMPLE_SPANNED = 0xC0380057,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NO_VALID_LOG_COPIES = 0xC0380058,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_PRIMARY_PACK_PRESENT = 0xC0380059,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_NUMBER_OF_DISKS_INVALID = 0xC038005A,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_MIRROR_NOT_SUPPORTED = 0xC038005B,
  MD_NTSTATUS_WIN_STATUS_VOLMGR_RAID5_NOT_SUPPORTED = 0xC038005C,
  MD_NTSTATUS_WIN_STATUS_BCD_TOO_MANY_ELEMENTS = 0xC0390002,
  MD_NTSTATUS_WIN_STATUS_VHD_DRIVE_FOOTER_MISSING = 0xC03A0001,
  MD_NTSTATUS_WIN_STATUS_VHD_DRIVE_FOOTER_CHECKSUM_MISMATCH = 0xC03A0002,
  MD_NTSTATUS_WIN_STATUS_VHD_DRIVE_FOOTER_CORRUPT = 0xC03A0003,
  MD_NTSTATUS_WIN_STATUS_VHD_FORMAT_UNKNOWN = 0xC03A0004,
  MD_NTSTATUS_WIN_STATUS_VHD_FORMAT_UNSUPPORTED_VERSION = 0xC03A0005,
  MD_NTSTATUS_WIN_STATUS_VHD_SPARSE_HEADER_CHECKSUM_MISMATCH = 0xC03A0006,
  MD_NTSTATUS_WIN_STATUS_VHD_SPARSE_HEADER_UNSUPPORTED_VERSION = 0xC03A0007,
  MD_NTSTATUS_WIN_STATUS_VHD_SPARSE_HEADER_CORRUPT = 0xC03A0008,
  MD_NTSTATUS_WIN_STATUS_VHD_BLOCK_ALLOCATION_FAILURE = 0xC03A0009,
  MD_NTSTATUS_WIN_STATUS_VHD_BLOCK_ALLOCATION_TABLE_CORRUPT = 0xC03A000A,
  MD_NTSTATUS_WIN_STATUS_VHD_INVALID_BLOCK_SIZE = 0xC03A000B,
  MD_NTSTATUS_WIN_STATUS_VHD_BITMAP_MISMATCH = 0xC03A000C,
  MD_NTSTATUS_WIN_STATUS_VHD_PARENT_VHD_NOT_FOUND = 0xC03A000D,
  MD_NTSTATUS_WIN_STATUS_VHD_CHILD_PARENT_ID_MISMATCH = 0xC03A000E,
  MD_NTSTATUS_WIN_STATUS_VHD_CHILD_PARENT_TIMESTAMP_MISMATCH = 0xC03A000F,
  MD_NTSTATUS_WIN_STATUS_VHD_METADATA_READ_FAILURE = 0xC03A0010,
  MD_NTSTATUS_WIN_STATUS_VHD_METADATA_WRITE_FAILURE = 0xC03A0011,
  MD_NTSTATUS_WIN_STATUS_VHD_INVALID_SIZE = 0xC03A0012,
  MD_NTSTATUS_WIN_STATUS_VHD_INVALID_FILE_SIZE = 0xC03A0013,
  MD_NTSTATUS_WIN_STATUS_VIRTDISK_PROVIDER_NOT_FOUND = 0xC03A0014,
  MD_NTSTATUS_WIN_STATUS_VIRTDISK_NOT_VIRTUAL_DISK = 0xC03A0015,
  MD_NTSTATUS_WIN_STATUS_VHD_PARENT_VHD_ACCESS_DENIED = 0xC03A0016,
  MD_NTSTATUS_WIN_STATUS_VHD_CHILD_PARENT_SIZE_MISMATCH = 0xC03A0017,
  MD_NTSTATUS_WIN_STATUS_VHD_DIFFERENCING_CHAIN_CYCLE_DETECTED = 0xC03A0018,
  MD_NTSTATUS_WIN_STATUS_VHD_DIFFERENCING_CHAIN_ERROR_IN_PARENT = 0xC03A0019,
  MD_NTSTATUS_WIN_STATUS_VIRTUAL_DISK_LIMITATION = 0xC03A001A,
  MD_NTSTATUS_WIN_STATUS_VHD_INVALID_TYPE = 0xC03A001B,
  MD_NTSTATUS_WIN_STATUS_VHD_INVALID_STATE = 0xC03A001C,
  MD_NTSTATUS_WIN_STATUS_VIRTDISK_UNSUPPORTED_DISK_SECTOR_SIZE = 0xC03A001D,
  MD_NTSTATUS_WIN_STATUS_VIRTDISK_DISK_ALREADY_OWNED = 0xC03A001E,
  MD_NTSTATUS_WIN_STATUS_VIRTDISK_DISK_ONLINE_AND_WRITABLE = 0xC03A001F,
  MD_NTSTATUS_WIN_STATUS_CTLOG_TRACKING_NOT_INITIALIZED = 0xC03A0020,
  MD_NTSTATUS_WIN_STATUS_CTLOG_LOGFILE_SIZE_EXCEEDED_MAXSIZE = 0xC03A0021,
  MD_NTSTATUS_WIN_STATUS_CTLOG_VHD_CHANGED_OFFLINE = 0xC03A0022,
  MD_NTSTATUS_WIN_STATUS_CTLOG_INVALID_TRACKING_STATE = 0xC03A0023,
  MD_NTSTATUS_WIN_STATUS_CTLOG_INCONSISTENT_TRACKING_FILE = 0xC03A0024,
  MD_NTSTATUS_WIN_STATUS_VHD_METADATA_FULL = 0xC03A0028,
  MD_NTSTATUS_WIN_STATUS_RKF_KEY_NOT_FOUND = 0xC0400001,
  MD_NTSTATUS_WIN_STATUS_RKF_DUPLICATE_KEY = 0xC0400002,
  MD_NTSTATUS_WIN_STATUS_RKF_BLOB_FULL = 0xC0400003,
  MD_NTSTATUS_WIN_STATUS_RKF_STORE_FULL = 0xC0400004,
  MD_NTSTATUS_WIN_STATUS_RKF_FILE_BLOCKED = 0xC0400005,
  MD_NTSTATUS_WIN_STATUS_RKF_ACTIVE_KEY = 0xC0400006,
  MD_NTSTATUS_WIN_STATUS_RDBSS_RESTART_OPERATION = 0xC0410001,
  MD_NTSTATUS_WIN_STATUS_RDBSS_CONTINUE_OPERATION = 0xC0410002,
  MD_NTSTATUS_WIN_STATUS_RDBSS_POST_OPERATION = 0xC0410003,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INVALID_HANDLE = 0xC0420001,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_READ_NOT_PERMITTED = 0xC0420002,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_WRITE_NOT_PERMITTED = 0xC0420003,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INVALID_PDU = 0xC0420004,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INSUFFICIENT_AUTHENTICATION = 0xC0420005,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_REQUEST_NOT_SUPPORTED = 0xC0420006,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INVALID_OFFSET = 0xC0420007,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INSUFFICIENT_AUTHORIZATION = 0xC0420008,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_PREPARE_QUEUE_FULL = 0xC0420009,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_ATTRIBUTE_NOT_FOUND = 0xC042000A,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_ATTRIBUTE_NOT_LONG = 0xC042000B,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INSUFFICIENT_ENCRYPTION_KEY_SIZE = 0xC042000C,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INVALID_ATTRIBUTE_VALUE_LENGTH = 0xC042000D,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_UNLIKELY = 0xC042000E,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INSUFFICIENT_ENCRYPTION = 0xC042000F,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_UNSUPPORTED_GROUP_TYPE = 0xC0420010,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_INSUFFICIENT_RESOURCES = 0xC0420011,
  MD_NTSTATUS_WIN_STATUS_BTH_ATT_UNKNOWN_ERROR = 0xC0421000,
  MD_NTSTATUS_WIN_STATUS_SECUREBOOT_ROLLBACK_DETECTED = 0xC0430001,
  MD_NTSTATUS_WIN_STATUS_SECUREBOOT_POLICY_VIOLATION = 0xC0430002,
  MD_NTSTATUS_WIN_STATUS_SECUREBOOT_INVALID_POLICY = 0xC0430003,
  MD_NTSTATUS_WIN_STATUS_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND = 0xC0430004,
  MD_NTSTATUS_WIN_STATUS_SECUREBOOT_POLICY_NOT_SIGNED = 0xC0430005,
  MD_NTSTATUS_WIN_STATUS_SECUREBOOT_FILE_REPLACED = 0xC0430007,
  MD_NTSTATUS_WIN_STATUS_AUDIO_ENGINE_NODE_NOT_FOUND = 0xC0440001,
  MD_NTSTATUS_WIN_STATUS_HDAUDIO_EMPTY_CONNECTION_LIST = 0xC0440002,
  MD_NTSTATUS_WIN_STATUS_HDAUDIO_CONNECTION_LIST_NOT_SUPPORTED = 0xC0440003,
  MD_NTSTATUS_WIN_STATUS_HDAUDIO_NO_LOGICAL_DEVICES_CREATED = 0xC0440004,
  MD_NTSTATUS_WIN_STATUS_HDAUDIO_NULL_LINKED_LIST_ENTRY = 0xC0440005,
  MD_NTSTATUS_WIN_STATUS_VOLSNAP_BOOTFILE_NOT_VALID = 0xC0500003,
  MD_NTSTATUS_WIN_STATUS_IO_PREEMPTED = 0xC0510001,
  MD_NTSTATUS_WIN_STATUS_SVHDX_ERROR_STORED = 0xC05C0000,
  MD_NTSTATUS_WIN_STATUS_SVHDX_ERROR_NOT_AVAILABLE = 0xC05CFF00,
  MD_NTSTATUS_WIN_STATUS_SVHDX_UNIT_ATTENTION_AVAILABLE = 0xC05CFF01,
  MD_NTSTATUS_WIN_STATUS_SVHDX_UNIT_ATTENTION_CAPACITY_DATA_CHANGED = 0xC05CFF02,
  MD_NTSTATUS_WIN_STATUS_SVHDX_UNIT_ATTENTION_RESERVATIONS_PREEMPTED = 0xC05CFF03,
  MD_NTSTATUS_WIN_STATUS_SVHDX_UNIT_ATTENTION_RESERVATIONS_RELEASED = 0xC05CFF04,
  MD_NTSTATUS_WIN_STATUS_SVHDX_UNIT_ATTENTION_REGISTRATIONS_PREEMPTED = 0xC05CFF05,
  MD_NTSTATUS_WIN_STATUS_SVHDX_UNIT_ATTENTION_OPERATING_DEFINITION_CHANGED = 0xC05CFF06,
  MD_NTSTATUS_WIN_STATUS_SVHDX_RESERVATION_CONFLICT = 0xC05CFF07,
  MD_NTSTATUS_WIN_STATUS_SVHDX_WRONG_FILE_TYPE = 0xC05CFF08,
  MD_NTSTATUS_WIN_STATUS_SVHDX_VERSION_MISMATCH = 0xC05CFF09,
  MD_NTSTATUS_WIN_STATUS_VHD_SHARED = 0xC05CFF0A,
  MD_NTSTATUS_WIN_STATUS_SPACES_RESILIENCY_TYPE_INVALID = 0xC0E70003,
  MD_NTSTATUS_WIN_STATUS_SPACES_DRIVE_SECTOR_SIZE_INVALID = 0xC0E70004,
  MD_NTSTATUS_WIN_STATUS_SPACES_INTERLEAVE_LENGTH_INVALID = 0xC0E70009,
  MD_NTSTATUS_WIN_STATUS_SPACES_NUMBER_OF_COLUMNS_INVALID = 0xC0E7000A,
  MD_NTSTATUS_WIN_STATUS_SPACES_NOT_ENOUGH_DRIVES = 0xC0E7000B
} MDNTStatusCodeWin;

// These constants are defined in the MSDN documentation of
// the EXCEPTION_RECORD structure.
typedef enum {
  MD_ACCESS_VIOLATION_WIN_READ  = 0,
  MD_ACCESS_VIOLATION_WIN_WRITE = 1,
  MD_ACCESS_VIOLATION_WIN_EXEC  = 8
} MDAccessViolationTypeWin;

// These constants are defined in the MSDN documentation of
// the EXCEPTION_RECORD structure.
typedef enum {
  MD_IN_PAGE_ERROR_WIN_READ  = 0,
  MD_IN_PAGE_ERROR_WIN_WRITE = 1,
  MD_IN_PAGE_ERROR_WIN_EXEC  = 8
} MDInPageErrorTypeWin;

// These constants are defined in winnt.h and are used with the
// STATUS_STACK_BUFFER_OVERRUN exception as exception subcodes.
typedef enum {
  MD_FAST_FAIL_LEGACY_GS_VIOLATION = 0,
  MD_FAST_FAIL_VTGUARD_CHECK_FAILURE = 1,
  MD_FAST_FAIL_STACK_COOKIE_CHECK_FAILURE = 2,
  MD_FAST_FAIL_CORRUPT_LIST_ENTRY = 3,
  MD_FAST_FAIL_INCORRECT_STACK = 4,
  MD_FAST_FAIL_INVALID_ARG = 5,
  MD_FAST_FAIL_GS_COOKIE_INIT = 6,
  MD_FAST_FAIL_FATAL_APP_EXIT = 7,
  MD_FAST_FAIL_RANGE_CHECK_FAILURE = 8,
  MD_FAST_FAIL_UNSAFE_REGISTRY_ACCESS = 9,
  MD_FAST_FAIL_GUARD_ICALL_CHECK_FAILURE = 10,
  MD_FAST_FAIL_GUARD_WRITE_CHECK_FAILURE = 11,
  MD_FAST_FAIL_INVALID_FIBER_SWITCH = 12,
  MD_FAST_FAIL_INVALID_SET_OF_CONTEXT = 13,
  MD_FAST_FAIL_INVALID_REFERENCE_COUNT = 14,
  MD_FAST_FAIL_INVALID_JUMP_BUFFER = 18,
  MD_FAST_FAIL_MRDATA_MODIFIED = 19,
  MD_FAST_FAIL_CERTIFICATION_FAILURE = 20,
  MD_FAST_FAIL_INVALID_EXCEPTION_CHAIN = 21,
  MD_FAST_FAIL_CRYPTO_LIBRARY = 22,
  MD_FAST_FAIL_INVALID_CALL_IN_DLL_CALLOUT = 23,
  MD_FAST_FAIL_INVALID_IMAGE_BASE = 24,
  MD_FAST_FAIL_DLOAD_PROTECTION_FAILURE = 25,
  MD_FAST_FAIL_UNSAFE_EXTENSION_CALL = 26,
  MD_FAST_FAIL_DEPRECATED_SERVICE_INVOKED = 27,
  MD_FAST_FAIL_INVALID_BUFFER_ACCESS = 28,
  MD_FAST_FAIL_INVALID_BALANCED_TREE = 29,
  MD_FAST_FAIL_INVALID_NEXT_THREAD = 30,
  MD_FAST_FAIL_GUARD_ICALL_CHECK_SUPPRESSED = 31,
  MD_FAST_FAIL_APCS_DISABLED = 32,
  MD_FAST_FAIL_INVALID_IDLE_STATE = 33,
  MD_FAST_FAIL_MRDATA_PROTECTION_FAILURE = 34,
  MD_FAST_FAIL_UNEXPECTED_HEAP_EXCEPTION = 35,
  MD_FAST_FAIL_INVALID_LOCK_STATE = 36,
  MD_FAST_FAIL_GUARD_JUMPTABLE = 37,
  MD_FAST_FAIL_INVALID_LONGJUMP_TARGET = 38,
  MD_FAST_FAIL_INVALID_DISPATCH_CONTEXT = 39,
  MD_FAST_FAIL_INVALID_THREAD = 40,
  MD_FAST_FAIL_INVALID_SYSCALL_NUMBER = 41,
  MD_FAST_FAIL_INVALID_FILE_OPERATION = 42,
  MD_FAST_FAIL_LPAC_ACCESS_DENIED = 43,
  MD_FAST_FAIL_GUARD_SS_FAILURE = 44,
  MD_FAST_FAIL_LOADER_CONTINUITY_FAILURE = 45,
  MD_FAST_FAIL_GUARD_EXPORT_SUPPRESSION_FAILURE = 46,
  MD_FAST_FAIL_INVALID_CONTROL_STACK = 47,
  MD_FAST_FAIL_SET_CONTEXT_DENIED = 48,
  MD_FAST_FAIL_INVALID_IAT = 49,
  MD_FAST_FAIL_HEAP_METADATA_CORRUPTION = 50,
  MD_FAST_FAIL_PAYLOAD_RESTRICTION_VIOLATION = 51,
  MD_FAST_FAIL_LOW_LABEL_ACCESS_DENIED = 52,
  MD_FAST_FAIL_ENCLAVE_CALL_FAILURE = 53,
  MD_FAST_FAIL_UNHANDLED_LSS_EXCEPTON = 54,
  MD_FAST_FAIL_ADMINLESS_ACCESS_DENIED = 55,
  MD_FAST_FAIL_UNEXPECTED_CALL = 56,
  MD_FAST_FAIL_CONTROL_INVALID_RETURN_ADDRESS = 57,
  MD_FAST_FAIL_UNEXPECTED_HOST_BEHAVIOR = 58,
  MD_FAST_FAIL_FLAGS_CORRUPTION = 59,
  MD_FAST_FAIL_VEH_CORRUPTION = 60,
  MD_FAST_FAIL_ETW_CORRUPTION = 61,
  MD_FAST_FAIL_RIO_ABORT = 62,
  MD_FAST_FAIL_INVALID_PFN = 63,
  MD_FAST_FAIL_GUARD_ICALL_CHECK_FAILURE_XFG = 64,
  MD_FAST_FAIL_CAST_GUARD = 65,
  MD_FAST_FAIL_HOST_VISIBILITY_CHANGE = 66,
  MD_FAST_FAIL_KERNEL_CET_SHADOW_STACK_ASSIST = 67,
  MD_FAST_FAIL_PATCH_CALLBACK_FAILED = 68,
  MD_FAST_FAIL_NTDLL_PATCH_FAILED = 69,
  MD_FAST_FAIL_INVALID_FLS_DATA = 70
} MDFastFailSubcodeTypeWin;

#endif  /* GOOGLE_BREAKPAD_COMMON_MINIDUMP_EXCEPTION_WIN32_H__ */
