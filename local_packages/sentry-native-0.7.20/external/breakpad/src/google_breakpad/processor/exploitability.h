// Copyright 2010 Google LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// exploitability_engine.h: Generic exploitability engine.
//
// The Exploitability class is an abstract base class providing common
// generic methods that apply to exploitability engines for specific platforms.
// Specific implementations will extend this class by providing run
// methods to fill in the exploitability_ enumeration of the ProcessState
// for a crash.
//
// Author: Cris Neckar

#ifndef GOOGLE_BREAKPAD_PROCESSOR_EXPLOITABILITY_H_
#define GOOGLE_BREAKPAD_PROCESSOR_EXPLOITABILITY_H_

#include "google_breakpad/common/breakpad_types.h"
#include "google_breakpad/processor/minidump.h"
#include "google_breakpad/processor/process_state.h"

namespace google_breakpad {

class Exploitability {
 public:
  virtual ~Exploitability() {}

  static Exploitability *ExploitabilityForPlatform(Minidump *dump,
                                                   ProcessState *process_state);

  // The boolean parameter signals whether the exploitability engine is
  // enabled to call out to objdump for disassembly. This is disabled by
  // default. It is used to check the identity of the instruction that
  // caused the program to crash. This should not be enabled if there are
  // portability concerns.
  static Exploitability *ExploitabilityForPlatform(Minidump *dump,
                                                   ProcessState *process_state,
                                                   bool enable_objdump);

  ExploitabilityRating CheckExploitability();
  bool AddressIsAscii(uint64_t);

 protected:
  Exploitability(Minidump *dump,
                 ProcessState *process_state);

  Minidump *dump_;
  ProcessState *process_state_;
  SystemInfo *system_info_;

 private:
  virtual ExploitabilityRating CheckPlatformExploitability() = 0;
};

}  // namespace google_breakpad

#endif  // GOOGLE_BREAKPAD_PROCESSOR_EXPLOITABILITY_H_
