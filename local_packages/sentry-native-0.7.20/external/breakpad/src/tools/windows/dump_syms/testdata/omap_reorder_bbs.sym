MODULE windows x86 99302CF6F3C244F2B8E048BA0268A12F1 omap_reorder_bbs.pdb
FILE 1 c:\src\breakpad\src\src\tools\windows\dump_syms\testdata\dump_syms_regtest.cc
FILE 2 f:\dd\public\sdk\inc\internal\pebteb.h
FILE 3 f:\dd\public\sdk\inc\internal\ntldr.h
FILE 4 f:\dd\public\sdk\inc\internal\ntconfig.h
FILE 5 f:\dd\public\sdk\inc\internal\ntregapi.h
FILE 6 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\stdarg.h
FILE 7 f:\dd\public\ddk\inc\ntdef.h
FILE 8 f:\dd\public\sdk\inc\internal\ntmmapi.h
FILE 9 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\ctype.h
FILE 10 f:\dd\public\sdk\inc\pshpack1.h
FILE 11 f:\dd\public\sdk\inc\internal\nxi386.h
FILE 12 f:\dd\public\ddk\inc\poppack.h
FILE 13 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\process.h
FILE 14 f:\dd\public\sdk\inc\pshpack8.h
FILE 15 f:\dd\public\ddk\inc\ntpoapi.h
FILE 16 f:\dd\public\sdk\inc\internal\ntexapi.h
FILE 17 f:\dd\public\sdk\inc\poppack.h
FILE 18 f:\dd\public\ddk\inc\ntimage.h
FILE 19 f:\dd\public\ddk\inc\pshpack4.h
FILE 20 f:\dd\public\sdk\inc\pshpack2.h
FILE 21 f:\dd\public\ddk\inc\ntnls.h
FILE 22 f:\dd\public\sdk\inc\internal\ntelfapi.h
FILE 23 f:\dd\public\sdk\inc\internal\ntpsapi.h
FILE 24 f:\dd\public\sdk\inc\internal\nti386.h
FILE 25 f:\dd\public\sdk\inc\specstrings.h
FILE 26 f:\dd\public\sdk\inc\sal_supp.h
FILE 27 f:\dd\public\sdk\inc\specstrings_supp.h
FILE 28 f:\dd\public\sdk\inc\specstrings_strict.h
FILE 29 f:\dd\public\sdk\inc\specstrings_undef.h
FILE 30 f:\dd\public\sdk\inc\driverspecs.h
FILE 31 f:\dd\public\sdk\inc\sdv_driverspecs.h
FILE 32 f:\dd\public\sdk\inc\basetsd.h
FILE 33 f:\dd\public\sdk\inc\internal\ntpnpapi.h
FILE 34 f:\dd\public\sdk\inc\cfg.h
FILE 35 f:\dd\public\sdk\inc\internal\ntxcapi.h
FILE 36 f:\dd\public\sdk\inc\guiddef.h
FILE 37 f:\dd\public\sdk\inc\internal\nt.h
FILE 38 f:\dd\public\sdk\inc\ntstatus.h
FILE 39 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\excpt.h
FILE 40 f:\dd\public\sdk\inc\internal\ntkeapi.h
FILE 41 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\crtdefs.h
FILE 42 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\sal.h
FILE 43 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\codeanalysis\sourceannotations.h
FILE 44 f:\dd\public\sdk\inc\internal\ntobapi.h
FILE 45 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\string.h
FILE 46 f:\dd\public\sdk\inc\internal\ntioapi.h
FILE 47 f:\dd\public\ddk\inc\devioctl.h
FILE 48 f:\dd\public\sdk\inc\internal\ntseapi.h
FILE 49 f:\dd\public\ddk\inc\mce.h
FILE 50 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\misc\i386\chandler4.c
FILE 51 f:\dd\public\sdk\inc\pshpack4.h
FILE 52 f:\dd\public\devdiv\inc\ddbanned.h
FILE 53 f:\dd\public\sdk\inc\internal\ntlpcapi.h
FILE 54 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\vadefs.h
FILE 55 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\cruntime.h
FILE 56 f:\dd\public\sdk\inc\internal\ntiolog.h
FILE 57 f:\dd\vctools\crt_bld\SELF_X86\crt\prebuild\misc\i386\exsup.asm
FILE 58 f:\dd\vctools\crt_bld\SELF_X86\crt\prebuild\h\pversion.inc
FILE 59 f:\dd\vctools\crt_bld\SELF_X86\crt\prebuild\h\cmacros.inc
FILE 60 f:\dd\vctools\crt_bld\SELF_X86\crt\prebuild\h\exsup.inc
FILE 61 f:\dd\vctools\crt_bld\SELF_X86\crt\prebuild\misc\i386\nlgsupp.asm
FILE 62 f:\dd\vctools\crt_bld\SELF_X86\crt\prebuild\misc\i386\exsup4.asm
FILE 64 f:\dd\vctools\crt_bld\SELF_X86\crt\prebuild\misc\i386\sehprolg4.asm
FILE 65 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\string\i386\p4_memcpy.c
FILE 69 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\string\i386\memcmp.c
FILE 73 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\string\i386\p4_memset.c
FILE 77 f:\dd\public\sdk\inc\winreg.h
FILE 78 f:\dd\public\sdk\inc\imm.h
FILE 79 f:\dd\public\sdk\inc\wingdi.h
FILE 80 f:\dd\vctools\crt_bld\self_x86\crt\src\stdlib.h
FILE 81 f:\dd\public\sdk\inc\winerror.h
FILE 82 f:\dd\public\sdk\inc\ktmtypes.h
FILE 84 f:\dd\vctools\crt_bld\self_x86\crt\src\stdarg.h
FILE 85 f:\dd\public\sdk\inc\windef.h
FILE 86 f:\dd\public\sdk\inc\winbase.h
FILE 88 f:\dd\vctools\crt_bld\self_x86\crt\src\string.h
FILE 89 f:\dd\public\sdk\inc\winuser.h
FILE 91 f:\dd\public\sdk\inc\winnetwk.h
FILE 92 f:\dd\public\sdk\inc\winnt.h
FILE 93 f:\dd\public\sdk\inc\wnnc.h
FILE 94 f:\dd\vctools\crt_bld\self_x86\crt\src\ctype.h
FILE 96 f:\dd\public\sdk\inc\winnls.h
FILE 98 f:\dd\vctools\crt_bld\self_x86\crt\src\mtdll.h
FILE 99 f:\dd\vctools\crt_bld\self_x86\crt\src\crtdefs.h
FILE 100 f:\dd\vctools\crt_bld\self_x86\crt\src\sal.h
FILE 101 f:\dd\vctools\crt_bld\self_x86\crt\src\codeanalysis\sourceannotations.h
FILE 102 f:\dd\public\sdk\inc\winver.h
FILE 103 f:\dd\public\sdk\inc\verrsrc.h
FILE 104 f:\dd\public\sdk\inc\wincon.h
FILE 105 f:\dd\public\sdk\inc\stralign.h
FILE 107 f:\dd\public\sdk\inc\mcx.h
FILE 108 f:\dd\vctools\crt_bld\self_x86\crt\src\atox.c
FILE 109 f:\dd\vctools\crt_bld\self_x86\crt\src\limits.h
FILE 110 f:\dd\public\sdk\inc\windows.h
FILE 111 f:\dd\vctools\crt_bld\self_x86\crt\src\setlocal.h
FILE 112 f:\dd\public\sdk\inc\sdkddkver.h
FILE 113 f:\dd\vctools\crt_bld\self_x86\crt\src\oscalls.h
FILE 114 f:\dd\vctools\crt_bld\self_x86\crt\src\excpt.h
FILE 120 f:\dd\public\sdk\inc\reason.h
FILE 123 f:\dd\public\sdk\inc\kernelspecs.h
FILE 125 f:\dd\vctools\crt_bld\self_x86\crt\src\tchar.h
FILE 126 f:\dd\vctools\crt_bld\self_x86\crt\src\mbstring.h
FILE 127 f:\dd\vctools\crt_bld\self_x86\crt\src\mbctype.h
FILE 128 f:\dd\public\sdk\inc\ime_cmodes.h
FILE 130 f:\dd\vctools\crt_bld\self_x86\crt\src\vadefs.h
FILE 131 f:\dd\vctools\crt_bld\self_x86\crt\src\cruntime.h
FILE 132 f:\dd\public\sdk\inc\tvout.h
FILE 145 f:\dd\vctools\crt_bld\self_x86\crt\src\internal_securecrt.h
FILE 152 f:\dd\vctools\crt_bld\self_x86\crt\src\internal.h
FILE 164 f:\dd\vctools\crt_bld\self_x86\crt\src\crtdbg.h
FILE 165 f:\dd\vctools\crt_bld\self_x86\crt\src\xtoa.c
FILE 178 f:\dd\vctools\crt_bld\self_x86\crt\src\xtoas.c
FILE 182 f:\dd\vctools\crt_bld\self_x86\crt\src\errno.h
FILE 224 f:\dd\vctools\crt_bld\self_x86\crt\src\dosmap.c
FILE 250 f:\dd\vctools\crt_bld\self_x86\crt\src\rtcsup.h
FILE 251 f:\dd\vctools\crt_bld\self_x86\crt\src\rtcapi.h
FILE 265 f:\dd\vctools\crt_bld\self_x86\crt\src\winheap.h
FILE 281 f:\dd\vctools\crt_bld\self_x86\crt\src\malloc.h
FILE 291 f:\dd\vctools\crt_bld\self_x86\crt\src\calloc_impl.c
FILE 299 f:\dd\vctools\crt_bld\self_x86\crt\src\dbgint.h
FILE 333 f:\dd\vctools\crt_bld\self_x86\crt\src\crtheap.c
FILE 389 f:\dd\vctools\crt_bld\self_x86\crt\src\free.c
FILE 453 f:\dd\vctools\crt_bld\self_x86\crt\src\heapinit.c
FILE 498 f:\dd\vctools\crt_bld\self_x86\crt\src\rterr.h
FILE 504 f:\dd\vctools\crt_bld\self_x86\crt\src\malloc.c
FILE 561 f:\dd\vctools\crt_bld\self_x86\crt\src\msize.c
FILE 618 f:\dd\vctools\crt_bld\self_x86\crt\src\realloc.c
FILE 677 f:\dd\vctools\crt_bld\self_x86\crt\src\recalloc.c
FILE 731 f:\dd\vctools\crt_bld\self_x86\crt\src\_newmode.c
FILE 774 f:\dd\vctools\crt_bld\self_x86\crt\src\msdos.h
FILE 777 f:\dd\vctools\crt_bld\self_x86\crt\src\stddef.h
FILE 792 f:\dd\vctools\crt_bld\self_x86\crt\src\ioinit.c
FILE 844 f:\dd\vctools\crt_bld\self_x86\crt\src\intel\loadcfg.c
FILE 892 f:\dd\vctools\crt_bld\self_x86\crt\src\intel\secchk.c
FILE 904 f:\dd\vctools\crt_bld\self_x86\crt\src\process.h
FILE 943 f:\dd\vctools\crt_bld\self_x86\crt\src\a_env.c
FILE 960 f:\dd\vctools\crt_bld\self_x86\crt\src\awint.h
FILE 975 f:\dd\vctools\crt_bld\self_x86\crt\src\signal.h
FILE 1011 f:\dd\vctools\crt_bld\self_x86\crt\src\abort.c
FILE 1039 f:\dd\vctools\crt_bld\self_x86\crt\src\swprintf.inl
FILE 1053 f:\dd\vctools\crt_bld\self_x86\crt\src\crtmbox.c
FILE 1063 f:\dd\vctools\crt_bld\self_x86\crt\src\crtmboxw.c
FILE 1065 f:\dd\vctools\crt_bld\self_x86\crt\src\wchar.h
FILE 1069 f:\dd\vctools\crt_bld\self_x86\crt\src\wtime.inl
FILE 1120 f:\dd\vctools\crt_bld\self_x86\crt\src\ctype.c
FILE 1145 f:\dd\vctools\crt_bld\self_x86\crt\src\dbghook.c
FILE 1181 f:\dd\vctools\crt_bld\self_x86\crt\src\errmode.c
FILE 1244 f:\dd\vctools\crt_bld\self_x86\crt\src\getqloc.c
FILE 1288 f:\dd\vctools\crt_bld\self_x86\crt\src\locale.h
FILE 1301 f:\dd\vctools\crt_bld\self_x86\crt\src\glstatus.c
FILE 1344 f:\dd\vctools\crt_bld\self_x86\crt\src\gs_cookie.c
FILE 1392 f:\dd\vctools\crt_bld\self_x86\crt\src\gs_report.c
FILE 1413 f:\binaries.x86ret\inc\mm3dnow.h
FILE 1415 f:\binaries.x86ret\inc\ammintrin.h
FILE 1424 f:\binaries.x86ret\inc\immintrin.h
FILE 1426 f:\binaries.x86ret\inc\wmmintrin.h
FILE 1427 f:\binaries.x86ret\inc\nmmintrin.h
FILE 1428 f:\binaries.x86ret\inc\smmintrin.h
FILE 1429 f:\binaries.x86ret\inc\tmmintrin.h
FILE 1430 f:\binaries.x86ret\inc\pmmintrin.h
FILE 1431 f:\binaries.x86ret\inc\emmintrin.h
FILE 1432 f:\binaries.x86ret\inc\xmmintrin.h
FILE 1433 f:\binaries.x86ret\inc\mmintrin.h
FILE 1455 f:\dd\vctools\crt_bld\self_x86\crt\src\gs_support.c
FILE 1467 f:\dd\vctools\crt_bld\self_x86\crt\src\intrin.h
FILE 1468 f:\dd\vctools\crt_bld\self_x86\crt\src\setjmp.h
FILE 1508 f:\dd\vctools\crt_bld\self_x86\crt\src\initcoll.c
FILE 1568 f:\dd\vctools\crt_bld\self_x86\crt\src\initctyp.c
FILE 1627 f:\dd\vctools\crt_bld\self_x86\crt\src\inithelp.c
FILE 1685 f:\dd\vctools\crt_bld\self_x86\crt\src\initmon.c
FILE 1737 f:\dd\vctools\crt_bld\self_x86\crt\src\nlsint.h
FILE 1742 f:\dd\vctools\crt_bld\self_x86\crt\src\initnum.c
FILE 1800 f:\dd\vctools\crt_bld\self_x86\crt\src\inittime.c
FILE 1855 f:\dd\vctools\crt_bld\self_x86\crt\src\lconv.c
FILE 1913 f:\dd\vctools\crt_bld\self_x86\crt\src\localref.c
FILE 1962 f:\dd\vctools\crt_bld\self_x86\crt\src\sect_attribs.h
FILE 1969 f:\dd\vctools\crt_bld\self_x86\crt\src\onexit.c
FILE 1989 f:\dd\vctools\crt_bld\self_x86\crt\src\nlsdata1.c
FILE 2036 f:\dd\vctools\crt_bld\self_x86\crt\src\nlsdata2.c
FILE 2079 f:\dd\vctools\crt_bld\self_x86\crt\src\pesect.c
FILE 2128 f:\dd\vctools\crt_bld\self_x86\crt\src\purevirt.c
FILE 2186 f:\dd\vctools\crt_bld\self_x86\crt\src\rand_s.c
FILE 2250 f:\dd\vctools\crt_bld\self_x86\crt\src\setlocal.c
FILE 2311 f:\dd\vctools\crt_bld\self_x86\crt\src\winsig.c
FILE 2314 f:\dd\vctools\crt_bld\self_x86\crt\src\float.h
FILE 2315 f:\dd\vctools\crt_bld\self_x86\crt\src\crtwrn.h
FILE 2369 f:\dd\vctools\crt_bld\self_x86\crt\src\winxfltr.c
FILE 2394 f:\dd\vctools\crt_bld\self_x86\crt\src\fltintrn.h
FILE 2419 f:\dd\vctools\crt_bld\self_x86\crt\src\cmiscdat.c
FILE 2445 f:\dd\vctools\crt_bld\self_x86\crt\src\intel\strncmp.c
FILE 2468 f:\dd\vctools\crt_bld\self_x86\crt\src\tcscat_s.inl
FILE 2493 f:\dd\vctools\crt_bld\self_x86\crt\src\strcat_s.c
FILE 2523 f:\dd\vctools\crt_bld\self_x86\crt\src\tcscpy_s.inl
FILE 2548 f:\dd\vctools\crt_bld\self_x86\crt\src\strcpy_s.c
FILE 2578 f:\dd\vctools\crt_bld\self_x86\crt\src\tcsncpy_s.inl
FILE 2603 f:\dd\vctools\crt_bld\self_x86\crt\src\strncpy_s.c
FILE 2658 f:\dd\vctools\crt_bld\self_x86\crt\src\wcscat_s.c
FILE 2713 f:\dd\vctools\crt_bld\self_x86\crt\src\wcscpy_s.c
FILE 2728 f:\dd\vctools\crt_bld\self_x86\crt\src\wcslen.c
FILE 2776 f:\dd\vctools\crt_bld\self_x86\crt\src\wcsncpy_s.c
FILE 2787 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\memcpy.asm
FILE 2788 f:\dd\vctools\crt_bld\SELF_X86\crt\src\cruntime.inc
FILE 2789 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\memset.asm
FILE 2791 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\strcmp.asm
FILE 2793 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\strcspn.asm
FILE 2794 f:\dd\vctools\crt_bld\SELF_X86\crt\src\Intel\STRSPN.ASM
FILE 2796 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\strlen.asm
FILE 2798 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\_strnicm.asm
FILE 2800 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\strpbrk.asm
FILE 2803 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\llmul.asm
FILE 2805 f:\dd\vctools\crt_bld\SELF_X86\crt\src\mm.inc
FILE 2806 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\ulldvrm.asm
FILE 2820 f:\dd\vctools\crt_bld\self_x86\crt\src\handler.cpp
FILE 2841 f:\dd\vctools\crt_bld\self_x86\crt\src\new.h
FILE 2875 f:\dd\vctools\crt_bld\self_x86\crt\src\delete.cpp
FILE 2931 f:\dd\vctools\crt_bld\self_x86\crt\src\_wctype.c
FILE 2987 f:\dd\vctools\crt_bld\self_x86\crt\src\iswctype.c
FILE 2998 f:\dd\vctools\crt_bld\self_x86\crt\src\stdio.h
FILE 3045 f:\dd\vctools\crt_bld\self_x86\crt\src\isctype.c
FILE 3106 f:\dd\vctools\crt_bld\self_x86\crt\src\strtol.c
FILE 3163 f:\dd\vctools\crt_bld\self_x86\crt\src\strtoq.c
FILE 3218 f:\dd\vctools\crt_bld\self_x86\crt\src\tolower.c
FILE 3271 f:\dd\vctools\crt_bld\self_x86\crt\src\ismbbyte.c
FILE 3305 f:\dd\vctools\crt_bld\self_x86\crt\src\mbdata.h
FILE 3326 f:\dd\vctools\crt_bld\self_x86\crt\src\mbctype.c
FILE 3386 f:\dd\vctools\crt_bld\self_x86\crt\src\a_loc.c
FILE 3447 f:\dd\vctools\crt_bld\self_x86\crt\src\a_map.c
FILE 3507 f:\dd\vctools\crt_bld\self_x86\crt\src\a_str.c
FILE 3583 f:\dd\vctools\crt_bld\self_x86\crt\src\invarg.c
FILE 3626 f:\dd\vctools\crt_bld\self_x86\crt\src\stricmp.c
FILE 3682 f:\dd\vctools\crt_bld\self_x86\crt\src\strnicmp.c
FILE 3774 f:\dd\vctools\crt_bld\self_x86\crt\src\tidtable.c
FILE 3778 f:\dd\vctools\crt_bld\self_x86\crt\src\memory.h
FILE 3838 f:\dd\vctools\crt_bld\self_x86\crt\src\stdenvp.c
FILE 3860 f:\dd\vctools\crt_bld\self_x86\crt\src\dos.h
FILE 3891 f:\dd\vctools\crt_bld\self_x86\crt\src\stdargv.c
FILE 3954 f:\dd\vctools\crt_bld\self_x86\crt\src\mlock.c
FILE 3998 f:\dd\vctools\crt_bld\self_x86\crt\src\cmsgs.h
FILE 4012 f:\dd\vctools\crt_bld\self_x86\crt\src\crt0msg.c
FILE 4072 f:\dd\vctools\crt_bld\self_x86\crt\src\crt0init.c
FILE 4123 f:\dd\vctools\crt_bld\self_x86\crt\src\crt0fp.c
FILE 4186 f:\dd\vctools\crt_bld\self_x86\crt\src\crt0dat.c
FILE 4250 f:\dd\vctools\crt_bld\self_x86\crt\src\crt0.c
FILE 4274 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\alloca16.asm
FILE 4276 f:\dd\vctools\crt_bld\SELF_X86\crt\src\INTEL\chkstk.asm
FILE 4289 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\errno.h
FILE 4293 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\internal.h
FILE 4294 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\limits.h
FILE 4295 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\mtdll.h
FILE 4309 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\sect_attribs.h
FILE 4315 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\tran\i386\cpu_disp.c
FILE 4327 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\crtdbg.h
FILE 4340 f:\dd\vctools\langapi\undname\undname.cxx
FILE 4345 f:\dd\vctools\langapi\undname\undname.inl
FILE 4347 f:\dd\vctools\langapi\undname\utf8.h
FILE 4355 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\swprintf.inl
FILE 4365 f:\dd\vctools\langapi\undname\undname.hxx
FILE 4366 f:\dd\vctools\langapi\undname\undname.h
FILE 4367 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\stdlib.h
FILE 4368 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\stdio.h
FILE 4396 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\eh.h
FILE 4397 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\eh\unhandld.cpp
FILE 4401 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\ehhooks.h
FILE 4405 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\ehassert.h
FILE 4427 f:\dd\vctools\langapi\include\ehdata.h
FILE 4429 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\stddef.h
FILE 4449 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\stdhpp\exception
FILE 4472 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\malloc.h
FILE 4473 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\eh\typname.cpp
FILE 4475 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\stdhpp\cstddef
FILE 4487 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\typeinfo.h
FILE 4488 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\stdhpp\typeinfo
FILE 4490 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\stdhpp\xstddef
FILE 4491 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\stdhpp\yvals.h
FILE 4492 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\stdhpp\use_ansi.h
FILE 4493 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\dbgint.h
FILE 4496 f:\dd\public\internal\vctools\include\undname.h
FILE 4531 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\eh\typinfo.cpp
FILE 4591 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\eh\hooks.cpp
FILE 4643 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\rtc\initsect.cpp
FILE 4664 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\rtcapi.h
FILE 4680 f:\dd\vctools\crt_bld\self_x86\crt\prebuild\h\rtcpriv.h
FUNC 2cec 54 0 main
2cec 6 57 1
2cf2 8 58 1
2cfa e 59 1
2d08 8 60 1
2d10 b 61 1
2d1b f 62 1
2d2a 12 64 1
2d3c 4 65 1
FUNC 4b70 a 0 static int google_breakpad::i()
4b70 3 51 1
4b73 5 52 1
4b78 2 53 1
FUNC 25a8 21 0 google_breakpad::C::C()
25a8 21 37 1
FUNC 131c 14 0 google_breakpad::C::~C()
131c 14 38 1
FUNC 1b94 16 0 google_breakpad::C::set_member(int)
1b94 16 40 1
FUNC 11e8 1e 0 google_breakpad::C::f()
11e8 1e 43 1
FUNC 1b3c 10 0 google_breakpad::C::g()
1b3c 10 44 1
FUNC 669c 7 0 google_breakpad::C::h(google_breakpad::C const &)
669c 7 45 1
FUNC 16d4 20 0 google_breakpad::C::`scalar deleting destructor'(unsigned int)
FUNC 211c c 0 google_breakpad::C::`scalar deleting destructor'(unsigned int)
FUNC 5458 9 0 google_breakpad::C::`scalar deleting destructor'(unsigned int)
FUNC 40f4 10 0 type_info::~type_info()
40f4 2 49 4531
40f6 d 50 4531
4103 1 51 4531
FUNC 1fd4 1c 0 type_info::`scalar deleting destructor'(unsigned int)
FUNC 46d8 7 0 type_info::`scalar deleting destructor'(unsigned int)
FUNC 65a8 7 0 type_info::`scalar deleting destructor'(unsigned int)
FUNC 12a0 b 0 operator delete(void *)
12a0 5 20 2875
12a5 1 24 2875
12a6 5 23 2875
FUNC 198c 4 0 static void fast_error_exit(int)
FUNC 1ff0 18 0 static void fast_error_exit(int)
FUNC 455c 12 0 static void fast_error_exit(int)
FUNC 4d0c 5 0 static void fast_error_exit(int)
1ff0 5 326 4250
1ff5 13 335 4250
4d0c 5 337 4250
455c 8 339 4250
198c 2 340 4250
4564 a 340 4250
198e 2 341 4250
FUNC 38be 161 0 static int __tmainCRTStartup()
38be c 196 4250
38ca a 214 4250
38d4 b 216 4250
38df 49 223 4250
3928 9 225 4250
3931 8 226 4250
3939 9 228 4250
3942 8 229 4250
394a 5 238 4250
394f 3 246 4250
3952 9 248 4250
395b 8 249 4250
3963 b 252 4250
396e a 255 4250
3978 9 257 4250
3981 8 258 4250
3989 9 259 4250
3992 8 260 4250
399a 8 262 4250
39a2 4 263 4250
39a6 7 264 4250
39ad a 277 4250
39b7 18 278 4250
39cf 5 281 4250
39d4 6 282 4250
39da 5 284 4250
39df 2 286 4250
39e1 17 287 4250
39f8 6 293 4250
39fe 6 295 4250
3a04 6 296 4250
3a0a 5 298 4250
3a0f 7 300 4250
3a16 3 302 4250
3a19 6 303 4250
FUNC 620c a 0 mainCRTStartup
620c 0 179 4250
620c 5 186 4250
6211 5 188 4250
FUNC 66c3 70 0 type_info::_Type_info_dtor(type_info *)
66c3 c 62 4473
66cf 8 63 4473
66d7 4 64 4473
66db a 65 4473
66e5 d 70 4473
66f2 4 72 4473
66f6 4 74 4473
66fa 6 79 4473
6700 7 80 4473
6707 9 94 4473
6710 4 101 4473
6714 c 103 4473
6720 6 107 4473
6726 2 83 4473
6728 2 72 4473
672a 9 104 4473
FUNC 27ab 88 0 strcmp
27ab 0 65 2791
27ab 4 73 2791
27af 4 74 2791
27b3 6 76 2791
27b9 2 77 2791
27bb 2 81 2791
27bd 2 83 2791
27bf 2 84 2791
27c1 2 85 2791
27c3 2 86 2791
27c5 3 87 2791
27c8 2 88 2791
27ca 2 89 2791
27cc 2 90 2791
27ce 3 92 2791
27d1 3 94 2791
27d4 2 95 2791
27d6 2 96 2791
27d8 2 97 2791
27da 3 98 2791
27dd 2 99 2791
27df 3 100 2791
27e2 3 101 2791
27e5 2 102 2791
27e7 4 103 2791
27eb 2 107 2791
27ed 2 108 2791
27ef 2 115 2791
27f1 2 116 2791
27f3 3 117 2791
27f6 1 118 2791
27f7 6 122 2791
27fd 2 123 2791
27ff 2 125 2791
2801 3 126 2791
2804 2 127 2791
2806 2 128 2791
2808 3 129 2791
280b 2 130 2791
280d 2 131 2791
280f 6 133 2791
2815 2 134 2791
2817 3 139 2791
281a 3 140 2791
281d 2 141 2791
281f 2 142 2791
2821 2 143 2791
2823 2 144 2791
2825 3 145 2791
2828 2 146 2791
282a 2 147 2791
282c 2 148 2791
282e 3 149 2791
2831 2 150 2791
FUNC 26fc 14 0 free
FUNC 2bcc 2 0 free
FUNC 3888 18 0 free
FUNC 3ea0 1f 0 free
26fc 5 40 389
2701 f 45 389
3ea0 11 50 389
3eb1 e 51 389
3888 18 53 389
2bcc 2 55 389
FUNC 199c 10 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
FUNC 2380 6 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
FUNC 3ce8 10 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
FUNC 4448 14 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
FUNC 4778 1c 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
FUNC 57d0 10 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
FUNC 5a40 5 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
FUNC 642c 10 0 __CxxUnhandledExceptionFilter(_EXCEPTION_POINTERS *)
4778 5 67 4397
199c 10 68 4397
3ce8 10 68 4397
4448 14 68 4397
477d 17 68 4397
57d0 10 68 4397
642c 10 68 4397
5a40 5 69 4397
2380 2 72 4397
2382 4 73 4397
FUNC 5a28 e 0 __CxxSetUnhandledExceptionFilter
5a28 0 86 4397
5a28 b 89 4397
5a33 2 90 4397
5a35 1 91 4397
FUNC 1db0 5 0 __crtCorExitProcess
FUNC 21a0 1e 0 __crtCorExitProcess
FUNC 24cc 1a 0 __crtCorExitProcess
FUNC 4c14 2 0 __crtCorExitProcess
21a0 5 675 4186
21a5 b 679 4186
21b0 e 680 4186
24cc c 681 4186
24d8 e 682 4186
1db0 5 683 4186
4c14 2 693 4186
FUNC 6a9b 18 0 __crtExitProcess
6a9b 5 698 4186
6aa0 9 699 4186
6aa9 a 708 4186
FUNC 2100 9 0 _lockexit
2100 0 758 4186
2100 8 759 4186
2108 1 760 4186
FUNC 2b50 9 0 _unlockexit
2b50 0 784 4186
2b50 8 785 4186
2b58 1 786 4186
FUNC 4594 33 0 _init_pointers
4594 3 809 4186
4597 7 810 4186
459e 6 812 4186
45a4 6 813 4186
45aa 6 814 4186
45b0 6 815 4186
45b6 6 816 4186
45bc a 817 4186
45c6 1 818 4186
FUNC 1bc8 3 0 _initterm_e
FUNC 1de4 e 0 _initterm_e
FUNC 22bc 2 0 _initterm_e
FUNC 2944 10 0 _initterm_e
FUNC 3dd4 3 0 _initterm_e
FUNC 3e80 f 0 _initterm_e
FUNC 6568 10 0 _initterm_e
2944 6 908 4186
1de4 e 917 4186
294a a 917 4186
6568 10 922 4186
22bc 2 923 4186
3dd4 3 924 4186
1bc8 1 917 4186
3e80 f 917 4186
1bc9 2 928 4186
FUNC 1d44 c 0 _cinit
FUNC 2470 10 0 _cinit
FUNC 2558 27 0 _cinit
FUNC 2660 14 0 _cinit
FUNC 269c 18 0 _cinit
FUNC 27a4 2 0 _cinit
FUNC 34ec 24 0 _cinit
FUNC 48dc 10 0 _cinit
FUNC 4bf8 2 0 _cinit
FUNC 4dd4 18 0 _cinit
FUNC 5b74 a 0 _cinit
FUNC 69e4 18 0 _cinit
FUNC 6f60 2 0 _cinit
69e4 5 258 4186
4dd4 18 268 4186
69e9 13 268 4186
5b74 a 270 4186
34ec 5 272 4186
34f1 11 278 4186
3502 2 279 4186
3504 c 280 4186
2558 c 283 4186
2470 10 288 4186
2564 1b 288 4186
27a4 2 288 4186
48dc 10 288 4186
2660 14 301 4186
269c 18 301 4186
1d44 c 303 4186
4bf8 2 307 4186
6f60 2 308 4186
FUNC 35a7 140 0 static void doexit(int, int, int)
35a7 c 489 4186
35b3 8 507 4186
35bb 4 508 4186
35bf f 510 4186
35ce 5 511 4186
35d3 8 514 4186
35db a 516 4186
35e5 13 532 4186
35f8 4 533 4186
35fc d 534 4186
3609 3 538 4186
360c 3 539 4186
360f 11 547 4186
3620 2 550 4186
3622 4 552 4186
3626 6 559 4186
362c 7 562 4186
3633 2 565 4186
3635 a 567 4186
363f 8 568 4186
3647 a 570 4186
3651 6 573 4186
3657 8 574 4186
365f 5 576 4186
3664 21 582 4186
3685 21 590 4186
36a6 c 608 4186
36b2 6 613 4186
36b8 a 617 4186
36c2 8 619 4186
36ca 8 621 4186
36d2 6 609 4186
36d8 9 610 4186
36e1 6 622 4186
FUNC 6d68 16 0 exit
6d68 5 392 4186
6d6d f 393 4186
6d7c 2 394 4186
FUNC 5464 16 0 _exit
5464 5 400 4186
5469 f 401 4186
5478 2 402 4186
FUNC 1df4 f 0 _cexit
1df4 0 407 4186
1df4 e 408 4186
1e02 1 409 4186
FUNC 5544 f 0 _c_exit
5544 0 414 4186
5544 e 415 4186
5552 1 416 4186
FUNC 3218 1e 0 _amsg_exit
3218 5 439 4186
321d 5 440 4186
3222 9 441 4186
322b b 442 4186
FUNC 1b78 4 0 _GET_RTERRMSG
FUNC 2888 9 0 _GET_RTERRMSG
FUNC 44d0 16 0 _GET_RTERRMSG
FUNC 4858 10 0 _GET_RTERRMSG
FUNC 6410 7 0 _GET_RTERRMSG
6410 5 165 4012
6415 2 168 4012
44d0 16 169 4012
4858 10 168 4012
1b78 2 172 4012
1b7a 2 173 4012
2888 7 170 4012
288f 2 173 4012
FUNC 11c4 12 0 _NMSG_WRITE
FUNC 2028 16 0 _NMSG_WRITE
FUNC 20c0 12 0 _NMSG_WRITE
FUNC 2460 f 0 _NMSG_WRITE
FUNC 2648 f 0 _NMSG_WRITE
FUNC 26c4 1a 0 _NMSG_WRITE
FUNC 349c 27 0 _NMSG_WRITE
FUNC 43a4 1e 0 _NMSG_WRITE
FUNC 4428 a 0 _NMSG_WRITE
FUNC 47c0 2b 0 _NMSG_WRITE
FUNC 49a4 18 0 _NMSG_WRITE
FUNC 4a64 1c 0 _NMSG_WRITE
FUNC 4d50 17 0 _NMSG_WRITE
FUNC 4e7c 39 0 _NMSG_WRITE
FUNC 4f5c 2 0 _NMSG_WRITE
FUNC 5810 25 0 _NMSG_WRITE
FUNC 59cc 16 0 _NMSG_WRITE
FUNC 5d18 34 0 _NMSG_WRITE
FUNC 64e8 5 0 _NMSG_WRITE
FUNC 6648 7 0 _NMSG_WRITE
FUNC 6678 22 0 _NMSG_WRITE
FUNC 6894 11 0 _NMSG_WRITE
FUNC 70ac 18 0 _NMSG_WRITE
4e7c 1b 196 4012
4e97 8 197 4012
4e9f 16 199 4012
11c4 12 226 4012
2028 16 226 4012
59cc 16 226 4012
6894 11 263 4012
5810 25 272 4012
47c0 2b 275 4012
4a64 1c 276 4012
64e8 5 276 4012
6648 7 276 4012
4d50 17 279 4012
5d18 d 281 4012
5d25 27 282 4012
6678 22 285 4012
43a4 1e 286 4012
49a4 18 290 4012
4428 a 272 4012
70ac a 228 4012
2460 f 229 4012
70b6 e 229 4012
4f5c 2 244 4012
26c4 a 246 4012
26ce 10 248 4012
20c0 12 244 4012
349c 27 260 4012
2648 f 294 4012
FUNC 1984 1 0 _FF_MSGBANNER
FUNC 2388 16 0 _FF_MSGBANNER
FUNC 4ab4 16 0 _FF_MSGBANNER
FUNC 4be0 17 0 _FF_MSGBANNER
FUNC 5ac8 13 0 _FF_MSGBANNER
4be0 0 134 4012
2388 16 138 4012
4be0 17 138 4012
5ac8 13 138 4012
4ab4 a 140 4012
4abe c 141 4012
1984 1 143 4012
FUNC 1490 10 0 _XcptFilter
FUNC 1540 3 0 _XcptFilter
FUNC 1720 14 0 _XcptFilter
FUNC 174c 1b 0 _XcptFilter
FUNC 19d8 2 0 _XcptFilter
FUNC 2054 3 0 _XcptFilter
FUNC 2218 9 0 _XcptFilter
FUNC 2440 c 0 _XcptFilter
FUNC 2870 e 0 _XcptFilter
FUNC 2928 c 0 _XcptFilter
FUNC 2ae8 11 0 _XcptFilter
FUNC 2d78 10 0 _XcptFilter
FUNC 356c f 0 _XcptFilter
FUNC 38a8 10 0 _XcptFilter
FUNC 3ed0 10 0 _XcptFilter
FUNC 3fa4 10 0 _XcptFilter
FUNC 44e8 7 0 _XcptFilter
FUNC 4810 17 0 _XcptFilter
FUNC 4cc8 c 0 _XcptFilter
FUNC 4f50 7 0 _XcptFilter
FUNC 5564 e 0 _XcptFilter
FUNC 56d4 10 0 _XcptFilter
FUNC 57a4 e 0 _XcptFilter
FUNC 59f8 16 0 _XcptFilter
FUNC 5bbc e 0 _XcptFilter
FUNC 5ca4 10 0 _XcptFilter
FUNC 5d60 c 0 _XcptFilter
FUNC 5e38 18 0 _XcptFilter
FUNC 5e60 10 0 _XcptFilter
FUNC 6090 c 0 _XcptFilter
FUNC 61f0 7 0 _XcptFilter
FUNC 6228 c 0 _XcptFilter
FUNC 648c c 0 _XcptFilter
FUNC 64dc 5 0 _XcptFilter
FUNC 650c 1a 0 _XcptFilter
FUNC 66a4 c 0 _XcptFilter
FUNC 68e4 10 0 _XcptFilter
FUNC 6ee8 3 0 _XcptFilter
FUNC 6f98 c 0 _XcptFilter
650c 6 195 2369
6512 7 202 2369
6519 d 203 2369
1720 14 208 2369
19d8 2 208 2369
2218 9 208 2369
2870 e 208 2369
4810 17 208 2369
57a4 e 208 2369
5bbc e 210 2369
5e60 3 216 2369
5e63 d 223 2369
4f50 7 224 2369
356c f 232 2369
6f98 4 237 2369
6f9c 8 238 2369
5564 3 244 2369
5567 b 248 2369
174c a 263 2369
1756 11 272 2369
6ee8 3 280 2369
5e38 18 283 2369
59f8 16 310 2369
6090 c 312 2369
56d4 10 314 2369
2440 c 316 2369
5ca4 10 318 2369
5d60 c 320 2369
3fa4 10 322 2369
648c c 324 2369
68e4 10 326 2369
4cc8 c 328 2369
2ae8 10 330 2369
2928 c 332 2369
2d78 10 334 2369
6228 c 336 2369
1490 10 338 2369
66a4 c 340 2369
3ed0 10 342 2369
61f0 7 344 2369
38a8 8 353 2369
38b0 3 358 2369
38b3 5 360 2369
44e8 4 365 2369
44ec 3 366 2369
64dc 1 366 2369
64dd 4 372 2369
1540 3 374 2369
2054 1 374 2369
2af8 1 374 2369
2055 2 376 2369
FUNC 4139 dc 0 _setenvp
4139 0 77 3838
4139 9 85 3838
4142 6 86 3838
4148 9 91 3838
4151 4 98 3838
4155 8 99 3838
415d 4 110 3838
4161 1 111 3838
4162 b 112 3838
416d 6 108 3838
4173 15 117 3838
4188 2 118 3838
418a 9 121 3838
4193 6 123 3838
4199 9 125 3838
41a2 10 127 3838
41b2 f 133 3838
41c1 3 134 3838
41c4 7 121 3838
41cb b 138 3838
41d6 7 139 3838
41dd 3 142 3838
41e0 a 149 3838
41ea 2 152 3838
41ec 2 138 3838
41ee 3 153 3838
41f1 b 129 3838
41fc 7 130 3838
4203 5 131 3838
4208 d 133 3838
FUNC 1018 8 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 19b4 e 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 19f4 2 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 1dd0 14 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 1fac 28 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2058 f 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 209c 9 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2364 1a 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 253c 4 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2598 9 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 25cc 10 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2718 b 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2730 e 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 28a8 a 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 28fc 2 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 290c 14 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2b34 f 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2bfc 10 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 2d88 7 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 3730 d 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 377c 2 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 37b0 10 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 3c38 24 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 3c84 10 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 3db8 1b 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 3e90 e 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 3efc 13 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4110 12 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 42dc f 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 42f0 10 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4518 6 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4610 7 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4684 11 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 46f8 13 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4848 a 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4994 f 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 49bc e 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4aac 1 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4cb8 f 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4ed8 24 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4f10 3 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 4f30 e 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 501c 10 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 5518 3 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 569c 3 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 57bc 12 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 5a50 10 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 5cc0 14 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 5e84 f 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 6448 f 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 6530 e 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 66b0 13 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 6970 4 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 6aec 7 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
FUNC 6f30 e 0 static void parse_cmdline(char *, char * *, char *, int *, int *)
1fac 6 221 3891
1fb2 9 229 3891
1fbb 2 232 3891
1fbd 17 234 3891
2598 9 234 3891
4f10 3 253 3891
5e84 f 255 3891
66b0 5 257 3891
66b5 9 258 3891
66be 5 259 3891
25cc 2 261 3891
25ce e 262 3891
1018 8 263 3891
3db8 2 265 3891
3dba 19 267 3891
57bc 2 268 3891
57be 10 269 3891
4848 a 270 3891
4aac 1 271 3891
2b34 f 275 3891
42f0 10 275 3891
4cb8 f 275 3891
4ed8 14 275 3891
2730 e 280 3891
291c 4 281 3891
253c 4 284 3891
5cc0 e 289 3891
37b0 10 290 3891
49bc e 290 3891
4610 7 291 3891
6aec 1 278 3891
6aed 6 279 3891
2370 e 294 3891
3c84 10 298 3891
209c 9 299 3891
19f4 2 300 3891
2718 3 314 3891
271b 8 318 3891
28fc 1 321 3891
28fd 1 322 3891
4994 f 319 3891
6448 f 324 3891
42dc f 327 3891
4684 11 328 3891
4eec 10 328 3891
2914 2 329 3891
2916 6 330 3891
3730 d 332 3891
377c 2 335 3891
2058 1 339 3891
4f30 e 339 3891
2059 e 340 3891
5cd0 4 341 3891
501c 10 342 3891
290c 3 341 3891
2bfc 10 346 3891
3c4c 10 346 3891
3e90 e 346 3891
6f30 e 346 3891
19b4 e 351 3891
3efc 13 353 3891
4110 12 353 3891
3c38 b 354 3891
3c43 2 355 3891
5a50 a 357 3891
5a5a 6 358 3891
1dd0 14 359 3891
569c 1 360 3891
569d 2 361 3891
2364 5 364 3891
4518 1 366 3891
4519 5 375 3891
6530 e 379 3891
2d88 7 380 3891
28a8 2 381 3891
28aa 8 382 3891
46f8 13 385 3891
5518 3 386 3891
6970 2 387 3891
6972 2 388 3891
FUNC 1ad8 3 0 _setargv
FUNC 2008 1f 0 _setargv
FUNC 22dc 16 0 _setargv
FUNC 2320 5 0 _setargv
FUNC 2934 3 0 _setargv
FUNC 33cc 17 0 _setargv
FUNC 4254 30 0 _setargv
FUNC 432c 5 0 _setargv
FUNC 4ad0 28 0 _setargv
FUNC 4b8c 2c 0 _setargv
FUNC 4c4c 12 0 _setargv
FUNC 6960 10 0 _setargv
2008 9 88 3891
2011 16 97 3891
432c 5 98 3891
4254 18 104 3891
2934 3 120 3891
426c 18 120 3891
6960 10 120 3891
4ad0 11 127 3891
4ae1 17 134 3891
4c4c 12 134 3891
22dc a 138 3891
22e6 c 140 3891
33cc 9 142 3891
33d5 2 143 3891
33d7 c 144 3891
4b8c 13 151 3891
4b9f c 156 3891
4bab 6 160 3891
4bb1 7 175 3891
1ad8 3 136 3891
2320 3 136 3891
2323 2 176 3891
FUNC 14f8 30 0 __crtGetEnvironmentStringsA
FUNC 23d0 10 0 __crtGetEnvironmentStringsA
FUNC 293c 1 0 __crtGetEnvironmentStringsA
FUNC 2bbc e 0 __crtGetEnvironmentStringsA
FUNC 3524 c 0 __crtGetEnvironmentStringsA
FUNC 3548 f 0 __crtGetEnvironmentStringsA
FUNC 3d10 18 0 __crtGetEnvironmentStringsA
FUNC 3df0 8 0 __crtGetEnvironmentStringsA
FUNC 3dfc 1c 0 __crtGetEnvironmentStringsA
FUNC 3fb4 22 0 __crtGetEnvironmentStringsA
FUNC 436c 12 0 __crtGetEnvironmentStringsA
FUNC 68d4 9 0 __crtGetEnvironmentStringsA
3fb4 a 40 943
3fbe 18 49 943
3df0 8 50 943
3548 f 54 943
2bbc e 55 943
14f8 1 56 943
436c 12 56 943
14f9 1b 70 943
1514 e 74 943
3d10 18 74 943
3dfc 1c 88 943
3524 9 90 943
352d 3 91 943
23d0 7 94 943
23d7 9 95 943
68d4 7 76 943
1524 2 77 943
293c 1 77 943
68db 2 77 943
1526 2 96 943
FUNC 1008 b 0 _ioinit
FUNC 1258 f 0 _ioinit
FUNC 12c8 e 0 _ioinit
FUNC 1548 33 0 _ioinit
FUNC 1708 18 0 _ioinit
FUNC 17a4 10 0 _ioinit
FUNC 197c 2 0 _ioinit
FUNC 1988 3 0 _ioinit
FUNC 22f8 1c 0 _ioinit
FUNC 23e0 4c 0 _ioinit
FUNC 244c 14 0 _ioinit
FUNC 24b8 8 0 _ioinit
FUNC 24e8 1c 0 _ioinit
FUNC 26e0 1c 0 _ioinit
FUNC 2ad0 10 0 _ioinit
FUNC 2b6c f 0 _ioinit
FUNC 2bf0 5 0 _ioinit
FUNC 2d54 a 0 _ioinit
FUNC 2db0 16 0 _ioinit
FUNC 2dcc 8 0 _ioinit
FUNC 31a8 4 0 _ioinit
FUNC 31e0 16 0 _ioinit
FUNC 3260 f 0 _ioinit
FUNC 334c 14 0 _ioinit
FUNC 33ac 12 0 _ioinit
FUNC 3cb0 3 0 _ioinit
FUNC 3d28 6 0 _ioinit
FUNC 3e24 2 0 _ioinit
FUNC 3e34 20 0 _ioinit
FUNC 3e5c 1f 0 _ioinit
FUNC 42a4 f 0 _ioinit
FUNC 4380 f 0 _ioinit
FUNC 4434 12 0 _ioinit
FUNC 4618 6 0 _ioinit
FUNC 47ec 14 0 _ioinit
FUNC 4920 a 0 _ioinit
FUNC 5c70 30 0 _ioinit
FUNC 5d6c 8 0 _ioinit
FUNC 617c 18 0 _ioinit
FUNC 6248 a 0 _ioinit
FUNC 636c e 0 _ioinit
FUNC 63f0 14 0 _ioinit
FUNC 6a74 a 0 _ioinit
FUNC 6ba0 3f 0 _ioinit
FUNC 6c4c 14 0 _ioinit
FUNC 6c84 8 0 _ioinit
FUNC 6f90 3 0 _ioinit
5c70 9 111 792
5c79 a 122 792
5c83 1d 129 792
6c84 8 131 792
3e5c 1f 137 792
6f90 3 134 792
23f4 a 139 792
23fe 3 141 792
2401 6 143 792
2407 4 145 792
240b 3 146 792
23e0 2 147 792
240e 1e 147 792
23e2 f 155 792
2ad0 10 155 792
3e34 2 160 792
3e36 6 166 792
3e3c 2 167 792
3e24 2 173 792
3e3e 16 173 792
2bf0 5 179 792
33ac 12 179 792
1708 18 185 792
26e0 7 199 792
26e7 15 201 792
1010 3 198 792
1548 4 203 792
154c 4 205 792
1550 4 206 792
1554 10 209 792
1564 17 210 792
47ec 14 179 792
3d28 6 280 792
4618 6 191 792
17a4 10 217 792
1258 f 230 792
244c 14 230 792
3260 f 230 792
334c 14 230 792
63f0 14 230 792
6ba0 14 232 792
6bb4 7 233 792
6bbb 8 234 792
6bc3 1c 237 792
3cb0 3 239 792
2db0 16 217 792
1008 2 249 792
22f8 b 251 792
2303 11 254 792
42a4 f 254 792
6a74 a 302 792
4434 4 258 792
24b8 8 262 792
4438 e 262 792
4920 a 262 792
617c 18 262 792
636c e 262 792
6c4c 14 262 792
31e0 16 273 792
6248 a 274 792
2b6c f 275 792
31a8 4 276 792
24e8 1c 280 792
5d6c 3 282 792
5d6f 5 284 792
2d54 4 293 792
2d58 6 294 792
4380 f 249 792
12c8 c 309 792
12d4 2 311 792
197c 2 311 792
1988 1 311 792
1989 2 312 792
2dcc 8 281 792
FUNC 12ac 3 0 _RTC_Initialize
FUNC 2d94 10 0 _RTC_Initialize
FUNC 447c 10 0 _RTC_Initialize
FUNC 5754 1e 0 _RTC_Initialize
FUNC 6958 2 0 _RTC_Initialize
FUNC 2480 3 0 _RTC_Terminate
FUNC 3cf8 10 0 _RTC_Terminate
FUNC 3e2c 2 0 _RTC_Terminate
FUNC 57e0 1e 0 _RTC_Terminate
FUNC 5c60 10 0 _RTC_Terminate
FUNC 1474 9 0 _encoded_null
1474 0 79 3774
1474 8 80 3774
147c 1 81 3774
FUNC 2bb0 9 4 __crtTlsAlloc
2bb0 0 95 3774
2bb0 6 96 3774
2bb6 3 97 3774
FUNC 3240 1f 0 __set_flsgetvalue
FUNC 338c 1b 0 __set_flsgetvalue
FUNC 6444 4 0 __set_flsgetvalue
3240 3 143 3774
3243 e 145 3774
3251 e 146 3774
338c e 148 3774
339a d 149 3774
6444 3 151 3774
6447 1 155 3774
FUNC 2068 16 0 _mtterm
FUNC 2b9c 14 0 _mtterm
FUNC 48c8 14 0 _mtterm
FUNC 5b84 5 0 _mtterm
FUNC 5e0c e 0 _mtterm
48c8 0 329 3774
48c8 14 336 3774
2068 f 337 3774
2077 7 338 3774
2b9c 14 342 3774
5e0c 7 343 3774
5e13 7 344 3774
5b84 5 352 3774
FUNC 10ca b4 0 _initptd
10ca c 379 3774
10d6 b 381 3774
10e1 a 384 3774
10eb 4 385 3774
10ef 6 386 3774
10f5 3 391 3774
10f8 7 395 3774
10ff 7 396 3774
1106 7 397 3774
110d 8 399 3774
1115 4 400 3774
1119 9 402 3774
1122 c 404 3774
112e 8 411 3774
1136 3 412 3774
1139 6 413 3774
113f 4 421 3774
1143 8 422 3774
114b 9 423 3774
1154 c 425 3774
1160 6 428 3774
1166 6 404 3774
116c 9 406 3774
1175 9 426 3774
FUNC 2954 28 0 _getptd_noexit
FUNC 4520 1c 0 _getptd_noexit
FUNC 45cc 1e 0 _getptd_noexit
FUNC 48ec 23 0 _getptd_noexit
FUNC 4b28 c 0 _getptd_noexit
FUNC 616c 9 0 _getptd_noexit
2954 4 448 3774
2958 6 452 3774
295e 1e 460 3774
45cc 1e 472 3774
48ec 23 475 3774
4520 a 481 3774
452a 6 483 3774
4530 6 484 3774
4536 6 486 3774
616c 7 492 3774
6173 2 493 3774
4b28 8 498 3774
4b30 3 500 3774
4b33 1 501 3774
FUNC 12c0 7 0 _getptd
FUNC 4314 18 0 _getptd
FUNC 44f4 4 0 _getptd
FUNC 4f9c 1 0 _getptd
4314 3 522 3774
4317 7 523 3774
431e e 524 3774
12c0 7 525 3774
4f9c 1 525 3774
44f4 3 527 3774
44f7 1 528 3774
FUNC 1330 12f 4 _freefls
1330 c 556 3774
133c b 567 3774
1347 7 568 3774
134e 7 569 3774
1355 7 571 3774
135c 7 572 3774
1363 7 574 3774
136a 7 575 3774
1371 7 577 3774
1378 7 578 3774
137f 7 580 3774
1386 7 581 3774
138d 7 583 3774
1394 7 584 3774
139b 7 586 3774
13a2 7 587 3774
13a9 a 589 3774
13b3 7 590 3774
13ba 8 592 3774
13c2 4 593 3774
13c6 1a 596 3774
13e0 7 597 3774
13e7 c 599 3774
13f3 8 603 3774
13fb 7 605 3774
1402 7 606 3774
1409 7 608 3774
1410 15 611 3774
1425 7 612 3774
142c c 615 3774
1438 7 619 3774
143f 8 622 3774
1447 3 599 3774
144a 9 600 3774
1453 3 615 3774
1456 9 616 3774
FUNC 1608 54 0 _mtinit
FUNC 2228 5 0 _mtinit
FUNC 3768 13 0 _mtinit
FUNC 3880 2 0 _mtinit
FUNC 3ba8 3 0 _mtinit
FUNC 3cb8 1e 0 _mtinit
FUNC 3d90 28 0 _mtinit
FUNC 45ec 9 0 _mtinit
FUNC 47a4 19 0 _mtinit
FUNC 4c18 1f 0 _mtinit
FUNC 547c 52 0 _mtinit
FUNC 54f8 16 0 _mtinit
FUNC 58b0 24 0 _mtinit
FUNC 597c 33 0 _mtinit
FUNC 5c28 e 0 _mtinit
FUNC 6458 1e 0 _mtinit
6458 3 203 3774
645b d 211 3774
6468 e 212 3774
45ec 5 213 3774
45f1 3 214 3774
1608 1 300 3774
45f4 1 300 3774
1609 e 218 3774
1617 d 221 3774
1624 d 224 3774
1631 d 227 3774
163e 1e 228 3774
3768 13 228 3774
599c 13 228 3774
5c28 e 228 3774
58b0 a 231 3774
58ba 1a 235 3774
47a4 19 244 3774
54f8 16 244 3774
547c 5 249 3774
5481 e 256 3774
548f d 257 3774
549c d 258 3774
54a9 12 259 3774
54bb 7 266 3774
54c2 c 268 3774
3d90 1d 274 3774
3dad b 276 3774
3cb8 1e 284 3774
4c18 1f 284 3774
597c a 294 3774
5986 6 296 3774
598c 6 297 3774
5992 9 299 3774
2228 5 286 3774
3880 2 245 3774
3ba8 2 245 3774
3baa 1 300 3774
FUNC 472c 1e 0 _heap_init
472c 0 40 453
472c 1d 44 453
4749 1 60 453
FUNC 503b 45 0 _SEH_prolog4
FUNC 5080 14 0 _SEH_epilog4
FUNC 10b0 1a 0 _except_handler4
FUNC 1180 c 0 _except_handler4
FUNC 1528 17 0 _except_handler4
FUNC 1734 10 0 _except_handler4
FUNC 1768 2c 0 _except_handler4
FUNC 210c 10 0 _except_handler4
FUNC 267c 13 0 _except_handler4
FUNC 2bd0 20 0 _except_handler4
FUNC 3270 a 0 _except_handler4
FUNC 3754 13 0 _except_handler4
FUNC 3b48 36 0 _except_handler4
FUNC 3d54 3 0 _except_handler4
FUNC 3f88 c 0 _except_handler4
FUNC 42b4 d 0 _except_handler4
FUNC 4f40 10 0 _except_handler4
FUNC 5a60 1c 0 _except_handler4
FUNC 5a84 22 0 _except_handler4
FUNC 5d88 d 0 _except_handler4
FUNC 5e70 f 0 _except_handler4
FUNC 5f50 12 0 _except_handler4
FUNC 5fcc 12 0 _except_handler4
FUNC 61bc 1d 0 _except_handler4
FUNC 6670 3 0 _except_handler4
FUNC 6a80 14 0 _except_handler4
FUNC 6b0c 20 0 _except_handler4
FUNC 6cd8 10 0 _except_handler4
FUNC 2244 2f 0 __security_init_cookie
FUNC 2740 48 0 __security_init_cookie
FUNC 33e4 d 0 __security_init_cookie
FUNC 3fd8 b 0 __security_init_cookie
FUNC 4390 f 0 __security_init_cookie
FUNC 4d68 c 0 __security_init_cookie
FUNC 5588 e 0 __security_init_cookie
FUNC 61a4 4 0 __security_init_cookie
FUNC 6610 c 0 __security_init_cookie
2244 8 97 1455
224c 27 114 1455
5588 e 114 1455
4d68 7 116 1455
2740 1 117 1455
4d6f 5 117 1455
2741 a 127 1455
274b 6 132 1455
2751 8 135 1455
2759 8 136 1455
2761 8 137 1455
2769 a 139 1455
2773 8 144 1455
277b d 161 1455
3fd8 b 163 1455
33e4 d 166 1455
6610 c 168 1455
4390 6 172 1455
4396 9 173 1455
61a4 2 173 1455
61a6 2 175 1455
FUNC 4570 f 0 _initp_misc_invarg
4570 5 64 3583
4575 8 65 3583
457d 2 66 3583
FUNC 3a1f 129 0 _call_reportfault
3a1f 16 164 3583
3a35 9 166 3583
3a3e 7 167 3583
3a45 17 170 3583
3a5c 1b 172 3583
3a77 6 179 3583
3a7d 6 180 3583
3a83 6 181 3583
3a89 6 182 3583
3a8f 6 183 3583
3a95 6 184 3583
3a9b 7 185 3583
3aa2 7 186 3583
3aa9 7 187 3583
3ab0 7 188 3583
3ab7 7 189 3583
3abe 7 190 3583
3ac5 1 191 3583
3ac6 6 192 3583
3acc 3 198 3583
3acf 19 199 3583
3ae8 9 201 3583
3af1 9 240 3583
3afa 9 241 3583
3b03 6 242 3583
3b09 6 245 3583
3b0f a 248 3583
3b19 d 250 3583
3b26 d 254 3583
3b33 7 255 3583
3b3a e 257 3583
FUNC 5c38 25 0 _invoke_watson
5c38 3 146 3583
5c3b 12 155 3583
5c4d f 156 3583
5c5c 1 157 3583
FUNC 6922 2d 0 _invalid_parameter
6922 5 96 3583
6927 c 103 3583
6933 4 104 3583
6937 1 111 3583
6938 2 106 3583
693a 15 110 3583
FUNC 6f50 10 0 _invalid_parameter_noinfo
6f50 0 120 3583
6f50 f 121 3583
6f5f 1 122 3583
FUNC 2130 b 0 _mtinitlocks
FUNC 2dec 14 0 _mtinitlocks
FUNC 4358 3 0 _mtinitlocks
FUNC 453c 10 0 _mtinitlocks
FUNC 479c 3 0 _mtinitlocks
FUNC 4b48 10 0 _mtinitlocks
FUNC 55a8 27 0 _mtinitlocks
2130 4 136 3954
2134 7 143 3954
2dec 14 144 3954
55a8 9 145 3954
55b1 1e 147 3954
4b48 10 143 3954
479c 3 156 3954
4358 3 157 3954
453c 10 150 3954
FUNC 20ec 14 0 _mtdeletelocks
FUNC 2298 10 0 _mtdeletelocks
FUNC 31d4 6 0 _mtdeletelocks
FUNC 3744 10 0 _mtdeletelocks
FUNC 4350 3 0 _mtdeletelocks
FUNC 46e4 14 0 _mtdeletelocks
FUNC 4a54 10 0 _mtdeletelocks
FUNC 5554 10 0 _mtdeletelocks
FUNC 5ec4 3 0 _mtdeletelocks
FUNC 63c0 d 0 _mtdeletelocks
FUNC 6b90 10 0 _mtdeletelocks
2298 3 187 3954
229b d 193 3954
5554 10 195 3954
6b90 10 195 3954
63c0 3 199 3954
63c3 6 205 3954
63c9 4 206 3954
20ec 14 193 3954
31d4 6 214 3954
3744 10 216 3954
4a54 10 216 3954
4350 3 220 3954
46e4 14 214 3954
5ec4 3 223 3954
FUNC 4218 17 0 _unlock
4218 5 370 3954
421d 10 374 3954
422d 2 375 3954
FUNC 3ff7 c2 0 _mtinitlocknum
3ff7 c 258 3954
4003 6 260 3954
4009 a 268 3954
4013 5 269 3954
4018 7 270 3954
401f c 271 3954
402b e 275 3954
4039 4 276 3954
403d e 278 3954
404b b 279 3954
4056 4 280 3954
405a 8 283 3954
4062 3 284 3954
4065 4 286 3954
4069 10 287 3954
4079 7 288 3954
4080 b 289 3954
408b 3 290 3954
408e 2 291 3954
4090 2 292 3954
4092 2 295 3954
4094 7 296 3954
409b c 299 3954
40a7 3 303 3954
40aa 6 304 3954
40b0 9 300 3954
FUNC 118c b 0 _lock
FUNC 1af0 14 0 _lock
FUNC 3718 7 0 _lock
FUNC 44a0 1f 0 _lock
FUNC 5bb4 1 0 _lock
44a0 5 332 3954
44a5 1a 337 3954
1af0 14 339 3954
3718 7 340 3954
5bb4 1 340 3954
118c 9 347 3954
1195 2 348 3954
FUNC 14ac 10 0 strcpy_s
FUNC 1990 c 0 strcpy_s
FUNC 1da4 c 0 strcpy_s
FUNC 2580 18 0 strcpy_s
FUNC 2e00 4 0 strcpy_s
FUNC 4674 10 0 strcpy_s
FUNC 470c 4 0 strcpy_s
FUNC 4794 2 0 strcpy_s
FUNC 4d40 e 0 strcpy_s
FUNC 59b8 14 0 strcpy_s
FUNC 6158 14 0 strcpy_s
FUNC 6a94 7 0 strcpy_s
FUNC 6c8c a 0 strcpy_s
2580 5 13 2523
1da4 c 18 2523
2585 13 18 2523
4674 10 18 2523
6c8c a 18 2523
14ac 10 19 2523
6a94 7 19 2523
1990 c 23 2523
2e00 4 23 2523
6158 14 23 2523
4d40 e 27 2523
59b8 3 29 2523
59bb 11 30 2523
470c 2 33 2523
4794 2 33 2523
470e 2 34 2523
FUNC 37f0 8b 0 strlen
37f0 0 54 2796
37f0 4 63 2796
37f4 6 64 2796
37fa 2 65 2796
37fc 2 69 2796
37fe 3 70 2796
3801 2 71 2796
3803 2 72 2796
3805 6 73 2796
380b 2 74 2796
380d 13 76 2796
3820 2 81 2796
3822 5 82 2796
3827 2 83 2796
3829 3 84 2796
382c 2 85 2796
382e 3 86 2796
3831 5 87 2796
3836 2 88 2796
3838 3 90 2796
383b 2 91 2796
383d 2 92 2796
383f 2 93 2796
3841 2 94 2796
3843 5 95 2796
3848 2 96 2796
384a 5 97 2796
384f 2 98 2796
3851 2 99 2796
3853 3 103 2796
3856 4 104 2796
385a 2 105 2796
385c 1 106 2796
385d 3 108 2796
3860 4 109 2796
3864 2 110 2796
3866 1 111 2796
3867 3 113 2796
386a 4 114 2796
386e 2 115 2796
3870 1 116 2796
3871 3 118 2796
3874 4 119 2796
3878 2 120 2796
387a 1 121 2796
FUNC 14a0 7 0 malloc
FUNC 22f4 3 0 malloc
FUNC 2838 e 0 malloc
FUNC 28e4 7 0 malloc
FUNC 2da4 a 0 malloc
FUNC 34d0 8 0 malloc
FUNC 3d5c 16 0 malloc
FUNC 4580 13 0 malloc
FUNC 4a4c 3 0 malloc
FUNC 4cf8 14 0 malloc
FUNC 4eb8 1f 0 malloc
FUNC 5a48 6 0 malloc
FUNC 5b20 18 0 malloc
FUNC 5b8c 14 0 malloc
FUNC 5df8 14 0 malloc
FUNC 60ec 2 0 malloc
FUNC 65a0 2 0 malloc
5b20 6 81 504
5b26 12 85 504
60ec 2 85 504
2838 e 89 504
34d0 8 89 504
3d5c 16 89 504
4580 13 89 504
4a4c 3 89 504
4eb8 11 89 504
65a0 2 89 504
4ec9 e 94 504
5df8 14 98 504
4cf8 14 105 504
5a48 6 109 504
14a0 7 100 504
28e4 7 119 504
2da4 a 121 504
5b8c 7 111 504
5b93 b 112 504
22f4 1 113 504
5b9e 2 113 504
22f5 2 122 504
FUNC 297c 90 0 _local_unwind4
FUNC 2a0c 46 0 static void _unwind_handler4()
FUNC 2a52 1c 4 _seh_longjmp_unwind4
FUNC 2a6e 17 0 _EH4_CallFilterFunc
FUNC 2a85 19 0 _EH4_TransferToHandler
FUNC 2a9e 19 0 _EH4_GlobalUnwind2
FUNC 2ab7 17 8 _EH4_LocalUnwind
FUNC 1480 5 0 _get_errno_from_oserr
FUNC 23bc 13 0 _get_errno_from_oserr
FUNC 3510 12 0 _get_errno_from_oserr
FUNC 5ec8 10 0 _get_errno_from_oserr
FUNC 5f78 a 0 _get_errno_from_oserr
FUNC 6ab4 1f 0 _get_errno_from_oserr
5f78 5 119 224
5f7d 5 123 224
23bc 13 124 224
5ec8 10 123 224
3510 12 133 224
1480 3 134 224
1483 2 139 224
6ab4 7 125 224
6abb 2 139 224
6ac0 11 135 224
6ad1 2 139 224
FUNC 5774 6 0 _errno
FUNC 5d4c 13 0 _errno
FUNC 6c60 4 0 _errno
5d4c 0 279 224
5d4c 5 280 224
5d51 e 281 224
5774 5 282 224
5779 1 287 224
6c60 3 284 224
6c63 1 287 224
FUNC 4638 39 0 terminate()
4638 c 84 4591
4644 8 89 4591
464c 4 90 4591
4650 4 95 4591
4654 2 99 4591
4656 2 100 4591
4658 7 101 4591
465f 7 106 4591
4666 5 114 4591
466b 6 115 4591
FUNC 4834 11 0 _initp_eh_hooks
4834 0 69 4591
4834 10 70 4591
4844 1 71 4591
FUNC 15b8 1e 0 _initp_misc_winsig
15b8 5 57 2311
15bd 8 58 2311
15c5 5 59 2311
15ca 5 60 2311
15cf 5 61 2311
15d4 2 62 2311
FUNC 15d8 18 0 static struct _XCPT_ACTION * siglookup(int, struct _XCPT_ACTION *)
FUNC 3538 f 0 static struct _XCPT_ACTION * siglookup(int, struct _XCPT_ACTION *)
FUNC 42c8 14 0 static struct _XCPT_ACTION * siglookup(int, struct _XCPT_ACTION *)
FUNC 45c8 2 0 static struct _XCPT_ACTION * siglookup(int, struct _XCPT_ACTION *)
FUNC 4e1c f 0 static struct _XCPT_ACTION * siglookup(int, struct _XCPT_ACTION *)
FUNC 55f0 2 0 static struct _XCPT_ACTION * siglookup(int, struct _XCPT_ACTION *)
FUNC 63ac f 0 static struct _XCPT_ACTION * siglookup(int, struct _XCPT_ACTION *)
63ac 5 629 2311
63b1 a 630 2311
15d8 18 639 2311
4e1c f 639 2311
3538 3 642 2311
42c8 14 642 2311
353b c 646 2311
55f0 2 651 2311
45c8 2 652 2311
FUNC 3708 d 0 __get_sigabrt
3708 0 669 2311
3708 c 670 2311
3714 1 671 2311
FUNC 17d8 1a3 0 raise
17d8 c 450 2311
17e4 5 457 2311
17e9 3 458 2311
17ec 1f 460 2311
180b a 486 2311
1815 4 487 2311
1819 8 488 2311
1821 a 463 2311
182b 2 465 2311
182d 10 490 2311
183d 2 491 2311
183f f 460 2311
184e 12 498 2311
1860 a 474 2311
186a 2 476 2311
186c a 468 2311
1876 2 470 2311
1878 a 479 2311
1882 7 480 2311
1889 a 500 2311
1893 2 508 2311
1895 4 507 2311
1899 6 508 2311
189f 5 513 2311
18a4 7 518 2311
18ab 5 525 2311
18b0 7 526 2311
18b7 5 528 2311
18bc f 539 2311
18cb 6 540 2311
18d1 3 541 2311
18d4 5 547 2311
18d9 6 548 2311
18df 7 549 2311
18e6 5 557 2311
18eb 1a 564 2311
1905 d 567 2311
1912 5 564 2311
1917 7 570 2311
191e c 573 2311
192a 5 578 2311
192f 8 584 2311
1937 2 585 2311
1939 6 573 2311
193f 6 574 2311
1945 9 575 2311
194e 5 586 2311
1953 f 593 2311
1962 6 594 2311
1968 5 599 2311
196d 6 600 2311
1973 2 603 2311
1975 6 604 2311
FUNC 502c f 0 _initp_misc_rand_s
502c 5 58 2186
5031 8 59 2186
5039 2 60 2186
FUNC 2848 f 0 _initp_misc_purevirt
2848 5 179 1627
284d 8 180 1627
2855 2 181 1627
FUNC 6fd0 f 0 _initp_heap_handler
6fd0 5 31 2820
6fd5 8 32 2820
6fdd 2 33 2820
FUNC 1660 14 0 _callnewh
FUNC 5708 1f 0 _callnewh
FUNC 60b0 4 0 _callnewh
FUNC 6c18 5 0 _callnewh
5708 5 131 2820
570d c 133 2820
1660 14 135 2820
5719 e 135 2820
6c18 3 138 2820
6c1b 2 139 2820
60b0 2 136 2820
60b2 2 139 2820
FUNC 2274 12 0 static  * _onexit_nolock( *)
FUNC 2484 10 0 static  * _onexit_nolock( *)
FUNC 2640 2 0 static  * _onexit_nolock( *)
FUNC 2b88 10 0 static  * _onexit_nolock( *)
FUNC 32ac 13 0 static  * _onexit_nolock( *)
FUNC 4124 15 0 static  * _onexit_nolock( *)
FUNC 43e8 33 0 static  * _onexit_nolock( *)
FUNC 45f8 18 0 static  * _onexit_nolock( *)
FUNC 4a2c 20 0 static  * _onexit_nolock( *)
FUNC 4b68 2 0 static  * _onexit_nolock( *)
FUNC 4bb8 18 0 static  * _onexit_nolock( *)
FUNC 4d84 5 0 static  * _onexit_nolock( *)
FUNC 6a0c 1a 0 static  * _onexit_nolock( *)
43e8 8 100 1969
43f0 f 103 1969
43ff f 104 1969
4124 15 108 1969
440e d 108 1969
6a0c 1a 118 1969
2640 2 123 1969
2b88 2 123 1969
32ac 13 123 1969
2b8a e 125 1969
45f8 18 125 1969
2484 3 130 1969
2487 d 132 1969
4bb8 18 132 1969
2274 3 143 1969
2277 f 145 1969
4a2c 10 152 1969
4a3c 8 153 1969
4a44 8 155 1969
4b68 2 110 1969
4d84 3 110 1969
4d87 2 156 1969
FUNC 14bc 2f 0 __onexitinit
FUNC 2658 7 0 __onexitinit
FUNC 318c 5 0 __onexitinit
14bc 3 201 1969
14bf d 204 1969
14cc 11 205 1969
14dd e 207 1969
318c 4 212 1969
3190 1 217 1969
2658 3 214 1969
265b 3 216 1969
265e 1 217 1969
FUNC 157b 3c 0 _onexit
157b c 81 1969
1587 5 84 1969
158c 4 86 1969
1590 c 87 1969
159c c 89 1969
15a8 3 93 1969
15ab 6 94 1969
15b1 6 90 1969
FUNC 56a4 17 0 atexit
56a4 5 161 1969
56a9 10 162 1969
56b9 2 163 1969
FUNC 6154 3 0 _initp_misc_cfltcvt_tab
FUNC 6b70 20 0 _initp_misc_cfltcvt_tab
FUNC 6ebc 6 0 _initp_misc_cfltcvt_tab
6ebc 4 54 2419
6ec0 2 56 2419
6b70 20 58 2419
6154 3 60 2419
FUNC 4af8 17 0 _ValidateImageBase
FUNC 4e40 1c 0 _ValidateImageBase
FUNC 5608 4 0 _ValidateImageBase
FUNC 585c 12 0 _ValidateImageBase
4e40 5 44 2079
4e45 17 50 2079
5608 2 52 2079
560a 2 68 2079
4af8 5 55 2079
4afd 6 56 2079
4b03 c 58 2079
585c 10 62 2079
586c 2 68 2079
FUNC 1234 5 0 _FindPESection
FUNC 1b2c 2 0 _FindPESection
FUNC 4948 13 0 _FindPESection
FUNC 4e08 12 0 _FindPESection
FUNC 5d9c 10 0 _FindPESection
FUNC 6d60 3 0 _FindPESection
FUNC 6fa4 2c 0 _FindPESection
6fa4 5 92 2079
6fa9 8 99 2079
6d60 3 108 2079
6fb1 1f 108 2079
4948 13 111 2079
5d9c 10 111 2079
4e08 12 108 2079
1234 3 123 2079
1b2c 2 123 2079
1237 2 124 2079
FUNC 6fee bc 0 _IsNonwritableInCurrentImage
6fee 35 149 2079
7023 7 156 2079
702a f 164 2079
7039 2 166 2079
703b 8 174 2079
7043 e 175 2079
7051 2 176 2079
7053 2 178 2079
7055 12 185 2079
7067 12 195 2079
7079 16 187 2079
708f 9 193 2079
7098 12 195 2079
FUNC 1064 4c 0 __crtMessageBoxW
FUNC 123c 1b 0 __crtMessageBoxW
FUNC 1460 14 0 __crtMessageBoxW
FUNC 19c4 12 0 __crtMessageBoxW
FUNC 2148 40 0 __crtMessageBoxW
FUNC 2b00 14 0 __crtMessageBoxW
FUNC 31b4 e 0 __crtMessageBoxW
FUNC 3290 1a 0 __crtMessageBoxW
FUNC 36e8 8 0 __crtMessageBoxW
FUNC 3794 1c 0 __crtMessageBoxW
FUNC 474c 1c 0 __crtMessageBoxW
FUNC 496c 10 0 __crtMessageBoxW
FUNC 4ca0 10 0 __crtMessageBoxW
FUNC 4d18 1c 0 __crtMessageBoxW
FUNC 5640 2 0 __crtMessageBoxW
FUNC 56e4 16 0 __crtMessageBoxW
FUNC 5cf4 10 0 __crtMessageBoxW
FUNC 5e50 10 0 __crtMessageBoxW
FUNC 5ee8 13 0 __crtMessageBoxW
FUNC 5f18 c 0 __crtMessageBoxW
FUNC 6f6c f 0 __crtMessageBoxW
FUNC 6f7c 14 0 __crtMessageBoxW
FUNC 70d4 10 0 __crtMessageBoxW
2148 12 41 1053
215a 14 49 1053
216e 4 56 1053
2172 16 62 1053
3290 d 64 1053
329d 2 65 1053
329f b 67 1053
123c 10 72 1053
124c b 76 1053
1064 9 78 1053
106d 10 81 1053
107d 10 84 1053
108d d 88 1053
109a 8 93 1053
10a2 e 95 1053
70d4 10 97 1053
19c4 12 110 1053
474c 1c 110 1053
4d18 3 113 1053
4d1b c 114 1053
31b4 e 116 1053
4d27 d 116 1053
3794 1c 121 1053
496c 10 121 1053
5cf4 10 121 1053
5f18 7 130 1053
5f1f 5 132 1053
6f7c 14 134 1053
5e50 3 136 1053
5e53 d 137 1053
5ee8 5 139 1053
1460 14 143 1053
5eed e 143 1053
4ca0 3 145 1053
4ca3 d 146 1053
36e8 8 148 1053
56e4 8 155 1053
56ec e 156 1053
2b00 14 158 1053
5640 2 163 1053
6f6c f 166 1053
FUNC 21d0 14 0 wcscat_s
FUNC 28c0 10 0 wcscat_s
FUNC 3784 a 0 wcscat_s
FUNC 3d78 18 0 wcscat_s
FUNC 4880 18 0 wcscat_s
FUNC 48bc b 0 wcscat_s
FUNC 4b7c 10 0 wcscat_s
FUNC 4f58 4 0 wcscat_s
FUNC 5448 e 0 wcscat_s
FUNC 55d8 12 0 wcscat_s
FUNC 5f9c 2 0 wcscat_s
FUNC 5fb4 7 0 wcscat_s
FUNC 6054 c 0 wcscat_s
FUNC 6194 10 0 wcscat_s
FUNC 6d50 10 0 wcscat_s
3d78 6 13 2468
3784 a 18 2468
3d7e 12 18 2468
4f58 2 18 2468
5fb4 7 18 2468
6194 10 18 2468
4f5a 2 46 2468
28c0 10 19 2468
48bc b 19 2468
55e8 2 21 2468
6d50 10 23 2468
55d8 3 25 2468
55db d 26 2468
5448 2 29 2468
544a c 32 2468
4880 18 35 2468
5f9c 2 35 2468
6054 c 35 2468
21d0 3 41 2468
4b7c 10 41 2468
21d3 11 42 2468
FUNC 103c c 0 wcsncpy_s
FUNC 11d8 10 0 wcsncpy_s
FUNC 12fc b 0 wcsncpy_s
FUNC 1ae0 e 0 wcsncpy_s
FUNC 1b4c f 0 wcsncpy_s
FUNC 1e0c 5 0 wcsncpy_s
FUNC 21e4 10 0 wcsncpy_s
FUNC 228c 4 0 wcsncpy_s
FUNC 22b0 c 0 wcsncpy_s
FUNC 3720 e 0 wcsncpy_s
FUNC 37c0 b 0 wcsncpy_s
FUNC 43d4 14 0 wcsncpy_s
FUNC 46c8 10 0 wcsncpy_s
FUNC 4828 c 0 wcsncpy_s
FUNC 495c a 0 wcsncpy_s
FUNC 49dc 34 0 wcsncpy_s
FUNC 4c38 2 0 wcsncpy_s
FUNC 4c84 5 0 wcsncpy_s
FUNC 5a38 5 0 wcsncpy_s
FUNC 5dc8 c 0 wcsncpy_s
FUNC 61e0 e 0 wcsncpy_s
FUNC 6288 4 0 wcsncpy_s
FUNC 63e0 d 0 wcsncpy_s
FUNC 6a28 10 0 wcsncpy_s
FUNC 6af4 18 0 wcsncpy_s
FUNC 6c3c e 0 wcsncpy_s
FUNC 70c4 10 0 wcsncpy_s
49dc 5 13 2578
1b4c f 17 2578
49e1 17 17 2578
6c3c e 17 2578
4c38 2 65 2578
5a38 3 65 2578
5a3b 2 66 2578
103c c 24 2578
11d8 10 24 2578
495c a 24 2578
61e0 e 24 2578
3720 e 25 2578
12fc 5 28 2578
1301 6 29 2578
37c0 b 31 2578
46c8 10 31 2578
21e4 2 33 2578
21e6 e 35 2578
22b0 c 37 2578
6288 4 37 2578
6af4 18 37 2578
1e0c 5 41 2578
228c 4 45 2578
4828 c 45 2578
49f8 18 45 2578
5dc8 c 45 2578
1ae0 e 48 2578
4c84 5 50 2578
63e0 d 54 2578
6a28 3 58 2578
70c4 10 58 2578
6a2b d 59 2578
43d4 3 61 2578
43d7 11 62 2578
FUNC 20d4 10 0 wcslen
FUNC 5520 10 0 wcslen
20dc 5 41 2728
20e1 3 42 2728
5520 10 44 2728
20d4 6 46 2728
20da 2 47 2728
FUNC 15f0 18 0 wcscpy_s
FUNC 17cc c 0 wcscpy_s
FUNC 23a0 10 0 wcscpy_s
FUNC 2494 a 0 wcscpy_s
FUNC 2858 18 0 wcscpy_s
FUNC 3208 10 0 wcscpy_s
FUNC 3ca8 8 0 wcscpy_s
FUNC 4bd0 10 0 wcscpy_s
FUNC 5530 7 0 wcscpy_s
FUNC 5880 4 0 wcscpy_s
FUNC 5f08 4 0 wcscpy_s
FUNC 5f24 14 0 wcscpy_s
15f0 6 13 2523
15f6 12 18 2523
23a0 10 18 2523
2494 a 18 2523
5530 7 18 2523
5880 2 18 2523
5882 2 34 2523
3208 10 19 2523
3ca8 8 19 2523
17cc c 23 2523
2858 18 23 2523
5f08 4 23 2523
4bd0 10 29 2523
5f24 3 29 2523
5f27 11 30 2523
FUNC 1268 d 0 _set_error_mode
FUNC 1b7c 16 0 _set_error_mode
FUNC 1e04 7 0 _set_error_mode
FUNC 31c4 f 0 _set_error_mode
FUNC 49cc f 0 _set_error_mode
FUNC 5eac 15 0 _set_error_mode
1b7c 5 43 1181
1b81 11 46 1181
31c4 f 46 1181
49cc f 46 1181
1e04 5 54 1181
1e09 2 61 1181
1268 5 50 1181
126d 6 51 1181
1273 2 61 1181
5eac 13 57 1181
5ebf 2 61 1181
FUNC 1275 f 0 __security_check_cookie
1275 0 52 892
1275 6 55 892
127b 2 56 892
127d 2 57 892
127f 5 59 892
FUNC 1488 6 0 _malloc_crt
FUNC 1b04 12 0 _malloc_crt
FUNC 1d3c 3 0 _malloc_crt
FUNC 5fe0 1f 0 _malloc_crt
FUNC 69d4 9 0 _malloc_crt
FUNC 6c64 18 0 _malloc_crt
FUNC 6f40 10 0 _malloc_crt
69d4 7 39 333
69db 2 40 333
6c64 b 44 333
1b04 12 45 333
6c6f d 45 333
1d3c 3 46 333
5fe0 1f 46 333
6f40 2 46 333
6f42 e 47 333
1488 4 50 333
148c 2 51 333
FUNC 3cd8 10 0 _calloc_crt
FUNC 541c 12 0 _calloc_crt
FUNC 553c 6 0 _calloc_crt
FUNC 5598 9 0 _calloc_crt
FUNC 6950 3 0 _calloc_crt
FUNC 6ec8 1f 0 _calloc_crt
FUNC 6ef0 20 0 _calloc_crt
5598 7 54 333
559f 2 55 333
6ef0 12 61 333
541c 12 62 333
6f02 e 62 333
3cd8 2 63 333
6950 3 63 333
6ec8 1f 63 333
3cda e 64 333
553c 4 67 333
5540 2 68 333
FUNC 2674 3 0 _realloc_crt
FUNC 2b5c 10 0 _realloc_crt
FUNC 38b8 6 0 _realloc_crt
FUNC 435c f 0 _realloc_crt
FUNC 633c 1c 0 _realloc_crt
FUNC 6580 1f 0 _realloc_crt
FUNC 65b0 12 0 _realloc_crt
FUNC 6f20 9 0 _realloc_crt
6f20 7 71 333
6f27 2 72 333
633c f 76 333
435c f 77 333
634b d 77 333
65b0 12 77 333
2674 3 78 333
2b5c 2 78 333
6580 1f 78 333
2b5e e 79 333
38b8 4 82 333
38bc 2 83 333
FUNC 28b4 c 0 static int CPtoLCID(int)
FUNC 3d08 6 0 static int CPtoLCID(int)
FUNC 5510 6 0 static int CPtoLCID(int)
FUNC 5600 6 0 static int CPtoLCID(int)
FUNC 5f14 3 0 static int CPtoLCID(int)
FUNC 6294 f 0 static int CPtoLCID(int)
FUNC 68f4 10 0 static int CPtoLCID(int)
FUNC 69cc 6 0 static int CPtoLCID(int)
FUNC 6fe0 e 0 static int CPtoLCID(int)
68f4 0 329 3326
28b4 c 330 3326
6294 f 330 3326
68f4 10 330 3326
6fe0 e 330 3326
5f14 2 345 3326
5f16 1 346 3326
5510 5 342 3326
5515 1 346 3326
3d08 5 339 3326
3d0d 1 346 3326
69cc 5 336 3326
69d1 1 346 3326
5600 5 333 3326
5605 1 346 3326
FUNC 2208 f 0 static void setSBCS(struct threadmbcinfostruct *)
FUNC 387c 3 0 static void setSBCS(struct threadmbcinfostruct *)
FUNC 48ac f 0 static void setSBCS(struct threadmbcinfostruct *)
FUNC 5900 44 0 static void setSBCS(struct threadmbcinfostruct *)
FUNC 6be0 b 0 static void setSBCS(struct threadmbcinfostruct *)
5900 6 363 3326
5906 11 368 3326
5917 1b 379 3326
5932 12 381 3326
2208 f 382 3326
6be0 b 384 3326
48ac f 385 3326
387c 3 386 3326
FUNC 12dc 18 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 1794 f 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 1b6c 7 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 2548 e 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 2c0c 39 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 3238 8 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 3e54 2 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 3ee0 16 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 445c 20 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 4800 f 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 4910 8 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 4a8c 12 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 4d8c e 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 566c f 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 56fc c 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 59e4 14 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 5aa8 6 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 5b08 17 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 5b60 14 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 5db4 12 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 6060 c 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 60b4 6 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 6218 f 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 62b8 6a 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 697c 3 0 static void setSBUpLow(struct threadmbcinfostruct *)
FUNC 6b44 1b 0 static void setSBUpLow(struct threadmbcinfostruct *)
2c0c 17 402 3326
2c23 10 412 3326
2c33 12 415 3326
3e54 2 415 3326
5db4 12 416 3326
6b44 1b 420 3326
60b4 6 419 3326
3ee0 16 421 3326
5b60 14 421 3326
59e4 14 420 3326
62b8 20 427 3326
62d8 23 432 3326
62fb 25 437 3326
6320 2 442 3326
5b08 17 443 3326
4a8c 5 445 3326
4a91 d 446 3326
4800 f 448 3326
6060 5 450 3326
56fc 7 451 3326
6065 7 451 3326
5703 5 453 3326
1b6c 7 454 3326
6218 f 442 3326
12dc 6 456 3326
5aa8 6 456 3326
12e2 12 472 3326
445c 20 461 3326
2548 5 463 3326
254d 9 464 3326
566c f 466 3326
4910 5 468 3326
3238 2 469 3326
4915 3 469 3326
323a 6 471 3326
697c 3 472 3326
1794 f 460 3326
4d8c e 474 3326
FUNC 2c45 a4 0 __updatetmbcinfo
2c45 c 495 3326
2c51 7 498 3326
2c58 10 499 3326
2c68 3 532 3326
2c6b 4 535 3326
2c6f 8 537 3326
2c77 2 540 3326
2c79 6 541 3326
2c7f 8 500 3326
2c87 4 502 3326
2c8b e 505 3326
2c99 17 511 3326
2cb0 7 516 3326
2cb7 11 523 3326
2cc8 7 524 3326
2ccf 11 527 3326
2ce0 9 529 3326
FUNC 12b0 8 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 2084 18 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 2314 7 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 2504 27 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 2904 7 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 3558 14 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 4d74 e 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 560c 13 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 5d04 14 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 5f68 a 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
FUNC 6cbc 1c 0 _LocaleUpdate::_LocaleUpdate(localeinfo_struct *)
6cbc 5 240 111
6cc1 17 241 111
2504 8 243 111
250c 5 244 111
2511 6 245 111
2314 7 247 111
2517 14 247 111
3558 14 247 111
12b0 8 248 111
2084 18 248 111
5d04 14 248 111
560c 13 249 111
4d74 4 251 111
4d78 4 252 111
4d7c 6 255 111
5f68 a 257 111
2904 7 259 111
FUNC 19fc e 0 static int getSystemCP(int)
FUNC 2690 7 0 static int getSystemCP(int)
FUNC 2b98 3 0 static int getSystemCP(int)
FUNC 3b80 28 0 static int getSystemCP(int)
FUNC 3e18 c 0 static int getSystemCP(int)
FUNC 4710 f 0 static int getSystemCP(int)
FUNC 4868 10 0 static int getSystemCP(int)
FUNC 56bc 16 0 static int getSystemCP(int)
FUNC 5e20 16 0 static int getSystemCP(int)
FUNC 6020 1f 0 static int getSystemCP(int)
FUNC 6f64 2 0 static int getSystemCP(int)
3b80 9 282 3326
3b89 b 284 3326
3b94 6 285 3326
3b9a e 289 3326
4868 a 291 3326
19fc e 292 3326
3e18 c 292 3326
4872 6 292 3326
6030 f 295 3326
5e20 a 297 3326
5e2a c 298 3326
6020 f 302 3326
56bc 16 305 3326
2690 7 308 3326
2b98 1 308 3326
4710 f 308 3326
6f64 2 308 3326
2b99 2 309 3326
FUNC 1208 29 0 _setmbcp_nolock
FUNC 16ac 20 0 _setmbcp_nolock
FUNC 1bc0 3 0 _setmbcp_nolock
FUNC 2188 11 0 _setmbcp_nolock
FUNC 23b0 6 0 _setmbcp_nolock
FUNC 2894 11 0 _setmbcp_nolock
FUNC 2b28 7 0 _setmbcp_nolock
FUNC 330c c 0 _setmbcp_nolock
FUNC 40bc e 0 _setmbcp_nolock
FUNC 454c f 0 _setmbcp_nolock
FUNC 4720 6 0 _setmbcp_nolock
FUNC 4768 f 0 _setmbcp_nolock
FUNC 4b58 10 0 _setmbcp_nolock
FUNC 4c40 c 0 _setmbcp_nolock
FUNC 5404 12 0 _setmbcp_nolock
FUNC 5728 2a 0 _setmbcp_nolock
FUNC 5800 e 0 _setmbcp_nolock
FUNC 5ab0 17 0 _setmbcp_nolock
FUNC 5adc 11 0 _setmbcp_nolock
FUNC 5ba8 c 0 _setmbcp_nolock
FUNC 5bcc 14 0 _setmbcp_nolock
FUNC 5fa4 8 0 _setmbcp_nolock
FUNC 6010 f 0 _setmbcp_nolock
FUNC 6074 1c 0 _setmbcp_nolock
FUNC 609c 14 0 _setmbcp_nolock
FUNC 60c0 2a 0 _setmbcp_nolock
FUNC 60f4 17 0 _setmbcp_nolock
FUNC 6358 14 0 _setmbcp_nolock
FUNC 638c 11 0 _setmbcp_nolock
FUNC 63d4 5 0 _setmbcp_nolock
FUNC 64a8 34 0 _setmbcp_nolock
FUNC 64f0 3 0 _setmbcp_nolock
FUNC 65c4 18 0 _setmbcp_nolock
FUNC 6974 7 0 _setmbcp_nolock
FUNC 6a64 f 0 _setmbcp_nolock
64a8 17 684 3326
64bf b 691 3326
64ca 12 694 3326
2b28 7 696 3326
6974 7 697 3326
63d4 3 701 3326
63d7 2 703 3326
5adc 11 706 3326
60f4 17 703 3326
2188 11 741 3326
2894 11 741 3326
5ab0 17 741 3326
65c4 18 749 3326
1208 f 754 3326
1217 1a 759 3326
1bc0 3 762 3326
454c f 762 3326
6010 f 762 3326
60a4 c 764 3326
5728 f 710 3326
5737 15 713 3326
4b58 10 718 3326
574c 6 718 3326
4c40 c 721 3326
5404 12 722 3326
5800 e 721 3326
4720 6 718 3326
6a64 f 718 3326
6074 1c 713 3326
16ac 20 729 3326
5bcc 14 731 3326
5ba8 7 734 3326
5baf 5 735 3326
23b0 6 765 3326
40bc e 764 3326
60d8 12 762 3326
5fa4 8 769 3326
330c c 770 3326
6358 b 773 3326
6363 3 776 3326
6366 6 778 3326
609c 3 780 3326
60c0 12 783 3326
60d2 6 787 3326
638c 6 792 3326
6392 b 795 3326
64f0 3 744 3326
4768 f 800 3326
FUNC 1e11 19a 0 _setmbcp
1e11 c 572 3326
1e1d 4 573 3326
1e21 a 577 3326
1e2b 5 579 3326
1e30 3 580 3326
1e33 b 582 3326
1e3e 9 584 3326
1e47 d 590 3326
1e54 8 592 3326
1e5c c 594 3326
1e68 3 605 3326
1e6b 16 610 3326
1e81 1a 612 3326
1e9b 7 613 3326
1ea2 3 617 3326
1ea5 9 618 3326
1eae 17 620 3326
1ec5 8 622 3326
1ecd 4 623 3326
1ed1 8 628 3326
1ed9 8 629 3326
1ee1 8 630 3326
1ee9 a 631 3326
1ef3 d 632 3326
1f00 3 631 3326
1f03 c 633 3326
1f0f a 634 3326
1f19 3 633 3326
1f1c c 635 3326
1f28 d 636 3326
1f35 3 635 3326
1f38 1c 638 3326
1f54 7 639 3326
1f5b 6 643 3326
1f61 3 644 3326
1f64 e 646 3326
1f72 9 648 3326
1f7b 2 651 3326
1f7d 5 652 3326
1f82 8 658 3326
1f8a 7 659 3326
1f91 b 660 3326
1f9c 2 666 3326
1f9e 4 671 3326
1fa2 3 680 3326
1fa5 6 681 3326
FUNC 165c 3 0 __initmbctable
FUNC 5dd4 12 0 __initmbctable
FUNC 6234 13 0 __initmbctable
6234 0 841 3326
6234 13 851 3326
5dd4 8 852 3326
5ddc a 853 3326
165c 2 858 3326
165e 1 859 3326
FUNC 1674 7 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 1c0c 2c 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 3d74 2 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 4c90 e 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 5430 16 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 59b0 2 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 6254 10 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 643c 3 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
FUNC 6498 10 0 static int x_ismbbtype_l(struct localeinfo_struct *, unsigned int, int, int)
1c0c 8 213 3271
1c14 b 214 3271
1c1f 4 219 3271
1674 7 222 3271
1c23 15 222 3271
4c90 e 222 3271
5430 16 222 3271
59b0 2 222 3271
6254 10 222 3271
643c 3 222 3271
6498 10 222 3271
3d74 2 223 3271
FUNC 4930 18 0 _ismbblead
4930 5 171 3271
4935 11 172 3271
4946 2 173 3271
FUNC 1bac 12 0 __addlocaleref
FUNC 26b4 a 0 __addlocaleref
FUNC 2880 3 0 __addlocaleref
FUNC 37d8 3 0 __addlocaleref
FUNC 3c70 14 0 __addlocaleref
FUNC 3fe4 13 0 __addlocaleref
FUNC 4a10 1c 0 __addlocaleref
FUNC 54d8 10 0 __addlocaleref
FUNC 54f0 3 0 __addlocaleref
FUNC 5620 3 0 __addlocaleref
FUNC 5dac 3 0 __addlocaleref
FUNC 6040 13 0 __addlocaleref
FUNC 6278 10 0 __addlocaleref
FUNC 6478 13 0 __addlocaleref
FUNC 6540 10 0 __addlocaleref
FUNC 6d08 28 0 __addlocaleref
6d08 7 36 1913
6d0f d 39 1913
6d1c 14 41 1913
2880 3 42 1913
4a18 14 44 1913
4a10 3 45 1913
3fe4 13 47 1913
5620 3 48 1913
3c70 14 50 1913
37d8 3 51 1913
26b4 a 53 1913
6478 13 55 1913
6540 10 55 1913
5dac 3 56 1913
54d8 10 59 1913
6278 10 59 1913
54f0 3 60 1913
1bac 12 53 1913
6040 11 63 1913
6051 2 64 1913
FUNC 2328 a 0 __removelocaleref
FUNC 252c 10 0 __removelocaleref
FUNC 2b14 14 0 __removelocaleref
FUNC 34c8 3 0 __removelocaleref
FUNC 3d44 10 0 __removelocaleref
FUNC 4c00 13 0 __removelocaleref
FUNC 4f70 14 0 __removelocaleref
FUNC 54d0 3 0 __removelocaleref
FUNC 5648 3 0 __removelocaleref
FUNC 5658 12 0 __removelocaleref
FUNC 5870 10 0 __removelocaleref
FUNC 5a7c 5 0 __removelocaleref
FUNC 5e94 16 0 __removelocaleref
FUNC 6578 3 0 __removelocaleref
FUNC 6668 3 0 __removelocaleref
FUNC 6cf0 10 0 __removelocaleref
FUNC 6d30 1f 0 __removelocaleref
FUNC 6d90 1b 0 __removelocaleref
5e94 6 74 1913
5e9a 10 77 1913
6d30 b 79 1913
6d3b 14 81 1913
6668 3 82 1913
2b14 14 84 1913
5648 3 85 1913
6d98 13 87 1913
34c8 3 88 1913
4f70 14 90 1913
6578 3 91 1913
2328 a 93 1913
252c 10 95 1913
4c00 13 95 1913
54d0 3 96 1913
3d44 10 99 1913
5870 10 99 1913
6d90 3 100 1913
5658 12 93 1913
6cf0 10 103 1913
5a7c 3 106 1913
5a7f 2 107 1913
FUNC 1dbc 14 0 __freetlocinfo
FUNC 20ac 14 0 __freetlocinfo
FUNC 21c0 10 0 __freetlocinfo
FUNC 22c4 13 0 __freetlocinfo
FUNC 2b44 c 0 __freetlocinfo
FUNC 3c5c f 0 __freetlocinfo
FUNC 3ec0 10 0 __freetlocinfo
FUNC 40cc 17 0 __freetlocinfo
FUNC 4294 e 0 __freetlocinfo
FUNC 44c0 e 0 __freetlocinfo
FUNC 44f8 20 0 __freetlocinfo
FUNC 4cd4 13 0 __freetlocinfo
FUNC 4d34 7 0 __freetlocinfo
FUNC 4df8 10 0 __freetlocinfo
FUNC 4f84 13 0 __freetlocinfo
FUNC 5630 e 0 __freetlocinfo
FUNC 577c a 0 __freetlocinfo
FUNC 5af0 13 0 __freetlocinfo
FUNC 5d74 12 0 __freetlocinfo
FUNC 5dec 7 0 __freetlocinfo
FUNC 5ed8 e 0 __freetlocinfo
FUNC 6000 e 0 __freetlocinfo
FUNC 61a8 14 0 __freetlocinfo
FUNC 6634 12 0 __freetlocinfo
FUNC 6748 13 0 __freetlocinfo
FUNC 6984 40 0 __freetlocinfo
FUNC 69fc e 0 __freetlocinfo
FUNC 6c9c 18 0 __freetlocinfo
44f8 7 129 1913
44ff 19 137 1913
4df8 10 137 1913
5ed8 e 137 1913
61a8 14 137 1913
1dbc 14 140 1913
6000 e 140 1913
4f84 6 142 1913
4f8a d 143 1913
20ac 14 147 1913
4294 e 147 1913
5af0 6 149 1913
5af6 d 150 1913
6c9c b 153 1913
6ca7 d 154 1913
5630 e 161 1913
6748 13 161 1913
6984 11 163 1913
6995 13 164 1913
69a8 e 165 1913
69b6 e 166 1913
40cc 17 173 1913
6634 12 173 1913
22c4 6 175 1913
22ca d 176 1913
577c a 179 1913
3ec0 10 182 1913
44c0 e 182 1913
4cd4 13 182 1913
5dec 7 184 1913
21c0 10 192 1913
3c5c f 192 1913
69fc e 192 1913
4d34 7 194 1913
5d74 12 179 1913
2b44 a 201 1913
2b4e 2 202 1913
FUNC 1d58 16 0 _updatetlocinfoEx_nolock
FUNC 2338 17 0 _updatetlocinfoEx_nolock
FUNC 2d40 2 0 _updatetlocinfoEx_nolock
FUNC 2dc8 3 0 _updatetlocinfoEx_nolock
FUNC 2dd4 16 0 _updatetlocinfoEx_nolock
FUNC 4f18 17 0 _updatetlocinfoEx_nolock
FUNC 5794 10 0 _updatetlocinfoEx_nolock
FUNC 57b4 8 0 _updatetlocinfoEx_nolock
FUNC 6c28 12 0 _updatetlocinfoEx_nolock
4f18 6 216 1913
2338 10 219 1913
4f1e 11 219 1913
5794 3 222 1913
5797 d 223 1913
1d58 9 230 1913
1d61 d 236 1913
2dd4 6 238 1913
2dda 10 248 1913
6c28 12 248 1913
2348 7 249 1913
57b4 8 253 1913
2d40 2 220 1913
2dc8 1 220 1913
2dc9 2 254 1913
FUNC 3f0f 79 0 __updatetlocinfo
3f0f c 281 1913
3f1b 7 283 1913
3f22 10 285 1913
3f32 8 297 1913
3f3a 4 300 1913
3f3e 8 302 1913
3f46 2 305 1913
3f48 6 306 1913
3f4e 8 286 1913
3f56 4 288 1913
3f5a 14 290 1913
3f6e e 292 1913
3f7c 8 294 1913
3f84 4 295 1913
FUNC 1ad0 8 0 _crt_debugger_hook
1ad0 0 62 1145
1ad0 7 65 1145
1ad7 1 66 1145
FUNC 4fa2 7a 0 memset
4fa2 0 59 2789
4fa2 4 68 2789
4fa6 4 69 2789
4faa 2 71 2789
4fac 2 72 2789
4fae 2 74 2789
4fb0 4 75 2789
4fb4 2 78 2789
4fb6 2 79 2789
4fb8 6 80 2789
4fbe 2 81 2789
4fc0 7 82 2789
4fc7 2 83 2789
4fc9 5 85 2789
4fce 1 91 2789
4fcf 2 92 2789
4fd1 3 94 2789
4fd4 2 95 2789
4fd6 2 97 2789
4fd8 3 98 2789
4fdb 2 99 2789
4fdd 2 101 2789
4fdf 2 103 2789
4fe1 3 104 2789
4fe4 3 105 2789
4fe7 2 106 2789
4fe9 2 110 2789
4feb 3 111 2789
4fee 2 113 2789
4ff0 2 115 2789
4ff2 3 117 2789
4ff5 2 119 2789
4ff7 2 122 2789
4ff9 3 123 2789
4ffc 3 124 2789
4fff 2 125 2789
5001 2 127 2789
5003 2 129 2789
5005 2 130 2789
5007 2 134 2789
5009 3 135 2789
500c 3 137 2789
500f 2 138 2789
5011 4 142 2789
5015 1 143 2789
5016 1 145 2789
5017 4 148 2789
501b 1 150 2789
FUNC 33f1 95 0 _aulldvrm
33f1 0 45 2806
33f1 1 48 2806
33f2 4 80 2806
33f6 2 81 2806
33f8 2 82 2806
33fa 4 83 2806
33fe 4 84 2806
3402 2 85 2806
3404 2 86 2806
3406 2 87 2806
3408 4 88 2806
340c 2 89 2806
340e 2 90 2806
3410 2 95 2806
3412 4 96 2806
3416 2 97 2806
3418 2 98 2806
341a 4 99 2806
341e 2 100 2806
3420 2 101 2806
3422 2 108 2806
3424 4 109 2806
3428 4 110 2806
342c 4 111 2806
3430 2 113 2806
3432 2 114 2806
3434 2 115 2806
3436 2 116 2806
3438 2 117 2806
343a 2 118 2806
343c 2 119 2806
343e 2 120 2806
3440 4 129 2806
3444 2 130 2806
3446 4 131 2806
344a 2 132 2806
344c 2 133 2806
344e 2 134 2806
3450 4 142 2806
3454 2 143 2806
3456 2 144 2806
3458 4 145 2806
345c 2 146 2806
345e 1 148 2806
345f 4 149 2806
3463 4 150 2806
3467 2 152 2806
3469 4 161 2806
346d 4 162 2806
3471 2 163 2806
3473 2 164 2806
3475 3 165 2806
3478 2 170 2806
347a 2 171 2806
347c 2 172 2806
347e 2 173 2806
3480 2 174 2806
3482 1 180 2806
3483 3 182 2806
FUNC 675b 20 0 _global_unwind2
FUNC 677b 45 0 static void __unwind_handler()
FUNC 67c0 84 0 _local_unwind2
FUNC 6844 23 0 _abnormal_termination
FUNC 6867 9 0 _NLG_Notify1
FUNC 6870 1f 0 _NLG_Notify
PUBLIC m 6887 0 _NLG_Dispatch
FUNC 688f 3 0 _NLG_Call
PUBLIC 6891 0 _NLG_Return2
FUNC 4695 33 0 abort
4695 0 54 1011
4695 5 71 1011
469a 4 72 1011
469e 8 74 1011
46a6 9 81 1011
46af 11 83 1011
46c0 8 92 1011
FUNC 50a3 361 0 memcpy
50a3 3 101 2787
50a6 1 113 2787
50a7 1 114 2787
50a8 3 116 2787
50ab 3 117 2787
50ae 3 119 2787
50b1 2 129 2787
50b3 2 131 2787
50b5 2 132 2787
50b7 2 134 2787
50b9 2 135 2787
50bb 2 137 2787
50bd 6 138 2787
50c3 6 147 2787
50c9 2 148 2787
50cb 7 150 2787
50d2 2 151 2787
50d4 1 153 2787
50d5 1 154 2787
50d6 3 155 2787
50d9 3 156 2787
50dc 2 157 2787
50de 1 158 2787
50df 1 159 2787
50e0 2 160 2787
50e2 5 163 2787
50e7 6 176 2787
50ed 2 177 2787
50ef 3 179 2787
50f2 3 180 2787
50f5 3 182 2787
50f8 2 183 2787
50fa 2 185 2787
50fc 7 187 2787
5103 2 205 2787
5105 5 206 2787
510a 3 208 2787
510d 2 209 2787
510f 3 211 2787
5112 2 212 2787
5114 7 214 2787
511b 8 218 2787
5123 14 222 2787
5137 2 229 2787
5139 2 230 2787
513b 2 232 2787
513d 3 233 2787
5140 3 235 2787
5143 3 236 2787
5146 3 238 2787
5149 3 239 2787
514c 3 241 2787
514f 3 242 2787
5152 3 244 2787
5155 2 245 2787
5157 2 247 2787
5159 a 249 2787
5163 2 253 2787
5165 2 254 2787
5167 2 256 2787
5169 3 257 2787
516c 3 259 2787
516f 3 260 2787
5172 3 262 2787
5175 3 263 2787
5178 3 265 2787
517b 2 266 2787
517d 2 268 2787
517f 8 270 2787
5187 2 274 2787
5189 2 275 2787
518b 2 277 2787
518d 3 278 2787
5190 3 280 2787
5193 3 281 2787
5196 3 283 2787
5199 2 284 2787
519b 2 286 2787
519d 2a 288 2787
51c7 4 295 2787
51cb 4 297 2787
51cf 4 299 2787
51d3 4 301 2787
51d7 4 303 2787
51db 4 305 2787
51df 4 307 2787
51e3 4 309 2787
51e7 4 311 2787
51eb 4 313 2787
51ef 4 315 2787
51f3 4 317 2787
51f7 4 319 2787
51fb 4 321 2787
51ff 7 323 2787
5206 2 325 2787
5208 2 326 2787
520a 19 328 2787
5223 3 337 2787
5226 1 338 2787
5227 1 339 2787
5228 3 341 2787
522b 2 345 2787
522d 2 347 2787
522f 3 348 2787
5232 1 349 2787
5233 1 350 2787
5234 3 351 2787
5237 2 355 2787
5239 2 357 2787
523b 3 358 2787
523e 3 359 2787
5241 3 360 2787
5244 1 361 2787
5245 1 362 2787
5246 5 363 2787
524b 2 367 2787
524d 2 369 2787
524f 3 370 2787
5252 3 371 2787
5255 3 372 2787
5258 3 373 2787
525b 3 374 2787
525e 1 375 2787
525f 1 376 2787
5260 3 377 2787
5263 4 388 2787
5267 4 389 2787
526b 6 394 2787
5271 2 395 2787
5273 3 397 2787
5276 3 398 2787
5279 3 400 2787
527c 2 401 2787
527e 1 403 2787
527f 2 404 2787
5281 1 405 2787
5282 9 407 2787
528b 2 411 2787
528d a 414 2787
5297 2 419 2787
5299 5 420 2787
529e 3 422 2787
52a1 2 423 2787
52a3 3 425 2787
52a6 2 426 2787
52a8 7 428 2787
52af 14 432 2787
52c3 3 439 2787
52c6 2 440 2787
52c8 3 442 2787
52cb 3 443 2787
52ce 3 445 2787
52d1 3 446 2787
52d4 3 448 2787
52d7 2 449 2787
52d9 1 451 2787
52da 2 452 2787
52dc 1 453 2787
52dd a 455 2787
52e7 3 459 2787
52ea 2 460 2787
52ec 3 462 2787
52ef 3 463 2787
52f2 3 465 2787
52f5 3 466 2787
52f8 3 468 2787
52fb 3 469 2787
52fe 3 471 2787
5301 2 472 2787
5303 1 474 2787
5304 2 475 2787
5306 1 476 2787
5307 8 478 2787
530f 3 482 2787
5312 2 483 2787
5314 3 485 2787
5317 3 486 2787
531a 3 488 2787
531d 3 489 2787
5320 3 491 2787
5323 3 492 2787
5326 3 494 2787
5329 3 495 2787
532c 3 497 2787
532f 6 498 2787
5335 1 500 2787
5336 2 501 2787
5338 1 502 2787
5339 2a 504 2787
5363 4 513 2787
5367 4 515 2787
536b 4 517 2787
536f 4 519 2787
5373 4 521 2787
5377 4 523 2787
537b 4 525 2787
537f 4 527 2787
5383 4 529 2787
5387 4 531 2787
538b 4 533 2787
538f 4 535 2787
5393 4 537 2787
5397 4 539 2787
539b 7 541 2787
53a2 2 543 2787
53a4 2 544 2787
53a6 19 546 2787
53bf 3 555 2787
53c2 1 557 2787
53c3 1 558 2787
53c4 3 559 2787
53c7 3 563 2787
53ca 3 565 2787
53cd 3 566 2787
53d0 1 567 2787
53d1 1 568 2787
53d2 5 569 2787
53d7 3 573 2787
53da 3 575 2787
53dd 3 576 2787
53e0 3 577 2787
53e3 3 578 2787
53e6 1 579 2787
53e7 1 580 2787
53e8 3 581 2787
53eb 3 585 2787
53ee 3 587 2787
53f1 3 588 2787
53f4 3 589 2787
53f7 3 590 2787
53fa 3 591 2787
53fd 3 592 2787
5400 1 593 2787
5401 1 594 2787
5402 2 595 2787
FUNC 219c 2 0 _freea
FUNC 4240 14 0 _freea
FUNC 5a10 16 0 _freea
FUNC 6904 7 0 _freea
5a10 5 235 281
5a15 11 237 281
4240 3 239 281
4243 11 241 281
6904 7 243 281
219c 2 252 281
FUNC 19e0 13 0 _msize
FUNC 4898 14 0 _msize
FUNC 6b2c 15 0 _msize
4898 5 39 561
489d f 43 561
6b2c 13 43 561
6b3f 2 50 561
19e0 11 46 561
19f1 2 50 561
FUNC 3c6c 2 0 _fptrap
FUNC 3c94 7 0 _fptrap
3c94 0 46 4123
3c6c 1 47 4123
3c94 7 47 4123
3c6d 1 48 4123
FUNC 6dab 106 0 __report_gsfailure
6dab b 140 1392
6db6 5 170 1392
6dbb 6 171 1392
6dc1 6 172 1392
6dc7 6 173 1392
6dcd 6 174 1392
6dd3 6 175 1392
6dd9 7 176 1392
6de0 7 177 1392
6de7 7 178 1392
6dee 7 179 1392
6df5 7 180 1392
6dfc 7 181 1392
6e03 1 182 1392
6e04 6 183 1392
6e0a 3 190 1392
6e0d 5 191 1392
6e12 3 192 1392
6e15 5 193 1392
6e1a 3 194 1392
6e1d 5 195 1392
6e22 6 201 1392
6e28 a 204 1392
6e32 a 206 1392
6e3c a 285 1392
6e46 a 286 1392
6e50 b 293 1392
6e5b b 294 1392
6e66 b 297 1392
6e71 8 298 1392
6e79 8 302 1392
6e81 b 304 1392
6e8c 9 313 1392
6e95 8 315 1392
6e9d 12 319 1392
6eaf 2 320 1392
FUNC 1284 1c 0 _calloc_impl
FUNC 1b18 14 0 _calloc_impl
FUNC 1b5c 10 0 _calloc_impl
FUNC 2d48 6 0 _calloc_impl
FUNC 3488 14 0 _calloc_impl
FUNC 3e7c 3 0 _calloc_impl
FUNC 40e4 f 0 _calloc_impl
FUNC 4c60 10 0 _calloc_impl
FUNC 5650 1 0 _calloc_impl
FUNC 5898 16 0 _calloc_impl
FUNC 5f88 13 0 _calloc_impl
FUNC 5fc0 6 0 _calloc_impl
FUNC 68a8 16 0 _calloc_impl
FUNC 6b60 10 0 _calloc_impl
FUNC 6c7c 8 0 _calloc_impl
5898 5 21 291
589d 11 26 291
40e4 d 28 291
68a8 16 28 291
40f1 2 69 291
3488 7 30 291
348f d 34 291
5650 1 35 291
1b5c 2 39 291
1b5e e 41 291
1284 f 44 291
1293 d 47 291
5f88 13 47 291
1b18 14 59 291
4c60 10 61 291
5fc0 6 62 291
6c7c 8 63 291
6b60 10 52 291
2d48 6 53 291
3e7c 1 53 291
3e7d 2 69 291
FUNC 1048 16 0 realloc
FUNC 11a0 e 0 realloc
FUNC 1744 2 0 realloc
FUNC 1b34 7 0 realloc
FUNC 261c 22 0 realloc
FUNC 2ae0 8 0 realloc
FUNC 3d30 12 0 realloc
FUNC 3df8 3 0 realloc
FUNC 4230 f 0 realloc
FUNC 4284 10 0 realloc
FUNC 4cb0 1 0 realloc
FUNC 5960 1c 0 realloc
FUNC 62a4 14 0 realloc
FUNC 6528 1 0 realloc
FUNC 6550 12 0 realloc
FUNC 65dc b 0 realloc
FUNC 6654 12 0 realloc
FUNC 6734 14 0 realloc
62a4 5 62 618
62a9 f 67 618
65dc 9 68 618
65e5 2 117 618
6654 1 117 618
6655 11 71 618
4284 9 73 618
1b34 1 74 618
428d 7 74 618
1b35 6 109 618
11a0 e 83 618
4cb0 1 84 618
261c 14 85 618
2630 e 94 618
3d30 12 94 618
6734 14 109 618
4230 f 81 618
6550 7 89 618
6557 b 90 618
1744 2 91 618
3df8 1 91 618
6528 1 91 618
3df9 2 117 618
5960 16 111 618
5976 6 112 618
1048 16 103 618
2ae0 8 105 618
FUNC 1000 7 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 11b0 14 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 17c4 8 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 1ac4 6 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 21f4 c 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 2234 9 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 2354 f 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 242c 12 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 2710 3 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 2794 b 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 28d0 13 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 31f8 5 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 327c 14 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 32c0 4a 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 336c 20 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 36f8 f 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 37e0 10 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 3c30 1 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 3f94 e 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 4300 14 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 4338 11 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 497c 16 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 4b10 15 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 4c70 14 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 4ce8 10 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 4db4 1e 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 4e68 12 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 54e8 3 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 5574 14 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 55d0 2 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 55f8 8 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 5628 8 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 578c 3 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 5838 24 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 58f4 c 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 5f44 7 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 61fc f 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 6328 14 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 63a0 c 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 6404 6 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 65e8 28 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 6624 10 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 6ad4 16 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 6d80 e 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 6eb4 3 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
FUNC 6f10 f 0 static int __crtLCMapStringA_stat(struct localeinfo_struct *, unsigned long, unsigned long, const char *, int, char *, int, int, int)
65e8 12 96 3447
65fa 16 101 3447
2354 f 102 3447
2710 3 102 3447
28d0 5 102 3447
31f8 5 102 3447
36f8 f 102 3447
28d5 e 106 3447
3c30 1 107 3447
54e8 3 109 3447
32f8 3 113 3447
32fb f 133 3447
2794 b 134 3447
32c0 36 145 3447
1000 7 146 3447
1ac4 6 149 3447
4300 14 149 3447
4c70 14 149 3447
5574 14 149 3447
55f8 8 149 3447
578c 3 149 3447
58f4 c 149 3447
63a0 c 149 3447
6ad4 16 149 3447
6eb4 3 149 3447
6f10 3 150 3447
6f13 c 151 3447
4db4 1e 160 3447
5838 24 169 3447
6328 14 172 3447
4ce8 10 175 3447
6d80 e 177 3447
4b10 10 186 3447
4b20 5 190 3447
11b0 14 196 3447
2234 9 196 3447
327c 14 196 3447
497c 16 196 3447
4e68 12 196 3447
55d0 2 196 3447
5628 8 196 3447
61fc f 196 3447
6624 10 196 3447
3f94 e 197 3447
336c 20 207 3447
17c4 2 220 3447
37e0 10 220 3447
17c6 6 223 3447
4338 11 233 3447
6404 6 233 3447
5f44 7 240 3447
21f4 8 242 3447
21fc 4 244 3447
242c 12 245 3447
FUNC 3204 2 0 __crtLCMapStringA
FUNC 3c24 7 0 __crtLCMapStringA
FUNC 610c 47 0 __crtLCMapStringA
610c 8 258 3447
6114 b 259 3447
3c24 7 271 3447
611f 34 271 3447
3204 2 272 3447
FUNC 1198 3 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 17b4 e 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 19ac 2 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 24a4 14 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 3318 33 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 357c 2b 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 43c4 b 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 4624 14 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 4d9c 11 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 5094 a 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 5b4c 12 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 5efc c 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 5f38 6 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 6420 c 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 68c0 14 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 6910 12 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 6bf0 28 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
FUNC 6c20 8 0 static int __crtGetStringTypeA_stat(struct localeinfo_struct *, unsigned long, const char *, int, unsigned short *, int, int, int)
6bf0 12 63 3507
6c02 7 67 3507
6c09 f 83 3507
43c4 b 84 3507
3318 33 95 3507
6c20 8 96 3507
1198 3 99 3507
19ac 2 99 3507
24a4 14 99 3507
4624 14 99 3507
5b4c 12 99 3507
5efc c 99 3507
5f38 6 99 3507
6420 c 99 3507
68c0 14 99 3507
17b4 2 100 3507
17b6 c 101 3507
357c f 103 3507
358b 1c 111 3507
4d9c 11 116 3507
5094 6 118 3507
509a 4 120 3507
6910 12 121 3507
FUNC 25dc 40 0 __crtGetStringTypeA
FUNC 4104 7 0 __crtGetStringTypeA
FUNC 5ca0 2 0 __crtGetStringTypeA
25dc 8 133 3507
25e4 b 134 3507
25ef 2d 145 3507
4104 7 145 3507
5ca0 2 146 3507
FUNC 2e0c 37b 0 __free_lc_time
FUNC 641c 3 0 __free_lc_time
2e0c 6 228 1800
2e12 10 229 1800
2e24 8 232 1800
2e2c 8 233 1800
2e34 8 234 1800
2e3c 8 235 1800
2e44 8 236 1800
2e4c 8 237 1800
2e54 7 238 1800
2e5b 8 240 1800
2e63 8 241 1800
2e6b 8 242 1800
2e73 8 243 1800
2e7b 8 244 1800
2e83 8 245 1800
2e8b 8 246 1800
2e93 8 248 1800
2e9b b 249 1800
2ea6 8 250 1800
2eae 8 251 1800
2eb6 8 252 1800
2ebe 8 253 1800
2ec6 8 254 1800
2ece 8 255 1800
2ed6 8 256 1800
2ede 8 257 1800
2ee6 8 258 1800
2eee 8 259 1800
2ef6 8 261 1800
2efe 8 262 1800
2f06 8 263 1800
2f0e 8 264 1800
2f16 8 265 1800
2f1e b 266 1800
2f29 b 267 1800
2f34 b 268 1800
2f3f b 269 1800
2f4a b 270 1800
2f55 b 271 1800
2f60 b 272 1800
2f6b b 274 1800
2f76 b 275 1800
2f81 b 277 1800
2f8c b 278 1800
2f97 b 279 1800
2fa2 b 282 1800
2fad b 283 1800
2fb8 b 284 1800
2fc3 b 285 1800
2fce e 286 1800
2fdc b 287 1800
2fe7 b 288 1800
2ff2 b 290 1800
2ffd b 291 1800
3008 b 292 1800
3013 b 293 1800
301e b 294 1800
3029 b 295 1800
3034 b 296 1800
303f b 298 1800
304a b 299 1800
3055 b 300 1800
3060 b 301 1800
306b b 302 1800
3076 b 303 1800
3081 e 304 1800
308f b 305 1800
309a b 306 1800
30a5 b 307 1800
30b0 b 308 1800
30bb b 309 1800
30c6 b 311 1800
30d1 b 312 1800
30dc b 313 1800
30e7 b 314 1800
30f2 b 315 1800
30fd b 316 1800
3108 b 317 1800
3113 b 318 1800
311e b 319 1800
3129 b 320 1800
3134 e 321 1800
3142 b 322 1800
314d b 324 1800
3158 b 325 1800
3163 b 327 1800
316e b 328 1800
3179 e 329 1800
641c 1 329 1800
641d 2 332 1800
FUNC 1308 14 0 __free_lconv_num
FUNC 14ec 7 0 __free_lconv_num
FUNC 24c0 7 0 __free_lconv_num
FUNC 2d64 14 0 __free_lconv_num
FUNC 3194 14 0 __free_lconv_num
FUNC 33c0 7 0 __free_lconv_num
FUNC 3c9c 7 0 __free_lconv_num
FUNC 42ec 3 0 __free_lconv_num
FUNC 441c 7 0 __free_lconv_num
FUNC 448c 14 0 __free_lconv_num
FUNC 5688 14 0 __free_lconv_num
FUNC 5cdc 17 0 __free_lconv_num
5cdc 6 218 1742
5ce2 11 219 1742
2d64 14 222 1742
441c 7 223 1742
448c 14 225 1742
33c0 7 226 1742
5688 14 228 1742
3c9c 7 229 1742
1308 14 231 1742
24c0 7 232 1742
3194 14 234 1742
14ec 7 235 1742
42ec 1 235 1742
42ed 2 236 1742
FUNC 1028 14 0 __free_lconv_mon
FUNC 16f4 14 0 __free_lconv_mon
FUNC 2040 14 0 __free_lconv_mon
FUNC 2724 7 0 __free_lconv_mon
FUNC 2788 7 0 __free_lconv_mon
FUNC 2834 3 0 __free_lconv_mon
FUNC 28f0 7 0 __free_lconv_mon
FUNC 2b7c 7 0 __free_lconv_mon
FUNC 3360 7 0 __free_lconv_mon
FUNC 34d8 14 0 __free_lconv_mon
FUNC 37cc 7 0 __free_lconv_mon
FUNC 3bac 14 0 __free_lconv_mon
FUNC 3ddc 14 0 __free_lconv_mon
FUNC 4a80 7 0 __free_lconv_mon
FUNC 4aa0 7 0 __free_lconv_mon
FUNC 4b34 14 0 __free_lconv_mon
FUNC 4dec 7 0 __free_lconv_mon
FUNC 4e2c 14 0 __free_lconv_mon
FUNC 4e5c 7 0 __free_lconv_mon
FUNC 4efc 14 0 __free_lconv_mon
FUNC 4f64 7 0 __free_lconv_mon
FUNC 567c 7 0 __free_lconv_mon
FUNC 58dc 16 0 __free_lconv_mon
FUNC 594c 14 0 __free_lconv_mon
FUNC 5b38 14 0 __free_lconv_mon
FUNC 5cb4 7 0 __free_lconv_mon
FUNC 6264 14 0 __free_lconv_mon
FUNC 64f8 14 0 __free_lconv_mon
58dc 6 270 1685
58e2 10 271 1685
3bac 14 274 1685
2788 7 275 1685
16f4 14 277 1685
2b7c 7 278 1685
4b34 14 280 1685
2724 7 281 1685
4efc 14 283 1685
4f64 7 284 1685
3ddc 14 286 1685
28f0 7 287 1685
34d8 14 289 1685
37cc 7 290 1685
594c 14 292 1685
4e5c 7 293 1685
5b38 14 295 1685
5cb4 7 296 1685
2040 14 298 1685
3360 7 299 1685
1028 14 301 1685
567c 7 302 1685
6264 14 304 1685
4a80 7 305 1685
4e2c 14 307 1685
4dec 7 308 1685
64f8 14 310 1685
2834 1 311 1685
4aa0 7 311 1685
2835 2 312 1685
FUNC 1a0a ba 0 _VEC_memzero
FUNC 637c 10 0 __sse2_available_init
637c 0 30 4315
637c d 31 4315
6389 2 32 4315
638b 1 33 4315
FUNC 1c38 103 0 _VEC_memcpy
FUNC 6a38 2c 0 _alloca_probe_16
6a38 0 44 4274
6a38 1 46 4274
6a39 4 47 4274
6a3d 2 48 4274
6a3f 3 49 4274
6a42 2 50 4274
6a44 2 51 4274
6a46 2 52 4274
6a48 1 53 4274
6a49 5 54 4274
6a4e 1 59 4274
6a4f 4 60 4274
6a53 2 61 4274
6a55 3 62 4274
6a58 2 63 4274
6a5a 2 64 4274
6a5c 2 65 4274
6a5e 1 66 4274
6a5f 5 67 4274
PUBLIC 6a4e 0 _alloca_probe_8
FUNC 1d6e 34 0 _allmul
1d6e 0 47 2803
1d6e 4 63 2803
1d72 4 64 2803
1d76 2 65 2803
1d78 4 66 2803
1d7c 2 67 2803
1d7e 4 69 2803
1d82 2 70 2803
1d84 3 72 2803
1d87 1 75 2803
1d88 2 83 2803
1d8a 2 84 2803
1d8c 4 86 2803
1d90 4 87 2803
1d94 2 88 2803
1d96 4 90 2803
1d9a 2 91 2803
1d9c 2 92 2803
1d9e 1 94 2803
1d9f 3 96 2803
FUNC 1680 2b 0 _chkstk
1680 0 65 4276
1680 1 69 4276
1681 4 73 4276
1685 2 74 4276
1687 2 79 4276
1689 2 80 4276
168b 2 81 4276
168d 2 83 4276
168f 5 84 4276
1694 2 87 4276
1696 2 88 4276
1698 2 89 4276
169a 1 90 4276
169b 1 91 4276
169c 2 92 4276
169e 3 93 4276
16a1 1 94 4276
16a2 5 98 4276
16a7 2 99 4276
16a9 2 100 4276
FUNC 5be0 46 0 strcspn
5be0 4 191 2794
5be4 2 198 2794
5be6 1 199 2794
5be7 1 200 2794
5be8 1 201 2794
5be9 1 202 2794
5bea 1 203 2794
5beb 1 204 2794
5bec 1 205 2794
5bed 1 206 2794
5bee 6 212 2794
5bf4 2 216 2794
5bf6 2 217 2794
5bf8 2 218 2794
5bfa 3 219 2794
5bfd 4 220 2794
5c01 2 221 2794
5c03 3 227 2794
5c06 6 229 2794
5c0c 3 234 2794
5c0f 2 236 2794
5c11 2 237 2794
5c13 2 238 2794
5c15 3 239 2794
5c18 4 240 2794
5c1c 2 245 2794
5c1e 2 255 2794
5c20 3 257 2794
5c23 3 259 2794
FUNC 1bcb 40 0 strpbrk
1bcb 4 191 2794
1bcf 2 198 2794
1bd1 1 199 2794
1bd2 1 200 2794
1bd3 1 201 2794
1bd4 1 202 2794
1bd5 1 203 2794
1bd6 1 204 2794
1bd7 1 205 2794
1bd8 1 206 2794
1bd9 6 212 2794
1bdf 2 216 2794
1be1 2 217 2794
1be3 2 218 2794
1be5 3 219 2794
1be8 4 220 2794
1bec 2 221 2794
1bee 5 227 2794
1bf3 2 236 2794
1bf5 2 237 2794
1bf7 2 238 2794
1bf9 3 239 2794
1bfc 4 240 2794
1c00 2 247 2794
1c02 3 248 2794
1c05 3 257 2794
1c08 3 259 2794
FUNC 3bc0 61 0 __ascii_strnicmp
3bc0 6 69 2798
3bc6 3 75 2798
3bc9 2 76 2798
3bcb 2 77 2798
3bcd 3 79 2798
3bd0 3 80 2798
3bd3 2 82 2798
3bd5 2 83 2798
3bd7 5 84 2798
3bdc 2 89 2798
3bde 2 91 2798
3be0 2 93 2798
3be2 2 95 2798
3be4 2 97 2798
3be6 2 98 2798
3be8 3 100 2798
3beb 3 101 2798
3bee 2 103 2798
3bf0 2 104 2798
3bf2 2 106 2798
3bf4 2 107 2798
3bf6 2 109 2798
3bf8 2 112 2798
3bfa 2 113 2798
3bfc 2 115 2798
3bfe 2 116 2798
3c00 2 118 2798
3c02 2 121 2798
3c04 2 122 2798
3c06 3 124 2798
3c09 2 125 2798
3c0b 2 128 2798
3c0d 2 129 2798
3c0f 2 130 2798
3c11 5 133 2798
3c16 2 134 2798
3c18 2 135 2798
3c1a 2 138 2798
3c1c 5 140 2798
PUBLIC 2140 10 RtlUnwind
STACK WIN 4 2cec 54 6 0 8 0 14 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4b70 a 3 0 0 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 25a8 21 7 0 0 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 131c 14 7 0 0 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b94 16 7 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 11e8 1e 7 0 0 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b3c 10 7 0 0 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 669c 7 3 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 16d4 7 7 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 16db 19 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 211c c 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5458 9 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 40f4 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 1fd4 8 8 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1fdc 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 46d8 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 65a8 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1fda 2 2 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1fdc 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 46d8 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 65a8 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 12a0 b 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1ff0 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 198c 4 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1ff5 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 455c 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d0c 5 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 38be 161 c 0 0 10 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 39e1 14 0 0 0 10 24 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 620c a 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 66c3 70 c 0 4 10 1c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 32 - ^ =  $23 $T0 36 - ^ =  $24 $T0 40 - ^ = 
STACK WIN 4 672a 8 0 0 4 10 1c 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 26fc 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2701 f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2bcc 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3888 18 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ea0 1f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3889 17 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4778 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 199c 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2380 6 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ce8 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4448 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 477d 17 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 57d0 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a40 5 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 642c 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a28 e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 21a0 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1db0 5 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 21a5 19 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 24cc 1a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c14 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6a9b 18 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2100 9 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2b50 9 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4594 33 3 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4597 2f 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2944 6 6 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1bc8 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1de4 e 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 22bc 2 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 294a a 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3dd4 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e80 f 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6568 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1bc8 1 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1de4 e 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 22bc 2 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 294a a 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3dd4 3 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3e80 f 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6568 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 69e4 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1d44 c 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2470 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2558 27 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2660 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 269c 18 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 27a4 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 34ec 24 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 48dc 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4bf8 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4dd4 18 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5b74 a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 69e9 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f60 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2470 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2559 26 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2660 9 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 27a4 2 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 48dc 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2470 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 255a 25 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2660 8 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 27a4 2 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 48dc 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 35a7 140 c 0 c 10 30 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 52 - ^ =  $23 $T0 56 - ^ =  $24 $T0 60 - ^ = 
STACK WIN 4 36d2 e 0 0 c 10 30 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 6d68 16 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5464 16 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1df4 f 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5544 f 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3218 1e 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6410 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b78 4 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2888 9 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 44d0 16 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4858 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6415 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e7c 1b 1b 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 11c4 12 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2028 16 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 20c0 12 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2460 f 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2648 f 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 26c4 1a 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 349c 27 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 43a4 1e 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4428 a 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 47c0 2b 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 49a4 18 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4a64 1c 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d50 17 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e97 1e 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f5c 2 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5810 25 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 59cc 16 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5d18 34 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 64e8 5 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6648 7 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6678 22 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6894 11 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 70ac 18 0 0 4 0 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e92 5 5 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 11c4 12 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 2028 16 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 20c0 12 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 2460 f 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 2648 8 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 26c4 1a 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 349c 27 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 43a4 1e 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 4428 a 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 47c0 2b 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 49a4 18 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 4a64 1c 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 4d50 17 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 4e97 1e 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 4f5c 2 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 5810 25 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 59cc 16 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 5d18 34 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 64e8 5 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 6648 7 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 6678 22 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 6894 11 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 70ac 18 0 0 4 4 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ = 
STACK WIN 4 4e93 4 4 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 11c4 12 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 2028 16 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 20c0 12 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 2460 f 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 2648 5 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 26c4 1a 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 349c 27 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 43a4 1e 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 4428 a 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 47c0 2b 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 49a4 18 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 4a64 1c 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 4d50 17 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 4e97 1e 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 4f5c 2 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 5810 25 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 59cc 16 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 5d18 34 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 64e8 5 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 6648 7 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 6678 22 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 6894 11 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 70ac 18 0 0 4 8 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ = 
STACK WIN 4 11c4 12 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 2028 16 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 20c0 12 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 2460 f 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 2648 4 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 26c4 1a 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 349c 27 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 43a4 1e 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 4428 a 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 47c0 2b 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 49a4 18 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 4a64 1c 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 4d50 17 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 4e97 1e 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 4f5c 2 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 5810 25 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 59cc 16 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 5d18 34 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 64e8 5 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 6648 7 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 6678 22 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 6894 11 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 70ac 18 0 0 4 c 1fc 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 512 - ^ =  $23 $T0 516 - ^ =  $24 $T0 520 - ^ = 
STACK WIN 4 1984 1 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2388 16 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4ab4 16 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4be0 17 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5ac8 13 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 650c 6 6 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1490 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1540 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1720 14 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 174c 1b 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 19d8 2 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2054 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2218 9 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2440 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2870 e 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2928 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2ae8 11 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2d78 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 356c f 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 38a8 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ed0 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3fa4 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 44e8 7 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4810 17 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4cc8 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f50 7 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5564 e 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 56d4 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 57a4 e 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 59f8 16 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5bbc e 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ca4 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5d60 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5e38 18 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5e60 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6090 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 61f0 7 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6228 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 648c c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 64dc 5 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6512 14 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 66a4 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 68e4 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6ee8 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f98 c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1490 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1540 3 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1720 14 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 174c 1b 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 19d8 2 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2054 1 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2218 9 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2440 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2870 e 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2928 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2ae8 11 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2d78 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 356c f 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 38a8 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3ed0 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3fa4 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 44e8 7 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4810 17 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4cc8 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4f50 7 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5564 e 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 56d4 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 57a4 e 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 59f8 16 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5bbc e 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5ca4 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5d60 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5e38 18 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5e60 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6090 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 61f0 7 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6228 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 648c c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 64dc 5 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6512 14 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 66a4 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 68e4 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6ee8 3 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6f98 c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1490 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1540 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1720 14 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 174c 1b 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 19d8 2 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2440 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2870 e 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2928 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2ae8 11 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2d78 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 356c f 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 38a8 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 3ed0 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 3fa4 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 44e8 7 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4810 17 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4cc8 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4f50 7 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5564 e 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 56d4 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 57a4 e 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 59f8 16 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5bbc e 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5ca4 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5d60 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5e38 18 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5e60 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6090 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 61f0 7 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6228 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 648c c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 64dc 5 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 66a4 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 68e4 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6ee8 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6f98 c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1490 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 1750 17 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 2440 c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 2928 c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 2ae8 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 2d78 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 38a8 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 3ed0 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 3fa4 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 44e8 7 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 4cc8 c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 56d4 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 59f8 16 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 5ca4 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 5d60 c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 5e38 18 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 6090 c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 61f0 7 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 6228 c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 648c c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 64dc 5 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 66a4 c 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 68e4 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 6ee8 3 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 4139 dc 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4148 a8 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 414f a0 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4191 5d 0 0 0 c 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ =  $20 $T0 12 - ^ = 
STACK WIN 4 1fac d d 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1018 8 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 19b4 e 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 19f4 2 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1dd0 14 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1fb9 1b 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2058 f 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 209c 9 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2364 1a 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 253c 4 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2598 9 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 25cc 10 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2718 b 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2730 e 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 28a8 a 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 28fc 2 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 290c 14 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b34 f 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2bfc 10 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2d88 7 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3730 d 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 377c 2 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 37b0 10 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3c38 24 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3c84 10 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3db8 1b 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e90 e 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3efc 13 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4110 12 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 42dc f 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 42f0 10 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4518 6 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4610 7 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4684 11 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 46f8 13 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4848 a 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4994 f 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 49bc e 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4aac 1 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4cb8 f 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4ed8 24 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f10 3 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f30 e 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 501c 10 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5518 3 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 569c 3 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 57bc 12 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a50 10 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5cc0 14 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5e84 f 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6448 f 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6530 e 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 66b0 13 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6970 4 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6aec 7 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f30 e 0 0 c 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1fb6 3 3 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 1018 8 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 19b4 e 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 19f4 2 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 1dd0 14 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 1fb9 1b 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2058 f 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 209c 9 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2364 1a 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 253c 4 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2598 9 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 25cc 10 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2718 b 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2730 e 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 28a8 a 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 28fc 2 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 290c 14 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2b34 f 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2bfc 10 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2d88 7 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 3730 d 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 377c 2 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 37b0 10 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 3c38 24 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 3c84 10 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 3db8 1b 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 3e90 e 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 3efc 13 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4110 12 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 42dc f 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 42f0 10 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4518 6 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4610 7 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4684 11 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 46f8 5 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4848 a 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4994 f 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 49bc e 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4aac 1 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4cb8 f 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4ed8 24 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4f10 3 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4f30 e 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 501c 10 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 569c 3 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 57bc 12 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 5a50 10 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 5cc0 14 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 5e84 f 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 6448 f 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 6530 e 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 66b0 13 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 6aec 7 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 6f30 e 0 0 c 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 1018 8 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 19b4 e 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 19f4 2 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 1dd0 14 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 1fb9 1b 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2058 f 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 209c 9 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2364 1a 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 253c 4 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2598 9 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 25cc 10 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2718 b 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2730 e 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 28a8 a 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 28fc 2 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 290c 14 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2b34 f 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2bfc 10 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2d88 7 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 3730 d 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 377c 2 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 37b0 10 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 3c38 24 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 3c84 10 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 3db8 1b 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 3e90 e 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 3efc 13 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4110 12 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 42dc f 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 42f0 10 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4518 6 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4610 7 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4684 11 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 46f8 4 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4848 a 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4994 f 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 49bc e 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4aac 1 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4cb8 f 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4ed8 24 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4f10 3 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4f30 e 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 501c 10 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 569c 3 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 57bc 12 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 5a50 10 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 5cc0 14 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 5e84 f 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6448 f 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6530 e 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 66b0 13 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6aec 7 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6f30 e 0 0 c 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2008 d d 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1ad8 3 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2015 12 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 22dc 16 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2320 5 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2934 3 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 33cc 17 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4254 30 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 432c 5 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4ad0 28 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4b8c 2c 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c4c 12 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6960 10 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2011 4 4 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 1ad8 3 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 2015 12 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 22dc 16 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 2320 3 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 2934 3 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 33cc 17 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 4254 30 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 432c 5 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 4ad0 28 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 4b8c 2c 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 4c4c 12 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 6960 10 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 2014 1 1 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 1ad8 3 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 2015 12 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 22dc 16 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 2320 2 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 2934 3 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 33cc 17 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 4254 30 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 432c 5 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 4ad0 28 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 4b8c 2c 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 4c4c 12 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 6960 10 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 1ad8 3 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 2015 12 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 22dc 16 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 2320 1 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 2934 3 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 33cc 17 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 4254 30 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 432c 5 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 4ad0 28 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 4b8c 2c 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 4c4c 12 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 6960 10 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 3fb4 a a 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 14f8 30 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 23d0 10 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 293c 1 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2bbc e 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3524 c 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3548 f 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d10 18 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3df0 8 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3dfc 1c 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3fbe 18 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 436c 12 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 68d4 9 0 0 0 0 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3fbd 1 1 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 14f8 2e 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 23d0 10 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 293c 1 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 2bbc e 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 3524 c 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 3548 f 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 3d10 18 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 3df0 8 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 3dfc 1c 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 3fbe 18 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 436c 12 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 68d4 9 0 0 0 4 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ = 
STACK WIN 4 14f8 2d 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 23d0 10 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 293c 1 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 2bbc e 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 3524 c 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 3548 f 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 3d10 18 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 3df0 8 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 3dfc 1c 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 3fbe 18 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 436c 12 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 68d4 9 0 0 0 8 c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ = 
STACK WIN 4 14f9 29 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 23d0 10 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 293c 1 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 3524 c 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 3d10 18 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 3dfc 1c 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 68d4 9 0 0 0 c c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 16 - ^ =  $23 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 5c70 9 9 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1008 b 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1258 f 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 12c8 e 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1548 33 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1708 18 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 17a4 10 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 197c 2 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1988 3 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 22f8 1c 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 23e0 4c 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 244c 14 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 24b8 8 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 24e8 1c 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 26e0 1c 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2ad0 10 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b6c f 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2bf0 5 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2d54 a 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2db0 16 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2dcc 8 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 31a8 4 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 31e0 16 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3260 f 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 334c 14 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 33ac 12 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3cb0 3 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d28 6 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e24 2 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e34 20 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e5c 1f 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 42a4 f 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4380 f 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4434 12 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4618 6 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 47ec 14 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4920 a 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5c79 27 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5d6c 8 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 617c 18 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6248 a 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 636c e 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 63f0 14 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6a74 a 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6ba0 3f 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c4c 14 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c84 8 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f90 3 0 0 0 0 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1008 b 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 1258 f 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 12c8 e 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 1548 33 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 1708 18 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 17a4 10 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 197c 2 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 1988 1 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 22f8 1c 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 23e0 4c 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 244c 14 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 24b8 8 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 24e8 1c 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 26e0 1c 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 2ad0 10 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 2b6c f 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 2bf0 5 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 2d54 a 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 2db0 16 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 31a8 4 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 31e0 16 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 3260 f 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 334c 14 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 33ac 12 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 3cb0 3 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 3d28 6 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 3e24 2 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 3e34 20 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 3e5c 1f 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 42a4 f 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 4380 f 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 4434 12 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 4618 6 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 47ec 14 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 4920 a 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 5c79 27 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 5d6c 8 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 617c 18 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 6248 a 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 636c e 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 63f0 14 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 6a74 a 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 6ba0 3f 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 6c4c 14 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 6c84 8 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 6f90 3 0 0 0 4 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ = 
STACK WIN 4 1008 b 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 1258 f 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 12c8 e 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 1548 33 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 1708 18 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 17a4 10 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 197c 2 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 22f8 1c 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 23e1 10 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 244c 14 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 24b8 8 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 24e8 1c 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 26e0 1c 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 2ad0 10 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 2b6c f 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 2bf0 5 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 2d54 a 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 2db0 16 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 31a8 4 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 31e0 16 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 3260 f 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 334c 14 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 33ac 12 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 3cb0 3 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 3d28 6 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 3e24 2 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 3e34 20 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 42a4 f 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 4380 f 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 4434 12 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 4618 6 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 47ec 14 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 4920 a 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 5d6c 8 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 617c 18 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 6248 a 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 636c e 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 63f0 14 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 6a74 a 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 6ba0 3f 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 6c4c 14 0 0 0 8 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ = 
STACK WIN 4 1008 b 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 1258 f 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 12c8 e 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 1548 33 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 1708 18 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 17a4 10 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 197c 1 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 22f8 1c 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 23e2 f 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 244c 14 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 24b8 8 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 24e8 1c 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 26e0 1c 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 2ad0 10 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 2b6c f 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 2bf0 5 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 2d54 a 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 2db0 16 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 31a8 4 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 31e0 16 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 3260 f 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 334c 14 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 33ac 12 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 3cb0 3 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 3d28 6 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 3e24 2 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 3e34 20 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 42a4 f 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 4380 f 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 4434 12 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 4618 6 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 47ec 14 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 4920 a 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 5d6c 8 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 617c 18 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 6248 a 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 636c e 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 63f0 14 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 6a74 a 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 6ba0 3f 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 6c4c 14 0 0 0 c 4c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 80 - ^ =  $20 $T0 84 - ^ =  $24 $T0 88 - ^ = 
STACK WIN 4 5754 e e 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 12ac 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2d94 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 447c 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5762 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6958 2 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5757 b b 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 12ac 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2d94 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 447c 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5762 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6958 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 12ac 1 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2d94 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 447c 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5762 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6958 2 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 57e0 e e 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2480 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3cf8 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3e2c 2 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 57ee 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5c60 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 57e3 b b 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2480 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3cf8 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3e2c 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 57ee 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5c60 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2480 1 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 3cf8 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 3e2c 2 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 57ee 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5c60 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1474 9 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2bb0 9 0 0 4 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3240 3 3 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3243 1c 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 338c 1b 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6444 4 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3243 1c 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 338c 1b 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6444 3 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2068 16 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2b9c 14 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 48c8 14 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5b84 5 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5e0c e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 10ca b4 c 0 8 10 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 1166 e 0 0 8 10 18 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 1175 8 0 0 8 10 18 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 2954 4 4 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2958 24 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4520 1c 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 45cc 1e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 48ec 23 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4b28 c 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 616c 9 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2957 1 1 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2958 24 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4520 1c 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 45cc 1e 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 48ec 23 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4b28 b 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 616c 9 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2958 24 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4520 1c 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 45cc 1e 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 48ec 23 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4b28 8 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 616c 9 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4314 3 3 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 12c0 7 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4317 15 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 44f4 4 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4f9c 1 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 12c0 7 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4317 15 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 44f4 3 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4f9c 1 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1330 12f c 0 4 10 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 1447 b 0 0 4 10 18 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 1453 b 0 0 4 10 18 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 6458 3 3 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 1608 54 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2228 5 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3768 13 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3880 2 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3ba8 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3cb8 1e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3d90 28 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 45ec 9 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 47a4 19 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4c18 1f 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 547c 52 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 54f8 16 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 58b0 24 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 597c 33 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5c28 e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 645b 1b 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 1608 54 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 2228 5 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 3768 13 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 3880 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 3ba8 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 3cb8 1e 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 3d90 28 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 45ec 9 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 47a4 19 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 4c18 1f 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 547c 52 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 54f8 16 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 58b0 24 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 597c 33 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 5c28 e 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 645b 1b 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ = 
STACK WIN 4 1609 53 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 2228 5 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3768 13 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3880 2 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3ba8 1 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3cb8 1e 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3d90 28 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 47a4 19 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4c18 1f 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 547c 52 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 54f8 16 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 58b0 24 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 597c 33 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5c28 e 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 472c 1e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3b48 17 17 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 10b0 1a 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1180 c 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1528 17 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1734 10 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1768 2c 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 210c 10 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 267c 13 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2bd0 20 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3270 a 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3754 13 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3b5f 1f 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d54 3 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3f88 c 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 42b4 d 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f40 10 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a60 1c 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a84 22 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5d88 d 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5e70 f 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f50 12 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5fcc 12 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 61bc 1d 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6670 3 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6a80 14 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6b0c 20 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6cd8 10 0 0 10 0 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3b51 e e 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 1180 c 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 1734 10 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 1768 2c 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 210c 10 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 2bd0 20 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 3270 6 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 3b5f 1f 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 3d54 3 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 42b4 d 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 4f40 10 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 5a84 22 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 6670 3 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 6b0c 20 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 6cd8 10 0 0 10 4 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ = 
STACK WIN 4 3b55 a a 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 1180 c 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 1734 10 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 1768 2c 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 210c 10 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 2bd0 20 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 3270 5 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 3b5f 1f 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 3d54 3 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 42b4 d 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 4f40 10 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 5a84 22 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 6670 3 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 6b0c 20 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 6cd8 10 0 0 10 8 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ = 
STACK WIN 4 1180 c 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 1734 10 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 1768 2c 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 210c 10 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 2bd0 20 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 3270 4 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 3b5f 1f 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 3d54 3 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 42b4 d 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 4f40 10 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 5a84 22 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 6670 3 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 6b0c 20 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 6cd8 10 0 0 10 c 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 2244 17 17 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 225b 18 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2740 48 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 33e4 d 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3fd8 b 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4390 f 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d68 c 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5588 e 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 61a4 4 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6610 c 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 225a 1 1 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 225b 18 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 2740 48 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 33e4 d 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 3fd8 b 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 4390 f 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 4d68 c 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 5588 e 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 61a4 2 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 6610 c 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 225b 18 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 2740 48 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 33e4 d 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 3fd8 b 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 4390 f 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 4d68 c 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 5588 e 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 61a4 1 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 6610 c 0 0 0 8 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ = 
STACK WIN 4 2741 47 0 0 0 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ =  $23 $T0 28 - ^ = 
STACK WIN 4 33e4 d 0 0 0 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ =  $23 $T0 28 - ^ = 
STACK WIN 4 3fd8 b 0 0 0 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ =  $23 $T0 28 - ^ = 
STACK WIN 4 4390 f 0 0 0 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ =  $23 $T0 28 - ^ = 
STACK WIN 4 6610 c 0 0 0 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $24 $T0 24 - ^ =  $23 $T0 28 - ^ = 
STACK WIN 4 4570 f 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3a1f 129 1a 0 c 8 328 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 812 - ^ =  $24 $T0 816 - ^ = 
STACK WIN 4 5c38 25 3 0 14 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5c3b 21 0 0 14 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6922 2d 5 0 14 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f50 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2130 4 4 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2134 7 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2dec 14 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4358 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 453c 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 479c 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4b48 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 55a8 27 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2133 1 1 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2134 7 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2dec 14 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4358 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 479c 3 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4b48 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 55a8 27 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2134 7 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2dec 14 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4358 1 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 479c 3 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4b48 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 55a8 27 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2298 a a 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 20ec 14 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 22a2 6 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 31d4 6 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3744 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4350 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 46e4 14 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4a54 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5554 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5ec4 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 63c0 d 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6b90 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 229b 7 7 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 20ec 14 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 22a2 6 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 31d4 6 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 3744 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4350 3 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 46e4 14 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4a54 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5554 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5ec4 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 63c0 d 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6b90 10 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ = 
STACK WIN 4 20ec 14 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 22a2 6 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 31d4 6 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3744 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4350 3 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 46e4 14 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4a54 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5554 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5ec4 1 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 63c0 d 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6b90 10 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 20ec 14 0 0 0 c 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 31d4 6 0 0 0 c 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5554 10 0 0 0 c 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 63c0 d 0 0 0 c 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6b90 10 0 0 0 c 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4218 17 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ff7 c2 c 0 4 10 1c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 32 - ^ =  $23 $T0 36 - ^ =  $24 $T0 40 - ^ = 
STACK WIN 4 40b0 8 0 0 4 10 1c 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 44a0 9 9 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 118c b 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1af0 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3718 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 44a9 16 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5bb4 1 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 118c 9 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1af0 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3718 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 44a9 16 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5bb4 1 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2580 a a 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 14ac 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1990 c 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1da4 c 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 258a e 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2e00 4 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4674 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 470c 4 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4794 2 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d40 e 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 59b8 14 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6158 14 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6a94 7 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c8c a 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2589 1 1 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 14ac 10 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1990 c 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1da4 c 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 258a e 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2e00 4 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4674 10 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 470c 2 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4794 2 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4d40 e 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 59b8 14 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6158 14 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6a94 7 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6c8c a 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 14ac 10 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1990 c 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1da4 c 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 258a e 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2e00 4 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4674 10 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 470c 1 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4794 2 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4d40 e 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 59b8 14 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6158 14 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6a94 7 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6c8c a 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5b20 6 6 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 14a0 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 22f4 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2838 e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 28e4 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2da4 a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 34d0 8 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d5c 16 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4580 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4a4c 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4cf8 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4eb8 1f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a48 6 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5b26 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5b8c 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5df8 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 60ec 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 65a0 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 14a0 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 22f4 1 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 2838 e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 28e4 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 2da4 a 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 34d0 8 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 3d5c 16 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4580 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4a4c 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4cf8 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4eb8 1f 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5a48 6 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5b26 12 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5b8c 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5df8 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 60ec 2 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 65a0 2 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 14a0 7 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 2838 e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 28e4 7 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 2da4 4 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 34d0 8 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3d5c 16 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4580 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4a4c 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4cf8 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4eb8 1f 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5a48 6 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5df8 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 60ed 1 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 65a0 2 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 14a0 7 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 2838 e 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 28e4 7 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 2da4 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 34d0 8 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 3d5c 16 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4580 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4a4c 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4cf8 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4eb8 1f 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5a48 6 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5df8 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 65a0 2 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5f78 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1480 5 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 23bc 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3510 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ec8 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f7d 5 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6ab4 1f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5774 6 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5d4c 13 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6c60 4 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4638 39 c 0 0 10 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 4658 4 0 0 0 10 18 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 4834 11 0 0 4 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 15b8 1e 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 63ac 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 15d8 18 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3538 f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 42c8 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 45c8 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e1c f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 55f0 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 63b1 a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 15d8 18 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 42c8 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4e1c f 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3708 d 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 17d8 1a3 c 0 4 10 30 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 52 - ^ =  $23 $T0 56 - ^ =  $24 $T0 60 - ^ = 
STACK WIN 4 1939 14 0 0 4 10 30 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 502c f 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2848 f 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6fd0 f 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5708 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1660 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 570d 1a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 60b0 4 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c18 5 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 43e8 f f 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2274 12 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2484 10 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2640 2 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b88 10 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 32ac 13 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4124 15 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 43f7 24 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 45f8 18 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4a2c 20 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4b68 2 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4bb8 18 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d84 5 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6a0c 1a 0 0 4 0 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 43ef 8 8 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2274 12 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2484 10 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2640 2 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 2b88 10 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 32ac 13 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4124 15 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 43f7 24 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 45f8 18 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4a2c 20 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4b68 2 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4bb8 18 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 4d84 3 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 6a0c 1a 0 0 4 4 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ = 
STACK WIN 4 43f0 7 7 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2274 12 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2484 10 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2640 2 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2b88 10 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 32ac 13 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4124 15 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 43f7 24 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 45f8 18 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4a2c 20 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4b68 2 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4bb8 18 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4d84 2 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6a0c 1a 0 0 4 8 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2274 12 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 2484 10 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 2640 2 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 2b88 10 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 32ac 13 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 4124 15 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 43f7 24 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 45f8 18 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 4a2c 20 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 4b68 2 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 4bb8 18 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 4d84 1 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 6a0c 1a 0 0 4 c 4 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ =  $24 $T0 16 - ^ = 
STACK WIN 4 14bc 3 3 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 14bf 2c 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2658 7 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 318c 5 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 14bf 2c 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2658 6 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 318c 5 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 157b 3c c 0 4 10 1c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 32 - ^ =  $23 $T0 36 - ^ =  $24 $T0 40 - ^ = 
STACK WIN 4 15b1 5 0 0 4 10 1c 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 56a4 17 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6ebc 4 4 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6154 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6b70 20 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6ec0 2 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6ebf 1 1 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6154 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6b70 20 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6ec0 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6154 1 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6b70 20 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6ec0 2 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4e40 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4af8 17 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e45 17 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5608 4 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 585c 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6fa4 1a 1a 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1234 5 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b2c 2 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4948 13 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e08 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5d9c 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6d60 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6fbe 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6fb6 8 8 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 1234 3 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 1b2c 2 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4948 13 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4e08 12 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5d9c 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6d60 3 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6fbe 12 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6fb7 7 7 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1234 2 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1b2c 2 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4948 13 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4e08 12 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5d9c 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6d60 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6fbe 12 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1234 1 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 1b2c 2 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4948 13 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4e08 12 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5d9c 10 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6d60 3 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6fbe 12 0 0 8 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6fee bc 35 0 4 10 18 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 28 - ^ =  $23 $T0 32 - ^ =  $24 $T0 36 - ^ = 
STACK WIN 4 7079 13 0 0 4 10 18 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 2148 1e 1e 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1064 4c 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 123c 1b 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1460 14 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 19c4 12 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2166 22 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b00 14 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 31b4 e 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3290 1a 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 36e8 8 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3794 1c 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 474c 1c 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 496c 10 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4ca0 10 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d18 1c 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5640 2 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 56e4 16 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5cf4 10 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5e50 10 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ee8 13 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f18 c 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f6c f 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f7c 14 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 70d4 10 0 0 c 0 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 215e 8 8 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 1064 4c 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 123c 1b 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 1460 14 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 19c4 12 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 2166 22 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 2b00 14 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 31b4 e 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 3290 1a 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 36e8 8 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 3794 1c 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 474c 1c 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 496c 10 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 4ca0 10 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 4d18 1c 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 5640 2 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 56e4 16 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 5cf4 10 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 5e50 10 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 5ee8 13 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 5f18 c 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 6f6c 8 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 6f7c 14 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 70d4 10 0 0 c 4 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ = 
STACK WIN 4 2165 1 1 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 1064 4c 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 123c 1b 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 1460 14 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 19c4 12 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 2166 22 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 2b00 14 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 31b4 e 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 3290 1a 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 36e8 8 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 3794 1c 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 474c 1c 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 496c 10 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 4ca0 10 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 4d18 1c 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 5640 2 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 56e4 16 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 5cf4 10 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 5e50 10 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 5ee8 13 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 5f18 c 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 6f6c 5 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 6f7c 14 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 70d4 10 0 0 c 8 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ = 
STACK WIN 4 1064 4c 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 123c 1b 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 1460 14 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 19c4 12 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 2166 22 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 2b00 14 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 31b4 e 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 3290 1a 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 36e8 8 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 3794 1c 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 474c 1c 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 496c 10 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 4ca0 10 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 4d18 1c 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 5640 2 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 56e4 16 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 5cf4 10 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 5e50 10 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 5ee8 13 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 5f18 c 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 6f6c 4 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 6f7c 14 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 70d4 10 0 0 c c 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 3d78 a a 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 21d0 14 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 28c0 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3784 a 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d82 e 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4880 18 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 48bc b 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4b7c 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f58 4 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5448 e 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 55d8 12 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f9c 2 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5fb4 7 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6054 c 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6194 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6d50 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d7e 4 4 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3784 a 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3d82 e 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4f58 2 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5fb4 7 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6194 10 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3784 a 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 3d82 e 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4f58 1 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5fb4 7 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6194 10 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 49dc e e 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 103c c 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 11d8 10 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 12fc b 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1ae0 e 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b4c f 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1e0c 5 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 21e4 10 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 228c 4 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 22b0 c 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3720 e 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 37c0 b 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 43d4 14 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 46c8 10 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4828 c 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 495c a 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 49ea 26 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c38 2 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c84 5 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a38 5 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5dc8 c 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 61e0 e 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6288 4 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 63e0 d 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6a28 10 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6af4 18 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c3c e 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 70c4 10 0 0 10 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 49e5 5 5 0 10 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 1b4c f 0 0 10 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 49ea e 0 0 10 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4c38 2 0 0 10 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5a38 3 0 0 10 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6c3c e 0 0 10 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 49e9 1 1 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 11d8 10 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1b4c f 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 495c 8 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 49ea e 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4c38 2 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5a38 5 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 61e0 e 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6c3c e 0 0 10 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1b4c f 0 0 10 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 49ea e 0 0 10 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4c38 2 0 0 10 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5a38 1 0 0 10 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6c3c e 0 0 10 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 20dc 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 20d4 8 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 20e1 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5520 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 15f0 a a 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 15fa e 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 17cc c 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 23a0 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2494 a 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2858 18 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3208 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ca8 8 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4bd0 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5530 7 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5880 4 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f08 4 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f24 14 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 15f6 4 4 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 15fa e 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 23a0 10 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2494 a 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5530 7 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5880 2 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 15fa e 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 23a0 10 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2494 a 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5530 7 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5880 1 0 0 c 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1b7c 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1268 d 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b81 11 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1e04 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 31c4 f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 49cc f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5eac 15 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1275 f 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 69d4 7 7 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1488 6 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b04 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1d3c 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5fe0 1f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 69db 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c64 18 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f40 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 69da 1 1 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1488 4 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1b04 12 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1d3c 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5fe0 1f 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 69db 2 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6c64 18 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6f40 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1488 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1b04 12 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1d3c 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5fe0 1f 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 69db 2 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6c64 18 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6f40 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5598 7 7 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3cd8 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 541c 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 553c 6 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 559f 2 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6950 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6ec8 1f 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6ef0 20 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 559e 1 1 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3cd8 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 541c 12 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 553c 4 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 559f 2 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6950 3 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6ec8 1f 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6ef0 20 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3cd8 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 541c 12 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 553c 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 559f 2 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6950 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6ec8 1f 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6ef0 20 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6f20 7 7 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2674 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b5c 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 38b8 6 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 435c f 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 633c 1c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6580 1f 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 65b0 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f27 2 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f26 1 1 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2674 3 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2b5c 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 38b8 4 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 435c f 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 633c 1c 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6580 1f 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 65b0 12 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6f27 2 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2674 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2b5c 10 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 38b8 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 435c f 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 633c 1c 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6580 1f 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 65b0 12 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6f27 2 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 28b4 c 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3d08 6 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5510 6 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5600 6 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5f14 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6294 f 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 68f4 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 69cc 6 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6fe0 e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5900 6 6 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 2208 f 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 387c 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 48ac f 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5906 3e 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6be0 b 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5903 3 3 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2208 f 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 387c 2 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 48ac f 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5906 3e 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6be0 b 0 0 0 4 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5904 2 2 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2208 f 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 387c 1 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 48ac f 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 5906 3e 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6be0 b 0 0 0 8 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 2c0c 17 17 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 12dc 18 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1794 f 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b6c 7 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2548 e 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2c23 22 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3238 8 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e54 2 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ee0 16 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 445c 20 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4800 f 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4910 8 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4a8c 12 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d8c e 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 566c f 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 56fc c 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 59e4 14 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5aa8 6 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5b08 17 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5b60 14 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5db4 12 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6060 c 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 60b4 6 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6218 f 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 62b8 6a 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 697c 3 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6b44 1b 0 0 0 0 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2c22 1 1 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 12dc 18 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 1794 f 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 1b6c 7 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 2548 e 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 2c23 22 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 3238 8 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 3e54 2 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 3ee0 16 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 445c 20 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 4800 f 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 4910 8 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 4a8c 12 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 4d8c 7 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 566c f 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 56fc c 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 59e4 14 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 5aa8 6 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 5b08 17 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 5b60 14 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 5db4 12 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 6060 c 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 60b4 6 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 6218 f 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 62b8 6a 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 697c 3 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 6b44 1b 0 0 0 4 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ = 
STACK WIN 4 12dc 18 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 1794 f 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 1b6c 7 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 2548 e 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 2c23 22 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 3238 8 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 3e54 2 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 3ee0 16 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 445c 20 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 4800 f 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 4910 8 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 4a8c 12 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 4d8c 4 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 566c f 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 56fc c 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 59e4 14 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 5aa8 6 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 5b08 17 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 5b60 14 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 5db4 12 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 6060 c 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 60b4 6 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 6218 f 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 62b8 6a 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 697c 3 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 6b44 1b 0 0 0 8 51c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 1312 - ^ =  $24 $T0 1316 - ^ = 
STACK WIN 4 2c45 a4 c 0 0 10 1c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 32 - ^ =  $23 $T0 36 - ^ =  $24 $T0 40 - ^ = 
STACK WIN 4 2cdd b 0 0 0 10 1c 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 6cbc b b 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 12b0 8 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2084 18 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2314 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2504 27 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2904 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3558 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d74 e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 560c 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5d04 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f68 a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6cc7 11 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6cc5 2 2 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 12b0 8 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2084 18 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2314 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2504 27 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2904 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3558 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4d74 e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 560c 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5d04 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5f68 a 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6cc7 11 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3b80 9 9 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 19fc e 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2690 7 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b98 3 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3b89 1f 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e18 c 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4710 f 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4868 10 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 56bc 16 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5e20 16 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6020 1f 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6f64 2 0 0 0 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 19fc e 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 2690 7 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 2b98 1 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 3b89 1f 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 3e18 c 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 4710 f 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 4868 10 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 56bc 16 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 5e20 16 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 6020 1f 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 6f64 2 0 0 0 4 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ = 
STACK WIN 4 64a8 1b 1b 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1208 29 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 16ac 20 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1bc0 3 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2188 11 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 23b0 6 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2894 11 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b28 7 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 330c c 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 40bc e 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 454c f 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4720 6 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4768 f 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4b58 10 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c40 c 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5404 12 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5728 2a 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5800 e 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ab0 17 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5adc 11 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ba8 c 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5bcc 14 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5fa4 8 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6010 f 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6074 1c 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 609c 14 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 60c0 2a 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 60f4 17 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6358 14 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 638c 11 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 63d4 5 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 64c3 19 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 64f0 3 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 65c4 18 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6974 7 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6a64 f 0 0 8 0 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 64bb 8 8 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 1208 29 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 16ac 20 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 1bc0 3 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 2188 11 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 23b0 6 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 2894 11 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 2b28 7 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 330c c 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 40bc e 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 454c f 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 4720 6 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 4768 8 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 4b58 10 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 4c40 c 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5404 12 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5728 2a 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5800 e 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5ab0 17 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5adc 11 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5ba8 c 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5bcc 14 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 5fa4 8 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 6010 f 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 6074 1c 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 609c 14 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 60c0 2a 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 60f4 17 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 6358 14 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 638c 11 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 63d4 5 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 64c3 19 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 64f0 3 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 65c4 18 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 6974 7 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 6a64 f 0 0 8 4 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ = 
STACK WIN 4 64bf 4 4 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 1208 29 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 16ac 20 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 1bc0 3 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 2188 11 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 23b0 6 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 2894 11 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 2b28 7 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 330c c 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 40bc e 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 454c f 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 4720 6 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 4768 5 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 4b58 10 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 4c40 c 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5404 12 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5728 2a 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5800 e 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5ab0 17 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5adc 11 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5ba8 c 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5bcc 14 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 5fa4 8 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 6010 f 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 6074 1c 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 609c 14 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 60c0 2a 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 60f4 17 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 6358 14 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 638c 11 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 63d4 5 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 64c3 19 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 64f0 3 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 65c4 18 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 6974 7 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 6a64 f 0 0 8 8 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ = 
STACK WIN 4 1208 29 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 16ac 20 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 1bc0 3 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 2188 11 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 23b0 6 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 2894 11 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 2b28 7 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 330c c 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 40bc e 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 454c f 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 4720 6 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 4768 4 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 4b58 10 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 4c40 c 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5404 12 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5728 2a 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5800 e 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5ab0 17 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5adc 11 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5ba8 c 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5bcc 14 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 5fa4 8 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 6010 f 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 6074 1c 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 609c 14 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 60c0 2a 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 60f4 17 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 6358 14 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 638c 11 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 63d4 5 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 64c3 19 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 64f0 3 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 65c4 18 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 6974 7 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 6a64 f 0 0 8 c 20 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 36 - ^ =  $23 $T0 40 - ^ =  $24 $T0 44 - ^ = 
STACK WIN 4 1e11 19a c 0 4 10 24 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 40 - ^ =  $23 $T0 44 - ^ =  $24 $T0 48 - ^ = 
STACK WIN 4 1f72 8 0 0 4 10 24 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 165c 3 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5dd4 12 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6234 13 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 1c0c 8 8 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1674 7 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1c14 24 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d74 2 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c90 e 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5430 16 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 59b0 2 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6254 10 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 643c 3 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6498 10 0 0 10 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4930 18 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6d08 e e 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1bac 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 26b4 a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2880 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 37d8 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3c70 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3fe4 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4a10 1c 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 54d8 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 54f0 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5620 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5dac 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6040 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6278 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6478 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6540 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6d16 1a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6d0e 8 8 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 1bac 12 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 26b4 a 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 2880 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 37d8 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 3c70 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 3fe4 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4a10 1c 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 54d8 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 54f0 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5620 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5dac 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6040 11 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6278 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6478 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6540 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6d16 1a 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6d0f 7 7 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1bac 12 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 26b4 a 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 2880 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 37d8 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3c70 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3fe4 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4a10 1c 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 54d8 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 54f0 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5620 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5dac 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6040 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6278 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6478 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6540 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6d16 1a 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1bac 12 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 26b4 a 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 2880 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 37d8 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 3c70 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 3fe4 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4a10 1c 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 54d8 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 54f0 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5620 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5dac 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6040 f 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6278 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6478 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6540 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6d16 1a 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5e94 6 6 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2328 a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 252c 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b14 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 34c8 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d44 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c00 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f70 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 54d0 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5648 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5658 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5870 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a7c 5 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5e9a 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6578 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6668 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6cf0 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6d30 1f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6d90 1b 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2328 a 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 252c 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 2b14 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 34c8 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 3d44 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 4c00 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 4f70 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 54d0 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 5648 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 5658 12 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 5870 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 5a7c 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 5e9a 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 6578 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 6668 3 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 6cf0 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 6d30 1f 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 6d90 1b 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 2328 a 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 252c 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 2b14 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 34c8 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 3d44 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 4c00 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 4f70 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 54d0 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 5648 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 5658 12 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 5870 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 6578 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 6668 3 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 6cf0 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 6d31 1e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 6d90 1b 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ = 
STACK WIN 4 2328 a 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 252c 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 2b14 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 34c8 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 3d44 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4c00 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 4f70 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 54d0 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 5648 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 5658 12 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 5870 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6578 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6668 3 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6cf0 f 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6d32 1d 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 6d90 1b 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $20 $T0 8 - ^ =  $23 $T0 12 - ^ = 
STACK WIN 4 44f8 13 13 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1dbc 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 20ac 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 21c0 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 22c4 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b44 c 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3c5c f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ec0 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 40cc 17 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4294 e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 44c0 e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 450b d 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4cd4 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4d34 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4df8 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f84 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5630 e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 577c a 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5af0 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5d74 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5dec 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ed8 e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6000 e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 61a8 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6634 12 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6748 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6984 40 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 69fc e 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c9c 18 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 44fe d d 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 1dbc 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 20ac 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 21c0 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 22c4 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 2b44 a 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 3c5c f 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 3ec0 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 40cc 17 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4294 e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 44c0 e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 450b d 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4cd4 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4d34 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4df8 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 4f84 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5630 e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 577c a 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5af0 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5d74 12 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5dec 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 5ed8 e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6000 e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 61a8 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6634 12 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6748 13 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6984 40 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 69fc e 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 6c9c 18 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ = 
STACK WIN 4 44ff c c 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1dbc 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 20ac 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 21c0 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 22c4 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 2b44 9 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3c5c f 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3ec0 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 40cc 17 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4294 e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 44c0 e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 450b d 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4cd4 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4d34 7 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4df8 10 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 4f84 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5630 e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 577c a 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5af0 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5d74 12 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5dec 7 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5ed8 e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6000 e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 61a8 14 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6634 12 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6748 13 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6984 40 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 69fc e 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6c9c 18 0 0 4 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 1dbc 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 20ac 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 21c0 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 22c4 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 2b44 8 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 3c5c f 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 3ec0 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 40cc 17 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4294 e 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 44c0 e 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 450b d 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4cd4 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4d34 7 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4df8 10 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4f84 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5630 e 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 577c a 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5af0 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5d74 12 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5dec 7 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 5ed8 e 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6000 e 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 61a8 14 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6634 12 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6748 13 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6984 40 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 69fc e 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 6c9c 18 0 0 4 c 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 4 - ^ =  $23 $T0 8 - ^ =  $24 $T0 12 - ^ = 
STACK WIN 4 4f18 6 6 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1d58 16 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2338 17 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2d40 2 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2dc8 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2dd4 16 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f1e 11 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5794 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 57b4 8 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c28 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1d58 16 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 2338 17 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 2d40 2 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 2dc8 1 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 2dd4 16 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 4f1e 11 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 5794 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 57b4 8 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 6c28 12 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ = 
STACK WIN 4 1d58 16 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 2348 7 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 2dd4 16 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 5795 f 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 57b4 3 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 6c28 12 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $24 $T0 4 - ^ =  $23 $T0 8 - ^ = 
STACK WIN 4 3f0f 79 c 0 0 10 1c 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 32 - ^ =  $23 $T0 36 - ^ =  $24 $T0 40 - ^ = 
STACK WIN 4 3f7c 8 0 0 0 10 1c 0 1 $T0 $ebp = $T2 $esp = $T1 .raSearchStart = $eip $T1 ^ = $ebp $T0 = $esp $T1 4 + = 
STACK WIN 4 1ad0 8 0 0 4 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 4695 33 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 5a10 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 219c 2 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4240 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5a15 11 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6904 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4898 5 5 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 19e0 13 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 489d f 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6b2c 15 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3c6c 2 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 3c94 7 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 6dab 106 b 0 0 0 328 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5898 5 5 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1284 1c 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b18 14 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b5c 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2d48 6 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3488 14 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3e7c 3 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 40e4 f 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4c60 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5650 1 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 589d 11 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5f88 13 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5fc0 6 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 68a8 16 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6b60 10 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6c7c 8 0 0 c 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1284 1c 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1b18 14 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1b5c 10 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2d48 6 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 348d f 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3e7c 1 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4c60 10 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5650 1 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5f88 13 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5fc0 6 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6b60 10 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6c7c 8 0 0 c 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 62a4 5 5 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1048 16 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 11a0 e 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1744 2 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1b34 7 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 261c 22 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2ae0 8 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3d30 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3df8 3 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4230 f 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4284 10 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4cb0 1 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5960 1c 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 62a9 f 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6528 1 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6550 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 65dc b 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6654 12 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6734 14 0 0 8 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 11a0 e 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1744 2 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1b34 7 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 261c 22 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3d30 12 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3df8 1 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4230 f 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4284 10 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4cb0 1 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6528 1 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6550 12 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6655 11 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6734 14 0 0 8 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 11a0 e 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1744 2 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 1b35 6 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 261c 22 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 3d30 12 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4230 f 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 4cb0 1 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6528 1 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6550 12 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 6734 14 0 0 8 8 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ =  $24 $T0 8 - ^ = 
STACK WIN 4 65e8 1a 1a 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 1000 7 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 11b0 14 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 17c4 8 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 1ac4 6 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 21f4 c 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 2234 9 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 2354 f 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 242c 12 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 2710 3 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 2794 b 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 28d0 13 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 31f8 5 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 327c 14 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 32c0 4a 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 336c 20 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 36f8 f 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 37e0 10 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 3c30 1 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 3f94 e 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 4300 14 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 4338 11 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 497c 16 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 4b10 15 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 4c70 14 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 4ce8 10 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 4db4 1e 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 4e68 12 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 54e8 3 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 5574 14 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 55d0 2 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 55f8 8 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 5628 8 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 578c 3 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 5838 24 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 58f4 c 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 5f44 7 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 61fc f 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6328 14 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 63a0 c 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6404 6 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6602 e 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6624 10 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6ad4 16 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6d80 e 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6eb4 3 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 6f10 f 0 0 24 c 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 20 - ^ =  $23 $T0 24 - ^ =  $24 $T0 28 - ^ = 
STACK WIN 4 610c 8 8 0 24 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3204 2 0 0 24 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3c24 7 0 0 24 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6114 3f 0 0 24 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6bf0 16 16 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 1198 3 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 17b4 e 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 19ac 2 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 24a4 14 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 3318 33 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 357c 2b 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 43c4 b 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 4624 14 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 4d9c 11 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 5094 a 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 5b4c 12 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 5efc c 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 5f38 6 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 6420 c 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 68c0 14 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 6910 12 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 6c06 12 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 6c20 8 0 0 1c c 8 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $20 $T0 12 - ^ =  $23 $T0 16 - ^ =  $24 $T0 20 - ^ = 
STACK WIN 4 25dc 8 8 0 20 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 25e4 38 0 0 20 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4104 7 0 0 20 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ca0 2 0 0 20 0 10 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2e0c 6 6 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2e12 375 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 641c 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2e12 375 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 641c 1 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5cdc 6 6 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1308 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 14ec 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 24c0 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2d64 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3194 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 33c0 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3c9c 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 42ec 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 441c 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 448c 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5688 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5ce2 11 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1308 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 14ec 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 24c0 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2d64 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3194 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 33c0 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3c9c 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 42ec 1 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 441c 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 448c 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5688 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5ce2 11 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 58dc 6 6 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1028 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 16f4 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2040 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2724 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2788 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2834 3 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 28f0 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 2b7c 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3360 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 34d8 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 37cc 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3bac 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 3ddc 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4a80 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4aa0 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4b34 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4dec 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e2c 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4e5c 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4efc 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 4f64 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 567c 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 58e2 10 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 594c 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5b38 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 5cb4 7 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 6264 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 64f8 14 0 0 4 0 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + = 
STACK WIN 4 1028 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 16f4 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2040 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2724 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2788 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2834 1 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 28f0 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 2b7c 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3360 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 34d8 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 37cc 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3bac 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 3ddc 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4a80 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4aa0 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4b34 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4dec 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4e2c 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4e5c 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4efc 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 4f64 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 567c 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 58e2 10 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 594c 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5b38 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 5cb4 7 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 6264 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 64f8 14 0 0 4 4 0 0 1 $T0 $ebp = $eip $T0 4 + ^ = $ebp $T0 ^ = $esp $T0 8 + =  $23 $T0 4 - ^ = 
STACK WIN 4 1a0a ba 0 0 c 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 637c 10 0 0 0 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 4 1c38 103 0 0 c 0 0 0 1 $T0 .raSearch = $eip $T0 ^ = $esp $T0 4 + = 
STACK WIN 0 39e1 14 0 0 0 0 0 0 0 0
STACK WIN 0 672a 9 0 0 0 0 0 0 0 0
STACK WIN 0 27ab 88 0 0 8 0 0 0 0 0
STACK WIN 0 36d2 f 0 0 0 0 0 0 0 0
STACK WIN 0 1166 f 0 0 0 0 0 0 0 0
STACK WIN 0 1175 9 0 0 0 0 0 0 0 0
STACK WIN 0 1447 c 0 0 0 0 0 0 0 0
STACK WIN 0 1453 c 0 0 0 0 0 0 0 0
STACK WIN 0 40b0 9 0 0 0 0 0 0 0 0
STACK WIN 0 37f0 8b 0 0 4 0 0 0 0 0
STACK WIN 0 297c 90 3 0 c c 0 0 0 0
STACK WIN 0 2a0c 46 0 0 10 4 0 0 0 1
STACK WIN 0 2a6e 17 4 0 0 10 0 0 0 1
STACK WIN 0 2a85 19 0 0 0 0 0 0 0 0
STACK WIN 0 2ab7 17 1 0 8 4 0 0 0 1
STACK WIN 0 4658 4 0 0 0 0 0 0 0 0
STACK WIN 0 1939 15 0 0 0 0 0 0 0 0
STACK WIN 0 15b1 6 0 0 0 0 0 0 0 0
STACK WIN 0 7079 13 0 0 0 0 0 0 0 0
STACK WIN 0 2cdd c 0 0 0 0 0 0 0 0
STACK WIN 0 1f72 9 0 0 0 0 0 0 0 0
STACK WIN 0 3f7c 9 0 0 0 0 0 0 0 0
STACK WIN 0 4fa2 7a 0 0 c 0 0 0 0 0
STACK WIN 0 33f1 95 0 0 10 0 4 0 0 0
STACK WIN 0 67c0 84 3 0 8 c 0 0 0 0
STACK WIN 0 6844 23 0 0 0 0 0 0 0 0
STACK WIN 0 688f 3 0 0 0 0 0 0 0 0
STACK WIN 0 1d6e 1a 0 0 10 0 0 0 0 0
STACK WIN 0 1d88 1a 0 0 10 0 4 0 0 0
