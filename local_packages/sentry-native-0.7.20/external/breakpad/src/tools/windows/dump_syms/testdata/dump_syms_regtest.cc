// Copyright 2007 Google LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// with 32-bit cl
// cl /Zi dump_syms_regtest.cc /link /PROFILE
// dump_syms dump_syms_regtest.pdb > dump_syms_regtest.sym

// with 64-bit cl
// cp dump_syms_regtest.cc dump_syms_regtest64.cc
// cl /Zi dump_syms_regtest64.cc /link /PROFILE
// dump_syms dump_syms_regtest64.pdb > dump_syms_regtest64.sym

#ifdef HAVE_CONFIG_H
#include <config.h>
#endif


namespace google_breakpad {

class C {
 public:
  C() : member_(1) {}
  virtual ~C() {}

  void set_member(int value) { member_ = value; }
  int member() const { return member_; }

  void f() { member_ = g(); }
  virtual int g() { return 2; }
  static char* h(const C& that) { return 0; }

 private:
  int member_;
};

static int i() {
  return 3;
}

}  // namespace google_breakpad

int main(int argc, char** argv) {
  google_breakpad::C object;
  object.set_member(google_breakpad::i());
  object.f();
  int value = object.g();
  char* nothing = object.h(object);

  return 0;
}
