

Debugging Stab table -- 104 entries

   0:  .stabs "dump_syms_regtest.cc",N_UNDF,0x0,0x67,0x71c
   1:  .stabs "/export/home/<USER>/cvs/breakpad/google-breakpad20070927/src/tools/solaris/dump_syms/testdata/",N_SO,0x0,0x0,0x0
   2:  .stabs "dump_syms_regtest.cc",N_SO,0x0,0x4,0x0
   3:  .stabs "",N_OBJ,0x0,0x0,0x0
   4:  .stabs "",N_OBJ,0x0,0x0,0x0
   5:  .stabs "V=9.0;DBG_GEN=5.0.8;dm;cd;backend;ptf;ptx;ptk;s;g;R=5.8<<Sun C++ 5.8 Patch 121018-07 2006/11/01 (ccfe)>>;G=.XAB6Z2hOiL$Gl1b.;A=2",N_OPT,0x0,0x0,0x46fcb88e
   6:  .stabs "dump_syms_regtest.cc",N_SOL,0x0,0x0,0x0
   7:  .stabs "char:t(0,1)=bsc1;0;8",N_ISYM,0x0,0x0,0x0
   8:  .stabs "short:t(0,2)=bs2;0;16",N_ISYM,0x0,0x0,0x0
   9:  .stabs "int:t(0,3)=bs4;0;32",N_ISYM,0x0,0x0,0x0
  10:  .stabs "long:t(0,4)=bs4;0;32",N_ISYM,0x0,0x0,0x0
  11:  .stabs "long long:t(0,5)=bs8;0;64",N_ISYM,0x0,0x0,0x0
  12:  .stabs "unsigned char:t(0,6)=buc1;0;8",N_ISYM,0x0,0x0,0x0
  13:  .stabs "unsigned short:t(0,7)=bu2;0;16",N_ISYM,0x0,0x0,0x0
  14:  .stabs "unsigned:t(0,8)=bu4;0;32",N_ISYM,0x0,0x0,0x0
  15:  .stabs "unsigned long:t(0,9)=bu4;0;32",N_ISYM,0x0,0x0,0x0
  16:  .stabs "unsigned long long:t(0,10)=bu8;0;64",N_ISYM,0x0,0x0,0x0
  17:  .stabs "signed char:t(0,11)=bsc1;0;8",N_ISYM,0x0,0x0,0x0
  18:  .stabs "wchar_t:t(0,12)=buc4;0;32",N_ISYM,0x0,0x0,0x0
  19:  .stabs "void:t(0,13)=bs0;0;0",N_ISYM,0x0,0x0,0x0
  20:  .stabs "float:t(0,14)=R1;4",N_ISYM,0x0,0x0,0x0
  21:  .stabs "double:t(0,15)=R2;8",N_ISYM,0x0,0x0,0x0
  22:  .stabs "long double:t(0,16)=R6;12",N_ISYM,0x0,0x0,0x0
  23:  .stabs "...:t(0,17)=buv4;0;32",N_ISYM,0x0,0x0,0x0
  24:  .stabs "bool:t(0,18)=bub1;0;8",N_ISYM,0x0,0x0,0x0
  25:  .stabs "__1nPgoogle_breakpad_:T(0,19)=Yn0google_breakpad;",N_ISYM,0x0,0x0,0x0
  26:  .stabs "nBC(0,19):U(0,20)",N_ESYM,0x0,0x0,0x0
  27:  .stabs "nBC(0,19):T(0,20)=Yc8C;;AcHmember_:(0,3),32,32;;Cc2t6M_v K2c2T6M_v CcKset_member6Mi_v CcGmember6kM_i CcBf6M_v K3cBg6M_i GcBh6Frk1_pc;;;2 0;;;;110;",N_ESYM,0x0,0x8,0x0
  28:  .stabs "main:F(0,3);(0,3);(0,21)=*(0,22)=*(0,1)",N_FUN,0x0,0x38,0x0
  29:  .stabs "main",N_MAIN,0x0,0x0,0x0
  30:  .stabs "argc:p(0,3)",N_PSYM,0x0,0x4,0x8
  31:  .stabs "argv:p(0,21)",N_PSYM,0x0,0x4,0xc
  32:  .stabn N_LBRAC,0x0,0x1,0x12
  33:  .stabs "object:(0,20)",N_LSYM,0x0,0x8,0xfffffff4
  34:  .stabs "value:(0,3)",N_LSYM,0x0,0x4,0xfffffff0
  35:  .stabs "nothing:(0,22)",N_LSYM,0x0,0x4,0xffffffec
  36:  .stabn N_SLINE,0x0,0x39,0x12
  37:  .stabs "object:2",N_CONSTRUCT,0x0,0xc,0x12
  38:  .stabn N_SLINE,0x2,0x3a,0x1e
  39:  .stabn N_SLINE,0x0,0x3b,0x36
  40:  .stabn N_SLINE,0x0,0x3c,0x42
  41:  .stabn N_SLINE,0x0,0x3d,0x57
  42:  .stabn N_SLINE,0x0,0x3f,0x6c
  43:  .stabs "2:0",N_DESTRUCT,0x0,0xc,0x73
  44:  .stabn N_SLINE,0xfffffffe,0x40,0x9c
  45:  .stabn N_RBRAC,0x0,0x1,0x9c
  46:  .stabs "__1cPgoogle_breakpadBi6F_i_:f(0,3)",N_FUN,0x0,0x32,0x0
  47:  .stabn N_LBRAC,0x0,0x1,0x6
  48:  .stabn N_SLINE,0x0,0x33,0x6
  49:  .stabn N_SLINE,0x0,0x34,0x10
  50:  .stabn N_RBRAC,0x0,0x1,0x10
  51:  .stabs "__1cPgoogle_breakpadBC2t6M_v_:F(0,13);(0,23)=*(0,20)",N_FUN,0x0,0x24,0x0
  52:  .stabs "this:p(0,23)",N_PSYM,0x0,0x4,0x8
  53:  .stabn N_LBRAC,0x0,0x1,0x3
  54:  .stabn N_SLINE,0x0,0x24,0x25
  55:  .stabn N_RBRAC,0x0,0x1,0x25
  56:  .stabs "__1cPgoogle_breakpadBC2T6M_v_:F(0,13);(0,23)",N_FUN,0x0,0x25,0x0
  57:  .stabs "this:p(0,23)",N_PSYM,0x0,0x4,0x8
  58:  .stabn N_LBRAC,0x0,0x1,0x3
  59:  .stabn N_SLINE,0x0,0x25,0x3
  60:  .stabn N_RBRAC,0x0,0x1,0x3
  61:  .stabs "__1cPgoogle_breakpadBCKset_member6Mi_v_:F(0,13);(0,23);(0,3)",N_FUN,0x0,0x27,0x0
  62:  .stabs "this:p(0,23)",N_PSYM,0x0,0x4,0x8
  63:  .stabs "value:p(0,3)",N_PSYM,0x0,0x4,0xc
  64:  .stabn N_LBRAC,0x0,0x1,0x3
  65:  .stabn N_SLINE,0x0,0x27,0x3
  66:  .stabn N_SLINE,0x0,0x27,0xc
  67:  .stabn N_RBRAC,0x0,0x1,0xc
  68:  .stabs "__1cPgoogle_breakpadBCBf6M_v_:F(0,13);(0,23)",N_FUN,0x0,0x2a,0x0
  69:  .stabs "this:p(0,23)",N_PSYM,0x0,0x4,0x8
  70:  .stabn N_LBRAC,0x0,0x1,0x3
  71:  .stabn N_SLINE,0x0,0x2a,0x3
  72:  .stabn N_SLINE,0x0,0x2a,0x1d
  73:  .stabn N_RBRAC,0x0,0x1,0x1d
  74:  .stabs "__1cPgoogle_breakpadBCBg6M_i_:F(0,3);(0,23)",N_FUN,0x0,0x2b,0x0
  75:  .stabs "this:p(0,23)",N_PSYM,0x0,0x4,0x8
  76:  .stabn N_LBRAC,0x0,0x1,0x6
  77:  .stabn N_SLINE,0x0,0x2b,0x6
  78:  .stabn N_SLINE,0x0,0x2b,0x10
  79:  .stabn N_RBRAC,0x0,0x1,0x10
  80:  .stabs "__1cPgoogle_breakpadBCBh6Frk1_pc_:F(0,22);(0,24)=&(0,25)=k(0,20)",N_FUN,0x0,0x2c,0x0
  81:  .stabs "that:p(0,24)",N_PSYM,0x0,0x4,0x8
  82:  .stabn N_LBRAC,0x0,0x1,0x6
  83:  .stabn N_SLINE,0x0,0x2c,0x6
  84:  .stabn N_SLINE,0x0,0x2c,0x10
  85:  .stabn N_RBRAC,0x0,0x1,0x10
  86:  .stabs "__1cPgoogle_breakpadBC2T5B6M_v_:F(0,13);(0,23)",N_FUN,0x0,0x25,0x0
  87:  .stabs "this:p(0,23)",N_PSYM,0x0,0x4,0x8
  88:  .stabn N_LBRAC,0x0,0x1,0x3
  89:  .stabn N_SLINE,0x0,0x25,0xf
  90:  .stabn N_RBRAC,0x0,0x1,0xf
  91:  .stabs "__SLIP.DELETER__A:f(0,13);(0,23);(0,3)",N_FUN,0x0,0x25,0x0
  92:  .stabs "this:p(0,23)",N_PSYM,0x0,0x4,0x8
  93:  .stabs "delete:p(0,3)",N_PSYM,0x0,0x4,0xc
  94:  .stabn N_LBRAC,0x0,0x1,0x3
  95:  .stabn N_LBRAC,0x0,0x2,0x3
  96:  .stabn N_RBRAC,0x0,0x2,0x28
  97:  .stabn N_RBRAC,0x0,0x1,0x28
  98:  .stabs "true:l(0,18);1",N_LSYM,0x0,0x4,0x0
  99:  .stabs "false:l(0,18);0",N_LSYM,0x0,0x4,0x0
 100:  .stabs "__1c2k6Fpv_v_:P(0,13);(0,26)=*(0,13)",N_FUN,0x0,0x0,0x0
 101:  .stabs "__1cPgoogle_breakpadBC2t5B6M_v_:F__1cPgoogle_breakpadBC2t6M_v_",N_ALIAS,0x0,0x0,0x0
 102:  .stabs "cbD__RTTI__1nPgoogle_breakpadBC_(0,19):YR(0,20)",N_LSYM,0x0,0x0,0x0
 103:  .stabn N_ENDM,0x0,0x0,0x0


Index Stab table -- 17 entries

   0:  .stabs "dump_syms_regtest.cc",N_UNDF,0x0,0x10,0x3b1
   1:  .stabs "/export/home/<USER>/cvs/breakpad/google-breakpad20070927/src/tools/solaris/dump_syms/testdata/",N_SO,0x0,0x0,0x0
   2:  .stabs "dump_syms_regtest.cc",N_SO,0x0,0x4,0x0
   3:  .stabs "/export/home/<USER>/cvs/breakpad/google-breakpad20070927/src/tools/solaris/dump_syms/testdata",N_OBJ,0x0,0x0,0x0
   4:  .stabs "dump_syms_regtest.o",N_OBJ,0x0,0x0,0x0
   5:  .stabs "V=9.0;DBG_GEN=5.0.8;dm;cd;backend;ptf;ptx;ptk;s;g;R=5.8<<Sun C++ 5.8 Patch 121018-07 2006/11/01 (ccfe)>>;G=.XAB6Z2hOiL$Gl1b.;A=2",N_OPT,0x0,0x0,0x46fcb88e
   6:  .stabs "/export/home/<USER>/cvs/breakpad/google-breakpad20070927/src/tools/solaris/dump_syms/testdata/; /ws/on10-tools-prc/SUNWspro/SS11/prod/bin/CC -g -xs -xdebugformat=stabs -I../../.. -I../../../common/solaris -D_REENTRANT  -xs  dump_syms_regtest.cc -Qoption ccfe -prefix -Qoption ccfe .XAB6Z2hOiL\$Gl1b.",N_CMDLINE,0x0,0x0,0x0
   7:  .stabs "__1nPgoogle_breakpadBC_:U",N_ESYM,0x0,0x0,0x0
   8:  .stabs "main",N_MAIN,0x0,0x0,0x0
   9:  .stabs "main",N_FUN,0x0,0x0,0x0
  10:  .stabs "__1cPgoogle_breakpadBC2t6M_v_",N_FUN,0x0,0x0,0x0
  11:  .stabs "__1cPgoogle_breakpadBC2T6M_v_",N_FUN,0x0,0x0,0x0
  12:  .stabs "__1cPgoogle_breakpadBCKset_member6Mi_v_",N_FUN,0x0,0x0,0x0
  13:  .stabs "__1cPgoogle_breakpadBCBf6M_v_",N_FUN,0x0,0x0,0x0
  14:  .stabs "__1cPgoogle_breakpadBCBg6M_i_",N_FUN,0x0,0x0,0x0
  15:  .stabs "__1cPgoogle_breakpadBCBh6Frk1_pc_",N_FUN,0x0,0x0,0x0
  16:  .stabs "__1cPgoogle_breakpadBC2T5B6M_v_",N_FUN,0x0,0x0,0x0
