// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 45;
	objects = {

/* Begin PBXAggregateTarget section */
		B88FAFC9116BDCAD00407530 /* all_unittests */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = B88FAFCC116BDCCC00407530 /* Build configuration list for PBXAggregateTarget "all_unittests" */;
			buildPhases = (
				B88FB094116CE73E00407530 /* ShellScript */,
			);
			dependencies = (
				B88FB15B116CF53E00407530 /* PBXTargetDependency */,
				B88FAFCF116BDD7000407530 /* PBXTargetDependency */,
				B88FB01D116BDF9800407530 /* PBXTargetDependency */,
				B88FB167116CF54B00407530 /* PBXTargetDependency */,
				B88FAFD1116BDD7000407530 /* PBXTargetDependency */,
				B88FB165116CF54B00407530 /* PBXTargetDependency */,
				B88FB161116CF54B00407530 /* PBXTargetDependency */,
				B88FB15F116CF54B00407530 /* PBXTargetDependency */,
				B88FB15D116CF54B00407530 /* PBXTargetDependency */,
				B84A9201116CF7D2006C210E /* PBXTargetDependency */,
				B88FB0C8116CEB4A00407530 /* PBXTargetDependency */,
				8B31051511F100CF00FCF3E4 /* PBXTargetDependency */,
			);
			name = all_unittests;
			productName = all_unittests;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		162F64FA161C591500CD68D5 /* arch_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = 162F64F8161C591500CD68D5 /* arch_utilities.cc */; };
		162F6500161C5F2200CD68D5 /* arch_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = 162F64F8161C591500CD68D5 /* arch_utilities.cc */; };
		4247E63D2110D4B200482558 /* path_helper.cc in Sources */ = {isa = PBXBuildFile; fileRef = EB06C7511FEBC515000214D9 /* path_helper.cc */; };
		4262382721AC496F00E5A3A6 /* dwarf_range_list_handler.cc in Sources */ = {isa = PBXBuildFile; fileRef = 4262382521AC496F00E5A3A6 /* dwarf_range_list_handler.cc */; };
		4262382821AC49A000E5A3A6 /* dwarf_range_list_handler.h in Sources */ = {isa = PBXBuildFile; fileRef = 4262382621AC496F00E5A3A6 /* dwarf_range_list_handler.h */; };
		4D72CAF513DFBAC2006CABE3 /* md5.cc in Sources */ = {isa = PBXBuildFile; fileRef = 4D72CAF413DFBAC2006CABE3 /* md5.cc */; };
		8BCAAA4C1CE3A7980046090B /* elf_reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = 8BCAAA4A1CE3A7980046090B /* elf_reader.cc */; };
		8BCAAA4D1CE3B1260046090B /* elf_reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = 8BCAAA4A1CE3A7980046090B /* elf_reader.cc */; };
		B84A91F8116CF78F006C210E /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B84A91FB116CF7AF006C210E /* module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE241166603300407530 /* module.cc */; };
		B84A91FC116CF7AF006C210E /* stabs_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE3C11666C8900407530 /* stabs_to_module.cc */; };
		B84A91FD116CF7AF006C210E /* stabs_to_module_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0D8116CEC0600407530 /* stabs_to_module_unittest.cc */; };
		B88FAE1911665FE400407530 /* dwarf2diehandler.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE1711665FE400407530 /* dwarf2diehandler.cc */; };
		B88FAE261166603300407530 /* dwarf_cu_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE1E1166603300407530 /* dwarf_cu_to_module.cc */; };
		B88FAE271166603300407530 /* dwarf_line_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE201166603300407530 /* dwarf_line_to_module.cc */; };
		B88FAE281166603300407530 /* language.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE221166603300407530 /* language.cc */; };
		B88FAE291166603300407530 /* module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE241166603300407530 /* module.cc */; };
		B88FAE2C1166606200407530 /* macho_reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0E6E1166571D00DD08C9 /* macho_reader.cc */; };
		B88FAE351166673E00407530 /* dwarf_cfi_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE331166673E00407530 /* dwarf_cfi_to_module.cc */; };
		B88FAE3B11666C6F00407530 /* stabs_reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE3911666C6F00407530 /* stabs_reader.cc */; };
		B88FAE3E11666C8900407530 /* stabs_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE3C11666C8900407530 /* stabs_to_module.cc */; };
		B88FAF37116A595400407530 /* cfi_assembler.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAF34116A595400407530 /* cfi_assembler.cc */; };
		B88FAF38116A595400407530 /* dwarf2reader_cfi_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAF36116A595400407530 /* dwarf2reader_cfi_unittest.cc */; };
		B88FAF3F116A5A2E00407530 /* dwarf2reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = F95B422F0E0E22D100DBDE83 /* dwarf2reader.cc */; };
		B88FAF40116A5A2E00407530 /* bytereader.cc in Sources */ = {isa = PBXBuildFile; fileRef = F95B422C0E0E22D100DBDE83 /* bytereader.cc */; };
		B88FB00F116BDEA700407530 /* stabs_reader_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB003116BDE7200407530 /* stabs_reader_unittest.cc */; };
		B88FB010116BDEA700407530 /* stabs_reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE3911666C6F00407530 /* stabs_reader.cc */; };
		B88FB028116BE03100407530 /* test_assembler.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE0911665B5700407530 /* test_assembler.cc */; };
		B88FB029116BE03100407530 /* gmock-all.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0EA311665AEA00DD08C9 /* gmock-all.cc */; };
		B88FB02A116BE03100407530 /* gtest_main.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0E9F11665AC300DD08C9 /* gtest_main.cc */; };
		B88FB02B116BE03100407530 /* gtest-all.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0EA011665AC300DD08C9 /* gtest-all.cc */; };
		B88FB03F116BE24200407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB042116BE3C400407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB057116C0CDE00407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB0BD116CEAE000407530 /* module_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0B5116CEA8A00407530 /* module_unittest.cc */; };
		B88FB0C1116CEB0600407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB0C4116CEB4100407530 /* module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE241166603300407530 /* module.cc */; };
		B88FB0E3116CEEB000407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB0E5116CEED300407530 /* dwarf2diehandler.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE1711665FE400407530 /* dwarf2diehandler.cc */; };
		B88FB0E6116CEED300407530 /* dwarf2diehandler_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0DB116CEC5800407530 /* dwarf2diehandler_unittest.cc */; };
		B88FB0F6116CEF2000407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB0FA116CF00E00407530 /* dwarf_line_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE201166603300407530 /* dwarf_line_to_module.cc */; };
		B88FB0FB116CF00E00407530 /* dwarf_line_to_module_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0D7116CEC0600407530 /* dwarf_line_to_module_unittest.cc */; };
		B88FB0FE116CF02400407530 /* module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE241166603300407530 /* module.cc */; };
		B88FB10E116CF08100407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB112116CF1F000407530 /* dwarf_cu_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE1E1166603300407530 /* dwarf_cu_to_module.cc */; };
		B88FB113116CF1F000407530 /* dwarf_cu_to_module_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0D6116CEC0600407530 /* dwarf_cu_to_module_unittest.cc */; };
		B88FB114116CF1F000407530 /* language.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE221166603300407530 /* language.cc */; };
		B88FB115116CF1F000407530 /* module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE241166603300407530 /* module.cc */; };
		B88FB123116CF28500407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB129116CF2DD00407530 /* module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE241166603300407530 /* module.cc */; };
		B88FB12A116CF2DD00407530 /* dwarf_cfi_to_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE331166673E00407530 /* dwarf_cfi_to_module.cc */; };
		B88FB12B116CF2DD00407530 /* dwarf_cfi_to_module_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0D5116CEC0600407530 /* dwarf_cfi_to_module_unittest.cc */; };
		B88FB139116CF31600407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB13D116CF38300407530 /* cfi_assembler.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAF34116A595400407530 /* cfi_assembler.cc */; };
		B88FB13E116CF38300407530 /* bytereader.cc in Sources */ = {isa = PBXBuildFile; fileRef = F95B422C0E0E22D100DBDE83 /* bytereader.cc */; };
		B88FB13F116CF38300407530 /* bytereader_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0DA116CEC5800407530 /* bytereader_unittest.cc */; };
		B88FB14F116CF4AE00407530 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		B88FB152116CF4D300407530 /* byte_cursor_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0D4116CEC0600407530 /* byte_cursor_unittest.cc */; };
		B89E0E781166576C00DD08C9 /* macho_reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0E6E1166571D00DD08C9 /* macho_reader.cc */; };
		B89E0E7A1166576C00DD08C9 /* macho_dump.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0E701166573700DD08C9 /* macho_dump.cc */; };
		B89E0E9911665A7200DD08C9 /* macho_reader_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0E6D1166571D00DD08C9 /* macho_reader_unittest.cc */; };
		B89E0E9A11665A7200DD08C9 /* macho_reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = B89E0E6E1166571D00DD08C9 /* macho_reader.cc */; };
		B8C5B5171166534700D34F4E /* dwarf2reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = F95B422F0E0E22D100DBDE83 /* dwarf2reader.cc */; };
		B8C5B5181166534700D34F4E /* bytereader.cc in Sources */ = {isa = PBXBuildFile; fileRef = F95B422C0E0E22D100DBDE83 /* bytereader.cc */; };
		B8C5B5191166534700D34F4E /* macho_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = 557800890BE1F3AB00EC23E0 /* macho_utilities.cc */; };
		B8C5B51A1166534700D34F4E /* file_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BE650410B52F6D800611104 /* file_id.cc */; };
		B8C5B51B1166534700D34F4E /* macho_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BE650430B52F6D800611104 /* macho_id.cc */; };
		B8C5B51C1166534700D34F4E /* macho_walker.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BE650450B52F6D800611104 /* macho_walker.cc */; };
		B8C5B51D1166534700D34F4E /* dump_syms.cc in Sources */ = {isa = PBXBuildFile; fileRef = 08FB7796FE84155DC02AAC07 /* dump_syms.cc */; };
		B8C5B51E1166534700D34F4E /* dump_syms_tool.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BDF186E0B1BB43700F8391B /* dump_syms_tool.cc */; };
		B8C5B523116653BA00D34F4E /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 08FB779EFE84155DC02AAC07 /* Foundation.framework */; };
		D21F97D711CBA12300239E38 /* test_assembler_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FB0D9116CEC0600407530 /* test_assembler_unittest.cc */; };
		D21F97D811CBA13D00239E38 /* libgtestmockall.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B88FB024116BDFFF00407530 /* libgtestmockall.a */; };
		D21F97E911CBA1FF00239E38 /* test_assembler.cc in Sources */ = {isa = PBXBuildFile; fileRef = B88FAE0911665B5700407530 /* test_assembler.cc */; };
		EB06C7531FEBC516000214D9 /* path_helper.cc in Sources */ = {isa = PBXBuildFile; fileRef = EB06C7511FEBC515000214D9 /* path_helper.cc */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		8B31051411F100CF00FCF3E4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D21F97D111CBA0F200239E38;
			remoteInfo = test_assembler_unittest;
		};
		B84A91F9116CF796006C210E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B84A9200116CF7D2006C210E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B84A91F3116CF784006C210E;
			remoteInfo = stabs_to_module_unittest;
		};
		B88FAFCE116BDD7000407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B89E0E9411665A6400DD08C9;
			remoteInfo = macho_reader_unittest;
		};
		B88FAFD0116BDD7000407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FAF2E116A591D00407530;
			remoteInfo = dwarf2reader_cfi_unittest;
		};
		B88FB01C116BDF9800407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB006116BDE8300407530;
			remoteInfo = stabs_reader_unittest;
		};
		B88FB039116BE17E00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB087116CE6D800407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB08F116CE71000407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB0BF116CEAFE00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB0C7116CEB4A00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB0B8116CEABF00407530;
			remoteInfo = module_unittest;
		};
		B88FB0E7116CEEDA00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB0F7116CEF2E00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB10F116CF08A00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB124116CF29E00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB13B116CF35C00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB150116CF4C100407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB023116BDFFF00407530;
			remoteInfo = gtestmockall;
		};
		B88FB15A116CF53E00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB14A116CF4A700407530;
			remoteInfo = byte_cursor_unittest;
		};
		B88FB15C116CF54B00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB11E116CF27F00407530;
			remoteInfo = dwarf_cfi_to_module_unittest;
		};
		B88FB15E116CF54B00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB0F1116CEF1900407530;
			remoteInfo = dwarf_line_to_module_unittest;
		};
		B88FB160116CF54B00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB109116CF07900407530;
			remoteInfo = dwarf_cu_to_module_unittest;
		};
		B88FB164116CF54B00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB0DE116CEEA800407530;
			remoteInfo = dwarf2diehandler_unittest;
		};
		B88FB166116CF54B00407530 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B88FB134116CF30F00407530;
			remoteInfo = bytereader_unittest;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		08FB7796FE84155DC02AAC07 /* dump_syms.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = dump_syms.cc; path = ../../../common/mac/dump_syms.cc; sourceTree = "<group>"; };
		08FB779EFE84155DC02AAC07 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		162F64F8161C591500CD68D5 /* arch_utilities.cc */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = arch_utilities.cc; path = ../../../common/mac/arch_utilities.cc; sourceTree = "<group>"; };
		162F64F9161C591500CD68D5 /* arch_utilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = arch_utilities.h; path = ../../../common/mac/arch_utilities.h; sourceTree = "<group>"; };
		4262382521AC496F00E5A3A6 /* dwarf_range_list_handler.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf_range_list_handler.cc; path = ../../../common/dwarf_range_list_handler.cc; sourceTree = "<group>"; };
		4262382621AC496F00E5A3A6 /* dwarf_range_list_handler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dwarf_range_list_handler.h; path = ../../../common/dwarf_range_list_handler.h; sourceTree = "<group>"; };
		4D72CAF413DFBAC2006CABE3 /* md5.cc */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = md5.cc; path = ../../../common/md5.cc; sourceTree = SOURCE_ROOT; };
		557800890BE1F3AB00EC23E0 /* macho_utilities.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = macho_utilities.cc; path = ../../../common/mac/macho_utilities.cc; sourceTree = SOURCE_ROOT; };
		5578008A0BE1F3AB00EC23E0 /* macho_utilities.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = macho_utilities.h; path = ../../../common/mac/macho_utilities.h; sourceTree = SOURCE_ROOT; };
		8B31023E11F0CF1C00FCF3E4 /* Breakpad.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Breakpad.xcconfig; path = ../../../common/mac/Breakpad.xcconfig; sourceTree = SOURCE_ROOT; };
		8B3102D411F0D60300FCF3E4 /* BreakpadDebug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = BreakpadDebug.xcconfig; path = ../../../common/mac/BreakpadDebug.xcconfig; sourceTree = SOURCE_ROOT; };
		8B3102D511F0D60300FCF3E4 /* BreakpadRelease.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = BreakpadRelease.xcconfig; path = ../../../common/mac/BreakpadRelease.xcconfig; sourceTree = SOURCE_ROOT; };
		8BCAAA4A1CE3A7980046090B /* elf_reader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = elf_reader.cc; path = ../../../common/dwarf/elf_reader.cc; sourceTree = "<group>"; };
		8BCAAA4B1CE3A7980046090B /* elf_reader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = elf_reader.h; path = ../../../common/dwarf/elf_reader.h; sourceTree = "<group>"; };
		9BDF186D0B1BB43700F8391B /* dump_syms.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dump_syms.h; path = ../../../common/mac/dump_syms.h; sourceTree = "<group>"; };
		9BDF186E0B1BB43700F8391B /* dump_syms_tool.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = dump_syms_tool.cc; sourceTree = "<group>"; };
		9BE650410B52F6D800611104 /* file_id.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = file_id.cc; path = ../../../common/mac/file_id.cc; sourceTree = SOURCE_ROOT; };
		9BE650420B52F6D800611104 /* file_id.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = file_id.h; path = ../../../common/mac/file_id.h; sourceTree = SOURCE_ROOT; };
		9BE650430B52F6D800611104 /* macho_id.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = macho_id.cc; path = ../../../common/mac/macho_id.cc; sourceTree = SOURCE_ROOT; };
		9BE650440B52F6D800611104 /* macho_id.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = macho_id.h; path = ../../../common/mac/macho_id.h; sourceTree = SOURCE_ROOT; };
		9BE650450B52F6D800611104 /* macho_walker.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = macho_walker.cc; path = ../../../common/mac/macho_walker.cc; sourceTree = SOURCE_ROOT; };
		9BE650460B52F6D800611104 /* macho_walker.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = macho_walker.h; path = ../../../common/mac/macho_walker.h; sourceTree = SOURCE_ROOT; };
		B84A91F4116CF784006C210E /* stabs_to_module_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = stabs_to_module_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FAE0911665B5700407530 /* test_assembler.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = test_assembler.cc; path = ../../../common/test_assembler.cc; sourceTree = SOURCE_ROOT; };
		B88FAE0A11665B5700407530 /* test_assembler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = test_assembler.h; path = ../../../common/test_assembler.h; sourceTree = SOURCE_ROOT; };
		B88FAE1711665FE400407530 /* dwarf2diehandler.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf2diehandler.cc; path = ../../../common/dwarf/dwarf2diehandler.cc; sourceTree = SOURCE_ROOT; };
		B88FAE1811665FE400407530 /* dwarf2diehandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dwarf2diehandler.h; path = ../../../common/dwarf/dwarf2diehandler.h; sourceTree = SOURCE_ROOT; };
		B88FAE1D1166603300407530 /* byte_cursor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = byte_cursor.h; path = ../../../common/byte_cursor.h; sourceTree = SOURCE_ROOT; };
		B88FAE1E1166603300407530 /* dwarf_cu_to_module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf_cu_to_module.cc; path = ../../../common/dwarf_cu_to_module.cc; sourceTree = SOURCE_ROOT; };
		B88FAE1F1166603300407530 /* dwarf_cu_to_module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dwarf_cu_to_module.h; path = ../../../common/dwarf_cu_to_module.h; sourceTree = SOURCE_ROOT; };
		B88FAE201166603300407530 /* dwarf_line_to_module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf_line_to_module.cc; path = ../../../common/dwarf_line_to_module.cc; sourceTree = SOURCE_ROOT; };
		B88FAE211166603300407530 /* dwarf_line_to_module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dwarf_line_to_module.h; path = ../../../common/dwarf_line_to_module.h; sourceTree = SOURCE_ROOT; };
		B88FAE221166603300407530 /* language.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = language.cc; path = ../../../common/language.cc; sourceTree = SOURCE_ROOT; };
		B88FAE231166603300407530 /* language.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = language.h; path = ../../../common/language.h; sourceTree = SOURCE_ROOT; };
		B88FAE241166603300407530 /* module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = module.cc; path = ../../../common/module.cc; sourceTree = SOURCE_ROOT; };
		B88FAE251166603300407530 /* module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = module.h; path = ../../../common/module.h; sourceTree = SOURCE_ROOT; };
		B88FAE331166673E00407530 /* dwarf_cfi_to_module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf_cfi_to_module.cc; path = ../../../common/dwarf_cfi_to_module.cc; sourceTree = SOURCE_ROOT; };
		B88FAE341166673E00407530 /* dwarf_cfi_to_module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dwarf_cfi_to_module.h; path = ../../../common/dwarf_cfi_to_module.h; sourceTree = SOURCE_ROOT; };
		B88FAE3911666C6F00407530 /* stabs_reader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = stabs_reader.cc; path = ../../../common/stabs_reader.cc; sourceTree = SOURCE_ROOT; };
		B88FAE3A11666C6F00407530 /* stabs_reader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = stabs_reader.h; path = ../../../common/stabs_reader.h; sourceTree = SOURCE_ROOT; };
		B88FAE3C11666C8900407530 /* stabs_to_module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = stabs_to_module.cc; path = ../../../common/stabs_to_module.cc; sourceTree = SOURCE_ROOT; };
		B88FAE3D11666C8900407530 /* stabs_to_module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = stabs_to_module.h; path = ../../../common/stabs_to_module.h; sourceTree = SOURCE_ROOT; };
		B88FAF2F116A591E00407530 /* dwarf2reader_cfi_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dwarf2reader_cfi_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FAF34116A595400407530 /* cfi_assembler.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = cfi_assembler.cc; path = ../../../common/dwarf/cfi_assembler.cc; sourceTree = SOURCE_ROOT; };
		B88FAF35116A595400407530 /* cfi_assembler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = cfi_assembler.h; path = ../../../common/dwarf/cfi_assembler.h; sourceTree = SOURCE_ROOT; };
		B88FAF36116A595400407530 /* dwarf2reader_cfi_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf2reader_cfi_unittest.cc; path = ../../../common/dwarf/dwarf2reader_cfi_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB003116BDE7200407530 /* stabs_reader_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = stabs_reader_unittest.cc; path = ../../../common/stabs_reader_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB007116BDE8300407530 /* stabs_reader_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = stabs_reader_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB024116BDFFF00407530 /* libgtestmockall.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libgtestmockall.a; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB0B5116CEA8A00407530 /* module_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = module_unittest.cc; path = ../../../common/module_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0B9116CEABF00407530 /* module_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = module_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB0D4116CEC0600407530 /* byte_cursor_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = byte_cursor_unittest.cc; path = ../../../common/byte_cursor_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0D5116CEC0600407530 /* dwarf_cfi_to_module_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf_cfi_to_module_unittest.cc; path = ../../../common/dwarf_cfi_to_module_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0D6116CEC0600407530 /* dwarf_cu_to_module_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf_cu_to_module_unittest.cc; path = ../../../common/dwarf_cu_to_module_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0D7116CEC0600407530 /* dwarf_line_to_module_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf_line_to_module_unittest.cc; path = ../../../common/dwarf_line_to_module_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0D8116CEC0600407530 /* stabs_to_module_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = stabs_to_module_unittest.cc; path = ../../../common/stabs_to_module_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0D9116CEC0600407530 /* test_assembler_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = test_assembler_unittest.cc; path = ../../../common/test_assembler_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0DA116CEC5800407530 /* bytereader_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = bytereader_unittest.cc; path = ../../../common/dwarf/bytereader_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0DB116CEC5800407530 /* dwarf2diehandler_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf2diehandler_unittest.cc; path = ../../../common/dwarf/dwarf2diehandler_unittest.cc; sourceTree = SOURCE_ROOT; };
		B88FB0DF116CEEA800407530 /* dwarf2diehandler_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dwarf2diehandler_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB0F2116CEF1900407530 /* dwarf_line_to_module_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dwarf_line_to_module_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB10A116CF07900407530 /* dwarf_cu_to_module_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dwarf_cu_to_module_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB11F116CF27F00407530 /* dwarf_cfi_to_module_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dwarf_cfi_to_module_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB135116CF30F00407530 /* bytereader_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = bytereader_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B88FB14B116CF4A700407530 /* byte_cursor_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = byte_cursor_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B89E0E6D1166571D00DD08C9 /* macho_reader_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = macho_reader_unittest.cc; path = ../../../common/mac/macho_reader_unittest.cc; sourceTree = SOURCE_ROOT; };
		B89E0E6E1166571D00DD08C9 /* macho_reader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = macho_reader.cc; path = ../../../common/mac/macho_reader.cc; sourceTree = SOURCE_ROOT; };
		B89E0E6F1166571D00DD08C9 /* macho_reader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = macho_reader.h; path = ../../../common/mac/macho_reader.h; sourceTree = SOURCE_ROOT; };
		B89E0E701166573700DD08C9 /* macho_dump.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = macho_dump.cc; sourceTree = "<group>"; };
		B89E0E741166575200DD08C9 /* macho_dump */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = macho_dump; sourceTree = BUILT_PRODUCTS_DIR; };
		B89E0E9511665A6400DD08C9 /* macho_reader_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = macho_reader_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		B89E0E9F11665AC300DD08C9 /* gtest_main.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = gtest_main.cc; path = ../../../testing/googletest/src/gtest_main.cc; sourceTree = SOURCE_ROOT; };
		B89E0EA011665AC300DD08C9 /* gtest-all.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = "gtest-all.cc"; path = "../../../testing/googletest/src/gtest-all.cc"; sourceTree = SOURCE_ROOT; };
		B89E0EA311665AEA00DD08C9 /* gmock-all.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = "gmock-all.cc"; path = "../../../testing/googlemock/src/gmock-all.cc"; sourceTree = SOURCE_ROOT; };
		B8C5B5111166531A00D34F4E /* dump_syms */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dump_syms; sourceTree = BUILT_PRODUCTS_DIR; };
		B8E8CA0C1156C854009E61B2 /* byteswap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = byteswap.h; path = ../../../common/mac/byteswap.h; sourceTree = SOURCE_ROOT; };
		D21F97D211CBA0F200239E38 /* test_assembler_unittest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = test_assembler_unittest; sourceTree = BUILT_PRODUCTS_DIR; };
		EB06C7511FEBC515000214D9 /* path_helper.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = path_helper.cc; path = ../../../common/path_helper.cc; sourceTree = "<group>"; };
		EB06C7521FEBC516000214D9 /* path_helper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = path_helper.h; path = ../../../common/path_helper.h; sourceTree = "<group>"; };
		F95B422B0E0E22D100DBDE83 /* bytereader-inl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "bytereader-inl.h"; path = "../../../common/dwarf/bytereader-inl.h"; sourceTree = SOURCE_ROOT; };
		F95B422C0E0E22D100DBDE83 /* bytereader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = bytereader.cc; path = ../../../common/dwarf/bytereader.cc; sourceTree = SOURCE_ROOT; };
		F95B422D0E0E22D100DBDE83 /* bytereader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = bytereader.h; path = ../../../common/dwarf/bytereader.h; sourceTree = SOURCE_ROOT; };
		F95B422E0E0E22D100DBDE83 /* dwarf2enums.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dwarf2enums.h; path = ../../../common/dwarf/dwarf2enums.h; sourceTree = SOURCE_ROOT; };
		F95B422F0E0E22D100DBDE83 /* dwarf2reader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf2reader.cc; path = ../../../common/dwarf/dwarf2reader.cc; sourceTree = SOURCE_ROOT; };
		F95B42300E0E22D100DBDE83 /* dwarf2reader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dwarf2reader.h; path = ../../../common/dwarf/dwarf2reader.h; sourceTree = SOURCE_ROOT; };
		F95B42310E0E22D100DBDE83 /* line_state_machine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = line_state_machine.h; path = ../../../common/dwarf/line_state_machine.h; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B84A91F2116CF784006C210E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B84A91F8116CF78F006C210E /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FAF2D116A591D00407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB042116BE3C400407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB005116BDE8300407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB03F116BE24200407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB022116BDFFF00407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB0B7116CEABF00407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB0C1116CEB0600407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB0DD116CEEA800407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB0E3116CEEB000407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB0F0116CEF1900407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB0F6116CEF2000407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB108116CF07900407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB10E116CF08100407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB11D116CF27F00407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB123116CF28500407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB133116CF30F00407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB139116CF31600407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB149116CF4A700407530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB14F116CF4AE00407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89E0E721166575200DD08C9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89E0E9311665A6400DD08C9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB057116C0CDE00407530 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B8C5B50F1166531A00D34F4E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B8C5B523116653BA00D34F4E /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D21F97D011CBA0F200239E38 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D21F97D811CBA13D00239E38 /* libgtestmockall.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08FB7794FE84155DC02AAC07 /* dump_syms */ = {
			isa = PBXGroup;
			children = (
				8B31023E11F0CF1C00FCF3E4 /* Breakpad.xcconfig */,
				8B3102D411F0D60300FCF3E4 /* BreakpadDebug.xcconfig */,
				8B3102D511F0D60300FCF3E4 /* BreakpadRelease.xcconfig */,
				B89E0E9D11665A9500DD08C9 /* TESTING */,
				F9F5344B0E7C8FFC0012363F /* DWARF */,
				B89E0E6C1166569700DD08C9 /* MACHO */,
				B88FAE3811666A1700407530 /* STABS */,
				B88FAE1C11665FFD00407530 /* MODULE */,
				162F64F8161C591500CD68D5 /* arch_utilities.cc */,
				162F64F9161C591500CD68D5 /* arch_utilities.h */,
				B88FAE1D1166603300407530 /* byte_cursor.h */,
				B88FB0D4116CEC0600407530 /* byte_cursor_unittest.cc */,
				B8E8CA0C1156C854009E61B2 /* byteswap.h */,
				9BE650410B52F6D800611104 /* file_id.cc */,
				9BE650420B52F6D800611104 /* file_id.h */,
				9BDF186D0B1BB43700F8391B /* dump_syms.h */,
				08FB7796FE84155DC02AAC07 /* dump_syms.cc */,
				9BDF186E0B1BB43700F8391B /* dump_syms_tool.cc */,
				B89E0E701166573700DD08C9 /* macho_dump.cc */,
				4D72CAF413DFBAC2006CABE3 /* md5.cc */,
				EB06C7511FEBC515000214D9 /* path_helper.cc */,
				EB06C7521FEBC516000214D9 /* path_helper.h */,
				08FB779DFE84155DC02AAC07 /* External Frameworks and Libraries */,
				1AB674ADFE9D54B511CA2CBB /* Products */,
			);
			name = dump_syms;
			sourceTree = "<group>";
		};
		08FB779DFE84155DC02AAC07 /* External Frameworks and Libraries */ = {
			isa = PBXGroup;
			children = (
				08FB779EFE84155DC02AAC07 /* Foundation.framework */,
			);
			name = "External Frameworks and Libraries";
			sourceTree = "<group>";
		};
		1AB674ADFE9D54B511CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				B8C5B5111166531A00D34F4E /* dump_syms */,
				B89E0E741166575200DD08C9 /* macho_dump */,
				B89E0E9511665A6400DD08C9 /* macho_reader_unittest */,
				B88FAF2F116A591E00407530 /* dwarf2reader_cfi_unittest */,
				B88FB007116BDE8300407530 /* stabs_reader_unittest */,
				B88FB024116BDFFF00407530 /* libgtestmockall.a */,
				B88FB0B9116CEABF00407530 /* module_unittest */,
				B88FB0DF116CEEA800407530 /* dwarf2diehandler_unittest */,
				B88FB0F2116CEF1900407530 /* dwarf_line_to_module_unittest */,
				B88FB10A116CF07900407530 /* dwarf_cu_to_module_unittest */,
				B88FB11F116CF27F00407530 /* dwarf_cfi_to_module_unittest */,
				B88FB135116CF30F00407530 /* bytereader_unittest */,
				B88FB14B116CF4A700407530 /* byte_cursor_unittest */,
				B84A91F4116CF784006C210E /* stabs_to_module_unittest */,
				D21F97D211CBA0F200239E38 /* test_assembler_unittest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B88FAE1C11665FFD00407530 /* MODULE */ = {
			isa = PBXGroup;
			children = (
				B88FAE1E1166603300407530 /* dwarf_cu_to_module.cc */,
				B88FAE1F1166603300407530 /* dwarf_cu_to_module.h */,
				B88FB0D6116CEC0600407530 /* dwarf_cu_to_module_unittest.cc */,
				B88FAE201166603300407530 /* dwarf_line_to_module.cc */,
				B88FAE211166603300407530 /* dwarf_line_to_module.h */,
				B88FB0D7116CEC0600407530 /* dwarf_line_to_module_unittest.cc */,
				B88FAE221166603300407530 /* language.cc */,
				B88FAE231166603300407530 /* language.h */,
				B88FAE241166603300407530 /* module.cc */,
				B88FAE251166603300407530 /* module.h */,
				B88FB0B5116CEA8A00407530 /* module_unittest.cc */,
				B88FAE331166673E00407530 /* dwarf_cfi_to_module.cc */,
				B88FAE341166673E00407530 /* dwarf_cfi_to_module.h */,
				B88FB0D5116CEC0600407530 /* dwarf_cfi_to_module_unittest.cc */,
				B88FAE3C11666C8900407530 /* stabs_to_module.cc */,
				B88FAE3D11666C8900407530 /* stabs_to_module.h */,
				B88FB0D8116CEC0600407530 /* stabs_to_module_unittest.cc */,
			);
			name = MODULE;
			sourceTree = "<group>";
		};
		B88FAE3811666A1700407530 /* STABS */ = {
			isa = PBXGroup;
			children = (
				B88FB003116BDE7200407530 /* stabs_reader_unittest.cc */,
				B88FAE3911666C6F00407530 /* stabs_reader.cc */,
				B88FAE3A11666C6F00407530 /* stabs_reader.h */,
			);
			name = STABS;
			sourceTree = "<group>";
		};
		B89E0E6C1166569700DD08C9 /* MACHO */ = {
			isa = PBXGroup;
			children = (
				B89E0E6D1166571D00DD08C9 /* macho_reader_unittest.cc */,
				B89E0E6E1166571D00DD08C9 /* macho_reader.cc */,
				B89E0E6F1166571D00DD08C9 /* macho_reader.h */,
				557800890BE1F3AB00EC23E0 /* macho_utilities.cc */,
				5578008A0BE1F3AB00EC23E0 /* macho_utilities.h */,
				9BE650430B52F6D800611104 /* macho_id.cc */,
				9BE650440B52F6D800611104 /* macho_id.h */,
				9BE650450B52F6D800611104 /* macho_walker.cc */,
				9BE650460B52F6D800611104 /* macho_walker.h */,
			);
			name = MACHO;
			sourceTree = "<group>";
		};
		B89E0E9D11665A9500DD08C9 /* TESTING */ = {
			isa = PBXGroup;
			children = (
				B88FAE0911665B5700407530 /* test_assembler.cc */,
				B88FAE0A11665B5700407530 /* test_assembler.h */,
				B88FB0D9116CEC0600407530 /* test_assembler_unittest.cc */,
				B89E0EA311665AEA00DD08C9 /* gmock-all.cc */,
				B89E0E9F11665AC300DD08C9 /* gtest_main.cc */,
				B89E0EA011665AC300DD08C9 /* gtest-all.cc */,
			);
			name = TESTING;
			sourceTree = "<group>";
		};
		F9F5344B0E7C8FFC0012363F /* DWARF */ = {
			isa = PBXGroup;
			children = (
				B88FAF34116A595400407530 /* cfi_assembler.cc */,
				B88FAF35116A595400407530 /* cfi_assembler.h */,
				F95B422E0E0E22D100DBDE83 /* dwarf2enums.h */,
				F95B422F0E0E22D100DBDE83 /* dwarf2reader.cc */,
				4262382521AC496F00E5A3A6 /* dwarf_range_list_handler.cc */,
				4262382621AC496F00E5A3A6 /* dwarf_range_list_handler.h */,
				F95B42300E0E22D100DBDE83 /* dwarf2reader.h */,
				B88FAF36116A595400407530 /* dwarf2reader_cfi_unittest.cc */,
				F95B422D0E0E22D100DBDE83 /* bytereader.h */,
				F95B422B0E0E22D100DBDE83 /* bytereader-inl.h */,
				F95B422C0E0E22D100DBDE83 /* bytereader.cc */,
				B88FB0DA116CEC5800407530 /* bytereader_unittest.cc */,
				F95B42310E0E22D100DBDE83 /* line_state_machine.h */,
				B88FAE1711665FE400407530 /* dwarf2diehandler.cc */,
				B88FAE1811665FE400407530 /* dwarf2diehandler.h */,
				B88FB0DB116CEC5800407530 /* dwarf2diehandler_unittest.cc */,
				8BCAAA4A1CE3A7980046090B /* elf_reader.cc */,
				8BCAAA4B1CE3A7980046090B /* elf_reader.h */,
			);
			name = DWARF;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		B88FB020116BDFFF00407530 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		B84A91F3116CF784006C210E /* stabs_to_module_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B84A9202116CF7F0006C210E /* Build configuration list for PBXNativeTarget "stabs_to_module_unittest" */;
			buildPhases = (
				B84A91F1116CF784006C210E /* Sources */,
				B84A91F2116CF784006C210E /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B84A91FA116CF796006C210E /* PBXTargetDependency */,
			);
			name = stabs_to_module_unittest;
			productName = stabs_to_module_unittest;
			productReference = B84A91F4116CF784006C210E /* stabs_to_module_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FAF2E116A591D00407530 /* dwarf2reader_cfi_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FAF33116A594800407530 /* Build configuration list for PBXNativeTarget "dwarf2reader_cfi_unittest" */;
			buildPhases = (
				B88FAF2C116A591D00407530 /* Sources */,
				B88FAF2D116A591D00407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB03A116BE17E00407530 /* PBXTargetDependency */,
			);
			name = dwarf2reader_cfi_unittest;
			productName = dwarf2reader_cfi_unittest;
			productReference = B88FAF2F116A591E00407530 /* dwarf2reader_cfi_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB006116BDE8300407530 /* stabs_reader_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB013116BDEC800407530 /* Build configuration list for PBXNativeTarget "stabs_reader_unittest" */;
			buildPhases = (
				B88FB004116BDE8300407530 /* Sources */,
				B88FB005116BDE8300407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB088116CE6D800407530 /* PBXTargetDependency */,
			);
			name = stabs_reader_unittest;
			productName = stabs_reader_unittest;
			productReference = B88FB007116BDE8300407530 /* stabs_reader_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB023116BDFFF00407530 /* gtestmockall */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB027116BE02900407530 /* Build configuration list for PBXNativeTarget "gtestmockall" */;
			buildPhases = (
				B88FB020116BDFFF00407530 /* Headers */,
				B88FB021116BDFFF00407530 /* Sources */,
				B88FB022116BDFFF00407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = gtestmockall;
			productName = gtestmockall;
			productReference = B88FB024116BDFFF00407530 /* libgtestmockall.a */;
			productType = "com.apple.product-type.library.static";
		};
		B88FB0B8116CEABF00407530 /* module_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB0BE116CEAFE00407530 /* Build configuration list for PBXNativeTarget "module_unittest" */;
			buildPhases = (
				B88FB0B6116CEABF00407530 /* Sources */,
				B88FB0B7116CEABF00407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB0C0116CEAFE00407530 /* PBXTargetDependency */,
			);
			name = module_unittest;
			productName = module_unittest;
			productReference = B88FB0B9116CEABF00407530 /* module_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB0DE116CEEA800407530 /* dwarf2diehandler_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB0E4116CEECE00407530 /* Build configuration list for PBXNativeTarget "dwarf2diehandler_unittest" */;
			buildPhases = (
				B88FB0DC116CEEA800407530 /* Sources */,
				B88FB0DD116CEEA800407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB0E8116CEEDA00407530 /* PBXTargetDependency */,
			);
			name = dwarf2diehandler_unittest;
			productName = dwarf2diehandler_unittest;
			productReference = B88FB0DF116CEEA800407530 /* dwarf2diehandler_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB0F1116CEF1900407530 /* dwarf_line_to_module_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB0F9116CEF9800407530 /* Build configuration list for PBXNativeTarget "dwarf_line_to_module_unittest" */;
			buildPhases = (
				B88FB0EF116CEF1900407530 /* Sources */,
				B88FB0F0116CEF1900407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB0F8116CEF2E00407530 /* PBXTargetDependency */,
			);
			name = dwarf_line_to_module_unittest;
			productName = dwarf_line_to_module_unittest;
			productReference = B88FB0F2116CEF1900407530 /* dwarf_line_to_module_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB109116CF07900407530 /* dwarf_cu_to_module_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB111116CF0A800407530 /* Build configuration list for PBXNativeTarget "dwarf_cu_to_module_unittest" */;
			buildPhases = (
				B88FB107116CF07900407530 /* Sources */,
				B88FB108116CF07900407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB110116CF08A00407530 /* PBXTargetDependency */,
			);
			name = dwarf_cu_to_module_unittest;
			productName = dwarf_cu_to_module_unittest;
			productReference = B88FB10A116CF07900407530 /* dwarf_cu_to_module_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB11E116CF27F00407530 /* dwarf_cfi_to_module_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB128116CF2C800407530 /* Build configuration list for PBXNativeTarget "dwarf_cfi_to_module_unittest" */;
			buildPhases = (
				B88FB11C116CF27F00407530 /* Sources */,
				B88FB11D116CF27F00407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB125116CF29E00407530 /* PBXTargetDependency */,
			);
			name = dwarf_cfi_to_module_unittest;
			productName = dwarf_cfi_to_module_unittest;
			productReference = B88FB11F116CF27F00407530 /* dwarf_cfi_to_module_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB134116CF30F00407530 /* bytereader_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB13A116CF33400407530 /* Build configuration list for PBXNativeTarget "bytereader_unittest" */;
			buildPhases = (
				B88FB132116CF30F00407530 /* Sources */,
				B88FB133116CF30F00407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB13C116CF35C00407530 /* PBXTargetDependency */,
			);
			name = bytereader_unittest;
			productName = bytereader_unittest;
			productReference = B88FB135116CF30F00407530 /* bytereader_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B88FB14A116CF4A700407530 /* byte_cursor_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B88FB159116CF4F900407530 /* Build configuration list for PBXNativeTarget "byte_cursor_unittest" */;
			buildPhases = (
				B88FB148116CF4A700407530 /* Sources */,
				B88FB149116CF4A700407530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB151116CF4C100407530 /* PBXTargetDependency */,
			);
			name = byte_cursor_unittest;
			productName = byte_cursor_unittest;
			productReference = B88FB14B116CF4A700407530 /* byte_cursor_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B89E0E731166575200DD08C9 /* macho_dump */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B89E0E7F116657A100DD08C9 /* Build configuration list for PBXNativeTarget "macho_dump" */;
			buildPhases = (
				B89E0E711166575200DD08C9 /* Sources */,
				B89E0E721166575200DD08C9 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = macho_dump;
			productName = macho_dump;
			productReference = B89E0E741166575200DD08C9 /* macho_dump */;
			productType = "com.apple.product-type.tool";
		};
		B89E0E9411665A6400DD08C9 /* macho_reader_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B89E0E9E11665A9600DD08C9 /* Build configuration list for PBXNativeTarget "macho_reader_unittest" */;
			buildPhases = (
				B89E0E9211665A6400DD08C9 /* Sources */,
				B89E0E9311665A6400DD08C9 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				B88FB090116CE71000407530 /* PBXTargetDependency */,
			);
			name = macho_reader_unittest;
			productName = macho_reader_unittest;
			productReference = B89E0E9511665A6400DD08C9 /* macho_reader_unittest */;
			productType = "com.apple.product-type.tool";
		};
		B8C5B5101166531A00D34F4E /* dump_syms */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B8C5B5151166533900D34F4E /* Build configuration list for PBXNativeTarget "dump_syms" */;
			buildPhases = (
				B8C5B50E1166531A00D34F4E /* Sources */,
				B8C5B50F1166531A00D34F4E /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = dump_syms;
			productName = dump_syms;
			productReference = B8C5B5111166531A00D34F4E /* dump_syms */;
			productType = "com.apple.product-type.tool";
		};
		D21F97D111CBA0F200239E38 /* test_assembler_unittest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D21F97D611CBA11000239E38 /* Build configuration list for PBXNativeTarget "test_assembler_unittest" */;
			buildPhases = (
				D21F97CF11CBA0F200239E38 /* Sources */,
				D21F97D011CBA0F200239E38 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = test_assembler_unittest;
			productName = test_assembler_unittest;
			productReference = D21F97D211CBA0F200239E38 /* test_assembler_unittest */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08FB7793FE84155DC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
			};
			buildConfigurationList = 1DEB927808733DD40010E9CD /* Build configuration list for PBXProject "dump_syms" */;
			compatibilityVersion = "Xcode 3.1";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
			);
			mainGroup = 08FB7794FE84155DC02AAC07 /* dump_syms */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B8C5B5101166531A00D34F4E /* dump_syms */,
				B89E0E731166575200DD08C9 /* macho_dump */,
				B88FB023116BDFFF00407530 /* gtestmockall */,
				B88FB14A116CF4A700407530 /* byte_cursor_unittest */,
				B89E0E9411665A6400DD08C9 /* macho_reader_unittest */,
				B88FB006116BDE8300407530 /* stabs_reader_unittest */,
				B88FB134116CF30F00407530 /* bytereader_unittest */,
				B88FAF2E116A591D00407530 /* dwarf2reader_cfi_unittest */,
				B88FB0DE116CEEA800407530 /* dwarf2diehandler_unittest */,
				B88FB109116CF07900407530 /* dwarf_cu_to_module_unittest */,
				B88FB0F1116CEF1900407530 /* dwarf_line_to_module_unittest */,
				B88FB11E116CF27F00407530 /* dwarf_cfi_to_module_unittest */,
				B84A91F3116CF784006C210E /* stabs_to_module_unittest */,
				B88FB0B8116CEABF00407530 /* module_unittest */,
				B88FAFC9116BDCAD00407530 /* all_unittests */,
				D21F97D111CBA0F200239E38 /* test_assembler_unittest */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		B88FB094116CE73E00407530 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -eu\n\ncd $BUILT_PRODUCTS_DIR\npwd\n\n./byte_cursor_unittest\n./macho_reader_unittest\n./stabs_reader_unittest\n./bytereader_unittest\n./dwarf2reader_cfi_unittest\n./dwarf2diehandler_unittest\n./dwarf_cu_to_module_unittest\n./dwarf_line_to_module_unittest\n./dwarf_cfi_to_module_unittest\n./stabs_to_module_unittest\n./module_unittest\n./test_assembler_unittest\n\necho \"Expect two warnings from the following tests:\"\necho \"   Errors.BadFileNumber\"\necho \"   Errors.BadDirectoryNumber\"\necho \"The proper behavior of these tests is to print text that XCode confuses with compiler warnings.\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B84A91F1116CF784006C210E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B84A91FB116CF7AF006C210E /* module.cc in Sources */,
				B84A91FC116CF7AF006C210E /* stabs_to_module.cc in Sources */,
				B84A91FD116CF7AF006C210E /* stabs_to_module_unittest.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FAF2C116A591D00407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FAF38116A595400407530 /* dwarf2reader_cfi_unittest.cc in Sources */,
				B88FAF3F116A5A2E00407530 /* dwarf2reader.cc in Sources */,
				8BCAAA4D1CE3B1260046090B /* elf_reader.cc in Sources */,
				B88FAF40116A5A2E00407530 /* bytereader.cc in Sources */,
				B88FAF37116A595400407530 /* cfi_assembler.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB004116BDE8300407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB00F116BDEA700407530 /* stabs_reader_unittest.cc in Sources */,
				B88FB010116BDEA700407530 /* stabs_reader.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB021116BDFFF00407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB028116BE03100407530 /* test_assembler.cc in Sources */,
				B88FB029116BE03100407530 /* gmock-all.cc in Sources */,
				B88FB02A116BE03100407530 /* gtest_main.cc in Sources */,
				B88FB02B116BE03100407530 /* gtest-all.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB0B6116CEABF00407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB0BD116CEAE000407530 /* module_unittest.cc in Sources */,
				B88FB0C4116CEB4100407530 /* module.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB0DC116CEEA800407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB0E5116CEED300407530 /* dwarf2diehandler.cc in Sources */,
				B88FB0E6116CEED300407530 /* dwarf2diehandler_unittest.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB0EF116CEF1900407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB0FA116CF00E00407530 /* dwarf_line_to_module.cc in Sources */,
				B88FB0FE116CF02400407530 /* module.cc in Sources */,
				B88FB0FB116CF00E00407530 /* dwarf_line_to_module_unittest.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB107116CF07900407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB112116CF1F000407530 /* dwarf_cu_to_module.cc in Sources */,
				B88FB113116CF1F000407530 /* dwarf_cu_to_module_unittest.cc in Sources */,
				B88FB114116CF1F000407530 /* language.cc in Sources */,
				B88FB115116CF1F000407530 /* module.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB11C116CF27F00407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB129116CF2DD00407530 /* module.cc in Sources */,
				B88FB12A116CF2DD00407530 /* dwarf_cfi_to_module.cc in Sources */,
				B88FB12B116CF2DD00407530 /* dwarf_cfi_to_module_unittest.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB132116CF30F00407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB13D116CF38300407530 /* cfi_assembler.cc in Sources */,
				B88FB13E116CF38300407530 /* bytereader.cc in Sources */,
				B88FB13F116CF38300407530 /* bytereader_unittest.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B88FB148116CF4A700407530 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B88FB152116CF4D300407530 /* byte_cursor_unittest.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89E0E711166575200DD08C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4247E63D2110D4B200482558 /* path_helper.cc in Sources */,
				162F6500161C5F2200CD68D5 /* arch_utilities.cc in Sources */,
				B89E0E781166576C00DD08C9 /* macho_reader.cc in Sources */,
				B89E0E7A1166576C00DD08C9 /* macho_dump.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89E0E9211665A6400DD08C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B89E0E9911665A7200DD08C9 /* macho_reader_unittest.cc in Sources */,
				B89E0E9A11665A7200DD08C9 /* macho_reader.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B8C5B50E1166531A00D34F4E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4262382821AC49A000E5A3A6 /* dwarf_range_list_handler.h in Sources */,
				162F64FA161C591500CD68D5 /* arch_utilities.cc in Sources */,
				B88FAE2C1166606200407530 /* macho_reader.cc in Sources */,
				8BCAAA4C1CE3A7980046090B /* elf_reader.cc in Sources */,
				B8C5B5171166534700D34F4E /* dwarf2reader.cc in Sources */,
				EB06C7531FEBC516000214D9 /* path_helper.cc in Sources */,
				B8C5B5181166534700D34F4E /* bytereader.cc in Sources */,
				B8C5B5191166534700D34F4E /* macho_utilities.cc in Sources */,
				B8C5B51A1166534700D34F4E /* file_id.cc in Sources */,
				B8C5B51B1166534700D34F4E /* macho_id.cc in Sources */,
				B8C5B51C1166534700D34F4E /* macho_walker.cc in Sources */,
				B8C5B51D1166534700D34F4E /* dump_syms.cc in Sources */,
				B8C5B51E1166534700D34F4E /* dump_syms_tool.cc in Sources */,
				B88FAE1911665FE400407530 /* dwarf2diehandler.cc in Sources */,
				B88FAE261166603300407530 /* dwarf_cu_to_module.cc in Sources */,
				B88FAE271166603300407530 /* dwarf_line_to_module.cc in Sources */,
				4262382721AC496F00E5A3A6 /* dwarf_range_list_handler.cc in Sources */,
				B88FAE281166603300407530 /* language.cc in Sources */,
				B88FAE291166603300407530 /* module.cc in Sources */,
				B88FAE351166673E00407530 /* dwarf_cfi_to_module.cc in Sources */,
				B88FAE3B11666C6F00407530 /* stabs_reader.cc in Sources */,
				B88FAE3E11666C8900407530 /* stabs_to_module.cc in Sources */,
				4D72CAF513DFBAC2006CABE3 /* md5.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D21F97CF11CBA0F200239E38 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D21F97E911CBA1FF00239E38 /* test_assembler.cc in Sources */,
				D21F97D711CBA12300239E38 /* test_assembler_unittest.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8B31051511F100CF00FCF3E4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D21F97D111CBA0F200239E38 /* test_assembler_unittest */;
			targetProxy = 8B31051411F100CF00FCF3E4 /* PBXContainerItemProxy */;
		};
		B84A91FA116CF796006C210E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B84A91F9116CF796006C210E /* PBXContainerItemProxy */;
		};
		B84A9201116CF7D2006C210E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B84A91F3116CF784006C210E /* stabs_to_module_unittest */;
			targetProxy = B84A9200116CF7D2006C210E /* PBXContainerItemProxy */;
		};
		B88FAFCF116BDD7000407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B89E0E9411665A6400DD08C9 /* macho_reader_unittest */;
			targetProxy = B88FAFCE116BDD7000407530 /* PBXContainerItemProxy */;
		};
		B88FAFD1116BDD7000407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FAF2E116A591D00407530 /* dwarf2reader_cfi_unittest */;
			targetProxy = B88FAFD0116BDD7000407530 /* PBXContainerItemProxy */;
		};
		B88FB01D116BDF9800407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB006116BDE8300407530 /* stabs_reader_unittest */;
			targetProxy = B88FB01C116BDF9800407530 /* PBXContainerItemProxy */;
		};
		B88FB03A116BE17E00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB039116BE17E00407530 /* PBXContainerItemProxy */;
		};
		B88FB088116CE6D800407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB087116CE6D800407530 /* PBXContainerItemProxy */;
		};
		B88FB090116CE71000407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB08F116CE71000407530 /* PBXContainerItemProxy */;
		};
		B88FB0C0116CEAFE00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB0BF116CEAFE00407530 /* PBXContainerItemProxy */;
		};
		B88FB0C8116CEB4A00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB0B8116CEABF00407530 /* module_unittest */;
			targetProxy = B88FB0C7116CEB4A00407530 /* PBXContainerItemProxy */;
		};
		B88FB0E8116CEEDA00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB0E7116CEEDA00407530 /* PBXContainerItemProxy */;
		};
		B88FB0F8116CEF2E00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB0F7116CEF2E00407530 /* PBXContainerItemProxy */;
		};
		B88FB110116CF08A00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB10F116CF08A00407530 /* PBXContainerItemProxy */;
		};
		B88FB125116CF29E00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB124116CF29E00407530 /* PBXContainerItemProxy */;
		};
		B88FB13C116CF35C00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB13B116CF35C00407530 /* PBXContainerItemProxy */;
		};
		B88FB151116CF4C100407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB023116BDFFF00407530 /* gtestmockall */;
			targetProxy = B88FB150116CF4C100407530 /* PBXContainerItemProxy */;
		};
		B88FB15B116CF53E00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB14A116CF4A700407530 /* byte_cursor_unittest */;
			targetProxy = B88FB15A116CF53E00407530 /* PBXContainerItemProxy */;
		};
		B88FB15D116CF54B00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB11E116CF27F00407530 /* dwarf_cfi_to_module_unittest */;
			targetProxy = B88FB15C116CF54B00407530 /* PBXContainerItemProxy */;
		};
		B88FB15F116CF54B00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB0F1116CEF1900407530 /* dwarf_line_to_module_unittest */;
			targetProxy = B88FB15E116CF54B00407530 /* PBXContainerItemProxy */;
		};
		B88FB161116CF54B00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB109116CF07900407530 /* dwarf_cu_to_module_unittest */;
			targetProxy = B88FB160116CF54B00407530 /* PBXContainerItemProxy */;
		};
		B88FB165116CF54B00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB0DE116CEEA800407530 /* dwarf2diehandler_unittest */;
			targetProxy = B88FB164116CF54B00407530 /* PBXContainerItemProxy */;
		};
		B88FB167116CF54B00407530 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B88FB134116CF30F00407530 /* bytereader_unittest */;
			targetProxy = B88FB166116CF54B00407530 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1DEB927908733DD40010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8B3102D411F0D60300FCF3E4 /* BreakpadDebug.xcconfig */;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				HEADER_SEARCH_PATHS = (
					../../..,
					../../../common/mac/include/,
					../../../third_party/musl/include/,
				);
			};
			name = Debug;
		};
		1DEB927A08733DD40010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8B3102D511F0D60300FCF3E4 /* BreakpadRelease.xcconfig */;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				HEADER_SEARCH_PATHS = (
					../../..,
					../../../common/mac/include/,
					../../../third_party/musl/include/,
				);
			};
			name = Release;
		};
		B84A91F6116CF784006C210E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = stabs_to_module_unittest;
			};
			name = Debug;
		};
		B84A91F7116CF784006C210E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = stabs_to_module_unittest;
			};
			name = Release;
		};
		B88FAF31116A591F00407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(LIBRARY_SEARCH_PATHS_QUOTED_FOR_TARGET_1)",
				);
				LIBRARY_SEARCH_PATHS_QUOTED_FOR_TARGET_1 = "\"$(SRCROOT)/build/Debug\"";
				PRODUCT_NAME = dwarf2reader_cfi_unittest;
			};
			name = Debug;
		};
		B88FAF32116A591F00407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(LIBRARY_SEARCH_PATHS_QUOTED_FOR_TARGET_1)",
				);
				LIBRARY_SEARCH_PATHS_QUOTED_FOR_TARGET_1 = "\"$(SRCROOT)/build/Debug\"";
				PRODUCT_NAME = dwarf2reader_cfi_unittest;
			};
			name = Release;
		};
		B88FAFCA116BDCAD00407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = all_unittests;
			};
			name = Debug;
		};
		B88FAFCB116BDCAD00407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = all_unittests;
			};
			name = Release;
		};
		B88FB009116BDE8400407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_MACH_O_NLIST_H;
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = stabs_reader_unittest;
			};
			name = Debug;
		};
		B88FB00A116BDE8400407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_MACH_O_NLIST_H;
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = stabs_reader_unittest;
			};
			name = Release;
		};
		B88FB025116BE00100407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = gtestmockall;
			};
			name = Debug;
		};
		B88FB026116BE00100407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = gtestmockall;
			};
			name = Release;
		};
		B88FB0BB116CEAC000407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = module_unittest;
			};
			name = Debug;
		};
		B88FB0BC116CEAC000407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = module_unittest;
			};
			name = Release;
		};
		B88FB0E1116CEEA800407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf2diehandler_unittest;
			};
			name = Debug;
		};
		B88FB0E2116CEEA800407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf2diehandler_unittest;
			};
			name = Release;
		};
		B88FB0F4116CEF1900407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf_line_to_module_unittest;
			};
			name = Debug;
		};
		B88FB0F5116CEF1900407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf_line_to_module_unittest;
			};
			name = Release;
		};
		B88FB10C116CF07A00407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf_cu_to_module_unittest;
			};
			name = Debug;
		};
		B88FB10D116CF07A00407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf_cu_to_module_unittest;
			};
			name = Release;
		};
		B88FB121116CF28000407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf_cfi_to_module_unittest;
			};
			name = Debug;
		};
		B88FB122116CF28000407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = dwarf_cfi_to_module_unittest;
			};
			name = Release;
		};
		B88FB137116CF30F00407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = bytereader_unittest;
			};
			name = Debug;
		};
		B88FB138116CF30F00407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
					../../../testing/googletest,
					../../../testing/googletest/include,
				);
				PRODUCT_NAME = bytereader_unittest;
			};
			name = Release;
		};
		B88FB14D116CF4A800407530 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googletest,
					../../../testing/googletest/include,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
				);
				PRODUCT_NAME = byte_cursor_unittest;
			};
			name = Debug;
		};
		B88FB14E116CF4A800407530 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googletest,
					../../../testing/googletest/include,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
				);
				PRODUCT_NAME = byte_cursor_unittest;
			};
			name = Release;
		};
		B89E0E761166575300DD08C9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = macho_dump;
			};
			name = Debug;
		};
		B89E0E771166575300DD08C9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = macho_dump;
			};
			name = Release;
		};
		B89E0E9711665A6400DD08C9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googletest,
					../../../testing/googletest/include,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
				);
				PRODUCT_NAME = macho_reader_unittest;
			};
			name = Debug;
		};
		B89E0E9811665A6400DD08C9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googletest,
					../../../testing/googletest/include,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
				);
				PRODUCT_NAME = macho_reader_unittest;
			};
			name = Release;
		};
		B8C5B5131166531B00D34F4E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_MACH_O_NLIST_H;
				GCC_VERSION = "";
				PRODUCT_NAME = dump_syms;
			};
			name = Debug;
		};
		B8C5B5141166531B00D34F4E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_MACH_O_NLIST_H;
				GCC_VERSION = "";
				PRODUCT_NAME = dump_syms;
			};
			name = Release;
		};
		D21F97D411CBA0F200239E38 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googletest,
					../../../testing/googletest/include,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
				);
				PRODUCT_NAME = test_assembler_unittest;
			};
			name = Debug;
		};
		D21F97D511CBA0F200239E38 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					$inherited,
					../../../testing/googletest,
					../../../testing/googletest/include,
					../../../testing/googlemock,
					../../../testing/googlemock/include,
				);
				PRODUCT_NAME = test_assembler_unittest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1DEB927808733DD40010E9CD /* Build configuration list for PBXProject "dump_syms" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB927908733DD40010E9CD /* Debug */,
				1DEB927A08733DD40010E9CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B84A9202116CF7F0006C210E /* Build configuration list for PBXNativeTarget "stabs_to_module_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B84A91F6116CF784006C210E /* Debug */,
				B84A91F7116CF784006C210E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FAF33116A594800407530 /* Build configuration list for PBXNativeTarget "dwarf2reader_cfi_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FAF31116A591F00407530 /* Debug */,
				B88FAF32116A591F00407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FAFCC116BDCCC00407530 /* Build configuration list for PBXAggregateTarget "all_unittests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FAFCA116BDCAD00407530 /* Debug */,
				B88FAFCB116BDCAD00407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB013116BDEC800407530 /* Build configuration list for PBXNativeTarget "stabs_reader_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB009116BDE8400407530 /* Debug */,
				B88FB00A116BDE8400407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB027116BE02900407530 /* Build configuration list for PBXNativeTarget "gtestmockall" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB025116BE00100407530 /* Debug */,
				B88FB026116BE00100407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB0BE116CEAFE00407530 /* Build configuration list for PBXNativeTarget "module_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB0BB116CEAC000407530 /* Debug */,
				B88FB0BC116CEAC000407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB0E4116CEECE00407530 /* Build configuration list for PBXNativeTarget "dwarf2diehandler_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB0E1116CEEA800407530 /* Debug */,
				B88FB0E2116CEEA800407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB0F9116CEF9800407530 /* Build configuration list for PBXNativeTarget "dwarf_line_to_module_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB0F4116CEF1900407530 /* Debug */,
				B88FB0F5116CEF1900407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB111116CF0A800407530 /* Build configuration list for PBXNativeTarget "dwarf_cu_to_module_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB10C116CF07A00407530 /* Debug */,
				B88FB10D116CF07A00407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB128116CF2C800407530 /* Build configuration list for PBXNativeTarget "dwarf_cfi_to_module_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB121116CF28000407530 /* Debug */,
				B88FB122116CF28000407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB13A116CF33400407530 /* Build configuration list for PBXNativeTarget "bytereader_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB137116CF30F00407530 /* Debug */,
				B88FB138116CF30F00407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B88FB159116CF4F900407530 /* Build configuration list for PBXNativeTarget "byte_cursor_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B88FB14D116CF4A800407530 /* Debug */,
				B88FB14E116CF4A800407530 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B89E0E7F116657A100DD08C9 /* Build configuration list for PBXNativeTarget "macho_dump" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B89E0E761166575300DD08C9 /* Debug */,
				B89E0E771166575300DD08C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B89E0E9E11665A9600DD08C9 /* Build configuration list for PBXNativeTarget "macho_reader_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B89E0E9711665A6400DD08C9 /* Debug */,
				B89E0E9811665A6400DD08C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B8C5B5151166533900D34F4E /* Build configuration list for PBXNativeTarget "dump_syms" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B8C5B5131166531B00D34F4E /* Debug */,
				B8C5B5141166531B00D34F4E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D21F97D611CBA11000239E38 /* Build configuration list for PBXNativeTarget "test_assembler_unittest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D21F97D411CBA0F200239E38 /* Debug */,
				D21F97D511CBA0F200239E38 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08FB7793FE84155DC02AAC07 /* Project object */;
}
