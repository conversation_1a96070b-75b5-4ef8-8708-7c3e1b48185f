// -*- mode: c++ -*-

// Test data for pairing functions and lines.
// 
// For a pair of functions that are adjacent (10,20),(20,25) and a
// pair that are not (10,15),(20,25), we include a test case for every
// possible arrangement of two lines relative to those functions. We
// include cases only for non-empty ranges, since empty functions and
// lines are dropped before we do any pairing.
//
// Each test case is represented by a macro call of the form:
// 
//   PAIRING(func1_start, func1_end, func2_start, func2_end,
//           line1_start, line1_end, line2_start, line2_end,
//           func1_num_lines, func2_num_lines,
//           func1_line1_start, func1_line1_end,
//           func1_line2_start, func1_line2_end,
//           func2_line1_start, func2_line1_end,
//           func2_line2_start, func2_line2_end,
//           uncovered_funcs, uncovered_lines)
//
// where:
// - funcN_{start,end} is the range of the N'th function
// - lineN_{start,end} is the range of the N'th function
// - funcN_num_lines is the number of source lines that should be
//   paired with the N'th function
// - funcN_lineM_{start,end} is the range of the M'th line
//   paired with the N'th function, where 0,0 indicates that
//   there should be no such line paired
// - uncovered_funcs is the number of functions with area that is
//   uncovered by any line, and
// - uncovered_lines is the reverse.

//      func1   func2    line1   line2    num    pairing1        pairing2       uncovered
PAIRING(10, 20, 20, 25,  6,  7,  7,  8,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #0
PAIRING(10, 20, 20, 25,  6,  7,  7, 10,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #1
PAIRING(10, 20, 20, 25,  6,  7,  7, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #2
PAIRING(10, 20, 20, 25,  6,  7,  7, 20,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 2) // #3
PAIRING(10, 20, 20, 25,  6,  7,  7, 21,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 2) // #4
PAIRING(10, 20, 20, 25,  6,  7,  7, 25,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #5
PAIRING(10, 20, 20, 25,  6,  7,  7, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #6
PAIRING(10, 20, 20, 25,  6,  7,  8,  9,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #7
PAIRING(10, 20, 20, 25,  6,  7,  8, 10,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #8
PAIRING(10, 20, 20, 25,  6,  7,  8, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #9
PAIRING(10, 20, 20, 25,  6,  7,  8, 20,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 2) // #10
PAIRING(10, 20, 20, 25,  6,  7,  8, 21,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 2) // #11
PAIRING(10, 20, 20, 25,  6,  7,  8, 25,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #12
PAIRING(10, 20, 20, 25,  6,  7,  8, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #13
PAIRING(10, 20, 20, 25,  6,  7, 10, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #14
PAIRING(10, 20, 20, 25,  6,  7, 10, 20,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 1) // #15
PAIRING(10, 20, 20, 25,  6,  7, 10, 21,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 1) // #16
PAIRING(10, 20, 20, 25,  6,  7, 10, 25,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 1) // #17
PAIRING(10, 20, 20, 25,  6,  7, 10, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #18
PAIRING(10, 20, 20, 25,  6,  7, 11, 12,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #19
PAIRING(10, 20, 20, 25,  6,  7, 11, 20,   1, 0, 11, 20,  0,  0,  0,  0,  0,  0,   2, 1) // #20
PAIRING(10, 20, 20, 25,  6,  7, 11, 21,   1, 1, 11, 20,  0,  0, 20, 21,  0,  0,   2, 1) // #21
PAIRING(10, 20, 20, 25,  6,  7, 11, 25,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 1) // #22
PAIRING(10, 20, 20, 25,  6,  7, 11, 26,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 2) // #23
PAIRING(10, 20, 20, 25,  6,  7, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #24
PAIRING(10, 20, 20, 25,  6,  7, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #25
PAIRING(10, 20, 20, 25,  6,  7, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #26
PAIRING(10, 20, 20, 25,  6,  7, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #27
PAIRING(10, 20, 20, 25,  6,  7, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #28
PAIRING(10, 20, 20, 25,  6,  7, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #29
PAIRING(10, 20, 20, 25,  6,  7, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #30
PAIRING(10, 20, 20, 25,  6,  7, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #31
PAIRING(10, 20, 20, 25,  6, 10, 10, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #32
PAIRING(10, 20, 20, 25,  6, 10, 10, 20,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 1) // #33
PAIRING(10, 20, 20, 25,  6, 10, 10, 21,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 1) // #34
PAIRING(10, 20, 20, 25,  6, 10, 10, 25,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 1) // #35
PAIRING(10, 20, 20, 25,  6, 10, 10, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #36
PAIRING(10, 20, 20, 25,  6, 10, 11, 12,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #37
PAIRING(10, 20, 20, 25,  6, 10, 11, 20,   1, 0, 11, 20,  0,  0,  0,  0,  0,  0,   2, 1) // #38
PAIRING(10, 20, 20, 25,  6, 10, 11, 21,   1, 1, 11, 20,  0,  0, 20, 21,  0,  0,   2, 1) // #39
PAIRING(10, 20, 20, 25,  6, 10, 11, 25,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 1) // #40
PAIRING(10, 20, 20, 25,  6, 10, 11, 26,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 2) // #41
PAIRING(10, 20, 20, 25,  6, 10, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #42
PAIRING(10, 20, 20, 25,  6, 10, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #43
PAIRING(10, 20, 20, 25,  6, 10, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #44
PAIRING(10, 20, 20, 25,  6, 10, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #45
PAIRING(10, 20, 20, 25,  6, 10, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #46
PAIRING(10, 20, 20, 25,  6, 10, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #47
PAIRING(10, 20, 20, 25,  6, 10, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #48
PAIRING(10, 20, 20, 25,  6, 10, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #49
PAIRING(10, 20, 20, 25,  6, 11, 11, 12,   2, 0, 10, 11, 11, 12,  0,  0,  0,  0,   2, 1) // #50
PAIRING(10, 20, 20, 25,  6, 11, 11, 20,   2, 0, 10, 11, 11, 20,  0,  0,  0,  0,   1, 1) // #51
PAIRING(10, 20, 20, 25,  6, 11, 11, 21,   2, 1, 10, 11, 11, 20, 20, 21,  0,  0,   1, 1) // #52
PAIRING(10, 20, 20, 25,  6, 11, 11, 25,   2, 1, 10, 11, 11, 20, 20, 25,  0,  0,   0, 1) // #53
PAIRING(10, 20, 20, 25,  6, 11, 11, 26,   2, 1, 10, 11, 11, 20, 20, 25,  0,  0,   0, 2) // #54
PAIRING(10, 20, 20, 25,  6, 11, 12, 13,   2, 0, 10, 11, 12, 13,  0,  0,  0,  0,   2, 1) // #55
PAIRING(10, 20, 20, 25,  6, 11, 12, 20,   2, 0, 10, 11, 12, 20,  0,  0,  0,  0,   2, 1) // #56
PAIRING(10, 20, 20, 25,  6, 11, 12, 21,   2, 1, 10, 11, 12, 20, 20, 21,  0,  0,   2, 1) // #57
PAIRING(10, 20, 20, 25,  6, 11, 12, 25,   2, 1, 10, 11, 12, 20, 20, 25,  0,  0,   1, 1) // #58
PAIRING(10, 20, 20, 25,  6, 11, 12, 26,   2, 1, 10, 11, 12, 20, 20, 25,  0,  0,   1, 2) // #59
PAIRING(10, 20, 20, 25,  6, 11, 20, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 1) // #60
PAIRING(10, 20, 20, 25,  6, 11, 20, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #61
PAIRING(10, 20, 20, 25,  6, 11, 20, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 2) // #62
PAIRING(10, 20, 20, 25,  6, 11, 21, 22,   1, 1, 10, 11,  0,  0, 21, 22,  0,  0,   2, 1) // #63
PAIRING(10, 20, 20, 25,  6, 11, 21, 25,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 1) // #64
PAIRING(10, 20, 20, 25,  6, 11, 21, 26,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 2) // #65
PAIRING(10, 20, 20, 25,  6, 11, 25, 26,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #66
PAIRING(10, 20, 20, 25,  6, 11, 26, 27,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #67
PAIRING(10, 20, 20, 25,  6, 20, 20, 21,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 1) // #68
PAIRING(10, 20, 20, 25,  6, 20, 20, 25,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 1) // #69
PAIRING(10, 20, 20, 25,  6, 20, 20, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #70
PAIRING(10, 20, 20, 25,  6, 20, 21, 22,   1, 1, 10, 20,  0,  0, 21, 22,  0,  0,   1, 1) // #71
PAIRING(10, 20, 20, 25,  6, 20, 21, 25,   1, 1, 10, 20,  0,  0, 21, 25,  0,  0,   1, 1) // #72
PAIRING(10, 20, 20, 25,  6, 20, 21, 26,   1, 1, 10, 20,  0,  0, 21, 25,  0,  0,   1, 2) // #73
PAIRING(10, 20, 20, 25,  6, 20, 25, 26,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 2) // #74
PAIRING(10, 20, 20, 25,  6, 20, 26, 27,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 2) // #75
PAIRING(10, 20, 20, 25,  6, 21, 21, 22,   1, 2, 10, 20,  0,  0, 20, 21, 21, 22,   1, 1) // #76
PAIRING(10, 20, 20, 25,  6, 21, 21, 25,   1, 2, 10, 20,  0,  0, 20, 21, 21, 25,   0, 1) // #77
PAIRING(10, 20, 20, 25,  6, 21, 21, 26,   1, 2, 10, 20,  0,  0, 20, 21, 21, 25,   0, 2) // #78
PAIRING(10, 20, 20, 25,  6, 21, 22, 23,   1, 2, 10, 20,  0,  0, 20, 21, 22, 23,   1, 1) // #79
PAIRING(10, 20, 20, 25,  6, 21, 22, 25,   1, 2, 10, 20,  0,  0, 20, 21, 22, 25,   1, 1) // #80
PAIRING(10, 20, 20, 25,  6, 21, 22, 26,   1, 2, 10, 20,  0,  0, 20, 21, 22, 25,   1, 2) // #81
PAIRING(10, 20, 20, 25,  6, 21, 25, 26,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 2) // #82
PAIRING(10, 20, 20, 25,  6, 21, 26, 27,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 2) // #83
PAIRING(10, 20, 20, 25,  6, 25, 25, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #84
PAIRING(10, 20, 20, 25,  6, 25, 26, 27,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #85
PAIRING(10, 20, 20, 25,  6, 26, 26, 27,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #86
PAIRING(10, 20, 20, 25,  6, 26, 27, 28,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #87
PAIRING(10, 20, 20, 25, 10, 11, 11, 12,   2, 0, 10, 11, 11, 12,  0,  0,  0,  0,   2, 0) // #88
PAIRING(10, 20, 20, 25, 10, 11, 11, 20,   2, 0, 10, 11, 11, 20,  0,  0,  0,  0,   1, 0) // #89
PAIRING(10, 20, 20, 25, 10, 11, 11, 21,   2, 1, 10, 11, 11, 20, 20, 21,  0,  0,   1, 0) // #90
PAIRING(10, 20, 20, 25, 10, 11, 11, 25,   2, 1, 10, 11, 11, 20, 20, 25,  0,  0,   0, 0) // #91
PAIRING(10, 20, 20, 25, 10, 11, 11, 26,   2, 1, 10, 11, 11, 20, 20, 25,  0,  0,   0, 1) // #92
PAIRING(10, 20, 20, 25, 10, 11, 12, 13,   2, 0, 10, 11, 12, 13,  0,  0,  0,  0,   2, 0) // #93
PAIRING(10, 20, 20, 25, 10, 11, 12, 20,   2, 0, 10, 11, 12, 20,  0,  0,  0,  0,   2, 0) // #94
PAIRING(10, 20, 20, 25, 10, 11, 12, 21,   2, 1, 10, 11, 12, 20, 20, 21,  0,  0,   2, 0) // #95
PAIRING(10, 20, 20, 25, 10, 11, 12, 25,   2, 1, 10, 11, 12, 20, 20, 25,  0,  0,   1, 0) // #96
PAIRING(10, 20, 20, 25, 10, 11, 12, 26,   2, 1, 10, 11, 12, 20, 20, 25,  0,  0,   1, 1) // #97
PAIRING(10, 20, 20, 25, 10, 11, 20, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 0) // #98
PAIRING(10, 20, 20, 25, 10, 11, 20, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 0) // #99
PAIRING(10, 20, 20, 25, 10, 11, 20, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #100
PAIRING(10, 20, 20, 25, 10, 11, 21, 22,   1, 1, 10, 11,  0,  0, 21, 22,  0,  0,   2, 0) // #101
PAIRING(10, 20, 20, 25, 10, 11, 21, 25,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 0) // #102
PAIRING(10, 20, 20, 25, 10, 11, 21, 26,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 1) // #103
PAIRING(10, 20, 20, 25, 10, 11, 25, 26,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #104
PAIRING(10, 20, 20, 25, 10, 11, 26, 27,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #105
PAIRING(10, 20, 20, 25, 10, 20, 20, 21,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 0) // #106
PAIRING(10, 20, 20, 25, 10, 20, 20, 25,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 0) // #107
PAIRING(10, 20, 20, 25, 10, 20, 20, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 1) // #108
PAIRING(10, 20, 20, 25, 10, 20, 21, 22,   1, 1, 10, 20,  0,  0, 21, 22,  0,  0,   1, 0) // #109
PAIRING(10, 20, 20, 25, 10, 20, 21, 25,   1, 1, 10, 20,  0,  0, 21, 25,  0,  0,   1, 0) // #110
PAIRING(10, 20, 20, 25, 10, 20, 21, 26,   1, 1, 10, 20,  0,  0, 21, 25,  0,  0,   1, 1) // #111
PAIRING(10, 20, 20, 25, 10, 20, 25, 26,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 1) // #112
PAIRING(10, 20, 20, 25, 10, 20, 26, 27,   1, 0, 10, 20,  0,  0,  0,  0,  0,  0,   1, 1) // #113
PAIRING(10, 20, 20, 25, 10, 21, 21, 22,   1, 2, 10, 20,  0,  0, 20, 21, 21, 22,   1, 0) // #114
PAIRING(10, 20, 20, 25, 10, 21, 21, 25,   1, 2, 10, 20,  0,  0, 20, 21, 21, 25,   0, 0) // #115
PAIRING(10, 20, 20, 25, 10, 21, 21, 26,   1, 2, 10, 20,  0,  0, 20, 21, 21, 25,   0, 1) // #116
PAIRING(10, 20, 20, 25, 10, 21, 22, 23,   1, 2, 10, 20,  0,  0, 20, 21, 22, 23,   1, 0) // #117
PAIRING(10, 20, 20, 25, 10, 21, 22, 25,   1, 2, 10, 20,  0,  0, 20, 21, 22, 25,   1, 0) // #118
PAIRING(10, 20, 20, 25, 10, 21, 22, 26,   1, 2, 10, 20,  0,  0, 20, 21, 22, 25,   1, 1) // #119
PAIRING(10, 20, 20, 25, 10, 21, 25, 26,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 1) // #120
PAIRING(10, 20, 20, 25, 10, 21, 26, 27,   1, 1, 10, 20,  0,  0, 20, 21,  0,  0,   1, 1) // #121
PAIRING(10, 20, 20, 25, 10, 25, 25, 26,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 1) // #122
PAIRING(10, 20, 20, 25, 10, 25, 26, 27,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 1) // #123
PAIRING(10, 20, 20, 25, 10, 26, 26, 27,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #124
PAIRING(10, 20, 20, 25, 10, 26, 27, 28,   1, 1, 10, 20,  0,  0, 20, 25,  0,  0,   0, 2) // #125
PAIRING(10, 20, 20, 25, 11, 12, 12, 13,   2, 0, 11, 12, 12, 13,  0,  0,  0,  0,   2, 0) // #126
PAIRING(10, 20, 20, 25, 11, 12, 12, 20,   2, 0, 11, 12, 12, 20,  0,  0,  0,  0,   2, 0) // #127
PAIRING(10, 20, 20, 25, 11, 12, 12, 21,   2, 1, 11, 12, 12, 20, 20, 21,  0,  0,   2, 0) // #128
PAIRING(10, 20, 20, 25, 11, 12, 12, 25,   2, 1, 11, 12, 12, 20, 20, 25,  0,  0,   1, 0) // #129
PAIRING(10, 20, 20, 25, 11, 12, 12, 26,   2, 1, 11, 12, 12, 20, 20, 25,  0,  0,   1, 1) // #130
PAIRING(10, 20, 20, 25, 11, 12, 13, 14,   2, 0, 11, 12, 13, 14,  0,  0,  0,  0,   2, 0) // #131
PAIRING(10, 20, 20, 25, 11, 12, 13, 20,   2, 0, 11, 12, 13, 20,  0,  0,  0,  0,   2, 0) // #132
PAIRING(10, 20, 20, 25, 11, 12, 13, 21,   2, 1, 11, 12, 13, 20, 20, 21,  0,  0,   2, 0) // #133
PAIRING(10, 20, 20, 25, 11, 12, 13, 25,   2, 1, 11, 12, 13, 20, 20, 25,  0,  0,   1, 0) // #134
PAIRING(10, 20, 20, 25, 11, 12, 13, 26,   2, 1, 11, 12, 13, 20, 20, 25,  0,  0,   1, 1) // #135
PAIRING(10, 20, 20, 25, 11, 12, 20, 21,   1, 1, 11, 12,  0,  0, 20, 21,  0,  0,   2, 0) // #136
PAIRING(10, 20, 20, 25, 11, 12, 20, 25,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 0) // #137
PAIRING(10, 20, 20, 25, 11, 12, 20, 26,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 1) // #138
PAIRING(10, 20, 20, 25, 11, 12, 21, 22,   1, 1, 11, 12,  0,  0, 21, 22,  0,  0,   2, 0) // #139
PAIRING(10, 20, 20, 25, 11, 12, 21, 25,   1, 1, 11, 12,  0,  0, 21, 25,  0,  0,   2, 0) // #140
PAIRING(10, 20, 20, 25, 11, 12, 21, 26,   1, 1, 11, 12,  0,  0, 21, 25,  0,  0,   2, 1) // #141
PAIRING(10, 20, 20, 25, 11, 12, 25, 26,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #142
PAIRING(10, 20, 20, 25, 11, 12, 26, 27,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #143
PAIRING(10, 20, 20, 25, 11, 20, 20, 21,   1, 1, 11, 20,  0,  0, 20, 21,  0,  0,   2, 0) // #144
PAIRING(10, 20, 20, 25, 11, 20, 20, 25,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 0) // #145
PAIRING(10, 20, 20, 25, 11, 20, 20, 26,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 1) // #146
PAIRING(10, 20, 20, 25, 11, 20, 21, 22,   1, 1, 11, 20,  0,  0, 21, 22,  0,  0,   2, 0) // #147
PAIRING(10, 20, 20, 25, 11, 20, 21, 25,   1, 1, 11, 20,  0,  0, 21, 25,  0,  0,   2, 0) // #148
PAIRING(10, 20, 20, 25, 11, 20, 21, 26,   1, 1, 11, 20,  0,  0, 21, 25,  0,  0,   2, 1) // #149
PAIRING(10, 20, 20, 25, 11, 20, 25, 26,   1, 0, 11, 20,  0,  0,  0,  0,  0,  0,   2, 1) // #150
PAIRING(10, 20, 20, 25, 11, 20, 26, 27,   1, 0, 11, 20,  0,  0,  0,  0,  0,  0,   2, 1) // #151
PAIRING(10, 20, 20, 25, 11, 21, 21, 22,   1, 2, 11, 20,  0,  0, 20, 21, 21, 22,   2, 0) // #152
PAIRING(10, 20, 20, 25, 11, 21, 21, 25,   1, 2, 11, 20,  0,  0, 20, 21, 21, 25,   1, 0) // #153
PAIRING(10, 20, 20, 25, 11, 21, 21, 26,   1, 2, 11, 20,  0,  0, 20, 21, 21, 25,   1, 1) // #154
PAIRING(10, 20, 20, 25, 11, 21, 22, 23,   1, 2, 11, 20,  0,  0, 20, 21, 22, 23,   2, 0) // #155
PAIRING(10, 20, 20, 25, 11, 21, 22, 25,   1, 2, 11, 20,  0,  0, 20, 21, 22, 25,   2, 0) // #156
PAIRING(10, 20, 20, 25, 11, 21, 22, 26,   1, 2, 11, 20,  0,  0, 20, 21, 22, 25,   2, 1) // #157
PAIRING(10, 20, 20, 25, 11, 21, 25, 26,   1, 1, 11, 20,  0,  0, 20, 21,  0,  0,   2, 1) // #158
PAIRING(10, 20, 20, 25, 11, 21, 26, 27,   1, 1, 11, 20,  0,  0, 20, 21,  0,  0,   2, 1) // #159
PAIRING(10, 20, 20, 25, 11, 25, 25, 26,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 1) // #160
PAIRING(10, 20, 20, 25, 11, 25, 26, 27,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 1) // #161
PAIRING(10, 20, 20, 25, 11, 26, 26, 27,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 2) // #162
PAIRING(10, 20, 20, 25, 11, 26, 27, 28,   1, 1, 11, 20,  0,  0, 20, 25,  0,  0,   1, 2) // #163
PAIRING(10, 20, 20, 25, 20, 21, 21, 22,   0, 2,  0,  0,  0,  0, 20, 21, 21, 22,   2, 0) // #164
PAIRING(10, 20, 20, 25, 20, 21, 21, 25,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 0) // #165
PAIRING(10, 20, 20, 25, 20, 21, 21, 26,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 1) // #166
PAIRING(10, 20, 20, 25, 20, 21, 22, 23,   0, 2,  0,  0,  0,  0, 20, 21, 22, 23,   2, 0) // #167
PAIRING(10, 20, 20, 25, 20, 21, 22, 25,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 0) // #168
PAIRING(10, 20, 20, 25, 20, 21, 22, 26,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 1) // #169
PAIRING(10, 20, 20, 25, 20, 21, 25, 26,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #170
PAIRING(10, 20, 20, 25, 20, 21, 26, 27,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #171
PAIRING(10, 20, 20, 25, 20, 25, 25, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #172
PAIRING(10, 20, 20, 25, 20, 25, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #173
PAIRING(10, 20, 20, 25, 20, 26, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #174
PAIRING(10, 20, 20, 25, 20, 26, 27, 28,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #175
PAIRING(10, 20, 20, 25, 21, 22, 22, 23,   0, 2,  0,  0,  0,  0, 21, 22, 22, 23,   2, 0) // #176
PAIRING(10, 20, 20, 25, 21, 22, 22, 25,   0, 2,  0,  0,  0,  0, 21, 22, 22, 25,   2, 0) // #177
PAIRING(10, 20, 20, 25, 21, 22, 22, 26,   0, 2,  0,  0,  0,  0, 21, 22, 22, 25,   2, 1) // #178
PAIRING(10, 20, 20, 25, 21, 22, 23, 24,   0, 2,  0,  0,  0,  0, 21, 22, 23, 24,   2, 0) // #179
PAIRING(10, 20, 20, 25, 21, 22, 23, 25,   0, 2,  0,  0,  0,  0, 21, 22, 23, 25,   2, 0) // #180
PAIRING(10, 20, 20, 25, 21, 22, 23, 26,   0, 2,  0,  0,  0,  0, 21, 22, 23, 25,   2, 1) // #181
PAIRING(10, 20, 20, 25, 21, 22, 25, 26,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #182
PAIRING(10, 20, 20, 25, 21, 22, 26, 27,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #183
PAIRING(10, 20, 20, 25, 21, 25, 25, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #184
PAIRING(10, 20, 20, 25, 21, 25, 26, 27,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #185
PAIRING(10, 20, 20, 25, 21, 26, 26, 27,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #186
PAIRING(10, 20, 20, 25, 21, 26, 27, 28,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #187
PAIRING(10, 20, 20, 25, 25, 26, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #188
PAIRING(10, 20, 20, 25, 25, 26, 27, 28,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #189
PAIRING(10, 20, 20, 25, 26, 27, 27, 28,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #190
PAIRING(10, 20, 20, 25, 26, 27, 28, 29,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #191
PAIRING(10, 15, 20, 25,  6,  7,  7,  8,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #192
PAIRING(10, 15, 20, 25,  6,  7,  7, 10,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #193
PAIRING(10, 15, 20, 25,  6,  7,  7, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #194
PAIRING(10, 15, 20, 25,  6,  7,  7, 15,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #195
PAIRING(10, 15, 20, 25,  6,  7,  7, 16,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #196
PAIRING(10, 15, 20, 25,  6,  7,  7, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #197
PAIRING(10, 15, 20, 25,  6,  7,  7, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #198
PAIRING(10, 15, 20, 25,  6,  7,  7, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #199
PAIRING(10, 15, 20, 25,  6,  7,  7, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #200
PAIRING(10, 15, 20, 25,  6,  7,  8,  9,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #201
PAIRING(10, 15, 20, 25,  6,  7,  8, 10,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #202
PAIRING(10, 15, 20, 25,  6,  7,  8, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #203
PAIRING(10, 15, 20, 25,  6,  7,  8, 15,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #204
PAIRING(10, 15, 20, 25,  6,  7,  8, 16,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #205
PAIRING(10, 15, 20, 25,  6,  7,  8, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #206
PAIRING(10, 15, 20, 25,  6,  7,  8, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #207
PAIRING(10, 15, 20, 25,  6,  7,  8, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #208
PAIRING(10, 15, 20, 25,  6,  7,  8, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #209
PAIRING(10, 15, 20, 25,  6,  7, 10, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #210
PAIRING(10, 15, 20, 25,  6,  7, 10, 15,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #211
PAIRING(10, 15, 20, 25,  6,  7, 10, 16,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #212
PAIRING(10, 15, 20, 25,  6,  7, 10, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #213
PAIRING(10, 15, 20, 25,  6,  7, 10, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #214
PAIRING(10, 15, 20, 25,  6,  7, 10, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #215
PAIRING(10, 15, 20, 25,  6,  7, 10, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #216
PAIRING(10, 15, 20, 25,  6,  7, 11, 12,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #217
PAIRING(10, 15, 20, 25,  6,  7, 11, 15,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #218
PAIRING(10, 15, 20, 25,  6,  7, 11, 16,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #219
PAIRING(10, 15, 20, 25,  6,  7, 11, 20,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #220
PAIRING(10, 15, 20, 25,  6,  7, 11, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 2) // #221
PAIRING(10, 15, 20, 25,  6,  7, 11, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #222
PAIRING(10, 15, 20, 25,  6,  7, 11, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #223
PAIRING(10, 15, 20, 25,  6,  7, 15, 16,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #224
PAIRING(10, 15, 20, 25,  6,  7, 15, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #225
PAIRING(10, 15, 20, 25,  6,  7, 15, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #226
PAIRING(10, 15, 20, 25,  6,  7, 15, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #227
PAIRING(10, 15, 20, 25,  6,  7, 15, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #228
PAIRING(10, 15, 20, 25,  6,  7, 16, 17,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #229
PAIRING(10, 15, 20, 25,  6,  7, 16, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #230
PAIRING(10, 15, 20, 25,  6,  7, 16, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #231
PAIRING(10, 15, 20, 25,  6,  7, 16, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #232
PAIRING(10, 15, 20, 25,  6,  7, 16, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #233
PAIRING(10, 15, 20, 25,  6,  7, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #234
PAIRING(10, 15, 20, 25,  6,  7, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #235
PAIRING(10, 15, 20, 25,  6,  7, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #236
PAIRING(10, 15, 20, 25,  6,  7, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #237
PAIRING(10, 15, 20, 25,  6,  7, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #238
PAIRING(10, 15, 20, 25,  6,  7, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #239
PAIRING(10, 15, 20, 25,  6,  7, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #240
PAIRING(10, 15, 20, 25,  6,  7, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #241
PAIRING(10, 15, 20, 25,  6, 10, 10, 11,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #242
PAIRING(10, 15, 20, 25,  6, 10, 10, 15,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #243
PAIRING(10, 15, 20, 25,  6, 10, 10, 16,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #244
PAIRING(10, 15, 20, 25,  6, 10, 10, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #245
PAIRING(10, 15, 20, 25,  6, 10, 10, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #246
PAIRING(10, 15, 20, 25,  6, 10, 10, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #247
PAIRING(10, 15, 20, 25,  6, 10, 10, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #248
PAIRING(10, 15, 20, 25,  6, 10, 11, 12,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #249
PAIRING(10, 15, 20, 25,  6, 10, 11, 15,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #250
PAIRING(10, 15, 20, 25,  6, 10, 11, 16,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #251
PAIRING(10, 15, 20, 25,  6, 10, 11, 20,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #252
PAIRING(10, 15, 20, 25,  6, 10, 11, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 2) // #253
PAIRING(10, 15, 20, 25,  6, 10, 11, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #254
PAIRING(10, 15, 20, 25,  6, 10, 11, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #255
PAIRING(10, 15, 20, 25,  6, 10, 15, 16,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #256
PAIRING(10, 15, 20, 25,  6, 10, 15, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #257
PAIRING(10, 15, 20, 25,  6, 10, 15, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #258
PAIRING(10, 15, 20, 25,  6, 10, 15, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #259
PAIRING(10, 15, 20, 25,  6, 10, 15, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #260
PAIRING(10, 15, 20, 25,  6, 10, 16, 17,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #261
PAIRING(10, 15, 20, 25,  6, 10, 16, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #262
PAIRING(10, 15, 20, 25,  6, 10, 16, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #263
PAIRING(10, 15, 20, 25,  6, 10, 16, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #264
PAIRING(10, 15, 20, 25,  6, 10, 16, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #265
PAIRING(10, 15, 20, 25,  6, 10, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #266
PAIRING(10, 15, 20, 25,  6, 10, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #267
PAIRING(10, 15, 20, 25,  6, 10, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #268
PAIRING(10, 15, 20, 25,  6, 10, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #269
PAIRING(10, 15, 20, 25,  6, 10, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #270
PAIRING(10, 15, 20, 25,  6, 10, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #271
PAIRING(10, 15, 20, 25,  6, 10, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #272
PAIRING(10, 15, 20, 25,  6, 10, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #273
PAIRING(10, 15, 20, 25,  6, 11, 11, 12,   2, 0, 10, 11, 11, 12,  0,  0,  0,  0,   2, 1) // #274
PAIRING(10, 15, 20, 25,  6, 11, 11, 15,   2, 0, 10, 11, 11, 15,  0,  0,  0,  0,   1, 1) // #275
PAIRING(10, 15, 20, 25,  6, 11, 11, 16,   2, 0, 10, 11, 11, 15,  0,  0,  0,  0,   1, 2) // #276
PAIRING(10, 15, 20, 25,  6, 11, 11, 20,   2, 0, 10, 11, 11, 15,  0,  0,  0,  0,   1, 1) // #277
PAIRING(10, 15, 20, 25,  6, 11, 11, 21,   2, 1, 10, 11, 11, 15, 20, 21,  0,  0,   1, 2) // #278
PAIRING(10, 15, 20, 25,  6, 11, 11, 25,   2, 1, 10, 11, 11, 15, 20, 25,  0,  0,   0, 2) // #279
PAIRING(10, 15, 20, 25,  6, 11, 11, 26,   2, 1, 10, 11, 11, 15, 20, 25,  0,  0,   0, 2) // #280
PAIRING(10, 15, 20, 25,  6, 11, 12, 13,   2, 0, 10, 11, 12, 13,  0,  0,  0,  0,   2, 1) // #281
PAIRING(10, 15, 20, 25,  6, 11, 12, 15,   2, 0, 10, 11, 12, 15,  0,  0,  0,  0,   2, 1) // #282
PAIRING(10, 15, 20, 25,  6, 11, 12, 16,   2, 0, 10, 11, 12, 15,  0,  0,  0,  0,   2, 2) // #283
PAIRING(10, 15, 20, 25,  6, 11, 12, 20,   2, 0, 10, 11, 12, 15,  0,  0,  0,  0,   2, 1) // #284
PAIRING(10, 15, 20, 25,  6, 11, 12, 21,   2, 1, 10, 11, 12, 15, 20, 21,  0,  0,   2, 2) // #285
PAIRING(10, 15, 20, 25,  6, 11, 12, 25,   2, 1, 10, 11, 12, 15, 20, 25,  0,  0,   1, 2) // #286
PAIRING(10, 15, 20, 25,  6, 11, 12, 26,   2, 1, 10, 11, 12, 15, 20, 25,  0,  0,   1, 2) // #287
PAIRING(10, 15, 20, 25,  6, 11, 15, 16,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #288
PAIRING(10, 15, 20, 25,  6, 11, 15, 20,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #289
PAIRING(10, 15, 20, 25,  6, 11, 15, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 2) // #290
PAIRING(10, 15, 20, 25,  6, 11, 15, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 2) // #291
PAIRING(10, 15, 20, 25,  6, 11, 15, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 2) // #292
PAIRING(10, 15, 20, 25,  6, 11, 16, 17,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #293
PAIRING(10, 15, 20, 25,  6, 11, 16, 20,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #294
PAIRING(10, 15, 20, 25,  6, 11, 16, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 2) // #295
PAIRING(10, 15, 20, 25,  6, 11, 16, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 2) // #296
PAIRING(10, 15, 20, 25,  6, 11, 16, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 2) // #297
PAIRING(10, 15, 20, 25,  6, 11, 20, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 1) // #298
PAIRING(10, 15, 20, 25,  6, 11, 20, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #299
PAIRING(10, 15, 20, 25,  6, 11, 20, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 2) // #300
PAIRING(10, 15, 20, 25,  6, 11, 21, 22,   1, 1, 10, 11,  0,  0, 21, 22,  0,  0,   2, 1) // #301
PAIRING(10, 15, 20, 25,  6, 11, 21, 25,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 1) // #302
PAIRING(10, 15, 20, 25,  6, 11, 21, 26,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 2) // #303
PAIRING(10, 15, 20, 25,  6, 11, 25, 26,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #304
PAIRING(10, 15, 20, 25,  6, 11, 26, 27,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 2) // #305
PAIRING(10, 15, 20, 25,  6, 15, 15, 16,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #306
PAIRING(10, 15, 20, 25,  6, 15, 15, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #307
PAIRING(10, 15, 20, 25,  6, 15, 15, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #308
PAIRING(10, 15, 20, 25,  6, 15, 15, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #309
PAIRING(10, 15, 20, 25,  6, 15, 15, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #310
PAIRING(10, 15, 20, 25,  6, 15, 16, 17,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #311
PAIRING(10, 15, 20, 25,  6, 15, 16, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #312
PAIRING(10, 15, 20, 25,  6, 15, 16, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #313
PAIRING(10, 15, 20, 25,  6, 15, 16, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #314
PAIRING(10, 15, 20, 25,  6, 15, 16, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #315
PAIRING(10, 15, 20, 25,  6, 15, 20, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 1) // #316
PAIRING(10, 15, 20, 25,  6, 15, 20, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #317
PAIRING(10, 15, 20, 25,  6, 15, 20, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #318
PAIRING(10, 15, 20, 25,  6, 15, 21, 22,   1, 1, 10, 15,  0,  0, 21, 22,  0,  0,   1, 1) // #319
PAIRING(10, 15, 20, 25,  6, 15, 21, 25,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 1) // #320
PAIRING(10, 15, 20, 25,  6, 15, 21, 26,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 2) // #321
PAIRING(10, 15, 20, 25,  6, 15, 25, 26,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #322
PAIRING(10, 15, 20, 25,  6, 15, 26, 27,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #323
PAIRING(10, 15, 20, 25,  6, 16, 16, 17,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #324
PAIRING(10, 15, 20, 25,  6, 16, 16, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #325
PAIRING(10, 15, 20, 25,  6, 16, 16, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #326
PAIRING(10, 15, 20, 25,  6, 16, 16, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #327
PAIRING(10, 15, 20, 25,  6, 16, 16, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #328
PAIRING(10, 15, 20, 25,  6, 16, 17, 18,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #329
PAIRING(10, 15, 20, 25,  6, 16, 17, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #330
PAIRING(10, 15, 20, 25,  6, 16, 17, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #331
PAIRING(10, 15, 20, 25,  6, 16, 17, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #332
PAIRING(10, 15, 20, 25,  6, 16, 17, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #333
PAIRING(10, 15, 20, 25,  6, 16, 20, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 1) // #334
PAIRING(10, 15, 20, 25,  6, 16, 20, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #335
PAIRING(10, 15, 20, 25,  6, 16, 20, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #336
PAIRING(10, 15, 20, 25,  6, 16, 21, 22,   1, 1, 10, 15,  0,  0, 21, 22,  0,  0,   1, 1) // #337
PAIRING(10, 15, 20, 25,  6, 16, 21, 25,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 1) // #338
PAIRING(10, 15, 20, 25,  6, 16, 21, 26,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 2) // #339
PAIRING(10, 15, 20, 25,  6, 16, 25, 26,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #340
PAIRING(10, 15, 20, 25,  6, 16, 26, 27,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #341
PAIRING(10, 15, 20, 25,  6, 20, 20, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 1) // #342
PAIRING(10, 15, 20, 25,  6, 20, 20, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #343
PAIRING(10, 15, 20, 25,  6, 20, 20, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #344
PAIRING(10, 15, 20, 25,  6, 20, 21, 22,   1, 1, 10, 15,  0,  0, 21, 22,  0,  0,   1, 1) // #345
PAIRING(10, 15, 20, 25,  6, 20, 21, 25,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 1) // #346
PAIRING(10, 15, 20, 25,  6, 20, 21, 26,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 2) // #347
PAIRING(10, 15, 20, 25,  6, 20, 25, 26,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #348
PAIRING(10, 15, 20, 25,  6, 20, 26, 27,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #349
PAIRING(10, 15, 20, 25,  6, 21, 21, 22,   1, 2, 10, 15,  0,  0, 20, 21, 21, 22,   1, 1) // #350
PAIRING(10, 15, 20, 25,  6, 21, 21, 25,   1, 2, 10, 15,  0,  0, 20, 21, 21, 25,   0, 1) // #351
PAIRING(10, 15, 20, 25,  6, 21, 21, 26,   1, 2, 10, 15,  0,  0, 20, 21, 21, 25,   0, 2) // #352
PAIRING(10, 15, 20, 25,  6, 21, 22, 23,   1, 2, 10, 15,  0,  0, 20, 21, 22, 23,   1, 1) // #353
PAIRING(10, 15, 20, 25,  6, 21, 22, 25,   1, 2, 10, 15,  0,  0, 20, 21, 22, 25,   1, 1) // #354
PAIRING(10, 15, 20, 25,  6, 21, 22, 26,   1, 2, 10, 15,  0,  0, 20, 21, 22, 25,   1, 2) // #355
PAIRING(10, 15, 20, 25,  6, 21, 25, 26,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #356
PAIRING(10, 15, 20, 25,  6, 21, 26, 27,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #357
PAIRING(10, 15, 20, 25,  6, 25, 25, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #358
PAIRING(10, 15, 20, 25,  6, 25, 26, 27,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #359
PAIRING(10, 15, 20, 25,  6, 26, 26, 27,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #360
PAIRING(10, 15, 20, 25,  6, 26, 27, 28,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #361
PAIRING(10, 15, 20, 25, 10, 11, 11, 12,   2, 0, 10, 11, 11, 12,  0,  0,  0,  0,   2, 0) // #362
PAIRING(10, 15, 20, 25, 10, 11, 11, 15,   2, 0, 10, 11, 11, 15,  0,  0,  0,  0,   1, 0) // #363
PAIRING(10, 15, 20, 25, 10, 11, 11, 16,   2, 0, 10, 11, 11, 15,  0,  0,  0,  0,   1, 1) // #364
PAIRING(10, 15, 20, 25, 10, 11, 11, 20,   2, 0, 10, 11, 11, 15,  0,  0,  0,  0,   1, 0) // #365
PAIRING(10, 15, 20, 25, 10, 11, 11, 21,   2, 1, 10, 11, 11, 15, 20, 21,  0,  0,   1, 1) // #366
PAIRING(10, 15, 20, 25, 10, 11, 11, 25,   2, 1, 10, 11, 11, 15, 20, 25,  0,  0,   0, 1) // #367
PAIRING(10, 15, 20, 25, 10, 11, 11, 26,   2, 1, 10, 11, 11, 15, 20, 25,  0,  0,   0, 1) // #368
PAIRING(10, 15, 20, 25, 10, 11, 12, 13,   2, 0, 10, 11, 12, 13,  0,  0,  0,  0,   2, 0) // #369
PAIRING(10, 15, 20, 25, 10, 11, 12, 15,   2, 0, 10, 11, 12, 15,  0,  0,  0,  0,   2, 0) // #370
PAIRING(10, 15, 20, 25, 10, 11, 12, 16,   2, 0, 10, 11, 12, 15,  0,  0,  0,  0,   2, 1) // #371
PAIRING(10, 15, 20, 25, 10, 11, 12, 20,   2, 0, 10, 11, 12, 15,  0,  0,  0,  0,   2, 0) // #372
PAIRING(10, 15, 20, 25, 10, 11, 12, 21,   2, 1, 10, 11, 12, 15, 20, 21,  0,  0,   2, 1) // #373
PAIRING(10, 15, 20, 25, 10, 11, 12, 25,   2, 1, 10, 11, 12, 15, 20, 25,  0,  0,   1, 1) // #374
PAIRING(10, 15, 20, 25, 10, 11, 12, 26,   2, 1, 10, 11, 12, 15, 20, 25,  0,  0,   1, 1) // #375
PAIRING(10, 15, 20, 25, 10, 11, 15, 16,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #376
PAIRING(10, 15, 20, 25, 10, 11, 15, 20,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #377
PAIRING(10, 15, 20, 25, 10, 11, 15, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 1) // #378
PAIRING(10, 15, 20, 25, 10, 11, 15, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #379
PAIRING(10, 15, 20, 25, 10, 11, 15, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #380
PAIRING(10, 15, 20, 25, 10, 11, 16, 17,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #381
PAIRING(10, 15, 20, 25, 10, 11, 16, 20,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #382
PAIRING(10, 15, 20, 25, 10, 11, 16, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 1) // #383
PAIRING(10, 15, 20, 25, 10, 11, 16, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #384
PAIRING(10, 15, 20, 25, 10, 11, 16, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #385
PAIRING(10, 15, 20, 25, 10, 11, 20, 21,   1, 1, 10, 11,  0,  0, 20, 21,  0,  0,   2, 0) // #386
PAIRING(10, 15, 20, 25, 10, 11, 20, 25,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 0) // #387
PAIRING(10, 15, 20, 25, 10, 11, 20, 26,   1, 1, 10, 11,  0,  0, 20, 25,  0,  0,   1, 1) // #388
PAIRING(10, 15, 20, 25, 10, 11, 21, 22,   1, 1, 10, 11,  0,  0, 21, 22,  0,  0,   2, 0) // #389
PAIRING(10, 15, 20, 25, 10, 11, 21, 25,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 0) // #390
PAIRING(10, 15, 20, 25, 10, 11, 21, 26,   1, 1, 10, 11,  0,  0, 21, 25,  0,  0,   2, 1) // #391
PAIRING(10, 15, 20, 25, 10, 11, 25, 26,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #392
PAIRING(10, 15, 20, 25, 10, 11, 26, 27,   1, 0, 10, 11,  0,  0,  0,  0,  0,  0,   2, 1) // #393
PAIRING(10, 15, 20, 25, 10, 15, 15, 16,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #394
PAIRING(10, 15, 20, 25, 10, 15, 15, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #395
PAIRING(10, 15, 20, 25, 10, 15, 15, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 1) // #396
PAIRING(10, 15, 20, 25, 10, 15, 15, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #397
PAIRING(10, 15, 20, 25, 10, 15, 15, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #398
PAIRING(10, 15, 20, 25, 10, 15, 16, 17,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #399
PAIRING(10, 15, 20, 25, 10, 15, 16, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #400
PAIRING(10, 15, 20, 25, 10, 15, 16, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 1) // #401
PAIRING(10, 15, 20, 25, 10, 15, 16, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #402
PAIRING(10, 15, 20, 25, 10, 15, 16, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #403
PAIRING(10, 15, 20, 25, 10, 15, 20, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 0) // #404
PAIRING(10, 15, 20, 25, 10, 15, 20, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 0) // #405
PAIRING(10, 15, 20, 25, 10, 15, 20, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #406
PAIRING(10, 15, 20, 25, 10, 15, 21, 22,   1, 1, 10, 15,  0,  0, 21, 22,  0,  0,   1, 0) // #407
PAIRING(10, 15, 20, 25, 10, 15, 21, 25,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 0) // #408
PAIRING(10, 15, 20, 25, 10, 15, 21, 26,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 1) // #409
PAIRING(10, 15, 20, 25, 10, 15, 25, 26,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #410
PAIRING(10, 15, 20, 25, 10, 15, 26, 27,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #411
PAIRING(10, 15, 20, 25, 10, 16, 16, 17,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #412
PAIRING(10, 15, 20, 25, 10, 16, 16, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #413
PAIRING(10, 15, 20, 25, 10, 16, 16, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #414
PAIRING(10, 15, 20, 25, 10, 16, 16, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #415
PAIRING(10, 15, 20, 25, 10, 16, 16, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #416
PAIRING(10, 15, 20, 25, 10, 16, 17, 18,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #417
PAIRING(10, 15, 20, 25, 10, 16, 17, 20,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #418
PAIRING(10, 15, 20, 25, 10, 16, 17, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #419
PAIRING(10, 15, 20, 25, 10, 16, 17, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #420
PAIRING(10, 15, 20, 25, 10, 16, 17, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #421
PAIRING(10, 15, 20, 25, 10, 16, 20, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 1) // #422
PAIRING(10, 15, 20, 25, 10, 16, 20, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #423
PAIRING(10, 15, 20, 25, 10, 16, 20, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #424
PAIRING(10, 15, 20, 25, 10, 16, 21, 22,   1, 1, 10, 15,  0,  0, 21, 22,  0,  0,   1, 1) // #425
PAIRING(10, 15, 20, 25, 10, 16, 21, 25,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 1) // #426
PAIRING(10, 15, 20, 25, 10, 16, 21, 26,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 2) // #427
PAIRING(10, 15, 20, 25, 10, 16, 25, 26,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #428
PAIRING(10, 15, 20, 25, 10, 16, 26, 27,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 2) // #429
PAIRING(10, 15, 20, 25, 10, 20, 20, 21,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 0) // #430
PAIRING(10, 15, 20, 25, 10, 20, 20, 25,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 0) // #431
PAIRING(10, 15, 20, 25, 10, 20, 20, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 1) // #432
PAIRING(10, 15, 20, 25, 10, 20, 21, 22,   1, 1, 10, 15,  0,  0, 21, 22,  0,  0,   1, 0) // #433
PAIRING(10, 15, 20, 25, 10, 20, 21, 25,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 0) // #434
PAIRING(10, 15, 20, 25, 10, 20, 21, 26,   1, 1, 10, 15,  0,  0, 21, 25,  0,  0,   1, 1) // #435
PAIRING(10, 15, 20, 25, 10, 20, 25, 26,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #436
PAIRING(10, 15, 20, 25, 10, 20, 26, 27,   1, 0, 10, 15,  0,  0,  0,  0,  0,  0,   1, 1) // #437
PAIRING(10, 15, 20, 25, 10, 21, 21, 22,   1, 2, 10, 15,  0,  0, 20, 21, 21, 22,   1, 1) // #438
PAIRING(10, 15, 20, 25, 10, 21, 21, 25,   1, 2, 10, 15,  0,  0, 20, 21, 21, 25,   0, 1) // #439
PAIRING(10, 15, 20, 25, 10, 21, 21, 26,   1, 2, 10, 15,  0,  0, 20, 21, 21, 25,   0, 2) // #440
PAIRING(10, 15, 20, 25, 10, 21, 22, 23,   1, 2, 10, 15,  0,  0, 20, 21, 22, 23,   1, 1) // #441
PAIRING(10, 15, 20, 25, 10, 21, 22, 25,   1, 2, 10, 15,  0,  0, 20, 21, 22, 25,   1, 1) // #442
PAIRING(10, 15, 20, 25, 10, 21, 22, 26,   1, 2, 10, 15,  0,  0, 20, 21, 22, 25,   1, 2) // #443
PAIRING(10, 15, 20, 25, 10, 21, 25, 26,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #444
PAIRING(10, 15, 20, 25, 10, 21, 26, 27,   1, 1, 10, 15,  0,  0, 20, 21,  0,  0,   1, 2) // #445
PAIRING(10, 15, 20, 25, 10, 25, 25, 26,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #446
PAIRING(10, 15, 20, 25, 10, 25, 26, 27,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #447
PAIRING(10, 15, 20, 25, 10, 26, 26, 27,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #448
PAIRING(10, 15, 20, 25, 10, 26, 27, 28,   1, 1, 10, 15,  0,  0, 20, 25,  0,  0,   0, 2) // #449
PAIRING(10, 15, 20, 25, 11, 12, 12, 13,   2, 0, 11, 12, 12, 13,  0,  0,  0,  0,   2, 0) // #450
PAIRING(10, 15, 20, 25, 11, 12, 12, 15,   2, 0, 11, 12, 12, 15,  0,  0,  0,  0,   2, 0) // #451
PAIRING(10, 15, 20, 25, 11, 12, 12, 16,   2, 0, 11, 12, 12, 15,  0,  0,  0,  0,   2, 1) // #452
PAIRING(10, 15, 20, 25, 11, 12, 12, 20,   2, 0, 11, 12, 12, 15,  0,  0,  0,  0,   2, 0) // #453
PAIRING(10, 15, 20, 25, 11, 12, 12, 21,   2, 1, 11, 12, 12, 15, 20, 21,  0,  0,   2, 1) // #454
PAIRING(10, 15, 20, 25, 11, 12, 12, 25,   2, 1, 11, 12, 12, 15, 20, 25,  0,  0,   1, 1) // #455
PAIRING(10, 15, 20, 25, 11, 12, 12, 26,   2, 1, 11, 12, 12, 15, 20, 25,  0,  0,   1, 1) // #456
PAIRING(10, 15, 20, 25, 11, 12, 13, 14,   2, 0, 11, 12, 13, 14,  0,  0,  0,  0,   2, 0) // #457
PAIRING(10, 15, 20, 25, 11, 12, 13, 15,   2, 0, 11, 12, 13, 15,  0,  0,  0,  0,   2, 0) // #458
PAIRING(10, 15, 20, 25, 11, 12, 13, 16,   2, 0, 11, 12, 13, 15,  0,  0,  0,  0,   2, 1) // #459
PAIRING(10, 15, 20, 25, 11, 12, 13, 20,   2, 0, 11, 12, 13, 15,  0,  0,  0,  0,   2, 0) // #460
PAIRING(10, 15, 20, 25, 11, 12, 13, 21,   2, 1, 11, 12, 13, 15, 20, 21,  0,  0,   2, 1) // #461
PAIRING(10, 15, 20, 25, 11, 12, 13, 25,   2, 1, 11, 12, 13, 15, 20, 25,  0,  0,   1, 1) // #462
PAIRING(10, 15, 20, 25, 11, 12, 13, 26,   2, 1, 11, 12, 13, 15, 20, 25,  0,  0,   1, 1) // #463
PAIRING(10, 15, 20, 25, 11, 12, 15, 16,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #464
PAIRING(10, 15, 20, 25, 11, 12, 15, 20,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #465
PAIRING(10, 15, 20, 25, 11, 12, 15, 21,   1, 1, 11, 12,  0,  0, 20, 21,  0,  0,   2, 1) // #466
PAIRING(10, 15, 20, 25, 11, 12, 15, 25,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 1) // #467
PAIRING(10, 15, 20, 25, 11, 12, 15, 26,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 1) // #468
PAIRING(10, 15, 20, 25, 11, 12, 16, 17,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #469
PAIRING(10, 15, 20, 25, 11, 12, 16, 20,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #470
PAIRING(10, 15, 20, 25, 11, 12, 16, 21,   1, 1, 11, 12,  0,  0, 20, 21,  0,  0,   2, 1) // #471
PAIRING(10, 15, 20, 25, 11, 12, 16, 25,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 1) // #472
PAIRING(10, 15, 20, 25, 11, 12, 16, 26,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 1) // #473
PAIRING(10, 15, 20, 25, 11, 12, 20, 21,   1, 1, 11, 12,  0,  0, 20, 21,  0,  0,   2, 0) // #474
PAIRING(10, 15, 20, 25, 11, 12, 20, 25,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 0) // #475
PAIRING(10, 15, 20, 25, 11, 12, 20, 26,   1, 1, 11, 12,  0,  0, 20, 25,  0,  0,   1, 1) // #476
PAIRING(10, 15, 20, 25, 11, 12, 21, 22,   1, 1, 11, 12,  0,  0, 21, 22,  0,  0,   2, 0) // #477
PAIRING(10, 15, 20, 25, 11, 12, 21, 25,   1, 1, 11, 12,  0,  0, 21, 25,  0,  0,   2, 0) // #478
PAIRING(10, 15, 20, 25, 11, 12, 21, 26,   1, 1, 11, 12,  0,  0, 21, 25,  0,  0,   2, 1) // #479
PAIRING(10, 15, 20, 25, 11, 12, 25, 26,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #480
PAIRING(10, 15, 20, 25, 11, 12, 26, 27,   1, 0, 11, 12,  0,  0,  0,  0,  0,  0,   2, 1) // #481
PAIRING(10, 15, 20, 25, 11, 15, 15, 16,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #482
PAIRING(10, 15, 20, 25, 11, 15, 15, 20,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #483
PAIRING(10, 15, 20, 25, 11, 15, 15, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 1) // #484
PAIRING(10, 15, 20, 25, 11, 15, 15, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 1) // #485
PAIRING(10, 15, 20, 25, 11, 15, 15, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 1) // #486
PAIRING(10, 15, 20, 25, 11, 15, 16, 17,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #487
PAIRING(10, 15, 20, 25, 11, 15, 16, 20,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #488
PAIRING(10, 15, 20, 25, 11, 15, 16, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 1) // #489
PAIRING(10, 15, 20, 25, 11, 15, 16, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 1) // #490
PAIRING(10, 15, 20, 25, 11, 15, 16, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 1) // #491
PAIRING(10, 15, 20, 25, 11, 15, 20, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 0) // #492
PAIRING(10, 15, 20, 25, 11, 15, 20, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 0) // #493
PAIRING(10, 15, 20, 25, 11, 15, 20, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 1) // #494
PAIRING(10, 15, 20, 25, 11, 15, 21, 22,   1, 1, 11, 15,  0,  0, 21, 22,  0,  0,   2, 0) // #495
PAIRING(10, 15, 20, 25, 11, 15, 21, 25,   1, 1, 11, 15,  0,  0, 21, 25,  0,  0,   2, 0) // #496
PAIRING(10, 15, 20, 25, 11, 15, 21, 26,   1, 1, 11, 15,  0,  0, 21, 25,  0,  0,   2, 1) // #497
PAIRING(10, 15, 20, 25, 11, 15, 25, 26,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #498
PAIRING(10, 15, 20, 25, 11, 15, 26, 27,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #499
PAIRING(10, 15, 20, 25, 11, 16, 16, 17,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #500
PAIRING(10, 15, 20, 25, 11, 16, 16, 20,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #501
PAIRING(10, 15, 20, 25, 11, 16, 16, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 2) // #502
PAIRING(10, 15, 20, 25, 11, 16, 16, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #503
PAIRING(10, 15, 20, 25, 11, 16, 16, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #504
PAIRING(10, 15, 20, 25, 11, 16, 17, 18,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #505
PAIRING(10, 15, 20, 25, 11, 16, 17, 20,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #506
PAIRING(10, 15, 20, 25, 11, 16, 17, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 2) // #507
PAIRING(10, 15, 20, 25, 11, 16, 17, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #508
PAIRING(10, 15, 20, 25, 11, 16, 17, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #509
PAIRING(10, 15, 20, 25, 11, 16, 20, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 1) // #510
PAIRING(10, 15, 20, 25, 11, 16, 20, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 1) // #511
PAIRING(10, 15, 20, 25, 11, 16, 20, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #512
PAIRING(10, 15, 20, 25, 11, 16, 21, 22,   1, 1, 11, 15,  0,  0, 21, 22,  0,  0,   2, 1) // #513
PAIRING(10, 15, 20, 25, 11, 16, 21, 25,   1, 1, 11, 15,  0,  0, 21, 25,  0,  0,   2, 1) // #514
PAIRING(10, 15, 20, 25, 11, 16, 21, 26,   1, 1, 11, 15,  0,  0, 21, 25,  0,  0,   2, 2) // #515
PAIRING(10, 15, 20, 25, 11, 16, 25, 26,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #516
PAIRING(10, 15, 20, 25, 11, 16, 26, 27,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 2) // #517
PAIRING(10, 15, 20, 25, 11, 20, 20, 21,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 0) // #518
PAIRING(10, 15, 20, 25, 11, 20, 20, 25,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 0) // #519
PAIRING(10, 15, 20, 25, 11, 20, 20, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 1) // #520
PAIRING(10, 15, 20, 25, 11, 20, 21, 22,   1, 1, 11, 15,  0,  0, 21, 22,  0,  0,   2, 0) // #521
PAIRING(10, 15, 20, 25, 11, 20, 21, 25,   1, 1, 11, 15,  0,  0, 21, 25,  0,  0,   2, 0) // #522
PAIRING(10, 15, 20, 25, 11, 20, 21, 26,   1, 1, 11, 15,  0,  0, 21, 25,  0,  0,   2, 1) // #523
PAIRING(10, 15, 20, 25, 11, 20, 25, 26,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #524
PAIRING(10, 15, 20, 25, 11, 20, 26, 27,   1, 0, 11, 15,  0,  0,  0,  0,  0,  0,   2, 1) // #525
PAIRING(10, 15, 20, 25, 11, 21, 21, 22,   1, 2, 11, 15,  0,  0, 20, 21, 21, 22,   2, 1) // #526
PAIRING(10, 15, 20, 25, 11, 21, 21, 25,   1, 2, 11, 15,  0,  0, 20, 21, 21, 25,   1, 1) // #527
PAIRING(10, 15, 20, 25, 11, 21, 21, 26,   1, 2, 11, 15,  0,  0, 20, 21, 21, 25,   1, 2) // #528
PAIRING(10, 15, 20, 25, 11, 21, 22, 23,   1, 2, 11, 15,  0,  0, 20, 21, 22, 23,   2, 1) // #529
PAIRING(10, 15, 20, 25, 11, 21, 22, 25,   1, 2, 11, 15,  0,  0, 20, 21, 22, 25,   2, 1) // #530
PAIRING(10, 15, 20, 25, 11, 21, 22, 26,   1, 2, 11, 15,  0,  0, 20, 21, 22, 25,   2, 2) // #531
PAIRING(10, 15, 20, 25, 11, 21, 25, 26,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 2) // #532
PAIRING(10, 15, 20, 25, 11, 21, 26, 27,   1, 1, 11, 15,  0,  0, 20, 21,  0,  0,   2, 2) // #533
PAIRING(10, 15, 20, 25, 11, 25, 25, 26,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #534
PAIRING(10, 15, 20, 25, 11, 25, 26, 27,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #535
PAIRING(10, 15, 20, 25, 11, 26, 26, 27,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #536
PAIRING(10, 15, 20, 25, 11, 26, 27, 28,   1, 1, 11, 15,  0,  0, 20, 25,  0,  0,   1, 2) // #537
PAIRING(10, 15, 20, 25, 15, 16, 16, 17,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #538
PAIRING(10, 15, 20, 25, 15, 16, 16, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #539
PAIRING(10, 15, 20, 25, 15, 16, 16, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #540
PAIRING(10, 15, 20, 25, 15, 16, 16, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #541
PAIRING(10, 15, 20, 25, 15, 16, 16, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #542
PAIRING(10, 15, 20, 25, 15, 16, 17, 18,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #543
PAIRING(10, 15, 20, 25, 15, 16, 17, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #544
PAIRING(10, 15, 20, 25, 15, 16, 17, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #545
PAIRING(10, 15, 20, 25, 15, 16, 17, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #546
PAIRING(10, 15, 20, 25, 15, 16, 17, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #547
PAIRING(10, 15, 20, 25, 15, 16, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #548
PAIRING(10, 15, 20, 25, 15, 16, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #549
PAIRING(10, 15, 20, 25, 15, 16, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #550
PAIRING(10, 15, 20, 25, 15, 16, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #551
PAIRING(10, 15, 20, 25, 15, 16, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #552
PAIRING(10, 15, 20, 25, 15, 16, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #553
PAIRING(10, 15, 20, 25, 15, 16, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #554
PAIRING(10, 15, 20, 25, 15, 16, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #555
PAIRING(10, 15, 20, 25, 15, 20, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #556
PAIRING(10, 15, 20, 25, 15, 20, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #557
PAIRING(10, 15, 20, 25, 15, 20, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #558
PAIRING(10, 15, 20, 25, 15, 20, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #559
PAIRING(10, 15, 20, 25, 15, 20, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #560
PAIRING(10, 15, 20, 25, 15, 20, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #561
PAIRING(10, 15, 20, 25, 15, 20, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #562
PAIRING(10, 15, 20, 25, 15, 20, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #563
PAIRING(10, 15, 20, 25, 15, 21, 21, 22,   0, 2,  0,  0,  0,  0, 20, 21, 21, 22,   2, 1) // #564
PAIRING(10, 15, 20, 25, 15, 21, 21, 25,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 1) // #565
PAIRING(10, 15, 20, 25, 15, 21, 21, 26,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 2) // #566
PAIRING(10, 15, 20, 25, 15, 21, 22, 23,   0, 2,  0,  0,  0,  0, 20, 21, 22, 23,   2, 1) // #567
PAIRING(10, 15, 20, 25, 15, 21, 22, 25,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 1) // #568
PAIRING(10, 15, 20, 25, 15, 21, 22, 26,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 2) // #569
PAIRING(10, 15, 20, 25, 15, 21, 25, 26,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #570
PAIRING(10, 15, 20, 25, 15, 21, 26, 27,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #571
PAIRING(10, 15, 20, 25, 15, 25, 25, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #572
PAIRING(10, 15, 20, 25, 15, 25, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #573
PAIRING(10, 15, 20, 25, 15, 26, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #574
PAIRING(10, 15, 20, 25, 15, 26, 27, 28,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #575
PAIRING(10, 15, 20, 25, 16, 17, 17, 18,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #576
PAIRING(10, 15, 20, 25, 16, 17, 17, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #577
PAIRING(10, 15, 20, 25, 16, 17, 17, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #578
PAIRING(10, 15, 20, 25, 16, 17, 17, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #579
PAIRING(10, 15, 20, 25, 16, 17, 17, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #580
PAIRING(10, 15, 20, 25, 16, 17, 18, 19,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #581
PAIRING(10, 15, 20, 25, 16, 17, 18, 20,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #582
PAIRING(10, 15, 20, 25, 16, 17, 18, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #583
PAIRING(10, 15, 20, 25, 16, 17, 18, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #584
PAIRING(10, 15, 20, 25, 16, 17, 18, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #585
PAIRING(10, 15, 20, 25, 16, 17, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #586
PAIRING(10, 15, 20, 25, 16, 17, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #587
PAIRING(10, 15, 20, 25, 16, 17, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #588
PAIRING(10, 15, 20, 25, 16, 17, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #589
PAIRING(10, 15, 20, 25, 16, 17, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #590
PAIRING(10, 15, 20, 25, 16, 17, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #591
PAIRING(10, 15, 20, 25, 16, 17, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #592
PAIRING(10, 15, 20, 25, 16, 17, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #593
PAIRING(10, 15, 20, 25, 16, 20, 20, 21,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #594
PAIRING(10, 15, 20, 25, 16, 20, 20, 25,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #595
PAIRING(10, 15, 20, 25, 16, 20, 20, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #596
PAIRING(10, 15, 20, 25, 16, 20, 21, 22,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #597
PAIRING(10, 15, 20, 25, 16, 20, 21, 25,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #598
PAIRING(10, 15, 20, 25, 16, 20, 21, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #599
PAIRING(10, 15, 20, 25, 16, 20, 25, 26,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #600
PAIRING(10, 15, 20, 25, 16, 20, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #601
PAIRING(10, 15, 20, 25, 16, 21, 21, 22,   0, 2,  0,  0,  0,  0, 20, 21, 21, 22,   2, 1) // #602
PAIRING(10, 15, 20, 25, 16, 21, 21, 25,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 1) // #603
PAIRING(10, 15, 20, 25, 16, 21, 21, 26,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 2) // #604
PAIRING(10, 15, 20, 25, 16, 21, 22, 23,   0, 2,  0,  0,  0,  0, 20, 21, 22, 23,   2, 1) // #605
PAIRING(10, 15, 20, 25, 16, 21, 22, 25,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 1) // #606
PAIRING(10, 15, 20, 25, 16, 21, 22, 26,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 2) // #607
PAIRING(10, 15, 20, 25, 16, 21, 25, 26,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #608
PAIRING(10, 15, 20, 25, 16, 21, 26, 27,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 2) // #609
PAIRING(10, 15, 20, 25, 16, 25, 25, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #610
PAIRING(10, 15, 20, 25, 16, 25, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #611
PAIRING(10, 15, 20, 25, 16, 26, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #612
PAIRING(10, 15, 20, 25, 16, 26, 27, 28,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #613
PAIRING(10, 15, 20, 25, 20, 21, 21, 22,   0, 2,  0,  0,  0,  0, 20, 21, 21, 22,   2, 0) // #614
PAIRING(10, 15, 20, 25, 20, 21, 21, 25,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 0) // #615
PAIRING(10, 15, 20, 25, 20, 21, 21, 26,   0, 2,  0,  0,  0,  0, 20, 21, 21, 25,   1, 1) // #616
PAIRING(10, 15, 20, 25, 20, 21, 22, 23,   0, 2,  0,  0,  0,  0, 20, 21, 22, 23,   2, 0) // #617
PAIRING(10, 15, 20, 25, 20, 21, 22, 25,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 0) // #618
PAIRING(10, 15, 20, 25, 20, 21, 22, 26,   0, 2,  0,  0,  0,  0, 20, 21, 22, 25,   2, 1) // #619
PAIRING(10, 15, 20, 25, 20, 21, 25, 26,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #620
PAIRING(10, 15, 20, 25, 20, 21, 26, 27,   0, 1,  0,  0,  0,  0, 20, 21,  0,  0,   2, 1) // #621
PAIRING(10, 15, 20, 25, 20, 25, 25, 26,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #622
PAIRING(10, 15, 20, 25, 20, 25, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 1) // #623
PAIRING(10, 15, 20, 25, 20, 26, 26, 27,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #624
PAIRING(10, 15, 20, 25, 20, 26, 27, 28,   0, 1,  0,  0,  0,  0, 20, 25,  0,  0,   1, 2) // #625
PAIRING(10, 15, 20, 25, 21, 22, 22, 23,   0, 2,  0,  0,  0,  0, 21, 22, 22, 23,   2, 0) // #626
PAIRING(10, 15, 20, 25, 21, 22, 22, 25,   0, 2,  0,  0,  0,  0, 21, 22, 22, 25,   2, 0) // #627
PAIRING(10, 15, 20, 25, 21, 22, 22, 26,   0, 2,  0,  0,  0,  0, 21, 22, 22, 25,   2, 1) // #628
PAIRING(10, 15, 20, 25, 21, 22, 23, 24,   0, 2,  0,  0,  0,  0, 21, 22, 23, 24,   2, 0) // #629
PAIRING(10, 15, 20, 25, 21, 22, 23, 25,   0, 2,  0,  0,  0,  0, 21, 22, 23, 25,   2, 0) // #630
PAIRING(10, 15, 20, 25, 21, 22, 23, 26,   0, 2,  0,  0,  0,  0, 21, 22, 23, 25,   2, 1) // #631
PAIRING(10, 15, 20, 25, 21, 22, 25, 26,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #632
PAIRING(10, 15, 20, 25, 21, 22, 26, 27,   0, 1,  0,  0,  0,  0, 21, 22,  0,  0,   2, 1) // #633
PAIRING(10, 15, 20, 25, 21, 25, 25, 26,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #634
PAIRING(10, 15, 20, 25, 21, 25, 26, 27,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 1) // #635
PAIRING(10, 15, 20, 25, 21, 26, 26, 27,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #636
PAIRING(10, 15, 20, 25, 21, 26, 27, 28,   0, 1,  0,  0,  0,  0, 21, 25,  0,  0,   2, 2) // #637
PAIRING(10, 15, 20, 25, 25, 26, 26, 27,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #638
PAIRING(10, 15, 20, 25, 25, 26, 27, 28,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #639
PAIRING(10, 15, 20, 25, 26, 27, 27, 28,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #640
PAIRING(10, 15, 20, 25, 26, 27, 28, 29,   0, 0,  0,  0,  0,  0,  0,  0,  0,  0,   2, 2) // #641
