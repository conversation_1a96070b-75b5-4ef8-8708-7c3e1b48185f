// Copyright 2010 Google LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// process_state_proto.proto: A client proto representation of a process,
// in a fully-digested state.
//
// Derived from earlier struct and class based models of a client-side
// processed minidump found under src/google_breakpad/processor.  The
// file process_state.h  holds the top level representation of this model,
// supported by additional classes.  We've added a proto representation
// to ease serialization and parsing for server-side storage of crash
// reports processed on the client.
//
// Author: Jess Gray

syntax = "proto2";

package google_breakpad;

// A proto representation of a process, in a fully-digested state.
// See src/google_breakpad/processor/process_state.h
message ProcessStateProto {
  // Next value: 14

  // The time-date stamp of the original minidump (time_t format)
  optional int64 time_date_stamp = 1;

  // The time-date stamp when the process was created (time_t format)
  optional int64 process_create_time = 13;

  message Crash {
    // The type of crash.  OS- and possibly CPU- specific.  For example,
    // "EXCEPTION_ACCESS_VIOLATION" (Windows), "EXC_BAD_ACCESS /
    // KERN_INVALID_ADDRESS" (Mac OS X), "SIGSEGV" (other Unix).
    required string reason = 1;

    // If crash_reason implicates memory, the memory address that caused the
    // crash.  For data access errors, this will be the data address that
    // caused the fault.  For code errors, this will be the address of the
    // instruction that caused the fault.
    required int64 address = 2;
  }
  optional Crash crash = 2;


  // If there was an assertion that was hit, a textual representation
  // of that assertion, possibly including the file and line at which
  // it occurred.
  optional string assertion = 3;

  // The index of the thread that requested a dump be written in the
  // threads vector.  If a dump was produced as a result of a crash, this
  // will point to the thread that crashed.  If the dump was produced as
  // by user code without crashing, and the dump contains extended Breakpad
  // information, this will point to the thread that requested the dump.
  optional int32 requesting_thread = 4;

  message Thread {
    // Stack for the given thread
    repeated StackFrame frames = 1;
  }

  // Stacks for each thread (except possibly the exception handler
  // thread) at the time of the crash.
  repeated Thread threads = 5;

  // The modules that were loaded into the process represented by the
  // ProcessState.
  repeated CodeModule modules = 6;

  // System Info: OS and CPU

  // A string identifying the operating system, such as "Windows NT",
  // "Mac OS X", or "Linux".  If the information is present in the dump but
  // its value is unknown, this field will contain a numeric value.  If
  // the information is not present in the dump, this field will be empty.
  optional string os = 7;

   // A short form of the os string, using lowercase letters and no spaces,
  // suitable for use in a filesystem.  Possible values are "windows",
  // "mac", and "linux".  Empty if the information is not present in the dump
  // or if the OS given by the dump is unknown.  The values stored in this
  // field should match those used by MinidumpSystemInfo::GetOS.
  optional string os_short = 8;

  // A string identifying the version of the operating system, such as
  // "5.1.2600 Service Pack 2" or "10.4.8 8L2127".  If the dump does not
  // contain this information, this field will be empty.
  optional string os_version = 9;

  // A string identifying the basic CPU family, such as "x86" or "ppc".
  // If this information is present in the dump but its value is unknown,
  // this field will contain a numeric value.  If the information is not
  // present in the dump, this field will be empty.  The values stored in
  // this field should match those used by MinidumpSystemInfo::GetCPU.
  optional string cpu = 10;

  // A string further identifying the specific CPU, such as
  // "GenuineIntel level 6 model 13 stepping 8".  If the information is not
  // present in the dump, or additional identifying information is not
  // defined for the CPU family, this field will be empty.
  optional string cpu_info = 11;

  // The number of processors in the system.  Will be greater than one for
  // multi-core systems.
  optional int32 cpu_count = 12;

  // Leave the ability to add the raw minidump to this representation
}


// Represents a single frame in a stack  
// See src/google_breakpad/processor/code_module.h
message StackFrame {
  // Next value: 8

  // The program counter location as an absolute virtual address.  For the
  // innermost called frame in a stack, this will be an exact program counter
  // or instruction pointer value.  For all other frames, this will be within
  // the instruction that caused execution to branch to a called function,
  // but may not necessarily point to the exact beginning of that instruction.
  required int64 instruction = 1;

  // The module in which the instruction resides.
  optional CodeModule module = 2;

  // The function name, may be omitted if debug symbols are not available.
  optional string function_name = 3;

  // The start address of the function, may be omitted if debug symbols
  // are not available.
  optional int64 function_base = 4;

  // The source file name, may be omitted if debug symbols are not available.
  optional string source_file_name = 5;

  // The (1-based) source line number, may be omitted if debug symbols are
  // not available.
  optional int32 source_line = 6;

  // The start address of the source line, may be omitted if debug symbols
  // are not available.
  optional int64 source_line_base = 7;
}


// Carries information about code modules that are loaded into a process.
// See src/google_breakpad/processor/code_module.h
message CodeModule {
  // Next value: 8

  // The base address of this code module as it was loaded by the process.
  optional int64 base_address = 1;

  // The size of the code module.
  optional int64 size = 2;

  // The path or file name that the code module was loaded from.
  optional string code_file = 3;

  // An identifying string used to discriminate between multiple versions and
  // builds of the same code module.  This may contain a uuid, timestamp,
  // version number, or any combination of this or other information, in an
  // implementation-defined format.
  optional string code_identifier = 4;

  // The filename containing debugging information associated with the code
  // module.  If debugging information is stored in a file separate from the
  // code module itself (as is the case when .pdb or .dSYM files are used),
  // this will be different from code_file.  If debugging information is
  // stored in the code module itself (possibly prior to stripping), this
  // will be the same as code_file.
  optional string debug_file = 5;

  // An identifying string similar to code_identifier, but identifies a
  // specific version and build of the associated debug file.  This may be
  // the same as code_identifier when the debug_file and code_file are
  // identical or when the same identifier is used to identify distinct
  // debug and code files.
  optional string debug_identifier = 6;

  // A human-readable representation of the code module's version.
  optional string version = 7;
}
