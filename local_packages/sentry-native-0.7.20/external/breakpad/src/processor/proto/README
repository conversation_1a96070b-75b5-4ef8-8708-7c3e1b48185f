If you wish to use these protobufs, you must generate their source files
using  protoc from the protobuf project (https://github.com/google/protobuf).

-----
Troubleshooting for Protobuf:

Install:
If you are getting permission errors install, make sure you are not trying to
install from an NFS.


Running protoc:
protoc: error while loading shared libraries: libprotobuf.so.0: cannot open
shared object file: No such file or directory

The issue is that Ubuntu 8.04 doesn't include /usr/local/lib in
library paths. 

To fix it for your current terminal session, just type in
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/lib 
