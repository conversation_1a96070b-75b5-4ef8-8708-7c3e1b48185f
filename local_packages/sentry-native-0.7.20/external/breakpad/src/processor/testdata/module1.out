MODULE windows x86 111111111111111111111111111111111 module1.pdb
INFO CODE_ID FFFFFFFF module1.exe
FILE 1 file1_1.cc
FILE 2 file1_2.cc
FILE 3 file1_3.cc
FUNC m 1000 c 0 Function1_1
1000 4 44 1
1004 4 45 1
1008 4 46 1
FUNC 1100 8 4 Function1_2
1100 4 65 2
1104 4 66 2
FUNC 1200 100 8 Function1_3
FUNC 1300 100 c Function1_4
FUNC 2000 0 0 Test_Zero_Size_Function_Is_Ignored
2000 4 88 2
PUBLIC m 2800 0 PublicSymbol
FUNC 3000 7000 42 LargeFunction
3000 7000 4098359 3
STACK WIN 4 1000 c 1 0 0 0 0 0 1 $eip 4 + ^ = $esp $ebp 8 + = $ebp $ebp ^ =
STACK WIN 4 1100 8 1 0 0 0 0 0 1 $eip 4 + ^ = $esp $ebp 8 + = $ebp $ebp ^ =
STACK WIN 4 1100 100 1 0 0 0 0 0 1 $eip 4 + ^ = $esp $ebp 8 + = $ebp $ebp ^ =
STACK WIN 4 1300 100 1 0 0 0 0 0 1 $eip 4 + ^ = $esp $ebp 8 + = $ebp $ebp ^ =
STACK CFI INIT 3d40 af .cfa: $esp 4 + .ra: .cfa 4 - ^
STACK CFI 3d41 .cfa: $esp 8 + 
STACK CFI 3d43 .cfa: $ebp 8 + $ebp: .cfa 8 - ^
STACK CFI 3d54 $ebx: .cfa 20 - ^
STACK CFI 3d5a $esi: .cfa 16 - ^
STACK CFI 3d84 $edi: .cfa 12 - ^
