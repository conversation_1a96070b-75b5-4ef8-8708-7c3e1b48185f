MODULE windows x86 222222222 module2.pdb
FILE 1 file2_1.cc
FILE 2 file2_2.cc
FILE 3 file2_3.cc
FUNC 2000 c 4 Function2_1
1000 4 54 1
1004 4 55 1
1008 4 56 1
PUBLIC 2160 0 Public2_1
FUNC 2170 14 4 Function2_2
2170 6 10 2
2176 4 12 2
217a 6 13 2
2180 4 21 2
PUBLIC 21a0 0 Public2_2
STACK WIN 4 2000 c 1 0 0 0 0 0 1 $eip 4 + ^ = $esp $ebp 8 + = $ebp $ebp ^ =
STACK WIN 4 2170 14 1 0 0 0 0 0 1 $eip 4 + ^ = $esp $ebp 8 + = $ebp $ebp ^ =
STACK CFI INIT 3df0 af .cfa: $esp 4 + .ra: .cfa 4 - ^
STACK CFI 3df1 .cfa: $esp 8 +
STACK CFI 3df3 .cfa: $ebp 8 + $ebp: .cfa 8 - ^
STACK CFI 3e04 $ebx: .cfa 20 - ^
STACK CFI 3e0a $esi: .cfa 16 - ^
STACK CFI 3e34 $edi: .cfa 12 - ^
