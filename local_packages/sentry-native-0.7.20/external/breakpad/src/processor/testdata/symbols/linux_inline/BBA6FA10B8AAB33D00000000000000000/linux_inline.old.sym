MODULE Linux x86_64 BBA6FA10B8AAB33D00000000000000000 linux_inline
INFO CODE_ID 10FAA6BBAAB83DB3
FILE 0 linux_inline.cpp
INLINE_ORIGIN 0 0 bar()
INLINE_ORIGIN 1 0 foo()
INLINE_ORIGIN 2 0 func()
FUNC 15b30 6cf 0 main
INLINE 0 42 1 15b45 6b1
INLINE 1 39 0 15b72 684
INLINE 2 32 2 15b83 673
15b30 15 41 0
15b45 11 36 0
15b56 a 37 0
15b60 6 37 0
15b66 5 38 0
15b6b 7 0 0
15b72 11 31 0
15b83 a 9 0
15b8d 4 9 0
15b91 6 9 0
15b97 7 0 0
15b9e 11 10 0
15baf 7 0 0
15bb6 2e 12 0
15be4 7 0 0
15beb 5 12 0
15bf0 1d 13 0
15c0d 1d 14 0
15c2a e 0 0
15c38 1c 15 0
15c54 a 16 0
15c5e 7 0 0
15c65 2c 16 0
15c91 15 0 0
15ca6 a 16 0
15cb0 87 15 0
15d37 7 0 0
15d3e 33 15 0
15d71 7 0 0
15d78 24 15 0
15d9c a 17 0
15da6 e 0 0
15db4 a 18 0
15dbe e 0 0
15dcc a 19 0
15dd6 7 0 0
15ddd a 20 0
15de7 7 0 0
15dee 2c 21 0
15e1a 3c 22 0
15e56 28 23 0
15e7e 5a 18 0
15ed8 d 28 0
15ee5 11 12 0
15ef6 67 28 0
15f5d 2b 15 0
15f88 7 0 0
15f8f 8c 15 0
1601b 7 0 0
16022 3d 15 0
1605f 67 28 0
160c6 54 18 0
1611a 3c 28 0
16156 c 12 0
16162 54 18 0
161b6 2 27 0
161b8 3e 28 0
161f6 9 43 0
