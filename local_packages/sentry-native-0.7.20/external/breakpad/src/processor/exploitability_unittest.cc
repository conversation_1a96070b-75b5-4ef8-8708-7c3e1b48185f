// Copyright 2010 Google LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#ifdef HAVE_CONFIG_H
#include <config.h>  // Must come first
#endif

#include <stdlib.h>
#include <unistd.h>

#include <type_traits>
#include <string>

#include "breakpad_googletest_includes.h"
#include "common/using_std_string.h"
#include "google_breakpad/processor/basic_source_line_resolver.h"
#include "google_breakpad/processor/minidump_processor.h"
#include "google_breakpad/processor/process_state.h"
#ifdef __linux__
#include "processor/exploitability_linux.h"
#endif  // __linux__
#include "processor/simple_symbol_supplier.h"

#ifdef __linux__
namespace google_breakpad {
class ExploitabilityLinuxTestMinidumpContext : public MinidumpContext {
 public:
  explicit ExploitabilityLinuxTestMinidumpContext(
      const MDRawContextAMD64& context) : MinidumpContext(NULL) {
    valid_ = true;
    SetContextAMD64(new MDRawContextAMD64(context));
    SetContextFlags(MD_CONTEXT_AMD64);
  }
};

}  // namespace google_breakpad
#endif  // __linux__

namespace {

using google_breakpad::BasicSourceLineResolver;
#ifdef __linux__
using google_breakpad::ExploitabilityLinuxTestMinidumpContext;
#endif  // __linux__
using google_breakpad::MinidumpProcessor;
using google_breakpad::ProcessState;
using google_breakpad::SimpleSymbolSupplier;

string TestDataDir() {
  return string(getenv("srcdir") ? getenv("srcdir") : ".") +
      "/src/processor/testdata";
}

// Find the given dump file in <srcdir>/src/processor/testdata, process it,
// and get the exploitability rating. Returns EXPLOITABILITY_ERR_PROCESSING
// if the crash dump can't be processed.
google_breakpad::ExploitabilityRating
ExploitabilityFor(const string& filename) {
  SimpleSymbolSupplier supplier(TestDataDir() + "/symbols");
  BasicSourceLineResolver resolver;
  MinidumpProcessor processor(&supplier, &resolver, true);
  processor.set_enable_objdump_for_exploitability(true);
  ProcessState state;

  string minidump_file = TestDataDir() + "/" + filename;

  if (processor.Process(minidump_file, &state) !=
      google_breakpad::PROCESS_OK) {
    return google_breakpad::EXPLOITABILITY_ERR_PROCESSING;
  }

  return state.exploitability();
}

TEST(ExploitabilityTest, TestWindowsEngine) {
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_read_av.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_read_av_block_write.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_read_av_clobber_write.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_read_av_conditional.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_read_av_then_jmp.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_read_av_xchg_write.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_write_av.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("ascii_write_av_arg_to_call.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_NONE,
            ExploitabilityFor("null_read_av.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_NONE,
            ExploitabilityFor("null_write_av.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_NONE,
            ExploitabilityFor("stack_exhaustion.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("exec_av_on_stack.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_MEDIUM,
            ExploitabilityFor("write_av_non_null.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_LOW,
            ExploitabilityFor("read_av_non_null.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_LOW,
            ExploitabilityFor("read_av_clobber_write.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_LOW,
            ExploitabilityFor("read_av_conditional.dmp"));
}

TEST(ExploitabilityTest, TestLinuxEngine) {
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_INTERESTING,
            ExploitabilityFor("linux_null_read_av.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_overflow.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_stacksmash.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_NONE,
            ExploitabilityFor("linux_divide_by_zero.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_INTERESTING,
            ExploitabilityFor("linux_null_dereference.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_jmp_to_0.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_outside_module.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_NONE,
            ExploitabilityFor("linux_raise_sigabrt.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_INTERESTING,
            ExploitabilityFor("linux_inside_module_exe_region1.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_INTERESTING,
            ExploitabilityFor("linux_inside_module_exe_region2.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_INTERESTING,
            ExploitabilityFor("linux_stack_pointer_in_stack.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_INTERESTING,
            ExploitabilityFor("linux_stack_pointer_in_stack_alt_name.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_stack_pointer_in_module.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_executable_stack.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_executable_heap.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_jmp_to_module_not_exe_region.dmp"));
#ifdef __linux__
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_write_to_nonwritable_module.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_write_to_nonwritable_region_math.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_write_to_outside_module.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_HIGH,
            ExploitabilityFor("linux_write_to_outside_module_via_math.dmp"));
  ASSERT_EQ(google_breakpad::EXPLOITABILITY_INTERESTING,
            ExploitabilityFor("linux_write_to_under_4k.dmp"));
#endif  // __linux__
}

}  // namespace
