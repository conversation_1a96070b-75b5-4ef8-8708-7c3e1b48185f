// Copyright 2007 Google LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: Alfred Peng

#ifdef HAVE_CONFIG_H
#include <config.h>  // Must come first
#endif

#include <pthread.h>
#include <unistd.h>

#include "client/minidump_file_writer.h"
#include "client/solaris/handler/minidump_generator.h"

using std::string;
using google_breakpad::MinidumpGenerator;

static bool doneWritingReport = false;

static void* Reporter(void*) {
  char buffer[PATH_MAX];
  MinidumpGenerator md;

  // Write it to the desktop
  snprintf(buffer, sizeof(buffer), "./minidump_test.out");
  fprintf(stdout, "Writing %s\n", buffer);

  md.WriteMinidumpToFile(buffer, 0, 0, NULL);
  doneWritingReport = true;

  return NULL;
}

static void SleepyFunction() {
  while (!doneWritingReport) {
    usleep(100);
  }
}

int main(int argc, char * const argv[]) {
  pthread_t reporter_thread;

  if (pthread_create(&reporter_thread, NULL, Reporter, NULL) == 0) {
    pthread_detach(reporter_thread);
  } else {
    perror("pthread_create");
  }

  SleepyFunction();

  return 0;
}
