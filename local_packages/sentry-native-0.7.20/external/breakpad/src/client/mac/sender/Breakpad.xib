<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="7.10">
	<data>
		<int key="IBDocument.SystemTarget">1070</int>
		<string key="IBDocument.SystemVersion">10F569</string>
		<string key="IBDocument.InterfaceBuilderVersion">762</string>
		<string key="IBDocument.AppKitVersion">1038.29</string>
		<string key="IBDocument.HIToolboxVersion">461.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSArray" key="dict.sortedKeys" id="0">
				<bool key="EncodedWithXMLCoder">YES</bool>
			</object>
			<object class="NSMutableArray" key="dict.values">
				<bool key="EncodedWithXMLCoder">YES</bool>
			</object>
		</object>
		<object class="NSMutableArray" key="IBDocument.EditedObjectIDs">
			<bool key="EncodedWithXMLCoder">YES</bool>
		</object>
		<reference key="IBDocument.PluginDependencies" ref="0"/>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<reference key="dict.sortedKeys" ref="0"/>
			<object class="NSMutableArray" key="dict.values">
				<bool key="EncodedWithXMLCoder">YES</bool>
			</object>
		</object>
		<object class="NSMutableArray" key="IBDocument.RootObjects" id="504246249">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSCustomObject" id="273934324">
				<string key="NSClassName">Reporter</string>
			</object>
			<object class="NSCustomObject" id="388635980">
				<string key="NSClassName">FirstResponder</string>
			</object>
			<object class="NSCustomObject" id="220995958">
				<string key="NSClassName">NSApplication</string>
			</object>
			<object class="NSWindowTemplate" id="762998835">
				<int key="NSWindowStyleMask">1</int>
				<int key="NSWindowBacking">2</int>
				<string key="NSWindowRect">{{72, 251}, {490, 489}}</string>
				<int key="NSWTFlags">536871936</int>
				<string key="NSWindowTitle"/>
				<string key="NSWindowClass">NSWindow</string>
				<nil key="NSViewClass"/>
				<string key="NSWindowContentMaxSize">{1.79769e+308, 1.79769e+308}</string>
				<string key="NSWindowContentMinSize">{72, 5}</string>
				<object class="NSView" key="NSWindowView" id="197525436">
					<nil key="NSNextResponder"/>
					<int key="NSvFlags">264</int>
					<object class="NSMutableArray" key="NSSubviews">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSBox" id="469837363">
							<reference key="NSNextResponder" ref="197525436"/>
							<int key="NSvFlags">272</int>
							<object class="NSMutableArray" key="NSSubviews">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSView" id="966817352">
									<reference key="NSNextResponder" ref="469837363"/>
									<int key="NSvFlags">256</int>
									<object class="NSMutableArray" key="NSSubviews">
										<bool key="EncodedWithXMLCoder">YES</bool>
										<object class="NSTextField" id="997378142">
											<reference key="NSNextResponder" ref="966817352"/>
											<int key="NSvFlags">290</int>
											<string key="NSFrame">{{17, 36}, {456, 70}}</string>
											<reference key="NSSuperview" ref="966817352"/>
											<bool key="NSEnabled">YES</bool>
											<object class="NSTextFieldCell" key="NSCell" id="509794736">
												<int key="NSCellFlags">67239424</int>
												<int key="NSCellFlags2">272760832</int>
												<string key="NSContents">Providing your email address is optional and will allow us contact you in case we need more details. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed arcu urna, pulvinar sit amet, tincidunt ac, fermentum ut, ligula. Quisque mi. Duis lectus. Vestibulum velit. Morbi turpis. Nunc at diam consectetur turpis volutpat tristique. Donec quis diam. Suspendisse scelerisque.</string>
												<object class="NSFont" key="NSSupport" id="26">
													<string key="NSName">LucidaGrande</string>
													<double key="NSSize">11</double>
													<int key="NSfFlags">3100</int>
												</object>
												<reference key="NSControlView" ref="997378142"/>
												<object class="NSColor" key="NSBackgroundColor" id="420457920">
													<int key="NSColorSpace">6</int>
													<string key="NSCatalogName">System</string>
													<string key="NSColorName">controlColor</string>
													<object class="NSColor" key="NSColor">
														<int key="NSColorSpace">3</int>
														<bytes key="NSWhite">MC42NjY2NjY2NjY3AA</bytes>
													</object>
												</object>
												<object class="NSColor" key="NSTextColor" id="800255527">
													<int key="NSColorSpace">6</int>
													<string key="NSCatalogName">System</string>
													<string key="NSColorName">controlTextColor</string>
													<object class="NSColor" key="NSColor" id="908763363">
														<int key="NSColorSpace">3</int>
														<bytes key="NSWhite">MAA</bytes>
													</object>
												</object>
											</object>
										</object>
										<object class="NSTextField" id="975305147">
											<reference key="NSNextResponder" ref="966817352"/>
											<int key="NSvFlags">290</int>
											<string key="NSFrame">{{87, 9}, {195, 19}}</string>
											<reference key="NSSuperview" ref="966817352"/>
											<bool key="NSEnabled">YES</bool>
											<object class="NSTextFieldCell" key="NSCell" id="592393645">
												<int key="NSCellFlags">-1804468671</int>
												<int key="NSCellFlags2">272761856</int>
												<string key="NSContents"/>
												<reference key="NSSupport" ref="26"/>
												<string key="NSPlaceholderString">optional</string>
												<reference key="NSControlView" ref="975305147"/>
												<bool key="NSDrawsBackground">YES</bool>
												<object class="NSColor" key="NSBackgroundColor" id="128478752">
													<int key="NSColorSpace">6</int>
													<string key="NSCatalogName">System</string>
													<string key="NSColorName">textBackgroundColor</string>
													<object class="NSColor" key="NSColor">
														<int key="NSColorSpace">3</int>
														<bytes key="NSWhite">MQA</bytes>
													</object>
												</object>
												<object class="NSColor" key="NSTextColor" id="734930533">
													<int key="NSColorSpace">6</int>
													<string key="NSCatalogName">System</string>
													<string key="NSColorName">textColor</string>
													<reference key="NSColor" ref="908763363"/>
												</object>
											</object>
										</object>
										<object class="NSTextField" id="268211031">
											<reference key="NSNextResponder" ref="966817352"/>
											<int key="NSvFlags">292</int>
											<string key="NSFrame">{{17, 11}, {65, 14}}</string>
											<reference key="NSSuperview" ref="966817352"/>
											<bool key="NSEnabled">YES</bool>
											<object class="NSTextFieldCell" key="NSCell" id="461570326">
												<int key="NSCellFlags">68288064</int>
												<int key="NSCellFlags2">71435264</int>
												<string key="NSContents">EmailLabel:</string>
												<reference key="NSSupport" ref="26"/>
												<reference key="NSControlView" ref="268211031"/>
												<reference key="NSBackgroundColor" ref="420457920"/>
												<reference key="NSTextColor" ref="800255527"/>
											</object>
										</object>
										<object class="NSButton" id="538303250">
											<reference key="NSNextResponder" ref="966817352"/>
											<int key="NSvFlags">289</int>
											<string key="NSFrame">{{456, 10}, {16, 17}}</string>
											<reference key="NSSuperview" ref="966817352"/>
											<bool key="NSEnabled">YES</bool>
											<object class="NSButtonCell" key="NSCell" id="778004767">
												<int key="NSCellFlags">-2080244224</int>
												<int key="NSCellFlags2">0</int>
												<string key="NSContents">Privacy Policy</string>
												<object class="NSFont" key="NSSupport" id="222882491">
													<string key="NSName">LucidaGrande</string>
													<double key="NSSize">13</double>
													<int key="NSfFlags">1044</int>
												</object>
												<reference key="NSControlView" ref="538303250"/>
												<int key="NSButtonFlags">-2040250113</int>
												<int key="NSButtonFlags2">36</int>
												<object class="NSCustomResource" key="NSNormalImage">
													<string key="NSClassName">NSImage</string>
													<string key="NSResourceName">goArrow</string>
												</object>
												<string key="NSAlternateContents"/>
												<string key="NSKeyEquivalent"/>
												<int key="NSPeriodicDelay">400</int>
												<int key="NSPeriodicInterval">75</int>
											</object>
										</object>
										<object class="NSTextField" id="655227981">
											<reference key="NSNextResponder" ref="966817352"/>
											<int key="NSvFlags">289</int>
											<string key="NSFrame">{{355, 11}, {100, 14}}</string>
											<reference key="NSSuperview" ref="966817352"/>
											<bool key="NSEnabled">YES</bool>
											<object class="NSTextFieldCell" key="NSCell" id="1012850565">
												<int key="NSCellFlags">68288064</int>
												<int key="NSCellFlags2">4326400</int>
												<string key="NSContents">PrivacyPolicyLabel</string>
												<reference key="NSSupport" ref="26"/>
												<reference key="NSControlView" ref="655227981"/>
												<reference key="NSBackgroundColor" ref="420457920"/>
												<reference key="NSTextColor" ref="800255527"/>
											</object>
										</object>
									</object>
									<string key="NSFrameSize">{490, 114}</string>
									<reference key="NSSuperview" ref="469837363"/>
								</object>
							</object>
							<string key="NSFrame">{{0, 51}, {490, 114}}</string>
							<reference key="NSSuperview" ref="197525436"/>
							<string key="NSOffsets">{0, 0}</string>
							<object class="NSTextFieldCell" key="NSTitleCell">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">0</int>
								<string key="NSContents">Title</string>
								<object class="NSFont" key="NSSupport" id="668643277">
									<string key="NSName">LucidaGrande</string>
									<double key="NSSize">11</double>
									<int key="NSfFlags">16</int>
								</object>
								<reference key="NSBackgroundColor" ref="128478752"/>
								<object class="NSColor" key="NSTextColor">
									<int key="NSColorSpace">3</int>
									<bytes key="NSWhite">MCAwLjgwMDAwMDAxAA</bytes>
								</object>
							</object>
							<reference key="NSContentView" ref="966817352"/>
							<int key="NSBorderType">0</int>
							<int key="NSBoxType">3</int>
							<int key="NSTitlePosition">0</int>
							<bool key="NSTransparent">NO</bool>
						</object>
						<object class="NSButton" id="219938755">
							<reference key="NSNextResponder" ref="197525436"/>
							<int key="NSvFlags">289</int>
							<string key="NSFrame">{{330, 12}, {146, 32}}</string>
							<reference key="NSSuperview" ref="197525436"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="733475259">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">SendReportLabel</string>
								<reference key="NSSupport" ref="222882491"/>
								<reference key="NSControlView" ref="219938755"/>
								<int key="NSButtonFlags">-2038284033</int>
								<int key="NSButtonFlags2">129</int>
								<reference key="NSAlternateImage" ref="222882491"/>
								<string key="NSAlternateContents"/>
								<string type="base64-UTF8" key="NSKeyEquivalent">DQ</string>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
						</object>
						<object class="NSButton" id="409721323">
							<reference key="NSNextResponder" ref="197525436"/>
							<int key="NSvFlags">289</int>
							<string key="NSFrame">{{214, 12}, {116, 32}}</string>
							<reference key="NSSuperview" ref="197525436"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="586160416">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">CancelLabel</string>
								<reference key="NSSupport" ref="222882491"/>
								<reference key="NSControlView" ref="409721323"/>
								<int key="NSButtonFlags">-2038284033</int>
								<int key="NSButtonFlags2">129</int>
								<reference key="NSAlternateImage" ref="222882491"/>
								<string key="NSAlternateContents"/>
								<string type="base64-UTF8" key="NSKeyEquivalent">Gw</string>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
						</object>
						<object class="NSBox" id="468151514">
							<reference key="NSNextResponder" ref="197525436"/>
							<int key="NSvFlags">256</int>
							<object class="NSMutableArray" key="NSSubviews">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSView" id="1059038623">
									<reference key="NSNextResponder" ref="468151514"/>
									<int key="NSvFlags">256</int>
									<object class="NSMutableArray" key="NSSubviews">
										<bool key="EncodedWithXMLCoder">YES</bool>
										<object class="NSTextField" id="375247105">
											<reference key="NSNextResponder" ref="1059038623"/>
											<int key="NSvFlags">266</int>
											<string key="NSFrame">{{17, 83}, {456, 154}}</string>
											<reference key="NSSuperview" ref="1059038623"/>
											<bool key="NSEnabled">YES</bool>
											<object class="NSTextFieldCell" key="NSCell" id="188082030">
												<int key="NSCellFlags">67239424</int>
												<int key="NSCellFlags2">272760832</int>
												<string type="base64-UTF8" key="NSContents">VGhlIHN5c3RlbSBhbmQgb3RoZXIgYXBwbGljYXRpb25zIGhhdmUgbm90IGJlZW4gYWZmZWN0ZWQuIEEg
cmVwb3J0IGhhcyBiZWVuIGNyZWF0ZWQgdGhhdCB5b3UgY2FuIHNlbmQgdG8gPFJlYWxseSBMb25nIENv
bXBhbnkgTmFtZT4gdG8gaGVscCBpZGVudGlmeSB0aGUgcHJvYmxlbS4gTG9yZW0gaXBzdW0gZG9sb3Ig
c2l0IGFtZXQsIGNvbnNlY3RldHVyIGFkaXBpc2NpbmcgZWxpdC4gU2VkIGFyY3UgdXJuYSwgcHVsdmlu
YXIgc2l0IGFtZXQsIHRpbmNpZHVudCBhYywgZmVybWVudHVtIHV0LCBsaWd1bGEuIFF1aXNxdWUgbWku
IER1aXMgbGVjdHVzLiBWZXN0aWJ1bHVtIHZlbGl0LiBNb3JiaSB0dXJwaXMuIE51bmMgYXQgZGlhbSBj
b25zZWN0ZXR1ciB0dXJwaXMgdm9sdXRwYXQgdHJpc3RpcXVlLiBEb25lYyBxdWlzIGRpYW0uIFN1c3Bl
bmRpc3NlIHNjZWxlcmlzcXVlLiBRdWlzcXVlIHB1bHZpbmFyIG1pIGlkIHB1cnVzLiBFdGlhbSB2aXRh
ZSB0dXJwaXMgdml0YWUgbmVxdWUgcG9ydGEgY29uZ3VlLgoKUGxlYXNlIGhlbHAgdXMgZml4IHRoZSBw
cm9ibGVtIGJ5IGRlc2NyaWJpbmcgd2hhdCBoYXBwZW5lZCBiZWZvcmUgdGhlIGNyYXNoLiBMb3JlbSBp
cHN1bSBkb2xvciBzaXQgYW1ldCwgY29uc2VjdGV0dXIgYWRpcGlzY2luZyBlbGl0LiBTZWQgYXJjdSB1
cm5hLCBwdWx2aW5hciBzaXQgYW1ldCwgdGluY2lkdW50IGFjLCBmZXJtZW50dW0gdXQsIGxpZ3VsYS4g
UXVpc3F1ZSBtaS4gRHVpcyBsZWN0dXMuA</string>
												<reference key="NSSupport" ref="26"/>
												<reference key="NSControlView" ref="375247105"/>
												<reference key="NSBackgroundColor" ref="420457920"/>
												<reference key="NSTextColor" ref="800255527"/>
											</object>
										</object>
										<object class="NSTextField" id="996404163">
											<reference key="NSNextResponder" ref="1059038623"/>
											<int key="NSvFlags">274</int>
											<string key="NSFrame">{{20, 14}, {450, 61}}</string>
											<reference key="NSSuperview" ref="1059038623"/>
											<bool key="NSEnabled">YES</bool>
											<object class="NSTextFieldCell" key="NSCell" id="242564194">
												<int key="NSCellFlags">341966337</int>
												<int key="NSCellFlags2">272760832</int>
												<string key="NSContents">Line 1 Line 1 Line 1 Line 1 Line 1 Line 1 Line 1 Line 1 Line 1 Line 1 Line 1 Line 1 Line 2 Line 2 Line 2 Line 2 Line 2 Line 2 Line 2 Line 2 Line 2 Line 2 Line 2 Line 2 Line 3 Line 3 Line 3 Line 3 Line 3 Line 3 Line 3 Line 3 Line 3 Line 3 Line 3 Line 3 Line 4 Line 4 Line 4 Line 4 Line 4 Line 4 Line 4 Line 4 Line 4 Line 4 Line 4 Line 4 </string>
												<reference key="NSSupport" ref="26"/>
												<reference key="NSControlView" ref="996404163"/>
												<bool key="NSDrawsBackground">YES</bool>
												<reference key="NSBackgroundColor" ref="128478752"/>
												<reference key="NSTextColor" ref="734930533"/>
											</object>
										</object>
										<object class="NSBox" id="667608859">
											<reference key="NSNextResponder" ref="1059038623"/>
											<int key="NSvFlags">256</int>
											<object class="NSMutableArray" key="NSSubviews">
												<bool key="EncodedWithXMLCoder">YES</bool>
												<object class="NSView" id="971021844">
													<reference key="NSNextResponder" ref="667608859"/>
													<int key="NSvFlags">256</int>
													<object class="NSMutableArray" key="NSSubviews">
														<bool key="EncodedWithXMLCoder">YES</bool>
														<object class="NSTextField" id="1032334641">
															<reference key="NSNextResponder" ref="971021844"/>
															<int key="NSvFlags">266</int>
															<string key="NSFrame">{{85, 10}, {381, 54}}</string>
															<reference key="NSSuperview" ref="971021844"/>
															<bool key="NSEnabled">YES</bool>
															<object class="NSTextFieldCell" key="NSCell" id="316557784">
																<int key="NSCellFlags">67239424</int>
																<int key="NSCellFlags2">272629760</int>
																<string key="NSContents">The application &lt;Really Long App Name Here&gt; has quit unexpectedly.</string>
																<object class="NSFont" key="NSSupport">
																	<string key="NSName">LucidaGrande-Bold</string>
																	<double key="NSSize">14</double>
																	<int key="NSfFlags">16</int>
																</object>
																<reference key="NSControlView" ref="1032334641"/>
																<reference key="NSBackgroundColor" ref="420457920"/>
																<reference key="NSTextColor" ref="800255527"/>
															</object>
														</object>
														<object class="NSImageView" id="594334723">
															<reference key="NSNextResponder" ref="971021844"/>
															<int key="NSvFlags">268</int>
															<object class="NSMutableSet" key="NSDragTypes">
																<bool key="EncodedWithXMLCoder">YES</bool>
																<object class="NSArray" key="set.sortedObjects">
																	<bool key="EncodedWithXMLCoder">YES</bool>
																	<string>Apple PDF pasteboard type</string>
																	<string>Apple PICT pasteboard type</string>
																	<string>Apple PNG pasteboard type</string>
																	<string>NSFilenamesPboardType</string>
																	<string>NeXT Encapsulated PostScript v1.2 pasteboard type</string>
																	<string>NeXT TIFF v4.0 pasteboard type</string>
																</object>
															</object>
															<string key="NSFrame">{{16, 0}, {64, 64}}</string>
															<reference key="NSSuperview" ref="971021844"/>
															<bool key="NSEnabled">YES</bool>
															<object class="NSImageCell" key="NSCell" id="465445685">
																<int key="NSCellFlags">130560</int>
																<int key="NSCellFlags2">33554432</int>
																<object class="NSCustomResource" key="NSContents">
																	<string key="NSClassName">NSImage</string>
																	<string key="NSResourceName">NSApplicationIcon</string>
																</object>
																<int key="NSAlign">0</int>
																<int key="NSScale">0</int>
																<int key="NSStyle">0</int>
																<bool key="NSAnimates">NO</bool>
															</object>
															<bool key="NSEditable">YES</bool>
														</object>
													</object>
													<string key="NSFrameSize">{482, 70}</string>
													<reference key="NSSuperview" ref="667608859"/>
												</object>
											</object>
											<string key="NSFrame">{{4, 245}, {482, 70}}</string>
											<reference key="NSSuperview" ref="1059038623"/>
											<string key="NSOffsets">{0, 0}</string>
											<object class="NSTextFieldCell" key="NSTitleCell">
												<int key="NSCellFlags">67239424</int>
												<int key="NSCellFlags2">0</int>
												<string key="NSContents">Title</string>
												<reference key="NSSupport" ref="668643277"/>
												<reference key="NSBackgroundColor" ref="128478752"/>
												<object class="NSColor" key="NSTextColor">
													<int key="NSColorSpace">3</int>
													<bytes key="NSWhite">MCAwLjgwMDAwMDAxAA</bytes>
												</object>
											</object>
											<reference key="NSContentView" ref="971021844"/>
											<int key="NSBorderType">0</int>
											<int key="NSBoxType">3</int>
											<int key="NSTitlePosition">0</int>
											<bool key="NSTransparent">NO</bool>
										</object>
									</object>
									<string key="NSFrameSize">{490, 325}</string>
									<reference key="NSSuperview" ref="468151514"/>
								</object>
							</object>
							<string key="NSFrame">{{0, 160}, {490, 325}}</string>
							<reference key="NSSuperview" ref="197525436"/>
							<string key="NSOffsets">{0, 0}</string>
							<object class="NSTextFieldCell" key="NSTitleCell">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">0</int>
								<string key="NSContents">Title</string>
								<reference key="NSSupport" ref="668643277"/>
								<reference key="NSBackgroundColor" ref="128478752"/>
								<object class="NSColor" key="NSTextColor">
									<int key="NSColorSpace">3</int>
									<bytes key="NSWhite">MCAwLjgwMDAwMDAxAA</bytes>
								</object>
							</object>
							<reference key="NSContentView" ref="1059038623"/>
							<int key="NSBorderType">0</int>
							<int key="NSBoxType">3</int>
							<int key="NSTitlePosition">0</int>
							<bool key="NSTransparent">NO</bool>
						</object>
						<object class="NSTextField" id="149448677">
							<reference key="NSNextResponder" ref="197525436"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{17, 20}, {163, 14}}</string>
							<reference key="NSSuperview" ref="197525436"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSTextFieldCell" key="NSCell" id="690832321">
								<int key="NSCellFlags">68288064</int>
								<int key="NSCellFlags2">272630784</int>
								<string key="NSContents">xx seconds.</string>
								<reference key="NSSupport" ref="668643277"/>
								<reference key="NSControlView" ref="149448677"/>
								<reference key="NSBackgroundColor" ref="420457920"/>
								<reference key="NSTextColor" ref="800255527"/>
							</object>
						</object>
					</object>
					<string key="NSFrameSize">{490, 489}</string>
				</object>
				<string key="NSScreenRect">{{0, 0}, {2560, 1578}}</string>
				<string key="NSMinSize">{72, 27}</string>
				<string key="NSMaxSize">{1.79769e+308, 1.79769e+308}</string>
			</object>
			<object class="NSUserDefaultsController" id="626548788">
				<bool key="NSSharedInstance">YES</bool>
			</object>
		</object>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<object class="NSMutableArray" key="connectionRecords">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">sendReport:</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="219938755"/>
					</object>
					<int key="connectionID">45</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">cancel:</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="409721323"/>
					</object>
					<int key="connectionID">46</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showPrivacyPolicy:</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="538303250"/>
					</object>
					<int key="connectionID">53</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBBindingConnection" key="connection">
						<string key="label">value: emailValue</string>
						<reference key="source" ref="975305147"/>
						<reference key="destination" ref="273934324"/>
						<object class="NSNibBindingConnector" key="connector">
							<reference key="NSSource" ref="975305147"/>
							<reference key="NSDestination" ref="273934324"/>
							<string key="NSLabel">value: emailValue</string>
							<string key="NSBinding">value</string>
							<string key="NSKeyPath">emailValue</string>
							<object class="NSDictionary" key="NSOptions">
								<string key="NS.key.0">NSNullPlaceholder</string>
								<string key="NS.object.0">optional</string>
							</object>
							<int key="NSNibBindingConnectorVersion">2</int>
						</object>
					</object>
					<int key="connectionID">90</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">initialFirstResponder</string>
						<reference key="source" ref="762998835"/>
						<reference key="destination" ref="219938755"/>
					</object>
					<int key="connectionID">91</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBBindingConnection" key="connection">
						<string key="label">value: commentsValue</string>
						<reference key="source" ref="996404163"/>
						<reference key="destination" ref="273934324"/>
						<object class="NSNibBindingConnector" key="connector">
							<reference key="NSSource" ref="996404163"/>
							<reference key="NSDestination" ref="273934324"/>
							<string key="NSLabel">value: commentsValue</string>
							<string key="NSBinding">value</string>
							<string key="NSKeyPath">commentsValue</string>
							<object class="NSDictionary" key="NSOptions">
								<string key="NS.key.0">NSNullPlaceholder</string>
								<string key="NS.object.0">optional comments</string>
							</object>
							<int key="NSNibBindingConnectorVersion">2</int>
						</object>
					</object>
					<int key="connectionID">124</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">nextKeyView</string>
						<reference key="source" ref="975305147"/>
						<reference key="destination" ref="219938755"/>
					</object>
					<int key="connectionID">125</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">nextKeyView</string>
						<reference key="source" ref="996404163"/>
						<reference key="destination" ref="975305147"/>
					</object>
					<int key="connectionID">126</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">nextKeyView</string>
						<reference key="source" ref="219938755"/>
						<reference key="destination" ref="996404163"/>
					</object>
					<int key="connectionID">127</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">delegate</string>
						<reference key="source" ref="996404163"/>
						<reference key="destination" ref="273934324"/>
					</object>
					<int key="connectionID">128</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">alertWindow_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="762998835"/>
					</object>
					<int key="connectionID">142</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">preEmailBox_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="468151514"/>
					</object>
					<int key="connectionID">150</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">headerBox_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="667608859"/>
					</object>
					<int key="connectionID">151</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">emailSectionBox_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="469837363"/>
					</object>
					<int key="connectionID">152</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">privacyLinkLabel_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="655227981"/>
					</object>
					<int key="connectionID">153</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">commentMessage_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="375247105"/>
					</object>
					<int key="connectionID">154</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">dialogTitle_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="1032334641"/>
					</object>
					<int key="connectionID">155</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">emailLabel_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="268211031"/>
					</object>
					<int key="connectionID">156</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">cancelButton_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="409721323"/>
					</object>
					<int key="connectionID">158</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">sendButton_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="219938755"/>
					</object>
					<int key="connectionID">159</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">emailEntryField_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="975305147"/>
					</object>
					<int key="connectionID">161</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">privacyLinkArrow_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="538303250"/>
					</object>
					<int key="connectionID">162</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">emailMessage_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="997378142"/>
					</object>
					<int key="connectionID">163</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">commentsEntryField_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="996404163"/>
					</object>
					<int key="connectionID">176</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBBindingConnection" key="connection">
						<string key="label">value: countdownMessage</string>
						<reference key="source" ref="149448677"/>
						<reference key="destination" ref="273934324"/>
						<object class="NSNibBindingConnector" key="connector">
							<reference key="NSSource" ref="149448677"/>
							<reference key="NSDestination" ref="273934324"/>
							<string key="NSLabel">value: countdownMessage</string>
							<string key="NSBinding">value</string>
							<string key="NSKeyPath">countdownMessage</string>
							<int key="NSNibBindingConnectorVersion">2</int>
						</object>
					</object>
					<int key="connectionID">194</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">countdownLabel_</string>
						<reference key="source" ref="273934324"/>
						<reference key="destination" ref="149448677"/>
					</object>
					<int key="connectionID">208</int>
				</object>
			</object>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<object class="NSArray" key="orderedObjects">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<reference key="object" ref="0"/>
						<reference key="children" ref="504246249"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="273934324"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="388635980"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">First Responder</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-3</int>
						<reference key="object" ref="220995958"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">Application</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">1</int>
						<reference key="object" ref="762998835"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="197525436"/>
						</object>
						<reference key="parent" ref="0"/>
						<string key="objectName">Window</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">2</int>
						<reference key="object" ref="197525436"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="219938755"/>
							<reference ref="409721323"/>
							<reference ref="469837363"/>
							<reference ref="468151514"/>
							<reference ref="149448677"/>
						</object>
						<reference key="parent" ref="762998835"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">12</int>
						<reference key="object" ref="219938755"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="733475259"/>
						</object>
						<reference key="parent" ref="197525436"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">14</int>
						<reference key="object" ref="409721323"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="586160416"/>
						</object>
						<reference key="parent" ref="197525436"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">132</int>
						<reference key="object" ref="469837363"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="997378142"/>
							<reference ref="975305147"/>
							<reference ref="268211031"/>
							<reference ref="538303250"/>
							<reference ref="655227981"/>
						</object>
						<reference key="parent" ref="197525436"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">145</int>
						<reference key="object" ref="468151514"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="375247105"/>
							<reference ref="996404163"/>
							<reference ref="667608859"/>
						</object>
						<reference key="parent" ref="197525436"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">189</int>
						<reference key="object" ref="149448677"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="690832321"/>
						</object>
						<reference key="parent" ref="197525436"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">191</int>
						<reference key="object" ref="626548788"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">Shared User Defaults Controller</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">210</int>
						<reference key="object" ref="733475259"/>
						<reference key="parent" ref="219938755"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">211</int>
						<reference key="object" ref="586160416"/>
						<reference key="parent" ref="409721323"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">221</int>
						<reference key="object" ref="690832321"/>
						<reference key="parent" ref="149448677"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">58</int>
						<reference key="object" ref="997378142"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="509794736"/>
						</object>
						<reference key="parent" ref="469837363"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">215</int>
						<reference key="object" ref="509794736"/>
						<reference key="parent" ref="997378142"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">18</int>
						<reference key="object" ref="975305147"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="592393645"/>
						</object>
						<reference key="parent" ref="469837363"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">212</int>
						<reference key="object" ref="592393645"/>
						<reference key="parent" ref="975305147"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">20</int>
						<reference key="object" ref="268211031"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="461570326"/>
						</object>
						<reference key="parent" ref="469837363"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">213</int>
						<reference key="object" ref="461570326"/>
						<reference key="parent" ref="268211031"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">48</int>
						<reference key="object" ref="538303250"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="778004767"/>
						</object>
						<reference key="parent" ref="469837363"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">214</int>
						<reference key="object" ref="778004767"/>
						<reference key="parent" ref="538303250"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">66</int>
						<reference key="object" ref="655227981"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1012850565"/>
						</object>
						<reference key="parent" ref="469837363"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">216</int>
						<reference key="object" ref="1012850565"/>
						<reference key="parent" ref="655227981"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">8</int>
						<reference key="object" ref="375247105"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="188082030"/>
						</object>
						<reference key="parent" ref="468151514"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">217</int>
						<reference key="object" ref="188082030"/>
						<reference key="parent" ref="375247105"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">116</int>
						<reference key="object" ref="996404163"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="242564194"/>
						</object>
						<reference key="parent" ref="468151514"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">218</int>
						<reference key="object" ref="242564194"/>
						<reference key="parent" ref="996404163"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">147</int>
						<reference key="object" ref="667608859"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1032334641"/>
							<reference ref="594334723"/>
						</object>
						<reference key="parent" ref="468151514"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">3</int>
						<reference key="object" ref="1032334641"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="316557784"/>
						</object>
						<reference key="parent" ref="667608859"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">219</int>
						<reference key="object" ref="316557784"/>
						<reference key="parent" ref="1032334641"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">6</int>
						<reference key="object" ref="594334723"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="465445685"/>
						</object>
						<reference key="parent" ref="667608859"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">220</int>
						<reference key="object" ref="465445685"/>
						<reference key="parent" ref="594334723"/>
					</object>
				</object>
			</object>
			<object class="NSMutableDictionary" key="flattenedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="NSArray" key="dict.sortedKeys">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>-3.ImportedFromIB2</string>
					<string>1.IBEditorWindowLastContentRect</string>
					<string>1.IBWindowTemplateEditedContentRect</string>
					<string>1.ImportedFromIB2</string>
					<string>1.windowTemplate.hasMinSize</string>
					<string>1.windowTemplate.minSize</string>
					<string>116.CustomClassName</string>
					<string>116.ImportedFromIB2</string>
					<string>12.ImportedFromIB2</string>
					<string>132.ImportedFromIB2</string>
					<string>14.ImportedFromIB2</string>
					<string>145.ImportedFromIB2</string>
					<string>147.ImportedFromIB2</string>
					<string>18.CustomClassName</string>
					<string>18.ImportedFromIB2</string>
					<string>189.ImportedFromIB2</string>
					<string>191.ImportedFromIB2</string>
					<string>2.ImportedFromIB2</string>
					<string>20.ImportedFromIB2</string>
					<string>3.ImportedFromIB2</string>
					<string>48.ImportedFromIB2</string>
					<string>58.ImportedFromIB2</string>
					<string>6.ImportedFromIB2</string>
					<string>66.ImportedFromIB2</string>
					<string>8.ImportedFromIB2</string>
				</object>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<boolean value="YES"/>
					<string>{{0, 656}, {490, 489}}</string>
					<string>{{0, 656}, {490, 489}}</string>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<string>{72, 5}</string>
					<string>LengthLimitingTextField</string>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<string>LengthLimitingTextField</string>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<boolean value="YES"/>
				</object>
			</object>
			<object class="NSMutableDictionary" key="unlocalizedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="activeLocalization"/>
			<object class="NSMutableDictionary" key="localizations">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="sourceID"/>
			<int key="maxID">221</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes">
			<object class="NSMutableArray" key="referencedPartialClassDescriptions">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBPartialClassDescription">
					<string key="className">LengthLimitingTextField</string>
					<string key="superclassName">NSTextField</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBUserSource</string>
						<string key="minorKey"/>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">Reporter</string>
					<string key="superclassName">NSObject</string>
					<object class="NSMutableDictionary" key="actions">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>cancel:</string>
							<string>sendReport:</string>
							<string>showPrivacyPolicy:</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>id</string>
							<string>id</string>
							<string>id</string>
						</object>
					</object>
					<object class="NSMutableDictionary" key="outlets">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>alertWindow_</string>
							<string>cancelButton_</string>
							<string>commentMessage_</string>
							<string>commentsEntryField_</string>
							<string>countdownLabel_</string>
							<string>dialogTitle_</string>
							<string>emailEntryField_</string>
							<string>emailLabel_</string>
							<string>emailMessage_</string>
							<string>emailSectionBox_</string>
							<string>headerBox_</string>
							<string>preEmailBox_</string>
							<string>privacyLinkArrow_</string>
							<string>privacyLinkLabel_</string>
							<string>sendButton_</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>NSWindow</string>
							<string>NSButton</string>
							<string>NSTextField</string>
							<string>LengthLimitingTextField</string>
							<string>NSTextField</string>
							<string>NSTextField</string>
							<string>LengthLimitingTextField</string>
							<string>NSTextField</string>
							<string>NSTextField</string>
							<string>NSBox</string>
							<string>NSBox</string>
							<string>NSBox</string>
							<string>NSView</string>
							<string>NSTextField</string>
							<string>NSButton</string>
						</object>
					</object>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBUserSource</string>
						<string key="minorKey"/>
					</object>
				</object>
			</object>
		</object>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaFramework</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.macosx</string>
			<integer value="1070" key="NS.object.0"/>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDevelopmentDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.InterfaceBuilder3</string>
			<integer value="3000" key="NS.object.0"/>
		</object>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<string key="IBDocument.LastKnownRelativeProjectPath">../Breakpad.xcodeproj</string>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<object class="NSMutableDictionary" key="IBDocument.LastKnownImageSizes">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSArray" key="dict.sortedKeys">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<string>NSApplicationIcon</string>
				<string>goArrow</string>
			</object>
			<object class="NSMutableArray" key="dict.values">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<string>{128, 128}</string>
				<string>{128, 128}</string>
			</object>
		</object>
	</data>
</archive>
