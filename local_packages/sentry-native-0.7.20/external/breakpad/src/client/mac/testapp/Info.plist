<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleIconFile</key>
	<string>bomb</string>
	<key>CFBundleIdentifier</key>
	<string>com.Google.BreakpadTest</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>${PRODUCT_NAME}</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>BreakpadProductDisplay</key>
	<string>Breakpad Tester</string>
	<key>BreakpadProduct</key>
	<string>Breakpad_Tester</string>
	<key>BreakpadVersion</key>
	<string>*******</string>
	<key>BreakpadReportInterval</key>
	<string>10</string>
	<key>BreakpadSkipConfirm</key>
	<string>NO</string>
	<key>BreakpadSendAndExit</key>
	<string>YES</string>
	<key>BreakpadRequestEmail</key>
	<string>YES</string>
	<key>BreakpadRequestComments</key>
	<string>YES</string>
	<key>BreakpadVendor</key>
	<string>Foo Bar Corp, Incorporated, LTD, LLC</string>
	<key>BreakpadServerParameters</key>
	<dict>
	  <key>Param1</key>
	  <string>Value1</string>
	  <key>Param2</key>
	  <string>Value2</string>
	</dict>
	<key>LSUIElement</key>
	<string>1</string>
</dict>
</plist>
