<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="7.10">
	<data>
		<int key="IBDocument.SystemTarget">1050</int>
		<string key="IBDocument.SystemVersion">10F569</string>
		<string key="IBDocument.InterfaceBuilderVersion">788</string>
		<string key="IBDocument.AppKitVersion">1038.29</string>
		<string key="IBDocument.HIToolboxVersion">461.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin</string>
			<string key="NS.object.0">788</string>
		</object>
		<object class="NSMutableArray" key="IBDocument.EditedObjectIDs">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<integer value="220"/>
		</object>
		<object class="NSArray" key="IBDocument.PluginDependencies">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSArray" key="dict.sortedKeys" id="0">
				<bool key="EncodedWithXMLCoder">YES</bool>
			</object>
			<object class="NSMutableArray" key="dict.values">
				<bool key="EncodedWithXMLCoder">YES</bool>
			</object>
		</object>
		<object class="NSMutableArray" key="IBDocument.RootObjects" id="925601844">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSCustomObject" id="870565383">
				<object class="NSMutableString" key="NSClassName">
					<characters key="NS.bytes">NSApplication</characters>
				</object>
			</object>
			<object class="NSCustomObject" id="442653439">
				<string key="NSClassName">FirstResponder</string>
			</object>
			<object class="NSCustomObject" id="751079937">
				<string key="NSClassName">NSApplication</string>
			</object>
			<object class="NSWindowTemplate" id="341270541">
				<int key="NSWindowStyleMask">15</int>
				<int key="NSWindowBacking">2</int>
				<string key="NSWindowRect">{{945, 874}, {320, 188}}</string>
				<int key="NSWTFlags">1886912512</int>
				<string key="NSWindowTitle">Window</string>
				<string key="NSWindowClass">NSWindow</string>
				<object class="NSMutableString" key="NSViewClass">
					<characters key="NS.bytes">View</characters>
				</object>
				<string key="NSWindowContentMaxSize">{1.79769e+308, 1.79769e+308}</string>
				<string key="NSWindowContentMinSize">{213, 107}</string>
				<object class="NSView" key="NSWindowView" id="814272478">
					<reference key="NSNextResponder"/>
					<int key="NSvFlags">256</int>
					<object class="NSMutableArray" key="NSSubviews">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSButton" id="726278107">
							<reference key="NSNextResponder" ref="814272478"/>
							<int key="NSvFlags">301</int>
							<string key="NSFrame">{{14, 140}, {292, 32}}</string>
							<reference key="NSSuperview" ref="814272478"/>
							<reference key="NSWindow"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="539552922">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Crash! (Airbag Installed)</string>
								<object class="NSFont" key="NSSupport" id="933596199">
									<string key="NSName">LucidaGrande</string>
									<double key="NSSize">13</double>
									<int key="NSfFlags">1044</int>
								</object>
								<reference key="NSControlView" ref="726278107"/>
								<int key="NSButtonFlags">-2038284033</int>
								<int key="NSButtonFlags2">1</int>
								<reference key="NSAlternateImage" ref="933596199"/>
								<string key="NSAlternateContents"/>
								<object class="NSMutableString" key="NSKeyEquivalent">
									<characters key="NS.bytes"/>
								</object>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
						</object>
						<object class="NSButton" id="799567279">
							<reference key="NSNextResponder" ref="814272478"/>
							<int key="NSvFlags">301</int>
							<string key="NSFrame">{{14, 76}, {292, 32}}</string>
							<reference key="NSSuperview" ref="814272478"/>
							<reference key="NSWindow"/>
							<int key="NSTag">2</int>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="1010617379">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Crash! (Airbag NOT Installed)</string>
								<reference key="NSSupport" ref="933596199"/>
								<reference key="NSControlView" ref="799567279"/>
								<int key="NSTag">2</int>
								<int key="NSButtonFlags">-2038284033</int>
								<int key="NSButtonFlags2">1</int>
								<reference key="NSAlternateImage" ref="933596199"/>
								<string key="NSAlternateContents"/>
								<object class="NSMutableString" key="NSKeyEquivalent">
									<characters key="NS.bytes"/>
								</object>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
						</object>
						<object class="NSButton" id="27781390">
							<reference key="NSNextResponder" ref="814272478"/>
							<int key="NSvFlags">301</int>
							<string key="NSFrame">{{14, 108}, {292, 32}}</string>
							<reference key="NSSuperview" ref="814272478"/>
							<reference key="NSWindow"/>
							<int key="NSTag">1</int>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="547901497">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Crash! (Airbag Installed w/10sec delay)</string>
								<reference key="NSSupport" ref="933596199"/>
								<reference key="NSControlView" ref="27781390"/>
								<int key="NSTag">1</int>
								<int key="NSButtonFlags">-2038284033</int>
								<int key="NSButtonFlags2">1</int>
								<reference key="NSAlternateImage" ref="933596199"/>
								<string key="NSAlternateContents"/>
								<object class="NSMutableString" key="NSKeyEquivalent">
									<characters key="NS.bytes"/>
								</object>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
						</object>
						<object class="NSButton" id="856256540">
							<reference key="NSNextResponder" ref="814272478"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{14, 44}, {292, 32}}</string>
							<reference key="NSSuperview" ref="814272478"/>
							<reference key="NSWindow"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="353736234">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Fork Test</string>
								<reference key="NSSupport" ref="933596199"/>
								<reference key="NSControlView" ref="856256540"/>
								<int key="NSButtonFlags">-2038284033</int>
								<int key="NSButtonFlags2">129</int>
								<string key="NSAlternateContents"/>
								<string key="NSKeyEquivalent"/>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
						</object>
						<object class="NSButton" id="460755987">
							<reference key="NSNextResponder" ref="814272478"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{14, 12}, {292, 32}}</string>
							<reference key="NSSuperview" ref="814272478"/>
							<reference key="NSWindow"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="775425649">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Generate report without crash</string>
								<reference key="NSSupport" ref="933596199"/>
								<reference key="NSControlView" ref="460755987"/>
								<int key="NSButtonFlags">-2038284033</int>
								<int key="NSButtonFlags2">129</int>
								<string key="NSAlternateContents"/>
								<string key="NSKeyEquivalent"/>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
						</object>
					</object>
					<string key="NSFrameSize">{320, 188}</string>
					<reference key="NSSuperview"/>
					<reference key="NSWindow"/>
				</object>
				<string key="NSScreenRect">{{0, 0}, {1440, 878}}</string>
				<string key="NSMinSize">{213, 129}</string>
				<string key="NSMaxSize">{1.79769e+308, 1.79769e+308}</string>
			</object>
			<object class="NSMenu" id="695387251">
				<string key="NSTitle">MainMenu</string>
				<object class="NSMutableArray" key="NSMenuItems">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="NSMenuItem" id="458207250">
						<reference key="NSMenu" ref="695387251"/>
						<string key="NSTitle">NewApplication</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<object class="NSCustomResource" key="NSOnImage" id="419346806">
							<string key="NSClassName">NSImage</string>
							<string key="NSResourceName">NSMenuCheckmark</string>
						</object>
						<object class="NSCustomResource" key="NSMixedImage" id="290286705">
							<string key="NSClassName">NSImage</string>
							<string key="NSResourceName">NSMenuMixedState</string>
						</object>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="753534561">
							<string key="NSTitle">NewApplication</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="838552093">
									<reference key="NSMenu" ref="753534561"/>
									<string key="NSTitle">About NewApplication</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="758254482">
									<reference key="NSMenu" ref="753534561"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="443649494">
									<reference key="NSMenu" ref="753534561"/>
									<string key="NSTitle">Preferences…</string>
									<string key="NSKeyEquiv">,</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="217746140">
									<reference key="NSMenu" ref="753534561"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="826764396">
									<reference key="NSMenu" ref="753534561"/>
									<string key="NSTitle">Services</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="276709607">
										<object class="NSMutableString" key="NSTitle">
											<characters key="NS.bytes">Services</characters>
										</object>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
										</object>
										<string key="NSName">_NSServicesMenu</string>
									</object>
								</object>
								<object class="NSMenuItem" id="881859155">
									<reference key="NSMenu" ref="753534561"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="104472016">
									<reference key="NSMenu" ref="753534561"/>
									<string key="NSTitle">Hide NewApplication</string>
									<string key="NSKeyEquiv">h</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="216168366">
									<reference key="NSMenu" ref="753534561"/>
									<string key="NSTitle">Hide Others</string>
									<string key="NSKeyEquiv">h</string>
									<int key="NSKeyEquivModMask">1572864</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="667790509">
									<reference key="NSMenu" ref="753534561"/>
									<string key="NSTitle">Show All</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="928933982">
									<reference key="NSMenu" ref="753534561"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="887927135">
									<reference key="NSMenu" ref="753534561"/>
									<string key="NSTitle">Quit NewApplication</string>
									<string key="NSKeyEquiv">q</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
							</object>
							<string key="NSName">_NSAppleMenu</string>
						</object>
					</object>
					<object class="NSMenuItem" id="369472335">
						<reference key="NSMenu" ref="695387251"/>
						<string key="NSTitle">File</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="419346806"/>
						<reference key="NSMixedImage" ref="290286705"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="902982238">
							<object class="NSMutableString" key="NSTitle">
								<characters key="NS.bytes">File</characters>
							</object>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="660391032">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">New</string>
									<string key="NSKeyEquiv">n</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="367379562">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Open...</string>
									<string key="NSKeyEquiv">o</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="84883275">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Open Recent</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="693280130">
										<object class="NSMutableString" key="NSTitle">
											<characters key="NS.bytes">Open Recent</characters>
										</object>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="85018532">
												<reference key="NSMenu" ref="693280130"/>
												<string key="NSTitle">Clear Menu</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
											</object>
										</object>
										<string key="NSName">_NSRecentDocumentsMenu</string>
									</object>
								</object>
								<object class="NSMenuItem" id="154948703">
									<reference key="NSMenu" ref="902982238"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="468594275">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Close</string>
									<string key="NSKeyEquiv">w</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="479945444">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Save</string>
									<string key="NSKeyEquiv">s</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="976375553">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Save As…</string>
									<string key="NSKeyEquiv">S</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="885975128">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Revert</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="232609393">
									<reference key="NSMenu" ref="902982238"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="409810395">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Page Setup…</string>
									<string key="NSKeyEquiv">P</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="302505815">
									<reference key="NSMenu" ref="902982238"/>
									<string key="NSTitle">Print…</string>
									<string key="NSKeyEquiv">p</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
							</object>
						</object>
					</object>
					<object class="NSMenuItem" id="542216986">
						<reference key="NSMenu" ref="695387251"/>
						<string key="NSTitle">Edit</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="419346806"/>
						<reference key="NSMixedImage" ref="290286705"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="1053284541">
							<object class="NSMutableString" key="NSTitle">
								<characters key="NS.bytes">Edit</characters>
							</object>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="284548410">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Undo</string>
									<string key="NSKeyEquiv">z</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="1001272176">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Redo</string>
									<string key="NSKeyEquiv">Z</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="209744238">
									<reference key="NSMenu" ref="1053284541"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="909447496">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Cut</string>
									<string key="NSKeyEquiv">x</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="994487277">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Copy</string>
									<string key="NSKeyEquiv">c</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="84012734">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Paste</string>
									<string key="NSKeyEquiv">v</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="182251545">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Paste and Match Style</string>
									<string key="NSKeyEquiv">V</string>
									<int key="NSKeyEquivModMask">1572864</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="512189403">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Delete</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="917620781">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Select All</string>
									<string key="NSKeyEquiv">a</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="431895313">
									<reference key="NSMenu" ref="1053284541"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="153501847">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Find</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="333484665">
										<object class="NSMutableString" key="NSTitle">
											<characters key="NS.bytes">Find</characters>
										</object>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="203238834">
												<reference key="NSMenu" ref="333484665"/>
												<string key="NSTitle">Find…</string>
												<string key="NSKeyEquiv">f</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
												<int key="NSTag">1</int>
											</object>
											<object class="NSMenuItem" id="861312964">
												<reference key="NSMenu" ref="333484665"/>
												<string key="NSTitle">Find Next</string>
												<string key="NSKeyEquiv">g</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
												<int key="NSTag">2</int>
											</object>
											<object class="NSMenuItem" id="743767160">
												<reference key="NSMenu" ref="333484665"/>
												<string key="NSTitle">Find Previous</string>
												<string key="NSKeyEquiv">G</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
												<int key="NSTag">3</int>
											</object>
											<object class="NSMenuItem" id="180446588">
												<reference key="NSMenu" ref="333484665"/>
												<string key="NSTitle">Use Selection for Find</string>
												<string key="NSKeyEquiv">e</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
												<int key="NSTag">7</int>
											</object>
											<object class="NSMenuItem" id="731027425">
												<reference key="NSMenu" ref="333484665"/>
												<string key="NSTitle">Jump to Selection</string>
												<string key="NSKeyEquiv">j</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
											</object>
										</object>
									</object>
								</object>
								<object class="NSMenuItem" id="61602259">
									<reference key="NSMenu" ref="1053284541"/>
									<string key="NSTitle">Spelling</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="8174285">
										<string key="NSTitle">Spelling</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="438210660">
												<reference key="NSMenu" ref="8174285"/>
												<string key="NSTitle">Spelling…</string>
												<string key="NSKeyEquiv">:</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
											</object>
											<object class="NSMenuItem" id="102172584">
												<reference key="NSMenu" ref="8174285"/>
												<string key="NSTitle">Check Spelling</string>
												<string key="NSKeyEquiv">;</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
											</object>
											<object class="NSMenuItem" id="540509341">
												<reference key="NSMenu" ref="8174285"/>
												<string key="NSTitle">Check Spelling as You Type</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="419346806"/>
												<reference key="NSMixedImage" ref="290286705"/>
											</object>
										</object>
									</object>
								</object>
							</object>
						</object>
					</object>
					<object class="NSMenuItem" id="764068863">
						<reference key="NSMenu" ref="695387251"/>
						<string key="NSTitle">Window</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="419346806"/>
						<reference key="NSMixedImage" ref="290286705"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="200536676">
							<object class="NSMutableString" key="NSTitle">
								<characters key="NS.bytes">Window</characters>
							</object>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="683986939">
									<reference key="NSMenu" ref="200536676"/>
									<string key="NSTitle">Minimize</string>
									<string key="NSKeyEquiv">m</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="548098734">
									<reference key="NSMenu" ref="200536676"/>
									<string key="NSTitle">Zoom</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="297002686">
									<reference key="NSMenu" ref="200536676"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
								<object class="NSMenuItem" id="164762492">
									<reference key="NSMenu" ref="200536676"/>
									<string key="NSTitle">Bring All to Front</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
							</object>
							<string key="NSName">_NSWindowsMenu</string>
						</object>
					</object>
					<object class="NSMenuItem" id="599772536">
						<reference key="NSMenu" ref="695387251"/>
						<string key="NSTitle">Help</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="419346806"/>
						<reference key="NSMixedImage" ref="290286705"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="1066958924">
							<string key="NSTitle">Help</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="192540884">
									<reference key="NSMenu" ref="1066958924"/>
									<string key="NSTitle">NewApplication Help</string>
									<string key="NSKeyEquiv">?</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="419346806"/>
									<reference key="NSMixedImage" ref="290286705"/>
								</object>
							</object>
						</object>
					</object>
				</object>
				<string key="NSName">_NSMainMenu</string>
			</object>
			<object class="NSCustomObject" id="623097029">
				<string key="NSClassName">Controller</string>
			</object>
			<object class="NSWindowTemplate" id="347013037">
				<int key="NSWindowStyleMask">15</int>
				<int key="NSWindowBacking">2</int>
				<string key="NSWindowRect">{{858, 755}, {787, 260}}</string>
				<int key="NSWTFlags">603979776</int>
				<string key="NSWindowTitle">Window</string>
				<string key="NSWindowClass">NSWindow</string>
				<nil key="NSViewClass"/>
				<string key="NSWindowContentMaxSize">{1.79769e+308, 1.79769e+308}</string>
				<object class="NSView" key="NSWindowView" id="594333702">
					<reference key="NSNextResponder"/>
					<int key="NSvFlags">256</int>
					<object class="NSMutableArray" key="NSSubviews">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSMatrix" id="891367997">
							<reference key="NSNextResponder" ref="594333702"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{20, 7}, {645, 79}}</string>
							<reference key="NSSuperview" ref="594333702"/>
							<bool key="NSEnabled">YES</bool>
							<int key="NSNumRows">3</int>
							<int key="NSNumCols">1</int>
							<object class="NSMutableArray" key="NSCells">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSButtonCell" id="410017819">
									<int key="NSCellFlags">-2080244224</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">program crashes during launch because of missing dylib</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="891367997"/>
									<int key="NSTag">5</int>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<object class="NSButtonImageSource" key="NSAlternateImage" id="619763889">
										<string key="NSImageName">NSRadioButton</string>
									</object>
									<string key="NSAlternateContents"/>
									<string key="NSKeyEquivalent"/>
									<int key="NSPeriodicDelay">200</int>
									<int key="NSPeriodicInterval">25</int>
								</object>
								<object class="NSButtonCell" id="904578786">
									<int key="NSCellFlags">67239424</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">program crashes after launch</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="891367997"/>
									<int key="NSTag">6</int>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<object class="NSImage" key="NSNormalImage">
										<int key="NSImageFlags">549453824</int>
										<string key="NSSize">{18, 18}</string>
										<object class="NSMutableArray" key="NSReps">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSArray">
												<bool key="EncodedWithXMLCoder">YES</bool>
												<integer value="0"/>
												<object class="NSBitmapImageRep">
													<object class="NSData" key="NSTIFFRepresentation">
														<bytes key="NS.bytes">TU0AKgAABRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAADwRERGLJycnySsrK/A1NTXw
IyMjyRwcHIsJCQk8AAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFRUVdVBQUOCoqKj/
29vb//n5+f/6+vr/2tra/6qqqv9UVFTgHx8fdQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUZGRl5
dXV198PDw//8/Pz////////////////////////////U1NT/fHx89yUlJXkAAAAFAAAAAAAAAAAAAAAA
AAAAAxEREUZqamrmtbW1/+3t7f/+/v7//v7+//7+/v/9/f3//f39//39/f/39/f/xMTE/3d3d+YZGRlG
AAAAAwAAAAAAAAAAAAAACkJCQqGtra3/xsbG/+vr6//y8vL/9fX1//X19f/z8/P/9fX1//Ly8v/u7u7/
0tLS/6+vr/9KSkqhAAAACgAAAAAAAAAAAAAAF3h4eN2/v7//z8/P/93d3f/q6ur/7+/v/+/v7//w8PD/
7e3t/+3t7f/i4uL/zs7O/8XFxf98fHzdAAAAFwAAAAAAAAADAAAAJKSkpPjOzs7/2dnZ/+Dg4P/i4uL/
5eXl/+bm5v/n5+f/5eXl/+Li4v/e3t7/2tra/9DQ0P+srKz4AAAAJAAAAAMAAAADAAAALrCwsPrW1tb/
3t7e/+Tk5P/p6en/6+vr/+zs7P/p6en/6+vr/+fn5//k5OT/4ODg/9nZ2f+zs7P6AAAALgAAAAMAAAAD
AAAALp2dnezg4OD/5eXl/+rq6v/u7u7/8PDw//Dw8P/x8fH/8PDw/+7u7v/q6ur/5ubm/+Hh4f+ZmZns
AAAALgAAAAMAAAADAAAAJG5ubs/l5eX/6enp/+/v7//y8vL/9vb2//r6+v/5+fn/9/f3//b29v/x8fH/
6+vr/+Tk5P9ra2vPAAAAJAAAAAMAAAAAAAAAFy4uLpPCwsL67Ozs//Pz8//5+fn//v7+//7+/v/+/v7/
/v7+//v7+//19fX/8PDw/8LCwvosLCyTAAAAFwAAAAAAAAAAAAAACgAAAENfX1/S5OTk/vn5+f/+/v7/
///////////////////////////8/Pz/5ubm/l9fX9IAAABDAAAACgAAAAAAAAAAAAAAAwAAABcAAABl
YmJi3NLS0v3////////////////////////////////V1dX9ZGRk3AAAAGUAAAAXAAAAAwAAAAAAAAAA
AAAAAAAAAAUAAAAfAAAAZTMzM8KAgIDwv7+//O3t7f/t7e3/v7+//ICAgPAzMzPCAAAAZQAAAB8AAAAF
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAFwAAAEMAAAB3AAAAnwAAALMAAACzAAAAnwAAAHcAAABD
AAAAFwAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAoAAAAXAAAAJAAAAC4AAAAu
AAAAJAAAABcAAAAKAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAwAAAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgEAAAMAAAABABIAAAEB
AAMAAAABABIAAAECAAMAAAAEAAAFxgEDAAMAAAABAAEAAAEGAAMAAAABAAIAAAERAAQAAAABAAAACAES
AAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABABIAAAEXAAQAAAABAAAFEAEcAAMAAAABAAEAAAFS
AAMAAAABAAEAAAFTAAMAAAAEAAAFzodzAAcAAAwYAAAF1gAAAAAACAAIAAgACAABAAEAAQABAAAMGGFw
cGwCAAAAbW50clJHQiBYWVogB9YABAADABMALAASYWNzcEFQUEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAPbWAAEAAAAA0y1hcHBsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAOclhZWgAAASwAAAAUZ1hZWgAAAUAAAAAUYlhZWgAAAVQAAAAUd3RwdAAAAWgAAAAUY2hhZAAA
AXwAAAAsclRSQwAAAagAAAAOZ1RSQwAAAbgAAAAOYlRSQwAAAcgAAAAOdmNndAAAAdgAAAMSbmRpbgAA
BOwAAAY+ZGVzYwAACywAAABkZHNjbQAAC5AAAAAubW1vZAAAC8AAAAAoY3BydAAAC+gAAAAtWFlaIAAA
AAAAAF1KAAA0kQAACCVYWVogAAAAAAAAdCAAALRgAAAjPVhZWiAAAAAAAAAlbAAAFyoAAKfDWFlaIAAA
AAAAAPNSAAEAAAABFs9zZjMyAAAAAAABDEIAAAXe///zJgAAB5IAAP2R///7ov///aMAAAPcAADAbGN1
cnYAAAAAAAAAAQHNAABjdXJ2AAAAAAAAAAEBzQAAY3VydgAAAAAAAAABAc0AAHZjZ3QAAAAAAAAAAAAD
AQAAAQACBAUGBwkKCw0ODxASExQWFxgaGxweHyAiIyQmJygpKywtLzAxMjM1Njc4OTs8PT5AQUJDREZH
SElKS0xOT1BRUlNUVVZXWFlaW1xdXl9hYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ent8fX5/gIGCg4SF
hoeIiYqLjI2Oj5CRkpOUlZaXmJmam5ydnZ6foKGio6SlpqanqKmqq6ytra6vsLGysrO0tba3uLi5uru8
vL2+v8DBwcLDxMXGxsfIycrKy8zNzs7P0NHS0tPU1dbW19jZ2drb3Nzd3t/g4eLi4+Tl5ufo6enq6+zt
7u/w8fHy8/T19vf4+fr7/P3+/v8AAgMEBQYHCAkKCwwNDg8QERITFBUWFxgZGhscHR8gISIjJCUnKCkq
Ky0uLzAxMzQ1Njc4OTo7PD0/QEFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaWltcXV5fYGFiY2RlZmdo
aWprbG1ub3BxcnN0dXZ3d3h5ent8fH1+f4CBgoKDhIWGh4iIiYqLjI2Oj5CRkpOUlJWWl5iZmpucnZ2e
n6ChoqOkpaamp6ipqqusra6vsLCxsrO0tba3uLm5uru8vb6/wMHCw8TFx8jJysvMzc7P0NDR0tPU1dbX
2Nna29ze3+Dh4uPk5ebn6err7O3u7/Hy8/T19vf5+vv8/f7/AAIDAwQFBgcICQoKCwwNDg8QERITFBUW
FxgZGhscHR4fICEiIyQlJicoKSorLC0uLzAxMjM0NTY3ODg5Ojs8PT4+P0BBQkNDREVGR0hJSUpLTE1O
Tk9QUVJSU1RVVVZXWFhZWltbXF1eXl9gYWFiY2RkZWZnZ2hpaWprbGxtbm5vcHFxcnNzdHV1dnd4eHl6
ent8fH1+fn+AgYGCg4SEhYaHiImJiouMjY6Oj5CRkpOTlJWWl5iZmZqbnJ2en6ChoqOkpaanqKmqq6yt
rq+xsrO0tba3uLq7vL2+wMHDxMbHycrMzs/R0tTW19nb3d7g4uTm6Ors7vDy9Pb4+vz+/wAAbmRpbgAA
AAAAAAY2AACXGgAAVjoAAFPKAACJ3gAAJ8IAABaoAABQDQAAVDkAAiuFAAIZmQABeFEAAwEAAAIAAAAA
AAEABgANABcAIwAxAEAAUgBlAHsAkwCrAMUA4gD/AR8BPwFhAYUBqgHQAfgCIAJLAncCpQLSAwIDMwNl
A5gDzgQFBD0EdQSvBOsFKQVnBacF6AYqBm4GtQb8B0UHkgfkCDkIkAjnCT4JmAn0ClAKrQsLC2sLygwq
DIwM8Q1XDcAOKA6SDv4PbA/bEE0QxBE7EbQSMRKwEzITuRREFNAVYBXxFocXHhfAGGIZBBmsGlQa+RuU
HC4czh1yHhQeux9jIA0gvCFoIhkizyOJJEEk+SW6JnknOygFKMspkypiKzIsASzXLawuhy9gMD4xGzH8
MtszvzSgNYY2cjdcOEw5OTorOxs8CD0EPfU+6z/nQOFB2ELUQ9VE00XcRttH5EjxSgBLCUwdTTFOUE9v
UI9Rt1LdVAVVNlZsV6VY4FohW21ct135X09goGH0Y0tkqGYFZ19oxGova5ptCG54b/BxbnLsdG119Xd/
eQh6knwqfcV/W4D4gpSEO4Xih4CJKorYjIqOOY/jkZuTWJUOlsyYiZpSnB6d4Z+soX+jWqUvpxOo+6rj
rMuuwLC4sra0rra0uL+60LzfvwDBHcLdxLXGhchYyi7MCs3lz7rRmtOA1WPXR9kq2xPc/97s4M/iveSn
5o3obupT7ELuLPAM8fLz0PW396H5f/tZ/T3//wAAAAEAAwALABYAJQA3AE0AZQCBAJ8AwQDlAQsBNQFh
AZABwQH1AisCZAKfAtwDHANfA6MD6gQ0BH8EzQT1BR0FcAXEBhsGdAbPBy0HXAeMB+4IUgi4CSAJVAmK
CfYKZArVC0cLgQu8DDIMqw0mDaIOIQ6hDyQPqRAvELgQ/RFDEc8SXRLuE4AUFRSrFUMV3RZ5FxcXthhY
GPwZoRpIGvEbnBxJHPgdqB5bHw8fxSB9ITch8iKwJDAk8yW3Jn4nRigQKNwpqSp5K0osHCzxLccuoC95
MFUxMzISMvMz1TS5NaA2hzdxOFw5STo4Oyg8Gj4DPvs/9EDuQepD6ETpRexG8Uf3SP9LFEwhTTBOQE9S
UGZSklOrVMVV4Vb/WB5ZP1phW4Vcq13SXvthUmJ/Y69k4GYSZ0dofGm0au1tZG6ib+FxInJlc6l073Y2
d396FXtjfLJ+A39VgKmB/4NWhK+GCYjCiiGLgYzjjkePrJESknuT5Ja8mCuZm5sMnH+d9J9qoOGiWqPV
pVGmz6eOqE6pzqtRrNSuWq/gsWmy8rR+tgu5Kbq6vE294b93wQ7Cp8RBxd3He8kZyrrLisxbzf/Po9FK
0vHUm9ZF1/HZn9tO3Cbc/96x4GTiGePQ5YjnQegf6Pzquex27jbv9/G583z0X/VC9wj40Pqa/GX+Mf//
AAAAAQADAAsAJQA3AE0AZQCBAJ8AwQELATUBYQGQAcEB9QIrAmQCnwLcAxwDXwOjA+oENAR/BM0FHQVw
BcQGGwZ0Bs8HLQeMB+4IUgi4CSAJign2CmQK1QtHC7wMMgyrDSYNog4hDqEPJA+pEC8QuBFDEl0S7hOA
FBUUqxVDFnkXFxe2GFgY/BpIGvEbnBxJHPgdqB8PH8UgfSE3IfIjbyQwJPMltydGKBAo3Cp5K0osHC3H
LqAveTEzMhIy8zS5NaA2hzhcOUk6ODwaPQ4+Az/0QO5C6EPoROlG8Uf3SglLFEwhTkBPUlF7UpJUxVXh
Vv9ZP1phXKtd0mAlYVJjr2TgZhJofGm0au1tZG6ib+FxInJldO92Nnd/eMl6FXyyfgN/VYCpgf+Er4YJ
h2WIwoohi4GOR4+skRKSe5PklVCWvJgrmZubDJx/nfSfaqDholqj1aVRps+oTqnOq1Gs1K2Xrlqv4LFp
svK0frYLt5m5Kbnxurq8Tb3hv3fBDsHawqfEQcUPxd3He8hKyRnKusuKzFvN/87Rz6PQdtFK0vHTxtSb
1kXXG9fx2MjZn9tO3Cbc/93Y3rHfiuBk4hni9ePQ5KzliOZk50HoH+j86drqueuX7HbtVu427xbv9/DX
8bnymvN89F/1QvYl9wj37PjQ+bX6mvt//GX9S/4x//8AAGRlc2MAAAAAAAAACkNvbG9yIExDRAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAABIAAAAcAEMAbwBsAG8AcgAgAEwAQwBE
AABtbW9kAAAAAAAABhAAAJxOAAAAAL5zkQAAAAAAAAAAAAAAAAAAAAAAdGV4dAAAAABDb3B5cmlnaHQg
QXBwbGUgQ29tcHV0ZXIsIEluYy4sIDIwMDUAAAAAA</bytes>
													</object>
												</object>
											</object>
										</object>
										<object class="NSColor" key="NSColor" id="30384615">
											<int key="NSColorSpace">3</int>
											<bytes key="NSWhite">MCAwAA</bytes>
										</object>
									</object>
									<reference key="NSAlternateImage" ref="619763889"/>
									<int key="NSPeriodicDelay">400</int>
									<int key="NSPeriodicInterval">75</int>
								</object>
								<object class="NSButtonCell" id="971445237">
									<int key="NSCellFlags">67239424</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">program crashes in between fork() and exec() (3rd option in first group will happen before crash)</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="891367997"/>
									<int key="NSTag">7</int>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<object class="NSImage" key="NSNormalImage">
										<int key="NSImageFlags">549453824</int>
										<string key="NSSize">{18, 18}</string>
										<object class="NSMutableArray" key="NSReps">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSArray">
												<bool key="EncodedWithXMLCoder">YES</bool>
												<integer value="0"/>
												<object class="NSBitmapImageRep">
													<object class="NSData" key="NSTIFFRepresentation">
														<bytes key="NS.bytes">TU0AKgAABRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAADwRERGLJycnySsrK/A1NTXw
IyMjyRwcHIsJCQk8AAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFRUVdVBQUOCoqKj/
29vb//n5+f/6+vr/2tra/6qqqv9UVFTgHx8fdQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUZGRl5
dXV198PDw//8/Pz////////////////////////////U1NT/fHx89yUlJXkAAAAFAAAAAAAAAAAAAAAA
AAAAAxEREUZqamrmtbW1/+3t7f/+/v7//v7+//7+/v/9/f3//f39//39/f/39/f/xMTE/3d3d+YZGRlG
AAAAAwAAAAAAAAAAAAAACkJCQqGtra3/xsbG/+vr6//y8vL/9fX1//X19f/z8/P/9fX1//Ly8v/u7u7/
0tLS/6+vr/9KSkqhAAAACgAAAAAAAAAAAAAAF3h4eN2/v7//z8/P/93d3f/q6ur/7+/v/+/v7//w8PD/
7e3t/+3t7f/i4uL/zs7O/8XFxf98fHzdAAAAFwAAAAAAAAADAAAAJKSkpPjOzs7/2dnZ/+Dg4P/i4uL/
5eXl/+bm5v/n5+f/5eXl/+Li4v/e3t7/2tra/9DQ0P+srKz4AAAAJAAAAAMAAAADAAAALrCwsPrW1tb/
3t7e/+Tk5P/p6en/6+vr/+zs7P/p6en/6+vr/+fn5//k5OT/4ODg/9nZ2f+zs7P6AAAALgAAAAMAAAAD
AAAALp2dnezg4OD/5eXl/+rq6v/u7u7/8PDw//Dw8P/x8fH/8PDw/+7u7v/q6ur/5ubm/+Hh4f+ZmZns
AAAALgAAAAMAAAADAAAAJG5ubs/l5eX/6enp/+/v7//y8vL/9vb2//r6+v/5+fn/9/f3//b29v/x8fH/
6+vr/+Tk5P9ra2vPAAAAJAAAAAMAAAAAAAAAFy4uLpPCwsL67Ozs//Pz8//5+fn//v7+//7+/v/+/v7/
/v7+//v7+//19fX/8PDw/8LCwvosLCyTAAAAFwAAAAAAAAAAAAAACgAAAENfX1/S5OTk/vn5+f/+/v7/
///////////////////////////8/Pz/5ubm/l9fX9IAAABDAAAACgAAAAAAAAAAAAAAAwAAABcAAABl
YmJi3NLS0v3////////////////////////////////V1dX9ZGRk3AAAAGUAAAAXAAAAAwAAAAAAAAAA
AAAAAAAAAAUAAAAfAAAAZTMzM8KAgIDwv7+//O3t7f/t7e3/v7+//ICAgPAzMzPCAAAAZQAAAB8AAAAF
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAFwAAAEMAAAB3AAAAnwAAALMAAACzAAAAnwAAAHcAAABD
AAAAFwAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAoAAAAXAAAAJAAAAC4AAAAu
AAAAJAAAABcAAAAKAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAwAAAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgEAAAMAAAABABIAAAEB
AAMAAAABABIAAAECAAMAAAAEAAAFxgEDAAMAAAABAAEAAAEGAAMAAAABAAIAAAERAAQAAAABAAAACAES
AAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABABIAAAEXAAQAAAABAAAFEAEcAAMAAAABAAEAAAFS
AAMAAAABAAEAAAFTAAMAAAAEAAAFzodzAAcAAAv0AAAF1gAAAAAACAAIAAgACAABAAEAAQABAAAL9GFw
cGwCAAAAbW50clJHQiBYWVogB9gAAgAMAAoAFgAIYWNzcEFQUEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAPbWAAEAAAAA0y1hcHBs625VECyhxeSV9P9A73pKGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAANclhZWgAAASAAAAAUZ1hZWgAAATQAAAAUYlhZWgAAAUgAAAAUd3RwdAAAAVwAAAAUY2hhZAAA
AXAAAAAsclRSQwAAAZwAAAAOZ1RSQwAAAawAAAAOYlRSQwAAAbwAAAAOdmNndAAAAcwAAAYSbmRpbgAA
B+AAAAMOZGVzYwAACvAAAACZY3BydAAAC4wAAABAbW1vZAAAC8wAAAAoWFlaIAAAAAAAAJumAABMVQAA
ArBYWVogAAAAAAAANWMAAJ/rAAAZsVhZWiAAAAAAAAAlzQAAE9UAALbFWFlaIAAAAAAAAPPYAAEAAAAB
FghzZjMyAAAAAAABC7cAAAWW///zVwAABykAAP3X///7t////aYAAAPaAADA9mN1cnYAAAAAAAAAAQHN
AABjdXJ2AAAAAAAAAAEBzQAAY3VydgAAAAAAAAABAc0AAHZjZ3QAAAAAAAAAAAADAQAAAgAAAioENAYA
B9AJlAtRDQwOshBOEekTgxUVFqMYMRm9GzwcvR4+H7chLiKnJBoliCb3KGIpyCswLJMt9C9WMLIyDjNn
NL02FTdoOLs6ETtdPKw9+D9DQJBB1kMeRGZFq0bySDRJeEq6S/tNPk59T75Q+lI2U25Uo1XZVwlYOlln
WpZbwFzsXhdfQWBrYZRiv2PoZRNmPWdqaJhpyGr5bC1tZW6hb+BxLXJ+c9d1OHafeA55gnr5fHR98H9t
gOuCY4PYhUqGsIgSiWyKuIwBjTqObY+VkLORyJLUk9eU05XFlrWXlZh2mUuaG5rsm6ucbJ0qndqei588
n9+ghKEqoceiYqL/o5qkLqTDpVml66Z7pwunnKgpqLWpQqnQqlqq5Ktvq/qsg60MrZauIa6qrzOvvbBH
sNGxXLHnsnKy/7OMtBm0p7U3tci2Wbbrt4C4FrituUa54rqAux27wbxlvQq9rr5Tvvi/ncBAwOXBisIu
wtPDeMQdxMLFaMYPxrXHXMgEyKzJVcn/yqnLVcwBzK7NXc4Mzr3PcNAk0NnRkdJK0wTTw9SC1UTWCtbR
153Ybdk+2hfa8dvM3KfdhN5g3zzgGuD34dbiteOV5HblWeY85yHoCOjw6drqxuu27Kbtm+6R74zwivGM
8pPzn/Sw9cj25/gP+T76e/u//Rr+hP//AAABpANzBRoGsggnCZsLFQx+Dd4PRRCiEf8TYxS0FgoXXRiu
GgQbTRyZHekfMCB8IcIjCSRSJZUm3SgdKWAqpCvjLSYuZC+lMOIyIDNgNJs12TcTOFA5izrEO/49NT5w
P6dA30IWQ01Eg0W4Ru9IIElVSoZLt0zmThVPRFBwUZ5SylP5VSRWUVd+WKtZ2lsIXDhdaV6bX89hBWI8
Y3dktGX0ZzhohGnVayxsiW3sb1Vww3I0c6p1I3aeeBl5k3sMfIR99X9kgM6CLYOJhNyGJodriKGJ1Ir5
jBuNL45Aj0WQSJE8kjKTGJP+lNyVspaKl1OYHZjlmaSaZJshm9ecj51EnfSepJ9Vn/+gqqFWofyio6NL
o/CklKU4pdymfacfp8KoYqkDqaWqRarlq4asJ6zHrWiuCq6rr02v77CSsTax27KAsyezzrR3tSG1zLZ5
tye32LiKuT659rqwu2q8J7zkvaK+YL8ev93AnMFcwhzC3MOdxF7FIMXixqXHaMgryPDJtcp6y0DMB8zO
zZbOX88oz/PQvtGJ0lbTI9Px1MDVkNZi1zTYB9jb2bDah9te3DjdEt3t3sjfpOB/4VviN+MT4/DkzeWq
5ojnZuhG6SXqBuro68rsre2S7nfvXvBH8TDyHPMI8/j06fXc9tL3yvjF+cL6w/vG/ND92v7s//8AAAMJ
BboIZwrCDSsPghG8E/IWHxg5GkgcVB5VIEQiMyQTJeknuimHK00tCy7AMHEyHDO/NV829ziKOhs7pj0s
PrBALEGmQx9EkkYCR3JI3EpCS6pND05vT89RLVKKU+dVP1aYV+9ZRVqdW/NdSV6hX+thM2JzY61k42YS
Z0FoZ2mOaq5rz2zsbglvI3A9cVRybHOEdJx1tHbOd+d5A3ofez98Yn2Lfrl/8IEqgmyDsoT8hkuHnYjw
ikSLmYzsjj+PjJDWkh2TW5SXlceW85gWmTOaSJtVnFqdWp5Pn0SgKaEQoeuiwqOYpGClKaXtpqmnZqgf
qNKph6o5quWrk6xArOatja41rtevebAcsLyxWbH3spWzL7PJtGO0/LWTtiq2wrdWt+q4f7kUuaW6OLrL
u1277Lx9vQ69nr4tvry/TL/bwGjA98GGwhPCocMvw77ESsTYxWXF9MZ/xwzHmcgkyKXJJ8mpyizKpMsc
y5XMDsyFzPjNa83fzlPOxc81z6fQGNCK0PvRbNHe0lDSw9M206vUINSV1QvVhtYA1nzW+Nd61//YhNkK
2ZnaL9rG213cCty63WveI97d35fgUuEO4crih+NE5ALkw+WD5kXnCufP6JbpYOor6vrry+yd7XbuUe8w
8BXw+/Ht8uDz4PTl9fj3E/hE+X363vxa/gH//wAAbmRpbgAAAAAAAAMGAACogAAAUwAAADRAAACqQAAA
JpcAABLbAABQQAAAVEAAAj99AAI1egACxUsAAwB4AAIAAAADAAsAGQAsAEUAYwCHALEA4QEWAVEBkgHZ
AiYCeQLSAzEDlwQDBHYE7wVvBfUGgwcXB7IIUwj8CawKYgsgC+QMrw2BDloPORAfEQ0SBRMGFBEVJBZA
F2MYjhm/GvYcMh1xHrMf9SE1ImwjnSTJJfAnFig7KWMqjivBLP4uSC+jMRMymzRBNgo3+joWPGY+8EG8
RNhIQEvvT95UCFhkXOxhlWZYaylv/XTKeYN+GoKOhxGLqJBOlP6Ztp5voyan1Kx0sQG1c7nGvfHB9cX7
ygbOFNIi1izaMN4p4hTl7emv7Vbw3vRC93z6iP1g//8AAAAEAA8AIgA9AF8AiQC7APQBNAF8AcwCIgKB
AuYDUwPHBEIExAVOBd4GdgcUB7oIZgkaCdQKlQteDC0NAw3gDsQPrxCiEZwSnxOpFLsV0xbyGBcZQRpw
G6Mc2R4RH0oggyG3IuckEyU8JmQniyiyKd0rDCxBLYAuyTAhMYkzBjSbNkw4HToSPDA+fED8Q7FGmUmx
TPdQaFQAV7tbll+KY5RnrmvTb/p0H3g6fEOAMoQXiASL+I/yk/KX+JwBoA2kHKgrrDuwS7RYuGK8aMBo
xGvId8yI0J/UuNjS3OvhAOUQ6RftE/EC9OH4rfxi//8AAAABAAYADQAXACUANQBIAF8AeQCWALcA3AEE
ATABYQGVAc4CDAJOApUC4QMyA4gD5QRGBK4FHAWPBgkGigcQB54IMQjMCW0KFArDC3cMMgzzDbsOiA9a
EDIRFhIIEwgUFRUvFlYXhxjDGgkbVhyqHgMfYCC8IhIjYSSrJfMnOSiBKc0rHyx7LeQvXTDrMpE0VTY7
OEg6gjzuP5NCckWCSMJMMk/QU5xXk1u1X/9kcGkGbb5ylneLfJqByoeDjcyUf5t4oo2plLBetru8eMFr
xirK7c+v1GnZFd2r4iXme+ql7pryUvXD+OT7qv4M//8AAGRlc2MAAAAAAAAAFUhQIExQMzA2NSBDYWxp
YnJhdGVkAAAAAAAAAAAVAEgAUAAgAEwAUAAzADAANgA1ACAAQwBhAGwAaQBiAHIAYQB0AGUAZAAAAAAV
SFAgTFAzMDY1IENhbGlicmF0ZWQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAHRleHQAAAAAQ29weXJpZ2h0IEFwcGxlIEluYy4sIDIwMDgAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAABtbW9kAAAAAAAAIvAAACaQAAAAAMJtVwAAAAAAAAAAAAAAAAAAAAAAA</bytes>
													</object>
												</object>
											</object>
										</object>
										<reference key="NSColor" ref="30384615"/>
									</object>
									<reference key="NSAlternateImage" ref="619763889"/>
									<string key="NSAlternateContents"/>
									<int key="NSPeriodicDelay">200</int>
									<int key="NSPeriodicInterval">25</int>
								</object>
							</object>
							<string key="NSCellSize">{645, 25}</string>
							<string key="NSIntercellSpacing">{4, 2}</string>
							<int key="NSMatrixFlags">1151868928</int>
							<string key="NSCellClass">NSActionCell</string>
							<object class="NSButtonCell" key="NSProtoCell" id="1072218638">
								<int key="NSCellFlags">-2080244224</int>
								<int key="NSCellFlags2">0</int>
								<string key="NSContents">program crashes after launch</string>
								<reference key="NSSupport" ref="933596199"/>
								<int key="NSTag">5</int>
								<int key="NSButtonFlags">1211912703</int>
								<int key="NSButtonFlags2">0</int>
								<reference key="NSAlternateImage" ref="619763889"/>
								<string key="NSAlternateContents"/>
								<string key="NSKeyEquivalent"/>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
							<reference key="NSSelectedCell" ref="410017819"/>
							<object class="NSColor" key="NSBackgroundColor" id="349124561">
								<int key="NSColorSpace">6</int>
								<string key="NSCatalogName">System</string>
								<string key="NSColorName">controlColor</string>
								<object class="NSColor" key="NSColor">
									<int key="NSColorSpace">3</int>
									<bytes key="NSWhite">MC42NjY2NjY2NjY3AA</bytes>
								</object>
							</object>
							<object class="NSColor" key="NSCellBackgroundColor" id="195671423">
								<int key="NSColorSpace">3</int>
								<bytes key="NSWhite">MQA</bytes>
							</object>
							<reference key="NSFont" ref="933596199"/>
						</object>
						<object class="NSMatrix" id="7590393">
							<reference key="NSNextResponder" ref="594333702"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{20, 170}, {565, 70}}</string>
							<reference key="NSSuperview" ref="594333702"/>
							<bool key="NSEnabled">YES</bool>
							<int key="NSNumRows">3</int>
							<int key="NSNumCols">1</int>
							<object class="NSMutableArray" key="NSCells">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSButtonCell" id="808388382">
									<int key="NSCellFlags">-2080244224</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">Leave breakpad alone before fork</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="7590393"/>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<reference key="NSAlternateImage" ref="619763889"/>
									<string key="NSAlternateContents"/>
									<string key="NSKeyEquivalent"/>
									<int key="NSPeriodicDelay">200</int>
									<int key="NSPeriodicInterval">25</int>
								</object>
								<object class="NSButtonCell" id="378736460">
									<int key="NSCellFlags">67239424</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">Uninitialize Breakpad before fork</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="7590393"/>
									<int key="NSTag">1</int>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<object class="NSImage" key="NSNormalImage">
										<int key="NSImageFlags">549453824</int>
										<string key="NSSize">{18, 18}</string>
										<object class="NSMutableArray" key="NSReps">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSArray">
												<bool key="EncodedWithXMLCoder">YES</bool>
												<integer value="0"/>
												<object class="NSBitmapImageRep">
													<object class="NSData" key="NSTIFFRepresentation">
														<bytes key="NS.bytes">TU0AKgAABRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAADwRERGLJycnySsrK/A1NTXw
IyMjyRwcHIsJCQk8AAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFRUVdVBQUOCoqKj/
29vb//n5+f/6+vr/2tra/6qqqv9UVFTgHx8fdQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUZGRl5
dXV198PDw//8/Pz////////////////////////////U1NT/fHx89yUlJXkAAAAFAAAAAAAAAAAAAAAA
AAAAAxEREUZqamrmtbW1/+3t7f/+/v7//v7+//7+/v/9/f3//f39//39/f/39/f/xMTE/3d3d+YZGRlG
AAAAAwAAAAAAAAAAAAAACkJCQqGtra3/xsbG/+vr6//y8vL/9fX1//X19f/z8/P/9fX1//Ly8v/u7u7/
0tLS/6+vr/9KSkqhAAAACgAAAAAAAAAAAAAAF3h4eN2/v7//z8/P/93d3f/q6ur/7+/v/+/v7//w8PD/
7e3t/+3t7f/i4uL/zs7O/8XFxf98fHzdAAAAFwAAAAAAAAADAAAAJKSkpPjOzs7/2dnZ/+Dg4P/i4uL/
5eXl/+bm5v/n5+f/5eXl/+Li4v/e3t7/2tra/9DQ0P+srKz4AAAAJAAAAAMAAAADAAAALrCwsPrW1tb/
3t7e/+Tk5P/p6en/6+vr/+zs7P/p6en/6+vr/+fn5//k5OT/4ODg/9nZ2f+zs7P6AAAALgAAAAMAAAAD
AAAALp2dnezg4OD/5eXl/+rq6v/u7u7/8PDw//Dw8P/x8fH/8PDw/+7u7v/q6ur/5ubm/+Hh4f+ZmZns
AAAALgAAAAMAAAADAAAAJG5ubs/l5eX/6enp/+/v7//y8vL/9vb2//r6+v/5+fn/9/f3//b29v/x8fH/
6+vr/+Tk5P9ra2vPAAAAJAAAAAMAAAAAAAAAFy4uLpPCwsL67Ozs//Pz8//5+fn//v7+//7+/v/+/v7/
/v7+//v7+//19fX/8PDw/8LCwvosLCyTAAAAFwAAAAAAAAAAAAAACgAAAENfX1/S5OTk/vn5+f/+/v7/
///////////////////////////8/Pz/5ubm/l9fX9IAAABDAAAACgAAAAAAAAAAAAAAAwAAABcAAABl
YmJi3NLS0v3////////////////////////////////V1dX9ZGRk3AAAAGUAAAAXAAAAAwAAAAAAAAAA
AAAAAAAAAAUAAAAfAAAAZTMzM8KAgIDwv7+//O3t7f/t7e3/v7+//ICAgPAzMzPCAAAAZQAAAB8AAAAF
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAFwAAAEMAAAB3AAAAnwAAALMAAACzAAAAnwAAAHcAAABD
AAAAFwAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAoAAAAXAAAAJAAAAC4AAAAu
AAAAJAAAABcAAAAKAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAwAAAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgEAAAMAAAABABIAAAEB
AAMAAAABABIAAAECAAMAAAAEAAAFxgEDAAMAAAABAAEAAAEGAAMAAAABAAIAAAERAAQAAAABAAAACAES
AAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABABIAAAEXAAQAAAABAAAFEAEcAAMAAAABAAEAAAFS
AAMAAAABAAEAAAFTAAMAAAAEAAAFzodzAAcAAAwYAAAF1gAAAAAACAAIAAgACAABAAEAAQABAAAMGGFw
cGwCAAAAbW50clJHQiBYWVogB9YABAADABMALAASYWNzcEFQUEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAPbWAAEAAAAA0y1hcHBsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAOclhZWgAAASwAAAAUZ1hZWgAAAUAAAAAUYlhZWgAAAVQAAAAUd3RwdAAAAWgAAAAUY2hhZAAA
AXwAAAAsclRSQwAAAagAAAAOZ1RSQwAAAbgAAAAOYlRSQwAAAcgAAAAOdmNndAAAAdgAAAMSbmRpbgAA
BOwAAAY+ZGVzYwAACywAAABkZHNjbQAAC5AAAAAubW1vZAAAC8AAAAAoY3BydAAAC+gAAAAtWFlaIAAA
AAAAAF1KAAA0kQAACCVYWVogAAAAAAAAdCAAALRgAAAjPVhZWiAAAAAAAAAlbAAAFyoAAKfDWFlaIAAA
AAAAAPNSAAEAAAABFs9zZjMyAAAAAAABDEIAAAXe///zJgAAB5IAAP2R///7ov///aMAAAPcAADAbGN1
cnYAAAAAAAAAAQHNAABjdXJ2AAAAAAAAAAEBzQAAY3VydgAAAAAAAAABAc0AAHZjZ3QAAAAAAAAAAAAD
AQAAAQACBAUGBwkKCw0ODxASExQWFxgaGxweHyAiIyQmJygpKywtLzAxMjM1Njc4OTs8PT5AQUJDREZH
SElKS0xOT1BRUlNUVVZXWFlaW1xdXl9hYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ent8fX5/gIGCg4SF
hoeIiYqLjI2Oj5CRkpOUlZaXmJmam5ydnZ6foKGio6SlpqanqKmqq6ytra6vsLGysrO0tba3uLi5uru8
vL2+v8DBwcLDxMXGxsfIycrKy8zNzs7P0NHS0tPU1dbW19jZ2drb3Nzd3t/g4eLi4+Tl5ufo6enq6+zt
7u/w8fHy8/T19vf4+fr7/P3+/v8AAgMEBQYHCAkKCwwNDg8QERITFBUWFxgZGhscHR8gISIjJCUnKCkq
Ky0uLzAxMzQ1Njc4OTo7PD0/QEFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaWltcXV5fYGFiY2RlZmdo
aWprbG1ub3BxcnN0dXZ3d3h5ent8fH1+f4CBgoKDhIWGh4iIiYqLjI2Oj5CRkpOUlJWWl5iZmpucnZ2e
n6ChoqOkpaamp6ipqqusra6vsLCxsrO0tba3uLm5uru8vb6/wMHCw8TFx8jJysvMzc7P0NDR0tPU1dbX
2Nna29ze3+Dh4uPk5ebn6err7O3u7/Hy8/T19vf5+vv8/f7/AAIDAwQFBgcICQoKCwwNDg8QERITFBUW
FxgZGhscHR4fICEiIyQlJicoKSorLC0uLzAxMjM0NTY3ODg5Ojs8PT4+P0BBQkNDREVGR0hJSUpLTE1O
Tk9QUVJSU1RVVVZXWFhZWltbXF1eXl9gYWFiY2RkZWZnZ2hpaWprbGxtbm5vcHFxcnNzdHV1dnd4eHl6
ent8fH1+fn+AgYGCg4SEhYaHiImJiouMjY6Oj5CRkpOTlJWWl5iZmZqbnJ2en6ChoqOkpaanqKmqq6yt
rq+xsrO0tba3uLq7vL2+wMHDxMbHycrMzs/R0tTW19nb3d7g4uTm6Ors7vDy9Pb4+vz+/wAAbmRpbgAA
AAAAAAY2AACXGgAAVjoAAFPKAACJ3gAAJ8IAABaoAABQDQAAVDkAAiuFAAIZmQABeFEAAwEAAAIAAAAA
AAEABgANABcAIwAxAEAAUgBlAHsAkwCrAMUA4gD/AR8BPwFhAYUBqgHQAfgCIAJLAncCpQLSAwIDMwNl
A5gDzgQFBD0EdQSvBOsFKQVnBacF6AYqBm4GtQb8B0UHkgfkCDkIkAjnCT4JmAn0ClAKrQsLC2sLygwq
DIwM8Q1XDcAOKA6SDv4PbA/bEE0QxBE7EbQSMRKwEzITuRREFNAVYBXxFocXHhfAGGIZBBmsGlQa+RuU
HC4czh1yHhQeux9jIA0gvCFoIhkizyOJJEEk+SW6JnknOygFKMspkypiKzIsASzXLawuhy9gMD4xGzH8
MtszvzSgNYY2cjdcOEw5OTorOxs8CD0EPfU+6z/nQOFB2ELUQ9VE00XcRttH5EjxSgBLCUwdTTFOUE9v
UI9Rt1LdVAVVNlZsV6VY4FohW21ct135X09goGH0Y0tkqGYFZ19oxGova5ptCG54b/BxbnLsdG119Xd/
eQh6knwqfcV/W4D4gpSEO4Xih4CJKorYjIqOOY/jkZuTWJUOlsyYiZpSnB6d4Z+soX+jWqUvpxOo+6rj
rMuuwLC4sra0rra0uL+60LzfvwDBHcLdxLXGhchYyi7MCs3lz7rRmtOA1WPXR9kq2xPc/97s4M/iveSn
5o3obupT7ELuLPAM8fLz0PW396H5f/tZ/T3//wAAAAEAAwALABYAJQA3AE0AZQCBAJ8AwQDlAQsBNQFh
AZABwQH1AisCZAKfAtwDHANfA6MD6gQ0BH8EzQT1BR0FcAXEBhsGdAbPBy0HXAeMB+4IUgi4CSAJVAmK
CfYKZArVC0cLgQu8DDIMqw0mDaIOIQ6hDyQPqRAvELgQ/RFDEc8SXRLuE4AUFRSrFUMV3RZ5FxcXthhY
GPwZoRpIGvEbnBxJHPgdqB5bHw8fxSB9ITch8iKwJDAk8yW3Jn4nRigQKNwpqSp5K0osHCzxLccuoC95
MFUxMzISMvMz1TS5NaA2hzdxOFw5STo4Oyg8Gj4DPvs/9EDuQepD6ETpRexG8Uf3SP9LFEwhTTBOQE9S
UGZSklOrVMVV4Vb/WB5ZP1phW4Vcq13SXvthUmJ/Y69k4GYSZ0dofGm0au1tZG6ib+FxInJlc6l073Y2
d396FXtjfLJ+A39VgKmB/4NWhK+GCYjCiiGLgYzjjkePrJESknuT5Ja8mCuZm5sMnH+d9J9qoOGiWqPV
pVGmz6eOqE6pzqtRrNSuWq/gsWmy8rR+tgu5Kbq6vE294b93wQ7Cp8RBxd3He8kZyrrLisxbzf/Po9FK
0vHUm9ZF1/HZn9tO3Cbc/96x4GTiGePQ5YjnQegf6Pzquex27jbv9/G583z0X/VC9wj40Pqa/GX+Mf//
AAAAAQADAAsAJQA3AE0AZQCBAJ8AwQELATUBYQGQAcEB9QIrAmQCnwLcAxwDXwOjA+oENAR/BM0FHQVw
BcQGGwZ0Bs8HLQeMB+4IUgi4CSAJign2CmQK1QtHC7wMMgyrDSYNog4hDqEPJA+pEC8QuBFDEl0S7hOA
FBUUqxVDFnkXFxe2GFgY/BpIGvEbnBxJHPgdqB8PH8UgfSE3IfIjbyQwJPMltydGKBAo3Cp5K0osHC3H
LqAveTEzMhIy8zS5NaA2hzhcOUk6ODwaPQ4+Az/0QO5C6EPoROlG8Uf3SglLFEwhTkBPUlF7UpJUxVXh
Vv9ZP1phXKtd0mAlYVJjr2TgZhJofGm0au1tZG6ib+FxInJldO92Nnd/eMl6FXyyfgN/VYCpgf+Er4YJ
h2WIwoohi4GOR4+skRKSe5PklVCWvJgrmZubDJx/nfSfaqDholqj1aVRps+oTqnOq1Gs1K2Xrlqv4LFp
svK0frYLt5m5Kbnxurq8Tb3hv3fBDsHawqfEQcUPxd3He8hKyRnKusuKzFvN/87Rz6PQdtFK0vHTxtSb
1kXXG9fx2MjZn9tO3Cbc/93Y3rHfiuBk4hni9ePQ5KzliOZk50HoH+j86drqueuX7HbtVu427xbv9/DX
8bnymvN89F/1QvYl9wj37PjQ+bX6mvt//GX9S/4x//8AAGRlc2MAAAAAAAAACkNvbG9yIExDRAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAABIAAAAcAEMAbwBsAG8AcgAgAEwAQwBE
AABtbW9kAAAAAAAABhAAAJxOAAAAAL5zkQAAAAAAAAAAAAAAAAAAAAAAdGV4dAAAAABDb3B5cmlnaHQg
QXBwbGUgQ29tcHV0ZXIsIEluYy4sIDIwMDUAAAAAA</bytes>
													</object>
												</object>
											</object>
										</object>
										<reference key="NSColor" ref="30384615"/>
									</object>
									<reference key="NSAlternateImage" ref="619763889"/>
									<int key="NSPeriodicDelay">400</int>
									<int key="NSPeriodicInterval">75</int>
								</object>
								<object class="NSButtonCell" id="251439646">
									<int key="NSCellFlags">67239424</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">Call task_set_exception_port with null exception port in child process before exec</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="7590393"/>
									<int key="NSTag">2</int>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<object class="NSImage" key="NSNormalImage">
										<int key="NSImageFlags">549453824</int>
										<string key="NSSize">{18, 18}</string>
										<object class="NSMutableArray" key="NSReps">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSArray">
												<bool key="EncodedWithXMLCoder">YES</bool>
												<integer value="0"/>
												<object class="NSBitmapImageRep">
													<object class="NSData" key="NSTIFFRepresentation">
														<bytes key="NS.bytes">TU0AKgAABRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAADwRERGLJycnySsrK/A1NTXw
IyMjyRwcHIsJCQk8AAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFRUVdVBQUOCoqKj/
29vb//n5+f/6+vr/2tra/6qqqv9UVFTgHx8fdQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUZGRl5
dXV198PDw//8/Pz////////////////////////////U1NT/fHx89yUlJXkAAAAFAAAAAAAAAAAAAAAA
AAAAAxEREUZqamrmtbW1/+3t7f/+/v7//v7+//7+/v/9/f3//f39//39/f/39/f/xMTE/3d3d+YZGRlG
AAAAAwAAAAAAAAAAAAAACkJCQqGtra3/xsbG/+vr6//y8vL/9fX1//X19f/z8/P/9fX1//Ly8v/u7u7/
0tLS/6+vr/9KSkqhAAAACgAAAAAAAAAAAAAAF3h4eN2/v7//z8/P/93d3f/q6ur/7+/v/+/v7//w8PD/
7e3t/+3t7f/i4uL/zs7O/8XFxf98fHzdAAAAFwAAAAAAAAADAAAAJKSkpPjOzs7/2dnZ/+Dg4P/i4uL/
5eXl/+bm5v/n5+f/5eXl/+Li4v/e3t7/2tra/9DQ0P+srKz4AAAAJAAAAAMAAAADAAAALrCwsPrW1tb/
3t7e/+Tk5P/p6en/6+vr/+zs7P/p6en/6+vr/+fn5//k5OT/4ODg/9nZ2f+zs7P6AAAALgAAAAMAAAAD
AAAALp2dnezg4OD/5eXl/+rq6v/u7u7/8PDw//Dw8P/x8fH/8PDw/+7u7v/q6ur/5ubm/+Hh4f+ZmZns
AAAALgAAAAMAAAADAAAAJG5ubs/l5eX/6enp/+/v7//y8vL/9vb2//r6+v/5+fn/9/f3//b29v/x8fH/
6+vr/+Tk5P9ra2vPAAAAJAAAAAMAAAAAAAAAFy4uLpPCwsL67Ozs//Pz8//5+fn//v7+//7+/v/+/v7/
/v7+//v7+//19fX/8PDw/8LCwvosLCyTAAAAFwAAAAAAAAAAAAAACgAAAENfX1/S5OTk/vn5+f/+/v7/
///////////////////////////8/Pz/5ubm/l9fX9IAAABDAAAACgAAAAAAAAAAAAAAAwAAABcAAABl
YmJi3NLS0v3////////////////////////////////V1dX9ZGRk3AAAAGUAAAAXAAAAAwAAAAAAAAAA
AAAAAAAAAAUAAAAfAAAAZTMzM8KAgIDwv7+//O3t7f/t7e3/v7+//ICAgPAzMzPCAAAAZQAAAB8AAAAF
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAFwAAAEMAAAB3AAAAnwAAALMAAACzAAAAnwAAAHcAAABD
AAAAFwAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAoAAAAXAAAAJAAAAC4AAAAu
AAAAJAAAABcAAAAKAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAwAAAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgEAAAMAAAABABIAAAEB
AAMAAAABABIAAAECAAMAAAAEAAAFxgEDAAMAAAABAAEAAAEGAAMAAAABAAIAAAERAAQAAAABAAAACAES
AAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABABIAAAEXAAQAAAABAAAFEAEcAAMAAAABAAEAAAFS
AAMAAAABAAEAAAFTAAMAAAAEAAAFzodzAAcAAAv0AAAF1gAAAAAACAAIAAgACAABAAEAAQABAAAL9GFw
cGwCAAAAbW50clJHQiBYWVogB9gAAgAMAAoAFgAIYWNzcEFQUEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAPbWAAEAAAAA0y1hcHBs625VECyhxeSV9P9A73pKGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAANclhZWgAAASAAAAAUZ1hZWgAAATQAAAAUYlhZWgAAAUgAAAAUd3RwdAAAAVwAAAAUY2hhZAAA
AXAAAAAsclRSQwAAAZwAAAAOZ1RSQwAAAawAAAAOYlRSQwAAAbwAAAAOdmNndAAAAcwAAAYSbmRpbgAA
B+AAAAMOZGVzYwAACvAAAACZY3BydAAAC4wAAABAbW1vZAAAC8wAAAAoWFlaIAAAAAAAAJumAABMVQAA
ArBYWVogAAAAAAAANWMAAJ/rAAAZsVhZWiAAAAAAAAAlzQAAE9UAALbFWFlaIAAAAAAAAPPYAAEAAAAB
FghzZjMyAAAAAAABC7cAAAWW///zVwAABykAAP3X///7t////aYAAAPaAADA9mN1cnYAAAAAAAAAAQHN
AABjdXJ2AAAAAAAAAAEBzQAAY3VydgAAAAAAAAABAc0AAHZjZ3QAAAAAAAAAAAADAQAAAgAAAioENAYA
B9AJlAtRDQwOshBOEekTgxUVFqMYMRm9GzwcvR4+H7chLiKnJBoliCb3KGIpyCswLJMt9C9WMLIyDjNn
NL02FTdoOLs6ETtdPKw9+D9DQJBB1kMeRGZFq0bySDRJeEq6S/tNPk59T75Q+lI2U25Uo1XZVwlYOlln
WpZbwFzsXhdfQWBrYZRiv2PoZRNmPWdqaJhpyGr5bC1tZW6hb+BxLXJ+c9d1OHafeA55gnr5fHR98H9t
gOuCY4PYhUqGsIgSiWyKuIwBjTqObY+VkLORyJLUk9eU05XFlrWXlZh2mUuaG5rsm6ucbJ0qndqei588
n9+ghKEqoceiYqL/o5qkLqTDpVml66Z7pwunnKgpqLWpQqnQqlqq5Ktvq/qsg60MrZauIa6qrzOvvbBH
sNGxXLHnsnKy/7OMtBm0p7U3tci2Wbbrt4C4FrituUa54rqAux27wbxlvQq9rr5Tvvi/ncBAwOXBisIu
wtPDeMQdxMLFaMYPxrXHXMgEyKzJVcn/yqnLVcwBzK7NXc4Mzr3PcNAk0NnRkdJK0wTTw9SC1UTWCtbR
153Ybdk+2hfa8dvM3KfdhN5g3zzgGuD34dbiteOV5HblWeY85yHoCOjw6drqxuu27Kbtm+6R74zwivGM
8pPzn/Sw9cj25/gP+T76e/u//Rr+hP//AAABpANzBRoGsggnCZsLFQx+Dd4PRRCiEf8TYxS0FgoXXRiu
GgQbTRyZHekfMCB8IcIjCSRSJZUm3SgdKWAqpCvjLSYuZC+lMOIyIDNgNJs12TcTOFA5izrEO/49NT5w
P6dA30IWQ01Eg0W4Ru9IIElVSoZLt0zmThVPRFBwUZ5SylP5VSRWUVd+WKtZ2lsIXDhdaV6bX89hBWI8
Y3dktGX0ZzhohGnVayxsiW3sb1Vww3I0c6p1I3aeeBl5k3sMfIR99X9kgM6CLYOJhNyGJodriKGJ1Ir5
jBuNL45Aj0WQSJE8kjKTGJP+lNyVspaKl1OYHZjlmaSaZJshm9ecj51EnfSepJ9Vn/+gqqFWofyio6NL
o/CklKU4pdymfacfp8KoYqkDqaWqRarlq4asJ6zHrWiuCq6rr02v77CSsTax27KAsyezzrR3tSG1zLZ5
tye32LiKuT659rqwu2q8J7zkvaK+YL8ev93AnMFcwhzC3MOdxF7FIMXixqXHaMgryPDJtcp6y0DMB8zO
zZbOX88oz/PQvtGJ0lbTI9Px1MDVkNZi1zTYB9jb2bDah9te3DjdEt3t3sjfpOB/4VviN+MT4/DkzeWq
5ojnZuhG6SXqBuro68rsre2S7nfvXvBH8TDyHPMI8/j06fXc9tL3yvjF+cL6w/vG/ND92v7s//8AAAMJ
BboIZwrCDSsPghG8E/IWHxg5GkgcVB5VIEQiMyQTJeknuimHK00tCy7AMHEyHDO/NV829ziKOhs7pj0s
PrBALEGmQx9EkkYCR3JI3EpCS6pND05vT89RLVKKU+dVP1aYV+9ZRVqdW/NdSV6hX+thM2JzY61k42YS
Z0FoZ2mOaq5rz2zsbglvI3A9cVRybHOEdJx1tHbOd+d5A3ofez98Yn2Lfrl/8IEqgmyDsoT8hkuHnYjw
ikSLmYzsjj+PjJDWkh2TW5SXlceW85gWmTOaSJtVnFqdWp5Pn0SgKaEQoeuiwqOYpGClKaXtpqmnZqgf
qNKph6o5quWrk6xArOatja41rtevebAcsLyxWbH3spWzL7PJtGO0/LWTtiq2wrdWt+q4f7kUuaW6OLrL
u1277Lx9vQ69nr4tvry/TL/bwGjA98GGwhPCocMvw77ESsTYxWXF9MZ/xwzHmcgkyKXJJ8mpyizKpMsc
y5XMDsyFzPjNa83fzlPOxc81z6fQGNCK0PvRbNHe0lDSw9M206vUINSV1QvVhtYA1nzW+Nd61//YhNkK
2ZnaL9rG213cCty63WveI97d35fgUuEO4crih+NE5ALkw+WD5kXnCufP6JbpYOor6vrry+yd7XbuUe8w
8BXw+/Ht8uDz4PTl9fj3E/hE+X363vxa/gH//wAAbmRpbgAAAAAAAAMGAACogAAAUwAAADRAAACqQAAA
JpcAABLbAABQQAAAVEAAAj99AAI1egACxUsAAwB4AAIAAAADAAsAGQAsAEUAYwCHALEA4QEWAVEBkgHZ
AiYCeQLSAzEDlwQDBHYE7wVvBfUGgwcXB7IIUwj8CawKYgsgC+QMrw2BDloPORAfEQ0SBRMGFBEVJBZA
F2MYjhm/GvYcMh1xHrMf9SE1ImwjnSTJJfAnFig7KWMqjivBLP4uSC+jMRMymzRBNgo3+joWPGY+8EG8
RNhIQEvvT95UCFhkXOxhlWZYaylv/XTKeYN+GoKOhxGLqJBOlP6Ztp5voyan1Kx0sQG1c7nGvfHB9cX7
ygbOFNIi1izaMN4p4hTl7emv7Vbw3vRC93z6iP1g//8AAAAEAA8AIgA9AF8AiQC7APQBNAF8AcwCIgKB
AuYDUwPHBEIExAVOBd4GdgcUB7oIZgkaCdQKlQteDC0NAw3gDsQPrxCiEZwSnxOpFLsV0xbyGBcZQRpw
G6Mc2R4RH0oggyG3IuckEyU8JmQniyiyKd0rDCxBLYAuyTAhMYkzBjSbNkw4HToSPDA+fED8Q7FGmUmx
TPdQaFQAV7tbll+KY5RnrmvTb/p0H3g6fEOAMoQXiASL+I/yk/KX+JwBoA2kHKgrrDuwS7RYuGK8aMBo
xGvId8yI0J/UuNjS3OvhAOUQ6RftE/EC9OH4rfxi//8AAAABAAYADQAXACUANQBIAF8AeQCWALcA3AEE
ATABYQGVAc4CDAJOApUC4QMyA4gD5QRGBK4FHAWPBgkGigcQB54IMQjMCW0KFArDC3cMMgzzDbsOiA9a
EDIRFhIIEwgUFRUvFlYXhxjDGgkbVhyqHgMfYCC8IhIjYSSrJfMnOSiBKc0rHyx7LeQvXTDrMpE0VTY7
OEg6gjzuP5NCckWCSMJMMk/QU5xXk1u1X/9kcGkGbb5ylneLfJqByoeDjcyUf5t4oo2plLBetru8eMFr
xirK7c+v1GnZFd2r4iXme+ql7pryUvXD+OT7qv4M//8AAGRlc2MAAAAAAAAAFUhQIExQMzA2NSBDYWxp
YnJhdGVkAAAAAAAAAAAVAEgAUAAgAEwAUAAzADAANgA1ACAAQwBhAGwAaQBiAHIAYQB0AGUAZAAAAAAV
SFAgTFAzMDY1IENhbGlicmF0ZWQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAHRleHQAAAAAQ29weXJpZ2h0IEFwcGxlIEluYy4sIDIwMDgAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAABtbW9kAAAAAAAAIvAAACaQAAAAAMJtVwAAAAAAAAAAAAAAAAAAAAAAA</bytes>
													</object>
												</object>
											</object>
										</object>
										<reference key="NSColor" ref="30384615"/>
									</object>
									<reference key="NSAlternateImage" ref="619763889"/>
									<int key="NSPeriodicDelay">400</int>
									<int key="NSPeriodicInterval">75</int>
								</object>
							</object>
							<string key="NSCellSize">{565, 22}</string>
							<string key="NSIntercellSpacing">{4, 2}</string>
							<int key="NSMatrixFlags">1151868928</int>
							<string key="NSCellClass">NSActionCell</string>
							<object class="NSButtonCell" key="NSProtoCell" id="773902463">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">0</int>
								<string key="NSContents">Radio</string>
								<reference key="NSSupport" ref="933596199"/>
								<int key="NSButtonFlags">1211912703</int>
								<int key="NSButtonFlags2">0</int>
								<object class="NSImage" key="NSNormalImage">
									<int key="NSImageFlags">549453824</int>
									<string key="NSSize">{18, 18}</string>
									<object class="NSMutableArray" key="NSReps">
										<bool key="EncodedWithXMLCoder">YES</bool>
										<object class="NSArray">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<integer value="0"/>
											<object class="NSBitmapImageRep">
												<object class="NSData" key="NSTIFFRepresentation">
													<bytes key="NS.bytes">TU0AKgAABRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAADwRERGLJycnySsrK/A1NTXw
IyMjyRwcHIsJCQk8AAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFRUVdVBQUOCoqKj/
29vb//n5+f/6+vr/2tra/6qqqv9UVFTgHx8fdQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUZGRl5
dXV198PDw//8/Pz////////////////////////////U1NT/fHx89yUlJXkAAAAFAAAAAAAAAAAAAAAA
AAAAAxEREUZqamrmtbW1/+3t7f/+/v7//v7+//7+/v/9/f3//f39//39/f/39/f/xMTE/3d3d+YZGRlG
AAAAAwAAAAAAAAAAAAAACkJCQqGtra3/xsbG/+vr6//y8vL/9fX1//X19f/z8/P/9fX1//Ly8v/u7u7/
0tLS/6+vr/9KSkqhAAAACgAAAAAAAAAAAAAAF3h4eN2/v7//z8/P/93d3f/q6ur/7+/v/+/v7//w8PD/
7e3t/+3t7f/i4uL/zs7O/8XFxf98fHzdAAAAFwAAAAAAAAADAAAAJKSkpPjOzs7/2dnZ/+Dg4P/i4uL/
5eXl/+bm5v/n5+f/5eXl/+Li4v/e3t7/2tra/9DQ0P+srKz4AAAAJAAAAAMAAAADAAAALrCwsPrW1tb/
3t7e/+Tk5P/p6en/6+vr/+zs7P/p6en/6+vr/+fn5//k5OT/4ODg/9nZ2f+zs7P6AAAALgAAAAMAAAAD
AAAALp2dnezg4OD/5eXl/+rq6v/u7u7/8PDw//Dw8P/x8fH/8PDw/+7u7v/q6ur/5ubm/+Hh4f+ZmZns
AAAALgAAAAMAAAADAAAAJG5ubs/l5eX/6enp/+/v7//y8vL/9vb2//r6+v/5+fn/9/f3//b29v/x8fH/
6+vr/+Tk5P9ra2vPAAAAJAAAAAMAAAAAAAAAFy4uLpPCwsL67Ozs//Pz8//5+fn//v7+//7+/v/+/v7/
/v7+//v7+//19fX/8PDw/8LCwvosLCyTAAAAFwAAAAAAAAAAAAAACgAAAENfX1/S5OTk/vn5+f/+/v7/
///////////////////////////8/Pz/5ubm/l9fX9IAAABDAAAACgAAAAAAAAAAAAAAAwAAABcAAABl
YmJi3NLS0v3////////////////////////////////V1dX9ZGRk3AAAAGUAAAAXAAAAAwAAAAAAAAAA
AAAAAAAAAAUAAAAfAAAAZTMzM8KAgIDwv7+//O3t7f/t7e3/v7+//ICAgPAzMzPCAAAAZQAAAB8AAAAF
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAFwAAAEMAAAB3AAAAnwAAALMAAACzAAAAnwAAAHcAAABD
AAAAFwAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAoAAAAXAAAAJAAAAC4AAAAu
AAAAJAAAABcAAAAKAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAwAAAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADQEAAAMAAAABABIAAAEB
AAMAAAABABIAAAECAAMAAAAEAAAFugEDAAMAAAABAAEAAAEGAAMAAAABAAIAAAERAAQAAAABAAAACAES
AAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABABIAAAEXAAQAAAABAAAFEAEcAAMAAAABAAEAAAFS
AAMAAAABAAEAAAFTAAMAAAAEAAAFwgAAAAAACAAIAAgACAABAAEAAQABA</bytes>
												</object>
											</object>
										</object>
									</object>
									<reference key="NSColor" ref="30384615"/>
								</object>
								<reference key="NSAlternateImage" ref="619763889"/>
								<int key="NSPeriodicDelay">400</int>
								<int key="NSPeriodicInterval">75</int>
							</object>
							<reference key="NSSelectedCell" ref="808388382"/>
							<reference key="NSBackgroundColor" ref="349124561"/>
							<reference key="NSCellBackgroundColor" ref="195671423"/>
							<reference key="NSFont" ref="933596199"/>
						</object>
						<object class="NSMatrix" id="1050951576">
							<reference key="NSNextResponder" ref="594333702"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{20, 104}, {565, 38}}</string>
							<reference key="NSSuperview" ref="594333702"/>
							<bool key="NSEnabled">YES</bool>
							<int key="NSNumRows">2</int>
							<int key="NSNumCols">1</int>
							<object class="NSMutableArray" key="NSCells">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSButtonCell" id="943458284">
									<int key="NSCellFlags">-2080244224</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">fork()</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="1050951576"/>
									<int key="NSTag">3</int>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<reference key="NSAlternateImage" ref="619763889"/>
									<string key="NSAlternateContents"/>
									<string key="NSKeyEquivalent"/>
									<int key="NSPeriodicDelay">200</int>
									<int key="NSPeriodicInterval">25</int>
								</object>
								<object class="NSButtonCell" id="69061500">
									<int key="NSCellFlags">67239424</int>
									<int key="NSCellFlags2">0</int>
									<string key="NSContents">vfork()</string>
									<reference key="NSSupport" ref="933596199"/>
									<reference key="NSControlView" ref="1050951576"/>
									<int key="NSTag">4</int>
									<int key="NSButtonFlags">1211912703</int>
									<int key="NSButtonFlags2">0</int>
									<object class="NSImage" key="NSNormalImage">
										<int key="NSImageFlags">549453824</int>
										<string key="NSSize">{18, 18}</string>
										<object class="NSMutableArray" key="NSReps">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSArray">
												<bool key="EncodedWithXMLCoder">YES</bool>
												<integer value="0"/>
												<object class="NSBitmapImageRep">
													<object class="NSData" key="NSTIFFRepresentation">
														<bytes key="NS.bytes">TU0AKgAABRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAADwRERGLJycnySsrK/A1NTXw
IyMjyRwcHIsJCQk8AAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFRUVdVBQUOCoqKj/
29vb//n5+f/6+vr/2tra/6qqqv9UVFTgHx8fdQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUZGRl5
dXV198PDw//8/Pz////////////////////////////U1NT/fHx89yUlJXkAAAAFAAAAAAAAAAAAAAAA
AAAAAxEREUZqamrmtbW1/+3t7f/+/v7//v7+//7+/v/9/f3//f39//39/f/39/f/xMTE/3d3d+YZGRlG
AAAAAwAAAAAAAAAAAAAACkJCQqGtra3/xsbG/+vr6//y8vL/9fX1//X19f/z8/P/9fX1//Ly8v/u7u7/
0tLS/6+vr/9KSkqhAAAACgAAAAAAAAAAAAAAF3h4eN2/v7//z8/P/93d3f/q6ur/7+/v/+/v7//w8PD/
7e3t/+3t7f/i4uL/zs7O/8XFxf98fHzdAAAAFwAAAAAAAAADAAAAJKSkpPjOzs7/2dnZ/+Dg4P/i4uL/
5eXl/+bm5v/n5+f/5eXl/+Li4v/e3t7/2tra/9DQ0P+srKz4AAAAJAAAAAMAAAADAAAALrCwsPrW1tb/
3t7e/+Tk5P/p6en/6+vr/+zs7P/p6en/6+vr/+fn5//k5OT/4ODg/9nZ2f+zs7P6AAAALgAAAAMAAAAD
AAAALp2dnezg4OD/5eXl/+rq6v/u7u7/8PDw//Dw8P/x8fH/8PDw/+7u7v/q6ur/5ubm/+Hh4f+ZmZns
AAAALgAAAAMAAAADAAAAJG5ubs/l5eX/6enp/+/v7//y8vL/9vb2//r6+v/5+fn/9/f3//b29v/x8fH/
6+vr/+Tk5P9ra2vPAAAAJAAAAAMAAAAAAAAAFy4uLpPCwsL67Ozs//Pz8//5+fn//v7+//7+/v/+/v7/
/v7+//v7+//19fX/8PDw/8LCwvosLCyTAAAAFwAAAAAAAAAAAAAACgAAAENfX1/S5OTk/vn5+f/+/v7/
///////////////////////////8/Pz/5ubm/l9fX9IAAABDAAAACgAAAAAAAAAAAAAAAwAAABcAAABl
YmJi3NLS0v3////////////////////////////////V1dX9ZGRk3AAAAGUAAAAXAAAAAwAAAAAAAAAA
AAAAAAAAAAUAAAAfAAAAZTMzM8KAgIDwv7+//O3t7f/t7e3/v7+//ICAgPAzMzPCAAAAZQAAAB8AAAAF
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAFwAAAEMAAAB3AAAAnwAAALMAAACzAAAAnwAAAHcAAABD
AAAAFwAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAoAAAAXAAAAJAAAAC4AAAAu
AAAAJAAAABcAAAAKAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAwAAAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgEAAAMAAAABABIAAAEB
AAMAAAABABIAAAECAAMAAAAEAAAFxgEDAAMAAAABAAEAAAEGAAMAAAABAAIAAAERAAQAAAABAAAACAES
AAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABABIAAAEXAAQAAAABAAAFEAEcAAMAAAABAAEAAAFS
AAMAAAABAAEAAAFTAAMAAAAEAAAFzodzAAcAAAwYAAAF1gAAAAAACAAIAAgACAABAAEAAQABAAAMGGFw
cGwCAAAAbW50clJHQiBYWVogB9YABAADABMALAASYWNzcEFQUEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAPbWAAEAAAAA0y1hcHBsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAOclhZWgAAASwAAAAUZ1hZWgAAAUAAAAAUYlhZWgAAAVQAAAAUd3RwdAAAAWgAAAAUY2hhZAAA
AXwAAAAsclRSQwAAAagAAAAOZ1RSQwAAAbgAAAAOYlRSQwAAAcgAAAAOdmNndAAAAdgAAAMSbmRpbgAA
BOwAAAY+ZGVzYwAACywAAABkZHNjbQAAC5AAAAAubW1vZAAAC8AAAAAoY3BydAAAC+gAAAAtWFlaIAAA
AAAAAF1KAAA0kQAACCVYWVogAAAAAAAAdCAAALRgAAAjPVhZWiAAAAAAAAAlbAAAFyoAAKfDWFlaIAAA
AAAAAPNSAAEAAAABFs9zZjMyAAAAAAABDEIAAAXe///zJgAAB5IAAP2R///7ov///aMAAAPcAADAbGN1
cnYAAAAAAAAAAQHNAABjdXJ2AAAAAAAAAAEBzQAAY3VydgAAAAAAAAABAc0AAHZjZ3QAAAAAAAAAAAAD
AQAAAQACBAUGBwkKCw0ODxASExQWFxgaGxweHyAiIyQmJygpKywtLzAxMjM1Njc4OTs8PT5AQUJDREZH
SElKS0xOT1BRUlNUVVZXWFlaW1xdXl9hYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ent8fX5/gIGCg4SF
hoeIiYqLjI2Oj5CRkpOUlZaXmJmam5ydnZ6foKGio6SlpqanqKmqq6ytra6vsLGysrO0tba3uLi5uru8
vL2+v8DBwcLDxMXGxsfIycrKy8zNzs7P0NHS0tPU1dbW19jZ2drb3Nzd3t/g4eLi4+Tl5ufo6enq6+zt
7u/w8fHy8/T19vf4+fr7/P3+/v8AAgMEBQYHCAkKCwwNDg8QERITFBUWFxgZGhscHR8gISIjJCUnKCkq
Ky0uLzAxMzQ1Njc4OTo7PD0/QEFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaWltcXV5fYGFiY2RlZmdo
aWprbG1ub3BxcnN0dXZ3d3h5ent8fH1+f4CBgoKDhIWGh4iIiYqLjI2Oj5CRkpOUlJWWl5iZmpucnZ2e
n6ChoqOkpaamp6ipqqusra6vsLCxsrO0tba3uLm5uru8vb6/wMHCw8TFx8jJysvMzc7P0NDR0tPU1dbX
2Nna29ze3+Dh4uPk5ebn6err7O3u7/Hy8/T19vf5+vv8/f7/AAIDAwQFBgcICQoKCwwNDg8QERITFBUW
FxgZGhscHR4fICEiIyQlJicoKSorLC0uLzAxMjM0NTY3ODg5Ojs8PT4+P0BBQkNDREVGR0hJSUpLTE1O
Tk9QUVJSU1RVVVZXWFhZWltbXF1eXl9gYWFiY2RkZWZnZ2hpaWprbGxtbm5vcHFxcnNzdHV1dnd4eHl6
ent8fH1+fn+AgYGCg4SEhYaHiImJiouMjY6Oj5CRkpOTlJWWl5iZmZqbnJ2en6ChoqOkpaanqKmqq6yt
rq+xsrO0tba3uLq7vL2+wMHDxMbHycrMzs/R0tTW19nb3d7g4uTm6Ors7vDy9Pb4+vz+/wAAbmRpbgAA
AAAAAAY2AACXGgAAVjoAAFPKAACJ3gAAJ8IAABaoAABQDQAAVDkAAiuFAAIZmQABeFEAAwEAAAIAAAAA
AAEABgANABcAIwAxAEAAUgBlAHsAkwCrAMUA4gD/AR8BPwFhAYUBqgHQAfgCIAJLAncCpQLSAwIDMwNl
A5gDzgQFBD0EdQSvBOsFKQVnBacF6AYqBm4GtQb8B0UHkgfkCDkIkAjnCT4JmAn0ClAKrQsLC2sLygwq
DIwM8Q1XDcAOKA6SDv4PbA/bEE0QxBE7EbQSMRKwEzITuRREFNAVYBXxFocXHhfAGGIZBBmsGlQa+RuU
HC4czh1yHhQeux9jIA0gvCFoIhkizyOJJEEk+SW6JnknOygFKMspkypiKzIsASzXLawuhy9gMD4xGzH8
MtszvzSgNYY2cjdcOEw5OTorOxs8CD0EPfU+6z/nQOFB2ELUQ9VE00XcRttH5EjxSgBLCUwdTTFOUE9v
UI9Rt1LdVAVVNlZsV6VY4FohW21ct135X09goGH0Y0tkqGYFZ19oxGova5ptCG54b/BxbnLsdG119Xd/
eQh6knwqfcV/W4D4gpSEO4Xih4CJKorYjIqOOY/jkZuTWJUOlsyYiZpSnB6d4Z+soX+jWqUvpxOo+6rj
rMuuwLC4sra0rra0uL+60LzfvwDBHcLdxLXGhchYyi7MCs3lz7rRmtOA1WPXR9kq2xPc/97s4M/iveSn
5o3obupT7ELuLPAM8fLz0PW396H5f/tZ/T3//wAAAAEAAwALABYAJQA3AE0AZQCBAJ8AwQDlAQsBNQFh
AZABwQH1AisCZAKfAtwDHANfA6MD6gQ0BH8EzQT1BR0FcAXEBhsGdAbPBy0HXAeMB+4IUgi4CSAJVAmK
CfYKZArVC0cLgQu8DDIMqw0mDaIOIQ6hDyQPqRAvELgQ/RFDEc8SXRLuE4AUFRSrFUMV3RZ5FxcXthhY
GPwZoRpIGvEbnBxJHPgdqB5bHw8fxSB9ITch8iKwJDAk8yW3Jn4nRigQKNwpqSp5K0osHCzxLccuoC95
MFUxMzISMvMz1TS5NaA2hzdxOFw5STo4Oyg8Gj4DPvs/9EDuQepD6ETpRexG8Uf3SP9LFEwhTTBOQE9S
UGZSklOrVMVV4Vb/WB5ZP1phW4Vcq13SXvthUmJ/Y69k4GYSZ0dofGm0au1tZG6ib+FxInJlc6l073Y2
d396FXtjfLJ+A39VgKmB/4NWhK+GCYjCiiGLgYzjjkePrJESknuT5Ja8mCuZm5sMnH+d9J9qoOGiWqPV
pVGmz6eOqE6pzqtRrNSuWq/gsWmy8rR+tgu5Kbq6vE294b93wQ7Cp8RBxd3He8kZyrrLisxbzf/Po9FK
0vHUm9ZF1/HZn9tO3Cbc/96x4GTiGePQ5YjnQegf6Pzquex27jbv9/G583z0X/VC9wj40Pqa/GX+Mf//
AAAAAQADAAsAJQA3AE0AZQCBAJ8AwQELATUBYQGQAcEB9QIrAmQCnwLcAxwDXwOjA+oENAR/BM0FHQVw
BcQGGwZ0Bs8HLQeMB+4IUgi4CSAJign2CmQK1QtHC7wMMgyrDSYNog4hDqEPJA+pEC8QuBFDEl0S7hOA
FBUUqxVDFnkXFxe2GFgY/BpIGvEbnBxJHPgdqB8PH8UgfSE3IfIjbyQwJPMltydGKBAo3Cp5K0osHC3H
LqAveTEzMhIy8zS5NaA2hzhcOUk6ODwaPQ4+Az/0QO5C6EPoROlG8Uf3SglLFEwhTkBPUlF7UpJUxVXh
Vv9ZP1phXKtd0mAlYVJjr2TgZhJofGm0au1tZG6ib+FxInJldO92Nnd/eMl6FXyyfgN/VYCpgf+Er4YJ
h2WIwoohi4GOR4+skRKSe5PklVCWvJgrmZubDJx/nfSfaqDholqj1aVRps+oTqnOq1Gs1K2Xrlqv4LFp
svK0frYLt5m5Kbnxurq8Tb3hv3fBDsHawqfEQcUPxd3He8hKyRnKusuKzFvN/87Rz6PQdtFK0vHTxtSb
1kXXG9fx2MjZn9tO3Cbc/93Y3rHfiuBk4hni9ePQ5KzliOZk50HoH+j86drqueuX7HbtVu427xbv9/DX
8bnymvN89F/1QvYl9wj37PjQ+bX6mvt//GX9S/4x//8AAGRlc2MAAAAAAAAACkNvbG9yIExDRAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAABIAAAAcAEMAbwBsAG8AcgAgAEwAQwBE
AABtbW9kAAAAAAAABhAAAJxOAAAAAL5zkQAAAAAAAAAAAAAAAAAAAAAAdGV4dAAAAABDb3B5cmlnaHQg
QXBwbGUgQ29tcHV0ZXIsIEluYy4sIDIwMDUAAAAAA</bytes>
													</object>
												</object>
											</object>
										</object>
										<reference key="NSColor" ref="30384615"/>
									</object>
									<reference key="NSAlternateImage" ref="619763889"/>
									<int key="NSPeriodicDelay">400</int>
									<int key="NSPeriodicInterval">75</int>
								</object>
							</object>
							<string key="NSCellSize">{565, 18}</string>
							<string key="NSIntercellSpacing">{4, 2}</string>
							<int key="NSMatrixFlags">1151868928</int>
							<string key="NSCellClass">NSActionCell</string>
							<object class="NSButtonCell" key="NSProtoCell" id="709643899">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">0</int>
								<string key="NSContents">Radio</string>
								<reference key="NSSupport" ref="933596199"/>
								<int key="NSButtonFlags">1211912703</int>
								<int key="NSButtonFlags2">0</int>
								<object class="NSImage" key="NSNormalImage">
									<int key="NSImageFlags">549453824</int>
									<string key="NSSize">{18, 18}</string>
									<object class="NSMutableArray" key="NSReps">
										<bool key="EncodedWithXMLCoder">YES</bool>
										<object class="NSArray">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<integer value="0"/>
											<object class="NSBitmapImageRep">
												<object class="NSData" key="NSTIFFRepresentation">
													<bytes key="NS.bytes">TU0AKgAABRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAADwRERGLJycnySsrK/A1NTXw
IyMjyRwcHIsJCQk8AAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFRUVdVBQUOCoqKj/
29vb//n5+f/6+vr/2tra/6qqqv9UVFTgHx8fdQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUZGRl5
dXV198PDw//8/Pz////////////////////////////U1NT/fHx89yUlJXkAAAAFAAAAAAAAAAAAAAAA
AAAAAxEREUZqamrmtbW1/+3t7f/+/v7//v7+//7+/v/9/f3//f39//39/f/39/f/xMTE/3d3d+YZGRlG
AAAAAwAAAAAAAAAAAAAACkJCQqGtra3/xsbG/+vr6//y8vL/9fX1//X19f/z8/P/9fX1//Ly8v/u7u7/
0tLS/6+vr/9KSkqhAAAACgAAAAAAAAAAAAAAF3h4eN2/v7//z8/P/93d3f/q6ur/7+/v/+/v7//w8PD/
7e3t/+3t7f/i4uL/zs7O/8XFxf98fHzdAAAAFwAAAAAAAAADAAAAJKSkpPjOzs7/2dnZ/+Dg4P/i4uL/
5eXl/+bm5v/n5+f/5eXl/+Li4v/e3t7/2tra/9DQ0P+srKz4AAAAJAAAAAMAAAADAAAALrCwsPrW1tb/
3t7e/+Tk5P/p6en/6+vr/+zs7P/p6en/6+vr/+fn5//k5OT/4ODg/9nZ2f+zs7P6AAAALgAAAAMAAAAD
AAAALp2dnezg4OD/5eXl/+rq6v/u7u7/8PDw//Dw8P/x8fH/8PDw/+7u7v/q6ur/5ubm/+Hh4f+ZmZns
AAAALgAAAAMAAAADAAAAJG5ubs/l5eX/6enp/+/v7//y8vL/9vb2//r6+v/5+fn/9/f3//b29v/x8fH/
6+vr/+Tk5P9ra2vPAAAAJAAAAAMAAAAAAAAAFy4uLpPCwsL67Ozs//Pz8//5+fn//v7+//7+/v/+/v7/
/v7+//v7+//19fX/8PDw/8LCwvosLCyTAAAAFwAAAAAAAAAAAAAACgAAAENfX1/S5OTk/vn5+f/+/v7/
///////////////////////////8/Pz/5ubm/l9fX9IAAABDAAAACgAAAAAAAAAAAAAAAwAAABcAAABl
YmJi3NLS0v3////////////////////////////////V1dX9ZGRk3AAAAGUAAAAXAAAAAwAAAAAAAAAA
AAAAAAAAAAUAAAAfAAAAZTMzM8KAgIDwv7+//O3t7f/t7e3/v7+//ICAgPAzMzPCAAAAZQAAAB8AAAAF
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAFwAAAEMAAAB3AAAAnwAAALMAAACzAAAAnwAAAHcAAABD
AAAAFwAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAoAAAAXAAAAJAAAAC4AAAAu
AAAAJAAAABcAAAAKAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAwAAAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADQEAAAMAAAABABIAAAEB
AAMAAAABABIAAAECAAMAAAAEAAAFugEDAAMAAAABAAEAAAEGAAMAAAABAAIAAAERAAQAAAABAAAACAES
AAMAAAABAAEAAAEVAAMAAAABAAQAAAEWAAMAAAABABIAAAEXAAQAAAABAAAFEAEcAAMAAAABAAEAAAFS
AAMAAAABAAEAAAFTAAMAAAAEAAAFwgAAAAAACAAIAAgACAABAAEAAQABA</bytes>
												</object>
											</object>
										</object>
									</object>
									<reference key="NSColor" ref="30384615"/>
								</object>
								<reference key="NSAlternateImage" ref="619763889"/>
								<int key="NSPeriodicDelay">400</int>
								<int key="NSPeriodicInterval">75</int>
							</object>
							<reference key="NSSelectedCell" ref="943458284"/>
							<reference key="NSBackgroundColor" ref="349124561"/>
							<reference key="NSCellBackgroundColor" ref="195671423"/>
							<reference key="NSFont" ref="933596199"/>
						</object>
						<object class="NSButton" id="512228208">
							<reference key="NSNextResponder" ref="594333702"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{591, 59}, {178, 161}}</string>
							<reference key="NSSuperview" ref="594333702"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="69630975">
								<int key="NSCellFlags">67239424</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Go!</string>
								<object class="NSFont" key="NSSupport">
									<string key="NSName">LucidaGrande</string>
									<double key="NSSize">10</double>
									<int key="NSfFlags">16</int>
								</object>
								<reference key="NSControlView" ref="512228208"/>
								<int key="NSButtonFlags">-2033434369</int>
								<int key="NSButtonFlags2">130</int>
								<string key="NSAlternateContents"/>
								<string key="NSKeyEquivalent"/>
								<int key="NSPeriodicDelay">400</int>
								<int key="NSPeriodicInterval">75</int>
							</object>
						</object>
					</object>
					<string key="NSFrameSize">{787, 260}</string>
					<reference key="NSSuperview"/>
				</object>
				<string key="NSScreenRect">{{0, 0}, {1440, 878}}</string>
				<string key="NSMaxSize">{1.79769e+308, 1.79769e+308}</string>
			</object>
		</object>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<object class="NSMutableArray" key="connectionRecords">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performMiniaturize:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="683986939"/>
					</object>
					<int key="connectionID">37</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">arrangeInFront:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="164762492"/>
					</object>
					<int key="connectionID">39</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">print:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="302505815"/>
					</object>
					<int key="connectionID">86</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">runPageLayout:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="409810395"/>
					</object>
					<int key="connectionID">87</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showHelp:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="192540884"/>
					</object>
					<int key="connectionID">122</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">clearRecentDocuments:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="85018532"/>
					</object>
					<int key="connectionID">127</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">terminate:</string>
						<reference key="source" ref="870565383"/>
						<reference key="destination" ref="887927135"/>
					</object>
					<int key="connectionID">139</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">orderFrontStandardAboutPanel:</string>
						<reference key="source" ref="870565383"/>
						<reference key="destination" ref="838552093"/>
					</object>
					<int key="connectionID">142</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">hideOtherApplications:</string>
						<reference key="source" ref="870565383"/>
						<reference key="destination" ref="216168366"/>
					</object>
					<int key="connectionID">146</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">hide:</string>
						<reference key="source" ref="870565383"/>
						<reference key="destination" ref="104472016"/>
					</object>
					<int key="connectionID">152</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">unhideAllApplications:</string>
						<reference key="source" ref="870565383"/>
						<reference key="destination" ref="667790509"/>
					</object>
					<int key="connectionID">153</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">cut:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="909447496"/>
					</object>
					<int key="connectionID">175</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">paste:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="84012734"/>
					</object>
					<int key="connectionID">176</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">redo:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="1001272176"/>
					</object>
					<int key="connectionID">178</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">selectAll:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="917620781"/>
					</object>
					<int key="connectionID">179</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">undo:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="284548410"/>
					</object>
					<int key="connectionID">180</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">copy:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="994487277"/>
					</object>
					<int key="connectionID">181</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showGuessPanel:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="438210660"/>
					</object>
					<int key="connectionID">188</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">checkSpelling:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="102172584"/>
					</object>
					<int key="connectionID">190</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleContinuousSpellChecking:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="540509341"/>
					</object>
					<int key="connectionID">192</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performClose:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="468594275"/>
					</object>
					<int key="connectionID">193</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">delete:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="512189403"/>
					</object>
					<int key="connectionID">195</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performZoom:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="548098734"/>
					</object>
					<int key="connectionID">198</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="203238834"/>
					</object>
					<int key="connectionID">199</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="861312964"/>
					</object>
					<int key="connectionID">200</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="743767160"/>
					</object>
					<int key="connectionID">201</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="180446588"/>
					</object>
					<int key="connectionID">202</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">centerSelectionInVisibleArea:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="731027425"/>
					</object>
					<int key="connectionID">203</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">pasteAsPlainText:</string>
						<reference key="source" ref="442653439"/>
						<reference key="destination" ref="182251545"/>
					</object>
					<int key="connectionID">205</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">crash:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="726278107"/>
					</object>
					<int key="connectionID">208</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">window_</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="341270541"/>
					</object>
					<int key="connectionID">209</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">crash:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="799567279"/>
					</object>
					<int key="connectionID">211</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">crash:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="27781390"/>
					</object>
					<int key="connectionID">213</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">forkTestOptions_</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="347013037"/>
					</object>
					<int key="connectionID">241</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">forkTestOptions:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="943458284"/>
					</object>
					<int key="connectionID">242</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">forkTestOptions:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="7590393"/>
					</object>
					<int key="connectionID">243</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">forkTestOptions:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="410017819"/>
					</object>
					<int key="connectionID">244</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">forkTestGo:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="512228208"/>
					</object>
					<int key="connectionID">250</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">forkTestOptions:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="1050951576"/>
					</object>
					<int key="connectionID">261</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">forkTestOptions:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="891367997"/>
					</object>
					<int key="connectionID">262</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showForkTestWindow:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="856256540"/>
					</object>
					<int key="connectionID">283</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">generateReportWithoutCrash:</string>
						<reference key="source" ref="623097029"/>
						<reference key="destination" ref="460755987"/>
					</object>
					<int key="connectionID">327</int>
				</object>
			</object>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<object class="NSArray" key="orderedObjects">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<reference key="object" ref="0"/>
						<reference key="children" ref="925601844"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="870565383"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="442653439"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">First Responder</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-3</int>
						<reference key="object" ref="751079937"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">Application</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">21</int>
						<reference key="object" ref="341270541"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="814272478"/>
						</object>
						<reference key="parent" ref="0"/>
						<string key="objectName">Window</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">2</int>
						<reference key="object" ref="814272478"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="726278107"/>
							<reference ref="799567279"/>
							<reference ref="27781390"/>
							<reference ref="856256540"/>
							<reference ref="460755987"/>
						</object>
						<reference key="parent" ref="341270541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">206</int>
						<reference key="object" ref="726278107"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="539552922"/>
						</object>
						<reference key="parent" ref="814272478"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">210</int>
						<reference key="object" ref="799567279"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1010617379"/>
						</object>
						<reference key="parent" ref="814272478"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">212</int>
						<reference key="object" ref="27781390"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="547901497"/>
						</object>
						<reference key="parent" ref="814272478"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">218</int>
						<reference key="object" ref="856256540"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="353736234"/>
						</object>
						<reference key="parent" ref="814272478"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">325</int>
						<reference key="object" ref="460755987"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="775425649"/>
						</object>
						<reference key="parent" ref="814272478"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">29</int>
						<reference key="object" ref="695387251"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="764068863"/>
							<reference ref="458207250"/>
							<reference ref="369472335"/>
							<reference ref="599772536"/>
							<reference ref="542216986"/>
						</object>
						<reference key="parent" ref="0"/>
						<string key="objectName">MainMenu</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">19</int>
						<reference key="object" ref="764068863"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="200536676"/>
						</object>
						<reference key="parent" ref="695387251"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">24</int>
						<reference key="object" ref="200536676"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="164762492"/>
							<reference ref="683986939"/>
							<reference ref="297002686"/>
							<reference ref="548098734"/>
						</object>
						<reference key="parent" ref="764068863"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">5</int>
						<reference key="object" ref="164762492"/>
						<reference key="parent" ref="200536676"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">23</int>
						<reference key="object" ref="683986939"/>
						<reference key="parent" ref="200536676"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">92</int>
						<reference key="object" ref="297002686"/>
						<reference key="parent" ref="200536676"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">197</int>
						<reference key="object" ref="548098734"/>
						<reference key="parent" ref="200536676"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">56</int>
						<reference key="object" ref="458207250"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="753534561"/>
						</object>
						<reference key="parent" ref="695387251"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">57</int>
						<reference key="object" ref="753534561"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="838552093"/>
							<reference ref="443649494"/>
							<reference ref="826764396"/>
							<reference ref="104472016"/>
							<reference ref="887927135"/>
							<reference ref="217746140"/>
							<reference ref="881859155"/>
							<reference ref="216168366"/>
							<reference ref="928933982"/>
							<reference ref="667790509"/>
							<reference ref="758254482"/>
						</object>
						<reference key="parent" ref="458207250"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">58</int>
						<reference key="object" ref="838552093"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">129</int>
						<reference key="object" ref="443649494"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">131</int>
						<reference key="object" ref="826764396"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="276709607"/>
						</object>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">130</int>
						<reference key="object" ref="276709607"/>
						<reference key="parent" ref="826764396"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">134</int>
						<reference key="object" ref="104472016"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">136</int>
						<reference key="object" ref="887927135"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">143</int>
						<reference key="object" ref="217746140"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">144</int>
						<reference key="object" ref="881859155"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">145</int>
						<reference key="object" ref="216168366"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">149</int>
						<reference key="object" ref="928933982"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">150</int>
						<reference key="object" ref="667790509"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">196</int>
						<reference key="object" ref="758254482"/>
						<reference key="parent" ref="753534561"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">83</int>
						<reference key="object" ref="369472335"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="902982238"/>
						</object>
						<reference key="parent" ref="695387251"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">81</int>
						<reference key="object" ref="902982238"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="367379562"/>
							<reference ref="468594275"/>
							<reference ref="232609393"/>
							<reference ref="479945444"/>
							<reference ref="409810395"/>
							<reference ref="302505815"/>
							<reference ref="154948703"/>
							<reference ref="976375553"/>
							<reference ref="660391032"/>
							<reference ref="885975128"/>
							<reference ref="84883275"/>
						</object>
						<reference key="parent" ref="369472335"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">72</int>
						<reference key="object" ref="367379562"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">73</int>
						<reference key="object" ref="468594275"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">74</int>
						<reference key="object" ref="232609393"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">75</int>
						<reference key="object" ref="479945444"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">77</int>
						<reference key="object" ref="409810395"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">78</int>
						<reference key="object" ref="302505815"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">79</int>
						<reference key="object" ref="154948703"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">80</int>
						<reference key="object" ref="976375553"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">82</int>
						<reference key="object" ref="660391032"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">112</int>
						<reference key="object" ref="885975128"/>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">124</int>
						<reference key="object" ref="84883275"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="693280130"/>
						</object>
						<reference key="parent" ref="902982238"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">125</int>
						<reference key="object" ref="693280130"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="85018532"/>
						</object>
						<reference key="parent" ref="84883275"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">126</int>
						<reference key="object" ref="85018532"/>
						<reference key="parent" ref="693280130"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">103</int>
						<reference key="object" ref="599772536"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1066958924"/>
						</object>
						<reference key="parent" ref="695387251"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">106</int>
						<reference key="object" ref="1066958924"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="192540884"/>
						</object>
						<reference key="parent" ref="599772536"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">111</int>
						<reference key="object" ref="192540884"/>
						<reference key="parent" ref="1066958924"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">163</int>
						<reference key="object" ref="542216986"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1053284541"/>
						</object>
						<reference key="parent" ref="695387251"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">169</int>
						<reference key="object" ref="1053284541"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="209744238"/>
							<reference ref="994487277"/>
							<reference ref="284548410"/>
							<reference ref="909447496"/>
							<reference ref="512189403"/>
							<reference ref="153501847"/>
							<reference ref="84012734"/>
							<reference ref="917620781"/>
							<reference ref="1001272176"/>
							<reference ref="431895313"/>
							<reference ref="61602259"/>
							<reference ref="182251545"/>
						</object>
						<reference key="parent" ref="542216986"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">156</int>
						<reference key="object" ref="209744238"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">157</int>
						<reference key="object" ref="994487277"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">158</int>
						<reference key="object" ref="284548410"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">160</int>
						<reference key="object" ref="909447496"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">164</int>
						<reference key="object" ref="512189403"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">168</int>
						<reference key="object" ref="153501847"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="333484665"/>
						</object>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">159</int>
						<reference key="object" ref="333484665"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="203238834"/>
							<reference ref="731027425"/>
							<reference ref="180446588"/>
							<reference ref="743767160"/>
							<reference ref="861312964"/>
						</object>
						<reference key="parent" ref="153501847"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">154</int>
						<reference key="object" ref="203238834"/>
						<reference key="parent" ref="333484665"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">155</int>
						<reference key="object" ref="731027425"/>
						<reference key="parent" ref="333484665"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">161</int>
						<reference key="object" ref="180446588"/>
						<reference key="parent" ref="333484665"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">162</int>
						<reference key="object" ref="743767160"/>
						<reference key="parent" ref="333484665"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">167</int>
						<reference key="object" ref="861312964"/>
						<reference key="parent" ref="333484665"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">171</int>
						<reference key="object" ref="84012734"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">172</int>
						<reference key="object" ref="917620781"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">173</int>
						<reference key="object" ref="1001272176"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">174</int>
						<reference key="object" ref="431895313"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">184</int>
						<reference key="object" ref="61602259"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="8174285"/>
						</object>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">185</int>
						<reference key="object" ref="8174285"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="438210660"/>
							<reference ref="102172584"/>
							<reference ref="540509341"/>
						</object>
						<reference key="parent" ref="61602259"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">187</int>
						<reference key="object" ref="438210660"/>
						<reference key="parent" ref="8174285"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">189</int>
						<reference key="object" ref="102172584"/>
						<reference key="parent" ref="8174285"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">191</int>
						<reference key="object" ref="540509341"/>
						<reference key="parent" ref="8174285"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">204</int>
						<reference key="object" ref="182251545"/>
						<reference key="parent" ref="1053284541"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">207</int>
						<reference key="object" ref="623097029"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">Controller</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">220</int>
						<reference key="object" ref="347013037"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="594333702"/>
						</object>
						<reference key="parent" ref="0"/>
						<string key="objectName">Window (Window)</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">221</int>
						<reference key="object" ref="594333702"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="891367997"/>
							<reference ref="7590393"/>
							<reference ref="1050951576"/>
							<reference ref="512228208"/>
						</object>
						<reference key="parent" ref="347013037"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">226</int>
						<reference key="object" ref="891367997"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="410017819"/>
							<reference ref="904578786"/>
							<reference ref="971445237"/>
							<reference ref="1072218638"/>
						</object>
						<reference key="parent" ref="594333702"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">227</int>
						<reference key="object" ref="410017819"/>
						<reference key="parent" ref="891367997"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">228</int>
						<reference key="object" ref="904578786"/>
						<reference key="parent" ref="891367997"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">272</int>
						<reference key="object" ref="971445237"/>
						<reference key="parent" ref="891367997"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">232</int>
						<reference key="object" ref="7590393"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="808388382"/>
							<reference ref="378736460"/>
							<reference ref="251439646"/>
							<reference ref="773902463"/>
						</object>
						<reference key="parent" ref="594333702"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">233</int>
						<reference key="object" ref="808388382"/>
						<reference key="parent" ref="7590393"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">234</int>
						<reference key="object" ref="378736460"/>
						<reference key="parent" ref="7590393"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">236</int>
						<reference key="object" ref="251439646"/>
						<reference key="parent" ref="7590393"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">237</int>
						<reference key="object" ref="1050951576"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="943458284"/>
							<reference ref="69061500"/>
							<reference ref="709643899"/>
						</object>
						<reference key="parent" ref="594333702"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">238</int>
						<reference key="object" ref="943458284"/>
						<reference key="parent" ref="1050951576"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">239</int>
						<reference key="object" ref="69061500"/>
						<reference key="parent" ref="1050951576"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">248</int>
						<reference key="object" ref="512228208"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="69630975"/>
						</object>
						<reference key="parent" ref="594333702"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">329</int>
						<reference key="object" ref="539552922"/>
						<reference key="parent" ref="726278107"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">330</int>
						<reference key="object" ref="1010617379"/>
						<reference key="parent" ref="799567279"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">331</int>
						<reference key="object" ref="547901497"/>
						<reference key="parent" ref="27781390"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">332</int>
						<reference key="object" ref="353736234"/>
						<reference key="parent" ref="856256540"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">333</int>
						<reference key="object" ref="775425649"/>
						<reference key="parent" ref="460755987"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">334</int>
						<reference key="object" ref="69630975"/>
						<reference key="parent" ref="512228208"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">335</int>
						<reference key="object" ref="1072218638"/>
						<reference key="parent" ref="891367997"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">336</int>
						<reference key="object" ref="773902463"/>
						<reference key="parent" ref="7590393"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">337</int>
						<reference key="object" ref="709643899"/>
						<reference key="parent" ref="1050951576"/>
					</object>
				</object>
			</object>
			<object class="NSMutableDictionary" key="flattenedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="NSArray" key="dict.sortedKeys">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>-3.IBPluginDependency</string>
					<string>-3.ImportedFromIB2</string>
					<string>103.IBPluginDependency</string>
					<string>103.ImportedFromIB2</string>
					<string>106.IBPluginDependency</string>
					<string>106.ImportedFromIB2</string>
					<string>111.IBPluginDependency</string>
					<string>111.ImportedFromIB2</string>
					<string>112.IBPluginDependency</string>
					<string>112.ImportedFromIB2</string>
					<string>124.IBPluginDependency</string>
					<string>124.ImportedFromIB2</string>
					<string>125.IBPluginDependency</string>
					<string>125.ImportedFromIB2</string>
					<string>126.IBPluginDependency</string>
					<string>126.ImportedFromIB2</string>
					<string>129.IBPluginDependency</string>
					<string>129.ImportedFromIB2</string>
					<string>130.IBPluginDependency</string>
					<string>130.ImportedFromIB2</string>
					<string>131.IBPluginDependency</string>
					<string>131.ImportedFromIB2</string>
					<string>134.IBPluginDependency</string>
					<string>134.ImportedFromIB2</string>
					<string>136.IBPluginDependency</string>
					<string>136.ImportedFromIB2</string>
					<string>143.IBPluginDependency</string>
					<string>143.ImportedFromIB2</string>
					<string>144.IBPluginDependency</string>
					<string>144.ImportedFromIB2</string>
					<string>145.IBPluginDependency</string>
					<string>145.ImportedFromIB2</string>
					<string>149.IBPluginDependency</string>
					<string>149.ImportedFromIB2</string>
					<string>150.IBPluginDependency</string>
					<string>150.ImportedFromIB2</string>
					<string>154.IBPluginDependency</string>
					<string>154.ImportedFromIB2</string>
					<string>155.IBPluginDependency</string>
					<string>155.ImportedFromIB2</string>
					<string>156.IBPluginDependency</string>
					<string>156.ImportedFromIB2</string>
					<string>157.IBPluginDependency</string>
					<string>157.ImportedFromIB2</string>
					<string>158.IBPluginDependency</string>
					<string>158.ImportedFromIB2</string>
					<string>159.IBPluginDependency</string>
					<string>159.ImportedFromIB2</string>
					<string>160.IBPluginDependency</string>
					<string>160.ImportedFromIB2</string>
					<string>161.IBPluginDependency</string>
					<string>161.ImportedFromIB2</string>
					<string>162.IBPluginDependency</string>
					<string>162.ImportedFromIB2</string>
					<string>163.IBPluginDependency</string>
					<string>163.ImportedFromIB2</string>
					<string>164.IBPluginDependency</string>
					<string>164.ImportedFromIB2</string>
					<string>167.IBPluginDependency</string>
					<string>167.ImportedFromIB2</string>
					<string>168.IBPluginDependency</string>
					<string>168.ImportedFromIB2</string>
					<string>169.IBPluginDependency</string>
					<string>169.ImportedFromIB2</string>
					<string>171.IBPluginDependency</string>
					<string>171.ImportedFromIB2</string>
					<string>172.IBPluginDependency</string>
					<string>172.ImportedFromIB2</string>
					<string>173.IBPluginDependency</string>
					<string>173.ImportedFromIB2</string>
					<string>174.IBPluginDependency</string>
					<string>174.ImportedFromIB2</string>
					<string>184.IBPluginDependency</string>
					<string>184.ImportedFromIB2</string>
					<string>185.IBPluginDependency</string>
					<string>185.ImportedFromIB2</string>
					<string>187.IBPluginDependency</string>
					<string>187.ImportedFromIB2</string>
					<string>189.IBPluginDependency</string>
					<string>189.ImportedFromIB2</string>
					<string>19.IBPluginDependency</string>
					<string>19.ImportedFromIB2</string>
					<string>191.IBPluginDependency</string>
					<string>191.ImportedFromIB2</string>
					<string>196.IBPluginDependency</string>
					<string>196.ImportedFromIB2</string>
					<string>197.IBPluginDependency</string>
					<string>197.ImportedFromIB2</string>
					<string>2.IBPluginDependency</string>
					<string>2.ImportedFromIB2</string>
					<string>204.IBPluginDependency</string>
					<string>204.ImportedFromIB2</string>
					<string>206.IBPluginDependency</string>
					<string>206.ImportedFromIB2</string>
					<string>207.ImportedFromIB2</string>
					<string>21.IBEditorWindowLastContentRect</string>
					<string>21.IBPluginDependency</string>
					<string>21.IBWindowTemplateEditedContentRect</string>
					<string>21.ImportedFromIB2</string>
					<string>21.windowTemplate.hasMinSize</string>
					<string>21.windowTemplate.minSize</string>
					<string>210.IBPluginDependency</string>
					<string>210.ImportedFromIB2</string>
					<string>212.IBPluginDependency</string>
					<string>212.ImportedFromIB2</string>
					<string>218.IBPluginDependency</string>
					<string>218.ImportedFromIB2</string>
					<string>220.IBEditorWindowLastContentRect</string>
					<string>220.IBPluginDependency</string>
					<string>220.IBWindowTemplateEditedContentRect</string>
					<string>220.ImportedFromIB2</string>
					<string>221.IBPluginDependency</string>
					<string>221.ImportedFromIB2</string>
					<string>226.IBPluginDependency</string>
					<string>226.ImportedFromIB2</string>
					<string>227.IBPluginDependency</string>
					<string>227.ImportedFromIB2</string>
					<string>228.IBPluginDependency</string>
					<string>228.ImportedFromIB2</string>
					<string>23.IBPluginDependency</string>
					<string>23.ImportedFromIB2</string>
					<string>232.IBPluginDependency</string>
					<string>232.ImportedFromIB2</string>
					<string>233.IBPluginDependency</string>
					<string>233.ImportedFromIB2</string>
					<string>234.IBPluginDependency</string>
					<string>234.ImportedFromIB2</string>
					<string>236.IBPluginDependency</string>
					<string>236.ImportedFromIB2</string>
					<string>237.IBPluginDependency</string>
					<string>237.ImportedFromIB2</string>
					<string>238.IBPluginDependency</string>
					<string>238.ImportedFromIB2</string>
					<string>239.IBPluginDependency</string>
					<string>239.ImportedFromIB2</string>
					<string>24.IBPluginDependency</string>
					<string>24.ImportedFromIB2</string>
					<string>248.IBPluginDependency</string>
					<string>248.ImportedFromIB2</string>
					<string>272.IBPluginDependency</string>
					<string>272.ImportedFromIB2</string>
					<string>29.IBEditorWindowLastContentRect</string>
					<string>29.IBPluginDependency</string>
					<string>29.ImportedFromIB2</string>
					<string>325.IBPluginDependency</string>
					<string>325.ImportedFromIB2</string>
					<string>329.IBPluginDependency</string>
					<string>330.IBPluginDependency</string>
					<string>331.IBPluginDependency</string>
					<string>332.IBPluginDependency</string>
					<string>333.IBPluginDependency</string>
					<string>334.IBPluginDependency</string>
					<string>335.IBPluginDependency</string>
					<string>336.IBPluginDependency</string>
					<string>337.IBPluginDependency</string>
					<string>5.IBPluginDependency</string>
					<string>5.ImportedFromIB2</string>
					<string>56.IBPluginDependency</string>
					<string>56.ImportedFromIB2</string>
					<string>57.IBPluginDependency</string>
					<string>57.ImportedFromIB2</string>
					<string>58.IBPluginDependency</string>
					<string>58.ImportedFromIB2</string>
					<string>72.IBPluginDependency</string>
					<string>72.ImportedFromIB2</string>
					<string>73.IBPluginDependency</string>
					<string>73.ImportedFromIB2</string>
					<string>74.IBPluginDependency</string>
					<string>74.ImportedFromIB2</string>
					<string>75.IBPluginDependency</string>
					<string>75.ImportedFromIB2</string>
					<string>77.IBPluginDependency</string>
					<string>77.ImportedFromIB2</string>
					<string>78.IBPluginDependency</string>
					<string>78.ImportedFromIB2</string>
					<string>79.IBPluginDependency</string>
					<string>79.ImportedFromIB2</string>
					<string>80.IBPluginDependency</string>
					<string>80.ImportedFromIB2</string>
					<string>81.IBPluginDependency</string>
					<string>81.ImportedFromIB2</string>
					<string>82.IBPluginDependency</string>
					<string>82.ImportedFromIB2</string>
					<string>83.IBPluginDependency</string>
					<string>83.ImportedFromIB2</string>
					<string>92.IBPluginDependency</string>
					<string>92.ImportedFromIB2</string>
				</object>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<string>{{510, 1250}, {320, 188}}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>{{510, 1250}, {320, 188}}</string>
					<boolean value="YES"/>
					<boolean value="YES"/>
					<string>{213, 107}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>{{-55, 1287}, {787, 260}}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>{{-55, 1287}, {787, 260}}</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>{{0, 1114}, {362, 20}}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<boolean value="YES"/>
				</object>
			</object>
			<object class="NSMutableDictionary" key="unlocalizedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="activeLocalization"/>
			<object class="NSMutableDictionary" key="localizations">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="sourceID"/>
			<int key="maxID">337</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes">
			<object class="NSMutableArray" key="referencedPartialClassDescriptions">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBPartialClassDescription">
					<string key="className">Controller</string>
					<string key="superclassName">NSObject</string>
					<object class="NSMutableDictionary" key="actions">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>crash:</string>
							<string>forkTestGo:</string>
							<string>forkTestOptions:</string>
							<string>generateReportWithoutCrash:</string>
							<string>showForkTestWindow:</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>id</string>
							<string>id</string>
							<string>id</string>
							<string>id</string>
							<string>id</string>
						</object>
					</object>
					<object class="NSMutableDictionary" key="actionInfosByName">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>crash:</string>
							<string>forkTestGo:</string>
							<string>forkTestOptions:</string>
							<string>generateReportWithoutCrash:</string>
							<string>showForkTestWindow:</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<object class="IBActionInfo">
								<string key="name">crash:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">forkTestGo:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">forkTestOptions:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">generateReportWithoutCrash:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">showForkTestWindow:</string>
								<string key="candidateClassName">id</string>
							</object>
						</object>
					</object>
					<object class="NSMutableDictionary" key="outlets">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>forkTestOptions_</string>
							<string>window_</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>NSWindow</string>
							<string>NSWindow</string>
						</object>
					</object>
					<object class="NSMutableDictionary" key="toOneOutletInfosByName">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>forkTestOptions_</string>
							<string>window_</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<object class="IBToOneOutletInfo">
								<string key="name">forkTestOptions_</string>
								<string key="candidateClassName">NSWindow</string>
							</object>
							<object class="IBToOneOutletInfo">
								<string key="name">window_</string>
								<string key="candidateClassName">NSWindow</string>
							</object>
						</object>
					</object>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">testapp/Controller.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">Controller</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBUserSource</string>
						<string key="minorKey"/>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">FirstResponder</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBUserSource</string>
						<string key="minorKey"/>
					</object>
				</object>
			</object>
			<object class="NSMutableArray" key="referencedPartialClassDescriptionsV3.2+">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBPartialClassDescription">
					<string key="className">NSActionCell</string>
					<string key="superclassName">NSCell</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSActionCell.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSApplication</string>
					<string key="superclassName">NSResponder</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="785325875">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSApplication.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSApplication</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="806686590">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSApplicationScripting.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSApplication</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="301712406">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSColorPanel.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSApplication</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSHelpManager.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSApplication</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSPageLayout.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSApplication</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSUserInterfaceItemSearching.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSBrowser</string>
					<string key="superclassName">NSControl</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSBrowser.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSButton</string>
					<string key="superclassName">NSControl</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSButton.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSButtonCell</string>
					<string key="superclassName">NSActionCell</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSButtonCell.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSCell</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSCell.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSControl</string>
					<string key="superclassName">NSView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="787388657">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSControl.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSDocument</string>
					<string key="superclassName">NSObject</string>
					<object class="NSMutableDictionary" key="actions">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>printDocument:</string>
							<string>revertDocumentToSaved:</string>
							<string>runPageLayout:</string>
							<string>saveDocument:</string>
							<string>saveDocumentAs:</string>
							<string>saveDocumentTo:</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>id</string>
							<string>id</string>
							<string>id</string>
							<string>id</string>
							<string>id</string>
							<string>id</string>
						</object>
					</object>
					<object class="NSMutableDictionary" key="actionInfosByName">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>printDocument:</string>
							<string>revertDocumentToSaved:</string>
							<string>runPageLayout:</string>
							<string>saveDocument:</string>
							<string>saveDocumentAs:</string>
							<string>saveDocumentTo:</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<object class="IBActionInfo">
								<string key="name">printDocument:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">revertDocumentToSaved:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">runPageLayout:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">saveDocument:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">saveDocumentAs:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">saveDocumentTo:</string>
								<string key="candidateClassName">id</string>
							</object>
						</object>
					</object>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSDocument.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSDocument</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSDocumentScripting.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSDocumentController</string>
					<string key="superclassName">NSObject</string>
					<object class="NSMutableDictionary" key="actions">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>clearRecentDocuments:</string>
							<string>newDocument:</string>
							<string>openDocument:</string>
							<string>saveAllDocuments:</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>id</string>
							<string>id</string>
							<string>id</string>
							<string>id</string>
						</object>
					</object>
					<object class="NSMutableDictionary" key="actionInfosByName">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>clearRecentDocuments:</string>
							<string>newDocument:</string>
							<string>openDocument:</string>
							<string>saveAllDocuments:</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<object class="IBActionInfo">
								<string key="name">clearRecentDocuments:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">newDocument:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">openDocument:</string>
								<string key="candidateClassName">id</string>
							</object>
							<object class="IBActionInfo">
								<string key="name">saveAllDocuments:</string>
								<string key="candidateClassName">id</string>
							</object>
						</object>
					</object>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSDocumentController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSFormatter</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSFormatter.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSMatrix</string>
					<string key="superclassName">NSControl</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSMatrix.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSMenu</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="136824428">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSMenu.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSMenuItem</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="171959132">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSMenuItem.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSMovieView</string>
					<string key="superclassName">NSView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSMovieView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSAccessibility.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<reference key="sourceIdentifier" ref="785325875"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<reference key="sourceIdentifier" ref="806686590"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<reference key="sourceIdentifier" ref="301712406"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<reference key="sourceIdentifier" ref="787388657"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSDictionaryController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSDragging.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSFontManager.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSFontPanel.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSKeyValueBinding.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<reference key="sourceIdentifier" ref="136824428"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSNibLoading.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSOutlineView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSPasteboard.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSSavePanel.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="521965700">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSTableView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSToolbarItem.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="104369095">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSArchiver.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSClassDescription.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSError.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSFileManager.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSKeyValueCoding.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSKeyValueObserving.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSKeyedArchiver.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSObject.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSObjectScripting.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSPortCoder.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSRunLoop.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSScriptClassDescription.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSScriptKeyValueCoding.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSScriptObjectSpecifiers.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSScriptWhoseTests.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSThread.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSURL.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSURLConnection.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSURLDownload.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSResponder</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSInterfaceStyle.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSResponder</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSResponder.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSTableView</string>
					<string key="superclassName">NSControl</string>
					<reference key="sourceIdentifier" ref="521965700"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSText</string>
					<string key="superclassName">NSView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSText.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSClipView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSView</string>
					<reference key="sourceIdentifier" ref="171959132"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSRulerView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSView</string>
					<string key="superclassName">NSResponder</string>
					<reference key="sourceIdentifier" ref="104369095"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSWindow</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSDrawer.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSWindow</string>
					<string key="superclassName">NSResponder</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSWindow.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSWindow</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">AppKit.framework/Headers/NSWindowScripting.h</string>
					</object>
				</object>
			</object>
		</object>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaFramework</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.macosx</string>
			<integer value="1050" key="NS.object.0"/>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDependencyDefaults">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.macosx</string>
			<integer value="1050" key="NS.object.0"/>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDevelopmentDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.InterfaceBuilder3</string>
			<integer value="3000" key="NS.object.0"/>
		</object>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<string key="IBDocument.LastKnownRelativeProjectPath">../../Breakpad.xcodeproj</string>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<object class="NSMutableDictionary" key="IBDocument.LastKnownImageSizes">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSArray" key="dict.sortedKeys">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<string>NSMenuCheckmark</string>
				<string>NSMenuMixedState</string>
			</object>
			<object class="NSMutableArray" key="dict.values">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<string>{9, 8}</string>
				<string>{7, 2}</string>
			</object>
		</object>
	</data>
</archive>
