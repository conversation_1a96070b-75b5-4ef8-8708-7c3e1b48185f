// Copyright 2008 Google LLC
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

//
//  dwarftests.m
//  minidump_test
//
//  Created by Neal Sidhwaney on 9/24/08.
//  Copyright 2008 Google LLC
//

#import "dwarftests.h"
#import "dump_syms.h"

@implementation dwarftests
- (void) testDWARFSymbolFileGeneration {
  NSString *inputBreakpadSymbolFile = @"testcases/testdata/dump_syms_i386_breakpad.sym";
  NSString *outputBreakpadSymbolFile = @"/tmp/dump_syms_i386.breakpad";

  DumpSymbols *dump = [[DumpSymbols alloc] initWithContentsOfFile:@"testcases/testdata/dump_syms_dwarf_data"];

  STAssertNotNil(dump, @"DumpSymbols is nil");
  [dump setArchitecture:@"i386"];
  [dump writeSymbolFile:outputBreakpadSymbolFile];
  
  NSData *d = [[NSData alloc] initWithContentsOfFile:inputBreakpadSymbolFile];
  STAssertNotNil(d, @"Input breakpad symbol file not found");
  
  NSData *d1 = [[NSData alloc] initWithContentsOfFile:outputBreakpadSymbolFile]; 
  STAssertNotNil(d1, @"Output breakpad symbol file not found");

  STAssertTrue([d isEqualToData:d1],
  @"Symbol files were not equal!",nil);
}
@end
