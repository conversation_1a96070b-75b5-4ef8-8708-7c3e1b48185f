// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		8BFC813F11FF9A58002CB4DC /* libcrypto.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */; };
		8BFC814411FF9A9C002CB4DC /* libcrypto.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */; };
		8BFC814511FF9A9D002CB4DC /* libcrypto.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */; };
		8BFC814811FF9B13002CB4DC /* libcrypto.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */; };
		8BFC814911FF9B13002CB4DC /* libcrypto.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */; };
		8BFC814A11FF9B13002CB4DC /* libcrypto.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */; };
		8BFC814B11FF9B3F002CB4DC /* SenTestingKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F9721FA10E8B0E2300D7E813 /* SenTestingKit.framework */; };
		8BFC814C11FF9B3F002CB4DC /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F9721F6B0E8B0D7000D7E813 /* Cocoa.framework */; };
		8BFC81A211FF9C2E002CB4DC /* CPlusTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC819211FF9C23002CB4DC /* CPlusTest.framework */; };
		8BFC81A311FF9C2F002CB4DC /* CPlusTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BFC819211FF9C23002CB4DC /* CPlusTest.framework */; };
		8BFC81AD11FF9C8A002CB4DC /* breakpad_nlist_64.cc in Sources */ = {isa = PBXBuildFile; fileRef = F98208A10DB32CAE0017AECA /* breakpad_nlist_64.cc */; };
		8BFC81AE11FF9C8C002CB4DC /* breakpad_nlist_64.cc in Sources */ = {isa = PBXBuildFile; fileRef = F98208A10DB32CAE0017AECA /* breakpad_nlist_64.cc */; };
		8BFC81AF11FF9C8C002CB4DC /* breakpad_nlist_64.cc in Sources */ = {isa = PBXBuildFile; fileRef = F98208A10DB32CAE0017AECA /* breakpad_nlist_64.cc */; };
		8BFC81B011FF9C8D002CB4DC /* breakpad_nlist_64.cc in Sources */ = {isa = PBXBuildFile; fileRef = F98208A10DB32CAE0017AECA /* breakpad_nlist_64.cc */; };
		9B35FF5A0B267D5F008DE8C7 /* convert_UTF.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9B35FF560B267D5F008DE8C7 /* convert_UTF.cc */; };
		9B35FF5B0B267D5F008DE8C7 /* string_conversion.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9B35FF580B267D5F008DE8C7 /* string_conversion.cc */; };
		9B37CEEC0AF98ECD00FA4BD4 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9B37CEEB0AF98ECD00FA4BD4 /* CoreFoundation.framework */; };
		9B7CA7700B12873A00CD3A1D /* minidump_file_writer-inl.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9BE3C01E0B0CE329009892DF /* minidump_file_writer-inl.h */; };
		9B7CA8540B12989000CD3A1D /* minidump_file_writer_unittest.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9B7CA8530B12989000CD3A1D /* minidump_file_writer_unittest.cc */; };
		9B7CA8550B1298A100CD3A1D /* minidump_file_writer.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C230B01344C0055103E /* minidump_file_writer.cc */; };
		9BC1D2940B336F2300F2A2B4 /* convert_UTF.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9B35FF560B267D5F008DE8C7 /* convert_UTF.cc */; };
		9BC1D2950B336F2500F2A2B4 /* string_conversion.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9B35FF580B267D5F008DE8C7 /* string_conversion.cc */; };
		9BD82AC10B0029DF0055103E /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9B37CEEB0AF98ECD00FA4BD4 /* CoreFoundation.framework */; };
		9BD82BFF0B01333D0055103E /* exception_handler_test.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82BFD0B01333D0055103E /* exception_handler_test.cc */; };
		9BD82C020B01333D0055103E /* minidump_generator_test.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82BFE0B01333D0055103E /* minidump_generator_test.cc */; };
		9BD82C0D0B0133520055103E /* exception_handler.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C090B0133520055103E /* exception_handler.cc */; };
		9BD82C0E0B0133520055103E /* minidump_generator.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C0B0B0133520055103E /* minidump_generator.cc */; };
		9BD82C0F0B0133520055103E /* exception_handler.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C090B0133520055103E /* exception_handler.cc */; };
		9BD82C100B0133520055103E /* exception_handler.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9BD82C0A0B0133520055103E /* exception_handler.h */; };
		9BD82C110B0133520055103E /* minidump_generator.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C0B0B0133520055103E /* minidump_generator.cc */; };
		9BD82C120B0133520055103E /* minidump_generator.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9BD82C0C0B0133520055103E /* minidump_generator.h */; };
		9BD82C250B01344C0055103E /* minidump_file_writer.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C230B01344C0055103E /* minidump_file_writer.cc */; };
		9BD82C260B01344C0055103E /* minidump_file_writer.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C230B01344C0055103E /* minidump_file_writer.cc */; };
		9BD82C270B01344C0055103E /* minidump_file_writer.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9BD82C240B01344C0055103E /* minidump_file_writer.h */; };
		9BD82C2D0B01345E0055103E /* string_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C2B0B01345E0055103E /* string_utilities.cc */; };
		9BD82C2E0B01345E0055103E /* string_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9BD82C2B0B01345E0055103E /* string_utilities.cc */; };
		9BD82C2F0B01345E0055103E /* string_utilities.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9BD82C2C0B01345E0055103E /* string_utilities.h */; };
		D2F651000BEF947200920385 /* file_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FA0BEF947200920385 /* file_id.cc */; };
		D2F651010BEF947200920385 /* file_id.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = D2F650FB0BEF947200920385 /* file_id.h */; };
		D2F651020BEF947200920385 /* macho_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FC0BEF947200920385 /* macho_id.cc */; };
		D2F651030BEF947200920385 /* macho_id.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = D2F650FD0BEF947200920385 /* macho_id.h */; };
		D2F651040BEF947200920385 /* macho_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FE0BEF947200920385 /* macho_utilities.cc */; };
		D2F651050BEF947200920385 /* macho_utilities.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = D2F650FF0BEF947200920385 /* macho_utilities.h */; };
		D2F651090BEF949A00920385 /* dynamic_images.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F651070BEF949A00920385 /* dynamic_images.cc */; };
		D2F6510A0BEF949A00920385 /* dynamic_images.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = D2F651080BEF949A00920385 /* dynamic_images.h */; };
		D2F6510E0BEF94EB00920385 /* macho_walker.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F6510C0BEF94EB00920385 /* macho_walker.cc */; };
		D2F6510F0BEF94EB00920385 /* macho_walker.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = D2F6510D0BEF94EB00920385 /* macho_walker.h */; };
		D2F651110BEF951700920385 /* string_conversion.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9B35FF580B267D5F008DE8C7 /* string_conversion.cc */; };
		D2F651130BEF951C00920385 /* string_conversion.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9B35FF590B267D5F008DE8C7 /* string_conversion.h */; };
		D2F651150BEF953000920385 /* convert_UTF.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9B35FF560B267D5F008DE8C7 /* convert_UTF.cc */; };
		D2F651160BEF953100920385 /* convert_UTF.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9B35FF570B267D5F008DE8C7 /* convert_UTF.h */; };
		D2F6511B0BEF970E00920385 /* dynamic_images.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F651070BEF949A00920385 /* dynamic_images.cc */; };
		D2F6511D0BEF973500920385 /* file_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FA0BEF947200920385 /* file_id.cc */; };
		D2F6511E0BEF973600920385 /* macho_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FC0BEF947200920385 /* macho_id.cc */; };
		D2F6511F0BEF973900920385 /* macho_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FE0BEF947200920385 /* macho_utilities.cc */; };
		D2F651210BEF975400920385 /* macho_walker.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F6510C0BEF94EB00920385 /* macho_walker.cc */; };
		F93A887D0E8B4C8C0026AF89 /* macho_walker.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F6510C0BEF94EB00920385 /* macho_walker.cc */; };
		F93A887E0E8B4C8C0026AF89 /* macho_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FC0BEF947200920385 /* macho_id.cc */; };
		F93A887F0E8B4C8C0026AF89 /* macho_utilities.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FE0BEF947200920385 /* macho_utilities.cc */; };
		F93A88800E8B4C8C0026AF89 /* file_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F650FA0BEF947200920385 /* file_id.cc */; };
		F93A88860E8B4C9A0026AF89 /* dwarftests.mm in Sources */ = {isa = PBXBuildFile; fileRef = F9721F310E8B07E800D7E813 /* dwarftests.mm */; };
		F93A88870E8B4C9A0026AF89 /* dump_syms.cc in Sources */ = {isa = PBXBuildFile; fileRef = F9721F390E8B0D0D00D7E813 /* dump_syms.cc */; };
		F93A88880E8B4C9A0026AF89 /* bytereader.cc in Sources */ = {isa = PBXBuildFile; fileRef = F9721F760E8B0DC700D7E813 /* bytereader.cc */; };
		F93A88890E8B4C9A0026AF89 /* dwarf2reader.cc in Sources */ = {isa = PBXBuildFile; fileRef = F9721F770E8B0DC700D7E813 /* dwarf2reader.cc */; };
		F93A888A0E8B4C9A0026AF89 /* functioninfo.cc in Sources */ = {isa = PBXBuildFile; fileRef = F9721F780E8B0DC700D7E813 /* functioninfo.cc */; };
		F93A888B0E8B4C9A0026AF89 /* md5.cc in Sources */ = {isa = PBXBuildFile; fileRef = F9721FA80E8B0E4800D7E813 /* md5.cc */; };
		F9721F6C0E8B0D7000D7E813 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F9721F6B0E8B0D7000D7E813 /* Cocoa.framework */; };
		F9721FA20E8B0E2300D7E813 /* SenTestingKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F9721FA10E8B0E2300D7E813 /* SenTestingKit.framework */; };
		F982089C0DB3280D0017AECA /* breakpad_nlist_test.cc in Sources */ = {isa = PBXBuildFile; fileRef = F982089B0DB3280D0017AECA /* breakpad_nlist_test.cc */; };
		F98208A30DB32CAE0017AECA /* breakpad_nlist_64.cc in Sources */ = {isa = PBXBuildFile; fileRef = F98208A10DB32CAE0017AECA /* breakpad_nlist_64.cc */; };
		F9AE5B390DBFDBDB00505983 /* dynamic_images.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F651070BEF949A00920385 /* dynamic_images.cc */; };
		F9AE5B3A0DBFDBDB00505983 /* DynamicImagesTests.cc in Sources */ = {isa = PBXBuildFile; fileRef = F9C5A4210DB82DD800209C76 /* DynamicImagesTests.cc */; };
		F9B34E870DBC1E1600306484 /* dynamic_images.cc in Sources */ = {isa = PBXBuildFile; fileRef = D2F651070BEF949A00920385 /* dynamic_images.cc */; };
		F9C5A4220DB82DD800209C76 /* DynamicImagesTests.cc in Sources */ = {isa = PBXBuildFile; fileRef = F9C5A4210DB82DD800209C76 /* DynamicImagesTests.cc */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		8DD76F690486A84900D96B5E /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 8;
			dstPath = /usr/share/man/man1/;
			dstSubfolderSpec = 0;
			files = (
				9BD82C100B0133520055103E /* exception_handler.h in CopyFiles */,
				9BD82C120B0133520055103E /* minidump_generator.h in CopyFiles */,
				9BD82C270B01344C0055103E /* minidump_file_writer.h in CopyFiles */,
				9BD82C2F0B01345E0055103E /* string_utilities.h in CopyFiles */,
				9B7CA7700B12873A00CD3A1D /* minidump_file_writer-inl.h in CopyFiles */,
				D2F651010BEF947200920385 /* file_id.h in CopyFiles */,
				D2F651030BEF947200920385 /* macho_id.h in CopyFiles */,
				D2F651050BEF947200920385 /* macho_utilities.h in CopyFiles */,
				D2F6510A0BEF949A00920385 /* dynamic_images.h in CopyFiles */,
				D2F6510F0BEF94EB00920385 /* macho_walker.h in CopyFiles */,
				D2F651130BEF951C00920385 /* string_conversion.h in CopyFiles */,
				D2F651160BEF953100920385 /* convert_UTF.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 1;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		8BFC812011FF99D5002CB4DC /* Breakpad.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Breakpad.xcconfig; path = ../../../common/mac/Breakpad.xcconfig; sourceTree = SOURCE_ROOT; };
		8BFC812111FF99D5002CB4DC /* BreakpadDebug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = BreakpadDebug.xcconfig; path = ../../../common/mac/BreakpadDebug.xcconfig; sourceTree = SOURCE_ROOT; };
		8BFC812211FF99D5002CB4DC /* BreakpadRelease.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = BreakpadRelease.xcconfig; path = ../../../common/mac/BreakpadRelease.xcconfig; sourceTree = SOURCE_ROOT; };
		8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libcrypto.dylib; path = usr/lib/libcrypto.dylib; sourceTree = SDKROOT; };
		8BFC815411FF9B7F002CB4DC /* Carbon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Carbon.framework; path = System/Library/Frameworks/Carbon.framework; sourceTree = SDKROOT; };
		8BFC819211FF9C23002CB4DC /* CPlusTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CPlusTest.framework; path = Library/Frameworks/CPlusTest.framework; sourceTree = DEVELOPER_DIR; };
		8DD76F6C0486A84900D96B5E /* generator_test */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = generator_test; sourceTree = BUILT_PRODUCTS_DIR; };
		9B35FF560B267D5F008DE8C7 /* convert_UTF.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = convert_UTF.cc; path = ../../../common/convert_UTF.cc; sourceTree = SOURCE_ROOT; };
		9B35FF570B267D5F008DE8C7 /* convert_UTF.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = convert_UTF.h; path = ../../../common/convert_UTF.h; sourceTree = SOURCE_ROOT; };
		9B35FF580B267D5F008DE8C7 /* string_conversion.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = string_conversion.cc; path = ../../../common/string_conversion.cc; sourceTree = SOURCE_ROOT; };
		9B35FF590B267D5F008DE8C7 /* string_conversion.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = string_conversion.h; path = ../../../common/string_conversion.h; sourceTree = SOURCE_ROOT; };
		9B37CEEB0AF98ECD00FA4BD4 /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = System/Library/Frameworks/CoreFoundation.framework; sourceTree = SDKROOT; };
		9B7CA84E0B1297F200CD3A1D /* unit_test */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = unit_test; sourceTree = BUILT_PRODUCTS_DIR; };
		9B7CA8530B12989000CD3A1D /* minidump_file_writer_unittest.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = minidump_file_writer_unittest.cc; path = ../../minidump_file_writer_unittest.cc; sourceTree = "<group>"; };
		9BD82A9B0B00267E0055103E /* handler_test */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = handler_test; sourceTree = BUILT_PRODUCTS_DIR; };
		9BD82BFD0B01333D0055103E /* exception_handler_test.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; path = exception_handler_test.cc; sourceTree = SOURCE_ROOT; };
		9BD82BFE0B01333D0055103E /* minidump_generator_test.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; path = minidump_generator_test.cc; sourceTree = SOURCE_ROOT; };
		9BD82C090B0133520055103E /* exception_handler.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; path = exception_handler.cc; sourceTree = SOURCE_ROOT; };
		9BD82C0A0B0133520055103E /* exception_handler.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; path = exception_handler.h; sourceTree = SOURCE_ROOT; };
		9BD82C0B0B0133520055103E /* minidump_generator.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; path = minidump_generator.cc; sourceTree = SOURCE_ROOT; };
		9BD82C0C0B0133520055103E /* minidump_generator.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; path = minidump_generator.h; sourceTree = SOURCE_ROOT; };
		9BD82C230B01344C0055103E /* minidump_file_writer.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = minidump_file_writer.cc; path = ../../minidump_file_writer.cc; sourceTree = SOURCE_ROOT; };
		9BD82C240B01344C0055103E /* minidump_file_writer.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = minidump_file_writer.h; path = ../../minidump_file_writer.h; sourceTree = SOURCE_ROOT; };
		9BD82C2B0B01345E0055103E /* string_utilities.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = string_utilities.cc; path = ../../../common/mac/string_utilities.cc; sourceTree = SOURCE_ROOT; };
		9BD82C2C0B01345E0055103E /* string_utilities.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = string_utilities.h; path = ../../../common/mac/string_utilities.h; sourceTree = SOURCE_ROOT; };
		9BE3C01E0B0CE329009892DF /* minidump_file_writer-inl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "minidump_file_writer-inl.h"; path = "../../minidump_file_writer-inl.h"; sourceTree = SOURCE_ROOT; };
		D2F650FA0BEF947200920385 /* file_id.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = file_id.cc; path = ../../../common/mac/file_id.cc; sourceTree = SOURCE_ROOT; };
		D2F650FB0BEF947200920385 /* file_id.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = file_id.h; path = ../../../common/mac/file_id.h; sourceTree = SOURCE_ROOT; };
		D2F650FC0BEF947200920385 /* macho_id.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = macho_id.cc; path = ../../../common/mac/macho_id.cc; sourceTree = SOURCE_ROOT; };
		D2F650FD0BEF947200920385 /* macho_id.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = macho_id.h; path = ../../../common/mac/macho_id.h; sourceTree = SOURCE_ROOT; };
		D2F650FE0BEF947200920385 /* macho_utilities.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = macho_utilities.cc; path = ../../../common/mac/macho_utilities.cc; sourceTree = SOURCE_ROOT; };
		D2F650FF0BEF947200920385 /* macho_utilities.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = macho_utilities.h; path = ../../../common/mac/macho_utilities.h; sourceTree = SOURCE_ROOT; };
		D2F651070BEF949A00920385 /* dynamic_images.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; path = dynamic_images.cc; sourceTree = "<group>"; };
		D2F651080BEF949A00920385 /* dynamic_images.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; path = dynamic_images.h; sourceTree = "<group>"; };
		D2F6510C0BEF94EB00920385 /* macho_walker.cc */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.cpp.cpp; name = macho_walker.cc; path = ../../../common/mac/macho_walker.cc; sourceTree = SOURCE_ROOT; };
		D2F6510D0BEF94EB00920385 /* macho_walker.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = macho_walker.h; path = ../../../common/mac/macho_walker.h; sourceTree = SOURCE_ROOT; };
		F917C4F70E03265A00F86017 /* breakpad_exc_server.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = breakpad_exc_server.c; sourceTree = "<group>"; };
		F917C4F80E03265A00F86017 /* breakpad_exc_server.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = breakpad_exc_server.h; sourceTree = "<group>"; };
		F93A88750E8B4C700026AF89 /* octestcases.octest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = octestcases.octest; sourceTree = BUILT_PRODUCTS_DIR; };
		F93A88760E8B4C700026AF89 /* obj-cTestCases-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "obj-cTestCases-Info.plist"; sourceTree = "<group>"; };
		F9721F300E8B07E800D7E813 /* dwarftests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dwarftests.h; sourceTree = "<group>"; };
		F9721F310E8B07E800D7E813 /* dwarftests.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = dwarftests.mm; sourceTree = "<group>"; };
		F9721F380E8B0CFC00D7E813 /* dump_syms.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dump_syms.h; path = ../../../common/mac/dump_syms.h; sourceTree = SOURCE_ROOT; };
		F9721F390E8B0D0D00D7E813 /* dump_syms.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = dump_syms.cc; path = ../../../common/mac/dump_syms.cc; sourceTree = SOURCE_ROOT; };
		F9721F6B0E8B0D7000D7E813 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		F9721F760E8B0DC700D7E813 /* bytereader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = bytereader.cc; path = ../../../common/dwarf/bytereader.cc; sourceTree = SOURCE_ROOT; };
		F9721F770E8B0DC700D7E813 /* dwarf2reader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dwarf2reader.cc; path = ../../../common/dwarf/dwarf2reader.cc; sourceTree = SOURCE_ROOT; };
		F9721F780E8B0DC700D7E813 /* functioninfo.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = functioninfo.cc; path = ../../../common/dwarf/functioninfo.cc; sourceTree = SOURCE_ROOT; };
		F9721FA10E8B0E2300D7E813 /* SenTestingKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SenTestingKit.framework; path = Library/Frameworks/SenTestingKit.framework; sourceTree = DEVELOPER_DIR; };
		F9721FA80E8B0E4800D7E813 /* md5.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = md5.cc; path = ../../../common/md5.cc; sourceTree = SOURCE_ROOT; };
		F982089A0DB3280D0017AECA /* breakpad_nlist_test.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = breakpad_nlist_test.h; sourceTree = "<group>"; };
		F982089B0DB3280D0017AECA /* breakpad_nlist_test.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = breakpad_nlist_test.cc; sourceTree = "<group>"; };
		F98208A10DB32CAE0017AECA /* breakpad_nlist_64.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = breakpad_nlist_64.cc; sourceTree = "<group>"; };
		F98208A20DB32CAE0017AECA /* breakpad_nlist_64.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = breakpad_nlist_64.h; sourceTree = "<group>"; };
		F9AE19B50DB040E300C98454 /* minidump_tests32-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "minidump_tests32-Info.plist"; sourceTree = "<group>"; };
		F9AE19C30DB04A9500C98454 /* minidump_tests64.cptest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = minidump_tests64.cptest; sourceTree = BUILT_PRODUCTS_DIR; };
		F9AE5B330DBFDBA300505983 /* minidump_tests32.cptest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = minidump_tests32.cptest; sourceTree = BUILT_PRODUCTS_DIR; };
		F9AE5B340DBFDBA300505983 /* minidump_tests64-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "minidump_tests64-Info.plist"; sourceTree = "<group>"; };
		F9C5A4200DB82DD800209C76 /* DynamicImagesTests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DynamicImagesTests.h; sourceTree = "<group>"; };
		F9C5A4210DB82DD800209C76 /* DynamicImagesTests.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = DynamicImagesTests.cc; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8DD76F660486A84900D96B5E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B37CEEC0AF98ECD00FA4BD4 /* CoreFoundation.framework in Frameworks */,
				8BFC813F11FF9A58002CB4DC /* libcrypto.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9B7CA84C0B1297F200CD3A1D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8BFC814511FF9A9D002CB4DC /* libcrypto.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BD82A990B00267E0055103E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9BD82AC10B0029DF0055103E /* CoreFoundation.framework in Frameworks */,
				8BFC814411FF9A9C002CB4DC /* libcrypto.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F93A88720E8B4C700026AF89 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8BFC814A11FF9B13002CB4DC /* libcrypto.dylib in Frameworks */,
				8BFC814B11FF9B3F002CB4DC /* SenTestingKit.framework in Frameworks */,
				8BFC814C11FF9B3F002CB4DC /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9AE19C00DB04A9500C98454 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8BFC814811FF9B13002CB4DC /* libcrypto.dylib in Frameworks */,
				8BFC81A211FF9C2E002CB4DC /* CPlusTest.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9AE5B300DBFDBA300505983 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F9721F6C0E8B0D7000D7E813 /* Cocoa.framework in Frameworks */,
				F9721FA20E8B0E2300D7E813 /* SenTestingKit.framework in Frameworks */,
				8BFC814911FF9B13002CB4DC /* libcrypto.dylib in Frameworks */,
				8BFC81A311FF9C2F002CB4DC /* CPlusTest.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08FB7794FE84155DC02AAC07 /* MinidumpWriter */ = {
			isa = PBXGroup;
			children = (
				8BFC812011FF99D5002CB4DC /* Breakpad.xcconfig */,
				8BFC812111FF99D5002CB4DC /* BreakpadDebug.xcconfig */,
				8BFC812211FF99D5002CB4DC /* BreakpadRelease.xcconfig */,
				F9721FA80E8B0E4800D7E813 /* md5.cc */,
				F9721F760E8B0DC700D7E813 /* bytereader.cc */,
				F9721F770E8B0DC700D7E813 /* dwarf2reader.cc */,
				F9721F780E8B0DC700D7E813 /* functioninfo.cc */,
				F9721F390E8B0D0D00D7E813 /* dump_syms.cc */,
				F9721F380E8B0CFC00D7E813 /* dump_syms.h */,
				F917C4F70E03265A00F86017 /* breakpad_exc_server.c */,
				F917C4F80E03265A00F86017 /* breakpad_exc_server.h */,
				F98208A10DB32CAE0017AECA /* breakpad_nlist_64.cc */,
				F98208A20DB32CAE0017AECA /* breakpad_nlist_64.h */,
				D2F6510C0BEF94EB00920385 /* macho_walker.cc */,
				D2F6510D0BEF94EB00920385 /* macho_walker.h */,
				D2F651070BEF949A00920385 /* dynamic_images.cc */,
				D2F651080BEF949A00920385 /* dynamic_images.h */,
				D2F650FA0BEF947200920385 /* file_id.cc */,
				D2F650FB0BEF947200920385 /* file_id.h */,
				D2F650FC0BEF947200920385 /* macho_id.cc */,
				D2F650FD0BEF947200920385 /* macho_id.h */,
				D2F650FE0BEF947200920385 /* macho_utilities.cc */,
				D2F650FF0BEF947200920385 /* macho_utilities.h */,
				F9C5A41F0DB82DB000209C76 /* testcases */,
				9BD82C040B0133420055103E /* Breakpad */,
				08FB7795FE84155DC02AAC07 /* Source */,
				9B37CEEA0AF98EB600FA4BD4 /* Frameworks */,
				1AB674ADFE9D54B511CA2CBB /* Products */,
				F9AE19B50DB040E300C98454 /* minidump_tests32-Info.plist */,
				F9AE5B340DBFDBA300505983 /* minidump_tests64-Info.plist */,
				F93A88760E8B4C700026AF89 /* obj-cTestCases-Info.plist */,
			);
			name = MinidumpWriter;
			sourceTree = "<group>";
		};
		08FB7795FE84155DC02AAC07 /* Source */ = {
			isa = PBXGroup;
			children = (
				9BD82BFD0B01333D0055103E /* exception_handler_test.cc */,
				9BD82BFE0B01333D0055103E /* minidump_generator_test.cc */,
				9B7CA8530B12989000CD3A1D /* minidump_file_writer_unittest.cc */,
			);
			name = Source;
			sourceTree = "<group>";
		};
		1AB674ADFE9D54B511CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				8DD76F6C0486A84900D96B5E /* generator_test */,
				9BD82A9B0B00267E0055103E /* handler_test */,
				9B7CA84E0B1297F200CD3A1D /* unit_test */,
				F9AE19C30DB04A9500C98454 /* minidump_tests64.cptest */,
				F9AE5B330DBFDBA300505983 /* minidump_tests32.cptest */,
				F93A88750E8B4C700026AF89 /* octestcases.octest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9B37CEEA0AF98EB600FA4BD4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8BFC813E11FF9A58002CB4DC /* libcrypto.dylib */,
				8BFC815411FF9B7F002CB4DC /* Carbon.framework */,
				F9721FA10E8B0E2300D7E813 /* SenTestingKit.framework */,
				F9721F6B0E8B0D7000D7E813 /* Cocoa.framework */,
				9B37CEEB0AF98ECD00FA4BD4 /* CoreFoundation.framework */,
				8BFC819211FF9C23002CB4DC /* CPlusTest.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9BD82C040B0133420055103E /* Breakpad */ = {
			isa = PBXGroup;
			children = (
				9B35FF560B267D5F008DE8C7 /* convert_UTF.cc */,
				9B35FF570B267D5F008DE8C7 /* convert_UTF.h */,
				9B35FF580B267D5F008DE8C7 /* string_conversion.cc */,
				9B35FF590B267D5F008DE8C7 /* string_conversion.h */,
				9BD82C090B0133520055103E /* exception_handler.cc */,
				9BD82C0A0B0133520055103E /* exception_handler.h */,
				9BD82C0B0B0133520055103E /* minidump_generator.cc */,
				9BD82C0C0B0133520055103E /* minidump_generator.h */,
				9BD82C230B01344C0055103E /* minidump_file_writer.cc */,
				9BE3C01E0B0CE329009892DF /* minidump_file_writer-inl.h */,
				9BD82C240B01344C0055103E /* minidump_file_writer.h */,
				9BD82C2B0B01345E0055103E /* string_utilities.cc */,
				9BD82C2C0B01345E0055103E /* string_utilities.h */,
			);
			name = Breakpad;
			sourceTree = "<group>";
		};
		F9C5A41F0DB82DB000209C76 /* testcases */ = {
			isa = PBXGroup;
			children = (
				F982089A0DB3280D0017AECA /* breakpad_nlist_test.h */,
				F982089B0DB3280D0017AECA /* breakpad_nlist_test.cc */,
				F9C5A4200DB82DD800209C76 /* DynamicImagesTests.h */,
				F9C5A4210DB82DD800209C76 /* DynamicImagesTests.cc */,
				F9721F300E8B07E800D7E813 /* dwarftests.h */,
				F9721F310E8B07E800D7E813 /* dwarftests.mm */,
			);
			path = testcases;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8DD76F620486A84900D96B5E /* generator_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1DEB923108733DC60010E9CD /* Build configuration list for PBXNativeTarget "generator_test" */;
			buildPhases = (
				8DD76F640486A84900D96B5E /* Sources */,
				8DD76F660486A84900D96B5E /* Frameworks */,
				8DD76F690486A84900D96B5E /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = generator_test;
			productInstallPath = "$(HOME)/bin";
			productName = MinidumpWriter;
			productReference = 8DD76F6C0486A84900D96B5E /* generator_test */;
			productType = "com.apple.product-type.tool";
		};
		9B7CA84D0B1297F200CD3A1D /* unit_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9B7CA8500B12984300CD3A1D /* Build configuration list for PBXNativeTarget "unit_test" */;
			buildPhases = (
				9B7CA84B0B1297F200CD3A1D /* Sources */,
				9B7CA84C0B1297F200CD3A1D /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = unit_test;
			productName = "filewriter unit test";
			productReference = 9B7CA84E0B1297F200CD3A1D /* unit_test */;
			productType = "com.apple.product-type.tool";
		};
		9BD82A9A0B00267E0055103E /* handler_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9BD82AA60B0026BF0055103E /* Build configuration list for PBXNativeTarget "handler_test" */;
			buildPhases = (
				9BD82A980B00267E0055103E /* Sources */,
				9BD82A990B00267E0055103E /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = handler_test;
			productName = ExceptionTester;
			productReference = 9BD82A9B0B00267E0055103E /* handler_test */;
			productType = "com.apple.product-type.tool";
		};
		F93A88740E8B4C700026AF89 /* obj-c_TestCases */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F93A88790E8B4C700026AF89 /* Build configuration list for PBXNativeTarget "obj-c_TestCases" */;
			buildPhases = (
				F93A88700E8B4C700026AF89 /* Resources */,
				F93A88710E8B4C700026AF89 /* Sources */,
				F93A88720E8B4C700026AF89 /* Frameworks */,
				F93A88730E8B4C700026AF89 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "obj-c_TestCases";
			productName = octestcases;
			productReference = F93A88750E8B4C700026AF89 /* octestcases.octest */;
			productType = "com.apple.product-type.bundle";
		};
		F9AE19C20DB04A9500C98454 /* minidump_tests64 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F9AE19C70DB04AA200C98454 /* Build configuration list for PBXNativeTarget "minidump_tests64" */;
			buildPhases = (
				F9AE19BE0DB04A9500C98454 /* Resources */,
				F9AE19BF0DB04A9500C98454 /* Sources */,
				F9AE19C00DB04A9500C98454 /* Frameworks */,
				F9AE19C10DB04A9500C98454 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = minidump_tests64;
			productName = minidump_tests;
			productReference = F9AE19C30DB04A9500C98454 /* minidump_tests64.cptest */;
			productType = "com.apple.product-type.bundle";
		};
		F9AE5B320DBFDBA300505983 /* minidump_tests32 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F9AE5B380DBFDBA300505983 /* Build configuration list for PBXNativeTarget "minidump_tests32" */;
			buildPhases = (
				F9AE5B2E0DBFDBA300505983 /* Resources */,
				F9AE5B2F0DBFDBA300505983 /* Sources */,
				F9AE5B300DBFDBA300505983 /* Frameworks */,
				F9AE5B310DBFDBA300505983 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = minidump_tests32;
			productName = Untitled;
			productReference = F9AE5B330DBFDBA300505983 /* minidump_tests32.cptest */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08FB7793FE84155DC02AAC07 /* Project object */ = {
			isa = PBXProject;
			buildConfigurationList = 1DEB923508733DC60010E9CD /* Build configuration list for PBXProject "minidump_test" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
			);
			mainGroup = 08FB7794FE84155DC02AAC07 /* MinidumpWriter */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8DD76F620486A84900D96B5E /* generator_test */,
				9BD82A9A0B00267E0055103E /* handler_test */,
				9B7CA84D0B1297F200CD3A1D /* unit_test */,
				F9AE19C20DB04A9500C98454 /* minidump_tests64 */,
				F9AE5B320DBFDBA300505983 /* minidump_tests32 */,
				F93A88740E8B4C700026AF89 /* obj-c_TestCases */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F93A88700E8B4C700026AF89 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9AE19BE0DB04A9500C98454 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9AE5B2E0DBFDBA300505983 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		F93A88730E8B4C700026AF89 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Run the unit tests in this test bundle.\n\"${SYSTEM_DEVELOPER_DIR}/Tools/RunUnitTests\"\n";
		};
		F9AE19C10DB04A9500C98454 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Run the unit tests in this test bundle.\n\"${SYSTEM_DEVELOPER_DIR}/Tools/RunUnitTests\"\n# Run gcov on the framework getting tested\nif [ \"${CONFIGURATION}\" = 'Coverage' ];\nthen\n     FRAMEWORK_NAME=minidump_tests64\n     FRAMEWORK_OBJ_DIR=${OBJROOT}/${PROJECT_NAME}.build/${CONFIGURATION}/${FRAMEWORK_NAME}.build/Objects-normal/${NATIVE_ARCH_ACTUAL}\n     mkdir -p coverage\n     pushd coverage\n     echo find ${OBJROOT} -name *.gcda -exec gcov -o ${FRAMEWORK_OBJ_DIR} {} \\;\n     find ${OBJROOT} -name *.gcda -exec gcov -o ${FRAMEWORK_OBJ_DIR} {} \\;\n     popd\nfi ";
		};
		F9AE5B310DBFDBA300505983 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Run the unit tests in this test bundle.\n\"${SYSTEM_DEVELOPER_DIR}/Tools/RunUnitTests\"\n\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8DD76F640486A84900D96B5E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9BD82C020B01333D0055103E /* minidump_generator_test.cc in Sources */,
				9BD82C0F0B0133520055103E /* exception_handler.cc in Sources */,
				9BD82C110B0133520055103E /* minidump_generator.cc in Sources */,
				9BD82C260B01344C0055103E /* minidump_file_writer.cc in Sources */,
				9BD82C2E0B01345E0055103E /* string_utilities.cc in Sources */,
				D2F651000BEF947200920385 /* file_id.cc in Sources */,
				D2F651020BEF947200920385 /* macho_id.cc in Sources */,
				D2F651040BEF947200920385 /* macho_utilities.cc in Sources */,
				D2F651090BEF949A00920385 /* dynamic_images.cc in Sources */,
				D2F6510E0BEF94EB00920385 /* macho_walker.cc in Sources */,
				D2F651110BEF951700920385 /* string_conversion.cc in Sources */,
				D2F651150BEF953000920385 /* convert_UTF.cc in Sources */,
				8BFC81B011FF9C8D002CB4DC /* breakpad_nlist_64.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9B7CA84B0B1297F200CD3A1D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B7CA8540B12989000CD3A1D /* minidump_file_writer_unittest.cc in Sources */,
				9B7CA8550B1298A100CD3A1D /* minidump_file_writer.cc in Sources */,
				9BC1D2940B336F2300F2A2B4 /* convert_UTF.cc in Sources */,
				9BC1D2950B336F2500F2A2B4 /* string_conversion.cc in Sources */,
				8BFC81AE11FF9C8C002CB4DC /* breakpad_nlist_64.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BD82A980B00267E0055103E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9BD82BFF0B01333D0055103E /* exception_handler_test.cc in Sources */,
				9BD82C0D0B0133520055103E /* exception_handler.cc in Sources */,
				9BD82C0E0B0133520055103E /* minidump_generator.cc in Sources */,
				9BD82C250B01344C0055103E /* minidump_file_writer.cc in Sources */,
				9BD82C2D0B01345E0055103E /* string_utilities.cc in Sources */,
				9B35FF5A0B267D5F008DE8C7 /* convert_UTF.cc in Sources */,
				9B35FF5B0B267D5F008DE8C7 /* string_conversion.cc in Sources */,
				D2F6511B0BEF970E00920385 /* dynamic_images.cc in Sources */,
				D2F6511D0BEF973500920385 /* file_id.cc in Sources */,
				D2F6511E0BEF973600920385 /* macho_id.cc in Sources */,
				D2F6511F0BEF973900920385 /* macho_utilities.cc in Sources */,
				D2F651210BEF975400920385 /* macho_walker.cc in Sources */,
				8BFC81AF11FF9C8C002CB4DC /* breakpad_nlist_64.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F93A88710E8B4C700026AF89 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F93A88860E8B4C9A0026AF89 /* dwarftests.mm in Sources */,
				F93A88870E8B4C9A0026AF89 /* dump_syms.cc in Sources */,
				F93A88880E8B4C9A0026AF89 /* bytereader.cc in Sources */,
				F93A88890E8B4C9A0026AF89 /* dwarf2reader.cc in Sources */,
				F93A888A0E8B4C9A0026AF89 /* functioninfo.cc in Sources */,
				F93A888B0E8B4C9A0026AF89 /* md5.cc in Sources */,
				F93A887D0E8B4C8C0026AF89 /* macho_walker.cc in Sources */,
				F93A887E0E8B4C8C0026AF89 /* macho_id.cc in Sources */,
				F93A887F0E8B4C8C0026AF89 /* macho_utilities.cc in Sources */,
				F93A88800E8B4C8C0026AF89 /* file_id.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9AE19BF0DB04A9500C98454 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F9B34E870DBC1E1600306484 /* dynamic_images.cc in Sources */,
				F982089C0DB3280D0017AECA /* breakpad_nlist_test.cc in Sources */,
				F98208A30DB32CAE0017AECA /* breakpad_nlist_64.cc in Sources */,
				F9C5A4220DB82DD800209C76 /* DynamicImagesTests.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9AE5B2F0DBFDBA300505983 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F9AE5B390DBFDBDB00505983 /* dynamic_images.cc in Sources */,
				F9AE5B3A0DBFDBDB00505983 /* DynamicImagesTests.cc in Sources */,
				8BFC81AD11FF9C8A002CB4DC /* breakpad_nlist_64.cc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1DEB923208733DC60010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(DEVELOPER_FRAMEWORKS_DIR)\"",
				);
				PRODUCT_NAME = generator_test;
				USER_HEADER_SEARCH_PATHS = "../../../** $(inherited)";
			};
			name = Debug;
		};
		1DEB923308733DC60010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(DEVELOPER_FRAMEWORKS_DIR)\"",
				);
				PRODUCT_NAME = generator_test;
				USER_HEADER_SEARCH_PATHS = "../../../** $(inherited)";
			};
			name = Release;
		};
		1DEB923608733DC60010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8BFC812111FF99D5002CB4DC /* BreakpadDebug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		1DEB923708733DC60010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8BFC812211FF99D5002CB4DC /* BreakpadRelease.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
		9B7CA8510B12984300CD3A1D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = unit_test;
				USER_HEADER_SEARCH_PATHS = "../../../** $(inherited)";
			};
			name = Debug;
		};
		9B7CA8520B12984300CD3A1D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = unit_test;
				USER_HEADER_SEARCH_PATHS = "../../../** $(inherited)";
			};
			name = Release;
		};
		9BD82AA70B0026BF0055103E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = handler_test;
				USER_HEADER_SEARCH_PATHS = "../../.. $(inherited)";
			};
			name = Debug;
		};
		9BD82AA80B0026BF0055103E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = handler_test;
				USER_HEADER_SEARCH_PATHS = "../../.. $(inherited)";
			};
			name = Release;
		};
		F93A88770E8B4C700026AF89 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = "$(DEVELOPER_LIBRARY_DIR)/Frameworks";
				INFOPLIST_FILE = "obj-cTestCases-Info.plist";
				PRODUCT_NAME = octestcases;
				USER_HEADER_SEARCH_PATHS = "../../../..//**";
				WRAPPER_EXTENSION = octest;
			};
			name = Debug;
		};
		F93A88780E8B4C700026AF89 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = "$(DEVELOPER_LIBRARY_DIR)/Frameworks";
				INFOPLIST_FILE = "obj-cTestCases-Info.plist";
				PRODUCT_NAME = octestcases;
				USER_HEADER_SEARCH_PATHS = "../../../..//**";
				WRAPPER_EXTENSION = octest;
			};
			name = Release;
		};
		F9AE19C40DB04A9500C98454 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = "$(DEVELOPER_LIBRARY_DIR)/Frameworks";
				INFOPLIST_FILE = "minidump_tests64-Info.plist";
				PRODUCT_NAME = minidump_tests64;
				USER_HEADER_SEARCH_PATHS = "../../../**";
				WRAPPER_EXTENSION = cptest;
			};
			name = Debug;
		};
		F9AE19C50DB04A9500C98454 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = "$(DEVELOPER_LIBRARY_DIR)/Frameworks";
				INFOPLIST_FILE = "minidump_tests64-Info.plist";
				PRODUCT_NAME = minidump_tests64;
				USER_HEADER_SEARCH_PATHS = "../../../**";
				WRAPPER_EXTENSION = cptest;
			};
			name = Release;
		};
		F9AE5B350DBFDBA300505983 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = "$(DEVELOPER_LIBRARY_DIR)/Frameworks";
				INFOPLIST_FILE = "minidump_tests32-Info.plist";
				PRODUCT_NAME = minidump_tests32;
				USER_HEADER_SEARCH_PATHS = "../../../**";
				WRAPPER_EXTENSION = cptest;
			};
			name = Debug;
		};
		F9AE5B370DBFDBA300505983 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = "$(DEVELOPER_LIBRARY_DIR)/Frameworks";
				INFOPLIST_FILE = "minidump_tests32-Info.plist";
				PRODUCT_NAME = minidump_tests32;
				USER_HEADER_SEARCH_PATHS = "../../../**";
				WRAPPER_EXTENSION = cptest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1DEB923108733DC60010E9CD /* Build configuration list for PBXNativeTarget "generator_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB923208733DC60010E9CD /* Debug */,
				1DEB923308733DC60010E9CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1DEB923508733DC60010E9CD /* Build configuration list for PBXProject "minidump_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB923608733DC60010E9CD /* Debug */,
				1DEB923708733DC60010E9CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9B7CA8500B12984300CD3A1D /* Build configuration list for PBXNativeTarget "unit_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9B7CA8510B12984300CD3A1D /* Debug */,
				9B7CA8520B12984300CD3A1D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9BD82AA60B0026BF0055103E /* Build configuration list for PBXNativeTarget "handler_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9BD82AA70B0026BF0055103E /* Debug */,
				9BD82AA80B0026BF0055103E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F93A88790E8B4C700026AF89 /* Build configuration list for PBXNativeTarget "obj-c_TestCases" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F93A88770E8B4C700026AF89 /* Debug */,
				F93A88780E8B4C700026AF89 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F9AE19C70DB04AA200C98454 /* Build configuration list for PBXNativeTarget "minidump_tests64" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F9AE19C40DB04A9500C98454 /* Debug */,
				F9AE19C50DB04A9500C98454 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F9AE5B380DBFDBA300505983 /* Build configuration list for PBXNativeTarget "minidump_tests32" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F9AE5B350DBFDBA300505983 /* Debug */,
				F9AE5B370DBFDBA300505983 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08FB7793FE84155DC02AAC07 /* Project object */;
}
