// Copyright 2013 Google LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#ifndef CLIENT_MAC_HANDLER_UCONTEXT_COMPAT_H_
#define CLIENT_MAC_HANDLER_UCONTEXT_COMPAT_H_

#include <Availability.h>
#include <sys/ucontext.h>

// do not have sense at least for mac 10.15
#if defined(MAC_OS_X_VERSION_10_15)
typedef ucontext_t breakpad_ucontext_t;
#define breakpad_uc_mcontext uc_mcontext
#else
// The purpose of this file is to work around the fact that ucontext_t's
// uc_mcontext member is an mcontext_t rather than an mcontext64_t on ARM64.
#if defined(__aarch64__)
// <sys/ucontext.h> doesn't include the below file.
#include <sys/_types/_ucontext64.h>
typedef ucontext64_t breakpad_ucontext_t;
#define breakpad_uc_mcontext uc_mcontext64
#else
typedef ucontext_t breakpad_ucontext_t;
#define breakpad_uc_mcontext uc_mcontext
#endif  // defined(__aarch64__)
#endif

#endif  // CLIENT_MAC_HANDLER_UCONTEXT_COMPAT_H_
