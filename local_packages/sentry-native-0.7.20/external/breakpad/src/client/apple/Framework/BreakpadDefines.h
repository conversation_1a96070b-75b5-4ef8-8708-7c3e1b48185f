// Copyright 2011 Google LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google LLC nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONS<PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Keys for configuration file
#define kReporterMinidumpDirectoryKey "MinidumpDir"
#define kReporterMinidumpIDKey        "MinidumpID"

// Filename for recording uploaded IDs
#define kReporterLogFilename          "uploads.log"

// The default subdirectory of the Library to put crash dumps in
// The subdirectory is
//  ~/Library/<kDefaultLibrarySubdirectory>/<GoogleBreakpadProduct>
#define kDefaultLibrarySubdirectory   "Breakpad"

// Specify some special keys to be used in the configuration file that is
// generated by Breakpad and consumed by the crash_sender.
#define BREAKPAD_PRODUCT               "BreakpadProduct"
#define BREAKPAD_PRODUCT_DISPLAY       "BreakpadProductDisplay"
#define BREAKPAD_VERSION               "BreakpadVersion"
#define BREAKPAD_VENDOR                "BreakpadVendor"
#define BREAKPAD_URL                   "BreakpadURL"
#define BREAKPAD_REPORT_INTERVAL       "BreakpadReportInterval"
#define BREAKPAD_SKIP_CONFIRM          "BreakpadSkipConfirm"
#define BREAKPAD_CONFIRM_TIMEOUT       "BreakpadConfirmTimeout"
#define BREAKPAD_SEND_AND_EXIT         "BreakpadSendAndExit"
#define BREAKPAD_DUMP_DIRECTORY        "BreakpadMinidumpLocation"
#define BREAKPAD_INSPECTOR_LOCATION    "BreakpadInspectorLocation"
#define BREAKPAD_REPORTER_EXE_LOCATION \
  "BreakpadReporterExeLocation"
#define BREAKPAD_LOGFILES              "BreakpadLogFiles"
#define BREAKPAD_LOGFILE_UPLOAD_SIZE   "BreakpadLogFileTailSize"
#define BREAKPAD_REQUEST_COMMENTS      "BreakpadRequestComments"
#define BREAKPAD_COMMENTS              "BreakpadComments"
#define BREAKPAD_REQUEST_EMAIL         "BreakpadRequestEmail"
#define BREAKPAD_EMAIL                 "BreakpadEmail"
#define BREAKPAD_SERVER_TYPE           "BreakpadServerType"
#define BREAKPAD_SERVER_PARAMETER_DICT "BreakpadServerParameters"
#define BREAKPAD_IN_PROCESS            "BreakpadInProcess"

// The keys below are NOT user supplied, and are used internally.
#define BREAKPAD_PROCESS_START_TIME       "BreakpadProcStartTime"
#define BREAKPAD_PROCESS_UP_TIME          "BreakpadProcessUpTime"
#define BREAKPAD_PROCESS_CRASH_TIME       "BreakpadProcessCrashTime"
#define BREAKPAD_LOGFILE_KEY_PREFIX       "BreakpadAppLogFile"
#define BREAKPAD_SERVER_PARAMETER_PREFIX  "BreakpadServerParameterPrefix_"
#define BREAKPAD_ON_DEMAND                "BreakpadOnDemand"
