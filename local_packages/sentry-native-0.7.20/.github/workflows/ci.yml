name: CI

on:
  push:
    branches:
      - master
      - "release/**"
    paths-ignore:
      - "*.md"
  pull_request:
    paths-ignore:
      - "*.md"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: <PERSON>t
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: make style

  build-ios:
    name: Xcode Build for iOS
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"
      - run: |
          cmake -B sentry-native-xcode -GXcode -DCMAKE_SYSTEM_NAME=iOS
          xcodebuild build -project sentry-native-xcode/Sentry-Native.xcodeproj -sdk iphonesimulator

  test:
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Linux (GCC 7, 32-bit)
            os: ubuntu-20.04
            CC: gcc-7
            CXX: g++-7
            TEST_X86: 1
          - name: Linux (GCC 9.4.0)
            os: ubuntu-20.04
            CC: gcc
            CXX: g++
            # ERROR_ON_WARNINGS: 1
          - name: Linux (GCC 12)
            os: ubuntu-22.04
            CC: gcc-12
            CXX: g++-12
            # ERROR_ON_WARNINGS: 1
            # The GCC analyzer 10.0.1 (as on CI) has an internal compiler error
            # currently, and is not stable enough to activate.
            # RUN_ANALYZER: gcc
          - name: Linux (clang 15 + asan + llvm-cov)
            os: ubuntu-22.04
            CC: clang-15
            CXX: clang++-15
            ERROR_ON_WARNINGS: 1
            RUN_ANALYZER: asan,llvm-cov
          - name: Linux (clang 15 + kcov)
            os: ubuntu-22.04
            CC: clang-15
            CXX: clang++-15
            ERROR_ON_WARNINGS: 1
            RUN_ANALYZER: kcov
          - name: Linux (gcc 12 + code-checker + valgrind)
            os: ubuntu-22.04
            RUN_ANALYZER: code-checker,valgrind
          - name: macOS 14 (xcode llvm)
            os: macos-14
            ERROR_ON_WARNINGS: 1
            SYSTEM_VERSION_COMPAT: 0
          - name: macOS 13 (xcode llvm)
            os: macos-13
            ERROR_ON_WARNINGS: 1
            SYSTEM_VERSION_COMPAT: 0
          - name: macOS 14 (xcode llvm + universal)
            os: macos-14
            ERROR_ON_WARNINGS: 1
            SYSTEM_VERSION_COMPAT: 0
            CMAKE_DEFINES: -DCMAKE_OSX_ARCHITECTURES=arm64;x86_64
          - name: macOS 15 (clang 18 + asan + llvm-cov)
            os: macos-15-large
            CC: clang
            CXX: clang++
            ERROR_ON_WARNINGS: 1
            SYSTEM_VERSION_COMPAT: 0
            RUN_ANALYZER: asan,llvm-cov
          - name: Windows (old VS, 32-bit)
            os: windows-2019
            TEST_X86: 1
          - name: Windows (latest)
            os: windows-latest
          - name: LLVM-Mingw
            os: windows-latest
            TEST_MINGW: 1
            MINGW_PKG_PREFIX: x86_64-w64-mingw32
            MINGW_ASM_MASM_COMPILER: llvm-ml;-m64
          # The Android emulator is currently only available on macos, see:
          # https://docs.microsoft.com/en-us/azure/devops/pipelines/ecosystems/android?view=azure-devops#test-on-the-android-emulator
          # TODO: switch to reactivecircus/android-emulator-runner, concurrently running emulators continuously fail now.
          # - name: Android (API 31, NDK 23)
          #   os: macos-15-large
          #   ANDROID_API: 31
          #   ANDROID_NDK: 23.2.8568313
          #   ANDROID_ARCH: x86_64
          - name: Android (API 35, NDK 27)
            os: macos-15-large
            ANDROID_API: 35
            ANDROID_NDK: 27.2.12479018
            ANDROID_ARCH: x86_64

    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}

    env:
      TEST_X86: ${{ matrix.TEST_X86 }}
      TEST_MINGW: ${{ matrix.TEST_MINGW }}
      ERROR_ON_WARNINGS: ${{ matrix.ERROR_ON_WARNINGS }}
      RUN_ANALYZER: ${{ matrix.RUN_ANALYZER }}
      ANDROID_API: ${{ matrix.ANDROID_API }}
      ANDROID_NDK: ${{ matrix.ANDROID_NDK }}
      ANDROID_ARCH: ${{ matrix.ANDROID_ARCH }}
      CMAKE_DEFINES: ${{ matrix.CMAKE_DEFINES }}
      SYSTEM_VERSION_COMPAT: ${{ matrix.SYSTEM_VERSION_COMPAT }}

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: "pip"

      - name: Installing Linux Dependencies
        if: ${{ runner.os == 'Linux' && matrix.os != 'ubuntu-20.04' && !env['TEST_X86'] }}
        run: |
          sudo apt update
          sudo apt install cmake clang-14 clang-tools llvm kcov g++-12 valgrind zlib1g-dev libcurl4-openssl-dev

      - name: Installing Linux GCC 9.4.0 Dependencies
        if: ${{ runner.os == 'Linux' && matrix.os == 'ubuntu-20.04' && !env['TEST_X86'] }}
        run: |
          sudo apt update
          sudo apt install cmake clang-tools llvm kcov g++ valgrind zlib1g-dev libcurl4-openssl-dev

      - name: Installing Linux 32-bit Dependencies
        if: ${{ runner.os == 'Linux' && env['TEST_X86'] }}
        run: |
          sudo dpkg --add-architecture i386
          sudo apt update
          sudo apt install cmake gcc-7-multilib g++-7-multilib zlib1g-dev:i386 libssl-dev:i386 libcurl4-openssl-dev:i386

        # https://github.com/actions/runner-images/issues/9491
      - name: Decrease vm.mmap_rnd_bit to prevent ASLR ASAN issues
        if: ${{ runner.os == 'Linux' && contains(env['RUN_ANALYZER'], 'asan') }}
        run: sudo sysctl vm.mmap_rnd_bits=28

      - name: Installing CodeChecker
        if: ${{ contains(env['RUN_ANALYZER'], 'code-checker') }}
        run: sudo snap install codechecker --classic

      - name: Expose llvm@15 PATH for Mac
        if: ${{ runner.os == 'macOS' }}
        run: echo $(brew --prefix llvm@15)/bin >> $GITHUB_PATH
             
      - name: Expose llvm@18 PATH for Mac
        if: ${{ runner.os == 'macOS' && matrix.os == 'macos-15-large' && matrix.RUN_ANALYZER == 'asan,llvm-cov' }}
        run: echo $(brew --prefix llvm@18)/bin >> $GITHUB_PATH
        
      - name: Installing LLVM-MINGW Dependencies
        if: ${{ runner.os == 'Windows' && env['TEST_MINGW'] }}
        shell: powershell
        env:
          MINGW_PKG_PREFIX: ${{ matrix.MINGW_PKG_PREFIX }}
          MINGW_ASM_MASM_COMPILER: ${{ matrix.MINGW_ASM_MASM_COMPILER }}
        run: . "scripts\install-llvm-mingw.ps1"

      - name: Set up zlib for Windows
        if: ${{ runner.os == 'Windows' }}
        shell: powershell
        run: . "scripts\install-zlib.ps1"

      - name: Starting Android Simulator
        if: ${{ env['ANDROID_API'] }}
        run: bash scripts/start-android.sh
        timeout-minutes: 20

      - name: Test
        shell: bash
        run: |
          pip install --upgrade --requirement tests/requirements.txt
          [ "${{ matrix.CC }}" ] && export CC="${{ matrix.CC }}"
          [ "${{ matrix.CXX }}" ] && export CXX="${{ matrix.CXX }}"
          pytest --capture=no --verbose tests

      - name: "Upload to codecov.io"
        if: ${{ contains(env['RUN_ANALYZER'], 'cov') }}
        uses: codecov/codecov-action@e28ff129e5465c2c0dcc6f003fc735cb6ae0c673 # pin@v4.5.0
        with:
          directory: coverage
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: false
          verbose: true

  archive:
    name: Create Release Archive
    runs-on: ubuntu-latest
    needs: [lint, test]
    # only run this on pushes, combined with the CI triggers, this will only
    # run on master or the release branch
    if: ${{ needs.test.result == 'success' && github.event_name == 'push' }}
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Java Version
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "17"

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@579fbbe7221704325eb4c4d4bf20c2b0859fba76 # pin@v3
        with:
          gradle-home-cache-cleanup: true

      - name: Create source archive
        run: |
          rm -rf build .c* .e* .git* scripts Makefile external/breakpad/src/tools external/breakpad/src/processor
          zip -r sentry-native.zip .

      - name: Build NDK artifacts
        working-directory: ndk
        run: ./gradlew clean distZip

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.sha }}
          if-no-files-found: error
          path: |
            ndk/lib/build/distributions/*.zip
            sentry-native.zip
