<!--
This is a small checklist for you as a contributor:

* Make sure code is formatted properly: `make format`.
* Make sure to run tests for your code: `make test`.
* Create new tests where appropriate:
  - Create new unit-tests or integration-tests covering your change.
  - When you create a bugfix, try to add a regression-test as well.
  - Make sure you run the tests with a leak checker or other static analyzer.
-->
