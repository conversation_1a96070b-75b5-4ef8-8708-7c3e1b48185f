# 外部消息管理完整使用文档

## 概述

本文档涵盖了外部消息系统的完整使用方法，包括：
- 如何构造和发送外部消息（JSON 格式）
- 如何在应用中使用 `ExternalMessageManager` 处理消息

`ExternalMessageManager` 是一个统一的外部消息管理器，用于管理外部消息的监听、分发和队列处理。它将原本分散在各个 view_model 中的外部消息处理逻辑整合到一个单独的类中，提高了代码的复用性和可维护性。

## 主要功能

1. **消息监听**: 自动监听来自外部的消息
2. **消息分发**: 根据消息类型分发给相应的处理器
3. **队列管理**: 管理待处理的消息队列
4. **结果回调**: 提供消息处理结果的回调

## 外部消息格式规范

### 基本消息格式

所有外部消息都应遵循以下JSON结构：

```json
{
  "type": "消息类型",
  "data": {
    // 消息具体数据
  },
  "requestId": "可选的请求ID",
  "timestamp": 1704067200000
}
```

### 支持的消息类型

#### 1. 导入项目 (import_project)

用于将外部文件导入到应用中创建新项目。

**必需字段:**
- `fileList`: 要导入的文件列表

**可选字段:**
- `projectId`: 项目ID（如果指定且项目已存在，会询问是否覆盖）
- `projectName`: 项目名称
- `autoNavigate`: 是否自动导航到编辑界面（默认false）

**示例JSON:**

```json
{
  "type": "import_project",
  "data": {
    "projectName": "我的新项目",
    "fileList": [
      {
        "originalPath": "C:\\Users\\<USER>\\Pictures\\photo1.jpg",
        "historyId": "s_95c02280-3e11-4e73-bacb-3829fafd9db1",
        "isSelected": false
      },
      {
        "originalPath": "C:\\Users\\<USER>\\Pictures\\photo2.png",
        "historyId": "s_1064a125-2ea7-43a6-a11d-5d737e3143d5",
        "isSelected": true
      }
    ],
    "autoNavigate": true
  },
  "requestId": "req-123",
  "timestamp": 1704067200000
}
```

**FileItem 对象说明:**
- `originalPath`: 文件的原始路径（必需）
- `historyId`: 文件的历史记录ID，用于关联之前的处理记录（可选）
- `isSelected`: 文件是否被选中，用于批量操作时的状态管理（可选，默认false）

#### 2. 打开项目 (open_project)

用于打开现有项目。（暂未实现）

**必需字段:**
- `projectId`: 要打开的项目ID

**示例JSON:**

```json
{
  "type": "open_project",
  "data": {
    "projectId": "existing-project-uuid"
  },
  "requestId": "req-124",
  "timestamp": 1704067200000
}
```

#### 3. 导出项目 (export_project)

用于导出项目（暂未实现）。

**必需字段:**
- `projectId`: 要导出的项目ID
- `exportPath`: 导出路径

**可选字段:**
- `preset`: 导出预设配置

**示例JSON:**

```json
{
  "type": "export_project",
  "data": {
    "projectId": "project-uuid-to-export",
    "exportPath": "C:\\Users\\<USER>\\Desktop\\exported_project",
    "preset": "high_quality"
  },
  "requestId": "req-125",
  "timestamp": 1704067200000
}
```

### 外部应用集成示例

#### Windows C++ 实现

```cpp
// 在C++应用中，可以使用WM_COPYDATA消息发送数据
void SendMessageToTuringArt(const std::string& message) {
    HWND hwnd = FindWindow(nullptr, L"turing_art"); // 找到应用窗口
    if (hwnd != nullptr) {
        COPYDATASTRUCT cds;
        cds.dwData = 0;
        cds.cbData = message.length() + 1;
        cds.lpData = (LPVOID)message.c_str();

        SendMessage(hwnd, WM_COPYDATA, 0, (LPARAM)&cds);
    }
}

// 使用示例
void ImportProjectExample() {
    std::string jsonMessage = R"({
        "type": "import_project",
        "data": {
            "projectName": "外部项目",
            "filePaths": [
                "C:\\Images\\photo1.jpg",
                "C:\\Images\\photo2.png"
            ],
            "autoNavigate": true
        }
    })";

    SendMessageToTuringArt(jsonMessage);
}
```

### 文件路径要求

- 支持绝对路径
- 支持的文件格式：jpg, jpeg, png, bmp, gif等图片格式
- 文件必须存在且可读
- 路径分隔符：Windows使用反斜杠(\)或正斜杠(/)

### 外部消息错误处理

当消息格式不正确或处理失败时，系统会在日志中记录错误信息。

#### 常见错误

1. **无效的消息格式**: JSON格式错误或缺少必需字段
2. **文件不存在**: 指定的文件路径无效或文件不可访问
3. **不支持的消息类型**: 发送了未实现的消息类型
4. **数据解析失败**: 消息数据格式不符合要求

## 应用内消息处理（ViewModel）

### 1. 在 ViewModel 中初始化

```dart
class YourViewModel extends ChangeNotifier {
  late final ExternalMessageManager _externalMessageManager;

  YourViewModel() {
    _initExternalMessageManager();
  }

  void _initExternalMessageManager() {
    _externalMessageManager = ExternalMessageManager();
    
    // 初始化消息管理器
    _externalMessageManager.initialize(
      onMessageResult: (result) {
        // 处理消息结果
        _handleMessageResult(result);
      },
      onMessageReceived: () {
        // 有新消息到达时通知UI
        notifyListeners();
      },
    );

    // 注册消息处理器
    _registerMessageHandlers();
  }

  void _registerMessageHandlers() {
    // 注册导入项目处理器
    _externalMessageManager.registerHandler(
      ImportProjectMessageHandler(
        onImportProject: _handleImportProject,
      ),
    );

    // 注册其他处理器...
  }

  void _handleMessageResult(ExternalMessageResult result) {
    switch (result) {
      case ExternalMessageSuccess success:
        // 处理成功的逻辑
        break;
      case ExternalMessageError error:
        // 处理错误的逻辑
        break;
      case ExternalMessageProcessing processing:
        // 处理中的逻辑
        break;
    }
  }

  @override
  void dispose() {
    _externalMessageManager.dispose();
    super.dispose();
  }
}
```

### 2. 处理待处理消息

```dart
class YourView extends StatefulWidget {
  @override
  _YourViewState createState() => _YourViewState();
}

class _YourViewState extends State<YourView> {
  late YourViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = YourViewModel();
    
    // 在下一帧处理待处理的消息
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _processPendingMessages();
    });
  }

  void _processPendingMessages() {
    if (_viewModel.hasPendingExternalMessages) {
      _viewModel.processPendingExternalMessages(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<YourViewModel>(
      builder: (context, viewModel, child) {
        // 如果有待处理消息，自动处理
        if (viewModel.hasPendingExternalMessages) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _processPendingMessages();
          });
        }

        return YourWidgetContent();
      },
    );
  }
}
```

### 3. 自定义消息处理器

```dart
// 创建自定义消息处理器
class CustomMessageHandler extends ExternalMessageHandler {
  final Future<ExternalMessageResult> Function(
    BuildContext context,
    CustomMessageData data,
  ) _onCustomMessage;

  CustomMessageHandler({
    required Future<ExternalMessageResult> Function(
      BuildContext context,
      CustomMessageData data,
    ) onCustomMessage,
  }) : _onCustomMessage = onCustomMessage;

  @override
  String get handlerName => 'CustomMessageHandler';

  @override
  ExternalMessageType get supportedType => ExternalMessageType.custom;

  @override
  Future<ExternalMessageResult> handle(
    BuildContext context,
    ExternalMessage message,
  ) async {
    try {
      if (!validateMessage(message)) {
        return const ExternalMessageError('无效的自定义消息格式');
      }

      final customData = CustomMessageData.fromMap(message.data);
      if (customData == null) {
        return const ExternalMessageError('无法解析自定义消息数据');
      }

      return await _onCustomMessage(context, customData);
    } catch (e) {
      return ExternalMessageError('处理自定义消息失败: $e');
    }
  }
}

// 在 ViewModel 中注册
void _registerMessageHandlers() {
  _externalMessageManager.registerHandler(
    CustomMessageHandler(
      onCustomMessage: _handleCustomMessage,
    ),
  );
}

Future<ExternalMessageResult> _handleCustomMessage(
  BuildContext context,
  CustomMessageData data,
) async {
  // 处理自定义消息的逻辑
  try {
    // 执行业务逻辑
    await doSomethingWithCustomData(data);
    return const ExternalMessageSuccess('自定义消息处理成功');
  } catch (e) {
    return ExternalMessageError('自定义消息处理失败: $e');
  }
}
```

## API 参考

### ExternalMessageManager

#### 方法

- `initialize({Function(ExternalMessageResult)? onMessageResult, VoidCallback? onMessageReceived})`: 初始化消息管理器
- `registerHandler(ExternalMessageHandler handler)`: 注册消息处理器
- `dispose()`: 销毁消息管理器
- `processPendingMessages(BuildContext context)`: 处理所有待处理消息
- `clearPendingMessages()`: 清空待处理消息队列

#### 属性

- `bool hasPendingExternalMessages`: 是否有待处理的外部消息
- `int pendingMessageCount`: 当前待处理消息数量
- `List<ExternalMessageHandler> handlers`: 所有注册的处理器

## 注意事项

1. **单例模式**: `ExternalMessageManager` 使用单例模式，确保全局只有一个实例
2. **初始化**: 必须在使用前调用 `initialize()` 方法
3. **上下文**: 处理消息时需要提供有效的 `BuildContext`
4. **资源清理**: 在 `dispose()` 方法中必须调用 `_externalMessageManager.dispose()`
5. **线程安全**: 消息处理是异步的，注意并发安全

## 最佳实践

### 外部消息发送方

1. **始终包含timestamp**: 有助于调试和日志记录
2. **使用requestId**: 便于跟踪特定请求的处理状态
3. **验证文件路径**: 发送消息前确保文件存在
4. **错误处理**: 为消息发送失败做好准备
5. **测试**: 在生产环境前充分测试各种消息格式

### 应用内消息处理

1. **及早初始化**: 在 ViewModel 构造函数中初始化消息管理器
2. **合理的错误处理**: 在消息处理器中添加适当的错误处理逻辑
3. **资源管理**: 确保在适当的时候调用 `dispose()` 方法
4. **UI 更新**: 利用 `onMessageReceived` 回调及时更新UI
5. **测试**: 为消息处理逻辑编写单元测试

## 调试提示

- 检查应用日志了解消息处理状态
- 使用有效的JSON格式化工具验证消息格式
- 确保应用窗口处于可接收消息的状态
- 在开发环境中启用详细日志记录

### 命令行参数支持

应用支持通过命令行参数直接导入文件，`--file` 参数具备强大的自动识别能力：

#### 基础用法
```bash
# 普通文件路径
turing_art.exe --file "C:\Images\photo.jpg"

# 带历史ID
turing_art.exe --file "C:\Images\photo.jpg|s_abc123"

# 完整格式（路径+历史ID+选中状态）
turing_art.exe --file "C:\Images\photo.jpg|s_abc123|true"
```

#### 自动识别规则
- **无管道符**：当作普通文件路径处理
- **含管道符**：自动解析为文件对象格式
- **支持0~2个分割符**：灵活适应不同需求

#### 等效JSON转换
```bash
# 命令行
turing_art.exe --file "C:\photo1.jpg" --file "C:\photo2.jpg|s_123|true"

# 等效JSON
{
  "type": "import_project", 
  "data": {
    "fileList": [
      {
        "originalPath": "C:\\photo1.jpg",
        "historyId": null,
        "isSelected": false
      },
      {
        "originalPath": "C:\\photo2.jpg", 
        "historyId": "s_123",
        "isSelected": true
      }
    ]
  }
}
``` 