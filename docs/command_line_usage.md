# 图灵精修 - 命令行参数使用手册

## 概述

图灵精修支持通过命令行参数启动应用并执行特定操作，主要用于：
- 集成到其他应用程序
- 批处理脚本调用

## 基本语法

```bash
turing_art.exe [选项] [文件路径...]
```

## 导入图片创建项目

### 1. 导入单个图片（默认操作）
```bash
turing_art.exe "C:\Users\<USER>\Pictures\photo.jpg"
```

### 2. 导入多个图片
```bash
turing_art.exe "C:\Users\<USER>\Pictures\photo1.jpg" "C:\Users\<USER>\Pictures\photo2.jpg"
```

### 3. 使用 --file 参数导入
```bash
# 长参数格式
turing_art.exe --file="C:\Users\<USER>\Pictures\photo.jpg"

# 短参数格式
turing_art.exe -f "C:\Users\<USER>\Pictures\photo.jpg"
```

### 4. 导入图片并指定项目名称
```bash
# 长参数格式
turing_art.exe --file="C:\Users\<USER>\Pictures\photo.jpg" --name="我的新项目"

# 短参数格式
turing_art.exe -f "C:\Users\<USER>\Pictures\photo.jpg" -n "我的新项目"
```

### 5. 导入图片并自动进入编辑界面
```bash
turing_art.exe --file="C:\Users\<USER>\Pictures\photo.jpg" --auto-navigate
```

### 6. 使用 --action 参数明确指定导入操作
```bash
turing_art.exe --action=import --file="C:\Users\<USER>\Pictures\photo.jpg"
```

## 打开现有项目

### 7. 通过项目ID打开项目
```bash
# 使用 --open 标志
turing_art.exe --open --project="project-uuid-12345"

# 使用 --action 参数
turing_art.exe --action=open --project="project-uuid-12345"
```

## 复杂示例

### 8. 从文件管理器右键菜单打开多个图片
```bash
turing_art.exe "C:\path\to\image1.jpg" "C:\path\to\image2.png" "C:\path\to\image3.tiff"
```

### 9. 批处理脚本调用
```bash
turing_art.exe --import --file="C:\batch\input\*.jpg" --name="批处理项目"
```

### 10. 集成到其他应用程序
```bash
turing_art.exe --action=import --file="%1" --auto-navigate
```

## 参数说明

### 文件参数
| 参数 | 短参数 | 说明 | 示例 |
|------|--------|------|------|
| `--file` | `-f` | 指定要导入的文件路径 | `--file="C:\image.jpg"` |
| `--file-with-history` | 无 | 指定带历史ID和选中状态的文件 | `--file-with-history "path\|historyId\|isSelected"` |

### 项目参数
| 参数 | 短参数 | 说明 | 示例 |
|------|--------|------|------|
| `--project` | `-p` | 指定项目ID | `--project="uuid-12345"` |
| `--name` | `-n` | 指定项目名称 | `--name="我的项目"` |

### 操作参数
| 参数 | 说明 | 示例 |
|------|------|------|
| `--action` | 明确指定操作类型（import/open） | `--action=import` |
| `--import` | 标志：导入操作 | `--import` |
| `--open` | 标志：打开项目操作 | `--open` |
| `--auto-navigate` | 标志：自动进入编辑界面 | `--auto-navigate` |

## Windows 文件关联

可以将图灵精修设置为图片文件的默认程序，这样双击图片文件时会自动启动应用并导入该图片。

### 注册表配置示例
```registry
[HKEY_CLASSES_ROOT\Applications\turing_art.exe\shell\open\command]
@="\"C:\Program Files\TuringArt\turing_art.exe\" \"%1\""
```

## 快捷方式示例

创建桌面快捷方式，预设特定参数：

**目标：**
```
"C:\Program Files\TuringArt\turing_art.exe" "C:\default\image.jpg"
```

**工作目录：**
```
C:\Program Files\TuringArt
```

## 支持的文件格式

- JPG/JPEG
- PNG  
- TIFF
- BMP
- CR2（Canon RAW）
- 其他常见图片格式

## 错误处理

- **文件不存在**：应用会显示错误信息并忽略该文件
- **无效路径**：应用会正常启动到主界面
- **权限问题**：会提示用户检查文件访问权限

## 注意事项

1. **文件路径**：必须使用完整的绝对路径
2. **引号**：包含空格的路径必须用引号包围
3. **反斜杠**：Windows路径中的反斜杠会自动处理
4. **文件存在性**：只有存在的文件才会被处理
5. **自动识别**：`--file` 参数会自动识别是否包含管道符并相应处理
6. **空历史ID**：如果历史ID为空字符串，会被当作 `null` 处理

## 开发调试

### VS Code 调试配置示例

在 `.vscode/launch.json` 中配置调试启动参数：

```json
{
  "name": "Flutter Debug - Import Single File",
  "request": "launch",
  "type": "dart",
  "program": "lib/main.dart",
  "toolArgs": [
    "--dart-define=IS_DEBUG_FLAG=true",
    "--dart-entrypoint-args=--import --project=test-123 --name=测试项目 --file=C:\\Users\\<USER>\\Downloads\\image.jpg"
  ]
}
```

### 测试用例参考

可以使用以下命令测试各种参数组合：

```bash
# 基本导入
turing_art.exe "C:\test\image.jpg"

# 完整参数
turing_art.exe --file="C:\test\image.jpg" --name="测试项目" --auto-navigate

# 多文件导入
turing_art.exe --file="C:\test\image1.jpg" --file="C:\test\image2.jpg"

# 打开项目
turing_art.exe --open --project="project-uuid-12345"
```

## 常见问题

### Q: 如何处理包含特殊字符的文件路径？
A: 使用双引号包围整个路径，例如：`"C:\我的文件夹\图片 (1).jpg"`

### Q: 可以同时导入多种格式的文件吗？
A: 可以，应用会自动识别支持的图片格式并导入

### Q: 如何在批处理脚本中使用？
A: 在批处理文件中调用，例如：
```batch
@echo off
"C:\Program Files\TuringArt\turing_art.exe" --file="%~1" --auto-navigate
```

## 命令行参数使用指南

### 基本用法

```bash
# 导入单个文件
turing_art.exe --file "C:\Images\photo.jpg"

# 导入多个文件
turing_art.exe --file "C:\Images\photo1.jpg" --file "C:\Images\photo2.png"

# 指定项目名称
turing_art.exe --name "我的项目" --file "C:\Images\photo.jpg"

# 自动导航到编辑界面
turing_art.exe --auto-navigate --file "C:\Images\photo.jpg"
```

### 高级用法（文件参数自动识别格式）

```bash
# 普通文件路径（无分割符）
turing_art.exe --file "C:\Images\photo1.jpg"

# 带历史ID（1个分割符）
turing_art.exe --file "C:\Images\photo2.jpg|s_95c02280-3e11-4e73-bacb-3829fafd9db1"

# 带历史ID和选中状态（2个分割符）
turing_art.exe --file "C:\Images\photo3.jpg|s_1064a125-2ea7-43a6-a11d-5d737e3143d5|true"

# 混合使用不同格式
turing_art.exe --name "混合项目" \
  --file "C:\Images\new_photo.jpg" \
  --file "C:\Images\old_photo.jpg|s_abc123" \
  --file "C:\Images\selected_photo.jpg|s_def456|true" \
  --auto-navigate
```

### 参数说明

| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--file` | `-f` | 指定文件（支持多种格式） | `--file "C:\image.jpg"` |
| `--name` | `-n` | 指定项目名称 | `--name "我的项目"` |
| `--project` | `-p` | 指定项目ID | `--project "project-uuid"` |
| `--auto-navigate` | 无 | 导入后自动导航到编辑界面 | `--auto-navigate` |
| `--import` | 无 | 明确指定导入操作 | `--import` |
| `--open` | 无 | 打开已存在的项目 | `--open` |
| `--action` | 无 | 指定操作类型 | `--action import` |

### --file 参数格式详解

`--file` 参数自动识别不同格式，使用管道符(`|`)分隔：

| 格式 | 示例 | 说明 |
|------|------|------|
| 仅路径 | `--file "C:\image.jpg"` | 普通文件路径 |
| 路径+历史ID | `--file "C:\image.jpg\|s_abc123"` | 包含历史记录ID |
| 路径+历史ID+选中状态 | `--file "C:\image.jpg\|s_abc123\|true"` | 完整格式 |

**格式说明：**
1. **文件路径**（必需）：文件的完整路径
2. **历史ID**（可选）：用于关联之前的处理记录，可以为空字符串
3. **是否选中**（可选）：`true` 或 `false`，默认为 `false`

### 实际使用场景

#### 1. 新项目导入
```bash
# 创建新项目并导入文件
turing_art.exe --name "风景照片" --file "C:\Photos\sunset.jpg" --file "C:\Photos\mountain.jpg"
```

#### 2. 继续之前的工作
```bash
# 导入之前处理过的文件，保持历史记录
turing_art.exe --name "继续编辑" \
  --file "C:\Photos\sunset.jpg|s_95c02280-3e11-4e73-bacb-3829fafd9db1|true" \
  --auto-navigate
```

#### 3. 批量处理
```bash
# 导入多个文件，部分已选中用于批量操作
turing_art.exe --name "批量处理" \
  --file "C:\Photos\img1.jpg|s_id1|true" \
  --file "C:\Photos\img2.jpg|s_id2|false" \
  --file "C:\Photos\img3.jpg|s_id3|true"
```

#### 4. 混合格式使用
```bash
# 同时使用不同格式的文件参数
turing_art.exe --name "混合项目" \
  --file "C:\Photos\new1.jpg" \
  --file "C:\Photos\new2.jpg" \
  --file "C:\Photos\processed1.jpg|s_history1" \
  --file "C:\Photos\processed2.jpg|s_history2|true" \
  --auto-navigate
```

### 注意事项

1. **文件路径**：必须使用完整的绝对路径
2. **引号**：包含空格的路径必须用引号包围
3. **反斜杠**：Windows路径中的反斜杠会自动处理
4. **文件存在性**：只有存在的文件才会被处理
5. **自动识别**：`--file` 参数会自动识别是否包含管道符并相应处理
6. **空历史ID**：如果历史ID为空字符串，会被当作 `null` 处理

---
