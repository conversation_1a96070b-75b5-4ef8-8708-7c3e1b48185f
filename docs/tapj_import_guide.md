# TAPJ文件导入工程完整使用指南

## 概述

TAPJ（Turing Art ProJect）是图灵精修的项目工程文件格式，包含完整的项目信息和原图文件列表。通过tapj文件，用户可以一键导入完整的项目配置，无需手动指定文件列表或其他参数。

## 支持的调用方式

### 1. 命令行调用

#### 语法
```bash
turing_art.exe --tapj=<tapj文件路径>
```

#### 示例

##### Windows路径（推荐使用双反斜杠）
```bash
turing_art.exe --tapj="C:\\Users\\<USER>\\Documents\\Projects\\我的项目.tapj"

```

#### 命令行参数说明

| 参数 | 必需 | 说明 |
|------|------|------|
| `--tapj` | 是 | tapj文件的完整路径，支持绝对路径和相对路径 |

**注意事项：**
- 路径中包含空格时必须使用引号包围
- 支持中文路径和文件名
- 路径分隔符可以使用 `\` 或 `/`

### 2. 外部JSON消息调用

#### 消息格式
```json
{
  "type": "import_project",
  "data": {
    "tapj": "<tapj文件路径>"
  },
}
```

#### 示例

##### 基本使用
```json
{
  "type": "import_project",
  "data": {
    "tapj": "C:\\Users\\<USER>\\Documents\\Projects\\我的项目.tapj"
  }
}
```

#### JSON消息字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `type` | string | 是 | 必须为 "import_project" |
| `data.tapj` | string | 是 | tapj文件的完整路径 |


**重要提醒：**
- JSON中的路径反斜杠必须转义为双反斜杠 `\\`
- 或者使用正斜杠 `/` 代替反斜杠


**注意**：tapj导入功能当前处于开发阶段，文档内容可能在后续版本中完善