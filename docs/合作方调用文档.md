# 图灵精修合作方调用文档

## 版本及更新日期
- **版本**：v0.4
- **更新日期**：2025.08.04
- **适用软件版本**：图灵精修 v1.8+

## 文档简介

本文档为第三方合作伙伴提供图灵精修应用的调用接口说明，包含应用启动检测、消息传递、参数配置等完整的集成方案。

**适用场景：**
- 第三方软件唤起图灵精修功能
- 选版/选片软件小样后确认选版信息
- 批量图片处理流程
- 自动化工作流集成

### 两种通信方式说明

图灵精修提供两种通信方式，根据应用状态自动选择：

1. **窗口消息通信**（应用已启动时）
   - 使用 `WM_COPYDATA` 发送JSON格式消息
   - 适用于图灵精修已在运行的情况
   - 支持实时消息传递和交互

2. **命令行参数通信**（应用未启动时）
   - 通过命令行参数启动应用并传递指令
   - 适用于首次启动或应用未运行的情况
   - 支持一次性参数传递和自动化脚本

**选择策略：**
- 优先检查图灵精修是否已启动（查找 "图灵精修" 窗口）
- 如果已启动，使用窗口消息通信
- 如果未启动，使用命令行参数启动应用



## 应用唤起及消息发送

### 标准调用流程

以下是检测和启动图灵精修应用的标准流程：

```mermaid
flowchart TD
    A["开始调用图灵精修"] --> B["检查 '图灵精修' 窗口是否存在"]
    B --> C{"窗口存在?"}
    C -->|是| D["发送窗口消息"]
    C -->|否| E["尝试命令行启动"]
    E --> F["检查固定路径是否存在"]
    F --> G{"固定路径存在?"}
    G -->|是| H["直接调用并传递命令行参数"]
    G -->|否| I["通过注册表检查是否安装"]
    I --> J{"注册表存在?"}
    J -->|是| K["读取注册表安装地址"]
    K --> L["使用安装地址调用并传递参数"]
    J -->|否| M["报告异常：未安装图灵精修"]
    D --> N["调用成功"]
    H --> N
    L --> N
    M --> O["调用失败"]
```

### 实现细节说明

1. **窗口检测**：查找窗口标题为 "图灵精修" 的进程窗口，使用 `FindWindowW(NULL, L"图灵精修")`
2. **固定路径检查**：检查以下常见安装路径：
   - `C:\Program Files\图灵精修\`
   - `C:\图灵精修\`
   - `D:\Program Files\图灵精修\`
   - `D:\图灵精修\`
   - ...

3. **注册表检查**：依次检查以下注册表路径：
   - `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall`

### 消息参数说明及示例

下表统一展示了窗口消息和命令行参数的对应关系：

| 功能 | 窗口消息（JSON格式） | 命令行参数 | 说明 |
|------|-------------------|------------|------|
| 智能导入 | `"type": "smart"` | `--smart` | 启用智能导入模式 |
| 图像路径 | `"imagePaths": ["path1", "path2"]` | `--images=path1\|path2` | 指定要导入的图像（或选片图像）文件路径 |
| 项目名称 | `"projectName": "项目名"` | `--name=项目名` | 指定新建项目的名称 |
| 强制新建 | `"newProject": true` | `--newProject` | 强制创建新项目（忽略现有TAPJ工程文件） |
| 自动导航 | `"autoNavigate": true` | `--auto-navigate` | 导入后自动导航到编辑界面 |
| 自定义参数 | `"customParams": {"key1": "value1", "key2": "value2"}` | `--custom-params='{"key1": "value1", "key2": "value2"}'` | 第三方自定义参数，支持任意JSON格式，用于合作方向图灵精修传递自定义内容（若需解析需要合作开发） |

**参数补充说明：**
1. 路径格式：
   - 窗口消息：json格式使用双反斜杠 `\\` 或正斜杠 `/`
   - 命令行：可直接使用单反斜杠 `\`，多个路径用 `|` 分隔
2. 参数可选性：
   - `type`/`--smart`：必需参数
   - `imagePaths`/`--images`：必需参数
   - 其他参数均为可选
3. 自定义参数格式：
   - 窗口消息：JSON对象格式，支持嵌套结构和各种数据类型
   - 命令行：JSON字符串格式，使用单引号包围以避免转义问题
   - 可用于第三方软件传递自定义业务参数，图灵精修会原样保存并在回调时返回
   - 若需解析三方自定义参数内容需联系进行合作开发

**示例：**

1. 窗口消息示例：

基本导入：
```json
{
  "type": "smart",
  "data": {
    "imagePaths": [
      "C:\\Users\\<USER>\\image1.jpg"
    ]
  }
}
```

完整参数：
```json
{
  "type": "smart",
  "data": {
    "imagePaths": [
      "C:\\Users\\<USER>\\image1.jpg"
    ],
    "projectName": "智能导入项目",
    "newProject": true,
    "autoNavigate": true,
  }
}
```

带自定义参数：
```json
{
  "type": "smart",
  "data": {
    "imagePaths": [
      "C:\\Users\\<USER>\\image1.jpg",
      "C:\\Users\\<USER>\\image2.jpg"
    ],
    "projectName": "智能导入项目",
    "newProject": true,
    "autoNavigate": true,
    "customParams": {
      "xxxCustomKey1": "ORD-2025-001",
      "xxxCustomKey2": "CUS-12345",
      "xxxCount": 3,
      "enableXXX": true,
      "xxxxxMetaData": {
        "xxxxx": "ERP系统",
        "xxxxx": "1.0"
      }
    }
  }
}
```

2. 命令行示例：
```bash
# 基本导入
turing_art.exe --smart --images=C:\Users\<USER>\image1.jpg|C:\Users\<USER>\image2.jpg

# 完整参数
turing_art.exe --smart --images=C:\Users\<USER>\image1.jpg --name=我的项目 --newProject --auto-navigate

# 包含自定义参数
turing_art.exe --smart --images=C:\Users\<USER>\image1.jpg --name=我的项目 --custom-params='{"orderId": "ORD-2025-001", "customerId": "CUS-12345", "workflow": "standard", "priority": "high", "retryCount": 3, "enableNotification": true}'
```

## 导入功能响应说明

### 功能说明

图灵精修提供的智能导入功能，主要行为逻辑如下：

1. **自动识别TAPJ项目**：在指定的图像文件所在目录中查找TAPJ文件
2. **智能导入模式**：
   - 如果找到TAPJ文件，按现有项目导入模式等待用户选择导入动作
   - 如果没有找到TAPJ文件，自动创建新项目
3. **传入图像文件处理**：传入图像文件，可作为工程输入图像文件创建新工程；对于已有项目，传入图像文件也可作为本次选片/选版结果标记为选中状态

### 使用场景

- **选版软件集成**：第三方选版软件可以通过此功能将选中的图像快速导入图灵精修
- **ERP软件集成**： ERP软件根据订单信息自动在图灵精修中创建工程项目并将修图后结果导出回ERP软件中
- **工作流自动化**：在后期处理流程中自动导入特定图像
- **批量处理**：一次性导入多个图像文件进行处理

### 智能导入逻辑

1. **检查目录**：扫描指定图像文件所在的目录
2. **查找TAPJ**：在目录中查找是否存在 `.tapj` 文件
3. **导入模式选择**：
   - 找到TAPJ → 按现有项目导入，根据用户选择将指定图像标记为选中或重新创建工程
   - 未找到TAPJ → 直接创建新项目，导入所有指定图像

## 集成工程代码实现示例

### Windows C++ 实现

#### 1. 头文件包含和库链接

```cpp
#include <windows.h>
#include <string>
#include <shlwapi.h>
#include <winreg.h>
#include <iostream>

#pragma comment(lib, "shlwapi.lib")
```

#### 2. 窗口查找和消息发送

```cpp
// 查找窗口
bool FindWindowByTitle(const std::wstring& title, HWND& out_hwnd) {
    out_hwnd = FindWindowW(NULL, title.c_str());
    return out_hwnd != NULL;
}

// 发送消息到窗口
bool SendMessageToWindow(HWND hwnd, const std::string& message) {
    if (!hwnd) return false;

    // 准备 COPYDATASTRUCT
    COPYDATASTRUCT cds;
    cds.dwData = 1;  // 自定义标识符
    cds.cbData = static_cast<DWORD>(message.length() + 1);  // 包含空终止符
    cds.lpData = (void*)message.c_str();

    // 发送 WM_COPYDATA 消息
    LRESULT result = SendMessageW(hwnd, WM_COPYDATA, 0, (LPARAM)&cds);
    return result == 1;
}
```

#### 4. 检查安装并获取执行路径

```cpp
// appName传递 '图灵精修'
bool IsApplicationInstalled(const std::wstring& appName, std::wstring& installPath) {
    // First check common installation paths
    if (CheckCommonInstallPaths(appName, installPath)) {
        return true;
    }

    // If not found in common paths, check registry
    // registry paths
    const wchar_t* registryPaths[] = {
        L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths",
        L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
        L"SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
    };

    for (const auto& basePath : registryPaths) {
        HKEY hKey;
        if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, basePath, 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            wchar_t subKeyName[256];
            DWORD index = 0;
            DWORD nameSize = sizeof(subKeyName) / sizeof(wchar_t);

            while (RegEnumKeyExW(hKey, index, subKeyName, &nameSize, NULL, NULL, NULL, NULL) == ERROR_SUCCESS) {
                HKEY hSubKey;
                std::wstring fullPath = std::wstring(basePath) + L"\\" + subKeyName;
                
                if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
                    wchar_t displayName[256];
                    DWORD dataSize = sizeof(displayName);
                    DWORD type = REG_SZ;

                    if (RegQueryValueExW(hSubKey, L"DisplayName", NULL, &type, (LPBYTE)displayName, &dataSize) == ERROR_SUCCESS) {
                        // convert both strings to lowercase for comparison
                        std::wstring displayNameLower = displayName;
                        std::wstring appNameLower = appName;
                        std::transform(displayNameLower.begin(), displayNameLower.end(), displayNameLower.begin(), ::towlower);
                        std::transform(appNameLower.begin(), appNameLower.end(), appNameLower.begin(), ::towlower);
                        
                        // use contains instead of equality
                        if (displayNameLower.find(appNameLower) != std::wstring::npos) {
                            // find the application and get the install path
                            wchar_t path[256];
                            dataSize = sizeof(path);
                            if (RegQueryValueExW(hSubKey, L"InstallLocation", NULL, &type, (LPBYTE)path, &dataSize) == ERROR_SUCCESS) {
                                installPath = path;
                                RegCloseKey(hSubKey);
                                RegCloseKey(hKey);
                                return true;
                            }
                        }
                    }
                    RegCloseKey(hSubKey);
                }
                nameSize = sizeof(subKeyName) / sizeof(wchar_t);
                index++;
            }
            RegCloseKey(hKey);
        }
    }
    return false;
}
```

#### 3. 完整的调用示例

```cpp
int main() {
    // 1. 检查图灵精修是否正在运行
    HWND target_hwnd;
    if (FindWindowByTitle(L"图灵精修", target_hwnd)) {
        // 应用正在运行，发送消息
        std::string message = R"({
             "type": "smart",
             "data": {
                 "imagePaths": [
                     "C:/Users/<USER>/Downloads/images/image1.jpg",
                     "C:/Users/<USER>/Downloads/images/image2.jpg"
                 ],
                 "projectName": "智能导入项目",
                 "newProject": true,
                 "customParams": {
                     "xxxKey": "ORD-2025-001",
                     "xxxKey1": "CUS-12345",
                     "xxxCount": 3,
                     "xxxEnable": true,
                     "metadata": {
                         "key": "value",
                         "key1": "value1"
                     }
                 }
             }
         })";
        
        if (SendMessageToWindow(target_hwnd, message)) {
            std::cout << "消息发送成功" << std::endl;
        } else {
            std::cout << "消息发送失败" << std::endl;
        }
    } else {
        // 应用未运行，尝试启动
        std::wstring installPath;
        if (IsApplicationInstalled(L"图灵精修", installPath)) {
            std::wstring exePath = installPath + L"\\turing_art.exe";
            // 构建自定义参数JSON字符串
            std::wstring customParams = L"{\\\"orderId\\\": \\\"ORD-2025-001\\\", "
                                      L"\\\"customerId\\\": \\\"CUS-12345\\\", "
                                      L"\\\"source\\\": \\\"C++调用示例\\\", "
                                      L"\\\"retryCount\\\": 3, "
                                      L"\\\"enableNotification\\\": true, "
                                      L"\\\"metadata\\\": {"
                                      L"\\\"platform\\\": \\\"Windows\\\", "
                                      L"\\\"version\\\": \\\"1.0.0\\\"}}";

            std::wstring arguments = L"--smart "
                                   L"--images=\"C:\\Users\\<USER>\\Downloads\\images\\image1.jpg|C:\\Users\\<USER>\\Downloads\\images\\image2.jpg\" "
                                   L"--newProject "
                                   L"--custom-params='" + customParams + L"'";
            
            if (LaunchApplication(exePath, arguments)) {
                std::cout << "应用启动成功" << std::endl;
            } else {
                std::cout << "应用启动失败" << std::endl;
            }
        } else {
            std::cout << "未找到图灵精修安装" << std::endl;
        }
    }
    
    return 0;
}
```


## 错误处理和调试

### 常见错误及解决方案

1. **窗口未找到**
   - 确认图灵精修应用正在运行
   - 检查窗口标题是否为 "图灵精修"
   - 确认应用版本是否支持窗口消息

2. **消息发送失败**
   - 确认JSON格式正确
   - 检查文件路径是否存在
   - 确认路径转义格式正确

3. **应用启动失败**
   - 检查安装路径是否正确
   - 确认可执行文件存在
   - 检查命令行参数格式

**技术支持：** 如需更多技术支持，请联系图灵精修技术团队。


## 版本更新历史

### v0.4 (2025.08.04)
- **重要更新**：
  - 新增 `customParams` 自定义参数字段，支持合作方传递任意JSON格式数据
- **文档优化**：
  - 添加基本调用和完整参数的对比示例
  - 完善自定义参数的使用说明和示例
  - 更新C++示例代码，展示自定义参数的使用方法

### v0.3 (2025.08.02)
- **重要更新**：
  - 移除其他导入形式，仅保留 `smart` 智能导入功能
- **功能简化**：
  - 统一使用智能导入方式，简化接口调用
- **文档优化**：
  - 更新所有示例代码和说明文档
- **架构优化**：
  - 选版信息直接合入工程文件，不再单独生成选版文件
  - 简化文件结构，提高数据一致性

### v0.2 (2025.07.05)
- **重要更新**：新增 `smart` 智能指令及相关功能
- **功能增强**：完善智能导入功能说明和使用指南
- **破坏性修改**：描述文件xxx_selected_files.json重命名为xxx_description.json

### v0.1 (2025.06.27)
- **初始版本**：完成基础的合作方调用接口文档
- **核心功能**：支持项目导入等基本功能
- **通信方式**：支持窗口消息和命令行参数两种通信方式
