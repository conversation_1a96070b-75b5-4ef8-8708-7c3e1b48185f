# Delete键拦截测试指南

## 问题描述

在Flutter与Unity混合应用中，当显示Flutter对话框时，Unity仍然能够响应Delete键等键盘快捷键，这导致了用户体验问题。

## 修复方案

我们实施了多层拦截机制来彻底解决这个问题：

### 1. 主窗口消息处理层拦截
在 `FlutterWindow::MessageHandler` 中：
- 使用 `ShouldUnityReceiveInput()` 智能检测
- 双重检查确保对话框状态
- 阻止事件传播到Unity

### 2. Unity窗口子类化层拦截
在 `UnitySubclassProc` 中：
- 直接在Unity窗口消息处理中拦截
- 检测到对话框活动时直接返回0
- 这是最后一道防线

### 3. 智能对话框检测
`IsFlutterDialogActive()` 方法：
- 检查 `preventAutoFocusToUnity` 标志
- 分析当前焦点窗口
- 识别Flutter子窗口
- 详细的调试日志

## 测试步骤

### 准备工作
1. 确保Unity窗口可见且有焦点
2. 在Unity中测试Delete键正常工作
3. 打开焦点调试页面

### 测试流程

#### 步骤1: 显示测试对话框
```dart
// 在调试页面中点击"场景1: Flutter对话框"
await showFocusTestDialog(context);
```

#### 步骤2: 验证拦截效果
1. **在Unity窗口中按Delete键**
   - ❌ 修复前：Unity会响应Delete键
   - ✅ 修复后：Unity不应该有任何反应

2. **在Flutter对话框中按Delete键**
   - ✅ Flutter对话框应该正常响应
   - ✅ Delete键计数器应该增加

3. **检查日志输出**
   - 应该看到 "Unity窗口子类化拦截键盘事件" 日志
   - 应该看到 "对话框活动，阻止Unity处理" 消息

#### 步骤3: 验证恢复
1. 关闭测试对话框
2. 在Unity窗口中再次按Delete键
3. Unity应该恢复正常响应

### 关键验证点

#### 1. 日志检查
查看以下关键日志：
```
IsFlutterDialogActive检测: ... -> 返回true (preventAutoFocusToUnity=true)
Unity不应该接收输入事件，让Flutter处理
Unity窗口子类化拦截键盘事件: ... - 对话框活动，阻止Unity处理
```

#### 2. 焦点状态检查
在测试对话框中查看：
- `preventAutoFocusToUnity`: true
- `isFlutterDialogActive`: true  
- `shouldUnityReceiveInput`: false
- `Delete键按下次数`: 应该增加（表示Flutter收到了事件）

#### 3. Unity行为检查
- Unity窗口中的对象不应该被Delete键删除
- Unity的其他快捷键也应该被拦截
- Unity的鼠标交互也应该被拦截

## 故障排除

### 问题1: Unity仍然响应Delete键
**可能原因**:
- `preventAutoFocusToUnity` 没有正确设置
- 对话框检测逻辑有问题
- Unity窗口子类化没有生效

**排查步骤**:
1. 检查日志中的 `preventAutoFocusToUnity` 值
2. 查看 `IsFlutterDialogActive` 的返回值
3. 确认Unity窗口子类化是否成功

### 问题2: Flutter对话框不响应Delete键
**可能原因**:
- 对话框焦点设置有问题
- 键盘事件处理器没有正确设置

**排查步骤**:
1. 检查对话框的 `autofocus` 设置
2. 确认 `_handleKeyEvent` 方法被调用
3. 查看Delete键计数器是否增加

### 问题3: 关闭对话框后Unity不恢复
**可能原因**:
- `preventAutoFocusToUnity` 没有正确重置
- 焦点没有正确转移回Unity

**排查步骤**:
1. 检查对话框关闭时的清理逻辑
2. 确认 `setFocusToFlutterWindowWhenUnityVisible(false)` 被调用
3. 查看焦点状态是否正确恢复

## 技术细节

### 拦截机制层次
1. **应用层**: Flutter对话框显示时设置标志
2. **消息处理层**: 主窗口消息处理中拦截
3. **窗口子类化层**: Unity窗口直接拦截
4. **事件传播层**: 阻止事件继续传播

### 关键代码位置
- `FlutterWindow::MessageHandler`: 主要拦截逻辑
- `UnitySubclassProc`: Unity窗口子类化拦截
- `IsFlutterDialogActive`: 对话框检测逻辑
- `ShouldUnityReceiveInput`: 综合判断逻辑

### 调试工具
- `FocusTestDialog`: 专用测试对话框
- `FocusDebugWidget`: 实时焦点状态监控
- 详细的日志输出系统

## 预期结果

修复成功后，应该实现：
1. ✅ 对话框显示时Unity完全停止响应键盘事件
2. ✅ Flutter对话框正常响应所有键盘事件
3. ✅ 对话框关闭后Unity立即恢复响应
4. ✅ 详细的调试日志帮助问题排查
5. ✅ 多层拦截机制确保可靠性

这个修复确保了在Flutter与Unity混合应用中，键盘事件能够正确地路由到当前活动的窗口，避免了双重响应和用户体验问题。
