# 焦点调试工具使用指南

## 概述

焦点调试工具是为了解决Flutter与Unity混合应用中的焦点管理问题而开发的调试工具。它提供了实时的焦点状态监控、焦点控制和问题诊断功能。

## 发现的焦点管理问题

### 1. 焦点竞争条件
**问题描述**: `WM_ACTIVATE` 和 `WM_SETFOCUS` 消息处理中都有焦点设置逻辑，可能导致竞争条件。

**解决方案**: 
- 将 `WM_SETFOCUS` 的焦点处理改为延迟处理
- 使用不同的延迟时间避免冲突
- 添加 `WM_APP + 3` 消息处理延迟的 `WM_SETFOCUS` 焦点设置

### 2. 父窗口焦点冲突
**问题描述**: `win32_window.cpp` 中的父窗口在 `WM_ACTIVATE` 时会设置焦点到子窗口，与Unity焦点管理冲突。

**解决方案**: 
- 移除父窗口中的自动焦点设置
- 让 `FlutterWindow` 统一处理焦点逻辑

### 3. 焦点状态不一致
**问题描述**: 缺乏统一的焦点状态管理和监控机制。

**解决方案**: 
- 添加焦点调试通道提供实时焦点信息
- 实现焦点状态监控和历史记录

## 工具组件

### 1. FocusDebugHelper (Dart)
提供焦点状态查询和控制的API接口。

```dart
// 获取当前焦点信息
final focusInfo = await FocusDebugHelper.getCurrentFocusInfo();

// 开始焦点监控
await FocusDebugHelper.startFocusMonitoring(
  interval: Duration(seconds: 1),
  onFocusChanged: (focusInfo) {
    print('焦点变化: ${focusInfo.focusStatus}');
  },
);

// 强制设置焦点
await FocusDebugHelper.forceFocusToFlutter();
await FocusDebugHelper.forceFocusToUnity();

// 检查焦点状态
final isUnityFocused = await FocusDebugHelper.isUnityFocused();
final isFlutterFocused = await FocusDebugHelper.isFlutterFocused();
```

### 2. FocusDebugWidget (Flutter组件)
提供可视化的焦点状态监控界面。

特性:
- 实时显示当前焦点状态
- 焦点历史记录
- 焦点控制按钮
- 自动监控开关

### 3. FocusDebugPage (完整调试页面)
提供完整的焦点调试和测试环境。

包含:
- Unity控制面板
- Flutter输入测试
- 焦点测试场景
- 快速操作按钮

### 4. C++ 焦点调试通道
在Windows端提供底层焦点信息获取和控制。

方法:
- `getCurrentFocusInfo`: 获取详细焦点信息
- `forceFocusToFlutter`: 强制设置焦点到Flutter
- `forceFocusToUnity`: 强制设置焦点到Unity
- `startFocusMonitoring`: 开始焦点监控
- `stopFocusMonitoring`: 停止焦点监控

## 使用方法

### 1. 基本使用

```dart
import 'package:turingart/widgets/unity/debug/focus_debug_page.dart';

// 在开发模式下添加调试页面
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const FocusDebugPage()),
);
```

### 2. 集成到现有页面

```dart
import 'package:turingart/widgets/unity/debug/focus_debug_widget.dart';

// 在现有页面中添加焦点调试组件
Column(
  children: [
    // 其他组件...
    if (kDebugMode) 
      const Expanded(child: FocusDebugWidget()),
  ],
)
```

### 3. 程序化使用

```dart
import 'package:turingart/widgets/unity/helper/focus_debug_helper.dart';

// 在需要的地方检查焦点状态
void checkFocusStatus() async {
  final focusInfo = await FocusDebugHelper.getCurrentFocusInfo();
  if (focusInfo != null) {
    print('当前焦点状态: ${focusInfo.focusStatus}');
    print('Unity有焦点: ${focusInfo.isUnityFocused}');
    print('Flutter有焦点: ${focusInfo.isFlutterFocused}');
  }
}

// 在对话框显示前后控制焦点
void showDialog() async {
  // 显示对话框前阻止自动焦点转移
  await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
    flutterWindowFocus: true,
  );
  
  // 显示对话框...
  
  // 对话框关闭后恢复焦点控制
  await WindowsUnityHelper.setFocusToFlutterWindowWhenUnityVisible(
    flutterWindowFocus: false,
  );
}
```

## 调试场景

### 1. Flutter对话框场景
测试在显示Flutter对话框时是否正确阻止了自动焦点转移到Unity。

### 2. Unity输入场景
测试Unity窗口是否能正确接收键盘和鼠标输入。

### 3. 窗口切换场景
测试在窗口激活/失活时焦点是否正确转移。

## 焦点信息说明

### FocusInfo 字段说明

- `focusStatus`: 焦点状态 ("flutter", "unity", "none", "other")
- `isFlutterFocused`: Flutter窗口是否有焦点
- `isUnityFocused`: Unity窗口是否有焦点
- `unityVisible`: Unity窗口是否可见
- `preventAutoFocusToUnity`: 是否阻止自动焦点转移到Unity
- `focusedWindowTitle`: 当前焦点窗口标题
- `focusedWindowClass`: 当前焦点窗口类名
- `foregroundWindowTitle`: 前台窗口标题
- `foregroundWindowClass`: 前台窗口类名

## 常见问题排查

### 1. 焦点丢失
**症状**: 键盘输入无响应
**排查**: 检查 `focusStatus` 是否为 "none"，查看焦点历史记录

### 2. 焦点错误转移
**症状**: 焦点转移到了错误的窗口
**排查**: 检查 `preventAutoFocusToUnity` 状态，查看焦点变化历史

### 3. Unity输入无响应
**症状**: Unity窗口无法接收输入
**排查**: 检查 `isUnityFocused` 和 `unityVisible` 状态

## 性能注意事项

1. 焦点监控会定期查询系统状态，建议仅在调试时使用
2. 监控间隔不要设置过短，推荐500ms-1s
3. 在生产环境中应该禁用焦点调试功能

## 扩展功能

可以根据需要扩展以下功能:
- 焦点状态持久化记录
- 焦点问题自动检测和修复
- 更详细的窗口层次结构信息
- 焦点性能分析
