PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - disk_space_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_unity_widget (4.0.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.2.4)
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.47.2):
    - sqlite3/common (= 3.47.2)
  - sqlite3/common (3.47.2)
  - sqlite3/dbstatvtab (3.47.2):
    - sqlite3/common
  - sqlite3/fts5 (3.47.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.47.2):
    - sqlite3/common
  - sqlite3/rtree (3.47.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.47.2)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - disk_space_plus (from `.symlinks/plugins/disk_space_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_unity_widget (from `.symlinks/plugins/flutter_unity_widget/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - DKImagePickerController
    - DKPhotoGallery
    - ReachabilitySwift
    - SDWebImage
    - SwiftyGif
  trunk:
    - sqlite3

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  disk_space_plus:
    :path: ".symlinks/plugins/disk_space_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_unity_widget:
    :path: ".symlinks/plugins/flutter_unity_widget/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  disk_space_plus: faa27633429bb392ecf7342395bf12040ca4ac9b
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 15fd9539e4eb735dc54bae8c0534a7a9511a03de
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_unity_widget: e509f3109933aa0c63b1bc149ca3d5b90fb68543
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  photo_manager: ff695c7a1dd5bc379974953a2b5c0a293f7c4c8a
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqlite3: 7559e33dae4c78538df563795af3a86fc887ee71
  sqlite3_flutter_libs: 58ae36c0dd086395d066b4fe4de9cdca83e717b3
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  webview_flutter_wkwebview: 2a23822e9039b7b1bc52e5add778e5d89ad488d1

PODFILE CHECKSUM: 61400bbea989be277411a818d1b87d2d50111154

COCOAPODS: 1.16.2
