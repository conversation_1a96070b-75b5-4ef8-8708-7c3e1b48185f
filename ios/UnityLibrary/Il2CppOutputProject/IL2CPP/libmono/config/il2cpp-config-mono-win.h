#pragma once

#include <SDKDDKVer.h>
#ifndef WINVER
    #define WINVER 0x0A00
#endif
#if _WIN32_WINNT < 0x0600
    #error "Mono requires Windows Vista or later"
#endif
#pragma warning(disable:4100 4127)

#if defined(_M_IX86)
#define ARCHITECTURE_IS_X86
#elif defined(_M_X64)
#define ARCHITECTURE_IS_AMD64
#elif defined(_M_ARM)
#define ARCHITECTURE_IS_ARMv7
#define ARM_FPU_VFP_HARD
#elif defined(_M_ARM64)
#define ARCHITECTURE_IS_ARM64
#else
#error Unknown architecture when building with MSVC!
#endif

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN 1
#endif

#define HOST_WIN32 1
#define TARGET_WIN32 1

#define DISABLE_PORTABILITY 1
#define F_SETFD 1
#define FD_CLOEXEC 1
#define G_DIR_SEPARATOR_IS_BACKSLASH 1
#define G_OS_WIN32 1
#define GPID_IS_VOID_P 1
#define G_SEARCHPATH_SEPARATOR_IS_SEMICOLON 1
#define HAVE_CRYPT_RNG 1
#define HAVE_DECL_INTERLOCKEDADD 1
#define HAVE_DECL_INTERLOCKEDADD64 1
#define HAVE_DECL_INTERLOCKEDCOMPAREEXCHANGE64 1
#define HAVE_DECL_INTERLOCKEDDECREMENT64 1
#define HAVE_DECL_INTERLOCKEDEXCHANGE64 1
#define HAVE_DECL_INTERLOCKEDINCREMENT64 1
#define HAVE_DECL___READFSDWORD 1
#define HAVE_GETPROCESSID 1
#define HAVE_GETADDRINFO 1
#define HAVE_GETNAMEINFO 1
#define HAVE_GETPROTOBYNAME 1
#define HAVE_GNUC_PRETTY_FUNCTION
#define HAVE_ISINF 1
#define HAVE_INET_PTON 1
#define HAVE_SIGNAL 1
#define HAVE_STRUCT_IP_MREQ 1
#define HAVE_STRUCT_SOCKADDR_IN6 1
#define HAVE_STRTOK_R 1
#define HAVE_SYSTEM 1
#define HAVE_TRUNC 1
#define MAXPATHLEN 242
#define MONO_ZERO_LEN_ARRAY 1
#define PLATFORM_IS_LITTLE_ENDIAN 1
#define HOST_NO_SYMLINKS 1
#define PID_T_IS_INT 1
#define strtok_r strtok_s
#define STDOUT_STDERR_REQUIRES_CAST 1
#define WNOHANG 1
#define X_OK 4

#define HAVE_COMPLEX_H 1
#define HAVE_DLFCN_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_MEMORY_H 1
#define HAVE_SIGNAL_H 1
#define HAVE_STDINT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRINGS_H 1
#define HAVE_STRING_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_SYS_UTIME_H 1
#define HAVE_WCHAR_H 1
#define STDC_HEADERS 1

#if defined(_XBOX_ONE)
    #define HAVE_CLASSIC_WINAPI_SUPPORT 0
    #define HAVE_UWP_WINAPI_SUPPORT 0
    #define HAVE_XBOXONE_WINAPI_SUPPORT 1
    #define HAVE_WINDOWS_GAMES_WINAPI_SUPPORT 0
#elif defined(WINAPI_FAMILY) && (WINAPI_FAMILY == WINAPI_FAMILY_APP)
    #define HAVE_CLASSIC_WINAPI_SUPPORT 0
    #define HAVE_UWP_WINAPI_SUPPORT 1
    #define HAVE_XBOXONE_WINAPI_SUPPORT 0
    #define HAVE_WINDOWS_GAMES_WINAPI_SUPPORT 0
#elif defined(WINAPI_FAMILY) && (WINAPI_FAMILY == WINAPI_FAMILY_GAMES)
    #define HAVE_CLASSIC_WINAPI_SUPPORT 0
    #define HAVE_UWP_WINAPI_SUPPORT 0
    #define HAVE_XBOXONE_WINAPI_SUPPORT 0
    #define HAVE_WINDOWS_GAMES_WINAPI_SUPPORT 1
#else
    #define HAVE_CLASSIC_WINAPI_SUPPORT 1
    #define HAVE_UWP_WINAPI_SUPPORT 0
    #define HAVE_XBOXONE_WINAPI_SUPPORT 0
    #define HAVE_WINDOWS_GAMES_WINAPI_SUPPORT 0
#endif

#define ABORT_ON_ASSERT_FAILURE 0
