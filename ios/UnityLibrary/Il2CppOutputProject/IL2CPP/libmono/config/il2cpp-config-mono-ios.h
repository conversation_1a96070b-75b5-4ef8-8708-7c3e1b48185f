#pragma once

#define _XOPEN_SOURCE 1
#define _DARWIN_C_SOURCE 1

#define HOST_DARWIN 1
#define HOST_IOS 1

#if defined(__i386__)
#define ARCHITECTURE_IS_X86 1
#elif defined(__x86_64__)
#define ARCHITECTURE_IS_AMD64 1
#elif defined(__arm__)
#define ARCHITECTURE_IS_ARMv7 1
#elif defined(__arm64__)
#define ARCHITECTURE_IS_ARM64 1
#else
#error Unknown architecture when building!
#endif

//Configurable defines for this platform
#define G_DIR_SEPARATOR_IS_FORWARDSLASH 1
#define G_SEARCHPATH_SEPARATOR_IS_COLON 1
#define GLIBC_BEFORE_2_3_4_SCHED_SETAFFINITY 1
#define GLIBC_HAS_CPU_COUNT 1
#define GPID_IS_INT 1
#define HAVE_DEV_RANDOM 1
#define HAVE_GNUC_UNUSED 1
#define HAVE_GNUC_NORETURN 1
#define HAVE_ARRAY_ELEM_INIT 1
#define HAVE_BACKTRACE_SYMBOLS 1
#define HAVE_BLKCNT_T 1
#define HAVE_BLKSIZE_T 1
#define HAVE_CLASSIC_WINAPI_SUPPORT 1
#define HAVE_CONC_GC_AS_DEFAULT 1
#define HAVE_CONFSTR 1
#define HAVE_CRYPT_RNG 1
#define HAVE_DLADDR 1
#define HAVE_DL_LOADER 1
#define HAVE_ENDGRENT 1
#define HAVE_ENDPWENT 1
#define HAVE_ENDUSERSHELL 1
//#define HAVE_EXECV 1
//#define HAVE_EXECVE 1
//#define HAVE_EXECVP 1
#define HAVE_FINITE 1
//#define HAVE_FORK 1
#define HAVE_FSTATAT 1
#define HAVE_FSTATFS 1
#define HAVE_FSTATVFS 1
#define HAVE_FUTIMES 1
#define HAVE_GETADDRINFO 1
#define HAVE_GETDOMAINNAME 1
#define HAVE_GETFSSTAT 1
#define HAVE_GETGRENT 1
#define HAVE_GETGRGID_R 1
#define HAVE_GETGRNAM_R 1
#define HAVE_GETHOSTBYNAME 1
#define HAVE_GETHOSTBYNAME2 1
#define HAVE_GETHOSTID 1
#define HAVE_GETIFADDRS 1
#define HAVE_GETLOGIN_R 1
#define HAVE_GETNAMEINFO 1
#define HAVE_GETPRIORITY 1
#define HAVE_GETPROTOBYNAME 1
#define HAVE_GETPWENT 1
#define HAVE_GETPWNAM_R 1
#define HAVE_GETPWUID_R 1
#define HAVE_GETRLIMIT 1
#define HAVE_GETRUSAGE 1
#define HAVE_ICONV 1
#define HAVE_IF_NAMETOINDEX 1
#define HAVE_INET_ATON 1
#define HAVE_INET_NTOP 1
#define HAVE_INET_PTON 1
#define HAVE_IPPROTO_IP 1
#define HAVE_IPPROTO_IPV6 1
#define HAVE_IPPROTO_TCP 1
#define HAVE_ISFINITE 1
#define HAVE_ISINF 1
#define HAVE_KILL 1
#define HAVE_KQUEUE 1
#define HAVE_LARGE_FILE_SUPPORT 1
#define HAVE_LOCKF 1
#define HAVE_LUTIMES 1
#define HAVE_MADVISE 1
#define HAVE_MINCORE 1
#define HAVE_MKSTEMP 1
#define HAVE_MMAP 1
#define HAVE_MOVING_COLLECTOR 1
#define HAVE_POLL 1
#define HAVE_POSIX_MADVISE 1
#define HAVE_PSIGNAL 1
#define HAVE_PTHREAD_ATTR_GETSTACK 1
#define HAVE_PTHREAD_ATTR_GETSTACKSIZE 1
#define HAVE_PTHREAD_ATTR_SETSTACKSIZE 1
#define HAVE_PTHREAD_GET_STACKADDR_NP 1
#define HAVE_PTHREAD_GET_STACKSIZE_NP 1
#define HAVE_PTHREAD_KILL 1
#define HAVE_PTHREAD_SETNAME_NP 1
#define HAVE_READLINKAT 1
#define HAVE_READV 1
#define HAVE_SEEKDIR 1
#define HAVE_SENDFILE 1
#define HAVE_SETDOMAINNAME 1
#define HAVE_SETGRENT 1
#define HAVE_SETGROUPS 1
#define HAVE_SETHOSTID 1
#define HAVE_SETHOSTNAME 1
#define HAVE_SETPGID 1
#define HAVE_SETPRIORITY 1
#define HAVE_SETPWENT 1
#define HAVE_SETUSERSHELL 1
#define HAVE_SHM_OPEN 1
#define HAVE_SIGACTION 1
#define HAVE_SIGNAL 1
#define HAVE_SIOCGIFCONF 1
#define HAVE_SOCKADDR_IN6_SIN_LEN 1
#define HAVE_SOCKADDR_IN_SIN_LEN 1
#define HAVE_SOCKLEN_T 1
#define HAVE_STATFS 1
#define HAVE_STATVFS 1
#define HAVE_STRERROR_R 1
#define HAVE_STRUCT_CMSGHDR 1
#define HAVE_STRUCT_DIRENT_D_RECLEN 1
#define HAVE_STRUCT_DIRENT_D_TYPE 1
#define HAVE_STRUCT_FLOCK 1
#define HAVE_STRUCT_IOVEC 1
#define HAVE_STRUCT_IP_MREQN 1
#define HAVE_STRUCT_KINFO_PROC_KP_PROC 1
#define HAVE_STRUCT_LINGER 1
#define HAVE_STRUCT_PASSWD_PW_GECOS 1
#define HAVE_STRUCT_POLLFD 1
#define HAVE_STRUCT_SOCKADDR 1
#define HAVE_STRUCT_SOCKADDR_IN 1
#define HAVE_STRUCT_SOCKADDR_IN6 1
#define HAVE_STRUCT_SOCKADDR_STORAGE 1
#define HAVE_STRUCT_SOCKADDR_UN 1
#define HAVE_STRUCT_STAT 1
#define HAVE_STRUCT_TIMESPEC 1
#define HAVE_STRUCT_TIMEVAL 1
#define HAVE_STRUCT_TIMEZONE 1
#define HAVE_STRUCT_UTIMBUF 1
#define HAVE_STRTOK_R 1
#define HAVE_SUSECONDS_T 1
#define HAVE_SWAB 1
#define HAVE_SYSCONF 1
#define HAVE_SYS_ZLIB 1
#define HAVE_SYSTEM 1
#define HAVE_TELLDIR 1
#define HAVE_TM_GMTOFF 1
#define HAVE_TRUNC 1
#define HAVE_TTYNAME_R 1
#define HAVE_UWP_WINAPI_SUPPORT 0
#define HAVE_VISIBILITY_HIDDEN 1
#define HAVE_VSNPRINTF 1
#define HAVE_WORKING_SIGALTSTACK 1
#define HAVE_WRITEV 1
#define HAVE_ZLIB 1
#define ICONV_CONST
#define LT_OBJDIR ".libs/"
#ifndef MAXPATHLEN
#define MAXPATHLEN 242
#endif
#define USE_MONO_INSIDE_RUNTIME 1
#define MONO_ZERO_LEN_ARRAY 0
#define NEED_LINK_UNLINK 1
#define PTHREAD_POINTER_ID 1
#define PLATFORM_IS_LITTLE_ENDIAN 1
#define HAVE_LIBICONV 1
#define HAVE_REWINDDIR 1
#define HAVE_STPCPY 1
#define HAVE_STRLCPY 1
#define HAVE_VASPRINTF 1
#define USE_GCC_ATOMIC_OPS 1
#define USE_MACH_SEMA 1

//Headers for this platform
#define G_HAVE_ALLOCA_H 1
#define HAVE_ALLOCA_H 1
#define HAVE_ARPA_INET_H 1
#define HAVE_COMMONCRYPTO_COMMONDIGEST_H 1
#define HAVE_COMPLEX_H 1
#define HAVE_CURSES_H 1
#define HAVE_DIRENT_H 1
#define HAVE_DLFCN_H 1
#define HAVE_EXECINFO_H 1
#define HAVE_FSTAB_H 1
#define HAVE_GETOPT_H 1
#define HAVE_GRP_H 1
#define HAVE_ICONV_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_LIBPROC_H 1
//#define HAVE_LOCALCHARSET_H 1
#define HAVE_MEMORY_H 1
#define HAVE_NETDB_H 1
#define HAVE_NETINET_IN_H 1
#define HAVE_NETINET_TCP_H 1
#define HAVE_NET_IF_H 1
#define HAVE_POLL_H 1
#define HAVE_PTHREAD_H 1
#define HAVE_PWD_H 1
#define HAVE_SEMAPHORE_H 1
#define HAVE_SIGNAL_H 1
#define HAVE_STDINT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRINGS_H 1
#define HAVE_STRING_H 1
#define HAVE_SYSLOG_H 1
#define HAVE_SYS_EVENT_H 1
#define HAVE_SYS_FILIO_H 1
#define HAVE_SYS_IOCTL_H 1
#define HAVE_SYS_IPC_H 1
#define HAVE_SYS_MMAN_H 1
#define HAVE_SYS_MOUNT_H 1
#define HAVE_SYS_PARAM_H 1
#define HAVE_SYS_POLL_H 1
#define HAVE_SYS_RESOURCE_H 1
#define HAVE_SYS_SDT_H 1
#define HAVE_SYS_SELECT_H 1
#define HAVE_SYS_SOCKET_H 1
#define HAVE_SYS_SOCKIO_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_STATVFS_H 1
#define HAVE_SYS_SYSCALL_H 1
#define HAVE_SYS_SYSCTL_H 1
#define HAVE_SYS_TIME_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_SYS_UIO_H 1
#define HAVE_SYS_UN_H 1
//#define HAVE_SYS_USER_H 1
#define HAVE_SYS_UTIME_H 1
//#define HAVE_SYS_UTSNAME_H 1
#define HAVE_SYS_WAIT_H 1
#define HAVE_SYS_XATTR_H 1
#define HAVE_TERMIOS_H 1
#define HAVE_TERM_H 1
#define HAVE_UCONTEXT_H 1
#define HAVE_UNISTD_H 1
#define HAVE_UNWIND_H 1
#define HAVE_UTIME_H 1
#define HAVE_WCHAR_H 1
#define STDC_HEADERS 1
