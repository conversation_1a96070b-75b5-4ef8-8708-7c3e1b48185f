Our intent is to make it easy to use libatomic_ops, in
both free and proprietary software.  Hence most code that we expect to be
linked into a client application is covered by an MIT-style license.

A few library routines are covered by the GNU General Public License.
These are put into a separate library, libatomic_ops_gpl.a .

The low-level part of the library is mostly covered by the following
license:

----------------------------------------

Copyright (c) ...

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

--------------------------------

A few files in the sysdeps directory were inherited in part from the
Boehm-Demers-Weiser conservative garbage collector, and are covered by
its license, which is similar in spirit:

--------------------------------

Copyright (c) ...

THIS MATERIAL IS PROVIDED AS IS, WITH ABSOLUTELY NO WARRANTY EXPRESSED
OR IMPLIED.  ANY USE IS AT YOUR OWN RISK.

Permission is hereby granted to use or copy this program
for any purpose,  provided the above notices are retained on all copies.
Permission to modify the code and to distribute modified code is granted,
provided the above notices are retained, and a notice that the code was
modified is included with the above copyright notice.

----------------------------------

A few files are covered by the GNU General Public License.  (See file
"COPYING".) This applies only to test code, sample applications,
and the libatomic_ops_gpl portion of the library.
Thus libatomic_ops_gpl should generally not be linked into proprietary code.
(This distinction was motivated by patent considerations.)

It is possible that the license of the GPL pieces may be changed for
future versions to make them more consistent with the rest of the package.
If you submit patches, and have strong preferences about licensing, please
express them.
