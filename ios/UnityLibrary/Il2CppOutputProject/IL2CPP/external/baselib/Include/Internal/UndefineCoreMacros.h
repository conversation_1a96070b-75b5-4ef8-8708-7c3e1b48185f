// DO NOT PUT #pragma once or include guard check here
// This header is designed to be able to be included multiple times

// We ignore PP_ and DETAIL__PP_
// It should not generate a warning if the same redefined with exactly the same

#undef FORCE_INLINE
#undef OPTIMIZER_LIKELY
#undef OPTIMIZER_UNLIKELY
#undef UNUSED
#undef COMPILER_WARNING
#undef UNSIGNED_FLAGS_1
#undef UNSIGNED_FLAGS_2
#undef UNSIGNED_FLAGS_3
#undef UNSIGNED_FLAGS_4
#undef UNSIGNED_FLAGS_5
#undef UNSIGNED_FLAGS_6
#undef UNSIGNED_FLAGS
