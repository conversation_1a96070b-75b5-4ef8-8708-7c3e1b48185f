/**
 * \file
 * Define Win32 API subset defaults.
 * Other subsetters can fork this file, or
 * define symbols ahead of it, or after it (with undef).
 *
 * Note that #if of an undefined symbols is defined as if 0,
 * so that an implicit default here.
 *
 * Copyright 2019 Microsoft
 * Licensed under the MIT license. See LICENSE file in the project root for full license information.
 */

#ifndef HAVE_API_SUPPORT_WIN32_BSTR
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_BSTR 1
#else
#define HAVE_API_SUPPORT_WIN32_BSTR 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CANCEL_IO
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CANCEL_IO 1
#else
#define HAVE_API_SUPPORT_WIN32_CANCEL_IO 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CANCEL_IO_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CANCEL_IO_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_CANCEL_IO_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CANCEL_SYNCHRONOUS_IO
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CANCEL_SYNCHRONOUS_IO 1
#else
#define HAVE_API_SUPPORT_WIN32_CANCEL_SYNCHRONOUS_IO 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_COMMAND_LINE_TO_ARGV
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_COMMAND_LINE_TO_ARGV 1
#else
#define HAVE_API_SUPPORT_WIN32_COMMAND_LINE_TO_ARGV 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CONSOLE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CONSOLE 1
#else
#define HAVE_API_SUPPORT_WIN32_CONSOLE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_COPY_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_COPY_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_COPY_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_COPY_FILE2
#if G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_COPY_FILE2 1
#else
#define HAVE_API_SUPPORT_WIN32_COPY_FILE2 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_COREE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_COREE 1
#else
#define HAVE_API_SUPPORT_WIN32_COREE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CREATE_PROCESS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CREATE_PROCESS 1
#else
#define HAVE_API_SUPPORT_WIN32_CREATE_PROCESS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CREATE_PROCESS_WITH_LOGON
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CREATE_PROCESS_WITH_LOGON 1
#else
#define HAVE_API_SUPPORT_WIN32_CREATE_PROCESS_WITH_LOGON 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CREATE_SEMAPHORE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CREATE_SEMAPHORE 1
#else
#define HAVE_API_SUPPORT_WIN32_CREATE_SEMAPHORE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CREATE_SEMAPHORE_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_CREATE_SEMAPHORE_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_CREATE_SEMAPHORE_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_DISCONNECT_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_DISCONNECT_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_DISCONNECT_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_ENUM_PROCESSES
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_ENUM_PROCESSES 1
#else
#define HAVE_API_SUPPORT_WIN32_ENUM_PROCESSES 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_ENUM_PROCESS_MODULES
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_ENUM_PROCESS_MODULES 1
#else
#define HAVE_API_SUPPORT_WIN32_ENUM_PROCESS_MODULES 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_ENUM_WINDOWS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_ENUM_WINDOWS 1
#else
#define HAVE_API_SUPPORT_WIN32_ENUM_WINDOWS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_FILE_MAPPING
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_FILE_MAPPING 1
#else
#define HAVE_API_SUPPORT_WIN32_FILE_MAPPING 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_FILE_MAPPING_FROM_APP
#if G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_FILE_MAPPING_FROM_APP 1
#else
#define HAVE_API_SUPPORT_WIN32_FILE_MAPPING_FROM_APP 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_FORMAT_MESSAGE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_FORMAT_MESSAGE 1
#else
#define HAVE_API_SUPPORT_WIN32_FORMAT_MESSAGE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_ACP
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_ACP 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_ACP 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_COMPUTER_NAME
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_COMPUTER_NAME 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_COMPUTER_NAME 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_CP_INFO_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_CP_INFO_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_CP_INFO_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_DRIVE_TYPE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_DRIVE_TYPE 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_DRIVE_TYPE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_FILE_SIZE_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_FILE_SIZE_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_FILE_SIZE_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_FILE_VERSION_INFO
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_FILE_VERSION_INFO 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_FILE_VERSION_INFO 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_LOGICAL_DRIVE_STRINGS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_LOGICAL_DRIVE_STRINGS 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_LOGICAL_DRIVE_STRINGS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_MODULE_BASE_NAME
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_BASE_NAME 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_BASE_NAME 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_MODULE_FILE_NAME_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_FILE_NAME_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_FILE_NAME_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_HANDLE_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_MODULE_INFORMATION
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_INFORMATION 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_MODULE_INFORMATION 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_PRIORITY_CLASS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_PRIORITY_CLASS 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_PRIORITY_CLASS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_PROCESS_TIMES
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_PROCESS_TIMES 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_PROCESS_TIMES 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_STD_HANDLE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_STD_HANDLE 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_STD_HANDLE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_SYSTEM_TIME_AS_FILE_TIME
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_SYSTEM_TIME_AS_FILE_TIME 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_SYSTEM_TIME_AS_FILE_TIME 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_SYSTEM_TIMES
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_SYSTEM_TIMES 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_SYSTEM_TIMES 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GET_WORKING_SET_SIZE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GET_WORKING_SET_SIZE 1
#else
#define HAVE_API_SUPPORT_WIN32_GET_WORKING_SET_SIZE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_GLOBAL_ALLOC_FREE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_GLOBAL_ALLOC_FREE 1
#else
#define HAVE_API_SUPPORT_WIN32_GLOBAL_ALLOC_FREE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_IS_WOW64_PROCESS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_IS_WOW64_PROCESS 1
#else
#define HAVE_API_SUPPORT_WIN32_IS_WOW64_PROCESS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOAD_LIBRARY
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOAD_LIBRARY 1
#else
#define HAVE_API_SUPPORT_WIN32_LOAD_LIBRARY 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOAD_PACKAGED_LIBRARY
#if G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOAD_PACKAGED_LIBRARY 1
#else
#define HAVE_API_SUPPORT_WIN32_LOAD_PACKAGED_LIBRARY 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOCAL_ALLOC_FREE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOCAL_ALLOC_FREE 1
#else
#define HAVE_API_SUPPORT_WIN32_LOCAL_ALLOC_FREE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOCAL_INFO
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOCAL_INFO 1
#else
#define HAVE_API_SUPPORT_WIN32_LOCAL_INFO 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOCAL_INFO_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOCAL_INFO_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_LOCAL_INFO_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_LOCK_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_LOCK_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_LOCK_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_MOVE_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_MOVE_FILE_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_MOVE_FILE_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_MSG_WAIT_FOR_MULTIPLE_OBJECTS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_MSG_WAIT_FOR_MULTIPLE_OBJECTS 1
#else
#define HAVE_API_SUPPORT_WIN32_MSG_WAIT_FOR_MULTIPLE_OBJECTS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_OPEN_PROCESS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_OPEN_PROCESS 1
#else
#define HAVE_API_SUPPORT_WIN32_OPEN_PROCESS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_OPEN_THREAD
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_OPEN_THREAD 1
#else
#define HAVE_API_SUPPORT_WIN32_OPEN_THREAD 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_REPLACE_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_REPLACE_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_REPLACE_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_RESET_STKOFLW
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_RESET_STKOFLW 1
#else
#define HAVE_API_SUPPORT_WIN32_RESET_STKOFLW 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SAFE_ARRAY
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SAFE_ARRAY 1
#else
#define HAVE_API_SUPPORT_WIN32_SAFE_ARRAY 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SECURITY
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SECURITY 1
#else
#define HAVE_API_SUPPORT_WIN32_SECURITY 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SEND_MESSAGE_TIMEOUT
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SEND_MESSAGE_TIMEOUT 1
#else
#define HAVE_API_SUPPORT_WIN32_SEND_MESSAGE_TIMEOUT 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SET_ERROR_MODE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SET_ERROR_MODE 1
#else
#define HAVE_API_SUPPORT_WIN32_SET_ERROR_MODE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SET_PRIORITY_CLASS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SET_PRIORITY_CLASS 1
#else
#define HAVE_API_SUPPORT_WIN32_SET_PRIORITY_CLASS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SET_THREAD_CONTEXT
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SET_THREAD_CONTEXT 1
#else
#define HAVE_API_SUPPORT_WIN32_SET_THREAD_CONTEXT 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SET_THREAD_DESCRIPTION
#if G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SET_THREAD_DESCRIPTION 1
#else
#define HAVE_API_SUPPORT_WIN32_SET_THREAD_DESCRIPTION 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SET_THREAD_STACK_GUARANTEE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SET_THREAD_STACK_GUARANTEE 1
#else
#define HAVE_API_SUPPORT_WIN32_SET_THREAD_STACK_GUARANTEE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SET_WORKING_SET_SIZE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SET_WORKING_SET_SIZE 1
#else
#define HAVE_API_SUPPORT_WIN32_SET_WORKING_SET_SIZE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SH_GET_FOLDER_PATH
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SH_GET_FOLDER_PATH 1
#else
#define HAVE_API_SUPPORT_WIN32_SH_GET_FOLDER_PATH 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SHELL_EXECUTE_EX
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SHELL_EXECUTE_EX 1
#else
#define HAVE_API_SUPPORT_WIN32_SHELL_EXECUTE_EX 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_SIGNAL_OBJECT_AND_WAIT
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_SIGNAL_OBJECT_AND_WAIT 1
#else
#define HAVE_API_SUPPORT_WIN32_SIGNAL_OBJECT_AND_WAIT 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_TIMERS
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_TIMERS 1
#else
#define HAVE_API_SUPPORT_WIN32_TIMERS 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_TRANSMIT_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_TRANSMIT_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_TRANSMIT_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_UNLOCK_FILE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT) || \
	G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_UNLOCK_FILE 1
#else
#define HAVE_API_SUPPORT_WIN32_UNLOCK_FILE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_VER_LANGUAGE_NAME
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_VER_LANGUAGE_NAME 1
#else
#define HAVE_API_SUPPORT_WIN32_VER_LANGUAGE_NAME 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_VER_QUERY_VALUE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_VER_QUERY_VALUE 1
#else
#define HAVE_API_SUPPORT_WIN32_VER_QUERY_VALUE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_WAIT_FOR_INPUT_IDLE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_WAIT_FOR_INPUT_IDLE 1
#else
#define HAVE_API_SUPPORT_WIN32_WAIT_FOR_INPUT_IDLE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_PIPE_OPEN_CLOSE
#if G_HAVE_API_SUPPORT(HAVE_CLASSIC_WINAPI_SUPPORT)
#define HAVE_API_SUPPORT_WIN32_PIPE_OPEN_CLOSE 1
#else
#define HAVE_API_SUPPORT_WIN32_PIPE_OPEN_CLOSE 0
#endif
#endif

#ifndef HAVE_API_SUPPORT_WIN32_CONTEXT_XSTATE
#define HAVE_API_SUPPORT_WIN32_CONTEXT_XSTATE 0
#endif
