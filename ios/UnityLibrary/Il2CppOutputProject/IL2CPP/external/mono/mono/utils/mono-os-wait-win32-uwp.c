/**
 * \file
 * UWP OS wait support for Mono.
 *
 * Copyright 2017 Microsoft
 * Licensed under the MIT license. See LICENSE file in the project root for full license information.
*/
#include <config.h>
#include <glib.h>
#include "mono/utils/mono-compiler.h"

#if G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT | HAVE_XBOXONE_WINAPI_SUPPORT)
#include <windows.h>

DWORD
mono_win32_msg_wait_for_multiple_objects_ex (DWORD count, CONST HANDLE *handles, DWORD timeout, DWORD wakeMask, DWORD flags)
{
	g_unsupported_api ("MsgWaitForMultipleObjectsEx");
	SetLastError (ERROR_NOT_SUPPORTED);

	return WAIT_FAILED;
}

#else /* G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT | HAVE_XBOXONE_WINAPI_SUPPORT) */

MONO_EMPTY_SOURCE_FILE (mono_os_wait_win32_uwp);
#endif /* G_HAVE_API_SUPPORT(HAVE_UWP_WINAPI_SUPPORT | HAVE_XBOXONE_WINAPI_SUPPORT) */
