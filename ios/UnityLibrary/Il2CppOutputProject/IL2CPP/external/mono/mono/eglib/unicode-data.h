/*
This file is automatically generated by ucd.exe.
The source for this generator should be in Mono repository
(mcs/class/corlib/Mono.Globalization.Unicode directory).
*/

#ifndef __UNICODE_DATA_H
#define __UNICODE_DATA_H

#include <glib.h>


/* ======== Structures ======== */
typedef struct {
	guint32 codepoint;
	guint32 upper;
	guint32 title;
} SimpleTitlecaseMapping;
typedef struct {
	guint32 start;
	guint32 end;
} CodePointRange;
typedef struct {
	guint32 upper;
	guint32 lower;
} SimpleCaseMapping;

/* ======== Unicode Categories ======== */
static const guint8 unicode_category_ranges_count = 11;
static const CodePointRange unicode_category_ranges [] = {
{0x000000, 0x003400},
{0x004DC0, 0x004E00},
{0x00A000, 0x00AA80},
{0x00F900, 0x010000},
{0x010000, 0x0104C0},
{0x010800, 0x010A80},
{0x012000, 0x012480},
{0x01D000, 0x01D800},
{0x01F000, 0x01F0C0},
{0x02F800, 0x02FA40},
{0x0E0000, 0x0E0200},
{0, 0}};
static const guint8 unicode_category_table0 [] = {
	/* ==== 0-3400 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	29,21,21,21,23,21,21,21,22,18,21,25,21,17,21,21,
	13,13,13,13,13,13,13,13,13,13,21,21,25,25,25,21,
	21,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,22,21,18,24,16,
	24,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,22,25,18,25,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	29,21,23,23,23,23,26,26,24,26,5,20,25,1,26,24,
	26,25,15,15,24,5,26,21,24,15,5,19,15,15,15,21,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,25,9,9,9,9,9,9,9,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,25,5,5,5,5,5,5,5,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,5,9,5,9,5,9,5,9,
	5,9,5,9,5,9,5,9,5,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,9,5,9,5,9,5,5,
	5,9,9,5,9,5,9,9,5,9,9,9,5,5,9,9,
	9,9,5,9,9,5,9,9,9,5,5,5,9,9,5,9,
	9,5,9,5,9,5,9,9,5,9,5,5,9,5,9,9,
	5,9,9,9,5,9,5,9,9,5,5,7,9,5,5,5,
	7,7,7,7,9,8,5,9,8,5,9,8,5,9,5,9,
	5,9,5,9,5,9,5,9,5,9,5,9,5,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	5,9,8,5,9,5,9,9,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,5,5,5,5,5,5,9,9,5,9,9,5,
	5,9,5,9,9,9,9,5,9,5,9,5,9,5,9,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,7,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
	6,6,24,24,24,24,6,6,6,6,6,6,6,6,6,6,
	6,6,24,24,24,24,24,24,24,24,24,24,24,24,24,24,
	6,6,6,6,6,24,24,24,24,24,24,24,6,24,6,24,
	24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	9,5,9,5,6,24,9,5,0,0,6,5,5,5,21,0,
	0,0,0,0,24,24,9,21,9,9,9,0,9,0,9,9,
	5,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,0,9,9,9,9,9,9,9,9,9,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,9,
	5,5,9,9,9,5,5,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	5,5,5,5,9,5,25,9,5,9,9,5,5,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,26,12,12,12,12,12,11,11,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,9,5,9,5,9,5,9,5,9,5,9,5,9,5,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,0,0,0,0,0,0,0,0,0,0,0,0,
	0,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,0,0,6,21,21,21,21,21,21,
	0,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,0,21,17,0,0,0,0,0,
	0,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,17,12,
	21,12,12,21,12,12,21,12,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,
	7,7,7,21,21,0,0,0,0,0,0,0,0,0,0,0,
	1,1,1,1,0,0,25,25,25,21,21,23,21,21,26,26,
	12,12,12,12,12,12,12,12,12,12,12,21,0,0,21,21,
	0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	6,7,7,7,7,7,7,7,7,7,7,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,0,
	13,13,13,13,13,13,13,13,13,13,21,21,21,21,7,7,
	12,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,21,7,12,12,12,12,12,12,12,1,11,12,
	12,12,12,12,12,6,6,12,12,26,12,12,12,12,7,7,
	13,13,13,13,13,13,13,13,13,13,7,7,7,26,26,7,
	21,21,21,21,21,21,21,21,21,21,21,21,21,21,0,1,
	7,12,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,0,0,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,12,12,12,12,12,12,12,12,12,12,
	12,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	13,13,13,13,13,13,13,13,13,13,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,12,12,12,12,12,
	12,12,12,12,6,6,26,21,21,21,6,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,12,12,10,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,12,7,10,10,
	10,12,12,12,12,12,12,12,12,10,10,10,10,12,0,0,
	7,12,12,12,12,0,0,0,7,7,7,7,7,7,7,7,
	7,7,12,12,21,21,13,13,13,13,13,13,13,13,13,13,
	21,6,7,0,0,0,0,0,0,0,0,7,7,7,7,7,
	0,12,10,10,0,7,7,7,7,7,7,7,7,0,0,7,
	7,0,0,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,
	7,0,7,0,0,0,7,7,7,7,0,0,12,7,10,10,
	10,12,12,12,12,0,0,10,10,0,0,10,10,12,7,0,
	0,0,0,0,0,0,0,10,0,0,0,0,7,7,0,7,
	7,7,12,12,0,0,13,13,13,13,13,13,13,13,13,13,
	7,7,23,23,15,15,15,15,15,15,26,0,0,0,0,0,
	0,12,12,10,0,7,7,7,7,7,7,0,0,0,0,7,
	7,0,0,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,
	7,0,7,7,0,7,7,0,7,7,0,0,12,0,10,10,
	10,12,12,0,0,0,0,12,12,0,0,12,12,12,0,0,
	0,12,0,0,0,0,0,0,0,7,7,7,7,0,7,0,
	0,0,0,0,0,0,13,13,13,13,13,13,13,13,13,13,
	12,12,7,7,7,12,0,0,0,0,0,0,0,0,0,0,
	0,12,12,10,0,7,7,7,7,7,7,7,7,7,0,7,
	7,7,0,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,
	7,0,7,7,0,7,7,7,7,7,0,0,12,7,10,10,
	10,12,12,12,12,12,0,12,12,10,0,10,10,12,0,0,
	7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,12,12,0,0,13,13,13,13,13,13,13,13,13,13,
	0,23,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,12,10,10,0,7,7,7,7,7,7,7,7,0,0,7,
	7,0,0,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,
	7,0,7,7,0,7,7,7,7,7,0,0,12,7,10,12,
	10,12,12,12,12,0,0,10,10,0,0,10,10,12,0,0,
	0,0,0,0,0,0,12,10,0,0,0,0,7,7,0,7,
	7,7,12,12,0,0,13,13,13,13,13,13,13,13,13,13,
	26,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,12,7,0,7,7,7,7,7,7,0,0,0,7,7,
	7,0,7,7,7,7,0,0,0,7,7,0,7,0,7,7,
	0,0,0,7,7,0,0,0,7,7,7,0,0,0,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,0,0,10,10,
	12,10,10,0,0,0,10,10,10,0,10,10,10,12,0,0,
	7,0,0,0,0,0,0,10,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,13,13,13,13,13,13,13,13,13,13,
	15,15,15,26,26,26,26,26,26,23,26,0,0,0,0,0,
	0,10,10,10,0,7,7,7,7,7,7,7,7,0,7,7,
	7,0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,
	7,7,7,7,0,7,7,7,7,7,0,0,0,7,12,12,
	12,10,10,10,10,0,12,12,12,0,12,12,12,12,0,0,
	0,0,0,0,0,12,12,0,7,7,0,0,0,0,0,0,
	7,7,12,12,0,0,13,13,13,13,13,13,13,13,13,13,
	0,0,0,0,0,0,0,0,15,15,15,15,15,15,15,26,
	0,0,10,10,0,7,7,7,7,7,7,7,7,0,7,7,
	7,0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,
	7,7,7,7,0,7,7,7,7,7,0,0,12,7,10,12,
	10,10,10,10,10,0,12,10,10,0,10,10,12,12,0,0,
	0,0,0,0,0,10,10,0,0,0,0,0,0,0,7,0,
	7,7,12,12,0,0,13,13,13,13,13,13,13,13,13,13,
	0,26,26,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,10,10,0,7,7,7,7,7,7,7,7,0,7,7,
	7,0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,0,7,10,10,
	10,12,12,12,12,0,10,10,10,0,10,10,10,12,0,0,
	0,0,0,0,0,0,0,10,0,0,0,0,0,0,0,0,
	7,7,12,12,0,0,13,13,13,13,13,13,13,13,13,13,
	15,15,15,15,15,15,0,0,0,26,7,7,7,7,7,7,
	0,0,10,10,0,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,0,0,0,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,0,7,7,7,7,7,7,7,7,7,0,7,0,0,
	7,7,7,7,7,7,7,0,0,0,12,0,0,0,0,10,
	10,10,12,12,12,0,12,0,10,10,10,10,10,10,10,10,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,10,10,21,0,0,0,0,0,0,0,0,0,0,0,
	0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,12,7,7,12,12,12,12,12,12,12,0,0,0,0,23,
	7,7,7,7,7,7,6,12,12,12,12,12,12,12,12,21,
	13,13,13,13,13,13,13,13,13,13,21,21,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,7,7,0,7,0,0,7,7,0,7,0,0,7,0,0,
	0,0,0,0,7,7,7,7,0,7,7,7,7,7,7,7,
	0,7,7,7,0,7,0,7,0,0,7,7,0,7,7,7,
	7,12,7,7,12,12,12,12,12,12,0,12,12,7,0,0,
	7,7,7,7,7,0,6,0,12,12,12,12,12,12,0,0,
	13,13,13,13,13,13,13,13,13,13,0,0,7,7,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,26,26,26,21,21,21,21,21,21,21,21,21,21,21,21,
	21,21,21,26,26,26,26,26,12,12,26,26,26,26,26,26,
	13,13,13,13,13,13,13,13,13,13,15,15,15,15,15,15,
	15,15,15,15,26,12,26,12,26,12,22,18,22,18,10,10,
	7,7,7,7,7,7,7,7,0,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,0,
	0,12,12,12,12,12,12,12,12,12,12,12,12,12,12,10,
	12,12,12,12,12,21,12,12,7,7,7,7,0,0,0,0,
	12,12,12,12,12,12,12,12,0,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,0,26,26,
	26,26,26,26,26,26,12,26,26,26,26,26,26,0,26,26,
	21,21,21,21,21,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,10,10,12,12,12,
	12,10,12,12,12,12,12,12,10,12,12,10,10,12,12,7,
	13,13,13,13,13,13,13,13,13,13,21,21,21,21,21,21,
	7,7,7,7,7,7,10,10,12,12,7,7,7,7,12,12,
	12,7,10,10,10,7,7,10,10,10,10,10,10,10,7,7,
	7,12,12,12,12,7,7,7,7,7,7,7,7,7,7,7,
	7,7,12,10,10,12,12,10,10,10,10,10,10,12,7,10,
	13,13,13,13,13,13,13,13,13,13,0,0,0,0,26,26,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,21,6,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,0,0,0,0,0,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,0,0,
	7,7,7,7,7,7,7,0,7,0,7,7,7,7,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,0,7,7,7,7,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,0,7,7,7,7,0,0,7,7,7,7,7,7,7,0,
	7,0,7,7,7,7,0,0,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,0,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,0,7,7,7,7,0,0,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,0,0,0,0,12,
	26,21,21,21,21,21,21,21,21,15,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,15,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	26,26,26,26,26,26,26,26,26,26,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,0,0,0,0,0,0,0,0,0,0,0,
	0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,21,21,7,
	7,7,7,7,7,7,7,0,0,0,0,0,0,0,0,0,
	29,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,22,18,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,21,21,21,14,14,
	14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,0,7,7,
	7,7,12,12,12,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,12,12,12,21,21,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,12,12,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,0,7,7,
	7,0,12,12,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,1,1,10,12,12,12,12,12,12,12,10,10,
	10,10,10,10,10,10,12,10,10,12,12,12,12,12,12,12,
	12,12,12,12,21,21,21,6,21,21,21,23,7,12,0,0,
	13,13,13,13,13,13,13,13,13,13,0,0,0,0,0,0,
	15,15,15,15,15,15,15,15,15,15,0,0,0,0,0,0,
	21,21,21,21,21,21,17,21,21,21,21,12,12,12,29,0,
	13,13,13,13,13,13,13,13,13,13,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,6,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,12,7,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,0,
	12,12,12,10,10,10,10,12,12,10,10,10,0,0,0,0,
	10,10,12,10,10,10,10,10,10,12,12,12,0,0,0,0,
	26,0,0,0,21,21,13,13,13,13,13,13,13,13,13,13,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,
	7,7,7,7,7,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,0,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,7,7,7,7,7,7,7,10,10,0,0,0,0,0,0,
	13,13,13,13,13,13,13,13,13,13,0,0,0,0,21,21,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,12,12,10,10,10,0,0,21,21,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	12,12,12,12,10,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,12,10,12,12,12,12,12,10,12,10,10,10,
	10,10,12,10,10,7,7,7,7,7,7,7,0,0,0,0,
	13,13,13,13,13,13,13,13,13,13,21,21,21,21,21,21,
	21,26,26,26,26,26,26,26,26,26,26,12,12,12,12,12,
	12,12,12,12,26,26,26,26,26,26,26,26,26,0,0,0,
	12,12,10,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,10,12,12,12,12,10,10,12,12,10,0,0,0,7,7,
	13,13,13,13,13,13,13,13,13,13,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,10,10,10,10,10,10,10,10,12,12,12,12,
	12,12,12,12,10,10,12,12,0,0,0,21,21,21,21,21,
	13,13,13,13,13,13,13,13,13,13,0,0,0,7,7,7,
	13,13,13,13,13,13,13,13,13,13,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,6,6,6,6,6,6,21,21,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,
	6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
	6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
	6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
	6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,6,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,
	6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
	6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,12,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,5,5,5,5,5,5,5,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	5,5,5,5,5,5,5,5,9,9,9,9,9,9,9,9,
	5,5,5,5,5,5,0,0,9,9,9,9,9,9,0,0,
	5,5,5,5,5,5,5,5,9,9,9,9,9,9,9,9,
	5,5,5,5,5,5,5,5,9,9,9,9,9,9,9,9,
	5,5,5,5,5,5,0,0,9,9,9,9,9,9,0,0,
	5,5,5,5,5,5,5,5,0,9,0,9,0,9,0,9,
	5,5,5,5,5,5,5,5,9,9,9,9,9,9,9,9,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,0,0,
	5,5,5,5,5,5,5,5,8,8,8,8,8,8,8,8,
	5,5,5,5,5,5,5,5,8,8,8,8,8,8,8,8,
	5,5,5,5,5,5,5,5,8,8,8,8,8,8,8,8,
	5,5,5,5,5,0,5,5,9,9,9,9,8,24,5,24,
	24,24,5,5,5,0,5,5,9,9,9,9,8,24,24,24,
	5,5,5,5,0,0,5,5,9,9,9,9,0,24,24,24,
	5,5,5,5,5,5,5,5,9,9,9,9,9,24,24,24,
	0,0,5,5,5,0,5,5,9,9,9,9,8,24,24,0,
	29,29,29,29,29,29,29,29,29,29,29,1,1,1,1,1,
	17,17,17,17,17,17,21,21,20,19,22,20,20,19,22,20,
	21,21,21,21,21,21,21,21,27,28,1,1,1,1,1,29,
	21,21,21,21,21,21,21,21,21,20,19,21,21,21,21,16,
	16,21,21,21,25,22,18,21,21,21,21,21,21,21,21,21,
	21,21,25,21,16,21,21,21,21,21,21,21,21,21,21,29,
	1,1,1,1,1,0,0,0,0,0,1,1,1,1,1,1,
	15,5,0,0,15,15,15,15,15,15,25,25,25,22,18,5,
	15,15,15,15,15,15,15,15,15,15,25,25,25,22,18,0,
	6,6,6,6,6,0,0,0,0,0,0,0,0,0,0,0,
	23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,
	23,23,23,23,23,23,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	12,12,12,12,12,12,12,12,12,12,12,12,12,11,11,11,
	11,12,11,11,11,12,12,12,12,12,12,12,12,12,12,12,
	12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,9,26,26,26,26,9,26,26,5,9,9,9,5,5,
	9,9,9,5,26,9,26,26,26,9,9,9,9,9,26,26,
	26,26,26,26,9,26,9,26,9,26,9,9,9,9,26,5,
	9,9,9,9,5,7,7,7,7,5,26,26,5,5,9,9,
	25,25,25,25,25,9,5,5,5,5,26,25,26,26,5,26,
	0,0,0,15,15,15,15,15,15,15,15,15,15,15,15,15,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,9,5,14,14,14,14,0,0,0,0,0,0,0,
	25,25,25,25,25,26,26,26,26,26,25,25,26,26,26,26,
	25,26,26,25,26,26,25,26,26,26,26,26,26,26,25,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,25,25,
	26,26,25,26,25,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	26,26,26,26,26,26,26,26,25,25,25,25,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	25,25,26,26,26,26,26,26,26,22,18,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,25,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,25,25,25,25,
	25,25,26,26,26,26,26,26,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,25,26,26,26,26,26,26,26,26,
	26,25,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,25,25,25,25,25,25,25,25,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,25,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,0,0,0,
	26,26,26,26,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,26,26,26,26,0,26,26,26,26,0,0,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,0,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,0,26,0,26,
	26,26,26,0,0,0,26,0,26,26,26,26,26,26,26,0,
	0,26,26,26,26,26,26,26,22,18,22,18,22,18,22,18,
	22,18,22,18,22,18,15,15,15,15,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	15,15,15,15,26,0,0,0,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	0,26,26,26,26,26,26,26,26,26,26,26,26,26,26,0,
	25,25,25,25,25,22,18,25,25,25,25,0,25,0,0,0,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,22,18,22,18,22,18,22,18,22,18,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,22,18,22,18,22,18,22,18,22,18,22,18,22,
	18,22,18,22,18,22,18,22,18,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,22,18,22,18,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,22,18,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,
	25,25,25,25,25,26,26,25,25,25,25,25,25,0,0,0,
	26,26,26,26,26,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,0,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,0,
	9,5,9,9,9,5,5,9,5,9,5,9,5,9,9,9,
	0,5,9,5,5,9,5,5,5,5,5,5,5,6,0,0,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,5,26,26,26,26,26,26,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,21,21,21,21,15,21,21,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,0,0,0,0,0,0,0,0,0,6,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,0,7,7,7,7,7,7,7,0,
	7,7,7,7,7,7,7,0,7,7,7,7,7,7,7,0,
	7,7,7,7,7,7,7,0,7,7,7,7,7,7,7,0,
	7,7,7,7,7,7,7,0,7,7,7,7,7,7,7,0,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	21,21,20,19,20,19,21,21,21,20,19,21,20,19,21,21,
	21,21,21,21,21,21,21,17,21,21,17,21,20,19,21,21,
	20,19,22,18,22,18,22,18,22,18,21,21,21,21,21,6,
	21,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,0,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,0,0,0,0,
	29,21,21,21,26,6,7,14,22,18,22,18,22,18,22,18,
	22,18,26,26,22,18,22,18,22,18,22,18,17,22,18,18,
	26,14,14,14,14,14,14,14,14,14,12,12,12,12,12,12,
	17,6,6,6,6,6,26,26,14,14,14,6,7,21,26,26,
	0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,0,0,12,12,24,24,6,6,7,
	17,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,21,6,6,6,7,
	0,0,0,0,0,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,
	0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,
	26,26,15,15,15,15,26,26,26,26,26,26,26,26,26,26,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,0,
	15,15,15,15,15,15,15,15,15,15,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,0,0,0,0,0,0,0,0,0,0,0,0,
	26,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	15,15,15,15,15,15,15,15,15,15,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	0};
static const guint8 unicode_category_table1 [] = {
	/* ==== 4DC0-4E00 ==== */
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	0};
static const guint8 unicode_category_table2 [] = {
	/* ==== A000-AA80 ==== */
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,6,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,6,21,21,21,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	13,13,13,13,13,13,13,13,13,13,7,7,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	0,0,9,5,9,5,9,5,9,5,9,5,9,5,7,12,
	11,11,11,21,0,0,0,0,0,0,0,0,12,12,21,6,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,
	24,24,24,24,24,24,24,6,6,6,6,6,6,6,6,6,
	24,24,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	5,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	9,5,9,5,9,5,9,5,9,5,9,5,9,5,9,5,
	6,5,5,5,5,5,5,5,5,9,5,9,5,9,9,5,
	9,5,9,5,9,5,9,5,6,24,24,9,5,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,7,7,7,7,7,
	7,7,12,7,7,7,12,7,7,7,7,12,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,10,10,12,12,10,26,26,26,26,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,21,21,21,21,0,0,0,0,0,0,0,0,
	10,10,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,12,0,0,0,0,0,0,0,0,0,21,21,
	13,13,13,13,13,13,13,13,13,13,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	13,13,13,13,13,13,13,13,13,13,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,12,12,12,12,12,12,12,12,21,21,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,12,12,12,12,12,12,12,12,12,
	12,12,10,10,0,0,0,0,0,0,0,0,0,0,0,21,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,12,12,12,12,12,12,10,
	10,12,12,10,10,12,12,0,0,0,0,0,0,0,0,0,
	7,7,7,12,7,7,7,7,7,7,7,7,12,10,0,0,
	13,13,13,13,13,13,13,13,13,13,0,0,21,21,21,21,
	0};
static const guint8 unicode_category_table3 [] = {
	/* ==== F900-10000 ==== */
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	5,5,5,5,5,5,5,0,0,0,0,0,0,0,0,0,
	0,0,0,5,5,5,5,5,0,0,0,0,0,7,12,7,
	7,7,7,7,7,7,7,7,7,25,7,7,7,7,7,7,
	7,7,7,7,7,7,7,0,7,7,7,7,7,0,7,0,
	7,7,0,7,7,0,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,22,18,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	0,0,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,23,26,0,0,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	21,21,21,21,21,21,21,22,18,21,0,0,0,0,0,0,
	12,12,12,12,12,12,12,0,0,0,0,0,0,0,0,0,
	21,17,17,16,16,22,18,22,18,22,18,22,18,22,18,22,
	18,22,18,22,18,21,21,22,18,21,21,21,21,16,16,16,
	21,21,21,0,21,21,21,21,17,22,18,22,18,22,18,21,
	21,21,25,17,25,25,25,0,21,23,21,21,0,0,0,0,
	7,7,7,7,7,0,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,1,
	0,21,21,21,23,21,21,21,22,18,21,25,21,17,21,21,
	13,13,13,13,13,13,13,13,13,13,21,21,25,25,25,21,
	21,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,22,21,18,24,16,
	24,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,22,25,18,25,22,
	18,21,22,18,21,21,7,7,7,7,7,7,7,7,7,7,
	6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,6,6,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,
	0,0,7,7,7,7,7,7,0,0,7,7,7,7,7,7,
	0,0,7,7,7,7,7,7,0,0,7,7,7,0,0,0,
	23,23,25,24,26,23,23,0,26,25,25,25,25,26,26,0,
	0,0,0,0,0,0,0,0,0,1,1,1,26,26,0};
static const guint8 unicode_category_table4 [] = {
	/* ==== 10000-104C0 ==== */
	7,7,7,7,7,7,7,7,7,7,7,7,0,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,0,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,0,7,7,0,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,
	21,21,26,0,0,0,0,15,15,15,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	15,15,15,15,0,0,0,26,26,26,26,26,26,26,26,26,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,15,15,15,15,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,15,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,12,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,
	15,15,15,15,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,14,7,7,7,7,7,7,7,7,14,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,21,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,0,0,0,0,7,7,7,7,7,7,7,7,
	21,14,14,14,14,14,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,0,
	13,13,13,13,13,13,13,13,13,13,0};
static const guint8 unicode_category_table5 [] = {
	/* ==== 10800-10A80 ==== */
	7,7,7,7,7,7,0,0,7,0,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,0,7,7,0,0,0,7,0,0,7,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,15,15,15,15,0,0,0,0,0,21,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,0,0,0,0,0,21,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	7,12,12,12,0,12,12,0,0,0,0,0,12,12,12,12,
	7,7,7,7,0,7,7,7,0,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,0,0,0,0,12,12,12,0,0,0,0,12,
	15,15,15,15,15,15,15,15,0,0,0,0,0,0,0,0,
	21,21,21,21,21,21,21,21,21,0};
static const guint8 unicode_category_table6 [] = {
	/* ==== 12000-12480 ==== */
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
	14,14,14,0,0,0,0,0,0,0,0,0,0,0,0,0,
	21,21,21,21,0};
static const guint8 unicode_category_table7 [] = {
	/* ==== 1D000-1D800 ==== */
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,0,0,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,10,10,12,12,12,26,26,26,10,10,10,
	10,10,10,1,1,1,1,1,1,1,1,12,12,12,12,12,
	12,12,12,26,26,12,12,12,12,12,12,12,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,12,12,12,12,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,12,12,12,26,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,0,0,0,0,0,0,0,0,0,
	15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,
	15,15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,5,5,
	5,5,5,5,5,0,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,9,0,9,9,
	0,0,9,0,0,9,9,0,0,9,9,9,9,0,9,9,
	9,9,9,9,9,9,5,5,5,5,0,5,0,5,5,5,
	5,5,5,5,0,5,5,5,5,5,5,5,5,5,5,5,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,9,9,0,9,9,9,9,0,0,9,9,9,
	9,9,9,9,9,0,9,9,9,9,9,9,9,0,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,9,9,0,9,9,9,9,0,
	9,9,9,9,9,0,9,0,0,0,9,9,9,9,9,9,
	9,0,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,0,0,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,25,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,25,5,5,5,5,
	5,5,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,25,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,25,5,5,5,5,5,5,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,25,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,25,
	5,5,5,5,5,5,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,25,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,25,5,5,5,5,5,5,
	9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,
	9,9,9,9,9,9,9,9,9,25,5,5,5,5,5,5,
	5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
	5,5,5,25,5,5,5,5,5,5,9,5,0,0,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	0};
static const guint8 unicode_category_table8 [] = {
	/* ==== 1F000-1F0C0 ==== */
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,0,0,0,0,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,
	26,26,26,26,0};
static const guint8 unicode_category_table9 [] = {
	/* ==== 2F800-2FA40 ==== */
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,
	7,7,7,7,7,7,7,7,7,7,7,7,7,7,0};
static const guint8 unicode_category_table10 [] = {
	/* ==== E0000-E0200 ==== */
	0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
	1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
	1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
	1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
	1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
	1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	0};
static const guint8 *unicode_category [11]  = {
	unicode_category_table0,
	unicode_category_table1,
	unicode_category_table2,
	unicode_category_table3,
	unicode_category_table4,
	unicode_category_table5,
	unicode_category_table6,
	unicode_category_table7,
	unicode_category_table8,
	unicode_category_table9,
	unicode_category_table10
};

static const guint8 simple_case_map_ranges_count = 9;
static const CodePointRange simple_case_map_ranges [] = {
{0x000040, 0x000600},
{0x001000, 0x0010D0},
{0x001D00, 0x002000},
{0x002100, 0x0021C0},
{0x002480, 0x002500},
{0x002C00, 0x002D80},
{0x00A640, 0x00A7C0},
{0x00FF20, 0x00FF80},
{0x010400, 0x010480},
{0, 0}};
static const guint16 simple_upper_case_mapping_lowarea_table0 [] = {
	/* ==== 40-600 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4A,0x4B,0x4C,0x4D,0x4E,0x4F,
	0x50,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5A,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0x39C,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0xC0,0xC1,0xC2,0xC3,0xC4,0xC5,0xC6,0xC7,0xC8,0xC9,0xCA,0xCB,0xCC,0xCD,0xCE,0xCF,
	0xD0,0xD1,0xD2,0xD3,0xD4,0xD5,0xD6,0,0xD8,0xD9,0xDA,0xDB,0xDC,0xDD,0xDE,0x178,
	0,0x100,0,0x102,0,0x104,0,0x106,0,0x108,0,0x10A,0,0x10C,0,0x10E,
	0,0x110,0,0x112,0,0x114,0,0x116,0,0x118,0,0x11A,0,0x11C,0,0x11E,
	0,0x120,0,0x122,0,0x124,0,0x126,0,0x128,0,0x12A,0,0x12C,0,0x12E,
	0,0x49,0,0x132,0,0x134,0,0x136,0,0,0x139,0,0x13B,0,0x13D,0,
	0x13F,0,0x141,0,0x143,0,0x145,0,0x147,0,0,0x14A,0,0x14C,0,0x14E,
	0,0x150,0,0x152,0,0x154,0,0x156,0,0x158,0,0x15A,0,0x15C,0,0x15E,
	0,0x160,0,0x162,0,0x164,0,0x166,0,0x168,0,0x16A,0,0x16C,0,0x16E,
	0,0x170,0,0x172,0,0x174,0,0x176,0,0,0x179,0,0x17B,0,0x17D,0x53,
	0x243,0,0,0x182,0,0x184,0,0,0x187,0,0,0,0x18B,0,0,0,
	0,0,0x191,0,0,0x1F6,0,0,0,0x198,0x23D,0,0,0,0x220,0,
	0,0x1A0,0,0x1A2,0,0x1A4,0,0,0x1A7,0,0,0,0,0x1AC,0,0,
	0x1AF,0,0,0,0x1B3,0,0x1B5,0,0,0x1B8,0,0,0,0x1BC,0,0x1F7,
	0,0,0,0,0,0x1C4,0x1C4,0,0x1C7,0x1C7,0,0x1CA,0x1CA,0,0x1CD,0,
	0x1CF,0,0x1D1,0,0x1D3,0,0x1D5,0,0x1D7,0,0x1D9,0,0x1DB,0x18E,0,0x1DE,
	0,0x1E0,0,0x1E2,0,0x1E4,0,0x1E6,0,0x1E8,0,0x1EA,0,0x1EC,0,0x1EE,
	0,0,0x1F1,0x1F1,0,0x1F4,0,0,0,0x1F8,0,0x1FA,0,0x1FC,0,0x1FE,
	0,0x200,0,0x202,0,0x204,0,0x206,0,0x208,0,0x20A,0,0x20C,0,0x20E,
	0,0x210,0,0x212,0,0x214,0,0x216,0,0x218,0,0x21A,0,0x21C,0,0x21E,
	0,0,0,0x222,0,0x224,0,0x226,0,0x228,0,0x22A,0,0x22C,0,0x22E,
	0,0x230,0,0x232,0,0,0,0,0,0,0,0,0x23B,0,0,0,
	0,0,0x241,0,0,0,0,0x246,0,0x248,0,0x24A,0,0x24C,0,0x24E,
	0x2C6F,0x2C6D,0,0x181,0x186,0,0x189,0x18A,0,0x18F,0,0x190,0,0,0,0,
	0x193,0,0,0x194,0,0,0,0,0x197,0x196,0,0x2C62,0,0,0,0x19C,
	0,0x2C6E,0x19D,0,0,0x19F,0,0,0,0,0,0,0,0x2C64,0,0,
	0x1A6,0,0,0x1A9,0,0,0,0,0x1AE,0x244,0x1B1,0x1B2,0x245,0,0,0,
	0,0,0x1B7,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0x399,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0x370,0,0x372,0,0,0,0x376,0,0,0,0x3FD,0x3FE,0x3FF,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0x386,0x388,0x389,0x38A,
	0,0x391,0x392,0x393,0x394,0x395,0x396,0x397,0x398,0x399,0x39A,0x39B,0x39C,0x39D,0x39E,0x39F,
	0x3A0,0x3A1,0x3A3,0x3A3,0x3A4,0x3A5,0x3A6,0x3A7,0x3A8,0x3A9,0x3AA,0x3AB,0x38C,0x38E,0x38F,0,
	0x392,0x398,0,0,0,0x3A6,0x3A0,0x3CF,0,0x3D8,0,0x3DA,0,0x3DC,0,0x3DE,
	0,0x3E0,0,0x3E2,0,0x3E4,0,0x3E6,0,0x3E8,0,0x3EA,0,0x3EC,0,0x3EE,
	0x39A,0x3A1,0x3F9,0,0,0x395,0,0,0x3F7,0,0,0x3FA,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x410,0x411,0x412,0x413,0x414,0x415,0x416,0x417,0x418,0x419,0x41A,0x41B,0x41C,0x41D,0x41E,0x41F,
	0x420,0x421,0x422,0x423,0x424,0x425,0x426,0x427,0x428,0x429,0x42A,0x42B,0x42C,0x42D,0x42E,0x42F,
	0x400,0x401,0x402,0x403,0x404,0x405,0x406,0x407,0x408,0x409,0x40A,0x40B,0x40C,0x40D,0x40E,0x40F,
	0,0x460,0,0x462,0,0x464,0,0x466,0,0x468,0,0x46A,0,0x46C,0,0x46E,
	0,0x470,0,0x472,0,0x474,0,0x476,0,0x478,0,0x47A,0,0x47C,0,0x47E,
	0,0x480,0,0,0,0,0,0,0,0,0,0x48A,0,0x48C,0,0x48E,
	0,0x490,0,0x492,0,0x494,0,0x496,0,0x498,0,0x49A,0,0x49C,0,0x49E,
	0,0x4A0,0,0x4A2,0,0x4A4,0,0x4A6,0,0x4A8,0,0x4AA,0,0x4AC,0,0x4AE,
	0,0x4B0,0,0x4B2,0,0x4B4,0,0x4B6,0,0x4B8,0,0x4BA,0,0x4BC,0,0x4BE,
	0,0,0x4C1,0,0x4C3,0,0x4C5,0,0x4C7,0,0x4C9,0,0x4CB,0,0x4CD,0x4C0,
	0,0x4D0,0,0x4D2,0,0x4D4,0,0x4D6,0,0x4D8,0,0x4DA,0,0x4DC,0,0x4DE,
	0,0x4E0,0,0x4E2,0,0x4E4,0,0x4E6,0,0x4E8,0,0x4EA,0,0x4EC,0,0x4EE,
	0,0x4F0,0,0x4F2,0,0x4F4,0,0x4F6,0,0x4F8,0,0x4FA,0,0x4FC,0,0x4FE,
	0,0x500,0,0x502,0,0x504,0,0x506,0,0x508,0,0x50A,0,0x50C,0,0x50E,
	0,0x510,0,0x512,0,0x514,0,0x516,0,0x518,0,0x51A,0,0x51C,0,0x51E,
	0,0x520,0,0x522,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0x531,0x532,0x533,0x534,0x535,0x536,0x537,0x538,0x539,0x53A,0x53B,0x53C,0x53D,0x53E,0x53F,
	0x540,0x541,0x542,0x543,0x544,0x545,0x546,0x547,0x548,0x549,0x54A,0x54B,0x54C,0x54D,0x54E,0x54F,
	0x550,0x551,0x552,0x553,0x554,0x555,0x556,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0};
static const guint16 simple_upper_case_mapping_lowarea_table1 [] = {
	/* ==== 1000-10D0 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0};
static const guint16 simple_upper_case_mapping_lowarea_table2 [] = {
	/* ==== 1D00-2000 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0xA77D,0,0,0,0x2C63,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0x1E00,0,0x1E02,0,0x1E04,0,0x1E06,0,0x1E08,0,0x1E0A,0,0x1E0C,0,0x1E0E,
	0,0x1E10,0,0x1E12,0,0x1E14,0,0x1E16,0,0x1E18,0,0x1E1A,0,0x1E1C,0,0x1E1E,
	0,0x1E20,0,0x1E22,0,0x1E24,0,0x1E26,0,0x1E28,0,0x1E2A,0,0x1E2C,0,0x1E2E,
	0,0x1E30,0,0x1E32,0,0x1E34,0,0x1E36,0,0x1E38,0,0x1E3A,0,0x1E3C,0,0x1E3E,
	0,0x1E40,0,0x1E42,0,0x1E44,0,0x1E46,0,0x1E48,0,0x1E4A,0,0x1E4C,0,0x1E4E,
	0,0x1E50,0,0x1E52,0,0x1E54,0,0x1E56,0,0x1E58,0,0x1E5A,0,0x1E5C,0,0x1E5E,
	0,0x1E60,0,0x1E62,0,0x1E64,0,0x1E66,0,0x1E68,0,0x1E6A,0,0x1E6C,0,0x1E6E,
	0,0x1E70,0,0x1E72,0,0x1E74,0,0x1E76,0,0x1E78,0,0x1E7A,0,0x1E7C,0,0x1E7E,
	0,0x1E80,0,0x1E82,0,0x1E84,0,0x1E86,0,0x1E88,0,0x1E8A,0,0x1E8C,0,0x1E8E,
	0,0x1E90,0,0x1E92,0,0x1E94,0,0,0,0,0,0x1E60,0,0,0,0,
	0,0x1EA0,0,0x1EA2,0,0x1EA4,0,0x1EA6,0,0x1EA8,0,0x1EAA,0,0x1EAC,0,0x1EAE,
	0,0x1EB0,0,0x1EB2,0,0x1EB4,0,0x1EB6,0,0x1EB8,0,0x1EBA,0,0x1EBC,0,0x1EBE,
	0,0x1EC0,0,0x1EC2,0,0x1EC4,0,0x1EC6,0,0x1EC8,0,0x1ECA,0,0x1ECC,0,0x1ECE,
	0,0x1ED0,0,0x1ED2,0,0x1ED4,0,0x1ED6,0,0x1ED8,0,0x1EDA,0,0x1EDC,0,0x1EDE,
	0,0x1EE0,0,0x1EE2,0,0x1EE4,0,0x1EE6,0,0x1EE8,0,0x1EEA,0,0x1EEC,0,0x1EEE,
	0,0x1EF0,0,0x1EF2,0,0x1EF4,0,0x1EF6,0,0x1EF8,0,0x1EFA,0,0x1EFC,0,0x1EFE,
	0x1F08,0x1F09,0x1F0A,0x1F0B,0x1F0C,0x1F0D,0x1F0E,0x1F0F,0,0,0,0,0,0,0,0,
	0x1F18,0x1F19,0x1F1A,0x1F1B,0x1F1C,0x1F1D,0,0,0,0,0,0,0,0,0,0,
0x1F28,0x1F29,0x1F2A,0x1F2B,0x1F2C,0x1F2D,0x1F2E,0x1F2F,0,0,0,0,0,0,0,0,
	0x1F38,0x1F39,0x1F3A,0x1F3B,0x1F3C,0x1F3D,0x1F3E,0x1F3F,0,0,0,0,0,0,0,0,
	0x1F48,0x1F49,0x1F4A,0x1F4B,0x1F4C,0x1F4D,0,0,0,0,0,0,0,0,0,0,
0,0x1F59,0,0x1F5B,0,0x1F5D,0,0x1F5F,0,0,0,0,0,0,0,0,
	0x1F68,0x1F69,0x1F6A,0x1F6B,0x1F6C,0x1F6D,0x1F6E,0x1F6F,0,0,0,0,0,0,0,0,
	0x1FBA,0x1FBB,0x1FC8,0x1FC9,0x1FCA,0x1FCB,0x1FDA,0x1FDB,0x1FF8,0x1FF9,0x1FEA,0x1FEB,0x1FFA,0x1FFB,0,0,
0x1F88,0x1F89,0x1F8A,0x1F8B,0x1F8C,0x1F8D,0x1F8E,0x1F8F,0,0,0,0,0,0,0,0,
	0x1F98,0x1F99,0x1F9A,0x1F9B,0x1F9C,0x1F9D,0x1F9E,0x1F9F,0,0,0,0,0,0,0,0,
	0x1FA8,0x1FA9,0x1FAA,0x1FAB,0x1FAC,0x1FAD,0x1FAE,0x1FAF,0,0,0,0,0,0,0,0,
	0x1FB8,0x1FB9,0,0x1FBC,0,0,0,0,0,0,0,0,0,0,0x399,0,
	0,0,0,0x1FCC,0,0,0,0,0,0,0,0,0,0,0,0,
	0x1FD8,0x1FD9,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x1FE8,0x1FE9,0,0,0,0x1FEC,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0x1FFC,0,0,0,0,0,0,0,0,0,0,0,0};
static const guint16 simple_upper_case_mapping_lowarea_table3 [] = {
	/* ==== 2100-21C0 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0x2132,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x2160,0x2161,0x2162,0x2163,0x2164,0x2165,0x2166,0x2167,0x2168,0x2169,0x216A,0x216B,0x216C,0x216D,0x216E,0x216F,
	0,0,0,0,0x2183,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 simple_upper_case_mapping_lowarea_table4 [] = {
	/* ==== 2480-2500 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x24B6,0x24B7,0x24B8,0x24B9,0x24BA,0x24BB,0x24BC,0x24BD,0x24BE,0x24BF,0x24C0,0x24C1,0x24C2,0x24C3,0x24C4,0x24C5,
	0x24C6,0x24C7,0x24C8,0x24C9,0x24CA,0x24CB,0x24CC,0x24CD,0x24CE,0x24CF,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 simple_upper_case_mapping_lowarea_table5 [] = {
	/* ==== 2C00-2D80 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0x2C00,0x2C01,0x2C02,0x2C03,0x2C04,0x2C05,0x2C06,0x2C07,0x2C08,0x2C09,0x2C0A,0x2C0B,0x2C0C,0x2C0D,0x2C0E,0x2C0F,
	0x2C10,0x2C11,0x2C12,0x2C13,0x2C14,0x2C15,0x2C16,0x2C17,0x2C18,0x2C19,0x2C1A,0x2C1B,0x2C1C,0x2C1D,0x2C1E,0x2C1F,
	0x2C20,0x2C21,0x2C22,0x2C23,0x2C24,0x2C25,0x2C26,0x2C27,0x2C28,0x2C29,0x2C2A,0x2C2B,0x2C2C,0x2C2D,0x2C2E,0,
0,0x2C60,0,0,0,0x23A,0x23E,0,0x2C67,0,0x2C69,0,0x2C6B,0,0,0,
	0,0,0,0x2C72,0,0,0x2C75,0,0,0,0,0,0,0,0,0,
0,0x2C80,0,0x2C82,0,0x2C84,0,0x2C86,0,0x2C88,0,0x2C8A,0,0x2C8C,0,0x2C8E,
	0,0x2C90,0,0x2C92,0,0x2C94,0,0x2C96,0,0x2C98,0,0x2C9A,0,0x2C9C,0,0x2C9E,
	0,0x2CA0,0,0x2CA2,0,0x2CA4,0,0x2CA6,0,0x2CA8,0,0x2CAA,0,0x2CAC,0,0x2CAE,
	0,0x2CB0,0,0x2CB2,0,0x2CB4,0,0x2CB6,0,0x2CB8,0,0x2CBA,0,0x2CBC,0,0x2CBE,
	0,0x2CC0,0,0x2CC2,0,0x2CC4,0,0x2CC6,0,0x2CC8,0,0x2CCA,0,0x2CCC,0,0x2CCE,
	0,0x2CD0,0,0x2CD2,0,0x2CD4,0,0x2CD6,0,0x2CD8,0,0x2CDA,0,0x2CDC,0,0x2CDE,
	0,0x2CE0,0,0x2CE2,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x10A0,0x10A1,0x10A2,0x10A3,0x10A4,0x10A5,0x10A6,0x10A7,0x10A8,0x10A9,0x10AA,0x10AB,0x10AC,0x10AD,0x10AE,0x10AF,
	0x10B0,0x10B1,0x10B2,0x10B3,0x10B4,0x10B5,0x10B6,0x10B7,0x10B8,0x10B9,0x10BA,0x10BB,0x10BC,0x10BD,0x10BE,0x10BF,
	0x10C0,0x10C1,0x10C2,0x10C3,0x10C4,0x10C5,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 simple_upper_case_mapping_lowarea_table6 [] = {
	/* ==== A640-A7C0 ==== */
	0,0xA640,0,0xA642,0,0xA644,0,0xA646,0,0xA648,0,0xA64A,0,0xA64C,0,0xA64E,
	0,0xA650,0,0xA652,0,0xA654,0,0xA656,0,0xA658,0,0xA65A,0,0xA65C,0,0xA65E,
	0,0,0,0xA662,0,0xA664,0,0xA666,0,0xA668,0,0xA66A,0,0xA66C,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0xA680,0,0xA682,0,0xA684,0,0xA686,0,0xA688,0,0xA68A,0,0xA68C,0,0xA68E,
	0,0xA690,0,0xA692,0,0xA694,0,0xA696,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0xA722,0,0xA724,0,0xA726,0,0xA728,0,0xA72A,0,0xA72C,0,0xA72E,
	0,0,0,0xA732,0,0xA734,0,0xA736,0,0xA738,0,0xA73A,0,0xA73C,0,0xA73E,
	0,0xA740,0,0xA742,0,0xA744,0,0xA746,0,0xA748,0,0xA74A,0,0xA74C,0,0xA74E,
	0,0xA750,0,0xA752,0,0xA754,0,0xA756,0,0xA758,0,0xA75A,0,0xA75C,0,0xA75E,
	0,0xA760,0,0xA762,0,0xA764,0,0xA766,0,0xA768,0,0xA76A,0,0xA76C,0,0xA76E,
	0,0,0,0,0,0,0,0,0,0,0xA779,0,0xA77B,0,0,0xA77E,
	0,0xA780,0,0xA782,0,0xA784,0,0xA786,0,0,0,0,0xA78B,0};
static const guint16 simple_upper_case_mapping_lowarea_table7 [] = {
	/* ==== FF20-FF80 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0xFF21,0xFF22,0xFF23,0xFF24,0xFF25,0xFF26,0xFF27,0xFF28,0xFF29,0xFF2A,0xFF2B,0xFF2C,0xFF2D,0xFF2E,0xFF2F,
	0xFF30,0xFF31,0xFF32,0xFF33,0xFF34,0xFF35,0xFF36,0xFF37,0xFF38,0xFF39,0xFF3A,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 *simple_upper_case_mapping_lowarea [] = {
	simple_upper_case_mapping_lowarea_table0,
	simple_upper_case_mapping_lowarea_table1,
	simple_upper_case_mapping_lowarea_table2,
	simple_upper_case_mapping_lowarea_table3,
	simple_upper_case_mapping_lowarea_table4,
	simple_upper_case_mapping_lowarea_table5,
	simple_upper_case_mapping_lowarea_table6,
	simple_upper_case_mapping_lowarea_table7};
static const int simple_upper_case_mapping_lowarea_table_count = 8;

static const guint32 simple_upper_case_mapping_higharea_table0 [] = {
	/* ==== 10400-10480 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0x10400,0x10401,0x10402,0x10403,0x10404,0x10405,0x10406,0x10407,
	0x10408,0x10409,0x1040A,0x1040B,0x1040C,0x1040D,0x1040E,0x1040F,0x10410,0x10411,0x10412,0x10413,0x10414,0x10415,0x10416,0x10417,
	0x10418,0x10419,0x1041A,0x1041B,0x1041C,0x1041D,0x1041E,0x1041F,0x10420,0x10421,0x10422,0x10423,0x10424,0x10425,0x10426,0x10427,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint32 *simple_upper_case_mapping_higharea [] = {
	simple_upper_case_mapping_higharea_table0};

static const guint16 simple_lower_case_mapping_lowarea_table0 [] = {
	/* ==== 40-600 ==== */
	0,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6A,0x6B,0x6C,0x6D,0x6E,0x6F,
	0x70,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7A,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0xE0,0xE1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9,0xEA,0xEB,0xEC,0xED,0xEE,0xEF,
	0xF0,0xF1,0xF2,0xF3,0xF4,0xF5,0xF6,0,0xF8,0xF9,0xFA,0xFB,0xFC,0xFD,0xFE,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x101,0,0x103,0,0x105,0,0x107,0,0x109,0,0x10B,0,0x10D,0,0x10F,0,
	0x111,0,0x113,0,0x115,0,0x117,0,0x119,0,0x11B,0,0x11D,0,0x11F,0,
	0x121,0,0x123,0,0x125,0,0x127,0,0x129,0,0x12B,0,0x12D,0,0x12F,0,
	0x69,0,0x133,0,0x135,0,0x137,0,0,0x13A,0,0x13C,0,0x13E,0,0x140,
	0,0x142,0,0x144,0,0x146,0,0x148,0,0,0x14B,0,0x14D,0,0x14F,0,
	0x151,0,0x153,0,0x155,0,0x157,0,0x159,0,0x15B,0,0x15D,0,0x15F,0,
	0x161,0,0x163,0,0x165,0,0x167,0,0x169,0,0x16B,0,0x16D,0,0x16F,0,
	0x171,0,0x173,0,0x175,0,0x177,0,0xFF,0x17A,0,0x17C,0,0x17E,0,0,
	0,0x253,0x183,0,0x185,0,0x254,0x188,0,0x256,0x257,0x18C,0,0,0x1DD,0x259,
	0x25B,0x192,0,0x260,0x263,0,0x269,0x268,0x199,0,0,0,0x26F,0x272,0,0x275,
	0x1A1,0,0x1A3,0,0x1A5,0,0x280,0x1A8,0,0x283,0,0,0x1AD,0,0x288,0x1B0,
	0,0x28A,0x28B,0x1B4,0,0x1B6,0,0x292,0x1B9,0,0,0,0x1BD,0,0,0,
	0,0,0,0,0x1C6,0x1C6,0,0x1C9,0x1C9,0,0x1CC,0x1CC,0,0x1CE,0,0x1D0,
	0,0x1D2,0,0x1D4,0,0x1D6,0,0x1D8,0,0x1DA,0,0x1DC,0,0,0x1DF,0,
	0x1E1,0,0x1E3,0,0x1E5,0,0x1E7,0,0x1E9,0,0x1EB,0,0x1ED,0,0x1EF,0,
	0,0x1F3,0x1F3,0,0x1F5,0,0x195,0x1BF,0x1F9,0,0x1FB,0,0x1FD,0,0x1FF,0,
	0x201,0,0x203,0,0x205,0,0x207,0,0x209,0,0x20B,0,0x20D,0,0x20F,0,
	0x211,0,0x213,0,0x215,0,0x217,0,0x219,0,0x21B,0,0x21D,0,0x21F,0,
	0x19E,0,0x223,0,0x225,0,0x227,0,0x229,0,0x22B,0,0x22D,0,0x22F,0,
	0x231,0,0x233,0,0,0,0,0,0,0,0x2C65,0x23C,0,0x19A,0x2C66,0,
	0,0x242,0,0x180,0x289,0x28C,0x247,0,0x249,0,0x24B,0,0x24D,0,0x24F,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x371,0,0x373,0,0,0,0x377,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0x3AC,0,0x3AD,0x3AE,0x3AF,0,0x3CC,0,0x3CD,0x3CE,
	0,0x3B1,0x3B2,0x3B3,0x3B4,0x3B5,0x3B6,0x3B7,0x3B8,0x3B9,0x3BA,0x3BB,0x3BC,0x3BD,0x3BE,0x3BF,
	0x3C0,0x3C1,0,0x3C3,0x3C4,0x3C5,0x3C6,0x3C7,0x3C8,0x3C9,0x3CA,0x3CB,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0x3D7,
	0,0,0,0,0,0,0,0,0x3D9,0,0x3DB,0,0x3DD,0,0x3DF,0,
	0x3E1,0,0x3E3,0,0x3E5,0,0x3E7,0,0x3E9,0,0x3EB,0,0x3ED,0,0x3EF,0,
	0,0,0,0,0x3B8,0,0,0x3F8,0,0x3F2,0x3FB,0,0,0x37B,0x37C,0x37D,
	0x450,0x451,0x452,0x453,0x454,0x455,0x456,0x457,0x458,0x459,0x45A,0x45B,0x45C,0x45D,0x45E,0x45F,
	0x430,0x431,0x432,0x433,0x434,0x435,0x436,0x437,0x438,0x439,0x43A,0x43B,0x43C,0x43D,0x43E,0x43F,
	0x440,0x441,0x442,0x443,0x444,0x445,0x446,0x447,0x448,0x449,0x44A,0x44B,0x44C,0x44D,0x44E,0x44F,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x461,0,0x463,0,0x465,0,0x467,0,0x469,0,0x46B,0,0x46D,0,0x46F,0,
	0x471,0,0x473,0,0x475,0,0x477,0,0x479,0,0x47B,0,0x47D,0,0x47F,0,
	0x481,0,0,0,0,0,0,0,0,0,0x48B,0,0x48D,0,0x48F,0,
	0x491,0,0x493,0,0x495,0,0x497,0,0x499,0,0x49B,0,0x49D,0,0x49F,0,
	0x4A1,0,0x4A3,0,0x4A5,0,0x4A7,0,0x4A9,0,0x4AB,0,0x4AD,0,0x4AF,0,
	0x4B1,0,0x4B3,0,0x4B5,0,0x4B7,0,0x4B9,0,0x4BB,0,0x4BD,0,0x4BF,0,
	0x4CF,0x4C2,0,0x4C4,0,0x4C6,0,0x4C8,0,0x4CA,0,0x4CC,0,0x4CE,0,0,
	0x4D1,0,0x4D3,0,0x4D5,0,0x4D7,0,0x4D9,0,0x4DB,0,0x4DD,0,0x4DF,0,
	0x4E1,0,0x4E3,0,0x4E5,0,0x4E7,0,0x4E9,0,0x4EB,0,0x4ED,0,0x4EF,0,
	0x4F1,0,0x4F3,0,0x4F5,0,0x4F7,0,0x4F9,0,0x4FB,0,0x4FD,0,0x4FF,0,
	0x501,0,0x503,0,0x505,0,0x507,0,0x509,0,0x50B,0,0x50D,0,0x50F,0,
	0x511,0,0x513,0,0x515,0,0x517,0,0x519,0,0x51B,0,0x51D,0,0x51F,0,
	0x521,0,0x523,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0x561,0x562,0x563,0x564,0x565,0x566,0x567,0x568,0x569,0x56A,0x56B,0x56C,0x56D,0x56E,0x56F,
	0x570,0x571,0x572,0x573,0x574,0x575,0x576,0x577,0x578,0x579,0x57A,0x57B,0x57C,0x57D,0x57E,0x57F,
	0x580,0x581,0x582,0x583,0x584,0x585,0x586,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0};
static const guint16 simple_lower_case_mapping_lowarea_table1 [] = {
	/* ==== 1000-10D0 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x2D00,0x2D01,0x2D02,0x2D03,0x2D04,0x2D05,0x2D06,0x2D07,0x2D08,0x2D09,0x2D0A,0x2D0B,0x2D0C,0x2D0D,0x2D0E,0x2D0F,
	0x2D10,0x2D11,0x2D12,0x2D13,0x2D14,0x2D15,0x2D16,0x2D17,0x2D18,0x2D19,0x2D1A,0x2D1B,0x2D1C,0x2D1D,0x2D1E,0x2D1F,
	0x2D20,0x2D21,0x2D22,0x2D23,0x2D24,0x2D25,0};
static const guint16 simple_lower_case_mapping_lowarea_table2 [] = {
	/* ==== 1D00-2000 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x1E01,0,0x1E03,0,0x1E05,0,0x1E07,0,0x1E09,0,0x1E0B,0,0x1E0D,0,0x1E0F,0,
	0x1E11,0,0x1E13,0,0x1E15,0,0x1E17,0,0x1E19,0,0x1E1B,0,0x1E1D,0,0x1E1F,0,
	0x1E21,0,0x1E23,0,0x1E25,0,0x1E27,0,0x1E29,0,0x1E2B,0,0x1E2D,0,0x1E2F,0,
	0x1E31,0,0x1E33,0,0x1E35,0,0x1E37,0,0x1E39,0,0x1E3B,0,0x1E3D,0,0x1E3F,0,
	0x1E41,0,0x1E43,0,0x1E45,0,0x1E47,0,0x1E49,0,0x1E4B,0,0x1E4D,0,0x1E4F,0,
	0x1E51,0,0x1E53,0,0x1E55,0,0x1E57,0,0x1E59,0,0x1E5B,0,0x1E5D,0,0x1E5F,0,
	0x1E61,0,0x1E63,0,0x1E65,0,0x1E67,0,0x1E69,0,0x1E6B,0,0x1E6D,0,0x1E6F,0,
	0x1E71,0,0x1E73,0,0x1E75,0,0x1E77,0,0x1E79,0,0x1E7B,0,0x1E7D,0,0x1E7F,0,
	0x1E81,0,0x1E83,0,0x1E85,0,0x1E87,0,0x1E89,0,0x1E8B,0,0x1E8D,0,0x1E8F,0,
	0x1E91,0,0x1E93,0,0x1E95,0,0,0,0,0,0,0,0,0,0xDF,0,
	0x1EA1,0,0x1EA3,0,0x1EA5,0,0x1EA7,0,0x1EA9,0,0x1EAB,0,0x1EAD,0,0x1EAF,0,
	0x1EB1,0,0x1EB3,0,0x1EB5,0,0x1EB7,0,0x1EB9,0,0x1EBB,0,0x1EBD,0,0x1EBF,0,
	0x1EC1,0,0x1EC3,0,0x1EC5,0,0x1EC7,0,0x1EC9,0,0x1ECB,0,0x1ECD,0,0x1ECF,0,
	0x1ED1,0,0x1ED3,0,0x1ED5,0,0x1ED7,0,0x1ED9,0,0x1EDB,0,0x1EDD,0,0x1EDF,0,
	0x1EE1,0,0x1EE3,0,0x1EE5,0,0x1EE7,0,0x1EE9,0,0x1EEB,0,0x1EED,0,0x1EEF,0,
	0x1EF1,0,0x1EF3,0,0x1EF5,0,0x1EF7,0,0x1EF9,0,0x1EFB,0,0x1EFD,0,0x1EFF,0,
	0,0,0,0,0,0,0,0,0x1F00,0x1F01,0x1F02,0x1F03,0x1F04,0x1F05,0x1F06,0x1F07,
	0,0,0,0,0,0,0,0,0x1F10,0x1F11,0x1F12,0x1F13,0x1F14,0x1F15,0,0,
0,0,0,0,0,0,0,0,0x1F20,0x1F21,0x1F22,0x1F23,0x1F24,0x1F25,0x1F26,0x1F27,
	0,0,0,0,0,0,0,0,0x1F30,0x1F31,0x1F32,0x1F33,0x1F34,0x1F35,0x1F36,0x1F37,
	0,0,0,0,0,0,0,0,0x1F40,0x1F41,0x1F42,0x1F43,0x1F44,0x1F45,0,0,
0,0,0,0,0,0,0,0,0,0x1F51,0,0x1F53,0,0x1F55,0,0x1F57,
	0,0,0,0,0,0,0,0,0x1F60,0x1F61,0x1F62,0x1F63,0x1F64,0x1F65,0x1F66,0x1F67,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0x1F80,0x1F81,0x1F82,0x1F83,0x1F84,0x1F85,0x1F86,0x1F87,
	0,0,0,0,0,0,0,0,0x1F90,0x1F91,0x1F92,0x1F93,0x1F94,0x1F95,0x1F96,0x1F97,
	0,0,0,0,0,0,0,0,0x1FA0,0x1FA1,0x1FA2,0x1FA3,0x1FA4,0x1FA5,0x1FA6,0x1FA7,
	0,0,0,0,0,0,0,0,0x1FB0,0x1FB1,0x1F70,0x1F71,0x1FB3,0,0,0,
	0,0,0,0,0,0,0,0,0x1F72,0x1F73,0x1F74,0x1F75,0x1FC3,0,0,0,
	0,0,0,0,0,0,0,0,0x1FD0,0x1FD1,0x1F76,0x1F77,0,0,0,0,
	0,0,0,0,0,0,0,0,0x1FE0,0x1FE1,0x1F7A,0x1F7B,0x1FE5,0,0,0,
	0,0,0,0,0,0,0,0,0x1F78,0x1F79,0x1F7C,0x1F7D,0x1FF3,0,0,0};
static const guint16 simple_lower_case_mapping_lowarea_table3 [] = {
	/* ==== 2100-21C0 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0x3C9,0,0,0,0x6B,0xE5,0,0,0,0,
	0,0,0x214E,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0x2170,0x2171,0x2172,0x2173,0x2174,0x2175,0x2176,0x2177,0x2178,0x2179,0x217A,0x217B,0x217C,0x217D,0x217E,0x217F,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0x2184,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 simple_lower_case_mapping_lowarea_table4 [] = {
	/* ==== 2480-2500 ==== */
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0x24D0,0x24D1,0x24D2,0x24D3,0x24D4,0x24D5,0x24D6,0x24D7,0x24D8,0x24D9,
	0x24DA,0x24DB,0x24DC,0x24DD,0x24DE,0x24DF,0x24E0,0x24E1,0x24E2,0x24E3,0x24E4,0x24E5,0x24E6,0x24E7,0x24E8,0x24E9,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 simple_lower_case_mapping_lowarea_table5 [] = {
	/* ==== 2C00-2D80 ==== */
	0x2C30,0x2C31,0x2C32,0x2C33,0x2C34,0x2C35,0x2C36,0x2C37,0x2C38,0x2C39,0x2C3A,0x2C3B,0x2C3C,0x2C3D,0x2C3E,0x2C3F,
	0x2C40,0x2C41,0x2C42,0x2C43,0x2C44,0x2C45,0x2C46,0x2C47,0x2C48,0x2C49,0x2C4A,0x2C4B,0x2C4C,0x2C4D,0x2C4E,0x2C4F,
	0x2C50,0x2C51,0x2C52,0x2C53,0x2C54,0x2C55,0x2C56,0x2C57,0x2C58,0x2C59,0x2C5A,0x2C5B,0x2C5C,0x2C5D,0x2C5E,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0x2C61,0,0x26B,0x1D7D,0x27D,0,0,0x2C68,0,0x2C6A,0,0x2C6C,0,0x251,0x271,0x250,
	0,0,0x2C73,0,0,0x2C76,0,0,0,0,0,0,0,0,0,0,
0x2C81,0,0x2C83,0,0x2C85,0,0x2C87,0,0x2C89,0,0x2C8B,0,0x2C8D,0,0x2C8F,0,
	0x2C91,0,0x2C93,0,0x2C95,0,0x2C97,0,0x2C99,0,0x2C9B,0,0x2C9D,0,0x2C9F,0,
	0x2CA1,0,0x2CA3,0,0x2CA5,0,0x2CA7,0,0x2CA9,0,0x2CAB,0,0x2CAD,0,0x2CAF,0,
	0x2CB1,0,0x2CB3,0,0x2CB5,0,0x2CB7,0,0x2CB9,0,0x2CBB,0,0x2CBD,0,0x2CBF,0,
	0x2CC1,0,0x2CC3,0,0x2CC5,0,0x2CC7,0,0x2CC9,0,0x2CCB,0,0x2CCD,0,0x2CCF,0,
	0x2CD1,0,0x2CD3,0,0x2CD5,0,0x2CD7,0,0x2CD9,0,0x2CDB,0,0x2CDD,0,0x2CDF,0,
	0x2CE1,0,0x2CE3,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 simple_lower_case_mapping_lowarea_table6 [] = {
	/* ==== A640-A7C0 ==== */
	0xA641,0,0xA643,0,0xA645,0,0xA647,0,0xA649,0,0xA64B,0,0xA64D,0,0xA64F,0,
	0xA651,0,0xA653,0,0xA655,0,0xA657,0,0xA659,0,0xA65B,0,0xA65D,0,0xA65F,0,
	0,0,0xA663,0,0xA665,0,0xA667,0,0xA669,0,0xA66B,0,0xA66D,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0xA681,0,0xA683,0,0xA685,0,0xA687,0,0xA689,0,0xA68B,0,0xA68D,0,0xA68F,0,
	0xA691,0,0xA693,0,0xA695,0,0xA697,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0xA723,0,0xA725,0,0xA727,0,0xA729,0,0xA72B,0,0xA72D,0,0xA72F,0,
	0,0,0xA733,0,0xA735,0,0xA737,0,0xA739,0,0xA73B,0,0xA73D,0,0xA73F,0,
	0xA741,0,0xA743,0,0xA745,0,0xA747,0,0xA749,0,0xA74B,0,0xA74D,0,0xA74F,0,
	0xA751,0,0xA753,0,0xA755,0,0xA757,0,0xA759,0,0xA75B,0,0xA75D,0,0xA75F,0,
	0xA761,0,0xA763,0,0xA765,0,0xA767,0,0xA769,0,0xA76B,0,0xA76D,0,0xA76F,0,
	0,0,0,0,0,0,0,0,0,0xA77A,0,0xA77C,0,0x1D79,0xA77F,0,
	0xA781,0,0xA783,0,0xA785,0,0xA787,0,0,0,0,0xA78C,0,0};
static const guint16 simple_lower_case_mapping_lowarea_table7 [] = {
	/* ==== FF20-FF80 ==== */
	0,0xFF41,0xFF42,0xFF43,0xFF44,0xFF45,0xFF46,0xFF47,0xFF48,0xFF49,0xFF4A,0xFF4B,0xFF4C,0xFF4D,0xFF4E,0xFF4F,
	0xFF50,0xFF51,0xFF52,0xFF53,0xFF54,0xFF55,0xFF56,0xFF57,0xFF58,0xFF59,0xFF5A,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint16 *simple_lower_case_mapping_lowarea [] = {
	simple_lower_case_mapping_lowarea_table0,
	simple_lower_case_mapping_lowarea_table1,
	simple_lower_case_mapping_lowarea_table2,
	simple_lower_case_mapping_lowarea_table3,
	simple_lower_case_mapping_lowarea_table4,
	simple_lower_case_mapping_lowarea_table5,
	simple_lower_case_mapping_lowarea_table6,
	simple_lower_case_mapping_lowarea_table7};
static const int simple_lower_case_mapping_lowarea_table_count = 8;

static const guint32 simple_lower_case_mapping_higharea_table0 [] = {
	/* ==== 10400-10480 ==== */
	0x10428,0x10429,0x1042A,0x1042B,0x1042C,0x1042D,0x1042E,0x1042F,0x10430,0x10431,0x10432,0x10433,0x10434,0x10435,0x10436,0x10437,
	0x10438,0x10439,0x1043A,0x1043B,0x1043C,0x1043D,0x1043E,0x1043F,0x10440,0x10441,0x10442,0x10443,0x10444,0x10445,0x10446,0x10447,
	0x10448,0x10449,0x1044A,0x1044B,0x1044C,0x1044D,0x1044E,0x1044F,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
	0};
static const guint32 *simple_lower_case_mapping_higharea [] = {
	simple_lower_case_mapping_higharea_table0};


static const SimpleTitlecaseMapping simple_titlecase_mapping [] = {
	{0x0001C4, 0x000000, 0x0001C5},
	{0x0001C5, 0x0001C4, 0x0001C5},
	{0x0001C6, 0x0001C4, 0x0001C5},
	{0x0001C7, 0x000000, 0x0001C8},
	{0x0001C8, 0x0001C7, 0x0001C8},
	{0x0001C9, 0x0001C7, 0x0001C8},
	{0x0001CA, 0x000000, 0x0001CB},
	{0x0001CB, 0x0001CA, 0x0001CB},
	{0x0001CC, 0x0001CA, 0x0001CB},
	{0x0001F1, 0x000000, 0x0001F2},
	{0x0001F2, 0x0001F1, 0x0001F2},
	{0x0001F3, 0x0001F1, 0x0001F2}
};
static const guint8 simple_titlecase_mapping_count = 12;

#endif

