/**
 * \file
 * Copyright 2016 Microsoft
 * Licensed under the MIT license. See LICENSE file in the project root for full license information.
 */
#ifndef __MONO_METADATA_MARSHAL_WINDOWS_INTERNALS_H__
#define __MONO_METADATA_MARSHAL_WINDOWS_INTERNALS_H__

#include <config.h>
#include <glib.h>

#ifdef HOST_WIN32
#include "mono/metadata/marshal.h"
#include "mono/metadata/marshal-internals.h"
#include "mono/metadata/exception.h"
#endif /* HOST_WIN32 */

#endif /* __MONO_METADATA_MARSHAL_WINDOWS_INTERNALS_H__ */
