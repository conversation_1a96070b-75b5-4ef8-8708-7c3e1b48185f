/**
 * \file
 * System.Security.Cryptography.RNGCryptoServiceProvider support
 *
 * Author:
 *      <PERSON> (<EMAIL>)
 *	<PERSON><PERSON><PERSON> (<EMAIL>)
 *
 * (C) 2001 Ximian, Inc.
 * Copyright (C) 2004-2005 Novell, Inc (http://www.novell.com)
 * Licensed under the MIT license. See LICENSE file in the project root for full license information.
 */

#ifndef _MONO_METADATA_RAND_H_
#define _MONO_METADATA_RAND_H_

#include <glib.h>
#include <mono/metadata/object.h>
#include "mono/utils/mono-compiler.h"
#include <mono/metadata/icalls.h>

#endif
