TABLEDEF(<PERSON><PERSON><PERSON><PERSON>TABLE_MODULE, "Module")
TABLEDEF(<PERSON>ONO_TABLE_TYPEREF, "TypeRef")
TABLEDEF(MONO_TABLE_TYPEDEF, "TypeDef")
TABLEDEF(MONO_TABLE_FIELD_POINTER, "FieldPtr")
TABLEDEF(MONO_TABLE_FIELD, "Field")
TABLEDEF(MONO_TABLE_METHOD_POINTER, "MethodPtr")
TABLEDEF(MONO_TABLE_METHOD, "Method")
TABLEDEF(MONO_TABLE_PARAM_POINTER, "ParamPtr")
TABLEDEF(MONO_TABLE_PARAM, "Param")
TABLEDEF(MONO_TABLE_INTERFACEIMPL, "InterfaceImpl")
TABLEDEF(MONO_TABLE_MEMBERREF, "MemberRef") /* 0xa */
TABLEDEF(<PERSON><PERSON><PERSON>_<PERSON><PERSON>LE_CONSTANT, "Constant")
TABLEDEF(<PERSON><PERSON><PERSON>_TABLE_CUSTOMATTRIBUTE, "CustomAttribute")
TABLEDEF(M<PERSON><PERSON>_TABLE_FIELDMARSHAL, "<PERSON>Marshal")
TABLEDEF(MONO_TABLE_DECLSECURITY, "DeclSecurity")
TABLEDEF(MONO_TABLE_CLASSLAYOUT, "ClassLayout")
TABLEDEF(MONO_TABLE_FIELDLAYOUT, "FieldLayoutt") /* 0x10 */
TABLEDEF(MONO_TABLE_STANDALONESIG, "StandaloneSig")
TABLEDEF(MONO_TABLE_EVENTMAP, "EventMap")
TABLEDEF(MONO_TABLE_EVENT_POINTER, "EventPtr")
TABLEDEF(MONO_TABLE_EVENT, "Event")
TABLEDEF(MONO_TABLE_PROPERTYMAP, "PropertyMap")
TABLEDEF(MONO_TABLE_PROPERTY_POINTER, "PropertyPtr")
TABLEDEF(MONO_TABLE_PROPERTY, "Property")
TABLEDEF(MONO_TABLE_METHODSEMANTICS, "MethodSemantics")
TABLEDEF(MONO_TABLE_METHODIMPL, "MethodImpl")
TABLEDEF(MONO_TABLE_MODULEREF, "Moduleref") /* 0x1a */
TABLEDEF(MONO_TABLE_TYPESPEC, "TypeSpec")
TABLEDEF(MONO_TABLE_IMPLMAP, "ImplMap")
TABLEDEF(MONO_TABLE_FIELDRVA, "FieldRVA")
TABLEDEF(MONO_TABLE_ENCLOG, "ENCLog")
TABLEDEF(MONO_TABLE_ENCMAP, "ENCMap")
TABLEDEF(MONO_TABLE_ASSEMBLY, "Assembly") /* 0x20 */
TABLEDEF(MONO_TABLE_ASSEMBLYPROCESSOR, "AssemblyProcessor")
TABLEDEF(MONO_TABLE_ASSEMBLYOS, "AssemblyOS")
TABLEDEF(MONO_TABLE_ASSEMBLYREF, "AssemblyRef")
TABLEDEF(MONO_TABLE_ASSEMBLYREFPROCESSOR, "AssemblyRefProcessor")
TABLEDEF(MONO_TABLE_ASSEMBLYREFOS, "AssemblyRefOS")
TABLEDEF(MONO_TABLE_FILE, "File")
TABLEDEF(MONO_TABLE_EXPORTEDTYPE, "ExportedType")
TABLEDEF(MONO_TABLE_MANIFESTRESOURCE, "ManifestResource")
TABLEDEF(MONO_TABLE_NESTEDCLASS, "NestedClass")
TABLEDEF(MONO_TABLE_GENERICPARAM, "GenericParam") /* 0x2a */
TABLEDEF(MONO_TABLE_METHODSPEC, "MethodSpec")
TABLEDEF(MONO_TABLE_GENERICPARAMCONSTRAINT, "GenericParamConstraint")

TABLEDEF(MONO_TABLE_UNUSED8, "Unused8")
TABLEDEF(MONO_TABLE_UNUSED9, "Unused9")
TABLEDEF(MONO_TABLE_UNUSED10, "Unused10")

/* Portable PDB tables */
TABLEDEF(MONO_TABLE_DOCUMENT, "Document")
TABLEDEF(MONO_TABLE_METHODBODY, "Methodbody")
TABLEDEF(MONO_TABLE_LOCALSCOPE, "LocalScope")
TABLEDEF(MONO_TABLE_LOCALVARIABLE, "LocalVariable")
TABLEDEF(MONO_TABLE_LOCALCONSTANT, "LocalConstant")
TABLEDEF(MONO_TABLE_IMPORTSCOPE, "ImportScope")
TABLEDEF(MONO_TABLE_STATEMACHINEMETHOD, "StateMachineMethod")
TABLEDEF(MONO_TABLE_CUSTOMDEBUGINFORMATION, "CustomDebugInformation")
