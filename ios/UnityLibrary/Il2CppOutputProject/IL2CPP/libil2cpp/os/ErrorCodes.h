#pragma once

#include <stdint.h>

namespace il2cpp
{
namespace os
{
    enum ErrorCode
    {
        kErrorCodeSuccess = 0,
        kErrorInvalidFunction = 1,
        kErrorCodeFileNotFound = 2,
        kErrorCodePathNotFound = 3,
        kErrorCodeTooManyOpenFiles = 4,
        kErrorCodeAccessDenied = 5,
        kErrorCodeInvalidHandle = 6,
        kErrorArenaTrashed = 7,
        kErrorNotEnoughMemory = 8,
        kErrorInvalidBlock = 9,
        kErrorBadEnvironment = 10,
        kErrorBadFormat = 11,
        kErrorInvalidAccess = 12,
        kErrorInvalidData = 13,
        kErrorOutofmemory = 14,
        kErrorCodeInvalidDrive = 15,
        kErrorCurrentDirectory = 16,
        kErrorCodeNotSameDevice = 17,
        kErrorCodeNoMoreFiles = 18,
        kErrorWriteProtect = 19,
        kErrorBadUnit = 20,
        kErrorNotReady = 21,
        kErrorBadCommand = 22,
        kErrorCrc = 23,
        kErrorBadLength = 24,
        kErrorSeek = 25,
        kErrorNotDosDisk = 26,
        kErrorSectorNotFound = 27,
        kErrorOutOfPaper = 28,
        kErrorCodeWriteFault = 29,
        kErrorCodeReadFault = 30,
        kErrorCodeGenFailure = 31,
        kErrorCodeSharingViolation = 32,
        kErrorCodeLockViolation = 33,
        kErrorWrongDisk = 34,
        kErrorSharingBufferExceeded = 36,
        kErrorHandleEof = 38,
        kErrorCodeHandleDiskFull = 39,
        kErrorNotSupported = 50,
        kErrorRemNotList = 51,
        kErrorDupName = 52,
        kErrorBadNetpath = 53,
        kErrorNetworkBusy = 54,
        kErrorDevNotExist = 55,
        kErrorTooManyCmds = 56,
        kErrorAdapHdwErr = 57,
        kErrorBadNetResp = 58,
        kErrorUnexpNetErr = 59,
        kErrorBadRemAdap = 60,
        kErrorPrintqFull = 61,
        kErrorNoSpoolSpace = 62,
        kErrorPrintCancelled = 63,
        kErrorNetnameDeleted = 64,
        kErrorNetworkAccessDenied = 65,
        kErrorBadDevType = 66,
        kErrorBadNetName = 67,
        kErrorTooManyNames = 68,
        kErrorTooManySess = 69,
        kErrorSharingPaused = 70,
        kErrorReqNotAccep = 71,
        kErrorRedirPaused = 72,
        kErrorCodeFileExists = 80,
        kErrorCodeCannotMake = 82,
        kErrorFailI24 = 83,
        kErrorOutOfStructures = 84,
        kErrorAlreadyAssigned = 85,
        kErrorInvalidPassword = 86,
        kErrorCodeInvalidParameter = 87,
        kErrorNetWriteFault = 88,
        kErrorNoProcSlots = 89,
        kErrorTooManySemaphores = 100,
        kErrorExclSemAlreadyOwned = 101,
        kErrorSemIsSet = 102,
        kErrorTooManySemRequests = 103,
        kErrorInvalidAtInterruptTime = 104,
        kErrorSemOwnerDied = 105,
        kErrorSemUserLimit = 106,
        kErrorDiskChange = 107,
        kErrorDriveLocked = 108,
        kErrorCodeBrokenPipe = 109,
        kErrorOpenFailed = 110,
        kErrorBufferOverflow = 111,
        kErrorDiskFull = 112,
        kErrorNoMoreSearchHandles = 113,
        kErrorInvalidTargetHandle = 114,
        kErrorInvalidCategory = 117,
        kErrorInvalidVerifySwitch = 118,
        kErrorBadDriverLevel = 119,
        kErrorCallNotImplemented = 120,
        kErrorSemTimeout = 121,
        kErrorInsufficientBuffer = 122,
        kErrorCodeInvalidName = 123,
        kErrorInvalidLevel = 124,
        kErrorNoVolumeLabel = 125,
        kErrorModNotFound = 126,
        kErrorProcNotFound = 127,
        kErrorWaitNoChildren = 128,
        kErrorChildNotComplete = 129,
        kErrorDirectAccessHandle = 130,
        kErrorNegativeSeek = 131,
        kErrorSeekOnDevice = 132,
        kErrorIsJoinTarget = 133,
        kErrorIsJoined = 134,
        kErrorIsSubsted = 135,
        kErrorNotJoined = 136,
        kErrorNotSubsted = 137,
        kErrorJoinToJoin = 138,
        kErrorSubstToSubst = 139,
        kErrorJoinToSubst = 140,
        kErrorSubstToJoin = 141,
        kErrorBusyDrive = 142,
        kErrorSameDrive = 143,
        kErrorDirNotRoot = 144,
        kErrorCodeDirNotEmpty = 145,
        kErrorIsSubstPath = 146,
        kErrorIsJoinPath = 147,
        kErrorPathBusy = 148,
        kErrorIsSubstTarget = 149,
        kErrorSystemTrace = 150,
        kErrorInvalidEventCount = 151,
        kErrorTooManyMuxwaiters = 152,
        kErrorInvalidListFormat = 153,
        kErrorLabelTooLong = 154,
        kErrorTooManyTcbs = 155,
        kErrorSignalRefused = 156,
        kErrorDiscarded = 157,
        kErrorNotLocked = 158,
        kErrorBadThreadidAddr = 159,
        kErrorBadArguments = 160,
        kErrorBadPathname = 161,
        kErrorSignalPending = 162,
        kErrorMaxThrdsReached = 164,
        kErrorLockFailed = 167,
        kErrorBusy = 170,
        kErrorCancelViolation = 173,
        kErrorAtomicLocksNotSupported = 174,
        kErrorInvalidSegmentNumber = 180,
        kErrorInvalidOrdinal = 182,
        kErrorCodeAlreadyExists = 183,
        kErrorInvalidFlagNumber = 186,
        kErrorSemNotFound = 187,
        kErrorInvalidStartingCodeseg = 188,
        kErrorInvalidStackseg = 189,
        kErrorInvalidModuletype = 190,
        kErrorInvalidExeSignature = 191,
        kErrorExeMarkedInvalid = 192,
        kErrorBadExeFormat = 193,
        kErrorIteratedDataExceeds64k = 194,
        kErrorInvalidMinallocsize = 195,
        kErrorDynlinkFromInvalidRing = 196,
        kErrorIoplNotEnabled = 197,
        kErrorInvalidSegdpl = 198,
        kErrorAutodatasegExceeds64k = 199,
        kErrorRing2segMustBeMovable = 200,
        kErrorRelocChainXeedsSeglim = 201,
        kErrorInfloopInRelocChain = 202,
        kErrorEnvvarNotFound = 203,
        kErrorNoSignalSent = 205,
        kErrorCodeFileNameExcedRange = 206,
        kErrorRing2StackInUse = 207,
        kErrorMetaExpansionTooLong = 208,
        kErrorInvalidSignalNumber = 209,
        kErrorThread1Inactive = 210,
        kErrorLocked = 212,
        kErrorTooManyModules = 214,
        kErrorNestingNotAllowed = 215,
        kErrorExeMachineTypeMismatch = 216,
        kErrorBadPipe = 230,
        kErrorPipeBusy = 231,
        kErrorNoData = 232,
        kErrorPipeNotConnected = 233,
        kErrorMoreData = 234,
        kErrorVcDisconnected = 240,
        kErrorInvalidEaName = 254,
        kErrorEaListInconsistent = 255,
        kWaitTimeout = 258,
        kErrorNoMoreItems = 259,
        kErrorCannotCopy = 266,
        kErrorDirectory = 267,
        kErrorEasDidntFit = 275,
        kErrorEaFileCorrupt = 276,
        kErrorEaTableFull = 277,
        kErrorInvalidEaHandle = 278,
        kErrorEasNotSupported = 282,
        kErrorNotOwner = 288,
        kErrorTooManyPosts = 298,
        kErrorPartialCopy = 299,
        kErrorOplockNotGranted = 300,
        kErrorInvalidOplockProtocol = 301,
        kErrorDiskTooFragmented = 302,
        kErrorDeletePending = 303,
        kErrorMrMidNotFound = 317,
        kErrorInvalidAddress = 487,
        kErrorArithmeticOverflow = 534,
        kErrorPipeConnected = 535,
        kErrorPipeListening = 536,
        kErrorEaAccessDenied = 994,
        kErrorOperationAborted = 995,
        kErrorIoIncomplete = 996,
        kErrorIoPending = 997,
        kErrorNoaccess = 998,
        kErrorSwaperror = 999,
        kErrorStackOverflow = 1001,
        kErrorInvalidMessage = 1002,
        kErrorCanNotComplete = 1003,
        kErrorInvalidFlags = 1004,
        kErrorUnrecognizedVolume = 1005,
        kErrorFileInvalid = 1006,
        kErrorFullscreenMode = 1007,
        kErrorNoToken = 1008,
        kErrorBaddb = 1009,
        kErrorBadkey = 1010,
        kErrorCantopen = 1011,
        kErrorCantread = 1012,
        kErrorCantwrite = 1013,
        kErrorRegistryRecovered = 1014,
        kErrorRegistryCorrupt = 1015,
        kErrorRegistryIoFailed = 1016,
        kErrorNotRegistryFile = 1017,
        kErrorKeyDeleted = 1018,
        kErrorNoLogSpace = 1019,
        kErrorKeyHasChildren = 1020,
        kErrorChildMustBeVolatile = 1021,
        kErrorNotifyEnumDir = 1022,
        kErrorDependentServicesRunning = 1051,
        kErrorInvalidServiceControl = 1052,
        kErrorServiceRequestTimeout = 1053,
        kErrorServiceNoThread = 1054,
        kErrorServiceDatabaseLocked = 1055,
        kErrorServiceAlreadyRunning = 1056,
        kErrorInvalidServiceAccount = 1057,
        kErrorServiceDisabled = 1058,
        kErrorCircularDependency = 1059,
        kErrorServiceDoesNotExist = 1060,
        kErrorServiceCannotAcceptCtrl = 1061,
        kErrorServiceNotActive = 1062,
        kErrorFailedServiceControllerConnect = 1063,
        kErrorExceptionInService = 1064,
        kErrorDatabaseDoesNotExist = 1065,
        kErrorServiceSpecificError = 1066,
        kErrorProcessAborted = 1067,
        kErrorServiceDependencyFail = 1068,
        kErrorServiceLogonFailed = 1069,
        kErrorServiceStartHang = 1070,
        kErrorInvalidServiceLock = 1071,
        kErrorServiceMarkedForDelete = 1072,
        kErrorServiceExists = 1073,
        kErrorAlreadyRunningLkg = 1074,
        kErrorServiceDependencyDeleted = 1075,
        kErrorBootAlreadyAccepted = 1076,
        kErrorServiceNeverStarted = 1077,
        kErrorDuplicateServiceName = 1078,
        kErrorDifferentServiceAccount = 1079,
        kErrorCannotDetectDriverFailure = 1080,
        kErrorCannotDetectProcessAbort = 1081,
        kErrorNoRecoveryProgram = 1082,
        kErrorServiceNotInExe = 1083,
        kErrorNotSafebootService = 1084,
        kErrorEndOfMedia = 1100,
        kErrorFilemarkDetected = 1101,
        kErrorBeginningOfMedia = 1102,
        kErrorSetmarkDetected = 1103,
        kErrorNoDataDetected = 1104,
        kErrorPartitionFailure = 1105,
        kErrorInvalidBlockLength = 1106,
        kErrorDeviceNotPartitioned = 1107,
        kErrorUnableToLockMedia = 1108,
        kErrorUnableToUnloadMedia = 1109,
        kErrorMediaChanged = 1110,
        kErrorBusReset = 1111,
        kErrorNoMediaInDrive = 1112,
        kErrorNoUnicodeTranslation = 1113,
        kErrorDllInitFailed = 1114,
        kErrorShutdownInProgress = 1115,
        kErrorNoShutdownInProgress = 1116,
        kErrorIoDevice = 1117,
        kErrorSerialNoDevice = 1118,
        kErrorIrqBusy = 1119,
        kErrorMoreWrites = 1120,
        kErrorCounterTimeout = 1121,
        kErrorFloppyIdMarkNotFound = 1122,
        kErrorFloppyWrongCylinder = 1123,
        kErrorFloppyUnknownError = 1124,
        kErrorFloppyBadRegisters = 1125,
        kErrorDiskRecalibrateFailed = 1126,
        kErrorDiskOperationFailed = 1127,
        kErrorDiskResetFailed = 1128,
        kErrorEomOverflow = 1129,
        kErrorNotEnoughServerMemory = 1130,
        kErrorPossibleDeadlock = 1131,
        kErrorMappedAlignment = 1132,
        kErrorSetPowerStateVetoed = 1140,
        kErrorSetPowerStateFailed = 1141,
        kErrorTooManyLinks = 1142,
        kErrorOldWinVersion = 1150,
        kErrorAppWrongOs = 1151,
        kErrorSingleInstanceApp = 1152,
        kErrorRmodeApp = 1153,
        kErrorInvalidDll = 1154,
        kErrorNoAssociation = 1155,
        kErrorDdeFail = 1156,
        kErrorDllNotFound = 1157,
        kErrorNoMoreUserHandles = 1158,
        kErrorMessageSyncOnly = 1159,
        kErrorSourceElementEmpty = 1160,
        kErrorDestinationElementFull = 1161,
        kErrorIllegalElementAddress = 1162,
        kErrorMagazineNotPresent = 1163,
        kErrorDeviceReinitializationNeeded = 1164,
        kErrorDeviceRequiresCleaning = 1165,
        kErrorDeviceDoorOpen = 1166,
        kErrorDeviceNotConnected = 1167,
        kErrorNotFound = 1168,
        kErrorNoMatch = 1169,
        kErrorSetNotFound = 1170,
        kErrorPointNotFound = 1171,
        kErrorNoTrackingService = 1172,
        kErrorNoVolumeId = 1173,
        kErrorUnableToRemoveReplaced = 1175,
        kErrorUnableToMoveReplacement = 1176,
        kErrorUnableToMoveReplacement2 = 1177,
        kErrorJournalDeleteInProgress = 1178,
        kErrorJournalNotActive = 1179,
        kErrorPotentialFileFound = 1180,
        kErrorJournalEntryDeleted = 1181,
        kErrorBadDevice = 1200,
        kErrorConnectionUnavail = 1201,
        kErrorDeviceAlreadyRemembered = 1202,
        kErrorNoNetOrBadPath = 1203,
        kErrorBadProvider = 1204,
        kErrorCannotOpenProfile = 1205,
        kErrorBadProfile = 1206,
        kErrorNotContainer = 1207,
        kErrorExtendedError = 1208,
        kErrorInvalidGroupname = 1209,
        kErrorInvalidComputername = 1210,
        kErrorInvalidEventname = 1211,
        kErrorInvalidDomainname = 1212,
        kErrorInvalidServicename = 1213,
        kErrorInvalidNetname = 1214,
        kErrorInvalidSharename = 1215,
        kErrorInvalidPasswordname = 1216,
        kErrorInvalidMessagename = 1217,
        kErrorInvalidMessagedest = 1218,
        kErrorSessionCredentialConflict = 1219,
        kErrorRemoteSessionLimitExceeded = 1220,
        kErrorDupDomainname = 1221,
        kErrorNoNetwork = 1222,
        kErrorCancelled = 1223,
        kErrorUserMappedFile = 1224,
        kErrorConnectionRefused = 1225,
        kErrorGracefulDisconnect = 1226,
        kErrorAddressAlreadyAssociated = 1227,
        kErrorAddressNotAssociated = 1228,
        kErrorConnectionInvalid = 1229,
        kErrorConnectionActive = 1230,
        kErrorNetworkUnreachable = 1231,
        kErrorHostUnreachable = 1232,
        kErrorProtocolUnreachable = 1233,
        kErrorPortUnreachable = 1234,
        kErrorRequestAborted = 1235,
        kErrorConnectionAborted = 1236,
        kErrorRetry = 1237,
        kErrorConnectionCountLimit = 1238,
        kErrorLoginTimeRestriction = 1239,
        kErrorLoginWkstaRestriction = 1240,
        kErrorIncorrectAddress = 1241,
        kErrorAlreadyRegistered = 1242,
        kErrorServiceNotFound = 1243,
        kErrorNotAuthenticated = 1244,
        kErrorNotLoggedOn = 1245,
        kErrorContinue = 1246,
        kErrorAlreadyInitialized = 1247,
        kErrorNoMoreDevices = 1248,
        kErrorNoSuchSite = 1249,
        kErrorDomainControllerExists = 1250,
        kErrorOnlyIfConnected = 1251,
        kErrorOverrideNochanges = 1252,
        kErrorBadUserProfile = 1253,
        kErrorNotSupportedOnSbs = 1254,
        kErrorServerShutdownInProgress = 1255,
        kErrorHostDown = 1256,
        kErrorNonAccountSid = 1257,
        kErrorNonDomainSid = 1258,
        kErrorApphelpBlock = 1259,
        kErrorAccessDisabledByPolicy = 1260,
        kErrorRegNatConsumption = 1261,
        kErrorCscshareOffline = 1262,
        kErrorPkinitFailure = 1263,
        kErrorSmartcardSubsystemFailure = 1264,
        kErrorDowngradeDetected = 1265,
        kSecESmartcardCertRevoked = 1266,
        kSecEIssuingCaUntrusted = 1267,
        kSecERevocationOfflineC = 1268,
        kSecEPkinitClientFailur = 1269,
        kSecESmartcardCertExpired = 1270,
        kErrorMachineLocked = 1271,
        kErrorCallbackSuppliedInvalidData = 1273,
        kErrorSyncForegroundRefreshRequired = 1274,
        kErrorDriverBlocked = 1275,
        kErrorInvalidImportOfNonDll = 1276,
        kErrorNotAllAssigned = 1300,
        kErrorSomeNotMapped = 1301,
        kErrorNoQuotasForAccount = 1302,
        kErrorLocalUserSessionKey = 1303,
        kErrorNullLmPassword = 1304,
        kErrorUnknownRevision = 1305,
        kErrorRevisionMismatch = 1306,
        kErrorInvalidOwner = 1307,
        kErrorInvalidPrimaryGroup = 1308,
        kErrorNoImpersonationToken = 1309,
        kErrorCantDisableMandatory = 1310,
        kErrorNoLogonServers = 1311,
        kErrorNoSuchLogonSession = 1312,
        kErrorNoSuchPrivilege = 1313,
        kErrorPrivilegeNotHeld = 1314,
        kErrorInvalidAccountName = 1315,
        kErrorUserExists = 1316,
        kErrorNoSuchUser = 1317,
        kErrorGroupExists = 1318,
        kErrorNoSuchGroup = 1319,
        kErrorMemberInGroup = 1320,
        kErrorMemberNotInGroup = 1321,
        kErrorLastAdmin = 1322,
        kErrorWrongPassword = 1323,
        kErrorIllFormedPassword = 1324,
        kErrorPasswordRestriction = 1325,
        kErrorLogonFailure = 1326,
        kErrorAccountRestriction = 1327,
        kErrorInvalidLogonHours = 1328,
        kErrorInvalidWorkstation = 1329,
        kErrorPasswordExpired = 1330,
        kErrorAccountDisabled = 1331,
        kErrorNoneMapped = 1332,
        kErrorTooManyLuidsRequested = 1333,
        kErrorLuidsExhausted = 1334,
        kErrorInvalidSubAuthority = 1335,
        kErrorInvalidAcl = 1336,
        kErrorInvalidSid = 1337,
        kErrorInvalidSecurityDescr = 1338,
        kErrorBadInheritanceAcl = 1340,
        kErrorServerDisabled = 1341,
        kErrorServerNotDisabled = 1342,
        kErrorInvalidIdAuthority = 1343,
        kErrorAllottedSpaceExceeded = 1344,
        kErrorInvalidGroupAttributes = 1345,
        kErrorBadImpersonationLevel = 1346,
        kErrorCantOpenAnonymous = 1347,
        kErrorBadValidationClass = 1348,
        kErrorBadTokenType = 1349,
        kErrorNoSecurityOnObject = 1350,
        kErrorCantAccessDomainInfo = 1351,
        kErrorInvalidServerState = 1352,
        kErrorInvalidDomainState = 1353,
        kErrorInvalidDomainRole = 1354,
        kErrorNoSuchDomain = 1355,
        kErrorDomainExists = 1356,
        kErrorDomainLimitExceeded = 1357,
        kErrorInternalDbCorruption = 1358,
        kErrorInternalError = 1359,
        kErrorGenericNotMapped = 1360,
        kErrorBadDescriptorFormat = 1361,
        kErrorNotLogonProcess = 1362,
        kErrorLogonSessionExists = 1363,
        kErrorNoSuchPackage = 1364,
        kErrorBadLogonSessionState = 1365,
        kErrorLogonSessionCollision = 1366,
        kErrorInvalidLogonType = 1367,
        kErrorCannotImpersonate = 1368,
        kErrorRxactInvalidState = 1369,
        kErrorRxactCommitFailure = 1370,
        kErrorSpecialAccount = 1371,
        kErrorSpecialGroup = 1372,
        kErrorSpecialUser = 1373,
        kErrorMembersPrimaryGroup = 1374,
        kErrorTokenAlreadyInUse = 1375,
        kErrorNoSuchAlias = 1376,
        kErrorMemberNotInAlias = 1377,
        kErrorMemberInAlias = 1378,
        kErrorAliasExists = 1379,
        kErrorLogonNotGranted = 1380,
        kErrorTooManySecrets = 1381,
        kErrorSecretTooLong = 1382,
        kErrorInternalDbError = 1383,
        kErrorTooManyContextIds = 1384,
        kErrorLogonTypeNotGranted = 1385,
        kErrorNtCrossEncryptionRequired = 1386,
        kErrorNoSuchMember = 1387,
        kErrorInvalidMember = 1388,
        kErrorTooManySids = 1389,
        kErrorLmCrossEncryptionRequired = 1390,
        kErrorNoInheritance = 1391,
        kErrorFileCorrupt = 1392,
        kErrorDiskCorrupt = 1393,
        kErrorNoUserSessionKey = 1394,
        kErrorLicenseQuotaExceeded = 1395,
        kErrorWrongTargetName = 1396,
        kErrorMutualAuthFailed = 1397,
        kErrorTimeSkew = 1398,
        kErrorCurrentDomainNotAllowed = 1399,
        kErrorInvalidWindowHandle = 1400,
        kErrorInvalidMenuHandle = 1401,
        kErrorInvalidCursorHandle = 1402,
        kErrorInvalidAccelHandle = 1403,
        kErrorInvalidHookHandle = 1404,
        kErrorInvalidDwpHandle = 1405,
        kErrorTlwWithWschild = 1406,
        kErrorCannotFindWndClass = 1407,
        kErrorWindowOfOtherThread = 1408,
        kErrorHotkeyAlreadyRegistered = 1409,
        kErrorClassAlreadyExists = 1410,
        kErrorClassDoesNotExist = 1411,
        kErrorClassHasWindows = 1412,
        kErrorInvalidIndex = 1413,
        kErrorInvalidIconHandle = 1414,
        kErrorPrivateDialogIndex = 1415,
        kErrorListboxIdNotFound = 1416,
        kErrorNoWildcardCharacters = 1417,
        kErrorClipboardNotOpen = 1418,
        kErrorHotkeyNotRegistered = 1419,
        kErrorWindowNotDialog = 1420,
        kErrorControlIdNotFound = 1421,
        kErrorInvalidComboboxMessage = 1422,
        kErrorWindowNotCombobox = 1423,
        kErrorInvalidEditHeight = 1424,
        kErrorDcNotFound = 1425,
        kErrorInvalidHookFilter = 1426,
        kErrorInvalidFilterProc = 1427,
        kErrorHookNeedsHmod = 1428,
        kErrorGlobalOnlyHook = 1429,
        kErrorJournalHookSet = 1430,
        kErrorHookNotInstalled = 1431,
        kErrorInvalidLbMessage = 1432,
        kErrorSetcountOnBadLb = 1433,
        kErrorLbWithoutTabstops = 1434,
        kErrorDestroyObjectOfOtherThread = 1435,
        kErrorChildWindowMenu = 1436,
        kErrorNoSystemMenu = 1437,
        kErrorInvalidMsgboxStyle = 1438,
        kErrorInvalidSpiValue = 1439,
        kErrorScreenAlreadyLocked = 1440,
        kErrorHwndsHaveDiffParent = 1441,
        kErrorNotChildWindow = 1442,
        kErrorInvalidGwCommand = 1443,
        kErrorInvalidThreadId = 1444,
        kErrorNonMdichildWindow = 1445,
        kErrorPopupAlreadyActive = 1446,
        kErrorNoScrollbars = 1447,
        kErrorInvalidScrollbarRange = 1448,
        kErrorInvalidShowwinCommand = 1449,
        kErrorNoSystemResources = 1450,
        kErrorNonpagedSystemResources = 1451,
        kErrorPagedSystemResources = 1452,
        kErrorWorkingSetQuota = 1453,
        kErrorPagefileQuota = 1454,
        kErrorCommitmentLimit = 1455,
        kErrorMenuItemNotFound = 1456,
        kErrorInvalidKeyboardHandle = 1457,
        kErrorHookTypeNotAllowed = 1458,
        kErrorRequiresInteractiveWindowstation = 1459,
        kErrorTimeout = 1460,
        kErrorInvalidMonitorHandle = 1461,
        kErrorEventlogFileCorrupt = 1500,
        kErrorEventlogCantStart = 1501,
        kErrorLogFileFull = 1502,
        kErrorEventlogFileChanged = 1503,
        kErrorInstallServiceFailure = 1601,
        kErrorInstallUserexit = 1602,
        kErrorInstallFailure = 1603,
        kErrorInstallSuspend = 1604,
        kErrorUnknownProduct = 1605,
        kErrorUnknownFeature = 1606,
        kErrorUnknownComponent = 1607,
        kErrorUnknownProperty = 1608,
        kErrorInvalidHandleState = 1609,
        kErrorBadConfiguration = 1610,
        kErrorIndexAbsent = 1611,
        kErrorInstallSourceAbsent = 1612,
        kErrorInstallPackageVersion = 1613,
        kErrorProductUninstalled = 1614,
        kErrorBadQuerySyntax = 1615,
        kErrorInvalidField = 1616,
        kErrorDeviceRemoved = 1617,
        kErrorInstallAlreadyRunning = 1618,
        kErrorInstallPackageOpenFailed = 1619,
        kErrorInstallPackageInvalid = 1620,
        kErrorInstallUiFailure = 1621,
        kErrorInstallLogFailure = 1622,
        kErrorInstallLanguageUnsupported = 1623,
        kErrorInstallTransformFailure = 1624,
        kErrorInstallPackageRejected = 1625,
        kErrorFunctionNotCalled = 1626,
        kErrorFunctionFailed = 1627,
        kErrorInvalidTable = 1628,
        kErrorDatatypeMismatch = 1629,
        kErrorUnsupportedType = 1630,
        kErrorCreateFailed = 1631,
        kErrorInstallTempUnwritable = 1632,
        kErrorInstallPlatformUnsupported = 1633,
        kErrorInstallNotused = 1634,
        kErrorPatchPackageOpenFailed = 1635,
        kErrorPatchPackageInvalid = 1636,
        kErrorPatchPackageUnsupported = 1637,
        kErrorProductVersion = 1638,
        kErrorInvalidCommandLine = 1639,
        kErrorInstallRemoteDisallowed = 1640,
        kErrorSuccessRebootInitiated = 1641,
        kErrorPatchTargetNotFound = 1642,
        kErrorPatchPackageRejected = 1643,
        kErrorInstallTransformRejected = 1644,
        kRpcSInvalidStringBinding = 1700,
        kRpcSWrongKindOfBinding = 1701,
        kRpcSInvalidBinding = 1702,
        kRpcSProtseqNotSupported = 1703,
        kRpcSInvalidRpcProtseq = 1704,
        kRpcSInvalidStringUuid = 1705,
        kRpcSInvalidEndpointFormat = 1706,
        kRpcSInvalidNetAddr = 1707,
        kRpcSNoEndpointFound = 1708,
        kRpcSInvalidTimeout = 1709,
        kRpcSObjectNotFound = 1710,
        kRpcSAlreadyRegistered = 1711,
        kRpcSTypeAlreadyRegistered = 1712,
        kRpcSAlreadyListening = 1713,
        kRpcSNoProtseqsRegistered = 1714,
        kRpcSNotListening = 1715,
        kRpcSUnknownMgrType = 1716,
        kRpcSUnknownIf = 1717,
        kRpcSNoBindings = 1718,
        kRpcSNoProtseqs = 1719,
        kRpcSCantCreateEndpoint = 1720,
        kRpcSOutOfResources = 1721,
        kRpcSServerUnavailable = 1722,
        kRpcSServerTooBusy = 1723,
        kRpcSInvalidNetworkOptions = 1724,
        kRpcSNoCallActive = 1725,
        kRpcSCallFailed = 1726,
        kRpcSCallFailedDne = 1727,
        kRpcSProtocolError = 1728,
        kRpcSUnsupportedTransSyn = 1730,
        kRpcSUnsupportedType = 1732,
        kRpcSInvalidTag = 1733,
        kRpcSInvalidBound = 1734,
        kRpcSNoEntryName = 1735,
        kRpcSInvalidNameSyntax = 1736,
        kRpcSUnsupportedNameSyntax = 1737,
        kRpcSUuidNoAddress = 1739,
        kRpcSDuplicateEndpoint = 1740,
        kRpcSUnknownAuthnType = 1741,
        kRpcSMaxCallsTooSmall = 1742,
        kRpcSStringTooLong = 1743,
        kRpcSProtseqNotFound = 1744,
        kRpcSProcnumOutOfRange = 1745,
        kRpcSBindingHasNoAuth = 1746,
        kRpcSUnknownAuthnService = 1747,
        kRpcSUnknownAuthnLevel = 1748,
        kRpcSInvalidAuthIdentity = 1749,
        kRpcSUnknownAuthzService = 1750,
        kEptSInvalidEntry = 1751,
        kEptSCantPerformOp = 1752,
        kEptSNotRegistered = 1753,
        kRpcSNothingToExport = 1754,
        kRpcSIncompleteName = 1755,
        kRpcSInvalidVersOption = 1756,
        kRpcSNoMoreMembers = 1757,
        kRpcSNotAllObjsUnexported = 1758,
        kRpcSInterfaceNotFound = 1759,
        kRpcSEntryAlreadyExists = 1760,
        kRpcSEntryNotFound = 1761,
        kRpcSNameServiceUnavailable = 1762,
        kRpcSInvalidNafId = 1763,
        kRpcSCannotSupport = 1764,
        kRpcSNoContextAvailable = 1765,
        kRpcSInternalError = 1766,
        kRpcSZeroDivide = 1767,
        kRpcSAddressError = 1768,
        kRpcSFpDivZero = 1769,
        kRpcSFpUnderflow = 1770,
        kRpcSFpOverflow = 1771,
        kRpcXNoMoreEntries = 1772,
        kRpcXSsCharTransOpenFail = 1773,
        kRpcXSsCharTransShortFile = 1774,
        kRpcXSsInNullContext = 1775,
        kRpcXSsContextDamaged = 1777,
        kRpcXSsHandlesMismatch = 1778,
        kRpcXSsCannotGetCallHandle = 1779,
        kRpcXNullRefPointer = 1780,
        kRpcXEnumValueOutOfRange = 1781,
        kRpcXByteCountTooSmall = 1782,
        kRpcXBadStubData = 1783,
        kErrorInvalidUserBuffer = 1784,
        kErrorUnrecognizedMedia = 1785,
        kErrorNoTrustLsaSecret = 1786,
        kErrorNoTrustSamAccount = 1787,
        kErrorTrustedDomainFailure = 1788,
        kErrorTrustedRelationshipFailure = 1789,
        kErrorTrustFailure = 1790,
        kRpcSCallInProgress = 1791,
        kErrorNetlogonNotStarted = 1792,
        kErrorAccountExpired = 1793,
        kErrorRedirectorHasOpenHandles = 1794,
        kErrorPrinterDriverAlreadyInstalled = 1795,
        kErrorUnknownPort = 1796,
        kErrorUnknownPrinterDriver = 1797,
        kErrorUnknownPrintprocessor = 1798,
        kErrorInvalidSeparatorFile = 1799,
        kErrorInvalidPriority = 1800,
        kErrorInvalidPrinterName = 1801,
        kErrorPrinterAlreadyExists = 1802,
        kErrorInvalidPrinterCommand = 1803,
        kErrorInvalidDatatype = 1804,
        kErrorInvalidEnvironment = 1805,
        kRpcSNoMoreBindings = 1806,
        kErrorNologonInterdomainTrustAccount = 1807,
        kErrorNologonWorkstationTrustAccount = 1808,
        kErrorNologonServerTrustAccount = 1809,
        kErrorDomainTrustInconsistent = 1810,
        kErrorServerHasOpenHandles = 1811,
        kErrorResourceDataNotFound = 1812,
        kErrorResourceTypeNotFound = 1813,
        kErrorResourceNameNotFound = 1814,
        kErrorResourceLangNotFound = 1815,
        kErrorNotEnoughQuota = 1816,
        kRpcSNoInterfaces = 1817,
        kRpcSCallCancelled = 1818,
        kRpcSBindingIncomplete = 1819,
        kRpcSCommFailure = 1820,
        kRpcSUnsupportedAuthnLevel = 1821,
        kRpcSNoPrincName = 1822,
        kRpcSNotRpcError = 1823,
        kRpcSUuidLocalOnly = 1824,
        kRpcSSecPkgError = 1825,
        kRpcSNotCancelled = 1826,
        kRpcXInvalidEsAction = 1827,
        kRpcXWrongEsVersion = 1828,
        kRpcXWrongStubVersion = 1829,
        kRpcXInvalidPipeObject = 1830,
        kRpcXWrongPipeOrder = 1831,
        kRpcXWrongPipeVersion = 1832,
        kRpcSGroupMemberNotFound = 1898,
        kEptSCantCreate = 1899,
        kRpcSInvalidObject = 1900,
        kErrorInvalidTime = 1901,
        kErrorInvalidFormName = 1902,
        kErrorInvalidFormSize = 1903,
        kErrorAlreadyWaiting = 1904,
        kErrorPrinterDeleted = 1905,
        kErrorInvalidPrinterState = 1906,
        kErrorPasswordMustChange = 1907,
        kErrorDomainControllerNotFound = 1908,
        kErrorAccountLockedOut = 1909,
        kOrInvalidOxid = 1910,
        kOrInvalidOid = 1911,
        kOrInvalidSet = 1912,
        kRpcSSendIncomplete = 1913,
        kRpcSInvalidAsyncHandle = 1914,
        kRpcSInvalidAsyncCall = 1915,
        kRpcXPipeClosed = 1916,
        kRpcXPipeDisciplineError = 1917,
        kRpcXPipeEmpty = 1918,
        kErrorNoSitename = 1919,
        kErrorCantAccessFile = 1920,
        kErrorCantResolveFilename = 1921,
        kRpcSEntryTypeMismatch = 1922,
        kRpcSNotAllObjsExported = 1923,
        kRpcSInterfaceNotExported = 1924,
        kRpcSProfileNotAdded = 1925,
        kRpcSPrfEltNotAdded = 1926,
        kRpcSPrfEltNotRemoved = 1927,
        kRpcSGrpEltNotAdded = 1928,
        kRpcSGrpEltNotRemoved = 1929,
        kErrorKmDriverBlocked = 1930,
        kErrorContextExpired = 1931,
        kErrorInvalidPixelFormat = 2000,
        kErrorBadDriver = 2001,
        kErrorInvalidWindowStyle = 2002,
        kErrorMetafileNotSupported = 2003,
        kErrorTransformNotSupported = 2004,
        kErrorClippingNotSupported = 2005,
        kErrorInvalidCmm = 2010,
        kErrorInvalidProfile = 2011,
        kErrorTagNotFound = 2012,
        kErrorTagNotPresent = 2013,
        kErrorDuplicateTag = 2014,
        kErrorProfileNotAssociatedWithDevice = 2015,
        kErrorProfileNotFound = 2016,
        kErrorInvalidColorspace = 2017,
        kErrorIcmNotEnabled = 2018,
        kErrorDeletingIcmXform = 2019,
        kErrorInvalidTransform = 2020,
        kErrorColorspaceMismatch = 2021,
        kErrorInvalidColorindex = 2022,
        kErrorConnectedOtherPassword = 2108,
        kErrorConnectedOtherPasswordDefault = 2109,
        kErrorBadUsername = 2202,
        kErrorNotConnected = 2250,
        kErrorOpenFiles = 2401,
        kErrorActiveConnections = 2402,
        kErrorDeviceInUse = 2404,
        kErrorUnknownPrintMonitor = 3000,
        kErrorPrinterDriverInUse = 3001,
        kErrorSpoolFileNotFound = 3002,
        kErrorSplNoStartdoc = 3003,
        kErrorSplNoAddjob = 3004,
        kErrorPrintProcessorAlreadyInstalled = 3005,
        kErrorPrintMonitorAlreadyInstalled = 3006,
        kErrorInvalidPrintMonitor = 3007,
        kErrorPrintMonitorInUse = 3008,
        kErrorPrinterHasJobsQueued = 3009,
        kErrorSuccessRebootRequired = 3010,
        kErrorSuccessRestartRequired = 3011,
        kErrorPrinterNotFound = 3012,
        kErrorPrinterDriverWarned = 3013,
        kErrorPrinterDriverBlocked = 3014,
        kErrorWinsInternal = 4000,
        kErrorCanNotDelLocalWins = 4001,
        kErrorStaticInit = 4002,
        kErrorIncBackup = 4003,
        kErrorFullBackup = 4004,
        kErrorRecNonExistent = 4005,
        kErrorRplNotAllowed = 4006,
        kErrorDhcpAddressConflict = 4100,
        kErrorWmiGuidNotFound = 4200,
        kErrorWmiInstanceNotFound = 4201,
        kErrorWmiItemidNotFound = 4202,
        kErrorWmiTryAgain = 4203,
        kErrorWmiDpNotFound = 4204,
        kErrorWmiUnresolvedInstanceRef = 4205,
        kErrorWmiAlreadyEnabled = 4206,
        kErrorWmiGuidDisconnected = 4207,
        kErrorWmiServerUnavailable = 4208,
        kErrorWmiDpFailed = 4209,
        kErrorWmiInvalidMof = 4210,
        kErrorWmiInvalidReginfo = 4211,
        kErrorWmiAlreadyDisabled = 4212,
        kErrorWmiReadOnly = 4213,
        kErrorWmiSetFailure = 4214,
        kErrorInvalidMedia = 4300,
        kErrorInvalidLibrary = 4301,
        kErrorInvalidMediaPool = 4302,
        kErrorDriveMediaMismatch = 4303,
        kErrorMediaOffline = 4304,
        kErrorLibraryOffline = 4305,
        kErrorEmpty = 4306,
        kErrorNotEmpty = 4307,
        kErrorMediaUnavailable = 4308,
        kErrorResourceDisabled = 4309,
        kErrorInvalidCleaner = 4310,
        kErrorUnableToClean = 4311,
        kErrorObjectNotFound = 4312,
        kErrorDatabaseFailure = 4313,
        kErrorDatabaseFull = 4314,
        kErrorMediaIncompatible = 4315,
        kErrorResourceNotPresent = 4316,
        kErrorInvalidOperation = 4317,
        kErrorMediaNotAvailable = 4318,
        kErrorDeviceNotAvailable = 4319,
        kErrorRequestRefused = 4320,
        kErrorInvalidDriveObject = 4321,
        kErrorLibraryFull = 4322,
        kErrorMediumNotAccessible = 4323,
        kErrorUnableToLoadMedium = 4324,
        kErrorUnableToInventoryDrive = 4325,
        kErrorUnableToInventorySlot = 4326,
        kErrorUnableToInventoryTransport = 4327,
        kErrorTransportFull = 4328,
        kErrorControllingIeport = 4329,
        kErrorUnableToEjectMountedMedia = 4330,
        kErrorCleanerSlotSet = 4331,
        kErrorCleanerSlotNotSet = 4332,
        kErrorCleanerCartridgeSpent = 4333,
        kErrorUnexpectedOmid = 4334,
        kErrorCantDeleteLastItem = 4335,
        kErrorMessageExceedsMaxSize = 4336,
        kErrorVolumeContainsSysFiles = 4337,
        kErrorIndigenousType = 4338,
        kErrorNoSupportingDrives = 4339,
        kErrorCleanerCartridgeInstalled = 4340,
        kErrorFileOffline = 4350,
        kErrorRemoteStorageNotActive = 4351,
        kErrorRemoteStorageMediaError = 4352,
        kErrorNotAReparsePoint = 4390,
        kErrorReparseAttributeConflict = 4391,
        kErrorInvalidReparseData = 4392,
        kErrorReparseTagInvalid = 4393,
        kErrorReparseTagMismatch = 4394,
        kErrorVolumeNotSisEnabled = 4500,
        kErrorDependentResourceExists = 5001,
        kErrorDependencyNotFound = 5002,
        kErrorDependencyAlreadyExists = 5003,
        kErrorResourceNotOnline = 5004,
        kErrorHostNodeNotAvailable = 5005,
        kErrorResourceNotAvailable = 5006,
        kErrorResourceNotFound = 5007,
        kErrorShutdownCluster = 5008,
        kErrorCantEvictActiveNode = 5009,
        kErrorObjectAlreadyExists = 5010,
        kErrorObjectInList = 5011,
        kErrorGroupNotAvailable = 5012,
        kErrorGroupNotFound = 5013,
        kErrorGroupNotOnline = 5014,
        kErrorHostNodeNotResourceOwner = 5015,
        kErrorHostNodeNotGroupOwner = 5016,
        kErrorResmonCreateFailed = 5017,
        kErrorResmonOnlineFailed = 5018,
        kErrorResourceOnline = 5019,
        kErrorQuorumResource = 5020,
        kErrorNotQuorumCapable = 5021,
        kErrorClusterShuttingDown = 5022,
        kErrorInvalidState = 5023,
        kErrorResourcePropertiesStored = 5024,
        kErrorNotQuorumClass = 5025,
        kErrorCoreResource = 5026,
        kErrorQuorumResourceOnlineFailed = 5027,
        kErrorQuorumlogOpenFailed = 5028,
        kErrorClusterlogCorrupt = 5029,
        kErrorClusterlogRecordExceedsMaxsize = 5030,
        kErrorClusterlogExceedsMaxsize = 5031,
        kErrorClusterlogChkpointNotFound = 5032,
        kErrorClusterlogNotEnoughSpace = 5033,
        kErrorQuorumOwnerAlive = 5034,
        kErrorNetworkNotAvailable = 5035,
        kErrorNodeNotAvailable = 5036,
        kErrorAllNodesNotAvailable = 5037,
        kErrorResourceFailed = 5038,
        kErrorClusterInvalidNode = 5039,
        kErrorClusterNodeExists = 5040,
        kErrorClusterJoinInProgress = 5041,
        kErrorClusterNodeNotFound = 5042,
        kErrorClusterLocalNodeNotFound = 5043,
        kErrorClusterNetworkExists = 5044,
        kErrorClusterNetworkNotFound = 5045,
        kErrorClusterNetinterfaceExists = 5046,
        kErrorClusterNetinterfaceNotFound = 5047,
        kErrorClusterInvalidRequest = 5048,
        kErrorClusterInvalidNetworkProvider = 5049,
        kErrorClusterNodeDown = 5050,
        kErrorClusterNodeUnreachable = 5051,
        kErrorClusterNodeNotMember = 5052,
        kErrorClusterJoinNotInProgress = 5053,
        kErrorClusterInvalidNetwork = 5054,
        kErrorClusterNodeUp = 5056,
        kErrorClusterIpaddrInUse = 5057,
        kErrorClusterNodeNotPaused = 5058,
        kErrorClusterNoSecurityContext = 5059,
        kErrorClusterNetworkNotInternal = 5060,
        kErrorClusterNodeAlreadyUp = 5061,
        kErrorClusterNodeAlreadyDown = 5062,
        kErrorClusterNetworkAlreadyOnline = 5063,
        kErrorClusterNetworkAlreadyOffline = 5064,
        kErrorClusterNodeAlreadyMember = 5065,
        kErrorClusterLastInternalNetwork = 5066,
        kErrorClusterNetworkHasDependents = 5067,
        kErrorInvalidOperationOnQuorum = 5068,
        kErrorDependencyNotAllowed = 5069,
        kErrorClusterNodePaused = 5070,
        kErrorNodeCantHostResource = 5071,
        kErrorClusterNodeNotReady = 5072,
        kErrorClusterNodeShuttingDown = 5073,
        kErrorClusterJoinAborted = 5074,
        kErrorClusterIncompatibleVersions = 5075,
        kErrorClusterMaxnumOfResourcesExceeded = 5076,
        kErrorClusterSystemConfigChanged = 5077,
        kErrorClusterResourceTypeNotFound = 5078,
        kErrorClusterRestypeNotSupported = 5079,
        kErrorClusterResnameNotFound = 5080,
        kErrorClusterNoRpcPackagesRegistered = 5081,
        kErrorClusterOwnerNotInPreflist = 5082,
        kErrorClusterDatabaseSeqmismatch = 5083,
        kErrorResmonInvalidState = 5084,
        kErrorClusterGumNotLocker = 5085,
        kErrorQuorumDiskNotFound = 5086,
        kErrorDatabaseBackupCorrupt = 5087,
        kErrorClusterNodeAlreadyHasDfsRoot = 5088,
        kErrorResourcePropertyUnchangeable = 5089,
        kErrorClusterMembershipInvalidState = 5890,
        kErrorClusterQuorumlogNotFound = 5891,
        kErrorClusterMembershipHalt = 5892,
        kErrorClusterInstanceIdMismatch = 5893,
        kErrorClusterNetworkNotFoundForIp = 5894,
        kErrorClusterPropertyDataTypeMismatch = 5895,
        kErrorClusterEvictWithoutCleanup = 5896,
        kErrorClusterParameterMismatch = 5897,
        kErrorNodeCannotBeClustered = 5898,
        kErrorClusterWrongOsVersion = 5899,
        kErrorClusterCantCreateDupClusterName = 5900,
        kErrorCodeEncryptionFailed = 6000,
        kErrorDecryptionFailed = 6001,
        kErrorFileEncrypted = 6002,
        kErrorNoRecoveryPolicy = 6003,
        kErrorNoEfs = 6004,
        kErrorWrongEfs = 6005,
        kErrorNoUserKeys = 6006,
        kErrorFileNotEncrypted = 6007,
        kErrorNotExportFormat = 6008,
        kErrorFileReadOnly = 6009,
        kErrorDirEfsDisallowed = 6010,
        kErrorEfsServerNotTrusted = 6011,
        kErrorBadRecoveryPolicy = 6012,
        kErrorEfsAlgBlobTooBig = 6013,
        kErrorVolumeNotSupportEfs = 6014,
        kErrorEfsDisabled = 6015,
        kErrorEfsVersionNotSupport = 6016,
        kErrorNoBrowserServersFound = 6118,
        kSchedEServiceNotLocalsystem = 6200,
        kErrorCtxWinstationNameInvalid = 7001,
        kErrorCtxInvalidPd = 7002,
        kErrorCtxPdNotFound = 7003,
        kErrorCtxWdNotFound = 7004,
        kErrorCtxCannotMakeEventlogEntry = 7005,
        kErrorCtxServiceNameCollision = 7006,
        kErrorCtxClosePending = 7007,
        kErrorCtxNoOutbuf = 7008,
        kErrorCtxModemInfNotFound = 7009,
        kErrorCtxInvalidModemname = 7010,
        kErrorCtxModemResponseError = 7011,
        kErrorCtxModemResponseTimeout = 7012,
        kErrorCtxModemResponseNoCarrier = 7013,
        kErrorCtxModemResponseNoDialtone = 7014,
        kErrorCtxModemResponseBusy = 7015,
        kErrorCtxModemResponseVoice = 7016,
        kErrorCtxTdError = 7017,
        kErrorCtxWinstationNotFound = 7022,
        kErrorCtxWinstationAlreadyExists = 7023,
        kErrorCtxWinstationBusy = 7024,
        kErrorCtxBadVideoMode = 7025,
        kErrorCtxGraphicsInvalid = 7035,
        kErrorCtxLogonDisabled = 7037,
        kErrorCtxNotConsole = 7038,
        kErrorCtxClientQueryTimeout = 7040,
        kErrorCtxConsoleDisconnect = 7041,
        kErrorCtxConsoleConnect = 7042,
        kErrorCtxShadowDenied = 7044,
        kErrorCtxWinstationAccessDenied = 7045,
        kErrorCtxInvalidWd = 7049,
        kErrorCtxShadowInvalid = 7050,
        kErrorCtxShadowDisabled = 7051,
        kErrorCtxClientLicenseInUse = 7052,
        kErrorCtxClientLicenseNotSet = 7053,
        kErrorCtxLicenseNotAvailable = 7054,
        kErrorCtxLicenseClientInvalid = 7055,
        kErrorCtxLicenseExpired = 7056,
        kErrorCtxShadowNotRunning = 7057,
        kErrorCtxShadowEndedByModeChange = 7058,
        kFrsErrInvalidApiSequence = 8001,
        kFrsErrStartingService = 8002,
        kFrsErrStoppingService = 8003,
        kFrsErrInternalApi = 8004,
        kFrsErrInternal = 8005,
        kFrsErrServiceComm = 8006,
        kFrsErrInsufficientPriv = 8007,
        kFrsErrAuthentication = 8008,
        kFrsErrParentInsufficientPriv = 8009,
        kFrsErrParentAuthentication = 8010,
        kFrsErrChildToParentComm = 8011,
        kFrsErrParentToChildComm = 8012,
        kFrsErrSysvolPopulate = 8013,
        kFrsErrSysvolPopulateTimeout = 8014,
        kFrsErrSysvolIsBusy = 8015,
        kFrsErrSysvolDemote = 8016,
        kFrsErrInvalidServiceParameter = 8017,
        kErrorDsNotInstalled = 8200,
        kErrorDsMembershipEvaluatedLocally = 8201,
        kErrorDsNoAttributeOrValue = 8202,
        kErrorDsInvalidAttributeSyntax = 8203,
        kErrorDsAttributeTypeUndefined = 8204,
        kErrorDsAttributeOrValueExists = 8205,
        kErrorDsBusy = 8206,
        kErrorDsUnavailable = 8207,
        kErrorDsNoRidsAllocated = 8208,
        kErrorDsNoMoreRids = 8209,
        kErrorDsIncorrectRoleOwner = 8210,
        kErrorDsRidmgrInitError = 8211,
        kErrorDsObjClassViolation = 8212,
        kErrorDsCantOnNonLeaf = 8213,
        kErrorDsCantOnRdn = 8214,
        kErrorDsCantModObjClass = 8215,
        kErrorDsCrossDomMoveError = 8216,
        kErrorDsGcNotAvailable = 8217,
        kErrorSharedPolicy = 8218,
        kErrorPolicyObjectNotFound = 8219,
        kErrorPolicyOnlyInDs = 8220,
        kErrorPromotionActive = 8221,
        kErrorNoPromotionActive = 8222,
        kErrorDsOperationsError = 8224,
        kErrorDsProtocolError = 8225,
        kErrorDsTimelimitExceeded = 8226,
        kErrorDsSizelimitExceeded = 8227,
        kErrorDsAdminLimitExceeded = 8228,
        kErrorDsCompareFalse = 8229,
        kErrorDsCompareTrue = 8230,
        kErrorDsAuthMethodNotSupported = 8231,
        kErrorDsStrongAuthRequired = 8232,
        kErrorDsInappropriateAuth = 8233,
        kErrorDsAuthUnknown = 8234,
        kErrorDsReferral = 8235,
        kErrorDsUnavailableCritExtension = 8236,
        kErrorDsConfidentialityRequired = 8237,
        kErrorDsInappropriateMatching = 8238,
        kErrorDsConstraintViolation = 8239,
        kErrorDsNoSuchObject = 8240,
        kErrorDsAliasProblem = 8241,
        kErrorDsInvalidDnSyntax = 8242,
        kErrorDsIsLeaf = 8243,
        kErrorDsAliasDerefProblem = 8244,
        kErrorDsUnwillingToPerform = 8245,
        kErrorDsLoopDetect = 8246,
        kErrorDsNamingViolation = 8247,
        kErrorDsObjectResultsTooLarge = 8248,
        kErrorDsAffectsMultipleDsas = 8249,
        kErrorDsServerDown = 8250,
        kErrorDsLocalError = 8251,
        kErrorDsEncodingError = 8252,
        kErrorDsDecodingError = 8253,
        kErrorDsFilterUnknown = 8254,
        kErrorDsParamError = 8255,
        kErrorDsNotSupported = 8256,
        kErrorDsNoResultsReturned = 8257,
        kErrorDsControlNotFound = 8258,
        kErrorDsClientLoop = 8259,
        kErrorDsReferralLimitExceeded = 8260,
        kErrorDsSortControlMissing = 8261,
        kErrorDsOffsetRangeError = 8262,
        kErrorDsRootMustBeNc = 8301,
        kErrorDsAddReplicaInhibited = 8302,
        kErrorDsAttNotDefInSchema = 8303,
        kErrorDsMaxObjSizeExceeded = 8304,
        kErrorDsObjStringNameExists = 8305,
        kErrorDsNoRdnDefinedInSchema = 8306,
        kErrorDsRdnDoesntMatchSchema = 8307,
        kErrorDsNoRequestedAttsFound = 8308,
        kErrorDsUserBufferToSmall = 8309,
        kErrorDsAttIsNotOnObj = 8310,
        kErrorDsIllegalModOperation = 8311,
        kErrorDsObjTooLarge = 8312,
        kErrorDsBadInstanceType = 8313,
        kErrorDsMasterdsaRequired = 8314,
        kErrorDsObjectClassRequired = 8315,
        kErrorDsMissingRequiredAtt = 8316,
        kErrorDsAttNotDefForClass = 8317,
        kErrorDsAttAlreadyExists = 8318,
        kErrorDsCantAddAttValues = 8320,
        kErrorDsSingleValueConstraint = 8321,
        kErrorDsRangeConstraint = 8322,
        kErrorDsAttValAlreadyExists = 8323,
        kErrorDsCantRemMissingAtt = 8324,
        kErrorDsCantRemMissingAttVal = 8325,
        kErrorDsRootCantBeSubref = 8326,
        kErrorDsNoChaining = 8327,
        kErrorDsNoChainedEval = 8328,
        kErrorDsNoParentObject = 8329,
        kErrorDsParentIsAnAlias = 8330,
        kErrorDsCantMixMasterAndReps = 8331,
        kErrorDsChildrenExist = 8332,
        kErrorDsObjNotFound = 8333,
        kErrorDsAliasedObjMissing = 8334,
        kErrorDsBadNameSyntax = 8335,
        kErrorDsAliasPointsToAlias = 8336,
        kErrorDsCantDerefAlias = 8337,
        kErrorDsOutOfScope = 8338,
        kErrorDsObjectBeingRemoved = 8339,
        kErrorDsCantDeleteDsaObj = 8340,
        kErrorDsGenericError = 8341,
        kErrorDsDsaMustBeIntMaster = 8342,
        kErrorDsClassNotDsa = 8343,
        kErrorDsInsuffAccessRights = 8344,
        kErrorDsIllegalSuperior = 8345,
        kErrorDsAttributeOwnedBySam = 8346,
        kErrorDsNameTooManyParts = 8347,
        kErrorDsNameTooLong = 8348,
        kErrorDsNameValueTooLong = 8349,
        kErrorDsNameUnparseable = 8350,
        kErrorDsNameTypeUnknown = 8351,
        kErrorDsNotAnObject = 8352,
        kErrorDsSecDescTooShort = 8353,
        kErrorDsSecDescInvalid = 8354,
        kErrorDsNoDeletedName = 8355,
        kErrorDsSubrefMustHaveParent = 8356,
        kErrorDsNcnameMustBeNc = 8357,
        kErrorDsCantAddSystemOnly = 8358,
        kErrorDsClassMustBeConcrete = 8359,
        kErrorDsInvalidDmd = 8360,
        kErrorDsObjGuidExists = 8361,
        kErrorDsNotOnBacklink = 8362,
        kErrorDsNoCrossrefForNc = 8363,
        kErrorDsShuttingDown = 8364,
        kErrorDsUnknownOperation = 8365,
        kErrorDsInvalidRoleOwner = 8366,
        kErrorDsCouldntContactFsmo = 8367,
        kErrorDsCrossNcDnRename = 8368,
        kErrorDsCantModSystemOnly = 8369,
        kErrorDsReplicatorOnly = 8370,
        kErrorDsObjClassNotDefined = 8371,
        kErrorDsObjClassNotSubclass = 8372,
        kErrorDsNameReferenceInvalid = 8373,
        kErrorDsCrossRefExists = 8374,
        kErrorDsCantDelMasterCrossref = 8375,
        kErrorDsSubtreeNotifyNotNcHead = 8376,
        kErrorDsNotifyFilterTooComplex = 8377,
        kErrorDsDupRdn = 8378,
        kErrorDsDupOid = 8379,
        kErrorDsDupMapiId = 8380,
        kErrorDsDupSchemaIdGuid = 8381,
        kErrorDsDupLdapDisplayName = 8382,
        kErrorDsSemanticAttTest = 8383,
        kErrorDsSyntaxMismatch = 8384,
        kErrorDsExistsInMustHave = 8385,
        kErrorDsExistsInMayHave = 8386,
        kErrorDsNonexistentMayHave = 8387,
        kErrorDsNonexistentMustHave = 8388,
        kErrorDsAuxClsTestFail = 8389,
        kErrorDsNonexistentPossSup = 8390,
        kErrorDsSubClsTestFail = 8391,
        kErrorDsBadRdnAttIdSyntax = 8392,
        kErrorDsExistsInAuxCls = 8393,
        kErrorDsExistsInSubCls = 8394,
        kErrorDsExistsInPossSup = 8395,
        kErrorDsRecalcschemaFailed = 8396,
        kErrorDsTreeDeleteNotFinished = 8397,
        kErrorDsCantDelete = 8398,
        kErrorDsAttSchemaReqId = 8399,
        kErrorDsBadAttSchemaSyntax = 8400,
        kErrorDsCantCacheAtt = 8401,
        kErrorDsCantCacheClass = 8402,
        kErrorDsCantRemoveAttCache = 8403,
        kErrorDsCantRemoveClassCache = 8404,
        kErrorDsCantRetrieveDn = 8405,
        kErrorDsMissingSupref = 8406,
        kErrorDsCantRetrieveInstance = 8407,
        kErrorDsCodeInconsistency = 8408,
        kErrorDsDatabaseError = 8409,
        kErrorDsGovernsidMissing = 8410,
        kErrorDsMissingExpectedAtt = 8411,
        kErrorDsNcnameMissingCrRef = 8412,
        kErrorDsSecurityCheckingError = 8413,
        kErrorDsSchemaNotLoaded = 8414,
        kErrorDsSchemaAllocFailed = 8415,
        kErrorDsAttSchemaReqSyntax = 8416,
        kErrorDsGcverifyError = 8417,
        kErrorDsDraSchemaMismatch = 8418,
        kErrorDsCantFindDsaObj = 8419,
        kErrorDsCantFindExpectedNc = 8420,
        kErrorDsCantFindNcInCache = 8421,
        kErrorDsCantRetrieveChild = 8422,
        kErrorDsSecurityIllegalModify = 8423,
        kErrorDsCantReplaceHiddenRec = 8424,
        kErrorDsBadHierarchyFile = 8425,
        kErrorDsBuildHierarchyTableFailed = 8426,
        kErrorDsConfigParamMissing = 8427,
        kErrorDsCountingAbIndicesFailed = 8428,
        kErrorDsHierarchyTableMallocFailed = 8429,
        kErrorDsInternalFailure = 8430,
        kErrorDsUnknownError = 8431,
        kErrorDsRootRequiresClassTop = 8432,
        kErrorDsRefusingFsmoRoles = 8433,
        kErrorDsMissingFsmoSettings = 8434,
        kErrorDsUnableToSurrenderRoles = 8435,
        kErrorDsDraGeneric = 8436,
        kErrorDsDraInvalidParameter = 8437,
        kErrorDsDraBusy = 8438,
        kErrorDsDraBadDn = 8439,
        kErrorDsDraBadNc = 8440,
        kErrorDsDraDnExists = 8441,
        kErrorDsDraInternalError = 8442,
        kErrorDsDraInconsistentDit = 8443,
        kErrorDsDraConnectionFailed = 8444,
        kErrorDsDraBadInstanceType = 8445,
        kErrorDsDraOutOfMem = 8446,
        kErrorDsDraMailProblem = 8447,
        kErrorDsDraRefAlreadyExists = 8448,
        kErrorDsDraRefNotFound = 8449,
        kErrorDsDraObjIsRepSource = 8450,
        kErrorDsDraDbError = 8451,
        kErrorDsDraNoReplica = 8452,
        kErrorDsDraAccessDenied = 8453,
        kErrorDsDraNotSupported = 8454,
        kErrorDsDraRpcCancelled = 8455,
        kErrorDsDraSourceDisabled = 8456,
        kErrorDsDraSinkDisabled = 8457,
        kErrorDsDraNameCollision = 8458,
        kErrorDsDraSourceReinstalled = 8459,
        kErrorDsDraMissingParent = 8460,
        kErrorDsDraPreempted = 8461,
        kErrorDsDraAbandonSync = 8462,
        kErrorDsDraShutdown = 8463,
        kErrorDsDraIncompatiblePartialSet = 8464,
        kErrorDsDraSourceIsPartialReplica = 8465,
        kErrorDsDraExtnConnectionFailed = 8466,
        kErrorDsInstallSchemaMismatch = 8467,
        kErrorDsDupLinkId = 8468,
        kErrorDsNameErrorResolving = 8469,
        kErrorDsNameErrorNotFound = 8470,
        kErrorDsNameErrorNotUnique = 8471,
        kErrorDsNameErrorNoMapping = 8472,
        kErrorDsNameErrorDomainOnly = 8473,
        kErrorDsNameErrorNoSyntacticalMapping = 8474,
        kErrorDsConstructedAttMod = 8475,
        kErrorDsWrongOmObjClass = 8476,
        kErrorDsDraReplPending = 8477,
        kErrorDsDsRequired = 8478,
        kErrorDsInvalidLdapDisplayName = 8479,
        kErrorDsNonBaseSearch = 8480,
        kErrorDsCantRetrieveAtts = 8481,
        kErrorDsBacklinkWithoutLink = 8482,
        kErrorDsEpochMismatch = 8483,
        kErrorDsSrcNameMismatch = 8484,
        kErrorDsSrcAndDstNcIdentical = 8485,
        kErrorDsDstNcMismatch = 8486,
        kErrorDsNotAuthoritiveForDstNc = 8487,
        kErrorDsSrcGuidMismatch = 8488,
        kErrorDsCantMoveDeletedObject = 8489,
        kErrorDsPdcOperationInProgress = 8490,
        kErrorDsCrossDomainCleanupReqd = 8491,
        kErrorDsIllegalXdomMoveOperation = 8492,
        kErrorDsCantWithAcctGroupMembershps = 8493,
        kErrorDsNcMustHaveNcParent = 8494,
        kErrorDsDstDomainNotNative = 8496,
        kErrorDsMissingInfrastructureContainer = 8497,
        kErrorDsCantMoveAccountGroup = 8498,
        kErrorDsCantMoveResourceGroup = 8499,
        kErrorDsInvalidSearchFlag = 8500,
        kErrorDsNoTreeDeleteAboveNc = 8501,
        kErrorDsCouldntLockTreeForDelete = 8502,
        kErrorDsCouldntIdentifyObjectsForTreeDelete = 8503,
        kErrorDsSamInitFailure = 8504,
        kErrorDsSensitiveGroupViolation = 8505,
        kErrorDsCantModPrimarygroupid = 8506,
        kErrorDsIllegalBaseSchemaMod = 8507,
        kErrorDsNonsafeSchemaChange = 8508,
        kErrorDsSchemaUpdateDisallowed = 8509,
        kErrorDsCantCreateUnderSchema = 8510,
        kErrorDsInstallNoSrcSchVersion = 8511,
        kErrorDsInstallNoSchVersionInInifile = 8512,
        kErrorDsInvalidGroupType = 8513,
        kErrorDsNoNestGlobalgroupInMixeddomain = 8514,
        kErrorDsNoNestLocalgroupInMixeddomain = 8515,
        kErrorDsGlobalCantHaveLocalMember = 8516,
        kErrorDsGlobalCantHaveUniversalMember = 8517,
        kErrorDsUniversalCantHaveLocalMember = 8518,
        kErrorDsGlobalCantHaveCrossdomainMember = 8519,
        kErrorDsLocalCantHaveCrossdomainLocalMember = 8520,
        kErrorDsHavePrimaryMembers = 8521,
        kErrorDsStringSdConversionFailed = 8522,
        kErrorDsNamingMasterGc = 8523,
        kErrorDsLookupFailure = 8524,
        kErrorDsCouldntUpdateSpns = 8525,
        kErrorDsCantRetrieveSd = 8526,
        kErrorDsKeyNotUnique = 8527,
        kErrorDsWrongLinkedAttSyntax = 8528,
        kErrorDsSamNeedBootkeyPassword = 8529,
        kErrorDsSamNeedBootkeyFloppy = 8530,
        kErrorDsCantStart = 8531,
        kErrorDsInitFailure = 8532,
        kErrorDsNoPktPrivacyOnConnection = 8533,
        kErrorDsSourceDomainInForest = 8534,
        kErrorDsDestinationDomainNotInForest = 8535,
        kErrorDsDestinationAuditingNotEnabled = 8536,
        kErrorDsCantFindDcForSrcDomain = 8537,
        kErrorDsSrcObjNotGroupOrUser = 8538,
        kErrorDsSrcSidExistsInForest = 8539,
        kErrorDsSrcAndDstObjectClassMismatch = 8540,
        kErrorSamInitFailure = 8541,
        kErrorDsDraSchemaInfoShip = 8542,
        kErrorDsDraSchemaConflict = 8543,
        kErrorDsDraEarlierSchemaConlict = 8544,
        kErrorDsDraObjNcMismatch = 8545,
        kErrorDsNcStillHasDsas = 8546,
        kErrorDsGcRequired = 8547,
        kErrorDsLocalMemberOfLocalOnly = 8548,
        kErrorDsNoFpoInUniversalGroups = 8549,
        kErrorDsCantAddToGc = 8550,
        kErrorDsNoCheckpointWithPdc = 8551,
        kErrorDsSourceAuditingNotEnabled = 8552,
        kErrorDsCantCreateInNondomainNc = 8553,
        kErrorDsInvalidNameForSpn = 8554,
        kErrorDsFilterUsesContructedAttrs = 8555,
        kErrorDsUnicodepwdNotInQuotes = 8556,
        kErrorDsMachineAccountQuotaExceeded = 8557,
        kErrorDsMustBeRunOnDstDc = 8558,
        kErrorDsSrcDcMustBeSp4OrGreater = 8559,
        kErrorDsCantTreeDeleteCriticalObj = 8560,
        kErrorDsInitFailureConsole = 8561,
        kErrorDsSamInitFailureConsole = 8562,
        kErrorDsForestVersionTooHigh = 8563,
        kErrorDsDomainVersionTooHigh = 8564,
        kErrorDsForestVersionTooLow = 8565,
        kErrorDsDomainVersionTooLow = 8566,
        kErrorDsIncompatibleVersion = 8567,
        kErrorDsLowDsaVersion = 8568,
        kErrorDsNoBehaviorVersionInMixeddomain = 8569,
        kErrorDsNotSupportedSortOrder = 8570,
        kErrorDsNameNotUnique = 8571,
        kErrorDsMachineAccountCreatedPrent4 = 8572,
        kErrorDsOutOfVersionStore = 8573,
        kErrorDsIncompatibleControlsUsed = 8574,
        kErrorDsNoRefDomain = 8575,
        kErrorDsReservedLinkId = 8576,
        kErrorDsLinkIdNotAvailable = 8577,
        kErrorDsAgCantHaveUniversalMember = 8578,
        kErrorDsModifydnDisallowedByInstanceType = 8579,
        kErrorDsNoObjectMoveInSchemaNc = 8580,
        kErrorDsModifydnDisallowedByFlag = 8581,
        kErrorDsModifydnWrongGrandparent = 8582,
        kErrorDsNameErrorTrustReferral = 8583,
        kErrorNotSupportedOnStandardServer = 8584,
        kErrorDsCantAccessRemotePartOfAd = 8585,
        kErrorDsCrImpossibleToValidate = 8586,
        kErrorDsThreadLimitExceeded = 8587,
        kErrorDsNotClosest = 8588,
        kErrorDsCantDeriveSpnWithoutServerRef = 8589,
        kErrorDsSingleUserModeFailed = 8590,
        kErrorDsNtdscriptSyntaxError = 8591,
        kErrorDsNtdscriptProcessError = 8592,
        kErrorDsDifferentReplEpochs = 8593,
        kErrorDsDrsExtensionsChanged = 8594,
        kErrorDsReplicaSetChangeNotAllowedOnDisabledCr = 8595,
        kErrorDsNoMsdsIntid = 8596,
        kErrorDsDupMsdsIntid = 8597,
        kErrorDsExistsInRdnattid = 8598,
        kErrorDsAuthorizationFailed = 8599,
        kErrorDsInvalidScript = 8600,
        kErrorDsRemoteCrossrefOpFailed = 8601,
        kDnsErrorRcodeFormatError = 9001,
        kDnsErrorRcodeServerFailure = 9002,
        kDnsErrorRcodeNameError = 9003,
        kDnsErrorRcodeNotImplemented = 9004,
        kDnsErrorRcodeRefused = 9005,
        kDnsErrorRcodeYxdomain = 9006,
        kDnsErrorRcodeYxrrset = 9007,
        kDnsErrorRcodeNxrrset = 9008,
        kDnsErrorRcodeNotauth = 9009,
        kDnsErrorRcodeNotzone = 9010,
        kDnsErrorRcodeBadsig = 9016,
        kDnsErrorRcodeBadkey = 9017,
        kDnsErrorRcodeBadtime = 9018,
        kDnsInfoNoRecords = 9501,
        kDnsErrorBadPacket = 9502,
        kDnsErrorNoPacket = 9503,
        kDnsErrorRcode = 9504,
        kDnsErrorUnsecurePacket = 9505,
        kDnsErrorInvalidType = 9551,
        kDnsErrorInvalidIpAddress = 9552,
        kDnsErrorInvalidProperty = 9553,
        kDnsErrorTryAgainLater = 9554,
        kDnsErrorNotUnique = 9555,
        kDnsErrorNonRfcName = 9556,
        kDnsStatusFqdn = 9557,
        kDnsStatusDottedName = 9558,
        kDnsStatusSinglePartName = 9559,
        kDnsErrorInvalidNameChar = 9560,
        kDnsErrorNumericName = 9561,
        kDnsErrorNotAllowedOnRootServer = 9562,
        kDnsErrorZoneDoesNotExist = 9601,
        kDnsErrorNoZoneInfo = 9602,
        kDnsErrorInvalidZoneOperation = 9603,
        kDnsErrorZoneConfigurationError = 9604,
        kDnsErrorZoneHasNoSoaRecord = 9605,
        kDnsErrorZoneHasNoNsRecords = 9606,
        kDnsErrorZoneLocked = 9607,
        kDnsErrorZoneCreationFailed = 9608,
        kDnsErrorZoneAlreadyExists = 9609,
        kDnsErrorAutozoneAlreadyExists = 9610,
        kDnsErrorInvalidZoneType = 9611,
        kDnsErrorSecondaryRequiresMasterIp = 9612,
        kDnsErrorZoneNotSecondary = 9613,
        kDnsErrorNeedSecondaryAddresses = 9614,
        kDnsErrorWinsInitFailed = 9615,
        kDnsErrorNeedWinsServers = 9616,
        kDnsErrorNbstatInitFailed = 9617,
        kDnsErrorSoaDeleteInvalid = 9618,
        kDnsErrorForwarderAlreadyExists = 9619,
        kDnsErrorZoneRequiresMasterIp = 9620,
        kDnsErrorZoneIsShutdown = 9621,
        kDnsErrorPrimaryRequiresDatafile = 9651,
        kDnsErrorInvalidDatafileName = 9652,
        kDnsErrorDatafileOpenFailure = 9653,
        kDnsErrorFileWritebackFailed = 9654,
        kDnsErrorDatafileParsing = 9655,
        kDnsErrorRecordDoesNotExist = 9701,
        kDnsErrorRecordFormat = 9702,
        kDnsErrorNodeCreationFailed = 9703,
        kDnsErrorUnknownRecordType = 9704,
        kDnsErrorRecordTimedOut = 9705,
        kDnsErrorNameNotInZone = 9706,
        kDnsErrorCnameLoop = 9707,
        kDnsErrorNodeIsCname = 9708,
        kDnsErrorCnameCollision = 9709,
        kDnsErrorRecordOnlyAtZoneRoot = 9710,
        kDnsErrorRecordAlreadyExists = 9711,
        kDnsErrorSecondaryData = 9712,
        kDnsErrorNoCreateCacheData = 9713,
        kDnsErrorNameDoesNotExist = 9714,
        kDnsWarningPtrCreateFailed = 9715,
        kDnsWarningDomainUndeleted = 9716,
        kDnsErrorDsUnavailable = 9717,
        kDnsErrorDsZoneAlreadyExists = 9718,
        kDnsErrorNoBootfileIfDsZone = 9719,
        kDnsInfoAxfrComplete = 9751,
        kDnsErrorAxfr = 9752,
        kDnsInfoAddedLocalWins = 9753,
        kDnsStatusContinueNeeded = 9801,
        kDnsErrorNoTcpip = 9851,
        kDnsErrorNoDnsServers = 9852,
        kDnsErrorDpDoesNotExist = 9901,
        kDnsErrorDpAlreadyExists = 9902,
        kDnsErrorDpNotEnlisted = 9903,
        kDnsErrorDpAlreadyEnlisted = 9904,
        kWSAeintr = 10004,
        kWSAebadf = 10009,
        kWSAeacces = 10013,
        kWSAefault = 10014,
        kWSAeinval = 10022,
        kWSAemfile = 10024,
        kWSAewouldblock = 10035,
        kWSAeinprogress = 10036,
        kWSAealready = 10037,
        kWSAenotsock = 10038,
        kWSAedestaddrreq = 10039,
        kWSAemsgsize = 10040,
        kWSAeprototype = 10041,
        kWSAenoprotoopt = 10042,
        kWSAeprotonosupport = 10043,
        kWSAesocktnosupport = 10044,
        kWSAeopnotsupp = 10045,
        kWSAepfnosupport = 10046,
        kWSAeafnosupport = 10047,
        kWSAeaddrinuse = 10048,
        kWSAeaddrnotavail = 10049,
        kWSAenetdown = 10050,
        kWSAenetunreach = 10051,
        kWSAenetreset = 10052,
        kWSAeconnaborted = 10053,
        kWSAeconnreset = 10054,
        kWSAenobufs = 10055,
        kWSAeisconn = 10056,
        kWSAenotconn = 10057,
        kWSAeshutdown = 10058,
        kWSAetoomanyrefs = 10059,
        kWSAetimedout = 10060,
        kWSAeconnrefused = 10061,
        kWSAeloop = 10062,
        kWSAenametoolong = 10063,
        kWSAehostdown = 10064,
        kWSAehostunreach = 10065,
        kWSAenotempty = 10066,
        kWSAeproclim = 10067,
        kWSAeusers = 10068,
        kWSAedquot = 10069,
        kWSAestale = 10070,
        kWSAeremote = 10071,
        kWSAsysnotready = 10091,
        kWSAvernotsupported = 10092,
        kWSAnotinitialised = 10093,
        kWSAediscon = 10101,
        kWSAenomore = 10102,
        kWSAecancelled = 10103,
        kWSAeinvalidproctable = 10104,
        kWSAeinvalidprovider = 10105,
        kWSAeproviderfailedinit = 10106,
        kWSAsyscallfailure = 10107,
        kWSAserviceNotFound = 10108,
        kWSAtypeNotFound = 10109,
        kWSAENoMore = 10110,
        kWSAECancelled = 10111,
        kWSAerefused = 10112,
        kWSAhostNotFound = 11001,
        kWSAtryAgain = 11002,
        kWSAnoRecovery = 11003,
        kWSAnoData = 11004,
        kWSAQosReceivers = 11005,
        kWSAQosSenders = 11006,
        kWSAQosNoSenders = 11007,
        kWSAQosNoReceivers = 11008,
        kWSAQosRequestConfirmed = 11009,
        kWSAQosAdmissionFailure = 11010,
        kWSAQosPolicyFailure = 11011,
        kWSAQosBadStyle = 11012,
        kWSAQosBadObject = 11013,
        kWSAQosTrafficCtrlError = 11014,
        kWSAQosGenericError = 11015,
        kWSAQosEservicetype = 11016,
        kWSAQosEflowspec = 11017,
        kWSAQosEprovspecbuf = 11018,
        kWSAQosEfilterstyle = 11019,
        kWSAQosEfiltertype = 11020,
        kWSAQosEfiltercount = 11021,
        kWSAQosEobjlength = 11022,
        kWSAQosEflowcount = 11023,
        kWSAQosEunknownpsobj = 11024,
        kWSAQosEpolicyobj = 11025,
        kWSAQosEflowdesc = 11026,
        kWSAQosEpsflowspec = 11027,
        kWSAQosEpsfilterspec = 11028,
        kWSAQosEsdmodeobj = 11029,
        kWSAQosEshaperateobj = 11030,
        kWSAQosReservedPetype = 11031,
        kErrorIpsecQmPolicyExists = 13000,
        kErrorIpsecQmPolicyNotFound = 13001,
        kErrorIpsecQmPolicyInUse = 13002,
        kErrorIpsecMmPolicyExists = 13003,
        kErrorIpsecMmPolicyNotFound = 13004,
        kErrorIpsecMmPolicyInUse = 13005,
        kErrorIpsecMmFilterExists = 13006,
        kErrorIpsecMmFilterNotFound = 13007,
        kErrorIpsecTransportFilterExists = 13008,
        kErrorIpsecTransportFilterNotFound = 13009,
        kErrorIpsecMmAuthExists = 13010,
        kErrorIpsecMmAuthNotFound = 13011,
        kErrorIpsecMmAuthInUse = 13012,
        kErrorIpsecDefaultMmPolicyNotFound = 13013,
        kErrorIpsecDefaultMmAuthNotFound = 13014,
        kErrorIpsecDefaultQmPolicyNotFound = 13015,
        kErrorIpsecTunnelFilterExists = 13016,
        kErrorIpsecTunnelFilterNotFound = 13017,
        kErrorIpsecMmFilterPendingDeletion = 13018,
        kErrorIpsecTransportFilterPendingDeletion = 13019,
        kErrorIpsecTunnelFilterPendingDeletion = 13020,
        kErrorIpsecMmPolicyPendingDeletion = 13021,
        kErrorIpsecMmAuthPendingDeletion = 13022,
        kErrorIpsecQmPolicyPendingDeletion = 13023,
        kErrorIpsecIkeAuthFail = 13801,
        kErrorIpsecIkeAttribFail = 13802,
        kErrorIpsecIkeNegotiationPending = 13803,
        kErrorIpsecIkeGeneralProcessingError = 13804,
        kErrorIpsecIkeTimedOut = 13805,
        kErrorIpsecIkeNoCert = 13806,
        kErrorIpsecIkeSaDeleted = 13807,
        kErrorIpsecIkeSaReaped = 13808,
        kErrorIpsecIkeMmAcquireDrop = 13809,
        kErrorIpsecIkeQmAcquireDrop = 13810,
        kErrorIpsecIkeQueueDropMm = 13811,
        kErrorIpsecIkeQueueDropNoMm = 13812,
        kErrorIpsecIkeDropNoResponse = 13813,
        kErrorIpsecIkeMmDelayDrop = 13814,
        kErrorIpsecIkeQmDelayDrop = 13815,
        kErrorIpsecIkeError = 13816,
        kErrorIpsecIkeCrlFailed = 13817,
        kErrorIpsecIkeInvalidKeyUsage = 13818,
        kErrorIpsecIkeInvalidCertType = 13819,
        kErrorIpsecIkeNoPrivateKey = 13820,
        kErrorIpsecIkeDhFail = 13822,
        kErrorIpsecIkeInvalidHeader = 13824,
        kErrorIpsecIkeNoPolicy = 13825,
        kErrorIpsecIkeInvalidSignature = 13826,
        kErrorIpsecIkeKerberosError = 13827,
        kErrorIpsecIkeNoPublicKey = 13828,
        kErrorIpsecIkeProcessErr = 13829,
        kErrorIpsecIkeProcessErrSa = 13830,
        kErrorIpsecIkeProcessErrProp = 13831,
        kErrorIpsecIkeProcessErrTrans = 13832,
        kErrorIpsecIkeProcessErrKe = 13833,
        kErrorIpsecIkeProcessErrId = 13834,
        kErrorIpsecIkeProcessErrCert = 13835,
        kErrorIpsecIkeProcessErrCertReq = 13836,
        kErrorIpsecIkeProcessErrHash = 13837,
        kErrorIpsecIkeProcessErrSig = 13838,
        kErrorIpsecIkeProcessErrNonce = 13839,
        kErrorIpsecIkeProcessErrNotify = 13840,
        kErrorIpsecIkeProcessErrDelete = 13841,
        kErrorIpsecIkeProcessErrVendor = 13842,
        kErrorIpsecIkeInvalidPayload = 13843,
        kErrorIpsecIkeLoadSoftSa = 13844,
        kErrorIpsecIkeSoftSaTornDown = 13845,
        kErrorIpsecIkeInvalidCookie = 13846,
        kErrorIpsecIkeNoPeerCert = 13847,
        kErrorIpsecIkePeerCrlFailed = 13848,
        kErrorIpsecIkePolicyChange = 13849,
        kErrorIpsecIkeNoMmPolicy = 13850,
        kErrorIpsecIkeNotcbpriv = 13851,
        kErrorIpsecIkeSecloadfail = 13852,
        kErrorIpsecIkeFailsspinit = 13853,
        kErrorIpsecIkeFailqueryssp = 13854,
        kErrorIpsecIkeSrvacqfail = 13855,
        kErrorIpsecIkeSrvquerycred = 13856,
        kErrorIpsecIkeGetspifail = 13857,
        kErrorIpsecIkeInvalidFilter = 13858,
        kErrorIpsecIkeOutOfMemory = 13859,
        kErrorIpsecIkeAddUpdateKeyFailed = 13860,
        kErrorIpsecIkeInvalidPolicy = 13861,
        kErrorIpsecIkeUnknownDoi = 13862,
        kErrorIpsecIkeInvalidSituation = 13863,
        kErrorIpsecIkeDhFailure = 13864,
        kErrorIpsecIkeInvalidGroup = 13865,
        kErrorIpsecIkeEncrypt = 13866,
        kErrorIpsecIkeDecrypt = 13867,
        kErrorIpsecIkePolicyMatch = 13868,
        kErrorIpsecIkeUnsupportedId = 13869,
        kErrorIpsecIkeInvalidHash = 13870,
        kErrorIpsecIkeInvalidHashAlg = 13871,
        kErrorIpsecIkeInvalidHashSize = 13872,
        kErrorIpsecIkeInvalidEncryptAlg = 13873,
        kErrorIpsecIkeInvalidAuthAlg = 13874,
        kErrorIpsecIkeInvalidSig = 13875,
        kErrorIpsecIkeLoadFailed = 13876,
        kErrorIpsecIkeRpcDelete = 13877,
        kErrorIpsecIkeBenignReinit = 13878,
        kErrorIpsecIkeInvalidResponderLifetimeNotify = 13879,
        kErrorIpsecIkeInvalidCertKeylen = 13881,
        kErrorIpsecIkeMmLimit = 13882,
        kErrorIpsecIkeNegotiationDisabled = 13883,
        kErrorIpsecIkeNegStatusEnd = 13884,
        kErrorSxsSectionNotFound = 14000,
        kErrorSxsCantGenActctx = 14001,
        kErrorSxsInvalidActctxdataFormat = 14002,
        kErrorSxsAssemblyNotFound = 14003,
        kErrorSxsManifestFormatError = 14004,
        kErrorSxsManifestParseError = 14005,
        kErrorSxsActivationContextDisabled = 14006,
        kErrorSxsKeyNotFound = 14007,
        kErrorSxsVersionConflict = 14008,
        kErrorSxsWrongSectionType = 14009,
        kErrorSxsThreadQueriesDisabled = 14010,
        kErrorSxsProcessDefaultAlreadySet = 14011,
        kErrorSxsUnknownEncodingGroup = 14012,
        kErrorSxsUnknownEncoding = 14013,
        kErrorSxsInvalidXmlNamespaceUri = 14014,
        kErrorSxsRootManifestDependencyNotInstalled = 14015,
        kErrorSxsLeafManifestDependencyNotInstalled = 14016,
        kErrorSxsInvalidAssemblyIdentityAttribute = 14017,
        kErrorSxsManifestMissingRequiredDefaultNamespace = 14018,
        kErrorSxsManifestInvalidRequiredDefaultNamespace = 14019,
        kErrorSxsPrivateManifestCrossPathWithReparsePoint = 14020,
        kErrorSxsDuplicateDllName = 14021,
        kErrorSxsDuplicateWindowclassName = 14022,
        kErrorSxsDuplicateClsid = 14023,
        kErrorSxsDuplicateIid = 14024,
        kErrorSxsDuplicateTlbid = 14025,
        kErrorSxsDuplicateProgid = 14026,
        kErrorSxsDuplicateAssemblyName = 14027,
        kErrorSxsFileHashMismatch = 14028,
        kErrorSxsPolicyParseError = 14029,
        kErrorSxsXmlEMissingquote = 14030,
        kErrorSxsXmlECommentsyntax = 14031,
        kErrorSxsXmlEBadstartnamechar = 14032,
        kErrorSxsXmlEBadnamechar = 14033,
        kErrorSxsXmlEBadcharinstring = 14034,
        kErrorSxsXmlEXmldeclsyntax = 14035,
        kErrorSxsXmlEBadchardata = 14036,
        kErrorSxsXmlEMissingwhitespace = 14037,
        kErrorSxsXmlEExpectingtagend = 14038,
        kErrorSxsXmlEMissingsemicolon = 14039,
        kErrorSxsXmlEUnbalancedparen = 14040,
        kErrorSxsXmlEInternalerror = 14041,
        kErrorSxsXmlEUnexpectedWhitespace = 14042,
        kErrorSxsXmlEIncompleteEncoding = 14043,
        kErrorSxsXmlEMissingParen = 14044,
        kErrorSxsXmlEExpectingclosequote = 14045,
        kErrorSxsXmlEMultipleColons = 14046,
        kErrorSxsXmlEInvalidDecimal = 14047,
        kErrorSxsXmlEInvalidHexidecimal = 14048,
        kErrorSxsXmlEInvalidUnicode = 14049,
        kErrorSxsXmlEWhitespaceorquestionmark = 14050,
        kErrorSxsXmlEUnexpectedendtag = 14051,
        kErrorSxsXmlEUnclosedtag = 14052,
        kErrorSxsXmlEDuplicateattribute = 14053,
        kErrorSxsXmlEMultipleroots = 14054,
        kErrorSxsXmlEInvalidatrootlevel = 14055,
        kErrorSxsXmlEBadxmldecl = 14056,
        kErrorSxsXmlEMissingroot = 14057,
        kErrorSxsXmlEUnexpectedeof = 14058,
        kErrorSxsXmlEBadperefinsubset = 14059,
        kErrorSxsXmlEUnclosedstarttag = 14060,
        kErrorSxsXmlEUnclosedendtag = 14061,
        kErrorSxsXmlEUnclosedstring = 14062,
        kErrorSxsXmlEUnclosedcomment = 14063,
        kErrorSxsXmlEUncloseddecl = 14064,
        kErrorSxsXmlEUnclosedcdata = 14065,
        kErrorSxsXmlEReservednamespace = 14066,
        kErrorSxsXmlEInvalidencoding = 14067,
        kErrorSxsXmlEInvalidswitch = 14068,
        kErrorSxsXmlEBadxmlcase = 14069,
        kErrorSxsXmlEInvalidStandalone = 14070,
        kErrorSxsXmlEUnexpectedStandalone = 14071,
        kErrorSxsXmlEInvalidVersion = 14072,
        kErrorSxsXmlEMissingequals = 14073,
        kErrorSxsProtectionRecoveryFailed = 14074,
        kErrorSxsProtectionPublicKeyTooShort = 14075,
        kErrorSxsProtectionCatalogNotValid = 14076,
        kErrorSxsUntranslatableHresult = 14077,
        kErrorSxsProtectionCatalogFileMissing = 14078,
        kErrorSxsMissingAssemblyIdentityAttribute = 14079,
        kErrorSxsInvalidAssemblyIdentityAttributeName = 14080
    };
}
}
