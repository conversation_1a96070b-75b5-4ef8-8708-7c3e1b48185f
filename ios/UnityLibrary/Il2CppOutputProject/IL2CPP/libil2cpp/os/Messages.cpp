#include <stdlib.h>

#include "il2cpp-config.h"

#include "os/Messages.h"

#define N_ELEMENTS(e) \
    (sizeof (e) / sizeof ((e)[0]))

namespace il2cpp
{
namespace os
{
    ErrorDesc common_messages[] =
    {
        { kErrorCodeSuccess, "Success" },
        { kErrorCodeFileNotFound, "Cannot find the specified file" },
        { kErrorCodePathNotFound, "Cannot find the specified file" },
        { kErrorCodeTooManyOpenFiles, "Too many open files" },
        { kErrorCodeAccessDenied, "Access denied" },
        { kErrorCodeInvalidHandle, "Invalid handle" },
        { kErrorInvalidData, "Invalid data" },
        { kErrorOutofmemory, "Out of memory" },
        { kErrorCodeNotSameDevice, "Not same device" },
        { kErrorCodeNoMoreFiles, "No more files" },
        { kErrorBadLength, "Bad length" },
        { kErrorCodeGenFailure, "General failure" },
        { kErrorCodeSharingViolation, "Sharing violation" },
        { kErrorCodeLockViolation, "Lock violation" },
        { kErrorNotSupported, "Operation not supported" },
        { kErrorCodeInvalidParameter, "Invalid parameter" },
        { kErrorCallNotImplemented, "Call not implemented" },
        { kErrorCodeInvalidName, "Invalid name" },
        { kErrorProcNotFound, "Process not found" },
        { kErrorCodeAlreadyExists, "Already exists" },
        { kErrorDirectory, "Is a directory" },
        { kErrorCodeEncryptionFailed, "Encryption failed" },
        { kWSAeintr, "interrupted" },
        { kWSAebadf, "Bad file number" },
        { kWSAeacces, "Access denied" },
        { kWSAefault, "Bad address" },
        { kWSAeinval, "Invalid arguments" },
        { kWSAemfile, "Too many open files" },
        { kWSAewouldblock, "Operation on non-blocking socket would block" },
        { kWSAeinprogress, "Operation in progress" },
        { kWSAealready, "Operation already in progress" },
        { kWSAenotsock, "The descriptor is not a socket" },
        { kWSAedestaddrreq, "Destination address required" },
        { kWSAemsgsize, "Message too long" },
        { kWSAeprototype, "Protocol wrong type for socket" },
        { kWSAenoprotoopt, "Protocol option not supported" },
        { kWSAeprotonosupport, "Protocol not supported" },
        { kWSAesocktnosupport, "Socket not supported" },
        { kWSAeopnotsupp, "Operation not supported" },
        { kWSAepfnosupport, "Protocol family not supported" },
        { kWSAeafnosupport, "An address incompatible with the requested protocol was used" },
        { kWSAeaddrinuse, "Address already in use" },
        { kWSAeaddrnotavail, "The requested address is not valid in this context" },
        { kWSAenetdown, "Network subsystem is down" },
        { kWSAenetunreach, "Network is unreachable" },
        { kWSAenetreset, "Connection broken, keep-alive detected a problem" },
        { kWSAeconnaborted, "An established connection was aborted in your host machine." },
        { kWSAeconnreset, "Connection reset by peer" },
        { kWSAenobufs, "Not enough buffer space is available" },
        { kWSAeisconn, "Socket is already connected" },
        { kWSAenotconn, "The socket is not connected" },
        { kWSAeshutdown, "The socket has been shut down" },
        { kWSAetoomanyrefs, "Too many references: cannot splice" },
        { kWSAetimedout, "Connection timed out" },
        { kWSAeconnrefused, "Connection refused" },
        { kWSAeloop, "Too many symbolic links encountered" },
        { kWSAenametoolong, "File name too long" },
        { kWSAehostdown, "Host is down" },
        { kWSAehostunreach, "No route to host" },
        { kWSAenotempty, "Directory not empty" },
        { kWSAeproclim, "EPROCLIM" },
        { kWSAeusers, "Too many users" },
        { kWSAedquot, "Quota exceeded" },
        { kWSAestale, "Stale NFS file handle" },
        { kWSAeremote, "Object is remote" },
        { kWSAsysnotready, "SYSNOTREADY" },
        { kWSAvernotsupported, "VERNOTSUPPORTED" },
        { kWSAnotinitialised, "Winsock not initialised" },
        { kWSAediscon, "EDISCON" },
        { kWSAenomore, "ENOMORE" },
        { kWSAecancelled, "Operation canceled" },
        { kWSAeinvalidproctable, "EINVALIDPROCTABLE" },
        { kWSAeinvalidprovider, "EINVALIDPROVIDER" },
        { kWSAeproviderfailedinit, "EPROVIDERFAILEDINIT" },
        { kWSAsyscallfailure, "System call failed" },
        { kWSAserviceNotFound, "SERVICE_NOT_FOUND" },
        { kWSAtypeNotFound, "TYPE_NOT_FOUND" },
        { kWSAENoMore, "E_NO_MORE" },
        { kWSAECancelled, "E_CANCELLED" },
        { kWSAerefused, "EREFUSED" },
        { kWSAhostNotFound, "No such host is known" },
        { kWSAtryAgain, "A temporary error occurred on an authoritative name server.  Try again later." },
        { kWSAnoRecovery, "No recovery" },
        { kWSAnoData, "No data" },
    };

#ifndef IL2CPP_DISABLE_FULL_MESSAGES
    ErrorDesc messages[] =
    {
        { kErrorInvalidFunction, "Invalid function" },
        { kErrorArenaTrashed, "Arena trashed" },
        { kErrorNotEnoughMemory, "Not enough memory" },
        { kErrorInvalidBlock, "Invalid block" },
        { kErrorBadEnvironment, "Bad environment" },
        { kErrorBadFormat, "Bad format" },
        { kErrorInvalidAccess, "Invalid access" },
        { kErrorInvalidDrive, "Invalid drive" },
        { kErrorCurrentDirectory, "Current directory" },
        { kErrorWriteProtect, "Write protect" },
        { kErrorBadUnit, "Bad unit" },
        { kErrorNotReady, "Not ready" },
        { kErrorBadCommand, "Bad command" },
        { kErrorCrc, "CRC" },
        { kErrorSeek, "Seek" },
        { kErrorNotDosDisk, "Not DOS disk" },
        { kErrorSectorNotFound, "Sector not found" },
        { kErrorOutOfPaper, "Out of paper" },
        { kErrorWriteFault, "Write fault" },
        { kErrorReadFault, "Read fault" },
        { kErrorWrongDisk, "Wrong disk" },
        { kErrorSharingBufferExceeded, "Sharing buffer exceeded" },
        { kErrorHandleEof, "Handle EOF" },
        { kErrorHandleDiskFull, "Handle disk full" },
        { kErrorRemNotList, "Rem not list" },
        { kErrorDupName, "Duplicate name" },
        { kErrorBadNetpath, "Bad netpath" },
        { kErrorNetworkBusy, "Network busy" },
        { kErrorDevNotExist, "Device does not exist" },
        { kErrorTooManyCmds, "Too many commands" },
        { kErrorAdapHdwErr, "ADAP HDW error" },
        { kErrorBadNetResp, "Bad net response" },
        { kErrorUnexpNetErr, "Unexpected net error" },
        { kErrorBadRemAdap, "Bad rem adap" },
        { kErrorPrintqFull, "Print queue full" },
        { kErrorNoSpoolSpace, "No spool space" },
        { kErrorPrintCancelled, "Print cancelled" },
        { kErrorNetnameDeleted, "Netname deleted" },
        { kErrorNetworkAccessDenied, "Network access denied" },
        { kErrorBadDevType, "Bad device type" },
        { kErrorBadNetName, "Bad net name" },
        { kErrorTooManyNames, "Too many names" },
        { kErrorTooManySess, "Too many sessions" },
        { kErrorSharingPaused, "Sharing paused" },
        { kErrorReqNotAccep, "Req not accep" },
        { kErrorRedirPaused, "Redir paused" },
        { kErrorFileExists, "File exists" },
        { kErrorCannotMake, "Cannot make" },
        { kErrorFailI24, "Fail i24" },
        { kErrorOutOfStructures, "Out of structures" },
        { kErrorAlreadyAssigned, "Already assigned" },
        { kErrorInvalidPassword, "Invalid password" },
        { kErrorNetWriteFault, "Net write fault" },
        { kErrorNoProcSlots, "No proc slots" },
        { kErrorTooManySemaphores, "Too many semaphores" },
        { kErrorExclSemAlreadyOwned, "Exclusive semaphore already owned" },
        { kErrorSemIsSet, "Semaphore is set" },
        { kErrorTooManySemRequests, "Too many semaphore requests" },
        { kErrorInvalidAtInterruptTime, "Invalid at interrupt time" },
        { kErrorSemOwnerDied, "Semaphore owner died" },
        { kErrorSemUserLimit, "Semaphore user limit" },
        { kErrorDiskChange, "Disk change" },
        { kErrorDriveLocked, "Drive locked" },
        { kErrorBrokenPipe, "Broken pipe" },
        { kErrorOpenFailed, "Open failed" },
        { kErrorBufferOverflow, "Buffer overflow" },
        { kErrorDiskFull, "Disk full" },
        { kErrorNoMoreSearchHandles, "No more search handles" },
        { kErrorInvalidTargetHandle, "Invalid target handle" },
        { kErrorInvalidCategory, "Invalid category" },
        { kErrorInvalidVerifySwitch, "Invalid verify switch" },
        { kErrorBadDriverLevel, "Bad driver level" },
        { kErrorSemTimeout, "Semaphore timeout" },
        { kErrorInsufficientBuffer, "Insufficient buffer" },
        { kErrorInvalidLevel, "Invalid level" },
        { kErrorNoVolumeLabel, "No volume label" },
        { kErrorModNotFound, "Module not found" },
        { kErrorWaitNoChildren, "Wait no children" },
        { kErrorChildNotComplete, "Child not complete" },
        { kErrorDirectAccessHandle, "Direct access handle" },
        { kErrorNegativeSeek, "Negative seek" },
        { kErrorSeekOnDevice, "Seek on device" },
        { kErrorIsJoinTarget, "Is join target" },
        { kErrorIsJoined, "Is joined" },
        { kErrorIsSubsted, "Is substed" },
        { kErrorNotJoined, "Not joined" },
        { kErrorNotSubsted, "Not substed" },
        { kErrorJoinToJoin, "Join to join" },
        { kErrorSubstToSubst, "Subst to subst" },
        { kErrorJoinToSubst, "Join to subst" },
        { kErrorSubstToJoin, "Subst to join" },
        { kErrorBusyDrive, "Busy drive" },
        { kErrorSameDrive, "Same drive" },
        { kErrorDirNotRoot, "Directory not root" },
        { kErrorDirNotEmpty, "Directory not empty" },
        { kErrorIsSubstPath, "Is subst path" },
        { kErrorIsJoinPath, "Is join path" },
        { kErrorPathBusy, "Path busy" },
        { kErrorIsSubstTarget, "Is subst target" },
        { kErrorSystemTrace, "System trace" },
        { kErrorInvalidEventCount, "Invalid event count" },
        { kErrorTooManyMuxwaiters, "Too many muxwaiters" },
        { kErrorInvalidListFormat, "Invalid list format" },
        { kErrorLabelTooLong, "Label too long" },
        { kErrorTooManyTcbs, "Too many TCBs" },
        { kErrorSignalRefused, "Signal refused" },
        { kErrorDiscarded, "Discarded" },
        { kErrorNotLocked, "Not locked" },
        { kErrorBadThreadidAddr, "Bad thread ID addr" },
        { kErrorBadArguments, "Bad arguments" },
        { kErrorBadPathname, "Bad pathname" },
        { kErrorSignalPending, "Signal pending" },
        { kErrorMaxThrdsReached, "Max thrds reached" },
        { kErrorLockFailed, "Lock failed" },
        { kErrorBusy, "Busy" },
        { kErrorCancelViolation, "Cancel violation" },
        { kErrorAtomicLocksNotSupported, "Atomic locks not supported" },
        { kErrorInvalidSegmentNumber, "Invalid segment number" },
        { kErrorInvalidOrdinal, "Invalid ordinal" },
        { kErrorInvalidFlagNumber, "Invalid flag number" },
        { kErrorSemNotFound, "Sem not found" },
        { kErrorInvalidStartingCodeseg, "Invalid starting codeseg" },
        { kErrorInvalidStackseg, "Invalid stackseg" },
        { kErrorInvalidModuletype, "Invalid moduletype" },
        { kErrorInvalidExeSignature, "Invalid exe signature" },
        { kErrorExeMarkedInvalid, "Exe marked invalid" },
        { kErrorBadExeFormat, "Bad exe format" },
        { kErrorIteratedDataExceeds64k, "Iterated data exceeds 64k (and that should be enough for anybody!)" },
        { kErrorInvalidMinallocsize, "Invalid minallocsize" },
        { kErrorDynlinkFromInvalidRing, "Dynlink from invalid ring" },
        { kErrorIoplNotEnabled, "IOPL not enabled" },
        { kErrorInvalidSegdpl, "Invalid segdpl" },
        { kErrorAutodatasegExceeds64k, "Autodataseg exceeds 64k" },
        { kErrorRing2segMustBeMovable, "Ring2seg must be movable" },
        { kErrorRelocChainXeedsSeglim, "Reloc chain exceeds seglim" },
        { kErrorInfloopInRelocChain, "Infloop in reloc chain" },
        { kErrorEnvvarNotFound, "Env var not found" },
        { kErrorNoSignalSent, "No signal sent" },
        { kErrorFilenameExcedRange, "Filename exceeds range" },
        { kErrorRing2StackInUse, "Ring2 stack in use" },
        { kErrorMetaExpansionTooLong, "Meta expansion too long" },
        { kErrorInvalidSignalNumber, "Invalid signal number" },
        { kErrorThread1Inactive, "Thread 1 inactive" },
        { kErrorLocked, "Locked" },
        { kErrorTooManyModules, "Too many modules" },
        { kErrorNestingNotAllowed, "Nesting not allowed" },
        { kErrorExeMachineTypeMismatch, "Exe machine type mismatch" },
        { kErrorBadPipe, "Bad pipe" },
        { kErrorPipeBusy, "Pipe busy" },
        { kErrorNoData, "No data" },
        { kErrorPipeNotConnected, "Pipe not connected" },
        { kErrorMoreData, "More data" },
        { kErrorVcDisconnected, "VC disconnected" },
        { kErrorInvalidEaName, "Invalid EA name" },
        { kErrorEaListInconsistent, "EA list inconsistent" },
        { kWaitTimeout, "Wait timeout" },
        { kErrorNoMoreItems, "No more items" },
        { kErrorCannotCopy, "Cannot copy" },
        { kErrorEasDidntFit, "EAS didnt fit" },
        { kErrorEaFileCorrupt, "EA file corrupt" },
        { kErrorEaTableFull, "EA table full" },
        { kErrorInvalidEaHandle, "Invalid EA handle" },
        { kErrorEasNotSupported, "EAs not supported" },
        { kErrorNotOwner, "Not owner" },
        { kErrorTooManyPosts, "Too many posts" },
        { kErrorPartialCopy, "Partial copy" },
        { kErrorOplockNotGranted, "Oplock not granted" },
        { kErrorInvalidOplockProtocol, "Invalid oplock protocol" },
        { kErrorDiskTooFragmented, "Disk too fragmented" },
        { kErrorDeletePending, "Delete pending" },
        { kErrorMrMidNotFound, "Mr Mid not found" },
        { kErrorInvalidAddress, "Invalid address" },
        { kErrorArithmeticOverflow, "Arithmetic overflow" },
        { kErrorPipeConnected, "Pipe connected" },
        { kErrorPipeListening, "Pipe listening" },
        { kErrorEaAccessDenied, "EA access denied" },
        { kErrorOperationAborted, "Operation aborted" },
        { kErrorIoIncomplete, "IO incomplete" },
        { kErrorIoPending, "IO pending" },
        { kErrorNoaccess, "No access" },
        { kErrorSwaperror, "Swap error" },
        { kErrorStackOverflow, "Stack overflow" },
        { kErrorInvalidMessage, "Invalid message" },
        { kErrorCanNotComplete, "Can not complete" },
        { kErrorInvalidFlags, "Invalid flags" },
        { kErrorUnrecognizedVolume, "Unrecognised volume" },
        { kErrorFileInvalid, "File invalid" },
        { kErrorFullscreenMode, "Full screen mode" },
        { kErrorNoToken, "No token" },
        { kErrorBaddb, "Bad DB" },
        { kErrorBadkey, "Bad key" },
        { kErrorCantopen, "Can't open" },
        { kErrorCantread, "Can't read" },
        { kErrorCantwrite, "Can't write" },
        { kErrorRegistryRecovered, "Registry recovered" },
        { kErrorRegistryCorrupt, "Registry corrupt" },
        { kErrorRegistryIoFailed, "Registry IO failed" },
        { kErrorNotRegistryFile, "Not registry file" },
        { kErrorKeyDeleted, "Key deleted" },
        { kErrorNoLogSpace, "No log space" },
        { kErrorKeyHasChildren, "Key has children" },
        { kErrorChildMustBeVolatile, "Child must be volatile" },
        { kErrorNotifyEnumDir, "Notify enum dir" },
        { kErrorDependentServicesRunning, "Dependent services running" },
        { kErrorInvalidServiceControl, "Invalid service control" },
        { kErrorServiceRequestTimeout, "Service request timeout" },
        { kErrorServiceNoThread, "Service no thread" },
        { kErrorServiceDatabaseLocked, "Service database locked" },
        { kErrorServiceAlreadyRunning, "Service already running" },
        { kErrorInvalidServiceAccount, "Invalid service account" },
        { kErrorServiceDisabled, "Service disabled" },
        { kErrorCircularDependency, "Circular dependency" },
        { kErrorServiceDoesNotExist, "Service does not exist" },
        { kErrorServiceCannotAcceptCtrl, "Service cannot accept ctrl" },
        { kErrorServiceNotActive, "Service not active" },
        { kErrorFailedServiceControllerConnect, "Failed service controller connect" },
        { kErrorExceptionInService, "Exception in service" },
        { kErrorDatabaseDoesNotExist, "Database does not exist" },
        { kErrorServiceSpecificError, "Service specific error" },
        { kErrorProcessAborted, "Process aborted" },
        { kErrorServiceDependencyFail, "Service dependency fail" },
        { kErrorServiceLogonFailed, "Service logon failed" },
        { kErrorServiceStartHang, "Service start hang" },
        { kErrorInvalidServiceLock, "Invalid service lock" },
        { kErrorServiceMarkedForDelete, "Service marked for delete" },
        { kErrorServiceExists, "Service exists" },
        { kErrorAlreadyRunningLkg, "Already running lkg" },
        { kErrorServiceDependencyDeleted, "Service dependency deleted" },
        { kErrorBootAlreadyAccepted, "Boot already accepted" },
        { kErrorServiceNeverStarted, "Service never started" },
        { kErrorDuplicateServiceName, "Duplicate service name" },
        { kErrorDifferentServiceAccount, "Different service account" },
        { kErrorCannotDetectDriverFailure, "Cannot detect driver failure" },
        { kErrorCannotDetectProcessAbort, "Cannot detect process abort" },
        { kErrorNoRecoveryProgram, "No recovery program" },
        { kErrorServiceNotInExe, "Service not in exe" },
        { kErrorNotSafebootService, "Not safeboot service" },
        { kErrorEndOfMedia, "End of media" },
        { kErrorFilemarkDetected, "Filemark detected" },
        { kErrorBeginningOfMedia, "Beginning of media" },
        { kErrorSetmarkDetected, "Setmark detected" },
        { kErrorNoDataDetected, "No data detected" },
        { kErrorPartitionFailure, "Partition failure" },
        { kErrorInvalidBlockLength, "Invalid block length" },
        { kErrorDeviceNotPartitioned, "Device not partitioned" },
        { kErrorUnableToLockMedia, "Unable to lock media" },
        { kErrorUnableToUnloadMedia, "Unable to unload media" },
        { kErrorMediaChanged, "Media changed" },
        { kErrorBusReset, "Bus reset" },
        { kErrorNoMediaInDrive, "No media in drive" },
        { kErrorNoUnicodeTranslation, "No unicode translation" },
        { kErrorDllInitFailed, "DLL init failed" },
        { kErrorShutdownInProgress, "Shutdown in progress" },
        { kErrorNoShutdownInProgress, "No shutdown in progress" },
        { kErrorIoDevice, "IO device" },
        { kErrorSerialNoDevice, "Serial IO device" },
        { kErrorIrqBusy, "IRQ busy" },
        { kErrorMoreWrites, "More writes" },
        { kErrorCounterTimeout, "Counter timeout" },
        { kErrorFloppyIdMarkNotFound, "Floppy ID mark not found" },
        { kErrorFloppyWrongCylinder, "Floppy wrong cylinder" },
        { kErrorFloppyUnknownError, "Floppy unknown error" },
        { kErrorFloppyBadRegisters, "Floppy bad registers" },
        { kErrorDiskRecalibrateFailed, "Disk recalibrate failed" },
        { kErrorDiskOperationFailed, "Disk operation failed" },
        { kErrorDiskResetFailed, "Disk reset failed" },
        { kErrorEomOverflow, "EOM overflow" },
        { kErrorNotEnoughServerMemory, "Not enough server memory" },
        { kErrorPossibleDeadlock, "Possible deadlock" },
        { kErrorMappedAlignment, "Mapped alignment" },
        { kErrorSetPowerStateVetoed, "Set power state vetoed" },
        { kErrorSetPowerStateFailed, "Set power state failed" },
        { kErrorTooManyLinks, "Too many links" },
        { kErrorOldWinVersion, "Old win version" },
        { kErrorAppWrongOs, "App wrong OS" },
        { kErrorSingleInstanceApp, "Single instance app" },
        { kErrorRmodeApp, "Rmode app" },
        { kErrorInvalidDll, "Invalid DLL" },
        { kErrorNoAssociation, "No association" },
        { kErrorDdeFail, "DDE fail" },
        { kErrorDllNotFound, "DLL not found" },
        { kErrorNoMoreUserHandles, "No more user handles" },
        { kErrorMessageSyncOnly, "Message sync only" },
        { kErrorSourceElementEmpty, "Source element empty" },
        { kErrorDestinationElementFull, "Destination element full" },
        { kErrorIllegalElementAddress, "Illegal element address" },
        { kErrorMagazineNotPresent, "Magazine not present" },
        { kErrorDeviceReinitializationNeeded, "Device reinitialization needed" },
        { kErrorDeviceRequiresCleaning, "Device requires cleaning" },
        { kErrorDeviceDoorOpen, "Device door open" },
        { kErrorDeviceNotConnected, "Device not connected" },
        { kErrorNotFound, "Not found" },
        { kErrorNoMatch, "No match" },
        { kErrorSetNotFound, "Set not found" },
        { kErrorPointNotFound, "Point not found" },
        { kErrorNoTrackingService, "No tracking service" },
        { kErrorNoVolumeId, "No volume ID" },
        { kErrorUnableToRemoveReplaced, "Unable to remove replaced" },
        { kErrorUnableToMoveReplacement, "Unable to move replacement" },
        { kErrorUnableToMoveReplacement2, "Unable to move replacement 2" },
        { kErrorJournalDeleteInProgress, "Journal delete in progress" },
        { kErrorJournalNotActive, "Journal not active" },
        { kErrorPotentialFileFound, "Potential file found" },
        { kErrorJournalEntryDeleted, "Journal entry deleted" },
        { kErrorBadDevice, "Bad device" },
        { kErrorConnectionUnavail, "Connection unavail" },
        { kErrorDeviceAlreadyRemembered, "Device already remembered" },
        { kErrorNoNetOrBadPath, "No net or bad path" },
        { kErrorBadProvider, "Bad provider" },
        { kErrorCannotOpenProfile, "Cannot open profile" },
        { kErrorBadProfile, "Bad profile" },
        { kErrorNotContainer, "Not container" },
        { kErrorExtendedError, "Extended error" },
        { kErrorInvalidGroupname, "Invalid group name" },
        { kErrorInvalidComputername, "Invalid computer name" },
        { kErrorInvalidEventname, "Invalid event name" },
        { kErrorInvalidDomainname, "Invalid domain name" },
        { kErrorInvalidServicename, "Invalid service name" },
        { kErrorInvalidNetname, "Invalid net name" },
        { kErrorInvalidSharename, "Invalid share name" },
        { kErrorInvalidPasswordname, "Invalid password name" },
        { kErrorInvalidMessagename, "Invalid message name" },
        { kErrorInvalidMessagedest, "Invalid message dest" },
        { kErrorSessionCredentialConflict, "Session credential conflict" },
        { kErrorRemoteSessionLimitExceeded, "Remote session limit exceeded" },
        { kErrorDupDomainname, "Dup domain name" },
        { kErrorNoNetwork, "No network" },
        { kErrorCancelled, "Cancelled" },
        { kErrorUserMappedFile, "User mapped file" },
        { kErrorConnectionRefused, "Connection refused" },
        { kErrorGracefulDisconnect, "Graceful disconnect" },
        { kErrorAddressAlreadyAssociated, "Address already associated" },
        { kErrorAddressNotAssociated, "Address not associated" },
        { kErrorConnectionInvalid, "Connected invalid" },
        { kErrorConnectionActive, "Connection active" },
        { kErrorNetworkUnreachable, "Network unreachable" },
        { kErrorHostUnreachable, "Host unreachable" },
        { kErrorProtocolUnreachable, "Protocol unreachable" },
        { kErrorPortUnreachable, "Port unreachable" },
        { kErrorRequestAborted, "Request aborted" },
        { kErrorConnectionAborted, "Connection aborted" },
        { kErrorRetry, "Retry" },
        { kErrorConnectionCountLimit, "Connection count limit" },
        { kErrorLoginTimeRestriction, "Login time restriction" },
        { kErrorLoginWkstaRestriction, "Login wksta restriction" },
        { kErrorIncorrectAddress, "Incorrect address" },
        { kErrorAlreadyRegistered, "Already registered" },
        { kErrorServiceNotFound, "Service not found" },
        { kErrorNotAuthenticated, "Not authenticated" },
        { kErrorNotLoggedOn, "Not logged on" },
        { kErrorContinue, "Continue" },
        { kErrorAlreadyInitialized, "Already initialised" },
        { kErrorNoMoreDevices, "No more devices" },
        { kErrorNoSuchSite, "No such site" },
        { kErrorDomainControllerExists, "Domain controller exists" },
        { kErrorOnlyIfConnected, "Only if connected" },
        { kErrorOverrideNochanges, "Override no changes" },
        { kErrorBadUserProfile, "Bad user profile" },
        { kErrorNotSupportedOnSbs, "Not supported on SBS" },
        { kErrorServerShutdownInProgress, "Server shutdown in progress" },
        { kErrorHostDown, "Host down" },
        { kErrorNonAccountSid, "Non account sid" },
        { kErrorNonDomainSid, "Non domain sid" },
        { kErrorApphelpBlock, "Apphelp block" },
        { kErrorAccessDisabledByPolicy, "Access disabled by policy" },
        { kErrorRegNatConsumption, "Reg nat consumption" },
        { kErrorCscshareOffline, "CSC share offline" },
        { kErrorPkinitFailure, "PK init failure" },
        { kErrorSmartcardSubsystemFailure, "Smartcard subsystem failure" },
        { kErrorDowngradeDetected, "Downgrade detected" },
        { kSecESmartcardCertRevoked, "Smartcard cert revoked" },
        { kSecEIssuingCaUntrusted, "Issuing CA untrusted" },
        { kSecERevocationOfflineC, "Revocation offline" },
        { kSecEPkinitClientFailur, "PK init client failure" },
        { kSecESmartcardCertExpired, "Smartcard cert expired" },
        { kErrorMachineLocked, "Machine locked" },
        { kErrorCallbackSuppliedInvalidData, "Callback supplied invalid data" },
        { kErrorSyncForegroundRefreshRequired, "Sync foreground refresh required" },
        { kErrorDriverBlocked, "Driver blocked" },
        { kErrorInvalidImportOfNonDll, "Invalid import of non DLL" },
        { kErrorNotAllAssigned, "Not all assigned" },
        { kErrorSomeNotMapped, "Some not mapped" },
        { kErrorNoQuotasForAccount, "No quotas for account" },
        { kErrorLocalUserSessionKey, "Local user session key" },
        { kErrorNullLmPassword, "Null LM password" },
        { kErrorUnknownRevision, "Unknown revision" },
        { kErrorRevisionMismatch, "Revision mismatch" },
        { kErrorInvalidOwner, "Invalid owner" },
        { kErrorInvalidPrimaryGroup, "Invalid primary group" },
        { kErrorNoImpersonationToken, "No impersonation token" },
        { kErrorCantDisableMandatory, "Can't disable mandatory" },
        { kErrorNoLogonServers, "No logon servers" },
        { kErrorNoSuchLogonSession, "No such logon session" },
        { kErrorNoSuchPrivilege, "No such privilege" },
        { kErrorPrivilegeNotHeld, "Privilege not held" },
        { kErrorInvalidAccountName, "Invalid account name" },
        { kErrorUserExists, "User exists" },
        { kErrorNoSuchUser, "No such user" },
        { kErrorGroupExists, "Group exists" },
        { kErrorNoSuchGroup, "No such group" },
        { kErrorMemberInGroup, "Member in group" },
        { kErrorMemberNotInGroup, "Member not in group" },
        { kErrorLastAdmin, "Last admin" },
        { kErrorWrongPassword, "Wrong password" },
        { kErrorIllFormedPassword, "Ill formed password" },
        { kErrorPasswordRestriction, "Password restriction" },
        { kErrorLogonFailure, "Logon failure" },
        { kErrorAccountRestriction, "Account restriction" },
        { kErrorInvalidLogonHours, "Invalid logon hours" },
        { kErrorInvalidWorkstation, "Invalid workstation" },
        { kErrorPasswordExpired, "Password expired" },
        { kErrorAccountDisabled, "Account disabled" },
        { kErrorNoneMapped, "None mapped" },
        { kErrorTooManyLuidsRequested, "Too many LUIDs requested" },
        { kErrorLuidsExhausted, "LUIDs exhausted" },
        { kErrorInvalidSubAuthority, "Invalid sub authority" },
        { kErrorInvalidAcl, "Invalid ACL" },
        { kErrorInvalidSid, "Invalid SID" },
        { kErrorInvalidSecurityDescr, "Invalid security descr" },
        { kErrorBadInheritanceAcl, "Bad inheritance ACL" },
        { kErrorServerDisabled, "Server disabled" },
        { kErrorServerNotDisabled, "Server not disabled" },
        { kErrorInvalidIdAuthority, "Invalid ID authority" },
        { kErrorAllottedSpaceExceeded, "Allotted space exceeded" },
        { kErrorInvalidGroupAttributes, "Invalid group attributes" },
        { kErrorBadImpersonationLevel, "Bad impersonation level" },
        { kErrorCantOpenAnonymous, "Can't open anonymous" },
        { kErrorBadValidationClass, "Bad validation class" },
        { kErrorBadTokenType, "Bad token type" },
        { kErrorNoSecurityOnObject, "No security on object" },
        { kErrorCantAccessDomainInfo, "Can't access domain info" },
        { kErrorInvalidServerState, "Invalid server state" },
        { kErrorInvalidDomainState, "Invalid domain state" },
        { kErrorInvalidDomainRole, "Invalid domain role" },
        { kErrorNoSuchDomain, "No such domain" },
        { kErrorDomainExists, "Domain exists" },
        { kErrorDomainLimitExceeded, "Domain limit exceeded" },
        { kErrorInternalDbCorruption, "Internal DB corruption" },
        { kErrorInternalError, "Internal error" },
        { kErrorGenericNotMapped, "Generic not mapped" },
        { kErrorBadDescriptorFormat, "Bad descriptor format" },
        { kErrorNotLogonProcess, "Not logon process" },
        { kErrorLogonSessionExists, "Logon session exists" },
        { kErrorNoSuchPackage, "No such package" },
        { kErrorBadLogonSessionState, "Bad logon session state" },
        { kErrorLogonSessionCollision, "Logon session collision" },
        { kErrorInvalidLogonType, "Invalid logon type" },
        { kErrorCannotImpersonate, "Cannot impersonate" },
        { kErrorRxactInvalidState, "Rxact invalid state" },
        { kErrorRxactCommitFailure, "Rxact commit failure" },
        { kErrorSpecialAccount, "Special account" },
        { kErrorSpecialGroup, "Special group" },
        { kErrorSpecialUser, "Special user" },
        { kErrorMembersPrimaryGroup, "Members primary group" },
        { kErrorTokenAlreadyInUse, "Token already in use" },
        { kErrorNoSuchAlias, "No such alias" },
        { kErrorMemberNotInAlias, "Member not in alias" },
        { kErrorMemberInAlias, "Member in alias" },
        { kErrorAliasExists, "Alias exists" },
        { kErrorLogonNotGranted, "Logon not granted" },
        { kErrorTooManySecrets, "Too many secrets" },
        { kErrorSecretTooLong, "Secret too long" },
        { kErrorInternalDbError, "Internal DB error" },
        { kErrorTooManyContextIds, "Too many context IDs" },
        { kErrorLogonTypeNotGranted, "Logon type not granted" },
        { kErrorNtCrossEncryptionRequired, "NT cross encryption required" },
        { kErrorNoSuchMember, "No such member" },
        { kErrorInvalidMember, "Invalid member" },
        { kErrorTooManySids, "Too many SIDs" },
        { kErrorLmCrossEncryptionRequired, "LM cross encryption required" },
        { kErrorNoInheritance, "No inheritance" },
        { kErrorFileCorrupt, "File corrupt" },
        { kErrorDiskCorrupt, "Disk corrupt" },
        { kErrorNoUserSessionKey, "No user session key" },
        { kErrorLicenseQuotaExceeded, "Licence quota exceeded" },
        { kErrorWrongTargetName, "Wrong target name" },
        { kErrorMutualAuthFailed, "Mutual auth failed" },
        { kErrorTimeSkew, "Time skew" },
        { kErrorCurrentDomainNotAllowed, "Current domain not allowed" },
        { kErrorInvalidWindowHandle, "Invalid window handle" },
        { kErrorInvalidMenuHandle, "Invalid menu handle" },
        { kErrorInvalidCursorHandle, "Invalid cursor handle" },
        { kErrorInvalidAccelHandle, "Invalid accel handle" },
        { kErrorInvalidHookHandle, "Invalid hook handle" },
        { kErrorInvalidDwpHandle, "Invalid DWP handle" },
        { kErrorTlwWithWschild, "TLW with wschild" },
        { kErrorCannotFindWndClass, "Cannot find WND class" },
        { kErrorWindowOfOtherThread, "Window of other thread" },
        { kErrorHotkeyAlreadyRegistered, "Hotkey already registered" },
        { kErrorClassAlreadyExists, "Class already exists" },
        { kErrorClassDoesNotExist, "Class does not exist" },
        { kErrorClassHasWindows, "Class has windows" },
        { kErrorInvalidIndex, "Invalid index" },
        { kErrorInvalidIconHandle, "Invalid icon handle" },
        { kErrorPrivateDialogIndex, "Private dialog index" },
        { kErrorListboxIdNotFound, "Listbox ID not found" },
        { kErrorNoWildcardCharacters, "No wildcard characters" },
        { kErrorClipboardNotOpen, "Clipboard not open" },
        { kErrorHotkeyNotRegistered, "Hotkey not registered" },
        { kErrorWindowNotDialog, "Window not dialog" },
        { kErrorControlIdNotFound, "Control ID not found" },
        { kErrorInvalidComboboxMessage, "Invalid combobox message" },
        { kErrorWindowNotCombobox, "Window not combobox" },
        { kErrorInvalidEditHeight, "Invalid edit height" },
        { kErrorDcNotFound, "DC not found" },
        { kErrorInvalidHookFilter, "Invalid hook filter" },
        { kErrorInvalidFilterProc, "Invalid filter proc" },
        { kErrorHookNeedsHmod, "Hook needs HMOD" },
        { kErrorGlobalOnlyHook, "Global only hook" },
        { kErrorJournalHookSet, "Journal hook set" },
        { kErrorHookNotInstalled, "Hook not installed" },
        { kErrorInvalidLbMessage, "Invalid LB message" },
        { kErrorSetcountOnBadLb, "Setcount on bad LB" },
        { kErrorLbWithoutTabstops, "LB without tabstops" },
        { kErrorDestroyObjectOfOtherThread, "Destroy object of other thread" },
        { kErrorChildWindowMenu, "Child window menu" },
        { kErrorNoSystemMenu, "No system menu" },
        { kErrorInvalidMsgboxStyle, "Invalid msgbox style" },
        { kErrorInvalidSpiValue, "Invalid SPI value" },
        { kErrorScreenAlreadyLocked, "Screen already locked" },
        { kErrorHwndsHaveDiffParent, "HWNDs have different parent" },
        { kErrorNotChildWindow, "Not child window" },
        { kErrorInvalidGwCommand, "Invalid GW command" },
        { kErrorInvalidThreadId, "Invalid thread ID" },
        { kErrorNonMdichildWindow, "Non MDI child window" },
        { kErrorPopupAlreadyActive, "Popup already active" },
        { kErrorNoScrollbars, "No scrollbars" },
        { kErrorInvalidScrollbarRange, "Invalid scrollbar range" },
        { kErrorInvalidShowwinCommand, "Invalid showwin command" },
        { kErrorNoSystemResources, "No system resources" },
        { kErrorNonpagedSystemResources, "Nonpaged system resources" },
        { kErrorPagedSystemResources, "Paged system resources" },
        { kErrorWorkingSetQuota, "Working set quota" },
        { kErrorPagefileQuota, "Pagefile quota" },
        { kErrorCommitmentLimit, "Commitment limit" },
        { kErrorMenuItemNotFound, "Menu item not found" },
        { kErrorInvalidKeyboardHandle, "Invalid keyboard handle" },
        { kErrorHookTypeNotAllowed, "Hook type not allowed" },
        { kErrorRequiresInteractiveWindowstation, "Requires interactive windowstation" },
        { kErrorTimeout, "Timeout" },
        { kErrorInvalidMonitorHandle, "Invalid monitor handle" },
        { kErrorEventlogFileCorrupt, "Eventlog file corrupt" },
        { kErrorEventlogCantStart, "Eventlog can't start" },
        { kErrorLogFileFull, "Log file full" },
        { kErrorEventlogFileChanged, "Eventlog file changed" },
        { kErrorInstallServiceFailure, "Install service failure" },
        { kErrorInstallUserexit, "Install userexit" },
        { kErrorInstallFailure, "Install failure" },
        { kErrorInstallSuspend, "Install suspend" },
        { kErrorUnknownProduct, "Unknown product" },
        { kErrorUnknownFeature, "Unknown feature" },
        { kErrorUnknownComponent, "Unknown component" },
        { kErrorUnknownProperty, "Unknown property" },
        { kErrorInvalidHandleState, "Invalid handle state" },
        { kErrorBadConfiguration, "Bad configuration" },
        { kErrorIndexAbsent, "Index absent" },
        { kErrorInstallSourceAbsent, "Install source absent" },
        { kErrorInstallPackageVersion, "Install package version" },
        { kErrorProductUninstalled, "Product uninstalled" },
        { kErrorBadQuerySyntax, "Bad query syntax" },
        { kErrorInvalidField, "Invalid field" },
        { kErrorDeviceRemoved, "Device removed" },
        { kErrorInstallAlreadyRunning, "Install already running" },
        { kErrorInstallPackageOpenFailed, "Install package open failed" },
        { kErrorInstallPackageInvalid, "Install package invalid" },
        { kErrorInstallUiFailure, "Install UI failure" },
        { kErrorInstallLogFailure, "Install log failure" },
        { kErrorInstallLanguageUnsupported, "Install language unsupported" },
        { kErrorInstallTransformFailure, "Install transform failure" },
        { kErrorInstallPackageRejected, "Install package rejected" },
        { kErrorFunctionNotCalled, "Function not called" },
        { kErrorFunctionFailed, "Function failed" },
        { kErrorInvalidTable, "Invalid table" },
        { kErrorDatatypeMismatch, "Datatype mismatch" },
        { kErrorUnsupportedType, "Unsupported type" },
        { kErrorCreateFailed, "Create failed" },
        { kErrorInstallTempUnwritable, "Install temp unwritable" },
        { kErrorInstallPlatformUnsupported, "Install platform unsupported" },
        { kErrorInstallNotused, "Install notused" },
        { kErrorPatchPackageOpenFailed, "Patch package open failed" },
        { kErrorPatchPackageInvalid, "Patch package invalid" },
        { kErrorPatchPackageUnsupported, "Patch package unsupported" },
        { kErrorProductVersion, "Product version" },
        { kErrorInvalidCommandLine, "Invalid command line" },
        { kErrorInstallRemoteDisallowed, "Install remote disallowed" },
        { kErrorSuccessRebootInitiated, "Success reboot initiated" },
        { kErrorPatchTargetNotFound, "Patch target not found" },
        { kErrorPatchPackageRejected, "Patch package rejected" },
        { kErrorInstallTransformRejected, "Install transform rejected" },
        { kRpcSInvalidStringBinding, "RPC S Invalid string binding" },
        { kRpcSWrongKindOfBinding, "RPC S Wrong kind of binding" },
        { kRpcSInvalidBinding, "RPC S Invalid binding" },
        { kRpcSProtseqNotSupported, "RPC S Protseq not supported" },
        { kRpcSInvalidRpcProtseq, "RPC S Invalid RPC protseq" },
        { kRpcSInvalidStringUuid, "RPC S Invalid string UUID" },
        { kRpcSInvalidEndpointFormat, "RPC S Invalid endpoint format" },
        { kRpcSInvalidNetAddr, "RPC S Invalid net addr" },
        { kRpcSNoEndpointFound, "RPC S No endpoint found" },
        { kRpcSInvalidTimeout, "RPC S Invalid timeout" },
        { kRpcSObjectNotFound, "RPC S Object not found" },
        { kRpcSAlreadyRegistered, "RPC S Already registered" },
        { kRpcSTypeAlreadyRegistered, "RPC S Type already registered" },
        { kRpcSAlreadyListening, "RPC S Already listening" },
        { kRpcSNoProtseqsRegistered, "RPC S Not protseqs registered" },
        { kRpcSNotListening, "RPC S Not listening" },
        { kRpcSUnknownMgrType, "RPC S Unknown mgr type" },
        { kRpcSUnknownIf, "RPC S Unknown IF" },
        { kRpcSNoBindings, "RPC S No bindings" },
        { kRpcSNoProtseqs, "RPC S Not protseqs" },
        { kRpcSCantCreateEndpoint, "RPC S Can't create endpoint" },
        { kRpcSOutOfResources, "RPC S Out of resources" },
        { kRpcSServerUnavailable, "RPC S Server unavailable" },
        { kRpcSServerTooBusy, "RPC S Server too busy" },
        { kRpcSInvalidNetworkOptions, "RPC S Invalid network options" },
        { kRpcSNoCallActive, "RPC S No call active" },
        { kRpcSCallFailed, "RPC S Call failed" },
        { kRpcSCallFailedDne, "RPC S Call failed DNE" },
        { kRpcSProtocolError, "RPC S Protocol error" },
        { kRpcSUnsupportedTransSyn, "RPC S Unsupported trans syn" },
        { kRpcSUnsupportedType, "RPC S Unsupported type" },
        { kRpcSInvalidTag, "RPC S Invalid tag" },
        { kRpcSInvalidBound, "RPC S Invalid bound" },
        { kRpcSNoEntryName, "RPC S No entry name" },
        { kRpcSInvalidNameSyntax, "RPC S Invalid name syntax" },
        { kRpcSUnsupportedNameSyntax, "RPC S Unsupported name syntax" },
        { kRpcSUuidNoAddress, "RPC S UUID no address" },
        { kRpcSDuplicateEndpoint, "RPC S Duplicate endpoint" },
        { kRpcSUnknownAuthnType, "RPC S Unknown authn type" },
        { kRpcSMaxCallsTooSmall, "RPC S Max calls too small" },
        { kRpcSStringTooLong, "RPC S String too long" },
        { kRpcSProtseqNotFound, "RPC S Protseq not found" },
        { kRpcSProcnumOutOfRange, "RPC S Procnum out of range" },
        { kRpcSBindingHasNoAuth, "RPC S Binding has no auth" },
        { kRpcSUnknownAuthnService, "RPC S Unknown authn service" },
        { kRpcSUnknownAuthnLevel, "RPC S Unknown authn level" },
        { kRpcSInvalidAuthIdentity, "RPC S Invalid auth identity" },
        { kRpcSUnknownAuthzService, "RPC S Unknown authz service" },
        { kEptSInvalidEntry, "EPT S Invalid entry" },
        { kEptSCantPerformOp, "EPT S Can't perform op" },
        { kEptSNotRegistered, "EPT S Not registered" },
        { kRpcSNothingToExport, "RPC S Nothing to export" },
        { kRpcSIncompleteName, "RPC S Incomplete name" },
        { kRpcSInvalidVersOption, "RPC S Invalid vers option" },
        { kRpcSNoMoreMembers, "RPC S No more members" },
        { kRpcSNotAllObjsUnexported, "RPC S Not all objs unexported" },
        { kRpcSInterfaceNotFound, "RPC S Interface not found" },
        { kRpcSEntryAlreadyExists, "RPC S Entry already exists" },
        { kRpcSEntryNotFound, "RPC S Entry not found" },
        { kRpcSNameServiceUnavailable, "RPC S Name service unavailable" },
        { kRpcSInvalidNafId, "RPC S Invalid naf ID" },
        { kRpcSCannotSupport, "RPC S Cannot support" },
        { kRpcSNoContextAvailable, "RPC S No context available" },
        { kRpcSInternalError, "RPC S Internal error" },
        { kRpcSZeroDivide, "RPC S Zero divide" },
        { kRpcSAddressError, "RPC S Address error" },
        { kRpcSFpDivZero, "RPC S FP div zero" },
        { kRpcSFpUnderflow, "RPC S FP Underflow" },
        { kRpcSFpOverflow, "RPC S Overflow" },
        { kRpcXNoMoreEntries, "RPC X No more entries" },
        { kRpcXSsCharTransOpenFail, "RPC X SS char trans open fail" },
        { kRpcXSsCharTransShortFile, "RPC X SS char trans short file" },
        { kRpcXSsInNullContext, "RPC S SS in null context" },
        { kRpcXSsContextDamaged, "RPC X SS context damaged" },
        { kRpcXSsHandlesMismatch, "RPC X SS handles mismatch" },
        { kRpcXSsCannotGetCallHandle, "RPC X SS cannot get call handle" },
        { kRpcXNullRefPointer, "RPC X Null ref pointer" },
        { kRpcXEnumValueOutOfRange, "RPC X enum value out of range" },
        { kRpcXByteCountTooSmall, "RPC X byte count too small" },
        { kRpcXBadStubData, "RPC X bad stub data" },
        { kErrorInvalidUserBuffer, "Invalid user buffer" },
        { kErrorUnrecognizedMedia, "Unrecognised media" },
        { kErrorNoTrustLsaSecret, "No trust lsa secret" },
        { kErrorNoTrustSamAccount, "No trust sam account" },
        { kErrorTrustedDomainFailure, "Trusted domain failure" },
        { kErrorTrustedRelationshipFailure, "Trusted relationship failure" },
        { kErrorTrustFailure, "Trust failure" },
        { kRpcSCallInProgress, "RPC S call in progress" },
        { kErrorNetlogonNotStarted, "Error netlogon not started" },
        { kErrorAccountExpired, "Account expired" },
        { kErrorRedirectorHasOpenHandles, "Redirector has open handles" },
        { kErrorPrinterDriverAlreadyInstalled, "Printer driver already installed" },
        { kErrorUnknownPort, "Unknown port" },
        { kErrorUnknownPrinterDriver, "Unknown printer driver" },
        { kErrorUnknownPrintprocessor, "Unknown printprocessor" },
        { kErrorInvalidSeparatorFile, "Invalid separator file" },
        { kErrorInvalidPriority, "Invalid priority" },
        { kErrorInvalidPrinterName, "Invalid printer name" },
        { kErrorPrinterAlreadyExists, "Printer already exists" },
        { kErrorInvalidPrinterCommand, "Invalid printer command" },
        { kErrorInvalidDatatype, "Invalid datatype" },
        { kErrorInvalidEnvironment, "Invalid environment" },
        { kRpcSNoMoreBindings, "RPC S no more bindings" },
        { kErrorNologonInterdomainTrustAccount, "Nologon interdomain trust account" },
        { kErrorNologonWorkstationTrustAccount, "Nologon workstation trust account" },
        { kErrorNologonServerTrustAccount, "Nologon server trust account" },
        { kErrorDomainTrustInconsistent, "Domain trust inconsistent" },
        { kErrorServerHasOpenHandles, "Server has open handles" },
        { kErrorResourceDataNotFound, "Resource data not found" },
        { kErrorResourceTypeNotFound, "Resource type not found" },
        { kErrorResourceNameNotFound, "Resource name not found" },
        { kErrorResourceLangNotFound, "Resource lang not found" },
        { kErrorNotEnoughQuota, "Not enough quota" },
        { kRpcSNoInterfaces, "RPC S no interfaces" },
        { kRpcSCallCancelled, "RPC S Call cancelled" },
        { kRpcSBindingIncomplete, "RPC S Binding incomplete" },
        { kRpcSCommFailure, "RPC S Comm failure" },
        { kRpcSUnsupportedAuthnLevel, "RPC S Unsupported authn level" },
        { kRpcSNoPrincName, "RPC S No princ name" },
        { kRpcSNotRpcError, "RPC S Not RPC error" },
        { kRpcSUuidLocalOnly, "RPC U UUID local only" },
        { kRpcSSecPkgError, "RPC S Sec pkg error" },
        { kRpcSNotCancelled, "RPC S Not cancelled" },
        { kRpcXInvalidEsAction, "RPC X Invalid ES action" },
        { kRpcXWrongEsVersion, "RPC X Wrong ES version" },
        { kRpcXWrongStubVersion, "RPC X Wrong stub version" },
        { kRpcXInvalidPipeObject, "RPC X Invalid pipe object" },
        { kRpcXWrongPipeOrder, "RPC X Wrong pipe order" },
        { kRpcXWrongPipeVersion, "RPC X Wrong pipe version" },
        { kRpcSGroupMemberNotFound, "RPC S group member not found" },
        { kEptSCantCreate, "EPT S Can't create" },
        { kRpcSInvalidObject, "RPC S Invalid object" },
        { kErrorInvalidTime, "Invalid time" },
        { kErrorInvalidFormName, "Invalid form name" },
        { kErrorInvalidFormSize, "Invalid form size" },
        { kErrorAlreadyWaiting, "Already waiting" },
        { kErrorPrinterDeleted, "Printer deleted" },
        { kErrorInvalidPrinterState, "Invalid printer state" },
        { kErrorPasswordMustChange, "Password must change" },
        { kErrorDomainControllerNotFound, "Domain controller not found" },
        { kErrorAccountLockedOut, "Account locked out" },
        { kOrInvalidOxid, "OR Invalid OXID" },
        { kOrInvalidOid, "OR Invalid OID" },
        { kOrInvalidSet, "OR Invalid set" },
        { kRpcSSendIncomplete, "RPC S Send incomplete" },
        { kRpcSInvalidAsyncHandle, "RPC S Invalid async handle" },
        { kRpcSInvalidAsyncCall, "RPC S Invalid async call" },
        { kRpcXPipeClosed, "RPC X Pipe closed" },
        { kRpcXPipeDisciplineError, "RPC X Pipe discipline error" },
        { kRpcXPipeEmpty, "RPC X Pipe empty" },
        { kErrorNoSitename, "No sitename" },
        { kErrorCantAccessFile, "Can't access file" },
        { kErrorCantResolveFilename, "Can't resolve filename" },
        { kRpcSEntryTypeMismatch, "RPC S Entry type mismatch" },
        { kRpcSNotAllObjsExported, "RPC S Not all objs exported" },
        { kRpcSInterfaceNotExported, "RPC S Interface not exported" },
        { kRpcSProfileNotAdded, "RPC S Profile not added" },
        { kRpcSPrfEltNotAdded, "RPC S PRF ELT not added" },
        { kRpcSPrfEltNotRemoved, "RPC S PRF ELT not removed" },
        { kRpcSGrpEltNotAdded, "RPC S GRP ELT not added" },
        { kRpcSGrpEltNotRemoved, "RPC S GRP ELT not removed" },
        { kErrorKmDriverBlocked, "KM driver blocked" },
        { kErrorContextExpired, "Context expired" },
        { kErrorInvalidPixelFormat, "Invalid pixel format" },
        { kErrorBadDriver, "Bad driver" },
        { kErrorInvalidWindowStyle, "Invalid window style" },
        { kErrorMetafileNotSupported, "Metafile not supported" },
        { kErrorTransformNotSupported, "Transform not supported" },
        { kErrorClippingNotSupported, "Clipping not supported" },
        { kErrorInvalidCmm, "Invalid CMM" },
        { kErrorInvalidProfile, "Invalid profile" },
        { kErrorTagNotFound, "Tag not found" },
        { kErrorTagNotPresent, "Tag not present" },
        { kErrorDuplicateTag, "Duplicate tag" },
        { kErrorProfileNotAssociatedWithDevice, "Profile not associated with device" },
        { kErrorProfileNotFound, "Profile not found" },
        { kErrorInvalidColorspace, "Invalid colorspace" },
        { kErrorIcmNotEnabled, "ICM not enabled" },
        { kErrorDeletingIcmXform, "Deleting ICM xform" },
        { kErrorInvalidTransform, "Invalid transform" },
        { kErrorColorspaceMismatch, "Colorspace mismatch" },
        { kErrorInvalidColorindex, "Invalid colorindex" },
        { kErrorConnectedOtherPassword, "Connected other password" },
        { kErrorConnectedOtherPasswordDefault, "Connected other password default" },
        { kErrorBadUsername, "Bad username" },
        { kErrorNotConnected, "Not connected" },
        { kErrorOpenFiles, "Open files" },
        { kErrorActiveConnections, "Active connections" },
        { kErrorDeviceInUse, "Device in use" },
        { kErrorUnknownPrintMonitor, "Unknown print monitor" },
        { kErrorPrinterDriverInUse, "Printer driver in use" },
        { kErrorSpoolFileNotFound, "Spool file not found" },
        { kErrorSplNoStartdoc, "SPL no startdoc" },
        { kErrorSplNoAddjob, "SPL no addjob" },
        { kErrorPrintProcessorAlreadyInstalled, "Print processor already installed" },
        { kErrorPrintMonitorAlreadyInstalled, "Print monitor already installed" },
        { kErrorInvalidPrintMonitor, "Invalid print monitor" },
        { kErrorPrintMonitorInUse, "Print monitor in use" },
        { kErrorPrinterHasJobsQueued, "Printer has jobs queued" },
        { kErrorSuccessRebootRequired, "Success reboot required" },
        { kErrorSuccessRestartRequired, "Success restart required" },
        { kErrorPrinterNotFound, "Printer not found" },
        { kErrorPrinterDriverWarned, "Printer driver warned" },
        { kErrorPrinterDriverBlocked, "Printer driver blocked" },
        { kErrorWinsInternal, "Wins internal" },
        { kErrorCanNotDelLocalWins, "Can not del local wins" },
        { kErrorStaticInit, "Static init" },
        { kErrorIncBackup, "Inc backup" },
        { kErrorFullBackup, "Full backup" },
        { kErrorRecNonExistent, "Rec not existent" },
        { kErrorRplNotAllowed, "RPL not allowed" },
        { kErrorDhcpAddressConflict, "DHCP address conflict" },
        { kErrorWmiGuidNotFound, "WMU GUID not found" },
        { kErrorWmiInstanceNotFound, "WMI instance not found" },
        { kErrorWmiItemidNotFound, "WMI ItemID not found" },
        { kErrorWmiTryAgain, "WMI try again" },
        { kErrorWmiDpNotFound, "WMI DP not found" },
        { kErrorWmiUnresolvedInstanceRef, "WMI unresolved instance ref" },
        { kErrorWmiAlreadyEnabled, "WMU already enabled" },
        { kErrorWmiGuidDisconnected, "WMU GUID disconnected" },
        { kErrorWmiServerUnavailable, "WMI server unavailable" },
        { kErrorWmiDpFailed, "WMI DP failed" },
        { kErrorWmiInvalidMof, "WMI invalid MOF" },
        { kErrorWmiInvalidReginfo, "WMI invalid reginfo" },
        { kErrorWmiAlreadyDisabled, "WMI already disabled" },
        { kErrorWmiReadOnly, "WMI read only" },
        { kErrorWmiSetFailure, "WMI set failure" },
        { kErrorInvalidMedia, "Invalid media" },
        { kErrorInvalidLibrary, "Invalid library" },
        { kErrorInvalidMediaPool, "Invalid media pool" },
        { kErrorDriveMediaMismatch, "Drive media mismatch" },
        { kErrorMediaOffline, "Media offline" },
        { kErrorLibraryOffline, "Library offline" },
        { kErrorEmpty, "Empty" },
        { kErrorNotEmpty, "Not empty" },
        { kErrorMediaUnavailable, "Media unavailable" },
        { kErrorResourceDisabled, "Resource disabled" },
        { kErrorInvalidCleaner, "Invalid cleaner" },
        { kErrorUnableToClean, "Unable to clean" },
        { kErrorObjectNotFound, "Object not found" },
        { kErrorDatabaseFailure, "Database failure" },
        { kErrorDatabaseFull, "Database full" },
        { kErrorMediaIncompatible, "Media incompatible" },
        { kErrorResourceNotPresent, "Resource not present" },
        { kErrorInvalidOperation, "Invalid operation" },
        { kErrorMediaNotAvailable, "Media not available" },
        { kErrorDeviceNotAvailable, "Device not available" },
        { kErrorRequestRefused, "Request refused" },
        { kErrorInvalidDriveObject, "Invalid drive object" },
        { kErrorLibraryFull, "Library full" },
        { kErrorMediumNotAccessible, "Medium not accessible" },
        { kErrorUnableToLoadMedium, "Unable to load medium" },
        { kErrorUnableToInventoryDrive, "Unable to inventory drive" },
        { kErrorUnableToInventorySlot, "Unable to inventory slot" },
        { kErrorUnableToInventoryTransport, "Unable to inventory transport" },
        { kErrorTransportFull, "Transport full" },
        { kErrorControllingIeport, "Controlling ieport" },
        { kErrorUnableToEjectMountedMedia, "Unable to eject mounted media" },
        { kErrorCleanerSlotSet, "Cleaner slot set" },
        { kErrorCleanerSlotNotSet, "Cleaner slot not set" },
        { kErrorCleanerCartridgeSpent, "Cleaner cartridge spent" },
        { kErrorUnexpectedOmid, "Unexpected omid" },
        { kErrorCantDeleteLastItem, "Can't delete last item" },
        { kErrorMessageExceedsMaxSize, "Message exceeds max size" },
        { kErrorVolumeContainsSysFiles, "Volume contains sys files" },
        { kErrorIndigenousType, "Indigenous type" },
        { kErrorNoSupportingDrives, "No supporting drives" },
        { kErrorCleanerCartridgeInstalled, "Cleaner cartridge installed" },
        { kErrorFileOffline, "Fill offline" },
        { kErrorRemoteStorageNotActive, "Remote storage not active" },
        { kErrorRemoteStorageMediaError, "Remote storage media error" },
        { kErrorNotAReparsePoint, "Not a reparse point" },
        { kErrorReparseAttributeConflict, "Reparse attribute conflict" },
        { kErrorInvalidReparseData, "Invalid reparse data" },
        { kErrorReparseTagInvalid, "Reparse tag invalid" },
        { kErrorReparseTagMismatch, "Reparse tag mismatch" },
        { kErrorVolumeNotSisEnabled, "Volume not sis enabled" },
        { kErrorDependentResourceExists, "Dependent resource exists" },
        { kErrorDependencyNotFound, "Dependency not found" },
        { kErrorDependencyAlreadyExists, "Dependency already exists" },
        { kErrorResourceNotOnline, "Resource not online" },
        { kErrorHostNodeNotAvailable, "Host node not available" },
        { kErrorResourceNotAvailable, "Resource not available" },
        { kErrorResourceNotFound, "Resource not found" },
        { kErrorShutdownCluster, "Shutdown cluster" },
        { kErrorCantEvictActiveNode, "Can't evict active node" },
        { kErrorObjectAlreadyExists, "Object already exists" },
        { kErrorObjectInList, "Object in list" },
        { kErrorGroupNotAvailable, "Group not available" },
        { kErrorGroupNotFound, "Group not found" },
        { kErrorGroupNotOnline, "Group not online" },
        { kErrorHostNodeNotResourceOwner, "Host node not resource owner" },
        { kErrorHostNodeNotGroupOwner, "Host node not group owner" },
        { kErrorResmonCreateFailed, "Resmon create failed" },
        { kErrorResmonOnlineFailed, "Resmon online failed" },
        { kErrorResourceOnline, "Resource online" },
        { kErrorQuorumResource, "Quorum resource" },
        { kErrorNotQuorumCapable, "Not quorum capable" },
        { kErrorClusterShuttingDown, "Cluster shutting down" },
        { kErrorInvalidState, "Invalid state" },
        { kErrorResourcePropertiesStored, "Resource properties stored" },
        { kErrorNotQuorumClass, "Not quorum class" },
        { kErrorCoreResource, "Core resource" },
        { kErrorQuorumResourceOnlineFailed, "Quorum resource online failed" },
        { kErrorQuorumlogOpenFailed, "Quorumlog open failed" },
        { kErrorClusterlogCorrupt, "Clusterlog corrupt" },
        { kErrorClusterlogRecordExceedsMaxsize, "Clusterlog record exceeds maxsize" },
        { kErrorClusterlogExceedsMaxsize, "Clusterlog exceeds maxsize" },
        { kErrorClusterlogChkpointNotFound, "Clusterlog chkpoint not found" },
        { kErrorClusterlogNotEnoughSpace, "Clusterlog not enough space" },
        { kErrorQuorumOwnerAlive, "Quorum owner alive" },
        { kErrorNetworkNotAvailable, "Network not available" },
        { kErrorNodeNotAvailable, "Node not available" },
        { kErrorAllNodesNotAvailable, "All nodes not available" },
        { kErrorResourceFailed, "Resource failed" },
        { kErrorClusterInvalidNode, "Cluster invalid node" },
        { kErrorClusterNodeExists, "Cluster node exists" },
        { kErrorClusterJoinInProgress, "Cluster join in progress" },
        { kErrorClusterNodeNotFound, "Cluster node not found" },
        { kErrorClusterLocalNodeNotFound, "Cluster local node not found" },
        { kErrorClusterNetworkExists, "Cluster network exists" },
        { kErrorClusterNetworkNotFound, "Cluster network not found" },
        { kErrorClusterNetinterfaceExists, "Cluster netinterface exists" },
        { kErrorClusterNetinterfaceNotFound, "Cluster netinterface not found" },
        { kErrorClusterInvalidRequest, "Cluster invalid request" },
        { kErrorClusterInvalidNetworkProvider, "Cluster invalid network provider" },
        { kErrorClusterNodeDown, "Cluster node down" },
        { kErrorClusterNodeUnreachable, "Cluster node unreachable" },
        { kErrorClusterNodeNotMember, "Cluster node not member" },
        { kErrorClusterJoinNotInProgress, "Cluster join not in progress" },
        { kErrorClusterInvalidNetwork, "Cluster invalid network" },
        { kErrorClusterNodeUp, "Cluster node up" },
        { kErrorClusterIpaddrInUse, "Cluster ipaddr in use" },
        { kErrorClusterNodeNotPaused, "Cluster node not paused" },
        { kErrorClusterNoSecurityContext, "Cluster no security context" },
        { kErrorClusterNetworkNotInternal, "Cluster network not internal" },
        { kErrorClusterNodeAlreadyUp, "Cluster node already up" },
        { kErrorClusterNodeAlreadyDown, "Cluster node already down" },
        { kErrorClusterNetworkAlreadyOnline, "Cluster network already online" },
        { kErrorClusterNetworkAlreadyOffline, "Cluster network already offline" },
        { kErrorClusterNodeAlreadyMember, "Cluster node already member" },
        { kErrorClusterLastInternalNetwork, "Cluster last internal network" },
        { kErrorClusterNetworkHasDependents, "Cluster network has dependents" },
        { kErrorInvalidOperationOnQuorum, "Invalid operation on quorum" },
        { kErrorDependencyNotAllowed, "Dependency not allowed" },
        { kErrorClusterNodePaused, "Cluster node paused" },
        { kErrorNodeCantHostResource, "Node can't host resource" },
        { kErrorClusterNodeNotReady, "Cluster node not ready" },
        { kErrorClusterNodeShuttingDown, "Cluster node shutting down" },
        { kErrorClusterJoinAborted, "Cluster join aborted" },
        { kErrorClusterIncompatibleVersions, "Cluster incompatible versions" },
        { kErrorClusterMaxnumOfResourcesExceeded, "Cluster maxnum of resources exceeded" },
        { kErrorClusterSystemConfigChanged, "Cluster system config changed" },
        { kErrorClusterResourceTypeNotFound, "Cluster resource type not found" },
        { kErrorClusterRestypeNotSupported, "Cluster restype not supported" },
        { kErrorClusterResnameNotFound, "Cluster resname not found" },
        { kErrorClusterNoRpcPackagesRegistered, "Cluster no RPC packages registered" },
        { kErrorClusterOwnerNotInPreflist, "Cluster owner not in preflist" },
        { kErrorClusterDatabaseSeqmismatch, "Cluster database seqmismatch" },
        { kErrorResmonInvalidState, "Resmon invalid state" },
        { kErrorClusterGumNotLocker, "Cluster gum not locker" },
        { kErrorQuorumDiskNotFound, "Quorum disk not found" },
        { kErrorDatabaseBackupCorrupt, "Database backup corrupt" },
        { kErrorClusterNodeAlreadyHasDfsRoot, "Cluster node already has DFS root" },
        { kErrorResourcePropertyUnchangeable, "Resource property unchangeable" },
        { kErrorClusterMembershipInvalidState, "Cluster membership invalid state" },
        { kErrorClusterQuorumlogNotFound, "Cluster quorumlog not found" },
        { kErrorClusterMembershipHalt, "Cluster membership halt" },
        { kErrorClusterInstanceIdMismatch, "Cluster instance ID mismatch" },
        { kErrorClusterNetworkNotFoundForIp, "Cluster network not found for IP" },
        { kErrorClusterPropertyDataTypeMismatch, "Cluster property data type mismatch" },
        { kErrorClusterEvictWithoutCleanup, "Cluster evict without cleanup" },
        { kErrorClusterParameterMismatch, "Cluster parameter mismatch" },
        { kErrorNodeCannotBeClustered, "Node cannot be clustered" },
        { kErrorClusterWrongOsVersion, "Cluster wrong OS version" },
        { kErrorClusterCantCreateDupClusterName, "Cluster can't create dup cluster name" },
        { kErrorDecryptionFailed, "Decryption failed" },
        { kErrorFileEncrypted, "File encrypted" },
        { kErrorNoRecoveryPolicy, "No recovery policy" },
        { kErrorNoEfs, "No EFS" },
        { kErrorWrongEfs, "Wrong EFS" },
        { kErrorNoUserKeys, "No user keys" },
        { kErrorFileNotEncrypted, "File not encryped" },
        { kErrorNotExportFormat, "Not export format" },
        { kErrorFileReadOnly, "File read only" },
        { kErrorDirEfsDisallowed, "Dir EFS disallowed" },
        { kErrorEfsServerNotTrusted, "EFS server not trusted" },
        { kErrorBadRecoveryPolicy, "Bad recovery policy" },
        { kErrorEfsAlgBlobTooBig, "ETS alg blob too big" },
        { kErrorVolumeNotSupportEfs, "Volume not support EFS" },
        { kErrorEfsDisabled, "EFS disabled" },
        { kErrorEfsVersionNotSupport, "EFS version not support" },
        { kErrorNoBrowserServersFound, "No browser servers found" },
        { kSchedEServiceNotLocalsystem, "Sched E service not localsystem" },
        { kErrorCtxWinstationNameInvalid, "Ctx winstation name invalid" },
        { kErrorCtxInvalidPd, "Ctx invalid PD" },
        { kErrorCtxPdNotFound, "Ctx PD not found" },
        { kErrorCtxWdNotFound, "Ctx WD not found" },
        { kErrorCtxCannotMakeEventlogEntry, "Ctx cannot make eventlog entry" },
        { kErrorCtxServiceNameCollision, "Ctx service name collision" },
        { kErrorCtxClosePending, "Ctx close pending" },
        { kErrorCtxNoOutbuf, "Ctx no outbuf" },
        { kErrorCtxModemInfNotFound, "Ctx modem inf not found" },
        { kErrorCtxInvalidModemname, "Ctx invalid modemname" },
        { kErrorCtxModemResponseError, "Ctx modem response error" },
        { kErrorCtxModemResponseTimeout, "Ctx modem response timeout" },
        { kErrorCtxModemResponseNoCarrier, "Ctx modem response no carrier" },
        { kErrorCtxModemResponseNoDialtone, "Ctx modem response no dial tone" },
        { kErrorCtxModemResponseBusy, "Ctx modem response busy" },
        { kErrorCtxModemResponseVoice, "Ctx modem response voice" },
        { kErrorCtxTdError, "Ctx TD error" },
        { kErrorCtxWinstationNotFound, "Ctx winstation not found" },
        { kErrorCtxWinstationAlreadyExists, "Ctx winstation already exists" },
        { kErrorCtxWinstationBusy, "Ctx winstation busy" },
        { kErrorCtxBadVideoMode, "Ctx bad video mode" },
        { kErrorCtxGraphicsInvalid, "Ctx graphics invalid" },
        { kErrorCtxLogonDisabled, "Ctx logon disabled" },
        { kErrorCtxNotConsole, "Ctx not console" },
        { kErrorCtxClientQueryTimeout, "Ctx client query timeout" },
        { kErrorCtxConsoleDisconnect, "Ctx console disconnect" },
        { kErrorCtxConsoleConnect, "Ctx console connect" },
        { kErrorCtxShadowDenied, "Ctx shadow denied" },
        { kErrorCtxWinstationAccessDenied, "Ctx winstation access denied" },
        { kErrorCtxInvalidWd, "Ctx invalid WD" },
        { kErrorCtxShadowInvalid, "Ctx shadow invalid" },
        { kErrorCtxShadowDisabled, "Ctx shadow disabled" },
        { kErrorCtxClientLicenseInUse, "Ctx client licence in use" },
        { kErrorCtxClientLicenseNotSet, "Ctx client licence not set" },
        { kErrorCtxLicenseNotAvailable, "Ctx licence not available" },
        { kErrorCtxLicenseClientInvalid, "Ctx licence client invalid" },
        { kErrorCtxLicenseExpired, "Ctx licence expired" },
        { kErrorCtxShadowNotRunning, "Ctx shadow not running" },
        { kErrorCtxShadowEndedByModeChange, "Ctx shadow ended by mode change" },
        { kFrsErrInvalidApiSequence, "FRS err invalid API sequence" },
        { kFrsErrStartingService, "FRS err starting service" },
        { kFrsErrStoppingService, "FRS err stopping service" },
        { kFrsErrInternalApi, "FRS err internal API" },
        { kFrsErrInternal, "FRS err internal" },
        { kFrsErrServiceComm, "FRS err service comm" },
        { kFrsErrInsufficientPriv, "FRS err insufficient priv" },
        { kFrsErrAuthentication, "FRS err authentication" },
        { kFrsErrParentInsufficientPriv, "FRS err parent insufficient priv" },
        { kFrsErrParentAuthentication, "FRS err parent authentication" },
        { kFrsErrChildToParentComm, "FRS err child to parent comm" },
        { kFrsErrParentToChildComm, "FRS err parent to child comm" },
        { kFrsErrSysvolPopulate, "FRS err sysvol populate" },
        { kFrsErrSysvolPopulateTimeout, "FRS err sysvol populate timeout" },
        { kFrsErrSysvolIsBusy, "FRS err sysvol is busy" },
        { kFrsErrSysvolDemote, "FRS err sysvol demote" },
        { kFrsErrInvalidServiceParameter, "FRS err invalid service parameter" },
        { kErrorDsNotInstalled, "DS not installed" },
        { kErrorDsMembershipEvaluatedLocally, "DS membership evaluated locally" },
        { kErrorDsNoAttributeOrValue, "DS no attribute or value" },
        { kErrorDsInvalidAttributeSyntax, "DS invalid attribute syntax" },
        { kErrorDsAttributeTypeUndefined, "DS attribute type undefined" },
        { kErrorDsAttributeOrValueExists, "DS attribute or value exists" },
        { kErrorDsBusy, "DS busy" },
        { kErrorDsUnavailable, "DS unavailable" },
        { kErrorDsNoRidsAllocated, "DS no rids allocated" },
        { kErrorDsNoMoreRids, "DS no more rids" },
        { kErrorDsIncorrectRoleOwner, "DS incorrect role owner" },
        { kErrorDsRidmgrInitError, "DS ridmgr init error" },
        { kErrorDsObjClassViolation, "DS obj class violation" },
        { kErrorDsCantOnNonLeaf, "DS can't on non leaf" },
        { kErrorDsCantOnRdn, "DS can't on rnd" },
        { kErrorDsCantModObjClass, "DS can't mod obj class" },
        { kErrorDsCrossDomMoveError, "DS cross dom move error" },
        { kErrorDsGcNotAvailable, "DS GC not available" },
        { kErrorSharedPolicy, "Shared policy" },
        { kErrorPolicyObjectNotFound, "Policy object not found" },
        { kErrorPolicyOnlyInDs, "Policy only in DS" },
        { kErrorPromotionActive, "Promotion active" },
        { kErrorNoPromotionActive, "No promotion active" },
        { kErrorDsOperationsError, "DS operations error" },
        { kErrorDsProtocolError, "DS protocol error" },
        { kErrorDsTimelimitExceeded, "DS timelimit exceeded" },
        { kErrorDsSizelimitExceeded, "DS sizelimit exceeded" },
        { kErrorDsAdminLimitExceeded, "DS admin limit exceeded" },
        { kErrorDsCompareFalse, "DS compare false" },
        { kErrorDsCompareTrue, "DS compare true" },
        { kErrorDsAuthMethodNotSupported, "DS auth method not supported" },
        { kErrorDsStrongAuthRequired, "DS strong auth required" },
        { kErrorDsInappropriateAuth, "DS inappropriate auth" },
        { kErrorDsAuthUnknown, "DS auth unknown" },
        { kErrorDsReferral, "DS referral" },
        { kErrorDsUnavailableCritExtension, "DS unavailable crit extension" },
        { kErrorDsConfidentialityRequired, "DS confidentiality required" },
        { kErrorDsInappropriateMatching, "DS inappropriate matching" },
        { kErrorDsConstraintViolation, "DS constraint violation" },
        { kErrorDsNoSuchObject, "DS no such object" },
        { kErrorDsAliasProblem, "DS alias problem" },
        { kErrorDsInvalidDnSyntax, "DS invalid dn syntax" },
        { kErrorDsIsLeaf, "DS is leaf" },
        { kErrorDsAliasDerefProblem, "DS alias deref problem" },
        { kErrorDsUnwillingToPerform, "DS unwilling to perform" },
        { kErrorDsLoopDetect, "DS loop detect" },
        { kErrorDsNamingViolation, "DS naming violation" },
        { kErrorDsObjectResultsTooLarge, "DS object results too large" },
        { kErrorDsAffectsMultipleDsas, "DS affects multiple dsas" },
        { kErrorDsServerDown, "DS server down" },
        { kErrorDsLocalError, "DS local error" },
        { kErrorDsEncodingError, "DS encoding error" },
        { kErrorDsDecodingError, "DS decoding error" },
        { kErrorDsFilterUnknown, "DS filter unknown" },
        { kErrorDsParamError, "DS param error" },
        { kErrorDsNotSupported, "DS not supported" },
        { kErrorDsNoResultsReturned, "DS no results returned" },
        { kErrorDsControlNotFound, "DS control not found" },
        { kErrorDsClientLoop, "DS client loop" },
        { kErrorDsReferralLimitExceeded, "DS referral limit exceeded" },
        { kErrorDsSortControlMissing, "DS sort control missing" },
        { kErrorDsOffsetRangeError, "DS offset range error" },
        { kErrorDsRootMustBeNc, "DS root must be nc" },
        { kErrorDsAddReplicaInhibited, "DS and replica inhibited" },
        { kErrorDsAttNotDefInSchema, "DS att not def in schema" },
        { kErrorDsMaxObjSizeExceeded, "DS max obj size exceeded" },
        { kErrorDsObjStringNameExists, "DS obj string name exists" },
        { kErrorDsNoRdnDefinedInSchema, "DS no rdn defined in schema" },
        { kErrorDsRdnDoesntMatchSchema, "DS rdn doesn't match schema" },
        { kErrorDsNoRequestedAttsFound, "DS no requested atts found" },
        { kErrorDsUserBufferToSmall, "DS user buffer too small" },
        { kErrorDsAttIsNotOnObj, "DS att is not on obj" },
        { kErrorDsIllegalModOperation, "DS illegal mod operation" },
        { kErrorDsObjTooLarge, "DS obj too large" },
        { kErrorDsBadInstanceType, "DS bad instance type" },
        { kErrorDsMasterdsaRequired, "DS masterdsa required" },
        { kErrorDsObjectClassRequired, "DS object class required" },
        { kErrorDsMissingRequiredAtt, "DS missing required att" },
        { kErrorDsAttNotDefForClass, "DS att not def for class" },
        { kErrorDsAttAlreadyExists, "DS att already exists" },
        { kErrorDsCantAddAttValues, "DS can't add att values" },
        { kErrorDsSingleValueConstraint, "DS single value constraint" },
        { kErrorDsRangeConstraint, "DS range constraint" },
        { kErrorDsAttValAlreadyExists, "DS att val already exists" },
        { kErrorDsCantRemMissingAtt, "DS can't rem missing att" },
        { kErrorDsCantRemMissingAttVal, "DS can't rem missing att val" },
        { kErrorDsRootCantBeSubref, "DS root can't be subref" },
        { kErrorDsNoChaining, "DS no chaining" },
        { kErrorDsNoChainedEval, "DS no chained eval" },
        { kErrorDsNoParentObject, "DS no parent object" },
        { kErrorDsParentIsAnAlias, "DS parent is an alias" },
        { kErrorDsCantMixMasterAndReps, "DS can't mix master and reps" },
        { kErrorDsChildrenExist, "DS children exist" },
        { kErrorDsObjNotFound, "DS obj not found" },
        { kErrorDsAliasedObjMissing, "DS aliased obj missing" },
        { kErrorDsBadNameSyntax, "DS bad name syntax" },
        { kErrorDsAliasPointsToAlias, "DS alias points to alias" },
        { kErrorDsCantDerefAlias, "DS can't redef alias" },
        { kErrorDsOutOfScope, "DS out of scope" },
        { kErrorDsObjectBeingRemoved, "DS object being removed" },
        { kErrorDsCantDeleteDsaObj, "DS can't delete dsa obj" },
        { kErrorDsGenericError, "DS generic error" },
        { kErrorDsDsaMustBeIntMaster, "DS dsa must be int master" },
        { kErrorDsClassNotDsa, "DS class not dsa" },
        { kErrorDsInsuffAccessRights, "DS insuff access rights" },
        { kErrorDsIllegalSuperior, "DS illegal superior" },
        { kErrorDsAttributeOwnedBySam, "DS attribute owned by sam" },
        { kErrorDsNameTooManyParts, "DS name too many parts" },
        { kErrorDsNameTooLong, "DS name too long" },
        { kErrorDsNameValueTooLong, "DS name value too long" },
        { kErrorDsNameUnparseable, "DS name unparseable" },
        { kErrorDsNameTypeUnknown, "DS name type unknown" },
        { kErrorDsNotAnObject, "DS not an object" },
        { kErrorDsSecDescTooShort, "DS sec desc too short" },
        { kErrorDsSecDescInvalid, "DS sec desc invalid" },
        { kErrorDsNoDeletedName, "DS no deleted name" },
        { kErrorDsSubrefMustHaveParent, "DS subref must have parent" },
        { kErrorDsNcnameMustBeNc, "DS ncname must be nc" },
        { kErrorDsCantAddSystemOnly, "DS can't add system only" },
        { kErrorDsClassMustBeConcrete, "DS class must be concrete" },
        { kErrorDsInvalidDmd, "DS invalid dmd" },
        { kErrorDsObjGuidExists, "DS obj GUID exists" },
        { kErrorDsNotOnBacklink, "DS not on backlink" },
        { kErrorDsNoCrossrefForNc, "DS no crossref for nc" },
        { kErrorDsShuttingDown, "DS shutting down" },
        { kErrorDsUnknownOperation, "DS unknown operation" },
        { kErrorDsInvalidRoleOwner, "DS invalid role owner" },
        { kErrorDsCouldntContactFsmo, "DS couldn't contact fsmo" },
        { kErrorDsCrossNcDnRename, "DS cross nc dn rename" },
        { kErrorDsCantModSystemOnly, "DS can't mod system only" },
        { kErrorDsReplicatorOnly, "DS replicator only" },
        { kErrorDsObjClassNotDefined, "DS obj class not defined" },
        { kErrorDsObjClassNotSubclass, "DS obj class not subclass" },
        { kErrorDsNameReferenceInvalid, "DS name reference invalid" },
        { kErrorDsCrossRefExists, "DS cross ref exists" },
        { kErrorDsCantDelMasterCrossref, "DS can't del master crossref" },
        { kErrorDsSubtreeNotifyNotNcHead, "DS subtree notify not nc head" },
        { kErrorDsNotifyFilterTooComplex, "DS notify filter too complex" },
        { kErrorDsDupRdn, "DS dup rdn" },
        { kErrorDsDupOid, "DS dup oid" },
        { kErrorDsDupMapiId, "DS dup mapi ID" },
        { kErrorDsDupSchemaIdGuid, "DS dup schema ID GUID" },
        { kErrorDsDupLdapDisplayName, "DS dup LDAP display name" },
        { kErrorDsSemanticAttTest, "DS semantic att test" },
        { kErrorDsSyntaxMismatch, "DS syntax mismatch" },
        { kErrorDsExistsInMustHave, "DS exists in must have" },
        { kErrorDsExistsInMayHave, "DS exists in may have" },
        { kErrorDsNonexistentMayHave, "DS nonexistent may have" },
        { kErrorDsNonexistentMustHave, "DS nonexistent must have" },
        { kErrorDsAuxClsTestFail, "DS aux cls test fail" },
        { kErrorDsNonexistentPossSup, "DS nonexistent poss sup" },
        { kErrorDsSubClsTestFail, "DS sub cls test fail" },
        { kErrorDsBadRdnAttIdSyntax, "DS bad rdn att ID syntax" },
        { kErrorDsExistsInAuxCls, "DS exists in aux cls" },
        { kErrorDsExistsInSubCls, "DS exists in sub cls" },
        { kErrorDsExistsInPossSup, "DS exists in poss sup" },
        { kErrorDsRecalcschemaFailed, "DS recalcschema failed" },
        { kErrorDsTreeDeleteNotFinished, "DS tree delete not finished" },
        { kErrorDsCantDelete, "DS can't delete" },
        { kErrorDsAttSchemaReqId, "DS att schema req ID" },
        { kErrorDsBadAttSchemaSyntax, "DS bad att schema syntax" },
        { kErrorDsCantCacheAtt, "DS can't cache att" },
        { kErrorDsCantCacheClass, "DS can't cache class" },
        { kErrorDsCantRemoveAttCache, "DS can't remove att cache" },
        { kErrorDsCantRemoveClassCache, "DS can't remove class cache" },
        { kErrorDsCantRetrieveDn, "DS can't retrieve DN" },
        { kErrorDsMissingSupref, "DS missing supref" },
        { kErrorDsCantRetrieveInstance, "DS can't retrieve instance" },
        { kErrorDsCodeInconsistency, "DS code inconsistency" },
        { kErrorDsDatabaseError, "DS database error" },
        { kErrorDsGovernsidMissing, "DS governsid missing" },
        { kErrorDsMissingExpectedAtt, "DS missing expected att" },
        { kErrorDsNcnameMissingCrRef, "DS ncname missing cr ref" },
        { kErrorDsSecurityCheckingError, "DS security checking error" },
        { kErrorDsSchemaNotLoaded, "DS schema not loaded" },
        { kErrorDsSchemaAllocFailed, "DS schema alloc failed" },
        { kErrorDsAttSchemaReqSyntax, "DS att schema req syntax" },
        { kErrorDsGcverifyError, "DS gcverify error" },
        { kErrorDsDraSchemaMismatch, "DS dra schema mismatch" },
        { kErrorDsCantFindDsaObj, "DS can't find dsa obj" },
        { kErrorDsCantFindExpectedNc, "DS can't find expected nc" },
        { kErrorDsCantFindNcInCache, "DS can't find nc in cache" },
        { kErrorDsCantRetrieveChild, "DS can't retrieve child" },
        { kErrorDsSecurityIllegalModify, "DS security illegal modify" },
        { kErrorDsCantReplaceHiddenRec, "DS can't replace hidden rec" },
        { kErrorDsBadHierarchyFile, "DS bad hierarchy file" },
        { kErrorDsBuildHierarchyTableFailed, "DS build hierarchy table failed" },
        { kErrorDsConfigParamMissing, "DS config param missing" },
        { kErrorDsCountingAbIndicesFailed, "DS counting ab indices failed" },
        { kErrorDsHierarchyTableMallocFailed, "DS hierarchy table malloc failed" },
        { kErrorDsInternalFailure, "DS internal failure" },
        { kErrorDsUnknownError, "DS unknown error" },
        { kErrorDsRootRequiresClassTop, "DS root requires class top" },
        { kErrorDsRefusingFsmoRoles, "DS refusing fmso roles" },
        { kErrorDsMissingFsmoSettings, "DS missing fmso settings" },
        { kErrorDsUnableToSurrenderRoles, "DS unable to surrender roles" },
        { kErrorDsDraGeneric, "DS dra generic" },
        { kErrorDsDraInvalidParameter, "DS dra invalid parameter" },
        { kErrorDsDraBusy, "DS dra busy" },
        { kErrorDsDraBadDn, "DS dra bad dn" },
        { kErrorDsDraBadNc, "DS dra bad nc" },
        { kErrorDsDraDnExists, "DS dra dn exists" },
        { kErrorDsDraInternalError, "DS dra internal error" },
        { kErrorDsDraInconsistentDit, "DS dra inconsistent dit" },
        { kErrorDsDraConnectionFailed, "DS dra connection failed" },
        { kErrorDsDraBadInstanceType, "DS dra bad instance type" },
        { kErrorDsDraOutOfMem, "DS dra out of mem" },
        { kErrorDsDraMailProblem, "DS dra mail problem" },
        { kErrorDsDraRefAlreadyExists, "DS dra ref already exists" },
        { kErrorDsDraRefNotFound, "DS dra ref not found" },
        { kErrorDsDraObjIsRepSource, "DS dra obj is rep source" },
        { kErrorDsDraDbError, "DS dra db error" },
        { kErrorDsDraNoReplica, "DS dra no replica" },
        { kErrorDsDraAccessDenied, "DS dra access denied" },
        { kErrorDsDraNotSupported, "DS dra not supported" },
        { kErrorDsDraRpcCancelled, "DS dra RPC cancelled" },
        { kErrorDsDraSourceDisabled, "DS dra source disabled" },
        { kErrorDsDraSinkDisabled, "DS dra sink disabled" },
        { kErrorDsDraNameCollision, "DS dra name collision" },
        { kErrorDsDraSourceReinstalled, "DS dra source reinstalled" },
        { kErrorDsDraMissingParent, "DS dra missing parent" },
        { kErrorDsDraPreempted, "DS dra preempted" },
        { kErrorDsDraAbandonSync, "DS dra abandon sync" },
        { kErrorDsDraShutdown, "DS dra shutdown" },
        { kErrorDsDraIncompatiblePartialSet, "DS dra incompatible partial set" },
        { kErrorDsDraSourceIsPartialReplica, "DS dra source is partial replica" },
        { kErrorDsDraExtnConnectionFailed, "DS dra extn connection failed" },
        { kErrorDsInstallSchemaMismatch, "DS install schema mismatch" },
        { kErrorDsDupLinkId, "DS dup link ID" },
        { kErrorDsNameErrorResolving, "DS name error resolving" },
        { kErrorDsNameErrorNotFound, "DS name error not found" },
        { kErrorDsNameErrorNotUnique, "DS name error not unique" },
        { kErrorDsNameErrorNoMapping, "DS name error no mapping" },
        { kErrorDsNameErrorDomainOnly, "DS name error domain only" },
        { kErrorDsNameErrorNoSyntacticalMapping, "DS name error no syntactical mapping" },
        { kErrorDsConstructedAttMod, "DS constructed att mod" },
        { kErrorDsWrongOmObjClass, "DS wrong om obj class" },
        { kErrorDsDraReplPending, "DS dra repl pending" },
        { kErrorDsDsRequired, "DS ds required" },
        { kErrorDsInvalidLdapDisplayName, "DS invalid LDAP display name" },
        { kErrorDsNonBaseSearch, "DS non base search" },
        { kErrorDsCantRetrieveAtts, "DS can't retrieve atts" },
        { kErrorDsBacklinkWithoutLink, "DS backlink without link" },
        { kErrorDsEpochMismatch, "DS epoch mismatch" },
        { kErrorDsSrcNameMismatch, "DS src name mismatch" },
        { kErrorDsSrcAndDstNcIdentical, "DS src and dst nc identical" },
        { kErrorDsDstNcMismatch, "DS dst nc mismatch" },
        { kErrorDsNotAuthoritiveForDstNc, "DS not authoritive for dst nc" },
        { kErrorDsSrcGuidMismatch, "DS src GUID mismatch" },
        { kErrorDsCantMoveDeletedObject, "DS can't move deleted object" },
        { kErrorDsPdcOperationInProgress, "DS pdc operation in progress" },
        { kErrorDsCrossDomainCleanupReqd, "DS cross domain cleanup reqd" },
        { kErrorDsIllegalXdomMoveOperation, "DS illegal xdom move operation" },
        { kErrorDsCantWithAcctGroupMembershps, "DS can't with acct group membershps" },
        { kErrorDsNcMustHaveNcParent, "DS nc must have nc parent" },
        { kErrorDsDstDomainNotNative, "DS dst domain not native" },
        { kErrorDsMissingInfrastructureContainer, "DS missing infrastructure container" },
        { kErrorDsCantMoveAccountGroup, "DS can't move account group" },
        { kErrorDsCantMoveResourceGroup, "DS can't move resource group" },
        { kErrorDsInvalidSearchFlag, "DS invalid search flag" },
        { kErrorDsNoTreeDeleteAboveNc, "DS no tree delete above nc" },
        { kErrorDsCouldntLockTreeForDelete, "DS couldn't lock tree for delete" },
        { kErrorDsCouldntIdentifyObjectsForTreeDelete, "DS couldn't identify objects for tree delete" },
        { kErrorDsSamInitFailure, "DS sam init failure" },
        { kErrorDsSensitiveGroupViolation, "DS sensitive group violation" },
        { kErrorDsCantModPrimarygroupid, "DS can't mod primarygroupid" },
        { kErrorDsIllegalBaseSchemaMod, "DS illegal base schema mod" },
        { kErrorDsNonsafeSchemaChange, "DS nonsafe schema change" },
        { kErrorDsSchemaUpdateDisallowed, "DS schema update disallowed" },
        { kErrorDsCantCreateUnderSchema, "DS can't create under schema" },
        { kErrorDsInstallNoSrcSchVersion, "DS install no src sch version" },
        { kErrorDsInstallNoSchVersionInInifile, "DS install no sch version in inifile" },
        { kErrorDsInvalidGroupType, "DS invalid group type" },
        { kErrorDsNoNestGlobalgroupInMixeddomain, "DS no nest globalgroup in mixeddomain" },
        { kErrorDsNoNestLocalgroupInMixeddomain, "DS no nest localgroup in mixeddomain" },
        { kErrorDsGlobalCantHaveLocalMember, "DS global can't have local member" },
        { kErrorDsGlobalCantHaveUniversalMember, "DS global can't have universal member" },
        { kErrorDsUniversalCantHaveLocalMember, "DS universal can't have local member" },
        { kErrorDsGlobalCantHaveCrossdomainMember, "DS global can't have crossdomain member" },
        { kErrorDsLocalCantHaveCrossdomainLocalMember, "DS local can't have crossdomain local member" },
        { kErrorDsHavePrimaryMembers, "DS have primary members" },
        { kErrorDsStringSdConversionFailed, "DS string sd conversion failed" },
        { kErrorDsNamingMasterGc, "DS naming master gc" },
        { kErrorDsLookupFailure, "DS lookup failure" },
        { kErrorDsCouldntUpdateSpns, "DS couldn't update spns" },
        { kErrorDsCantRetrieveSd, "DS can't retrieve sd" },
        { kErrorDsKeyNotUnique, "DS key not unique" },
        { kErrorDsWrongLinkedAttSyntax, "DS wrong linked att syntax" },
        { kErrorDsSamNeedBootkeyPassword, "DS sam need bootkey password" },
        { kErrorDsSamNeedBootkeyFloppy, "DS sam need bootkey floppy" },
        { kErrorDsCantStart, "DS can't start" },
        { kErrorDsInitFailure, "DS init failure" },
        { kErrorDsNoPktPrivacyOnConnection, "DS no pkt privacy on connection" },
        { kErrorDsSourceDomainInForest, "DS source domain in forest" },
        { kErrorDsDestinationDomainNotInForest, "DS destination domain not in forest" },
        { kErrorDsDestinationAuditingNotEnabled, "DS destination auditing not enabled" },
        { kErrorDsCantFindDcForSrcDomain, "DS can't find dc for src domain" },
        { kErrorDsSrcObjNotGroupOrUser, "DS src obj not group or user" },
        { kErrorDsSrcSidExistsInForest, "DS src sid exists in forest" },
        { kErrorDsSrcAndDstObjectClassMismatch, "DS src and dst object class mismatch" },
        { kErrorSamInitFailure, "Sam init failure" },
        { kErrorDsDraSchemaInfoShip, "DS dra schema info ship" },
        { kErrorDsDraSchemaConflict, "DS dra schema conflict" },
        { kErrorDsDraEarlierSchemaConlict, "DS dra earlier schema conflict" },
        { kErrorDsDraObjNcMismatch, "DS dra obj nc mismatch" },
        { kErrorDsNcStillHasDsas, "DS nc still has dsas" },
        { kErrorDsGcRequired, "DS gc required" },
        { kErrorDsLocalMemberOfLocalOnly, "DS local member of local only" },
        { kErrorDsNoFpoInUniversalGroups, "DS no fpo in universal groups" },
        { kErrorDsCantAddToGc, "DS can't add to gc" },
        { kErrorDsNoCheckpointWithPdc, "DS no checkpoint with pdc" },
        { kErrorDsSourceAuditingNotEnabled, "DS source auditing not enabled" },
        { kErrorDsCantCreateInNondomainNc, "DS can't create in nondomain nc" },
        { kErrorDsInvalidNameForSpn, "DS invalid name for spn" },
        { kErrorDsFilterUsesContructedAttrs, "DS filter uses constructed attrs" },
        { kErrorDsUnicodepwdNotInQuotes, "DS unicodepwd not in quotes" },
        { kErrorDsMachineAccountQuotaExceeded, "DS machine account quota exceeded" },
        { kErrorDsMustBeRunOnDstDc, "DS must be run on dst dc" },
        { kErrorDsSrcDcMustBeSp4OrGreater, "DS src dc must be sp4 or greater" },
        { kErrorDsCantTreeDeleteCriticalObj, "DS can't tree delete critical obj" },
        { kErrorDsInitFailureConsole, "DS init failure console" },
        { kErrorDsSamInitFailureConsole, "DS sam init failure console" },
        { kErrorDsForestVersionTooHigh, "DS forest version too high" },
        { kErrorDsDomainVersionTooHigh, "DS domain version too high" },
        { kErrorDsForestVersionTooLow, "DS forest version too low" },
        { kErrorDsDomainVersionTooLow, "DS domain version too low" },
        { kErrorDsIncompatibleVersion, "DS incompatible version" },
        { kErrorDsLowDsaVersion, "DS low dsa version" },
        { kErrorDsNoBehaviorVersionInMixeddomain, "DS no behaviour version in mixeddomain" },
        { kErrorDsNotSupportedSortOrder, "DS not supported sort order" },
        { kErrorDsNameNotUnique, "DS name not unique" },
        { kErrorDsMachineAccountCreatedPrent4, "DS machine account created prent4" },
        { kErrorDsOutOfVersionStore, "DS out of version store" },
        { kErrorDsIncompatibleControlsUsed, "DS incompatible controls used" },
        { kErrorDsNoRefDomain, "DS no ref domain" },
        { kErrorDsReservedLinkId, "DS reserved link ID" },
        { kErrorDsLinkIdNotAvailable, "DS link ID not available" },
        { kErrorDsAgCantHaveUniversalMember, "DS ag can't have universal member" },
        { kErrorDsModifydnDisallowedByInstanceType, "DS modifydn disallowed by instance type" },
        { kErrorDsNoObjectMoveInSchemaNc, "DS no object move in schema nc" },
        { kErrorDsModifydnDisallowedByFlag, "DS modifydn disallowed by flag" },
        { kErrorDsModifydnWrongGrandparent, "DS modifydn wrong grandparent" },
        { kErrorDsNameErrorTrustReferral, "DS name error trust referral" },
        { kErrorNotSupportedOnStandardServer, "Not supported on standard server" },
        { kErrorDsCantAccessRemotePartOfAd, "DS can't access remote part of ad" },
        { kErrorDsCrImpossibleToValidate, "DS cr impossible to validate" },
        { kErrorDsThreadLimitExceeded, "DS thread limit exceeded" },
        { kErrorDsNotClosest, "DS not closest" },
        { kErrorDsCantDeriveSpnWithoutServerRef, "DS can't derive spn without server ref" },
        { kErrorDsSingleUserModeFailed, "DS single user mode failed" },
        { kErrorDsNtdscriptSyntaxError, "DS ntdscript syntax error" },
        { kErrorDsNtdscriptProcessError, "DS ntdscript process error" },
        { kErrorDsDifferentReplEpochs, "DS different repl epochs" },
        { kErrorDsDrsExtensionsChanged, "DS drs extensions changed" },
        { kErrorDsReplicaSetChangeNotAllowedOnDisabledCr, "DS replica set change not allowed on disabled cr" },
        { kErrorDsNoMsdsIntid, "DS no msds intid" },
        { kErrorDsDupMsdsIntid, "DS dup msds intid" },
        { kErrorDsExistsInRdnattid, "DS exists in rdnattid" },
        { kErrorDsAuthorizationFailed, "DS authorisation failed" },
        { kErrorDsInvalidScript, "DS invalid script" },
        { kErrorDsRemoteCrossrefOpFailed, "DS remote crossref op failed" },
        { kDnsErrorRcodeFormatError, "DNS error rcode format error" },
        { kDnsErrorRcodeServerFailure, "DNS error rcode server failure" },
        { kDnsErrorRcodeNameError, "DNS error rcode name error" },
        { kDnsErrorRcodeNotImplemented, "DNS error rcode not implemented" },
        { kDnsErrorRcodeRefused, "DNS error rcode refused" },
        { kDnsErrorRcodeYxdomain, "DNS error rcode yxdomain" },
        { kDnsErrorRcodeYxrrset, "DNS error rcode yxrrset" },
        { kDnsErrorRcodeNxrrset, "DNS error rcode nxrrset" },
        { kDnsErrorRcodeNotauth, "DNS error rcode notauth" },
        { kDnsErrorRcodeNotzone, "DNS error rcode notzone" },
        { kDnsErrorRcodeBadsig, "DNS error rcode badsig" },
        { kDnsErrorRcodeBadkey, "DNS error rcode badkey" },
        { kDnsErrorRcodeBadtime, "DNS error rcode badtime" },
        { kDnsInfoNoRecords, "DNS info no records" },
        { kDnsErrorBadPacket, "DNS error bad packet" },
        { kDnsErrorNoPacket, "DNS error no packet" },
        { kDnsErrorRcode, "DNS error rcode" },
        { kDnsErrorUnsecurePacket, "DNS error unsecure packet" },
        { kDnsErrorInvalidType, "DNS error invalid type" },
        { kDnsErrorInvalidIpAddress, "DNS error invalid IP address" },
        { kDnsErrorInvalidProperty, "DNS error invalid property" },
        { kDnsErrorTryAgainLater, "DNS error try again later" },
        { kDnsErrorNotUnique, "DNS error not unique" },
        { kDnsErrorNonRfcName, "DNS error non RFC name" },
        { kDnsStatusFqdn, "DNS status FQDN" },
        { kDnsStatusDottedName, "DNS status dotted name" },
        { kDnsStatusSinglePartName, "DNS status single part name" },
        { kDnsErrorInvalidNameChar, "DNS error invalid name char" },
        { kDnsErrorNumericName, "DNS error numeric name" },
        { kDnsErrorNotAllowedOnRootServer, "DNS error not allowed on root server" },
        { kDnsErrorZoneDoesNotExist, "DNS error zone does not exist" },
        { kDnsErrorNoZoneInfo, "DNS error not zone info" },
        { kDnsErrorInvalidZoneOperation, "DNS error invalid zone operation" },
        { kDnsErrorZoneConfigurationError, "DNS error zone configuration error" },
        { kDnsErrorZoneHasNoSoaRecord, "DNS error zone has not SOA record" },
        { kDnsErrorZoneHasNoNsRecords, "DNS error zone has no NS records" },
        { kDnsErrorZoneLocked, "DNS error zone locked" },
        { kDnsErrorZoneCreationFailed, "DNS error zone creation failed" },
        { kDnsErrorZoneAlreadyExists, "DNS error zone already exists" },
        { kDnsErrorAutozoneAlreadyExists, "DNS error autozone already exists" },
        { kDnsErrorInvalidZoneType, "DNS error invalid zone type" },
        { kDnsErrorSecondaryRequiresMasterIp, "DNS error secondary requires master IP" },
        { kDnsErrorZoneNotSecondary, "DNS error zone not secondary" },
        { kDnsErrorNeedSecondaryAddresses, "DNS error need secondary addresses" },
        { kDnsErrorWinsInitFailed, "DNS error wins init failed" },
        { kDnsErrorNeedWinsServers, "DNS error need wins servers" },
        { kDnsErrorNbstatInitFailed, "DNS error nbstat init failed" },
        { kDnsErrorSoaDeleteInvalid, "DNS error SOA delete invalid" },
        { kDnsErrorForwarderAlreadyExists, "DNS error forwarder already exists" },
        { kDnsErrorZoneRequiresMasterIp, "DNS error zone requires master IP" },
        { kDnsErrorZoneIsShutdown, "DNS error zone is shutdown" },
        { kDnsErrorPrimaryRequiresDatafile, "DNS error primary requires datafile" },
        { kDnsErrorInvalidDatafileName, "DNS error invalid datafile name" },
        { kDnsErrorDatafileOpenFailure, "DNS error datafile open failure" },
        { kDnsErrorFileWritebackFailed, "DNS error file writeback failed" },
        { kDnsErrorDatafileParsing, "DNS error datafile parsing" },
        { kDnsErrorRecordDoesNotExist, "DNS error record does not exist" },
        { kDnsErrorRecordFormat, "DNS error record format" },
        { kDnsErrorNodeCreationFailed, "DNS error node creation failed" },
        { kDnsErrorUnknownRecordType, "DNS error unknown record type" },
        { kDnsErrorRecordTimedOut, "DNS error record timed out" },
        { kDnsErrorNameNotInZone, "DNS error name not in zone" },
        { kDnsErrorCnameLoop, "DNS error CNAME loop" },
        { kDnsErrorNodeIsCname, "DNS error node is CNAME" },
        { kDnsErrorCnameCollision, "DNS error CNAME collision" },
        { kDnsErrorRecordOnlyAtZoneRoot, "DNS error record only at zone root" },
        { kDnsErrorRecordAlreadyExists, "DNS error record already exists" },
        { kDnsErrorSecondaryData, "DNS error secondary data" },
        { kDnsErrorNoCreateCacheData, "DNS error no create cache data" },
        { kDnsErrorNameDoesNotExist, "DNS error name does not exist" },
        { kDnsWarningPtrCreateFailed, "DNS warning PTR create failed" },
        { kDnsWarningDomainUndeleted, "DNS warning domain undeleted" },
        { kDnsErrorDsUnavailable, "DNS error ds unavailable" },
        { kDnsErrorDsZoneAlreadyExists, "DNS error ds zone already exists" },
        { kDnsErrorNoBootfileIfDsZone, "DNS error no bootfile if ds zone" },
        { kDnsInfoAxfrComplete, "DNS info AXFR complete" },
        { kDnsErrorAxfr, "DNS error AXFR" },
        { kDnsInfoAddedLocalWins, "DNS info added local wins" },
        { kDnsStatusContinueNeeded, "DNS status continue needed" },
        { kDnsErrorNoTcpip, "DNS error no TCPIP" },
        { kDnsErrorNoDnsServers, "DNS error no DNS servers" },
        { kDnsErrorDpDoesNotExist, "DNS error dp does not exist" },
        { kDnsErrorDpAlreadyExists, "DNS error dp already exists" },
        { kDnsErrorDpNotEnlisted, "DNS error dp not enlisted" },
        { kDnsErrorDpAlreadyEnlisted, "DNS error dp already enlisted" },
        { kWSAQosReceivers, "QOS receivers" },
        { kWSAQosSenders, "QOS senders" },
        { kWSAQosNoSenders, "QOS no senders" },
        { kWSAQosNoReceivers, "QOS no receivers" },
        { kWSAQosRequestConfirmed, "QOS request confirmed" },
        { kWSAQosAdmissionFailure, "QOS admission failure" },
        { kWSAQosPolicyFailure, "QOS policy failure" },
        { kWSAQosBadStyle, "QOS bad style" },
        { kWSAQosBadObject, "QOS bad object" },
        { kWSAQosTrafficCtrlError, "QOS traffic ctrl error" },
        { kWSAQosGenericError, "QOS generic error" },
        { kWSAQosEservicetype, "QOS eservicetype" },
        { kWSAQosEflowspec, "QOS eflowspec" },
        { kWSAQosEprovspecbuf, "QOS eprovspecbuf" },
        { kWSAQosEfilterstyle, "QOS efilterstyle" },
        { kWSAQosEfiltertype, "QOS efiltertype" },
        { kWSAQosEfiltercount, "QOS efiltercount" },
        { kWSAQosEobjlength, "QOS eobjlength" },
        { kWSAQosEflowcount, "QOS eflowcount" },
        { kWSAQosEunknownpsobj, "QOS eunknownpsobj" },
        { kWSAQosEpolicyobj, "QOS epolicyobj" },
        { kWSAQosEflowdesc, "QOS eflowdesc" },
        { kWSAQosEpsflowspec, "QOS epsflowspec" },
        { kWSAQosEpsfilterspec, "QOS epsfilterspec" },
        { kWSAQosEsdmodeobj, "QOS esdmodeobj" },
        { kWSAQosEshaperateobj, "QOS eshaperateobj" },
        { kWSAQosReservedPetype, "QOS reserved petype" },
        { kErrorIpsecQmPolicyExists, "IPSEC qm policy exists" },
        { kErrorIpsecQmPolicyNotFound, "IPSEC qm policy not found" },
        { kErrorIpsecQmPolicyInUse, "IPSEC qm policy in use" },
        { kErrorIpsecMmPolicyExists, "IPSEC mm policy exists" },
        { kErrorIpsecMmPolicyNotFound, "IPSEC mm policy not found" },
        { kErrorIpsecMmPolicyInUse, "IPSEC mm policy in use" },
        { kErrorIpsecMmFilterExists, "IPSEC mm filter exists" },
        { kErrorIpsecMmFilterNotFound, "IPSEC mm filter not found" },
        { kErrorIpsecTransportFilterExists, "IPSEC transport filter exists" },
        { kErrorIpsecTransportFilterNotFound, "IPSEC transport filter not found" },
        { kErrorIpsecMmAuthExists, "IPSEC mm auth exists" },
        { kErrorIpsecMmAuthNotFound, "IPSEC mm auth not found" },
        { kErrorIpsecMmAuthInUse, "IPSEC mm auth in use" },
        { kErrorIpsecDefaultMmPolicyNotFound, "IPSEC default mm policy not found" },
        { kErrorIpsecDefaultMmAuthNotFound, "IPSEC default mm auth not found" },
        { kErrorIpsecDefaultQmPolicyNotFound, "IPSEC default qm policy not found" },
        { kErrorIpsecTunnelFilterExists, "IPSEC tunnel filter exists" },
        { kErrorIpsecTunnelFilterNotFound, "IPSEC tunnel filter not found" },
        { kErrorIpsecMmFilterPendingDeletion, "IPSEC mm filter pending deletion" },
        { kErrorIpsecTransportFilterPendingDeletion, "IPSEC transport filter pending deletion" },
        { kErrorIpsecTunnelFilterPendingDeletion, "IPSEC tunnel filter pending deletion" },
        { kErrorIpsecMmPolicyPendingDeletion, "IPSEC mm policy pending deletion" },
        { kErrorIpsecMmAuthPendingDeletion, "IPSEC mm auth pending deletion" },
        { kErrorIpsecQmPolicyPendingDeletion, "IPSEC qm policy pending deletion" },
        { kErrorIpsecIkeAuthFail, "IPSEC IKE auth fail" },
        { kErrorIpsecIkeAttribFail, "IPSEC IKE attrib fail" },
        { kErrorIpsecIkeNegotiationPending, "IPSEC IKE negotiation pending" },
        { kErrorIpsecIkeGeneralProcessingError, "IPSEC IKE general processing error" },
        { kErrorIpsecIkeTimedOut, "IPSEC IKE timed out" },
        { kErrorIpsecIkeNoCert, "IPSEC IKE no cert" },
        { kErrorIpsecIkeSaDeleted, "IPSEC IKE sa deleted" },
        { kErrorIpsecIkeSaReaped, "IPSEC IKE sa reaped" },
        { kErrorIpsecIkeMmAcquireDrop, "IPSEC IKE mm acquire drop" },
        { kErrorIpsecIkeQmAcquireDrop, "IPSEC IKE qm acquire drop" },
        { kErrorIpsecIkeQueueDropMm, "IPSEC IKE queue drop mm" },
        { kErrorIpsecIkeQueueDropNoMm, "IPSEC IKE queue drop no mm" },
        { kErrorIpsecIkeDropNoResponse, "IPSEC IKE drop no response" },
        { kErrorIpsecIkeMmDelayDrop, "IPSEC IKE mm delay drop" },
        { kErrorIpsecIkeQmDelayDrop, "IPSEC IKE qm delay drop" },
        { kErrorIpsecIkeError, "IPSEC IKE error" },
        { kErrorIpsecIkeCrlFailed, "IPSEC IKE crl failed" },
        { kErrorIpsecIkeInvalidKeyUsage, "IPSEC IKE invalid key usage" },
        { kErrorIpsecIkeInvalidCertType, "IPSEC IKE invalid cert type" },
        { kErrorIpsecIkeNoPrivateKey, "IPSEC IKE no private key" },
        { kErrorIpsecIkeDhFail, "IPSEC IKE dh fail" },
        { kErrorIpsecIkeInvalidHeader, "IPSEC IKE invalid header" },
        { kErrorIpsecIkeNoPolicy, "IPSEC IKE no policy" },
        { kErrorIpsecIkeInvalidSignature, "IPSEC IKE invalid signature" },
        { kErrorIpsecIkeKerberosError, "IPSEC IKE kerberos error" },
        { kErrorIpsecIkeNoPublicKey, "IPSEC IKE no public key" },
        { kErrorIpsecIkeProcessErr, "IPSEC IKE process err" },
        { kErrorIpsecIkeProcessErrSa, "IPSEC IKE process err sa" },
        { kErrorIpsecIkeProcessErrProp, "IPSEC IKE process err prop" },
        { kErrorIpsecIkeProcessErrTrans, "IPSEC IKE process err trans" },
        { kErrorIpsecIkeProcessErrKe, "IPSEC IKE process err ke" },
        { kErrorIpsecIkeProcessErrId, "IPSEC IKE process err ID" },
        { kErrorIpsecIkeProcessErrCert, "IPSEC IKE process err cert" },
        { kErrorIpsecIkeProcessErrCertReq, "IPSEC IKE process err cert req" },
        { kErrorIpsecIkeProcessErrHash, "IPSEC IKE process err hash" },
        { kErrorIpsecIkeProcessErrSig, "IPSEC IKE process err sig" },
        { kErrorIpsecIkeProcessErrNonce, "IPSEC IKE process err nonce" },
        { kErrorIpsecIkeProcessErrNotify, "IPSEC IKE process err notify" },
        { kErrorIpsecIkeProcessErrDelete, "IPSEC IKE process err delete" },
        { kErrorIpsecIkeProcessErrVendor, "IPSEC IKE process err vendor" },
        { kErrorIpsecIkeInvalidPayload, "IPSEC IKE invalid payload" },
        { kErrorIpsecIkeLoadSoftSa, "IPSEC IKE load soft sa" },
        { kErrorIpsecIkeSoftSaTornDown, "IPSEC IKE soft sa torn down" },
        { kErrorIpsecIkeInvalidCookie, "IPSEC IKE invalid cookie" },
        { kErrorIpsecIkeNoPeerCert, "IPSEC IKE no peer cert" },
        { kErrorIpsecIkePeerCrlFailed, "IPSEC IKE peer CRL failed" },
        { kErrorIpsecIkePolicyChange, "IPSEC IKE policy change" },
        { kErrorIpsecIkeNoMmPolicy, "IPSEC IKE no mm policy" },
        { kErrorIpsecIkeNotcbpriv, "IPSEC IKE notcbpriv" },
        { kErrorIpsecIkeSecloadfail, "IPSEC IKE secloadfail" },
        { kErrorIpsecIkeFailsspinit, "IPSEC IKE failsspinit" },
        { kErrorIpsecIkeFailqueryssp, "IPSEC IKE failqueryssp" },
        { kErrorIpsecIkeSrvacqfail, "IPSEC IKE srvacqfail" },
        { kErrorIpsecIkeSrvquerycred, "IPSEC IKE srvquerycred" },
        { kErrorIpsecIkeGetspifail, "IPSEC IKE getspifail" },
        { kErrorIpsecIkeInvalidFilter, "IPSEC IKE invalid filter" },
        { kErrorIpsecIkeOutOfMemory, "IPSEC IKE out of memory" },
        { kErrorIpsecIkeAddUpdateKeyFailed, "IPSEC IKE add update key failed" },
        { kErrorIpsecIkeInvalidPolicy, "IPSEC IKE invalid policy" },
        { kErrorIpsecIkeUnknownDoi, "IPSEC IKE unknown doi" },
        { kErrorIpsecIkeInvalidSituation, "IPSEC IKE invalid situation" },
        { kErrorIpsecIkeDhFailure, "IPSEC IKE dh failure" },
        { kErrorIpsecIkeInvalidGroup, "IPSEC IKE invalid group" },
        { kErrorIpsecIkeEncrypt, "IPSEC IKE encrypt" },
        { kErrorIpsecIkeDecrypt, "IPSEC IKE decrypt" },
        { kErrorIpsecIkePolicyMatch, "IPSEC IKE policy match" },
        { kErrorIpsecIkeUnsupportedId, "IPSEC IKE unsupported ID" },
        { kErrorIpsecIkeInvalidHash, "IPSEC IKE invalid hash" },
        { kErrorIpsecIkeInvalidHashAlg, "IPSEC IKE invalid hash alg" },
        { kErrorIpsecIkeInvalidHashSize, "IPSEC IKE invalid hash size" },
        { kErrorIpsecIkeInvalidEncryptAlg, "IPSEC IKE invalid encrypt alg" },
        { kErrorIpsecIkeInvalidAuthAlg, "IPSEC IKE invalid auth alg" },
        { kErrorIpsecIkeInvalidSig, "IPSEC IKE invalid sig" },
        { kErrorIpsecIkeLoadFailed, "IPSEC IKE load failed" },
        { kErrorIpsecIkeRpcDelete, "IPSEC IKE rpc delete" },
        { kErrorIpsecIkeBenignReinit, "IPSEC IKE benign reinit" },
        { kErrorIpsecIkeInvalidResponderLifetimeNotify, "IPSEC IKE invalid responder lifetime notify" },
        { kErrorIpsecIkeInvalidCertKeylen, "IPSEC IKE invalid cert keylen" },
        { kErrorIpsecIkeMmLimit, "IPSEC IKE mm limit" },
        { kErrorIpsecIkeNegotiationDisabled, "IPSEC IKE negotiation disabled" },
        { kErrorIpsecIkeNegStatusEnd, "IPSEC IKE neg status end" },
        { kErrorSxsSectionNotFound, "Sxs section not found" },
        { kErrorSxsCantGenActctx, "Sxs can't gen actctx" },
        { kErrorSxsInvalidActctxdataFormat, "Sxs invalid actctxdata format" },
        { kErrorSxsAssemblyNotFound, "Sxs assembly not found" },
        { kErrorSxsManifestFormatError, "Sxs manifest format error" },
        { kErrorSxsManifestParseError, "Sxs manifest parse error" },
        { kErrorSxsActivationContextDisabled, "Sxs activation context disabled" },
        { kErrorSxsKeyNotFound, "Sxs key not found" },
        { kErrorSxsVersionConflict, "Sxs version conflict" },
        { kErrorSxsWrongSectionType, "Sxs wrong section type" },
        { kErrorSxsThreadQueriesDisabled, "Sxs thread queries disabled" },
        { kErrorSxsProcessDefaultAlreadySet, "Sxs process default already set" },
        { kErrorSxsUnknownEncodingGroup, "Sxs unknown encoding group" },
        { kErrorSxsUnknownEncoding, "Sxs unknown encoding" },
        { kErrorSxsInvalidXmlNamespaceUri, "Sxs invalid XML namespace URI" },
        { kErrorSxsRootManifestDependencyNotInstalled, "Sxs root manifest dependency not installed" },
        { kErrorSxsLeafManifestDependencyNotInstalled, "Sxs leaf manifest dependency not installed" },
        { kErrorSxsInvalidAssemblyIdentityAttribute, "Sxs invalid assembly indentity attribute" },
        { kErrorSxsManifestMissingRequiredDefaultNamespace, "Sxs manifest missing required default namespace" },
        { kErrorSxsManifestInvalidRequiredDefaultNamespace, "Sxs manifest invalid required default namespace" },
        { kErrorSxsPrivateManifestCrossPathWithReparsePoint, "Sxs private manifest cross path with reparse point" },
        { kErrorSxsDuplicateDllName, "Sxs duplicate dll name" },
        { kErrorSxsDuplicateWindowclassName, "Sxs duplicate windowclass name" },
        { kErrorSxsDuplicateClsid, "Sxs duplicate clsid" },
        { kErrorSxsDuplicateIid, "Sxs duplicate iid" },
        { kErrorSxsDuplicateTlbid, "Sxs duplicate tlbid" },
        { kErrorSxsDuplicateProgid, "Sxs duplicate progid" },
        { kErrorSxsDuplicateAssemblyName, "Sxs duplicate assembly name" },
        { kErrorSxsFileHashMismatch, "Sxs file hash mismatch" },
        { kErrorSxsPolicyParseError, "Sxs policy parse error" },
        { kErrorSxsXmlEMissingquote, "Sxs XML e missingquote" },
        { kErrorSxsXmlECommentsyntax, "Sxs XML e commentsyntax" },
        { kErrorSxsXmlEBadstartnamechar, "Sxs XML e badstartnamechar" },
        { kErrorSxsXmlEBadnamechar, "Sxs XML e badnamechar" },
        { kErrorSxsXmlEBadcharinstring, "Sxs XML e badcharinstring" },
        { kErrorSxsXmlEXmldeclsyntax, "Sxs XML e xmldeclsyntax" },
        { kErrorSxsXmlEBadchardata, "Sxs XML e badchardata" },
        { kErrorSxsXmlEMissingwhitespace, "Sxs XML e missingwhitespace" },
        { kErrorSxsXmlEExpectingtagend, "Sxs XML e expectingtagend" },
        { kErrorSxsXmlEMissingsemicolon, "Sxs XML e missingsemicolon" },
        { kErrorSxsXmlEUnbalancedparen, "Sxs XML e unbalancedparen" },
        { kErrorSxsXmlEInternalerror, "Sxs XML e internalerror" },
        { kErrorSxsXmlEUnexpectedWhitespace, "Sxs XML e unexpected whitespace" },
        { kErrorSxsXmlEIncompleteEncoding, "Sxs XML e incomplete encoding" },
        { kErrorSxsXmlEMissingParen, "Sxs XML e missing paren" },
        { kErrorSxsXmlEExpectingclosequote, "Sxs XML e expectingclosequote" },
        { kErrorSxsXmlEMultipleColons, "Sxs XML e multiple colons" },
        { kErrorSxsXmlEInvalidDecimal, "Sxs XML e invalid decimal" },
        { kErrorSxsXmlEInvalidHexidecimal, "Sxs XML e invalid hexidecimal" },
        { kErrorSxsXmlEInvalidUnicode, "Sxs XML e invalid unicode" },
        { kErrorSxsXmlEWhitespaceorquestionmark, "Sxs XML e whitespaceorquestionmark" },
        { kErrorSxsXmlEUnexpectedendtag, "Sxs XML e unexpectedendtag" },
        { kErrorSxsXmlEUnclosedtag, "Sxs XML e unclosedtag" },
        { kErrorSxsXmlEDuplicateattribute, "Sxs XML e duplicateattribute" },
        { kErrorSxsXmlEMultipleroots, "Sxs XML e multipleroots" },
        { kErrorSxsXmlEInvalidatrootlevel, "Sxs XML e invalidatrootlevel" },
        { kErrorSxsXmlEBadxmldecl, "Sxs XML e badxmldecl" },
        { kErrorSxsXmlEMissingroot, "Sxs XML e missingroot" },
        { kErrorSxsXmlEUnexpectedeof, "Sxs XML e unexpectedeof" },
        { kErrorSxsXmlEBadperefinsubset, "Sxs XML e badperefinsubset" },
        { kErrorSxsXmlEUnclosedstarttag, "Sxs XML e unclosedstarttag" },
        { kErrorSxsXmlEUnclosedendtag, "Sxs XML e unclosedendtag" },
        { kErrorSxsXmlEUnclosedstring, "Sxs XML e unclosedstring" },
        { kErrorSxsXmlEUnclosedcomment, "Sxs XML e unclosedcomment" },
        { kErrorSxsXmlEUncloseddecl, "Sxs XML e uncloseddecl" },
        { kErrorSxsXmlEUnclosedcdata, "Sxs XML e unclosedcdata" },
        { kErrorSxsXmlEReservednamespace, "Sxs XML e reservednamespace" },
        { kErrorSxsXmlEInvalidencoding, "Sxs XML e invalidencoding" },
        { kErrorSxsXmlEInvalidswitch, "Sxs XML e invalidswitch" },
        { kErrorSxsXmlEBadxmlcase, "Sxs XML e badxmlcase" },
        { kErrorSxsXmlEInvalidStandalone, "Sxs XML e invalid standalone" },
        { kErrorSxsXmlEUnexpectedStandalone, "Sxs XML e unexpected standalone" },
        { kErrorSxsXmlEInvalidVersion, "Sxs XML e invalid version" },
        { kErrorSxsXmlEMissingequals, "Sxs XML e missingequals" },
        { kErrorSxsProtectionRecoveryFailed, "Sxs protection recovery failed" },
        { kErrorSxsProtectionPublicKeyTooShort, "Sxs protection public key too short" },
        { kErrorSxsProtectionCatalogNotValid, "Sxs protection catalog not valid" },
        { kErrorSxsUntranslatableHresult, "Sxs untranslatable hresult" },
        { kErrorSxsProtectionCatalogFileMissing, "Sxs protection catalog file missing" },
        { kErrorSxsMissingAssemblyIdentityAttribute, "Sxs missing assembly identity attribute" },
        { kErrorSxsInvalidAssemblyIdentityAttributeName, "Sxs invalid assembly identity attribute name" },
    };
#endif /* IL2CPP_DISABLE_FULL_MESSAGES */


    static int32_t compare_message(const void *first, const void *second)
    {
        ErrorDesc *efirst = (ErrorDesc*)first;
        ErrorDesc *esecond = (ErrorDesc*)second;

        return (int32_t)efirst->code - (int32_t)esecond->code;
    }

    static const char *find_message(ErrorCode code, ErrorDesc *list, int32_t count)
    {
        ErrorDesc key = { code, "" };
        ErrorDesc *result = (ErrorDesc*)bsearch(&key, list, count, sizeof(ErrorDesc), compare_message);
        return result ? result->message : NULL;
    }

    static const char *find_message_linear(ErrorCode code, ErrorDesc *list, int32_t count)
    {
        int32_t prev = -1;

        for (int32_t i = 0; i < count; ++i)
        {
            if (list[i].code > prev)
                prev = list[i].code;
            else
            {
                // static int error_shown;
                // if (!error_shown){
                //      error_shown = 1;
                //      fprintf (stderr, "Mono: Incorrect message sorted in io-layer/messages.c at index %d (msg=%s)\n", i, list [i].txt);
                // }
            }

            if (list[i].code == code)
            {
                // static int error_shown;
                // if (!error_shown){
                //      error_shown = 1;
                //      fprintf (stderr, "Mono: Error %d with text %s is improperly sorted in io-layer/messages.c\n", id, list [i].txt);
                // }
                return list[i].message;
            }
        }
        return NULL;
    }

    std::string Messages::FromCode(ErrorCode code)
    {
        const char *message = find_message(code, common_messages, N_ELEMENTS(common_messages));
        if (message != NULL)
            return message;

#ifndef IL2CPP_DISABLE_FULL_MESSAGES
        message = find_message(code, messages, N_ELEMENTS(messages));
        if (message != NULL)
            return message;
#endif

        // Linear search, in case someone adds an error message and does not add it
        // to the list in a sorted position, this will be catched during development.

        message = find_message_linear(code, common_messages, N_ELEMENTS(common_messages));
        if (message != NULL)
            return message;

#ifndef IL2CPP_DISABLE_FULL_MESSAGES
        message = find_message_linear(code, messages, N_ELEMENTS(messages));
        if (message != NULL)
            return message;
#endif

        return std::string();
    }
}
}
