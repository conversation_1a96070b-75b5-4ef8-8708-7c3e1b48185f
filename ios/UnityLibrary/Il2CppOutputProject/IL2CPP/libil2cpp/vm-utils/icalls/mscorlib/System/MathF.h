#pragma once

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
    class LIBIL2CPP_CODEGEN_API MathF
    {
    public:
        static float Acos(float x);
        static float Acosh(float x);
        static float Asin(float x);
        static float Asinh(float x);
        static float Atan(float x);
        static float Atan2(float y, float x);
        static float Atanh(float x);
        static float Cbrt(float x);
        static float Ceiling(float x);
        static float Cos(float x);
        static float Cosh(float x);
        static float Exp(float x);
        static float Floor(float x);
        static float FMod(float x, float y);
        static float Log(float x);
        static float Log10(float x);
        static float ModF(float x, float* intptr);
        static float Pow(float x, float y);
        static float Sin(float x);
        static float Sinh(float x);
        static float Sqrt(float x);
        static float Tan(float x);
        static float Tanh(float x);
    };
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
