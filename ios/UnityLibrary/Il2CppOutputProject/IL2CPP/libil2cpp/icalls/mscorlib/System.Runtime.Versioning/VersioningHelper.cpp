#include "il2cpp-config.h"
#include "VersioningHelper.h"

#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Runtime
{
namespace Versioning
{
    int32_t VersioningHelper::GetRuntimeId()
    {
        NOT_SUPPORTED_IL2CPP(VersioningHelper::GetRuntimeId, "This icall is not supported by il2cpp.");
        return 0;
    }
} // namespace Versioning
} // namespace Runtime
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
