#pragma once

struct Il2CppCalendarData;

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Globalization
{
    class LIBIL2CPP_CODEGEN_API CalendarData
    {
    public:
        static bool fill_calendar_data(Il2CppCalendarData* _this, Il2CppString* localeName, int32_t datetimeIndex);
    };
} // namespace Globalization
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
