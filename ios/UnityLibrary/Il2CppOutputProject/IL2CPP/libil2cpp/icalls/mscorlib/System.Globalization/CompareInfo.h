#pragma once

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Globalization
{
    class LIBIL2CPP_CODEGEN_API CompareInfo
    {
    public:
        static int32_t internal_compare_icall(Il2CppChar* str1, int32_t length1, Il2CppChar* str2, int32_t length2, int32_t options);
        static int32_t internal_index_icall(Il2CppChar* source, int32_t sindex, int32_t count, Il2CppChar* value, int32_t value_length, bool first);
    };
} /* namespace Globalization */
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
