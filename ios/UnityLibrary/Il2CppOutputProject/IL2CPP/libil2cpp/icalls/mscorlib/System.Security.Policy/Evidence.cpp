#include "il2cpp-config.h"

#include "icalls/mscorlib/System.Security.Policy/Evidence.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Security
{
namespace Policy
{
    bool Evidence::IsAuthenticodePresent(Il2CppAssembly* a)
    {
        NOT_SUPPORTED_IL2CPP(Evidence::IsAuthenticodePresent, "This icall is not supported by il2cpp.");

        return false;
    }
} /* namespace Policy */
} /* namespace Security */
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
