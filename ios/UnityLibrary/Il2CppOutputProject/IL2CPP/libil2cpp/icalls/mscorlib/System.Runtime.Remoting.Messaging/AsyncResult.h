#pragma once

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Runtime
{
namespace Remoting
{
namespace Messaging
{
    class LIBIL2CPP_CODEGEN_API AsyncResult
    {
    public:
        static Il2CppObject* Invoke(Il2CppObject* _this);
    };
} // namespace Messaging
} // namespace Remoting
} // namespace Runtime
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
