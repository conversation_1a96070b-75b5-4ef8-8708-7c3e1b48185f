#include "il2cpp-config.h"
#include "ArgIterator.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
    intptr_t ArgIterator::IntGetNextArgType(mscorlib_System_ArgIterator* thisPtr)
    {
        NOT_SUPPORTED_IL2CPP(ArgIterator::IntGetNextArgType, "ArgIterator is not supported. Do not use __argList, use params instead.");
        return intptr_t();
    }

    void ArgIterator::IntGetNextArg(mscorlib_System_ArgIterator* thisPtr, void* res)
    {
        NOT_SUPPORTED_IL2CPP(ArgIterator::IntGetNextArg, "ArgIterator is not supported. Do not use __argList, use params instead.");
    }

    void ArgIterator::IntGetNextArgWithType(mscorlib_System_ArgIterator* thisPtr, void* res, intptr_t rth)
    {
        NOT_SUPPORTED_IL2CPP(ArgIterator::IntGetNextArgWithType, "ArgIterator is not supported. Do not use __argList, use params instead.");
    }

    void ArgIterator::Setup(mscorlib_System_ArgIterator* thisPtr, intptr_t argsp, intptr_t start)
    {
        NOT_SUPPORTED_IL2CPP(ArgIterator::Setup, "ArgIterator is not supported. Do not use __argList, use params instead.");
    }
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
