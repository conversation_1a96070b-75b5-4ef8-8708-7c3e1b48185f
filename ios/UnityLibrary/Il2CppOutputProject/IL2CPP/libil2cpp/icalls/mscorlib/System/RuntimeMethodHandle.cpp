#include "il2cpp-config.h"

#include "icalls/mscorlib/System/RuntimeMethodHandle.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
    intptr_t RuntimeMethodHandle::GetFunctionPointer(intptr_t m)
    {
        NOT_SUPPORTED_IL2CPP(RuntimeMethodHandle::GetFunctionPointer, "This icall is not supported by il2cpp. Use Marshal.GetFunctionPointerForDelegate instead.");
        return 0;
    }
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
