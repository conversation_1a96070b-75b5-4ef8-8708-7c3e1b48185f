#pragma once
#include "il2cpp-object-internals.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Text
{
    class LIBIL2CPP_CODEGEN_API Normalization
    {
    public:
        static void load_normalization_resource(intptr_t* props, intptr_t* mappedChars, intptr_t* charMapIndex, intptr_t* helperIndex, intptr_t* mapIdxToComposite, intptr_t* combiningClass);
    };
} // namespace Text
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
