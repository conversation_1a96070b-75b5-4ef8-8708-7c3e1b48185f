#include "il2cpp-config.h"

#include "icalls/mscorlib/System.Security.Principal/WindowsImpersonationContext.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Security
{
namespace Principal
{
    bool WindowsImpersonationContext::CloseToken(intptr_t token)
    {
        NOT_SUPPORTED_IL2CPP(WindowsImpersonationContext::CloseToken, "This icall is not supported by il2cpp.");

        return false;
    }

    bool WindowsImpersonationContext::RevertToSelf()
    {
        NOT_SUPPORTED_IL2CPP(WindowsImpersonationContext::RevertToSelf, "This icall is not supported by il2cpp.");

        return false;
    }

    bool WindowsImpersonationContext::SetCurrentToken(intptr_t token)
    {
        NOT_SUPPORTED_IL2CPP(WindowsImpersonationContext::SetCurrentToken, "This icall is not supported by il2cpp.");

        return false;
    }

    intptr_t WindowsImpersonationContext::DuplicateToken(intptr_t token)
    {
        NOT_SUPPORTED_IL2CPP(WindowsImpersonationContext::DuplicateToken, "This icall is not supported by il2cpp.");

        return 0;
    }
} /* namespace Principal */
} /* namespace Security */
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
