#include "il2cpp-config.h"

#include "icalls/mscorlib/System.Security.Principal/WindowsPrincipal.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Security
{
namespace Principal
{
    bool WindowsPrincipal::IsMemberOfGroupId(intptr_t user, intptr_t group)
    {
        NOT_SUPPORTED_IL2CPP(WindowsPrincipal::IsMemberOfGroupId, "This icall is not supported by il2cpp.");

        return false;
    }

    bool WindowsPrincipal::IsMemberOfGroupName(intptr_t user, intptr_t group)
    {
        NOT_SUPPORTED_IL2CPP(WindowsPrincipal::IsMemberOfGroupName, "This icall is not supported by il2cpp.");

        return false;
    }
} /* namespace Principal */
} /* namespace Security */
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
