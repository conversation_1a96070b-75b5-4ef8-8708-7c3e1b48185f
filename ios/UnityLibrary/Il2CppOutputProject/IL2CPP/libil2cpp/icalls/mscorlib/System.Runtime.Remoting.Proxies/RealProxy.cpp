#include "il2cpp-config.h"
#include "icalls/mscorlib/System.Runtime.Remoting.Proxies/RealProxy.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Runtime
{
namespace Remoting
{
namespace Proxies
{
    Il2CppObject* RealProxy::InternalGetTransparentProxy(Il2CppObject*, Il2CppString*)
    {
        NOT_SUPPORTED_REMOTING(RealProxy::InternalGetTransparentProxy);
        return NULL;
    }

    Il2CppReflectionType* RealProxy::InternalGetProxyType(Il2CppObject *)
    {
        NOT_SUPPORTED_REMOTING(RealProxy::InternalGetProxyType);
        return NULL;
    }
} /* namespace Proxies */
} /* namespace Remoting */
} /* namespace Runtime */
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
