#pragma once

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Reflection
{
    class LIBIL2CPP_CODEGEN_API RuntimeConstructorInfo
    {
    public:
        static int32_t get_metadata_token(Il2CppObject* method);
        static Il2CppObject* InternalInvoke(Il2CppReflectionMethod* method, Il2CppObject* thisPtr, Il2CppArray* params, Il2CppException** exc);
    };
} // namespace Reflection
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
