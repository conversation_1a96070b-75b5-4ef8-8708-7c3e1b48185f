#pragma once

#include "il2cpp-object-internals.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Reflection
{
    class LIBIL2CPP_CODEGEN_API CustomAttributeData
    {
    public:
        static void ResolveArgumentsInternal(Il2CppObject* ctor, Il2CppObject* assembly, intptr_t data, uint32_t data_length, Il2CppArray** ctorArgs, Il2CppArray** namedArgs);
    };
} // namespace Reflection
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
