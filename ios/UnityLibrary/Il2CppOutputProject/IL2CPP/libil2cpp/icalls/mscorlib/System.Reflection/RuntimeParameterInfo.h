#pragma once

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Reflection
{
    class LIBIL2CPP_CODEGEN_API RuntimeParameterInfo
    {
    public:
        static int32_t GetMetadataToken(Il2CppObject* thisPtr);
        static Il2CppArray* GetTypeModifiers(Il2CppObject* type, Il2CppObject* member, int32_t position, bool optional);
    };
} // namespace Reflection
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
