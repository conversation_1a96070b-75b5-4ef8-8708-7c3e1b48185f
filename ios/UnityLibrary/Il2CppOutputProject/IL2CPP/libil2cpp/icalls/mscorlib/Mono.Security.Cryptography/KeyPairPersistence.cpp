#include "il2cpp-config.h"
#include "icalls/mscorlib/Mono.Security.Cryptography/KeyPairPersistence.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace Mono
{
namespace Security
{
namespace Cryptography
{
    bool KeyPairPersistence::_CanSecure(Il2CppChar* root)
    {
        IL2CPP_NOT_IMPLEMENTED_ICALL(KeyPairPersistence::_CanSecure);
        return false;
    }

    bool KeyPairPersistence::_IsMachineProtected(Il2CppChar* path)
    {
        IL2CPP_NOT_IMPLEMENTED_ICALL(KeyPairPersistence::_IsMachineProtected);
        return false;
    }

    bool KeyPairPersistence::_IsUserProtected(Il2CppChar* path)
    {
        IL2CPP_NOT_IMPLEMENTED_ICALL(KeyPairPersistence::_IsUserProtected);
        return false;
    }

    bool KeyPairPersistence::_ProtectMachine(Il2CppChar* path)
    {
        IL2CPP_NOT_IMPLEMENTED_ICALL(KeyPairPersistence::_ProtectMachine);
        return false;
    }

    bool KeyPairPersistence::_ProtectUser(Il2CppChar* path)
    {
        IL2CPP_NOT_IMPLEMENTED_ICALL(KeyPairPersistence::_ProtectUser);
        return false;
    }
} /* namespace Cryptography */
} /* namespace Security */
} /* namespace Mono */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
