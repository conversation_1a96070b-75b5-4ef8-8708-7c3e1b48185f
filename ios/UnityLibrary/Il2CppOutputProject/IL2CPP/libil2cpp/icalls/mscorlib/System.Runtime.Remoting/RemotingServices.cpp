#include "il2cpp-config.h"
#include "icalls/mscorlib/System.Runtime.Remoting/RemotingServices.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Runtime
{
namespace Remoting
{
    Il2CppReflectionMethod* RemotingServices::GetVirtualMethod(Il2CppReflectionType*, Il2CppReflectionMethod*)
    {
        NOT_SUPPORTED_REMOTING(RemotingServices::GetVirtualMethod);
        return NULL;
    }

    Il2CppObject* RemotingServices::InternalExecute(Il2CppReflectionMethod*, Il2CppObject*, Il2CppArray*, Il2CppArray**)
    {
        NOT_SUPPORTED_REMOTING(RemotingServices::InternalExecute);
        return NULL;
    }
} /* namespace Remoting */
} /* namespace Runtime */
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
