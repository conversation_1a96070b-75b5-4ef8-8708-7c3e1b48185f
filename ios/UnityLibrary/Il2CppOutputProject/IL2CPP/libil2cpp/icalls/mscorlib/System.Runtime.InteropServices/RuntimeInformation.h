#pragma once

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Runtime
{
namespace InteropServices
{
    class LIBIL2CPP_CODEGEN_API RuntimeInformation
    {
    public:
        static Il2CppString* GetOSName();
        static Il2CppString* GetRuntimeArchitecture();
    };
} // namespace InteropServices
} // namespace Runtime
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
