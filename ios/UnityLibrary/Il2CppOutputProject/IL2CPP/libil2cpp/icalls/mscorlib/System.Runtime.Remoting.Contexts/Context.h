#pragma once

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Runtime
{
namespace Remoting
{
namespace Contexts
{
    class LIBIL2CPP_CODEGEN_API Context
    {
    public:
        static void RegisterContext(Il2CppObject* ctx);
        static void ReleaseContext(Il2CppObject* ctx);
    };
} // namespace Contexts
} // namespace Remoting
} // namespace Runtime
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
