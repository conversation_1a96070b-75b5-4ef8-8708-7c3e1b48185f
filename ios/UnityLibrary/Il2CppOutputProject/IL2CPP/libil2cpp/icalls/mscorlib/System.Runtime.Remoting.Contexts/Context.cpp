#include "il2cpp-config.h"
#include "Context.h"

#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace Runtime
{
namespace Remoting
{
namespace Contexts
{
    void Context::RegisterContext(Il2CppObject* ctx)
    {
        NOT_SUPPORTED_IL2CPP(Context::RegisterContext, "This icall is not supported by il2cpp.");
    }

    void Context::ReleaseContext(Il2CppObject* ctx)
    {
        NOT_SUPPORTED_IL2CPP(Context::ReleaseContext, "This icall is not supported by il2cpp.");
    }
} // namespace Contexts
} // namespace Remoting
} // namespace Runtime
} // namespace System
} // namespace mscorlib
} // namespace icalls
} // namespace il2cpp
