#include "il2cpp-config.h"

#include "icalls/mscorlib/System.IO/DriveInfo.h"
#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace mscorlib
{
namespace System
{
namespace IO
{
    bool DriveInfo::GetDiskFreeSpaceInternal(Il2CppChar* pathName, int32_t pathName_length, uint64_t* freeBytesAvail, uint64_t* totalNumberOfBytes, uint64_t* totalNumberOfFreeBytes, int32_t* error)
    {
        NOT_SUPPORTED_IL2CPP(DriveInfo::GetDiskFreeSpaceInternal, "This icall is not supported by il2cpp.");

        return false;
    }

    Il2CppString* DriveInfo::GetDriveFormatInternal(Il2CppChar* rootPathName, int32_t rootPathName_length)
    {
        NOT_SUPPORTED_IL2CPP(DriveInfo::GetDriveFormat, "This icall is not supported by il2cpp.");
        return NULL;
    }

    uint32_t DriveInfo::GetDriveTypeInternal(Il2CppChar* rootPathName, int32_t rootPathName_length)
    {
        NOT_SUPPORTED_IL2CPP(DriveInfo::GetDriveTypeInternal, "This icall is not supported by il2cpp.");

        return 0;
    }
} /* namespace IO */
} /* namespace System */
} /* namespace mscorlib */
} /* namespace icalls */
} /* namespace il2cpp */
