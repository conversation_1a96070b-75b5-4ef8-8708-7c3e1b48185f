#pragma once

namespace il2cpp
{
namespace icalls
{
namespace System
{
namespace System
{
namespace Diagnostics
{
    class LIBIL2CPP_CODEGEN_API FileVersionInfo
    {
    public:
        static void GetVersionInfo_icall(Il2CppObject* thisPtr, Il2CppChar* fileName, int32_t fileName_length);
    };
} /* namespace Diagnostics */
} /* namespace System */
} /* namespace System */
} /* namespace icalls */
} /* namespace il2cpp */
