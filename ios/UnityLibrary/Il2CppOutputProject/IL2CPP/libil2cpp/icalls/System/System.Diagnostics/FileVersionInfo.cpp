#include "il2cpp-config.h"
#include "FileVersionInfo.h"

#include "vm/Exception.h"

namespace il2cpp
{
namespace icalls
{
namespace System
{
namespace System
{
namespace Diagnostics
{
    void FileVersionInfo::GetVersionInfo_icall(Il2CppObject* thisPtr, Il2CppChar* fileName, int32_t fileName_length)
    {
        NOT_SUPPORTED_IL2CPP(FileVersionInfo::GetVersionInfo_icall, "This icall is not supported by il2cpp.");
    }
} /* namespace Diagnostics */
} /* namespace System */
} /* namespace System */
} /* namespace icalls */
} /* namespace il2cpp */
