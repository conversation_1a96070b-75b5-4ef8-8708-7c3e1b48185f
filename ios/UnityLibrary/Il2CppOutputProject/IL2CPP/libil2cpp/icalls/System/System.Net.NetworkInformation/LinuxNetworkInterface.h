#pragma once

namespace il2cpp
{
namespace icalls
{
namespace System
{
namespace System
{
namespace Net
{
namespace NetworkInformation
{
    class LIBIL2CPP_CODEGEN_API LinuxNetworkInterface
    {
    public:
        static bool unitydroid_get_network_interface_up_state(Il2CppString* ifName, bool* isUp);
    };
} // namespace NetworkInformation
} // namespace Net
} // namespace System
} // namespace System
} // namespace icalls
} // namespace il2cpp
