#pragma once

namespace il2cpp
{
namespace icalls
{
namespace System
{
namespace System
{
namespace Net
{
namespace NetworkInformation
{
    class LIBIL2CPP_CODEGEN_API MacOsIPInterfaceProperties
    {
    public:
        static bool ParseRouteInfo_icall(Il2CppString* iface, Il2CppArray** gw_addr_list);
    };
} // namespace NetworkInformation
} // namespace Net
} // namespace System
} // namespace System
} // namespace icalls
} // namespace il2cpp
