{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/osx-arm64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/osx-arm64": {"il2cpp/1.0.0": {"dependencies": {"Unity.Api.Attributes": "1.0.0", "Unity.IL2CPP": "1.0.0", "Unity.IL2CPP.Api": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.Android": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.AppleTV": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.Linux": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.MacOSX": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.UniversalWindows": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.VisionOS": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.WebGL": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.iOS": "1.0.0", "Unity.IL2CPP.Building": "1.0.0", "Unity.IL2CPP.Common": "1.0.0", "Unity.IL2CPP.Common35": "1.0.0", "Unity.IL2CPP.Shell": "1.0.0", "Unity.Options": "1.0.0", "Unity.TinyProfiler": "1.0.0", "Bee.BeeDriver": "1.0.0.0", "Bee.TinyProfiler2": "0.0.0.0", "Mono.Cecil": "********", "Mono.Cecil.Pdb": "********", "runtimepack.Microsoft.NETCore.App.Runtime.osx-arm64": "6.0.18"}, "runtime": {"il2cpp.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.osx-arm64/6.0.18": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.100.1823.26907"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "6.0.1823.26907"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.1823.26907"}}, "native": {"createdump": {"fileVersion": "0.0.0.0"}, "libSystem.Globalization.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.IO.Compression.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Net.Security.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.Apple.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.OpenSsl.dylib": {"fileVersion": "0.0.0.0"}, "libclrjit.dylib": {"fileVersion": "0.0.0.0"}, "libcoreclr.dylib": {"fileVersion": "0.0.0.0"}, "libdbgshim.dylib": {"fileVersion": "0.0.0.0"}, "libhostfxr.dylib": {"fileVersion": "0.0.0.0"}, "libhostpolicy.dylib": {"fileVersion": "0.0.0.0"}, "libmscordaccore.dylib": {"fileVersion": "0.0.0.0"}, "libmscordbi.dylib": {"fileVersion": "0.0.0.0"}}}, "JetBrains.Profiler.Api/1.1.7": {"runtime": {"lib/netstandard2.0/JetBrains.Profiler.Api.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/6.0.0-preview.5.21301.5": {"dependencies": {"System.Security.AccessControl": "6.0.0-preview.5.21301.5", "System.Security.Principal.Windows": "6.0.0-preview.5.21301.5"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}}, "System.Security.AccessControl/6.0.0-preview.5.21301.5": {"dependencies": {"System.Security.Principal.Windows": "6.0.0-preview.5.21301.5"}}, "System.Security.Principal.Windows/6.0.0-preview.5.21301.5": {}, "Analytics.Api.Output/1.0.0": {"runtime": {"Analytics.Api.Output.dll": {}}}, "Unity.Api.Attributes/1.0.0": {"runtime": {"Unity.Api.Attributes.dll": {}}}, "Unity.Cecil.Awesome/1.0.0": {"runtime": {"Unity.Cecil.Awesome.dll": {}}}, "Unity.Cecil.Visitor/1.0.0": {"runtime": {"Unity.Cecil.Visitor.dll": {}}}, "Unity.IL2CPP/1.0.0": {"dependencies": {"JetBrains.Profiler.Api": "1.1.7", "Unity.Cecil.Awesome": "1.0.0", "Unity.Cecil.Visitor": "1.0.0", "Unity.IL2CPP.Api": "1.0.0", "Unity.IL2CPP.Api.Output": "1.0.0", "Unity.IL2CPP.Common": "1.0.0", "Unity.IL2CPP.Common35": "1.0.0", "Unity.IL2CPP.CompilerServices": "1.0.0", "Unity.IL2CPP.DataModel": "1.0.0", "Unity.IL2CPP.Shell": "1.0.0", "Unity.Options": "1.0.0", "Unity.TinyProfiler": "1.0.0"}, "runtime": {"Unity.IL2CPP.dll": {}}}, "Unity.IL2CPP.Api/1.0.0": {"dependencies": {"Unity.Api.Attributes": "1.0.0"}, "runtime": {"Unity.IL2CPP.Api.dll": {}}}, "Unity.IL2CPP.Api.Output/1.0.0": {"dependencies": {"Analytics.Api.Output": "1.0.0", "Unity.IL2CPP.Api": "1.0.0"}, "runtime": {"Unity.IL2CPP.Api.Output.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic/1.0.0": {"dependencies": {"Unity.Api.Attributes": "1.0.0", "Unity.IL2CPP.Api": "1.0.0", "Unity.Linker.Api": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.Android/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.Android.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.AppleTV/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.iOS": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.iOS/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.iOS.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.Linux/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.Linux.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.MacOSX/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.UniversalWindows/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.VisionOS/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.iOS": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.WebGL/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.WebGL.dll": {}}}, "Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop/1.0.0": {"dependencies": {"Unity.IL2CPP.Bee.BuildLogic": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll": {}}}, "Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram/1.0.0": {"dependencies": {"Unity.IL2CPP.Api": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic": "1.0.0", "Unity.IL2CPP.Bee.BuildLogic.UniversalWindows": "1.0.0", "Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll": {}}}, "Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data/1.0.0": {"dependencies": {"Unity.IL2CPP.Api": "1.0.0"}, "runtime": {"Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll": {}}}, "Unity.IL2CPP.Building/1.0.0": {"dependencies": {"Microsoft.Win32.Registry": "6.0.0-preview.5.21301.5", "Newtonsoft.Json": "13.0.1", "Unity.IL2CPP": "1.0.0", "Unity.IL2CPP.Api": "1.0.0", "Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram": "1.0.0", "Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data": "1.0.0", "Unity.IL2CPP.Common": "1.0.0", "Unity.IL2CPP.Common35": "1.0.0", "Unity.IL2CPP.Shell": "1.0.0", "Unity.Options": "1.0.0", "Unity.TinyProfiler": "1.0.0"}, "runtime": {"Unity.IL2CPP.Building.dll": {}}}, "Unity.IL2CPP.Common/1.0.0": {"dependencies": {"Unity.Cecil.Awesome": "1.0.0", "Unity.IL2CPP.Common35": "1.0.0"}, "runtime": {"Unity.IL2CPP.Common.dll": {}}}, "Unity.IL2CPP.Common35/1.0.0": {"runtime": {"Unity.IL2CPP.Common35.dll": {}}}, "Unity.IL2CPP.CompilerServices/1.0.0": {"runtime": {"Unity.IL2CPP.CompilerServices.dll": {}}}, "Unity.IL2CPP.DataModel/1.0.0": {"dependencies": {"Unity.Cecil.Awesome": "1.0.0", "Unity.IL2CPP.Common": "1.0.0", "Unity.IL2CPP.Common35": "1.0.0", "Unity.TinyProfiler": "1.0.0"}, "runtime": {"Unity.IL2CPP.DataModel.dll": {}}}, "Unity.IL2CPP.Shell/1.0.0": {"dependencies": {"Unity.IL2CPP.Common35": "1.0.0"}, "runtime": {"Unity.IL2CPP.Shell.dll": {}}}, "Unity.Linker.Api/1.0.0": {"dependencies": {"Unity.Api.Attributes": "1.0.0"}, "runtime": {"Unity.Linker.Api.dll": {}}}, "Unity.Options/1.0.0": {"runtime": {"Unity.Options.dll": {}}}, "Unity.TinyProfiler/1.0.0": {"dependencies": {"Unity.IL2CPP.Common35": "1.0.0"}, "runtime": {"Unity.TinyProfiler.dll": {}}}, "Bee.BeeDriver/1.0.0.0": {"runtime": {"Bee.BeeDriver.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Bee.TinyProfiler2/0.0.0.0": {"runtime": {"Bee.TinyProfiler2.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Mono.Cecil/********": {"runtime": {"Mono.Cecil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Pdb/********": {"runtime": {"Mono.Cecil.Pdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Bee.NativeProgramSupport/0.0.0.0": {"runtime": {"Bee.NativeProgramSupport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Core/0.0.0.0": {"runtime": {"Bee.Core.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "NiceIO/0.0.0.0": {"runtime": {"NiceIO.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Tools/0.0.0.0": {"runtime": {"Bee.Tools.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.Android/0.0.0.0": {"runtime": {"Bee.Toolchain.Android.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.LLVM/0.0.0.0": {"runtime": {"Bee.Toolchain.LLVM.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.GNU/0.0.0.0": {"runtime": {"Bee.Toolchain.GNU.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.TvOS/0.0.0.0": {"runtime": {"Bee.Toolchain.TvOS.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.Xcode/0.0.0.0": {"runtime": {"Bee.Toolchain.Xcode.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.EmbeddedLinux/0.0.0.0": {"runtime": {"Bee.Toolchain.EmbeddedLinux.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.IOS/0.0.0.0": {"runtime": {"Bee.Toolchain.IOS.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.VisionOS/0.0.0.0": {"runtime": {"Bee.Toolchain.VisionOS.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.Linux/0.0.0.0": {"runtime": {"Bee.Toolchain.Linux.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.MacOS/0.0.0.0": {"runtime": {"Bee.Toolchain.MacOS.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.VisualStudio/0.0.0.0": {"runtime": {"Bee.Toolchain.VisualStudio.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.UWP/0.0.0.0": {"runtime": {"Bee.Toolchain.UWP.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.Emscripten/0.0.0.0": {"runtime": {"Bee.Toolchain.Emscripten.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Toolchain.Windows/0.0.0.0": {"runtime": {"Bee.Toolchain.Windows.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.TundraBackend/0.0.0.0": {"runtime": {"Bee.TundraBackend.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.Stevedore.Program/0.0.0.0": {"runtime": {"Bee.Stevedore.Program.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Mono.Cecil.Rocks/********": {"runtime": {"Mono.Cecil.Rocks.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Mdb/********": {"runtime": {"Mono.Cecil.Mdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Bee.CSharpSupport/0.0.0.0": {"runtime": {"Bee.CSharpSupport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.DotNet/0.0.0.0": {"runtime": {"Bee.DotNet.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Bee.VisualStudioSolution/0.0.0.0": {"runtime": {"Bee.VisualStudioSolution.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"il2cpp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.osx-arm64/6.0.18": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "JetBrains.Profiler.Api/1.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-scN2nYNc7yGUNS3wF6fwuoAKZwWW2aMBHDqeVENrpJjRFKzEOWWrOGzNfr/WZft9+XzIz+ajshrx/dzz8Xp5Ig==", "path": "jetbrains.profiler.api/1.1.7", "hashPath": "jetbrains.profiler.api.1.1.7.nupkg.sha512"}, "Microsoft.Win32.Registry/6.0.0-preview.5.21301.5": {"type": "package", "serviceable": true, "sha512": "sha512-qYLtJIAEJJmY2vXxlVO8x4uXfgq7DFOHjpmnHlLm7kmAvyNFckYY/Dx5CZythBXvI2/7sratbIGKqSTysfgZ8A==", "path": "microsoft.win32.registry/6.0.0-preview.5.21301.5", "hashPath": "microsoft.win32.registry.6.0.0-preview.5.21301.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "System.Security.AccessControl/6.0.0-preview.5.21301.5": {"type": "package", "serviceable": true, "sha512": "sha512-EA9ul7nGN8oggMvloILnR+wnrbgLNZZQBYHq5nEq/ixwnKLV3M3Tbd1Jbj8oGck3XMj0owq81e4Jxp3s0IMICw==", "path": "system.security.accesscontrol/6.0.0-preview.5.21301.5", "hashPath": "system.security.accesscontrol.6.0.0-preview.5.21301.5.nupkg.sha512"}, "System.Security.Principal.Windows/6.0.0-preview.5.21301.5": {"type": "package", "serviceable": true, "sha512": "sha512-ywwCqFAaRVbgqqORqYg8jdaX6NUEpzbuhxyUhAs+7mZ8AFAO4PzFYrZ5JPkYejXwougDldtbi0zOkk1lLzugLw==", "path": "system.security.principal.windows/6.0.0-preview.5.21301.5", "hashPath": "system.security.principal.windows.6.0.0-preview.5.21301.5.nupkg.sha512"}, "Analytics.Api.Output/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Api.Attributes/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Cecil.Awesome/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Cecil.Visitor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Api.Output/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.Android/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.AppleTV/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.iOS/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.Linux/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.MacOSX/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.UniversalWindows/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.VisionOS/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.WebGL/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Building/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Common35/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.CompilerServices/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.DataModel/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Shell/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Linker.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Options/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.TinyProfiler/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bee.BeeDriver/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.TinyProfiler2/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Pdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.NativeProgramSupport/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Core/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "NiceIO/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Tools/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.Android/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.LLVM/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.GNU/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.TvOS/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.Xcode/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.EmbeddedLinux/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.IOS/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.VisionOS/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.Linux/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.MacOS/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.VisualStudio/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.UWP/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.Emscripten/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Toolchain.Windows/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.TundraBackend/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.Stevedore.Program/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Rocks/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Mdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.CSharpSupport/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.DotNet/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bee.VisualStudioSolution/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"osx-arm64": ["osx", "unix-arm64", "unix", "any", "base"], "osx.10.10-arm64": ["osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.10.11-arm64": ["osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.10.12-arm64": ["osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.10.13-arm64": ["osx.10.13", "osx.10.12-arm64", "osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.10.14-arm64": ["osx.10.14", "osx.10.13-arm64", "osx.10.13", "osx.10.12-arm64", "osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.10.15-arm64": ["osx.10.15", "osx.10.14-arm64", "osx.10.14", "osx.10.13-arm64", "osx.10.13", "osx.10.12-arm64", "osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.10.16-arm64": ["osx.10.16", "osx.10.15-arm64", "osx.10.15", "osx.10.14-arm64", "osx.10.14", "osx.10.13-arm64", "osx.10.13", "osx.10.12-arm64", "osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.11.0-arm64": ["osx.11.0", "osx.10.16-arm64", "osx.10.16", "osx.10.15-arm64", "osx.10.15", "osx.10.14-arm64", "osx.10.14", "osx.10.13-arm64", "osx.10.13", "osx.10.12-arm64", "osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.12-arm64": ["osx.12", "osx.11.0-arm64", "osx.11.0", "osx.10.16-arm64", "osx.10.16", "osx.10.15-arm64", "osx.10.15", "osx.10.14-arm64", "osx.10.14", "osx.10.13-arm64", "osx.10.13", "osx.10.12-arm64", "osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"], "osx.13-arm64": ["osx.13", "osx.12-arm64", "osx.12", "osx.11.0-arm64", "osx.11.0", "osx.10.16-arm64", "osx.10.16", "osx.10.15-arm64", "osx.10.15", "osx.10.14-arm64", "osx.10.14", "osx.10.13-arm64", "osx.10.13", "osx.10.12-arm64", "osx.10.12", "osx.10.11-arm64", "osx.10.11", "osx.10.10-arm64", "osx.10.10", "osx-arm64", "osx", "unix-arm64", "unix", "any", "base"]}}