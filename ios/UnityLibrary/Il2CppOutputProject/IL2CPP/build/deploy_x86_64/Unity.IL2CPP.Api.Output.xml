<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Unity.IL2CPP.Api.Output</name>
    </assembly>
    <members>
        <member name="T:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable">
            <summary>
            Analytics data from il2cpp.exe
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.build_event_id">
            <summary>
            The build_event_id from the Editor
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.node_executed">
            <summary>
            Whether or not il2cpp.exe was executed
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.attribute_total_count_eager_static_constructor">
            <summary>
            The total number of eager static constructor attributes
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.attribute_total_count_set_option">
            <summary>
            The total number of il2cpp set option attributes
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.attribute_total_count_generate_into_own_cpp_file">
            <summary>
            The total number of generate into own cpp file attributes
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.attribute_total_count_ignore_by_deep_profiler">
            <summary>
            The total number of ignore deep profiler attributes
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.extra_types_total_count">
            <summary>
            The total number of extra types defined by all extra type files
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_extra_types_file_count">
            <summary>
            The number of extra types files that were included
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_debug_assembly_name_count">
            <summary>
            The number of assemblies to emit debug information for
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_additional_cpp_count">
            <summary>
            The number of additional cpp files that were included
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_emit_null_checks">
            <summary>
            Whether or not emitting null checks was enabled
            
            Note that this option can also be enabled via the code generation option field
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_enable_stacktrace">
            <summary>
            Whether or not emitting stack traces was enabled
            
            Note that this option can also be enabled via the code generation option field
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_enable_deep_profiler">
            <summary>
            Whether or not deep profiler support was enabled
            
            Note that this option can also be enabled via the feature field
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_enable_stats">
            <summary>
            Whether or not emitting stats was enabled
            
            Note that this option can also be enabled via the diagnostic option field
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_enable_array_bounds_check">
            <summary>
            Whether or not array bounds checks was enabled
            
            Note that this option can also be enabled via the code generation option field
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_enable_divide_by_zero_check">
            <summary>
            Whether or not divide by zero checks was enabled
            
            Note that this option can also be enabled via the code generation option field
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_emit_comments">
            <summary>
            Whether or not emitting comments was enabled
            
            Note that this option can also be enabled via the code generation option field
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_disable_generic_sharing">
            <summary>
            Whether or not generic sharing should be disabled
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_maximum_recursive_generic_depth">
            <summary>
            The maximum recursive generic depth
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_generic_virtual_method_iterations">
            <summary>
            The number of times to iterate looking for generic virtual methods
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_code_generation_option">
            <summary>
            Flags indicating which code generation options were enabled
            
            Possible Values:
              None
              EnableNullChecks
              EnableStacktrace
              EnableArrayBoundsCheck
              EnableDivideByZeroCheck
              EnableLazyStaticConstructors
              EnableComments
              EnableSerial
              EnablePerAssemblyMetadata
              EnableInlining
              VirtualCallsViaInvokers
              SharedGenericCallsViaInvokers
              DelegateCallsViaInvokers
            
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_file_generation_option">
            <summary>
            Flags indicating which file generation options were enabled
            
            Possible Values:
              None
              EmitSourceMapping
              EmitMethodMap
            
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_generics_option">
            <summary>
            Flags indicating which generics options were enabled
            
            Possible Values:
              None
              EnableSharing
              EnableEnumTypeSharing
              EnablePrimitiveValueTypeGenericSharing
              EnableFullSharing
              EnableLegacyGenericSharing
              EnableFullSharingForStaticConstructors
            
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_feature">
            <summary>
            Flags indicating whether or not various features were enabled
            
            Possible Values:
              None
              EnableReload
              EnableCodeConversionCache
              EnableDebugger
              EnableDeepProfiler
              EnableAnalytics
            
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_diagnostic_option">
            <summary>
            Flags indicating which diagnostic options were enabled
            
            Possible Values:
              None
              EnableStats
              NeverAttachDialog
              EmitAttachDialog
              EnableTinyDiagnostics
              DebuggerOff
              EmitReversePInvokeWrapperDebuggingHelpers
              EnableDiagnostics
              EnableTinyStaticConstructorExplanation
            
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_convert_to_cpp">
            <summary>
            Whether or not il2cpp was used to generate code
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_compile_cpp">
            <summary>
            Whether or not il2cpp was used to compile code
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_development_mode">
            <summary>
            Whether or not the this is a development build
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_enable_debugger">
            <summary>
            Whether or not support for the managed debugger was enabled
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_generate_usym_file">
            <summary>
            Whether or not generating a usym file was enabled
            </summary>
        </member>
        <member name="P:Unity.IL2CPP.Api.Output.Analytics.Il2CppDataTable.option_jobs">
            <summary>
            The number of jobs that were requested.  Defaults to the processor count
            </summary>
        </member>
    </members>
</doc>
