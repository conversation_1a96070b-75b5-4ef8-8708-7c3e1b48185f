<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Analytics.Api.Output</name>
    </assembly>
    <members>
        <member name="P:Analytics.Api.Output.BuildAnalyticsCollectionEvent.message">
            <summary>
            Human readable message describing the error encountered while collecting the player build analytics.
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.BuildAnalyticsCollectionEvent.code">
            <summary>
            Enum value for the error encountered while collecting the player build analytics.
            </summary>
        </member>
        <member name="T:Analytics.Api.Output.BuildAnalyticsCollectionInfo">
            <summary>
            Analytics data for the collection of player build analytics. This table records the performance and any errors encountered when running Analytics.exe after a Bee build.
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.BuildAnalyticsCollectionInfo.build_guid">
            <summary>
            buildGUID is a random GUID. Editor generates the GUID and sends that buildGUID to EditorAnalytics with (cloudProjectId and buildGUID) in BuildInfoEvent. Editor will also bake the buildGUID part of the build for Runtime. Runtime will use the baked buildGUID and send events to CDP/UCA with cloudProjectId and buildGUID
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.BuildAnalyticsCollectionInfo.build_event_id">
            <summary>
            A random GUID generated from the editor passed to the Analytics.exe. All table rows generated by Analytics.exe must also contain this GUID to allow the events to be joined across tables.
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.BuildAnalyticsCollectionInfo.errors">
            <summary>
            Array of errors encountered while collecting the player build analytics.
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.BuildAnalyticsCollectionInfo.summary_collector_exit_code">
            <summary>
            Exit code of the Analytics.exe process (sub process launched to parse the build output directory and collect analytics info)
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.BuildAnalyticsCollectionInfo.time_to_run_summary_collector">
            <summary>
            (milliseconds) Time taken to run the Analytics.exe process (sub process launched to parse the build output directory and collect analytics info)
            </summary>
        </member>
        <member name="T:Analytics.Api.Output.BuildAnalyticsErrorCodes">
            <summary>
            Should be kept in sync with
            https://github.cds.internal.unity3d.com/unity/unity/blob/trunk/Modules/UnityEditorAnalyticsEditor/Events/BuildAnalyticsCollectionEvent.h#L15-L32
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.UnknownError">
            <summary>
            Unknown Error
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseGenericError">
            <summary>
            Failed parsing json document.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseNoRootError">
            <summary>
            Unable to find root node 'player_build_data.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseInvalidRootError">
            <summary>
            Invalid root node encountered.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseEventNotAnObjectError">
            <summary>
            Invalid event found. Event not formatted as an object.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseTableNameNotFoundError">
            <summary>
            Event name not found.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseInvalidTableNameError">
            <summary>
            Invalid event name encountered.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseEventMissingMemberError">
            <summary>
            Missing required member in event.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseEventUnknownMemberError">
            <summary>
            Unknown member encountered in event.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseEventMessageNotAnObjectError">
            <summary>
            Invalid event message found. Message not formatted as an object.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseEventMessageEmptyError">
            <summary>
            Empty message found in event.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseEventMessageMissingBuildEventId">
            <summary>
            Missing required 'build_event_id' member in event.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseEventMessageMismatchedBuildEventId">
            <summary>
            Build event id found in event does not match the expected value.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.SummaryCollectorProcessFailedToLaunch">
            <summary>
            The Build Analytics Build Summary failed to launch.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.SummaryCollectorProcessTimeout">
            <summary>
            The Build Analytics Build Summary process timed out.
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.JsonParseTableVersionNotInt">
            <summary>
            Table version number can not be parsed as an int..
            </summary>
        </member>
        <member name="F:Analytics.Api.Output.BuildAnalyticsErrorCodes.SummaryCollectorNonZeroExit">
            <summary>
            The Build Analytics Build Summary failed with non-zero exit code.
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.NodeTimingData.name">
            <summary>
            The name of the tool node
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.NodeTimingData.duration">
            <summary>
            (milliseconds) The amount of time it took for the tool to complete
            </summary>
        </member>
        <member name="T:Analytics.Api.Output.PlayerBuildTimingData">
            <summary>
            Analytics for stages of the player build
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.build_event_id">
            <summary>
            The build_event_id from the Editor
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.build_player">
            <summary>
            (milliseconds) The total player build time
            #details
            Should always appear unless the duration is under tiny profilers minimum duration threshold
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.preprocess_player">
            <summary>
            (milliseconds) The time it took to complete the pre process built player section
            #details
            Should always appear unless the duration is under tiny profilers minimum duration threshold
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.produce_player_script_assemblies">
            <summary>
            (milliseconds) The total time it took to produce the script assemblies
            #details
            Should always appear unless the duration is under tiny profilers minimum duration threshold
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.build_scripts_dlls">
            <summary>
            (milliseconds) The time it took to build the script assemblies
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.writing_asset_files">
             <summary>
             (milliseconds) The total time it took to write all asset files
            
             May not appear during no change builds
             </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.postprocess_built_player">
            <summary>
            (milliseconds) The time it took to complete the post process built player section
            #details
            Should always appear unless the duration is under tiny profilers minimum duration threshold
            </summary>
        </member>
        <member name="P:Analytics.Api.Output.PlayerBuildTimingData.node_summary_table">
            <summary>
            Timing data for nodes in the player build graph
            </summary>
        </member>
    </members>
</doc>
