<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Bee.BeeDriver</name>
    </assembly>
    <members>
        <member name="T:Bee.BeeDriver.BeeDriver">
             <summary>
             BeeDriver is a tool that lets one setup and control external bee graphs. Unity uses this for script compilation.
             You are supposed to bring your own prebuilt buildprogram that can create bee DAG .json files. BeeDriver will invoke
             your buildprogram whenever it has no previous dag.json graph, or when the bee backend (currently tundra2.exe) has reason to
             believe the previous dag.json file is no longer reliable.
            
             Once you supply your buildprogram, you can call beeDriver.Build(), or beeDriver.BuildAsync(), and when at
             some point bee is done building, you get back an BeeDriverResult result that has results of all individual nodes that were built.
             It is up to the user of BeeDriver to parse those results.
            
             It is common to want to share some data between the user of BeeDriver and the build program. You can use the .InputData.Add()
             facility to help with this. The data you pass in will be serialized into a json file that is easy for the buildprogram to consume. Unity
             uses this to inform the buildprogram about things like playersettings.
            
             BeeDriver exposes several extension points. You can supply your own implementation of ProgressAPI, which wll be invoked
             (on a thread) in realtime as nodes are building.
            
             You can supply your own SourceFileUpdaterBase. The Unity script updater is an example of this. Each SourceFileUpdaterBase gets
             to take a look at output from nodes and say if it thinks it can fix any problems. If so, it will be run.
            
             The last extension point is the SourceFileUpdatersResultHandler. Here you have to provide an implementation that is responsible
             for doing something with the results of the source file updaters. In Unity's case this is not trivial: to overwrite files it
             has to ask the VCS integration to make files readable.  It has to ask for consent for the user. If the files live in an immutable package,
             it needs to let packman know it will be overwriting some files.  Unity's usage of BeeBriver provides an implementation of SourceFileUpdatersResultHandler
             that does all this.
             </summary>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriver.DagJsonFile">
            <summary>
            The location of the dag json file used by the build
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.#ctor(Bee.BeeDriver.RunnableProgram,Bee.BeeDriver.RunnableProgram,System.String,System.String,System.String,Bee.BeeDriver.SourceFileUpdaterBase[],Bee.BeeDriver.SourceFileUpdatersResultHandler,Bee.BeeDriver.ProgressAPI,System.Boolean,System.String,System.String)">
            <summary>
            Entrypoint for the BeeDriver. Once you construct it, invoke .Add(), .Build() or .BuildAsync()
            </summary>
            <param name="buildProgram">The buildprogram that should be used. Typically one would use a SystemProcessRunnableProgram here.</param>
            <param name="backendProgram">Which tundra backend program to use.</param>
            <param name="projectRoot">The root directory for the build graph. Typically this matches the Unity's project folder.</param>
            <param name="dagName">The name of the dag to use. Choose different dag names if you have different configurations that user will switch between.</param>
            <param name="buildStateDirectory">Directory where to store the buildstate files.</param>
            <param name="sourceFileUpdaters">A list of SourceFileUpdaters to use for this BeeDriver.</param>
            <param name="processSourceFileUpdatersResult">An implementation that can take care of handling the results of the sourcefile updaters.</param>
            <param name="progressApi">An implementation that will be notified on a thread, in realtime, of the progress of the build.</param>
            <param name="continueBuildingAfterFirstFailure">If set to false the bee driver will stop as quickly as possible once a single node has failed.</param>
            <param name="profilerOutputFile">Path to write a chrome://tracing style profiler output file for the build.</param>
            <param name="profilerProcessName">The process name to report in the profiler output file</param>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriver.DataForBuildProgram">
            <summary>
            Use DataForBuildProgram to write c# objects to disk that can be conveniently read from your buildprogram.
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriver.DataFromBuildProgram">
            <summary>
            Use DataFromBuildProgram to read c# objects from disk that have been written by the buildprogram.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.Build(System.String)">
            <summary>
            This builds the given targetname, and will only return when a buildresult is ready.
            </summary>
            <param name="target">the name of a target in the buildgraph generated by the buildprogram</param>
            <returns>The BeeDriverResult for this build</returns>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.Build">
            <summary>
            This builds all targets, and will only return when a buildresult is ready.
            </summary>
            <returns>The BeeDriverResult for this build</returns>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.WaitForResult">
            <summary>
            Block until the result of the currently active build is ready.
            </summary>
            <returns>The BeeDriverResult for the currently active build</returns>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.BuildAsync">
            <summary>
            Start a build, and immediately return. You can periodically invoke .Tick() to see if the build is already finished.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.BuildAsync(System.String)">
            <summary>
            Start a build, and immediately return. You can periodically invoke .Tick() to see if the build is already finished.
            </summary>
            <param name="target">the name of a target in the buildgraph generated by the buildprogram</param>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.Tick(System.Boolean)">
            <summary>
            Use Tick() to periodically check if the currently active build is already finished.
            </summary>
            <returns>Will return null if the build is not finished, otherwise will return the BeeDriverResult of the active build</returns>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.CancelBuild">
            <summary>
            Cancel build. This does not abort the build immediately but rather gracefully shuts down the build.
            Users should keep calling Tick() until a result is returned.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.Dispose">
            <summary>
            Calling Dispose will abort any currently active build. This might not be immediate as the backend process that might be running could require a bit of time
            to finish currently in-flight nodes as well as write out some bookkeeping data files.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriver.Finalize">
            <summary>
            Finalizer. A safety mechinism to make sure all resources are cleaned up even if .Dispose is not called.
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.BeeDriverResult">
            <summary>
            A data object holding information related to the result of a build
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriverResult.NodeResults">
            <summary>
            An array of all the nodeResults for all nodes that have executed as part of the build
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriverResult.Success">
            <summary>
            Whether or not the build was successful.
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriverResult.BeeDriverMessages">
            <summary>
            Messages that are not about individual nodes, but about BeeDriver specific things that have to be communicated. Things like
            a buildprogram throwing an exception. A script updater not producing any results when it promised to do so. The backend crashing.
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.BeeDriverResult.Message">
            <summary>
            A Message that is not about individual nodes, but about BeeDriver specific things that have to be communicated. Things like
            a buildprogram throwing an exception. A script updater not producing any results when it promised to do so. The backend crashing.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriverResult.Message.#ctor(System.String,Bee.BeeDriver.BeeDriverResult.MessageKind)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriverResult.Message.Text">
            <summary>
            The text payload of the message
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.BeeDriverResult.Message.Kind">
            <summary>
            Wether the message is a warning or an error
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriverResult.Message.ToString">
            <summary>
            string representation of the message
            </summary>
            <returns></returns>
        </member>
        <member name="M:Bee.BeeDriver.BeeDriverResult.ToString">
            <summary>
            text summary of the success status and all beedriver messages.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Bee.BeeDriver.BeeDriverResult.MessageKind">
            <summary>
            Warning/Error enum for BeeDrivermessage
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.BeeDriverResult.MessageKind.Warning">
            <summary>
            It was a warning
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.BeeDriverResult.MessageKind.Error">
            <summary>
            It was an error
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.BeeBackendLogMessage">
            <summary>
            base class for various messages that can appear in a bee backend log
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.BeeBackendLogMessage.msg">
            <summary>
            the message payload
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.NodeResult">
            <summary>
            Results from the bee backend about a specific node having been executed.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.annotation">
            <summary>
            the annotation of the dag node
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.displayName">
            <summary>
            Human-readable description of what this node does for showing in progress bars
            or other UI elements.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.stdout">
            <summary>
            the stdout of the action that was run
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.outputfile">
            <summary>
            the first outputfile of the node
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.outputdirectory">
            <summary>
            the first output directory if any of the node
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.exitcode">
            <summary>
            the exitcode the action finished with
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.processed_node_count">
            <summary>
            this corresponds to the first number in the [20/3043] messages that you see in normal bee output.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.number_of_nodes_ever_queued">
            <summary>
            The total number of nodes ever queued. Beware that this number is not constant. It is guaranteed to never decrease.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.cmdline">
            <summary>
            The commandline of this node.  will only be filled in when the node failed.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.rsps">
            <summary>
            The response files used by this node. will only be filled in when the node failed.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.NodeResult.profiler_output">
            <summary>
            If this node had a profileroutput set it will be reported here
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.ProgressAPI">
            <summary>
            When using a BeeDriver, you can pass in a class derived from ProgressAPI to be notified of the progress of a running build. Beware
            that these progress notifications will be delivered on a background thread. They are guaranteed to come from the same thread, but you
            have to take care of data-synchronization with your mainthread yourself.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.ProgressAPI.Start">
            <summary>
            Override this method. The object it returns will receive the Report invocations for this build.
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.ProgressAPI.ProgressToken">
            <summary>
            Derive from ProgressToken your own class that has the logic you want to happen on progress notifications.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.ProgressAPI.ProgressToken.Report(System.String)">
            <summary>
            This overload of Report is called for phase-changing messages. things like "running buildprogram". "running scriptupdater".
            </summary>
            <param name="msg"> the message payload </param>
        </member>
        <member name="M:Bee.BeeDriver.ProgressAPI.ProgressToken.Report(Bee.BeeDriver.NodeResult)">
            <summary>
            This overload of Report is called for each node that finished running.
            </summary>
            <param name="nodeResult">nodeResult is a dataobject that has a lot of detail about the node that just finished running</param>
        </member>
        <member name="M:Bee.BeeDriver.ProgressAPI.ProgressToken.ReportNodeStarted(Bee.BeeDriver.NodeResult)">
            <summary>
            ReportNodeStarted is called for each node that starts running.
            </summary>
            <param name="nodeResult">nodeResult is a dataobject that has a lot of detail about the node that just started running</param>
        </member>
        <member name="M:Bee.BeeDriver.ProgressAPI.ProgressToken.Finish">
            <summary>
            Finish will be called when the build has finished.
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.RunnableProgram">
            <summary>
            Abstraction of runnable programs that makes it easy to test different bee driver components against fake external programs that we can easily make crash / do uncommon things
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunnableProgram.Start(System.String,System.String[])">
            <summary>
            Start should start this runnable program, and return a RunningProgram derived object.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunnableProgram.StartImpl(System.String,System.String[])">
            <summary>
            StartImpl will be invoked when the user invokes Start()
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.RunningProgram">
            <summary>
            Abstraction layer for running programs.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunningProgram.#ctor(Bee.BeeDriver.StdOutMode)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.RunningProgram.HasExited">
            <summary>
            Whether or not this running program has exited
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.RunningProgram.HasExitedImpl">
            <summary>
            This method should return wether or not your runningprogram has exited.
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.RunningProgram.ExitCode">
            <summary>
            The exitcode this running program exited with
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.RunningProgram.Name">
            <summary>
            The name by which to refer to this program in log messages
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunningProgram.GetStdoutAndStdErrCombined">
            <summary>
            The combined stdout/stderr output of the running program that has exited
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunningProgram.WaitForExit">
            <summary>
            Will return when the program has exited.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunningProgram.Abort">
            <summary>
            Aborts this running program
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunningProgram.AbortImpl">
            <summary>
            AbortImpl will be invoked when the user wants to abort this process
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunningProgram.CloseStandardInput">
            <summary>
            Will close the standard input stream to the process. This can be used as a cross platform mechanism to signal "your host would like you to stop running now".
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.RunningProgram.CloseStandardInputImpl">
            <summary>
            CloseStandardInputImpl will be invoked when the user invokes CloseStandardInput()
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.SourceFileUpdaterBase">
            <summary>
            Implement this base class to plug in a facility like the Unity script updater that is able to modify original source files based on compiler errors/warnings.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.SourceFileUpdaterBase.StartIfYouCanFixProblemsInTheseMessages(Bee.BeeDriver.NodeResult,Bee.BeeDriver.BeeDriver)">
            <summary>
            Implement this method to return a Task specific to your implementation that represents an ongoing process that will produce updated files.
            If the updater does not have anything to update, it's supposed to return null.
            </summary>
            <param name="nodeResult">The node result to analyze. typically you would look into the .stdout property.</param>
            <param name="beeDriver">The beeDriver doing the build.</param>
            ///
            <returns></returns>
        </member>
        <member name="T:Bee.BeeDriver.SourceFileUpdaterBase.Task">
            <summary>
            The base type that should be used for what your SourceFileUpdaterBase implementation returns from StartIfYouCanFixProblemsInTheseMessages
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.SourceFileUpdaterBase.Task.#ctor(Bee.BeeDriver.NodeResult,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="nodeResult">The noderesult this task will attempt to fix</param>
            <param name="produceErrorIfNoUpdatesAreProduced">If we should produce an error if no updates are produced. Set this to false if you're not sure ahead of time if your updater will be able to fix the problem.</param>
        </member>
        <member name="P:Bee.BeeDriver.SourceFileUpdaterBase.Task.Finished">
            <summary>
            Whether or not this currently running updater is finished
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.SourceFileUpdaterBase.Task.WaitUntilFinished">
            <summary>
            Block until the sourcefile updater finished
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.SourceFileUpdaterBase.Task.NodeResult">
            <summary>
            The noderesult that has the failure this updater is trying to fix
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.SourceFileUpdaterBase.Task.ProduceErrorIfNoUpdatesAreProduced">
            <summary>
            If we should produce an error if no updates are produced. Set this to false if you're not sure ahead of time if your updater will be able to fix the problem.
            </summary>
        </member>
        <member name="P:Bee.BeeDriver.SourceFileUpdaterBase.Task.Results">
            <summary>
            The results of the running of this sourcefile updater
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.SourceFileUpdaterBase.Task.Abort">
            <summary>
            Invoking Abort will stop the running source updating proces as soon as possible
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.SourceFileUpdaterBase.Results">
            <summary>
            Data about the results of running a sourcefile updater.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.SourceFileUpdaterBase.Results.ProducedUpdates">
            <summary>
            Returns the produced updates by this sourcefile updater.
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.SourceFileUpdaterBase.Results.Messages">
            <summary>
            Returns the list of messages for this sourcefile updater. Use this to communciate errors/warnings that happened as part of the updating.
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.SourceFileUpdaterBase.Update">
            <summary>
            A type that represents a single update the sourcefile updater has produced
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.SourceFileUpdaterBase.Update.originalFileWithError">
            <summary>
            the original file that had the error the sourcefileupdater knows how to fix
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.SourceFileUpdaterBase.Update.tempFileWithNewContents">
            <summary>
            the actual fixed content to be applied to the original file with error.
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.SourceFileUpdatersResultHandler">
            <summary>
            Implementors of this base class are responsible for applying the results of all the source file updaters.
            The Unity implementation of this class will coordinate with the VCS integration, with Packman, and ask for user consent
            depending on if the sourcecode is versioned or in a package.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.SourceFileUpdatersResultHandler.ProcessUpdaterResults(Bee.BeeDriver.SourceFileUpdaterBase.Update[])">
            <summary>
            This method will be invoked after the beedriver has finished waiting on all running script updaters.
            </summary>
            <param name="producedUpdates">The updates that all sourcefileupdaters have produced that need to be applied</param>
        </member>
        <member name="T:Bee.BeeDriver.StdOutMode">
            <summary>
            StdOut writing behaviour for a SystemProcessRunnableProgram
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.StdOutMode.Off">
            <summary>
            Don't log anything
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.StdOutMode.Stream">
            <summary>
            Stream the programs stdout and stderr directly to the host process stdout/stderr
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.StdOutMode.LogStartArgumentsAndExitcode">
            <summary>
            Write to stdout the starting arguments and exist code
            </summary>
        </member>
        <member name="F:Bee.BeeDriver.StdOutMode.LogStdOutOnFinish">
            <summary>
            Write to stdout the captured stdout when the program finished
            </summary>
        </member>
        <member name="T:Bee.BeeDriver.SystemProcessRunnableProgram">
            <summary>
            Use SystemProcessRunnableProgram to represent a normal system process that can be run.
            </summary>
        </member>
        <member name="M:Bee.BeeDriver.SystemProcessRunnableProgram.#ctor(System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="executable">The executable to run</param>
            <param name="alwaysArgument">Any arguments that should always be passed to the executable</param>
        </member>
        <member name="M:Bee.BeeDriver.SystemProcessRunnableProgram.#ctor(System.String,System.String[],System.Collections.Generic.Dictionary{System.String,System.String},Bee.BeeDriver.StdOutMode)">
            <summary>
            Constructor
            </summary>
            <param name="executable">The executable to run</param>
            <param name="alwaysArguments">Any arguments that should always be passed to the executable</param>
            <param name="alwaysEnvironmentVariables">Environment variables to set when running this program</param>
            <param name="stdOutMode">The stdout logging behaviour to use</param>
        </member>
        <member name="M:Bee.BeeDriver.SystemProcessRunnableProgram.StartImpl(System.String,System.String[])">
            <summary>
            The Start implementation.
            </summary>
        </member>
        <member name="T:Bee.Serialization.ObjectsToDisk">
            <summary>
            A helper class to facilitate easy typesafe data transfer between two .net processes that reference a single shared data assembly.
            </summary>
        </member>
        <member name="P:Bee.Serialization.ObjectsToDisk.DidWrite">
            <summary>
            Returns if Write() has already been called
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsToDisk.Add``1(``0)">
            <summary>
            Adds a csharp object as a data payload. It will be serialized and readable on the reading process through .Get()
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsToDisk.Add(System.Type,System.Object)">
            <summary>
            Adds a csharp object as a data payload. It will be serialized and readable on the reading process through .Get()
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsToDisk.Write(System.String)">
            <summary>
            Writes all the data that was Add()ed to a file
            </summary>
        </member>
        <member name="T:Bee.Serialization.ObjectsFromDisk">
            <summary>
            A helper class to facilitate easy typesafe data transfer between two .net processes that reference a single shared data assembly.
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsFromDisk.#ctor(System.String)">
            <summary>
            Read and deserialize a json file into an ObjectsFromDisk instance
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsFromDisk.FromString(System.String)">
            <summary>
            Read and deserialize a json string into an ObjectsFromDisk instance
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsFromDisk.Get``1">
            <summary>
            Get a deserialied csharp object from a serialized file. It must have been .Add()ed from an ObjectsToDisk class previously
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsFromDisk.TryGet``1(``0@)">
            <summary>
            Get a deserialied csharp object from a serialized file. It must have been .Add()ed from an ObjectsToDisk class previously
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsFromDisk.TryGet(System.Type,System.Object@)">
            <summary>
            Get a deserialied csharp object from a serialized file. It must have been .Add()ed from an ObjectsToDisk class previously
            </summary>
        </member>
        <member name="M:Bee.Serialization.ObjectsFromDisk.Get(System.Type)">
            <summary>
            Get a deserialied csharp object from a serialized file. It must have been .Add()ed from an ObjectsToDisk class previously
            </summary>
        </member>
        <member name="T:Bee.Core.TinyProfiler2Base">
            <summary>Methods shared by both <see cref="T:Bee.Core.TinyProfiler2"/>and <see cref="T:Bee.Core.Track"/></summary>
        </member>
        <member name="M:Bee.Core.TinyProfiler2Base.Section(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>Profile a section tracked by an IDisposable object</summary>
            <remarks>TraceEvent duration is calculated the first time Dispose is called.</remarks>
            <param name="label">Label of what is being profiled. If omitted, this is the name of the calling function.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
            <returns>An IDisposable object tracking the profiled section.</returns>
        </member>
        <member name="M:Bee.Core.TinyProfiler2Base.Section(System.String,System.Action,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>Profiles the submitted Action</summary>
            <param name="label">Label of what is being profiled.</param>
            <param name="action">Code to be profiled.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2Base.Section``1(System.String,System.Func{``0},System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>Profiles the submitted Func</summary>
            <param name="label">Label of what is being profiled.</param>
            <param name="func">Code to be profiled.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2Base.Section(System.Action,System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>Profiles the submitted Action</summary>
            <param name="action">Code to be profiled.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
            <param name="label">Default initialized to calling member name</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2Base.Section``1(System.Func{``0},System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>Profiles the submitted Func</summary>
            <param name="func">Code to be profiled.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
            <param name="label">Default initialized to calling member name</param>
        </member>
        <member name="T:Bee.Core.TinyProfiler2">
            <summary>A profiler capable of emitting a chrome trace report</summary>
            <remarks>
            </remarks>
        </member>
        <member name="P:Bee.Core.TinyProfiler2.Global">
            <summary>Global profiler instance</summary>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.ResetGlobalInstance">
            <summary>Resets the global instance</summary>
        </member>
        <member name="P:Bee.Core.TinyProfiler2.StartTime">
            <summary>Absolute time of profiler instantiation</summary>
            <remarks>Can be used to align event timestamps from other systems to this profiler instance.</remarks>
        </member>
        <member name="P:Bee.Core.TinyProfiler2.ElapsedProfilerTime">
            <summary>Time that has passed since profiler instantiation</summary>
        </member>
        <member name="P:Bee.Core.TinyProfiler2.CurrentThreadTrack">
            <summary>Get or implicitly create a track for the current thread</summary>
            <remarks>A track need at least one event to be visible in the chrome trace report</remarks>
        </member>
        <member name="P:Bee.Core.TinyProfiler2.DefaultOptions">
            Default options for writing a chrome trace report
        </member>
        <member name="M:Bee.Core.TinyProfiler2.#ctor">
            <summary>Create a new profiler instance</summary>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.SetupNewTrack(System.String)">
            <summary>Setup a new track</summary>
            <remarks>
            A track for the most common use case represents a thread.
            But a user can also explicitly setup a track to hold submitted events and sections.</remarks>
            <param name="trackName">Name of the track</param>
            <returns>An instance assigned to the created track</returns>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.AddExternalTraceEventsFile(System.String)">
            <summary>Add .traceevents file to be included in the report</summary>
            <param name="traceEventsFile">Path to .traceevents file</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.AddExternalTraceEventsFile(System.Threading.Tasks.Task{System.String})">
            <summary>Add .traceevents file to be included in the report</summary>
            <remarks>
            This function allow you to pass a Task returning the file instead of the file itself.
            Using this you can start writing out the profile data, before all ExternalTraceFiles are ready.
            ExternalTraceFiles are consumed as late as possible and only when ready.
            </remarks>
            <param name="traceEventsFile">Task to be completed before appending .traceevents file to the report</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.Write(System.String,Bee.Core.ChromeTraceOptions)">
            <summary>Write a chrome trace events to stream</summary>
            <remarks>
            If the extension of the file passed is '.traceevents' this method will invoke WriteChromeTraceEvents(), otherwise it will call WriteChromeTrace()
            See <see cref="F:Bee.Core.ChromeTraceOptions.ExternalTraceEventFiles"/>.
            </remarks>
            <param name="file">output file</param>
            <param name="options">chrome trace serialization options</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.WriteChromeTrace(System.String,Bee.Core.ChromeTraceOptions)">
            <summary>Write a chrome trace report data to file</summary>
            <param name="file">output file</param>
            <param name="options">chrome trace serialization options</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.WriteChromeTraceEvents(System.String,Bee.Core.ChromeTraceOptions)">
            <summary>Write a chrome trace events to stream</summary>
            <remarks>
            This is a list of trace events that can later be concatenated with reports from other processes or sessions.
            See <see cref="F:Bee.Core.ChromeTraceOptions.ExternalTraceEventFiles"/>.
            </remarks>
            <param name="file">output file</param>
            <param name="options">chrome trace serialization options</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.WriteChromeTrace(System.IO.TextWriter,Bee.Core.ChromeTraceOptions)">
            <summary>Write a chrome trace report data to stream</summary>
            <param name="output">output stream</param>
            <param name="options">chrome trace serialization options</param>
        </member>
        <member name="M:Bee.Core.TinyProfiler2.WriteChromeTraceEvents(System.IO.TextWriter,Bee.Core.ChromeTraceOptions)">
            <summary>Write a chrome trace events to stream</summary>
            <remarks>
            This is a list of trace events that can later be concatenated with reports from other processes or sessions.
            See <see cref="F:Bee.Core.ChromeTraceOptions.ExternalTraceEventFiles"/>.
            </remarks>
            <param name="output">output stream</param>
            <param name="options">chrome trace serialization options</param>
        </member>
        <member name="T:Bee.Core.Track">
             <summary>A track in the chrome trace report is the same as "lane" or "thread"</summary>
             <remarks>
             A track is either an explicitly created track <see cref="M:Bee.Core.TinyProfiler2.SetupNewTrack(System.String)"/> or implicitly created
             for the current thread <see cref="P:Bee.Core.TinyProfiler2.CurrentThreadTrack"/>.
            
             Note that when creating events manually using an external clock source. Make sure timestamp is relative to <see cref="P:Bee.Core.Track.ProfilerStartTime"/>.
             One way to do that is to use <see cref="M:Bee.Core.Track.CreateEvent(System.DateTimeOffset,System.String,System.Collections.Generic.Dictionary{System.String,System.String})"/>.
             </remarks>
        </member>
        <member name="P:Bee.Core.Track.Id">
            <summary>Track Id</summary>
            <remarks>
            Explicitly created tracks use upper 32 bits to generate unique ids while thread tracks use the lower 32 bits
            to store <see cref="P:System.Threading.Thread.ManagedThreadId"/>
            </remarks>
        </member>
        <member name="P:Bee.Core.Track.Name">
            <summary>Name of the track</summary>
            <remarks>Thread tracks copy <see cref="P:System.Threading.Thread.Name"/> at the time of track creation</remarks>
        </member>
        <member name="P:Bee.Core.Track.ElapsedProfilerTime">
            <summary>Time that has elapsed since the profiler was instantiated</summary>
        </member>
        <member name="P:Bee.Core.Track.ProfilerStartTime">
            <summary>Time of profiler instantiation</summary>
        </member>
        <member name="M:Bee.Core.Track.#ctor(System.Threading.Thread,Bee.Core.TinyProfiler2)">
            <summary>Create track from thread</summary>
            <param name="thread"></param>
            <param name="profiler"></param>
        </member>
        <member name="M:Bee.Core.Track.#ctor(System.String,Bee.Core.TinyProfiler2)">
            <summary>Create track from name</summary>
            <param name="name"></param>
            <param name="profiler"></param>
        </member>
        <member name="M:Bee.Core.Track.CreateEvent(System.TimeSpan,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>Create and register a trace event with the profiler</summary>
            <remarks>Duration can be set after the fact.</remarks>
            <param name="timestamp">Start time of the event. This should be relative instantiation time of the profiler. In most cases <see cref="P:Bee.Core.Track.ProfilerStartTime"/>.</param>
            <param name="label">Label of what is being profiled. If omitted, this is the name of the calling function.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
            <returns>A trace event initialized according to parameters</returns>
        </member>
        <member name="M:Bee.Core.Track.CreateEvent(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>Create and register a trace event with the profiler</summary>
            <remarks>
            Timestamp of the event will be set to current time at event creation.
            Duration can be set after the fact.
            </remarks>
            <param name="label">Label of what is being profiled. If omitted, this is the name of the calling function.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
            <returns>A trace event initialized according to parameters</returns>
        </member>
        <member name="M:Bee.Core.Track.CreateEvent(System.DateTimeOffset,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>Create and register a trace event with the profiler</summary>
            <remarks>Mainly intended for externally occuring events. Duration can be set after the fact.</remarks>
            <param name="timestamp">Start time of the event.</param>
            <param name="label">Label of what is being profiled. If omitted, this is the name of the calling function.</param>
            <param name="metadata">A dictionary of metadata associated with the event.</param>
            <returns>A trace event initialized according to parameters</returns>
        </member>
        <member name="M:Bee.Core.Track.SetEventDuration(Bee.Core.TraceEvent,System.TimeSpan)">
            <summary>Sets duration of the TraceEvent</summary>
            <param name="traceEvent">The event to receive duration</param>
            <param name="duration">Duration of the event</param>
            <returns>The event</returns>
        </member>
        <member name="M:Bee.Core.Track.SetEventDurationBasedOnCurrentTime(Bee.Core.TraceEvent)">
            <summary>Sets duration of the TraceEvent to ElapsedProfilerTime - event start time</summary>
            <param name="traceEvent">The event to receive duration</param>
            <returns>The event</returns>
        </member>
        <member name="M:Bee.Core.Track.SetEventDurationBasedOnEndTime(Bee.Core.TraceEvent,System.DateTimeOffset)">
            <summary>Sets duration of the TraceEvent to endTime - event start time</summary>
            <remarks>Mainly intended for externally occuring events and paired with <see cref="M:Bee.Core.Track.CreateEvent(System.TimeSpan,System.String,System.Collections.Generic.Dictionary{System.String,System.String})"/></remarks>
            <param name="traceEvent">The event to receive duration</param>
            <param name="endTime">Time when the event ended</param>
            <returns>The event</returns>
        </member>
        <member name="T:Bee.Core.TraceEvent">
            <summary>TraceEvent</summary>
        </member>
        <member name="T:Bee.Core.ChromeTraceOptions">
            <summary>Chrome trace serialization options</summary>
        </member>
        <member name="F:Bee.Core.ChromeTraceOptions.EventDurationThreshold">
            <summary>If an event has a duration less than this it will be excluded from the report</summary>
            <remarks>1 microsecond is default</remarks>
        </member>
        <member name="F:Bee.Core.ChromeTraceOptions.ProcessId">
            <summary>Id of the process</summary>
            <remarks>Defaults to current process id.</remarks>
        </member>
        <member name="F:Bee.Core.ChromeTraceOptions.ProcessName">
            <summary>Process name as seen in the chrome trace UI</summary>
            <remarks>Defaults to entry assembly name.</remarks>
        </member>
        <member name="F:Bee.Core.ChromeTraceOptions.ProcessSortIndex">
            <summary>Process sort index</summary>
            <remarks>
            This index decide how different processes are sorted in the chrome trace report.
            Lower indices, including negative ones are sorted topmost.
            </remarks>
        </member>
        <member name="F:Bee.Core.ChromeTraceOptions.ExternalTraceEventFiles">
            <summary>Additional trace event files</summary>
            <remarks>
            Any additional trace event files that should be merged with the current report.
            You need to pass a Task returning the file instead of the file itself.
            For more info: <see cref="M:Bee.Core.TinyProfiler2.AddExternalTraceEventsFile(System.Threading.Tasks.Task{System.String})"/>.
            </remarks>
        </member>
        <member name="F:Bee.Core.ChromeTraceOptions.HeaderMetadata">
            <summary>
            A dictionary of metadata about the machine that will be emitted in the header
            </summary>
        </member>
        <member name="T:Bee.Core.SectionDisposable">
            <summary>IDisposable struct that records duration on <see cref="M:Bee.Core.SectionDisposable.Dispose"/></summary>
        </member>
        <member name="M:Bee.Core.SectionDisposable.Dispose">
            <summary>Record duration</summary>
        </member>
        <member name="T:Bee.Core.TraceEventsData">
            <summary>TraceEvents data</summary>
            <remarks>
            We keep structs in a separate array from managed references to create less GC pressure.
            We also keep a separate a regular list for Metadata as we assume that usage of metadata is rare and by using a
            regular list we can create our own sparse array.
            </remarks>
        </member>
        <member name="T:Bee.Core.ChromeTrace">
            <summary>ChromeTrace is responsible for json serialization of TraceEvent data</summary>
            <remarks>
            The implementation is highly specific to the profiler implementation and should not be seen in any way as a
            general purpose utility.
            </remarks>
        </member>
        <member name="T:Bee.Core.BucketArray`1">
             <summary>BucketArray is optimized for quick lookup and insert</summary>
             <remarks>
             BucketArray will only lock the first time an entry is inserted into a bucket. Bucket sizes grow exponentially.
             For an initial bucket size of 1024 that means there are at most 21 buckets for a 32 bit integer range.
            
             Note! get by index isn't protected, which means it's undefined behavior to access an item not previously written.
             It may throw a NullReferenceException or return default(T) depending on the situation.
             </remarks>
        </member>
        <member name="T:NiceIO.NPath">
            <summary>
            A filesystem path.
            </summary>
            <remarks>
            The path can be absolute or relative; the entity it refers to could be a file or a directory, and may or may not
            actually exist in the filesystem.
            </remarks>
        </member>
        <member name="M:NiceIO.NPath.#ctor(System.String)">
            <summary>
            Create a new NPath.
            </summary>
            <param name="path">The path that this NPath should represent.</param>
        </member>
        <member name="M:NiceIO.NPath.Combine(System.String)">
            <summary>
            Create a new NPath by appending a path fragment.
            </summary>
            <param name="append">The path fragment to append. This can be a filename, or a whole relative path.</param>
            <returns>A new NPath which is the existing path with the fragment appended.</returns>
        </member>
        <member name="M:NiceIO.NPath.Combine(System.String,System.String)">
            <summary>
            Create a new NPath by appending two path fragments, one after the other.
            </summary>
            <param name="append1">The first path fragment to append.</param>
            <param name="append2">The second path fragment to append.</param>
            <returns>A new NPath which is the existing path with the first fragment appended, then the second fragment appended.</returns>
        </member>
        <member name="M:NiceIO.NPath.Combine(NiceIO.NPath)">
            <summary>
            Create a new NPath by appending a path fragment.
            </summary>
            <param name="append">The path fragment to append.</param>
            <returns>A new NPath which is the existing path with the fragment appended.</returns>
        </member>
        <member name="M:NiceIO.NPath.Combine(NiceIO.NPath[])">
            <summary>
            Create a new NPath by appending multiple path fragments.
            </summary>
            <param name="append">The path fragments to append, in order.</param>
            <returns>A new NPath which is this existing path with all the supplied path fragments appended, in order.</returns>
        </member>
        <member name="P:NiceIO.NPath.Parent">
            <summary>
            The parent path fragment (i.e. the directory) of the path.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.RelativeTo(NiceIO.NPath)">
            <summary>
            Create a new NPath by computing the existing path relative to some other base path.
            </summary>
            <param name="path">The base path that the result should be relative to.</param>
            <returns>A new NPath, which refers to the same target as the existing path, but is described relative to the given base path.</returns>
        </member>
        <member name="M:NiceIO.NPath.ChangeExtension(System.String)">
            <summary>
            Create an NPath by changing the extension of this one.
            </summary>
            <param name="extension">The new extension to use. Starting it with a "." character is optional. If you pass an empty string, the resulting path will have the extension stripped entirely, including the dot character.</param>
            <returns>A new NPath which is the existing path but with the new extension at the end.</returns>
        </member>
        <member name="P:NiceIO.NPath.IsRelative">
            <summary>
            Whether this path is relative (i.e. not absolute) or not.
            </summary>
        </member>
        <member name="P:NiceIO.NPath.FileName">
            <summary>
            The name of the file or directory given at the end of this path, including any extension.
            </summary>
        </member>
        <member name="P:NiceIO.NPath.FileNameWithoutExtension">
            <summary>
            The name of the file or directory given at the end of this path, excluding the extension.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.HasDirectory(System.String)">
            <summary>
            Determines whether the given path is, or is a child of, a directory with the given name.
            </summary>
            <param name="dir">The name of the directory to search for.</param>
            <returns>True if the path describes a file/directory that is or is a child of a directory with the given name; false otherwise.</returns>
        </member>
        <member name="P:NiceIO.NPath.Depth">
            <summary>
            The depth of the path, determined by the number of path separators present.
            </summary>
        </member>
        <member name="P:NiceIO.NPath.IsCurrentDir">
            <summary>
            Tests whether the path is the current directory string ".".
            </summary>
        </member>
        <member name="M:NiceIO.NPath.Exists(NiceIO.NPath)">
            <summary>
            Tests whether the path exists.
            </summary>
            <param name="append">An optional path fragment to append before testing.</param>
            <returns>True if the path (with optional appended fragment) exists, false otherwise.</returns>
        </member>
        <member name="M:NiceIO.NPath.DirectoryExists(NiceIO.NPath)">
            <summary>
            Tests whether the path exists and is a directory.
            </summary>
            <param name="append">An optional path fragment to append before testing.</param>
            <returns>True if the path (with optional appended fragment) exists and is a directory, false otherwise.</returns>
        </member>
        <member name="M:NiceIO.NPath.FileExists(NiceIO.NPath)">
            <summary>
            Tests whether the path exists and is a file.
            </summary>
            <param name="append">An optional path fragment to append before testing.</param>
            <returns>True if the path (with optional appended fragment) exists and is a file, false otherwise.</returns>
        </member>
        <member name="P:NiceIO.NPath.Extension">
            <summary>
            The extension of the file, excluding the initial "." character.
            </summary>
        </member>
        <member name="P:NiceIO.NPath.UNCServerName">
            <summary>
            UNC server name of the path, if present. Null if not present.
            </summary>
        </member>
        <member name="P:NiceIO.NPath.DriveLetter">
            <summary>
            The Windows drive letter of the path, if present. Null if not present.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.InQuotes(NiceIO.SlashMode)">
            <summary>
            Provides a quoted version of the path as a string, with the requested path separator type.
            </summary>
            <param name="slashMode">The path separator to use. See the <see cref="T:NiceIO.SlashMode">SlashMode</see> enum for an explanation of the values. Defaults to <c>SlashMode.Forward</c>.</param>
            <returns>The path, with the requested path separator type, in quotes.</returns>
        </member>
        <member name="M:NiceIO.NPath.ToString">
            <summary>
            Convert this path to a string, using forward slashes as path separators.
            </summary>
            <returns>The string representation of this path.</returns>
        </member>
        <member name="M:NiceIO.NPath.ToString(NiceIO.SlashMode)">
            <summary>
            Convert this path to a string, using the requested path separator type.
            </summary>
            <param name="slashMode">The path separator type to use. See <see cref="T:NiceIO.SlashMode">SlashMode</see> for possible values.</param>
            <returns>The string representation of this path.</returns>
        </member>
        <member name="M:NiceIO.NPath.Equals(System.Object)">
            <summary>
            Checks if this NPath represents the same path as another object.
            </summary>
            <param name="obj">The object to compare to.</param>
            <returns>True if this NPath represents the same path as the other object; false if it does not, if the other object is not an NPath, or is null.</returns>
        </member>
        <member name="M:NiceIO.NPath.Equals(NiceIO.NPath)">
            <summary>
            Checks if this NPath is equal to another NPath.
            </summary>
            <param name="p">The path to compare to.</param>
            <returns>True if this NPath represents the same path as the other NPath; false otherwise.</returns>
            <remarks>Note that the comparison requires that the paths are the same, not just that the targets are the same; "foo/bar" and "foo/baz/../bar" refer to the same target but will not be treated as equal by this comparison. However, this comparison will ignore case differences when the current operating system does not use case-sensitive filesystems.</remarks>
        </member>
        <member name="M:NiceIO.NPath.op_Equality(NiceIO.NPath,NiceIO.NPath)">
            <summary>
            Compare two NPaths for equality.
            </summary>
            <param name="a">The first NPath to compare.</param>
            <param name="b">The second NPath to compare.</param>
            <returns>True if the NPaths are both equal (or both null), false otherwise. See <see cref="M:NiceIO.NPath.Equals(NiceIO.NPath)">Equals.</see></returns>
        </member>
        <member name="M:NiceIO.NPath.GetHashCode">
            <summary>
            Get an appropriate hash value for this NPath.
            </summary>
            <returns>A hash value for this NPath.</returns>
        </member>
        <member name="M:NiceIO.NPath.CompareTo(System.Object)">
            <summary>
            Compare this NPath to another NPath, returning a value that can be used to sort the two objects in a stable order.
            </summary>
            <param name="obj">The object to compare to. Note that this object must be castable to NPath.</param>
            <returns>A value that indicates the relative order of the two objects. The return value has these meanings:
            <list type="table">
            <listheader><term>Value</term><description>Meaning</description></listheader>
            <item><term>Less than zero</term><description>This instance precedes <c>obj</c> in the sort order.</description></item>
            <item><term>Zero</term><description>This instance occurs in the same position as <c>obj</c> in the sort order.</description></item>
            <item><term>Greater than zero</term><description>This instance follows <c>obj</c> in the sort order.</description></item>
            </list>
            </returns>
        </member>
        <member name="M:NiceIO.NPath.op_Inequality(NiceIO.NPath,NiceIO.NPath)">
            <summary>
            Compare two NPaths for inequality.
            </summary>
            <param name="a">The first NPath to compare.</param>
            <param name="b">The second NPath to compare.</param>
            <returns>True if the NPaths are not equal, false otherwise.</returns>
        </member>
        <member name="M:NiceIO.NPath.HasExtension(System.String)">
            <summary>
            Tests whether this NPath has the provided extension.
            </summary>
            <param name="extension">The extension to test for.</param>
            <returns>True if this NPath has has the provided extension. False otherwise.</returns>
            <remarks>The extension "*" is special, and will return true for all paths if specified.</remarks>
        </member>
        <member name="M:NiceIO.NPath.HasExtension(System.String[])">
            <summary>
            Tests whether this NPath has one of the provided extensions, or if no extensions are provided, whether it has any extension at all.
            </summary>
            <param name="extensions">The possible extensions to test for.</param>
            <returns>True if this NPath has one of the provided extensions; or, if no extensions are provided, true if this NPath has an extension. False otherwise.</returns>
            <remarks>The extension "*" is special, and will return true for all paths if specified.</remarks>
        </member>
        <member name="P:NiceIO.NPath.IsRoot">
            <summary>
            Whether this path is rooted or not (begins with a slash character or drive specifier).
            </summary>
        </member>
        <member name="P:NiceIO.NPath.IsUNC">
            <summary>
            Whether this path starts with an UNC path prefix "\\" or not.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.Files(System.String,System.Boolean)">
            <summary>
            Find all files within this path that match the given filter.
            </summary>
            <param name="filter">The filter to match against the names of files. Wildcards can be included.</param>
            <param name="recurse">If true, search recursively inside subdirectories of this path; if false, search only for files that are immediate children of this path. Defaults to false.</param>
            <returns>An array of files that were found.</returns>
        </member>
        <member name="M:NiceIO.NPath.Files(System.Boolean)">
            <summary>
            Find all files within this path.
            </summary>
            <param name="recurse">If true, search recursively inside subdirectories of this path; if false, search only for files that are immediate children of this path. Defaults to false.</param>
            <returns>An array of files that were found.</returns>
        </member>
        <member name="M:NiceIO.NPath.Files(System.String[],System.Boolean)">
            <summary>
            Find all files within this path that have one of the provided extensions.
            </summary>
            <param name="extensions">The extensions to search for.</param>
            <param name="recurse">If true, search recursively inside subdirectories of this path; if false, search only for files that are immediate children of this path. Defaults to false.</param>
            <returns>An array of files that were found.</returns>
        </member>
        <member name="M:NiceIO.NPath.Contents(System.String,System.Boolean)">
            <summary>
            Find all files or directories within this path that match the given filter.
            </summary>
            <param name="filter">The filter to match against the names of files and directories. Wildcards can be included.</param>
            <param name="recurse">If true, search recursively inside subdirectories of this path; if false, search only for files and directories that are immediate children of this path. Defaults to false.</param>
            <returns>An array of files and directories that were found.</returns>
        </member>
        <member name="M:NiceIO.NPath.Contents(System.Boolean)">
            <summary>
            Find all files and directories within this path.
            </summary>
            <param name="recurse">If true, search recursively inside subdirectories of this path; if false, search only for files and directories that are immediate children of this path. Defaults to false.</param>
            <returns>An array of files and directories that were found.</returns>
        </member>
        <member name="M:NiceIO.NPath.Directories(System.String,System.Boolean)">
            <summary>
            Find all directories within this path that match the given filter.
            </summary>
            <param name="filter">The filter to match against the names of directories. Wildcards can be included.</param>
            <param name="recurse">If true, search recursively inside subdirectories of this path; if false, search only for directories that are immediate children of this path. Defaults to false.</param>
            <returns>An array of directories that were found.</returns>
        </member>
        <member name="M:NiceIO.NPath.Directories(System.Boolean)">
            <summary>
            Find all directories within this path.
            </summary>
            <param name="recurse">If true, search recursively inside subdirectories of this path; if false, search only for directories that are immediate children of this path. Defaults to false.</param>
            <returns>An array of directories that were found.</returns>
        </member>
        <member name="M:NiceIO.NPath.CreateFile">
            <summary>
            Create an empty file at this path.
            </summary>
            <returns>This NPath, for chaining further operations.</returns>
            <remarks>If a file already exists at this path, it will be overwritten.</remarks>
        </member>
        <member name="M:NiceIO.NPath.CreateFile(NiceIO.NPath)">
            <summary>
            Append the given path fragment to this path, and create an empty file there.
            </summary>
            <param name="file">The path fragment to append.</param>
            <returns>The path to the created file, for chaining further operations.</returns>
            <remarks>If a file already exists at that path, it will be overwritten.</remarks>
        </member>
        <member name="M:NiceIO.NPath.CreateDirectory">
            <summary>
            Create this path as a directory if it does not already exist.
            </summary>
            <returns>This NPath, for chaining further operations.</returns>
            <remark>This is identical to <see cref="M:NiceIO.NPath.EnsureDirectoryExists(NiceIO.NPath)"/>, except that EnsureDirectoryExists triggers "Stat" callbacks and this doesn't.</remark>
        </member>
        <member name="M:NiceIO.NPath.CreateDirectory(NiceIO.NPath)">
            <summary>
            Append the given path fragment to this path, and create it as a directory if it does not already exist.
            </summary>
            <param name="directory">The path fragment to append.</param>
            <returns>The path to the created directory, for chaining further operations.</returns>
        </member>
        <member name="M:NiceIO.NPath.CreateSymbolicLink(NiceIO.NPath,System.Boolean)">
            <summary>
            Create this path as a symbolic link to another file or directory.
            </summary>
            <param name="targetPath">The path that this path should be a symbolic link to. Can be relative or absolute.</param>
            <param name="targetIsFile">Specifies whether this link is to a file or to a directory (required on Windows). Defaults to file.</param>
            <returns>The path to the created symbolic link, for chaining further operations.</returns>
        </member>
        <member name="P:NiceIO.NPath.IsSymbolicLink">
            <summary>
            Checks whether the entity referred to by this path is a symbolic link.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.Copy(NiceIO.NPath)">
            <summary>
            Copy this NPath to the given destination.
            </summary>
            <param name="dest">The path to copy to.</param>
            <returns>The path to the copied result, for chaining further operations.</returns>
        </member>
        <member name="M:NiceIO.NPath.Copy(NiceIO.NPath,System.Func{NiceIO.NPath,System.Boolean})">
            <summary>
            Copy this NPath to the given destination, applying a filter function to decide which files are copied.
            </summary>
            <param name="dest">The path to copy to.</param>
            <param name="fileFilter">The filter function. Each candidate file is passed to this function; if the function returns true, the file will be copied, otherwise it will not.</param>
            <returns></returns>
        </member>
        <member name="M:NiceIO.NPath.MakeAbsolute(NiceIO.NPath)">
            <summary>
            Create a new NPath by converting this path into an absolute representation.
            </summary>
            <param name="base">Optional base to use as a root for relative paths.</param>
            <returns></returns>
        </member>
        <member name="M:NiceIO.NPath.Delete(NiceIO.DeleteMode)">
            <summary>
            Deletes the file or directory referred to by the NPath.
            </summary>
            <param name="deleteMode">The deletion mode to use, see <see cref="T:NiceIO.DeleteMode">DeleteMode.</see> Defaults to DeleteMode.Normal.</param>
            <exception cref="T:System.InvalidOperationException">The path does not exist. See also <see cref="M:NiceIO.NPath.DeleteIfExists(NiceIO.DeleteMode)">DeleteIfExists</see>.</exception>
        </member>
        <member name="M:NiceIO.NPath.DeleteIfExists(NiceIO.DeleteMode)">
            <summary>
            Deletes the file or directory referred to by the NPath, if it exists.
            </summary>
            <param name="deleteMode">The deletion mode to use, see <see cref="T:NiceIO.DeleteMode">DeleteMode.</see> Defaults to DeleteMode.Normal.</param>
            <returns>This NPath, for chaining further operations.</returns>
        </member>
        <member name="M:NiceIO.NPath.DeleteContents">
            <summary>
            Deletes all files and directories inside the directory referred to by this NPath.
            </summary>
            <returns>This NPath, for chaining further operations.</returns>
            <exception cref="T:System.InvalidOperationException">This NPath refers to a file, rather than a directory.</exception>
        </member>
        <member name="M:NiceIO.NPath.CreateTempDirectory(System.String)">
            <summary>
            Create a temporary directory in the system temporary location and return the NPath of it.
            </summary>
            <param name="prefix">A prefix to use for the name of the temporary directory.</param>
            <returns>A new NPath which targets the newly created temporary directory.</returns>
        </member>
        <member name="M:NiceIO.NPath.Move(NiceIO.NPath)">
            <summary>
            Move the file or directory targetted by this NPath to a new location.
            </summary>
            <param name="dest">The destination for the move.</param>
            <returns>An NPath representing the newly moved file or directory.</returns>
        </member>
        <member name="P:NiceIO.NPath.CurrentDirectory">
            <summary>
            The current directory in use by the process.
            </summary>
            <remarks>Note that every read from this property will result in an operating system query, unless <see cref="M:NiceIO.NPath.WithFrozenCurrentDirectory(NiceIO.NPath)">WithFrozenCurrentDirectory</see> is used.</remarks>
        </member>
        <member name="M:NiceIO.NPath.SetCurrentDirectory(NiceIO.NPath)">
            <summary>
            Temporarily change the current directory for the process.
            </summary>
            <param name="directory">The new directory to set as the current directory.</param>
            <returns>A token representing the change in current directory. When this is disposed, the current directory will be returned to its previous value. The usual usage pattern is to capture the token with a <c>using</c> statement, such that it is automatically disposed of when the <c>using</c> block exits.</returns>
        </member>
        <member name="P:NiceIO.NPath.HomeDirectory">
            <summary>
            The current user's home directory.
            </summary>
        </member>
        <member name="P:NiceIO.NPath.SystemTemp">
            <summary>
            The system temporary directory.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.EnsureDirectoryExists(NiceIO.NPath)">
            <summary>
            Append an optional path fragment to this NPath, then create it as a directory if it does not already exist.
            </summary>
            <param name="append">The path fragment to append.</param>
            <returns>The path to the directory that is now guaranteed to exist.</returns>
            <remark>This is identical to <see cref="M:NiceIO.NPath.CreateDirectory"/>, except that this triggers "Stat" callbacks and CreateDirectory doesn't.</remark>
        </member>
        <member name="M:NiceIO.NPath.EnsureParentDirectoryExists">
            <summary>
            Create the parent directory of this NPath if it does not already exist.
            </summary>
            <returns>This NPath, for chaining further operations.</returns>
        </member>
        <member name="M:NiceIO.NPath.FileMustExist">
            <summary>
            Throw an exception if this path does not exist as a file.
            </summary>
            <returns>This path, in order to chain further operations.</returns>
            <exception cref="T:System.IO.FileNotFoundException">The path does not exist, or is not a file.</exception>
        </member>
        <member name="M:NiceIO.NPath.DirectoryMustExist">
            <summary>
            Throw an exception if this directory does not exist.
            </summary>
            <returns>This path, in order to chain further operations.</returns>
            <exception cref="T:System.IO.FileNotFoundException">The path does not exist, or is not a directory.</exception>
        </member>
        <member name="M:NiceIO.NPath.IsChildOf(NiceIO.NPath)">
            <summary>
            Check if this path is a child of the given path hierarchy root (i.e. is a file or directory that is inside the given hierachy root directory or one of its descendent directories).
            </summary>
            <param name="potentialBasePath">The path hierarchy root to check.</param>
            <returns>True if this path is a child of the given root path, false otherwise.</returns>
        </member>
        <member name="M:NiceIO.NPath.IsSameAsOrChildOf(NiceIO.NPath)">
            <summary>
            Check if this path is a child of the given path hierarchy root (i.e. is a file or directory that is inside the given hierachy root directory or one of its descendent directories), or is equal to it.
            </summary>
            <param name="potentialBasePath">The path hierarchy root to check.</param>
            <returns>True if this path is equal to or is a child of the given root path, false otherwise.</returns>
        </member>
        <member name="P:NiceIO.NPath.RecursiveParents">
            <summary>
            Return each parent directory of this path, starting with the immediate parent, then that directory's parent, and so on, until the root of the path is reached.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.ParentContaining(NiceIO.NPath)">
            <summary>
            Search all parent directories of this path for one that contains a file or directory with the given name.
            </summary>
            <param name="needle">The name of the file or directory to search for.</param>
            <returns>The path to the parent directory that contains the file or directory, or null if none of the parents contained a file or directory with the requested name.</returns>
        </member>
        <member name="M:NiceIO.NPath.WriteAllText(System.String)">
            <summary>
            Open this path as a text file, write the given string to it, then close the file.
            </summary>
            <param name="contents">The string to write to the text file.</param>
            <returns>The path to this file, for use in chaining further operations.</returns>
        </member>
        <member name="M:NiceIO.NPath.ReplaceAllText(System.String)">
            <summary>
            Open this file as a text file, and replace the contents with the provided string, if they do not already match. Then close the file.
            </summary>
            <param name="contents">The string to replace the file's contents with.</param>
            <returns>The path to this file, for use in chaining further operations.</returns>
            <remarks>Note that if the contents of the file already match the provided string, the file is not modified - this includes not modifying the file's "last written" timestamp.</remarks>
        </member>
        <member name="M:NiceIO.NPath.WriteAllBytes(System.Byte[])">
            <summary>
            Open this path as a file, write the given bytes to it, then close the file.
            </summary>
            <param name="bytes">The bytes to write to the file.</param>
            <returns>The path to this file, for use in chaining further operations.</returns>
        </member>
        <member name="M:NiceIO.NPath.ReadAllText">
            <summary>
            Opens a text file, reads all the text in the file into a single string, then closes the file.
            </summary>
            <returns>The contents of the text file, as a single string.</returns>
        </member>
        <member name="M:NiceIO.NPath.ReadAllBytes">
            <summary>
            Opens a file, reads all the bytes in the file, then closes the file.
            </summary>
            <returns>The contents of the file, as a bytes array.</returns>
        </member>
        <member name="M:NiceIO.NPath.WriteAllLines(System.String[])">
            <summary>
            Opens a text file, writes all entries of a string array as separate lines into the file, then closes the file.
            </summary>
            <param name="contents">The entries to write into the file as separate lines.</param>
            <returns>The path to this file.</returns>
        </member>
        <member name="M:NiceIO.NPath.ReadAllLines">
            <summary>
            Opens a text file, reads all lines of the file into a string array, and then closes the file.
            </summary>
            <returns>A string array containing all lines of the file.</returns>
        </member>
        <member name="M:NiceIO.NPath.CopyFiles(NiceIO.NPath,System.Boolean,System.Func{NiceIO.NPath,System.Boolean})">
            <summary>
            Copy all files in this NPath to the given destination directory.
            </summary>
            <param name="destination">The directory to copy the files to.</param>
            <param name="recurse">If true, files inside subdirectories of this NPath will also be copied. If false, only immediate child files of this NPath will be copied.</param>
            <param name="fileFilter">An optional predicate function that can be used to filter files. It is passed each source file path in turn, and if it returns true, the file is copied; otherwise, the file is not copied.</param>
            <returns>The paths to all the newly copied files.</returns>
            <remarks>Note that the directory structure of the files relative to this NPath will be preserved within the target directory.</remarks>
        </member>
        <member name="M:NiceIO.NPath.MoveFiles(NiceIO.NPath,System.Boolean,System.Func{NiceIO.NPath,System.Boolean})">
            <summary>
            Move all files in this NPath to the given destination directory.
            </summary>
            <param name="destination">The directory to move the files to.</param>
            <param name="recurse">If true, files inside subdirectories of this NPath will also be moved. If false, only immediate child files of this NPath will be moved.</param>
            <param name="fileFilter">An optional predicate function that can be used to filter files. It is passed each source file path in turn, and if it returns true, the file is moved; otherwise, the file is not moved.</param>
            <returns>The paths to all the newly moved files.</returns>
            <remarks>Note that the directory structure of the files relative to this NPath will be preserved within the target directory.</remarks>
        </member>
        <member name="M:NiceIO.NPath.op_Implicit(System.String)~NiceIO.NPath">
            <summary>
            Implicitly construct a new NPath from a string.
            </summary>
            <param name="input">The string to construct the new NPath from.</param>
        </member>
        <member name="M:NiceIO.NPath.SetLastWriteTimeUtc(System.DateTime)">
            <summary>
            Set the last time the file was written to, in UTC.
            </summary>
            <returns>The last time the file was written to, in UTC.</returns>
            <remarks>This is set automatically by the OS when the file is modified, but it can sometimes be useful
            to explicitly update the timestamp without modifying the file contents.</remarks>
        </member>
        <member name="M:NiceIO.NPath.GetLastWriteTimeUtc">
            <summary>
            Get the last time the file was written to, in UTC.
            </summary>
            <returns>The last time the file was written to, in UTC.</returns>
        </member>
        <member name="M:NiceIO.NPath.GetFileSize">
            <summary>
            Get the file length in bytes.
            </summary>
            <returns>The file length in bytes.</returns>
        </member>
        <member name="P:NiceIO.NPath.Attributes">
            <summary>
            The filesystem attributes of the given file, assuming it exists. Note that when you set this property, the
            OS may still modify the the actual attributes of the file beyond what you requested, so setting and then
            getting the property is not guaranteed to roundtrip the value. Note also that some attributes (e.g.
            FileAttributes.Hidden) are not supported on every OS, and may throw exceptions or simply be ignored.
            </summary>
        </member>
        <member name="M:NiceIO.NPath.WithFileSystem(NiceIO.NPath.FileSystem)">
            <summary>
            Until .Dispose is invoked on the returnvalue, makes all NPath's on this thread use the provided filesystem implementation for all filesystem access.
            </summary>
            <param name="fileSystem"></param>
            <returns>An object you can invoke .Dispose() on, which will make all NPath filesystem operations stop using the provided filesystem</returns>
        </member>
        <member name="M:NiceIO.NPath.ResolveWithFileSystem">
            <summary>
            Lets the currently active filesystem resolve the path.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NiceIO.NPath.InQuotesResolved(NiceIO.SlashMode)">
            <summary>
            Shorthand for .ResolveWithFileSystem.InQuotes()
            </summary>
            <param name="slashMode"></param>
            <returns></returns>
        </member>
        <member name="T:NiceIO.NPath.FileSystem">
            <summary>
            Abstract baseclass you can use to plug in different underlying filesystem behaviour for NPath to operate on
            </summary>
        </member>
        <member name="P:NiceIO.NPath.FileSystem.Active">
            <summary>
            The currently active filesystem for NPath operations. Set this using NPath.WithFileSystem()
            </summary>
        </member>
        <member name="M:NiceIO.NPath.FileSystem.Dispose">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.FileSystem.Resolve(NiceIO.NPath)">
            <summary>
            If your filesystem does any kind of redirection or other magic, Resolve() is required to return the path that can be used against the raw lowlevel filesystem of the OS.
            </summary>
            <param name="path">The path to resolve</param>
            <returns>The resolved path that is valid to use against the OS's real filesystem</returns>
        </member>
        <member name="T:NiceIO.NPath.RelayingFileSystem">
            <summary>
            A Filesystem that forwards all calls to another filesytem. Derive your own filesystem from this if you only want to
            change the behaviour of a few methods.
            </summary>
        </member>
        <member name="P:NiceIO.NPath.RelayingFileSystem.BaseFileSystem">
            <summary>
            The filesystem all methods will be forwarded to
            </summary>
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.#ctor(NiceIO.NPath.FileSystem)">
            <summary>
            Constructor
            </summary>
            <param name="baseFileSystem">the filesystem all calls will be forwarded to</param>
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_GetFiles(NiceIO.NPath,System.String,System.IO.SearchOption)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_Exists(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_Exists(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_WriteAllBytes(NiceIO.NPath,System.Byte[])">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_Copy(NiceIO.NPath,NiceIO.NPath,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_Delete(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_Move(NiceIO.NPath,NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_WriteAllText(NiceIO.NPath,System.String)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_ReadAllText(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_WriteAllLines(NiceIO.NPath,System.String[])">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_ReadAllLines(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_ReadAllBytes(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_SetLastWriteTimeUtc(NiceIO.NPath,System.DateTime)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_GetLastWriteTimeUtc(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_SetAttributes(NiceIO.NPath,System.IO.FileAttributes)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_GetAttributes(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.File_GetSize(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_CreateDirectory(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_Delete(NiceIO.NPath,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_Move(NiceIO.NPath,NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_GetCurrentDirectory">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_SetCurrentDirectory(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Directory_GetDirectories(NiceIO.NPath,System.String,System.IO.SearchOption)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.Resolve(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.IsSymbolicLink(NiceIO.NPath)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.RelayingFileSystem.CreateSymbolicLink(NiceIO.NPath,NiceIO.NPath,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:NiceIO.NPath.WithFrozenCurrentDirectory(NiceIO.NPath)">
            <summary>
            Temporarily assume that the current directory is a given value, instead of querying it from the environment when needed, in order to improve performance.
            </summary>
            <param name="frozenCurrentDirectory">The current directory to assume.</param>
            <returns>A token representing the registered callback. This should be disposed of when the assumption is no longer required. The usual usage pattern is to capture the token with a <c>using</c> statement, such that it is automatically disposed of when the <c>using</c> block exits.</returns>
        </member>
        <member name="T:NiceIO.Extensions">
            <summary>
            NPath-related extension methods for other common types.
            </summary>
        </member>
        <member name="M:NiceIO.Extensions.Copy(System.Collections.Generic.IEnumerable{NiceIO.NPath},NiceIO.NPath)">
            <summary>
            Copy these NPaths into the given directory.
            </summary>
            <param name="self">An enumerable sequence of NPaths.</param>
            <param name="dest">The path to the target directory.</param>
            <returns>The paths to the newly copied files.</returns>
            <remarks>All path information in the source paths is ignored, other than the final file name; the resulting copied files and directories will all be immediate children of the target directory.</remarks>
        </member>
        <member name="M:NiceIO.Extensions.Move(System.Collections.Generic.IEnumerable{NiceIO.NPath},NiceIO.NPath)">
            <summary>
            Move these NPaths into the given directory.
            </summary>
            <param name="self">An enumerable sequence of NPaths.</param>
            <param name="dest">The path to the target directory.</param>
            <returns>The paths to the newly moved files.</returns>
            <remarks>All path information in the source paths is ignored, other than the final file name; the resulting moved files and directories will all be immediate children of the target directory.</remarks>
        </member>
        <member name="M:NiceIO.Extensions.Delete(System.Collections.Generic.IEnumerable{NiceIO.NPath})">
            <summary>
            Delete the files/directories targetted by these paths.
            </summary>
            <param name="self">The paths to delete.</param>
            <returns>All paths that were passed in to the method.</returns>
        </member>
        <member name="M:NiceIO.Extensions.InQuotes(System.Collections.Generic.IEnumerable{NiceIO.NPath},NiceIO.SlashMode)">
            <summary>
            Convert all these paths to quoted strings, using the requested path separator type.
            </summary>
            <param name="self">The paths to convert.</param>
            <param name="slashMode">The path separator type to use. Defaults to <c>SlashMode.Forward</c>.</param>
            <returns>The paths, converted to quoted strings.</returns>
        </member>
        <member name="M:NiceIO.Extensions.ToNPath(System.String)">
            <summary>
            Construct a new NPath from this string.
            </summary>
            <param name="path">The string to construct the path from.</param>
            <returns>A new NPath constructed from this string.</returns>
        </member>
        <member name="M:NiceIO.Extensions.ToNPaths(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Construct new NPaths from each of these strings.
            </summary>
            <param name="paths">The strings to construct NPaths from.</param>
            <returns>The newly constructed NPaths.</returns>
        </member>
        <member name="M:NiceIO.Extensions.ResolveWithFileSystem(System.Collections.Generic.IEnumerable{NiceIO.NPath})">
            <summary>
            Invokes .ResolveWithFileSystem on all NPaths
            </summary>
            <param name="paths"></param>
            <returns></returns>
        </member>
        <member name="M:NiceIO.Extensions.InQuotesResolved(System.Collections.Generic.IEnumerable{NiceIO.NPath})">
            <summary>
            Invokes InQuotesResolved on all NPaths
            </summary>
            <param name="paths"></param>
            <returns></returns>
        </member>
        <member name="T:NiceIO.SlashMode">
            <summary>
            Describes the different kinds of path separators that can be used when converting NPaths back into strings.
            </summary>
        </member>
        <member name="F:NiceIO.SlashMode.Native">
            <summary>
            Use the slash mode that is native for the current platform - backslashes on Windows, forward slashes on macOS and Linux systems.
            </summary>
        </member>
        <member name="F:NiceIO.SlashMode.Forward">
            <summary>
            Use forward slashes as path separators.
            </summary>
        </member>
        <member name="F:NiceIO.SlashMode.Backward">
            <summary>
            Use backslashes as path separators.
            </summary>
        </member>
        <member name="T:NiceIO.DeleteMode">
            <summary>
            Specifies the way that directory deletion should be performed.
            </summary>
        </member>
        <member name="F:NiceIO.DeleteMode.Normal">
            <summary>
            When deleting a directory, if an IOException occurs, rethrow it.
            </summary>
        </member>
        <member name="F:NiceIO.DeleteMode.Soft">
            <summary>
            When deleting a directory, if an IOException occurs, ignore it. The deletion request may or may not be later fulfilled by the OS.
            </summary>
        </member>
    </members>
</doc>
