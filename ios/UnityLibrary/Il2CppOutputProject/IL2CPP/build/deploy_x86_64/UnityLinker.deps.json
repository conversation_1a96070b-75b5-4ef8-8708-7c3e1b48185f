{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/osx-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/osx-x64": {"UnityLinker/1.0.0": {"dependencies": {"Analytics.Api.Output": "1.0.0", "Microsoft.Win32.Registry": "6.0.0-preview.5.21301.5", "Unity.Cecil.Awesome": "1.0.0", "Unity.IL2CPP.Common": "1.0.0", "Unity.IL2CPP.Common35": "1.0.0", "Unity.IL2CPP.Shell": "1.0.0", "Unity.Linker.Api": "1.0.0", "Unity.Linker.Api.Output": "1.0.0", "Unity.Options": "1.0.0", "Unity.TinyProfiler": "1.0.0", "monolinker": "1.0.0", "Bee.TinyProfiler2": "0.0.0.0", "Mono.Cecil": "********", "Mono.Cecil.Mdb": "********", "Mono.Cecil.Pdb": "********", "Mono.Cecil.Rocks": "********", "SharpYaml": "*******", "runtimepack.Microsoft.NETCore.App.Runtime.osx-x64": "6.0.18"}, "runtime": {"UnityLinker.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.osx-x64/6.0.18": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "11.100.1823.26907"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "6.0.1823.26907"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.1823.26907"}}, "native": {"createdump": {"fileVersion": "0.0.0.0"}, "libSystem.Globalization.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.IO.Compression.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Net.Security.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.Apple.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.OpenSsl.dylib": {"fileVersion": "0.0.0.0"}, "libclrjit.dylib": {"fileVersion": "0.0.0.0"}, "libcoreclr.dylib": {"fileVersion": "0.0.0.0"}, "libdbgshim.dylib": {"fileVersion": "0.0.0.0"}, "libhostfxr.dylib": {"fileVersion": "0.0.0.0"}, "libhostpolicy.dylib": {"fileVersion": "0.0.0.0"}, "libmscordaccore.dylib": {"fileVersion": "0.0.0.0"}, "libmscordbi.dylib": {"fileVersion": "0.0.0.0"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Win32.Registry/6.0.0-preview.5.21301.5": {"dependencies": {"System.Security.AccessControl": "6.0.0-preview.5.21301.5", "System.Security.Principal.Windows": "6.0.0-preview.5.21301.5"}}, "System.Security.AccessControl/6.0.0-preview.5.21301.5": {"dependencies": {"System.Security.Principal.Windows": "6.0.0-preview.5.21301.5"}}, "System.Security.Principal.Windows/6.0.0-preview.5.21301.5": {}, "Analytics.Api.Output/1.0.0": {"runtime": {"Analytics.Api.Output.dll": {}}}, "monolinker/1.0.0": {"dependencies": {"Microsoft.Bcl.HashCode": "1.1.0"}, "runtime": {"monolinker.dll": {}}}, "Unity.Api.Attributes/1.0.0": {"runtime": {"Unity.Api.Attributes.dll": {}}}, "Unity.Cecil.Awesome/1.0.0": {"runtime": {"Unity.Cecil.Awesome.dll": {}}}, "Unity.IL2CPP.Common/1.0.0": {"dependencies": {"Unity.Cecil.Awesome": "1.0.0", "Unity.IL2CPP.Common35": "1.0.0"}, "runtime": {"Unity.IL2CPP.Common.dll": {}}}, "Unity.IL2CPP.Common35/1.0.0": {"runtime": {"Unity.IL2CPP.Common35.dll": {}}}, "Unity.IL2CPP.Shell/1.0.0": {"dependencies": {"Unity.IL2CPP.Common35": "1.0.0"}, "runtime": {"Unity.IL2CPP.Shell.dll": {}}}, "Unity.Linker.Api/1.0.0": {"dependencies": {"Unity.Api.Attributes": "1.0.0"}, "runtime": {"Unity.Linker.Api.dll": {}}}, "Unity.Linker.Api.Output/1.0.0": {"dependencies": {"Analytics.Api.Output": "1.0.0", "Unity.Linker.Api": "1.0.0"}, "runtime": {"Unity.Linker.Api.Output.dll": {}}}, "Unity.Options/1.0.0": {"runtime": {"Unity.Options.dll": {}}}, "Unity.TinyProfiler/1.0.0": {"dependencies": {"Unity.IL2CPP.Common35": "1.0.0"}, "runtime": {"Unity.TinyProfiler.dll": {}}}, "Bee.TinyProfiler2/0.0.0.0": {"runtime": {"Bee.TinyProfiler2.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Mono.Cecil/********": {"runtime": {"Mono.Cecil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Mdb/********": {"runtime": {"Mono.Cecil.Mdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Pdb/********": {"runtime": {"Mono.Cecil.Pdb.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.Cecil.Rocks/********": {"runtime": {"Mono.Cecil.Rocks.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpYaml/*******": {"runtime": {"SharpYaml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"UnityLinker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.osx-x64/6.0.18": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/6.0.0-preview.5.21301.5": {"type": "package", "serviceable": true, "sha512": "sha512-qYLtJIAEJJmY2vXxlVO8x4uXfgq7DFOHjpmnHlLm7kmAvyNFckYY/Dx5CZythBXvI2/7sratbIGKqSTysfgZ8A==", "path": "microsoft.win32.registry/6.0.0-preview.5.21301.5", "hashPath": "microsoft.win32.registry.6.0.0-preview.5.21301.5.nupkg.sha512"}, "System.Security.AccessControl/6.0.0-preview.5.21301.5": {"type": "package", "serviceable": true, "sha512": "sha512-EA9ul7nGN8oggMvloILnR+wnrbgLNZZQBYHq5nEq/ixwnKLV3M3Tbd1Jbj8oGck3XMj0owq81e4Jxp3s0IMICw==", "path": "system.security.accesscontrol/6.0.0-preview.5.21301.5", "hashPath": "system.security.accesscontrol.6.0.0-preview.5.21301.5.nupkg.sha512"}, "System.Security.Principal.Windows/6.0.0-preview.5.21301.5": {"type": "package", "serviceable": true, "sha512": "sha512-ywwCqFAaRVbgqqORqYg8jdaX6NUEpzbuhxyUhAs+7mZ8AFAO4PzFYrZ5JPkYejXwougDldtbi0zOkk1lLzugLw==", "path": "system.security.principal.windows/6.0.0-preview.5.21301.5", "hashPath": "system.security.principal.windows.6.0.0-preview.5.21301.5.nupkg.sha512"}, "Analytics.Api.Output/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "monolinker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Api.Attributes/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Cecil.Awesome/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Common35/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.IL2CPP.Shell/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Linker.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Linker.Api.Output/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.Options/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Unity.TinyProfiler/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bee.TinyProfiler2/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Mdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Pdb/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Mono.Cecil.Rocks/********": {"type": "reference", "serviceable": false, "sha512": ""}, "SharpYaml/*******": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"osx-x64": ["osx", "unix-x64", "unix", "any", "base"], "osx.10.10-x64": ["osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.11-x64": ["osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.12-x64": ["osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.13-x64": ["osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.14-x64": ["osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.15-x64": ["osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.16-x64": ["osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.11.0-x64": ["osx.11.0", "osx.10.16-x64", "osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.12-x64": ["osx.12", "osx.11.0-x64", "osx.11.0", "osx.10.16-x64", "osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.13-x64": ["osx.13", "osx.12-x64", "osx.12", "osx.11.0-x64", "osx.11.0", "osx.10.16-x64", "osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"]}}