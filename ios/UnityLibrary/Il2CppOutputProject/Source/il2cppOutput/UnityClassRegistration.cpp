extern "C" void RegisterStaticallyLinkedModulesGranular()
{
	void RegisterModule_SharedInternals();
	RegisterModule_SharedInternals();

	void RegisterModule_Core();
	RegisterModule_Core();

	void RegisterModule_AI();
	RegisterModule_AI();

	void RegisterModule_AR();
	RegisterModule_AR();

	void RegisterModule_AndroidJNI();
	RegisterModule_AndroidJNI();

	void RegisterModule_Animation();
	RegisterModule_Animation();

	void RegisterModule_AssetBundle();
	RegisterModule_AssetBundle();

	void RegisterModule_Audio();
	RegisterModule_Audio();

	void RegisterModule_Cloth();
	RegisterModule_Cloth();

	void RegisterModule_ContentLoad();
	RegisterModule_ContentLoad();

	void RegisterModule_CrashReporting();
	RegisterModule_CrashReporting();

	void RegisterModule_DSPGraph();
	RegisterModule_DSPGraph();

	void RegisterModule_Director();
	RegisterModule_Director();

	void RegisterModule_GameCenter();
	RegisterModule_GameCenter();

	void RegisterModule_Grid();
	RegisterModule_Grid();

	void RegisterModule_HotReload();
	RegisterModule_HotReload();

	void RegisterModule_InputLegacy();
	RegisterModule_InputLegacy();

	void RegisterModule_IMGUI();
	RegisterModule_IMGUI();

	void RegisterModule_ImageConversion();
	RegisterModule_ImageConversion();

	void RegisterModule_JSONSerialize();
	RegisterModule_JSONSerialize();

	void RegisterModule_Input();
	RegisterModule_Input();

	void RegisterModule_Localization();
	RegisterModule_Localization();

	void RegisterModule_ParticleSystem();
	RegisterModule_ParticleSystem();

	void RegisterModule_PerformanceReporting();
	RegisterModule_PerformanceReporting();

	void RegisterModule_Physics();
	RegisterModule_Physics();

	void RegisterModule_Physics2D();
	RegisterModule_Physics2D();

	void RegisterModule_RuntimeInitializeOnLoadManagerInitializer();
	RegisterModule_RuntimeInitializeOnLoadManagerInitializer();

	void RegisterModule_ScreenCapture();
	RegisterModule_ScreenCapture();

	void RegisterModule_SpriteMask();
	RegisterModule_SpriteMask();

	void RegisterModule_SpriteShape();
	RegisterModule_SpriteShape();

	void RegisterModule_Streaming();
	RegisterModule_Streaming();

	void RegisterModule_Substance();
	RegisterModule_Substance();

	void RegisterModule_Subsystems();
	RegisterModule_Subsystems();

	void RegisterModule_TLS();
	RegisterModule_TLS();

	void RegisterModule_Terrain();
	RegisterModule_Terrain();

	void RegisterModule_TerrainPhysics();
	RegisterModule_TerrainPhysics();

	void RegisterModule_TextRendering();
	RegisterModule_TextRendering();

	void RegisterModule_TextCoreFontEngine();
	RegisterModule_TextCoreFontEngine();

	void RegisterModule_TextCoreTextEngine();
	RegisterModule_TextCoreTextEngine();

	void RegisterModule_Tilemap();
	RegisterModule_Tilemap();

	void RegisterModule_UI();
	RegisterModule_UI();

	void RegisterModule_UIElements();
	RegisterModule_UIElements();

	void RegisterModule_UnityAnalyticsCommon();
	RegisterModule_UnityAnalyticsCommon();

	void RegisterModule_UnityConnect();
	RegisterModule_UnityConnect();

	void RegisterModule_UnityWebRequest();
	RegisterModule_UnityWebRequest();

	void RegisterModule_UnityAnalytics();
	RegisterModule_UnityAnalytics();

	void RegisterModule_UnityWebRequestAssetBundle();
	RegisterModule_UnityWebRequestAssetBundle();

	void RegisterModule_UnityWebRequestAudio();
	RegisterModule_UnityWebRequestAudio();

	void RegisterModule_UnityWebRequestTexture();
	RegisterModule_UnityWebRequestTexture();

	void RegisterModule_UnityWebRequestWWW();
	RegisterModule_UnityWebRequestWWW();

	void RegisterModule_VFX();
	RegisterModule_VFX();

	void RegisterModule_VR();
	RegisterModule_VR();

	void RegisterModule_Vehicles();
	RegisterModule_Vehicles();

	void RegisterModule_Video();
	RegisterModule_Video();

	void RegisterModule_Wind();
	RegisterModule_Wind();

	void RegisterModule_XR();
	RegisterModule_XR();

}

template <typename T> void RegisterUnityClass(const char*);
template <typename T> void RegisterStrippedType(int, const char*, const char*);

void InvokeRegisterStaticallyLinkedModuleClasses()
{
	// Do nothing (we're in stripping mode)
}

class NavMeshAgent; template <> void RegisterUnityClass<NavMeshAgent>(const char*);
class NavMeshData; template <> void RegisterUnityClass<NavMeshData>(const char*);
class NavMeshObstacle; template <> void RegisterUnityClass<NavMeshObstacle>(const char*);
class NavMeshProjectSettings; template <> void RegisterUnityClass<NavMeshProjectSettings>(const char*);
class NavMeshSettings; template <> void RegisterUnityClass<NavMeshSettings>(const char*);
class OffMeshLink; template <> void RegisterUnityClass<OffMeshLink>(const char*);
class AimConstraint; template <> void RegisterUnityClass<AimConstraint>(const char*);
class Animation; template <> void RegisterUnityClass<Animation>(const char*);
class AnimationClip; template <> void RegisterUnityClass<AnimationClip>(const char*);
class Animator; template <> void RegisterUnityClass<Animator>(const char*);
class AnimatorController; template <> void RegisterUnityClass<AnimatorController>(const char*);
class AnimatorOverrideController; template <> void RegisterUnityClass<AnimatorOverrideController>(const char*);
class Avatar; template <> void RegisterUnityClass<Avatar>(const char*);
class AvatarMask; template <> void RegisterUnityClass<AvatarMask>(const char*);
class IConstraint; template <> void RegisterUnityClass<IConstraint>(const char*);
class LookAtConstraint; template <> void RegisterUnityClass<LookAtConstraint>(const char*);
class Motion; template <> void RegisterUnityClass<Motion>(const char*);
class ParentConstraint; template <> void RegisterUnityClass<ParentConstraint>(const char*);
class PositionConstraint; template <> void RegisterUnityClass<PositionConstraint>(const char*);
class RotationConstraint; template <> void RegisterUnityClass<RotationConstraint>(const char*);
class RuntimeAnimatorController; template <> void RegisterUnityClass<RuntimeAnimatorController>(const char*);
class ScaleConstraint; template <> void RegisterUnityClass<ScaleConstraint>(const char*);
class AssetBundle; template <> void RegisterUnityClass<AssetBundle>(const char*);
class AssetBundleManifest; template <> void RegisterUnityClass<AssetBundleManifest>(const char*);
class AudioBehaviour; template <> void RegisterUnityClass<AudioBehaviour>(const char*);
class AudioChorusFilter; template <> void RegisterUnityClass<AudioChorusFilter>(const char*);
class AudioClip; template <> void RegisterUnityClass<AudioClip>(const char*);
class AudioDistortionFilter; template <> void RegisterUnityClass<AudioDistortionFilter>(const char*);
class AudioEchoFilter; template <> void RegisterUnityClass<AudioEchoFilter>(const char*);
class AudioFilter; template <> void RegisterUnityClass<AudioFilter>(const char*);
class AudioHighPassFilter; template <> void RegisterUnityClass<AudioHighPassFilter>(const char*);
class AudioListener; template <> void RegisterUnityClass<AudioListener>(const char*);
class AudioLowPassFilter; template <> void RegisterUnityClass<AudioLowPassFilter>(const char*);
class AudioManager; template <> void RegisterUnityClass<AudioManager>(const char*);
class AudioMixer; template <> void RegisterUnityClass<AudioMixer>(const char*);
class AudioMixerGroup; template <> void RegisterUnityClass<AudioMixerGroup>(const char*);
class AudioMixerSnapshot; template <> void RegisterUnityClass<AudioMixerSnapshot>(const char*);
class AudioReverbFilter; template <> void RegisterUnityClass<AudioReverbFilter>(const char*);
class AudioReverbZone; template <> void RegisterUnityClass<AudioReverbZone>(const char*);
class AudioSource; template <> void RegisterUnityClass<AudioSource>(const char*);
class BaseVideoTexture; template <> void RegisterUnityClass<BaseVideoTexture>(const char*);
class SampleClip; template <> void RegisterUnityClass<SampleClip>(const char*);
class WebCamTexture; template <> void RegisterUnityClass<WebCamTexture>(const char*);
namespace Unity { class Cloth; } template <> void RegisterUnityClass<Unity::Cloth>(const char*);
class Behaviour; template <> void RegisterUnityClass<Behaviour>(const char*);
class BillboardAsset; template <> void RegisterUnityClass<BillboardAsset>(const char*);
class BillboardRenderer; template <> void RegisterUnityClass<BillboardRenderer>(const char*);
class BuildSettings; template <> void RegisterUnityClass<BuildSettings>(const char*);
class Camera; template <> void RegisterUnityClass<Camera>(const char*);
namespace Unity { class Component; } template <> void RegisterUnityClass<Unity::Component>(const char*);
class ComputeShader; template <> void RegisterUnityClass<ComputeShader>(const char*);
class Cubemap; template <> void RegisterUnityClass<Cubemap>(const char*);
class CubemapArray; template <> void RegisterUnityClass<CubemapArray>(const char*);
class CustomRenderTexture; template <> void RegisterUnityClass<CustomRenderTexture>(const char*);
class DelayedCallManager; template <> void RegisterUnityClass<DelayedCallManager>(const char*);
class EditorExtension; template <> void RegisterUnityClass<EditorExtension>(const char*);
class Flare; template <> void RegisterUnityClass<Flare>(const char*);
class FlareLayer; template <> void RegisterUnityClass<FlareLayer>(const char*);
class GameManager; template <> void RegisterUnityClass<GameManager>(const char*);
class GameObject; template <> void RegisterUnityClass<GameObject>(const char*);
class GlobalGameManager; template <> void RegisterUnityClass<GlobalGameManager>(const char*);
class GraphicsSettings; template <> void RegisterUnityClass<GraphicsSettings>(const char*);
class Halo; template <> void RegisterUnityClass<Halo>(const char*);
class InputManager; template <> void RegisterUnityClass<InputManager>(const char*);
class LODGroup; template <> void RegisterUnityClass<LODGroup>(const char*);
class LensFlare; template <> void RegisterUnityClass<LensFlare>(const char*);
class LevelGameManager; template <> void RegisterUnityClass<LevelGameManager>(const char*);
class Light; template <> void RegisterUnityClass<Light>(const char*);
class LightProbeGroup; template <> void RegisterUnityClass<LightProbeGroup>(const char*);
class LightProbeProxyVolume; template <> void RegisterUnityClass<LightProbeProxyVolume>(const char*);
class LightProbes; template <> void RegisterUnityClass<LightProbes>(const char*);
class LightingSettings; template <> void RegisterUnityClass<LightingSettings>(const char*);
class LightmapSettings; template <> void RegisterUnityClass<LightmapSettings>(const char*);
class LineRenderer; template <> void RegisterUnityClass<LineRenderer>(const char*);
class LowerResBlitTexture; template <> void RegisterUnityClass<LowerResBlitTexture>(const char*);
class Material; template <> void RegisterUnityClass<Material>(const char*);
class Mesh; template <> void RegisterUnityClass<Mesh>(const char*);
class MeshFilter; template <> void RegisterUnityClass<MeshFilter>(const char*);
class MeshRenderer; template <> void RegisterUnityClass<MeshRenderer>(const char*);
class MonoBehaviour; template <> void RegisterUnityClass<MonoBehaviour>(const char*);
class MonoManager; template <> void RegisterUnityClass<MonoManager>(const char*);
class MonoScript; template <> void RegisterUnityClass<MonoScript>(const char*);
class NamedObject; template <> void RegisterUnityClass<NamedObject>(const char*);
class Object; template <> void RegisterUnityClass<Object>(const char*);
class OcclusionArea; template <> void RegisterUnityClass<OcclusionArea>(const char*);
class OcclusionPortal; template <> void RegisterUnityClass<OcclusionPortal>(const char*);
class PlayerSettings; template <> void RegisterUnityClass<PlayerSettings>(const char*);
class PreloadData; template <> void RegisterUnityClass<PreloadData>(const char*);
class Projector; template <> void RegisterUnityClass<Projector>(const char*);
class QualitySettings; template <> void RegisterUnityClass<QualitySettings>(const char*);
class RayTracingShader; template <> void RegisterUnityClass<RayTracingShader>(const char*);
namespace UI { class RectTransform; } template <> void RegisterUnityClass<UI::RectTransform>(const char*);
class ReflectionProbe; template <> void RegisterUnityClass<ReflectionProbe>(const char*);
class RenderSettings; template <> void RegisterUnityClass<RenderSettings>(const char*);
class RenderTexture; template <> void RegisterUnityClass<RenderTexture>(const char*);
class Renderer; template <> void RegisterUnityClass<Renderer>(const char*);
class ResourceManager; template <> void RegisterUnityClass<ResourceManager>(const char*);
class RuntimeInitializeOnLoadManager; template <> void RegisterUnityClass<RuntimeInitializeOnLoadManager>(const char*);
class Shader; template <> void RegisterUnityClass<Shader>(const char*);
class ShaderNameRegistry; template <> void RegisterUnityClass<ShaderNameRegistry>(const char*);
class ShaderVariantCollection; template <> void RegisterUnityClass<ShaderVariantCollection>(const char*);
class SkinnedMeshRenderer; template <> void RegisterUnityClass<SkinnedMeshRenderer>(const char*);
class Skybox; template <> void RegisterUnityClass<Skybox>(const char*);
class SortingGroup; template <> void RegisterUnityClass<SortingGroup>(const char*);
class SparseTexture; template <> void RegisterUnityClass<SparseTexture>(const char*);
class Sprite; template <> void RegisterUnityClass<Sprite>(const char*);
class SpriteAtlas; template <> void RegisterUnityClass<SpriteAtlas>(const char*);
class SpriteRenderer; template <> void RegisterUnityClass<SpriteRenderer>(const char*);
class TagManager; template <> void RegisterUnityClass<TagManager>(const char*);
class TextAsset; template <> void RegisterUnityClass<TextAsset>(const char*);
class Texture; template <> void RegisterUnityClass<Texture>(const char*);
class Texture2D; template <> void RegisterUnityClass<Texture2D>(const char*);
class Texture2DArray; template <> void RegisterUnityClass<Texture2DArray>(const char*);
class Texture3D; template <> void RegisterUnityClass<Texture3D>(const char*);
class TimeManager; template <> void RegisterUnityClass<TimeManager>(const char*);
class TrailRenderer; template <> void RegisterUnityClass<TrailRenderer>(const char*);
class Transform; template <> void RegisterUnityClass<Transform>(const char*);
class PlayableDirector; template <> void RegisterUnityClass<PlayableDirector>(const char*);
class Grid; template <> void RegisterUnityClass<Grid>(const char*);
class GridLayout; template <> void RegisterUnityClass<GridLayout>(const char*);
class LocalizationAsset; template <> void RegisterUnityClass<LocalizationAsset>(const char*);
class ParticleSystem; template <> void RegisterUnityClass<ParticleSystem>(const char*);
class ParticleSystemForceField; template <> void RegisterUnityClass<ParticleSystemForceField>(const char*);
class ParticleSystemRenderer; template <> void RegisterUnityClass<ParticleSystemRenderer>(const char*);
namespace Unity { class ArticulationBody; } template <> void RegisterUnityClass<Unity::ArticulationBody>(const char*);
class BoxCollider; template <> void RegisterUnityClass<BoxCollider>(const char*);
class CapsuleCollider; template <> void RegisterUnityClass<CapsuleCollider>(const char*);
class CharacterController; template <> void RegisterUnityClass<CharacterController>(const char*);
namespace Unity { class CharacterJoint; } template <> void RegisterUnityClass<Unity::CharacterJoint>(const char*);
class Collider; template <> void RegisterUnityClass<Collider>(const char*);
namespace Unity { class ConfigurableJoint; } template <> void RegisterUnityClass<Unity::ConfigurableJoint>(const char*);
class ConstantForce; template <> void RegisterUnityClass<ConstantForce>(const char*);
namespace Unity { class FixedJoint; } template <> void RegisterUnityClass<Unity::FixedJoint>(const char*);
namespace Unity { class HingeJoint; } template <> void RegisterUnityClass<Unity::HingeJoint>(const char*);
namespace Unity { class Joint; } template <> void RegisterUnityClass<Unity::Joint>(const char*);
class MeshCollider; template <> void RegisterUnityClass<MeshCollider>(const char*);
class PhysicMaterial; template <> void RegisterUnityClass<PhysicMaterial>(const char*);
class PhysicsManager; template <> void RegisterUnityClass<PhysicsManager>(const char*);
class Rigidbody; template <> void RegisterUnityClass<Rigidbody>(const char*);
class SphereCollider; template <> void RegisterUnityClass<SphereCollider>(const char*);
namespace Unity { class SpringJoint; } template <> void RegisterUnityClass<Unity::SpringJoint>(const char*);
class AnchoredJoint2D; template <> void RegisterUnityClass<AnchoredJoint2D>(const char*);
class AreaEffector2D; template <> void RegisterUnityClass<AreaEffector2D>(const char*);
class BoxCollider2D; template <> void RegisterUnityClass<BoxCollider2D>(const char*);
class BuoyancyEffector2D; template <> void RegisterUnityClass<BuoyancyEffector2D>(const char*);
class CapsuleCollider2D; template <> void RegisterUnityClass<CapsuleCollider2D>(const char*);
class CircleCollider2D; template <> void RegisterUnityClass<CircleCollider2D>(const char*);
class Collider2D; template <> void RegisterUnityClass<Collider2D>(const char*);
class CompositeCollider2D; template <> void RegisterUnityClass<CompositeCollider2D>(const char*);
class ConstantForce2D; template <> void RegisterUnityClass<ConstantForce2D>(const char*);
class CustomCollider2D; template <> void RegisterUnityClass<CustomCollider2D>(const char*);
class DistanceJoint2D; template <> void RegisterUnityClass<DistanceJoint2D>(const char*);
class EdgeCollider2D; template <> void RegisterUnityClass<EdgeCollider2D>(const char*);
class Effector2D; template <> void RegisterUnityClass<Effector2D>(const char*);
class FixedJoint2D; template <> void RegisterUnityClass<FixedJoint2D>(const char*);
class FrictionJoint2D; template <> void RegisterUnityClass<FrictionJoint2D>(const char*);
class HingeJoint2D; template <> void RegisterUnityClass<HingeJoint2D>(const char*);
class Joint2D; template <> void RegisterUnityClass<Joint2D>(const char*);
class Physics2DSettings; template <> void RegisterUnityClass<Physics2DSettings>(const char*);
class PhysicsMaterial2D; template <> void RegisterUnityClass<PhysicsMaterial2D>(const char*);
class PhysicsUpdateBehaviour2D; template <> void RegisterUnityClass<PhysicsUpdateBehaviour2D>(const char*);
class PlatformEffector2D; template <> void RegisterUnityClass<PlatformEffector2D>(const char*);
class PointEffector2D; template <> void RegisterUnityClass<PointEffector2D>(const char*);
class PolygonCollider2D; template <> void RegisterUnityClass<PolygonCollider2D>(const char*);
class RelativeJoint2D; template <> void RegisterUnityClass<RelativeJoint2D>(const char*);
class Rigidbody2D; template <> void RegisterUnityClass<Rigidbody2D>(const char*);
class SliderJoint2D; template <> void RegisterUnityClass<SliderJoint2D>(const char*);
class SpringJoint2D; template <> void RegisterUnityClass<SpringJoint2D>(const char*);
class SurfaceEffector2D; template <> void RegisterUnityClass<SurfaceEffector2D>(const char*);
class TargetJoint2D; template <> void RegisterUnityClass<TargetJoint2D>(const char*);
class WheelJoint2D; template <> void RegisterUnityClass<WheelJoint2D>(const char*);
class SpriteMask; template <> void RegisterUnityClass<SpriteMask>(const char*);
class SpriteShapeRenderer; template <> void RegisterUnityClass<SpriteShapeRenderer>(const char*);
class StreamingController; template <> void RegisterUnityClass<StreamingController>(const char*);
class StreamingManager; template <> void RegisterUnityClass<StreamingManager>(const char*);
class ProceduralMaterial; template <> void RegisterUnityClass<ProceduralMaterial>(const char*);
class ProceduralTexture; template <> void RegisterUnityClass<ProceduralTexture>(const char*);
class SpeedTreeWindAsset; template <> void RegisterUnityClass<SpeedTreeWindAsset>(const char*);
class Terrain; template <> void RegisterUnityClass<Terrain>(const char*);
class TerrainData; template <> void RegisterUnityClass<TerrainData>(const char*);
class TerrainLayer; template <> void RegisterUnityClass<TerrainLayer>(const char*);
class Tree; template <> void RegisterUnityClass<Tree>(const char*);
class TerrainCollider; template <> void RegisterUnityClass<TerrainCollider>(const char*);
namespace TextRendering { class Font; } template <> void RegisterUnityClass<TextRendering::Font>(const char*);
namespace TextRenderingPrivate { class TextMesh; } template <> void RegisterUnityClass<TextRenderingPrivate::TextMesh>(const char*);
class Tilemap; template <> void RegisterUnityClass<Tilemap>(const char*);
class TilemapCollider2D; template <> void RegisterUnityClass<TilemapCollider2D>(const char*);
class TilemapRenderer; template <> void RegisterUnityClass<TilemapRenderer>(const char*);
namespace UI { class Canvas; } template <> void RegisterUnityClass<UI::Canvas>(const char*);
namespace UI { class CanvasGroup; } template <> void RegisterUnityClass<UI::CanvasGroup>(const char*);
namespace UI { class CanvasRenderer; } template <> void RegisterUnityClass<UI::CanvasRenderer>(const char*);
class UnityConnectSettings; template <> void RegisterUnityClass<UnityConnectSettings>(const char*);
class VFXManager; template <> void RegisterUnityClass<VFXManager>(const char*);
class VFXRenderer; template <> void RegisterUnityClass<VFXRenderer>(const char*);
class VisualEffect; template <> void RegisterUnityClass<VisualEffect>(const char*);
class VisualEffectAsset; template <> void RegisterUnityClass<VisualEffectAsset>(const char*);
class VisualEffectObject; template <> void RegisterUnityClass<VisualEffectObject>(const char*);
class WheelCollider; template <> void RegisterUnityClass<WheelCollider>(const char*);
class VideoClip; template <> void RegisterUnityClass<VideoClip>(const char*);
class VideoPlayer; template <> void RegisterUnityClass<VideoPlayer>(const char*);
class WindZone; template <> void RegisterUnityClass<WindZone>(const char*);

void RegisterAllClasses()
{
void RegisterBuiltinTypes();
RegisterBuiltinTypes();
	//Total: 201 non stripped classes
	//0. NavMeshAgent
	RegisterUnityClass<NavMeshAgent>("AI");
	//1. NavMeshData
	RegisterUnityClass<NavMeshData>("AI");
	//2. NavMeshObstacle
	RegisterUnityClass<NavMeshObstacle>("AI");
	//3. NavMeshProjectSettings
	RegisterUnityClass<NavMeshProjectSettings>("AI");
	//4. NavMeshSettings
	RegisterUnityClass<NavMeshSettings>("AI");
	//5. OffMeshLink
	RegisterUnityClass<OffMeshLink>("AI");
	//6. AimConstraint
	RegisterUnityClass<AimConstraint>("Animation");
	//7. Animation
	RegisterUnityClass<Animation>("Animation");
	//8. AnimationClip
	RegisterUnityClass<AnimationClip>("Animation");
	//9. Animator
	RegisterUnityClass<Animator>("Animation");
	//10. AnimatorController
	RegisterUnityClass<AnimatorController>("Animation");
	//11. AnimatorOverrideController
	RegisterUnityClass<AnimatorOverrideController>("Animation");
	//12. Avatar
	RegisterUnityClass<Avatar>("Animation");
	//13. AvatarMask
	RegisterUnityClass<AvatarMask>("Animation");
	//14. IConstraint
	RegisterUnityClass<IConstraint>("Animation");
	//15. LookAtConstraint
	RegisterUnityClass<LookAtConstraint>("Animation");
	//16. Motion
	RegisterUnityClass<Motion>("Animation");
	//17. ParentConstraint
	RegisterUnityClass<ParentConstraint>("Animation");
	//18. PositionConstraint
	RegisterUnityClass<PositionConstraint>("Animation");
	//19. RotationConstraint
	RegisterUnityClass<RotationConstraint>("Animation");
	//20. RuntimeAnimatorController
	RegisterUnityClass<RuntimeAnimatorController>("Animation");
	//21. ScaleConstraint
	RegisterUnityClass<ScaleConstraint>("Animation");
	//22. AssetBundle
	RegisterUnityClass<AssetBundle>("AssetBundle");
	//23. AssetBundleManifest
	RegisterUnityClass<AssetBundleManifest>("AssetBundle");
	//24. AudioBehaviour
	RegisterUnityClass<AudioBehaviour>("Audio");
	//25. AudioChorusFilter
	RegisterUnityClass<AudioChorusFilter>("Audio");
	//26. AudioClip
	RegisterUnityClass<AudioClip>("Audio");
	//27. AudioDistortionFilter
	RegisterUnityClass<AudioDistortionFilter>("Audio");
	//28. AudioEchoFilter
	RegisterUnityClass<AudioEchoFilter>("Audio");
	//29. AudioFilter
	RegisterUnityClass<AudioFilter>("Audio");
	//30. AudioHighPassFilter
	RegisterUnityClass<AudioHighPassFilter>("Audio");
	//31. AudioListener
	RegisterUnityClass<AudioListener>("Audio");
	//32. AudioLowPassFilter
	RegisterUnityClass<AudioLowPassFilter>("Audio");
	//33. AudioManager
	RegisterUnityClass<AudioManager>("Audio");
	//34. AudioMixer
	RegisterUnityClass<AudioMixer>("Audio");
	//35. AudioMixerGroup
	RegisterUnityClass<AudioMixerGroup>("Audio");
	//36. AudioMixerSnapshot
	RegisterUnityClass<AudioMixerSnapshot>("Audio");
	//37. AudioReverbFilter
	RegisterUnityClass<AudioReverbFilter>("Audio");
	//38. AudioReverbZone
	RegisterUnityClass<AudioReverbZone>("Audio");
	//39. AudioSource
	RegisterUnityClass<AudioSource>("Audio");
	//40. BaseVideoTexture
	RegisterUnityClass<BaseVideoTexture>("Audio");
	//41. SampleClip
	RegisterUnityClass<SampleClip>("Audio");
	//42. WebCamTexture
	RegisterUnityClass<WebCamTexture>("Audio");
	//43. Cloth
	RegisterUnityClass<Unity::Cloth>("Cloth");
	//44. Behaviour
	RegisterUnityClass<Behaviour>("Core");
	//45. BillboardAsset
	RegisterUnityClass<BillboardAsset>("Core");
	//46. BillboardRenderer
	RegisterUnityClass<BillboardRenderer>("Core");
	//47. BuildSettings
	RegisterUnityClass<BuildSettings>("Core");
	//48. Camera
	RegisterUnityClass<Camera>("Core");
	//49. Component
	RegisterUnityClass<Unity::Component>("Core");
	//50. ComputeShader
	RegisterUnityClass<ComputeShader>("Core");
	//51. Cubemap
	RegisterUnityClass<Cubemap>("Core");
	//52. CubemapArray
	RegisterUnityClass<CubemapArray>("Core");
	//53. CustomRenderTexture
	RegisterUnityClass<CustomRenderTexture>("Core");
	//54. DelayedCallManager
	RegisterUnityClass<DelayedCallManager>("Core");
	//55. EditorExtension
	RegisterUnityClass<EditorExtension>("Core");
	//56. Flare
	RegisterUnityClass<Flare>("Core");
	//57. FlareLayer
	RegisterUnityClass<FlareLayer>("Core");
	//58. GameManager
	RegisterUnityClass<GameManager>("Core");
	//59. GameObject
	RegisterUnityClass<GameObject>("Core");
	//60. GlobalGameManager
	RegisterUnityClass<GlobalGameManager>("Core");
	//61. GraphicsSettings
	RegisterUnityClass<GraphicsSettings>("Core");
	//62. Halo
	RegisterUnityClass<Halo>("Core");
	//63. InputManager
	RegisterUnityClass<InputManager>("Core");
	//64. LODGroup
	RegisterUnityClass<LODGroup>("Core");
	//65. LensFlare
	RegisterUnityClass<LensFlare>("Core");
	//66. LevelGameManager
	RegisterUnityClass<LevelGameManager>("Core");
	//67. Light
	RegisterUnityClass<Light>("Core");
	//68. LightProbeGroup
	RegisterUnityClass<LightProbeGroup>("Core");
	//69. LightProbeProxyVolume
	RegisterUnityClass<LightProbeProxyVolume>("Core");
	//70. LightProbes
	RegisterUnityClass<LightProbes>("Core");
	//71. LightingSettings
	RegisterUnityClass<LightingSettings>("Core");
	//72. LightmapSettings
	RegisterUnityClass<LightmapSettings>("Core");
	//73. LineRenderer
	RegisterUnityClass<LineRenderer>("Core");
	//74. LowerResBlitTexture
	RegisterUnityClass<LowerResBlitTexture>("Core");
	//75. Material
	RegisterUnityClass<Material>("Core");
	//76. Mesh
	RegisterUnityClass<Mesh>("Core");
	//77. MeshFilter
	RegisterUnityClass<MeshFilter>("Core");
	//78. MeshRenderer
	RegisterUnityClass<MeshRenderer>("Core");
	//79. MonoBehaviour
	RegisterUnityClass<MonoBehaviour>("Core");
	//80. MonoManager
	RegisterUnityClass<MonoManager>("Core");
	//81. MonoScript
	RegisterUnityClass<MonoScript>("Core");
	//82. NamedObject
	RegisterUnityClass<NamedObject>("Core");
	//83. Object
	//Skipping Object
	//84. OcclusionArea
	RegisterUnityClass<OcclusionArea>("Core");
	//85. OcclusionPortal
	RegisterUnityClass<OcclusionPortal>("Core");
	//86. PlayerSettings
	RegisterUnityClass<PlayerSettings>("Core");
	//87. PreloadData
	RegisterUnityClass<PreloadData>("Core");
	//88. Projector
	RegisterUnityClass<Projector>("Core");
	//89. QualitySettings
	RegisterUnityClass<QualitySettings>("Core");
	//90. RayTracingShader
	RegisterUnityClass<RayTracingShader>("Core");
	//91. RectTransform
	RegisterUnityClass<UI::RectTransform>("Core");
	//92. ReflectionProbe
	RegisterUnityClass<ReflectionProbe>("Core");
	//93. RenderSettings
	RegisterUnityClass<RenderSettings>("Core");
	//94. RenderTexture
	RegisterUnityClass<RenderTexture>("Core");
	//95. Renderer
	RegisterUnityClass<Renderer>("Core");
	//96. ResourceManager
	RegisterUnityClass<ResourceManager>("Core");
	//97. RuntimeInitializeOnLoadManager
	RegisterUnityClass<RuntimeInitializeOnLoadManager>("Core");
	//98. Shader
	RegisterUnityClass<Shader>("Core");
	//99. ShaderNameRegistry
	RegisterUnityClass<ShaderNameRegistry>("Core");
	//100. ShaderVariantCollection
	RegisterUnityClass<ShaderVariantCollection>("Core");
	//101. SkinnedMeshRenderer
	RegisterUnityClass<SkinnedMeshRenderer>("Core");
	//102. Skybox
	RegisterUnityClass<Skybox>("Core");
	//103. SortingGroup
	RegisterUnityClass<SortingGroup>("Core");
	//104. SparseTexture
	RegisterUnityClass<SparseTexture>("Core");
	//105. Sprite
	RegisterUnityClass<Sprite>("Core");
	//106. SpriteAtlas
	RegisterUnityClass<SpriteAtlas>("Core");
	//107. SpriteRenderer
	RegisterUnityClass<SpriteRenderer>("Core");
	//108. TagManager
	RegisterUnityClass<TagManager>("Core");
	//109. TextAsset
	RegisterUnityClass<TextAsset>("Core");
	//110. Texture
	RegisterUnityClass<Texture>("Core");
	//111. Texture2D
	RegisterUnityClass<Texture2D>("Core");
	//112. Texture2DArray
	RegisterUnityClass<Texture2DArray>("Core");
	//113. Texture3D
	RegisterUnityClass<Texture3D>("Core");
	//114. TimeManager
	RegisterUnityClass<TimeManager>("Core");
	//115. TrailRenderer
	RegisterUnityClass<TrailRenderer>("Core");
	//116. Transform
	RegisterUnityClass<Transform>("Core");
	//117. PlayableDirector
	RegisterUnityClass<PlayableDirector>("Director");
	//118. Grid
	RegisterUnityClass<Grid>("Grid");
	//119. GridLayout
	RegisterUnityClass<GridLayout>("Grid");
	//120. LocalizationAsset
	RegisterUnityClass<LocalizationAsset>("Localization");
	//121. ParticleSystem
	RegisterUnityClass<ParticleSystem>("ParticleSystem");
	//122. ParticleSystemForceField
	RegisterUnityClass<ParticleSystemForceField>("ParticleSystem");
	//123. ParticleSystemRenderer
	RegisterUnityClass<ParticleSystemRenderer>("ParticleSystem");
	//124. ArticulationBody
	RegisterUnityClass<Unity::ArticulationBody>("Physics");
	//125. BoxCollider
	RegisterUnityClass<BoxCollider>("Physics");
	//126. CapsuleCollider
	RegisterUnityClass<CapsuleCollider>("Physics");
	//127. CharacterController
	RegisterUnityClass<CharacterController>("Physics");
	//128. CharacterJoint
	RegisterUnityClass<Unity::CharacterJoint>("Physics");
	//129. Collider
	RegisterUnityClass<Collider>("Physics");
	//130. ConfigurableJoint
	RegisterUnityClass<Unity::ConfigurableJoint>("Physics");
	//131. ConstantForce
	RegisterUnityClass<ConstantForce>("Physics");
	//132. FixedJoint
	RegisterUnityClass<Unity::FixedJoint>("Physics");
	//133. HingeJoint
	RegisterUnityClass<Unity::HingeJoint>("Physics");
	//134. Joint
	RegisterUnityClass<Unity::Joint>("Physics");
	//135. MeshCollider
	RegisterUnityClass<MeshCollider>("Physics");
	//136. PhysicMaterial
	RegisterUnityClass<PhysicMaterial>("Physics");
	//137. PhysicsManager
	RegisterUnityClass<PhysicsManager>("Physics");
	//138. Rigidbody
	RegisterUnityClass<Rigidbody>("Physics");
	//139. SphereCollider
	RegisterUnityClass<SphereCollider>("Physics");
	//140. SpringJoint
	RegisterUnityClass<Unity::SpringJoint>("Physics");
	//141. AnchoredJoint2D
	RegisterUnityClass<AnchoredJoint2D>("Physics2D");
	//142. AreaEffector2D
	RegisterUnityClass<AreaEffector2D>("Physics2D");
	//143. BoxCollider2D
	RegisterUnityClass<BoxCollider2D>("Physics2D");
	//144. BuoyancyEffector2D
	RegisterUnityClass<BuoyancyEffector2D>("Physics2D");
	//145. CapsuleCollider2D
	RegisterUnityClass<CapsuleCollider2D>("Physics2D");
	//146. CircleCollider2D
	RegisterUnityClass<CircleCollider2D>("Physics2D");
	//147. Collider2D
	RegisterUnityClass<Collider2D>("Physics2D");
	//148. CompositeCollider2D
	RegisterUnityClass<CompositeCollider2D>("Physics2D");
	//149. ConstantForce2D
	RegisterUnityClass<ConstantForce2D>("Physics2D");
	//150. CustomCollider2D
	RegisterUnityClass<CustomCollider2D>("Physics2D");
	//151. DistanceJoint2D
	RegisterUnityClass<DistanceJoint2D>("Physics2D");
	//152. EdgeCollider2D
	RegisterUnityClass<EdgeCollider2D>("Physics2D");
	//153. Effector2D
	RegisterUnityClass<Effector2D>("Physics2D");
	//154. FixedJoint2D
	RegisterUnityClass<FixedJoint2D>("Physics2D");
	//155. FrictionJoint2D
	RegisterUnityClass<FrictionJoint2D>("Physics2D");
	//156. HingeJoint2D
	RegisterUnityClass<HingeJoint2D>("Physics2D");
	//157. Joint2D
	RegisterUnityClass<Joint2D>("Physics2D");
	//158. Physics2DSettings
	RegisterUnityClass<Physics2DSettings>("Physics2D");
	//159. PhysicsMaterial2D
	RegisterUnityClass<PhysicsMaterial2D>("Physics2D");
	//160. PhysicsUpdateBehaviour2D
	RegisterUnityClass<PhysicsUpdateBehaviour2D>("Physics2D");
	//161. PlatformEffector2D
	RegisterUnityClass<PlatformEffector2D>("Physics2D");
	//162. PointEffector2D
	RegisterUnityClass<PointEffector2D>("Physics2D");
	//163. PolygonCollider2D
	RegisterUnityClass<PolygonCollider2D>("Physics2D");
	//164. RelativeJoint2D
	RegisterUnityClass<RelativeJoint2D>("Physics2D");
	//165. Rigidbody2D
	RegisterUnityClass<Rigidbody2D>("Physics2D");
	//166. SliderJoint2D
	RegisterUnityClass<SliderJoint2D>("Physics2D");
	//167. SpringJoint2D
	RegisterUnityClass<SpringJoint2D>("Physics2D");
	//168. SurfaceEffector2D
	RegisterUnityClass<SurfaceEffector2D>("Physics2D");
	//169. TargetJoint2D
	RegisterUnityClass<TargetJoint2D>("Physics2D");
	//170. WheelJoint2D
	RegisterUnityClass<WheelJoint2D>("Physics2D");
	//171. SpriteMask
	RegisterUnityClass<SpriteMask>("SpriteMask");
	//172. SpriteShapeRenderer
	RegisterUnityClass<SpriteShapeRenderer>("SpriteShape");
	//173. StreamingController
	RegisterUnityClass<StreamingController>("Streaming");
	//174. StreamingManager
	RegisterUnityClass<StreamingManager>("Streaming");
	//175. ProceduralMaterial
	RegisterUnityClass<ProceduralMaterial>("Substance");
	//176. ProceduralTexture
	RegisterUnityClass<ProceduralTexture>("Substance");
	//177. SpeedTreeWindAsset
	RegisterUnityClass<SpeedTreeWindAsset>("Terrain");
	//178. Terrain
	RegisterUnityClass<Terrain>("Terrain");
	//179. TerrainData
	RegisterUnityClass<TerrainData>("Terrain");
	//180. TerrainLayer
	RegisterUnityClass<TerrainLayer>("Terrain");
	//181. Tree
	RegisterUnityClass<Tree>("Terrain");
	//182. TerrainCollider
	RegisterUnityClass<TerrainCollider>("TerrainPhysics");
	//183. Font
	RegisterUnityClass<TextRendering::Font>("TextRendering");
	//184. TextMesh
	RegisterUnityClass<TextRenderingPrivate::TextMesh>("TextRendering");
	//185. Tilemap
	RegisterUnityClass<Tilemap>("Tilemap");
	//186. TilemapCollider2D
	RegisterUnityClass<TilemapCollider2D>("Tilemap");
	//187. TilemapRenderer
	RegisterUnityClass<TilemapRenderer>("Tilemap");
	//188. Canvas
	RegisterUnityClass<UI::Canvas>("UI");
	//189. CanvasGroup
	RegisterUnityClass<UI::CanvasGroup>("UI");
	//190. CanvasRenderer
	RegisterUnityClass<UI::CanvasRenderer>("UI");
	//191. UnityConnectSettings
	RegisterUnityClass<UnityConnectSettings>("UnityConnect");
	//192. VFXManager
	RegisterUnityClass<VFXManager>("VFX");
	//193. VFXRenderer
	RegisterUnityClass<VFXRenderer>("VFX");
	//194. VisualEffect
	RegisterUnityClass<VisualEffect>("VFX");
	//195. VisualEffectAsset
	RegisterUnityClass<VisualEffectAsset>("VFX");
	//196. VisualEffectObject
	RegisterUnityClass<VisualEffectObject>("VFX");
	//197. WheelCollider
	RegisterUnityClass<WheelCollider>("Vehicles");
	//198. VideoClip
	RegisterUnityClass<VideoClip>("Video");
	//199. VideoPlayer
	RegisterUnityClass<VideoPlayer>("Video");
	//200. WindZone
	RegisterUnityClass<WindZone>("Wind");

}
