﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AnimationCurveCloner_Handles_mAE1AE747E153A7340A5B374A798036FBBC57EEA9 (void);
extern void AnimationCurveCloner_ConstructClone_m865B553D86AC7DF69A877604D8D058043919AA50 (void);
extern void AnimationCurveCloner_FillClone_mD22B76874E416DD92983B2CF1B92B3DAC93CABD8 (void);
extern void AnimationCurveCloner__ctor_mD6E48B9E766EF280BEDFCC3EB16E0632BB829138 (void);
extern void ArrayCloner_Handles_m0E4BB720F8CD89F4E273BA17FDA497BDF776D27A (void);
extern void ArrayCloner_ConstructClone_m04579E3DD0E8F23680D6C79F015400FD43174459 (void);
extern void ArrayCloner_FillClone_m0F528E997C2E6204FAF85C13039277A19BE756E1 (void);
extern void ArrayCloner__ctor_m4D3B005B865126FDAD5A36C8568128F3490ED950 (void);
extern void DictionaryCloner_Handles_m570A2092BB4E0F5E831541242A6CA8F21BC514D7 (void);
extern void DictionaryCloner_FillClone_mB8E877FF96C605D93364484F8B58C7E9B201EFD2 (void);
extern void DictionaryCloner__ctor_m8AAF2AF8CFF369BCD791A2397A5353C0141D848C (void);
extern void EnumerableCloner_Handles_m5A8F03D9D08058137BD11460B1EA554D81CB6B30 (void);
extern void EnumerableCloner_FillClone_mE457AA3D51BD07351B88DE79FB538822AB43B465 (void);
extern void EnumerableCloner_GetAddMethod_m09AC7C8CB77990AEBF0474E09C06A2816D9DA739 (void);
extern void EnumerableCloner__ctor_m507726268EB81A88D3DE851334C5E55B030F01EE (void);
extern void FakeSerializationCloner_get_config_m443ABF26ED2ACFC73C689914998F11A246A489B5 (void);
extern void FakeSerializationCloner_BeforeClone_mEBE7770ADAE80B2F6AB2A783AB35CB2226905D72 (void);
extern void FakeSerializationCloner_AfterClone_m0D2786E5900CBAFFB14F856AAF777072C673EA71 (void);
extern void FakeSerializationCloner_GetMembers_m5F23561288E698A71CEAD95A20D1517572C720C8 (void);
extern void FakeSerializationCloner__ctor_m09078A547F0A2E8664F20128626B429EB0288D51 (void);
extern void U3CU3Ec__cctor_mBCD221ED0EBA60148CA2A33074DCDE2D49E4F3D5 (void);
extern void U3CU3Ec__ctor_m742CC1499296E2F071621B16CD254C6EB2C5C142 (void);
extern void U3CU3Ec_U3CGetMembersU3Eb__6_0_m33FFFEA984ECA1DC5B1B81AB7FEA3962385977CD (void);
extern void FieldsCloner_IncludeField_m80655FC00A1C89C89AC871F2C929D545524BEB6E (void);
extern void FieldsCloner_IncludeProperty_m289BBFEA1CDF28ABBC9D1026BC0AA7A0A6565E72 (void);
extern void FieldsCloner__ctor_mA7E9B5ADDE2AC98DB4AE3F1F3003C908C7EF2096 (void);
extern void GradientCloner_Handles_m7280317154C345895B420698E0A5680BDF52ECC4 (void);
extern void GradientCloner_ConstructClone_m884C9EF1B85AF90A19ED92711CA46C4B88499A75 (void);
extern void GradientCloner_FillClone_m9173321CA6500A4353D5BFDF8EAD3E258E5DF0C6 (void);
extern void GradientCloner__ctor_mBFE97BBDC345F88F56B246505BFC32DB223EF0F7 (void);
extern void ListCloner_Handles_m0512E76505FD1DC7F672FCBD716077AF6277D2AE (void);
extern void ListCloner_FillClone_m1A846798478300D38A3DDA5A0B63D0D70E09C892 (void);
extern void ListCloner__ctor_m1821F8C5C50EE3AA7435EE660199FE2A8F005CE5 (void);
extern void ReflectedCloner_Handles_mBF1E5713155B43142E3EECE29B234645B0D91FA4 (void);
extern void ReflectedCloner_FillClone_mA133F029F37A42A0679BFD0CF354B57AB5DB66A9 (void);
extern void ReflectedCloner_GetAccessors_mFFA54F408B0C5FF803981B57C9E3EF9484959B11 (void);
extern void ReflectedCloner_GetOptimizedAccessors_m33131F7F62AE218E2392B97BB58287ABE450876D (void);
extern void ReflectedCloner_GetMembers_m73242C1E83E3D780632CB0D046182C4F5F1792CF (void);
extern void ReflectedCloner_IncludeField_mE8AAB0BA775EDE18E58992170ACF017B8F830231 (void);
extern void ReflectedCloner_IncludeProperty_m113FC0D97DB7A6B87302AA92006B296A8B9FB1D0 (void);
extern void ReflectedCloner__ctor_m26645C52857996628C7758F2ECFD22C087DA184C (void);
extern void Cloning__cctor_m13D9F298AA9A49D5CE6C135D05ADA852842CE078 (void);
extern void Cloning_get_cloners_m7516AD27C82F21D85A91DE78A062393AF04A8D2D (void);
extern void Cloning_get_arrayCloner_mDCF18912AF69448D0131E0EB6150313BAE1AAD20 (void);
extern void Cloning_get_dictionaryCloner_m8DA4729ECB30283B37557A8FB3626BF017551F25 (void);
extern void Cloning_get_enumerableCloner_m6ACB3160E0E452B38585CD88520DFF89C49FB2A1 (void);
extern void Cloning_get_listCloner_m5AECF91367F610B9FD436FC41495FE6AAC6BF9BF (void);
extern void Cloning_get_animationCurveCloner_mF92679E30BFBFCA8D51C6490C91B80C031763330 (void);
extern void Cloning_get_gradientCloner_m1452BDF0263935B1B609AC61F324E69791CCEEA0 (void);
extern void Cloning_get_fakeSerializationCloner_mD2C0A4FE224EAD6C528A9A407A7B38E6183F6CF1 (void);
extern void Cloning_Clone_m568ED7627B5747E9315765956B742ECFE1C7FA5A (void);
extern void Cloning_CloneViaFakeSerialization_mB0ECB29B50A1250E3E955E9A9145A834CDBAFE78 (void);
extern void Cloning_Clone_mA8AD9C1A5AAA7EB227B1D1584CABE06774586763 (void);
extern void Cloning_CloneInto_mCCB3833D539D0A930911E8D9B685B089720CF5AE (void);
extern void Cloning_GetCloner_mB6E52BB0F815151B1CA8F0FBF813C565404D3119 (void);
extern void Cloning_GetCloner_mD5582F98DCC18C4EFFB76DCB231339008EAF66B8 (void);
extern void Cloning_Skippable_mE90C99877B244E54730F5916073EFF3796745A33 (void);
extern void U3CU3Ec__DisplayClass35_0__ctor_m13D109E0F468768FA847E7C8934F3C253D95A3B4 (void);
extern void U3CU3Ec__DisplayClass35_0_U3CGetClonerU3Eb__0_m451E5D14F3547FE762A929FF1AFE47FD4F2B82A1 (void);
extern void CloningContext_get_clonings_m57EEC8EC2641C2ACE1A86770D42B9ACFC277E9D3 (void);
extern void CloningContext_get_fallbackCloner_m3DD3EFC34D0F6144533C7D17A3D21C553C95B950 (void);
extern void CloningContext_set_fallbackCloner_m55BC99F5581F65FA3FA0F8345EE1C1375A7D7434 (void);
extern void CloningContext_get_tryPreserveInstances_m35B40A236AED8CBC45326D77EE55BD51E99B13CB (void);
extern void CloningContext_set_tryPreserveInstances_mC1B373E85527CDB25357401BDFBAF98DEA0CF94F (void);
extern void CloningContext_Unity_VisualScripting_IPoolable_New_m4F67C90E938687CFCB78B20FD9DF18133248407A (void);
extern void CloningContext_Unity_VisualScripting_IPoolable_Free_mEECBBB9A5415A637E58FC835C7E6B9ED2438CEB0 (void);
extern void CloningContext_Dispose_m37225C7C7D5768A50305108638CC3FE7989ED7F8 (void);
extern void CloningContext_New_mFC459B2EEDB08F5A62EE6F6DFF680D9098D90439 (void);
extern void CloningContext__ctor_mEF4F7DECC098C27FB5EC50006277B510DB3B57C9 (void);
extern void U3CU3Ec__cctor_m8CE275C818AD7595B93CC5CB9CDC2E07269AAA34 (void);
extern void U3CU3Ec__ctor_m1D52D9043B0C07EF5B6F8160EDE1BE9D50089920 (void);
extern void U3CU3Ec_U3CNewU3Eb__15_0_mB0A2EC8258AE48181D6FB1702910B987F0B572C3 (void);
extern void AotDictionary__ctor_m29C507F410D2E930CC83552F8AF33E610B0E1B72 (void);
extern void AotDictionary_AotStubs_mB9DC0685ECF292621A6CFD4C46E9945633DF6409 (void);
extern void AotList__ctor_m41ED35E8BE97563A62E8597DAF14DA22E00F3715 (void);
extern void AotList_AotStubs_m70D81C2DC96A490390F08762CEF44F20837C62D3 (void);
extern void ValueAttribute__ctor_m175E73D23899766D498DF0609F1FFC5549043F0B (void);
extern void DisableAnnotationAttribute__ctor_mACB2BBBBB84EF7B50F4B2A4E628C0B4679384AE2 (void);
extern void IncludeInSettingsAttribute__ctor_mF420F5DCC5F72184853A7F5A71AA6FE5A81EDB0D (void);
extern void IncludeInSettingsAttribute_set_include_m47FE024A0C7096039DD171CE40FBD58EE473B51C (void);
extern void InspectableAttribute__ctor_m7870BEC1B8BA0F0D7952891C210AE0551A80DD25 (void);
extern void InspectorWideAttribute__ctor_m552B28D4B7600A6F6D6EA70A7FA982BE83D81177 (void);
extern void InspectorWideAttribute_set_toEdge_m9E239E2E48DCDC88ECE51309AB059176AABD21D4 (void);
extern void Ensure_get_IsActive_m02209695F39927F34703939CFC6611A834DFEB2A (void);
extern void Ensure_set_IsActive_m2E42ADB156261AB2ACBFD74C88A2686B096C51BD (void);
extern void Ensure_That_mA8D5A2A6E1D51157C99920962B64DF9BF20DA1E9 (void);
extern void Ensure_OnRuntimeMethodLoad_mFA2CA8893587A952FFED184B45DD91016CD67845 (void);
extern void Ensure__cctor_m8FD6F07FFF518C24B23AB780D8E17ACFB9F7D830 (void);
extern void EnsureThat_IsNotNull_m5EC50846951F99E0A677FCF5EFB764413EEF5ABD (void);
extern void EnsureThat__ctor_mB4F1D463D88F8A1E747C2D6D14829651EB0C4818 (void);
extern void ExceptionMessages_get_Common_IsNotNull_Failed_mE09022A0483B91AAD832AD2F3C098E8272147E7B (void);
extern void ExceptionMessages__cctor_m3AFBD6D30CDC473DD735CC118E69374BB5DE6B35 (void);
extern void InvalidImplementationException__ctor_mB7F77B15C275D57A96CA4CB448B481A5734E50A2 (void);
extern void PlatformUtility__cctor_mFF0A0C5C6FFDD91762B2378CD8BCD9FE8D9AE777 (void);
extern void PlatformUtility_CheckJitSupport_m2C0CCF8DF88FDC26FAD5FAA533BBCE4DF5B6E53B (void);
extern void ProfiledSegment__ctor_m0FA4B5DD1FFD67A924C451ACD6D7CECCF721DF4A (void);
extern void ProfiledSegment_get_name_m9A83A1B1858918C129A43ED337E23277F9EE9AA2 (void);
extern void ProfiledSegment_set_name_m2C9E954609FDD3F421CC890ACDD3FE7C5B26D4B0 (void);
extern void ProfiledSegment_get_stopwatch_mAADE38FDD80356EE0367C2823A5871B416BD80BE (void);
extern void ProfiledSegment_set_stopwatch_mE936E4624C2FC58738A37162ABA87C3F7D66E188 (void);
extern void ProfiledSegment_get_calls_m42FA79BC36DC4D4E154A230EF3568805AB20C14D (void);
extern void ProfiledSegment_set_calls_mF492A80029C52FADF5E3BBAC1666B4976A022848 (void);
extern void ProfiledSegment_get_parent_mBE916AC5FE45C65AA47912ADF8C79AF92E8E2518 (void);
extern void ProfiledSegment_set_parent_m2C899280C5AEF9D4FFF92497B2A21112CDF29E13 (void);
extern void ProfiledSegment_get_children_m1750C6E57AF8A2244AC7955903409A2078D73A52 (void);
extern void ProfiledSegment_set_children_m8DD8249EAA297F8064CFB19454766920D0C93579 (void);
extern void ProfiledSegmentCollection_GetKeyForItem_mDB1F3291339E5E624FC7CFE00B7DCA9FC75755BC (void);
extern void ProfiledSegmentCollection__ctor_m26764F5154AB5023FBDBF67CB12F6B1155843AEF (void);
extern void ProfilingScope__ctor_mF9BE98497C393234478FA7985004C5987D1144EE (void);
extern void ProfilingScope_Dispose_m7F66BF3E60126E49FAFA1E38F6126D71CFCDBB44 (void);
extern void ProfilingUtility__cctor_m9621EEE77AF891941DF842EB1DA273FAE639AC82 (void);
extern void ProfilingUtility_set_rootSegment_mE6791774DDC5D645738C0C0725054BA8A4146287 (void);
extern void ProfilingUtility_get_currentSegment_m29989761BD0FBE5AAF19C4398F03E042D0EBB7EC (void);
extern void ProfilingUtility_set_currentSegment_m8614FC08664A8E73A0504CC30310DB5D7F6D51C7 (void);
extern void ProfilingUtility_SampleBlock_mE7FA8F94361D7FF9F175E498F4DB1B7AECB5DDDC (void);
extern void ProfilingUtility_BeginSample_m944E5C3C885F14F0E837BEDC56B67FF7D1C3D86D (void);
extern void ProfilingUtility_EndSample_m8476D0C3313F65B91CE8018400448213B71A6A3B (void);
extern void AttributeUtility_GetAttributeCache_m0BF85D2907402A33848A0288DF1B4D0A8B4222CF (void);
extern void AttributeUtility__cctor_m4AB10CF9AF3D21DEAC9E70F059807BC758E01C3C (void);
extern void AttributeCache_get_inheritedAttributes_m7B00A5C8EBB1B79595E284E882E06BF6619A08E5 (void);
extern void AttributeCache_get_definedAttributes_mBBBDA84A9281235FAA537ADF2A9EE7F1A495E00C (void);
extern void AttributeCache__ctor_mDE1B2BBCE9B04F8EE8A6D6938A4C9644872C63A1 (void);
extern void AttributeCache_Cache_m73430F04D21D69FB1B0520570C42ADDBDC941DAA (void);
extern void AttributeCache_HasAttribute_mF47A0A17CE82F96216E586E73E928C29F8B96CF5 (void);
extern void AttributeCache_GetAttribute_m9042BAE430568B5D7E851F7AC9EF0F1B9970493A (void);
extern void AttributeCache_HasAttribute_m6DE0BA7A52D8EB2284ED28B641AF6F41C53C8AA7 (void);
extern void AttributeCache_GetAttribute_m1B367E0EC1D06BB18254A3C357B7547316F26AA9 (void);
extern void ConversionUtility_RespectsIdentity_m25D67F31084F93B202E7770D8E6148FCC3EC0630 (void);
extern void ConversionUtility_IsUpcast_m6E1F4D622F39517A4EA13CF09CD03178E2BDF074 (void);
extern void ConversionUtility_IsDowncast_m2A5D16A66B697AF36D35281164C2DC5AB265DF0F (void);
extern void ConversionUtility_HasImplicitNumericConversion_m1EAF48D8B2AB05EDA60F201E70640AE516DCDAA7 (void);
extern void ConversionUtility_HasExplicitNumericConversion_m5FF693F9371522083BEC877146CE6B5BCD512769 (void);
extern void ConversionUtility_FindUserDefinedConversionMethods_m013D713F44B187E0452B6D0681CA048C83D3EBB2 (void);
extern void ConversionUtility_GetUserDefinedConversionMethods_mB8E662C43F88A841E105E956ACA0170169DBD264 (void);
extern void ConversionUtility_GetUserDefinedConversionType_m613D07F77FF3949915BFDEE25C1607BA7A53BB6F (void);
extern void ConversionUtility_HasEnumerableToArrayConversion_m7A2916FB4E31FBDF28313748935DB5012CE7A245 (void);
extern void ConversionUtility_HasEnumerableToListConversion_m3962A56BC1B219E355FC61F2575DB6B871A7A591 (void);
extern void ConversionUtility_HasUnityHierarchyConversion_m60943B20F9A86C6EE9690C22D0E7E53554810CE4 (void);
extern void ConversionUtility_IsValidConversion_m895179055643109D4A844010FCB29F3B3F9CF608 (void);
extern void ConversionUtility_CanConvert_mA8F22C8F21FFD91A803529F6B6DCF5548A20D336 (void);
extern void ConversionUtility_IsConvertibleTo_m449880AECB0EBD44001F046AFEFEC9E20ABC9416 (void);
extern void ConversionUtility_GetRequiredConversion_m59F588841432F5AC06A57E4EF4CC604B6A234436 (void);
extern void ConversionUtility_DetermineConversionType_mB2F9B24E0333EA4A608E8D8404C652245BA4BE06 (void);
extern void ConversionUtility_GetRequiredConversion_m35AD8C744818D84F32BB83FE06949AB127233AAD (void);
extern void ConversionUtility__cctor_m6417CFACAE78479369968DFC33EC8A47CA7FA0F3 (void);
extern void ConversionQuery__ctor_m8486070E4307ABFAD499BBFD5694FC44D47A4B59 (void);
extern void ConversionQuery_Equals_m433F18C3D368EF871293F990F3433EA2A2DCE7DF (void);
extern void ConversionQuery_Equals_mEBBAF7F90C0933C8072AFF2A88FBE2938DB9629F (void);
extern void ConversionQuery_GetHashCode_m19A38C82ED3B33971CCA4792FC48A99BA21BBC24 (void);
extern void ConversionQueryComparer_Equals_m1227EF34810F4D09B03F2B6D54B5B8E1BCC93AF7 (void);
extern void ConversionQueryComparer_GetHashCode_m0AAD5740A28AA6CB55CAAFC400083D84AE5D98C5 (void);
extern void U3CU3Ec__cctor_mFFC2A9ABDE8C451BBB0BEF6E8489EE9AA9A6FEC2 (void);
extern void U3CU3Ec__ctor_m3C7EF343BCFD80554D3B1432EEE0A90952F5F654 (void);
extern void U3CU3Ec_U3CFindUserDefinedConversionMethodsU3Eb__11_0_m1532E38366EF46B817581A490728661E62DAD8E8 (void);
extern void U3CU3Ec_U3CFindUserDefinedConversionMethodsU3Eb__11_1_m9312372514C015C92094F467740F71A15F4C0CCF (void);
extern void U3CU3Ec__DisplayClass11_0__ctor_mD21BF2AF4F387C43CE373E271D37D00F0D0FD175 (void);
extern void U3CU3Ec__DisplayClass11_0_U3CFindUserDefinedConversionMethodsU3Eb__2_mEAE1A6C43BF8690564235F0CF048B3BDDDE465BD (void);
extern void U3CU3Ec__DisplayClass13_0__ctor_m5E3D815FF989DB07952FFEA94DE25C5D3264ED98 (void);
extern void U3CU3Ec__DisplayClass13_0_U3CGetUserDefinedConversionTypeU3Eb__0_m7F4A2C93288A462B6E5353D052FB78388314EDFB (void);
extern void U3CU3Ec__DisplayClass13_0_U3CGetUserDefinedConversionTypeU3Eb__1_m493930AF75FBC7E891B52A7AA988D434D6AB6A8E (void);
extern void U3CU3Ec__DisplayClass13_0_U3CGetUserDefinedConversionTypeU3Eb__2_m3A850A03B1C3EE21A0784685C4EB14B0A76E877F (void);
extern void LooseAssemblyName__ctor_m8834C59F1E492EA1A935207B7134961B35DCE8EA (void);
extern void LooseAssemblyName_Equals_mA5DB041D4D8667E44F9F1CB82A5C0BBE84E7E7B1 (void);
extern void LooseAssemblyName_GetHashCode_mB97F58ECA80BDA748C4D1A101386F6E2AAE2494A (void);
extern void LooseAssemblyName_ToString_m30BA77AF842E4FD31F5E422190DD9DBDADA9A3FE (void);
extern void MemberUtility__cctor_m664FAD732DCE19B8DE7DD19B0AF081B50CAD54D6 (void);
extern void MemberUtility_IsUserDefinedConversion_mCE97B647A7E9DC669FAF87DE4AD01FAAECC7544A (void);
extern void MemberUtility_IsExtension_mAAE33EA58F9578CD8F2FEFEDD5CE87A725DD03A8 (void);
extern void MemberUtility_IsStatic_m518306DE8B210938210C1A2DFC8AAA4A041F288A (void);
extern void MemberUtility_IsStatic_mD56EF75FDD803BB5D98ADCA15CE6FF46DDF8FD03 (void);
extern void MemberUtility_CanWrite_m3DAD0B8FCA1BF1092783730DAAC05F3DCC759860 (void);
extern void U3CU3Ec__cctor_m0E868FF92E2C40AD8C1A913911E48330D268C9D0 (void);
extern void U3CU3Ec__ctor_m43145BDC3A79A5557E76FC10A01C26661D0747D6 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__0_0_m7173AFF9208ED469948E0FA302CB6D3D10DA96D8 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__0_1_m9667E5040570CAD1933E9C402DF65C06CFCF1E9A (void);
extern void U3CU3Ec_U3C_cctorU3Eb__0_2_m131AD23DC7F2AE71D7304861E4C63F20E6F9AC72 (void);
extern void ExtensionMethodCache__ctor_mBA3AB489FAEA7A274B8D1236C6FEE0A9554C2B6C (void);
extern void U3CU3Ec__cctor_m7DE32F911FD2DE85CB320F5A78D2AA7C0BAD3E18 (void);
extern void U3CU3Ec__ctor_mF7C67F6B049E9C001A08FC852800B09C18E2E12E (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_0_mDF6087D5B1E8D42E1AD5B2D1B82E68DC16714CFC (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_1_mCD23635F671D3FCD7139716EC946DFB88DD159CB (void);
extern void U3CU3Ec_U3C_ctorU3Eb__0_2_m36397327B09DCB2F111245BB77F422AFA17CBD5E (void);
extern void Namespace__ctor_mEFD631C64BA242ED84917A8A41187E12274E557C (void);
extern void Namespace_get_FullName_mDB579A0D0D2C27FE32DEDCEA37DC084EF08D3D36 (void);
extern void Namespace_GetHashCode_m2A2B37632988B46BFDE3B6383C527318D43F8637 (void);
extern void Namespace_ToString_m8743EE052004FC1236F7ED0ECCE981C8EB9FCBBA (void);
extern void Namespace__cctor_mB5403664D6FF82D5E8826104284643B99EF4E8CF (void);
extern void Namespace_get_Global_mCE4DF5BEB067F955772F134FD16F9372E8F7E175 (void);
extern void Namespace_FromFullName_m1F3A9348555D5C71E8963B495BC4C41EDBE45C63 (void);
extern void Namespace_Equals_m041B99F9C7946F8EE79048F78856975FAF9D15F1 (void);
extern void Namespace_op_Implicit_m4BC642F9C84B895BDBF4F88F6E005C2372DF47FE (void);
extern void Namespace_op_Equality_m2626D986A4EFACB844C4545EE4A8B193CCE04478 (void);
extern void Collection_GetKeyForItem_m38823CEBF30147D7FCF5C2B2F9E63DA7E3294830 (void);
extern void Collection_TryGetValue_mADBB0DCB690F9886C1F201868247162A6B6F4B7F (void);
extern void Collection__ctor_m5EE24B9AFB48B63BD972C4B5193393BCA20C116D (void);
extern void InvokerBase__ctor_mF8778193D06F6C818A249562C7962709730CC5C3 (void);
extern void InvokerBase_Compile_m473742347F42A36C28E38FD5EA23B24A0E2FCF5E (void);
extern void InvokerBase_GetParameterExpressions_m4000146B3859FA03C9411B13994B29A6B791E94B (void);
extern void InvokerBase_Invoke_mE5B3475184E0816DD1D8C7BCBB0888BE48DB02A4 (void);
extern void OptimizedReflection__cctor_mA7D038684333D3CCEA63B838F2C0421EF3C94554 (void);
extern void OptimizedReflection_get_useJit_mA1E53EB6ECAF6C799D746D0F87F6EBC7AC01996B (void);
extern void OptimizedReflection_get_useJitIfAvailable_m7B5D5B745D9567F0B2078B6D9F78FEDFB06C2958 (void);
extern void OptimizedReflection_get_safeMode_m16A922E85DCBC77BF8D7842CF9D46D3AD747308A (void);
extern void OptimizedReflection_set_safeMode_m8917E0D8E613CE4C2EC23A10A70E42ECA21480CF (void);
extern void OptimizedReflection_OnRuntimeMethodLoad_mBD2F4E483E9A25C92E616EB594B727008FA650EA (void);
extern void OptimizedReflection_VerifyStaticTarget_mC301AAA3260A101CEF161A2DCBA13C66511FA6BE (void);
extern void OptimizedReflection_VerifyTarget_mD2F971BDCF20B242584AEAEB6997C010A2AB7FA1 (void);
extern void OptimizedReflection_SupportsOptimization_m347BFAAD8CC42D62232E26689C7B424C7700C348 (void);
extern void OptimizedReflection_Prewarm_m516501B4E933B7D7EFB1ACDF37FC7BB36A2B9E34 (void);
extern void OptimizedReflection_SetValueOptimized_m31D72287894B166E76AB07044AD0577D36872BF9 (void);
extern void OptimizedReflection_SupportsOptimization_mF3283EA8FB29FF45FF362320A80FA514F5124336 (void);
extern void OptimizedReflection_GetFieldAccessor_m1FE0A1FF5046C3F8518F3075BCCD1041B7BA06C1 (void);
extern void OptimizedReflection_Prewarm_m64238D56795E70E3BC787858AECD92AD2E80EA0C (void);
extern void OptimizedReflection_SetValueOptimized_m90041502530ABB8093833AB1AAF7EAD71CDA42AA (void);
extern void OptimizedReflection_SupportsOptimization_mE77F5BA15A9E93ED8D84947A11097FDFDE0E8D32 (void);
extern void OptimizedReflection_GetPropertyAccessor_m2B9D5C4E7AA788B3A4D24AEB15C8BC3BA6481951 (void);
extern void OptimizedReflection_Prewarm_mE002ED1A6BAE2192B09BB38105660A0D60C3FD84 (void);
extern void OptimizedReflection_SupportsOptimization_m6F8B75D83A6AA63D88FD9E39F8F774E9B92F3914 (void);
extern void OptimizedReflection_GetMethodInvoker_m1F5AE4FEE232BB21C091CFE0E87C2B80D68D1A2D (void);
extern void U3CU3Ec__cctor_mFDC2972A66902F0DB6D2194BC804AC618592E372 (void);
extern void U3CU3Ec__ctor_m229CF2F8E2915479FECDC11787B7C68731EDD5C8 (void);
extern void U3CU3Ec_U3CSupportsOptimizationU3Eb__39_0_m9814B0F6FBC37E25E109C41F4C94AE2C80A32D75 (void);
extern void ReflectionFieldAccessor__ctor_mF78A1143AC4C6DDACB9E9FECBFAC08F94A3537E4 (void);
extern void ReflectionFieldAccessor_Compile_mEBC82506C662D5D6BD8B5ECCCC84486258B5E14F (void);
extern void ReflectionFieldAccessor_GetValue_mEF2F61EE17D6187F4AD0ECC9BCAE5C3DA154C40C (void);
extern void ReflectionFieldAccessor_SetValue_mB377A07ABD9DAC8C32E16A900E825E94669D6716 (void);
extern void ReflectionInvoker__ctor_mAB6A4A7633A3F7C6F39FE67D3E67E808743061F3 (void);
extern void ReflectionInvoker_Compile_m0E557DBBCF69F0B9DBFC7C65F1E9E04291BBAEB9 (void);
extern void ReflectionInvoker_Invoke_mCEAB3C649FD9E5C239FA81DF0E6C5B626B0AAB89 (void);
extern void ReflectionInvoker__cctor_m95B3CC2E85900381361C876219C88FC9EC8FAA6C (void);
extern void ReflectionPropertyAccessor__ctor_m6D9ACC5A79528E9BC510DD39E888C308AD7DBBA4 (void);
extern void ReflectionPropertyAccessor_Compile_mA548D99C2C1CF4FC2D6BFDE60B88CC3290F68825 (void);
extern void ReflectionPropertyAccessor_GetValue_m24E6DB4D752CA9ECF169145015DFFBB9E830FFC5 (void);
extern void ReflectionPropertyAccessor_SetValue_m864D0F0A46C817DF7D444F569A3352930481828D (void);
extern void StaticActionInvokerBase__ctor_m5ACCD147F42CA3D67E774B9FEA8F256BF215321C (void);
extern void StaticActionInvoker__ctor_m68937E5823AB3318282DF052FC16E6A3932EE0C2 (void);
extern void StaticActionInvoker_GetParameterTypes_m785C5398B6E9D7BC36077F14788966EA4AC04641 (void);
extern void StaticActionInvoker_CompileExpression_m850609F926060D54357A35638C924DF30DC1F7E1 (void);
extern void StaticActionInvoker_CreateDelegate_mEAD1944C9CA6D3064120700F5FDF27DEA81EDF30 (void);
extern void StaticActionInvoker_U3CCreateDelegateU3Eb__7_0_m62B13C47D02C64D0D12A564105859E1715987E68 (void);
extern void StaticInvokerBase__ctor_m6C9DC047E597DE757E95781C8ACA69DC87604534 (void);
extern void StaticInvokerBase_CompileExpression_m46F00B15A03177401B26BEAA7F8637D699B5CB5A (void);
extern void StaticInvokerBase_VerifyTarget_mA527572123E57DFA81EC65FE82D50BA811AC1424 (void);
extern void RenamedAssemblyAttribute_get_previousName_m49CAA91F128F051AD44EF708A67B1709491F3FC0 (void);
extern void RenamedAssemblyAttribute_get_newName_m7D5A43A237D16F14068DA42A4961685B43B8EC5A (void);
extern void RenamedFromAttribute_get_previousName_mCC937DC8B67B589D4111AAC308C3D074A1632352 (void);
extern void RenamedNamespaceAttribute__ctor_mDDD1EB50234271FA6931DBB745EA5297F345E63E (void);
extern void RenamedNamespaceAttribute_get_previousName_m7F20312B67C7E59FF98C8CAF6BB823FD3F9B3FAB (void);
extern void RenamedNamespaceAttribute_get_newName_m2EBF2918C960DC6299410D91BFCCD26E084078F4 (void);
extern void RuntimeCodebase_get_types_m3F45B74CB58DFCB03FC5547B9ADCDDD490BC7DAA (void);
extern void RuntimeCodebase_get_assemblies_m152648BAC9B232F7A0B7600EE603D0C10D89E35B (void);
extern void RuntimeCodebase__cctor_mC4C663CE8466644C7EA7E2E98F12A7D68F7D5F07 (void);
extern void RuntimeCodebase_GetAssemblyAttributes_mEA3530C25AD69CA371E1ED80B51CE431574CED04 (void);
extern void RuntimeCodebase_GetAssemblyAttributes_m61A6334A9520E8A29CA6DE4C1FDF671B246BE8EC (void);
extern void RuntimeCodebase_SerializeType_m5C4C24AE860C5758DAAF06C213A8F23868F26EF1 (void);
extern void RuntimeCodebase_TryDeserializeType_m5CC65D7B9C28DA10A2CA580A21EBF4B3AED83149 (void);
extern void RuntimeCodebase_DeserializeType_mDAC85B396158EA8898F0FC4F718B90BC97B607C1 (void);
extern void RuntimeCodebase_TryCachedTypeLookup_mE0D4B034A584410139207C9837E467072B5F8F0E (void);
extern void RuntimeCodebase_TrySystemTypeLookup_m1613832DE9F9FE5D75923D36764A00E337B4F7CA (void);
extern void RuntimeCodebase_TrySystemTypeLookup_mFB5AE558EB67EBB4258567B8C5103E7A802B5E63 (void);
extern void RuntimeCodebase_TryRenamedTypeLookup_mF92440902F2F467EB40F8F07C5976DC6E55A653E (void);
extern void RuntimeCodebase_get_renamedNamespaces_m6A46742DBC322161E1D84D4B99628416ACC9AF20 (void);
extern void RuntimeCodebase_get_renamedAssemblies_mF5DE74B7E878DAA69297418DAA63FE93BEE3CEB6 (void);
extern void RuntimeCodebase_get_renamedTypes_mD01972DB9723D31C6491C25795429DE47A3579A4 (void);
extern void RuntimeCodebase_FetchRenamedNamespaces_m6AEF3ED15432107D5DD44C4C19319F5CAB97EAA8 (void);
extern void RuntimeCodebase_FetchRenamedAssemblies_m7E7EF9246967FF76CABE08F1485F0FB556CC921D (void);
extern void RuntimeCodebase_FetchRenamedTypes_m95705C73DA7BC9BDCFF3E7191DFB44FC24645AA5 (void);
extern void U3CU3Ec__DisplayClass25_0__ctor_m0D93D81BF21CE63B7F8E961ED5EE8B6B4797122D (void);
extern void U3CU3Ec__DisplayClass25_0_U3CTrySystemTypeLookupU3Eb__0_m35E9BED0D9C2FE659F43D79227F6B69684BE03D2 (void);
extern void U3CGetAssemblyAttributesU3Ed__15__ctor_m47420C8E084D30FB82070FAB0D44CD2E015FEC3F (void);
extern void U3CGetAssemblyAttributesU3Ed__15_System_IDisposable_Dispose_m975BBD0D64613D68E9BB439F9ECD56F1AE5A5B06 (void);
extern void U3CGetAssemblyAttributesU3Ed__15_MoveNext_m212DF6D7868EF9CD85C5BFF546C65C7CA0622BA6 (void);
extern void U3CGetAssemblyAttributesU3Ed__15_U3CU3Em__Finally1_m3B86D5FC290D424874FDE134F9664955DDD69F4C (void);
extern void U3CGetAssemblyAttributesU3Ed__15_U3CU3Em__Finally2_m4037CD33D7E52258B71656E925137DC11608D827 (void);
extern void U3CGetAssemblyAttributesU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_AttributeU3E_get_Current_m91D9E801B043345FA4FA529CFDA0152F349C03DA (void);
extern void U3CGetAssemblyAttributesU3Ed__15_System_Collections_IEnumerator_Reset_m61BFD4A96E6ED6AE40EBEEC0D22A370415224E60 (void);
extern void U3CGetAssemblyAttributesU3Ed__15_System_Collections_IEnumerator_get_Current_m2AC28B978947B95D322602E2FB4125B2B3517A5B (void);
extern void U3CGetAssemblyAttributesU3Ed__15_System_Collections_Generic_IEnumerableU3CSystem_AttributeU3E_GetEnumerator_m1ACF3B7B507CF7278E09B68E927C80F897FA467E (void);
extern void U3CGetAssemblyAttributesU3Ed__15_System_Collections_IEnumerable_GetEnumerator_m0B04727F2A2E9D9B1550180CD3A262471134125D (void);
extern void TypeName_get_AssemblyDescription_mADDDD6FA51C519C60491A8B20F8B69949A7121EF (void);
extern void TypeName_set_AssemblyDescription_m3EBD7FB0F0C264E8EB6362C10645035C9384F05D (void);
extern void TypeName_get_AssemblyName_mBE44717AA3A7377931E7A5B955711CE85019186F (void);
extern void TypeName_set_AssemblyName_m9AD0F0CA466333A8C06257F367D5BDC84FB5C568 (void);
extern void TypeName_set_AssemblyVersion_m7BC5B2480F201871A042EBE5694A35E73C89887D (void);
extern void TypeName_set_AssemblyCulture_mDD4A88BFC38A7B3A2F55030570E9ADD11AAD4A33 (void);
extern void TypeName_set_AssemblyPublicKeyToken_m03E38B53CD7467D5EDD4089AF75863381280AD80 (void);
extern void TypeName_get_GenericParameters_m519E4A51DCCD0F0701F35C09784D8128EE8263AC (void);
extern void TypeName_get_Name_mBC768C507F7EC26072615B2B6036307367B42D9E (void);
extern void TypeName_set_Name_mBC6647619A19BB99875C39EF7A6FD0D14B516E56 (void);
extern void TypeName_get_IsArray_m92C2087D662FC897ADA6F7251D84612D4A9BB036 (void);
extern void TypeName_Parse_m164944B90D4C17C850C4D732D96D55B515BE4F41 (void);
extern void TypeName__ctor_mA9E1A4603EA5E12401679124F6E53C0DFBF0F735 (void);
extern void TypeName_LookForPairThenRemove_mCE5F569E040A70172646F30AAF4D96B2379EE24B (void);
extern void TypeName_ReplaceNamespace_mA1A47EA949FF2DE3F9EDBF5E6A2D746CDD5C35C5 (void);
extern void TypeName_ReplaceAssembly_m14F4A61C1B10FA4C98C0FA156FEE5A3152A0438D (void);
extern void TypeName_ReplaceName_m33BDC9C3E587682E4D9D1A70E5689ED2CE1D7CB3 (void);
extern void TypeName_ReplaceName_m32FDC43BEC0C6B53EECEE46C0560CDF61AB0BD5D (void);
extern void TypeName_ToElementTypeName_m1FF41E9B6AC37F2E7753C12F77F2279865743342 (void);
extern void TypeName_ToArrayOrType_mF8E6577D6572798C6AB5E0C4CDF708D45ED6C574 (void);
extern void TypeName_SetAssemblyName_mA67AE36887C5DC459E77D3463A518CA58B45E3A4 (void);
extern void TypeName_UpdateName_mDFE4E330B5E41A27413315AED6F977B6527FDE13 (void);
extern void TypeName_ToString_mA2BA8AAEF508ECFFF91FBB6F67D451A96D6AD1A8 (void);
extern void TypeName_ToString_mE707318674F5482901B92C14E7269AEF03067AE7 (void);
extern void TypeName_ToLooseString_m65BC19E3146198A4D8B2B7CF96495EF119E70EB9 (void);
extern void U3CU3Ec__cctor_m20A9DCE1C467E521281D411A17A0C7DD2D8145FD (void);
extern void U3CU3Ec__ctor_m02363FEAE6C3572064D33111C69596ACDC921325 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__35_0_m7515082ACA3072CCCE8F2CEA54CE7C8B3C85833B (void);
extern void TypeUtility_IsStatic_m95F7E5A523CE72B003E45896FFF9EAF419B64D86 (void);
extern void TypeUtility_IsNullable_m231FD352991AA34AFE82055417DA61DB28FF4AF8 (void);
extern void TypeUtility_IsReferenceType_m1319781FE9641ACD464EC92146549F533A985232 (void);
extern void TypeUtility_IsAssignableFrom_mAEB6320B1B550044B01FABADEF19F5B2C1D4D569 (void);
extern void TypeUtility_GetTypesSafely_m1127FF593F0365E1F97DE2683797FD161911459B (void);
extern void TypeUtility__cctor_m45284A9000012A4F4B5D4783E319433401346E1B (void);
extern void U3CU3Ec__cctor_mAEC24D9AC93F35222671E1A349499CA523B22063 (void);
extern void U3CU3Ec__ctor_m9C9EBF4D33D62E1F215CFA1389D642C1876A2B96 (void);
extern void U3CU3Ec_U3CGetTypesSafelyU3Eb__35_1_mD0A8D58ECF1ADF9351D3B57C260A51854805B3B8 (void);
extern void U3CU3Ec_U3CGetTypesSafelyU3Eb__35_0_mE62999A474952F85D25E9C36CD210C383AA4C4BF (void);
extern void U3CGetTypesSafelyU3Ed__35__ctor_m9701C2638FE76E20AFAD85EA4D7A9CF0E2FE0345 (void);
extern void U3CGetTypesSafelyU3Ed__35_System_IDisposable_Dispose_m5EA2247E9D84A5ADC55E29460937CF9F88134AF7 (void);
extern void U3CGetTypesSafelyU3Ed__35_MoveNext_m602A89C0EEB551F173B43FB64773BC191EA2DD5E (void);
extern void U3CGetTypesSafelyU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_TypeU3E_get_Current_mB8219F6708B3866C2051342395157FC925AEA8ED (void);
extern void U3CGetTypesSafelyU3Ed__35_System_Collections_IEnumerator_Reset_m840895FA7E2F01ACFEAB3185C25F1907417F9F91 (void);
extern void U3CGetTypesSafelyU3Ed__35_System_Collections_IEnumerator_get_Current_mB28A5657B028511D87AF2DB15D631FF090690FF6 (void);
extern void U3CGetTypesSafelyU3Ed__35_System_Collections_Generic_IEnumerableU3CSystem_TypeU3E_GetEnumerator_m7C50E41A556CF784E82B0CFEA752B79C96779D84 (void);
extern void U3CGetTypesSafelyU3Ed__35_System_Collections_IEnumerable_GetEnumerator_m9BE818126736C35125B5535744D903B198AAA135 (void);
extern void LooseAssemblyNameConverter_get_ModelType_mB19B6AFD590D99235B8AA71EBBFE78EE9444521F (void);
extern void LooseAssemblyNameConverter_CreateInstance_mDA13B4217C2F40BE38CAB5D948C2D50ABFA129AE (void);
extern void LooseAssemblyNameConverter_TrySerialize_mA6B65EF2FE0C61D1314D291A85C4841344734252 (void);
extern void LooseAssemblyNameConverter_TryDeserialize_mB83E01E613DBD3F4F96BE45BB20C4F12636422FE (void);
extern void LooseAssemblyNameConverter__ctor_mE139E612CCF8019E6150DE862817FA1E3EE25CFF (void);
extern void NamespaceConverter_get_ModelType_m19B036601B6EAD39365E78803BBD02DC3501ADAE (void);
extern void NamespaceConverter_CreateInstance_mB2970F338146D188B4AFF549B6CE84E156E90BDE (void);
extern void NamespaceConverter_TrySerialize_m525E79E5EC37A73BDE5B7C524825D534BE92CE65 (void);
extern void NamespaceConverter_TryDeserialize_mC49A2DD95384647E052E58E4F592B2779435202F (void);
extern void NamespaceConverter__ctor_mA33B806D736BCA8EDC2B2F43A919B01CC958E339 (void);
extern void Ray2DConverter_DoSerialize_m2CDE91F48310806906DB55D3F3C3798524F6205E (void);
extern void Ray2DConverter_DoDeserialize_mAC43A59CCEC9EA2FAD564306C791AEFB7E22DAB3 (void);
extern void Ray2DConverter_CreateInstance_mC4483214E994B76F96970FF99BB554FB727167E4 (void);
extern void Ray2DConverter__ctor_mFCD2BA4B7D50BF03A02FE263BAD388BFA121AECA (void);
extern void RayConverter_DoSerialize_m69D7D5CE5706F302544207622C297087AA073F83 (void);
extern void RayConverter_DoDeserialize_mDC94564FC1FAC221EF831AF9AF4E1175ED7967F2 (void);
extern void RayConverter_CreateInstance_m023835981B6872E0A01ED20D2635AE9BCFB2AED0 (void);
extern void RayConverter__ctor_m8B3EF461784305F308BF7D6259A3019B7DD2DB03 (void);
extern void UnityObjectConverter_get_objectReferences_mE0B7C99E0991DBA79ECCB99F81C82136C22F3416 (void);
extern void UnityObjectConverter_CanProcess_m561DF6C035BDD50DBF3ABA03EBA3946E451E8CC5 (void);
extern void UnityObjectConverter_RequestCycleSupport_m6318E3411DE26A9F9FD38E58DCF4E29863634F34 (void);
extern void UnityObjectConverter_RequestInheritanceSupport_m4E047496E7F592E0ABEBDD12486B016D75C679BB (void);
extern void UnityObjectConverter_TrySerialize_m04D02BCCF4CDD400F40D5CE29D7E8E5FB444604A (void);
extern void UnityObjectConverter_TryDeserialize_m6EAC5B6CC58F710CA73DBF1D6BD50D5CC59E9A6D (void);
extern void UnityObjectConverter_CreateInstance_mD4FA2B90A8700DCC585D1431FACD9AD5FDE0D4C7 (void);
extern void UnityObjectConverter__ctor_mFA76717DCDA335EA5F6687B108DE6AEC3810288C (void);
extern void DoNotSerializeAttribute__ctor_m27900120F3A25D3895C619492BD83F612E91B3D8 (void);
extern void Serialization__cctor_mB4F0A488DA42E04996281B8CBA51226AB1C8AC41 (void);
extern void Serialization_get_isUnitySerializing_m46208D5D6E042D9797B2DFBFCE0632A1BE5A9504 (void);
extern void Serialization_set_isUnitySerializing_mAC835F229C4FAE90E9040E4760001AD7DB39016E (void);
extern void Serialization_get_isCustomSerializing_m3C895CC8649F88D6712A406A25167C7B369BC0A8 (void);
extern void Serialization_StartOperation_m0E07E844663C5C9673C0B5870CB73BEF53B0E0EB (void);
extern void Serialization_EndOperation_mC37CC5563ED0C5086EC3AC2D58A8EE86BF822247 (void);
extern void Serialization_Serialize_m32FAE37FB98FC80EE641572B44AA555F4C374AFA (void);
extern void Serialization_DeserializeInto_mF9E364DD7743C17A22CD089EA8D4FC3065B08062 (void);
extern void Serialization_Deserialize_mA102EBE57964B8923192696AD3FE6541229415C0 (void);
extern void Serialization_SerializeJson_mB0FA51A5F9BE987432B3205814DFC0CA609C1914 (void);
extern void Serialization_DeserializeJsonUtil_mA520214A610CDBF12EAD5743E64528494362F4B6 (void);
extern void Serialization_DeserializeJson_mA737B5ECA3C7DFE7442A2D6563BCCDD0E586BEB8 (void);
extern void Serialization_HandleResult_mA8F7C587002F125F0A2B4214108BB4651359B4F7 (void);
extern void Serialization_PrettyPrint_m425EE6DFF2AA0559BA142CDE136693238DDEEA42 (void);
extern void SerializationData_get_json_m316B4017338BC7DB9B4D3D9A81BEF1C01232FB24 (void);
extern void SerializationData_get_objectReferences_m70704774677025CE081A408A7D558A9309C24DA4 (void);
extern void SerializationData__ctor_m590F6C005C219D41968701544B5D4D2A9E2C2607 (void);
extern void SerializationData__ctor_m1AECF919A4D6D2B9256D1ECD164E2A8189DE64AD (void);
extern void SerializationData_Clear_mC7B73F14EFA97C2831731F9B3C558765F08EC73D (void);
extern void SerializationData_ToString_m277BCD1A9A8431713FCADB24C479817E0C6A3A4C (void);
extern void SerializationData_ToString_mAC5F79322472876518D60654C6515E4ED142EF87 (void);
extern void SerializationData_ShowString_m53B7B7664BCFCE62D2382E53FF7C0205D9034270 (void);
extern void SerializationOperation__ctor_mB4A87C9CDA0B896AA0142F226C53CFB73DAA3573 (void);
extern void SerializationOperation_get_serializer_m0B67471F76A1393A6221DBE9F13706236C371ECF (void);
extern void SerializationOperation_set_serializer_m93AD82939387F372A2E3DB25EC068CD0A87E5C60 (void);
extern void SerializationOperation_get_objectReferences_mAF348F7C8689414FC1FEAE422D6173253D4D5444 (void);
extern void SerializationOperation_set_objectReferences_m73EC5199265D1C61291DBD948CE1687460EF1155 (void);
extern void SerializationOperation_Reset_m6E8884DC5D1B7E20DB08B3B5468D52D8E93BA217 (void);
extern void SerializationVersionAttribute__ctor_mB7B6BA540C0D134C649AE39016D9FF506B34DFB7 (void);
extern void SerializeAttribute__ctor_mC49BD2B8E51A1117AC0B8760F62A89A21AD93BCA (void);
extern void LudiqScriptableObject_add_OnDestroyActions_m0A7072A424603508C71272D0CE997137A47092DC (void);
extern void LudiqScriptableObject_remove_OnDestroyActions_m30485F5810C1BEBDF70C495771A0CFCE62D5260A (void);
extern void LudiqScriptableObject_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m8758CB699FAD9F7908247C87D3EF177277174642 (void);
extern void LudiqScriptableObject_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m18C20C86B3BF295C04C543AFD9B68F961735F53F (void);
extern void LudiqScriptableObject_OnBeforeSerialize_mE3F4CB617DE9FFDE4C13DBF648D4139CB8D05E02 (void);
extern void LudiqScriptableObject_OnAfterSerialize_mE88BFC91F746BAA50EB394941006C389FD11E87B (void);
extern void LudiqScriptableObject_OnBeforeDeserialize_m89A629A4F62E0C972ADFDD5A29FA6D10F58BB3CF (void);
extern void LudiqScriptableObject_OnAfterDeserialize_m622CDCDACBD0B2B628EAEBD829C5753CA9D25ACB (void);
extern void LudiqScriptableObject_OnPostDeserializeInEditor_mE76D1870C90DB8EFA83C8DE030861FC78B9081CE (void);
extern void LudiqScriptableObject_OnDestroy_m0CD244E99818B2DBB68F05040CD9452C18034AA3 (void);
extern void LudiqScriptableObject_ShowData_m53F17A66E811705D002006DED0BED7EE7F06428D (void);
extern void LudiqScriptableObject_ToString_m870D1107807BDC18072D2F3549A7E51930EBC5B7 (void);
extern void LudiqScriptableObject__ctor_mEA1CF35A7B3BAA4B07941A50023E6D2A79F42F87 (void);
extern void SingletonAttribute__ctor_mE4483DC8C17D6081B95A1905CC76403F5585B52B (void);
extern void SingletonAttribute_get_Persistent_m2EFEC55D21A0D5E42FCD1F50CF6F5AD37453D618 (void);
extern void SingletonAttribute_set_Persistent_m581349C686F49DB2CF7C05E04375DE24E78BC103 (void);
extern void SingletonAttribute_get_Automatic_m0D0360C87EA09DFBEB983A531ABB8AD722938B43 (void);
extern void SingletonAttribute_set_Automatic_m9E2E6F4618A0AD9F4C785684E00DE1BA9E452518 (void);
extern void SingletonAttribute_get_HideFlags_mF438190953DB0E5AC7670D86186B130BE244B7CD (void);
extern void SingletonAttribute_set_HideFlags_m4040377AD3624F4946BBF9E76F877E17D4C2E766 (void);
extern void SingletonAttribute_get_Name_m0C2C867429B94CA6AE16403AEE5CE7E0628CF3DF (void);
extern void SingletonAttribute_set_Name_m09450F7BDECB475532DD96B40E6F622208CE0275 (void);
extern void UnityThread_get_allowsAPI_mD5F65F1DD7EB660A36B87BAAC6F346FC41B7EADD (void);
extern void UnityThread_RuntimeInitialize_m85A2C09B6D8474B13CC2F1EE1B6E7BC91E482EDC (void);
extern void UnityThread__cctor_mA06A81D8293AFDDC8E9B4899A342843052D20957 (void);
extern void Recursion_set_safeMode_m7300893217499247EA9F97AEA8A3B9128CD49E54 (void);
extern void Recursion_OnRuntimeMethodLoad_m278652A8C70DEC3283FE5DC1C419613D5A601979 (void);
extern void Recursion__cctor_mADF5B2B7B757832C0ACAEEB220754DFFBFE29DE2 (void);
extern void ReferenceCollector_Initialize_m574BDBCDD36CE25A2B8E839EBB7365285149DA22 (void);
extern void U3CU3Ec__cctor_mE857E2EB95F3BA16B14C357CE4A277FFA8574DF9 (void);
extern void U3CU3Ec__ctor_m06358C0A5C784796096CF1C08035A61D4529DB0D (void);
extern void U3CU3Ec_U3CInitializeU3Eb__3_0_mDCDC7B052AF3332850F77AFB6123C9AFF02443BB (void);
extern void ReferenceEqualityComparer__ctor_m335A10C125C91D936A1AC64F5FCF0F95810F29CF (void);
extern void ReferenceEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_Equals_mE6E4ACBF46F0D537E34F1E3310BEFE42649CF15A (void);
extern void ReferenceEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_GetHashCode_mCE7C7ECBFAA52764FA101B54A143C07A13D17268 (void);
extern void ReferenceEqualityComparer_GetHashCode_m08759602B1C135CAB0976FB150F9FE28D5898787 (void);
extern void ReferenceEqualityComparer__cctor_m925EFC6A80AEA9AC8EA94B7DEDEA8CB4ABEF792A (void);
extern void RuntimeVSUsageUtility_RuntimeInitializeOnLoadBeforeSceneLoad_mA585350F53EDD32CC1D28F89C58D4A3EABD65F1B (void);
extern void StringUtility_TrimStart_m4191946C2A5B0F1D5E22B3CA40EA28AA9AE964D7 (void);
extern void StringUtility_PartsAround_mA21406BFF73C1B226F8D6F2AECE0E13F75249A5E (void);
extern void StringUtility_ToHexString_m4FFBE792B82A834F0FD1854A887611B08B381563 (void);
extern void StringUtility__cctor_m4272A2025A6775E07ED6B4FB6415C955A6DF0B38 (void);
extern void UnityObjectUtility_IsUnityNull_m0FE15C8DFA320342403361EA2AC22C9B3E9E3CDE (void);
extern void UnityObjectUtility_ToSafeString_m84CD790BE6B992B0B0643EA094D177FDAFBFA3E7 (void);
extern void XColor_ToHexString_mB2541847421172380A8CCAC744CD1133FDC901BA (void);
extern void ApplicationVariables_get_asset_mAC82AD0A8791B3E45066F5A5CD1BF6CDC1F4335E (void);
extern void ApplicationVariables_Load_mD4C28B27245BCB69584579C07CE3A84BA0580703 (void);
extern void ApplicationVariables_set_runtime_m779787A48A8464A65ABC60F0545215A408BAE22B (void);
extern void ApplicationVariables_OnEnterPlayMode_m158EB87217011E5492FE939689B5A63730EDBF6D (void);
extern void ApplicationVariables_OnExitPlayMode_m713CE18DBFEF16162B560A33B9AAA5B002E18B5E (void);
extern void ApplicationVariables_CreateRuntimeDeclarations_m8705F9B91A9977A4FB2B86420E332753EB30C664 (void);
extern void InspectorVariableNameAttribute__ctor_mE877B6A2AB7668B0C54FAF5200B7F891ADCF6A7E (void);
extern void InspectorVariableNameAttribute_set_direction_mF82F757A0C28155744312E689BE39A85D031019C (void);
extern void SavedVariables_get_asset_m2ABC58FB92A5DFFF415AE795F234E2C7C88F442F (void);
extern void SavedVariables_Load_mA346021F739C7CD07F6C86A1A2ABBFE99479A38F (void);
extern void SavedVariables_OnEnterPlayMode_m8348B68A4565CC44CB32D77CC2558A5127095790 (void);
extern void SavedVariables_OnExitPlayMode_m4705B846AABFBC1C43E2DA56F4F888E3B261B805 (void);
extern void SavedVariables_get_initial_m55156EC1B7BC87F6BDDC305AFB9D9DA8454C351F (void);
extern void SavedVariables_get_saved_m68970298C6A788AD12D1B6E6DDA4481748996E46 (void);
extern void SavedVariables_set_saved_mFC0FE359C1E74F9189BD61FD4C1070361597F010 (void);
extern void SavedVariables_get_merged_m2FCEA0A2B856980331A13741C41D97C0B40E4C85 (void);
extern void SavedVariables_set_merged_mFBA9F3625CD79A8454E808C586DA4788E3F9E09D (void);
extern void SavedVariables_SaveDeclarations_m1633E78588DD9F7DB9645B7DD857C79D31FD354B (void);
extern void SavedVariables_FetchSavedDeclarations_mB016AD9358254B042630C551E927A6650EE1C75F (void);
extern void SavedVariables_MergeInitialAndSavedDeclarations_mA2806AAA01CCDA45C049CA46CD4E8140CDCC0C27 (void);
extern void SavedVariables_WarnAndNullifyUnityObjectReferences_mA5149E683327A624F90EEF1ED0C5CFDAA3DCC5A7 (void);
extern void U3CU3Ec__cctor_m24A2FC17A308D9E94CB89C033FBDEA5664137202 (void);
extern void U3CU3Ec__ctor_m4FC04D2A0A4E6BE891C34B7F6DC34ACFEAC10527 (void);
extern void U3CU3Ec_U3COnEnterPlayModeU3Eb__8_0_mC0A93597FC90BC9463C4B338463C80262E6AB277 (void);
extern void U3CU3Ec_U3CMergeInitialAndSavedDeclarationsU3Eb__24_0_mE09A90014F06CE1B8AB7B9CB16C4EE906C7BE7B0 (void);
extern void VariableDeclaration__ctor_m19DDC2BE1A20663AA762A2BED1B1BBF3B6B4A00B (void);
extern void VariableDeclaration_get_name_mAB897FA47D6F56121A391BD082EDE439E7BCA460 (void);
extern void VariableDeclaration_set_name_m1236B1FA852575CA8389C823FDAA23E661BB2E74 (void);
extern void VariableDeclaration_get_value_mCDAD586AE963B276956CB9D0E6BD1E7B9CCBAB9F (void);
extern void VariableDeclaration_set_value_m623EC0A2B1A37614E02A5709DD3A8735A47CF9E0 (void);
extern void VariableDeclarationCollection_GetKeyForItem_m58A5E51177B533651A31A632C8D271156E089B6C (void);
extern void VariableDeclarationCollection_TryGetValue_m8A8DBD09FFA20967E9F5688A22E871733F566B74 (void);
extern void VariableDeclarationCollection__ctor_mF1E29200884D4A1C4B6E1B69003B84549AA38B42 (void);
extern void VariableDeclarations__ctor_m3E547EDC44BB015DC5EA65D7EA885A42B0B207A9 (void);
extern void VariableDeclarations_get_Item_mD67E8FAE86B3840FE88D0ED3A36517A9B14267DC (void);
extern void VariableDeclarations_set_Item_m921139194A666C1B540B69A845B64CF520BE5655 (void);
extern void VariableDeclarations_Set_m09832F170109B10027857DC25E918EB5A986F538 (void);
extern void VariableDeclarations_Get_m0192852426CBE4530276D02A82DA16BF17102F49 (void);
extern void VariableDeclarations_IsDefined_m85E475111442886C76BB3A4E3782B404A5C650B8 (void);
extern void VariableDeclarations_GetEnumerator_m3DBC156D137712C181A630DB5D3322ECCD1E4C77 (void);
extern void VariableDeclarations_System_Collections_IEnumerable_GetEnumerator_m57030E53D229728DAF44ED2E4F0F6788EDC7030D (void);
extern void VariableDeclarations_Unity_VisualScripting_ISpecifiesCloner_get_cloner_mBDD534757C69C2145DDCFD80D43B203A63305A5B (void);
extern void VariableDeclarationsCloner_Handles_m21457B45501D8901B9C4265DAD6ACE452D81E065 (void);
extern void VariableDeclarationsCloner_ConstructClone_m58CD892EBB1B6C0E33A2B7C51266E9BA1B7A57D1 (void);
extern void VariableDeclarationsCloner_FillClone_m1BB86F85D412EE0441AA71D3138B6CB9204AE938 (void);
extern void VariableDeclarationsCloner__ctor_mCF3117747FC69E08A8FFB1A1702B0AD88116916E (void);
extern void VariableDeclarationsCloner__cctor_mA3E82395967E0BF0DF1854B1E8D80A4E52909CA6 (void);
extern void VariablesAsset_get_declarations_m9939ACC84678A6CE414DD508746C4C0DB8BADA4B (void);
extern void VariablesAsset_set_declarations_m31CB793840DA5F2D835086D5A56A5EC36D033C60 (void);
extern void VariablesAsset_ShowData_m2C0E7F5E69407605A5122F7F7F0308333DB59666 (void);
extern void VariablesAsset__ctor_mC5FE118E6F88D54FCE5C1BA8D33BFC9C01FC7357 (void);
extern void VariablesSaver_Awake_m852AAA1C45F0B404F20D4B280D66E26A8479F1B6 (void);
extern void VariablesSaver_OnDestroy_m747AB9394CFFF257C050AF0F256ED90F9A6C1703 (void);
extern void VariablesSaver_OnApplicationQuit_mD4D6F0F37729530E78362A7E5C90E8C6CF5B405D (void);
extern void VariablesSaver_OnApplicationPause_m52633E88C2830E0C922A1DCD93063DBC58CE36A9 (void);
extern void VariablesSaver_get_instance_m68C6C68569AFEB49DD9C687B73B81EEC72219CBC (void);
extern void VariablesSaver_Instantiate_m0BE1F7A1FDC76B91E4563340A5F8116D6E3A1092 (void);
extern void VariablesSaver__ctor_m0D71CC9CC4C45098D340EC3477BB7E696A9F0F4A (void);
extern void fsArrayConverter_CanProcess_mC812F63BA9DA4087CC82E0E869B5F7390FAC81C1 (void);
extern void fsArrayConverter_RequestCycleSupport_m0A7F56F448ED67FB95EE6163FD6A0E5F20992678 (void);
extern void fsArrayConverter_RequestInheritanceSupport_mC81F939D38E5BD04E2E75E8C30F6B5634A86167C (void);
extern void fsArrayConverter_TrySerialize_m06455924AD13F62F26F5AC4A8857AC3C6C920925 (void);
extern void fsArrayConverter_TryDeserialize_m1ED67A6A2F6AE5A630F8BDB4EABC3E748459D34D (void);
extern void fsArrayConverter_CreateInstance_m69271B347C7C9E69AC7E1544904B8D5821321EED (void);
extern void fsArrayConverter__ctor_mF3DB4719DA4C53D0645C14406F15106CBA69F326 (void);
extern void fsDateConverter_get_DateTimeFormatString_m7060267388902A4A213718EBDB898926BB64CBA4 (void);
extern void fsDateConverter_CanProcess_mC55064B69B219D9686EBB81918F714C012A40ECF (void);
extern void fsDateConverter_TrySerialize_m42983F2FB45C8C8B9D2CD66ACAB58790659AB86C (void);
extern void fsDateConverter_TryDeserialize_m2FDEC499002F64CD557C7BC26F08400AB9086941 (void);
extern void fsDateConverter__ctor_mE7B2A85671B95BEE2B13D8AB2F3A443EBF5E83CC (void);
extern void fsDictionaryConverter_CanProcess_m0F2C9585AB012FAEF9F2AEA0E3A81CB4FAF1CC08 (void);
extern void fsDictionaryConverter_CreateInstance_mD873B9FC931CBBE18F68222E1E02E8469BA015F4 (void);
extern void fsDictionaryConverter_TryDeserialize_mCE4A4465D0860BBB22C2D53F7E8C5DAEAB537952 (void);
extern void fsDictionaryConverter_TrySerialize_m7670C41256D3265FA8FB68CA82652A6057E6F1C6 (void);
extern void fsDictionaryConverter_AddItemToDictionary_mBEA790A8C84CA37141202830ED648FAB52BC446D (void);
extern void fsDictionaryConverter_GetKeyValueTypes_mDB5DAA4B2785D2BADBA9CFC94FF2895FDDAD64CE (void);
extern void fsDictionaryConverter__ctor_m668F4BE65E6467436373F586FEAD37F472A55ACA (void);
extern void fsEnumConverter_CanProcess_mB7E95AEBA9B47FC761C98C350E92838DCE26A634 (void);
extern void fsEnumConverter_RequestCycleSupport_m312A3C38F2910A267A44D687D833D4EDD9EB6D91 (void);
extern void fsEnumConverter_RequestInheritanceSupport_m61A12023AEDA7FD27C68AC605EB5FCD9C4EF696C (void);
extern void fsEnumConverter_CreateInstance_m83AC52BB50846317BB08BF009F18CDD3F6DE5561 (void);
extern void fsEnumConverter_TrySerialize_m9EE5BE8BF13D15ED7CCCED9D50A9B506BC30EE50 (void);
extern void fsEnumConverter_TryDeserialize_mC4B539334A518C125E7AC968D21086556B2B25B4 (void);
extern void fsEnumConverter__ctor_m60A7A37304B4B655B135261505092177EB5271CF (void);
extern void U3CU3Ec__cctor_m6707E13BA8E1A8DC4E5D8353FE5ED3B9C7DBA024 (void);
extern void U3CU3Ec__ctor_m15D4E64BD9ABA845A5F475B1A76567EE30FB8BDD (void);
extern void U3CU3Ec_U3CTryDeserializeU3Eb__5_0_m3BEA44FB419581664656FC13ED434B30D9890898 (void);
extern void U3CU3Ec_U3CTryDeserializeU3Eb__5_1_m019B199D427FC38370BE36AE8A8F1EB0A5E60B8A (void);
extern void U3CU3Ec_U3CTryDeserializeU3Eb__5_2_m326BBED045D120714F928CC40F53B443CD235562 (void);
extern void U3CU3Ec__DisplayClass5_0__ctor_m8E355725F37DCF563CA859BD8ADD2CD2C71E5C87 (void);
extern void U3CU3Ec__DisplayClass5_0_U3CTryDeserializeU3Eb__3_m6820BCBED4F823DFBFB958884E53012470332A11 (void);
extern void fsForwardConverter__ctor_mB658EBCDE1BF8CDAB66C52D3EAC9B8A1521BA45C (void);
extern void fsForwardConverter_CanProcess_mE762E41568C5E18DBC4904E92F67D12C37830446 (void);
extern void fsForwardConverter_GetProperty_m2B901658700E58C0FD7D005FC00D9CC270686F65 (void);
extern void fsForwardConverter_TrySerialize_m11974CAAD5374B236CAF2C0467F7B29E80A24976 (void);
extern void fsForwardConverter_TryDeserialize_mDE2FE372CF24AE333C56CDCC52EBFCB271919188 (void);
extern void fsForwardConverter_CreateInstance_m72B8E32F657BFD6AFC09A14E614C43284D389DF2 (void);
extern void fsGuidConverter_CanProcess_m9BDF04BB4DD6D4B3E58B5D15E0452618BB4DCA29 (void);
extern void fsGuidConverter_RequestCycleSupport_m3DBFC29137F937A7813E0B15BFAD3DE0B91D77A7 (void);
extern void fsGuidConverter_RequestInheritanceSupport_mC309A9B8F6D17F76644AD2D93C3BB2D3739164ED (void);
extern void fsGuidConverter_TrySerialize_m12F5DB4BF4E420D8110F75B863EB4E125E84D686 (void);
extern void fsGuidConverter_TryDeserialize_m42EFB6BFDFF52BB1DE9F7350A836A92A5AD99788 (void);
extern void fsGuidConverter_CreateInstance_mB9260EDF8704C0F3869417571FB8E82F071261C5 (void);
extern void fsGuidConverter__ctor_m55052DA5223263915D2BC2AEF25B124274525728 (void);
extern void fsIEnumerableConverter_CanProcess_m6C7D24E9CF92E0962C6B5E366934A8914F65F21F (void);
extern void fsIEnumerableConverter_CreateInstance_m64AAF627F2DCC6302CCA1BF54333EB9B84BCE3F1 (void);
extern void fsIEnumerableConverter_TrySerialize_m494915090B70D63C3FED5AE22044BFBCF90FB253 (void);
extern void fsIEnumerableConverter_IsStack_m71CB1418705BAD0698E1DF662B2F04EEF5CC3CED (void);
extern void fsIEnumerableConverter_TryDeserialize_m9EE147DFCA4E87D72745310A09C008D74873FE9D (void);
extern void fsIEnumerableConverter_HintSize_mC8612A00BAAF5DA7B2CECDD4E302200F99BAEF43 (void);
extern void fsIEnumerableConverter_GetElementType_m17EA40AC5698BF0A99FA0F5ED94EB6095D217582 (void);
extern void fsIEnumerableConverter_TryClear_mD876D5946943670A3A0AE2F4F588064A48C36D06 (void);
extern void fsIEnumerableConverter_GetAddMethod_m5C9E232A6434AD298218EB1C9F4457684F2CF9B4 (void);
extern void fsIEnumerableConverter__ctor_m5543F512D94FBAB49D28753CF3912F4AE5798E9D (void);
extern void fsKeyValuePairConverter_CanProcess_mA592307887176EC33A92C1E4A9E3C0CAFC19B138 (void);
extern void fsKeyValuePairConverter_RequestCycleSupport_m19CC77ECC3D2F75FD3A85EE8317D74BA6440CCDD (void);
extern void fsKeyValuePairConverter_RequestInheritanceSupport_m5CCE9E420FBC8EFB080B1C96BCEF424B3911129C (void);
extern void fsKeyValuePairConverter_TryDeserialize_m2C5543FF62C82E68164B87CFBFDBCEDF3ECFEFE9 (void);
extern void fsKeyValuePairConverter_TrySerialize_mCEAED5E05508FA18654A11735CA8467772BBFE98 (void);
extern void fsKeyValuePairConverter__ctor_mB2D131F5758DA37234271F8DF86CF66644030ACD (void);
extern void fsNullableConverter_CanProcess_m1A706E340440C15213F125EB106628B06C03F455 (void);
extern void fsNullableConverter_TrySerialize_m39EAE7FDE4F6A1D4120A5E9FB85C7F5C4654B6B8 (void);
extern void fsNullableConverter_TryDeserialize_mBCA7A072A80F873BEF6EE840DEA7EC096A768633 (void);
extern void fsNullableConverter_CreateInstance_mF0AA1385488BDD894E97916472CE67CF0F0AB418 (void);
extern void fsNullableConverter__ctor_m6B019ABC41E275FC29C0C002C25E61AD303DB349 (void);
extern void fsPrimitiveConverter_CanProcess_m26DD96F0878FD3ECEFF0EDB17D6E0112CE533CCE (void);
extern void fsPrimitiveConverter_RequestCycleSupport_m64A8B1EED78BB79C1E5BB5B4C7FCA836D49E8CBF (void);
extern void fsPrimitiveConverter_RequestInheritanceSupport_m867B4302EE1E04FD668A159410CB0155F34C3ECF (void);
extern void fsPrimitiveConverter_TrySerialize_m825861168AFAB6D96AFEC21203DC967EF6417459 (void);
extern void fsPrimitiveConverter_TryDeserialize_mB7E511360F5E0EB46232E90D0D70FC62A29DAE9F (void);
extern void fsPrimitiveConverter_UseBool_mFAF124DD783CA5CC22568BF064CEFCF0A5C23D49 (void);
extern void fsPrimitiveConverter_UseInt64_m33413FBA83B3FA7DC50ABA4678D8123385D1CEF6 (void);
extern void fsPrimitiveConverter_UseDouble_m2D0C1A06EFFDBC9DD1E2294D8083901E827164AE (void);
extern void fsPrimitiveConverter_UseString_m6AC59DF7D2E4A2C1B8C29CED600C44574ECF3CFB (void);
extern void fsPrimitiveConverter__ctor_m09F9AAC8DDBA0AD9D03FEAB2E413EAE812073340 (void);
extern void fsReflectedConverter_CanProcess_mBF06950794A6274696482F9EC4F9F9FB888ADA37 (void);
extern void fsReflectedConverter_TrySerialize_m8E6B54451736AE514D422B3FECD54006D0BA3133 (void);
extern void fsReflectedConverter_TryDeserialize_m513AA91805BA657A339C614A96C9C8182D98869D (void);
extern void fsReflectedConverter_CreateInstance_m03A9A5B8D678763FDE97EC8E029D332F41E1E949 (void);
extern void fsReflectedConverter__ctor_mC1B10DE2E5B49784B0FC658CAA28814F555647A6 (void);
extern void fsTypeConverter_CanProcess_m91B9CE689C4CCAE928291938CE940AA165F4A42D (void);
extern void fsTypeConverter_RequestCycleSupport_m39201AC284A5288BD9C0B7414019CF0450F53C09 (void);
extern void fsTypeConverter_RequestInheritanceSupport_m770B87C422596DF9CD79FC17464C0D083CDCB2A1 (void);
extern void fsTypeConverter_TrySerialize_m5C85B08391033D1A3ABD90DED6660C90CEEFE622 (void);
extern void fsTypeConverter_TryDeserialize_m11D4FC25E9920438E8CDFE40FC8EDF0CB9130485 (void);
extern void fsTypeConverter_CreateInstance_m1B47139441FD91D866882C8603F28938B869D0A5 (void);
extern void fsTypeConverter__ctor_mB3785D775EC38A17971F369D089F39FA835D087B (void);
extern void fsWeakReferenceConverter_CanProcess_mFAA2163DFDD567276F9DCB1FC44C5AC0CD4618DD (void);
extern void fsWeakReferenceConverter_RequestCycleSupport_m8376F8CD4E3E179ABA1401B75BB31E5CFDF61E0C (void);
extern void fsWeakReferenceConverter_RequestInheritanceSupport_m4EE0E35271996B23F70D17ED61F1B0549CA664ED (void);
extern void fsWeakReferenceConverter_TrySerialize_mB71B83716AA3FA2CADB4F34836055A7B7314F279 (void);
extern void fsWeakReferenceConverter_TryDeserialize_m2B488EEB987C1DB9E969066BE54FDA76104E8286 (void);
extern void fsWeakReferenceConverter_CreateInstance_mAE847039874CB4CE7B2D67F72BF1088EAEBA790E (void);
extern void fsWeakReferenceConverter__ctor_m961A93BBF5A2B9E276E52E1764917A8A3D48469D (void);
extern void fsConverterRegistrar__cctor_m06F8018AD5F3307316F0DC9162EBB268ABA6C868 (void);
extern void fsAotCompilationManager_AddAotCompilation_mC52615432A98FF68630197F195080908FB443A19 (void);
extern void fsAotCompilationManager__cctor_mFF57869A10E0688492F69111655BEEF3A4AB22EB (void);
extern void fsBaseConverter_CreateInstance_m560B7E27C98998D4E16AB5223E6D14792C7EE213 (void);
extern void fsBaseConverter_RequestCycleSupport_mB28BD5D386B27FE5AD8C7C823569FEB489AAB898 (void);
extern void fsBaseConverter_RequestInheritanceSupport_m847B97D52071D2C8AB8FCEB0A2B79E233083B720 (void);
extern void fsBaseConverter_FailExpectedType_m7016D6FE93F844E574F232EFF61942E2E07015B4 (void);
extern void fsBaseConverter_CheckType_m14C4D0CAF424DF9F14D6365B5E5681C43FA82F32 (void);
extern void fsBaseConverter_CheckKey_m82204341C573CEB47CC62BD43C0522CFE648F3E1 (void);
extern void fsBaseConverter_CheckKey_mD4F876A6B4BE2208484A4917AD3E3DAC9DE3620A (void);
extern void fsBaseConverter__ctor_mDC69FC94FF8A58F044B12046BBA96F994430A2FC (void);
extern void U3CU3Ec__cctor_m8BC66C7D54482B015159B5F437EB8C26763EC106 (void);
extern void U3CU3Ec__ctor_mE8CC346CCBC60E7018B5B4BE957B2330AB84D54C (void);
extern void U3CU3Ec_U3CFailExpectedTypeU3Eb__6_0_m537F189F92A9F7D906D2EDD9659143C639C621A3 (void);
extern void fsGlobalConfig__cctor_m7706ACB33CD10F7B39B7B53B802F7AE7B6CAC775 (void);
extern void fsConfig__ctor_mCB5CF40444C2E640B4141CFE2B8700ADCC08E839 (void);
extern void U3CU3Ec__cctor_m95D819656FB95982AD3DD0AD070FA011C087C8BB (void);
extern void U3CU3Ec__ctor_mC79C18C2451830A98579F11E7C7BBFB4CB5E8FAF (void);
extern void U3CU3Ec_U3C_ctorU3Eb__10_0_mBD487886165A2FB0F5AB8F396C98A27C9D1B5DAE (void);
extern void fsContext__ctor_m48FB2999693498F2BCE7A978A76773DF86E290DB (void);
extern void fsConverter__ctor_m3C3DE66DA4A3FA50ED9A7D0F10EF70BB0D5FEA26 (void);
extern void fsData_ToString_mB9B72A2286B2AADA8582221CDE13712F3F3C6C9F (void);
extern void fsData__ctor_mA4E52DCE50BB43C4429E77519CDBD113343180DC (void);
extern void fsData__ctor_m740FD2D0D7D78B72181DFC0216FED95818893268 (void);
extern void fsData__ctor_m9C5AE8C2391D7773629D904624B36357DB3F7A24 (void);
extern void fsData__ctor_mEF553618B93DB72D7816FD84740EC37A942F129C (void);
extern void fsData__ctor_m144DD5B09AD525C0F94B2E1750A4256495266F66 (void);
extern void fsData__ctor_mCF4D73BFD9271596000ACC3E17988E3492236781 (void);
extern void fsData__ctor_mDBA39D40BC739FD480F7C2DA53570109CA466B39 (void);
extern void fsData_CreateDictionary_m90ED2CCE0AE0A70F6E6D91181080DE89DCA7C53D (void);
extern void fsData_CreateList_m99EDBD341F412886FD95A18B4AF1E1B763C10E95 (void);
extern void fsData_BecomeDictionary_m1375A313DDB0E7DBFFB609A807DA2EE916AF644C (void);
extern void fsData_Clone_mEC41F9EA408E94F8D85EFFF6EADD9DF1B7CA54EF (void);
extern void fsData_get_Type_mCDB1F38635730E10B61C3D62B8D64C8F5AB845CE (void);
extern void fsData_get_IsNull_m2D1318B1C77BD8D5C5C87D52400D1B9239815A2C (void);
extern void fsData_get_IsDouble_m4895B3C81A738E2907493684593AE745943B6936 (void);
extern void fsData_get_IsInt64_m53E266DA7DED5DBC5CA5CE79F7BB9C25237B7E42 (void);
extern void fsData_get_IsBool_m5C55D9E43C2D8B6840411DF6C192113E60050367 (void);
extern void fsData_get_IsString_m1B40F32297A731D5A4EB637438B95632453C84B8 (void);
extern void fsData_get_IsDictionary_m323625E7633DF9747CF9FAF44C0E5E9BBA4671E4 (void);
extern void fsData_get_IsList_mE4BFDA69E183AB45F4BA89D3F95B630341CF36A2 (void);
extern void fsData_get_AsDouble_m5CD93E5F7A8DBE8646C880F0F561CD91613F4D2E (void);
extern void fsData_get_AsInt64_m1C652F5AF0A3C8EB84183204CF3AE983AEC3441E (void);
extern void fsData_get_AsBool_m34337669439083657DDAD711DAFD463858A2FA15 (void);
extern void fsData_get_AsString_mCF0FBBF9D300BCAC9E0F1C7B6C9DAA4B8AFB9570 (void);
extern void fsData_get_AsDictionary_m51779E71BBC994A7F5036BFB43F61B28C0817D86 (void);
extern void fsData_get_AsList_m0B2AF972F10AE6F9A8EA9157BC27FC0316BB33EA (void);
extern void fsData_Equals_m28C1452B6B9094F2F8650100539701AE322BEBFE (void);
extern void fsData_Equals_m693932B7DA39DBC63C1B528A9530AD5B51009E9C (void);
extern void fsData_op_Equality_mD14A5DBF98FAA29F99C352197F711324CC4A0107 (void);
extern void fsData_op_Inequality_m678ABE3BFD1DA4C7FFE5236BF1C842B3901A4EF6 (void);
extern void fsData_GetHashCode_m0527A5668429A615A6812061DFA50E592E1F4F82 (void);
extern void fsData__cctor_mA6352A2037DD0EDF62944123ED2B03E63225C67D (void);
extern void fsDirectConverter__ctor_mCE6F7898DA2A3FBE101F5939D06A809E3164ABE1 (void);
extern void fsMissingVersionConstructorException__ctor_m46EA05FE262E8C98465A921D2B2EB6035ED3FEFB (void);
extern void fsDuplicateVersionNameException__ctor_m0CA794E21F56EBADEED2971C6E9DC603CAA0AF51 (void);
extern void fsSerializationCallbackProcessor_CanProcess_mC382EF1EA240B657B95653DF7A345748648B7D22 (void);
extern void fsSerializationCallbackProcessor_OnBeforeSerialize_mD345A5E296EAB4995B767549EC297AE842C98EA9 (void);
extern void fsSerializationCallbackProcessor_OnAfterSerialize_m04ECE444A656E46901FDFA8A4A50CB88B58829A3 (void);
extern void fsSerializationCallbackProcessor_OnBeforeDeserializeAfterInstanceCreation_mCB42E40ABF7205C44D09BFAEBF79DD7ED2E8BB62 (void);
extern void fsSerializationCallbackProcessor_OnAfterDeserialize_mBA672AADF2BE57D975BA57E3ECBE67CF53254C9C (void);
extern void fsSerializationCallbackProcessor__ctor_mA53988890D8BB06D89990CABF451036BFB686968 (void);
extern void fsSerializationCallbackReceiverProcessor_CanProcess_m70B299D9E2829CAD22276102D7A83EA98EC9ADCE (void);
extern void fsSerializationCallbackReceiverProcessor_OnBeforeSerialize_m25ABF450D793FD0619FF1F7625E1CFB5144DEFB8 (void);
extern void fsSerializationCallbackReceiverProcessor_OnAfterDeserialize_mFF0DAEAE073EDE4E227E46A97DB9C6F93A3EAC1E (void);
extern void fsSerializationCallbackReceiverProcessor__ctor_m8F7AD77AE53D0C041389A60F0787447745505921 (void);
extern void fsJsonParser__ctor_mDF3573B996535708BA931F67A6831FA2082EBDDD (void);
extern void fsJsonParser_MakeFailure_m954A9022A21F0A861B51A0D990B4119B22CD3F79 (void);
extern void fsJsonParser_TryMoveNext_mEA865DF9A93335D212815AFF263D9B24B85BBB57 (void);
extern void fsJsonParser_HasValue_m1CA829DFE012BE97368B282E74E1726358F06E07 (void);
extern void fsJsonParser_HasValue_mF7A6023B0B43CCE0E45CD2EB994E0F73F168336B (void);
extern void fsJsonParser_Character_m65CBDF5471FF5B35581B7EFEAE6EC6372E04586B (void);
extern void fsJsonParser_Character_mDDE622FD95D90CE0D28BCA6BC3BF26E751C8EA86 (void);
extern void fsJsonParser_SkipSpace_mD9A6A4A11F3A5BB3B1F04637B1F271888001A7AD (void);
extern void fsJsonParser_TryParseExact_m505485875C284E11FA456B622D67DAB70E21D887 (void);
extern void fsJsonParser_TryParseTrue_m24EC3E78B57CC6F2D509AA248E77EE2A85E233C0 (void);
extern void fsJsonParser_TryParseFalse_m596BC9F804F6B9401B40EDB5237AD29B4D3D80A5 (void);
extern void fsJsonParser_TryParseNull_m843F34234DB664D0543CABF0E220F750ED46064D (void);
extern void fsJsonParser_IsSeparator_mABE1E2ECF0F25F34AD7DD13F88B79126101BA6E0 (void);
extern void fsJsonParser_TryParseNumber_m2E21CD4F0C7523B4D27DB12562D2246F245978EB (void);
extern void fsJsonParser_TryParseString_m4257104805D84B9015D377222C7101E9E96600A4 (void);
extern void fsJsonParser_TryParseArray_mB56A5AF71B2A7D1561B823E1F377754D5FE8B43A (void);
extern void fsJsonParser_TryParseObject_m7B7190173328FF691F900E784239160AC4AEA6A4 (void);
extern void fsJsonParser_RunParse_m0A394E05ACEFF4E6F796992D5C9196AB233A3025 (void);
extern void fsJsonParser_Parse_m8C6D52CF715A543C1CC24E79221A3E13EEBF3AC1 (void);
extern void fsJsonParser_Parse_m8BE8A63CD24E3C0D05A65A977FF70AC8E1D34336 (void);
extern void fsJsonParser_IsHex_m48F2F8DDD813FC39FCF6977E477217C949B99A57 (void);
extern void fsJsonParser_ParseSingleChar_mD88F637A84EA790C3F29F23648DBF64DA14DB323 (void);
extern void fsJsonParser_ParseUnicode_m45633DD01111D8B14D037163008AF1D036AB9B70 (void);
extern void fsJsonParser_TryUnescapeChar_mE8EB2102F384B53B9847BA88CCE4DE6CCCB91456 (void);
extern void fsJsonPrinter_InsertSpacing_mB7AF68DA90DB29D402F6C71992131038B1F55BDF (void);
extern void fsJsonPrinter_EscapeString_m88827B05B5B4530043982F18C3200A176A94E214 (void);
extern void fsJsonPrinter_BuildCompressedString_m19AEE93D21E751B171D191701D780E4A1BAEC144 (void);
extern void fsJsonPrinter_BuildPrettyString_m151C19973019F8D728104F27872EA61548EFC0CB (void);
extern void fsJsonPrinter_PrettyJson_m610FD801940DC409B059808A5E954ABAF62CAB8E (void);
extern void fsJsonPrinter_CompressedJson_mAD916E32C1E828444E79CE2D32E74BD7892FC767 (void);
extern void fsJsonPrinter_ConvertDoubleToString_m3A8E7CD06EDB9E9ED63721E3E8A35D30BBD1AC71 (void);
extern void fsObjectAttribute__ctor_mABB40D348C9AFA8BB3E34A31F2F6DE7620519060 (void);
extern void fsObjectProcessor_CanProcess_m86183AE7FC4638153EFDF8ADD5FC0C8702A54410 (void);
extern void fsObjectProcessor_OnBeforeSerialize_mE084D698B97C142C4CA8A7CF9581C49D00A4BF59 (void);
extern void fsObjectProcessor_OnAfterSerialize_mFC2EE7C13211D4E1F90AEDD9966276C31FDBA040 (void);
extern void fsObjectProcessor_OnBeforeDeserialize_m6E16DBE8D82C98DA9F2D23674DC6CE04A02B0639 (void);
extern void fsObjectProcessor_OnBeforeDeserializeAfterInstanceCreation_m60D055099B48A9042D7AC51AAF014EB5D27B80D3 (void);
extern void fsObjectProcessor_OnAfterDeserialize_m5103A8FA0718C709CC309A39A5E8FE9C30812231 (void);
extern void fsObjectProcessor__ctor_mCA89DB5E44BFCD8B8C5C57A5CBEB1283EB9B4426 (void);
extern void fsResult_AddMessage_mC4B690BC3884FF2D235733FD65081C163B4DDD55 (void);
extern void fsResult_AddMessages_m37B84549F6D482E5C1D8E8761DE4C006D7AB4B9D (void);
extern void fsResult_Merge_mB5B8E8B4BD5B6086B6DDFAF478640D9DA56EF0C1 (void);
extern void fsResult_Warn_m7F899FA4134674AB4FC072A865DF5EEBCBD4AEAD (void);
extern void fsResult_Fail_m3315594F29830C02FD560F7401D311F99DA241CD (void);
extern void fsResult_op_Addition_mA94A4AD68668E539DEFE1255DC72B9D11A6DE41C (void);
extern void fsResult_get_Failed_m1398C627A72E75F5C7F8DB1A7C14E5B3271FF1FB (void);
extern void fsResult_get_Succeeded_m01E08C3B731D94618B867B7B55DCC0061CD11769 (void);
extern void fsResult_get_HasWarnings_m030A020FD6B48D0E8E2867D2428135EB771064CE (void);
extern void fsResult_AssertSuccess_m7077B79DD80747B32B66ADCDF49243575800FA12 (void);
extern void fsResult_get_AsException_m6D92E907B2E0815691BD5639138FB4E4C578EA0B (void);
extern void fsResult_get_RawMessages_m0B25A624CB4B2F03DF6FC6077284AF92CFFC4683 (void);
extern void fsResult_get_FormattedMessages_mCF960860BF1F178EDFCA80D1196B2F69096144B7 (void);
extern void fsResult__cctor_m5AD662C6FC9BB3FA9701789A9AE2C051D5AE1A44 (void);
extern void fsSerializer__ctor_m2CF241A6E8D255269BC2694F7B87B8BB7381C739 (void);
extern void fsSerializer_RemapAbstractStorageTypeToDefaultType_mDAACC633FEB7E4E36783EB6617A943521305150A (void);
extern void fsSerializer_SetDefaultStorageType_m91D7A9724B7D2280BA9633B5159887B59A4DD01E (void);
extern void fsSerializer_GetProcessors_mB240A96D2D312CE91D3E14DD67CE734BBBE10217 (void);
extern void fsSerializer_AddConverter_m7EE7A34BDDD2065481BEB0457C9572270588ED59 (void);
extern void fsSerializer_GetConverter_m3FBA1F6CC05EF20F9F31F1CEE65DCABB40218495 (void);
extern void fsSerializer_TrySerialize_mADDE6667080A2E2C69A54662C47111A08A7EC006 (void);
extern void fsSerializer_TrySerialize_m2E029A68D43C8F4783B5912AB8004658E41AB84B (void);
extern void fsSerializer_InternalSerialize_1_ProcessCycles_m81B0B13FD1CA775E3686E91C753424BF2717D9AF (void);
extern void fsSerializer_InternalSerialize_2_Inheritance_mDDDBA4731B6545294441CB77CB1CEB115C5A2037 (void);
extern void fsSerializer_InternalSerialize_3_ProcessVersioning_m59970A3730FC476B186B8D9A9E258C4E3CB2C338 (void);
extern void fsSerializer_InternalSerialize_4_Converter_m0695101440E5D807387BFD82D7B5BD841BC22456 (void);
extern void fsSerializer_TryDeserialize_mBBA19D9240A719A58C3DA23F2D8D4C538C5C81F2 (void);
extern void fsSerializer_TryDeserialize_m675340784F47BA9FF50C193F53A3BAAA1D18D2B9 (void);
extern void fsSerializer_InternalDeserialize_1_CycleReference_m1D9180F91CE5B8505373E3301E63AB1FBAB9FEE4 (void);
extern void fsSerializer_InternalDeserialize_2_Version_m41956C872601212418C1DAE44AE54FA43265E8A9 (void);
extern void fsSerializer_InternalDeserialize_3_Inheritance_m43B49A0E8363275937AC5DC9284B87847142886F (void);
extern void fsSerializer_InternalDeserialize_4_Cycles_m913C1277065AD471EB7F1F22DEACDE4DE1645521 (void);
extern void fsSerializer_InternalDeserialize_5_Converter_m30EBD20C91E165C4B9BC87FCB1467B9BD4EE8DF6 (void);
extern void fsSerializer_GetDataType_m064ECD9CDD126B0705BAB7BE0F6A24E0AC1DE13E (void);
extern void fsSerializer_EnsureDictionary_m852412879A44895491BB671AB6D9A29CCD1E7760 (void);
extern void fsSerializer__cctor_m8FA92415657E595F6071011E2E7E81A74BA8AA49 (void);
extern void fsSerializer_IsReservedKeyword_m1A656E8EDA5598C00BC12E84623074A18FEF03FE (void);
extern void fsSerializer_IsObjectReference_m42B2DF9FAEA83929849E6F0982EF9E21CDDF8F79 (void);
extern void fsSerializer_IsObjectDefinition_m2D0919684B6C9C269F1B1A803F3113D786D10272 (void);
extern void fsSerializer_IsVersioned_mE7064A56C3886F675F764EA02DEDF14627EB879A (void);
extern void fsSerializer_IsTypeSpecified_mBD85E42C5EFB8553EF83EB0AEA55AA6898DD1A15 (void);
extern void fsSerializer_IsWrappedData_m177A55FD22E57532B934E70669BC15DF449C8F3D (void);
extern void fsSerializer_IsVisualScriptingUnit_mF307CA12573997A8C69C2D06ABA32BBA8F41A6C1 (void);
extern void fsSerializer_ConvertLegacyData_mA961F5B0DCE7D15FB7C887586CF793D96B820830 (void);
extern void fsSerializer_Invoke_OnBeforeSerialize_m24BD9C61E67A5AC371884D793C2F3B3C7CFF095C (void);
extern void fsSerializer_Invoke_OnAfterSerialize_m6D2CA5F43C91291C965239F884CB603D89C30293 (void);
extern void fsSerializer_Invoke_OnBeforeDeserialize_m13D482A4E6E930C3117C0A6575BF87C14CDBE3E3 (void);
extern void fsSerializer_Invoke_OnBeforeDeserializeAfterInstanceCreation_mDE398ACCB3A5DA6D6BDE4692F1F748D20AF3CA52 (void);
extern void fsSerializer_Invoke_OnAfterDeserialize_m2B62287C736CCBCA14C3A2119A32490A05036974 (void);
extern void fsLazyCycleDefinitionWriter_WriteDefinition_mE5A307D8E186FF9645CA85A979D7B781C1A8C665 (void);
extern void fsLazyCycleDefinitionWriter_WriteReference_mACBE88BD37EDA06D992F9FF0A30B0D3BBE33FD74 (void);
extern void fsLazyCycleDefinitionWriter_Clear_mF81302B0538AD1FBA244D919EF7429B935B1157C (void);
extern void fsLazyCycleDefinitionWriter__ctor_m88404815CC83159CB4B9581C0737F8FE44847A38 (void);
extern void fsMetaProperty__ctor_m8EAD810AB5CC9482CE78ACDAA6D7DCC2680022A6 (void);
extern void fsMetaProperty__ctor_m6C23AFA33EA55557279A069D19DC87FCCEBBBAD1 (void);
extern void fsMetaProperty_get_StorageType_m44BED9C0B89B0A58FF1B29E69582E7B5FF75414E (void);
extern void fsMetaProperty_set_StorageType_mBC9EEB04E34352346F970CB36245E0CAC10F29E6 (void);
extern void fsMetaProperty_get_OverrideConverterType_mED201398F793429A572BAC96BBCFA8334F63C6A3 (void);
extern void fsMetaProperty_set_OverrideConverterType_m37267DED2A75405AF6B2E4FF77802820C8F8B04B (void);
extern void fsMetaProperty_get_CanRead_mA2A7D389C2D41C426B303F6A269403879F17964D (void);
extern void fsMetaProperty_set_CanRead_m29A21E1EC23A8330A771140563638EBE23C01C73 (void);
extern void fsMetaProperty_get_CanWrite_m2BD75237BFBF0A4162BEDF112CF33C6C748EF3D6 (void);
extern void fsMetaProperty_set_CanWrite_mCE48F80DD621B06344EE627E0E71B988A63BC694 (void);
extern void fsMetaProperty_get_JsonName_mA58DF433A59CBE9214939853C7F4E9D4B838A652 (void);
extern void fsMetaProperty_set_JsonName_mF0BBA2CA8267D1CCEB6AD349EC15006B2FC68ED4 (void);
extern void fsMetaProperty_get_MemberName_mED2E167648A8A76B9C0FD73A6405012D82D9EDBE (void);
extern void fsMetaProperty_set_MemberName_m910365017A946E12F897AA268E2C54E92D8B0492 (void);
extern void fsMetaProperty_get_IsPublic_m30FEC58C34CBDB5F59B85174A8B4F1CB9D7FFE7D (void);
extern void fsMetaProperty_set_IsPublic_mDDC0D9F1D8C16C97D5A967A66B90C96CF00EFDBA (void);
extern void fsMetaProperty_get_IsReadOnly_m0C3E3C1607BF2EE1BFE02073A743AD580EB0EB5A (void);
extern void fsMetaProperty_set_IsReadOnly_m81F1BE9C4A906FD3A846B032068D8CF2E7C69ED4 (void);
extern void fsMetaProperty_CommonInitialize_mED4CA3EF3000E44A9A61F6FE6F487BAD44B5AE5A (void);
extern void fsMetaProperty_Write_mEA2E2F7E5E785063984023ABD51657C856E60783 (void);
extern void fsMetaProperty_Read_mE5F0AE85092593DE88AEDA717359310E9E06C046 (void);
extern void fsMetaType__ctor_mC6683E69F4CE1C8FE4BC514BD4ED19E33CE35CF3 (void);
extern void fsMetaType_get_Properties_m54618035A54E08C924EB7E8781AA55C1D19F950A (void);
extern void fsMetaType_set_Properties_m75C4CFDD1D8A0844B8E8E61B1028BB013A011A9E (void);
extern void fsMetaType_get_HasDefaultConstructor_m6AFFF40736CFD48C95681AF3228409840BB4F81E (void);
extern void fsMetaType_EmitAotData_m567914383E93A38ADA1F4D3AE645632CC6982AC4 (void);
extern void fsMetaType_CreateInstance_m29B05EF9602FCAE885BBC789C04C5EA32D60AE72 (void);
extern void fsMetaType_Get_m5622F5FF2787C35C795789C1373F6013DCAB4363 (void);
extern void fsMetaType_CollectProperties_m4ED60C5E38D1D5EC03A3A61803D7F60E21ED2D3C (void);
extern void fsMetaType_IsAutoProperty_mC28CBE4BA104DE04A8D082D781C962C39287CC28 (void);
extern void fsMetaType_CanSerializeProperty_mAD3B5D6C4B122AEBA0895E510A69BFDDA00427B2 (void);
extern void fsMetaType_CanSerializeField_mD6450E719027D7DC41CA949858689F8B2B87682E (void);
extern void fsMetaType__cctor_mE583D07A7F203890BAF8361A65C8885B0155B781 (void);
extern void U3CU3Ec__DisplayClass16_0__ctor_m538342D591F27E0886D3D9F7969DB43E9F1AC3A0 (void);
extern void U3CU3Ec__DisplayClass16_0_U3CCollectPropertiesU3Eb__0_mBDC7709CE09425F240C42147C3BB2FDB7E76FAF5 (void);
extern void U3CU3Ec__DisplayClass16_0_U3CCollectPropertiesU3Eb__1_m086012131CD06B409756038400BF39327BF9F233 (void);
extern void U3CU3Ec__DisplayClass16_0_U3CCollectPropertiesU3Eb__2_mCA3D8AC6B0001C61241D0164C6089EE68DF7F245 (void);
extern void U3CU3Ec__DisplayClass18_0__ctor_m409998F2ADDDE7B61682E1940C31EB545A5333A9 (void);
extern void U3CU3Ec__DisplayClass18_0_U3CCanSerializePropertyU3Eb__0_m9DF628356CB27A717F31CD102E1D609070B00ABB (void);
extern void U3CU3Ec__DisplayClass19_0__ctor_m577E5745A5BD61B689CD9DF9A336D664BDE608AA (void);
extern void U3CU3Ec__DisplayClass19_0_U3CCanSerializeFieldU3Eb__0_mFA28917E7AF68FAD4A6A425DF445DD5AC3115D07 (void);
extern void fsReflectionUtility_GetInterface_mECB1C271163A4B35F1CB6F2976818AF0A50AD905 (void);
extern void fsCyclicReferenceManager_Enter_m28863BBFD4BDEF23F1DCBC9D2FC71D797EB2FDB4 (void);
extern void fsCyclicReferenceManager_Exit_m91D1DB5745A082CE50E57ABB26B16E0F3661120F (void);
extern void fsCyclicReferenceManager_GetReferenceObject_mA7330D4D27E050EE92EC6F5333767307B8E1B7C4 (void);
extern void fsCyclicReferenceManager_AddReferenceWithId_mEC4A2DE046EF97033AE58B86AA1AADBA88A170C5 (void);
extern void fsCyclicReferenceManager_GetReferenceId_m30B91E17BE1C02D89F10F705C0EE3FE8F0E0458F (void);
extern void fsCyclicReferenceManager_IsReference_mCE7FC77905F712E503F2CBE8D6F02244C74D9437 (void);
extern void fsCyclicReferenceManager_MarkSerialized_m8CD82B690712E7EEE90E8B7C15EAF22E2E76B952 (void);
extern void fsCyclicReferenceManager__ctor_m4904103AD3F430576C520F7C2C412C35C10BC1D0 (void);
extern void ObjectReferenceEqualityComparator_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_Equals_mCF77DB060539A6992A81D2700283385C08751EB6 (void);
extern void ObjectReferenceEqualityComparator_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_GetHashCode_mFB4BE9ACD40DEAE98F69833C25B3533F8DFED864 (void);
extern void ObjectReferenceEqualityComparator__ctor_m609265685F45AFF755A5EF6BCF53C9B2FAF2DF64 (void);
extern void ObjectReferenceEqualityComparator__cctor_m8B819E32F9065E54766F626C4A17B330217E2D51 (void);
extern void fsPortableReflection_HasAttribute_mD4F1A8E233F51111056C7383B81BB3C2F6034477 (void);
extern void fsPortableReflection_HasAttribute_m1F9C28B7432B2B5C3DE18ACDF7E12F2318E52973 (void);
extern void fsPortableReflection_GetAttribute_m54325D68CE95535644F25CD39FAA3D4858745EB8 (void);
extern void fsPortableReflection_GetDeclaredProperty_m78691C838C06D0C4ADCE259161DE2323504862FC (void);
extern void fsPortableReflection_GetDeclaredMethod_mDC22CDA0777452C8D2FE05B2FB07B14B697A1E5B (void);
extern void fsPortableReflection_GetDeclaredConstructor_m9F5B44FCB7E14D7E7CF90482A9D1D13C11445C01 (void);
extern void fsPortableReflection_GetDeclaredConstructors_mB5B777C7EC535253A345584074FC1D8FBA32160A (void);
extern void fsPortableReflection_GetFlattenedMethod_mE5EF30F98C8A994F7581740E8ECEDBA6E42C8072 (void);
extern void fsPortableReflection_GetDeclaredMethods_mF1E5A4D80A892D0E7E9FED20061B23E39E413DB3 (void);
extern void fsPortableReflection_GetDeclaredProperties_mF26959E95815BD982802EC6160E3DA185B1DC6A7 (void);
extern void fsPortableReflection_GetDeclaredFields_m8FCC0F8F72BA45796F5C46E49F74E6187790D89B (void);
extern void fsPortableReflection_GetDeclaredMembers_m65055614D9291573376917BF3044659CAF23409E (void);
extern void fsPortableReflection_Resolve_m995640D6EA5B5B1268075371EC800CF8720BF9C3 (void);
extern void fsPortableReflection__cctor_m809A0F036D94DD50B44E6986C9AC53DA8215DE41 (void);
extern void AttributeQueryComparator_Equals_m27EA6B949A2AAF440EFE865A87E20FB9CED53635 (void);
extern void AttributeQueryComparator_GetHashCode_m538CC0620B54C1478F4B17229A036FADB11E4359 (void);
extern void AttributeQueryComparator__ctor_m97A20B142642A29462C6A1EEC957529E30965B4C (void);
extern void fsTypeExtensions_CSharpName_mA1935C2EB35A9B042BDC965DA3A75B31F60B5671 (void);
extern void fsTypeExtensions_CSharpName_m7B9CEB55166AB73F3E8475491EB08E4F91475FE6 (void);
extern void U3CU3Ec__DisplayClass2_0__ctor_m80831530C50B87BB035A96D17E242F8B7011C105 (void);
extern void U3CU3Ec__DisplayClass2_0_U3CCSharpNameU3Eb__0_mB271DB9009C764D4CB33A03876DB554A4EEFB2C3 (void);
extern void fsVersionedType_Migrate_mA2431BCBC723C85F909F6D50CCC218ABFAED65AB (void);
extern void fsVersionedType_ToString_m584F4EBD02BC6D0502664D2047FF3D71270A359F (void);
extern void fsVersionedType_Equals_m051C07595BCB7037C2E668E0E09D0A4C74A2958F (void);
extern void fsVersionedType_GetHashCode_m02754D5563950758CECD86BE72D69DD7046C47C6 (void);
extern void fsVersionManager_GetVersionImportPath_mF78001EFBFAB16E1FB6D627FB2327FAA6382BCC6 (void);
extern void fsVersionManager_GetVersionImportPathRecursive_mB3A3038031C067D3C072AC7FA1A6483F923360B4 (void);
extern void fsVersionManager_GetVersionedType_m4CF940685782769880C5341A1C3D2FC703906484 (void);
extern void fsVersionManager_VerifyConstructors_m8411C5605527031779BFE91B425DF200A968ACD7 (void);
extern void fsVersionManager_VerifyUniqueVersionStrings_m930312E5E49A7FEFFAED1994F0B9633BC9BE4276 (void);
extern void fsVersionManager__cctor_m54A1308F62862C4480603F2633A6DAB7362EBF92 (void);
static Il2CppMethodPointer s_methodPointers[1084] = 
{
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AnimationCurveCloner_Handles_mAE1AE747E153A7340A5B374A798036FBBC57EEA9,
	AnimationCurveCloner_ConstructClone_m865B553D86AC7DF69A877604D8D058043919AA50,
	AnimationCurveCloner_FillClone_mD22B76874E416DD92983B2CF1B92B3DAC93CABD8,
	AnimationCurveCloner__ctor_mD6E48B9E766EF280BEDFCC3EB16E0632BB829138,
	ArrayCloner_Handles_m0E4BB720F8CD89F4E273BA17FDA497BDF776D27A,
	ArrayCloner_ConstructClone_m04579E3DD0E8F23680D6C79F015400FD43174459,
	ArrayCloner_FillClone_m0F528E997C2E6204FAF85C13039277A19BE756E1,
	ArrayCloner__ctor_m4D3B005B865126FDAD5A36C8568128F3490ED950,
	DictionaryCloner_Handles_m570A2092BB4E0F5E831541242A6CA8F21BC514D7,
	DictionaryCloner_FillClone_mB8E877FF96C605D93364484F8B58C7E9B201EFD2,
	DictionaryCloner__ctor_m8AAF2AF8CFF369BCD791A2397A5353C0141D848C,
	EnumerableCloner_Handles_m5A8F03D9D08058137BD11460B1EA554D81CB6B30,
	EnumerableCloner_FillClone_mE457AA3D51BD07351B88DE79FB538822AB43B465,
	EnumerableCloner_GetAddMethod_m09AC7C8CB77990AEBF0474E09C06A2816D9DA739,
	EnumerableCloner__ctor_m507726268EB81A88D3DE851334C5E55B030F01EE,
	FakeSerializationCloner_get_config_m443ABF26ED2ACFC73C689914998F11A246A489B5,
	FakeSerializationCloner_BeforeClone_mEBE7770ADAE80B2F6AB2A783AB35CB2226905D72,
	FakeSerializationCloner_AfterClone_m0D2786E5900CBAFFB14F856AAF777072C673EA71,
	FakeSerializationCloner_GetMembers_m5F23561288E698A71CEAD95A20D1517572C720C8,
	FakeSerializationCloner__ctor_m09078A547F0A2E8664F20128626B429EB0288D51,
	U3CU3Ec__cctor_mBCD221ED0EBA60148CA2A33074DCDE2D49E4F3D5,
	U3CU3Ec__ctor_m742CC1499296E2F071621B16CD254C6EB2C5C142,
	U3CU3Ec_U3CGetMembersU3Eb__6_0_m33FFFEA984ECA1DC5B1B81AB7FEA3962385977CD,
	FieldsCloner_IncludeField_m80655FC00A1C89C89AC871F2C929D545524BEB6E,
	FieldsCloner_IncludeProperty_m289BBFEA1CDF28ABBC9D1026BC0AA7A0A6565E72,
	FieldsCloner__ctor_mA7E9B5ADDE2AC98DB4AE3F1F3003C908C7EF2096,
	GradientCloner_Handles_m7280317154C345895B420698E0A5680BDF52ECC4,
	GradientCloner_ConstructClone_m884C9EF1B85AF90A19ED92711CA46C4B88499A75,
	GradientCloner_FillClone_m9173321CA6500A4353D5BFDF8EAD3E258E5DF0C6,
	GradientCloner__ctor_mBFE97BBDC345F88F56B246505BFC32DB223EF0F7,
	ListCloner_Handles_m0512E76505FD1DC7F672FCBD716077AF6277D2AE,
	ListCloner_FillClone_m1A846798478300D38A3DDA5A0B63D0D70E09C892,
	ListCloner__ctor_m1821F8C5C50EE3AA7435EE660199FE2A8F005CE5,
	ReflectedCloner_Handles_mBF1E5713155B43142E3EECE29B234645B0D91FA4,
	ReflectedCloner_FillClone_mA133F029F37A42A0679BFD0CF354B57AB5DB66A9,
	ReflectedCloner_GetAccessors_mFFA54F408B0C5FF803981B57C9E3EF9484959B11,
	ReflectedCloner_GetOptimizedAccessors_m33131F7F62AE218E2392B97BB58287ABE450876D,
	ReflectedCloner_GetMembers_m73242C1E83E3D780632CB0D046182C4F5F1792CF,
	ReflectedCloner_IncludeField_mE8AAB0BA775EDE18E58992170ACF017B8F830231,
	ReflectedCloner_IncludeProperty_m113FC0D97DB7A6B87302AA92006B296A8B9FB1D0,
	ReflectedCloner__ctor_m26645C52857996628C7758F2ECFD22C087DA184C,
	Cloning__cctor_m13D9F298AA9A49D5CE6C135D05ADA852842CE078,
	Cloning_get_cloners_m7516AD27C82F21D85A91DE78A062393AF04A8D2D,
	Cloning_get_arrayCloner_mDCF18912AF69448D0131E0EB6150313BAE1AAD20,
	Cloning_get_dictionaryCloner_m8DA4729ECB30283B37557A8FB3626BF017551F25,
	Cloning_get_enumerableCloner_m6ACB3160E0E452B38585CD88520DFF89C49FB2A1,
	Cloning_get_listCloner_m5AECF91367F610B9FD436FC41495FE6AAC6BF9BF,
	Cloning_get_animationCurveCloner_mF92679E30BFBFCA8D51C6490C91B80C031763330,
	Cloning_get_gradientCloner_m1452BDF0263935B1B609AC61F324E69791CCEEA0,
	Cloning_get_fakeSerializationCloner_mD2C0A4FE224EAD6C528A9A407A7B38E6183F6CF1,
	Cloning_Clone_m568ED7627B5747E9315765956B742ECFE1C7FA5A,
	Cloning_CloneViaFakeSerialization_mB0ECB29B50A1250E3E955E9A9145A834CDBAFE78,
	NULL,
	Cloning_Clone_mA8AD9C1A5AAA7EB227B1D1584CABE06774586763,
	Cloning_CloneInto_mCCB3833D539D0A930911E8D9B685B089720CF5AE,
	Cloning_GetCloner_mB6E52BB0F815151B1CA8F0FBF813C565404D3119,
	Cloning_GetCloner_mD5582F98DCC18C4EFFB76DCB231339008EAF66B8,
	Cloning_Skippable_mE90C99877B244E54730F5916073EFF3796745A33,
	U3CU3Ec__DisplayClass35_0__ctor_m13D109E0F468768FA847E7C8934F3C253D95A3B4,
	U3CU3Ec__DisplayClass35_0_U3CGetClonerU3Eb__0_m451E5D14F3547FE762A929FF1AFE47FD4F2B82A1,
	CloningContext_get_clonings_m57EEC8EC2641C2ACE1A86770D42B9ACFC277E9D3,
	CloningContext_get_fallbackCloner_m3DD3EFC34D0F6144533C7D17A3D21C553C95B950,
	CloningContext_set_fallbackCloner_m55BC99F5581F65FA3FA0F8345EE1C1375A7D7434,
	CloningContext_get_tryPreserveInstances_m35B40A236AED8CBC45326D77EE55BD51E99B13CB,
	CloningContext_set_tryPreserveInstances_mC1B373E85527CDB25357401BDFBAF98DEA0CF94F,
	CloningContext_Unity_VisualScripting_IPoolable_New_m4F67C90E938687CFCB78B20FD9DF18133248407A,
	CloningContext_Unity_VisualScripting_IPoolable_Free_mEECBBB9A5415A637E58FC835C7E6B9ED2438CEB0,
	CloningContext_Dispose_m37225C7C7D5768A50305108638CC3FE7989ED7F8,
	CloningContext_New_mFC459B2EEDB08F5A62EE6F6DFF680D9098D90439,
	CloningContext__ctor_mEF4F7DECC098C27FB5EC50006277B510DB3B57C9,
	U3CU3Ec__cctor_m8CE275C818AD7595B93CC5CB9CDC2E07269AAA34,
	U3CU3Ec__ctor_m1D52D9043B0C07EF5B6F8160EDE1BE9D50089920,
	U3CU3Ec_U3CNewU3Eb__15_0_mB0A2EC8258AE48181D6FB1702910B987F0B572C3,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AotDictionary__ctor_m29C507F410D2E930CC83552F8AF33E610B0E1B72,
	AotDictionary_AotStubs_mB9DC0685ECF292621A6CFD4C46E9945633DF6409,
	AotList__ctor_m41ED35E8BE97563A62E8597DAF14DA22E00F3715,
	AotList_AotStubs_m70D81C2DC96A490390F08762CEF44F20837C62D3,
	ValueAttribute__ctor_m175E73D23899766D498DF0609F1FFC5549043F0B,
	DisableAnnotationAttribute__ctor_mACB2BBBBB84EF7B50F4B2A4E628C0B4679384AE2,
	IncludeInSettingsAttribute__ctor_mF420F5DCC5F72184853A7F5A71AA6FE5A81EDB0D,
	IncludeInSettingsAttribute_set_include_m47FE024A0C7096039DD171CE40FBD58EE473B51C,
	InspectableAttribute__ctor_m7870BEC1B8BA0F0D7952891C210AE0551A80DD25,
	InspectorWideAttribute__ctor_m552B28D4B7600A6F6D6EA70A7FA982BE83D81177,
	InspectorWideAttribute_set_toEdge_m9E239E2E48DCDC88ECE51309AB059176AABD21D4,
	Ensure_get_IsActive_m02209695F39927F34703939CFC6611A834DFEB2A,
	Ensure_set_IsActive_m2E42ADB156261AB2ACBFD74C88A2686B096C51BD,
	Ensure_That_mA8D5A2A6E1D51157C99920962B64DF9BF20DA1E9,
	Ensure_OnRuntimeMethodLoad_mFA2CA8893587A952FFED184B45DD91016CD67845,
	Ensure__cctor_m8FD6F07FFF518C24B23AB780D8E17ACFB9F7D830,
	NULL,
	EnsureThat_IsNotNull_m5EC50846951F99E0A677FCF5EFB764413EEF5ABD,
	EnsureThat__ctor_mB4F1D463D88F8A1E747C2D6D14829651EB0C4818,
	ExceptionMessages_get_Common_IsNotNull_Failed_mE09022A0483B91AAD832AD2F3C098E8272147E7B,
	ExceptionMessages__cctor_m3AFBD6D30CDC473DD735CC118E69374BB5DE6B35,
	InvalidImplementationException__ctor_mB7F77B15C275D57A96CA4CB448B481A5734E50A2,
	PlatformUtility__cctor_mFF0A0C5C6FFDD91762B2378CD8BCD9FE8D9AE777,
	PlatformUtility_CheckJitSupport_m2C0CCF8DF88FDC26FAD5FAA533BBCE4DF5B6E53B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ProfiledSegment__ctor_m0FA4B5DD1FFD67A924C451ACD6D7CECCF721DF4A,
	ProfiledSegment_get_name_m9A83A1B1858918C129A43ED337E23277F9EE9AA2,
	ProfiledSegment_set_name_m2C9E954609FDD3F421CC890ACDD3FE7C5B26D4B0,
	ProfiledSegment_get_stopwatch_mAADE38FDD80356EE0367C2823A5871B416BD80BE,
	ProfiledSegment_set_stopwatch_mE936E4624C2FC58738A37162ABA87C3F7D66E188,
	ProfiledSegment_get_calls_m42FA79BC36DC4D4E154A230EF3568805AB20C14D,
	ProfiledSegment_set_calls_mF492A80029C52FADF5E3BBAC1666B4976A022848,
	ProfiledSegment_get_parent_mBE916AC5FE45C65AA47912ADF8C79AF92E8E2518,
	ProfiledSegment_set_parent_m2C899280C5AEF9D4FFF92497B2A21112CDF29E13,
	ProfiledSegment_get_children_m1750C6E57AF8A2244AC7955903409A2078D73A52,
	ProfiledSegment_set_children_m8DD8249EAA297F8064CFB19454766920D0C93579,
	ProfiledSegmentCollection_GetKeyForItem_mDB1F3291339E5E624FC7CFE00B7DCA9FC75755BC,
	ProfiledSegmentCollection__ctor_m26764F5154AB5023FBDBF67CB12F6B1155843AEF,
	ProfilingScope__ctor_mF9BE98497C393234478FA7985004C5987D1144EE,
	ProfilingScope_Dispose_m7F66BF3E60126E49FAFA1E38F6126D71CFCDBB44,
	ProfilingUtility__cctor_m9621EEE77AF891941DF842EB1DA273FAE639AC82,
	ProfilingUtility_set_rootSegment_mE6791774DDC5D645738C0C0725054BA8A4146287,
	ProfilingUtility_get_currentSegment_m29989761BD0FBE5AAF19C4398F03E042D0EBB7EC,
	ProfilingUtility_set_currentSegment_m8614FC08664A8E73A0504CC30310DB5D7F6D51C7,
	ProfilingUtility_SampleBlock_mE7FA8F94361D7FF9F175E498F4DB1B7AECB5DDDC,
	ProfilingUtility_BeginSample_m944E5C3C885F14F0E837BEDC56B67FF7D1C3D86D,
	ProfilingUtility_EndSample_m8476D0C3313F65B91CE8018400448213B71A6A3B,
	AttributeUtility_GetAttributeCache_m0BF85D2907402A33848A0288DF1B4D0A8B4222CF,
	NULL,
	NULL,
	NULL,
	AttributeUtility__cctor_m4AB10CF9AF3D21DEAC9E70F059807BC758E01C3C,
	AttributeCache_get_inheritedAttributes_m7B00A5C8EBB1B79595E284E882E06BF6619A08E5,
	AttributeCache_get_definedAttributes_mBBBDA84A9281235FAA537ADF2A9EE7F1A495E00C,
	AttributeCache__ctor_mDE1B2BBCE9B04F8EE8A6D6938A4C9644872C63A1,
	AttributeCache_Cache_m73430F04D21D69FB1B0520570C42ADDBDC941DAA,
	AttributeCache_HasAttribute_mF47A0A17CE82F96216E586E73E928C29F8B96CF5,
	AttributeCache_GetAttribute_m9042BAE430568B5D7E851F7AC9EF0F1B9970493A,
	AttributeCache_HasAttribute_m6DE0BA7A52D8EB2284ED28B641AF6F41C53C8AA7,
	AttributeCache_GetAttribute_m1B367E0EC1D06BB18254A3C357B7547316F26AA9,
	NULL,
	NULL,
	ConversionUtility_RespectsIdentity_m25D67F31084F93B202E7770D8E6148FCC3EC0630,
	ConversionUtility_IsUpcast_m6E1F4D622F39517A4EA13CF09CD03178E2BDF074,
	ConversionUtility_IsDowncast_m2A5D16A66B697AF36D35281164C2DC5AB265DF0F,
	ConversionUtility_HasImplicitNumericConversion_m1EAF48D8B2AB05EDA60F201E70640AE516DCDAA7,
	ConversionUtility_HasExplicitNumericConversion_m5FF693F9371522083BEC877146CE6B5BCD512769,
	ConversionUtility_FindUserDefinedConversionMethods_m013D713F44B187E0452B6D0681CA048C83D3EBB2,
	ConversionUtility_GetUserDefinedConversionMethods_mB8E662C43F88A841E105E956ACA0170169DBD264,
	ConversionUtility_GetUserDefinedConversionType_m613D07F77FF3949915BFDEE25C1607BA7A53BB6F,
	ConversionUtility_HasEnumerableToArrayConversion_m7A2916FB4E31FBDF28313748935DB5012CE7A245,
	ConversionUtility_HasEnumerableToListConversion_m3962A56BC1B219E355FC61F2575DB6B871A7A591,
	ConversionUtility_HasUnityHierarchyConversion_m60943B20F9A86C6EE9690C22D0E7E53554810CE4,
	ConversionUtility_IsValidConversion_m895179055643109D4A844010FCB29F3B3F9CF608,
	ConversionUtility_CanConvert_mA8F22C8F21FFD91A803529F6B6DCF5548A20D336,
	ConversionUtility_IsConvertibleTo_m449880AECB0EBD44001F046AFEFEC9E20ABC9416,
	ConversionUtility_GetRequiredConversion_m59F588841432F5AC06A57E4EF4CC604B6A234436,
	ConversionUtility_DetermineConversionType_mB2F9B24E0333EA4A608E8D8404C652245BA4BE06,
	ConversionUtility_GetRequiredConversion_m35AD8C744818D84F32BB83FE06949AB127233AAD,
	ConversionUtility__cctor_m6417CFACAE78479369968DFC33EC8A47CA7FA0F3,
	ConversionQuery__ctor_m8486070E4307ABFAD499BBFD5694FC44D47A4B59,
	ConversionQuery_Equals_m433F18C3D368EF871293F990F3433EA2A2DCE7DF,
	ConversionQuery_Equals_mEBBAF7F90C0933C8072AFF2A88FBE2938DB9629F,
	ConversionQuery_GetHashCode_m19A38C82ED3B33971CCA4792FC48A99BA21BBC24,
	ConversionQueryComparer_Equals_m1227EF34810F4D09B03F2B6D54B5B8E1BCC93AF7,
	ConversionQueryComparer_GetHashCode_m0AAD5740A28AA6CB55CAAFC400083D84AE5D98C5,
	U3CU3Ec__cctor_mFFC2A9ABDE8C451BBB0BEF6E8489EE9AA9A6FEC2,
	U3CU3Ec__ctor_m3C7EF343BCFD80554D3B1432EEE0A90952F5F654,
	U3CU3Ec_U3CFindUserDefinedConversionMethodsU3Eb__11_0_m1532E38366EF46B817581A490728661E62DAD8E8,
	U3CU3Ec_U3CFindUserDefinedConversionMethodsU3Eb__11_1_m9312372514C015C92094F467740F71A15F4C0CCF,
	U3CU3Ec__DisplayClass11_0__ctor_mD21BF2AF4F387C43CE373E271D37D00F0D0FD175,
	U3CU3Ec__DisplayClass11_0_U3CFindUserDefinedConversionMethodsU3Eb__2_mEAE1A6C43BF8690564235F0CF048B3BDDDE465BD,
	U3CU3Ec__DisplayClass13_0__ctor_m5E3D815FF989DB07952FFEA94DE25C5D3264ED98,
	U3CU3Ec__DisplayClass13_0_U3CGetUserDefinedConversionTypeU3Eb__0_m7F4A2C93288A462B6E5353D052FB78388314EDFB,
	U3CU3Ec__DisplayClass13_0_U3CGetUserDefinedConversionTypeU3Eb__1_m493930AF75FBC7E891B52A7AA988D434D6AB6A8E,
	U3CU3Ec__DisplayClass13_0_U3CGetUserDefinedConversionTypeU3Eb__2_m3A850A03B1C3EE21A0784685C4EB14B0A76E877F,
	LooseAssemblyName__ctor_m8834C59F1E492EA1A935207B7134961B35DCE8EA,
	LooseAssemblyName_Equals_mA5DB041D4D8667E44F9F1CB82A5C0BBE84E7E7B1,
	LooseAssemblyName_GetHashCode_mB97F58ECA80BDA748C4D1A101386F6E2AAE2494A,
	LooseAssemblyName_ToString_m30BA77AF842E4FD31F5E422190DD9DBDADA9A3FE,
	MemberUtility__cctor_m664FAD732DCE19B8DE7DD19B0AF081B50CAD54D6,
	MemberUtility_IsUserDefinedConversion_mCE97B647A7E9DC669FAF87DE4AD01FAAECC7544A,
	MemberUtility_IsExtension_mAAE33EA58F9578CD8F2FEFEDD5CE87A725DD03A8,
	MemberUtility_IsStatic_m518306DE8B210938210C1A2DFC8AAA4A041F288A,
	MemberUtility_IsStatic_mD56EF75FDD803BB5D98ADCA15CE6FF46DDF8FD03,
	MemberUtility_CanWrite_m3DAD0B8FCA1BF1092783730DAAC05F3DCC759860,
	U3CU3Ec__cctor_m0E868FF92E2C40AD8C1A913911E48330D268C9D0,
	U3CU3Ec__ctor_m43145BDC3A79A5557E76FC10A01C26661D0747D6,
	U3CU3Ec_U3C_cctorU3Eb__0_0_m7173AFF9208ED469948E0FA302CB6D3D10DA96D8,
	U3CU3Ec_U3C_cctorU3Eb__0_1_m9667E5040570CAD1933E9C402DF65C06CFCF1E9A,
	U3CU3Ec_U3C_cctorU3Eb__0_2_m131AD23DC7F2AE71D7304861E4C63F20E6F9AC72,
	ExtensionMethodCache__ctor_mBA3AB489FAEA7A274B8D1236C6FEE0A9554C2B6C,
	U3CU3Ec__cctor_m7DE32F911FD2DE85CB320F5A78D2AA7C0BAD3E18,
	U3CU3Ec__ctor_mF7C67F6B049E9C001A08FC852800B09C18E2E12E,
	U3CU3Ec_U3C_ctorU3Eb__0_0_mDF6087D5B1E8D42E1AD5B2D1B82E68DC16714CFC,
	U3CU3Ec_U3C_ctorU3Eb__0_1_mCD23635F671D3FCD7139716EC946DFB88DD159CB,
	U3CU3Ec_U3C_ctorU3Eb__0_2_m36397327B09DCB2F111245BB77F422AFA17CBD5E,
	Namespace__ctor_mEFD631C64BA242ED84917A8A41187E12274E557C,
	Namespace_get_FullName_mDB579A0D0D2C27FE32DEDCEA37DC084EF08D3D36,
	Namespace_GetHashCode_m2A2B37632988B46BFDE3B6383C527318D43F8637,
	Namespace_ToString_m8743EE052004FC1236F7ED0ECCE981C8EB9FCBBA,
	Namespace__cctor_mB5403664D6FF82D5E8826104284643B99EF4E8CF,
	Namespace_get_Global_mCE4DF5BEB067F955772F134FD16F9372E8F7E175,
	Namespace_FromFullName_m1F3A9348555D5C71E8963B495BC4C41EDBE45C63,
	Namespace_Equals_m041B99F9C7946F8EE79048F78856975FAF9D15F1,
	Namespace_op_Implicit_m4BC642F9C84B895BDBF4F88F6E005C2372DF47FE,
	Namespace_op_Equality_m2626D986A4EFACB844C4545EE4A8B193CCE04478,
	Collection_GetKeyForItem_m38823CEBF30147D7FCF5C2B2F9E63DA7E3294830,
	Collection_TryGetValue_mADBB0DCB690F9886C1F201868247162A6B6F4B7F,
	Collection__ctor_m5EE24B9AFB48B63BD972C4B5193393BCA20C116D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	InvokerBase__ctor_mF8778193D06F6C818A249562C7962709730CC5C3,
	NULL,
	InvokerBase_Compile_m473742347F42A36C28E38FD5EA23B24A0E2FCF5E,
	InvokerBase_GetParameterExpressions_m4000146B3859FA03C9411B13994B29A6B791E94B,
	NULL,
	InvokerBase_Invoke_mE5B3475184E0816DD1D8C7BCBB0888BE48DB02A4,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	OptimizedReflection__cctor_mA7D038684333D3CCEA63B838F2C0421EF3C94554,
	OptimizedReflection_get_useJit_mA1E53EB6ECAF6C799D746D0F87F6EBC7AC01996B,
	OptimizedReflection_get_useJitIfAvailable_m7B5D5B745D9567F0B2078B6D9F78FEDFB06C2958,
	OptimizedReflection_get_safeMode_m16A922E85DCBC77BF8D7842CF9D46D3AD747308A,
	OptimizedReflection_set_safeMode_m8917E0D8E613CE4C2EC23A10A70E42ECA21480CF,
	OptimizedReflection_OnRuntimeMethodLoad_mBD2F4E483E9A25C92E616EB594B727008FA650EA,
	OptimizedReflection_VerifyStaticTarget_mC301AAA3260A101CEF161A2DCBA13C66511FA6BE,
	NULL,
	OptimizedReflection_VerifyTarget_mD2F971BDCF20B242584AEAEB6997C010A2AB7FA1,
	OptimizedReflection_SupportsOptimization_m347BFAAD8CC42D62232E26689C7B424C7700C348,
	OptimizedReflection_Prewarm_m516501B4E933B7D7EFB1ACDF37FC7BB36A2B9E34,
	OptimizedReflection_SetValueOptimized_m31D72287894B166E76AB07044AD0577D36872BF9,
	OptimizedReflection_SupportsOptimization_mF3283EA8FB29FF45FF362320A80FA514F5124336,
	OptimizedReflection_GetFieldAccessor_m1FE0A1FF5046C3F8518F3075BCCD1041B7BA06C1,
	OptimizedReflection_Prewarm_m64238D56795E70E3BC787858AECD92AD2E80EA0C,
	OptimizedReflection_SetValueOptimized_m90041502530ABB8093833AB1AAF7EAD71CDA42AA,
	OptimizedReflection_SupportsOptimization_mE77F5BA15A9E93ED8D84947A11097FDFDE0E8D32,
	OptimizedReflection_GetPropertyAccessor_m2B9D5C4E7AA788B3A4D24AEB15C8BC3BA6481951,
	OptimizedReflection_Prewarm_mE002ED1A6BAE2192B09BB38105660A0D60C3FD84,
	OptimizedReflection_SupportsOptimization_m6F8B75D83A6AA63D88FD9E39F8F774E9B92F3914,
	OptimizedReflection_GetMethodInvoker_m1F5AE4FEE232BB21C091CFE0E87C2B80D68D1A2D,
	U3CU3Ec__cctor_mFDC2972A66902F0DB6D2194BC804AC618592E372,
	U3CU3Ec__ctor_m229CF2F8E2915479FECDC11787B7C68731EDD5C8,
	U3CU3Ec_U3CSupportsOptimizationU3Eb__39_0_m9814B0F6FBC37E25E109C41F4C94AE2C80A32D75,
	ReflectionFieldAccessor__ctor_mF78A1143AC4C6DDACB9E9FECBFAC08F94A3537E4,
	ReflectionFieldAccessor_Compile_mEBC82506C662D5D6BD8B5ECCCC84486258B5E14F,
	ReflectionFieldAccessor_GetValue_mEF2F61EE17D6187F4AD0ECC9BCAE5C3DA154C40C,
	ReflectionFieldAccessor_SetValue_mB377A07ABD9DAC8C32E16A900E825E94669D6716,
	ReflectionInvoker__ctor_mAB6A4A7633A3F7C6F39FE67D3E67E808743061F3,
	ReflectionInvoker_Compile_m0E557DBBCF69F0B9DBFC7C65F1E9E04291BBAEB9,
	ReflectionInvoker_Invoke_mCEAB3C649FD9E5C239FA81DF0E6C5B626B0AAB89,
	ReflectionInvoker__cctor_m95B3CC2E85900381361C876219C88FC9EC8FAA6C,
	ReflectionPropertyAccessor__ctor_m6D9ACC5A79528E9BC510DD39E888C308AD7DBBA4,
	ReflectionPropertyAccessor_Compile_mA548D99C2C1CF4FC2D6BFDE60B88CC3290F68825,
	ReflectionPropertyAccessor_GetValue_m24E6DB4D752CA9ECF169145015DFFBB9E830FFC5,
	ReflectionPropertyAccessor_SetValue_m864D0F0A46C817DF7D444F569A3352930481828D,
	StaticActionInvokerBase__ctor_m5ACCD147F42CA3D67E774B9FEA8F256BF215321C,
	StaticActionInvoker__ctor_m68937E5823AB3318282DF052FC16E6A3932EE0C2,
	StaticActionInvoker_GetParameterTypes_m785C5398B6E9D7BC36077F14788966EA4AC04641,
	StaticActionInvoker_CompileExpression_m850609F926060D54357A35638C924DF30DC1F7E1,
	StaticActionInvoker_CreateDelegate_mEAD1944C9CA6D3064120700F5FDF27DEA81EDF30,
	StaticActionInvoker_U3CCreateDelegateU3Eb__7_0_m62B13C47D02C64D0D12A564105859E1715987E68,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	StaticInvokerBase__ctor_m6C9DC047E597DE757E95781C8ACA69DC87604534,
	StaticInvokerBase_CompileExpression_m46F00B15A03177401B26BEAA7F8637D699B5CB5A,
	NULL,
	StaticInvokerBase_VerifyTarget_mA527572123E57DFA81EC65FE82D50BA811AC1424,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RenamedAssemblyAttribute_get_previousName_m49CAA91F128F051AD44EF708A67B1709491F3FC0,
	RenamedAssemblyAttribute_get_newName_m7D5A43A237D16F14068DA42A4961685B43B8EC5A,
	RenamedFromAttribute_get_previousName_mCC937DC8B67B589D4111AAC308C3D074A1632352,
	RenamedNamespaceAttribute__ctor_mDDD1EB50234271FA6931DBB745EA5297F345E63E,
	RenamedNamespaceAttribute_get_previousName_m7F20312B67C7E59FF98C8CAF6BB823FD3F9B3FAB,
	RenamedNamespaceAttribute_get_newName_m2EBF2918C960DC6299410D91BFCCD26E084078F4,
	RuntimeCodebase_get_types_m3F45B74CB58DFCB03FC5547B9ADCDDD490BC7DAA,
	RuntimeCodebase_get_assemblies_m152648BAC9B232F7A0B7600EE603D0C10D89E35B,
	RuntimeCodebase__cctor_mC4C663CE8466644C7EA7E2E98F12A7D68F7D5F07,
	RuntimeCodebase_GetAssemblyAttributes_mEA3530C25AD69CA371E1ED80B51CE431574CED04,
	RuntimeCodebase_GetAssemblyAttributes_m61A6334A9520E8A29CA6DE4C1FDF671B246BE8EC,
	NULL,
	RuntimeCodebase_SerializeType_m5C4C24AE860C5758DAAF06C213A8F23868F26EF1,
	RuntimeCodebase_TryDeserializeType_m5CC65D7B9C28DA10A2CA580A21EBF4B3AED83149,
	RuntimeCodebase_DeserializeType_mDAC85B396158EA8898F0FC4F718B90BC97B607C1,
	RuntimeCodebase_TryCachedTypeLookup_mE0D4B034A584410139207C9837E467072B5F8F0E,
	RuntimeCodebase_TrySystemTypeLookup_m1613832DE9F9FE5D75923D36764A00E337B4F7CA,
	RuntimeCodebase_TrySystemTypeLookup_mFB5AE558EB67EBB4258567B8C5103E7A802B5E63,
	RuntimeCodebase_TryRenamedTypeLookup_mF92440902F2F467EB40F8F07C5976DC6E55A653E,
	RuntimeCodebase_get_renamedNamespaces_m6A46742DBC322161E1D84D4B99628416ACC9AF20,
	RuntimeCodebase_get_renamedAssemblies_mF5DE74B7E878DAA69297418DAA63FE93BEE3CEB6,
	RuntimeCodebase_get_renamedTypes_mD01972DB9723D31C6491C25795429DE47A3579A4,
	RuntimeCodebase_FetchRenamedNamespaces_m6AEF3ED15432107D5DD44C4C19319F5CAB97EAA8,
	RuntimeCodebase_FetchRenamedAssemblies_m7E7EF9246967FF76CABE08F1485F0FB556CC921D,
	RuntimeCodebase_FetchRenamedTypes_m95705C73DA7BC9BDCFF3E7191DFB44FC24645AA5,
	U3CU3Ec__DisplayClass25_0__ctor_m0D93D81BF21CE63B7F8E961ED5EE8B6B4797122D,
	U3CU3Ec__DisplayClass25_0_U3CTrySystemTypeLookupU3Eb__0_m35E9BED0D9C2FE659F43D79227F6B69684BE03D2,
	U3CGetAssemblyAttributesU3Ed__15__ctor_m47420C8E084D30FB82070FAB0D44CD2E015FEC3F,
	U3CGetAssemblyAttributesU3Ed__15_System_IDisposable_Dispose_m975BBD0D64613D68E9BB439F9ECD56F1AE5A5B06,
	U3CGetAssemblyAttributesU3Ed__15_MoveNext_m212DF6D7868EF9CD85C5BFF546C65C7CA0622BA6,
	U3CGetAssemblyAttributesU3Ed__15_U3CU3Em__Finally1_m3B86D5FC290D424874FDE134F9664955DDD69F4C,
	U3CGetAssemblyAttributesU3Ed__15_U3CU3Em__Finally2_m4037CD33D7E52258B71656E925137DC11608D827,
	U3CGetAssemblyAttributesU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_AttributeU3E_get_Current_m91D9E801B043345FA4FA529CFDA0152F349C03DA,
	U3CGetAssemblyAttributesU3Ed__15_System_Collections_IEnumerator_Reset_m61BFD4A96E6ED6AE40EBEEC0D22A370415224E60,
	U3CGetAssemblyAttributesU3Ed__15_System_Collections_IEnumerator_get_Current_m2AC28B978947B95D322602E2FB4125B2B3517A5B,
	U3CGetAssemblyAttributesU3Ed__15_System_Collections_Generic_IEnumerableU3CSystem_AttributeU3E_GetEnumerator_m1ACF3B7B507CF7278E09B68E927C80F897FA467E,
	U3CGetAssemblyAttributesU3Ed__15_System_Collections_IEnumerable_GetEnumerator_m0B04727F2A2E9D9B1550180CD3A262471134125D,
	TypeName_get_AssemblyDescription_mADDDD6FA51C519C60491A8B20F8B69949A7121EF,
	TypeName_set_AssemblyDescription_m3EBD7FB0F0C264E8EB6362C10645035C9384F05D,
	TypeName_get_AssemblyName_mBE44717AA3A7377931E7A5B955711CE85019186F,
	TypeName_set_AssemblyName_m9AD0F0CA466333A8C06257F367D5BDC84FB5C568,
	TypeName_set_AssemblyVersion_m7BC5B2480F201871A042EBE5694A35E73C89887D,
	TypeName_set_AssemblyCulture_mDD4A88BFC38A7B3A2F55030570E9ADD11AAD4A33,
	TypeName_set_AssemblyPublicKeyToken_m03E38B53CD7467D5EDD4089AF75863381280AD80,
	TypeName_get_GenericParameters_m519E4A51DCCD0F0701F35C09784D8128EE8263AC,
	TypeName_get_Name_mBC768C507F7EC26072615B2B6036307367B42D9E,
	TypeName_set_Name_mBC6647619A19BB99875C39EF7A6FD0D14B516E56,
	TypeName_get_IsArray_m92C2087D662FC897ADA6F7251D84612D4A9BB036,
	TypeName_Parse_m164944B90D4C17C850C4D732D96D55B515BE4F41,
	TypeName__ctor_mA9E1A4603EA5E12401679124F6E53C0DFBF0F735,
	TypeName_LookForPairThenRemove_mCE5F569E040A70172646F30AAF4D96B2379EE24B,
	TypeName_ReplaceNamespace_mA1A47EA949FF2DE3F9EDBF5E6A2D746CDD5C35C5,
	TypeName_ReplaceAssembly_m14F4A61C1B10FA4C98C0FA156FEE5A3152A0438D,
	TypeName_ReplaceName_m33BDC9C3E587682E4D9D1A70E5689ED2CE1D7CB3,
	TypeName_ReplaceName_m32FDC43BEC0C6B53EECEE46C0560CDF61AB0BD5D,
	TypeName_ToElementTypeName_m1FF41E9B6AC37F2E7753C12F77F2279865743342,
	TypeName_ToArrayOrType_mF8E6577D6572798C6AB5E0C4CDF708D45ED6C574,
	TypeName_SetAssemblyName_mA67AE36887C5DC459E77D3463A518CA58B45E3A4,
	TypeName_UpdateName_mDFE4E330B5E41A27413315AED6F977B6527FDE13,
	TypeName_ToString_mA2BA8AAEF508ECFFF91FBB6F67D451A96D6AD1A8,
	TypeName_ToString_mE707318674F5482901B92C14E7269AEF03067AE7,
	TypeName_ToLooseString_m65BC19E3146198A4D8B2B7CF96495EF119E70EB9,
	U3CU3Ec__cctor_m20A9DCE1C467E521281D411A17A0C7DD2D8145FD,
	U3CU3Ec__ctor_m02363FEAE6C3572064D33111C69596ACDC921325,
	U3CU3Ec_U3C_ctorU3Eb__35_0_m7515082ACA3072CCCE8F2CEA54CE7C8B3C85833B,
	TypeUtility_IsStatic_m95F7E5A523CE72B003E45896FFF9EAF419B64D86,
	TypeUtility_IsNullable_m231FD352991AA34AFE82055417DA61DB28FF4AF8,
	TypeUtility_IsReferenceType_m1319781FE9641ACD464EC92146549F533A985232,
	TypeUtility_IsAssignableFrom_mAEB6320B1B550044B01FABADEF19F5B2C1D4D569,
	TypeUtility_GetTypesSafely_m1127FF593F0365E1F97DE2683797FD161911459B,
	TypeUtility__cctor_m45284A9000012A4F4B5D4783E319433401346E1B,
	U3CU3Ec__cctor_mAEC24D9AC93F35222671E1A349499CA523B22063,
	U3CU3Ec__ctor_m9C9EBF4D33D62E1F215CFA1389D642C1876A2B96,
	U3CU3Ec_U3CGetTypesSafelyU3Eb__35_1_mD0A8D58ECF1ADF9351D3B57C260A51854805B3B8,
	U3CU3Ec_U3CGetTypesSafelyU3Eb__35_0_mE62999A474952F85D25E9C36CD210C383AA4C4BF,
	U3CGetTypesSafelyU3Ed__35__ctor_m9701C2638FE76E20AFAD85EA4D7A9CF0E2FE0345,
	U3CGetTypesSafelyU3Ed__35_System_IDisposable_Dispose_m5EA2247E9D84A5ADC55E29460937CF9F88134AF7,
	U3CGetTypesSafelyU3Ed__35_MoveNext_m602A89C0EEB551F173B43FB64773BC191EA2DD5E,
	U3CGetTypesSafelyU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_TypeU3E_get_Current_mB8219F6708B3866C2051342395157FC925AEA8ED,
	U3CGetTypesSafelyU3Ed__35_System_Collections_IEnumerator_Reset_m840895FA7E2F01ACFEAB3185C25F1907417F9F91,
	U3CGetTypesSafelyU3Ed__35_System_Collections_IEnumerator_get_Current_mB28A5657B028511D87AF2DB15D631FF090690FF6,
	U3CGetTypesSafelyU3Ed__35_System_Collections_Generic_IEnumerableU3CSystem_TypeU3E_GetEnumerator_m7C50E41A556CF784E82B0CFEA752B79C96779D84,
	U3CGetTypesSafelyU3Ed__35_System_Collections_IEnumerable_GetEnumerator_m9BE818126736C35125B5535744D903B198AAA135,
	LooseAssemblyNameConverter_get_ModelType_mB19B6AFD590D99235B8AA71EBBFE78EE9444521F,
	LooseAssemblyNameConverter_CreateInstance_mDA13B4217C2F40BE38CAB5D948C2D50ABFA129AE,
	LooseAssemblyNameConverter_TrySerialize_mA6B65EF2FE0C61D1314D291A85C4841344734252,
	LooseAssemblyNameConverter_TryDeserialize_mB83E01E613DBD3F4F96BE45BB20C4F12636422FE,
	LooseAssemblyNameConverter__ctor_mE139E612CCF8019E6150DE862817FA1E3EE25CFF,
	NamespaceConverter_get_ModelType_m19B036601B6EAD39365E78803BBD02DC3501ADAE,
	NamespaceConverter_CreateInstance_mB2970F338146D188B4AFF549B6CE84E156E90BDE,
	NamespaceConverter_TrySerialize_m525E79E5EC37A73BDE5B7C524825D534BE92CE65,
	NamespaceConverter_TryDeserialize_mC49A2DD95384647E052E58E4F592B2779435202F,
	NamespaceConverter__ctor_mA33B806D736BCA8EDC2B2F43A919B01CC958E339,
	Ray2DConverter_DoSerialize_m2CDE91F48310806906DB55D3F3C3798524F6205E,
	Ray2DConverter_DoDeserialize_mAC43A59CCEC9EA2FAD564306C791AEFB7E22DAB3,
	Ray2DConverter_CreateInstance_mC4483214E994B76F96970FF99BB554FB727167E4,
	Ray2DConverter__ctor_mFCD2BA4B7D50BF03A02FE263BAD388BFA121AECA,
	RayConverter_DoSerialize_m69D7D5CE5706F302544207622C297087AA073F83,
	RayConverter_DoDeserialize_mDC94564FC1FAC221EF831AF9AF4E1175ED7967F2,
	RayConverter_CreateInstance_m023835981B6872E0A01ED20D2635AE9BCFB2AED0,
	RayConverter__ctor_m8B3EF461784305F308BF7D6259A3019B7DD2DB03,
	UnityObjectConverter_get_objectReferences_mE0B7C99E0991DBA79ECCB99F81C82136C22F3416,
	UnityObjectConverter_CanProcess_m561DF6C035BDD50DBF3ABA03EBA3946E451E8CC5,
	UnityObjectConverter_RequestCycleSupport_m6318E3411DE26A9F9FD38E58DCF4E29863634F34,
	UnityObjectConverter_RequestInheritanceSupport_m4E047496E7F592E0ABEBDD12486B016D75C679BB,
	UnityObjectConverter_TrySerialize_m04D02BCCF4CDD400F40D5CE29D7E8E5FB444604A,
	UnityObjectConverter_TryDeserialize_m6EAC5B6CC58F710CA73DBF1D6BD50D5CC59E9A6D,
	UnityObjectConverter_CreateInstance_mD4FA2B90A8700DCC585D1431FACD9AD5FDE0D4C7,
	UnityObjectConverter__ctor_mFA76717DCDA335EA5F6687B108DE6AEC3810288C,
	DoNotSerializeAttribute__ctor_m27900120F3A25D3895C619492BD83F612E91B3D8,
	Serialization__cctor_mB4F0A488DA42E04996281B8CBA51226AB1C8AC41,
	Serialization_get_isUnitySerializing_m46208D5D6E042D9797B2DFBFCE0632A1BE5A9504,
	Serialization_set_isUnitySerializing_mAC835F229C4FAE90E9040E4760001AD7DB39016E,
	Serialization_get_isCustomSerializing_m3C895CC8649F88D6712A406A25167C7B369BC0A8,
	Serialization_StartOperation_m0E07E844663C5C9673C0B5870CB73BEF53B0E0EB,
	Serialization_EndOperation_mC37CC5563ED0C5086EC3AC2D58A8EE86BF822247,
	Serialization_Serialize_m32FAE37FB98FC80EE641572B44AA555F4C374AFA,
	Serialization_DeserializeInto_mF9E364DD7743C17A22CD089EA8D4FC3065B08062,
	Serialization_Deserialize_mA102EBE57964B8923192696AD3FE6541229415C0,
	Serialization_SerializeJson_mB0FA51A5F9BE987432B3205814DFC0CA609C1914,
	Serialization_DeserializeJsonUtil_mA520214A610CDBF12EAD5743E64528494362F4B6,
	Serialization_DeserializeJson_mA737B5ECA3C7DFE7442A2D6563BCCDD0E586BEB8,
	Serialization_HandleResult_mA8F7C587002F125F0A2B4214108BB4651359B4F7,
	Serialization_PrettyPrint_m425EE6DFF2AA0559BA142CDE136693238DDEEA42,
	SerializationData_get_json_m316B4017338BC7DB9B4D3D9A81BEF1C01232FB24,
	SerializationData_get_objectReferences_m70704774677025CE081A408A7D558A9309C24DA4,
	SerializationData__ctor_m590F6C005C219D41968701544B5D4D2A9E2C2607,
	SerializationData__ctor_m1AECF919A4D6D2B9256D1ECD164E2A8189DE64AD,
	SerializationData_Clear_mC7B73F14EFA97C2831731F9B3C558765F08EC73D,
	SerializationData_ToString_m277BCD1A9A8431713FCADB24C479817E0C6A3A4C,
	SerializationData_ToString_mAC5F79322472876518D60654C6515E4ED142EF87,
	SerializationData_ShowString_m53B7B7664BCFCE62D2382E53FF7C0205D9034270,
	SerializationOperation__ctor_mB4A87C9CDA0B896AA0142F226C53CFB73DAA3573,
	SerializationOperation_get_serializer_m0B67471F76A1393A6221DBE9F13706236C371ECF,
	SerializationOperation_set_serializer_m93AD82939387F372A2E3DB25EC068CD0A87E5C60,
	SerializationOperation_get_objectReferences_mAF348F7C8689414FC1FEAE422D6173253D4D5444,
	SerializationOperation_set_objectReferences_m73EC5199265D1C61291DBD948CE1687460EF1155,
	SerializationOperation_Reset_m6E8884DC5D1B7E20DB08B3B5468D52D8E93BA217,
	SerializationVersionAttribute__ctor_mB7B6BA540C0D134C649AE39016D9FF506B34DFB7,
	SerializeAttribute__ctor_mC49BD2B8E51A1117AC0B8760F62A89A21AD93BCA,
	LudiqScriptableObject_add_OnDestroyActions_m0A7072A424603508C71272D0CE997137A47092DC,
	LudiqScriptableObject_remove_OnDestroyActions_m30485F5810C1BEBDF70C495771A0CFCE62D5260A,
	LudiqScriptableObject_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m8758CB699FAD9F7908247C87D3EF177277174642,
	LudiqScriptableObject_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m18C20C86B3BF295C04C543AFD9B68F961735F53F,
	LudiqScriptableObject_OnBeforeSerialize_mE3F4CB617DE9FFDE4C13DBF648D4139CB8D05E02,
	LudiqScriptableObject_OnAfterSerialize_mE88BFC91F746BAA50EB394941006C389FD11E87B,
	LudiqScriptableObject_OnBeforeDeserialize_m89A629A4F62E0C972ADFDD5A29FA6D10F58BB3CF,
	LudiqScriptableObject_OnAfterDeserialize_m622CDCDACBD0B2B628EAEBD829C5753CA9D25ACB,
	LudiqScriptableObject_OnPostDeserializeInEditor_mE76D1870C90DB8EFA83C8DE030861FC78B9081CE,
	LudiqScriptableObject_OnDestroy_m0CD244E99818B2DBB68F05040CD9452C18034AA3,
	LudiqScriptableObject_ShowData_m53F17A66E811705D002006DED0BED7EE7F06428D,
	LudiqScriptableObject_ToString_m870D1107807BDC18072D2F3549A7E51930EBC5B7,
	LudiqScriptableObject__ctor_mEA1CF35A7B3BAA4B07941A50023E6D2A79F42F87,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SingletonAttribute__ctor_mE4483DC8C17D6081B95A1905CC76403F5585B52B,
	SingletonAttribute_get_Persistent_m2EFEC55D21A0D5E42FCD1F50CF6F5AD37453D618,
	SingletonAttribute_set_Persistent_m581349C686F49DB2CF7C05E04375DE24E78BC103,
	SingletonAttribute_get_Automatic_m0D0360C87EA09DFBEB983A531ABB8AD722938B43,
	SingletonAttribute_set_Automatic_m9E2E6F4618A0AD9F4C785684E00DE1BA9E452518,
	SingletonAttribute_get_HideFlags_mF438190953DB0E5AC7670D86186B130BE244B7CD,
	SingletonAttribute_set_HideFlags_m4040377AD3624F4946BBF9E76F877E17D4C2E766,
	SingletonAttribute_get_Name_m0C2C867429B94CA6AE16403AEE5CE7E0628CF3DF,
	SingletonAttribute_set_Name_m09450F7BDECB475532DD96B40E6F622208CE0275,
	UnityThread_get_allowsAPI_mD5F65F1DD7EB660A36B87BAAC6F346FC41B7EADD,
	UnityThread_RuntimeInitialize_m85A2C09B6D8474B13CC2F1EE1B6E7BC91E482EDC,
	UnityThread__cctor_mA06A81D8293AFDDC8E9B4899A342843052D20957,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Recursion_set_safeMode_m7300893217499247EA9F97AEA8A3B9128CD49E54,
	Recursion_OnRuntimeMethodLoad_m278652A8C70DEC3283FE5DC1C419613D5A601979,
	Recursion__cctor_mADF5B2B7B757832C0ACAEEB220754DFFBFE29DE2,
	ReferenceCollector_Initialize_m574BDBCDD36CE25A2B8E839EBB7365285149DA22,
	U3CU3Ec__cctor_mE857E2EB95F3BA16B14C357CE4A277FFA8574DF9,
	U3CU3Ec__ctor_m06358C0A5C784796096CF1C08035A61D4529DB0D,
	U3CU3Ec_U3CInitializeU3Eb__3_0_mDCDC7B052AF3332850F77AFB6123C9AFF02443BB,
	ReferenceEqualityComparer__ctor_m335A10C125C91D936A1AC64F5FCF0F95810F29CF,
	ReferenceEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_Equals_mE6E4ACBF46F0D537E34F1E3310BEFE42649CF15A,
	ReferenceEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_GetHashCode_mCE7C7ECBFAA52764FA101B54A143C07A13D17268,
	ReferenceEqualityComparer_GetHashCode_m08759602B1C135CAB0976FB150F9FE28D5898787,
	ReferenceEqualityComparer__cctor_m925EFC6A80AEA9AC8EA94B7DEDEA8CB4ABEF792A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RuntimeVSUsageUtility_RuntimeInitializeOnLoadBeforeSceneLoad_mA585350F53EDD32CC1D28F89C58D4A3EABD65F1B,
	StringUtility_TrimStart_m4191946C2A5B0F1D5E22B3CA40EA28AA9AE964D7,
	StringUtility_PartsAround_mA21406BFF73C1B226F8D6F2AECE0E13F75249A5E,
	StringUtility_ToHexString_m4FFBE792B82A834F0FD1854A887611B08B381563,
	StringUtility__cctor_m4272A2025A6775E07ED6B4FB6415C955A6DF0B38,
	UnityObjectUtility_IsUnityNull_m0FE15C8DFA320342403361EA2AC22C9B3E9E3CDE,
	UnityObjectUtility_ToSafeString_m84CD790BE6B992B0B0643EA094D177FDAFBFA3E7,
	XColor_ToHexString_mB2541847421172380A8CCAC744CD1133FDC901BA,
	ApplicationVariables_get_asset_mAC82AD0A8791B3E45066F5A5CD1BF6CDC1F4335E,
	ApplicationVariables_Load_mD4C28B27245BCB69584579C07CE3A84BA0580703,
	ApplicationVariables_set_runtime_m779787A48A8464A65ABC60F0545215A408BAE22B,
	ApplicationVariables_OnEnterPlayMode_m158EB87217011E5492FE939689B5A63730EDBF6D,
	ApplicationVariables_OnExitPlayMode_m713CE18DBFEF16162B560A33B9AAA5B002E18B5E,
	ApplicationVariables_CreateRuntimeDeclarations_m8705F9B91A9977A4FB2B86420E332753EB30C664,
	InspectorVariableNameAttribute__ctor_mE877B6A2AB7668B0C54FAF5200B7F891ADCF6A7E,
	InspectorVariableNameAttribute_set_direction_mF82F757A0C28155744312E689BE39A85D031019C,
	SavedVariables_get_asset_m2ABC58FB92A5DFFF415AE795F234E2C7C88F442F,
	SavedVariables_Load_mA346021F739C7CD07F6C86A1A2ABBFE99479A38F,
	SavedVariables_OnEnterPlayMode_m8348B68A4565CC44CB32D77CC2558A5127095790,
	SavedVariables_OnExitPlayMode_m4705B846AABFBC1C43E2DA56F4F888E3B261B805,
	SavedVariables_get_initial_m55156EC1B7BC87F6BDDC305AFB9D9DA8454C351F,
	SavedVariables_get_saved_m68970298C6A788AD12D1B6E6DDA4481748996E46,
	SavedVariables_set_saved_mFC0FE359C1E74F9189BD61FD4C1070361597F010,
	SavedVariables_get_merged_m2FCEA0A2B856980331A13741C41D97C0B40E4C85,
	SavedVariables_set_merged_mFBA9F3625CD79A8454E808C586DA4788E3F9E09D,
	SavedVariables_SaveDeclarations_m1633E78588DD9F7DB9645B7DD857C79D31FD354B,
	SavedVariables_FetchSavedDeclarations_mB016AD9358254B042630C551E927A6650EE1C75F,
	SavedVariables_MergeInitialAndSavedDeclarations_mA2806AAA01CCDA45C049CA46CD4E8140CDCC0C27,
	SavedVariables_WarnAndNullifyUnityObjectReferences_mA5149E683327A624F90EEF1ED0C5CFDAA3DCC5A7,
	U3CU3Ec__cctor_m24A2FC17A308D9E94CB89C033FBDEA5664137202,
	U3CU3Ec__ctor_m4FC04D2A0A4E6BE891C34B7F6DC34ACFEAC10527,
	U3CU3Ec_U3COnEnterPlayModeU3Eb__8_0_mC0A93597FC90BC9463C4B338463C80262E6AB277,
	U3CU3Ec_U3CMergeInitialAndSavedDeclarationsU3Eb__24_0_mE09A90014F06CE1B8AB7B9CB16C4EE906C7BE7B0,
	VariableDeclaration__ctor_m19DDC2BE1A20663AA762A2BED1B1BBF3B6B4A00B,
	VariableDeclaration_get_name_mAB897FA47D6F56121A391BD082EDE439E7BCA460,
	VariableDeclaration_set_name_m1236B1FA852575CA8389C823FDAA23E661BB2E74,
	VariableDeclaration_get_value_mCDAD586AE963B276956CB9D0E6BD1E7B9CCBAB9F,
	VariableDeclaration_set_value_m623EC0A2B1A37614E02A5709DD3A8735A47CF9E0,
	VariableDeclarationCollection_GetKeyForItem_m58A5E51177B533651A31A632C8D271156E089B6C,
	VariableDeclarationCollection_TryGetValue_m8A8DBD09FFA20967E9F5688A22E871733F566B74,
	VariableDeclarationCollection__ctor_mF1E29200884D4A1C4B6E1B69003B84549AA38B42,
	VariableDeclarations__ctor_m3E547EDC44BB015DC5EA65D7EA885A42B0B207A9,
	VariableDeclarations_get_Item_mD67E8FAE86B3840FE88D0ED3A36517A9B14267DC,
	VariableDeclarations_set_Item_m921139194A666C1B540B69A845B64CF520BE5655,
	VariableDeclarations_Set_m09832F170109B10027857DC25E918EB5A986F538,
	VariableDeclarations_Get_m0192852426CBE4530276D02A82DA16BF17102F49,
	VariableDeclarations_IsDefined_m85E475111442886C76BB3A4E3782B404A5C650B8,
	VariableDeclarations_GetEnumerator_m3DBC156D137712C181A630DB5D3322ECCD1E4C77,
	VariableDeclarations_System_Collections_IEnumerable_GetEnumerator_m57030E53D229728DAF44ED2E4F0F6788EDC7030D,
	VariableDeclarations_Unity_VisualScripting_ISpecifiesCloner_get_cloner_mBDD534757C69C2145DDCFD80D43B203A63305A5B,
	VariableDeclarationsCloner_Handles_m21457B45501D8901B9C4265DAD6ACE452D81E065,
	VariableDeclarationsCloner_ConstructClone_m58CD892EBB1B6C0E33A2B7C51266E9BA1B7A57D1,
	VariableDeclarationsCloner_FillClone_m1BB86F85D412EE0441AA71D3138B6CB9204AE938,
	VariableDeclarationsCloner__ctor_mCF3117747FC69E08A8FFB1A1702B0AD88116916E,
	VariableDeclarationsCloner__cctor_mA3E82395967E0BF0DF1854B1E8D80A4E52909CA6,
	VariablesAsset_get_declarations_m9939ACC84678A6CE414DD508746C4C0DB8BADA4B,
	VariablesAsset_set_declarations_m31CB793840DA5F2D835086D5A56A5EC36D033C60,
	VariablesAsset_ShowData_m2C0E7F5E69407605A5122F7F7F0308333DB59666,
	VariablesAsset__ctor_mC5FE118E6F88D54FCE5C1BA8D33BFC9C01FC7357,
	VariablesSaver_Awake_m852AAA1C45F0B404F20D4B280D66E26A8479F1B6,
	VariablesSaver_OnDestroy_m747AB9394CFFF257C050AF0F256ED90F9A6C1703,
	VariablesSaver_OnApplicationQuit_mD4D6F0F37729530E78362A7E5C90E8C6CF5B405D,
	VariablesSaver_OnApplicationPause_m52633E88C2830E0C922A1DCD93063DBC58CE36A9,
	VariablesSaver_get_instance_m68C6C68569AFEB49DD9C687B73B81EEC72219CBC,
	VariablesSaver_Instantiate_m0BE1F7A1FDC76B91E4563340A5F8116D6E3A1092,
	VariablesSaver__ctor_m0D71CC9CC4C45098D340EC3477BB7E696A9F0F4A,
	fsArrayConverter_CanProcess_mC812F63BA9DA4087CC82E0E869B5F7390FAC81C1,
	fsArrayConverter_RequestCycleSupport_m0A7F56F448ED67FB95EE6163FD6A0E5F20992678,
	fsArrayConverter_RequestInheritanceSupport_mC81F939D38E5BD04E2E75E8C30F6B5634A86167C,
	fsArrayConverter_TrySerialize_m06455924AD13F62F26F5AC4A8857AC3C6C920925,
	fsArrayConverter_TryDeserialize_m1ED67A6A2F6AE5A630F8BDB4EABC3E748459D34D,
	fsArrayConverter_CreateInstance_m69271B347C7C9E69AC7E1544904B8D5821321EED,
	fsArrayConverter__ctor_mF3DB4719DA4C53D0645C14406F15106CBA69F326,
	fsDateConverter_get_DateTimeFormatString_m7060267388902A4A213718EBDB898926BB64CBA4,
	fsDateConverter_CanProcess_mC55064B69B219D9686EBB81918F714C012A40ECF,
	fsDateConverter_TrySerialize_m42983F2FB45C8C8B9D2CD66ACAB58790659AB86C,
	fsDateConverter_TryDeserialize_m2FDEC499002F64CD557C7BC26F08400AB9086941,
	fsDateConverter__ctor_mE7B2A85671B95BEE2B13D8AB2F3A443EBF5E83CC,
	fsDictionaryConverter_CanProcess_m0F2C9585AB012FAEF9F2AEA0E3A81CB4FAF1CC08,
	fsDictionaryConverter_CreateInstance_mD873B9FC931CBBE18F68222E1E02E8469BA015F4,
	fsDictionaryConverter_TryDeserialize_mCE4A4465D0860BBB22C2D53F7E8C5DAEAB537952,
	fsDictionaryConverter_TrySerialize_m7670C41256D3265FA8FB68CA82652A6057E6F1C6,
	fsDictionaryConverter_AddItemToDictionary_mBEA790A8C84CA37141202830ED648FAB52BC446D,
	fsDictionaryConverter_GetKeyValueTypes_mDB5DAA4B2785D2BADBA9CFC94FF2895FDDAD64CE,
	fsDictionaryConverter__ctor_m668F4BE65E6467436373F586FEAD37F472A55ACA,
	fsEnumConverter_CanProcess_mB7E95AEBA9B47FC761C98C350E92838DCE26A634,
	fsEnumConverter_RequestCycleSupport_m312A3C38F2910A267A44D687D833D4EDD9EB6D91,
	fsEnumConverter_RequestInheritanceSupport_m61A12023AEDA7FD27C68AC605EB5FCD9C4EF696C,
	fsEnumConverter_CreateInstance_m83AC52BB50846317BB08BF009F18CDD3F6DE5561,
	fsEnumConverter_TrySerialize_m9EE5BE8BF13D15ED7CCCED9D50A9B506BC30EE50,
	fsEnumConverter_TryDeserialize_mC4B539334A518C125E7AC968D21086556B2B25B4,
	NULL,
	fsEnumConverter__ctor_m60A7A37304B4B655B135261505092177EB5271CF,
	U3CU3Ec__cctor_m6707E13BA8E1A8DC4E5D8353FE5ED3B9C7DBA024,
	U3CU3Ec__ctor_m15D4E64BD9ABA845A5F475B1A76567EE30FB8BDD,
	U3CU3Ec_U3CTryDeserializeU3Eb__5_0_m3BEA44FB419581664656FC13ED434B30D9890898,
	U3CU3Ec_U3CTryDeserializeU3Eb__5_1_m019B199D427FC38370BE36AE8A8F1EB0A5E60B8A,
	U3CU3Ec_U3CTryDeserializeU3Eb__5_2_m326BBED045D120714F928CC40F53B443CD235562,
	U3CU3Ec__DisplayClass5_0__ctor_m8E355725F37DCF563CA859BD8ADD2CD2C71E5C87,
	U3CU3Ec__DisplayClass5_0_U3CTryDeserializeU3Eb__3_m6820BCBED4F823DFBFB958884E53012470332A11,
	fsForwardConverter__ctor_mB658EBCDE1BF8CDAB66C52D3EAC9B8A1521BA45C,
	fsForwardConverter_CanProcess_mE762E41568C5E18DBC4904E92F67D12C37830446,
	fsForwardConverter_GetProperty_m2B901658700E58C0FD7D005FC00D9CC270686F65,
	fsForwardConverter_TrySerialize_m11974CAAD5374B236CAF2C0467F7B29E80A24976,
	fsForwardConverter_TryDeserialize_mDE2FE372CF24AE333C56CDCC52EBFCB271919188,
	fsForwardConverter_CreateInstance_m72B8E32F657BFD6AFC09A14E614C43284D389DF2,
	fsGuidConverter_CanProcess_m9BDF04BB4DD6D4B3E58B5D15E0452618BB4DCA29,
	fsGuidConverter_RequestCycleSupport_m3DBFC29137F937A7813E0B15BFAD3DE0B91D77A7,
	fsGuidConverter_RequestInheritanceSupport_mC309A9B8F6D17F76644AD2D93C3BB2D3739164ED,
	fsGuidConverter_TrySerialize_m12F5DB4BF4E420D8110F75B863EB4E125E84D686,
	fsGuidConverter_TryDeserialize_m42EFB6BFDFF52BB1DE9F7350A836A92A5AD99788,
	fsGuidConverter_CreateInstance_mB9260EDF8704C0F3869417571FB8E82F071261C5,
	fsGuidConverter__ctor_m55052DA5223263915D2BC2AEF25B124274525728,
	fsIEnumerableConverter_CanProcess_m6C7D24E9CF92E0962C6B5E366934A8914F65F21F,
	fsIEnumerableConverter_CreateInstance_m64AAF627F2DCC6302CCA1BF54333EB9B84BCE3F1,
	fsIEnumerableConverter_TrySerialize_m494915090B70D63C3FED5AE22044BFBCF90FB253,
	fsIEnumerableConverter_IsStack_m71CB1418705BAD0698E1DF662B2F04EEF5CC3CED,
	fsIEnumerableConverter_TryDeserialize_m9EE147DFCA4E87D72745310A09C008D74873FE9D,
	fsIEnumerableConverter_HintSize_mC8612A00BAAF5DA7B2CECDD4E302200F99BAEF43,
	fsIEnumerableConverter_GetElementType_m17EA40AC5698BF0A99FA0F5ED94EB6095D217582,
	fsIEnumerableConverter_TryClear_mD876D5946943670A3A0AE2F4F588064A48C36D06,
	fsIEnumerableConverter_GetAddMethod_m5C9E232A6434AD298218EB1C9F4457684F2CF9B4,
	fsIEnumerableConverter__ctor_m5543F512D94FBAB49D28753CF3912F4AE5798E9D,
	fsKeyValuePairConverter_CanProcess_mA592307887176EC33A92C1E4A9E3C0CAFC19B138,
	fsKeyValuePairConverter_RequestCycleSupport_m19CC77ECC3D2F75FD3A85EE8317D74BA6440CCDD,
	fsKeyValuePairConverter_RequestInheritanceSupport_m5CCE9E420FBC8EFB080B1C96BCEF424B3911129C,
	fsKeyValuePairConverter_TryDeserialize_m2C5543FF62C82E68164B87CFBFDBCEDF3ECFEFE9,
	fsKeyValuePairConverter_TrySerialize_mCEAED5E05508FA18654A11735CA8467772BBFE98,
	fsKeyValuePairConverter__ctor_mB2D131F5758DA37234271F8DF86CF66644030ACD,
	fsNullableConverter_CanProcess_m1A706E340440C15213F125EB106628B06C03F455,
	fsNullableConverter_TrySerialize_m39EAE7FDE4F6A1D4120A5E9FB85C7F5C4654B6B8,
	fsNullableConverter_TryDeserialize_mBCA7A072A80F873BEF6EE840DEA7EC096A768633,
	fsNullableConverter_CreateInstance_mF0AA1385488BDD894E97916472CE67CF0F0AB418,
	fsNullableConverter__ctor_m6B019ABC41E275FC29C0C002C25E61AD303DB349,
	fsPrimitiveConverter_CanProcess_m26DD96F0878FD3ECEFF0EDB17D6E0112CE533CCE,
	fsPrimitiveConverter_RequestCycleSupport_m64A8B1EED78BB79C1E5BB5B4C7FCA836D49E8CBF,
	fsPrimitiveConverter_RequestInheritanceSupport_m867B4302EE1E04FD668A159410CB0155F34C3ECF,
	fsPrimitiveConverter_TrySerialize_m825861168AFAB6D96AFEC21203DC967EF6417459,
	fsPrimitiveConverter_TryDeserialize_mB7E511360F5E0EB46232E90D0D70FC62A29DAE9F,
	fsPrimitiveConverter_UseBool_mFAF124DD783CA5CC22568BF064CEFCF0A5C23D49,
	fsPrimitiveConverter_UseInt64_m33413FBA83B3FA7DC50ABA4678D8123385D1CEF6,
	fsPrimitiveConverter_UseDouble_m2D0C1A06EFFDBC9DD1E2294D8083901E827164AE,
	fsPrimitiveConverter_UseString_m6AC59DF7D2E4A2C1B8C29CED600C44574ECF3CFB,
	fsPrimitiveConverter__ctor_m09F9AAC8DDBA0AD9D03FEAB2E413EAE812073340,
	fsReflectedConverter_CanProcess_mBF06950794A6274696482F9EC4F9F9FB888ADA37,
	fsReflectedConverter_TrySerialize_m8E6B54451736AE514D422B3FECD54006D0BA3133,
	fsReflectedConverter_TryDeserialize_m513AA91805BA657A339C614A96C9C8182D98869D,
	fsReflectedConverter_CreateInstance_m03A9A5B8D678763FDE97EC8E029D332F41E1E949,
	fsReflectedConverter__ctor_mC1B10DE2E5B49784B0FC658CAA28814F555647A6,
	fsTypeConverter_CanProcess_m91B9CE689C4CCAE928291938CE940AA165F4A42D,
	fsTypeConverter_RequestCycleSupport_m39201AC284A5288BD9C0B7414019CF0450F53C09,
	fsTypeConverter_RequestInheritanceSupport_m770B87C422596DF9CD79FC17464C0D083CDCB2A1,
	fsTypeConverter_TrySerialize_m5C85B08391033D1A3ABD90DED6660C90CEEFE622,
	fsTypeConverter_TryDeserialize_m11D4FC25E9920438E8CDFE40FC8EDF0CB9130485,
	fsTypeConverter_CreateInstance_m1B47139441FD91D866882C8603F28938B869D0A5,
	fsTypeConverter__ctor_mB3785D775EC38A17971F369D089F39FA835D087B,
	fsWeakReferenceConverter_CanProcess_mFAA2163DFDD567276F9DCB1FC44C5AC0CD4618DD,
	fsWeakReferenceConverter_RequestCycleSupport_m8376F8CD4E3E179ABA1401B75BB31E5CFDF61E0C,
	fsWeakReferenceConverter_RequestInheritanceSupport_m4EE0E35271996B23F70D17ED61F1B0549CA664ED,
	fsWeakReferenceConverter_TrySerialize_mB71B83716AA3FA2CADB4F34836055A7B7314F279,
	fsWeakReferenceConverter_TryDeserialize_m2B488EEB987C1DB9E969066BE54FDA76104E8286,
	fsWeakReferenceConverter_CreateInstance_mAE847039874CB4CE7B2D67F72BF1088EAEBA790E,
	fsWeakReferenceConverter__ctor_m961A93BBF5A2B9E276E52E1764917A8A3D48469D,
	fsConverterRegistrar__cctor_m06F8018AD5F3307316F0DC9162EBB268ABA6C868,
	fsAotCompilationManager_AddAotCompilation_mC52615432A98FF68630197F195080908FB443A19,
	fsAotCompilationManager__cctor_mFF57869A10E0688492F69111655BEEF3A4AB22EB,
	fsBaseConverter_CreateInstance_m560B7E27C98998D4E16AB5223E6D14792C7EE213,
	fsBaseConverter_RequestCycleSupport_mB28BD5D386B27FE5AD8C7C823569FEB489AAB898,
	fsBaseConverter_RequestInheritanceSupport_m847B97D52071D2C8AB8FCEB0A2B79E233083B720,
	NULL,
	NULL,
	fsBaseConverter_FailExpectedType_m7016D6FE93F844E574F232EFF61942E2E07015B4,
	fsBaseConverter_CheckType_m14C4D0CAF424DF9F14D6365B5E5681C43FA82F32,
	fsBaseConverter_CheckKey_m82204341C573CEB47CC62BD43C0522CFE648F3E1,
	fsBaseConverter_CheckKey_mD4F876A6B4BE2208484A4917AD3E3DAC9DE3620A,
	NULL,
	NULL,
	fsBaseConverter__ctor_mDC69FC94FF8A58F044B12046BBA96F994430A2FC,
	U3CU3Ec__cctor_m8BC66C7D54482B015159B5F437EB8C26763EC106,
	U3CU3Ec__ctor_mE8CC346CCBC60E7018B5B4BE957B2330AB84D54C,
	U3CU3Ec_U3CFailExpectedTypeU3Eb__6_0_m537F189F92A9F7D906D2EDD9659143C639C621A3,
	fsGlobalConfig__cctor_m7706ACB33CD10F7B39B7B53B802F7AE7B6CAC775,
	fsConfig__ctor_mCB5CF40444C2E640B4141CFE2B8700ADCC08E839,
	U3CU3Ec__cctor_m95D819656FB95982AD3DD0AD070FA011C087C8BB,
	U3CU3Ec__ctor_mC79C18C2451830A98579F11E7C7BBFB4CB5E8FAF,
	U3CU3Ec_U3C_ctorU3Eb__10_0_mBD487886165A2FB0F5AB8F396C98A27C9D1B5DAE,
	NULL,
	NULL,
	fsContext__ctor_m48FB2999693498F2BCE7A978A76773DF86E290DB,
	NULL,
	fsConverter__ctor_m3C3DE66DA4A3FA50ED9A7D0F10EF70BB0D5FEA26,
	fsData_ToString_mB9B72A2286B2AADA8582221CDE13712F3F3C6C9F,
	fsData__ctor_mA4E52DCE50BB43C4429E77519CDBD113343180DC,
	fsData__ctor_m740FD2D0D7D78B72181DFC0216FED95818893268,
	fsData__ctor_m9C5AE8C2391D7773629D904624B36357DB3F7A24,
	fsData__ctor_mEF553618B93DB72D7816FD84740EC37A942F129C,
	fsData__ctor_m144DD5B09AD525C0F94B2E1750A4256495266F66,
	fsData__ctor_mCF4D73BFD9271596000ACC3E17988E3492236781,
	fsData__ctor_mDBA39D40BC739FD480F7C2DA53570109CA466B39,
	fsData_CreateDictionary_m90ED2CCE0AE0A70F6E6D91181080DE89DCA7C53D,
	fsData_CreateList_m99EDBD341F412886FD95A18B4AF1E1B763C10E95,
	fsData_BecomeDictionary_m1375A313DDB0E7DBFFB609A807DA2EE916AF644C,
	fsData_Clone_mEC41F9EA408E94F8D85EFFF6EADD9DF1B7CA54EF,
	fsData_get_Type_mCDB1F38635730E10B61C3D62B8D64C8F5AB845CE,
	fsData_get_IsNull_m2D1318B1C77BD8D5C5C87D52400D1B9239815A2C,
	fsData_get_IsDouble_m4895B3C81A738E2907493684593AE745943B6936,
	fsData_get_IsInt64_m53E266DA7DED5DBC5CA5CE79F7BB9C25237B7E42,
	fsData_get_IsBool_m5C55D9E43C2D8B6840411DF6C192113E60050367,
	fsData_get_IsString_m1B40F32297A731D5A4EB637438B95632453C84B8,
	fsData_get_IsDictionary_m323625E7633DF9747CF9FAF44C0E5E9BBA4671E4,
	fsData_get_IsList_mE4BFDA69E183AB45F4BA89D3F95B630341CF36A2,
	fsData_get_AsDouble_m5CD93E5F7A8DBE8646C880F0F561CD91613F4D2E,
	fsData_get_AsInt64_m1C652F5AF0A3C8EB84183204CF3AE983AEC3441E,
	fsData_get_AsBool_m34337669439083657DDAD711DAFD463858A2FA15,
	fsData_get_AsString_mCF0FBBF9D300BCAC9E0F1C7B6C9DAA4B8AFB9570,
	fsData_get_AsDictionary_m51779E71BBC994A7F5036BFB43F61B28C0817D86,
	fsData_get_AsList_m0B2AF972F10AE6F9A8EA9157BC27FC0316BB33EA,
	NULL,
	fsData_Equals_m28C1452B6B9094F2F8650100539701AE322BEBFE,
	fsData_Equals_m693932B7DA39DBC63C1B528A9530AD5B51009E9C,
	fsData_op_Equality_mD14A5DBF98FAA29F99C352197F711324CC4A0107,
	fsData_op_Inequality_m678ABE3BFD1DA4C7FFE5236BF1C842B3901A4EF6,
	fsData_GetHashCode_m0527A5668429A615A6812061DFA50E592E1F4F82,
	fsData__cctor_mA6352A2037DD0EDF62944123ED2B03E63225C67D,
	NULL,
	fsDirectConverter__ctor_mCE6F7898DA2A3FBE101F5939D06A809E3164ABE1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	fsMissingVersionConstructorException__ctor_m46EA05FE262E8C98465A921D2B2EB6035ED3FEFB,
	fsDuplicateVersionNameException__ctor_m0CA794E21F56EBADEED2971C6E9DC603CAA0AF51,
	NULL,
	NULL,
	NULL,
	NULL,
	fsSerializationCallbackProcessor_CanProcess_mC382EF1EA240B657B95653DF7A345748648B7D22,
	fsSerializationCallbackProcessor_OnBeforeSerialize_mD345A5E296EAB4995B767549EC297AE842C98EA9,
	fsSerializationCallbackProcessor_OnAfterSerialize_m04ECE444A656E46901FDFA8A4A50CB88B58829A3,
	fsSerializationCallbackProcessor_OnBeforeDeserializeAfterInstanceCreation_mCB42E40ABF7205C44D09BFAEBF79DD7ED2E8BB62,
	fsSerializationCallbackProcessor_OnAfterDeserialize_mBA672AADF2BE57D975BA57E3ECBE67CF53254C9C,
	fsSerializationCallbackProcessor__ctor_mA53988890D8BB06D89990CABF451036BFB686968,
	fsSerializationCallbackReceiverProcessor_CanProcess_m70B299D9E2829CAD22276102D7A83EA98EC9ADCE,
	fsSerializationCallbackReceiverProcessor_OnBeforeSerialize_m25ABF450D793FD0619FF1F7625E1CFB5144DEFB8,
	fsSerializationCallbackReceiverProcessor_OnAfterDeserialize_mFF0DAEAE073EDE4E227E46A97DB9C6F93A3EAC1E,
	fsSerializationCallbackReceiverProcessor__ctor_m8F7AD77AE53D0C041389A60F0787447745505921,
	fsJsonParser__ctor_mDF3573B996535708BA931F67A6831FA2082EBDDD,
	fsJsonParser_MakeFailure_m954A9022A21F0A861B51A0D990B4119B22CD3F79,
	fsJsonParser_TryMoveNext_mEA865DF9A93335D212815AFF263D9B24B85BBB57,
	fsJsonParser_HasValue_m1CA829DFE012BE97368B282E74E1726358F06E07,
	fsJsonParser_HasValue_mF7A6023B0B43CCE0E45CD2EB994E0F73F168336B,
	fsJsonParser_Character_m65CBDF5471FF5B35581B7EFEAE6EC6372E04586B,
	fsJsonParser_Character_mDDE622FD95D90CE0D28BCA6BC3BF26E751C8EA86,
	fsJsonParser_SkipSpace_mD9A6A4A11F3A5BB3B1F04637B1F271888001A7AD,
	fsJsonParser_TryParseExact_m505485875C284E11FA456B622D67DAB70E21D887,
	fsJsonParser_TryParseTrue_m24EC3E78B57CC6F2D509AA248E77EE2A85E233C0,
	fsJsonParser_TryParseFalse_m596BC9F804F6B9401B40EDB5237AD29B4D3D80A5,
	fsJsonParser_TryParseNull_m843F34234DB664D0543CABF0E220F750ED46064D,
	fsJsonParser_IsSeparator_mABE1E2ECF0F25F34AD7DD13F88B79126101BA6E0,
	fsJsonParser_TryParseNumber_m2E21CD4F0C7523B4D27DB12562D2246F245978EB,
	fsJsonParser_TryParseString_m4257104805D84B9015D377222C7101E9E96600A4,
	fsJsonParser_TryParseArray_mB56A5AF71B2A7D1561B823E1F377754D5FE8B43A,
	fsJsonParser_TryParseObject_m7B7190173328FF691F900E784239160AC4AEA6A4,
	fsJsonParser_RunParse_m0A394E05ACEFF4E6F796992D5C9196AB233A3025,
	fsJsonParser_Parse_m8C6D52CF715A543C1CC24E79221A3E13EEBF3AC1,
	fsJsonParser_Parse_m8BE8A63CD24E3C0D05A65A977FF70AC8E1D34336,
	fsJsonParser_IsHex_m48F2F8DDD813FC39FCF6977E477217C949B99A57,
	fsJsonParser_ParseSingleChar_mD88F637A84EA790C3F29F23648DBF64DA14DB323,
	fsJsonParser_ParseUnicode_m45633DD01111D8B14D037163008AF1D036AB9B70,
	fsJsonParser_TryUnescapeChar_mE8EB2102F384B53B9847BA88CCE4DE6CCCB91456,
	fsJsonPrinter_InsertSpacing_mB7AF68DA90DB29D402F6C71992131038B1F55BDF,
	fsJsonPrinter_EscapeString_m88827B05B5B4530043982F18C3200A176A94E214,
	fsJsonPrinter_BuildCompressedString_m19AEE93D21E751B171D191701D780E4A1BAEC144,
	fsJsonPrinter_BuildPrettyString_m151C19973019F8D728104F27872EA61548EFC0CB,
	fsJsonPrinter_PrettyJson_m610FD801940DC409B059808A5E954ABAF62CAB8E,
	fsJsonPrinter_CompressedJson_mAD916E32C1E828444E79CE2D32E74BD7892FC767,
	fsJsonPrinter_ConvertDoubleToString_m3A8E7CD06EDB9E9ED63721E3E8A35D30BBD1AC71,
	fsObjectAttribute__ctor_mABB40D348C9AFA8BB3E34A31F2F6DE7620519060,
	fsObjectProcessor_CanProcess_m86183AE7FC4638153EFDF8ADD5FC0C8702A54410,
	fsObjectProcessor_OnBeforeSerialize_mE084D698B97C142C4CA8A7CF9581C49D00A4BF59,
	fsObjectProcessor_OnAfterSerialize_mFC2EE7C13211D4E1F90AEDD9966276C31FDBA040,
	fsObjectProcessor_OnBeforeDeserialize_m6E16DBE8D82C98DA9F2D23674DC6CE04A02B0639,
	fsObjectProcessor_OnBeforeDeserializeAfterInstanceCreation_m60D055099B48A9042D7AC51AAF014EB5D27B80D3,
	fsObjectProcessor_OnAfterDeserialize_m5103A8FA0718C709CC309A39A5E8FE9C30812231,
	fsObjectProcessor__ctor_mCA89DB5E44BFCD8B8C5C57A5CBEB1283EB9B4426,
	fsResult_AddMessage_mC4B690BC3884FF2D235733FD65081C163B4DDD55,
	fsResult_AddMessages_m37B84549F6D482E5C1D8E8761DE4C006D7AB4B9D,
	fsResult_Merge_mB5B8E8B4BD5B6086B6DDFAF478640D9DA56EF0C1,
	fsResult_Warn_m7F899FA4134674AB4FC072A865DF5EEBCBD4AEAD,
	fsResult_Fail_m3315594F29830C02FD560F7401D311F99DA241CD,
	fsResult_op_Addition_mA94A4AD68668E539DEFE1255DC72B9D11A6DE41C,
	fsResult_get_Failed_m1398C627A72E75F5C7F8DB1A7C14E5B3271FF1FB,
	fsResult_get_Succeeded_m01E08C3B731D94618B867B7B55DCC0061CD11769,
	fsResult_get_HasWarnings_m030A020FD6B48D0E8E2867D2428135EB771064CE,
	fsResult_AssertSuccess_m7077B79DD80747B32B66ADCDF49243575800FA12,
	fsResult_get_AsException_m6D92E907B2E0815691BD5639138FB4E4C578EA0B,
	fsResult_get_RawMessages_m0B25A624CB4B2F03DF6FC6077284AF92CFFC4683,
	fsResult_get_FormattedMessages_mCF960860BF1F178EDFCA80D1196B2F69096144B7,
	fsResult__cctor_m5AD662C6FC9BB3FA9701789A9AE2C051D5AE1A44,
	fsSerializer__ctor_m2CF241A6E8D255269BC2694F7B87B8BB7381C739,
	fsSerializer_RemapAbstractStorageTypeToDefaultType_mDAACC633FEB7E4E36783EB6617A943521305150A,
	fsSerializer_SetDefaultStorageType_m91D7A9724B7D2280BA9633B5159887B59A4DD01E,
	fsSerializer_GetProcessors_mB240A96D2D312CE91D3E14DD67CE734BBBE10217,
	fsSerializer_AddConverter_m7EE7A34BDDD2065481BEB0457C9572270588ED59,
	fsSerializer_GetConverter_m3FBA1F6CC05EF20F9F31F1CEE65DCABB40218495,
	NULL,
	NULL,
	fsSerializer_TrySerialize_mADDE6667080A2E2C69A54662C47111A08A7EC006,
	fsSerializer_TrySerialize_m2E029A68D43C8F4783B5912AB8004658E41AB84B,
	fsSerializer_InternalSerialize_1_ProcessCycles_m81B0B13FD1CA775E3686E91C753424BF2717D9AF,
	fsSerializer_InternalSerialize_2_Inheritance_mDDDBA4731B6545294441CB77CB1CEB115C5A2037,
	fsSerializer_InternalSerialize_3_ProcessVersioning_m59970A3730FC476B186B8D9A9E258C4E3CB2C338,
	fsSerializer_InternalSerialize_4_Converter_m0695101440E5D807387BFD82D7B5BD841BC22456,
	fsSerializer_TryDeserialize_mBBA19D9240A719A58C3DA23F2D8D4C538C5C81F2,
	fsSerializer_TryDeserialize_m675340784F47BA9FF50C193F53A3BAAA1D18D2B9,
	fsSerializer_InternalDeserialize_1_CycleReference_m1D9180F91CE5B8505373E3301E63AB1FBAB9FEE4,
	fsSerializer_InternalDeserialize_2_Version_m41956C872601212418C1DAE44AE54FA43265E8A9,
	fsSerializer_InternalDeserialize_3_Inheritance_m43B49A0E8363275937AC5DC9284B87847142886F,
	fsSerializer_InternalDeserialize_4_Cycles_m913C1277065AD471EB7F1F22DEACDE4DE1645521,
	fsSerializer_InternalDeserialize_5_Converter_m30EBD20C91E165C4B9BC87FCB1467B9BD4EE8DF6,
	fsSerializer_GetDataType_m064ECD9CDD126B0705BAB7BE0F6A24E0AC1DE13E,
	fsSerializer_EnsureDictionary_m852412879A44895491BB671AB6D9A29CCD1E7760,
	fsSerializer__cctor_m8FA92415657E595F6071011E2E7E81A74BA8AA49,
	fsSerializer_IsReservedKeyword_m1A656E8EDA5598C00BC12E84623074A18FEF03FE,
	fsSerializer_IsObjectReference_m42B2DF9FAEA83929849E6F0982EF9E21CDDF8F79,
	fsSerializer_IsObjectDefinition_m2D0919684B6C9C269F1B1A803F3113D786D10272,
	fsSerializer_IsVersioned_mE7064A56C3886F675F764EA02DEDF14627EB879A,
	fsSerializer_IsTypeSpecified_mBD85E42C5EFB8553EF83EB0AEA55AA6898DD1A15,
	fsSerializer_IsWrappedData_m177A55FD22E57532B934E70669BC15DF449C8F3D,
	fsSerializer_IsVisualScriptingUnit_mF307CA12573997A8C69C2D06ABA32BBA8F41A6C1,
	fsSerializer_ConvertLegacyData_mA961F5B0DCE7D15FB7C887586CF793D96B820830,
	fsSerializer_Invoke_OnBeforeSerialize_m24BD9C61E67A5AC371884D793C2F3B3C7CFF095C,
	fsSerializer_Invoke_OnAfterSerialize_m6D2CA5F43C91291C965239F884CB603D89C30293,
	fsSerializer_Invoke_OnBeforeDeserialize_m13D482A4E6E930C3117C0A6575BF87C14CDBE3E3,
	fsSerializer_Invoke_OnBeforeDeserializeAfterInstanceCreation_mDE398ACCB3A5DA6D6BDE4692F1F748D20AF3CA52,
	fsSerializer_Invoke_OnAfterDeserialize_m2B62287C736CCBCA14C3A2119A32490A05036974,
	fsLazyCycleDefinitionWriter_WriteDefinition_mE5A307D8E186FF9645CA85A979D7B781C1A8C665,
	fsLazyCycleDefinitionWriter_WriteReference_mACBE88BD37EDA06D992F9FF0A30B0D3BBE33FD74,
	fsLazyCycleDefinitionWriter_Clear_mF81302B0538AD1FBA244D919EF7429B935B1157C,
	fsLazyCycleDefinitionWriter__ctor_m88404815CC83159CB4B9581C0737F8FE44847A38,
	fsMetaProperty__ctor_m8EAD810AB5CC9482CE78ACDAA6D7DCC2680022A6,
	fsMetaProperty__ctor_m6C23AFA33EA55557279A069D19DC87FCCEBBBAD1,
	fsMetaProperty_get_StorageType_m44BED9C0B89B0A58FF1B29E69582E7B5FF75414E,
	fsMetaProperty_set_StorageType_mBC9EEB04E34352346F970CB36245E0CAC10F29E6,
	fsMetaProperty_get_OverrideConverterType_mED201398F793429A572BAC96BBCFA8334F63C6A3,
	fsMetaProperty_set_OverrideConverterType_m37267DED2A75405AF6B2E4FF77802820C8F8B04B,
	fsMetaProperty_get_CanRead_mA2A7D389C2D41C426B303F6A269403879F17964D,
	fsMetaProperty_set_CanRead_m29A21E1EC23A8330A771140563638EBE23C01C73,
	fsMetaProperty_get_CanWrite_m2BD75237BFBF0A4162BEDF112CF33C6C748EF3D6,
	fsMetaProperty_set_CanWrite_mCE48F80DD621B06344EE627E0E71B988A63BC694,
	fsMetaProperty_get_JsonName_mA58DF433A59CBE9214939853C7F4E9D4B838A652,
	fsMetaProperty_set_JsonName_mF0BBA2CA8267D1CCEB6AD349EC15006B2FC68ED4,
	fsMetaProperty_get_MemberName_mED2E167648A8A76B9C0FD73A6405012D82D9EDBE,
	fsMetaProperty_set_MemberName_m910365017A946E12F897AA268E2C54E92D8B0492,
	fsMetaProperty_get_IsPublic_m30FEC58C34CBDB5F59B85174A8B4F1CB9D7FFE7D,
	fsMetaProperty_set_IsPublic_mDDC0D9F1D8C16C97D5A967A66B90C96CF00EFDBA,
	fsMetaProperty_get_IsReadOnly_m0C3E3C1607BF2EE1BFE02073A743AD580EB0EB5A,
	fsMetaProperty_set_IsReadOnly_m81F1BE9C4A906FD3A846B032068D8CF2E7C69ED4,
	fsMetaProperty_CommonInitialize_mED4CA3EF3000E44A9A61F6FE6F487BAD44B5AE5A,
	fsMetaProperty_Write_mEA2E2F7E5E785063984023ABD51657C856E60783,
	fsMetaProperty_Read_mE5F0AE85092593DE88AEDA717359310E9E06C046,
	fsMetaType__ctor_mC6683E69F4CE1C8FE4BC514BD4ED19E33CE35CF3,
	fsMetaType_get_Properties_m54618035A54E08C924EB7E8781AA55C1D19F950A,
	fsMetaType_set_Properties_m75C4CFDD1D8A0844B8E8E61B1028BB013A011A9E,
	fsMetaType_get_HasDefaultConstructor_m6AFFF40736CFD48C95681AF3228409840BB4F81E,
	fsMetaType_EmitAotData_m567914383E93A38ADA1F4D3AE645632CC6982AC4,
	fsMetaType_CreateInstance_m29B05EF9602FCAE885BBC789C04C5EA32D60AE72,
	fsMetaType_Get_m5622F5FF2787C35C795789C1373F6013DCAB4363,
	fsMetaType_CollectProperties_m4ED60C5E38D1D5EC03A3A61803D7F60E21ED2D3C,
	fsMetaType_IsAutoProperty_mC28CBE4BA104DE04A8D082D781C962C39287CC28,
	fsMetaType_CanSerializeProperty_mAD3B5D6C4B122AEBA0895E510A69BFDDA00427B2,
	fsMetaType_CanSerializeField_mD6450E719027D7DC41CA949858689F8B2B87682E,
	fsMetaType__cctor_mE583D07A7F203890BAF8361A65C8885B0155B781,
	U3CU3Ec__DisplayClass16_0__ctor_m538342D591F27E0886D3D9F7969DB43E9F1AC3A0,
	U3CU3Ec__DisplayClass16_0_U3CCollectPropertiesU3Eb__0_mBDC7709CE09425F240C42147C3BB2FDB7E76FAF5,
	U3CU3Ec__DisplayClass16_0_U3CCollectPropertiesU3Eb__1_m086012131CD06B409756038400BF39327BF9F233,
	U3CU3Ec__DisplayClass16_0_U3CCollectPropertiesU3Eb__2_mCA3D8AC6B0001C61241D0164C6089EE68DF7F245,
	U3CU3Ec__DisplayClass18_0__ctor_m409998F2ADDDE7B61682E1940C31EB545A5333A9,
	U3CU3Ec__DisplayClass18_0_U3CCanSerializePropertyU3Eb__0_m9DF628356CB27A717F31CD102E1D609070B00ABB,
	U3CU3Ec__DisplayClass19_0__ctor_m577E5745A5BD61B689CD9DF9A336D664BDE608AA,
	U3CU3Ec__DisplayClass19_0_U3CCanSerializeFieldU3Eb__0_mFA28917E7AF68FAD4A6A425DF445DD5AC3115D07,
	fsReflectionUtility_GetInterface_mECB1C271163A4B35F1CB6F2976818AF0A50AD905,
	fsCyclicReferenceManager_Enter_m28863BBFD4BDEF23F1DCBC9D2FC71D797EB2FDB4,
	fsCyclicReferenceManager_Exit_m91D1DB5745A082CE50E57ABB26B16E0F3661120F,
	fsCyclicReferenceManager_GetReferenceObject_mA7330D4D27E050EE92EC6F5333767307B8E1B7C4,
	fsCyclicReferenceManager_AddReferenceWithId_mEC4A2DE046EF97033AE58B86AA1AADBA88A170C5,
	fsCyclicReferenceManager_GetReferenceId_m30B91E17BE1C02D89F10F705C0EE3FE8F0E0458F,
	fsCyclicReferenceManager_IsReference_mCE7FC77905F712E503F2CBE8D6F02244C74D9437,
	fsCyclicReferenceManager_MarkSerialized_m8CD82B690712E7EEE90E8B7C15EAF22E2E76B952,
	fsCyclicReferenceManager__ctor_m4904103AD3F430576C520F7C2C412C35C10BC1D0,
	ObjectReferenceEqualityComparator_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_Equals_mCF77DB060539A6992A81D2700283385C08751EB6,
	ObjectReferenceEqualityComparator_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_GetHashCode_mFB4BE9ACD40DEAE98F69833C25B3533F8DFED864,
	ObjectReferenceEqualityComparator__ctor_m609265685F45AFF755A5EF6BCF53C9B2FAF2DF64,
	ObjectReferenceEqualityComparator__cctor_m8B819E32F9065E54766F626C4A17B330217E2D51,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	fsPortableReflection_HasAttribute_mD4F1A8E233F51111056C7383B81BB3C2F6034477,
	fsPortableReflection_HasAttribute_m1F9C28B7432B2B5C3DE18ACDF7E12F2318E52973,
	fsPortableReflection_GetAttribute_m54325D68CE95535644F25CD39FAA3D4858745EB8,
	NULL,
	NULL,
	fsPortableReflection_GetDeclaredProperty_m78691C838C06D0C4ADCE259161DE2323504862FC,
	fsPortableReflection_GetDeclaredMethod_mDC22CDA0777452C8D2FE05B2FB07B14B697A1E5B,
	fsPortableReflection_GetDeclaredConstructor_m9F5B44FCB7E14D7E7CF90482A9D1D13C11445C01,
	fsPortableReflection_GetDeclaredConstructors_mB5B777C7EC535253A345584074FC1D8FBA32160A,
	fsPortableReflection_GetFlattenedMethod_mE5EF30F98C8A994F7581740E8ECEDBA6E42C8072,
	fsPortableReflection_GetDeclaredMethods_mF1E5A4D80A892D0E7E9FED20061B23E39E413DB3,
	fsPortableReflection_GetDeclaredProperties_mF26959E95815BD982802EC6160E3DA185B1DC6A7,
	fsPortableReflection_GetDeclaredFields_m8FCC0F8F72BA45796F5C46E49F74E6187790D89B,
	fsPortableReflection_GetDeclaredMembers_m65055614D9291573376917BF3044659CAF23409E,
	fsPortableReflection_Resolve_m995640D6EA5B5B1268075371EC800CF8720BF9C3,
	fsPortableReflection__cctor_m809A0F036D94DD50B44E6986C9AC53DA8215DE41,
	AttributeQueryComparator_Equals_m27EA6B949A2AAF440EFE865A87E20FB9CED53635,
	AttributeQueryComparator_GetHashCode_m538CC0620B54C1478F4B17229A036FADB11E4359,
	AttributeQueryComparator__ctor_m97A20B142642A29462C6A1EEC957529E30965B4C,
	fsTypeExtensions_CSharpName_mA1935C2EB35A9B042BDC965DA3A75B31F60B5671,
	fsTypeExtensions_CSharpName_m7B9CEB55166AB73F3E8475491EB08E4F91475FE6,
	U3CU3Ec__DisplayClass2_0__ctor_m80831530C50B87BB035A96D17E242F8B7011C105,
	U3CU3Ec__DisplayClass2_0_U3CCSharpNameU3Eb__0_mB271DB9009C764D4CB33A03876DB554A4EEFB2C3,
	fsVersionedType_Migrate_mA2431BCBC723C85F909F6D50CCC218ABFAED65AB,
	fsVersionedType_ToString_m584F4EBD02BC6D0502664D2047FF3D71270A359F,
	fsVersionedType_Equals_m051C07595BCB7037C2E668E0E09D0A4C74A2958F,
	fsVersionedType_GetHashCode_m02754D5563950758CECD86BE72D69DD7046C47C6,
	fsVersionManager_GetVersionImportPath_mF78001EFBFAB16E1FB6D627FB2327FAA6382BCC6,
	fsVersionManager_GetVersionImportPathRecursive_mB3A3038031C067D3C072AC7FA1A6483F923360B4,
	fsVersionManager_GetVersionedType_m4CF940685782769880C5341A1C3D2FC703906484,
	fsVersionManager_VerifyConstructors_m8411C5605527031779BFE91B425DF200A968ACD7,
	fsVersionManager_VerifyUniqueVersionStrings_m930312E5E49A7FEFFAED1994F0B9633BC9BE4276,
	fsVersionManager__cctor_m54A1308F62862C4480603F2633A6DAB7362EBF92,
};
extern void ProfilingScope__ctor_mF9BE98497C393234478FA7985004C5987D1144EE_AdjustorThunk (void);
extern void ProfilingScope_Dispose_m7F66BF3E60126E49FAFA1E38F6126D71CFCDBB44_AdjustorThunk (void);
extern void ConversionQuery__ctor_m8486070E4307ABFAD499BBFD5694FC44D47A4B59_AdjustorThunk (void);
extern void ConversionQuery_Equals_m433F18C3D368EF871293F990F3433EA2A2DCE7DF_AdjustorThunk (void);
extern void ConversionQuery_Equals_mEBBAF7F90C0933C8072AFF2A88FBE2938DB9629F_AdjustorThunk (void);
extern void ConversionQuery_GetHashCode_m19A38C82ED3B33971CCA4792FC48A99BA21BBC24_AdjustorThunk (void);
extern void ConversionQueryComparer_Equals_m1227EF34810F4D09B03F2B6D54B5B8E1BCC93AF7_AdjustorThunk (void);
extern void ConversionQueryComparer_GetHashCode_m0AAD5740A28AA6CB55CAAFC400083D84AE5D98C5_AdjustorThunk (void);
extern void LooseAssemblyName__ctor_m8834C59F1E492EA1A935207B7134961B35DCE8EA_AdjustorThunk (void);
extern void LooseAssemblyName_Equals_mA5DB041D4D8667E44F9F1CB82A5C0BBE84E7E7B1_AdjustorThunk (void);
extern void LooseAssemblyName_GetHashCode_mB97F58ECA80BDA748C4D1A101386F6E2AAE2494A_AdjustorThunk (void);
extern void LooseAssemblyName_ToString_m30BA77AF842E4FD31F5E422190DD9DBDADA9A3FE_AdjustorThunk (void);
extern void SerializationData_get_json_m316B4017338BC7DB9B4D3D9A81BEF1C01232FB24_AdjustorThunk (void);
extern void SerializationData_get_objectReferences_m70704774677025CE081A408A7D558A9309C24DA4_AdjustorThunk (void);
extern void SerializationData__ctor_m590F6C005C219D41968701544B5D4D2A9E2C2607_AdjustorThunk (void);
extern void SerializationData__ctor_m1AECF919A4D6D2B9256D1ECD164E2A8189DE64AD_AdjustorThunk (void);
extern void SerializationData_Clear_mC7B73F14EFA97C2831731F9B3C558765F08EC73D_AdjustorThunk (void);
extern void SerializationData_ToString_m277BCD1A9A8431713FCADB24C479817E0C6A3A4C_AdjustorThunk (void);
extern void SerializationData_ToString_mAC5F79322472876518D60654C6515E4ED142EF87_AdjustorThunk (void);
extern void SerializationData_ShowString_m53B7B7664BCFCE62D2382E53FF7C0205D9034270_AdjustorThunk (void);
extern void fsResult_AddMessage_mC4B690BC3884FF2D235733FD65081C163B4DDD55_AdjustorThunk (void);
extern void fsResult_AddMessages_m37B84549F6D482E5C1D8E8761DE4C006D7AB4B9D_AdjustorThunk (void);
extern void fsResult_Merge_mB5B8E8B4BD5B6086B6DDFAF478640D9DA56EF0C1_AdjustorThunk (void);
extern void fsResult_get_Failed_m1398C627A72E75F5C7F8DB1A7C14E5B3271FF1FB_AdjustorThunk (void);
extern void fsResult_get_Succeeded_m01E08C3B731D94618B867B7B55DCC0061CD11769_AdjustorThunk (void);
extern void fsResult_get_HasWarnings_m030A020FD6B48D0E8E2867D2428135EB771064CE_AdjustorThunk (void);
extern void fsResult_AssertSuccess_m7077B79DD80747B32B66ADCDF49243575800FA12_AdjustorThunk (void);
extern void fsResult_get_AsException_m6D92E907B2E0815691BD5639138FB4E4C578EA0B_AdjustorThunk (void);
extern void fsResult_get_RawMessages_m0B25A624CB4B2F03DF6FC6077284AF92CFFC4683_AdjustorThunk (void);
extern void fsResult_get_FormattedMessages_mCF960860BF1F178EDFCA80D1196B2F69096144B7_AdjustorThunk (void);
extern void fsVersionedType_Migrate_mA2431BCBC723C85F909F6D50CCC218ABFAED65AB_AdjustorThunk (void);
extern void fsVersionedType_ToString_m584F4EBD02BC6D0502664D2047FF3D71270A359F_AdjustorThunk (void);
extern void fsVersionedType_Equals_m051C07595BCB7037C2E668E0E09D0A4C74A2958F_AdjustorThunk (void);
extern void fsVersionedType_GetHashCode_m02754D5563950758CECD86BE72D69DD7046C47C6_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[34] = 
{
	{ 0x06000084, ProfilingScope__ctor_mF9BE98497C393234478FA7985004C5987D1144EE_AdjustorThunk },
	{ 0x06000085, ProfilingScope_Dispose_m7F66BF3E60126E49FAFA1E38F6126D71CFCDBB44_AdjustorThunk },
	{ 0x060000AE, ConversionQuery__ctor_m8486070E4307ABFAD499BBFD5694FC44D47A4B59_AdjustorThunk },
	{ 0x060000AF, ConversionQuery_Equals_m433F18C3D368EF871293F990F3433EA2A2DCE7DF_AdjustorThunk },
	{ 0x060000B0, ConversionQuery_Equals_mEBBAF7F90C0933C8072AFF2A88FBE2938DB9629F_AdjustorThunk },
	{ 0x060000B1, ConversionQuery_GetHashCode_m19A38C82ED3B33971CCA4792FC48A99BA21BBC24_AdjustorThunk },
	{ 0x060000B2, ConversionQueryComparer_Equals_m1227EF34810F4D09B03F2B6D54B5B8E1BCC93AF7_AdjustorThunk },
	{ 0x060000B3, ConversionQueryComparer_GetHashCode_m0AAD5740A28AA6CB55CAAFC400083D84AE5D98C5_AdjustorThunk },
	{ 0x060000BE, LooseAssemblyName__ctor_m8834C59F1E492EA1A935207B7134961B35DCE8EA_AdjustorThunk },
	{ 0x060000BF, LooseAssemblyName_Equals_mA5DB041D4D8667E44F9F1CB82A5C0BBE84E7E7B1_AdjustorThunk },
	{ 0x060000C0, LooseAssemblyName_GetHashCode_mB97F58ECA80BDA748C4D1A101386F6E2AAE2494A_AdjustorThunk },
	{ 0x060000C1, LooseAssemblyName_ToString_m30BA77AF842E4FD31F5E422190DD9DBDADA9A3FE_AdjustorThunk },
	{ 0x06000234, SerializationData_get_json_m316B4017338BC7DB9B4D3D9A81BEF1C01232FB24_AdjustorThunk },
	{ 0x06000235, SerializationData_get_objectReferences_m70704774677025CE081A408A7D558A9309C24DA4_AdjustorThunk },
	{ 0x06000236, SerializationData__ctor_m590F6C005C219D41968701544B5D4D2A9E2C2607_AdjustorThunk },
	{ 0x06000237, SerializationData__ctor_m1AECF919A4D6D2B9256D1ECD164E2A8189DE64AD_AdjustorThunk },
	{ 0x06000238, SerializationData_Clear_mC7B73F14EFA97C2831731F9B3C558765F08EC73D_AdjustorThunk },
	{ 0x06000239, SerializationData_ToString_m277BCD1A9A8431713FCADB24C479817E0C6A3A4C_AdjustorThunk },
	{ 0x0600023A, SerializationData_ToString_mAC5F79322472876518D60654C6515E4ED142EF87_AdjustorThunk },
	{ 0x0600023B, SerializationData_ShowString_m53B7B7664BCFCE62D2382E53FF7C0205D9034270_AdjustorThunk },
	{ 0x060003AA, fsResult_AddMessage_mC4B690BC3884FF2D235733FD65081C163B4DDD55_AdjustorThunk },
	{ 0x060003AB, fsResult_AddMessages_m37B84549F6D482E5C1D8E8761DE4C006D7AB4B9D_AdjustorThunk },
	{ 0x060003AC, fsResult_Merge_mB5B8E8B4BD5B6086B6DDFAF478640D9DA56EF0C1_AdjustorThunk },
	{ 0x060003B0, fsResult_get_Failed_m1398C627A72E75F5C7F8DB1A7C14E5B3271FF1FB_AdjustorThunk },
	{ 0x060003B1, fsResult_get_Succeeded_m01E08C3B731D94618B867B7B55DCC0061CD11769_AdjustorThunk },
	{ 0x060003B2, fsResult_get_HasWarnings_m030A020FD6B48D0E8E2867D2428135EB771064CE_AdjustorThunk },
	{ 0x060003B3, fsResult_AssertSuccess_m7077B79DD80747B32B66ADCDF49243575800FA12_AdjustorThunk },
	{ 0x060003B4, fsResult_get_AsException_m6D92E907B2E0815691BD5639138FB4E4C578EA0B_AdjustorThunk },
	{ 0x060003B5, fsResult_get_RawMessages_m0B25A624CB4B2F03DF6FC6077284AF92CFFC4683_AdjustorThunk },
	{ 0x060003B6, fsResult_get_FormattedMessages_mCF960860BF1F178EDFCA80D1196B2F69096144B7_AdjustorThunk },
	{ 0x06000433, fsVersionedType_Migrate_mA2431BCBC723C85F909F6D50CCC218ABFAED65AB_AdjustorThunk },
	{ 0x06000434, fsVersionedType_ToString_m584F4EBD02BC6D0502664D2047FF3D71270A359F_AdjustorThunk },
	{ 0x06000435, fsVersionedType_Equals_m051C07595BCB7037C2E668E0E09D0A4C74A2958F_AdjustorThunk },
	{ 0x06000436, fsVersionedType_GetHashCode_m02754D5563950758CECD86BE72D69DD7046C47C6_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1084] = 
{
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3185,
	2524,
	1385,
	4364,
	3185,
	2524,
	1385,
	4364,
	3185,
	1385,
	4364,
	3185,
	1385,
	3518,
	4364,
	4250,
	2802,
	2802,
	3518,
	4364,
	9089,
	4364,
	3518,
	3185,
	3185,
	4364,
	3185,
	2524,
	1385,
	4364,
	3185,
	1385,
	4364,
	3185,
	1385,
	3518,
	3518,
	3518,
	3185,
	3185,
	4364,
	9089,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	6690,
	8505,
	0,
	7631,
	6947,
	7631,
	6693,
	8220,
	4364,
	3185,
	4250,
	4250,
	3881,
	4168,
	3807,
	4364,
	4364,
	4364,
	7616,
	4364,
	9089,
	4364,
	4250,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	9089,
	4364,
	9089,
	4364,
	4364,
	3807,
	3807,
	4364,
	3807,
	3807,
	8993,
	8868,
	8505,
	9089,
	9089,
	0,
	3881,
	4364,
	9031,
	9089,
	3881,
	9089,
	8993,
	0,
	0,
	0,
	0,
	0,
	2802,
	4250,
	3881,
	4250,
	3881,
	4217,
	3853,
	4250,
	3881,
	4250,
	3881,
	3518,
	4364,
	3881,
	4364,
	9089,
	8887,
	9031,
	8887,
	8566,
	8887,
	9089,
	8505,
	0,
	0,
	0,
	9089,
	4250,
	4250,
	3881,
	2802,
	2322,
	2524,
	2316,
	2518,
	0,
	0,
	7261,
	7261,
	7261,
	7261,
	7261,
	8529,
	7631,
	7489,
	7261,
	7261,
	7261,
	7224,
	6366,
	6366,
	7489,
	8413,
	7489,
	9089,
	2802,
	3298,
	3185,
	4216,
	2356,
	3458,
	9089,
	4364,
	3185,
	3185,
	4364,
	3185,
	4364,
	3185,
	3185,
	3185,
	3881,
	3185,
	4216,
	4250,
	9089,
	8220,
	8220,
	8220,
	8220,
	8220,
	9089,
	4364,
	4250,
	4250,
	4250,
	4364,
	9089,
	4364,
	3185,
	3518,
	3185,
	3881,
	4250,
	4216,
	4250,
	9089,
	9031,
	8505,
	3185,
	8505,
	7261,
	3518,
	2315,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3881,
	0,
	4364,
	4250,
	0,
	2524,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9089,
	8993,
	8993,
	8993,
	8868,
	9089,
	7975,
	0,
	6980,
	8220,
	8505,
	6985,
	8220,
	8505,
	8505,
	6985,
	8220,
	8505,
	8505,
	8220,
	8505,
	9089,
	4364,
	3185,
	3881,
	4364,
	3518,
	2802,
	3881,
	4364,
	2524,
	9089,
	3881,
	4364,
	3518,
	2802,
	3881,
	3881,
	4250,
	2802,
	4364,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3881,
	4364,
	0,
	3881,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	4250,
	4250,
	2802,
	4250,
	4250,
	9031,
	9031,
	9089,
	8505,
	7631,
	0,
	8505,
	7252,
	8505,
	7252,
	7252,
	7252,
	7252,
	9031,
	9031,
	9031,
	9031,
	9031,
	9031,
	4364,
	3185,
	3852,
	4364,
	4168,
	4364,
	4364,
	4250,
	4364,
	4250,
	4250,
	4250,
	4250,
	3881,
	4250,
	3881,
	3881,
	3881,
	3881,
	4250,
	4250,
	3881,
	4168,
	8505,
	2787,
	7631,
	2802,
	2802,
	2802,
	2084,
	8505,
	7631,
	3881,
	4364,
	2507,
	4250,
	4250,
	9089,
	4364,
	3518,
	8220,
	8220,
	8220,
	7261,
	8505,
	9089,
	9089,
	4364,
	3185,
	3185,
	3852,
	4364,
	4168,
	4250,
	4364,
	4250,
	4250,
	4250,
	4250,
	2524,
	2210,
	2210,
	4364,
	4250,
	2524,
	2210,
	2210,
	4364,
	2935,
	2931,
	2524,
	4364,
	2934,
	2931,
	2524,
	4364,
	4250,
	3185,
	3185,
	3185,
	2210,
	2210,
	2524,
	4364,
	4364,
	9089,
	8993,
	8868,
	8993,
	9031,
	8887,
	7724,
	7019,
	7654,
	6690,
	6232,
	6169,
	7009,
	8505,
	4250,
	4250,
	2802,
	2802,
	4364,
	3518,
	4250,
	3881,
	4364,
	4250,
	3881,
	4250,
	3881,
	4364,
	2802,
	4364,
	3881,
	3881,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4250,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4250,
	3881,
	8993,
	9089,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8868,
	9089,
	9089,
	9089,
	9089,
	4364,
	3922,
	4364,
	2322,
	3419,
	8399,
	9089,
	0,
	0,
	0,
	0,
	0,
	9089,
	7631,
	6200,
	8505,
	9089,
	8220,
	8505,
	8490,
	9031,
	9089,
	8887,
	9089,
	9089,
	9089,
	3852,
	3852,
	9031,
	9089,
	9089,
	9089,
	9031,
	9031,
	8887,
	9031,
	8887,
	8887,
	9089,
	9089,
	8887,
	9089,
	4364,
	4364,
	3518,
	2802,
	4250,
	3881,
	4250,
	3881,
	3518,
	2315,
	4364,
	4364,
	3518,
	2802,
	2802,
	3518,
	3185,
	4250,
	4250,
	4250,
	3185,
	2524,
	1385,
	4364,
	9089,
	4250,
	3881,
	4364,
	4364,
	4364,
	4364,
	4364,
	3807,
	9031,
	9089,
	4364,
	3185,
	3185,
	3185,
	2210,
	2210,
	2524,
	4364,
	4250,
	3185,
	2210,
	2210,
	4364,
	3185,
	2524,
	2210,
	2210,
	2212,
	6944,
	4364,
	3185,
	3185,
	3185,
	2524,
	2210,
	2210,
	0,
	4364,
	9089,
	4364,
	3518,
	3495,
	3495,
	4364,
	3009,
	3881,
	3185,
	2931,
	2210,
	2210,
	2524,
	3185,
	3185,
	3185,
	2210,
	2210,
	2524,
	4364,
	3185,
	2524,
	2210,
	3185,
	2210,
	8399,
	8505,
	7975,
	8505,
	4364,
	3185,
	3185,
	3185,
	2210,
	2210,
	4364,
	3185,
	2210,
	2210,
	2524,
	4364,
	3185,
	3185,
	3185,
	2210,
	2210,
	8220,
	8220,
	8220,
	8220,
	4364,
	3185,
	2210,
	2210,
	2524,
	4364,
	3185,
	3185,
	3185,
	2210,
	2210,
	2524,
	4364,
	3185,
	3185,
	3185,
	2210,
	2210,
	2524,
	4364,
	9089,
	6980,
	9089,
	2524,
	3185,
	3185,
	0,
	0,
	2933,
	2932,
	2211,
	2211,
	0,
	0,
	4364,
	9089,
	4364,
	3515,
	9089,
	4364,
	9089,
	4364,
	2524,
	0,
	0,
	4364,
	0,
	4364,
	4250,
	4364,
	3807,
	3826,
	3853,
	3881,
	3881,
	3881,
	9031,
	8501,
	4364,
	4250,
	4216,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	4190,
	4217,
	4168,
	4250,
	4250,
	4250,
	0,
	3185,
	3185,
	7261,
	7261,
	4216,
	9089,
	0,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	2802,
	2084,
	0,
	0,
	0,
	0,
	3185,
	2802,
	2075,
	2075,
	2802,
	4364,
	3185,
	2802,
	2802,
	4364,
	3881,
	4029,
	4168,
	4168,
	3166,
	4349,
	3624,
	4364,
	4029,
	4028,
	4028,
	4028,
	3270,
	4028,
	4028,
	4028,
	4028,
	4028,
	8064,
	8505,
	3270,
	2596,
	1264,
	4028,
	7969,
	8505,
	7975,
	6982,
	8505,
	8505,
	8497,
	2802,
	3185,
	2802,
	2075,
	2787,
	2075,
	2802,
	4364,
	3881,
	3990,
	4030,
	8933,
	8933,
	8065,
	4168,
	4168,
	4168,
	4369,
	4250,
	4250,
	4250,
	9089,
	4364,
	3788,
	2802,
	3518,
	3881,
	2524,
	0,
	0,
	2211,
	1564,
	1564,
	1564,
	2211,
	2211,
	2211,
	1564,
	1037,
	1037,
	1037,
	1564,
	1564,
	6625,
	8887,
	9089,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8220,
	8867,
	6985,
	6184,
	6979,
	6184,
	6985,
	2739,
	2739,
	4364,
	4364,
	2802,
	2802,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	3881,
	2802,
	3518,
	2802,
	4250,
	3881,
	4168,
	4168,
	4250,
	7631,
	6985,
	7261,
	5699,
	6366,
	9089,
	4364,
	3185,
	3185,
	3185,
	4364,
	3185,
	4364,
	3185,
	7631,
	4364,
	4168,
	3515,
	2739,
	3419,
	3185,
	3881,
	4364,
	2322,
	3419,
	4364,
	9089,
	0,
	0,
	0,
	0,
	0,
	7261,
	6366,
	6690,
	0,
	0,
	7631,
	7631,
	7631,
	8505,
	7631,
	8505,
	8505,
	8505,
	8505,
	8505,
	9089,
	2361,
	3461,
	4364,
	8505,
	7616,
	4364,
	3518,
	3518,
	4250,
	3185,
	4216,
	7060,
	6374,
	8151,
	8910,
	8910,
	9089,
};
static const Il2CppTokenRangePair s_rgctxIndices[66] = 
{
	{ 0x02000002, { 0, 7 } },
	{ 0x0200001F, { 9, 17 } },
	{ 0x0200003A, { 35, 2 } },
	{ 0x0200003B, { 37, 9 } },
	{ 0x0200003C, { 46, 15 } },
	{ 0x0200003D, { 61, 11 } },
	{ 0x0200003E, { 72, 12 } },
	{ 0x0200003F, { 84, 13 } },
	{ 0x02000040, { 97, 14 } },
	{ 0x02000041, { 111, 22 } },
	{ 0x02000042, { 133, 3 } },
	{ 0x02000043, { 136, 9 } },
	{ 0x02000044, { 145, 16 } },
	{ 0x02000045, { 161, 11 } },
	{ 0x02000046, { 172, 12 } },
	{ 0x02000047, { 184, 13 } },
	{ 0x02000048, { 197, 14 } },
	{ 0x02000049, { 211, 4 } },
	{ 0x0200004A, { 215, 17 } },
	{ 0x02000055, { 234, 13 } },
	{ 0x02000056, { 247, 13 } },
	{ 0x02000057, { 260, 15 } },
	{ 0x02000058, { 275, 17 } },
	{ 0x02000059, { 292, 19 } },
	{ 0x0200005A, { 311, 22 } },
	{ 0x0200005B, { 333, 2 } },
	{ 0x0200005C, { 335, 1 } },
	{ 0x0200005D, { 336, 12 } },
	{ 0x0200005E, { 348, 16 } },
	{ 0x0200005F, { 364, 16 } },
	{ 0x02000060, { 380, 18 } },
	{ 0x02000061, { 398, 20 } },
	{ 0x02000062, { 418, 22 } },
	{ 0x02000064, { 440, 14 } },
	{ 0x02000081, { 457, 20 } },
	{ 0x02000084, { 477, 8 } },
	{ 0x02000087, { 501, 4 } },
	{ 0x02000088, { 505, 12 } },
	{ 0x0200008E, { 517, 5 } },
	{ 0x020000BB, { 539, 6 } },
	{ 0x020000D3, { 550, 4 } },
	{ 0x0600003F, { 7, 1 } },
	{ 0x0600006A, { 8, 1 } },
	{ 0x0600008E, { 26, 3 } },
	{ 0x0600008F, { 29, 1 } },
	{ 0x06000090, { 30, 2 } },
	{ 0x0600009A, { 32, 1 } },
	{ 0x0600009B, { 33, 2 } },
	{ 0x06000131, { 232, 1 } },
	{ 0x06000145, { 233, 1 } },
	{ 0x060001C3, { 454, 3 } },
	{ 0x06000269, { 485, 2 } },
	{ 0x0600026A, { 487, 4 } },
	{ 0x0600026B, { 491, 3 } },
	{ 0x0600026C, { 494, 7 } },
	{ 0x060002E6, { 522, 6 } },
	{ 0x0600033A, { 528, 2 } },
	{ 0x0600033B, { 530, 3 } },
	{ 0x06000345, { 533, 2 } },
	{ 0x06000346, { 535, 2 } },
	{ 0x06000364, { 537, 2 } },
	{ 0x060003BE, { 545, 2 } },
	{ 0x060003BF, { 547, 3 } },
	{ 0x0600041B, { 554, 3 } },
	{ 0x0600041F, { 557, 2 } },
	{ 0x06000420, { 559, 2 } },
};
extern const uint32_t g_rgctx_T_tD0E7CC5B6021F5900E6584189CDAAB4318ED7523;
extern const uint32_t g_rgctx_Cloner_1_tCCE13274C8C182729D26F6533A793299A8B52C13;
extern const uint32_t g_rgctx_Cloner_1_BeforeClone_mD64C1349F749751152C4C79403154A5427BD99A9;
extern const uint32_t g_rgctx_Cloner_1_ConstructClone_m0C1278F0465743D092AAC4E74009988BCCB6EFB4;
extern const uint32_t g_rgctx_Cloner_1_FillClone_mA3D5B836FBC15DAA935C500D5CF46CEE22196AB4;
extern const uint32_t g_rgctx_TU26_t1C4A29B72E29891FF677CADAC5361C40CCF57CD9;
extern const uint32_t g_rgctx_Cloner_1_AfterClone_mA0C8E699FAA15EB143CCA5613A8038AA3B4031FB;
extern const uint32_t g_rgctx_T_t3E3386298B2DAC41488753698F0C77739EC52535;
extern const uint32_t g_rgctx_T_t93A7576DD6096E9C991D8485DA482792DF03B9B7;
extern const uint32_t g_rgctx_GenericPool_1_tEBC7C4EC2AD92F6ACCF235037E37AD908CF9DBCB;
extern const uint32_t g_rgctx_GenericPool_1_tEBC7C4EC2AD92F6ACCF235037E37AD908CF9DBCB;
extern const uint32_t g_rgctx_Stack_1_t545EFA9FDE9E4875EF1829DA0B5C4DF179A3877F;
extern const uint32_t g_rgctx_Stack_1_get_Count_m43F1E0B07999664606B520644ECA44F332C01157;
extern const uint32_t g_rgctx_Func_1_t07F6064D8AB14BF4CAC41D5C4B2726CB459E5DD0;
extern const uint32_t g_rgctx_Func_1_Invoke_m3D2BF2F788B89E8E1DB8DE7BA3D09DDFB3A54E6B;
extern const uint32_t g_rgctx_T_t0FD06116FFBD9219EDC1E72202B927041A94E956;
extern const uint32_t g_rgctx_Stack_1_Push_m32BD2B01D2797AFACBA85601C264D3176EC1A3AB;
extern const uint32_t g_rgctx_Stack_1_Pop_mC57DD66029E6C68104578092B9DD92D6DF2AEBED;
extern const uint32_t g_rgctx_HashSet_1_t6CB8DC49EA36380D2CB9A8D88939EF64A3BF8389;
extern const uint32_t g_rgctx_HashSet_1_Add_m452F40361D5361C100001997A0B78335B06389A6;
extern const uint32_t g_rgctx_HashSet_1_Remove_m9E101F43EB15163E7103C80F28606FBEBE17AC78;
extern const uint32_t g_rgctx_Stack_1__ctor_mAA537F1124AB3B49D374DF33270230B29892B114;
extern const uint32_t g_rgctx_ReferenceEqualityComparer_1_t22C4B26ED2BC7C33222ACACB33ACAE157CBAD8BC;
extern const uint32_t g_rgctx_ReferenceEqualityComparer_1_t22C4B26ED2BC7C33222ACACB33ACAE157CBAD8BC;
extern const uint32_t g_rgctx_HashSet_1__ctor_m5E0EAA3401141BC6021C61EB033C7DE67E11826F;
extern const uint32_t g_rgctx_IEqualityComparer_1_tBDEA3BCC88C388DEB98B2020829BA9F9C3C5A45A;
extern const uint32_t g_rgctx_T_t10416D203278A79DF63D5150ED9F4EF81D97EB1B;
extern const uint32_t g_rgctx_Enumerable_Cast_TisT_t10416D203278A79DF63D5150ED9F4EF81D97EB1B_mA59ACFD208BE2F8E7C38EE039D7C133ADB9BA2F9;
extern const uint32_t g_rgctx_IEnumerable_1_tDACA09A7604D3E5D9DB87E1B381F0E3974139936;
extern const uint32_t g_rgctx_AttributeCache_HasAttribute_TisTAttribute_t278E8D943C3C4633EE6BBC5C277CB0456F32B4EF_mCCDF22945C35FBE25C2A2CFA16DA3814209706F3;
extern const uint32_t g_rgctx_AttributeCache_GetAttribute_TisTAttribute_tD40A15B1D67955664858591F10455A74847F5521_mEF6EE167AF090B40690F766DED906D5FB8037342;
extern const uint32_t g_rgctx_TAttribute_tD40A15B1D67955664858591F10455A74847F5521;
extern const uint32_t g_rgctx_TAttribute_t09F62645649E5BEF28C540E351A872736B5B3EF7;
extern const uint32_t g_rgctx_TAttribute_t2235B12A1A0F6FCB5A26D5803750F882C41688AD;
extern const uint32_t g_rgctx_TAttribute_t2235B12A1A0F6FCB5A26D5803750F882C41688AD;
extern const uint32_t g_rgctx_InstanceInvokerBase_1__ctor_m87FE896EA5BED67F6F290F43099E6DD9BFFBCEB8;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_tB2F2C827C68FD9E89CAEFB20F8908126A0D875F3;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1__ctor_m814CF7475AAF95EEA4F67F6A3DC27D86EB17A0D5;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1_tD6179661A3581B84FEC64F1B76C161A5B6590261;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t8E75247B0AC6AC703B5486043C0AE77463C32B70;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_1_t34774AC08C69B05C4CFFD7C6E6373B93ECC3ECB7_m99571C6E66A977E4FF1BEBB3DDDE0FA974A058B2;
extern const uint32_t g_rgctx_Expression_1_t729DE59AEC880C4E5398933670F3DEAC60F45AC7;
extern const uint32_t g_rgctx_Expression_1_Compile_mA7588CE1724155FA8143BB08CDEE8E57B4BB96E9;
extern const uint32_t g_rgctx_Action_1_t34774AC08C69B05C4CFFD7C6E6373B93ECC3ECB7;
extern const uint32_t g_rgctx_InstanceActionInvoker_1_t148BE4E218F6D97BB53B0C1AF29D0EF3C2227079;
extern const uint32_t g_rgctx_Action_1_t34774AC08C69B05C4CFFD7C6E6373B93ECC3ECB7;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1__ctor_m22247D35CC3751DEE0792A754F58F20EA52BAA0C;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1_tBDA6BD6A824A1647ABFFC01CFDE1CD1689674067;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t4B23F32137512A148161617F2856C10BC432B9C9;
extern const uint32_t g_rgctx_InvokerBase_VerifyArgument_TisTParam0_tB0903A5E85633A9FFEDCCD49BE84C3F7C77C6D5E_mAB9599FB49F58D8BAEAD5A0032CFD78605A9B818;
extern const uint32_t g_rgctx_InstanceActionInvoker_2_InvokeUnsafe_m90517B1E02DA0ED86430198B6C2B658FDAFFB3A4;
extern const uint32_t g_rgctx_InstanceActionInvoker_2_tA9C126A526D11F6ABBDC9CF2E0FB6334A34EFD08;
extern const uint32_t g_rgctx_Action_2_tD9329D8E2F44E13D21EF75CBF4ABA639DEEAE726;
extern const uint32_t g_rgctx_TTarget_tF5A587B6C52DBA1F762AB83557E591A8974CE7AA;
extern const uint32_t g_rgctx_TParam0_tB0903A5E85633A9FFEDCCD49BE84C3F7C77C6D5E;
extern const uint32_t g_rgctx_Action_2_Invoke_mA1AE2383C2D0050A5B1687D0987B990E30F944B4;
extern const uint32_t g_rgctx_TParam0_tB0903A5E85633A9FFEDCCD49BE84C3F7C77C6D5E;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_2_tD9329D8E2F44E13D21EF75CBF4ABA639DEEAE726_mC001CD2CCE7292E1FCC5FBEE0719D590425E1739;
extern const uint32_t g_rgctx_Expression_1_tDC435B2E1054C2F2AC5005AD4B19A99B9FD17C07;
extern const uint32_t g_rgctx_Expression_1_Compile_mA9E112AD1841360372ACFD423C754D510253F8CA;
extern const uint32_t g_rgctx_Action_2_tD9329D8E2F44E13D21EF75CBF4ABA639DEEAE726;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1__ctor_m4A9E17072A2EF4EBA0FB57E932681002C377D2AA;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1_t35BD617675493624083E886F467BE464F64AEC36;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_tEBC4C11F6BC7A5A20AF06AB20C1E3ED3118EBA9B;
extern const uint32_t g_rgctx_TParam0_tB97378C3ECDFF15433C2968A0FFF44F9B6C343D7;
extern const uint32_t g_rgctx_TParam1_tE0BD732C338FC97E6C89DBACA511EA3E263C8370;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_3_t7527FDADCA09E08C5B0121CD783745BE8B002510_m347F84D3B345274CE0AAF260451A82A610D41264;
extern const uint32_t g_rgctx_Expression_1_t7D4528A3406F459546E2BDAFE2BDD5DC1CB6153F;
extern const uint32_t g_rgctx_Expression_1_Compile_mDB9B3B5128614BBBA90EBB20AE68A4098B5C22F9;
extern const uint32_t g_rgctx_Action_3_t7527FDADCA09E08C5B0121CD783745BE8B002510;
extern const uint32_t g_rgctx_InstanceActionInvoker_3_t692C0022A275B61F2919162B001F4DE319C62229;
extern const uint32_t g_rgctx_Action_3_t7527FDADCA09E08C5B0121CD783745BE8B002510;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1__ctor_mA7FB23CA9170BB4F26056C8CFC053CBE3E3F2687;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1_tCADB567CCA155EFE3536FDA7D215DA4C70472B2B;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t29A5C332B3F087EA7F58A11255FBBF0BC01EB4B9;
extern const uint32_t g_rgctx_TParam0_t4F34A2F814F53FFB7842B16A5A2ABC848BA9AE02;
extern const uint32_t g_rgctx_TParam1_t00A749ED21B7BA52DECF9BD1E34EE093B4349097;
extern const uint32_t g_rgctx_TParam2_t18FC0E2F2C6BE1B67662B4A7B73B60C09F11AD76;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_4_t73BE0A5552A51C94B0D6D15FDB3BA1A71272D762_mACB197143E9477EF36E1ED879BDADC9445742752;
extern const uint32_t g_rgctx_Expression_1_t67443EF4D56FAE2CB16E9D6693F3F854B1396B3A;
extern const uint32_t g_rgctx_Expression_1_Compile_m9AC5E824AF4D72D97E6778D4DA7247FBC05AC4FA;
extern const uint32_t g_rgctx_Action_4_t73BE0A5552A51C94B0D6D15FDB3BA1A71272D762;
extern const uint32_t g_rgctx_InstanceActionInvoker_4_tE4AC8800A552FCF4FDF6E2BAB71BCC5D531DDC03;
extern const uint32_t g_rgctx_Action_4_t73BE0A5552A51C94B0D6D15FDB3BA1A71272D762;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1__ctor_mC1E077487D192666FD90E6B585C0BFF88480A90C;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1_t99BCAC031309FBC658982F4031EFB836A4E98D03;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_tA63FCB646873CF72134AE20DFE3C28EF0058CBAE;
extern const uint32_t g_rgctx_TParam0_t467853C14C61D627D3D868997B41F245EA658B25;
extern const uint32_t g_rgctx_TParam1_t1B9F94A217736CE10D849DFAF18AF51249EE9DC5;
extern const uint32_t g_rgctx_TParam2_tE3E286F5D5D52EA6F493C529ED4A91E90474D262;
extern const uint32_t g_rgctx_TParam3_t46D21B50A9E7D6FD8D23EDAC901B98E2DBA25B45;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_5_tAD9BA6FFA0BA81BDBC2A7FCAA6EF81D8DD6D33B3_m2F4B51EF18F4A0B5D4100687D55D714026468378;
extern const uint32_t g_rgctx_Expression_1_t3D14567CC70D40AD13C22F2EB8AA2CA6D425999C;
extern const uint32_t g_rgctx_Expression_1_Compile_mB10B0C17FEF1015B63D6C9D8B737938DDE1AC275;
extern const uint32_t g_rgctx_Action_5_tAD9BA6FFA0BA81BDBC2A7FCAA6EF81D8DD6D33B3;
extern const uint32_t g_rgctx_InstanceActionInvoker_5_tE74AF6329E1D020CD39375E497EF281E1A8406DC;
extern const uint32_t g_rgctx_Action_5_tAD9BA6FFA0BA81BDBC2A7FCAA6EF81D8DD6D33B3;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1__ctor_m24D14210B721D29DFCF2EB8F35CB5D0D6D6A06B6;
extern const uint32_t g_rgctx_InstanceActionInvokerBase_1_tD7D7BB56146E5493AF64F92513546FF604B6D5E0;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_tFBFCFA62B915792A83FD3CED7CC4A7B785F2D36E;
extern const uint32_t g_rgctx_TParam0_tCB368BFFD77B922D05072D6402E2EF83E3C578AA;
extern const uint32_t g_rgctx_TParam1_t758B55381FDA347AF7274E426835846B907E5814;
extern const uint32_t g_rgctx_TParam2_tE53C47007FB25E2A01482B7D29F7508E2BAA2D0B;
extern const uint32_t g_rgctx_TParam3_t85BFF2B5FA8C98FA7833040946BEAE0A4318E267;
extern const uint32_t g_rgctx_TParam4_tE3EA287C0B2A6966D1AA50BB238A8EB7A749A146;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_6_t5F118FC7371ADB810F4C1BFB5EF70195AAFDC419_mC720D1D7ED0E8DE93ECB9D23F53CCD205E9D744E;
extern const uint32_t g_rgctx_Expression_1_t0C89E5DCF2EE712146302A7E2B9176EA4EDB3730;
extern const uint32_t g_rgctx_Expression_1_Compile_m92E3F1C5DFB88CF49C021B9134CAA7A5C32A683B;
extern const uint32_t g_rgctx_Action_6_t5F118FC7371ADB810F4C1BFB5EF70195AAFDC419;
extern const uint32_t g_rgctx_InstanceActionInvoker_6_t5708727AAEFB90DD157B30791BF19123AB162975;
extern const uint32_t g_rgctx_Action_6_t5F118FC7371ADB810F4C1BFB5EF70195AAFDC419;
extern const uint32_t g_rgctx_TTarget_t9BA028F0C4CEFA2A978528B9AB2EAEEC79913009;
extern const uint32_t g_rgctx_TField_t95570A183372691696232E002B96791233EC54EB;
extern const uint32_t g_rgctx_InstanceFieldAccessor_2_t2FC8F9696173244C2CE303AEF12534C972E302A2;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_2_tE1336C5B220D3B1E7FB4C9594AD5F84CD6EE2196_mB6B319993379CE34862980C27A71337BFB16E37A;
extern const uint32_t g_rgctx_Expression_1_tAAB1A8203CC500A01909F4AC4C86A3E995216830;
extern const uint32_t g_rgctx_Expression_1_Compile_mD4BDE7DE66B1CB16D59DCD7E21484FB6583573F8;
extern const uint32_t g_rgctx_Func_2_tE1336C5B220D3B1E7FB4C9594AD5F84CD6EE2196;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_2_t252AD7A97D0D912C97014C03463B7AE64637B6A9_m95B788B3E1241C0FC6443EE97DC3B8A138020F74;
extern const uint32_t g_rgctx_Expression_1_t02A2AABB5C86B65D1AEC0764DF0CD71EE3C897DF;
extern const uint32_t g_rgctx_Expression_1_Compile_m7420583691497F9D9B6AD3F29E1B9E4610F1B10B;
extern const uint32_t g_rgctx_Action_2_t252AD7A97D0D912C97014C03463B7AE64637B6A9;
extern const uint32_t g_rgctx_InstanceFieldAccessor_2_U3CCompileU3Eb__4_0_m8360559D505484E13501DDDB880FB5907835A32D;
extern const uint32_t g_rgctx_Func_2__ctor_m34E9C75CABAD15D1BE41E27F0AB22A85036252DB;
extern const uint32_t g_rgctx_InstanceFieldAccessor_2_U3CCompileU3Eb__4_1_m23A8977F5BAF9483C467F50DB50E3B8C530148E5;
extern const uint32_t g_rgctx_Action_2__ctor_m452B0518D5E82CD6202B338FF72DA4E3F11112FD;
extern const uint32_t g_rgctx_OptimizedReflection_VerifyInstanceTarget_TisTTarget_t9BA028F0C4CEFA2A978528B9AB2EAEEC79913009_mC5C016098CA22F32176A4B7856BF54AAACE845C2;
extern const uint32_t g_rgctx_InstanceFieldAccessor_2_GetValueUnsafe_mA1D26E457FBF97F086F521BBF910B790F44692E6;
extern const uint32_t g_rgctx_TTarget_t9BA028F0C4CEFA2A978528B9AB2EAEEC79913009;
extern const uint32_t g_rgctx_Func_2_Invoke_m308CFEA5197C87F8CF02BA7FD9BCDBADBFB8FD1F;
extern const uint32_t g_rgctx_TField_t95570A183372691696232E002B96791233EC54EB;
extern const uint32_t g_rgctx_InstanceFieldAccessor_2_SetValueUnsafe_mC2DC2692A5D129D2E9EDA468365A478987307B3F;
extern const uint32_t g_rgctx_Action_2_Invoke_m92487F7296183BEBFD4973CF055CEF463D7FB4B5;
extern const uint32_t g_rgctx_InstanceInvokerBase_1__ctor_mBE9A6899016F411A1A1457C8FFA2E42E5CB531FB;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_tC0DE22622C0E886F3D29D177B465BFC51EE2A5D1;
extern const uint32_t g_rgctx_TResult_t374DC02DEC914881936501E15390A74A3C0FBDDA;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2__ctor_m970D11D43DB94E544D93C4C1836BA9733AA67DF8;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2_tB2363C479A5E72B62E215355B059AF3B7C478043;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t9F494176A9B1825961A2F5B5693D7164983B0693;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_2_tEA48B7C01656ABFC696C99EA4C2FF9E2F3DFA800_m608669266144A490B4521C9F56174857388B5D96;
extern const uint32_t g_rgctx_Expression_1_tBFEBCF151B5A833C62623DD4AFFD63A6CEC562C2;
extern const uint32_t g_rgctx_Expression_1_Compile_m33576EF249E6F829048F4F574B53614596522C55;
extern const uint32_t g_rgctx_Func_2_tEA48B7C01656ABFC696C99EA4C2FF9E2F3DFA800;
extern const uint32_t g_rgctx_InstanceFunctionInvoker_2_t2241D493B1E4C555CFCB7D894DB575338A3965B5;
extern const uint32_t g_rgctx_Func_2_tEA48B7C01656ABFC696C99EA4C2FF9E2F3DFA800;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2__ctor_mE1C1DC896BA60B6D717120B845A3AE97327F00B1;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2_t50B56B6425932E7867263FA1A68A4BF8A4C88495;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t36B72B6E1741474375C4E687F350CEB2DEDF36F9;
extern const uint32_t g_rgctx_InvokerBase_VerifyArgument_TisTParam0_t666542896C005CFC8965A1161EC08E0ED3A341C3_mF22BF370167C3E098A0C896649F2623FAFBEC2F4;
extern const uint32_t g_rgctx_InstanceFunctionInvoker_3_InvokeUnsafe_m54BF8BAF8892CA35A4E04BA08640618DB1A28BA3;
extern const uint32_t g_rgctx_InstanceFunctionInvoker_3_tE683F17E1212F4C8F7D59A1F642491D18CCE6C8C;
extern const uint32_t g_rgctx_Func_3_t3AFC1B1D97CE58F0A40DDFF7C61E9221FA3464C8;
extern const uint32_t g_rgctx_TTarget_t27DEA11169D17BB6FE4EA3FC40EFD57869598EAA;
extern const uint32_t g_rgctx_TParam0_t666542896C005CFC8965A1161EC08E0ED3A341C3;
extern const uint32_t g_rgctx_Func_3_Invoke_m20B9B1392DFF3FE4B4F258B47FA2ABF430217DC4;
extern const uint32_t g_rgctx_TResult_tE9CF0C03DC0DFD6703ACFD11CEE7DC8B3D016C80;
extern const uint32_t g_rgctx_TParam0_t666542896C005CFC8965A1161EC08E0ED3A341C3;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_3_t3AFC1B1D97CE58F0A40DDFF7C61E9221FA3464C8_m5A720E3B4C823A1424E030621C41958A2594AC4D;
extern const uint32_t g_rgctx_Expression_1_t48078CEB41483958E74884C2901A1C39BE35289A;
extern const uint32_t g_rgctx_Expression_1_Compile_m8DCADCFA124D5C98986473BDA24200C0C450CA3C;
extern const uint32_t g_rgctx_Func_3_t3AFC1B1D97CE58F0A40DDFF7C61E9221FA3464C8;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2__ctor_m076E87C656C9F9947980D4DBE13B628DE1FAD706;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2_tB97FEE94648EE7AF16E14702E60A0BF79B952A12;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t96DA7F30FC35EED2DB1F174191D4146075E87529;
extern const uint32_t g_rgctx_TParam0_tB84D295236BC36089904690069C305C28B63C61C;
extern const uint32_t g_rgctx_TParam1_tA9AA2D38BABA20D3A86D4094F721B36909A19C75;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_4_t2FADD491A55A6167CF81C1E901176D9396F21D0D_mC2422706B94B432CB4A9E65B99A96CBCA201CDE2;
extern const uint32_t g_rgctx_Expression_1_t065CCFA0167860408FF79EF78C376AED8B83DD97;
extern const uint32_t g_rgctx_Expression_1_Compile_m86EAAA748F9AED948E087F756103FE6D6D21FF22;
extern const uint32_t g_rgctx_Func_4_t2FADD491A55A6167CF81C1E901176D9396F21D0D;
extern const uint32_t g_rgctx_InstanceFunctionInvoker_4_tB6B1A6B21F7F83D4171DAC91D0197861E08E045A;
extern const uint32_t g_rgctx_Func_4_t2FADD491A55A6167CF81C1E901176D9396F21D0D;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2__ctor_mC51007809F066C69F5D56498B119021BF819DCE2;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2_t3675A8EED3E75A98D575EC755C6098006D35F456;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t61C249656592C5929AFF8E9E7772A7D88C65D677;
extern const uint32_t g_rgctx_TParam0_t8E8CFFDFD87DC50C835474F8F71BB5E12A77296E;
extern const uint32_t g_rgctx_TParam1_tF775C5E0610FAF863E8C52A22B23B53974DB31DB;
extern const uint32_t g_rgctx_TParam2_t569CA848FA0DA3ECE16BC96B02A02A8F86D4E789;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_5_t85317FA97E2451CFAF004B63EA687727D184D95A_mCCF01B5CD7CAF865D21936DF8DCB5A8F1972711B;
extern const uint32_t g_rgctx_Expression_1_t2D701BABD43F6E99CCB3EB3C67DF0BCF6BDB9301;
extern const uint32_t g_rgctx_Expression_1_Compile_mC2D9C67542983C0E31A7E8CE5037286254F1F887;
extern const uint32_t g_rgctx_Func_5_t85317FA97E2451CFAF004B63EA687727D184D95A;
extern const uint32_t g_rgctx_InstanceFunctionInvoker_5_tA982112185746211E77A1111FECB0E0F14A90251;
extern const uint32_t g_rgctx_Func_5_t85317FA97E2451CFAF004B63EA687727D184D95A;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2__ctor_m4A71EFD6E7FCF4B4865E1B14A1F3137198B44719;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2_t905F65DC7EA62AD56793048401EB02203B8727AB;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_tB656A518DEEFE1BB3A38C9DD40E25998063DA0C6;
extern const uint32_t g_rgctx_TParam0_tA8A708307468799CE023EA66A08F6A1AFD560C60;
extern const uint32_t g_rgctx_TParam1_tE251E65C4D47A8CAB8C1F86848F46091660DFA8A;
extern const uint32_t g_rgctx_TParam2_t80B79B4A660088F087B14C0F480AD6100FCB2877;
extern const uint32_t g_rgctx_TParam3_tF447104F517C524A7FCEC88B736A4C82F605C47C;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_6_t2F95C7D598C08DFBF87D5937F29B5DB51781BF03_m13A92409F7317E65E28EFD99A03357012F653D8C;
extern const uint32_t g_rgctx_Expression_1_tA41244564F921E964C0B88984F8B1335664DD569;
extern const uint32_t g_rgctx_Expression_1_Compile_m8A231DBA61C025F34EF748111A8301FCD2943FF8;
extern const uint32_t g_rgctx_Func_6_t2F95C7D598C08DFBF87D5937F29B5DB51781BF03;
extern const uint32_t g_rgctx_InstanceFunctionInvoker_6_t81B736C7F3C36D3B33EE0E6412109EF8C8929EA2;
extern const uint32_t g_rgctx_Func_6_t2F95C7D598C08DFBF87D5937F29B5DB51781BF03;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2__ctor_m6C405C8AC55BAB7CBB44D2D677C2676519FE1ACC;
extern const uint32_t g_rgctx_InstanceFunctionInvokerBase_2_t2D822AF95E1DC8BEDE40C325E0D0F2096EA7B466;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_t63119156B57161C782109346600D16B5FF1AAB60;
extern const uint32_t g_rgctx_TParam0_t24922A2A2F4C3DDEC30EF00E17C75EBA3B858125;
extern const uint32_t g_rgctx_TParam1_t207C9D6267AA7CF3FABDB9FFB2CF06FCE223747C;
extern const uint32_t g_rgctx_TParam2_tDCAB8AE5D5348F04C0F0F347431762308152BA73;
extern const uint32_t g_rgctx_TParam3_tAFB0F8952762361B7155C9D000AFDE95A2C7D61F;
extern const uint32_t g_rgctx_TParam4_t368994B1EE4104468124A1C2431998E727729444;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_7_t7F87270DBDA786B5CBAB293E6BB3CA1B8D5EEA87_mBFC7986A10D9105F3978B7FF1ACCFC8DEFCDABF1;
extern const uint32_t g_rgctx_Expression_1_t039D9FBC30A6E71FD348555CDA0EEE1ED534FD97;
extern const uint32_t g_rgctx_Expression_1_Compile_mF2FD373089F04D8ECCC2E0E677B4FDBD1B8A087B;
extern const uint32_t g_rgctx_Func_7_t7F87270DBDA786B5CBAB293E6BB3CA1B8D5EEA87;
extern const uint32_t g_rgctx_InstanceFunctionInvoker_7_t5B3E4390E7D3C5A174B4C674383B5064A6C06272;
extern const uint32_t g_rgctx_Func_7_t7F87270DBDA786B5CBAB293E6BB3CA1B8D5EEA87;
extern const uint32_t g_rgctx_TTarget_tA2C83F64E73974094F04E4B802DBD7228089868A;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_tF272F21D190B52292817193FAC0E1EF3EE10F629;
extern const uint32_t g_rgctx_InstanceInvokerBase_1_CompileExpression_mD93B29FF23F0FC2FFA3200AC5C86757741F6C1D2;
extern const uint32_t g_rgctx_OptimizedReflection_VerifyInstanceTarget_TisTTarget_tA2C83F64E73974094F04E4B802DBD7228089868A_m6B3734E92013FB640FCF4C2D3F37BF51063C8F16;
extern const uint32_t g_rgctx_TTarget_tCD183E58BE3BAC47547C2FFDAE1F1DDA78BF66F2;
extern const uint32_t g_rgctx_TProperty_t0AC578A4FFDF3C55817BF6BCE830CEB1FDC0929E;
extern const uint32_t g_rgctx_InstancePropertyAccessor_2_tB6CFB4A87B9BF73E0DAC00AD731A9E642F131656;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_2_t1B6994F0E41E50FBF3AC444E1657A64049F33ADC_mF04DE42CBA80254A32B286D73301F9E278B7598D;
extern const uint32_t g_rgctx_Expression_1_t053F6083C0FB9B90079A9BF5240C3E2E3595F3F4;
extern const uint32_t g_rgctx_Expression_1_Compile_mA79C4059C83D4F2F37074F231D4D44C004AFB92F;
extern const uint32_t g_rgctx_Func_2_t1B6994F0E41E50FBF3AC444E1657A64049F33ADC;
extern const uint32_t g_rgctx_Action_2_tADB29402D7C47BABC12285653F4A57B28144BDEE;
extern const uint32_t g_rgctx_Action_2_tADB29402D7C47BABC12285653F4A57B28144BDEE;
extern const uint32_t g_rgctx_Func_2_t1B6994F0E41E50FBF3AC444E1657A64049F33ADC;
extern const uint32_t g_rgctx_OptimizedReflection_VerifyInstanceTarget_TisTTarget_tCD183E58BE3BAC47547C2FFDAE1F1DDA78BF66F2_mC31B55B8B9E9EB11EAF619D13D5D6CB09DD05015;
extern const uint32_t g_rgctx_InstancePropertyAccessor_2_GetValueUnsafe_m5EF8AF1E718FD61630EEC257A979DBEF44B8CC7C;
extern const uint32_t g_rgctx_TTarget_tCD183E58BE3BAC47547C2FFDAE1F1DDA78BF66F2;
extern const uint32_t g_rgctx_Func_2_Invoke_mC7BE7F5179A43CB2208449418CFE4B9E0181C2F0;
extern const uint32_t g_rgctx_TProperty_t0AC578A4FFDF3C55817BF6BCE830CEB1FDC0929E;
extern const uint32_t g_rgctx_InstancePropertyAccessor_2_SetValueUnsafe_mA284DD836865045862733320F6CB175784B8DA4D;
extern const uint32_t g_rgctx_Action_2_Invoke_m2926F1EA01D1146C40676A130444A1B826AC0886;
extern const uint32_t g_rgctx_TParam_t2677EE2955EE1DF1B224D9DF181AEDDD9B75A15A;
extern const uint32_t g_rgctx_TTArget_t5E1B3E08A8BB88B27DE0F3CEC01DCD2B827F736B;
extern const uint32_t g_rgctx_InvokerBase_VerifyArgument_TisTParam0_t70883A0781F5F6CB2B198778F44E2822470F29E4_mF82D8BD332425F15499922FE042D8C7C96007B19;
extern const uint32_t g_rgctx_StaticActionInvoker_1_InvokeUnsafe_m10D17E37F677F401F7FDB866E26A584FA46F01DB;
extern const uint32_t g_rgctx_StaticActionInvoker_1_t09B9E68028DE17162F06E072105DB72FDBA00EE1;
extern const uint32_t g_rgctx_Action_1_tAFF9C802458582D72A297B22E34E95EC770873AC;
extern const uint32_t g_rgctx_TParam0_t70883A0781F5F6CB2B198778F44E2822470F29E4;
extern const uint32_t g_rgctx_Action_1_Invoke_m677C57CEFC932B08AEB28A8F38DDB7D64F63C4E8;
extern const uint32_t g_rgctx_TParam0_t70883A0781F5F6CB2B198778F44E2822470F29E4;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_1_tAFF9C802458582D72A297B22E34E95EC770873AC_mE6A78B3E1F299ED537CD194023E333803EAB2348;
extern const uint32_t g_rgctx_Expression_1_tDC88FCAA5348ABFD247A277D54F710CC23A737DB;
extern const uint32_t g_rgctx_Expression_1_Compile_m25325FEBD75B8AC2A4D6313ACDC5A5EC0E2F5C90;
extern const uint32_t g_rgctx_StaticActionInvoker_1_U3CCreateDelegateU3Eb__7_0_mAE332879453DA0C27E52016AE36FC972002D74AA;
extern const uint32_t g_rgctx_Action_1__ctor_mFD2F00F129B99B543D0E671A17AD0ECA82448E08;
extern const uint32_t g_rgctx_Action_1_tAFF9C802458582D72A297B22E34E95EC770873AC;
extern const uint32_t g_rgctx_TParam0_t03C009C4389AEE079A6EB554D82EB02D7CD70696;
extern const uint32_t g_rgctx_TParam1_t0524CD6351FBAB5B1D77E280351DD8B7F3457627;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_2_tD457FA8D0F38E20CB44D8759A3BD0F15C9CBC396_m0E9B26BA21031460AB47CD9EBA98CBCE4CF44CF4;
extern const uint32_t g_rgctx_Expression_1_t8FCA21FF311FB9D66698A766E55F0635BDA0B6AE;
extern const uint32_t g_rgctx_Expression_1_Compile_m862242AEE3DF2CC33CECFBCF40EFF106C7DEBB56;
extern const uint32_t g_rgctx_Action_2_tD457FA8D0F38E20CB44D8759A3BD0F15C9CBC396;
extern const uint32_t g_rgctx_StaticActionInvoker_2_t070181137870A525748586429DFFE14ABD1E7A3D;
extern const uint32_t g_rgctx_StaticActionInvoker_2_U3CCreateDelegateU3Eb__7_0_mF44D5B81140A345F041C96CDF53FD389B71F94E1;
extern const uint32_t g_rgctx_Action_2__ctor_m7DDA7ABE1BE46977CB110D5CCB8143FE7AD6C02B;
extern const uint32_t g_rgctx_Action_2_tD457FA8D0F38E20CB44D8759A3BD0F15C9CBC396;
extern const uint32_t g_rgctx_TParam0_t03C009C4389AEE079A6EB554D82EB02D7CD70696;
extern const uint32_t g_rgctx_TParam1_t0524CD6351FBAB5B1D77E280351DD8B7F3457627;
extern const uint32_t g_rgctx_Action_2_Invoke_m934A3E1933904C90645CF168DE484EFF5B4D11A1;
extern const uint32_t g_rgctx_TParam0_tEFA956FD61400367EB77E85D941FAE066DDF0701;
extern const uint32_t g_rgctx_TParam1_t9A1ADE41F11DA9CB8DB3346EDE664E5E93DE025B;
extern const uint32_t g_rgctx_TParam2_tD1C7960BB34152B1C61A5CC652BC1EFB5E34390B;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_3_t6265FA95FB81CA4F40A8C868AF9611F9CCD92E1C_m195FE97A8625653C08054C23BCF550AEF9D37513;
extern const uint32_t g_rgctx_Expression_1_tAB4153540368358A1B540F66DC837774FC09CC39;
extern const uint32_t g_rgctx_Expression_1_Compile_mA1CEA161C26825CF3D6AB25C46512F86FD679D5E;
extern const uint32_t g_rgctx_Action_3_t6265FA95FB81CA4F40A8C868AF9611F9CCD92E1C;
extern const uint32_t g_rgctx_StaticActionInvoker_3_t28190A158BA38E04A379AD6D4F47AAC1899706BA;
extern const uint32_t g_rgctx_StaticActionInvoker_3_U3CCreateDelegateU3Eb__7_0_m9F1EEEE22EF39E42DA74F91E091BAC866366B41C;
extern const uint32_t g_rgctx_Action_3__ctor_mC021598FBAA25FB2E7E87FA080D7078E6ED20F3F;
extern const uint32_t g_rgctx_Action_3_t6265FA95FB81CA4F40A8C868AF9611F9CCD92E1C;
extern const uint32_t g_rgctx_TParam0_tEFA956FD61400367EB77E85D941FAE066DDF0701;
extern const uint32_t g_rgctx_TParam1_t9A1ADE41F11DA9CB8DB3346EDE664E5E93DE025B;
extern const uint32_t g_rgctx_TParam2_tD1C7960BB34152B1C61A5CC652BC1EFB5E34390B;
extern const uint32_t g_rgctx_Action_3_Invoke_m94D190F90B93AC7699E066B8C6DB5309032A0C89;
extern const uint32_t g_rgctx_TParam0_tFB06336859B552FF237677A546EAAE5DDC59FF5F;
extern const uint32_t g_rgctx_TParam1_t68F5173634B79D5E473D41D7CC9BAAE797F90C87;
extern const uint32_t g_rgctx_TParam2_t976FEF93A5CA79E2863FAC9FA8E66E1E405DF724;
extern const uint32_t g_rgctx_TParam3_tD9619AAD0D47762ADA9BF94EAEEDA36F4E17EC4B;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_4_t191026BE3DF97D5A98906CA8CC71A6C0636C69FF_m9AC7CCE9B645EB71FCE1104AACEB776525ACA77C;
extern const uint32_t g_rgctx_Expression_1_tB816D29C570517BCE7152E62FFE523BAA0C1AC82;
extern const uint32_t g_rgctx_Expression_1_Compile_m4652D925B6D04022785D18FE26366B9C1DBCABC6;
extern const uint32_t g_rgctx_Action_4_t191026BE3DF97D5A98906CA8CC71A6C0636C69FF;
extern const uint32_t g_rgctx_StaticActionInvoker_4_t05A4FDD2896876D28632DAEBECFB33D3E93DDB1A;
extern const uint32_t g_rgctx_StaticActionInvoker_4_U3CCreateDelegateU3Eb__7_0_m59730E65302229260427D81DAD1A230C58F71FD2;
extern const uint32_t g_rgctx_Action_4__ctor_m1B6ED995DD1842E5BA79118EB42E4DCDD3371C4F;
extern const uint32_t g_rgctx_Action_4_t191026BE3DF97D5A98906CA8CC71A6C0636C69FF;
extern const uint32_t g_rgctx_TParam0_tFB06336859B552FF237677A546EAAE5DDC59FF5F;
extern const uint32_t g_rgctx_TParam1_t68F5173634B79D5E473D41D7CC9BAAE797F90C87;
extern const uint32_t g_rgctx_TParam2_t976FEF93A5CA79E2863FAC9FA8E66E1E405DF724;
extern const uint32_t g_rgctx_TParam3_tD9619AAD0D47762ADA9BF94EAEEDA36F4E17EC4B;
extern const uint32_t g_rgctx_Action_4_Invoke_mB9A6FC5360FD489994126C57C5CA29C1EFAE818B;
extern const uint32_t g_rgctx_TParam0_t0568BE0E1AB419C578B4216CB910E536E73B5ED9;
extern const uint32_t g_rgctx_TParam1_t898D56D82420FA965FC9337B0CE0A9FEE2A6244A;
extern const uint32_t g_rgctx_TParam2_tB608103294D25D139E5E7A0F41AAA6CE69A22B36;
extern const uint32_t g_rgctx_TParam3_t1A139D7DC6DB929CE3D98A472211D75B45C17995;
extern const uint32_t g_rgctx_TParam4_t437FF73AD9BB2A26EFFFD67B9BB8EEBAEF5B1B0D;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_5_tF92D92F0B7C3BAF6E1D8762A467C0195403CD4C9_m8F43CCE9996449075B755C7E9611A99341FFF79D;
extern const uint32_t g_rgctx_Expression_1_t135295915C5D46A6809E2CBC8242985C1101A932;
extern const uint32_t g_rgctx_Expression_1_Compile_m1D73DE114FF8C5935CBF98AC8C671003AAC24795;
extern const uint32_t g_rgctx_Action_5_tF92D92F0B7C3BAF6E1D8762A467C0195403CD4C9;
extern const uint32_t g_rgctx_StaticActionInvoker_5_tDD0E844E53381624DC82FA9875016210964CA973;
extern const uint32_t g_rgctx_StaticActionInvoker_5_U3CCreateDelegateU3Eb__7_0_mC53DF78ECF694BA312D4AEB6559F0D7E646C5791;
extern const uint32_t g_rgctx_Action_5__ctor_mF60E0B16643E9A87C2FA0F46C982FA6AB7B4BB25;
extern const uint32_t g_rgctx_Action_5_tF92D92F0B7C3BAF6E1D8762A467C0195403CD4C9;
extern const uint32_t g_rgctx_TParam0_t0568BE0E1AB419C578B4216CB910E536E73B5ED9;
extern const uint32_t g_rgctx_TParam1_t898D56D82420FA965FC9337B0CE0A9FEE2A6244A;
extern const uint32_t g_rgctx_TParam2_tB608103294D25D139E5E7A0F41AAA6CE69A22B36;
extern const uint32_t g_rgctx_TParam3_t1A139D7DC6DB929CE3D98A472211D75B45C17995;
extern const uint32_t g_rgctx_TParam4_t437FF73AD9BB2A26EFFFD67B9BB8EEBAEF5B1B0D;
extern const uint32_t g_rgctx_Action_5_Invoke_m74431BBDBA4239D50102FE3C2CF85763C7A3AA4C;
extern const uint32_t g_rgctx_TField_tBF2975972161099FC1FC7D544D0ACDA8527C91E6;
extern const uint32_t g_rgctx_StaticFieldAccessor_1_tCC9D8C714930B415ACD10FD3EF2B6D228381080B;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_t189A4796632FD3DA38A702D29066AB6E42599876;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0__ctor_mF12FECA4313E16FDB8F383C4DB59F9B0B87510B2;
extern const uint32_t g_rgctx_TField_tBF2975972161099FC1FC7D544D0ACDA8527C91E6;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_U3CCompileU3Eb__2_mC1C6D62ECF1BABCFA178B2A1CDE949FE7E1CC446;
extern const uint32_t g_rgctx_Func_1_tA2C5CACF3E0D74FC6D170A38F11FF02A7E009C32;
extern const uint32_t g_rgctx_Func_1__ctor_m99908619EA7CEDA0B505032AB38251404D545FFF;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_1_tA2C5CACF3E0D74FC6D170A38F11FF02A7E009C32_m226064F5701F4C087651B8694DE5018EAB9D652C;
extern const uint32_t g_rgctx_Expression_1_tBFB23331FE9BD6B59D88FCF49C17AEAEDA3D2095;
extern const uint32_t g_rgctx_Expression_1_Compile_mEB27E3BB4E0B48DF4A41F7856F75E263BDC824BD;
extern const uint32_t g_rgctx_Expression_Lambda_TisAction_1_t43410EE9683BC5A93519D9F7BA4E32140F99F08C_m0F4CA8B9AC0AF4A2EBFF1A6536A3AAC9A98A9C9D;
extern const uint32_t g_rgctx_Expression_1_t5817CCDFB4CA41949ABB6335192D537A177BCF32;
extern const uint32_t g_rgctx_Expression_1_Compile_m5C9AE33A9D43EDDB9DBDDD5616A566300EDD937A;
extern const uint32_t g_rgctx_Action_1_t43410EE9683BC5A93519D9F7BA4E32140F99F08C;
extern const uint32_t g_rgctx_StaticFieldAccessor_1_U3CCompileU3Eb__5_0_mA72AFB166710777B3780217078D11E4CD6A0DB3A;
extern const uint32_t g_rgctx_StaticFieldAccessor_1_U3CCompileU3Eb__5_1_mA5731F9CC2B746E708ECA788F52C9726808FFE09;
extern const uint32_t g_rgctx_Action_1__ctor_mB1A6B6CC57F709D33EFFA32E1ABF34466926245F;
extern const uint32_t g_rgctx_StaticFieldAccessor_1_GetValueUnsafe_mA7AE278A42AE954354754F8D7325E4B5CF65D5A9;
extern const uint32_t g_rgctx_Func_1_Invoke_m194EFDE4A01FC32D2577B3676FAA0BA038DFF83E;
extern const uint32_t g_rgctx_StaticFieldAccessor_1_SetValueUnsafe_m8A0AD4316ED972FF6872697A2E95408E1FFD18E5;
extern const uint32_t g_rgctx_Action_1_Invoke_mDD47E78F1A8522A8A2B70BA5B267887F6EE90FCB;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_t3845781BC38114BC33D4F1EF6AC25F0999E5B603;
extern const uint32_t g_rgctx_TField_t6BAE5F3DBF42A82312FCE6DF4361DDE55C2B2291;
extern const uint32_t g_rgctx_TResult_t7A7534C8B321C1E0D93733409251CB4B27599809;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1__ctor_mBBC86E7C45B6F5509FC72BAD565FDB27A56713BD;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1_t53925464FCCF3B3B4C8FB6DB326A49489017E873;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_1_t459C274F717E668E077E1922AE1526FD2113C438_m46F20923011ABC90B17D514CDAD7B6C69A4B087E;
extern const uint32_t g_rgctx_Expression_1_t7A6DD889F9254E3EF16DEE41F46ECB55CA735AC2;
extern const uint32_t g_rgctx_Expression_1_Compile_m5D036AE77D2A330F7DD44F7104526C6BBA11A35E;
extern const uint32_t g_rgctx_Func_1_t459C274F717E668E077E1922AE1526FD2113C438;
extern const uint32_t g_rgctx_StaticFunctionInvoker_1_t49D9A8B53E4B8977237A849896983C080C83E2D1;
extern const uint32_t g_rgctx_StaticFunctionInvoker_1_U3CCreateDelegateU3Eb__7_0_m6B32B099AA6C127AA1485F563100F8B30B4FB593;
extern const uint32_t g_rgctx_Func_1__ctor_mB7506D6B7D60CE1B783FED31B0D0A3E0DAA8891A;
extern const uint32_t g_rgctx_Func_1_t459C274F717E668E077E1922AE1526FD2113C438;
extern const uint32_t g_rgctx_Func_1_Invoke_m29A223601D7BE2B002182008FE07D69E7B66EF8B;
extern const uint32_t g_rgctx_TResult_t8BBBBAA87C6BB6D3B1709C628DEFC34FB4AA96DB;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1__ctor_m5B677D0E9D7105A25FA1FD1F74E024943E12BBAD;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1_tC2FE945FD0BABCA69879394F50E032D73C50BFD8;
extern const uint32_t g_rgctx_InvokerBase_VerifyArgument_TisTParam0_t45238165CE2805A1C7261B5F79E9CDE7E3AC84A0_mC9F25E114B5F61A6A06D55C829E649E6F2061572;
extern const uint32_t g_rgctx_StaticFunctionInvoker_2_InvokeUnsafe_mA5AFA353896E811242FC5B8B2D94DBDC1F69FBF9;
extern const uint32_t g_rgctx_StaticFunctionInvoker_2_t2997263388D7F2C66AAB05F9417021456899E938;
extern const uint32_t g_rgctx_Func_2_t2A536DE3C9DF8316BE8DA8C2C25F209347813CF1;
extern const uint32_t g_rgctx_TParam0_t45238165CE2805A1C7261B5F79E9CDE7E3AC84A0;
extern const uint32_t g_rgctx_Func_2_Invoke_m8B60781815ECBE3BBBE2C69B116F37010B9E03AD;
extern const uint32_t g_rgctx_TResult_t5790748B85A511329D49845DFD418C3FC2D656AD;
extern const uint32_t g_rgctx_TParam0_t45238165CE2805A1C7261B5F79E9CDE7E3AC84A0;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_2_t2A536DE3C9DF8316BE8DA8C2C25F209347813CF1_m8D001FEF4C3CA7E43660F5EB31519A8775C0E9A3;
extern const uint32_t g_rgctx_Expression_1_t7D0AC3AA8B6CE107303185107359742FDEACD0B4;
extern const uint32_t g_rgctx_Expression_1_Compile_m1B93EE3C1708327F5B3D43EBF9FF10C570C6A7E0;
extern const uint32_t g_rgctx_StaticFunctionInvoker_2_U3CCreateDelegateU3Eb__7_0_m96D3354A2571FE5136FE07447B4017A173EFB806;
extern const uint32_t g_rgctx_Func_2__ctor_m0F4C8384540E7991281F88E85EC4DF54519F797D;
extern const uint32_t g_rgctx_Func_2_t2A536DE3C9DF8316BE8DA8C2C25F209347813CF1;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1__ctor_m8CFD1FEA00E34BE512D718FB64E7055772D49418;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1_tF2EFBB465F423484E71627650C022F09681A066C;
extern const uint32_t g_rgctx_TParam0_tCEA3CE3D4BEBE1F88471D325E631F3FFC5149D84;
extern const uint32_t g_rgctx_TParam1_t116B30461427C070A91828895FB38E2C4FA5D7BC;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_3_t13C5FF1C32D007AB290C5617CF17A1A90CC2E951_mCCAC8D04849FCBF766C93F6B5C7BD2DD12CD7B9B;
extern const uint32_t g_rgctx_Expression_1_tEA9D6981D846E1B01E139E3DF59F8222FF867E5D;
extern const uint32_t g_rgctx_Expression_1_Compile_m1BADB3E4D049D52310909476E570D6FF6CF93AFE;
extern const uint32_t g_rgctx_Func_3_t13C5FF1C32D007AB290C5617CF17A1A90CC2E951;
extern const uint32_t g_rgctx_StaticFunctionInvoker_3_t60FDA0ED217B84AF589030A1FBDD3EB75FF88FD5;
extern const uint32_t g_rgctx_StaticFunctionInvoker_3_U3CCreateDelegateU3Eb__7_0_m35950B90124E675B841A17B6CACB4D3201BFFB3A;
extern const uint32_t g_rgctx_Func_3__ctor_mA7FAF17D942F191541959D2383E2B596608EAD90;
extern const uint32_t g_rgctx_Func_3_t13C5FF1C32D007AB290C5617CF17A1A90CC2E951;
extern const uint32_t g_rgctx_TParam0_tCEA3CE3D4BEBE1F88471D325E631F3FFC5149D84;
extern const uint32_t g_rgctx_TParam1_t116B30461427C070A91828895FB38E2C4FA5D7BC;
extern const uint32_t g_rgctx_Func_3_Invoke_mA2E7A118A1A04384D0F3AA63B50045E6B47F8A0C;
extern const uint32_t g_rgctx_TResult_t166D5E5B72E6F62D9D0EF4865B665464277F5DDE;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1__ctor_m1C7CA0DA5DD6F118CBA3C26333AA1C39695C9F03;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1_t695A85CD59117660D9AECBEE29420E8B28CF267A;
extern const uint32_t g_rgctx_TParam0_tBB0D083AFEA16C69BC0659DE5A305C3A9D66EA40;
extern const uint32_t g_rgctx_TParam1_t1A661897D4059EF3B4B6FB7828972DFF71144665;
extern const uint32_t g_rgctx_TParam2_t05D649BE45E61B19A1EDD18CDE26D19563F2A0E1;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_4_t6C63D15AB0828AE655769298D98650C845F9D4ED_m004DE9F2EF9BD4812CC0232746ED9C2E1D2DBF5F;
extern const uint32_t g_rgctx_Expression_1_tDD6CF5C2903E8C0BEE393251FD0C8E235F630D31;
extern const uint32_t g_rgctx_Expression_1_Compile_m008BD893724C5AF97A6B13AD467BB41C1014CA15;
extern const uint32_t g_rgctx_Func_4_t6C63D15AB0828AE655769298D98650C845F9D4ED;
extern const uint32_t g_rgctx_StaticFunctionInvoker_4_t221252ECB37F0B11D3FBDE582ECBBE8742CE433A;
extern const uint32_t g_rgctx_StaticFunctionInvoker_4_U3CCreateDelegateU3Eb__7_0_mE89B125D0F0D705AC05729E1305D15247AF1198B;
extern const uint32_t g_rgctx_Func_4__ctor_mC77E38527C23CD5F4571465ED61CAF706B4D5996;
extern const uint32_t g_rgctx_Func_4_t6C63D15AB0828AE655769298D98650C845F9D4ED;
extern const uint32_t g_rgctx_TParam0_tBB0D083AFEA16C69BC0659DE5A305C3A9D66EA40;
extern const uint32_t g_rgctx_TParam1_t1A661897D4059EF3B4B6FB7828972DFF71144665;
extern const uint32_t g_rgctx_TParam2_t05D649BE45E61B19A1EDD18CDE26D19563F2A0E1;
extern const uint32_t g_rgctx_Func_4_Invoke_mF99CA6A98C3ABCD975F90FFCD7411D032FC3B416;
extern const uint32_t g_rgctx_TResult_tF56A3C6FC6DE9140668F3D648E39D673C4C03DFB;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1__ctor_m5789AD168D76FBFE6FFB778596D72675431499F4;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1_t97C11A31BBAB0EBE4B005A211ACF9E731D661900;
extern const uint32_t g_rgctx_TParam0_t9D7F142A17427503734D29F4E05CC308F9DF9D88;
extern const uint32_t g_rgctx_TParam1_t8962A9306BC9FEC0F1EB221DFFFBED58DA656ACF;
extern const uint32_t g_rgctx_TParam2_t0EED94CB47DBDC4E819232D590F1CDBC83971852;
extern const uint32_t g_rgctx_TParam3_tC999AF431FC6A861A9A75A4032D5BDCD8B1C87A5;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_5_tEEA67AD4242DB2D4CF03B8010A961B189E0FE57B_mE7C5736A6E55F101F8D00D0BE1BC61FA4D4AE3EF;
extern const uint32_t g_rgctx_Expression_1_t7571B0CC133ACBF962A069CF6D33E1A9188B1B16;
extern const uint32_t g_rgctx_Expression_1_Compile_m88DBFB54249C6B2ACB02DD33BC9DF85B32A5E249;
extern const uint32_t g_rgctx_Func_5_tEEA67AD4242DB2D4CF03B8010A961B189E0FE57B;
extern const uint32_t g_rgctx_StaticFunctionInvoker_5_tEE03821258950F13A1F1CEEE21ED24512AFAAAFD;
extern const uint32_t g_rgctx_StaticFunctionInvoker_5_U3CCreateDelegateU3Eb__7_0_mBB06A64AFE3E3BEB9309DE43E87A51C7EA818FB6;
extern const uint32_t g_rgctx_Func_5__ctor_mF0B389EAE16FB3542DD5ABD20BBE1DA9A174022A;
extern const uint32_t g_rgctx_Func_5_tEEA67AD4242DB2D4CF03B8010A961B189E0FE57B;
extern const uint32_t g_rgctx_TParam0_t9D7F142A17427503734D29F4E05CC308F9DF9D88;
extern const uint32_t g_rgctx_TParam1_t8962A9306BC9FEC0F1EB221DFFFBED58DA656ACF;
extern const uint32_t g_rgctx_TParam2_t0EED94CB47DBDC4E819232D590F1CDBC83971852;
extern const uint32_t g_rgctx_TParam3_tC999AF431FC6A861A9A75A4032D5BDCD8B1C87A5;
extern const uint32_t g_rgctx_Func_5_Invoke_m50B6593A304F71189C7553D92CE5E2DFFBF0BB6B;
extern const uint32_t g_rgctx_TResult_t688022EDC0380C26312208B41CB4EB69EEECAE3F;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1__ctor_mBBE48ED80D0CA8C3F617B516A88A0EDD703EE878;
extern const uint32_t g_rgctx_StaticFunctionInvokerBase_1_tDB3F0D5999A25209CFBB20B091AD526331AC311D;
extern const uint32_t g_rgctx_TParam0_tBE109D97A87AE383D7FAD3171A1CA2E2B29C907E;
extern const uint32_t g_rgctx_TParam1_t724B0D93D2F88B70F2AB9B96B8C438A0F38BB509;
extern const uint32_t g_rgctx_TParam2_t81B12B2685ED1C16B2C5BC1976779FA7305EEA45;
extern const uint32_t g_rgctx_TParam3_tBC7D452F3224EEA79E046C459CAA6E2B218548C5;
extern const uint32_t g_rgctx_TParam4_t82AD0C9CD8D58F5CAF936FD86C74C1B0EFAD5BDF;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_6_t748AE0665192BEC3838DE12E012845FA3E5D73BF_m9E36E06DE012C2B7EA9221F476D41A37BBC20D32;
extern const uint32_t g_rgctx_Expression_1_tBDA1518353D9903F760D971675410A07EF0FF412;
extern const uint32_t g_rgctx_Expression_1_Compile_mCE43879CDFF85D1D4FD276C115FAF38B9AF423DB;
extern const uint32_t g_rgctx_Func_6_t748AE0665192BEC3838DE12E012845FA3E5D73BF;
extern const uint32_t g_rgctx_StaticFunctionInvoker_6_t5A3D2EECE2B99C1CBEF24D047954EE1262098391;
extern const uint32_t g_rgctx_StaticFunctionInvoker_6_U3CCreateDelegateU3Eb__7_0_mAD7965C4BFC1A396576E72DFAA54964894CAF3F8;
extern const uint32_t g_rgctx_Func_6__ctor_m0D36DD38F358E11767682F8EAB5F62C642A0A02E;
extern const uint32_t g_rgctx_Func_6_t748AE0665192BEC3838DE12E012845FA3E5D73BF;
extern const uint32_t g_rgctx_TParam0_tBE109D97A87AE383D7FAD3171A1CA2E2B29C907E;
extern const uint32_t g_rgctx_TParam1_t724B0D93D2F88B70F2AB9B96B8C438A0F38BB509;
extern const uint32_t g_rgctx_TParam2_t81B12B2685ED1C16B2C5BC1976779FA7305EEA45;
extern const uint32_t g_rgctx_TParam3_tBC7D452F3224EEA79E046C459CAA6E2B218548C5;
extern const uint32_t g_rgctx_TParam4_t82AD0C9CD8D58F5CAF936FD86C74C1B0EFAD5BDF;
extern const uint32_t g_rgctx_Func_6_Invoke_mBFEA0983E6C086D5F8588B03EDD403F930C538AB;
extern const uint32_t g_rgctx_TResult_t4DA15E77AB729D43CA356186D298BB3DD50FEA36;
extern const uint32_t g_rgctx_TProperty_tA65616EE0394D1D8D986F5ED5379C3AAE5558330;
extern const uint32_t g_rgctx_StaticPropertyAccessor_1_tAA87B9629DA64C638A1EF588A6E701FB63D29548;
extern const uint32_t g_rgctx_Expression_Lambda_TisFunc_1_t02FF0498DB701C793476BA279D6EC631120895DA_m121E7C23CEDE1AC099A94773EEB0B8CADB3D5DB5;
extern const uint32_t g_rgctx_Expression_1_tBA0351396328D3389ADA6D481ECB3C55FE220516;
extern const uint32_t g_rgctx_Expression_1_Compile_m8638560D25F045BBBD878ED42AD054D6A1085F53;
extern const uint32_t g_rgctx_Func_1_t02FF0498DB701C793476BA279D6EC631120895DA;
extern const uint32_t g_rgctx_Action_1_t87D454F49933A03DADF029DDC1F21884E447E739;
extern const uint32_t g_rgctx_Action_1_t87D454F49933A03DADF029DDC1F21884E447E739;
extern const uint32_t g_rgctx_Func_1_t02FF0498DB701C793476BA279D6EC631120895DA;
extern const uint32_t g_rgctx_StaticPropertyAccessor_1_GetValueUnsafe_m20AF22E43F30A6A2B399E465D02C4672AD7CAA35;
extern const uint32_t g_rgctx_Func_1_Invoke_mC423C9EA9B612312D38AC82794F717AE5605BD60;
extern const uint32_t g_rgctx_TProperty_tA65616EE0394D1D8D986F5ED5379C3AAE5558330;
extern const uint32_t g_rgctx_StaticPropertyAccessor_1_SetValueUnsafe_m37BA9455A956CCF411D855E51A8E9AA919E831E8;
extern const uint32_t g_rgctx_Action_1_Invoke_mE55020C56C2115E44652A9A9DA10CB4A9AED069E;
extern const uint32_t g_rgctx_TAttribute_t0326D6C5451F5F4EC47DF88DA4928D4544427CD2;
extern const uint32_t g_rgctx_Enumerable_Cast_TisTAttribute_t0326D6C5451F5F4EC47DF88DA4928D4544427CD2_m5130FB1D506822CDACA65C2A851BE93EAD9AA993;
extern const uint32_t g_rgctx_IEnumerable_1_tD4BB669E703CA2F811B494F5F1AC1453CD9ECDD3;
extern const uint32_t g_rgctx_Singleton_1_tED45646493AD41EC54F4FAF96E9CC16F3EB1429E;
extern const uint32_t g_rgctx_Singleton_1_tED45646493AD41EC54F4FAF96E9CC16F3EB1429E;
extern const uint32_t g_rgctx_HashSet_1_t783349C7F2BC3A3127A6EFAA27CC3D5F66D1C75F;
extern const uint32_t g_rgctx_HashSet_1__ctor_m2E6EAACAE07F7D9AE0372F83D11C0DBA4FD79237;
extern const uint32_t g_rgctx_T_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF;
extern const uint32_t g_rgctx_T_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF;
extern const uint32_t g_rgctx_Singleton_1_Instantiate_m685848D4FA7A2449E8625088C3B768BF3C5F2416;
extern const uint32_t g_rgctx_Object_FindObjectsOfType_TisT_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF_m72B28D6AD7FD3FE3D60E48D481630F0B386828C2;
extern const uint32_t g_rgctx_TU5BU5D_t19432F5387D337C7B003B5BD28C0E04C30ACD28F;
extern const uint32_t g_rgctx_Singleton_1_FindObjectsOfType_mEFC9A4B8456C75C50240D6014C7EB2FC8B7AC55B;
extern const uint32_t g_rgctx_Singleton_1_FindInstances_m66893225420E4C17D1963FE32B08458966624AB7;
extern const uint32_t g_rgctx_Singleton_1_get_automatic_m0F009012D443F00A33C98869C6845DD593A19830;
extern const uint32_t g_rgctx_Singleton_1_get_name_m29CFBC438A226A981B32377880C50FAEEA3F5066;
extern const uint32_t g_rgctx_Singleton_1_get_hideFlags_m9D6C18F1761D6D531017D3C8B811BB92625DE932;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF_mE164806ACE31D0974AFB61B69E2230CCB061840F;
extern const uint32_t g_rgctx_Singleton_1_Awake_m7009914966616A12FF3DCD5899BE842774C9CCF4;
extern const uint32_t g_rgctx_Singleton_1_get_persistent_m8E260B7077A65F9172CD27ABA8F5F06E7E1840A1;
extern const uint32_t g_rgctx_EnsureThat_IsNotNull_TisT_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF_m0165570A314DAF2068B70D63C1928C167C2EF932;
extern const uint32_t g_rgctx_HashSet_1_Contains_m0535DB9F10DF4D450A5BCF232B3C38984E12A247;
extern const uint32_t g_rgctx_HashSet_1_Add_m82FBEB1A0E23BA67F1C92FA44C2B1D59C69B603E;
extern const uint32_t g_rgctx_TU5BU5D_tF3B273F3F094C9E634FBF2D135AB01C65C8D95C1;
extern const uint32_t g_rgctx_Empty_1_tAD140F158571BD0C7DED03139F7545F12BF54E0A;
extern const uint32_t g_rgctx_TU5BU5D_tF3B273F3F094C9E634FBF2D135AB01C65C8D95C1;
extern const uint32_t g_rgctx_Empty_1_tAD140F158571BD0C7DED03139F7545F12BF54E0A;
extern const uint32_t g_rgctx_List_1_tEC047EB534499FC8CF217001DC0264023DEFCFB7;
extern const uint32_t g_rgctx_List_1__ctor_mFAE2EF3B560A9A9B0895B3812A2DEDA9C518BCA6;
extern const uint32_t g_rgctx_HashSet_1_t86A553DF03365B3B91C63CD7A45B03070BC05717;
extern const uint32_t g_rgctx_HashSet_1__ctor_m7C00A423590FAA9F52E318E7479A6F1AD10AB3DE;
extern const uint32_t g_rgctx_T_tC0F2CC5AA620443E9DF787C656FA9A3EC6046D0D;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tC0F2CC5AA620443E9DF787C656FA9A3EC6046D0D_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
extern const uint32_t g_rgctx_T1_t12FD4C81F8668B7023F9E9EC1E7845E2D15E6F7C;
extern const Il2CppRGCTXConstrainedData g_rgctx_T1_t12FD4C81F8668B7023F9E9EC1E7845E2D15E6F7C_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
extern const uint32_t g_rgctx_T2_t43CDE2394B80FB56F5A398CBAE0597EFCAD9BAEC;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_t43CDE2394B80FB56F5A398CBAE0597EFCAD9BAEC_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1_t13515DCE2E387DB77A8F87B535E8C9D40203ED5D;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1__ctor_mF28051DC7E21CCD3336D77B117A4C71F50CFEA23;
extern const uint32_t g_rgctx_IEnumerable_1_t1CA2E9C943941178DCD887D86ED97A8B5E1FB802;
extern const uint32_t g_rgctx_IEnumerable_1_t1C71122DC5846575B23595569AD61EEB34CC4A45;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_t04325BC0737AC3234D4C0826979B56979163DA6B;
extern const uint32_t g_rgctx_Func_2_tF96F336AF1EE764BED5BD39B7A37ED25879719F3;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_t04325BC0737AC3234D4C0826979B56979163DA6B;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_U3CNotNullU3Eb__2_0_mC3BC2CADA58184778C3A890992216AAE84C24DBA;
extern const uint32_t g_rgctx_Func_2__ctor_mB1C5A806B682C35E30A1432FC61808885ABF398F;
extern const uint32_t g_rgctx_Enumerable_Where_TisT_tA168C99AE6C83FAF42DD7D1AAF71FCD557B57BEB_m545FE7D30A9130C957840159FED7924F5DD6FA8E;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_tF637B46223DF17878F707465E7ABE469EFC7A4DB;
extern const uint32_t g_rgctx_U3CU3Ec__2_1__ctor_m9B79CAB6D3FFE4C3A835604D9DD4C91FF0AAA322;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_tF637B46223DF17878F707465E7ABE469EFC7A4DB;
extern const uint32_t g_rgctx_T_t7E8436779A6D74C1E84828869FFC96948DEAFCED;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1_t90E48C012CB874F2E571E4683972BF2ADA1A0FCE;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1_U3CU3Em__Finally2_m0CF75D901D4F9E05EF91089B9A9251B591702B4D;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1_U3CU3Em__Finally1_m084B05E2E19862394815C2F5C3CD06E789465AA0;
extern const uint32_t g_rgctx_Enumerable_OfType_TisT_tD4EC27FE359CA26C71342AFD1B3E20628BCFB63D_m53F131BBD066EE67BB23C786AF2464EF4763123F;
extern const uint32_t g_rgctx_IEnumerable_1_t7E2301D170FD43561F934398F5C5B93DA8BACCBC;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_mD88E0D1522F05D09ED1820EAAFF8E0AA77A67B6C;
extern const uint32_t g_rgctx_IEnumerator_1_tFAE092251D764997AFF22D13A71974C3CE4DD5E1;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m1CAEE709F6DFF620EC07B9843CB88BA05A98539C;
extern const uint32_t g_rgctx_T_tD4EC27FE359CA26C71342AFD1B3E20628BCFB63D;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1_System_IDisposable_Dispose_m7DC901A01D2775B045FFF20D0DC051C4FF3C684E;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1__ctor_m04879677A566C2EA42BB3D0424CB2C7551824D4B;
extern const uint32_t g_rgctx_U3CConcatU3Ed__0_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mFD7B63A4ACEA04B14D9C83CDD68266747946714F;
extern const uint32_t g_rgctx_T_tA434DAAA533BF9E7D8D102F6B31F557EAC68F27E;
extern const uint32_t g_rgctx_ReferenceEqualityComparer_1_GetHashCode_mE8D6E49E2683897C8D95B6D3AFF7AADB235F6CCB;
extern const uint32_t g_rgctx_ReferenceEqualityComparer_1_t4EA150DA5DAC8063D06564B23B5425239EC0D851;
extern const uint32_t g_rgctx_ReferenceEqualityComparer_1_t4EA150DA5DAC8063D06564B23B5425239EC0D851;
extern const uint32_t g_rgctx_ReferenceEqualityComparer_1__ctor_mB927257D8C9F96C81754D0C80A94DD97D0A23798;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_mDB5CADDED5947D3289BC6A965130309EBD7738BB;
extern const uint32_t g_rgctx_EqualityComparer_1_t4F75BB3292C15C9AD8CAED648B1072D41EC46A40;
extern const uint32_t g_rgctx_EqualityComparer_1_t4F75BB3292C15C9AD8CAED648B1072D41EC46A40;
extern const uint32_t g_rgctx_TU5BU5D_tBCB4AFD09117205E00F693B9B61ADD044B58E03B;
extern const uint32_t g_rgctx_T_t059EDBAAA8BB194D521A4740722ABDEDE6811131;
extern const uint32_t g_rgctx_EqualityComparer_1_Equals_m666A63604BEA714B24FD4E7AEB456ED33F6A6E98;
extern const uint32_t g_rgctx_T_t3A237E4AA0892E30C7702FD24E2E0300B01B1262;
extern const uint32_t g_rgctx_T_t3A237E4AA0892E30C7702FD24E2E0300B01B1262;
extern const uint32_t g_rgctx_TU26_tC180A47FBC84684922ACBB32FFE9D7EA918B4F3A;
extern const uint32_t g_rgctx_T_t66A6B07095DD463B03FC01500296FB7356DCAEB1;
extern const uint32_t g_rgctx_T_t66A6B07095DD463B03FC01500296FB7356DCAEB1;
extern const uint32_t g_rgctx_T_t5A1B1DFB3DBD00270ED3B0AD25CD7659CF44974B;
extern const uint32_t g_rgctx_T_t5A1B1DFB3DBD00270ED3B0AD25CD7659CF44974B;
extern const uint32_t g_rgctx_T_t4FF9F0AFC05B7E0F85A4B934B59D234155F6E4A5;
extern const uint32_t g_rgctx_T_t4FF9F0AFC05B7E0F85A4B934B59D234155F6E4A5;
extern const uint32_t g_rgctx_T_tBE9A67E171C85D8CA29A1569A871BCDB274C8EBF;
extern const uint32_t g_rgctx_T_tBE9A67E171C85D8CA29A1569A871BCDB274C8EBF;
extern const uint32_t g_rgctx_TModel_tA846BAB14AD72C3F97B08FAFEF7B2629C8054C13;
extern const uint32_t g_rgctx_TModel_tA846BAB14AD72C3F97B08FAFEF7B2629C8054C13;
extern const uint32_t g_rgctx_fsDirectConverter_1_t0EF3B59944746AB537D9449A99F11833F2D78EDE;
extern const uint32_t g_rgctx_fsDirectConverter_1_DoSerialize_m4A44064DDCD50F053CDFC38CF4D3F62CA8874B5E;
extern const uint32_t g_rgctx_fsDirectConverter_1_DoDeserialize_m1A512349B1E46B9C069209815C166DB902EC8822;
extern const uint32_t g_rgctx_TModelU26_t30E33E940C62C34794AE1FC252140E33D3EB618F;
extern const uint32_t g_rgctx_T_tEB7E9E00FDC44AE50752B1BF0E0F507EF4AFFBAB;
extern const uint32_t g_rgctx_T_tEB7E9E00FDC44AE50752B1BF0E0F507EF4AFFBAB;
extern const uint32_t g_rgctx_TU26_t3BE3F4B70DC24BF1596038EE672847471DD041AF;
extern const uint32_t g_rgctx_T_t594BBE454D274A88EA1B5E96B1F70F1AFAB8E1AC;
extern const uint32_t g_rgctx_T_t594BBE454D274A88EA1B5E96B1F70F1AFAB8E1AC;
extern const uint32_t g_rgctx_fsOption_1_tBA834DAEC7376CB376DA946F0E5F5F6D1E240334;
extern const uint32_t g_rgctx_fsOption_1_get_IsEmpty_m6198B54E39FF8D4513440ACC323FB7815AEEC173;
extern const uint32_t g_rgctx_fsOption_1_tBA834DAEC7376CB376DA946F0E5F5F6D1E240334;
extern const uint32_t g_rgctx_T_tC5D84CBABB6B1934F21007E0A382E362C744EBA6;
extern const uint32_t g_rgctx_T_tF41E1145E66EAE3CF7E6499BD09CBA3A0EF365E4;
extern const uint32_t g_rgctx_fsOption_1_t94E57BA0CA9FA35974F429ECB93CB3C18B13D8DA;
extern const uint32_t g_rgctx_fsOption_1__ctor_mE8C8AFB3E17F0261C53D9F67224575FAFE756426;
extern const uint32_t g_rgctx_TAttribute_t9A4A9352D6A7BA2E2152F2E06FA05DAFBAAF9EC9;
extern const uint32_t g_rgctx_TAttribute_t9A4A9352D6A7BA2E2152F2E06FA05DAFBAAF9EC9;
extern const uint32_t g_rgctx_fsPortableReflection_GetAttribute_TisTAttribute_tC89EDB76B0770E376AE4684BD18227EEC2AAA9D2_m548F9E6A81089AEFB3BDE22D59614D419F3E437C;
extern const uint32_t g_rgctx_TAttribute_tC89EDB76B0770E376AE4684BD18227EEC2AAA9D2;
static const Il2CppRGCTXDefinition s_rgctxValues[561] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD0E7CC5B6021F5900E6584189CDAAB4318ED7523 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Cloner_1_tCCE13274C8C182729D26F6533A793299A8B52C13 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Cloner_1_BeforeClone_mD64C1349F749751152C4C79403154A5427BD99A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Cloner_1_ConstructClone_m0C1278F0465743D092AAC4E74009988BCCB6EFB4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Cloner_1_FillClone_mA3D5B836FBC15DAA935C500D5CF46CEE22196AB4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t1C4A29B72E29891FF677CADAC5361C40CCF57CD9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Cloner_1_AfterClone_mA0C8E699FAA15EB143CCA5613A8038AA3B4031FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3E3386298B2DAC41488753698F0C77739EC52535 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t93A7576DD6096E9C991D8485DA482792DF03B9B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GenericPool_1_tEBC7C4EC2AD92F6ACCF235037E37AD908CF9DBCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GenericPool_1_tEBC7C4EC2AD92F6ACCF235037E37AD908CF9DBCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Stack_1_t545EFA9FDE9E4875EF1829DA0B5C4DF179A3877F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_get_Count_m43F1E0B07999664606B520644ECA44F332C01157 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t07F6064D8AB14BF4CAC41D5C4B2726CB459E5DD0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_m3D2BF2F788B89E8E1DB8DE7BA3D09DDFB3A54E6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0FD06116FFBD9219EDC1E72202B927041A94E956 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Push_m32BD2B01D2797AFACBA85601C264D3176EC1A3AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Pop_mC57DD66029E6C68104578092B9DD92D6DF2AEBED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t6CB8DC49EA36380D2CB9A8D88939EF64A3BF8389 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Add_m452F40361D5361C100001997A0B78335B06389A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Remove_m9E101F43EB15163E7103C80F28606FBEBE17AC78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1__ctor_mAA537F1124AB3B49D374DF33270230B29892B114 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReferenceEqualityComparer_1_t22C4B26ED2BC7C33222ACACB33ACAE157CBAD8BC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReferenceEqualityComparer_1_t22C4B26ED2BC7C33222ACACB33ACAE157CBAD8BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1__ctor_m5E0EAA3401141BC6021C61EB033C7DE67E11826F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_tBDEA3BCC88C388DEB98B2020829BA9F9C3C5A45A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t10416D203278A79DF63D5150ED9F4EF81D97EB1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Cast_TisT_t10416D203278A79DF63D5150ED9F4EF81D97EB1B_mA59ACFD208BE2F8E7C38EE039D7C133ADB9BA2F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tDACA09A7604D3E5D9DB87E1B381F0E3974139936 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AttributeCache_HasAttribute_TisTAttribute_t278E8D943C3C4633EE6BBC5C277CB0456F32B4EF_mCCDF22945C35FBE25C2A2CFA16DA3814209706F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AttributeCache_GetAttribute_TisTAttribute_tD40A15B1D67955664858591F10455A74847F5521_mEF6EE167AF090B40690F766DED906D5FB8037342 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAttribute_tD40A15B1D67955664858591F10455A74847F5521 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TAttribute_t09F62645649E5BEF28C540E351A872736B5B3EF7 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TAttribute_t2235B12A1A0F6FCB5A26D5803750F882C41688AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAttribute_t2235B12A1A0F6FCB5A26D5803750F882C41688AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceInvokerBase_1__ctor_m87FE896EA5BED67F6F290F43099E6DD9BFFBCEB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_tB2F2C827C68FD9E89CAEFB20F8908126A0D875F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceActionInvokerBase_1__ctor_m814CF7475AAF95EEA4F67F6A3DC27D86EB17A0D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvokerBase_1_tD6179661A3581B84FEC64F1B76C161A5B6590261 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t8E75247B0AC6AC703B5486043C0AE77463C32B70 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_1_t34774AC08C69B05C4CFFD7C6E6373B93ECC3ECB7_m99571C6E66A977E4FF1BEBB3DDDE0FA974A058B2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t729DE59AEC880C4E5398933670F3DEAC60F45AC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mA7588CE1724155FA8143BB08CDEE8E57B4BB96E9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t34774AC08C69B05C4CFFD7C6E6373B93ECC3ECB7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvoker_1_t148BE4E218F6D97BB53B0C1AF29D0EF3C2227079 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_1_t34774AC08C69B05C4CFFD7C6E6373B93ECC3ECB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceActionInvokerBase_1__ctor_m22247D35CC3751DEE0792A754F58F20EA52BAA0C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvokerBase_1_tBDA6BD6A824A1647ABFFC01CFDE1CD1689674067 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t4B23F32137512A148161617F2856C10BC432B9C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokerBase_VerifyArgument_TisTParam0_tB0903A5E85633A9FFEDCCD49BE84C3F7C77C6D5E_mAB9599FB49F58D8BAEAD5A0032CFD78605A9B818 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceActionInvoker_2_InvokeUnsafe_m90517B1E02DA0ED86430198B6C2B658FDAFFB3A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvoker_2_tA9C126A526D11F6ABBDC9CF2E0FB6334A34EFD08 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tD9329D8E2F44E13D21EF75CBF4ABA639DEEAE726 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTarget_tF5A587B6C52DBA1F762AB83557E591A8974CE7AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_tB0903A5E85633A9FFEDCCD49BE84C3F7C77C6D5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_mA1AE2383C2D0050A5B1687D0987B990E30F944B4 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tB0903A5E85633A9FFEDCCD49BE84C3F7C77C6D5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_2_tD9329D8E2F44E13D21EF75CBF4ABA639DEEAE726_mC001CD2CCE7292E1FCC5FBEE0719D590425E1739 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tDC435B2E1054C2F2AC5005AD4B19A99B9FD17C07 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mA9E112AD1841360372ACFD423C754D510253F8CA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_2_tD9329D8E2F44E13D21EF75CBF4ABA639DEEAE726 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceActionInvokerBase_1__ctor_m4A9E17072A2EF4EBA0FB57E932681002C377D2AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvokerBase_1_t35BD617675493624083E886F467BE464F64AEC36 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_tEBC4C11F6BC7A5A20AF06AB20C1E3ED3118EBA9B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tB97378C3ECDFF15433C2968A0FFF44F9B6C343D7 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_tE0BD732C338FC97E6C89DBACA511EA3E263C8370 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_3_t7527FDADCA09E08C5B0121CD783745BE8B002510_m347F84D3B345274CE0AAF260451A82A610D41264 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t7D4528A3406F459546E2BDAFE2BDD5DC1CB6153F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mDB9B3B5128614BBBA90EBB20AE68A4098B5C22F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t7527FDADCA09E08C5B0121CD783745BE8B002510 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvoker_3_t692C0022A275B61F2919162B001F4DE319C62229 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_3_t7527FDADCA09E08C5B0121CD783745BE8B002510 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceActionInvokerBase_1__ctor_mA7FB23CA9170BB4F26056C8CFC053CBE3E3F2687 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvokerBase_1_tCADB567CCA155EFE3536FDA7D215DA4C70472B2B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t29A5C332B3F087EA7F58A11255FBBF0BC01EB4B9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t4F34A2F814F53FFB7842B16A5A2ABC848BA9AE02 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t00A749ED21B7BA52DECF9BD1E34EE093B4349097 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_t18FC0E2F2C6BE1B67662B4A7B73B60C09F11AD76 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_4_t73BE0A5552A51C94B0D6D15FDB3BA1A71272D762_mACB197143E9477EF36E1ED879BDADC9445742752 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t67443EF4D56FAE2CB16E9D6693F3F854B1396B3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m9AC5E824AF4D72D97E6778D4DA7247FBC05AC4FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_t73BE0A5552A51C94B0D6D15FDB3BA1A71272D762 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvoker_4_tE4AC8800A552FCF4FDF6E2BAB71BCC5D531DDC03 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_4_t73BE0A5552A51C94B0D6D15FDB3BA1A71272D762 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceActionInvokerBase_1__ctor_mC1E077487D192666FD90E6B585C0BFF88480A90C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvokerBase_1_t99BCAC031309FBC658982F4031EFB836A4E98D03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_tA63FCB646873CF72134AE20DFE3C28EF0058CBAE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t467853C14C61D627D3D868997B41F245EA658B25 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t1B9F94A217736CE10D849DFAF18AF51249EE9DC5 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_tE3E286F5D5D52EA6F493C529ED4A91E90474D262 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_t46D21B50A9E7D6FD8D23EDAC901B98E2DBA25B45 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_5_tAD9BA6FFA0BA81BDBC2A7FCAA6EF81D8DD6D33B3_m2F4B51EF18F4A0B5D4100687D55D714026468378 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t3D14567CC70D40AD13C22F2EB8AA2CA6D425999C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mB10B0C17FEF1015B63D6C9D8B737938DDE1AC275 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_5_tAD9BA6FFA0BA81BDBC2A7FCAA6EF81D8DD6D33B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvoker_5_tE74AF6329E1D020CD39375E497EF281E1A8406DC },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_5_tAD9BA6FFA0BA81BDBC2A7FCAA6EF81D8DD6D33B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceActionInvokerBase_1__ctor_m24D14210B721D29DFCF2EB8F35CB5D0D6D6A06B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvokerBase_1_tD7D7BB56146E5493AF64F92513546FF604B6D5E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_tFBFCFA62B915792A83FD3CED7CC4A7B785F2D36E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tCB368BFFD77B922D05072D6402E2EF83E3C578AA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t758B55381FDA347AF7274E426835846B907E5814 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_tE53C47007FB25E2A01482B7D29F7508E2BAA2D0B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_t85BFF2B5FA8C98FA7833040946BEAE0A4318E267 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam4_tE3EA287C0B2A6966D1AA50BB238A8EB7A749A146 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_6_t5F118FC7371ADB810F4C1BFB5EF70195AAFDC419_mC720D1D7ED0E8DE93ECB9D23F53CCD205E9D744E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t0C89E5DCF2EE712146302A7E2B9176EA4EDB3730 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m92E3F1C5DFB88CF49C021B9134CAA7A5C32A683B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_6_t5F118FC7371ADB810F4C1BFB5EF70195AAFDC419 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceActionInvoker_6_t5708727AAEFB90DD157B30791BF19123AB162975 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_6_t5F118FC7371ADB810F4C1BFB5EF70195AAFDC419 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TTarget_t9BA028F0C4CEFA2A978528B9AB2EAEEC79913009 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TField_t95570A183372691696232E002B96791233EC54EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFieldAccessor_2_t2FC8F9696173244C2CE303AEF12534C972E302A2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_2_tE1336C5B220D3B1E7FB4C9594AD5F84CD6EE2196_mB6B319993379CE34862980C27A71337BFB16E37A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tAAB1A8203CC500A01909F4AC4C86A3E995216830 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mD4BDE7DE66B1CB16D59DCD7E21484FB6583573F8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tE1336C5B220D3B1E7FB4C9594AD5F84CD6EE2196 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_2_t252AD7A97D0D912C97014C03463B7AE64637B6A9_m95B788B3E1241C0FC6443EE97DC3B8A138020F74 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t02A2AABB5C86B65D1AEC0764DF0CD71EE3C897DF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m7420583691497F9D9B6AD3F29E1B9E4610F1B10B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t252AD7A97D0D912C97014C03463B7AE64637B6A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFieldAccessor_2_U3CCompileU3Eb__4_0_m8360559D505484E13501DDDB880FB5907835A32D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m34E9C75CABAD15D1BE41E27F0AB22A85036252DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFieldAccessor_2_U3CCompileU3Eb__4_1_m23A8977F5BAF9483C467F50DB50E3B8C530148E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2__ctor_m452B0518D5E82CD6202B338FF72DA4E3F11112FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_OptimizedReflection_VerifyInstanceTarget_TisTTarget_t9BA028F0C4CEFA2A978528B9AB2EAEEC79913009_mC5C016098CA22F32176A4B7856BF54AAACE845C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFieldAccessor_2_GetValueUnsafe_mA1D26E457FBF97F086F521BBF910B790F44692E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTarget_t9BA028F0C4CEFA2A978528B9AB2EAEEC79913009 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m308CFEA5197C87F8CF02BA7FD9BCDBADBFB8FD1F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TField_t95570A183372691696232E002B96791233EC54EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFieldAccessor_2_SetValueUnsafe_mC2DC2692A5D129D2E9EDA468365A478987307B3F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m92487F7296183BEBFD4973CF055CEF463D7FB4B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceInvokerBase_1__ctor_mBE9A6899016F411A1A1457C8FFA2E42E5CB531FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_tC0DE22622C0E886F3D29D177B465BFC51EE2A5D1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TResult_t374DC02DEC914881936501E15390A74A3C0FBDDA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2__ctor_m970D11D43DB94E544D93C4C1836BA9733AA67DF8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2_tB2363C479A5E72B62E215355B059AF3B7C478043 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t9F494176A9B1825961A2F5B5693D7164983B0693 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_2_tEA48B7C01656ABFC696C99EA4C2FF9E2F3DFA800_m608669266144A490B4521C9F56174857388B5D96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tBFEBCF151B5A833C62623DD4AFFD63A6CEC562C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m33576EF249E6F829048F4F574B53614596522C55 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tEA48B7C01656ABFC696C99EA4C2FF9E2F3DFA800 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvoker_2_t2241D493B1E4C555CFCB7D894DB575338A3965B5 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_2_tEA48B7C01656ABFC696C99EA4C2FF9E2F3DFA800 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2__ctor_mE1C1DC896BA60B6D717120B845A3AE97327F00B1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2_t50B56B6425932E7867263FA1A68A4BF8A4C88495 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t36B72B6E1741474375C4E687F350CEB2DEDF36F9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokerBase_VerifyArgument_TisTParam0_t666542896C005CFC8965A1161EC08E0ED3A341C3_mF22BF370167C3E098A0C896649F2623FAFBEC2F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFunctionInvoker_3_InvokeUnsafe_m54BF8BAF8892CA35A4E04BA08640618DB1A28BA3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvoker_3_tE683F17E1212F4C8F7D59A1F642491D18CCE6C8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t3AFC1B1D97CE58F0A40DDFF7C61E9221FA3464C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTarget_t27DEA11169D17BB6FE4EA3FC40EFD57869598EAA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_t666542896C005CFC8965A1161EC08E0ED3A341C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_3_Invoke_m20B9B1392DFF3FE4B4F258B47FA2ABF430217DC4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_tE9CF0C03DC0DFD6703ACFD11CEE7DC8B3D016C80 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t666542896C005CFC8965A1161EC08E0ED3A341C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_3_t3AFC1B1D97CE58F0A40DDFF7C61E9221FA3464C8_m5A720E3B4C823A1424E030621C41958A2594AC4D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t48078CEB41483958E74884C2901A1C39BE35289A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m8DCADCFA124D5C98986473BDA24200C0C450CA3C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_3_t3AFC1B1D97CE58F0A40DDFF7C61E9221FA3464C8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2__ctor_m076E87C656C9F9947980D4DBE13B628DE1FAD706 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2_tB97FEE94648EE7AF16E14702E60A0BF79B952A12 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t96DA7F30FC35EED2DB1F174191D4146075E87529 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tB84D295236BC36089904690069C305C28B63C61C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_tA9AA2D38BABA20D3A86D4094F721B36909A19C75 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_4_t2FADD491A55A6167CF81C1E901176D9396F21D0D_mC2422706B94B432CB4A9E65B99A96CBCA201CDE2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t065CCFA0167860408FF79EF78C376AED8B83DD97 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m86EAAA748F9AED948E087F756103FE6D6D21FF22 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_t2FADD491A55A6167CF81C1E901176D9396F21D0D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvoker_4_tB6B1A6B21F7F83D4171DAC91D0197861E08E045A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_4_t2FADD491A55A6167CF81C1E901176D9396F21D0D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2__ctor_mC51007809F066C69F5D56498B119021BF819DCE2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2_t3675A8EED3E75A98D575EC755C6098006D35F456 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t61C249656592C5929AFF8E9E7772A7D88C65D677 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t8E8CFFDFD87DC50C835474F8F71BB5E12A77296E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_tF775C5E0610FAF863E8C52A22B23B53974DB31DB },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_t569CA848FA0DA3ECE16BC96B02A02A8F86D4E789 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_5_t85317FA97E2451CFAF004B63EA687727D184D95A_mCCF01B5CD7CAF865D21936DF8DCB5A8F1972711B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t2D701BABD43F6E99CCB3EB3C67DF0BCF6BDB9301 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mC2D9C67542983C0E31A7E8CE5037286254F1F887 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_t85317FA97E2451CFAF004B63EA687727D184D95A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvoker_5_tA982112185746211E77A1111FECB0E0F14A90251 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_5_t85317FA97E2451CFAF004B63EA687727D184D95A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2__ctor_m4A71EFD6E7FCF4B4865E1B14A1F3137198B44719 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2_t905F65DC7EA62AD56793048401EB02203B8727AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_tB656A518DEEFE1BB3A38C9DD40E25998063DA0C6 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tA8A708307468799CE023EA66A08F6A1AFD560C60 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_tE251E65C4D47A8CAB8C1F86848F46091660DFA8A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_t80B79B4A660088F087B14C0F480AD6100FCB2877 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_tF447104F517C524A7FCEC88B736A4C82F605C47C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_6_t2F95C7D598C08DFBF87D5937F29B5DB51781BF03_m13A92409F7317E65E28EFD99A03357012F653D8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tA41244564F921E964C0B88984F8B1335664DD569 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m8A231DBA61C025F34EF748111A8301FCD2943FF8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_6_t2F95C7D598C08DFBF87D5937F29B5DB51781BF03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvoker_6_t81B736C7F3C36D3B33EE0E6412109EF8C8929EA2 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_6_t2F95C7D598C08DFBF87D5937F29B5DB51781BF03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2__ctor_m6C405C8AC55BAB7CBB44D2D677C2676519FE1ACC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvokerBase_2_t2D822AF95E1DC8BEDE40C325E0D0F2096EA7B466 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_t63119156B57161C782109346600D16B5FF1AAB60 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t24922A2A2F4C3DDEC30EF00E17C75EBA3B858125 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t207C9D6267AA7CF3FABDB9FFB2CF06FCE223747C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_tDCAB8AE5D5348F04C0F0F347431762308152BA73 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_tAFB0F8952762361B7155C9D000AFDE95A2C7D61F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam4_t368994B1EE4104468124A1C2431998E727729444 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_7_t7F87270DBDA786B5CBAB293E6BB3CA1B8D5EEA87_mBFC7986A10D9105F3978B7FF1ACCFC8DEFCDABF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t039D9FBC30A6E71FD348555CDA0EEE1ED534FD97 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mF2FD373089F04D8ECCC2E0E677B4FDBD1B8A087B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_7_t7F87270DBDA786B5CBAB293E6BB3CA1B8D5EEA87 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceFunctionInvoker_7_t5B3E4390E7D3C5A174B4C674383B5064A6C06272 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_7_t7F87270DBDA786B5CBAB293E6BB3CA1B8D5EEA87 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TTarget_tA2C83F64E73974094F04E4B802DBD7228089868A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstanceInvokerBase_1_tF272F21D190B52292817193FAC0E1EF3EE10F629 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstanceInvokerBase_1_CompileExpression_mD93B29FF23F0FC2FFA3200AC5C86757741F6C1D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_OptimizedReflection_VerifyInstanceTarget_TisTTarget_tA2C83F64E73974094F04E4B802DBD7228089868A_m6B3734E92013FB640FCF4C2D3F37BF51063C8F16 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TTarget_tCD183E58BE3BAC47547C2FFDAE1F1DDA78BF66F2 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TProperty_t0AC578A4FFDF3C55817BF6BCE830CEB1FDC0929E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InstancePropertyAccessor_2_tB6CFB4A87B9BF73E0DAC00AD731A9E642F131656 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_2_t1B6994F0E41E50FBF3AC444E1657A64049F33ADC_mF04DE42CBA80254A32B286D73301F9E278B7598D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t053F6083C0FB9B90079A9BF5240C3E2E3595F3F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mA79C4059C83D4F2F37074F231D4D44C004AFB92F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t1B6994F0E41E50FBF3AC444E1657A64049F33ADC },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_2_tADB29402D7C47BABC12285653F4A57B28144BDEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tADB29402D7C47BABC12285653F4A57B28144BDEE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_2_t1B6994F0E41E50FBF3AC444E1657A64049F33ADC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_OptimizedReflection_VerifyInstanceTarget_TisTTarget_tCD183E58BE3BAC47547C2FFDAE1F1DDA78BF66F2_mC31B55B8B9E9EB11EAF619D13D5D6CB09DD05015 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstancePropertyAccessor_2_GetValueUnsafe_m5EF8AF1E718FD61630EEC257A979DBEF44B8CC7C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTarget_tCD183E58BE3BAC47547C2FFDAE1F1DDA78BF66F2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mC7BE7F5179A43CB2208449418CFE4B9E0181C2F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProperty_t0AC578A4FFDF3C55817BF6BCE830CEB1FDC0929E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InstancePropertyAccessor_2_SetValueUnsafe_mA284DD836865045862733320F6CB175784B8DA4D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m2926F1EA01D1146C40676A130444A1B826AC0886 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam_t2677EE2955EE1DF1B224D9DF181AEDDD9B75A15A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TTArget_t5E1B3E08A8BB88B27DE0F3CEC01DCD2B827F736B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokerBase_VerifyArgument_TisTParam0_t70883A0781F5F6CB2B198778F44E2822470F29E4_mF82D8BD332425F15499922FE042D8C7C96007B19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticActionInvoker_1_InvokeUnsafe_m10D17E37F677F401F7FDB866E26A584FA46F01DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticActionInvoker_1_t09B9E68028DE17162F06E072105DB72FDBA00EE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tAFF9C802458582D72A297B22E34E95EC770873AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_t70883A0781F5F6CB2B198778F44E2822470F29E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m677C57CEFC932B08AEB28A8F38DDB7D64F63C4E8 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t70883A0781F5F6CB2B198778F44E2822470F29E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_1_tAFF9C802458582D72A297B22E34E95EC770873AC_mE6A78B3E1F299ED537CD194023E333803EAB2348 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tDC88FCAA5348ABFD247A277D54F710CC23A737DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m25325FEBD75B8AC2A4D6313ACDC5A5EC0E2F5C90 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticActionInvoker_1_U3CCreateDelegateU3Eb__7_0_mAE332879453DA0C27E52016AE36FC972002D74AA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mFD2F00F129B99B543D0E671A17AD0ECA82448E08 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_1_tAFF9C802458582D72A297B22E34E95EC770873AC },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t03C009C4389AEE079A6EB554D82EB02D7CD70696 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t0524CD6351FBAB5B1D77E280351DD8B7F3457627 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_2_tD457FA8D0F38E20CB44D8759A3BD0F15C9CBC396_m0E9B26BA21031460AB47CD9EBA98CBCE4CF44CF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t8FCA21FF311FB9D66698A766E55F0635BDA0B6AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m862242AEE3DF2CC33CECFBCF40EFF106C7DEBB56 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tD457FA8D0F38E20CB44D8759A3BD0F15C9CBC396 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticActionInvoker_2_t070181137870A525748586429DFFE14ABD1E7A3D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticActionInvoker_2_U3CCreateDelegateU3Eb__7_0_mF44D5B81140A345F041C96CDF53FD389B71F94E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2__ctor_m7DDA7ABE1BE46977CB110D5CCB8143FE7AD6C02B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_2_tD457FA8D0F38E20CB44D8759A3BD0F15C9CBC396 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_t03C009C4389AEE079A6EB554D82EB02D7CD70696 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t0524CD6351FBAB5B1D77E280351DD8B7F3457627 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m934A3E1933904C90645CF168DE484EFF5B4D11A1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tEFA956FD61400367EB77E85D941FAE066DDF0701 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t9A1ADE41F11DA9CB8DB3346EDE664E5E93DE025B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_tD1C7960BB34152B1C61A5CC652BC1EFB5E34390B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_3_t6265FA95FB81CA4F40A8C868AF9611F9CCD92E1C_m195FE97A8625653C08054C23BCF550AEF9D37513 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tAB4153540368358A1B540F66DC837774FC09CC39 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mA1CEA161C26825CF3D6AB25C46512F86FD679D5E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t6265FA95FB81CA4F40A8C868AF9611F9CCD92E1C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticActionInvoker_3_t28190A158BA38E04A379AD6D4F47AAC1899706BA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticActionInvoker_3_U3CCreateDelegateU3Eb__7_0_m9F1EEEE22EF39E42DA74F91E091BAC866366B41C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3__ctor_mC021598FBAA25FB2E7E87FA080D7078E6ED20F3F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_3_t6265FA95FB81CA4F40A8C868AF9611F9CCD92E1C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_tEFA956FD61400367EB77E85D941FAE066DDF0701 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t9A1ADE41F11DA9CB8DB3346EDE664E5E93DE025B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam2_tD1C7960BB34152B1C61A5CC652BC1EFB5E34390B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3_Invoke_m94D190F90B93AC7699E066B8C6DB5309032A0C89 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tFB06336859B552FF237677A546EAAE5DDC59FF5F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t68F5173634B79D5E473D41D7CC9BAAE797F90C87 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_t976FEF93A5CA79E2863FAC9FA8E66E1E405DF724 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_tD9619AAD0D47762ADA9BF94EAEEDA36F4E17EC4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_4_t191026BE3DF97D5A98906CA8CC71A6C0636C69FF_m9AC7CCE9B645EB71FCE1104AACEB776525ACA77C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tB816D29C570517BCE7152E62FFE523BAA0C1AC82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m4652D925B6D04022785D18FE26366B9C1DBCABC6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_t191026BE3DF97D5A98906CA8CC71A6C0636C69FF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticActionInvoker_4_t05A4FDD2896876D28632DAEBECFB33D3E93DDB1A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticActionInvoker_4_U3CCreateDelegateU3Eb__7_0_m59730E65302229260427D81DAD1A230C58F71FD2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_4__ctor_m1B6ED995DD1842E5BA79118EB42E4DCDD3371C4F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_4_t191026BE3DF97D5A98906CA8CC71A6C0636C69FF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_tFB06336859B552FF237677A546EAAE5DDC59FF5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t68F5173634B79D5E473D41D7CC9BAAE797F90C87 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam2_t976FEF93A5CA79E2863FAC9FA8E66E1E405DF724 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam3_tD9619AAD0D47762ADA9BF94EAEEDA36F4E17EC4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_4_Invoke_mB9A6FC5360FD489994126C57C5CA29C1EFAE818B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t0568BE0E1AB419C578B4216CB910E536E73B5ED9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t898D56D82420FA965FC9337B0CE0A9FEE2A6244A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_tB608103294D25D139E5E7A0F41AAA6CE69A22B36 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_t1A139D7DC6DB929CE3D98A472211D75B45C17995 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam4_t437FF73AD9BB2A26EFFFD67B9BB8EEBAEF5B1B0D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_5_tF92D92F0B7C3BAF6E1D8762A467C0195403CD4C9_m8F43CCE9996449075B755C7E9611A99341FFF79D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t135295915C5D46A6809E2CBC8242985C1101A932 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m1D73DE114FF8C5935CBF98AC8C671003AAC24795 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_5_tF92D92F0B7C3BAF6E1D8762A467C0195403CD4C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticActionInvoker_5_tDD0E844E53381624DC82FA9875016210964CA973 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticActionInvoker_5_U3CCreateDelegateU3Eb__7_0_mC53DF78ECF694BA312D4AEB6559F0D7E646C5791 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_5__ctor_mF60E0B16643E9A87C2FA0F46C982FA6AB7B4BB25 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_5_tF92D92F0B7C3BAF6E1D8762A467C0195403CD4C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_t0568BE0E1AB419C578B4216CB910E536E73B5ED9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t898D56D82420FA965FC9337B0CE0A9FEE2A6244A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam2_tB608103294D25D139E5E7A0F41AAA6CE69A22B36 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam3_t1A139D7DC6DB929CE3D98A472211D75B45C17995 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam4_t437FF73AD9BB2A26EFFFD67B9BB8EEBAEF5B1B0D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_5_Invoke_m74431BBDBA4239D50102FE3C2CF85763C7A3AA4C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TField_tBF2975972161099FC1FC7D544D0ACDA8527C91E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFieldAccessor_1_tCC9D8C714930B415ACD10FD3EF2B6D228381080B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_t189A4796632FD3DA38A702D29066AB6E42599876 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0__ctor_mF12FECA4313E16FDB8F383C4DB59F9B0B87510B2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TField_tBF2975972161099FC1FC7D544D0ACDA8527C91E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_U3CCompileU3Eb__2_mC1C6D62ECF1BABCFA178B2A1CDE949FE7E1CC446 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_tA2C5CACF3E0D74FC6D170A38F11FF02A7E009C32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1__ctor_m99908619EA7CEDA0B505032AB38251404D545FFF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_1_tA2C5CACF3E0D74FC6D170A38F11FF02A7E009C32_m226064F5701F4C087651B8694DE5018EAB9D652C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tBFB23331FE9BD6B59D88FCF49C17AEAEDA3D2095 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mEB27E3BB4E0B48DF4A41F7856F75E263BDC824BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisAction_1_t43410EE9683BC5A93519D9F7BA4E32140F99F08C_m0F4CA8B9AC0AF4A2EBFF1A6536A3AAC9A98A9C9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t5817CCDFB4CA41949ABB6335192D537A177BCF32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m5C9AE33A9D43EDDB9DBDDD5616A566300EDD937A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t43410EE9683BC5A93519D9F7BA4E32140F99F08C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFieldAccessor_1_U3CCompileU3Eb__5_0_mA72AFB166710777B3780217078D11E4CD6A0DB3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFieldAccessor_1_U3CCompileU3Eb__5_1_mA5731F9CC2B746E708ECA788F52C9726808FFE09 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mB1A6B6CC57F709D33EFFA32E1ABF34466926245F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFieldAccessor_1_GetValueUnsafe_mA7AE278A42AE954354754F8D7325E4B5CF65D5A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_m194EFDE4A01FC32D2577B3676FAA0BA038DFF83E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFieldAccessor_1_SetValueUnsafe_m8A0AD4316ED972FF6872697A2E95408E1FFD18E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mDD47E78F1A8522A8A2B70BA5B267887F6EE90FCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_t3845781BC38114BC33D4F1EF6AC25F0999E5B603 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TField_t6BAE5F3DBF42A82312FCE6DF4361DDE55C2B2291 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TResult_t7A7534C8B321C1E0D93733409251CB4B27599809 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvokerBase_1__ctor_mBBC86E7C45B6F5509FC72BAD565FDB27A56713BD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvokerBase_1_t53925464FCCF3B3B4C8FB6DB326A49489017E873 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_1_t459C274F717E668E077E1922AE1526FD2113C438_m46F20923011ABC90B17D514CDAD7B6C69A4B087E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t7A6DD889F9254E3EF16DEE41F46ECB55CA735AC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m5D036AE77D2A330F7DD44F7104526C6BBA11A35E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t459C274F717E668E077E1922AE1526FD2113C438 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvoker_1_t49D9A8B53E4B8977237A849896983C080C83E2D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvoker_1_U3CCreateDelegateU3Eb__7_0_m6B32B099AA6C127AA1485F563100F8B30B4FB593 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1__ctor_mB7506D6B7D60CE1B783FED31B0D0A3E0DAA8891A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_1_t459C274F717E668E077E1922AE1526FD2113C438 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_m29A223601D7BE2B002182008FE07D69E7B66EF8B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t8BBBBAA87C6BB6D3B1709C628DEFC34FB4AA96DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvokerBase_1__ctor_m5B677D0E9D7105A25FA1FD1F74E024943E12BBAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvokerBase_1_tC2FE945FD0BABCA69879394F50E032D73C50BFD8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InvokerBase_VerifyArgument_TisTParam0_t45238165CE2805A1C7261B5F79E9CDE7E3AC84A0_mC9F25E114B5F61A6A06D55C829E649E6F2061572 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvoker_2_InvokeUnsafe_mA5AFA353896E811242FC5B8B2D94DBDC1F69FBF9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvoker_2_t2997263388D7F2C66AAB05F9417021456899E938 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t2A536DE3C9DF8316BE8DA8C2C25F209347813CF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_t45238165CE2805A1C7261B5F79E9CDE7E3AC84A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m8B60781815ECBE3BBBE2C69B116F37010B9E03AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t5790748B85A511329D49845DFD418C3FC2D656AD },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t45238165CE2805A1C7261B5F79E9CDE7E3AC84A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_2_t2A536DE3C9DF8316BE8DA8C2C25F209347813CF1_m8D001FEF4C3CA7E43660F5EB31519A8775C0E9A3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t7D0AC3AA8B6CE107303185107359742FDEACD0B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m1B93EE3C1708327F5B3D43EBF9FF10C570C6A7E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvoker_2_U3CCreateDelegateU3Eb__7_0_m96D3354A2571FE5136FE07447B4017A173EFB806 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m0F4C8384540E7991281F88E85EC4DF54519F797D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_2_t2A536DE3C9DF8316BE8DA8C2C25F209347813CF1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvokerBase_1__ctor_m8CFD1FEA00E34BE512D718FB64E7055772D49418 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvokerBase_1_tF2EFBB465F423484E71627650C022F09681A066C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tCEA3CE3D4BEBE1F88471D325E631F3FFC5149D84 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t116B30461427C070A91828895FB38E2C4FA5D7BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_3_t13C5FF1C32D007AB290C5617CF17A1A90CC2E951_mCCAC8D04849FCBF766C93F6B5C7BD2DD12CD7B9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tEA9D6981D846E1B01E139E3DF59F8222FF867E5D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m1BADB3E4D049D52310909476E570D6FF6CF93AFE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t13C5FF1C32D007AB290C5617CF17A1A90CC2E951 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvoker_3_t60FDA0ED217B84AF589030A1FBDD3EB75FF88FD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvoker_3_U3CCreateDelegateU3Eb__7_0_m35950B90124E675B841A17B6CACB4D3201BFFB3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_3__ctor_mA7FAF17D942F191541959D2383E2B596608EAD90 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_3_t13C5FF1C32D007AB290C5617CF17A1A90CC2E951 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_tCEA3CE3D4BEBE1F88471D325E631F3FFC5149D84 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t116B30461427C070A91828895FB38E2C4FA5D7BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_3_Invoke_mA2E7A118A1A04384D0F3AA63B50045E6B47F8A0C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t166D5E5B72E6F62D9D0EF4865B665464277F5DDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvokerBase_1__ctor_m1C7CA0DA5DD6F118CBA3C26333AA1C39695C9F03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvokerBase_1_t695A85CD59117660D9AECBEE29420E8B28CF267A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tBB0D083AFEA16C69BC0659DE5A305C3A9D66EA40 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t1A661897D4059EF3B4B6FB7828972DFF71144665 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_t05D649BE45E61B19A1EDD18CDE26D19563F2A0E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_4_t6C63D15AB0828AE655769298D98650C845F9D4ED_m004DE9F2EF9BD4812CC0232746ED9C2E1D2DBF5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tDD6CF5C2903E8C0BEE393251FD0C8E235F630D31 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m008BD893724C5AF97A6B13AD467BB41C1014CA15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_t6C63D15AB0828AE655769298D98650C845F9D4ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvoker_4_t221252ECB37F0B11D3FBDE582ECBBE8742CE433A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvoker_4_U3CCreateDelegateU3Eb__7_0_mE89B125D0F0D705AC05729E1305D15247AF1198B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_4__ctor_mC77E38527C23CD5F4571465ED61CAF706B4D5996 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_4_t6C63D15AB0828AE655769298D98650C845F9D4ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_tBB0D083AFEA16C69BC0659DE5A305C3A9D66EA40 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t1A661897D4059EF3B4B6FB7828972DFF71144665 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam2_t05D649BE45E61B19A1EDD18CDE26D19563F2A0E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_4_Invoke_mF99CA6A98C3ABCD975F90FFCD7411D032FC3B416 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_tF56A3C6FC6DE9140668F3D648E39D673C4C03DFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvokerBase_1__ctor_m5789AD168D76FBFE6FFB778596D72675431499F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvokerBase_1_t97C11A31BBAB0EBE4B005A211ACF9E731D661900 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_t9D7F142A17427503734D29F4E05CC308F9DF9D88 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t8962A9306BC9FEC0F1EB221DFFFBED58DA656ACF },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_t0EED94CB47DBDC4E819232D590F1CDBC83971852 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_tC999AF431FC6A861A9A75A4032D5BDCD8B1C87A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_5_tEEA67AD4242DB2D4CF03B8010A961B189E0FE57B_mE7C5736A6E55F101F8D00D0BE1BC61FA4D4AE3EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_t7571B0CC133ACBF962A069CF6D33E1A9188B1B16 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m88DBFB54249C6B2ACB02DD33BC9DF85B32A5E249 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_tEEA67AD4242DB2D4CF03B8010A961B189E0FE57B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvoker_5_tEE03821258950F13A1F1CEEE21ED24512AFAAAFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvoker_5_U3CCreateDelegateU3Eb__7_0_mBB06A64AFE3E3BEB9309DE43E87A51C7EA818FB6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_5__ctor_mF0B389EAE16FB3542DD5ABD20BBE1DA9A174022A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_5_tEEA67AD4242DB2D4CF03B8010A961B189E0FE57B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_t9D7F142A17427503734D29F4E05CC308F9DF9D88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t8962A9306BC9FEC0F1EB221DFFFBED58DA656ACF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam2_t0EED94CB47DBDC4E819232D590F1CDBC83971852 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam3_tC999AF431FC6A861A9A75A4032D5BDCD8B1C87A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_5_Invoke_m50B6593A304F71189C7553D92CE5E2DFFBF0BB6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t688022EDC0380C26312208B41CB4EB69EEECAE3F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvokerBase_1__ctor_mBBE48ED80D0CA8C3F617B516A88A0EDD703EE878 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvokerBase_1_tDB3F0D5999A25209CFBB20B091AD526331AC311D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam0_tBE109D97A87AE383D7FAD3171A1CA2E2B29C907E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam1_t724B0D93D2F88B70F2AB9B96B8C438A0F38BB509 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam2_t81B12B2685ED1C16B2C5BC1976779FA7305EEA45 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam3_tBC7D452F3224EEA79E046C459CAA6E2B218548C5 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TParam4_t82AD0C9CD8D58F5CAF936FD86C74C1B0EFAD5BDF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_6_t748AE0665192BEC3838DE12E012845FA3E5D73BF_m9E36E06DE012C2B7EA9221F476D41A37BBC20D32 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tBDA1518353D9903F760D971675410A07EF0FF412 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_mCE43879CDFF85D1D4FD276C115FAF38B9AF423DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_6_t748AE0665192BEC3838DE12E012845FA3E5D73BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticFunctionInvoker_6_t5A3D2EECE2B99C1CBEF24D047954EE1262098391 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticFunctionInvoker_6_U3CCreateDelegateU3Eb__7_0_mAD7965C4BFC1A396576E72DFAA54964894CAF3F8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_6__ctor_m0D36DD38F358E11767682F8EAB5F62C642A0A02E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_6_t748AE0665192BEC3838DE12E012845FA3E5D73BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam0_tBE109D97A87AE383D7FAD3171A1CA2E2B29C907E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam1_t724B0D93D2F88B70F2AB9B96B8C438A0F38BB509 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam2_t81B12B2685ED1C16B2C5BC1976779FA7305EEA45 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam3_tBC7D452F3224EEA79E046C459CAA6E2B218548C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TParam4_t82AD0C9CD8D58F5CAF936FD86C74C1B0EFAD5BDF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_6_Invoke_mBFEA0983E6C086D5F8588B03EDD403F930C538AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_t4DA15E77AB729D43CA356186D298BB3DD50FEA36 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TProperty_tA65616EE0394D1D8D986F5ED5379C3AAE5558330 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StaticPropertyAccessor_1_tAA87B9629DA64C638A1EF588A6E701FB63D29548 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_Lambda_TisFunc_1_t02FF0498DB701C793476BA279D6EC631120895DA_m121E7C23CEDE1AC099A94773EEB0B8CADB3D5DB5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Expression_1_tBA0351396328D3389ADA6D481ECB3C55FE220516 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Expression_1_Compile_m8638560D25F045BBBD878ED42AD054D6A1085F53 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t02FF0498DB701C793476BA279D6EC631120895DA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Action_1_t87D454F49933A03DADF029DDC1F21884E447E739 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t87D454F49933A03DADF029DDC1F21884E447E739 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Func_1_t02FF0498DB701C793476BA279D6EC631120895DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticPropertyAccessor_1_GetValueUnsafe_m20AF22E43F30A6A2B399E465D02C4672AD7CAA35 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_mC423C9EA9B612312D38AC82794F717AE5605BD60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProperty_tA65616EE0394D1D8D986F5ED5379C3AAE5558330 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StaticPropertyAccessor_1_SetValueUnsafe_m37BA9455A956CCF411D855E51A8E9AA919E831E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mE55020C56C2115E44652A9A9DA10CB4A9AED069E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TAttribute_t0326D6C5451F5F4EC47DF88DA4928D4544427CD2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Cast_TisTAttribute_t0326D6C5451F5F4EC47DF88DA4928D4544427CD2_m5130FB1D506822CDACA65C2A851BE93EAD9AA993 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tD4BB669E703CA2F811B494F5F1AC1453CD9ECDD3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_1_tED45646493AD41EC54F4FAF96E9CC16F3EB1429E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_1_tED45646493AD41EC54F4FAF96E9CC16F3EB1429E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t783349C7F2BC3A3127A6EFAA27CC3D5F66D1C75F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1__ctor_m2E6EAACAE07F7D9AE0372F83D11C0DBA4FD79237 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_Instantiate_m685848D4FA7A2449E8625088C3B768BF3C5F2416 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectsOfType_TisT_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF_m72B28D6AD7FD3FE3D60E48D481630F0B386828C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t19432F5387D337C7B003B5BD28C0E04C30ACD28F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_FindObjectsOfType_mEFC9A4B8456C75C50240D6014C7EB2FC8B7AC55B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_FindInstances_m66893225420E4C17D1963FE32B08458966624AB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_get_automatic_m0F009012D443F00A33C98869C6845DD593A19830 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_get_name_m29CFBC438A226A981B32377880C50FAEEA3F5066 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_get_hideFlags_m9D6C18F1761D6D531017D3C8B811BB92625DE932 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF_mE164806ACE31D0974AFB61B69E2230CCB061840F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_Awake_m7009914966616A12FF3DCD5899BE842774C9CCF4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Singleton_1_get_persistent_m8E260B7077A65F9172CD27ABA8F5F06E7E1840A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EnsureThat_IsNotNull_TisT_t3B32DDEFB6F6CD66A0CB2DD960F882D1309085AF_m0165570A314DAF2068B70D63C1928C167C2EF932 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Contains_m0535DB9F10DF4D450A5BCF232B3C38984E12A247 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Add_m82FBEB1A0E23BA67F1C92FA44C2B1D59C69B603E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF3B273F3F094C9E634FBF2D135AB01C65C8D95C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Empty_1_tAD140F158571BD0C7DED03139F7545F12BF54E0A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF3B273F3F094C9E634FBF2D135AB01C65C8D95C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Empty_1_tAD140F158571BD0C7DED03139F7545F12BF54E0A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tEC047EB534499FC8CF217001DC0264023DEFCFB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mFAE2EF3B560A9A9B0895B3812A2DEDA9C518BCA6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t86A553DF03365B3B91C63CD7A45B03070BC05717 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1__ctor_m7C00A423590FAA9F52E318E7479A6F1AD10AB3DE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC0F2CC5AA620443E9DF787C656FA9A3EC6046D0D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tC0F2CC5AA620443E9DF787C656FA9A3EC6046D0D_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t12FD4C81F8668B7023F9E9EC1E7845E2D15E6F7C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T1_t12FD4C81F8668B7023F9E9EC1E7845E2D15E6F7C_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t43CDE2394B80FB56F5A398CBAE0597EFCAD9BAEC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_t43CDE2394B80FB56F5A398CBAE0597EFCAD9BAEC_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CConcatU3Ed__0_1_t13515DCE2E387DB77A8F87B535E8C9D40203ED5D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CConcatU3Ed__0_1__ctor_mF28051DC7E21CCD3336D77B117A4C71F50CFEA23 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t1CA2E9C943941178DCD887D86ED97A8B5E1FB802 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t1C71122DC5846575B23595569AD61EEB34CC4A45 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_t04325BC0737AC3234D4C0826979B56979163DA6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tF96F336AF1EE764BED5BD39B7A37ED25879719F3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_t04325BC0737AC3234D4C0826979B56979163DA6B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__2_1_U3CNotNullU3Eb__2_0_mC3BC2CADA58184778C3A890992216AAE84C24DBA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_mB1C5A806B682C35E30A1432FC61808885ABF398F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Where_TisT_tA168C99AE6C83FAF42DD7D1AAF71FCD557B57BEB_m545FE7D30A9130C957840159FED7924F5DD6FA8E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_tF637B46223DF17878F707465E7ABE469EFC7A4DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__2_1__ctor_m9B79CAB6D3FFE4C3A835604D9DD4C91FF0AAA322 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_tF637B46223DF17878F707465E7ABE469EFC7A4DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7E8436779A6D74C1E84828869FFC96948DEAFCED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CConcatU3Ed__0_1_t90E48C012CB874F2E571E4683972BF2ADA1A0FCE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CConcatU3Ed__0_1_U3CU3Em__Finally2_m0CF75D901D4F9E05EF91089B9A9251B591702B4D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CConcatU3Ed__0_1_U3CU3Em__Finally1_m084B05E2E19862394815C2F5C3CD06E789465AA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_OfType_TisT_tD4EC27FE359CA26C71342AFD1B3E20628BCFB63D_m53F131BBD066EE67BB23C786AF2464EF4763123F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t7E2301D170FD43561F934398F5C5B93DA8BACCBC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_mD88E0D1522F05D09ED1820EAAFF8E0AA77A67B6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tFAE092251D764997AFF22D13A71974C3CE4DD5E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m1CAEE709F6DFF620EC07B9843CB88BA05A98539C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD4EC27FE359CA26C71342AFD1B3E20628BCFB63D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CConcatU3Ed__0_1_System_IDisposable_Dispose_m7DC901A01D2775B045FFF20D0DC051C4FF3C684E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CConcatU3Ed__0_1__ctor_m04879677A566C2EA42BB3D0424CB2C7551824D4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CConcatU3Ed__0_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mFD7B63A4ACEA04B14D9C83CDD68266747946714F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA434DAAA533BF9E7D8D102F6B31F557EAC68F27E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReferenceEqualityComparer_1_GetHashCode_mE8D6E49E2683897C8D95B6D3AFF7AADB235F6CCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReferenceEqualityComparer_1_t4EA150DA5DAC8063D06564B23B5425239EC0D851 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReferenceEqualityComparer_1_t4EA150DA5DAC8063D06564B23B5425239EC0D851 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReferenceEqualityComparer_1__ctor_mB927257D8C9F96C81754D0C80A94DD97D0A23798 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_mDB5CADDED5947D3289BC6A965130309EBD7738BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t4F75BB3292C15C9AD8CAED648B1072D41EC46A40 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t4F75BB3292C15C9AD8CAED648B1072D41EC46A40 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tBCB4AFD09117205E00F693B9B61ADD044B58E03B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t059EDBAAA8BB194D521A4740722ABDEDE6811131 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_Equals_m666A63604BEA714B24FD4E7AEB456ED33F6A6E98 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3A237E4AA0892E30C7702FD24E2E0300B01B1262 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3A237E4AA0892E30C7702FD24E2E0300B01B1262 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tC180A47FBC84684922ACBB32FFE9D7EA918B4F3A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t66A6B07095DD463B03FC01500296FB7356DCAEB1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t66A6B07095DD463B03FC01500296FB7356DCAEB1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t5A1B1DFB3DBD00270ED3B0AD25CD7659CF44974B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5A1B1DFB3DBD00270ED3B0AD25CD7659CF44974B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t4FF9F0AFC05B7E0F85A4B934B59D234155F6E4A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4FF9F0AFC05B7E0F85A4B934B59D234155F6E4A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBE9A67E171C85D8CA29A1569A871BCDB274C8EBF },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tBE9A67E171C85D8CA29A1569A871BCDB274C8EBF },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TModel_tA846BAB14AD72C3F97B08FAFEF7B2629C8054C13 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TModel_tA846BAB14AD72C3F97B08FAFEF7B2629C8054C13 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_fsDirectConverter_1_t0EF3B59944746AB537D9449A99F11833F2D78EDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_fsDirectConverter_1_DoSerialize_m4A44064DDCD50F053CDFC38CF4D3F62CA8874B5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_fsDirectConverter_1_DoDeserialize_m1A512349B1E46B9C069209815C166DB902EC8822 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TModelU26_t30E33E940C62C34794AE1FC252140E33D3EB618F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tEB7E9E00FDC44AE50752B1BF0E0F507EF4AFFBAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEB7E9E00FDC44AE50752B1BF0E0F507EF4AFFBAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t3BE3F4B70DC24BF1596038EE672847471DD041AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t594BBE454D274A88EA1B5E96B1F70F1AFAB8E1AC },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t594BBE454D274A88EA1B5E96B1F70F1AFAB8E1AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_fsOption_1_tBA834DAEC7376CB376DA946F0E5F5F6D1E240334 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_fsOption_1_get_IsEmpty_m6198B54E39FF8D4513440ACC323FB7815AEEC173 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_fsOption_1_tBA834DAEC7376CB376DA946F0E5F5F6D1E240334 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC5D84CBABB6B1934F21007E0A382E362C744EBA6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF41E1145E66EAE3CF7E6499BD09CBA3A0EF365E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_fsOption_1_t94E57BA0CA9FA35974F429ECB93CB3C18B13D8DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_fsOption_1__ctor_mE8C8AFB3E17F0261C53D9F67224575FAFE756426 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TAttribute_t9A4A9352D6A7BA2E2152F2E06FA05DAFBAAF9EC9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAttribute_t9A4A9352D6A7BA2E2152F2E06FA05DAFBAAF9EC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_fsPortableReflection_GetAttribute_TisTAttribute_tC89EDB76B0770E376AE4684BD18227EEC2AAA9D2_m548F9E6A81089AEFB3BDE22D59614D419F3E437C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAttribute_tC89EDB76B0770E376AE4684BD18227EEC2AAA9D2 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnity_VisualScripting_Core;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_VisualScripting_Core_CodeGenModule;
const Il2CppCodeGenModule g_Unity_VisualScripting_Core_CodeGenModule = 
{
	"Unity.VisualScripting.Core.dll",
	1084,
	s_methodPointers,
	34,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	66,
	s_rgctxIndices,
	561,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnity_VisualScripting_Core,
	NULL,
	NULL,
	NULL,
	NULL,
};
