﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA;
struct EmbeddedAttribute_tDF54F33B67821BEBD880B9BDB7E888D62301B8F8;
struct NullableAttribute_t14C2881F37BD05FDE882ED6965646FB46CC77743;
struct NullableContextAttribute_t15AF1365D7FC7D6635930E49E38DD3C16A59DE22;
struct NullablePublicOnlyAttribute_t64603269B89EF6C72BC502B5A36D80F608EE0902;
struct RefSafetyRulesAttribute_tBF5290AC8E40AE41B28A15D732B84D1B9B16B9FF;
struct String_t;
struct TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249;
struct SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1;

IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* EmbeddedAttribute__ctor_m625632FDB45145A48F560E2C4307820303DA73C0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NullableAttribute__ctor_m955CC5FAC91FACD765BF375CFE8F6D3B0F615947_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NullableContextAttribute__ctor_m61B641AFA37F7B28FA042DC9B5BF7BF3A78B70A5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NullablePublicOnlyAttribute__ctor_mA96C21221F7BA4C045AB20D502F5C69DF5528271_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RefSafetyRulesAttribute__ctor_m8B1D7DF66C25C25B28564B934A0751B209253C98_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SystemTimeProvider__ctor_mD8275E7C145CBAC35CD35ACD233EDBD82752CCD8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TimeProvider__cctor_m0615C74640034A1BC815C1B4F1C64A653202E96B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TimeProvider__ctor_m6202A3551B7CE4D2BCF4B2E094D8A275DB6BA40C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TimeProvider_get_System_m94CF1104364EEBCB021329E0B10460066417496D_RuntimeMethod_var;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t934C5782932F11EA02A14F534364378905D64E1B 
{
};
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA  : public RuntimeObject
{
};
struct TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D 
{
	uint64_t ____dateData;
};
struct EmbeddedAttribute_tDF54F33B67821BEBD880B9BDB7E888D62301B8F8  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct NullableAttribute_t14C2881F37BD05FDE882ED6965646FB46CC77743  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___NullableFlags;
};
struct NullableContextAttribute_t15AF1365D7FC7D6635930E49E38DD3C16A59DE22  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	uint8_t ___Flag;
};
struct NullablePublicOnlyAttribute_t64603269B89EF6C72BC502B5A36D80F608EE0902  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	bool ___IncludesInternals;
};
struct RefSafetyRulesAttribute_tBF5290AC8E40AE41B28A15D732B84D1B9B16B9FF  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	int32_t ___Version;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1  : public TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249
{
};
struct TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_StaticFields
{
	TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249* ___U3CSystemU3Ek__BackingField;
	int64_t ___s_minDateTicks;
	int64_t ___s_maxDateTicks;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___s_daysToMonth365;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___s_daysToMonth366;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___MinValue;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___MaxValue;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___UnixEpoch;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2 (Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SystemTimeProvider__ctor_mD8275E7C145CBAC35CD35ACD233EDBD82752CCD8 (SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t DateTime_get_Ticks_mC2CF04ED0EAB425C72C2532FFC5743777F3C93A6 (DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TimeProvider__ctor_m6202A3551B7CE4D2BCF4B2E094D8A275DB6BA40C (TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EmbeddedAttribute__ctor_m625632FDB45145A48F560E2C4307820303DA73C0 (EmbeddedAttribute_tDF54F33B67821BEBD880B9BDB7E888D62301B8F8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EmbeddedAttribute__ctor_m625632FDB45145A48F560E2C4307820303DA73C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, EmbeddedAttribute__ctor_m625632FDB45145A48F560E2C4307820303DA73C0_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NullableAttribute__ctor_m955CC5FAC91FACD765BF375CFE8F6D3B0F615947 (NullableAttribute_t14C2881F37BD05FDE882ED6965646FB46CC77743* __this, uint8_t ___0_p, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NullableAttribute__ctor_m955CC5FAC91FACD765BF375CFE8F6D3B0F615947_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, NullableAttribute__ctor_m955CC5FAC91FACD765BF375CFE8F6D3B0F615947_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		uint8_t L_2 = ___0_p;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (uint8_t)L_2);
		__this->___NullableFlags = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___NullableFlags), (void*)L_1);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NullableContextAttribute__ctor_m61B641AFA37F7B28FA042DC9B5BF7BF3A78B70A5 (NullableContextAttribute_t15AF1365D7FC7D6635930E49E38DD3C16A59DE22* __this, uint8_t ___0_p, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NullableContextAttribute__ctor_m61B641AFA37F7B28FA042DC9B5BF7BF3A78B70A5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, NullableContextAttribute__ctor_m61B641AFA37F7B28FA042DC9B5BF7BF3A78B70A5_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		uint8_t L_0 = ___0_p;
		__this->___Flag = L_0;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NullablePublicOnlyAttribute__ctor_mA96C21221F7BA4C045AB20D502F5C69DF5528271 (NullablePublicOnlyAttribute_t64603269B89EF6C72BC502B5A36D80F608EE0902* __this, bool ___0_p, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NullablePublicOnlyAttribute__ctor_mA96C21221F7BA4C045AB20D502F5C69DF5528271_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, NullablePublicOnlyAttribute__ctor_mA96C21221F7BA4C045AB20D502F5C69DF5528271_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		bool L_0 = ___0_p;
		__this->___IncludesInternals = L_0;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RefSafetyRulesAttribute__ctor_m8B1D7DF66C25C25B28564B934A0751B209253C98 (RefSafetyRulesAttribute_tBF5290AC8E40AE41B28A15D732B84D1B9B16B9FF* __this, int32_t ___0_p, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RefSafetyRulesAttribute__ctor_m8B1D7DF66C25C25B28564B934A0751B209253C98_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RefSafetyRulesAttribute__ctor_m8B1D7DF66C25C25B28564B934A0751B209253C98_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		int32_t L_0 = ___0_p;
		__this->___Version = L_0;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249* TimeProvider_get_System_m94CF1104364EEBCB021329E0B10460066417496D (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeProvider_get_System_m94CF1104364EEBCB021329E0B10460066417496D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, TimeProvider_get_System_m94CF1104364EEBCB021329E0B10460066417496D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		il2cpp_codegen_runtime_class_init_inline(TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var);
		TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249* L_0 = ((TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_StaticFields*)il2cpp_codegen_static_fields_for(TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var))->___U3CSystemU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TimeProvider__ctor_m6202A3551B7CE4D2BCF4B2E094D8A275DB6BA40C (TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeProvider__ctor_m6202A3551B7CE4D2BCF4B2E094D8A275DB6BA40C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, TimeProvider__ctor_m6202A3551B7CE4D2BCF4B2E094D8A275DB6BA40C_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TimeProvider__cctor_m0615C74640034A1BC815C1B4F1C64A653202E96B (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeProvider__cctor_m0615C74640034A1BC815C1B4F1C64A653202E96B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, TimeProvider__cctor_m0615C74640034A1BC815C1B4F1C64A653202E96B_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1* L_0 = (SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1*)il2cpp_codegen_object_new(SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1_il2cpp_TypeInfo_var);
		SystemTimeProvider__ctor_mD8275E7C145CBAC35CD35ACD233EDBD82752CCD8(L_0, NULL);
		((TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_StaticFields*)il2cpp_codegen_static_fields_for(TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var))->___U3CSystemU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_StaticFields*)il2cpp_codegen_static_fields_for(TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var))->___U3CSystemU3Ek__BackingField), (void*)L_0);
		il2cpp_codegen_runtime_class_init_inline(DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var);
		DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D L_1 = ((DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_StaticFields*)il2cpp_codegen_static_fields_for(DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var))->___MinValue;
		V_0 = L_1;
		int64_t L_2;
		L_2 = DateTime_get_Ticks_mC2CF04ED0EAB425C72C2532FFC5743777F3C93A6((&V_0), NULL);
		((TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_StaticFields*)il2cpp_codegen_static_fields_for(TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var))->___s_minDateTicks = L_2;
		DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D L_3 = ((DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_StaticFields*)il2cpp_codegen_static_fields_for(DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var))->___MaxValue;
		V_0 = L_3;
		int64_t L_4;
		L_4 = DateTime_get_Ticks_mC2CF04ED0EAB425C72C2532FFC5743777F3C93A6((&V_0), NULL);
		((TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_StaticFields*)il2cpp_codegen_static_fields_for(TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var))->___s_maxDateTicks = L_4;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SystemTimeProvider__ctor_mD8275E7C145CBAC35CD35ACD233EDBD82752CCD8 (SystemTimeProvider_tBF880AC85E4431F3D0F5D959AC8845D5DBBBECC1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SystemTimeProvider__ctor_mD8275E7C145CBAC35CD35ACD233EDBD82752CCD8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SystemTimeProvider__ctor_mD8275E7C145CBAC35CD35ACD233EDBD82752CCD8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		il2cpp_codegen_runtime_class_init_inline(TimeProvider_t37846A32FB1F52B8572CDF43ECE9852159346249_il2cpp_TypeInfo_var);
		TimeProvider__ctor_m6202A3551B7CE4D2BCF4B2E094D8A275DB6BA40C(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
