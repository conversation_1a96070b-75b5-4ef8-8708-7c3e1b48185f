﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF;
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99;
struct SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C const RuntimeMethod* SpriteMaskUtility_HasSpriteMaskInLayerRange_m7E522D077F4992310FECE3D2911B0C1EE1F72F6B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteMask_GetSpriteBounds_mBE1ED797A95FF5F952CFB718ACFC279151530B52_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SpriteMask__ctor_mE8AD60D949B00F8FF8DEFCB43563FEFEF86C612D_RuntimeMethod_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tADBDFCFBC18780C9B802150A15718A4868C20FAA 
{
};
struct SpriteMaskUtility_t222687932E86DF4FEEDC5998F722ACEB61FD41DD  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct SortingLayerRange_t96D04CFB4E8824978FEB2CFFFCFEAC37E56D52C9 
{
	int16_t ___m_LowerBound;
	int16_t ___m_UpperBound;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Center;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Extents;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct SpriteSortPoint_t675776A019AA63D08BABB83C87DC85D1508A19A7 
{
	int32_t ___value__;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3  : public Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Renderer__ctor_m8B4EE9696B155A1B0A2CF13EBFC363CE175B9271 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD (SortingLayerRange_t96D04CFB4E8824978FEB2CFFFCFEAC37E56D52C9* ___0_range, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteMask_get_frontSortingLayerID_mCDFA70861C17DE5BC7FC178DF5FFD1E7BEE12B3E (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*SpriteMask_get_frontSortingLayerID_mCDFA70861C17DE5BC7FC178DF5FFD1E7BEE12B3E_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_frontSortingLayerID_mCDFA70861C17DE5BC7FC178DF5FFD1E7BEE12B3E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_frontSortingLayerID_mCDFA70861C17DE5BC7FC178DF5FFD1E7BEE12B3E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_frontSortingLayerID()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_frontSortingLayerID_m023C6504B9363E89DC2528FA83D82208192E8198 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_frontSortingLayerID_m023C6504B9363E89DC2528FA83D82208192E8198_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, int32_t);
	static SpriteMask_set_frontSortingLayerID_m023C6504B9363E89DC2528FA83D82208192E8198_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_frontSortingLayerID_m023C6504B9363E89DC2528FA83D82208192E8198_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_frontSortingLayerID(System.Int32)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteMask_get_frontSortingOrder_m9F4B657A7E0C3BDC5344C8FD3EAC4B3A04D43979 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*SpriteMask_get_frontSortingOrder_m9F4B657A7E0C3BDC5344C8FD3EAC4B3A04D43979_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_frontSortingOrder_m9F4B657A7E0C3BDC5344C8FD3EAC4B3A04D43979_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_frontSortingOrder_m9F4B657A7E0C3BDC5344C8FD3EAC4B3A04D43979_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_frontSortingOrder()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_frontSortingOrder_mBB6C461477D2D11F95CC0CB7398BC8A3AD3E98BC (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_frontSortingOrder_mBB6C461477D2D11F95CC0CB7398BC8A3AD3E98BC_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, int32_t);
	static SpriteMask_set_frontSortingOrder_mBB6C461477D2D11F95CC0CB7398BC8A3AD3E98BC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_frontSortingOrder_mBB6C461477D2D11F95CC0CB7398BC8A3AD3E98BC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_frontSortingOrder(System.Int32)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteMask_get_backSortingLayerID_m0A888DD1A63A9AE95D8EDE5C92F106C3669F963D (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*SpriteMask_get_backSortingLayerID_m0A888DD1A63A9AE95D8EDE5C92F106C3669F963D_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_backSortingLayerID_m0A888DD1A63A9AE95D8EDE5C92F106C3669F963D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_backSortingLayerID_m0A888DD1A63A9AE95D8EDE5C92F106C3669F963D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_backSortingLayerID()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_backSortingLayerID_m9DB8BA104EF7F184A3AA55B6DCDDCBF40E103D7B (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_backSortingLayerID_m9DB8BA104EF7F184A3AA55B6DCDDCBF40E103D7B_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, int32_t);
	static SpriteMask_set_backSortingLayerID_m9DB8BA104EF7F184A3AA55B6DCDDCBF40E103D7B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_backSortingLayerID_m9DB8BA104EF7F184A3AA55B6DCDDCBF40E103D7B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_backSortingLayerID(System.Int32)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteMask_get_backSortingOrder_m47CF66F8F8C4098B438106A423D45BD805EF11DB (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*SpriteMask_get_backSortingOrder_m47CF66F8F8C4098B438106A423D45BD805EF11DB_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_backSortingOrder_m47CF66F8F8C4098B438106A423D45BD805EF11DB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_backSortingOrder_m47CF66F8F8C4098B438106A423D45BD805EF11DB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_backSortingOrder()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_backSortingOrder_m383FEC8838B334E961536C2477B80437CF401839 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_backSortingOrder_m383FEC8838B334E961536C2477B80437CF401839_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, int32_t);
	static SpriteMask_set_backSortingOrder_m383FEC8838B334E961536C2477B80437CF401839_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_backSortingOrder_m383FEC8838B334E961536C2477B80437CF401839_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_backSortingOrder(System.Int32)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float SpriteMask_get_alphaCutoff_m4994EFE29AEEA861928DDD627823DE1F349C8CF9 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef float (*SpriteMask_get_alphaCutoff_m4994EFE29AEEA861928DDD627823DE1F349C8CF9_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_alphaCutoff_m4994EFE29AEEA861928DDD627823DE1F349C8CF9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_alphaCutoff_m4994EFE29AEEA861928DDD627823DE1F349C8CF9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_alphaCutoff()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_alphaCutoff_m7D7BBA4C83CA779FFCFEDDC92773E3639197DBF2 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_alphaCutoff_m7D7BBA4C83CA779FFCFEDDC92773E3639197DBF2_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, float);
	static SpriteMask_set_alphaCutoff_m7D7BBA4C83CA779FFCFEDDC92773E3639197DBF2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_alphaCutoff_m7D7BBA4C83CA779FFCFEDDC92773E3639197DBF2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_alphaCutoff(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* SpriteMask_get_sprite_m56DE2BB449F7A3E0F01373E390B64755ACA121A7 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* (*SpriteMask_get_sprite_m56DE2BB449F7A3E0F01373E390B64755ACA121A7_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_sprite_m56DE2BB449F7A3E0F01373E390B64755ACA121A7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_sprite_m56DE2BB449F7A3E0F01373E390B64755ACA121A7_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_sprite()");
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_sprite_mF086ACFDAB47F00285BE7D2D17EF5E2F9128EB84 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_sprite_mF086ACFDAB47F00285BE7D2D17EF5E2F9128EB84_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99*);
	static SpriteMask_set_sprite_mF086ACFDAB47F00285BE7D2D17EF5E2F9128EB84_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_sprite_mF086ACFDAB47F00285BE7D2D17EF5E2F9128EB84_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_sprite(UnityEngine.Sprite)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SpriteMask_get_isCustomRangeActive_mA0DFDC2307128FAEC2C1B0B9B6B5D382A40CD115 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef bool (*SpriteMask_get_isCustomRangeActive_mA0DFDC2307128FAEC2C1B0B9B6B5D382A40CD115_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_isCustomRangeActive_mA0DFDC2307128FAEC2C1B0B9B6B5D382A40CD115_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_isCustomRangeActive_mA0DFDC2307128FAEC2C1B0B9B6B5D382A40CD115_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_isCustomRangeActive()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_isCustomRangeActive_mDE57CE93CF78D45E83BDF3F4DF6330D5EE778F3D (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_isCustomRangeActive_mDE57CE93CF78D45E83BDF3F4DF6330D5EE778F3D_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, bool);
	static SpriteMask_set_isCustomRangeActive_mDE57CE93CF78D45E83BDF3F4DF6330D5EE778F3D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_isCustomRangeActive_mDE57CE93CF78D45E83BDF3F4DF6330D5EE778F3D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_isCustomRangeActive(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SpriteMask_get_spriteSortPoint_m84E047D2BE64E2A4DF3C193DDCF6466743FEA7C3 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*SpriteMask_get_spriteSortPoint_m84E047D2BE64E2A4DF3C193DDCF6466743FEA7C3_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*);
	static SpriteMask_get_spriteSortPoint_m84E047D2BE64E2A4DF3C193DDCF6466743FEA7C3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_get_spriteSortPoint_m84E047D2BE64E2A4DF3C193DDCF6466743FEA7C3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::get_spriteSortPoint()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_set_spriteSortPoint_m2CF60E48A89F305EC0DB1E87C05676801F81CB1F (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_set_spriteSortPoint_m2CF60E48A89F305EC0DB1E87C05676801F81CB1F_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, int32_t);
	static SpriteMask_set_spriteSortPoint_m2CF60E48A89F305EC0DB1E87C05676801F81CB1F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_set_spriteSortPoint_m2CF60E48A89F305EC0DB1E87C05676801F81CB1F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::set_spriteSortPoint(UnityEngine.SpriteSortPoint)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 SpriteMask_GetSpriteBounds_mBE1ED797A95FF5F952CFB718ACFC279151530B52 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteMask_GetSpriteBounds_mBE1ED797A95FF5F952CFB718ACFC279151530B52_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteMask_GetSpriteBounds_mBE1ED797A95FF5F952CFB718ACFC279151530B52_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01(__this, (&V_0), NULL);
		Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask__ctor_mE8AD60D949B00F8FF8DEFCB43563FEFEF86C612D (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteMask__ctor_mE8AD60D949B00F8FF8DEFCB43563FEFEF86C612D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteMask__ctor_mE8AD60D949B00F8FF8DEFCB43563FEFEF86C612D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Renderer__ctor_m8B4EE9696B155A1B0A2CF13EBFC363CE175B9271(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01 (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3* __this, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01_ftn) (SpriteMask_tB723EAFFAEACE86D32C80726CE63B19FE6C6F2B3*, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3*);
	static SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMask::GetSpriteBounds_Injected(UnityEngine.Bounds&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SpriteMaskUtility_HasSpriteMaskInLayerRange_m7E522D077F4992310FECE3D2911B0C1EE1F72F6B (SortingLayerRange_t96D04CFB4E8824978FEB2CFFFCFEAC37E56D52C9 ___0_range, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpriteMaskUtility_HasSpriteMaskInLayerRange_m7E522D077F4992310FECE3D2911B0C1EE1F72F6B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SpriteMaskUtility_HasSpriteMaskInLayerRange_m7E522D077F4992310FECE3D2911B0C1EE1F72F6B_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		bool L_0;
		L_0 = SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD((&___0_range), NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD (SortingLayerRange_t96D04CFB4E8824978FEB2CFFFCFEAC37E56D52C9* ___0_range, const RuntimeMethod* method) 
{
	typedef bool (*SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD_ftn) (SortingLayerRange_t96D04CFB4E8824978FEB2CFFFCFEAC37E56D52C9*);
	static SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.SpriteMaskUtility::HasSpriteMaskInLayerRange_Injected(UnityEngine.Rendering.SortingLayerRange&)");
	bool icallRetVal = _il2cpp_icall_func(___0_range);
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
