﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[5] = 
{
	{ 29228, 0,  4 },
	{ 11784, 1,  4 },
	{ 29228, 0,  5 },
	{ 15830, 2,  5 },
	{ 11944, 3,  5 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[4] = 
{
	"info",
	"array",
	"buffer",
	"slice",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[41] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 2 },
	{ 2, 3 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_SpriteShapeModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_SpriteShapeModule[169] = 
{
	{ 109594, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109594, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109594, 1, 51, 51, 17, 18, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109594, 1, 51, 51, 19, 38, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109594, 1, 51, 51, 39, 40, 10, kSequencePointKind_Normal, 0, 4 },
	{ 109595, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 5 },
	{ 109595, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 6 },
	{ 109595, 1, 52, 52, 17, 18, 0, kSequencePointKind_Normal, 0, 7 },
	{ 109595, 1, 52, 52, 19, 39, 1, kSequencePointKind_Normal, 0, 8 },
	{ 109595, 1, 52, 52, 40, 41, 8, kSequencePointKind_Normal, 0, 9 },
	{ 109596, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 10 },
	{ 109596, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 11 },
	{ 109596, 1, 56, 56, 17, 18, 0, kSequencePointKind_Normal, 0, 12 },
	{ 109596, 1, 56, 56, 19, 39, 1, kSequencePointKind_Normal, 0, 13 },
	{ 109596, 1, 56, 56, 40, 41, 10, kSequencePointKind_Normal, 0, 14 },
	{ 109597, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 15 },
	{ 109597, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 16 },
	{ 109597, 1, 57, 57, 17, 18, 0, kSequencePointKind_Normal, 0, 17 },
	{ 109597, 1, 57, 57, 19, 40, 1, kSequencePointKind_Normal, 0, 18 },
	{ 109597, 1, 57, 57, 41, 42, 8, kSequencePointKind_Normal, 0, 19 },
	{ 109598, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 20 },
	{ 109598, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 21 },
	{ 109598, 1, 61, 61, 17, 18, 0, kSequencePointKind_Normal, 0, 22 },
	{ 109598, 1, 61, 61, 19, 40, 1, kSequencePointKind_Normal, 0, 23 },
	{ 109598, 1, 61, 61, 41, 42, 10, kSequencePointKind_Normal, 0, 24 },
	{ 109599, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 25 },
	{ 109599, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 26 },
	{ 109599, 1, 62, 62, 17, 18, 0, kSequencePointKind_Normal, 0, 27 },
	{ 109599, 1, 62, 62, 19, 41, 1, kSequencePointKind_Normal, 0, 28 },
	{ 109599, 1, 62, 62, 42, 43, 8, kSequencePointKind_Normal, 0, 29 },
	{ 109600, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 30 },
	{ 109600, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 31 },
	{ 109600, 1, 66, 66, 17, 18, 0, kSequencePointKind_Normal, 0, 32 },
	{ 109600, 1, 66, 66, 19, 40, 1, kSequencePointKind_Normal, 0, 33 },
	{ 109600, 1, 66, 66, 41, 42, 10, kSequencePointKind_Normal, 0, 34 },
	{ 109601, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 35 },
	{ 109601, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 36 },
	{ 109601, 1, 67, 67, 17, 18, 0, kSequencePointKind_Normal, 0, 37 },
	{ 109601, 1, 67, 67, 19, 41, 1, kSequencePointKind_Normal, 0, 38 },
	{ 109601, 1, 67, 67, 42, 43, 8, kSequencePointKind_Normal, 0, 39 },
	{ 109607, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 40 },
	{ 109607, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 41 },
	{ 109607, 1, 116, 116, 9, 10, 0, kSequencePointKind_Normal, 0, 42 },
	{ 109607, 1, 121, 121, 13, 46, 1, kSequencePointKind_Normal, 0, 43 },
	{ 109607, 1, 121, 121, 13, 46, 3, kSequencePointKind_StepOut, 0, 44 },
	{ 109607, 1, 122, 122, 13, 130, 9, kSequencePointKind_Normal, 0, 45 },
	{ 109607, 1, 122, 122, 13, 130, 11, kSequencePointKind_StepOut, 0, 46 },
	{ 109607, 1, 122, 122, 13, 130, 18, kSequencePointKind_StepOut, 0, 47 },
	{ 109607, 1, 122, 122, 13, 130, 24, kSequencePointKind_StepOut, 0, 48 },
	{ 109607, 1, 127, 127, 13, 26, 30, kSequencePointKind_Normal, 0, 49 },
	{ 109607, 1, 128, 128, 9, 10, 34, kSequencePointKind_Normal, 0, 50 },
	{ 109608, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 51 },
	{ 109608, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 52 },
	{ 109608, 1, 131, 131, 9, 10, 0, kSequencePointKind_Normal, 0, 53 },
	{ 109608, 1, 136, 136, 13, 48, 1, kSequencePointKind_Normal, 0, 54 },
	{ 109608, 1, 136, 136, 13, 48, 3, kSequencePointKind_StepOut, 0, 55 },
	{ 109608, 1, 137, 137, 13, 61, 9, kSequencePointKind_Normal, 0, 56 },
	{ 109608, 1, 137, 137, 13, 61, 11, kSequencePointKind_StepOut, 0, 57 },
	{ 109608, 1, 137, 137, 13, 61, 18, kSequencePointKind_StepOut, 0, 58 },
	{ 109608, 1, 138, 138, 13, 119, 25, kSequencePointKind_Normal, 0, 59 },
	{ 109608, 1, 138, 138, 13, 119, 28, kSequencePointKind_StepOut, 0, 60 },
	{ 109608, 1, 138, 138, 13, 119, 35, kSequencePointKind_StepOut, 0, 61 },
	{ 109608, 1, 138, 138, 13, 119, 40, kSequencePointKind_StepOut, 0, 62 },
	{ 109608, 1, 143, 143, 13, 26, 46, kSequencePointKind_Normal, 0, 63 },
	{ 109608, 1, 144, 144, 9, 10, 50, kSequencePointKind_Normal, 0, 64 },
	{ 109615, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 65 },
	{ 109615, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 66 },
	{ 109615, 1, 158, 158, 9, 10, 0, kSequencePointKind_Normal, 0, 67 },
	{ 109615, 1, 159, 159, 13, 80, 1, kSequencePointKind_Normal, 0, 68 },
	{ 109615, 1, 159, 159, 13, 80, 3, kSequencePointKind_StepOut, 0, 69 },
	{ 109615, 1, 160, 160, 9, 10, 11, kSequencePointKind_Normal, 0, 70 },
	{ 109616, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 71 },
	{ 109616, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 72 },
	{ 109616, 1, 168, 168, 9, 10, 0, kSequencePointKind_Normal, 0, 73 },
	{ 109616, 1, 169, 169, 13, 39, 1, kSequencePointKind_Normal, 0, 74 },
	{ 109616, 1, 169, 169, 13, 39, 3, kSequencePointKind_StepOut, 0, 75 },
	{ 109616, 1, 170, 170, 13, 88, 9, kSequencePointKind_Normal, 0, 76 },
	{ 109616, 1, 170, 170, 13, 88, 11, kSequencePointKind_StepOut, 0, 77 },
	{ 109616, 1, 171, 171, 9, 10, 19, kSequencePointKind_Normal, 0, 78 },
	{ 109617, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 79 },
	{ 109617, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 80 },
	{ 109617, 1, 181, 181, 9, 10, 0, kSequencePointKind_Normal, 0, 81 },
	{ 109617, 1, 182, 182, 13, 50, 1, kSequencePointKind_Normal, 0, 82 },
	{ 109617, 1, 182, 182, 13, 50, 4, kSequencePointKind_StepOut, 0, 83 },
	{ 109617, 1, 183, 183, 13, 77, 10, kSequencePointKind_Normal, 0, 84 },
	{ 109617, 1, 183, 183, 13, 77, 13, kSequencePointKind_StepOut, 0, 85 },
	{ 109617, 1, 184, 184, 13, 114, 23, kSequencePointKind_Normal, 0, 86 },
	{ 109617, 1, 184, 184, 13, 114, 27, kSequencePointKind_StepOut, 0, 87 },
	{ 109617, 1, 185, 185, 13, 119, 37, kSequencePointKind_Normal, 0, 88 },
	{ 109617, 1, 185, 185, 13, 119, 42, kSequencePointKind_StepOut, 0, 89 },
	{ 109617, 1, 186, 186, 9, 10, 52, kSequencePointKind_Normal, 0, 90 },
	{ 109618, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 91 },
	{ 109618, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 92 },
	{ 109618, 1, 197, 197, 9, 10, 0, kSequencePointKind_Normal, 0, 93 },
	{ 109618, 1, 198, 198, 13, 92, 1, kSequencePointKind_Normal, 0, 94 },
	{ 109618, 1, 198, 198, 13, 92, 5, kSequencePointKind_StepOut, 0, 95 },
	{ 109618, 1, 199, 199, 13, 77, 11, kSequencePointKind_Normal, 0, 96 },
	{ 109618, 1, 199, 199, 13, 77, 14, kSequencePointKind_StepOut, 0, 97 },
	{ 109618, 1, 200, 200, 13, 114, 24, kSequencePointKind_Normal, 0, 98 },
	{ 109618, 1, 200, 200, 13, 114, 28, kSequencePointKind_StepOut, 0, 99 },
	{ 109618, 1, 201, 201, 13, 119, 38, kSequencePointKind_Normal, 0, 100 },
	{ 109618, 1, 201, 201, 13, 119, 43, kSequencePointKind_StepOut, 0, 101 },
	{ 109618, 1, 202, 202, 13, 108, 53, kSequencePointKind_Normal, 0, 102 },
	{ 109618, 1, 202, 202, 13, 108, 58, kSequencePointKind_StepOut, 0, 103 },
	{ 109618, 1, 203, 203, 9, 10, 68, kSequencePointKind_Normal, 0, 104 },
	{ 109619, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 105 },
	{ 109619, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 106 },
	{ 109619, 1, 214, 214, 9, 10, 0, kSequencePointKind_Normal, 0, 107 },
	{ 109619, 1, 215, 215, 13, 94, 1, kSequencePointKind_Normal, 0, 108 },
	{ 109619, 1, 215, 215, 13, 94, 5, kSequencePointKind_StepOut, 0, 109 },
	{ 109619, 1, 216, 216, 13, 77, 11, kSequencePointKind_Normal, 0, 110 },
	{ 109619, 1, 216, 216, 13, 77, 14, kSequencePointKind_StepOut, 0, 111 },
	{ 109619, 1, 217, 217, 13, 114, 24, kSequencePointKind_Normal, 0, 112 },
	{ 109619, 1, 217, 217, 13, 114, 28, kSequencePointKind_StepOut, 0, 113 },
	{ 109619, 1, 218, 218, 13, 119, 38, kSequencePointKind_Normal, 0, 114 },
	{ 109619, 1, 218, 218, 13, 119, 43, kSequencePointKind_StepOut, 0, 115 },
	{ 109619, 1, 219, 219, 13, 114, 53, kSequencePointKind_Normal, 0, 116 },
	{ 109619, 1, 219, 219, 13, 114, 58, kSequencePointKind_StepOut, 0, 117 },
	{ 109619, 1, 220, 220, 9, 10, 68, kSequencePointKind_Normal, 0, 118 },
	{ 109620, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 119 },
	{ 109620, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 120 },
	{ 109620, 1, 231, 231, 9, 10, 0, kSequencePointKind_Normal, 0, 121 },
	{ 109620, 1, 232, 232, 13, 132, 1, kSequencePointKind_Normal, 0, 122 },
	{ 109620, 1, 232, 232, 13, 132, 6, kSequencePointKind_StepOut, 0, 123 },
	{ 109620, 1, 233, 233, 13, 77, 12, kSequencePointKind_Normal, 0, 124 },
	{ 109620, 1, 233, 233, 13, 77, 15, kSequencePointKind_StepOut, 0, 125 },
	{ 109620, 1, 234, 234, 13, 114, 25, kSequencePointKind_Normal, 0, 126 },
	{ 109620, 1, 234, 234, 13, 114, 29, kSequencePointKind_StepOut, 0, 127 },
	{ 109620, 1, 235, 235, 13, 119, 39, kSequencePointKind_Normal, 0, 128 },
	{ 109620, 1, 235, 235, 13, 119, 44, kSequencePointKind_StepOut, 0, 129 },
	{ 109620, 1, 236, 236, 13, 108, 54, kSequencePointKind_Normal, 0, 130 },
	{ 109620, 1, 236, 236, 13, 108, 59, kSequencePointKind_StepOut, 0, 131 },
	{ 109620, 1, 237, 237, 13, 114, 69, kSequencePointKind_Normal, 0, 132 },
	{ 109620, 1, 237, 237, 13, 114, 74, kSequencePointKind_StepOut, 0, 133 },
	{ 109620, 1, 238, 238, 9, 10, 84, kSequencePointKind_Normal, 0, 134 },
	{ 109621, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 135 },
	{ 109621, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 136 },
	{ 109621, 1, 250, 250, 9, 10, 0, kSequencePointKind_Normal, 0, 137 },
	{ 109621, 1, 251, 251, 13, 133, 1, kSequencePointKind_Normal, 0, 138 },
	{ 109621, 1, 251, 251, 13, 133, 5, kSequencePointKind_StepOut, 0, 139 },
	{ 109621, 1, 252, 252, 13, 77, 11, kSequencePointKind_Normal, 0, 140 },
	{ 109621, 1, 252, 252, 13, 77, 14, kSequencePointKind_StepOut, 0, 141 },
	{ 109621, 1, 253, 253, 13, 114, 24, kSequencePointKind_Normal, 0, 142 },
	{ 109621, 1, 253, 253, 13, 114, 28, kSequencePointKind_StepOut, 0, 143 },
	{ 109621, 1, 254, 254, 13, 119, 38, kSequencePointKind_Normal, 0, 144 },
	{ 109621, 1, 254, 254, 13, 119, 43, kSequencePointKind_StepOut, 0, 145 },
	{ 109621, 1, 255, 255, 13, 114, 53, kSequencePointKind_Normal, 0, 146 },
	{ 109621, 1, 255, 255, 13, 114, 58, kSequencePointKind_StepOut, 0, 147 },
	{ 109621, 1, 256, 256, 13, 111, 68, kSequencePointKind_Normal, 0, 148 },
	{ 109621, 1, 256, 256, 13, 111, 73, kSequencePointKind_StepOut, 0, 149 },
	{ 109621, 1, 257, 257, 9, 10, 83, kSequencePointKind_Normal, 0, 150 },
	{ 109622, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 151 },
	{ 109622, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 152 },
	{ 109622, 1, 268, 268, 9, 10, 0, kSequencePointKind_Normal, 0, 153 },
	{ 109622, 1, 269, 269, 13, 169, 1, kSequencePointKind_Normal, 0, 154 },
	{ 109622, 1, 269, 269, 13, 169, 6, kSequencePointKind_StepOut, 0, 155 },
	{ 109622, 1, 270, 270, 13, 77, 12, kSequencePointKind_Normal, 0, 156 },
	{ 109622, 1, 270, 270, 13, 77, 15, kSequencePointKind_StepOut, 0, 157 },
	{ 109622, 1, 271, 271, 13, 114, 25, kSequencePointKind_Normal, 0, 158 },
	{ 109622, 1, 271, 271, 13, 114, 29, kSequencePointKind_StepOut, 0, 159 },
	{ 109622, 1, 272, 272, 13, 119, 39, kSequencePointKind_Normal, 0, 160 },
	{ 109622, 1, 272, 272, 13, 119, 44, kSequencePointKind_StepOut, 0, 161 },
	{ 109622, 1, 273, 273, 13, 108, 54, kSequencePointKind_Normal, 0, 162 },
	{ 109622, 1, 273, 273, 13, 108, 59, kSequencePointKind_StepOut, 0, 163 },
	{ 109622, 1, 274, 274, 13, 114, 69, kSequencePointKind_Normal, 0, 164 },
	{ 109622, 1, 274, 274, 13, 114, 74, kSequencePointKind_StepOut, 0, 165 },
	{ 109622, 1, 275, 275, 13, 111, 84, kSequencePointKind_Normal, 0, 166 },
	{ 109622, 1, 275, 275, 13, 111, 89, kSequencePointKind_StepOut, 0, 167 },
	{ 109622, 1, 276, 276, 9, 10, 99, kSequencePointKind_Normal, 0, 168 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_SpriteShapeModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_SpriteShapeModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/SpriteShape/Public/ScriptBindings/SpriteShapeRenderer.bindings.cs", { 220, 152, 250, 185, 196, 231, 204, 62, 35, 169, 172, 98, 141, 251, 210, 186} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14089, 1 },
	{ 14091, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[8] = 
{
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 36 },
	{ 0, 52 },
	{ 0, 13 },
	{ 0, 21 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[41] = 
{
	{ 12, 0, 1 },
	{ 0, 0, 0 },
	{ 12, 1, 1 },
	{ 0, 0, 0 },
	{ 12, 2, 1 },
	{ 0, 0, 0 },
	{ 12, 3, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 36, 4, 1 },
	{ 52, 5, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 13, 6, 1 },
	{ 21, 7, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SpriteShapeModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SpriteShapeModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	169,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_SpriteShapeModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
