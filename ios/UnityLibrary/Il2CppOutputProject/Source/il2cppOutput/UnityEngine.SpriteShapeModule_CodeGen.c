﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F (void);
extern void SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25 (void);
extern void SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A (void);
extern void SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1 (void);
extern void SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381 (void);
extern void SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3 (void);
extern void SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5 (void);
extern void SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13 (void);
extern void SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F (void);
extern void SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51 (void);
extern void SpriteShapeRenderer_get_maskInteraction_mDC3CCF2A83DBA14E86841D07208DD388BBD4B652 (void);
extern void SpriteShapeRenderer_set_maskInteraction_mEB99D9FDCAE6E313335F1DDB1719ABE99CD2F67A (void);
extern void SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950 (void);
extern void SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5 (void);
extern void SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6 (void);
extern void SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF (void);
extern void SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D (void);
extern void SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA (void);
extern void SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080 (void);
extern void SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A (void);
extern void SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20 (void);
extern void SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755 (void);
extern void SpriteShapeRenderer_GetChannels_m481C5B4F534E8037FFA0B856BCD371DAB112C3CF (void);
extern void SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F (void);
extern void SpriteShapeRenderer_GetChannels_mB103260A403E60CB28AFFE799447DE075A114FC6 (void);
extern void SpriteShapeRenderer_GetChannels_mB9F0096C5B4DCCB7BA9A9DAFA085B5400E0B33E2 (void);
extern void SpriteShapeRenderer_GetChannels_m2F1DEBB233238E415AFF4B82BA7D1B3C4A9FC85C (void);
extern void SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6 (void);
extern void SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3 (void);
extern void SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF (void);
extern void SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D (void);
extern void SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C (void);
extern void SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D (void);
extern void SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105 (void);
extern void SpriteShapeUtility_Generate_m37169E418B14B90AE2B0D7E2CA35C7B666B55016 (void);
extern void SpriteShapeUtility_GenerateSpriteShape_mB19EF6B7F3B223E8E10CD85EFA808D4A7D6330AF (void);
extern void SpriteShapeUtility__ctor_m15FD3D11661152CFCE8FEE92BEBA837B55238426 (void);
extern void SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4 (void);
extern void SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB (void);
static Il2CppMethodPointer s_methodPointers[41] = 
{
	SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F,
	SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25,
	SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A,
	SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1,
	SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381,
	SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3,
	SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5,
	SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13,
	SpriteShapeRenderer_get_color_mB5F692D74527187556CAE5F02D93EA792B03D45F,
	SpriteShapeRenderer_set_color_mD5C6A8ADF2BA925EBA287105C5450251004E0E51,
	SpriteShapeRenderer_get_maskInteraction_mDC3CCF2A83DBA14E86841D07208DD388BBD4B652,
	SpriteShapeRenderer_set_maskInteraction_mEB99D9FDCAE6E313335F1DDB1719ABE99CD2F67A,
	SpriteShapeRenderer_Prepare_m3EB1D60213EC54306CEC059519D22E2754072950,
	NULL,
	NULL,
	SpriteShapeRenderer_SetSegmentCount_m649276042B95E37EE26246F371675D7123480EC5,
	SpriteShapeRenderer_SetMeshDataCount_m7F8F41487DABF81F7DF3B4334452EE0C8A7095F6,
	SpriteShapeRenderer_SetMeshChannelInfo_m08D74539463B0B415A4F0A9D9863D4E79A76E7AF,
	SpriteShapeRenderer_GetDataInfo_m8BEE8EE1332F043EA4FF0CFDF2F0C2882BDF800D,
	SpriteShapeRenderer_GetChannelInfo_m6B622671C214A2920BAC1DF0190DEC62ABA7BADA,
	SpriteShapeRenderer_SetLocalAABB_m50672DFB7F3EF0AB13FF725A86EA3DA718C8F080,
	SpriteShapeRenderer_GetBounds_mB1109C67BE9B7A2376B92299C07B89E25026E42A,
	SpriteShapeRenderer_GetSegments_m20EAF8C9AA9B74C31053A0F69B60B15D7967AA20,
	SpriteShapeRenderer_GetChannels_mBFF908DA30D2D2A9650F917211D83F6A5795D755,
	SpriteShapeRenderer_GetChannels_m481C5B4F534E8037FFA0B856BCD371DAB112C3CF,
	SpriteShapeRenderer_GetChannels_m08BD580C754823A2578D887BEEF70AA43019DB8F,
	SpriteShapeRenderer_GetChannels_mB103260A403E60CB28AFFE799447DE075A114FC6,
	SpriteShapeRenderer_GetChannels_mB9F0096C5B4DCCB7BA9A9DAFA085B5400E0B33E2,
	SpriteShapeRenderer_GetChannels_m2F1DEBB233238E415AFF4B82BA7D1B3C4A9FC85C,
	SpriteShapeRenderer__ctor_m44BF2C8BE32D32910B2E162D0830BFC36694D6A6,
	SpriteShapeRenderer_get_color_Injected_m5DF8CA684904BB1D9EB6A1D8C51050C33AE837E3,
	SpriteShapeRenderer_set_color_Injected_m3738681F39E29CDC45E204CAFE8B417D8B7969CF,
	SpriteShapeRenderer_Prepare_Injected_mD1195648DC363033B06C211A6F5A00F30C8B0F7D,
	SpriteShapeRenderer_GetDataInfo_Injected_m9F93C146CC8377F6C657038271C48CBD8E2E784C,
	SpriteShapeRenderer_GetChannelInfo_Injected_mABC1866E231BC32890EC4CE7768652F2DFA4913D,
	SpriteShapeRenderer_SetLocalAABB_Injected_m1539FADCD075EFA3FA8BFE925FC503192847F105,
	SpriteShapeUtility_Generate_m37169E418B14B90AE2B0D7E2CA35C7B666B55016,
	SpriteShapeUtility_GenerateSpriteShape_mB19EF6B7F3B223E8E10CD85EFA808D4A7D6330AF,
	SpriteShapeUtility__ctor_m15FD3D11661152CFCE8FEE92BEBA837B55238426,
	SpriteShapeUtility_Generate_Injected_m7EDCAE2EEE61FA62B4DC05DE3B42C2E7FA7CFBA4,
	SpriteShapeUtility_GenerateSpriteShape_Injected_m29F14BC6698B8BEF672D4B734B9BBE08E481F5AB,
};
extern void SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F_AdjustorThunk (void);
extern void SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_AdjustorThunk (void);
extern void SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_AdjustorThunk (void);
extern void SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_AdjustorThunk (void);
extern void SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_AdjustorThunk (void);
extern void SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_AdjustorThunk (void);
extern void SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5_AdjustorThunk (void);
extern void SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[8] = 
{
	{ 0x06000001, SpriteShapeSegment_get_geomIndex_m9131A7903BDF1F2363F939588979D497E2E1CE5F_AdjustorThunk },
	{ 0x06000002, SpriteShapeSegment_set_geomIndex_m14DE47F211B8A0689AE1CBD62C3A1EEFF0605E25_AdjustorThunk },
	{ 0x06000003, SpriteShapeSegment_get_indexCount_mB1823401E991934E00A50147D40297C300AF456A_AdjustorThunk },
	{ 0x06000004, SpriteShapeSegment_set_indexCount_m28732D6B993D21A6327A1A0CEC2AA9EDCCA2C4A1_AdjustorThunk },
	{ 0x06000005, SpriteShapeSegment_get_vertexCount_m7FAC6E2254D9AC12C0293E26EEC5BE64832F7381_AdjustorThunk },
	{ 0x06000006, SpriteShapeSegment_set_vertexCount_mFF8D13BF27EC6010581D458BB131F118EF52A0F3_AdjustorThunk },
	{ 0x06000007, SpriteShapeSegment_get_spriteIndex_m1B53BD50F1EFDFBBB6C1EAC98F7B3A36545ED8E5_AdjustorThunk },
	{ 0x06000008, SpriteShapeSegment_set_spriteIndex_mDAFA4E1F6BB47EE7540C6CD46CC5376652DADC13_AdjustorThunk },
};
static const int32_t s_InvokerIndices[41] = 
{
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4172,
	3811,
	4216,
	3852,
	2016,
	0,
	0,
	3852,
	2734,
	1973,
	3595,
	3595,
	3805,
	4056,
	2948,
	1330,
	856,
	856,
	534,
	534,
	325,
	4364,
	3788,
	3788,
	1907,
	2728,
	2728,
	3788,
	4800,
	4864,
	4364,
	4781,
	4843,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x0600000E, { 0, 2 } },
	{ 0x0600000F, { 2, 2 } },
};
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t5BC1F8928A1B35AC768A2C4B0239C04CEDA1FD56_mE6BDF655A67788463021A6D09555A3B92E856D7B;
extern const uint32_t g_rgctx_NativeArray_1_t669037BF55DFB05FFC88EED828421A071607779E;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_ConvertExistingDataToNativeSlice_TisT_t987F30B32AF875C4AE5FF41A2CF0541B43BA72C8_mFBF5CBDC8F822FB2B34B6722DC374C229061FC2E;
extern const uint32_t g_rgctx_NativeSlice_1_t548FE7BFD18D3AE82BF650C973E1A93318CA56B8;
static const Il2CppRGCTXDefinition s_rgctxValues[4] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t5BC1F8928A1B35AC768A2C4B0239C04CEDA1FD56_mE6BDF655A67788463021A6D09555A3B92E856D7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t669037BF55DFB05FFC88EED828421A071607779E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_ConvertExistingDataToNativeSlice_TisT_t987F30B32AF875C4AE5FF41A2CF0541B43BA72C8_mFBF5CBDC8F822FB2B34B6722DC374C229061FC2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t548FE7BFD18D3AE82BF650C973E1A93318CA56B8 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SpriteShapeModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SpriteShapeModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SpriteShapeModule_CodeGenModule = 
{
	"UnityEngine.SpriteShapeModule.dll",
	41,
	s_methodPointers,
	8,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	4,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_SpriteShapeModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
