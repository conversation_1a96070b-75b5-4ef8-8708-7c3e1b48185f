﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnityConnectSettings_get_enabled_m867E6927DE3787A5259CCD98C70D5CDC6B36ADA5 (void);
extern void UnityConnectSettings_set_enabled_mB696F73FD98AB871072E3D8134EB4D9B549E76A7 (void);
extern void UnityConnectSettings_get_testMode_mDFAFDB7CF6591A3C1175264BA690DB215EDA5D46 (void);
extern void UnityConnectSettings_set_testMode_m69C03E750DF3B2F03910C78F3285E40FD545B475 (void);
extern void UnityConnectSettings_get_eventUrl_m4F530A4F56AEB2421110E8E5490A2224D3D9AC62 (void);
extern void UnityConnectSettings_set_eventUrl_mA85C06C998DEE553E85E1553C460315CB6C0392B (void);
extern void UnityConnectSettings_get_eventOldUrl_mC6F81A1E6394386CD5C2537E5431AE7FC803333B (void);
extern void UnityConnectSettings_set_eventOldUrl_m12CD35CA5AF9D3FC92D4917FA8E521CAF70AEA02 (void);
extern void UnityConnectSettings_get_configUrl_m13D4E12D95DB2135570B8D774EDBB57572640C70 (void);
extern void UnityConnectSettings_set_configUrl_mAF280198CC19DC82B1A0078B455D24031918AABC (void);
extern void UnityConnectSettings_get_testInitMode_m127F04D899DDDEF0AE5A2B986B2414849A3DC3C3 (void);
extern void UnityConnectSettings_set_testInitMode_m8B90D81B0ABAC31965FB939721320F082A81A59D (void);
extern void UnityConnectSettings__ctor_m52B180993E2FBFBA90A679B119590FDA83E0D2AB (void);
extern void UnityAdsSettings_get_enabled_m7FC5CA5CAC32D89E997E56F85709E95D693FFDD6 (void);
extern void UnityAdsSettings_set_enabled_mED8F9E3987137A06FAC0DED1074976FA27A20279 (void);
extern void UnityAdsSettings_IsPlatformEnabled_m5544575971444B4D60A66E75872DB99D04F6CA2F (void);
extern void UnityAdsSettings_SetPlatformEnabled_mE15C6EBB1CA537EE9477CA5576C342320D560551 (void);
extern void UnityAdsSettings_get_initializeOnStartup_m7C45929DD9C44F2C41978FF2F1F83DB24BB85698 (void);
extern void UnityAdsSettings_set_initializeOnStartup_m5EF6EACF0342129CB46CB4A9A19E545ADB4E09EE (void);
extern void UnityAdsSettings_get_testMode_mF098639F51F4AD94C01CA0DFA0CF70B81769BB55 (void);
extern void UnityAdsSettings_set_testMode_m7786647A721E0A19C2ADA5794F62FB4871C8BD8E (void);
extern void UnityAdsSettings_GetGameId_m86D0B75C01498D76593CEC10A3D89A90DD3B6CCF (void);
extern void UnityAdsSettings_SetGameId_m1D9A3409CDF70F991D28B087B0D6EED04144DB3D (void);
static Il2CppMethodPointer s_methodPointers[23] = 
{
	UnityConnectSettings_get_enabled_m867E6927DE3787A5259CCD98C70D5CDC6B36ADA5,
	UnityConnectSettings_set_enabled_mB696F73FD98AB871072E3D8134EB4D9B549E76A7,
	UnityConnectSettings_get_testMode_mDFAFDB7CF6591A3C1175264BA690DB215EDA5D46,
	UnityConnectSettings_set_testMode_m69C03E750DF3B2F03910C78F3285E40FD545B475,
	UnityConnectSettings_get_eventUrl_m4F530A4F56AEB2421110E8E5490A2224D3D9AC62,
	UnityConnectSettings_set_eventUrl_mA85C06C998DEE553E85E1553C460315CB6C0392B,
	UnityConnectSettings_get_eventOldUrl_mC6F81A1E6394386CD5C2537E5431AE7FC803333B,
	UnityConnectSettings_set_eventOldUrl_m12CD35CA5AF9D3FC92D4917FA8E521CAF70AEA02,
	UnityConnectSettings_get_configUrl_m13D4E12D95DB2135570B8D774EDBB57572640C70,
	UnityConnectSettings_set_configUrl_mAF280198CC19DC82B1A0078B455D24031918AABC,
	UnityConnectSettings_get_testInitMode_m127F04D899DDDEF0AE5A2B986B2414849A3DC3C3,
	UnityConnectSettings_set_testInitMode_m8B90D81B0ABAC31965FB939721320F082A81A59D,
	UnityConnectSettings__ctor_m52B180993E2FBFBA90A679B119590FDA83E0D2AB,
	UnityAdsSettings_get_enabled_m7FC5CA5CAC32D89E997E56F85709E95D693FFDD6,
	UnityAdsSettings_set_enabled_mED8F9E3987137A06FAC0DED1074976FA27A20279,
	UnityAdsSettings_IsPlatformEnabled_m5544575971444B4D60A66E75872DB99D04F6CA2F,
	UnityAdsSettings_SetPlatformEnabled_mE15C6EBB1CA537EE9477CA5576C342320D560551,
	UnityAdsSettings_get_initializeOnStartup_m7C45929DD9C44F2C41978FF2F1F83DB24BB85698,
	UnityAdsSettings_set_initializeOnStartup_m5EF6EACF0342129CB46CB4A9A19E545ADB4E09EE,
	UnityAdsSettings_get_testMode_mF098639F51F4AD94C01CA0DFA0CF70B81769BB55,
	UnityAdsSettings_set_testMode_m7786647A721E0A19C2ADA5794F62FB4871C8BD8E,
	UnityAdsSettings_GetGameId_m86D0B75C01498D76593CEC10A3D89A90DD3B6CCF,
	UnityAdsSettings_SetGameId_m1D9A3409CDF70F991D28B087B0D6EED04144DB3D,
};
static const int32_t s_InvokerIndices[23] = 
{
	8993,
	8868,
	8993,
	8868,
	9031,
	8887,
	9031,
	8887,
	9031,
	8887,
	9018,
	8880,
	4364,
	8993,
	8868,
	8216,
	7926,
	8993,
	8868,
	8993,
	8868,
	8501,
	7932,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityConnectModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityConnectModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityConnectModule_CodeGenModule = 
{
	"UnityEngine.UnityConnectModule.dll",
	23,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_UnityConnectModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
