﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CrashReportHandler_tDC3582120286C333EE85347A7CE6C793B1B2B019;
struct String_t;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_CrashReportingModule[];
IL2CPP_EXTERN_C const RuntimeMethod* CrashReportHandler__ctor_m66D6B1BA3D0454273476C0B7320BB50EB5345BF0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* CrashReportHandler_tDC3582120286C333EE85347A7CE6C793B1B2B019_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t62B8AA732FD56BBF041B9F0091F091311788A0F6 
{
};
struct CrashReportHandler_tDC3582120286C333EE85347A7CE6C793B1B2B019  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CrashReportHandler__ctor_m66D6B1BA3D0454273476C0B7320BB50EB5345BF0 (CrashReportHandler_tDC3582120286C333EE85347A7CE6C793B1B2B019* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CrashReportHandler__ctor_m66D6B1BA3D0454273476C0B7320BB50EB5345BF0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CrashReportHandler_tDC3582120286C333EE85347A7CE6C793B1B2B019_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, CrashReportHandler__ctor_m66D6B1BA3D0454273476C0B7320BB50EB5345BF0_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CrashReportingModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CrashReportingModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CrashReportingModule + 2));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CrashReportingModule + 3));
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CrashReportingModule + 3));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CrashReportingModule + 4));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CrashReportingModule + 5));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CrashReportHandler_get_enableCaptureExceptions_mCDE26F4C646D163DCB61E4A4EA2137F2C3BBF461 (const RuntimeMethod* method) 
{
	typedef bool (*CrashReportHandler_get_enableCaptureExceptions_mCDE26F4C646D163DCB61E4A4EA2137F2C3BBF461_ftn) ();
	static CrashReportHandler_get_enableCaptureExceptions_mCDE26F4C646D163DCB61E4A4EA2137F2C3BBF461_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_get_enableCaptureExceptions_mCDE26F4C646D163DCB61E4A4EA2137F2C3BBF461_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::get_enableCaptureExceptions()");
	bool icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CrashReportHandler_set_enableCaptureExceptions_m43B6DDC35A11833111069353501CDA7EE2D75351 (bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*CrashReportHandler_set_enableCaptureExceptions_m43B6DDC35A11833111069353501CDA7EE2D75351_ftn) (bool);
	static CrashReportHandler_set_enableCaptureExceptions_m43B6DDC35A11833111069353501CDA7EE2D75351_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_set_enableCaptureExceptions_m43B6DDC35A11833111069353501CDA7EE2D75351_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::set_enableCaptureExceptions(System.Boolean)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t CrashReportHandler_get_logBufferSize_m6D3487D46A3B7745F128AA25D19EE829DDDB70F2 (const RuntimeMethod* method) 
{
	typedef uint32_t (*CrashReportHandler_get_logBufferSize_m6D3487D46A3B7745F128AA25D19EE829DDDB70F2_ftn) ();
	static CrashReportHandler_get_logBufferSize_m6D3487D46A3B7745F128AA25D19EE829DDDB70F2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_get_logBufferSize_m6D3487D46A3B7745F128AA25D19EE829DDDB70F2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::get_logBufferSize()");
	uint32_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CrashReportHandler_set_logBufferSize_mD085CF929DFFDB39F8D9A8CC25E027462FB907B1 (uint32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*CrashReportHandler_set_logBufferSize_mD085CF929DFFDB39F8D9A8CC25E027462FB907B1_ftn) (uint32_t);
	static CrashReportHandler_set_logBufferSize_mD085CF929DFFDB39F8D9A8CC25E027462FB907B1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_set_logBufferSize_mD085CF929DFFDB39F8D9A8CC25E027462FB907B1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::set_logBufferSize(System.UInt32)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* CrashReportHandler_get_installationIdentifier_m83A71077FDCF0D83D7BDF61EC976378C6457BD43 (const RuntimeMethod* method) 
{
	typedef String_t* (*CrashReportHandler_get_installationIdentifier_m83A71077FDCF0D83D7BDF61EC976378C6457BD43_ftn) ();
	static CrashReportHandler_get_installationIdentifier_m83A71077FDCF0D83D7BDF61EC976378C6457BD43_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_get_installationIdentifier_m83A71077FDCF0D83D7BDF61EC976378C6457BD43_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::get_installationIdentifier()");
	String_t* icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CrashReportHandler_set_installationIdentifier_m226426172B933FAAAC3FA11183FCCCA1460D2C04 (String_t* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*CrashReportHandler_set_installationIdentifier_m226426172B933FAAAC3FA11183FCCCA1460D2C04_ftn) (String_t*);
	static CrashReportHandler_set_installationIdentifier_m226426172B933FAAAC3FA11183FCCCA1460D2C04_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_set_installationIdentifier_m226426172B933FAAAC3FA11183FCCCA1460D2C04_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::set_installationIdentifier(System.String)");
	_il2cpp_icall_func(___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* CrashReportHandler_GetUserMetadata_m9A07B42BE5F86009D5C7AF918CC600F3BF4BF8D0 (String_t* ___0_key, const RuntimeMethod* method) 
{
	typedef String_t* (*CrashReportHandler_GetUserMetadata_m9A07B42BE5F86009D5C7AF918CC600F3BF4BF8D0_ftn) (String_t*);
	static CrashReportHandler_GetUserMetadata_m9A07B42BE5F86009D5C7AF918CC600F3BF4BF8D0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_GetUserMetadata_m9A07B42BE5F86009D5C7AF918CC600F3BF4BF8D0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::GetUserMetadata(System.String)");
	String_t* icallRetVal = _il2cpp_icall_func(___0_key);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CrashReportHandler_SetUserMetadata_mCBED74642C36CA95B6B0C52FAE02EBD0ACBC51FE (String_t* ___0_key, String_t* ___1_value, const RuntimeMethod* method) 
{
	typedef void (*CrashReportHandler_SetUserMetadata_mCBED74642C36CA95B6B0C52FAE02EBD0ACBC51FE_ftn) (String_t*, String_t*);
	static CrashReportHandler_SetUserMetadata_mCBED74642C36CA95B6B0C52FAE02EBD0ACBC51FE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (CrashReportHandler_SetUserMetadata_mCBED74642C36CA95B6B0C52FAE02EBD0ACBC51FE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.CrashReportHandler.CrashReportHandler::SetUserMetadata(System.String,System.String)");
	_il2cpp_icall_func(___0_key, ___1_value);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
