﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void GUIText_FeatureRemoved_mC9F8847D82E89E618B430969197280C6182DAD44 (void);
extern void GUIText_get_text_m5D3FBAABC74EE7E17747502E0EBF9ACB9ED710D7 (void);
extern void GUIText_set_text_m40F198779FB1B8ED8258DE3DA085E3F8FC978B51 (void);
extern void GUIText_get_material_m319F7AECA2D3BB694CE41977E3D81BB5AE9A3CAF (void);
extern void GUIText_set_material_m24D4A87CB0F8BEC8C88C8575E5D8C8883B4D08E5 (void);
extern void GUIText_get_font_m5248F289C34D598E7C6EF6A6F075FAE1AB0F673B (void);
extern void GUIText_set_font_m8CD7EB07F7C36EB30228E40C69B5C3E2E11CDC3E (void);
extern void GUIText_get_alignment_m504ABE5DAD84D32B681E73FB4B32133FFBEC8CF6 (void);
extern void GUIText_set_alignment_m5E2754D9D927AEAA41AA7916DAE820908270C790 (void);
extern void GUIText_get_anchor_m45A2941A6B51909CB641B0F107B83A24FAFE3FEA (void);
extern void GUIText_set_anchor_m970352CA718AC43CA7ABDA004DE1038BEC8C0118 (void);
extern void GUIText_get_lineSpacing_m9F7AD4889C4F9CF4359A979289D1C44F455BA0D2 (void);
extern void GUIText_set_lineSpacing_mCA765CD050EFAA3F82B182B6716CBFA98C1BD626 (void);
extern void GUIText_get_tabSize_m74C8B4AEE61356042535B403ADDBE2916EBF43A8 (void);
extern void GUIText_set_tabSize_m92659905BA1483DAF1D457EDDFF29BB4F111904D (void);
extern void GUIText_get_fontSize_mB0BE64E69A3B469DFFC50F90EC9EA7A68E75B06F (void);
extern void GUIText_set_fontSize_mDA3A887575E05D7DBBAE6DB710B037716F92274E (void);
extern void GUIText_get_fontStyle_m44CBF934DED881F0629B405E76CA2289E091F923 (void);
extern void GUIText_set_fontStyle_m3E4C47E5C322D36C2068C736C649E12B52D744C2 (void);
extern void GUIText_get_richText_m4261B50EA0A481653706AEB382422B9454F1BD28 (void);
extern void GUIText_set_richText_mF3C8A221380CA347B87139DC4251EFC374D9BB60 (void);
extern void GUIText_get_color_m2BD817AF4A4F2300766272B4565FCAC110733896 (void);
extern void GUIText_set_color_m015FA90F99238C2D1757DD9EF161DAF2A2C59D3B (void);
extern void GUIText_get_pixelOffset_m6C1468D8F3C9DD1A0A8406834070B096F4412BB3 (void);
extern void GUIText_set_pixelOffset_m9460EAB501F50EC1B602DAF6C1D06EC2B65A9C2F (void);
extern void GUIText__ctor_m9C80477CD0FCBE9740C300FFCB776740427E422B (void);
extern void TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53 (void);
extern void TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E (void);
extern void TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840 (void);
extern void TextGenerator__ctor_m2BFF25B11D7FD0FA4F8E1B04D107219A30E8A187 (void);
extern void TextGenerator__ctor_m05E7CFDC682F880A590F4AA840F81647844A0A32 (void);
extern void TextGenerator_Finalize_mD3C40A16FD4661E4712C85586430041C72E914F7 (void);
extern void TextGenerator_System_IDisposable_Dispose_m5B2B52DA9C73CE736E908030738002D6D860227F (void);
extern void TextGenerator_get_characterCountVisible_m407C344C5A13A26471315449899B927FF3D3CDFC (void);
extern void TextGenerator_ValidatedSettings_mAE5128305E953EE41E5E655675323896EF1154AE (void);
extern void TextGenerator_Invalidate_m999CE96E2F02E326092DFEB53EE8BC08CAAF8260 (void);
extern void TextGenerator_GetCharacters_mB7956402E0C66D9F9D51A3ADFCA84BE83BB54D90 (void);
extern void TextGenerator_GetLines_mFA8508172C251E1E4BBB1FBB734C82C81EF9330F (void);
extern void TextGenerator_GetVertices_m97C7D4CB9B122A10BA5921BC38F83A245EB1B97E (void);
extern void TextGenerator_GetPreferredWidth_mDDE43A5C8BDD7BBD235AE740C4C2A293A92BD28E (void);
extern void TextGenerator_GetPreferredHeight_m0EAF7CE0F503C97F4D35D0EE4A3AD923B472F89E (void);
extern void TextGenerator_PopulateWithErrors_m0E56C12E9D7714F4B8772422AAB2CAC9EA10F834 (void);
extern void TextGenerator_Populate_m5620AF6DBC180FC3FBE3E5F5930CF7CB65DD3CE2 (void);
extern void TextGenerator_PopulateWithError_m0F7C1CE45F372D80603C28F1E01F6FF796B95E02 (void);
extern void TextGenerator_PopulateAlways_mA99FFC0276B51F8DED3EABD1FF660130BE803EDA (void);
extern void TextGenerator_get_verts_mA197E8944ABE4831B93275662BB66EC53DE349D8 (void);
extern void TextGenerator_get_characters_mDA07F7108C6EF6F5DF61C478FDF3EB18DCE6B34A (void);
extern void TextGenerator_get_lines_m52DAB15921DC96120CA6C21A3FEB2406D4A76064 (void);
extern void TextGenerator_get_rectExtents_m55100375428EFAA89F3AC1B528E8716E615F9F2C (void);
extern void TextGenerator_get_vertexCount_mA13BC9C1E803E0EAAA55990D5346AA5FD81BF7E4 (void);
extern void TextGenerator_get_characterCount_mD4FD13BBBEDA2AB495C045160693754E4C106FFF (void);
extern void TextGenerator_get_lineCount_mC568C375C85695478DD054B296CBEAB11D3C4108 (void);
extern void TextGenerator_get_fontSizeUsedForBestFit_mBCA834ACDE42232D91E30FB189D99D7CE9EAE084 (void);
extern void TextGenerator_Internal_Create_m66701C08DAE160892766930F831D2AB1EB4E998C (void);
extern void TextGenerator_Internal_Destroy_m0AE3865A6886503BAE5798E6D9899EF59A0285A7 (void);
extern void TextGenerator_Populate_Internal_m74A216D58183B4069BFBD6CDF624CB97A9EC4CAA (void);
extern void TextGenerator_Populate_Internal_m4DF908756A1AC26B04AC8AB12A004203366B294B (void);
extern void TextGenerator_GetVerticesArray_m4D8B7205F525CFB46529648C0A895C1B7A8D3671 (void);
extern void TextGenerator_GetCharactersArray_mFB13E1DB78EF6E01FD615E917CF1AB4629AD8EF9 (void);
extern void TextGenerator_GetLinesArray_m6C9218CEAD56B0BC151BF13A9DD9E6105C071C1F (void);
extern void TextGenerator_GetVerticesInternal_m8BB7AB760D1071709A584FD9371D7549DA2A1B21 (void);
extern void TextGenerator_GetCharactersInternal_mB5157BBD4E1C42ACEDCC696D718E656BDD5E2398 (void);
extern void TextGenerator_GetLinesInternal_mDAC83E88FD1FD79CCCE294E49E02ED748CFC8E12 (void);
extern void TextGenerator_get_rectExtents_Injected_m2AB029C462FB27B9643C3EA2EB345E0EB011B2B8 (void);
extern void TextGenerator_Populate_Internal_Injected_m694EA7CA7449D60B16643460DA13ABC6BE0F2947 (void);
extern void TextMesh_get_text_mB3E900AED17390DE50DFC984428BC29EB1CA60A2 (void);
extern void TextMesh_set_text_mDF79D39638ED82797D0B0B3BB9E6B10712F8EA9E (void);
extern void TextMesh_get_font_m94D3A4C8E4DB171B74E4FF00AC7EC27F3D495664 (void);
extern void TextMesh_set_font_m7E407CAEDBB382B95B70069D8FAB8A9E74EAAA74 (void);
extern void TextMesh_get_fontSize_m9D9DCB98FC5E439868F25A8CDA2A4F5C2D85985C (void);
extern void TextMesh_set_fontSize_mAB9F7FFC0E4DB759B786F6A9357B18C86015498B (void);
extern void TextMesh_get_fontStyle_mB5186C6CCE5303FF459E6C95D7EB35C48A22AD73 (void);
extern void TextMesh_set_fontStyle_m038D1FE5DA05B74AF611B1D2DCDD771756E744CF (void);
extern void TextMesh_get_offsetZ_mBFF3610B382430095EF40CB4D00A770F87FD4529 (void);
extern void TextMesh_set_offsetZ_m387CC7DE7BBD2FECADF377051EB3E57E0F7B7DF7 (void);
extern void TextMesh_get_alignment_m9D0F0104F6DDA4C0B716B5C391F881200766915A (void);
extern void TextMesh_set_alignment_mCEAFE4A6CB546C3CB2B03582289FCC943EEF760F (void);
extern void TextMesh_get_anchor_m504B52CADC07A2B29CF2F229A0F03B2E102DCD69 (void);
extern void TextMesh_set_anchor_m3FCB7C4B1FF66CE189B56076C0306AFE984FCD32 (void);
extern void TextMesh_get_characterSize_mA9495F227772CFEBB2EB64B65166E682DB5E8147 (void);
extern void TextMesh_set_characterSize_mAEAE87C4648EF49409BDA93E5F504356B68D6052 (void);
extern void TextMesh_get_lineSpacing_mC8A8CA472D961F42465D3193B44E0104E2DD817E (void);
extern void TextMesh_set_lineSpacing_m3FB0A3F54E8F0412DD8BED2095AF0A73491E0658 (void);
extern void TextMesh_get_tabSize_m627F284247B15DF0F03336C7418A24EFA5A7D459 (void);
extern void TextMesh_set_tabSize_m1CA08EEE2A13E28227466754E7FE616C2DC023BA (void);
extern void TextMesh_get_richText_mD09467720AE70BEA5ACB9F5227693507957949D6 (void);
extern void TextMesh_set_richText_m26D52770D73A6B142A153B913F2D005E895F2EE2 (void);
extern void TextMesh_get_color_m128E5D16AA72D5284C70957253DEAEE4FBEB023E (void);
extern void TextMesh_set_color_mF08F30C3CD797C16289225B567724B9F07DC641E (void);
extern void TextMesh__ctor_m987D6C4E18BF8F2DB68D8D0A88FF226263F8BB52 (void);
extern void TextMesh_get_color_Injected_m3E6F8CA2677A2304BCF58A8FCE4468B29D97F769 (void);
extern void TextMesh_set_color_Injected_mC7C51FAE003F1B2FB510F50DC1C4AFBFE740B4E4 (void);
extern void CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A (void);
extern void CharacterInfo_set_advance_m0A827100F9C99B7E562798B06FFAD9F41EFB1FF1 (void);
extern void CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D (void);
extern void CharacterInfo_set_glyphWidth_m860C5ED5681A1F268910627D6EEE7DDE8507E06B (void);
extern void CharacterInfo_get_glyphHeight_m62D836A42E4D0FEEEE3BAC965F12F81D1C2F2AE8 (void);
extern void CharacterInfo_set_glyphHeight_m682595A16069892C00159D72EB20B2D50F05C724 (void);
extern void CharacterInfo_get_bearing_m71DD3F7CD871C59C7821F88E4D637729ED5C51EA (void);
extern void CharacterInfo_set_bearing_mD8494A4820C5D7C81904A423CC923EC15F5EE760 (void);
extern void CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A (void);
extern void CharacterInfo_set_minY_m62669AE0B1BF5A427B0EAFD96B7A72D69FDD564F (void);
extern void CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927 (void);
extern void CharacterInfo_set_maxY_mA0BC3D0C62E6503DC0CA8A85426E78B47F017001 (void);
extern void CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328 (void);
extern void CharacterInfo_set_minX_mFDD6B4A7E0EE9617FC4DDC8211BDE18C492EE30E (void);
extern void CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20 (void);
extern void CharacterInfo_set_maxX_m7424868F23C9275377E5CA440290046981FC6A8C (void);
extern void CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244 (void);
extern void CharacterInfo_set_uvBottomLeftUnFlipped_m8BEA5D77C004C9C284028985862DD89354B58857 (void);
extern void CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E (void);
extern void CharacterInfo_set_uvBottomRightUnFlipped_mFABB2B9BEC498079CEC2F0D633A6577A7050E46B (void);
extern void CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29 (void);
extern void CharacterInfo_set_uvTopRightUnFlipped_m3C748D994F0C767C33B0AEDC02A2A55A9F5EAECD (void);
extern void CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB (void);
extern void CharacterInfo_set_uvTopLeftUnFlipped_mE6276D39C1E1D2A4B7D54E56476290F514F04D55 (void);
extern void CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F (void);
extern void CharacterInfo_set_uvBottomLeft_m06BE4811249D7C08C8A77CAD19FAE7E1A0B2F9B2 (void);
extern void CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17 (void);
extern void CharacterInfo_set_uvBottomRight_m182BA2C48B5EAF1830A0C7B2FBDA0DB0BF157293 (void);
extern void CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16 (void);
extern void CharacterInfo_set_uvTopRight_m4FF862FED8345346FFE3F68DD33013931631CDE0 (void);
extern void CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33 (void);
extern void CharacterInfo_set_uvTopLeft_m1B1F3DAE87A83A2AD94285AA8C7E3895550C71DE (void);
extern void UIVertex__cctor_mF98CC8F21D6CD8703B47E168E976BBE15E0F6C62 (void);
extern void Font_add_textureRebuilt_m0BBB44846C17A580B078599DA5AE231DA9D6DAD7 (void);
extern void Font_remove_textureRebuilt_mB7EF9EEE803E9C70AF4217190B49C83FE287904A (void);
extern void Font_add_m_FontTextureRebuildCallback_mA82EF5F0C5CB58F31BFF773A718140DF1AD83A54 (void);
extern void Font_remove_m_FontTextureRebuildCallback_m91ABF9F0D9500493DAC69A3AB6175B52305D526C (void);
extern void Font_get_material_m61ABDEC14C6D659DDC5A4F080023699116C17364 (void);
extern void Font_set_material_m5A40979A00FF8E5038A716D1BD30A0BBF781789A (void);
extern void Font_get_fontNames_mA6C670A31716B63FA98E7B3E84D25ABFBD5820DD (void);
extern void Font_set_fontNames_mBAFE40DD1D13DF54CD833AE1999C019205AD58E3 (void);
extern void Font_get_dynamic_m5231258BFEAABA3B3EA6AB5D8C2FDCBC2AD1371C (void);
extern void Font_get_ascent_mE45D411D51A31A7F8B20C0A3C5ADDAE03B8E1F9B (void);
extern void Font_get_fontSize_m02C2F5C7A7C89A878F04C2087248DD46BBF9E26B (void);
extern void Font_get_characterInfo_mEF860463BBDF0BEEA7FD115FB358CE8640F3C0C6 (void);
extern void Font_set_characterInfo_m4F451478BB8ABA9011C0A87235E1F01750CCBAA5 (void);
extern void Font_get_lineHeight_m430CF5758E4AE2A6BF417140BFB86499AEE17790 (void);
extern void Font_get_textureRebuildCallback_m6020278C184DF0A87FFA572C95200A19AC153848 (void);
extern void Font_set_textureRebuildCallback_mE252170CFF8D476470DB7F586788675D22B046CC (void);
extern void Font__ctor_m9106C7F312AE77F6721001A5A3143951201AC841 (void);
extern void Font__ctor_mEBD3D047AEAFE39BB56C9F71668EB626FED841C2 (void);
extern void Font__ctor_m9F4256214EE9A4A3F9C8287C21ABCAA0BCCBA461 (void);
extern void Font_CreateDynamicFontFromOSFont_m3DF4B7A70F4AD6918FC44195252D9A645B3C5D62 (void);
extern void Font_CreateDynamicFontFromOSFont_m3A62128B393F57EE1C15A100A96F591FBFFCAFE6 (void);
extern void Font_InvokeTextureRebuilt_Internal_m874D1025267C908E5FCD437B41929E4DE248B01B (void);
extern void Font_GetMaxVertsForString_mA4D7EB68D13A9258D1FC755CC0A24529D03A8549 (void);
extern void Font_GetDefault_m93A1C993B4D5B93D1F5A334A4F1407B4C1DA4DC6 (void);
extern void Font_HasCharacter_m71A84FE036055880E1543D79A38FEFA495AD200B (void);
extern void Font_HasCharacter_mAB838A26F002CB5E4B4DB297F7D6836A28625B18 (void);
extern void Font_GetOSInstalledFontNames_m0C3D5751862D10A13CA25B9DE98C0EAE8A686B75 (void);
extern void Font_GetPathsToOSFonts_mF2EB5A086E0313B02C22666C9C0E980FE0EB60B8 (void);
extern void Font_Internal_CreateFont_m97CB036BAA033DDAD87E14F9D3493A3A2D9C72B1 (void);
extern void Font_Internal_CreateFontFromPath_m533A80BFC47F5313ACBFE9A4514BB7D34AC2FDB4 (void);
extern void Font_Internal_CreateDynamicFont_m5252E98E214E0B9B2D42C70A67A9A446341E050E (void);
extern void Font_GetCharacterInfo_m66CF18A6ECA8A877CA793DCC733DCF9659F398E8 (void);
extern void Font_GetCharacterInfo_m16CAF16CA4CAEA2CCC7A1DB4211F78A76040FBAA (void);
extern void Font_GetCharacterInfo_mEC706A59008A40BEFD7448A2F06589747E138EDB (void);
extern void Font_RequestCharactersInTexture_m87509ABBEDF61305BA10B2DC65B565E57FF6DDD4 (void);
extern void Font_RequestCharactersInTexture_mED1DC6B4C7FD29226A1A42AE003F4D2C3F0F0031 (void);
extern void Font_RequestCharactersInTexture_mB331838B63AEAF610A9EF70815F60ECB255D7C38 (void);
extern void FontTextureRebuildCallback__ctor_m1AF27FC83F3136E493F47015F99CE7A4E6BCA0BC (void);
extern void FontTextureRebuildCallback_Invoke_m8B52C3F4823ADBB80062209E6BA2B33202AE958D (void);
extern void FontTextureRebuildCallback_BeginInvoke_m1BE09DD783DE5DE283A630A98B2D2C6323593952 (void);
extern void FontTextureRebuildCallback_EndInvoke_m6BD88D422AB6493F49E6D4345C561AC80BFE6AEF (void);
static Il2CppMethodPointer s_methodPointers[166] = 
{
	GUIText_FeatureRemoved_mC9F8847D82E89E618B430969197280C6182DAD44,
	GUIText_get_text_m5D3FBAABC74EE7E17747502E0EBF9ACB9ED710D7,
	GUIText_set_text_m40F198779FB1B8ED8258DE3DA085E3F8FC978B51,
	GUIText_get_material_m319F7AECA2D3BB694CE41977E3D81BB5AE9A3CAF,
	GUIText_set_material_m24D4A87CB0F8BEC8C88C8575E5D8C8883B4D08E5,
	GUIText_get_font_m5248F289C34D598E7C6EF6A6F075FAE1AB0F673B,
	GUIText_set_font_m8CD7EB07F7C36EB30228E40C69B5C3E2E11CDC3E,
	GUIText_get_alignment_m504ABE5DAD84D32B681E73FB4B32133FFBEC8CF6,
	GUIText_set_alignment_m5E2754D9D927AEAA41AA7916DAE820908270C790,
	GUIText_get_anchor_m45A2941A6B51909CB641B0F107B83A24FAFE3FEA,
	GUIText_set_anchor_m970352CA718AC43CA7ABDA004DE1038BEC8C0118,
	GUIText_get_lineSpacing_m9F7AD4889C4F9CF4359A979289D1C44F455BA0D2,
	GUIText_set_lineSpacing_mCA765CD050EFAA3F82B182B6716CBFA98C1BD626,
	GUIText_get_tabSize_m74C8B4AEE61356042535B403ADDBE2916EBF43A8,
	GUIText_set_tabSize_m92659905BA1483DAF1D457EDDFF29BB4F111904D,
	GUIText_get_fontSize_mB0BE64E69A3B469DFFC50F90EC9EA7A68E75B06F,
	GUIText_set_fontSize_mDA3A887575E05D7DBBAE6DB710B037716F92274E,
	GUIText_get_fontStyle_m44CBF934DED881F0629B405E76CA2289E091F923,
	GUIText_set_fontStyle_m3E4C47E5C322D36C2068C736C649E12B52D744C2,
	GUIText_get_richText_m4261B50EA0A481653706AEB382422B9454F1BD28,
	GUIText_set_richText_mF3C8A221380CA347B87139DC4251EFC374D9BB60,
	GUIText_get_color_m2BD817AF4A4F2300766272B4565FCAC110733896,
	GUIText_set_color_m015FA90F99238C2D1757DD9EF161DAF2A2C59D3B,
	GUIText_get_pixelOffset_m6C1468D8F3C9DD1A0A8406834070B096F4412BB3,
	GUIText_set_pixelOffset_m9460EAB501F50EC1B602DAF6C1D06EC2B65A9C2F,
	GUIText__ctor_m9C80477CD0FCBE9740C300FFCB776740427E422B,
	TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53,
	TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E,
	TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840,
	TextGenerator__ctor_m2BFF25B11D7FD0FA4F8E1B04D107219A30E8A187,
	TextGenerator__ctor_m05E7CFDC682F880A590F4AA840F81647844A0A32,
	TextGenerator_Finalize_mD3C40A16FD4661E4712C85586430041C72E914F7,
	TextGenerator_System_IDisposable_Dispose_m5B2B52DA9C73CE736E908030738002D6D860227F,
	TextGenerator_get_characterCountVisible_m407C344C5A13A26471315449899B927FF3D3CDFC,
	TextGenerator_ValidatedSettings_mAE5128305E953EE41E5E655675323896EF1154AE,
	TextGenerator_Invalidate_m999CE96E2F02E326092DFEB53EE8BC08CAAF8260,
	TextGenerator_GetCharacters_mB7956402E0C66D9F9D51A3ADFCA84BE83BB54D90,
	TextGenerator_GetLines_mFA8508172C251E1E4BBB1FBB734C82C81EF9330F,
	TextGenerator_GetVertices_m97C7D4CB9B122A10BA5921BC38F83A245EB1B97E,
	TextGenerator_GetPreferredWidth_mDDE43A5C8BDD7BBD235AE740C4C2A293A92BD28E,
	TextGenerator_GetPreferredHeight_m0EAF7CE0F503C97F4D35D0EE4A3AD923B472F89E,
	TextGenerator_PopulateWithErrors_m0E56C12E9D7714F4B8772422AAB2CAC9EA10F834,
	TextGenerator_Populate_m5620AF6DBC180FC3FBE3E5F5930CF7CB65DD3CE2,
	TextGenerator_PopulateWithError_m0F7C1CE45F372D80603C28F1E01F6FF796B95E02,
	TextGenerator_PopulateAlways_mA99FFC0276B51F8DED3EABD1FF660130BE803EDA,
	TextGenerator_get_verts_mA197E8944ABE4831B93275662BB66EC53DE349D8,
	TextGenerator_get_characters_mDA07F7108C6EF6F5DF61C478FDF3EB18DCE6B34A,
	TextGenerator_get_lines_m52DAB15921DC96120CA6C21A3FEB2406D4A76064,
	TextGenerator_get_rectExtents_m55100375428EFAA89F3AC1B528E8716E615F9F2C,
	TextGenerator_get_vertexCount_mA13BC9C1E803E0EAAA55990D5346AA5FD81BF7E4,
	TextGenerator_get_characterCount_mD4FD13BBBEDA2AB495C045160693754E4C106FFF,
	TextGenerator_get_lineCount_mC568C375C85695478DD054B296CBEAB11D3C4108,
	TextGenerator_get_fontSizeUsedForBestFit_mBCA834ACDE42232D91E30FB189D99D7CE9EAE084,
	TextGenerator_Internal_Create_m66701C08DAE160892766930F831D2AB1EB4E998C,
	TextGenerator_Internal_Destroy_m0AE3865A6886503BAE5798E6D9899EF59A0285A7,
	TextGenerator_Populate_Internal_m74A216D58183B4069BFBD6CDF624CB97A9EC4CAA,
	TextGenerator_Populate_Internal_m4DF908756A1AC26B04AC8AB12A004203366B294B,
	TextGenerator_GetVerticesArray_m4D8B7205F525CFB46529648C0A895C1B7A8D3671,
	TextGenerator_GetCharactersArray_mFB13E1DB78EF6E01FD615E917CF1AB4629AD8EF9,
	TextGenerator_GetLinesArray_m6C9218CEAD56B0BC151BF13A9DD9E6105C071C1F,
	TextGenerator_GetVerticesInternal_m8BB7AB760D1071709A584FD9371D7549DA2A1B21,
	TextGenerator_GetCharactersInternal_mB5157BBD4E1C42ACEDCC696D718E656BDD5E2398,
	TextGenerator_GetLinesInternal_mDAC83E88FD1FD79CCCE294E49E02ED748CFC8E12,
	TextGenerator_get_rectExtents_Injected_m2AB029C462FB27B9643C3EA2EB345E0EB011B2B8,
	TextGenerator_Populate_Internal_Injected_m694EA7CA7449D60B16643460DA13ABC6BE0F2947,
	TextMesh_get_text_mB3E900AED17390DE50DFC984428BC29EB1CA60A2,
	TextMesh_set_text_mDF79D39638ED82797D0B0B3BB9E6B10712F8EA9E,
	TextMesh_get_font_m94D3A4C8E4DB171B74E4FF00AC7EC27F3D495664,
	TextMesh_set_font_m7E407CAEDBB382B95B70069D8FAB8A9E74EAAA74,
	TextMesh_get_fontSize_m9D9DCB98FC5E439868F25A8CDA2A4F5C2D85985C,
	TextMesh_set_fontSize_mAB9F7FFC0E4DB759B786F6A9357B18C86015498B,
	TextMesh_get_fontStyle_mB5186C6CCE5303FF459E6C95D7EB35C48A22AD73,
	TextMesh_set_fontStyle_m038D1FE5DA05B74AF611B1D2DCDD771756E744CF,
	TextMesh_get_offsetZ_mBFF3610B382430095EF40CB4D00A770F87FD4529,
	TextMesh_set_offsetZ_m387CC7DE7BBD2FECADF377051EB3E57E0F7B7DF7,
	TextMesh_get_alignment_m9D0F0104F6DDA4C0B716B5C391F881200766915A,
	TextMesh_set_alignment_mCEAFE4A6CB546C3CB2B03582289FCC943EEF760F,
	TextMesh_get_anchor_m504B52CADC07A2B29CF2F229A0F03B2E102DCD69,
	TextMesh_set_anchor_m3FCB7C4B1FF66CE189B56076C0306AFE984FCD32,
	TextMesh_get_characterSize_mA9495F227772CFEBB2EB64B65166E682DB5E8147,
	TextMesh_set_characterSize_mAEAE87C4648EF49409BDA93E5F504356B68D6052,
	TextMesh_get_lineSpacing_mC8A8CA472D961F42465D3193B44E0104E2DD817E,
	TextMesh_set_lineSpacing_m3FB0A3F54E8F0412DD8BED2095AF0A73491E0658,
	TextMesh_get_tabSize_m627F284247B15DF0F03336C7418A24EFA5A7D459,
	TextMesh_set_tabSize_m1CA08EEE2A13E28227466754E7FE616C2DC023BA,
	TextMesh_get_richText_mD09467720AE70BEA5ACB9F5227693507957949D6,
	TextMesh_set_richText_m26D52770D73A6B142A153B913F2D005E895F2EE2,
	TextMesh_get_color_m128E5D16AA72D5284C70957253DEAEE4FBEB023E,
	TextMesh_set_color_mF08F30C3CD797C16289225B567724B9F07DC641E,
	TextMesh__ctor_m987D6C4E18BF8F2DB68D8D0A88FF226263F8BB52,
	TextMesh_get_color_Injected_m3E6F8CA2677A2304BCF58A8FCE4468B29D97F769,
	TextMesh_set_color_Injected_mC7C51FAE003F1B2FB510F50DC1C4AFBFE740B4E4,
	CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A,
	CharacterInfo_set_advance_m0A827100F9C99B7E562798B06FFAD9F41EFB1FF1,
	CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D,
	CharacterInfo_set_glyphWidth_m860C5ED5681A1F268910627D6EEE7DDE8507E06B,
	CharacterInfo_get_glyphHeight_m62D836A42E4D0FEEEE3BAC965F12F81D1C2F2AE8,
	CharacterInfo_set_glyphHeight_m682595A16069892C00159D72EB20B2D50F05C724,
	CharacterInfo_get_bearing_m71DD3F7CD871C59C7821F88E4D637729ED5C51EA,
	CharacterInfo_set_bearing_mD8494A4820C5D7C81904A423CC923EC15F5EE760,
	CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A,
	CharacterInfo_set_minY_m62669AE0B1BF5A427B0EAFD96B7A72D69FDD564F,
	CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927,
	CharacterInfo_set_maxY_mA0BC3D0C62E6503DC0CA8A85426E78B47F017001,
	CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328,
	CharacterInfo_set_minX_mFDD6B4A7E0EE9617FC4DDC8211BDE18C492EE30E,
	CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20,
	CharacterInfo_set_maxX_m7424868F23C9275377E5CA440290046981FC6A8C,
	CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244,
	CharacterInfo_set_uvBottomLeftUnFlipped_m8BEA5D77C004C9C284028985862DD89354B58857,
	CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E,
	CharacterInfo_set_uvBottomRightUnFlipped_mFABB2B9BEC498079CEC2F0D633A6577A7050E46B,
	CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29,
	CharacterInfo_set_uvTopRightUnFlipped_m3C748D994F0C767C33B0AEDC02A2A55A9F5EAECD,
	CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB,
	CharacterInfo_set_uvTopLeftUnFlipped_mE6276D39C1E1D2A4B7D54E56476290F514F04D55,
	CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F,
	CharacterInfo_set_uvBottomLeft_m06BE4811249D7C08C8A77CAD19FAE7E1A0B2F9B2,
	CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17,
	CharacterInfo_set_uvBottomRight_m182BA2C48B5EAF1830A0C7B2FBDA0DB0BF157293,
	CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16,
	CharacterInfo_set_uvTopRight_m4FF862FED8345346FFE3F68DD33013931631CDE0,
	CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33,
	CharacterInfo_set_uvTopLeft_m1B1F3DAE87A83A2AD94285AA8C7E3895550C71DE,
	UIVertex__cctor_mF98CC8F21D6CD8703B47E168E976BBE15E0F6C62,
	Font_add_textureRebuilt_m0BBB44846C17A580B078599DA5AE231DA9D6DAD7,
	Font_remove_textureRebuilt_mB7EF9EEE803E9C70AF4217190B49C83FE287904A,
	Font_add_m_FontTextureRebuildCallback_mA82EF5F0C5CB58F31BFF773A718140DF1AD83A54,
	Font_remove_m_FontTextureRebuildCallback_m91ABF9F0D9500493DAC69A3AB6175B52305D526C,
	Font_get_material_m61ABDEC14C6D659DDC5A4F080023699116C17364,
	Font_set_material_m5A40979A00FF8E5038A716D1BD30A0BBF781789A,
	Font_get_fontNames_mA6C670A31716B63FA98E7B3E84D25ABFBD5820DD,
	Font_set_fontNames_mBAFE40DD1D13DF54CD833AE1999C019205AD58E3,
	Font_get_dynamic_m5231258BFEAABA3B3EA6AB5D8C2FDCBC2AD1371C,
	Font_get_ascent_mE45D411D51A31A7F8B20C0A3C5ADDAE03B8E1F9B,
	Font_get_fontSize_m02C2F5C7A7C89A878F04C2087248DD46BBF9E26B,
	Font_get_characterInfo_mEF860463BBDF0BEEA7FD115FB358CE8640F3C0C6,
	Font_set_characterInfo_m4F451478BB8ABA9011C0A87235E1F01750CCBAA5,
	Font_get_lineHeight_m430CF5758E4AE2A6BF417140BFB86499AEE17790,
	Font_get_textureRebuildCallback_m6020278C184DF0A87FFA572C95200A19AC153848,
	Font_set_textureRebuildCallback_mE252170CFF8D476470DB7F586788675D22B046CC,
	Font__ctor_m9106C7F312AE77F6721001A5A3143951201AC841,
	Font__ctor_mEBD3D047AEAFE39BB56C9F71668EB626FED841C2,
	Font__ctor_m9F4256214EE9A4A3F9C8287C21ABCAA0BCCBA461,
	Font_CreateDynamicFontFromOSFont_m3DF4B7A70F4AD6918FC44195252D9A645B3C5D62,
	Font_CreateDynamicFontFromOSFont_m3A62128B393F57EE1C15A100A96F591FBFFCAFE6,
	Font_InvokeTextureRebuilt_Internal_m874D1025267C908E5FCD437B41929E4DE248B01B,
	Font_GetMaxVertsForString_mA4D7EB68D13A9258D1FC755CC0A24529D03A8549,
	Font_GetDefault_m93A1C993B4D5B93D1F5A334A4F1407B4C1DA4DC6,
	Font_HasCharacter_m71A84FE036055880E1543D79A38FEFA495AD200B,
	Font_HasCharacter_mAB838A26F002CB5E4B4DB297F7D6836A28625B18,
	Font_GetOSInstalledFontNames_m0C3D5751862D10A13CA25B9DE98C0EAE8A686B75,
	Font_GetPathsToOSFonts_mF2EB5A086E0313B02C22666C9C0E980FE0EB60B8,
	Font_Internal_CreateFont_m97CB036BAA033DDAD87E14F9D3493A3A2D9C72B1,
	Font_Internal_CreateFontFromPath_m533A80BFC47F5313ACBFE9A4514BB7D34AC2FDB4,
	Font_Internal_CreateDynamicFont_m5252E98E214E0B9B2D42C70A67A9A446341E050E,
	Font_GetCharacterInfo_m66CF18A6ECA8A877CA793DCC733DCF9659F398E8,
	Font_GetCharacterInfo_m16CAF16CA4CAEA2CCC7A1DB4211F78A76040FBAA,
	Font_GetCharacterInfo_mEC706A59008A40BEFD7448A2F06589747E138EDB,
	Font_RequestCharactersInTexture_m87509ABBEDF61305BA10B2DC65B565E57FF6DDD4,
	Font_RequestCharactersInTexture_mED1DC6B4C7FD29226A1A42AE003F4D2C3F0F0031,
	Font_RequestCharactersInTexture_mB331838B63AEAF610A9EF70815F60ECB255D7C38,
	FontTextureRebuildCallback__ctor_m1AF27FC83F3136E493F47015F99CE7A4E6BCA0BC,
	FontTextureRebuildCallback_Invoke_m8B52C3F4823ADBB80062209E6BA2B33202AE958D,
	FontTextureRebuildCallback_BeginInvoke_m1BE09DD783DE5DE283A630A98B2D2C6323593952,
	FontTextureRebuildCallback_EndInvoke_m6BD88D422AB6493F49E6D4345C561AC80BFE6AEF,
};
extern void TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53_AdjustorThunk (void);
extern void TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E_AdjustorThunk (void);
extern void TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840_AdjustorThunk (void);
extern void CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A_AdjustorThunk (void);
extern void CharacterInfo_set_advance_m0A827100F9C99B7E562798B06FFAD9F41EFB1FF1_AdjustorThunk (void);
extern void CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D_AdjustorThunk (void);
extern void CharacterInfo_set_glyphWidth_m860C5ED5681A1F268910627D6EEE7DDE8507E06B_AdjustorThunk (void);
extern void CharacterInfo_get_glyphHeight_m62D836A42E4D0FEEEE3BAC965F12F81D1C2F2AE8_AdjustorThunk (void);
extern void CharacterInfo_set_glyphHeight_m682595A16069892C00159D72EB20B2D50F05C724_AdjustorThunk (void);
extern void CharacterInfo_get_bearing_m71DD3F7CD871C59C7821F88E4D637729ED5C51EA_AdjustorThunk (void);
extern void CharacterInfo_set_bearing_mD8494A4820C5D7C81904A423CC923EC15F5EE760_AdjustorThunk (void);
extern void CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A_AdjustorThunk (void);
extern void CharacterInfo_set_minY_m62669AE0B1BF5A427B0EAFD96B7A72D69FDD564F_AdjustorThunk (void);
extern void CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927_AdjustorThunk (void);
extern void CharacterInfo_set_maxY_mA0BC3D0C62E6503DC0CA8A85426E78B47F017001_AdjustorThunk (void);
extern void CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328_AdjustorThunk (void);
extern void CharacterInfo_set_minX_mFDD6B4A7E0EE9617FC4DDC8211BDE18C492EE30E_AdjustorThunk (void);
extern void CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20_AdjustorThunk (void);
extern void CharacterInfo_set_maxX_m7424868F23C9275377E5CA440290046981FC6A8C_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244_AdjustorThunk (void);
extern void CharacterInfo_set_uvBottomLeftUnFlipped_m8BEA5D77C004C9C284028985862DD89354B58857_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E_AdjustorThunk (void);
extern void CharacterInfo_set_uvBottomRightUnFlipped_mFABB2B9BEC498079CEC2F0D633A6577A7050E46B_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29_AdjustorThunk (void);
extern void CharacterInfo_set_uvTopRightUnFlipped_m3C748D994F0C767C33B0AEDC02A2A55A9F5EAECD_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB_AdjustorThunk (void);
extern void CharacterInfo_set_uvTopLeftUnFlipped_mE6276D39C1E1D2A4B7D54E56476290F514F04D55_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F_AdjustorThunk (void);
extern void CharacterInfo_set_uvBottomLeft_m06BE4811249D7C08C8A77CAD19FAE7E1A0B2F9B2_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17_AdjustorThunk (void);
extern void CharacterInfo_set_uvBottomRight_m182BA2C48B5EAF1830A0C7B2FBDA0DB0BF157293_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16_AdjustorThunk (void);
extern void CharacterInfo_set_uvTopRight_m4FF862FED8345346FFE3F68DD33013931631CDE0_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33_AdjustorThunk (void);
extern void CharacterInfo_set_uvTopLeft_m1B1F3DAE87A83A2AD94285AA8C7E3895550C71DE_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[35] = 
{
	{ 0x0600001B, TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53_AdjustorThunk },
	{ 0x0600001C, TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E_AdjustorThunk },
	{ 0x0600001D, TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840_AdjustorThunk },
	{ 0x0600005D, CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A_AdjustorThunk },
	{ 0x0600005E, CharacterInfo_set_advance_m0A827100F9C99B7E562798B06FFAD9F41EFB1FF1_AdjustorThunk },
	{ 0x0600005F, CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D_AdjustorThunk },
	{ 0x06000060, CharacterInfo_set_glyphWidth_m860C5ED5681A1F268910627D6EEE7DDE8507E06B_AdjustorThunk },
	{ 0x06000061, CharacterInfo_get_glyphHeight_m62D836A42E4D0FEEEE3BAC965F12F81D1C2F2AE8_AdjustorThunk },
	{ 0x06000062, CharacterInfo_set_glyphHeight_m682595A16069892C00159D72EB20B2D50F05C724_AdjustorThunk },
	{ 0x06000063, CharacterInfo_get_bearing_m71DD3F7CD871C59C7821F88E4D637729ED5C51EA_AdjustorThunk },
	{ 0x06000064, CharacterInfo_set_bearing_mD8494A4820C5D7C81904A423CC923EC15F5EE760_AdjustorThunk },
	{ 0x06000065, CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A_AdjustorThunk },
	{ 0x06000066, CharacterInfo_set_minY_m62669AE0B1BF5A427B0EAFD96B7A72D69FDD564F_AdjustorThunk },
	{ 0x06000067, CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927_AdjustorThunk },
	{ 0x06000068, CharacterInfo_set_maxY_mA0BC3D0C62E6503DC0CA8A85426E78B47F017001_AdjustorThunk },
	{ 0x06000069, CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328_AdjustorThunk },
	{ 0x0600006A, CharacterInfo_set_minX_mFDD6B4A7E0EE9617FC4DDC8211BDE18C492EE30E_AdjustorThunk },
	{ 0x0600006B, CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20_AdjustorThunk },
	{ 0x0600006C, CharacterInfo_set_maxX_m7424868F23C9275377E5CA440290046981FC6A8C_AdjustorThunk },
	{ 0x0600006D, CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244_AdjustorThunk },
	{ 0x0600006E, CharacterInfo_set_uvBottomLeftUnFlipped_m8BEA5D77C004C9C284028985862DD89354B58857_AdjustorThunk },
	{ 0x0600006F, CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E_AdjustorThunk },
	{ 0x06000070, CharacterInfo_set_uvBottomRightUnFlipped_mFABB2B9BEC498079CEC2F0D633A6577A7050E46B_AdjustorThunk },
	{ 0x06000071, CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29_AdjustorThunk },
	{ 0x06000072, CharacterInfo_set_uvTopRightUnFlipped_m3C748D994F0C767C33B0AEDC02A2A55A9F5EAECD_AdjustorThunk },
	{ 0x06000073, CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB_AdjustorThunk },
	{ 0x06000074, CharacterInfo_set_uvTopLeftUnFlipped_mE6276D39C1E1D2A4B7D54E56476290F514F04D55_AdjustorThunk },
	{ 0x06000075, CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F_AdjustorThunk },
	{ 0x06000076, CharacterInfo_set_uvBottomLeft_m06BE4811249D7C08C8A77CAD19FAE7E1A0B2F9B2_AdjustorThunk },
	{ 0x06000077, CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17_AdjustorThunk },
	{ 0x06000078, CharacterInfo_set_uvBottomRight_m182BA2C48B5EAF1830A0C7B2FBDA0DB0BF157293_AdjustorThunk },
	{ 0x06000079, CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16_AdjustorThunk },
	{ 0x0600007A, CharacterInfo_set_uvTopRight_m4FF862FED8345346FFE3F68DD33013931631CDE0_AdjustorThunk },
	{ 0x0600007B, CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33_AdjustorThunk },
	{ 0x0600007C, CharacterInfo_set_uvTopLeft_m1B1F3DAE87A83A2AD94285AA8C7E3895550C71DE_AdjustorThunk },
};
static const int32_t s_InvokerIndices[166] = 
{
	9089,
	4168,
	3807,
	4250,
	3881,
	4250,
	3881,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4168,
	3807,
	4172,
	3811,
	4356,
	3978,
	4364,
	2287,
	2346,
	3258,
	4364,
	3852,
	4364,
	4364,
	4216,
	3609,
	4364,
	3881,
	3881,
	3881,
	2574,
	2574,
	1645,
	2324,
	2439,
	2439,
	4250,
	4250,
	4250,
	4276,
	4216,
	4216,
	4216,
	4216,
	9020,
	8882,
	4,
	6,
	4250,
	4250,
	4250,
	3881,
	3881,
	3881,
	3788,
	3,
	4250,
	3881,
	4250,
	3881,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4172,
	3811,
	4364,
	3788,
	3788,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	9089,
	8887,
	8887,
	3881,
	3881,
	4250,
	3881,
	4250,
	3881,
	4168,
	4216,
	4216,
	4250,
	3881,
	4216,
	4250,
	3881,
	4364,
	3881,
	2796,
	7626,
	7626,
	8887,
	8399,
	9031,
	3270,
	3166,
	9031,
	9031,
	7975,
	7975,
	6982,
	1084,
	1650,
	2338,
	2053,
	2796,
	3881,
	2798,
	4364,
	2524,
	3881,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TextRenderingModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule = 
{
	"UnityEngine.TextRenderingModule.dll",
	166,
	s_methodPointers,
	35,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_TextRenderingModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
