﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD (void);
extern void Touch_set_fingerId_m8BBCD294D98305D705F6515EB6EB6A302B23E543 (void);
extern void Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A (void);
extern void Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B (void);
extern void Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D (void);
extern void Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B (void);
extern void Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299 (void);
extern void Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A (void);
extern void Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC (void);
extern void Touch_set_deltaTime_m184C925F328EBD11ACD9D0B2969A7AECB09BA429 (void);
extern void Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6 (void);
extern void Touch_set_tapCount_m5F356E1CF7FF9DD41D37AD5D59C0650FC5771E36 (void);
extern void Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0 (void);
extern void Touch_set_phase_m47EEB383DD6F9EC6C4840B1F145D6818FCEBBD93 (void);
extern void Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C (void);
extern void Touch_set_pressure_m2D9F9D813ECC29883196988E1127DEC93EC83C78 (void);
extern void Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE (void);
extern void Touch_set_maximumPossiblePressure_m9C821DDC85966E5E009EC9A039A6FD9B5511E841 (void);
extern void Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745 (void);
extern void Touch_set_type_mA97992E6B2C00207DF82CA2184DE6B4F22A7BEC1 (void);
extern void Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391 (void);
extern void Touch_set_altitudeAngle_mBB5987AA6F0CCBF4E48BBC004474F7AA70F430F0 (void);
extern void Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490 (void);
extern void Touch_set_azimuthAngle_m076DE0C898C8C61F3CF4E1A5BA2D60F8FF66B87C (void);
extern void Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960 (void);
extern void Touch_set_radius_m50765FCA177E136896A04E640403CE1C1BE4F1FC (void);
extern void Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21 (void);
extern void Touch_set_radiusVariance_mD579FB863378B3774D71C87886D3D1F26B3BF4B9 (void);
extern void AccelerationEvent_get_acceleration_m3F898B68161B923DD2EE28BE4680261C273CD783 (void);
extern void AccelerationEvent_get_deltaTime_m7493D24DE20EE02573BFEC898FBD3E808A20C2AE (void);
extern void Gyroscope__ctor_mC476EA849DBF7705A36094F4A2609B5F6286408F (void);
extern void Gyroscope_rotationRate_Internal_mC69243A53ADC6720409577DA04DAE4C573F9498D (void);
extern void Gyroscope_rotationRateUnbiased_Internal_m4E9399AE2BEE46D5998CA2103F5BCE545E7DCEC3 (void);
extern void Gyroscope_gravity_Internal_m4FE5E5B9F0F9906129BEC2489FF7A8DB137BB303 (void);
extern void Gyroscope_userAcceleration_Internal_mAA738C8CCED8880648951AA48BD1EB2434CACADD (void);
extern void Gyroscope_attitude_Internal_mE673BB2C9BDCD791AD249FF307C10F83DCC5F3D2 (void);
extern void Gyroscope_getEnabled_Internal_m69827470009782D6CC88AA7F9EEAC66A3E5C3BCE (void);
extern void Gyroscope_setEnabled_Internal_mBA928BFDE5565840E372846AF9E0D0E45AD7D115 (void);
extern void Gyroscope_getUpdateInterval_Internal_mFFF394E36CEF917BD5357FC99E3AEE31516FBDE4 (void);
extern void Gyroscope_setUpdateInterval_Internal_m9E08181F8BF28707553BFB77A2B7697C7E33DB6D (void);
extern void Gyroscope_get_rotationRate_mCF8F2D040B77A6C147092302C80C8DEC39918954 (void);
extern void Gyroscope_get_rotationRateUnbiased_m6B54A5F9A866E1F5005EA8B1575607DF2F3AB7A3 (void);
extern void Gyroscope_get_gravity_m33AEF129216C2ECF5289C06B795FCA0EECEBE1BA (void);
extern void Gyroscope_get_userAcceleration_m85C280EFA252EAD6C566811235033FEE76654637 (void);
extern void Gyroscope_get_attitude_mF6D8131ED2D0E5BF979C7FC4AAC99E87A01CBE85 (void);
extern void Gyroscope_get_enabled_m10F5B3F646AB1A6EEE2831010642E9E1E0BCBDB9 (void);
extern void Gyroscope_set_enabled_m2B22BC93369BA61034A80350405FE1B493822DAB (void);
extern void Gyroscope_get_updateInterval_mBB5A5F3CC5556384DCE0A5BB4485FB37D1917C5D (void);
extern void Gyroscope_set_updateInterval_m477CB8AF6D656813C14467CCB62EDC3BF1383925 (void);
extern void Gyroscope_rotationRate_Internal_Injected_mDE2FBCB9A01D69CED09AF11FA0958E0EA6B0E5DF (void);
extern void Gyroscope_rotationRateUnbiased_Internal_Injected_m1BEF4BAAC9DF68F7C8858E504D4F15CBCA92B63B (void);
extern void Gyroscope_gravity_Internal_Injected_m71D25DBC8914B61C552F2AFA5329C097F768576A (void);
extern void Gyroscope_userAcceleration_Internal_Injected_mA8306BBF901F1D2F9CB443CE00BAB3E0F7AC3F35 (void);
extern void Gyroscope_attitude_Internal_Injected_m550F81250B94FE9B11049C8F49DE2F8B9061A2AC (void);
extern void LocationInfo_get_latitude_mAF0A46443555AF18EA3C516292CB92B1669CA863 (void);
extern void LocationInfo_get_longitude_mCB720DD0E139B7C614F78D40595E1BBF1F5433A2 (void);
extern void LocationInfo_get_altitude_m3B4BE2F447F3599F5677DD72A86C3A1A801ABAE0 (void);
extern void LocationInfo_get_horizontalAccuracy_m3178154DD5F4F72B8743665F33CCAE5BEC6296F4 (void);
extern void LocationInfo_get_verticalAccuracy_m90B3477474136BE858810B92C2C601BBB311A16B (void);
extern void LocationInfo_get_timestamp_mAD1095E60A61DF0FA66195AA06A0F801CF9BDC9F (void);
extern void LocationService_IsServiceEnabledByUser_m88DF3FEBB0B816F0D90E4972630D95714E5DDEDF (void);
extern void LocationService_GetLocationStatus_m1BE18671C8A455141C877979259EB9BA546A9103 (void);
extern void LocationService_GetLastLocation_m50BFC921ADF32DD69D00937B44131B0770D4F662 (void);
extern void LocationService_SetDesiredAccuracy_m6F69C809996908CD95C17A6BD96D216667FD5B58 (void);
extern void LocationService_SetDistanceFilter_m847B85271A56F9291337C79E6C80012F2BD6C1AE (void);
extern void LocationService_StartUpdatingLocation_m93789B3CDDF62EBD26326127DEC65C6C94123D29 (void);
extern void LocationService_StopUpdatingLocation_mB063A5546139211C3796E0422365829ABBB26C20 (void);
extern void LocationService_GetLastHeading_m4ED66327647F8DA6CFCB895859CCDD5952276BEA (void);
extern void LocationService_IsHeadingUpdatesEnabled_mC0885519051CEF5C9387EDC7FCED32C774B5C873 (void);
extern void LocationService_SetHeadingUpdatesEnabled_m77B8BAC72D790DF0B03CC97AA3CA1863F657B9A7 (void);
extern void LocationService_get_isEnabledByUser_m78FE3A38B1101BAF658E259C1A6B4CD9A008DCAE (void);
extern void LocationService_get_status_m25B7C4012B9529265D9746BB73ED689737E9C9CD (void);
extern void LocationService_get_lastData_m50C68F3DFB23894CF519F00BBA9D72146EE9345F (void);
extern void LocationService_Start_m5076FE201E96C086B0F9C2D8677DA69C98099F3D (void);
extern void LocationService_Start_m9415AA1CC6D8E4C450C6CF1C9D32BFBCF44C1F12 (void);
extern void LocationService_Start_mC2A90619923D4BEB5F9B5CCD95F317B98D67AF3A (void);
extern void LocationService_Stop_mB9332CB653E7A7CE6AE07240EA6C0B6C9AEC0D96 (void);
extern void LocationService__ctor_mCA7E0A5F2303FE28CE83787FEE9C5A020195E6DC (void);
extern void LocationService_GetLastLocation_Injected_m2463D5B46B8E9CA2C10AB3E611CD176BE7C3D472 (void);
extern void LocationService_GetLastHeading_Injected_m6BC975D8809617948C105507082D3273FBDF0539 (void);
extern void Compass_get_magneticHeading_m5052F97DD5B914F629B138956D250AE2EFFE1C55 (void);
extern void Compass_get_trueHeading_m5546F74294A5CC2B4A731ECE1E02F0BE7085582C (void);
extern void Compass_get_headingAccuracy_mED82316932D27BCF4C30297B3D97169E49A8DBFF (void);
extern void Compass_get_rawVector_mC6A8DB056776CA677F9D573BF8415241BC116591 (void);
extern void Compass_get_timestamp_m3D2E1DB4FF2F73FA62DA8DD2655DB7D402BC0346 (void);
extern void Compass_get_enabled_mA531BD367FE1E72DF6FCB3DBA21ECDDBFE3EBA56 (void);
extern void Compass_set_enabled_m26DFB64F789DA4B875359E8CE1C3E9E2270CFCA9 (void);
extern void Compass__ctor_mC0A067ED82089D178EC3E1CE6311B85366F33D35 (void);
extern void CameraRaycastHelper_RaycastTry_m79A654495BD2C09623E9067BCC70D23A0DA3BF58 (void);
extern void CameraRaycastHelper_RaycastTry2D_m132832B9171CD030AD231A63BF70D1226ED1F373 (void);
extern void CameraRaycastHelper__ctor_mA1A7E770622B10806FD65CD3DB0A6A19191E306D (void);
extern void CameraRaycastHelper_RaycastTry_Injected_m4A9EA285FB7B24B7B3D894E7EE997B41ED302DEF (void);
extern void CameraRaycastHelper_RaycastTry2D_Injected_m2620821FE8CB793C314AAE43E3B4C7BEAE5D4C9E (void);
extern void Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62 (void);
extern void Input_GetAxisRaw_m47C0CF8E090561A2F407A4E11D5F2A45044EB8E4 (void);
extern void Input_GetButton_m2F217DAE69DB3D1324FB848B3C9C84F19A80989E (void);
extern void Input_GetButtonDown_mEF5F80C9E8F04104E807D9CBD6F70CDB98751579 (void);
extern void Input_GetButtonUp_mEE713E86F1A024762EFED092BC59F3478E786601 (void);
extern void Input_GetKeyInt_m2FFCC49AF36B74247CC1B412E9787A15D0984E95 (void);
extern void Input_GetKeyUpInt_mB26B433DD3A21ACAF04D23252B09068EFFEDA0F9 (void);
extern void Input_GetKeyDownInt_m0B655F969FCBC011BC2616E3E5A657CF7D76568A (void);
extern void Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA (void);
extern void Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3 (void);
extern void Input_GetMouseButtonUp_mBE89CC9C69BBEA9A863819E77EA54411B0476ED6 (void);
extern void Input_ResetInputAxes_mB5A22E8AFB4F27164387B5AAE8EFBF796B06EF8C (void);
extern void Input_GetJoystickNames_m506FC5C5D06CE7A15EBB9ACEC9DCF546E2DDCC0B (void);
extern void Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1 (void);
extern void Input_GetPenEvent_mFCFFB0288585085BB212A69BC42381B5235FE5DE (void);
extern void Input_GetLastPenContactEvent_mAB37B70407FDE3DE8230B55D3D4828DC50EAF0BF (void);
extern void Input_ResetPenEvents_m10EDA2C5C566DB6AEA7CE3D81A553D4D5CE797C8 (void);
extern void Input_ClearLastPenContactEvent_m3240C9BA6CE089E85624BD0FA031A48CDE2664A3 (void);
extern void Input_GetAccelerationEvent_mA4488CE59AE0F4D5518A8B9BFF861B13FC7C5D56 (void);
extern void Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434 (void);
extern void Input_GetKey_m8D6171F09AC784866255D2634A3986A75644BE6C (void);
extern void Input_GetKeyUp_m9A962E395811A9901E7E05F267E198A533DBEF2F (void);
extern void Input_GetKeyUp_m504C6CF9A40BD840964AD0495266CD003676289E (void);
extern void Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2 (void);
extern void Input_GetKeyDown_m789DB780D0567DCC23B501D15AABD4F2E3591A3F (void);
extern void Input_SimulateTouch_mAB41EA2CE74B94F069216FDE8D75DC3735115629 (void);
extern void Input_SimulateTouchInternal_m4E438A4F030156CFCF0C9C064C240202023370A3 (void);
extern void Input_get_simulateMouseWithTouches_mA95D11052F78FD2E8D253E6790968CEFA0E98B84 (void);
extern void Input_set_simulateMouseWithTouches_m1D86DBF4BDCAA4AF3F15EE2BD51FB4C215D2AC6E (void);
extern void Input_get_anyKey_m6FF00BB4F01FA996E2FB3F3727EBC414C8EB2CEB (void);
extern void Input_get_anyKeyDown_m9B8D98B5574A2AA63CEED98FBF0E2A1D946BCA71 (void);
extern void Input_get_inputString_mB022DED50E8838B8319C591830BEB21E4904561B (void);
extern void Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C (void);
extern void Input_get_mouseScrollDelta_mD112408E9182AA0F529179FF31E21D8DCD5CFA74 (void);
extern void Input_get_imeCompositionMode_mAD9C0224B3845A9132D4265AF468FF203AA43BAC (void);
extern void Input_set_imeCompositionMode_m0399964447DDFE54E04F516A01696862F7174C9A (void);
extern void Input_get_compositionString_mC9E603E4FB61090827F77A3D509BF3AA0A48C9A9 (void);
extern void Input_get_imeIsSelected_m4A47A74A3CB95CCC1A8B8498E67C87205EF73D0D (void);
extern void Input_get_compositionCursorPos_mE1E48997CA0C30D206D08FAF06455123D8D24D15 (void);
extern void Input_set_compositionCursorPos_m16A856BFBF1DAE42B0089696906F530334861E98 (void);
extern void Input_get_eatKeyPressOnTextFieldFocus_mD831F89BC64E06F074275912D875BA16D18FF8BE (void);
extern void Input_set_eatKeyPressOnTextFieldFocus_m9C1646B12CA35694F5DCA534B82661C196D0E53A (void);
extern void Input_get_mousePresent_mAD77FFD987CD5B998AFAD4DAECADBC76034026BF (void);
extern void Input_get_penEventCount_m1D58E95EF58782806E49CE22CFCE4344FD74CDEE (void);
extern void Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39 (void);
extern void Input_get_touchPressureSupported_m5639F3C442612C4402AB323494066C82ECA51A91 (void);
extern void Input_get_stylusTouchSupported_mF212313893054C678976234F65BE527BEB556AEE (void);
extern void Input_get_touchSupported_m2A4FA398A793861AE1BC5971A1363552AB33BEEF (void);
extern void Input_get_multiTouchEnabled_m2A24C57718A0D5395A31FEC6B1E9A9CB98359108 (void);
extern void Input_set_multiTouchEnabled_m64EA2AE4FAFE45D8D6975F583DEC337341984571 (void);
extern void Input_get_isGyroAvailable_m9EB911D8FE425AE458A6A8AB20AD8E30A875DC7C (void);
extern void Input_get_deviceOrientation_mC6761FD3F3123DC994FCD2BFAF5BCF0D21DFE91C (void);
extern void Input_get_acceleration_m73A4104C360F0F5E590B94745137BDD78AEFC56A (void);
extern void Input_get_compensateSensors_mE9736D598882AF6DCD6AA0CADDD299EE5871A281 (void);
extern void Input_set_compensateSensors_mA19D8E9E57B0383711E5B918F6166B15FF331A3A (void);
extern void Input_get_accelerationEventCount_mEA1B14768AE6096E6FBE71CB08FF8E68CD38A007 (void);
extern void Input_get_backButtonLeavesApp_mA4449F9DEDF920AC385E072A0BF988D22510BA51 (void);
extern void Input_set_backButtonLeavesApp_m6F6F10C484FA91D9956F0301ECF99D95359483C4 (void);
extern void Input_get_location_m1D1FA0938E5B83C46A666CE78D95A58B39164DB5 (void);
extern void Input_get_compass_m7DA4657286F9B24D55BB120C9CF761A50B13899B (void);
extern void Input_GetGyroInternal_mE769DAF56501C53B38B2714742CC75E99EC696C4 (void);
extern void Input_get_gyro_m895498B803FE9A3124FBFE3C05966431F8840548 (void);
extern void Input_get_touches_m7CFDF6848F3EC3A8FE458436B2B8BD14B5C65CEF (void);
extern void Input_get_accelerationEvents_mC43F0CD5B1F62F5BF8190B6A3F28DAFAA6128C0E (void);
extern void Input_CheckDisabled_m359B281F7F5DDAB74780E1898311AECD9B0ECCE1 (void);
extern void Input__ctor_m4BC610B94D57523F9F932A0A320BBD1CF95368BA (void);
extern void Input_GetTouch_Injected_m04E25DD035583531339AB310FBDD4F5A30817F87 (void);
extern void Input_GetPenEvent_Injected_m3F22C0423AA0C6DDB6F31EE0A186428EFA8A3C54 (void);
extern void Input_GetLastPenContactEvent_Injected_mA7F2DE408F86C38FC15357E468645CD82D38A84A (void);
extern void Input_GetAccelerationEvent_Injected_m50F3F90193AFFC093898F1BE51C6D9336D5956D6 (void);
extern void Input_SimulateTouchInternal_Injected_mB02BFE48DD1AC07C88DF7E26AD9C575E8575AEDE (void);
extern void Input_get_mousePosition_Injected_m7EF43ADB535051F9182A366CA84951F946984E1A (void);
extern void Input_get_mouseScrollDelta_Injected_m31BF633C98E1BBA4583E7FCE0573BDECB1BA4A29 (void);
extern void Input_get_compositionCursorPos_Injected_m67C1CB8A21F4708CA76FAB39E3BC436DE33C214E (void);
extern void Input_set_compositionCursorPos_Injected_m46E4934CD2A9F2E97B8A86D52169C848EF6D91E8 (void);
extern void Input_get_acceleration_Injected_m09D52B38221B9D246F7DA233E0350C3FA5855C2A (void);
extern void SendMouseEvents_UpdateMouse_m7EC9A21B75612D3AA9ECEE2BB142A27481147FF1 (void);
extern void SendMouseEvents_SetMouseMoved_mDA82278267CC62E9942C9D6154610AD7F3308B51 (void);
extern void SendMouseEvents_DoSendMouseEvents_m17FCC3A684C7BC4A7A6AA7EBB62E3F56AAB416A7 (void);
extern void SendMouseEvents_SendEvents_m3DA609154485AAA0F9501BAA602F63A9E357D35C (void);
extern void SendMouseEvents__ctor_mF1452CA40C88A62C79212442456D7F54A10EF88B (void);
extern void SendMouseEvents__cctor_m6B1E043BF3142442AC8312E9B28A54C487A5A755 (void);
extern void HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541 (void);
extern void HitInfo_op_Implicit_m4162F5E6640E1D2CB82AB0AE00090AB46CE997AC (void);
extern void HitInfo_Compare_m374F9DF7CFE9C31264CD38D42FFFCA4DB0E6CD05 (void);
extern void InputUnsafeUtility_GetKeyString_m979A0145CF1B37BED79657AC3E42103E9842E41C (void);
extern void InputUnsafeUtility_GetKeyString__Unmanaged_mD736022C3E6C98ED4B4AEA824AC2DA98A3A1B223 (void);
extern void InputUnsafeUtility_GetKeyUpString_m2719D27190937E8153412F222D02FBE5E07FF315 (void);
extern void InputUnsafeUtility_GetKeyUpString__Unmanaged_mFD0D4ED74B64E565CB3F543CBBE38F6AE84AC27A (void);
extern void InputUnsafeUtility_GetKeyDownString_mE43ABE7F9E5C458B03A2CB535089C7C6E5060F41 (void);
extern void InputUnsafeUtility_GetKeyDownString__Unmanaged_m53E2CCD225AC1CE7CB40002403EDC5AEF266EB8D (void);
extern void InputUnsafeUtility_GetAxis_m034889C00C914572B94F091E8EC646E86408A29F (void);
extern void InputUnsafeUtility_GetAxis__Unmanaged_m288AEE066E04CCFDD229629882CA42DBD1D85CF2 (void);
extern void InputUnsafeUtility_GetAxisRaw_m0EBF75200CDA664FCCD51BDB4C8C8798CDD6097B (void);
extern void InputUnsafeUtility_GetAxisRaw__Unmanaged_m31953D955E75028D274A75661CAB2E13E13320AE (void);
extern void InputUnsafeUtility_GetButton_mD23EF9339E147CD6081BC4A6B45F10EE5461CEE2 (void);
extern void InputUnsafeUtility_GetButton__Unmanaged_m270892EB7D8C88443200DF972F76076AC28952B2 (void);
extern void InputUnsafeUtility_GetButtonDown_mB35B46B92DA28196820B996E2420E5E320088EE4 (void);
extern void InputUnsafeUtility_GetButtonDown__Unmanaged_m411E8743768382A33B3778E734AF072F8DBDECC4 (void);
extern void InputUnsafeUtility_GetButtonUp_m3C83507077BC4A729502BF8D2677CAE6B8344905 (void);
extern void InputUnsafeUtility_GetButtonUp__Unmanaged_mCCE34158CE05432541102B1C2AEC6545E25D0732 (void);
static Il2CppMethodPointer s_methodPointers[194] = 
{
	Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD,
	Touch_set_fingerId_m8BBCD294D98305D705F6515EB6EB6A302B23E543,
	Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A,
	Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B,
	Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D,
	Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B,
	Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299,
	Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A,
	Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC,
	Touch_set_deltaTime_m184C925F328EBD11ACD9D0B2969A7AECB09BA429,
	Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6,
	Touch_set_tapCount_m5F356E1CF7FF9DD41D37AD5D59C0650FC5771E36,
	Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0,
	Touch_set_phase_m47EEB383DD6F9EC6C4840B1F145D6818FCEBBD93,
	Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C,
	Touch_set_pressure_m2D9F9D813ECC29883196988E1127DEC93EC83C78,
	Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE,
	Touch_set_maximumPossiblePressure_m9C821DDC85966E5E009EC9A039A6FD9B5511E841,
	Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745,
	Touch_set_type_mA97992E6B2C00207DF82CA2184DE6B4F22A7BEC1,
	Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391,
	Touch_set_altitudeAngle_mBB5987AA6F0CCBF4E48BBC004474F7AA70F430F0,
	Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490,
	Touch_set_azimuthAngle_m076DE0C898C8C61F3CF4E1A5BA2D60F8FF66B87C,
	Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960,
	Touch_set_radius_m50765FCA177E136896A04E640403CE1C1BE4F1FC,
	Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21,
	Touch_set_radiusVariance_mD579FB863378B3774D71C87886D3D1F26B3BF4B9,
	AccelerationEvent_get_acceleration_m3F898B68161B923DD2EE28BE4680261C273CD783,
	AccelerationEvent_get_deltaTime_m7493D24DE20EE02573BFEC898FBD3E808A20C2AE,
	Gyroscope__ctor_mC476EA849DBF7705A36094F4A2609B5F6286408F,
	Gyroscope_rotationRate_Internal_mC69243A53ADC6720409577DA04DAE4C573F9498D,
	Gyroscope_rotationRateUnbiased_Internal_m4E9399AE2BEE46D5998CA2103F5BCE545E7DCEC3,
	Gyroscope_gravity_Internal_m4FE5E5B9F0F9906129BEC2489FF7A8DB137BB303,
	Gyroscope_userAcceleration_Internal_mAA738C8CCED8880648951AA48BD1EB2434CACADD,
	Gyroscope_attitude_Internal_mE673BB2C9BDCD791AD249FF307C10F83DCC5F3D2,
	Gyroscope_getEnabled_Internal_m69827470009782D6CC88AA7F9EEAC66A3E5C3BCE,
	Gyroscope_setEnabled_Internal_mBA928BFDE5565840E372846AF9E0D0E45AD7D115,
	Gyroscope_getUpdateInterval_Internal_mFFF394E36CEF917BD5357FC99E3AEE31516FBDE4,
	Gyroscope_setUpdateInterval_Internal_m9E08181F8BF28707553BFB77A2B7697C7E33DB6D,
	Gyroscope_get_rotationRate_mCF8F2D040B77A6C147092302C80C8DEC39918954,
	Gyroscope_get_rotationRateUnbiased_m6B54A5F9A866E1F5005EA8B1575607DF2F3AB7A3,
	Gyroscope_get_gravity_m33AEF129216C2ECF5289C06B795FCA0EECEBE1BA,
	Gyroscope_get_userAcceleration_m85C280EFA252EAD6C566811235033FEE76654637,
	Gyroscope_get_attitude_mF6D8131ED2D0E5BF979C7FC4AAC99E87A01CBE85,
	Gyroscope_get_enabled_m10F5B3F646AB1A6EEE2831010642E9E1E0BCBDB9,
	Gyroscope_set_enabled_m2B22BC93369BA61034A80350405FE1B493822DAB,
	Gyroscope_get_updateInterval_mBB5A5F3CC5556384DCE0A5BB4485FB37D1917C5D,
	Gyroscope_set_updateInterval_m477CB8AF6D656813C14467CCB62EDC3BF1383925,
	Gyroscope_rotationRate_Internal_Injected_mDE2FBCB9A01D69CED09AF11FA0958E0EA6B0E5DF,
	Gyroscope_rotationRateUnbiased_Internal_Injected_m1BEF4BAAC9DF68F7C8858E504D4F15CBCA92B63B,
	Gyroscope_gravity_Internal_Injected_m71D25DBC8914B61C552F2AFA5329C097F768576A,
	Gyroscope_userAcceleration_Internal_Injected_mA8306BBF901F1D2F9CB443CE00BAB3E0F7AC3F35,
	Gyroscope_attitude_Internal_Injected_m550F81250B94FE9B11049C8F49DE2F8B9061A2AC,
	LocationInfo_get_latitude_mAF0A46443555AF18EA3C516292CB92B1669CA863,
	LocationInfo_get_longitude_mCB720DD0E139B7C614F78D40595E1BBF1F5433A2,
	LocationInfo_get_altitude_m3B4BE2F447F3599F5677DD72A86C3A1A801ABAE0,
	LocationInfo_get_horizontalAccuracy_m3178154DD5F4F72B8743665F33CCAE5BEC6296F4,
	LocationInfo_get_verticalAccuracy_m90B3477474136BE858810B92C2C601BBB311A16B,
	LocationInfo_get_timestamp_mAD1095E60A61DF0FA66195AA06A0F801CF9BDC9F,
	LocationService_IsServiceEnabledByUser_m88DF3FEBB0B816F0D90E4972630D95714E5DDEDF,
	LocationService_GetLocationStatus_m1BE18671C8A455141C877979259EB9BA546A9103,
	LocationService_GetLastLocation_m50BFC921ADF32DD69D00937B44131B0770D4F662,
	LocationService_SetDesiredAccuracy_m6F69C809996908CD95C17A6BD96D216667FD5B58,
	LocationService_SetDistanceFilter_m847B85271A56F9291337C79E6C80012F2BD6C1AE,
	LocationService_StartUpdatingLocation_m93789B3CDDF62EBD26326127DEC65C6C94123D29,
	LocationService_StopUpdatingLocation_mB063A5546139211C3796E0422365829ABBB26C20,
	LocationService_GetLastHeading_m4ED66327647F8DA6CFCB895859CCDD5952276BEA,
	LocationService_IsHeadingUpdatesEnabled_mC0885519051CEF5C9387EDC7FCED32C774B5C873,
	LocationService_SetHeadingUpdatesEnabled_m77B8BAC72D790DF0B03CC97AA3CA1863F657B9A7,
	LocationService_get_isEnabledByUser_m78FE3A38B1101BAF658E259C1A6B4CD9A008DCAE,
	LocationService_get_status_m25B7C4012B9529265D9746BB73ED689737E9C9CD,
	LocationService_get_lastData_m50C68F3DFB23894CF519F00BBA9D72146EE9345F,
	LocationService_Start_m5076FE201E96C086B0F9C2D8677DA69C98099F3D,
	LocationService_Start_m9415AA1CC6D8E4C450C6CF1C9D32BFBCF44C1F12,
	LocationService_Start_mC2A90619923D4BEB5F9B5CCD95F317B98D67AF3A,
	LocationService_Stop_mB9332CB653E7A7CE6AE07240EA6C0B6C9AEC0D96,
	LocationService__ctor_mCA7E0A5F2303FE28CE83787FEE9C5A020195E6DC,
	LocationService_GetLastLocation_Injected_m2463D5B46B8E9CA2C10AB3E611CD176BE7C3D472,
	LocationService_GetLastHeading_Injected_m6BC975D8809617948C105507082D3273FBDF0539,
	Compass_get_magneticHeading_m5052F97DD5B914F629B138956D250AE2EFFE1C55,
	Compass_get_trueHeading_m5546F74294A5CC2B4A731ECE1E02F0BE7085582C,
	Compass_get_headingAccuracy_mED82316932D27BCF4C30297B3D97169E49A8DBFF,
	Compass_get_rawVector_mC6A8DB056776CA677F9D573BF8415241BC116591,
	Compass_get_timestamp_m3D2E1DB4FF2F73FA62DA8DD2655DB7D402BC0346,
	Compass_get_enabled_mA531BD367FE1E72DF6FCB3DBA21ECDDBFE3EBA56,
	Compass_set_enabled_m26DFB64F789DA4B875359E8CE1C3E9E2270CFCA9,
	Compass__ctor_mC0A067ED82089D178EC3E1CE6311B85366F33D35,
	CameraRaycastHelper_RaycastTry_m79A654495BD2C09623E9067BCC70D23A0DA3BF58,
	CameraRaycastHelper_RaycastTry2D_m132832B9171CD030AD231A63BF70D1226ED1F373,
	CameraRaycastHelper__ctor_mA1A7E770622B10806FD65CD3DB0A6A19191E306D,
	CameraRaycastHelper_RaycastTry_Injected_m4A9EA285FB7B24B7B3D894E7EE997B41ED302DEF,
	CameraRaycastHelper_RaycastTry2D_Injected_m2620821FE8CB793C314AAE43E3B4C7BEAE5D4C9E,
	Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62,
	Input_GetAxisRaw_m47C0CF8E090561A2F407A4E11D5F2A45044EB8E4,
	Input_GetButton_m2F217DAE69DB3D1324FB848B3C9C84F19A80989E,
	Input_GetButtonDown_mEF5F80C9E8F04104E807D9CBD6F70CDB98751579,
	Input_GetButtonUp_mEE713E86F1A024762EFED092BC59F3478E786601,
	Input_GetKeyInt_m2FFCC49AF36B74247CC1B412E9787A15D0984E95,
	Input_GetKeyUpInt_mB26B433DD3A21ACAF04D23252B09068EFFEDA0F9,
	Input_GetKeyDownInt_m0B655F969FCBC011BC2616E3E5A657CF7D76568A,
	Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA,
	Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3,
	Input_GetMouseButtonUp_mBE89CC9C69BBEA9A863819E77EA54411B0476ED6,
	Input_ResetInputAxes_mB5A22E8AFB4F27164387B5AAE8EFBF796B06EF8C,
	Input_GetJoystickNames_m506FC5C5D06CE7A15EBB9ACEC9DCF546E2DDCC0B,
	Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1,
	Input_GetPenEvent_mFCFFB0288585085BB212A69BC42381B5235FE5DE,
	Input_GetLastPenContactEvent_mAB37B70407FDE3DE8230B55D3D4828DC50EAF0BF,
	Input_ResetPenEvents_m10EDA2C5C566DB6AEA7CE3D81A553D4D5CE797C8,
	Input_ClearLastPenContactEvent_m3240C9BA6CE089E85624BD0FA031A48CDE2664A3,
	Input_GetAccelerationEvent_mA4488CE59AE0F4D5518A8B9BFF861B13FC7C5D56,
	Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434,
	Input_GetKey_m8D6171F09AC784866255D2634A3986A75644BE6C,
	Input_GetKeyUp_m9A962E395811A9901E7E05F267E198A533DBEF2F,
	Input_GetKeyUp_m504C6CF9A40BD840964AD0495266CD003676289E,
	Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2,
	Input_GetKeyDown_m789DB780D0567DCC23B501D15AABD4F2E3591A3F,
	Input_SimulateTouch_mAB41EA2CE74B94F069216FDE8D75DC3735115629,
	Input_SimulateTouchInternal_m4E438A4F030156CFCF0C9C064C240202023370A3,
	Input_get_simulateMouseWithTouches_mA95D11052F78FD2E8D253E6790968CEFA0E98B84,
	Input_set_simulateMouseWithTouches_m1D86DBF4BDCAA4AF3F15EE2BD51FB4C215D2AC6E,
	Input_get_anyKey_m6FF00BB4F01FA996E2FB3F3727EBC414C8EB2CEB,
	Input_get_anyKeyDown_m9B8D98B5574A2AA63CEED98FBF0E2A1D946BCA71,
	Input_get_inputString_mB022DED50E8838B8319C591830BEB21E4904561B,
	Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C,
	Input_get_mouseScrollDelta_mD112408E9182AA0F529179FF31E21D8DCD5CFA74,
	Input_get_imeCompositionMode_mAD9C0224B3845A9132D4265AF468FF203AA43BAC,
	Input_set_imeCompositionMode_m0399964447DDFE54E04F516A01696862F7174C9A,
	Input_get_compositionString_mC9E603E4FB61090827F77A3D509BF3AA0A48C9A9,
	Input_get_imeIsSelected_m4A47A74A3CB95CCC1A8B8498E67C87205EF73D0D,
	Input_get_compositionCursorPos_mE1E48997CA0C30D206D08FAF06455123D8D24D15,
	Input_set_compositionCursorPos_m16A856BFBF1DAE42B0089696906F530334861E98,
	Input_get_eatKeyPressOnTextFieldFocus_mD831F89BC64E06F074275912D875BA16D18FF8BE,
	Input_set_eatKeyPressOnTextFieldFocus_m9C1646B12CA35694F5DCA534B82661C196D0E53A,
	Input_get_mousePresent_mAD77FFD987CD5B998AFAD4DAECADBC76034026BF,
	Input_get_penEventCount_m1D58E95EF58782806E49CE22CFCE4344FD74CDEE,
	Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39,
	Input_get_touchPressureSupported_m5639F3C442612C4402AB323494066C82ECA51A91,
	Input_get_stylusTouchSupported_mF212313893054C678976234F65BE527BEB556AEE,
	Input_get_touchSupported_m2A4FA398A793861AE1BC5971A1363552AB33BEEF,
	Input_get_multiTouchEnabled_m2A24C57718A0D5395A31FEC6B1E9A9CB98359108,
	Input_set_multiTouchEnabled_m64EA2AE4FAFE45D8D6975F583DEC337341984571,
	Input_get_isGyroAvailable_m9EB911D8FE425AE458A6A8AB20AD8E30A875DC7C,
	Input_get_deviceOrientation_mC6761FD3F3123DC994FCD2BFAF5BCF0D21DFE91C,
	Input_get_acceleration_m73A4104C360F0F5E590B94745137BDD78AEFC56A,
	Input_get_compensateSensors_mE9736D598882AF6DCD6AA0CADDD299EE5871A281,
	Input_set_compensateSensors_mA19D8E9E57B0383711E5B918F6166B15FF331A3A,
	Input_get_accelerationEventCount_mEA1B14768AE6096E6FBE71CB08FF8E68CD38A007,
	Input_get_backButtonLeavesApp_mA4449F9DEDF920AC385E072A0BF988D22510BA51,
	Input_set_backButtonLeavesApp_m6F6F10C484FA91D9956F0301ECF99D95359483C4,
	Input_get_location_m1D1FA0938E5B83C46A666CE78D95A58B39164DB5,
	Input_get_compass_m7DA4657286F9B24D55BB120C9CF761A50B13899B,
	Input_GetGyroInternal_mE769DAF56501C53B38B2714742CC75E99EC696C4,
	Input_get_gyro_m895498B803FE9A3124FBFE3C05966431F8840548,
	Input_get_touches_m7CFDF6848F3EC3A8FE458436B2B8BD14B5C65CEF,
	Input_get_accelerationEvents_mC43F0CD5B1F62F5BF8190B6A3F28DAFAA6128C0E,
	Input_CheckDisabled_m359B281F7F5DDAB74780E1898311AECD9B0ECCE1,
	Input__ctor_m4BC610B94D57523F9F932A0A320BBD1CF95368BA,
	Input_GetTouch_Injected_m04E25DD035583531339AB310FBDD4F5A30817F87,
	Input_GetPenEvent_Injected_m3F22C0423AA0C6DDB6F31EE0A186428EFA8A3C54,
	Input_GetLastPenContactEvent_Injected_mA7F2DE408F86C38FC15357E468645CD82D38A84A,
	Input_GetAccelerationEvent_Injected_m50F3F90193AFFC093898F1BE51C6D9336D5956D6,
	Input_SimulateTouchInternal_Injected_mB02BFE48DD1AC07C88DF7E26AD9C575E8575AEDE,
	Input_get_mousePosition_Injected_m7EF43ADB535051F9182A366CA84951F946984E1A,
	Input_get_mouseScrollDelta_Injected_m31BF633C98E1BBA4583E7FCE0573BDECB1BA4A29,
	Input_get_compositionCursorPos_Injected_m67C1CB8A21F4708CA76FAB39E3BC436DE33C214E,
	Input_set_compositionCursorPos_Injected_m46E4934CD2A9F2E97B8A86D52169C848EF6D91E8,
	Input_get_acceleration_Injected_m09D52B38221B9D246F7DA233E0350C3FA5855C2A,
	SendMouseEvents_UpdateMouse_m7EC9A21B75612D3AA9ECEE2BB142A27481147FF1,
	SendMouseEvents_SetMouseMoved_mDA82278267CC62E9942C9D6154610AD7F3308B51,
	SendMouseEvents_DoSendMouseEvents_m17FCC3A684C7BC4A7A6AA7EBB62E3F56AAB416A7,
	SendMouseEvents_SendEvents_m3DA609154485AAA0F9501BAA602F63A9E357D35C,
	SendMouseEvents__ctor_mF1452CA40C88A62C79212442456D7F54A10EF88B,
	SendMouseEvents__cctor_m6B1E043BF3142442AC8312E9B28A54C487A5A755,
	HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541,
	HitInfo_op_Implicit_m4162F5E6640E1D2CB82AB0AE00090AB46CE997AC,
	HitInfo_Compare_m374F9DF7CFE9C31264CD38D42FFFCA4DB0E6CD05,
	InputUnsafeUtility_GetKeyString_m979A0145CF1B37BED79657AC3E42103E9842E41C,
	InputUnsafeUtility_GetKeyString__Unmanaged_mD736022C3E6C98ED4B4AEA824AC2DA98A3A1B223,
	InputUnsafeUtility_GetKeyUpString_m2719D27190937E8153412F222D02FBE5E07FF315,
	InputUnsafeUtility_GetKeyUpString__Unmanaged_mFD0D4ED74B64E565CB3F543CBBE38F6AE84AC27A,
	InputUnsafeUtility_GetKeyDownString_mE43ABE7F9E5C458B03A2CB535089C7C6E5060F41,
	InputUnsafeUtility_GetKeyDownString__Unmanaged_m53E2CCD225AC1CE7CB40002403EDC5AEF266EB8D,
	InputUnsafeUtility_GetAxis_m034889C00C914572B94F091E8EC646E86408A29F,
	InputUnsafeUtility_GetAxis__Unmanaged_m288AEE066E04CCFDD229629882CA42DBD1D85CF2,
	InputUnsafeUtility_GetAxisRaw_m0EBF75200CDA664FCCD51BDB4C8C8798CDD6097B,
	InputUnsafeUtility_GetAxisRaw__Unmanaged_m31953D955E75028D274A75661CAB2E13E13320AE,
	InputUnsafeUtility_GetButton_mD23EF9339E147CD6081BC4A6B45F10EE5461CEE2,
	InputUnsafeUtility_GetButton__Unmanaged_m270892EB7D8C88443200DF972F76076AC28952B2,
	InputUnsafeUtility_GetButtonDown_mB35B46B92DA28196820B996E2420E5E320088EE4,
	InputUnsafeUtility_GetButtonDown__Unmanaged_m411E8743768382A33B3778E734AF072F8DBDECC4,
	InputUnsafeUtility_GetButtonUp_m3C83507077BC4A729502BF8D2677CAE6B8344905,
	InputUnsafeUtility_GetButtonUp__Unmanaged_mCCE34158CE05432541102B1C2AEC6545E25D0732,
};
extern void Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD_AdjustorThunk (void);
extern void Touch_set_fingerId_m8BBCD294D98305D705F6515EB6EB6A302B23E543_AdjustorThunk (void);
extern void Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A_AdjustorThunk (void);
extern void Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B_AdjustorThunk (void);
extern void Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D_AdjustorThunk (void);
extern void Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B_AdjustorThunk (void);
extern void Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299_AdjustorThunk (void);
extern void Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A_AdjustorThunk (void);
extern void Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC_AdjustorThunk (void);
extern void Touch_set_deltaTime_m184C925F328EBD11ACD9D0B2969A7AECB09BA429_AdjustorThunk (void);
extern void Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6_AdjustorThunk (void);
extern void Touch_set_tapCount_m5F356E1CF7FF9DD41D37AD5D59C0650FC5771E36_AdjustorThunk (void);
extern void Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0_AdjustorThunk (void);
extern void Touch_set_phase_m47EEB383DD6F9EC6C4840B1F145D6818FCEBBD93_AdjustorThunk (void);
extern void Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C_AdjustorThunk (void);
extern void Touch_set_pressure_m2D9F9D813ECC29883196988E1127DEC93EC83C78_AdjustorThunk (void);
extern void Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE_AdjustorThunk (void);
extern void Touch_set_maximumPossiblePressure_m9C821DDC85966E5E009EC9A039A6FD9B5511E841_AdjustorThunk (void);
extern void Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745_AdjustorThunk (void);
extern void Touch_set_type_mA97992E6B2C00207DF82CA2184DE6B4F22A7BEC1_AdjustorThunk (void);
extern void Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391_AdjustorThunk (void);
extern void Touch_set_altitudeAngle_mBB5987AA6F0CCBF4E48BBC004474F7AA70F430F0_AdjustorThunk (void);
extern void Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490_AdjustorThunk (void);
extern void Touch_set_azimuthAngle_m076DE0C898C8C61F3CF4E1A5BA2D60F8FF66B87C_AdjustorThunk (void);
extern void Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960_AdjustorThunk (void);
extern void Touch_set_radius_m50765FCA177E136896A04E640403CE1C1BE4F1FC_AdjustorThunk (void);
extern void Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21_AdjustorThunk (void);
extern void Touch_set_radiusVariance_mD579FB863378B3774D71C87886D3D1F26B3BF4B9_AdjustorThunk (void);
extern void AccelerationEvent_get_acceleration_m3F898B68161B923DD2EE28BE4680261C273CD783_AdjustorThunk (void);
extern void AccelerationEvent_get_deltaTime_m7493D24DE20EE02573BFEC898FBD3E808A20C2AE_AdjustorThunk (void);
extern void LocationInfo_get_latitude_mAF0A46443555AF18EA3C516292CB92B1669CA863_AdjustorThunk (void);
extern void LocationInfo_get_longitude_mCB720DD0E139B7C614F78D40595E1BBF1F5433A2_AdjustorThunk (void);
extern void LocationInfo_get_altitude_m3B4BE2F447F3599F5677DD72A86C3A1A801ABAE0_AdjustorThunk (void);
extern void LocationInfo_get_horizontalAccuracy_m3178154DD5F4F72B8743665F33CCAE5BEC6296F4_AdjustorThunk (void);
extern void LocationInfo_get_verticalAccuracy_m90B3477474136BE858810B92C2C601BBB311A16B_AdjustorThunk (void);
extern void LocationInfo_get_timestamp_mAD1095E60A61DF0FA66195AA06A0F801CF9BDC9F_AdjustorThunk (void);
extern void HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[37] = 
{
	{ 0x06000001, Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD_AdjustorThunk },
	{ 0x06000002, Touch_set_fingerId_m8BBCD294D98305D705F6515EB6EB6A302B23E543_AdjustorThunk },
	{ 0x06000003, Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A_AdjustorThunk },
	{ 0x06000004, Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B_AdjustorThunk },
	{ 0x06000005, Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D_AdjustorThunk },
	{ 0x06000006, Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B_AdjustorThunk },
	{ 0x06000007, Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299_AdjustorThunk },
	{ 0x06000008, Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A_AdjustorThunk },
	{ 0x06000009, Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC_AdjustorThunk },
	{ 0x0600000A, Touch_set_deltaTime_m184C925F328EBD11ACD9D0B2969A7AECB09BA429_AdjustorThunk },
	{ 0x0600000B, Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6_AdjustorThunk },
	{ 0x0600000C, Touch_set_tapCount_m5F356E1CF7FF9DD41D37AD5D59C0650FC5771E36_AdjustorThunk },
	{ 0x0600000D, Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0_AdjustorThunk },
	{ 0x0600000E, Touch_set_phase_m47EEB383DD6F9EC6C4840B1F145D6818FCEBBD93_AdjustorThunk },
	{ 0x0600000F, Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C_AdjustorThunk },
	{ 0x06000010, Touch_set_pressure_m2D9F9D813ECC29883196988E1127DEC93EC83C78_AdjustorThunk },
	{ 0x06000011, Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE_AdjustorThunk },
	{ 0x06000012, Touch_set_maximumPossiblePressure_m9C821DDC85966E5E009EC9A039A6FD9B5511E841_AdjustorThunk },
	{ 0x06000013, Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745_AdjustorThunk },
	{ 0x06000014, Touch_set_type_mA97992E6B2C00207DF82CA2184DE6B4F22A7BEC1_AdjustorThunk },
	{ 0x06000015, Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391_AdjustorThunk },
	{ 0x06000016, Touch_set_altitudeAngle_mBB5987AA6F0CCBF4E48BBC004474F7AA70F430F0_AdjustorThunk },
	{ 0x06000017, Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490_AdjustorThunk },
	{ 0x06000018, Touch_set_azimuthAngle_m076DE0C898C8C61F3CF4E1A5BA2D60F8FF66B87C_AdjustorThunk },
	{ 0x06000019, Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960_AdjustorThunk },
	{ 0x0600001A, Touch_set_radius_m50765FCA177E136896A04E640403CE1C1BE4F1FC_AdjustorThunk },
	{ 0x0600001B, Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21_AdjustorThunk },
	{ 0x0600001C, Touch_set_radiusVariance_mD579FB863378B3774D71C87886D3D1F26B3BF4B9_AdjustorThunk },
	{ 0x0600001D, AccelerationEvent_get_acceleration_m3F898B68161B923DD2EE28BE4680261C273CD783_AdjustorThunk },
	{ 0x0600001E, AccelerationEvent_get_deltaTime_m7493D24DE20EE02573BFEC898FBD3E808A20C2AE_AdjustorThunk },
	{ 0x06000037, LocationInfo_get_latitude_mAF0A46443555AF18EA3C516292CB92B1669CA863_AdjustorThunk },
	{ 0x06000038, LocationInfo_get_longitude_mCB720DD0E139B7C614F78D40595E1BBF1F5433A2_AdjustorThunk },
	{ 0x06000039, LocationInfo_get_altitude_m3B4BE2F447F3599F5677DD72A86C3A1A801ABAE0_AdjustorThunk },
	{ 0x0600003A, LocationInfo_get_horizontalAccuracy_m3178154DD5F4F72B8743665F33CCAE5BEC6296F4_AdjustorThunk },
	{ 0x0600003B, LocationInfo_get_verticalAccuracy_m90B3477474136BE858810B92C2C601BBB311A16B_AdjustorThunk },
	{ 0x0600003C, LocationInfo_get_timestamp_mAD1095E60A61DF0FA66195AA06A0F801CF9BDC9F_AdjustorThunk },
	{ 0x060000B0, HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541_AdjustorThunk },
};
static const int32_t s_InvokerIndices[194] = 
{
	4216,
	3852,
	4356,
	3978,
	4356,
	3978,
	4356,
	3978,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4358,
	4298,
	3852,
	8835,
	8835,
	8835,
	8835,
	8573,
	8216,
	7926,
	8622,
	7934,
	4358,
	4358,
	4358,
	4358,
	4267,
	4168,
	3807,
	4298,
	3928,
	7925,
	7925,
	7925,
	7925,
	7925,
	4298,
	4298,
	4298,
	4298,
	4298,
	4190,
	8993,
	9018,
	9023,
	8899,
	8899,
	9089,
	9089,
	9100,
	8993,
	8868,
	4168,
	4216,
	4236,
	2850,
	3928,
	4364,
	4364,
	4364,
	8867,
	8867,
	4298,
	4298,
	4298,
	4358,
	4190,
	4168,
	3807,
	4364,
	5962,
	5962,
	4364,
	5923,
	5923,
	8626,
	8626,
	8220,
	8220,
	8220,
	8216,
	8216,
	8216,
	8216,
	8216,
	8216,
	9089,
	9031,
	8761,
	8536,
	9033,
	9089,
	9089,
	8161,
	8216,
	8220,
	8216,
	8220,
	8216,
	8220,
	8902,
	8017,
	8993,
	8868,
	8993,
	8993,
	9031,
	9086,
	9084,
	9018,
	8880,
	9031,
	8993,
	9084,
	8907,
	8993,
	8868,
	8993,
	9018,
	9018,
	8993,
	8993,
	8993,
	8993,
	8868,
	8993,
	9018,
	9086,
	8993,
	8868,
	9018,
	8993,
	8868,
	9031,
	9031,
	9018,
	9031,
	9031,
	9031,
	8993,
	4364,
	7925,
	7925,
	8867,
	7925,
	7898,
	8867,
	8867,
	8867,
	8867,
	8867,
	9089,
	9089,
	8880,
	7937,
	4364,
	9089,
	3881,
	8251,
	7374,
	8220,
	7144,
	8220,
	7144,
	8220,
	7144,
	8626,
	7727,
	8626,
	7727,
	8220,
	7144,
	8220,
	7144,
	8220,
	7144,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_InputLegacyModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule = 
{
	"UnityEngine.InputLegacyModule.dll",
	194,
	s_methodPointers,
	37,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_InputLegacyModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
