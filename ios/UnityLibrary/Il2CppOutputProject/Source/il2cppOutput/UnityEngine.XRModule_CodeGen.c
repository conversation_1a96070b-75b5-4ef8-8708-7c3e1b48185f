﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void InputTracking_add_trackingAcquired_mB4BFD78603798E38C0915B6E9E49DBF2E3EEE3E4 (void);
extern void InputTracking_remove_trackingAcquired_m610A04BE4915CA2FA429810C607C42CF24792816 (void);
extern void InputTracking_add_trackingLost_mE5935FB7C709D54B2F38B94673451EDCB188EEBF (void);
extern void InputTracking_remove_trackingLost_m48A73CF7CA715230A1760E473AC30CEB33E16893 (void);
extern void InputTracking_add_nodeAdded_mEF63261745947F87908B9C23380E2354BC3EA3F7 (void);
extern void InputTracking_remove_nodeAdded_m73C678FB4049A38E7F66635CC810FF7D989A3<PERSON><PERSON> (void);
extern void InputTracking_add_nodeRemoved_mBAEA16F632E4F5E173B4DCB604BBA1C3A1BFFA60 (void);
extern void InputTracking_remove_nodeRemoved_m8E2532C10663D71663CFD2F2959B4B4865139DBB (void);
extern void InputTracking_InvokeTrackingEvent_mA218CBE5D81A639B9C9A084A5360FEAD4625C42C (void);
extern void InputTracking_GetLocalPosition_m0753549019EB2F0675886E94F237DA9E456EC31D (void);
extern void InputTracking_GetLocalRotation_m25C883320022BBF4B0F2DF6B1449CDB69DCF7E5A (void);
extern void InputTracking_Recenter_m1843AECFE9093882149E6B197E6EFEB6151E6C7C (void);
extern void InputTracking_GetNodeName_m4E82799BB255A098F05DF2A7550FF5D4D6F48911 (void);
extern void InputTracking_GetNodeStates_mA2E8D154A47C817ED74AD42F6B38A9C906A57A67 (void);
extern void InputTracking_GetNodeStates_Internal_m6E67CE5EC9C950ED633AEC42F8BD5B15EFE3B916 (void);
extern void InputTracking_get_disablePositionalTracking_m91D5AF9BE2C47A2209D2ADC8D9DDC49B2EA072A2 (void);
extern void InputTracking_set_disablePositionalTracking_mC4C0EC50561442893A070430991FCCC35666E727 (void);
extern void InputTracking_GetDeviceIdAtXRNode_mF6AA8F79B81E685890712AE72FADC4D1113CDECC (void);
extern void InputTracking_GetDeviceIdsAtXRNode_Internal_m982E266A1E40EFA04380D0D1A5E91CBB09584389 (void);
extern void InputTracking_GetLocalPosition_Injected_mC244C17675072F3828580AA42BA8BC8FB84CC4A7 (void);
extern void InputTracking_GetLocalRotation_Injected_m78D7134A157A6905B75999DF5EE086B093A75933 (void);
extern void XRNodeState_get_uniqueID_m5776ABB4A4516A1DD61A4BECE94FF7CE09812755 (void);
extern void XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F (void);
extern void XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932 (void);
extern void XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4 (void);
extern void XRNodeState_get_tracked_m777E4EE8D1812BC9849A696EE52FFAD3F4760F79 (void);
extern void XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45 (void);
extern void XRNodeState_set_position_m4DEE96188C00F002912BA93B35976267209F4278 (void);
extern void XRNodeState_set_rotation_m233504F15F6581E219A0412A49BAB2630A060627 (void);
extern void XRNodeState_set_velocity_m0E4D32C82609E2606B360E92DDF822A8B9938AAC (void);
extern void XRNodeState_set_angularVelocity_m46D5154D7C0FEF8C3B6BE38A178753FBF475F1B7 (void);
extern void XRNodeState_set_acceleration_mA6BDB5D1ADE1A011A885A205EC9B7E41608923CA (void);
extern void XRNodeState_set_angularAcceleration_mA5C680F9CFC1D534377B7FEA7ED065023BB33844 (void);
extern void XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6 (void);
extern void XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301 (void);
extern void XRNodeState_TryGetVelocity_m558D0C6424C5F0DC98E151C0DAC61173EA4AD0C4 (void);
extern void XRNodeState_TryGetAngularVelocity_mB7C1D0876373441A07815A8CCB1FC05C11729176 (void);
extern void XRNodeState_TryGetAcceleration_m9EAEB95F9ED4B779D54F6BEF7DDFDC34B7EE6892 (void);
extern void XRNodeState_TryGetAngularAcceleration_m9D4FF5411CFB801471802F8A084B4333C62F4C04 (void);
extern void XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630 (void);
extern void XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616 (void);
extern void HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9 (void);
extern void HapticCapabilities_set_numChannels_m59424616BE7E0EEAA6B9CBBF5659DAA0FC2662D7 (void);
extern void HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C (void);
extern void HapticCapabilities_set_supportsImpulse_mEE83FC8E998D832A8A4DAD0803DB6AE42459A223 (void);
extern void HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59 (void);
extern void HapticCapabilities_set_supportsBuffer_m7E835F610102BC8DAB832FED19709FBBE3D2A701 (void);
extern void HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA (void);
extern void HapticCapabilities_set_bufferFrequencyHz_m1D3C289047D65CC38E340C07F232328C6140A75E (void);
extern void HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C (void);
extern void HapticCapabilities_set_bufferMaxSize_m1524C183E3DC90C7E938BD5CF8BE05DCE24CF3C5 (void);
extern void HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407 (void);
extern void HapticCapabilities_set_bufferOptimalSize_mA6839E1849FB8B2F5A937A7B69980B12F1F7179C (void);
extern void HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57 (void);
extern void HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F (void);
extern void HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB (void);
extern void HapticCapabilities_op_Equality_m5751D10DF2639053956854F5CA4B02E907B35436 (void);
extern void HapticCapabilities_op_Inequality_mCD17A6A196EE20B994A7391EF20E3B5CC05BCCCD (void);
extern void InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085 (void);
extern void InputFeatureUsage_set_name_mBC7F8DE6FF4A45609D62A63B5AF9AA7EB867F3C4 (void);
extern void InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810 (void);
extern void InputFeatureUsage_set_internalType_m5BB4726F3F1A94047BA41FAA1E93DAA40AD6D00F (void);
extern void InputFeatureUsage_get_type_mD9697C1BFA3EA57E89A23A06019AAB73CB24326D (void);
extern void InputFeatureUsage__ctor_m34E81E0915344257F81AC76BF38CF9AFE97D6819 (void);
extern void InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0 (void);
extern void InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D (void);
extern void InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D (void);
extern void InputFeatureUsage_op_Equality_mCA5EC7D0F4A65D02D22E45C39102EB1EFAF4B017 (void);
extern void InputFeatureUsage_op_Inequality_m15BD00AC67A5313511786BAF5E73667B38E33A6E (void);
extern void CommonUsages__cctor_mC385C864BC1092A2B00B21E9AA6A7F079B195B9C (void);
extern void InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34 (void);
extern void InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19 (void);
extern void InputDevice_get_subsystem_mF78D9FF3EC606129608E19A0B22F25A80E005FFF (void);
extern void InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948 (void);
extern void InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE (void);
extern void InputDevice_get_role_m8A5CD735B1BD8E9712A75CB734092C443452925E (void);
extern void InputDevice_get_manufacturer_mABBD3690895EC6A8FE86094CCFA4E0B081C102BB (void);
extern void InputDevice_get_serialNumber_m777847E27A8EB8871A66910132D17B2B58380B0E (void);
extern void InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB (void);
extern void InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB (void);
extern void InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B (void);
extern void InputDevice_SendHapticBuffer_m8988C8FF59C5A33B65EDA0BD7EAF4996D530C879 (void);
extern void InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3 (void);
extern void InputDevice_StopHaptics_m816C765A638F5571FD884AEED49FFA74BD535080 (void);
extern void InputDevice_TryGetFeatureUsages_mFD9F3CE1465177544260CEB32E1821AA0FDD24AC (void);
extern void InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884 (void);
extern void InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C (void);
extern void InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081 (void);
extern void InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA (void);
extern void InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167 (void);
extern void InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F (void);
extern void InputDevice_TryGetFeatureValue_mC30F276811E65A010BC493B2C7707837CF52396C (void);
extern void InputDevice_TryGetFeatureValue_m069EBD94A6A1DEB178F0DEFD4ADD328B31FCFF52 (void);
extern void InputDevice_TryGetFeatureValue_mBD7F8831DE80286ED4A5B4F55686EBD4EF8058F0 (void);
extern void InputDevice_TryGetFeatureValue_mCF4528B1552EB1B5980DAAED7287CBE83556688F (void);
extern void InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11 (void);
extern void InputDevice_TryGetFeatureValue_mAAA12B1ACD0C5EABB0AD26B8C9F52E41E98AC277 (void);
extern void InputDevice_TryGetFeatureValue_m12875D2D90E21B5F08D81E620B082854905A178D (void);
extern void InputDevice_TryGetFeatureValue_mF1ED1B13F43D27144E424503FD36CB88BB1FD4E3 (void);
extern void InputDevice_TryGetFeatureValue_mE22CFE88DBE2862117C4BFA85C6E304C17CF38B9 (void);
extern void InputDevice_TryGetFeatureValue_m14C541BA603C521379165D18DE71638E0726BE7B (void);
extern void InputDevice_TryGetFeatureValue_mC0CB4610B0A7ACA72411D0E7B8FC29EA4AEEF335 (void);
extern void InputDevice_TryGetFeatureValue_mC1E70E2DB5E12C8B0884141078038FBE22ED95DB (void);
extern void InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC (void);
extern void InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC (void);
extern void InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D (void);
extern void InputDevice_op_Equality_m57E5FF6F966E9E8E65AB3AED68ED4D099F307044 (void);
extern void InputDevice_op_Inequality_m3C29589128440555BC60B512C5C6776C3B245540 (void);
extern void TimeConverter_get_now_m9746E40FCC1F094CC6A5B3053772B22798D7CBC2 (void);
extern void TimeConverter_LocalDateTimeToUnixTimeMilliseconds_mCE04003EB92CF112677D736E98BB11BCB2A8C4B5 (void);
extern void TimeConverter_UnixTimeMillisecondsToLocalDateTime_m30F081C166D14DCDD37B60142EF7C8C86387E2C1 (void);
extern void TimeConverter__cctor_mA39D7738A61C34C0A022191DF0D3375E183CE95D (void);
extern void Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A (void);
extern void Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775 (void);
extern void Hand_TryGetRootBone_m57751D162BB60316E15C09608D4365C4C8FFB346 (void);
extern void Hand_Hand_TryGetRootBone_mFCE4EF8CA602250B3440900F0E0E207B578C1F11 (void);
extern void Hand_TryGetFingerBones_m62D184582CD2EC6FE21C6B70DB9CBEA1B4FADAE4 (void);
extern void Hand_Hand_TryGetFingerBonesAsList_mE35ECA09B8BAEB6ABED19A1290D3E7A728C7E53E (void);
extern void Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06 (void);
extern void Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864 (void);
extern void Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE (void);
extern void Hand_op_Equality_m5EFB50DDFADE7B6FDC7148A38AB825F452AA67E6 (void);
extern void Hand_op_Inequality_mC0887C4B3DAA48720699F959CA8A2823E64D3A61 (void);
extern void Hand_Hand_TryGetRootBone_Injected_mFE6861D8999D6C44446F2F35316473F14BE65025 (void);
extern void Hand_Hand_TryGetFingerBonesAsList_Injected_m2308D6F291C6F6CC6D1C05CADCCABFB0850BAF57 (void);
extern void Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD (void);
extern void Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8 (void);
extern void Eyes_TryGetLeftEyePosition_mCA6E54596518263010B4939CDA4F921E8C825036 (void);
extern void Eyes_TryGetRightEyePosition_m9DF9633DB7FDB907DAA8DBB4D46227C175003A24 (void);
extern void Eyes_TryGetLeftEyeRotation_mAE5123DBED78CC8CC6DF4D52CE281DE84F4486B9 (void);
extern void Eyes_TryGetRightEyeRotation_mE4CED1712FC85CCCD5840BABD5513FA70D72D37A (void);
extern void Eyes_Eyes_TryGetEyePosition_m3591B79F2A13ACDB6D449B392FF8964EF804E445 (void);
extern void Eyes_Eyes_TryGetEyeRotation_m1F1A4B030A7092CFC39C11903CE92AEFA81E59F7 (void);
extern void Eyes_TryGetFixationPoint_mD7E98889AA0C4920837775486B2792F1674B461F (void);
extern void Eyes_Eyes_TryGetFixationPoint_m70CBDBADA88A9C3ADADCBF765A4B7EB1635663E9 (void);
extern void Eyes_TryGetLeftEyeOpenAmount_m6F75E47258DB11053E5FAE6F1CB1B3FB77149A29 (void);
extern void Eyes_TryGetRightEyeOpenAmount_m26AD3342F50D3FA4AC1FA2252D2B7FC4F8D53B94 (void);
extern void Eyes_Eyes_TryGetEyeOpenAmount_mABEF702A3A302798A1B56003DC13082D1927832A (void);
extern void Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D (void);
extern void Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969 (void);
extern void Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8 (void);
extern void Eyes_op_Equality_m16F7DDF591696D06CF7F93F0EAED15F125D2DC30 (void);
extern void Eyes_op_Inequality_m14641754AC3F79AD482762C0B7BCE700FA8B6FD6 (void);
extern void Eyes_Eyes_TryGetEyePosition_Injected_mFDA15FE36C4BA408456EE9AB0506B18346C9C888 (void);
extern void Eyes_Eyes_TryGetEyeRotation_Injected_m14A277758AE782F3AC70F6024FEA832789FFFFC1 (void);
extern void Eyes_Eyes_TryGetFixationPoint_Injected_m039934F8C688269A2683CC2F93148194B5DEF7FB (void);
extern void Eyes_Eyes_TryGetEyeOpenAmount_Injected_m2F48ADDC7E8A12E002E3643916901249F580A013 (void);
extern void Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1 (void);
extern void Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE (void);
extern void Bone_TryGetPosition_m995E6BD44A081407B8FA2B4BB1B6223C1526E8D7 (void);
extern void Bone_Bone_TryGetPosition_m456BB5F8E230160FC465158AD3063980F0225AB8 (void);
extern void Bone_TryGetRotation_mBCD1D06C58E9C28F9C8F2A24A454D6B90ED6EB65 (void);
extern void Bone_Bone_TryGetRotation_m9D11366D142C8771974FCCD06B36D4BBB7CFF1F8 (void);
extern void Bone_TryGetParentBone_m7E683151870A5D22C319C89CBA9577D4CADDFF85 (void);
extern void Bone_Bone_TryGetParentBone_m81863232C2D432EC2C15E42EFB6F447283894EC3 (void);
extern void Bone_TryGetChildBones_m979B87C6EF1951412265D6218617E7C8022BB587 (void);
extern void Bone_Bone_TryGetChildBones_m03608D9A2EB5D471CF5E50311D058CBE500A758D (void);
extern void Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7 (void);
extern void Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F (void);
extern void Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82 (void);
extern void Bone_op_Equality_m2A1372766C8E0A9A4AB75785C1249861E8DFFA2A (void);
extern void Bone_op_Inequality_m6B4B585AE9E00372BCC048784BF676054FCC8AE8 (void);
extern void Bone_Bone_TryGetPosition_Injected_m9DE18CFB67D1F10A82A3BAADDED1FC9CE58ECA3F (void);
extern void Bone_Bone_TryGetRotation_Injected_m0FB167F34FDAEDCAA62F4F448D2E955AD1EC6971 (void);
extern void Bone_Bone_TryGetParentBone_Injected_m459A11867660B6D4AA4F5D019DCAC9E79E118F99 (void);
extern void Bone_Bone_TryGetChildBones_Injected_m386F08BBF17833FCF98537C2FAFC93792D70522C (void);
extern void InputDevices_GetDeviceAtXRNode_m3D322E7D1FFDA9C4D53E6B944E636C39B7A9592B (void);
extern void InputDevices_GetDevicesAtXRNode_m3371E3BD10324AE9CA741119693542E624CB5F46 (void);
extern void InputDevices_GetDevices_mDB6E1E057DC81A1833AEB55B62FA22228D6EFA26 (void);
extern void InputDevices_GetDevicesWithRole_mD55C73FD20EDAD609B079FCA016DFA12B0728E89 (void);
extern void InputDevices_GetDevicesWithCharacteristics_m82F54DE2802FCE4EB730FCFBF8731CA91A27DEB0 (void);
extern void InputDevices_add_deviceConnected_m0329DBAE47CA4A20778F7DCBA470FCF9A672E9C9 (void);
extern void InputDevices_remove_deviceConnected_m52D0C5E73A9BBEEF775E21DF93DDF0DD4F7D1BB5 (void);
extern void InputDevices_add_deviceDisconnected_mADAB4CDAFF3655811C41D7715B29DCC0A2082F1B (void);
extern void InputDevices_remove_deviceDisconnected_m1F052759E0E0911D2AB2B1275D49381BBFDCED1A (void);
extern void InputDevices_add_deviceConfigChanged_mFCA359D6E3569BC8CD39458476194AF0DAEF5946 (void);
extern void InputDevices_remove_deviceConfigChanged_m459857D040C452378C00AD947681EB2B40E06E8E (void);
extern void InputDevices_InvokeConnectionEvent_m10F62F8E2E197247E88668345C22114268233B1A (void);
extern void InputDevices_GetDevices_Internal_mD8C7DD36BABD46C9AD2C048602566B2043BF438D (void);
extern void InputDevices_SendHapticImpulse_mC965C7C6A20D28CD51EDFC5BE61E85D6505A74F7 (void);
extern void InputDevices_SendHapticBuffer_mFB463C7946F55AF5F8D416F1DFD606390171D93F (void);
extern void InputDevices_TryGetHapticCapabilities_mBCEC7D1AE8C869608B3FF5D05EF2629729B91CA7 (void);
extern void InputDevices_StopHaptics_mC099F9F71A3D876D00A03DB22794D6B04F2D5FC8 (void);
extern void InputDevices_TryGetFeatureUsages_m0F8BDF0C21C67D051D24EC4C0D3F9CF3CB4106B4 (void);
extern void InputDevices_TryGetFeatureValue_bool_mD652BFAC63C19FFE34BA0E552A764C655402F0F2 (void);
extern void InputDevices_TryGetFeatureValue_UInt32_m50BDA6D2D451E2032210DFF4CDA8FE7EDE3EE4D2 (void);
extern void InputDevices_TryGetFeatureValue_float_m2F667629D89251CD719FF2752E644467DF5B7F7D (void);
extern void InputDevices_TryGetFeatureValue_Vector2f_mF98BD89249A038E4D9EDA8D4E64CCEFAFB40E646 (void);
extern void InputDevices_TryGetFeatureValue_Vector3f_m765F82CDF7B96A1F616753B9C14AF5144F71F9BB (void);
extern void InputDevices_TryGetFeatureValue_Quaternionf_m7E4BA3BCD07D23060BA8273A1018CD40EB41B1B4 (void);
extern void InputDevices_TryGetFeatureValue_Custom_mF29D94245283B070F4D49A59FA6C252EA17FB782 (void);
extern void InputDevices_TryGetFeatureValueAtTime_bool_mBA038D49EA09AF4FD4442193B09DCA539DEC7AC7 (void);
extern void InputDevices_TryGetFeatureValueAtTime_UInt32_m8E491F70643DC61FE93768E2D2E6CCBEE916F5DC (void);
extern void InputDevices_TryGetFeatureValueAtTime_float_mCEB72E001EFC98B5B530964EC973E4BADC752E62 (void);
extern void InputDevices_TryGetFeatureValueAtTime_Vector2f_m7C9F46AF0431FA389A9ADE4ABDAFAE212595139B (void);
extern void InputDevices_TryGetFeatureValueAtTime_Vector3f_m5BB24F301FA58F3B94CC59A94837224D9B4DF90E (void);
extern void InputDevices_TryGetFeatureValueAtTime_Quaternionf_m805D0B3BF7B8CF81961B32198AEAF5C7E5BBB629 (void);
extern void InputDevices_TryGetFeatureValue_XRHand_m008CA6A68AD1F9F869A28C0A5E80879F852EB3CF (void);
extern void InputDevices_TryGetFeatureValue_XRBone_mD37555D263443153CCF96A09CDB15B2CBB7063EC (void);
extern void InputDevices_TryGetFeatureValue_XREyes_m8441D931D2F58CD56474EE4BA3E43213A600180A (void);
extern void InputDevices_IsDeviceValid_m3A26291DCB7733D1538DF700E4638BE9DF9A994A (void);
extern void InputDevices_GetDeviceName_m0AC6649B2A6AF0394487068FC82DFFA6D33A3D92 (void);
extern void InputDevices_GetDeviceManufacturer_mB18CDA11A3315FFF8E3F5E3FC766D57C13F7FAA6 (void);
extern void InputDevices_GetDeviceSerialNumber_mCE69D3FFAB17F066A9750A11D1EF7D9053AF119E (void);
extern void InputDevices_GetDeviceCharacteristics_m484271629C390D593B073791099D05EC8F45CDF9 (void);
extern void InputDevices_GetDeviceRole_m929A6C663EC2455C495D176D8D3A6927D65E94D8 (void);
extern void InputDevices__ctor_m4DD6E14E841F20A662EB337DB1BEDAB4880F74D8 (void);
extern void XRDisplaySubsystem_add_displayFocusChanged_m315D48546BB85C1663F561CAC72B998555973513 (void);
extern void XRDisplaySubsystem_remove_displayFocusChanged_mEC3FAED38901DCE5226B71D8DF4A2A83570CCA37 (void);
extern void XRDisplaySubsystem_InvokeDisplayFocusChanged_m57036DB43BB9F6BF12AADC268AC47D190378BE56 (void);
extern void XRDisplaySubsystem_get_singlePassRenderingDisabled_m98D2EEC80C6707F5ABCE3E240E7E2B1D3E154CCA (void);
extern void XRDisplaySubsystem_set_singlePassRenderingDisabled_m12F7CB1E763859088C32DD297A3EEEC011352CF4 (void);
extern void XRDisplaySubsystem_get_displayOpaque_m6D1368A243BF5187222D2F86E5C0582A0E2614D9 (void);
extern void XRDisplaySubsystem_get_contentProtectionEnabled_mEF6558C47871AD502E1770D7444DC00E863219DE (void);
extern void XRDisplaySubsystem_set_contentProtectionEnabled_mE21526559F3694BA39AF77EE9AD56927E1C0649B (void);
extern void XRDisplaySubsystem_get_scaleOfAllViewports_m1D6F59F3BDD94B560C7AD8D5F3FC39D24032E1E7 (void);
extern void XRDisplaySubsystem_set_scaleOfAllViewports_m80AECF4F96C20B3B7F70E48AB2812116D15D4A05 (void);
extern void XRDisplaySubsystem_get_scaleOfAllRenderTargets_m1FFC03BBC784C298178DF6CA39FB66B0799A100D (void);
extern void XRDisplaySubsystem_set_scaleOfAllRenderTargets_mB17201481C1A22DB287CA1B6DEC07C6679BD2DB8 (void);
extern void XRDisplaySubsystem_get_zNear_m82243F7005CC85D8AE5D83F0B3ACF78533B1A81B (void);
extern void XRDisplaySubsystem_set_zNear_mA82157C51F3B61B72570A31574E0149E9743933A (void);
extern void XRDisplaySubsystem_get_zFar_m8AA871915F3C13BD5D0260285B836AFE00FC39FF (void);
extern void XRDisplaySubsystem_set_zFar_m8A13EC1823DAE428374A3CBA218B2038D785777B (void);
extern void XRDisplaySubsystem_get_sRGB_mB86EE65EC87EC63037E43A657021222688C475AF (void);
extern void XRDisplaySubsystem_set_sRGB_m441B1B29A0A440B2C3A874021FA7730D8E300758 (void);
extern void XRDisplaySubsystem_get_occlusionMaskScale_mA25920B0AA9B92FAA3609452058560C567F40E00 (void);
extern void XRDisplaySubsystem_set_occlusionMaskScale_m42E19FC069553C3B6473A751A9ADB0BB818D644B (void);
extern void XRDisplaySubsystem_get_foveatedRenderingLevel_m014863692C31B15AF6C14C8F524B05DFF36CD892 (void);
extern void XRDisplaySubsystem_set_foveatedRenderingLevel_mF3EF4C87C1AA8D4D176D2FA3D80862C3083DA8A5 (void);
extern void XRDisplaySubsystem_get_foveatedRenderingFlags_m2DAEDB6BA7BAF479F366A11BA5204AD9F0638C86 (void);
extern void XRDisplaySubsystem_set_foveatedRenderingFlags_m4B2A646A7CFDDF5B7C7BD058AC7936D5131CDF7A (void);
extern void XRDisplaySubsystem_MarkTransformLateLatched_m413E10547A6E6607F4B41F0ED6CFA2EC6986E944 (void);
extern void XRDisplaySubsystem_get_textureLayout_m966B7714CDC86D10146E5FA8C6CDB00C5A06C642 (void);
extern void XRDisplaySubsystem_set_textureLayout_mE0390E5525CBC1CFBA94D7ED494084E06631B51C (void);
extern void XRDisplaySubsystem_get_supportedTextureLayouts_m3FC3B0A25D73FBD48AC95DDFB11C5DAEFD51F0DB (void);
extern void XRDisplaySubsystem_get_reprojectionMode_m4432B35FBD4B039031862B167E0EF418862103DF (void);
extern void XRDisplaySubsystem_set_reprojectionMode_m98DEBB6FA85BBB949E42CAA7FFE4FEBAFAB99DB4 (void);
extern void XRDisplaySubsystem_SetFocusPlane_mBBEC4ACF1CE86DC96AE857DB1FA084D920C3AE79 (void);
extern void XRDisplaySubsystem_SetMSAALevel_m5059067DF2E69C356138B8C2DC99131C22F3488C (void);
extern void XRDisplaySubsystem_get_disableLegacyRenderer_m8A6F9E7099377AD6DE4F180855FA1400897A996F (void);
extern void XRDisplaySubsystem_set_disableLegacyRenderer_m410F3270C21C0337FC3B71E87A85B68A99A58843 (void);
extern void XRDisplaySubsystem_GetRenderPassCount_m75514B28F1542BF4999E1BC0EEBF9561DA1835C6 (void);
extern void XRDisplaySubsystem_GetRenderPass_m81F4AE299700BFE74AD54F8B036D87CD439E8874 (void);
extern void XRDisplaySubsystem_Internal_TryGetRenderPass_mBE659772E08AFDCE6C5B6444C70111BFA4B029E2 (void);
extern void XRDisplaySubsystem_EndRecordingIfLateLatched_mDDADB0A2E961464C86429243D7CCDBE1A75D6A20 (void);
extern void XRDisplaySubsystem_Internal_TryEndRecordingIfLateLatched_mB550855846A6B88D9FE8B1A5B829F1D754579B40 (void);
extern void XRDisplaySubsystem_BeginRecordingIfLateLatched_mDD37688ACD9999F051DB8802819701BA58DFE58B (void);
extern void XRDisplaySubsystem_Internal_TryBeginRecordingIfLateLatched_mEF81E07646D84AAF371EAAE29EAF5B16CB23856C (void);
extern void XRDisplaySubsystem_GetCullingParameters_m6BF6737DD0B607C8719CC80F1AFFE7700498F266 (void);
extern void XRDisplaySubsystem_Internal_TryGetCullingParams_m6EB3424D3E830934E7B004BAC01B4BC1F44F612C (void);
extern void XRDisplaySubsystem_TryGetAppGPUTimeLastFrame_m17BABDCD1716475A2473143A47ECCFE64F17A766 (void);
extern void XRDisplaySubsystem_TryGetCompositorGPUTimeLastFrame_m9D8587317EBC9785C2AEAD86ACC3B939A497E82D (void);
extern void XRDisplaySubsystem_TryGetDroppedFrameCount_m0A036331F1C70470DB1A757734C917CCA0757282 (void);
extern void XRDisplaySubsystem_TryGetFramePresentCount_mC165D298E6ECD02741A4C9E73E69F91D229166F4 (void);
extern void XRDisplaySubsystem_TryGetDisplayRefreshRate_mC92C72C1B54E33C4281B714483222D5CD11866BB (void);
extern void XRDisplaySubsystem_TryGetMotionToPhoton_mEF734C48F96C17C3A63305B75988F1851298D3E6 (void);
extern void XRDisplaySubsystem_GetRenderTexture_mABB964AEAFF9B12DB279EDECAE85A52F6253E5CA (void);
extern void XRDisplaySubsystem_GetRenderTextureForRenderPass_mC51FADA3F1607DF45ECDBF8018049819E09850DE (void);
extern void XRDisplaySubsystem_GetSharedDepthTextureForRenderPass_mE622E4AAFCF6C46CB385CB722A95C3106A7BD476 (void);
extern void XRDisplaySubsystem_GetPreferredMirrorBlitMode_m24F966A405A26B172FCD6050AC440D7F95C14329 (void);
extern void XRDisplaySubsystem_SetPreferredMirrorBlitMode_mCB062F49CAD06EFBAAE71D57842BA9054A53B9D2 (void);
extern void XRDisplaySubsystem_GetMirrorViewBlitDesc_m5117350A305554237006270CECD72113E2286F2F (void);
extern void XRDisplaySubsystem_GetMirrorViewBlitDesc_m457DF247F40C563D6AFE45C3E541EE2B75D0C8F6 (void);
extern void XRDisplaySubsystem_AddGraphicsThreadMirrorViewBlit_mC53840380280B81E9F8A0F5D4EBADB26B37DACD9 (void);
extern void XRDisplaySubsystem_AddGraphicsThreadMirrorViewBlit_mD55776DC2FD5FFC61D639E44B31D1D26E298E4DB (void);
extern void XRDisplaySubsystem_get_hdrOutputSettings_m54E5C853953AC231441243765D2CF8C76EA1C171 (void);
extern void XRDisplaySubsystem__ctor_m5DA92849F107C6A802BF584D5E328FF2DB971B01 (void);
extern void XRDisplaySubsystem_SetFocusPlane_Injected_mF5B3A280B982C44DC633EB549B53F8C42BADFBA8 (void);
extern void XRRenderPass_GetRenderParameter_m3526E26F8ABDA52C52BDF163F4EA74B7DE4B6A0B (void);
extern void XRRenderPass_GetRenderParameterCount_m8FECAAF96CD4DF45B0786CB19CD169C1C46BE10A (void);
extern void XRRenderPass_GetRenderParameter_Injected_m172BF15F32F6F47B9C9C6BC14C4DDCF8E811DA31 (void);
extern void XRRenderPass_GetRenderParameterCount_Injected_m0FE39E4D6E091E02A4B1B4F4506B41BC52302803 (void);
extern void XRMirrorViewBlitDesc_GetBlitParameter_m3464A6CB2461B550C808BC4CF3B5AA2EDBCBD17C (void);
extern void XRMirrorViewBlitDesc_GetBlitParameter_Injected_m918BB7915BBD91B0430FCD23187F72B8982C1CC5 (void);
extern void XRDisplaySubsystemDescriptor_get_disablesLegacyVr_m9F1954399F9C3CD104EDADF9A3437893A23E1513 (void);
extern void XRDisplaySubsystemDescriptor_get_enableBackBufferMSAA_mD7AF811B5AD3C0C9749C0204978DF2BB8DAF7AA3 (void);
extern void XRDisplaySubsystemDescriptor_GetAvailableMirrorBlitModeCount_mE92A9846AE52063AA605651CD1C2BBBE1050080A (void);
extern void XRDisplaySubsystemDescriptor_GetMirrorBlitModeByIndex_mE218C92B83208625D8E6D764A7E18F21A9A5654A (void);
extern void XRDisplaySubsystemDescriptor__ctor_mB9B2993D74FFC580731C03B390C764260458FAA6 (void);
extern void XRInputSubsystem_GetIndex_m0B132BB23F35C6A8CB0C6F344088AE7BF9D63051 (void);
extern void XRInputSubsystem_TryRecenter_m4F8888E40ED79139DCB81D56A67C03B4D931A6BB (void);
extern void XRInputSubsystem_TryGetInputDevices_mF04BC44FFD5A9C4FBFB7B6FB5B9AD84735D65377 (void);
extern void XRInputSubsystem_TrySetTrackingOriginMode_m132C190CEAE4403A381BF1C1C4B5FF349F2A3FA7 (void);
extern void XRInputSubsystem_GetTrackingOriginMode_mBAFED615F74039A681825BB956AD3C8FA7DE45F2 (void);
extern void XRInputSubsystem_GetSupportedTrackingOriginModes_mBA7190E84E6BB4F251C232B97565E228AECB3018 (void);
extern void XRInputSubsystem_TryGetBoundaryPoints_m7FEEF524DD8B85151CE8C99378DD690825951C3B (void);
extern void XRInputSubsystem_TryGetBoundaryPoints_AsList_m4F2B3C703869F8F0946677E85EE0433FD2750CF2 (void);
extern void XRInputSubsystem_add_trackingOriginUpdated_mA5E69767B6E8D505BE73804A4B4EA738A27F675E (void);
extern void XRInputSubsystem_remove_trackingOriginUpdated_m6A04D2813F1D4A37C013BA00EBC862D1EEA7473E (void);
extern void XRInputSubsystem_add_boundaryChanged_m669E66C0AFB8B069034196A2BFEF14888E10AEB0 (void);
extern void XRInputSubsystem_remove_boundaryChanged_m43F741BBF0553EB25843CF6BE9EF41CF2922E990 (void);
extern void XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_mD9F93C1B2F0BCDE37190DC500F9D93B273362EEB (void);
extern void XRInputSubsystem_InvokeBoundaryChangedEvent_m5BAE59235BADE518D0E32B8D420A0572B63C68C2 (void);
extern void XRInputSubsystem_TryGetDeviceIds_AsList_m2115E2722113A6D775E5F18E5A7C152189B2398A (void);
extern void XRInputSubsystem__ctor_mD0260427CD99745155B171BB6D03862B3CE303E4 (void);
extern void XRInputSubsystemDescriptor_get_disablesLegacyInput_m666CEB17A2F065A1E3ABA6122B1F448166E936DD (void);
extern void XRInputSubsystemDescriptor__ctor_m7DFAE8F8670A5721F02B0AE27BB47389BA0F8DFB (void);
extern void MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4 (void);
extern void MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0 (void);
extern void MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9 (void);
extern void MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818 (void);
extern void MeshId_op_Equality_m2FBB40F4059AE561DB83EC181E4F1B347BEB4F27 (void);
extern void MeshId_op_Inequality_mD2799782D7ED1EB48EDF219127C4C5FD7A846F07 (void);
extern void MeshId_get_InvalidId_m5DE10A6930F737A7303B5897713D8F7A21E0F219 (void);
extern void MeshId__cctor_mE4556EF31E7F96397E4C9E7C3DF80A3C129D431D (void);
extern void HashCodeHelper_Combine_m2A63E4964D06338CC6F9DE17F5EFCCC348A6A1D7 (void);
extern void HashCodeHelper_Combine_mEDE733EC1ABDEA82040BBC18551FB975F3FAC322 (void);
extern void HashCodeHelper_Combine_mF867D2FAB545AD0A4832679FC73F7A2B289A37F7 (void);
extern void HashCodeHelper_Combine_mBE52BFDECDDA219A4A334421E8706078A4BE6680 (void);
extern void HashCodeHelper_Combine_m429AA1346C5A7ACFA6EC9A5D26250CEA4A1BAF89 (void);
extern void HashCodeHelper_Combine_m110C54CCBA83DD580295D7BA248976C50F6BF606 (void);
extern void HashCodeHelper_Combine_mBF383AC565B49ACFCB9A0046504C40A8997BAEFD (void);
extern void MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D (void);
extern void MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9 (void);
extern void MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180 (void);
extern void MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0 (void);
extern void MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B (void);
extern void MeshGenerationResult_get_Timestamp_m3D0F0FD234467DF615695ECBB381AFE33030F6CE (void);
extern void MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F (void);
extern void MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3 (void);
extern void MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3 (void);
extern void MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F (void);
extern void MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC (void);
extern void MeshGenerationResult_op_Equality_mFC78B67E0CDF2086240795242F103772175261B9 (void);
extern void MeshGenerationResult_op_Inequality_mEB926A2C222D2D696FE4A4838C0E353FD3C0B82E (void);
extern void MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC (void);
extern void MeshInfo_get_MeshId_m96D69F5BA60A596D7C9FAAF0931940452E3D8846 (void);
extern void MeshInfo_set_MeshId_mF676EFDD6F67DE2292126F0B473F2E23869C2178 (void);
extern void MeshInfo_get_ChangeState_mF366492B8EF4A9FC8F677E01A6BA4C6C75D3BF68 (void);
extern void MeshInfo_set_ChangeState_mC3719CF950FC8E42E86B967F83F22AE0BCCC0784 (void);
extern void MeshInfo_get_PriorityHint_mDBD3A096EC571EAAFB1E6CB93F4F309BD70F9231 (void);
extern void MeshInfo_set_PriorityHint_mA0F4BB9543846770A082A85C721E16D16F664615 (void);
extern void MeshInfo_Equals_m832745EC601145A6C99A608A49B6B750C0B8B5F1 (void);
extern void MeshInfo_Equals_m8D4446A3EBBC42CDF8E0637EC4CFD00AB89B12C6 (void);
extern void MeshInfo_op_Equality_mC8E2D6A30564703552744FA35B1B570B0517E450 (void);
extern void MeshInfo_op_Inequality_m4C3E5CDBC3F34C5246DB4DE4D39F8BEB9134A3AF (void);
extern void MeshInfo_GetHashCode_mFA72664A2E6A9B6E86CE36C2D211B8D2AC22250E (void);
extern void MeshTransform_get_MeshId_m6B6006910DA8EF0DDB517424DDBCC2DD5B7E92B9 (void);
extern void MeshTransform_get_Timestamp_m63A9BEB7A7544ABA72AFBCF9A4CBA043C872FDFD (void);
extern void MeshTransform_get_Position_m4E4F6240B8F9DBE4634C1CA346AAFAC1555197E9 (void);
extern void MeshTransform_get_Rotation_m1DE1FC2459EC908BCB7A2F82EDFCE437BC211F8F (void);
extern void MeshTransform_get_Scale_m2C279AD6989B26E8F48402B82312322F0D8B520A (void);
extern void MeshTransform__ctor_mC589777DA0363B43418905B044E5B5BB4D140F9F (void);
extern void MeshTransform_Equals_mEE84420FF15F891F3A08C777C91B463712C25200 (void);
extern void MeshTransform_Equals_m2F9F68FEC43A7A1E4F5E45E89EA191CC768C5784 (void);
extern void MeshTransform_op_Equality_mA614ED0C41B2018F55CE63DD3123D3D2AEB34B1E (void);
extern void MeshTransform_op_Inequality_mB231191281C9ED068D392A05D242059BC0CA4EB4 (void);
extern void MeshTransform_GetHashCode_m5EBA0C63B6E23F9024D0A56EFA98B99C5A5CD5F5 (void);
extern void XRMeshSubsystem_TryGetMeshInfos_m05F584066A873F286C16B99A929C4B3B9AB58FD2 (void);
extern void XRMeshSubsystem_GetMeshInfosAsList_m72D6DDE116C761283E51B90D4EBC50D59C395917 (void);
extern void XRMeshSubsystem_GetMeshInfosAsFixedArray_mB66DAD4FDAB7465AE12A993D38DBC2056E558892 (void);
extern void XRMeshSubsystem_GenerateMeshAsync_m3DEA3C294695FD7C54D1186FF7DCE84A4BA1CFB7 (void);
extern void XRMeshSubsystem_GenerateMeshAsync_mB0C6892EB55BC93DB6340E11D9C7ABDAB05E2803 (void);
extern void XRMeshSubsystem_InvokeMeshReadyDelegate_m495A42DE7C44B760CB7D41244A9314F860EA6C53 (void);
extern void XRMeshSubsystem_get_meshDensity_mBCCC19C5A36D9209666706D5750DAFC19E6C1504 (void);
extern void XRMeshSubsystem_set_meshDensity_mB3B23768DE704088634FCBEE3F21B9E0C869BE31 (void);
extern void XRMeshSubsystem_SetBoundingVolume_mC2B3A3A003779328460EF482220BC94C71D40294 (void);
extern void XRMeshSubsystem_GetUpdatedMeshTransforms_mB0D1B03FB20FF0F5086AF899E97FD2ED97124652 (void);
extern void XRMeshSubsystem_GetUpdatedMeshTransforms_m1D7E5E319F6C2D8C46C2AD21C5F4B6649C27FAD2 (void);
extern void XRMeshSubsystem__ctor_mA9C27A31A690B9023F70B628A9D9F5E3F5ED2AEA (void);
extern void XRMeshSubsystem_GenerateMeshAsync_Injected_m47C5F75BC47F1A33FD82EFE2A58CF764E71DADB6 (void);
extern void XRMeshSubsystem_SetBoundingVolume_Injected_m9810F204B1036C59089B1FF7716C51922BFCDE7C (void);
extern void MeshTransformList__ctor_m7B11F4035F0C79BEEC061590F37136CF7AC16B71 (void);
extern void MeshTransformList_get_Count_m971FEDFD7B9522D8D122B2414E58A3E5A3B81F8A (void);
extern void MeshTransformList_get_Data_m98A09C2E5F2744566323EB52A47A0F002139C40D (void);
extern void MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636 (void);
extern void MeshTransformList_GetLength_mC3AE75E252CD06356380765AF655073B396A5080 (void);
extern void MeshTransformList_GetData_m6A656C505BBE740688C6A158BF73E7C4C19B8F8C (void);
extern void MeshTransformList_Dispose_m7655ACDE6BC605B30EF2BC387A7B9D0F4D9EED19 (void);
extern void XRMeshSubsystemDescriptor__ctor_mFD056F69A8BECE56819411D4CD84653B3B735A1B (void);
extern void XRStats_TryGetStat_mE91924B240A89BA396554CE3742A06210514FA8B (void);
extern void XRStats_TryGetStat_Internal_mDE5287222A37D521F603FAF12C7AB875E43E5DE5 (void);
static Il2CppMethodPointer s_methodPointers[384] = 
{
	InputTracking_add_trackingAcquired_mB4BFD78603798E38C0915B6E9E49DBF2E3EEE3E4,
	InputTracking_remove_trackingAcquired_m610A04BE4915CA2FA429810C607C42CF24792816,
	InputTracking_add_trackingLost_mE5935FB7C709D54B2F38B94673451EDCB188EEBF,
	InputTracking_remove_trackingLost_m48A73CF7CA715230A1760E473AC30CEB33E16893,
	InputTracking_add_nodeAdded_mEF63261745947F87908B9C23380E2354BC3EA3F7,
	InputTracking_remove_nodeAdded_m73C678FB4049A38E7F66635CC810FF7D989A3CBC,
	InputTracking_add_nodeRemoved_mBAEA16F632E4F5E173B4DCB604BBA1C3A1BFFA60,
	InputTracking_remove_nodeRemoved_m8E2532C10663D71663CFD2F2959B4B4865139DBB,
	InputTracking_InvokeTrackingEvent_mA218CBE5D81A639B9C9A084A5360FEAD4625C42C,
	InputTracking_GetLocalPosition_m0753549019EB2F0675886E94F237DA9E456EC31D,
	InputTracking_GetLocalRotation_m25C883320022BBF4B0F2DF6B1449CDB69DCF7E5A,
	InputTracking_Recenter_m1843AECFE9093882149E6B197E6EFEB6151E6C7C,
	InputTracking_GetNodeName_m4E82799BB255A098F05DF2A7550FF5D4D6F48911,
	InputTracking_GetNodeStates_mA2E8D154A47C817ED74AD42F6B38A9C906A57A67,
	InputTracking_GetNodeStates_Internal_m6E67CE5EC9C950ED633AEC42F8BD5B15EFE3B916,
	InputTracking_get_disablePositionalTracking_m91D5AF9BE2C47A2209D2ADC8D9DDC49B2EA072A2,
	InputTracking_set_disablePositionalTracking_mC4C0EC50561442893A070430991FCCC35666E727,
	InputTracking_GetDeviceIdAtXRNode_mF6AA8F79B81E685890712AE72FADC4D1113CDECC,
	InputTracking_GetDeviceIdsAtXRNode_Internal_m982E266A1E40EFA04380D0D1A5E91CBB09584389,
	InputTracking_GetLocalPosition_Injected_mC244C17675072F3828580AA42BA8BC8FB84CC4A7,
	InputTracking_GetLocalRotation_Injected_m78D7134A157A6905B75999DF5EE086B093A75933,
	XRNodeState_get_uniqueID_m5776ABB4A4516A1DD61A4BECE94FF7CE09812755,
	XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F,
	XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932,
	XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4,
	XRNodeState_get_tracked_m777E4EE8D1812BC9849A696EE52FFAD3F4760F79,
	XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45,
	XRNodeState_set_position_m4DEE96188C00F002912BA93B35976267209F4278,
	XRNodeState_set_rotation_m233504F15F6581E219A0412A49BAB2630A060627,
	XRNodeState_set_velocity_m0E4D32C82609E2606B360E92DDF822A8B9938AAC,
	XRNodeState_set_angularVelocity_m46D5154D7C0FEF8C3B6BE38A178753FBF475F1B7,
	XRNodeState_set_acceleration_mA6BDB5D1ADE1A011A885A205EC9B7E41608923CA,
	XRNodeState_set_angularAcceleration_mA5C680F9CFC1D534377B7FEA7ED065023BB33844,
	XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6,
	XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301,
	XRNodeState_TryGetVelocity_m558D0C6424C5F0DC98E151C0DAC61173EA4AD0C4,
	XRNodeState_TryGetAngularVelocity_mB7C1D0876373441A07815A8CCB1FC05C11729176,
	XRNodeState_TryGetAcceleration_m9EAEB95F9ED4B779D54F6BEF7DDFDC34B7EE6892,
	XRNodeState_TryGetAngularAcceleration_m9D4FF5411CFB801471802F8A084B4333C62F4C04,
	XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630,
	XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616,
	HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9,
	HapticCapabilities_set_numChannels_m59424616BE7E0EEAA6B9CBBF5659DAA0FC2662D7,
	HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C,
	HapticCapabilities_set_supportsImpulse_mEE83FC8E998D832A8A4DAD0803DB6AE42459A223,
	HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59,
	HapticCapabilities_set_supportsBuffer_m7E835F610102BC8DAB832FED19709FBBE3D2A701,
	HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA,
	HapticCapabilities_set_bufferFrequencyHz_m1D3C289047D65CC38E340C07F232328C6140A75E,
	HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C,
	HapticCapabilities_set_bufferMaxSize_m1524C183E3DC90C7E938BD5CF8BE05DCE24CF3C5,
	HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407,
	HapticCapabilities_set_bufferOptimalSize_mA6839E1849FB8B2F5A937A7B69980B12F1F7179C,
	HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57,
	HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F,
	HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB,
	HapticCapabilities_op_Equality_m5751D10DF2639053956854F5CA4B02E907B35436,
	HapticCapabilities_op_Inequality_mCD17A6A196EE20B994A7391EF20E3B5CC05BCCCD,
	InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085,
	InputFeatureUsage_set_name_mBC7F8DE6FF4A45609D62A63B5AF9AA7EB867F3C4,
	InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810,
	InputFeatureUsage_set_internalType_m5BB4726F3F1A94047BA41FAA1E93DAA40AD6D00F,
	InputFeatureUsage_get_type_mD9697C1BFA3EA57E89A23A06019AAB73CB24326D,
	InputFeatureUsage__ctor_m34E81E0915344257F81AC76BF38CF9AFE97D6819,
	InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0,
	InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D,
	InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D,
	InputFeatureUsage_op_Equality_mCA5EC7D0F4A65D02D22E45C39102EB1EFAF4B017,
	InputFeatureUsage_op_Inequality_m15BD00AC67A5313511786BAF5E73667B38E33A6E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CommonUsages__cctor_mC385C864BC1092A2B00B21E9AA6A7F079B195B9C,
	InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34,
	InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19,
	InputDevice_get_subsystem_mF78D9FF3EC606129608E19A0B22F25A80E005FFF,
	InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948,
	InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE,
	InputDevice_get_role_m8A5CD735B1BD8E9712A75CB734092C443452925E,
	InputDevice_get_manufacturer_mABBD3690895EC6A8FE86094CCFA4E0B081C102BB,
	InputDevice_get_serialNumber_m777847E27A8EB8871A66910132D17B2B58380B0E,
	InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB,
	InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB,
	InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B,
	InputDevice_SendHapticBuffer_m8988C8FF59C5A33B65EDA0BD7EAF4996D530C879,
	InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3,
	InputDevice_StopHaptics_m816C765A638F5571FD884AEED49FFA74BD535080,
	InputDevice_TryGetFeatureUsages_mFD9F3CE1465177544260CEB32E1821AA0FDD24AC,
	InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884,
	InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C,
	InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081,
	InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA,
	InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167,
	InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F,
	InputDevice_TryGetFeatureValue_mC30F276811E65A010BC493B2C7707837CF52396C,
	InputDevice_TryGetFeatureValue_m069EBD94A6A1DEB178F0DEFD4ADD328B31FCFF52,
	InputDevice_TryGetFeatureValue_mBD7F8831DE80286ED4A5B4F55686EBD4EF8058F0,
	InputDevice_TryGetFeatureValue_mCF4528B1552EB1B5980DAAED7287CBE83556688F,
	InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11,
	InputDevice_TryGetFeatureValue_mAAA12B1ACD0C5EABB0AD26B8C9F52E41E98AC277,
	InputDevice_TryGetFeatureValue_m12875D2D90E21B5F08D81E620B082854905A178D,
	InputDevice_TryGetFeatureValue_mF1ED1B13F43D27144E424503FD36CB88BB1FD4E3,
	InputDevice_TryGetFeatureValue_mE22CFE88DBE2862117C4BFA85C6E304C17CF38B9,
	InputDevice_TryGetFeatureValue_m14C541BA603C521379165D18DE71638E0726BE7B,
	InputDevice_TryGetFeatureValue_mC0CB4610B0A7ACA72411D0E7B8FC29EA4AEEF335,
	InputDevice_TryGetFeatureValue_mC1E70E2DB5E12C8B0884141078038FBE22ED95DB,
	NULL,
	InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC,
	InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC,
	InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D,
	InputDevice_op_Equality_m57E5FF6F966E9E8E65AB3AED68ED4D099F307044,
	InputDevice_op_Inequality_m3C29589128440555BC60B512C5C6776C3B245540,
	TimeConverter_get_now_m9746E40FCC1F094CC6A5B3053772B22798D7CBC2,
	TimeConverter_LocalDateTimeToUnixTimeMilliseconds_mCE04003EB92CF112677D736E98BB11BCB2A8C4B5,
	TimeConverter_UnixTimeMillisecondsToLocalDateTime_m30F081C166D14DCDD37B60142EF7C8C86387E2C1,
	TimeConverter__cctor_mA39D7738A61C34C0A022191DF0D3375E183CE95D,
	Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A,
	Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775,
	Hand_TryGetRootBone_m57751D162BB60316E15C09608D4365C4C8FFB346,
	Hand_Hand_TryGetRootBone_mFCE4EF8CA602250B3440900F0E0E207B578C1F11,
	Hand_TryGetFingerBones_m62D184582CD2EC6FE21C6B70DB9CBEA1B4FADAE4,
	Hand_Hand_TryGetFingerBonesAsList_mE35ECA09B8BAEB6ABED19A1290D3E7A728C7E53E,
	Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06,
	Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864,
	Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE,
	Hand_op_Equality_m5EFB50DDFADE7B6FDC7148A38AB825F452AA67E6,
	Hand_op_Inequality_mC0887C4B3DAA48720699F959CA8A2823E64D3A61,
	Hand_Hand_TryGetRootBone_Injected_mFE6861D8999D6C44446F2F35316473F14BE65025,
	Hand_Hand_TryGetFingerBonesAsList_Injected_m2308D6F291C6F6CC6D1C05CADCCABFB0850BAF57,
	Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD,
	Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8,
	Eyes_TryGetLeftEyePosition_mCA6E54596518263010B4939CDA4F921E8C825036,
	Eyes_TryGetRightEyePosition_m9DF9633DB7FDB907DAA8DBB4D46227C175003A24,
	Eyes_TryGetLeftEyeRotation_mAE5123DBED78CC8CC6DF4D52CE281DE84F4486B9,
	Eyes_TryGetRightEyeRotation_mE4CED1712FC85CCCD5840BABD5513FA70D72D37A,
	Eyes_Eyes_TryGetEyePosition_m3591B79F2A13ACDB6D449B392FF8964EF804E445,
	Eyes_Eyes_TryGetEyeRotation_m1F1A4B030A7092CFC39C11903CE92AEFA81E59F7,
	Eyes_TryGetFixationPoint_mD7E98889AA0C4920837775486B2792F1674B461F,
	Eyes_Eyes_TryGetFixationPoint_m70CBDBADA88A9C3ADADCBF765A4B7EB1635663E9,
	Eyes_TryGetLeftEyeOpenAmount_m6F75E47258DB11053E5FAE6F1CB1B3FB77149A29,
	Eyes_TryGetRightEyeOpenAmount_m26AD3342F50D3FA4AC1FA2252D2B7FC4F8D53B94,
	Eyes_Eyes_TryGetEyeOpenAmount_mABEF702A3A302798A1B56003DC13082D1927832A,
	Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D,
	Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969,
	Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8,
	Eyes_op_Equality_m16F7DDF591696D06CF7F93F0EAED15F125D2DC30,
	Eyes_op_Inequality_m14641754AC3F79AD482762C0B7BCE700FA8B6FD6,
	Eyes_Eyes_TryGetEyePosition_Injected_mFDA15FE36C4BA408456EE9AB0506B18346C9C888,
	Eyes_Eyes_TryGetEyeRotation_Injected_m14A277758AE782F3AC70F6024FEA832789FFFFC1,
	Eyes_Eyes_TryGetFixationPoint_Injected_m039934F8C688269A2683CC2F93148194B5DEF7FB,
	Eyes_Eyes_TryGetEyeOpenAmount_Injected_m2F48ADDC7E8A12E002E3643916901249F580A013,
	Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1,
	Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE,
	Bone_TryGetPosition_m995E6BD44A081407B8FA2B4BB1B6223C1526E8D7,
	Bone_Bone_TryGetPosition_m456BB5F8E230160FC465158AD3063980F0225AB8,
	Bone_TryGetRotation_mBCD1D06C58E9C28F9C8F2A24A454D6B90ED6EB65,
	Bone_Bone_TryGetRotation_m9D11366D142C8771974FCCD06B36D4BBB7CFF1F8,
	Bone_TryGetParentBone_m7E683151870A5D22C319C89CBA9577D4CADDFF85,
	Bone_Bone_TryGetParentBone_m81863232C2D432EC2C15E42EFB6F447283894EC3,
	Bone_TryGetChildBones_m979B87C6EF1951412265D6218617E7C8022BB587,
	Bone_Bone_TryGetChildBones_m03608D9A2EB5D471CF5E50311D058CBE500A758D,
	Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7,
	Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F,
	Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82,
	Bone_op_Equality_m2A1372766C8E0A9A4AB75785C1249861E8DFFA2A,
	Bone_op_Inequality_m6B4B585AE9E00372BCC048784BF676054FCC8AE8,
	Bone_Bone_TryGetPosition_Injected_m9DE18CFB67D1F10A82A3BAADDED1FC9CE58ECA3F,
	Bone_Bone_TryGetRotation_Injected_m0FB167F34FDAEDCAA62F4F448D2E955AD1EC6971,
	Bone_Bone_TryGetParentBone_Injected_m459A11867660B6D4AA4F5D019DCAC9E79E118F99,
	Bone_Bone_TryGetChildBones_Injected_m386F08BBF17833FCF98537C2FAFC93792D70522C,
	InputDevices_GetDeviceAtXRNode_m3D322E7D1FFDA9C4D53E6B944E636C39B7A9592B,
	InputDevices_GetDevicesAtXRNode_m3371E3BD10324AE9CA741119693542E624CB5F46,
	InputDevices_GetDevices_mDB6E1E057DC81A1833AEB55B62FA22228D6EFA26,
	InputDevices_GetDevicesWithRole_mD55C73FD20EDAD609B079FCA016DFA12B0728E89,
	InputDevices_GetDevicesWithCharacteristics_m82F54DE2802FCE4EB730FCFBF8731CA91A27DEB0,
	InputDevices_add_deviceConnected_m0329DBAE47CA4A20778F7DCBA470FCF9A672E9C9,
	InputDevices_remove_deviceConnected_m52D0C5E73A9BBEEF775E21DF93DDF0DD4F7D1BB5,
	InputDevices_add_deviceDisconnected_mADAB4CDAFF3655811C41D7715B29DCC0A2082F1B,
	InputDevices_remove_deviceDisconnected_m1F052759E0E0911D2AB2B1275D49381BBFDCED1A,
	InputDevices_add_deviceConfigChanged_mFCA359D6E3569BC8CD39458476194AF0DAEF5946,
	InputDevices_remove_deviceConfigChanged_m459857D040C452378C00AD947681EB2B40E06E8E,
	InputDevices_InvokeConnectionEvent_m10F62F8E2E197247E88668345C22114268233B1A,
	InputDevices_GetDevices_Internal_mD8C7DD36BABD46C9AD2C048602566B2043BF438D,
	InputDevices_SendHapticImpulse_mC965C7C6A20D28CD51EDFC5BE61E85D6505A74F7,
	InputDevices_SendHapticBuffer_mFB463C7946F55AF5F8D416F1DFD606390171D93F,
	InputDevices_TryGetHapticCapabilities_mBCEC7D1AE8C869608B3FF5D05EF2629729B91CA7,
	InputDevices_StopHaptics_mC099F9F71A3D876D00A03DB22794D6B04F2D5FC8,
	InputDevices_TryGetFeatureUsages_m0F8BDF0C21C67D051D24EC4C0D3F9CF3CB4106B4,
	InputDevices_TryGetFeatureValue_bool_mD652BFAC63C19FFE34BA0E552A764C655402F0F2,
	InputDevices_TryGetFeatureValue_UInt32_m50BDA6D2D451E2032210DFF4CDA8FE7EDE3EE4D2,
	InputDevices_TryGetFeatureValue_float_m2F667629D89251CD719FF2752E644467DF5B7F7D,
	InputDevices_TryGetFeatureValue_Vector2f_mF98BD89249A038E4D9EDA8D4E64CCEFAFB40E646,
	InputDevices_TryGetFeatureValue_Vector3f_m765F82CDF7B96A1F616753B9C14AF5144F71F9BB,
	InputDevices_TryGetFeatureValue_Quaternionf_m7E4BA3BCD07D23060BA8273A1018CD40EB41B1B4,
	InputDevices_TryGetFeatureValue_Custom_mF29D94245283B070F4D49A59FA6C252EA17FB782,
	InputDevices_TryGetFeatureValueAtTime_bool_mBA038D49EA09AF4FD4442193B09DCA539DEC7AC7,
	InputDevices_TryGetFeatureValueAtTime_UInt32_m8E491F70643DC61FE93768E2D2E6CCBEE916F5DC,
	InputDevices_TryGetFeatureValueAtTime_float_mCEB72E001EFC98B5B530964EC973E4BADC752E62,
	InputDevices_TryGetFeatureValueAtTime_Vector2f_m7C9F46AF0431FA389A9ADE4ABDAFAE212595139B,
	InputDevices_TryGetFeatureValueAtTime_Vector3f_m5BB24F301FA58F3B94CC59A94837224D9B4DF90E,
	InputDevices_TryGetFeatureValueAtTime_Quaternionf_m805D0B3BF7B8CF81961B32198AEAF5C7E5BBB629,
	InputDevices_TryGetFeatureValue_XRHand_m008CA6A68AD1F9F869A28C0A5E80879F852EB3CF,
	InputDevices_TryGetFeatureValue_XRBone_mD37555D263443153CCF96A09CDB15B2CBB7063EC,
	InputDevices_TryGetFeatureValue_XREyes_m8441D931D2F58CD56474EE4BA3E43213A600180A,
	InputDevices_IsDeviceValid_m3A26291DCB7733D1538DF700E4638BE9DF9A994A,
	InputDevices_GetDeviceName_m0AC6649B2A6AF0394487068FC82DFFA6D33A3D92,
	InputDevices_GetDeviceManufacturer_mB18CDA11A3315FFF8E3F5E3FC766D57C13F7FAA6,
	InputDevices_GetDeviceSerialNumber_mCE69D3FFAB17F066A9750A11D1EF7D9053AF119E,
	InputDevices_GetDeviceCharacteristics_m484271629C390D593B073791099D05EC8F45CDF9,
	InputDevices_GetDeviceRole_m929A6C663EC2455C495D176D8D3A6927D65E94D8,
	InputDevices__ctor_m4DD6E14E841F20A662EB337DB1BEDAB4880F74D8,
	XRDisplaySubsystem_add_displayFocusChanged_m315D48546BB85C1663F561CAC72B998555973513,
	XRDisplaySubsystem_remove_displayFocusChanged_mEC3FAED38901DCE5226B71D8DF4A2A83570CCA37,
	XRDisplaySubsystem_InvokeDisplayFocusChanged_m57036DB43BB9F6BF12AADC268AC47D190378BE56,
	XRDisplaySubsystem_get_singlePassRenderingDisabled_m98D2EEC80C6707F5ABCE3E240E7E2B1D3E154CCA,
	XRDisplaySubsystem_set_singlePassRenderingDisabled_m12F7CB1E763859088C32DD297A3EEEC011352CF4,
	XRDisplaySubsystem_get_displayOpaque_m6D1368A243BF5187222D2F86E5C0582A0E2614D9,
	XRDisplaySubsystem_get_contentProtectionEnabled_mEF6558C47871AD502E1770D7444DC00E863219DE,
	XRDisplaySubsystem_set_contentProtectionEnabled_mE21526559F3694BA39AF77EE9AD56927E1C0649B,
	XRDisplaySubsystem_get_scaleOfAllViewports_m1D6F59F3BDD94B560C7AD8D5F3FC39D24032E1E7,
	XRDisplaySubsystem_set_scaleOfAllViewports_m80AECF4F96C20B3B7F70E48AB2812116D15D4A05,
	XRDisplaySubsystem_get_scaleOfAllRenderTargets_m1FFC03BBC784C298178DF6CA39FB66B0799A100D,
	XRDisplaySubsystem_set_scaleOfAllRenderTargets_mB17201481C1A22DB287CA1B6DEC07C6679BD2DB8,
	XRDisplaySubsystem_get_zNear_m82243F7005CC85D8AE5D83F0B3ACF78533B1A81B,
	XRDisplaySubsystem_set_zNear_mA82157C51F3B61B72570A31574E0149E9743933A,
	XRDisplaySubsystem_get_zFar_m8AA871915F3C13BD5D0260285B836AFE00FC39FF,
	XRDisplaySubsystem_set_zFar_m8A13EC1823DAE428374A3CBA218B2038D785777B,
	XRDisplaySubsystem_get_sRGB_mB86EE65EC87EC63037E43A657021222688C475AF,
	XRDisplaySubsystem_set_sRGB_m441B1B29A0A440B2C3A874021FA7730D8E300758,
	XRDisplaySubsystem_get_occlusionMaskScale_mA25920B0AA9B92FAA3609452058560C567F40E00,
	XRDisplaySubsystem_set_occlusionMaskScale_m42E19FC069553C3B6473A751A9ADB0BB818D644B,
	XRDisplaySubsystem_get_foveatedRenderingLevel_m014863692C31B15AF6C14C8F524B05DFF36CD892,
	XRDisplaySubsystem_set_foveatedRenderingLevel_mF3EF4C87C1AA8D4D176D2FA3D80862C3083DA8A5,
	XRDisplaySubsystem_get_foveatedRenderingFlags_m2DAEDB6BA7BAF479F366A11BA5204AD9F0638C86,
	XRDisplaySubsystem_set_foveatedRenderingFlags_m4B2A646A7CFDDF5B7C7BD058AC7936D5131CDF7A,
	XRDisplaySubsystem_MarkTransformLateLatched_m413E10547A6E6607F4B41F0ED6CFA2EC6986E944,
	XRDisplaySubsystem_get_textureLayout_m966B7714CDC86D10146E5FA8C6CDB00C5A06C642,
	XRDisplaySubsystem_set_textureLayout_mE0390E5525CBC1CFBA94D7ED494084E06631B51C,
	XRDisplaySubsystem_get_supportedTextureLayouts_m3FC3B0A25D73FBD48AC95DDFB11C5DAEFD51F0DB,
	XRDisplaySubsystem_get_reprojectionMode_m4432B35FBD4B039031862B167E0EF418862103DF,
	XRDisplaySubsystem_set_reprojectionMode_m98DEBB6FA85BBB949E42CAA7FFE4FEBAFAB99DB4,
	XRDisplaySubsystem_SetFocusPlane_mBBEC4ACF1CE86DC96AE857DB1FA084D920C3AE79,
	XRDisplaySubsystem_SetMSAALevel_m5059067DF2E69C356138B8C2DC99131C22F3488C,
	XRDisplaySubsystem_get_disableLegacyRenderer_m8A6F9E7099377AD6DE4F180855FA1400897A996F,
	XRDisplaySubsystem_set_disableLegacyRenderer_m410F3270C21C0337FC3B71E87A85B68A99A58843,
	XRDisplaySubsystem_GetRenderPassCount_m75514B28F1542BF4999E1BC0EEBF9561DA1835C6,
	XRDisplaySubsystem_GetRenderPass_m81F4AE299700BFE74AD54F8B036D87CD439E8874,
	XRDisplaySubsystem_Internal_TryGetRenderPass_mBE659772E08AFDCE6C5B6444C70111BFA4B029E2,
	XRDisplaySubsystem_EndRecordingIfLateLatched_mDDADB0A2E961464C86429243D7CCDBE1A75D6A20,
	XRDisplaySubsystem_Internal_TryEndRecordingIfLateLatched_mB550855846A6B88D9FE8B1A5B829F1D754579B40,
	XRDisplaySubsystem_BeginRecordingIfLateLatched_mDD37688ACD9999F051DB8802819701BA58DFE58B,
	XRDisplaySubsystem_Internal_TryBeginRecordingIfLateLatched_mEF81E07646D84AAF371EAAE29EAF5B16CB23856C,
	XRDisplaySubsystem_GetCullingParameters_m6BF6737DD0B607C8719CC80F1AFFE7700498F266,
	XRDisplaySubsystem_Internal_TryGetCullingParams_m6EB3424D3E830934E7B004BAC01B4BC1F44F612C,
	XRDisplaySubsystem_TryGetAppGPUTimeLastFrame_m17BABDCD1716475A2473143A47ECCFE64F17A766,
	XRDisplaySubsystem_TryGetCompositorGPUTimeLastFrame_m9D8587317EBC9785C2AEAD86ACC3B939A497E82D,
	XRDisplaySubsystem_TryGetDroppedFrameCount_m0A036331F1C70470DB1A757734C917CCA0757282,
	XRDisplaySubsystem_TryGetFramePresentCount_mC165D298E6ECD02741A4C9E73E69F91D229166F4,
	XRDisplaySubsystem_TryGetDisplayRefreshRate_mC92C72C1B54E33C4281B714483222D5CD11866BB,
	XRDisplaySubsystem_TryGetMotionToPhoton_mEF734C48F96C17C3A63305B75988F1851298D3E6,
	XRDisplaySubsystem_GetRenderTexture_mABB964AEAFF9B12DB279EDECAE85A52F6253E5CA,
	XRDisplaySubsystem_GetRenderTextureForRenderPass_mC51FADA3F1607DF45ECDBF8018049819E09850DE,
	XRDisplaySubsystem_GetSharedDepthTextureForRenderPass_mE622E4AAFCF6C46CB385CB722A95C3106A7BD476,
	XRDisplaySubsystem_GetPreferredMirrorBlitMode_m24F966A405A26B172FCD6050AC440D7F95C14329,
	XRDisplaySubsystem_SetPreferredMirrorBlitMode_mCB062F49CAD06EFBAAE71D57842BA9054A53B9D2,
	XRDisplaySubsystem_GetMirrorViewBlitDesc_m5117350A305554237006270CECD72113E2286F2F,
	XRDisplaySubsystem_GetMirrorViewBlitDesc_m457DF247F40C563D6AFE45C3E541EE2B75D0C8F6,
	XRDisplaySubsystem_AddGraphicsThreadMirrorViewBlit_mC53840380280B81E9F8A0F5D4EBADB26B37DACD9,
	XRDisplaySubsystem_AddGraphicsThreadMirrorViewBlit_mD55776DC2FD5FFC61D639E44B31D1D26E298E4DB,
	XRDisplaySubsystem_get_hdrOutputSettings_m54E5C853953AC231441243765D2CF8C76EA1C171,
	XRDisplaySubsystem__ctor_m5DA92849F107C6A802BF584D5E328FF2DB971B01,
	XRDisplaySubsystem_SetFocusPlane_Injected_mF5B3A280B982C44DC633EB549B53F8C42BADFBA8,
	XRRenderPass_GetRenderParameter_m3526E26F8ABDA52C52BDF163F4EA74B7DE4B6A0B,
	XRRenderPass_GetRenderParameterCount_m8FECAAF96CD4DF45B0786CB19CD169C1C46BE10A,
	XRRenderPass_GetRenderParameter_Injected_m172BF15F32F6F47B9C9C6BC14C4DDCF8E811DA31,
	XRRenderPass_GetRenderParameterCount_Injected_m0FE39E4D6E091E02A4B1B4F4506B41BC52302803,
	XRMirrorViewBlitDesc_GetBlitParameter_m3464A6CB2461B550C808BC4CF3B5AA2EDBCBD17C,
	XRMirrorViewBlitDesc_GetBlitParameter_Injected_m918BB7915BBD91B0430FCD23187F72B8982C1CC5,
	XRDisplaySubsystemDescriptor_get_disablesLegacyVr_m9F1954399F9C3CD104EDADF9A3437893A23E1513,
	XRDisplaySubsystemDescriptor_get_enableBackBufferMSAA_mD7AF811B5AD3C0C9749C0204978DF2BB8DAF7AA3,
	XRDisplaySubsystemDescriptor_GetAvailableMirrorBlitModeCount_mE92A9846AE52063AA605651CD1C2BBBE1050080A,
	XRDisplaySubsystemDescriptor_GetMirrorBlitModeByIndex_mE218C92B83208625D8E6D764A7E18F21A9A5654A,
	XRDisplaySubsystemDescriptor__ctor_mB9B2993D74FFC580731C03B390C764260458FAA6,
	XRInputSubsystem_GetIndex_m0B132BB23F35C6A8CB0C6F344088AE7BF9D63051,
	XRInputSubsystem_TryRecenter_m4F8888E40ED79139DCB81D56A67C03B4D931A6BB,
	XRInputSubsystem_TryGetInputDevices_mF04BC44FFD5A9C4FBFB7B6FB5B9AD84735D65377,
	XRInputSubsystem_TrySetTrackingOriginMode_m132C190CEAE4403A381BF1C1C4B5FF349F2A3FA7,
	XRInputSubsystem_GetTrackingOriginMode_mBAFED615F74039A681825BB956AD3C8FA7DE45F2,
	XRInputSubsystem_GetSupportedTrackingOriginModes_mBA7190E84E6BB4F251C232B97565E228AECB3018,
	XRInputSubsystem_TryGetBoundaryPoints_m7FEEF524DD8B85151CE8C99378DD690825951C3B,
	XRInputSubsystem_TryGetBoundaryPoints_AsList_m4F2B3C703869F8F0946677E85EE0433FD2750CF2,
	XRInputSubsystem_add_trackingOriginUpdated_mA5E69767B6E8D505BE73804A4B4EA738A27F675E,
	XRInputSubsystem_remove_trackingOriginUpdated_m6A04D2813F1D4A37C013BA00EBC862D1EEA7473E,
	XRInputSubsystem_add_boundaryChanged_m669E66C0AFB8B069034196A2BFEF14888E10AEB0,
	XRInputSubsystem_remove_boundaryChanged_m43F741BBF0553EB25843CF6BE9EF41CF2922E990,
	XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_mD9F93C1B2F0BCDE37190DC500F9D93B273362EEB,
	XRInputSubsystem_InvokeBoundaryChangedEvent_m5BAE59235BADE518D0E32B8D420A0572B63C68C2,
	XRInputSubsystem_TryGetDeviceIds_AsList_m2115E2722113A6D775E5F18E5A7C152189B2398A,
	XRInputSubsystem__ctor_mD0260427CD99745155B171BB6D03862B3CE303E4,
	XRInputSubsystemDescriptor_get_disablesLegacyInput_m666CEB17A2F065A1E3ABA6122B1F448166E936DD,
	XRInputSubsystemDescriptor__ctor_m7DFAE8F8670A5721F02B0AE27BB47389BA0F8DFB,
	MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4,
	MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0,
	MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9,
	MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818,
	MeshId_op_Equality_m2FBB40F4059AE561DB83EC181E4F1B347BEB4F27,
	MeshId_op_Inequality_mD2799782D7ED1EB48EDF219127C4C5FD7A846F07,
	MeshId_get_InvalidId_m5DE10A6930F737A7303B5897713D8F7A21E0F219,
	MeshId__cctor_mE4556EF31E7F96397E4C9E7C3DF80A3C129D431D,
	HashCodeHelper_Combine_m2A63E4964D06338CC6F9DE17F5EFCCC348A6A1D7,
	HashCodeHelper_Combine_mEDE733EC1ABDEA82040BBC18551FB975F3FAC322,
	HashCodeHelper_Combine_mF867D2FAB545AD0A4832679FC73F7A2B289A37F7,
	HashCodeHelper_Combine_mBE52BFDECDDA219A4A334421E8706078A4BE6680,
	HashCodeHelper_Combine_m429AA1346C5A7ACFA6EC9A5D26250CEA4A1BAF89,
	HashCodeHelper_Combine_m110C54CCBA83DD580295D7BA248976C50F6BF606,
	HashCodeHelper_Combine_mBF383AC565B49ACFCB9A0046504C40A8997BAEFD,
	MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D,
	MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9,
	MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180,
	MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0,
	MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B,
	MeshGenerationResult_get_Timestamp_m3D0F0FD234467DF615695ECBB381AFE33030F6CE,
	MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F,
	MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3,
	MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3,
	MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F,
	MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC,
	MeshGenerationResult_op_Equality_mFC78B67E0CDF2086240795242F103772175261B9,
	MeshGenerationResult_op_Inequality_mEB926A2C222D2D696FE4A4838C0E353FD3C0B82E,
	MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC,
	MeshInfo_get_MeshId_m96D69F5BA60A596D7C9FAAF0931940452E3D8846,
	MeshInfo_set_MeshId_mF676EFDD6F67DE2292126F0B473F2E23869C2178,
	MeshInfo_get_ChangeState_mF366492B8EF4A9FC8F677E01A6BA4C6C75D3BF68,
	MeshInfo_set_ChangeState_mC3719CF950FC8E42E86B967F83F22AE0BCCC0784,
	MeshInfo_get_PriorityHint_mDBD3A096EC571EAAFB1E6CB93F4F309BD70F9231,
	MeshInfo_set_PriorityHint_mA0F4BB9543846770A082A85C721E16D16F664615,
	MeshInfo_Equals_m832745EC601145A6C99A608A49B6B750C0B8B5F1,
	MeshInfo_Equals_m8D4446A3EBBC42CDF8E0637EC4CFD00AB89B12C6,
	MeshInfo_op_Equality_mC8E2D6A30564703552744FA35B1B570B0517E450,
	MeshInfo_op_Inequality_m4C3E5CDBC3F34C5246DB4DE4D39F8BEB9134A3AF,
	MeshInfo_GetHashCode_mFA72664A2E6A9B6E86CE36C2D211B8D2AC22250E,
	MeshTransform_get_MeshId_m6B6006910DA8EF0DDB517424DDBCC2DD5B7E92B9,
	MeshTransform_get_Timestamp_m63A9BEB7A7544ABA72AFBCF9A4CBA043C872FDFD,
	MeshTransform_get_Position_m4E4F6240B8F9DBE4634C1CA346AAFAC1555197E9,
	MeshTransform_get_Rotation_m1DE1FC2459EC908BCB7A2F82EDFCE437BC211F8F,
	MeshTransform_get_Scale_m2C279AD6989B26E8F48402B82312322F0D8B520A,
	MeshTransform__ctor_mC589777DA0363B43418905B044E5B5BB4D140F9F,
	MeshTransform_Equals_mEE84420FF15F891F3A08C777C91B463712C25200,
	MeshTransform_Equals_m2F9F68FEC43A7A1E4F5E45E89EA191CC768C5784,
	MeshTransform_op_Equality_mA614ED0C41B2018F55CE63DD3123D3D2AEB34B1E,
	MeshTransform_op_Inequality_mB231191281C9ED068D392A05D242059BC0CA4EB4,
	MeshTransform_GetHashCode_m5EBA0C63B6E23F9024D0A56EFA98B99C5A5CD5F5,
	XRMeshSubsystem_TryGetMeshInfos_m05F584066A873F286C16B99A929C4B3B9AB58FD2,
	XRMeshSubsystem_GetMeshInfosAsList_m72D6DDE116C761283E51B90D4EBC50D59C395917,
	XRMeshSubsystem_GetMeshInfosAsFixedArray_mB66DAD4FDAB7465AE12A993D38DBC2056E558892,
	XRMeshSubsystem_GenerateMeshAsync_m3DEA3C294695FD7C54D1186FF7DCE84A4BA1CFB7,
	XRMeshSubsystem_GenerateMeshAsync_mB0C6892EB55BC93DB6340E11D9C7ABDAB05E2803,
	XRMeshSubsystem_InvokeMeshReadyDelegate_m495A42DE7C44B760CB7D41244A9314F860EA6C53,
	XRMeshSubsystem_get_meshDensity_mBCCC19C5A36D9209666706D5750DAFC19E6C1504,
	XRMeshSubsystem_set_meshDensity_mB3B23768DE704088634FCBEE3F21B9E0C869BE31,
	XRMeshSubsystem_SetBoundingVolume_mC2B3A3A003779328460EF482220BC94C71D40294,
	XRMeshSubsystem_GetUpdatedMeshTransforms_mB0D1B03FB20FF0F5086AF899E97FD2ED97124652,
	XRMeshSubsystem_GetUpdatedMeshTransforms_m1D7E5E319F6C2D8C46C2AD21C5F4B6649C27FAD2,
	XRMeshSubsystem__ctor_mA9C27A31A690B9023F70B628A9D9F5E3F5ED2AEA,
	XRMeshSubsystem_GenerateMeshAsync_Injected_m47C5F75BC47F1A33FD82EFE2A58CF764E71DADB6,
	XRMeshSubsystem_SetBoundingVolume_Injected_m9810F204B1036C59089B1FF7716C51922BFCDE7C,
	MeshTransformList__ctor_m7B11F4035F0C79BEEC061590F37136CF7AC16B71,
	MeshTransformList_get_Count_m971FEDFD7B9522D8D122B2414E58A3E5A3B81F8A,
	MeshTransformList_get_Data_m98A09C2E5F2744566323EB52A47A0F002139C40D,
	MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636,
	MeshTransformList_GetLength_mC3AE75E252CD06356380765AF655073B396A5080,
	MeshTransformList_GetData_m6A656C505BBE740688C6A158BF73E7C4C19B8F8C,
	MeshTransformList_Dispose_m7655ACDE6BC605B30EF2BC387A7B9D0F4D9EED19,
	XRMeshSubsystemDescriptor__ctor_mFD056F69A8BECE56819411D4CD84653B3B735A1B,
	XRStats_TryGetStat_mE91924B240A89BA396554CE3742A06210514FA8B,
	XRStats_TryGetStat_Internal_mDE5287222A37D521F603FAF12C7AB875E43E5DE5,
};
extern void XRNodeState_get_uniqueID_m5776ABB4A4516A1DD61A4BECE94FF7CE09812755_AdjustorThunk (void);
extern void XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F_AdjustorThunk (void);
extern void XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932_AdjustorThunk (void);
extern void XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4_AdjustorThunk (void);
extern void XRNodeState_get_tracked_m777E4EE8D1812BC9849A696EE52FFAD3F4760F79_AdjustorThunk (void);
extern void XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45_AdjustorThunk (void);
extern void XRNodeState_set_position_m4DEE96188C00F002912BA93B35976267209F4278_AdjustorThunk (void);
extern void XRNodeState_set_rotation_m233504F15F6581E219A0412A49BAB2630A060627_AdjustorThunk (void);
extern void XRNodeState_set_velocity_m0E4D32C82609E2606B360E92DDF822A8B9938AAC_AdjustorThunk (void);
extern void XRNodeState_set_angularVelocity_m46D5154D7C0FEF8C3B6BE38A178753FBF475F1B7_AdjustorThunk (void);
extern void XRNodeState_set_acceleration_mA6BDB5D1ADE1A011A885A205EC9B7E41608923CA_AdjustorThunk (void);
extern void XRNodeState_set_angularAcceleration_mA5C680F9CFC1D534377B7FEA7ED065023BB33844_AdjustorThunk (void);
extern void XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6_AdjustorThunk (void);
extern void XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301_AdjustorThunk (void);
extern void XRNodeState_TryGetVelocity_m558D0C6424C5F0DC98E151C0DAC61173EA4AD0C4_AdjustorThunk (void);
extern void XRNodeState_TryGetAngularVelocity_mB7C1D0876373441A07815A8CCB1FC05C11729176_AdjustorThunk (void);
extern void XRNodeState_TryGetAcceleration_m9EAEB95F9ED4B779D54F6BEF7DDFDC34B7EE6892_AdjustorThunk (void);
extern void XRNodeState_TryGetAngularAcceleration_m9D4FF5411CFB801471802F8A084B4333C62F4C04_AdjustorThunk (void);
extern void XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630_AdjustorThunk (void);
extern void XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616_AdjustorThunk (void);
extern void HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9_AdjustorThunk (void);
extern void HapticCapabilities_set_numChannels_m59424616BE7E0EEAA6B9CBBF5659DAA0FC2662D7_AdjustorThunk (void);
extern void HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C_AdjustorThunk (void);
extern void HapticCapabilities_set_supportsImpulse_mEE83FC8E998D832A8A4DAD0803DB6AE42459A223_AdjustorThunk (void);
extern void HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59_AdjustorThunk (void);
extern void HapticCapabilities_set_supportsBuffer_m7E835F610102BC8DAB832FED19709FBBE3D2A701_AdjustorThunk (void);
extern void HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA_AdjustorThunk (void);
extern void HapticCapabilities_set_bufferFrequencyHz_m1D3C289047D65CC38E340C07F232328C6140A75E_AdjustorThunk (void);
extern void HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C_AdjustorThunk (void);
extern void HapticCapabilities_set_bufferMaxSize_m1524C183E3DC90C7E938BD5CF8BE05DCE24CF3C5_AdjustorThunk (void);
extern void HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407_AdjustorThunk (void);
extern void HapticCapabilities_set_bufferOptimalSize_mA6839E1849FB8B2F5A937A7B69980B12F1F7179C_AdjustorThunk (void);
extern void HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57_AdjustorThunk (void);
extern void HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F_AdjustorThunk (void);
extern void HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB_AdjustorThunk (void);
extern void InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085_AdjustorThunk (void);
extern void InputFeatureUsage_set_name_mBC7F8DE6FF4A45609D62A63B5AF9AA7EB867F3C4_AdjustorThunk (void);
extern void InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810_AdjustorThunk (void);
extern void InputFeatureUsage_set_internalType_m5BB4726F3F1A94047BA41FAA1E93DAA40AD6D00F_AdjustorThunk (void);
extern void InputFeatureUsage_get_type_mD9697C1BFA3EA57E89A23A06019AAB73CB24326D_AdjustorThunk (void);
extern void InputFeatureUsage__ctor_m34E81E0915344257F81AC76BF38CF9AFE97D6819_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D_AdjustorThunk (void);
extern void InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D_AdjustorThunk (void);
extern void InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34_AdjustorThunk (void);
extern void InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19_AdjustorThunk (void);
extern void InputDevice_get_subsystem_mF78D9FF3EC606129608E19A0B22F25A80E005FFF_AdjustorThunk (void);
extern void InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948_AdjustorThunk (void);
extern void InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE_AdjustorThunk (void);
extern void InputDevice_get_role_m8A5CD735B1BD8E9712A75CB734092C443452925E_AdjustorThunk (void);
extern void InputDevice_get_manufacturer_mABBD3690895EC6A8FE86094CCFA4E0B081C102BB_AdjustorThunk (void);
extern void InputDevice_get_serialNumber_m777847E27A8EB8871A66910132D17B2B58380B0E_AdjustorThunk (void);
extern void InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB_AdjustorThunk (void);
extern void InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB_AdjustorThunk (void);
extern void InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B_AdjustorThunk (void);
extern void InputDevice_SendHapticBuffer_m8988C8FF59C5A33B65EDA0BD7EAF4996D530C879_AdjustorThunk (void);
extern void InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3_AdjustorThunk (void);
extern void InputDevice_StopHaptics_m816C765A638F5571FD884AEED49FFA74BD535080_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureUsages_mFD9F3CE1465177544260CEB32E1821AA0FDD24AC_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mC30F276811E65A010BC493B2C7707837CF52396C_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m069EBD94A6A1DEB178F0DEFD4ADD328B31FCFF52_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mBD7F8831DE80286ED4A5B4F55686EBD4EF8058F0_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mCF4528B1552EB1B5980DAAED7287CBE83556688F_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mAAA12B1ACD0C5EABB0AD26B8C9F52E41E98AC277_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m12875D2D90E21B5F08D81E620B082854905A178D_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mF1ED1B13F43D27144E424503FD36CB88BB1FD4E3_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mE22CFE88DBE2862117C4BFA85C6E304C17CF38B9_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_m14C541BA603C521379165D18DE71638E0726BE7B_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mC0CB4610B0A7ACA72411D0E7B8FC29EA4AEEF335_AdjustorThunk (void);
extern void InputDevice_TryGetFeatureValue_mC1E70E2DB5E12C8B0884141078038FBE22ED95DB_AdjustorThunk (void);
extern void InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC_AdjustorThunk (void);
extern void InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC_AdjustorThunk (void);
extern void InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D_AdjustorThunk (void);
extern void Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A_AdjustorThunk (void);
extern void Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775_AdjustorThunk (void);
extern void Hand_TryGetRootBone_m57751D162BB60316E15C09608D4365C4C8FFB346_AdjustorThunk (void);
extern void Hand_TryGetFingerBones_m62D184582CD2EC6FE21C6B70DB9CBEA1B4FADAE4_AdjustorThunk (void);
extern void Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06_AdjustorThunk (void);
extern void Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864_AdjustorThunk (void);
extern void Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE_AdjustorThunk (void);
extern void Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD_AdjustorThunk (void);
extern void Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8_AdjustorThunk (void);
extern void Eyes_TryGetLeftEyePosition_mCA6E54596518263010B4939CDA4F921E8C825036_AdjustorThunk (void);
extern void Eyes_TryGetRightEyePosition_m9DF9633DB7FDB907DAA8DBB4D46227C175003A24_AdjustorThunk (void);
extern void Eyes_TryGetLeftEyeRotation_mAE5123DBED78CC8CC6DF4D52CE281DE84F4486B9_AdjustorThunk (void);
extern void Eyes_TryGetRightEyeRotation_mE4CED1712FC85CCCD5840BABD5513FA70D72D37A_AdjustorThunk (void);
extern void Eyes_TryGetFixationPoint_mD7E98889AA0C4920837775486B2792F1674B461F_AdjustorThunk (void);
extern void Eyes_TryGetLeftEyeOpenAmount_m6F75E47258DB11053E5FAE6F1CB1B3FB77149A29_AdjustorThunk (void);
extern void Eyes_TryGetRightEyeOpenAmount_m26AD3342F50D3FA4AC1FA2252D2B7FC4F8D53B94_AdjustorThunk (void);
extern void Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D_AdjustorThunk (void);
extern void Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969_AdjustorThunk (void);
extern void Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8_AdjustorThunk (void);
extern void Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1_AdjustorThunk (void);
extern void Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE_AdjustorThunk (void);
extern void Bone_TryGetPosition_m995E6BD44A081407B8FA2B4BB1B6223C1526E8D7_AdjustorThunk (void);
extern void Bone_TryGetRotation_mBCD1D06C58E9C28F9C8F2A24A454D6B90ED6EB65_AdjustorThunk (void);
extern void Bone_TryGetParentBone_m7E683151870A5D22C319C89CBA9577D4CADDFF85_AdjustorThunk (void);
extern void Bone_TryGetChildBones_m979B87C6EF1951412265D6218617E7C8022BB587_AdjustorThunk (void);
extern void Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7_AdjustorThunk (void);
extern void Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F_AdjustorThunk (void);
extern void Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82_AdjustorThunk (void);
extern void XRRenderPass_GetRenderParameter_m3526E26F8ABDA52C52BDF163F4EA74B7DE4B6A0B_AdjustorThunk (void);
extern void XRRenderPass_GetRenderParameterCount_m8FECAAF96CD4DF45B0786CB19CD169C1C46BE10A_AdjustorThunk (void);
extern void XRMirrorViewBlitDesc_GetBlitParameter_m3464A6CB2461B550C808BC4CF3B5AA2EDBCBD17C_AdjustorThunk (void);
extern void MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4_AdjustorThunk (void);
extern void MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0_AdjustorThunk (void);
extern void MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9_AdjustorThunk (void);
extern void MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D_AdjustorThunk (void);
extern void MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180_AdjustorThunk (void);
extern void MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0_AdjustorThunk (void);
extern void MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B_AdjustorThunk (void);
extern void MeshGenerationResult_get_Timestamp_m3D0F0FD234467DF615695ECBB381AFE33030F6CE_AdjustorThunk (void);
extern void MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F_AdjustorThunk (void);
extern void MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3_AdjustorThunk (void);
extern void MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC_AdjustorThunk (void);
extern void MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC_AdjustorThunk (void);
extern void MeshInfo_get_MeshId_m96D69F5BA60A596D7C9FAAF0931940452E3D8846_AdjustorThunk (void);
extern void MeshInfo_set_MeshId_mF676EFDD6F67DE2292126F0B473F2E23869C2178_AdjustorThunk (void);
extern void MeshInfo_get_ChangeState_mF366492B8EF4A9FC8F677E01A6BA4C6C75D3BF68_AdjustorThunk (void);
extern void MeshInfo_set_ChangeState_mC3719CF950FC8E42E86B967F83F22AE0BCCC0784_AdjustorThunk (void);
extern void MeshInfo_get_PriorityHint_mDBD3A096EC571EAAFB1E6CB93F4F309BD70F9231_AdjustorThunk (void);
extern void MeshInfo_set_PriorityHint_mA0F4BB9543846770A082A85C721E16D16F664615_AdjustorThunk (void);
extern void MeshInfo_Equals_m832745EC601145A6C99A608A49B6B750C0B8B5F1_AdjustorThunk (void);
extern void MeshInfo_Equals_m8D4446A3EBBC42CDF8E0637EC4CFD00AB89B12C6_AdjustorThunk (void);
extern void MeshInfo_GetHashCode_mFA72664A2E6A9B6E86CE36C2D211B8D2AC22250E_AdjustorThunk (void);
extern void MeshTransform_get_MeshId_m6B6006910DA8EF0DDB517424DDBCC2DD5B7E92B9_AdjustorThunk (void);
extern void MeshTransform_get_Timestamp_m63A9BEB7A7544ABA72AFBCF9A4CBA043C872FDFD_AdjustorThunk (void);
extern void MeshTransform_get_Position_m4E4F6240B8F9DBE4634C1CA346AAFAC1555197E9_AdjustorThunk (void);
extern void MeshTransform_get_Rotation_m1DE1FC2459EC908BCB7A2F82EDFCE437BC211F8F_AdjustorThunk (void);
extern void MeshTransform_get_Scale_m2C279AD6989B26E8F48402B82312322F0D8B520A_AdjustorThunk (void);
extern void MeshTransform__ctor_mC589777DA0363B43418905B044E5B5BB4D140F9F_AdjustorThunk (void);
extern void MeshTransform_Equals_mEE84420FF15F891F3A08C777C91B463712C25200_AdjustorThunk (void);
extern void MeshTransform_Equals_m2F9F68FEC43A7A1E4F5E45E89EA191CC768C5784_AdjustorThunk (void);
extern void MeshTransform_GetHashCode_m5EBA0C63B6E23F9024D0A56EFA98B99C5A5CD5F5_AdjustorThunk (void);
extern void MeshTransformList__ctor_m7B11F4035F0C79BEEC061590F37136CF7AC16B71_AdjustorThunk (void);
extern void MeshTransformList_get_Count_m971FEDFD7B9522D8D122B2414E58A3E5A3B81F8A_AdjustorThunk (void);
extern void MeshTransformList_get_Data_m98A09C2E5F2744566323EB52A47A0F002139C40D_AdjustorThunk (void);
extern void MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[149] = 
{
	{ 0x06000016, XRNodeState_get_uniqueID_m5776ABB4A4516A1DD61A4BECE94FF7CE09812755_AdjustorThunk },
	{ 0x06000017, XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F_AdjustorThunk },
	{ 0x06000018, XRNodeState_get_nodeType_mFC49286B25FF7732CE6CAF231A1DE5C083C13932_AdjustorThunk },
	{ 0x06000019, XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4_AdjustorThunk },
	{ 0x0600001A, XRNodeState_get_tracked_m777E4EE8D1812BC9849A696EE52FFAD3F4760F79_AdjustorThunk },
	{ 0x0600001B, XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45_AdjustorThunk },
	{ 0x0600001C, XRNodeState_set_position_m4DEE96188C00F002912BA93B35976267209F4278_AdjustorThunk },
	{ 0x0600001D, XRNodeState_set_rotation_m233504F15F6581E219A0412A49BAB2630A060627_AdjustorThunk },
	{ 0x0600001E, XRNodeState_set_velocity_m0E4D32C82609E2606B360E92DDF822A8B9938AAC_AdjustorThunk },
	{ 0x0600001F, XRNodeState_set_angularVelocity_m46D5154D7C0FEF8C3B6BE38A178753FBF475F1B7_AdjustorThunk },
	{ 0x06000020, XRNodeState_set_acceleration_mA6BDB5D1ADE1A011A885A205EC9B7E41608923CA_AdjustorThunk },
	{ 0x06000021, XRNodeState_set_angularAcceleration_mA5C680F9CFC1D534377B7FEA7ED065023BB33844_AdjustorThunk },
	{ 0x06000022, XRNodeState_TryGetPosition_mC17C5DFEEA4062A6D91D7E252DD108829AC4E0A6_AdjustorThunk },
	{ 0x06000023, XRNodeState_TryGetRotation_mA7F9597BE3AFE747BF2F95BF491779520FEF7301_AdjustorThunk },
	{ 0x06000024, XRNodeState_TryGetVelocity_m558D0C6424C5F0DC98E151C0DAC61173EA4AD0C4_AdjustorThunk },
	{ 0x06000025, XRNodeState_TryGetAngularVelocity_mB7C1D0876373441A07815A8CCB1FC05C11729176_AdjustorThunk },
	{ 0x06000026, XRNodeState_TryGetAcceleration_m9EAEB95F9ED4B779D54F6BEF7DDFDC34B7EE6892_AdjustorThunk },
	{ 0x06000027, XRNodeState_TryGetAngularAcceleration_m9D4FF5411CFB801471802F8A084B4333C62F4C04_AdjustorThunk },
	{ 0x06000028, XRNodeState_TryGet_m6C1D3FC5E773BC5E134F37815FB08839489F5630_AdjustorThunk },
	{ 0x06000029, XRNodeState_TryGet_m3EC1CBB3B6B823611B82073139949C041CE7C616_AdjustorThunk },
	{ 0x0600002A, HapticCapabilities_get_numChannels_m23871C9860B4A1F6E22DE4579A7550A7A9382BA9_AdjustorThunk },
	{ 0x0600002B, HapticCapabilities_set_numChannels_m59424616BE7E0EEAA6B9CBBF5659DAA0FC2662D7_AdjustorThunk },
	{ 0x0600002C, HapticCapabilities_get_supportsImpulse_m855193672304BD935913E215B690B14952E0C59C_AdjustorThunk },
	{ 0x0600002D, HapticCapabilities_set_supportsImpulse_mEE83FC8E998D832A8A4DAD0803DB6AE42459A223_AdjustorThunk },
	{ 0x0600002E, HapticCapabilities_get_supportsBuffer_mE87BCD83AA4FEE3C89EE906EF2F1B6DFA9B7BA59_AdjustorThunk },
	{ 0x0600002F, HapticCapabilities_set_supportsBuffer_m7E835F610102BC8DAB832FED19709FBBE3D2A701_AdjustorThunk },
	{ 0x06000030, HapticCapabilities_get_bufferFrequencyHz_m31270DBF2491B9FDC969D0082ABB5CF0F0DFDBAA_AdjustorThunk },
	{ 0x06000031, HapticCapabilities_set_bufferFrequencyHz_m1D3C289047D65CC38E340C07F232328C6140A75E_AdjustorThunk },
	{ 0x06000032, HapticCapabilities_get_bufferMaxSize_mAC7A86B40B716A2B9B17D4B3B26EE6B7D0346C7C_AdjustorThunk },
	{ 0x06000033, HapticCapabilities_set_bufferMaxSize_m1524C183E3DC90C7E938BD5CF8BE05DCE24CF3C5_AdjustorThunk },
	{ 0x06000034, HapticCapabilities_get_bufferOptimalSize_m166A6DD9882CD48F702FE7447E87C60AAF479407_AdjustorThunk },
	{ 0x06000035, HapticCapabilities_set_bufferOptimalSize_mA6839E1849FB8B2F5A937A7B69980B12F1F7179C_AdjustorThunk },
	{ 0x06000036, HapticCapabilities_Equals_m87B9C608F4E14AE0052DA784111BA3FE74EF5B57_AdjustorThunk },
	{ 0x06000037, HapticCapabilities_Equals_m4CD220F74171CB85C19D2CA75D3BC9933B699F0F_AdjustorThunk },
	{ 0x06000038, HapticCapabilities_GetHashCode_m7C7504CA0AADBD291E33540E08B00EFF47A2A2DB_AdjustorThunk },
	{ 0x0600003B, InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085_AdjustorThunk },
	{ 0x0600003C, InputFeatureUsage_set_name_mBC7F8DE6FF4A45609D62A63B5AF9AA7EB867F3C4_AdjustorThunk },
	{ 0x0600003D, InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810_AdjustorThunk },
	{ 0x0600003E, InputFeatureUsage_set_internalType_m5BB4726F3F1A94047BA41FAA1E93DAA40AD6D00F_AdjustorThunk },
	{ 0x0600003F, InputFeatureUsage_get_type_mD9697C1BFA3EA57E89A23A06019AAB73CB24326D_AdjustorThunk },
	{ 0x06000040, InputFeatureUsage__ctor_m34E81E0915344257F81AC76BF38CF9AFE97D6819_AdjustorThunk },
	{ 0x06000041, InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0_AdjustorThunk },
	{ 0x06000042, InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D_AdjustorThunk },
	{ 0x06000043, InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D_AdjustorThunk },
	{ 0x06000052, InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34_AdjustorThunk },
	{ 0x06000053, InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19_AdjustorThunk },
	{ 0x06000054, InputDevice_get_subsystem_mF78D9FF3EC606129608E19A0B22F25A80E005FFF_AdjustorThunk },
	{ 0x06000055, InputDevice_get_isValid_mA908CF8195CECA44FF457430AFF9198C3FEC0948_AdjustorThunk },
	{ 0x06000056, InputDevice_get_name_mAEA08A3755E715697022C54227556428CEF4E1CE_AdjustorThunk },
	{ 0x06000057, InputDevice_get_role_m8A5CD735B1BD8E9712A75CB734092C443452925E_AdjustorThunk },
	{ 0x06000058, InputDevice_get_manufacturer_mABBD3690895EC6A8FE86094CCFA4E0B081C102BB_AdjustorThunk },
	{ 0x06000059, InputDevice_get_serialNumber_m777847E27A8EB8871A66910132D17B2B58380B0E_AdjustorThunk },
	{ 0x0600005A, InputDevice_get_characteristics_mEEDC9AB2254C155C47B48848FECF347A87925ADB_AdjustorThunk },
	{ 0x0600005B, InputDevice_IsValidId_m13527A66E7D03B0C9AA64C2FFCC4A2F3889756BB_AdjustorThunk },
	{ 0x0600005C, InputDevice_SendHapticImpulse_m7166A784508F8E0F3AE5BD88863171C7A905BC1B_AdjustorThunk },
	{ 0x0600005D, InputDevice_SendHapticBuffer_m8988C8FF59C5A33B65EDA0BD7EAF4996D530C879_AdjustorThunk },
	{ 0x0600005E, InputDevice_TryGetHapticCapabilities_mC97EE9A231941988E8B6FA8AF0CB310EFE1B77A3_AdjustorThunk },
	{ 0x0600005F, InputDevice_StopHaptics_m816C765A638F5571FD884AEED49FFA74BD535080_AdjustorThunk },
	{ 0x06000060, InputDevice_TryGetFeatureUsages_mFD9F3CE1465177544260CEB32E1821AA0FDD24AC_AdjustorThunk },
	{ 0x06000061, InputDevice_TryGetFeatureValue_m24EC3B6C41AE4098269427232AD5F52E786BF884_AdjustorThunk },
	{ 0x06000062, InputDevice_TryGetFeatureValue_m9FC969BEFF0E5BAB78DD9F2130F437788D20068C_AdjustorThunk },
	{ 0x06000063, InputDevice_TryGetFeatureValue_m675D52240379FEF80D6499B5031941812FDFD081_AdjustorThunk },
	{ 0x06000064, InputDevice_TryGetFeatureValue_mB2C15D1FC747DA9FB5958FA17E77049886FB3BBA_AdjustorThunk },
	{ 0x06000065, InputDevice_TryGetFeatureValue_m472B5ECE996FB7440CACCF1E85722DA4963E3167_AdjustorThunk },
	{ 0x06000066, InputDevice_TryGetFeatureValue_m0C1A9761DD0D1C6D1EF4BAB2FAF1BC1A9541BB9F_AdjustorThunk },
	{ 0x06000067, InputDevice_TryGetFeatureValue_mC30F276811E65A010BC493B2C7707837CF52396C_AdjustorThunk },
	{ 0x06000068, InputDevice_TryGetFeatureValue_m069EBD94A6A1DEB178F0DEFD4ADD328B31FCFF52_AdjustorThunk },
	{ 0x06000069, InputDevice_TryGetFeatureValue_mBD7F8831DE80286ED4A5B4F55686EBD4EF8058F0_AdjustorThunk },
	{ 0x0600006A, InputDevice_TryGetFeatureValue_mCF4528B1552EB1B5980DAAED7287CBE83556688F_AdjustorThunk },
	{ 0x0600006B, InputDevice_TryGetFeatureValue_m8A01F07356DC85042F6BB7C6258A75C3EC3C4E11_AdjustorThunk },
	{ 0x0600006C, InputDevice_TryGetFeatureValue_mAAA12B1ACD0C5EABB0AD26B8C9F52E41E98AC277_AdjustorThunk },
	{ 0x0600006D, InputDevice_TryGetFeatureValue_m12875D2D90E21B5F08D81E620B082854905A178D_AdjustorThunk },
	{ 0x0600006E, InputDevice_TryGetFeatureValue_mF1ED1B13F43D27144E424503FD36CB88BB1FD4E3_AdjustorThunk },
	{ 0x0600006F, InputDevice_TryGetFeatureValue_mE22CFE88DBE2862117C4BFA85C6E304C17CF38B9_AdjustorThunk },
	{ 0x06000070, InputDevice_TryGetFeatureValue_m14C541BA603C521379165D18DE71638E0726BE7B_AdjustorThunk },
	{ 0x06000071, InputDevice_TryGetFeatureValue_mC0CB4610B0A7ACA72411D0E7B8FC29EA4AEEF335_AdjustorThunk },
	{ 0x06000072, InputDevice_TryGetFeatureValue_mC1E70E2DB5E12C8B0884141078038FBE22ED95DB_AdjustorThunk },
	{ 0x06000074, InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC_AdjustorThunk },
	{ 0x06000075, InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC_AdjustorThunk },
	{ 0x06000076, InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D_AdjustorThunk },
	{ 0x0600007D, Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A_AdjustorThunk },
	{ 0x0600007E, Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775_AdjustorThunk },
	{ 0x0600007F, Hand_TryGetRootBone_m57751D162BB60316E15C09608D4365C4C8FFB346_AdjustorThunk },
	{ 0x06000081, Hand_TryGetFingerBones_m62D184582CD2EC6FE21C6B70DB9CBEA1B4FADAE4_AdjustorThunk },
	{ 0x06000083, Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06_AdjustorThunk },
	{ 0x06000084, Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864_AdjustorThunk },
	{ 0x06000085, Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE_AdjustorThunk },
	{ 0x0600008A, Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD_AdjustorThunk },
	{ 0x0600008B, Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8_AdjustorThunk },
	{ 0x0600008C, Eyes_TryGetLeftEyePosition_mCA6E54596518263010B4939CDA4F921E8C825036_AdjustorThunk },
	{ 0x0600008D, Eyes_TryGetRightEyePosition_m9DF9633DB7FDB907DAA8DBB4D46227C175003A24_AdjustorThunk },
	{ 0x0600008E, Eyes_TryGetLeftEyeRotation_mAE5123DBED78CC8CC6DF4D52CE281DE84F4486B9_AdjustorThunk },
	{ 0x0600008F, Eyes_TryGetRightEyeRotation_mE4CED1712FC85CCCD5840BABD5513FA70D72D37A_AdjustorThunk },
	{ 0x06000092, Eyes_TryGetFixationPoint_mD7E98889AA0C4920837775486B2792F1674B461F_AdjustorThunk },
	{ 0x06000094, Eyes_TryGetLeftEyeOpenAmount_m6F75E47258DB11053E5FAE6F1CB1B3FB77149A29_AdjustorThunk },
	{ 0x06000095, Eyes_TryGetRightEyeOpenAmount_m26AD3342F50D3FA4AC1FA2252D2B7FC4F8D53B94_AdjustorThunk },
	{ 0x06000097, Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D_AdjustorThunk },
	{ 0x06000098, Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969_AdjustorThunk },
	{ 0x06000099, Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8_AdjustorThunk },
	{ 0x060000A0, Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1_AdjustorThunk },
	{ 0x060000A1, Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE_AdjustorThunk },
	{ 0x060000A2, Bone_TryGetPosition_m995E6BD44A081407B8FA2B4BB1B6223C1526E8D7_AdjustorThunk },
	{ 0x060000A4, Bone_TryGetRotation_mBCD1D06C58E9C28F9C8F2A24A454D6B90ED6EB65_AdjustorThunk },
	{ 0x060000A6, Bone_TryGetParentBone_m7E683151870A5D22C319C89CBA9577D4CADDFF85_AdjustorThunk },
	{ 0x060000A8, Bone_TryGetChildBones_m979B87C6EF1951412265D6218617E7C8022BB587_AdjustorThunk },
	{ 0x060000AA, Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7_AdjustorThunk },
	{ 0x060000AB, Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F_AdjustorThunk },
	{ 0x060000AC, Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82_AdjustorThunk },
	{ 0x06000119, XRRenderPass_GetRenderParameter_m3526E26F8ABDA52C52BDF163F4EA74B7DE4B6A0B_AdjustorThunk },
	{ 0x0600011A, XRRenderPass_GetRenderParameterCount_m8FECAAF96CD4DF45B0786CB19CD169C1C46BE10A_AdjustorThunk },
	{ 0x0600011D, XRMirrorViewBlitDesc_GetBlitParameter_m3464A6CB2461B550C808BC4CF3B5AA2EDBCBD17C_AdjustorThunk },
	{ 0x06000136, MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4_AdjustorThunk },
	{ 0x06000137, MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0_AdjustorThunk },
	{ 0x06000138, MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9_AdjustorThunk },
	{ 0x06000139, MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818_AdjustorThunk },
	{ 0x06000145, MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D_AdjustorThunk },
	{ 0x06000146, MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9_AdjustorThunk },
	{ 0x06000147, MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180_AdjustorThunk },
	{ 0x06000148, MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0_AdjustorThunk },
	{ 0x06000149, MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B_AdjustorThunk },
	{ 0x0600014A, MeshGenerationResult_get_Timestamp_m3D0F0FD234467DF615695ECBB381AFE33030F6CE_AdjustorThunk },
	{ 0x0600014B, MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F_AdjustorThunk },
	{ 0x0600014C, MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3_AdjustorThunk },
	{ 0x0600014D, MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3_AdjustorThunk },
	{ 0x0600014E, MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F_AdjustorThunk },
	{ 0x0600014F, MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC_AdjustorThunk },
	{ 0x06000152, MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC_AdjustorThunk },
	{ 0x06000153, MeshInfo_get_MeshId_m96D69F5BA60A596D7C9FAAF0931940452E3D8846_AdjustorThunk },
	{ 0x06000154, MeshInfo_set_MeshId_mF676EFDD6F67DE2292126F0B473F2E23869C2178_AdjustorThunk },
	{ 0x06000155, MeshInfo_get_ChangeState_mF366492B8EF4A9FC8F677E01A6BA4C6C75D3BF68_AdjustorThunk },
	{ 0x06000156, MeshInfo_set_ChangeState_mC3719CF950FC8E42E86B967F83F22AE0BCCC0784_AdjustorThunk },
	{ 0x06000157, MeshInfo_get_PriorityHint_mDBD3A096EC571EAAFB1E6CB93F4F309BD70F9231_AdjustorThunk },
	{ 0x06000158, MeshInfo_set_PriorityHint_mA0F4BB9543846770A082A85C721E16D16F664615_AdjustorThunk },
	{ 0x06000159, MeshInfo_Equals_m832745EC601145A6C99A608A49B6B750C0B8B5F1_AdjustorThunk },
	{ 0x0600015A, MeshInfo_Equals_m8D4446A3EBBC42CDF8E0637EC4CFD00AB89B12C6_AdjustorThunk },
	{ 0x0600015D, MeshInfo_GetHashCode_mFA72664A2E6A9B6E86CE36C2D211B8D2AC22250E_AdjustorThunk },
	{ 0x0600015E, MeshTransform_get_MeshId_m6B6006910DA8EF0DDB517424DDBCC2DD5B7E92B9_AdjustorThunk },
	{ 0x0600015F, MeshTransform_get_Timestamp_m63A9BEB7A7544ABA72AFBCF9A4CBA043C872FDFD_AdjustorThunk },
	{ 0x06000160, MeshTransform_get_Position_m4E4F6240B8F9DBE4634C1CA346AAFAC1555197E9_AdjustorThunk },
	{ 0x06000161, MeshTransform_get_Rotation_m1DE1FC2459EC908BCB7A2F82EDFCE437BC211F8F_AdjustorThunk },
	{ 0x06000162, MeshTransform_get_Scale_m2C279AD6989B26E8F48402B82312322F0D8B520A_AdjustorThunk },
	{ 0x06000163, MeshTransform__ctor_mC589777DA0363B43418905B044E5B5BB4D140F9F_AdjustorThunk },
	{ 0x06000164, MeshTransform_Equals_mEE84420FF15F891F3A08C777C91B463712C25200_AdjustorThunk },
	{ 0x06000165, MeshTransform_Equals_m2F9F68FEC43A7A1E4F5E45E89EA191CC768C5784_AdjustorThunk },
	{ 0x06000168, MeshTransform_GetHashCode_m5EBA0C63B6E23F9024D0A56EFA98B99C5A5CD5F5_AdjustorThunk },
	{ 0x06000177, MeshTransformList__ctor_m7B11F4035F0C79BEEC061590F37136CF7AC16B71_AdjustorThunk },
	{ 0x06000178, MeshTransformList_get_Count_m971FEDFD7B9522D8D122B2414E58A3E5A3B81F8A_AdjustorThunk },
	{ 0x06000179, MeshTransformList_get_Data_m98A09C2E5F2744566323EB52A47A0F002139C40D_AdjustorThunk },
	{ 0x0600017A, MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636_AdjustorThunk },
};
static const int32_t s_InvokerIndices[384] = 
{
	8887,
	8887,
	8887,
	8887,
	8887,
	8887,
	8887,
	8887,
	6116,
	8835,
	8573,
	9089,
	8522,
	8887,
	8887,
	8993,
	8868,
	8805,
	7932,
	7925,
	7925,
	4351,
	3974,
	4216,
	3852,
	4168,
	3807,
	3980,
	3896,
	3980,
	3980,
	3980,
	3980,
	3079,
	3079,
	3079,
	3079,
	3079,
	3079,
	1659,
	1647,
	4350,
	3973,
	4168,
	3807,
	4168,
	3807,
	4350,
	3973,
	4350,
	3973,
	4350,
	3973,
	3185,
	3158,
	4216,
	7215,
	7215,
	4250,
	3881,
	4350,
	3973,
	4250,
	2814,
	3185,
	3164,
	4216,
	7222,
	7222,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9089,
	3974,
	4351,
	4250,
	4168,
	4250,
	4350,
	4250,
	4250,
	4350,
	4168,
	1655,
	2342,
	3079,
	4364,
	3185,
	2264,
	2270,
	2269,
	2271,
	2272,
	2268,
	2266,
	2263,
	2265,
	2262,
	2267,
	1589,
	1593,
	1592,
	1594,
	1595,
	1591,
	1590,
	0,
	3185,
	3163,
	4216,
	7221,
	7221,
	9004,
	8418,
	8296,
	9089,
	4351,
	4350,
	3079,
	7213,
	2299,
	6326,
	3185,
	3156,
	4216,
	7214,
	7214,
	7140,
	6309,
	4351,
	4350,
	3079,
	3079,
	3079,
	3079,
	6325,
	6325,
	3079,
	7200,
	3079,
	3079,
	6325,
	3185,
	3142,
	4216,
	7201,
	7201,
	6306,
	6306,
	7140,
	6306,
	4351,
	4350,
	3079,
	7164,
	3079,
	7164,
	3079,
	7164,
	3185,
	7166,
	3185,
	3108,
	4216,
	7165,
	7165,
	7140,
	7140,
	7140,
	7145,
	8362,
	7932,
	8887,
	8025,
	8025,
	8887,
	8887,
	8887,
	8887,
	8887,
	8887,
	8029,
	8887,
	5724,
	6406,
	7354,
	8904,
	7355,
	6404,
	6404,
	6404,
	6404,
	6404,
	6404,
	6405,
	5723,
	5723,
	5723,
	5723,
	5723,
	5723,
	6404,
	6404,
	6404,
	8243,
	8522,
	8522,
	8522,
	8792,
	8792,
	4364,
	3881,
	3881,
	3807,
	4168,
	3807,
	4168,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	2796,
	4216,
	3852,
	4216,
	4216,
	3852,
	2196,
	3852,
	4168,
	3807,
	4216,
	2728,
	2293,
	3881,
	3185,
	3881,
	3185,
	2042,
	1637,
	3079,
	3079,
	3079,
	3079,
	3079,
	3079,
	3527,
	3515,
	3515,
	4216,
	3852,
	2315,
	1630,
	2316,
	1634,
	4250,
	4364,
	1904,
	2042,
	4216,
	6090,
	8380,
	2728,
	6859,
	4168,
	4168,
	4216,
	2728,
	4364,
	4350,
	4168,
	3185,
	3166,
	4216,
	4216,
	3185,
	3185,
	3881,
	3881,
	3881,
	3881,
	8882,
	8882,
	3881,
	4364,
	4168,
	4364,
	4250,
	4216,
	3185,
	3179,
	7249,
	7249,
	9025,
	9089,
	7473,
	6499,
	5792,
	5254,
	4936,
	4747,
	4626,
	4245,
	4250,
	4250,
	4216,
	4216,
	4351,
	4358,
	4267,
	4358,
	3185,
	3178,
	7248,
	7248,
	4216,
	4245,
	3876,
	4216,
	3852,
	4216,
	3852,
	3185,
	3180,
	7250,
	7250,
	4216,
	4245,
	4351,
	4358,
	4267,
	4358,
	838,
	3185,
	3181,
	7251,
	7251,
	4216,
	3185,
	3185,
	4250,
	906,
	563,
	2780,
	4298,
	3928,
	2350,
	2947,
	4218,
	4364,
	527,
	2279,
	3854,
	4216,
	4218,
	4364,
	8397,
	8444,
	8882,
	4364,
	6365,
	6343,
};
static const Il2CppTokenRangePair s_rgctxIndices[3] = 
{
	{ 0x0200000E, { 3, 8 } },
	{ 0x06000046, { 0, 3 } },
	{ 0x06000073, { 11, 2 } },
};
extern const uint32_t g_rgctx_T_tF36D5D1CCB232BA23BE35354501A2E5966A901DF;
extern const uint32_t g_rgctx_InputFeatureUsage_1_t668ECC06F98810416C5181CADE44F7B5586A6A7F;
extern const uint32_t g_rgctx_InputFeatureUsage_1__ctor_mA3F8DF62C8B566478D7E1EED745B3398B11E11C9;
extern const uint32_t g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211;
extern const uint32_t g_rgctx_InputFeatureUsage_1_set_name_m9C2C093BBED5BE9C72D51D95FF8D7BD9E1B58FDC;
extern const uint32_t g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211;
extern const uint32_t g_rgctx_InputFeatureUsage_1_Equals_m32D9AF9C7F5E3ED62A18CC5A632C325B33CA06EB;
extern const uint32_t g_rgctx_InputFeatureUsage_1_get_name_m3E355A98DFFCFF3CACAA50623EEA65FAFCCF1F34;
extern const uint32_t g_rgctx_InputFeatureUsage_1_op_Equality_mD8F90A076B93F71303F90F75E7AEA347B3D42F0C;
extern const uint32_t g_rgctx_T_t24F1DF635309E6B190BC5A7E4286D7C68E4EE287;
extern const uint32_t g_rgctx_InputFeatureUsage_1_get_usageType_mD144C01A1E0BAB90ABFE65C7C0E607D2152476EC;
extern const uint32_t g_rgctx_TU26_t13A298AC79CDB905C6CF36F67BD0F1F63F9600FC;
extern const uint32_t g_rgctx_T_t0BE7F59F971FBD264FC32E6DC95BC7553CD48284;
static const Il2CppRGCTXDefinition s_rgctxValues[13] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tF36D5D1CCB232BA23BE35354501A2E5966A901DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputFeatureUsage_1_t668ECC06F98810416C5181CADE44F7B5586A6A7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1__ctor_mA3F8DF62C8B566478D7E1EED745B3398B11E11C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_set_name_m9C2C093BBED5BE9C72D51D95FF8D7BD9E1B58FDC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_InputFeatureUsage_1_tC3E25C35CC3C47EA8C96D9AD547165F3A28C8211 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_Equals_m32D9AF9C7F5E3ED62A18CC5A632C325B33CA06EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_get_name_m3E355A98DFFCFF3CACAA50623EEA65FAFCCF1F34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_op_Equality_mD8F90A076B93F71303F90F75E7AEA347B3D42F0C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t24F1DF635309E6B190BC5A7E4286D7C68E4EE287 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_InputFeatureUsage_1_get_usageType_mD144C01A1E0BAB90ABFE65C7C0E607D2152476EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t13A298AC79CDB905C6CF36F67BD0F1F63F9600FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0BE7F59F971FBD264FC32E6DC95BC7553CD48284 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_XRModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule = 
{
	"UnityEngine.XRModule.dll",
	384,
	s_methodPointers,
	149,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	3,
	s_rgctxIndices,
	13,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_XRModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
