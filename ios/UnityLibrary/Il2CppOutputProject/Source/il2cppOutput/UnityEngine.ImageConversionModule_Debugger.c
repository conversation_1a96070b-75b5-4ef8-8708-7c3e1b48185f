﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[12] = 
{
	{ 24489, 0,  5 },
	{ 15918, 1,  5 },
	{ 11826, 2,  5 },
	{ 24489, 0,  7 },
	{ 15918, 1,  7 },
	{ 11826, 2,  7 },
	{ 24489, 0,  9 },
	{ 15918, 1,  9 },
	{ 11826, 2,  9 },
	{ 24489, 0,  11 },
	{ 15918, 1,  11 },
	{ 11826, 2,  11 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[3] = 
{
	"size",
	"result",
	"output",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[24] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 3 },
	{ 3, 3 },
	{ 6, 3 },
	{ 9, 3 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_ImageConversionModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ImageConversionModule[86] = 
{
	{ 109909, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109909, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109909, 1, 16, 16, 13, 14, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109909, 1, 17, 17, 17, 69, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109909, 1, 17, 17, 17, 69, 1, kSequencePointKind_StepOut, 0, 4 },
	{ 109909, 1, 18, 18, 13, 14, 9, kSequencePointKind_Normal, 0, 5 },
	{ 109910, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 109910, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 109910, 1, 20, 20, 13, 14, 0, kSequencePointKind_Normal, 0, 8 },
	{ 109910, 1, 21, 21, 17, 67, 1, kSequencePointKind_Normal, 0, 9 },
	{ 109910, 1, 21, 21, 17, 67, 2, kSequencePointKind_StepOut, 0, 10 },
	{ 109910, 1, 22, 22, 13, 14, 8, kSequencePointKind_Normal, 0, 11 },
	{ 109916, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 109916, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 109916, 1, 40, 40, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 109916, 1, 41, 41, 13, 40, 1, kSequencePointKind_Normal, 0, 15 },
	{ 109916, 1, 41, 41, 13, 40, 4, kSequencePointKind_StepOut, 0, 16 },
	{ 109916, 1, 42, 42, 9, 10, 12, kSequencePointKind_Normal, 0, 17 },
	{ 109918, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 18 },
	{ 109918, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 19 },
	{ 109918, 1, 47, 47, 9, 10, 0, kSequencePointKind_Normal, 0, 20 },
	{ 109918, 1, 48, 48, 13, 62, 1, kSequencePointKind_Normal, 0, 21 },
	{ 109918, 1, 48, 48, 13, 62, 3, kSequencePointKind_StepOut, 0, 22 },
	{ 109918, 1, 49, 49, 9, 10, 11, kSequencePointKind_Normal, 0, 23 },
	{ 109920, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 24 },
	{ 109920, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 25 },
	{ 109920, 1, 54, 54, 9, 10, 0, kSequencePointKind_Normal, 0, 26 },
	{ 109920, 1, 55, 55, 13, 48, 1, kSequencePointKind_Normal, 0, 27 },
	{ 109920, 1, 55, 55, 13, 48, 4, kSequencePointKind_StepOut, 0, 28 },
	{ 109920, 1, 56, 56, 9, 10, 12, kSequencePointKind_Normal, 0, 29 },
	{ 109925, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 30 },
	{ 109925, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 31 },
	{ 109925, 1, 71, 71, 9, 10, 0, kSequencePointKind_Normal, 0, 32 },
	{ 109925, 1, 73, 73, 13, 14, 1, kSequencePointKind_Normal, 0, 33 },
	{ 109925, 1, 74, 74, 17, 71, 2, kSequencePointKind_Normal, 0, 34 },
	{ 109925, 1, 74, 74, 17, 71, 4, kSequencePointKind_StepOut, 0, 35 },
	{ 109925, 1, 74, 74, 17, 71, 9, kSequencePointKind_StepOut, 0, 36 },
	{ 109925, 1, 75, 75, 17, 174, 16, kSequencePointKind_Normal, 0, 37 },
	{ 109925, 1, 75, 75, 17, 174, 17, kSequencePointKind_StepOut, 0, 38 },
	{ 109925, 1, 75, 75, 17, 174, 29, kSequencePointKind_StepOut, 0, 39 },
	{ 109925, 1, 76, 76, 17, 130, 35, kSequencePointKind_Normal, 0, 40 },
	{ 109925, 1, 76, 76, 17, 130, 38, kSequencePointKind_StepOut, 0, 41 },
	{ 109925, 1, 82, 82, 17, 31, 44, kSequencePointKind_Normal, 0, 42 },
	{ 109925, 1, 84, 84, 9, 10, 48, kSequencePointKind_Normal, 0, 43 },
	{ 109926, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 44 },
	{ 109926, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 45 },
	{ 109926, 1, 87, 87, 9, 10, 0, kSequencePointKind_Normal, 0, 46 },
	{ 109926, 1, 89, 89, 13, 14, 1, kSequencePointKind_Normal, 0, 47 },
	{ 109926, 1, 90, 90, 17, 71, 2, kSequencePointKind_Normal, 0, 48 },
	{ 109926, 1, 90, 90, 17, 71, 4, kSequencePointKind_StepOut, 0, 49 },
	{ 109926, 1, 90, 90, 17, 71, 9, kSequencePointKind_StepOut, 0, 50 },
	{ 109926, 1, 91, 91, 17, 174, 16, kSequencePointKind_Normal, 0, 51 },
	{ 109926, 1, 91, 91, 17, 174, 17, kSequencePointKind_StepOut, 0, 52 },
	{ 109926, 1, 91, 91, 17, 174, 29, kSequencePointKind_StepOut, 0, 53 },
	{ 109926, 1, 92, 92, 17, 130, 35, kSequencePointKind_Normal, 0, 54 },
	{ 109926, 1, 92, 92, 17, 130, 38, kSequencePointKind_StepOut, 0, 55 },
	{ 109926, 1, 98, 98, 17, 31, 44, kSequencePointKind_Normal, 0, 56 },
	{ 109926, 1, 100, 100, 9, 10, 48, kSequencePointKind_Normal, 0, 57 },
	{ 109927, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 58 },
	{ 109927, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 59 },
	{ 109927, 1, 103, 103, 9, 10, 0, kSequencePointKind_Normal, 0, 60 },
	{ 109927, 1, 105, 105, 13, 14, 1, kSequencePointKind_Normal, 0, 61 },
	{ 109927, 1, 106, 106, 17, 71, 2, kSequencePointKind_Normal, 0, 62 },
	{ 109927, 1, 106, 106, 17, 71, 4, kSequencePointKind_StepOut, 0, 63 },
	{ 109927, 1, 106, 106, 17, 71, 9, kSequencePointKind_StepOut, 0, 64 },
	{ 109927, 1, 107, 107, 17, 183, 16, kSequencePointKind_Normal, 0, 65 },
	{ 109927, 1, 107, 107, 17, 183, 17, kSequencePointKind_StepOut, 0, 66 },
	{ 109927, 1, 107, 107, 17, 183, 31, kSequencePointKind_StepOut, 0, 67 },
	{ 109927, 1, 108, 108, 17, 130, 37, kSequencePointKind_Normal, 0, 68 },
	{ 109927, 1, 108, 108, 17, 130, 40, kSequencePointKind_StepOut, 0, 69 },
	{ 109927, 1, 114, 114, 17, 31, 46, kSequencePointKind_Normal, 0, 70 },
	{ 109927, 1, 116, 116, 9, 10, 50, kSequencePointKind_Normal, 0, 71 },
	{ 109928, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 72 },
	{ 109928, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 73 },
	{ 109928, 1, 119, 119, 9, 10, 0, kSequencePointKind_Normal, 0, 74 },
	{ 109928, 1, 121, 121, 13, 14, 1, kSequencePointKind_Normal, 0, 75 },
	{ 109928, 1, 122, 122, 17, 71, 2, kSequencePointKind_Normal, 0, 76 },
	{ 109928, 1, 122, 122, 17, 71, 4, kSequencePointKind_StepOut, 0, 77 },
	{ 109928, 1, 122, 122, 17, 71, 9, kSequencePointKind_StepOut, 0, 78 },
	{ 109928, 1, 123, 123, 17, 181, 16, kSequencePointKind_Normal, 0, 79 },
	{ 109928, 1, 123, 123, 17, 181, 17, kSequencePointKind_StepOut, 0, 80 },
	{ 109928, 1, 123, 123, 17, 181, 31, kSequencePointKind_StepOut, 0, 81 },
	{ 109928, 1, 124, 124, 17, 130, 37, kSequencePointKind_Normal, 0, 82 },
	{ 109928, 1, 124, 124, 17, 130, 40, kSequencePointKind_StepOut, 0, 83 },
	{ 109928, 1, 130, 130, 17, 31, 46, kSequencePointKind_Normal, 0, 84 },
	{ 109928, 1, 132, 132, 9, 10, 50, kSequencePointKind_Normal, 0, 85 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_ImageConversionModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ImageConversionModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/ImageConversion/ScriptBindings/ImageConversion.bindings.cs", { 194, 164, 23, 169, 240, 109, 250, 142, 2, 250, 128, 45, 138, 233, 83, 123} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = 
{
	{ 14130, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[12] = 
{
	{ 0, 11 },
	{ 0, 14 },
	{ 0, 13 },
	{ 0, 14 },
	{ 0, 50 },
	{ 1, 48 },
	{ 0, 50 },
	{ 1, 48 },
	{ 0, 52 },
	{ 1, 50 },
	{ 0, 52 },
	{ 1, 50 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[24] = 
{
	{ 11, 0, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 14, 1, 1 },
	{ 0, 0, 0 },
	{ 13, 2, 1 },
	{ 0, 0, 0 },
	{ 14, 3, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 50, 4, 2 },
	{ 50, 6, 2 },
	{ 52, 8, 2 },
	{ 52, 10, 2 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ImageConversionModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ImageConversionModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	86,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_ImageConversionModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	1,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
