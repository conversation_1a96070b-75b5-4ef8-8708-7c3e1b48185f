﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void SR_Format_m3F5EEA52EC82E931BDEFCEE9789D876FD27D9459 (void);
extern void SR_Format_m3E6D6782736A6F361E95EEE06395B4EBF14391F4 (void);
extern void BaseUriAnnotation__ctor_m63EAC1E4CB30B2C91C25D20C36FE9821D6F02865 (void);
extern void LineInfoAnnotation__ctor_mF684C98B92D1A45CD0E4B4C95FECDB970DAC8714 (void);
extern void LineInfoEndElementAnnotation__ctor_mC7AFB594F54BD80B3336BF5BFD7F0576C7C85788 (void);
extern void XAttribute__ctor_m913E6ED815250651BF84B13AE1C1B79C6C7F9CE0 (void);
extern void XAttribute__ctor_mC9A487EED1DCF3B088107A1313BB6212D56DA1BB (void);
extern void XAttribute_get_IsNamespaceDeclaration_m70367F65F7C13C9FB217DDA24F6550EBD02A54BE (void);
extern void XAttribute_get_Name_m5EB28279BB8BB19266997CB15733BD72CFB1DCB1 (void);
extern void XAttribute_get_NodeType_m6BC056AA324A07F9D5ACD0AB2F4A9CEAE3147CC6 (void);
extern void XAttribute_get_Value_mDCE15378AC161DF20094EF77EFF017A6F6766EB1 (void);
extern void XAttribute_ToString_m4B12B42FB4BBBF4047579A2465288AAEE9F31BB5 (void);
extern void XAttribute_GetPrefixOfNamespace_mDE12E0403D299B366DBD8FF87B384EB2F0DAD2F3 (void);
extern void XAttribute_ValidateAttribute_m70D9985C6D278228512D45C5566495AF457A2071 (void);
extern void XCData__ctor_m649661856E9B7BA8D204EB3FB1CE80D76998D416 (void);
extern void XCData__ctor_mF90A1941F63C439B2B36DE991E39238F34E26D6E (void);
extern void XCData_get_NodeType_mAF5D64410A51E966EB34DCA38F13EC3C242C59F6 (void);
extern void XCData_WriteTo_mB80625ABC3A8285F93950FFF55756510FA2F917B (void);
extern void XCData_CloneNode_mFFC5945D967A14A3B692B2A8D4A306BE7650ADD7 (void);
extern void XComment__ctor_mF1891F18BA45A4D593BF20DA9DC98AC2B2DE8020 (void);
extern void XComment__ctor_m29EF1C87D9D07AADB3DD91C2D694BE425DE177EA (void);
extern void XComment_get_NodeType_m6E5DA978FD76228FC3B94DCFEA46D46D97DDDF7A (void);
extern void XComment_get_Value_mD2D3D9900752AD9FA019947C965FF6FAF2B3E7DA (void);
extern void XComment_WriteTo_mC35C6FE9BA47571A497DFD17A1D5A1E00527FD0B (void);
extern void XComment_CloneNode_mA4DDE5EEE4B455D72D2F1EEE61DA484E448BBE71 (void);
extern void XContainer__ctor_mB0466ABD37EA7BAA821760B9A3CAB32152538708 (void);
extern void XContainer__ctor_mA9E3D02F0E4DB6052A159E4F52141ED1AAD8A637 (void);
extern void XContainer_get_LastNode_mA0896FBB61341D138E2F48F032D6C8169DEEED5E (void);
extern void XContainer_Add_m26111A020919619F03525ED30091D4501090075B (void);
extern void XContainer_Nodes_mBB3D95BB437752701F9A687E56EA226C0F27EFE4 (void);
extern void XContainer_AddAttribute_m197B5ED4F1FAA66D701FC163229773E9EA217791 (void);
extern void XContainer_AddAttributeSkipNotify_mD0A4D11C531721EB94046E3041693F0C0447E5BF (void);
extern void XContainer_AddContentSkipNotify_mB41DFA15D1363216D5008AB31FD7DFE757A63D4A (void);
extern void XContainer_AddNode_m1155CE5B3BD6DFEDE82BAEDB45582E8CCDE7CC86 (void);
extern void XContainer_AddNodeSkipNotify_mB704298C699554BB599036911E48A4F2907A7AD7 (void);
extern void XContainer_AddString_m5330FF509CFD4A6CD5F8857039604A133FA1B25F (void);
extern void XContainer_AddStringSkipNotify_mC5B6271BC57BB2FC5BF692D98DF86B48A64134E8 (void);
extern void XContainer_AppendNode_mAC391374030A84706012719D6411DE1C9349A6DE (void);
extern void XContainer_AppendNodeSkipNotify_m57D06340C7F57D650D6BE2DF27810AF19DE25DB5 (void);
extern void XContainer_AppendText_m04B752A0A4AE57DC5D5317F8C62A1140EC5FD805 (void);
extern void XContainer_ConvertTextToNode_m02D880D7454D1A02F5C33F38086BF31FEA676BAF (void);
extern void XContainer_GetStringValue_m4C4D548565D4B287DD399F92C62AD5371F88D183 (void);
extern void XContainer_ReadContentFrom_m3A9BB167B42CEE4F428F39F5AF0BE4B337D444A1 (void);
extern void XContainer_ReadContentFrom_m6CCDD05649CBCE45ABEF7B6F9D2F14E4EC58D31D (void);
extern void XContainer_RemoveNode_m801CAABC52F39D3BBDAAE0E091A58DD25D957467 (void);
extern void XContainer_ValidateNode_m037A22DA0826DD18CE52BDE166672570CB0951BF (void);
extern void XContainer_ValidateString_mFF9F71A5040EEC6BCAFA212B4F9B76867EB7CFC7 (void);
extern void XContainer_WriteContentTo_mE5DB0F2476BE53C5FF7D29ED51DDFBF8C4C8F652 (void);
extern void ContentReader__ctor_mA2D452D77A83C9E8F1D07653A908DC0364B85CD5 (void);
extern void ContentReader__ctor_mCB901D03293EA48E391356C1E66D145400DD7E7C (void);
extern void ContentReader_ReadContentFrom_mDACD1907690D92E4A5C7ABE5E69FADD75F0EBB99 (void);
extern void ContentReader_ReadContentFrom_m08791D2D38E6DE5D0C5CF9862D5FCFC4029F02F5 (void);
extern void U3CNodesU3Ed__18__ctor_mAE3AC8E1B1C44851E8725187A32151589F12DDC7 (void);
extern void U3CNodesU3Ed__18_System_IDisposable_Dispose_mAFA1AF67FBC4FC230F47DA06FD72390371AF295C (void);
extern void U3CNodesU3Ed__18_MoveNext_mDC19B175139EC4BD7F79CD6C19228FA27B83880D (void);
extern void U3CNodesU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_Xml_Linq_XNodeU3E_get_Current_m7703483DA2641C26FC93AFE025D0754AA262F792 (void);
extern void U3CNodesU3Ed__18_System_Collections_IEnumerator_Reset_m4BBFA6E48A0D3DC934C0569BCC63329A42285C8A (void);
extern void U3CNodesU3Ed__18_System_Collections_IEnumerator_get_Current_m3EAF102C979DF606467D13D9E642B5FACB60419B (void);
extern void U3CNodesU3Ed__18_System_Collections_Generic_IEnumerableU3CSystem_Xml_Linq_XNodeU3E_GetEnumerator_m018F008AB15C7FB2F855A224C8B8E999D4BD1325 (void);
extern void U3CNodesU3Ed__18_System_Collections_IEnumerable_GetEnumerator_mEE33E613422BD4C4E4FCB4BA8FFEC7E8AE5035B7 (void);
extern void XDeclaration__ctor_m8107AC3F844468E3C2A20075B14A09D24A3768BF (void);
extern void XDeclaration__ctor_m401611E4B4732A39D9187B5468A1A204EC13B279 (void);
extern void XDeclaration_get_Encoding_mA27D7E3A1673EE7D2A52F7D93A17FD9A33C4D5E0 (void);
extern void XDeclaration_get_Standalone_mCC775D6A93C6ED7C87BFFA4E3565675B4139CE7D (void);
extern void XDeclaration_get_Version_m675EDE875CAF23C6801015479393247D2B8A05E7 (void);
extern void XDeclaration_ToString_mA096BD87F527F200F57C88EDEA3CB0DD3BC65658 (void);
extern void XDocument__ctor_m75DFDCC516F6DB707830B7147BB51FEE9F72DDAD (void);
extern void XDocument__ctor_m495825983EF7F4A3E92F9B6334E7C488474C36EF (void);
extern void XDocument_get_Declaration_m54F6FE679582E4CC7FBC6FD3565260BC308D98CA (void);
extern void XDocument_set_Declaration_m3DAB9857DC323A55ABA6F973614AABE5FB59A672 (void);
extern void XDocument_get_NodeType_m2EA343EB0CB93D6FE1D5766B1E99E1FB01742046 (void);
extern void XDocument_get_Root_mB2847BD75F5350D1D795E2A2691BF9CB210300C8 (void);
extern void XDocument_WriteTo_m8A77978B2A117B0D4AB7CB799108F7D009A5F68B (void);
extern void XDocument_AddAttribute_m033107976FEFFC6DC7449331B97854D653EC6F6A (void);
extern void XDocument_AddAttributeSkipNotify_mFF42062B1570F6241005A55C4D162454C792D1BB (void);
extern void XDocument_CloneNode_mADF511715A902A14216456FA6248B3AEE1EF55C5 (void);
extern void XDocument_IsWhitespace_m7375DB9FBF379F950DFA1A1704A92CA596A2628D (void);
extern void XDocument_ValidateNode_mC2034D575F2FD45984E83566D6B2FDFEB294806A (void);
extern void XDocument_ValidateDocument_mBE76330494260C5034127B11C20F4DA2B1DDD17E (void);
extern void XDocument_ValidateString_mBD670C69B8F3474A76444AC11E6427385B205547 (void);
extern void XDocumentType__ctor_mD192CD368E807CA6B4AB0B5094A903CBFA37CA46 (void);
extern void XDocumentType__ctor_m7DCCD78A65C3B27C9235293D59756ABE9E19CAA6 (void);
extern void XDocumentType_get_InternalSubset_m6914F698B32A68A55C15AD4B971FEBFA85183DA1 (void);
extern void XDocumentType_get_Name_m297D10AE558AA29F11E05CCAEF2D74C43F980AE9 (void);
extern void XDocumentType_get_NodeType_m668392624D56D7E9DFCD8AFE07C2DDA0851E0043 (void);
extern void XDocumentType_get_PublicId_mAFD124B111C0B665C14A2078EE4E30C28F1854FC (void);
extern void XDocumentType_get_SystemId_m864BD298982BDA1D5B9C4DB6CB904B78FAFA04A7 (void);
extern void XDocumentType_WriteTo_m86D5EC70566DF9E0B31F1D73E65A3FD2CE981409 (void);
extern void XDocumentType_CloneNode_m4B0088F58CBD57B1FC1F4BD7341810102DB393C9 (void);
extern void XElement__ctor_mFDCA9C62F1562D0E005F47D0298A49C57DBC126C (void);
extern void XElement__ctor_m0C5B4B5FE2311B8D4D756928B475275BA6A84380 (void);
extern void XElement__ctor_m75A0943E2A1BF80A57A376F06DD505B4504D66EE (void);
extern void XElement_get_IsEmpty_m4451FEC389D90F236A5537E14D0638D40C44D049 (void);
extern void XElement_get_Name_mAF691F0AFE4F732FE836AEAC566536C1231AA74D (void);
extern void XElement_get_NodeType_mC7D6F185CEE118ED6DD002358522434286031C03 (void);
extern void XElement_get_Value_mC49BD0D0A616BD26B1B8E2D33D2E22D2F5A667BA (void);
extern void XElement_Attribute_m6CE84BC20DCC12D81D86DEAC966D0E22862CDFB1 (void);
extern void XElement_Attributes_m6B330A71FCAFA7A746FF494CC45F8B757F2CEF37 (void);
extern void XElement_GetPrefixOfNamespace_mD293E0A204AED396C6768157226CBC6A72BFAD11 (void);
extern void XElement_WriteTo_m8828BD0E77A7955443F6C2EDF364B9830237AEEB (void);
extern void XElement_System_Xml_Serialization_IXmlSerializable_GetSchema_m6437C83C3F13CB7B96F8C8439486460108EB363B (void);
extern void XElement_System_Xml_Serialization_IXmlSerializable_ReadXml_m5809B835E1CFBAD76A3013F05F5D1AC042616499 (void);
extern void XElement_System_Xml_Serialization_IXmlSerializable_WriteXml_m7404E67E305739639E7035D171786C23D1D07603 (void);
extern void XElement_AddAttribute_m05757FEB709493261D79B044EC46E46C67FD52AE (void);
extern void XElement_AddAttributeSkipNotify_m1FC2E9C25DD04CB6E8D8E3F9C0753B1E6EF66891 (void);
extern void XElement_AppendAttribute_mBB1DA5FA3D139DBB9051B6805CC3999155C3F046 (void);
extern void XElement_AppendAttributeSkipNotify_mDAC4BBD49687B7FF06E11B22B17EDC350B906C59 (void);
extern void XElement_CloneNode_mD8C9E4154C4531FB2DFF0D8DD140F8868DDCC407 (void);
extern void XElement_GetAttributes_m8AFAB0F578B9AFE4B7671B441DA71D43A2824DE1 (void);
extern void XElement_GetNamespaceOfPrefixInScope_m663AE0D4222FF7A41067711CA8575DD29B1609E4 (void);
extern void XElement_ReadElementFrom_mE677FF9A108179BEAE913C1B5392D77DCB949B8B (void);
extern void XElement_ReadElementFromImpl_m8B6FD4923DDF002EB78EAA3A42508CC0F355194B (void);
extern void XElement_SetEndElementLineInfo_mC221A9CDAF38014E70BC700A8A2320E1AF0D98F3 (void);
extern void XElement_ValidateNode_mB9015590DED5FDE981E95F47D0C86509F3D92DFA (void);
extern void U3CGetAttributesU3Ed__116__ctor_m93AF86C7CDA29826BEF24FBC3CC581C1C537869F (void);
extern void U3CGetAttributesU3Ed__116_System_IDisposable_Dispose_mB278A2635F8AC31417CB783D58FA1C584386C389 (void);
extern void U3CGetAttributesU3Ed__116_MoveNext_mA2DA57F48DA5148224888EBAEE44F9EE9172AB22 (void);
extern void U3CGetAttributesU3Ed__116_System_Collections_Generic_IEnumeratorU3CSystem_Xml_Linq_XAttributeU3E_get_Current_mF2523004C396AB89915B13A1F8686CB716D98E7D (void);
extern void U3CGetAttributesU3Ed__116_System_Collections_IEnumerator_Reset_mFD66D66F520409BAE9C9D6C64D451A84A8014589 (void);
extern void U3CGetAttributesU3Ed__116_System_Collections_IEnumerator_get_Current_mA0D426EAD3627932F3366FF70A8873D2BBDA37D0 (void);
extern void U3CGetAttributesU3Ed__116_System_Collections_Generic_IEnumerableU3CSystem_Xml_Linq_XAttributeU3E_GetEnumerator_m08FC77DE7BAA24009A1404E63AA7AA0787443960 (void);
extern void U3CGetAttributesU3Ed__116_System_Collections_IEnumerable_GetEnumerator_m63685649CE07AB1CD4AFB444A9C20B43948EF867 (void);
extern void NamespaceCache_Get_m3493C1A45330169E117ECBCCA137BF1DE0D97ADC (void);
extern void ElementWriter__ctor_m9799ED04B0CC92C56270CC842CEE0B55C2CFC7DA (void);
extern void ElementWriter_WriteElement_mC858417C14469E1FF409B1C70993E2FBEF9D9FD9 (void);
extern void ElementWriter_GetPrefixOfNamespace_m6B42C7437A2CDB0F8D44DCA223E41AED4FB647EF (void);
extern void ElementWriter_PushAncestors_mE594931ADD6A3BFAFE02B768322DDF8A6F3B8C29 (void);
extern void ElementWriter_PushElement_mDC0DBDBC8C9029B35D453BA33FAB358F33D6F19C (void);
extern void ElementWriter_WriteEndElement_m68C45F2C6EE295852F44AAFFDD3008B353226ABC (void);
extern void ElementWriter_WriteFullEndElement_mBE3CCBF82B1F5D975ED79C9A764B98C730703D21 (void);
extern void ElementWriter_WriteStartElement_m2B5053DBE09413D2F2284EE447A6EB9A6C8C38C2 (void);
extern void NamespaceResolver_PushScope_m59AE5DD3B165CBAE63A7087B6103E170D56E9A1C (void);
extern void NamespaceResolver_PopScope_m84BCD5486EBB774B284EB334A641E1B9B1F1E4A8 (void);
extern void NamespaceResolver_Add_mD1691BA82CBBB75227EF918D87D05B9B2A1DED7D (void);
extern void NamespaceResolver_AddFirst_m97452EE84946421A878A52174C7A8FF98127FB5F (void);
extern void NamespaceResolver_GetPrefixOfNamespace_mB05121AE3616861C488DAC20D8B3E5E542428F84 (void);
extern void NamespaceDeclaration__ctor_m03AF0C0AE2F655C47B97390A85F29FA6DEF6BFCF (void);
extern void XName__ctor_mA1D99FDB997DE7A0178011EB30EF44C9F42E35FD (void);
extern void XName_get_LocalName_m4CA50C151ADB8183972EA556868148AF0F5BED75 (void);
extern void XName_get_Namespace_mC66E7FA73994C5EE096C2C64774B3BE7C316FDE9 (void);
extern void XName_get_NamespaceName_mBEAFAB116BBDFE5B13EE2672C6C9DB3E58D257D3 (void);
extern void XName_ToString_mC0FF991D95C0FF47A87EBCBFC14EA0EC8CEBCEC8 (void);
extern void XName_Get_mC5654185D68DD5960BF16D91C0D7CF3AA6B44785 (void);
extern void XName_Get_mECF7FD66C88749DF760999F9C6F506BA9A0D0373 (void);
extern void XName_op_Implicit_m3A259E71F7D76AA504349A98DAE3C47D7A943736 (void);
extern void XName_Equals_m22A983F0AA2093E9D33BD42ADC89C79548F6D220 (void);
extern void XName_GetHashCode_mE5EAAFEE68E00AD9505E78AC0A11E31B05F2B70E (void);
extern void XName_op_Equality_m3F2AF3BB435B0F79A8D197D2FB22CDB8E150DBC0 (void);
extern void XName_System_IEquatableU3CSystem_Xml_Linq_XNameU3E_Equals_mBF0DDFCD3955DEE4E90DF3CAB7E7FCC5815C60D2 (void);
extern void XName_System_Runtime_Serialization_ISerializable_GetObjectData_m7C96AD2A92138B962DE0BAAEA3F8F0D011612057 (void);
extern void XName__ctor_mB579540EB28ED78A0EEE6BD7A81129EDEB876E57 (void);
extern void XNamespace__ctor_m9D184158922B44BBCD28D394F3AF0086485E5C5E (void);
extern void XNamespace_get_NamespaceName_m582F8EEBEE2DD35B118583335F51870305CDCC38 (void);
extern void XNamespace_GetName_mE518274722135FD918AF466F4C5B5CD7BFDEE311 (void);
extern void XNamespace_ToString_m40DD4165D7931CC3B84952751D50BA0F814417A3 (void);
extern void XNamespace_get_None_mCE21C56684E4F1A7C7E1FFB36F72C54F9330EE9C (void);
extern void XNamespace_get_Xml_mE1966B4156DA2D168566FDC81E6327CC63E9B0EB (void);
extern void XNamespace_get_Xmlns_mC5CB27940F5AE9D090C6168033663616CED1F559 (void);
extern void XNamespace_Get_mEE1B17946112BFAC41E4331433CF28B75FE761BE (void);
extern void XNamespace_op_Implicit_m82CA31E66BE67924ED340B6CF69B6DF3E8FAC7DC (void);
extern void XNamespace_Equals_m3437E3FED90F6B3ED6CFED8C5B79AAC8F1B7C57A (void);
extern void XNamespace_GetHashCode_m2DB1CED2C4D1F5EFCBFBBBA99B03BF8A26E9AA6C (void);
extern void XNamespace_op_Equality_m616EF9FF2A818AD180E63594576384790E19254D (void);
extern void XNamespace_op_Inequality_m72707CE08FC24456A262D7C72ECC042531A1918C (void);
extern void XNamespace_GetName_m9446102D865A37ACF99EFB99DC5A81C18087664E (void);
extern void XNamespace_Get_m055C6410B3413FD04032A881D48706114891016C (void);
extern void XNamespace_ExtractLocalName_mAEB547B4641EB2F51CBAD373C57457E6B04905C2 (void);
extern void XNamespace_ExtractNamespace_m4716EF367DE17D509CB4839911A4726FEEE9859D (void);
extern void XNamespace_EnsureNamespace_mFBE191305CF760E4F2FC15B3E3DA1BFAD147A55C (void);
extern void XNode__ctor_m7E4F8620BA971E95E0F2EBB6A86720C8027EB4F4 (void);
extern void XNode_Remove_m74CDB71751058523C66B0529333D7D0E880C29F9 (void);
extern void XNode_ToString_m5E3D419064B677FD88EAA43E9DB28E08749B3D38 (void);
extern void XNode_AppendText_mB923157D1C8D0A319A43E361461D44752E10C90B (void);
extern void XNode_GetXmlString_mB27A807BB96F3DBB6B402FE8D86135A25F6CAFFD (void);
extern void XObject__ctor_m864E494182E9F8ED183E1FB2B46D96BC8F1F02BB (void);
extern void XObject_get_BaseUri_mC14446B876AC5EE54F9EDEDDC455AE8FBFE8D694 (void);
extern void XObject_get_Parent_m192B7F82C3DF6A43672AD9C61B704A40DCA0213C (void);
extern void XObject_AddAnnotation_m6B5D94FD486310F23671C89934877B1B9CBD5B2D (void);
extern void XObject_AnnotationForSealedType_m95050AB67339123867458EFAAC338BDF706D3342 (void);
extern void XObject_System_Xml_IXmlLineInfo_HasLineInfo_m4F4D7395F121FF7F36691BA607CE7C6B67D6E201 (void);
extern void XObject_System_Xml_IXmlLineInfo_get_LineNumber_m43097DFF4FFD56A57AEE558FD22E3BC6DECA9F0F (void);
extern void XObject_System_Xml_IXmlLineInfo_get_LinePosition_mF386E26BFE458B4268D0C3BF4F4F162667EEF2D9 (void);
extern void XObject_get_HasBaseUri_m12C744571C69A1EF5177DB7D55A11ABEA3280330 (void);
extern void XObject_NotifyChanged_m61E3332FA65B150370DA03B46D430E3AB0EDC60C (void);
extern void XObject_NotifyChanging_m0665A301FB67CA79708ED59C19B6ED0063EE2189 (void);
extern void XObject_SetBaseUri_m934974B77056C8A108D0086CF6E2324C5A2B3E88 (void);
extern void XObject_SetLineInfo_m1BB40D6AEE26EE76C831CA6185F7A500D53E031C (void);
extern void XObject_SkipNotify_mBE5105AB523E68C26A65D153F8523DFF6636FD9F (void);
extern void XObject_GetSaveOptionsFromAnnotations_m46D960C1A1473A58A5A2F5676DDF16042E7E51A9 (void);
extern void XObjectChangeEventArgs__ctor_m0EC4DEEDCAC8B4660F086D1C980BEF79887DBBE2 (void);
extern void XObjectChangeEventArgs__cctor_mA2E8E5085C0016AFE1915D747016FFFA055C2F5D (void);
extern void XProcessingInstruction__ctor_m9D42D9DF59625543003025C09E1AED9659A1E862 (void);
extern void XProcessingInstruction__ctor_mEF30F650C629AC6570B9C202E4EFAF58CD711F89 (void);
extern void XProcessingInstruction_get_Data_mF06C89FCF9F580497E3B59331676AFC9AA3F416B (void);
extern void XProcessingInstruction_get_NodeType_mAE450ADAAE886EA5434230864F9CE7A86A3649D5 (void);
extern void XProcessingInstruction_get_Target_mBB98BD38B91EBDC03558F4990591BE33FC1EB0DE (void);
extern void XProcessingInstruction_WriteTo_m56732BE1F56BF38E2F161186A83CE394CA04636C (void);
extern void XProcessingInstruction_CloneNode_m680B674C04EEE4E2C57294D645742048079D97B1 (void);
extern void XProcessingInstruction_ValidateName_m945E9C60579F07D700F600E25579FA81F9D372CE (void);
extern void XText__ctor_m30153536CE7D619BF5609B3D3453109B3ECDD28D (void);
extern void XText__ctor_m57E9875B16480CE76D1F612F68AC85B2EAFBADF8 (void);
extern void XText_get_NodeType_m0B5BA5C2D51F659BCD454D1566D4A5B48F62CE9C (void);
extern void XText_get_Value_m5B2A033755DE27DEC73860B9E94AA41BB55E208C (void);
extern void XText_set_Value_m285A62B03AEFA17C99B9637F5E9BD62A5E707C38 (void);
extern void XText_WriteTo_m8DA512ED0623E5D736844AEF4F956CC761F834E3 (void);
extern void XText_AppendText_m67BD74F5CFEBC7BF31089C195221CE2A0BB57FB2 (void);
extern void XText_CloneNode_m817AC9E0BB0881591E092B3F1F3AE4B1E00F8C1E (void);
extern void StringBuilderCache_Acquire_m56CF0EE17E4DBF597E4A74230E25A18E9D4B77BE (void);
extern void StringBuilderCache_Release_m8BC1DF4DCAFAC31A5FEC78A03891D522561D0835 (void);
extern void StringBuilderCache_GetStringAndRelease_mF731F5D5144F0723DFFCB719692FFAEAEDF47389 (void);
extern void ThrowStub_ThrowNotSupportedException_m9860569D0F80DAC07D9ECCCAFC15125E0A3603C1 (void);
static Il2CppMethodPointer s_methodPointers[227] = 
{
	SR_Format_m3F5EEA52EC82E931BDEFCEE9789D876FD27D9459,
	SR_Format_m3E6D6782736A6F361E95EEE06395B4EBF14391F4,
	BaseUriAnnotation__ctor_m63EAC1E4CB30B2C91C25D20C36FE9821D6F02865,
	LineInfoAnnotation__ctor_mF684C98B92D1A45CD0E4B4C95FECDB970DAC8714,
	LineInfoEndElementAnnotation__ctor_mC7AFB594F54BD80B3336BF5BFD7F0576C7C85788,
	XAttribute__ctor_m913E6ED815250651BF84B13AE1C1B79C6C7F9CE0,
	XAttribute__ctor_mC9A487EED1DCF3B088107A1313BB6212D56DA1BB,
	XAttribute_get_IsNamespaceDeclaration_m70367F65F7C13C9FB217DDA24F6550EBD02A54BE,
	XAttribute_get_Name_m5EB28279BB8BB19266997CB15733BD72CFB1DCB1,
	XAttribute_get_NodeType_m6BC056AA324A07F9D5ACD0AB2F4A9CEAE3147CC6,
	XAttribute_get_Value_mDCE15378AC161DF20094EF77EFF017A6F6766EB1,
	XAttribute_ToString_m4B12B42FB4BBBF4047579A2465288AAEE9F31BB5,
	XAttribute_GetPrefixOfNamespace_mDE12E0403D299B366DBD8FF87B384EB2F0DAD2F3,
	XAttribute_ValidateAttribute_m70D9985C6D278228512D45C5566495AF457A2071,
	XCData__ctor_m649661856E9B7BA8D204EB3FB1CE80D76998D416,
	XCData__ctor_mF90A1941F63C439B2B36DE991E39238F34E26D6E,
	XCData_get_NodeType_mAF5D64410A51E966EB34DCA38F13EC3C242C59F6,
	XCData_WriteTo_mB80625ABC3A8285F93950FFF55756510FA2F917B,
	XCData_CloneNode_mFFC5945D967A14A3B692B2A8D4A306BE7650ADD7,
	XComment__ctor_mF1891F18BA45A4D593BF20DA9DC98AC2B2DE8020,
	XComment__ctor_m29EF1C87D9D07AADB3DD91C2D694BE425DE177EA,
	XComment_get_NodeType_m6E5DA978FD76228FC3B94DCFEA46D46D97DDDF7A,
	XComment_get_Value_mD2D3D9900752AD9FA019947C965FF6FAF2B3E7DA,
	XComment_WriteTo_mC35C6FE9BA47571A497DFD17A1D5A1E00527FD0B,
	XComment_CloneNode_mA4DDE5EEE4B455D72D2F1EEE61DA484E448BBE71,
	XContainer__ctor_mB0466ABD37EA7BAA821760B9A3CAB32152538708,
	XContainer__ctor_mA9E3D02F0E4DB6052A159E4F52141ED1AAD8A637,
	XContainer_get_LastNode_mA0896FBB61341D138E2F48F032D6C8169DEEED5E,
	XContainer_Add_m26111A020919619F03525ED30091D4501090075B,
	XContainer_Nodes_mBB3D95BB437752701F9A687E56EA226C0F27EFE4,
	XContainer_AddAttribute_m197B5ED4F1FAA66D701FC163229773E9EA217791,
	XContainer_AddAttributeSkipNotify_mD0A4D11C531721EB94046E3041693F0C0447E5BF,
	XContainer_AddContentSkipNotify_mB41DFA15D1363216D5008AB31FD7DFE757A63D4A,
	XContainer_AddNode_m1155CE5B3BD6DFEDE82BAEDB45582E8CCDE7CC86,
	XContainer_AddNodeSkipNotify_mB704298C699554BB599036911E48A4F2907A7AD7,
	XContainer_AddString_m5330FF509CFD4A6CD5F8857039604A133FA1B25F,
	XContainer_AddStringSkipNotify_mC5B6271BC57BB2FC5BF692D98DF86B48A64134E8,
	XContainer_AppendNode_mAC391374030A84706012719D6411DE1C9349A6DE,
	XContainer_AppendNodeSkipNotify_m57D06340C7F57D650D6BE2DF27810AF19DE25DB5,
	XContainer_AppendText_m04B752A0A4AE57DC5D5317F8C62A1140EC5FD805,
	XContainer_ConvertTextToNode_m02D880D7454D1A02F5C33F38086BF31FEA676BAF,
	XContainer_GetStringValue_m4C4D548565D4B287DD399F92C62AD5371F88D183,
	XContainer_ReadContentFrom_m3A9BB167B42CEE4F428F39F5AF0BE4B337D444A1,
	XContainer_ReadContentFrom_m6CCDD05649CBCE45ABEF7B6F9D2F14E4EC58D31D,
	XContainer_RemoveNode_m801CAABC52F39D3BBDAAE0E091A58DD25D957467,
	XContainer_ValidateNode_m037A22DA0826DD18CE52BDE166672570CB0951BF,
	XContainer_ValidateString_mFF9F71A5040EEC6BCAFA212B4F9B76867EB7CFC7,
	XContainer_WriteContentTo_mE5DB0F2476BE53C5FF7D29ED51DDFBF8C4C8F652,
	ContentReader__ctor_mA2D452D77A83C9E8F1D07653A908DC0364B85CD5,
	ContentReader__ctor_mCB901D03293EA48E391356C1E66D145400DD7E7C,
	ContentReader_ReadContentFrom_mDACD1907690D92E4A5C7ABE5E69FADD75F0EBB99,
	ContentReader_ReadContentFrom_m08791D2D38E6DE5D0C5CF9862D5FCFC4029F02F5,
	U3CNodesU3Ed__18__ctor_mAE3AC8E1B1C44851E8725187A32151589F12DDC7,
	U3CNodesU3Ed__18_System_IDisposable_Dispose_mAFA1AF67FBC4FC230F47DA06FD72390371AF295C,
	U3CNodesU3Ed__18_MoveNext_mDC19B175139EC4BD7F79CD6C19228FA27B83880D,
	U3CNodesU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_Xml_Linq_XNodeU3E_get_Current_m7703483DA2641C26FC93AFE025D0754AA262F792,
	U3CNodesU3Ed__18_System_Collections_IEnumerator_Reset_m4BBFA6E48A0D3DC934C0569BCC63329A42285C8A,
	U3CNodesU3Ed__18_System_Collections_IEnumerator_get_Current_m3EAF102C979DF606467D13D9E642B5FACB60419B,
	U3CNodesU3Ed__18_System_Collections_Generic_IEnumerableU3CSystem_Xml_Linq_XNodeU3E_GetEnumerator_m018F008AB15C7FB2F855A224C8B8E999D4BD1325,
	U3CNodesU3Ed__18_System_Collections_IEnumerable_GetEnumerator_mEE33E613422BD4C4E4FCB4BA8FFEC7E8AE5035B7,
	XDeclaration__ctor_m8107AC3F844468E3C2A20075B14A09D24A3768BF,
	XDeclaration__ctor_m401611E4B4732A39D9187B5468A1A204EC13B279,
	XDeclaration_get_Encoding_mA27D7E3A1673EE7D2A52F7D93A17FD9A33C4D5E0,
	XDeclaration_get_Standalone_mCC775D6A93C6ED7C87BFFA4E3565675B4139CE7D,
	XDeclaration_get_Version_m675EDE875CAF23C6801015479393247D2B8A05E7,
	XDeclaration_ToString_mA096BD87F527F200F57C88EDEA3CB0DD3BC65658,
	XDocument__ctor_m75DFDCC516F6DB707830B7147BB51FEE9F72DDAD,
	XDocument__ctor_m495825983EF7F4A3E92F9B6334E7C488474C36EF,
	XDocument_get_Declaration_m54F6FE679582E4CC7FBC6FD3565260BC308D98CA,
	XDocument_set_Declaration_m3DAB9857DC323A55ABA6F973614AABE5FB59A672,
	XDocument_get_NodeType_m2EA343EB0CB93D6FE1D5766B1E99E1FB01742046,
	XDocument_get_Root_mB2847BD75F5350D1D795E2A2691BF9CB210300C8,
	XDocument_WriteTo_m8A77978B2A117B0D4AB7CB799108F7D009A5F68B,
	XDocument_AddAttribute_m033107976FEFFC6DC7449331B97854D653EC6F6A,
	XDocument_AddAttributeSkipNotify_mFF42062B1570F6241005A55C4D162454C792D1BB,
	XDocument_CloneNode_mADF511715A902A14216456FA6248B3AEE1EF55C5,
	NULL,
	XDocument_IsWhitespace_m7375DB9FBF379F950DFA1A1704A92CA596A2628D,
	XDocument_ValidateNode_mC2034D575F2FD45984E83566D6B2FDFEB294806A,
	XDocument_ValidateDocument_mBE76330494260C5034127B11C20F4DA2B1DDD17E,
	XDocument_ValidateString_mBD670C69B8F3474A76444AC11E6427385B205547,
	XDocumentType__ctor_mD192CD368E807CA6B4AB0B5094A903CBFA37CA46,
	XDocumentType__ctor_m7DCCD78A65C3B27C9235293D59756ABE9E19CAA6,
	XDocumentType_get_InternalSubset_m6914F698B32A68A55C15AD4B971FEBFA85183DA1,
	XDocumentType_get_Name_m297D10AE558AA29F11E05CCAEF2D74C43F980AE9,
	XDocumentType_get_NodeType_m668392624D56D7E9DFCD8AFE07C2DDA0851E0043,
	XDocumentType_get_PublicId_mAFD124B111C0B665C14A2078EE4E30C28F1854FC,
	XDocumentType_get_SystemId_m864BD298982BDA1D5B9C4DB6CB904B78FAFA04A7,
	XDocumentType_WriteTo_m86D5EC70566DF9E0B31F1D73E65A3FD2CE981409,
	XDocumentType_CloneNode_m4B0088F58CBD57B1FC1F4BD7341810102DB393C9,
	XElement__ctor_mFDCA9C62F1562D0E005F47D0298A49C57DBC126C,
	XElement__ctor_m0C5B4B5FE2311B8D4D756928B475275BA6A84380,
	XElement__ctor_m75A0943E2A1BF80A57A376F06DD505B4504D66EE,
	XElement_get_IsEmpty_m4451FEC389D90F236A5537E14D0638D40C44D049,
	XElement_get_Name_mAF691F0AFE4F732FE836AEAC566536C1231AA74D,
	XElement_get_NodeType_mC7D6F185CEE118ED6DD002358522434286031C03,
	XElement_get_Value_mC49BD0D0A616BD26B1B8E2D33D2E22D2F5A667BA,
	XElement_Attribute_m6CE84BC20DCC12D81D86DEAC966D0E22862CDFB1,
	XElement_Attributes_m6B330A71FCAFA7A746FF494CC45F8B757F2CEF37,
	XElement_GetPrefixOfNamespace_mD293E0A204AED396C6768157226CBC6A72BFAD11,
	XElement_WriteTo_m8828BD0E77A7955443F6C2EDF364B9830237AEEB,
	XElement_System_Xml_Serialization_IXmlSerializable_GetSchema_m6437C83C3F13CB7B96F8C8439486460108EB363B,
	XElement_System_Xml_Serialization_IXmlSerializable_ReadXml_m5809B835E1CFBAD76A3013F05F5D1AC042616499,
	XElement_System_Xml_Serialization_IXmlSerializable_WriteXml_m7404E67E305739639E7035D171786C23D1D07603,
	XElement_AddAttribute_m05757FEB709493261D79B044EC46E46C67FD52AE,
	XElement_AddAttributeSkipNotify_m1FC2E9C25DD04CB6E8D8E3F9C0753B1E6EF66891,
	XElement_AppendAttribute_mBB1DA5FA3D139DBB9051B6805CC3999155C3F046,
	XElement_AppendAttributeSkipNotify_mDAC4BBD49687B7FF06E11B22B17EDC350B906C59,
	XElement_CloneNode_mD8C9E4154C4531FB2DFF0D8DD140F8868DDCC407,
	XElement_GetAttributes_m8AFAB0F578B9AFE4B7671B441DA71D43A2824DE1,
	XElement_GetNamespaceOfPrefixInScope_m663AE0D4222FF7A41067711CA8575DD29B1609E4,
	XElement_ReadElementFrom_mE677FF9A108179BEAE913C1B5392D77DCB949B8B,
	XElement_ReadElementFromImpl_m8B6FD4923DDF002EB78EAA3A42508CC0F355194B,
	XElement_SetEndElementLineInfo_mC221A9CDAF38014E70BC700A8A2320E1AF0D98F3,
	XElement_ValidateNode_mB9015590DED5FDE981E95F47D0C86509F3D92DFA,
	U3CGetAttributesU3Ed__116__ctor_m93AF86C7CDA29826BEF24FBC3CC581C1C537869F,
	U3CGetAttributesU3Ed__116_System_IDisposable_Dispose_mB278A2635F8AC31417CB783D58FA1C584386C389,
	U3CGetAttributesU3Ed__116_MoveNext_mA2DA57F48DA5148224888EBAEE44F9EE9172AB22,
	U3CGetAttributesU3Ed__116_System_Collections_Generic_IEnumeratorU3CSystem_Xml_Linq_XAttributeU3E_get_Current_mF2523004C396AB89915B13A1F8686CB716D98E7D,
	U3CGetAttributesU3Ed__116_System_Collections_IEnumerator_Reset_mFD66D66F520409BAE9C9D6C64D451A84A8014589,
	U3CGetAttributesU3Ed__116_System_Collections_IEnumerator_get_Current_mA0D426EAD3627932F3366FF70A8873D2BBDA37D0,
	U3CGetAttributesU3Ed__116_System_Collections_Generic_IEnumerableU3CSystem_Xml_Linq_XAttributeU3E_GetEnumerator_m08FC77DE7BAA24009A1404E63AA7AA0787443960,
	U3CGetAttributesU3Ed__116_System_Collections_IEnumerable_GetEnumerator_m63685649CE07AB1CD4AFB444A9C20B43948EF867,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NamespaceCache_Get_m3493C1A45330169E117ECBCCA137BF1DE0D97ADC,
	ElementWriter__ctor_m9799ED04B0CC92C56270CC842CEE0B55C2CFC7DA,
	ElementWriter_WriteElement_mC858417C14469E1FF409B1C70993E2FBEF9D9FD9,
	ElementWriter_GetPrefixOfNamespace_m6B42C7437A2CDB0F8D44DCA223E41AED4FB647EF,
	ElementWriter_PushAncestors_mE594931ADD6A3BFAFE02B768322DDF8A6F3B8C29,
	ElementWriter_PushElement_mDC0DBDBC8C9029B35D453BA33FAB358F33D6F19C,
	ElementWriter_WriteEndElement_m68C45F2C6EE295852F44AAFFDD3008B353226ABC,
	ElementWriter_WriteFullEndElement_mBE3CCBF82B1F5D975ED79C9A764B98C730703D21,
	ElementWriter_WriteStartElement_m2B5053DBE09413D2F2284EE447A6EB9A6C8C38C2,
	NamespaceResolver_PushScope_m59AE5DD3B165CBAE63A7087B6103E170D56E9A1C,
	NamespaceResolver_PopScope_m84BCD5486EBB774B284EB334A641E1B9B1F1E4A8,
	NamespaceResolver_Add_mD1691BA82CBBB75227EF918D87D05B9B2A1DED7D,
	NamespaceResolver_AddFirst_m97452EE84946421A878A52174C7A8FF98127FB5F,
	NamespaceResolver_GetPrefixOfNamespace_mB05121AE3616861C488DAC20D8B3E5E542428F84,
	NamespaceDeclaration__ctor_m03AF0C0AE2F655C47B97390A85F29FA6DEF6BFCF,
	XName__ctor_mA1D99FDB997DE7A0178011EB30EF44C9F42E35FD,
	XName_get_LocalName_m4CA50C151ADB8183972EA556868148AF0F5BED75,
	XName_get_Namespace_mC66E7FA73994C5EE096C2C64774B3BE7C316FDE9,
	XName_get_NamespaceName_mBEAFAB116BBDFE5B13EE2672C6C9DB3E58D257D3,
	XName_ToString_mC0FF991D95C0FF47A87EBCBFC14EA0EC8CEBCEC8,
	XName_Get_mC5654185D68DD5960BF16D91C0D7CF3AA6B44785,
	XName_Get_mECF7FD66C88749DF760999F9C6F506BA9A0D0373,
	XName_op_Implicit_m3A259E71F7D76AA504349A98DAE3C47D7A943736,
	XName_Equals_m22A983F0AA2093E9D33BD42ADC89C79548F6D220,
	XName_GetHashCode_mE5EAAFEE68E00AD9505E78AC0A11E31B05F2B70E,
	XName_op_Equality_m3F2AF3BB435B0F79A8D197D2FB22CDB8E150DBC0,
	XName_System_IEquatableU3CSystem_Xml_Linq_XNameU3E_Equals_mBF0DDFCD3955DEE4E90DF3CAB7E7FCC5815C60D2,
	XName_System_Runtime_Serialization_ISerializable_GetObjectData_m7C96AD2A92138B962DE0BAAEA3F8F0D011612057,
	XName__ctor_mB579540EB28ED78A0EEE6BD7A81129EDEB876E57,
	XNamespace__ctor_m9D184158922B44BBCD28D394F3AF0086485E5C5E,
	XNamespace_get_NamespaceName_m582F8EEBEE2DD35B118583335F51870305CDCC38,
	XNamespace_GetName_mE518274722135FD918AF466F4C5B5CD7BFDEE311,
	XNamespace_ToString_m40DD4165D7931CC3B84952751D50BA0F814417A3,
	XNamespace_get_None_mCE21C56684E4F1A7C7E1FFB36F72C54F9330EE9C,
	XNamespace_get_Xml_mE1966B4156DA2D168566FDC81E6327CC63E9B0EB,
	XNamespace_get_Xmlns_mC5CB27940F5AE9D090C6168033663616CED1F559,
	XNamespace_Get_mEE1B17946112BFAC41E4331433CF28B75FE761BE,
	XNamespace_op_Implicit_m82CA31E66BE67924ED340B6CF69B6DF3E8FAC7DC,
	XNamespace_Equals_m3437E3FED90F6B3ED6CFED8C5B79AAC8F1B7C57A,
	XNamespace_GetHashCode_m2DB1CED2C4D1F5EFCBFBBBA99B03BF8A26E9AA6C,
	XNamespace_op_Equality_m616EF9FF2A818AD180E63594576384790E19254D,
	XNamespace_op_Inequality_m72707CE08FC24456A262D7C72ECC042531A1918C,
	XNamespace_GetName_m9446102D865A37ACF99EFB99DC5A81C18087664E,
	XNamespace_Get_m055C6410B3413FD04032A881D48706114891016C,
	XNamespace_ExtractLocalName_mAEB547B4641EB2F51CBAD373C57457E6B04905C2,
	XNamespace_ExtractNamespace_m4716EF367DE17D509CB4839911A4726FEEE9859D,
	XNamespace_EnsureNamespace_mFBE191305CF760E4F2FC15B3E3DA1BFAD147A55C,
	XNode__ctor_m7E4F8620BA971E95E0F2EBB6A86720C8027EB4F4,
	XNode_Remove_m74CDB71751058523C66B0529333D7D0E880C29F9,
	XNode_ToString_m5E3D419064B677FD88EAA43E9DB28E08749B3D38,
	NULL,
	XNode_AppendText_mB923157D1C8D0A319A43E361461D44752E10C90B,
	NULL,
	XNode_GetXmlString_mB27A807BB96F3DBB6B402FE8D86135A25F6CAFFD,
	XObject__ctor_m864E494182E9F8ED183E1FB2B46D96BC8F1F02BB,
	XObject_get_BaseUri_mC14446B876AC5EE54F9EDEDDC455AE8FBFE8D694,
	NULL,
	XObject_get_Parent_m192B7F82C3DF6A43672AD9C61B704A40DCA0213C,
	XObject_AddAnnotation_m6B5D94FD486310F23671C89934877B1B9CBD5B2D,
	XObject_AnnotationForSealedType_m95050AB67339123867458EFAAC338BDF706D3342,
	NULL,
	XObject_System_Xml_IXmlLineInfo_HasLineInfo_m4F4D7395F121FF7F36691BA607CE7C6B67D6E201,
	XObject_System_Xml_IXmlLineInfo_get_LineNumber_m43097DFF4FFD56A57AEE558FD22E3BC6DECA9F0F,
	XObject_System_Xml_IXmlLineInfo_get_LinePosition_mF386E26BFE458B4268D0C3BF4F4F162667EEF2D9,
	XObject_get_HasBaseUri_m12C744571C69A1EF5177DB7D55A11ABEA3280330,
	XObject_NotifyChanged_m61E3332FA65B150370DA03B46D430E3AB0EDC60C,
	XObject_NotifyChanging_m0665A301FB67CA79708ED59C19B6ED0063EE2189,
	XObject_SetBaseUri_m934974B77056C8A108D0086CF6E2324C5A2B3E88,
	XObject_SetLineInfo_m1BB40D6AEE26EE76C831CA6185F7A500D53E031C,
	XObject_SkipNotify_mBE5105AB523E68C26A65D153F8523DFF6636FD9F,
	XObject_GetSaveOptionsFromAnnotations_m46D960C1A1473A58A5A2F5676DDF16042E7E51A9,
	XObjectChangeEventArgs__ctor_m0EC4DEEDCAC8B4660F086D1C980BEF79887DBBE2,
	XObjectChangeEventArgs__cctor_mA2E8E5085C0016AFE1915D747016FFFA055C2F5D,
	XProcessingInstruction__ctor_m9D42D9DF59625543003025C09E1AED9659A1E862,
	XProcessingInstruction__ctor_mEF30F650C629AC6570B9C202E4EFAF58CD711F89,
	XProcessingInstruction_get_Data_mF06C89FCF9F580497E3B59331676AFC9AA3F416B,
	XProcessingInstruction_get_NodeType_mAE450ADAAE886EA5434230864F9CE7A86A3649D5,
	XProcessingInstruction_get_Target_mBB98BD38B91EBDC03558F4990591BE33FC1EB0DE,
	XProcessingInstruction_WriteTo_m56732BE1F56BF38E2F161186A83CE394CA04636C,
	XProcessingInstruction_CloneNode_m680B674C04EEE4E2C57294D645742048079D97B1,
	XProcessingInstruction_ValidateName_m945E9C60579F07D700F600E25579FA81F9D372CE,
	XText__ctor_m30153536CE7D619BF5609B3D3453109B3ECDD28D,
	XText__ctor_m57E9875B16480CE76D1F612F68AC85B2EAFBADF8,
	XText_get_NodeType_m0B5BA5C2D51F659BCD454D1566D4A5B48F62CE9C,
	XText_get_Value_m5B2A033755DE27DEC73860B9E94AA41BB55E208C,
	XText_set_Value_m285A62B03AEFA17C99B9637F5E9BD62A5E707C38,
	XText_WriteTo_m8DA512ED0623E5D736844AEF4F956CC761F834E3,
	XText_AppendText_m67BD74F5CFEBC7BF31089C195221CE2A0BB57FB2,
	XText_CloneNode_m817AC9E0BB0881591E092B3F1F3AE4B1E00F8C1E,
	StringBuilderCache_Acquire_m56CF0EE17E4DBF597E4A74230E25A18E9D4B77BE,
	StringBuilderCache_Release_m8BC1DF4DCAFAC31A5FEC78A03891D522561D0835,
	StringBuilderCache_GetStringAndRelease_mF731F5D5144F0723DFFCB719692FFAEAEDF47389,
	ThrowStub_ThrowNotSupportedException_m9860569D0F80DAC07D9ECCCAFC15125E0A3603C1,
};
extern void NamespaceCache_Get_m3493C1A45330169E117ECBCCA137BF1DE0D97ADC_AdjustorThunk (void);
extern void ElementWriter__ctor_m9799ED04B0CC92C56270CC842CEE0B55C2CFC7DA_AdjustorThunk (void);
extern void ElementWriter_WriteElement_mC858417C14469E1FF409B1C70993E2FBEF9D9FD9_AdjustorThunk (void);
extern void ElementWriter_GetPrefixOfNamespace_m6B42C7437A2CDB0F8D44DCA223E41AED4FB647EF_AdjustorThunk (void);
extern void ElementWriter_PushAncestors_mE594931ADD6A3BFAFE02B768322DDF8A6F3B8C29_AdjustorThunk (void);
extern void ElementWriter_PushElement_mDC0DBDBC8C9029B35D453BA33FAB358F33D6F19C_AdjustorThunk (void);
extern void ElementWriter_WriteEndElement_m68C45F2C6EE295852F44AAFFDD3008B353226ABC_AdjustorThunk (void);
extern void ElementWriter_WriteFullEndElement_mBE3CCBF82B1F5D975ED79C9A764B98C730703D21_AdjustorThunk (void);
extern void ElementWriter_WriteStartElement_m2B5053DBE09413D2F2284EE447A6EB9A6C8C38C2_AdjustorThunk (void);
extern void NamespaceResolver_PushScope_m59AE5DD3B165CBAE63A7087B6103E170D56E9A1C_AdjustorThunk (void);
extern void NamespaceResolver_PopScope_m84BCD5486EBB774B284EB334A641E1B9B1F1E4A8_AdjustorThunk (void);
extern void NamespaceResolver_Add_mD1691BA82CBBB75227EF918D87D05B9B2A1DED7D_AdjustorThunk (void);
extern void NamespaceResolver_AddFirst_m97452EE84946421A878A52174C7A8FF98127FB5F_AdjustorThunk (void);
extern void NamespaceResolver_GetPrefixOfNamespace_mB05121AE3616861C488DAC20D8B3E5E542428F84_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[14] = 
{
	{ 0x06000087, NamespaceCache_Get_m3493C1A45330169E117ECBCCA137BF1DE0D97ADC_AdjustorThunk },
	{ 0x06000088, ElementWriter__ctor_m9799ED04B0CC92C56270CC842CEE0B55C2CFC7DA_AdjustorThunk },
	{ 0x06000089, ElementWriter_WriteElement_mC858417C14469E1FF409B1C70993E2FBEF9D9FD9_AdjustorThunk },
	{ 0x0600008A, ElementWriter_GetPrefixOfNamespace_m6B42C7437A2CDB0F8D44DCA223E41AED4FB647EF_AdjustorThunk },
	{ 0x0600008B, ElementWriter_PushAncestors_mE594931ADD6A3BFAFE02B768322DDF8A6F3B8C29_AdjustorThunk },
	{ 0x0600008C, ElementWriter_PushElement_mDC0DBDBC8C9029B35D453BA33FAB358F33D6F19C_AdjustorThunk },
	{ 0x0600008D, ElementWriter_WriteEndElement_m68C45F2C6EE295852F44AAFFDD3008B353226ABC_AdjustorThunk },
	{ 0x0600008E, ElementWriter_WriteFullEndElement_mBE3CCBF82B1F5D975ED79C9A764B98C730703D21_AdjustorThunk },
	{ 0x0600008F, ElementWriter_WriteStartElement_m2B5053DBE09413D2F2284EE447A6EB9A6C8C38C2_AdjustorThunk },
	{ 0x06000090, NamespaceResolver_PushScope_m59AE5DD3B165CBAE63A7087B6103E170D56E9A1C_AdjustorThunk },
	{ 0x06000091, NamespaceResolver_PopScope_m84BCD5486EBB774B284EB334A641E1B9B1F1E4A8_AdjustorThunk },
	{ 0x06000092, NamespaceResolver_Add_mD1691BA82CBBB75227EF918D87D05B9B2A1DED7D_AdjustorThunk },
	{ 0x06000093, NamespaceResolver_AddFirst_m97452EE84946421A878A52174C7A8FF98127FB5F_AdjustorThunk },
	{ 0x06000094, NamespaceResolver_GetPrefixOfNamespace_mB05121AE3616861C488DAC20D8B3E5E542428F84_AdjustorThunk },
};
static const int32_t s_InvokerIndices[227] = 
{
	7631,
	6693,
	3881,
	2734,
	2734,
	2802,
	3881,
	4168,
	4250,
	4216,
	4250,
	4250,
	3518,
	7975,
	3881,
	3881,
	4216,
	3881,
	4250,
	3881,
	3881,
	4216,
	4250,
	3881,
	4250,
	4364,
	3881,
	4250,
	3881,
	4250,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	4364,
	8505,
	3881,
	2796,
	3881,
	2802,
	3881,
	3881,
	3881,
	2081,
	2322,
	1642,
	3852,
	4364,
	4168,
	4250,
	4364,
	4250,
	4250,
	4250,
	2084,
	3881,
	4250,
	4250,
	4250,
	4250,
	4364,
	3881,
	4250,
	3881,
	4216,
	4250,
	3881,
	3881,
	3881,
	4250,
	0,
	8220,
	2802,
	2053,
	3881,
	1458,
	3881,
	4250,
	4250,
	4216,
	4250,
	4250,
	3881,
	4250,
	3881,
	3881,
	3881,
	4168,
	4250,
	4216,
	4250,
	3518,
	4250,
	3518,
	3881,
	4250,
	3881,
	3881,
	3881,
	3881,
	3881,
	3881,
	4250,
	3518,
	2524,
	2796,
	2796,
	2734,
	2802,
	3852,
	4364,
	4168,
	4250,
	4364,
	4250,
	4250,
	4250,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3518,
	3881,
	3881,
	2518,
	3881,
	3881,
	4364,
	4364,
	3881,
	4364,
	4364,
	2802,
	2802,
	2518,
	4364,
	2802,
	4250,
	4250,
	4250,
	4250,
	8505,
	7631,
	8505,
	3185,
	4216,
	7261,
	3185,
	2811,
	4364,
	3881,
	4250,
	3518,
	4250,
	9031,
	9031,
	9031,
	8505,
	8505,
	3185,
	4216,
	7261,
	7261,
	1793,
	6686,
	8505,
	8505,
	7575,
	4364,
	4364,
	4250,
	0,
	3881,
	0,
	3515,
	4364,
	4250,
	0,
	4250,
	3881,
	3518,
	0,
	4168,
	4216,
	4216,
	4168,
	2322,
	2322,
	3881,
	2734,
	4168,
	4216,
	3852,
	9089,
	2802,
	3881,
	4250,
	4216,
	4250,
	3881,
	4250,
	8887,
	3881,
	3881,
	4216,
	4250,
	3881,
	3881,
	3881,
	4250,
	8501,
	8887,
	8505,
	9089,
};
static const Il2CppTokenRangePair s_rgctxIndices[4] = 
{
	{ 0x02000011, { 1, 9 } },
	{ 0x02000013, { 10, 13 } },
	{ 0x0600004D, { 0, 1 } },
	{ 0x060000C3, { 23, 1 } },
};
extern const uint32_t g_rgctx_T_t6AE14001EF83E69DDA91C9229AD90DDFB7BE32C2;
extern const uint32_t g_rgctx_ExtractKeyDelegate_tC9875002E5A4A27414DEA0109C322A7346BF1F73;
extern const uint32_t g_rgctx_XHashtableState_t22B3C62C9F2630EF1DC9DABCD13B12E54216F337;
extern const uint32_t g_rgctx_XHashtableState__ctor_m48A8D5B1CACA3CE979C7E3A4D284FF24976BDB7A;
extern const uint32_t g_rgctx_XHashtable_1_t90FECC8F1A1D788B570F64C55948894D00060891;
extern const uint32_t g_rgctx_TValueU26_tC25AE6FD3A8BA6EC820208CD6F357093CA6D8461;
extern const uint32_t g_rgctx_XHashtableState_TryGetValue_m51F57FA34C3A34048140A69DECD605610DFA2286;
extern const uint32_t g_rgctx_TValue_t3953344BBD5AABD452C9834A9E3F75B5A767B1A7;
extern const uint32_t g_rgctx_XHashtableState_TryAdd_mFE3CDCB729D3D95710A1FA266D8B4E6491156665;
extern const uint32_t g_rgctx_XHashtableState_Resize_mBB4264F964728EE2A6B30762700E8820D356C597;
extern const uint32_t g_rgctx_XHashtableState_tAAB367EBB2F3E2042378A2A2C168E35FE7E19A82;
extern const uint32_t g_rgctx_EntryU5BU5D_t758B7F87B8DE10D14B2EBE56CF7FAF51B2A09E7A;
extern const uint32_t g_rgctx_EntryU5BU5D_t758B7F87B8DE10D14B2EBE56CF7FAF51B2A09E7A;
extern const uint32_t g_rgctx_ExtractKeyDelegate_t0103AE5A70F76EA4E5725C932B4E63AED57F18B8;
extern const uint32_t g_rgctx_Entry_tF3E6891881012E597B74C0F28ED711FC28DBF267;
extern const uint32_t g_rgctx_TValue_tE0E843520D1B6FE8622D14458F0B584A2B7BCD70;
extern const uint32_t g_rgctx_ExtractKeyDelegate_Invoke_m4CA080959B14181EDBBD2D8A9E1444ABDCE28C8A;
extern const uint32_t g_rgctx_XHashtableState__ctor_m3B3498D60FA3C61028B7726E206316B86D850F7E;
extern const uint32_t g_rgctx_XHashtableState_TryAdd_mA8CF2496461D368E224C022600A76E21DDF2BD47;
extern const uint32_t g_rgctx_TValueU26_t2A033FAE22A5CE1ACCED2D68E7F1D379398F0649;
extern const uint32_t g_rgctx_XHashtableState_ComputeHashCode_m09C1C276C55AE405F61C73CFB7B8F27FE3AE9AD7;
extern const uint32_t g_rgctx_XHashtableState_tAAB367EBB2F3E2042378A2A2C168E35FE7E19A82;
extern const uint32_t g_rgctx_XHashtableState_FindEntry_mE084B1FC2671F32E9D05302CB3B66EFAC0DA2A1A;
extern const uint32_t g_rgctx_T_t186893F92D702197560EBA73AE6364E452F93DDD;
static const Il2CppRGCTXDefinition s_rgctxValues[24] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6AE14001EF83E69DDA91C9229AD90DDFB7BE32C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExtractKeyDelegate_tC9875002E5A4A27414DEA0109C322A7346BF1F73 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_XHashtableState_t22B3C62C9F2630EF1DC9DABCD13B12E54216F337 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState__ctor_m48A8D5B1CACA3CE979C7E3A4D284FF24976BDB7A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_XHashtable_1_t90FECC8F1A1D788B570F64C55948894D00060891 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_tC25AE6FD3A8BA6EC820208CD6F357093CA6D8461 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState_TryGetValue_m51F57FA34C3A34048140A69DECD605610DFA2286 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t3953344BBD5AABD452C9834A9E3F75B5A767B1A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState_TryAdd_mFE3CDCB729D3D95710A1FA266D8B4E6491156665 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState_Resize_mBB4264F964728EE2A6B30762700E8820D356C597 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_XHashtableState_tAAB367EBB2F3E2042378A2A2C168E35FE7E19A82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EntryU5BU5D_t758B7F87B8DE10D14B2EBE56CF7FAF51B2A09E7A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EntryU5BU5D_t758B7F87B8DE10D14B2EBE56CF7FAF51B2A09E7A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExtractKeyDelegate_t0103AE5A70F76EA4E5725C932B4E63AED57F18B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Entry_tF3E6891881012E597B74C0F28ED711FC28DBF267 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tE0E843520D1B6FE8622D14458F0B584A2B7BCD70 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExtractKeyDelegate_Invoke_m4CA080959B14181EDBBD2D8A9E1444ABDCE28C8A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState__ctor_m3B3498D60FA3C61028B7726E206316B86D850F7E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState_TryAdd_mA8CF2496461D368E224C022600A76E21DDF2BD47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t2A033FAE22A5CE1ACCED2D68E7F1D379398F0649 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState_ComputeHashCode_m09C1C276C55AE405F61C73CFB7B8F27FE3AE9AD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_XHashtableState_tAAB367EBB2F3E2042378A2A2C168E35FE7E19A82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XHashtableState_FindEntry_mE084B1FC2671F32E9D05302CB3B66EFAC0DA2A1A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t186893F92D702197560EBA73AE6364E452F93DDD },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationSystem_Xml_Linq;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Xml_Linq_CodeGenModule;
const Il2CppCodeGenModule g_System_Xml_Linq_CodeGenModule = 
{
	"System.Xml.Linq.dll",
	227,
	s_methodPointers,
	14,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	4,
	s_rgctxIndices,
	24,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationSystem_Xml_Linq,
	NULL,
	NULL,
	NULL,
	NULL,
};
