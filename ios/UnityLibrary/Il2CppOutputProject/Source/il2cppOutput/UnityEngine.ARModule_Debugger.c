﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[4] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_ARModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ARModule[18] = 
{
	{ 110187, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110187, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110187, 1, 35, 35, 17, 18, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110187, 1, 35, 35, 19, 129, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110187, 1, 35, 35, 19, 129, 29, kSequencePointKind_StepOut, 0, 4 },
	{ 110187, 1, 35, 35, 130, 131, 37, kSequencePointKind_Normal, 0, 5 },
	{ 110188, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110188, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110188, 1, 40, 40, 17, 18, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110188, 1, 40, 40, 19, 104, 1, kSequencePointKind_Normal, 0, 9 },
	{ 110188, 1, 40, 40, 19, 104, 22, kSequencePointKind_StepOut, 0, 10 },
	{ 110188, 1, 40, 40, 105, 106, 30, kSequencePointKind_Normal, 0, 11 },
	{ 110190, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 110190, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 110190, 1, 51, 51, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 110190, 1, 52, 52, 13, 56, 1, kSequencePointKind_Normal, 0, 15 },
	{ 110190, 1, 52, 52, 13, 56, 2, kSequencePointKind_StepOut, 0, 16 },
	{ 110190, 1, 53, 53, 9, 10, 10, kSequencePointKind_Normal, 0, 17 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_ARModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ARModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/AR/ARCore/ScriptBindings/ARCore.bindings.cs", { 42, 16, 102, 55, 220, 159, 172, 216, 254, 10, 190, 200, 8, 225, 119, 116} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14184, 1 },
	{ 14185, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[3] = 
{
	{ 0, 39 },
	{ 0, 32 },
	{ 0, 12 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[4] = 
{
	{ 39, 0, 1 },
	{ 32, 1, 1 },
	{ 0, 0, 0 },
	{ 12, 2, 1 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ARModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ARModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	18,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_ARModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
