﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[73] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_DSPGraphModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_DSPGraphModule[107] = 
{
	{ 109298, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109298, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109298, 1, 20, 20, 17, 18, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109298, 1, 20, 20, 19, 40, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109298, 1, 20, 20, 19, 40, 7, kSequencePointKind_StepOut, 0, 4 },
	{ 109298, 1, 20, 20, 42, 43, 15, kSequencePointKind_Normal, 0, 5 },
	{ 109299, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 109299, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 109299, 1, 22, 22, 13, 14, 0, kSequencePointKind_Normal, 0, 8 },
	{ 109299, 1, 23, 23, 17, 35, 1, kSequencePointKind_Normal, 0, 9 },
	{ 109299, 1, 23, 23, 0, 0, 7, kSequencePointKind_Normal, 0, 10 },
	{ 109299, 1, 24, 24, 21, 55, 10, kSequencePointKind_Normal, 0, 11 },
	{ 109299, 1, 24, 24, 21, 55, 10, kSequencePointKind_StepOut, 0, 12 },
	{ 109299, 1, 25, 25, 17, 40, 16, kSequencePointKind_Normal, 0, 13 },
	{ 109299, 1, 25, 25, 17, 40, 18, kSequencePointKind_StepOut, 0, 14 },
	{ 109299, 1, 26, 26, 17, 42, 28, kSequencePointKind_Normal, 0, 15 },
	{ 109299, 1, 27, 27, 13, 14, 40, kSequencePointKind_Normal, 0, 16 },
	{ 109300, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 17 },
	{ 109300, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 18 },
	{ 109300, 1, 32, 32, 17, 18, 0, kSequencePointKind_Normal, 0, 19 },
	{ 109300, 1, 32, 32, 19, 66, 1, kSequencePointKind_Normal, 0, 20 },
	{ 109300, 1, 32, 32, 19, 66, 2, kSequencePointKind_StepOut, 0, 21 },
	{ 109300, 1, 32, 32, 19, 66, 13, kSequencePointKind_StepOut, 0, 22 },
	{ 109300, 1, 32, 32, 67, 68, 26, kSequencePointKind_Normal, 0, 23 },
	{ 109301, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 24 },
	{ 109301, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 25 },
	{ 109301, 1, 34, 34, 13, 14, 0, kSequencePointKind_Normal, 0, 26 },
	{ 109301, 1, 35, 35, 17, 45, 1, kSequencePointKind_Normal, 0, 27 },
	{ 109301, 1, 35, 35, 0, 0, 6, kSequencePointKind_Normal, 0, 28 },
	{ 109301, 1, 36, 36, 21, 63, 9, kSequencePointKind_Normal, 0, 29 },
	{ 109301, 1, 36, 36, 21, 63, 14, kSequencePointKind_StepOut, 0, 30 },
	{ 109301, 1, 37, 37, 17, 28, 20, kSequencePointKind_Normal, 0, 31 },
	{ 109301, 1, 37, 37, 17, 28, 21, kSequencePointKind_StepOut, 0, 32 },
	{ 109301, 1, 37, 37, 0, 0, 30, kSequencePointKind_Normal, 0, 33 },
	{ 109301, 1, 38, 38, 21, 100, 33, kSequencePointKind_Normal, 0, 34 },
	{ 109301, 1, 38, 38, 21, 100, 38, kSequencePointKind_StepOut, 0, 35 },
	{ 109301, 1, 39, 39, 17, 54, 44, kSequencePointKind_Normal, 0, 36 },
	{ 109301, 1, 39, 39, 17, 54, 45, kSequencePointKind_StepOut, 0, 37 },
	{ 109301, 1, 39, 39, 0, 0, 62, kSequencePointKind_Normal, 0, 38 },
	{ 109301, 1, 40, 40, 21, 114, 65, kSequencePointKind_Normal, 0, 39 },
	{ 109301, 1, 40, 40, 21, 114, 71, kSequencePointKind_StepOut, 0, 40 },
	{ 109301, 1, 40, 40, 21, 114, 86, kSequencePointKind_StepOut, 0, 41 },
	{ 109301, 1, 40, 40, 21, 114, 91, kSequencePointKind_StepOut, 0, 42 },
	{ 109301, 1, 41, 41, 17, 40, 97, kSequencePointKind_Normal, 0, 43 },
	{ 109301, 1, 41, 41, 17, 40, 98, kSequencePointKind_StepOut, 0, 44 },
	{ 109301, 1, 42, 42, 13, 14, 109, kSequencePointKind_Normal, 0, 45 },
	{ 109302, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 46 },
	{ 109302, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 47 },
	{ 109302, 1, 46, 46, 9, 10, 0, kSequencePointKind_Normal, 0, 48 },
	{ 109302, 1, 47, 47, 13, 30, 1, kSequencePointKind_Normal, 0, 49 },
	{ 109302, 1, 47, 47, 0, 0, 7, kSequencePointKind_Normal, 0, 50 },
	{ 109302, 1, 48, 48, 17, 63, 10, kSequencePointKind_Normal, 0, 51 },
	{ 109302, 1, 48, 48, 17, 63, 15, kSequencePointKind_StepOut, 0, 52 },
	{ 109302, 1, 49, 49, 13, 44, 21, kSequencePointKind_Normal, 0, 53 },
	{ 109302, 1, 49, 49, 0, 0, 34, kSequencePointKind_Normal, 0, 54 },
	{ 109302, 1, 50, 50, 17, 91, 37, kSequencePointKind_Normal, 0, 55 },
	{ 109302, 1, 50, 50, 17, 91, 53, kSequencePointKind_StepOut, 0, 56 },
	{ 109302, 1, 50, 50, 17, 91, 58, kSequencePointKind_StepOut, 0, 57 },
	{ 109302, 1, 51, 51, 13, 37, 64, kSequencePointKind_Normal, 0, 58 },
	{ 109302, 1, 52, 52, 13, 40, 76, kSequencePointKind_Normal, 0, 59 },
	{ 109302, 1, 52, 52, 13, 40, 78, kSequencePointKind_StepOut, 0, 60 },
	{ 109302, 1, 53, 53, 9, 10, 88, kSequencePointKind_Normal, 0, 61 },
	{ 109303, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 62 },
	{ 109303, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 63 },
	{ 109303, 1, 56, 56, 9, 10, 0, kSequencePointKind_Normal, 0, 64 },
	{ 109303, 1, 57, 57, 13, 24, 1, kSequencePointKind_Normal, 0, 65 },
	{ 109303, 1, 57, 57, 13, 24, 2, kSequencePointKind_StepOut, 0, 66 },
	{ 109303, 1, 57, 57, 0, 0, 11, kSequencePointKind_Normal, 0, 67 },
	{ 109303, 1, 58, 58, 17, 97, 14, kSequencePointKind_Normal, 0, 68 },
	{ 109303, 1, 58, 58, 17, 97, 19, kSequencePointKind_StepOut, 0, 69 },
	{ 109303, 1, 59, 59, 13, 45, 25, kSequencePointKind_Normal, 0, 70 },
	{ 109303, 1, 59, 59, 13, 45, 26, kSequencePointKind_StepOut, 0, 71 },
	{ 109303, 1, 60, 60, 13, 35, 37, kSequencePointKind_Normal, 0, 72 },
	{ 109303, 1, 60, 60, 13, 35, 38, kSequencePointKind_StepOut, 0, 73 },
	{ 109303, 1, 61, 61, 9, 10, 53, kSequencePointKind_Normal, 0, 74 },
	{ 109304, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 75 },
	{ 109304, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 76 },
	{ 109304, 1, 64, 64, 9, 10, 0, kSequencePointKind_Normal, 0, 77 },
	{ 109304, 1, 65, 65, 13, 71, 1, kSequencePointKind_Normal, 0, 78 },
	{ 109304, 1, 65, 65, 13, 71, 13, kSequencePointKind_StepOut, 0, 79 },
	{ 109304, 1, 66, 66, 9, 10, 40, kSequencePointKind_Normal, 0, 80 },
	{ 109305, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 81 },
	{ 109305, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 82 },
	{ 109305, 1, 69, 69, 9, 10, 0, kSequencePointKind_Normal, 0, 83 },
	{ 109305, 1, 70, 70, 13, 44, 1, kSequencePointKind_Normal, 0, 84 },
	{ 109305, 1, 70, 70, 0, 0, 6, kSequencePointKind_Normal, 0, 85 },
	{ 109305, 1, 70, 70, 45, 58, 9, kSequencePointKind_Normal, 0, 86 },
	{ 109305, 1, 71, 71, 13, 57, 13, kSequencePointKind_Normal, 0, 87 },
	{ 109305, 1, 71, 71, 13, 57, 28, kSequencePointKind_StepOut, 0, 88 },
	{ 109305, 1, 72, 72, 9, 10, 39, kSequencePointKind_Normal, 0, 89 },
	{ 109306, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 90 },
	{ 109306, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 91 },
	{ 109306, 1, 75, 75, 9, 10, 0, kSequencePointKind_Normal, 0, 92 },
	{ 109306, 1, 77, 77, 13, 14, 1, kSequencePointKind_Normal, 0, 93 },
	{ 109306, 1, 78, 78, 17, 54, 2, kSequencePointKind_Normal, 0, 94 },
	{ 109306, 1, 78, 78, 17, 54, 8, kSequencePointKind_StepOut, 0, 95 },
	{ 109306, 1, 80, 80, 9, 10, 29, kSequencePointKind_Normal, 0, 96 },
	{ 109307, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 97 },
	{ 109307, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 98 },
	{ 109307, 1, 82, 82, 30, 85, 0, kSequencePointKind_Normal, 0, 99 },
	{ 109307, 1, 82, 82, 30, 85, 11, kSequencePointKind_StepOut, 0, 100 },
	{ 109307, 1, 82, 82, 30, 85, 19, kSequencePointKind_StepOut, 0, 101 },
	{ 109308, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 102 },
	{ 109308, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 103 },
	{ 109308, 1, 83, 83, 30, 71, 0, kSequencePointKind_Normal, 0, 104 },
	{ 109308, 1, 83, 83, 30, 71, 1, kSequencePointKind_StepOut, 0, 105 },
	{ 109308, 1, 83, 83, 30, 71, 9, kSequencePointKind_StepOut, 0, 106 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_DSPGraphModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_DSPGraphModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/DSPGraph/Public/ScriptBindings/AudioHandle.bindings.cs", { 82, 39, 159, 187, 236, 62, 254, 177, 250, 81, 180, 156, 253, 206, 199, 136} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = 
{
	{ 14041, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[9] = 
{
	{ 0, 17 },
	{ 0, 41 },
	{ 0, 28 },
	{ 0, 110 },
	{ 0, 89 },
	{ 0, 54 },
	{ 0, 42 },
	{ 0, 41 },
	{ 0, 31 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[73] = 
{
	{ 17, 0, 1 },
	{ 41, 1, 1 },
	{ 28, 2, 1 },
	{ 110, 3, 1 },
	{ 89, 4, 1 },
	{ 54, 5, 1 },
	{ 42, 6, 1 },
	{ 41, 7, 1 },
	{ 31, 8, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_DSPGraphModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_DSPGraphModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	107,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_DSPGraphModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	1,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
