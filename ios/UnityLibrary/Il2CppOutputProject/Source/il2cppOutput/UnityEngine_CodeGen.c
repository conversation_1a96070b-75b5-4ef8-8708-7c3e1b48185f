﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void GUIText__ctor_m52692E4C69D21D7A45A756E5FA2A8F4EE3C47B58 (void);
extern void GUIElement__ctor_m88ABD449B0C9E43C02E2468D3224FF091AAF22BC (void);
extern void GUILayer__ctor_m95A0C08183E042AC0AAE44E6464325499E59E076 (void);
extern void GUITexture__ctor_m8FC3C626A502F5F47C5C49DCDB1FF464961654DB (void);
extern void ProceduralMaterial__ctor_m58E68BF840CABD1788F5DCD9EE2043C7EDA87801 (void);
extern void ProceduralPropertyDescription__ctor_mFBD57770AE3E9EEA492912402BC45B1B8DFE3942 (void);
extern void ProceduralTexture__ctor_m347A5BCA76A988CF29E9966EC2481F56457DECF7 (void);
extern void DownloadHandlerMovieTexture__ctor_mE72C0213C0CD127E537B92823E307E799D100122 (void);
extern void Remote__ctor_m0494F2604ECB04C8FFAE16324B17B8C41FC8AFF6 (void);
extern void ObjectSelectorHandlerWithLabelsAttribute__ctor_m589EEE7FF139A804B82344A4A109CE1DC62B61E8 (void);
extern void ObjectSelectorHandlerWithTagsAttribute__ctor_mA63903FD6B9D5526AC467F63A7F3245D965014AC (void);
static Il2CppMethodPointer s_methodPointers[11] = 
{
	GUIText__ctor_m52692E4C69D21D7A45A756E5FA2A8F4EE3C47B58,
	GUIElement__ctor_m88ABD449B0C9E43C02E2468D3224FF091AAF22BC,
	GUILayer__ctor_m95A0C08183E042AC0AAE44E6464325499E59E076,
	GUITexture__ctor_m8FC3C626A502F5F47C5C49DCDB1FF464961654DB,
	ProceduralMaterial__ctor_m58E68BF840CABD1788F5DCD9EE2043C7EDA87801,
	ProceduralPropertyDescription__ctor_mFBD57770AE3E9EEA492912402BC45B1B8DFE3942,
	ProceduralTexture__ctor_m347A5BCA76A988CF29E9966EC2481F56457DECF7,
	DownloadHandlerMovieTexture__ctor_mE72C0213C0CD127E537B92823E307E799D100122,
	Remote__ctor_m0494F2604ECB04C8FFAE16324B17B8C41FC8AFF6,
	ObjectSelectorHandlerWithLabelsAttribute__ctor_m589EEE7FF139A804B82344A4A109CE1DC62B61E8,
	ObjectSelectorHandlerWithTagsAttribute__ctor_mA63903FD6B9D5526AC467F63A7F3245D965014AC,
};
static const int32_t s_InvokerIndices[11] = 
{
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_CodeGenModule = 
{
	"UnityEngine.dll",
	11,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine,
	NULL,
	NULL,
	NULL,
	NULL,
};
