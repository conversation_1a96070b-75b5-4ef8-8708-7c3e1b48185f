﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ContentFileUnloadHandle_get_IsCompleted_m5D0F92CFE8B883A6EE42D66CB8F1785A9DBCD867 (void);
extern void ContentFileUnloadHandle_WaitForCompletion_m87AF6FA3C6C1A580E122D16E6AF496012408EBB6 (void);
extern void ContentFile_UnloadAsync_mF35518E83FCF4C1701AF2C6CEBF7E507490481D6 (void);
extern void ContentFile_GetObjects_mEED568134C9046222A7888BD6F2BDC2AD6DDF5F1 (void);
extern void ContentFile_GetObject_m25237CC9C570B3574294F8667DE671A6F5B7D20B (void);
extern void ContentFile_ThrowIfInvalidHandle_m0C388124E8CC1C998DEA3CA74E59C6C5EF8E5826 (void);
extern void ContentFile_ThrowIfNotComplete_m3C9627D20A4779403CBE6F4407BDB44F1970B9A1 (void);
extern void ContentFile_get_IsValid_m97FD158E14C0BD06C247FCF1EE9D6B4462A1A232 (void);
extern void ContentFile_get_LoadingStatus_m817F3B02A1703C58B781D344EA02BB88FA3491A5 (void);
extern void ContentFile_WaitForCompletion_m5C2F7F1D88033A180308C14A205CA4B55BF588B5 (void);
extern void ContentFile_get_GlobalTableDependency_mB7BA8FEBFBC1903CE4AA8FFB0F59E114B162CB95 (void);
extern void ContentSceneParameters_get_loadSceneMode_mF50EF5CFE17E68164A0A460FB6BB1803957E9455 (void);
extern void ContentSceneParameters_set_loadSceneMode_mA723C4590B3F6CCE83AE529496D8C8BF13B162F1 (void);
extern void ContentSceneParameters_get_localPhysicsMode_m194E57BACE6F3110BAD9DC90452CE37D62E0E864 (void);
extern void ContentSceneParameters_set_localPhysicsMode_m7F4B8C7194784F931536C72F8723887B1651DCBE (void);
extern void ContentSceneParameters_get_autoIntegrate_m50D1EDC471FCB8EDDB23791F46D6FBA115981B43 (void);
extern void ContentSceneParameters_set_autoIntegrate_mDB147D6E25BDAB8A084FF1311EEBD3DCE72CDCE1 (void);
extern void ContentSceneFile_get_Scene_m1B955941DFF1DB02C7FA56F76BBE85AD5046AC8C (void);
extern void ContentSceneFile_IntegrateAtEndOfFrame_m2A0F8ECA728959948FCEA16396FD424BFC2553EC (void);
extern void ContentSceneFile_get_Status_m20A09664EA7F608229537D61970E2A81B2C2781A (void);
extern void ContentSceneFile_UnloadAtEndOfFrame_mDD005CE213D061D4D5F4856D8DDA308C587EE43B (void);
extern void ContentSceneFile_WaitForLoadCompletion_m4E7B4E8C0EE9BB9840B1880846858658535B74A0 (void);
extern void ContentSceneFile_get_IsValid_mDBFF0E700D858E97FFF83D6C66BE1CEE5467D6F9 (void);
extern void ContentSceneFile_ThrowIfInvalidHandle_m4E432144CC286B283AD601ADABAD39E2F66FD60B (void);
extern void ContentLoadInterface_LoadContentFileAsync_m1F2C6A42804870B91DE9A5BA36C000D5ED3F7AC1 (void);
extern void ContentLoadInterface_ContentFile_UnloadAsync_mCBAE5AE5F74A71253490CDA0D2220E81C600FB61 (void);
extern void ContentLoadInterface_ContentFile_GetObject_mBC941A69DAE7972773B08F1861D16491B40014A6 (void);
extern void ContentLoadInterface_ContentFile_GetObjects_mB0FBE53C5A554463DA2A1711D09C3F6475354E76 (void);
extern void ContentLoadInterface_ContentFile_GetLoadingStatus_m4A9904D8864DEE383424067D0D3D238A413E4478 (void);
extern void ContentLoadInterface_ContentFile_IsHandleValid_m95CB5BDC920B66ABBBBBEE881786925B532F4C44 (void);
extern void ContentLoadInterface_get_IntegrationTimeMS_m9D8775564088728A1B086A550F8B95DA4665973C (void);
extern void ContentLoadInterface_set_IntegrationTimeMS_mE351E16D80B37D38DB17F53F4B1C9216A7DBA757 (void);
extern void ContentLoadInterface_WaitForLoadCompletion_m44EC0E1E2D4A3113DC02454861EE9A4A64061A29 (void);
extern void ContentLoadInterface_WaitForUnloadCompletion_mD69DC13908C33ADBD5C43399C85F5D0B073D04E1 (void);
extern void ContentLoadInterface_ContentFile_IsUnloadComplete_mC528B5FB981535A5B83BCC8D718431A9A656ABB8 (void);
extern void ContentLoadInterface_LoadSceneAsync_m2A91D81883613F9F9236DDC734E7423BA04B9A88 (void);
extern void ContentLoadInterface_ContentSceneFile_GetScene_m26FE6A507ACA260AAF659C862906182D4882ED54 (void);
extern void ContentLoadInterface_ContentSceneFile_GetStatus_mC0B98CEECBFCF465467538B9C1A4CC2BBD48492C (void);
extern void ContentLoadInterface_ContentSceneFile_IntegrateAtEndOfFrame_mCF08F34C8C5EC541097FC349EDAC93F7B90A1ED5 (void);
extern void ContentLoadInterface_ContentSceneFile_UnloadAtEndOfFrame_m3F5003CFE4F62E8071C1969771A16B2531B1CF9F (void);
extern void ContentLoadInterface_ContentSceneFile_IsHandleValid_m3FA8ADB79052888FAEE65C2474B097C0595C145E (void);
extern void ContentLoadInterface_ContentSceneFile_WaitForCompletion_m3C34CE2423C3B403A677CFA15FBE2B9D7C41D738 (void);
extern void ContentLoadInterface_LoadSceneAsync_m1075E8F5B0F280D3341F90FC581C7C5A587C5F63 (void);
extern void ContentLoadInterface_LoadContentFileAsync_mDABD7059AD83880C7A0F2E4F1F4001AB4B1259AD (void);
extern void ContentLoadInterface_GetContentFiles_mEE5187ED1F9AE8A37013280F17A7211BCA562599 (void);
extern void ContentLoadInterface_GetSceneFiles_mA83BAECDEB4821A44AF80C262AE22DA29F05F63C (void);
extern void ContentLoadInterface_GetIntegrationTimeMS_mF5573D8AB76B464ED7D0680A72C3C1CAD41EEB38 (void);
extern void ContentLoadInterface_SetIntegrationTimeMS_m00E97AD5DF74FC2EF8DA3AA7418B48C37F57DB54 (void);
extern void ContentLoadInterface_LoadContentFileAsync_Injected_m2F9A22FE8B3B5696C919CD89D6110006FA7BDDB2 (void);
extern void ContentLoadInterface_ContentFile_UnloadAsync_Injected_m121F128E81F5226570D37ECB8F85ECE9656C4138 (void);
extern void ContentLoadInterface_ContentFile_GetObject_Injected_m8DDE6E20B19810978F3FB99506EDA5279F1C2E08 (void);
extern void ContentLoadInterface_ContentFile_GetObjects_Injected_m099FBA96DE7D9B71CF618826C0800EACB5A44661 (void);
extern void ContentLoadInterface_ContentFile_GetLoadingStatus_Injected_m82FC4FB7486DBE7B736BD8A53367724DD3E05FC6 (void);
extern void ContentLoadInterface_ContentFile_IsHandleValid_Injected_mF18B962A640C88C92AE43937E74D3324E1119892 (void);
extern void ContentLoadInterface_WaitForLoadCompletion_Injected_mE203D301E4915D84B752442F519064303BCC5489 (void);
extern void ContentLoadInterface_WaitForUnloadCompletion_Injected_m27DCB3710D1BFD687E1E62B3D5D42C24A165FD80 (void);
extern void ContentLoadInterface_ContentFile_IsUnloadComplete_Injected_m907760B72127ADFA4ABDC3C2BED1BCE2D2FDC006 (void);
extern void ContentLoadInterface_LoadSceneAsync_Injected_m6DBA4A8505A9436B665EAE33FA7608D548F988A0 (void);
extern void ContentLoadInterface_ContentSceneFile_GetScene_Injected_m7AB9E24F3DF3ACE8E83272108EB17CCCF21A05DA (void);
extern void ContentLoadInterface_ContentSceneFile_GetStatus_Injected_m53B3A78D93E1633D5E6148FC0D6512F50DC1B155 (void);
extern void ContentLoadInterface_ContentSceneFile_IntegrateAtEndOfFrame_Injected_m8093175ABC8082EFB54D5D564657E8E1E55432E3 (void);
extern void ContentLoadInterface_ContentSceneFile_UnloadAtEndOfFrame_Injected_mEFF02EE8C4B200BCBDE7A5FB970BC83B4545D1DE (void);
extern void ContentLoadInterface_ContentSceneFile_IsHandleValid_Injected_mFAB5132BCAC77F4824F7D922BF8B4CB05985F184 (void);
extern void ContentLoadInterface_ContentSceneFile_WaitForCompletion_Injected_m7B36615B20D55FADE1B860AB94C5FD3D82559903 (void);
extern void ContentLoadInterface_GetContentFiles_Injected_m7ED73FC1704BC8F7498A429BDED3AF14948D61E1 (void);
extern void ContentLoadInterface_GetSceneFiles_Injected_m7A6CAC2590D03BAEBE8EC86908F8174240BEC574 (void);
static Il2CppMethodPointer s_methodPointers[66] = 
{
	ContentFileUnloadHandle_get_IsCompleted_m5D0F92CFE8B883A6EE42D66CB8F1785A9DBCD867,
	ContentFileUnloadHandle_WaitForCompletion_m87AF6FA3C6C1A580E122D16E6AF496012408EBB6,
	ContentFile_UnloadAsync_mF35518E83FCF4C1701AF2C6CEBF7E507490481D6,
	ContentFile_GetObjects_mEED568134C9046222A7888BD6F2BDC2AD6DDF5F1,
	ContentFile_GetObject_m25237CC9C570B3574294F8667DE671A6F5B7D20B,
	ContentFile_ThrowIfInvalidHandle_m0C388124E8CC1C998DEA3CA74E59C6C5EF8E5826,
	ContentFile_ThrowIfNotComplete_m3C9627D20A4779403CBE6F4407BDB44F1970B9A1,
	ContentFile_get_IsValid_m97FD158E14C0BD06C247FCF1EE9D6B4462A1A232,
	ContentFile_get_LoadingStatus_m817F3B02A1703C58B781D344EA02BB88FA3491A5,
	ContentFile_WaitForCompletion_m5C2F7F1D88033A180308C14A205CA4B55BF588B5,
	ContentFile_get_GlobalTableDependency_mB7BA8FEBFBC1903CE4AA8FFB0F59E114B162CB95,
	ContentSceneParameters_get_loadSceneMode_mF50EF5CFE17E68164A0A460FB6BB1803957E9455,
	ContentSceneParameters_set_loadSceneMode_mA723C4590B3F6CCE83AE529496D8C8BF13B162F1,
	ContentSceneParameters_get_localPhysicsMode_m194E57BACE6F3110BAD9DC90452CE37D62E0E864,
	ContentSceneParameters_set_localPhysicsMode_m7F4B8C7194784F931536C72F8723887B1651DCBE,
	ContentSceneParameters_get_autoIntegrate_m50D1EDC471FCB8EDDB23791F46D6FBA115981B43,
	ContentSceneParameters_set_autoIntegrate_mDB147D6E25BDAB8A084FF1311EEBD3DCE72CDCE1,
	ContentSceneFile_get_Scene_m1B955941DFF1DB02C7FA56F76BBE85AD5046AC8C,
	ContentSceneFile_IntegrateAtEndOfFrame_m2A0F8ECA728959948FCEA16396FD424BFC2553EC,
	ContentSceneFile_get_Status_m20A09664EA7F608229537D61970E2A81B2C2781A,
	ContentSceneFile_UnloadAtEndOfFrame_mDD005CE213D061D4D5F4856D8DDA308C587EE43B,
	ContentSceneFile_WaitForLoadCompletion_m4E7B4E8C0EE9BB9840B1880846858658535B74A0,
	ContentSceneFile_get_IsValid_mDBFF0E700D858E97FFF83D6C66BE1CEE5467D6F9,
	ContentSceneFile_ThrowIfInvalidHandle_m4E432144CC286B283AD601ADABAD39E2F66FD60B,
	ContentLoadInterface_LoadContentFileAsync_m1F2C6A42804870B91DE9A5BA36C000D5ED3F7AC1,
	ContentLoadInterface_ContentFile_UnloadAsync_mCBAE5AE5F74A71253490CDA0D2220E81C600FB61,
	ContentLoadInterface_ContentFile_GetObject_mBC941A69DAE7972773B08F1861D16491B40014A6,
	ContentLoadInterface_ContentFile_GetObjects_mB0FBE53C5A554463DA2A1711D09C3F6475354E76,
	ContentLoadInterface_ContentFile_GetLoadingStatus_m4A9904D8864DEE383424067D0D3D238A413E4478,
	ContentLoadInterface_ContentFile_IsHandleValid_m95CB5BDC920B66ABBBBBEE881786925B532F4C44,
	ContentLoadInterface_get_IntegrationTimeMS_m9D8775564088728A1B086A550F8B95DA4665973C,
	ContentLoadInterface_set_IntegrationTimeMS_mE351E16D80B37D38DB17F53F4B1C9216A7DBA757,
	ContentLoadInterface_WaitForLoadCompletion_m44EC0E1E2D4A3113DC02454861EE9A4A64061A29,
	ContentLoadInterface_WaitForUnloadCompletion_mD69DC13908C33ADBD5C43399C85F5D0B073D04E1,
	ContentLoadInterface_ContentFile_IsUnloadComplete_mC528B5FB981535A5B83BCC8D718431A9A656ABB8,
	ContentLoadInterface_LoadSceneAsync_m2A91D81883613F9F9236DDC734E7423BA04B9A88,
	ContentLoadInterface_ContentSceneFile_GetScene_m26FE6A507ACA260AAF659C862906182D4882ED54,
	ContentLoadInterface_ContentSceneFile_GetStatus_mC0B98CEECBFCF465467538B9C1A4CC2BBD48492C,
	ContentLoadInterface_ContentSceneFile_IntegrateAtEndOfFrame_mCF08F34C8C5EC541097FC349EDAC93F7B90A1ED5,
	ContentLoadInterface_ContentSceneFile_UnloadAtEndOfFrame_m3F5003CFE4F62E8071C1969771A16B2531B1CF9F,
	ContentLoadInterface_ContentSceneFile_IsHandleValid_m3FA8ADB79052888FAEE65C2474B097C0595C145E,
	ContentLoadInterface_ContentSceneFile_WaitForCompletion_m3C34CE2423C3B403A677CFA15FBE2B9D7C41D738,
	ContentLoadInterface_LoadSceneAsync_m1075E8F5B0F280D3341F90FC581C7C5A587C5F63,
	ContentLoadInterface_LoadContentFileAsync_mDABD7059AD83880C7A0F2E4F1F4001AB4B1259AD,
	ContentLoadInterface_GetContentFiles_mEE5187ED1F9AE8A37013280F17A7211BCA562599,
	ContentLoadInterface_GetSceneFiles_mA83BAECDEB4821A44AF80C262AE22DA29F05F63C,
	ContentLoadInterface_GetIntegrationTimeMS_mF5573D8AB76B464ED7D0680A72C3C1CAD41EEB38,
	ContentLoadInterface_SetIntegrationTimeMS_m00E97AD5DF74FC2EF8DA3AA7418B48C37F57DB54,
	ContentLoadInterface_LoadContentFileAsync_Injected_m2F9A22FE8B3B5696C919CD89D6110006FA7BDDB2,
	ContentLoadInterface_ContentFile_UnloadAsync_Injected_m121F128E81F5226570D37ECB8F85ECE9656C4138,
	ContentLoadInterface_ContentFile_GetObject_Injected_m8DDE6E20B19810978F3FB99506EDA5279F1C2E08,
	ContentLoadInterface_ContentFile_GetObjects_Injected_m099FBA96DE7D9B71CF618826C0800EACB5A44661,
	ContentLoadInterface_ContentFile_GetLoadingStatus_Injected_m82FC4FB7486DBE7B736BD8A53367724DD3E05FC6,
	ContentLoadInterface_ContentFile_IsHandleValid_Injected_mF18B962A640C88C92AE43937E74D3324E1119892,
	ContentLoadInterface_WaitForLoadCompletion_Injected_mE203D301E4915D84B752442F519064303BCC5489,
	ContentLoadInterface_WaitForUnloadCompletion_Injected_m27DCB3710D1BFD687E1E62B3D5D42C24A165FD80,
	ContentLoadInterface_ContentFile_IsUnloadComplete_Injected_m907760B72127ADFA4ABDC3C2BED1BCE2D2FDC006,
	ContentLoadInterface_LoadSceneAsync_Injected_m6DBA4A8505A9436B665EAE33FA7608D548F988A0,
	ContentLoadInterface_ContentSceneFile_GetScene_Injected_m7AB9E24F3DF3ACE8E83272108EB17CCCF21A05DA,
	ContentLoadInterface_ContentSceneFile_GetStatus_Injected_m53B3A78D93E1633D5E6148FC0D6512F50DC1B155,
	ContentLoadInterface_ContentSceneFile_IntegrateAtEndOfFrame_Injected_m8093175ABC8082EFB54D5D564657E8E1E55432E3,
	ContentLoadInterface_ContentSceneFile_UnloadAtEndOfFrame_Injected_mEFF02EE8C4B200BCBDE7A5FB970BC83B4545D1DE,
	ContentLoadInterface_ContentSceneFile_IsHandleValid_Injected_mFAB5132BCAC77F4824F7D922BF8B4CB05985F184,
	ContentLoadInterface_ContentSceneFile_WaitForCompletion_Injected_m7B36615B20D55FADE1B860AB94C5FD3D82559903,
	ContentLoadInterface_GetContentFiles_Injected_m7ED73FC1704BC8F7498A429BDED3AF14948D61E1,
	ContentLoadInterface_GetSceneFiles_Injected_m7A6CAC2590D03BAEBE8EC86908F8174240BEC574,
};
extern void ContentFileUnloadHandle_get_IsCompleted_m5D0F92CFE8B883A6EE42D66CB8F1785A9DBCD867_AdjustorThunk (void);
extern void ContentFileUnloadHandle_WaitForCompletion_m87AF6FA3C6C1A580E122D16E6AF496012408EBB6_AdjustorThunk (void);
extern void ContentFile_UnloadAsync_mF35518E83FCF4C1701AF2C6CEBF7E507490481D6_AdjustorThunk (void);
extern void ContentFile_GetObjects_mEED568134C9046222A7888BD6F2BDC2AD6DDF5F1_AdjustorThunk (void);
extern void ContentFile_GetObject_m25237CC9C570B3574294F8667DE671A6F5B7D20B_AdjustorThunk (void);
extern void ContentFile_ThrowIfInvalidHandle_m0C388124E8CC1C998DEA3CA74E59C6C5EF8E5826_AdjustorThunk (void);
extern void ContentFile_ThrowIfNotComplete_m3C9627D20A4779403CBE6F4407BDB44F1970B9A1_AdjustorThunk (void);
extern void ContentFile_get_IsValid_m97FD158E14C0BD06C247FCF1EE9D6B4462A1A232_AdjustorThunk (void);
extern void ContentFile_get_LoadingStatus_m817F3B02A1703C58B781D344EA02BB88FA3491A5_AdjustorThunk (void);
extern void ContentFile_WaitForCompletion_m5C2F7F1D88033A180308C14A205CA4B55BF588B5_AdjustorThunk (void);
extern void ContentSceneParameters_get_loadSceneMode_mF50EF5CFE17E68164A0A460FB6BB1803957E9455_AdjustorThunk (void);
extern void ContentSceneParameters_set_loadSceneMode_mA723C4590B3F6CCE83AE529496D8C8BF13B162F1_AdjustorThunk (void);
extern void ContentSceneParameters_get_localPhysicsMode_m194E57BACE6F3110BAD9DC90452CE37D62E0E864_AdjustorThunk (void);
extern void ContentSceneParameters_set_localPhysicsMode_m7F4B8C7194784F931536C72F8723887B1651DCBE_AdjustorThunk (void);
extern void ContentSceneParameters_get_autoIntegrate_m50D1EDC471FCB8EDDB23791F46D6FBA115981B43_AdjustorThunk (void);
extern void ContentSceneParameters_set_autoIntegrate_mDB147D6E25BDAB8A084FF1311EEBD3DCE72CDCE1_AdjustorThunk (void);
extern void ContentSceneFile_get_Scene_m1B955941DFF1DB02C7FA56F76BBE85AD5046AC8C_AdjustorThunk (void);
extern void ContentSceneFile_IntegrateAtEndOfFrame_m2A0F8ECA728959948FCEA16396FD424BFC2553EC_AdjustorThunk (void);
extern void ContentSceneFile_get_Status_m20A09664EA7F608229537D61970E2A81B2C2781A_AdjustorThunk (void);
extern void ContentSceneFile_UnloadAtEndOfFrame_mDD005CE213D061D4D5F4856D8DDA308C587EE43B_AdjustorThunk (void);
extern void ContentSceneFile_WaitForLoadCompletion_m4E7B4E8C0EE9BB9840B1880846858658535B74A0_AdjustorThunk (void);
extern void ContentSceneFile_get_IsValid_mDBFF0E700D858E97FFF83D6C66BE1CEE5467D6F9_AdjustorThunk (void);
extern void ContentSceneFile_ThrowIfInvalidHandle_m4E432144CC286B283AD601ADABAD39E2F66FD60B_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[23] = 
{
	{ 0x06000001, ContentFileUnloadHandle_get_IsCompleted_m5D0F92CFE8B883A6EE42D66CB8F1785A9DBCD867_AdjustorThunk },
	{ 0x06000002, ContentFileUnloadHandle_WaitForCompletion_m87AF6FA3C6C1A580E122D16E6AF496012408EBB6_AdjustorThunk },
	{ 0x06000003, ContentFile_UnloadAsync_mF35518E83FCF4C1701AF2C6CEBF7E507490481D6_AdjustorThunk },
	{ 0x06000004, ContentFile_GetObjects_mEED568134C9046222A7888BD6F2BDC2AD6DDF5F1_AdjustorThunk },
	{ 0x06000005, ContentFile_GetObject_m25237CC9C570B3574294F8667DE671A6F5B7D20B_AdjustorThunk },
	{ 0x06000006, ContentFile_ThrowIfInvalidHandle_m0C388124E8CC1C998DEA3CA74E59C6C5EF8E5826_AdjustorThunk },
	{ 0x06000007, ContentFile_ThrowIfNotComplete_m3C9627D20A4779403CBE6F4407BDB44F1970B9A1_AdjustorThunk },
	{ 0x06000008, ContentFile_get_IsValid_m97FD158E14C0BD06C247FCF1EE9D6B4462A1A232_AdjustorThunk },
	{ 0x06000009, ContentFile_get_LoadingStatus_m817F3B02A1703C58B781D344EA02BB88FA3491A5_AdjustorThunk },
	{ 0x0600000A, ContentFile_WaitForCompletion_m5C2F7F1D88033A180308C14A205CA4B55BF588B5_AdjustorThunk },
	{ 0x0600000C, ContentSceneParameters_get_loadSceneMode_mF50EF5CFE17E68164A0A460FB6BB1803957E9455_AdjustorThunk },
	{ 0x0600000D, ContentSceneParameters_set_loadSceneMode_mA723C4590B3F6CCE83AE529496D8C8BF13B162F1_AdjustorThunk },
	{ 0x0600000E, ContentSceneParameters_get_localPhysicsMode_m194E57BACE6F3110BAD9DC90452CE37D62E0E864_AdjustorThunk },
	{ 0x0600000F, ContentSceneParameters_set_localPhysicsMode_m7F4B8C7194784F931536C72F8723887B1651DCBE_AdjustorThunk },
	{ 0x06000010, ContentSceneParameters_get_autoIntegrate_m50D1EDC471FCB8EDDB23791F46D6FBA115981B43_AdjustorThunk },
	{ 0x06000011, ContentSceneParameters_set_autoIntegrate_mDB147D6E25BDAB8A084FF1311EEBD3DCE72CDCE1_AdjustorThunk },
	{ 0x06000012, ContentSceneFile_get_Scene_m1B955941DFF1DB02C7FA56F76BBE85AD5046AC8C_AdjustorThunk },
	{ 0x06000013, ContentSceneFile_IntegrateAtEndOfFrame_m2A0F8ECA728959948FCEA16396FD424BFC2553EC_AdjustorThunk },
	{ 0x06000014, ContentSceneFile_get_Status_m20A09664EA7F608229537D61970E2A81B2C2781A_AdjustorThunk },
	{ 0x06000015, ContentSceneFile_UnloadAtEndOfFrame_mDD005CE213D061D4D5F4856D8DDA308C587EE43B_AdjustorThunk },
	{ 0x06000016, ContentSceneFile_WaitForLoadCompletion_m4E7B4E8C0EE9BB9840B1880846858658535B74A0_AdjustorThunk },
	{ 0x06000017, ContentSceneFile_get_IsValid_mDBFF0E700D858E97FFF83D6C66BE1CEE5467D6F9_AdjustorThunk },
	{ 0x06000018, ContentSceneFile_ThrowIfInvalidHandle_m4E432144CC286B283AD601ADABAD39E2F66FD60B_AdjustorThunk },
};
static const int32_t s_InvokerIndices[66] = 
{
	4168,
	3166,
	4179,
	4250,
	3528,
	4364,
	4364,
	4168,
	4216,
	3166,
	8999,
	4216,
	3852,
	4216,
	3852,
	4168,
	3807,
	4294,
	4364,
	4216,
	4168,
	3166,
	4168,
	4364,
	4917,
	8872,
	7585,
	8492,
	8387,
	8209,
	9064,
	8899,
	7181,
	7181,
	8209,
	4735,
	8610,
	8388,
	8874,
	8211,
	8211,
	7182,
	4918,
	5751,
	8493,
	8493,
	9064,
	8899,
	4834,
	8867,
	7576,
	8485,
	8380,
	8200,
	7144,
	7144,
	8200,
	4675,
	7894,
	8380,
	8867,
	8200,
	8200,
	7144,
	8485,
	8485,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ContentLoadModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_ContentLoadModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_ContentLoadModule_CodeGenModule = 
{
	"UnityEngine.ContentLoadModule.dll",
	66,
	s_methodPointers,
	23,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_ContentLoadModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
