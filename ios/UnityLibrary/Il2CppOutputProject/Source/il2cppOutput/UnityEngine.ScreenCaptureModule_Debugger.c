﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[9] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_ScreenCaptureModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ScreenCaptureModule[36] = 
{
	{ 110208, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110208, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110208, 1, 9, 9, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110208, 1, 10, 10, 13, 77, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110208, 1, 10, 10, 13, 77, 4, kSequencePointKind_StepOut, 0, 4 },
	{ 110208, 1, 11, 11, 9, 10, 10, kSequencePointKind_Normal, 0, 5 },
	{ 110209, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110209, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110209, 1, 14, 14, 9, 10, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110209, 1, 15, 15, 13, 85, 1, kSequencePointKind_Normal, 0, 9 },
	{ 110209, 1, 15, 15, 13, 85, 4, kSequencePointKind_StepOut, 0, 10 },
	{ 110209, 1, 16, 16, 9, 10, 10, kSequencePointKind_Normal, 0, 11 },
	{ 110210, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 110210, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 110210, 1, 19, 19, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 110210, 1, 20, 20, 13, 63, 1, kSequencePointKind_Normal, 0, 15 },
	{ 110210, 1, 20, 20, 13, 63, 4, kSequencePointKind_StepOut, 0, 16 },
	{ 110210, 1, 21, 21, 9, 10, 10, kSequencePointKind_Normal, 0, 17 },
	{ 110211, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 18 },
	{ 110211, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 19 },
	{ 110211, 1, 24, 24, 9, 10, 0, kSequencePointKind_Normal, 0, 20 },
	{ 110211, 1, 25, 25, 13, 83, 1, kSequencePointKind_Normal, 0, 21 },
	{ 110211, 1, 25, 25, 13, 83, 3, kSequencePointKind_StepOut, 0, 22 },
	{ 110211, 1, 26, 26, 9, 10, 11, kSequencePointKind_Normal, 0, 23 },
	{ 110212, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 24 },
	{ 110212, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 25 },
	{ 110212, 1, 29, 29, 9, 10, 0, kSequencePointKind_Normal, 0, 26 },
	{ 110212, 1, 30, 30, 13, 91, 1, kSequencePointKind_Normal, 0, 27 },
	{ 110212, 1, 30, 30, 13, 91, 3, kSequencePointKind_StepOut, 0, 28 },
	{ 110212, 1, 31, 31, 9, 10, 11, kSequencePointKind_Normal, 0, 29 },
	{ 110213, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 30 },
	{ 110213, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 31 },
	{ 110213, 1, 34, 34, 9, 10, 0, kSequencePointKind_Normal, 0, 32 },
	{ 110213, 1, 35, 35, 13, 69, 1, kSequencePointKind_Normal, 0, 33 },
	{ 110213, 1, 35, 35, 13, 69, 3, kSequencePointKind_StepOut, 0, 34 },
	{ 110213, 1, 36, 36, 9, 10, 11, kSequencePointKind_Normal, 0, 35 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_ScreenCaptureModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ScreenCaptureModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/ScreenCapture/ScriptBindings/ScreenCapture.bindings.cs", { 227, 190, 249, 122, 42, 192, 93, 246, 128, 112, 178, 147, 194, 168, 236, 84} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = 
{
	{ 14192, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[3] = 
{
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 13 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[9] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 13, 0, 1 },
	{ 13, 1, 1 },
	{ 13, 2, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ScreenCaptureModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ScreenCaptureModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	36,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_ScreenCaptureModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	1,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
