﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Binding_unitytls_client_send_data_m5086C52FA8F5F51D6A764CDF277FFFBCBA6D1928 (void);
extern void Binding_unitytls_client_read_data_mF232A9B9C2DB17E25ACB540144CB2C4CD3D67C2F (void);
extern void Binding_unitytls_client_add_ciphersuite_m84984570244F915AA13080689DB22C9E07D67F9D (void);
extern void Binding_unitytls_client_get_ciphersuite_mC97F9B45F0437090A710FCB23ED5A6B32782196D (void);
extern void Binding_unitytls_client_get_ciphersuite_cnt_m747F87B3AB85A453C6D41FCFC220F79FE7172A03 (void);
extern void Binding_unitytls_client_init_config_m82074495EA2A17758484F4A7FF1C6BB2EFE198B5 (void);
extern void Binding_unitytls_client_create_mA830787311EBD55A7ACAA925541EE3A28F398026 (void);
extern void Binding_unitytls_client_destroy_m3454EE8424B3F530183F2FB04DE935EFA0D4CE0B (void);
extern void Binding_unitytls_client_init_m4A2D0AEC37B2EEAFFF7793ECE1C27B52E718C9AA (void);
extern void Binding_unitytls_client_handshake_mD90B620858219D8DF2E01747ED7DEFE6CF944F46 (void);
extern void Binding_unitytls_client_set_cookie_info_mF05314AE268C13A32F3E0C8C39FD3AA5F6D1FD97 (void);
extern void Binding_unitytls_client_get_handshake_state_m5DCB8B5E8841F1BCF2CC2E52310B30CCE0EB2199 (void);
extern void Binding_unitytls_client_get_errorsState_m7452878361ADEBEC32111BC8087D2C61F370505F (void);
extern void Binding_unitytls_client_get_state_mFA36FD1C48800D3B88D142ED9E1FFFC65F196B0F (void);
extern void Binding_unitytls_client_get_role_m1F838334033B29D9251DDD0153303DEE21B6C8EB (void);
extern void unitytls_client_on_data_callback__ctor_mD1DC95893B9380D4AE1AFDCAF2FBD67A305F7D44 (void);
extern void unitytls_client_on_data_callback_Invoke_mA3893E35B92BA659B354B72EFA3D74318081F02A (void);
extern void unitytls_client_on_data_callback_BeginInvoke_m8F8A4773B643EDF70404204F78D0A20DD944016D (void);
extern void unitytls_client_on_data_callback_EndInvoke_m25F46ADC919CC72F77913F6FD56C5EFD891038BE (void);
extern void unitytls_client_data_send_callback__ctor_mA3190917673633997FB351384D19AC2DCB09F3D4 (void);
extern void unitytls_client_data_send_callback_Invoke_m9BAC5496CD637DEBFA55F4D2BA1960C45B8AF277 (void);
extern void unitytls_client_data_send_callback_BeginInvoke_mF72FE067F367ACC8FD0A20602234371619705F06 (void);
extern void unitytls_client_data_send_callback_EndInvoke_mD68D2304E499C86858AE409CF4818A282B1AA58A (void);
extern void unitytls_client_data_receive_callback__ctor_m856BA3CAF960B07B7C62FF0A91FA3108F55FF56E (void);
extern void unitytls_client_data_receive_callback_Invoke_m569CE992486A426475D923974F91171087AAE6B8 (void);
extern void unitytls_client_data_receive_callback_BeginInvoke_m2291A35F94D21AF0F4AA2A96B72277A83361261E (void);
extern void unitytls_client_data_receive_callback_EndInvoke_mB7488D349B94A8E4BD7FFD9D9CF681B169B6E15C (void);
extern void unitytls_client_data_receive_timeout_callback__ctor_m1186BA925875A5D88925413936A79FD3FBF4F752 (void);
extern void unitytls_client_data_receive_timeout_callback_Invoke_mA9B2C79399B3AF4805A72776AF709BD0535695BF (void);
extern void unitytls_client_data_receive_timeout_callback_BeginInvoke_m429E46948CAD5B05204E630465556599D1191447 (void);
extern void unitytls_client_data_receive_timeout_callback_EndInvoke_mEE0E4E8986F982E8BEC2325236B6405FBECD34FC (void);
extern void unitytls_client_log_callback__ctor_mF4D1B69EBBC1450CE50921B0C4E648F333BA9396 (void);
extern void unitytls_client_log_callback_Invoke_m92876965C2CF1E3FEB2495C6E7FADC56AC5DBE3A (void);
extern void unitytls_client_log_callback_BeginInvoke_mA714249D066C39CB8D214755DB4329FE96CC733F (void);
extern void unitytls_client_log_callback_EndInvoke_m5B4F9DD7F0ED39C5E5E2F3FFBB810DAD8A7CCEDB (void);
extern void unitytls_tlsctx_handshake_on_blocking_callback__ctor_m46F45A2BF9542571374D297D0E56DB0E85B1742E (void);
extern void unitytls_tlsctx_handshake_on_blocking_callback_Invoke_mA1279AA4D6C03C336DE8888C33230C498FE915B5 (void);
extern void unitytls_tlsctx_handshake_on_blocking_callback_BeginInvoke_mE81D1E5D1B2AA692EF181602AB31994740AE0D21 (void);
extern void unitytls_tlsctx_handshake_on_blocking_callback_EndInvoke_mCAFC04BC7CBC45852499D18B33CD85DAE1E54945 (void);
static Il2CppMethodPointer s_methodPointers[39] = 
{
	Binding_unitytls_client_send_data_m5086C52FA8F5F51D6A764CDF277FFFBCBA6D1928,
	Binding_unitytls_client_read_data_mF232A9B9C2DB17E25ACB540144CB2C4CD3D67C2F,
	Binding_unitytls_client_add_ciphersuite_m84984570244F915AA13080689DB22C9E07D67F9D,
	Binding_unitytls_client_get_ciphersuite_mC97F9B45F0437090A710FCB23ED5A6B32782196D,
	Binding_unitytls_client_get_ciphersuite_cnt_m747F87B3AB85A453C6D41FCFC220F79FE7172A03,
	Binding_unitytls_client_init_config_m82074495EA2A17758484F4A7FF1C6BB2EFE198B5,
	Binding_unitytls_client_create_mA830787311EBD55A7ACAA925541EE3A28F398026,
	Binding_unitytls_client_destroy_m3454EE8424B3F530183F2FB04DE935EFA0D4CE0B,
	Binding_unitytls_client_init_m4A2D0AEC37B2EEAFFF7793ECE1C27B52E718C9AA,
	Binding_unitytls_client_handshake_mD90B620858219D8DF2E01747ED7DEFE6CF944F46,
	Binding_unitytls_client_set_cookie_info_mF05314AE268C13A32F3E0C8C39FD3AA5F6D1FD97,
	Binding_unitytls_client_get_handshake_state_m5DCB8B5E8841F1BCF2CC2E52310B30CCE0EB2199,
	Binding_unitytls_client_get_errorsState_m7452878361ADEBEC32111BC8087D2C61F370505F,
	Binding_unitytls_client_get_state_mFA36FD1C48800D3B88D142ED9E1FFFC65F196B0F,
	Binding_unitytls_client_get_role_m1F838334033B29D9251DDD0153303DEE21B6C8EB,
	unitytls_client_on_data_callback__ctor_mD1DC95893B9380D4AE1AFDCAF2FBD67A305F7D44,
	unitytls_client_on_data_callback_Invoke_mA3893E35B92BA659B354B72EFA3D74318081F02A,
	unitytls_client_on_data_callback_BeginInvoke_m8F8A4773B643EDF70404204F78D0A20DD944016D,
	unitytls_client_on_data_callback_EndInvoke_m25F46ADC919CC72F77913F6FD56C5EFD891038BE,
	unitytls_client_data_send_callback__ctor_mA3190917673633997FB351384D19AC2DCB09F3D4,
	unitytls_client_data_send_callback_Invoke_m9BAC5496CD637DEBFA55F4D2BA1960C45B8AF277,
	unitytls_client_data_send_callback_BeginInvoke_mF72FE067F367ACC8FD0A20602234371619705F06,
	unitytls_client_data_send_callback_EndInvoke_mD68D2304E499C86858AE409CF4818A282B1AA58A,
	unitytls_client_data_receive_callback__ctor_m856BA3CAF960B07B7C62FF0A91FA3108F55FF56E,
	unitytls_client_data_receive_callback_Invoke_m569CE992486A426475D923974F91171087AAE6B8,
	unitytls_client_data_receive_callback_BeginInvoke_m2291A35F94D21AF0F4AA2A96B72277A83361261E,
	unitytls_client_data_receive_callback_EndInvoke_mB7488D349B94A8E4BD7FFD9D9CF681B169B6E15C,
	unitytls_client_data_receive_timeout_callback__ctor_m1186BA925875A5D88925413936A79FD3FBF4F752,
	unitytls_client_data_receive_timeout_callback_Invoke_mA9B2C79399B3AF4805A72776AF709BD0535695BF,
	unitytls_client_data_receive_timeout_callback_BeginInvoke_m429E46948CAD5B05204E630465556599D1191447,
	unitytls_client_data_receive_timeout_callback_EndInvoke_mEE0E4E8986F982E8BEC2325236B6405FBECD34FC,
	unitytls_client_log_callback__ctor_mF4D1B69EBBC1450CE50921B0C4E648F333BA9396,
	unitytls_client_log_callback_Invoke_m92876965C2CF1E3FEB2495C6E7FADC56AC5DBE3A,
	unitytls_client_log_callback_BeginInvoke_mA714249D066C39CB8D214755DB4329FE96CC733F,
	unitytls_client_log_callback_EndInvoke_m5B4F9DD7F0ED39C5E5E2F3FFBB810DAD8A7CCEDB,
	unitytls_tlsctx_handshake_on_blocking_callback__ctor_m46F45A2BF9542571374D297D0E56DB0E85B1742E,
	unitytls_tlsctx_handshake_on_blocking_callback_Invoke_mA1279AA4D6C03C336DE8888C33230C498FE915B5,
	unitytls_tlsctx_handshake_on_blocking_callback_BeginInvoke_mE81D1E5D1B2AA692EF181602AB31994740AE0D21,
	unitytls_tlsctx_handshake_on_blocking_callback_EndInvoke_mCAFC04BC7CBC45852499D18B33CD85DAE1E54945,
};
static const int32_t s_InvokerIndices[39] = 
{
	6807,
	6038,
	7904,
	7807,
	8380,
	8867,
	7106,
	8867,
	8380,
	8779,
	6806,
	8779,
	7806,
	8779,
	8779,
	2798,
	1374,
	480,
	3881,
	2798,
	1117,
	480,
	3419,
	2798,
	1117,
	480,
	3419,
	2798,
	727,
	297,
	3419,
	2798,
	535,
	173,
	3881,
	2798,
	1692,
	752,
	3419,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TLSModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TLSModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TLSModule_CodeGenModule = 
{
	"UnityEngine.TLSModule.dll",
	39,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_TLSModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
