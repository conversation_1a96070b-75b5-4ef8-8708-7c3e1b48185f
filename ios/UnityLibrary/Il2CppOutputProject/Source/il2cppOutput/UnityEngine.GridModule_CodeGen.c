﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Grid_GetCellCenterLocal_m62E6CFAE046C145B8340904FAE45D043B927CAB8 (void);
extern void Grid_GetCellCenterWorld_m9972542B8C9076B7FC979A70A439DE0A3AC65F3D (void);
extern void Grid_get_cellSize_m5512593532CABA9CFC058123B923AFA483D6003E (void);
extern void Grid_set_cellSize_m9868F4574C37B62CE64E8AF776E085B54F2F1BF8 (void);
extern void Grid_get_cellGap_m9B12CA3DA5A7AC906A1E1943FABD3E1A523439A1 (void);
extern void Grid_set_cellGap_m7DFD489B9B17CE724B56B05AB0639B99F79A3D02 (void);
extern void Grid_get_cellLayout_m68897E54C769C732FC091914D07E312BC9244358 (void);
extern void Grid_set_cellLayout_m61FEB7CE68F4598356DFD0B564BFF8B15F6A7766 (void);
extern void Grid_get_cellSwizzle_m8F68B475BAA2554A80C06CAA7EDC36009E9ED5E6 (void);
extern void Grid_set_cellSwizzle_mEA7F3096C6537707CC8A1EE5FBB86AD7D68D2F5A (void);
extern void Grid_Swizzle_m7740439E445C7DCB6FD3EBB28768B9946DC410EC (void);
extern void Grid_InverseSwizzle_m16294FE6EAEE7C897AC122CC3203E050AF1E8388 (void);
extern void Grid__ctor_mF8857FF5BF55232C1C252B55DA6E34F9C15CD75C (void);
extern void Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10 (void);
extern void Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD (void);
extern void Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4 (void);
extern void Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E (void);
extern void Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1 (void);
extern void Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4 (void);
extern void GridLayout_get_cellSize_m71C86547CB23B9B3F124BC8815BE97EE5BF80543 (void);
extern void GridLayout_get_cellGap_m5FDC0C21B2071E93589B6A353DAFA7265BC1BE56 (void);
extern void GridLayout_get_cellLayout_m31F956C726F79C4823A001CBC80DA8FC7ABEF80D (void);
extern void GridLayout_get_cellSwizzle_m6AFC21A656AD668FAB42AA5FAF65EB53C6A305FD (void);
extern void GridLayout_GetBoundsLocal_m9D6FA8A2B292CE45B7AA27CBB9B91EBA3165E949 (void);
extern void GridLayout_GetBoundsLocal_mFAD4D953F5FCF5A7C42FAC81984EDE9CBD78AC08 (void);
extern void GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0 (void);
extern void GridLayout_CellToLocal_m993F6316B4D584BE5633A27757D2451927A54EC5 (void);
extern void GridLayout_LocalToCell_mEDD45C2761BEDE6CFE9C796582BED95E22DFA5AE (void);
extern void GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621 (void);
extern void GridLayout_LocalToCellInterpolated_m32D646B126793BB4E12269B61670484C080C797C (void);
extern void GridLayout_CellToWorld_m513467A7565AD77DD66F9032C76AC96BA1DC0105 (void);
extern void GridLayout_WorldToCell_m72AFBAA2458CD70A302708AA09DC2641BD95E21E (void);
extern void GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6 (void);
extern void GridLayout_WorldToLocal_m3CC7D1B5D856C92E2CA0E9D411EC5A44D9FE68CF (void);
extern void GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014 (void);
extern void GridLayout_DoNothing_mA280987BF98D257023D46C2C01902FC82EE6A00A (void);
extern void GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828 (void);
extern void GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA (void);
extern void GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21 (void);
extern void GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40 (void);
extern void GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33 (void);
extern void GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52 (void);
extern void GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145 (void);
extern void GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0 (void);
extern void GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752 (void);
extern void GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89 (void);
extern void GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E (void);
extern void GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0 (void);
extern void GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD (void);
extern void GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC (void);
static Il2CppMethodPointer s_methodPointers[50] = 
{
	Grid_GetCellCenterLocal_m62E6CFAE046C145B8340904FAE45D043B927CAB8,
	Grid_GetCellCenterWorld_m9972542B8C9076B7FC979A70A439DE0A3AC65F3D,
	Grid_get_cellSize_m5512593532CABA9CFC058123B923AFA483D6003E,
	Grid_set_cellSize_m9868F4574C37B62CE64E8AF776E085B54F2F1BF8,
	Grid_get_cellGap_m9B12CA3DA5A7AC906A1E1943FABD3E1A523439A1,
	Grid_set_cellGap_m7DFD489B9B17CE724B56B05AB0639B99F79A3D02,
	Grid_get_cellLayout_m68897E54C769C732FC091914D07E312BC9244358,
	Grid_set_cellLayout_m61FEB7CE68F4598356DFD0B564BFF8B15F6A7766,
	Grid_get_cellSwizzle_m8F68B475BAA2554A80C06CAA7EDC36009E9ED5E6,
	Grid_set_cellSwizzle_mEA7F3096C6537707CC8A1EE5FBB86AD7D68D2F5A,
	Grid_Swizzle_m7740439E445C7DCB6FD3EBB28768B9946DC410EC,
	Grid_InverseSwizzle_m16294FE6EAEE7C897AC122CC3203E050AF1E8388,
	Grid__ctor_mF8857FF5BF55232C1C252B55DA6E34F9C15CD75C,
	Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10,
	Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD,
	Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4,
	Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E,
	Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1,
	Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4,
	GridLayout_get_cellSize_m71C86547CB23B9B3F124BC8815BE97EE5BF80543,
	GridLayout_get_cellGap_m5FDC0C21B2071E93589B6A353DAFA7265BC1BE56,
	GridLayout_get_cellLayout_m31F956C726F79C4823A001CBC80DA8FC7ABEF80D,
	GridLayout_get_cellSwizzle_m6AFC21A656AD668FAB42AA5FAF65EB53C6A305FD,
	GridLayout_GetBoundsLocal_m9D6FA8A2B292CE45B7AA27CBB9B91EBA3165E949,
	GridLayout_GetBoundsLocal_mFAD4D953F5FCF5A7C42FAC81984EDE9CBD78AC08,
	GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0,
	GridLayout_CellToLocal_m993F6316B4D584BE5633A27757D2451927A54EC5,
	GridLayout_LocalToCell_mEDD45C2761BEDE6CFE9C796582BED95E22DFA5AE,
	GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621,
	GridLayout_LocalToCellInterpolated_m32D646B126793BB4E12269B61670484C080C797C,
	GridLayout_CellToWorld_m513467A7565AD77DD66F9032C76AC96BA1DC0105,
	GridLayout_WorldToCell_m72AFBAA2458CD70A302708AA09DC2641BD95E21E,
	GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6,
	GridLayout_WorldToLocal_m3CC7D1B5D856C92E2CA0E9D411EC5A44D9FE68CF,
	GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014,
	GridLayout_DoNothing_mA280987BF98D257023D46C2C01902FC82EE6A00A,
	GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828,
	GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA,
	GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21,
	GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40,
	GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33,
	GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52,
	GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145,
	GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0,
	GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752,
	GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89,
	GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E,
	GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0,
	GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD,
	GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC,
};
static const int32_t s_InvokerIndices[50] = 
{
	3671,
	3671,
	4358,
	3980,
	4358,
	3980,
	4216,
	3852,
	4216,
	3852,
	7860,
	7860,
	4364,
	3788,
	3788,
	3788,
	3788,
	6896,
	6896,
	4358,
	4358,
	4216,
	4216,
	3052,
	2252,
	2252,
	3671,
	3673,
	3670,
	3670,
	3671,
	3673,
	3670,
	3670,
	4358,
	4364,
	4364,
	3788,
	3788,
	2657,
	1904,
	2657,
	2657,
	2657,
	2657,
	2657,
	2657,
	2657,
	2657,
	3788,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_GridModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_GridModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_GridModule_CodeGenModule = 
{
	"UnityEngine.GridModule.dll",
	50,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_GridModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
