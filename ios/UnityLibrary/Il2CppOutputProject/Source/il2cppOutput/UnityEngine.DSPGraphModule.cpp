﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_DSPGraphModule[];
IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral43A726BFE4F8AD6659EF58D558BC8F20CE847DEF;
IL2CPP_EXTERN_C String_t* _stringLiteral4493EFD8DC2A8DCCD210E56C1246D88E9A581DB1;
IL2CPP_EXTERN_C String_t* _stringLiteral8389C1EE7B1447BF8BB6BA0025A2EFC49DC7E23F;
IL2CPP_EXTERN_C String_t* _stringLiteralB0955058463C93D4C40863F3E2CE710E1748DE4B;
IL2CPP_EXTERN_C String_t* _stringLiteralB7B813B0748EC1FD4AB00CE8C9061897CFA8691E;
IL2CPP_EXTERN_C String_t* _stringLiteralD43B83259CBF032EB58F1BC3B3739510397D2C31;
IL2CPP_EXTERN_C const RuntimeMethod* DSPGraphInternal_Internal_AllocateHandle_m69E97B3223CAA4EFFE358A028F59BE32A923A1F2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DSPGraphInternal_Internal_ScheduleGraph_m2930B5BB4FA10E217AC3250854D12825DBC580C0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* DSPGraphInternal_Internal_SyncFenceNoWorkSteal_m516B51B2F645FC5F2B685BFA1590F75CA740BE5E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* NodeU2A_t3D07544D7959700B986D7E7CF90D8557AB5B06AB_0_0_0_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tA4B1C23679546A60C8EDFAD02EF08A3270B0EC5D 
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct AudioMemoryManager_t6194352EA30C4FCEFE00518138154DE8FACF1AFB 
{
	union
	{
		struct
		{
		};
		uint8_t AudioMemoryManager_t6194352EA30C4FCEFE00518138154DE8FACF1AFB__padding[1];
	};
};
struct AudioOutputHookManager_t9E63B1F3496B0B6FDCBBBDFCE1493A4BF2C1C3B5 
{
	union
	{
		struct
		{
		};
		uint8_t AudioOutputHookManager_t9E63B1F3496B0B6FDCBBBDFCE1493A4BF2C1C3B5__padding[1];
	};
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct DSPCommandBlockInternal_t294EEBC9845D95F2E599B73FE792C3138EEA9129 
{
	union
	{
		struct
		{
		};
		uint8_t DSPCommandBlockInternal_t294EEBC9845D95F2E599B73FE792C3138EEA9129__padding[1];
	};
};
struct DSPGraphExecutionNode_t5493A250E7F7B91CA2D9C2416921903E800074D0 
{
	void* ___ReflectionData;
	void* ___JobStructData;
	void* ___JobData;
	void* ___ResourceContext;
	int32_t ___FunctionIndex;
	int32_t ___FenceIndex;
	int32_t ___FenceCount;
};
struct DSPGraphInternal_tB4D9177BB769619FBE13D4DBED34D71178D16B22 
{
	union
	{
		struct
		{
		};
		uint8_t DSPGraphInternal_tB4D9177BB769619FBE13D4DBED34D71178D16B22__padding[1];
	};
};
struct DSPNodeUpdateRequestHandleInternal_tA0A273138D98C2311E043087F9E9B946D45EDD5B 
{
	union
	{
		struct
		{
		};
		uint8_t DSPNodeUpdateRequestHandleInternal_tA0A273138D98C2311E043087F9E9B946D45EDD5B__padding[1];
	};
};
struct DSPSampleProviderInternal_t0DF6D7F252688C449EE59A29C689450C27510990 
{
	union
	{
		struct
		{
		};
		uint8_t DSPSampleProviderInternal_t0DF6D7F252688C449EE59A29C689450C27510990__padding[1];
	};
};
struct ExecuteContextInternal_t8116BBF8204E5B8DBD39AD477FC4B0D50DF9E488 
{
	union
	{
		struct
		{
		};
		uint8_t ExecuteContextInternal_t8116BBF8204E5B8DBD39AD477FC4B0D50DF9E488__padding[1];
	};
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08 
{
	uint64_t ___jobGroup;
	int32_t ___version;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455 
{
	uint16_t ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF 
{
	uint64_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E 
{
	int64_t ___Next;
	int32_t ___Id;
	int32_t ___Version;
	int32_t ___DidAllocate;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A 
{
	intptr_t ___m_Node;
	int32_t ___Version;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void* IntPtr_op_Explicit_m2728CBA081E79B97DDCF1D4FAD77B309CA1E94BF (intptr_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentNullException__ctor_m4A0936689D360EBC545690326B4DF187196BF2B9 (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR intptr_t IntPtr_op_Explicit_mE2CEC14C61FD5E2159A03EA2AD97F5CDC5BB9F4D (void* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162 (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8 (String_t* ___0_format, RuntimeObject* ___1_arg0, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* __this, String_t* ___0_paramName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* ___0_node, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool IntPtr_op_Equality_m7D9CDCDE9DC2A0C2C614633F4921E90187FAB271 (intptr_t ___0_value1, intptr_t ___1_value2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t IntPtr_op_Explicit_mC33775570B5AC86421DABA8F8B9885DBFF49B02F (intptr_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool IntPtr_op_Inequality_m90EFC9C4CAD9A33E309F2DDF98EE4E1DD253637B (intptr_t ___0_value1, intptr_t ___1_value2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625 (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___0_inputDeps, void* ___1_nodes, int32_t ___2_nodeCount, int32_t* ___3_childTable, void* ___4_dependencies, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___0_handle, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NodeU2A_t3D07544D7959700B986D7E7CF90D8557AB5B06AB_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 3));
		intptr_t L_0 = __this->___m_Node;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 4));
		void* L_1;
		L_1 = IntPtr_op_Explicit_m2728CBA081E79B97DDCF1D4FAD77B309CA1E94BF(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 4));
		V_0 = (Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E*)L_1;
		goto IL_000f;
	}

IL_000f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 5));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* _returnValue;
	_returnValue = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 6));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 7));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 8));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 9));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_0 = ___0_value;
		V_0 = (bool)((((intptr_t)L_0) == ((intptr_t)((uintptr_t)0)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 10));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0010;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 11));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 12));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_2 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m4A0936689D360EBC545690326B4DF187196BF2B9(L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 12));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A_RuntimeMethod_var)));
	}

IL_0010:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 13));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_3 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 14));
		intptr_t L_4;
		L_4 = IntPtr_op_Explicit_mE2CEC14C61FD5E2159A03EA2AD97F5CDC5BB9F4D((void*)L_3, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 14));
		__this->___m_Node = L_4;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 15));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_5 = ___0_value;
		NullCheck(L_5);
		int32_t L_6 = L_5->___Version;
		__this->___Version = L_6;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 16));
		return;
	}
}
IL2CPP_EXTERN_C  void Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A_AdjustorThunk (RuntimeObject* __this, Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* ___0_value, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 17));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 18));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 19));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 20));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 21));
		bool L_0;
		L_0 = Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 21));
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		G_B3_0 = (-1);
		goto IL_0017;
	}

IL_000c:
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 22));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_1;
		L_1 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 22));
		NullCheck(L_1);
		int32_t L_2 = L_1->___Id;
		G_B3_0 = L_2;
	}

IL_0017:
	{
		V_0 = G_B3_0;
		goto IL_001a;
	}

IL_001a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 23));
		int32_t L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C  int32_t Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 24));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 25));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 26));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 27));
		int32_t L_0 = ___0_value;
		V_0 = (bool)((((int32_t)L_0) == ((int32_t)(-1)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 28));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0014;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 29));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 30));
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_2 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB7B813B0748EC1FD4AB00CE8C9061897CFA8691E)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 30));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_RuntimeMethod_var)));
	}

IL_0014:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 31));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 32));
		bool L_3;
		L_3 = Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 32));
		V_1 = (bool)((((int32_t)L_3) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 33));
		bool L_4 = V_1;
		if (!L_4)
		{
			goto IL_002c;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 34));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 35));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_5 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral43A726BFE4F8AD6659EF58D558BC8F20CE847DEF)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 35));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_RuntimeMethod_var)));
	}

IL_002c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 36));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 37));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_6;
		L_6 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 37));
		NullCheck(L_6);
		int32_t L_7 = L_6->___Id;
		V_2 = (bool)((((int32_t)((((int32_t)L_7) == ((int32_t)(-1)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 38));
		bool L_8 = V_2;
		if (!L_8)
		{
			goto IL_0061;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 39));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 40));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_9;
		L_9 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 40));
		NullCheck(L_9);
		int32_t L_10 = L_9->___Id;
		int32_t L_11 = L_10;
		RuntimeObject* L_12 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_11);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 41));
		String_t* L_13;
		L_13 = String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral4493EFD8DC2A8DCCD210E56C1246D88E9A581DB1)), L_12, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 41));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 42));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_14 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_14, L_13, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 42));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_14, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_RuntimeMethod_var)));
	}

IL_0061:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 43));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 44));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_15;
		L_15 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 44));
		int32_t L_16 = ___0_value;
		NullCheck(L_15);
		L_15->___Id = L_16;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 45));
		return;
	}
}
IL2CPP_EXTERN_C  void Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* ___0_node, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_node));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 46));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 47));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 48));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 49));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_0 = ___0_node;
		V_0 = (bool)((((intptr_t)L_0) == ((intptr_t)((uintptr_t)0)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 50));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 51));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 52));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_2 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD43B83259CBF032EB58F1BC3B3739510397D2C31)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 52));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_RuntimeMethod_var)));
	}

IL_0015:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 53));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_3 = ___0_node;
		NullCheck(L_3);
		int32_t L_4 = L_3->___Id;
		V_1 = (bool)((((int32_t)((((int32_t)L_4) == ((int32_t)(-1)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 54));
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_0040;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 55));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_6 = ___0_node;
		NullCheck(L_6);
		int32_t L_7 = L_6->___Id;
		int32_t L_8 = L_7;
		RuntimeObject* L_9 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_8);
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 56));
		String_t* L_10;
		L_10 = String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral8389C1EE7B1447BF8BB6BA0025A2EFC49DC7E23F)), L_9, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 56));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 57));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_11 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_11, L_10, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 57));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_11, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_RuntimeMethod_var)));
	}

IL_0040:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 58));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_12 = ___0_node;
		NullCheck(L_12);
		int32_t L_13 = L_12->___Version;
		__this->___Version = L_13;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 59));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_14 = ___0_node;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 60));
		intptr_t L_15;
		L_15 = IntPtr_op_Explicit_mE2CEC14C61FD5E2159A03EA2AD97F5CDC5BB9F4D((void*)L_14, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 60));
		__this->___m_Node = L_15;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 61));
		return;
	}
}
IL2CPP_EXTERN_C  void Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_AdjustorThunk (RuntimeObject* __this, Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* ___0_node, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4(_thisAdjusted, ___0_node, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 62));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 63));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 64));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 65));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 66));
		bool L_0;
		L_0 = Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 66));
		V_0 = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 67));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0019;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 68));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 69));
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_2 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB0955058463C93D4C40863F3E2CE710E1748DE4B)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 69));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B_RuntimeMethod_var)));
	}

IL_0019:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 70));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 71));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_3;
		L_3 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 71));
		NullCheck(L_3);
		L_3->___Id = (-1);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 72));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 73));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_4;
		L_4 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 73));
		NullCheck(L_4);
		int32_t* L_5 = (int32_t*)(&L_4->___Version);
		int32_t* L_6 = L_5;
		int32_t L_7 = *((int32_t*)L_6);
		*((int32_t*)L_6) = (int32_t)((int32_t)il2cpp_codegen_add(L_7, 1));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 74));
		return;
	}
}
IL2CPP_EXTERN_C  void Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B(_thisAdjusted, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_other));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 75));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 76));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 77));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 78));
		intptr_t L_0 = __this->___m_Node;
		Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A L_1 = ___0_other;
		intptr_t L_2 = L_1.___m_Node;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 79));
		bool L_3;
		L_3 = IntPtr_op_Equality_m7D9CDCDE9DC2A0C2C614633F4921E90187FAB271(L_0, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 79));
		if (!L_3)
		{
			goto IL_0024;
		}
	}
	{
		int32_t L_4 = __this->___Version;
		Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A L_5 = ___0_other;
		int32_t L_6 = L_5.___Version;
		G_B3_0 = ((((int32_t)L_4) == ((int32_t)L_6))? 1 : 0);
		goto IL_0025;
	}

IL_0024:
	{
		G_B3_0 = 0;
	}

IL_0025:
	{
		V_0 = (bool)G_B3_0;
		goto IL_0028;
	}

IL_0028:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 80));
		bool L_7 = V_0;
		return L_7;
	}
}
IL2CPP_EXTERN_C  bool Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554_AdjustorThunk (RuntimeObject* __this, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A ___0_other, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	bool _returnValue;
	_returnValue = Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_obj));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 81));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 82));
	int32_t G_B5_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 83));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 84));
		RuntimeObject* L_0 = ___0_obj;
		V_0 = (bool)((((RuntimeObject*)(RuntimeObject*)L_0) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 85));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_000d;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 86));
		V_1 = (bool)0;
		goto IL_0027;
	}

IL_000d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 87));
		RuntimeObject* L_2 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_2, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_il2cpp_TypeInfo_var)))
		{
			goto IL_0023;
		}
	}
	{
		RuntimeObject* L_3 = ___0_obj;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 88));
		bool L_4;
		L_4 = Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554(__this, ((*(Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*)((Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*)(Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*)UnBox(L_3, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_il2cpp_TypeInfo_var)))), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 88));
		G_B5_0 = ((int32_t)(L_4));
		goto IL_0024;
	}

IL_0023:
	{
		G_B5_0 = 0;
	}

IL_0024:
	{
		V_1 = (bool)G_B5_0;
		goto IL_0027;
	}

IL_0027:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 89));
		bool L_5 = V_1;
		return L_5;
	}
}
IL2CPP_EXTERN_C  bool Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	bool _returnValue;
	_returnValue = Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 90));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 91));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 92));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 93));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 94));
		intptr_t L_0 = __this->___m_Node;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 95));
		int32_t L_1;
		L_1 = IntPtr_op_Explicit_mC33775570B5AC86421DABA8F8B9885DBFF49B02F(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 95));
		int32_t L_2 = __this->___Version;
		V_0 = ((int32_t)(((int32_t)il2cpp_codegen_multiply(L_1, ((int32_t)397)))^L_2));
		goto IL_001d;
	}

IL_001d:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 96));
		int32_t L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C  int32_t Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 97));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 98));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 99));
		intptr_t L_0 = __this->___m_Node;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 100));
		bool L_1;
		L_1 = IntPtr_op_Inequality_m90EFC9C4CAD9A33E309F2DDF98EE4E1DD253637B(L_0, 0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 100));
		if (!L_1)
		{
			goto IL_0027;
		}
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 101));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_2;
		L_2 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 101));
		NullCheck(L_2);
		int32_t L_3 = L_2->___Version;
		int32_t L_4 = __this->___Version;
		G_B3_0 = ((((int32_t)L_3) == ((int32_t)L_4))? 1 : 0);
		goto IL_0028;
	}

IL_0027:
	{
		G_B3_0 = 0;
	}

IL_0028:
	{
		return (bool)G_B3_0;
	}
}
IL2CPP_EXTERN_C  bool Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	bool _returnValue;
	_returnValue = Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 102));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 103));
	int32_t G_B3_0 = 0;
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 104));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 105));
		bool L_0;
		L_0 = Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 105));
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 106));
		Node_t5577650C6DCB3E708DB90A14CFE609D0FA298B3E* L_1;
		L_1 = Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DSPGraphModule + 106));
		NullCheck(L_1);
		int32_t L_2 = L_1->___Id;
		G_B3_0 = ((((int32_t)((((int32_t)L_2) == ((int32_t)(-1)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_001c;
	}

IL_001b:
	{
		G_B3_0 = 0;
	}

IL_001c:
	{
		return (bool)G_B3_0;
	}
}
IL2CPP_EXTERN_C  bool Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*>(__this + _offset);
	bool _returnValue;
	_returnValue = Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void* AudioMemoryManager_Internal_AllocateAudioMemory_m6477CDE12AEC407FA1F621DD7DF3FDE4AEF3B218 (int32_t ___0_size, int32_t ___1_alignment, const RuntimeMethod* method) 
{
	typedef void* (*AudioMemoryManager_Internal_AllocateAudioMemory_m6477CDE12AEC407FA1F621DD7DF3FDE4AEF3B218_ftn) (int32_t, int32_t);
	static AudioMemoryManager_Internal_AllocateAudioMemory_m6477CDE12AEC407FA1F621DD7DF3FDE4AEF3B218_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AudioMemoryManager_Internal_AllocateAudioMemory_m6477CDE12AEC407FA1F621DD7DF3FDE4AEF3B218_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.AudioMemoryManager::Internal_AllocateAudioMemory(System.Int32,System.Int32)");
	void* icallRetVal = _il2cpp_icall_func(___0_size, ___1_alignment);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioMemoryManager_Internal_FreeAudioMemory_mDE924139CDF3495E1DA5701BCE721BE8B91995B5 (void* ___0_memory, const RuntimeMethod* method) 
{
	typedef void (*AudioMemoryManager_Internal_FreeAudioMemory_mDE924139CDF3495E1DA5701BCE721BE8B91995B5_ftn) (void*);
	static AudioMemoryManager_Internal_FreeAudioMemory_mDE924139CDF3495E1DA5701BCE721BE8B91995B5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AudioMemoryManager_Internal_FreeAudioMemory_mDE924139CDF3495E1DA5701BCE721BE8B91995B5_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.AudioMemoryManager::Internal_FreeAudioMemory(System.Void*)");
	_il2cpp_icall_func(___0_memory);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioOutputHookManager_Internal_CreateAudioOutputHook_m79D1750B1E46DFAC54B6A6CC0EF62BD881466652 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_outputHook, void* ___1_jobReflectionData, void* ___2_jobData, const RuntimeMethod* method) 
{
	typedef void (*AudioOutputHookManager_Internal_CreateAudioOutputHook_m79D1750B1E46DFAC54B6A6CC0EF62BD881466652_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, void*);
	static AudioOutputHookManager_Internal_CreateAudioOutputHook_m79D1750B1E46DFAC54B6A6CC0EF62BD881466652_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AudioOutputHookManager_Internal_CreateAudioOutputHook_m79D1750B1E46DFAC54B6A6CC0EF62BD881466652_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.AudioOutputHookManager::Internal_CreateAudioOutputHook(Unity.Audio.Handle&,System.Void*,System.Void*)");
	_il2cpp_icall_func(___0_outputHook, ___1_jobReflectionData, ___2_jobData);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioOutputHookManager_Internal_DisposeAudioOutputHook_m341EC751DD8261C166DDB3DCE0921E1F7562E5AE (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_outputHook, const RuntimeMethod* method) 
{
	typedef void (*AudioOutputHookManager_Internal_DisposeAudioOutputHook_m341EC751DD8261C166DDB3DCE0921E1F7562E5AE_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static AudioOutputHookManager_Internal_DisposeAudioOutputHook_m341EC751DD8261C166DDB3DCE0921E1F7562E5AE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AudioOutputHookManager_Internal_DisposeAudioOutputHook_m341EC751DD8261C166DDB3DCE0921E1F7562E5AE_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.AudioOutputHookManager::Internal_DisposeAudioOutputHook(Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_outputHook);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_CreateDSPNode_mF7A179F960B4A69BE2BA8B4CA6C4A920F1D0A6CA (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, void* ___3_jobReflectionData, void* ___4_jobMemory, void* ___5_parameterDescriptionArray, int32_t ___6_parameterCount, void* ___7_sampleProviderDescriptionArray, int32_t ___8_sampleProviderCount, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_CreateDSPNode_mF7A179F960B4A69BE2BA8B4CA6C4A920F1D0A6CA_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, void*, void*, int32_t, void*, int32_t);
	static DSPCommandBlockInternal_Internal_CreateDSPNode_mF7A179F960B4A69BE2BA8B4CA6C4A920F1D0A6CA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_CreateDSPNode_mF7A179F960B4A69BE2BA8B4CA6C4A920F1D0A6CA_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_CreateDSPNode(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Void*,System.Void*,System.Void*,System.Int32,System.Void*,System.Int32)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_jobReflectionData, ___4_jobMemory, ___5_parameterDescriptionArray, ___6_parameterCount, ___7_sampleProviderDescriptionArray, ___8_sampleProviderCount);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_SetFloat_m2BAFF539218CADA8EA4A55781E669E05EC674FF7 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, void* ___3_jobReflectionData, uint32_t ___4_pIndex, float ___5_value, uint32_t ___6_interpolationLength, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_SetFloat_m2BAFF539218CADA8EA4A55781E669E05EC674FF7_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, uint32_t, float, uint32_t);
	static DSPCommandBlockInternal_Internal_SetFloat_m2BAFF539218CADA8EA4A55781E669E05EC674FF7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_SetFloat_m2BAFF539218CADA8EA4A55781E669E05EC674FF7_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_SetFloat(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Void*,System.UInt32,System.Single,System.UInt32)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_jobReflectionData, ___4_pIndex, ___5_value, ___6_interpolationLength);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_AddFloatKey_mDB6E047D8D2325F41B3B32D08351F8A8582B38F9 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, void* ___3_jobReflectionData, uint32_t ___4_pIndex, uint64_t ___5_dspClock, float ___6_value, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_AddFloatKey_mDB6E047D8D2325F41B3B32D08351F8A8582B38F9_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, uint32_t, uint64_t, float);
	static DSPCommandBlockInternal_Internal_AddFloatKey_mDB6E047D8D2325F41B3B32D08351F8A8582B38F9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_AddFloatKey_mDB6E047D8D2325F41B3B32D08351F8A8582B38F9_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_AddFloatKey(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Void*,System.UInt32,System.UInt64,System.Single)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_jobReflectionData, ___4_pIndex, ___5_dspClock, ___6_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_SustainFloat_mB3BF509D4BF71450A5728C6225C23441A750BCC9 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, void* ___3_jobReflectionData, uint32_t ___4_pIndex, uint64_t ___5_dspClock, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_SustainFloat_mB3BF509D4BF71450A5728C6225C23441A750BCC9_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, uint32_t, uint64_t);
	static DSPCommandBlockInternal_Internal_SustainFloat_mB3BF509D4BF71450A5728C6225C23441A750BCC9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_SustainFloat_mB3BF509D4BF71450A5728C6225C23441A750BCC9_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_SustainFloat(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Void*,System.UInt32,System.UInt64)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_jobReflectionData, ___4_pIndex, ___5_dspClock);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_UpdateAudioJob_mC37A6F66E87D89951F7D6AE851F0DB75967288BF (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, void* ___3_updateJobMem, void* ___4_updateJobReflectionData, void* ___5_nodeReflectionData, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_UpdateAudioJob_mC37A6F66E87D89951F7D6AE851F0DB75967288BF_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, void*, void*);
	static DSPCommandBlockInternal_Internal_UpdateAudioJob_mC37A6F66E87D89951F7D6AE851F0DB75967288BF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_UpdateAudioJob_mC37A6F66E87D89951F7D6AE851F0DB75967288BF_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_UpdateAudioJob(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Void*,System.Void*,System.Void*)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_updateJobMem, ___4_updateJobReflectionData, ___5_nodeReflectionData);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_CreateUpdateRequest_mCFE48A69148D612EF5F46A618D59E5EFFA77ABB4 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___3_request, RuntimeObject* ___4_callback, void* ___5_updateJobMem, void* ___6_updateJobReflectionData, void* ___7_nodeReflectionData, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_CreateUpdateRequest_mCFE48A69148D612EF5F46A618D59E5EFFA77ABB4_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, RuntimeObject*, void*, void*, void*);
	static DSPCommandBlockInternal_Internal_CreateUpdateRequest_mCFE48A69148D612EF5F46A618D59E5EFFA77ABB4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_CreateUpdateRequest_mCFE48A69148D612EF5F46A618D59E5EFFA77ABB4_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_CreateUpdateRequest(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Object,System.Void*,System.Void*,System.Void*)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_request, ___4_callback, ___5_updateJobMem, ___6_updateJobReflectionData, ___7_nodeReflectionData);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_ReleaseDSPNode_m06CC2F8EC21B5D44D3DF99F5D3547206D6B05417 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_ReleaseDSPNode_m06CC2F8EC21B5D44D3DF99F5D3547206D6B05417_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPCommandBlockInternal_Internal_ReleaseDSPNode_m06CC2F8EC21B5D44D3DF99F5D3547206D6B05417_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_ReleaseDSPNode_m06CC2F8EC21B5D44D3DF99F5D3547206D6B05417_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_ReleaseDSPNode(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_Connect_m054DF2EAA02C53B31D2F5961DB404F5054DA9D30 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_output, int32_t ___3_outputPort, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___4_input, int32_t ___5_inputPort, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___6_connection, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_Connect_m054DF2EAA02C53B31D2F5961DB404F5054DA9D30_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPCommandBlockInternal_Internal_Connect_m054DF2EAA02C53B31D2F5961DB404F5054DA9D30_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_Connect_m054DF2EAA02C53B31D2F5961DB404F5054DA9D30_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_Connect(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Int32,Unity.Audio.Handle&,System.Int32,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_output, ___3_outputPort, ___4_input, ___5_inputPort, ___6_connection);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_Disconnect_m5DAE137FAAD9AC00A839D79802F4E4E6202A3893 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_output, int32_t ___3_outputPort, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___4_input, int32_t ___5_inputPort, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_Disconnect_m5DAE137FAAD9AC00A839D79802F4E4E6202A3893_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t);
	static DSPCommandBlockInternal_Internal_Disconnect_m5DAE137FAAD9AC00A839D79802F4E4E6202A3893_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_Disconnect_m5DAE137FAAD9AC00A839D79802F4E4E6202A3893_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_Disconnect(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Int32,Unity.Audio.Handle&,System.Int32)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_output, ___3_outputPort, ___4_input, ___5_inputPort);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_DisconnectByHandle_mEEFF5BEF6440EDE4FD470F8D2EFD985C24AED724 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_connection, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_DisconnectByHandle_mEEFF5BEF6440EDE4FD470F8D2EFD985C24AED724_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPCommandBlockInternal_Internal_DisconnectByHandle_mEEFF5BEF6440EDE4FD470F8D2EFD985C24AED724_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_DisconnectByHandle_mEEFF5BEF6440EDE4FD470F8D2EFD985C24AED724_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_DisconnectByHandle(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_connection);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_SetAttenuation_m4292E37733553E6E4BDCA61B60A6EA515A10E5B0 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_connection, void* ___3_value, uint8_t ___4_dimension, uint32_t ___5_interpolationLength, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_SetAttenuation_m4292E37733553E6E4BDCA61B60A6EA515A10E5B0_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, uint8_t, uint32_t);
	static DSPCommandBlockInternal_Internal_SetAttenuation_m4292E37733553E6E4BDCA61B60A6EA515A10E5B0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_SetAttenuation_m4292E37733553E6E4BDCA61B60A6EA515A10E5B0_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_SetAttenuation(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Void*,System.Byte,System.UInt32)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_connection, ___3_value, ___4_dimension, ___5_interpolationLength);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_AddAttenuationKey_m53CF3B9C8F8E10BB7C423A4BE86E8D260A2B5A97 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_connection, uint64_t ___3_dspClock, void* ___4_value, uint8_t ___5_dimension, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_AddAttenuationKey_m53CF3B9C8F8E10BB7C423A4BE86E8D260A2B5A97_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, uint64_t, void*, uint8_t);
	static DSPCommandBlockInternal_Internal_AddAttenuationKey_m53CF3B9C8F8E10BB7C423A4BE86E8D260A2B5A97_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_AddAttenuationKey_m53CF3B9C8F8E10BB7C423A4BE86E8D260A2B5A97_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_AddAttenuationKey(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.UInt64,System.Void*,System.Byte)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_connection, ___3_dspClock, ___4_value, ___5_dimension);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_SustainAttenuation_m1EA00E4B260E23EF2336D75119403DC89E31ABE7 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_connection, uint64_t ___3_dspClock, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_SustainAttenuation_m1EA00E4B260E23EF2336D75119403DC89E31ABE7_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, uint64_t);
	static DSPCommandBlockInternal_Internal_SustainAttenuation_m1EA00E4B260E23EF2336D75119403DC89E31ABE7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_SustainAttenuation_m1EA00E4B260E23EF2336D75119403DC89E31ABE7_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_SustainAttenuation(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.UInt64)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_connection, ___3_dspClock);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_AddInletPort_mB87A548C9E61181DF109B8026228D4C519BC5471 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, int32_t ___3_channelCount, int32_t ___4_format, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_AddInletPort_mB87A548C9E61181DF109B8026228D4C519BC5471_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, int32_t);
	static DSPCommandBlockInternal_Internal_AddInletPort_mB87A548C9E61181DF109B8026228D4C519BC5471_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_AddInletPort_mB87A548C9E61181DF109B8026228D4C519BC5471_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_AddInletPort(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Int32,System.Int32)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_channelCount, ___4_format);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_AddOutletPort_mDCA8B2779EEDDCD6C32EC5DB1220EE99C3E8B917 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, int32_t ___3_channelCount, int32_t ___4_format, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_AddOutletPort_mDCA8B2779EEDDCD6C32EC5DB1220EE99C3E8B917_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, int32_t);
	static DSPCommandBlockInternal_Internal_AddOutletPort_mDCA8B2779EEDDCD6C32EC5DB1220EE99C3E8B917_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_AddOutletPort_mDCA8B2779EEDDCD6C32EC5DB1220EE99C3E8B917_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_AddOutletPort(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Int32,System.Int32)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_channelCount, ___4_format);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_SetSampleProvider_m60DDBDF9A0577CA1F61D65FE4F348BE53CF368AE (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, int32_t ___3_item, int32_t ___4_index, uint32_t ___5_audioSampleProviderId, bool ___6_destroyOnRemove, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_SetSampleProvider_m60DDBDF9A0577CA1F61D65FE4F348BE53CF368AE_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, int32_t, uint32_t, bool);
	static DSPCommandBlockInternal_Internal_SetSampleProvider_m60DDBDF9A0577CA1F61D65FE4F348BE53CF368AE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_SetSampleProvider_m60DDBDF9A0577CA1F61D65FE4F348BE53CF368AE_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_SetSampleProvider(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Int32,System.Int32,System.UInt32,System.Boolean)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_item, ___4_index, ___5_audioSampleProviderId, ___6_destroyOnRemove);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_InsertSampleProvider_mC7823CF15825F7DD9DF57E00867129C279BE8AE4 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, int32_t ___3_item, int32_t ___4_index, uint32_t ___5_audioSampleProviderId, bool ___6_destroyOnRemove, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_InsertSampleProvider_mC7823CF15825F7DD9DF57E00867129C279BE8AE4_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, int32_t, uint32_t, bool);
	static DSPCommandBlockInternal_Internal_InsertSampleProvider_mC7823CF15825F7DD9DF57E00867129C279BE8AE4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_InsertSampleProvider_mC7823CF15825F7DD9DF57E00867129C279BE8AE4_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_InsertSampleProvider(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Int32,System.Int32,System.UInt32,System.Boolean)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_item, ___4_index, ___5_audioSampleProviderId, ___6_destroyOnRemove);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_RemoveSampleProvider_mFF427348FD2793A350A11C1D383256FCD1573A2F (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, int32_t ___3_item, int32_t ___4_index, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_RemoveSampleProvider_mFF427348FD2793A350A11C1D383256FCD1573A2F_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, int32_t);
	static DSPCommandBlockInternal_Internal_RemoveSampleProvider_mFF427348FD2793A350A11C1D383256FCD1573A2F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_RemoveSampleProvider_mFF427348FD2793A350A11C1D383256FCD1573A2F_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_RemoveSampleProvider(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&,System.Int32,System.Int32)");
	_il2cpp_icall_func(___0_graph, ___1_block, ___2_node, ___3_item, ___4_index);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_Complete_mC729AD7209B1FBA59BDB2589B2C83F247EB5F8B2 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_Complete_mC729AD7209B1FBA59BDB2589B2C83F247EB5F8B2_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPCommandBlockInternal_Internal_Complete_mC729AD7209B1FBA59BDB2589B2C83F247EB5F8B2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_Complete_mC729AD7209B1FBA59BDB2589B2C83F247EB5F8B2_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_Complete(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_block);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPCommandBlockInternal_Internal_Cancel_m152036F83982FA1BACAC4FFF89EED12282E0E6DB (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, const RuntimeMethod* method) 
{
	typedef void (*DSPCommandBlockInternal_Internal_Cancel_m152036F83982FA1BACAC4FFF89EED12282E0E6DB_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPCommandBlockInternal_Internal_Cancel_m152036F83982FA1BACAC4FFF89EED12282E0E6DB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPCommandBlockInternal_Internal_Cancel_m152036F83982FA1BACAC4FFF89EED12282E0E6DB_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPCommandBlockInternal::Internal_Cancel(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_block);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_CreateDSPGraph_m72A230BE10087CF328C66141B467D09B55A9BABF (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, int32_t ___1_outputFormat, uint32_t ___2_outputChannels, uint32_t ___3_dspBufferSize, uint32_t ___4_sampleRate, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_CreateDSPGraph_m72A230BE10087CF328C66141B467D09B55A9BABF_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, uint32_t, uint32_t, uint32_t);
	static DSPGraphInternal_Internal_CreateDSPGraph_m72A230BE10087CF328C66141B467D09B55A9BABF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_CreateDSPGraph_m72A230BE10087CF328C66141B467D09B55A9BABF_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_CreateDSPGraph(Unity.Audio.Handle&,System.Int32,System.UInt32,System.UInt32,System.UInt32)");
	_il2cpp_icall_func(___0_graph, ___1_outputFormat, ___2_outputChannels, ___3_dspBufferSize, ___4_sampleRate);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_DisposeDSPGraph_mCDCAF8DEF12C5F6DDD621BD71C966040CF2A8F46 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_DisposeDSPGraph_mCDCAF8DEF12C5F6DDD621BD71C966040CF2A8F46_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_DisposeDSPGraph_mCDCAF8DEF12C5F6DDD621BD71C966040CF2A8F46_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_DisposeDSPGraph_mCDCAF8DEF12C5F6DDD621BD71C966040CF2A8F46_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_DisposeDSPGraph(Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_CreateDSPCommandBlock_mFA2BC21F3A834F1B1E3F10BAB6526354CCEA6F6E (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_block, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_CreateDSPCommandBlock_mFA2BC21F3A834F1B1E3F10BAB6526354CCEA6F6E_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_CreateDSPCommandBlock_mFA2BC21F3A834F1B1E3F10BAB6526354CCEA6F6E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_CreateDSPCommandBlock_mFA2BC21F3A834F1B1E3F10BAB6526354CCEA6F6E_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_CreateDSPCommandBlock(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_block);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t DSPGraphInternal_Internal_AddNodeEventHandler_m188CB69F74DAD2564F4E9D09541D1DD9B74077C5 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, int64_t ___1_eventTypeHashCode, RuntimeObject* ___2_handler, const RuntimeMethod* method) 
{
	typedef uint32_t (*DSPGraphInternal_Internal_AddNodeEventHandler_m188CB69F74DAD2564F4E9D09541D1DD9B74077C5_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int64_t, RuntimeObject*);
	static DSPGraphInternal_Internal_AddNodeEventHandler_m188CB69F74DAD2564F4E9D09541D1DD9B74077C5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_AddNodeEventHandler_m188CB69F74DAD2564F4E9D09541D1DD9B74077C5_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_AddNodeEventHandler(Unity.Audio.Handle&,System.Int64,System.Object)");
	uint32_t icallRetVal = _il2cpp_icall_func(___0_graph, ___1_eventTypeHashCode, ___2_handler);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DSPGraphInternal_Internal_RemoveNodeEventHandler_mD5B5A6D981EBD95555C69371D4997B46DEF6CD46 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, uint32_t ___1_handlerId, const RuntimeMethod* method) 
{
	typedef bool (*DSPGraphInternal_Internal_RemoveNodeEventHandler_mD5B5A6D981EBD95555C69371D4997B46DEF6CD46_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, uint32_t);
	static DSPGraphInternal_Internal_RemoveNodeEventHandler_mD5B5A6D981EBD95555C69371D4997B46DEF6CD46_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_RemoveNodeEventHandler_mD5B5A6D981EBD95555C69371D4997B46DEF6CD46_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_RemoveNodeEventHandler(Unity.Audio.Handle&,System.UInt32)");
	bool icallRetVal = _il2cpp_icall_func(___0_graph, ___1_handlerId);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_GetRootDSP_m1B8CDAFAF7DA9D5C978BCC82141B800EC5258CDE (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_root, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_GetRootDSP_m1B8CDAFAF7DA9D5C978BCC82141B800EC5258CDE_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_GetRootDSP_m1B8CDAFAF7DA9D5C978BCC82141B800EC5258CDE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_GetRootDSP_m1B8CDAFAF7DA9D5C978BCC82141B800EC5258CDE_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_GetRootDSP(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_root);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t DSPGraphInternal_Internal_GetDSPClock_mE5ED88200F2CABB64145D79795A6B272048C9C50 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, const RuntimeMethod* method) 
{
	typedef uint64_t (*DSPGraphInternal_Internal_GetDSPClock_mE5ED88200F2CABB64145D79795A6B272048C9C50_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_GetDSPClock_mE5ED88200F2CABB64145D79795A6B272048C9C50_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_GetDSPClock_mE5ED88200F2CABB64145D79795A6B272048C9C50_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_GetDSPClock(Unity.Audio.Handle&)");
	uint64_t icallRetVal = _il2cpp_icall_func(___0_graph);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_BeginMix_m74026FE70451C061050B5F3734FEBDB23723E76E (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, int32_t ___1_frameCount, int32_t ___2_executionMode, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_BeginMix_m74026FE70451C061050B5F3734FEBDB23723E76E_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, int32_t, int32_t);
	static DSPGraphInternal_Internal_BeginMix_m74026FE70451C061050B5F3734FEBDB23723E76E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_BeginMix_m74026FE70451C061050B5F3734FEBDB23723E76E_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_BeginMix(Unity.Audio.Handle&,System.Int32,System.Int32)");
	_il2cpp_icall_func(___0_graph, ___1_frameCount, ___2_executionMode);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_ReadMix_mFB711B685105375970217979CE01E8405F7405E6 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, void* ___1_buffer, int32_t ___2_frameCount, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_ReadMix_mFB711B685105375970217979CE01E8405F7405E6_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, void*, int32_t);
	static DSPGraphInternal_Internal_ReadMix_mFB711B685105375970217979CE01E8405F7405E6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_ReadMix_mFB711B685105375970217979CE01E8405F7405E6_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_ReadMix(Unity.Audio.Handle&,System.Void*,System.Int32)");
	_il2cpp_icall_func(___0_graph, ___1_buffer, ___2_frameCount);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_Update_mD60D6CF4E6AB1E62281A4CE9AF4F7E70B4B6EC72 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_Update_mD60D6CF4E6AB1E62281A4CE9AF4F7E70B4B6EC72_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_Update_mD60D6CF4E6AB1E62281A4CE9AF4F7E70B4B6EC72_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_Update_mD60D6CF4E6AB1E62281A4CE9AF4F7E70B4B6EC72_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_Update(Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DSPGraphInternal_Internal_AssertMixerThread_mB04CB56D118E4565A43408939143F7FD0F06D3B2 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, const RuntimeMethod* method) 
{
	typedef bool (*DSPGraphInternal_Internal_AssertMixerThread_mB04CB56D118E4565A43408939143F7FD0F06D3B2_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_AssertMixerThread_mB04CB56D118E4565A43408939143F7FD0F06D3B2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_AssertMixerThread_mB04CB56D118E4565A43408939143F7FD0F06D3B2_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_AssertMixerThread(Unity.Audio.Handle&)");
	bool icallRetVal = _il2cpp_icall_func(___0_graph);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DSPGraphInternal_Internal_AssertMainThread_m9410E67771DB59819D755C4CCC208677D7A88CC1 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, const RuntimeMethod* method) 
{
	typedef bool (*DSPGraphInternal_Internal_AssertMainThread_m9410E67771DB59819D755C4CCC208677D7A88CC1_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_AssertMainThread_m9410E67771DB59819D755C4CCC208677D7A88CC1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_AssertMainThread_m9410E67771DB59819D755C4CCC208677D7A88CC1_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_AssertMainThread(Unity.Audio.Handle&)");
	bool icallRetVal = _il2cpp_icall_func(___0_graph);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A DSPGraphInternal_Internal_AllocateHandle_m69E97B3223CAA4EFFE358A028F59BE32A923A1F2 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DSPGraphInternal_Internal_AllocateHandle_m69E97B3223CAA4EFFE358A028F59BE32A923A1F2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DSPGraphInternal_Internal_AllocateHandle_m69E97B3223CAA4EFFE358A028F59BE32A923A1F2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* L_0 = ___0_graph;
		DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F(L_0, (&V_0), NULL);
		Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_InitializeJob_m1B01207BAFBF94072EB90293CF19F0D89F3F1EB6 (void* ___0_jobStructData, void* ___1_jobReflectionData, void* ___2_resourceContext, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_InitializeJob_m1B01207BAFBF94072EB90293CF19F0D89F3F1EB6_ftn) (void*, void*, void*);
	static DSPGraphInternal_Internal_InitializeJob_m1B01207BAFBF94072EB90293CF19F0D89F3F1EB6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_InitializeJob_m1B01207BAFBF94072EB90293CF19F0D89F3F1EB6_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_InitializeJob(System.Void*,System.Void*,System.Void*)");
	_il2cpp_icall_func(___0_jobStructData, ___1_jobReflectionData, ___2_resourceContext);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_ExecuteJob_mF6669FC184BC1FAE03CB67DA4EFF5A15E3904F90 (void* ___0_jobStructData, void* ___1_jobReflectionData, void* ___2_jobData, void* ___3_resourceContext, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_ExecuteJob_mF6669FC184BC1FAE03CB67DA4EFF5A15E3904F90_ftn) (void*, void*, void*, void*);
	static DSPGraphInternal_Internal_ExecuteJob_mF6669FC184BC1FAE03CB67DA4EFF5A15E3904F90_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_ExecuteJob_mF6669FC184BC1FAE03CB67DA4EFF5A15E3904F90_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_ExecuteJob(System.Void*,System.Void*,System.Void*,System.Void*)");
	_il2cpp_icall_func(___0_jobStructData, ___1_jobReflectionData, ___2_jobData, ___3_resourceContext);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_ExecuteUpdateJob_mE122CCFC7CB11D1C086470598A93FEDB367384EB (void* ___0_updateStructMemory, void* ___1_updateReflectionData, void* ___2_jobStructMemory, void* ___3_jobReflectionData, void* ___4_resourceContext, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___5_requestHandle, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___6_fence, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_ExecuteUpdateJob_mE122CCFC7CB11D1C086470598A93FEDB367384EB_ftn) (void*, void*, void*, void*, void*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08*);
	static DSPGraphInternal_Internal_ExecuteUpdateJob_mE122CCFC7CB11D1C086470598A93FEDB367384EB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_ExecuteUpdateJob_mE122CCFC7CB11D1C086470598A93FEDB367384EB_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_ExecuteUpdateJob(System.Void*,System.Void*,System.Void*,System.Void*,System.Void*,Unity.Audio.Handle&,Unity.Jobs.JobHandle&)");
	_il2cpp_icall_func(___0_updateStructMemory, ___1_updateReflectionData, ___2_jobStructMemory, ___3_jobReflectionData, ___4_resourceContext, ___5_requestHandle, ___6_fence);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_DisposeJob_mCCC34DC360E1875DE1151A69688E4759FA135E84 (void* ___0_jobStructData, void* ___1_jobReflectionData, void* ___2_resourceContext, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_DisposeJob_mCCC34DC360E1875DE1151A69688E4759FA135E84_ftn) (void*, void*, void*);
	static DSPGraphInternal_Internal_DisposeJob_mCCC34DC360E1875DE1151A69688E4759FA135E84_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_DisposeJob_mCCC34DC360E1875DE1151A69688E4759FA135E84_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_DisposeJob(System.Void*,System.Void*,System.Void*)");
	_il2cpp_icall_func(___0_jobStructData, ___1_jobReflectionData, ___2_resourceContext);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_ScheduleGraph_m2930B5BB4FA10E217AC3250854D12825DBC580C0 (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08 ___0_inputDeps, void* ___1_nodes, int32_t ___2_nodeCount, int32_t* ___3_childTable, void* ___4_dependencies, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DSPGraphInternal_Internal_ScheduleGraph_m2930B5BB4FA10E217AC3250854D12825DBC580C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DSPGraphInternal_Internal_ScheduleGraph_m2930B5BB4FA10E217AC3250854D12825DBC580C0_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		void* L_0 = ___1_nodes;
		int32_t L_1 = ___2_nodeCount;
		int32_t* L_2 = ___3_childTable;
		void* L_3 = ___4_dependencies;
		DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625((&___0_inputDeps), L_0, L_1, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_SyncFenceNoWorkSteal_m516B51B2F645FC5F2B685BFA1590F75CA740BE5E (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08 ___0_handle, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DSPGraphInternal_Internal_SyncFenceNoWorkSteal_m516B51B2F645FC5F2B685BFA1590F75CA740BE5E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, DSPGraphInternal_Internal_SyncFenceNoWorkSteal_m516B51B2F645FC5F2B685BFA1590F75CA740BE5E_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB((&___0_handle), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_AllocateHandle_Injected(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625 (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___0_inputDeps, void* ___1_nodes, int32_t ___2_nodeCount, int32_t* ___3_childTable, void* ___4_dependencies, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625_ftn) (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08*, void*, int32_t, int32_t*, void*);
	static DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_ScheduleGraph_Injected(Unity.Jobs.JobHandle&,System.Void*,System.Int32,System.Int32*,System.Void*)");
	_il2cpp_icall_func(___0_inputDeps, ___1_nodes, ___2_nodeCount, ___3_childTable, ___4_dependencies);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___0_handle, const RuntimeMethod* method) 
{
	typedef void (*DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB_ftn) (JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08*);
	static DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPGraphInternal::Internal_SyncFenceNoWorkSteal_Injected(Unity.Jobs.JobHandle&)");
	_il2cpp_icall_func(___0_handle);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void* DSPNodeUpdateRequestHandleInternal_Internal_GetUpdateJobData_m0128B820FAC1845E701B941E7EF0ED111EBB650F (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_requestHandle, const RuntimeMethod* method) 
{
	typedef void* (*DSPNodeUpdateRequestHandleInternal_Internal_GetUpdateJobData_m0128B820FAC1845E701B941E7EF0ED111EBB650F_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPNodeUpdateRequestHandleInternal_Internal_GetUpdateJobData_m0128B820FAC1845E701B941E7EF0ED111EBB650F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPNodeUpdateRequestHandleInternal_Internal_GetUpdateJobData_m0128B820FAC1845E701B941E7EF0ED111EBB650F_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPNodeUpdateRequestHandleInternal::Internal_GetUpdateJobData(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	void* icallRetVal = _il2cpp_icall_func(___0_graph, ___1_requestHandle);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool DSPNodeUpdateRequestHandleInternal_Internal_HasError_mCD6B9073C26ECF79028D14CB4653D87D485237BE (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_requestHandle, const RuntimeMethod* method) 
{
	typedef bool (*DSPNodeUpdateRequestHandleInternal_Internal_HasError_mCD6B9073C26ECF79028D14CB4653D87D485237BE_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPNodeUpdateRequestHandleInternal_Internal_HasError_mCD6B9073C26ECF79028D14CB4653D87D485237BE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPNodeUpdateRequestHandleInternal_Internal_HasError_mCD6B9073C26ECF79028D14CB4653D87D485237BE_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPNodeUpdateRequestHandleInternal::Internal_HasError(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	bool icallRetVal = _il2cpp_icall_func(___0_graph, ___1_requestHandle);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPNodeUpdateRequestHandleInternal_Internal_GetDSPNode_mB3A8DCAE003DC7D7F8FD4438BD5EA58DE0FE82BC (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_requestHandle, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___2_node, const RuntimeMethod* method) 
{
	typedef void (*DSPNodeUpdateRequestHandleInternal_Internal_GetDSPNode_mB3A8DCAE003DC7D7F8FD4438BD5EA58DE0FE82BC_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPNodeUpdateRequestHandleInternal_Internal_GetDSPNode_mB3A8DCAE003DC7D7F8FD4438BD5EA58DE0FE82BC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPNodeUpdateRequestHandleInternal_Internal_GetDSPNode_mB3A8DCAE003DC7D7F8FD4438BD5EA58DE0FE82BC_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPNodeUpdateRequestHandleInternal::Internal_GetDSPNode(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_requestHandle, ___2_node);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPNodeUpdateRequestHandleInternal_Internal_GetFence_mC49E96BD64E8B0A4105A12630E552E1E57CC6155 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_requestHandle, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08* ___2_fence, const RuntimeMethod* method) 
{
	typedef void (*DSPNodeUpdateRequestHandleInternal_Internal_GetFence_mC49E96BD64E8B0A4105A12630E552E1E57CC6155_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, JobHandle_t5DF5F99902FED3C801A81C05205CEA6CE039EF08*);
	static DSPNodeUpdateRequestHandleInternal_Internal_GetFence_mC49E96BD64E8B0A4105A12630E552E1E57CC6155_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPNodeUpdateRequestHandleInternal_Internal_GetFence_mC49E96BD64E8B0A4105A12630E552E1E57CC6155_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPNodeUpdateRequestHandleInternal::Internal_GetFence(Unity.Audio.Handle&,Unity.Audio.Handle&,Unity.Jobs.JobHandle&)");
	_il2cpp_icall_func(___0_graph, ___1_requestHandle, ___2_fence);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DSPNodeUpdateRequestHandleInternal_Internal_Dispose_m471B6C9D926E3060B016B948E6D76BD191D11FA6 (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___0_graph, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A* ___1_requestHandle, const RuntimeMethod* method) 
{
	typedef void (*DSPNodeUpdateRequestHandleInternal_Internal_Dispose_m471B6C9D926E3060B016B948E6D76BD191D11FA6_ftn) (Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*, Handle_t7BA00291B5066C7B0343BD8FB5E89633633AF35A*);
	static DSPNodeUpdateRequestHandleInternal_Internal_Dispose_m471B6C9D926E3060B016B948E6D76BD191D11FA6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPNodeUpdateRequestHandleInternal_Internal_Dispose_m471B6C9D926E3060B016B948E6D76BD191D11FA6_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPNodeUpdateRequestHandleInternal::Internal_Dispose(Unity.Audio.Handle&,Unity.Audio.Handle&)");
	_il2cpp_icall_func(___0_graph, ___1_requestHandle);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProvider_m452593870CB755E994B7AD42D8C2DE192480B835 (void* ___0_provider, int32_t ___1_format, void* ___2_buffer, int32_t ___3_length, const RuntimeMethod* method) 
{
	typedef int32_t (*DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProvider_m452593870CB755E994B7AD42D8C2DE192480B835_ftn) (void*, int32_t, void*, int32_t);
	static DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProvider_m452593870CB755E994B7AD42D8C2DE192480B835_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProvider_m452593870CB755E994B7AD42D8C2DE192480B835_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_ReadUInt8FromSampleProvider(System.Void*,System.Int32,System.Void*,System.Int32)");
	int32_t icallRetVal = _il2cpp_icall_func(___0_provider, ___1_format, ___2_buffer, ___3_length);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProvider_mBABB781CA231ED433404E196767651697022D5A2 (void* ___0_provider, int32_t ___1_format, void* ___2_buffer, int32_t ___3_length, const RuntimeMethod* method) 
{
	typedef int32_t (*DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProvider_mBABB781CA231ED433404E196767651697022D5A2_ftn) (void*, int32_t, void*, int32_t);
	static DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProvider_mBABB781CA231ED433404E196767651697022D5A2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProvider_mBABB781CA231ED433404E196767651697022D5A2_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_ReadSInt16FromSampleProvider(System.Void*,System.Int32,System.Void*,System.Int32)");
	int32_t icallRetVal = _il2cpp_icall_func(___0_provider, ___1_format, ___2_buffer, ___3_length);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DSPSampleProviderInternal_Internal_ReadFloatFromSampleProvider_m214E1C1E2D627E77536EFAC67C228912D3A6A0D0 (void* ___0_provider, void* ___1_buffer, int32_t ___2_length, const RuntimeMethod* method) 
{
	typedef int32_t (*DSPSampleProviderInternal_Internal_ReadFloatFromSampleProvider_m214E1C1E2D627E77536EFAC67C228912D3A6A0D0_ftn) (void*, void*, int32_t);
	static DSPSampleProviderInternal_Internal_ReadFloatFromSampleProvider_m214E1C1E2D627E77536EFAC67C228912D3A6A0D0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_ReadFloatFromSampleProvider_m214E1C1E2D627E77536EFAC67C228912D3A6A0D0_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_ReadFloatFromSampleProvider(System.Void*,System.Void*,System.Int32)");
	int32_t icallRetVal = _il2cpp_icall_func(___0_provider, ___1_buffer, ___2_length);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t DSPSampleProviderInternal_Internal_GetChannelCount_m0F11CAAD6596B88F7E56BDABDA99E1F21D3EDC8A (void* ___0_provider, const RuntimeMethod* method) 
{
	typedef uint16_t (*DSPSampleProviderInternal_Internal_GetChannelCount_m0F11CAAD6596B88F7E56BDABDA99E1F21D3EDC8A_ftn) (void*);
	static DSPSampleProviderInternal_Internal_GetChannelCount_m0F11CAAD6596B88F7E56BDABDA99E1F21D3EDC8A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_GetChannelCount_m0F11CAAD6596B88F7E56BDABDA99E1F21D3EDC8A_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_GetChannelCount(System.Void*)");
	uint16_t icallRetVal = _il2cpp_icall_func(___0_provider);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t DSPSampleProviderInternal_Internal_GetSampleRate_m679B37C8351CADEA6FF53F99B5A71725416D2B38 (void* ___0_provider, const RuntimeMethod* method) 
{
	typedef uint32_t (*DSPSampleProviderInternal_Internal_GetSampleRate_m679B37C8351CADEA6FF53F99B5A71725416D2B38_ftn) (void*);
	static DSPSampleProviderInternal_Internal_GetSampleRate_m679B37C8351CADEA6FF53F99B5A71725416D2B38_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_GetSampleRate_m679B37C8351CADEA6FF53F99B5A71725416D2B38_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_GetSampleRate(System.Void*)");
	uint32_t icallRetVal = _il2cpp_icall_func(___0_provider);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProviderById_m1B5A33CD09C3CBC636E5E1BC2C1381DB8346B605 (uint32_t ___0_providerId, int32_t ___1_format, void* ___2_buffer, int32_t ___3_length, const RuntimeMethod* method) 
{
	typedef int32_t (*DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProviderById_m1B5A33CD09C3CBC636E5E1BC2C1381DB8346B605_ftn) (uint32_t, int32_t, void*, int32_t);
	static DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProviderById_m1B5A33CD09C3CBC636E5E1BC2C1381DB8346B605_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProviderById_m1B5A33CD09C3CBC636E5E1BC2C1381DB8346B605_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_ReadUInt8FromSampleProviderById(System.UInt32,System.Int32,System.Void*,System.Int32)");
	int32_t icallRetVal = _il2cpp_icall_func(___0_providerId, ___1_format, ___2_buffer, ___3_length);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProviderById_m1C3099A8576A8D28FD89BAFF4397FF937F18FEEA (uint32_t ___0_providerId, int32_t ___1_format, void* ___2_buffer, int32_t ___3_length, const RuntimeMethod* method) 
{
	typedef int32_t (*DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProviderById_m1C3099A8576A8D28FD89BAFF4397FF937F18FEEA_ftn) (uint32_t, int32_t, void*, int32_t);
	static DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProviderById_m1C3099A8576A8D28FD89BAFF4397FF937F18FEEA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProviderById_m1C3099A8576A8D28FD89BAFF4397FF937F18FEEA_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_ReadSInt16FromSampleProviderById(System.UInt32,System.Int32,System.Void*,System.Int32)");
	int32_t icallRetVal = _il2cpp_icall_func(___0_providerId, ___1_format, ___2_buffer, ___3_length);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DSPSampleProviderInternal_Internal_ReadFloatFromSampleProviderById_m585D8447ECFDDE270DE19750436DEC7A88A515D8 (uint32_t ___0_providerId, void* ___1_buffer, int32_t ___2_length, const RuntimeMethod* method) 
{
	typedef int32_t (*DSPSampleProviderInternal_Internal_ReadFloatFromSampleProviderById_m585D8447ECFDDE270DE19750436DEC7A88A515D8_ftn) (uint32_t, void*, int32_t);
	static DSPSampleProviderInternal_Internal_ReadFloatFromSampleProviderById_m585D8447ECFDDE270DE19750436DEC7A88A515D8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_ReadFloatFromSampleProviderById_m585D8447ECFDDE270DE19750436DEC7A88A515D8_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_ReadFloatFromSampleProviderById(System.UInt32,System.Void*,System.Int32)");
	int32_t icallRetVal = _il2cpp_icall_func(___0_providerId, ___1_buffer, ___2_length);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t DSPSampleProviderInternal_Internal_GetChannelCountById_mF9B7C00D8DFB78E5F40C8BA0D3E6BA75BE00DB63 (uint32_t ___0_providerId, const RuntimeMethod* method) 
{
	typedef uint16_t (*DSPSampleProviderInternal_Internal_GetChannelCountById_mF9B7C00D8DFB78E5F40C8BA0D3E6BA75BE00DB63_ftn) (uint32_t);
	static DSPSampleProviderInternal_Internal_GetChannelCountById_mF9B7C00D8DFB78E5F40C8BA0D3E6BA75BE00DB63_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_GetChannelCountById_mF9B7C00D8DFB78E5F40C8BA0D3E6BA75BE00DB63_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_GetChannelCountById(System.UInt32)");
	uint16_t icallRetVal = _il2cpp_icall_func(___0_providerId);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t DSPSampleProviderInternal_Internal_GetSampleRateById_m52C797917E17C7E639BBC8CE8A65C251473C3924 (uint32_t ___0_providerId, const RuntimeMethod* method) 
{
	typedef uint32_t (*DSPSampleProviderInternal_Internal_GetSampleRateById_m52C797917E17C7E639BBC8CE8A65C251473C3924_ftn) (uint32_t);
	static DSPSampleProviderInternal_Internal_GetSampleRateById_m52C797917E17C7E639BBC8CE8A65C251473C3924_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (DSPSampleProviderInternal_Internal_GetSampleRateById_m52C797917E17C7E639BBC8CE8A65C251473C3924_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.DSPSampleProviderInternal::Internal_GetSampleRateById(System.UInt32)");
	uint32_t icallRetVal = _il2cpp_icall_func(___0_providerId);
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExecuteContextInternal_Internal_PostEvent_m85AEE83FF62AF2694A6153A056AB8C711BE7E685 (void* ___0_dspNodePtr, int64_t ___1_eventTypeHashCode, void* ___2_eventPtr, int32_t ___3_eventSize, const RuntimeMethod* method) 
{
	typedef void (*ExecuteContextInternal_Internal_PostEvent_m85AEE83FF62AF2694A6153A056AB8C711BE7E685_ftn) (void*, int64_t, void*, int32_t);
	static ExecuteContextInternal_Internal_PostEvent_m85AEE83FF62AF2694A6153A056AB8C711BE7E685_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (ExecuteContextInternal_Internal_PostEvent_m85AEE83FF62AF2694A6153A056AB8C711BE7E685_ftn)il2cpp_codegen_resolve_icall ("Unity.Audio.ExecuteContextInternal::Internal_PostEvent(System.Void*,System.Int64,System.Void*,System.Int32)");
	_il2cpp_icall_func(___0_dspNodePtr, ___1_eventTypeHashCode, ___2_eventPtr, ___3_eventSize);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
