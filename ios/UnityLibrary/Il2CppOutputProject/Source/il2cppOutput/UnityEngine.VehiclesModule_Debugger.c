﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[61] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_VehiclesModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VehiclesModule[80] = 
{
	{ 109933, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109933, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109933, 1, 20, 20, 40, 41, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109933, 1, 20, 20, 42, 60, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109933, 1, 20, 20, 61, 62, 10, kSequencePointKind_Normal, 0, 4 },
	{ 109934, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 5 },
	{ 109934, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 6 },
	{ 109934, 1, 20, 20, 67, 68, 0, kSequencePointKind_Normal, 0, 7 },
	{ 109934, 1, 20, 20, 69, 88, 1, kSequencePointKind_Normal, 0, 8 },
	{ 109934, 1, 20, 20, 89, 90, 8, kSequencePointKind_Normal, 0, 9 },
	{ 109935, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 10 },
	{ 109935, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 11 },
	{ 109935, 1, 22, 22, 39, 40, 0, kSequencePointKind_Normal, 0, 12 },
	{ 109935, 1, 22, 22, 41, 56, 1, kSequencePointKind_Normal, 0, 13 },
	{ 109935, 1, 22, 22, 57, 58, 10, kSequencePointKind_Normal, 0, 14 },
	{ 109936, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 15 },
	{ 109936, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 16 },
	{ 109936, 1, 22, 22, 63, 64, 0, kSequencePointKind_Normal, 0, 17 },
	{ 109936, 1, 22, 22, 65, 81, 1, kSequencePointKind_Normal, 0, 18 },
	{ 109936, 1, 22, 22, 82, 83, 8, kSequencePointKind_Normal, 0, 19 },
	{ 109937, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 20 },
	{ 109937, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 21 },
	{ 109937, 1, 23, 23, 40, 41, 0, kSequencePointKind_Normal, 0, 22 },
	{ 109937, 1, 23, 23, 42, 58, 1, kSequencePointKind_Normal, 0, 23 },
	{ 109937, 1, 23, 23, 59, 60, 10, kSequencePointKind_Normal, 0, 24 },
	{ 109938, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 25 },
	{ 109938, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 26 },
	{ 109938, 1, 23, 23, 65, 66, 0, kSequencePointKind_Normal, 0, 27 },
	{ 109938, 1, 23, 23, 67, 84, 1, kSequencePointKind_Normal, 0, 28 },
	{ 109938, 1, 23, 23, 85, 86, 8, kSequencePointKind_Normal, 0, 29 },
	{ 109939, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 30 },
	{ 109939, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 31 },
	{ 109939, 1, 24, 24, 44, 45, 0, kSequencePointKind_Normal, 0, 32 },
	{ 109939, 1, 24, 24, 46, 66, 1, kSequencePointKind_Normal, 0, 33 },
	{ 109939, 1, 24, 24, 67, 68, 10, kSequencePointKind_Normal, 0, 34 },
	{ 109940, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 35 },
	{ 109940, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 36 },
	{ 109940, 1, 24, 24, 73, 74, 0, kSequencePointKind_Normal, 0, 37 },
	{ 109940, 1, 24, 24, 75, 96, 1, kSequencePointKind_Normal, 0, 38 },
	{ 109940, 1, 24, 24, 97, 98, 8, kSequencePointKind_Normal, 0, 39 },
	{ 109941, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 40 },
	{ 109941, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 41 },
	{ 109941, 1, 25, 25, 45, 46, 0, kSequencePointKind_Normal, 0, 42 },
	{ 109941, 1, 25, 25, 47, 68, 1, kSequencePointKind_Normal, 0, 43 },
	{ 109941, 1, 25, 25, 69, 70, 10, kSequencePointKind_Normal, 0, 44 },
	{ 109942, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 45 },
	{ 109942, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 46 },
	{ 109942, 1, 25, 25, 75, 76, 0, kSequencePointKind_Normal, 0, 47 },
	{ 109942, 1, 25, 25, 77, 99, 1, kSequencePointKind_Normal, 0, 48 },
	{ 109942, 1, 25, 25, 100, 101, 8, kSequencePointKind_Normal, 0, 49 },
	{ 109943, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 50 },
	{ 109943, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 51 },
	{ 109943, 1, 26, 26, 39, 40, 0, kSequencePointKind_Normal, 0, 52 },
	{ 109943, 1, 26, 26, 41, 56, 1, kSequencePointKind_Normal, 0, 53 },
	{ 109943, 1, 26, 26, 57, 58, 10, kSequencePointKind_Normal, 0, 54 },
	{ 109944, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 55 },
	{ 109944, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 56 },
	{ 109944, 1, 26, 26, 63, 64, 0, kSequencePointKind_Normal, 0, 57 },
	{ 109944, 1, 26, 26, 65, 81, 1, kSequencePointKind_Normal, 0, 58 },
	{ 109944, 1, 26, 26, 82, 83, 8, kSequencePointKind_Normal, 0, 59 },
	{ 109945, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 60 },
	{ 109945, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 61 },
	{ 109945, 1, 27, 27, 45, 46, 0, kSequencePointKind_Normal, 0, 62 },
	{ 109945, 1, 27, 27, 47, 68, 1, kSequencePointKind_Normal, 0, 63 },
	{ 109945, 1, 27, 27, 69, 70, 10, kSequencePointKind_Normal, 0, 64 },
	{ 109946, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 65 },
	{ 109946, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 66 },
	{ 109946, 1, 27, 27, 75, 76, 0, kSequencePointKind_Normal, 0, 67 },
	{ 109946, 1, 27, 27, 77, 99, 1, kSequencePointKind_Normal, 0, 68 },
	{ 109946, 1, 27, 27, 100, 101, 8, kSequencePointKind_Normal, 0, 69 },
	{ 109947, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 70 },
	{ 109947, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 71 },
	{ 109947, 1, 28, 28, 46, 47, 0, kSequencePointKind_Normal, 0, 72 },
	{ 109947, 1, 28, 28, 48, 70, 1, kSequencePointKind_Normal, 0, 73 },
	{ 109947, 1, 28, 28, 71, 72, 10, kSequencePointKind_Normal, 0, 74 },
	{ 109948, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 75 },
	{ 109948, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 76 },
	{ 109948, 1, 28, 28, 77, 78, 0, kSequencePointKind_Normal, 0, 77 },
	{ 109948, 1, 28, 28, 79, 102, 1, kSequencePointKind_Normal, 0, 78 },
	{ 109948, 1, 28, 28, 103, 104, 8, kSequencePointKind_Normal, 0, 79 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_VehiclesModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VehiclesModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Vehicles/Vehicles.bindings.cs", { 186, 163, 206, 140, 165, 172, 104, 223, 104, 193, 123, 123, 182, 7, 221, 95} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = 
{
	{ 14132, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[8] = 
{
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[61] = 
{
	{ 12, 0, 1 },
	{ 0, 0, 0 },
	{ 12, 1, 1 },
	{ 0, 0, 0 },
	{ 12, 2, 1 },
	{ 0, 0, 0 },
	{ 12, 3, 1 },
	{ 0, 0, 0 },
	{ 12, 4, 1 },
	{ 0, 0, 0 },
	{ 12, 5, 1 },
	{ 0, 0, 0 },
	{ 12, 6, 1 },
	{ 0, 0, 0 },
	{ 12, 7, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VehiclesModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VehiclesModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	80,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_VehiclesModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	1,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
