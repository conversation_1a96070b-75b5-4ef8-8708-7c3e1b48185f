﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Il2CppEagerStaticClassConstructionAttribute__ctor_mCACE94326399F3059F318EB568BD8E45037E3139 (void);
extern void math_hash_m102FB27D63A32D75D8CB1430F32BAFDEE29BCED8 (void);
extern void math_hash_m8ECC7CFBA8D302A2A3E9468DE65D705E9C1298EB (void);
extern void math_hash_m6B6E0FC08FCC3BC0940397676690F22B03DB4F16 (void);
extern void math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288 (void);
extern void math_asuint_m64DA623C5CFEB8445663480384F2B1C202150EE5 (void);
extern void math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139 (void);
extern void math_asuint_m22CC00686F9722FF2ED6330E3C0B4699C55CB1EE (void);
extern void math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E (void);
extern void math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F (void);
extern void math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9 (void);
extern void math_min_m02D43DF516544C279AF660EA4731449C82991849 (void);
extern void math_min_mA22BCFB62A81B533821704D26BE23D8D6402C8EB (void);
extern void math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B (void);
extern void math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE (void);
extern void math_max_mEBAE1BF7FA6B43BD0F4AE2E47FB6190041F8CE43 (void);
extern void math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496 (void);
extern void math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84 (void);
extern void math_lerp_mD91B5AE263EAACF3CE7DF14579522796CD90859E (void);
extern void math_lerp_mA20BFB8D988B57C1CFA28047538F3B47208D1371 (void);
extern void math_clamp_mB7233FC9D6C27522014C4E6D4E056D36CE82C97E (void);
extern void math_abs_mFF027629978A9039B059528ED3075D775AA0B0AB (void);
extern void math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1 (void);
extern void math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D (void);
extern void math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD (void);
extern void math_cos_m28B6228E047D552B1312CCFADB8AE95DDD94A6AF (void);
extern void math_sin_m231F847C28B88B17BDAD7F49A7A38E46DF12D3FF (void);
extern void math_floor_m0FDF19C33B0B1062079FCB10FB081869AEC1FB48 (void);
extern void math_floor_m9DB1E2394251575274F09FD450A9944D3F2CA65A (void);
extern void math_ceil_m01FC8783CB8656774F0A793EA3BBF831F7CE19C0 (void);
extern void math_ceil_m4225E9F121CBDB0D696914C34A9BB9B907729A94 (void);
extern void math_pow_m2B2C611A37952CFB13BB0AE800A6A601A2E4A49B (void);
extern void math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A (void);
extern void math_length_m3DB47D254C8544FBB740A892B4AE2143E8F45634 (void);
extern void math_length_m6A2B63D7A3B84261C2F7FCAA2CB382288A57D257 (void);
extern void math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9 (void);
extern void math_distance_m516495927BCF37E20FFA3E99C821062D329DFFF8 (void);
extern void math_lzcnt_mA6B7E71DB1B5D4CE8B67C66FF1AC4339FA368D07 (void);
extern void math_lzcnt_m121BDDDEE89F5A401E2E5F0AD900D22E47C8741C (void);
extern void math_tzcnt_m85FEAD596A8E327F7B6820310B7FBD9822BA735C (void);
extern void math_tzcnt_m07FD7550AAB5D94312E99571B112D652E8230360 (void);
extern void math_ceilpow2_mA00505409975D36AB3D7658687AC3BD5A26F3769 (void);
extern void math_csum_m0B6655875FE24428E18B97FC3F5C745E374530A3 (void);
extern void math_csum_m9C15CCCED13E8ADB45EFC388D141E55091A61C1C (void);
extern void math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F (void);
extern void math_uint2_m861F5F74EBBBD3DA19E84A1155320B89FF7341C3 (void);
extern void math_hash_m5D21276708BFB4DEEF3498774D296FE6B14FC5B0 (void);
extern void math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0 (void);
extern void math_hash_m31E070E721A961188B5629FCAC3C9559145F1A76 (void);
extern void math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336 (void);
extern void math_hash_m1A4778A79FFB5E05B04BD09B0F85EA9483D8A3CA (void);
extern void float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97 (void);
extern void float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979 (void);
extern void float2_op_Implicit_mDCE8EF24B96CB48EEFA3D9C75760ECA01874C735 (void);
extern void float2_op_Multiply_mD97F1F12A640BE857FD483CD188E7EDF44FB19A9 (void);
extern void float2_op_Multiply_m34D03129CE0D7AD665A914DE83CB749585B2455F (void);
extern void float2_op_Multiply_m9117237F9A26A1B934C1DE0A5FE5BD6EF7D3B26C (void);
extern void float2_op_Addition_m718974663A956F64D7C45D06C088550637F13693 (void);
extern void float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380 (void);
extern void float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA (void);
extern void float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788 (void);
extern void float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7 (void);
extern void float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0 (void);
extern void float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1 (void);
extern void float2_op_Implicit_m274CE0EFDC0FFD5168817614BF1A3699784DE02B (void);
extern void DebuggerProxy__ctor_mD03111C553C593ABABDF70B75AEA55958E2E3339 (void);
extern void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9 (void);
extern void float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350 (void);
extern void float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B (void);
extern void float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2 (void);
extern void float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A (void);
extern void float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A (void);
extern void float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C (void);
extern void float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017 (void);
extern void float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA (void);
extern void float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081 (void);
extern void float3_op_Implicit_mE1831A3AC179B7EB3236F8202EC8DD5CE05376AB (void);
extern void DebuggerProxy__ctor_mF5A0AF04FF4FAC0E6998E136BB69C081676F98ED (void);
extern void int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4 (void);
extern void int2__ctor_m79C9EA4A9751C36153FB626BBE0F62C923071806 (void);
extern void int2_op_Explicit_m6AA03EC57140BAF82A6ABEA85F72016BD12C8FF7 (void);
extern void int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6 (void);
extern void int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481 (void);
extern void int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6 (void);
extern void int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466 (void);
extern void int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47 (void);
extern void DebuggerProxy__ctor_mA86C1989EA15D7EA837569F4607BDAD18C0AA46C (void);
extern void uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F (void);
extern void uint2_op_Multiply_m2A4BC394328643E664AD9C17DA4BF1B0AC58E857 (void);
extern void uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C (void);
extern void uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257 (void);
extern void uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4 (void);
extern void uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5 (void);
extern void uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD (void);
extern void DebuggerProxy__ctor_m61D654567BFBC798E2EC37A0E2857EF0D2AAD6E3 (void);
extern void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF (void);
extern void uint3_op_Multiply_m756859015AC9BE9CB34BACE67DF92F64EA76C9AD (void);
extern void uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81 (void);
extern void uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D (void);
extern void uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D (void);
extern void uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633 (void);
extern void uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE (void);
extern void DebuggerProxy__ctor_mC8FD8175B5319BCFCA99A6FF5D589C38016ECEF3 (void);
extern void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008 (void);
extern void uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED (void);
extern void uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB (void);
extern void uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386 (void);
extern void uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A (void);
extern void uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B (void);
extern void uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3 (void);
extern void DebuggerProxy__ctor_m1F653087085C8A6C95D4284E9C55C18A67B0F532 (void);
static Il2CppMethodPointer s_methodPointers[111] = 
{
	Il2CppEagerStaticClassConstructionAttribute__ctor_mCACE94326399F3059F318EB568BD8E45037E3139,
	math_hash_m102FB27D63A32D75D8CB1430F32BAFDEE29BCED8,
	math_hash_m8ECC7CFBA8D302A2A3E9468DE65D705E9C1298EB,
	math_hash_m6B6E0FC08FCC3BC0940397676690F22B03DB4F16,
	math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288,
	math_asuint_m64DA623C5CFEB8445663480384F2B1C202150EE5,
	math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139,
	math_asuint_m22CC00686F9722FF2ED6330E3C0B4699C55CB1EE,
	math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E,
	math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F,
	math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9,
	math_min_m02D43DF516544C279AF660EA4731449C82991849,
	math_min_mA22BCFB62A81B533821704D26BE23D8D6402C8EB,
	math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B,
	math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE,
	math_max_mEBAE1BF7FA6B43BD0F4AE2E47FB6190041F8CE43,
	math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496,
	math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84,
	math_lerp_mD91B5AE263EAACF3CE7DF14579522796CD90859E,
	math_lerp_mA20BFB8D988B57C1CFA28047538F3B47208D1371,
	math_clamp_mB7233FC9D6C27522014C4E6D4E056D36CE82C97E,
	math_abs_mFF027629978A9039B059528ED3075D775AA0B0AB,
	math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1,
	math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D,
	math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD,
	math_cos_m28B6228E047D552B1312CCFADB8AE95DDD94A6AF,
	math_sin_m231F847C28B88B17BDAD7F49A7A38E46DF12D3FF,
	math_floor_m0FDF19C33B0B1062079FCB10FB081869AEC1FB48,
	math_floor_m9DB1E2394251575274F09FD450A9944D3F2CA65A,
	math_ceil_m01FC8783CB8656774F0A793EA3BBF831F7CE19C0,
	math_ceil_m4225E9F121CBDB0D696914C34A9BB9B907729A94,
	math_pow_m2B2C611A37952CFB13BB0AE800A6A601A2E4A49B,
	math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A,
	math_length_m3DB47D254C8544FBB740A892B4AE2143E8F45634,
	math_length_m6A2B63D7A3B84261C2F7FCAA2CB382288A57D257,
	math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9,
	math_distance_m516495927BCF37E20FFA3E99C821062D329DFFF8,
	math_lzcnt_mA6B7E71DB1B5D4CE8B67C66FF1AC4339FA368D07,
	math_lzcnt_m121BDDDEE89F5A401E2E5F0AD900D22E47C8741C,
	math_tzcnt_m85FEAD596A8E327F7B6820310B7FBD9822BA735C,
	math_tzcnt_m07FD7550AAB5D94312E99571B112D652E8230360,
	math_ceilpow2_mA00505409975D36AB3D7658687AC3BD5A26F3769,
	math_csum_m0B6655875FE24428E18B97FC3F5C745E374530A3,
	math_csum_m9C15CCCED13E8ADB45EFC388D141E55091A61C1C,
	math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F,
	math_uint2_m861F5F74EBBBD3DA19E84A1155320B89FF7341C3,
	math_hash_m5D21276708BFB4DEEF3498774D296FE6B14FC5B0,
	math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0,
	math_hash_m31E070E721A961188B5629FCAC3C9559145F1A76,
	math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336,
	math_hash_m1A4778A79FFB5E05B04BD09B0F85EA9483D8A3CA,
	float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97,
	float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979,
	float2_op_Implicit_mDCE8EF24B96CB48EEFA3D9C75760ECA01874C735,
	float2_op_Multiply_mD97F1F12A640BE857FD483CD188E7EDF44FB19A9,
	float2_op_Multiply_m34D03129CE0D7AD665A914DE83CB749585B2455F,
	float2_op_Multiply_m9117237F9A26A1B934C1DE0A5FE5BD6EF7D3B26C,
	float2_op_Addition_m718974663A956F64D7C45D06C088550637F13693,
	float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380,
	float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA,
	float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788,
	float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7,
	float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0,
	float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1,
	float2_op_Implicit_m274CE0EFDC0FFD5168817614BF1A3699784DE02B,
	DebuggerProxy__ctor_mD03111C553C593ABABDF70B75AEA55958E2E3339,
	float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9,
	float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350,
	float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B,
	float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2,
	float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A,
	float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A,
	float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C,
	float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017,
	float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA,
	float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081,
	float3_op_Implicit_mE1831A3AC179B7EB3236F8202EC8DD5CE05376AB,
	DebuggerProxy__ctor_mF5A0AF04FF4FAC0E6998E136BB69C081676F98ED,
	int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4,
	int2__ctor_m79C9EA4A9751C36153FB626BBE0F62C923071806,
	int2_op_Explicit_m6AA03EC57140BAF82A6ABEA85F72016BD12C8FF7,
	int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6,
	int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481,
	int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6,
	int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466,
	int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47,
	DebuggerProxy__ctor_mA86C1989EA15D7EA837569F4607BDAD18C0AA46C,
	uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F,
	uint2_op_Multiply_m2A4BC394328643E664AD9C17DA4BF1B0AC58E857,
	uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C,
	uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257,
	uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4,
	uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5,
	uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD,
	DebuggerProxy__ctor_m61D654567BFBC798E2EC37A0E2857EF0D2AAD6E3,
	uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF,
	uint3_op_Multiply_m756859015AC9BE9CB34BACE67DF92F64EA76C9AD,
	uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81,
	uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D,
	uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D,
	uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633,
	uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE,
	DebuggerProxy__ctor_mC8FD8175B5319BCFCA99A6FF5D589C38016ECEF3,
	uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008,
	uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED,
	uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB,
	uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386,
	uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A,
	uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B,
	uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3,
	DebuggerProxy__ctor_m1F653087085C8A6C95D4284E9C55C18A67B0F532,
};
extern void float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_AdjustorThunk (void);
extern void float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979_AdjustorThunk (void);
extern void float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA_AdjustorThunk (void);
extern void float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788_AdjustorThunk (void);
extern void float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7_AdjustorThunk (void);
extern void float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0_AdjustorThunk (void);
extern void float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1_AdjustorThunk (void);
extern void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_AdjustorThunk (void);
extern void float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A_AdjustorThunk (void);
extern void float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_AdjustorThunk (void);
extern void float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C_AdjustorThunk (void);
extern void float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017_AdjustorThunk (void);
extern void float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA_AdjustorThunk (void);
extern void float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081_AdjustorThunk (void);
extern void int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_AdjustorThunk (void);
extern void int2__ctor_m79C9EA4A9751C36153FB626BBE0F62C923071806_AdjustorThunk (void);
extern void int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6_AdjustorThunk (void);
extern void int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481_AdjustorThunk (void);
extern void int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6_AdjustorThunk (void);
extern void int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466_AdjustorThunk (void);
extern void int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47_AdjustorThunk (void);
extern void uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F_AdjustorThunk (void);
extern void uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C_AdjustorThunk (void);
extern void uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257_AdjustorThunk (void);
extern void uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4_AdjustorThunk (void);
extern void uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5_AdjustorThunk (void);
extern void uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD_AdjustorThunk (void);
extern void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_AdjustorThunk (void);
extern void uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81_AdjustorThunk (void);
extern void uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D_AdjustorThunk (void);
extern void uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D_AdjustorThunk (void);
extern void uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633_AdjustorThunk (void);
extern void uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE_AdjustorThunk (void);
extern void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_AdjustorThunk (void);
extern void uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_AdjustorThunk (void);
extern void uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386_AdjustorThunk (void);
extern void uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A_AdjustorThunk (void);
extern void uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B_AdjustorThunk (void);
extern void uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[39] = 
{
	{ 0x06000034, float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_AdjustorThunk },
	{ 0x06000035, float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979_AdjustorThunk },
	{ 0x0600003C, float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA_AdjustorThunk },
	{ 0x0600003D, float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788_AdjustorThunk },
	{ 0x0600003E, float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7_AdjustorThunk },
	{ 0x0600003F, float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0_AdjustorThunk },
	{ 0x06000040, float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1_AdjustorThunk },
	{ 0x06000043, float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_AdjustorThunk },
	{ 0x06000047, float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A_AdjustorThunk },
	{ 0x06000048, float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_AdjustorThunk },
	{ 0x06000049, float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C_AdjustorThunk },
	{ 0x0600004A, float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017_AdjustorThunk },
	{ 0x0600004B, float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA_AdjustorThunk },
	{ 0x0600004C, float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081_AdjustorThunk },
	{ 0x0600004F, int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_AdjustorThunk },
	{ 0x06000050, int2__ctor_m79C9EA4A9751C36153FB626BBE0F62C923071806_AdjustorThunk },
	{ 0x06000052, int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6_AdjustorThunk },
	{ 0x06000053, int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481_AdjustorThunk },
	{ 0x06000054, int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6_AdjustorThunk },
	{ 0x06000055, int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466_AdjustorThunk },
	{ 0x06000056, int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47_AdjustorThunk },
	{ 0x06000058, uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F_AdjustorThunk },
	{ 0x0600005A, uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C_AdjustorThunk },
	{ 0x0600005B, uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257_AdjustorThunk },
	{ 0x0600005C, uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4_AdjustorThunk },
	{ 0x0600005D, uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5_AdjustorThunk },
	{ 0x0600005E, uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD_AdjustorThunk },
	{ 0x06000060, uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_AdjustorThunk },
	{ 0x06000062, uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81_AdjustorThunk },
	{ 0x06000063, uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D_AdjustorThunk },
	{ 0x06000064, uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D_AdjustorThunk },
	{ 0x06000065, uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633_AdjustorThunk },
	{ 0x06000066, uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE_AdjustorThunk },
	{ 0x06000068, uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_AdjustorThunk },
	{ 0x0600006A, uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_AdjustorThunk },
	{ 0x0600006B, uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386_AdjustorThunk },
	{ 0x0600006C, uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A_AdjustorThunk },
	{ 0x0600006D, uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B_AdjustorThunk },
	{ 0x0600006E, uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3_AdjustorThunk },
};
static const int32_t s_InvokerIndices[111] = 
{
	4364,
	8793,
	8794,
	8795,
	8405,
	8936,
	8789,
	8935,
	8937,
	8622,
	8631,
	7473,
	7520,
	7742,
	7473,
	7520,
	7742,
	6788,
	7058,
	7059,
	6788,
	8395,
	8628,
	7751,
	7752,
	8628,
	8628,
	8628,
	8930,
	8628,
	8930,
	7742,
	8628,
	8637,
	8638,
	7751,
	7752,
	8395,
	8408,
	8395,
	8408,
	8395,
	8796,
	8797,
	8798,
	8066,
	8796,
	7061,
	8797,
	6233,
	8798,
	2850,
	3991,
	8931,
	8061,
	8060,
	8059,
	8061,
	8061,
	3291,
	3185,
	4216,
	4250,
	2524,
	8929,
	3988,
	2150,
	8062,
	8063,
	8063,
	4368,
	3292,
	3185,
	4216,
	4250,
	2524,
	8932,
	3989,
	2734,
	3988,
	8934,
	3293,
	3185,
	4216,
	4250,
	2524,
	3991,
	2875,
	8067,
	3294,
	3185,
	4216,
	4250,
	2524,
	3992,
	2162,
	8068,
	3295,
	3185,
	4216,
	4250,
	2524,
	3993,
	1532,
	8070,
	3296,
	3185,
	4216,
	4250,
	2524,
	3994,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnity_Mathematics;
static TypeDefinitionIndex s_staticConstructorsToRunAtStartup[8] = 
{
	14100,
	14102,
	14104,
	14106,
	14108,
	14110,
	14112,
	0,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule = 
{
	"Unity.Mathematics.dll",
	111,
	s_methodPointers,
	39,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnity_Mathematics,
	NULL,
	s_staticConstructorsToRunAtStartup,
	NULL,
	NULL,
};
