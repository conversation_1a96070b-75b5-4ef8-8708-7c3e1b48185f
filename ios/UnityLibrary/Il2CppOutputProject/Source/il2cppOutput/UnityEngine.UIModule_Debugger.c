﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[37] = 
{
	{ 19415, 0,  2 },
	{ 25624, 1,  4 },
	{ 11287, 2,  4 },
	{ 10797, 3,  4 },
	{ 11289, 4,  4 },
	{ 11289, 5,  4 },
	{ 11289, 6,  4 },
	{ 11289, 7,  4 },
	{ 11287, 8,  4 },
	{ 11289, 9,  4 },
	{ 10964, 10,  4 },
	{ 24489, 11,  5 },
	{ 24489, 12,  6 },
	{ 27693, 13,  10 },
	{ 27089, 14,  10 },
	{ 28902, 15,  10 },
	{ 28902, 16,  10 },
	{ 31482, 17,  11 },
	{ 31482, 18,  12 },
	{ 17143, 19,  14 },
	{ 31482, 20,  15 },
	{ 31482, 21,  15 },
	{ 25515, 22,  15 },
	{ 18858, 23,  15 },
	{ 24489, 11,  16 },
	{ 24489, 24,  16 },
	{ 24489, 25,  17 },
	{ 31482, 26,  18 },
	{ 31459, 27,  20 },
	{ 31459, 28,  20 },
	{ 31459, 29,  20 },
	{ 31459, 30,  20 },
	{ 28902, 31,  20 },
	{ 24489, 11,  21 },
	{ 27808, 32,  22 },
	{ 24489, 11,  24 },
	{ 27808, 32,  25 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[33] = 
{
	"color",
	"mesh",
	"positions",
	"colors",
	"uv0S",
	"uv1S",
	"uv2S",
	"uv3S",
	"normals",
	"tangents",
	"indices",
	"i",
	"k",
	"ray",
	"plane",
	"dist",
	"dot",
	"worldPoint",
	"pos",
	"rects",
	"vMin",
	"vMax",
	"toLocal",
	"b",
	"imax",
	"j",
	"v",
	"pivot",
	"anchoredPosition",
	"anchorMin",
	"anchorMax",
	"temp",
	"childRect",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[155] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 1, 12 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 13, 4 },
	{ 17, 1 },
	{ 18, 1 },
	{ 0, 0 },
	{ 19, 9 },
	{ 0, 0 },
	{ 28, 7 },
	{ 35, 2 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_UIModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UIModule[515] = 
{
	{ 108693, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 108693, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 108693, 1, 23, 23, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 108693, 1, 24, 24, 13, 35, 1, kSequencePointKind_Normal, 0, 3 },
	{ 108693, 1, 24, 24, 13, 35, 2, kSequencePointKind_StepOut, 0, 4 },
	{ 108693, 1, 25, 25, 9, 10, 10, kSequencePointKind_Normal, 0, 5 },
	{ 108709, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 108709, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 108709, 2, 23, 23, 30, 34, 0, kSequencePointKind_Normal, 0, 8 },
	{ 108710, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 9 },
	{ 108710, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 10 },
	{ 108710, 2, 23, 23, 35, 39, 0, kSequencePointKind_Normal, 0, 11 },
	{ 108726, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 108726, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 108726, 2, 41, 41, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 108726, 2, 42, 42, 13, 33, 1, kSequencePointKind_Normal, 0, 15 },
	{ 108726, 2, 42, 42, 13, 33, 2, kSequencePointKind_StepOut, 0, 16 },
	{ 108726, 2, 43, 43, 9, 10, 15, kSequencePointKind_Normal, 0, 17 },
	{ 108727, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 18 },
	{ 108727, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 19 },
	{ 108727, 2, 46, 46, 9, 10, 0, kSequencePointKind_Normal, 0, 20 },
	{ 108727, 2, 47, 47, 13, 36, 1, kSequencePointKind_Normal, 0, 21 },
	{ 108727, 2, 47, 47, 13, 36, 2, kSequencePointKind_StepOut, 0, 22 },
	{ 108727, 2, 48, 48, 13, 29, 8, kSequencePointKind_Normal, 0, 23 },
	{ 108727, 2, 49, 49, 13, 29, 16, kSequencePointKind_Normal, 0, 24 },
	{ 108727, 2, 49, 49, 13, 29, 18, kSequencePointKind_StepOut, 0, 25 },
	{ 108727, 2, 50, 50, 9, 10, 24, kSequencePointKind_Normal, 0, 26 },
	{ 108729, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 27 },
	{ 108729, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 28 },
	{ 108729, 2, 55, 55, 9, 10, 0, kSequencePointKind_Normal, 0, 29 },
	{ 108729, 2, 56, 56, 13, 56, 1, kSequencePointKind_Normal, 0, 30 },
	{ 108729, 2, 56, 56, 13, 56, 4, kSequencePointKind_StepOut, 0, 31 },
	{ 108729, 2, 56, 56, 13, 56, 9, kSequencePointKind_StepOut, 0, 32 },
	{ 108729, 2, 56, 56, 13, 56, 14, kSequencePointKind_StepOut, 0, 33 },
	{ 108729, 2, 57, 57, 13, 38, 20, kSequencePointKind_Normal, 0, 34 },
	{ 108729, 2, 57, 57, 13, 38, 23, kSequencePointKind_StepOut, 0, 35 },
	{ 108729, 2, 58, 58, 13, 33, 29, kSequencePointKind_Normal, 0, 36 },
	{ 108729, 2, 58, 58, 13, 33, 31, kSequencePointKind_StepOut, 0, 37 },
	{ 108729, 2, 59, 59, 9, 10, 37, kSequencePointKind_Normal, 0, 38 },
	{ 108730, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 39 },
	{ 108730, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 40 },
	{ 108730, 2, 62, 62, 9, 10, 0, kSequencePointKind_Normal, 0, 41 },
	{ 108730, 2, 63, 63, 13, 35, 1, kSequencePointKind_Normal, 0, 42 },
	{ 108730, 2, 63, 63, 13, 35, 3, kSequencePointKind_StepOut, 0, 43 },
	{ 108730, 2, 64, 64, 9, 10, 11, kSequencePointKind_Normal, 0, 44 },
	{ 108731, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 45 },
	{ 108731, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 46 },
	{ 108731, 2, 68, 68, 9, 10, 0, kSequencePointKind_Normal, 0, 47 },
	{ 108731, 2, 69, 69, 13, 142, 1, kSequencePointKind_Normal, 0, 48 },
	{ 108731, 2, 69, 69, 13, 142, 7, kSequencePointKind_StepOut, 0, 49 },
	{ 108731, 2, 69, 69, 13, 142, 12, kSequencePointKind_StepOut, 0, 50 },
	{ 108731, 2, 69, 69, 13, 142, 23, kSequencePointKind_StepOut, 0, 51 },
	{ 108731, 2, 70, 70, 9, 10, 29, kSequencePointKind_Normal, 0, 52 },
	{ 108732, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 53 },
	{ 108732, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 54 },
	{ 108732, 2, 74, 74, 9, 10, 0, kSequencePointKind_Normal, 0, 55 },
	{ 108732, 2, 75, 75, 13, 111, 1, kSequencePointKind_Normal, 0, 56 },
	{ 108732, 2, 75, 75, 13, 111, 15, kSequencePointKind_StepOut, 0, 57 },
	{ 108732, 2, 76, 76, 13, 57, 21, kSequencePointKind_Normal, 0, 58 },
	{ 108732, 2, 76, 76, 13, 57, 24, kSequencePointKind_StepOut, 0, 59 },
	{ 108732, 2, 77, 77, 9, 10, 30, kSequencePointKind_Normal, 0, 60 },
	{ 108733, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 61 },
	{ 108733, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 62 },
	{ 108733, 2, 80, 80, 9, 10, 0, kSequencePointKind_Normal, 0, 63 },
	{ 108733, 2, 81, 81, 13, 142, 1, kSequencePointKind_Normal, 0, 64 },
	{ 108733, 2, 81, 81, 13, 142, 7, kSequencePointKind_StepOut, 0, 65 },
	{ 108733, 2, 81, 81, 13, 142, 12, kSequencePointKind_StepOut, 0, 66 },
	{ 108733, 2, 81, 81, 13, 142, 23, kSequencePointKind_StepOut, 0, 67 },
	{ 108733, 2, 82, 82, 9, 10, 29, kSequencePointKind_Normal, 0, 68 },
	{ 108734, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 69 },
	{ 108734, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 70 },
	{ 108734, 2, 85, 85, 9, 10, 0, kSequencePointKind_Normal, 0, 71 },
	{ 108734, 2, 86, 86, 13, 120, 1, kSequencePointKind_Normal, 0, 72 },
	{ 108734, 2, 86, 86, 13, 120, 17, kSequencePointKind_StepOut, 0, 73 },
	{ 108734, 2, 87, 87, 9, 10, 23, kSequencePointKind_Normal, 0, 74 },
	{ 108735, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 75 },
	{ 108735, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 76 },
	{ 108735, 2, 90, 90, 9, 10, 0, kSequencePointKind_Normal, 0, 77 },
	{ 108735, 2, 91, 91, 13, 130, 1, kSequencePointKind_Normal, 0, 78 },
	{ 108735, 2, 91, 91, 13, 130, 7, kSequencePointKind_StepOut, 0, 79 },
	{ 108735, 2, 91, 91, 13, 130, 12, kSequencePointKind_StepOut, 0, 80 },
	{ 108735, 2, 91, 91, 13, 130, 21, kSequencePointKind_StepOut, 0, 81 },
	{ 108735, 2, 92, 92, 9, 10, 27, kSequencePointKind_Normal, 0, 82 },
	{ 108736, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 83 },
	{ 108736, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 84 },
	{ 108736, 2, 95, 95, 9, 10, 0, kSequencePointKind_Normal, 0, 85 },
	{ 108736, 2, 96, 96, 13, 111, 1, kSequencePointKind_Normal, 0, 86 },
	{ 108736, 2, 96, 96, 13, 111, 15, kSequencePointKind_StepOut, 0, 87 },
	{ 108736, 2, 97, 97, 9, 10, 21, kSequencePointKind_Normal, 0, 88 },
	{ 108737, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 89 },
	{ 108737, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 90 },
	{ 108737, 2, 101, 101, 9, 10, 0, kSequencePointKind_Normal, 0, 91 },
	{ 108737, 2, 102, 102, 13, 61, 1, kSequencePointKind_Normal, 0, 92 },
	{ 108737, 2, 102, 102, 13, 61, 3, kSequencePointKind_StepOut, 0, 93 },
	{ 108737, 2, 102, 102, 13, 61, 9, kSequencePointKind_StepOut, 0, 94 },
	{ 108737, 2, 102, 102, 13, 61, 14, kSequencePointKind_StepOut, 0, 95 },
	{ 108737, 2, 103, 103, 9, 10, 20, kSequencePointKind_Normal, 0, 96 },
	{ 108738, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 97 },
	{ 108738, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 98 },
	{ 108738, 2, 107, 107, 9, 10, 0, kSequencePointKind_Normal, 0, 99 },
	{ 108738, 2, 108, 108, 13, 35, 1, kSequencePointKind_Normal, 0, 100 },
	{ 108738, 2, 108, 108, 13, 35, 1, kSequencePointKind_StepOut, 0, 101 },
	{ 108738, 2, 110, 110, 13, 49, 7, kSequencePointKind_Normal, 0, 102 },
	{ 108738, 2, 110, 110, 13, 49, 7, kSequencePointKind_StepOut, 0, 103 },
	{ 108738, 2, 111, 111, 13, 46, 13, kSequencePointKind_Normal, 0, 104 },
	{ 108738, 2, 111, 111, 13, 46, 13, kSequencePointKind_StepOut, 0, 105 },
	{ 108738, 2, 112, 112, 13, 44, 19, kSequencePointKind_Normal, 0, 106 },
	{ 108738, 2, 112, 112, 13, 44, 19, kSequencePointKind_StepOut, 0, 107 },
	{ 108738, 2, 113, 113, 13, 44, 25, kSequencePointKind_Normal, 0, 108 },
	{ 108738, 2, 113, 113, 13, 44, 25, kSequencePointKind_StepOut, 0, 109 },
	{ 108738, 2, 114, 114, 13, 44, 32, kSequencePointKind_Normal, 0, 110 },
	{ 108738, 2, 114, 114, 13, 44, 32, kSequencePointKind_StepOut, 0, 111 },
	{ 108738, 2, 115, 115, 13, 44, 39, kSequencePointKind_Normal, 0, 112 },
	{ 108738, 2, 115, 115, 13, 44, 39, kSequencePointKind_StepOut, 0, 113 },
	{ 108738, 2, 116, 116, 13, 47, 46, kSequencePointKind_Normal, 0, 114 },
	{ 108738, 2, 116, 116, 13, 47, 46, kSequencePointKind_StepOut, 0, 115 },
	{ 108738, 2, 117, 117, 13, 48, 53, kSequencePointKind_Normal, 0, 116 },
	{ 108738, 2, 117, 117, 13, 48, 53, kSequencePointKind_StepOut, 0, 117 },
	{ 108738, 2, 118, 118, 13, 43, 60, kSequencePointKind_Normal, 0, 118 },
	{ 108738, 2, 118, 118, 13, 43, 60, kSequencePointKind_StepOut, 0, 119 },
	{ 108738, 2, 120, 120, 18, 27, 67, kSequencePointKind_Normal, 0, 120 },
	{ 108738, 2, 120, 120, 0, 0, 70, kSequencePointKind_Normal, 0, 121 },
	{ 108738, 2, 121, 121, 13, 14, 75, kSequencePointKind_Normal, 0, 122 },
	{ 108738, 2, 122, 122, 22, 31, 76, kSequencePointKind_Normal, 0, 123 },
	{ 108738, 2, 122, 122, 0, 0, 79, kSequencePointKind_Normal, 0, 124 },
	{ 108738, 2, 123, 123, 17, 18, 84, kSequencePointKind_Normal, 0, 125 },
	{ 108738, 2, 124, 124, 21, 61, 85, kSequencePointKind_Normal, 0, 126 },
	{ 108738, 2, 124, 124, 21, 61, 102, kSequencePointKind_StepOut, 0, 127 },
	{ 108738, 2, 125, 125, 21, 55, 108, kSequencePointKind_Normal, 0, 128 },
	{ 108738, 2, 125, 125, 21, 55, 125, kSequencePointKind_StepOut, 0, 129 },
	{ 108738, 2, 126, 126, 21, 51, 131, kSequencePointKind_Normal, 0, 130 },
	{ 108738, 2, 126, 126, 21, 51, 148, kSequencePointKind_StepOut, 0, 131 },
	{ 108738, 2, 127, 127, 21, 51, 154, kSequencePointKind_Normal, 0, 132 },
	{ 108738, 2, 127, 127, 21, 51, 172, kSequencePointKind_StepOut, 0, 133 },
	{ 108738, 2, 128, 128, 21, 51, 178, kSequencePointKind_Normal, 0, 134 },
	{ 108738, 2, 128, 128, 21, 51, 196, kSequencePointKind_StepOut, 0, 135 },
	{ 108738, 2, 129, 129, 21, 51, 202, kSequencePointKind_Normal, 0, 136 },
	{ 108738, 2, 129, 129, 21, 51, 220, kSequencePointKind_StepOut, 0, 137 },
	{ 108738, 2, 130, 130, 21, 57, 226, kSequencePointKind_Normal, 0, 138 },
	{ 108738, 2, 130, 130, 21, 57, 244, kSequencePointKind_StepOut, 0, 139 },
	{ 108738, 2, 131, 131, 21, 59, 250, kSequencePointKind_Normal, 0, 140 },
	{ 108738, 2, 131, 131, 21, 59, 268, kSequencePointKind_StepOut, 0, 141 },
	{ 108738, 2, 132, 132, 17, 18, 274, kSequencePointKind_Normal, 0, 142 },
	{ 108738, 2, 122, 122, 40, 43, 275, kSequencePointKind_Normal, 0, 143 },
	{ 108738, 2, 122, 122, 33, 38, 281, kSequencePointKind_Normal, 0, 144 },
	{ 108738, 2, 122, 122, 0, 0, 288, kSequencePointKind_Normal, 0, 145 },
	{ 108738, 2, 134, 134, 17, 32, 295, kSequencePointKind_Normal, 0, 146 },
	{ 108738, 2, 134, 134, 17, 32, 299, kSequencePointKind_StepOut, 0, 147 },
	{ 108738, 2, 135, 135, 17, 36, 305, kSequencePointKind_Normal, 0, 148 },
	{ 108738, 2, 135, 135, 17, 36, 311, kSequencePointKind_StepOut, 0, 149 },
	{ 108738, 2, 136, 136, 17, 36, 317, kSequencePointKind_Normal, 0, 150 },
	{ 108738, 2, 136, 136, 17, 36, 323, kSequencePointKind_StepOut, 0, 151 },
	{ 108738, 2, 138, 138, 17, 36, 329, kSequencePointKind_Normal, 0, 152 },
	{ 108738, 2, 138, 138, 17, 36, 335, kSequencePointKind_StepOut, 0, 153 },
	{ 108738, 2, 139, 139, 17, 36, 341, kSequencePointKind_Normal, 0, 154 },
	{ 108738, 2, 139, 139, 17, 36, 347, kSequencePointKind_StepOut, 0, 155 },
	{ 108738, 2, 140, 140, 17, 32, 353, kSequencePointKind_Normal, 0, 156 },
	{ 108738, 2, 140, 140, 17, 32, 357, kSequencePointKind_StepOut, 0, 157 },
	{ 108738, 2, 141, 141, 13, 14, 363, kSequencePointKind_Normal, 0, 158 },
	{ 108738, 2, 120, 120, 39, 45, 364, kSequencePointKind_Normal, 0, 159 },
	{ 108738, 2, 120, 120, 29, 37, 370, kSequencePointKind_Normal, 0, 160 },
	{ 108738, 2, 120, 120, 0, 0, 377, kSequencePointKind_Normal, 0, 161 },
	{ 108738, 2, 143, 143, 13, 41, 384, kSequencePointKind_Normal, 0, 162 },
	{ 108738, 2, 143, 143, 13, 41, 386, kSequencePointKind_StepOut, 0, 163 },
	{ 108738, 2, 144, 144, 13, 36, 392, kSequencePointKind_Normal, 0, 164 },
	{ 108738, 2, 144, 144, 13, 36, 394, kSequencePointKind_StepOut, 0, 165 },
	{ 108738, 2, 145, 145, 13, 38, 400, kSequencePointKind_Normal, 0, 166 },
	{ 108738, 2, 145, 145, 13, 38, 403, kSequencePointKind_StepOut, 0, 167 },
	{ 108738, 2, 146, 146, 13, 40, 409, kSequencePointKind_Normal, 0, 168 },
	{ 108738, 2, 146, 146, 13, 40, 412, kSequencePointKind_StepOut, 0, 169 },
	{ 108738, 2, 147, 147, 13, 34, 418, kSequencePointKind_Normal, 0, 170 },
	{ 108738, 2, 147, 147, 13, 34, 421, kSequencePointKind_StepOut, 0, 171 },
	{ 108738, 2, 148, 148, 13, 34, 427, kSequencePointKind_Normal, 0, 172 },
	{ 108738, 2, 148, 148, 13, 34, 431, kSequencePointKind_StepOut, 0, 173 },
	{ 108738, 2, 149, 149, 13, 34, 437, kSequencePointKind_Normal, 0, 174 },
	{ 108738, 2, 149, 149, 13, 34, 441, kSequencePointKind_StepOut, 0, 175 },
	{ 108738, 2, 150, 150, 13, 34, 447, kSequencePointKind_Normal, 0, 176 },
	{ 108738, 2, 150, 150, 13, 34, 451, kSequencePointKind_StepOut, 0, 177 },
	{ 108738, 2, 151, 151, 13, 75, 457, kSequencePointKind_Normal, 0, 178 },
	{ 108738, 2, 151, 151, 13, 75, 460, kSequencePointKind_StepOut, 0, 179 },
	{ 108738, 2, 151, 151, 13, 75, 467, kSequencePointKind_StepOut, 0, 180 },
	{ 108738, 2, 152, 152, 13, 27, 473, kSequencePointKind_Normal, 0, 181 },
	{ 108738, 2, 152, 152, 13, 27, 475, kSequencePointKind_StepOut, 0, 182 },
	{ 108738, 2, 153, 153, 13, 36, 481, kSequencePointKind_Normal, 0, 183 },
	{ 108738, 2, 153, 153, 13, 36, 482, kSequencePointKind_StepOut, 0, 184 },
	{ 108738, 2, 154, 154, 9, 10, 488, kSequencePointKind_Normal, 0, 185 },
	{ 108751, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 186 },
	{ 108751, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 187 },
	{ 108751, 3, 7, 7, 9, 39, 0, kSequencePointKind_Normal, 0, 188 },
	{ 108751, 3, 7, 7, 9, 39, 1, kSequencePointKind_StepOut, 0, 189 },
	{ 108751, 3, 7, 7, 40, 41, 7, kSequencePointKind_Normal, 0, 190 },
	{ 108751, 3, 7, 7, 41, 42, 8, kSequencePointKind_Normal, 0, 191 },
	{ 108752, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 192 },
	{ 108752, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 193 },
	{ 108752, 3, 10, 10, 9, 10, 0, kSequencePointKind_Normal, 0, 194 },
	{ 108752, 3, 11, 11, 13, 74, 1, kSequencePointKind_Normal, 0, 195 },
	{ 108752, 3, 11, 11, 13, 74, 4, kSequencePointKind_StepOut, 0, 196 },
	{ 108752, 3, 12, 12, 9, 10, 12, kSequencePointKind_Normal, 0, 197 },
	{ 108753, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 198 },
	{ 108753, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 199 },
	{ 108753, 3, 15, 15, 9, 10, 0, kSequencePointKind_Normal, 0, 200 },
	{ 108753, 3, 16, 16, 13, 87, 1, kSequencePointKind_Normal, 0, 201 },
	{ 108753, 3, 16, 16, 13, 87, 4, kSequencePointKind_StepOut, 0, 202 },
	{ 108753, 3, 16, 16, 13, 87, 9, kSequencePointKind_StepOut, 0, 203 },
	{ 108753, 3, 17, 17, 9, 10, 17, kSequencePointKind_Normal, 0, 204 },
	{ 108754, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 205 },
	{ 108754, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 206 },
	{ 108754, 3, 20, 20, 9, 10, 0, kSequencePointKind_Normal, 0, 207 },
	{ 108754, 3, 21, 21, 13, 69, 1, kSequencePointKind_Normal, 0, 208 },
	{ 108754, 3, 21, 21, 13, 69, 5, kSequencePointKind_StepOut, 0, 209 },
	{ 108754, 3, 22, 22, 9, 10, 13, kSequencePointKind_Normal, 0, 210 },
	{ 108755, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 211 },
	{ 108755, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 212 },
	{ 108755, 3, 25, 25, 9, 10, 0, kSequencePointKind_Normal, 0, 213 },
	{ 108755, 3, 26, 26, 13, 39, 1, kSequencePointKind_Normal, 0, 214 },
	{ 108755, 3, 26, 26, 13, 39, 2, kSequencePointKind_StepOut, 0, 215 },
	{ 108755, 3, 26, 26, 13, 39, 7, kSequencePointKind_StepOut, 0, 216 },
	{ 108755, 3, 27, 27, 13, 58, 17, kSequencePointKind_Normal, 0, 217 },
	{ 108755, 3, 27, 27, 13, 58, 19, kSequencePointKind_StepOut, 0, 218 },
	{ 108755, 3, 28, 28, 13, 80, 25, kSequencePointKind_Normal, 0, 219 },
	{ 108755, 3, 28, 28, 13, 80, 28, kSequencePointKind_StepOut, 0, 220 },
	{ 108755, 3, 28, 28, 13, 80, 33, kSequencePointKind_StepOut, 0, 221 },
	{ 108755, 3, 28, 28, 13, 80, 38, kSequencePointKind_StepOut, 0, 222 },
	{ 108755, 3, 28, 28, 13, 80, 44, kSequencePointKind_StepOut, 0, 223 },
	{ 108755, 3, 28, 28, 13, 80, 49, kSequencePointKind_StepOut, 0, 224 },
	{ 108755, 3, 30, 30, 13, 28, 54, kSequencePointKind_Normal, 0, 225 },
	{ 108755, 3, 32, 32, 13, 98, 60, kSequencePointKind_Normal, 0, 226 },
	{ 108755, 3, 32, 32, 13, 98, 61, kSequencePointKind_StepOut, 0, 227 },
	{ 108755, 3, 32, 32, 13, 98, 68, kSequencePointKind_StepOut, 0, 228 },
	{ 108755, 3, 32, 32, 13, 98, 73, kSequencePointKind_StepOut, 0, 229 },
	{ 108755, 3, 32, 32, 13, 98, 78, kSequencePointKind_StepOut, 0, 230 },
	{ 108755, 3, 32, 32, 13, 98, 85, kSequencePointKind_StepOut, 0, 231 },
	{ 108755, 3, 32, 32, 13, 98, 90, kSequencePointKind_StepOut, 0, 232 },
	{ 108755, 3, 34, 34, 13, 59, 96, kSequencePointKind_Normal, 0, 233 },
	{ 108755, 3, 34, 34, 13, 59, 109, kSequencePointKind_StepOut, 0, 234 },
	{ 108755, 3, 34, 34, 0, 0, 122, kSequencePointKind_Normal, 0, 235 },
	{ 108755, 3, 35, 35, 17, 30, 126, kSequencePointKind_Normal, 0, 236 },
	{ 108755, 3, 37, 37, 13, 45, 131, kSequencePointKind_Normal, 0, 237 },
	{ 108755, 3, 37, 37, 13, 45, 135, kSequencePointKind_StepOut, 0, 238 },
	{ 108755, 3, 38, 38, 13, 25, 145, kSequencePointKind_Normal, 0, 239 },
	{ 108755, 3, 39, 39, 9, 10, 150, kSequencePointKind_Normal, 0, 240 },
	{ 108756, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 241 },
	{ 108756, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 242 },
	{ 108756, 3, 42, 42, 9, 10, 0, kSequencePointKind_Normal, 0, 243 },
	{ 108756, 3, 43, 43, 13, 39, 1, kSequencePointKind_Normal, 0, 244 },
	{ 108756, 3, 43, 43, 13, 39, 2, kSequencePointKind_StepOut, 0, 245 },
	{ 108756, 3, 45, 45, 13, 92, 12, kSequencePointKind_Normal, 0, 246 },
	{ 108756, 3, 45, 45, 13, 92, 17, kSequencePointKind_StepOut, 0, 247 },
	{ 108756, 3, 45, 45, 0, 0, 23, kSequencePointKind_Normal, 0, 248 },
	{ 108756, 3, 46, 46, 13, 14, 26, kSequencePointKind_Normal, 0, 249 },
	{ 108756, 3, 47, 47, 17, 69, 27, kSequencePointKind_Normal, 0, 250 },
	{ 108756, 3, 47, 47, 17, 69, 30, kSequencePointKind_StepOut, 0, 251 },
	{ 108756, 3, 47, 47, 17, 69, 35, kSequencePointKind_StepOut, 0, 252 },
	{ 108756, 3, 48, 48, 17, 29, 45, kSequencePointKind_Normal, 0, 253 },
	{ 108756, 3, 50, 50, 13, 26, 49, kSequencePointKind_Normal, 0, 254 },
	{ 108756, 3, 51, 51, 9, 10, 53, kSequencePointKind_Normal, 0, 255 },
	{ 108757, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 256 },
	{ 108757, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 257 },
	{ 108757, 3, 54, 54, 9, 10, 0, kSequencePointKind_Normal, 0, 258 },
	{ 108757, 3, 55, 55, 13, 29, 1, kSequencePointKind_Normal, 0, 259 },
	{ 108757, 3, 55, 55, 13, 29, 3, kSequencePointKind_StepOut, 0, 260 },
	{ 108757, 3, 55, 55, 0, 0, 9, kSequencePointKind_Normal, 0, 261 },
	{ 108757, 3, 56, 56, 17, 56, 12, kSequencePointKind_Normal, 0, 262 },
	{ 108757, 3, 56, 56, 17, 56, 14, kSequencePointKind_StepOut, 0, 263 },
	{ 108757, 3, 56, 56, 17, 56, 19, kSequencePointKind_StepOut, 0, 264 },
	{ 108757, 3, 58, 58, 13, 37, 27, kSequencePointKind_Normal, 0, 265 },
	{ 108757, 3, 58, 58, 13, 37, 28, kSequencePointKind_StepOut, 0, 266 },
	{ 108757, 3, 59, 59, 13, 27, 34, kSequencePointKind_Normal, 0, 267 },
	{ 108757, 3, 60, 60, 13, 50, 50, kSequencePointKind_Normal, 0, 268 },
	{ 108757, 3, 60, 60, 13, 50, 51, kSequencePointKind_StepOut, 0, 269 },
	{ 108757, 3, 60, 60, 13, 50, 56, kSequencePointKind_StepOut, 0, 270 },
	{ 108757, 3, 61, 61, 9, 10, 64, kSequencePointKind_Normal, 0, 271 },
	{ 108758, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 272 },
	{ 108758, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 273 },
	{ 108758, 3, 64, 64, 9, 10, 0, kSequencePointKind_Normal, 0, 274 },
	{ 108758, 3, 65, 65, 13, 29, 1, kSequencePointKind_Normal, 0, 275 },
	{ 108758, 3, 65, 65, 13, 29, 3, kSequencePointKind_StepOut, 0, 276 },
	{ 108758, 3, 65, 65, 0, 0, 9, kSequencePointKind_Normal, 0, 277 },
	{ 108758, 3, 66, 66, 17, 64, 12, kSequencePointKind_Normal, 0, 278 },
	{ 108758, 3, 66, 66, 17, 64, 24, kSequencePointKind_StepOut, 0, 279 },
	{ 108758, 3, 68, 68, 13, 55, 32, kSequencePointKind_Normal, 0, 280 },
	{ 108758, 3, 68, 68, 13, 55, 34, kSequencePointKind_StepOut, 0, 281 },
	{ 108758, 3, 68, 68, 13, 55, 39, kSequencePointKind_StepOut, 0, 282 },
	{ 108758, 3, 69, 69, 9, 10, 47, kSequencePointKind_Normal, 0, 283 },
	{ 108759, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 284 },
	{ 108759, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 285 },
	{ 108759, 3, 72, 72, 9, 10, 0, kSequencePointKind_Normal, 0, 286 },
	{ 108759, 3, 73, 73, 13, 89, 1, kSequencePointKind_Normal, 0, 287 },
	{ 108759, 3, 73, 73, 13, 89, 3, kSequencePointKind_StepOut, 0, 288 },
	{ 108759, 3, 75, 75, 13, 34, 9, kSequencePointKind_Normal, 0, 289 },
	{ 108759, 3, 75, 75, 0, 0, 15, kSequencePointKind_Normal, 0, 290 },
	{ 108759, 3, 76, 76, 13, 14, 21, kSequencePointKind_Normal, 0, 291 },
	{ 108759, 3, 77, 77, 17, 92, 22, kSequencePointKind_Normal, 0, 292 },
	{ 108759, 3, 77, 77, 17, 92, 39, kSequencePointKind_StepOut, 0, 293 },
	{ 108759, 3, 78, 78, 17, 92, 44, kSequencePointKind_Normal, 0, 294 },
	{ 108759, 3, 78, 78, 17, 92, 61, kSequencePointKind_StepOut, 0, 295 },
	{ 108759, 3, 80, 80, 17, 61, 66, kSequencePointKind_Normal, 0, 296 },
	{ 108759, 3, 80, 80, 17, 61, 67, kSequencePointKind_StepOut, 0, 297 },
	{ 108759, 3, 82, 82, 22, 31, 74, kSequencePointKind_Normal, 0, 298 },
	{ 108759, 3, 82, 82, 33, 52, 77, kSequencePointKind_Normal, 0, 299 },
	{ 108759, 3, 82, 82, 0, 0, 82, kSequencePointKind_Normal, 0, 300 },
	{ 108759, 3, 83, 83, 17, 18, 84, kSequencePointKind_Normal, 0, 301 },
	{ 108759, 3, 84, 84, 21, 57, 85, kSequencePointKind_Normal, 0, 302 },
	{ 108759, 3, 84, 84, 21, 57, 94, kSequencePointKind_StepOut, 0, 303 },
	{ 108759, 3, 85, 85, 26, 35, 100, kSequencePointKind_Normal, 0, 304 },
	{ 108759, 3, 85, 85, 0, 0, 103, kSequencePointKind_Normal, 0, 305 },
	{ 108759, 3, 86, 86, 21, 22, 105, kSequencePointKind_Normal, 0, 306 },
	{ 108759, 3, 87, 87, 25, 76, 106, kSequencePointKind_Normal, 0, 307 },
	{ 108759, 3, 87, 87, 25, 76, 120, kSequencePointKind_StepOut, 0, 308 },
	{ 108759, 3, 88, 88, 25, 53, 127, kSequencePointKind_Normal, 0, 309 },
	{ 108759, 3, 88, 88, 25, 53, 130, kSequencePointKind_StepOut, 0, 310 },
	{ 108759, 3, 89, 89, 25, 53, 136, kSequencePointKind_Normal, 0, 311 },
	{ 108759, 3, 89, 89, 25, 53, 139, kSequencePointKind_StepOut, 0, 312 },
	{ 108759, 3, 90, 90, 21, 22, 145, kSequencePointKind_Normal, 0, 313 },
	{ 108759, 3, 85, 85, 44, 47, 146, kSequencePointKind_Normal, 0, 314 },
	{ 108759, 3, 85, 85, 37, 42, 152, kSequencePointKind_Normal, 0, 315 },
	{ 108759, 3, 85, 85, 0, 0, 159, kSequencePointKind_Normal, 0, 316 },
	{ 108759, 3, 91, 91, 17, 18, 163, kSequencePointKind_Normal, 0, 317 },
	{ 108759, 3, 82, 82, 64, 67, 164, kSequencePointKind_Normal, 0, 318 },
	{ 108759, 3, 82, 82, 54, 62, 170, kSequencePointKind_Normal, 0, 319 },
	{ 108759, 3, 82, 82, 0, 0, 178, kSequencePointKind_Normal, 0, 320 },
	{ 108759, 3, 93, 93, 17, 59, 182, kSequencePointKind_Normal, 0, 321 },
	{ 108759, 3, 93, 93, 17, 59, 185, kSequencePointKind_StepOut, 0, 322 },
	{ 108759, 3, 93, 93, 17, 59, 190, kSequencePointKind_StepOut, 0, 323 },
	{ 108759, 3, 94, 94, 17, 37, 195, kSequencePointKind_Normal, 0, 324 },
	{ 108759, 3, 94, 94, 17, 37, 198, kSequencePointKind_StepOut, 0, 325 },
	{ 108759, 3, 95, 95, 17, 26, 204, kSequencePointKind_Normal, 0, 326 },
	{ 108759, 3, 97, 97, 13, 59, 210, kSequencePointKind_Normal, 0, 327 },
	{ 108759, 3, 97, 97, 13, 59, 210, kSequencePointKind_StepOut, 0, 328 },
	{ 108759, 3, 97, 97, 13, 59, 215, kSequencePointKind_StepOut, 0, 329 },
	{ 108759, 3, 97, 97, 13, 59, 220, kSequencePointKind_StepOut, 0, 330 },
	{ 108759, 3, 98, 98, 9, 10, 229, kSequencePointKind_Normal, 0, 331 },
	{ 108760, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 332 },
	{ 108760, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 333 },
	{ 108760, 3, 101, 101, 9, 10, 0, kSequencePointKind_Normal, 0, 334 },
	{ 108760, 3, 102, 102, 13, 71, 1, kSequencePointKind_Normal, 0, 335 },
	{ 108760, 3, 102, 102, 13, 71, 3, kSequencePointKind_StepOut, 0, 336 },
	{ 108760, 3, 103, 103, 9, 10, 11, kSequencePointKind_Normal, 0, 337 },
	{ 108761, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 338 },
	{ 108761, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 339 },
	{ 108761, 3, 106, 106, 9, 10, 0, kSequencePointKind_Normal, 0, 340 },
	{ 108761, 3, 107, 107, 13, 30, 1, kSequencePointKind_Normal, 0, 341 },
	{ 108761, 3, 107, 107, 13, 30, 3, kSequencePointKind_StepOut, 0, 342 },
	{ 108761, 3, 107, 107, 0, 0, 10, kSequencePointKind_Normal, 0, 343 },
	{ 108761, 3, 108, 108, 17, 24, 14, kSequencePointKind_Normal, 0, 344 },
	{ 108761, 3, 110, 110, 13, 27, 19, kSequencePointKind_Normal, 0, 345 },
	{ 108761, 3, 110, 110, 0, 0, 22, kSequencePointKind_Normal, 0, 346 },
	{ 108761, 3, 111, 111, 13, 14, 26, kSequencePointKind_Normal, 0, 347 },
	{ 108761, 3, 112, 112, 22, 31, 27, kSequencePointKind_Normal, 0, 348 },
	{ 108761, 3, 112, 112, 0, 0, 30, kSequencePointKind_Normal, 0, 349 },
	{ 108761, 3, 113, 113, 17, 18, 32, kSequencePointKind_Normal, 0, 350 },
	{ 108761, 3, 114, 114, 21, 81, 33, kSequencePointKind_Normal, 0, 351 },
	{ 108761, 3, 114, 114, 21, 81, 36, kSequencePointKind_StepOut, 0, 352 },
	{ 108761, 3, 115, 115, 21, 43, 48, kSequencePointKind_Normal, 0, 353 },
	{ 108761, 3, 115, 115, 21, 43, 51, kSequencePointKind_StepOut, 0, 354 },
	{ 108761, 3, 115, 115, 0, 0, 58, kSequencePointKind_Normal, 0, 355 },
	{ 108761, 3, 116, 116, 25, 72, 62, kSequencePointKind_Normal, 0, 356 },
	{ 108761, 3, 116, 116, 25, 72, 67, kSequencePointKind_StepOut, 0, 357 },
	{ 108761, 3, 117, 117, 17, 18, 73, kSequencePointKind_Normal, 0, 358 },
	{ 108761, 3, 112, 112, 54, 57, 74, kSequencePointKind_Normal, 0, 359 },
	{ 108761, 3, 112, 112, 33, 52, 80, kSequencePointKind_Normal, 0, 360 },
	{ 108761, 3, 112, 112, 33, 52, 83, kSequencePointKind_StepOut, 0, 361 },
	{ 108761, 3, 112, 112, 0, 0, 92, kSequencePointKind_Normal, 0, 362 },
	{ 108761, 3, 118, 118, 13, 14, 96, kSequencePointKind_Normal, 0, 363 },
	{ 108761, 3, 120, 120, 13, 40, 97, kSequencePointKind_Normal, 0, 364 },
	{ 108761, 3, 120, 120, 13, 40, 98, kSequencePointKind_StepOut, 0, 365 },
	{ 108761, 3, 121, 121, 13, 46, 104, kSequencePointKind_Normal, 0, 366 },
	{ 108761, 3, 121, 121, 13, 46, 115, kSequencePointKind_StepOut, 0, 367 },
	{ 108761, 3, 121, 121, 13, 46, 121, kSequencePointKind_StepOut, 0, 368 },
	{ 108761, 3, 122, 122, 13, 32, 127, kSequencePointKind_Normal, 0, 369 },
	{ 108761, 3, 122, 122, 13, 32, 129, kSequencePointKind_StepOut, 0, 370 },
	{ 108761, 3, 124, 124, 13, 33, 135, kSequencePointKind_Normal, 0, 371 },
	{ 108761, 3, 124, 124, 0, 0, 138, kSequencePointKind_Normal, 0, 372 },
	{ 108761, 3, 125, 125, 17, 24, 142, kSequencePointKind_Normal, 0, 373 },
	{ 108761, 3, 127, 127, 13, 62, 144, kSequencePointKind_Normal, 0, 374 },
	{ 108761, 3, 127, 127, 13, 62, 145, kSequencePointKind_StepOut, 0, 375 },
	{ 108761, 3, 128, 128, 13, 62, 151, kSequencePointKind_Normal, 0, 376 },
	{ 108761, 3, 128, 128, 13, 62, 157, kSequencePointKind_StepOut, 0, 377 },
	{ 108761, 3, 128, 128, 13, 62, 163, kSequencePointKind_StepOut, 0, 378 },
	{ 108761, 3, 129, 129, 13, 54, 169, kSequencePointKind_Normal, 0, 379 },
	{ 108761, 3, 129, 129, 13, 54, 171, kSequencePointKind_StepOut, 0, 380 },
	{ 108761, 3, 131, 131, 13, 48, 177, kSequencePointKind_Normal, 0, 381 },
	{ 108761, 3, 131, 131, 13, 48, 178, kSequencePointKind_StepOut, 0, 382 },
	{ 108761, 3, 132, 132, 13, 48, 184, kSequencePointKind_Normal, 0, 383 },
	{ 108761, 3, 132, 132, 13, 48, 185, kSequencePointKind_StepOut, 0, 384 },
	{ 108761, 3, 133, 133, 13, 42, 191, kSequencePointKind_Normal, 0, 385 },
	{ 108761, 3, 133, 133, 13, 42, 194, kSequencePointKind_StepOut, 0, 386 },
	{ 108761, 3, 134, 134, 13, 51, 201, kSequencePointKind_Normal, 0, 387 },
	{ 108761, 3, 134, 134, 13, 51, 212, kSequencePointKind_StepOut, 0, 388 },
	{ 108761, 3, 134, 134, 13, 51, 218, kSequencePointKind_StepOut, 0, 389 },
	{ 108761, 3, 135, 135, 13, 40, 224, kSequencePointKind_Normal, 0, 390 },
	{ 108761, 3, 135, 135, 13, 40, 235, kSequencePointKind_StepOut, 0, 391 },
	{ 108761, 3, 136, 136, 13, 40, 241, kSequencePointKind_Normal, 0, 392 },
	{ 108761, 3, 136, 136, 13, 40, 243, kSequencePointKind_StepOut, 0, 393 },
	{ 108761, 3, 137, 137, 13, 40, 249, kSequencePointKind_Normal, 0, 394 },
	{ 108761, 3, 137, 137, 13, 40, 251, kSequencePointKind_StepOut, 0, 395 },
	{ 108761, 3, 138, 138, 9, 10, 257, kSequencePointKind_Normal, 0, 396 },
	{ 108762, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 397 },
	{ 108762, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 398 },
	{ 108762, 3, 141, 141, 9, 10, 0, kSequencePointKind_Normal, 0, 399 },
	{ 108762, 3, 142, 142, 13, 30, 1, kSequencePointKind_Normal, 0, 400 },
	{ 108762, 3, 142, 142, 13, 30, 3, kSequencePointKind_StepOut, 0, 401 },
	{ 108762, 3, 142, 142, 0, 0, 9, kSequencePointKind_Normal, 0, 402 },
	{ 108762, 3, 143, 143, 17, 24, 12, kSequencePointKind_Normal, 0, 403 },
	{ 108762, 3, 145, 145, 13, 27, 17, kSequencePointKind_Normal, 0, 404 },
	{ 108762, 3, 145, 145, 0, 0, 19, kSequencePointKind_Normal, 0, 405 },
	{ 108762, 3, 146, 146, 13, 14, 22, kSequencePointKind_Normal, 0, 406 },
	{ 108762, 3, 147, 147, 22, 31, 23, kSequencePointKind_Normal, 0, 407 },
	{ 108762, 3, 147, 147, 0, 0, 25, kSequencePointKind_Normal, 0, 408 },
	{ 108762, 3, 148, 148, 17, 18, 27, kSequencePointKind_Normal, 0, 409 },
	{ 108762, 3, 149, 149, 21, 81, 28, kSequencePointKind_Normal, 0, 410 },
	{ 108762, 3, 149, 149, 21, 81, 30, kSequencePointKind_StepOut, 0, 411 },
	{ 108762, 3, 150, 150, 21, 43, 41, kSequencePointKind_Normal, 0, 412 },
	{ 108762, 3, 150, 150, 21, 43, 43, kSequencePointKind_StepOut, 0, 413 },
	{ 108762, 3, 150, 150, 0, 0, 50, kSequencePointKind_Normal, 0, 414 },
	{ 108762, 3, 151, 151, 25, 64, 54, kSequencePointKind_Normal, 0, 415 },
	{ 108762, 3, 151, 151, 25, 64, 57, kSequencePointKind_StepOut, 0, 416 },
	{ 108762, 3, 152, 152, 17, 18, 63, kSequencePointKind_Normal, 0, 417 },
	{ 108762, 3, 147, 147, 54, 57, 64, kSequencePointKind_Normal, 0, 418 },
	{ 108762, 3, 147, 147, 33, 52, 68, kSequencePointKind_Normal, 0, 419 },
	{ 108762, 3, 147, 147, 33, 52, 70, kSequencePointKind_StepOut, 0, 420 },
	{ 108762, 3, 147, 147, 0, 0, 79, kSequencePointKind_Normal, 0, 421 },
	{ 108762, 3, 153, 153, 13, 14, 83, kSequencePointKind_Normal, 0, 422 },
	{ 108762, 3, 155, 155, 13, 52, 84, kSequencePointKind_Normal, 0, 423 },
	{ 108762, 3, 155, 155, 13, 52, 86, kSequencePointKind_StepOut, 0, 424 },
	{ 108762, 3, 155, 155, 13, 52, 91, kSequencePointKind_StepOut, 0, 425 },
	{ 108762, 3, 155, 155, 13, 52, 96, kSequencePointKind_StepOut, 0, 426 },
	{ 108762, 3, 156, 156, 13, 60, 102, kSequencePointKind_Normal, 0, 427 },
	{ 108762, 3, 156, 156, 13, 60, 104, kSequencePointKind_StepOut, 0, 428 },
	{ 108762, 3, 156, 156, 13, 60, 109, kSequencePointKind_StepOut, 0, 429 },
	{ 108762, 3, 156, 156, 13, 60, 114, kSequencePointKind_StepOut, 0, 430 },
	{ 108762, 3, 158, 158, 13, 33, 120, kSequencePointKind_Normal, 0, 431 },
	{ 108762, 3, 158, 158, 0, 0, 123, kSequencePointKind_Normal, 0, 432 },
	{ 108762, 3, 159, 159, 17, 24, 127, kSequencePointKind_Normal, 0, 433 },
	{ 108762, 3, 161, 161, 13, 74, 129, kSequencePointKind_Normal, 0, 434 },
	{ 108762, 3, 161, 161, 13, 74, 131, kSequencePointKind_StepOut, 0, 435 },
	{ 108762, 3, 161, 161, 13, 74, 136, kSequencePointKind_StepOut, 0, 436 },
	{ 108762, 3, 161, 161, 13, 74, 141, kSequencePointKind_StepOut, 0, 437 },
	{ 108762, 3, 162, 162, 13, 60, 147, kSequencePointKind_Normal, 0, 438 },
	{ 108762, 3, 162, 162, 13, 60, 149, kSequencePointKind_StepOut, 0, 439 },
	{ 108762, 3, 162, 162, 13, 60, 154, kSequencePointKind_StepOut, 0, 440 },
	{ 108762, 3, 162, 162, 13, 60, 159, kSequencePointKind_StepOut, 0, 441 },
	{ 108762, 3, 163, 163, 13, 60, 165, kSequencePointKind_Normal, 0, 442 },
	{ 108762, 3, 163, 163, 13, 60, 167, kSequencePointKind_StepOut, 0, 443 },
	{ 108762, 3, 163, 163, 13, 60, 172, kSequencePointKind_StepOut, 0, 444 },
	{ 108762, 3, 163, 163, 13, 60, 177, kSequencePointKind_StepOut, 0, 445 },
	{ 108762, 3, 164, 164, 9, 10, 183, kSequencePointKind_Normal, 0, 446 },
	{ 108763, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 447 },
	{ 108763, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 448 },
	{ 108763, 3, 167, 167, 9, 10, 0, kSequencePointKind_Normal, 0, 449 },
	{ 108763, 3, 168, 168, 13, 50, 1, kSequencePointKind_Normal, 0, 450 },
	{ 108763, 3, 168, 168, 13, 50, 13, kSequencePointKind_StepOut, 0, 451 },
	{ 108763, 3, 169, 169, 9, 10, 21, kSequencePointKind_Normal, 0, 452 },
	{ 108764, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 453 },
	{ 108764, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 454 },
	{ 108764, 3, 5, 5, 9, 70, 0, kSequencePointKind_Normal, 0, 455 },
	{ 108806, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 456 },
	{ 108806, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 457 },
	{ 108806, 4, 65, 65, 65, 69, 0, kSequencePointKind_Normal, 0, 458 },
	{ 108807, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 459 },
	{ 108807, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 460 },
	{ 108807, 4, 65, 65, 70, 74, 0, kSequencePointKind_Normal, 0, 461 },
	{ 108808, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 462 },
	{ 108808, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 463 },
	{ 108808, 4, 66, 66, 71, 75, 0, kSequencePointKind_Normal, 0, 464 },
	{ 108809, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 465 },
	{ 108809, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 466 },
	{ 108809, 4, 66, 66, 76, 80, 0, kSequencePointKind_Normal, 0, 467 },
	{ 108810, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 468 },
	{ 108810, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 469 },
	{ 108810, 4, 67, 67, 63, 67, 0, kSequencePointKind_Normal, 0, 470 },
	{ 108811, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 471 },
	{ 108811, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 472 },
	{ 108811, 4, 67, 67, 68, 72, 0, kSequencePointKind_Normal, 0, 473 },
	{ 108823, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 474 },
	{ 108823, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 475 },
	{ 108823, 4, 90, 90, 9, 10, 0, kSequencePointKind_Normal, 0, 476 },
	{ 108823, 4, 91, 91, 13, 41, 1, kSequencePointKind_Normal, 0, 477 },
	{ 108823, 4, 91, 91, 13, 41, 1, kSequencePointKind_StepOut, 0, 478 },
	{ 108823, 4, 92, 92, 13, 38, 7, kSequencePointKind_Normal, 0, 479 },
	{ 108823, 4, 92, 92, 13, 38, 7, kSequencePointKind_StepOut, 0, 480 },
	{ 108823, 4, 93, 93, 9, 10, 13, kSequencePointKind_Normal, 0, 481 },
	{ 108824, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 482 },
	{ 108824, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 483 },
	{ 108824, 4, 97, 97, 9, 10, 0, kSequencePointKind_Normal, 0, 484 },
	{ 108824, 4, 98, 98, 13, 45, 1, kSequencePointKind_Normal, 0, 485 },
	{ 108824, 4, 98, 98, 13, 45, 12, kSequencePointKind_StepOut, 0, 486 },
	{ 108824, 4, 99, 99, 9, 10, 18, kSequencePointKind_Normal, 0, 487 },
	{ 108825, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 488 },
	{ 108825, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 489 },
	{ 108825, 4, 103, 103, 9, 10, 0, kSequencePointKind_Normal, 0, 490 },
	{ 108825, 4, 104, 104, 13, 42, 1, kSequencePointKind_Normal, 0, 491 },
	{ 108825, 4, 104, 104, 13, 42, 12, kSequencePointKind_StepOut, 0, 492 },
	{ 108825, 4, 105, 105, 9, 10, 18, kSequencePointKind_Normal, 0, 493 },
	{ 108826, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 494 },
	{ 108826, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 495 },
	{ 108826, 4, 109, 109, 9, 10, 0, kSequencePointKind_Normal, 0, 496 },
	{ 108826, 4, 110, 110, 13, 61, 1, kSequencePointKind_Normal, 0, 497 },
	{ 108826, 4, 110, 110, 13, 61, 1, kSequencePointKind_StepOut, 0, 498 },
	{ 108826, 4, 110, 110, 13, 61, 13, kSequencePointKind_StepOut, 0, 499 },
	{ 108826, 4, 111, 111, 9, 10, 19, kSequencePointKind_Normal, 0, 500 },
	{ 108827, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 501 },
	{ 108827, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 502 },
	{ 108827, 4, 115, 115, 9, 10, 0, kSequencePointKind_Normal, 0, 503 },
	{ 108827, 4, 116, 116, 13, 76, 1, kSequencePointKind_Normal, 0, 504 },
	{ 108827, 4, 116, 116, 13, 76, 1, kSequencePointKind_StepOut, 0, 505 },
	{ 108827, 4, 116, 116, 13, 76, 14, kSequencePointKind_StepOut, 0, 506 },
	{ 108827, 4, 117, 117, 9, 10, 20, kSequencePointKind_Normal, 0, 507 },
	{ 108828, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 508 },
	{ 108828, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 509 },
	{ 108828, 4, 121, 121, 9, 10, 0, kSequencePointKind_Normal, 0, 510 },
	{ 108828, 4, 122, 122, 13, 59, 1, kSequencePointKind_Normal, 0, 511 },
	{ 108828, 4, 122, 122, 13, 59, 1, kSequencePointKind_StepOut, 0, 512 },
	{ 108828, 4, 122, 122, 13, 59, 13, kSequencePointKind_StepOut, 0, 513 },
	{ 108828, 4, 123, 123, 9, 10, 19, kSequencePointKind_Normal, 0, 514 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_UIModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UIModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UI/ScriptBindings/CanvasGroup.bindings.cs", { 95, 5, 239, 61, 209, 226, 205, 182, 140, 185, 147, 80, 77, 9, 100, 186} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UI/ScriptBindings/CanvasRenderer.bindings.cs", { 135, 199, 204, 121, 216, 244, 162, 129, 69, 183, 190, 70, 97, 148, 38, 229} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UI/ScriptBindings/RectTransformUtility.cs", { 37, 153, 240, 31, 183, 222, 142, 197, 21, 182, 96, 12, 38, 58, 123, 215} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UI/ScriptBindings/UICanvas.bindings.cs", { 131, 105, 250, 183, 35, 102, 124, 86, 164, 177, 236, 17, 155, 3, 221, 154} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[4] = 
{
	{ 13941, 1 },
	{ 13942, 2 },
	{ 13943, 3 },
	{ 13948, 4 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[27] = 
{
	{ 0, 12 },
	{ 0, 17 },
	{ 0, 25 },
	{ 0, 13 },
	{ 0, 489 },
	{ 67, 384 },
	{ 76, 295 },
	{ 0, 14 },
	{ 0, 19 },
	{ 0, 15 },
	{ 0, 153 },
	{ 0, 55 },
	{ 0, 66 },
	{ 0, 49 },
	{ 0, 232 },
	{ 21, 210 },
	{ 74, 182 },
	{ 100, 163 },
	{ 105, 146 },
	{ 0, 13 },
	{ 0, 258 },
	{ 27, 96 },
	{ 32, 74 },
	{ 0, 184 },
	{ 23, 83 },
	{ 27, 64 },
	{ 0, 23 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[155] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 12, 0, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 17, 1, 1 },
	{ 25, 2, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 13, 3, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 489, 4, 3 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 14, 7, 1 },
	{ 19, 8, 1 },
	{ 15, 9, 1 },
	{ 153, 10, 1 },
	{ 55, 11, 1 },
	{ 66, 12, 1 },
	{ 49, 13, 1 },
	{ 232, 14, 5 },
	{ 13, 19, 1 },
	{ 258, 20, 3 },
	{ 184, 23, 3 },
	{ 23, 26, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UIModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UIModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	515,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_UIModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	4,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
