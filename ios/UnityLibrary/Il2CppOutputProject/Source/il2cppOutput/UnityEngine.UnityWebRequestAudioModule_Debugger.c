﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[16] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAudioModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAudioModule[63] = 
{
	{ 110111, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110111, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110111, 1, 19, 19, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110111, 1, 20, 20, 13, 50, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110111, 1, 20, 20, 13, 50, 5, kSequencePointKind_StepOut, 0, 4 },
	{ 110111, 1, 21, 21, 9, 10, 15, kSequencePointKind_Normal, 0, 5 },
	{ 110112, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110112, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110112, 1, 23, 23, 9, 73, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110112, 1, 23, 23, 9, 73, 1, kSequencePointKind_StepOut, 0, 9 },
	{ 110112, 1, 24, 24, 9, 10, 7, kSequencePointKind_Normal, 0, 10 },
	{ 110112, 1, 25, 25, 13, 53, 8, kSequencePointKind_Normal, 0, 11 },
	{ 110112, 1, 25, 25, 13, 53, 11, kSequencePointKind_StepOut, 0, 12 },
	{ 110112, 1, 26, 26, 9, 10, 17, kSequencePointKind_Normal, 0, 13 },
	{ 110113, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 14 },
	{ 110113, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 15 },
	{ 110113, 1, 28, 28, 9, 70, 0, kSequencePointKind_Normal, 0, 16 },
	{ 110113, 1, 28, 28, 9, 70, 1, kSequencePointKind_StepOut, 0, 17 },
	{ 110113, 1, 29, 29, 9, 10, 7, kSequencePointKind_Normal, 0, 18 },
	{ 110113, 1, 30, 30, 13, 65, 8, kSequencePointKind_Normal, 0, 19 },
	{ 110113, 1, 30, 30, 13, 65, 10, kSequencePointKind_StepOut, 0, 20 },
	{ 110113, 1, 30, 30, 13, 65, 16, kSequencePointKind_StepOut, 0, 21 },
	{ 110113, 1, 31, 31, 9, 10, 22, kSequencePointKind_Normal, 0, 22 },
	{ 110114, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 23 },
	{ 110114, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 24 },
	{ 110114, 1, 34, 34, 9, 10, 0, kSequencePointKind_Normal, 0, 25 },
	{ 110114, 1, 35, 35, 13, 67, 1, kSequencePointKind_Normal, 0, 26 },
	{ 110114, 1, 35, 35, 13, 67, 8, kSequencePointKind_StepOut, 0, 27 },
	{ 110114, 1, 36, 36, 9, 10, 16, kSequencePointKind_Normal, 0, 28 },
	{ 110115, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 29 },
	{ 110115, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 30 },
	{ 110115, 1, 39, 39, 9, 10, 0, kSequencePointKind_Normal, 0, 31 },
	{ 110115, 1, 40, 40, 13, 50, 1, kSequencePointKind_Normal, 0, 32 },
	{ 110115, 1, 40, 40, 13, 50, 7, kSequencePointKind_StepOut, 0, 33 },
	{ 110115, 1, 41, 41, 13, 28, 13, kSequencePointKind_Normal, 0, 34 },
	{ 110115, 1, 41, 41, 13, 28, 14, kSequencePointKind_StepOut, 0, 35 },
	{ 110115, 1, 42, 42, 9, 10, 20, kSequencePointKind_Normal, 0, 36 },
	{ 110116, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 37 },
	{ 110116, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 38 },
	{ 110116, 1, 45, 45, 9, 10, 0, kSequencePointKind_Normal, 0, 39 },
	{ 110116, 1, 46, 46, 13, 102, 1, kSequencePointKind_Normal, 0, 40 },
	{ 110116, 1, 46, 46, 13, 102, 6, kSequencePointKind_StepOut, 0, 41 },
	{ 110122, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 42 },
	{ 110122, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 43 },
	{ 110122, 1, 57, 57, 9, 10, 0, kSequencePointKind_Normal, 0, 44 },
	{ 110122, 1, 58, 58, 13, 82, 1, kSequencePointKind_Normal, 0, 45 },
	{ 110122, 1, 58, 58, 13, 82, 2, kSequencePointKind_StepOut, 0, 46 },
	{ 110122, 1, 58, 58, 13, 82, 7, kSequencePointKind_StepOut, 0, 47 },
	{ 110122, 1, 59, 59, 9, 10, 15, kSequencePointKind_Normal, 0, 48 },
	{ 110124, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 49 },
	{ 110124, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 50 },
	{ 110124, 2, 9, 9, 9, 10, 0, kSequencePointKind_Normal, 0, 51 },
	{ 110124, 2, 10, 10, 13, 127, 1, kSequencePointKind_Normal, 0, 52 },
	{ 110124, 2, 10, 10, 13, 127, 9, kSequencePointKind_StepOut, 0, 53 },
	{ 110124, 2, 10, 10, 13, 127, 15, kSequencePointKind_StepOut, 0, 54 },
	{ 110124, 2, 11, 11, 9, 10, 23, kSequencePointKind_Normal, 0, 55 },
	{ 110125, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 56 },
	{ 110125, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 57 },
	{ 110125, 2, 14, 14, 9, 10, 0, kSequencePointKind_Normal, 0, 58 },
	{ 110125, 2, 15, 15, 13, 127, 1, kSequencePointKind_Normal, 0, 59 },
	{ 110125, 2, 15, 15, 13, 127, 9, kSequencePointKind_StepOut, 0, 60 },
	{ 110125, 2, 15, 15, 13, 127, 15, kSequencePointKind_StepOut, 0, 61 },
	{ 110125, 2, 16, 16, 9, 10, 23, kSequencePointKind_Normal, 0, 62 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAudioModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAudioModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UnityWebRequestAudio/Public/DownloadHandlerAudio.bindings.cs", { 4, 250, 184, 8, 122, 191, 225, 156, 186, 137, 32, 90, 98, 74, 184, 183} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UnityWebRequestAudio/UnityWebRequestMultimedia.cs", { 46, 47, 54, 101, 212, 233, 229, 103, 104, 218, 41, 49, 244, 202, 200, 208} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14157, 1 },
	{ 14159, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[4] = 
{
	{ 0, 18 },
	{ 0, 17 },
	{ 0, 25 },
	{ 0, 25 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[16] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 18, 0, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 17, 1, 1 },
	{ 0, 0, 0 },
	{ 25, 2, 1 },
	{ 25, 3, 1 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestAudioModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestAudioModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	63,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_UnityWebRequestAudioModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
