﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[50] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_GridModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_GridModule[29] = 
{
	{ 109746, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109746, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109746, 1, 5, 5, 64, 65, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109746, 1, 5, 5, 66, 131, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109746, 1, 5, 5, 66, 131, 3, kSequencePointKind_StepOut, 0, 4 },
	{ 109746, 1, 5, 5, 66, 131, 9, kSequencePointKind_StepOut, 0, 5 },
	{ 109746, 1, 5, 5, 66, 131, 14, kSequencePointKind_StepOut, 0, 6 },
	{ 109746, 1, 5, 5, 66, 131, 19, kSequencePointKind_StepOut, 0, 7 },
	{ 109746, 1, 5, 5, 132, 133, 27, kSequencePointKind_Normal, 0, 8 },
	{ 109747, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 9 },
	{ 109747, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 10 },
	{ 109747, 1, 6, 6, 64, 65, 0, kSequencePointKind_Normal, 0, 11 },
	{ 109747, 1, 6, 6, 66, 145, 1, kSequencePointKind_Normal, 0, 12 },
	{ 109747, 1, 6, 6, 66, 145, 4, kSequencePointKind_StepOut, 0, 13 },
	{ 109747, 1, 6, 6, 66, 145, 10, kSequencePointKind_StepOut, 0, 14 },
	{ 109747, 1, 6, 6, 66, 145, 15, kSequencePointKind_StepOut, 0, 15 },
	{ 109747, 1, 6, 6, 66, 145, 20, kSequencePointKind_StepOut, 0, 16 },
	{ 109747, 1, 6, 6, 66, 145, 25, kSequencePointKind_StepOut, 0, 17 },
	{ 109747, 1, 6, 6, 146, 147, 33, kSequencePointKind_Normal, 0, 18 },
	{ 109770, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 19 },
	{ 109770, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 20 },
	{ 109770, 2, 57, 57, 9, 10, 0, kSequencePointKind_Normal, 0, 21 },
	{ 109770, 2, 58, 58, 13, 59, 1, kSequencePointKind_Normal, 0, 22 },
	{ 109770, 2, 58, 58, 13, 59, 4, kSequencePointKind_StepOut, 0, 23 },
	{ 109770, 2, 59, 59, 9, 10, 12, kSequencePointKind_Normal, 0, 24 },
	{ 109781, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 25 },
	{ 109781, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 26 },
	{ 109781, 2, 92, 92, 34, 35, 0, kSequencePointKind_Normal, 0, 27 },
	{ 109781, 2, 92, 92, 35, 36, 1, kSequencePointKind_Normal, 0, 28 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_GridModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_GridModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Grid/Managed/Grid.cs", { 205, 45, 190, 144, 44, 193, 74, 240, 184, 93, 26, 21, 161, 159, 60, 39} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Grid/ScriptBindings/GridLayout.bindings.cs", { 241, 190, 171, 186, 18, 113, 38, 103, 176, 228, 207, 106, 70, 8, 191, 49} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14114, 1 },
	{ 14117, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[3] = 
{
	{ 0, 29 },
	{ 0, 35 },
	{ 0, 14 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[50] = 
{
	{ 29, 0, 1 },
	{ 35, 1, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 14, 2, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_GridModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_GridModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	29,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_GridModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
