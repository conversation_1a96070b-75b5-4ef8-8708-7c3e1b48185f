﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Action_1_tB645F646DB079054A9500B09427CB02A88372D3F;
struct Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129;
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct MethodInfo_t;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E;
struct PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_DirectorModule[];
IL2CPP_EXTERN_C RuntimeClass* Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral37B97248AD78DD69BA3DF192A954DA136A0917D3;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_ClearReferenceValue_m011DC51A81993B00D95ACC74FD90291363AB534C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_DeferredEvaluate_mB87912913EA74033DF28BD325E55F3D1E171C23C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_Play_m8C2D0E5C525817068EFA757221AFBEA4B4678F27_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_SetGenericBinding_m6A14E1731C8CB0198777B8063DC278CE1166D244_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_SetReferenceValue_m2CD757BCF2F23538F99A0DE09AC87A03EC90EADE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector__ctor_mAC3BE2F955641A2D384EAE3235FFBF8ECFD27E3B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_add_paused_m23F6F60C960AAA38C9C73CC75265D3E3FF0105DB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_add_played_m8A41D810B43EA46886904460F74EFD184AAE8604_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_add_stopped_m8162B112FF06D90F144C49E6C990F705DFC28C96_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_get_playOnAwake_m2A99756D8D17B8FBA329BABF6B641D473DBD90E2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_remove_paused_m5AC5D63CBF564106D6A4EA018CC5D851C6F5AF31_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_remove_played_mEDED15F8A15D2D32F464D1B9C07AF51451BF4374_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_remove_stopped_m615BBD51C1CA79B01469504CD9403C84938C535F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_set_playOnAwake_mE95232C47209C2F9E414D561C58D53418B8E3A8F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* DirectorWrapMode_t25EBFAAB0FD6C649DD657C6A0A6EB3FB2ACC1A99_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* PlayState_tC54D3C31D69D8394457D7EC5712E56B0891A5750_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t30BE97772842985A3AF723FA1759387A3E79C563 
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E 
{
	int32_t ___m_Rate;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2 
{
	int32_t ___id;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct DirectorUpdateMode_t54781357DD39BC40A15CDEF720890F34ABFB32A9 
{
	int32_t ___value__;
};
struct DirectorWrapMode_t25EBFAAB0FD6C649DD657C6A0A6EB3FB2ACC1A99 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PlayState_tC54D3C31D69D8394457D7EC5712E56B0891A5750 
{
	int32_t ___value__;
};
struct PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E 
{
	intptr_t ___m_Handle;
	uint32_t ___m_Version;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Action_1_tB645F646DB079054A9500B09427CB02A88372D3F  : public MulticastDelegate_t
{
};
struct Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99  : public MulticastDelegate_t
{
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
};
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
};
struct PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___played;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___paused;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___stopped;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E_StaticFields
{
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_24Fps;
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_23_976Fps;
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_25Fps;
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_30Fps;
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_29_97Fps;
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_50Fps;
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_60Fps;
	FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___k_59_94Fps;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif


IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_m5A038831CEB84A7E374FE59D43444412629F833F_gshared_inline (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* __this, Il2CppFullySharedGenericAny ___0_obj, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, int32_t ___0_mode, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* ___0_asset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, bool ___0_on, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___0_frameRate, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* __this, String_t* ___0_paramName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* ___0_asset, int32_t ___1_mode, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_key, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E* ___0_frameRate, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2* ___0_id, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2* ___0_id, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2* ___0_id, bool* ___1_idValid, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
inline void Action_1_Invoke_m1DDC149E3BDDF7CAA1CCEBDA6D58D6971F126303_inline (Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* __this, PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99*, Il2CppFullySharedGenericAny, const RuntimeMethod*))Action_1_Invoke_m5A038831CEB84A7E374FE59D43444412629F833F_gshared_inline)((Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99*)__this, (Il2CppFullySharedGenericAny)___0_obj, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8 (Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayState_tC54D3C31D69D8394457D7EC5712E56B0891A5750_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 3));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 4));
		int32_t L_0;
		L_0 = PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 4));
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 5));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 6));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 7));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 8));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 9));
		int32_t L_0 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 10));
		PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 10));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 11));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DirectorWrapMode_t25EBFAAB0FD6C649DD657C6A0A6EB3FB2ACC1A99_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 12));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 13));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 14));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 15));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 16));
		int32_t L_0;
		L_0 = PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 16));
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 17));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 18));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 19));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 20));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 21));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 22));
		ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* L_0;
		L_0 = PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 22));
		V_0 = ((PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E*)IsInstClass((RuntimeObject*)L_0, PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E_il2cpp_TypeInfo_var));
		goto IL_000f;
	}

IL_000f:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 23));
		PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 24));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 25));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 26));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 27));
		PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* L_0 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 28));
		PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 28));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 29));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 30));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 31));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 32));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 33));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 34));
		PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E L_0;
		L_0 = PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 34));
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 35));
		PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayableDirector_get_playOnAwake_m2A99756D8D17B8FBA329BABF6B641D473DBD90E2 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_get_playOnAwake_m2A99756D8D17B8FBA329BABF6B641D473DBD90E2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_get_playOnAwake_m2A99756D8D17B8FBA329BABF6B641D473DBD90E2_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 36));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 37));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 38));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 39));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 40));
		bool L_0;
		L_0 = PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 40));
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 41));
		bool L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_playOnAwake_mE95232C47209C2F9E414D561C58D53418B8E3A8F (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_set_playOnAwake_mE95232C47209C2F9E414D561C58D53418B8E3A8F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_set_playOnAwake_mE95232C47209C2F9E414D561C58D53418B8E3A8F_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 42));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 43));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 44));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 45));
		bool L_0 = ___0_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 46));
		PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 46));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 47));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_DeferredEvaluate_mB87912913EA74033DF28BD325E55F3D1E171C23C (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_DeferredEvaluate_mB87912913EA74033DF28BD325E55F3D1E171C23C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_DeferredEvaluate_mB87912913EA74033DF28BD325E55F3D1E171C23C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 48));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 49));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 50));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 51));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 52));
		PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 52));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 53));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___0_frameRate, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_frameRate));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 54));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 55));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 56));
		FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E L_0 = ___0_frameRate;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 57));
		PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5(__this, L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 57));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Play_m8C2D0E5C525817068EFA757221AFBEA4B4678F27 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* ___0_asset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_Play_m8C2D0E5C525817068EFA757221AFBEA4B4678F27_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_asset));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_Play_m8C2D0E5C525817068EFA757221AFBEA4B4678F27_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 58));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 59));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 60));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 61));
		PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* L_0 = ___0_asset;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 62));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 62));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 63));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 64));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 65));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_3 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_3, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral37B97248AD78DD69BA3DF192A954DA136A0917D3)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 65));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_3, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&PlayableDirector_Play_m8C2D0E5C525817068EFA757221AFBEA4B4678F27_RuntimeMethod_var)));
	}

IL_0017:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 66));
		PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* L_4 = ___0_asset;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 67));
		int32_t L_5;
		L_5 = PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 67));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 68));
		PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452(__this, L_4, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 68));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 69));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* ___0_asset, int32_t ___1_mode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_asset), (&___1_mode));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 70));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 71));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 72));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 73));
		PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* L_0 = ___0_asset;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 74));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 74));
		V_0 = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 75));
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 76));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 77));
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_3 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_3, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral37B97248AD78DD69BA3DF192A954DA136A0917D3)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 77));
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_3, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&PlayableDirector_Play_m35ABD2C6E99A600DBAA6E2A8FC4EA47C74F8F452_RuntimeMethod_var)));
	}

IL_0017:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 78));
		PlayableAsset_t6964211C3DAE503FEEDD04089ED6B962945D271E* L_4 = ___0_asset;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 79));
		PlayableDirector_set_playableAsset_mC8E8E91BAD45035A183E0C8E920626B437D6263B(__this, L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 79));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 80));
		int32_t L_5 = ___1_mode;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 81));
		PlayableDirector_set_extrapolationMode_m4F34CE7E1527D8F2A0BC1E5D003C78A4604FADB7(__this, L_5, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 81));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 82));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 83));
		PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 83));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 84));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetGenericBinding_m6A14E1731C8CB0198777B8063DC278CE1166D244 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_key, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_SetGenericBinding_m6A14E1731C8CB0198777B8063DC278CE1166D244_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_key), (&___1_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_SetGenericBinding_m6A14E1731C8CB0198777B8063DC278CE1166D244_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 85));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 86));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 87));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 88));
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_0 = ___0_key;
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_1 = ___1_value;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 89));
		PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5(__this, L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 89));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 90));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_timeUpdateMode_m31564B33CFAB9E9BBF7964970E1990F491272E17 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_set_timeUpdateMode_m31564B33CFAB9E9BBF7964970E1990F491272E17_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, int32_t);
	static PlayableDirector_set_timeUpdateMode_m31564B33CFAB9E9BBF7964970E1990F491272E17_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_set_timeUpdateMode_m31564B33CFAB9E9BBF7964970E1990F491272E17_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::set_timeUpdateMode(UnityEngine.Playables.DirectorUpdateMode)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_get_timeUpdateMode_m0E3162CC808AED8F717496C53F92C61AFF87FB80 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*PlayableDirector_get_timeUpdateMode_m0E3162CC808AED8F717496C53F92C61AFF87FB80_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_get_timeUpdateMode_m0E3162CC808AED8F717496C53F92C61AFF87FB80_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_get_timeUpdateMode_m0E3162CC808AED8F717496C53F92C61AFF87FB80_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::get_timeUpdateMode()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, double ___0_value, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, double);
	static PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::set_time(System.Double)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef double (*PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::get_time()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_set_initialTime_mB36EFEA98AC9F650CD92A73B4B717B44E0A9CD02 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, double ___0_value, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_set_initialTime_mB36EFEA98AC9F650CD92A73B4B717B44E0A9CD02_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, double);
	static PlayableDirector_set_initialTime_mB36EFEA98AC9F650CD92A73B4B717B44E0A9CD02_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_set_initialTime_mB36EFEA98AC9F650CD92A73B4B717B44E0A9CD02_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::set_initialTime(System.Double)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double PlayableDirector_get_initialTime_mDB1A2FEE06944870DCE1A71AF6A84844E9B694BF (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef double (*PlayableDirector_get_initialTime_mDB1A2FEE06944870DCE1A71AF6A84844E9B694BF_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_get_initialTime_mDB1A2FEE06944870DCE1A71AF6A84844E9B694BF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_get_initialTime_mDB1A2FEE06944870DCE1A71AF6A84844E9B694BF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::get_initialTime()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef double (*PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_get_duration_mEA5C8076E9806A26B9E9075D07485CBF7046E1F6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::get_duration()");
	double icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::Evaluate()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E ___0_frameRate, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F(__this, (&___0_frameRate), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::Play()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::Stop()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::Pause()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Resume_m8D0E95B7A33EEA6F5A7CEF05FE5D6A6B7E3AD178 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_Resume_m8D0E95B7A33EEA6F5A7CEF05FE5D6A6B7E3AD178_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_Resume_m8D0E95B7A33EEA6F5A7CEF05FE5D6A6B7E3AD178_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_Resume_m8D0E95B7A33EEA6F5A7CEF05FE5D6A6B7E3AD178_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::Resume()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_RebuildGraph_m344F4BA72962159E6F76F4B4C2E203BEB802D0F5 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_RebuildGraph_m344F4BA72962159E6F76F4B4C2E203BEB802D0F5_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_RebuildGraph_m344F4BA72962159E6F76F4B4C2E203BEB802D0F5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_RebuildGraph_m344F4BA72962159E6F76F4B4C2E203BEB802D0F5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::RebuildGraph()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_ClearReferenceValue_m011DC51A81993B00D95ACC74FD90291363AB534C (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2 ___0_id, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_ClearReferenceValue_m011DC51A81993B00D95ACC74FD90291363AB534C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_ClearReferenceValue_m011DC51A81993B00D95ACC74FD90291363AB534C_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B(__this, (&___0_id), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetReferenceValue_m2CD757BCF2F23538F99A0DE09AC87A03EC90EADE (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2 ___0_id, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_SetReferenceValue_m2CD757BCF2F23538F99A0DE09AC87A03EC90EADE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_SetReferenceValue_m2CD757BCF2F23538F99A0DE09AC87A03EC90EADE_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_0 = ___1_value;
		PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43(__this, (&___0_id), L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2 ___0_id, bool* ___1_idValid, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		bool* L_0 = ___1_idValid;
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_1;
		L_1 = PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106(__this, (&___0_id), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_key, const RuntimeMethod* method) 
{
	typedef Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* (*PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*);
	static PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::GetGenericBinding(UnityEngine.Object)");
	Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* icallRetVal = _il2cpp_icall_func(__this, ___0_key);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_ClearGenericBinding_mB68594465FC0DA4DA847830887157C1BE6D366E1 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_key, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_ClearGenericBinding_mB68594465FC0DA4DA847830887157C1BE6D366E1_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*);
	static PlayableDirector_ClearGenericBinding_mB68594465FC0DA4DA847830887157C1BE6D366E1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_ClearGenericBinding_mB68594465FC0DA4DA847830887157C1BE6D366E1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::ClearGenericBinding(UnityEngine.Object)");
	_il2cpp_icall_func(__this, ___0_key);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_RebindPlayableGraphOutputs_mEDA5855AED2223620A84E0DB99186F4953874B2A (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_RebindPlayableGraphOutputs_mEDA5855AED2223620A84E0DB99186F4953874B2A_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_RebindPlayableGraphOutputs_mEDA5855AED2223620A84E0DB99186F4953874B2A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_RebindPlayableGraphOutputs_mEDA5855AED2223620A84E0DB99186F4953874B2A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::RebindPlayableGraphOutputs()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_ProcessPendingGraphChanges_m97DFC4879F7CC9B7B55B242494D62DDF843844D5 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_ProcessPendingGraphChanges_m97DFC4879F7CC9B7B55B242494D62DDF843844D5_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_ProcessPendingGraphChanges_m97DFC4879F7CC9B7B55B242494D62DDF843844D5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_ProcessPendingGraphChanges_m97DFC4879F7CC9B7B55B242494D62DDF843844D5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::ProcessPendingGraphChanges()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayableDirector_HasGenericBinding_m94B154CC81AFA99A98383CA1ED7D56AB46E463DB (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_key, const RuntimeMethod* method) 
{
	typedef bool (*PlayableDirector_HasGenericBinding_m94B154CC81AFA99A98383CA1ED7D56AB46E463DB_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*);
	static PlayableDirector_HasGenericBinding_m94B154CC81AFA99A98383CA1ED7D56AB46E463DB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_HasGenericBinding_m94B154CC81AFA99A98383CA1ED7D56AB46E463DB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::HasGenericBinding(UnityEngine.Object)");
	bool icallRetVal = _il2cpp_icall_func(__this, ___0_key);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::GetPlayState()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, int32_t ___0_mode, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, int32_t);
	static PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_SetWrapMode_mDA42ABB7479C351AA377797F29DCEA54684264DB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::SetWrapMode(UnityEngine.Playables.DirectorWrapMode)");
	_il2cpp_icall_func(__this, ___0_mode);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::GetWrapMode()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_EvaluateNextFrame_m07FBD909431C2A913B46581622A247A469B3FBEC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::EvaluateNextFrame()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC(__this, (&V_0), NULL);
		PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, bool ___0_on, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, bool);
	static PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_SetPlayOnAwake_m4ABF4DB3685AB38CBBC856FD52D8C1BD3D89D755_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::SetPlayOnAwake(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_on);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef bool (*PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_GetPlayOnAwake_mDF2FAEAD040E29871245FB5AD6A9AA525B5B64C3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::GetPlayOnAwake()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_key, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_value, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*);
	static PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_Internal_SetGenericBinding_m5889234A1C5222C3ACFA329202AD6D58B3C4AFC5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::Internal_SetGenericBinding(UnityEngine.Object,UnityEngine.Object)");
	_il2cpp_icall_func(__this, ___0_key, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* ___0_asset, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A*);
	static PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_SetPlayableAsset_mCB30A400861B0D554BF6B397A2469B4C15AAE01D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::SetPlayableAsset(UnityEngine.ScriptableObject)");
	_il2cpp_icall_func(__this, ___0_asset);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	typedef ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* (*PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*);
	static PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::Internal_GetPlayableAsset()");
	ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_add_played_m8A41D810B43EA46886904460F74EFD184AAE8604 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_add_played_m8A41D810B43EA46886904460F74EFD184AAE8604_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_0 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_1 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_add_played_m8A41D810B43EA46886904460F74EFD184AAE8604_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___played;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)Castclass((RuntimeObject*)L_4, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F** L_5 = (Action_1_tB645F646DB079054A9500B09427CB02A88372D3F**)(&__this->___played);
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_6 = V_2;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_7 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_8;
		L_8 = InterlockedCompareExchangeImpl<Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*>(L_5, L_6, L_7);
		V_0 = L_8;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_9 = V_0;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_10 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_9) == ((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_remove_played_mEDED15F8A15D2D32F464D1B9C07AF51451BF4374 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_remove_played_mEDED15F8A15D2D32F464D1B9C07AF51451BF4374_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_0 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_1 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_remove_played_mEDED15F8A15D2D32F464D1B9C07AF51451BF4374_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___played;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)Castclass((RuntimeObject*)L_4, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F** L_5 = (Action_1_tB645F646DB079054A9500B09427CB02A88372D3F**)(&__this->___played);
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_6 = V_2;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_7 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_8;
		L_8 = InterlockedCompareExchangeImpl<Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*>(L_5, L_6, L_7);
		V_0 = L_8;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_9 = V_0;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_10 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_9) == ((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_add_paused_m23F6F60C960AAA38C9C73CC75265D3E3FF0105DB (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_add_paused_m23F6F60C960AAA38C9C73CC75265D3E3FF0105DB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_0 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_1 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_add_paused_m23F6F60C960AAA38C9C73CC75265D3E3FF0105DB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___paused;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)Castclass((RuntimeObject*)L_4, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F** L_5 = (Action_1_tB645F646DB079054A9500B09427CB02A88372D3F**)(&__this->___paused);
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_6 = V_2;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_7 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_8;
		L_8 = InterlockedCompareExchangeImpl<Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*>(L_5, L_6, L_7);
		V_0 = L_8;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_9 = V_0;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_10 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_9) == ((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_remove_paused_m5AC5D63CBF564106D6A4EA018CC5D851C6F5AF31 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_remove_paused_m5AC5D63CBF564106D6A4EA018CC5D851C6F5AF31_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_0 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_1 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_remove_paused_m5AC5D63CBF564106D6A4EA018CC5D851C6F5AF31_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___paused;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)Castclass((RuntimeObject*)L_4, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F** L_5 = (Action_1_tB645F646DB079054A9500B09427CB02A88372D3F**)(&__this->___paused);
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_6 = V_2;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_7 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_8;
		L_8 = InterlockedCompareExchangeImpl<Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*>(L_5, L_6, L_7);
		V_0 = L_8;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_9 = V_0;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_10 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_9) == ((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_add_stopped_m8162B112FF06D90F144C49E6C990F705DFC28C96 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_add_stopped_m8162B112FF06D90F144C49E6C990F705DFC28C96_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_0 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_1 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_add_stopped_m8162B112FF06D90F144C49E6C990F705DFC28C96_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___stopped;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)Castclass((RuntimeObject*)L_4, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F** L_5 = (Action_1_tB645F646DB079054A9500B09427CB02A88372D3F**)(&__this->___stopped);
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_6 = V_2;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_7 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_8;
		L_8 = InterlockedCompareExchangeImpl<Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*>(L_5, L_6, L_7);
		V_0 = L_8;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_9 = V_0;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_10 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_9) == ((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_remove_stopped_m615BBD51C1CA79B01469504CD9403C84938C535F (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_remove_stopped_m615BBD51C1CA79B01469504CD9403C84938C535F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_0 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_1 = NULL;
	Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* V_2 = NULL;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_remove_stopped_m615BBD51C1CA79B01469504CD9403C84938C535F_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___stopped;
		V_0 = L_0;
	}

IL_0007:
	{
		CHECK_PAUSE_POINT;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_1 = V_0;
		V_1 = L_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)Castclass((RuntimeObject*)L_4, Action_1_tB645F646DB079054A9500B09427CB02A88372D3F_il2cpp_TypeInfo_var));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F** L_5 = (Action_1_tB645F646DB079054A9500B09427CB02A88372D3F**)(&__this->___stopped);
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_6 = V_2;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_7 = V_1;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_8;
		L_8 = InterlockedCompareExchangeImpl<Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*>(L_5, L_6, L_7);
		V_0 = L_8;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_9 = V_0;
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_10 = V_1;
		if ((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_9) == ((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_ResetFrameTiming_m6842816BD7123E2F0AD11D198DA684199BB03206 (const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_ResetFrameTiming_m6842816BD7123E2F0AD11D198DA684199BB03206_ftn) ();
	static PlayableDirector_ResetFrameTiming_m6842816BD7123E2F0AD11D198DA684199BB03206_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_ResetFrameTiming_m6842816BD7123E2F0AD11D198DA684199BB03206_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::ResetFrameTiming()");
	_il2cpp_icall_func();
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 91));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 92));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 93));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 94));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___played;
		V_0 = (bool)((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_0) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 95));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 96));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = __this->___played;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 97));
		NullCheck(L_2);
		Action_1_Invoke_m1DDC149E3BDDF7CAA1CCEBDA6D58D6971F126303_inline(L_2, __this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 97));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 98));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 99));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 100));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 101));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 102));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___paused;
		V_0 = (bool)((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_0) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 103));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 104));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = __this->___paused;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 105));
		NullCheck(L_2);
		Action_1_Invoke_m1DDC149E3BDDF7CAA1CCEBDA6D58D6971F126303_inline(L_2, __this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 105));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 106));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 107));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 108));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 109));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 110));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_0 = __this->___stopped;
		V_0 = (bool)((!(((RuntimeObject*)(Action_1_tB645F646DB079054A9500B09427CB02A88372D3F*)L_0) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 111));
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_001b;
		}
	}
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 112));
		Action_1_tB645F646DB079054A9500B09427CB02A88372D3F* L_2 = __this->___stopped;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 113));
		NullCheck(L_2);
		Action_1_Invoke_m1DDC149E3BDDF7CAA1CCEBDA6D58D6971F126303_inline(L_2, __this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 113));
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_DirectorModule + 114));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector__ctor_mAC3BE2F955641A2D384EAE3235FFBF8ECFD27E3B (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayableDirector__ctor_mAC3BE2F955641A2D384EAE3235FFBF8ECFD27E3B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PlayableDirector__ctor_mAC3BE2F955641A2D384EAE3235FFBF8ECFD27E3B_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E* ___0_frameRate, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, FrameRate_t57F62C304A9ED1D60D64D5B7D4B7D4F0FC30964E*);
	static PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::PlayOnFrame_Injected(UnityEngine.Playables.FrameRate&)");
	_il2cpp_icall_func(__this, ___0_frameRate);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2* ___0_id, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2*);
	static PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_ClearReferenceValue_Injected_m9955C0A6558AA830976F3072700092D374A79A8B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::ClearReferenceValue_Injected(UnityEngine.PropertyName&)");
	_il2cpp_icall_func(__this, ___0_id);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2* ___0_id, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_value, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2*, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*);
	static PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_SetReferenceValue_Injected_m62E8502787C5A05B1361424D93C3AE7DB9646C43_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::SetReferenceValue_Injected(UnityEngine.PropertyName&,UnityEngine.Object)");
	_il2cpp_icall_func(__this, ___0_id, ___1_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106 (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2* ___0_id, bool* ___1_idValid, const RuntimeMethod* method) 
{
	typedef Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* (*PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, PropertyName_tE4B4AAA58AF3BF2C0CD95509EB7B786F096901C2*, bool*);
	static PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::GetReferenceValue_Injected(UnityEngine.PropertyName&,System.Boolean&)");
	Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* icallRetVal = _il2cpp_icall_func(__this, ___0_id, ___1_idValid);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475* __this, PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC_ftn) (PlayableDirector_t895D7BC3CFBFFD823278F438EAC4AA91DBFEC475*, PlayableGraph_t4A5B0B45343A240F0761574FD7C672E0CFFF7A6E*);
	static PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Playables.PlayableDirector::GetGraphHandle_Injected(UnityEngine.Playables.PlayableGraph&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_m5A038831CEB84A7E374FE59D43444412629F833F_gshared_inline (Action_1_t923A20D1D4F6B55B2ED5AE21B90F1A0CE0450D99* __this, Il2CppFullySharedGenericAny ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, Il2CppFullySharedGenericAny, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
