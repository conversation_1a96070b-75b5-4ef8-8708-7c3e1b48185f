﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[13] = 
{
	{ 30139, 0,  19 },
	{ 30139, 1,  20 },
	{ 30142, 2,  22 },
	{ 30139, 0,  22 },
	{ 30909, 3,  26 },
	{ 18823, 4,  26 },
	{ 28902, 5,  30 },
	{ 28902, 5,  34 },
	{ 28902, 5,  36 },
	{ 31459, 5,  39 },
	{ 31459, 5,  41 },
	{ 31459, 5,  44 },
	{ 18823, 6,  54 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[7] = 
{
	"error",
	"textGenerationError",
	"validSettings",
	"uerror",
	"res",
	"old",
	"isFileName",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[166] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 1, 1 },
	{ 0, 0 },
	{ 2, 2 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 4, 2 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 6, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 7, 1 },
	{ 0, 0 },
	{ 8, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 9, 1 },
	{ 0, 0 },
	{ 10, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 11, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 12, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_TextRenderingModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_TextRenderingModule[796] = 
{
	{ 108518, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 108518, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 108518, 1, 12, 12, 38, 39, 0, kSequencePointKind_Normal, 0, 2 },
	{ 108518, 1, 12, 12, 40, 121, 1, kSequencePointKind_Normal, 0, 3 },
	{ 108518, 1, 12, 12, 40, 121, 6, kSequencePointKind_StepOut, 0, 4 },
	{ 108519, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 5 },
	{ 108519, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 6 },
	{ 108519, 1, 18, 18, 13, 14, 0, kSequencePointKind_Normal, 0, 7 },
	{ 108519, 1, 19, 19, 17, 34, 1, kSequencePointKind_Normal, 0, 8 },
	{ 108519, 1, 19, 19, 17, 34, 1, kSequencePointKind_StepOut, 0, 9 },
	{ 108519, 1, 20, 20, 17, 30, 7, kSequencePointKind_Normal, 0, 10 },
	{ 108519, 1, 21, 21, 13, 14, 11, kSequencePointKind_Normal, 0, 11 },
	{ 108520, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 108520, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 108520, 1, 22, 22, 17, 18, 0, kSequencePointKind_Normal, 0, 14 },
	{ 108520, 1, 22, 22, 19, 36, 1, kSequencePointKind_Normal, 0, 15 },
	{ 108520, 1, 22, 22, 19, 36, 1, kSequencePointKind_StepOut, 0, 16 },
	{ 108520, 1, 22, 22, 37, 38, 7, kSequencePointKind_Normal, 0, 17 },
	{ 108521, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 18 },
	{ 108521, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 19 },
	{ 108521, 1, 29, 29, 13, 14, 0, kSequencePointKind_Normal, 0, 20 },
	{ 108521, 1, 30, 30, 17, 34, 1, kSequencePointKind_Normal, 0, 21 },
	{ 108521, 1, 30, 30, 17, 34, 1, kSequencePointKind_StepOut, 0, 22 },
	{ 108521, 1, 31, 31, 17, 29, 7, kSequencePointKind_Normal, 0, 23 },
	{ 108521, 1, 32, 32, 13, 14, 11, kSequencePointKind_Normal, 0, 24 },
	{ 108522, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 25 },
	{ 108522, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 26 },
	{ 108522, 1, 33, 33, 17, 18, 0, kSequencePointKind_Normal, 0, 27 },
	{ 108522, 1, 33, 33, 19, 36, 1, kSequencePointKind_Normal, 0, 28 },
	{ 108522, 1, 33, 33, 19, 36, 1, kSequencePointKind_StepOut, 0, 29 },
	{ 108522, 1, 33, 33, 37, 38, 7, kSequencePointKind_Normal, 0, 30 },
	{ 108523, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 31 },
	{ 108523, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 32 },
	{ 108523, 1, 40, 40, 13, 14, 0, kSequencePointKind_Normal, 0, 33 },
	{ 108523, 1, 41, 41, 17, 34, 1, kSequencePointKind_Normal, 0, 34 },
	{ 108523, 1, 41, 41, 17, 34, 1, kSequencePointKind_StepOut, 0, 35 },
	{ 108523, 1, 42, 42, 17, 29, 7, kSequencePointKind_Normal, 0, 36 },
	{ 108523, 1, 43, 43, 13, 14, 11, kSequencePointKind_Normal, 0, 37 },
	{ 108524, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 38 },
	{ 108524, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 39 },
	{ 108524, 1, 44, 44, 17, 18, 0, kSequencePointKind_Normal, 0, 40 },
	{ 108524, 1, 44, 44, 19, 36, 1, kSequencePointKind_Normal, 0, 41 },
	{ 108524, 1, 44, 44, 19, 36, 1, kSequencePointKind_StepOut, 0, 42 },
	{ 108524, 1, 44, 44, 37, 38, 7, kSequencePointKind_Normal, 0, 43 },
	{ 108525, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 44 },
	{ 108525, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 45 },
	{ 108525, 1, 51, 51, 13, 14, 0, kSequencePointKind_Normal, 0, 46 },
	{ 108525, 1, 52, 52, 17, 34, 1, kSequencePointKind_Normal, 0, 47 },
	{ 108525, 1, 52, 52, 17, 34, 1, kSequencePointKind_StepOut, 0, 48 },
	{ 108525, 1, 53, 53, 17, 26, 7, kSequencePointKind_Normal, 0, 49 },
	{ 108525, 1, 54, 54, 13, 14, 11, kSequencePointKind_Normal, 0, 50 },
	{ 108526, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 51 },
	{ 108526, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 52 },
	{ 108526, 1, 55, 55, 17, 18, 0, kSequencePointKind_Normal, 0, 53 },
	{ 108526, 1, 55, 55, 19, 36, 1, kSequencePointKind_Normal, 0, 54 },
	{ 108526, 1, 55, 55, 19, 36, 1, kSequencePointKind_StepOut, 0, 55 },
	{ 108526, 1, 55, 55, 37, 38, 7, kSequencePointKind_Normal, 0, 56 },
	{ 108527, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 57 },
	{ 108527, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 58 },
	{ 108527, 1, 62, 62, 13, 14, 0, kSequencePointKind_Normal, 0, 59 },
	{ 108527, 1, 63, 63, 17, 34, 1, kSequencePointKind_Normal, 0, 60 },
	{ 108527, 1, 63, 63, 17, 34, 1, kSequencePointKind_StepOut, 0, 61 },
	{ 108527, 1, 64, 64, 17, 26, 7, kSequencePointKind_Normal, 0, 62 },
	{ 108527, 1, 65, 65, 13, 14, 11, kSequencePointKind_Normal, 0, 63 },
	{ 108528, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 64 },
	{ 108528, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 65 },
	{ 108528, 1, 66, 66, 17, 18, 0, kSequencePointKind_Normal, 0, 66 },
	{ 108528, 1, 66, 66, 19, 36, 1, kSequencePointKind_Normal, 0, 67 },
	{ 108528, 1, 66, 66, 19, 36, 1, kSequencePointKind_StepOut, 0, 68 },
	{ 108528, 1, 66, 66, 37, 38, 7, kSequencePointKind_Normal, 0, 69 },
	{ 108529, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 70 },
	{ 108529, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 71 },
	{ 108529, 1, 73, 73, 13, 14, 0, kSequencePointKind_Normal, 0, 72 },
	{ 108529, 1, 74, 74, 17, 34, 1, kSequencePointKind_Normal, 0, 73 },
	{ 108529, 1, 74, 74, 17, 34, 1, kSequencePointKind_StepOut, 0, 74 },
	{ 108529, 1, 75, 75, 17, 29, 7, kSequencePointKind_Normal, 0, 75 },
	{ 108529, 1, 76, 76, 13, 14, 15, kSequencePointKind_Normal, 0, 76 },
	{ 108530, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 77 },
	{ 108530, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 78 },
	{ 108530, 1, 77, 77, 17, 18, 0, kSequencePointKind_Normal, 0, 79 },
	{ 108530, 1, 77, 77, 19, 36, 1, kSequencePointKind_Normal, 0, 80 },
	{ 108530, 1, 77, 77, 19, 36, 1, kSequencePointKind_StepOut, 0, 81 },
	{ 108530, 1, 77, 77, 37, 38, 7, kSequencePointKind_Normal, 0, 82 },
	{ 108531, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 83 },
	{ 108531, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 84 },
	{ 108531, 1, 84, 84, 13, 14, 0, kSequencePointKind_Normal, 0, 85 },
	{ 108531, 1, 85, 85, 17, 34, 1, kSequencePointKind_Normal, 0, 86 },
	{ 108531, 1, 85, 85, 17, 34, 1, kSequencePointKind_StepOut, 0, 87 },
	{ 108531, 1, 86, 86, 17, 29, 7, kSequencePointKind_Normal, 0, 88 },
	{ 108531, 1, 87, 87, 13, 14, 15, kSequencePointKind_Normal, 0, 89 },
	{ 108532, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 90 },
	{ 108532, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 91 },
	{ 108532, 1, 88, 88, 17, 18, 0, kSequencePointKind_Normal, 0, 92 },
	{ 108532, 1, 88, 88, 19, 36, 1, kSequencePointKind_Normal, 0, 93 },
	{ 108532, 1, 88, 88, 19, 36, 1, kSequencePointKind_StepOut, 0, 94 },
	{ 108532, 1, 88, 88, 37, 38, 7, kSequencePointKind_Normal, 0, 95 },
	{ 108533, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 96 },
	{ 108533, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 97 },
	{ 108533, 1, 95, 95, 13, 14, 0, kSequencePointKind_Normal, 0, 98 },
	{ 108533, 1, 96, 96, 17, 34, 1, kSequencePointKind_Normal, 0, 99 },
	{ 108533, 1, 96, 96, 17, 34, 1, kSequencePointKind_StepOut, 0, 100 },
	{ 108533, 1, 97, 97, 17, 26, 7, kSequencePointKind_Normal, 0, 101 },
	{ 108533, 1, 98, 98, 13, 14, 11, kSequencePointKind_Normal, 0, 102 },
	{ 108534, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 103 },
	{ 108534, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 104 },
	{ 108534, 1, 99, 99, 17, 18, 0, kSequencePointKind_Normal, 0, 105 },
	{ 108534, 1, 99, 99, 19, 36, 1, kSequencePointKind_Normal, 0, 106 },
	{ 108534, 1, 99, 99, 19, 36, 1, kSequencePointKind_StepOut, 0, 107 },
	{ 108534, 1, 99, 99, 37, 38, 7, kSequencePointKind_Normal, 0, 108 },
	{ 108535, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 109 },
	{ 108535, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 110 },
	{ 108535, 1, 106, 106, 13, 14, 0, kSequencePointKind_Normal, 0, 111 },
	{ 108535, 1, 107, 107, 17, 34, 1, kSequencePointKind_Normal, 0, 112 },
	{ 108535, 1, 107, 107, 17, 34, 1, kSequencePointKind_StepOut, 0, 113 },
	{ 108535, 1, 108, 108, 17, 26, 7, kSequencePointKind_Normal, 0, 114 },
	{ 108535, 1, 109, 109, 13, 14, 11, kSequencePointKind_Normal, 0, 115 },
	{ 108536, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 116 },
	{ 108536, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 117 },
	{ 108536, 1, 110, 110, 17, 18, 0, kSequencePointKind_Normal, 0, 118 },
	{ 108536, 1, 110, 110, 19, 36, 1, kSequencePointKind_Normal, 0, 119 },
	{ 108536, 1, 110, 110, 19, 36, 1, kSequencePointKind_StepOut, 0, 120 },
	{ 108536, 1, 110, 110, 37, 38, 7, kSequencePointKind_Normal, 0, 121 },
	{ 108537, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 122 },
	{ 108537, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 123 },
	{ 108537, 1, 117, 117, 13, 14, 0, kSequencePointKind_Normal, 0, 124 },
	{ 108537, 1, 118, 118, 17, 34, 1, kSequencePointKind_Normal, 0, 125 },
	{ 108537, 1, 118, 118, 17, 34, 1, kSequencePointKind_StepOut, 0, 126 },
	{ 108537, 1, 119, 119, 17, 30, 7, kSequencePointKind_Normal, 0, 127 },
	{ 108537, 1, 120, 120, 13, 14, 11, kSequencePointKind_Normal, 0, 128 },
	{ 108538, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 129 },
	{ 108538, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 130 },
	{ 108538, 1, 121, 121, 17, 18, 0, kSequencePointKind_Normal, 0, 131 },
	{ 108538, 1, 121, 121, 19, 36, 1, kSequencePointKind_Normal, 0, 132 },
	{ 108538, 1, 121, 121, 19, 36, 1, kSequencePointKind_StepOut, 0, 133 },
	{ 108538, 1, 121, 121, 37, 38, 7, kSequencePointKind_Normal, 0, 134 },
	{ 108539, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 135 },
	{ 108539, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 136 },
	{ 108539, 1, 128, 128, 13, 14, 0, kSequencePointKind_Normal, 0, 137 },
	{ 108539, 1, 129, 129, 17, 34, 1, kSequencePointKind_Normal, 0, 138 },
	{ 108539, 1, 129, 129, 17, 34, 1, kSequencePointKind_StepOut, 0, 139 },
	{ 108539, 1, 130, 130, 17, 52, 7, kSequencePointKind_Normal, 0, 140 },
	{ 108539, 1, 130, 130, 17, 52, 22, kSequencePointKind_StepOut, 0, 141 },
	{ 108539, 1, 131, 131, 13, 14, 30, kSequencePointKind_Normal, 0, 142 },
	{ 108540, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 143 },
	{ 108540, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 144 },
	{ 108540, 1, 132, 132, 17, 18, 0, kSequencePointKind_Normal, 0, 145 },
	{ 108540, 1, 132, 132, 19, 36, 1, kSequencePointKind_Normal, 0, 146 },
	{ 108540, 1, 132, 132, 19, 36, 1, kSequencePointKind_StepOut, 0, 147 },
	{ 108540, 1, 132, 132, 37, 38, 7, kSequencePointKind_Normal, 0, 148 },
	{ 108541, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 149 },
	{ 108541, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 150 },
	{ 108541, 1, 139, 139, 13, 14, 0, kSequencePointKind_Normal, 0, 151 },
	{ 108541, 1, 140, 140, 17, 34, 1, kSequencePointKind_Normal, 0, 152 },
	{ 108541, 1, 140, 140, 17, 34, 1, kSequencePointKind_StepOut, 0, 153 },
	{ 108541, 1, 141, 141, 17, 48, 7, kSequencePointKind_Normal, 0, 154 },
	{ 108541, 1, 141, 141, 17, 48, 17, kSequencePointKind_StepOut, 0, 155 },
	{ 108541, 1, 142, 142, 13, 14, 25, kSequencePointKind_Normal, 0, 156 },
	{ 108542, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 157 },
	{ 108542, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 158 },
	{ 108542, 1, 143, 143, 17, 18, 0, kSequencePointKind_Normal, 0, 159 },
	{ 108542, 1, 143, 143, 19, 36, 1, kSequencePointKind_Normal, 0, 160 },
	{ 108542, 1, 143, 143, 19, 36, 1, kSequencePointKind_StepOut, 0, 161 },
	{ 108542, 1, 143, 143, 37, 38, 7, kSequencePointKind_Normal, 0, 162 },
	{ 108544, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 163 },
	{ 108544, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 164 },
	{ 108544, 2, 49, 49, 9, 10, 0, kSequencePointKind_Normal, 0, 165 },
	{ 108544, 2, 50, 53, 13, 57, 1, kSequencePointKind_Normal, 0, 166 },
	{ 108544, 2, 50, 53, 13, 57, 13, kSequencePointKind_StepOut, 0, 167 },
	{ 108544, 2, 50, 53, 13, 57, 32, kSequencePointKind_StepOut, 0, 168 },
	{ 108544, 2, 50, 53, 13, 57, 51, kSequencePointKind_StepOut, 0, 169 },
	{ 108544, 2, 50, 53, 13, 57, 70, kSequencePointKind_StepOut, 0, 170 },
	{ 108544, 2, 54, 54, 9, 10, 81, kSequencePointKind_Normal, 0, 171 },
	{ 108545, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 172 },
	{ 108545, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 173 },
	{ 108545, 2, 57, 57, 9, 10, 0, kSequencePointKind_Normal, 0, 174 },
	{ 108545, 2, 58, 58, 13, 97, 1, kSequencePointKind_Normal, 0, 175 },
	{ 108545, 2, 58, 58, 13, 97, 13, kSequencePointKind_StepOut, 0, 176 },
	{ 108545, 2, 58, 58, 13, 97, 32, kSequencePointKind_StepOut, 0, 177 },
	{ 108545, 2, 59, 59, 9, 10, 43, kSequencePointKind_Normal, 0, 178 },
	{ 108546, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 179 },
	{ 108546, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 180 },
	{ 108546, 2, 62, 62, 9, 10, 0, kSequencePointKind_Normal, 0, 181 },
	{ 108546, 2, 63, 79, 13, 39, 1, kSequencePointKind_Normal, 0, 182 },
	{ 108546, 2, 63, 79, 13, 39, 14, kSequencePointKind_StepOut, 0, 183 },
	{ 108546, 2, 63, 79, 13, 39, 53, kSequencePointKind_StepOut, 0, 184 },
	{ 108546, 2, 63, 79, 13, 39, 109, kSequencePointKind_StepOut, 0, 185 },
	{ 108546, 2, 63, 79, 13, 39, 253, kSequencePointKind_StepOut, 0, 186 },
	{ 108546, 2, 63, 79, 13, 39, 273, kSequencePointKind_StepOut, 0, 187 },
	{ 108546, 2, 63, 79, 13, 39, 292, kSequencePointKind_StepOut, 0, 188 },
	{ 108546, 2, 80, 80, 9, 10, 303, kSequencePointKind_Normal, 0, 189 },
	{ 108547, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 190 },
	{ 108547, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 191 },
	{ 108547, 2, 90, 90, 15, 23, 0, kSequencePointKind_Normal, 0, 192 },
	{ 108547, 2, 90, 90, 15, 23, 3, kSequencePointKind_StepOut, 0, 193 },
	{ 108547, 2, 91, 91, 9, 10, 9, kSequencePointKind_Normal, 0, 194 },
	{ 108547, 2, 91, 91, 10, 11, 10, kSequencePointKind_Normal, 0, 195 },
	{ 108548, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 196 },
	{ 108548, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 197 },
	{ 108548, 2, 93, 93, 9, 50, 0, kSequencePointKind_Normal, 0, 198 },
	{ 108548, 2, 93, 93, 9, 50, 1, kSequencePointKind_StepOut, 0, 199 },
	{ 108548, 2, 94, 94, 9, 10, 7, kSequencePointKind_Normal, 0, 200 },
	{ 108548, 2, 95, 95, 13, 39, 8, kSequencePointKind_Normal, 0, 201 },
	{ 108548, 2, 95, 95, 13, 39, 9, kSequencePointKind_StepOut, 0, 202 },
	{ 108548, 2, 96, 96, 13, 69, 19, kSequencePointKind_Normal, 0, 203 },
	{ 108548, 2, 96, 96, 13, 69, 25, kSequencePointKind_StepOut, 0, 204 },
	{ 108548, 2, 97, 97, 13, 70, 35, kSequencePointKind_Normal, 0, 205 },
	{ 108548, 2, 97, 97, 13, 70, 39, kSequencePointKind_StepOut, 0, 206 },
	{ 108548, 2, 98, 98, 13, 48, 49, kSequencePointKind_Normal, 0, 207 },
	{ 108548, 2, 98, 98, 13, 48, 52, kSequencePointKind_StepOut, 0, 208 },
	{ 108548, 2, 106, 106, 9, 10, 62, kSequencePointKind_Normal, 0, 209 },
	{ 108549, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 210 },
	{ 108549, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 211 },
	{ 108549, 2, 109, 109, 9, 10, 0, kSequencePointKind_Normal, 0, 212 },
	{ 108549, 2, 109, 109, 9, 10, 1, kSequencePointKind_Normal, 0, 213 },
	{ 108549, 2, 110, 110, 13, 43, 2, kSequencePointKind_Normal, 0, 214 },
	{ 108549, 2, 110, 110, 13, 43, 3, kSequencePointKind_StepOut, 0, 215 },
	{ 108549, 2, 111, 111, 9, 10, 11, kSequencePointKind_Normal, 0, 216 },
	{ 108549, 2, 111, 111, 9, 10, 12, kSequencePointKind_StepOut, 0, 217 },
	{ 108549, 2, 111, 111, 9, 10, 19, kSequencePointKind_Normal, 0, 218 },
	{ 108550, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 219 },
	{ 108550, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 220 },
	{ 108550, 2, 114, 114, 9, 10, 0, kSequencePointKind_Normal, 0, 221 },
	{ 108550, 2, 122, 122, 13, 38, 1, kSequencePointKind_Normal, 0, 222 },
	{ 108550, 2, 122, 122, 13, 38, 12, kSequencePointKind_StepOut, 0, 223 },
	{ 108550, 2, 122, 122, 0, 0, 18, kSequencePointKind_Normal, 0, 224 },
	{ 108550, 2, 123, 123, 13, 14, 21, kSequencePointKind_Normal, 0, 225 },
	{ 108550, 2, 124, 124, 17, 41, 22, kSequencePointKind_Normal, 0, 226 },
	{ 108550, 2, 124, 124, 17, 41, 28, kSequencePointKind_StepOut, 0, 227 },
	{ 108550, 2, 125, 125, 17, 37, 34, kSequencePointKind_Normal, 0, 228 },
	{ 108550, 2, 126, 126, 13, 14, 45, kSequencePointKind_Normal, 0, 229 },
	{ 108550, 2, 127, 127, 9, 10, 46, kSequencePointKind_Normal, 0, 230 },
	{ 108551, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 231 },
	{ 108551, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 232 },
	{ 108551, 2, 129, 129, 45, 63, 0, kSequencePointKind_Normal, 0, 233 },
	{ 108551, 2, 129, 129, 45, 63, 1, kSequencePointKind_StepOut, 0, 234 },
	{ 108552, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 235 },
	{ 108552, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 236 },
	{ 108552, 2, 149, 149, 9, 10, 0, kSequencePointKind_Normal, 0, 237 },
	{ 108552, 2, 150, 150, 13, 64, 1, kSequencePointKind_Normal, 0, 238 },
	{ 108552, 2, 150, 150, 13, 64, 8, kSequencePointKind_StepOut, 0, 239 },
	{ 108552, 2, 150, 150, 13, 64, 21, kSequencePointKind_StepOut, 0, 240 },
	{ 108552, 2, 150, 150, 0, 0, 30, kSequencePointKind_Normal, 0, 241 },
	{ 108552, 2, 151, 151, 17, 33, 33, kSequencePointKind_Normal, 0, 242 },
	{ 108552, 2, 153, 153, 13, 82, 40, kSequencePointKind_Normal, 0, 243 },
	{ 108552, 2, 153, 153, 0, 0, 61, kSequencePointKind_Normal, 0, 244 },
	{ 108552, 2, 154, 154, 13, 14, 64, kSequencePointKind_Normal, 0, 245 },
	{ 108552, 2, 155, 155, 17, 43, 65, kSequencePointKind_Normal, 0, 246 },
	{ 108552, 2, 155, 155, 17, 43, 72, kSequencePointKind_StepOut, 0, 247 },
	{ 108552, 2, 155, 155, 0, 0, 78, kSequencePointKind_Normal, 0, 248 },
	{ 108552, 2, 156, 156, 21, 177, 81, kSequencePointKind_Normal, 0, 249 },
	{ 108552, 2, 156, 156, 21, 177, 106, kSequencePointKind_StepOut, 0, 250 },
	{ 108552, 2, 156, 156, 21, 177, 112, kSequencePointKind_StepOut, 0, 251 },
	{ 108552, 2, 157, 157, 17, 39, 118, kSequencePointKind_Normal, 0, 252 },
	{ 108552, 2, 158, 158, 17, 55, 126, kSequencePointKind_Normal, 0, 253 },
	{ 108552, 2, 159, 159, 13, 14, 134, kSequencePointKind_Normal, 0, 254 },
	{ 108552, 2, 161, 161, 13, 47, 135, kSequencePointKind_Normal, 0, 255 },
	{ 108552, 2, 161, 161, 0, 0, 143, kSequencePointKind_Normal, 0, 256 },
	{ 108552, 2, 162, 162, 13, 14, 147, kSequencePointKind_Normal, 0, 257 },
	{ 108552, 2, 163, 163, 17, 43, 148, kSequencePointKind_Normal, 0, 258 },
	{ 108552, 2, 163, 163, 17, 43, 155, kSequencePointKind_StepOut, 0, 259 },
	{ 108552, 2, 163, 163, 0, 0, 162, kSequencePointKind_Normal, 0, 260 },
	{ 108552, 2, 164, 164, 21, 154, 166, kSequencePointKind_Normal, 0, 261 },
	{ 108552, 2, 164, 164, 21, 154, 191, kSequencePointKind_StepOut, 0, 262 },
	{ 108552, 2, 164, 164, 21, 154, 197, kSequencePointKind_StepOut, 0, 263 },
	{ 108552, 2, 165, 165, 17, 55, 203, kSequencePointKind_Normal, 0, 264 },
	{ 108552, 2, 166, 166, 13, 14, 211, kSequencePointKind_Normal, 0, 265 },
	{ 108552, 2, 167, 167, 13, 29, 212, kSequencePointKind_Normal, 0, 266 },
	{ 108552, 2, 168, 168, 9, 10, 216, kSequencePointKind_Normal, 0, 267 },
	{ 108553, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 268 },
	{ 108553, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 269 },
	{ 108553, 2, 171, 171, 9, 10, 0, kSequencePointKind_Normal, 0, 270 },
	{ 108553, 2, 172, 172, 13, 36, 1, kSequencePointKind_Normal, 0, 271 },
	{ 108553, 2, 173, 173, 9, 10, 8, kSequencePointKind_Normal, 0, 272 },
	{ 108554, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 273 },
	{ 108554, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 274 },
	{ 108554, 2, 176, 176, 9, 10, 0, kSequencePointKind_Normal, 0, 275 },
	{ 108554, 2, 177, 177, 13, 47, 1, kSequencePointKind_Normal, 0, 276 },
	{ 108554, 2, 177, 177, 13, 47, 3, kSequencePointKind_StepOut, 0, 277 },
	{ 108554, 2, 178, 178, 9, 10, 9, kSequencePointKind_Normal, 0, 278 },
	{ 108555, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 279 },
	{ 108555, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 280 },
	{ 108555, 2, 181, 181, 9, 10, 0, kSequencePointKind_Normal, 0, 281 },
	{ 108555, 2, 182, 182, 13, 37, 1, kSequencePointKind_Normal, 0, 282 },
	{ 108555, 2, 182, 182, 13, 37, 3, kSequencePointKind_StepOut, 0, 283 },
	{ 108555, 2, 183, 183, 9, 10, 9, kSequencePointKind_Normal, 0, 284 },
	{ 108556, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 285 },
	{ 108556, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 286 },
	{ 108556, 2, 186, 186, 9, 10, 0, kSequencePointKind_Normal, 0, 287 },
	{ 108556, 2, 187, 187, 13, 43, 1, kSequencePointKind_Normal, 0, 288 },
	{ 108556, 2, 187, 187, 13, 43, 3, kSequencePointKind_StepOut, 0, 289 },
	{ 108556, 2, 188, 188, 9, 10, 9, kSequencePointKind_Normal, 0, 290 },
	{ 108557, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 291 },
	{ 108557, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 292 },
	{ 108557, 2, 191, 191, 9, 10, 0, kSequencePointKind_Normal, 0, 293 },
	{ 108557, 2, 192, 192, 13, 71, 1, kSequencePointKind_Normal, 0, 294 },
	{ 108557, 2, 193, 193, 13, 67, 9, kSequencePointKind_Normal, 0, 295 },
	{ 108557, 2, 194, 194, 13, 42, 17, kSequencePointKind_Normal, 0, 296 },
	{ 108557, 2, 195, 195, 13, 37, 25, kSequencePointKind_Normal, 0, 297 },
	{ 108557, 2, 195, 195, 13, 37, 28, kSequencePointKind_StepOut, 0, 298 },
	{ 108557, 2, 196, 196, 13, 38, 34, kSequencePointKind_Normal, 0, 299 },
	{ 108557, 2, 196, 196, 13, 38, 35, kSequencePointKind_StepOut, 0, 300 },
	{ 108557, 2, 196, 196, 13, 38, 43, kSequencePointKind_StepOut, 0, 301 },
	{ 108557, 2, 197, 197, 9, 10, 51, kSequencePointKind_Normal, 0, 302 },
	{ 108558, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 303 },
	{ 108558, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 304 },
	{ 108558, 2, 200, 200, 9, 10, 0, kSequencePointKind_Normal, 0, 305 },
	{ 108558, 2, 201, 201, 13, 67, 1, kSequencePointKind_Normal, 0, 306 },
	{ 108558, 2, 202, 202, 13, 42, 9, kSequencePointKind_Normal, 0, 307 },
	{ 108558, 2, 203, 203, 13, 37, 17, kSequencePointKind_Normal, 0, 308 },
	{ 108558, 2, 203, 203, 13, 37, 20, kSequencePointKind_StepOut, 0, 309 },
	{ 108558, 2, 204, 204, 13, 39, 26, kSequencePointKind_Normal, 0, 310 },
	{ 108558, 2, 204, 204, 13, 39, 27, kSequencePointKind_StepOut, 0, 311 },
	{ 108558, 2, 204, 204, 13, 39, 35, kSequencePointKind_StepOut, 0, 312 },
	{ 108558, 2, 205, 205, 9, 10, 43, kSequencePointKind_Normal, 0, 313 },
	{ 108559, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 314 },
	{ 108559, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 315 },
	{ 108559, 2, 208, 208, 9, 10, 0, kSequencePointKind_Normal, 0, 316 },
	{ 108559, 2, 209, 209, 13, 58, 1, kSequencePointKind_Normal, 0, 317 },
	{ 108559, 2, 209, 209, 13, 58, 4, kSequencePointKind_StepOut, 0, 318 },
	{ 108559, 2, 210, 210, 13, 51, 10, kSequencePointKind_Normal, 0, 319 },
	{ 108559, 2, 210, 210, 0, 0, 15, kSequencePointKind_Normal, 0, 320 },
	{ 108559, 2, 211, 211, 17, 29, 18, kSequencePointKind_Normal, 0, 321 },
	{ 108559, 2, 212, 212, 13, 79, 22, kSequencePointKind_Normal, 0, 322 },
	{ 108559, 2, 212, 212, 0, 0, 29, kSequencePointKind_Normal, 0, 323 },
	{ 108559, 2, 213, 213, 17, 131, 32, kSequencePointKind_Normal, 0, 324 },
	{ 108559, 2, 213, 213, 17, 131, 53, kSequencePointKind_StepOut, 0, 325 },
	{ 108559, 2, 214, 214, 13, 80, 59, kSequencePointKind_Normal, 0, 326 },
	{ 108559, 2, 214, 214, 0, 0, 67, kSequencePointKind_Normal, 0, 327 },
	{ 108559, 2, 215, 215, 17, 132, 71, kSequencePointKind_Normal, 0, 328 },
	{ 108559, 2, 215, 215, 17, 132, 92, kSequencePointKind_StepOut, 0, 329 },
	{ 108559, 2, 218, 218, 13, 26, 98, kSequencePointKind_Normal, 0, 330 },
	{ 108559, 2, 219, 219, 9, 10, 102, kSequencePointKind_Normal, 0, 331 },
	{ 108560, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 332 },
	{ 108560, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 333 },
	{ 108560, 2, 222, 222, 9, 10, 0, kSequencePointKind_Normal, 0, 334 },
	{ 108560, 2, 223, 223, 13, 72, 1, kSequencePointKind_Normal, 0, 335 },
	{ 108560, 2, 223, 223, 13, 72, 4, kSequencePointKind_StepOut, 0, 336 },
	{ 108560, 2, 224, 224, 13, 68, 10, kSequencePointKind_Normal, 0, 337 },
	{ 108560, 2, 225, 225, 9, 10, 17, kSequencePointKind_Normal, 0, 338 },
	{ 108561, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 339 },
	{ 108561, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 340 },
	{ 108561, 2, 228, 228, 9, 10, 0, kSequencePointKind_Normal, 0, 341 },
	{ 108561, 2, 229, 229, 13, 90, 1, kSequencePointKind_Normal, 0, 342 },
	{ 108561, 2, 229, 229, 13, 90, 16, kSequencePointKind_StepOut, 0, 343 },
	{ 108561, 2, 229, 229, 13, 90, 31, kSequencePointKind_StepOut, 0, 344 },
	{ 108561, 2, 229, 229, 0, 0, 40, kSequencePointKind_Normal, 0, 345 },
	{ 108561, 2, 230, 230, 17, 36, 43, kSequencePointKind_Normal, 0, 346 },
	{ 108561, 2, 232, 232, 13, 57, 52, kSequencePointKind_Normal, 0, 347 },
	{ 108561, 2, 232, 232, 13, 57, 56, kSequencePointKind_StepOut, 0, 348 },
	{ 108561, 2, 233, 233, 13, 32, 66, kSequencePointKind_Normal, 0, 349 },
	{ 108561, 2, 234, 234, 9, 10, 75, kSequencePointKind_Normal, 0, 350 },
	{ 108562, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 351 },
	{ 108562, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 352 },
	{ 108562, 2, 237, 237, 9, 10, 0, kSequencePointKind_Normal, 0, 353 },
	{ 108562, 2, 238, 238, 13, 32, 1, kSequencePointKind_Normal, 0, 354 },
	{ 108562, 2, 239, 239, 13, 35, 8, kSequencePointKind_Normal, 0, 355 },
	{ 108562, 2, 240, 240, 13, 35, 15, kSequencePointKind_Normal, 0, 356 },
	{ 108562, 2, 241, 241, 13, 40, 22, kSequencePointKind_Normal, 0, 357 },
	{ 108562, 2, 242, 242, 13, 35, 29, kSequencePointKind_Normal, 0, 358 },
	{ 108562, 2, 243, 243, 13, 39, 36, kSequencePointKind_Normal, 0, 359 },
	{ 108562, 2, 245, 245, 13, 61, 43, kSequencePointKind_Normal, 0, 360 },
	{ 108562, 2, 245, 245, 13, 61, 45, kSequencePointKind_StepOut, 0, 361 },
	{ 108562, 2, 248, 253, 13, 115, 51, kSequencePointKind_Normal, 0, 362 },
	{ 108562, 2, 248, 253, 13, 115, 163, kSequencePointKind_StepOut, 0, 363 },
	{ 108562, 2, 254, 254, 13, 33, 169, kSequencePointKind_Normal, 0, 364 },
	{ 108562, 2, 255, 255, 13, 26, 176, kSequencePointKind_Normal, 0, 365 },
	{ 108562, 2, 256, 256, 9, 10, 180, kSequencePointKind_Normal, 0, 366 },
	{ 108563, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 367 },
	{ 108563, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 368 },
	{ 108563, 2, 261, 261, 13, 14, 0, kSequencePointKind_Normal, 0, 369 },
	{ 108563, 2, 262, 262, 17, 36, 1, kSequencePointKind_Normal, 0, 370 },
	{ 108563, 2, 262, 262, 0, 0, 11, kSequencePointKind_Normal, 0, 371 },
	{ 108563, 2, 263, 263, 17, 18, 14, kSequencePointKind_Normal, 0, 372 },
	{ 108563, 2, 264, 264, 21, 42, 15, kSequencePointKind_Normal, 0, 373 },
	{ 108563, 2, 264, 264, 21, 42, 22, kSequencePointKind_StepOut, 0, 374 },
	{ 108563, 2, 265, 265, 21, 42, 28, kSequencePointKind_Normal, 0, 375 },
	{ 108563, 2, 266, 266, 17, 18, 35, kSequencePointKind_Normal, 0, 376 },
	{ 108563, 2, 267, 267, 17, 32, 36, kSequencePointKind_Normal, 0, 377 },
	{ 108563, 2, 268, 268, 13, 14, 45, kSequencePointKind_Normal, 0, 378 },
	{ 108564, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 379 },
	{ 108564, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 380 },
	{ 108564, 2, 274, 274, 13, 14, 0, kSequencePointKind_Normal, 0, 381 },
	{ 108564, 2, 275, 275, 17, 41, 1, kSequencePointKind_Normal, 0, 382 },
	{ 108564, 2, 275, 275, 0, 0, 11, kSequencePointKind_Normal, 0, 383 },
	{ 108564, 2, 276, 276, 17, 18, 14, kSequencePointKind_Normal, 0, 384 },
	{ 108564, 2, 277, 277, 21, 49, 15, kSequencePointKind_Normal, 0, 385 },
	{ 108564, 2, 277, 277, 21, 49, 22, kSequencePointKind_StepOut, 0, 386 },
	{ 108564, 2, 278, 278, 21, 47, 28, kSequencePointKind_Normal, 0, 387 },
	{ 108564, 2, 279, 279, 17, 18, 35, kSequencePointKind_Normal, 0, 388 },
	{ 108564, 2, 280, 280, 17, 37, 36, kSequencePointKind_Normal, 0, 389 },
	{ 108564, 2, 281, 281, 13, 14, 45, kSequencePointKind_Normal, 0, 390 },
	{ 108565, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 391 },
	{ 108565, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 392 },
	{ 108565, 2, 287, 287, 13, 14, 0, kSequencePointKind_Normal, 0, 393 },
	{ 108565, 2, 288, 288, 17, 36, 1, kSequencePointKind_Normal, 0, 394 },
	{ 108565, 2, 288, 288, 0, 0, 11, kSequencePointKind_Normal, 0, 395 },
	{ 108565, 2, 289, 289, 17, 18, 14, kSequencePointKind_Normal, 0, 396 },
	{ 108565, 2, 290, 290, 21, 39, 15, kSequencePointKind_Normal, 0, 397 },
	{ 108565, 2, 290, 290, 21, 39, 22, kSequencePointKind_StepOut, 0, 398 },
	{ 108565, 2, 291, 291, 21, 42, 28, kSequencePointKind_Normal, 0, 399 },
	{ 108565, 2, 292, 292, 17, 18, 35, kSequencePointKind_Normal, 0, 400 },
	{ 108565, 2, 293, 293, 17, 32, 36, kSequencePointKind_Normal, 0, 401 },
	{ 108565, 2, 294, 294, 13, 14, 45, kSequencePointKind_Normal, 0, 402 },
	{ 108574, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 403 },
	{ 108574, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 404 },
	{ 108574, 3, 421, 421, 9, 10, 0, kSequencePointKind_Normal, 0, 405 },
	{ 108574, 3, 422, 422, 13, 30, 1, kSequencePointKind_Normal, 0, 406 },
	{ 108574, 3, 422, 422, 13, 30, 3, kSequencePointKind_StepOut, 0, 407 },
	{ 108574, 3, 422, 422, 0, 0, 9, kSequencePointKind_Normal, 0, 408 },
	{ 108574, 3, 423, 423, 13, 14, 12, kSequencePointKind_Normal, 0, 409 },
	{ 108574, 3, 424, 424, 17, 52, 13, kSequencePointKind_Normal, 0, 410 },
	{ 108574, 3, 425, 425, 17, 30, 17, kSequencePointKind_Normal, 0, 411 },
	{ 108574, 3, 428, 428, 13, 29, 21, kSequencePointKind_Normal, 0, 412 },
	{ 108574, 3, 429, 434, 13, 115, 23, kSequencePointKind_Normal, 0, 413 },
	{ 108574, 3, 429, 434, 13, 115, 85, kSequencePointKind_StepOut, 0, 414 },
	{ 108574, 3, 435, 435, 13, 49, 91, kSequencePointKind_Normal, 0, 415 },
	{ 108574, 3, 436, 436, 13, 24, 95, kSequencePointKind_Normal, 0, 416 },
	{ 108574, 3, 437, 437, 9, 10, 99, kSequencePointKind_Normal, 0, 417 },
	{ 108610, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 418 },
	{ 108610, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 419 },
	{ 108610, 3, 79, 79, 17, 18, 0, kSequencePointKind_Normal, 0, 420 },
	{ 108610, 3, 79, 79, 19, 80, 1, kSequencePointKind_Normal, 0, 421 },
	{ 108610, 3, 79, 79, 19, 80, 9, kSequencePointKind_StepOut, 0, 422 },
	{ 108610, 3, 79, 79, 81, 82, 18, kSequencePointKind_Normal, 0, 423 },
	{ 108611, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 424 },
	{ 108611, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 425 },
	{ 108611, 3, 80, 80, 17, 18, 0, kSequencePointKind_Normal, 0, 426 },
	{ 108611, 3, 80, 80, 19, 33, 1, kSequencePointKind_Normal, 0, 427 },
	{ 108611, 3, 80, 80, 34, 35, 9, kSequencePointKind_Normal, 0, 428 },
	{ 108612, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 429 },
	{ 108612, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 430 },
	{ 108612, 3, 85, 85, 17, 18, 0, kSequencePointKind_Normal, 0, 431 },
	{ 108612, 3, 85, 85, 19, 42, 1, kSequencePointKind_Normal, 0, 432 },
	{ 108612, 3, 85, 85, 19, 42, 7, kSequencePointKind_StepOut, 0, 433 },
	{ 108612, 3, 85, 85, 43, 44, 16, kSequencePointKind_Normal, 0, 434 },
	{ 108613, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 435 },
	{ 108613, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 436 },
	{ 108613, 3, 86, 86, 17, 18, 0, kSequencePointKind_Normal, 0, 437 },
	{ 108613, 3, 86, 86, 19, 38, 1, kSequencePointKind_Normal, 0, 438 },
	{ 108613, 3, 86, 86, 19, 38, 9, kSequencePointKind_StepOut, 0, 439 },
	{ 108613, 3, 86, 86, 39, 40, 15, kSequencePointKind_Normal, 0, 440 },
	{ 108614, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 441 },
	{ 108614, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 442 },
	{ 108614, 3, 91, 91, 17, 18, 0, kSequencePointKind_Normal, 0, 443 },
	{ 108614, 3, 91, 91, 19, 44, 1, kSequencePointKind_Normal, 0, 444 },
	{ 108614, 3, 91, 91, 19, 44, 7, kSequencePointKind_StepOut, 0, 445 },
	{ 108614, 3, 91, 91, 45, 46, 17, kSequencePointKind_Normal, 0, 446 },
	{ 108615, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 447 },
	{ 108615, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 448 },
	{ 108615, 3, 93, 93, 13, 14, 0, kSequencePointKind_Normal, 0, 449 },
	{ 108615, 3, 94, 94, 17, 39, 1, kSequencePointKind_Normal, 0, 450 },
	{ 108615, 3, 94, 94, 17, 39, 7, kSequencePointKind_StepOut, 0, 451 },
	{ 108615, 3, 95, 95, 17, 38, 13, kSequencePointKind_Normal, 0, 452 },
	{ 108615, 3, 95, 95, 17, 38, 22, kSequencePointKind_StepOut, 0, 453 },
	{ 108615, 3, 96, 96, 17, 45, 28, kSequencePointKind_Normal, 0, 454 },
	{ 108615, 3, 96, 96, 17, 45, 35, kSequencePointKind_StepOut, 0, 455 },
	{ 108615, 3, 96, 96, 17, 45, 47, kSequencePointKind_StepOut, 0, 456 },
	{ 108615, 3, 96, 96, 17, 45, 54, kSequencePointKind_StepOut, 0, 457 },
	{ 108615, 3, 97, 97, 13, 14, 60, kSequencePointKind_Normal, 0, 458 },
	{ 108616, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 459 },
	{ 108616, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 460 },
	{ 108616, 3, 102, 102, 17, 18, 0, kSequencePointKind_Normal, 0, 461 },
	{ 108616, 3, 102, 102, 19, 38, 1, kSequencePointKind_Normal, 0, 462 },
	{ 108616, 3, 102, 102, 19, 38, 7, kSequencePointKind_StepOut, 0, 463 },
	{ 108616, 3, 102, 102, 39, 40, 16, kSequencePointKind_Normal, 0, 464 },
	{ 108617, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 465 },
	{ 108617, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 466 },
	{ 108617, 3, 103, 103, 17, 18, 0, kSequencePointKind_Normal, 0, 467 },
	{ 108617, 3, 103, 103, 19, 34, 1, kSequencePointKind_Normal, 0, 468 },
	{ 108617, 3, 103, 103, 19, 34, 9, kSequencePointKind_StepOut, 0, 469 },
	{ 108617, 3, 103, 103, 35, 36, 15, kSequencePointKind_Normal, 0, 470 },
	{ 108618, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 471 },
	{ 108618, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 472 },
	{ 108618, 3, 108, 108, 17, 18, 0, kSequencePointKind_Normal, 0, 473 },
	{ 108618, 3, 108, 108, 19, 54, 1, kSequencePointKind_Normal, 0, 474 },
	{ 108618, 3, 108, 108, 19, 54, 7, kSequencePointKind_StepOut, 0, 475 },
	{ 108618, 3, 108, 108, 19, 54, 18, kSequencePointKind_StepOut, 0, 476 },
	{ 108618, 3, 108, 108, 55, 56, 28, kSequencePointKind_Normal, 0, 477 },
	{ 108619, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 478 },
	{ 108619, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 479 },
	{ 108619, 3, 109, 109, 17, 18, 0, kSequencePointKind_Normal, 0, 480 },
	{ 108619, 3, 109, 109, 19, 48, 1, kSequencePointKind_Normal, 0, 481 },
	{ 108619, 3, 109, 109, 19, 48, 15, kSequencePointKind_StepOut, 0, 482 },
	{ 108619, 3, 109, 109, 19, 48, 21, kSequencePointKind_StepOut, 0, 483 },
	{ 108619, 3, 109, 109, 49, 50, 27, kSequencePointKind_Normal, 0, 484 },
	{ 108620, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 485 },
	{ 108620, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 486 },
	{ 108620, 3, 114, 114, 17, 18, 0, kSequencePointKind_Normal, 0, 487 },
	{ 108620, 3, 114, 114, 19, 38, 1, kSequencePointKind_Normal, 0, 488 },
	{ 108620, 3, 114, 114, 19, 38, 7, kSequencePointKind_StepOut, 0, 489 },
	{ 108620, 3, 114, 114, 39, 40, 16, kSequencePointKind_Normal, 0, 490 },
	{ 108621, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 491 },
	{ 108621, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 492 },
	{ 108621, 3, 116, 116, 13, 14, 0, kSequencePointKind_Normal, 0, 493 },
	{ 108621, 3, 117, 117, 17, 34, 1, kSequencePointKind_Normal, 0, 494 },
	{ 108621, 3, 117, 117, 17, 34, 7, kSequencePointKind_StepOut, 0, 495 },
	{ 108621, 3, 118, 118, 17, 32, 13, kSequencePointKind_Normal, 0, 496 },
	{ 108621, 3, 118, 118, 17, 32, 21, kSequencePointKind_StepOut, 0, 497 },
	{ 108621, 3, 119, 119, 17, 45, 27, kSequencePointKind_Normal, 0, 498 },
	{ 108621, 3, 119, 119, 17, 45, 34, kSequencePointKind_StepOut, 0, 499 },
	{ 108621, 3, 119, 119, 17, 45, 46, kSequencePointKind_StepOut, 0, 500 },
	{ 108621, 3, 119, 119, 17, 45, 53, kSequencePointKind_StepOut, 0, 501 },
	{ 108621, 3, 120, 120, 13, 14, 59, kSequencePointKind_Normal, 0, 502 },
	{ 108622, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 503 },
	{ 108622, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 504 },
	{ 108622, 3, 125, 125, 17, 18, 0, kSequencePointKind_Normal, 0, 505 },
	{ 108622, 3, 125, 125, 19, 38, 1, kSequencePointKind_Normal, 0, 506 },
	{ 108622, 3, 125, 125, 19, 38, 7, kSequencePointKind_StepOut, 0, 507 },
	{ 108622, 3, 125, 125, 39, 40, 16, kSequencePointKind_Normal, 0, 508 },
	{ 108623, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 509 },
	{ 108623, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 510 },
	{ 108623, 3, 127, 127, 13, 14, 0, kSequencePointKind_Normal, 0, 511 },
	{ 108623, 3, 128, 128, 17, 34, 1, kSequencePointKind_Normal, 0, 512 },
	{ 108623, 3, 128, 128, 17, 34, 7, kSequencePointKind_StepOut, 0, 513 },
	{ 108623, 3, 129, 129, 17, 32, 13, kSequencePointKind_Normal, 0, 514 },
	{ 108623, 3, 129, 129, 17, 32, 21, kSequencePointKind_StepOut, 0, 515 },
	{ 108623, 3, 130, 130, 17, 44, 27, kSequencePointKind_Normal, 0, 516 },
	{ 108623, 3, 130, 130, 17, 44, 34, kSequencePointKind_StepOut, 0, 517 },
	{ 108623, 3, 130, 130, 17, 44, 46, kSequencePointKind_StepOut, 0, 518 },
	{ 108623, 3, 130, 130, 17, 44, 53, kSequencePointKind_StepOut, 0, 519 },
	{ 108623, 3, 131, 131, 13, 14, 59, kSequencePointKind_Normal, 0, 520 },
	{ 108624, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 521 },
	{ 108624, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 522 },
	{ 108624, 3, 136, 136, 17, 18, 0, kSequencePointKind_Normal, 0, 523 },
	{ 108624, 3, 136, 136, 19, 53, 1, kSequencePointKind_Normal, 0, 524 },
	{ 108624, 3, 136, 136, 19, 53, 7, kSequencePointKind_StepOut, 0, 525 },
	{ 108624, 3, 136, 136, 19, 53, 18, kSequencePointKind_StepOut, 0, 526 },
	{ 108624, 3, 136, 136, 54, 55, 28, kSequencePointKind_Normal, 0, 527 },
	{ 108625, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 528 },
	{ 108625, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 529 },
	{ 108625, 3, 137, 137, 17, 18, 0, kSequencePointKind_Normal, 0, 530 },
	{ 108625, 3, 137, 137, 19, 47, 1, kSequencePointKind_Normal, 0, 531 },
	{ 108625, 3, 137, 137, 19, 47, 15, kSequencePointKind_StepOut, 0, 532 },
	{ 108625, 3, 137, 137, 19, 47, 21, kSequencePointKind_StepOut, 0, 533 },
	{ 108625, 3, 137, 137, 48, 49, 27, kSequencePointKind_Normal, 0, 534 },
	{ 108626, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 535 },
	{ 108626, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 536 },
	{ 108626, 3, 142, 142, 17, 18, 0, kSequencePointKind_Normal, 0, 537 },
	{ 108626, 3, 142, 142, 19, 50, 1, kSequencePointKind_Normal, 0, 538 },
	{ 108626, 3, 142, 142, 19, 50, 7, kSequencePointKind_StepOut, 0, 539 },
	{ 108626, 3, 142, 142, 19, 50, 18, kSequencePointKind_StepOut, 0, 540 },
	{ 108626, 3, 142, 142, 19, 50, 23, kSequencePointKind_StepOut, 0, 541 },
	{ 108626, 3, 142, 142, 51, 52, 31, kSequencePointKind_Normal, 0, 542 },
	{ 108627, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 543 },
	{ 108627, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 544 },
	{ 108627, 3, 144, 144, 13, 14, 0, kSequencePointKind_Normal, 0, 545 },
	{ 108627, 3, 145, 145, 17, 47, 1, kSequencePointKind_Normal, 0, 546 },
	{ 108627, 3, 145, 145, 17, 47, 2, kSequencePointKind_StepOut, 0, 547 },
	{ 108627, 3, 146, 146, 17, 32, 8, kSequencePointKind_Normal, 0, 548 },
	{ 108627, 3, 146, 146, 17, 32, 20, kSequencePointKind_StepOut, 0, 549 },
	{ 108627, 3, 147, 147, 17, 32, 26, kSequencePointKind_Normal, 0, 550 },
	{ 108627, 3, 147, 147, 17, 32, 38, kSequencePointKind_StepOut, 0, 551 },
	{ 108627, 3, 148, 148, 17, 41, 44, kSequencePointKind_Normal, 0, 552 },
	{ 108627, 3, 148, 148, 17, 41, 62, kSequencePointKind_StepOut, 0, 553 },
	{ 108627, 3, 148, 148, 17, 41, 68, kSequencePointKind_StepOut, 0, 554 },
	{ 108627, 3, 149, 149, 17, 42, 74, kSequencePointKind_Normal, 0, 555 },
	{ 108627, 3, 149, 149, 17, 42, 92, kSequencePointKind_StepOut, 0, 556 },
	{ 108627, 3, 149, 149, 17, 42, 98, kSequencePointKind_StepOut, 0, 557 },
	{ 108627, 3, 150, 150, 13, 14, 104, kSequencePointKind_Normal, 0, 558 },
	{ 108628, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 559 },
	{ 108628, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 560 },
	{ 108628, 3, 155, 155, 17, 18, 0, kSequencePointKind_Normal, 0, 561 },
	{ 108628, 3, 155, 155, 19, 61, 1, kSequencePointKind_Normal, 0, 562 },
	{ 108628, 3, 155, 155, 19, 61, 7, kSequencePointKind_StepOut, 0, 563 },
	{ 108628, 3, 155, 155, 19, 61, 18, kSequencePointKind_StepOut, 0, 564 },
	{ 108628, 3, 155, 155, 19, 61, 30, kSequencePointKind_StepOut, 0, 565 },
	{ 108628, 3, 155, 155, 19, 61, 35, kSequencePointKind_StepOut, 0, 566 },
	{ 108628, 3, 155, 155, 62, 63, 43, kSequencePointKind_Normal, 0, 567 },
	{ 108629, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 568 },
	{ 108629, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 569 },
	{ 108629, 3, 157, 157, 13, 14, 0, kSequencePointKind_Normal, 0, 570 },
	{ 108629, 3, 158, 158, 17, 47, 1, kSequencePointKind_Normal, 0, 571 },
	{ 108629, 3, 158, 158, 17, 47, 2, kSequencePointKind_StepOut, 0, 572 },
	{ 108629, 3, 159, 159, 17, 43, 8, kSequencePointKind_Normal, 0, 573 },
	{ 108629, 3, 159, 159, 17, 43, 26, kSequencePointKind_StepOut, 0, 574 },
	{ 108629, 3, 159, 159, 17, 43, 32, kSequencePointKind_StepOut, 0, 575 },
	{ 108629, 3, 160, 160, 17, 32, 38, kSequencePointKind_Normal, 0, 576 },
	{ 108629, 3, 160, 160, 17, 32, 50, kSequencePointKind_StepOut, 0, 577 },
	{ 108629, 3, 161, 161, 17, 42, 56, kSequencePointKind_Normal, 0, 578 },
	{ 108629, 3, 161, 161, 17, 42, 74, kSequencePointKind_StepOut, 0, 579 },
	{ 108629, 3, 161, 161, 17, 42, 80, kSequencePointKind_StepOut, 0, 580 },
	{ 108629, 3, 162, 162, 13, 14, 86, kSequencePointKind_Normal, 0, 581 },
	{ 108630, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 582 },
	{ 108630, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 583 },
	{ 108630, 3, 167, 167, 17, 18, 0, kSequencePointKind_Normal, 0, 584 },
	{ 108630, 3, 167, 167, 19, 73, 1, kSequencePointKind_Normal, 0, 585 },
	{ 108630, 3, 167, 167, 19, 73, 7, kSequencePointKind_StepOut, 0, 586 },
	{ 108630, 3, 167, 167, 19, 73, 18, kSequencePointKind_StepOut, 0, 587 },
	{ 108630, 3, 167, 167, 19, 73, 30, kSequencePointKind_StepOut, 0, 588 },
	{ 108630, 3, 167, 167, 19, 73, 41, kSequencePointKind_StepOut, 0, 589 },
	{ 108630, 3, 167, 167, 19, 73, 47, kSequencePointKind_StepOut, 0, 590 },
	{ 108630, 3, 167, 167, 74, 75, 55, kSequencePointKind_Normal, 0, 591 },
	{ 108631, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 592 },
	{ 108631, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 593 },
	{ 108631, 3, 169, 169, 13, 14, 0, kSequencePointKind_Normal, 0, 594 },
	{ 108631, 3, 170, 170, 17, 43, 1, kSequencePointKind_Normal, 0, 595 },
	{ 108631, 3, 170, 170, 17, 43, 19, kSequencePointKind_StepOut, 0, 596 },
	{ 108631, 3, 170, 170, 17, 43, 25, kSequencePointKind_StepOut, 0, 597 },
	{ 108631, 3, 171, 171, 17, 44, 31, kSequencePointKind_Normal, 0, 598 },
	{ 108631, 3, 171, 171, 17, 44, 49, kSequencePointKind_StepOut, 0, 599 },
	{ 108631, 3, 171, 171, 17, 44, 55, kSequencePointKind_StepOut, 0, 600 },
	{ 108631, 3, 172, 172, 13, 14, 61, kSequencePointKind_Normal, 0, 601 },
	{ 108632, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 602 },
	{ 108632, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 603 },
	{ 108632, 3, 177, 177, 17, 18, 0, kSequencePointKind_Normal, 0, 604 },
	{ 108632, 3, 177, 177, 19, 62, 1, kSequencePointKind_Normal, 0, 605 },
	{ 108632, 3, 177, 177, 19, 62, 7, kSequencePointKind_StepOut, 0, 606 },
	{ 108632, 3, 177, 177, 19, 62, 18, kSequencePointKind_StepOut, 0, 607 },
	{ 108632, 3, 177, 177, 19, 62, 29, kSequencePointKind_StepOut, 0, 608 },
	{ 108632, 3, 177, 177, 19, 62, 35, kSequencePointKind_StepOut, 0, 609 },
	{ 108632, 3, 177, 177, 63, 64, 43, kSequencePointKind_Normal, 0, 610 },
	{ 108633, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 611 },
	{ 108633, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 612 },
	{ 108633, 3, 179, 179, 13, 14, 0, kSequencePointKind_Normal, 0, 613 },
	{ 108633, 3, 180, 180, 17, 47, 1, kSequencePointKind_Normal, 0, 614 },
	{ 108633, 3, 180, 180, 17, 47, 2, kSequencePointKind_StepOut, 0, 615 },
	{ 108633, 3, 181, 181, 17, 32, 8, kSequencePointKind_Normal, 0, 616 },
	{ 108633, 3, 181, 181, 17, 32, 20, kSequencePointKind_StepOut, 0, 617 },
	{ 108633, 3, 182, 182, 17, 44, 26, kSequencePointKind_Normal, 0, 618 },
	{ 108633, 3, 182, 182, 17, 44, 44, kSequencePointKind_StepOut, 0, 619 },
	{ 108633, 3, 182, 182, 17, 44, 50, kSequencePointKind_StepOut, 0, 620 },
	{ 108633, 3, 183, 183, 17, 41, 56, kSequencePointKind_Normal, 0, 621 },
	{ 108633, 3, 183, 183, 17, 41, 74, kSequencePointKind_StepOut, 0, 622 },
	{ 108633, 3, 183, 183, 17, 41, 80, kSequencePointKind_StepOut, 0, 623 },
	{ 108633, 3, 184, 184, 13, 14, 86, kSequencePointKind_Normal, 0, 624 },
	{ 108634, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 625 },
	{ 108634, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 626 },
	{ 108634, 3, 189, 189, 17, 18, 0, kSequencePointKind_Normal, 0, 627 },
	{ 108634, 3, 189, 189, 19, 48, 1, kSequencePointKind_Normal, 0, 628 },
	{ 108634, 3, 189, 189, 19, 48, 2, kSequencePointKind_StepOut, 0, 629 },
	{ 108634, 3, 189, 189, 49, 50, 10, kSequencePointKind_Normal, 0, 630 },
	{ 108635, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 631 },
	{ 108635, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 632 },
	{ 108635, 3, 190, 190, 17, 18, 0, kSequencePointKind_Normal, 0, 633 },
	{ 108635, 3, 190, 190, 19, 49, 1, kSequencePointKind_Normal, 0, 634 },
	{ 108635, 3, 190, 190, 19, 49, 3, kSequencePointKind_StepOut, 0, 635 },
	{ 108635, 3, 190, 190, 50, 51, 9, kSequencePointKind_Normal, 0, 636 },
	{ 108636, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 637 },
	{ 108636, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 638 },
	{ 108636, 3, 195, 195, 17, 18, 0, kSequencePointKind_Normal, 0, 639 },
	{ 108636, 3, 195, 195, 19, 80, 1, kSequencePointKind_Normal, 0, 640 },
	{ 108636, 3, 195, 195, 19, 80, 10, kSequencePointKind_StepOut, 0, 641 },
	{ 108636, 3, 195, 195, 19, 80, 18, kSequencePointKind_StepOut, 0, 642 },
	{ 108636, 3, 195, 195, 81, 82, 26, kSequencePointKind_Normal, 0, 643 },
	{ 108637, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 644 },
	{ 108637, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 645 },
	{ 108637, 3, 197, 197, 13, 14, 0, kSequencePointKind_Normal, 0, 646 },
	{ 108637, 3, 198, 198, 17, 29, 1, kSequencePointKind_Normal, 0, 647 },
	{ 108637, 3, 198, 198, 0, 0, 8, kSequencePointKind_Normal, 0, 648 },
	{ 108637, 3, 199, 199, 21, 48, 11, kSequencePointKind_Normal, 0, 649 },
	{ 108637, 3, 199, 199, 21, 48, 13, kSequencePointKind_StepOut, 0, 650 },
	{ 108637, 3, 199, 199, 0, 0, 19, kSequencePointKind_Normal, 0, 651 },
	{ 108637, 3, 201, 201, 21, 52, 21, kSequencePointKind_Normal, 0, 652 },
	{ 108637, 3, 201, 201, 21, 52, 23, kSequencePointKind_StepOut, 0, 653 },
	{ 108637, 3, 202, 202, 13, 14, 29, kSequencePointKind_Normal, 0, 654 },
	{ 108638, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 655 },
	{ 108638, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 656 },
	{ 108638, 3, 207, 207, 17, 18, 0, kSequencePointKind_Normal, 0, 657 },
	{ 108638, 3, 207, 207, 19, 46, 1, kSequencePointKind_Normal, 0, 658 },
	{ 108638, 3, 207, 207, 19, 46, 2, kSequencePointKind_StepOut, 0, 659 },
	{ 108638, 3, 207, 207, 47, 48, 10, kSequencePointKind_Normal, 0, 660 },
	{ 108639, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 661 },
	{ 108639, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 662 },
	{ 108639, 3, 208, 208, 17, 18, 0, kSequencePointKind_Normal, 0, 663 },
	{ 108639, 3, 208, 208, 19, 47, 1, kSequencePointKind_Normal, 0, 664 },
	{ 108639, 3, 208, 208, 19, 47, 3, kSequencePointKind_StepOut, 0, 665 },
	{ 108639, 3, 208, 208, 48, 49, 9, kSequencePointKind_Normal, 0, 666 },
	{ 108640, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 667 },
	{ 108640, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 668 },
	{ 108640, 3, 213, 213, 17, 18, 0, kSequencePointKind_Normal, 0, 669 },
	{ 108640, 3, 213, 213, 19, 80, 1, kSequencePointKind_Normal, 0, 670 },
	{ 108640, 3, 213, 213, 19, 80, 10, kSequencePointKind_StepOut, 0, 671 },
	{ 108640, 3, 213, 213, 19, 80, 18, kSequencePointKind_StepOut, 0, 672 },
	{ 108640, 3, 213, 213, 81, 82, 26, kSequencePointKind_Normal, 0, 673 },
	{ 108641, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 674 },
	{ 108641, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 675 },
	{ 108641, 3, 215, 215, 13, 14, 0, kSequencePointKind_Normal, 0, 676 },
	{ 108641, 3, 216, 216, 17, 29, 1, kSequencePointKind_Normal, 0, 677 },
	{ 108641, 3, 216, 216, 0, 0, 8, kSequencePointKind_Normal, 0, 678 },
	{ 108641, 3, 217, 217, 21, 52, 11, kSequencePointKind_Normal, 0, 679 },
	{ 108641, 3, 217, 217, 21, 52, 13, kSequencePointKind_StepOut, 0, 680 },
	{ 108641, 3, 217, 217, 0, 0, 19, kSequencePointKind_Normal, 0, 681 },
	{ 108641, 3, 219, 219, 21, 48, 21, kSequencePointKind_Normal, 0, 682 },
	{ 108641, 3, 219, 219, 21, 48, 23, kSequencePointKind_StepOut, 0, 683 },
	{ 108641, 3, 220, 220, 13, 14, 29, kSequencePointKind_Normal, 0, 684 },
	{ 108642, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 685 },
	{ 108642, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 686 },
	{ 108642, 3, 253, 253, 9, 90, 0, kSequencePointKind_Normal, 0, 687 },
	{ 108642, 3, 253, 253, 9, 90, 20, kSequencePointKind_StepOut, 0, 688 },
	{ 108642, 3, 254, 254, 9, 97, 30, kSequencePointKind_Normal, 0, 689 },
	{ 108642, 3, 254, 254, 9, 97, 50, kSequencePointKind_StepOut, 0, 690 },
	{ 108642, 3, 256, 266, 9, 11, 60, kSequencePointKind_Normal, 0, 691 },
	{ 108642, 3, 256, 266, 9, 11, 70, kSequencePointKind_StepOut, 0, 692 },
	{ 108642, 3, 256, 266, 9, 11, 82, kSequencePointKind_StepOut, 0, 693 },
	{ 108642, 3, 256, 266, 9, 11, 118, kSequencePointKind_StepOut, 0, 694 },
	{ 108642, 3, 256, 266, 9, 11, 130, kSequencePointKind_StepOut, 0, 695 },
	{ 108642, 3, 256, 266, 9, 11, 142, kSequencePointKind_StepOut, 0, 696 },
	{ 108642, 3, 256, 266, 9, 11, 154, kSequencePointKind_StepOut, 0, 697 },
	{ 108657, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 698 },
	{ 108657, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 699 },
	{ 108657, 3, 297, 297, 17, 18, 0, kSequencePointKind_Normal, 0, 700 },
	{ 108657, 3, 297, 297, 19, 55, 1, kSequencePointKind_Normal, 0, 701 },
	{ 108657, 3, 297, 297, 56, 57, 10, kSequencePointKind_Normal, 0, 702 },
	{ 108658, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 703 },
	{ 108658, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 704 },
	{ 108658, 3, 298, 298, 17, 18, 0, kSequencePointKind_Normal, 0, 705 },
	{ 108658, 3, 298, 298, 19, 56, 1, kSequencePointKind_Normal, 0, 706 },
	{ 108658, 3, 298, 298, 57, 58, 8, kSequencePointKind_Normal, 0, 707 },
	{ 108659, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 708 },
	{ 108659, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 709 },
	{ 108659, 3, 301, 301, 9, 22, 0, kSequencePointKind_Normal, 0, 710 },
	{ 108659, 3, 301, 301, 9, 22, 1, kSequencePointKind_StepOut, 0, 711 },
	{ 108659, 3, 302, 302, 9, 10, 7, kSequencePointKind_Normal, 0, 712 },
	{ 108659, 3, 303, 303, 13, 45, 8, kSequencePointKind_Normal, 0, 713 },
	{ 108659, 3, 303, 303, 13, 45, 10, kSequencePointKind_StepOut, 0, 714 },
	{ 108659, 3, 304, 304, 9, 10, 16, kSequencePointKind_Normal, 0, 715 },
	{ 108660, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 716 },
	{ 108660, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 717 },
	{ 108660, 3, 306, 306, 9, 33, 0, kSequencePointKind_Normal, 0, 718 },
	{ 108660, 3, 306, 306, 9, 33, 1, kSequencePointKind_StepOut, 0, 719 },
	{ 108660, 3, 307, 307, 9, 10, 7, kSequencePointKind_Normal, 0, 720 },
	{ 108660, 3, 309, 309, 13, 85, 8, kSequencePointKind_Normal, 0, 721 },
	{ 108660, 3, 309, 309, 13, 85, 9, kSequencePointKind_StepOut, 0, 722 },
	{ 108660, 3, 309, 309, 13, 85, 19, kSequencePointKind_StepOut, 0, 723 },
	{ 108660, 3, 311, 311, 13, 28, 25, kSequencePointKind_Normal, 0, 724 },
	{ 108660, 3, 311, 311, 0, 0, 27, kSequencePointKind_Normal, 0, 725 },
	{ 108660, 3, 312, 312, 17, 49, 30, kSequencePointKind_Normal, 0, 726 },
	{ 108660, 3, 312, 312, 17, 49, 32, kSequencePointKind_StepOut, 0, 727 },
	{ 108660, 3, 312, 312, 0, 0, 38, kSequencePointKind_Normal, 0, 728 },
	{ 108660, 3, 314, 314, 17, 57, 40, kSequencePointKind_Normal, 0, 729 },
	{ 108660, 3, 314, 314, 17, 57, 42, kSequencePointKind_StepOut, 0, 730 },
	{ 108660, 3, 315, 315, 9, 10, 48, kSequencePointKind_Normal, 0, 731 },
	{ 108661, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 732 },
	{ 108661, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 733 },
	{ 108661, 3, 317, 317, 9, 47, 0, kSequencePointKind_Normal, 0, 734 },
	{ 108661, 3, 317, 317, 9, 47, 1, kSequencePointKind_StepOut, 0, 735 },
	{ 108661, 3, 318, 318, 9, 10, 7, kSequencePointKind_Normal, 0, 736 },
	{ 108661, 3, 319, 319, 13, 59, 8, kSequencePointKind_Normal, 0, 737 },
	{ 108661, 3, 319, 319, 13, 59, 11, kSequencePointKind_StepOut, 0, 738 },
	{ 108661, 3, 320, 320, 9, 10, 17, kSequencePointKind_Normal, 0, 739 },
	{ 108662, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 740 },
	{ 108662, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 741 },
	{ 108662, 3, 323, 323, 9, 10, 0, kSequencePointKind_Normal, 0, 742 },
	{ 108662, 3, 324, 324, 13, 53, 1, kSequencePointKind_Normal, 0, 743 },
	{ 108662, 3, 324, 324, 13, 53, 12, kSequencePointKind_StepOut, 0, 744 },
	{ 108662, 3, 325, 325, 9, 10, 20, kSequencePointKind_Normal, 0, 745 },
	{ 108663, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 746 },
	{ 108663, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 747 },
	{ 108663, 3, 328, 328, 9, 10, 0, kSequencePointKind_Normal, 0, 748 },
	{ 108663, 3, 329, 329, 13, 46, 1, kSequencePointKind_Normal, 0, 749 },
	{ 108663, 3, 329, 329, 13, 46, 3, kSequencePointKind_StepOut, 0, 750 },
	{ 108663, 3, 330, 330, 9, 10, 11, kSequencePointKind_Normal, 0, 751 },
	{ 108664, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 752 },
	{ 108664, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 753 },
	{ 108664, 3, 334, 334, 9, 10, 0, kSequencePointKind_Normal, 0, 754 },
	{ 108664, 3, 335, 335, 13, 42, 1, kSequencePointKind_Normal, 0, 755 },
	{ 108664, 3, 335, 335, 13, 42, 13, kSequencePointKind_StepOut, 0, 756 },
	{ 108664, 3, 336, 336, 13, 57, 19, kSequencePointKind_Normal, 0, 757 },
	{ 108664, 3, 336, 336, 13, 57, 31, kSequencePointKind_StepOut, 0, 758 },
	{ 108664, 3, 337, 337, 9, 10, 37, kSequencePointKind_Normal, 0, 759 },
	{ 108665, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 760 },
	{ 108665, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 761 },
	{ 108665, 3, 340, 340, 9, 10, 0, kSequencePointKind_Normal, 0, 762 },
	{ 108665, 3, 341, 341, 13, 39, 1, kSequencePointKind_Normal, 0, 763 },
	{ 108665, 3, 341, 341, 13, 39, 2, kSequencePointKind_StepOut, 0, 764 },
	{ 108665, 3, 342, 342, 9, 10, 14, kSequencePointKind_Normal, 0, 765 },
	{ 108667, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 766 },
	{ 108667, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 767 },
	{ 108667, 3, 347, 347, 9, 10, 0, kSequencePointKind_Normal, 0, 768 },
	{ 108667, 3, 348, 348, 13, 41, 1, kSequencePointKind_Normal, 0, 769 },
	{ 108667, 3, 348, 348, 13, 41, 3, kSequencePointKind_StepOut, 0, 770 },
	{ 108667, 3, 349, 349, 9, 10, 11, kSequencePointKind_Normal, 0, 771 },
	{ 108675, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 772 },
	{ 108675, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 773 },
	{ 108675, 3, 362, 362, 99, 100, 0, kSequencePointKind_Normal, 0, 774 },
	{ 108675, 3, 362, 362, 101, 163, 1, kSequencePointKind_Normal, 0, 775 },
	{ 108675, 3, 362, 362, 101, 163, 6, kSequencePointKind_StepOut, 0, 776 },
	{ 108675, 3, 362, 362, 164, 165, 14, kSequencePointKind_Normal, 0, 777 },
	{ 108676, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 778 },
	{ 108676, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 779 },
	{ 108676, 3, 363, 363, 89, 90, 0, kSequencePointKind_Normal, 0, 780 },
	{ 108676, 3, 363, 363, 91, 150, 1, kSequencePointKind_Normal, 0, 781 },
	{ 108676, 3, 363, 363, 91, 150, 6, kSequencePointKind_StepOut, 0, 782 },
	{ 108676, 3, 363, 363, 151, 152, 14, kSequencePointKind_Normal, 0, 783 },
	{ 108678, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 784 },
	{ 108678, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 785 },
	{ 108678, 3, 366, 366, 95, 96, 0, kSequencePointKind_Normal, 0, 786 },
	{ 108678, 3, 366, 366, 97, 160, 1, kSequencePointKind_Normal, 0, 787 },
	{ 108678, 3, 366, 366, 97, 160, 5, kSequencePointKind_StepOut, 0, 788 },
	{ 108678, 3, 366, 366, 161, 162, 11, kSequencePointKind_Normal, 0, 789 },
	{ 108679, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 790 },
	{ 108679, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 791 },
	{ 108679, 3, 367, 367, 85, 86, 0, kSequencePointKind_Normal, 0, 792 },
	{ 108679, 3, 367, 367, 87, 147, 1, kSequencePointKind_Normal, 0, 793 },
	{ 108679, 3, 367, 367, 87, 147, 5, kSequencePointKind_StepOut, 0, 794 },
	{ 108679, 3, 367, 367, 148, 149, 11, kSequencePointKind_Normal, 0, 795 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_TextRenderingModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_TextRenderingModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/TextRendering/GUIText.deprecated.cs", { 158, 26, 218, 34, 83, 75, 52, 133, 63, 79, 20, 48, 214, 144, 156, 61} },
{ "/Users/<USER>/build/output/unity/unity/Modules/TextRendering/TextGenerator.cs", { 7, 38, 113, 126, 212, 36, 235, 173, 57, 4, 10, 255, 128, 119, 204, 180} },
{ "/Users/<USER>/build/output/unity/unity/Modules/TextRendering/TextRendering.bindings.cs", { 16, 105, 135, 11, 85, 98, 178, 111, 127, 57, 111, 15, 187, 88, 203, 2} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[7] = 
{
	{ 13924, 1 },
	{ 13926, 2 },
	{ 13927, 2 },
	{ 13927, 3 },
	{ 13933, 3 },
	{ 13936, 3 },
	{ 13938, 3 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[61] = 
{
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 32 },
	{ 0, 27 },
	{ 0, 83 },
	{ 0, 45 },
	{ 0, 305 },
	{ 0, 47 },
	{ 0, 218 },
	{ 0, 53 },
	{ 0, 45 },
	{ 0, 104 },
	{ 0, 19 },
	{ 0, 77 },
	{ 0, 182 },
	{ 0, 47 },
	{ 0, 47 },
	{ 0, 47 },
	{ 0, 101 },
	{ 0, 20 },
	{ 0, 18 },
	{ 0, 19 },
	{ 0, 61 },
	{ 0, 18 },
	{ 0, 30 },
	{ 0, 18 },
	{ 0, 60 },
	{ 0, 18 },
	{ 0, 60 },
	{ 0, 30 },
	{ 0, 33 },
	{ 0, 105 },
	{ 0, 45 },
	{ 0, 87 },
	{ 0, 57 },
	{ 0, 45 },
	{ 0, 87 },
	{ 0, 12 },
	{ 0, 28 },
	{ 0, 30 },
	{ 0, 12 },
	{ 0, 28 },
	{ 0, 30 },
	{ 0, 171 },
	{ 0, 12 },
	{ 0, 49 },
	{ 7, 49 },
	{ 0, 22 },
	{ 0, 13 },
	{ 0, 16 },
	{ 0, 13 },
	{ 0, 16 },
	{ 0, 16 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[166] = 
{
	{ 0, 0, 0 },
	{ 13, 0, 1 },
	{ 0, 0, 0 },
	{ 13, 1, 1 },
	{ 0, 0, 0 },
	{ 13, 2, 1 },
	{ 0, 0, 0 },
	{ 13, 3, 1 },
	{ 0, 0, 0 },
	{ 13, 4, 1 },
	{ 0, 0, 0 },
	{ 17, 5, 1 },
	{ 0, 0, 0 },
	{ 17, 6, 1 },
	{ 0, 0, 0 },
	{ 13, 7, 1 },
	{ 0, 0, 0 },
	{ 13, 8, 1 },
	{ 0, 0, 0 },
	{ 13, 9, 1 },
	{ 0, 0, 0 },
	{ 32, 10, 1 },
	{ 0, 0, 0 },
	{ 27, 11, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 83, 12, 1 },
	{ 45, 13, 1 },
	{ 305, 14, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 47, 15, 1 },
	{ 0, 0, 0 },
	{ 218, 16, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 53, 17, 1 },
	{ 45, 18, 1 },
	{ 104, 19, 1 },
	{ 19, 20, 1 },
	{ 77, 21, 1 },
	{ 182, 22, 1 },
	{ 47, 23, 1 },
	{ 47, 24, 1 },
	{ 47, 25, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 101, 26, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 20, 27, 1 },
	{ 0, 0, 0 },
	{ 18, 28, 1 },
	{ 0, 0, 0 },
	{ 19, 29, 1 },
	{ 61, 30, 1 },
	{ 18, 31, 1 },
	{ 0, 0, 0 },
	{ 30, 32, 1 },
	{ 0, 0, 0 },
	{ 18, 33, 1 },
	{ 60, 34, 1 },
	{ 18, 35, 1 },
	{ 60, 36, 1 },
	{ 30, 37, 1 },
	{ 0, 0, 0 },
	{ 33, 38, 1 },
	{ 105, 39, 1 },
	{ 45, 40, 1 },
	{ 87, 41, 1 },
	{ 57, 42, 1 },
	{ 0, 0, 0 },
	{ 45, 43, 1 },
	{ 87, 44, 1 },
	{ 12, 45, 1 },
	{ 0, 0, 0 },
	{ 28, 46, 1 },
	{ 30, 47, 1 },
	{ 12, 48, 1 },
	{ 0, 0, 0 },
	{ 28, 49, 1 },
	{ 30, 50, 1 },
	{ 171, 51, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 12, 52, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 49, 53, 2 },
	{ 0, 0, 0 },
	{ 22, 55, 1 },
	{ 13, 56, 1 },
	{ 0, 0, 0 },
	{ 16, 57, 1 },
	{ 0, 0, 0 },
	{ 13, 58, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 16, 59, 1 },
	{ 16, 60, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TextRenderingModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TextRenderingModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	796,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_TextRenderingModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	7,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
