﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void MonoTODOAttribute__ctor_m40097723D242705105133D2FEE544CDD0D4892F0 (void);
extern void MonoTODOAttribute__ctor_m001DEBBEADAD5B035E3A2C7A2F5781EBE30F9B78 (void);
extern void TransactionCompletedEventHandler__ctor_mAC6B56920A35858254ACE4F75E3645DD9C394CB4 (void);
extern void TransactionCompletedEventHandler_Invoke_mBFD780E1344550FC207E7DC648CE87AD1BF5E08D (void);
extern void Enlistment__ctor_m8A01B2DCBBE2F2BB4CD040581D0D3521A449583F (void);
extern void Enlistment_Done_mA9F7878F361CF3BFE6964A1166939ECE0B39471F (void);
extern void Enlistment_InternalOnDone_m1690D08C96EE3C079839658BFC403F7C1CD63F8E (void);
extern void SinglePhaseEnlistment__ctor_mFE1891FDD22473024672131E28D94176F7D74A01 (void);
extern void Transaction_get_Volatiles_mCD1BF84C66655C8FD244A1F5578DD02DBED0129A (void);
extern void Transaction_get_Durables_mCFB83FC73DFCF3EBD3A758327D3EE835389566A3 (void);
extern void Transaction_System_Runtime_Serialization_ISerializable_GetObjectData_mCD8C8392E3EE07D0A0D0AB5362E7659BC4A83AB5 (void);
extern void Transaction_get_Current_m4781EDC03D62E0BEBD0D9B647FA4A264814DC529 (void);
extern void Transaction_get_CurrentInternal_m195CAC9933D30007BD941BF68FA56CE0C7C7FBEE (void);
extern void Transaction_get_TransactionInformation_mB4DBFB45362745793F6E268C7F5BD9ADED3E5861 (void);
extern void Transaction_Dispose_m8507F1AF0ADFB3D1201F30E58BAB1166BD748585 (void);
extern void Transaction_EnlistVolatile_m988B22FDB2386F15912916E43600B19BBADCFF4F (void);
extern void Transaction_EnlistVolatileInternal_m654C843CE33B9FB1394CB614266E08F12FDC3380 (void);
extern void Transaction_Equals_m6F34A0E9EC2422A790EA8E9774E3E6BAB6B3C11A (void);
extern void Transaction_Equals_mC586D4C5C87F45DCF282AF7B071738E8E8B2F6ED (void);
extern void Transaction_op_Equality_m39B1A486DD944290954B17E02B0BFBDC248659BC (void);
extern void Transaction_op_Inequality_mDDF0206373667C46B4E753596C1C0F1CC1DD730D (void);
extern void Transaction_GetHashCode_mAE0370AD6752F802566D7F7D56997BE72D8E4CDB (void);
extern void Transaction_Rollback_m700FBC7080668AD032FCDA055719E100117F14B5 (void);
extern void Transaction_Rollback_m7BEAC0E95FA3B45F0E78C5FF1857F914AAA1909E (void);
extern void Transaction_Rollback_m298E5E20A30D81EFD61CC338DF07E9E3BA82C28D (void);
extern void Transaction_set_Aborted_m24683739B6DC84AF5BEAF5E83C6ECA1D4B44BE17 (void);
extern void Transaction_get_Scope_mCB49E7F6BABD63238A74D8C3D08B7267B01CFB8E (void);
extern void Transaction_FireCompleted_mD21E71C2CDB94A3F1ED8AEBD8F258C70F8210440 (void);
extern void Transaction_EnsureIncompleteCurrentScope_m50358C964D025F5EAF27CC2F0D24229E86BBA031 (void);
extern void Transaction__ctor_mDF856DFDF7C245002C76F0B0E0AC7821B25E7ADC (void);
extern void TransactionEventArgs__ctor_mFD81A5A7A11F8417373C43F9BEB03EAABF6B28B3 (void);
extern void TransactionEventArgs__ctor_mDE16E072857474C6DD16928DB47058E2A9F98046 (void);
extern void TransactionException__ctor_m3C29A3EB6D1A3AA42E78B96EF45C22CC1F8171BB (void);
extern void TransactionException__ctor_mD6A1BC6487DB3CE81488727A3D811024D45F8859 (void);
extern void TransactionException__ctor_m3FDD9AE8E185D636E05D9A9D83E738C6A63715E4 (void);
extern void TransactionInformation_get_Status_mB45D34431DAC463611A59869926E98513B97414A (void);
extern void TransactionInformation_set_Status_m6D0E87BE8EDD80DC7AFD5A3CEC8E8EA4B1626AFE (void);
extern void TransactionManager__cctor_m4E4759360B22227816CDA61B5E775400B5D490C4 (void);
extern void TransactionManager_get_DefaultTimeout_m986CEE04836E5DCF3328652175C50A276DD75E0F (void);
extern void TransactionOptions__ctor_mB6E01EA3E9A536D3DD7518541B0A19791D044910 (void);
extern void TransactionOptions_op_Equality_m5B9B64DE16F3F0C7BBDE6C6201B59679B90E6C97 (void);
extern void TransactionOptions_Equals_m6E5D4EFB290F14239D57B0C7D364109797600E48 (void);
extern void TransactionOptions_GetHashCode_mD8274DC4F6F15118D764DB6D0043BB82AD162234 (void);
extern void TransactionScope_get_IsComplete_mA48470D9FFCC137101B3972C9B1D46ED5EC51C8C (void);
extern void TransactionScope__cctor_m3EFA247165D4145B3459A1B3D476309A6D5DCB48 (void);
extern void ThrowStub_ThrowNotSupportedException_m53C3B333318540135E1FEA2D1ADAD8EC68844340 (void);
static Il2CppMethodPointer s_methodPointers[48] = 
{
	MonoTODOAttribute__ctor_m40097723D242705105133D2FEE544CDD0D4892F0,
	MonoTODOAttribute__ctor_m001DEBBEADAD5B035E3A2C7A2F5781EBE30F9B78,
	TransactionCompletedEventHandler__ctor_mAC6B56920A35858254ACE4F75E3645DD9C394CB4,
	TransactionCompletedEventHandler_Invoke_mBFD780E1344550FC207E7DC648CE87AD1BF5E08D,
	Enlistment__ctor_m8A01B2DCBBE2F2BB4CD040581D0D3521A449583F,
	Enlistment_Done_mA9F7878F361CF3BFE6964A1166939ECE0B39471F,
	Enlistment_InternalOnDone_m1690D08C96EE3C079839658BFC403F7C1CD63F8E,
	NULL,
	NULL,
	SinglePhaseEnlistment__ctor_mFE1891FDD22473024672131E28D94176F7D74A01,
	Transaction_get_Volatiles_mCD1BF84C66655C8FD244A1F5578DD02DBED0129A,
	Transaction_get_Durables_mCFB83FC73DFCF3EBD3A758327D3EE835389566A3,
	Transaction_System_Runtime_Serialization_ISerializable_GetObjectData_mCD8C8392E3EE07D0A0D0AB5362E7659BC4A83AB5,
	Transaction_get_Current_m4781EDC03D62E0BEBD0D9B647FA4A264814DC529,
	Transaction_get_CurrentInternal_m195CAC9933D30007BD941BF68FA56CE0C7C7FBEE,
	Transaction_get_TransactionInformation_mB4DBFB45362745793F6E268C7F5BD9ADED3E5861,
	Transaction_Dispose_m8507F1AF0ADFB3D1201F30E58BAB1166BD748585,
	Transaction_EnlistVolatile_m988B22FDB2386F15912916E43600B19BBADCFF4F,
	Transaction_EnlistVolatileInternal_m654C843CE33B9FB1394CB614266E08F12FDC3380,
	Transaction_Equals_m6F34A0E9EC2422A790EA8E9774E3E6BAB6B3C11A,
	Transaction_Equals_mC586D4C5C87F45DCF282AF7B071738E8E8B2F6ED,
	Transaction_op_Equality_m39B1A486DD944290954B17E02B0BFBDC248659BC,
	Transaction_op_Inequality_mDDF0206373667C46B4E753596C1C0F1CC1DD730D,
	Transaction_GetHashCode_mAE0370AD6752F802566D7F7D56997BE72D8E4CDB,
	Transaction_Rollback_m700FBC7080668AD032FCDA055719E100117F14B5,
	Transaction_Rollback_m7BEAC0E95FA3B45F0E78C5FF1857F914AAA1909E,
	Transaction_Rollback_m298E5E20A30D81EFD61CC338DF07E9E3BA82C28D,
	Transaction_set_Aborted_m24683739B6DC84AF5BEAF5E83C6ECA1D4B44BE17,
	Transaction_get_Scope_mCB49E7F6BABD63238A74D8C3D08B7267B01CFB8E,
	Transaction_FireCompleted_mD21E71C2CDB94A3F1ED8AEBD8F258C70F8210440,
	Transaction_EnsureIncompleteCurrentScope_m50358C964D025F5EAF27CC2F0D24229E86BBA031,
	Transaction__ctor_mDF856DFDF7C245002C76F0B0E0AC7821B25E7ADC,
	TransactionEventArgs__ctor_mFD81A5A7A11F8417373C43F9BEB03EAABF6B28B3,
	TransactionEventArgs__ctor_mDE16E072857474C6DD16928DB47058E2A9F98046,
	TransactionException__ctor_m3C29A3EB6D1A3AA42E78B96EF45C22CC1F8171BB,
	TransactionException__ctor_mD6A1BC6487DB3CE81488727A3D811024D45F8859,
	TransactionException__ctor_m3FDD9AE8E185D636E05D9A9D83E738C6A63715E4,
	TransactionInformation_get_Status_mB45D34431DAC463611A59869926E98513B97414A,
	TransactionInformation_set_Status_m6D0E87BE8EDD80DC7AFD5A3CEC8E8EA4B1626AFE,
	TransactionManager__cctor_m4E4759360B22227816CDA61B5E775400B5D490C4,
	TransactionManager_get_DefaultTimeout_m986CEE04836E5DCF3328652175C50A276DD75E0F,
	TransactionOptions__ctor_mB6E01EA3E9A536D3DD7518541B0A19791D044910,
	TransactionOptions_op_Equality_m5B9B64DE16F3F0C7BBDE6C6201B59679B90E6C97,
	TransactionOptions_Equals_m6E5D4EFB290F14239D57B0C7D364109797600E48,
	TransactionOptions_GetHashCode_mD8274DC4F6F15118D764DB6D0043BB82AD162234,
	TransactionScope_get_IsComplete_mA48470D9FFCC137101B3972C9B1D46ED5EC51C8C,
	TransactionScope__cctor_m3EFA247165D4145B3459A1B3D476309A6D5DCB48,
	ThrowStub_ThrowNotSupportedException_m53C3B333318540135E1FEA2D1ADAD8EC68844340,
};
extern void TransactionOptions__ctor_mB6E01EA3E9A536D3DD7518541B0A19791D044910_AdjustorThunk (void);
extern void TransactionOptions_Equals_m6E5D4EFB290F14239D57B0C7D364109797600E48_AdjustorThunk (void);
extern void TransactionOptions_GetHashCode_mD8274DC4F6F15118D764DB6D0043BB82AD162234_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[3] = 
{
	{ 0x0600002A, TransactionOptions__ctor_mB6E01EA3E9A536D3DD7518541B0A19791D044910_AdjustorThunk },
	{ 0x0600002C, TransactionOptions_Equals_m6E5D4EFB290F14239D57B0C7D364109797600E48_AdjustorThunk },
	{ 0x0600002D, TransactionOptions_GetHashCode_mD8274DC4F6F15118D764DB6D0043BB82AD162234_AdjustorThunk },
};
static const int32_t s_InvokerIndices[48] = 
{
	4364,
	3881,
	2798,
	2802,
	4364,
	4364,
	4364,
	0,
	0,
	4364,
	4250,
	4250,
	2811,
	9031,
	9031,
	4250,
	4364,
	2522,
	2522,
	3185,
	3185,
	7261,
	7261,
	4216,
	4364,
	3881,
	2802,
	3807,
	4250,
	4364,
	9089,
	4364,
	4364,
	3881,
	4364,
	3881,
	2811,
	4216,
	3852,
	9089,
	9075,
	2746,
	7342,
	3185,
	4216,
	4168,
	9089,
	9089,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationSystem_Transactions;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Transactions_CodeGenModule;
const Il2CppCodeGenModule g_System_Transactions_CodeGenModule = 
{
	"System.Transactions.dll",
	48,
	s_methodPointers,
	3,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationSystem_Transactions,
	NULL,
	NULL,
	NULL,
	NULL,
};
