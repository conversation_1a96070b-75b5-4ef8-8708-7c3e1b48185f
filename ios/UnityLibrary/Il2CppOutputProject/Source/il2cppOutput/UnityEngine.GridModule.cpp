﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA;
struct Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE;
struct GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_CoreModule[];
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_GridModule[];
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_CellToLocal_m993F6316B4D584BE5633A27757D2451927A54EC5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_CellToWorld_m513467A7565AD77DD66F9032C76AC96BA1DC0105_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_DoNothing_mA280987BF98D257023D46C2C01902FC82EE6A00A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_GetBoundsLocal_m9D6FA8A2B292CE45B7AA27CBB9B91EBA3165E949_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_GetBoundsLocal_mFAD4D953F5FCF5A7C42FAC81984EDE9CBD78AC08_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_LocalToCellInterpolated_m32D646B126793BB4E12269B61670484C080C797C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_LocalToCell_mEDD45C2761BEDE6CFE9C796582BED95E22DFA5AE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_WorldToCell_m72AFBAA2458CD70A302708AA09DC2641BD95E21E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_WorldToLocal_m3CC7D1B5D856C92E2CA0E9D411EC5A44D9FE68CF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_get_cellGap_m5FDC0C21B2071E93589B6A353DAFA7265BC1BE56_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GridLayout_get_cellSize_m71C86547CB23B9B3F124BC8815BE97EE5BF80543_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_GetCellCenterLocal_m62E6CFAE046C145B8340904FAE45D043B927CAB8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_GetCellCenterWorld_m9972542B8C9076B7FC979A70A439DE0A3AC65F3D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_InverseSwizzle_m16294FE6EAEE7C897AC122CC3203E050AF1E8388_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_Swizzle_m7740439E445C7DCB6FD3EBB28768B9946DC410EC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid__ctor_mF8857FF5BF55232C1C252B55DA6E34F9C15CD75C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_get_cellGap_m9B12CA3DA5A7AC906A1E1943FABD3E1A523439A1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_get_cellSize_m5512593532CABA9CFC058123B923AFA483D6003E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_set_cellGap_m7DFD489B9B17CE724B56B05AB0639B99F79A3D02_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Grid_set_cellSize_m9868F4574C37B62CE64E8AF776E085B54F2F1BF8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector3Int_op_Implicit_m13297B1F6D07F1E46C0627EAAB8413E637FCA442_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t2C7BF608494A5C8FB8C8C4D318FB27BCF6CE322A 
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 
{
	int32_t ___m_X;
	int32_t ___m_Y;
	int32_t ___m_Z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Center;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Extents;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct CellLayout_tA008DBFCE86EB2F6A83EA40230EA7925A0615C5B 
{
	int32_t ___value__;
};
struct CellSwizzle_t2ED61626ACF1D6BE26740A8C327D567405793CA1 
{
	int32_t ___value__;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE  : public GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B
{
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_StaticFields
{
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_Zero;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_One;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_Up;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_Down;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_Left;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_Right;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_Forward;
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___s_Back;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3Int_op_Implicit_m13297B1F6D07F1E46C0627EAAB8413E637FCA442_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___0_v, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_cellPosition, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_localPosition, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1 (int32_t ___0_swizzle, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_position, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___2_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4 (int32_t ___0_swizzle, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_position, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___2_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___0_cellPosition, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_origin, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_size, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_origin, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_size, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___2_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___0_cellPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_localPosition, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_cellPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_localPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___0_cellPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_worldPosition, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_localPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_worldPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8 (Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Grid_GetCellCenterLocal_m62E6CFAE046C145B8340904FAE45D043B927CAB8 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___0_position, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_GetCellCenterLocal_m62E6CFAE046C145B8340904FAE45D043B927CAB8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_position));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_GetCellCenterLocal_m62E6CFAE046C145B8340904FAE45D043B927CAB8_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 3));
		Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 L_0 = ___0_position;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 4));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3Int_op_Implicit_m13297B1F6D07F1E46C0627EAAB8413E637FCA442_inline(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 4));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 5));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 5));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 6));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_1, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 6));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 7));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621(__this, L_3, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 7));
		V_0 = L_4;
		goto IL_001b;
	}

IL_001b:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 8));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = V_0;
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Grid_GetCellCenterWorld_m9972542B8C9076B7FC979A70A439DE0A3AC65F3D (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___0_position, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_GetCellCenterWorld_m9972542B8C9076B7FC979A70A439DE0A3AC65F3D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_position));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_GetCellCenterWorld_m9972542B8C9076B7FC979A70A439DE0A3AC65F3D_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 9));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 10));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 11));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 12));
		Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 L_0 = ___0_position;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 13));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3Int_op_Implicit_m13297B1F6D07F1E46C0627EAAB8413E637FCA442_inline(L_0, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 13));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 14));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 14));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 15));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_1, L_2, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 15));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 16));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621(__this, L_3, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 16));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 17));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5;
		L_5 = GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6(__this, L_4, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 17));
		V_0 = L_5;
		goto IL_0021;
	}

IL_0021:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 18));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Grid_get_cellSize_m5512593532CABA9CFC058123B923AFA483D6003E (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_get_cellSize_m5512593532CABA9CFC058123B923AFA483D6003E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_get_cellSize_m5512593532CABA9CFC058123B923AFA483D6003E_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellSize_m9868F4574C37B62CE64E8AF776E085B54F2F1BF8 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_set_cellSize_m9868F4574C37B62CE64E8AF776E085B54F2F1BF8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_set_cellSize_m9868F4574C37B62CE64E8AF776E085B54F2F1BF8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Grid_get_cellGap_m9B12CA3DA5A7AC906A1E1943FABD3E1A523439A1 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_get_cellGap_m9B12CA3DA5A7AC906A1E1943FABD3E1A523439A1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_get_cellGap_m9B12CA3DA5A7AC906A1E1943FABD3E1A523439A1_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellGap_m7DFD489B9B17CE724B56B05AB0639B99F79A3D02 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_set_cellGap_m7DFD489B9B17CE724B56B05AB0639B99F79A3D02_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_set_cellGap_m7DFD489B9B17CE724B56B05AB0639B99F79A3D02_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Grid_get_cellLayout_m68897E54C769C732FC091914D07E312BC9244358 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*Grid_get_cellLayout_m68897E54C769C732FC091914D07E312BC9244358_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*);
	static Grid_get_cellLayout_m68897E54C769C732FC091914D07E312BC9244358_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_get_cellLayout_m68897E54C769C732FC091914D07E312BC9244358_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::get_cellLayout()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellLayout_m61FEB7CE68F4598356DFD0B564BFF8B15F6A7766 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Grid_set_cellLayout_m61FEB7CE68F4598356DFD0B564BFF8B15F6A7766_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*, int32_t);
	static Grid_set_cellLayout_m61FEB7CE68F4598356DFD0B564BFF8B15F6A7766_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_set_cellLayout_m61FEB7CE68F4598356DFD0B564BFF8B15F6A7766_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::set_cellLayout(UnityEngine.GridLayout/CellLayout)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Grid_get_cellSwizzle_m8F68B475BAA2554A80C06CAA7EDC36009E9ED5E6 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*Grid_get_cellSwizzle_m8F68B475BAA2554A80C06CAA7EDC36009E9ED5E6_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*);
	static Grid_get_cellSwizzle_m8F68B475BAA2554A80C06CAA7EDC36009E9ED5E6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_get_cellSwizzle_m8F68B475BAA2554A80C06CAA7EDC36009E9ED5E6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::get_cellSwizzle()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellSwizzle_mEA7F3096C6537707CC8A1EE5FBB86AD7D68D2F5A (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Grid_set_cellSwizzle_mEA7F3096C6537707CC8A1EE5FBB86AD7D68D2F5A_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*, int32_t);
	static Grid_set_cellSwizzle_mEA7F3096C6537707CC8A1EE5FBB86AD7D68D2F5A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_set_cellSwizzle_mEA7F3096C6537707CC8A1EE5FBB86AD7D68D2F5A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::set_cellSwizzle(UnityEngine.GridLayout/CellSwizzle)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Grid_Swizzle_m7740439E445C7DCB6FD3EBB28768B9946DC410EC (int32_t ___0_swizzle, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_position, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_Swizzle_m7740439E445C7DCB6FD3EBB28768B9946DC410EC_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_Swizzle_m7740439E445C7DCB6FD3EBB28768B9946DC410EC_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = ___0_swizzle;
		Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1(L_0, (&___1_position), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Grid_InverseSwizzle_m16294FE6EAEE7C897AC122CC3203E050AF1E8388 (int32_t ___0_swizzle, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_position, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid_InverseSwizzle_m16294FE6EAEE7C897AC122CC3203E050AF1E8388_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid_InverseSwizzle_m16294FE6EAEE7C897AC122CC3203E050AF1E8388_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = ___0_swizzle;
		Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4(L_0, (&___1_position), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid__ctor_mF8857FF5BF55232C1C252B55DA6E34F9C15CD75C (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Grid__ctor_mF8857FF5BF55232C1C252B55DA6E34F9C15CD75C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Grid__ctor_mF8857FF5BF55232C1C252B55DA6E34F9C15CD75C_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_get_cellSize_Injected_mC4C85B70ACB504F8A70B6E28C458BDFCD98EFF10_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::get_cellSize_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_set_cellSize_Injected_mB9D7DD3AC90AF161BC40F9DBD67F8A925BB1B6FD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::set_cellSize_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4 (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_get_cellGap_Injected_m0A087196AB37708D9FEB3DA055F0DFEB6C6086D4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::get_cellGap_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E_ftn) (Grid_t4C0ACF986206D18240E35155E01B5A97DAA651FE*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_set_cellGap_Injected_m246E978F8E8268147B94741CDCA03830594D184E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::set_cellGap_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1 (int32_t ___0_swizzle, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_position, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___2_ret, const RuntimeMethod* method) 
{
	typedef void (*Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1_ftn) (int32_t, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_Swizzle_Injected_mB8E613F5DEF74D95D1BE90BABCE8E96C8C4394E1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::Swizzle_Injected(UnityEngine.GridLayout/CellSwizzle,UnityEngine.Vector3&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(___0_swizzle, ___1_position, ___2_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4 (int32_t ___0_swizzle, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_position, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___2_ret, const RuntimeMethod* method) 
{
	typedef void (*Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4_ftn) (int32_t, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (Grid_InverseSwizzle_Injected_mD1193316E3BC11444809BEA8A2B5E319751B0CF4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.Grid::InverseSwizzle_Injected(UnityEngine.GridLayout/CellSwizzle,UnityEngine.Vector3&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(___0_swizzle, ___1_position, ___2_ret);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_get_cellSize_m71C86547CB23B9B3F124BC8815BE97EE5BF80543 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_get_cellSize_m71C86547CB23B9B3F124BC8815BE97EE5BF80543_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_get_cellSize_m71C86547CB23B9B3F124BC8815BE97EE5BF80543_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_get_cellGap_m5FDC0C21B2071E93589B6A353DAFA7265BC1BE56 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_get_cellGap_m5FDC0C21B2071E93589B6A353DAFA7265BC1BE56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_get_cellGap_m5FDC0C21B2071E93589B6A353DAFA7265BC1BE56_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t GridLayout_get_cellLayout_m31F956C726F79C4823A001CBC80DA8FC7ABEF80D (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*GridLayout_get_cellLayout_m31F956C726F79C4823A001CBC80DA8FC7ABEF80D_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*);
	static GridLayout_get_cellLayout_m31F956C726F79C4823A001CBC80DA8FC7ABEF80D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_get_cellLayout_m31F956C726F79C4823A001CBC80DA8FC7ABEF80D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::get_cellLayout()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t GridLayout_get_cellSwizzle_m6AFC21A656AD668FAB42AA5FAF65EB53C6A305FD (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*GridLayout_get_cellSwizzle_m6AFC21A656AD668FAB42AA5FAF65EB53C6A305FD_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*);
	static GridLayout_get_cellSwizzle_m6AFC21A656AD668FAB42AA5FAF65EB53C6A305FD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_get_cellSwizzle_m6AFC21A656AD668FAB42AA5FAF65EB53C6A305FD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::get_cellSwizzle()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 GridLayout_GetBoundsLocal_m9D6FA8A2B292CE45B7AA27CBB9B91EBA3165E949 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___0_cellPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_GetBoundsLocal_m9D6FA8A2B292CE45B7AA27CBB9B91EBA3165E949_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_GetBoundsLocal_m9D6FA8A2B292CE45B7AA27CBB9B91EBA3165E949_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40(__this, (&___0_cellPosition), (&V_0), NULL);
		Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 GridLayout_GetBoundsLocal_mFAD4D953F5FCF5A7C42FAC81984EDE9CBD78AC08 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_origin, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_size, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_GetBoundsLocal_mFAD4D953F5FCF5A7C42FAC81984EDE9CBD78AC08_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_origin), (&___1_size));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_GetBoundsLocal_mFAD4D953F5FCF5A7C42FAC81984EDE9CBD78AC08_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 19));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 20));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 21));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 22));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_origin;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___1_size;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 23));
		Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 L_2;
		L_2 = GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0(__this, L_0, L_1, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 23));
		V_0 = L_2;
		goto IL_000c;
	}

IL_000c:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 24));
		Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_origin, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_size, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_GetBoundsLocalOriginSize_m4480950C28F0F839467E23A587B4C27F9D8B79B0_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33(__this, (&___0_origin), (&___1_size), (&V_0), NULL);
		Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_CellToLocal_m993F6316B4D584BE5633A27757D2451927A54EC5 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___0_cellPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_CellToLocal_m993F6316B4D584BE5633A27757D2451927A54EC5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_CellToLocal_m993F6316B4D584BE5633A27757D2451927A54EC5_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52(__this, (&___0_cellPosition), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 GridLayout_LocalToCell_mEDD45C2761BEDE6CFE9C796582BED95E22DFA5AE (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_localPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_LocalToCell_mEDD45C2761BEDE6CFE9C796582BED95E22DFA5AE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_LocalToCell_mEDD45C2761BEDE6CFE9C796582BED95E22DFA5AE_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145(__this, (&___0_localPosition), (&V_0), NULL);
		Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_cellPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_CellToLocalInterpolated_mE1FC35F36111BD0881573C6F51C37239BF3BD621_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0(__this, (&___0_cellPosition), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_LocalToCellInterpolated_m32D646B126793BB4E12269B61670484C080C797C (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_localPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_LocalToCellInterpolated_m32D646B126793BB4E12269B61670484C080C797C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_LocalToCellInterpolated_m32D646B126793BB4E12269B61670484C080C797C_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752(__this, (&___0_localPosition), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_CellToWorld_m513467A7565AD77DD66F9032C76AC96BA1DC0105 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___0_cellPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_CellToWorld_m513467A7565AD77DD66F9032C76AC96BA1DC0105_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_CellToWorld_m513467A7565AD77DD66F9032C76AC96BA1DC0105_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89(__this, (&___0_cellPosition), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 GridLayout_WorldToCell_m72AFBAA2458CD70A302708AA09DC2641BD95E21E (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_WorldToCell_m72AFBAA2458CD70A302708AA09DC2641BD95E21E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_WorldToCell_m72AFBAA2458CD70A302708AA09DC2641BD95E21E_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E(__this, (&___0_worldPosition), (&V_0), NULL);
		Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_localPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_LocalToWorld_m4714A4DAF4FF9545E9CCB3A8ACB5731A498D9CA6_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0(__this, (&___0_localPosition), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_WorldToLocal_m3CC7D1B5D856C92E2CA0E9D411EC5A44D9FE68CF (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_WorldToLocal_m3CC7D1B5D856C92E2CA0E9D411EC5A44D9FE68CF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_WorldToLocal_m3CC7D1B5D856C92E2CA0E9D411EC5A44D9FE68CF_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD(__this, (&___0_worldPosition), (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_GetLayoutCellCenter_mAAAFBE12686D56E4A18A2ECBC4860A80C109D014_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_DoNothing_mA280987BF98D257023D46C2C01902FC82EE6A00A (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_DoNothing_mA280987BF98D257023D46C2C01902FC82EE6A00A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout_DoNothing_mA280987BF98D257023D46C2C01902FC82EE6A00A_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 25));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 26));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 27));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_GridModule + 28));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, GridLayout__ctor_m9266D2F9A58091E4214E9E5B69C0E5350F344828_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_get_cellSize_Injected_mE35CFB16984580764B681FF43C5B9DE53B9A48DA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::get_cellSize_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_get_cellGap_Injected_m4F79F7681C7F395D3028167CC5FCFDA22D83AE21_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::get_cellGap_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___0_cellPosition, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376*, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3*);
	static GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_GetBoundsLocal_Injected_m2D992D9391E0EEFC9BE349E3ECC8B4C1AD842F40_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::GetBoundsLocal_Injected(UnityEngine.Vector3Int&,UnityEngine.Bounds&)");
	_il2cpp_icall_func(__this, ___0_cellPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_origin, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_size, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___2_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3*);
	static GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_GetBoundsLocalOriginSize_Injected_mE93975B08711614733E00BFDEF8C171CF96CBA33_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::GetBoundsLocalOriginSize_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Bounds&)");
	_il2cpp_icall_func(__this, ___0_origin, ___1_size, ___2_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___0_cellPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_CellToLocal_Injected_mCCD75D6FE61B940B0EA4ADCE21B120C773BC0E52_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::CellToLocal_Injected(UnityEngine.Vector3Int&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_cellPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_localPosition, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376*);
	static GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_LocalToCell_Injected_mFE762633666D00923684AE9A5FD4A46589257145_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::LocalToCell_Injected(UnityEngine.Vector3&,UnityEngine.Vector3Int&)");
	_il2cpp_icall_func(__this, ___0_localPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_cellPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_CellToLocalInterpolated_Injected_m64A34FF84B0948E3E0CAD199855CBCE3434976D0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::CellToLocalInterpolated_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_cellPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_localPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_LocalToCellInterpolated_Injected_m309AAE2525206CA22D8A11E6613433F66D7C9752_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::LocalToCellInterpolated_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_localPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___0_cellPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_CellToWorld_Injected_mDB836DF11EE280CF4D3AEA734215BC54E9F8BB89_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::CellToWorld_Injected(UnityEngine.Vector3Int&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_cellPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_worldPosition, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376*);
	static GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_WorldToCell_Injected_mFC4155CB0C1B36A15550FCE30F22A799676F939E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::WorldToCell_Injected(UnityEngine.Vector3&,UnityEngine.Vector3Int&)");
	_il2cpp_icall_func(__this, ___0_worldPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0 (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_localPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_LocalToWorld_Injected_m9BCAFA5F003507ADC3015DD74103CD4438A515A0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::LocalToWorld_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_localPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_worldPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___1_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_WorldToLocal_Injected_m4B42F8DF186A8E652F4CCB17A941CFBFA30754AD_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::WorldToLocal_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_worldPosition, ___1_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC_ftn) (GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (GridLayout_GetLayoutCellCenter_Injected_m5FA842844DACF1EF77D0C1368CA8E830F88A5BDC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.GridLayout::GetLayoutCellCenter_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3Int_op_Implicit_m13297B1F6D07F1E46C0627EAAB8413E637FCA442_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376 ___0_v, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3Int_op_Implicit_m13297B1F6D07F1E46C0627EAAB8413E637FCA442_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_v));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector3Int_op_Implicit_m13297B1F6D07F1E46C0627EAAB8413E637FCA442_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27649));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27650));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27651));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27652));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27653));
		int32_t L_0;
		L_0 = Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_inline((&___0_v), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27653));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27654));
		int32_t L_1;
		L_1 = Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_inline((&___0_v), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27654));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27655));
		int32_t L_2;
		L_2 = Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_inline((&___0_v), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27655));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27656));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		memset((&L_3), 0, sizeof(L_3));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_3), ((float)L_0), ((float)L_1), ((float)L_2), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27656));
		V_0 = L_3;
		goto IL_0021;
	}

IL_0021:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27657));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_a), (&___1_b));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_RuntimeMethod_var, NULL, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25476));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25477));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25478));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25479));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25480));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25480));
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25481));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector3Int_get_x_m21C268D2AA4C03CE35AA49DF6155347C9748054C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27441));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27442));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27443));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27444));
		int32_t L_0 = __this->___m_X;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27445));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector3Int_get_y_m42F43000F85D356557CAF03442273E7AA08F7F72_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27451));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27452));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27453));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27454));
		int32_t L_0 = __this->___m_Y;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27455));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_inline (Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector3Int_get_z_m96E180F866145E373F42358F2371EFF446F08AED_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27461));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27462));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27463));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27464));
		int32_t L_0 = __this->___m_Z;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 27465));
		int32_t L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_x), (&___1_y), (&___2_z));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25197));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25198));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25199));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25200));
		float L_0 = ___0_x;
		__this->___x = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25201));
		float L_1 = ___1_y;
		__this->___y = L_1;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25202));
		float L_2 = ___2_z;
		__this->___z = L_2;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_CoreModule + 25203));
		return;
	}
}
