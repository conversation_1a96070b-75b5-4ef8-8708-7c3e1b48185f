﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[8] = 
{
	{ 31087, 0,  2 },
	{ 31087, 0,  3 },
	{ 31087, 0,  4 },
	{ 31087, 0,  5 },
	{ 31087, 0,  6 },
	{ 31087, 0,  7 },
	{ 31087, 0,  8 },
	{ 31087, 0,  9 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = 
{
	"request",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[27] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 1, 1 },
	{ 2, 1 },
	{ 3, 1 },
	{ 4, 1 },
	{ 5, 1 },
	{ 6, 1 },
	{ 7, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAssetBundleModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAssetBundleModule[152] = 
{
	{ 110032, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110032, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110032, 1, 9, 9, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110032, 1, 10, 10, 13, 43, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110032, 1, 10, 10, 13, 43, 3, kSequencePointKind_StepOut, 0, 4 },
	{ 110032, 1, 11, 11, 9, 10, 11, kSequencePointKind_Normal, 0, 5 },
	{ 110033, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 110033, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 110033, 1, 14, 14, 9, 10, 0, kSequencePointKind_Normal, 0, 8 },
	{ 110033, 1, 15, 15, 13, 43, 1, kSequencePointKind_Normal, 0, 9 },
	{ 110033, 1, 15, 15, 13, 43, 3, kSequencePointKind_StepOut, 0, 10 },
	{ 110033, 1, 16, 16, 9, 10, 11, kSequencePointKind_Normal, 0, 11 },
	{ 110034, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 110034, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 110034, 1, 19, 19, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 110034, 1, 20, 25, 13, 15, 1, kSequencePointKind_Normal, 0, 15 },
	{ 110034, 1, 20, 25, 13, 15, 9, kSequencePointKind_StepOut, 0, 16 },
	{ 110034, 1, 20, 25, 13, 15, 15, kSequencePointKind_StepOut, 0, 17 },
	{ 110034, 1, 27, 27, 13, 28, 21, kSequencePointKind_Normal, 0, 18 },
	{ 110034, 1, 28, 28, 9, 10, 25, kSequencePointKind_Normal, 0, 19 },
	{ 110035, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 20 },
	{ 110035, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 21 },
	{ 110035, 1, 31, 31, 9, 10, 0, kSequencePointKind_Normal, 0, 22 },
	{ 110035, 1, 32, 37, 13, 15, 1, kSequencePointKind_Normal, 0, 23 },
	{ 110035, 1, 32, 37, 13, 15, 8, kSequencePointKind_StepOut, 0, 24 },
	{ 110035, 1, 32, 37, 13, 15, 14, kSequencePointKind_StepOut, 0, 25 },
	{ 110035, 1, 32, 37, 13, 15, 20, kSequencePointKind_StepOut, 0, 26 },
	{ 110035, 1, 39, 39, 13, 28, 26, kSequencePointKind_Normal, 0, 27 },
	{ 110035, 1, 40, 40, 9, 10, 30, kSequencePointKind_Normal, 0, 28 },
	{ 110036, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 29 },
	{ 110036, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 30 },
	{ 110036, 1, 43, 43, 9, 10, 0, kSequencePointKind_Normal, 0, 31 },
	{ 110036, 1, 44, 49, 13, 15, 1, kSequencePointKind_Normal, 0, 32 },
	{ 110036, 1, 44, 49, 13, 15, 10, kSequencePointKind_StepOut, 0, 33 },
	{ 110036, 1, 44, 49, 13, 15, 16, kSequencePointKind_StepOut, 0, 34 },
	{ 110036, 1, 51, 51, 13, 28, 22, kSequencePointKind_Normal, 0, 35 },
	{ 110036, 1, 52, 52, 9, 10, 26, kSequencePointKind_Normal, 0, 36 },
	{ 110037, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 37 },
	{ 110037, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 38 },
	{ 110037, 1, 55, 55, 9, 10, 0, kSequencePointKind_Normal, 0, 39 },
	{ 110037, 1, 56, 61, 13, 15, 1, kSequencePointKind_Normal, 0, 40 },
	{ 110037, 1, 56, 61, 13, 15, 8, kSequencePointKind_StepOut, 0, 41 },
	{ 110037, 1, 56, 61, 13, 15, 15, kSequencePointKind_StepOut, 0, 42 },
	{ 110037, 1, 56, 61, 13, 15, 21, kSequencePointKind_StepOut, 0, 43 },
	{ 110037, 1, 63, 63, 13, 28, 27, kSequencePointKind_Normal, 0, 44 },
	{ 110037, 1, 64, 64, 9, 10, 31, kSequencePointKind_Normal, 0, 45 },
	{ 110038, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 46 },
	{ 110038, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 47 },
	{ 110038, 1, 67, 67, 9, 10, 0, kSequencePointKind_Normal, 0, 48 },
	{ 110038, 1, 68, 73, 13, 15, 1, kSequencePointKind_Normal, 0, 49 },
	{ 110038, 1, 68, 73, 13, 15, 10, kSequencePointKind_StepOut, 0, 50 },
	{ 110038, 1, 68, 73, 13, 15, 16, kSequencePointKind_StepOut, 0, 51 },
	{ 110038, 1, 75, 75, 13, 28, 22, kSequencePointKind_Normal, 0, 52 },
	{ 110038, 1, 76, 76, 9, 10, 26, kSequencePointKind_Normal, 0, 53 },
	{ 110039, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 54 },
	{ 110039, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 55 },
	{ 110039, 1, 79, 79, 9, 10, 0, kSequencePointKind_Normal, 0, 56 },
	{ 110039, 1, 80, 85, 13, 15, 1, kSequencePointKind_Normal, 0, 57 },
	{ 110039, 1, 80, 85, 13, 15, 8, kSequencePointKind_StepOut, 0, 58 },
	{ 110039, 1, 80, 85, 13, 15, 15, kSequencePointKind_StepOut, 0, 59 },
	{ 110039, 1, 80, 85, 13, 15, 21, kSequencePointKind_StepOut, 0, 60 },
	{ 110039, 1, 87, 87, 13, 28, 27, kSequencePointKind_Normal, 0, 61 },
	{ 110039, 1, 88, 88, 9, 10, 31, kSequencePointKind_Normal, 0, 62 },
	{ 110040, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 63 },
	{ 110040, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 64 },
	{ 110040, 1, 91, 91, 9, 10, 0, kSequencePointKind_Normal, 0, 65 },
	{ 110040, 1, 92, 97, 13, 15, 1, kSequencePointKind_Normal, 0, 66 },
	{ 110040, 1, 92, 97, 13, 15, 10, kSequencePointKind_StepOut, 0, 67 },
	{ 110040, 1, 92, 97, 13, 15, 16, kSequencePointKind_StepOut, 0, 68 },
	{ 110040, 1, 99, 99, 13, 28, 22, kSequencePointKind_Normal, 0, 69 },
	{ 110040, 1, 100, 100, 9, 10, 26, kSequencePointKind_Normal, 0, 70 },
	{ 110041, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 71 },
	{ 110041, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 72 },
	{ 110041, 1, 103, 103, 9, 10, 0, kSequencePointKind_Normal, 0, 73 },
	{ 110041, 1, 104, 109, 13, 15, 1, kSequencePointKind_Normal, 0, 74 },
	{ 110041, 1, 104, 109, 13, 15, 8, kSequencePointKind_StepOut, 0, 75 },
	{ 110041, 1, 104, 109, 13, 15, 15, kSequencePointKind_StepOut, 0, 76 },
	{ 110041, 1, 104, 109, 13, 15, 21, kSequencePointKind_StepOut, 0, 77 },
	{ 110041, 1, 111, 111, 13, 28, 27, kSequencePointKind_Normal, 0, 78 },
	{ 110041, 1, 112, 112, 9, 10, 31, kSequencePointKind_Normal, 0, 79 },
	{ 110044, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 80 },
	{ 110044, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 81 },
	{ 110044, 2, 16, 16, 9, 10, 0, kSequencePointKind_Normal, 0, 82 },
	{ 110044, 2, 17, 17, 13, 44, 1, kSequencePointKind_Normal, 0, 83 },
	{ 110044, 2, 17, 17, 13, 44, 5, kSequencePointKind_StepOut, 0, 84 },
	{ 110044, 2, 18, 18, 9, 10, 15, kSequencePointKind_Normal, 0, 85 },
	{ 110045, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 86 },
	{ 110045, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 87 },
	{ 110045, 2, 21, 21, 9, 10, 0, kSequencePointKind_Normal, 0, 88 },
	{ 110045, 2, 22, 22, 13, 62, 1, kSequencePointKind_Normal, 0, 89 },
	{ 110045, 2, 22, 22, 13, 62, 8, kSequencePointKind_StepOut, 0, 90 },
	{ 110045, 2, 23, 23, 9, 10, 18, kSequencePointKind_Normal, 0, 91 },
	{ 110046, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 92 },
	{ 110046, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 93 },
	{ 110046, 2, 25, 25, 9, 64, 0, kSequencePointKind_Normal, 0, 94 },
	{ 110046, 2, 25, 25, 9, 64, 1, kSequencePointKind_StepOut, 0, 95 },
	{ 110046, 2, 26, 26, 9, 10, 7, kSequencePointKind_Normal, 0, 96 },
	{ 110046, 2, 27, 27, 13, 49, 8, kSequencePointKind_Normal, 0, 97 },
	{ 110046, 2, 27, 27, 13, 49, 11, kSequencePointKind_StepOut, 0, 98 },
	{ 110046, 2, 28, 28, 9, 10, 17, kSequencePointKind_Normal, 0, 99 },
	{ 110047, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 100 },
	{ 110047, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 101 },
	{ 110047, 2, 30, 30, 9, 78, 0, kSequencePointKind_Normal, 0, 102 },
	{ 110047, 2, 30, 30, 9, 78, 1, kSequencePointKind_StepOut, 0, 103 },
	{ 110047, 2, 31, 31, 9, 10, 7, kSequencePointKind_Normal, 0, 104 },
	{ 110047, 2, 32, 32, 13, 90, 8, kSequencePointKind_Normal, 0, 105 },
	{ 110047, 2, 32, 32, 13, 90, 19, kSequencePointKind_StepOut, 0, 106 },
	{ 110047, 2, 32, 32, 13, 90, 25, kSequencePointKind_StepOut, 0, 107 },
	{ 110047, 2, 33, 33, 9, 10, 31, kSequencePointKind_Normal, 0, 108 },
	{ 110048, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 109 },
	{ 110048, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 110 },
	{ 110048, 2, 35, 35, 9, 78, 0, kSequencePointKind_Normal, 0, 111 },
	{ 110048, 2, 35, 35, 9, 78, 1, kSequencePointKind_StepOut, 0, 112 },
	{ 110048, 2, 36, 36, 9, 10, 7, kSequencePointKind_Normal, 0, 113 },
	{ 110048, 2, 37, 37, 13, 65, 8, kSequencePointKind_Normal, 0, 114 },
	{ 110048, 2, 37, 37, 13, 65, 17, kSequencePointKind_StepOut, 0, 115 },
	{ 110048, 2, 38, 38, 9, 10, 23, kSequencePointKind_Normal, 0, 116 },
	{ 110049, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 117 },
	{ 110049, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 118 },
	{ 110049, 2, 40, 40, 9, 91, 0, kSequencePointKind_Normal, 0, 119 },
	{ 110049, 2, 40, 40, 9, 91, 1, kSequencePointKind_StepOut, 0, 120 },
	{ 110049, 2, 41, 41, 9, 10, 7, kSequencePointKind_Normal, 0, 121 },
	{ 110049, 2, 42, 42, 13, 67, 8, kSequencePointKind_Normal, 0, 122 },
	{ 110049, 2, 42, 42, 13, 67, 14, kSequencePointKind_StepOut, 0, 123 },
	{ 110049, 2, 43, 43, 9, 10, 20, kSequencePointKind_Normal, 0, 124 },
	{ 110050, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 125 },
	{ 110050, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 126 },
	{ 110050, 2, 45, 45, 9, 96, 0, kSequencePointKind_Normal, 0, 127 },
	{ 110050, 2, 45, 45, 9, 96, 1, kSequencePointKind_StepOut, 0, 128 },
	{ 110050, 2, 46, 46, 9, 10, 7, kSequencePointKind_Normal, 0, 129 },
	{ 110050, 2, 47, 47, 13, 93, 8, kSequencePointKind_Normal, 0, 130 },
	{ 110050, 2, 47, 47, 13, 93, 12, kSequencePointKind_StepOut, 0, 131 },
	{ 110050, 2, 47, 47, 13, 93, 19, kSequencePointKind_StepOut, 0, 132 },
	{ 110050, 2, 47, 47, 13, 93, 25, kSequencePointKind_StepOut, 0, 133 },
	{ 110050, 2, 48, 48, 9, 10, 31, kSequencePointKind_Normal, 0, 134 },
	{ 110051, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 135 },
	{ 110051, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 136 },
	{ 110051, 2, 51, 51, 9, 10, 0, kSequencePointKind_Normal, 0, 137 },
	{ 110051, 2, 52, 52, 13, 106, 1, kSequencePointKind_Normal, 0, 138 },
	{ 110051, 2, 52, 52, 13, 106, 6, kSequencePointKind_StepOut, 0, 139 },
	{ 110052, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 140 },
	{ 110052, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 141 },
	{ 110052, 2, 56, 56, 9, 10, 0, kSequencePointKind_Normal, 0, 142 },
	{ 110052, 2, 57, 57, 13, 104, 1, kSequencePointKind_Normal, 0, 143 },
	{ 110052, 2, 57, 57, 13, 104, 6, kSequencePointKind_StepOut, 0, 144 },
	{ 110057, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 145 },
	{ 110057, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 146 },
	{ 110057, 2, 67, 67, 9, 10, 0, kSequencePointKind_Normal, 0, 147 },
	{ 110057, 2, 68, 68, 13, 86, 1, kSequencePointKind_Normal, 0, 148 },
	{ 110057, 2, 68, 68, 13, 86, 2, kSequencePointKind_StepOut, 0, 149 },
	{ 110057, 2, 68, 68, 13, 86, 7, kSequencePointKind_StepOut, 0, 150 },
	{ 110057, 2, 69, 69, 9, 10, 15, kSequencePointKind_Normal, 0, 151 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAssetBundleModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_UnityWebRequestAssetBundleModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UnityWebRequestAssetBundle/UnityWebRequestAssetBundle.cs", { 43, 59, 30, 199, 31, 44, 42, 123, 118, 187, 5, 96, 49, 79, 186, 7} },
{ "/Users/<USER>/build/output/unity/unity/Modules/UnityWebRequestAssetBundle/Public/DownloadHandlerAssetBundle.bindings.cs", { 172, 106, 229, 19, 162, 150, 132, 180, 165, 5, 112, 231, 114, 44, 77, 232} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[2] = 
{
	{ 14146, 1 },
	{ 14147, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[11] = 
{
	{ 0, 13 },
	{ 0, 13 },
	{ 0, 27 },
	{ 0, 32 },
	{ 0, 28 },
	{ 0, 33 },
	{ 0, 28 },
	{ 0, 33 },
	{ 0, 28 },
	{ 0, 33 },
	{ 0, 17 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[27] = 
{
	{ 13, 0, 1 },
	{ 13, 1, 1 },
	{ 27, 2, 1 },
	{ 32, 3, 1 },
	{ 28, 4, 1 },
	{ 33, 5, 1 },
	{ 28, 6, 1 },
	{ 33, 7, 1 },
	{ 28, 8, 1 },
	{ 33, 9, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 17, 10, 1 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestAssetBundleModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityWebRequestAssetBundleModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	152,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_UnityWebRequestAssetBundleModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	2,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
