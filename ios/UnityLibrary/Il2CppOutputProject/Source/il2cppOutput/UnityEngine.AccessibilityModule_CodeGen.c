﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61 (void);
extern void VisionUtility_GetLuminanceValuesForPalette_m4562F0DB7287E02F2F5A49C232BAD595AB12909A (void);
extern void VisionUtility_GetColorBlindSafePalette_m5F26D6E833D631BD408C92252C820E35E90CF5B8 (void);
extern void VisionUtility_GetColorBlindSafePalette_m9040BE9D231EC79FC3EB7B7260C52171F5D7D073 (void);
extern void VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3 (void);
extern void VisionUtility__cctor_m35662172C08EDF4AD2EAEF1C163AD895D9680887 (void);
extern void U3CU3Ec__cctor_m706C606C247042784FA728C5CFDC047D23FC4397 (void);
extern void U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23 (void);
extern void U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00 (void);
extern void U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0 (void);
extern void U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419 (void);
static Il2CppMethodPointer s_methodPointers[12] = 
{
	VisionUtility_ComputePerceivedLuminance_mA222FB3202943AB9537487D3D35BF02EA2BA0C61,
	VisionUtility_GetLuminanceValuesForPalette_m4562F0DB7287E02F2F5A49C232BAD595AB12909A,
	VisionUtility_GetColorBlindSafePalette_m5F26D6E833D631BD408C92252C820E35E90CF5B8,
	VisionUtility_GetColorBlindSafePalette_m9040BE9D231EC79FC3EB7B7260C52171F5D7D073,
	VisionUtility_GetColorBlindSafePaletteInternal_mD50EEB7A36AA747A5CD4B253E274BCC3F0A280B3,
	VisionUtility__cctor_m35662172C08EDF4AD2EAEF1C163AD895D9680887,
	U3CU3Ec__cctor_m706C606C247042784FA728C5CFDC047D23FC4397,
	U3CU3Ec__ctor_m375D9FA45BBCEEC94E1321DE73848F78861ABF23,
	U3CU3Ec_U3CGetColorBlindSafePaletteInternalU3Eb__6_1_m195E3E4E3C265158BC3372E5074C577C22E4CDA3,
	U3CU3Ec_U3C_cctorU3Eb__7_0_m00C6C9C42D59EEF7ECCA2A2E896F679157689D00,
	U3CU3Ec__DisplayClass6_0__ctor_m27E74D627F1FED046499F34EDEA5EA5E89E0B0E0,
	U3CU3Ec__DisplayClass6_0_U3CGetColorBlindSafePaletteInternalU3Eb__0_mA7E1D4B71CD092A0C36B3775CD1F86C5A2517419,
};
static const int32_t s_InvokerIndices[12] = 
{
	8618,
	7961,
	6539,
	6539,
	5250,
	9089,
	9089,
	4364,
	3312,
	3578,
	4364,
	3166,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_AccessibilityModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AccessibilityModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AccessibilityModule_CodeGenModule = 
{
	"UnityEngine.AccessibilityModule.dll",
	12,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_AccessibilityModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
