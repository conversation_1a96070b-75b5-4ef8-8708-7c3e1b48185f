﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[5] = 
{
	{ 4864, 0,  1 },
	{ 26173, 0,  2 },
	{ 15873, 1,  2 },
	{ 5020, 0,  3 },
	{ 6974, 0,  4 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[2] = 
{
	"callback",
	"eventBufferPtr",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[26] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 1, 2 },
	{ 3, 1 },
	{ 4, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_InputModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_InputModule[82] = 
{
	{ 110010, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 110010, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 110010, 1, 51, 51, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 110010, 1, 52, 52, 13, 30, 1, kSequencePointKind_Normal, 0, 3 },
	{ 110010, 1, 53, 53, 13, 52, 8, kSequencePointKind_Normal, 0, 4 },
	{ 110010, 1, 54, 54, 13, 46, 16, kSequencePointKind_Normal, 0, 5 },
	{ 110010, 1, 55, 55, 13, 30, 24, kSequencePointKind_Normal, 0, 6 },
	{ 110010, 1, 56, 56, 13, 30, 31, kSequencePointKind_Normal, 0, 7 },
	{ 110010, 1, 57, 57, 9, 10, 39, kSequencePointKind_Normal, 0, 8 },
	{ 110011, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 9 },
	{ 110011, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 10 },
	{ 110011, 1, 85, 85, 17, 18, 0, kSequencePointKind_Normal, 0, 11 },
	{ 110011, 1, 85, 85, 19, 55, 1, kSequencePointKind_Normal, 0, 12 },
	{ 110011, 1, 85, 85, 56, 57, 9, kSequencePointKind_Normal, 0, 13 },
	{ 110012, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 14 },
	{ 110012, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 15 },
	{ 110012, 1, 87, 87, 13, 14, 0, kSequencePointKind_Normal, 0, 16 },
	{ 110012, 1, 88, 88, 17, 54, 1, kSequencePointKind_Normal, 0, 17 },
	{ 110012, 1, 89, 89, 17, 84, 7, kSequencePointKind_Normal, 0, 18 },
	{ 110012, 1, 89, 89, 17, 84, 15, kSequencePointKind_StepOut, 0, 19 },
	{ 110012, 1, 90, 90, 13, 14, 21, kSequencePointKind_Normal, 0, 20 },
	{ 110013, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 21 },
	{ 110013, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 22 },
	{ 110013, 1, 94, 94, 9, 10, 0, kSequencePointKind_Normal, 0, 23 },
	{ 110013, 1, 97, 97, 13, 49, 1, kSequencePointKind_Normal, 0, 24 },
	{ 110013, 1, 97, 97, 13, 49, 2, kSequencePointKind_StepOut, 0, 25 },
	{ 110013, 1, 98, 98, 9, 10, 8, kSequencePointKind_Normal, 0, 26 },
	{ 110014, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 27 },
	{ 110014, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 28 },
	{ 110014, 1, 102, 102, 9, 10, 0, kSequencePointKind_Normal, 0, 29 },
	{ 110014, 1, 103, 103, 13, 66, 1, kSequencePointKind_Normal, 0, 30 },
	{ 110014, 1, 104, 104, 13, 34, 7, kSequencePointKind_Normal, 0, 31 },
	{ 110014, 1, 104, 104, 0, 0, 12, kSequencePointKind_Normal, 0, 32 },
	{ 110014, 1, 105, 105, 17, 38, 15, kSequencePointKind_Normal, 0, 33 },
	{ 110014, 1, 105, 105, 17, 38, 17, kSequencePointKind_StepOut, 0, 34 },
	{ 110014, 1, 106, 106, 9, 10, 23, kSequencePointKind_Normal, 0, 35 },
	{ 110015, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 36 },
	{ 110015, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 37 },
	{ 110015, 1, 110, 110, 9, 10, 0, kSequencePointKind_Normal, 0, 38 },
	{ 110015, 1, 111, 111, 13, 54, 1, kSequencePointKind_Normal, 0, 39 },
	{ 110015, 1, 112, 112, 13, 83, 7, kSequencePointKind_Normal, 0, 40 },
	{ 110015, 1, 112, 112, 13, 83, 9, kSequencePointKind_StepOut, 0, 41 },
	{ 110015, 1, 113, 113, 13, 34, 15, kSequencePointKind_Normal, 0, 42 },
	{ 110015, 1, 113, 113, 0, 0, 20, kSequencePointKind_Normal, 0, 43 },
	{ 110015, 1, 114, 114, 13, 14, 23, kSequencePointKind_Normal, 0, 44 },
	{ 110015, 1, 115, 115, 17, 48, 24, kSequencePointKind_Normal, 0, 45 },
	{ 110015, 1, 116, 116, 17, 49, 31, kSequencePointKind_Normal, 0, 46 },
	{ 110015, 1, 117, 117, 13, 14, 38, kSequencePointKind_Normal, 0, 47 },
	{ 110015, 1, 117, 117, 0, 0, 39, kSequencePointKind_Normal, 0, 48 },
	{ 110015, 1, 119, 119, 13, 14, 41, kSequencePointKind_Normal, 0, 49 },
	{ 110015, 1, 120, 120, 17, 54, 42, kSequencePointKind_Normal, 0, 50 },
	{ 110015, 1, 120, 120, 17, 54, 45, kSequencePointKind_StepOut, 0, 51 },
	{ 110015, 1, 121, 121, 13, 14, 51, kSequencePointKind_Normal, 0, 52 },
	{ 110015, 1, 122, 122, 9, 10, 52, kSequencePointKind_Normal, 0, 53 },
	{ 110016, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 54 },
	{ 110016, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 55 },
	{ 110016, 1, 126, 126, 9, 10, 0, kSequencePointKind_Normal, 0, 56 },
	{ 110016, 1, 127, 127, 13, 84, 1, kSequencePointKind_Normal, 0, 57 },
	{ 110016, 1, 128, 128, 13, 34, 7, kSequencePointKind_Normal, 0, 58 },
	{ 110016, 1, 128, 128, 0, 0, 12, kSequencePointKind_Normal, 0, 59 },
	{ 110016, 1, 129, 129, 17, 54, 15, kSequencePointKind_Normal, 0, 60 },
	{ 110016, 1, 129, 129, 17, 54, 18, kSequencePointKind_StepOut, 0, 61 },
	{ 110016, 1, 130, 130, 9, 10, 24, kSequencePointKind_Normal, 0, 62 },
	{ 110017, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 63 },
	{ 110017, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 64 },
	{ 110017, 1, 134, 134, 9, 10, 0, kSequencePointKind_Normal, 0, 65 },
	{ 110017, 1, 135, 135, 13, 72, 1, kSequencePointKind_Normal, 0, 66 },
	{ 110017, 1, 136, 136, 13, 69, 7, kSequencePointKind_Normal, 0, 67 },
	{ 110017, 1, 136, 136, 13, 69, 16, kSequencePointKind_StepOut, 0, 68 },
	{ 110017, 1, 137, 137, 9, 10, 22, kSequencePointKind_Normal, 0, 69 },
	{ 110022, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 70 },
	{ 110022, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 71 },
	{ 110022, 2, 28, 28, 9, 10, 0, kSequencePointKind_Normal, 0, 72 },
	{ 110022, 2, 29, 29, 13, 91, 1, kSequencePointKind_Normal, 0, 73 },
	{ 110022, 2, 29, 29, 13, 91, 2, kSequencePointKind_StepOut, 0, 74 },
	{ 110022, 2, 29, 29, 13, 91, 7, kSequencePointKind_StepOut, 0, 75 },
	{ 110022, 2, 29, 29, 13, 91, 12, kSequencePointKind_StepOut, 0, 76 },
	{ 110022, 2, 30, 30, 9, 10, 18, kSequencePointKind_Normal, 0, 77 },
	{ 110028, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 78 },
	{ 110028, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 79 },
	{ 110028, 2, 45, 45, 9, 10, 0, kSequencePointKind_Normal, 0, 80 },
	{ 110028, 2, 46, 46, 9, 10, 1, kSequencePointKind_Normal, 0, 81 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_InputModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_InputModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Input/Private/Input.cs", { 103, 159, 142, 190, 1, 0, 47, 209, 127, 197, 149, 83, 247, 243, 157, 8} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Input/Private/InputModule.bindings.cs", { 207, 145, 69, 51, 144, 144, 40, 63, 34, 147, 6, 119, 27, 7, 224, 204} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[3] = 
{
	{ 14142, 1 },
	{ 14144, 1 },
	{ 14144, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[5] = 
{
	{ 0, 11 },
	{ 0, 24 },
	{ 0, 53 },
	{ 0, 25 },
	{ 0, 23 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[26] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 11, 0, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 24, 1, 1 },
	{ 53, 2, 1 },
	{ 25, 3, 1 },
	{ 23, 4, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_InputModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_InputModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	82,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_InputModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	3,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
