﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Terrain_get_terrainData_m3B6C1D89471A4E1C60FC19C168DB37A011B924FD (void);
extern void Terrain_set_terrainData_m59F63BECF7DC2657DC887F1F59DA3427ED59994C (void);
extern void Terrain_get_treeDistance_mE5394C9AAD12F1BD5474B51615D2E3906404F77A (void);
extern void Terrain_set_treeDistance_m8836D1691B1C7BDC76725A624601E6543806C14C (void);
extern void Terrain_get_treeBillboardDistance_m5A9D3F3B388042BE9C19F346426AA7BA01CBE1D8 (void);
extern void Terrain_set_treeBillboardDistance_m654B35A6BEE23489F2E4DEF0D12214D9F4A5FD8E (void);
extern void Terrain_get_treeCrossFadeLength_m037BF60679DC6C878F5D7B5189F4555CF496AFD4 (void);
extern void Terrain_set_treeCrossFadeLength_m639B8CAD49CF4AF938477D3F1E080932F2DBE6F1 (void);
extern void Terrain_get_treeMaximumFullLODCount_m3626EB8E365A9B8620375AEB15604D112F669812 (void);
extern void Terrain_set_treeMaximumFullLODCount_mCDE91147B62F207B2841D41A2710A4B563A953D1 (void);
extern void Terrain_get_detailObjectDistance_m230A961919F3B468A7F79312C164C1D50C648EBA (void);
extern void Terrain_set_detailObjectDistance_mE8B84B8EDE307BEB41E477CC9C35F8BA3A969EDE (void);
extern void Terrain_get_detailObjectDensity_m2FEBE42D389A14F98A38888A423B21FD91E605E9 (void);
extern void Terrain_set_detailObjectDensity_mFDE71C06416A56C18C54ED41485CBB1D1CB3520D (void);
extern void Terrain_get_heightmapPixelError_m9051CAA3D0939B5F0F5719F53A3B5E4CF55438C5 (void);
extern void Terrain_set_heightmapPixelError_mEE31EA7AF7F41EB82C643468B52F4B10109C7FFF (void);
extern void Terrain_get_heightmapMaximumLOD_mC57749C8FF75F58EE1F209BBD26C2B1C5292C829 (void);
extern void Terrain_set_heightmapMaximumLOD_m0638986CDAF871DDD4C80EA55E1CF60F44DF6F5E (void);
extern void Terrain_get_heightmapMinimumLODSimplification_m319C4BAEFD45FAA988396F395F6D220487FFBA44 (void);
extern void Terrain_set_heightmapMinimumLODSimplification_mE9AF955106D0E1CB137AE372BF1E6C64E3BEDA6C (void);
extern void Terrain_get_basemapDistance_m19DDA753F682B416665C5A7E171376EEE3BF312B (void);
extern void Terrain_set_basemapDistance_m3CC75A7D5D67BA75A0E030F300A462C16905CF40 (void);
extern void Terrain_get_lightmapIndex_m79CF88EEEA60C4FACBA6D568C10A769D9654803E (void);
extern void Terrain_set_lightmapIndex_m771ADCC6B582144408D4736C93E97EFFE21319C4 (void);
extern void Terrain_get_realtimeLightmapIndex_m7DA20D23C390C3525A3245A8CF3340188541BB64 (void);
extern void Terrain_set_realtimeLightmapIndex_m1D972992A2DCBBD141D21DD089ADEB561CF6EA30 (void);
extern void Terrain_get_lightmapScaleOffset_m27B3E8FD4CF439470223ABF5F077AAC2A045480B (void);
extern void Terrain_set_lightmapScaleOffset_m5ABDFF3AAA8BC1243B976AD50A89AA864C8D218D (void);
extern void Terrain_get_realtimeLightmapScaleOffset_m028BC94F9D808480C241BD2D1A55F857E2D5287D (void);
extern void Terrain_set_realtimeLightmapScaleOffset_m599578984D728DC7EF3AE412879D7DC293A66E77 (void);
extern void Terrain_get_freeUnusedRenderingResources_mB7750A5195DFE7A96E99763E9EF0143D52124715 (void);
extern void Terrain_set_freeUnusedRenderingResources_mAFBEB163234FB5F808B453246765B980A8A28DDA (void);
extern void Terrain_get_keepUnusedRenderingResources_m795AD911467CB1E237543208A48A9E5DDFFF5032 (void);
extern void Terrain_set_keepUnusedRenderingResources_mFFE068865CA7F9EF1B875E0C1B0548A4D14E8C9F (void);
extern void Terrain_GetKeepUnusedCameraRenderingResources_m36AE2CC2933A8ED02A0A6B1581B1733C9309BA97 (void);
extern void Terrain_SetKeepUnusedCameraRenderingResources_m00A637BC1210C488F62A6D39A7E60EDD843371D0 (void);
extern void Terrain_get_shadowCastingMode_m4070042014ED55612368631D6335105FDF951114 (void);
extern void Terrain_set_shadowCastingMode_m1267E7389BEF6EBA9B6E23044225362CD6EA79BD (void);
extern void Terrain_get_reflectionProbeUsage_mAAFE9BC6BD45E280BF1EFD911184A630CC91C090 (void);
extern void Terrain_set_reflectionProbeUsage_m9D034C6790A0F28D118E330F590772E3240373B6 (void);
extern void Terrain_GetClosestReflectionProbes_m6DFCD7ACACF5566A37691DBDD466BFD0F96A0B29 (void);
extern void Terrain_get_materialTemplate_mB3BE06B73866CF772A9B2B42069CE3AECA288DE7 (void);
extern void Terrain_set_materialTemplate_mAC433DD496BAB83C63718B862C11064FD20EB7F0 (void);
extern void Terrain_get_drawHeightmap_m41EBA9E260C303324FD8A24A9FD155001D4715FE (void);
extern void Terrain_set_drawHeightmap_m39CA641057F8C3482CF7BADD65B1FD89106195F9 (void);
extern void Terrain_get_allowAutoConnect_m4E9CB87D130BB118517C5504C8FB4A5CC3AA91D7 (void);
extern void Terrain_set_allowAutoConnect_mBAA98DD489268404D1157A29759982CDFB45BF11 (void);
extern void Terrain_get_groupingID_mE52E78018126A5D00F837081287BE076E7709C24 (void);
extern void Terrain_set_groupingID_m0C89CE036FD16514CDC70B52270B853F7FCBF334 (void);
extern void Terrain_get_drawInstanced_m7E8C2EB30B4F778927EE017163B79B113598CB34 (void);
extern void Terrain_set_drawInstanced_m8846CAE84A778178A98DE6C0A764EBEA7B45D1F3 (void);
extern void Terrain_get_enableHeightmapRayTracing_mEC782BB01FE856D14FA6F8AA36DFFEA30F351409 (void);
extern void Terrain_set_enableHeightmapRayTracing_mD93ADA6B988DE9DC3B15E4C0254FFCAEE58F07BE (void);
extern void Terrain_get_normalmapTexture_m0B073CA3D5CD8E6176A8512B6B095F7BBE0D4C53 (void);
extern void Terrain_get_drawTreesAndFoliage_mD04FA3FDAAF4C49C875AFF6DA926531AD2190187 (void);
extern void Terrain_set_drawTreesAndFoliage_mCF36A66A7AC9072D3EC5706A9213CFC46D38689A (void);
extern void Terrain_get_patchBoundsMultiplier_m9FFCE6D0355BE5853182DAEF3DA4043EE3CC7435 (void);
extern void Terrain_set_patchBoundsMultiplier_mD5B6AED331E3843988403FD13BBC2E625344BAAC (void);
extern void Terrain_SampleHeight_m460F9060BC4D5F05275391A6AC05570047EF3177 (void);
extern void Terrain_AddTreeInstance_mD11FD23312CDF066606036F336EC317FC63B6436 (void);
extern void Terrain_SetNeighbors_m2FFA89D199120125D264EF7EE0BC749A35514C1E (void);
extern void Terrain_get_treeLODBiasMultiplier_m1BB2454CBE32BB3AE525E5F0FAD8F9363CBB7DCA (void);
extern void Terrain_set_treeLODBiasMultiplier_mD8287BC51A55A0B68C2066F3C22AD1C18AE863E5 (void);
extern void Terrain_get_collectDetailPatches_m0E86F73ABD8BDC8DE29A062D307BCEC4E03EE64B (void);
extern void Terrain_set_collectDetailPatches_m1FC681318E104B97274412E3C80F04FF90E6B564 (void);
extern void Terrain_get_ignoreQualitySettings_m173884E86DDCA1F511B1C4A59FC14BEA356693CB (void);
extern void Terrain_set_ignoreQualitySettings_m46E198F92258132EDBEA5491AADE5A3149EE9780 (void);
extern void Terrain_get_editorRenderFlags_mE55875C5D3E964CC1275FC4ACAF3674D9D8793F4 (void);
extern void Terrain_set_editorRenderFlags_m891E1F37730C9E3D6ABDED416B360A3EB0468C8A (void);
extern void Terrain_GetPosition_m5A1020F22CA4B1818E69A3B9687668AFAB2C43F5 (void);
extern void Terrain_Flush_m960CA9087AB6C18BE3C6B54DD993B5E728E5FA95 (void);
extern void Terrain_RemoveTrees_mD2D7E380123771264CEB2306594DE290AD604EB7 (void);
extern void Terrain_SetSplatMaterialPropertyBlock_mF7D441C25CA04812F7A95D5CDBAA6F4ACA6D7F0E (void);
extern void Terrain_GetSplatMaterialPropertyBlock_m12C9F2F14B6D92021E72AE994D438E2C6DF69FEF (void);
extern void Terrain_Internal_GetSplatMaterialPropertyBlock_m30EF43010BAE0F7F7AD19041E36A4F44F023D61E (void);
extern void Terrain_get_treeMotionVectorModeOverride_mDB25A728890AD3B16057940451AC9DE3ACBCE859 (void);
extern void Terrain_set_treeMotionVectorModeOverride_m24581C6A921EFDB811EA3D560664158D18AEF65D (void);
extern void Terrain_get_preserveTreePrototypeLayers_m8C45D508EBB7A14A25920093D24AFC66E2ACFBD6 (void);
extern void Terrain_set_preserveTreePrototypeLayers_mCBE60BF8EE44DA31170E32B8C31B64393A8EC4DA (void);
extern void Terrain_get_heightmapFormat_m7BDD479FF7710457999A405C226D2AEE901DDF87 (void);
extern void Terrain_get_heightmapTextureFormat_mD3B13DC4664DDB71E4C4CD1CE95ACC9DF35B9722 (void);
extern void Terrain_get_heightmapRenderTextureFormat_m81550AE5326309C22601E667FA4F5032DDE56BC5 (void);
extern void Terrain_get_normalmapFormat_m70C88E3838D7938A60BFBBEF5BB2EFCD99EBA716 (void);
extern void Terrain_get_normalmapTextureFormat_m4599EE3389898A7589ABC29F50DB337BC2F81BFC (void);
extern void Terrain_get_normalmapRenderTextureFormat_mE32604BFA382DEE5EF9E223FC5B0AED451FBB7A5 (void);
extern void Terrain_get_holesFormat_m3BCE4632B6CE7D1A058070DC65CEDEF5365838BE (void);
extern void Terrain_get_holesRenderTextureFormat_m960C437EC8DA9338ED56323263AD6F8B5CB6CD3B (void);
extern void Terrain_get_compressedHolesFormat_m386E6544EE29FC198609605B796C8DD06983658C (void);
extern void Terrain_get_compressedHolesTextureFormat_m1613558760307F7999EEFE230A1DFC612BE73A33 (void);
extern void Terrain_get_activeTerrain_mAE5A7FE933C2C1A57FC9542E9BFA315A413F224E (void);
extern void Terrain_SetConnectivityDirty_mE2F27F3B012109EC58BA2B37711EAAA9329CF91C (void);
extern void Terrain_get_activeTerrains_mB90A9BC89764F626D13F3EF1420EA8D3E186B701 (void);
extern void Terrain_GetActiveTerrains_mED4BDC0714F7743290CEC886012B04A81DC77686 (void);
extern void Terrain_Internal_FillActiveTerrainList_m00E77C57C0ED1C10647C734780A511DF56B50692 (void);
extern void Terrain_CreateTerrainGameObject_m31CBC26A8971667B09CC08C868EA31E8A5D29522 (void);
extern void Terrain_get_leftNeighbor_mA42E758BD513BC9E0FB87888FC24B3C4349A353E (void);
extern void Terrain_get_rightNeighbor_mA50D19F61FFBA544E94733B097D4409B15FECC17 (void);
extern void Terrain_get_topNeighbor_mCBC502C4CC0D617863D7A3A726EBFA4BD18BDF8C (void);
extern void Terrain_get_bottomNeighbor_m31306E01318F62D214B5E0966C988FB24255406F (void);
extern void Terrain_get_renderingLayerMask_m8A0C2E21E7DC988D9DF2E058DA231D0AA13ECC31 (void);
extern void Terrain_set_renderingLayerMask_m5F843E15CF24EF2BE27AEB21F1C9E6C846F2D249 (void);
extern void Terrain_get_splatmapDistance_m0FC2686DCFB5606FE515B9B7EC720FE92C96065E (void);
extern void Terrain_set_splatmapDistance_m863B50B87A1DF039426E79D916E690A1E2EAFAF3 (void);
extern void Terrain_get_castShadows_m3323F44BE250A712E8D34CE3C5D4EC7F1AD87BC7 (void);
extern void Terrain_set_castShadows_mEC211FA9586B718ED5436632A566DE8D6C0E26AB (void);
extern void Terrain_get_materialType_m4DA6FCD1D6E98FFB51B163E852CB904522B60FCB (void);
extern void Terrain_set_materialType_mC78AA6B3C29A28909943D2498C281CE882E8D50F (void);
extern void Terrain_get_legacySpecular_m9066C975D891182376AB99BEDDC9B66EAB841C30 (void);
extern void Terrain_set_legacySpecular_m8576A8BD0E10E6C5F88D324C317367522BEF7242 (void);
extern void Terrain_get_legacyShininess_mA4C410EBAF37DB0A6986890C41ED2627AE9846B7 (void);
extern void Terrain_set_legacyShininess_mA4716046912CE099D9B58B1CD70F2063455BE757 (void);
extern void Terrain_ApplyDelayedHeightmapModification_m7C23AC364F9431B7F890B47F8D3EFA3FB268DB01 (void);
extern void Terrain__ctor_m11F03EC6C1E68752DDCAE8EF2DED99CFD939FCDC (void);
extern void Terrain_get_lightmapScaleOffset_Injected_mD2EAABA59EC39ABCF49DAF4F0EA8273664B65562 (void);
extern void Terrain_set_lightmapScaleOffset_Injected_m6EE17634B3B017635B441AB3224BA8E3A1E5DE47 (void);
extern void Terrain_get_realtimeLightmapScaleOffset_Injected_m29ECF237E6B854DBF05C2BF365431CDAE3B4EDE4 (void);
extern void Terrain_set_realtimeLightmapScaleOffset_Injected_mF70656C179FC898C168A2F8AD8009D6CF21FE4FF (void);
extern void Terrain_get_patchBoundsMultiplier_Injected_m50860EC61F48B0DEB611BED7571AE29B436982F6 (void);
extern void Terrain_set_patchBoundsMultiplier_Injected_mD42F66A68FF0A7BE6E39B7C962EEEAC055F2E462 (void);
extern void Terrain_SampleHeight_Injected_m08851544EDBB88AFD061CD7E63E7995203BC7557 (void);
extern void Terrain_AddTreeInstance_Injected_m68720D1D8743CF74F8911B424DD9284C78563210 (void);
extern void Terrain_GetPosition_Injected_m23EC0958466016EFB553CFBBE1B6E4FB076FE419 (void);
extern void Terrain_RemoveTrees_Injected_m7401D70AB9B44261A015FB398996D1041CB46846 (void);
extern void TerrainExtensions_UpdateGIMaterials_m0D5B2CCB122576F254BEC7BCAF5F54DB77FB27D1 (void);
extern void TerrainExtensions_UpdateGIMaterials_m348138829150E8D140B19E7B1224F2B276C0E44D (void);
extern void TerrainExtensions_UpdateGIMaterialsForTerrain_mB0B0C183D5618E81C093CAFE3970CAE2F9170E3B (void);
extern void TerrainExtensions_UpdateGIMaterialsForTerrain_Injected_mFCCF2418F7BA12C50818453130292ABA074AF2A2 (void);
extern void Tree_get_data_mBFF1F514CA23A22FDBBCA1C2EC782793588A32DA (void);
extern void Tree_set_data_mAE6F594BA8D3A705CBC8956CEFD98CAE90C96A71 (void);
extern void Tree_get_hasSpeedTreeWind_mB50245BBCFFCA4A390C27A308899C0196A7B5344 (void);
extern void Tree__ctor_m634F3BDA4C40F235FDC59CD0D5428A788009FCDA (void);
extern void SpeedTreeWindAsset__ctor_m5F2944099EEA8B34A2D97820438C427797A67FE7 (void);
extern void TerrainCallbacks_add_heightmapChanged_m903E49435A5284E734F0A1F049183A9389672427 (void);
extern void TerrainCallbacks_remove_heightmapChanged_m922A2608D0BDFD0A4324592527E28DCEB633F17A (void);
extern void TerrainCallbacks_add_textureChanged_m511506C76D652D13E82174C8EA732497826350C7 (void);
extern void TerrainCallbacks_remove_textureChanged_m4B1C823F9DE53D68B8A69CCD7A5712DB946BCD27 (void);
extern void TerrainCallbacks_InvokeHeightmapChangedCallback_m731ED939CBD563CCCE503062602DF5908205AD04 (void);
extern void TerrainCallbacks_InvokeTextureChangedCallback_mB508E8B7A884854AA01AE5B88AB33E1AE40F4318 (void);
extern void HeightmapChangedCallback__ctor_m6A7E4189E0A7A1B70EE73818B93B0FC9F613648C (void);
extern void HeightmapChangedCallback_Invoke_m63C1C93709641DBE02DCE9F71B7895C5793AF875 (void);
extern void HeightmapChangedCallback_BeginInvoke_m5607A1E2C8633045D2C4EBFBDC704D6C5A57C5CC (void);
extern void HeightmapChangedCallback_EndInvoke_m3EB0F33689F26783A7C722DB0F001303146E335C (void);
extern void TextureChangedCallback__ctor_m64076D799FEB79E3D6BE2C4EB33CD081A398F0EF (void);
extern void TextureChangedCallback_Invoke_m1194A44102843272B51A70C302EBDBC8214647DE (void);
extern void TextureChangedCallback_BeginInvoke_mE0E6DAD71F05C26F1A2B546BB9126326F863B8DB (void);
extern void TextureChangedCallback_EndInvoke_m520E06126898CF212692FA47F5D722ECF814E5A5 (void);
extern void TreePrototype_get_prefab_mCE1630C35B09770D35B2ECA45B98D1CB6D5AC67C (void);
extern void TreePrototype_set_prefab_m795E5BAADC413A62B97AAA7B2742F2B09CD60E62 (void);
extern void TreePrototype_get_bendFactor_mC78774070395FFBEF5588233ED4C40D253F2B087 (void);
extern void TreePrototype_set_bendFactor_m28AF8D21C6BED89B59DCD1F3F79596D4A3C281CD (void);
extern void TreePrototype_get_navMeshLod_m68F7C292A64B7560076E09BF0B3AB6D681886C6C (void);
extern void TreePrototype_set_navMeshLod_m2F83E58E814E337E53CDF5EA7E32F209C72F8235 (void);
extern void TreePrototype__ctor_m319858B89E2F9AF0FD4009A015E2A34776F6CAC5 (void);
extern void TreePrototype__ctor_mB52731EBEFC3B50E5BA52AFFA32B39E069A15A1A (void);
extern void TreePrototype_Equals_m50E85BD703A2633D4ECA590DB0F1B803EE192F9A (void);
extern void TreePrototype_GetHashCode_m3E71334805650043E1C12F1FD6228D6281560E91 (void);
extern void TreePrototype_Equals_m6F39B894827A28E1ADBF4403922FFCA8CF55E265 (void);
extern void TreePrototype_Validate_m198AB043B5BFFA95D3CC3A38A8174EC7129BB054 (void);
extern void TreePrototype_ValidateTreePrototype_m4C45516FEDB674BCCD18283003836F38E15EDB82 (void);
extern void DetailPrototype_get_prototype_m4231116338BF5EA568B47405CD7626FA511EA695 (void);
extern void DetailPrototype_set_prototype_m3D231B05003B29C4FAE5146B42514F34C7D7F61C (void);
extern void DetailPrototype_get_prototypeTexture_m293614A486AE92BBAE7CA448B1A87BE5F64B4D7E (void);
extern void DetailPrototype_set_prototypeTexture_m2679A1548B564B70036AFC79413C99271AA76A82 (void);
extern void DetailPrototype_get_minWidth_m41AAF89F1E5EBFBD6D064684D6956F6796F8CE7C (void);
extern void DetailPrototype_set_minWidth_m35117222A238E215BC941F5F82E7A25D7BABF3DF (void);
extern void DetailPrototype_get_maxWidth_m428CF8DDC8FE3BB9E55051702ACBBA7431E8C073 (void);
extern void DetailPrototype_set_maxWidth_m481FAB59429BE8596BD8B4478D4853FCCBB7DC27 (void);
extern void DetailPrototype_get_minHeight_mD714BF9D18EBC2E2920908302EAFE31F014A44A8 (void);
extern void DetailPrototype_set_minHeight_mBD5DC44011EF8981E9256ED394D9FB6989193955 (void);
extern void DetailPrototype_get_maxHeight_m6048EC459FF0A728CDB5D2DD1169894F3A3A7C15 (void);
extern void DetailPrototype_set_maxHeight_mF56BB11D1C9AF4D3131D36FDB63795BCD6B9A5A6 (void);
extern void DetailPrototype_get_noiseSeed_mA4148AE5D207584CFDB9D9E266C8660FF358D49A (void);
extern void DetailPrototype_set_noiseSeed_m76256663CB2C2749D0A50099A4B0A1C53FEB869E (void);
extern void DetailPrototype_get_noiseSpread_m7FC289CF0B780679986A5395ECA4318FB696EBE1 (void);
extern void DetailPrototype_set_noiseSpread_mEDAF66AFB8F1423453A74CDE41E6EEC0CCDD14FB (void);
extern void DetailPrototype_get_density_m4F747BD95E4A6201BB26283DC7EC9BB92EC6AEB7 (void);
extern void DetailPrototype_set_density_m7C996AE5BDEE23F568392DE3BD51EB0C14C2A4A6 (void);
extern void DetailPrototype_get_bendFactor_mD10E7E587B192DE10321CF05014CE9ABAD581964 (void);
extern void DetailPrototype_set_bendFactor_m44FB373C8EC39A14C985EFCD910690CB63C31519 (void);
extern void DetailPrototype_get_holeEdgePadding_m2ED0A86DC359BFDC210BA493260466228169785D (void);
extern void DetailPrototype_set_holeEdgePadding_mC3EF2AA55AE1C51980B6D4FD01EEE78FE9CA282E (void);
extern void DetailPrototype_get_healthyColor_mD81828F1E2867C4819B38B0466ABAF2B10E10FD9 (void);
extern void DetailPrototype_set_healthyColor_m9CD3FB1504B33DD13ED6BDADC089AB984FE48FBA (void);
extern void DetailPrototype_get_dryColor_mCA70253238F53DC22FE0F6D6A644F6ED839A310F (void);
extern void DetailPrototype_set_dryColor_mC5BE4EC0A161311A8A864687B7341F347F8644E2 (void);
extern void DetailPrototype_get_renderMode_mE98D096AA932509AEF13C2AA75D34AFCA0AA1891 (void);
extern void DetailPrototype_set_renderMode_mFDE617505B0142B83B0FF74D43793CD684D3E087 (void);
extern void DetailPrototype_get_usePrototypeMesh_m3F21E15D7C815DC93B8C51A9F6AED7FBA5F6B4F8 (void);
extern void DetailPrototype_set_usePrototypeMesh_m77296758634404A1ABB97ED11733672F4EA446FD (void);
extern void DetailPrototype_get_useInstancing_m217E01AE7B95A6E0A721E5A9B3B4681C7D63BA61 (void);
extern void DetailPrototype_set_useInstancing_m21E73698E0A1A02B88FE13E508129B1B3AE16DEE (void);
extern void DetailPrototype_get_targetCoverage_mB025603135CDB7C5DFB3D41D7C29C38B33402333 (void);
extern void DetailPrototype_set_targetCoverage_m55157EACFBA98E5E6DF70EA93A8E69C0323A3C38 (void);
extern void DetailPrototype_get_useDensityScaling_mAC589A366333F4DB6C4B282A3B9F71D8147640C2 (void);
extern void DetailPrototype_set_useDensityScaling_m056FD505699CBEFB297232A2B53ECEFF14DD9236 (void);
extern void DetailPrototype_get_alignToGround_m88E88F6F4815EC485113A87211F765502F261DFE (void);
extern void DetailPrototype_set_alignToGround_mC2A9DA38FC3ECA4478F32CA9CCD44C2C2BD83189 (void);
extern void DetailPrototype_get_positionJitter_mDB281253889D02E1C2D90FEEBC478D7DC1FD09A3 (void);
extern void DetailPrototype_set_positionJitter_m8A2E2A209C7CEC4B0E0006A3D0C9EDA91E44B227 (void);
extern void DetailPrototype__ctor_m9157B5DBD1A80AF51E9090177A751EB401247103 (void);
extern void DetailPrototype__ctor_mA958B452872D2004C50CBFD24B5998FF933D5BA0 (void);
extern void DetailPrototype_Equals_m4366EA36A57FC1391A85828835FD5B493A573E93 (void);
extern void DetailPrototype_GetHashCode_m7C87FF388D92F7A9396DC46F1F4DB4AC436D9ED0 (void);
extern void DetailPrototype_Equals_mDCF0B4B10AB82B883A1B1EE9F286D92A99636B20 (void);
extern void DetailPrototype_Validate_mB3C8763387C029886F98ED4DDE9F9E6D5289095B (void);
extern void DetailPrototype_Validate_mDF3789F5839651A8C10D2FCC2BC69C102FBCB87A (void);
extern void DetailPrototype_ValidateDetailPrototype_mD3870D4E09EF61CDE045CC449B6749453F440DFA (void);
extern void DetailPrototype_IsModeSupportedByRenderPipeline_m5BCAB08F59B141CFAD3E314E7A0036B96A2B5765 (void);
extern void DetailPrototype__cctor_m5D5A4C448F2584173CF5618DD3CB725B16F6406F (void);
extern void SplatPrototype_get_texture_m10D1E021FA78ADB602A6B9DCF7D7C3108B5226EB (void);
extern void SplatPrototype_set_texture_m6810CF1FB35918B61BD81F5F857F3FF553D53E37 (void);
extern void SplatPrototype_get_normalMap_mB81C93E54EEDF854E3A6265259C627A80B3E76D3 (void);
extern void SplatPrototype_set_normalMap_mAA0B8894AEE5B8A8B52A61F12F4F3E8461DB98CE (void);
extern void SplatPrototype_get_tileSize_mFC64C788FD37AC90A3DADEA115E77476704C6496 (void);
extern void SplatPrototype_set_tileSize_mCBBFA459982E2D402A20DCF81BF6C8AA836370E2 (void);
extern void SplatPrototype_get_tileOffset_m5D7E37F7FDB7B6A2CD8A2323D2AF0B7CCE88FCFF (void);
extern void SplatPrototype_set_tileOffset_mE1408107A9BFB3C4F370E5143C37B225C1E607FC (void);
extern void SplatPrototype_get_specular_m4DBE720D7BF51F447B1C59AB008CE4FF777AB17B (void);
extern void SplatPrototype_set_specular_mA2BDD44AC681C851BEE37017A73B19FBCCC56BFD (void);
extern void SplatPrototype_get_metallic_mF45573E9F0187E619B352DCC1A43D8950A7B5544 (void);
extern void SplatPrototype_set_metallic_mB6FE9F3D7FA7C887307F8B6BD1D5994C63A41CAC (void);
extern void SplatPrototype_get_smoothness_m41F39F21541FFA167558900FC05F6508E2CBB519 (void);
extern void SplatPrototype_set_smoothness_m4F90E6490EC1AC09C362D772F2C57003BF746053 (void);
extern void SplatPrototype__ctor_m8F453C42984513A7B33BD5E9B728F4082B7D57A5 (void);
extern void PatchExtents_get_min_mCDE97D43190F69E3E33F187EF510D05E6BE36171 (void);
extern void PatchExtents_set_min_m767CBF5D34E86C328619AC9066FEE97A30D45254 (void);
extern void PatchExtents_get_max_m54D71762D0C9923352FC5ACF3AECA7FA88759FD6 (void);
extern void PatchExtents_set_max_mD20D2F8F79550A260A9A8AC21B46A4871B0423DE (void);
extern void TerrainData_GetBoundaryValue_mA9217CC15BBC958C9F7071B96CE74769EFDC322E (void);
extern void TerrainData__ctor_m1B68EB89248D5706C2528F47279812F824E27A2E (void);
extern void TerrainData_Internal_Create_m79BF764CFF5F49D17E2BFC8B20F60B4CF70BE4E1 (void);
extern void TerrainData_UpdateDirtyRegion_m6A9D35DED10DC4E5BD43B27D01022E697D6E5E11 (void);
extern void TerrainData_get_heightmapWidth_mBD11F8B3F89DD66B275CA91759534A22DCE070A6 (void);
extern void TerrainData_get_heightmapHeight_mDCB90912307BA444DCB3B21648050812E5278757 (void);
extern void TerrainData_get_heightmapTexture_m26CB863455F0048CEA74741C71BF3021576FF8CE (void);
extern void TerrainData_get_heightmapResolution_m39FE9A5C31A80B28021F8E2484EF5F2664798836 (void);
extern void TerrainData_set_heightmapResolution_m2E5AA9451129E9460D9573CCA899DC1F3CFFDD21 (void);
extern void TerrainData_get_internalHeightmapResolution_m83C6A32499AAAAFDD57DF73BA460CBCF02F98118 (void);
extern void TerrainData_set_internalHeightmapResolution_m18F96875BE176D13F26A55AEBB0343902D5C1B5A (void);
extern void TerrainData_get_heightmapScale_m4B6AB6495384109BA54955CA52B883A118015188 (void);
extern void TerrainData_get_holesTexture_m967249F842D3BFC631ED03C847C41EE4D0A56912 (void);
extern void TerrainData_get_enableHolesTextureCompression_m6BA8BB7840CEF3B832D7C64D91689BECBBA1E920 (void);
extern void TerrainData_set_enableHolesTextureCompression_mDD73FBE3EA89453DB6FA3BA236FB527B2F71EE7C (void);
extern void TerrainData_get_holesRenderTexture_m953821042F6D4FF04EB1FD861BBA604DED1F1A74 (void);
extern void TerrainData_IsHolesTextureCompressed_mBFE653BBF59360E5382F66592EF1F3E48ED4057D (void);
extern void TerrainData_GetHolesTexture_mE1BC7A225983716A760BA8ACEFEC6DEFF6EA572A (void);
extern void TerrainData_GetCompressedHolesTexture_m219472651551DC5C4613B441C27E504DA693411F (void);
extern void TerrainData_get_holesResolution_m9DE109FB3A0874F7F4706543D0903AFA12D1BC53 (void);
extern void TerrainData_get_size_mCD3977F344B9DEBFF61DD537D03FEB9473838DA5 (void);
extern void TerrainData_set_size_m4F3EF9EDDF9BE2E0903FE3A5121D8F4ABDA46DC1 (void);
extern void TerrainData_get_bounds_m9CE9B3BF067EA06A837AB98B5BC6C0C4669B5A32 (void);
extern void TerrainData_get_thickness_m7B81848297CCC307A8D171AAAF49F950A287F237 (void);
extern void TerrainData_set_thickness_m5D4165CE2D3C2952DB0EB26EBEF4AC75F5B03781 (void);
extern void TerrainData_GetHeight_mC0FAC93E5B8128D5B918EDDDFEE55F6E3E23E8D5 (void);
extern void TerrainData_GetInterpolatedHeight_m30AAF72C79B6BE19E23962304DB80B21023B5752 (void);
extern void TerrainData_GetInterpolatedHeights_mF8E7DEF026AB7676FC3BC35CE413A80D7A4DA648 (void);
extern void TerrainData_GetInterpolatedHeights_mC8D6FE42800B4480E1863D795445A4D1630D0E57 (void);
extern void TerrainData_Internal_GetInterpolatedHeights_m4794EE72628D345B4F3A7571806BCA0BE61B11AF (void);
extern void TerrainData_GetHeights_m3E5C109E98E72A23E39B92F7DF48D87888B2D488 (void);
extern void TerrainData_Internal_GetHeights_mEDF77233265AFA8901DE8FB61661385337B70810 (void);
extern void TerrainData_SetHeights_m104C6E5C4E4A12223AA0E2E123E0557302097F23 (void);
extern void TerrainData_Internal_SetHeights_m8E3E77B7A38E85521BF693D7EB3B9E277CDBE12E (void);
extern void TerrainData_GetPatchMinMaxHeights_mF44EB92C548A6218079F957B1387919E7C5B0334 (void);
extern void TerrainData_OverrideMinMaxPatchHeights_m6C6C7B6DC573747776B0E4DB710CAFF410AC9E4B (void);
extern void TerrainData_GetMaximumHeightError_m9BB2B3C5FFBDE5C38F4A858F75E1FF056BC66706 (void);
extern void TerrainData_OverrideMaximumHeightError_mEE18F9EF4470CD87A4130D37936975D2E4B7CDEC (void);
extern void TerrainData_SetHeightsDelayLOD_m72A8BE616AC1D0CFF5834ABE2654F8C172EA3FF1 (void);
extern void TerrainData_Internal_SetHeightsDelayLOD_m9DA0CBE8DE96316DC8AA8EBD652ECC42459B3658 (void);
extern void TerrainData_IsHole_m1C0EDB14F8EDCBF6AB6CFEFC52485B3E8FD04A7B (void);
extern void TerrainData_GetHoles_mE81BB7B6E8764586CC5BA130AC075536D0617318 (void);
extern void TerrainData_SetHoles_mDC03D926A9AD88D971A74C0343C4B0D765B612FA (void);
extern void TerrainData_SetHolesDelayLOD_m8397B5D2F861B7F41835E28D96E23CA7141B588B (void);
extern void TerrainData_Internal_SetHoles_m212D61E07A95ECAC00950284E590D6CAED57EC66 (void);
extern void TerrainData_Internal_GetHoles_m13EE36473247F7D4E96C627CE8A4CBF0D4C329C8 (void);
extern void TerrainData_Internal_IsHole_mF0ADC1C43497B05375BBAC01FD9764C86F7DA109 (void);
extern void TerrainData_Internal_SetHolesDelayLOD_m00B8068FAA00FC46BC464B19A58082F8BD05F6BC (void);
extern void TerrainData_GetSteepness_mA0AD10DFEA5D97CF63DBDB34D99E7A43640D93B9 (void);
extern void TerrainData_GetInterpolatedNormal_m2925F25FF12A4DC2F2CDD9331F3E2A55D42D7DCE (void);
extern void TerrainData_GetAdjustedSize_m17561CC6F50D3E0B7DBA34CA7D8C4F2D57ED5514 (void);
extern void TerrainData_get_wavingGrassStrength_mEE2A93E488F4349999D85D55DD6CD9DB626FF373 (void);
extern void TerrainData_set_wavingGrassStrength_m40D1A37A911BD8E430E0CE2FB20A1BDD7E899776 (void);
extern void TerrainData_get_wavingGrassAmount_mAE3A3E9E51DEE6F3062064D55F5FE90105FA18EA (void);
extern void TerrainData_set_wavingGrassAmount_m42B701F18798FEFCFF39AA29855D12A946AB47CC (void);
extern void TerrainData_get_wavingGrassSpeed_m4528E32081633762C16079370ADA8DDE1794AB75 (void);
extern void TerrainData_set_wavingGrassSpeed_m45C0014ECD806D1B8877B734F80BBF0E87D7A7D5 (void);
extern void TerrainData_get_wavingGrassTint_m9D14E1262509149850E101ABF9A4515AD44CFAE7 (void);
extern void TerrainData_set_wavingGrassTint_m5BC16B415EAB6493D1E8541FC8391E23E8E06F74 (void);
extern void TerrainData_get_detailWidth_m145CC1C91FF8C752907B80338DF03440E53AEBB4 (void);
extern void TerrainData_get_detailHeight_m1DBBB1664689DD08F64A9AF4023248F23865D304 (void);
extern void TerrainData_get_maxDetailScatterPerRes_m026B44FFA5D74E2AFEEC745D597244B473502851 (void);
extern void TerrainData_SetDetailResolution_mB4D4C5E5AAACFBB2C624006D93CA04FA22CDA76E (void);
extern void TerrainData_Internal_SetDetailResolution_mF138086D635E5D45AF0357589B4107706DD624D3 (void);
extern void TerrainData_SetDetailScatterMode_m4567232EA53D4B143108ECC6DBA4438BB28ECB33 (void);
extern void TerrainData_Internal_SetDetailScatterMode_mB43FA9629CA81B76A47545BD841E5CB622AFEB68 (void);
extern void TerrainData_get_detailPatchCount_m40F4FD2CE4A805B134F39CFD3C2602301A355B4D (void);
extern void TerrainData_get_detailResolution_m999CE72D34D858E7D35182FA8DA05CE3C92F8D72 (void);
extern void TerrainData_get_detailResolutionPerPatch_mFB05C93330007A6A3121327D203F404AD0600622 (void);
extern void TerrainData_get_detailScatterMode_m31914761E09DA99C2C34709C9BD76609F1077051 (void);
extern void TerrainData_ResetDirtyDetails_m9A648A3CBA741F3B7621B07B58F51C3B0F95C2F8 (void);
extern void TerrainData_RefreshPrototypes_mCB97A38BE96FAC08C2F2E8C1F38E88C7450CADBC (void);
extern void TerrainData_get_detailPrototypes_m057F428D22C9FCCD36C6BE6768263DE777C6B2C4 (void);
extern void TerrainData_set_detailPrototypes_m9C9DDFE5DBC3B789A70DAD6D06326C1140057A31 (void);
extern void TerrainData_GetSupportedLayers_m207D3CF276D6D6CC2F2E6BEF2271245C1DB99BD5 (void);
extern void TerrainData_GetSupportedLayers_mCE73B4AE3DAE8CC3F53771E6FE67BE34E9E78205 (void);
extern void TerrainData_GetDetailLayer_m8EB9B85C8CE8836E10D4D54B3A43BFE9AF888591 (void);
extern void TerrainData_GetDetailLayer_m9B343FA7ABE6C0F24714474A22CDBC7BF94204CC (void);
extern void TerrainData_ComputeDetailInstanceTransforms_m81F9EE9500D1C8F2777ADDFFC7D526B5FF445848 (void);
extern void TerrainData_ComputeDetailCoverage_mBE71CDF1F8B277AD7EDFB335C3C05FA0575A6038 (void);
extern void TerrainData_SetDetailLayer_m03F76CB703CB3277723319EBF29B4924E22ED84B (void);
extern void TerrainData_SetDetailLayer_mD4072ED0A4C79BB8A62DE669F75610DEBF442003 (void);
extern void TerrainData_Internal_SetDetailLayer_m782D0D38E2A1BDAEAF1F3E3C2D0282991019899F (void);
extern void TerrainData_get_treeInstances_mDAB68FD1F3677BD5CB122EA943493D5FC94B2147 (void);
extern void TerrainData_set_treeInstances_m4B62FA8A516D3839829AA50209897EDB7647F65D (void);
extern void TerrainData_Internal_GetTreeInstances_m0DCDC4D93E2CEC457C5BD8D0FE898B5A632E8347 (void);
extern void TerrainData_SetTreeInstances_m536FA2F30FE5085FCFB05F7BE8D26943214EAA02 (void);
extern void TerrainData_GetTreeInstance_m7D234B2CC46149BCA0F6B8D79BB749D8C61CC500 (void);
extern void TerrainData_Internal_GetTreeInstance_mB86AC8651E537E859C80986BF9B9124177B74CBA (void);
extern void TerrainData_SetTreeInstance_mADB51401F25DA608A9B9A0E21726BAEFCB2741A3 (void);
extern void TerrainData_get_treeInstanceCount_mB09A8EC0638DC245D035E2B864EAFEB7EB161982 (void);
extern void TerrainData_get_treePrototypes_m0A43789B50E554DACB5DF88C86DA23B89DB33EEB (void);
extern void TerrainData_set_treePrototypes_m5F309E7FFEB234DA8450C4CBD43EB7045C33BCAA (void);
extern void TerrainData_RemoveTreePrototype_mC1BA50DFCA75319E365AF435F518D11608D32EF0 (void);
extern void TerrainData_RemoveDetailPrototype_m8D48BCD1F68B474E2D2017F57E24793CFB8C2B52 (void);
extern void TerrainData_NeedUpgradeScaledTreePrototypes_mEAA3D3CDB2DB734F6A8B7BD6D9A564203103F6DB (void);
extern void TerrainData_UpgradeScaledTreePrototype_m3A266EA86CFE29A26FC989EDD1DF24C1FAB7E8F7 (void);
extern void TerrainData_get_alphamapLayers_mF8A0A4F157F7C56354C5A6E3FABF9F230F410F69 (void);
extern void TerrainData_GetAlphamaps_m2DEF5D2068D54BDAE78661483C1FC4936B06EA01 (void);
extern void TerrainData_Internal_GetAlphamaps_m6891C19FF72B4394D6BAC3B098A3FCAC1FC6BF36 (void);
extern void TerrainData_get_alphamapResolution_mC5D1C8FF4A5AFFCCFCF1382FED0D1AD46563D6F8 (void);
extern void TerrainData_set_alphamapResolution_mECF82D6CF9A3C640B18AB6DE14666BF380E03389 (void);
extern void TerrainData_GetAlphamapResolutionInternal_m5C312434763B8F0BD8DE760ACF439DFEFAC2F3E5 (void);
extern void TerrainData_get_Internal_alphamapResolution_mDA8EF6055A2022B3E1E4E6ECBF8DF4387DE8A930 (void);
extern void TerrainData_set_Internal_alphamapResolution_mA33B7F95DF603037846D76F6061355DDD1C8E556 (void);
extern void TerrainData_get_alphamapWidth_m07E5B04B08E87AC9F66D766B363000F94C8612D4 (void);
extern void TerrainData_get_alphamapHeight_m4A8273D6E0E3526A31E2669FBAB240353C086AED (void);
extern void TerrainData_get_baseMapResolution_mCD5E59C7A2778009CD4A4CA571B6B85CC0993C0C (void);
extern void TerrainData_set_baseMapResolution_mB1F3A79E687A45F695A716B8B7417B35E77C19A7 (void);
extern void TerrainData_get_Internal_baseMapResolution_m160A9621FE9E5BE642C8CE8C8DA64A07DDF5830F (void);
extern void TerrainData_set_Internal_baseMapResolution_mB7C108582B3A0100033DAA477D55A687405B5DD8 (void);
extern void TerrainData_SetAlphamaps_m2FA8E29ADF4D1B2B8286ED0FE61E0CEB1663E423 (void);
extern void TerrainData_Internal_SetAlphamaps_m87D35A2FED5E274AEBDF3ADB00A7FA477861E8FD (void);
extern void TerrainData_SetBaseMapDirty_m71323D916BC5353D2A357E91C5ECE55E65700618 (void);
extern void TerrainData_GetAlphamapTexture_mFA6A25F6C09FE64114F69D528046E78B1581366C (void);
extern void TerrainData_get_alphamapTextureCount_mF378908EA58CA135A4D58D586179418F1A6F14CD (void);
extern void TerrainData_get_alphamapTextures_m82B7287C772D95D4E3D6A5793A8B9A15745D8C45 (void);
extern void TerrainData_get_splatPrototypes_m349F72923854A74F3EE20D2F09452ED73852923E (void);
extern void TerrainData_set_splatPrototypes_m66D557939D09A1116BBAA1CD2E650EE68E0505E0 (void);
extern void TerrainData_get_terrainLayers_m3B436DF37DDD9F18A46DD6BF112925AD5B8857C8 (void);
extern void TerrainData_set_terrainLayers_m8FC80DAB5CD38A11CDB082F94DBD3186CD434671 (void);
extern void TerrainData_AddTree_m3DA41B91351531F932E4172A5E1DA1A5359FEA96 (void);
extern void TerrainData_RemoveTrees_m6600E1C337753D0C6B1B08AC2B4C1E6C46A314E4 (void);
extern void TerrainData_Internal_CopyActiveRenderTextureToHeightmap_m431A62A0EC8B28CE9CA58FB081A58123060EB378 (void);
extern void TerrainData_Internal_DirtyHeightmapRegion_mF080B77155696B5E1E0715540DE8031A3F421FC4 (void);
extern void TerrainData_SyncHeightmap_mD8D77E9DD9063B7201D062C810CFEAF2BE743267 (void);
extern void TerrainData_Internal_CopyActiveRenderTextureToHoles_m3181CE7CD4B40E3EB42B571E3489A2226C6E0A1A (void);
extern void TerrainData_Internal_DirtyHolesRegion_mBC52464648FFA679E45C579BCC3B9FFF68DD3EBA (void);
extern void TerrainData_Internal_SyncHoles_m536B2E7102335849E42E867DB27166BB55FED0F6 (void);
extern void TerrainData_Internal_MarkAlphamapDirtyRegion_mB212FE00063E5391813EA3C57C71AF5669F9C064 (void);
extern void TerrainData_Internal_ClearAlphamapDirtyRegion_m570C14031954B78348B93B24361EB0B570622588 (void);
extern void TerrainData_Internal_SyncAlphamaps_mFDFD1F09BB38C77A2F408331299CCF586FDC0A33 (void);
extern void TerrainData_get_atlasFormat_m68777C54DF215D47A6249076DD2A2CC0E123DAA9 (void);
extern void TerrainData_get_users_m0C569F1AD5853CEBF3C572723A1CAD04AC8433C4 (void);
extern void TerrainData_get_SupportsCopyTextureBetweenRTAndTexture_mE93A6D23586EABCFC4B1267F66E98A8C6C7E35BE (void);
extern void TerrainData_CopyActiveRenderTextureToHeightmap_m36D01BB8242D02F3E319271CA2BC2A6B5C428EE5 (void);
extern void TerrainData_DirtyHeightmapRegion_m92C3A0182EE87E484316B0514F25C3D106349AB7 (void);
extern void TerrainData_get_AlphamapTextureName_mC3E6B77E19252385A78C74D853F7CA4375A51882 (void);
extern void TerrainData_get_HolesTextureName_m050635081E2E26FDE05BFD35253710DE84E7A12F (void);
extern void TerrainData_CopyActiveRenderTextureToTexture_mC6FBAB316DCFA11F9A51A32F2943B01B0AA79A74 (void);
extern void TerrainData_DirtyTextureRegion_mB66EE109AB4B647EDDC49E3B7A2C8F1CA0215FA5 (void);
extern void TerrainData_SyncTexture_m4799EFAE1FACCB344F25855A7190C0CC3F8C9B2D (void);
extern void TerrainData__cctor_m525F8AF6DEDDACF640BD2D24767502121ED6D9B0 (void);
extern void TerrainData_get_heightmapScale_Injected_m3F94935D94B0B8029E2145C33B023AB8677C39AB (void);
extern void TerrainData_get_size_Injected_m0F56E87C4D7EDD1D84F038E4AF0F273D328CF661 (void);
extern void TerrainData_set_size_Injected_mF315143468DD64DB825C9D0A03EF491E035666D8 (void);
extern void TerrainData_get_bounds_Injected_mD98BEEB610CCB2688123B28111C35D3E6415BDE5 (void);
extern void TerrainData_GetInterpolatedNormal_Injected_mF6E35FCDF546E498658E864D47EB87C22B41E571 (void);
extern void TerrainData_get_wavingGrassTint_Injected_mDDE7C4FCA35CCFAF7D2674D225DC36B4D77CAD25 (void);
extern void TerrainData_set_wavingGrassTint_Injected_mDF8BCA497383058F5054AE782FFE9CAFE63FF142 (void);
extern void TerrainData_Internal_GetTreeInstance_Injected_mE634C00ADF80B11B02A395DB539C113CC2230119 (void);
extern void TerrainData_SetTreeInstance_Injected_m17BD0E8478078C994F5CCADD825CB03F334D62A2 (void);
extern void TerrainData_RemoveTrees_Injected_m52522817B1A5F12F9505DFD8F62D85E647DB9B8A (void);
extern void TerrainData_Internal_CopyActiveRenderTextureToHeightmap_Injected_mBCC5601037CF0D8C81E936AB9C6270C2E128EF7C (void);
extern void TerrainData_Internal_CopyActiveRenderTextureToHoles_Injected_m79962847A7F930086EE6436D8D0EDFABF3C3F967 (void);
extern void TerrainLayer__ctor_m3B6D12B4296D98B5813C08DF87D09022BD8FDC74 (void);
extern void TerrainLayer_Internal_Create_mA5EB262D9B9D9B3180705A14040FBEAA4257D3E1 (void);
extern void TerrainLayer_get_diffuseTexture_mAF75D09F08293C26B26D7D422B4A0ACC9732DD31 (void);
extern void TerrainLayer_set_diffuseTexture_mC21DEF81B6B58FD1BA8FA381FD569E2DB6173C53 (void);
extern void TerrainLayer_get_normalMapTexture_mCBC9DFA1E5243F5227A99A2D7CBF53BE259E9620 (void);
extern void TerrainLayer_set_normalMapTexture_m6E4BC16FFD3E9C2351E208F38CB1C8B99DD3F5AE (void);
extern void TerrainLayer_get_maskMapTexture_m184F1143BA11213B5B6E1D3487BCDF8A747954EA (void);
extern void TerrainLayer_set_maskMapTexture_m7A5384131F06DF24AB8F9B2D0E8C7A8510A17F78 (void);
extern void TerrainLayer_get_tileSize_mF6313141BE22D6AC7362DA9A40FB41236458E0A3 (void);
extern void TerrainLayer_set_tileSize_m53D498E2704EF4B92B11588CF47C726923D57F84 (void);
extern void TerrainLayer_get_tileOffset_m1E81B6D002E91BE4EF402337C595227221E8163D (void);
extern void TerrainLayer_set_tileOffset_m259D3D0F278A65D405923BF0338766BDA79556A9 (void);
extern void TerrainLayer_get_specular_m14209D88524F3739C026F906A8D34B3597EEE07A (void);
extern void TerrainLayer_set_specular_mE0E85F57BCD6D771826D57562E6EADDA4E14F538 (void);
extern void TerrainLayer_get_metallic_m99FCD988D3CE9CADBDD23C9E0C92095DB66F2EE8 (void);
extern void TerrainLayer_set_metallic_mAD23C76C3953D3A6D54CFC9B399A13841F8F1E87 (void);
extern void TerrainLayer_get_smoothness_m79BBB46810976A88A0BADB0D52A0C028781C7120 (void);
extern void TerrainLayer_set_smoothness_mB79F6272E5AA2F3F103B22382A2FFEB418EB1172 (void);
extern void TerrainLayer_get_normalScale_m63EA677D87ABD1DCF0195D35A8B9738B80C70B3C (void);
extern void TerrainLayer_set_normalScale_m0758116D1F0B171EDB48B4E2A13490C1A5703991 (void);
extern void TerrainLayer_get_diffuseRemapMin_m949BAC51C5758A977052835D009CC8A2949E9F98 (void);
extern void TerrainLayer_set_diffuseRemapMin_m5899CADEF34D6193180D9B79B1E86E38C05869A9 (void);
extern void TerrainLayer_get_diffuseRemapMax_mCA5648C5150800713F24E7D2AB96B4D548E2D4BC (void);
extern void TerrainLayer_set_diffuseRemapMax_mEFA03F94C12ECEFEC2060901D966DD15BDA89D3C (void);
extern void TerrainLayer_get_maskMapRemapMin_m7DF5C3CD76EA311585C8AC7102DF8FE843465368 (void);
extern void TerrainLayer_set_maskMapRemapMin_mD8778273503FD0FD05869EB86DB6EADDA553856F (void);
extern void TerrainLayer_get_maskMapRemapMax_mE05D91C74E45DE9F2A5AB6ABE75B2BC561ED6342 (void);
extern void TerrainLayer_set_maskMapRemapMax_m7E81C871C9AABC01E2F743E690FD3E9002DB008C (void);
extern void TerrainLayer_get_tileSize_Injected_m8D8A89EA332B064157DB6A83826870E691AA4FC5 (void);
extern void TerrainLayer_set_tileSize_Injected_m2D6D83EA3DFBB1948BDC4D1984BCE9D43CAF18B2 (void);
extern void TerrainLayer_get_tileOffset_Injected_mA2FADD8981A297BFB4DE8663FA757834324DB8E0 (void);
extern void TerrainLayer_set_tileOffset_Injected_m4082583251015F2D3621EE132031B852741C092C (void);
extern void TerrainLayer_get_specular_Injected_m17BCA78D6D0B917E71AE7D441367C8A30149BC3C (void);
extern void TerrainLayer_set_specular_Injected_mB6275B761452010AEE54E6B15F63435FA7A1D485 (void);
extern void TerrainLayer_get_diffuseRemapMin_Injected_m12394DF34C1CC7B138FA3389D4583EC00D555FF0 (void);
extern void TerrainLayer_set_diffuseRemapMin_Injected_mFA1B63F9AA91C8EA82104C4CC1A7778C5AED7706 (void);
extern void TerrainLayer_get_diffuseRemapMax_Injected_m2E247FD71A4D51D8E54DA3BF504C1731FB6F23AC (void);
extern void TerrainLayer_set_diffuseRemapMax_Injected_m90271AB60993CFD03E422C688FAFC64017D4BEE3 (void);
extern void TerrainLayer_get_maskMapRemapMin_Injected_mEC87DA9E3173F7D39C047FABAB507CC4351FDF24 (void);
extern void TerrainLayer_set_maskMapRemapMin_Injected_m388350F2F0C06A10AF0DA39725B59103F6EE7F2C (void);
extern void TerrainLayer_get_maskMapRemapMax_Injected_m1BA6C54D1C610E455C6A7919E5E08EA304CA7403 (void);
extern void TerrainLayer_set_maskMapRemapMax_Injected_m10CF5BC6AA549FC669BCB7881D6D75D569EC20EE (void);
extern void TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A (void);
extern void TerrainMap_GetTerrain_mFF9C935F05859DF70E95994E727565BD67CDD6FC (void);
extern void TerrainMap_CreateFromConnectedNeighbors_m4AA17A1E61EBA1295BE150640D2D38B0E1EDD739 (void);
extern void TerrainMap_CreateFromPlacement_mC7822A5F4FC2A2CB119259A48F19D364ACEC5AE7 (void);
extern void TerrainMap_CreateFromPlacement_m64B90ADBC1D3A1AE18CEC7D0B452377E10B2BCB5 (void);
extern void TerrainMap_get_terrainTiles_m9EAA8FCB972C834E2093DDD49B26DBBA2E74A2AB (void);
extern void TerrainMap__ctor_mCDB47BA50D9D54E65754028F9CF8F91828FE616F (void);
extern void TerrainMap_AddTerrainInternal_m507CE3A3F880B33CA2330F69464E3511D5B9BD71 (void);
extern void TerrainMap_TryToAddTerrain_m03A05C883F317FD2E6956ADD6625409E8A90BE15 (void);
extern void TerrainMap_ValidateTerrain_m8D9B035B3851E0ED8BB5877BD11F63BA85029653 (void);
extern void TerrainMap_Validate_mAFBB4A2D0290E25D59902A1BD5DA1EBC2ACD1326 (void);
extern void QueueElement__ctor_m1A6C6D9526BCDA206E451B66F7AF9AFF5445F125 (void);
extern void U3CU3Ec__DisplayClass3_0__ctor_mAFD4AEF760F5CC7CE66BAD750DAD3697397E8945 (void);
extern void U3CU3Ec__DisplayClass3_0_U3CCreateFromPlacementU3Eb__0_m703A4D4E3D378C9896199B70A89FCDF1A07C737B (void);
extern void TerrainUtility_ValidTerrainsExist_m0DD08E4CEC739929A9AEBCEA849EDFE79985A207 (void);
extern void TerrainUtility_ClearConnectivity_m7448E42CD3F2941EF02C10DE358778EEAF9B0AA9 (void);
extern void TerrainUtility_CollectTerrains_mDFCA0AFA00FFD16CEC8B4EFA9C55E3B7B6803EC4 (void);
extern void TerrainUtility_AutoConnect_m3E435D139BE402DC495248EDD1FF2C1E9377A897 (void);
extern void U3CU3Ec__DisplayClass2_0__ctor_m4C022C4675BA4CFC7E7AAA5692979CDE6CD8E611 (void);
extern void U3CU3Ec__DisplayClass2_1__ctor_mA329ED5B221AE8787EAEA1124A2A95675FDD1695 (void);
extern void U3CU3Ec__DisplayClass2_1_U3CCollectTerrainsU3Eb__0_m57E871EB2399E5FB7DF78B3C9EBFBF152116AC2C (void);
extern void BrushTransform_get_brushOrigin_m77837ABD7D3250A219DF37D4943E6C8F87A12EE1 (void);
extern void BrushTransform_get_brushU_m96C6C42F19670792A93667FB998D6AB9C5233835 (void);
extern void BrushTransform_get_brushV_mD5FE860D6229C74AE92086F15835ED4F232814E2 (void);
extern void BrushTransform_get_targetOrigin_m257AB19A9D74C20E09A0178A2B909065744CBB1A (void);
extern void BrushTransform_get_targetX_mBFDE3565FA7AA37EC5C15F58C6C04711F39B67D4 (void);
extern void BrushTransform_get_targetY_m35E99EA7B0B5E7502BDC49D9D985722B99ED3B95 (void);
extern void BrushTransform__ctor_mC0DC3696A91B14BEE4FF118A3723BF2311D2524D (void);
extern void BrushTransform_GetBrushXYBounds_mF0BB5DFE1215819C5432C0672E73A27BDA1ED76B (void);
extern void BrushTransform_FromRect_m848D89CB4297AAF0ECD870EA1E26B2063C9490A9 (void);
extern void BrushTransform_ToBrushUV_mADE37644B70BB500341B48F983845F9436ADF4BD (void);
extern void BrushTransform_FromBrushUV_mE9216C72AE603E81CF9630F1C19DAAA4751DE9CA (void);
extern void PaintContext_get_originTerrain_mCD05E4154C291B8E4400902ABA56B3C017E52338 (void);
extern void PaintContext_get_pixelRect_m5B74BA7635A35A622595FF04BFFC1C982BCDECB3 (void);
extern void PaintContext_get_targetTextureWidth_m6ECD3E0E648DBB6632C633E06B5D6037A0B2CFEB (void);
extern void PaintContext_get_targetTextureHeight_m4975CA86AC71297D69844835B8D006174F806240 (void);
extern void PaintContext_get_pixelSize_m625AE2931B8FBA55050107943314D6202DD930C5 (void);
extern void PaintContext_get_sourceRenderTexture_m45B68671807EA82C3333635B0C5BB8F5D5161B86 (void);
extern void PaintContext_set_sourceRenderTexture_m729A4274969D259E689DA526313697ACA2299CD6 (void);
extern void PaintContext_get_destinationRenderTexture_m5E3BA23300AA39CB2A260F795561FD707DB91E92 (void);
extern void PaintContext_set_destinationRenderTexture_mE0DC8FD99A811C95223A9F7F0082D139EE3CF4CC (void);
extern void PaintContext_get_oldRenderTexture_mBE3586662ED5A1DA7AB82FC7FB01D674095E48EA (void);
extern void PaintContext_set_oldRenderTexture_m911313349E453DD49EC9DB4606E9A4E5C86BC043 (void);
extern void PaintContext_get_terrainCount_mDE945E81F9C5229C79734258BA00235C44B3D17C (void);
extern void PaintContext_GetTerrain_mCBF0B805F670C14BF56C7FE13FBA9408B7649ED8 (void);
extern void PaintContext_GetClippedPixelRectInTerrainPixels_m4B5AF3E6861DDCB269036E6E02FC9D5CD950DA09 (void);
extern void PaintContext_GetClippedPixelRectInRenderTexturePixels_mBFF751A429A4EDAB774A7293FB09DDF176683EE6 (void);
extern void PaintContext_get_heightWorldSpaceMin_m4459086F36CF286988016E0CF4B3CC03F531D22B (void);
extern void PaintContext_get_heightWorldSpaceSize_m928330CAAFBD610A719F90DFBEA37D9988E031FB (void);
extern void PaintContext_get_kNormalizedHeightScale_m4C0AF7DDF82CEB71CA3A3418837ADEB54F4F6585 (void);
extern void PaintContext_add_onTerrainTileBeforePaint_m483D38714ABEE42335EA4A60683A563ABA07BB2F (void);
extern void PaintContext_remove_onTerrainTileBeforePaint_m6DED2BD72D5FDC60E2DD23DF321B7260212B1F63 (void);
extern void PaintContext_ClampContextResolution_m90A8DECD94A9CBDA1BA2ABB4AF30B2D4A3C241B6 (void);
extern void PaintContext__ctor_m5F36E6944154C93E1D271D4A9C077C8DAB108F86 (void);
extern void PaintContext_CreateFromBounds_mE034417CBA9CD6F83EAB77DA319323F458CD04E4 (void);
extern void PaintContext_FindTerrainTilesUnlimited_mC85574536AE3A36A4A9399DAC6F8C5171FAE1433 (void);
extern void PaintContext_CreateRenderTargets_m08252C94D832923CD0F129B6EEA1305DEAF18BB5 (void);
extern void PaintContext_Cleanup_mEB09BBB29CF7FBC5E11FC63BB2A3D651B27EB28A (void);
extern void PaintContext_GatherInternal_mB58AFCC343495B1A7E2D742CFD75F337A6FE781D (void);
extern void PaintContext_ScatterInternal_m18C7B496911CC4C7C95E40A6220B1ACCE8427149 (void);
extern void PaintContext_Gather_mC468CF561D85DFF52AA72668070131027D66A594 (void);
extern void PaintContext_Scatter_m5A8E066AD67DB6D22F7BF62508A7F2BDC45BD576 (void);
extern void PaintContext_GatherHeightmap_m87B6F640A04CB3F1E8871102F2024B4D0DDE302E (void);
extern void PaintContext_ScatterHeightmap_mC457E5310E278339150CD517F59F43AAE1CE1320 (void);
extern void PaintContext_GatherHoles_m856265068B3F5493674358D99163E622CFA1885A (void);
extern void PaintContext_ScatterHoles_m9F2B1CCB1613FFDAF31B569E4D72E23C398BF355 (void);
extern void PaintContext_GatherNormals_mD28D1EE6ED2531984EE8FD64BA793CA752AF06B5 (void);
extern void PaintContext_GetTerrainLayerUserData_m2816B0CA7CA72A0492887923DE89F9382877E632 (void);
extern void PaintContext_GatherAlphamap_m9CF436C1D0D163692E9EDF27958A1704D06E3F44 (void);
extern void PaintContext_ScatterAlphamap_m207A3E71E8014A4639C37905471D1F9C85404C91 (void);
extern void PaintContext_OnTerrainPainted_mA6BF9D65B8EA95D18E69FBC02D538588DEF3385E (void);
extern void PaintContext_ApplyDelayedActions_mC92DBABBF663D58900E7376026C0EEEBD5B6B3DA (void);
extern void PaintContext__cctor_m381829AD174E3AB512452C6158E625AC70AEF66B (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_terrain_m2B00DE94A36AD70ED919E482E7B45A88C6524D27 (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_clippedTerrainPixels_mF4756DB3495110649E77905E24EDADBF345CBE02 (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_clippedPCPixels_m58D39841BC4DDD19502995895D3BC6990618798F (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_paddedTerrainPixels_mC2F832F2BFAC0E9432D21DCA3FEDA88163854E88 (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_paddedPCPixels_m01D7520FB0A8D050E53E99E07F096912B3017551 (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_gatherEnable_m986CD9B32972EC18328285CEFFF08A4593D3F2CE (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_set_gatherEnable_mD98909C10D49D400863D7E1917ED046B316FF8B6 (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_scatterEnable_m2919CBEB18721C001621F385D13D8F83990E177D (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_set_scatterEnable_mBA474FDF265ED8CBB6D5888F51B8DF7FCC4D8B72 (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_userData_m8A130E699EDBE85C316C4DE83C1E3429D117F0DD (void);
extern void TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_set_userData_m0118BB95E521E20483826CC9D336DA2D74715A8B (void);
extern void TerrainTile_Make_m464414DF78B623306730F00C6DDE305D9A46AEFD (void);
extern void TerrainTile__ctor_mCDA7097EEEC5D66D7E2E2A1AD7778F783EB20F8C (void);
extern void SplatmapUserData__ctor_m8B0FF764C4FB0A470238FF13FBDAD970DEDCD713 (void);
extern void U3CU3Ec__cctor_mE59BB9EAF12CDCEAB09C34BC847703237F78E2E2 (void);
extern void U3CU3Ec__ctor_m0F1F9BD4FD33A770EBD350CFB01B2099B03E3354 (void);
extern void U3CU3Ec_U3CGatherHeightmapU3Eb__60_0_m23F229C5102FCC7A2AD3B5AD12A14F0992B1010C (void);
extern void U3CU3Ec_U3CScatterHeightmapU3Eb__61_0_mB419973E110683FFE1A28372744E29E66C0DDB2A (void);
extern void U3CU3Ec_U3CScatterHeightmapU3Eb__61_2_mF9AB8BADF2E47F638CE89FF180B6A9D2025EC6B6 (void);
extern void U3CU3Ec_U3CGatherHolesU3Eb__62_0_m2FCAB1248BAD362632AAE5D40707788956C4BC82 (void);
extern void U3CU3Ec_U3CGatherNormalsU3Eb__64_0_m584859F8C89B95EB220738028E1F8D07817342F1 (void);
extern void U3CU3Ec__DisplayClass53_0__ctor_mB1DCC08BEBB1B500CAD7277614120E2743B5853E (void);
extern void U3CU3Ec__DisplayClass53_0_U3CFindTerrainTilesUnlimitedU3Eb__0_mA575AAFB77E11432AD4789A5E4DB47EE29ABA676 (void);
extern void U3CU3Ec__DisplayClass60_0__ctor_m8B39322C4D4896391FF55C7EBC497A8F3EBA0CCA (void);
extern void U3CU3Ec__DisplayClass60_0_U3CGatherHeightmapU3Eb__1_m98A9800C587B48C9A620B69F335D5698024F8724 (void);
extern void U3CU3Ec__DisplayClass61_0__ctor_mE3CAACC693950F073FB23BA5EA4A7E72BA7AA4EE (void);
extern void U3CU3Ec__DisplayClass61_0_U3CScatterHeightmapU3Eb__1_mBE666B97592271745BB7EF56B09DCBB7FCD672F1 (void);
extern void U3CU3Ec__DisplayClass63_0__ctor_m35AAEE93BCA8DD28A4096DAA0A1EF5B390B20429 (void);
extern void U3CU3Ec__DisplayClass63_0_U3CScatterHolesU3Eb__0_m63EEA7EC9D7883B1704B7FEB6E42FD0735416B87 (void);
extern void U3CU3Ec__DisplayClass66_0__ctor_m7546F25D02A8C4AE3CA8A1966E400F3C3DCC3FF3 (void);
extern void U3CU3Ec__DisplayClass66_0_U3CGatherAlphamapU3Eb__0_m267E46A16FA8D4413C38D05DB8E68C242563046F (void);
extern void U3CU3Ec__DisplayClass66_0_U3CGatherAlphamapU3Eb__1_mC736CCFD74BEA0444925BA18C5D877590161F531 (void);
extern void U3CU3Ec__DisplayClass67_0__ctor_mF166EB7437F0888686AA463B2ED545AB463B15E6 (void);
extern void U3CU3Ec__DisplayClass67_0_U3CScatterAlphamapU3Eb__0_m9ABB337FFDEF8CA150010C341050F1ACE434ED78 (void);
extern void TerrainPaintUtility_GetBuiltinPaintMaterial_mA169E2BA319EF1B0624A52D8E604D384AE9ABA4C (void);
extern void TerrainPaintUtility_GetBrushWorldSizeLimits_m90DF01D033170FD2AEA6A7A855D650C6A636AE55 (void);
extern void TerrainPaintUtility_CalculateBrushTransform_mC09FF992CED60AADB39F6FCED42DEE79BBD232DB (void);
extern void TerrainPaintUtility_BuildTransformPaintContextUVToPaintContextUV_m8D9BF942AA822C79029BF555CDF53F5A9CA93FA0 (void);
extern void TerrainPaintUtility_SetupTerrainToolMaterialProperties_mEC21EFEE7F7829E3D8D84B92EB64C5CAFE03CAF4 (void);
extern void TerrainPaintUtility_get_paintTextureUsesCopyTexture_m3B6A9141CAD26F632175A9E2900D561AAC114D32 (void);
extern void TerrainPaintUtility_InitializePaintContext_m4AF5ECCA84C9B1B010244E35544C4E3B41F97F70 (void);
extern void TerrainPaintUtility_ReleaseContextResources_mA0E11F8951E9AD5575756F1C60409EFB64DE35BA (void);
extern void TerrainPaintUtility_BeginPaintHeightmap_mC0188F34F03BE6B6CD23F65479F39EA9A3301F2A (void);
extern void TerrainPaintUtility_EndPaintHeightmap_m8BA767909C7EC5B02B88EDEA0D4B5F2341373F84 (void);
extern void TerrainPaintUtility_BeginPaintHoles_m78D3B2BA0F36F05B9F1873A02B2627FA4D2069F8 (void);
extern void TerrainPaintUtility_EndPaintHoles_m0D7B87C922FC65BB92862DEBEEC39F1D2202E7A8 (void);
extern void TerrainPaintUtility_CollectNormals_mEA3059F427359B4569259FECF905328EC59D3FC7 (void);
extern void TerrainPaintUtility_BeginPaintTexture_m0BBFA0F86453D0363334C3E960CF7AB9AF40EA4C (void);
extern void TerrainPaintUtility_EndPaintTexture_m06D132134AC924F27FA3F5598E3C9F91CFC1A936 (void);
extern void TerrainPaintUtility_GetBlitMaterial_mE30CA5859A38CD72365ADAAF487060AA531FB583 (void);
extern void TerrainPaintUtility_GetHeightBlitMaterial_m118BD111DB96A8AA7FAABD16AAD2FA856D9C72AA (void);
extern void TerrainPaintUtility_GetCopyTerrainLayerMaterial_m4AE79A9122DBF2DF8A4932CBA9E2D7347CDACDA6 (void);
extern void TerrainPaintUtility_DrawQuad_m3DF46A679C95B544FD1CA783020DFDFDA084728B (void);
extern void TerrainPaintUtility_DrawQuad2_m70891EE13A543416FBBDF98FEEC6499A2B38494A (void);
extern void TerrainPaintUtility_DrawQuadPadded_m48168E3A5DF4E5063D290556E06E8FF75EDD1552 (void);
extern void TerrainPaintUtility_CalcPixelRectFromBounds_mA0F27994D74342C8EECA59B32E3B24CEF52D3334 (void);
extern void TerrainPaintUtility_GetTerrainAlphaMapChecked_m61B8DC77E576C67F4A93CA116608ABC0786E4A92 (void);
extern void TerrainPaintUtility_FindTerrainLayerIndex_mF21091EAB56A8C467F80D187A800D1288FDB135A (void);
extern void TerrainPaintUtility_AddTerrainLayer_mF5232126EC2C8B14BBC0ACDA0AD60652002DEC0A (void);
static Il2CppMethodPointer s_methodPointers[568] = 
{
	Terrain_get_terrainData_m3B6C1D89471A4E1C60FC19C168DB37A011B924FD,
	Terrain_set_terrainData_m59F63BECF7DC2657DC887F1F59DA3427ED59994C,
	Terrain_get_treeDistance_mE5394C9AAD12F1BD5474B51615D2E3906404F77A,
	Terrain_set_treeDistance_m8836D1691B1C7BDC76725A624601E6543806C14C,
	Terrain_get_treeBillboardDistance_m5A9D3F3B388042BE9C19F346426AA7BA01CBE1D8,
	Terrain_set_treeBillboardDistance_m654B35A6BEE23489F2E4DEF0D12214D9F4A5FD8E,
	Terrain_get_treeCrossFadeLength_m037BF60679DC6C878F5D7B5189F4555CF496AFD4,
	Terrain_set_treeCrossFadeLength_m639B8CAD49CF4AF938477D3F1E080932F2DBE6F1,
	Terrain_get_treeMaximumFullLODCount_m3626EB8E365A9B8620375AEB15604D112F669812,
	Terrain_set_treeMaximumFullLODCount_mCDE91147B62F207B2841D41A2710A4B563A953D1,
	Terrain_get_detailObjectDistance_m230A961919F3B468A7F79312C164C1D50C648EBA,
	Terrain_set_detailObjectDistance_mE8B84B8EDE307BEB41E477CC9C35F8BA3A969EDE,
	Terrain_get_detailObjectDensity_m2FEBE42D389A14F98A38888A423B21FD91E605E9,
	Terrain_set_detailObjectDensity_mFDE71C06416A56C18C54ED41485CBB1D1CB3520D,
	Terrain_get_heightmapPixelError_m9051CAA3D0939B5F0F5719F53A3B5E4CF55438C5,
	Terrain_set_heightmapPixelError_mEE31EA7AF7F41EB82C643468B52F4B10109C7FFF,
	Terrain_get_heightmapMaximumLOD_mC57749C8FF75F58EE1F209BBD26C2B1C5292C829,
	Terrain_set_heightmapMaximumLOD_m0638986CDAF871DDD4C80EA55E1CF60F44DF6F5E,
	Terrain_get_heightmapMinimumLODSimplification_m319C4BAEFD45FAA988396F395F6D220487FFBA44,
	Terrain_set_heightmapMinimumLODSimplification_mE9AF955106D0E1CB137AE372BF1E6C64E3BEDA6C,
	Terrain_get_basemapDistance_m19DDA753F682B416665C5A7E171376EEE3BF312B,
	Terrain_set_basemapDistance_m3CC75A7D5D67BA75A0E030F300A462C16905CF40,
	Terrain_get_lightmapIndex_m79CF88EEEA60C4FACBA6D568C10A769D9654803E,
	Terrain_set_lightmapIndex_m771ADCC6B582144408D4736C93E97EFFE21319C4,
	Terrain_get_realtimeLightmapIndex_m7DA20D23C390C3525A3245A8CF3340188541BB64,
	Terrain_set_realtimeLightmapIndex_m1D972992A2DCBBD141D21DD089ADEB561CF6EA30,
	Terrain_get_lightmapScaleOffset_m27B3E8FD4CF439470223ABF5F077AAC2A045480B,
	Terrain_set_lightmapScaleOffset_m5ABDFF3AAA8BC1243B976AD50A89AA864C8D218D,
	Terrain_get_realtimeLightmapScaleOffset_m028BC94F9D808480C241BD2D1A55F857E2D5287D,
	Terrain_set_realtimeLightmapScaleOffset_m599578984D728DC7EF3AE412879D7DC293A66E77,
	Terrain_get_freeUnusedRenderingResources_mB7750A5195DFE7A96E99763E9EF0143D52124715,
	Terrain_set_freeUnusedRenderingResources_mAFBEB163234FB5F808B453246765B980A8A28DDA,
	Terrain_get_keepUnusedRenderingResources_m795AD911467CB1E237543208A48A9E5DDFFF5032,
	Terrain_set_keepUnusedRenderingResources_mFFE068865CA7F9EF1B875E0C1B0548A4D14E8C9F,
	Terrain_GetKeepUnusedCameraRenderingResources_m36AE2CC2933A8ED02A0A6B1581B1733C9309BA97,
	Terrain_SetKeepUnusedCameraRenderingResources_m00A637BC1210C488F62A6D39A7E60EDD843371D0,
	Terrain_get_shadowCastingMode_m4070042014ED55612368631D6335105FDF951114,
	Terrain_set_shadowCastingMode_m1267E7389BEF6EBA9B6E23044225362CD6EA79BD,
	Terrain_get_reflectionProbeUsage_mAAFE9BC6BD45E280BF1EFD911184A630CC91C090,
	Terrain_set_reflectionProbeUsage_m9D034C6790A0F28D118E330F590772E3240373B6,
	Terrain_GetClosestReflectionProbes_m6DFCD7ACACF5566A37691DBDD466BFD0F96A0B29,
	Terrain_get_materialTemplate_mB3BE06B73866CF772A9B2B42069CE3AECA288DE7,
	Terrain_set_materialTemplate_mAC433DD496BAB83C63718B862C11064FD20EB7F0,
	Terrain_get_drawHeightmap_m41EBA9E260C303324FD8A24A9FD155001D4715FE,
	Terrain_set_drawHeightmap_m39CA641057F8C3482CF7BADD65B1FD89106195F9,
	Terrain_get_allowAutoConnect_m4E9CB87D130BB118517C5504C8FB4A5CC3AA91D7,
	Terrain_set_allowAutoConnect_mBAA98DD489268404D1157A29759982CDFB45BF11,
	Terrain_get_groupingID_mE52E78018126A5D00F837081287BE076E7709C24,
	Terrain_set_groupingID_m0C89CE036FD16514CDC70B52270B853F7FCBF334,
	Terrain_get_drawInstanced_m7E8C2EB30B4F778927EE017163B79B113598CB34,
	Terrain_set_drawInstanced_m8846CAE84A778178A98DE6C0A764EBEA7B45D1F3,
	Terrain_get_enableHeightmapRayTracing_mEC782BB01FE856D14FA6F8AA36DFFEA30F351409,
	Terrain_set_enableHeightmapRayTracing_mD93ADA6B988DE9DC3B15E4C0254FFCAEE58F07BE,
	Terrain_get_normalmapTexture_m0B073CA3D5CD8E6176A8512B6B095F7BBE0D4C53,
	Terrain_get_drawTreesAndFoliage_mD04FA3FDAAF4C49C875AFF6DA926531AD2190187,
	Terrain_set_drawTreesAndFoliage_mCF36A66A7AC9072D3EC5706A9213CFC46D38689A,
	Terrain_get_patchBoundsMultiplier_m9FFCE6D0355BE5853182DAEF3DA4043EE3CC7435,
	Terrain_set_patchBoundsMultiplier_mD5B6AED331E3843988403FD13BBC2E625344BAAC,
	Terrain_SampleHeight_m460F9060BC4D5F05275391A6AC05570047EF3177,
	Terrain_AddTreeInstance_mD11FD23312CDF066606036F336EC317FC63B6436,
	Terrain_SetNeighbors_m2FFA89D199120125D264EF7EE0BC749A35514C1E,
	Terrain_get_treeLODBiasMultiplier_m1BB2454CBE32BB3AE525E5F0FAD8F9363CBB7DCA,
	Terrain_set_treeLODBiasMultiplier_mD8287BC51A55A0B68C2066F3C22AD1C18AE863E5,
	Terrain_get_collectDetailPatches_m0E86F73ABD8BDC8DE29A062D307BCEC4E03EE64B,
	Terrain_set_collectDetailPatches_m1FC681318E104B97274412E3C80F04FF90E6B564,
	Terrain_get_ignoreQualitySettings_m173884E86DDCA1F511B1C4A59FC14BEA356693CB,
	Terrain_set_ignoreQualitySettings_m46E198F92258132EDBEA5491AADE5A3149EE9780,
	Terrain_get_editorRenderFlags_mE55875C5D3E964CC1275FC4ACAF3674D9D8793F4,
	Terrain_set_editorRenderFlags_m891E1F37730C9E3D6ABDED416B360A3EB0468C8A,
	Terrain_GetPosition_m5A1020F22CA4B1818E69A3B9687668AFAB2C43F5,
	Terrain_Flush_m960CA9087AB6C18BE3C6B54DD993B5E728E5FA95,
	Terrain_RemoveTrees_mD2D7E380123771264CEB2306594DE290AD604EB7,
	Terrain_SetSplatMaterialPropertyBlock_mF7D441C25CA04812F7A95D5CDBAA6F4ACA6D7F0E,
	Terrain_GetSplatMaterialPropertyBlock_m12C9F2F14B6D92021E72AE994D438E2C6DF69FEF,
	Terrain_Internal_GetSplatMaterialPropertyBlock_m30EF43010BAE0F7F7AD19041E36A4F44F023D61E,
	Terrain_get_treeMotionVectorModeOverride_mDB25A728890AD3B16057940451AC9DE3ACBCE859,
	Terrain_set_treeMotionVectorModeOverride_m24581C6A921EFDB811EA3D560664158D18AEF65D,
	Terrain_get_preserveTreePrototypeLayers_m8C45D508EBB7A14A25920093D24AFC66E2ACFBD6,
	Terrain_set_preserveTreePrototypeLayers_mCBE60BF8EE44DA31170E32B8C31B64393A8EC4DA,
	Terrain_get_heightmapFormat_m7BDD479FF7710457999A405C226D2AEE901DDF87,
	Terrain_get_heightmapTextureFormat_mD3B13DC4664DDB71E4C4CD1CE95ACC9DF35B9722,
	Terrain_get_heightmapRenderTextureFormat_m81550AE5326309C22601E667FA4F5032DDE56BC5,
	Terrain_get_normalmapFormat_m70C88E3838D7938A60BFBBEF5BB2EFCD99EBA716,
	Terrain_get_normalmapTextureFormat_m4599EE3389898A7589ABC29F50DB337BC2F81BFC,
	Terrain_get_normalmapRenderTextureFormat_mE32604BFA382DEE5EF9E223FC5B0AED451FBB7A5,
	Terrain_get_holesFormat_m3BCE4632B6CE7D1A058070DC65CEDEF5365838BE,
	Terrain_get_holesRenderTextureFormat_m960C437EC8DA9338ED56323263AD6F8B5CB6CD3B,
	Terrain_get_compressedHolesFormat_m386E6544EE29FC198609605B796C8DD06983658C,
	Terrain_get_compressedHolesTextureFormat_m1613558760307F7999EEFE230A1DFC612BE73A33,
	Terrain_get_activeTerrain_mAE5A7FE933C2C1A57FC9542E9BFA315A413F224E,
	Terrain_SetConnectivityDirty_mE2F27F3B012109EC58BA2B37711EAAA9329CF91C,
	Terrain_get_activeTerrains_mB90A9BC89764F626D13F3EF1420EA8D3E186B701,
	Terrain_GetActiveTerrains_mED4BDC0714F7743290CEC886012B04A81DC77686,
	Terrain_Internal_FillActiveTerrainList_m00E77C57C0ED1C10647C734780A511DF56B50692,
	Terrain_CreateTerrainGameObject_m31CBC26A8971667B09CC08C868EA31E8A5D29522,
	Terrain_get_leftNeighbor_mA42E758BD513BC9E0FB87888FC24B3C4349A353E,
	Terrain_get_rightNeighbor_mA50D19F61FFBA544E94733B097D4409B15FECC17,
	Terrain_get_topNeighbor_mCBC502C4CC0D617863D7A3A726EBFA4BD18BDF8C,
	Terrain_get_bottomNeighbor_m31306E01318F62D214B5E0966C988FB24255406F,
	Terrain_get_renderingLayerMask_m8A0C2E21E7DC988D9DF2E058DA231D0AA13ECC31,
	Terrain_set_renderingLayerMask_m5F843E15CF24EF2BE27AEB21F1C9E6C846F2D249,
	Terrain_get_splatmapDistance_m0FC2686DCFB5606FE515B9B7EC720FE92C96065E,
	Terrain_set_splatmapDistance_m863B50B87A1DF039426E79D916E690A1E2EAFAF3,
	Terrain_get_castShadows_m3323F44BE250A712E8D34CE3C5D4EC7F1AD87BC7,
	Terrain_set_castShadows_mEC211FA9586B718ED5436632A566DE8D6C0E26AB,
	Terrain_get_materialType_m4DA6FCD1D6E98FFB51B163E852CB904522B60FCB,
	Terrain_set_materialType_mC78AA6B3C29A28909943D2498C281CE882E8D50F,
	Terrain_get_legacySpecular_m9066C975D891182376AB99BEDDC9B66EAB841C30,
	Terrain_set_legacySpecular_m8576A8BD0E10E6C5F88D324C317367522BEF7242,
	Terrain_get_legacyShininess_mA4C410EBAF37DB0A6986890C41ED2627AE9846B7,
	Terrain_set_legacyShininess_mA4716046912CE099D9B58B1CD70F2063455BE757,
	Terrain_ApplyDelayedHeightmapModification_m7C23AC364F9431B7F890B47F8D3EFA3FB268DB01,
	Terrain__ctor_m11F03EC6C1E68752DDCAE8EF2DED99CFD939FCDC,
	Terrain_get_lightmapScaleOffset_Injected_mD2EAABA59EC39ABCF49DAF4F0EA8273664B65562,
	Terrain_set_lightmapScaleOffset_Injected_m6EE17634B3B017635B441AB3224BA8E3A1E5DE47,
	Terrain_get_realtimeLightmapScaleOffset_Injected_m29ECF237E6B854DBF05C2BF365431CDAE3B4EDE4,
	Terrain_set_realtimeLightmapScaleOffset_Injected_mF70656C179FC898C168A2F8AD8009D6CF21FE4FF,
	Terrain_get_patchBoundsMultiplier_Injected_m50860EC61F48B0DEB611BED7571AE29B436982F6,
	Terrain_set_patchBoundsMultiplier_Injected_mD42F66A68FF0A7BE6E39B7C962EEEAC055F2E462,
	Terrain_SampleHeight_Injected_m08851544EDBB88AFD061CD7E63E7995203BC7557,
	Terrain_AddTreeInstance_Injected_m68720D1D8743CF74F8911B424DD9284C78563210,
	Terrain_GetPosition_Injected_m23EC0958466016EFB553CFBBE1B6E4FB076FE419,
	Terrain_RemoveTrees_Injected_m7401D70AB9B44261A015FB398996D1041CB46846,
	TerrainExtensions_UpdateGIMaterials_m0D5B2CCB122576F254BEC7BCAF5F54DB77FB27D1,
	TerrainExtensions_UpdateGIMaterials_m348138829150E8D140B19E7B1224F2B276C0E44D,
	TerrainExtensions_UpdateGIMaterialsForTerrain_mB0B0C183D5618E81C093CAFE3970CAE2F9170E3B,
	TerrainExtensions_UpdateGIMaterialsForTerrain_Injected_mFCCF2418F7BA12C50818453130292ABA074AF2A2,
	Tree_get_data_mBFF1F514CA23A22FDBBCA1C2EC782793588A32DA,
	Tree_set_data_mAE6F594BA8D3A705CBC8956CEFD98CAE90C96A71,
	Tree_get_hasSpeedTreeWind_mB50245BBCFFCA4A390C27A308899C0196A7B5344,
	Tree__ctor_m634F3BDA4C40F235FDC59CD0D5428A788009FCDA,
	SpeedTreeWindAsset__ctor_m5F2944099EEA8B34A2D97820438C427797A67FE7,
	TerrainCallbacks_add_heightmapChanged_m903E49435A5284E734F0A1F049183A9389672427,
	TerrainCallbacks_remove_heightmapChanged_m922A2608D0BDFD0A4324592527E28DCEB633F17A,
	TerrainCallbacks_add_textureChanged_m511506C76D652D13E82174C8EA732497826350C7,
	TerrainCallbacks_remove_textureChanged_m4B1C823F9DE53D68B8A69CCD7A5712DB946BCD27,
	TerrainCallbacks_InvokeHeightmapChangedCallback_m731ED939CBD563CCCE503062602DF5908205AD04,
	TerrainCallbacks_InvokeTextureChangedCallback_mB508E8B7A884854AA01AE5B88AB33E1AE40F4318,
	HeightmapChangedCallback__ctor_m6A7E4189E0A7A1B70EE73818B93B0FC9F613648C,
	HeightmapChangedCallback_Invoke_m63C1C93709641DBE02DCE9F71B7895C5793AF875,
	HeightmapChangedCallback_BeginInvoke_m5607A1E2C8633045D2C4EBFBDC704D6C5A57C5CC,
	HeightmapChangedCallback_EndInvoke_m3EB0F33689F26783A7C722DB0F001303146E335C,
	TextureChangedCallback__ctor_m64076D799FEB79E3D6BE2C4EB33CD081A398F0EF,
	TextureChangedCallback_Invoke_m1194A44102843272B51A70C302EBDBC8214647DE,
	TextureChangedCallback_BeginInvoke_mE0E6DAD71F05C26F1A2B546BB9126326F863B8DB,
	TextureChangedCallback_EndInvoke_m520E06126898CF212692FA47F5D722ECF814E5A5,
	TreePrototype_get_prefab_mCE1630C35B09770D35B2ECA45B98D1CB6D5AC67C,
	TreePrototype_set_prefab_m795E5BAADC413A62B97AAA7B2742F2B09CD60E62,
	TreePrototype_get_bendFactor_mC78774070395FFBEF5588233ED4C40D253F2B087,
	TreePrototype_set_bendFactor_m28AF8D21C6BED89B59DCD1F3F79596D4A3C281CD,
	TreePrototype_get_navMeshLod_m68F7C292A64B7560076E09BF0B3AB6D681886C6C,
	TreePrototype_set_navMeshLod_m2F83E58E814E337E53CDF5EA7E32F209C72F8235,
	TreePrototype__ctor_m319858B89E2F9AF0FD4009A015E2A34776F6CAC5,
	TreePrototype__ctor_mB52731EBEFC3B50E5BA52AFFA32B39E069A15A1A,
	TreePrototype_Equals_m50E85BD703A2633D4ECA590DB0F1B803EE192F9A,
	TreePrototype_GetHashCode_m3E71334805650043E1C12F1FD6228D6281560E91,
	TreePrototype_Equals_m6F39B894827A28E1ADBF4403922FFCA8CF55E265,
	TreePrototype_Validate_m198AB043B5BFFA95D3CC3A38A8174EC7129BB054,
	TreePrototype_ValidateTreePrototype_m4C45516FEDB674BCCD18283003836F38E15EDB82,
	DetailPrototype_get_prototype_m4231116338BF5EA568B47405CD7626FA511EA695,
	DetailPrototype_set_prototype_m3D231B05003B29C4FAE5146B42514F34C7D7F61C,
	DetailPrototype_get_prototypeTexture_m293614A486AE92BBAE7CA448B1A87BE5F64B4D7E,
	DetailPrototype_set_prototypeTexture_m2679A1548B564B70036AFC79413C99271AA76A82,
	DetailPrototype_get_minWidth_m41AAF89F1E5EBFBD6D064684D6956F6796F8CE7C,
	DetailPrototype_set_minWidth_m35117222A238E215BC941F5F82E7A25D7BABF3DF,
	DetailPrototype_get_maxWidth_m428CF8DDC8FE3BB9E55051702ACBBA7431E8C073,
	DetailPrototype_set_maxWidth_m481FAB59429BE8596BD8B4478D4853FCCBB7DC27,
	DetailPrototype_get_minHeight_mD714BF9D18EBC2E2920908302EAFE31F014A44A8,
	DetailPrototype_set_minHeight_mBD5DC44011EF8981E9256ED394D9FB6989193955,
	DetailPrototype_get_maxHeight_m6048EC459FF0A728CDB5D2DD1169894F3A3A7C15,
	DetailPrototype_set_maxHeight_mF56BB11D1C9AF4D3131D36FDB63795BCD6B9A5A6,
	DetailPrototype_get_noiseSeed_mA4148AE5D207584CFDB9D9E266C8660FF358D49A,
	DetailPrototype_set_noiseSeed_m76256663CB2C2749D0A50099A4B0A1C53FEB869E,
	DetailPrototype_get_noiseSpread_m7FC289CF0B780679986A5395ECA4318FB696EBE1,
	DetailPrototype_set_noiseSpread_mEDAF66AFB8F1423453A74CDE41E6EEC0CCDD14FB,
	DetailPrototype_get_density_m4F747BD95E4A6201BB26283DC7EC9BB92EC6AEB7,
	DetailPrototype_set_density_m7C996AE5BDEE23F568392DE3BD51EB0C14C2A4A6,
	DetailPrototype_get_bendFactor_mD10E7E587B192DE10321CF05014CE9ABAD581964,
	DetailPrototype_set_bendFactor_m44FB373C8EC39A14C985EFCD910690CB63C31519,
	DetailPrototype_get_holeEdgePadding_m2ED0A86DC359BFDC210BA493260466228169785D,
	DetailPrototype_set_holeEdgePadding_mC3EF2AA55AE1C51980B6D4FD01EEE78FE9CA282E,
	DetailPrototype_get_healthyColor_mD81828F1E2867C4819B38B0466ABAF2B10E10FD9,
	DetailPrototype_set_healthyColor_m9CD3FB1504B33DD13ED6BDADC089AB984FE48FBA,
	DetailPrototype_get_dryColor_mCA70253238F53DC22FE0F6D6A644F6ED839A310F,
	DetailPrototype_set_dryColor_mC5BE4EC0A161311A8A864687B7341F347F8644E2,
	DetailPrototype_get_renderMode_mE98D096AA932509AEF13C2AA75D34AFCA0AA1891,
	DetailPrototype_set_renderMode_mFDE617505B0142B83B0FF74D43793CD684D3E087,
	DetailPrototype_get_usePrototypeMesh_m3F21E15D7C815DC93B8C51A9F6AED7FBA5F6B4F8,
	DetailPrototype_set_usePrototypeMesh_m77296758634404A1ABB97ED11733672F4EA446FD,
	DetailPrototype_get_useInstancing_m217E01AE7B95A6E0A721E5A9B3B4681C7D63BA61,
	DetailPrototype_set_useInstancing_m21E73698E0A1A02B88FE13E508129B1B3AE16DEE,
	DetailPrototype_get_targetCoverage_mB025603135CDB7C5DFB3D41D7C29C38B33402333,
	DetailPrototype_set_targetCoverage_m55157EACFBA98E5E6DF70EA93A8E69C0323A3C38,
	DetailPrototype_get_useDensityScaling_mAC589A366333F4DB6C4B282A3B9F71D8147640C2,
	DetailPrototype_set_useDensityScaling_m056FD505699CBEFB297232A2B53ECEFF14DD9236,
	DetailPrototype_get_alignToGround_m88E88F6F4815EC485113A87211F765502F261DFE,
	DetailPrototype_set_alignToGround_mC2A9DA38FC3ECA4478F32CA9CCD44C2C2BD83189,
	DetailPrototype_get_positionJitter_mDB281253889D02E1C2D90FEEBC478D7DC1FD09A3,
	DetailPrototype_set_positionJitter_m8A2E2A209C7CEC4B0E0006A3D0C9EDA91E44B227,
	DetailPrototype__ctor_m9157B5DBD1A80AF51E9090177A751EB401247103,
	DetailPrototype__ctor_mA958B452872D2004C50CBFD24B5998FF933D5BA0,
	DetailPrototype_Equals_m4366EA36A57FC1391A85828835FD5B493A573E93,
	DetailPrototype_GetHashCode_m7C87FF388D92F7A9396DC46F1F4DB4AC436D9ED0,
	DetailPrototype_Equals_mDCF0B4B10AB82B883A1B1EE9F286D92A99636B20,
	DetailPrototype_Validate_mB3C8763387C029886F98ED4DDE9F9E6D5289095B,
	DetailPrototype_Validate_mDF3789F5839651A8C10D2FCC2BC69C102FBCB87A,
	DetailPrototype_ValidateDetailPrototype_mD3870D4E09EF61CDE045CC449B6749453F440DFA,
	DetailPrototype_IsModeSupportedByRenderPipeline_m5BCAB08F59B141CFAD3E314E7A0036B96A2B5765,
	DetailPrototype__cctor_m5D5A4C448F2584173CF5618DD3CB725B16F6406F,
	SplatPrototype_get_texture_m10D1E021FA78ADB602A6B9DCF7D7C3108B5226EB,
	SplatPrototype_set_texture_m6810CF1FB35918B61BD81F5F857F3FF553D53E37,
	SplatPrototype_get_normalMap_mB81C93E54EEDF854E3A6265259C627A80B3E76D3,
	SplatPrototype_set_normalMap_mAA0B8894AEE5B8A8B52A61F12F4F3E8461DB98CE,
	SplatPrototype_get_tileSize_mFC64C788FD37AC90A3DADEA115E77476704C6496,
	SplatPrototype_set_tileSize_mCBBFA459982E2D402A20DCF81BF6C8AA836370E2,
	SplatPrototype_get_tileOffset_m5D7E37F7FDB7B6A2CD8A2323D2AF0B7CCE88FCFF,
	SplatPrototype_set_tileOffset_mE1408107A9BFB3C4F370E5143C37B225C1E607FC,
	SplatPrototype_get_specular_m4DBE720D7BF51F447B1C59AB008CE4FF777AB17B,
	SplatPrototype_set_specular_mA2BDD44AC681C851BEE37017A73B19FBCCC56BFD,
	SplatPrototype_get_metallic_mF45573E9F0187E619B352DCC1A43D8950A7B5544,
	SplatPrototype_set_metallic_mB6FE9F3D7FA7C887307F8B6BD1D5994C63A41CAC,
	SplatPrototype_get_smoothness_m41F39F21541FFA167558900FC05F6508E2CBB519,
	SplatPrototype_set_smoothness_m4F90E6490EC1AC09C362D772F2C57003BF746053,
	SplatPrototype__ctor_m8F453C42984513A7B33BD5E9B728F4082B7D57A5,
	PatchExtents_get_min_mCDE97D43190F69E3E33F187EF510D05E6BE36171,
	PatchExtents_set_min_m767CBF5D34E86C328619AC9066FEE97A30D45254,
	PatchExtents_get_max_m54D71762D0C9923352FC5ACF3AECA7FA88759FD6,
	PatchExtents_set_max_mD20D2F8F79550A260A9A8AC21B46A4871B0423DE,
	TerrainData_GetBoundaryValue_mA9217CC15BBC958C9F7071B96CE74769EFDC322E,
	TerrainData__ctor_m1B68EB89248D5706C2528F47279812F824E27A2E,
	TerrainData_Internal_Create_m79BF764CFF5F49D17E2BFC8B20F60B4CF70BE4E1,
	TerrainData_UpdateDirtyRegion_m6A9D35DED10DC4E5BD43B27D01022E697D6E5E11,
	TerrainData_get_heightmapWidth_mBD11F8B3F89DD66B275CA91759534A22DCE070A6,
	TerrainData_get_heightmapHeight_mDCB90912307BA444DCB3B21648050812E5278757,
	TerrainData_get_heightmapTexture_m26CB863455F0048CEA74741C71BF3021576FF8CE,
	TerrainData_get_heightmapResolution_m39FE9A5C31A80B28021F8E2484EF5F2664798836,
	TerrainData_set_heightmapResolution_m2E5AA9451129E9460D9573CCA899DC1F3CFFDD21,
	TerrainData_get_internalHeightmapResolution_m83C6A32499AAAAFDD57DF73BA460CBCF02F98118,
	TerrainData_set_internalHeightmapResolution_m18F96875BE176D13F26A55AEBB0343902D5C1B5A,
	TerrainData_get_heightmapScale_m4B6AB6495384109BA54955CA52B883A118015188,
	TerrainData_get_holesTexture_m967249F842D3BFC631ED03C847C41EE4D0A56912,
	TerrainData_get_enableHolesTextureCompression_m6BA8BB7840CEF3B832D7C64D91689BECBBA1E920,
	TerrainData_set_enableHolesTextureCompression_mDD73FBE3EA89453DB6FA3BA236FB527B2F71EE7C,
	TerrainData_get_holesRenderTexture_m953821042F6D4FF04EB1FD861BBA604DED1F1A74,
	TerrainData_IsHolesTextureCompressed_mBFE653BBF59360E5382F66592EF1F3E48ED4057D,
	TerrainData_GetHolesTexture_mE1BC7A225983716A760BA8ACEFEC6DEFF6EA572A,
	TerrainData_GetCompressedHolesTexture_m219472651551DC5C4613B441C27E504DA693411F,
	TerrainData_get_holesResolution_m9DE109FB3A0874F7F4706543D0903AFA12D1BC53,
	TerrainData_get_size_mCD3977F344B9DEBFF61DD537D03FEB9473838DA5,
	TerrainData_set_size_m4F3EF9EDDF9BE2E0903FE3A5121D8F4ABDA46DC1,
	TerrainData_get_bounds_m9CE9B3BF067EA06A837AB98B5BC6C0C4669B5A32,
	TerrainData_get_thickness_m7B81848297CCC307A8D171AAAF49F950A287F237,
	TerrainData_set_thickness_m5D4165CE2D3C2952DB0EB26EBEF4AC75F5B03781,
	TerrainData_GetHeight_mC0FAC93E5B8128D5B918EDDDFEE55F6E3E23E8D5,
	TerrainData_GetInterpolatedHeight_m30AAF72C79B6BE19E23962304DB80B21023B5752,
	TerrainData_GetInterpolatedHeights_mF8E7DEF026AB7676FC3BC35CE413A80D7A4DA648,
	TerrainData_GetInterpolatedHeights_mC8D6FE42800B4480E1863D795445A4D1630D0E57,
	TerrainData_Internal_GetInterpolatedHeights_m4794EE72628D345B4F3A7571806BCA0BE61B11AF,
	TerrainData_GetHeights_m3E5C109E98E72A23E39B92F7DF48D87888B2D488,
	TerrainData_Internal_GetHeights_mEDF77233265AFA8901DE8FB61661385337B70810,
	TerrainData_SetHeights_m104C6E5C4E4A12223AA0E2E123E0557302097F23,
	TerrainData_Internal_SetHeights_m8E3E77B7A38E85521BF693D7EB3B9E277CDBE12E,
	TerrainData_GetPatchMinMaxHeights_mF44EB92C548A6218079F957B1387919E7C5B0334,
	TerrainData_OverrideMinMaxPatchHeights_m6C6C7B6DC573747776B0E4DB710CAFF410AC9E4B,
	TerrainData_GetMaximumHeightError_m9BB2B3C5FFBDE5C38F4A858F75E1FF056BC66706,
	TerrainData_OverrideMaximumHeightError_mEE18F9EF4470CD87A4130D37936975D2E4B7CDEC,
	TerrainData_SetHeightsDelayLOD_m72A8BE616AC1D0CFF5834ABE2654F8C172EA3FF1,
	TerrainData_Internal_SetHeightsDelayLOD_m9DA0CBE8DE96316DC8AA8EBD652ECC42459B3658,
	TerrainData_IsHole_m1C0EDB14F8EDCBF6AB6CFEFC52485B3E8FD04A7B,
	TerrainData_GetHoles_mE81BB7B6E8764586CC5BA130AC075536D0617318,
	TerrainData_SetHoles_mDC03D926A9AD88D971A74C0343C4B0D765B612FA,
	TerrainData_SetHolesDelayLOD_m8397B5D2F861B7F41835E28D96E23CA7141B588B,
	TerrainData_Internal_SetHoles_m212D61E07A95ECAC00950284E590D6CAED57EC66,
	TerrainData_Internal_GetHoles_m13EE36473247F7D4E96C627CE8A4CBF0D4C329C8,
	TerrainData_Internal_IsHole_mF0ADC1C43497B05375BBAC01FD9764C86F7DA109,
	TerrainData_Internal_SetHolesDelayLOD_m00B8068FAA00FC46BC464B19A58082F8BD05F6BC,
	TerrainData_GetSteepness_mA0AD10DFEA5D97CF63DBDB34D99E7A43640D93B9,
	TerrainData_GetInterpolatedNormal_m2925F25FF12A4DC2F2CDD9331F3E2A55D42D7DCE,
	TerrainData_GetAdjustedSize_m17561CC6F50D3E0B7DBA34CA7D8C4F2D57ED5514,
	TerrainData_get_wavingGrassStrength_mEE2A93E488F4349999D85D55DD6CD9DB626FF373,
	TerrainData_set_wavingGrassStrength_m40D1A37A911BD8E430E0CE2FB20A1BDD7E899776,
	TerrainData_get_wavingGrassAmount_mAE3A3E9E51DEE6F3062064D55F5FE90105FA18EA,
	TerrainData_set_wavingGrassAmount_m42B701F18798FEFCFF39AA29855D12A946AB47CC,
	TerrainData_get_wavingGrassSpeed_m4528E32081633762C16079370ADA8DDE1794AB75,
	TerrainData_set_wavingGrassSpeed_m45C0014ECD806D1B8877B734F80BBF0E87D7A7D5,
	TerrainData_get_wavingGrassTint_m9D14E1262509149850E101ABF9A4515AD44CFAE7,
	TerrainData_set_wavingGrassTint_m5BC16B415EAB6493D1E8541FC8391E23E8E06F74,
	TerrainData_get_detailWidth_m145CC1C91FF8C752907B80338DF03440E53AEBB4,
	TerrainData_get_detailHeight_m1DBBB1664689DD08F64A9AF4023248F23865D304,
	TerrainData_get_maxDetailScatterPerRes_m026B44FFA5D74E2AFEEC745D597244B473502851,
	TerrainData_SetDetailResolution_mB4D4C5E5AAACFBB2C624006D93CA04FA22CDA76E,
	TerrainData_Internal_SetDetailResolution_mF138086D635E5D45AF0357589B4107706DD624D3,
	TerrainData_SetDetailScatterMode_m4567232EA53D4B143108ECC6DBA4438BB28ECB33,
	TerrainData_Internal_SetDetailScatterMode_mB43FA9629CA81B76A47545BD841E5CB622AFEB68,
	TerrainData_get_detailPatchCount_m40F4FD2CE4A805B134F39CFD3C2602301A355B4D,
	TerrainData_get_detailResolution_m999CE72D34D858E7D35182FA8DA05CE3C92F8D72,
	TerrainData_get_detailResolutionPerPatch_mFB05C93330007A6A3121327D203F404AD0600622,
	TerrainData_get_detailScatterMode_m31914761E09DA99C2C34709C9BD76609F1077051,
	TerrainData_ResetDirtyDetails_m9A648A3CBA741F3B7621B07B58F51C3B0F95C2F8,
	TerrainData_RefreshPrototypes_mCB97A38BE96FAC08C2F2E8C1F38E88C7450CADBC,
	TerrainData_get_detailPrototypes_m057F428D22C9FCCD36C6BE6768263DE777C6B2C4,
	TerrainData_set_detailPrototypes_m9C9DDFE5DBC3B789A70DAD6D06326C1140057A31,
	TerrainData_GetSupportedLayers_m207D3CF276D6D6CC2F2E6BEF2271245C1DB99BD5,
	TerrainData_GetSupportedLayers_mCE73B4AE3DAE8CC3F53771E6FE67BE34E9E78205,
	TerrainData_GetDetailLayer_m8EB9B85C8CE8836E10D4D54B3A43BFE9AF888591,
	TerrainData_GetDetailLayer_m9B343FA7ABE6C0F24714474A22CDBC7BF94204CC,
	TerrainData_ComputeDetailInstanceTransforms_m81F9EE9500D1C8F2777ADDFFC7D526B5FF445848,
	TerrainData_ComputeDetailCoverage_mBE71CDF1F8B277AD7EDFB335C3C05FA0575A6038,
	TerrainData_SetDetailLayer_m03F76CB703CB3277723319EBF29B4924E22ED84B,
	TerrainData_SetDetailLayer_mD4072ED0A4C79BB8A62DE669F75610DEBF442003,
	TerrainData_Internal_SetDetailLayer_m782D0D38E2A1BDAEAF1F3E3C2D0282991019899F,
	TerrainData_get_treeInstances_mDAB68FD1F3677BD5CB122EA943493D5FC94B2147,
	TerrainData_set_treeInstances_m4B62FA8A516D3839829AA50209897EDB7647F65D,
	TerrainData_Internal_GetTreeInstances_m0DCDC4D93E2CEC457C5BD8D0FE898B5A632E8347,
	TerrainData_SetTreeInstances_m536FA2F30FE5085FCFB05F7BE8D26943214EAA02,
	TerrainData_GetTreeInstance_m7D234B2CC46149BCA0F6B8D79BB749D8C61CC500,
	TerrainData_Internal_GetTreeInstance_mB86AC8651E537E859C80986BF9B9124177B74CBA,
	TerrainData_SetTreeInstance_mADB51401F25DA608A9B9A0E21726BAEFCB2741A3,
	TerrainData_get_treeInstanceCount_mB09A8EC0638DC245D035E2B864EAFEB7EB161982,
	TerrainData_get_treePrototypes_m0A43789B50E554DACB5DF88C86DA23B89DB33EEB,
	TerrainData_set_treePrototypes_m5F309E7FFEB234DA8450C4CBD43EB7045C33BCAA,
	TerrainData_RemoveTreePrototype_mC1BA50DFCA75319E365AF435F518D11608D32EF0,
	TerrainData_RemoveDetailPrototype_m8D48BCD1F68B474E2D2017F57E24793CFB8C2B52,
	TerrainData_NeedUpgradeScaledTreePrototypes_mEAA3D3CDB2DB734F6A8B7BD6D9A564203103F6DB,
	TerrainData_UpgradeScaledTreePrototype_m3A266EA86CFE29A26FC989EDD1DF24C1FAB7E8F7,
	TerrainData_get_alphamapLayers_mF8A0A4F157F7C56354C5A6E3FABF9F230F410F69,
	TerrainData_GetAlphamaps_m2DEF5D2068D54BDAE78661483C1FC4936B06EA01,
	TerrainData_Internal_GetAlphamaps_m6891C19FF72B4394D6BAC3B098A3FCAC1FC6BF36,
	TerrainData_get_alphamapResolution_mC5D1C8FF4A5AFFCCFCF1382FED0D1AD46563D6F8,
	TerrainData_set_alphamapResolution_mECF82D6CF9A3C640B18AB6DE14666BF380E03389,
	TerrainData_GetAlphamapResolutionInternal_m5C312434763B8F0BD8DE760ACF439DFEFAC2F3E5,
	TerrainData_get_Internal_alphamapResolution_mDA8EF6055A2022B3E1E4E6ECBF8DF4387DE8A930,
	TerrainData_set_Internal_alphamapResolution_mA33B7F95DF603037846D76F6061355DDD1C8E556,
	TerrainData_get_alphamapWidth_m07E5B04B08E87AC9F66D766B363000F94C8612D4,
	TerrainData_get_alphamapHeight_m4A8273D6E0E3526A31E2669FBAB240353C086AED,
	TerrainData_get_baseMapResolution_mCD5E59C7A2778009CD4A4CA571B6B85CC0993C0C,
	TerrainData_set_baseMapResolution_mB1F3A79E687A45F695A716B8B7417B35E77C19A7,
	TerrainData_get_Internal_baseMapResolution_m160A9621FE9E5BE642C8CE8C8DA64A07DDF5830F,
	TerrainData_set_Internal_baseMapResolution_mB7C108582B3A0100033DAA477D55A687405B5DD8,
	TerrainData_SetAlphamaps_m2FA8E29ADF4D1B2B8286ED0FE61E0CEB1663E423,
	TerrainData_Internal_SetAlphamaps_m87D35A2FED5E274AEBDF3ADB00A7FA477861E8FD,
	TerrainData_SetBaseMapDirty_m71323D916BC5353D2A357E91C5ECE55E65700618,
	TerrainData_GetAlphamapTexture_mFA6A25F6C09FE64114F69D528046E78B1581366C,
	TerrainData_get_alphamapTextureCount_mF378908EA58CA135A4D58D586179418F1A6F14CD,
	TerrainData_get_alphamapTextures_m82B7287C772D95D4E3D6A5793A8B9A15745D8C45,
	TerrainData_get_splatPrototypes_m349F72923854A74F3EE20D2F09452ED73852923E,
	TerrainData_set_splatPrototypes_m66D557939D09A1116BBAA1CD2E650EE68E0505E0,
	TerrainData_get_terrainLayers_m3B436DF37DDD9F18A46DD6BF112925AD5B8857C8,
	TerrainData_set_terrainLayers_m8FC80DAB5CD38A11CDB082F94DBD3186CD434671,
	TerrainData_AddTree_m3DA41B91351531F932E4172A5E1DA1A5359FEA96,
	TerrainData_RemoveTrees_m6600E1C337753D0C6B1B08AC2B4C1E6C46A314E4,
	TerrainData_Internal_CopyActiveRenderTextureToHeightmap_m431A62A0EC8B28CE9CA58FB081A58123060EB378,
	TerrainData_Internal_DirtyHeightmapRegion_mF080B77155696B5E1E0715540DE8031A3F421FC4,
	TerrainData_SyncHeightmap_mD8D77E9DD9063B7201D062C810CFEAF2BE743267,
	TerrainData_Internal_CopyActiveRenderTextureToHoles_m3181CE7CD4B40E3EB42B571E3489A2226C6E0A1A,
	TerrainData_Internal_DirtyHolesRegion_mBC52464648FFA679E45C579BCC3B9FFF68DD3EBA,
	TerrainData_Internal_SyncHoles_m536B2E7102335849E42E867DB27166BB55FED0F6,
	TerrainData_Internal_MarkAlphamapDirtyRegion_mB212FE00063E5391813EA3C57C71AF5669F9C064,
	TerrainData_Internal_ClearAlphamapDirtyRegion_m570C14031954B78348B93B24361EB0B570622588,
	TerrainData_Internal_SyncAlphamaps_mFDFD1F09BB38C77A2F408331299CCF586FDC0A33,
	TerrainData_get_atlasFormat_m68777C54DF215D47A6249076DD2A2CC0E123DAA9,
	TerrainData_get_users_m0C569F1AD5853CEBF3C572723A1CAD04AC8433C4,
	TerrainData_get_SupportsCopyTextureBetweenRTAndTexture_mE93A6D23586EABCFC4B1267F66E98A8C6C7E35BE,
	TerrainData_CopyActiveRenderTextureToHeightmap_m36D01BB8242D02F3E319271CA2BC2A6B5C428EE5,
	TerrainData_DirtyHeightmapRegion_m92C3A0182EE87E484316B0514F25C3D106349AB7,
	TerrainData_get_AlphamapTextureName_mC3E6B77E19252385A78C74D853F7CA4375A51882,
	TerrainData_get_HolesTextureName_m050635081E2E26FDE05BFD35253710DE84E7A12F,
	TerrainData_CopyActiveRenderTextureToTexture_mC6FBAB316DCFA11F9A51A32F2943B01B0AA79A74,
	TerrainData_DirtyTextureRegion_mB66EE109AB4B647EDDC49E3B7A2C8F1CA0215FA5,
	TerrainData_SyncTexture_m4799EFAE1FACCB344F25855A7190C0CC3F8C9B2D,
	TerrainData__cctor_m525F8AF6DEDDACF640BD2D24767502121ED6D9B0,
	TerrainData_get_heightmapScale_Injected_m3F94935D94B0B8029E2145C33B023AB8677C39AB,
	TerrainData_get_size_Injected_m0F56E87C4D7EDD1D84F038E4AF0F273D328CF661,
	TerrainData_set_size_Injected_mF315143468DD64DB825C9D0A03EF491E035666D8,
	TerrainData_get_bounds_Injected_mD98BEEB610CCB2688123B28111C35D3E6415BDE5,
	TerrainData_GetInterpolatedNormal_Injected_mF6E35FCDF546E498658E864D47EB87C22B41E571,
	TerrainData_get_wavingGrassTint_Injected_mDDE7C4FCA35CCFAF7D2674D225DC36B4D77CAD25,
	TerrainData_set_wavingGrassTint_Injected_mDF8BCA497383058F5054AE782FFE9CAFE63FF142,
	TerrainData_Internal_GetTreeInstance_Injected_mE634C00ADF80B11B02A395DB539C113CC2230119,
	TerrainData_SetTreeInstance_Injected_m17BD0E8478078C994F5CCADD825CB03F334D62A2,
	TerrainData_RemoveTrees_Injected_m52522817B1A5F12F9505DFD8F62D85E647DB9B8A,
	TerrainData_Internal_CopyActiveRenderTextureToHeightmap_Injected_mBCC5601037CF0D8C81E936AB9C6270C2E128EF7C,
	TerrainData_Internal_CopyActiveRenderTextureToHoles_Injected_m79962847A7F930086EE6436D8D0EDFABF3C3F967,
	TerrainLayer__ctor_m3B6D12B4296D98B5813C08DF87D09022BD8FDC74,
	TerrainLayer_Internal_Create_mA5EB262D9B9D9B3180705A14040FBEAA4257D3E1,
	TerrainLayer_get_diffuseTexture_mAF75D09F08293C26B26D7D422B4A0ACC9732DD31,
	TerrainLayer_set_diffuseTexture_mC21DEF81B6B58FD1BA8FA381FD569E2DB6173C53,
	TerrainLayer_get_normalMapTexture_mCBC9DFA1E5243F5227A99A2D7CBF53BE259E9620,
	TerrainLayer_set_normalMapTexture_m6E4BC16FFD3E9C2351E208F38CB1C8B99DD3F5AE,
	TerrainLayer_get_maskMapTexture_m184F1143BA11213B5B6E1D3487BCDF8A747954EA,
	TerrainLayer_set_maskMapTexture_m7A5384131F06DF24AB8F9B2D0E8C7A8510A17F78,
	TerrainLayer_get_tileSize_mF6313141BE22D6AC7362DA9A40FB41236458E0A3,
	TerrainLayer_set_tileSize_m53D498E2704EF4B92B11588CF47C726923D57F84,
	TerrainLayer_get_tileOffset_m1E81B6D002E91BE4EF402337C595227221E8163D,
	TerrainLayer_set_tileOffset_m259D3D0F278A65D405923BF0338766BDA79556A9,
	TerrainLayer_get_specular_m14209D88524F3739C026F906A8D34B3597EEE07A,
	TerrainLayer_set_specular_mE0E85F57BCD6D771826D57562E6EADDA4E14F538,
	TerrainLayer_get_metallic_m99FCD988D3CE9CADBDD23C9E0C92095DB66F2EE8,
	TerrainLayer_set_metallic_mAD23C76C3953D3A6D54CFC9B399A13841F8F1E87,
	TerrainLayer_get_smoothness_m79BBB46810976A88A0BADB0D52A0C028781C7120,
	TerrainLayer_set_smoothness_mB79F6272E5AA2F3F103B22382A2FFEB418EB1172,
	TerrainLayer_get_normalScale_m63EA677D87ABD1DCF0195D35A8B9738B80C70B3C,
	TerrainLayer_set_normalScale_m0758116D1F0B171EDB48B4E2A13490C1A5703991,
	TerrainLayer_get_diffuseRemapMin_m949BAC51C5758A977052835D009CC8A2949E9F98,
	TerrainLayer_set_diffuseRemapMin_m5899CADEF34D6193180D9B79B1E86E38C05869A9,
	TerrainLayer_get_diffuseRemapMax_mCA5648C5150800713F24E7D2AB96B4D548E2D4BC,
	TerrainLayer_set_diffuseRemapMax_mEFA03F94C12ECEFEC2060901D966DD15BDA89D3C,
	TerrainLayer_get_maskMapRemapMin_m7DF5C3CD76EA311585C8AC7102DF8FE843465368,
	TerrainLayer_set_maskMapRemapMin_mD8778273503FD0FD05869EB86DB6EADDA553856F,
	TerrainLayer_get_maskMapRemapMax_mE05D91C74E45DE9F2A5AB6ABE75B2BC561ED6342,
	TerrainLayer_set_maskMapRemapMax_m7E81C871C9AABC01E2F743E690FD3E9002DB008C,
	TerrainLayer_get_tileSize_Injected_m8D8A89EA332B064157DB6A83826870E691AA4FC5,
	TerrainLayer_set_tileSize_Injected_m2D6D83EA3DFBB1948BDC4D1984BCE9D43CAF18B2,
	TerrainLayer_get_tileOffset_Injected_mA2FADD8981A297BFB4DE8663FA757834324DB8E0,
	TerrainLayer_set_tileOffset_Injected_m4082583251015F2D3621EE132031B852741C092C,
	TerrainLayer_get_specular_Injected_m17BCA78D6D0B917E71AE7D441367C8A30149BC3C,
	TerrainLayer_set_specular_Injected_mB6275B761452010AEE54E6B15F63435FA7A1D485,
	TerrainLayer_get_diffuseRemapMin_Injected_m12394DF34C1CC7B138FA3389D4583EC00D555FF0,
	TerrainLayer_set_diffuseRemapMin_Injected_mFA1B63F9AA91C8EA82104C4CC1A7778C5AED7706,
	TerrainLayer_get_diffuseRemapMax_Injected_m2E247FD71A4D51D8E54DA3BF504C1731FB6F23AC,
	TerrainLayer_set_diffuseRemapMax_Injected_m90271AB60993CFD03E422C688FAFC64017D4BEE3,
	TerrainLayer_get_maskMapRemapMin_Injected_mEC87DA9E3173F7D39C047FABAB507CC4351FDF24,
	TerrainLayer_set_maskMapRemapMin_Injected_m388350F2F0C06A10AF0DA39725B59103F6EE7F2C,
	TerrainLayer_get_maskMapRemapMax_Injected_m1BA6C54D1C610E455C6A7919E5E08EA304CA7403,
	TerrainLayer_set_maskMapRemapMax_Injected_m10CF5BC6AA549FC669BCB7881D6D75D569EC20EE,
	TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A,
	TerrainMap_GetTerrain_mFF9C935F05859DF70E95994E727565BD67CDD6FC,
	TerrainMap_CreateFromConnectedNeighbors_m4AA17A1E61EBA1295BE150640D2D38B0E1EDD739,
	TerrainMap_CreateFromPlacement_mC7822A5F4FC2A2CB119259A48F19D364ACEC5AE7,
	TerrainMap_CreateFromPlacement_m64B90ADBC1D3A1AE18CEC7D0B452377E10B2BCB5,
	TerrainMap_get_terrainTiles_m9EAA8FCB972C834E2093DDD49B26DBBA2E74A2AB,
	TerrainMap__ctor_mCDB47BA50D9D54E65754028F9CF8F91828FE616F,
	TerrainMap_AddTerrainInternal_m507CE3A3F880B33CA2330F69464E3511D5B9BD71,
	TerrainMap_TryToAddTerrain_m03A05C883F317FD2E6956ADD6625409E8A90BE15,
	TerrainMap_ValidateTerrain_m8D9B035B3851E0ED8BB5877BD11F63BA85029653,
	TerrainMap_Validate_mAFBB4A2D0290E25D59902A1BD5DA1EBC2ACD1326,
	QueueElement__ctor_m1A6C6D9526BCDA206E451B66F7AF9AFF5445F125,
	U3CU3Ec__DisplayClass3_0__ctor_mAFD4AEF760F5CC7CE66BAD750DAD3697397E8945,
	U3CU3Ec__DisplayClass3_0_U3CCreateFromPlacementU3Eb__0_m703A4D4E3D378C9896199B70A89FCDF1A07C737B,
	TerrainUtility_ValidTerrainsExist_m0DD08E4CEC739929A9AEBCEA849EDFE79985A207,
	TerrainUtility_ClearConnectivity_m7448E42CD3F2941EF02C10DE358778EEAF9B0AA9,
	TerrainUtility_CollectTerrains_mDFCA0AFA00FFD16CEC8B4EFA9C55E3B7B6803EC4,
	TerrainUtility_AutoConnect_m3E435D139BE402DC495248EDD1FF2C1E9377A897,
	U3CU3Ec__DisplayClass2_0__ctor_m4C022C4675BA4CFC7E7AAA5692979CDE6CD8E611,
	U3CU3Ec__DisplayClass2_1__ctor_mA329ED5B221AE8787EAEA1124A2A95675FDD1695,
	U3CU3Ec__DisplayClass2_1_U3CCollectTerrainsU3Eb__0_m57E871EB2399E5FB7DF78B3C9EBFBF152116AC2C,
	BrushTransform_get_brushOrigin_m77837ABD7D3250A219DF37D4943E6C8F87A12EE1,
	BrushTransform_get_brushU_m96C6C42F19670792A93667FB998D6AB9C5233835,
	BrushTransform_get_brushV_mD5FE860D6229C74AE92086F15835ED4F232814E2,
	BrushTransform_get_targetOrigin_m257AB19A9D74C20E09A0178A2B909065744CBB1A,
	BrushTransform_get_targetX_mBFDE3565FA7AA37EC5C15F58C6C04711F39B67D4,
	BrushTransform_get_targetY_m35E99EA7B0B5E7502BDC49D9D985722B99ED3B95,
	BrushTransform__ctor_mC0DC3696A91B14BEE4FF118A3723BF2311D2524D,
	BrushTransform_GetBrushXYBounds_mF0BB5DFE1215819C5432C0672E73A27BDA1ED76B,
	BrushTransform_FromRect_m848D89CB4297AAF0ECD870EA1E26B2063C9490A9,
	BrushTransform_ToBrushUV_mADE37644B70BB500341B48F983845F9436ADF4BD,
	BrushTransform_FromBrushUV_mE9216C72AE603E81CF9630F1C19DAAA4751DE9CA,
	PaintContext_get_originTerrain_mCD05E4154C291B8E4400902ABA56B3C017E52338,
	PaintContext_get_pixelRect_m5B74BA7635A35A622595FF04BFFC1C982BCDECB3,
	PaintContext_get_targetTextureWidth_m6ECD3E0E648DBB6632C633E06B5D6037A0B2CFEB,
	PaintContext_get_targetTextureHeight_m4975CA86AC71297D69844835B8D006174F806240,
	PaintContext_get_pixelSize_m625AE2931B8FBA55050107943314D6202DD930C5,
	PaintContext_get_sourceRenderTexture_m45B68671807EA82C3333635B0C5BB8F5D5161B86,
	PaintContext_set_sourceRenderTexture_m729A4274969D259E689DA526313697ACA2299CD6,
	PaintContext_get_destinationRenderTexture_m5E3BA23300AA39CB2A260F795561FD707DB91E92,
	PaintContext_set_destinationRenderTexture_mE0DC8FD99A811C95223A9F7F0082D139EE3CF4CC,
	PaintContext_get_oldRenderTexture_mBE3586662ED5A1DA7AB82FC7FB01D674095E48EA,
	PaintContext_set_oldRenderTexture_m911313349E453DD49EC9DB4606E9A4E5C86BC043,
	PaintContext_get_terrainCount_mDE945E81F9C5229C79734258BA00235C44B3D17C,
	PaintContext_GetTerrain_mCBF0B805F670C14BF56C7FE13FBA9408B7649ED8,
	PaintContext_GetClippedPixelRectInTerrainPixels_m4B5AF3E6861DDCB269036E6E02FC9D5CD950DA09,
	PaintContext_GetClippedPixelRectInRenderTexturePixels_mBFF751A429A4EDAB774A7293FB09DDF176683EE6,
	PaintContext_get_heightWorldSpaceMin_m4459086F36CF286988016E0CF4B3CC03F531D22B,
	PaintContext_get_heightWorldSpaceSize_m928330CAAFBD610A719F90DFBEA37D9988E031FB,
	PaintContext_get_kNormalizedHeightScale_m4C0AF7DDF82CEB71CA3A3418837ADEB54F4F6585,
	PaintContext_add_onTerrainTileBeforePaint_m483D38714ABEE42335EA4A60683A563ABA07BB2F,
	PaintContext_remove_onTerrainTileBeforePaint_m6DED2BD72D5FDC60E2DD23DF321B7260212B1F63,
	PaintContext_ClampContextResolution_m90A8DECD94A9CBDA1BA2ABB4AF30B2D4A3C241B6,
	PaintContext__ctor_m5F36E6944154C93E1D271D4A9C077C8DAB108F86,
	PaintContext_CreateFromBounds_mE034417CBA9CD6F83EAB77DA319323F458CD04E4,
	PaintContext_FindTerrainTilesUnlimited_mC85574536AE3A36A4A9399DAC6F8C5171FAE1433,
	PaintContext_CreateRenderTargets_m08252C94D832923CD0F129B6EEA1305DEAF18BB5,
	PaintContext_Cleanup_mEB09BBB29CF7FBC5E11FC63BB2A3D651B27EB28A,
	PaintContext_GatherInternal_mB58AFCC343495B1A7E2D742CFD75F337A6FE781D,
	PaintContext_ScatterInternal_m18C7B496911CC4C7C95E40A6220B1ACCE8427149,
	PaintContext_Gather_mC468CF561D85DFF52AA72668070131027D66A594,
	PaintContext_Scatter_m5A8E066AD67DB6D22F7BF62508A7F2BDC45BD576,
	PaintContext_GatherHeightmap_m87B6F640A04CB3F1E8871102F2024B4D0DDE302E,
	PaintContext_ScatterHeightmap_mC457E5310E278339150CD517F59F43AAE1CE1320,
	PaintContext_GatherHoles_m856265068B3F5493674358D99163E622CFA1885A,
	PaintContext_ScatterHoles_m9F2B1CCB1613FFDAF31B569E4D72E23C398BF355,
	PaintContext_GatherNormals_mD28D1EE6ED2531984EE8FD64BA793CA752AF06B5,
	PaintContext_GetTerrainLayerUserData_m2816B0CA7CA72A0492887923DE89F9382877E632,
	PaintContext_GatherAlphamap_m9CF436C1D0D163692E9EDF27958A1704D06E3F44,
	PaintContext_ScatterAlphamap_m207A3E71E8014A4639C37905471D1F9C85404C91,
	PaintContext_OnTerrainPainted_mA6BF9D65B8EA95D18E69FBC02D538588DEF3385E,
	PaintContext_ApplyDelayedActions_mC92DBABBF663D58900E7376026C0EEEBD5B6B3DA,
	PaintContext__cctor_m381829AD174E3AB512452C6158E625AC70AEF66B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_terrain_m2B00DE94A36AD70ED919E482E7B45A88C6524D27,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_clippedTerrainPixels_mF4756DB3495110649E77905E24EDADBF345CBE02,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_clippedPCPixels_m58D39841BC4DDD19502995895D3BC6990618798F,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_paddedTerrainPixels_mC2F832F2BFAC0E9432D21DCA3FEDA88163854E88,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_paddedPCPixels_m01D7520FB0A8D050E53E99E07F096912B3017551,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_gatherEnable_m986CD9B32972EC18328285CEFFF08A4593D3F2CE,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_set_gatherEnable_mD98909C10D49D400863D7E1917ED046B316FF8B6,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_scatterEnable_m2919CBEB18721C001621F385D13D8F83990E177D,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_set_scatterEnable_mBA474FDF265ED8CBB6D5888F51B8DF7FCC4D8B72,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_get_userData_m8A130E699EDBE85C316C4DE83C1E3429D117F0DD,
	TerrainTile_UnityEngine_TerrainTools_PaintContext_ITerrainInfo_set_userData_m0118BB95E521E20483826CC9D336DA2D74715A8B,
	TerrainTile_Make_m464414DF78B623306730F00C6DDE305D9A46AEFD,
	TerrainTile__ctor_mCDA7097EEEC5D66D7E2E2A1AD7778F783EB20F8C,
	SplatmapUserData__ctor_m8B0FF764C4FB0A470238FF13FBDAD970DEDCD713,
	U3CU3Ec__cctor_mE59BB9EAF12CDCEAB09C34BC847703237F78E2E2,
	U3CU3Ec__ctor_m0F1F9BD4FD33A770EBD350CFB01B2099B03E3354,
	U3CU3Ec_U3CGatherHeightmapU3Eb__60_0_m23F229C5102FCC7A2AD3B5AD12A14F0992B1010C,
	U3CU3Ec_U3CScatterHeightmapU3Eb__61_0_mB419973E110683FFE1A28372744E29E66C0DDB2A,
	U3CU3Ec_U3CScatterHeightmapU3Eb__61_2_mF9AB8BADF2E47F638CE89FF180B6A9D2025EC6B6,
	U3CU3Ec_U3CGatherHolesU3Eb__62_0_m2FCAB1248BAD362632AAE5D40707788956C4BC82,
	U3CU3Ec_U3CGatherNormalsU3Eb__64_0_m584859F8C89B95EB220738028E1F8D07817342F1,
	U3CU3Ec__DisplayClass53_0__ctor_mB1DCC08BEBB1B500CAD7277614120E2743B5853E,
	U3CU3Ec__DisplayClass53_0_U3CFindTerrainTilesUnlimitedU3Eb__0_mA575AAFB77E11432AD4789A5E4DB47EE29ABA676,
	U3CU3Ec__DisplayClass60_0__ctor_m8B39322C4D4896391FF55C7EBC497A8F3EBA0CCA,
	U3CU3Ec__DisplayClass60_0_U3CGatherHeightmapU3Eb__1_m98A9800C587B48C9A620B69F335D5698024F8724,
	U3CU3Ec__DisplayClass61_0__ctor_mE3CAACC693950F073FB23BA5EA4A7E72BA7AA4EE,
	U3CU3Ec__DisplayClass61_0_U3CScatterHeightmapU3Eb__1_mBE666B97592271745BB7EF56B09DCBB7FCD672F1,
	U3CU3Ec__DisplayClass63_0__ctor_m35AAEE93BCA8DD28A4096DAA0A1EF5B390B20429,
	U3CU3Ec__DisplayClass63_0_U3CScatterHolesU3Eb__0_m63EEA7EC9D7883B1704B7FEB6E42FD0735416B87,
	U3CU3Ec__DisplayClass66_0__ctor_m7546F25D02A8C4AE3CA8A1966E400F3C3DCC3FF3,
	U3CU3Ec__DisplayClass66_0_U3CGatherAlphamapU3Eb__0_m267E46A16FA8D4413C38D05DB8E68C242563046F,
	U3CU3Ec__DisplayClass66_0_U3CGatherAlphamapU3Eb__1_mC736CCFD74BEA0444925BA18C5D877590161F531,
	U3CU3Ec__DisplayClass67_0__ctor_mF166EB7437F0888686AA463B2ED545AB463B15E6,
	U3CU3Ec__DisplayClass67_0_U3CScatterAlphamapU3Eb__0_m9ABB337FFDEF8CA150010C341050F1ACE434ED78,
	TerrainPaintUtility_GetBuiltinPaintMaterial_mA169E2BA319EF1B0624A52D8E604D384AE9ABA4C,
	TerrainPaintUtility_GetBrushWorldSizeLimits_m90DF01D033170FD2AEA6A7A855D650C6A636AE55,
	TerrainPaintUtility_CalculateBrushTransform_mC09FF992CED60AADB39F6FCED42DEE79BBD232DB,
	TerrainPaintUtility_BuildTransformPaintContextUVToPaintContextUV_m8D9BF942AA822C79029BF555CDF53F5A9CA93FA0,
	TerrainPaintUtility_SetupTerrainToolMaterialProperties_mEC21EFEE7F7829E3D8D84B92EB64C5CAFE03CAF4,
	TerrainPaintUtility_get_paintTextureUsesCopyTexture_m3B6A9141CAD26F632175A9E2900D561AAC114D32,
	TerrainPaintUtility_InitializePaintContext_m4AF5ECCA84C9B1B010244E35544C4E3B41F97F70,
	TerrainPaintUtility_ReleaseContextResources_mA0E11F8951E9AD5575756F1C60409EFB64DE35BA,
	TerrainPaintUtility_BeginPaintHeightmap_mC0188F34F03BE6B6CD23F65479F39EA9A3301F2A,
	TerrainPaintUtility_EndPaintHeightmap_m8BA767909C7EC5B02B88EDEA0D4B5F2341373F84,
	TerrainPaintUtility_BeginPaintHoles_m78D3B2BA0F36F05B9F1873A02B2627FA4D2069F8,
	TerrainPaintUtility_EndPaintHoles_m0D7B87C922FC65BB92862DEBEEC39F1D2202E7A8,
	TerrainPaintUtility_CollectNormals_mEA3059F427359B4569259FECF905328EC59D3FC7,
	TerrainPaintUtility_BeginPaintTexture_m0BBFA0F86453D0363334C3E960CF7AB9AF40EA4C,
	TerrainPaintUtility_EndPaintTexture_m06D132134AC924F27FA3F5598E3C9F91CFC1A936,
	TerrainPaintUtility_GetBlitMaterial_mE30CA5859A38CD72365ADAAF487060AA531FB583,
	TerrainPaintUtility_GetHeightBlitMaterial_m118BD111DB96A8AA7FAABD16AAD2FA856D9C72AA,
	TerrainPaintUtility_GetCopyTerrainLayerMaterial_m4AE79A9122DBF2DF8A4932CBA9E2D7347CDACDA6,
	TerrainPaintUtility_DrawQuad_m3DF46A679C95B544FD1CA783020DFDFDA084728B,
	TerrainPaintUtility_DrawQuad2_m70891EE13A543416FBBDF98FEEC6499A2B38494A,
	TerrainPaintUtility_DrawQuadPadded_m48168E3A5DF4E5063D290556E06E8FF75EDD1552,
	TerrainPaintUtility_CalcPixelRectFromBounds_mA0F27994D74342C8EECA59B32E3B24CEF52D3334,
	TerrainPaintUtility_GetTerrainAlphaMapChecked_m61B8DC77E576C67F4A93CA116608ABC0786E4A92,
	TerrainPaintUtility_FindTerrainLayerIndex_mF21091EAB56A8C467F80D187A800D1288FDB135A,
	TerrainPaintUtility_AddTerrainLayer_mF5232126EC2C8B14BBC0ACDA0AD60652002DEC0A,
};
extern void PatchExtents_get_min_mCDE97D43190F69E3E33F187EF510D05E6BE36171_AdjustorThunk (void);
extern void PatchExtents_set_min_m767CBF5D34E86C328619AC9066FEE97A30D45254_AdjustorThunk (void);
extern void PatchExtents_get_max_m54D71762D0C9923352FC5ACF3AECA7FA88759FD6_AdjustorThunk (void);
extern void PatchExtents_set_max_mD20D2F8F79550A260A9A8AC21B46A4871B0423DE_AdjustorThunk (void);
extern void TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A_AdjustorThunk (void);
extern void QueueElement__ctor_m1A6C6D9526BCDA206E451B66F7AF9AFF5445F125_AdjustorThunk (void);
extern void BrushTransform_get_brushOrigin_m77837ABD7D3250A219DF37D4943E6C8F87A12EE1_AdjustorThunk (void);
extern void BrushTransform_get_brushU_m96C6C42F19670792A93667FB998D6AB9C5233835_AdjustorThunk (void);
extern void BrushTransform_get_brushV_mD5FE860D6229C74AE92086F15835ED4F232814E2_AdjustorThunk (void);
extern void BrushTransform_get_targetOrigin_m257AB19A9D74C20E09A0178A2B909065744CBB1A_AdjustorThunk (void);
extern void BrushTransform_get_targetX_mBFDE3565FA7AA37EC5C15F58C6C04711F39B67D4_AdjustorThunk (void);
extern void BrushTransform_get_targetY_m35E99EA7B0B5E7502BDC49D9D985722B99ED3B95_AdjustorThunk (void);
extern void BrushTransform__ctor_mC0DC3696A91B14BEE4FF118A3723BF2311D2524D_AdjustorThunk (void);
extern void BrushTransform_GetBrushXYBounds_mF0BB5DFE1215819C5432C0672E73A27BDA1ED76B_AdjustorThunk (void);
extern void BrushTransform_ToBrushUV_mADE37644B70BB500341B48F983845F9436ADF4BD_AdjustorThunk (void);
extern void BrushTransform_FromBrushUV_mE9216C72AE603E81CF9630F1C19DAAA4751DE9CA_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[16] = 
{
	{ 0x060000E1, PatchExtents_get_min_mCDE97D43190F69E3E33F187EF510D05E6BE36171_AdjustorThunk },
	{ 0x060000E2, PatchExtents_set_min_m767CBF5D34E86C328619AC9066FEE97A30D45254_AdjustorThunk },
	{ 0x060000E3, PatchExtents_get_max_m54D71762D0C9923352FC5ACF3AECA7FA88759FD6_AdjustorThunk },
	{ 0x060000E4, PatchExtents_set_max_mD20D2F8F79550A260A9A8AC21B46A4871B0423DE_AdjustorThunk },
	{ 0x060001AA, TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A_AdjustorThunk },
	{ 0x060001B5, QueueElement__ctor_m1A6C6D9526BCDA206E451B66F7AF9AFF5445F125_AdjustorThunk },
	{ 0x060001BF, BrushTransform_get_brushOrigin_m77837ABD7D3250A219DF37D4943E6C8F87A12EE1_AdjustorThunk },
	{ 0x060001C0, BrushTransform_get_brushU_m96C6C42F19670792A93667FB998D6AB9C5233835_AdjustorThunk },
	{ 0x060001C1, BrushTransform_get_brushV_mD5FE860D6229C74AE92086F15835ED4F232814E2_AdjustorThunk },
	{ 0x060001C2, BrushTransform_get_targetOrigin_m257AB19A9D74C20E09A0178A2B909065744CBB1A_AdjustorThunk },
	{ 0x060001C3, BrushTransform_get_targetX_mBFDE3565FA7AA37EC5C15F58C6C04711F39B67D4_AdjustorThunk },
	{ 0x060001C4, BrushTransform_get_targetY_m35E99EA7B0B5E7502BDC49D9D985722B99ED3B95_AdjustorThunk },
	{ 0x060001C5, BrushTransform__ctor_mC0DC3696A91B14BEE4FF118A3723BF2311D2524D_AdjustorThunk },
	{ 0x060001C6, BrushTransform_GetBrushXYBounds_mF0BB5DFE1215819C5432C0672E73A27BDA1ED76B_AdjustorThunk },
	{ 0x060001C8, BrushTransform_ToBrushUV_mADE37644B70BB500341B48F983845F9436ADF4BD_AdjustorThunk },
	{ 0x060001C9, BrushTransform_FromBrushUV_mE9216C72AE603E81CF9630F1C19DAAA4751DE9CA_AdjustorThunk },
};
static const int32_t s_InvokerIndices[568] = 
{
	4250,
	3881,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4360,
	3982,
	4360,
	3982,
	4168,
	3807,
	4168,
	3807,
	3166,
	2729,
	4216,
	3852,
	4216,
	3852,
	3881,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4168,
	3807,
	4168,
	3807,
	4250,
	4168,
	3807,
	4358,
	3980,
	3590,
	3969,
	1458,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4358,
	4364,
	2169,
	3881,
	3881,
	3881,
	4216,
	3852,
	4168,
	3807,
	9018,
	9018,
	9018,
	9018,
	9018,
	9018,
	9018,
	9018,
	9018,
	9018,
	9031,
	9089,
	9031,
	8887,
	8887,
	8505,
	4250,
	4250,
	4250,
	4250,
	4350,
	3973,
	4298,
	3928,
	4168,
	3807,
	4216,
	3852,
	4172,
	3811,
	4298,
	3928,
	4364,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3575,
	3788,
	3788,
	1920,
	8887,
	5545,
	7933,
	7925,
	4250,
	3881,
	4168,
	4364,
	4364,
	8887,
	8887,
	8887,
	8887,
	6994,
	6190,
	2798,
	2097,
	801,
	3881,
	2798,
	1462,
	495,
	3881,
	4250,
	3881,
	4298,
	3928,
	4216,
	3852,
	4364,
	3881,
	3185,
	4216,
	3185,
	3079,
	7252,
	4250,
	3881,
	4250,
	3881,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4172,
	3811,
	4172,
	3811,
	4216,
	3852,
	4168,
	3807,
	4168,
	3807,
	4298,
	3928,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4364,
	3881,
	3185,
	4216,
	3185,
	4168,
	3079,
	7252,
	6327,
	9089,
	4250,
	3881,
	4250,
	3881,
	4356,
	3978,
	4356,
	3978,
	4172,
	3811,
	4298,
	3928,
	4298,
	3928,
	4364,
	4298,
	3928,
	4298,
	3928,
	8395,
	4364,
	8887,
	866,
	4216,
	4216,
	4250,
	4216,
	3852,
	4216,
	3852,
	4358,
	4250,
	4168,
	3807,
	4250,
	4168,
	4250,
	4250,
	4216,
	4358,
	3980,
	4166,
	4298,
	3928,
	2567,
	2579,
	503,
	136,
	92,
	1169,
	1169,
	1974,
	869,
	4250,
	3881,
	4250,
	3881,
	1974,
	869,
	2296,
	1169,
	1974,
	1974,
	869,
	1169,
	2296,
	869,
	2579,
	2629,
	3415,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4172,
	3811,
	4216,
	4216,
	4216,
	2734,
	2734,
	3852,
	3852,
	4216,
	4216,
	4216,
	4216,
	4364,
	4364,
	4250,
	3881,
	1169,
	2547,
	758,
	1826,
	760,
	3581,
	1348,
	2176,
	544,
	4250,
	3881,
	4250,
	2788,
	3622,
	3622,
	2747,
	4216,
	4250,
	3881,
	3852,
	3852,
	4168,
	4364,
	4216,
	1169,
	1169,
	4216,
	3852,
	4298,
	4216,
	3852,
	4216,
	4216,
	4216,
	3852,
	4216,
	3852,
	1974,
	869,
	4364,
	3515,
	4216,
	4250,
	4250,
	3881,
	4250,
	3881,
	3788,
	1738,
	1502,
	868,
	4364,
	1501,
	866,
	4364,
	868,
	3852,
	4364,
	4216,
	4250,
	8993,
	2132,
	2835,
	9031,
	9031,
	939,
	2097,
	3881,
	9089,
	3788,
	3788,
	3788,
	3788,
	2146,
	3788,
	3788,
	2728,
	2728,
	1695,
	1292,
	1291,
	4364,
	8887,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4356,
	3978,
	4356,
	3978,
	4172,
	3811,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4360,
	3982,
	4360,
	3982,
	4360,
	3982,
	4360,
	3982,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	2734,
	2507,
	6690,
	6690,
	5997,
	4250,
	4364,
	1974,
	1619,
	2734,
	4216,
	1974,
	4364,
	3185,
	8993,
	9089,
	8488,
	9089,
	4364,
	4364,
	3185,
	4356,
	4356,
	4356,
	4356,
	4356,
	4356,
	2174,
	4276,
	8196,
	3662,
	3662,
	4250,
	4277,
	4216,
	4216,
	4356,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4216,
	3515,
	3561,
	3561,
	4298,
	4298,
	9064,
	8887,
	8887,
	8395,
	639,
	4798,
	2688,
	3852,
	3807,
	353,
	618,
	569,
	960,
	4364,
	3881,
	4364,
	3881,
	4364,
	1796,
	2788,
	3881,
	7969,
	9089,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	4277,
	4277,
	4277,
	4277,
	4168,
	3807,
	4168,
	3807,
	4250,
	3881,
	4787,
	4364,
	4364,
	9089,
	4364,
	3518,
	3518,
	3881,
	3518,
	3518,
	4364,
	3185,
	4364,
	3881,
	4364,
	3881,
	4364,
	3518,
	4364,
	3518,
	3881,
	4364,
	3518,
	9031,
	5078,
	5624,
	6979,
	6947,
	8993,
	4652,
	8887,
	5963,
	7975,
	5963,
	7975,
	5963,
	5383,
	7975,
	9031,
	9031,
	9031,
	7016,
	5588,
	5589,
	5043,
	7626,
	7489,
	7489,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TerrainModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule = 
{
	"UnityEngine.TerrainModule.dll",
	568,
	s_methodPointers,
	16,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_TerrainModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
