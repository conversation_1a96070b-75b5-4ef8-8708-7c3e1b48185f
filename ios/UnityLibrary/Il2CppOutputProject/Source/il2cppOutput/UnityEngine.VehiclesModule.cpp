﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_VehiclesModule[];
IL2CPP_EXTERN_C RuntimeField* WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider__ctor_mD44B2C97960274797DABD055BBEB18E664DAD906_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_get_center_mE21895BF5F8C9C2523B9468B2C9DA88737A4E163_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_set_center_m734D85FB64BF40A15DB24954792FCD177D4148BB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_forwardDir_mEC17414DD098E3BB4080E356221BB5655E3E3100_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_sidewaysDir_mF1D87D1AFD314249E2BCC4F7FCD78B03752BD475_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_collider_m661F87C36616207CF90615A16C102A6124EB15B2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_force_m78BDB39182947B9A52424CBA33D2874D61A7232F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_forwardDir_m0A02053AD079E33E59E488929C0E84CB07BF6D92_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_forwardSlip_mB96F4BFFC19D83031568E1A1BCF20B1AA61B91A4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_normal_m2A9BD0A7000CC5101129402A92AA2589C65F8454_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_point_m6FE90840F9AC30F65E5E9DF4561BC4146D60E507_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_sidewaysDir_mA8A9A18917737810ED96C9B7E7BD8E4AA4FD3D64_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* WheelHit_set_sidewaysSlip_m66A9374E3A9F8C12EDD482BC1C370809DCD79C1E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t3E8E0BA57A4D4D0EF43301B668B802ED48E39035 
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 
{
	float ___spring;
	float ___damper;
	float ___targetPosition;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 
{
	float ___m_ExtremumSlip;
	float ___m_ExtremumValue;
	float ___m_AsymptoteSlip;
	float ___m_AsymptoteValue;
	float ___m_Stiffness;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir;
	float ___m_Force;
	float ___m_ForwardSlip;
	float ___m_SidewaysSlip;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider;
};
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir;
	float ___m_Force;
	float ___m_ForwardSlip;
	float ___m_SidewaysSlip;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider;
};
struct WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_ForwardDir;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_SidewaysDir;
	float ___m_Force;
	float ___m_ForwardSlip;
	float ___m_SidewaysSlip;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481  : public Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___identityQuaternion;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_collider_m661F87C36616207CF90615A16C102A6124EB15B2 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_point_m6FE90840F9AC30F65E5E9DF4561BC4146D60E507 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_normal_m2A9BD0A7000CC5101129402A92AA2589C65F8454 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_forwardDir_mEC17414DD098E3BB4080E356221BB5655E3E3100 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_forwardDir_m0A02053AD079E33E59E488929C0E84CB07BF6D92 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_sidewaysDir_mF1D87D1AFD314249E2BCC4F7FCD78B03752BD475 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_sidewaysDir_mA8A9A18917737810ED96C9B7E7BD8E4AA4FD3D64 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_force_m78BDB39182947B9A52424CBA33D2874D61A7232F (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_forwardSlip_mB96F4BFFC19D83031568E1A1BCF20B1AA61B91A4 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_sidewaysSlip_m66A9374E3A9F8C12EDD482BC1C370809DCD79C1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_center_Injected_m5F830D14320A88078EF056E5D83599CCD023D6F5 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_center_Injected_mB0BF5FBEBA1EE28BF7B8E788D1FC39EABACDEECC (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA (Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke_back(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_pinvoke_cleanup(WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com_back(const WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_ColliderException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", WheelHit_t15D44A463BF2792AD26161787B98CB5698519455____m_Collider_FieldInfo_var, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_ColliderException, NULL);
}
IL2CPP_EXTERN_C void WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshal_com_cleanup(WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* V_0 = NULL;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 2));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 3));
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_0 = __this->___m_Collider;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 4));
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* _returnValue;
	_returnValue = WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_collider_m661F87C36616207CF90615A16C102A6124EB15B2 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_collider_m661F87C36616207CF90615A16C102A6124EB15B2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_collider_m661F87C36616207CF90615A16C102A6124EB15B2_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 5));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 6));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 7));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 8));
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_0 = ___0_value;
		__this->___m_Collider = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Collider), (void*)L_0);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 9));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_collider_m661F87C36616207CF90615A16C102A6124EB15B2_AdjustorThunk (RuntimeObject* __this, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_collider_m661F87C36616207CF90615A16C102A6124EB15B2(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 10));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 11));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 12));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 13));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_Point;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 14));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_point_m6FE90840F9AC30F65E5E9DF4561BC4146D60E507 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_point_m6FE90840F9AC30F65E5E9DF4561BC4146D60E507_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_point_m6FE90840F9AC30F65E5E9DF4561BC4146D60E507_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 15));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 16));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 17));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 18));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_value;
		__this->___m_Point = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 19));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_point_m6FE90840F9AC30F65E5E9DF4561BC4146D60E507_AdjustorThunk (RuntimeObject* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_point_m6FE90840F9AC30F65E5E9DF4561BC4146D60E507(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 20));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 21));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 22));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 23));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_Normal;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 24));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_normal_m2A9BD0A7000CC5101129402A92AA2589C65F8454 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_normal_m2A9BD0A7000CC5101129402A92AA2589C65F8454_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_normal_m2A9BD0A7000CC5101129402A92AA2589C65F8454_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 25));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 26));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 27));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 28));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_value;
		__this->___m_Normal = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 29));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_normal_m2A9BD0A7000CC5101129402A92AA2589C65F8454_AdjustorThunk (RuntimeObject* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_normal_m2A9BD0A7000CC5101129402A92AA2589C65F8454(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_forwardDir_mEC17414DD098E3BB4080E356221BB5655E3E3100 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_forwardDir_mEC17414DD098E3BB4080E356221BB5655E3E3100_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_forwardDir_mEC17414DD098E3BB4080E356221BB5655E3E3100_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 30));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 31));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 32));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 33));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_ForwardDir;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 34));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_forwardDir_mEC17414DD098E3BB4080E356221BB5655E3E3100_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_forwardDir_mEC17414DD098E3BB4080E356221BB5655E3E3100(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_forwardDir_m0A02053AD079E33E59E488929C0E84CB07BF6D92 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_forwardDir_m0A02053AD079E33E59E488929C0E84CB07BF6D92_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_forwardDir_m0A02053AD079E33E59E488929C0E84CB07BF6D92_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 35));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 36));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 37));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 38));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_value;
		__this->___m_ForwardDir = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 39));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_forwardDir_m0A02053AD079E33E59E488929C0E84CB07BF6D92_AdjustorThunk (RuntimeObject* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_forwardDir_m0A02053AD079E33E59E488929C0E84CB07BF6D92(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_sidewaysDir_mF1D87D1AFD314249E2BCC4F7FCD78B03752BD475 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_sidewaysDir_mF1D87D1AFD314249E2BCC4F7FCD78B03752BD475_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_sidewaysDir_mF1D87D1AFD314249E2BCC4F7FCD78B03752BD475_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 40));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 41));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 42));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 43));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___m_SidewaysDir;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 44));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelHit_get_sidewaysDir_mF1D87D1AFD314249E2BCC4F7FCD78B03752BD475_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 _returnValue;
	_returnValue = WheelHit_get_sidewaysDir_mF1D87D1AFD314249E2BCC4F7FCD78B03752BD475(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_sidewaysDir_mA8A9A18917737810ED96C9B7E7BD8E4AA4FD3D64 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_sidewaysDir_mA8A9A18917737810ED96C9B7E7BD8E4AA4FD3D64_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_sidewaysDir_mA8A9A18917737810ED96C9B7E7BD8E4AA4FD3D64_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 45));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 46));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 47));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 48));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_value;
		__this->___m_SidewaysDir = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 49));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_sidewaysDir_mA8A9A18917737810ED96C9B7E7BD8E4AA4FD3D64_AdjustorThunk (RuntimeObject* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_sidewaysDir_mA8A9A18917737810ED96C9B7E7BD8E4AA4FD3D64(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 50));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 51));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 52));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 53));
		float L_0 = __this->___m_Force;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 54));
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_force_m78BDB39182947B9A52424CBA33D2874D61A7232F (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_force_m78BDB39182947B9A52424CBA33D2874D61A7232F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_force_m78BDB39182947B9A52424CBA33D2874D61A7232F_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 55));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 56));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 57));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 58));
		float L_0 = ___0_value;
		__this->___m_Force = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 59));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_force_m78BDB39182947B9A52424CBA33D2874D61A7232F_AdjustorThunk (RuntimeObject* __this, float ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_force_m78BDB39182947B9A52424CBA33D2874D61A7232F(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 60));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 61));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 62));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 63));
		float L_0 = __this->___m_ForwardSlip;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 64));
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_forwardSlip_mB96F4BFFC19D83031568E1A1BCF20B1AA61B91A4 (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_forwardSlip_mB96F4BFFC19D83031568E1A1BCF20B1AA61B91A4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_forwardSlip_mB96F4BFFC19D83031568E1A1BCF20B1AA61B91A4_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 65));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 66));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 67));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 68));
		float L_0 = ___0_value;
		__this->___m_ForwardSlip = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 69));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_forwardSlip_mB96F4BFFC19D83031568E1A1BCF20B1AA61B91A4_AdjustorThunk (RuntimeObject* __this, float ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_forwardSlip_mB96F4BFFC19D83031568E1A1BCF20B1AA61B91A4(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 70));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 71));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 72));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 73));
		float L_0 = __this->___m_SidewaysSlip;
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 74));
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	float _returnValue;
	_returnValue = WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelHit_set_sidewaysSlip_m66A9374E3A9F8C12EDD482BC1C370809DCD79C1E (WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* __this, float ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_set_sidewaysSlip_m66A9374E3A9F8C12EDD482BC1C370809DCD79C1E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelHit_t15D44A463BF2792AD26161787B98CB5698519455_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_PARAMS(methodExecutionContextParameters, (&___0_value));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelHit_set_sidewaysSlip_m66A9374E3A9F8C12EDD482BC1C370809DCD79C1E_RuntimeMethod_var, methodExecutionContextThis, methodExecutionContextParameters, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 75));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 76));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 77));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 78));
		float L_0 = ___0_value;
		__this->___m_SidewaysSlip = L_0;
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_VehiclesModule + 79));
		return;
	}
}
IL2CPP_EXTERN_C  void WheelHit_set_sidewaysSlip_m66A9374E3A9F8C12EDD482BC1C370809DCD79C1E_AdjustorThunk (RuntimeObject* __this, float ___0_value, const RuntimeMethod* method)
{
	WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*>(__this + _offset);
	WheelHit_set_sidewaysSlip_m66A9374E3A9F8C12EDD482BC1C370809DCD79C1E(_thisAdjusted, ___0_value, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WheelCollider_get_center_mE21895BF5F8C9C2523B9468B2C9DA88737A4E163 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_get_center_mE21895BF5F8C9C2523B9468B2C9DA88737A4E163_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_get_center_mE21895BF5F8C9C2523B9468B2C9DA88737A4E163_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_get_center_Injected_m5F830D14320A88078EF056E5D83599CCD023D6F5(__this, (&V_0), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_center_m734D85FB64BF40A15DB24954792FCD177D4148BB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_set_center_m734D85FB64BF40A15DB24954792FCD177D4148BB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_set_center_m734D85FB64BF40A15DB24954792FCD177D4148BB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_set_center_Injected_mB0BF5FBEBA1EE28BF7B8E788D1FC39EABACDEECC(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_radius()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_radius(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_suspensionDistance()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_suspensionDistance(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB(__this, (&V_0), NULL);
		JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_get_suspensionExpansionLimited_m63789B6183F4BE5952F897F591A0725791737626 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef bool (*WheelCollider_get_suspensionExpansionLimited_m63789B6183F4BE5952F897F591A0725791737626_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_suspensionExpansionLimited_m63789B6183F4BE5952F897F591A0725791737626_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_suspensionExpansionLimited_m63789B6183F4BE5952F897F591A0725791737626_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_suspensionExpansionLimited()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionExpansionLimited_m0CF4E265551DF168D9F6C450E7737CC63B94F76B (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_suspensionExpansionLimited_m0CF4E265551DF168D9F6C450E7737CC63B94F76B_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, bool);
	static WheelCollider_set_suspensionExpansionLimited_m0CF4E265551DF168D9F6C450E7737CC63B94F76B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_suspensionExpansionLimited_m0CF4E265551DF168D9F6C450E7737CC63B94F76B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_suspensionExpansionLimited(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_forceAppPointDistance_m1747E5D2D20CA6BE0C1A22086F64DC5BAC6FFC76 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_forceAppPointDistance_m1747E5D2D20CA6BE0C1A22086F64DC5BAC6FFC76_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_forceAppPointDistance_m1747E5D2D20CA6BE0C1A22086F64DC5BAC6FFC76_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_forceAppPointDistance_m1747E5D2D20CA6BE0C1A22086F64DC5BAC6FFC76_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_forceAppPointDistance()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_forceAppPointDistance(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_mass_mD45E3CDD818D42BE6FD1AFC9EB8B3993F36F2628 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_mass_mD45E3CDD818D42BE6FD1AFC9EB8B3993F36F2628_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_mass_mD45E3CDD818D42BE6FD1AFC9EB8B3993F36F2628_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_mass_mD45E3CDD818D42BE6FD1AFC9EB8B3993F36F2628_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_mass()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_mass(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_wheelDampingRate_m0BAF4121AC0CBC647F171E7EC7CE599D698733D0 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_wheelDampingRate_m0BAF4121AC0CBC647F171E7EC7CE599D698733D0_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_wheelDampingRate_m0BAF4121AC0CBC647F171E7EC7CE599D698733D0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_wheelDampingRate_m0BAF4121AC0CBC647F171E7EC7CE599D698733D0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_wheelDampingRate()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_wheelDampingRate(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2(__this, (&V_0), NULL);
		WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1(__this, (&V_0), NULL);
		WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 L_0 = V_0;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810 ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9(__this, (&___0_value), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_motorTorque()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_motorTorque(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_brakeTorque()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_brakeTorque(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_steerAngle()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_steerAngle(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef bool (*WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_isGrounded()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_rpm()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_sprungMass_m68CE93EA82BB593FEFE5E68AC4EBF4611B55336B (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_sprungMass_m68CE93EA82BB593FEFE5E68AC4EBF4611B55336B_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_sprungMass_m68CE93EA82BB593FEFE5E68AC4EBF4611B55336B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_sprungMass_m68CE93EA82BB593FEFE5E68AC4EBF4611B55336B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_sprungMass()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sprungMass_mD08590C595A5225CBA750CDD7E34E38BC981AD93 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_sprungMass_mD08590C595A5225CBA750CDD7E34E38BC981AD93_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_sprungMass_mD08590C595A5225CBA750CDD7E34E38BC981AD93_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_sprungMass_mD08590C595A5225CBA750CDD7E34E38BC981AD93_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_sprungMass(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WheelCollider_get_rotationSpeed_m4AF2BB0179449931F726EFB4494FF46C124351BF (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef float (*WheelCollider_get_rotationSpeed_m4AF2BB0179449931F726EFB4494FF46C124351BF_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_get_rotationSpeed_m4AF2BB0179449931F726EFB4494FF46C124351BF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_rotationSpeed_m4AF2BB0179449931F726EFB4494FF46C124351BF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_rotationSpeed()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_rotationSpeed_mF96EF49476E8C2BB92B7941DBD6FD11EB5697D5A (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_rotationSpeed_mF96EF49476E8C2BB92B7941DBD6FD11EB5697D5A_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float);
	static WheelCollider_set_rotationSpeed_mF96EF49476E8C2BB92B7941DBD6FD11EB5697D5A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_rotationSpeed_mF96EF49476E8C2BB92B7941DBD6FD11EB5697D5A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_rotationSpeed(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_ResetSprungMasses_m03C9446B194EC15CE5CC2EC7A3CF3950D8BDC327 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_ResetSprungMasses_m03C9446B194EC15CE5CC2EC7A3CF3950D8BDC327_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*);
	static WheelCollider_ResetSprungMasses_m03C9446B194EC15CE5CC2EC7A3CF3950D8BDC327_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_ResetSprungMasses_m03C9446B194EC15CE5CC2EC7A3CF3950D8BDC327_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::ResetSprungMasses()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_ConfigureVehicleSubsteps_mE2D86B3F981158E554EBD8F79F255F359EBD1488 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, float ___0_speedThreshold, int32_t ___1_stepsBelowThreshold, int32_t ___2_stepsAboveThreshold, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_ConfigureVehicleSubsteps_mE2D86B3F981158E554EBD8F79F255F359EBD1488_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, float, int32_t, int32_t);
	static WheelCollider_ConfigureVehicleSubsteps_mE2D86B3F981158E554EBD8F79F255F359EBD1488_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_ConfigureVehicleSubsteps_mE2D86B3F981158E554EBD8F79F255F359EBD1488_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::ConfigureVehicleSubsteps(System.Single,System.Int32,System.Int32)");
	_il2cpp_icall_func(__this, ___0_speedThreshold, ___1_stepsBelowThreshold, ___2_stepsAboveThreshold);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_pos, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* ___1_quat, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*);
	static WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::GetWorldPose(UnityEngine.Vector3&,UnityEngine.Quaternion&)");
	_il2cpp_icall_func(__this, ___0_pos, ___1_quat);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455* ___0_hit, const RuntimeMethod* method) 
{
	typedef bool (*WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelHit_t15D44A463BF2792AD26161787B98CB5698519455*);
	static WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::GetGroundHit(UnityEngine.WheelHit&)");
	bool icallRetVal = _il2cpp_icall_func(__this, ___0_hit);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider__ctor_mD44B2C97960274797DABD055BBEB18E664DAD906 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WheelCollider__ctor_mD44B2C97960274797DABD055BBEB18E664DAD906_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WheelCollider__ctor_mD44B2C97960274797DABD055BBEB18E664DAD906_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_center_Injected_m5F830D14320A88078EF056E5D83599CCD023D6F5 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_center_Injected_m5F830D14320A88078EF056E5D83599CCD023D6F5_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static WheelCollider_get_center_Injected_m5F830D14320A88078EF056E5D83599CCD023D6F5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_center_Injected_m5F830D14320A88078EF056E5D83599CCD023D6F5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_center_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_center_Injected_mB0BF5FBEBA1EE28BF7B8E788D1FC39EABACDEECC (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_center_Injected_mB0BF5FBEBA1EE28BF7B8E788D1FC39EABACDEECC_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*);
	static WheelCollider_set_center_Injected_mB0BF5FBEBA1EE28BF7B8E788D1FC39EABACDEECC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_center_Injected_mB0BF5FBEBA1EE28BF7B8E788D1FC39EABACDEECC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_center_Injected(UnityEngine.Vector3&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5*);
	static WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_suspensionSpring_Injected(UnityEngine.JointSpring&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, JointSpring_t5223C53C8742463D5615BE8E9772EC0EE2EBBBD5*);
	static WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_suspensionSpring_Injected(UnityEngine.JointSpring&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_ret, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::get_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_ret);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9_ftn) (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481*, WheelFrictionCurve_tB7103DCB44101BD02986974ED9D4EE6B880E8810*);
	static WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WheelCollider::set_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)");
	_il2cpp_icall_func(__this, ___0_value);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
