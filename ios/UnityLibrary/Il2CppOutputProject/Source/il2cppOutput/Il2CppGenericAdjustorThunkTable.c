﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif








IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END




IL2CPP_EXTERN_C void U3CRunWithTimeoutWorkerU3Ed__241_1_MoveNext_m5611BDCED6E1FE6014D81219434F3A90D681FF0E_AdjustorThunk ();
IL2CPP_EXTERN_C void U3CRunWithTimeoutWorkerU3Ed__241_1_SetStateMachine_m69A9C0162383431ABA5421204CD78BFB43A16761_AdjustorThunk ();
IL2CPP_EXTERN_C void U3CWaitForCompletionU3Ed__15_MoveNext_m0627E58E33A26A6238717A4FE1C333BDDC45B39D_AdjustorThunk ();
IL2CPP_EXTERN_C void U3CWaitForCompletionU3Ed__15_SetStateMachine_m193015C8A9A30959F8709F36131ED5F6119D8B2F_AdjustorThunk ();
IL2CPP_EXTERN_C void AllKeyFramesStruct_1__ctor_m064E86758535987228E5742AB227195D5E1EDAC2_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_Add_mDD8FDB2E901CE9DF375C393C6EB1E1A94D68AC10_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_GetActivePropertiesForElement_mA378BCE8A10910B60FD3417B89FA48871613C86D_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_IndexOf_m39B27A0CD7E79387931C2E717D46F83956674874_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_LocalInit_mB05E91215A20813B3CEE5F24136B0E4575497BBB_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_Remove_mE3FF1BDFE9FAE731CB39755EA7EFCD334C81FC0E_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_RemoveAll_m5B6C0383FC4BD596AD65C8F509A928F84C61A69D_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_RemoveAll_m6E80B23A0CC786FD422734760343379434C6B818_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_Replace_mFEA4D840D4A70B11F713A987AC677F5CDEED3DE2_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_get_capacity_m02CE1174F074197B274146A1558994F98A1E4BE0_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationDataSet_2_set_capacity_m472F441C6D753CC3C3B04E0A0A6A27F8F3EB2C8F_AdjustorThunk ();
IL2CPP_EXTERN_C void Array32768_1_ElementAt_m82701E882669CA0FE1E0D5144F2FC0AF3AE96509_AdjustorThunk ();
IL2CPP_EXTERN_C void Array32768_1_get_Length_m0D08A19FC0206C255A5F4D7C4DB9CBF4CA4BE46A_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1__ctor_m1E5BCB2E003E602A6B469C66A2C3CD1ECDC7E143_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_ToArray_mB9A5257137C2C2A30D57A230D2391DED66DE6C35_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_UncheckedAdd_m264C61076357A116E3F82CACF14EBF9D18CD7671_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_Add_m1AC3F4BDD806CECCB6C29AD0253266FDF39F0065_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_EnsureCapacity_m6BA8EE4C3649B8CFBFA4E193BE6929CBD6F682FF_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_UncheckedAdd_m75F6AD906ED1EE176D2DDDD367B1F6DEC97C697E_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_get_Capacity_m503F745B099A44EB7E468555D74CF17E43C5BCD1_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_get_Count_m587CBF5DB2467A88A5DA669AC70AF8422765B660_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayBuilder_1_get_Item_mA86DA9F82A4B5149814D1C0B1F62557F92757407_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_BlockIndexOfElement_mDF48A76387683347BEF0BF210E94CF3D45B44A88_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_Clear_m7783304A5FA845DC3E8C4AA9E2E1EF3409A203FB_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_Dispose_m43AFAA25B3A81D3D03819B426149571B5F3FD13E_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_LockfreeAdd_m1CF92F5C153F9C0F40F9A73874E88BA431D39A91_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_RemoveAtSwapBack_mA2F7915A9CB86DFA61CCB826D66765CC137A2398_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_Rewind_m01A338B49AE90835038F585E2C60E676A1641B29_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_TrimExcess_m194AB512AA77539A26BC45263B6B9E7FA8A57E44_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_get_BlockMask_mFA7725CE0B6B1544940BBC60055CA73AC1A93137_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_get_BlockSizeInBytes_mB60D7E599E55B623ED557696F63C1876AC82FAF9_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_get_BlockSizeInElements_m5F95A30F1370257157354057B9F4F39B07236990_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_get_Item_mF3A73278BF4C9E08279BBD4B483CE5FB89DCCB9D_AdjustorThunk ();
IL2CPP_EXTERN_C void ArrayOfArrays_1_get_Length_m108701CB27FAF60BA83B76435904C3310B7D6953_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1__ctor_mD451D40E98D059A4FAC27FDB626B23D1ED00E1B5_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1__ctor_m3A1337B17EA471D2D1538CB43ADB3B91267F978F_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_CopyTo_mDF1836BAF82385762BD2AD0F2FEBF2B9A1078DC1_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_Equals_m56E653D8CF357BAF5CCAE99EAADF890F11E23963_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_Equals_m47C8DD34DCC5A719882AFA64CA689F2245FD0748_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_GetEnumerator_mC6A812AD66B70E1CABA6B8F9734E97583E5A615A_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_GetHashCode_mD8CE9832FC50FCB215D2D58996761AD89434257A_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Add_m999A2F57CED17EF50C3CAE4109B556F69AB53950_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Clear_m521BD5A96D0269A31C7E1DE2C3FDC94912844ABA_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Contains_m7AA613C65175728F164ED7425E4D3DD455B6E034_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Remove_m71971789EFA719C172557880FC16130505B1053D_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_get_IsReadOnly_m3191B5D6CCE2AFBEBBC2D8228422F3C8ED6CD548_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mB561C2A02ACC0A836BBB742A4999D2FD3C237DE4_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_IListU3CTU3E_IndexOf_mD2BF33101E37C8A326016EC64A2953785E4CFE03_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_IListU3CTU3E_Insert_m5CCD11CD54587DD8FC897FDAFBD36AF0814672C8_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_IListU3CTU3E_RemoveAt_m291C118005848F3A89144B11AF496B20D4E20284_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_IListU3CTU3E_get_Item_m4F262D3148E3C9620F09B1EB0D37263FE444690D_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_IListU3CTU3E_set_Item_mAB40F2A9A0B64D87ECF766A8543F78518333DD29_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_Generic_IReadOnlyListU3CTU3E_get_Item_mB7BC1E3625F87D7FC1BDD09FB734CC9FB6EB6F97_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_System_Collections_IEnumerable_GetEnumerator_mB362F70B4869017E3811F46DBCCB1822E4611617_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_ThrowInvalidOperationIfDefault_m22F7F852A7B6751E4D9670504414A579241554A8_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_get_Array_mE4A35DFD81ABF447350B9A05C0F4BF0248A3CFF2_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_get_Count_mEF96248898C14F3687A5C72B920F841701E4E13C_AdjustorThunk ();
IL2CPP_EXTERN_C void ArraySegment_1_get_Offset_m4FAFF061AB36BF278BA8DC37B6D13718A06301E6_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_1_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m0B984271E33D944D547FB29917733016C411E5EB_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_1_Start_TisIl2CppFullySharedGenericAny_m81177143E3D9118AF316E4C8E5D2AB2BF16C4E80_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_1_SetException_m25948BB842FBF253D89FE8399CCB2325B491EE34_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_1_SetResult_mC5A4FB0746878FC882C792D8BCAF5277E1F24778_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_1_SetResult_m86D5196DCF794B39FEB8D92C2BDB3C20421249F4_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_1_SetStateMachine_m7EF377C3A25FB4388DAF14ECAFC18E1C9F3EEA4A_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_1_get_Task_m90B072626CA4BF0F567616D4A035739B97F46D8B_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskMethodBuilder_1_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mB581027AD2DAA9354D87721008929F4DDFAC471E_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskMethodBuilder_1_Start_TisIl2CppFullySharedGenericAny_mA4B2E5CD3C0F6E8CB3612B0BE99533FC821970FC_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskMethodBuilder_1_SetException_m1812AF0E769F5A0E532DDEF8FE0921CA78F12A7D_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskMethodBuilder_1_SetResult_mF7DA80E1E8E4F9D51929B6BC5DC7B7B11F889339_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskMethodBuilder_1_get_Task_m8B0444E14A015F72613FA9939E443CD7F0FCBB76_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncValueTaskMethodBuilder_1_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m268E5FBC3DCEF0470D41F9C2584E6B13F9BD7B8D_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncValueTaskMethodBuilder_1_Start_TisIl2CppFullySharedGenericAny_m85A9FFB7131400A58050438B2B0A7CD7F043738A_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncValueTaskMethodBuilder_1_SetException_m4FEA96E8C41844A34A2339C9D0B905E7B5007CD5_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncValueTaskMethodBuilder_1_SetResult_mC029EDB24659DB11EA83AAFF46BD5F9400D5C9BD_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncValueTaskMethodBuilder_1_SetStateMachine_m3903212517D60016C9BCED8BA377C3F1CD71A110_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncValueTaskMethodBuilder_1_get_Task_mDBD70CC608F07003B473CF5F8C8A6FE86583C229_AdjustorThunk ();
IL2CPP_EXTERN_C void Awaiter__ctor_m75F3F14568F9463778CE7429585D539E87F226F3_AdjustorThunk ();
IL2CPP_EXTERN_C void Awaiter_GetResult_mFF0CF33768E54ACB5D4D4F47A4A6FE3DB29EE6D8_AdjustorThunk ();
IL2CPP_EXTERN_C void Awaiter_SourceOnCompleted_m7BB8B4D6B70376483D828F7D3BEE03C2C33602E0_AdjustorThunk ();
IL2CPP_EXTERN_C void Awaiter_UnsafeOnCompleted_m9BA083B1ED6E4385066B9A863135A587D4260E32_AdjustorThunk ();
IL2CPP_EXTERN_C void Awaiter_get_IsCompleted_m22B365903CC1BE835CA1126FE3BD3E71156DDFA8_AdjustorThunk ();
IL2CPP_EXTERN_C void BatchQueryJob_2__ctor_m52B5B1BDEE537675680BDEFA6BF9D38FC31DA09B_AdjustorThunk ();
IL2CPP_EXTERN_C void Buffer_1__ctor_m55137EEF7AF9E0883A438776A066DA5520FFDE49_AdjustorThunk ();
IL2CPP_EXTERN_C void Buffer_1_ToArray_mFA9AEA7D664154CB0695FD1ED98B3C9539146194_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredTaskAwaitable_1__ctor_mCF681CB4825E085E3ED42B9E990609C36F282536_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredTaskAwaitable_1_GetAwaiter_mA5D8A0E225B9D580F1FC5216C47A0B828B033390_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredTaskAwaiter__ctor_m5B53A410AE8900B3D565ED7C7FE9DAB92B2ECEC9_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredTaskAwaiter_GetResult_m14D32632322F465B16F5EB858BFE5886B7217701_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredTaskAwaiter_UnsafeOnCompleted_m2EFFC8EFEDD85479876580D8FDEF045B2DCA2D66_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredTaskAwaiter_get_IsCompleted_mAEF99891A1B576254827D55C54C6E0E1787AA7EF_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredValueTaskAwaitable_1__ctor_m50F71CECBEA21581E8170F4CDFE15E0182FE41D0_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredValueTaskAwaitable_1_GetAwaiter_m44A568FC09F949B84CD6F0F09E907A2A04E2EF46_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredValueTaskAwaiter__ctor_m3DE02ADEF13DE18D55B4E303EBB2BA001A30DE09_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredValueTaskAwaiter_GetResult_mCF267B69492EBE1F642327D1A858EF350DEBC19E_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredValueTaskAwaiter_UnsafeOnCompleted_m562052388D99EC0C34D5412AE114E2DF90358BFA_AdjustorThunk ();
IL2CPP_EXTERN_C void ConfiguredValueTaskAwaiter_get_IsCompleted_m900B802370ACB3E11F808026A4BEE1183E91507A_AdjustorThunk ();
IL2CPP_EXTERN_C void ContentHeightCacheInfo__ctor_mF2BB6598B1FEDCE079C61DBE32A2CCA1000648F2_AdjustorThunk ();
IL2CPP_EXTERN_C void CustomStyleProperty_1__ctor_m51F05AE8977374556253B23F63CF84C76FB546AA_AdjustorThunk ();
IL2CPP_EXTERN_C void CustomStyleProperty_1_Equals_m7BBDFACAA54A231C503EA95081A02CB77E8F2F02_AdjustorThunk ();
IL2CPP_EXTERN_C void CustomStyleProperty_1_Equals_m23B71AC52884B6CBB0E91A337DC513D835679203_AdjustorThunk ();
IL2CPP_EXTERN_C void CustomStyleProperty_1_GetHashCode_m728F3D620894896FF88E08CECC1BC9BC2E9BDAA0_AdjustorThunk ();
IL2CPP_EXTERN_C void CustomStyleProperty_1_get_name_m0854CB7E93086BB018F00956EE37E700057D1DEB_AdjustorThunk ();
IL2CPP_EXTERN_C void CustomStyleProperty_1_set_name_m799831D93960394105AEC1C47B11B81C08A46861_AdjustorThunk ();
IL2CPP_EXTERN_C void DictionaryEnumerator_2__ctor_mB2404C7ABD77FE384388890F4B7EDB74070EEE25_AdjustorThunk ();
IL2CPP_EXTERN_C void DictionaryEnumerator_2_MoveNext_mD8A7D1B941AEB7ADA1091F3ABA9FEADC16A1A0C8_AdjustorThunk ();
IL2CPP_EXTERN_C void DictionaryEnumerator_2_Reset_mF8330A60A65FFFED2A9EA40BC9E3FE926CC8AEED_AdjustorThunk ();
IL2CPP_EXTERN_C void DictionaryEnumerator_2_get_Current_mB2264B3813E71A58982A4A62939586C97BC9AFA5_AdjustorThunk ();
IL2CPP_EXTERN_C void DictionaryEnumerator_2_get_Entry_mA9FCA0DD088F8A2D58E769612B91FE7EDD45D4C2_AdjustorThunk ();
IL2CPP_EXTERN_C void DictionaryEnumerator_2_get_Key_mB1B3E656D88C36BB892A57A4858B92C75467A8F1_AdjustorThunk ();
IL2CPP_EXTERN_C void DictionaryEnumerator_2_get_Value_m1833BB99169E7A10A1B94B82ADD64E9F2ED64933_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerable__ctor_m9088B0F646E8EF80BCDA3B11D884763A9A817E61_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerable_System_Collections_Generic_IEnumerableU3CUnity_Properties_IPropertyU3CTDictionaryU3EU3E_GetEnumerator_m3A72329B599B83EC044C68832928DC458ACC6501_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerable_System_Collections_IEnumerable_GetEnumerator_m22D9023302E21EDDA6EDAB5AB7C40055EF33EBF4_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mD912767F3C3AB1A573EE48AE27D5952C2B610660_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m80DC6195B05476661E3CD158706F318E49FDD6CC_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_mCF34F788777D451B9DE38CF61C0A084532530E85_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Reset_m27A83D13EBEF784BDDC1B8DA1F5B6B87B0CE44DE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mC57BA17257480558B7FFF0DC4035F61328A85905_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m3E97569F59CC48BA1B86206FE02174721868B215_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m54EAA0B597A1197DBC10AF73A3C423B5F8046A75_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m97F29D6F80B478A97A6DA81F665E7DA693E17E64_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_mC9099E56E01AB33169AB4EE888BD9A2B752961DE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Reset_mB15834348E10F7612876D2AE774493AE7AF604B2_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m81767706F1E3B41EAB92F21B1669693FFE92ACCB_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_mF9A198DACCE8ED0DC6FD097825D7A0F82D149DBA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mDE93458BF02169A52FC3A449C9F0B500A0B25958_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m0BBB67CF7366B5A989FBB12AA2293CD18853C98D_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m0DEF4AFE20672D3425DCFB0B4B7EBBAA7B69F595_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Reset_m7DFE6628F82F51C868E30AF059855DEBF7ABE53A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mEB8088957B367DEEE33935D4174B19579BF14B58_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_mD5F9A8DA4A689D3BE78549843241C5C6D4936D84_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m7E124C7D7F367A3EC2AE25CA5F4D1284F5FA2E43_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_mCB9B6D15D244D49204D2A903402C1D5011CC2830_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m9589C320A0B345A9190284C38FCB97F2015861DA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Reset_m43C21832D8660DF1F98FA2733CE9D752ACB020D5_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m9E9FBC7AD882EDFB5FDCF6024ECA5DA4B2DD24DA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m44CEFC7935FF87861A589ACDB1024033E52A123E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m1E2A074F9FBFEF05AD1CA9A1E05FA4CBBD85FA5E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m71A32736176290409FABCA7026F119BB668173B7_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m0FFB94B57BE7FB5AB94F53DBC90E6F067BC5E2A6_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m5DD5A0CAD9CA8A1FEACC3479F140A987AF8BADC2_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m2C2D45C464CAD806710B057958AC5DFED13D059F_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m290C2CCA066988F3CF229AA769C67BE5E36AB510_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m62B32E8D2F70569824269DDC82F384BDBC8662F5_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m915D35B9BB2A77C5396F4FE1A1C53F1C93B8E337_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m1756F06B6BC460B8EEB4522B562E097F37D54C59_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m27D5470C040D50CC0200DDAB3C93DF3C4CA755B3_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m7D04FEF0F3E60E6E691D979806634E034498134E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m51282E47C90E55BF2D96A0D329869ABC122A85A0_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m0F98614729A5017594BCB5D1D847C1D319913311_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m37AB9FF23CB9A754FCD2CA40D0B482F61F9D6531_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m25395D42D3EE1E89A0B024E2FAF823E4A3FEF4D1_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m4FB57970CC4FBEE295E533DE12F0B21FF7E13AAA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_mEE0F6DC54C3238FF39CDFF5675B11CD97C544897_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m0F26F0C362DA633022A66F6C342673D39AD4622B_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Runtime_Serialization_IDeserializationCallback_OnDeserialization_m3675ECA18E1A14A4318723D0B93024692A40759B_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Runtime_Serialization_ISerializable_GetObjectData_m200D1982786B356E5B42E63B71C05AC7B3EA337E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m5BA2E5A6E9016D327B7C3067D3D2B2016D37DFDA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m5C66A91DC68887BE203985B56EF2F74222E9AF50_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_mFE1EBE6F6425283FEAEAE7C79D02CDE4F9D367E8_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m8D8E5E878AF0A88A535AB1AB5BA4F23E151A678A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNextRare_mF1A6D7920FDC3C83A7DA1F838C2FC51D716637D9_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_mFA2701BBDA14ED6CC3113A32ED0E8B80DAD58A84_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m3FCE22D15C040EE9F8614C711F786205BA6AAFB6_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m8B42D4B2DE853B9D11B997120CD0228D4780E394_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m6C59AA282C548B36E982B84CACC3A8E4AA904775_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_mDE5B605B7794A192CC35F12013300AE26EB8FE5F_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m2F9CDD8406C6495182E05B55A00C3D0C9620CA67_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNextRare_mA2A9C4EFAA8131F5D97D9B8626FDEDF343E93488_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m15625F750730D127B128E2654A2B59982DEAB9C9_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mA8280DCF9D59E671AB204C71415939B6D7B4211E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m05879F7740A935DB74AE5672E2B537349369A3C1_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m9788E04B31AAEC644383BF9A7D0FD66F4D89F6F2_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mEA52ADA4F9D63275758AC0D426BA0CBDDAD6474E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mB5DAC8151C7ABCC2E039D679CC59C09170005D6A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m1938093C428E4EDE3B40A07184993FA35F74AB42_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_mB0957ADFA01BC52170FB018BD2AD59C2E163A22F_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Reset_mDD80A4C2F5EFC81C22B7F74CACFF9D0921BA0A3C_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mBB836D4E68A5F554A9A7C377FFBD67DA4253EA7D_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_mA7D722D310859E84FC0C9A2F8DBCAF8BA78F1D54_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_set_Current_m1BB3A6D5580E7C6A864831EED00A4E1B8E81B1B5_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m8DA499B2030A9C843BE8FCAEFDFD4CD4EB5BA0BB_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_mA25DFE205338DF12128791E990699B49B335B759_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_mA6D24403558EA8BFEE5028D75AC049EC8677F637_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m5E20159EC017B0F62631E89E5784B2D532E2D314_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m01A32A0663137A48FD334AD3F7BF1A172FEBA204_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_ThrowEnumerationNotStartedOrEnded_mA8F87F65FFD80CCBB3A060C159463BB5B5603E93_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m2DDD9E2B36A595D40708EBE84125C065CCF6F11D_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m37A6313A32B9FC5F6EB5D51D8F684495F740383E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mFAF8E13AE32872378C00447B1AFCD9858441D07C_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m1B66D14092A623BE5F8A22939E7A7A57B47232BE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Initialize_m30C4758A1218C7294ED5F31CA799ED27A625C242_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_mE700C9CC529ECE454379FBD8FC409DA45E1B03AC_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Reset_mBD98F1F40AC3C51AB5FBDD2158DAAFCCF590EA0E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m7C75161AC2EE66A22E01977A98745F72EC2B66A0_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m1AE588BAA26DC5410664D87CC3DCD2EFF73CF686_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Runtime_Serialization_IDeserializationCallback_OnDeserialization_mD467A31CC142C287C668941326845B60908BD80D_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Runtime_Serialization_ISerializable_GetObjectData_mC455BEBC09DF1E94D9D8367E3A9D51F38A9DF1BF_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_mB3DADD6848C84F194FD9CA2F6A8D5A5BB83E2377_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_NotStartedOrEnded_m274BAD4868C654F0095AEAD551E52954F8DD0E83_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mD65EF492693E70B41695A031A49F72C7EFA82FCE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m475AD9FE055280B2B1CF43E93F9A2340C3EAE75C_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m3347052D874561F1E1857019C4F418A27B125353_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m796D8D2C90C2436A06B08E30008143FCF74E8EC9_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mC2EE4A74701F665A795443A0814F1CF22BC12560_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_ThrowEnumerationNotStartedOrEnded_mDFA786E51878E61AE16B8C27D512FD3407BA6AFA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m748B0758792F434FAB29656C2F5CF6EEB4481D27_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m7877FBC3233ABBF3242DBB8767B21F547B50A31A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_DisposeAsync_mB5E8AF8D2416E409E0EF0F189AD0FD01145A5FF3_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNextAsync_m7441BF52E44BDD79E4E7CAE1F9C0FD51F3BB3E72_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m899BAFFEB4702228080255387361D95ED3AA0647_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m9ED6D04154B0287F36E8E29C5A49F8113F8D3ED1_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m3D89F01AE65EC60062FFB578C0E771C098EF2CB7_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m97783F73CDB1D0083A2F7D26A51847BF0843ADEA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Entry_m66120A939C97C89BAFF013B3AE7FEF9BB6070F6E_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Key_mB49F4C26DC633814F50A1744D2F43332CEF8914C_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Value_m2B58ED135DE2504A9786BE2A06708190C7F9F7ED_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_mF8A12FD9DD4421A2C1B75FBE470014C6D1E0296A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m1B03B4376AEFE278EBF80B22815E42BD5FC2EBBF_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m26AF82C275C82180BB7F23C7E408BC1FEB9A38EE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mA347E7F9A4E89648A28432A4CEA2FABA20C4DEEE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m7C0F723CD15AC0F1B5F6D2937235CFD7306329E5_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_mAD0690F634E79D1C9896559DE69D5E2815850BD7_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Reset_m668460C937EA6D80F3C20D44C7D1F4D02511C0F3_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Entry_mEDF07B3B182C171328B6FD434AB0C51C4CF640FA_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Key_m5F7C47A383C19C80BDBE795F07CB0132AC37BDD0_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Value_m89734560ACBA07CB4AEDF0CC514294E267B5EE61_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m4FAC386B04F3311DA126417AE7A5F70AA8B9FB36_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m237C220E130919E063350A9141BB13478DC51806_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m66C839BFE9F105BCBF859FC59834ABC4D2842EDE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_NotStartedOrEnded_mA33F0A7BB7608FDC42CBD775E9ABFE8348234EC0_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m74E4507FF37BDC183623DA6AEE98F82E112E9F2F_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m1F5DB7D225E10E445137815B300A7C836CA06E3D_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m1092908331BD584DC099DBC0DE61ABDA7267F2B6_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Entry_m7EB9CA7BCC59643D29941E0B384A3A5A76CDCA3C_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Key_m311B50B02172FC06CF666E2D5FEF0DDCD5786E99_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IDictionaryEnumerator_get_Value_m6F0091ADAB32E0D0DC7497CC2B04DEF08F20A73A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m2B4442329A7840135B8F78692CD5308700437D8A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_m4D874FB38E08A2D26498947E4FB8A36CA469082B_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m6AF552684A6136E5163CC3C09360202C85160FBC_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mF61ACC0911DFBA7D4E9F1BBC6A95781A23763CD6_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_m3E0B734E98D35978EEE706FCEC2FE08FEA74FD35_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m21C503A713FDCA406492E2BE960D85320752E615_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_mCD1674F679047ABFB40A195BABF666D7CC837AC3_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mAF47EBACCABC3D69CA80CAAFDA4A6B19CB27F7A7_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m2D9E3398C2A3349E3F0F189A38E881D11DE5AA15_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m53208E6BE0322FB9D8336F1303586341867496CE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_mB05777B182DD6D8C5ED21811C2CA95405D2743F8_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m297C3CEEAAC8D29D8F7C93E0801CD1D362D1C66F_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m6F93BBC247A6914CB4E45D9FA5345930C3B507B3_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mB728CD9238796C981668909E33EC186CC6FE76C5_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m77681472C32B9ABB97AE602C0D4C365A0A8E8ACE_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_m8FD77EB53030329DF2E29E08BEE0AE4EA8D822E7_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_mF012D679A710A034FE596935A8B2C8DA7E92A837_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_m89F44DFC7BC66A1BA4FD6C09935A1F492738908F_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_mC56550C90B1621E19229741C78EC55C6A5D0BEA7_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mD5CAB46FA0627A7646E826A66B111A7B0EDD0F4A_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m18B14000CB7077E7AD45EDFAD3CF8B8DEC307243_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator__ctor_mA52845CC6D3DF6FBC4065D8496C74B2162B90A35_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_Dispose_mF4A570E2314AA029BCC1C32B2C0E41673909A3F8_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_MoveNext_mAA12AA9E1229E36CEF390B7D1A9EA48DC5059894_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_Reset_m5CD8B11FC178B5E94103DDB1FAB65FCE08ABCB88_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_System_Collections_IEnumerator_get_Current_mFBD205409FD3FDEACB92F262B54A515BFEE70791_AdjustorThunk ();
IL2CPP_EXTERN_C void Enumerator_get_Current_m5427AE558435E25DBDA61E6236092F6D3B0ADEF4_AdjustorThunk ();
IL2CPP_EXTERN_C void ExcludeContext_1__ctor_m8C7B1D32CD98A0C7A4E04E22C4433FBB21D3423E_AdjustorThunk ();
IL2CPP_EXTERN_C void ExcludeContext_1_get_Property_m6A19D562A0F7F6FFB368440CC075DB91A5325251_AdjustorThunk ();
IL2CPP_EXTERN_C void ExcludeContext_2__ctor_m6C3CAB2F1ABAF02AB478E955EE91483932857CA9_AdjustorThunk ();
IL2CPP_EXTERN_C void ExcludeContext_2_get_Property_m5C4ECE37827CAD13983BF7A1DDC3C19E5932E5B2_AdjustorThunk ();
IL2CPP_EXTERN_C void ExposedReference_1_Resolve_mFA2938DF9E0A74422B71F573F5CC0A8B1CA54647_AdjustorThunk ();
IL2CPP_EXTERN_C void FieldDescription__ctor_m6FA19D01FA63FA6A6CEA6EBC2E43C1E03EB57DE7_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_CompareTo_mAF36C346FD4AE2600025889DD02B9D44284C8C7F_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_CompareTo_m4293303C3DF5B8C98D0888639C72E6B9D8E259D8_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_CompareTo_m05620456947045FE8ECA0586B33EAA6530CB39A4_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_CompareTo_m5E0B1FA6EA34DD296BC43FB162441A68065B02BC_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_CompareTo_mBF678DF621A5F04CD8AA0BAF4725317C07A2DD25_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_Equals_m4145FA97673D2191E4ECF2CBF2792FF13A84D752_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_Equals_m291560352A3884460A7CA6404D76607160887918_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_Equals_m53B970512EAC6DCCD5690CB7C5586846D2675ABF_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_Equals_mE4BF76213B101088B999DBE426C9BCF856A8B7D3_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_Equals_m90466CBC2F0AA1F1F4B2F831952937983B348C52_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_Equals_m6D35114F890EE34855BEF854312B1A7CAFCB3BB9_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_GetHashCode_m4565D17CC76AE50032A2E95C45D66C493D7FEDB4_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mC866DD1936E0DF3EB7D8CE147BE7358CD28E0536_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_System_Collections_IEnumerable_GetEnumerator_m8970F4B5B7B1C558072C3391E983BD03F754ACC1_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_ToArray_mE453C883A4FB8F6D9FF84AD9139A7AE4E893526C_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_get_Buffer_mD43014DEC5833FAF9392FC3FCBE3E9B54C6C4156_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_get_Length_m927F92EC07E24F20A7314C091F5C5FACA9A0C16B_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_get_LengthInBytes_mEAD7F77987799E0D336DE5ABCEAA71C533E5801A_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_get_buffer_m3FA0F1453D72938135D3074318E14AD2A1B32DA4_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList128Bytes_1_get_length_m01D23D934D19D0EEDCA425F421ABA56B3DA58F3C_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_CompareTo_m423D7F263E7F7BFEB06CB8C817D534391B465C6F_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_CompareTo_mC56C07D51B7BB71D545FFCF9199FDE8933806CD7_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_CompareTo_mB123ED3E04C501E2B0FE9B27D3BBBC535D49D4DF_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_CompareTo_m720DCC8408299084D8CE9218FDFA9F2F8A29EA03_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_CompareTo_mCCABF337C720F84AC57728059B35683CFD6BC749_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_Equals_m5CDBB6208409BC6F1BD4619D76E9788EE0CB3AC2_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_Equals_m1F86304BBD4CB7F0C1F968F6736611957F7D85A8_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_Equals_m9532613448A35774D4D150FEE96E457BF174E8DB_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_Equals_m1FFF57EFEB15ECE1B694E007239CFE7EA324F31B_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_Equals_m5BFDD3FBB8CF240B35DDE0DA813AD2A500A28485_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_Equals_m276811E7FF7931B4320FC8B7C8BAFAB88E9D96E9_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_GetHashCode_m74D739320BEE925AC8446E58E20F59CF21561C44_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m7012A8E1D82BBDEB998E88A124C9DF8C9E2A765D_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_System_Collections_IEnumerable_GetEnumerator_mB22190AE9299BD81948F66DE4F37972ACABC5E0C_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_ToArray_m480123C27D084EABC25806107091A5FCCE272DF7_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_get_Buffer_mFB1AAFA86AB113AC3BEF550971AA4DA182A64606_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_get_Length_m69BE1E6D357498954A3C7A28D7F00CAAA79F80DE_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_get_LengthInBytes_m73F38D714876A4B0D7DC0AC8834B6ED0745D585A_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_get_buffer_m139B05C9B2F45AA86EBFBD4F0EC8803848137F66_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList32Bytes_1_get_length_mD16FF5C8CCA29B2A58BB98073E6729F0E2BBE907_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_CompareTo_m0DF14F0642F8E54D86B4A3310F015F0E22C38129_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_CompareTo_mE92BCDD9CAC8EE2EF5F1D0C18BF4BA12B32E6ECB_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_CompareTo_m7A1B6F3399E24A26B0E570CF3696434FE361C6A2_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_CompareTo_m35A1B10BBC2AC4E720C46C63E128D2DB40BA5BD6_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_CompareTo_m46B9A52B5684F574DB0E9879DBBB39713D11B944_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_Equals_m203CDC64FDD866292DB60D5FD259915539E9AD17_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_Equals_m83E576D45D844460AC0E872ADE1805ED90AFDFC7_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_Equals_mF82C3CC55D8D2851DDA383EC658E9CB30038C55E_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_Equals_m790B4671C26193AC8908C5FFBDEEDCA00151B8F6_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_Equals_m25B7C6C6F180158EF2FBD64C19A2B8EA0995E0B6_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_Equals_m21ECB8AD6E1B0BA1D384FC95254966920CBD16E8_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_GetHashCode_m999C80B2EF7B4D1E4DAAC97BB1FD2B88AD2DCEA8_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m781FA48EA39C7C02190DBB57CEEEFAE618A7C03D_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_System_Collections_IEnumerable_GetEnumerator_m9591E6949DD5A39AA97614FC75AD51A98CA605C2_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_ToArray_m2DE5CC6C5841C3C033F952A783853B56823F7F73_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_get_Buffer_m76BCD608C0436D7647CC2BD2D197629A9B1C6DC3_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_get_Item_m970363422CEE1B790AFBEB5934459409F61AD136_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_get_Length_m7F849A6DE2F1D919926FD832199A91C25A15CF83_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_get_LengthInBytes_m946CB445CCA697A0A884BD8A95986A378A3DA087_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_get_buffer_mC6FF3A165067CDB3016B89246011CB5D7B34B71D_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_get_length_m0437506FA2283836CDDC3A46CC5F706D1819B05A_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList4096Bytes_1_set_Item_m8FBFF44654DE0B13D3B6968206668554CCE665D2_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_CompareTo_m495B66EAA67EBC91176D18B39569066B905DF19D_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_CompareTo_mDCAD639C8842A821B3550E6D2ED6E94FC2CEA40C_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_CompareTo_m3F70E2A1F880757CA1BC7C681898C42459486DC7_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_CompareTo_m806888F07579CEB7590777299CD5B312ACC408B6_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_CompareTo_mF7FFCD39BB662F4B7D659AC5904864082B4990F4_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_Equals_m7A8FE88F8493F3D3BC94A0B3D1BADDAE67F9457D_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_Equals_m9ACFF0F110192C087A0872039AC8AAA736756288_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_Equals_m710DF325B8692C020E48D7B795413917A62DA074_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_Equals_m4474646DE461531A45836110FF120257A175DACB_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_Equals_m40B7CB0CEDC451DBF6AA9677B73A68298EFD50F1_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_Equals_mCB08CCF4DB6E7C3B359F1949D89003C9324124A2_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_GetHashCode_mC8DD2C89A69D94BFD8F02AAC003C87D6EB878D96_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m35CE6DC32FA8EE026258C5DF9CBD8A9DA7CB09CC_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_System_Collections_IEnumerable_GetEnumerator_m487E7F515D84F283DEAD75EDC04306EDBF71D0FA_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_ToArray_mEBC6A9D83F355E772A9BDC2A85E819C705E78D5D_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_get_Buffer_mB8203817FDA4AEEF7E986FEF9A92B92F48456DA4_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_get_Length_m39F0C6ECC0732E96B3F8C0A2553A71E975011ABF_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_get_LengthInBytes_m3D7D1A03155F1BE615645D5C5E570DE054738D8C_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_get_buffer_m513212BD1E1DB094D6923FAD0B5D92DF8FBAC102_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList512Bytes_1_get_length_mB15FCBDD135D7C01311794E83AF0B6436D2D5464_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_CompareTo_m1C8BB1A8B5A249FEC1D43343DA55CDF35F9CBCBC_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_CompareTo_m96A1729FE2EE27FF7DA19BBA8E962E170CC01197_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_CompareTo_m60410F1612BB2C774B5914E174315F47F2671A62_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_CompareTo_m6FFCA493F022922BC480E8886FBD9D5516D2BF1B_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_CompareTo_mD2F702CFAC4536BF92D218262CC096509AF97E4C_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_Equals_mF8215C50AF16D3685D298D6D3B4E35A345D4E9DD_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_Equals_mA7C94D9600D89D6734E45E2470CF57F4A8A2F442_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_Equals_m3964C355A393F163EC05ADB72F54317D7439DF82_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_Equals_m355B482C62357D27E8C0C4CA820B8EF3E34FCB6B_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_Equals_m2AC7716111558CBA2C8E5B34B623B31BAAF475F4_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_Equals_mFCAFBA37F06212725C4B46CD578D208779D40D12_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_GetHashCode_m722820B8F7CE4975F4A060C565E192259C5E8F7A_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mE589F12ED543025C909BC6F9C1C65DCF756CCFCA_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_System_Collections_IEnumerable_GetEnumerator_mB5B987A8B69250FA50AD2A66AEC23BE5B73A72FA_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_ToArray_mD992438F1C3B1ED9436D55E80F539B05F02876A9_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_get_Buffer_m453BB0D8491D838AC1381E57320C9FE6E3943F68_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_get_Length_m9CE762AB945BF8C956C2B6AF84AEE58B20A679C3_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_get_LengthInBytes_m2718009B0C788432818D447CD03D022FDFB2E4B0_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_get_buffer_m590958E1E714990BD451E67228F00F8DB98CC77E_AdjustorThunk ();
IL2CPP_EXTERN_C void FixedList64Bytes_1_get_length_m3DE952DD93CA6DA9DCC7AE3B38049D044BC24628_AdjustorThunk ();
IL2CPP_EXTERN_C void FreeListCore_1__ctor_m018D1A8697D2FB65C3C8BBFBF175DB9C68EAFB2F_AdjustorThunk ();
IL2CPP_EXTERN_C void FreeListCore_1_Add_mE3E0D914E60A3F9BC9482692FD08BBB813165FF6_AdjustorThunk ();
IL2CPP_EXTERN_C void FreeListCore_1_AsSpan_m6D31CDAF0AB9B37C33C0BC5DE22A207A98EEA373_AdjustorThunk ();
IL2CPP_EXTERN_C void FreeListCore_1_Dispose_m7726D719476C9DD4FBF79316486DCD895A3A8CAD_AdjustorThunk ();
IL2CPP_EXTERN_C void FreeListCore_1_Remove_m05518591CC734CA777E45FB0ED1051C9429F25D3_AdjustorThunk ();
IL2CPP_EXTERN_C void FreeListCore_1_get_IsDisposed_m4874FF53FEA7B18F41A9D71270BE5A0AB9A848F5_AdjustorThunk ();
IL2CPP_EXTERN_C void FunctionPointer_1__ctor_mC619E78A7A6A909670DFFE07825842FDA7D178F7_AdjustorThunk ();
IL2CPP_EXTERN_C void FunctionPointer_1_get_Invoke_m552FD5AD590BE1F22C35842835DBC3FB96BACA44_AdjustorThunk ();
IL2CPP_EXTERN_C void HashMapHelper_1_GetKeyValueArrays_TisIl2CppFullySharedGenericStruct_mE21354629769E9053A0A66D49C238EEE6A378A51_AdjustorThunk ();
IL2CPP_EXTERN_C void HashMapHelper_1_Dispose_mB9A6E998C327368FCA81FFF52F90700CE2557703_AdjustorThunk ();
IL2CPP_EXTERN_C void HashMapHelper_1_get_IsCreated_mFD6A2975F04E9C84A9886543625D8E8BCA71E398_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerable_1__ctor_mE8BEFCF0EF5E38CD2946F0344224AFF95CB9D88A_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerable_1_GetEnumerator_mE319C71A38C7A7AE12288857B13E0CA49C65A116_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerator_1__ctor_m700D1FAC76BF226957060636D4D8AB9C6584CDEC_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerator_1_Dispose_mEBC1E3FF6876007C72F6F76ED237165B599B2505_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerator_1_MoveNext_m6695B2065C6EB0E71B90C2EFF9429E4A76F4C0BA_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerator_1_Reset_mEF20F04CD33523EE6B378C8F992E49A2214E1626_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerator_1_System_Collections_IEnumerator_get_Current_mD2E14558F371D66EBB6269FEAA4143BCC357D472_AdjustorThunk ();
IL2CPP_EXTERN_C void IndexedCollectionPropertyBagEnumerator_1_get_Current_m4E570DCDE463BEA50BA7B3DC03595ED13939EDBE_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_1__ctor_mAE978CC133E57B40C2D810A714714CC92408E485_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_1_Equals_mCC83C55839566F40031379F89D9A13CB97E251F8_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_1_Equals_m9A435C12C15B901C148A4C8260D5501F5BB062D8_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_1_GetHashCode_mA7299C66E5453574F4375112C419DA0A60E442BA_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_1_get_name_m78A37B7D883B922994224E33DB7D146EA91D7590_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_1_get_usageType_mB00911CFBC80A809A96C358A5EF4040330D4EF21_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_1_set_name_mF25DD23D2A5D6234F01FA753D6A4C0518D664B08_AdjustorThunk ();
IL2CPP_EXTERN_C void InternalEnumerator_1__ctor_m464A908F35C335E4B3A17DE886808274C7745493_AdjustorThunk ();
IL2CPP_EXTERN_C void InternalEnumerator_1_Dispose_mF3113BF34937C09318EE23FAA3C8CAF4324284AA_AdjustorThunk ();
IL2CPP_EXTERN_C void InternalEnumerator_1_MoveNext_m0DF22C0F597D04AD07643D13B9C657A88F58A1F9_AdjustorThunk ();
IL2CPP_EXTERN_C void InternalEnumerator_1_System_Collections_IEnumerator_Reset_mA541889D3D4D07BEB148635EAEDC7F3AD70D6E43_AdjustorThunk ();
IL2CPP_EXTERN_C void InternalEnumerator_1_System_Collections_IEnumerator_get_Current_m9F616DD0F37ADC09A91128D6CB72AF97F8221D26_AdjustorThunk ();
IL2CPP_EXTERN_C void InternalEnumerator_1_get_Current_m2E7A04B825CC1C6C0A7B4B49CD8473CEF6A89FAC_AdjustorThunk ();
IL2CPP_EXTERN_C void JEnumerable_1__ctor_mD4769E3AF4442E9295648902C9C07C267C899D35_AdjustorThunk ();
IL2CPP_EXTERN_C void JEnumerable_1_Equals_m6E47C16543AEAFDA1DBA86907F9D1CE003468A6D_AdjustorThunk ();
IL2CPP_EXTERN_C void JEnumerable_1_Equals_m32BDED99001ED47C141F09BB9A0A9106524606CF_AdjustorThunk ();
IL2CPP_EXTERN_C void JEnumerable_1_GetEnumerator_mA8D66319EF7DD05C26DCF00F267B8FF4AD0B2B36_AdjustorThunk ();
IL2CPP_EXTERN_C void JEnumerable_1_GetHashCode_m9BD12C54D0E4F6C7A68476C79C7FD12280A19C2C_AdjustorThunk ();
IL2CPP_EXTERN_C void JEnumerable_1_System_Collections_IEnumerable_GetEnumerator_m047B396B5DFDE9A2034CEF35021890948C573109_AdjustorThunk ();
IL2CPP_EXTERN_C void KVPair_2_get_Key_mED89D3602B012281C2C837349C34191B0875115C_AdjustorThunk ();
IL2CPP_EXTERN_C void KVPair_2_get_Value_mC94F9A68A6FADF40DC6A14F23C4FF284241A6D88_AdjustorThunk ();
IL2CPP_EXTERN_C void KeyValuePair_2__ctor_mD82E516936D2BDE6D46C8C45270250647986231E_AdjustorThunk ();
IL2CPP_EXTERN_C void KeyValuePair_2_ToString_m73A769E5C2C6956378D80913C3E9D8A68513F905_AdjustorThunk ();
IL2CPP_EXTERN_C void KeyValuePair_2_get_Key_mBE75BF8983618BC1ACEC20F94C1BFF85C8AA50F1_AdjustorThunk ();
IL2CPP_EXTERN_C void KeyValuePair_2_get_Value_mFA1964BF56AA214EE0D491CC197F61BC9E5F1F7A_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1__ctor_m9169978DDAA889BFF05C452AB15A211C63E4A35C_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1__ctor_mDF00881455B7BBDCEB668A1E3119CC67B7DAEE23_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1_AddRange_mCE64CAFAC2EBC41A1C72EB2F383F2F21744A4331_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1_AddWithBufferAllocation_mD7E0D26AB6CE709D88228EEA5C8C20B4AF973ECD_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1_AllocateBuffer_m6399AE087574673718CFC172F364FCBCFDD3E288_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1_CopyTo_m2E80BBDC8892C16A99C069C380981AEC0341AA43_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1_GetBuffer_m5EB830E4B175F0E9EBE9C90BB979B8666BC98F9E_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1_ToArray_m1AC52FEFD7058DF67EC15212C4CE9111F640BF6F_AdjustorThunk ();
IL2CPP_EXTERN_C void LargeArrayBuilder_1_TryMove_mA502F3D33ABD9C0EC5C2ADDC0D8DA3EACE586AFA_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1__ctor_mC47FC2942AE3A0C07B8D034B46341372061D1C4A_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1__ctor_m699AD6DEF5B75F84F0B5549C9434767A4A2DB9C9_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1_get_asset_m4BA1374FF657FC2C40BDFAF371B574B76F1965F8_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1_get_instanceID_m9B7093A756F58875D533B3E3DA5533175864A74B_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1_get_isBroken_mFC4F5B17985606AFA118D25579C7C5FD51A7D3AC_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1_get_isSet_mA5602191FDFA1435F2264EFA60BB5E632AA681E0_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1_set_asset_mCF4C051B624B3D5D1978EE86D81E9EB90CF42F4C_AdjustorThunk ();
IL2CPP_EXTERN_C void LazyLoadReference_1_set_instanceID_mADE5AACEDA2F279D33555FD667C398C88BAC244F_AdjustorThunk ();
IL2CPP_EXTERN_C void ListBuilder_1__ctor_mFC129BE2CF658F37264E5AE87C38ACBE1686FC06_AdjustorThunk ();
IL2CPP_EXTERN_C void ListBuilder_1_Add_mC19811ACD1D830765599A1583F96C56D16A9E5AD_AdjustorThunk ();
IL2CPP_EXTERN_C void ListBuilder_1_CopyTo_m07DD8110CA53CAE4B1C65F1E9A2AC9C074F14378_AdjustorThunk ();
IL2CPP_EXTERN_C void ListBuilder_1_ToArray_mADCCB17012F7FB8EE90E45E2CD13DBD2E71FE781_AdjustorThunk ();
IL2CPP_EXTERN_C void ListBuilder_1_get_Count_m5FC923A60B6BCD6D28FA95B0495EF5E537266285_AdjustorThunk ();
IL2CPP_EXTERN_C void ListBuilder_1_get_Item_m23AA6DB4615BCE00F8A63C6152113822F0250EEE_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1__ctor_m3743D2A1BDE7D9720706FF7CFE3C270432C4D381_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1__ctor_m7EA183117D6958FFAC919F291510339F7792C7CD_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1__ctor_m39F9C3933A6F0AD9E9CE3A5B597A770C54F6FECF_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1__ctor_m58BECFC48979D14A69D6AC373301B755043ACA9B_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1__ctor_m5ABFBF1303AB4B2B8AD03A4586CC52B6098DCBE9_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1__ctor_m5F9E06AAB95D8F8AF62C3AD68DEC2F6716CDEE05_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_CopyTo_mDB8DD4693F007CBBDC70454425C368FD0614D3FC_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_Equals_m2F882A89DBD535FFE48B39EBAF2626BA2AD36DA0_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_Equals_m03AA1FCD0E395B27BD53551C06FDC8ED34F10A9A_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_GetHashCode_m7A4C30D97D133B153A50D638A7D596CDA1BD11B5_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_Pin_m9EEBCD8E7B6269C02BFBD57F2E67854920DFB85B_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_Slice_mB1A59814638EB046790D5DB854A8D42BA2CB686F_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_Slice_m6D92DFA60814F303301792ABF267BA35242FACA4_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_ToArray_mF5FFD4A0E8AC951FDA34807539E9631310B6D757_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_ToString_m8587562831B505E363617A254DE467726D4C6351_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_TryCopyTo_mA894E1A58E1FB0ACB1FEDECD7DDA18A8ABFD4CD3_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_get_IsEmpty_mFE27DC49FE38E33AD226323A42DB57922F00B0C8_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_get_Length_m0D3942C435EEDB938ADDA2CE7C625761CEAB520E_AdjustorThunk ();
IL2CPP_EXTERN_C void Memory_1_get_Span_m86E7A7A0C72430FDB1B2A2BCEEAAEEF7E7F83596_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CheckReinterpretLoadRange_TisIl2CppFullySharedGenericStruct_m26E933EF2857F7DFEB041A243BCE46DA67A31435_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CheckReinterpretSize_TisIl2CppFullySharedGenericAny_mB5EA92D2C8ED01EA1AF30582560964A0FF2523F8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CheckReinterpretStoreRange_TisIl2CppFullySharedGenericStruct_m941429E862A4F6C5329367847862AE742771E044_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_InternalReinterpret_TisIl2CppFullySharedGenericStruct_mB0CDEB18464001F253D27086821B2DEE7C1859E8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_Reinterpret_TisIl2CppFullySharedGenericStruct_m1FE7062E31411B740E16829B437A95E0BC1FE66E_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_Reinterpret_TisIl2CppFullySharedGenericStruct_m0A4282BEF39DA967523E47AF7251C3F5A58651F8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_ReinterpretLoad_TisIl2CppFullySharedGenericStruct_mAEB3472F537D9233CFEA99C737C97B229E5D2F31_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_ReinterpretStore_TisIl2CppFullySharedGenericStruct_mC77044D13F85A6D5A7AFFF1AB25B0E8E157CFD0C_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1__ctor_mFA31ABA65EA503C10D2A14D67A78BA391FC47979_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1__ctor_m840B99F1F4447BB0CE13C53803EB5DB4930E3F89_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1__ctor_m788DE0F85C4051DDF092DDF96484DE655ACFB6F1_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_AsReadOnly_m6A3C4FD632C19C139E79007CE683549D40A600F2_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_AsReadOnlySpan_mEDF5E795C5FC628766F2016DFB498E87E36BA3A0_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_AsSpan_m5E5BA6DA8F13E99DA9E483864C4FE5CC56ED1259_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CheckElementReadAccess_mB188CE6C850D24F288C813CEC493CC63760FB6F2_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CheckElementWriteAccess_m354C69BA2B8C9E9788F54ECA26043F230054BB85_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CheckGetSubArrayArguments_mB47D62002A4852E22E03AC421FFDFF5A48FDA21E_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CopyFrom_m910E1EF42DE5ACF6E07014ADC35D83E87A3C43A8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CopyFrom_mA52B88617F4F7972D7275E97D2D52CF623754308_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CopyTo_mCE5ACF49C2F1719FB011E7B9715D90315C6BB72C_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_CopyTo_m6072353F8BA28E08B24828767C0A5AA713F0B307_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_Dispose_m365A262FA4CA431467F021D7732CECD68316AF80_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_Dispose_mC40FD0F1B87730842624526506EDBA485496D3EB_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_Equals_mCC874C041CCDC87B4FDAF62364706ADA4DE292C4_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_Equals_m5FD0AFF8B0E23458ADFA928C1505060F707D34B3_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_GetEnumerator_mB8476B005B66C374A79CAA0F5887F98EDD4D8E32_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_GetHashCode_mB187A5111AF164207661A0D456D14F283F391866_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_GetSubArray_mB3D1C75BB777B0C93DF3FB3B297F748B41362959_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m4D33419BA13170369A1911A84F5CE9538A1ADB9A_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_System_Collections_IEnumerable_GetEnumerator_m9DE04F86BF0FB744FEB842CD21E503987BB298DA_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_ToArray_m36734C0A577134F73463545CA7A587BCA1792002_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_get_IsCreated_m527A2C3B75C25BFF29D1D9EA88C81172FF4F5A5A_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_get_Item_mA8C8A69EB3A5D460C55DFCD27275CD5BA5E2B455_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_get_Length_mBE5CC8B844994CFC4AB434235F915881575E63C8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeArray_1_set_Item_m629BDF69720F9FF193478E89307F9B6A56425379_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeKeyValueArrays_2__ctor_m584C91680CD96728B10CBAA2021F14AE162A04E4_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeKeyValueArrays_2_Dispose_m08B6A4226A0125C6123942BE90703D08153CCE37_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeKeyValueArrays_2_get_Length_m5540F10E36DBBB6005821F5FEAF15610EB68C429_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_Dispose_TisIl2CppFullySharedGenericStruct_mC1029EC6803B86B2C7577203E6B311E262BE4F65_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_Initialize_TisIl2CppFullySharedGenericStruct_m84B39CC62859C7E4EDB87CA79D4F1B31FCCA4C54_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1__ctor_mCF266C5F6762E110B7EAEAF5546388FDB3621BD8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1__ctor_mA1DFF4EEBED87ED011F318AEC145588B17212121_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_Add_m9BE8F632802CF4BA6110C86A3C1842F568C58DBB_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AddNoResize_m36834BC68209BDEE1E19A6F058430821B0C64827_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AddRange_mA24575907F4D1E91B47EFA158C59E36759917EA3_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AddRange_m5469007F99D38C73CAF59B7F3A2A20BDA7605288_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AddRangeNoResize_m788785F2770CF6601F6A8AC031CFE93FE154229E_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AddRangeNoResize_m0FB384BF2D4A909BBF08DC85CA4C351B29D245AA_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AddReplicate_m639D064AC5E19B02227D042D88A1204E0B131F8A_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AsArray_m1E9616CC42457555561B1165B47ED6E2EEADAC98_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AsDeferredJobArray_m0C54A266A92C6F33AA5A55FDC25744EA55651FE1_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AsParallelReader_mC0476FC7DBBEC92472400D556FDA960843E4B1DD_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AsParallelWriter_m3F0A4A00A415130242EDE59D2AB67FF06B6AD9D2_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_AsReadOnly_mE3E9EA4C33D970606F4BFD32677EC2C9EE74A717_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_CheckHandleMatches_mA892EAA79CDE121D317AC1FC2FCE8A0C50F03CD4_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_Clear_mD122F42577EAB804E1D4E3459202BCABA97A9021_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_CopyFrom_m432E91DFECDC6162EBC8406FE5B0F485E8CDD6FD_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_CopyFrom_mD2E8CAA2F97C6B8960A7686EEB7AEAA457463287_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_CopyFrom_mA6685A428A1C6F5D6DFD17EC2816E367B117ED7A_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_Dispose_m42535264C7291A64741CA36B4F0567D15D7BDC2F_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_Dispose_m5BFE1E59D09E1706BD6BBDB5D3C7BD0E3C18FB1D_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_ElementAt_mC997B931531C23A8CC2051A7A7F82A866F4F89BA_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_GetEnumerator_mAEC3E4F148C87993A6A957CCD8D16A1CBADF6621_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_GetUnsafeList_m4787F7FF0C74B362B5DC98531C2FF66279A5EAEF_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_InsertRange_m5616E3C81C172218D366190BC6EFEAF13BFF6AAF_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_InsertRangeWithBeginEnd_m6C08CC6FCE0C86D983776D77B196EAF0FA4FB020_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_RemoveAt_mEDD020DF08725F529B5AA06F652196FD3B6ABC92_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_RemoveAtSwapBack_m3BAA4B8DC92D6A907E4B8570A4919F0C0B2211B7_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_RemoveRange_mC7C99994ADAF3DD89F75B190D90921449CDB3247_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_RemoveRangeSwapBack_mC915D35A96C41DB035F4D0FCEF77143963205A4E_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_Resize_m84F443F6B9C92F2415832AF704F927D86166870C_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_ResizeUninitialized_mC281739878E0A1D9BD814C0B970B46A554D8EDFD_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_SetCapacity_mF54F13DED0AFDF81EFEA207F617090CEC6865C20_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m8645EF055A7AF1625D1EC908EB24A081FB8C2EA8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_System_Collections_IEnumerable_GetEnumerator_m5BE8F41FB19932A28D5A41428FBE94C619FE5EF2_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_ToArray_m3ED93E608FAA962BC4927B048C3F729D2E9B0B4F_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_TrimExcess_m83C547A2B977DBE462279C50F85931114868E16B_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_get_Capacity_m5F1CF166F164381AE02960A7686D8ACFBA0BF5ED_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_get_IsCreated_m4E2356CCFFB361C91CBEFB528F727AC7C244043F_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_get_IsEmpty_m7BFF94CACA435ED947CEEC6E9090D8D1EC80FC6B_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_get_Item_m4C9E1C7BB475457EAE88A496A68A77E3F7A64F92_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_get_Length_mBCE0D52E1FEFC40B5CFEE2F41B493C7FF6A07FA7_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_set_Capacity_m73E9EE87AF9B2F512893B9A3BD6240B4D8395CF1_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_set_Item_m30347B8869454C74E36A9479132E95C1C2282C01_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeList_1_set_Length_m49FDBFBB875686DADE2EF40A24625D87D6893E6A_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_SliceConvert_TisIl2CppFullySharedGenericStruct_m95D4A04F6012B6943507CFAECA0DFAE7A1D26AB1_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_SliceWithStride_TisIl2CppFullySharedGenericStruct_m3CBBB7F7C24E30E29495D156CD61D886436BB645_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_SliceWithStride_TisIl2CppFullySharedGenericStruct_mEE3A5892BF7F5897108DA26224AD248912AE0099_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1__ctor_m606E9478EC6822C3776B093EFC3DC98678E00F9A_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1__ctor_m89E2A81C9B0649A573BC279C2AA35CAF771B8B7D_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1__ctor_mFB24F80C08D8E11398A46C05BF60BC2DC06F71E9_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1__ctor_m313B1A91AB4ADBA821C074187D67957126F93DFF_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1__ctor_m4C0C75BC6633B56253FD4A7CD7F38A34E73C6253_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_CheckReadIndex_mCEFD744F532EA8119235FCB631C44E0B48E3C583_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_CheckWriteIndex_m8B0937EED6BF8634106BE95140CE8A5AB9F873C2_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_CopyFrom_mEC5EDFAA67CE0B371247FCF1CAB5D9471B33681F_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_CopyFrom_mDC4D4CE8165BCA0DF465BA4F0B381EC4EB400379_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_CopyTo_m0B252F8E50B4792025D47383A4F5521AF80B747D_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_CopyTo_m2BD0A050C86D24036D2E97A444931C7E7D25E5F8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_Equals_m02465844945FD2B47F0A98A9050960DBE3AE1B3F_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_Equals_m62EA67D83A6D9F7C01046E9427C883C1E8A6C872_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_GetEnumerator_mF660D58D227B89E1A0B3B8273F3350163CEA1112_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_GetHashCode_m15101FD5A9795DB87AE06FF9D749AD7D79D9E09C_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mB8DB4618FC8BF5F06A40176413B052895F44439D_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_System_Collections_IEnumerable_GetEnumerator_mEA7995EB021548CB2B13B07DEB57985095EB7161_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_ToArray_m015E65BBFE8EB970B4FF4ACAB91FD4B952A3F0E0_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_get_Item_mB5E7BDE4EDF7E31480A9785BE103CC7E17E0E3A8_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_get_Length_m0225CA0944599882AC9C2A06A99FDC685362AFBE_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_get_Stride_m3F2ACE95526BFFDE7967887D3C6188F286C6A12D_AdjustorThunk ();
IL2CPP_EXTERN_C void NativeSlice_1_set_Item_mAFD3C724B49679743057B7DE15937A2D78EB2339_AdjustorThunk ();
IL2CPP_EXTERN_C void NodePath__ctor_mA9A74612E0785F30CCC660C7936418FD57A9EB55_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1__ctor_m4257D7FF23A495D1B204F20330FBDED58248E4CC_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1_Equals_m9563DBFA2EA4159645E91A19EAEF1F30B96B0482_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1_GetHashCode_mA01CD04085D8BE8763C59776251FB3D85411024E_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1_GetValueOrDefault_mC057FBD944AF068B90EBDD0B496231A01B2A4228_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1_GetValueOrDefault_m7CC659C1A1F8403C8CE47BB7B66152FA3B054598_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1_ToString_mA7FCB0708C4028709EB48813E00AE0B2F29688A6_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1_get_HasValue_m14F273FB376DF00D727434CDCD28AB4EDCC14C3C_AdjustorThunk ();
IL2CPP_EXTERN_C void Nullable_1_get_Value_mA083C4D9192050DC38513BDD9D364C5C68A3A675_AdjustorThunk ();
IL2CPP_EXTERN_C void Pair_2__ctor_mCA54688368FE894C9F314471A3DA94A72B709F51_AdjustorThunk ();
IL2CPP_EXTERN_C void Pair_2_ToString_m085342B96D11597F12C847BB5175075784F51E5B_AdjustorThunk ();
IL2CPP_EXTERN_C void ParallelWriter__ctor_m90A3784A8D355193CE6FFD0D49A8B0B9B950661A_AdjustorThunk ();
IL2CPP_EXTERN_C void PooledObject_1__ctor_m26481DA76B39862752040C3016392A923303D122_AdjustorThunk ();
IL2CPP_EXTERN_C void PooledObject_1_System_IDisposable_Dispose_mBDBE6E4606DF5793230E351CA0B89611C13606FC_AdjustorThunk ();
IL2CPP_EXTERN_C void PropertyCollection_1__ctor_m0E8538F1E11CB09AB1E8846DB44D0B6806838688_AdjustorThunk ();
IL2CPP_EXTERN_C void PropertyCollection_1__ctor_m0A7C407D8B67A170E2EFD9899B52ABFDE4BCB886_AdjustorThunk ();
IL2CPP_EXTERN_C void PropertyCollection_1__ctor_m9E9AD7E25A9F865F9C290CD6A8E6A6DED56B358B_AdjustorThunk ();
IL2CPP_EXTERN_C void PropertyCollection_1_GetEnumerator_mD933057B218712B4B2DAFBF6E423497F0D578261_AdjustorThunk ();
IL2CPP_EXTERN_C void PropertyCollection_1_System_Collections_Generic_IEnumerableU3CUnity_Properties_IPropertyU3CTContainerU3EU3E_GetEnumerator_m64C5ED91B740D237B110478C30C15996C06199F4_AdjustorThunk ();
IL2CPP_EXTERN_C void PropertyCollection_1_System_Collections_IEnumerable_GetEnumerator_m91E0BFAF4178B55606E909C44EDAEA3A543AD5B5_AdjustorThunk ();
IL2CPP_EXTERN_C void RBTreeEnumerator__ctor_m4F958B5E04B28EDEA0C7B8FB8E7CF4ED0739BBEB_AdjustorThunk ();
IL2CPP_EXTERN_C void RBTreeEnumerator__ctor_mC7027B6747A32BD6A978E0DA6DE74FA6DB6AEA5C_AdjustorThunk ();
IL2CPP_EXTERN_C void RBTreeEnumerator_Dispose_m44368C76E9C0F3A17DB3342C808CA8F9582C5955_AdjustorThunk ();
IL2CPP_EXTERN_C void RBTreeEnumerator_MoveNext_mB5562C7DBD432B39AC6CDE17139006D3141A2E7E_AdjustorThunk ();
IL2CPP_EXTERN_C void RBTreeEnumerator_System_Collections_IEnumerator_Reset_m30C1ACC2356659BDDEC767619AA0D8B5EA06E470_AdjustorThunk ();
IL2CPP_EXTERN_C void RBTreeEnumerator_System_Collections_IEnumerator_get_Current_mD023E34D3906399CCC1A8E8AB645C5C9810898FB_AdjustorThunk ();
IL2CPP_EXTERN_C void RBTreeEnumerator_get_Current_m55E7496F0199640CB43772D3C21F0A10462AE734_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_Reinterpret_TisIl2CppFullySharedGenericStruct_m5605CC31285684E9A024E62D6D5C680A798020E0_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly__ctor_mB05017BDF2B337BE34CC3E2F090BD7A67F155549_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_AsReadOnlySpan_m64C20F2CC8054C58682D3CB09F2D1B7109C17950_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_CheckElementReadAccess_m785E3B2561122A22110209C8643E4D1A37879538_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_CopyTo_mF33553F53E45582FE86F5FB738A50F5929AD5A3E_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_CopyTo_m22CB48ADD0104582FDC3CF2581B8A7BF187FD3DD_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_GetEnumerator_m1BA5BE1261F5F3B4965AEDA65F7D33ACB9C31117_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mD1B3924A7A3A2B202FABF9D1A8391737F0CB3943_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_System_Collections_IEnumerable_GetEnumerator_mEDCB287C2177C8DCFDE2136300142D738D5594F4_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_ToArray_mFD80D7B7BE13A74B4BE53E54CE3BCB5300F46D27_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_get_IsCreated_mADD8624D940450CB4F64C20BCFEFAC0065E485A2_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_get_Item_mAD85B6B3DEDEACD9CB3738C91AE09F7B0102B15D_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_get_Length_m0A746E7788B5229B9FA65917E34CEAC58D76B351_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_System_Collections_Generic_IEnumerableU3CUnity_Collections_KVPairU3CTKeyU2CTValueU3EU3E_GetEnumerator_m6D89344EA22D40C667DD941E4B2C2D5D967BD4FE_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnly_System_Collections_IEnumerable_GetEnumerator_mFAFF5C0905599BCEC904BDB222C31534F8F299E8_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1__ctor_m499964C79FA86289EF11F618EF98F63259D680F6_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1__ctor_m1FD19CA8A2CB8A92DC095EE34D414ADE2FD39A12_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1__ctor_m10BC8BCF1707700EF96C3A32CA7F3349F4080508_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_CopyTo_m27F26CD338FEA8EBF4C636CC610BD3D0A596B446_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_Equals_mF7DA75997B4FFDFB2D6F953E8DC580EC3C941A61_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_Equals_m2C16DF2AE78D8E7182CFAE8E3E07C3B20A777888_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_GetHashCode_m96535F6C6512A16E6DBC2A8341F8FECDF2E47F54_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_GetObjectStartLength_m76D2DB58E89EF8DAAB0BEEC5E80CC99E7C3CB752_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_Pin_m0F5197F4F1ABABE6C5A8976AEBC8243711CD3709_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_Slice_m068EB458013792137E461EA951FF873B6A3E185E_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_Slice_mDFC40FA7D3D023B52F861034F4368E6BE00B47C3_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_ToArray_m44807420910F7442D09F80751674CF30711B0764_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_ToString_m1AB8B8725FFC7144748E59C73875E2C6822E2321_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_TryCopyTo_m7B61F10E7E32DDFD439E77FF7BBB31AEBDAD1CDA_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_get_IsEmpty_m09A6A2BB7D8CFDDD7CA4DE125253FA884283A59C_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_get_Length_m65A4E9056707DCDBB66443924288D7E0BA7D0074_AdjustorThunk ();
IL2CPP_EXTERN_C void ReadOnlyMemory_1_get_Span_m3BBCF2EFAFAB7DAA8882208AF319487E00DC3895_AdjustorThunk ();
IL2CPP_EXTERN_C void RentArray_1__ctor_mE3E2E7E996AC6814E25FF061694B621203718996_AdjustorThunk ();
IL2CPP_EXTERN_C void RentArray_1_Dispose_mADD3D61E5163B43B6945A4D4A66C492DA34721AC_AdjustorThunk ();
IL2CPP_EXTERN_C void RentArray_1_DisposeManually_m85CBD0E938DF7FD532BA929C5B5696BC9DD77129_AdjustorThunk ();
IL2CPP_EXTERN_C void ScriptPlayable_1__ctor_m63AE9D2F322913109CA948EDC429CB9CC2FD8516_AdjustorThunk ();
IL2CPP_EXTERN_C void ScriptPlayable_1_Equals_mC3D0F2978F3E80114ADFB3D17BDB38DE6B299ACE_AdjustorThunk ();
IL2CPP_EXTERN_C void ScriptPlayable_1_GetBehaviour_m86713AAFF1D1F7D4FB370489F71AE4228741EFE8_AdjustorThunk ();
IL2CPP_EXTERN_C void ScriptPlayable_1_GetHandle_mFBB086A8188A0D77BB5CF4A1A03031EA9B67D22A_AdjustorThunk ();
IL2CPP_EXTERN_C void SharedStatic_1__ctor_m467F9A64986F442AA4853C5C314D0A54D887CDDC_AdjustorThunk ();
IL2CPP_EXTERN_C void SharedStatic_1_get_Data_m679BD82198B4EC1D89F2EDE946A60F4DEE8E47E2_AdjustorThunk ();
IL2CPP_EXTERN_C void SharedStatic_1_get_UnsafeDataPointer_m3F411F556361D224096615DAA35B86D6B725C09D_AdjustorThunk ();
IL2CPP_EXTERN_C void SharedStatic_1__ctor_m57842D87210A109206E3DAFEBD441B46EDBC809E_AdjustorThunk ();
IL2CPP_EXTERN_C void SharedStatic_1_get_Data_m4D3D9A03646881BE9065C8939BF1CA28195FF262_AdjustorThunk ();
IL2CPP_EXTERN_C void SparselyPopulatedArrayAddInfo_1__ctor_m323E378EC0EE0C48A24AA0E14E40D7B020BB0458_AdjustorThunk ();
IL2CPP_EXTERN_C void SparselyPopulatedArrayAddInfo_1_get_Index_mCBBF3F010A994612AC94DA417AB8D04A2C92B45A_AdjustorThunk ();
IL2CPP_EXTERN_C void SparselyPopulatedArrayAddInfo_1_get_Source_mBA043CE79666AA955D0FE4335ED3B82802E75C86_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_Acquire_m280DBCF2EC3FCBCE18CEBDC5679B88754FF6E20E_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_CopyFrom_m202E0E466F8BBC806DE35A1E6B0ECB88CB087D4E_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_Equals_m37FFDDD96CA2205E2E23202FED5DDEC5C1008E3E_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_Equals_m7ADB463EAFC073CFB4C9A9BACA0FE52F67A59118_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_GetHashCode_m9D45C4A9A08526FE0FE86F68B23EE4F22CE50B9C_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_Read_m91033FE9A7F22D88A387AF7C0F02BC6C84567CA8_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_ReferenceEquals_m701B72F6C9809D21F952990782ACB6211760F69B_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_Release_mD49B706BA9CDDAC374D9733812BA6F07277B7AE8_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_Write_m59356C10374B7A043341F241B1C8F16FF6B17177_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_get_id_m911C0DB700108F5CEACB5A891BC5907B1DAF6B0A_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleDataRef_1_get_refCount_mE06114815FAE2B0330A77036CDB5CFA909D05307_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1__ctor_m098CD8479B142F8A1041BAE3DBB39CB44CBD10C5_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1__ctor_m12078C14D0B69B284AFA4CD626F791C75FD9F5B9_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1__ctor_mAF41CAADB2D61B5861967141DE9BCFD6EA66DC40_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_Equals_m35F52A3A4D787AB2FC378F40FDF39543F72FC97D_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_Equals_mE489D9352D87217721642E46C4EFD2216E219967_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_GetHashCode_m6DE12566480AA4B9B8B4B99568482E501F8C80B3_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_ToString_m648E71C585F6EF9216C5CF7EA096E4AE1ADC0AC6_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_get_keyword_m760AB801D94DF23D10535741365D6753632295E6_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_get_value_m46D4F7BEA4855BD3882B5F4692086C84C6174A92_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_set_keyword_m122C65127E2AFFCB5E896D01C961BB438C8B6937_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleEnum_1_set_value_mF2E141FA5D14DCA317134E4CE1AAF805BEE79D99_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1__ctor_m333E7B6EA3C2FC53814EDC3656A2E4D13836F26A_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1__ctor_m1E496823D7D31CB43EEB6B43F7790ED601CB673A_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1__ctor_mBC7A084C97F0F7B884ED94CFFE16F937D60CCED3_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_Equals_mCB0855F7F81E96EF2A4619A830ADE420F87B92D0_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_Equals_m9AC92EF88DE3B421DB485613F276789C3CE49625_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_GetHashCode_mA134CD3E8F97859CDE6CB0464067DA5B6881863E_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_ToString_mD110328BDAC3B05B2F3406D3178B8FEFAE66E02D_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_get_keyword_m28C3A94C237B251861E39930D591248D28587F01_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_get_value_m3BC32FA5CA94A2A59B8C80EBEE844B064C63D68C_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_set_keyword_mB5843B33643AD8D57109BDD664B69C7A72DFFF27_AdjustorThunk ();
IL2CPP_EXTERN_C void StyleList_1_set_value_m61C01B4E5B57572F93B48A871AD54700DFEF9A3C_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1__ctor_mC0685A91667DFC9925767472A6E98CB57BC6E8B7_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1__ctor_mB5B6585D0032E8D8768C2651C033827F6DBA7034_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1__ctor_m7B96812F826309F2ECD5AC73A882940FB618C52D_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_Add_m248525D1C0A87CC5664F0F76FF02B7FF626B0C59_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_Clear_m5B8F995528E4DB2F1177A7B3B4E806DAB18C9685_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_CurrentItem_m56E11105E8B704235B8DFB1A260090D688895F0D_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_Peek_mF7B4050F25F0B33220443A6A19D4465E36A7356C_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_Pop_m988B6D5B3E24937F963C9AB1A16240A1E4A0D1D3_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_PreviousItem_m5C228E38434E671964665E55379F16DC1345A914_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_Push_m7A60BD56D66B9FB81107DC6A78C3E8AFB12B618C_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_Remove_mB5F58B12D1767F89DE2C328D949CF0BD9DEBC656_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_SetDefault_m3FA35ECFD02705D964BB1274304892CC27CADB51_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_get_Count_m9FF7B40FB58184A477384BA3D58E97729DECC6B4_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_get_current_mB3845029EE3EE8D0C0EBF05DACC242E8C72155BD_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_get_rolloverSize_m048FA3E2119E34E520AE3015B813C24992092583_AdjustorThunk ();
IL2CPP_EXTERN_C void TMP_TextProcessingStack_1_set_rolloverSize_m9B297CB1848946D09147926310F5B2F989EB9D5D_AdjustorThunk ();
IL2CPP_EXTERN_C void TaskAwaiter_1__ctor_m33D20D5D1F650793298C91D129010A10D7176FFD_AdjustorThunk ();
IL2CPP_EXTERN_C void TaskAwaiter_1_GetResult_mA0FDEC1F33CAC08401C6F3B9E5A5C6F1B4503EEB_AdjustorThunk ();
IL2CPP_EXTERN_C void TaskAwaiter_1_UnsafeOnCompleted_m8B9DC9D7FF95BB8129F4A5D5CF2DF0AE62E081E3_AdjustorThunk ();
IL2CPP_EXTERN_C void TaskAwaiter_1_get_IsCompleted_m7F153D4DF6456F60BABB1E7663CDDF3EFE172007_AdjustorThunk ();
IL2CPP_EXTERN_C void TaskPool_1_TryPop_m90F71FE3BA3FC7353C008C48842AE70D358E6D76_AdjustorThunk ();
IL2CPP_EXTERN_C void TaskPool_1_TryPush_mAC9F8F3F6E70F2F279D0200E089A1C122A5D77E4_AdjustorThunk ();
IL2CPP_EXTERN_C void TaskPool_1_get_Size_mA6E840439A90DAC450C66102D1940FEC10734999_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1__ctor_m0FC3CEAC0DDDFF6286541241CB857AF50C5A902A_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1__ctor_m58B4B2FDC40E86003BB9D6C7915682C6913F9CD0_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1__ctor_m8AC2C57EC94FA3001FF52C2ED5D645B7562C7AED_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_Add_mCF77A6D04A7073AF3B3567DBE467B2FFDADE761E_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_Clear_m035C75BF8F609D61E1DFE28E4562BDB153767D9B_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_CurrentItem_m391132854FBCFFEF6E24BA7A0702428DB40D6995_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_Peek_mC737683E45A62573A24643E87A99F1AAC8EB405E_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_Pop_m56AD5DA4F9270C4AE36BDE85BA774AF5D2EA94D4_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_PreviousItem_mDD8857B37F66B4B261BC52ADC0A6ED128281A9C7_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_Push_mDB20BD1D2A8E67A798610E28B8D7B0C8740CBE33_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_Remove_m39EAC0C6497D42164E0388A42A7BF94F3D1A426C_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_SetDefault_mE48E32BB264F7043E8473984493F95B9F23F9D5E_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_get_Count_m67CA9DEEEC5AE089020715679DA79E883D59FE99_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_get_current_mB594B5CC26EB0A7E99F76C90A4F28CAF6CCB145D_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_get_rolloverSize_m11BD7543661CF955F6B203F7EF53D7B550643B24_AdjustorThunk ();
IL2CPP_EXTERN_C void TextProcessingStack_1_set_rolloverSize_mD2C4E5D6DA61E38220C30E05D4CF79F0D91D313C_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1__ctor_mBAA896CCBE0AA825D700E5559353BE8587C1424A_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_AddItem_m53A8A34BB24B49FBEFFB53A21F269CCCEF648F08_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_AddItemToParent_m780A3FDB6A1188BEA1309A0AE9BB25AD29D40161_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_BuildTree_mD166D8360BFC596078297B378D5D986DF13A0731_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_GetDataForId_m9690E042F292CEBB8ECB763317BA9609FF756845_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_GetParentId_m4CE1D6161FAB0FD1ACA997479BAE91171CEDEE9B_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_HasAncestor_mBDE8B6AAE139D95CCCC57585C9215776FADA79B2_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_Move_m14B65B5E9B4AE186320510D7CAE612E2EB51F754_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_RefreshTree_m2ED2991C1256D4D87520C98B912C98A85CD85065_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_RemoveFromParent_m61ABB21A998047C53B79949DF81E230F0724B35D_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_TryRemove_m819B318E7B2046685FFCA06BB29FF9857AE49F22_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_TryRemoveChildrenIds_mE741422819F243163B839D863C6A37ABBB1D4E27_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_UpdateParentTree_m2081FD7D8124B87D56033B37E0F87A3E19D05815_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeData_1_get_rootItemIds_mFF10BB5A8020FDE42204D4ABCA2407DCEA372E13_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1__ctor_m13FDCF45EF39F25E38B74175D1E3808996B6E39D_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_AddChild_mB937584D40D5080ED0F3F21C9846A6182D8AE4FB_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_AddChildren_m88170DDB56BC6F4A3327DDE8A70FB8F5874EB5BA_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_GetChildIndex_mAF9A92B34A982331F790E760FD1278C0747C0B0C_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_InsertChild_mD024A216C9B3FC87615A2020A5FEF9278DD251BE_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_RemoveChild_m1FEFFA22DAB2CB0EAEC760B9D8E516EDDA91078F_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_ReplaceChild_m809FD903843DAA5A68A0BB8894CA4E90DEAA0E33_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_get_children_mDF10B651BB3421F040A1F1D85F59E6C6A4FC2E21_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_get_data_mC3A73BA0B7B186E5674660F3B0A4D6D08D37216B_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_get_hasChildren_m884635D21B4D6454E480EF0C2DE596C3F81107FB_AdjustorThunk ();
IL2CPP_EXTERN_C void TreeViewItemData_1_get_id_m8636291EDF9E60912758667BCAC0037EE002E3D4_AdjustorThunk ();
IL2CPP_EXTERN_C void TriggerEvent_1_Add_m099707EB523C057C0893F6205B24E2BFF93D755B_AdjustorThunk ();
IL2CPP_EXTERN_C void TriggerEvent_1_LogError_m7F2F0D3F680FA92149285B385339F16B50951364_AdjustorThunk ();
IL2CPP_EXTERN_C void TriggerEvent_1_Remove_m4E517F1987E502F01B96F58F94A5AF3F4B28724D_AdjustorThunk ();
IL2CPP_EXTERN_C void TriggerEvent_1_SetCompleted_m3E9328515C580B4E5698CF720026119EBC6A8421_AdjustorThunk ();
IL2CPP_EXTERN_C void TriggerEvent_1_SetResult_m76ECB482F9A180878BE4539A553D4FC8AF4B5691_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddRelationship_TisRuntimeObject_mCE90C2CFF3D74498DB002082A6C369476CF60A1E_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddType_TisRuntimeObject_mAA792DDF5F5D7C04FAE62C49830117857251CBD8_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Children_TisRuntimeObject_m61D8848C1C54D2DD978DD5AAC64C7A43BC15721E_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Children_TisRuntimeObject_mEDAB1BB2C9AC80AA6745897236806154F343A948_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Descendents_TisRuntimeObject_m2139DC8B45DB936419759E7E7A05BC8BF9FB1D80_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Descendents_TisRuntimeObject_m288C5D7CE5E90BEA1A8E3AFCDA6FC32CFA5FEA82_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_ForEach_TisIl2CppFullySharedGenericAny_m26541CE74E5AD2D165E96E05155B822676EAF54C_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_ForEach_TisIl2CppFullySharedGenericAny_m3C0FDD427EB640B1AE1BC7655C1219A368331F5A_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_OfType_TisRuntimeObject_mE7DE3EF3A29D06AB2EA8C78B1837489AB6874C28_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_OfType_TisRuntimeObject_m6A9DF3D9E7A19FA293DE237F51E02D0BB8FCF616_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1__ctor_m9C0EC0A8947CAE0CD99B7FC847861454BE629EF6_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Active_m7B033B36BE6E1EBEA505238B59F315482A31CBB8_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddClass_m024380A53D91A5E74E24ED97267B79C3F844DDA6_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddClasses_mC48D45DA3DA18F0656C200817455FAB79FDA6D58_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddName_m05D0503DFB262662CD42472C45C0B29145CBA547_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddNegativePseudoState_mC04225C36F5118EB6E8C6B738390CA7C7EDA786D_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddPseudoState_m5BA527033C9E13ABA265EE6AAD0F7632FCE98139_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AddPseudoStatesRuleIfNecessasy_m71FB68969B671E698FBE074CBFAAEDD0556B2CF5_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_AtIndex_m701E0E6C6C6AC97693D234A643D8ADF695F78B17_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Build_m51E76A0E9FF868FC34DD84F7F6A4BC369D1A316B_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Checked_m9F542E56ABB9753C3E6EA405CE5DC8F0F8E31B65_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Class_m871FA9DA07723AFB1E4AD0ED23E6F50983ED5491_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_CurrentSelectorEmpty_m8A756E419D48BB5DA57FF3686CB960C31CD82A01_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Enabled_mEDE39D81F9C178C50A175CD92A04EBD103B8A6FB_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Equals_m810C3B33A1AC3150EE3C9F58323B18DE63E7BA12_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Equals_m4C3C3AD813E72BEE6EA42912562E5100411B08F0_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_FinishCurrentSelector_m500B300233E4DD7110D6B33EA18DA0C326A80CA6_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_FinishSelector_mAFA278FA0C1BE641CAFE110E6A76855BBC126054_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_First_m0913A218B87E7FCDE96F46F3B1DDD80302B2432E_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Focused_mCA50D395E834C466C071559DF4667572F82A9848_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_ForEach_mED54645051528F0BC37AAC896B57BEB7A9A19F06_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_GetHashCode_m662C33EBC9881854EE15E98428D555D2C28C369B_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Hovered_m3BE92A961925670F643966A733537CA7FE031A5C_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Last_mE32242A4967E36A2D53B60AE002A6C4AD9D96F60_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Name_mF930BD3AB84A45024126BE05E64B1C951A46F344_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_NotActive_mCA59134C857570B8FFD3CC6722913A72A72CD957_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_NotChecked_mFEDA9E61B5E8CA292622B8051182DF476FD7DEBD_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_NotEnabled_m84508AD4DC060D5EA2F9836EEB48601924A9DE02_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_NotFocused_mA34269D9F676316B87F9A840CA513AE0457C86CD_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_NotHovered_m62B21D7FE0EDB536CD22BD561E32C234669D5783_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_NotSelected_mFA120E7078BDF1168DC4748387E2E5777591B07F_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_NotVisible_mAC36CEB7E4558FE652DA7C07A7744631A234F86F_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Selected_m2ABFD315E96C4B65B79B6214C8AC5046D28286E8_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_SingleBaseType_m288935529BB5FA432732088C9DFFDA931371422E_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_ToList_m60AA6FCDA94BCA677C6C58C997CC018C52509995_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_ToList_m68596FEED660C9FF1D38DCCD32309E2F16435D37_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Visible_m654F5F934E08BCB72702B8266309D429A0E44F50_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_Where_m50F18AA063808BC67E6E326B438FD5A485E1C2EA_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_get_parts_m5ECC58E4F2CE734BC0F4597D48FD51A243B9294F_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryBuilder_1_get_styleSelectors_m1817BE5A446B8C9331E72A7206CFF86768F2A0EC_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_ForEach_TisIl2CppFullySharedGenericAny_m684F55EEFC1F037371B16320426DF824E623A0ED_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_ForEach_TisIl2CppFullySharedGenericAny_m9BFEDFED4193BE50F4B882A548479BC8363F6EF1_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1__ctor_m661A100C20F49D2E8A76BA57CE5774A562DC8FF8_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_AtIndex_m2E2E9CB4F071A500429DD094D5FBEC25FEE56531_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_Equals_m6824EF7324D356025924883F014E3AD22FAD72DA_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_Equals_mD35A6743162EA3D3D4FBEC55AFEBFF08B6EF0379_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_First_m19FF1885E9D1D57D0EBA715820CA3C02C2C9C363_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_ForEach_m2016E47C98379A010EE78D09CFD8DF7625894301_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_GetEnumerator_m78AA2D739336C3CBE72B4425D65C35F91853C367_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_GetHashCode_m20DCD93A49AEC50C2B816604897E7C298AAD8058_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_Last_m2B17175712632F4C3DE6A0581BC17B0182C76FE2_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_RebuildOn_mF29E43348045B1219A757EBBF43C892C32EEA5DC_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_Single_m96252EE5598F3DC4B72CE509362A0D7DFA20A40B_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m524A6E90BFB245E9643A8E23E28943DC0F8CAAAD_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_System_Collections_IEnumerable_GetEnumerator_m817650F16F99755331C119D6979227201B14F689_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_ToList_m908498BA91666931C25DD5A0A23AB194D0EA6560_AdjustorThunk ();
IL2CPP_EXTERN_C void UQueryState_1_ToList_m9E511725E503E0D1B049A63696362FB0087D8D3E_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCancelableAsyncEnumerable_1__ctor_mF3E1B424AE982C37B2AAE2B0168243E2B04B98B3_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCancelableAsyncEnumerable_1_GetAsyncEnumerator_m40B1BF925C2526B24BDDBFE1B2D146E32751A870_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_GetResult_m212E0DA923DC1D1803D087419F049DFD9DBE51F9_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_GetStatus_m25012764771D843D048C0547C4D99B5F32E29B35_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_OnCompleted_mABABCB75E06A39AF02AEC067E42C0AEE73BFA015_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_ReportUnhandledError_mCB553FC688C1666335721D1F09AA5C00298F8571_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_Reset_m788665B21E38E2A5451D5A30194E957469083C5C_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_TrySetCanceled_mEA4B7341003C6B1365405F43730371BF3D935E14_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_TrySetException_mF9AB6AE834A9D074A3E16E3C6B7E7821DA5936AB_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_TrySetResult_m7DA02F53706893C67B6747B3F95C9B82D0946D40_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_UnsafeGetStatus_mB6C5DA816F12A340957522E0BA82D26629CC8E44_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_ValidateToken_m0D278FBD064187C302FE1582CA68EEF557E7E3EE_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTaskCompletionSourceCore_1_get_Version_mC206FEA615DEB3676B72991ABAE79848523CAC0B_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTask_1__ctor_m0F12D2AF19A9F3F86DA3D09D780E58868C07EA89_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTask_1__ctor_m608BBDBA054799FF72D4ED7758DA9BA32EBB8F22_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTask_1_GetAwaiter_mEC480E8F8586214E6ECD880FBFAA9C8CB1C963ED_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTask_1_ToString_m829248BDF095544BC67FC8B91BA828DBFBBE4601_AdjustorThunk ();
IL2CPP_EXTERN_C void UniTask_1_get_Status_mA1326B91312FE816FF0720BF512F4C90E72B6CEA_AdjustorThunk ();
IL2CPP_EXTERN_C void UnmanagedArray_1_Dispose_mBAF9B42AE46BCDFC015F71B39AAA93C8065EDA22_AdjustorThunk ();
IL2CPP_EXTERN_C void UnmanagedArray_1_get_Item_m05D75737AB1F82959AD5DB58D5AF0E8F310F8481_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeHashMap_2_Dispose_mC5832831A098A0C1453F76414130051BD4551445_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeHashMap_2_System_Collections_Generic_IEnumerableU3CUnity_Collections_KVPairU3CTKeyU2CTValueU3EU3E_GetEnumerator_m213433CFC5779226609C25F9233C72EDF0F42F50_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeHashMap_2_System_Collections_IEnumerable_GetEnumerator_mA665362F1662B06D61798E982BB434109B4D08E1_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeHashMap_2_get_IsCreated_m829E792B858A2DAA2FE81CC44146A9189BB243F2_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_Dispose_TisIl2CppFullySharedGenericStruct_mE4A291BF549BB5ADA342FB04746C5E75D2851CC2_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_ResizeExact_TisIl2CppFullySharedGenericStruct_mCEE0EEBD50C687E281D02DA756FF1F08927B89CF_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_SetCapacity_TisIl2CppFullySharedGenericStruct_mE9376397B41232E820D706511C7A7D8A818EBE83_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1__ctor_m77340A75055228D3857F5DCDDE675BB31951957A_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_Add_m409DAD25A7C3746AA3B1224872A3E79020FF5711_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_AddNoResize_m537D4EB76C2A0A23494E32D37E044764DAB89F6F_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_AddRange_mC9AB6AD54ED7854723E3821BA1D3CCD1FBB8207C_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_AddRangeNoResize_m809794F219ECE3B47B9AC4E9714F90561223A0D9_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_AddRangeNoResize_m4B0C28517675234B04F6AF9E3D3AC53E1F7D2B2D_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_AddReplicate_mCD1778D61A491577966A18ADA7F3041D77A054F5_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_Clear_m720AD7943BF8DF9949FD0AD21A25E9EADD97DA7F_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_CopyFrom_m71D334AE652996B990635A7F4621CCA5DA993D94_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_CopyFrom_m0753F3589416C7223C92570AA2B9D458C10AAD7E_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_Dispose_m40FE65A111C7CF6FB979B4AA2DBB0EAF2881D734_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_ElementAt_mF9FA891787954F18D91AE55D96B428C168706731_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_InsertRangeWithBeginEnd_mFB76DC3407CA799EC64AE8F1BFE765A91D458998_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_RemoveAt_m62B4AE5FE18470F9F8DF5979C721230185D58DA3_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_RemoveAtSwapBack_mDAA38AE9EB799E4FE6C8144FC24F19CBE6527144_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_RemoveRange_m5502595ABECDC51DDFFD4B5A08B050C670FAF9D4_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_RemoveRangeSwapBack_m6100DA6FD1302DAA3A6C20420A4D357C8FD8A918_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_Resize_m1F99567F0B3EFCD21227B49E3BFF2858AD4082DB_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_ResizeExact_m285C4C95E502AED13C29541360D2EAF92C3D0EC7_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_SetCapacity_m1A26A7EC9C2610C1842C8FD2B3247CAB52BA3F3D_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mCB2C19A74F8BEDABFA4D327317EB968B261AD4A9_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_System_Collections_IEnumerable_GetEnumerator_mCAF80DF7CB4AF09DB010BBC3E142B258C700F281_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_TrimExcess_m9B0C5809F881B2178E4314E6F215EBC62DCE45C9_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_get_Capacity_m163371206E2F39354C1DF26C807700F2FDF08C8B_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_get_IsCreated_mAE8AA411D9B1BD097C854F0657BAC04053C27B7F_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_get_IsEmpty_mF548243ADB6DEC9F961B55925E75369B0EF94A5E_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_get_Item_mB847AC77ACE34F511CEABF3E45B6EAD5D59165AF_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_get_Length_m35C71DFABA31811E9ABCD2FF56F066B449E3C84A_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_set_Capacity_m55A593E215A735D7C440BFB62D75BE4C186E038B_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_set_Item_m61F9495A96E1D08A222DEB26C1FC24A279807D53_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeList_1_set_Length_mA84640FEE498E051FBB8C91DCDC4F718295BBA8A_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeQueue_1_Dispose_m6A73C25E9270945D386226FB4A5F7454889A85EC_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeQueue_1_get_IsCreated_m0B22F04A9ACF6226E60FD3F78970158670B242A0_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeRingQueue_1_Dispose_m72851D0FDFB87F46F84EEC0724A99A5354EE29CC_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeRingQueue_1_get_Capacity_m3BAD1C0DA1E53909608B3CD29385212722046C05_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeRingQueue_1_get_IsCreated_m7ECCEAA05D3ACBA98D7E9D229B1046523557672B_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeRingQueue_1_get_IsEmpty_mE7D7D3EF7B442FE9D948891ED40473A02B5FA104_AdjustorThunk ();
IL2CPP_EXTERN_C void UnsafeRingQueue_1_get_Length_mE7755222D1DF20FA47C59EDC6DDF0142A7A70E11_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTaskAwaiter_1__ctor_m7A5127CE080DF8BE37A788EFDE4C6EBED02FEA93_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTaskAwaiter_1_GetResult_m8973CE2E1FCAD6916DD9B366CE71B5078BC019AF_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTaskAwaiter_1_UnsafeOnCompleted_m80446D0C780C1396A45604E48D89DBE5BD3DA2AD_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTaskAwaiter_1_get_IsCompleted_mA84942EAB1D2CE8ACE5BFA8378E9ED7E1D8971EB_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1__ctor_m8346EDD752A97883147D3BC8685D3DA45F1CAD19_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1__ctor_mB12720B6617FD51550F36A5DDA1FB5B905F8931A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1__ctor_m6290E5FE79CCE71C6983D5D86933A41BFB4BDEE0_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1__ctor_mF8C25D7941B477ADE70916CF8B71E76E7D9895ED_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_AsTask_m7982AD4E459905FB552E2E531A534C2F828B56E5_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_ConfigureAwait_m699DD324BA535F744134CF26F542D6C9821511C7_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_Equals_mD5BF0BE72581594DA92F59B348593443F6DF6950_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_Equals_mABC4FB221AC4A671D4DFADEF992B569AD2C30A9D_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_GetAwaiter_mEA7FB0067672E57AAD8A0299D0B90B4C32914B50_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_GetHashCode_m8000CE1DEAB0FA01A6ECB8A08BE19213A970D73E_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_GetTaskForValueTaskSource_mEAEF30AE4C3EF71CCAED02D41A6B9D9E88A74915_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_ToString_m4F52C632CA8530BBCAD18F1CE53FAF04011A4436_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_get_IsCompleted_m6FC365DA579395B0B09E0A33A77AF97D29DDE383_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_get_IsCompletedSuccessfully_m951AC7136DB7D58F31DC79344D3C51412D4744DA_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTask_1_get_Result_m4C8284BCA2ADC4FF77D6594A1056775D523ACC82_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1__ctor_m538A52BFBDA47CC5C05AEE670CAE9D0657D425CB_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_CompareTo_mFE3D3F1BBCCB23FF42959D2990AD177231EB7586_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_Equals_m744EA4C6D7198DA2A95179DB68116437BBC453E7_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_Equals_mE31990E0876A75374263FD30DDB89A7D8B1E2D91_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_GetHashCode_m0C86BD1C67D66BCAEB44D777CE0885CE0EF76BA6_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_Collections_IStructuralComparable_CompareTo_m2D3994A0A1930ABFBC788A619697E6EDA6507AEE_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_Collections_IStructuralEquatable_Equals_m478C9A5F2E5423A54F1B0A1D451BD86F90DD4EEE_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_Collections_IStructuralEquatable_GetHashCode_mEF47D4B715325E8733A2797A2DC4D80939E18565_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_IComparable_CompareTo_m27BCDCCC04DBC7C71F96F97D759F4F8BDC3EB7E4_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_IValueTupleInternal_GetHashCode_m8826B801A5B79BA75CBB3632593EF99E034E61B7_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_IValueTupleInternal_ToStringEnd_mC9A89165D4A8F414E8FF4B40B30386AAE4544751_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_Runtime_CompilerServices_ITuple_get_Item_mFBB9DBCCE1C238E52DACA6E83CBD26FF082FEF92_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_System_Runtime_CompilerServices_ITuple_get_Length_mDC15DE3BB4C0C1F78ED4090E77EFD093761C0C49_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_1_ToString_m5D2A8965564621C5442CBAC724FAB374EB61379A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2__ctor_mCAE8E725F680FA6BE2C23B9686C9F6056BB7E5CD_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_CompareTo_mD4B10342200C73FAA0E7553433244862FFF316D7_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_Equals_mD903BBEBB3EDB6897C67C462F1A958D28E156085_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_Equals_m691065991CED657BB7925B16C9C654A09F3292DA_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_GetHashCode_m4BA7B2997C6450C829DC80A188E4DD22AE712A75_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_GetHashCodeCore_m67662E6D7F40D4FD382E0E75F15CEC0B7FDDB429_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_Collections_IStructuralComparable_CompareTo_mBE4DB6C73A3970373B450757CC80C03D318C4269_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_Collections_IStructuralEquatable_Equals_m4FC09D4CA21099E02F8DA2E9E2DAC07DADDE7596_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_Collections_IStructuralEquatable_GetHashCode_mFF86F83A0C3ECE4DBC8519A067043F2670CBAF0C_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_IComparable_CompareTo_mF8C76C36693FDDA83BE43D8D76A91386E69A9959_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_IValueTupleInternal_GetHashCode_m9CF6EB50E064F7FFE19AF23697500FAB1168DE2B_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_IValueTupleInternal_ToStringEnd_m00CF3AC7D704F0DB5D633B3D18B4CACEE08B8172_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_Runtime_CompilerServices_ITuple_get_Item_m6BCA5019059D019D9C934D031DA4B3CD5E553E78_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_System_Runtime_CompilerServices_ITuple_get_Length_m4228CA61B034F353ABC6547567B3D7F748B43ED8_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_2_ToString_m3A385F535CA53166311E733E7699676231CFD10F_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3__ctor_mAB800E7134D8E52646FD55A8B2979AC908028759_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_CompareTo_m4C3E08414A848EC10D70182B7471304542521377_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_Equals_m1716966B94A34CEFBA3FCB15F00A3DE34CF167B4_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_Equals_m31C947DE700D3842AA28795E512D50FE1E505820_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_GetHashCode_m895E8857672E5454D68082DD0A8752865D8E71FC_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_GetHashCodeCore_m33A8FA4704589007BFD95252E15C89CFDC3D7642_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_Collections_IStructuralComparable_CompareTo_m0EAD982A014687A521C863B33102049D6042380B_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_Collections_IStructuralEquatable_Equals_m3F5FEF37FD4F019408802635862595B7C9A633DB_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_Collections_IStructuralEquatable_GetHashCode_m23E4A5E0CD82CE6A0C844FBFAAC98A510810E98A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_IComparable_CompareTo_mFB7A9AF6B4FE4D8FAB3B464AC27C78B9B8CA8725_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_IValueTupleInternal_GetHashCode_mF4FA0E951A78739A0DA2416A08FFDE9388320AAA_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_IValueTupleInternal_ToStringEnd_mBA0C6359D2897615B5C51B341FB79F4F9C639F31_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_Runtime_CompilerServices_ITuple_get_Item_mFEC0531427CFC1773E3A8219D4237B359DE9F914_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_System_Runtime_CompilerServices_ITuple_get_Length_m5C7DEC7C8EBD2A0F0FDFA89009D316BBBD41FC5A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_3_ToString_m9995C56C122AEC521C9BF4AD9C00A1498AA26744_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4__ctor_mFF0D3A69E30DE69A5DB3A2559A8901F1E9851FCB_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_CompareTo_mFD48E2AA15064CAC797A24D6595473A41F755F23_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_Equals_mA6E7E6D2B61BCEE388C695E55241E3D23B136E92_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_Equals_mB24DD728F77D42F7BA367887EE8BCC1AEAC2B86E_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_GetHashCode_mD228930A54E53FF9E9F3370361B0D81F05BBB828_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_GetHashCodeCore_m5E8ECC19294F8DFC3FBEA99FFC32F0D41D5FBA77_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_Collections_IStructuralComparable_CompareTo_m8EFEB9D7B5790959B79D8E53DD14DB1A038C304C_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_Collections_IStructuralEquatable_Equals_m88F5EA4CCB9B7F414EB6FE747DCDBDC088459208_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_Collections_IStructuralEquatable_GetHashCode_mE44B3DB721B4562AA53675B76D454998D8883828_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_IComparable_CompareTo_m9B7539113D2B5D30818A1A60D51E010B7DBE1BD8_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_IValueTupleInternal_GetHashCode_m611C902045E4669D57C34E6609AB0626823B19CD_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_IValueTupleInternal_ToStringEnd_mC81C0F3DF892B61082B42A6CDE5A39B18F692814_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_Runtime_CompilerServices_ITuple_get_Item_m6A49A2D72011FF3ED0A14280087A6631F426B086_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_System_Runtime_CompilerServices_ITuple_get_Length_m10BCA13E3F340EE4467F6DD105E60646F0D563F9_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_4_ToString_m05CC5E764EFAAE3D2796762E7A3B9729474CF8D4_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5__ctor_m137533B18C66876071676469953787BAFAA5AEFF_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_CompareTo_mC808DB0CE9EDE1F4B2C4D7382F3E57105940208A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_Equals_m1BDE6D49AEE834527814FAEBE4534B414E1022A9_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_Equals_mB9CD17DAD4FABF6412558E3A82BB15FE28160D82_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_GetHashCode_m7B00692478B9218079F0364A02363A500A3202B0_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_GetHashCodeCore_mCAC2EA3BE3C748840BE08F742B272274BB7E171A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_Collections_IStructuralComparable_CompareTo_mF83475B1821A3AE012364E6215C27FFFF25A399E_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_Collections_IStructuralEquatable_Equals_m6220851574828DC96CE4C7E2D09213B84CA5C1FE_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_Collections_IStructuralEquatable_GetHashCode_mBA5888BE2DA308AEA1CCE2C0E702F5DF77975E7C_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_IComparable_CompareTo_mA31E7015CC7DCBB59BA58FC44A74FB3ED5BBEF8F_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_IValueTupleInternal_GetHashCode_m76348DC65A7A91FB4C0B2994EFA9ECFE288F7E4F_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_IValueTupleInternal_ToStringEnd_m6CEF8C4D0881F16FD3EB0677334CB013A03D2D6D_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_Runtime_CompilerServices_ITuple_get_Item_m8253E9D1002923342C60BBBFBF619F2943FAB5C1_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_System_Runtime_CompilerServices_ITuple_get_Length_m8B407CEF945B739D7AB2AB153EE9EBEDCAA67755_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_5_ToString_m7265BEA8B1A29B537886BB6BEE00D1A154F8D814_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8__ctor_m456CD331E19E16CE692E636499DF94A47061A7E9_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_CompareTo_m7BA5E274F0F612A33415A48FBBD329877F6CA1CC_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_Equals_m81A7442A1DCE82EB1C3D46334D42A88B21F92274_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_Equals_m6E9EB22F4F990C711D38C5929488A47E32204C5A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_GetHashCode_mFFB8FC3075672FDEC47412793447ACDA1DB90FC1_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_GetHashCodeCore_mD6AC6CAEA9F3BB1951814E084AC3E8C6C803577A_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_Collections_IStructuralComparable_CompareTo_mDA0F007332659CDA6ED3E2156DE347F91EB9EBB9_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_Collections_IStructuralEquatable_Equals_m8EC26DEDFFB4B136ADB2AF17FA7E9395B7E948A0_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_Collections_IStructuralEquatable_GetHashCode_mBBB3278470E2465A6B3F9EBB77231B7749837D84_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_IComparable_CompareTo_m876067CC3338F81C90DFD2DD51CF451CDB5DDDC1_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_IValueTupleInternal_GetHashCode_mEDB78B450BFD722ED3A333CBBF3CBE862F445EDE_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_IValueTupleInternal_ToStringEnd_m5413C9CBCCC7063A9FEADA1E0B07CB2E522B708D_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_Runtime_CompilerServices_ITuple_get_Item_mFD1FF614F4F4505046AEAE30835537989E5DB09E_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_System_Runtime_CompilerServices_ITuple_get_Length_mCEC9AD927A3B4A0BF8150A2FC47A62375090D949_AdjustorThunk ();
IL2CPP_EXTERN_C void ValueTuple_8_ToString_mCBC8EEE6DF5982B972AEC0CAF758197B2CDE7E3E_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1__ctor_m7540979061ABB2A0A6D57213359B3E83A1B7B4D2_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1__ctor_mF674294C34FEB07FB44E694358BDA181F564B674_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1__ctor_mD3ECCFAE19D970AAEFCC15057E4B90EEA62EB001_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1__ctor_mAD567DC632B01A95C6F418F1C6265F295D1EB0A9_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1_Equals_mBC248D1BFF0E4CEA6585C0F10707B1843A4502E8_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1_Equals_m1D6500596F9B825F39D474A32D7A6D5E14E22B8F_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1_GetHashCode_m3ACEDE87318824BACAD7E8CB760798D98D37C4FF_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1_ToString_m212191DE0383E512D8297CE138DB5C06BF1E6F4A_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1_ToString_mB21C58D9FD6C9A89DF0D786C50F960E551B1FB8F_AdjustorThunk ();
IL2CPP_EXTERN_C void Vector_1_get_Item_m301D7183D2063DC97710DCD2CEF970E7A67EB21C_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_1__ctor_m31354CF5ADC3BDA59751A23406B0FDDADB4BC0AE_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_1_ContinueVisitation_m800888319D5A63406E05D0F38ADD3C59EF0C732A_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_1_ContinueVisitationWithoutAdapters_m6FE4A77169F00A1DC03CED539FA9596553EB62E5_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_1_get_Property_m1B164AC870CDFE66DB4D46F5C68A337E5869E8B6_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_2__ctor_mB7536685F417398FD93AB7F0C7EEE1EB8C2CD822_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_2_ContinueVisitation_m368A808DC687C81FE2EF255F3E4ABD91B87047DE_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_2_ContinueVisitationWithoutAdapters_m52B8B80829612CB1C70331D9950C5431D60E6CBA_AdjustorThunk ();
IL2CPP_EXTERN_C void VisitContext_2_get_Property_m98E03E078D18616C47421049904506BD890E556E_AdjustorThunk ();
IL2CPP_EXTERN_C void fsOption_1__ctor_m343A7CF35E302DB9E5BCA27A0DAC03751B238516_AdjustorThunk ();
IL2CPP_EXTERN_C void fsOption_1_get_HasValue_m2E9021BB4CB6156A14918A5B865D21751CA6D00C_AdjustorThunk ();
IL2CPP_EXTERN_C void fsOption_1_get_IsEmpty_m8F02E0608720E562CDB4C8906C1CD863FD8FE767_AdjustorThunk ();
IL2CPP_EXTERN_C void fsOption_1_get_Value_m2A0C3FDC3098EA4DC3BB5CF398E95EC2C991988A_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationScriptPlayable_CheckJobTypeValidity_TisIl2CppFullySharedGenericAny_m33EAA66CE1A7CA0786F87FB47468385A87873115_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationScriptPlayable_GetJobData_TisIl2CppFullySharedGenericStruct_m26F80B7CCA451FFF9BE6437FA5E5EB673A55D6EB_AdjustorThunk ();
IL2CPP_EXTERN_C void AnimationScriptPlayable_SetJobData_TisIl2CppFullySharedGenericStruct_m0875800AE8D572C36F6FD69BD08657826F883C75_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncGPUReadbackRequest_GetData_TisIl2CppFullySharedGenericStruct_m785CA6B68B6527258558130B5F81B345AE654EF0_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mAFEAD0E0483FFA3D862272177C9DE55F2C943F8F_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncTaskMethodBuilder_Start_TisIl2CppFullySharedGenericAny_m36201A5D380AC483FAE5E144880EEE2EB09D69E2_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mD6CD2CEDAD85EBF9E210B5527441061D410057AA_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskMethodBuilder_Start_TisIl2CppFullySharedGenericAny_mA13C3EA048D166590DA2E340CD6FC2B3FC3DCA85_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskVoidMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mF3C1A86A14D011BBE119A6E750C6BEEEC816CC9E_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncUniTaskVoidMethodBuilder_Start_TisIl2CppFullySharedGenericAny_mF35DDB7830BC9CB0FC86DDCF30A3585A6D79B997_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m03130C9822AF7F0C435E5DAF03F245D2F35FC6EE_AdjustorThunk ();
IL2CPP_EXTERN_C void AsyncVoidMethodBuilder_Start_TisIl2CppFullySharedGenericAny_mF2CE0E0265F13C43AAB330B33509540A399C62A2_AdjustorThunk ();
IL2CPP_EXTERN_C void CullingResults_GetNativeArray_TisIl2CppFullySharedGenericStruct_mAD95E79FFE908C81E8C8514A3F2E8E583998A7C0_AdjustorThunk ();
IL2CPP_EXTERN_C void Hash128_Append_TisIl2CppFullySharedGenericStruct_mE148C85A9415FF4819E7137E15652770296D8B8C_AdjustorThunk ();
IL2CPP_EXTERN_C void Hash128_Append_TisIl2CppFullySharedGenericStruct_m1DF2B7F4C2182E42BB9D482C6BD9B621AD96A37F_AdjustorThunk ();
IL2CPP_EXTERN_C void Hash128_Append_TisIl2CppFullySharedGenericStruct_m7EBA779E6E080D089CBFCA2DC8316E4C972E42AB_AdjustorThunk ();
IL2CPP_EXTERN_C void Hash128_Append_TisIl2CppFullySharedGenericStruct_m5E19D655EC03ED8A908594E0D4E927DAF40E4C0C_AdjustorThunk ();
IL2CPP_EXTERN_C void Hash128_Append_TisIl2CppFullySharedGenericStruct_mAD033BD30A77E9C834A51A6A0CCB0DE51CB89F93_AdjustorThunk ();
IL2CPP_EXTERN_C void Hash128_Append_TisIl2CppFullySharedGenericStruct_m94DE3968A172E67D1ABE6794E3DF614B5A482DB9_AdjustorThunk ();
IL2CPP_EXTERN_C void Hash128_Append_TisIl2CppFullySharedGenericStruct_mE1E8828A3459E86FF79BFA283170EED36F3C3162_AdjustorThunk ();
IL2CPP_EXTERN_C void HashCode_Add_TisIl2CppFullySharedGenericAny_m7D286F70F6ABEBACBB74E3FD2CEA60626F908ADB_AdjustorThunk ();
IL2CPP_EXTERN_C void InputDevice_CheckValidAndSetDefault_TisIl2CppFullySharedGenericAny_mEF020C615D84C1731C46461C60100807B353E47C_AdjustorThunk ();
IL2CPP_EXTERN_C void InputFeatureUsage_As_TisIl2CppFullySharedGenericAny_m6D8205335AA616E9D9B73F04B1B32AB08F728FF9_AdjustorThunk ();
IL2CPP_EXTERN_C void ParticleSystemJobData_CreateNativeArray_TisIl2CppFullySharedGenericStruct_m999DC724C60DE8B74BC368867979AFEEC4C48F64_AdjustorThunk ();
IL2CPP_EXTERN_C void Playable_IsPlayableOfType_TisIl2CppFullySharedGenericStruct_m295C4DE8654EB520ABA0C31EAF1E01D89A770DDB_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableGraph_Connect_TisIl2CppFullySharedGenericStruct_TisIl2CppFullySharedGenericStruct_mE6755FEF0B1318150CE9FA535DD165037E08CE3A_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableGraph_DestroyOutput_TisIl2CppFullySharedGenericStruct_m0E033AC1F2C22345EB7A36527B30D8825B7821E4_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableGraph_DestroyPlayable_TisIl2CppFullySharedGenericStruct_mD2E0791BF597E588C8FFA67DF593EB718CC8B195_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableGraph_DestroySubgraph_TisIl2CppFullySharedGenericStruct_mA15F58973E26BEDA428AFDA16EF472C4348199FD_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableGraph_Disconnect_TisIl2CppFullySharedGenericStruct_m020954D570D3838C680F7D851422EE302DA69D25_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableGraph_GetOutputByType_TisIl2CppFullySharedGenericStruct_m23BFFD6D32C7C3B0626F2C33BAB229EC9E4A800E_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableGraph_GetOutputCountByType_TisIl2CppFullySharedGenericStruct_m309F7343AEE30B430C5E6E92B8B1A5DDA6DFA4AD_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableHandle_GetObject_TisRuntimeObject_m8DB359D6799D32A54972E5FC49BB8DBD5F74A6A4_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableHandle_IsPlayableOfType_TisIl2CppFullySharedGenericAny_mA4E158678160F1D9713643D243397452502EE932_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableOutput_IsPlayableOutputOfType_TisIl2CppFullySharedGenericStruct_mEA1ADAC81B0F78FC2EA6AC46EA0AF1FB5C128BDD_AdjustorThunk ();
IL2CPP_EXTERN_C void PlayableOutputHandle_IsPlayableOutputOfType_TisIl2CppFullySharedGenericAny_mAF0114D0A8F1D86A1248F52E4D85173FA4C20F03_AdjustorThunk ();
IL2CPP_EXTERN_C void MeshData_CopyAttributeInto_TisIl2CppFullySharedGenericStruct_mBC51B1613719DE91F3CE9994EE6D15059DE32469_AdjustorThunk ();
IL2CPP_EXTERN_C void MeshData_GetIndexData_TisIl2CppFullySharedGenericStruct_mB6FA280A6F231D6988DA8F708CEC9B806B181E3E_AdjustorThunk ();
IL2CPP_EXTERN_C void MeshData_GetVertexData_TisIl2CppFullySharedGenericStruct_mCB44203C07C04A028F3DF24B278D2AAFF94469A4_AdjustorThunk ();
IL2CPP_EXTERN_C const Il2CppMethodPointer g_Il2CppGenericAdjustorThunks[];
const Il2CppMethodPointer g_Il2CppGenericAdjustorThunks[1049] = 
{
	U3CRunWithTimeoutWorkerU3Ed__241_1_MoveNext_m5611BDCED6E1FE6014D81219434F3A90D681FF0E_AdjustorThunk,
	U3CRunWithTimeoutWorkerU3Ed__241_1_SetStateMachine_m69A9C0162383431ABA5421204CD78BFB43A16761_AdjustorThunk,
	U3CWaitForCompletionU3Ed__15_MoveNext_m0627E58E33A26A6238717A4FE1C333BDDC45B39D_AdjustorThunk,
	U3CWaitForCompletionU3Ed__15_SetStateMachine_m193015C8A9A30959F8709F36131ED5F6119D8B2F_AdjustorThunk,
	AllKeyFramesStruct_1__ctor_m064E86758535987228E5742AB227195D5E1EDAC2_AdjustorThunk,
	AnimationDataSet_2_Add_mDD8FDB2E901CE9DF375C393C6EB1E1A94D68AC10_AdjustorThunk,
	AnimationDataSet_2_GetActivePropertiesForElement_mA378BCE8A10910B60FD3417B89FA48871613C86D_AdjustorThunk,
	AnimationDataSet_2_IndexOf_m39B27A0CD7E79387931C2E717D46F83956674874_AdjustorThunk,
	AnimationDataSet_2_LocalInit_mB05E91215A20813B3CEE5F24136B0E4575497BBB_AdjustorThunk,
	AnimationDataSet_2_Remove_mE3FF1BDFE9FAE731CB39755EA7EFCD334C81FC0E_AdjustorThunk,
	AnimationDataSet_2_RemoveAll_m5B6C0383FC4BD596AD65C8F509A928F84C61A69D_AdjustorThunk,
	AnimationDataSet_2_RemoveAll_m6E80B23A0CC786FD422734760343379434C6B818_AdjustorThunk,
	AnimationDataSet_2_Replace_mFEA4D840D4A70B11F713A987AC677F5CDEED3DE2_AdjustorThunk,
	AnimationDataSet_2_get_capacity_m02CE1174F074197B274146A1558994F98A1E4BE0_AdjustorThunk,
	AnimationDataSet_2_set_capacity_m472F441C6D753CC3C3B04E0A0A6A27F8F3EB2C8F_AdjustorThunk,
	Array32768_1_ElementAt_m82701E882669CA0FE1E0D5144F2FC0AF3AE96509_AdjustorThunk,
	Array32768_1_get_Length_m0D08A19FC0206C255A5F4D7C4DB9CBF4CA4BE46A_AdjustorThunk,
	ArrayBuilder_1__ctor_m1E5BCB2E003E602A6B469C66A2C3CD1ECDC7E143_AdjustorThunk,
	ArrayBuilder_1_ToArray_mB9A5257137C2C2A30D57A230D2391DED66DE6C35_AdjustorThunk,
	ArrayBuilder_1_UncheckedAdd_m264C61076357A116E3F82CACF14EBF9D18CD7671_AdjustorThunk,
	ArrayBuilder_1_Add_m1AC3F4BDD806CECCB6C29AD0253266FDF39F0065_AdjustorThunk,
	ArrayBuilder_1_EnsureCapacity_m6BA8EE4C3649B8CFBFA4E193BE6929CBD6F682FF_AdjustorThunk,
	ArrayBuilder_1_UncheckedAdd_m75F6AD906ED1EE176D2DDDD367B1F6DEC97C697E_AdjustorThunk,
	ArrayBuilder_1_get_Capacity_m503F745B099A44EB7E468555D74CF17E43C5BCD1_AdjustorThunk,
	ArrayBuilder_1_get_Count_m587CBF5DB2467A88A5DA669AC70AF8422765B660_AdjustorThunk,
	ArrayBuilder_1_get_Item_mA86DA9F82A4B5149814D1C0B1F62557F92757407_AdjustorThunk,
	ArrayOfArrays_1_BlockIndexOfElement_mDF48A76387683347BEF0BF210E94CF3D45B44A88_AdjustorThunk,
	ArrayOfArrays_1_Clear_m7783304A5FA845DC3E8C4AA9E2E1EF3409A203FB_AdjustorThunk,
	ArrayOfArrays_1_Dispose_m43AFAA25B3A81D3D03819B426149571B5F3FD13E_AdjustorThunk,
	ArrayOfArrays_1_LockfreeAdd_m1CF92F5C153F9C0F40F9A73874E88BA431D39A91_AdjustorThunk,
	ArrayOfArrays_1_RemoveAtSwapBack_mA2F7915A9CB86DFA61CCB826D66765CC137A2398_AdjustorThunk,
	ArrayOfArrays_1_Rewind_m01A338B49AE90835038F585E2C60E676A1641B29_AdjustorThunk,
	ArrayOfArrays_1_TrimExcess_m194AB512AA77539A26BC45263B6B9E7FA8A57E44_AdjustorThunk,
	ArrayOfArrays_1_get_BlockMask_mFA7725CE0B6B1544940BBC60055CA73AC1A93137_AdjustorThunk,
	ArrayOfArrays_1_get_BlockSizeInBytes_mB60D7E599E55B623ED557696F63C1876AC82FAF9_AdjustorThunk,
	ArrayOfArrays_1_get_BlockSizeInElements_m5F95A30F1370257157354057B9F4F39B07236990_AdjustorThunk,
	ArrayOfArrays_1_get_Item_mF3A73278BF4C9E08279BBD4B483CE5FB89DCCB9D_AdjustorThunk,
	ArrayOfArrays_1_get_Length_m108701CB27FAF60BA83B76435904C3310B7D6953_AdjustorThunk,
	ArraySegment_1__ctor_mD451D40E98D059A4FAC27FDB626B23D1ED00E1B5_AdjustorThunk,
	ArraySegment_1__ctor_m3A1337B17EA471D2D1538CB43ADB3B91267F978F_AdjustorThunk,
	ArraySegment_1_CopyTo_mDF1836BAF82385762BD2AD0F2FEBF2B9A1078DC1_AdjustorThunk,
	ArraySegment_1_Equals_m56E653D8CF357BAF5CCAE99EAADF890F11E23963_AdjustorThunk,
	ArraySegment_1_Equals_m47C8DD34DCC5A719882AFA64CA689F2245FD0748_AdjustorThunk,
	ArraySegment_1_GetEnumerator_mC6A812AD66B70E1CABA6B8F9734E97583E5A615A_AdjustorThunk,
	ArraySegment_1_GetHashCode_mD8CE9832FC50FCB215D2D58996761AD89434257A_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Add_m999A2F57CED17EF50C3CAE4109B556F69AB53950_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Clear_m521BD5A96D0269A31C7E1DE2C3FDC94912844ABA_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Contains_m7AA613C65175728F164ED7425E4D3DD455B6E034_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_Remove_m71971789EFA719C172557880FC16130505B1053D_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_ICollectionU3CTU3E_get_IsReadOnly_m3191B5D6CCE2AFBEBBC2D8228422F3C8ED6CD548_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mB561C2A02ACC0A836BBB742A4999D2FD3C237DE4_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_IListU3CTU3E_IndexOf_mD2BF33101E37C8A326016EC64A2953785E4CFE03_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_IListU3CTU3E_Insert_m5CCD11CD54587DD8FC897FDAFBD36AF0814672C8_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_IListU3CTU3E_RemoveAt_m291C118005848F3A89144B11AF496B20D4E20284_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_IListU3CTU3E_get_Item_m4F262D3148E3C9620F09B1EB0D37263FE444690D_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_IListU3CTU3E_set_Item_mAB40F2A9A0B64D87ECF766A8543F78518333DD29_AdjustorThunk,
	ArraySegment_1_System_Collections_Generic_IReadOnlyListU3CTU3E_get_Item_mB7BC1E3625F87D7FC1BDD09FB734CC9FB6EB6F97_AdjustorThunk,
	ArraySegment_1_System_Collections_IEnumerable_GetEnumerator_mB362F70B4869017E3811F46DBCCB1822E4611617_AdjustorThunk,
	ArraySegment_1_ThrowInvalidOperationIfDefault_m22F7F852A7B6751E4D9670504414A579241554A8_AdjustorThunk,
	ArraySegment_1_get_Array_mE4A35DFD81ABF447350B9A05C0F4BF0248A3CFF2_AdjustorThunk,
	ArraySegment_1_get_Count_mEF96248898C14F3687A5C72B920F841701E4E13C_AdjustorThunk,
	ArraySegment_1_get_Offset_m4FAFF061AB36BF278BA8DC37B6D13718A06301E6_AdjustorThunk,
	AsyncTaskMethodBuilder_1_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m0B984271E33D944D547FB29917733016C411E5EB_AdjustorThunk,
	AsyncTaskMethodBuilder_1_Start_TisIl2CppFullySharedGenericAny_m81177143E3D9118AF316E4C8E5D2AB2BF16C4E80_AdjustorThunk,
	AsyncTaskMethodBuilder_1_SetException_m25948BB842FBF253D89FE8399CCB2325B491EE34_AdjustorThunk,
	AsyncTaskMethodBuilder_1_SetResult_mC5A4FB0746878FC882C792D8BCAF5277E1F24778_AdjustorThunk,
	AsyncTaskMethodBuilder_1_SetResult_m86D5196DCF794B39FEB8D92C2BDB3C20421249F4_AdjustorThunk,
	AsyncTaskMethodBuilder_1_SetStateMachine_m7EF377C3A25FB4388DAF14ECAFC18E1C9F3EEA4A_AdjustorThunk,
	AsyncTaskMethodBuilder_1_get_Task_m90B072626CA4BF0F567616D4A035739B97F46D8B_AdjustorThunk,
	AsyncUniTaskMethodBuilder_1_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mB581027AD2DAA9354D87721008929F4DDFAC471E_AdjustorThunk,
	AsyncUniTaskMethodBuilder_1_Start_TisIl2CppFullySharedGenericAny_mA4B2E5CD3C0F6E8CB3612B0BE99533FC821970FC_AdjustorThunk,
	AsyncUniTaskMethodBuilder_1_SetException_m1812AF0E769F5A0E532DDEF8FE0921CA78F12A7D_AdjustorThunk,
	AsyncUniTaskMethodBuilder_1_SetResult_mF7DA80E1E8E4F9D51929B6BC5DC7B7B11F889339_AdjustorThunk,
	AsyncUniTaskMethodBuilder_1_get_Task_m8B0444E14A015F72613FA9939E443CD7F0FCBB76_AdjustorThunk,
	AsyncValueTaskMethodBuilder_1_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m268E5FBC3DCEF0470D41F9C2584E6B13F9BD7B8D_AdjustorThunk,
	AsyncValueTaskMethodBuilder_1_Start_TisIl2CppFullySharedGenericAny_m85A9FFB7131400A58050438B2B0A7CD7F043738A_AdjustorThunk,
	AsyncValueTaskMethodBuilder_1_SetException_m4FEA96E8C41844A34A2339C9D0B905E7B5007CD5_AdjustorThunk,
	AsyncValueTaskMethodBuilder_1_SetResult_mC029EDB24659DB11EA83AAFF46BD5F9400D5C9BD_AdjustorThunk,
	AsyncValueTaskMethodBuilder_1_SetStateMachine_m3903212517D60016C9BCED8BA377C3F1CD71A110_AdjustorThunk,
	AsyncValueTaskMethodBuilder_1_get_Task_mDBD70CC608F07003B473CF5F8C8A6FE86583C229_AdjustorThunk,
	Awaiter__ctor_m75F3F14568F9463778CE7429585D539E87F226F3_AdjustorThunk,
	Awaiter_GetResult_mFF0CF33768E54ACB5D4D4F47A4A6FE3DB29EE6D8_AdjustorThunk,
	Awaiter_SourceOnCompleted_m7BB8B4D6B70376483D828F7D3BEE03C2C33602E0_AdjustorThunk,
	Awaiter_UnsafeOnCompleted_m9BA083B1ED6E4385066B9A863135A587D4260E32_AdjustorThunk,
	Awaiter_get_IsCompleted_m22B365903CC1BE835CA1126FE3BD3E71156DDFA8_AdjustorThunk,
	BatchQueryJob_2__ctor_m52B5B1BDEE537675680BDEFA6BF9D38FC31DA09B_AdjustorThunk,
	Buffer_1__ctor_m55137EEF7AF9E0883A438776A066DA5520FFDE49_AdjustorThunk,
	Buffer_1_ToArray_mFA9AEA7D664154CB0695FD1ED98B3C9539146194_AdjustorThunk,
	ConfiguredTaskAwaitable_1__ctor_mCF681CB4825E085E3ED42B9E990609C36F282536_AdjustorThunk,
	ConfiguredTaskAwaitable_1_GetAwaiter_mA5D8A0E225B9D580F1FC5216C47A0B828B033390_AdjustorThunk,
	ConfiguredTaskAwaiter__ctor_m5B53A410AE8900B3D565ED7C7FE9DAB92B2ECEC9_AdjustorThunk,
	ConfiguredTaskAwaiter_GetResult_m14D32632322F465B16F5EB858BFE5886B7217701_AdjustorThunk,
	ConfiguredTaskAwaiter_UnsafeOnCompleted_m2EFFC8EFEDD85479876580D8FDEF045B2DCA2D66_AdjustorThunk,
	ConfiguredTaskAwaiter_get_IsCompleted_mAEF99891A1B576254827D55C54C6E0E1787AA7EF_AdjustorThunk,
	ConfiguredValueTaskAwaitable_1__ctor_m50F71CECBEA21581E8170F4CDFE15E0182FE41D0_AdjustorThunk,
	ConfiguredValueTaskAwaitable_1_GetAwaiter_m44A568FC09F949B84CD6F0F09E907A2A04E2EF46_AdjustorThunk,
	ConfiguredValueTaskAwaiter__ctor_m3DE02ADEF13DE18D55B4E303EBB2BA001A30DE09_AdjustorThunk,
	ConfiguredValueTaskAwaiter_GetResult_mCF267B69492EBE1F642327D1A858EF350DEBC19E_AdjustorThunk,
	ConfiguredValueTaskAwaiter_UnsafeOnCompleted_m562052388D99EC0C34D5412AE114E2DF90358BFA_AdjustorThunk,
	ConfiguredValueTaskAwaiter_get_IsCompleted_m900B802370ACB3E11F808026A4BEE1183E91507A_AdjustorThunk,
	ContentHeightCacheInfo__ctor_mF2BB6598B1FEDCE079C61DBE32A2CCA1000648F2_AdjustorThunk,
	CustomStyleProperty_1__ctor_m51F05AE8977374556253B23F63CF84C76FB546AA_AdjustorThunk,
	CustomStyleProperty_1_Equals_m7BBDFACAA54A231C503EA95081A02CB77E8F2F02_AdjustorThunk,
	CustomStyleProperty_1_Equals_m23B71AC52884B6CBB0E91A337DC513D835679203_AdjustorThunk,
	CustomStyleProperty_1_GetHashCode_m728F3D620894896FF88E08CECC1BC9BC2E9BDAA0_AdjustorThunk,
	CustomStyleProperty_1_get_name_m0854CB7E93086BB018F00956EE37E700057D1DEB_AdjustorThunk,
	CustomStyleProperty_1_set_name_m799831D93960394105AEC1C47B11B81C08A46861_AdjustorThunk,
	DictionaryEnumerator_2__ctor_mB2404C7ABD77FE384388890F4B7EDB74070EEE25_AdjustorThunk,
	DictionaryEnumerator_2_MoveNext_mD8A7D1B941AEB7ADA1091F3ABA9FEADC16A1A0C8_AdjustorThunk,
	DictionaryEnumerator_2_Reset_mF8330A60A65FFFED2A9EA40BC9E3FE926CC8AEED_AdjustorThunk,
	DictionaryEnumerator_2_get_Current_mB2264B3813E71A58982A4A62939586C97BC9AFA5_AdjustorThunk,
	DictionaryEnumerator_2_get_Entry_mA9FCA0DD088F8A2D58E769612B91FE7EDD45D4C2_AdjustorThunk,
	DictionaryEnumerator_2_get_Key_mB1B3E656D88C36BB892A57A4858B92C75467A8F1_AdjustorThunk,
	DictionaryEnumerator_2_get_Value_m1833BB99169E7A10A1B94B82ADD64E9F2ED64933_AdjustorThunk,
	Enumerable__ctor_m9088B0F646E8EF80BCDA3B11D884763A9A817E61_AdjustorThunk,
	Enumerable_System_Collections_Generic_IEnumerableU3CUnity_Properties_IPropertyU3CTDictionaryU3EU3E_GetEnumerator_m3A72329B599B83EC044C68832928DC458ACC6501_AdjustorThunk,
	Enumerable_System_Collections_IEnumerable_GetEnumerator_m22D9023302E21EDDA6EDAB5AB7C40055EF33EBF4_AdjustorThunk,
	Enumerator__ctor_mD912767F3C3AB1A573EE48AE27D5952C2B610660_AdjustorThunk,
	Enumerator_Dispose_m80DC6195B05476661E3CD158706F318E49FDD6CC_AdjustorThunk,
	Enumerator_MoveNext_mCF34F788777D451B9DE38CF61C0A084532530E85_AdjustorThunk,
	Enumerator_Reset_m27A83D13EBEF784BDDC1B8DA1F5B6B87B0CE44DE_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mC57BA17257480558B7FFF0DC4035F61328A85905_AdjustorThunk,
	Enumerator_get_Current_m3E97569F59CC48BA1B86206FE02174721868B215_AdjustorThunk,
	Enumerator__ctor_m54EAA0B597A1197DBC10AF73A3C423B5F8046A75_AdjustorThunk,
	Enumerator_Dispose_m97F29D6F80B478A97A6DA81F665E7DA693E17E64_AdjustorThunk,
	Enumerator_MoveNext_mC9099E56E01AB33169AB4EE888BD9A2B752961DE_AdjustorThunk,
	Enumerator_Reset_mB15834348E10F7612876D2AE774493AE7AF604B2_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m81767706F1E3B41EAB92F21B1669693FFE92ACCB_AdjustorThunk,
	Enumerator_get_Current_mF9A198DACCE8ED0DC6FD097825D7A0F82D149DBA_AdjustorThunk,
	Enumerator__ctor_mDE93458BF02169A52FC3A449C9F0B500A0B25958_AdjustorThunk,
	Enumerator_Dispose_m0BBB67CF7366B5A989FBB12AA2293CD18853C98D_AdjustorThunk,
	Enumerator_MoveNext_m0DEF4AFE20672D3425DCFB0B4B7EBBAA7B69F595_AdjustorThunk,
	Enumerator_Reset_m7DFE6628F82F51C868E30AF059855DEBF7ABE53A_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mEB8088957B367DEEE33935D4174B19579BF14B58_AdjustorThunk,
	Enumerator_get_Current_mD5F9A8DA4A689D3BE78549843241C5C6D4936D84_AdjustorThunk,
	Enumerator__ctor_m7E124C7D7F367A3EC2AE25CA5F4D1284F5FA2E43_AdjustorThunk,
	Enumerator_Dispose_mCB9B6D15D244D49204D2A903402C1D5011CC2830_AdjustorThunk,
	Enumerator_MoveNext_m9589C320A0B345A9190284C38FCB97F2015861DA_AdjustorThunk,
	Enumerator_Reset_m43C21832D8660DF1F98FA2733CE9D752ACB020D5_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m9E9FBC7AD882EDFB5FDCF6024ECA5DA4B2DD24DA_AdjustorThunk,
	Enumerator_get_Current_m44CEFC7935FF87861A589ACDB1024033E52A123E_AdjustorThunk,
	Enumerator__ctor_m1E2A074F9FBFEF05AD1CA9A1E05FA4CBBD85FA5E_AdjustorThunk,
	Enumerator_Dispose_m71A32736176290409FABCA7026F119BB668173B7_AdjustorThunk,
	Enumerator_MoveNext_m0FFB94B57BE7FB5AB94F53DBC90E6F067BC5E2A6_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m5DD5A0CAD9CA8A1FEACC3479F140A987AF8BADC2_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m2C2D45C464CAD806710B057958AC5DFED13D059F_AdjustorThunk,
	Enumerator_get_Current_m290C2CCA066988F3CF229AA769C67BE5E36AB510_AdjustorThunk,
	Enumerator__ctor_m62B32E8D2F70569824269DDC82F384BDBC8662F5_AdjustorThunk,
	Enumerator_Dispose_m915D35B9BB2A77C5396F4FE1A1C53F1C93B8E337_AdjustorThunk,
	Enumerator_MoveNext_m1756F06B6BC460B8EEB4522B562E097F37D54C59_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m27D5470C040D50CC0200DDAB3C93DF3C4CA755B3_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m7D04FEF0F3E60E6E691D979806634E034498134E_AdjustorThunk,
	Enumerator_get_Current_m51282E47C90E55BF2D96A0D329869ABC122A85A0_AdjustorThunk,
	Enumerator__ctor_m0F98614729A5017594BCB5D1D847C1D319913311_AdjustorThunk,
	Enumerator__ctor_m37AB9FF23CB9A754FCD2CA40D0B482F61F9D6531_AdjustorThunk,
	Enumerator_Dispose_m25395D42D3EE1E89A0B024E2FAF823E4A3FEF4D1_AdjustorThunk,
	Enumerator_MoveNext_m4FB57970CC4FBEE295E533DE12F0B21FF7E13AAA_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_mEE0F6DC54C3238FF39CDFF5675B11CD97C544897_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m0F26F0C362DA633022A66F6C342673D39AD4622B_AdjustorThunk,
	Enumerator_System_Runtime_Serialization_IDeserializationCallback_OnDeserialization_m3675ECA18E1A14A4318723D0B93024692A40759B_AdjustorThunk,
	Enumerator_System_Runtime_Serialization_ISerializable_GetObjectData_m200D1982786B356E5B42E63B71C05AC7B3EA337E_AdjustorThunk,
	Enumerator_get_Current_m5BA2E5A6E9016D327B7C3067D3D2B2016D37DFDA_AdjustorThunk,
	Enumerator__ctor_m5C66A91DC68887BE203985B56EF2F74222E9AF50_AdjustorThunk,
	Enumerator_Dispose_mFE1EBE6F6425283FEAEAE7C79D02CDE4F9D367E8_AdjustorThunk,
	Enumerator_MoveNext_m8D8E5E878AF0A88A535AB1AB5BA4F23E151A678A_AdjustorThunk,
	Enumerator_MoveNextRare_mF1A6D7920FDC3C83A7DA1F838C2FC51D716637D9_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_mFA2701BBDA14ED6CC3113A32ED0E8B80DAD58A84_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m3FCE22D15C040EE9F8614C711F786205BA6AAFB6_AdjustorThunk,
	Enumerator_get_Current_m8B42D4B2DE853B9D11B997120CD0228D4780E394_AdjustorThunk,
	Enumerator__ctor_m6C59AA282C548B36E982B84CACC3A8E4AA904775_AdjustorThunk,
	Enumerator_Dispose_mDE5B605B7794A192CC35F12013300AE26EB8FE5F_AdjustorThunk,
	Enumerator_MoveNext_m2F9CDD8406C6495182E05B55A00C3D0C9620CA67_AdjustorThunk,
	Enumerator_MoveNextRare_mA2A9C4EFAA8131F5D97D9B8626FDEDF343E93488_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m15625F750730D127B128E2654A2B59982DEAB9C9_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mA8280DCF9D59E671AB204C71415939B6D7B4211E_AdjustorThunk,
	Enumerator_get_Current_m05879F7740A935DB74AE5672E2B537349369A3C1_AdjustorThunk,
	Enumerator__ctor_m9788E04B31AAEC644383BF9A7D0FD66F4D89F6F2_AdjustorThunk,
	Enumerator__ctor_mEA52ADA4F9D63275758AC0D426BA0CBDDAD6474E_AdjustorThunk,
	Enumerator__ctor_mB5DAC8151C7ABCC2E039D679CC59C09170005D6A_AdjustorThunk,
	Enumerator_Dispose_m1938093C428E4EDE3B40A07184993FA35F74AB42_AdjustorThunk,
	Enumerator_MoveNext_mB0957ADFA01BC52170FB018BD2AD59C2E163A22F_AdjustorThunk,
	Enumerator_Reset_mDD80A4C2F5EFC81C22B7F74CACFF9D0921BA0A3C_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mBB836D4E68A5F554A9A7C377FFBD67DA4253EA7D_AdjustorThunk,
	Enumerator_get_Current_mA7D722D310859E84FC0C9A2F8DBCAF8BA78F1D54_AdjustorThunk,
	Enumerator_set_Current_m1BB3A6D5580E7C6A864831EED00A4E1B8E81B1B5_AdjustorThunk,
	Enumerator__ctor_m8DA499B2030A9C843BE8FCAEFDFD4CD4EB5BA0BB_AdjustorThunk,
	Enumerator_Dispose_mA25DFE205338DF12128791E990699B49B335B759_AdjustorThunk,
	Enumerator_MoveNext_mA6D24403558EA8BFEE5028D75AC049EC8677F637_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m5E20159EC017B0F62631E89E5784B2D532E2D314_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m01A32A0663137A48FD334AD3F7BF1A172FEBA204_AdjustorThunk,
	Enumerator_ThrowEnumerationNotStartedOrEnded_mA8F87F65FFD80CCBB3A060C159463BB5B5603E93_AdjustorThunk,
	Enumerator_get_Current_m2DDD9E2B36A595D40708EBE84125C065CCF6F11D_AdjustorThunk,
	Enumerator__ctor_m37A6313A32B9FC5F6EB5D51D8F684495F740383E_AdjustorThunk,
	Enumerator__ctor_mFAF8E13AE32872378C00447B1AFCD9858441D07C_AdjustorThunk,
	Enumerator_Dispose_m1B66D14092A623BE5F8A22939E7A7A57B47232BE_AdjustorThunk,
	Enumerator_Initialize_m30C4758A1218C7294ED5F31CA799ED27A625C242_AdjustorThunk,
	Enumerator_MoveNext_mE700C9CC529ECE454379FBD8FC409DA45E1B03AC_AdjustorThunk,
	Enumerator_Reset_mBD98F1F40AC3C51AB5FBDD2158DAAFCCF590EA0E_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m7C75161AC2EE66A22E01977A98745F72EC2B66A0_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m1AE588BAA26DC5410664D87CC3DCD2EFF73CF686_AdjustorThunk,
	Enumerator_System_Runtime_Serialization_IDeserializationCallback_OnDeserialization_mD467A31CC142C287C668941326845B60908BD80D_AdjustorThunk,
	Enumerator_System_Runtime_Serialization_ISerializable_GetObjectData_mC455BEBC09DF1E94D9D8367E3A9D51F38A9DF1BF_AdjustorThunk,
	Enumerator_get_Current_mB3DADD6848C84F194FD9CA2F6A8D5A5BB83E2377_AdjustorThunk,
	Enumerator_get_NotStartedOrEnded_m274BAD4868C654F0095AEAD551E52954F8DD0E83_AdjustorThunk,
	Enumerator__ctor_mD65EF492693E70B41695A031A49F72C7EFA82FCE_AdjustorThunk,
	Enumerator_Dispose_m475AD9FE055280B2B1CF43E93F9A2340C3EAE75C_AdjustorThunk,
	Enumerator_MoveNext_m3347052D874561F1E1857019C4F418A27B125353_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m796D8D2C90C2436A06B08E30008143FCF74E8EC9_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mC2EE4A74701F665A795443A0814F1CF22BC12560_AdjustorThunk,
	Enumerator_ThrowEnumerationNotStartedOrEnded_mDFA786E51878E61AE16B8C27D512FD3407BA6AFA_AdjustorThunk,
	Enumerator_get_Current_m748B0758792F434FAB29656C2F5CF6EEB4481D27_AdjustorThunk,
	Enumerator__ctor_m7877FBC3233ABBF3242DBB8767B21F547B50A31A_AdjustorThunk,
	Enumerator_DisposeAsync_mB5E8AF8D2416E409E0EF0F189AD0FD01145A5FF3_AdjustorThunk,
	Enumerator_MoveNextAsync_m7441BF52E44BDD79E4E7CAE1F9C0FD51F3BB3E72_AdjustorThunk,
	Enumerator_get_Current_m899BAFFEB4702228080255387361D95ED3AA0647_AdjustorThunk,
	Enumerator__ctor_m9ED6D04154B0287F36E8E29C5A49F8113F8D3ED1_AdjustorThunk,
	Enumerator_Dispose_m3D89F01AE65EC60062FFB578C0E771C098EF2CB7_AdjustorThunk,
	Enumerator_MoveNext_m97783F73CDB1D0083A2F7D26A51847BF0843ADEA_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Entry_m66120A939C97C89BAFF013B3AE7FEF9BB6070F6E_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Key_mB49F4C26DC633814F50A1744D2F43332CEF8914C_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Value_m2B58ED135DE2504A9786BE2A06708190C7F9F7ED_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_mF8A12FD9DD4421A2C1B75FBE470014C6D1E0296A_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m1B03B4376AEFE278EBF80B22815E42BD5FC2EBBF_AdjustorThunk,
	Enumerator_get_Current_m26AF82C275C82180BB7F23C7E408BC1FEB9A38EE_AdjustorThunk,
	Enumerator__ctor_mA347E7F9A4E89648A28432A4CEA2FABA20C4DEEE_AdjustorThunk,
	Enumerator_Dispose_m7C0F723CD15AC0F1B5F6D2937235CFD7306329E5_AdjustorThunk,
	Enumerator_MoveNext_mAD0690F634E79D1C9896559DE69D5E2815850BD7_AdjustorThunk,
	Enumerator_Reset_m668460C937EA6D80F3C20D44C7D1F4D02511C0F3_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Entry_mEDF07B3B182C171328B6FD434AB0C51C4CF640FA_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Key_m5F7C47A383C19C80BDBE795F07CB0132AC37BDD0_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Value_m89734560ACBA07CB4AEDF0CC514294E267B5EE61_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m4FAC386B04F3311DA126417AE7A5F70AA8B9FB36_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m237C220E130919E063350A9141BB13478DC51806_AdjustorThunk,
	Enumerator_get_Current_m66C839BFE9F105BCBF859FC59834ABC4D2842EDE_AdjustorThunk,
	Enumerator_get_NotStartedOrEnded_mA33F0A7BB7608FDC42CBD775E9ABFE8348234EC0_AdjustorThunk,
	Enumerator__ctor_m74E4507FF37BDC183623DA6AEE98F82E112E9F2F_AdjustorThunk,
	Enumerator_Dispose_m1F5DB7D225E10E445137815B300A7C836CA06E3D_AdjustorThunk,
	Enumerator_MoveNext_m1092908331BD584DC099DBC0DE61ABDA7267F2B6_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Entry_m7EB9CA7BCC59643D29941E0B384A3A5A76CDCA3C_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Key_m311B50B02172FC06CF666E2D5FEF0DDCD5786E99_AdjustorThunk,
	Enumerator_System_Collections_IDictionaryEnumerator_get_Value_m6F0091ADAB32E0D0DC7497CC2B04DEF08F20A73A_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m2B4442329A7840135B8F78692CD5308700437D8A_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_m4D874FB38E08A2D26498947E4FB8A36CA469082B_AdjustorThunk,
	Enumerator_get_Current_m6AF552684A6136E5163CC3C09360202C85160FBC_AdjustorThunk,
	Enumerator__ctor_mF61ACC0911DFBA7D4E9F1BBC6A95781A23763CD6_AdjustorThunk,
	Enumerator_Dispose_m3E0B734E98D35978EEE706FCEC2FE08FEA74FD35_AdjustorThunk,
	Enumerator_MoveNext_m21C503A713FDCA406492E2BE960D85320752E615_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_mCD1674F679047ABFB40A195BABF666D7CC837AC3_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mAF47EBACCABC3D69CA80CAAFDA4A6B19CB27F7A7_AdjustorThunk,
	Enumerator_get_Current_m2D9E3398C2A3349E3F0F189A38E881D11DE5AA15_AdjustorThunk,
	Enumerator__ctor_m53208E6BE0322FB9D8336F1303586341867496CE_AdjustorThunk,
	Enumerator_Dispose_mB05777B182DD6D8C5ED21811C2CA95405D2743F8_AdjustorThunk,
	Enumerator_MoveNext_m297C3CEEAAC8D29D8F7C93E0801CD1D362D1C66F_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m6F93BBC247A6914CB4E45D9FA5345930C3B507B3_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mB728CD9238796C981668909E33EC186CC6FE76C5_AdjustorThunk,
	Enumerator_get_Current_m77681472C32B9ABB97AE602C0D4C365A0A8E8ACE_AdjustorThunk,
	Enumerator__ctor_m8FD77EB53030329DF2E29E08BEE0AE4EA8D822E7_AdjustorThunk,
	Enumerator_Dispose_mF012D679A710A034FE596935A8B2C8DA7E92A837_AdjustorThunk,
	Enumerator_MoveNext_m89F44DFC7BC66A1BA4FD6C09935A1F492738908F_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_mC56550C90B1621E19229741C78EC55C6A5D0BEA7_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mD5CAB46FA0627A7646E826A66B111A7B0EDD0F4A_AdjustorThunk,
	Enumerator_get_Current_m18B14000CB7077E7AD45EDFAD3CF8B8DEC307243_AdjustorThunk,
	Enumerator__ctor_mA52845CC6D3DF6FBC4065D8496C74B2162B90A35_AdjustorThunk,
	Enumerator_Dispose_mF4A570E2314AA029BCC1C32B2C0E41673909A3F8_AdjustorThunk,
	Enumerator_MoveNext_mAA12AA9E1229E36CEF390B7D1A9EA48DC5059894_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_Reset_m5CD8B11FC178B5E94103DDB1FAB65FCE08ABCB88_AdjustorThunk,
	Enumerator_System_Collections_IEnumerator_get_Current_mFBD205409FD3FDEACB92F262B54A515BFEE70791_AdjustorThunk,
	Enumerator_get_Current_m5427AE558435E25DBDA61E6236092F6D3B0ADEF4_AdjustorThunk,
	ExcludeContext_1__ctor_m8C7B1D32CD98A0C7A4E04E22C4433FBB21D3423E_AdjustorThunk,
	ExcludeContext_1_get_Property_m6A19D562A0F7F6FFB368440CC075DB91A5325251_AdjustorThunk,
	ExcludeContext_2__ctor_m6C3CAB2F1ABAF02AB478E955EE91483932857CA9_AdjustorThunk,
	ExcludeContext_2_get_Property_m5C4ECE37827CAD13983BF7A1DDC3C19E5932E5B2_AdjustorThunk,
	ExposedReference_1_Resolve_mFA2938DF9E0A74422B71F573F5CC0A8B1CA54647_AdjustorThunk,
	FieldDescription__ctor_m6FA19D01FA63FA6A6CEA6EBC2E43C1E03EB57DE7_AdjustorThunk,
	FixedList128Bytes_1_CompareTo_mAF36C346FD4AE2600025889DD02B9D44284C8C7F_AdjustorThunk,
	FixedList128Bytes_1_CompareTo_m4293303C3DF5B8C98D0888639C72E6B9D8E259D8_AdjustorThunk,
	FixedList128Bytes_1_CompareTo_m05620456947045FE8ECA0586B33EAA6530CB39A4_AdjustorThunk,
	FixedList128Bytes_1_CompareTo_m5E0B1FA6EA34DD296BC43FB162441A68065B02BC_AdjustorThunk,
	FixedList128Bytes_1_CompareTo_mBF678DF621A5F04CD8AA0BAF4725317C07A2DD25_AdjustorThunk,
	FixedList128Bytes_1_Equals_m4145FA97673D2191E4ECF2CBF2792FF13A84D752_AdjustorThunk,
	FixedList128Bytes_1_Equals_m291560352A3884460A7CA6404D76607160887918_AdjustorThunk,
	FixedList128Bytes_1_Equals_m53B970512EAC6DCCD5690CB7C5586846D2675ABF_AdjustorThunk,
	FixedList128Bytes_1_Equals_mE4BF76213B101088B999DBE426C9BCF856A8B7D3_AdjustorThunk,
	FixedList128Bytes_1_Equals_m90466CBC2F0AA1F1F4B2F831952937983B348C52_AdjustorThunk,
	FixedList128Bytes_1_Equals_m6D35114F890EE34855BEF854312B1A7CAFCB3BB9_AdjustorThunk,
	FixedList128Bytes_1_GetHashCode_m4565D17CC76AE50032A2E95C45D66C493D7FEDB4_AdjustorThunk,
	FixedList128Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mC866DD1936E0DF3EB7D8CE147BE7358CD28E0536_AdjustorThunk,
	FixedList128Bytes_1_System_Collections_IEnumerable_GetEnumerator_m8970F4B5B7B1C558072C3391E983BD03F754ACC1_AdjustorThunk,
	FixedList128Bytes_1_ToArray_mE453C883A4FB8F6D9FF84AD9139A7AE4E893526C_AdjustorThunk,
	FixedList128Bytes_1_get_Buffer_mD43014DEC5833FAF9392FC3FCBE3E9B54C6C4156_AdjustorThunk,
	FixedList128Bytes_1_get_Length_m927F92EC07E24F20A7314C091F5C5FACA9A0C16B_AdjustorThunk,
	FixedList128Bytes_1_get_LengthInBytes_mEAD7F77987799E0D336DE5ABCEAA71C533E5801A_AdjustorThunk,
	FixedList128Bytes_1_get_buffer_m3FA0F1453D72938135D3074318E14AD2A1B32DA4_AdjustorThunk,
	FixedList128Bytes_1_get_length_m01D23D934D19D0EEDCA425F421ABA56B3DA58F3C_AdjustorThunk,
	FixedList32Bytes_1_CompareTo_m423D7F263E7F7BFEB06CB8C817D534391B465C6F_AdjustorThunk,
	FixedList32Bytes_1_CompareTo_mC56C07D51B7BB71D545FFCF9199FDE8933806CD7_AdjustorThunk,
	FixedList32Bytes_1_CompareTo_mB123ED3E04C501E2B0FE9B27D3BBBC535D49D4DF_AdjustorThunk,
	FixedList32Bytes_1_CompareTo_m720DCC8408299084D8CE9218FDFA9F2F8A29EA03_AdjustorThunk,
	FixedList32Bytes_1_CompareTo_mCCABF337C720F84AC57728059B35683CFD6BC749_AdjustorThunk,
	FixedList32Bytes_1_Equals_m5CDBB6208409BC6F1BD4619D76E9788EE0CB3AC2_AdjustorThunk,
	FixedList32Bytes_1_Equals_m1F86304BBD4CB7F0C1F968F6736611957F7D85A8_AdjustorThunk,
	FixedList32Bytes_1_Equals_m9532613448A35774D4D150FEE96E457BF174E8DB_AdjustorThunk,
	FixedList32Bytes_1_Equals_m1FFF57EFEB15ECE1B694E007239CFE7EA324F31B_AdjustorThunk,
	FixedList32Bytes_1_Equals_m5BFDD3FBB8CF240B35DDE0DA813AD2A500A28485_AdjustorThunk,
	FixedList32Bytes_1_Equals_m276811E7FF7931B4320FC8B7C8BAFAB88E9D96E9_AdjustorThunk,
	FixedList32Bytes_1_GetHashCode_m74D739320BEE925AC8446E58E20F59CF21561C44_AdjustorThunk,
	FixedList32Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m7012A8E1D82BBDEB998E88A124C9DF8C9E2A765D_AdjustorThunk,
	FixedList32Bytes_1_System_Collections_IEnumerable_GetEnumerator_mB22190AE9299BD81948F66DE4F37972ACABC5E0C_AdjustorThunk,
	FixedList32Bytes_1_ToArray_m480123C27D084EABC25806107091A5FCCE272DF7_AdjustorThunk,
	FixedList32Bytes_1_get_Buffer_mFB1AAFA86AB113AC3BEF550971AA4DA182A64606_AdjustorThunk,
	FixedList32Bytes_1_get_Length_m69BE1E6D357498954A3C7A28D7F00CAAA79F80DE_AdjustorThunk,
	FixedList32Bytes_1_get_LengthInBytes_m73F38D714876A4B0D7DC0AC8834B6ED0745D585A_AdjustorThunk,
	FixedList32Bytes_1_get_buffer_m139B05C9B2F45AA86EBFBD4F0EC8803848137F66_AdjustorThunk,
	FixedList32Bytes_1_get_length_mD16FF5C8CCA29B2A58BB98073E6729F0E2BBE907_AdjustorThunk,
	FixedList4096Bytes_1_CompareTo_m0DF14F0642F8E54D86B4A3310F015F0E22C38129_AdjustorThunk,
	FixedList4096Bytes_1_CompareTo_mE92BCDD9CAC8EE2EF5F1D0C18BF4BA12B32E6ECB_AdjustorThunk,
	FixedList4096Bytes_1_CompareTo_m7A1B6F3399E24A26B0E570CF3696434FE361C6A2_AdjustorThunk,
	FixedList4096Bytes_1_CompareTo_m35A1B10BBC2AC4E720C46C63E128D2DB40BA5BD6_AdjustorThunk,
	FixedList4096Bytes_1_CompareTo_m46B9A52B5684F574DB0E9879DBBB39713D11B944_AdjustorThunk,
	FixedList4096Bytes_1_Equals_m203CDC64FDD866292DB60D5FD259915539E9AD17_AdjustorThunk,
	FixedList4096Bytes_1_Equals_m83E576D45D844460AC0E872ADE1805ED90AFDFC7_AdjustorThunk,
	FixedList4096Bytes_1_Equals_mF82C3CC55D8D2851DDA383EC658E9CB30038C55E_AdjustorThunk,
	FixedList4096Bytes_1_Equals_m790B4671C26193AC8908C5FFBDEEDCA00151B8F6_AdjustorThunk,
	FixedList4096Bytes_1_Equals_m25B7C6C6F180158EF2FBD64C19A2B8EA0995E0B6_AdjustorThunk,
	FixedList4096Bytes_1_Equals_m21ECB8AD6E1B0BA1D384FC95254966920CBD16E8_AdjustorThunk,
	FixedList4096Bytes_1_GetHashCode_m999C80B2EF7B4D1E4DAAC97BB1FD2B88AD2DCEA8_AdjustorThunk,
	FixedList4096Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m781FA48EA39C7C02190DBB57CEEEFAE618A7C03D_AdjustorThunk,
	FixedList4096Bytes_1_System_Collections_IEnumerable_GetEnumerator_m9591E6949DD5A39AA97614FC75AD51A98CA605C2_AdjustorThunk,
	FixedList4096Bytes_1_ToArray_m2DE5CC6C5841C3C033F952A783853B56823F7F73_AdjustorThunk,
	FixedList4096Bytes_1_get_Buffer_m76BCD608C0436D7647CC2BD2D197629A9B1C6DC3_AdjustorThunk,
	FixedList4096Bytes_1_get_Item_m970363422CEE1B790AFBEB5934459409F61AD136_AdjustorThunk,
	FixedList4096Bytes_1_get_Length_m7F849A6DE2F1D919926FD832199A91C25A15CF83_AdjustorThunk,
	FixedList4096Bytes_1_get_LengthInBytes_m946CB445CCA697A0A884BD8A95986A378A3DA087_AdjustorThunk,
	FixedList4096Bytes_1_get_buffer_mC6FF3A165067CDB3016B89246011CB5D7B34B71D_AdjustorThunk,
	FixedList4096Bytes_1_get_length_m0437506FA2283836CDDC3A46CC5F706D1819B05A_AdjustorThunk,
	FixedList4096Bytes_1_set_Item_m8FBFF44654DE0B13D3B6968206668554CCE665D2_AdjustorThunk,
	FixedList512Bytes_1_CompareTo_m495B66EAA67EBC91176D18B39569066B905DF19D_AdjustorThunk,
	FixedList512Bytes_1_CompareTo_mDCAD639C8842A821B3550E6D2ED6E94FC2CEA40C_AdjustorThunk,
	FixedList512Bytes_1_CompareTo_m3F70E2A1F880757CA1BC7C681898C42459486DC7_AdjustorThunk,
	FixedList512Bytes_1_CompareTo_m806888F07579CEB7590777299CD5B312ACC408B6_AdjustorThunk,
	FixedList512Bytes_1_CompareTo_mF7FFCD39BB662F4B7D659AC5904864082B4990F4_AdjustorThunk,
	FixedList512Bytes_1_Equals_m7A8FE88F8493F3D3BC94A0B3D1BADDAE67F9457D_AdjustorThunk,
	FixedList512Bytes_1_Equals_m9ACFF0F110192C087A0872039AC8AAA736756288_AdjustorThunk,
	FixedList512Bytes_1_Equals_m710DF325B8692C020E48D7B795413917A62DA074_AdjustorThunk,
	FixedList512Bytes_1_Equals_m4474646DE461531A45836110FF120257A175DACB_AdjustorThunk,
	FixedList512Bytes_1_Equals_m40B7CB0CEDC451DBF6AA9677B73A68298EFD50F1_AdjustorThunk,
	FixedList512Bytes_1_Equals_mCB08CCF4DB6E7C3B359F1949D89003C9324124A2_AdjustorThunk,
	FixedList512Bytes_1_GetHashCode_mC8DD2C89A69D94BFD8F02AAC003C87D6EB878D96_AdjustorThunk,
	FixedList512Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m35CE6DC32FA8EE026258C5DF9CBD8A9DA7CB09CC_AdjustorThunk,
	FixedList512Bytes_1_System_Collections_IEnumerable_GetEnumerator_m487E7F515D84F283DEAD75EDC04306EDBF71D0FA_AdjustorThunk,
	FixedList512Bytes_1_ToArray_mEBC6A9D83F355E772A9BDC2A85E819C705E78D5D_AdjustorThunk,
	FixedList512Bytes_1_get_Buffer_mB8203817FDA4AEEF7E986FEF9A92B92F48456DA4_AdjustorThunk,
	FixedList512Bytes_1_get_Length_m39F0C6ECC0732E96B3F8C0A2553A71E975011ABF_AdjustorThunk,
	FixedList512Bytes_1_get_LengthInBytes_m3D7D1A03155F1BE615645D5C5E570DE054738D8C_AdjustorThunk,
	FixedList512Bytes_1_get_buffer_m513212BD1E1DB094D6923FAD0B5D92DF8FBAC102_AdjustorThunk,
	FixedList512Bytes_1_get_length_mB15FCBDD135D7C01311794E83AF0B6436D2D5464_AdjustorThunk,
	FixedList64Bytes_1_CompareTo_m1C8BB1A8B5A249FEC1D43343DA55CDF35F9CBCBC_AdjustorThunk,
	FixedList64Bytes_1_CompareTo_m96A1729FE2EE27FF7DA19BBA8E962E170CC01197_AdjustorThunk,
	FixedList64Bytes_1_CompareTo_m60410F1612BB2C774B5914E174315F47F2671A62_AdjustorThunk,
	FixedList64Bytes_1_CompareTo_m6FFCA493F022922BC480E8886FBD9D5516D2BF1B_AdjustorThunk,
	FixedList64Bytes_1_CompareTo_mD2F702CFAC4536BF92D218262CC096509AF97E4C_AdjustorThunk,
	FixedList64Bytes_1_Equals_mF8215C50AF16D3685D298D6D3B4E35A345D4E9DD_AdjustorThunk,
	FixedList64Bytes_1_Equals_mA7C94D9600D89D6734E45E2470CF57F4A8A2F442_AdjustorThunk,
	FixedList64Bytes_1_Equals_m3964C355A393F163EC05ADB72F54317D7439DF82_AdjustorThunk,
	FixedList64Bytes_1_Equals_m355B482C62357D27E8C0C4CA820B8EF3E34FCB6B_AdjustorThunk,
	FixedList64Bytes_1_Equals_m2AC7716111558CBA2C8E5B34B623B31BAAF475F4_AdjustorThunk,
	FixedList64Bytes_1_Equals_mFCAFBA37F06212725C4B46CD578D208779D40D12_AdjustorThunk,
	FixedList64Bytes_1_GetHashCode_m722820B8F7CE4975F4A060C565E192259C5E8F7A_AdjustorThunk,
	FixedList64Bytes_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mE589F12ED543025C909BC6F9C1C65DCF756CCFCA_AdjustorThunk,
	FixedList64Bytes_1_System_Collections_IEnumerable_GetEnumerator_mB5B987A8B69250FA50AD2A66AEC23BE5B73A72FA_AdjustorThunk,
	FixedList64Bytes_1_ToArray_mD992438F1C3B1ED9436D55E80F539B05F02876A9_AdjustorThunk,
	FixedList64Bytes_1_get_Buffer_m453BB0D8491D838AC1381E57320C9FE6E3943F68_AdjustorThunk,
	FixedList64Bytes_1_get_Length_m9CE762AB945BF8C956C2B6AF84AEE58B20A679C3_AdjustorThunk,
	FixedList64Bytes_1_get_LengthInBytes_m2718009B0C788432818D447CD03D022FDFB2E4B0_AdjustorThunk,
	FixedList64Bytes_1_get_buffer_m590958E1E714990BD451E67228F00F8DB98CC77E_AdjustorThunk,
	FixedList64Bytes_1_get_length_m3DE952DD93CA6DA9DCC7AE3B38049D044BC24628_AdjustorThunk,
	FreeListCore_1__ctor_m018D1A8697D2FB65C3C8BBFBF175DB9C68EAFB2F_AdjustorThunk,
	FreeListCore_1_Add_mE3E0D914E60A3F9BC9482692FD08BBB813165FF6_AdjustorThunk,
	FreeListCore_1_AsSpan_m6D31CDAF0AB9B37C33C0BC5DE22A207A98EEA373_AdjustorThunk,
	FreeListCore_1_Dispose_m7726D719476C9DD4FBF79316486DCD895A3A8CAD_AdjustorThunk,
	FreeListCore_1_Remove_m05518591CC734CA777E45FB0ED1051C9429F25D3_AdjustorThunk,
	FreeListCore_1_get_IsDisposed_m4874FF53FEA7B18F41A9D71270BE5A0AB9A848F5_AdjustorThunk,
	FunctionPointer_1__ctor_mC619E78A7A6A909670DFFE07825842FDA7D178F7_AdjustorThunk,
	FunctionPointer_1_get_Invoke_m552FD5AD590BE1F22C35842835DBC3FB96BACA44_AdjustorThunk,
	HashMapHelper_1_GetKeyValueArrays_TisIl2CppFullySharedGenericStruct_mE21354629769E9053A0A66D49C238EEE6A378A51_AdjustorThunk,
	HashMapHelper_1_Dispose_mB9A6E998C327368FCA81FFF52F90700CE2557703_AdjustorThunk,
	HashMapHelper_1_get_IsCreated_mFD6A2975F04E9C84A9886543625D8E8BCA71E398_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerable_1__ctor_mE8BEFCF0EF5E38CD2946F0344224AFF95CB9D88A_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerable_1_GetEnumerator_mE319C71A38C7A7AE12288857B13E0CA49C65A116_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerator_1__ctor_m700D1FAC76BF226957060636D4D8AB9C6584CDEC_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerator_1_Dispose_mEBC1E3FF6876007C72F6F76ED237165B599B2505_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerator_1_MoveNext_m6695B2065C6EB0E71B90C2EFF9429E4A76F4C0BA_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerator_1_Reset_mEF20F04CD33523EE6B378C8F992E49A2214E1626_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerator_1_System_Collections_IEnumerator_get_Current_mD2E14558F371D66EBB6269FEAA4143BCC357D472_AdjustorThunk,
	IndexedCollectionPropertyBagEnumerator_1_get_Current_m4E570DCDE463BEA50BA7B3DC03595ED13939EDBE_AdjustorThunk,
	InputFeatureUsage_1__ctor_mAE978CC133E57B40C2D810A714714CC92408E485_AdjustorThunk,
	InputFeatureUsage_1_Equals_mCC83C55839566F40031379F89D9A13CB97E251F8_AdjustorThunk,
	InputFeatureUsage_1_Equals_m9A435C12C15B901C148A4C8260D5501F5BB062D8_AdjustorThunk,
	InputFeatureUsage_1_GetHashCode_mA7299C66E5453574F4375112C419DA0A60E442BA_AdjustorThunk,
	InputFeatureUsage_1_get_name_m78A37B7D883B922994224E33DB7D146EA91D7590_AdjustorThunk,
	InputFeatureUsage_1_get_usageType_mB00911CFBC80A809A96C358A5EF4040330D4EF21_AdjustorThunk,
	InputFeatureUsage_1_set_name_mF25DD23D2A5D6234F01FA753D6A4C0518D664B08_AdjustorThunk,
	InternalEnumerator_1__ctor_m464A908F35C335E4B3A17DE886808274C7745493_AdjustorThunk,
	InternalEnumerator_1_Dispose_mF3113BF34937C09318EE23FAA3C8CAF4324284AA_AdjustorThunk,
	InternalEnumerator_1_MoveNext_m0DF22C0F597D04AD07643D13B9C657A88F58A1F9_AdjustorThunk,
	InternalEnumerator_1_System_Collections_IEnumerator_Reset_mA541889D3D4D07BEB148635EAEDC7F3AD70D6E43_AdjustorThunk,
	InternalEnumerator_1_System_Collections_IEnumerator_get_Current_m9F616DD0F37ADC09A91128D6CB72AF97F8221D26_AdjustorThunk,
	InternalEnumerator_1_get_Current_m2E7A04B825CC1C6C0A7B4B49CD8473CEF6A89FAC_AdjustorThunk,
	JEnumerable_1__ctor_mD4769E3AF4442E9295648902C9C07C267C899D35_AdjustorThunk,
	JEnumerable_1_Equals_m6E47C16543AEAFDA1DBA86907F9D1CE003468A6D_AdjustorThunk,
	JEnumerable_1_Equals_m32BDED99001ED47C141F09BB9A0A9106524606CF_AdjustorThunk,
	JEnumerable_1_GetEnumerator_mA8D66319EF7DD05C26DCF00F267B8FF4AD0B2B36_AdjustorThunk,
	JEnumerable_1_GetHashCode_m9BD12C54D0E4F6C7A68476C79C7FD12280A19C2C_AdjustorThunk,
	JEnumerable_1_System_Collections_IEnumerable_GetEnumerator_m047B396B5DFDE9A2034CEF35021890948C573109_AdjustorThunk,
	KVPair_2_get_Key_mED89D3602B012281C2C837349C34191B0875115C_AdjustorThunk,
	KVPair_2_get_Value_mC94F9A68A6FADF40DC6A14F23C4FF284241A6D88_AdjustorThunk,
	KeyValuePair_2__ctor_mD82E516936D2BDE6D46C8C45270250647986231E_AdjustorThunk,
	KeyValuePair_2_ToString_m73A769E5C2C6956378D80913C3E9D8A68513F905_AdjustorThunk,
	KeyValuePair_2_get_Key_mBE75BF8983618BC1ACEC20F94C1BFF85C8AA50F1_AdjustorThunk,
	KeyValuePair_2_get_Value_mFA1964BF56AA214EE0D491CC197F61BC9E5F1F7A_AdjustorThunk,
	LargeArrayBuilder_1__ctor_m9169978DDAA889BFF05C452AB15A211C63E4A35C_AdjustorThunk,
	LargeArrayBuilder_1__ctor_mDF00881455B7BBDCEB668A1E3119CC67B7DAEE23_AdjustorThunk,
	LargeArrayBuilder_1_AddRange_mCE64CAFAC2EBC41A1C72EB2F383F2F21744A4331_AdjustorThunk,
	LargeArrayBuilder_1_AddWithBufferAllocation_mD7E0D26AB6CE709D88228EEA5C8C20B4AF973ECD_AdjustorThunk,
	LargeArrayBuilder_1_AllocateBuffer_m6399AE087574673718CFC172F364FCBCFDD3E288_AdjustorThunk,
	LargeArrayBuilder_1_CopyTo_m2E80BBDC8892C16A99C069C380981AEC0341AA43_AdjustorThunk,
	LargeArrayBuilder_1_GetBuffer_m5EB830E4B175F0E9EBE9C90BB979B8666BC98F9E_AdjustorThunk,
	LargeArrayBuilder_1_ToArray_m1AC52FEFD7058DF67EC15212C4CE9111F640BF6F_AdjustorThunk,
	LargeArrayBuilder_1_TryMove_mA502F3D33ABD9C0EC5C2ADDC0D8DA3EACE586AFA_AdjustorThunk,
	LazyLoadReference_1__ctor_mC47FC2942AE3A0C07B8D034B46341372061D1C4A_AdjustorThunk,
	LazyLoadReference_1__ctor_m699AD6DEF5B75F84F0B5549C9434767A4A2DB9C9_AdjustorThunk,
	LazyLoadReference_1_get_asset_m4BA1374FF657FC2C40BDFAF371B574B76F1965F8_AdjustorThunk,
	LazyLoadReference_1_get_instanceID_m9B7093A756F58875D533B3E3DA5533175864A74B_AdjustorThunk,
	LazyLoadReference_1_get_isBroken_mFC4F5B17985606AFA118D25579C7C5FD51A7D3AC_AdjustorThunk,
	LazyLoadReference_1_get_isSet_mA5602191FDFA1435F2264EFA60BB5E632AA681E0_AdjustorThunk,
	LazyLoadReference_1_set_asset_mCF4C051B624B3D5D1978EE86D81E9EB90CF42F4C_AdjustorThunk,
	LazyLoadReference_1_set_instanceID_mADE5AACEDA2F279D33555FD667C398C88BAC244F_AdjustorThunk,
	ListBuilder_1__ctor_mFC129BE2CF658F37264E5AE87C38ACBE1686FC06_AdjustorThunk,
	ListBuilder_1_Add_mC19811ACD1D830765599A1583F96C56D16A9E5AD_AdjustorThunk,
	ListBuilder_1_CopyTo_m07DD8110CA53CAE4B1C65F1E9A2AC9C074F14378_AdjustorThunk,
	ListBuilder_1_ToArray_mADCCB17012F7FB8EE90E45E2CD13DBD2E71FE781_AdjustorThunk,
	ListBuilder_1_get_Count_m5FC923A60B6BCD6D28FA95B0495EF5E537266285_AdjustorThunk,
	ListBuilder_1_get_Item_m23AA6DB4615BCE00F8A63C6152113822F0250EEE_AdjustorThunk,
	Memory_1__ctor_m3743D2A1BDE7D9720706FF7CFE3C270432C4D381_AdjustorThunk,
	Memory_1__ctor_m7EA183117D6958FFAC919F291510339F7792C7CD_AdjustorThunk,
	Memory_1__ctor_m39F9C3933A6F0AD9E9CE3A5B597A770C54F6FECF_AdjustorThunk,
	Memory_1__ctor_m58BECFC48979D14A69D6AC373301B755043ACA9B_AdjustorThunk,
	Memory_1__ctor_m5ABFBF1303AB4B2B8AD03A4586CC52B6098DCBE9_AdjustorThunk,
	Memory_1__ctor_m5F9E06AAB95D8F8AF62C3AD68DEC2F6716CDEE05_AdjustorThunk,
	Memory_1_CopyTo_mDB8DD4693F007CBBDC70454425C368FD0614D3FC_AdjustorThunk,
	Memory_1_Equals_m2F882A89DBD535FFE48B39EBAF2626BA2AD36DA0_AdjustorThunk,
	Memory_1_Equals_m03AA1FCD0E395B27BD53551C06FDC8ED34F10A9A_AdjustorThunk,
	Memory_1_GetHashCode_m7A4C30D97D133B153A50D638A7D596CDA1BD11B5_AdjustorThunk,
	Memory_1_Pin_m9EEBCD8E7B6269C02BFBD57F2E67854920DFB85B_AdjustorThunk,
	Memory_1_Slice_mB1A59814638EB046790D5DB854A8D42BA2CB686F_AdjustorThunk,
	Memory_1_Slice_m6D92DFA60814F303301792ABF267BA35242FACA4_AdjustorThunk,
	Memory_1_ToArray_mF5FFD4A0E8AC951FDA34807539E9631310B6D757_AdjustorThunk,
	Memory_1_ToString_m8587562831B505E363617A254DE467726D4C6351_AdjustorThunk,
	Memory_1_TryCopyTo_mA894E1A58E1FB0ACB1FEDECD7DDA18A8ABFD4CD3_AdjustorThunk,
	Memory_1_get_IsEmpty_mFE27DC49FE38E33AD226323A42DB57922F00B0C8_AdjustorThunk,
	Memory_1_get_Length_m0D3942C435EEDB938ADDA2CE7C625761CEAB520E_AdjustorThunk,
	Memory_1_get_Span_m86E7A7A0C72430FDB1B2A2BCEEAAEEF7E7F83596_AdjustorThunk,
	NativeArray_1_CheckReinterpretLoadRange_TisIl2CppFullySharedGenericStruct_m26E933EF2857F7DFEB041A243BCE46DA67A31435_AdjustorThunk,
	NativeArray_1_CheckReinterpretSize_TisIl2CppFullySharedGenericAny_mB5EA92D2C8ED01EA1AF30582560964A0FF2523F8_AdjustorThunk,
	NativeArray_1_CheckReinterpretStoreRange_TisIl2CppFullySharedGenericStruct_m941429E862A4F6C5329367847862AE742771E044_AdjustorThunk,
	NativeArray_1_InternalReinterpret_TisIl2CppFullySharedGenericStruct_mB0CDEB18464001F253D27086821B2DEE7C1859E8_AdjustorThunk,
	NativeArray_1_Reinterpret_TisIl2CppFullySharedGenericStruct_m1FE7062E31411B740E16829B437A95E0BC1FE66E_AdjustorThunk,
	NativeArray_1_Reinterpret_TisIl2CppFullySharedGenericStruct_m0A4282BEF39DA967523E47AF7251C3F5A58651F8_AdjustorThunk,
	NativeArray_1_ReinterpretLoad_TisIl2CppFullySharedGenericStruct_mAEB3472F537D9233CFEA99C737C97B229E5D2F31_AdjustorThunk,
	NativeArray_1_ReinterpretStore_TisIl2CppFullySharedGenericStruct_mC77044D13F85A6D5A7AFFF1AB25B0E8E157CFD0C_AdjustorThunk,
	NativeArray_1__ctor_mFA31ABA65EA503C10D2A14D67A78BA391FC47979_AdjustorThunk,
	NativeArray_1__ctor_m840B99F1F4447BB0CE13C53803EB5DB4930E3F89_AdjustorThunk,
	NativeArray_1__ctor_m788DE0F85C4051DDF092DDF96484DE655ACFB6F1_AdjustorThunk,
	NativeArray_1_AsReadOnly_m6A3C4FD632C19C139E79007CE683549D40A600F2_AdjustorThunk,
	NativeArray_1_AsReadOnlySpan_mEDF5E795C5FC628766F2016DFB498E87E36BA3A0_AdjustorThunk,
	NativeArray_1_AsSpan_m5E5BA6DA8F13E99DA9E483864C4FE5CC56ED1259_AdjustorThunk,
	NativeArray_1_CheckElementReadAccess_mB188CE6C850D24F288C813CEC493CC63760FB6F2_AdjustorThunk,
	NativeArray_1_CheckElementWriteAccess_m354C69BA2B8C9E9788F54ECA26043F230054BB85_AdjustorThunk,
	NativeArray_1_CheckGetSubArrayArguments_mB47D62002A4852E22E03AC421FFDFF5A48FDA21E_AdjustorThunk,
	NativeArray_1_CopyFrom_m910E1EF42DE5ACF6E07014ADC35D83E87A3C43A8_AdjustorThunk,
	NativeArray_1_CopyFrom_mA52B88617F4F7972D7275E97D2D52CF623754308_AdjustorThunk,
	NativeArray_1_CopyTo_mCE5ACF49C2F1719FB011E7B9715D90315C6BB72C_AdjustorThunk,
	NativeArray_1_CopyTo_m6072353F8BA28E08B24828767C0A5AA713F0B307_AdjustorThunk,
	NativeArray_1_Dispose_m365A262FA4CA431467F021D7732CECD68316AF80_AdjustorThunk,
	NativeArray_1_Dispose_mC40FD0F1B87730842624526506EDBA485496D3EB_AdjustorThunk,
	NativeArray_1_Equals_mCC874C041CCDC87B4FDAF62364706ADA4DE292C4_AdjustorThunk,
	NativeArray_1_Equals_m5FD0AFF8B0E23458ADFA928C1505060F707D34B3_AdjustorThunk,
	NativeArray_1_GetEnumerator_mB8476B005B66C374A79CAA0F5887F98EDD4D8E32_AdjustorThunk,
	NativeArray_1_GetHashCode_mB187A5111AF164207661A0D456D14F283F391866_AdjustorThunk,
	NativeArray_1_GetSubArray_mB3D1C75BB777B0C93DF3FB3B297F748B41362959_AdjustorThunk,
	NativeArray_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m4D33419BA13170369A1911A84F5CE9538A1ADB9A_AdjustorThunk,
	NativeArray_1_System_Collections_IEnumerable_GetEnumerator_m9DE04F86BF0FB744FEB842CD21E503987BB298DA_AdjustorThunk,
	NativeArray_1_ToArray_m36734C0A577134F73463545CA7A587BCA1792002_AdjustorThunk,
	NativeArray_1_get_IsCreated_m527A2C3B75C25BFF29D1D9EA88C81172FF4F5A5A_AdjustorThunk,
	NativeArray_1_get_Item_mA8C8A69EB3A5D460C55DFCD27275CD5BA5E2B455_AdjustorThunk,
	NativeArray_1_get_Length_mBE5CC8B844994CFC4AB434235F915881575E63C8_AdjustorThunk,
	NativeArray_1_set_Item_m629BDF69720F9FF193478E89307F9B6A56425379_AdjustorThunk,
	NativeKeyValueArrays_2__ctor_m584C91680CD96728B10CBAA2021F14AE162A04E4_AdjustorThunk,
	NativeKeyValueArrays_2_Dispose_m08B6A4226A0125C6123942BE90703D08153CCE37_AdjustorThunk,
	NativeKeyValueArrays_2_get_Length_m5540F10E36DBBB6005821F5FEAF15610EB68C429_AdjustorThunk,
	NativeList_1_Dispose_TisIl2CppFullySharedGenericStruct_mC1029EC6803B86B2C7577203E6B311E262BE4F65_AdjustorThunk,
	NativeList_1_Initialize_TisIl2CppFullySharedGenericStruct_m84B39CC62859C7E4EDB87CA79D4F1B31FCCA4C54_AdjustorThunk,
	NativeList_1__ctor_mCF266C5F6762E110B7EAEAF5546388FDB3621BD8_AdjustorThunk,
	NativeList_1__ctor_mA1DFF4EEBED87ED011F318AEC145588B17212121_AdjustorThunk,
	NativeList_1_Add_m9BE8F632802CF4BA6110C86A3C1842F568C58DBB_AdjustorThunk,
	NativeList_1_AddNoResize_m36834BC68209BDEE1E19A6F058430821B0C64827_AdjustorThunk,
	NativeList_1_AddRange_mA24575907F4D1E91B47EFA158C59E36759917EA3_AdjustorThunk,
	NativeList_1_AddRange_m5469007F99D38C73CAF59B7F3A2A20BDA7605288_AdjustorThunk,
	NativeList_1_AddRangeNoResize_m788785F2770CF6601F6A8AC031CFE93FE154229E_AdjustorThunk,
	NativeList_1_AddRangeNoResize_m0FB384BF2D4A909BBF08DC85CA4C351B29D245AA_AdjustorThunk,
	NativeList_1_AddReplicate_m639D064AC5E19B02227D042D88A1204E0B131F8A_AdjustorThunk,
	NativeList_1_AsArray_m1E9616CC42457555561B1165B47ED6E2EEADAC98_AdjustorThunk,
	NativeList_1_AsDeferredJobArray_m0C54A266A92C6F33AA5A55FDC25744EA55651FE1_AdjustorThunk,
	NativeList_1_AsParallelReader_mC0476FC7DBBEC92472400D556FDA960843E4B1DD_AdjustorThunk,
	NativeList_1_AsParallelWriter_m3F0A4A00A415130242EDE59D2AB67FF06B6AD9D2_AdjustorThunk,
	NativeList_1_AsReadOnly_mE3E9EA4C33D970606F4BFD32677EC2C9EE74A717_AdjustorThunk,
	NativeList_1_CheckHandleMatches_mA892EAA79CDE121D317AC1FC2FCE8A0C50F03CD4_AdjustorThunk,
	NativeList_1_Clear_mD122F42577EAB804E1D4E3459202BCABA97A9021_AdjustorThunk,
	NativeList_1_CopyFrom_m432E91DFECDC6162EBC8406FE5B0F485E8CDD6FD_AdjustorThunk,
	NativeList_1_CopyFrom_mD2E8CAA2F97C6B8960A7686EEB7AEAA457463287_AdjustorThunk,
	NativeList_1_CopyFrom_mA6685A428A1C6F5D6DFD17EC2816E367B117ED7A_AdjustorThunk,
	NativeList_1_Dispose_m42535264C7291A64741CA36B4F0567D15D7BDC2F_AdjustorThunk,
	NativeList_1_Dispose_m5BFE1E59D09E1706BD6BBDB5D3C7BD0E3C18FB1D_AdjustorThunk,
	NativeList_1_ElementAt_mC997B931531C23A8CC2051A7A7F82A866F4F89BA_AdjustorThunk,
	NativeList_1_GetEnumerator_mAEC3E4F148C87993A6A957CCD8D16A1CBADF6621_AdjustorThunk,
	NativeList_1_GetUnsafeList_m4787F7FF0C74B362B5DC98531C2FF66279A5EAEF_AdjustorThunk,
	NativeList_1_InsertRange_m5616E3C81C172218D366190BC6EFEAF13BFF6AAF_AdjustorThunk,
	NativeList_1_InsertRangeWithBeginEnd_m6C08CC6FCE0C86D983776D77B196EAF0FA4FB020_AdjustorThunk,
	NativeList_1_RemoveAt_mEDD020DF08725F529B5AA06F652196FD3B6ABC92_AdjustorThunk,
	NativeList_1_RemoveAtSwapBack_m3BAA4B8DC92D6A907E4B8570A4919F0C0B2211B7_AdjustorThunk,
	NativeList_1_RemoveRange_mC7C99994ADAF3DD89F75B190D90921449CDB3247_AdjustorThunk,
	NativeList_1_RemoveRangeSwapBack_mC915D35A96C41DB035F4D0FCEF77143963205A4E_AdjustorThunk,
	NativeList_1_Resize_m84F443F6B9C92F2415832AF704F927D86166870C_AdjustorThunk,
	NativeList_1_ResizeUninitialized_mC281739878E0A1D9BD814C0B970B46A554D8EDFD_AdjustorThunk,
	NativeList_1_SetCapacity_mF54F13DED0AFDF81EFEA207F617090CEC6865C20_AdjustorThunk,
	NativeList_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m8645EF055A7AF1625D1EC908EB24A081FB8C2EA8_AdjustorThunk,
	NativeList_1_System_Collections_IEnumerable_GetEnumerator_m5BE8F41FB19932A28D5A41428FBE94C619FE5EF2_AdjustorThunk,
	NativeList_1_ToArray_m3ED93E608FAA962BC4927B048C3F729D2E9B0B4F_AdjustorThunk,
	NativeList_1_TrimExcess_m83C547A2B977DBE462279C50F85931114868E16B_AdjustorThunk,
	NativeList_1_get_Capacity_m5F1CF166F164381AE02960A7686D8ACFBA0BF5ED_AdjustorThunk,
	NativeList_1_get_IsCreated_m4E2356CCFFB361C91CBEFB528F727AC7C244043F_AdjustorThunk,
	NativeList_1_get_IsEmpty_m7BFF94CACA435ED947CEEC6E9090D8D1EC80FC6B_AdjustorThunk,
	NativeList_1_get_Item_m4C9E1C7BB475457EAE88A496A68A77E3F7A64F92_AdjustorThunk,
	NativeList_1_get_Length_mBCE0D52E1FEFC40B5CFEE2F41B493C7FF6A07FA7_AdjustorThunk,
	NativeList_1_set_Capacity_m73E9EE87AF9B2F512893B9A3BD6240B4D8395CF1_AdjustorThunk,
	NativeList_1_set_Item_m30347B8869454C74E36A9479132E95C1C2282C01_AdjustorThunk,
	NativeList_1_set_Length_m49FDBFBB875686DADE2EF40A24625D87D6893E6A_AdjustorThunk,
	NativeSlice_1_SliceConvert_TisIl2CppFullySharedGenericStruct_m95D4A04F6012B6943507CFAECA0DFAE7A1D26AB1_AdjustorThunk,
	NativeSlice_1_SliceWithStride_TisIl2CppFullySharedGenericStruct_m3CBBB7F7C24E30E29495D156CD61D886436BB645_AdjustorThunk,
	NativeSlice_1_SliceWithStride_TisIl2CppFullySharedGenericStruct_mEE3A5892BF7F5897108DA26224AD248912AE0099_AdjustorThunk,
	NativeSlice_1__ctor_m606E9478EC6822C3776B093EFC3DC98678E00F9A_AdjustorThunk,
	NativeSlice_1__ctor_m89E2A81C9B0649A573BC279C2AA35CAF771B8B7D_AdjustorThunk,
	NativeSlice_1__ctor_mFB24F80C08D8E11398A46C05BF60BC2DC06F71E9_AdjustorThunk,
	NativeSlice_1__ctor_m313B1A91AB4ADBA821C074187D67957126F93DFF_AdjustorThunk,
	NativeSlice_1__ctor_m4C0C75BC6633B56253FD4A7CD7F38A34E73C6253_AdjustorThunk,
	NativeSlice_1_CheckReadIndex_mCEFD744F532EA8119235FCB631C44E0B48E3C583_AdjustorThunk,
	NativeSlice_1_CheckWriteIndex_m8B0937EED6BF8634106BE95140CE8A5AB9F873C2_AdjustorThunk,
	NativeSlice_1_CopyFrom_mEC5EDFAA67CE0B371247FCF1CAB5D9471B33681F_AdjustorThunk,
	NativeSlice_1_CopyFrom_mDC4D4CE8165BCA0DF465BA4F0B381EC4EB400379_AdjustorThunk,
	NativeSlice_1_CopyTo_m0B252F8E50B4792025D47383A4F5521AF80B747D_AdjustorThunk,
	NativeSlice_1_CopyTo_m2BD0A050C86D24036D2E97A444931C7E7D25E5F8_AdjustorThunk,
	NativeSlice_1_Equals_m02465844945FD2B47F0A98A9050960DBE3AE1B3F_AdjustorThunk,
	NativeSlice_1_Equals_m62EA67D83A6D9F7C01046E9427C883C1E8A6C872_AdjustorThunk,
	NativeSlice_1_GetEnumerator_mF660D58D227B89E1A0B3B8273F3350163CEA1112_AdjustorThunk,
	NativeSlice_1_GetHashCode_m15101FD5A9795DB87AE06FF9D749AD7D79D9E09C_AdjustorThunk,
	NativeSlice_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mB8DB4618FC8BF5F06A40176413B052895F44439D_AdjustorThunk,
	NativeSlice_1_System_Collections_IEnumerable_GetEnumerator_mEA7995EB021548CB2B13B07DEB57985095EB7161_AdjustorThunk,
	NativeSlice_1_ToArray_m015E65BBFE8EB970B4FF4ACAB91FD4B952A3F0E0_AdjustorThunk,
	NativeSlice_1_get_Item_mB5E7BDE4EDF7E31480A9785BE103CC7E17E0E3A8_AdjustorThunk,
	NativeSlice_1_get_Length_m0225CA0944599882AC9C2A06A99FDC685362AFBE_AdjustorThunk,
	NativeSlice_1_get_Stride_m3F2ACE95526BFFDE7967887D3C6188F286C6A12D_AdjustorThunk,
	NativeSlice_1_set_Item_mAFD3C724B49679743057B7DE15937A2D78EB2339_AdjustorThunk,
	NodePath__ctor_mA9A74612E0785F30CCC660C7936418FD57A9EB55_AdjustorThunk,
	Nullable_1__ctor_m4257D7FF23A495D1B204F20330FBDED58248E4CC_AdjustorThunk,
	Nullable_1_Equals_m9563DBFA2EA4159645E91A19EAEF1F30B96B0482_AdjustorThunk,
	Nullable_1_GetHashCode_mA01CD04085D8BE8763C59776251FB3D85411024E_AdjustorThunk,
	Nullable_1_GetValueOrDefault_mC057FBD944AF068B90EBDD0B496231A01B2A4228_AdjustorThunk,
	Nullable_1_GetValueOrDefault_m7CC659C1A1F8403C8CE47BB7B66152FA3B054598_AdjustorThunk,
	Nullable_1_ToString_mA7FCB0708C4028709EB48813E00AE0B2F29688A6_AdjustorThunk,
	Nullable_1_get_HasValue_m14F273FB376DF00D727434CDCD28AB4EDCC14C3C_AdjustorThunk,
	Nullable_1_get_Value_mA083C4D9192050DC38513BDD9D364C5C68A3A675_AdjustorThunk,
	Pair_2__ctor_mCA54688368FE894C9F314471A3DA94A72B709F51_AdjustorThunk,
	Pair_2_ToString_m085342B96D11597F12C847BB5175075784F51E5B_AdjustorThunk,
	ParallelWriter__ctor_m90A3784A8D355193CE6FFD0D49A8B0B9B950661A_AdjustorThunk,
	PooledObject_1__ctor_m26481DA76B39862752040C3016392A923303D122_AdjustorThunk,
	PooledObject_1_System_IDisposable_Dispose_mBDBE6E4606DF5793230E351CA0B89611C13606FC_AdjustorThunk,
	PropertyCollection_1__ctor_m0E8538F1E11CB09AB1E8846DB44D0B6806838688_AdjustorThunk,
	PropertyCollection_1__ctor_m0A7C407D8B67A170E2EFD9899B52ABFDE4BCB886_AdjustorThunk,
	PropertyCollection_1__ctor_m9E9AD7E25A9F865F9C290CD6A8E6A6DED56B358B_AdjustorThunk,
	PropertyCollection_1_GetEnumerator_mD933057B218712B4B2DAFBF6E423497F0D578261_AdjustorThunk,
	PropertyCollection_1_System_Collections_Generic_IEnumerableU3CUnity_Properties_IPropertyU3CTContainerU3EU3E_GetEnumerator_m64C5ED91B740D237B110478C30C15996C06199F4_AdjustorThunk,
	PropertyCollection_1_System_Collections_IEnumerable_GetEnumerator_m91E0BFAF4178B55606E909C44EDAEA3A543AD5B5_AdjustorThunk,
	RBTreeEnumerator__ctor_m4F958B5E04B28EDEA0C7B8FB8E7CF4ED0739BBEB_AdjustorThunk,
	RBTreeEnumerator__ctor_mC7027B6747A32BD6A978E0DA6DE74FA6DB6AEA5C_AdjustorThunk,
	RBTreeEnumerator_Dispose_m44368C76E9C0F3A17DB3342C808CA8F9582C5955_AdjustorThunk,
	RBTreeEnumerator_MoveNext_mB5562C7DBD432B39AC6CDE17139006D3141A2E7E_AdjustorThunk,
	RBTreeEnumerator_System_Collections_IEnumerator_Reset_m30C1ACC2356659BDDEC767619AA0D8B5EA06E470_AdjustorThunk,
	RBTreeEnumerator_System_Collections_IEnumerator_get_Current_mD023E34D3906399CCC1A8E8AB645C5C9810898FB_AdjustorThunk,
	RBTreeEnumerator_get_Current_m55E7496F0199640CB43772D3C21F0A10462AE734_AdjustorThunk,
	ReadOnly_Reinterpret_TisIl2CppFullySharedGenericStruct_m5605CC31285684E9A024E62D6D5C680A798020E0_AdjustorThunk,
	ReadOnly__ctor_mB05017BDF2B337BE34CC3E2F090BD7A67F155549_AdjustorThunk,
	ReadOnly_AsReadOnlySpan_m64C20F2CC8054C58682D3CB09F2D1B7109C17950_AdjustorThunk,
	ReadOnly_CheckElementReadAccess_m785E3B2561122A22110209C8643E4D1A37879538_AdjustorThunk,
	ReadOnly_CopyTo_mF33553F53E45582FE86F5FB738A50F5929AD5A3E_AdjustorThunk,
	ReadOnly_CopyTo_m22CB48ADD0104582FDC3CF2581B8A7BF187FD3DD_AdjustorThunk,
	ReadOnly_GetEnumerator_m1BA5BE1261F5F3B4965AEDA65F7D33ACB9C31117_AdjustorThunk,
	ReadOnly_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mD1B3924A7A3A2B202FABF9D1A8391737F0CB3943_AdjustorThunk,
	ReadOnly_System_Collections_IEnumerable_GetEnumerator_mEDCB287C2177C8DCFDE2136300142D738D5594F4_AdjustorThunk,
	ReadOnly_ToArray_mFD80D7B7BE13A74B4BE53E54CE3BCB5300F46D27_AdjustorThunk,
	ReadOnly_get_IsCreated_mADD8624D940450CB4F64C20BCFEFAC0065E485A2_AdjustorThunk,
	ReadOnly_get_Item_mAD85B6B3DEDEACD9CB3738C91AE09F7B0102B15D_AdjustorThunk,
	ReadOnly_get_Length_m0A746E7788B5229B9FA65917E34CEAC58D76B351_AdjustorThunk,
	ReadOnly_System_Collections_Generic_IEnumerableU3CUnity_Collections_KVPairU3CTKeyU2CTValueU3EU3E_GetEnumerator_m6D89344EA22D40C667DD941E4B2C2D5D967BD4FE_AdjustorThunk,
	ReadOnly_System_Collections_IEnumerable_GetEnumerator_mFAFF5C0905599BCEC904BDB222C31534F8F299E8_AdjustorThunk,
	ReadOnlyMemory_1__ctor_m499964C79FA86289EF11F618EF98F63259D680F6_AdjustorThunk,
	ReadOnlyMemory_1__ctor_m1FD19CA8A2CB8A92DC095EE34D414ADE2FD39A12_AdjustorThunk,
	ReadOnlyMemory_1__ctor_m10BC8BCF1707700EF96C3A32CA7F3349F4080508_AdjustorThunk,
	ReadOnlyMemory_1_CopyTo_m27F26CD338FEA8EBF4C636CC610BD3D0A596B446_AdjustorThunk,
	ReadOnlyMemory_1_Equals_mF7DA75997B4FFDFB2D6F953E8DC580EC3C941A61_AdjustorThunk,
	ReadOnlyMemory_1_Equals_m2C16DF2AE78D8E7182CFAE8E3E07C3B20A777888_AdjustorThunk,
	ReadOnlyMemory_1_GetHashCode_m96535F6C6512A16E6DBC2A8341F8FECDF2E47F54_AdjustorThunk,
	ReadOnlyMemory_1_GetObjectStartLength_m76D2DB58E89EF8DAAB0BEEC5E80CC99E7C3CB752_AdjustorThunk,
	ReadOnlyMemory_1_Pin_m0F5197F4F1ABABE6C5A8976AEBC8243711CD3709_AdjustorThunk,
	ReadOnlyMemory_1_Slice_m068EB458013792137E461EA951FF873B6A3E185E_AdjustorThunk,
	ReadOnlyMemory_1_Slice_mDFC40FA7D3D023B52F861034F4368E6BE00B47C3_AdjustorThunk,
	ReadOnlyMemory_1_ToArray_m44807420910F7442D09F80751674CF30711B0764_AdjustorThunk,
	ReadOnlyMemory_1_ToString_m1AB8B8725FFC7144748E59C73875E2C6822E2321_AdjustorThunk,
	ReadOnlyMemory_1_TryCopyTo_m7B61F10E7E32DDFD439E77FF7BBB31AEBDAD1CDA_AdjustorThunk,
	ReadOnlyMemory_1_get_IsEmpty_m09A6A2BB7D8CFDDD7CA4DE125253FA884283A59C_AdjustorThunk,
	ReadOnlyMemory_1_get_Length_m65A4E9056707DCDBB66443924288D7E0BA7D0074_AdjustorThunk,
	ReadOnlyMemory_1_get_Span_m3BBCF2EFAFAB7DAA8882208AF319487E00DC3895_AdjustorThunk,
	RentArray_1__ctor_mE3E2E7E996AC6814E25FF061694B621203718996_AdjustorThunk,
	RentArray_1_Dispose_mADD3D61E5163B43B6945A4D4A66C492DA34721AC_AdjustorThunk,
	RentArray_1_DisposeManually_m85CBD0E938DF7FD532BA929C5B5696BC9DD77129_AdjustorThunk,
	ScriptPlayable_1__ctor_m63AE9D2F322913109CA948EDC429CB9CC2FD8516_AdjustorThunk,
	ScriptPlayable_1_Equals_mC3D0F2978F3E80114ADFB3D17BDB38DE6B299ACE_AdjustorThunk,
	ScriptPlayable_1_GetBehaviour_m86713AAFF1D1F7D4FB370489F71AE4228741EFE8_AdjustorThunk,
	ScriptPlayable_1_GetHandle_mFBB086A8188A0D77BB5CF4A1A03031EA9B67D22A_AdjustorThunk,
	SharedStatic_1__ctor_m467F9A64986F442AA4853C5C314D0A54D887CDDC_AdjustorThunk,
	SharedStatic_1_get_Data_m679BD82198B4EC1D89F2EDE946A60F4DEE8E47E2_AdjustorThunk,
	SharedStatic_1_get_UnsafeDataPointer_m3F411F556361D224096615DAA35B86D6B725C09D_AdjustorThunk,
	SharedStatic_1__ctor_m57842D87210A109206E3DAFEBD441B46EDBC809E_AdjustorThunk,
	SharedStatic_1_get_Data_m4D3D9A03646881BE9065C8939BF1CA28195FF262_AdjustorThunk,
	SparselyPopulatedArrayAddInfo_1__ctor_m323E378EC0EE0C48A24AA0E14E40D7B020BB0458_AdjustorThunk,
	SparselyPopulatedArrayAddInfo_1_get_Index_mCBBF3F010A994612AC94DA417AB8D04A2C92B45A_AdjustorThunk,
	SparselyPopulatedArrayAddInfo_1_get_Source_mBA043CE79666AA955D0FE4335ED3B82802E75C86_AdjustorThunk,
	StyleDataRef_1_Acquire_m280DBCF2EC3FCBCE18CEBDC5679B88754FF6E20E_AdjustorThunk,
	StyleDataRef_1_CopyFrom_m202E0E466F8BBC806DE35A1E6B0ECB88CB087D4E_AdjustorThunk,
	StyleDataRef_1_Equals_m37FFDDD96CA2205E2E23202FED5DDEC5C1008E3E_AdjustorThunk,
	StyleDataRef_1_Equals_m7ADB463EAFC073CFB4C9A9BACA0FE52F67A59118_AdjustorThunk,
	StyleDataRef_1_GetHashCode_m9D45C4A9A08526FE0FE86F68B23EE4F22CE50B9C_AdjustorThunk,
	StyleDataRef_1_Read_m91033FE9A7F22D88A387AF7C0F02BC6C84567CA8_AdjustorThunk,
	StyleDataRef_1_ReferenceEquals_m701B72F6C9809D21F952990782ACB6211760F69B_AdjustorThunk,
	StyleDataRef_1_Release_mD49B706BA9CDDAC374D9733812BA6F07277B7AE8_AdjustorThunk,
	StyleDataRef_1_Write_m59356C10374B7A043341F241B1C8F16FF6B17177_AdjustorThunk,
	StyleDataRef_1_get_id_m911C0DB700108F5CEACB5A891BC5907B1DAF6B0A_AdjustorThunk,
	StyleDataRef_1_get_refCount_mE06114815FAE2B0330A77036CDB5CFA909D05307_AdjustorThunk,
	StyleEnum_1__ctor_m098CD8479B142F8A1041BAE3DBB39CB44CBD10C5_AdjustorThunk,
	StyleEnum_1__ctor_m12078C14D0B69B284AFA4CD626F791C75FD9F5B9_AdjustorThunk,
	StyleEnum_1__ctor_mAF41CAADB2D61B5861967141DE9BCFD6EA66DC40_AdjustorThunk,
	StyleEnum_1_Equals_m35F52A3A4D787AB2FC378F40FDF39543F72FC97D_AdjustorThunk,
	StyleEnum_1_Equals_mE489D9352D87217721642E46C4EFD2216E219967_AdjustorThunk,
	StyleEnum_1_GetHashCode_m6DE12566480AA4B9B8B4B99568482E501F8C80B3_AdjustorThunk,
	StyleEnum_1_ToString_m648E71C585F6EF9216C5CF7EA096E4AE1ADC0AC6_AdjustorThunk,
	StyleEnum_1_get_keyword_m760AB801D94DF23D10535741365D6753632295E6_AdjustorThunk,
	StyleEnum_1_get_value_m46D4F7BEA4855BD3882B5F4692086C84C6174A92_AdjustorThunk,
	StyleEnum_1_set_keyword_m122C65127E2AFFCB5E896D01C961BB438C8B6937_AdjustorThunk,
	StyleEnum_1_set_value_mF2E141FA5D14DCA317134E4CE1AAF805BEE79D99_AdjustorThunk,
	StyleList_1__ctor_m333E7B6EA3C2FC53814EDC3656A2E4D13836F26A_AdjustorThunk,
	StyleList_1__ctor_m1E496823D7D31CB43EEB6B43F7790ED601CB673A_AdjustorThunk,
	StyleList_1__ctor_mBC7A084C97F0F7B884ED94CFFE16F937D60CCED3_AdjustorThunk,
	StyleList_1_Equals_mCB0855F7F81E96EF2A4619A830ADE420F87B92D0_AdjustorThunk,
	StyleList_1_Equals_m9AC92EF88DE3B421DB485613F276789C3CE49625_AdjustorThunk,
	StyleList_1_GetHashCode_mA134CD3E8F97859CDE6CB0464067DA5B6881863E_AdjustorThunk,
	StyleList_1_ToString_mD110328BDAC3B05B2F3406D3178B8FEFAE66E02D_AdjustorThunk,
	StyleList_1_get_keyword_m28C3A94C237B251861E39930D591248D28587F01_AdjustorThunk,
	StyleList_1_get_value_m3BC32FA5CA94A2A59B8C80EBEE844B064C63D68C_AdjustorThunk,
	StyleList_1_set_keyword_mB5843B33643AD8D57109BDD664B69C7A72DFFF27_AdjustorThunk,
	StyleList_1_set_value_m61C01B4E5B57572F93B48A871AD54700DFEF9A3C_AdjustorThunk,
	TMP_TextProcessingStack_1__ctor_mC0685A91667DFC9925767472A6E98CB57BC6E8B7_AdjustorThunk,
	TMP_TextProcessingStack_1__ctor_mB5B6585D0032E8D8768C2651C033827F6DBA7034_AdjustorThunk,
	TMP_TextProcessingStack_1__ctor_m7B96812F826309F2ECD5AC73A882940FB618C52D_AdjustorThunk,
	TMP_TextProcessingStack_1_Add_m248525D1C0A87CC5664F0F76FF02B7FF626B0C59_AdjustorThunk,
	TMP_TextProcessingStack_1_Clear_m5B8F995528E4DB2F1177A7B3B4E806DAB18C9685_AdjustorThunk,
	TMP_TextProcessingStack_1_CurrentItem_m56E11105E8B704235B8DFB1A260090D688895F0D_AdjustorThunk,
	TMP_TextProcessingStack_1_Peek_mF7B4050F25F0B33220443A6A19D4465E36A7356C_AdjustorThunk,
	TMP_TextProcessingStack_1_Pop_m988B6D5B3E24937F963C9AB1A16240A1E4A0D1D3_AdjustorThunk,
	TMP_TextProcessingStack_1_PreviousItem_m5C228E38434E671964665E55379F16DC1345A914_AdjustorThunk,
	TMP_TextProcessingStack_1_Push_m7A60BD56D66B9FB81107DC6A78C3E8AFB12B618C_AdjustorThunk,
	TMP_TextProcessingStack_1_Remove_mB5F58B12D1767F89DE2C328D949CF0BD9DEBC656_AdjustorThunk,
	TMP_TextProcessingStack_1_SetDefault_m3FA35ECFD02705D964BB1274304892CC27CADB51_AdjustorThunk,
	TMP_TextProcessingStack_1_get_Count_m9FF7B40FB58184A477384BA3D58E97729DECC6B4_AdjustorThunk,
	TMP_TextProcessingStack_1_get_current_mB3845029EE3EE8D0C0EBF05DACC242E8C72155BD_AdjustorThunk,
	TMP_TextProcessingStack_1_get_rolloverSize_m048FA3E2119E34E520AE3015B813C24992092583_AdjustorThunk,
	TMP_TextProcessingStack_1_set_rolloverSize_m9B297CB1848946D09147926310F5B2F989EB9D5D_AdjustorThunk,
	TaskAwaiter_1__ctor_m33D20D5D1F650793298C91D129010A10D7176FFD_AdjustorThunk,
	TaskAwaiter_1_GetResult_mA0FDEC1F33CAC08401C6F3B9E5A5C6F1B4503EEB_AdjustorThunk,
	TaskAwaiter_1_UnsafeOnCompleted_m8B9DC9D7FF95BB8129F4A5D5CF2DF0AE62E081E3_AdjustorThunk,
	TaskAwaiter_1_get_IsCompleted_m7F153D4DF6456F60BABB1E7663CDDF3EFE172007_AdjustorThunk,
	TaskPool_1_TryPop_m90F71FE3BA3FC7353C008C48842AE70D358E6D76_AdjustorThunk,
	TaskPool_1_TryPush_mAC9F8F3F6E70F2F279D0200E089A1C122A5D77E4_AdjustorThunk,
	TaskPool_1_get_Size_mA6E840439A90DAC450C66102D1940FEC10734999_AdjustorThunk,
	TextProcessingStack_1__ctor_m0FC3CEAC0DDDFF6286541241CB857AF50C5A902A_AdjustorThunk,
	TextProcessingStack_1__ctor_m58B4B2FDC40E86003BB9D6C7915682C6913F9CD0_AdjustorThunk,
	TextProcessingStack_1__ctor_m8AC2C57EC94FA3001FF52C2ED5D645B7562C7AED_AdjustorThunk,
	TextProcessingStack_1_Add_mCF77A6D04A7073AF3B3567DBE467B2FFDADE761E_AdjustorThunk,
	TextProcessingStack_1_Clear_m035C75BF8F609D61E1DFE28E4562BDB153767D9B_AdjustorThunk,
	TextProcessingStack_1_CurrentItem_m391132854FBCFFEF6E24BA7A0702428DB40D6995_AdjustorThunk,
	TextProcessingStack_1_Peek_mC737683E45A62573A24643E87A99F1AAC8EB405E_AdjustorThunk,
	TextProcessingStack_1_Pop_m56AD5DA4F9270C4AE36BDE85BA774AF5D2EA94D4_AdjustorThunk,
	TextProcessingStack_1_PreviousItem_mDD8857B37F66B4B261BC52ADC0A6ED128281A9C7_AdjustorThunk,
	TextProcessingStack_1_Push_mDB20BD1D2A8E67A798610E28B8D7B0C8740CBE33_AdjustorThunk,
	TextProcessingStack_1_Remove_m39EAC0C6497D42164E0388A42A7BF94F3D1A426C_AdjustorThunk,
	TextProcessingStack_1_SetDefault_mE48E32BB264F7043E8473984493F95B9F23F9D5E_AdjustorThunk,
	TextProcessingStack_1_get_Count_m67CA9DEEEC5AE089020715679DA79E883D59FE99_AdjustorThunk,
	TextProcessingStack_1_get_current_mB594B5CC26EB0A7E99F76C90A4F28CAF6CCB145D_AdjustorThunk,
	TextProcessingStack_1_get_rolloverSize_m11BD7543661CF955F6B203F7EF53D7B550643B24_AdjustorThunk,
	TextProcessingStack_1_set_rolloverSize_mD2C4E5D6DA61E38220C30E05D4CF79F0D91D313C_AdjustorThunk,
	TreeData_1__ctor_mBAA896CCBE0AA825D700E5559353BE8587C1424A_AdjustorThunk,
	TreeData_1_AddItem_m53A8A34BB24B49FBEFFB53A21F269CCCEF648F08_AdjustorThunk,
	TreeData_1_AddItemToParent_m780A3FDB6A1188BEA1309A0AE9BB25AD29D40161_AdjustorThunk,
	TreeData_1_BuildTree_mD166D8360BFC596078297B378D5D986DF13A0731_AdjustorThunk,
	TreeData_1_GetDataForId_m9690E042F292CEBB8ECB763317BA9609FF756845_AdjustorThunk,
	TreeData_1_GetParentId_m4CE1D6161FAB0FD1ACA997479BAE91171CEDEE9B_AdjustorThunk,
	TreeData_1_HasAncestor_mBDE8B6AAE139D95CCCC57585C9215776FADA79B2_AdjustorThunk,
	TreeData_1_Move_m14B65B5E9B4AE186320510D7CAE612E2EB51F754_AdjustorThunk,
	TreeData_1_RefreshTree_m2ED2991C1256D4D87520C98B912C98A85CD85065_AdjustorThunk,
	TreeData_1_RemoveFromParent_m61ABB21A998047C53B79949DF81E230F0724B35D_AdjustorThunk,
	TreeData_1_TryRemove_m819B318E7B2046685FFCA06BB29FF9857AE49F22_AdjustorThunk,
	TreeData_1_TryRemoveChildrenIds_mE741422819F243163B839D863C6A37ABBB1D4E27_AdjustorThunk,
	TreeData_1_UpdateParentTree_m2081FD7D8124B87D56033B37E0F87A3E19D05815_AdjustorThunk,
	TreeData_1_get_rootItemIds_mFF10BB5A8020FDE42204D4ABCA2407DCEA372E13_AdjustorThunk,
	TreeViewItemData_1__ctor_m13FDCF45EF39F25E38B74175D1E3808996B6E39D_AdjustorThunk,
	TreeViewItemData_1_AddChild_mB937584D40D5080ED0F3F21C9846A6182D8AE4FB_AdjustorThunk,
	TreeViewItemData_1_AddChildren_m88170DDB56BC6F4A3327DDE8A70FB8F5874EB5BA_AdjustorThunk,
	TreeViewItemData_1_GetChildIndex_mAF9A92B34A982331F790E760FD1278C0747C0B0C_AdjustorThunk,
	TreeViewItemData_1_InsertChild_mD024A216C9B3FC87615A2020A5FEF9278DD251BE_AdjustorThunk,
	TreeViewItemData_1_RemoveChild_m1FEFFA22DAB2CB0EAEC760B9D8E516EDDA91078F_AdjustorThunk,
	TreeViewItemData_1_ReplaceChild_m809FD903843DAA5A68A0BB8894CA4E90DEAA0E33_AdjustorThunk,
	TreeViewItemData_1_get_children_mDF10B651BB3421F040A1F1D85F59E6C6A4FC2E21_AdjustorThunk,
	TreeViewItemData_1_get_data_mC3A73BA0B7B186E5674660F3B0A4D6D08D37216B_AdjustorThunk,
	TreeViewItemData_1_get_hasChildren_m884635D21B4D6454E480EF0C2DE596C3F81107FB_AdjustorThunk,
	TreeViewItemData_1_get_id_m8636291EDF9E60912758667BCAC0037EE002E3D4_AdjustorThunk,
	TriggerEvent_1_Add_m099707EB523C057C0893F6205B24E2BFF93D755B_AdjustorThunk,
	TriggerEvent_1_LogError_m7F2F0D3F680FA92149285B385339F16B50951364_AdjustorThunk,
	TriggerEvent_1_Remove_m4E517F1987E502F01B96F58F94A5AF3F4B28724D_AdjustorThunk,
	TriggerEvent_1_SetCompleted_m3E9328515C580B4E5698CF720026119EBC6A8421_AdjustorThunk,
	TriggerEvent_1_SetResult_m76ECB482F9A180878BE4539A553D4FC8AF4B5691_AdjustorThunk,
	UQueryBuilder_1_AddRelationship_TisRuntimeObject_mCE90C2CFF3D74498DB002082A6C369476CF60A1E_AdjustorThunk,
	UQueryBuilder_1_AddType_TisRuntimeObject_mAA792DDF5F5D7C04FAE62C49830117857251CBD8_AdjustorThunk,
	UQueryBuilder_1_Children_TisRuntimeObject_m61D8848C1C54D2DD978DD5AAC64C7A43BC15721E_AdjustorThunk,
	UQueryBuilder_1_Children_TisRuntimeObject_mEDAB1BB2C9AC80AA6745897236806154F343A948_AdjustorThunk,
	UQueryBuilder_1_Descendents_TisRuntimeObject_m2139DC8B45DB936419759E7E7A05BC8BF9FB1D80_AdjustorThunk,
	UQueryBuilder_1_Descendents_TisRuntimeObject_m288C5D7CE5E90BEA1A8E3AFCDA6FC32CFA5FEA82_AdjustorThunk,
	UQueryBuilder_1_ForEach_TisIl2CppFullySharedGenericAny_m26541CE74E5AD2D165E96E05155B822676EAF54C_AdjustorThunk,
	UQueryBuilder_1_ForEach_TisIl2CppFullySharedGenericAny_m3C0FDD427EB640B1AE1BC7655C1219A368331F5A_AdjustorThunk,
	UQueryBuilder_1_OfType_TisRuntimeObject_mE7DE3EF3A29D06AB2EA8C78B1837489AB6874C28_AdjustorThunk,
	UQueryBuilder_1_OfType_TisRuntimeObject_m6A9DF3D9E7A19FA293DE237F51E02D0BB8FCF616_AdjustorThunk,
	UQueryBuilder_1__ctor_m9C0EC0A8947CAE0CD99B7FC847861454BE629EF6_AdjustorThunk,
	UQueryBuilder_1_Active_m7B033B36BE6E1EBEA505238B59F315482A31CBB8_AdjustorThunk,
	UQueryBuilder_1_AddClass_m024380A53D91A5E74E24ED97267B79C3F844DDA6_AdjustorThunk,
	UQueryBuilder_1_AddClasses_mC48D45DA3DA18F0656C200817455FAB79FDA6D58_AdjustorThunk,
	UQueryBuilder_1_AddName_m05D0503DFB262662CD42472C45C0B29145CBA547_AdjustorThunk,
	UQueryBuilder_1_AddNegativePseudoState_mC04225C36F5118EB6E8C6B738390CA7C7EDA786D_AdjustorThunk,
	UQueryBuilder_1_AddPseudoState_m5BA527033C9E13ABA265EE6AAD0F7632FCE98139_AdjustorThunk,
	UQueryBuilder_1_AddPseudoStatesRuleIfNecessasy_m71FB68969B671E698FBE074CBFAAEDD0556B2CF5_AdjustorThunk,
	UQueryBuilder_1_AtIndex_m701E0E6C6C6AC97693D234A643D8ADF695F78B17_AdjustorThunk,
	UQueryBuilder_1_Build_m51E76A0E9FF868FC34DD84F7F6A4BC369D1A316B_AdjustorThunk,
	UQueryBuilder_1_Checked_m9F542E56ABB9753C3E6EA405CE5DC8F0F8E31B65_AdjustorThunk,
	UQueryBuilder_1_Class_m871FA9DA07723AFB1E4AD0ED23E6F50983ED5491_AdjustorThunk,
	UQueryBuilder_1_CurrentSelectorEmpty_m8A756E419D48BB5DA57FF3686CB960C31CD82A01_AdjustorThunk,
	UQueryBuilder_1_Enabled_mEDE39D81F9C178C50A175CD92A04EBD103B8A6FB_AdjustorThunk,
	UQueryBuilder_1_Equals_m810C3B33A1AC3150EE3C9F58323B18DE63E7BA12_AdjustorThunk,
	UQueryBuilder_1_Equals_m4C3C3AD813E72BEE6EA42912562E5100411B08F0_AdjustorThunk,
	UQueryBuilder_1_FinishCurrentSelector_m500B300233E4DD7110D6B33EA18DA0C326A80CA6_AdjustorThunk,
	UQueryBuilder_1_FinishSelector_mAFA278FA0C1BE641CAFE110E6A76855BBC126054_AdjustorThunk,
	UQueryBuilder_1_First_m0913A218B87E7FCDE96F46F3B1DDD80302B2432E_AdjustorThunk,
	UQueryBuilder_1_Focused_mCA50D395E834C466C071559DF4667572F82A9848_AdjustorThunk,
	UQueryBuilder_1_ForEach_mED54645051528F0BC37AAC896B57BEB7A9A19F06_AdjustorThunk,
	UQueryBuilder_1_GetHashCode_m662C33EBC9881854EE15E98428D555D2C28C369B_AdjustorThunk,
	UQueryBuilder_1_Hovered_m3BE92A961925670F643966A733537CA7FE031A5C_AdjustorThunk,
	UQueryBuilder_1_Last_mE32242A4967E36A2D53B60AE002A6C4AD9D96F60_AdjustorThunk,
	UQueryBuilder_1_Name_mF930BD3AB84A45024126BE05E64B1C951A46F344_AdjustorThunk,
	UQueryBuilder_1_NotActive_mCA59134C857570B8FFD3CC6722913A72A72CD957_AdjustorThunk,
	UQueryBuilder_1_NotChecked_mFEDA9E61B5E8CA292622B8051182DF476FD7DEBD_AdjustorThunk,
	UQueryBuilder_1_NotEnabled_m84508AD4DC060D5EA2F9836EEB48601924A9DE02_AdjustorThunk,
	UQueryBuilder_1_NotFocused_mA34269D9F676316B87F9A840CA513AE0457C86CD_AdjustorThunk,
	UQueryBuilder_1_NotHovered_m62B21D7FE0EDB536CD22BD561E32C234669D5783_AdjustorThunk,
	UQueryBuilder_1_NotSelected_mFA120E7078BDF1168DC4748387E2E5777591B07F_AdjustorThunk,
	UQueryBuilder_1_NotVisible_mAC36CEB7E4558FE652DA7C07A7744631A234F86F_AdjustorThunk,
	UQueryBuilder_1_Selected_m2ABFD315E96C4B65B79B6214C8AC5046D28286E8_AdjustorThunk,
	UQueryBuilder_1_SingleBaseType_m288935529BB5FA432732088C9DFFDA931371422E_AdjustorThunk,
	UQueryBuilder_1_ToList_m60AA6FCDA94BCA677C6C58C997CC018C52509995_AdjustorThunk,
	UQueryBuilder_1_ToList_m68596FEED660C9FF1D38DCCD32309E2F16435D37_AdjustorThunk,
	UQueryBuilder_1_Visible_m654F5F934E08BCB72702B8266309D429A0E44F50_AdjustorThunk,
	UQueryBuilder_1_Where_m50F18AA063808BC67E6E326B438FD5A485E1C2EA_AdjustorThunk,
	UQueryBuilder_1_get_parts_m5ECC58E4F2CE734BC0F4597D48FD51A243B9294F_AdjustorThunk,
	UQueryBuilder_1_get_styleSelectors_m1817BE5A446B8C9331E72A7206CFF86768F2A0EC_AdjustorThunk,
	UQueryState_1_ForEach_TisIl2CppFullySharedGenericAny_m684F55EEFC1F037371B16320426DF824E623A0ED_AdjustorThunk,
	UQueryState_1_ForEach_TisIl2CppFullySharedGenericAny_m9BFEDFED4193BE50F4B882A548479BC8363F6EF1_AdjustorThunk,
	UQueryState_1__ctor_m661A100C20F49D2E8A76BA57CE5774A562DC8FF8_AdjustorThunk,
	UQueryState_1_AtIndex_m2E2E9CB4F071A500429DD094D5FBEC25FEE56531_AdjustorThunk,
	UQueryState_1_Equals_m6824EF7324D356025924883F014E3AD22FAD72DA_AdjustorThunk,
	UQueryState_1_Equals_mD35A6743162EA3D3D4FBEC55AFEBFF08B6EF0379_AdjustorThunk,
	UQueryState_1_First_m19FF1885E9D1D57D0EBA715820CA3C02C2C9C363_AdjustorThunk,
	UQueryState_1_ForEach_m2016E47C98379A010EE78D09CFD8DF7625894301_AdjustorThunk,
	UQueryState_1_GetEnumerator_m78AA2D739336C3CBE72B4425D65C35F91853C367_AdjustorThunk,
	UQueryState_1_GetHashCode_m20DCD93A49AEC50C2B816604897E7C298AAD8058_AdjustorThunk,
	UQueryState_1_Last_m2B17175712632F4C3DE6A0581BC17B0182C76FE2_AdjustorThunk,
	UQueryState_1_RebuildOn_mF29E43348045B1219A757EBBF43C892C32EEA5DC_AdjustorThunk,
	UQueryState_1_Single_m96252EE5598F3DC4B72CE509362A0D7DFA20A40B_AdjustorThunk,
	UQueryState_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_m524A6E90BFB245E9643A8E23E28943DC0F8CAAAD_AdjustorThunk,
	UQueryState_1_System_Collections_IEnumerable_GetEnumerator_m817650F16F99755331C119D6979227201B14F689_AdjustorThunk,
	UQueryState_1_ToList_m908498BA91666931C25DD5A0A23AB194D0EA6560_AdjustorThunk,
	UQueryState_1_ToList_m9E511725E503E0D1B049A63696362FB0087D8D3E_AdjustorThunk,
	UniTaskCancelableAsyncEnumerable_1__ctor_mF3E1B424AE982C37B2AAE2B0168243E2B04B98B3_AdjustorThunk,
	UniTaskCancelableAsyncEnumerable_1_GetAsyncEnumerator_m40B1BF925C2526B24BDDBFE1B2D146E32751A870_AdjustorThunk,
	UniTaskCompletionSourceCore_1_GetResult_m212E0DA923DC1D1803D087419F049DFD9DBE51F9_AdjustorThunk,
	UniTaskCompletionSourceCore_1_GetStatus_m25012764771D843D048C0547C4D99B5F32E29B35_AdjustorThunk,
	UniTaskCompletionSourceCore_1_OnCompleted_mABABCB75E06A39AF02AEC067E42C0AEE73BFA015_AdjustorThunk,
	UniTaskCompletionSourceCore_1_ReportUnhandledError_mCB553FC688C1666335721D1F09AA5C00298F8571_AdjustorThunk,
	UniTaskCompletionSourceCore_1_Reset_m788665B21E38E2A5451D5A30194E957469083C5C_AdjustorThunk,
	UniTaskCompletionSourceCore_1_TrySetCanceled_mEA4B7341003C6B1365405F43730371BF3D935E14_AdjustorThunk,
	UniTaskCompletionSourceCore_1_TrySetException_mF9AB6AE834A9D074A3E16E3C6B7E7821DA5936AB_AdjustorThunk,
	UniTaskCompletionSourceCore_1_TrySetResult_m7DA02F53706893C67B6747B3F95C9B82D0946D40_AdjustorThunk,
	UniTaskCompletionSourceCore_1_UnsafeGetStatus_mB6C5DA816F12A340957522E0BA82D26629CC8E44_AdjustorThunk,
	UniTaskCompletionSourceCore_1_ValidateToken_m0D278FBD064187C302FE1582CA68EEF557E7E3EE_AdjustorThunk,
	UniTaskCompletionSourceCore_1_get_Version_mC206FEA615DEB3676B72991ABAE79848523CAC0B_AdjustorThunk,
	UniTask_1__ctor_m0F12D2AF19A9F3F86DA3D09D780E58868C07EA89_AdjustorThunk,
	UniTask_1__ctor_m608BBDBA054799FF72D4ED7758DA9BA32EBB8F22_AdjustorThunk,
	UniTask_1_GetAwaiter_mEC480E8F8586214E6ECD880FBFAA9C8CB1C963ED_AdjustorThunk,
	UniTask_1_ToString_m829248BDF095544BC67FC8B91BA828DBFBBE4601_AdjustorThunk,
	UniTask_1_get_Status_mA1326B91312FE816FF0720BF512F4C90E72B6CEA_AdjustorThunk,
	UnmanagedArray_1_Dispose_mBAF9B42AE46BCDFC015F71B39AAA93C8065EDA22_AdjustorThunk,
	UnmanagedArray_1_get_Item_m05D75737AB1F82959AD5DB58D5AF0E8F310F8481_AdjustorThunk,
	UnsafeHashMap_2_Dispose_mC5832831A098A0C1453F76414130051BD4551445_AdjustorThunk,
	UnsafeHashMap_2_System_Collections_Generic_IEnumerableU3CUnity_Collections_KVPairU3CTKeyU2CTValueU3EU3E_GetEnumerator_m213433CFC5779226609C25F9233C72EDF0F42F50_AdjustorThunk,
	UnsafeHashMap_2_System_Collections_IEnumerable_GetEnumerator_mA665362F1662B06D61798E982BB434109B4D08E1_AdjustorThunk,
	UnsafeHashMap_2_get_IsCreated_m829E792B858A2DAA2FE81CC44146A9189BB243F2_AdjustorThunk,
	UnsafeList_1_Dispose_TisIl2CppFullySharedGenericStruct_mE4A291BF549BB5ADA342FB04746C5E75D2851CC2_AdjustorThunk,
	UnsafeList_1_ResizeExact_TisIl2CppFullySharedGenericStruct_mCEE0EEBD50C687E281D02DA756FF1F08927B89CF_AdjustorThunk,
	UnsafeList_1_SetCapacity_TisIl2CppFullySharedGenericStruct_mE9376397B41232E820D706511C7A7D8A818EBE83_AdjustorThunk,
	UnsafeList_1__ctor_m77340A75055228D3857F5DCDDE675BB31951957A_AdjustorThunk,
	UnsafeList_1_Add_m409DAD25A7C3746AA3B1224872A3E79020FF5711_AdjustorThunk,
	UnsafeList_1_AddNoResize_m537D4EB76C2A0A23494E32D37E044764DAB89F6F_AdjustorThunk,
	UnsafeList_1_AddRange_mC9AB6AD54ED7854723E3821BA1D3CCD1FBB8207C_AdjustorThunk,
	UnsafeList_1_AddRangeNoResize_m809794F219ECE3B47B9AC4E9714F90561223A0D9_AdjustorThunk,
	UnsafeList_1_AddRangeNoResize_m4B0C28517675234B04F6AF9E3D3AC53E1F7D2B2D_AdjustorThunk,
	UnsafeList_1_AddReplicate_mCD1778D61A491577966A18ADA7F3041D77A054F5_AdjustorThunk,
	UnsafeList_1_Clear_m720AD7943BF8DF9949FD0AD21A25E9EADD97DA7F_AdjustorThunk,
	UnsafeList_1_CopyFrom_m71D334AE652996B990635A7F4621CCA5DA993D94_AdjustorThunk,
	UnsafeList_1_CopyFrom_m0753F3589416C7223C92570AA2B9D458C10AAD7E_AdjustorThunk,
	UnsafeList_1_Dispose_m40FE65A111C7CF6FB979B4AA2DBB0EAF2881D734_AdjustorThunk,
	UnsafeList_1_ElementAt_mF9FA891787954F18D91AE55D96B428C168706731_AdjustorThunk,
	UnsafeList_1_InsertRangeWithBeginEnd_mFB76DC3407CA799EC64AE8F1BFE765A91D458998_AdjustorThunk,
	UnsafeList_1_RemoveAt_m62B4AE5FE18470F9F8DF5979C721230185D58DA3_AdjustorThunk,
	UnsafeList_1_RemoveAtSwapBack_mDAA38AE9EB799E4FE6C8144FC24F19CBE6527144_AdjustorThunk,
	UnsafeList_1_RemoveRange_m5502595ABECDC51DDFFD4B5A08B050C670FAF9D4_AdjustorThunk,
	UnsafeList_1_RemoveRangeSwapBack_m6100DA6FD1302DAA3A6C20420A4D357C8FD8A918_AdjustorThunk,
	UnsafeList_1_Resize_m1F99567F0B3EFCD21227B49E3BFF2858AD4082DB_AdjustorThunk,
	UnsafeList_1_ResizeExact_m285C4C95E502AED13C29541360D2EAF92C3D0EC7_AdjustorThunk,
	UnsafeList_1_SetCapacity_m1A26A7EC9C2610C1842C8FD2B3247CAB52BA3F3D_AdjustorThunk,
	UnsafeList_1_System_Collections_Generic_IEnumerableU3CTU3E_GetEnumerator_mCB2C19A74F8BEDABFA4D327317EB968B261AD4A9_AdjustorThunk,
	UnsafeList_1_System_Collections_IEnumerable_GetEnumerator_mCAF80DF7CB4AF09DB010BBC3E142B258C700F281_AdjustorThunk,
	UnsafeList_1_TrimExcess_m9B0C5809F881B2178E4314E6F215EBC62DCE45C9_AdjustorThunk,
	UnsafeList_1_get_Capacity_m163371206E2F39354C1DF26C807700F2FDF08C8B_AdjustorThunk,
	UnsafeList_1_get_IsCreated_mAE8AA411D9B1BD097C854F0657BAC04053C27B7F_AdjustorThunk,
	UnsafeList_1_get_IsEmpty_mF548243ADB6DEC9F961B55925E75369B0EF94A5E_AdjustorThunk,
	UnsafeList_1_get_Item_mB847AC77ACE34F511CEABF3E45B6EAD5D59165AF_AdjustorThunk,
	UnsafeList_1_get_Length_m35C71DFABA31811E9ABCD2FF56F066B449E3C84A_AdjustorThunk,
	UnsafeList_1_set_Capacity_m55A593E215A735D7C440BFB62D75BE4C186E038B_AdjustorThunk,
	UnsafeList_1_set_Item_m61F9495A96E1D08A222DEB26C1FC24A279807D53_AdjustorThunk,
	UnsafeList_1_set_Length_mA84640FEE498E051FBB8C91DCDC4F718295BBA8A_AdjustorThunk,
	UnsafeQueue_1_Dispose_m6A73C25E9270945D386226FB4A5F7454889A85EC_AdjustorThunk,
	UnsafeQueue_1_get_IsCreated_m0B22F04A9ACF6226E60FD3F78970158670B242A0_AdjustorThunk,
	UnsafeRingQueue_1_Dispose_m72851D0FDFB87F46F84EEC0724A99A5354EE29CC_AdjustorThunk,
	UnsafeRingQueue_1_get_Capacity_m3BAD1C0DA1E53909608B3CD29385212722046C05_AdjustorThunk,
	UnsafeRingQueue_1_get_IsCreated_m7ECCEAA05D3ACBA98D7E9D229B1046523557672B_AdjustorThunk,
	UnsafeRingQueue_1_get_IsEmpty_mE7D7D3EF7B442FE9D948891ED40473A02B5FA104_AdjustorThunk,
	UnsafeRingQueue_1_get_Length_mE7755222D1DF20FA47C59EDC6DDF0142A7A70E11_AdjustorThunk,
	ValueTaskAwaiter_1__ctor_m7A5127CE080DF8BE37A788EFDE4C6EBED02FEA93_AdjustorThunk,
	ValueTaskAwaiter_1_GetResult_m8973CE2E1FCAD6916DD9B366CE71B5078BC019AF_AdjustorThunk,
	ValueTaskAwaiter_1_UnsafeOnCompleted_m80446D0C780C1396A45604E48D89DBE5BD3DA2AD_AdjustorThunk,
	ValueTaskAwaiter_1_get_IsCompleted_mA84942EAB1D2CE8ACE5BFA8378E9ED7E1D8971EB_AdjustorThunk,
	ValueTask_1__ctor_m8346EDD752A97883147D3BC8685D3DA45F1CAD19_AdjustorThunk,
	ValueTask_1__ctor_mB12720B6617FD51550F36A5DDA1FB5B905F8931A_AdjustorThunk,
	ValueTask_1__ctor_m6290E5FE79CCE71C6983D5D86933A41BFB4BDEE0_AdjustorThunk,
	ValueTask_1__ctor_mF8C25D7941B477ADE70916CF8B71E76E7D9895ED_AdjustorThunk,
	ValueTask_1_AsTask_m7982AD4E459905FB552E2E531A534C2F828B56E5_AdjustorThunk,
	ValueTask_1_ConfigureAwait_m699DD324BA535F744134CF26F542D6C9821511C7_AdjustorThunk,
	ValueTask_1_Equals_mD5BF0BE72581594DA92F59B348593443F6DF6950_AdjustorThunk,
	ValueTask_1_Equals_mABC4FB221AC4A671D4DFADEF992B569AD2C30A9D_AdjustorThunk,
	ValueTask_1_GetAwaiter_mEA7FB0067672E57AAD8A0299D0B90B4C32914B50_AdjustorThunk,
	ValueTask_1_GetHashCode_m8000CE1DEAB0FA01A6ECB8A08BE19213A970D73E_AdjustorThunk,
	ValueTask_1_GetTaskForValueTaskSource_mEAEF30AE4C3EF71CCAED02D41A6B9D9E88A74915_AdjustorThunk,
	ValueTask_1_ToString_m4F52C632CA8530BBCAD18F1CE53FAF04011A4436_AdjustorThunk,
	ValueTask_1_get_IsCompleted_m6FC365DA579395B0B09E0A33A77AF97D29DDE383_AdjustorThunk,
	ValueTask_1_get_IsCompletedSuccessfully_m951AC7136DB7D58F31DC79344D3C51412D4744DA_AdjustorThunk,
	ValueTask_1_get_Result_m4C8284BCA2ADC4FF77D6594A1056775D523ACC82_AdjustorThunk,
	ValueTuple_1__ctor_m538A52BFBDA47CC5C05AEE670CAE9D0657D425CB_AdjustorThunk,
	ValueTuple_1_CompareTo_mFE3D3F1BBCCB23FF42959D2990AD177231EB7586_AdjustorThunk,
	ValueTuple_1_Equals_m744EA4C6D7198DA2A95179DB68116437BBC453E7_AdjustorThunk,
	ValueTuple_1_Equals_mE31990E0876A75374263FD30DDB89A7D8B1E2D91_AdjustorThunk,
	ValueTuple_1_GetHashCode_m0C86BD1C67D66BCAEB44D777CE0885CE0EF76BA6_AdjustorThunk,
	ValueTuple_1_System_Collections_IStructuralComparable_CompareTo_m2D3994A0A1930ABFBC788A619697E6EDA6507AEE_AdjustorThunk,
	ValueTuple_1_System_Collections_IStructuralEquatable_Equals_m478C9A5F2E5423A54F1B0A1D451BD86F90DD4EEE_AdjustorThunk,
	ValueTuple_1_System_Collections_IStructuralEquatable_GetHashCode_mEF47D4B715325E8733A2797A2DC4D80939E18565_AdjustorThunk,
	ValueTuple_1_System_IComparable_CompareTo_m27BCDCCC04DBC7C71F96F97D759F4F8BDC3EB7E4_AdjustorThunk,
	ValueTuple_1_System_IValueTupleInternal_GetHashCode_m8826B801A5B79BA75CBB3632593EF99E034E61B7_AdjustorThunk,
	ValueTuple_1_System_IValueTupleInternal_ToStringEnd_mC9A89165D4A8F414E8FF4B40B30386AAE4544751_AdjustorThunk,
	ValueTuple_1_System_Runtime_CompilerServices_ITuple_get_Item_mFBB9DBCCE1C238E52DACA6E83CBD26FF082FEF92_AdjustorThunk,
	ValueTuple_1_System_Runtime_CompilerServices_ITuple_get_Length_mDC15DE3BB4C0C1F78ED4090E77EFD093761C0C49_AdjustorThunk,
	ValueTuple_1_ToString_m5D2A8965564621C5442CBAC724FAB374EB61379A_AdjustorThunk,
	ValueTuple_2__ctor_mCAE8E725F680FA6BE2C23B9686C9F6056BB7E5CD_AdjustorThunk,
	ValueTuple_2_CompareTo_mD4B10342200C73FAA0E7553433244862FFF316D7_AdjustorThunk,
	ValueTuple_2_Equals_mD903BBEBB3EDB6897C67C462F1A958D28E156085_AdjustorThunk,
	ValueTuple_2_Equals_m691065991CED657BB7925B16C9C654A09F3292DA_AdjustorThunk,
	ValueTuple_2_GetHashCode_m4BA7B2997C6450C829DC80A188E4DD22AE712A75_AdjustorThunk,
	ValueTuple_2_GetHashCodeCore_m67662E6D7F40D4FD382E0E75F15CEC0B7FDDB429_AdjustorThunk,
	ValueTuple_2_System_Collections_IStructuralComparable_CompareTo_mBE4DB6C73A3970373B450757CC80C03D318C4269_AdjustorThunk,
	ValueTuple_2_System_Collections_IStructuralEquatable_Equals_m4FC09D4CA21099E02F8DA2E9E2DAC07DADDE7596_AdjustorThunk,
	ValueTuple_2_System_Collections_IStructuralEquatable_GetHashCode_mFF86F83A0C3ECE4DBC8519A067043F2670CBAF0C_AdjustorThunk,
	ValueTuple_2_System_IComparable_CompareTo_mF8C76C36693FDDA83BE43D8D76A91386E69A9959_AdjustorThunk,
	ValueTuple_2_System_IValueTupleInternal_GetHashCode_m9CF6EB50E064F7FFE19AF23697500FAB1168DE2B_AdjustorThunk,
	ValueTuple_2_System_IValueTupleInternal_ToStringEnd_m00CF3AC7D704F0DB5D633B3D18B4CACEE08B8172_AdjustorThunk,
	ValueTuple_2_System_Runtime_CompilerServices_ITuple_get_Item_m6BCA5019059D019D9C934D031DA4B3CD5E553E78_AdjustorThunk,
	ValueTuple_2_System_Runtime_CompilerServices_ITuple_get_Length_m4228CA61B034F353ABC6547567B3D7F748B43ED8_AdjustorThunk,
	ValueTuple_2_ToString_m3A385F535CA53166311E733E7699676231CFD10F_AdjustorThunk,
	ValueTuple_3__ctor_mAB800E7134D8E52646FD55A8B2979AC908028759_AdjustorThunk,
	ValueTuple_3_CompareTo_m4C3E08414A848EC10D70182B7471304542521377_AdjustorThunk,
	ValueTuple_3_Equals_m1716966B94A34CEFBA3FCB15F00A3DE34CF167B4_AdjustorThunk,
	ValueTuple_3_Equals_m31C947DE700D3842AA28795E512D50FE1E505820_AdjustorThunk,
	ValueTuple_3_GetHashCode_m895E8857672E5454D68082DD0A8752865D8E71FC_AdjustorThunk,
	ValueTuple_3_GetHashCodeCore_m33A8FA4704589007BFD95252E15C89CFDC3D7642_AdjustorThunk,
	ValueTuple_3_System_Collections_IStructuralComparable_CompareTo_m0EAD982A014687A521C863B33102049D6042380B_AdjustorThunk,
	ValueTuple_3_System_Collections_IStructuralEquatable_Equals_m3F5FEF37FD4F019408802635862595B7C9A633DB_AdjustorThunk,
	ValueTuple_3_System_Collections_IStructuralEquatable_GetHashCode_m23E4A5E0CD82CE6A0C844FBFAAC98A510810E98A_AdjustorThunk,
	ValueTuple_3_System_IComparable_CompareTo_mFB7A9AF6B4FE4D8FAB3B464AC27C78B9B8CA8725_AdjustorThunk,
	ValueTuple_3_System_IValueTupleInternal_GetHashCode_mF4FA0E951A78739A0DA2416A08FFDE9388320AAA_AdjustorThunk,
	ValueTuple_3_System_IValueTupleInternal_ToStringEnd_mBA0C6359D2897615B5C51B341FB79F4F9C639F31_AdjustorThunk,
	ValueTuple_3_System_Runtime_CompilerServices_ITuple_get_Item_mFEC0531427CFC1773E3A8219D4237B359DE9F914_AdjustorThunk,
	ValueTuple_3_System_Runtime_CompilerServices_ITuple_get_Length_m5C7DEC7C8EBD2A0F0FDFA89009D316BBBD41FC5A_AdjustorThunk,
	ValueTuple_3_ToString_m9995C56C122AEC521C9BF4AD9C00A1498AA26744_AdjustorThunk,
	ValueTuple_4__ctor_mFF0D3A69E30DE69A5DB3A2559A8901F1E9851FCB_AdjustorThunk,
	ValueTuple_4_CompareTo_mFD48E2AA15064CAC797A24D6595473A41F755F23_AdjustorThunk,
	ValueTuple_4_Equals_mA6E7E6D2B61BCEE388C695E55241E3D23B136E92_AdjustorThunk,
	ValueTuple_4_Equals_mB24DD728F77D42F7BA367887EE8BCC1AEAC2B86E_AdjustorThunk,
	ValueTuple_4_GetHashCode_mD228930A54E53FF9E9F3370361B0D81F05BBB828_AdjustorThunk,
	ValueTuple_4_GetHashCodeCore_m5E8ECC19294F8DFC3FBEA99FFC32F0D41D5FBA77_AdjustorThunk,
	ValueTuple_4_System_Collections_IStructuralComparable_CompareTo_m8EFEB9D7B5790959B79D8E53DD14DB1A038C304C_AdjustorThunk,
	ValueTuple_4_System_Collections_IStructuralEquatable_Equals_m88F5EA4CCB9B7F414EB6FE747DCDBDC088459208_AdjustorThunk,
	ValueTuple_4_System_Collections_IStructuralEquatable_GetHashCode_mE44B3DB721B4562AA53675B76D454998D8883828_AdjustorThunk,
	ValueTuple_4_System_IComparable_CompareTo_m9B7539113D2B5D30818A1A60D51E010B7DBE1BD8_AdjustorThunk,
	ValueTuple_4_System_IValueTupleInternal_GetHashCode_m611C902045E4669D57C34E6609AB0626823B19CD_AdjustorThunk,
	ValueTuple_4_System_IValueTupleInternal_ToStringEnd_mC81C0F3DF892B61082B42A6CDE5A39B18F692814_AdjustorThunk,
	ValueTuple_4_System_Runtime_CompilerServices_ITuple_get_Item_m6A49A2D72011FF3ED0A14280087A6631F426B086_AdjustorThunk,
	ValueTuple_4_System_Runtime_CompilerServices_ITuple_get_Length_m10BCA13E3F340EE4467F6DD105E60646F0D563F9_AdjustorThunk,
	ValueTuple_4_ToString_m05CC5E764EFAAE3D2796762E7A3B9729474CF8D4_AdjustorThunk,
	ValueTuple_5__ctor_m137533B18C66876071676469953787BAFAA5AEFF_AdjustorThunk,
	ValueTuple_5_CompareTo_mC808DB0CE9EDE1F4B2C4D7382F3E57105940208A_AdjustorThunk,
	ValueTuple_5_Equals_m1BDE6D49AEE834527814FAEBE4534B414E1022A9_AdjustorThunk,
	ValueTuple_5_Equals_mB9CD17DAD4FABF6412558E3A82BB15FE28160D82_AdjustorThunk,
	ValueTuple_5_GetHashCode_m7B00692478B9218079F0364A02363A500A3202B0_AdjustorThunk,
	ValueTuple_5_GetHashCodeCore_mCAC2EA3BE3C748840BE08F742B272274BB7E171A_AdjustorThunk,
	ValueTuple_5_System_Collections_IStructuralComparable_CompareTo_mF83475B1821A3AE012364E6215C27FFFF25A399E_AdjustorThunk,
	ValueTuple_5_System_Collections_IStructuralEquatable_Equals_m6220851574828DC96CE4C7E2D09213B84CA5C1FE_AdjustorThunk,
	ValueTuple_5_System_Collections_IStructuralEquatable_GetHashCode_mBA5888BE2DA308AEA1CCE2C0E702F5DF77975E7C_AdjustorThunk,
	ValueTuple_5_System_IComparable_CompareTo_mA31E7015CC7DCBB59BA58FC44A74FB3ED5BBEF8F_AdjustorThunk,
	ValueTuple_5_System_IValueTupleInternal_GetHashCode_m76348DC65A7A91FB4C0B2994EFA9ECFE288F7E4F_AdjustorThunk,
	ValueTuple_5_System_IValueTupleInternal_ToStringEnd_m6CEF8C4D0881F16FD3EB0677334CB013A03D2D6D_AdjustorThunk,
	ValueTuple_5_System_Runtime_CompilerServices_ITuple_get_Item_m8253E9D1002923342C60BBBFBF619F2943FAB5C1_AdjustorThunk,
	ValueTuple_5_System_Runtime_CompilerServices_ITuple_get_Length_m8B407CEF945B739D7AB2AB153EE9EBEDCAA67755_AdjustorThunk,
	ValueTuple_5_ToString_m7265BEA8B1A29B537886BB6BEE00D1A154F8D814_AdjustorThunk,
	ValueTuple_8__ctor_m456CD331E19E16CE692E636499DF94A47061A7E9_AdjustorThunk,
	ValueTuple_8_CompareTo_m7BA5E274F0F612A33415A48FBBD329877F6CA1CC_AdjustorThunk,
	ValueTuple_8_Equals_m81A7442A1DCE82EB1C3D46334D42A88B21F92274_AdjustorThunk,
	ValueTuple_8_Equals_m6E9EB22F4F990C711D38C5929488A47E32204C5A_AdjustorThunk,
	ValueTuple_8_GetHashCode_mFFB8FC3075672FDEC47412793447ACDA1DB90FC1_AdjustorThunk,
	ValueTuple_8_GetHashCodeCore_mD6AC6CAEA9F3BB1951814E084AC3E8C6C803577A_AdjustorThunk,
	ValueTuple_8_System_Collections_IStructuralComparable_CompareTo_mDA0F007332659CDA6ED3E2156DE347F91EB9EBB9_AdjustorThunk,
	ValueTuple_8_System_Collections_IStructuralEquatable_Equals_m8EC26DEDFFB4B136ADB2AF17FA7E9395B7E948A0_AdjustorThunk,
	ValueTuple_8_System_Collections_IStructuralEquatable_GetHashCode_mBBB3278470E2465A6B3F9EBB77231B7749837D84_AdjustorThunk,
	ValueTuple_8_System_IComparable_CompareTo_m876067CC3338F81C90DFD2DD51CF451CDB5DDDC1_AdjustorThunk,
	ValueTuple_8_System_IValueTupleInternal_GetHashCode_mEDB78B450BFD722ED3A333CBBF3CBE862F445EDE_AdjustorThunk,
	ValueTuple_8_System_IValueTupleInternal_ToStringEnd_m5413C9CBCCC7063A9FEADA1E0B07CB2E522B708D_AdjustorThunk,
	ValueTuple_8_System_Runtime_CompilerServices_ITuple_get_Item_mFD1FF614F4F4505046AEAE30835537989E5DB09E_AdjustorThunk,
	ValueTuple_8_System_Runtime_CompilerServices_ITuple_get_Length_mCEC9AD927A3B4A0BF8150A2FC47A62375090D949_AdjustorThunk,
	ValueTuple_8_ToString_mCBC8EEE6DF5982B972AEC0CAF758197B2CDE7E3E_AdjustorThunk,
	Vector_1__ctor_m7540979061ABB2A0A6D57213359B3E83A1B7B4D2_AdjustorThunk,
	Vector_1__ctor_mF674294C34FEB07FB44E694358BDA181F564B674_AdjustorThunk,
	Vector_1__ctor_mD3ECCFAE19D970AAEFCC15057E4B90EEA62EB001_AdjustorThunk,
	Vector_1__ctor_mAD567DC632B01A95C6F418F1C6265F295D1EB0A9_AdjustorThunk,
	Vector_1_Equals_mBC248D1BFF0E4CEA6585C0F10707B1843A4502E8_AdjustorThunk,
	Vector_1_Equals_m1D6500596F9B825F39D474A32D7A6D5E14E22B8F_AdjustorThunk,
	Vector_1_GetHashCode_m3ACEDE87318824BACAD7E8CB760798D98D37C4FF_AdjustorThunk,
	Vector_1_ToString_m212191DE0383E512D8297CE138DB5C06BF1E6F4A_AdjustorThunk,
	Vector_1_ToString_mB21C58D9FD6C9A89DF0D786C50F960E551B1FB8F_AdjustorThunk,
	Vector_1_get_Item_m301D7183D2063DC97710DCD2CEF970E7A67EB21C_AdjustorThunk,
	VisitContext_1__ctor_m31354CF5ADC3BDA59751A23406B0FDDADB4BC0AE_AdjustorThunk,
	VisitContext_1_ContinueVisitation_m800888319D5A63406E05D0F38ADD3C59EF0C732A_AdjustorThunk,
	VisitContext_1_ContinueVisitationWithoutAdapters_m6FE4A77169F00A1DC03CED539FA9596553EB62E5_AdjustorThunk,
	VisitContext_1_get_Property_m1B164AC870CDFE66DB4D46F5C68A337E5869E8B6_AdjustorThunk,
	VisitContext_2__ctor_mB7536685F417398FD93AB7F0C7EEE1EB8C2CD822_AdjustorThunk,
	VisitContext_2_ContinueVisitation_m368A808DC687C81FE2EF255F3E4ABD91B87047DE_AdjustorThunk,
	VisitContext_2_ContinueVisitationWithoutAdapters_m52B8B80829612CB1C70331D9950C5431D60E6CBA_AdjustorThunk,
	VisitContext_2_get_Property_m98E03E078D18616C47421049904506BD890E556E_AdjustorThunk,
	fsOption_1__ctor_m343A7CF35E302DB9E5BCA27A0DAC03751B238516_AdjustorThunk,
	fsOption_1_get_HasValue_m2E9021BB4CB6156A14918A5B865D21751CA6D00C_AdjustorThunk,
	fsOption_1_get_IsEmpty_m8F02E0608720E562CDB4C8906C1CD863FD8FE767_AdjustorThunk,
	fsOption_1_get_Value_m2A0C3FDC3098EA4DC3BB5CF398E95EC2C991988A_AdjustorThunk,
	AnimationScriptPlayable_CheckJobTypeValidity_TisIl2CppFullySharedGenericAny_m33EAA66CE1A7CA0786F87FB47468385A87873115_AdjustorThunk,
	AnimationScriptPlayable_GetJobData_TisIl2CppFullySharedGenericStruct_m26F80B7CCA451FFF9BE6437FA5E5EB673A55D6EB_AdjustorThunk,
	AnimationScriptPlayable_SetJobData_TisIl2CppFullySharedGenericStruct_m0875800AE8D572C36F6FD69BD08657826F883C75_AdjustorThunk,
	AsyncGPUReadbackRequest_GetData_TisIl2CppFullySharedGenericStruct_m785CA6B68B6527258558130B5F81B345AE654EF0_AdjustorThunk,
	AsyncTaskMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mAFEAD0E0483FFA3D862272177C9DE55F2C943F8F_AdjustorThunk,
	AsyncTaskMethodBuilder_Start_TisIl2CppFullySharedGenericAny_m36201A5D380AC483FAE5E144880EEE2EB09D69E2_AdjustorThunk,
	AsyncUniTaskMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mD6CD2CEDAD85EBF9E210B5527441061D410057AA_AdjustorThunk,
	AsyncUniTaskMethodBuilder_Start_TisIl2CppFullySharedGenericAny_mA13C3EA048D166590DA2E340CD6FC2B3FC3DCA85_AdjustorThunk,
	AsyncUniTaskVoidMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_mF3C1A86A14D011BBE119A6E750C6BEEEC816CC9E_AdjustorThunk,
	AsyncUniTaskVoidMethodBuilder_Start_TisIl2CppFullySharedGenericAny_mF35DDB7830BC9CB0FC86DDCF30A3585A6D79B997_AdjustorThunk,
	AsyncVoidMethodBuilder_AwaitUnsafeOnCompleted_TisIl2CppFullySharedGenericAny_TisIl2CppFullySharedGenericAny_m03130C9822AF7F0C435E5DAF03F245D2F35FC6EE_AdjustorThunk,
	AsyncVoidMethodBuilder_Start_TisIl2CppFullySharedGenericAny_mF2CE0E0265F13C43AAB330B33509540A399C62A2_AdjustorThunk,
	CullingResults_GetNativeArray_TisIl2CppFullySharedGenericStruct_mAD95E79FFE908C81E8C8514A3F2E8E583998A7C0_AdjustorThunk,
	Hash128_Append_TisIl2CppFullySharedGenericStruct_mE148C85A9415FF4819E7137E15652770296D8B8C_AdjustorThunk,
	Hash128_Append_TisIl2CppFullySharedGenericStruct_m1DF2B7F4C2182E42BB9D482C6BD9B621AD96A37F_AdjustorThunk,
	Hash128_Append_TisIl2CppFullySharedGenericStruct_m7EBA779E6E080D089CBFCA2DC8316E4C972E42AB_AdjustorThunk,
	Hash128_Append_TisIl2CppFullySharedGenericStruct_m5E19D655EC03ED8A908594E0D4E927DAF40E4C0C_AdjustorThunk,
	Hash128_Append_TisIl2CppFullySharedGenericStruct_mAD033BD30A77E9C834A51A6A0CCB0DE51CB89F93_AdjustorThunk,
	Hash128_Append_TisIl2CppFullySharedGenericStruct_m94DE3968A172E67D1ABE6794E3DF614B5A482DB9_AdjustorThunk,
	Hash128_Append_TisIl2CppFullySharedGenericStruct_mE1E8828A3459E86FF79BFA283170EED36F3C3162_AdjustorThunk,
	HashCode_Add_TisIl2CppFullySharedGenericAny_m7D286F70F6ABEBACBB74E3FD2CEA60626F908ADB_AdjustorThunk,
	InputDevice_CheckValidAndSetDefault_TisIl2CppFullySharedGenericAny_mEF020C615D84C1731C46461C60100807B353E47C_AdjustorThunk,
	InputFeatureUsage_As_TisIl2CppFullySharedGenericAny_m6D8205335AA616E9D9B73F04B1B32AB08F728FF9_AdjustorThunk,
	ParticleSystemJobData_CreateNativeArray_TisIl2CppFullySharedGenericStruct_m999DC724C60DE8B74BC368867979AFEEC4C48F64_AdjustorThunk,
	Playable_IsPlayableOfType_TisIl2CppFullySharedGenericStruct_m295C4DE8654EB520ABA0C31EAF1E01D89A770DDB_AdjustorThunk,
	PlayableGraph_Connect_TisIl2CppFullySharedGenericStruct_TisIl2CppFullySharedGenericStruct_mE6755FEF0B1318150CE9FA535DD165037E08CE3A_AdjustorThunk,
	PlayableGraph_DestroyOutput_TisIl2CppFullySharedGenericStruct_m0E033AC1F2C22345EB7A36527B30D8825B7821E4_AdjustorThunk,
	PlayableGraph_DestroyPlayable_TisIl2CppFullySharedGenericStruct_mD2E0791BF597E588C8FFA67DF593EB718CC8B195_AdjustorThunk,
	PlayableGraph_DestroySubgraph_TisIl2CppFullySharedGenericStruct_mA15F58973E26BEDA428AFDA16EF472C4348199FD_AdjustorThunk,
	PlayableGraph_Disconnect_TisIl2CppFullySharedGenericStruct_m020954D570D3838C680F7D851422EE302DA69D25_AdjustorThunk,
	PlayableGraph_GetOutputByType_TisIl2CppFullySharedGenericStruct_m23BFFD6D32C7C3B0626F2C33BAB229EC9E4A800E_AdjustorThunk,
	PlayableGraph_GetOutputCountByType_TisIl2CppFullySharedGenericStruct_m309F7343AEE30B430C5E6E92B8B1A5DDA6DFA4AD_AdjustorThunk,
	PlayableHandle_GetObject_TisRuntimeObject_m8DB359D6799D32A54972E5FC49BB8DBD5F74A6A4_AdjustorThunk,
	PlayableHandle_IsPlayableOfType_TisIl2CppFullySharedGenericAny_mA4E158678160F1D9713643D243397452502EE932_AdjustorThunk,
	PlayableOutput_IsPlayableOutputOfType_TisIl2CppFullySharedGenericStruct_mEA1ADAC81B0F78FC2EA6AC46EA0AF1FB5C128BDD_AdjustorThunk,
	PlayableOutputHandle_IsPlayableOutputOfType_TisIl2CppFullySharedGenericAny_mAF0114D0A8F1D86A1248F52E4D85173FA4C20F03_AdjustorThunk,
	MeshData_CopyAttributeInto_TisIl2CppFullySharedGenericStruct_mBC51B1613719DE91F3CE9994EE6D15059DE32469_AdjustorThunk,
	MeshData_GetIndexData_TisIl2CppFullySharedGenericStruct_mB6FA280A6F231D6988DA8F708CEC9B806B181E3E_AdjustorThunk,
	MeshData_GetVertexData_TisIl2CppFullySharedGenericStruct_mCB44203C07C04A028F3DF24B278D2AAFF94469A4_AdjustorThunk,
};
