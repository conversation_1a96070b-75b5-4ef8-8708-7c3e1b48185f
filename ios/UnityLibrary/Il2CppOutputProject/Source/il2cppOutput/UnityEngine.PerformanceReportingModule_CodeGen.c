﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void PerformanceReporting_get_enabled_m13A834E7124C3E52E95FD3DBDA5BE5C9906D436C (void);
extern void PerformanceReporting_set_enabled_mAAC2FBB9CE52F6F37C8456B121DD17C8395F80F5 (void);
extern void PerformanceReporting_get_graphicsInitializationFinishTime_m14D93A90C929C57074781B64E6889878E593A9C6 (void);
static Il2CppMethodPointer s_methodPointers[3] = 
{
	PerformanceReporting_get_enabled_m13A834E7124C3E52E95FD3DBDA5BE5C9906D436C,
	PerformanceReporting_set_enabled_mAAC2FBB9CE52F6F37C8456B121DD17C8395F80F5,
	PerformanceReporting_get_graphicsInitializationFinishTime_m14D93A90C929C57074781B64E6889878E593A9C6,
};
static const int32_t s_InvokerIndices[3] = 
{
	8993,
	8868,
	9019,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_PerformanceReportingModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PerformanceReportingModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PerformanceReportingModule_CodeGenModule = 
{
	"UnityEngine.PerformanceReportingModule.dll",
	3,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_PerformanceReportingModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
