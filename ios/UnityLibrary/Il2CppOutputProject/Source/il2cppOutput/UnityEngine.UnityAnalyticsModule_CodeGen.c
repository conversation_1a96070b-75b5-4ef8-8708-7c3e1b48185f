﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void RemoteSettings_add_Updated_m236B4D6AAF3342720696E96A252AAD9956B84A72 (void);
extern void RemoteSettings_remove_Updated_m6DD904116828854B4BDD51B968AE01FC49872A3B (void);
extern void RemoteSettings_add_BeforeFetchFromServer_m7C4023E81A6D8BDD9C7799109B2D3C16A8A71249 (void);
extern void RemoteSettings_remove_BeforeFetchFromServer_m170DE118276391E8544343DD0EBFFF9DAA6EBD57 (void);
extern void RemoteSettings_add_Completed_mC6EBFB8A2A4CD30593ECE8E25A5D8F03F6197925 (void);
extern void RemoteSettings_remove_Completed_mF2B0CB5D6B5538FB6890D167271E5925A8EC90FF (void);
extern void RemoteSettings_RemoteSettingsUpdated_m6202CCC0AF33D44838BB46977D075E54FD5EC069 (void);
extern void RemoteSettings_RemoteSettingsBeforeFetchFromServer_m677DED4CFA8C9E498227A3E939242974DF8FA35C (void);
extern void RemoteSettings_RemoteSettingsUpdateCompleted_m56713308E00B18BF0E5FADEC93D67A70F7E5FD86 (void);
extern void RemoteSettings_CallOnUpdate_mBAD3507A62CE4778E69A3E1B8DEF756EA7881FC4 (void);
extern void RemoteSettings_ForceUpdate_m05A437FF0CB7552AF872FF999106AD795F7A9BEB (void);
extern void RemoteSettings_WasLastUpdatedFromServer_m9D9DF108C0C014670CE1E1A5ABC59C76FC89234C (void);
extern void RemoteSettings_GetInt_m6B2CCA75F70E2052BC562E2F6C2D91F1CB66335E (void);
extern void RemoteSettings_GetInt_m2A108979AEA2871AF6EA0477E696829AC306F895 (void);
extern void RemoteSettings_GetLong_m80B4C8BFB5422165CD7B38C5D56A2A7E63CEB643 (void);
extern void RemoteSettings_GetLong_m3BC2690DD60E61C043584B31FC5371E9DE1D9DF7 (void);
extern void RemoteSettings_GetFloat_m4062B14E0CF358581AC256B668F74BB542777807 (void);
extern void RemoteSettings_GetFloat_m7E6489CE52CBC46BD07076CD2D3522178AB6A4AC (void);
extern void RemoteSettings_GetString_mD41BAF75A8DD0F38CD78B4AE26E22D3D594B337C (void);
extern void RemoteSettings_GetString_m8BD5E66BCEC5FC5A4530FCC89EC41658205DEE03 (void);
extern void RemoteSettings_GetBool_m34D0B037018CA55A2C7EDAD2121864B28A9BAC78 (void);
extern void RemoteSettings_GetBool_m50D6676AEFFB7716A97CC50E3E6B1DC353684BBE (void);
extern void RemoteSettings_HasKey_mB1DB6707317744D3FC2219B7624F1471328BE3C6 (void);
extern void RemoteSettings_GetCount_m49C775161ED638196C7511A7A1C9D23F9F0EC324 (void);
extern void RemoteSettings_GetKeys_m2805E93AD62ECB68C9FC6BC56695FF6E5D8F1EFC (void);
extern void RemoteSettings_GetObject_mFB8625B73855280DA61481AD35CB403D71ED7EB8 (void);
extern void RemoteSettings_GetObject_m9A853457384FCE727F2679D50BCD70B92F58B1A9 (void);
extern void RemoteSettings_GetAsScriptingObject_mEA3D460879F6249689C4B6995FCEC39F27E0DF5C (void);
extern void RemoteSettings_GetDictionary_mA380A38776248F7C8CD21DC63E38C615926DD3A4 (void);
extern void RemoteSettings_UseSafeLock_mAC8531EA6DA06F46E4136CC743D3100CE261A88D (void);
extern void RemoteSettings_ReleaseSafeLock_m44A9D085587ADD0C5B7777AA2710215F01AABE27 (void);
extern void RemoteSettings_GetSafeTopMap_m7A94C149EBE994576B4A7181FF14E0B46DA58351 (void);
extern void UpdatedEventHandler__ctor_mB914409481F8FDC738B4EDB1DBB4883F743F863A (void);
extern void UpdatedEventHandler_Invoke_m4D496F648FD908A8537A35C4A94CBB44294D6D50 (void);
extern void UpdatedEventHandler_BeginInvoke_m4B1BACFB1C2FCFC570CB4CDA5588C0C773A5CD6B (void);
extern void UpdatedEventHandler_EndInvoke_m43343E8FEC6C0999E21D07C1CEDB6A671B1C4564 (void);
extern void RemoteConfigSettings_add_Updated_m8A8D60F6EF94B462D2335C107AF92BCB50B1913E (void);
extern void RemoteConfigSettings_remove_Updated_m5B31F8182233022D23616B13CE2D93984E8D696D (void);
extern void RemoteConfigSettings__ctor_m4A401FD82EC64839361F949CC685C23ACDA21314 (void);
extern void RemoteConfigSettings__ctor_mBD9ED693B604C0B5609A5DF17A061D4E3C869386 (void);
extern void RemoteConfigSettings_Finalize_m8F9AD007752780EDE938BDDFE66095B4020464FE (void);
extern void RemoteConfigSettings_Destroy_m70658D54B09F063FF370D55263E57760D5B71D68 (void);
extern void RemoteConfigSettings_Dispose_m017C53F73969B3BB7F6F0062C3E9EC0F02457E3F (void);
extern void RemoteConfigSettings_Internal_Create_m7279BEF3CAF6F0C3F363A9B2D55FF06223437B05 (void);
extern void RemoteConfigSettings_Internal_Destroy_m7C8476D5305483A8CD70B28C10877A0E16114E15 (void);
extern void RemoteConfigSettings_RemoteConfigSettingsUpdated_mA71E7C6CDAF5D349BF0B4880A4D54DF2365EB948 (void);
extern void RemoteConfigSettings_QueueConfig_m8343C39D716CCDDE3E3C5FECFD5C4B9553B91607 (void);
extern void RemoteConfigSettings_SendDeviceInfoInConfigRequest_mA93E22254D6F94CE718340133656B647A7F87A21 (void);
extern void RemoteConfigSettings_AddSessionTag_m75157BAC1F1222C5A716270DE70C9336D43773DA (void);
extern void RemoteConfigSettings_ForceUpdate_m9E9BB783171EC728CD183B5E81CDFFF446B6A325 (void);
extern void RemoteConfigSettings_WasLastUpdatedFromServer_mDC3CCF752AAC5E836651C083E811DAC5E3C5B0A3 (void);
extern void RemoteConfigSettings_GetInt_mEEA407CD65E65A6CCC8FD74E1CD2223B14158F49 (void);
extern void RemoteConfigSettings_GetInt_m607B6E3A635EE70D2D6061AE209B1A5509B97780 (void);
extern void RemoteConfigSettings_GetLong_m340B23089CF847EF902127057FF198E367DB94CF (void);
extern void RemoteConfigSettings_GetLong_m575A81B74F1D4E570B4967960CCDCAB627594E15 (void);
extern void RemoteConfigSettings_GetFloat_mE06BC22BC1D0BB705D8599A8F8CA97BF0BC1293C (void);
extern void RemoteConfigSettings_GetFloat_mF68A91FEE8EE978C9AF4E094238825DD3D7461A6 (void);
extern void RemoteConfigSettings_GetString_mA227CF9EA36C47C97E5709BA7B9FF904B270CDA5 (void);
extern void RemoteConfigSettings_GetString_mCDE06A85F65F6098524A727FB20265F997F41C58 (void);
extern void RemoteConfigSettings_GetBool_m5D9776156BB8E8F6E227951BB23711A8917351D3 (void);
extern void RemoteConfigSettings_GetBool_m1962BD72DA2FA9F7D99E622E8DFFB9851E00DAF4 (void);
extern void RemoteConfigSettings_HasKey_mBEECAD4F451BE932D6A8708EA0F6A57DF1E7C884 (void);
extern void RemoteConfigSettings_GetCount_m1ED0C54F0F560B32030D67FF3845591D697698E0 (void);
extern void RemoteConfigSettings_GetKeys_mE6F23DE94B040F20C1F3377CD9E7ED2C466DAD71 (void);
extern void RemoteConfigSettings_GetObject_m66061F15D0D4B3C2FA1F218A4273E401B2783BFB (void);
extern void RemoteConfigSettings_GetObject_mAB0845C36AB176D9210E1D05FE38EF2340B21BEC (void);
extern void RemoteConfigSettings_GetAsScriptingObject_m3309860DD99AE65A7D2BA98ECA5CD89C349FCD77 (void);
extern void RemoteConfigSettings_GetDictionary_m81DAC6113568CF4836989C22AEE2AA6184103FAD (void);
extern void RemoteConfigSettings_UseSafeLock_mCFA1F9878D946C8B7D2108EFCDF59D48202858B9 (void);
extern void RemoteConfigSettings_ReleaseSafeLock_m183E58C5DCA1B2370A6F18793D7599A481223973 (void);
extern void RemoteConfigSettings_GetSafeTopMap_mB2D208AC7DE047CE56F46C698341A5B76DD67779 (void);
extern void RemoteConfigSettingsHelper_GetSafeMap_m78F4CDD24142C68A51DF758634CCCF6EF4D081AB (void);
extern void RemoteConfigSettingsHelper_GetSafeMapKeys_mE0E2E36ED443E7F9C12C8AAB199CB8EF3A666076 (void);
extern void RemoteConfigSettingsHelper_GetSafeMapTypes_mA90EDFEE8587B0AC81752DBEFF70725C39692D20 (void);
extern void RemoteConfigSettingsHelper_GetSafeNumber_m9E3FFA46A7452B0198AB60262773CFA7D3E4609A (void);
extern void RemoteConfigSettingsHelper_GetSafeFloat_m938CD7953BBFAE4891AA344E914D4DA7FE29962D (void);
extern void RemoteConfigSettingsHelper_GetSafeBool_m396A830F6BA55EA8B3EF321E142F29A27F7FFE71 (void);
extern void RemoteConfigSettingsHelper_GetSafeStringValue_m5883D941CF24D67D0A7ADC0B6EFA37A6DB2E107E (void);
extern void RemoteConfigSettingsHelper_GetSafeArray_m91FD9FE222A5CE91CB5D506BB1F35000C3581132 (void);
extern void RemoteConfigSettingsHelper_GetSafeArraySize_mAEE84CBF6C828663707A023844A7CD8E7D1D3C74 (void);
extern void RemoteConfigSettingsHelper_GetSafeArrayArray_mC7D01B788F5ED0C84BF7B7205C72869A4E65F17E (void);
extern void RemoteConfigSettingsHelper_GetSafeArrayMap_m13FC2C072EA1DE00534B813472C61D866B794F19 (void);
extern void RemoteConfigSettingsHelper_GetSafeArrayType_mFDF686DC5C5972D8D74DC63B3E9B75CD3D287401 (void);
extern void RemoteConfigSettingsHelper_GetSafeNumberArray_m6F7F2F03025037FD126645E07F1E02D8B832D1B5 (void);
extern void RemoteConfigSettingsHelper_GetSafeArrayFloat_mA8DFB1631F2609C31129FDAB4D4CE05E6B736F90 (void);
extern void RemoteConfigSettingsHelper_GetSafeArrayBool_m60A6A3C01742D5E3B9FC0E3DCAF13D02AC023976 (void);
extern void RemoteConfigSettingsHelper_GetSafeArrayStringValue_m45F246B8B5175E4517BE03DE3BE6458C612545A7 (void);
extern void RemoteConfigSettingsHelper_GetDictionary_mE027FC91EE6145E2C58D409F80E9D068D56E87E0 (void);
extern void RemoteConfigSettingsHelper_GetDictionary_m3220BB5F2646E7A22FC92BA005B7EC79889808C6 (void);
extern void RemoteConfigSettingsHelper_GetArrayArrayEntries_mB348BDBF50C08F110D09DDD47168FD9DE72A5483 (void);
extern void RemoteConfigSettingsHelper_GetArrayMapEntries_m445A8ED964B7D7007140912A750B77354B8680E3 (void);
extern void RemoteConfigSettingsHelper_GetArrayEntries_m4BA4CDBBAC856DF3496E6BA47632C7B1CC43910A (void);
extern void RemoteConfigSettingsHelper_GetMixedArrayEntries_m0EBF1D34C1F56CCEDB121C343991AB54AD6BF45D (void);
extern void RemoteConfigSettingsHelper_SetDictKeyType_m890D2854D45C8A65FD876A54D674BB1375AE10CE (void);
extern void ContinuousEvent_ConfigureCustomEvent_mA02C1E739FCF1E64F542D81BEB97C85EC8759763 (void);
extern void ContinuousEvent_ConfigureEvent_m831E004BEE026472ADC3C2C47E124486A5948FFB (void);
extern void ContinuousEvent_InternalRegisterCollector_m3292B4C94CA4A63E3B5A6137ED74FE3D181C468D (void);
extern void ContinuousEvent_InternalSetEventHistogramThresholds_m57CB9FA59A53A6068DD9F7D431DE3EC4EF1658E1 (void);
extern void ContinuousEvent_InternalSetCustomEventHistogramThresholds_mDA7D59D22E10AE79B97317F0F865827A4EBE5597 (void);
extern void ContinuousEvent_InternalConfigureCustomEvent_m383B508A1F11756EE022A51ACF3E3BEB93E4E58D (void);
extern void ContinuousEvent_InternalConfigureEvent_mE50BFD8B7B022ED34ADE4D4314FF6E0AB3ABD0DD (void);
extern void ContinuousEvent_IsInitialized_m99BD2642ABA7CE3AF5E4B709C30EA70247B17B60 (void);
extern void ContinuousEvent__ctor_m4D17724B72E6ACB92E66082474565F77D567B19C (void);
extern void AnalyticsSessionInfo_add_sessionStateChanged_mF51918A05A7489228D8EF3EA7E5CF34B1294FCD6 (void);
extern void AnalyticsSessionInfo_remove_sessionStateChanged_m92DCC7984A5447F18D61BF0ECBA4D3C2AAACB2CC (void);
extern void AnalyticsSessionInfo_CallSessionStateChanged_m6C3C7DD13064E37D7C3AE9411355BCEF77C4664B (void);
extern void AnalyticsSessionInfo_get_sessionState_mAE350FF1756FA691718D35E6ABB263818DA4AEF3 (void);
extern void AnalyticsSessionInfo_get_sessionId_m2022F1F0127FBBB17BC8B77CA5FCBEFF3FA2A4CA (void);
extern void AnalyticsSessionInfo_get_sessionCount_m7348C0E3A6A0B2EEFDFB1C957D0102D4C092856C (void);
extern void AnalyticsSessionInfo_get_sessionElapsedTime_m4ADE521B2B2E25F2C0D5542A135D1EE337D64BAB (void);
extern void AnalyticsSessionInfo_get_sessionFirstRun_m7A053D6BA505100F72698748372C21081702251A (void);
extern void AnalyticsSessionInfo_get_userId_m4CD30B7A030EBE80357D1A5FF3836E77E0CFC196 (void);
extern void AnalyticsSessionInfo_get_customUserId_m2403A7B82C72578E70B00F884CA833BDA03E37D7 (void);
extern void AnalyticsSessionInfo_set_customUserId_mE11E38440F8F4EBC313579611C1D7FB3422DA129 (void);
extern void AnalyticsSessionInfo_get_customDeviceId_m1B0544126B401D4E1A90FB86D121C88AE43C3F0E (void);
extern void AnalyticsSessionInfo_set_customDeviceId_mD14A133995AF3EE79FEC8A0A6E5261FD3EA883D3 (void);
extern void AnalyticsSessionInfo_add_identityTokenChanged_m64C2D38EAE253AA50478B858E5E01CA1B13EFF78 (void);
extern void AnalyticsSessionInfo_remove_identityTokenChanged_mA043898827661833FCE89DCC3682B5FF3FC19C69 (void);
extern void AnalyticsSessionInfo_CallIdentityTokenChanged_m1AD21A1840BCB9CB222455F609DBBF7B7B380911 (void);
extern void AnalyticsSessionInfo_get_identityToken_m6F9C473FE5266E55DFAA285A3F8BF52C9DB89722 (void);
extern void AnalyticsSessionInfo_get_identityTokenInternal_m417D73073A6991448FB614CE628036E4FC86CE42 (void);
extern void AnalyticsSessionInfo_get_customUserIdInternal_m780E73B31CBC707E70C64D8EBF5EE43CBC79310A (void);
extern void AnalyticsSessionInfo_set_customUserIdInternal_m9DB96EAC021F4B647F2C922F82E5D9DE5C8A646D (void);
extern void AnalyticsSessionInfo_get_customDeviceIdInternal_m39881526880F9740E854FA7BD05B13455DA5F4F6 (void);
extern void AnalyticsSessionInfo_set_customDeviceIdInternal_m66B601C96BABF6D316A9AB3F9E88F41977CCAD38 (void);
extern void SessionStateChanged__ctor_m8E04BB6766439BA455F9C808171BD791230496D8 (void);
extern void SessionStateChanged_Invoke_mB9195B30A226CB3E53E470C24FD31E039E5BB4F5 (void);
extern void SessionStateChanged_BeginInvoke_m8B5D445C34B6C14E3F1D41A97EF7BDA062E1F8B4 (void);
extern void SessionStateChanged_EndInvoke_m1A58800B523F53B51A8DAE05458ED2EC90FF5B69 (void);
extern void IdentityTokenChanged__ctor_m1970F8BEEDAA84A8FC5ABB973C0DB62FA2AA8312 (void);
extern void IdentityTokenChanged_Invoke_m22D3DA825F0D6E701D050EFA3D35E84DFAC7F8D9 (void);
extern void IdentityTokenChanged_BeginInvoke_m0A9BD21CD1D86D1FD3486792053E3741D272F2C7 (void);
extern void IdentityTokenChanged_EndInvoke_mE011A609235BBFC3748E76B2A0E3A22E5FDC2D74 (void);
extern void CustomEventData__ctor_m8CD5F577E855AF6354FDE06FD509480850AA03F8 (void);
extern void CustomEventData__ctor_m56F78E56BEB38960137D4310EBA08871D1F8B4E5 (void);
extern void CustomEventData_Finalize_m9BEB37895872F784C37236033CD022211DA8219D (void);
extern void CustomEventData_Destroy_mCE7CEA4B647288F3A6673F325D2397603BA927D0 (void);
extern void CustomEventData_Dispose_mF7FAD8DFA5AE64CA3F3BBDCA22D1ACBE810FAEA6 (void);
extern void CustomEventData_Internal_Create_mA00A12FE26AE334F0251226C8ED5FB02882BC2ED (void);
extern void CustomEventData_Internal_Destroy_m8556DAF414664C7669D1D38217AE2E763B4CEC59 (void);
extern void CustomEventData_AddString_m42AB3534D86CCFCDF54E3F3006B1502C8BA269C9 (void);
extern void CustomEventData_AddInt32_m425314D03ECCAFE49DFDAEFDC50E393C4EAACD66 (void);
extern void CustomEventData_AddUInt32_m44ECC55B2F900092F5C92A5053B83D0955211946 (void);
extern void CustomEventData_AddInt64_m168582B1D88D236B83134237B44A61595F226EA1 (void);
extern void CustomEventData_AddUInt64_m487A081EB24E08F9A504E82C9FB5448D89190C7B (void);
extern void CustomEventData_AddBool_mFB872845AE96AC3BB7E7ADC9DB297F00CC355747 (void);
extern void CustomEventData_AddDouble_mE63C2C3EAD92FD83869373A3A3D942641B428897 (void);
extern void CustomEventData_AddDictionary_m8277B05DF41A78C3C8BEA8DB0724CABB32769221 (void);
extern void Analytics_get_initializeOnStartup_m286CE9E15C3CBFBE64F4D6DBBC222CAE5A59028D (void);
extern void Analytics_set_initializeOnStartup_m00B6044E2C752D2E86F74147493464CF047B5986 (void);
extern void Analytics_ResumeInitialization_m60FB92BBEEA51A13A1BF21A43BC9BA0B6A764FE2 (void);
extern void Analytics_ResumeInitializationInternal_mEE921E4120696DAD668731D837AAA9A736FC9017 (void);
extern void Analytics_get_initializeOnStartupInternal_mDEBF337AB78DDCC3AEFBA439B6EFE2A733002D15 (void);
extern void Analytics_set_initializeOnStartupInternal_m8BD4203D3356D13B4F0FB541B562B458DA31E0FF (void);
extern void Analytics_IsInitialized_mBC7670704DE959791C8DF859A7C66D5DD5168F12 (void);
extern void Analytics_get_enabledInternal_mD94B221876BDD04801C9E9A96302A09DF3FD1C21 (void);
extern void Analytics_set_enabledInternal_m68F6A31D3874B593BDB1E7842354CFBFE5B2FA0C (void);
extern void Analytics_get_playerOptedOutInternal_m846E8370E100DF0753FD82CCD2970DF45E47408C (void);
extern void Analytics_get_eventUrlInternal_mB30C0B42F708A2FF2085DB56646E95BC26550E43 (void);
extern void Analytics_get_configUrlInternal_m727BDC9443A2A9D7FFC5F3EF89B6FC38A6B6C8D2 (void);
extern void Analytics_get_dashboardUrlInternal_m3E961F972F59D3A76E28629911F0AC4D48F54F2A (void);
extern void Analytics_get_limitUserTrackingInternal_m503016D011BEE05EEC34C58632BECF51A16A1FCE (void);
extern void Analytics_set_limitUserTrackingInternal_m491EBEDB60C6169F76C9C2EAF309246534686F23 (void);
extern void Analytics_get_deviceStatsEnabledInternal_m0D31256018E5139AA699CEB4DD87E16B816F3210 (void);
extern void Analytics_set_deviceStatsEnabledInternal_mA64D45012F4E8F73C4E1B5A63D3AB7EBCD7C77CC (void);
extern void Analytics_FlushArchivedEvents_m750AA08AE2D12AED629DF3919A61254C2D0151CF (void);
extern void Analytics_Transaction_m6A16C2852B2365FFA31B407BE5EBF4A6525B5488 (void);
extern void Analytics_SendCustomEventName_m714D4EBF35293ACA77E6B8EDA5D454B760992CCF (void);
extern void Analytics_SendCustomEvent_mDFD010BF86A285FB5620405455D04D31FD0F014B (void);
extern void Analytics_IsCustomEventWithLimitEnabled_m54BB0A094225A5BAF03C99F927AC04735CED901C (void);
extern void Analytics_EnableCustomEventWithLimit_m160FA963BC43689AB806AA8F8D2FB358AF85C397 (void);
extern void Analytics_IsEventWithLimitEnabled_m74EDF34F2314AEA76D79EAE89C88DD622EEFC738 (void);
extern void Analytics_EnableEventWithLimit_m0C3395133D272B218AE76875CA146E5EA4B40959 (void);
extern void Analytics_RegisterEventWithLimit_m1C2E9701EE73122C7788186C9EA9A3C23CF51252 (void);
extern void Analytics_RegisterEventsWithLimit_m84C18E052B2CF10E4103667A0D0549D6A5BBF828 (void);
extern void Analytics_SendEventWithLimit_m084C487537F41F55CA161ED14838A6B892421956 (void);
extern void Analytics_SetEventWithLimitEndPoint_m03BF4DD33AA8FF698578817602AEE7F7F98DBEFA (void);
extern void Analytics_SetEventWithLimitPriority_m1E7B7AF0F36EF2F89A0E96C0DF4EE667FEDB08B8 (void);
extern void Analytics_QueueEvent_m6D14D5C66B8AEF17F76424435899714751E72B2D (void);
extern void Analytics_get_playerOptedOut_mAF35A3DFB6C2A227B2A345B1163E94A11FFC5AB4 (void);
extern void Analytics_get_eventUrl_m50B1E732E8B96427BE8C9DF8EA8FE6C687A8FF53 (void);
extern void Analytics_get_dashboardUrl_m52FFF2BAF2F2844812FB2FF4266B9F57F024AB2A (void);
extern void Analytics_get_configUrl_m459A2EDA5987546513E87F61D8204A64B430EC0C (void);
extern void Analytics_get_limitUserTracking_m0581A21AE831809855623F14E82BD648647CB5C4 (void);
extern void Analytics_set_limitUserTracking_m4E675017801EACD2B5DB9FBAD2F089E3BE7F7859 (void);
extern void Analytics_get_deviceStatsEnabled_m2F80A19ABE8D68CA8E364DB19D0E1529D3F45D8D (void);
extern void Analytics_set_deviceStatsEnabled_mBD793306FA86CBA16BF930B3435E1BC059402144 (void);
extern void Analytics_get_enabled_mE4A7B84E816A7CEC2E8AB90D3165DB5B83D2C141 (void);
extern void Analytics_set_enabled_mF433189C114B6560C5DD8E7D4B6B3B06B7F5679C (void);
extern void Analytics_FlushEvents_m18887A3537F3EC6DD07C32A6EE4D621FF3CDCEBF (void);
extern void Analytics_SetUserId_mF800DBBDDA68C5FC9372A2C8299165B5610FF2FB (void);
extern void Analytics_SetUserGender_m1297C836A2FD612D21AF8F6B6626D3C6E8BFE33A (void);
extern void Analytics_SetUserBirthYear_m5096D5990515A0582BC216E7C7484D10480CB04B (void);
extern void Analytics_SendUserInfoEvent_mC025051A4E5FAEB44036A47E3615392CAED2E30F (void);
extern void Analytics_Transaction_mB5A6694CA8B93C23598952EF8FF435AC67885FEB (void);
extern void Analytics_Transaction_m9BD442FC02953AC2C573E3340E098751608ADEBB (void);
extern void Analytics_Transaction_m237599F1B6EDBB64DB6CE0CC6ED9A6E4B3391F96 (void);
extern void Analytics_CustomEvent_m9AB6593D77ED4BF66F5C613E1C40CD103C2CEAEB (void);
extern void Analytics_CustomEvent_mE0EF0AD12B037F6B4EEE5E23128762DF83E76458 (void);
extern void Analytics_CustomEvent_m663CDDCA04FF832169D1BCEE01C943A7FB6D4363 (void);
extern void Analytics_EnableCustomEvent_m4022096A40E49F647EFC2F29D5DB22D7F1DC53B4 (void);
extern void Analytics_IsCustomEventEnabled_mBAD0C826A757E4959C391A0CAB3565FA6EF8CCB8 (void);
extern void Analytics_RegisterEvent_mC82148D11A02968E347F9D11237D50826223A7C3 (void);
extern void Analytics_RegisterEvent_m202C3CC5E766F86B080009E64218EAD375CDCFF3 (void);
extern void Analytics_RegisterEvent_m82BE2F186E2F452E6FFE10DC44207C3438E660E4 (void);
extern void Analytics_SendEvent_m9E942728E301EA9E90741ADD1ED71DAD118819B5 (void);
extern void Analytics_SetEventEndPoint_m770B11C00EB5727517405ACAAA421DA72A6324F0 (void);
extern void Analytics_SetEventPriority_m5B4812B18DEDBA2B41D2368EB7881B4A38914F7F (void);
extern void Analytics_EnableEvent_m15322E9698E5545E301EE2053C4E8AB5EACA3C95 (void);
extern void Analytics_IsEventEnabled_m38092B674BDB5E0E7046A0939808F08962605093 (void);
static Il2CppMethodPointer s_methodPointers[216] = 
{
	RemoteSettings_add_Updated_m236B4D6AAF3342720696E96A252AAD9956B84A72,
	RemoteSettings_remove_Updated_m6DD904116828854B4BDD51B968AE01FC49872A3B,
	RemoteSettings_add_BeforeFetchFromServer_m7C4023E81A6D8BDD9C7799109B2D3C16A8A71249,
	RemoteSettings_remove_BeforeFetchFromServer_m170DE118276391E8544343DD0EBFFF9DAA6EBD57,
	RemoteSettings_add_Completed_mC6EBFB8A2A4CD30593ECE8E25A5D8F03F6197925,
	RemoteSettings_remove_Completed_mF2B0CB5D6B5538FB6890D167271E5925A8EC90FF,
	RemoteSettings_RemoteSettingsUpdated_m6202CCC0AF33D44838BB46977D075E54FD5EC069,
	RemoteSettings_RemoteSettingsBeforeFetchFromServer_m677DED4CFA8C9E498227A3E939242974DF8FA35C,
	RemoteSettings_RemoteSettingsUpdateCompleted_m56713308E00B18BF0E5FADEC93D67A70F7E5FD86,
	RemoteSettings_CallOnUpdate_mBAD3507A62CE4778E69A3E1B8DEF756EA7881FC4,
	RemoteSettings_ForceUpdate_m05A437FF0CB7552AF872FF999106AD795F7A9BEB,
	RemoteSettings_WasLastUpdatedFromServer_m9D9DF108C0C014670CE1E1A5ABC59C76FC89234C,
	RemoteSettings_GetInt_m6B2CCA75F70E2052BC562E2F6C2D91F1CB66335E,
	RemoteSettings_GetInt_m2A108979AEA2871AF6EA0477E696829AC306F895,
	RemoteSettings_GetLong_m80B4C8BFB5422165CD7B38C5D56A2A7E63CEB643,
	RemoteSettings_GetLong_m3BC2690DD60E61C043584B31FC5371E9DE1D9DF7,
	RemoteSettings_GetFloat_m4062B14E0CF358581AC256B668F74BB542777807,
	RemoteSettings_GetFloat_m7E6489CE52CBC46BD07076CD2D3522178AB6A4AC,
	RemoteSettings_GetString_mD41BAF75A8DD0F38CD78B4AE26E22D3D594B337C,
	RemoteSettings_GetString_m8BD5E66BCEC5FC5A4530FCC89EC41658205DEE03,
	RemoteSettings_GetBool_m34D0B037018CA55A2C7EDAD2121864B28A9BAC78,
	RemoteSettings_GetBool_m50D6676AEFFB7716A97CC50E3E6B1DC353684BBE,
	RemoteSettings_HasKey_mB1DB6707317744D3FC2219B7624F1471328BE3C6,
	RemoteSettings_GetCount_m49C775161ED638196C7511A7A1C9D23F9F0EC324,
	RemoteSettings_GetKeys_m2805E93AD62ECB68C9FC6BC56695FF6E5D8F1EFC,
	NULL,
	RemoteSettings_GetObject_mFB8625B73855280DA61481AD35CB403D71ED7EB8,
	RemoteSettings_GetObject_m9A853457384FCE727F2679D50BCD70B92F58B1A9,
	RemoteSettings_GetAsScriptingObject_mEA3D460879F6249689C4B6995FCEC39F27E0DF5C,
	RemoteSettings_GetDictionary_mA380A38776248F7C8CD21DC63E38C615926DD3A4,
	RemoteSettings_UseSafeLock_mAC8531EA6DA06F46E4136CC743D3100CE261A88D,
	RemoteSettings_ReleaseSafeLock_m44A9D085587ADD0C5B7777AA2710215F01AABE27,
	RemoteSettings_GetSafeTopMap_m7A94C149EBE994576B4A7181FF14E0B46DA58351,
	UpdatedEventHandler__ctor_mB914409481F8FDC738B4EDB1DBB4883F743F863A,
	UpdatedEventHandler_Invoke_m4D496F648FD908A8537A35C4A94CBB44294D6D50,
	UpdatedEventHandler_BeginInvoke_m4B1BACFB1C2FCFC570CB4CDA5588C0C773A5CD6B,
	UpdatedEventHandler_EndInvoke_m43343E8FEC6C0999E21D07C1CEDB6A671B1C4564,
	RemoteConfigSettings_add_Updated_m8A8D60F6EF94B462D2335C107AF92BCB50B1913E,
	RemoteConfigSettings_remove_Updated_m5B31F8182233022D23616B13CE2D93984E8D696D,
	RemoteConfigSettings__ctor_m4A401FD82EC64839361F949CC685C23ACDA21314,
	RemoteConfigSettings__ctor_mBD9ED693B604C0B5609A5DF17A061D4E3C869386,
	RemoteConfigSettings_Finalize_m8F9AD007752780EDE938BDDFE66095B4020464FE,
	RemoteConfigSettings_Destroy_m70658D54B09F063FF370D55263E57760D5B71D68,
	RemoteConfigSettings_Dispose_m017C53F73969B3BB7F6F0062C3E9EC0F02457E3F,
	RemoteConfigSettings_Internal_Create_m7279BEF3CAF6F0C3F363A9B2D55FF06223437B05,
	RemoteConfigSettings_Internal_Destroy_m7C8476D5305483A8CD70B28C10877A0E16114E15,
	RemoteConfigSettings_RemoteConfigSettingsUpdated_mA71E7C6CDAF5D349BF0B4880A4D54DF2365EB948,
	RemoteConfigSettings_QueueConfig_m8343C39D716CCDDE3E3C5FECFD5C4B9553B91607,
	RemoteConfigSettings_SendDeviceInfoInConfigRequest_mA93E22254D6F94CE718340133656B647A7F87A21,
	RemoteConfigSettings_AddSessionTag_m75157BAC1F1222C5A716270DE70C9336D43773DA,
	RemoteConfigSettings_ForceUpdate_m9E9BB783171EC728CD183B5E81CDFFF446B6A325,
	RemoteConfigSettings_WasLastUpdatedFromServer_mDC3CCF752AAC5E836651C083E811DAC5E3C5B0A3,
	RemoteConfigSettings_GetInt_mEEA407CD65E65A6CCC8FD74E1CD2223B14158F49,
	RemoteConfigSettings_GetInt_m607B6E3A635EE70D2D6061AE209B1A5509B97780,
	RemoteConfigSettings_GetLong_m340B23089CF847EF902127057FF198E367DB94CF,
	RemoteConfigSettings_GetLong_m575A81B74F1D4E570B4967960CCDCAB627594E15,
	RemoteConfigSettings_GetFloat_mE06BC22BC1D0BB705D8599A8F8CA97BF0BC1293C,
	RemoteConfigSettings_GetFloat_mF68A91FEE8EE978C9AF4E094238825DD3D7461A6,
	RemoteConfigSettings_GetString_mA227CF9EA36C47C97E5709BA7B9FF904B270CDA5,
	RemoteConfigSettings_GetString_mCDE06A85F65F6098524A727FB20265F997F41C58,
	RemoteConfigSettings_GetBool_m5D9776156BB8E8F6E227951BB23711A8917351D3,
	RemoteConfigSettings_GetBool_m1962BD72DA2FA9F7D99E622E8DFFB9851E00DAF4,
	RemoteConfigSettings_HasKey_mBEECAD4F451BE932D6A8708EA0F6A57DF1E7C884,
	RemoteConfigSettings_GetCount_m1ED0C54F0F560B32030D67FF3845591D697698E0,
	RemoteConfigSettings_GetKeys_mE6F23DE94B040F20C1F3377CD9E7ED2C466DAD71,
	NULL,
	RemoteConfigSettings_GetObject_m66061F15D0D4B3C2FA1F218A4273E401B2783BFB,
	RemoteConfigSettings_GetObject_mAB0845C36AB176D9210E1D05FE38EF2340B21BEC,
	RemoteConfigSettings_GetAsScriptingObject_m3309860DD99AE65A7D2BA98ECA5CD89C349FCD77,
	RemoteConfigSettings_GetDictionary_m81DAC6113568CF4836989C22AEE2AA6184103FAD,
	RemoteConfigSettings_UseSafeLock_mCFA1F9878D946C8B7D2108EFCDF59D48202858B9,
	RemoteConfigSettings_ReleaseSafeLock_m183E58C5DCA1B2370A6F18793D7599A481223973,
	RemoteConfigSettings_GetSafeTopMap_mB2D208AC7DE047CE56F46C698341A5B76DD67779,
	RemoteConfigSettingsHelper_GetSafeMap_m78F4CDD24142C68A51DF758634CCCF6EF4D081AB,
	RemoteConfigSettingsHelper_GetSafeMapKeys_mE0E2E36ED443E7F9C12C8AAB199CB8EF3A666076,
	RemoteConfigSettingsHelper_GetSafeMapTypes_mA90EDFEE8587B0AC81752DBEFF70725C39692D20,
	RemoteConfigSettingsHelper_GetSafeNumber_m9E3FFA46A7452B0198AB60262773CFA7D3E4609A,
	RemoteConfigSettingsHelper_GetSafeFloat_m938CD7953BBFAE4891AA344E914D4DA7FE29962D,
	RemoteConfigSettingsHelper_GetSafeBool_m396A830F6BA55EA8B3EF321E142F29A27F7FFE71,
	RemoteConfigSettingsHelper_GetSafeStringValue_m5883D941CF24D67D0A7ADC0B6EFA37A6DB2E107E,
	RemoteConfigSettingsHelper_GetSafeArray_m91FD9FE222A5CE91CB5D506BB1F35000C3581132,
	RemoteConfigSettingsHelper_GetSafeArraySize_mAEE84CBF6C828663707A023844A7CD8E7D1D3C74,
	RemoteConfigSettingsHelper_GetSafeArrayArray_mC7D01B788F5ED0C84BF7B7205C72869A4E65F17E,
	RemoteConfigSettingsHelper_GetSafeArrayMap_m13FC2C072EA1DE00534B813472C61D866B794F19,
	RemoteConfigSettingsHelper_GetSafeArrayType_mFDF686DC5C5972D8D74DC63B3E9B75CD3D287401,
	RemoteConfigSettingsHelper_GetSafeNumberArray_m6F7F2F03025037FD126645E07F1E02D8B832D1B5,
	RemoteConfigSettingsHelper_GetSafeArrayFloat_mA8DFB1631F2609C31129FDAB4D4CE05E6B736F90,
	RemoteConfigSettingsHelper_GetSafeArrayBool_m60A6A3C01742D5E3B9FC0E3DCAF13D02AC023976,
	RemoteConfigSettingsHelper_GetSafeArrayStringValue_m45F246B8B5175E4517BE03DE3BE6458C612545A7,
	RemoteConfigSettingsHelper_GetDictionary_mE027FC91EE6145E2C58D409F80E9D068D56E87E0,
	RemoteConfigSettingsHelper_GetDictionary_m3220BB5F2646E7A22FC92BA005B7EC79889808C6,
	RemoteConfigSettingsHelper_GetArrayArrayEntries_mB348BDBF50C08F110D09DDD47168FD9DE72A5483,
	RemoteConfigSettingsHelper_GetArrayMapEntries_m445A8ED964B7D7007140912A750B77354B8680E3,
	NULL,
	RemoteConfigSettingsHelper_GetArrayEntries_m4BA4CDBBAC856DF3496E6BA47632C7B1CC43910A,
	RemoteConfigSettingsHelper_GetMixedArrayEntries_m0EBF1D34C1F56CCEDB121C343991AB54AD6BF45D,
	RemoteConfigSettingsHelper_SetDictKeyType_m890D2854D45C8A65FD876A54D674BB1375AE10CE,
	NULL,
	NULL,
	NULL,
	ContinuousEvent_ConfigureCustomEvent_mA02C1E739FCF1E64F542D81BEB97C85EC8759763,
	ContinuousEvent_ConfigureEvent_m831E004BEE026472ADC3C2C47E124486A5948FFB,
	ContinuousEvent_InternalRegisterCollector_m3292B4C94CA4A63E3B5A6137ED74FE3D181C468D,
	ContinuousEvent_InternalSetEventHistogramThresholds_m57CB9FA59A53A6068DD9F7D431DE3EC4EF1658E1,
	ContinuousEvent_InternalSetCustomEventHistogramThresholds_mDA7D59D22E10AE79B97317F0F865827A4EBE5597,
	ContinuousEvent_InternalConfigureCustomEvent_m383B508A1F11756EE022A51ACF3E3BEB93E4E58D,
	ContinuousEvent_InternalConfigureEvent_mE50BFD8B7B022ED34ADE4D4314FF6E0AB3ABD0DD,
	ContinuousEvent_IsInitialized_m99BD2642ABA7CE3AF5E4B709C30EA70247B17B60,
	ContinuousEvent__ctor_m4D17724B72E6ACB92E66082474565F77D567B19C,
	AnalyticsSessionInfo_add_sessionStateChanged_mF51918A05A7489228D8EF3EA7E5CF34B1294FCD6,
	AnalyticsSessionInfo_remove_sessionStateChanged_m92DCC7984A5447F18D61BF0ECBA4D3C2AAACB2CC,
	AnalyticsSessionInfo_CallSessionStateChanged_m6C3C7DD13064E37D7C3AE9411355BCEF77C4664B,
	AnalyticsSessionInfo_get_sessionState_mAE350FF1756FA691718D35E6ABB263818DA4AEF3,
	AnalyticsSessionInfo_get_sessionId_m2022F1F0127FBBB17BC8B77CA5FCBEFF3FA2A4CA,
	AnalyticsSessionInfo_get_sessionCount_m7348C0E3A6A0B2EEFDFB1C957D0102D4C092856C,
	AnalyticsSessionInfo_get_sessionElapsedTime_m4ADE521B2B2E25F2C0D5542A135D1EE337D64BAB,
	AnalyticsSessionInfo_get_sessionFirstRun_m7A053D6BA505100F72698748372C21081702251A,
	AnalyticsSessionInfo_get_userId_m4CD30B7A030EBE80357D1A5FF3836E77E0CFC196,
	AnalyticsSessionInfo_get_customUserId_m2403A7B82C72578E70B00F884CA833BDA03E37D7,
	AnalyticsSessionInfo_set_customUserId_mE11E38440F8F4EBC313579611C1D7FB3422DA129,
	AnalyticsSessionInfo_get_customDeviceId_m1B0544126B401D4E1A90FB86D121C88AE43C3F0E,
	AnalyticsSessionInfo_set_customDeviceId_mD14A133995AF3EE79FEC8A0A6E5261FD3EA883D3,
	AnalyticsSessionInfo_add_identityTokenChanged_m64C2D38EAE253AA50478B858E5E01CA1B13EFF78,
	AnalyticsSessionInfo_remove_identityTokenChanged_mA043898827661833FCE89DCC3682B5FF3FC19C69,
	AnalyticsSessionInfo_CallIdentityTokenChanged_m1AD21A1840BCB9CB222455F609DBBF7B7B380911,
	AnalyticsSessionInfo_get_identityToken_m6F9C473FE5266E55DFAA285A3F8BF52C9DB89722,
	AnalyticsSessionInfo_get_identityTokenInternal_m417D73073A6991448FB614CE628036E4FC86CE42,
	AnalyticsSessionInfo_get_customUserIdInternal_m780E73B31CBC707E70C64D8EBF5EE43CBC79310A,
	AnalyticsSessionInfo_set_customUserIdInternal_m9DB96EAC021F4B647F2C922F82E5D9DE5C8A646D,
	AnalyticsSessionInfo_get_customDeviceIdInternal_m39881526880F9740E854FA7BD05B13455DA5F4F6,
	AnalyticsSessionInfo_set_customDeviceIdInternal_m66B601C96BABF6D316A9AB3F9E88F41977CCAD38,
	SessionStateChanged__ctor_m8E04BB6766439BA455F9C808171BD791230496D8,
	SessionStateChanged_Invoke_mB9195B30A226CB3E53E470C24FD31E039E5BB4F5,
	SessionStateChanged_BeginInvoke_m8B5D445C34B6C14E3F1D41A97EF7BDA062E1F8B4,
	SessionStateChanged_EndInvoke_m1A58800B523F53B51A8DAE05458ED2EC90FF5B69,
	IdentityTokenChanged__ctor_m1970F8BEEDAA84A8FC5ABB973C0DB62FA2AA8312,
	IdentityTokenChanged_Invoke_m22D3DA825F0D6E701D050EFA3D35E84DFAC7F8D9,
	IdentityTokenChanged_BeginInvoke_m0A9BD21CD1D86D1FD3486792053E3741D272F2C7,
	IdentityTokenChanged_EndInvoke_mE011A609235BBFC3748E76B2A0E3A22E5FDC2D74,
	CustomEventData__ctor_m8CD5F577E855AF6354FDE06FD509480850AA03F8,
	CustomEventData__ctor_m56F78E56BEB38960137D4310EBA08871D1F8B4E5,
	CustomEventData_Finalize_m9BEB37895872F784C37236033CD022211DA8219D,
	CustomEventData_Destroy_mCE7CEA4B647288F3A6673F325D2397603BA927D0,
	CustomEventData_Dispose_mF7FAD8DFA5AE64CA3F3BBDCA22D1ACBE810FAEA6,
	CustomEventData_Internal_Create_mA00A12FE26AE334F0251226C8ED5FB02882BC2ED,
	CustomEventData_Internal_Destroy_m8556DAF414664C7669D1D38217AE2E763B4CEC59,
	CustomEventData_AddString_m42AB3534D86CCFCDF54E3F3006B1502C8BA269C9,
	CustomEventData_AddInt32_m425314D03ECCAFE49DFDAEFDC50E393C4EAACD66,
	CustomEventData_AddUInt32_m44ECC55B2F900092F5C92A5053B83D0955211946,
	CustomEventData_AddInt64_m168582B1D88D236B83134237B44A61595F226EA1,
	CustomEventData_AddUInt64_m487A081EB24E08F9A504E82C9FB5448D89190C7B,
	CustomEventData_AddBool_mFB872845AE96AC3BB7E7ADC9DB297F00CC355747,
	CustomEventData_AddDouble_mE63C2C3EAD92FD83869373A3A3D942641B428897,
	CustomEventData_AddDictionary_m8277B05DF41A78C3C8BEA8DB0724CABB32769221,
	Analytics_get_initializeOnStartup_m286CE9E15C3CBFBE64F4D6DBBC222CAE5A59028D,
	Analytics_set_initializeOnStartup_m00B6044E2C752D2E86F74147493464CF047B5986,
	Analytics_ResumeInitialization_m60FB92BBEEA51A13A1BF21A43BC9BA0B6A764FE2,
	Analytics_ResumeInitializationInternal_mEE921E4120696DAD668731D837AAA9A736FC9017,
	Analytics_get_initializeOnStartupInternal_mDEBF337AB78DDCC3AEFBA439B6EFE2A733002D15,
	Analytics_set_initializeOnStartupInternal_m8BD4203D3356D13B4F0FB541B562B458DA31E0FF,
	Analytics_IsInitialized_mBC7670704DE959791C8DF859A7C66D5DD5168F12,
	Analytics_get_enabledInternal_mD94B221876BDD04801C9E9A96302A09DF3FD1C21,
	Analytics_set_enabledInternal_m68F6A31D3874B593BDB1E7842354CFBFE5B2FA0C,
	Analytics_get_playerOptedOutInternal_m846E8370E100DF0753FD82CCD2970DF45E47408C,
	Analytics_get_eventUrlInternal_mB30C0B42F708A2FF2085DB56646E95BC26550E43,
	Analytics_get_configUrlInternal_m727BDC9443A2A9D7FFC5F3EF89B6FC38A6B6C8D2,
	Analytics_get_dashboardUrlInternal_m3E961F972F59D3A76E28629911F0AC4D48F54F2A,
	Analytics_get_limitUserTrackingInternal_m503016D011BEE05EEC34C58632BECF51A16A1FCE,
	Analytics_set_limitUserTrackingInternal_m491EBEDB60C6169F76C9C2EAF309246534686F23,
	Analytics_get_deviceStatsEnabledInternal_m0D31256018E5139AA699CEB4DD87E16B816F3210,
	Analytics_set_deviceStatsEnabledInternal_mA64D45012F4E8F73C4E1B5A63D3AB7EBCD7C77CC,
	Analytics_FlushArchivedEvents_m750AA08AE2D12AED629DF3919A61254C2D0151CF,
	Analytics_Transaction_m6A16C2852B2365FFA31B407BE5EBF4A6525B5488,
	Analytics_SendCustomEventName_m714D4EBF35293ACA77E6B8EDA5D454B760992CCF,
	Analytics_SendCustomEvent_mDFD010BF86A285FB5620405455D04D31FD0F014B,
	Analytics_IsCustomEventWithLimitEnabled_m54BB0A094225A5BAF03C99F927AC04735CED901C,
	Analytics_EnableCustomEventWithLimit_m160FA963BC43689AB806AA8F8D2FB358AF85C397,
	Analytics_IsEventWithLimitEnabled_m74EDF34F2314AEA76D79EAE89C88DD622EEFC738,
	Analytics_EnableEventWithLimit_m0C3395133D272B218AE76875CA146E5EA4B40959,
	Analytics_RegisterEventWithLimit_m1C2E9701EE73122C7788186C9EA9A3C23CF51252,
	Analytics_RegisterEventsWithLimit_m84C18E052B2CF10E4103667A0D0549D6A5BBF828,
	Analytics_SendEventWithLimit_m084C487537F41F55CA161ED14838A6B892421956,
	Analytics_SetEventWithLimitEndPoint_m03BF4DD33AA8FF698578817602AEE7F7F98DBEFA,
	Analytics_SetEventWithLimitPriority_m1E7B7AF0F36EF2F89A0E96C0DF4EE667FEDB08B8,
	Analytics_QueueEvent_m6D14D5C66B8AEF17F76424435899714751E72B2D,
	Analytics_get_playerOptedOut_mAF35A3DFB6C2A227B2A345B1163E94A11FFC5AB4,
	Analytics_get_eventUrl_m50B1E732E8B96427BE8C9DF8EA8FE6C687A8FF53,
	Analytics_get_dashboardUrl_m52FFF2BAF2F2844812FB2FF4266B9F57F024AB2A,
	Analytics_get_configUrl_m459A2EDA5987546513E87F61D8204A64B430EC0C,
	Analytics_get_limitUserTracking_m0581A21AE831809855623F14E82BD648647CB5C4,
	Analytics_set_limitUserTracking_m4E675017801EACD2B5DB9FBAD2F089E3BE7F7859,
	Analytics_get_deviceStatsEnabled_m2F80A19ABE8D68CA8E364DB19D0E1529D3F45D8D,
	Analytics_set_deviceStatsEnabled_mBD793306FA86CBA16BF930B3435E1BC059402144,
	Analytics_get_enabled_mE4A7B84E816A7CEC2E8AB90D3165DB5B83D2C141,
	Analytics_set_enabled_mF433189C114B6560C5DD8E7D4B6B3B06B7F5679C,
	Analytics_FlushEvents_m18887A3537F3EC6DD07C32A6EE4D621FF3CDCEBF,
	Analytics_SetUserId_mF800DBBDDA68C5FC9372A2C8299165B5610FF2FB,
	Analytics_SetUserGender_m1297C836A2FD612D21AF8F6B6626D3C6E8BFE33A,
	Analytics_SetUserBirthYear_m5096D5990515A0582BC216E7C7484D10480CB04B,
	Analytics_SendUserInfoEvent_mC025051A4E5FAEB44036A47E3615392CAED2E30F,
	Analytics_Transaction_mB5A6694CA8B93C23598952EF8FF435AC67885FEB,
	Analytics_Transaction_m9BD442FC02953AC2C573E3340E098751608ADEBB,
	Analytics_Transaction_m237599F1B6EDBB64DB6CE0CC6ED9A6E4B3391F96,
	Analytics_CustomEvent_m9AB6593D77ED4BF66F5C613E1C40CD103C2CEAEB,
	Analytics_CustomEvent_mE0EF0AD12B037F6B4EEE5E23128762DF83E76458,
	Analytics_CustomEvent_m663CDDCA04FF832169D1BCEE01C943A7FB6D4363,
	Analytics_EnableCustomEvent_m4022096A40E49F647EFC2F29D5DB22D7F1DC53B4,
	Analytics_IsCustomEventEnabled_mBAD0C826A757E4959C391A0CAB3565FA6EF8CCB8,
	Analytics_RegisterEvent_mC82148D11A02968E347F9D11237D50826223A7C3,
	Analytics_RegisterEvent_m202C3CC5E766F86B080009E64218EAD375CDCFF3,
	Analytics_RegisterEvent_m82BE2F186E2F452E6FFE10DC44207C3438E660E4,
	Analytics_SendEvent_m9E942728E301EA9E90741ADD1ED71DAD118819B5,
	Analytics_SetEventEndPoint_m770B11C00EB5727517405ACAAA421DA72A6324F0,
	Analytics_SetEventPriority_m5B4812B18DEDBA2B41D2368EB7881B4A38914F7F,
	Analytics_EnableEvent_m15322E9698E5545E301EE2053C4E8AB5EACA3C95,
	Analytics_IsEventEnabled_m38092B674BDB5E0E7046A0939808F08962605093,
};
static const int32_t s_InvokerIndices[216] = 
{
	8887,
	8887,
	8887,
	8887,
	8887,
	8887,
	8868,
	9089,
	6886,
	9089,
	9089,
	8993,
	8399,
	7488,
	8425,
	7527,
	8626,
	7739,
	8505,
	7631,
	8220,
	7254,
	8220,
	9018,
	9031,
	0,
	7631,
	7631,
	6693,
	8505,
	9089,
	9089,
	9020,
	2798,
	4364,
	2524,
	3881,
	3881,
	3881,
	4364,
	3881,
	4364,
	4364,
	4364,
	7545,
	8882,
	7963,
	5696,
	8993,
	8887,
	4364,
	4168,
	3419,
	2434,
	3469,
	2472,
	3584,
	2573,
	3518,
	2524,
	3185,
	2316,
	3185,
	4216,
	4250,
	0,
	2524,
	2524,
	1801,
	3518,
	4364,
	4364,
	4218,
	7538,
	8503,
	8503,
	6569,
	6780,
	6344,
	6666,
	7538,
	8424,
	7536,
	7536,
	7480,
	7523,
	7733,
	7234,
	7610,
	7612,
	8503,
	7610,
	7610,
	0,
	8503,
	8503,
	6141,
	0,
	0,
	0,
	5278,
	4755,
	6538,
	4947,
	5824,
	5278,
	4755,
	8993,
	4364,
	8887,
	8887,
	6120,
	9018,
	9019,
	9019,
	9019,
	8993,
	9031,
	9031,
	8887,
	9031,
	8887,
	8887,
	8887,
	8887,
	9031,
	9031,
	9031,
	8887,
	9031,
	8887,
	2798,
	1358,
	476,
	3881,
	2798,
	3881,
	1801,
	3881,
	4364,
	3881,
	4364,
	4364,
	4364,
	7545,
	8882,
	2322,
	2320,
	2326,
	2321,
	2327,
	2316,
	2319,
	3185,
	8993,
	8868,
	9018,
	9018,
	8993,
	8868,
	8993,
	8993,
	8868,
	8993,
	9031,
	9031,
	9031,
	8993,
	8868,
	8993,
	8868,
	8993,
	4942,
	8399,
	8399,
	8399,
	7486,
	6531,
	5809,
	4628,
	4628,
	5824,
	5824,
	5815,
	5696,
	8993,
	9031,
	9031,
	9031,
	8993,
	8868,
	8993,
	8868,
	8993,
	8868,
	9018,
	8399,
	8395,
	8395,
	8399,
	6523,
	5266,
	4941,
	8399,
	7491,
	7489,
	7486,
	8399,
	5270,
	4945,
	4752,
	5824,
	5824,
	5815,
	5809,
	6531,
};
static const Il2CppTokenRangePair s_rgctxIndices[6] = 
{
	{ 0x0600001A, { 0, 2 } },
	{ 0x06000042, { 2, 2 } },
	{ 0x0600005E, { 4, 5 } },
	{ 0x06000062, { 9, 2 } },
	{ 0x06000063, { 11, 2 } },
	{ 0x06000064, { 13, 2 } },
};
extern const uint32_t g_rgctx_T_tF9CD62F82FF4964327C905CA12FBA01E6BCF640B;
extern const uint32_t g_rgctx_T_tF9CD62F82FF4964327C905CA12FBA01E6BCF640B;
extern const uint32_t g_rgctx_T_tFCAFD7AB64B2F9D7AC34DFAC1ADCB7870EF1F7EC;
extern const uint32_t g_rgctx_T_tFCAFD7AB64B2F9D7AC34DFAC1ADCB7870EF1F7EC;
extern const uint32_t g_rgctx_TU5BU5D_tD1E2FFB0F288700ED057ED083143BEAC12D7F66A;
extern const uint32_t g_rgctx_Func_3_t192EF471B008654E19497871E3F3ECC930A73382;
extern const uint32_t g_rgctx_Func_3_Invoke_mBB3E39D182195B98DF02EBFC6B13A44892516E89;
extern const uint32_t g_rgctx_T_t9A260EA1D266EC0D00E837D8F519514A8FE25176;
extern const uint32_t g_rgctx_TU5BU5D_tD1E2FFB0F288700ED057ED083143BEAC12D7F66A;
extern const uint32_t g_rgctx_T_t3DF06769A9DC072E630DA8203495A9B4B0F08664;
extern const uint32_t g_rgctx_Func_1_t9152FEB1F3CD954AECB429C033D6525260A9D493;
extern const uint32_t g_rgctx_T_t53404F686589124EF835CE602A8EE64324B36582;
extern const uint32_t g_rgctx_TU5BU5D_t6D832D40EEC6DD28F41D72B627301514BC467EA8;
extern const uint32_t g_rgctx_T_t0A6F2C91E3DD10EF722EABD2594A1C7A996153F2;
extern const uint32_t g_rgctx_TU5BU5D_tF36B24ADCE8D4B9A3A0A3729FF9EA50E5FA6FB89;
static const Il2CppRGCTXDefinition s_rgctxValues[15] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tF9CD62F82FF4964327C905CA12FBA01E6BCF640B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF9CD62F82FF4964327C905CA12FBA01E6BCF640B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tFCAFD7AB64B2F9D7AC34DFAC1ADCB7870EF1F7EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFCAFD7AB64B2F9D7AC34DFAC1ADCB7870EF1F7EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD1E2FFB0F288700ED057ED083143BEAC12D7F66A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t192EF471B008654E19497871E3F3ECC930A73382 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_3_Invoke_mBB3E39D182195B98DF02EBFC6B13A44892516E89 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9A260EA1D266EC0D00E837D8F519514A8FE25176 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD1E2FFB0F288700ED057ED083143BEAC12D7F66A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3DF06769A9DC072E630DA8203495A9B4B0F08664 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t9152FEB1F3CD954AECB429C033D6525260A9D493 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t53404F686589124EF835CE602A8EE64324B36582 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6D832D40EEC6DD28F41D72B627301514BC467EA8 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t0A6F2C91E3DD10EF722EABD2594A1C7A996153F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF36B24ADCE8D4B9A3A0A3729FF9EA50E5FA6FB89 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityAnalyticsModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsModule_CodeGenModule = 
{
	"UnityEngine.UnityAnalyticsModule.dll",
	216,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	6,
	s_rgctxIndices,
	15,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_UnityAnalyticsModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
