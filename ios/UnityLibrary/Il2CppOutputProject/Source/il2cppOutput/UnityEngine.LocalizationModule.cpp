﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_LocalizationModule[];
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* LocalizationAsset__ctor_m46CD29B0A6C783B9E1FC59DE2E0F0D1AEAD99024_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t835A533FCA3623E00AB95D0BB273228C822AF99E 
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* ___0_locAsset, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LocalizationAsset__ctor_m46CD29B0A6C783B9E1FC59DE2E0F0D1AEAD99024 (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LocalizationAsset__ctor_m46CD29B0A6C783B9E1FC59DE2E0F0D1AEAD99024_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_THIS(methodExecutionContextThis, (&__this));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, LocalizationAsset__ctor_m46CD29B0A6C783B9E1FC59DE2E0F0D1AEAD99024_RuntimeMethod_var, methodExecutionContextThis, NULL, NULL);
	CHECK_METHOD_ENTRY_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 0));
	CHECK_METHOD_EXIT_SEQ_POINT(methodExitChecker, methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 1));
	{
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 2));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 3));
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object__ctor_m2149FA40CEC8D82AC20D3508AB40C0D8EFEF68E6(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 3));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 4));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 5));
		STORE_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 6));
		LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B(__this, NULL);
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 6));
		CHECK_SEQ_POINT(methodExecutionContext, (g_sequencePointsUnityEngine_LocalizationModule + 7));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* ___0_locAsset, const RuntimeMethod* method) 
{
	typedef void (*LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B_ftn) (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11*);
	static LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (LocalizationAsset_Internal_CreateInstance_mC84135ED0D09DB20376DD8FD4DDF022C8C29D25B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.LocalizationAsset::Internal_CreateInstance(UnityEngine.LocalizationAsset)");
	_il2cpp_icall_func(___0_locAsset);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LocalizationAsset_SetLocalizedString_m84173DFA5EA21BA9BE6F7624809B1CA9E372B851 (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* __this, String_t* ___0_original, String_t* ___1_localized, const RuntimeMethod* method) 
{
	typedef void (*LocalizationAsset_SetLocalizedString_m84173DFA5EA21BA9BE6F7624809B1CA9E372B851_ftn) (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11*, String_t*, String_t*);
	static LocalizationAsset_SetLocalizedString_m84173DFA5EA21BA9BE6F7624809B1CA9E372B851_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (LocalizationAsset_SetLocalizedString_m84173DFA5EA21BA9BE6F7624809B1CA9E372B851_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.LocalizationAsset::SetLocalizedString(System.String,System.String)");
	_il2cpp_icall_func(__this, ___0_original, ___1_localized);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* LocalizationAsset_GetLocalizedString_m2715A79A67104837D21825FDA24C144C0E6ADE75 (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* __this, String_t* ___0_original, const RuntimeMethod* method) 
{
	typedef String_t* (*LocalizationAsset_GetLocalizedString_m2715A79A67104837D21825FDA24C144C0E6ADE75_ftn) (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11*, String_t*);
	static LocalizationAsset_GetLocalizedString_m2715A79A67104837D21825FDA24C144C0E6ADE75_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (LocalizationAsset_GetLocalizedString_m2715A79A67104837D21825FDA24C144C0E6ADE75_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.LocalizationAsset::GetLocalizedString(System.String)");
	String_t* icallRetVal = _il2cpp_icall_func(__this, ___0_original);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* LocalizationAsset_get_localeIsoCode_m9313AB325D233D2797370673C25D2FE14E46F25A (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* __this, const RuntimeMethod* method) 
{
	typedef String_t* (*LocalizationAsset_get_localeIsoCode_m9313AB325D233D2797370673C25D2FE14E46F25A_ftn) (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11*);
	static LocalizationAsset_get_localeIsoCode_m9313AB325D233D2797370673C25D2FE14E46F25A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (LocalizationAsset_get_localeIsoCode_m9313AB325D233D2797370673C25D2FE14E46F25A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.LocalizationAsset::get_localeIsoCode()");
	String_t* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LocalizationAsset_set_localeIsoCode_m32F7A49BB3A7CBA6AA27B1332A5559C99BA33A09 (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	typedef void (*LocalizationAsset_set_localeIsoCode_m32F7A49BB3A7CBA6AA27B1332A5559C99BA33A09_ftn) (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11*, String_t*);
	static LocalizationAsset_set_localeIsoCode_m32F7A49BB3A7CBA6AA27B1332A5559C99BA33A09_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (LocalizationAsset_set_localeIsoCode_m32F7A49BB3A7CBA6AA27B1332A5559C99BA33A09_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.LocalizationAsset::set_localeIsoCode(System.String)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool LocalizationAsset_get_isEditorAsset_m3988BFB87CC7B61F1DA24D48B4F5E78C23FB4149 (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* __this, const RuntimeMethod* method) 
{
	typedef bool (*LocalizationAsset_get_isEditorAsset_m3988BFB87CC7B61F1DA24D48B4F5E78C23FB4149_ftn) (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11*);
	static LocalizationAsset_get_isEditorAsset_m3988BFB87CC7B61F1DA24D48B4F5E78C23FB4149_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (LocalizationAsset_get_isEditorAsset_m3988BFB87CC7B61F1DA24D48B4F5E78C23FB4149_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.LocalizationAsset::get_isEditorAsset()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LocalizationAsset_set_isEditorAsset_m5FCCD7D337594A82756ED5B59436EE14AF58824E (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11* __this, bool ___0_value, const RuntimeMethod* method) 
{
	typedef void (*LocalizationAsset_set_isEditorAsset_m5FCCD7D337594A82756ED5B59436EE14AF58824E_ftn) (LocalizationAsset_tB52AEBFEF8CB32C3F82C34F00E18764154BF9B11*, bool);
	static LocalizationAsset_set_isEditorAsset_m5FCCD7D337594A82756ED5B59436EE14AF58824E_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (LocalizationAsset_set_isEditorAsset_m5FCCD7D337594A82756ED5B59436EE14AF58824E_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.LocalizationAsset::set_isEditorAsset(System.Boolean)");
	_il2cpp_icall_func(__this, ___0_value);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
