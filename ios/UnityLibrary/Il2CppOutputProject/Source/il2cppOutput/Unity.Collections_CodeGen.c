﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* AutoFreeAllocator_TryU24BurstManaged_mE86E6DC99C88B259454710D7CC1906F995968ECF_RuntimeMethod_var;
extern const RuntimeMethod* AutoFreeAllocator_Try_m4FFF073754714947DFC8F4D7134E74CDC78C019C_RuntimeMethod_var;
extern const RuntimeMethod* RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8_RuntimeMethod_var;
extern const RuntimeMethod* RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35_RuntimeMethod_var;
extern const RuntimeMethod* SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA_RuntimeMethod_var;
extern const RuntimeMethod* SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB_RuntimeMethod_var;
extern const RuntimeMethod* StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2_RuntimeMethod_var;
extern const RuntimeMethod* StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234_RuntimeMethod_var;



extern void EmbeddedAttribute__ctor_mB9EA4CCF3A3DC39A3BC92CFE9557FFAA77D15404 (void);
extern void IsUnmanagedAttribute__ctor_m15974D59768AFF916E346F7107F7FF7F6AD9099C (void);
extern void EarlyInitHelpers__cctor_m19122BBB2CF7BC74606113B23BF74FA959A5D467 (void);
extern void EarlyInitHelpers_FlushEarlyInits_m2B9C35967B87AF2BD2018C649BF964DFD5C40033 (void);
extern void EarlyInitHelpers_JobReflectionDataCreationFailed_mD6AB08D5BB411CCE38A87793C3C7062EC91FD1EC (void);
extern void EarlyInitFunction__ctor_m74E4D36634E32C8FE21AA86C7D9597F7FD77E0B6 (void);
extern void EarlyInitFunction_Invoke_mA2104BB505B5D3F16622D30BDA3AF83F3FE2FB2D (void);
extern void DOTSCompilerGeneratedAttribute__ctor_m8689CDD675567BC580F1FADCCF386B0FEE07B0E5 (void);
extern void Spinner_Acquire_m9B13CD5A5170792255A188C07C7B96B65C3DB968 (void);
extern void Spinner_Release_mEBA54EE373D18FE0473625B128784C92B65C6D2A (void);
extern void AllocatorManager_Free_mB8AE9C4CB989A9121F4E3F2E6C7781076DFB3025 (void);
extern void AllocatorManager_CheckDelegate_m52D3F12472A2BBC5A28D2F4B5011B19D2E36AC61 (void);
extern void AllocatorManager_UseDelegate_mEB18420309DAA2CC710BA123C6996C9FB6FC3798 (void);
extern void AllocatorManager_allocate_block_mBEB6E6FDC334118DB679CF2619EBB3FF4FDD7FB5 (void);
extern void AllocatorManager_forward_mono_allocate_block_mD2A9A49DFC8CBDC39F27E2749048ABC91E124519 (void);
extern void AllocatorManager_LegacyOf_mAD212C1A7F5295C8987A6C9D7F81E8FF42E0A3BF (void);
extern void AllocatorManager_TryLegacy_mF4F0B8CE7B0293504FA12A6F9C4ACFF28B59FF79 (void);
extern void AllocatorManager_Try_m24A6A4EF594F8909B5677C94C4788F365E02E7F9 (void);
extern void AllocatorManager_IsCustomAllocator_m38BCD079BAB0D64962201CD05D671C2A42CE1909 (void);
extern void AllocatorManager__cctor_m3E94344CB4CD852C9427FE9394EBE4EC36BFEEA1 (void);
extern void AllocatorManager_InitializeU24StackAllocator_Try_000000ABU24BurstDirectCall_m62B68CEB2CEC113F6649A86053857969363D4439 (void);
extern void AllocatorManager_InitializeU24SlabAllocator_Try_000000B9U24BurstDirectCall_m114F46E2185704C30FD2EBC834888CD4DD147EED (void);
extern void TryFunction__ctor_m10C4A7B32E87301727B84D8CBA081FABAE3CCE53 (void);
extern void TryFunction_Invoke_mED723D46A7B0C4B590ABECE0868EA02AD94D07A2 (void);
extern void AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63 (void);
extern void AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1 (void);
extern void AllocatorHandle_op_Implicit_mDCF4431F31BB4A09438AE644785C4273F86B2B8D (void);
extern void AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F (void);
extern void AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9 (void);
extern void AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2 (void);
extern void AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF (void);
extern void AllocatorHandle_get_IsAutoDispose_m605B841B976828E0219FFA8C9B15585F497C80E4 (void);
extern void AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3 (void);
extern void AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A (void);
extern void AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939 (void);
extern void AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243 (void);
extern void AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4 (void);
extern void Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A (void);
extern void Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7 (void);
extern void Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08 (void);
extern void Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633 (void);
extern void Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F (void);
extern void Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED (void);
extern void Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225 (void);
extern void StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267 (void);
extern void StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558 (void);
extern void StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234 (void);
extern void StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB (void);
extern void StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2 (void);
extern void Try_000000ABU24PostfixBurstDelegate__ctor_m1FA005030A4C2F4D41A3A4B0DC4E389885D94589 (void);
extern void Try_000000ABU24PostfixBurstDelegate_Invoke_m2603FA862A2883ED15CC6BAA38C0587AF3B5C18F (void);
extern void Try_000000ABU24BurstDirectCall_GetFunctionPointerDiscard_m437C4A40179A23A6C038F34C027477873F6E5234 (void);
extern void Try_000000ABU24BurstDirectCall_GetFunctionPointer_m36EE8A97493668F5C9E13B62859DEA396C70D149 (void);
extern void Try_000000ABU24BurstDirectCall_Constructor_m9A29EEBC364EEAFCA2A24D02607DF777CBB6A2DC (void);
extern void Try_000000ABU24BurstDirectCall_Initialize_m884465F80F4848B5961979B0B066E75F4E8F40FC (void);
extern void Try_000000ABU24BurstDirectCall__cctor_mA7788848AEBE4C739E58C844C44E53126CF65D08 (void);
extern void Try_000000ABU24BurstDirectCall_Invoke_mB50DDBC5F5CAFBA77D4394F23361901DF05D110F (void);
extern void SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE (void);
extern void SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612 (void);
extern void SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD (void);
extern void SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB (void);
extern void SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912 (void);
extern void SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA (void);
extern void Try_000000B9U24PostfixBurstDelegate__ctor_m337A82A5B9945C843D8678A471B9C936859B74CE (void);
extern void Try_000000B9U24PostfixBurstDelegate_Invoke_m7CA5052C1AB537489DEAACEC0700B8FA84DDFF13 (void);
extern void Try_000000B9U24BurstDirectCall_GetFunctionPointerDiscard_m113DF748656427E3A8295EF6C755C9B838DB7726 (void);
extern void Try_000000B9U24BurstDirectCall_GetFunctionPointer_mE5972A3AFD6CF81A52CEF0CB913D99A511AB56B5 (void);
extern void Try_000000B9U24BurstDirectCall_Constructor_m8018D3AC02AEFC7EBCCD2379BBF3D0525A0D774D (void);
extern void Try_000000B9U24BurstDirectCall_Initialize_mC6B7DE10ED47AB9B5D173AA0E60D181DA7F0E954 (void);
extern void Try_000000B9U24BurstDirectCall__cctor_mB15CC0C076D7890FEF19A88CD1814E2131AE3188 (void);
extern void Try_000000B9U24BurstDirectCall_Invoke_m9D2AFCB01D45314F4272BE199F77F848A3EF5675 (void);
extern void TableEntry__cctor_mCA16889126B2ED5EF69666F8B0376FCC8834FCE1 (void);
extern void IsAutoDispose__cctor_m433B37771502FF75597D5E68EE586209D5C9DF02 (void);
extern void Managed__cctor_mE3BC99DF4AF7BC63DE01424848BDC790B53500BA (void);
extern void AutoFreeAllocator_Update_m604EB4FA2A968881010E1DFA00567370D3833B79 (void);
extern void AutoFreeAllocator_FreeAll_m88244CFEADFA303DB16F16BBAABF74937615E707 (void);
extern void AutoFreeAllocator_Dispose_m465D490D2A99DCCF84EA24945014C82E7032FB25 (void);
extern void AutoFreeAllocator_Try_mEE9F186E12A650296921481BCAACC4E0662C11CD (void);
extern void AutoFreeAllocator_Try_m4FFF073754714947DFC8F4D7134E74CDC78C019C (void);
extern void AutoFreeAllocator_get_Handle_m86CF40C2F9FB7A51F4F4F4B86834F4AFAC2475C3 (void);
extern void AutoFreeAllocator_TryU24BurstManaged_mE86E6DC99C88B259454710D7CC1906F995968ECF (void);
extern void Try_000000E3U24PostfixBurstDelegate__ctor_m27B61F05DBF00EEEB47251AE625B11C76F63E31F (void);
extern void Try_000000E3U24PostfixBurstDelegate_Invoke_mD82F3F33E6C9297016D083430D1AF4270655C47B (void);
extern void Try_000000E3U24BurstDirectCall_GetFunctionPointerDiscard_mFFFE735110FDD2C2EB168A4B8FEA018FD2955963 (void);
extern void Try_000000E3U24BurstDirectCall_GetFunctionPointer_m8882BF00E3841F12C4328BC70EE81DD70DF98477 (void);
extern void Try_000000E3U24BurstDirectCall_Constructor_mA93FDF027D4C05D5B566E0B83D8E7CE3EBA93BD3 (void);
extern void Try_000000E3U24BurstDirectCall_Initialize_mA6B96FE892305FE1B9B1AB7C8492F29BDDFD84E6 (void);
extern void Try_000000E3U24BurstDirectCall__cctor_m12F864C33F1F28EE2BC01723BA8B420C7EDB67C0 (void);
extern void Try_000000E3U24BurstDirectCall_Invoke_m34FD4768C4ABCDC403E19CBC5FEC9E901CED9480 (void);
extern void Bitwise_IsSet_m67C151D3322C524985721C6773365295D00AFF21 (void);
extern void CollectionHelper_Hash_mFB14DD4BA7288CEDF90E514A9397FB9C27E55293 (void);
extern void CollectionHelper_ShouldDeallocate_m505E7EDBA71F02BAF52CC9DCD7C593CDA85D5465 (void);
extern void CollectionHelper_AssumePositive_mD1EC1F05F50F605141D9BA5D70C4332AC902B4B1 (void);
extern void DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085 (void);
extern void Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520 (void);
extern void Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185 (void);
extern void GenerateTestsForBurstCompatibilityAttribute_set_GenericTypeArguments_mF1072BD236646D4159082D3BB685B365EA45C01A (void);
extern void GenerateTestsForBurstCompatibilityAttribute__ctor_m86CEFB7F89EBCA8FECA15EC6F21CC9DFCDCDA235 (void);
extern void ExcludeFromBurstCompatTestingAttribute_set_Reason_m54DAB86449D0D2B47E1521F71AE433D1EC2598E5 (void);
extern void ExcludeFromBurstCompatTestingAttribute__ctor_mE85EC7FAEC0AF711D75010FE42AD803D9442806D (void);
extern void Unmanaged_Allocate_m7310B1FE896DEFFA18303D961C9859C8FF3D21E5 (void);
extern void Unmanaged_Free_m09F6EA89F368ED2C9E5EC5EA60C894C4434F4FD1 (void);
extern void Array_IsCustom_m7651BFF84F5AEFA592FEE86C834A85C373DDC126 (void);
extern void Array_CustomResize_mB51497D583399092F23AA773ABB64F0780610D82 (void);
extern void Array_Resize_mC7BE2965DE3FCF4014D43B606D94951480A65380 (void);
extern void NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA (void);
extern void NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780 (void);
extern void NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0 (void);
extern void NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792 (void);
extern void NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223 (void);
extern void NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E (void);
extern void NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D (void);
extern void NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9 (void);
extern void NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81 (void);
extern void NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5 (void);
extern void NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE (void);
extern void NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4 (void);
extern void NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64 (void);
extern void NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92 (void);
extern void NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9 (void);
extern void ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1 (void);
extern void ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922 (void);
extern void NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692 (void);
extern void NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77 (void);
extern void NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52 (void);
extern void NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972 (void);
extern void RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595 (void);
extern void RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F (void);
extern void RewindableAllocator_TryAllocate_mBD9EC80EDB768E80D25E5F72176E85D88FAFA418 (void);
extern void RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED (void);
extern void RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35 (void);
extern void RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E (void);
extern void RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8 (void);
extern void Union_get_m_current_mC92616C894B255E4DF5F3B0D95B5F147A0B1C57D (void);
extern void Union_set_m_current_m696C493651AB0C9F11496025FF50C67548A77A4F (void);
extern void Union_get_m_allocCount_m3FF45309DF2EF51A2F6BF02748D9787C2C293AB2 (void);
extern void Union_set_m_allocCount_m8053FC4C9A57DDF9A1AD199B7BDD7F0F16FE3988 (void);
extern void MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F (void);
extern void MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069 (void);
extern void MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F (void);
extern void MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC (void);
extern void Try_000009DEU24PostfixBurstDelegate__ctor_mD7D5D9437C5C5F8DCBAB81A1AD7CAD0ABD5D9D57 (void);
extern void Try_000009DEU24PostfixBurstDelegate_Invoke_mEE1AEF3715752949B54CDFA75F1A443FA3D892CB (void);
extern void Try_000009DEU24BurstDirectCall_GetFunctionPointerDiscard_mB9D526369D305BE3D3CB241A84BF5FF888164E05 (void);
extern void Try_000009DEU24BurstDirectCall_GetFunctionPointer_mE5AA8F3011F9D029B128029946E4038F2F7C1758 (void);
extern void Try_000009DEU24BurstDirectCall_Constructor_m8FE78DD7C9EB612C1F16E3FF36F13B71BC038674 (void);
extern void Try_000009DEU24BurstDirectCall_Initialize_m09D93B636BACE72BF4DD546222EAD1C0CAC77D1D (void);
extern void Try_000009DEU24BurstDirectCall__cctor_m3E2D9B8CE8BC183FF29B7A3D0051683F2E13B830 (void);
extern void Try_000009DEU24BurstDirectCall_Invoke_mB9BEE5675F857409B3B1B6246FC7A2E37A60AC83 (void);
extern void Unicode_IsValidCodePoint_m6F8516FA18D35B6D052FD6BDE01D9D497EA06B9D (void);
extern void Unicode_NotTrailer_m9FD6FC331044FE0EFC90E29A2808C4A6535E4CAD (void);
extern void Unicode_get_ReplacementCharacter_m525CDE0E6CAB489454025711F93FF832A600556A (void);
extern void Unicode_Utf8ToUcs_m013E3A507C4B6F5459B09C6EA8EA229BDC979827 (void);
extern void Unicode_UcsToUtf16_m14C1098270C0DFFAF6B48D47C3214344FD4FAE0E (void);
extern void Unicode_Utf8ToUtf16_mF3051E9181A57301EEF945C10B97D3C9356706DD (void);
extern void Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7 (void);
extern void Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729 (void);
extern void UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816 (void);
extern void UnsafeQueueData_DeallocateQueue_mF4E2184511650FED8D9B85ECDB14B6E43AB06373 (void);
extern void UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502 (void);
extern void UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E (void);
extern void xxHash3_Avx2HashLongInternalLoop_mCAEEE715FCB699CA2F1B947BCD252AA0F87D2B15 (void);
extern void xxHash3_Avx2ScrambleAcc_m64D8B68219EA3E164A61D2001E0969263CF098CE (void);
extern void xxHash3_Avx2Accumulate_mD57A48AB8FB3471A923F64F7C8B52FF8538E791D (void);
extern void xxHash3_Avx2Accumulate512_mBB4B8AAAA2DC7E6B1350597687C11B82E81CEF06 (void);
extern void xxHash3_Hash64Long_m9950702E864DCCD9B8DEAAE23E7CBB5E79D4AC62 (void);
extern void xxHash3_Hash128Long_mED9958D31B54E0E0666AAD34A52DE7CDEB802E6F (void);
extern void xxHash3_ToUint4_m811AB95294FBBC0F17A5358D0A22669691CE3633 (void);
extern void xxHash3_Read64LE_mD275A5EFD8727CDE8B8E280D4A5D5B82D5E3B195 (void);
extern void xxHash3_Write64LE_m79CC2011BF16363F2338D61BE43E99E6467A9437 (void);
extern void xxHash3_Mul32To64_m9210E9379305FC38A6D69C698F6E1A30013BC4F5 (void);
extern void xxHash3_XorShift64_mF4245CDE1C4AF6B1CC8F57AAE0DA8C7E04673CFC (void);
extern void xxHash3_Mul128Fold64_mF59DCB5142027D151F52C7748BFA28C32B3B8F38 (void);
extern void xxHash3_Avalanche_m059990B780566C6F04C66700B2BE7817B4FA2F18 (void);
extern void xxHash3_Mix2Acc_mDEB8D0C149D943295B8A3049A437578BE879BED8 (void);
extern void xxHash3_MergeAcc_mB01ADB1934EDFE8FE3B2AAB13DA6884EB1133A14 (void);
extern void xxHash3_DefaultHashLongInternalLoop_m9181A3A8DBE8DBEFF1B730ECC9A9AA5E93110F1B (void);
extern void xxHash3_DefaultAccumulate_m3D28C5486CC42D31D2D832F40DEFE1A7CF508CA5 (void);
extern void xxHash3_DefaultAccumulate512_mFADF15092DA5379116D3FCCFC4238ADBF48D85D7 (void);
extern void xxHash3_DefaultScrambleAcc_mA46D6E8E1BA4613A50B56C8536B0DA3F50437137 (void);
extern void xxHash3_Hash64LongU24BurstManaged_m71E36BBD116CCA46ED23162F80B08D3B2F782B4D (void);
extern void xxHash3_Hash128LongU24BurstManaged_m961A07284DAB6ADFF52EB4287E9D105AB971FDF6 (void);
extern void Hash64Long_00000A73U24PostfixBurstDelegate__ctor_m6C164D2A8C65A7C3ADFF1734A543736FAE47C206 (void);
extern void Hash64Long_00000A73U24PostfixBurstDelegate_Invoke_mD8B0912F807F954B2A1F6D05E52ECA05195EF29F (void);
extern void Hash64Long_00000A73U24BurstDirectCall_GetFunctionPointerDiscard_mB02B3782D721D3FD1E1BCC26C98397473D58A8B8 (void);
extern void Hash64Long_00000A73U24BurstDirectCall_GetFunctionPointer_m8443DD2CA1016BB02C434C4FA74A32A883AD456A (void);
extern void Hash64Long_00000A73U24BurstDirectCall_Constructor_m3A444F37DC10EC79F29AD476E4963F17AE2E0809 (void);
extern void Hash64Long_00000A73U24BurstDirectCall_Initialize_m6B9AF7B8C2553F712D21CBD389788FADAFA802A7 (void);
extern void Hash64Long_00000A73U24BurstDirectCall__cctor_m7AB1C1979E6BA01973D6552A22B50E30F0D89CB0 (void);
extern void Hash64Long_00000A73U24BurstDirectCall_Invoke_m907F360339DBE2BC742D85D814D46156865DD5B0 (void);
extern void Hash128Long_00000A7AU24PostfixBurstDelegate__ctor_mE7FF5D807EB74D77814701BA2CA6CBDE0B3C17D3 (void);
extern void Hash128Long_00000A7AU24PostfixBurstDelegate_Invoke_m4187EE5B0816EBBC2516E038078CC0187AE19A2B (void);
extern void Hash128Long_00000A7AU24BurstDirectCall_GetFunctionPointerDiscard_m64BBA60CA3638F64354F7588BFC5E707E906EEB8 (void);
extern void Hash128Long_00000A7AU24BurstDirectCall_GetFunctionPointer_m93A9EDD89E1114121B1BE2E9E7E319DD996981A7 (void);
extern void Hash128Long_00000A7AU24BurstDirectCall_Constructor_mBDFB62FF66EB710A87EE1643E3C54FBDAF5B7005 (void);
extern void Hash128Long_00000A7AU24BurstDirectCall_Initialize_m0700B71CBB558E5B4B8D0856861B65AE1094DE7E (void);
extern void Hash128Long_00000A7AU24BurstDirectCall__cctor_m09DDA1C5419D4D17E8719EAB46EB06E4E43E75F4 (void);
extern void Hash128Long_00000A7AU24BurstDirectCall_Invoke_m66696F33E60B8FB667A3436D08B467008EA17778 (void);
extern void UnsafeBitArray_Free_m593DC0AC7ADB85F86BF8E27CDCA5EF1395650D0D (void);
extern void UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1 (void);
extern void UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2 (void);
extern void UnsafeBitArray_IsSet_mC96E92F48DCF8062E347E4B9E43886B27235DDE9 (void);
extern void UnsafeBitArrayDebugView__ctor_mDCEA05186DBDBB94172636C9E6FC3D2F93FBDCD7 (void);
extern void UnsafeBitArrayDebugView_get_Bits_m257AD507B6FA78BD2A8817BDF8DBBD2A23A9A834 (void);
extern void UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC (void);
extern void UnsafeParallelHashMapData_DeallocateHashMap_m8D0FEE08B8522A1D05FBFFBBB43CB203304F114F (void);
extern void UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3 (void);
extern void UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC (void);
extern void UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1 (void);
extern void UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464 (void);
extern void UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065 (void);
extern void UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F (void);
extern void UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C (void);
extern void UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C (void);
extern void DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E (void);
extern void ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB (void);
extern void ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50 (void);
extern void UnsafeTextExtensions_AsUnsafeListOfBytes_mA80ABFE08762E38D788ACF506BEB4A0E3621D439 (void);
extern void UnsafeTextExtensions_AsUnsafeListOfBytesRO_mB4E57457A3442858265E8A664CCBBC8E0A4C6AF4 (void);
extern void UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1 (void);
extern void UnsafeText_Free_m22C162680EFB31663020E8FE94BE67596994A98B (void);
extern void UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC (void);
extern void UnsafeText_get_IsEmpty_m9B402264304B240CCBD459849F0F2115843C6541 (void);
extern void UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C (void);
extern void UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815 (void);
extern void UnsafeText_set_Capacity_m036A8112B10BF0E24DC47D863B0F3E1DBC34EF86 (void);
extern void UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40 (void);
extern void UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9 (void);
extern void UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2 (void);
extern void __JobReflectionRegistrationOutput__1652832624114795843_CreateJobReflectionData_m6F06A9348EFD949AABE2DB3703E98ECE169E5C2A (void);
extern void __JobReflectionRegistrationOutput__1652832624114795843_EarlyInit_m464D1467C681AD66A33B32BF5ECE7CF7EFD21C95 (void);
extern void U24BurstDirectCallInitializer_Initialize_mBB7299DE1F1DF732C60394307234ED45AE14AD82 (void);
static Il2CppMethodPointer s_methodPointers[505] = 
{
	EmbeddedAttribute__ctor_mB9EA4CCF3A3DC39A3BC92CFE9557FFAA77D15404,
	IsUnmanagedAttribute__ctor_m15974D59768AFF916E346F7107F7FF7F6AD9099C,
	EarlyInitHelpers__cctor_m19122BBB2CF7BC74606113B23BF74FA959A5D467,
	EarlyInitHelpers_FlushEarlyInits_m2B9C35967B87AF2BD2018C649BF964DFD5C40033,
	EarlyInitHelpers_JobReflectionDataCreationFailed_mD6AB08D5BB411CCE38A87793C3C7062EC91FD1EC,
	EarlyInitFunction__ctor_m74E4D36634E32C8FE21AA86C7D9597F7FD77E0B6,
	EarlyInitFunction_Invoke_mA2104BB505B5D3F16622D30BDA3AF83F3FE2FB2D,
	DOTSCompilerGeneratedAttribute__ctor_m8689CDD675567BC580F1FADCCF386B0FEE07B0E5,
	Spinner_Acquire_m9B13CD5A5170792255A188C07C7B96B65C3DB968,
	Spinner_Release_mEBA54EE373D18FE0473625B128784C92B65C6D2A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AllocatorManager_Free_mB8AE9C4CB989A9121F4E3F2E6C7781076DFB3025,
	NULL,
	AllocatorManager_CheckDelegate_m52D3F12472A2BBC5A28D2F4B5011B19D2E36AC61,
	AllocatorManager_UseDelegate_mEB18420309DAA2CC710BA123C6996C9FB6FC3798,
	AllocatorManager_allocate_block_mBEB6E6FDC334118DB679CF2619EBB3FF4FDD7FB5,
	AllocatorManager_forward_mono_allocate_block_mD2A9A49DFC8CBDC39F27E2749048ABC91E124519,
	AllocatorManager_LegacyOf_mAD212C1A7F5295C8987A6C9D7F81E8FF42E0A3BF,
	AllocatorManager_TryLegacy_mF4F0B8CE7B0293504FA12A6F9C4ACFF28B59FF79,
	AllocatorManager_Try_m24A6A4EF594F8909B5677C94C4788F365E02E7F9,
	AllocatorManager_IsCustomAllocator_m38BCD079BAB0D64962201CD05D671C2A42CE1909,
	AllocatorManager__cctor_m3E94344CB4CD852C9427FE9394EBE4EC36BFEEA1,
	AllocatorManager_InitializeU24StackAllocator_Try_000000ABU24BurstDirectCall_m62B68CEB2CEC113F6649A86053857969363D4439,
	AllocatorManager_InitializeU24SlabAllocator_Try_000000B9U24BurstDirectCall_m114F46E2185704C30FD2EBC834888CD4DD147EED,
	TryFunction__ctor_m10C4A7B32E87301727B84D8CBA081FABAE3CCE53,
	TryFunction_Invoke_mED723D46A7B0C4B590ABECE0868EA02AD94D07A2,
	AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63,
	AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1,
	AllocatorHandle_op_Implicit_mDCF4431F31BB4A09438AE644785C4273F86B2B8D,
	AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F,
	AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9,
	AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2,
	AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF,
	AllocatorHandle_get_IsAutoDispose_m605B841B976828E0219FFA8C9B15585F497C80E4,
	AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3,
	AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A,
	AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939,
	AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243,
	AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4,
	Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A,
	Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7,
	Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08,
	Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633,
	Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F,
	Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED,
	Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225,
	NULL,
	NULL,
	StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267,
	StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558,
	StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234,
	StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB,
	StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2,
	Try_000000ABU24PostfixBurstDelegate__ctor_m1FA005030A4C2F4D41A3A4B0DC4E389885D94589,
	Try_000000ABU24PostfixBurstDelegate_Invoke_m2603FA862A2883ED15CC6BAA38C0587AF3B5C18F,
	Try_000000ABU24BurstDirectCall_GetFunctionPointerDiscard_m437C4A40179A23A6C038F34C027477873F6E5234,
	Try_000000ABU24BurstDirectCall_GetFunctionPointer_m36EE8A97493668F5C9E13B62859DEA396C70D149,
	Try_000000ABU24BurstDirectCall_Constructor_m9A29EEBC364EEAFCA2A24D02607DF777CBB6A2DC,
	Try_000000ABU24BurstDirectCall_Initialize_m884465F80F4848B5961979B0B066E75F4E8F40FC,
	Try_000000ABU24BurstDirectCall__cctor_mA7788848AEBE4C739E58C844C44E53126CF65D08,
	Try_000000ABU24BurstDirectCall_Invoke_mB50DDBC5F5CAFBA77D4394F23361901DF05D110F,
	SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE,
	SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612,
	SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD,
	SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB,
	SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912,
	SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA,
	Try_000000B9U24PostfixBurstDelegate__ctor_m337A82A5B9945C843D8678A471B9C936859B74CE,
	Try_000000B9U24PostfixBurstDelegate_Invoke_m7CA5052C1AB537489DEAACEC0700B8FA84DDFF13,
	Try_000000B9U24BurstDirectCall_GetFunctionPointerDiscard_m113DF748656427E3A8295EF6C755C9B838DB7726,
	Try_000000B9U24BurstDirectCall_GetFunctionPointer_mE5972A3AFD6CF81A52CEF0CB913D99A511AB56B5,
	Try_000000B9U24BurstDirectCall_Constructor_m8018D3AC02AEFC7EBCCD2379BBF3D0525A0D774D,
	Try_000000B9U24BurstDirectCall_Initialize_mC6B7DE10ED47AB9B5D173AA0E60D181DA7F0E954,
	Try_000000B9U24BurstDirectCall__cctor_mB15CC0C076D7890FEF19A88CD1814E2131AE3188,
	Try_000000B9U24BurstDirectCall_Invoke_m9D2AFCB01D45314F4272BE199F77F848A3EF5675,
	NULL,
	NULL,
	TableEntry__cctor_mCA16889126B2ED5EF69666F8B0376FCC8834FCE1,
	IsAutoDispose__cctor_m433B37771502FF75597D5E68EE586209D5C9DF02,
	Managed__cctor_mE3BC99DF4AF7BC63DE01424848BDC790B53500BA,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AutoFreeAllocator_Update_m604EB4FA2A968881010E1DFA00567370D3833B79,
	AutoFreeAllocator_FreeAll_m88244CFEADFA303DB16F16BBAABF74937615E707,
	AutoFreeAllocator_Dispose_m465D490D2A99DCCF84EA24945014C82E7032FB25,
	AutoFreeAllocator_Try_mEE9F186E12A650296921481BCAACC4E0662C11CD,
	AutoFreeAllocator_Try_m4FFF073754714947DFC8F4D7134E74CDC78C019C,
	AutoFreeAllocator_get_Handle_m86CF40C2F9FB7A51F4F4F4B86834F4AFAC2475C3,
	AutoFreeAllocator_TryU24BurstManaged_mE86E6DC99C88B259454710D7CC1906F995968ECF,
	Try_000000E3U24PostfixBurstDelegate__ctor_m27B61F05DBF00EEEB47251AE625B11C76F63E31F,
	Try_000000E3U24PostfixBurstDelegate_Invoke_mD82F3F33E6C9297016D083430D1AF4270655C47B,
	Try_000000E3U24BurstDirectCall_GetFunctionPointerDiscard_mFFFE735110FDD2C2EB168A4B8FEA018FD2955963,
	Try_000000E3U24BurstDirectCall_GetFunctionPointer_m8882BF00E3841F12C4328BC70EE81DD70DF98477,
	Try_000000E3U24BurstDirectCall_Constructor_mA93FDF027D4C05D5B566E0B83D8E7CE3EBA93BD3,
	Try_000000E3U24BurstDirectCall_Initialize_mA6B96FE892305FE1B9B1AB7C8492F29BDDFD84E6,
	Try_000000E3U24BurstDirectCall__cctor_m12F864C33F1F28EE2BC01723BA8B420C7EDB67C0,
	Try_000000E3U24BurstDirectCall_Invoke_m34FD4768C4ABCDC403E19CBC5FEC9E901CED9480,
	Bitwise_IsSet_m67C151D3322C524985721C6773365295D00AFF21,
	CollectionHelper_Hash_mFB14DD4BA7288CEDF90E514A9397FB9C27E55293,
	CollectionHelper_ShouldDeallocate_m505E7EDBA71F02BAF52CC9DCD7C593CDA85D5465,
	CollectionHelper_AssumePositive_mD1EC1F05F50F605141D9BA5D70C4332AC902B4B1,
	NULL,
	DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085,
	Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520,
	Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	GenerateTestsForBurstCompatibilityAttribute_set_GenericTypeArguments_mF1072BD236646D4159082D3BB685B365EA45C01A,
	GenerateTestsForBurstCompatibilityAttribute__ctor_m86CEFB7F89EBCA8FECA15EC6F21CC9DFCDCDA235,
	ExcludeFromBurstCompatTestingAttribute_set_Reason_m54DAB86449D0D2B47E1521F71AE433D1EC2598E5,
	ExcludeFromBurstCompatTestingAttribute__ctor_mE85EC7FAEC0AF711D75010FE42AD803D9442806D,
	Unmanaged_Allocate_m7310B1FE896DEFFA18303D961C9859C8FF3D21E5,
	Unmanaged_Free_m09F6EA89F368ED2C9E5EC5EA60C894C4434F4FD1,
	NULL,
	Array_IsCustom_m7651BFF84F5AEFA592FEE86C834A85C373DDC126,
	Array_CustomResize_mB51497D583399092F23AA773ABB64F0780610D82,
	Array_Resize_mC7BE2965DE3FCF4014D43B606D94951480A65380,
	NULL,
	NULL,
	NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA,
	NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780,
	NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0,
	NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223,
	NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D,
	NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9,
	NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81,
	NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5,
	NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE,
	NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4,
	NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64,
	NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92,
	NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9,
	ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1,
	ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922,
	NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692,
	NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77,
	NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52,
	NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972,
	NULL,
	NULL,
	RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595,
	RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F,
	RewindableAllocator_TryAllocate_mBD9EC80EDB768E80D25E5F72176E85D88FAFA418,
	RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED,
	RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35,
	RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E,
	RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8,
	Union_get_m_current_mC92616C894B255E4DF5F3B0D95B5F147A0B1C57D,
	Union_set_m_current_m696C493651AB0C9F11496025FF50C67548A77A4F,
	Union_get_m_allocCount_m3FF45309DF2EF51A2F6BF02748D9787C2C293AB2,
	Union_set_m_allocCount_m8053FC4C9A57DDF9A1AD199B7BDD7F0F16FE3988,
	MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F,
	MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069,
	MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F,
	MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC,
	Try_000009DEU24PostfixBurstDelegate__ctor_mD7D5D9437C5C5F8DCBAB81A1AD7CAD0ABD5D9D57,
	Try_000009DEU24PostfixBurstDelegate_Invoke_mEE1AEF3715752949B54CDFA75F1A443FA3D892CB,
	Try_000009DEU24BurstDirectCall_GetFunctionPointerDiscard_mB9D526369D305BE3D3CB241A84BF5FF888164E05,
	Try_000009DEU24BurstDirectCall_GetFunctionPointer_mE5AA8F3011F9D029B128029946E4038F2F7C1758,
	Try_000009DEU24BurstDirectCall_Constructor_m8FE78DD7C9EB612C1F16E3FF36F13B71BC038674,
	Try_000009DEU24BurstDirectCall_Initialize_m09D93B636BACE72BF4DD546222EAD1C0CAC77D1D,
	Try_000009DEU24BurstDirectCall__cctor_m3E2D9B8CE8BC183FF29B7A3D0051683F2E13B830,
	Try_000009DEU24BurstDirectCall_Invoke_mB9BEE5675F857409B3B1B6246FC7A2E37A60AC83,
	Unicode_IsValidCodePoint_m6F8516FA18D35B6D052FD6BDE01D9D497EA06B9D,
	Unicode_NotTrailer_m9FD6FC331044FE0EFC90E29A2808C4A6535E4CAD,
	Unicode_get_ReplacementCharacter_m525CDE0E6CAB489454025711F93FF832A600556A,
	Unicode_Utf8ToUcs_m013E3A507C4B6F5459B09C6EA8EA229BDC979827,
	Unicode_UcsToUtf16_m14C1098270C0DFFAF6B48D47C3214344FD4FAE0E,
	Unicode_Utf8ToUtf16_mF3051E9181A57301EEF945C10B97D3C9356706DD,
	Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7,
	Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729,
	UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816,
	UnsafeQueueData_DeallocateQueue_mF4E2184511650FED8D9B85ECDB14B6E43AB06373,
	NULL,
	NULL,
	NULL,
	UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502,
	UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E,
	xxHash3_Avx2HashLongInternalLoop_mCAEEE715FCB699CA2F1B947BCD252AA0F87D2B15,
	xxHash3_Avx2ScrambleAcc_m64D8B68219EA3E164A61D2001E0969263CF098CE,
	xxHash3_Avx2Accumulate_mD57A48AB8FB3471A923F64F7C8B52FF8538E791D,
	xxHash3_Avx2Accumulate512_mBB4B8AAAA2DC7E6B1350597687C11B82E81CEF06,
	xxHash3_Hash64Long_m9950702E864DCCD9B8DEAAE23E7CBB5E79D4AC62,
	xxHash3_Hash128Long_mED9958D31B54E0E0666AAD34A52DE7CDEB802E6F,
	xxHash3_ToUint4_m811AB95294FBBC0F17A5358D0A22669691CE3633,
	xxHash3_Read64LE_mD275A5EFD8727CDE8B8E280D4A5D5B82D5E3B195,
	xxHash3_Write64LE_m79CC2011BF16363F2338D61BE43E99E6467A9437,
	xxHash3_Mul32To64_m9210E9379305FC38A6D69C698F6E1A30013BC4F5,
	xxHash3_XorShift64_mF4245CDE1C4AF6B1CC8F57AAE0DA8C7E04673CFC,
	xxHash3_Mul128Fold64_mF59DCB5142027D151F52C7748BFA28C32B3B8F38,
	xxHash3_Avalanche_m059990B780566C6F04C66700B2BE7817B4FA2F18,
	xxHash3_Mix2Acc_mDEB8D0C149D943295B8A3049A437578BE879BED8,
	xxHash3_MergeAcc_mB01ADB1934EDFE8FE3B2AAB13DA6884EB1133A14,
	xxHash3_DefaultHashLongInternalLoop_m9181A3A8DBE8DBEFF1B730ECC9A9AA5E93110F1B,
	xxHash3_DefaultAccumulate_m3D28C5486CC42D31D2D832F40DEFE1A7CF508CA5,
	xxHash3_DefaultAccumulate512_mFADF15092DA5379116D3FCCFC4238ADBF48D85D7,
	xxHash3_DefaultScrambleAcc_mA46D6E8E1BA4613A50B56C8536B0DA3F50437137,
	xxHash3_Hash64LongU24BurstManaged_m71E36BBD116CCA46ED23162F80B08D3B2F782B4D,
	xxHash3_Hash128LongU24BurstManaged_m961A07284DAB6ADFF52EB4287E9D105AB971FDF6,
	Hash64Long_00000A73U24PostfixBurstDelegate__ctor_m6C164D2A8C65A7C3ADFF1734A543736FAE47C206,
	Hash64Long_00000A73U24PostfixBurstDelegate_Invoke_mD8B0912F807F954B2A1F6D05E52ECA05195EF29F,
	Hash64Long_00000A73U24BurstDirectCall_GetFunctionPointerDiscard_mB02B3782D721D3FD1E1BCC26C98397473D58A8B8,
	Hash64Long_00000A73U24BurstDirectCall_GetFunctionPointer_m8443DD2CA1016BB02C434C4FA74A32A883AD456A,
	Hash64Long_00000A73U24BurstDirectCall_Constructor_m3A444F37DC10EC79F29AD476E4963F17AE2E0809,
	Hash64Long_00000A73U24BurstDirectCall_Initialize_m6B9AF7B8C2553F712D21CBD389788FADAFA802A7,
	Hash64Long_00000A73U24BurstDirectCall__cctor_m7AB1C1979E6BA01973D6552A22B50E30F0D89CB0,
	Hash64Long_00000A73U24BurstDirectCall_Invoke_m907F360339DBE2BC742D85D814D46156865DD5B0,
	Hash128Long_00000A7AU24PostfixBurstDelegate__ctor_mE7FF5D807EB74D77814701BA2CA6CBDE0B3C17D3,
	Hash128Long_00000A7AU24PostfixBurstDelegate_Invoke_m4187EE5B0816EBBC2516E038078CC0187AE19A2B,
	Hash128Long_00000A7AU24BurstDirectCall_GetFunctionPointerDiscard_m64BBA60CA3638F64354F7588BFC5E707E906EEB8,
	Hash128Long_00000A7AU24BurstDirectCall_GetFunctionPointer_m93A9EDD89E1114121B1BE2E9E7E319DD996981A7,
	Hash128Long_00000A7AU24BurstDirectCall_Constructor_mBDFB62FF66EB710A87EE1643E3C54FBDAF5B7005,
	Hash128Long_00000A7AU24BurstDirectCall_Initialize_m0700B71CBB558E5B4B8D0856861B65AE1094DE7E,
	Hash128Long_00000A7AU24BurstDirectCall__cctor_m09DDA1C5419D4D17E8719EAB46EB06E4E43E75F4,
	Hash128Long_00000A7AU24BurstDirectCall_Invoke_m66696F33E60B8FB667A3436D08B467008EA17778,
	UnsafeBitArray_Free_m593DC0AC7ADB85F86BF8E27CDCA5EF1395650D0D,
	UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1,
	UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2,
	UnsafeBitArray_IsSet_mC96E92F48DCF8062E347E4B9E43886B27235DDE9,
	UnsafeBitArrayDebugView__ctor_mDCEA05186DBDBB94172636C9E6FC3D2F93FBDCD7,
	UnsafeBitArrayDebugView_get_Bits_m257AD507B6FA78BD2A8817BDF8DBBD2A23A9A834,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnsafeParallelHashMapData_DeallocateHashMap_m8D0FEE08B8522A1D05FBFFBBB43CB203304F114F,
	UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3,
	UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC,
	UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464,
	UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065,
	UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F,
	UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C,
	UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C,
	DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E,
	ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB,
	ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50,
	UnsafeTextExtensions_AsUnsafeListOfBytes_mA80ABFE08762E38D788ACF506BEB4A0E3621D439,
	UnsafeTextExtensions_AsUnsafeListOfBytesRO_mB4E57457A3442858265E8A664CCBBC8E0A4C6AF4,
	UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1,
	UnsafeText_Free_m22C162680EFB31663020E8FE94BE67596994A98B,
	UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC,
	UnsafeText_get_IsEmpty_m9B402264304B240CCBD459849F0F2115843C6541,
	UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C,
	UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815,
	UnsafeText_set_Capacity_m036A8112B10BF0E24DC47D863B0F3E1DBC34EF86,
	UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40,
	UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9,
	UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2,
	__JobReflectionRegistrationOutput__1652832624114795843_CreateJobReflectionData_m6F06A9348EFD949AABE2DB3703E98ECE169E5C2A,
	__JobReflectionRegistrationOutput__1652832624114795843_EarlyInit_m464D1467C681AD66A33B32BF5ECE7CF7EFD21C95,
	U24BurstDirectCallInitializer_Initialize_mBB7299DE1F1DF732C60394307234ED45AE14AD82,
};
extern void Spinner_Acquire_m9B13CD5A5170792255A188C07C7B96B65C3DB968_AdjustorThunk (void);
extern void Spinner_Release_mEBA54EE373D18FE0473625B128784C92B65C6D2A_AdjustorThunk (void);
extern void AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63_AdjustorThunk (void);
extern void AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1_AdjustorThunk (void);
extern void AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F_AdjustorThunk (void);
extern void AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9_AdjustorThunk (void);
extern void AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2_AdjustorThunk (void);
extern void AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF_AdjustorThunk (void);
extern void AllocatorHandle_get_IsAutoDispose_m605B841B976828E0219FFA8C9B15585F497C80E4_AdjustorThunk (void);
extern void AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3_AdjustorThunk (void);
extern void AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A_AdjustorThunk (void);
extern void AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939_AdjustorThunk (void);
extern void AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243_AdjustorThunk (void);
extern void AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4_AdjustorThunk (void);
extern void Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A_AdjustorThunk (void);
extern void Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7_AdjustorThunk (void);
extern void Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08_AdjustorThunk (void);
extern void Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633_AdjustorThunk (void);
extern void Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F_AdjustorThunk (void);
extern void Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED_AdjustorThunk (void);
extern void Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225_AdjustorThunk (void);
extern void StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267_AdjustorThunk (void);
extern void StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558_AdjustorThunk (void);
extern void StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB_AdjustorThunk (void);
extern void SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE_AdjustorThunk (void);
extern void SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612_AdjustorThunk (void);
extern void SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD_AdjustorThunk (void);
extern void SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912_AdjustorThunk (void);
extern void AutoFreeAllocator_Update_m604EB4FA2A968881010E1DFA00567370D3833B79_AdjustorThunk (void);
extern void AutoFreeAllocator_FreeAll_m88244CFEADFA303DB16F16BBAABF74937615E707_AdjustorThunk (void);
extern void AutoFreeAllocator_Dispose_m465D490D2A99DCCF84EA24945014C82E7032FB25_AdjustorThunk (void);
extern void AutoFreeAllocator_Try_mEE9F186E12A650296921481BCAACC4E0662C11CD_AdjustorThunk (void);
extern void AutoFreeAllocator_get_Handle_m86CF40C2F9FB7A51F4F4F4B86834F4AFAC2475C3_AdjustorThunk (void);
extern void DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085_AdjustorThunk (void);
extern void Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520_AdjustorThunk (void);
extern void Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185_AdjustorThunk (void);
extern void NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA_AdjustorThunk (void);
extern void NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780_AdjustorThunk (void);
extern void NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0_AdjustorThunk (void);
extern void NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792_AdjustorThunk (void);
extern void NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223_AdjustorThunk (void);
extern void NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E_AdjustorThunk (void);
extern void NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D_AdjustorThunk (void);
extern void NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9_AdjustorThunk (void);
extern void NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81_AdjustorThunk (void);
extern void NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5_AdjustorThunk (void);
extern void NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE_AdjustorThunk (void);
extern void NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4_AdjustorThunk (void);
extern void NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64_AdjustorThunk (void);
extern void NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92_AdjustorThunk (void);
extern void NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9_AdjustorThunk (void);
extern void ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1_AdjustorThunk (void);
extern void ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922_AdjustorThunk (void);
extern void NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692_AdjustorThunk (void);
extern void NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77_AdjustorThunk (void);
extern void NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52_AdjustorThunk (void);
extern void NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972_AdjustorThunk (void);
extern void RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595_AdjustorThunk (void);
extern void RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F_AdjustorThunk (void);
extern void RewindableAllocator_TryAllocate_mBD9EC80EDB768E80D25E5F72176E85D88FAFA418_AdjustorThunk (void);
extern void RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED_AdjustorThunk (void);
extern void RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E_AdjustorThunk (void);
extern void Union_get_m_current_mC92616C894B255E4DF5F3B0D95B5F147A0B1C57D_AdjustorThunk (void);
extern void Union_set_m_current_m696C493651AB0C9F11496025FF50C67548A77A4F_AdjustorThunk (void);
extern void Union_get_m_allocCount_m3FF45309DF2EF51A2F6BF02748D9787C2C293AB2_AdjustorThunk (void);
extern void Union_set_m_allocCount_m8053FC4C9A57DDF9A1AD199B7BDD7F0F16FE3988_AdjustorThunk (void);
extern void MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F_AdjustorThunk (void);
extern void MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069_AdjustorThunk (void);
extern void MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F_AdjustorThunk (void);
extern void MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC_AdjustorThunk (void);
extern void Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7_AdjustorThunk (void);
extern void Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729_AdjustorThunk (void);
extern void UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816_AdjustorThunk (void);
extern void UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502_AdjustorThunk (void);
extern void UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E_AdjustorThunk (void);
extern void UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1_AdjustorThunk (void);
extern void UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2_AdjustorThunk (void);
extern void UnsafeBitArray_IsSet_mC96E92F48DCF8062E347E4B9E43886B27235DDE9_AdjustorThunk (void);
extern void UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC_AdjustorThunk (void);
extern void UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3_AdjustorThunk (void);
extern void UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC_AdjustorThunk (void);
extern void UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1_AdjustorThunk (void);
extern void UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464_AdjustorThunk (void);
extern void UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065_AdjustorThunk (void);
extern void UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F_AdjustorThunk (void);
extern void UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C_AdjustorThunk (void);
extern void UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C_AdjustorThunk (void);
extern void DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E_AdjustorThunk (void);
extern void ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB_AdjustorThunk (void);
extern void ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50_AdjustorThunk (void);
extern void UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1_AdjustorThunk (void);
extern void UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC_AdjustorThunk (void);
extern void UnsafeText_get_IsEmpty_m9B402264304B240CCBD459849F0F2115843C6541_AdjustorThunk (void);
extern void UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C_AdjustorThunk (void);
extern void UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815_AdjustorThunk (void);
extern void UnsafeText_set_Capacity_m036A8112B10BF0E24DC47D863B0F3E1DBC34EF86_AdjustorThunk (void);
extern void UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40_AdjustorThunk (void);
extern void UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9_AdjustorThunk (void);
extern void UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[99] = 
{
	{ 0x06000009, Spinner_Acquire_m9B13CD5A5170792255A188C07C7B96B65C3DB968_AdjustorThunk },
	{ 0x0600000A, Spinner_Release_mEBA54EE373D18FE0473625B128784C92B65C6D2A_AdjustorThunk },
	{ 0x06000021, AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63_AdjustorThunk },
	{ 0x06000022, AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1_AdjustorThunk },
	{ 0x06000024, AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F_AdjustorThunk },
	{ 0x06000025, AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9_AdjustorThunk },
	{ 0x06000026, AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2_AdjustorThunk },
	{ 0x06000027, AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF_AdjustorThunk },
	{ 0x06000028, AllocatorHandle_get_IsAutoDispose_m605B841B976828E0219FFA8C9B15585F497C80E4_AdjustorThunk },
	{ 0x06000029, AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3_AdjustorThunk },
	{ 0x0600002A, AllocatorHandle_Equals_mF2E151E2C3E8881305EA7EA9A46EA58D7CE44C9A_AdjustorThunk },
	{ 0x0600002B, AllocatorHandle_Equals_m2A603E4EBB8FDB0937A4801F9CB161E0D8458939_AdjustorThunk },
	{ 0x0600002C, AllocatorHandle_GetHashCode_mE5C215E6AEDF384713522C73D29E4D6E82E4E243_AdjustorThunk },
	{ 0x0600002D, AllocatorHandle_CompareTo_mBC5073E933F44F999E6D112CE04E9EE79E2AF6A4_AdjustorThunk },
	{ 0x0600002E, Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A_AdjustorThunk },
	{ 0x0600002F, Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7_AdjustorThunk },
	{ 0x06000030, Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08_AdjustorThunk },
	{ 0x06000031, Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633_AdjustorThunk },
	{ 0x06000032, Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F_AdjustorThunk },
	{ 0x06000033, Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED_AdjustorThunk },
	{ 0x06000034, Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225_AdjustorThunk },
	{ 0x06000037, StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267_AdjustorThunk },
	{ 0x06000038, StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558_AdjustorThunk },
	{ 0x0600003A, StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB_AdjustorThunk },
	{ 0x06000044, SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE_AdjustorThunk },
	{ 0x06000045, SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612_AdjustorThunk },
	{ 0x06000046, SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD_AdjustorThunk },
	{ 0x06000048, SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912_AdjustorThunk },
	{ 0x06000063, AutoFreeAllocator_Update_m604EB4FA2A968881010E1DFA00567370D3833B79_AdjustorThunk },
	{ 0x06000064, AutoFreeAllocator_FreeAll_m88244CFEADFA303DB16F16BBAABF74937615E707_AdjustorThunk },
	{ 0x06000065, AutoFreeAllocator_Dispose_m465D490D2A99DCCF84EA24945014C82E7032FB25_AdjustorThunk },
	{ 0x06000066, AutoFreeAllocator_Try_mEE9F186E12A650296921481BCAACC4E0662C11CD_AdjustorThunk },
	{ 0x06000068, AutoFreeAllocator_get_Handle_m86CF40C2F9FB7A51F4F4F4B86834F4AFAC2475C3_AdjustorThunk },
	{ 0x06000077, DummyJob_Execute_mFD529845FB6001C124D6E93F4CB30AC87842D085_AdjustorThunk },
	{ 0x06000078, Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520_AdjustorThunk },
	{ 0x06000079, Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185_AdjustorThunk },
	{ 0x060000FB, NativeBitArrayDispose_Dispose_m1B62640BDF4CD7C212D576F9E96E7C09591BA6FA_AdjustorThunk },
	{ 0x060000FC, NativeBitArrayDisposeJob_Execute_mFB5CEA927325091B306954255DAF283AFDC34780_AdjustorThunk },
	{ 0x060000FD, NativeHashMapDispose_Dispose_mC3C0B4D350083ECD98FB33653ECA5B9E64ECC8A0_AdjustorThunk },
	{ 0x060000FE, NativeHashMapDisposeJob_Execute_m1A16F21E2C5C17AECCBA47BED1155479D219E792_AdjustorThunk },
	{ 0x06000139, NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223_AdjustorThunk },
	{ 0x0600013A, NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E_AdjustorThunk },
	{ 0x06000140, NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D_AdjustorThunk },
	{ 0x06000141, NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9_AdjustorThunk },
	{ 0x06000142, NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81_AdjustorThunk },
	{ 0x06000143, NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5_AdjustorThunk },
	{ 0x06000144, NativeRingQueueDispose_Dispose_mAFC3E885E53F715512815E9D5BAEC6F54B4419EE_AdjustorThunk },
	{ 0x06000145, NativeRingQueueDisposeJob_Execute_m917869AD688F9A0B3F0B7F0CE60BC42136DC4BD4_AdjustorThunk },
	{ 0x06000146, NativeStream_get_IsCreated_m139F58FDEA696FB948C962A2BBC0F13B583B2F64_AdjustorThunk },
	{ 0x06000147, NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92_AdjustorThunk },
	{ 0x06000148, NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9_AdjustorThunk },
	{ 0x06000149, ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1_AdjustorThunk },
	{ 0x0600014A, ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922_AdjustorThunk },
	{ 0x0600014B, NativeStreamDispose_Dispose_m2896546615BEF6A590E2D60ACA6FD27BFD224692_AdjustorThunk },
	{ 0x0600014C, NativeStreamDisposeJob_Execute_m4DFC0A231FE4B2DD7F1E4C16A2C2BD5EA2103A77_AdjustorThunk },
	{ 0x0600014D, NativeTextDispose_Dispose_m78F766D5AE3C4A7734C025650944A8BF1F472D52_AdjustorThunk },
	{ 0x0600014E, NativeTextDisposeJob_Execute_mCA918E48AB23694F215614FAFF4511B2C9F57972_AdjustorThunk },
	{ 0x06000151, RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595_AdjustorThunk },
	{ 0x06000152, RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F_AdjustorThunk },
	{ 0x06000153, RewindableAllocator_TryAllocate_mBD9EC80EDB768E80D25E5F72176E85D88FAFA418_AdjustorThunk },
	{ 0x06000154, RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED_AdjustorThunk },
	{ 0x06000156, RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E_AdjustorThunk },
	{ 0x06000158, Union_get_m_current_mC92616C894B255E4DF5F3B0D95B5F147A0B1C57D_AdjustorThunk },
	{ 0x06000159, Union_set_m_current_m696C493651AB0C9F11496025FF50C67548A77A4F_AdjustorThunk },
	{ 0x0600015A, Union_get_m_allocCount_m3FF45309DF2EF51A2F6BF02748D9787C2C293AB2_AdjustorThunk },
	{ 0x0600015B, Union_set_m_allocCount_m8053FC4C9A57DDF9A1AD199B7BDD7F0F16FE3988_AdjustorThunk },
	{ 0x0600015C, MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F_AdjustorThunk },
	{ 0x0600015D, MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069_AdjustorThunk },
	{ 0x0600015E, MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F_AdjustorThunk },
	{ 0x0600015F, MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC_AdjustorThunk },
	{ 0x0600016E, Rune_Equals_mE3D6609368AB40A856C04A58E887B4123B0066E7_AdjustorThunk },
	{ 0x0600016F, Rune_GetHashCode_m68368D05A3983D96C4402F27AB0B760B17F55729_AdjustorThunk },
	{ 0x06000170, UnsafeQueueBlockPoolData_FreeBlock_mA0771AE409B2B98C9B69208A7612732536BE2816_AdjustorThunk },
	{ 0x06000175, UnsafeQueueDispose_Dispose_mC88928459218E24F22077B5D77A92A48B361A502_AdjustorThunk },
	{ 0x06000176, UnsafeQueueDisposeJob_Execute_m539787ED63E9D1A60DC2E7C76FE7102473D2808E_AdjustorThunk },
	{ 0x0600019D, UnsafeBitArray_get_IsCreated_m2C813128AFAA24077BA71E3B1C6A9EB6918F1DE1_AdjustorThunk },
	{ 0x0600019E, UnsafeBitArray_Dispose_m95D39C0718064B87A854A3142329041E8244FCD2_AdjustorThunk },
	{ 0x0600019F, UnsafeBitArray_IsSet_mC96E92F48DCF8062E347E4B9E43886B27235DDE9_AdjustorThunk },
	{ 0x060001AF, UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC_AdjustorThunk },
	{ 0x060001D8, UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3_AdjustorThunk },
	{ 0x060001D9, UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC_AdjustorThunk },
	{ 0x060001DA, UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1_AdjustorThunk },
	{ 0x060001E3, UnsafeStreamBlockData_Free_m91AAAF7EB91E93E8806ACB6E87436DB1757D5464_AdjustorThunk },
	{ 0x060001E4, UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065_AdjustorThunk },
	{ 0x060001E5, UnsafeStream_get_IsCreated_m070EB9BC1C5FA09ABFC3B3E8C44F33418A927E4F_AdjustorThunk },
	{ 0x060001E6, UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C_AdjustorThunk },
	{ 0x060001E7, UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C_AdjustorThunk },
	{ 0x060001E8, DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E_AdjustorThunk },
	{ 0x060001E9, ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB_AdjustorThunk },
	{ 0x060001EA, ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50_AdjustorThunk },
	{ 0x060001ED, UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1_AdjustorThunk },
	{ 0x060001EF, UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC_AdjustorThunk },
	{ 0x060001F0, UnsafeText_get_IsEmpty_m9B402264304B240CCBD459849F0F2115843C6541_AdjustorThunk },
	{ 0x060001F1, UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C_AdjustorThunk },
	{ 0x060001F2, UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815_AdjustorThunk },
	{ 0x060001F3, UnsafeText_set_Capacity_m036A8112B10BF0E24DC47D863B0F3E1DBC34EF86_AdjustorThunk },
	{ 0x060001F4, UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40_AdjustorThunk },
	{ 0x060001F5, UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9_AdjustorThunk },
	{ 0x060001F6, UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2_AdjustorThunk },
};
static const int32_t s_InvokerIndices[505] = 
{
	4364,
	4364,
	9089,
	9089,
	8887,
	2798,
	4364,
	4364,
	4364,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8043,
	0,
	8867,
	8993,
	8380,
	7894,
	8412,
	8380,
	8380,
	8249,
	9089,
	9089,
	9089,
	2798,
	2429,
	4151,
	4364,
	8940,
	4216,
	3397,
	4370,
	4216,
	4168,
	4364,
	3185,
	3297,
	4216,
	3456,
	4364,
	4217,
	4217,
	4216,
	3852,
	4364,
	4216,
	0,
	0,
	4370,
	3397,
	7478,
	4364,
	7478,
	2798,
	2429,
	8867,
	9020,
	9089,
	9089,
	9089,
	7478,
	4370,
	4216,
	3397,
	7478,
	4364,
	7478,
	2798,
	2429,
	8867,
	9020,
	9089,
	9089,
	9089,
	7478,
	0,
	0,
	9089,
	9089,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	4364,
	4364,
	3397,
	7478,
	4370,
	7478,
	2798,
	2429,
	8867,
	9020,
	9089,
	9089,
	9089,
	7478,
	7144,
	7807,
	8249,
	8395,
	0,
	4364,
	4216,
	3027,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3881,
	4364,
	3881,
	3881,
	6266,
	7909,
	0,
	8249,
	4877,
	4877,
	0,
	0,
	4364,
	4364,
	4364,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	4364,
	0,
	0,
	0,
	0,
	0,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4168,
	4364,
	3852,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	0,
	0,
	4364,
	4364,
	718,
	3397,
	7478,
	4370,
	7478,
	4217,
	3853,
	4217,
	3853,
	3853,
	4364,
	4364,
	3168,
	2798,
	2429,
	8867,
	9020,
	9089,
	9089,
	9089,
	7478,
	8216,
	8205,
	9103,
	5775,
	5779,
	5248,
	3185,
	4216,
	3788,
	6855,
	0,
	0,
	0,
	4364,
	4364,
	5065,
	7894,
	5061,
	6066,
	6047,
	5478,
	8069,
	8799,
	7905,
	7827,
	7828,
	7829,
	8813,
	6817,
	6815,
	5065,
	5061,
	5458,
	7894,
	6047,
	5478,
	2798,
	1265,
	8867,
	9020,
	9089,
	9089,
	9089,
	6047,
	2798,
	830,
	8867,
	9020,
	9089,
	9089,
	9089,
	5478,
	7909,
	4168,
	4364,
	3166,
	3976,
	4250,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7909,
	4364,
	4364,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3788,
	3852,
	4168,
	4364,
	4364,
	4364,
	4364,
	4364,
	8157,
	8141,
	4168,
	8867,
	4364,
	4168,
	4151,
	4216,
	3852,
	4216,
	3852,
	4250,
	9089,
	9089,
	9089,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[8] = 
{
	{ 0x06000039, 26,  (void**)&StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234_RuntimeMethod_var, 0 },
	{ 0x0600003B, 27,  (void**)&StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2_RuntimeMethod_var, 0 },
	{ 0x06000047, 24,  (void**)&SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB_RuntimeMethod_var, 0 },
	{ 0x06000049, 25,  (void**)&SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA_RuntimeMethod_var, 0 },
	{ 0x06000067, 0,  (void**)&AutoFreeAllocator_Try_m4FFF073754714947DFC8F4D7134E74CDC78C019C_RuntimeMethod_var, 0 },
	{ 0x06000069, 1,  (void**)&AutoFreeAllocator_TryU24BurstManaged_mE86E6DC99C88B259454710D7CC1906F995968ECF_RuntimeMethod_var, 0 },
	{ 0x06000155, 17,  (void**)&RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35_RuntimeMethod_var, 0 },
	{ 0x06000157, 18,  (void**)&RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[51] = 
{
	{ 0x02000018, { 29, 7 } },
	{ 0x0200001D, { 36, 13 } },
	{ 0x02000028, { 54, 3 } },
	{ 0x0200002A, { 58, 41 } },
	{ 0x0200002B, { 99, 5 } },
	{ 0x0200002C, { 104, 41 } },
	{ 0x0200002D, { 145, 5 } },
	{ 0x0200002E, { 150, 41 } },
	{ 0x0200002F, { 191, 5 } },
	{ 0x02000030, { 196, 41 } },
	{ 0x02000031, { 237, 5 } },
	{ 0x02000032, { 242, 43 } },
	{ 0x02000033, { 285, 5 } },
	{ 0x02000047, { 304, 8 } },
	{ 0x0200004A, { 312, 58 } },
	{ 0x0200004B, { 376, 2 } },
	{ 0x0200004E, { 378, 12 } },
	{ 0x0200004F, { 390, 10 } },
	{ 0x0200005D, { 400, 5 } },
	{ 0x02000069, { 405, 6 } },
	{ 0x02000073, { 411, 11 } },
	{ 0x02000074, { 431, 8 } },
	{ 0x02000075, { 439, 1 } },
	{ 0x02000076, { 440, 23 } },
	{ 0x02000079, { 463, 31 } },
	{ 0x0200007A, { 508, 8 } },
	{ 0x0200007F, { 516, 8 } },
	{ 0x02000080, { 524, 8 } },
	{ 0x0600000B, { 0, 4 } },
	{ 0x0600000C, { 4, 2 } },
	{ 0x0600000D, { 6, 6 } },
	{ 0x0600000E, { 12, 5 } },
	{ 0x0600000F, { 17, 3 } },
	{ 0x06000010, { 20, 2 } },
	{ 0x06000011, { 22, 5 } },
	{ 0x06000013, { 27, 2 } },
	{ 0x06000076, { 49, 5 } },
	{ 0x0600007C, { 57, 1 } },
	{ 0x060000ED, { 290, 4 } },
	{ 0x060000F5, { 294, 2 } },
	{ 0x060000F9, { 296, 3 } },
	{ 0x060000FA, { 299, 5 } },
	{ 0x06000104, { 370, 2 } },
	{ 0x06000105, { 372, 2 } },
	{ 0x0600011E, { 374, 2 } },
	{ 0x060001A5, { 422, 9 } },
	{ 0x060001B8, { 494, 4 } },
	{ 0x060001B9, { 498, 3 } },
	{ 0x060001BD, { 501, 2 } },
	{ 0x060001C1, { 503, 3 } },
	{ 0x060001C3, { 506, 2 } },
};
extern const uint32_t g_rgctx_TU26_t5335F97D02534CC2D1EFAD2B1B8CE794A459864F;
extern const uint32_t g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2;
extern const uint32_t g_rgctx_TU26_t5CEA5E60B9EC484E5F0A5233B0FE6573F8FD4204;
extern const uint32_t g_rgctx_AllocatorManager_AllocateBlock_TisT_t4B6526BAD6B8C750196E96687551E6FB248D8B93_m20F711BEC9AE42C4FA04E61E37EDAE5F8E13C2A2;
extern const uint32_t g_rgctx_TU26_tF4435F8B669166A749CCFA3D5E9E46AAD064E2CE;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m147F0BD45B75434C545EA473E1C858F26B10C11C;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m21F060306B883FAA6844D2D92424AD0C418E7FB8;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisT_t53C98BE980141A98061F7E0C00F1E53863D49172_mCE845A9B9E6485B0D134D2578EAAB89D9FB73D29;
extern const uint32_t g_rgctx_UU2A_t486C2DB331AC02939CDB4420494EF6926230852B;
extern const uint32_t g_rgctx_U_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB;
extern const uint32_t g_rgctx_TU26_t9B5D20DBF15F05E31EF8CD986A51EE4170B1087D;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisU_tD3D99706DA190E86407D1D82879F6F709EB4C868_m2AC5DE4526CE78C7DF28F5E331592A1D35522F32;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisU_tD3D99706DA190E86407D1D82879F6F709EB4C868_m71C14B1576AD1BAF27A35D6414FAB6B4FCF35856;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisT_t3DD7401FA21FE0512E56A1C674CD70F032268D44_m47B4DF89F52B6CE8FB4FD11EC4E4A82004076746;
extern const uint32_t g_rgctx_U_tD3D99706DA190E86407D1D82879F6F709EB4C868;
extern const uint32_t g_rgctx_TU26_t3F3F4F77ACB3DE60F9CEA6D14439ACADFACF96A9;
extern const uint32_t g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2;
extern const uint32_t g_rgctx_TU26_t7F0CFCEFF9DD94FC01C07F1F29EF14CBE94EFA6C;
extern const uint32_t g_rgctx_AllocatorManager_FreeBlock_TisT_t35BD07ABBCB8D61BAD11D72A4D6D6D997BD815DA_mCFAB24856A7C4B600583475999609C1BDE147617;
extern const uint32_t g_rgctx_TU26_t512535147A2E70989C9FED965A59897CB227A3CB;
extern const uint32_t g_rgctx_UU2A_tFEE847BDBB617FAF279654649190AF314B52F7B1;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m08C7637594479E2DE074EBCB3AB56DE38E47F0EA;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m54C6FF17733951B3182314D7A7392CAF02AE8CBE;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisT_tB5F0204FCE510FB4611F370EFC46DA8C45DC09AF_m866B5AC4270563CCF787270E884E9ADB696947CF;
extern const uint32_t g_rgctx_TU2A_t329EAE82F86B22F9B6C69972AF45D5E40392CCEA;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t570532536E3FD3B2205FD25800E5A7DAFDA40675_mC0583857F21D37F314ADCD109E5E9DD8244E4792;
extern const uint32_t g_rgctx_Array32768_1_t27311415036D8E10790953E71D1B37720B017554;
extern const uint32_t g_rgctx_Array4096_1_t44B5406E19508C8BD82A7FB15B4FAB261D6376F7;
extern const uint32_t g_rgctx_T_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisT_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34_m224DE97901461C7EFDC5FE9F0AC057A7815ACC92;
extern const uint32_t g_rgctx_TU26_t17FDE0F88AA456BDCCA436101E8DF16EEE82EC17;
extern const uint32_t g_rgctx_Array4096_1U2A_tF0B9FD8E1D868F59B252AAF699513C8FAB631575;
extern const uint32_t g_rgctx_Array4096_1U26_t538F10C46BA0E53BCE3887D97DD2B3549A3250DC;
extern const uint32_t g_rgctx_ArrayOfArrays_1_t1A891279D44A27D7201971E48FF0595AE6C329FD;
extern const uint32_t g_rgctx_ArrayOfArrays_1_get_BlockSizeInElements_m3B6658871D4634BA2F2D8E48DEF6DF0C4A7C6513;
extern const uint32_t g_rgctx_ArrayOfArrays_1_t1A891279D44A27D7201971E48FF0595AE6C329FD;
extern const uint32_t g_rgctx_T_t3DC68D76EEFD5956B4495D3A5090407667A3B949;
extern const uint32_t g_rgctx_ArrayOfArrays_1_BlockIndexOfElement_mD96537023159C87DDE47A0EBA926C1B158750BA7;
extern const uint32_t g_rgctx_ArrayOfArrays_1_get_BlockSizeInBytes_m21FD39C48ACC0DF7CE10952DC192942204F6AEE3;
extern const uint32_t g_rgctx_ArrayOfArrays_1_get_Item_m3D3013143B197F211B8AE6D9C24C67A0C3CF27B0;
extern const uint32_t g_rgctx_TU26_tF5DCA4F097698BC7F46F3BDEE454D6CE33AC0FF2;
extern const uint32_t g_rgctx_ArrayOfArrays_1_get_BlockMask_m53FAE8F54503ABD3131449934C38F20BDEF3FBE6;
extern const uint32_t g_rgctx_TU2A_t9CC6943E1E01E1FAD3FE839835FCD9DBEB2CCB44;
extern const uint32_t g_rgctx_ArrayOfArrays_1_Rewind_mE344EAD9FCDABCF5E34CC3ECC4DBBD0934E23164;
extern const uint32_t g_rgctx_ArrayOfArrays_1_Clear_mD997F31D3C5B390B5F5E34B31E6C5E66E155574C;
extern const uint32_t g_rgctx_ArrayOfArrays_1_get_Length_mA78EE6A17459AFA9101E194E34EA5596B15129B1;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m4ECD2C0F498BB96F26D22D2211DA124311E6913C;
extern const uint32_t g_rgctx_NativeArray_1_tB4AC81566A315CB43ABF22CDBBF5756B1EF56EE9;
extern const uint32_t g_rgctx_NativeArray_1_tB4AC81566A315CB43ABF22CDBBF5756B1EF56EE9;
extern const uint32_t g_rgctx_NativeArrayExtensions_Initialize_TisT_t4F1456D1F0D78CBDAD04233A68B351AFD370B8D6_m2A33B8DA1839BC8856106CBC5C6359DE80E72FAB;
extern const uint32_t g_rgctx_NativeArray_1U26_t7C2AAC3610216CE0D5112505492D477B7AB8781C;
extern const uint32_t g_rgctx_Key_tE14246D43A66068B6CBEBFCFE05D56F1A63F6AAF;
extern const uint32_t g_rgctx_Pair_2_t1A30834948233CCB312886C3BC501F2DC2625556;
extern const uint32_t g_rgctx_Value_tA30DC3FF0EF833B7B36ED2001F58CF9FF1C23056;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tA5912E8646DA88E85E66D08CB0327BAA5E6B0184_mA7BC1C40589A53585C6F00F43E6B2FF9C40DBE3B;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_length_m8D8786E16209392D85AE98D83B29BC7640336172;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_m09C6C267D9BAF792F6EE22EE3EBFFA6DF40A1AF0;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m1CA32CEFE1BB9A05D042A6414E2C873CFDDB24FA;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_buffer_mD13DD7ADD4B630F57B10024897887B721799F114;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_mF9274D4AE2A751E849E75F7FF15624553335C9E3;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Buffer_m8C59DC6D188FDA6852C8E1DCD65467F24AB6C6FF;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_LengthInBytes_mF4D1CE0CFE876F4AEA6727E2F9DC3E9ED4C6F49F;
extern const uint32_t g_rgctx_TU5BU5D_t2D7B9833D50064F29F611CE503A43EFC2FF511E6;
extern const uint32_t g_rgctx_T_t604DA2B2C61AC824178F38507FE80321D55C1110;
extern const uint32_t g_rgctx_TU5BU5D_t2D7B9833D50064F29F611CE503A43EFC2FF511E6;
extern const uint32_t g_rgctx_TU2A_t1C27F4BE14CE8270932C7E85B4E71352AA8A8A89;
extern const uint32_t g_rgctx_TU5BU5D_t2D7B9833D50064F29F611CE503A43EFC2FF511E6;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_mBBBC65A417BA1B4D27BD77E11B44DE17E4AE14EF;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_buffer_mAD49E4514051A525417EED88DCD83FB45B4F65FB;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m927C78B4D944D4E876AB25A3E7AB4F6DFAD4F108;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_m6290A920F0B1C958BBD7B60BA6F94FD3164B806A;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_buffer_m611329C9598F14EB3EA4EF6388A6C6E40E8523EB;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_m07F322CB13F58D937FC51D44F14EE31B19067359;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_m6642F674EE22AC655E358EF533B0F700C16A0A94;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_buffer_m8CB6CFDB26F706901B0078B71B1A2CE1BAF60C6D;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m1CC3012C703D85C7C951AE77DD2B3F5B8E42E918;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_mFAF2429BB4895D0C3AF7C5B981A3C53F440BE6A5;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_buffer_m24223FCEED7DFD56169DBC2A03AAAEA5174C5D3E;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_m29F0A70AA1DAC8624B99E5E213DC4E34036BBF0C;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_m21876ACD769677EB12796837397549EACDC6DA1F;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_mA9B2F154A856E8EB9B4983E2745FD077BA0D98C3;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m496CF2C8A1FAFE63A285C2350D9530A60CC9B463;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m3B2C3C3508065763A6DEE8185CD3507C80EE2159;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m8812C7F42A79683AC17FBC09A7F04E5E909E3A67;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m7701FD9664F730DE055F5A80657EBC6BF96BB399;
extern const uint32_t g_rgctx_IEnumerator_1_t864132BBC4D6B1C046B0F3D7F2450911AEEE1A9D;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tD9E93DC592424CD8364F004CEB76995E76D77AB3;
extern const uint32_t g_rgctx_FixedList32BytesDebugView_1_t1CD5A8A58D08D7919D8281F974EB935476833D6C;
extern const uint32_t g_rgctx_FixedList32Bytes_1_ToArray_mF6911C475E9306A197D8A04413510623F0E8F740;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tD9E93DC592424CD8364F004CEB76995E76D77AB3;
extern const uint32_t g_rgctx_TU5BU5D_t34FE2CCB27C7445A3F5148565BA70492A4623E83;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_length_m5C640A541B738CC503605F802327396C25BA1171;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m904CEC4D13DAB3EC2E63867290A4919B3EE07B94;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_m9AEE49A539D4229D6C082363CC4750BC3C7BD959;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_buffer_mA0BB9B402100E607139F7FA62801A9D7202ED0D1;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_mBEB73D718598A132E3FB38993715F6580F14AAF4;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Buffer_m4ACFD76E5BAB7BBA3B105EF045FB34DF16925121;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_LengthInBytes_m830026A47AC35E78ACFB4ED8613C4241631C7FB3;
extern const uint32_t g_rgctx_TU5BU5D_tF6911E053D5AE9603D92BE68F72EAEDBB086064A;
extern const uint32_t g_rgctx_T_tD32C0F6209442C9BB65A030D29698147784945B7;
extern const uint32_t g_rgctx_TU5BU5D_tF6911E053D5AE9603D92BE68F72EAEDBB086064A;
extern const uint32_t g_rgctx_TU2A_t0B76C0159913EA2F39094B5C3C6626862C41BDD4;
extern const uint32_t g_rgctx_TU5BU5D_tF6911E053D5AE9603D92BE68F72EAEDBB086064A;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_buffer_mF5764B9ABD9312B62FFBDDE169D2596CB8322F82;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mDC309D74DBDE3857D8CF451A6C61E4DF244DB906;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_mFFA2BDEF1D5931103F2349F121D89B9EFDA18B2A;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_m1B404F21933A0C0E3F4EDB9BDC56FF1374BA36BB;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_buffer_mD636EC5A897F515A617C8D8C989634210575B190;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_m913F6E9C36DA6E5150201BB04705176C61779A4A;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_mAE605CEAEF66638FB3772258684647B86795A026;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_buffer_mE9A72851C9374D46F80C06732364E75915F60123;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_mB4AC876C1C0A6D784950D1DF9D938E8187CC254A;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_m709B483E5DEB7FB21FD73055E1D18EFC77CB8D94;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_buffer_m420F196A05B746248682A7B92620D626BBB9703C;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_mA84088FDB735E31D8EC68ED7C7241B82F7E382E9;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_m72AEE1D5D4DD2A52568A3DB0395D4C6A4450D662;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_mFA63385DF5A53E2D34A838F6B4D3E5EB93454707;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_m4A58B7E48EC3D1CE4B417CC606F7AF283179CE19;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_mD426D7F36DA3313161AF4ACB1D163554927B2B5C;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_mA3CB277239697E135B80B1652428B650E393E3EF;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_m676325C656968A3297E0312C5E86C06A021B6A7A;
extern const uint32_t g_rgctx_IEnumerator_1_tB69EBF70B6FD3D2A8F0F87587EDB9568DE58455A;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t67CE70EBD3E649EABEB02AB090791ED7FC71560F;
extern const uint32_t g_rgctx_FixedList64BytesDebugView_1_t3D0591DA37051557C93E337E6B35626BF8BFDA0A;
extern const uint32_t g_rgctx_FixedList64Bytes_1_ToArray_mD850B8780F4E7F3B71859C121BB5ACD36722D1E9;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t67CE70EBD3E649EABEB02AB090791ED7FC71560F;
extern const uint32_t g_rgctx_TU5BU5D_tB1F4A796DD882BF45251D135B2D774A9F6992DBE;
extern const uint32_t g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_length_mBAE161C1DFF2ECF7842E424F7288B260F2A2C51E;
extern const uint32_t g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_m0455E51110778F6A133DB6106D4F22A64B989348;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_mDFFBC1AB4195A6724110DE5980F5E23E6FFBD712;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_buffer_mDB5930630CF855B08587014DE8D3506473296D4A;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_m69D18CFEBEB2907DF3FAC8CA19E77BE6A657316C;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Buffer_m97D30707BB2AEA2F5DBADE3B0FAC8F672E8B1A3D;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_LengthInBytes_m47F607A647F86AE5CEE40BB1760159288C68D0BB;
extern const uint32_t g_rgctx_TU5BU5D_tC487C024BCBA4EA47337A7932AC0700F1F6D1BE3;
extern const uint32_t g_rgctx_T_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30;
extern const uint32_t g_rgctx_TU5BU5D_tC487C024BCBA4EA47337A7932AC0700F1F6D1BE3;
extern const uint32_t g_rgctx_TU2A_t6C372DED1F96DC4FCEC010A180FA1A7C1B902C47;
extern const uint32_t g_rgctx_TU5BU5D_tC487C024BCBA4EA47337A7932AC0700F1F6D1BE3;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_buffer_mC681B3C14BA963B940099A62CAAF506692748203;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mB2C086817BC1745080C475DEF2DC556F31A59D67;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m2666E22B9261026A0D7D0DDBE9CAF263093BCA3B;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_buffer_mC5DD069AE88A690B45AEC4459C47AA99FAA25DED;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m4D4D3FDEAC491E57BF639002CE416C8CAEEAF68B;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_mE9957B37908F02DBF2C0FBC8A01F575132266E51;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m4C2FEAF12A383067066580E5AE042BA1E5E15353;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_buffer_m3EAF6BFA2913658C5F6F5D775406EF214782EF82;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m660F2F29958AF880448E6418281CCCEB44F5B7D4;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m437197F791AB58DC99F09DD25DFF430B2F4CFA16;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_buffer_mFB3AE2574EA0E518DD431C7ACEF8567394C06452;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_m9A5EEBCAC9EB81EA90B9BD7FFB53C8C16795D572;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m255D7C47599B63B530DEF04B43CD7223D203C6DC;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_m086E1CCF2A8A8917B11F2B64CF7D29C6EE148120;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_mBF301D370C792F53697A10E96FFDC089205671AE;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_m2CDB0F15DB14C27621333422EF9796AD3D031766;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_mB57417246E4D25627832638BE91CB141FDF04EA9;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_m23DA07023310D345AD86B3B55A0D2AA231BD7788;
extern const uint32_t g_rgctx_IEnumerator_1_t37AA14CD4097DAA4BDEBD3F3465239E439BF2709;
extern const uint32_t g_rgctx_FixedList128Bytes_1_tE8BF96CAFB0066AE80E807FBB6F6EA548F9EC11F;
extern const uint32_t g_rgctx_FixedList128BytesDebugView_1_tF14BE8325B7FB79760B619E72A8F9C11EE487E4A;
extern const uint32_t g_rgctx_FixedList128Bytes_1_ToArray_m2E58AE81A711F5159810D0D0D001FFB11AF77F66;
extern const uint32_t g_rgctx_FixedList128Bytes_1_tE8BF96CAFB0066AE80E807FBB6F6EA548F9EC11F;
extern const uint32_t g_rgctx_TU5BU5D_t671D07AADBFE2908D6F1C8A9C7DC34A27114E913;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_length_m183A68DCF6C55C241A6955D7F1A07EC1076FA4FE;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m0B248C6A39E59A7CC633B8EBB0E2214A7374A24A;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_mFAB9277A26B57EEBBD96FBFF9A8E348CDF0F3695;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_buffer_m24ECC2F78DC3BA65E330EBEEFBFC1B93287FBC71;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_m84508C7415A499FE729E49407F30491D8BA1347B;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Buffer_mD366D995A0996A4941BFD8FF7751F8888529019A;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_LengthInBytes_mE533117FD90EE225AB1657584FE15D9FCD3B31F6;
extern const uint32_t g_rgctx_TU5BU5D_t6BF24E63E512684913FA7955EAE4CA033EA37484;
extern const uint32_t g_rgctx_T_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B;
extern const uint32_t g_rgctx_TU5BU5D_t6BF24E63E512684913FA7955EAE4CA033EA37484;
extern const uint32_t g_rgctx_TU2A_tFCD2417ECD70E58C278F45815915A4E505D63172;
extern const uint32_t g_rgctx_TU5BU5D_t6BF24E63E512684913FA7955EAE4CA033EA37484;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_buffer_mD1216DEBE552AFECD7CB3523B89BEF8787593BF8;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mFEB57847565241DE5AC20F3C47DFB1C1FBD77D42;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m2ED44CE82345E495B02845190DC950E9369F9B66;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_buffer_mDD766CB12EDF53E04C08F72158D191225232B2AB;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m0E56845145169510605A77387713117B1DF42A2D;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m7D404D1A8DCDBFBA6E77CA9A1BAE087DA8BEDB45;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_buffer_m599E74FA5CB0C59A80C4BE8908C71761AA5112C4;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_mC45C102EEA509426C4E25123ACB537AA2E1C8B53;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m0BE3A1FE674C670F1F163603702EA2EC121C96C0;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_mCB87559DEEEFBF43BBE5EDA844844CCB64B12C06;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_buffer_mE9FD041845ABCC4AC983A467A9A332FA2B8F5022;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_mAFB31975A1194980FE703C4FF6157CA124568175;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m71F5DF9EF41C7A4B6C05EDC77E4AEBD567829F44;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mFEFB3BF366F96DCEEF8CF0F400F6F6BC865C4EFA;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mDC81D66D0B81AB0FEBD1F8666E2BB7783FA5ACB8;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_m2A8267A3F7D08EC772B96BE833442B1E5887C72E;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mBDE871330097648822A6964D577DA54C9C62CDE1;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mDAE776DA9235ED2BD2A75CD0AC265DB954F739E3;
extern const uint32_t g_rgctx_IEnumerator_1_tFF57FF7FE99225B911B779420D0023CC4CC8BEBD;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tE449F4888A88ACA1D546E4F14DB48FB0546A91AF;
extern const uint32_t g_rgctx_FixedList512BytesDebugView_1_t681D060BACAFCFA5C41A9DB6BDA3A0B6D6718D0A;
extern const uint32_t g_rgctx_FixedList512Bytes_1_ToArray_mA6C601DFB85EF1B300C53550D5F09F266E9BDC78;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tE449F4888A88ACA1D546E4F14DB48FB0546A91AF;
extern const uint32_t g_rgctx_TU5BU5D_t695A4288D6AC6783F4B8B7D44ACF896AAEDE253A;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_length_m5DAAEEC7B6793F953FF70903199662A4D8C62107;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_mF981B72DCECDB30EBED5ACCC7749B57FE4D25643;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_mDF9CEBCF3A941F23B144FCD20FDA1D094151485F;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_buffer_mA83EE3BEAA048AF0B8821EFAF1EE65D43DF40886;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m866B91A370BB67C092A4B0E5D72A7734F2132F73;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Buffer_m8C59032AB0882881E308B5310103C4C0C67FE527;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m4521B626F59BBB2321D000869DFCBDC991657E02;
extern const uint32_t g_rgctx_T_tBD073BADADC7386EA705AE4342916B0F07BDACDE;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m7E4C5808A41558EDB297D03CF1DE6FE52199AA57;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_LengthInBytes_mDD272B4890655CD06ACF62B312C72673B7066DAB;
extern const uint32_t g_rgctx_TU5BU5D_tD0DA9C3D0F09BCD217626879DDE80C9EA2173C94;
extern const uint32_t g_rgctx_TU5BU5D_tD0DA9C3D0F09BCD217626879DDE80C9EA2173C94;
extern const uint32_t g_rgctx_TU2A_tC21487FD27289E4B9CF40AD20FC35D459A0F5CD0;
extern const uint32_t g_rgctx_TU5BU5D_tD0DA9C3D0F09BCD217626879DDE80C9EA2173C94;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_buffer_mC5B36234B549F0F6310D4F8F97C377A24615914C;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mE8F8A185DE5BB8CB0BEE9383F387044EC232D30C;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_mDCA60AD837CDD603A14317382F8FE7B7A5332496;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_buffer_m5C3545752DAA512CD96BEA762BB0AE5851998386;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m6B94FC005EB3D868CEC8B0A1A2953929BDB403B9;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m1EE2D74734AD460BFAC1124BEC50F6A7242C157A;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_buffer_m21F75E25B66B6C71D44AB8F0C5ECE446415A67B0;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_mBEFA2EA7F7D0BDBF7900A55AD4870026F5CDD9E0;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m76A8C7244A78E281DE7D75356C8C98452CC9B973;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_buffer_m6E459353D7D7CC62D83C06475D7DB27CAD04594E;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m0FB45017FC642488814B76ED2D5856074D0A2C13;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m7B2C705430CA47FDBA89E556A4BA14F10B5E62D6;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m48E8E70F5EC08F0B2764E1FA714A0E6B42AD79B3;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_mA097DF642E512753B3F3819A63A991273FB15433;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_m3F25D402683E10A10CC6E899C3C5F3242C07DE65;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_mA57897971800CECFA745EAA611995A129370DE0E;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_m234039E2C015B564CDC7962B8ECA3F6161456BC2;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_m79776FC49E1BE2ECE4CD5A12AB1AD9E67199A5B6;
extern const uint32_t g_rgctx_IEnumerator_1_t3529DEFB532FA3B3FC1BECA7AE3AC6C2626EDA06;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t7978B41AA926CEFCFA9E2A4ED471874EA8CC0264;
extern const uint32_t g_rgctx_FixedList4096BytesDebugView_1_tF63D61EC5A1501B9B13C851EF45BF481C7438810;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_ToArray_m7C6E1198C41198C1E2CDE6DB9FAD9F929C8EA525;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t7978B41AA926CEFCFA9E2A4ED471874EA8CC0264;
extern const uint32_t g_rgctx_TU5BU5D_t5289F2C4DE123337284FA748C5DCD822308B0391;
extern const uint32_t g_rgctx_TU26_t9AD8DE72370FBE3DDFE640DBB2D54F0447C30046;
extern const uint32_t g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const uint32_t g_rgctx_TU2A_t9675EE6497AD8465FD78590B10D7DF78A42B1513;
extern const uint32_t g_rgctx_Array_Resize_TisT_tA7BC8A9B01B94F56CE3273E1C3F4463BAFDB2774_m40E5359FE293594F47DD50DDB1F2AD213B4A709A;
extern const uint32_t g_rgctx_TU2A_tA596F7F23DF141DFAF4BC0E1445C816B4C13BFCF;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD44E7FDD63803D509A5BB08B506B82CA121DF38A;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD999DEAF969B234226FD5F050A1A8DF99545F7FC;
extern const uint32_t g_rgctx_NativeArray_1U26_tB283BC3676F2247244A6FB94FDF3FBC638046CA8;
extern const uint32_t g_rgctx_NativeArray_1_tB91A1705CDACA5A60CCC06E5E49A755A829637B8;
extern const uint32_t g_rgctx_T_t68DD3E290C5045D5C9C9EAB6CF8F7F4B7CCEAEF5;
extern const uint32_t g_rgctx_AllocatorManager_AllocateStruct_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t68DD3E290C5045D5C9C9EAB6CF8F7F4B7CCEAEF5_mCD78204D5334495A64D5596132C3B8DF63A9ADFB;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t68DD3E290C5045D5C9C9EAB6CF8F7F4B7CCEAEF5_m73DED6813E12309EE3307BDC06E7D267D3029682;
extern const uint32_t g_rgctx_KVPair_2_tD31735992B66882845229CF969151CC30A084762;
extern const uint32_t g_rgctx_HashMapHelper_1U2A_t4CD02EABE95C6E16D6835F93627F53EE48D4C439;
extern const uint32_t g_rgctx_HashMapHelper_1_tBF74536BC336B8C6FD5259BAB0C872C5F7FC84BE;
extern const uint32_t g_rgctx_TKeyU2A_t7C90E3216650027538E8BDE61D45B94B977EEB49;
extern const uint32_t g_rgctx_TKey_t1C23B5C5206ECE6876F6A933C5393052C3A6D7CF;
extern const uint32_t g_rgctx_TValue_t07BAD9681A6887A56CE27B7BA46659F1E8B4AA45;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisTValue_t07BAD9681A6887A56CE27B7BA46659F1E8B4AA45_m790E5C2ABDADB1747EA6FE4E1249117162CE60CF;
extern const uint32_t g_rgctx_TValueU26_t20450582597353CC2F26FCAA2B473FAF94E82A7F;
extern const uint32_t g_rgctx_NativeList_1__ctor_m9DE27CA89335FD75C2E78B75E36B145BEAA0BF18;
extern const uint32_t g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1;
extern const uint32_t g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1;
extern const uint32_t g_rgctx_NativeList_1_Initialize_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_m05DBC7A44FF9DAD310466511144CC0D0092FF1A2;
extern const uint32_t g_rgctx_T_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA;
extern const uint32_t g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tC9B789EAE714A4CB9218D40209373216B4911076;
extern const uint32_t g_rgctx_UnsafeList_1_get_Item_m3A75728B303CDC6919167D5BFCD56D9BAE755F4A;
extern const uint32_t g_rgctx_UnsafeList_1_set_Item_m55F6D1F1AE6B627E9916AC7867161D4DFA6EB267;
extern const uint32_t g_rgctx_UnsafeList_1_ElementAt_m94F2861AED0CAABDD863BF768BD5B41FE0A10976;
extern const uint32_t g_rgctx_TU26_t9182D13124B0C24A7BE52A270A8164C77416315C;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_mBB48D1B7E16C1A3EFC1FECFAE979F4AC003C5BA7;
extern const uint32_t g_rgctx_UnsafeList_1_Resize_m877B9B1A6AA00562D5D52E78696C5B2364FCB296;
extern const uint32_t g_rgctx_UnsafeList_1_get_Capacity_m7CEDBD1C464E131C4BA666F0BE92ED8747465B1D;
extern const uint32_t g_rgctx_UnsafeList_1_set_Capacity_m3A8F01AD3A516A23455A17E28F75BF7E15624DEB;
extern const uint32_t g_rgctx_UnsafeList_1_AddNoResize_mD39DF18D05C9376E38DF2DFFF72B25B3031055EE;
extern const uint32_t g_rgctx_UnsafeList_1_AddRangeNoResize_mAF4A76BDFF934C7E42B9898B1F0FEB34AFB50A33;
extern const uint32_t g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448;
extern const uint32_t g_rgctx_UnsafeList_1_AddRangeNoResize_mEFCF55AD205D289A4AECB5F522010095E20B773E;
extern const uint32_t g_rgctx_UnsafeList_1_Add_m31E9C9CB6476E5AF889F68A699B2DE541626AE2D;
extern const uint32_t g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m5BD6350564595C21EC23B64D69B98AEC037EDCED;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mF3D9F277FD8C8586515F953BA049D04561B988F9;
extern const uint32_t g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E;
extern const uint32_t g_rgctx_NativeList_1_AddRange_mD97FE11A8C0AA8DFB3B9F220B52BFFE5892A994F;
extern const uint32_t g_rgctx_UnsafeList_1_AddRange_mC90AE6F4AB675E2D4134B516FFF0DB3A03610E8A;
extern const uint32_t g_rgctx_UnsafeList_1_AddReplicate_m189B9A83C79DE8F3E136C86486DC30C425052AE3;
extern const uint32_t g_rgctx_UnsafeList_1_InsertRangeWithBeginEnd_m124A00651AD640BCE5EE0FC32A2E7BB38F706F0B;
extern const uint32_t g_rgctx_NativeList_1_InsertRangeWithBeginEnd_m4BD156E99A1FDEF0984498FB2F356ABB5A042C79;
extern const uint32_t g_rgctx_UnsafeList_1_RemoveAtSwapBack_mACD31FE8F0282A96901562DFFED349749A9CD451;
extern const uint32_t g_rgctx_UnsafeList_1_RemoveRangeSwapBack_mBA1DCB0B3816FB0ADE7B92474A81A04896A6478E;
extern const uint32_t g_rgctx_UnsafeList_1_RemoveAt_m8E84B72BBF49287163A0716AC74C07A268F63AB5;
extern const uint32_t g_rgctx_UnsafeList_1_RemoveRange_m11E70479BBDC0EF84F9FCE64DFDA72793591B139;
extern const uint32_t g_rgctx_NativeList_1_get_IsCreated_m28034692FB3A35195BDD71F815562D61130D7939;
extern const uint32_t g_rgctx_UnsafeList_1_Destroy_m4365069A2F94BC7B50074F5C3C23A35769947475;
extern const uint32_t g_rgctx_UnsafeList_1_Clear_m763F409F9070AAB6B2E20A3952CED497999D10B5;
extern const uint32_t g_rgctx_NativeList_1_AsArray_mF7F649295B1EB59DCF87D80023CDC9825B535243;
extern const uint32_t g_rgctx_TU2A_tB9A028E4087C5A8ADBA9F589ADEB6159DFEF1E1D;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m30203C993DF4EFC52F1B6D61B11B1BE1438DC7A0;
extern const uint32_t g_rgctx_NativeList_1_get_Length_m4B8C67C04462D2985B149497D6D3E59F12616FDB;
extern const uint32_t g_rgctx_CollectionHelper_CreateNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m394862D9377E43B62D1B7D9B30D1FEF56D44B7C6;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m6FB7A7FABFD5B2CF7B07365DE0905FA8CB3A48D3;
extern const uint32_t g_rgctx_NativeArray_1U26_tFD62C24A2E017496E9DD763151C686F3FD3674BA;
extern const uint32_t g_rgctx_UnsafeList_1_CopyFrom_m7BC77DFBA24397B599E7966E0972B7C2D6E2C5C0;
extern const uint32_t g_rgctx_UnsafeList_1U26_t673B25B279BDE67FD78DC88DF9737224EC237FEF;
extern const uint32_t g_rgctx_UnsafeList_1_CopyFrom_m8AC4B9F9AFF29B3DB1B0E153A6F17CDA1412EEE9;
extern const uint32_t g_rgctx_NativeList_1U26_t92D8D927B38C0AA81530D7A1E24F899996570B93;
extern const uint32_t g_rgctx_NativeList_1_CopyFrom_mA352BCA5AEFB0B513135F363717CB9989E8504F7;
extern const uint32_t g_rgctx_Enumerator_tA55E226E9D7AB26F044B58ECE28F8B79A8215FB6;
extern const uint32_t g_rgctx_Enumerator__ctor_mD2EFC009587810FA50EDBA17918DE09FF1A0D3E8;
extern const uint32_t g_rgctx_IEnumerator_1_tE14F09601BCF464F55C6C384864842F6F442D077;
extern const uint32_t g_rgctx_NativeList_1_Resize_m2DA751BFAA461CD57325BF1C6766FBF50AE0E384;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_m8164128FCF10BA0EE2AECE19EE89789B94EA2F61;
extern const uint32_t g_rgctx_UnsafeList_1_TrimExcess_mD22DFEACFEEB1A478154E8EFFDC10FDD04D096C0;
extern const uint32_t g_rgctx_ReadOnly_t722B973D85DAA6C4A475578C8DAB21D81DA66A29;
extern const uint32_t g_rgctx_ReadOnly__ctor_mB77B3AC6D07C2D450CFEB139602CB819918BD593;
extern const uint32_t g_rgctx_ParallelWriter_tA38ACEA2D6CE15CA82A92A21D13E256D2D89ED24;
extern const uint32_t g_rgctx_ParallelWriter__ctor_m91412B47305FD5876EBF9350E5C27BB78704A424;
extern const uint32_t g_rgctx_UU26_t6E44BF865BC62898D5ECAB2D870625781521B69F;
extern const uint32_t g_rgctx_UnsafeList_1_Create_TisU_tAB741574063FA4E9A22A701208EE0217BD0FB7B3_m96E02C6605049092E9322B3F5F499BBF5CA2B0B3;
extern const uint32_t g_rgctx_UU26_tA7B1CA0DA5075A53850D85AB9CEF23CD84CA807B;
extern const uint32_t g_rgctx_NativeList_1_Initialize_TisU_t94B07FD717D5652F1EDF212BF4FA085A54957D42_m7DE83B2E2A8B99EF750065D45795E981F4B793F8;
extern const uint32_t g_rgctx_UU26_t9242600E90BCB0ECBCB53B423ED678CBCE65D887;
extern const uint32_t g_rgctx_UnsafeList_1_Destroy_TisU_t2A39745A8ADF9FF3C2974EA5DF503478AB8D49FC_m56A05C71B3AE325278645642F11E1337304A64BF;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tFD6373CAA08D3C77B8FCA3752E8A2536287E424C;
extern const uint32_t g_rgctx_ParallelWriter_tA6D09E1AA1BA56D8C5D80A07AD8B488DDC5179A6;
extern const uint32_t g_rgctx_NativeList_1_tB8855D88546E7A2838D49D77DCE6E75E1F80FB14;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tD3ABBAF14F47AEDC815DC5F661C18A8AEA961F39;
extern const uint32_t g_rgctx_NativeListDebugView_1_tEBBEC87673430072B17329B4A3372C4642CDB616;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_mE59EDAE5E4888DB0D63771B3059717FF51FF0BDA;
extern const uint32_t g_rgctx_UnsafeList_1_tE42BA19ADFCEAB338A6082A8048FCB8EFBF2A839;
extern const uint32_t g_rgctx_TU5BU5D_t22BBCE13067E0DC5FB9461EF6F5756E406428D9B;
extern const uint32_t g_rgctx_T_t854F231106E098971359B13C59E4AD44632F18AB;
extern const uint32_t g_rgctx_UnsafeList_1_tE42BA19ADFCEAB338A6082A8048FCB8EFBF2A839;
extern const uint32_t g_rgctx_TU2A_t70444A1E233D3D165BF6E6F8023F5EF2A9C6826D;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t854F231106E098971359B13C59E4AD44632F18AB_m0A260818D11A07171045A34EFBA95A92C0FD2DF2;
extern const uint32_t g_rgctx_TU5BU5D_t22BBCE13067E0DC5FB9461EF6F5756E406428D9B;
extern const uint32_t g_rgctx_TU26_tA006DCB5B778DACF2A45C9BB7A1F4F309A8D5278;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2_tAD81EE04B3089A92AC195D2CF8B37490EF094FCD;
extern const uint32_t g_rgctx_NativeArray_1_tF38276A687982E7ACFB12CFD11617B93C74390D8;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m51DFD39B0CD27998E3C4E4F99A4526D147D830A6;
extern const uint32_t g_rgctx_NativeArray_1_tF38276A687982E7ACFB12CFD11617B93C74390D8;
extern const uint32_t g_rgctx_CollectionHelper_CreateNativeArray_TisTKey_t2879092A2024D95D206CF80AF1DE71A78862D016_mD6AC010EF2DF84D5ED3A1C05032458FD4098FB4A;
extern const uint32_t g_rgctx_CollectionHelper_CreateNativeArray_TisTValue_t1C16BAD3E0F7F6FB2449A74B57E68AD46C50CAAE_m92DA03B6BB0DD2206F78CE80CE2B32FC14024AC1;
extern const uint32_t g_rgctx_NativeArray_1_tDEAFC2D54691FE35A2410FE8F57733F13DAEA419;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_mBFEEB5F93DC7E6C867320B7B91131C2723B32D4B;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m8ED4B0DC3476E15C6C1C30B3A7EAA5BA79B43843;
extern const uint32_t g_rgctx_NativeArray_1_tDEAFC2D54691FE35A2410FE8F57733F13DAEA419;
extern const uint32_t g_rgctx_UnmanagedArray_1_tAF6F3819AE72060460FDDAAECEA69E07573B0961;
extern const uint32_t g_rgctx_Unmanaged_Free_TisT_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F_m80B9E4D5EF4A9B36865D150DF2BAEA934AF4B721;
extern const uint32_t g_rgctx_TU2A_tA8F0D072948467527BD52919C365F649AD380C4F;
extern const uint32_t g_rgctx_T_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F;
extern const uint32_t g_rgctx_TU26_tD02DEACCD2B1FD9DEC0AB7F0DFCBBE7EA290A972;
extern const uint32_t g_rgctx_UnsafeQueue_1U2A_t346ECD7AA60CC0B712CC0A6D62F3FA7B12044DD8;
extern const uint32_t g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731;
extern const uint32_t g_rgctx_UnsafeQueue_1_Dispose_m6813829F54FEC31082580562A198BD12F944DC98;
extern const uint32_t g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731;
extern const uint32_t g_rgctx_Unmanaged_Free_TisUnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731_mD105604A2B26407DDF1C5192C10EC9E2AFD454EB;
extern const uint32_t g_rgctx_UnsafeQueue_1_get_IsCreated_mA1F8A614AB0FAE94D15FB9EFCB2167F4A49A8FFD;
extern const uint32_t g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E;
extern const uint32_t g_rgctx_TKeyU2A_tBC54584B7218EA60CC14F8E09B72CCBC0B29AAFE;
extern const uint32_t g_rgctx_HashMapHelper_1U2A_t5CEBA338D2DAFE89C0ACC9FC00EC7B9A41350094;
extern const uint32_t g_rgctx_HashMapHelper_1_Dispose_m8809E5F1A5B7E17F903B5210658F6DF0D2E1F803;
extern const uint32_t g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E;
extern const uint32_t g_rgctx_Unmanaged_Free_TisHashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E_mC24A99CF170BD9350FA0109D2AF3F098B4F0492A;
extern const uint32_t g_rgctx_NativeArray_1_t577A59F749AA3B466DA2833C3D2375F81C09C8FA;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisTKey_t349D5E2348DC065C1656895AA09EC3114157BDEA_m8CE4E814EBB072000CDC39204148706A49DCDCC2;
extern const uint32_t g_rgctx_TKey_t349D5E2348DC065C1656895AA09EC3114157BDEA;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_m5D3E37246C69A90C71AA474AEA5A720B13AA16A5;
extern const uint32_t g_rgctx_NativeArray_1_t577A59F749AA3B466DA2833C3D2375F81C09C8FA;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2__ctor_m129C90738EDD0E04DA63F06A9DEDC83552C7778C;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2_t121B0B2A94F4A88F22881DDE6DBE4B6E9CD4BAFC;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2_get_Length_m23A1DA663765BC6DE23C152F6E7909401A82AD26;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2_t121B0B2A94F4A88F22881DDE6DBE4B6E9CD4BAFC;
extern const uint32_t g_rgctx_NativeArray_1_tDE7BA92803CD5B1EEF7A7E8AC5ECDFCEE8609185;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisTValue_t2FB20D8E529147558F5C433B8B2116AC671EA74B_mDB81772D02C3367EEE6FC3AE69E5FBE1A28CE379;
extern const uint32_t g_rgctx_TValue_t2FB20D8E529147558F5C433B8B2116AC671EA74B;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_mEF896DB283BB4834B0B677A71259243DAB5911EE;
extern const uint32_t g_rgctx_NativeArray_1_tDE7BA92803CD5B1EEF7A7E8AC5ECDFCEE8609185;
extern const uint32_t g_rgctx_UnsafeHashMap_2_get_IsCreated_m5151D7A4A961C290E40795AB4FFDE861B4FCC80D;
extern const uint32_t g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68;
extern const uint32_t g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68;
extern const uint32_t g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96;
extern const uint32_t g_rgctx_HashMapHelper_1_Dispose_m24274E013E518996265A47C2D3DA864B36B89DD6;
extern const uint32_t g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96;
extern const uint32_t g_rgctx_HashMapHelper_1_get_IsCreated_m530BA624A88470DC9FD059C81F9F68E8964C486E;
extern const uint32_t g_rgctx_IEnumerator_1_tD3B69D84966D4D8CBF6548C3A689A1080AEA5614;
extern const uint32_t g_rgctx_IEnumerator_1_t43E45DF4D38B4B8A6E4052D19052E025EEDCDC8D;
extern const uint32_t g_rgctx_UnsafeHashMap_2_tC72AF6AFCF48CE6863E21D1B006C838CF0630FB2;
extern const uint32_t g_rgctx_HashMapHelper_1_tC1AB19BD7F9913D5EAED13E23443E7E6606B8D3F;
extern const uint32_t g_rgctx_UnsafeHashMapDebuggerTypeProxy_2_t19AF83C08B2C513F56313DCEA9FDA5BEAB8B9395;
extern const uint32_t g_rgctx_ReadOnly_t070B2518099F5B7E3D258A84EF675E86F50BE0A1;
extern const uint32_t g_rgctx_List_1_tFCB652B4164832BA2CAE12C9ECDE904710999576;
extern const uint32_t g_rgctx_List_1__ctor_m39BBE76C8346C1233A5BDA00C918060D2D9216E5;
extern const uint32_t g_rgctx_HashMapHelper_1_GetKeyValueArrays_TisTValue_tD3B65CB27D640BC3AF300C5B938FEF933866CA21_m40E34899C6CAE319402A924CD1843C5726DCEA29;
extern const uint32_t g_rgctx_HashMapHelper_1_tC1AB19BD7F9913D5EAED13E23443E7E6606B8D3F;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2_tBADB6013A72B9F9D2C772C42D5BF6442AC814DB6;
extern const uint32_t g_rgctx_NativeArray_1_tB31C96E5CF5461F4FC573CC324AAEA4545074E60;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m48C1E3D1E2D003FC38B7CE9FB9B031690A4C0B65;
extern const uint32_t g_rgctx_NativeArray_1_tB31C96E5CF5461F4FC573CC324AAEA4545074E60;
extern const uint32_t g_rgctx_TKey_t678182020946B40D9B03CFE288BDF3A38AFDE198;
extern const uint32_t g_rgctx_NativeArray_1_t4C9BB2515F95065DC4EB3379C2711DD2A9005D67;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m1A999BAE5336F924046581632D1FD42156789E7C;
extern const uint32_t g_rgctx_NativeArray_1_t4C9BB2515F95065DC4EB3379C2711DD2A9005D67;
extern const uint32_t g_rgctx_TValue_tD3B65CB27D640BC3AF300C5B938FEF933866CA21;
extern const uint32_t g_rgctx_Pair_2_tE8416D82587C8B3F36DBDBE597002153FED05A66;
extern const uint32_t g_rgctx_Pair_2__ctor_mD90DEFA7EC49D923D32082937289347EE45CF96C;
extern const uint32_t g_rgctx_List_1_Add_m88FB92F1240A9F7849DA00948FB154C9F2AD961C;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2_get_Length_mAABF698AF4FEBE422F22BF2D5762B70EFC61104A;
extern const uint32_t g_rgctx_NativeKeyValueArrays_2_tBADB6013A72B9F9D2C772C42D5BF6442AC814DB6;
extern const Il2CppRGCTXConstrainedData g_rgctx_NativeKeyValueArrays_2_tBADB6013A72B9F9D2C772C42D5BF6442AC814DB6_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF;
extern const uint32_t g_rgctx_UnsafeList_1_get_Capacity_mE30AE2BB42DF086225C6BEEACED7A1E66EAC91ED;
extern const uint32_t g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF;
extern const uint32_t g_rgctx_UnsafeList_1_Resize_mD2E7D06E288A389059211E1D82BA7DB35F3AF301;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_mC9FF72A63AF11927A72AFC4491824A38C3741104;
extern const uint32_t g_rgctx_TU2A_t252CC32CDA4B2899B3C069D2669CF4691A3FA43E;
extern const uint32_t g_rgctx_T_t6368C7377A351E8DAE030B3776E2EAB48430F6A1;
extern const uint32_t g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tB931B1757B901B48D48901E8D9AED5AFE81F9DB0;
extern const uint32_t g_rgctx_UnsafeList_1__ctor_mB4DC9C7FDDD8459A5985E14F365473C199A3B3E6;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m4FC1C2939F94321338A4632CDEC0FCEBD7920A1F;
extern const uint32_t g_rgctx_UnsafeList_1_Dispose_mA9C46557DD7D143A317FDF3F55E3DB921A44125C;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m10CA4CD105BFD216034DA9E1E453483651FF7BA7;
extern const uint32_t g_rgctx_UnsafeList_1_get_IsCreated_mC1CC3BDA221944B65278B4F18CE0CB07BF618466;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3380E20A35AE021576F755FA3B86CE60AC955DAB;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m541A7459A8FD164187328166064C4404C9CB1247;
extern const uint32_t g_rgctx_UnsafeList_1_ResizeExact_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_m8643833E8B2CF9CDA9C180A462087121322AFC85;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mB514D6019190B92124D1A238FB222C812F7CC09F;
extern const uint32_t g_rgctx_UnsafeList_1_ResizeExact_mC6D1ECC609A81F523CBF11A11380D91609374527;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_mA94F2BC0DE06EB1D0025E0C41B8513CADC008966;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_m84D5FF3632FD449358D5BFBAD4238CD69C98A61D;
extern const uint32_t g_rgctx_UnsafeList_1_AddRangeNoResize_mCA9913B621995B104B5DEBB23F27F30AC2B4B2CD;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m26A1720E75C9C5B642A2C8736B8323913BE7FA21;
extern const uint32_t g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3;
extern const uint32_t g_rgctx_NativeArray_1U26_t5BC6C78B70DB44E262A11AEDA8AA15E23106CAE5;
extern const uint32_t g_rgctx_NativeArray_1_tBF37C76FCF59508D463F6F20B9A836B1891EC649;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m22AADF779F5C7106D03C48EAB685E636345262C7;
extern const uint32_t g_rgctx_NativeArray_1_tBF37C76FCF59508D463F6F20B9A836B1891EC649;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m83630D4DD2FEF8577092966EA3BAF6DBF892DC73;
extern const uint32_t g_rgctx_UnsafeList_1U26_t2C5B5082DFE47BF6EA8D2610F5BA769A751450C8;
extern const uint32_t g_rgctx_IEnumerator_1_tC56E7A39F471FA7A79480CF2118DAD86C52EB939;
extern const uint32_t g_rgctx_UU26_tD9381527A9C070524FBDBDD330FB7658EDFB83FE;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m33A1C62888A2F0358171DBBB13F92E1995C3982D;
extern const uint32_t g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const uint32_t g_rgctx_UU26_t1B0C73291D19FCB4D6C2FCB3BE3E3D98CD92A78A;
extern const uint32_t g_rgctx_UnsafeList_1_Dispose_TisU_t691A7A8B9C4B558FFA7C9A441E7EDB7B0DBE7F0B_m4CECF45C70FF03910446526F4DE43C444D7C6D89;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisU_t691A7A8B9C4B558FFA7C9A441E7EDB7B0DBE7F0B_m64907AB2D31D9598D0A9B941E35277096F268C12;
extern const uint32_t g_rgctx_UU26_tBF5424FABD879BCBF0DFF2756E13109837860058;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisU_t57153737BB55C69ED76CD3B19F66313058C480FC_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m0171EB9FD9C4611C9145AD5E313FCF721AA6210F;
extern const uint32_t g_rgctx_UU26_t5E4DD5532E4E2D16A863020640E69C9DAAE252E6;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_m4C9D55555D8D78FB343F93C6FC881B5522D5A662;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3F329AE97A7BCCAF3600F13135080670DEBCABC9;
extern const uint32_t g_rgctx_UU26_t8968815DF07BA4CC2C505CF3D681EC7542D8FB73;
extern const uint32_t g_rgctx_UnsafeList_1_ResizeExact_TisU_t74C7EABD355F620088583D354F66E077F74C2116_m4BE8398AA411662D1BF1D2E76EC3558FEB20ECC0;
extern const uint32_t g_rgctx_UnsafeList_1_tE03A86D124B6DD47466D794652CB6A643A39091A;
extern const uint32_t g_rgctx_UnsafeListTDebugView_1_t325A58791301A2821837F7600A06183C4C29894D;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_m85AECBAFC600ABB77B6BE1AD4CF1FA3CCB77FE98;
extern const uint32_t g_rgctx_UnsafeList_1_tE03A86D124B6DD47466D794652CB6A643A39091A;
extern const uint32_t g_rgctx_TU5BU5D_tF837907E098548EB8DB869EE73AAED4A0350D888;
extern const uint32_t g_rgctx_TU2A_t5D8377B1CCBAEAB76E3377E7D8404EE09904044E;
extern const uint32_t g_rgctx_T_tC72B24B3437C42EFBDF11FDB40E3EF22BFE6C166;
extern const uint32_t g_rgctx_TU5BU5D_tF837907E098548EB8DB869EE73AAED4A0350D888;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144;
extern const uint32_t g_rgctx_UnsafeRingQueue_1U2A_t3F23854F9AB67CB5D4FD97923F4BDBC13D8C68A6;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_Dispose_m99EAC3F731930FB75E82E61247A3A6C275889183;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144;
extern const uint32_t g_rgctx_Unmanaged_Free_TisUnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144_m282CDB764993A89C3A1BB6F5EC72DC6AA5CD29C7;
extern const uint32_t g_rgctx_TU2A_t3AA0F4F01B5C2973240CAD0082C9C9F29DCF1A2E;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_get_IsCreated_m6B65E1CD42C387030AF533C0903162F4D2B6BA4A;
extern const uint32_t g_rgctx_Unmanaged_Free_TisT_t6BFCBF80FC8103569C292690B1ABCFD575907821_m17C79CF9056428C2824756091922F227A9495990;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_t886676070BC961806D28002970BC4B0009BB7587;
extern const uint32_t g_rgctx_UnsafeRingQueueDebugView_1_t41FF4B2379E4AFC743C70BE6050495F2C3FD8C02;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_get_Length_m76D6AA4562DA9E7E1C09EF4C2C9FF06422B57B9C;
extern const uint32_t g_rgctx_UnsafeRingQueue_1_t886676070BC961806D28002970BC4B0009BB7587;
extern const uint32_t g_rgctx_TU5BU5D_t9A37E20E593CAD8203006607007509335792B5AD;
extern const uint32_t g_rgctx_TU2A_tE8704AE00B******************************;
extern const uint32_t g_rgctx_T_t2382A1A8375AFA322F11EF8EF3D65E5F81C5FD9A;
extern const uint32_t g_rgctx_TU5BU5D_t9A37E20E593CAD8203006607007509335792B5AD;
static const Il2CppRGCTXDefinition s_rgctxValues[532] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5335F97D02534CC2D1EFAD2B1B8CE794A459864F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5CEA5E60B9EC484E5F0A5233B0FE6573F8FD4204 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_AllocateBlock_TisT_t4B6526BAD6B8C750196E96687551E6FB248D8B93_m20F711BEC9AE42C4FA04E61E37EDAE5F8E13C2A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tF4435F8B669166A749CCFA3D5E9E46AAD064E2CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m147F0BD45B75434C545EA473E1C858F26B10C11C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m21F060306B883FAA6844D2D92424AD0C418E7FB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisT_t53C98BE980141A98061F7E0C00F1E53863D49172_mCE845A9B9E6485B0D134D2578EAAB89D9FB73D29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU2A_t486C2DB331AC02939CDB4420494EF6926230852B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9B5D20DBF15F05E31EF8CD986A51EE4170B1087D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisU_tD3D99706DA190E86407D1D82879F6F709EB4C868_m2AC5DE4526CE78C7DF28F5E331592A1D35522F32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisU_tD3D99706DA190E86407D1D82879F6F709EB4C868_m71C14B1576AD1BAF27A35D6414FAB6B4FCF35856 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisT_t3DD7401FA21FE0512E56A1C674CD70F032268D44_m47B4DF89F52B6CE8FB4FD11EC4E4A82004076746 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tD3D99706DA190E86407D1D82879F6F709EB4C868 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t3F3F4F77ACB3DE60F9CEA6D14439ACADFACF96A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t7F0CFCEFF9DD94FC01C07F1F29EF14CBE94EFA6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_FreeBlock_TisT_t35BD07ABBCB8D61BAD11D72A4D6D6D997BD815DA_mCFAB24856A7C4B600583475999609C1BDE147617 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t512535147A2E70989C9FED965A59897CB227A3CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU2A_tFEE847BDBB617FAF279654649190AF314B52F7B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m08C7637594479E2DE074EBCB3AB56DE38E47F0EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m54C6FF17733951B3182314D7A7392CAF02AE8CBE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisT_tB5F0204FCE510FB4611F370EFC46DA8C45DC09AF_m866B5AC4270563CCF787270E884E9ADB696947CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t329EAE82F86B22F9B6C69972AF45D5E40392CCEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t570532536E3FD3B2205FD25800E5A7DAFDA40675_mC0583857F21D37F314ADCD109E5E9DD8244E4792 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array32768_1_t27311415036D8E10790953E71D1B37720B017554 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array4096_1_t44B5406E19508C8BD82A7FB15B4FAB261D6376F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisT_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34_m224DE97901461C7EFDC5FE9F0AC057A7815ACC92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t17FDE0F88AA456BDCCA436101E8DF16EEE82EC17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array4096_1U2A_tF0B9FD8E1D868F59B252AAF699513C8FAB631575 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array4096_1U26_t538F10C46BA0E53BCE3887D97DD2B3549A3250DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayOfArrays_1_t1A891279D44A27D7201971E48FF0595AE6C329FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_get_BlockSizeInElements_m3B6658871D4634BA2F2D8E48DEF6DF0C4A7C6513 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayOfArrays_1_t1A891279D44A27D7201971E48FF0595AE6C329FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3DC68D76EEFD5956B4495D3A5090407667A3B949 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_BlockIndexOfElement_mD96537023159C87DDE47A0EBA926C1B158750BA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_get_BlockSizeInBytes_m21FD39C48ACC0DF7CE10952DC192942204F6AEE3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_get_Item_m3D3013143B197F211B8AE6D9C24C67A0C3CF27B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tF5DCA4F097698BC7F46F3BDEE454D6CE33AC0FF2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_get_BlockMask_m53FAE8F54503ABD3131449934C38F20BDEF3FBE6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t9CC6943E1E01E1FAD3FE839835FCD9DBEB2CCB44 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_Rewind_mE344EAD9FCDABCF5E34CC3ECC4DBBD0934E23164 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_Clear_mD997F31D3C5B390B5F5E34B31E6C5E66E155574C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayOfArrays_1_get_Length_mA78EE6A17459AFA9101E194E34EA5596B15129B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m4ECD2C0F498BB96F26D22D2211DA124311E6913C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB4AC81566A315CB43ABF22CDBBF5756B1EF56EE9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB4AC81566A315CB43ABF22CDBBF5756B1EF56EE9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayExtensions_Initialize_TisT_t4F1456D1F0D78CBDAD04233A68B351AFD370B8D6_m2A33B8DA1839BC8856106CBC5C6359DE80E72FAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t7C2AAC3610216CE0D5112505492D477B7AB8781C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Key_tE14246D43A66068B6CBEBFCFE05D56F1A63F6AAF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Pair_2_t1A30834948233CCB312886C3BC501F2DC2625556 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Value_tA30DC3FF0EF833B7B36ED2001F58CF9FF1C23056 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tA5912E8646DA88E85E66D08CB0327BAA5E6B0184_mA7BC1C40589A53585C6F00F43E6B2FF9C40DBE3B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_length_m8D8786E16209392D85AE98D83B29BC7640336172 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_m09C6C267D9BAF792F6EE22EE3EBFFA6DF40A1AF0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m1CA32CEFE1BB9A05D042A6414E2C873CFDDB24FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_buffer_mD13DD7ADD4B630F57B10024897887B721799F114 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_mF9274D4AE2A751E849E75F7FF15624553335C9E3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Buffer_m8C59DC6D188FDA6852C8E1DCD65467F24AB6C6FF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_LengthInBytes_mF4D1CE0CFE876F4AEA6727E2F9DC3E9ED4C6F49F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2D7B9833D50064F29F611CE503A43EFC2FF511E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t604DA2B2C61AC824178F38507FE80321D55C1110 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2D7B9833D50064F29F611CE503A43EFC2FF511E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t1C27F4BE14CE8270932C7E85B4E71352AA8A8A89 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2D7B9833D50064F29F611CE503A43EFC2FF511E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_mBBBC65A417BA1B4D27BD77E11B44DE17E4AE14EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_buffer_mAD49E4514051A525417EED88DCD83FB45B4F65FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m927C78B4D944D4E876AB25A3E7AB4F6DFAD4F108 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_m6290A920F0B1C958BBD7B60BA6F94FD3164B806A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_buffer_m611329C9598F14EB3EA4EF6388A6C6E40E8523EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_m07F322CB13F58D937FC51D44F14EE31B19067359 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_m6642F674EE22AC655E358EF533B0F700C16A0A94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_buffer_m8CB6CFDB26F706901B0078B71B1A2CE1BAF60C6D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m1CC3012C703D85C7C951AE77DD2B3F5B8E42E918 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_mFAF2429BB4895D0C3AF7C5B981A3C53F440BE6A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_buffer_m24223FCEED7DFD56169DBC2A03AAAEA5174C5D3E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_m29F0A70AA1DAC8624B99E5E213DC4E34036BBF0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_m21876ACD769677EB12796837397549EACDC6DA1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_mA9B2F154A856E8EB9B4983E2745FD077BA0D98C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m496CF2C8A1FAFE63A285C2350D9530A60CC9B463 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m3B2C3C3508065763A6DEE8185CD3507C80EE2159 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m8812C7F42A79683AC17FBC09A7F04E5E909E3A67 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m7701FD9664F730DE055F5A80657EBC6BF96BB399 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t864132BBC4D6B1C046B0F3D7F2450911AEEE1A9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tD9E93DC592424CD8364F004CEB76995E76D77AB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32BytesDebugView_1_t1CD5A8A58D08D7919D8281F974EB935476833D6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_ToArray_mF6911C475E9306A197D8A04413510623F0E8F740 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tD9E93DC592424CD8364F004CEB76995E76D77AB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t34FE2CCB27C7445A3F5148565BA70492A4623E83 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_length_m5C640A541B738CC503605F802327396C25BA1171 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m904CEC4D13DAB3EC2E63867290A4919B3EE07B94 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_m9AEE49A539D4229D6C082363CC4750BC3C7BD959 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_buffer_mA0BB9B402100E607139F7FA62801A9D7202ED0D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_mBEB73D718598A132E3FB38993715F6580F14AAF4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Buffer_m4ACFD76E5BAB7BBA3B105EF045FB34DF16925121 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_LengthInBytes_m830026A47AC35E78ACFB4ED8613C4241631C7FB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF6911E053D5AE9603D92BE68F72EAEDBB086064A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD32C0F6209442C9BB65A030D29698147784945B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF6911E053D5AE9603D92BE68F72EAEDBB086064A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t0B76C0159913EA2F39094B5C3C6626862C41BDD4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF6911E053D5AE9603D92BE68F72EAEDBB086064A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_buffer_mF5764B9ABD9312B62FFBDDE169D2596CB8322F82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mDC309D74DBDE3857D8CF451A6C61E4DF244DB906 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_mFFA2BDEF1D5931103F2349F121D89B9EFDA18B2A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_m1B404F21933A0C0E3F4EDB9BDC56FF1374BA36BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_buffer_mD636EC5A897F515A617C8D8C989634210575B190 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_m913F6E9C36DA6E5150201BB04705176C61779A4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_mAE605CEAEF66638FB3772258684647B86795A026 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_buffer_mE9A72851C9374D46F80C06732364E75915F60123 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_mB4AC876C1C0A6D784950D1DF9D938E8187CC254A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_m709B483E5DEB7FB21FD73055E1D18EFC77CB8D94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_buffer_m420F196A05B746248682A7B92620D626BBB9703C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_mA84088FDB735E31D8EC68ED7C7241B82F7E382E9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_m72AEE1D5D4DD2A52568A3DB0395D4C6A4450D662 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_mFA63385DF5A53E2D34A838F6B4D3E5EB93454707 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_m4A58B7E48EC3D1CE4B417CC606F7AF283179CE19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_mD426D7F36DA3313161AF4ACB1D163554927B2B5C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_mA3CB277239697E135B80B1652428B650E393E3EF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_m676325C656968A3297E0312C5E86C06A021B6A7A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tB69EBF70B6FD3D2A8F0F87587EDB9568DE58455A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t67CE70EBD3E649EABEB02AB090791ED7FC71560F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64BytesDebugView_1_t3D0591DA37051557C93E337E6B35626BF8BFDA0A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_ToArray_mD850B8780F4E7F3B71859C121BB5ACD36722D1E9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t67CE70EBD3E649EABEB02AB090791ED7FC71560F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tB1F4A796DD882BF45251D135B2D774A9F6992DBE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_length_mBAE161C1DFF2ECF7842E424F7288B260F2A2C51E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_m0455E51110778F6A133DB6106D4F22A64B989348 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_mDFFBC1AB4195A6724110DE5980F5E23E6FFBD712 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_buffer_mDB5930630CF855B08587014DE8D3506473296D4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_m69D18CFEBEB2907DF3FAC8CA19E77BE6A657316C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Buffer_m97D30707BB2AEA2F5DBADE3B0FAC8F672E8B1A3D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_LengthInBytes_m47F607A647F86AE5CEE40BB1760159288C68D0BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tC487C024BCBA4EA47337A7932AC0700F1F6D1BE3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tC487C024BCBA4EA47337A7932AC0700F1F6D1BE3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t6C372DED1F96DC4FCEC010A180FA1A7C1B902C47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tC487C024BCBA4EA47337A7932AC0700F1F6D1BE3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_buffer_mC681B3C14BA963B940099A62CAAF506692748203 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mB2C086817BC1745080C475DEF2DC556F31A59D67 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m2666E22B9261026A0D7D0DDBE9CAF263093BCA3B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_buffer_mC5DD069AE88A690B45AEC4459C47AA99FAA25DED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m4D4D3FDEAC491E57BF639002CE416C8CAEEAF68B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_mE9957B37908F02DBF2C0FBC8A01F575132266E51 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m4C2FEAF12A383067066580E5AE042BA1E5E15353 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_buffer_m3EAF6BFA2913658C5F6F5D775406EF214782EF82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m660F2F29958AF880448E6418281CCCEB44F5B7D4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m437197F791AB58DC99F09DD25DFF430B2F4CFA16 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_buffer_mFB3AE2574EA0E518DD431C7ACEF8567394C06452 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_m9A5EEBCAC9EB81EA90B9BD7FFB53C8C16795D572 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m255D7C47599B63B530DEF04B43CD7223D203C6DC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_m086E1CCF2A8A8917B11F2B64CF7D29C6EE148120 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_mBF301D370C792F53697A10E96FFDC089205671AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_m2CDB0F15DB14C27621333422EF9796AD3D031766 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_mB57417246E4D25627832638BE91CB141FDF04EA9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_m23DA07023310D345AD86B3B55A0D2AA231BD7788 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t37AA14CD4097DAA4BDEBD3F3465239E439BF2709 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_tE8BF96CAFB0066AE80E807FBB6F6EA548F9EC11F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128BytesDebugView_1_tF14BE8325B7FB79760B619E72A8F9C11EE487E4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_ToArray_m2E58AE81A711F5159810D0D0D001FFB11AF77F66 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_tE8BF96CAFB0066AE80E807FBB6F6EA548F9EC11F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t671D07AADBFE2908D6F1C8A9C7DC34A27114E913 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_length_m183A68DCF6C55C241A6955D7F1A07EC1076FA4FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m0B248C6A39E59A7CC633B8EBB0E2214A7374A24A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_mFAB9277A26B57EEBBD96FBFF9A8E348CDF0F3695 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_buffer_m24ECC2F78DC3BA65E330EBEEFBFC1B93287FBC71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_m84508C7415A499FE729E49407F30491D8BA1347B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Buffer_mD366D995A0996A4941BFD8FF7751F8888529019A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_LengthInBytes_mE533117FD90EE225AB1657584FE15D9FCD3B31F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6BF24E63E512684913FA7955EAE4CA033EA37484 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6BF24E63E512684913FA7955EAE4CA033EA37484 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tFCD2417ECD70E58C278F45815915A4E505D63172 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6BF24E63E512684913FA7955EAE4CA033EA37484 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_buffer_mD1216DEBE552AFECD7CB3523B89BEF8787593BF8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mFEB57847565241DE5AC20F3C47DFB1C1FBD77D42 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m2ED44CE82345E495B02845190DC950E9369F9B66 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_buffer_mDD766CB12EDF53E04C08F72158D191225232B2AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m0E56845145169510605A77387713117B1DF42A2D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m7D404D1A8DCDBFBA6E77CA9A1BAE087DA8BEDB45 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_buffer_m599E74FA5CB0C59A80C4BE8908C71761AA5112C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_mC45C102EEA509426C4E25123ACB537AA2E1C8B53 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m0BE3A1FE674C670F1F163603702EA2EC121C96C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_mCB87559DEEEFBF43BBE5EDA844844CCB64B12C06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_buffer_mE9FD041845ABCC4AC983A467A9A332FA2B8F5022 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_mAFB31975A1194980FE703C4FF6157CA124568175 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m71F5DF9EF41C7A4B6C05EDC77E4AEBD567829F44 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mFEFB3BF366F96DCEEF8CF0F400F6F6BC865C4EFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mDC81D66D0B81AB0FEBD1F8666E2BB7783FA5ACB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_m2A8267A3F7D08EC772B96BE833442B1E5887C72E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mBDE871330097648822A6964D577DA54C9C62CDE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mDAE776DA9235ED2BD2A75CD0AC265DB954F739E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tFF57FF7FE99225B911B779420D0023CC4CC8BEBD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tE449F4888A88ACA1D546E4F14DB48FB0546A91AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512BytesDebugView_1_t681D060BACAFCFA5C41A9DB6BDA3A0B6D6718D0A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_ToArray_mA6C601DFB85EF1B300C53550D5F09F266E9BDC78 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tE449F4888A88ACA1D546E4F14DB48FB0546A91AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t695A4288D6AC6783F4B8B7D44ACF896AAEDE253A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_length_m5DAAEEC7B6793F953FF70903199662A4D8C62107 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_mF981B72DCECDB30EBED5ACCC7749B57FE4D25643 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_mDF9CEBCF3A941F23B144FCD20FDA1D094151485F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_buffer_mA83EE3BEAA048AF0B8821EFAF1EE65D43DF40886 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m866B91A370BB67C092A4B0E5D72A7734F2132F73 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Buffer_m8C59032AB0882881E308B5310103C4C0C67FE527 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m4521B626F59BBB2321D000869DFCBDC991657E02 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBD073BADADC7386EA705AE4342916B0F07BDACDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m7E4C5808A41558EDB297D03CF1DE6FE52199AA57 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_LengthInBytes_mDD272B4890655CD06ACF62B312C72673B7066DAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD0DA9C3D0F09BCD217626879DDE80C9EA2173C94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD0DA9C3D0F09BCD217626879DDE80C9EA2173C94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tC21487FD27289E4B9CF40AD20FC35D459A0F5CD0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD0DA9C3D0F09BCD217626879DDE80C9EA2173C94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_buffer_mC5B36234B549F0F6310D4F8F97C377A24615914C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mE8F8A185DE5BB8CB0BEE9383F387044EC232D30C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_mDCA60AD837CDD603A14317382F8FE7B7A5332496 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_buffer_m5C3545752DAA512CD96BEA762BB0AE5851998386 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m6B94FC005EB3D868CEC8B0A1A2953929BDB403B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m1EE2D74734AD460BFAC1124BEC50F6A7242C157A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_buffer_m21F75E25B66B6C71D44AB8F0C5ECE446415A67B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_mBEFA2EA7F7D0BDBF7900A55AD4870026F5CDD9E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m76A8C7244A78E281DE7D75356C8C98452CC9B973 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_buffer_m6E459353D7D7CC62D83C06475D7DB27CAD04594E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m0FB45017FC642488814B76ED2D5856074D0A2C13 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m7B2C705430CA47FDBA89E556A4BA14F10B5E62D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m48E8E70F5EC08F0B2764E1FA714A0E6B42AD79B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_mA097DF642E512753B3F3819A63A991273FB15433 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_m3F25D402683E10A10CC6E899C3C5F3242C07DE65 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_mA57897971800CECFA745EAA611995A129370DE0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_m234039E2C015B564CDC7962B8ECA3F6161456BC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_m79776FC49E1BE2ECE4CD5A12AB1AD9E67199A5B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t3529DEFB532FA3B3FC1BECA7AE3AC6C2626EDA06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t7978B41AA926CEFCFA9E2A4ED471874EA8CC0264 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096BytesDebugView_1_tF63D61EC5A1501B9B13C851EF45BF481C7438810 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_ToArray_m7C6E1198C41198C1E2CDE6DB9FAD9F929C8EA525 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t7978B41AA926CEFCFA9E2A4ED471874EA8CC0264 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t5289F2C4DE123337284FA748C5DCD822308B0391 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9AD8DE72370FBE3DDFE640DBB2D54F0447C30046 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t9675EE6497AD8465FD78590B10D7DF78A42B1513 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tA7BC8A9B01B94F56CE3273E1C3F4463BAFDB2774_m40E5359FE293594F47DD50DDB1F2AD213B4A709A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tA596F7F23DF141DFAF4BC0E1445C816B4C13BFCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD44E7FDD63803D509A5BB08B506B82CA121DF38A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD999DEAF969B234226FD5F050A1A8DF99545F7FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tB283BC3676F2247244A6FB94FDF3FBC638046CA8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB91A1705CDACA5A60CCC06E5E49A755A829637B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t68DD3E290C5045D5C9C9EAB6CF8F7F4B7CCEAEF5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_AllocateStruct_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t68DD3E290C5045D5C9C9EAB6CF8F7F4B7CCEAEF5_mCD78204D5334495A64D5596132C3B8DF63A9ADFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t68DD3E290C5045D5C9C9EAB6CF8F7F4B7CCEAEF5_m73DED6813E12309EE3307BDC06E7D267D3029682 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KVPair_2_tD31735992B66882845229CF969151CC30A084762 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1U2A_t4CD02EABE95C6E16D6835F93627F53EE48D4C439 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_tBF74536BC336B8C6FD5259BAB0C872C5F7FC84BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKeyU2A_t7C90E3216650027538E8BDE61D45B94B977EEB49 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t1C23B5C5206ECE6876F6A933C5393052C3A6D7CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t07BAD9681A6887A56CE27B7BA46659F1E8B4AA45 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisTValue_t07BAD9681A6887A56CE27B7BA46659F1E8B4AA45_m790E5C2ABDADB1747EA6FE4E1249117162CE60CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t20450582597353CC2F26FCAA2B473FAF94E82A7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1__ctor_m9DE27CA89335FD75C2E78B75E36B145BEAA0BF18 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Initialize_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_m05DBC7A44FF9DAD310466511144CC0D0092FF1A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tC9B789EAE714A4CB9218D40209373216B4911076 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Item_m3A75728B303CDC6919167D5BFCD56D9BAE755F4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_set_Item_m55F6D1F1AE6B627E9916AC7867161D4DFA6EB267 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_ElementAt_m94F2861AED0CAABDD863BF768BD5B41FE0A10976 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9182D13124B0C24A7BE52A270A8164C77416315C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_mBB48D1B7E16C1A3EFC1FECFAE979F4AC003C5BA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Resize_m877B9B1A6AA00562D5D52E78696C5B2364FCB296 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Capacity_m7CEDBD1C464E131C4BA666F0BE92ED8747465B1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_set_Capacity_m3A8F01AD3A516A23455A17E28F75BF7E15624DEB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_AddNoResize_mD39DF18D05C9376E38DF2DFFF72B25B3031055EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_AddRangeNoResize_mAF4A76BDFF934C7E42B9898B1F0FEB34AFB50A33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_AddRangeNoResize_mEFCF55AD205D289A4AECB5F522010095E20B773E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Add_m31E9C9CB6476E5AF889F68A699B2DE541626AE2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m5BD6350564595C21EC23B64D69B98AEC037EDCED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mF3D9F277FD8C8586515F953BA049D04561B988F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_AddRange_mD97FE11A8C0AA8DFB3B9F220B52BFFE5892A994F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_AddRange_mC90AE6F4AB675E2D4134B516FFF0DB3A03610E8A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_AddReplicate_m189B9A83C79DE8F3E136C86486DC30C425052AE3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_InsertRangeWithBeginEnd_m124A00651AD640BCE5EE0FC32A2E7BB38F706F0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_InsertRangeWithBeginEnd_m4BD156E99A1FDEF0984498FB2F356ABB5A042C79 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_RemoveAtSwapBack_mACD31FE8F0282A96901562DFFED349749A9CD451 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_RemoveRangeSwapBack_mBA1DCB0B3816FB0ADE7B92474A81A04896A6478E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_RemoveAt_m8E84B72BBF49287163A0716AC74C07A268F63AB5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_RemoveRange_m11E70479BBDC0EF84F9FCE64DFDA72793591B139 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_get_IsCreated_m28034692FB3A35195BDD71F815562D61130D7939 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Destroy_m4365069A2F94BC7B50074F5C3C23A35769947475 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Clear_m763F409F9070AAB6B2E20A3952CED497999D10B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_AsArray_mF7F649295B1EB59DCF87D80023CDC9825B535243 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tB9A028E4087C5A8ADBA9F589ADEB6159DFEF1E1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m30203C993DF4EFC52F1B6D61B11B1BE1438DC7A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_get_Length_m4B8C67C04462D2985B149497D6D3E59F12616FDB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionHelper_CreateNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m394862D9377E43B62D1B7D9B30D1FEF56D44B7C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m6FB7A7FABFD5B2CF7B07365DE0905FA8CB3A48D3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tFD62C24A2E017496E9DD763151C686F3FD3674BA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_CopyFrom_m7BC77DFBA24397B599E7966E0972B7C2D6E2C5C0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U26_t673B25B279BDE67FD78DC88DF9737224EC237FEF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_CopyFrom_m8AC4B9F9AFF29B3DB1B0E153A6F17CDA1412EEE9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1U26_t92D8D927B38C0AA81530D7A1E24F899996570B93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_CopyFrom_mA352BCA5AEFB0B513135F363717CB9989E8504F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tA55E226E9D7AB26F044B58ECE28F8B79A8215FB6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator__ctor_mD2EFC009587810FA50EDBA17918DE09FF1A0D3E8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tE14F09601BCF464F55C6C384864842F6F442D077 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Resize_m2DA751BFAA461CD57325BF1C6766FBF50AE0E384 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_m8164128FCF10BA0EE2AECE19EE89789B94EA2F61 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_TrimExcess_mD22DFEACFEEB1A478154E8EFFDC10FDD04D096C0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnly_t722B973D85DAA6C4A475578C8DAB21D81DA66A29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReadOnly__ctor_mB77B3AC6D07C2D450CFEB139602CB819918BD593 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelWriter_tA38ACEA2D6CE15CA82A92A21D13E256D2D89ED24 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParallelWriter__ctor_m91412B47305FD5876EBF9350E5C27BB78704A424 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t6E44BF865BC62898D5ECAB2D870625781521B69F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Create_TisU_tAB741574063FA4E9A22A701208EE0217BD0FB7B3_m96E02C6605049092E9322B3F5F499BBF5CA2B0B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_tA7B1CA0DA5075A53850D85AB9CEF23CD84CA807B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Initialize_TisU_t94B07FD717D5652F1EDF212BF4FA085A54957D42_m7DE83B2E2A8B99EF750065D45795E981F4B793F8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t9242600E90BCB0ECBCB53B423ED678CBCE65D887 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Destroy_TisU_t2A39745A8ADF9FF3C2974EA5DF503478AB8D49FC_m56A05C71B3AE325278645642F11E1337304A64BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tFD6373CAA08D3C77B8FCA3752E8A2536287E424C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelWriter_tA6D09E1AA1BA56D8C5D80A07AD8B488DDC5179A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tB8855D88546E7A2838D49D77DCE6E75E1F80FB14 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tD3ABBAF14F47AEDC815DC5F661C18A8AEA961F39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeListDebugView_1_tEBBEC87673430072B17329B4A3372C4642CDB616 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_mE59EDAE5E4888DB0D63771B3059717FF51FF0BDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_tE42BA19ADFCEAB338A6082A8048FCB8EFBF2A839 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t22BBCE13067E0DC5FB9461EF6F5756E406428D9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t854F231106E098971359B13C59E4AD44632F18AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_tE42BA19ADFCEAB338A6082A8048FCB8EFBF2A839 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t70444A1E233D3D165BF6E6F8023F5EF2A9C6826D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t854F231106E098971359B13C59E4AD44632F18AB_m0A260818D11A07171045A34EFBA95A92C0FD2DF2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t22BBCE13067E0DC5FB9461EF6F5756E406428D9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tA006DCB5B778DACF2A45C9BB7A1F4F309A8D5278 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeKeyValueArrays_2_tAD81EE04B3089A92AC195D2CF8B37490EF094FCD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tF38276A687982E7ACFB12CFD11617B93C74390D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m51DFD39B0CD27998E3C4E4F99A4526D147D830A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tF38276A687982E7ACFB12CFD11617B93C74390D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionHelper_CreateNativeArray_TisTKey_t2879092A2024D95D206CF80AF1DE71A78862D016_mD6AC010EF2DF84D5ED3A1C05032458FD4098FB4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionHelper_CreateNativeArray_TisTValue_t1C16BAD3E0F7F6FB2449A74B57E68AD46C50CAAE_m92DA03B6BB0DD2206F78CE80CE2B32FC14024AC1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tDEAFC2D54691FE35A2410FE8F57733F13DAEA419 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_mBFEEB5F93DC7E6C867320B7B91131C2723B32D4B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m8ED4B0DC3476E15C6C1C30B3A7EAA5BA79B43843 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tDEAFC2D54691FE35A2410FE8F57733F13DAEA419 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnmanagedArray_1_tAF6F3819AE72060460FDDAAECEA69E07573B0961 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisT_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F_m80B9E4D5EF4A9B36865D150DF2BAEA934AF4B721 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tA8F0D072948467527BD52919C365F649AD380C4F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tD02DEACCD2B1FD9DEC0AB7F0DFCBBE7EA290A972 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeQueue_1U2A_t346ECD7AA60CC0B712CC0A6D62F3FA7B12044DD8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeQueue_1_Dispose_m6813829F54FEC31082580562A198BD12F944DC98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisUnsafeQueue_1_tDB563C8EF11AD469637928F73E835A69FEDC4731_mD105604A2B26407DDF1C5192C10EC9E2AFD454EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeQueue_1_get_IsCreated_mA1F8A614AB0FAE94D15FB9EFCB2167F4A49A8FFD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKeyU2A_tBC54584B7218EA60CC14F8E09B72CCBC0B29AAFE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1U2A_t5CEBA338D2DAFE89C0ACC9FC00EC7B9A41350094 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashMapHelper_1_Dispose_m8809E5F1A5B7E17F903B5210658F6DF0D2E1F803 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisHashMapHelper_1_tEED9A6CCF30C2B6A67BC689904FEA26F2CDF152E_mC24A99CF170BD9350FA0109D2AF3F098B4F0492A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t577A59F749AA3B466DA2833C3D2375F81C09C8FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisTKey_t349D5E2348DC065C1656895AA09EC3114157BDEA_m8CE4E814EBB072000CDC39204148706A49DCDCC2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t349D5E2348DC065C1656895AA09EC3114157BDEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_m5D3E37246C69A90C71AA474AEA5A720B13AA16A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t577A59F749AA3B466DA2833C3D2375F81C09C8FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeKeyValueArrays_2__ctor_m129C90738EDD0E04DA63F06A9DEDC83552C7778C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeKeyValueArrays_2_t121B0B2A94F4A88F22881DDE6DBE4B6E9CD4BAFC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeKeyValueArrays_2_get_Length_m23A1DA663765BC6DE23C152F6E7909401A82AD26 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeKeyValueArrays_2_t121B0B2A94F4A88F22881DDE6DBE4B6E9CD4BAFC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tDE7BA92803CD5B1EEF7A7E8AC5ECDFCEE8609185 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisTValue_t2FB20D8E529147558F5C433B8B2116AC671EA74B_mDB81772D02C3367EEE6FC3AE69E5FBE1A28CE379 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t2FB20D8E529147558F5C433B8B2116AC671EA74B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_mEF896DB283BB4834B0B677A71259243DAB5911EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tDE7BA92803CD5B1EEF7A7E8AC5ECDFCEE8609185 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeHashMap_2_get_IsCreated_m5151D7A4A961C290E40795AB4FFDE861B4FCC80D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeHashMap_2_t4F0687351E4A3BAFE11AF7ADE1078F7A87AA6F68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashMapHelper_1_Dispose_m24274E013E518996265A47C2D3DA864B36B89DD6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_t3DE78F544727F83893C6754658CE19346F8B4E96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashMapHelper_1_get_IsCreated_m530BA624A88470DC9FD059C81F9F68E8964C486E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tD3B69D84966D4D8CBF6548C3A689A1080AEA5614 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t43E45DF4D38B4B8A6E4052D19052E025EEDCDC8D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeHashMap_2_tC72AF6AFCF48CE6863E21D1B006C838CF0630FB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_tC1AB19BD7F9913D5EAED13E23443E7E6606B8D3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeHashMapDebuggerTypeProxy_2_t19AF83C08B2C513F56313DCEA9FDA5BEAB8B9395 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnly_t070B2518099F5B7E3D258A84EF675E86F50BE0A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tFCB652B4164832BA2CAE12C9ECDE904710999576 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m39BBE76C8346C1233A5BDA00C918060D2D9216E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashMapHelper_1_GetKeyValueArrays_TisTValue_tD3B65CB27D640BC3AF300C5B938FEF933866CA21_m40E34899C6CAE319402A924CD1843C5726DCEA29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashMapHelper_1_tC1AB19BD7F9913D5EAED13E23443E7E6606B8D3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeKeyValueArrays_2_tBADB6013A72B9F9D2C772C42D5BF6442AC814DB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB31C96E5CF5461F4FC573CC324AAEA4545074E60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m48C1E3D1E2D003FC38B7CE9FB9B031690A4C0B65 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB31C96E5CF5461F4FC573CC324AAEA4545074E60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t678182020946B40D9B03CFE288BDF3A38AFDE198 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t4C9BB2515F95065DC4EB3379C2711DD2A9005D67 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m1A999BAE5336F924046581632D1FD42156789E7C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t4C9BB2515F95065DC4EB3379C2711DD2A9005D67 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tD3B65CB27D640BC3AF300C5B938FEF933866CA21 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Pair_2_tE8416D82587C8B3F36DBDBE597002153FED05A66 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Pair_2__ctor_mD90DEFA7EC49D923D32082937289347EE45CF96C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m88FB92F1240A9F7849DA00948FB154C9F2AD961C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeKeyValueArrays_2_get_Length_mAABF698AF4FEBE422F22BF2D5762B70EFC61104A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeKeyValueArrays_2_tBADB6013A72B9F9D2C772C42D5BF6442AC814DB6 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_NativeKeyValueArrays_2_tBADB6013A72B9F9D2C772C42D5BF6442AC814DB6_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Capacity_mE30AE2BB42DF086225C6BEEACED7A1E66EAC91ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Resize_mD2E7D06E288A389059211E1D82BA7DB35F3AF301 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_mC9FF72A63AF11927A72AFC4491824A38C3741104 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t252CC32CDA4B2899B3C069D2669CF4691A3FA43E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6368C7377A351E8DAE030B3776E2EAB48430F6A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tB931B1757B901B48D48901E8D9AED5AFE81F9DB0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1__ctor_mB4DC9C7FDDD8459A5985E14F365473C199A3B3E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m4FC1C2939F94321338A4632CDEC0FCEBD7920A1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Dispose_mA9C46557DD7D143A317FDF3F55E3DB921A44125C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m10CA4CD105BFD216034DA9E1E453483651FF7BA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_IsCreated_mC1CC3BDA221944B65278B4F18CE0CB07BF618466 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3380E20A35AE021576F755FA3B86CE60AC955DAB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m541A7459A8FD164187328166064C4404C9CB1247 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_ResizeExact_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_m8643833E8B2CF9CDA9C180A462087121322AFC85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mB514D6019190B92124D1A238FB222C812F7CC09F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_ResizeExact_mC6D1ECC609A81F523CBF11A11380D91609374527 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_mA94F2BC0DE06EB1D0025E0C41B8513CADC008966 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_m84D5FF3632FD449358D5BFBAD4238CD69C98A61D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_AddRangeNoResize_mCA9913B621995B104B5DEBB23F27F30AC2B4B2CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m26A1720E75C9C5B642A2C8736B8323913BE7FA21 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t5BC6C78B70DB44E262A11AEDA8AA15E23106CAE5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tBF37C76FCF59508D463F6F20B9A836B1891EC649 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m22AADF779F5C7106D03C48EAB685E636345262C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tBF37C76FCF59508D463F6F20B9A836B1891EC649 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafeReadOnlyPtr_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m83630D4DD2FEF8577092966EA3BAF6DBF892DC73 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U26_t2C5B5082DFE47BF6EA8D2610F5BA769A751450C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tC56E7A39F471FA7A79480CF2118DAD86C52EB939 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_tD9381527A9C070524FBDBDD330FB7658EDFB83FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m33A1C62888A2F0358171DBBB13F92E1995C3982D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t1B0C73291D19FCB4D6C2FCB3BE3E3D98CD92A78A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Dispose_TisU_t691A7A8B9C4B558FFA7C9A441E7EDB7B0DBE7F0B_m4CECF45C70FF03910446526F4DE43C444D7C6D89 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisU_t691A7A8B9C4B558FFA7C9A441E7EDB7B0DBE7F0B_m64907AB2D31D9598D0A9B941E35277096F268C12 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_tBF5424FABD879BCBF0DFF2756E13109837860058 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisU_t57153737BB55C69ED76CD3B19F66313058C480FC_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m0171EB9FD9C4611C9145AD5E313FCF721AA6210F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t5E4DD5532E4E2D16A863020640E69C9DAAE252E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_m4C9D55555D8D78FB343F93C6FC881B5522D5A662 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisU_tE5F15C2129BD553522B73DAA63B9711E7A0CE4C8_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3F329AE97A7BCCAF3600F13135080670DEBCABC9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t8968815DF07BA4CC2C505CF3D681EC7542D8FB73 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_ResizeExact_TisU_t74C7EABD355F620088583D354F66E077F74C2116_m4BE8398AA411662D1BF1D2E76EC3558FEB20ECC0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_tE03A86D124B6DD47466D794652CB6A643A39091A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeListTDebugView_1_t325A58791301A2821837F7600A06183C4C29894D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_m85AECBAFC600ABB77B6BE1AD4CF1FA3CCB77FE98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_tE03A86D124B6DD47466D794652CB6A643A39091A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF837907E098548EB8DB869EE73AAED4A0350D888 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t5D8377B1CCBAEAB76E3377E7D8404EE09904044E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC72B24B3437C42EFBDF11FDB40E3EF22BFE6C166 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF837907E098548EB8DB869EE73AAED4A0350D888 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1U2A_t3F23854F9AB67CB5D4FD97923F4BDBC13D8C68A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeRingQueue_1_Dispose_m99EAC3F731930FB75E82E61247A3A6C275889183 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisUnsafeRingQueue_1_tD4356C6481016CDEDFF6697D5A6945FCD7C97144_m282CDB764993A89C3A1BB6F5EC72DC6AA5CD29C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t3AA0F4F01B5C2973240CAD0082C9C9F29DCF1A2E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeRingQueue_1_get_IsCreated_m6B65E1CD42C387030AF533C0903162F4D2B6BA4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisT_t6BFCBF80FC8103569C292690B1ABCFD575907821_m17C79CF9056428C2824756091922F227A9495990 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1_t886676070BC961806D28002970BC4B0009BB7587 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueueDebugView_1_t41FF4B2379E4AFC743C70BE6050495F2C3FD8C02 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeRingQueue_1_get_Length_m76D6AA4562DA9E7E1C09EF4C2C9FF06422B57B9C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeRingQueue_1_t886676070BC961806D28002970BC4B0009BB7587 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t9A37E20E593CAD8203006607007509335792B5AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tE8704AE00B****************************** },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2382A1A8375AFA322F11EF8EF3D65E5F81C5FD9A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t9A37E20E593CAD8203006607007509335792B5AD },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnity_Collections;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Collections_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Collections_CodeGenModule = 
{
	"Unity.Collections.dll",
	505,
	s_methodPointers,
	99,
	s_adjustorThunks,
	s_InvokerIndices,
	8,
	s_reversePInvokeIndices,
	51,
	s_rgctxIndices,
	532,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnity_Collections,
	NULL,
	NULL,
	NULL,
	NULL,
};
