﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7 (void);
extern void Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A (void);
extern void Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB (void);
extern void Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC (void);
extern void Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4 (void);
extern void Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B (void);
extern void Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554 (void);
extern void Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB (void);
extern void Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A (void);
extern void Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E (void);
extern void Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC (void);
extern void AudioMemoryManager_Internal_AllocateAudioMemory_m6477CDE12AEC407FA1F621DD7DF3FDE4AEF3B218 (void);
extern void AudioMemoryManager_Internal_FreeAudioMemory_mDE924139CDF3495E1DA5701BCE721BE8B91995B5 (void);
extern void AudioOutputHookManager_Internal_CreateAudioOutputHook_m79D1750B1E46DFAC54B6A6CC0EF62BD881466652 (void);
extern void AudioOutputHookManager_Internal_DisposeAudioOutputHook_m341EC751DD8261C166DDB3DCE0921E1F7562E5AE (void);
extern void DSPCommandBlockInternal_Internal_CreateDSPNode_mF7A179F960B4A69BE2BA8B4CA6C4A920F1D0A6CA (void);
extern void DSPCommandBlockInternal_Internal_SetFloat_m2BAFF539218CADA8EA4A55781E669E05EC674FF7 (void);
extern void DSPCommandBlockInternal_Internal_AddFloatKey_mDB6E047D8D2325F41B3B32D08351F8A8582B38F9 (void);
extern void DSPCommandBlockInternal_Internal_SustainFloat_mB3BF509D4BF71450A5728C6225C23441A750BCC9 (void);
extern void DSPCommandBlockInternal_Internal_UpdateAudioJob_mC37A6F66E87D89951F7D6AE851F0DB75967288BF (void);
extern void DSPCommandBlockInternal_Internal_CreateUpdateRequest_mCFE48A69148D612EF5F46A618D59E5EFFA77ABB4 (void);
extern void DSPCommandBlockInternal_Internal_ReleaseDSPNode_m06CC2F8EC21B5D44D3DF99F5D3547206D6B05417 (void);
extern void DSPCommandBlockInternal_Internal_Connect_m054DF2EAA02C53B31D2F5961DB404F5054DA9D30 (void);
extern void DSPCommandBlockInternal_Internal_Disconnect_m5DAE137FAAD9AC00A839D79802F4E4E6202A3893 (void);
extern void DSPCommandBlockInternal_Internal_DisconnectByHandle_mEEFF5BEF6440EDE4FD470F8D2EFD985C24AED724 (void);
extern void DSPCommandBlockInternal_Internal_SetAttenuation_m4292E37733553E6E4BDCA61B60A6EA515A10E5B0 (void);
extern void DSPCommandBlockInternal_Internal_AddAttenuationKey_m53CF3B9C8F8E10BB7C423A4BE86E8D260A2B5A97 (void);
extern void DSPCommandBlockInternal_Internal_SustainAttenuation_m1EA00E4B260E23EF2336D75119403DC89E31ABE7 (void);
extern void DSPCommandBlockInternal_Internal_AddInletPort_mB87A548C9E61181DF109B8026228D4C519BC5471 (void);
extern void DSPCommandBlockInternal_Internal_AddOutletPort_mDCA8B2779EEDDCD6C32EC5DB1220EE99C3E8B917 (void);
extern void DSPCommandBlockInternal_Internal_SetSampleProvider_m60DDBDF9A0577CA1F61D65FE4F348BE53CF368AE (void);
extern void DSPCommandBlockInternal_Internal_InsertSampleProvider_mC7823CF15825F7DD9DF57E00867129C279BE8AE4 (void);
extern void DSPCommandBlockInternal_Internal_RemoveSampleProvider_mFF427348FD2793A350A11C1D383256FCD1573A2F (void);
extern void DSPCommandBlockInternal_Internal_Complete_mC729AD7209B1FBA59BDB2589B2C83F247EB5F8B2 (void);
extern void DSPCommandBlockInternal_Internal_Cancel_m152036F83982FA1BACAC4FFF89EED12282E0E6DB (void);
extern void DSPGraphInternal_Internal_CreateDSPGraph_m72A230BE10087CF328C66141B467D09B55A9BABF (void);
extern void DSPGraphInternal_Internal_DisposeDSPGraph_mCDCAF8DEF12C5F6DDD621BD71C966040CF2A8F46 (void);
extern void DSPGraphInternal_Internal_CreateDSPCommandBlock_mFA2BC21F3A834F1B1E3F10BAB6526354CCEA6F6E (void);
extern void DSPGraphInternal_Internal_AddNodeEventHandler_m188CB69F74DAD2564F4E9D09541D1DD9B74077C5 (void);
extern void DSPGraphInternal_Internal_RemoveNodeEventHandler_mD5B5A6D981EBD95555C69371D4997B46DEF6CD46 (void);
extern void DSPGraphInternal_Internal_GetRootDSP_m1B8CDAFAF7DA9D5C978BCC82141B800EC5258CDE (void);
extern void DSPGraphInternal_Internal_GetDSPClock_mE5ED88200F2CABB64145D79795A6B272048C9C50 (void);
extern void DSPGraphInternal_Internal_BeginMix_m74026FE70451C061050B5F3734FEBDB23723E76E (void);
extern void DSPGraphInternal_Internal_ReadMix_mFB711B685105375970217979CE01E8405F7405E6 (void);
extern void DSPGraphInternal_Internal_Update_mD60D6CF4E6AB1E62281A4CE9AF4F7E70B4B6EC72 (void);
extern void DSPGraphInternal_Internal_AssertMixerThread_mB04CB56D118E4565A43408939143F7FD0F06D3B2 (void);
extern void DSPGraphInternal_Internal_AssertMainThread_m9410E67771DB59819D755C4CCC208677D7A88CC1 (void);
extern void DSPGraphInternal_Internal_AllocateHandle_m69E97B3223CAA4EFFE358A028F59BE32A923A1F2 (void);
extern void DSPGraphInternal_Internal_InitializeJob_m1B01207BAFBF94072EB90293CF19F0D89F3F1EB6 (void);
extern void DSPGraphInternal_Internal_ExecuteJob_mF6669FC184BC1FAE03CB67DA4EFF5A15E3904F90 (void);
extern void DSPGraphInternal_Internal_ExecuteUpdateJob_mE122CCFC7CB11D1C086470598A93FEDB367384EB (void);
extern void DSPGraphInternal_Internal_DisposeJob_mCCC34DC360E1875DE1151A69688E4759FA135E84 (void);
extern void DSPGraphInternal_Internal_ScheduleGraph_m2930B5BB4FA10E217AC3250854D12825DBC580C0 (void);
extern void DSPGraphInternal_Internal_SyncFenceNoWorkSteal_m516B51B2F645FC5F2B685BFA1590F75CA740BE5E (void);
extern void DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F (void);
extern void DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625 (void);
extern void DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB (void);
extern void DSPNodeUpdateRequestHandleInternal_Internal_GetUpdateJobData_m0128B820FAC1845E701B941E7EF0ED111EBB650F (void);
extern void DSPNodeUpdateRequestHandleInternal_Internal_HasError_mCD6B9073C26ECF79028D14CB4653D87D485237BE (void);
extern void DSPNodeUpdateRequestHandleInternal_Internal_GetDSPNode_mB3A8DCAE003DC7D7F8FD4438BD5EA58DE0FE82BC (void);
extern void DSPNodeUpdateRequestHandleInternal_Internal_GetFence_mC49E96BD64E8B0A4105A12630E552E1E57CC6155 (void);
extern void DSPNodeUpdateRequestHandleInternal_Internal_Dispose_m471B6C9D926E3060B016B948E6D76BD191D11FA6 (void);
extern void DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProvider_m452593870CB755E994B7AD42D8C2DE192480B835 (void);
extern void DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProvider_mBABB781CA231ED433404E196767651697022D5A2 (void);
extern void DSPSampleProviderInternal_Internal_ReadFloatFromSampleProvider_m214E1C1E2D627E77536EFAC67C228912D3A6A0D0 (void);
extern void DSPSampleProviderInternal_Internal_GetChannelCount_m0F11CAAD6596B88F7E56BDABDA99E1F21D3EDC8A (void);
extern void DSPSampleProviderInternal_Internal_GetSampleRate_m679B37C8351CADEA6FF53F99B5A71725416D2B38 (void);
extern void DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProviderById_m1B5A33CD09C3CBC636E5E1BC2C1381DB8346B605 (void);
extern void DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProviderById_m1C3099A8576A8D28FD89BAFF4397FF937F18FEEA (void);
extern void DSPSampleProviderInternal_Internal_ReadFloatFromSampleProviderById_m585D8447ECFDDE270DE19750436DEC7A88A515D8 (void);
extern void DSPSampleProviderInternal_Internal_GetChannelCountById_mF9B7C00D8DFB78E5F40C8BA0D3E6BA75BE00DB63 (void);
extern void DSPSampleProviderInternal_Internal_GetSampleRateById_m52C797917E17C7E639BBC8CE8A65C251473C3924 (void);
extern void ExecuteContextInternal_Internal_PostEvent_m85AEE83FF62AF2694A6153A056AB8C711BE7E685 (void);
static Il2CppMethodPointer s_methodPointers[73] = 
{
	Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7,
	Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A,
	Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB,
	Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC,
	Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4,
	Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B,
	Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554,
	Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB,
	Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A,
	Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E,
	Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC,
	AudioMemoryManager_Internal_AllocateAudioMemory_m6477CDE12AEC407FA1F621DD7DF3FDE4AEF3B218,
	AudioMemoryManager_Internal_FreeAudioMemory_mDE924139CDF3495E1DA5701BCE721BE8B91995B5,
	AudioOutputHookManager_Internal_CreateAudioOutputHook_m79D1750B1E46DFAC54B6A6CC0EF62BD881466652,
	AudioOutputHookManager_Internal_DisposeAudioOutputHook_m341EC751DD8261C166DDB3DCE0921E1F7562E5AE,
	DSPCommandBlockInternal_Internal_CreateDSPNode_mF7A179F960B4A69BE2BA8B4CA6C4A920F1D0A6CA,
	DSPCommandBlockInternal_Internal_SetFloat_m2BAFF539218CADA8EA4A55781E669E05EC674FF7,
	DSPCommandBlockInternal_Internal_AddFloatKey_mDB6E047D8D2325F41B3B32D08351F8A8582B38F9,
	DSPCommandBlockInternal_Internal_SustainFloat_mB3BF509D4BF71450A5728C6225C23441A750BCC9,
	DSPCommandBlockInternal_Internal_UpdateAudioJob_mC37A6F66E87D89951F7D6AE851F0DB75967288BF,
	DSPCommandBlockInternal_Internal_CreateUpdateRequest_mCFE48A69148D612EF5F46A618D59E5EFFA77ABB4,
	DSPCommandBlockInternal_Internal_ReleaseDSPNode_m06CC2F8EC21B5D44D3DF99F5D3547206D6B05417,
	DSPCommandBlockInternal_Internal_Connect_m054DF2EAA02C53B31D2F5961DB404F5054DA9D30,
	DSPCommandBlockInternal_Internal_Disconnect_m5DAE137FAAD9AC00A839D79802F4E4E6202A3893,
	DSPCommandBlockInternal_Internal_DisconnectByHandle_mEEFF5BEF6440EDE4FD470F8D2EFD985C24AED724,
	DSPCommandBlockInternal_Internal_SetAttenuation_m4292E37733553E6E4BDCA61B60A6EA515A10E5B0,
	DSPCommandBlockInternal_Internal_AddAttenuationKey_m53CF3B9C8F8E10BB7C423A4BE86E8D260A2B5A97,
	DSPCommandBlockInternal_Internal_SustainAttenuation_m1EA00E4B260E23EF2336D75119403DC89E31ABE7,
	DSPCommandBlockInternal_Internal_AddInletPort_mB87A548C9E61181DF109B8026228D4C519BC5471,
	DSPCommandBlockInternal_Internal_AddOutletPort_mDCA8B2779EEDDCD6C32EC5DB1220EE99C3E8B917,
	DSPCommandBlockInternal_Internal_SetSampleProvider_m60DDBDF9A0577CA1F61D65FE4F348BE53CF368AE,
	DSPCommandBlockInternal_Internal_InsertSampleProvider_mC7823CF15825F7DD9DF57E00867129C279BE8AE4,
	DSPCommandBlockInternal_Internal_RemoveSampleProvider_mFF427348FD2793A350A11C1D383256FCD1573A2F,
	DSPCommandBlockInternal_Internal_Complete_mC729AD7209B1FBA59BDB2589B2C83F247EB5F8B2,
	DSPCommandBlockInternal_Internal_Cancel_m152036F83982FA1BACAC4FFF89EED12282E0E6DB,
	DSPGraphInternal_Internal_CreateDSPGraph_m72A230BE10087CF328C66141B467D09B55A9BABF,
	DSPGraphInternal_Internal_DisposeDSPGraph_mCDCAF8DEF12C5F6DDD621BD71C966040CF2A8F46,
	DSPGraphInternal_Internal_CreateDSPCommandBlock_mFA2BC21F3A834F1B1E3F10BAB6526354CCEA6F6E,
	DSPGraphInternal_Internal_AddNodeEventHandler_m188CB69F74DAD2564F4E9D09541D1DD9B74077C5,
	DSPGraphInternal_Internal_RemoveNodeEventHandler_mD5B5A6D981EBD95555C69371D4997B46DEF6CD46,
	DSPGraphInternal_Internal_GetRootDSP_m1B8CDAFAF7DA9D5C978BCC82141B800EC5258CDE,
	DSPGraphInternal_Internal_GetDSPClock_mE5ED88200F2CABB64145D79795A6B272048C9C50,
	DSPGraphInternal_Internal_BeginMix_m74026FE70451C061050B5F3734FEBDB23723E76E,
	DSPGraphInternal_Internal_ReadMix_mFB711B685105375970217979CE01E8405F7405E6,
	DSPGraphInternal_Internal_Update_mD60D6CF4E6AB1E62281A4CE9AF4F7E70B4B6EC72,
	DSPGraphInternal_Internal_AssertMixerThread_mB04CB56D118E4565A43408939143F7FD0F06D3B2,
	DSPGraphInternal_Internal_AssertMainThread_m9410E67771DB59819D755C4CCC208677D7A88CC1,
	DSPGraphInternal_Internal_AllocateHandle_m69E97B3223CAA4EFFE358A028F59BE32A923A1F2,
	DSPGraphInternal_Internal_InitializeJob_m1B01207BAFBF94072EB90293CF19F0D89F3F1EB6,
	DSPGraphInternal_Internal_ExecuteJob_mF6669FC184BC1FAE03CB67DA4EFF5A15E3904F90,
	DSPGraphInternal_Internal_ExecuteUpdateJob_mE122CCFC7CB11D1C086470598A93FEDB367384EB,
	DSPGraphInternal_Internal_DisposeJob_mCCC34DC360E1875DE1151A69688E4759FA135E84,
	DSPGraphInternal_Internal_ScheduleGraph_m2930B5BB4FA10E217AC3250854D12825DBC580C0,
	DSPGraphInternal_Internal_SyncFenceNoWorkSteal_m516B51B2F645FC5F2B685BFA1590F75CA740BE5E,
	DSPGraphInternal_Internal_AllocateHandle_Injected_m7F7A2944D3B491E2E1817791BE54847C42781C2F,
	DSPGraphInternal_Internal_ScheduleGraph_Injected_m6506ABB603478F0FFB88B42FBA8769E48D381625,
	DSPGraphInternal_Internal_SyncFenceNoWorkSteal_Injected_mEB21161C8F3495DC93952ACDEEB8D6C1A670BAFB,
	DSPNodeUpdateRequestHandleInternal_Internal_GetUpdateJobData_m0128B820FAC1845E701B941E7EF0ED111EBB650F,
	DSPNodeUpdateRequestHandleInternal_Internal_HasError_mCD6B9073C26ECF79028D14CB4653D87D485237BE,
	DSPNodeUpdateRequestHandleInternal_Internal_GetDSPNode_mB3A8DCAE003DC7D7F8FD4438BD5EA58DE0FE82BC,
	DSPNodeUpdateRequestHandleInternal_Internal_GetFence_mC49E96BD64E8B0A4105A12630E552E1E57CC6155,
	DSPNodeUpdateRequestHandleInternal_Internal_Dispose_m471B6C9D926E3060B016B948E6D76BD191D11FA6,
	DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProvider_m452593870CB755E994B7AD42D8C2DE192480B835,
	DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProvider_mBABB781CA231ED433404E196767651697022D5A2,
	DSPSampleProviderInternal_Internal_ReadFloatFromSampleProvider_m214E1C1E2D627E77536EFAC67C228912D3A6A0D0,
	DSPSampleProviderInternal_Internal_GetChannelCount_m0F11CAAD6596B88F7E56BDABDA99E1F21D3EDC8A,
	DSPSampleProviderInternal_Internal_GetSampleRate_m679B37C8351CADEA6FF53F99B5A71725416D2B38,
	DSPSampleProviderInternal_Internal_ReadUInt8FromSampleProviderById_m1B5A33CD09C3CBC636E5E1BC2C1381DB8346B605,
	DSPSampleProviderInternal_Internal_ReadSInt16FromSampleProviderById_m1C3099A8576A8D28FD89BAFF4397FF937F18FEEA,
	DSPSampleProviderInternal_Internal_ReadFloatFromSampleProviderById_m585D8447ECFDDE270DE19750436DEC7A88A515D8,
	DSPSampleProviderInternal_Internal_GetChannelCountById_mF9B7C00D8DFB78E5F40C8BA0D3E6BA75BE00DB63,
	DSPSampleProviderInternal_Internal_GetSampleRateById_m52C797917E17C7E639BBC8CE8A65C251473C3924,
	ExecuteContextInternal_Internal_PostEvent_m85AEE83FF62AF2694A6153A056AB8C711BE7E685,
};
extern void Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7_AdjustorThunk (void);
extern void Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A_AdjustorThunk (void);
extern void Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB_AdjustorThunk (void);
extern void Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_AdjustorThunk (void);
extern void Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_AdjustorThunk (void);
extern void Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B_AdjustorThunk (void);
extern void Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554_AdjustorThunk (void);
extern void Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB_AdjustorThunk (void);
extern void Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A_AdjustorThunk (void);
extern void Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E_AdjustorThunk (void);
extern void Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[11] = 
{
	{ 0x06000001, Handle_get_AtomicNode_m521D6E15C25DE324086A28774AE8F0B143114FA7_AdjustorThunk },
	{ 0x06000002, Handle_set_AtomicNode_m7F2857C219B79E10A82493DCD55799611B86CE2A_AdjustorThunk },
	{ 0x06000003, Handle_get_Id_mAF83AE0F0BB5E34AAAC4A5E7816361BEB91550EB_AdjustorThunk },
	{ 0x06000004, Handle_set_Id_m5F626922A5BABC922164F9CC11C489758F2FF0FC_AdjustorThunk },
	{ 0x06000005, Handle__ctor_m5F5992912B5EAE11DAC8E6FD0F918307EC7EBEF4_AdjustorThunk },
	{ 0x06000006, Handle_FlushNode_m23A5F76AC30A50A751CE5431D1ABF2FEE414CA8B_AdjustorThunk },
	{ 0x06000007, Handle_Equals_mF3DE2FC53714EE9223B0CC9F76A1D660F2C07554_AdjustorThunk },
	{ 0x06000008, Handle_Equals_mEB6AC1B0082519D3BCDF585957ED86AE211D04EB_AdjustorThunk },
	{ 0x06000009, Handle_GetHashCode_m45D64D928D42831ADB9609E111DAC43536A4B68A_AdjustorThunk },
	{ 0x0600000A, Handle_get_Valid_m0D578E8C201BCF86E6A57ED9CAD975E59E511B8E_AdjustorThunk },
	{ 0x0600000B, Handle_get_Alive_mB2F4003657A12E2FD25EB623E92AA8A4F6363FEC_AdjustorThunk },
};
static const int32_t s_InvokerIndices[73] = 
{
	4151,
	3788,
	4216,
	3852,
	3788,
	4364,
	3157,
	3185,
	4216,
	4168,
	4168,
	7104,
	8867,
	6845,
	8867,
	4570,
	4824,
	4825,
	5062,
	5058,
	4668,
	6845,
	4826,
	5063,
	6845,
	5060,
	5069,
	6069,
	5460,
	5460,
	4827,
	4827,
	5460,
	7894,
	7894,
	5489,
	8867,
	7894,
	6808,
	7147,
	7894,
	8799,
	6861,
	6847,
	8867,
	8200,
	8200,
	8356,
	6845,
	6066,
	4822,
	6845,
	5527,
	8883,
	7894,
	5462,
	8867,
	7098,
	7140,
	6845,
	6845,
	7894,
	5781,
	5781,
	6480,
	8763,
	8779,
	5840,
	5840,
	6550,
	8777,
	8791,
	6086,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_DSPGraphModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_DSPGraphModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_DSPGraphModule_CodeGenModule = 
{
	"UnityEngine.DSPGraphModule.dll",
	73,
	s_methodPointers,
	11,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_DSPGraphModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
