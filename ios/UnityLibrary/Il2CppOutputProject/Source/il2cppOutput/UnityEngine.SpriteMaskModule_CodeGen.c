﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void SpriteMask_get_frontSortingLayerID_mCDFA70861C17DE5BC7FC178DF5FFD1E7BEE12B3E (void);
extern void SpriteMask_set_frontSortingLayerID_m023C6504B9363E89DC2528FA83D82208192E8198 (void);
extern void SpriteMask_get_frontSortingOrder_m9F4B657A7E0C3BDC5344C8FD3EAC4B3A04D43979 (void);
extern void SpriteMask_set_frontSortingOrder_mBB6C461477D2D11F95CC0CB7398BC8A3AD3E98BC (void);
extern void SpriteMask_get_backSortingLayerID_m0A888DD1A63A9AE95D8EDE5C92F106C3669F963D (void);
extern void SpriteMask_set_backSortingLayerID_m9DB8BA104EF7F184A3AA55B6DCDDCBF40E103D7B (void);
extern void SpriteMask_get_backSortingOrder_m47CF66F8F8C4098B438106A423D45BD805EF11DB (void);
extern void SpriteMask_set_backSortingOrder_m383FEC8838B334E961536C2477B80437CF401839 (void);
extern void SpriteMask_get_alphaCutoff_m4994EFE29AEEA861928DDD627823DE1F349C8CF9 (void);
extern void SpriteMask_set_alphaCutoff_m7D7BBA4C83CA779FFCFEDDC92773E3639197DBF2 (void);
extern void SpriteMask_get_sprite_m56DE2BB449F7A3E0F01373E390B64755ACA121A7 (void);
extern void SpriteMask_set_sprite_mF086ACFDAB47F00285BE7D2D17EF5E2F9128EB84 (void);
extern void SpriteMask_get_isCustomRangeActive_mA0DFDC2307128FAEC2C1B0B9B6B5D382A40CD115 (void);
extern void SpriteMask_set_isCustomRangeActive_mDE57CE93CF78D45E83BDF3F4DF6330D5EE778F3D (void);
extern void SpriteMask_get_spriteSortPoint_m84E047D2BE64E2A4DF3C193DDCF6466743FEA7C3 (void);
extern void SpriteMask_set_spriteSortPoint_m2CF60E48A89F305EC0DB1E87C05676801F81CB1F (void);
extern void SpriteMask_GetSpriteBounds_mBE1ED797A95FF5F952CFB718ACFC279151530B52 (void);
extern void SpriteMask__ctor_mE8AD60D949B00F8FF8DEFCB43563FEFEF86C612D (void);
extern void SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01 (void);
extern void SpriteMaskUtility_HasSpriteMaskInLayerRange_m7E522D077F4992310FECE3D2911B0C1EE1F72F6B (void);
extern void SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD (void);
static Il2CppMethodPointer s_methodPointers[21] = 
{
	SpriteMask_get_frontSortingLayerID_mCDFA70861C17DE5BC7FC178DF5FFD1E7BEE12B3E,
	SpriteMask_set_frontSortingLayerID_m023C6504B9363E89DC2528FA83D82208192E8198,
	SpriteMask_get_frontSortingOrder_m9F4B657A7E0C3BDC5344C8FD3EAC4B3A04D43979,
	SpriteMask_set_frontSortingOrder_mBB6C461477D2D11F95CC0CB7398BC8A3AD3E98BC,
	SpriteMask_get_backSortingLayerID_m0A888DD1A63A9AE95D8EDE5C92F106C3669F963D,
	SpriteMask_set_backSortingLayerID_m9DB8BA104EF7F184A3AA55B6DCDDCBF40E103D7B,
	SpriteMask_get_backSortingOrder_m47CF66F8F8C4098B438106A423D45BD805EF11DB,
	SpriteMask_set_backSortingOrder_m383FEC8838B334E961536C2477B80437CF401839,
	SpriteMask_get_alphaCutoff_m4994EFE29AEEA861928DDD627823DE1F349C8CF9,
	SpriteMask_set_alphaCutoff_m7D7BBA4C83CA779FFCFEDDC92773E3639197DBF2,
	SpriteMask_get_sprite_m56DE2BB449F7A3E0F01373E390B64755ACA121A7,
	SpriteMask_set_sprite_mF086ACFDAB47F00285BE7D2D17EF5E2F9128EB84,
	SpriteMask_get_isCustomRangeActive_mA0DFDC2307128FAEC2C1B0B9B6B5D382A40CD115,
	SpriteMask_set_isCustomRangeActive_mDE57CE93CF78D45E83BDF3F4DF6330D5EE778F3D,
	SpriteMask_get_spriteSortPoint_m84E047D2BE64E2A4DF3C193DDCF6466743FEA7C3,
	SpriteMask_set_spriteSortPoint_m2CF60E48A89F305EC0DB1E87C05676801F81CB1F,
	SpriteMask_GetSpriteBounds_mBE1ED797A95FF5F952CFB718ACFC279151530B52,
	SpriteMask__ctor_mE8AD60D949B00F8FF8DEFCB43563FEFEF86C612D,
	SpriteMask_GetSpriteBounds_Injected_mDDDA1DB7A90554A6156057C1CF8C95FA27A2EE01,
	SpriteMaskUtility_HasSpriteMaskInLayerRange_m7E522D077F4992310FECE3D2911B0C1EE1F72F6B,
	SpriteMaskUtility_HasSpriteMaskInLayerRange_Injected_m2C4AA54A7B3110F8F11DEEA22FBA36A1CC77B7CD,
};
static const int32_t s_InvokerIndices[21] = 
{
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4250,
	3881,
	4168,
	3807,
	4216,
	3852,
	4166,
	4364,
	3788,
	8235,
	8200,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SpriteMaskModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SpriteMaskModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SpriteMaskModule_CodeGenModule = 
{
	"UnityEngine.SpriteMaskModule.dll",
	21,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_SpriteMaskModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
