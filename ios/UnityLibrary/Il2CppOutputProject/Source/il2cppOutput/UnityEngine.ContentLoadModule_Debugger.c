﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = 
{
	{ 25210, 0,  6 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = 
{
	"status",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[66] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_ContentLoadModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ContentLoadModule[204] = 
{
	{ 109371, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109371, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109371, 1, 29, 29, 39, 40, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109371, 1, 29, 29, 41, 102, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109371, 1, 29, 29, 41, 102, 7, kSequencePointKind_StepOut, 0, 4 },
	{ 109371, 1, 29, 29, 103, 104, 15, kSequencePointKind_Normal, 0, 5 },
	{ 109372, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 109372, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 109372, 1, 30, 30, 54, 55, 0, kSequencePointKind_Normal, 0, 8 },
	{ 109372, 1, 30, 30, 56, 123, 1, kSequencePointKind_Normal, 0, 9 },
	{ 109372, 1, 30, 30, 56, 123, 8, kSequencePointKind_StepOut, 0, 10 },
	{ 109372, 1, 30, 30, 124, 125, 16, kSequencePointKind_Normal, 0, 11 },
	{ 109373, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 109373, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 109373, 1, 40, 40, 9, 10, 0, kSequencePointKind_Normal, 0, 14 },
	{ 109373, 1, 41, 41, 13, 36, 1, kSequencePointKind_Normal, 0, 15 },
	{ 109373, 1, 41, 41, 13, 36, 2, kSequencePointKind_StepOut, 0, 16 },
	{ 109373, 1, 42, 42, 13, 64, 8, kSequencePointKind_Normal, 0, 17 },
	{ 109373, 1, 42, 42, 13, 64, 14, kSequencePointKind_StepOut, 0, 18 },
	{ 109373, 1, 43, 43, 13, 62, 20, kSequencePointKind_Normal, 0, 19 },
	{ 109373, 1, 44, 44, 9, 10, 45, kSequencePointKind_Normal, 0, 20 },
	{ 109374, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 21 },
	{ 109374, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 22 },
	{ 109374, 1, 47, 47, 9, 10, 0, kSequencePointKind_Normal, 0, 23 },
	{ 109374, 1, 48, 48, 13, 34, 1, kSequencePointKind_Normal, 0, 24 },
	{ 109374, 1, 48, 48, 13, 34, 2, kSequencePointKind_StepOut, 0, 25 },
	{ 109374, 1, 49, 49, 13, 70, 8, kSequencePointKind_Normal, 0, 26 },
	{ 109374, 1, 49, 49, 13, 70, 14, kSequencePointKind_StepOut, 0, 27 },
	{ 109374, 1, 50, 50, 9, 10, 22, kSequencePointKind_Normal, 0, 28 },
	{ 109375, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 29 },
	{ 109375, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 30 },
	{ 109375, 1, 53, 53, 9, 10, 0, kSequencePointKind_Normal, 0, 31 },
	{ 109375, 1, 54, 54, 13, 34, 1, kSequencePointKind_Normal, 0, 32 },
	{ 109375, 1, 54, 54, 13, 34, 2, kSequencePointKind_StepOut, 0, 33 },
	{ 109375, 1, 55, 55, 13, 92, 8, kSequencePointKind_Normal, 0, 34 },
	{ 109375, 1, 55, 55, 13, 92, 15, kSequencePointKind_StepOut, 0, 35 },
	{ 109375, 1, 56, 56, 9, 10, 23, kSequencePointKind_Normal, 0, 36 },
	{ 109376, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 37 },
	{ 109376, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 38 },
	{ 109376, 1, 59, 59, 9, 10, 0, kSequencePointKind_Normal, 0, 39 },
	{ 109376, 1, 60, 60, 13, 26, 1, kSequencePointKind_Normal, 0, 40 },
	{ 109376, 1, 60, 60, 13, 26, 2, kSequencePointKind_StepOut, 0, 41 },
	{ 109376, 1, 60, 60, 0, 0, 11, kSequencePointKind_Normal, 0, 42 },
	{ 109376, 1, 61, 61, 17, 144, 14, kSequencePointKind_Normal, 0, 43 },
	{ 109376, 1, 61, 61, 17, 144, 19, kSequencePointKind_StepOut, 0, 44 },
	{ 109376, 1, 62, 62, 9, 10, 25, kSequencePointKind_Normal, 0, 45 },
	{ 109377, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 46 },
	{ 109377, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 47 },
	{ 109377, 1, 65, 65, 9, 10, 0, kSequencePointKind_Normal, 0, 48 },
	{ 109377, 1, 66, 66, 13, 50, 1, kSequencePointKind_Normal, 0, 49 },
	{ 109377, 1, 66, 66, 13, 50, 2, kSequencePointKind_StepOut, 0, 50 },
	{ 109377, 1, 67, 67, 13, 48, 8, kSequencePointKind_Normal, 0, 51 },
	{ 109377, 1, 67, 67, 0, 0, 13, kSequencePointKind_Normal, 0, 52 },
	{ 109377, 1, 68, 68, 17, 83, 16, kSequencePointKind_Normal, 0, 53 },
	{ 109377, 1, 68, 68, 17, 83, 21, kSequencePointKind_StepOut, 0, 54 },
	{ 109377, 1, 69, 69, 13, 52, 27, kSequencePointKind_Normal, 0, 55 },
	{ 109377, 1, 69, 69, 0, 0, 32, kSequencePointKind_Normal, 0, 56 },
	{ 109377, 1, 70, 70, 17, 117, 35, kSequencePointKind_Normal, 0, 57 },
	{ 109377, 1, 70, 70, 17, 117, 40, kSequencePointKind_StepOut, 0, 58 },
	{ 109377, 1, 71, 71, 9, 10, 46, kSequencePointKind_Normal, 0, 59 },
	{ 109378, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 60 },
	{ 109378, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 61 },
	{ 109378, 1, 73, 73, 35, 36, 0, kSequencePointKind_Normal, 0, 62 },
	{ 109378, 1, 73, 73, 37, 97, 1, kSequencePointKind_Normal, 0, 63 },
	{ 109378, 1, 73, 73, 37, 97, 7, kSequencePointKind_StepOut, 0, 64 },
	{ 109378, 1, 73, 73, 98, 99, 15, kSequencePointKind_Normal, 0, 65 },
	{ 109379, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 66 },
	{ 109379, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 67 },
	{ 109379, 1, 76, 76, 17, 18, 0, kSequencePointKind_Normal, 0, 68 },
	{ 109379, 1, 77, 77, 17, 40, 1, kSequencePointKind_Normal, 0, 69 },
	{ 109379, 1, 77, 77, 17, 40, 2, kSequencePointKind_StepOut, 0, 70 },
	{ 109379, 1, 78, 78, 17, 80, 8, kSequencePointKind_Normal, 0, 71 },
	{ 109379, 1, 78, 78, 17, 80, 14, kSequencePointKind_StepOut, 0, 72 },
	{ 109379, 1, 79, 79, 13, 14, 22, kSequencePointKind_Normal, 0, 73 },
	{ 109380, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 74 },
	{ 109380, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 75 },
	{ 109380, 1, 83, 83, 9, 10, 0, kSequencePointKind_Normal, 0, 76 },
	{ 109380, 1, 84, 84, 13, 36, 1, kSequencePointKind_Normal, 0, 77 },
	{ 109380, 1, 84, 84, 13, 36, 2, kSequencePointKind_StepOut, 0, 78 },
	{ 109380, 1, 85, 85, 13, 80, 8, kSequencePointKind_Normal, 0, 79 },
	{ 109380, 1, 85, 85, 13, 80, 15, kSequencePointKind_StepOut, 0, 80 },
	{ 109380, 1, 86, 86, 9, 10, 23, kSequencePointKind_Normal, 0, 81 },
	{ 109381, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 82 },
	{ 109381, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 83 },
	{ 109381, 1, 90, 90, 17, 18, 0, kSequencePointKind_Normal, 0, 84 },
	{ 109381, 1, 90, 90, 19, 102, 1, kSequencePointKind_Normal, 0, 85 },
	{ 109381, 1, 90, 90, 103, 104, 22, kSequencePointKind_Normal, 0, 86 },
	{ 109382, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 87 },
	{ 109382, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 88 },
	{ 109382, 1, 113, 113, 50, 51, 0, kSequencePointKind_Normal, 0, 89 },
	{ 109382, 1, 113, 113, 52, 75, 1, kSequencePointKind_Normal, 0, 90 },
	{ 109382, 1, 113, 113, 76, 77, 10, kSequencePointKind_Normal, 0, 91 },
	{ 109383, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 92 },
	{ 109383, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 93 },
	{ 109383, 1, 113, 113, 82, 83, 0, kSequencePointKind_Normal, 0, 94 },
	{ 109383, 1, 113, 113, 84, 108, 1, kSequencePointKind_Normal, 0, 95 },
	{ 109383, 1, 113, 113, 109, 110, 8, kSequencePointKind_Normal, 0, 96 },
	{ 109384, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 97 },
	{ 109384, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 98 },
	{ 109384, 1, 114, 114, 56, 57, 0, kSequencePointKind_Normal, 0, 99 },
	{ 109384, 1, 114, 114, 58, 84, 1, kSequencePointKind_Normal, 0, 100 },
	{ 109384, 1, 114, 114, 85, 86, 10, kSequencePointKind_Normal, 0, 101 },
	{ 109385, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 102 },
	{ 109385, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 103 },
	{ 109385, 1, 114, 114, 91, 92, 0, kSequencePointKind_Normal, 0, 104 },
	{ 109385, 1, 114, 114, 93, 120, 1, kSequencePointKind_Normal, 0, 105 },
	{ 109385, 1, 114, 114, 121, 122, 8, kSequencePointKind_Normal, 0, 106 },
	{ 109386, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 107 },
	{ 109386, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 108 },
	{ 109386, 1, 115, 115, 41, 42, 0, kSequencePointKind_Normal, 0, 109 },
	{ 109386, 1, 115, 115, 43, 66, 1, kSequencePointKind_Normal, 0, 110 },
	{ 109386, 1, 115, 115, 67, 68, 10, kSequencePointKind_Normal, 0, 111 },
	{ 109387, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 112 },
	{ 109387, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 113 },
	{ 109387, 1, 115, 115, 73, 74, 0, kSequencePointKind_Normal, 0, 114 },
	{ 109387, 1, 115, 115, 75, 99, 1, kSequencePointKind_Normal, 0, 115 },
	{ 109387, 1, 115, 115, 100, 101, 8, kSequencePointKind_Normal, 0, 116 },
	{ 109388, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 117 },
	{ 109388, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 118 },
	{ 109388, 1, 122, 122, 34, 35, 0, kSequencePointKind_Normal, 0, 119 },
	{ 109388, 1, 122, 122, 36, 59, 1, kSequencePointKind_Normal, 0, 120 },
	{ 109388, 1, 122, 122, 36, 59, 2, kSequencePointKind_StepOut, 0, 121 },
	{ 109388, 1, 122, 122, 60, 120, 8, kSequencePointKind_Normal, 0, 122 },
	{ 109388, 1, 122, 122, 60, 120, 14, kSequencePointKind_StepOut, 0, 123 },
	{ 109388, 1, 122, 122, 121, 122, 22, kSequencePointKind_Normal, 0, 124 },
	{ 109389, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 125 },
	{ 109389, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 126 },
	{ 109389, 1, 124, 124, 45, 46, 0, kSequencePointKind_Normal, 0, 127 },
	{ 109389, 1, 124, 124, 47, 70, 1, kSequencePointKind_Normal, 0, 128 },
	{ 109389, 1, 124, 124, 47, 70, 2, kSequencePointKind_StepOut, 0, 129 },
	{ 109389, 1, 124, 124, 71, 137, 8, kSequencePointKind_Normal, 0, 130 },
	{ 109389, 1, 124, 124, 71, 137, 14, kSequencePointKind_StepOut, 0, 131 },
	{ 109389, 1, 124, 124, 138, 139, 20, kSequencePointKind_Normal, 0, 132 },
	{ 109390, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 133 },
	{ 109390, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 134 },
	{ 109390, 1, 125, 125, 48, 49, 0, kSequencePointKind_Normal, 0, 135 },
	{ 109390, 1, 125, 125, 50, 73, 1, kSequencePointKind_Normal, 0, 136 },
	{ 109390, 1, 125, 125, 50, 73, 2, kSequencePointKind_StepOut, 0, 137 },
	{ 109390, 1, 125, 125, 74, 135, 8, kSequencePointKind_Normal, 0, 138 },
	{ 109390, 1, 125, 125, 74, 135, 14, kSequencePointKind_StepOut, 0, 139 },
	{ 109390, 1, 125, 125, 136, 137, 22, kSequencePointKind_Normal, 0, 140 },
	{ 109391, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 141 },
	{ 109391, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 142 },
	{ 109391, 1, 127, 127, 42, 43, 0, kSequencePointKind_Normal, 0, 143 },
	{ 109391, 1, 127, 127, 44, 67, 1, kSequencePointKind_Normal, 0, 144 },
	{ 109391, 1, 127, 127, 44, 67, 2, kSequencePointKind_StepOut, 0, 145 },
	{ 109391, 1, 127, 127, 68, 138, 8, kSequencePointKind_Normal, 0, 146 },
	{ 109391, 1, 127, 127, 68, 138, 14, kSequencePointKind_StepOut, 0, 147 },
	{ 109391, 1, 127, 127, 139, 140, 22, kSequencePointKind_Normal, 0, 148 },
	{ 109392, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 149 },
	{ 109392, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 150 },
	{ 109392, 1, 128, 128, 58, 59, 0, kSequencePointKind_Normal, 0, 151 },
	{ 109392, 1, 128, 128, 60, 83, 1, kSequencePointKind_Normal, 0, 152 },
	{ 109392, 1, 128, 128, 60, 83, 2, kSequencePointKind_StepOut, 0, 153 },
	{ 109392, 1, 128, 128, 84, 164, 8, kSequencePointKind_Normal, 0, 154 },
	{ 109392, 1, 128, 128, 84, 164, 15, kSequencePointKind_StepOut, 0, 155 },
	{ 109392, 1, 128, 128, 165, 166, 23, kSequencePointKind_Normal, 0, 156 },
	{ 109393, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 157 },
	{ 109393, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 158 },
	{ 109393, 1, 130, 130, 35, 36, 0, kSequencePointKind_Normal, 0, 159 },
	{ 109393, 1, 130, 130, 37, 102, 1, kSequencePointKind_Normal, 0, 160 },
	{ 109393, 1, 130, 130, 37, 102, 7, kSequencePointKind_StepOut, 0, 161 },
	{ 109393, 1, 130, 130, 103, 104, 15, kSequencePointKind_Normal, 0, 162 },
	{ 109394, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 163 },
	{ 109394, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 164 },
	{ 109394, 1, 133, 133, 9, 10, 0, kSequencePointKind_Normal, 0, 165 },
	{ 109394, 1, 134, 134, 13, 26, 1, kSequencePointKind_Normal, 0, 166 },
	{ 109394, 1, 134, 134, 13, 26, 2, kSequencePointKind_StepOut, 0, 167 },
	{ 109394, 1, 134, 134, 0, 0, 11, kSequencePointKind_Normal, 0, 168 },
	{ 109394, 1, 135, 135, 17, 149, 14, kSequencePointKind_Normal, 0, 169 },
	{ 109394, 1, 135, 135, 17, 149, 19, kSequencePointKind_StepOut, 0, 170 },
	{ 109394, 1, 136, 136, 9, 10, 25, kSequencePointKind_Normal, 0, 171 },
	{ 109413, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 172 },
	{ 109413, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 173 },
	{ 109413, 1, 179, 179, 9, 10, 0, kSequencePointKind_Normal, 0, 174 },
	{ 109413, 1, 181, 181, 13, 14, 1, kSequencePointKind_Normal, 0, 175 },
	{ 109413, 1, 182, 182, 17, 158, 2, kSequencePointKind_Normal, 0, 176 },
	{ 109413, 1, 182, 182, 17, 158, 15, kSequencePointKind_StepOut, 0, 177 },
	{ 109413, 1, 182, 182, 17, 158, 22, kSequencePointKind_StepOut, 0, 178 },
	{ 109413, 1, 184, 184, 9, 10, 30, kSequencePointKind_Normal, 0, 179 },
	{ 109414, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 180 },
	{ 109414, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 181 },
	{ 109414, 1, 188, 188, 9, 10, 0, kSequencePointKind_Normal, 0, 182 },
	{ 109414, 1, 190, 190, 13, 14, 1, kSequencePointKind_Normal, 0, 183 },
	{ 109414, 1, 191, 191, 17, 154, 2, kSequencePointKind_Normal, 0, 184 },
	{ 109414, 1, 191, 191, 17, 154, 12, kSequencePointKind_StepOut, 0, 185 },
	{ 109414, 1, 191, 191, 17, 154, 19, kSequencePointKind_StepOut, 0, 186 },
	{ 109414, 1, 193, 193, 9, 10, 27, kSequencePointKind_Normal, 0, 187 },
	{ 109417, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 188 },
	{ 109417, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 189 },
	{ 109417, 1, 199, 199, 9, 10, 0, kSequencePointKind_Normal, 0, 190 },
	{ 109417, 1, 200, 200, 13, 39, 1, kSequencePointKind_Normal, 0, 191 },
	{ 109417, 1, 200, 200, 13, 39, 1, kSequencePointKind_StepOut, 0, 192 },
	{ 109417, 1, 201, 201, 9, 10, 9, kSequencePointKind_Normal, 0, 193 },
	{ 109418, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 194 },
	{ 109418, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 195 },
	{ 109418, 1, 204, 204, 9, 10, 0, kSequencePointKind_Normal, 0, 196 },
	{ 109418, 1, 205, 205, 13, 40, 1, kSequencePointKind_Normal, 0, 197 },
	{ 109418, 1, 205, 205, 0, 0, 13, kSequencePointKind_Normal, 0, 198 },
	{ 109418, 1, 206, 206, 17, 142, 16, kSequencePointKind_Normal, 0, 199 },
	{ 109418, 1, 206, 206, 17, 142, 26, kSequencePointKind_StepOut, 0, 200 },
	{ 109418, 1, 208, 208, 13, 52, 32, kSequencePointKind_Normal, 0, 201 },
	{ 109418, 1, 208, 208, 13, 52, 33, kSequencePointKind_StepOut, 0, 202 },
	{ 109418, 1, 209, 209, 9, 10, 39, kSequencePointKind_Normal, 0, 203 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_ContentLoadModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_ContentLoadModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/ContentLoad/Public/ContentLoad.bindings.cs", { 84, 49, 12, 56, 210, 145, 210, 173, 22, 117, 142, 50, 161, 22, 231, 37} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[5] = 
{
	{ 14053, 1 },
	{ 14054, 1 },
	{ 14056, 1 },
	{ 14057, 1 },
	{ 14058, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[24] = 
{
	{ 0, 17 },
	{ 0, 18 },
	{ 0, 47 },
	{ 0, 24 },
	{ 0, 25 },
	{ 0, 26 },
	{ 0, 47 },
	{ 0, 17 },
	{ 0, 24 },
	{ 0, 25 },
	{ 0, 24 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 24 },
	{ 0, 24 },
	{ 0, 24 },
	{ 0, 25 },
	{ 0, 17 },
	{ 0, 26 },
	{ 0, 32 },
	{ 0, 29 },
	{ 0, 11 },
	{ 0, 40 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[66] = 
{
	{ 17, 0, 1 },
	{ 18, 1, 1 },
	{ 47, 2, 1 },
	{ 24, 3, 1 },
	{ 25, 4, 1 },
	{ 26, 5, 1 },
	{ 47, 6, 1 },
	{ 17, 7, 1 },
	{ 24, 8, 1 },
	{ 25, 9, 1 },
	{ 24, 10, 1 },
	{ 12, 11, 1 },
	{ 0, 0, 0 },
	{ 12, 12, 1 },
	{ 0, 0, 0 },
	{ 12, 13, 1 },
	{ 0, 0, 0 },
	{ 24, 14, 1 },
	{ 0, 0, 0 },
	{ 24, 15, 1 },
	{ 24, 16, 1 },
	{ 25, 17, 1 },
	{ 17, 18, 1 },
	{ 26, 19, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 32, 20, 1 },
	{ 29, 21, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 11, 22, 1 },
	{ 40, 23, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ContentLoadModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ContentLoadModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	204,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_ContentLoadModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	5,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
