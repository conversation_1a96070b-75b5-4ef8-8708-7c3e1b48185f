﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[10] = 
{
	{ 27119, 0,  0 },
	{ 31606, 1,  0 },
	{ 27119, 0,  1 },
	{ 20711, 2,  12 },
	{ 20711, 2,  14 },
	{ 30895, 3,  15 },
	{ 31603, 4,  15 },
	{ 18407, 5,  15 },
	{ 24489, 6,  17 },
	{ 18407, 5,  26 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[7] = 
{
	"handle",
	"playable",
	"currentDelay",
	"count",
	"mode",
	"provider",
	"maxNumTracks",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[232] = 
{
	{ 0, 2 },
	{ 2, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 3, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 4, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 5, 3 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 8, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 9, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_VideoModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VideoModule[371] = 
{
	{ 108092, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 108092, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 108092, 1, 25, 25, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 108092, 1, 26, 26, 13, 61, 1, kSequencePointKind_Normal, 0, 3 },
	{ 108092, 1, 26, 26, 13, 61, 4, kSequencePointKind_StepOut, 0, 4 },
	{ 108092, 1, 27, 27, 13, 58, 10, kSequencePointKind_Normal, 0, 5 },
	{ 108092, 1, 27, 27, 13, 58, 13, kSequencePointKind_StepOut, 0, 6 },
	{ 108092, 1, 28, 28, 13, 30, 18, kSequencePointKind_Normal, 0, 7 },
	{ 108092, 1, 28, 28, 13, 30, 20, kSequencePointKind_StepOut, 0, 8 },
	{ 108092, 1, 28, 28, 0, 0, 26, kSequencePointKind_Normal, 0, 9 },
	{ 108092, 1, 29, 29, 17, 51, 29, kSequencePointKind_Normal, 0, 10 },
	{ 108092, 1, 29, 29, 17, 51, 31, kSequencePointKind_StepOut, 0, 11 },
	{ 108092, 1, 29, 29, 17, 51, 36, kSequencePointKind_StepOut, 0, 12 },
	{ 108092, 1, 30, 30, 13, 29, 42, kSequencePointKind_Normal, 0, 13 },
	{ 108092, 1, 31, 31, 9, 10, 46, kSequencePointKind_Normal, 0, 14 },
	{ 108093, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 15 },
	{ 108093, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 16 },
	{ 108093, 1, 34, 34, 9, 10, 0, kSequencePointKind_Normal, 0, 17 },
	{ 108093, 1, 35, 35, 13, 57, 1, kSequencePointKind_Normal, 0, 18 },
	{ 108093, 1, 35, 35, 13, 57, 1, kSequencePointKind_StepOut, 0, 19 },
	{ 108093, 1, 36, 36, 13, 88, 7, kSequencePointKind_Normal, 0, 20 },
	{ 108093, 1, 36, 36, 13, 88, 13, kSequencePointKind_StepOut, 0, 21 },
	{ 108093, 1, 36, 36, 0, 0, 22, kSequencePointKind_Normal, 0, 22 },
	{ 108093, 1, 37, 37, 17, 44, 25, kSequencePointKind_Normal, 0, 23 },
	{ 108093, 1, 37, 37, 17, 44, 25, kSequencePointKind_StepOut, 0, 24 },
	{ 108093, 1, 38, 38, 13, 27, 33, kSequencePointKind_Normal, 0, 25 },
	{ 108093, 1, 39, 39, 9, 10, 37, kSequencePointKind_Normal, 0, 26 },
	{ 108094, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 27 },
	{ 108094, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 28 },
	{ 108094, 1, 42, 42, 9, 10, 0, kSequencePointKind_Normal, 0, 29 },
	{ 108094, 1, 43, 43, 13, 34, 1, kSequencePointKind_Normal, 0, 30 },
	{ 108094, 1, 43, 43, 13, 34, 3, kSequencePointKind_StepOut, 0, 31 },
	{ 108094, 1, 43, 43, 0, 0, 9, kSequencePointKind_Normal, 0, 32 },
	{ 108094, 1, 44, 44, 13, 14, 12, kSequencePointKind_Normal, 0, 33 },
	{ 108094, 1, 45, 45, 17, 67, 13, kSequencePointKind_Normal, 0, 34 },
	{ 108094, 1, 45, 45, 17, 67, 15, kSequencePointKind_StepOut, 0, 35 },
	{ 108094, 1, 45, 45, 0, 0, 24, kSequencePointKind_Normal, 0, 36 },
	{ 108094, 1, 46, 46, 21, 115, 27, kSequencePointKind_Normal, 0, 37 },
	{ 108094, 1, 46, 46, 21, 115, 32, kSequencePointKind_StepOut, 0, 38 },
	{ 108094, 1, 47, 47, 13, 14, 38, kSequencePointKind_Normal, 0, 39 },
	{ 108094, 1, 49, 49, 13, 31, 39, kSequencePointKind_Normal, 0, 40 },
	{ 108094, 1, 50, 50, 9, 10, 46, kSequencePointKind_Normal, 0, 41 },
	{ 108095, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 42 },
	{ 108095, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 43 },
	{ 108095, 1, 53, 53, 9, 10, 0, kSequencePointKind_Normal, 0, 44 },
	{ 108095, 1, 54, 54, 13, 29, 1, kSequencePointKind_Normal, 0, 45 },
	{ 108095, 1, 55, 55, 9, 10, 10, kSequencePointKind_Normal, 0, 46 },
	{ 108096, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 47 },
	{ 108096, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 48 },
	{ 108096, 1, 58, 58, 9, 10, 0, kSequencePointKind_Normal, 0, 49 },
	{ 108096, 1, 59, 59, 13, 55, 1, kSequencePointKind_Normal, 0, 50 },
	{ 108096, 1, 59, 59, 13, 55, 3, kSequencePointKind_StepOut, 0, 51 },
	{ 108096, 1, 59, 59, 13, 55, 8, kSequencePointKind_StepOut, 0, 52 },
	{ 108096, 1, 60, 60, 9, 10, 16, kSequencePointKind_Normal, 0, 53 },
	{ 108097, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 54 },
	{ 108097, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 55 },
	{ 108097, 1, 63, 63, 9, 10, 0, kSequencePointKind_Normal, 0, 56 },
	{ 108097, 1, 64, 64, 13, 64, 1, kSequencePointKind_Normal, 0, 57 },
	{ 108097, 1, 64, 64, 13, 64, 3, kSequencePointKind_StepOut, 0, 58 },
	{ 108097, 1, 64, 64, 13, 64, 8, kSequencePointKind_StepOut, 0, 59 },
	{ 108097, 1, 65, 65, 9, 10, 16, kSequencePointKind_Normal, 0, 60 },
	{ 108098, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 61 },
	{ 108098, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 62 },
	{ 108098, 1, 68, 68, 9, 10, 0, kSequencePointKind_Normal, 0, 63 },
	{ 108098, 1, 69, 69, 13, 53, 1, kSequencePointKind_Normal, 0, 64 },
	{ 108098, 1, 69, 69, 13, 53, 2, kSequencePointKind_StepOut, 0, 65 },
	{ 108098, 1, 69, 69, 13, 53, 9, kSequencePointKind_StepOut, 0, 66 },
	{ 108098, 1, 69, 69, 13, 53, 14, kSequencePointKind_StepOut, 0, 67 },
	{ 108098, 1, 70, 70, 9, 10, 22, kSequencePointKind_Normal, 0, 68 },
	{ 108099, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 69 },
	{ 108099, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 70 },
	{ 108099, 1, 75, 75, 9, 10, 0, kSequencePointKind_Normal, 0, 71 },
	{ 108099, 1, 76, 76, 13, 50, 1, kSequencePointKind_Normal, 0, 72 },
	{ 108099, 1, 76, 76, 13, 50, 7, kSequencePointKind_StepOut, 0, 73 },
	{ 108099, 1, 77, 77, 9, 10, 15, kSequencePointKind_Normal, 0, 74 },
	{ 108100, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 75 },
	{ 108100, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 76 },
	{ 108100, 1, 80, 80, 9, 10, 0, kSequencePointKind_Normal, 0, 77 },
	{ 108100, 1, 81, 81, 13, 50, 1, kSequencePointKind_Normal, 0, 78 },
	{ 108100, 1, 81, 81, 13, 50, 8, kSequencePointKind_StepOut, 0, 79 },
	{ 108100, 1, 82, 82, 9, 10, 14, kSequencePointKind_Normal, 0, 80 },
	{ 108101, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 81 },
	{ 108101, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 82 },
	{ 108101, 1, 85, 85, 9, 10, 0, kSequencePointKind_Normal, 0, 83 },
	{ 108101, 1, 86, 86, 13, 52, 1, kSequencePointKind_Normal, 0, 84 },
	{ 108101, 1, 86, 86, 13, 52, 7, kSequencePointKind_StepOut, 0, 85 },
	{ 108101, 1, 87, 87, 9, 10, 15, kSequencePointKind_Normal, 0, 86 },
	{ 108102, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 87 },
	{ 108102, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 88 },
	{ 108102, 1, 90, 90, 9, 10, 0, kSequencePointKind_Normal, 0, 89 },
	{ 108102, 1, 91, 91, 13, 52, 1, kSequencePointKind_Normal, 0, 90 },
	{ 108102, 1, 91, 91, 13, 52, 8, kSequencePointKind_StepOut, 0, 91 },
	{ 108102, 1, 92, 92, 9, 10, 14, kSequencePointKind_Normal, 0, 92 },
	{ 108103, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 93 },
	{ 108103, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 94 },
	{ 108103, 1, 95, 95, 9, 10, 0, kSequencePointKind_Normal, 0, 95 },
	{ 108103, 1, 96, 96, 13, 55, 1, kSequencePointKind_Normal, 0, 96 },
	{ 108103, 1, 96, 96, 13, 55, 7, kSequencePointKind_StepOut, 0, 97 },
	{ 108103, 1, 97, 97, 9, 10, 15, kSequencePointKind_Normal, 0, 98 },
	{ 108104, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 99 },
	{ 108104, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 100 },
	{ 108104, 1, 100, 100, 9, 10, 0, kSequencePointKind_Normal, 0, 101 },
	{ 108104, 1, 101, 101, 13, 56, 1, kSequencePointKind_Normal, 0, 102 },
	{ 108104, 1, 101, 101, 13, 56, 7, kSequencePointKind_StepOut, 0, 103 },
	{ 108104, 1, 102, 102, 9, 10, 15, kSequencePointKind_Normal, 0, 104 },
	{ 108105, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 105 },
	{ 108105, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 106 },
	{ 108105, 1, 105, 105, 9, 10, 0, kSequencePointKind_Normal, 0, 107 },
	{ 108105, 1, 106, 106, 13, 47, 1, kSequencePointKind_Normal, 0, 108 },
	{ 108105, 1, 106, 106, 13, 47, 3, kSequencePointKind_StepOut, 0, 109 },
	{ 108105, 1, 107, 107, 13, 56, 9, kSequencePointKind_Normal, 0, 110 },
	{ 108105, 1, 107, 107, 13, 56, 16, kSequencePointKind_StepOut, 0, 111 },
	{ 108105, 1, 108, 108, 9, 10, 22, kSequencePointKind_Normal, 0, 112 },
	{ 108106, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 113 },
	{ 108106, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 114 },
	{ 108106, 1, 111, 111, 9, 10, 0, kSequencePointKind_Normal, 0, 115 },
	{ 108106, 1, 112, 112, 13, 56, 1, kSequencePointKind_Normal, 0, 116 },
	{ 108106, 1, 112, 112, 13, 56, 7, kSequencePointKind_StepOut, 0, 117 },
	{ 108106, 1, 113, 113, 9, 10, 15, kSequencePointKind_Normal, 0, 118 },
	{ 108107, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 119 },
	{ 108107, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 120 },
	{ 108107, 1, 116, 116, 9, 10, 0, kSequencePointKind_Normal, 0, 121 },
	{ 108107, 1, 117, 117, 13, 71, 1, kSequencePointKind_Normal, 0, 122 },
	{ 108107, 1, 117, 117, 13, 71, 7, kSequencePointKind_StepOut, 0, 123 },
	{ 108107, 1, 119, 120, 13, 80, 13, kSequencePointKind_Normal, 0, 124 },
	{ 108107, 1, 119, 120, 13, 80, 19, kSequencePointKind_StepOut, 0, 125 },
	{ 108107, 1, 119, 120, 0, 0, 73, kSequencePointKind_Normal, 0, 126 },
	{ 108107, 1, 121, 122, 17, 92, 76, kSequencePointKind_Normal, 0, 127 },
	{ 108107, 1, 121, 122, 17, 92, 83, kSequencePointKind_StepOut, 0, 128 },
	{ 108107, 1, 121, 122, 17, 92, 93, kSequencePointKind_StepOut, 0, 129 },
	{ 108107, 1, 121, 122, 17, 92, 98, kSequencePointKind_StepOut, 0, 130 },
	{ 108107, 1, 124, 124, 13, 56, 104, kSequencePointKind_Normal, 0, 131 },
	{ 108107, 1, 124, 124, 13, 56, 111, kSequencePointKind_StepOut, 0, 132 },
	{ 108107, 1, 125, 125, 9, 10, 117, kSequencePointKind_Normal, 0, 133 },
	{ 108108, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 134 },
	{ 108108, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 135 },
	{ 108108, 1, 128, 128, 9, 10, 0, kSequencePointKind_Normal, 0, 136 },
	{ 108108, 1, 129, 129, 13, 44, 1, kSequencePointKind_Normal, 0, 137 },
	{ 108108, 1, 129, 129, 13, 44, 13, kSequencePointKind_StepOut, 0, 138 },
	{ 108108, 1, 130, 130, 9, 10, 19, kSequencePointKind_Normal, 0, 139 },
	{ 108109, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 140 },
	{ 108109, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 141 },
	{ 108109, 1, 133, 133, 9, 10, 0, kSequencePointKind_Normal, 0, 142 },
	{ 108109, 1, 134, 134, 13, 52, 1, kSequencePointKind_Normal, 0, 143 },
	{ 108109, 1, 134, 134, 13, 52, 3, kSequencePointKind_StepOut, 0, 144 },
	{ 108109, 1, 135, 135, 13, 61, 9, kSequencePointKind_Normal, 0, 145 },
	{ 108109, 1, 135, 135, 13, 61, 16, kSequencePointKind_StepOut, 0, 146 },
	{ 108109, 1, 136, 136, 13, 30, 22, kSequencePointKind_Normal, 0, 147 },
	{ 108109, 1, 136, 136, 0, 0, 35, kSequencePointKind_Normal, 0, 148 },
	{ 108109, 1, 137, 137, 13, 14, 38, kSequencePointKind_Normal, 0, 149 },
	{ 108109, 1, 140, 140, 17, 60, 39, kSequencePointKind_Normal, 0, 150 },
	{ 108109, 1, 140, 140, 17, 60, 48, kSequencePointKind_StepOut, 0, 151 },
	{ 108109, 1, 142, 142, 17, 76, 54, kSequencePointKind_Normal, 0, 152 },
	{ 108109, 1, 142, 142, 17, 76, 63, kSequencePointKind_StepOut, 0, 153 },
	{ 108109, 1, 143, 143, 13, 14, 69, kSequencePointKind_Normal, 0, 154 },
	{ 108109, 1, 143, 143, 0, 0, 70, kSequencePointKind_Normal, 0, 155 },
	{ 108109, 1, 145, 145, 13, 14, 72, kSequencePointKind_Normal, 0, 156 },
	{ 108109, 1, 146, 146, 17, 55, 73, kSequencePointKind_Normal, 0, 157 },
	{ 108109, 1, 146, 146, 17, 55, 88, kSequencePointKind_StepOut, 0, 158 },
	{ 108109, 1, 147, 147, 17, 56, 94, kSequencePointKind_Normal, 0, 159 },
	{ 108109, 1, 147, 147, 17, 56, 109, kSequencePointKind_StepOut, 0, 160 },
	{ 108109, 1, 148, 148, 13, 14, 115, kSequencePointKind_Normal, 0, 161 },
	{ 108109, 1, 150, 150, 13, 41, 116, kSequencePointKind_Normal, 0, 162 },
	{ 108109, 1, 150, 150, 13, 41, 123, kSequencePointKind_StepOut, 0, 163 },
	{ 108109, 1, 151, 151, 13, 29, 129, kSequencePointKind_Normal, 0, 164 },
	{ 108109, 1, 151, 151, 13, 29, 135, kSequencePointKind_StepOut, 0, 165 },
	{ 108109, 1, 152, 152, 9, 10, 141, kSequencePointKind_Normal, 0, 166 },
	{ 108110, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 167 },
	{ 108110, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 168 },
	{ 108110, 1, 155, 155, 9, 10, 0, kSequencePointKind_Normal, 0, 169 },
	{ 108110, 1, 156, 156, 13, 71, 1, kSequencePointKind_Normal, 0, 170 },
	{ 108110, 1, 156, 156, 13, 71, 7, kSequencePointKind_StepOut, 0, 171 },
	{ 108110, 1, 162, 163, 13, 115, 13, kSequencePointKind_Normal, 0, 172 },
	{ 108110, 1, 162, 163, 13, 115, 14, kSequencePointKind_StepOut, 0, 173 },
	{ 108110, 1, 162, 163, 0, 0, 67, kSequencePointKind_Normal, 0, 174 },
	{ 108110, 1, 164, 164, 13, 14, 70, kSequencePointKind_Normal, 0, 175 },
	{ 108110, 1, 165, 166, 17, 92, 71, kSequencePointKind_Normal, 0, 176 },
	{ 108110, 1, 165, 166, 17, 92, 78, kSequencePointKind_StepOut, 0, 177 },
	{ 108110, 1, 165, 166, 17, 92, 88, kSequencePointKind_StepOut, 0, 178 },
	{ 108110, 1, 165, 166, 17, 92, 93, kSequencePointKind_StepOut, 0, 179 },
	{ 108110, 1, 167, 167, 13, 14, 99, kSequencePointKind_Normal, 0, 180 },
	{ 108110, 1, 168, 168, 9, 10, 100, kSequencePointKind_Normal, 0, 181 },
	{ 108122, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 182 },
	{ 108122, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 183 },
	{ 108122, 2, 17, 17, 9, 10, 0, kSequencePointKind_Normal, 0, 184 },
	{ 108122, 2, 18, 18, 13, 54, 1, kSequencePointKind_Normal, 0, 185 },
	{ 108122, 2, 18, 18, 13, 54, 2, kSequencePointKind_StepOut, 0, 186 },
	{ 108122, 2, 19, 19, 13, 37, 8, kSequencePointKind_Normal, 0, 187 },
	{ 108122, 2, 19, 19, 0, 0, 16, kSequencePointKind_Normal, 0, 188 },
	{ 108122, 2, 20, 22, 17, 87, 19, kSequencePointKind_Normal, 0, 189 },
	{ 108122, 2, 20, 22, 17, 87, 37, kSequencePointKind_StepOut, 0, 190 },
	{ 108122, 2, 20, 22, 17, 87, 47, kSequencePointKind_StepOut, 0, 191 },
	{ 108122, 2, 20, 22, 17, 87, 52, kSequencePointKind_StepOut, 0, 192 },
	{ 108122, 2, 24, 24, 13, 43, 58, kSequencePointKind_Normal, 0, 193 },
	{ 108122, 2, 24, 24, 13, 43, 59, kSequencePointKind_StepOut, 0, 194 },
	{ 108122, 2, 25, 25, 13, 54, 65, kSequencePointKind_Normal, 0, 195 },
	{ 108122, 2, 25, 25, 0, 0, 74, kSequencePointKind_Normal, 0, 196 },
	{ 108122, 2, 26, 28, 17, 41, 78, kSequencePointKind_Normal, 0, 197 },
	{ 108122, 2, 26, 28, 17, 41, 91, kSequencePointKind_StepOut, 0, 198 },
	{ 108122, 2, 26, 28, 17, 41, 96, kSequencePointKind_StepOut, 0, 199 },
	{ 108122, 2, 26, 28, 17, 41, 101, kSequencePointKind_StepOut, 0, 200 },
	{ 108122, 2, 30, 31, 13, 82, 107, kSequencePointKind_Normal, 0, 201 },
	{ 108122, 2, 30, 31, 13, 82, 109, kSequencePointKind_StepOut, 0, 202 },
	{ 108122, 2, 30, 31, 13, 82, 116, kSequencePointKind_StepOut, 0, 203 },
	{ 108122, 2, 33, 33, 13, 34, 122, kSequencePointKind_Normal, 0, 204 },
	{ 108122, 2, 33, 33, 0, 0, 128, kSequencePointKind_Normal, 0, 205 },
	{ 108122, 2, 34, 35, 17, 78, 132, kSequencePointKind_Normal, 0, 206 },
	{ 108122, 2, 34, 35, 17, 78, 137, kSequencePointKind_StepOut, 0, 207 },
	{ 108122, 2, 37, 37, 13, 38, 143, kSequencePointKind_Normal, 0, 208 },
	{ 108122, 2, 37, 37, 13, 38, 144, kSequencePointKind_StepOut, 0, 209 },
	{ 108122, 2, 37, 37, 13, 38, 150, kSequencePointKind_StepOut, 0, 210 },
	{ 108122, 2, 37, 37, 0, 0, 157, kSequencePointKind_Normal, 0, 211 },
	{ 108122, 2, 38, 39, 17, 112, 161, kSequencePointKind_Normal, 0, 212 },
	{ 108122, 2, 38, 39, 17, 112, 166, kSequencePointKind_StepOut, 0, 213 },
	{ 108122, 2, 41, 41, 13, 51, 172, kSequencePointKind_Normal, 0, 214 },
	{ 108122, 2, 41, 41, 13, 51, 173, kSequencePointKind_StepOut, 0, 215 },
	{ 108122, 2, 41, 41, 0, 0, 186, kSequencePointKind_Normal, 0, 216 },
	{ 108122, 2, 42, 44, 17, 72, 190, kSequencePointKind_Normal, 0, 217 },
	{ 108122, 2, 42, 44, 17, 72, 196, kSequencePointKind_StepOut, 0, 218 },
	{ 108122, 2, 42, 44, 17, 72, 205, kSequencePointKind_StepOut, 0, 219 },
	{ 108122, 2, 42, 44, 17, 72, 217, kSequencePointKind_StepOut, 0, 220 },
	{ 108122, 2, 42, 44, 17, 72, 222, kSequencePointKind_StepOut, 0, 221 },
	{ 108122, 2, 42, 44, 17, 72, 227, kSequencePointKind_StepOut, 0, 222 },
	{ 108122, 2, 46, 46, 13, 29, 233, kSequencePointKind_Normal, 0, 223 },
	{ 108122, 2, 47, 47, 9, 10, 238, kSequencePointKind_Normal, 0, 224 },
	{ 108124, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 225 },
	{ 108124, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 226 },
	{ 108124, 3, 13, 13, 9, 28, 0, kSequencePointKind_Normal, 0, 227 },
	{ 108124, 3, 13, 13, 9, 28, 1, kSequencePointKind_StepOut, 0, 228 },
	{ 108124, 3, 13, 13, 29, 30, 7, kSequencePointKind_Normal, 0, 229 },
	{ 108124, 3, 13, 13, 30, 31, 8, kSequencePointKind_Normal, 0, 230 },
	{ 108210, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 231 },
	{ 108210, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 232 },
	{ 108210, 4, 242, 242, 13, 14, 0, kSequencePointKind_Normal, 0, 233 },
	{ 108210, 4, 243, 243, 17, 55, 1, kSequencePointKind_Normal, 0, 234 },
	{ 108210, 4, 243, 243, 17, 55, 2, kSequencePointKind_StepOut, 0, 235 },
	{ 108210, 4, 244, 244, 13, 14, 10, kSequencePointKind_Normal, 0, 236 },
	{ 108211, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 237 },
	{ 108211, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 238 },
	{ 108211, 4, 247, 247, 13, 14, 0, kSequencePointKind_Normal, 0, 239 },
	{ 108211, 4, 248, 248, 17, 65, 1, kSequencePointKind_Normal, 0, 240 },
	{ 108211, 4, 248, 248, 17, 65, 1, kSequencePointKind_StepOut, 0, 241 },
	{ 108211, 4, 249, 249, 17, 42, 7, kSequencePointKind_Normal, 0, 242 },
	{ 108211, 4, 249, 249, 0, 0, 12, kSequencePointKind_Normal, 0, 243 },
	{ 108211, 4, 250, 250, 21, 127, 15, kSequencePointKind_Normal, 0, 244 },
	{ 108211, 4, 250, 250, 21, 127, 26, kSequencePointKind_StepOut, 0, 245 },
	{ 108211, 4, 250, 250, 21, 127, 36, kSequencePointKind_StepOut, 0, 246 },
	{ 108211, 4, 252, 252, 17, 53, 42, kSequencePointKind_Normal, 0, 247 },
	{ 108211, 4, 252, 252, 17, 53, 44, kSequencePointKind_StepOut, 0, 248 },
	{ 108211, 4, 253, 253, 13, 14, 50, kSequencePointKind_Normal, 0, 249 },
	{ 108243, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 250 },
	{ 108243, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 251 },
	{ 108243, 4, 310, 310, 9, 10, 0, kSequencePointKind_Normal, 0, 252 },
	{ 108243, 4, 311, 311, 13, 49, 1, kSequencePointKind_Normal, 0, 253 },
	{ 108243, 4, 311, 311, 0, 0, 11, kSequencePointKind_Normal, 0, 254 },
	{ 108243, 4, 312, 312, 17, 49, 14, kSequencePointKind_Normal, 0, 255 },
	{ 108243, 4, 312, 312, 17, 49, 21, kSequencePointKind_StepOut, 0, 256 },
	{ 108243, 4, 313, 313, 9, 10, 27, kSequencePointKind_Normal, 0, 257 },
	{ 108244, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 258 },
	{ 108244, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 259 },
	{ 108244, 4, 317, 317, 9, 10, 0, kSequencePointKind_Normal, 0, 260 },
	{ 108244, 4, 318, 318, 13, 43, 1, kSequencePointKind_Normal, 0, 261 },
	{ 108244, 4, 318, 318, 0, 0, 11, kSequencePointKind_Normal, 0, 262 },
	{ 108244, 4, 319, 319, 17, 53, 14, kSequencePointKind_Normal, 0, 263 },
	{ 108244, 4, 319, 319, 17, 53, 22, kSequencePointKind_StepOut, 0, 264 },
	{ 108244, 4, 320, 320, 9, 10, 28, kSequencePointKind_Normal, 0, 265 },
	{ 108245, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 266 },
	{ 108245, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 267 },
	{ 108245, 4, 324, 324, 9, 10, 0, kSequencePointKind_Normal, 0, 268 },
	{ 108245, 4, 325, 325, 13, 49, 1, kSequencePointKind_Normal, 0, 269 },
	{ 108245, 4, 325, 325, 0, 0, 11, kSequencePointKind_Normal, 0, 270 },
	{ 108245, 4, 326, 326, 17, 49, 14, kSequencePointKind_Normal, 0, 271 },
	{ 108245, 4, 326, 326, 17, 49, 21, kSequencePointKind_StepOut, 0, 272 },
	{ 108245, 4, 327, 327, 9, 10, 27, kSequencePointKind_Normal, 0, 273 },
	{ 108246, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 274 },
	{ 108246, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 275 },
	{ 108246, 4, 331, 331, 9, 10, 0, kSequencePointKind_Normal, 0, 276 },
	{ 108246, 4, 332, 332, 13, 40, 1, kSequencePointKind_Normal, 0, 277 },
	{ 108246, 4, 332, 332, 0, 0, 11, kSequencePointKind_Normal, 0, 278 },
	{ 108246, 4, 333, 333, 17, 40, 14, kSequencePointKind_Normal, 0, 279 },
	{ 108246, 4, 333, 333, 17, 40, 21, kSequencePointKind_StepOut, 0, 280 },
	{ 108246, 4, 334, 334, 9, 10, 27, kSequencePointKind_Normal, 0, 281 },
	{ 108247, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 282 },
	{ 108247, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 283 },
	{ 108247, 4, 338, 338, 9, 10, 0, kSequencePointKind_Normal, 0, 284 },
	{ 108247, 4, 339, 339, 13, 45, 1, kSequencePointKind_Normal, 0, 285 },
	{ 108247, 4, 339, 339, 0, 0, 11, kSequencePointKind_Normal, 0, 286 },
	{ 108247, 4, 340, 340, 17, 45, 14, kSequencePointKind_Normal, 0, 287 },
	{ 108247, 4, 340, 340, 17, 45, 21, kSequencePointKind_StepOut, 0, 288 },
	{ 108247, 4, 341, 341, 9, 10, 27, kSequencePointKind_Normal, 0, 289 },
	{ 108248, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 290 },
	{ 108248, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 291 },
	{ 108248, 4, 345, 345, 9, 10, 0, kSequencePointKind_Normal, 0, 292 },
	{ 108248, 4, 346, 346, 13, 46, 1, kSequencePointKind_Normal, 0, 293 },
	{ 108248, 4, 346, 346, 0, 0, 11, kSequencePointKind_Normal, 0, 294 },
	{ 108248, 4, 347, 347, 17, 56, 14, kSequencePointKind_Normal, 0, 295 },
	{ 108248, 4, 347, 347, 17, 56, 22, kSequencePointKind_StepOut, 0, 296 },
	{ 108248, 4, 348, 348, 9, 10, 28, kSequencePointKind_Normal, 0, 297 },
	{ 108249, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 298 },
	{ 108249, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 299 },
	{ 108249, 4, 352, 352, 9, 10, 0, kSequencePointKind_Normal, 0, 300 },
	{ 108249, 4, 353, 353, 13, 46, 1, kSequencePointKind_Normal, 0, 301 },
	{ 108249, 4, 353, 353, 0, 0, 11, kSequencePointKind_Normal, 0, 302 },
	{ 108249, 4, 354, 354, 17, 46, 14, kSequencePointKind_Normal, 0, 303 },
	{ 108249, 4, 354, 354, 17, 46, 21, kSequencePointKind_StepOut, 0, 304 },
	{ 108249, 4, 355, 355, 9, 10, 27, kSequencePointKind_Normal, 0, 305 },
	{ 108250, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 306 },
	{ 108250, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 307 },
	{ 108250, 4, 359, 359, 9, 10, 0, kSequencePointKind_Normal, 0, 308 },
	{ 108250, 4, 360, 360, 13, 52, 1, kSequencePointKind_Normal, 0, 309 },
	{ 108250, 4, 360, 360, 0, 0, 11, kSequencePointKind_Normal, 0, 310 },
	{ 108250, 4, 361, 361, 17, 61, 14, kSequencePointKind_Normal, 0, 311 },
	{ 108250, 4, 361, 361, 17, 61, 22, kSequencePointKind_StepOut, 0, 312 },
	{ 108250, 4, 362, 362, 9, 10, 28, kSequencePointKind_Normal, 0, 313 },
	{ 108300, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 314 },
	{ 108300, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 315 },
	{ 108300, 5, 97, 97, 9, 10, 0, kSequencePointKind_Normal, 0, 316 },
	{ 108300, 5, 98, 98, 13, 52, 1, kSequencePointKind_Normal, 0, 317 },
	{ 108300, 5, 98, 98, 13, 52, 3, kSequencePointKind_StepOut, 0, 318 },
	{ 108300, 5, 98, 98, 0, 0, 14, kSequencePointKind_Normal, 0, 319 },
	{ 108300, 5, 99, 101, 17, 79, 17, kSequencePointKind_Normal, 0, 320 },
	{ 108300, 5, 99, 101, 17, 79, 34, kSequencePointKind_StepOut, 0, 321 },
	{ 108300, 5, 99, 101, 17, 79, 42, kSequencePointKind_StepOut, 0, 322 },
	{ 108300, 5, 99, 101, 17, 79, 52, kSequencePointKind_StepOut, 0, 323 },
	{ 108300, 5, 99, 101, 17, 79, 57, kSequencePointKind_StepOut, 0, 324 },
	{ 108300, 5, 103, 103, 13, 111, 63, kSequencePointKind_Normal, 0, 325 },
	{ 108300, 5, 103, 103, 13, 111, 65, kSequencePointKind_StepOut, 0, 326 },
	{ 108300, 5, 103, 103, 13, 111, 72, kSequencePointKind_StepOut, 0, 327 },
	{ 108300, 5, 105, 105, 13, 34, 78, kSequencePointKind_Normal, 0, 328 },
	{ 108300, 5, 105, 105, 0, 0, 83, kSequencePointKind_Normal, 0, 329 },
	{ 108300, 5, 106, 107, 17, 80, 86, kSequencePointKind_Normal, 0, 330 },
	{ 108300, 5, 106, 107, 17, 80, 91, kSequencePointKind_StepOut, 0, 331 },
	{ 108300, 5, 109, 109, 13, 40, 97, kSequencePointKind_Normal, 0, 332 },
	{ 108300, 5, 109, 109, 13, 40, 98, kSequencePointKind_StepOut, 0, 333 },
	{ 108300, 5, 109, 109, 13, 40, 104, kSequencePointKind_StepOut, 0, 334 },
	{ 108300, 5, 109, 109, 0, 0, 111, kSequencePointKind_Normal, 0, 335 },
	{ 108300, 5, 110, 111, 17, 117, 115, kSequencePointKind_Normal, 0, 336 },
	{ 108300, 5, 110, 111, 17, 117, 120, kSequencePointKind_StepOut, 0, 337 },
	{ 108300, 5, 113, 113, 13, 51, 126, kSequencePointKind_Normal, 0, 338 },
	{ 108300, 5, 113, 113, 13, 51, 127, kSequencePointKind_StepOut, 0, 339 },
	{ 108300, 5, 113, 113, 0, 0, 140, kSequencePointKind_Normal, 0, 340 },
	{ 108300, 5, 114, 116, 17, 72, 144, kSequencePointKind_Normal, 0, 341 },
	{ 108300, 5, 114, 116, 17, 72, 150, kSequencePointKind_StepOut, 0, 342 },
	{ 108300, 5, 114, 116, 17, 72, 158, kSequencePointKind_StepOut, 0, 343 },
	{ 108300, 5, 114, 116, 17, 72, 170, kSequencePointKind_StepOut, 0, 344 },
	{ 108300, 5, 114, 116, 17, 72, 175, kSequencePointKind_StepOut, 0, 345 },
	{ 108300, 5, 114, 116, 17, 72, 180, kSequencePointKind_StepOut, 0, 346 },
	{ 108300, 5, 118, 118, 13, 29, 186, kSequencePointKind_Normal, 0, 347 },
	{ 108300, 5, 119, 119, 9, 10, 191, kSequencePointKind_Normal, 0, 348 },
	{ 108307, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 349 },
	{ 108307, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 350 },
	{ 108307, 6, 18, 18, 9, 34, 0, kSequencePointKind_Normal, 0, 351 },
	{ 108307, 6, 18, 18, 9, 34, 1, kSequencePointKind_StepOut, 0, 352 },
	{ 108307, 6, 19, 19, 9, 10, 7, kSequencePointKind_Normal, 0, 353 },
	{ 108307, 6, 20, 20, 13, 39, 8, kSequencePointKind_Normal, 0, 354 },
	{ 108307, 6, 20, 20, 13, 39, 9, kSequencePointKind_StepOut, 0, 355 },
	{ 108307, 6, 21, 21, 9, 10, 19, kSequencePointKind_Normal, 0, 356 },
	{ 108308, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 357 },
	{ 108308, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 358 },
	{ 108308, 6, 24, 24, 9, 10, 0, kSequencePointKind_Normal, 0, 359 },
	{ 108308, 6, 25, 25, 13, 38, 1, kSequencePointKind_Normal, 0, 360 },
	{ 108308, 6, 25, 25, 13, 38, 12, kSequencePointKind_StepOut, 0, 361 },
	{ 108308, 6, 25, 25, 0, 0, 18, kSequencePointKind_Normal, 0, 362 },
	{ 108308, 6, 26, 26, 13, 14, 21, kSequencePointKind_Normal, 0, 363 },
	{ 108308, 6, 27, 27, 17, 41, 22, kSequencePointKind_Normal, 0, 364 },
	{ 108308, 6, 27, 27, 17, 41, 28, kSequencePointKind_StepOut, 0, 365 },
	{ 108308, 6, 28, 28, 17, 37, 34, kSequencePointKind_Normal, 0, 366 },
	{ 108308, 6, 29, 29, 13, 14, 45, kSequencePointKind_Normal, 0, 367 },
	{ 108308, 6, 30, 30, 13, 39, 46, kSequencePointKind_Normal, 0, 368 },
	{ 108308, 6, 30, 30, 13, 39, 47, kSequencePointKind_StepOut, 0, 369 },
	{ 108308, 6, 31, 31, 9, 10, 53, kSequencePointKind_Normal, 0, 370 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_VideoModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_VideoModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Video/Public/ScriptBindings/VideoClipPlayable.bindings.cs", { 105, 241, 37, 55, 123, 182, 43, 39, 93, 51, 224, 145, 33, 242, 124, 192} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Video/Public/ScriptBindings/VideoPlayerExtensions.bindings.cs", { 97, 30, 178, 106, 1, 140, 65, 115, 96, 68, 163, 249, 26, 52, 175, 40} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Video/Public/ScriptBindings/VideoClip.bindings.cs", { 197, 100, 3, 100, 149, 252, 91, 219, 17, 122, 169, 23, 206, 209, 245, 109} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Video/Public/ScriptBindings/VideoPlayer.bindings.cs", { 75, 96, 48, 146, 42, 59, 240, 165, 47, 186, 102, 125, 150, 85, 126, 104} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Video/Public/ScriptBindings/MediaComponent.bindings.cs", { 146, 251, 144, 165, 113, 221, 176, 105, 101, 208, 235, 248, 110, 109, 193, 26} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Video/Public/ScriptBindings/VideoMediaPlayback.bindings.cs", { 99, 235, 251, 169, 159, 50, 125, 176, 212, 221, 199, 137, 187, 57, 226, 55} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[6] = 
{
	{ 13876, 1 },
	{ 13877, 2 },
	{ 13878, 3 },
	{ 13891, 4 },
	{ 13896, 5 },
	{ 13899, 6 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[28] = 
{
	{ 0, 48 },
	{ 0, 39 },
	{ 0, 47 },
	{ 0, 12 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 24 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 17 },
	{ 0, 118 },
	{ 0, 142 },
	{ 0, 101 },
	{ 0, 241 },
	{ 0, 12 },
	{ 0, 51 },
	{ 0, 28 },
	{ 0, 29 },
	{ 0, 28 },
	{ 0, 28 },
	{ 0, 28 },
	{ 0, 29 },
	{ 0, 28 },
	{ 0, 29 },
	{ 0, 194 },
	{ 0, 54 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[232] = 
{
	{ 48, 0, 1 },
	{ 39, 1, 1 },
	{ 47, 2, 1 },
	{ 12, 3, 1 },
	{ 18, 4, 1 },
	{ 18, 5, 1 },
	{ 24, 6, 1 },
	{ 17, 7, 1 },
	{ 0, 0, 0 },
	{ 17, 8, 1 },
	{ 0, 0, 0 },
	{ 17, 9, 1 },
	{ 17, 10, 1 },
	{ 0, 0, 0 },
	{ 17, 11, 1 },
	{ 118, 12, 1 },
	{ 0, 0, 0 },
	{ 142, 13, 1 },
	{ 101, 14, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 241, 15, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 12, 16, 1 },
	{ 51, 17, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 28, 18, 1 },
	{ 29, 19, 1 },
	{ 28, 20, 1 },
	{ 28, 21, 1 },
	{ 28, 22, 1 },
	{ 29, 23, 1 },
	{ 28, 24, 1 },
	{ 29, 25, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 194, 26, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 54, 27, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VideoModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VideoModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	371,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_VideoModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	6,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
