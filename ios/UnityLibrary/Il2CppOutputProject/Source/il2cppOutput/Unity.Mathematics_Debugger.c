﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[10] = 
{
	{ 38900, 0,  3 },
	{ 38900, 0,  8 },
	{ 38901, 0,  37 },
	{ 38901, 0,  39 },
	{ 32703, 1,  57 },
	{ 32706, 1,  67 },
	{ 32770, 1,  74 },
	{ 32776, 1,  80 },
	{ 32777, 1,  86 },
	{ 32778, 1,  92 },
};
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[2] = 
{
	"u",
	"converted",
};
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[111] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 1, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 2, 1 },
	{ 0, 0 },
	{ 3, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 4, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 5, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 6, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 7, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 8, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 9, 1 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnity_Mathematics[];
Il2CppSequencePoint g_sequencePointsUnity_Mathematics[732] = 
{
	{ 109636, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109636, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109636, 1, 903, 903, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109636, 1, 904, 904, 13, 84, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109636, 1, 904, 904, 13, 84, 2, kSequencePointKind_StepOut, 0, 4 },
	{ 109636, 1, 904, 904, 13, 84, 17, kSequencePointKind_StepOut, 0, 5 },
	{ 109636, 1, 904, 904, 13, 84, 22, kSequencePointKind_StepOut, 0, 6 },
	{ 109636, 1, 904, 904, 13, 84, 27, kSequencePointKind_StepOut, 0, 7 },
	{ 109636, 1, 905, 905, 9, 10, 41, kSequencePointKind_Normal, 0, 8 },
	{ 109637, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 9 },
	{ 109637, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 10 },
	{ 109637, 2, 1779, 1779, 9, 10, 0, kSequencePointKind_Normal, 0, 11 },
	{ 109637, 2, 1780, 1780, 13, 97, 1, kSequencePointKind_Normal, 0, 12 },
	{ 109637, 2, 1780, 1780, 13, 97, 2, kSequencePointKind_StepOut, 0, 13 },
	{ 109637, 2, 1780, 1780, 13, 97, 22, kSequencePointKind_StepOut, 0, 14 },
	{ 109637, 2, 1780, 1780, 13, 97, 27, kSequencePointKind_StepOut, 0, 15 },
	{ 109637, 2, 1780, 1780, 13, 97, 32, kSequencePointKind_StepOut, 0, 16 },
	{ 109637, 2, 1781, 1781, 9, 10, 46, kSequencePointKind_Normal, 0, 17 },
	{ 109638, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 18 },
	{ 109638, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 19 },
	{ 109638, 3, 948, 948, 9, 10, 0, kSequencePointKind_Normal, 0, 20 },
	{ 109638, 3, 949, 949, 13, 84, 1, kSequencePointKind_Normal, 0, 21 },
	{ 109638, 3, 949, 949, 13, 84, 2, kSequencePointKind_StepOut, 0, 22 },
	{ 109638, 3, 949, 949, 13, 84, 17, kSequencePointKind_StepOut, 0, 23 },
	{ 109638, 3, 949, 949, 13, 84, 22, kSequencePointKind_StepOut, 0, 24 },
	{ 109638, 3, 949, 949, 13, 84, 27, kSequencePointKind_StepOut, 0, 25 },
	{ 109638, 3, 950, 950, 9, 10, 41, kSequencePointKind_Normal, 0, 26 },
	{ 109639, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 27 },
	{ 109639, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 28 },
	{ 109639, 4, 180, 180, 42, 43, 0, kSequencePointKind_Normal, 0, 29 },
	{ 109639, 4, 182, 182, 13, 28, 1, kSequencePointKind_Normal, 0, 30 },
	{ 109639, 4, 183, 183, 13, 30, 9, kSequencePointKind_Normal, 0, 31 },
	{ 109639, 4, 184, 184, 13, 31, 17, kSequencePointKind_Normal, 0, 32 },
	{ 109639, 4, 185, 185, 9, 10, 26, kSequencePointKind_Normal, 0, 33 },
	{ 109640, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 34 },
	{ 109640, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 35 },
	{ 109640, 4, 216, 216, 44, 45, 0, kSequencePointKind_Normal, 0, 36 },
	{ 109640, 4, 216, 216, 46, 81, 1, kSequencePointKind_Normal, 0, 37 },
	{ 109640, 4, 216, 216, 46, 81, 13, kSequencePointKind_StepOut, 0, 38 },
	{ 109640, 4, 216, 216, 82, 83, 21, kSequencePointKind_Normal, 0, 39 },
	{ 109641, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 40 },
	{ 109641, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 41 },
	{ 109641, 4, 235, 235, 44, 45, 0, kSequencePointKind_Normal, 0, 42 },
	{ 109641, 4, 235, 235, 46, 68, 1, kSequencePointKind_Normal, 0, 43 },
	{ 109641, 4, 235, 235, 46, 68, 2, kSequencePointKind_StepOut, 0, 44 },
	{ 109641, 4, 235, 235, 69, 70, 10, kSequencePointKind_Normal, 0, 45 },
	{ 109642, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 46 },
	{ 109642, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 47 },
	{ 109642, 4, 241, 241, 46, 47, 0, kSequencePointKind_Normal, 0, 48 },
	{ 109642, 4, 241, 241, 48, 87, 1, kSequencePointKind_Normal, 0, 49 },
	{ 109642, 4, 241, 241, 48, 87, 7, kSequencePointKind_StepOut, 0, 50 },
	{ 109642, 4, 241, 241, 48, 87, 18, kSequencePointKind_StepOut, 0, 51 },
	{ 109642, 4, 241, 241, 48, 87, 23, kSequencePointKind_StepOut, 0, 52 },
	{ 109642, 4, 241, 241, 88, 89, 31, kSequencePointKind_Normal, 0, 53 },
	{ 109643, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 54 },
	{ 109643, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 55 },
	{ 109643, 4, 247, 247, 46, 47, 0, kSequencePointKind_Normal, 0, 56 },
	{ 109643, 4, 247, 247, 48, 100, 1, kSequencePointKind_Normal, 0, 57 },
	{ 109643, 4, 247, 247, 48, 100, 7, kSequencePointKind_StepOut, 0, 58 },
	{ 109643, 4, 247, 247, 48, 100, 18, kSequencePointKind_StepOut, 0, 59 },
	{ 109643, 4, 247, 247, 48, 100, 29, kSequencePointKind_StepOut, 0, 60 },
	{ 109643, 4, 247, 247, 48, 100, 34, kSequencePointKind_StepOut, 0, 61 },
	{ 109643, 4, 247, 247, 101, 102, 42, kSequencePointKind_Normal, 0, 62 },
	{ 109644, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 63 },
	{ 109644, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 64 },
	{ 109644, 4, 293, 293, 9, 10, 0, kSequencePointKind_Normal, 0, 65 },
	{ 109644, 4, 295, 295, 13, 30, 1, kSequencePointKind_Normal, 0, 66 },
	{ 109644, 4, 296, 296, 13, 28, 13, kSequencePointKind_Normal, 0, 67 },
	{ 109644, 4, 298, 298, 13, 33, 21, kSequencePointKind_Normal, 0, 68 },
	{ 109644, 4, 299, 299, 9, 10, 30, kSequencePointKind_Normal, 0, 69 },
	{ 109645, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 70 },
	{ 109645, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 71 },
	{ 109645, 4, 324, 324, 46, 47, 0, kSequencePointKind_Normal, 0, 72 },
	{ 109645, 4, 324, 324, 48, 71, 1, kSequencePointKind_Normal, 0, 73 },
	{ 109645, 4, 324, 324, 48, 71, 2, kSequencePointKind_StepOut, 0, 74 },
	{ 109645, 4, 324, 324, 72, 73, 10, kSequencePointKind_Normal, 0, 75 },
	{ 109646, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 76 },
	{ 109646, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 77 },
	{ 109646, 4, 672, 672, 45, 46, 0, kSequencePointKind_Normal, 0, 78 },
	{ 109646, 4, 672, 672, 47, 68, 1, kSequencePointKind_Normal, 0, 79 },
	{ 109646, 4, 672, 672, 69, 70, 12, kSequencePointKind_Normal, 0, 80 },
	{ 109647, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 81 },
	{ 109647, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 82 },
	{ 109647, 4, 730, 730, 48, 49, 0, kSequencePointKind_Normal, 0, 83 },
	{ 109647, 4, 730, 730, 50, 71, 1, kSequencePointKind_Normal, 0, 84 },
	{ 109647, 4, 730, 730, 72, 73, 12, kSequencePointKind_Normal, 0, 85 },
	{ 109648, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 86 },
	{ 109648, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 87 },
	{ 109648, 4, 746, 746, 51, 52, 0, kSequencePointKind_Normal, 0, 88 },
	{ 109648, 4, 746, 746, 53, 92, 1, kSequencePointKind_Normal, 0, 89 },
	{ 109648, 4, 746, 746, 53, 92, 2, kSequencePointKind_StepOut, 0, 90 },
	{ 109648, 4, 746, 746, 93, 94, 20, kSequencePointKind_Normal, 0, 91 },
	{ 109649, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 92 },
	{ 109649, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 93 },
	{ 109649, 4, 804, 804, 45, 46, 0, kSequencePointKind_Normal, 0, 94 },
	{ 109649, 4, 804, 804, 47, 68, 1, kSequencePointKind_Normal, 0, 95 },
	{ 109649, 4, 804, 804, 69, 70, 12, kSequencePointKind_Normal, 0, 96 },
	{ 109650, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 97 },
	{ 109650, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 98 },
	{ 109650, 4, 862, 862, 48, 49, 0, kSequencePointKind_Normal, 0, 99 },
	{ 109650, 4, 862, 862, 50, 71, 1, kSequencePointKind_Normal, 0, 100 },
	{ 109650, 4, 862, 862, 72, 73, 12, kSequencePointKind_Normal, 0, 101 },
	{ 109651, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 102 },
	{ 109651, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 103 },
	{ 109651, 4, 878, 878, 51, 52, 0, kSequencePointKind_Normal, 0, 104 },
	{ 109651, 4, 878, 878, 53, 92, 1, kSequencePointKind_Normal, 0, 105 },
	{ 109651, 4, 878, 878, 53, 92, 2, kSequencePointKind_StepOut, 0, 106 },
	{ 109651, 4, 878, 878, 93, 94, 20, kSequencePointKind_Normal, 0, 107 },
	{ 109652, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 108 },
	{ 109652, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 109 },
	{ 109652, 4, 940, 940, 61, 62, 0, kSequencePointKind_Normal, 0, 110 },
	{ 109652, 4, 940, 940, 63, 86, 1, kSequencePointKind_Normal, 0, 111 },
	{ 109652, 4, 940, 940, 87, 88, 11, kSequencePointKind_Normal, 0, 112 },
	{ 109653, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 113 },
	{ 109653, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 114 },
	{ 109653, 4, 951, 951, 64, 65, 0, kSequencePointKind_Normal, 0, 115 },
	{ 109653, 4, 951, 951, 66, 89, 1, kSequencePointKind_Normal, 0, 116 },
	{ 109653, 4, 951, 951, 66, 89, 5, kSequencePointKind_StepOut, 0, 117 },
	{ 109653, 4, 951, 951, 66, 89, 10, kSequencePointKind_StepOut, 0, 118 },
	{ 109653, 4, 951, 951, 66, 89, 15, kSequencePointKind_StepOut, 0, 119 },
	{ 109653, 4, 951, 951, 90, 91, 23, kSequencePointKind_Normal, 0, 120 },
	{ 109654, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 121 },
	{ 109654, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 122 },
	{ 109654, 4, 962, 962, 64, 65, 0, kSequencePointKind_Normal, 0, 123 },
	{ 109654, 4, 962, 962, 66, 89, 1, kSequencePointKind_Normal, 0, 124 },
	{ 109654, 4, 962, 962, 66, 89, 5, kSequencePointKind_StepOut, 0, 125 },
	{ 109654, 4, 962, 962, 66, 89, 10, kSequencePointKind_StepOut, 0, 126 },
	{ 109654, 4, 962, 962, 66, 89, 15, kSequencePointKind_StepOut, 0, 127 },
	{ 109654, 4, 962, 962, 90, 91, 23, kSequencePointKind_Normal, 0, 128 },
	{ 109655, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 129 },
	{ 109655, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 130 },
	{ 109655, 4, 1516, 1516, 62, 63, 0, kSequencePointKind_Normal, 0, 131 },
	{ 109655, 4, 1516, 1516, 64, 89, 1, kSequencePointKind_Normal, 0, 132 },
	{ 109655, 4, 1516, 1516, 64, 89, 4, kSequencePointKind_StepOut, 0, 133 },
	{ 109655, 4, 1516, 1516, 64, 89, 9, kSequencePointKind_StepOut, 0, 134 },
	{ 109655, 4, 1516, 1516, 90, 91, 17, kSequencePointKind_Normal, 0, 135 },
	{ 109656, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 136 },
	{ 109656, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 137 },
	{ 109656, 4, 1630, 1630, 38, 39, 0, kSequencePointKind_Normal, 0, 138 },
	{ 109656, 4, 1630, 1630, 40, 58, 1, kSequencePointKind_Normal, 0, 139 },
	{ 109656, 4, 1630, 1630, 40, 58, 4, kSequencePointKind_StepOut, 0, 140 },
	{ 109656, 4, 1630, 1630, 59, 60, 12, kSequencePointKind_Normal, 0, 141 },
	{ 109657, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 142 },
	{ 109657, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 143 },
	{ 109657, 4, 1661, 1661, 42, 43, 0, kSequencePointKind_Normal, 0, 144 },
	{ 109657, 4, 1661, 1661, 44, 83, 1, kSequencePointKind_Normal, 0, 145 },
	{ 109657, 4, 1661, 1661, 44, 83, 2, kSequencePointKind_StepOut, 0, 146 },
	{ 109657, 4, 1661, 1661, 44, 83, 13, kSequencePointKind_StepOut, 0, 147 },
	{ 109657, 4, 1661, 1661, 84, 85, 21, kSequencePointKind_Normal, 0, 148 },
	{ 109658, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 149 },
	{ 109658, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 150 },
	{ 109658, 4, 1777, 1777, 53, 54, 0, kSequencePointKind_Normal, 0, 151 },
	{ 109658, 4, 1777, 1777, 55, 84, 1, kSequencePointKind_Normal, 0, 152 },
	{ 109658, 4, 1777, 1777, 85, 86, 31, kSequencePointKind_Normal, 0, 153 },
	{ 109659, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 154 },
	{ 109659, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 155 },
	{ 109659, 4, 1784, 1784, 53, 54, 0, kSequencePointKind_Normal, 0, 156 },
	{ 109659, 4, 1784, 1784, 55, 96, 1, kSequencePointKind_Normal, 0, 157 },
	{ 109659, 4, 1784, 1784, 97, 98, 45, kSequencePointKind_Normal, 0, 158 },
	{ 109660, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 159 },
	{ 109660, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 160 },
	{ 109660, 4, 2035, 2035, 42, 43, 0, kSequencePointKind_Normal, 0, 161 },
	{ 109660, 4, 2035, 2035, 44, 77, 1, kSequencePointKind_Normal, 0, 162 },
	{ 109660, 4, 2035, 2035, 44, 77, 3, kSequencePointKind_StepOut, 0, 163 },
	{ 109660, 4, 2035, 2035, 78, 79, 12, kSequencePointKind_Normal, 0, 164 },
	{ 109661, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 165 },
	{ 109661, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 166 },
	{ 109661, 4, 2185, 2185, 42, 43, 0, kSequencePointKind_Normal, 0, 167 },
	{ 109661, 4, 2185, 2185, 44, 84, 1, kSequencePointKind_Normal, 0, 168 },
	{ 109661, 4, 2185, 2185, 44, 84, 4, kSequencePointKind_StepOut, 0, 169 },
	{ 109661, 4, 2185, 2185, 85, 86, 13, kSequencePointKind_Normal, 0, 170 },
	{ 109662, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 171 },
	{ 109662, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 172 },
	{ 109662, 4, 2335, 2335, 44, 45, 0, kSequencePointKind_Normal, 0, 173 },
	{ 109662, 4, 2335, 2335, 46, 88, 1, kSequencePointKind_Normal, 0, 174 },
	{ 109662, 4, 2335, 2335, 46, 88, 4, kSequencePointKind_StepOut, 0, 175 },
	{ 109662, 4, 2335, 2335, 89, 90, 13, kSequencePointKind_Normal, 0, 176 },
	{ 109663, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 177 },
	{ 109663, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 178 },
	{ 109663, 4, 2341, 2341, 46, 47, 0, kSequencePointKind_Normal, 0, 179 },
	{ 109663, 4, 2341, 2341, 48, 90, 1, kSequencePointKind_Normal, 0, 180 },
	{ 109663, 4, 2341, 2341, 48, 90, 7, kSequencePointKind_StepOut, 0, 181 },
	{ 109663, 4, 2341, 2341, 48, 90, 18, kSequencePointKind_StepOut, 0, 182 },
	{ 109663, 4, 2341, 2341, 48, 90, 23, kSequencePointKind_StepOut, 0, 183 },
	{ 109663, 4, 2341, 2341, 91, 92, 31, kSequencePointKind_Normal, 0, 184 },
	{ 109664, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 185 },
	{ 109664, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 186 },
	{ 109664, 4, 2385, 2385, 43, 44, 0, kSequencePointKind_Normal, 0, 187 },
	{ 109664, 4, 2385, 2385, 45, 89, 1, kSequencePointKind_Normal, 0, 188 },
	{ 109664, 4, 2385, 2385, 45, 89, 4, kSequencePointKind_StepOut, 0, 189 },
	{ 109664, 4, 2385, 2385, 90, 91, 13, kSequencePointKind_Normal, 0, 190 },
	{ 109665, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 191 },
	{ 109665, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 192 },
	{ 109665, 4, 2391, 2391, 45, 46, 0, kSequencePointKind_Normal, 0, 193 },
	{ 109665, 4, 2391, 2391, 47, 87, 1, kSequencePointKind_Normal, 0, 194 },
	{ 109665, 4, 2391, 2391, 47, 87, 7, kSequencePointKind_StepOut, 0, 195 },
	{ 109665, 4, 2391, 2391, 47, 87, 18, kSequencePointKind_StepOut, 0, 196 },
	{ 109665, 4, 2391, 2391, 47, 87, 23, kSequencePointKind_StepOut, 0, 197 },
	{ 109665, 4, 2391, 2391, 88, 89, 31, kSequencePointKind_Normal, 0, 198 },
	{ 109666, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 199 },
	{ 109666, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 200 },
	{ 109666, 4, 2685, 2685, 51, 52, 0, kSequencePointKind_Normal, 0, 201 },
	{ 109666, 4, 2685, 2685, 53, 103, 1, kSequencePointKind_Normal, 0, 202 },
	{ 109666, 4, 2685, 2685, 53, 103, 7, kSequencePointKind_StepOut, 0, 203 },
	{ 109666, 4, 2685, 2685, 104, 105, 16, kSequencePointKind_Normal, 0, 204 },
	{ 109667, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 205 },
	{ 109667, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 206 },
	{ 109667, 4, 3175, 3175, 43, 44, 0, kSequencePointKind_Normal, 0, 207 },
	{ 109667, 4, 3175, 3175, 45, 86, 1, kSequencePointKind_Normal, 0, 208 },
	{ 109667, 4, 3175, 3175, 45, 86, 4, kSequencePointKind_StepOut, 0, 209 },
	{ 109667, 4, 3175, 3175, 87, 88, 13, kSequencePointKind_Normal, 0, 210 },
	{ 109668, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 211 },
	{ 109668, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 212 },
	{ 109668, 4, 3405, 3405, 46, 47, 0, kSequencePointKind_Normal, 0, 213 },
	{ 109668, 4, 3405, 3405, 48, 71, 1, kSequencePointKind_Normal, 0, 214 },
	{ 109668, 4, 3405, 3405, 48, 71, 3, kSequencePointKind_StepOut, 0, 215 },
	{ 109668, 4, 3405, 3405, 48, 71, 8, kSequencePointKind_StepOut, 0, 216 },
	{ 109668, 4, 3405, 3405, 72, 73, 16, kSequencePointKind_Normal, 0, 217 },
	{ 109669, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 218 },
	{ 109669, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 219 },
	{ 109669, 4, 3411, 3411, 46, 47, 0, kSequencePointKind_Normal, 0, 220 },
	{ 109669, 4, 3411, 3411, 48, 71, 1, kSequencePointKind_Normal, 0, 221 },
	{ 109669, 4, 3411, 3411, 48, 71, 3, kSequencePointKind_StepOut, 0, 222 },
	{ 109669, 4, 3411, 3411, 48, 71, 8, kSequencePointKind_StepOut, 0, 223 },
	{ 109669, 4, 3411, 3411, 72, 73, 16, kSequencePointKind_Normal, 0, 224 },
	{ 109670, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 225 },
	{ 109670, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 226 },
	{ 109670, 4, 3507, 3507, 58, 59, 0, kSequencePointKind_Normal, 0, 227 },
	{ 109670, 4, 3507, 3507, 60, 81, 1, kSequencePointKind_Normal, 0, 228 },
	{ 109670, 4, 3507, 3507, 60, 81, 3, kSequencePointKind_StepOut, 0, 229 },
	{ 109670, 4, 3507, 3507, 60, 81, 8, kSequencePointKind_StepOut, 0, 230 },
	{ 109670, 4, 3507, 3507, 82, 83, 16, kSequencePointKind_Normal, 0, 231 },
	{ 109671, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 232 },
	{ 109671, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 233 },
	{ 109671, 4, 3514, 3514, 58, 59, 0, kSequencePointKind_Normal, 0, 234 },
	{ 109671, 4, 3514, 3514, 60, 81, 1, kSequencePointKind_Normal, 0, 235 },
	{ 109671, 4, 3514, 3514, 60, 81, 3, kSequencePointKind_StepOut, 0, 236 },
	{ 109671, 4, 3514, 3514, 60, 81, 8, kSequencePointKind_StepOut, 0, 237 },
	{ 109671, 4, 3514, 3514, 82, 83, 16, kSequencePointKind_Normal, 0, 238 },
	{ 109672, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 239 },
	{ 109672, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 240 },
	{ 109672, 4, 4820, 4820, 40, 41, 0, kSequencePointKind_Normal, 0, 241 },
	{ 109672, 4, 4820, 4820, 42, 64, 1, kSequencePointKind_Normal, 0, 242 },
	{ 109672, 4, 4820, 4820, 42, 64, 2, kSequencePointKind_StepOut, 0, 243 },
	{ 109672, 4, 4820, 4820, 65, 66, 10, kSequencePointKind_Normal, 0, 244 },
	{ 109673, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 245 },
	{ 109673, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 246 },
	{ 109673, 4, 4846, 4846, 9, 10, 0, kSequencePointKind_Normal, 0, 247 },
	{ 109673, 4, 4847, 4847, 13, 24, 1, kSequencePointKind_Normal, 0, 248 },
	{ 109673, 4, 4847, 4847, 0, 0, 6, kSequencePointKind_Normal, 0, 249 },
	{ 109673, 4, 4848, 4848, 17, 27, 9, kSequencePointKind_Normal, 0, 250 },
	{ 109673, 4, 4850, 4850, 13, 33, 14, kSequencePointKind_Normal, 0, 251 },
	{ 109673, 4, 4851, 4851, 13, 51, 30, kSequencePointKind_Normal, 0, 252 },
	{ 109673, 4, 4852, 4852, 13, 49, 49, kSequencePointKind_Normal, 0, 253 },
	{ 109673, 4, 4853, 4853, 13, 53, 69, kSequencePointKind_Normal, 0, 254 },
	{ 109673, 4, 4854, 4854, 9, 10, 88, kSequencePointKind_Normal, 0, 255 },
	{ 109674, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 256 },
	{ 109674, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 257 },
	{ 109674, 4, 4913, 4913, 40, 41, 0, kSequencePointKind_Normal, 0, 258 },
	{ 109674, 4, 4913, 4913, 42, 64, 1, kSequencePointKind_Normal, 0, 259 },
	{ 109674, 4, 4913, 4913, 42, 64, 2, kSequencePointKind_StepOut, 0, 260 },
	{ 109674, 4, 4913, 4913, 65, 66, 10, kSequencePointKind_Normal, 0, 261 },
	{ 109675, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 262 },
	{ 109675, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 263 },
	{ 109675, 4, 4967, 4967, 9, 10, 0, kSequencePointKind_Normal, 0, 264 },
	{ 109675, 4, 4968, 4968, 13, 24, 1, kSequencePointKind_Normal, 0, 265 },
	{ 109675, 4, 4968, 4968, 0, 0, 6, kSequencePointKind_Normal, 0, 266 },
	{ 109675, 4, 4969, 4969, 17, 27, 9, kSequencePointKind_Normal, 0, 267 },
	{ 109675, 4, 4971, 4971, 13, 27, 14, kSequencePointKind_Normal, 0, 268 },
	{ 109675, 4, 4973, 4973, 13, 33, 22, kSequencePointKind_Normal, 0, 269 },
	{ 109675, 4, 4974, 4974, 13, 51, 38, kSequencePointKind_Normal, 0, 270 },
	{ 109675, 4, 4975, 4975, 13, 49, 57, kSequencePointKind_Normal, 0, 271 },
	{ 109675, 4, 4976, 4976, 13, 53, 77, kSequencePointKind_Normal, 0, 272 },
	{ 109675, 4, 4977, 4977, 9, 10, 96, kSequencePointKind_Normal, 0, 273 },
	{ 109676, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 274 },
	{ 109676, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 275 },
	{ 109676, 4, 5314, 5314, 9, 10, 0, kSequencePointKind_Normal, 0, 276 },
	{ 109676, 4, 5315, 5315, 13, 20, 1, kSequencePointKind_Normal, 0, 277 },
	{ 109676, 4, 5316, 5316, 13, 25, 6, kSequencePointKind_Normal, 0, 278 },
	{ 109676, 4, 5317, 5317, 13, 25, 13, kSequencePointKind_Normal, 0, 279 },
	{ 109676, 4, 5318, 5318, 13, 25, 20, kSequencePointKind_Normal, 0, 280 },
	{ 109676, 4, 5319, 5319, 13, 25, 27, kSequencePointKind_Normal, 0, 281 },
	{ 109676, 4, 5320, 5320, 13, 26, 34, kSequencePointKind_Normal, 0, 282 },
	{ 109676, 4, 5321, 5321, 13, 26, 42, kSequencePointKind_Normal, 0, 283 },
	{ 109676, 4, 5322, 5322, 9, 10, 48, kSequencePointKind_Normal, 0, 284 },
	{ 109677, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 285 },
	{ 109677, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 286 },
	{ 109677, 4, 5947, 5947, 42, 43, 0, kSequencePointKind_Normal, 0, 287 },
	{ 109677, 4, 5947, 5947, 44, 61, 1, kSequencePointKind_Normal, 0, 288 },
	{ 109677, 4, 5947, 5947, 62, 63, 17, kSequencePointKind_Normal, 0, 289 },
	{ 109678, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 290 },
	{ 109678, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 291 },
	{ 109678, 4, 5953, 5953, 42, 43, 0, kSequencePointKind_Normal, 0, 292 },
	{ 109678, 4, 5953, 5953, 44, 67, 1, kSequencePointKind_Normal, 0, 293 },
	{ 109678, 4, 5953, 5953, 68, 69, 24, kSequencePointKind_Normal, 0, 294 },
	{ 109679, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 295 },
	{ 109679, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 296 },
	{ 109679, 4, 5959, 5959, 42, 43, 0, kSequencePointKind_Normal, 0, 297 },
	{ 109679, 4, 5959, 5959, 44, 73, 1, kSequencePointKind_Normal, 0, 298 },
	{ 109679, 4, 5959, 5959, 74, 75, 31, kSequencePointKind_Normal, 0, 299 },
	{ 109680, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 300 },
	{ 109680, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 301 },
	{ 109680, 5, 881, 881, 51, 52, 0, kSequencePointKind_Normal, 0, 302 },
	{ 109680, 5, 881, 881, 53, 76, 1, kSequencePointKind_Normal, 0, 303 },
	{ 109680, 5, 881, 881, 53, 76, 3, kSequencePointKind_StepOut, 0, 304 },
	{ 109680, 5, 881, 881, 77, 78, 11, kSequencePointKind_Normal, 0, 305 },
	{ 109681, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 306 },
	{ 109681, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 307 },
	{ 109681, 5, 948, 948, 9, 10, 0, kSequencePointKind_Normal, 0, 308 },
	{ 109681, 5, 949, 949, 13, 76, 1, kSequencePointKind_Normal, 0, 309 },
	{ 109681, 5, 949, 949, 13, 76, 12, kSequencePointKind_StepOut, 0, 310 },
	{ 109681, 5, 949, 949, 13, 76, 17, kSequencePointKind_StepOut, 0, 311 },
	{ 109681, 5, 949, 949, 13, 76, 22, kSequencePointKind_StepOut, 0, 312 },
	{ 109681, 5, 950, 950, 9, 10, 36, kSequencePointKind_Normal, 0, 313 },
	{ 109682, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 314 },
	{ 109682, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 315 },
	{ 109682, 6, 1741, 1741, 59, 60, 0, kSequencePointKind_Normal, 0, 316 },
	{ 109682, 6, 1741, 1741, 61, 87, 1, kSequencePointKind_Normal, 0, 317 },
	{ 109682, 6, 1741, 1741, 61, 87, 4, kSequencePointKind_StepOut, 0, 318 },
	{ 109682, 6, 1741, 1741, 88, 89, 12, kSequencePointKind_Normal, 0, 319 },
	{ 109683, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 320 },
	{ 109683, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 321 },
	{ 109683, 6, 1822, 1822, 9, 10, 0, kSequencePointKind_Normal, 0, 322 },
	{ 109683, 6, 1823, 1823, 13, 89, 1, kSequencePointKind_Normal, 0, 323 },
	{ 109683, 6, 1823, 1823, 13, 89, 17, kSequencePointKind_StepOut, 0, 324 },
	{ 109683, 6, 1823, 1823, 13, 89, 22, kSequencePointKind_StepOut, 0, 325 },
	{ 109683, 6, 1823, 1823, 13, 89, 27, kSequencePointKind_StepOut, 0, 326 },
	{ 109683, 6, 1824, 1824, 9, 10, 41, kSequencePointKind_Normal, 0, 327 },
	{ 109684, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 328 },
	{ 109684, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 329 },
	{ 109684, 7, 3878, 3878, 67, 68, 0, kSequencePointKind_Normal, 0, 330 },
	{ 109684, 7, 3878, 3878, 69, 98, 1, kSequencePointKind_Normal, 0, 331 },
	{ 109684, 7, 3878, 3878, 69, 98, 5, kSequencePointKind_StepOut, 0, 332 },
	{ 109684, 7, 3878, 3878, 99, 100, 13, kSequencePointKind_Normal, 0, 333 },
	{ 109685, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 334 },
	{ 109685, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 335 },
	{ 109685, 7, 3990, 3990, 9, 10, 0, kSequencePointKind_Normal, 0, 336 },
	{ 109685, 7, 3991, 3991, 13, 102, 1, kSequencePointKind_Normal, 0, 337 },
	{ 109685, 7, 3991, 3991, 13, 102, 22, kSequencePointKind_StepOut, 0, 338 },
	{ 109685, 7, 3991, 3991, 13, 102, 27, kSequencePointKind_StepOut, 0, 339 },
	{ 109685, 7, 3991, 3991, 13, 102, 32, kSequencePointKind_StepOut, 0, 340 },
	{ 109685, 7, 3992, 3992, 9, 10, 46, kSequencePointKind_Normal, 0, 341 },
	{ 109686, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 342 },
	{ 109686, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 343 },
	{ 109686, 1, 37, 37, 9, 10, 0, kSequencePointKind_Normal, 0, 344 },
	{ 109686, 1, 38, 38, 13, 24, 1, kSequencePointKind_Normal, 0, 345 },
	{ 109686, 1, 39, 39, 13, 24, 8, kSequencePointKind_Normal, 0, 346 },
	{ 109686, 1, 40, 40, 9, 10, 15, kSequencePointKind_Normal, 0, 347 },
	{ 109687, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 348 },
	{ 109687, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 349 },
	{ 109687, 1, 91, 91, 9, 10, 0, kSequencePointKind_Normal, 0, 350 },
	{ 109687, 1, 92, 92, 13, 26, 1, kSequencePointKind_Normal, 0, 351 },
	{ 109687, 1, 93, 93, 13, 26, 14, kSequencePointKind_Normal, 0, 352 },
	{ 109687, 1, 94, 94, 9, 10, 27, kSequencePointKind_Normal, 0, 353 },
	{ 109688, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 354 },
	{ 109688, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 355 },
	{ 109688, 1, 179, 179, 56, 57, 0, kSequencePointKind_Normal, 0, 356 },
	{ 109688, 1, 179, 179, 58, 79, 1, kSequencePointKind_Normal, 0, 357 },
	{ 109688, 1, 179, 179, 58, 79, 2, kSequencePointKind_StepOut, 0, 358 },
	{ 109688, 1, 179, 179, 80, 81, 10, kSequencePointKind_Normal, 0, 359 },
	{ 109689, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 360 },
	{ 109689, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 361 },
	{ 109689, 1, 223, 223, 66, 67, 0, kSequencePointKind_Normal, 0, 362 },
	{ 109689, 1, 223, 223, 68, 117, 1, kSequencePointKind_Normal, 0, 363 },
	{ 109689, 1, 223, 223, 68, 117, 27, kSequencePointKind_StepOut, 0, 364 },
	{ 109689, 1, 223, 223, 118, 119, 35, kSequencePointKind_Normal, 0, 365 },
	{ 109690, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 366 },
	{ 109690, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 367 },
	{ 109690, 1, 230, 230, 65, 66, 0, kSequencePointKind_Normal, 0, 368 },
	{ 109690, 1, 230, 230, 67, 112, 1, kSequencePointKind_Normal, 0, 369 },
	{ 109690, 1, 230, 230, 67, 112, 17, kSequencePointKind_StepOut, 0, 370 },
	{ 109690, 1, 230, 230, 113, 114, 25, kSequencePointKind_Normal, 0, 371 },
	{ 109691, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 372 },
	{ 109691, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 373 },
	{ 109691, 1, 237, 237, 65, 66, 0, kSequencePointKind_Normal, 0, 374 },
	{ 109691, 1, 237, 237, 67, 112, 1, kSequencePointKind_Normal, 0, 375 },
	{ 109691, 1, 237, 237, 67, 112, 17, kSequencePointKind_StepOut, 0, 376 },
	{ 109691, 1, 237, 237, 113, 114, 25, kSequencePointKind_Normal, 0, 377 },
	{ 109692, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 378 },
	{ 109692, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 379 },
	{ 109692, 1, 245, 245, 66, 67, 0, kSequencePointKind_Normal, 0, 380 },
	{ 109692, 1, 245, 245, 68, 117, 1, kSequencePointKind_Normal, 0, 381 },
	{ 109692, 1, 245, 245, 68, 117, 27, kSequencePointKind_StepOut, 0, 382 },
	{ 109692, 1, 245, 245, 118, 119, 35, kSequencePointKind_Normal, 0, 383 },
	{ 109693, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 384 },
	{ 109693, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 385 },
	{ 109693, 1, 267, 267, 66, 67, 0, kSequencePointKind_Normal, 0, 386 },
	{ 109693, 1, 267, 267, 68, 117, 1, kSequencePointKind_Normal, 0, 387 },
	{ 109693, 1, 267, 267, 68, 117, 27, kSequencePointKind_StepOut, 0, 388 },
	{ 109693, 1, 267, 267, 118, 119, 35, kSequencePointKind_Normal, 0, 389 },
	{ 109694, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 390 },
	{ 109694, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 391 },
	{ 109694, 1, 772, 772, 40, 41, 0, kSequencePointKind_Normal, 0, 392 },
	{ 109694, 1, 772, 772, 42, 74, 1, kSequencePointKind_Normal, 0, 393 },
	{ 109694, 1, 772, 772, 75, 76, 35, kSequencePointKind_Normal, 0, 394 },
	{ 109695, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 395 },
	{ 109695, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 396 },
	{ 109695, 1, 777, 777, 47, 48, 0, kSequencePointKind_Normal, 0, 397 },
	{ 109695, 1, 777, 777, 49, 99, 1, kSequencePointKind_Normal, 0, 398 },
	{ 109695, 1, 777, 777, 49, 99, 18, kSequencePointKind_StepOut, 0, 399 },
	{ 109695, 1, 777, 777, 100, 101, 29, kSequencePointKind_Normal, 0, 400 },
	{ 109696, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 401 },
	{ 109696, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 402 },
	{ 109696, 1, 783, 783, 43, 44, 0, kSequencePointKind_Normal, 0, 403 },
	{ 109696, 1, 783, 783, 45, 73, 1, kSequencePointKind_Normal, 0, 404 },
	{ 109696, 1, 783, 783, 45, 73, 7, kSequencePointKind_StepOut, 0, 405 },
	{ 109696, 1, 783, 783, 74, 75, 15, kSequencePointKind_Normal, 0, 406 },
	{ 109697, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 407 },
	{ 109697, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 408 },
	{ 109697, 1, 790, 790, 9, 10, 0, kSequencePointKind_Normal, 0, 409 },
	{ 109697, 1, 791, 791, 13, 62, 1, kSequencePointKind_Normal, 0, 410 },
	{ 109697, 1, 791, 791, 13, 62, 28, kSequencePointKind_StepOut, 0, 411 },
	{ 109697, 1, 792, 792, 9, 10, 36, kSequencePointKind_Normal, 0, 412 },
	{ 109698, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 413 },
	{ 109698, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 414 },
	{ 109698, 1, 800, 800, 9, 10, 0, kSequencePointKind_Normal, 0, 415 },
	{ 109698, 1, 801, 801, 13, 128, 1, kSequencePointKind_Normal, 0, 416 },
	{ 109698, 1, 801, 801, 13, 128, 14, kSequencePointKind_StepOut, 0, 417 },
	{ 109698, 1, 801, 801, 13, 128, 27, kSequencePointKind_StepOut, 0, 418 },
	{ 109698, 1, 801, 801, 13, 128, 32, kSequencePointKind_StepOut, 0, 419 },
	{ 109698, 1, 802, 802, 9, 10, 40, kSequencePointKind_Normal, 0, 420 },
	{ 109699, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 421 },
	{ 109699, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 422 },
	{ 109699, 8, 22, 22, 63, 64, 0, kSequencePointKind_Normal, 0, 423 },
	{ 109699, 8, 22, 22, 65, 93, 1, kSequencePointKind_Normal, 0, 424 },
	{ 109699, 8, 22, 22, 65, 93, 13, kSequencePointKind_StepOut, 0, 425 },
	{ 109699, 8, 22, 22, 94, 95, 21, kSequencePointKind_Normal, 0, 426 },
	{ 109700, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 427 },
	{ 109700, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 428 },
	{ 109700, 1, 808, 808, 13, 43, 0, kSequencePointKind_Normal, 0, 429 },
	{ 109700, 1, 808, 808, 13, 43, 1, kSequencePointKind_StepOut, 0, 430 },
	{ 109700, 1, 809, 809, 13, 14, 7, kSequencePointKind_Normal, 0, 431 },
	{ 109700, 1, 810, 810, 17, 25, 8, kSequencePointKind_Normal, 0, 432 },
	{ 109700, 1, 811, 811, 17, 25, 20, kSequencePointKind_Normal, 0, 433 },
	{ 109700, 1, 812, 812, 13, 14, 32, kSequencePointKind_Normal, 0, 434 },
	{ 109701, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 435 },
	{ 109701, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 436 },
	{ 109701, 2, 40, 40, 9, 10, 0, kSequencePointKind_Normal, 0, 437 },
	{ 109701, 2, 41, 41, 13, 24, 1, kSequencePointKind_Normal, 0, 438 },
	{ 109701, 2, 42, 42, 13, 24, 8, kSequencePointKind_Normal, 0, 439 },
	{ 109701, 2, 43, 43, 13, 24, 15, kSequencePointKind_Normal, 0, 440 },
	{ 109701, 2, 44, 44, 9, 10, 22, kSequencePointKind_Normal, 0, 441 },
	{ 109702, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 442 },
	{ 109702, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 443 },
	{ 109702, 2, 275, 275, 65, 66, 0, kSequencePointKind_Normal, 0, 444 },
	{ 109702, 2, 275, 275, 67, 125, 1, kSequencePointKind_Normal, 0, 445 },
	{ 109702, 2, 275, 275, 67, 125, 25, kSequencePointKind_StepOut, 0, 446 },
	{ 109702, 2, 275, 275, 126, 127, 33, kSequencePointKind_Normal, 0, 447 },
	{ 109703, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 448 },
	{ 109703, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 449 },
	{ 109703, 2, 283, 283, 66, 67, 0, kSequencePointKind_Normal, 0, 450 },
	{ 109703, 2, 283, 283, 68, 132, 1, kSequencePointKind_Normal, 0, 451 },
	{ 109703, 2, 283, 283, 68, 132, 40, kSequencePointKind_StepOut, 0, 452 },
	{ 109703, 2, 283, 283, 133, 134, 48, kSequencePointKind_Normal, 0, 453 },
	{ 109704, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 454 },
	{ 109704, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 455 },
	{ 109704, 2, 305, 305, 66, 67, 0, kSequencePointKind_Normal, 0, 456 },
	{ 109704, 2, 305, 305, 68, 132, 1, kSequencePointKind_Normal, 0, 457 },
	{ 109704, 2, 305, 305, 68, 132, 40, kSequencePointKind_StepOut, 0, 458 },
	{ 109704, 2, 305, 305, 133, 134, 48, kSequencePointKind_Normal, 0, 459 },
	{ 109705, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 460 },
	{ 109705, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 461 },
	{ 109705, 2, 1526, 1526, 17, 18, 0, kSequencePointKind_Normal, 0, 462 },
	{ 109705, 2, 1526, 1526, 19, 43, 1, kSequencePointKind_Normal, 0, 463 },
	{ 109705, 2, 1526, 1526, 19, 43, 13, kSequencePointKind_StepOut, 0, 464 },
	{ 109705, 2, 1526, 1526, 44, 45, 21, kSequencePointKind_Normal, 0, 465 },
	{ 109706, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 466 },
	{ 109706, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 467 },
	{ 109706, 2, 1631, 1631, 40, 41, 0, kSequencePointKind_Normal, 0, 468 },
	{ 109706, 2, 1631, 1631, 42, 88, 1, kSequencePointKind_Normal, 0, 469 },
	{ 109706, 2, 1631, 1631, 89, 90, 49, kSequencePointKind_Normal, 0, 470 },
	{ 109707, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 471 },
	{ 109707, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 472 },
	{ 109707, 2, 1636, 1636, 47, 48, 0, kSequencePointKind_Normal, 0, 473 },
	{ 109707, 2, 1636, 1636, 49, 99, 1, kSequencePointKind_Normal, 0, 474 },
	{ 109707, 2, 1636, 1636, 49, 99, 18, kSequencePointKind_StepOut, 0, 475 },
	{ 109707, 2, 1636, 1636, 100, 101, 29, kSequencePointKind_Normal, 0, 476 },
	{ 109708, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 477 },
	{ 109708, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 478 },
	{ 109708, 2, 1642, 1642, 43, 44, 0, kSequencePointKind_Normal, 0, 479 },
	{ 109708, 2, 1642, 1642, 45, 73, 1, kSequencePointKind_Normal, 0, 480 },
	{ 109708, 2, 1642, 1642, 45, 73, 7, kSequencePointKind_StepOut, 0, 481 },
	{ 109708, 2, 1642, 1642, 74, 75, 15, kSequencePointKind_Normal, 0, 482 },
	{ 109709, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 483 },
	{ 109709, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 484 },
	{ 109709, 2, 1649, 1649, 9, 10, 0, kSequencePointKind_Normal, 0, 485 },
	{ 109709, 2, 1650, 1650, 13, 71, 1, kSequencePointKind_Normal, 0, 486 },
	{ 109709, 2, 1650, 1650, 13, 71, 39, kSequencePointKind_StepOut, 0, 487 },
	{ 109709, 2, 1651, 1651, 9, 10, 47, kSequencePointKind_Normal, 0, 488 },
	{ 109710, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 489 },
	{ 109710, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 490 },
	{ 109710, 2, 1659, 1659, 9, 10, 0, kSequencePointKind_Normal, 0, 491 },
	{ 109710, 2, 1660, 1660, 13, 170, 1, kSequencePointKind_Normal, 0, 492 },
	{ 109710, 2, 1660, 1660, 13, 170, 14, kSequencePointKind_StepOut, 0, 493 },
	{ 109710, 2, 1660, 1660, 13, 170, 27, kSequencePointKind_StepOut, 0, 494 },
	{ 109710, 2, 1660, 1660, 13, 170, 40, kSequencePointKind_StepOut, 0, 495 },
	{ 109710, 2, 1660, 1660, 13, 170, 45, kSequencePointKind_StepOut, 0, 496 },
	{ 109710, 2, 1661, 1661, 9, 10, 53, kSequencePointKind_Normal, 0, 497 },
	{ 109711, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 498 },
	{ 109711, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 499 },
	{ 109711, 8, 39, 39, 63, 64, 0, kSequencePointKind_Normal, 0, 500 },
	{ 109711, 8, 39, 39, 65, 98, 1, kSequencePointKind_Normal, 0, 501 },
	{ 109711, 8, 39, 39, 65, 98, 19, kSequencePointKind_StepOut, 0, 502 },
	{ 109711, 8, 39, 39, 99, 100, 27, kSequencePointKind_Normal, 0, 503 },
	{ 109712, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 504 },
	{ 109712, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 505 },
	{ 109712, 2, 1668, 1668, 13, 43, 0, kSequencePointKind_Normal, 0, 506 },
	{ 109712, 2, 1668, 1668, 13, 43, 1, kSequencePointKind_StepOut, 0, 507 },
	{ 109712, 2, 1669, 1669, 13, 14, 7, kSequencePointKind_Normal, 0, 508 },
	{ 109712, 2, 1670, 1670, 17, 25, 8, kSequencePointKind_Normal, 0, 509 },
	{ 109712, 2, 1671, 1671, 17, 25, 20, kSequencePointKind_Normal, 0, 510 },
	{ 109712, 2, 1672, 1672, 17, 25, 32, kSequencePointKind_Normal, 0, 511 },
	{ 109712, 2, 1673, 1673, 13, 14, 44, kSequencePointKind_Normal, 0, 512 },
	{ 109713, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 513 },
	{ 109713, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 514 },
	{ 109713, 3, 37, 37, 9, 10, 0, kSequencePointKind_Normal, 0, 515 },
	{ 109713, 3, 38, 38, 13, 24, 1, kSequencePointKind_Normal, 0, 516 },
	{ 109713, 3, 39, 39, 13, 24, 8, kSequencePointKind_Normal, 0, 517 },
	{ 109713, 3, 40, 40, 9, 10, 15, kSequencePointKind_Normal, 0, 518 },
	{ 109714, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 519 },
	{ 109714, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 520 },
	{ 109714, 3, 109, 109, 9, 10, 0, kSequencePointKind_Normal, 0, 521 },
	{ 109714, 3, 110, 110, 13, 31, 1, kSequencePointKind_Normal, 0, 522 },
	{ 109714, 3, 111, 111, 13, 31, 14, kSequencePointKind_Normal, 0, 523 },
	{ 109714, 3, 112, 112, 9, 10, 27, kSequencePointKind_Normal, 0, 524 },
	{ 109715, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 525 },
	{ 109715, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 526 },
	{ 109715, 3, 173, 173, 56, 57, 0, kSequencePointKind_Normal, 0, 527 },
	{ 109715, 3, 173, 173, 58, 77, 1, kSequencePointKind_Normal, 0, 528 },
	{ 109715, 3, 173, 173, 58, 77, 2, kSequencePointKind_StepOut, 0, 529 },
	{ 109715, 3, 173, 173, 78, 79, 10, kSequencePointKind_Normal, 0, 530 },
	{ 109716, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 531 },
	{ 109716, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 532 },
	{ 109716, 3, 829, 829, 38, 39, 0, kSequencePointKind_Normal, 0, 533 },
	{ 109716, 3, 829, 829, 40, 72, 1, kSequencePointKind_Normal, 0, 534 },
	{ 109716, 3, 829, 829, 73, 74, 35, kSequencePointKind_Normal, 0, 535 },
	{ 109717, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 536 },
	{ 109717, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 537 },
	{ 109717, 3, 834, 834, 47, 48, 0, kSequencePointKind_Normal, 0, 538 },
	{ 109717, 3, 834, 834, 49, 97, 1, kSequencePointKind_Normal, 0, 539 },
	{ 109717, 3, 834, 834, 49, 97, 18, kSequencePointKind_StepOut, 0, 540 },
	{ 109717, 3, 834, 834, 98, 99, 29, kSequencePointKind_Normal, 0, 541 },
	{ 109718, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 542 },
	{ 109718, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 543 },
	{ 109718, 3, 840, 840, 43, 44, 0, kSequencePointKind_Normal, 0, 544 },
	{ 109718, 3, 840, 840, 45, 73, 1, kSequencePointKind_Normal, 0, 545 },
	{ 109718, 3, 840, 840, 45, 73, 7, kSequencePointKind_StepOut, 0, 546 },
	{ 109718, 3, 840, 840, 74, 75, 15, kSequencePointKind_Normal, 0, 547 },
	{ 109719, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 548 },
	{ 109719, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 549 },
	{ 109719, 3, 847, 847, 9, 10, 0, kSequencePointKind_Normal, 0, 550 },
	{ 109719, 3, 848, 848, 13, 58, 1, kSequencePointKind_Normal, 0, 551 },
	{ 109719, 3, 848, 848, 13, 58, 28, kSequencePointKind_StepOut, 0, 552 },
	{ 109719, 3, 849, 849, 9, 10, 36, kSequencePointKind_Normal, 0, 553 },
	{ 109720, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 554 },
	{ 109720, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 555 },
	{ 109720, 3, 857, 857, 9, 10, 0, kSequencePointKind_Normal, 0, 556 },
	{ 109720, 3, 858, 858, 13, 124, 1, kSequencePointKind_Normal, 0, 557 },
	{ 109720, 3, 858, 858, 13, 124, 14, kSequencePointKind_StepOut, 0, 558 },
	{ 109720, 3, 858, 858, 13, 124, 27, kSequencePointKind_StepOut, 0, 559 },
	{ 109720, 3, 858, 858, 13, 124, 32, kSequencePointKind_StepOut, 0, 560 },
	{ 109720, 3, 859, 859, 9, 10, 40, kSequencePointKind_Normal, 0, 561 },
	{ 109721, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 562 },
	{ 109721, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 563 },
	{ 109721, 3, 865, 865, 13, 41, 0, kSequencePointKind_Normal, 0, 564 },
	{ 109721, 3, 865, 865, 13, 41, 1, kSequencePointKind_StepOut, 0, 565 },
	{ 109721, 3, 866, 866, 13, 14, 7, kSequencePointKind_Normal, 0, 566 },
	{ 109721, 3, 867, 867, 17, 25, 8, kSequencePointKind_Normal, 0, 567 },
	{ 109721, 3, 868, 868, 17, 25, 20, kSequencePointKind_Normal, 0, 568 },
	{ 109721, 3, 869, 869, 13, 14, 32, kSequencePointKind_Normal, 0, 569 },
	{ 109722, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 570 },
	{ 109722, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 571 },
	{ 109722, 5, 37, 37, 9, 10, 0, kSequencePointKind_Normal, 0, 572 },
	{ 109722, 5, 38, 38, 13, 24, 1, kSequencePointKind_Normal, 0, 573 },
	{ 109722, 5, 39, 39, 13, 24, 8, kSequencePointKind_Normal, 0, 574 },
	{ 109722, 5, 40, 40, 9, 10, 15, kSequencePointKind_Normal, 0, 575 },
	{ 109723, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 576 },
	{ 109723, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 577 },
	{ 109723, 5, 193, 193, 63, 64, 0, kSequencePointKind_Normal, 0, 578 },
	{ 109723, 5, 193, 193, 65, 113, 1, kSequencePointKind_Normal, 0, 579 },
	{ 109723, 5, 193, 193, 65, 113, 27, kSequencePointKind_StepOut, 0, 580 },
	{ 109723, 5, 193, 193, 114, 115, 35, kSequencePointKind_Normal, 0, 581 },
	{ 109724, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 582 },
	{ 109724, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 583 },
	{ 109724, 5, 829, 829, 39, 40, 0, kSequencePointKind_Normal, 0, 584 },
	{ 109724, 5, 829, 829, 41, 73, 1, kSequencePointKind_Normal, 0, 585 },
	{ 109724, 5, 829, 829, 74, 75, 35, kSequencePointKind_Normal, 0, 586 },
	{ 109725, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 587 },
	{ 109725, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 588 },
	{ 109725, 5, 834, 834, 47, 48, 0, kSequencePointKind_Normal, 0, 589 },
	{ 109725, 5, 834, 834, 49, 98, 1, kSequencePointKind_Normal, 0, 590 },
	{ 109725, 5, 834, 834, 49, 98, 18, kSequencePointKind_StepOut, 0, 591 },
	{ 109725, 5, 834, 834, 99, 100, 29, kSequencePointKind_Normal, 0, 592 },
	{ 109726, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 593 },
	{ 109726, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 594 },
	{ 109726, 5, 840, 840, 43, 44, 0, kSequencePointKind_Normal, 0, 595 },
	{ 109726, 5, 840, 840, 45, 73, 1, kSequencePointKind_Normal, 0, 596 },
	{ 109726, 5, 840, 840, 45, 73, 7, kSequencePointKind_StepOut, 0, 597 },
	{ 109726, 5, 840, 840, 74, 75, 15, kSequencePointKind_Normal, 0, 598 },
	{ 109727, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 599 },
	{ 109727, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 600 },
	{ 109727, 5, 847, 847, 9, 10, 0, kSequencePointKind_Normal, 0, 601 },
	{ 109727, 5, 848, 848, 13, 59, 1, kSequencePointKind_Normal, 0, 602 },
	{ 109727, 5, 848, 848, 13, 59, 28, kSequencePointKind_StepOut, 0, 603 },
	{ 109727, 5, 849, 849, 9, 10, 36, kSequencePointKind_Normal, 0, 604 },
	{ 109728, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 605 },
	{ 109728, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 606 },
	{ 109728, 5, 857, 857, 9, 10, 0, kSequencePointKind_Normal, 0, 607 },
	{ 109728, 5, 858, 858, 13, 125, 1, kSequencePointKind_Normal, 0, 608 },
	{ 109728, 5, 858, 858, 13, 125, 14, kSequencePointKind_StepOut, 0, 609 },
	{ 109728, 5, 858, 858, 13, 125, 27, kSequencePointKind_StepOut, 0, 610 },
	{ 109728, 5, 858, 858, 13, 125, 32, kSequencePointKind_StepOut, 0, 611 },
	{ 109728, 5, 859, 859, 9, 10, 40, kSequencePointKind_Normal, 0, 612 },
	{ 109729, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 613 },
	{ 109729, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 614 },
	{ 109729, 5, 865, 865, 13, 42, 0, kSequencePointKind_Normal, 0, 615 },
	{ 109729, 5, 865, 865, 13, 42, 1, kSequencePointKind_StepOut, 0, 616 },
	{ 109729, 5, 866, 866, 13, 14, 7, kSequencePointKind_Normal, 0, 617 },
	{ 109729, 5, 867, 867, 17, 25, 8, kSequencePointKind_Normal, 0, 618 },
	{ 109729, 5, 868, 868, 17, 25, 20, kSequencePointKind_Normal, 0, 619 },
	{ 109729, 5, 869, 869, 13, 14, 32, kSequencePointKind_Normal, 0, 620 },
	{ 109730, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 621 },
	{ 109730, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 622 },
	{ 109730, 6, 40, 40, 9, 10, 0, kSequencePointKind_Normal, 0, 623 },
	{ 109730, 6, 41, 41, 13, 24, 1, kSequencePointKind_Normal, 0, 624 },
	{ 109730, 6, 42, 42, 13, 24, 8, kSequencePointKind_Normal, 0, 625 },
	{ 109730, 6, 43, 43, 13, 24, 15, kSequencePointKind_Normal, 0, 626 },
	{ 109730, 6, 44, 44, 9, 10, 22, kSequencePointKind_Normal, 0, 627 },
	{ 109731, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 628 },
	{ 109731, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 629 },
	{ 109731, 6, 229, 229, 63, 64, 0, kSequencePointKind_Normal, 0, 630 },
	{ 109731, 6, 229, 229, 65, 128, 1, kSequencePointKind_Normal, 0, 631 },
	{ 109731, 6, 229, 229, 65, 128, 40, kSequencePointKind_StepOut, 0, 632 },
	{ 109731, 6, 229, 229, 129, 130, 48, kSequencePointKind_Normal, 0, 633 },
	{ 109732, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 634 },
	{ 109732, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 635 },
	{ 109732, 6, 1686, 1686, 39, 40, 0, kSequencePointKind_Normal, 0, 636 },
	{ 109732, 6, 1686, 1686, 41, 87, 1, kSequencePointKind_Normal, 0, 637 },
	{ 109732, 6, 1686, 1686, 88, 89, 49, kSequencePointKind_Normal, 0, 638 },
	{ 109733, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 639 },
	{ 109733, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 640 },
	{ 109733, 6, 1691, 1691, 47, 48, 0, kSequencePointKind_Normal, 0, 641 },
	{ 109733, 6, 1691, 1691, 49, 98, 1, kSequencePointKind_Normal, 0, 642 },
	{ 109733, 6, 1691, 1691, 49, 98, 18, kSequencePointKind_StepOut, 0, 643 },
	{ 109733, 6, 1691, 1691, 99, 100, 29, kSequencePointKind_Normal, 0, 644 },
	{ 109734, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 645 },
	{ 109734, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 646 },
	{ 109734, 6, 1697, 1697, 43, 44, 0, kSequencePointKind_Normal, 0, 647 },
	{ 109734, 6, 1697, 1697, 45, 73, 1, kSequencePointKind_Normal, 0, 648 },
	{ 109734, 6, 1697, 1697, 45, 73, 7, kSequencePointKind_StepOut, 0, 649 },
	{ 109734, 6, 1697, 1697, 74, 75, 15, kSequencePointKind_Normal, 0, 650 },
	{ 109735, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 651 },
	{ 109735, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 652 },
	{ 109735, 6, 1704, 1704, 9, 10, 0, kSequencePointKind_Normal, 0, 653 },
	{ 109735, 6, 1705, 1705, 13, 67, 1, kSequencePointKind_Normal, 0, 654 },
	{ 109735, 6, 1705, 1705, 13, 67, 39, kSequencePointKind_StepOut, 0, 655 },
	{ 109735, 6, 1706, 1706, 9, 10, 47, kSequencePointKind_Normal, 0, 656 },
	{ 109736, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 657 },
	{ 109736, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 658 },
	{ 109736, 6, 1714, 1714, 9, 10, 0, kSequencePointKind_Normal, 0, 659 },
	{ 109736, 6, 1715, 1715, 13, 166, 1, kSequencePointKind_Normal, 0, 660 },
	{ 109736, 6, 1715, 1715, 13, 166, 14, kSequencePointKind_StepOut, 0, 661 },
	{ 109736, 6, 1715, 1715, 13, 166, 27, kSequencePointKind_StepOut, 0, 662 },
	{ 109736, 6, 1715, 1715, 13, 166, 40, kSequencePointKind_StepOut, 0, 663 },
	{ 109736, 6, 1715, 1715, 13, 166, 45, kSequencePointKind_StepOut, 0, 664 },
	{ 109736, 6, 1716, 1716, 9, 10, 53, kSequencePointKind_Normal, 0, 665 },
	{ 109737, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 666 },
	{ 109737, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 667 },
	{ 109737, 6, 1723, 1723, 13, 42, 0, kSequencePointKind_Normal, 0, 668 },
	{ 109737, 6, 1723, 1723, 13, 42, 1, kSequencePointKind_StepOut, 0, 669 },
	{ 109737, 6, 1724, 1724, 13, 14, 7, kSequencePointKind_Normal, 0, 670 },
	{ 109737, 6, 1725, 1725, 17, 25, 8, kSequencePointKind_Normal, 0, 671 },
	{ 109737, 6, 1726, 1726, 17, 25, 20, kSequencePointKind_Normal, 0, 672 },
	{ 109737, 6, 1727, 1727, 17, 25, 32, kSequencePointKind_Normal, 0, 673 },
	{ 109737, 6, 1728, 1728, 13, 14, 44, kSequencePointKind_Normal, 0, 674 },
	{ 109738, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 675 },
	{ 109738, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 676 },
	{ 109738, 7, 43, 43, 9, 10, 0, kSequencePointKind_Normal, 0, 677 },
	{ 109738, 7, 44, 44, 13, 24, 1, kSequencePointKind_Normal, 0, 678 },
	{ 109738, 7, 45, 45, 13, 24, 8, kSequencePointKind_Normal, 0, 679 },
	{ 109738, 7, 46, 46, 13, 24, 15, kSequencePointKind_Normal, 0, 680 },
	{ 109738, 7, 47, 47, 13, 24, 22, kSequencePointKind_Normal, 0, 681 },
	{ 109738, 7, 48, 48, 9, 10, 30, kSequencePointKind_Normal, 0, 682 },
	{ 109739, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 683 },
	{ 109739, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 684 },
	{ 109739, 7, 296, 296, 63, 64, 0, kSequencePointKind_Normal, 0, 685 },
	{ 109739, 7, 296, 296, 65, 143, 1, kSequencePointKind_Normal, 0, 686 },
	{ 109739, 7, 296, 296, 65, 143, 53, kSequencePointKind_StepOut, 0, 687 },
	{ 109739, 7, 296, 296, 144, 145, 61, kSequencePointKind_Normal, 0, 688 },
	{ 109740, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 689 },
	{ 109740, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 690 },
	{ 109740, 7, 3820, 3820, 39, 40, 0, kSequencePointKind_Normal, 0, 691 },
	{ 109740, 7, 3820, 3820, 41, 101, 1, kSequencePointKind_Normal, 0, 692 },
	{ 109740, 7, 3820, 3820, 102, 103, 63, kSequencePointKind_Normal, 0, 693 },
	{ 109741, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 694 },
	{ 109741, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 695 },
	{ 109741, 7, 3825, 3825, 47, 48, 0, kSequencePointKind_Normal, 0, 696 },
	{ 109741, 7, 3825, 3825, 49, 98, 1, kSequencePointKind_Normal, 0, 697 },
	{ 109741, 7, 3825, 3825, 49, 98, 18, kSequencePointKind_StepOut, 0, 698 },
	{ 109741, 7, 3825, 3825, 99, 100, 29, kSequencePointKind_Normal, 0, 699 },
	{ 109742, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 700 },
	{ 109742, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 701 },
	{ 109742, 7, 3831, 3831, 43, 44, 0, kSequencePointKind_Normal, 0, 702 },
	{ 109742, 7, 3831, 3831, 45, 73, 1, kSequencePointKind_Normal, 0, 703 },
	{ 109742, 7, 3831, 3831, 45, 73, 7, kSequencePointKind_StepOut, 0, 704 },
	{ 109742, 7, 3831, 3831, 74, 75, 15, kSequencePointKind_Normal, 0, 705 },
	{ 109743, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 706 },
	{ 109743, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 707 },
	{ 109743, 7, 3838, 3838, 9, 10, 0, kSequencePointKind_Normal, 0, 708 },
	{ 109743, 7, 3839, 3839, 13, 75, 1, kSequencePointKind_Normal, 0, 709 },
	{ 109743, 7, 3839, 3839, 13, 75, 68, kSequencePointKind_StepOut, 0, 710 },
	{ 109743, 7, 3840, 3840, 9, 10, 76, kSequencePointKind_Normal, 0, 711 },
	{ 109744, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 712 },
	{ 109744, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 713 },
	{ 109744, 7, 3848, 3848, 9, 10, 0, kSequencePointKind_Normal, 0, 714 },
	{ 109744, 7, 3849, 3849, 13, 207, 1, kSequencePointKind_Normal, 0, 715 },
	{ 109744, 7, 3849, 3849, 13, 207, 22, kSequencePointKind_StepOut, 0, 716 },
	{ 109744, 7, 3849, 3849, 13, 207, 38, kSequencePointKind_StepOut, 0, 717 },
	{ 109744, 7, 3849, 3849, 13, 207, 54, kSequencePointKind_StepOut, 0, 718 },
	{ 109744, 7, 3849, 3849, 13, 207, 70, kSequencePointKind_StepOut, 0, 719 },
	{ 109744, 7, 3849, 3849, 13, 207, 76, kSequencePointKind_StepOut, 0, 720 },
	{ 109744, 7, 3850, 3850, 9, 10, 84, kSequencePointKind_Normal, 0, 721 },
	{ 109745, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 722 },
	{ 109745, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 723 },
	{ 109745, 7, 3858, 3858, 13, 42, 0, kSequencePointKind_Normal, 0, 724 },
	{ 109745, 7, 3858, 3858, 13, 42, 1, kSequencePointKind_StepOut, 0, 725 },
	{ 109745, 7, 3859, 3859, 13, 14, 7, kSequencePointKind_Normal, 0, 726 },
	{ 109745, 7, 3860, 3860, 17, 25, 8, kSequencePointKind_Normal, 0, 727 },
	{ 109745, 7, 3861, 3861, 17, 25, 20, kSequencePointKind_Normal, 0, 728 },
	{ 109745, 7, 3862, 3862, 17, 25, 32, kSequencePointKind_Normal, 0, 729 },
	{ 109745, 7, 3863, 3863, 17, 25, 44, kSequencePointKind_Normal, 0, 730 },
	{ 109745, 7, 3864, 3864, 13, 14, 56, kSequencePointKind_Normal, 0, 731 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnity_Mathematics[];
Il2CppSequencePoint g_sequencePointsUnity_Mathematics[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/float2.gen.cs", { 31, 164, 26, 206, 77, 179, 29, 102, 14, 184, 2, 114, 45, 158, 77, 5} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/float3.gen.cs", { 197, 136, 9, 222, 115, 246, 74, 30, 200, 224, 160, 133, 203, 79, 82, 64} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/int2.gen.cs", { 0, 15, 10, 222, 188, 252, 151, 231, 17, 187, 66, 153, 239, 27, 19, 112} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/math.cs", { 125, 178, 231, 104, 7, 19, 177, 7, 194, 47, 94, 0, 245, 209, 161, 203} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/uint2.gen.cs", { 248, 142, 113, 28, 15, 64, 202, 74, 96, 153, 1, 39, 4, 49, 138, 155} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/uint3.gen.cs", { 95, 33, 0, 111, 95, 246, 182, 138, 252, 202, 215, 82, 27, 235, 165, 207} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/uint4.gen.cs", { 131, 207, 118, 180, 179, 91, 198, 70, 21, 198, 12, 27, 97, 90, 108, 87} },
{ "./Library/PackageCache/com.unity.mathematics@1.2.6/Unity.Mathematics/math_unity_conversion.cs", { 23, 61, 120, 252, 244, 54, 125, 59, 59, 206, 152, 200, 39, 112, 158, 211} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[21] = 
{
	{ 14100, 1 },
	{ 14100, 2 },
	{ 14100, 3 },
	{ 14100, 4 },
	{ 14100, 5 },
	{ 14100, 6 },
	{ 14100, 7 },
	{ 14102, 1 },
	{ 14102, 8 },
	{ 14101, 1 },
	{ 14104, 2 },
	{ 14104, 8 },
	{ 14103, 2 },
	{ 14106, 3 },
	{ 14105, 3 },
	{ 14108, 5 },
	{ 14107, 5 },
	{ 14110, 6 },
	{ 14109, 6 },
	{ 14112, 7 },
	{ 14111, 7 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[96] = 
{
	{ 0, 43 },
	{ 0, 48 },
	{ 0, 43 },
	{ 0, 28 },
	{ 0, 23 },
	{ 0, 12 },
	{ 0, 33 },
	{ 0, 44 },
	{ 0, 32 },
	{ 0, 12 },
	{ 0, 14 },
	{ 0, 14 },
	{ 0, 22 },
	{ 0, 14 },
	{ 0, 14 },
	{ 0, 22 },
	{ 0, 13 },
	{ 0, 25 },
	{ 0, 25 },
	{ 0, 19 },
	{ 0, 14 },
	{ 0, 23 },
	{ 0, 33 },
	{ 0, 47 },
	{ 0, 14 },
	{ 0, 15 },
	{ 0, 15 },
	{ 0, 33 },
	{ 0, 15 },
	{ 0, 33 },
	{ 0, 18 },
	{ 0, 15 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 18 },
	{ 0, 12 },
	{ 0, 90 },
	{ 0, 12 },
	{ 0, 98 },
	{ 0, 50 },
	{ 0, 19 },
	{ 0, 26 },
	{ 0, 33 },
	{ 0, 13 },
	{ 0, 38 },
	{ 0, 14 },
	{ 0, 43 },
	{ 0, 15 },
	{ 0, 48 },
	{ 0, 12 },
	{ 0, 37 },
	{ 0, 27 },
	{ 0, 27 },
	{ 0, 37 },
	{ 0, 37 },
	{ 0, 37 },
	{ 0, 31 },
	{ 0, 17 },
	{ 0, 38 },
	{ 0, 42 },
	{ 0, 23 },
	{ 0, 35 },
	{ 0, 50 },
	{ 0, 50 },
	{ 0, 23 },
	{ 0, 51 },
	{ 0, 31 },
	{ 0, 17 },
	{ 0, 49 },
	{ 0, 55 },
	{ 0, 29 },
	{ 0, 12 },
	{ 0, 37 },
	{ 0, 31 },
	{ 0, 17 },
	{ 0, 38 },
	{ 0, 42 },
	{ 0, 37 },
	{ 0, 37 },
	{ 0, 31 },
	{ 0, 17 },
	{ 0, 38 },
	{ 0, 42 },
	{ 0, 50 },
	{ 0, 51 },
	{ 0, 31 },
	{ 0, 17 },
	{ 0, 49 },
	{ 0, 55 },
	{ 0, 63 },
	{ 0, 65 },
	{ 0, 31 },
	{ 0, 17 },
	{ 0, 78 },
	{ 0, 86 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[111] = 
{
	{ 0, 0, 0 },
	{ 43, 0, 1 },
	{ 48, 1, 1 },
	{ 43, 2, 1 },
	{ 28, 3, 1 },
	{ 23, 4, 1 },
	{ 12, 5, 1 },
	{ 33, 6, 1 },
	{ 44, 7, 1 },
	{ 32, 8, 1 },
	{ 12, 9, 1 },
	{ 14, 10, 1 },
	{ 14, 11, 1 },
	{ 22, 12, 1 },
	{ 14, 13, 1 },
	{ 14, 14, 1 },
	{ 22, 15, 1 },
	{ 13, 16, 1 },
	{ 25, 17, 1 },
	{ 25, 18, 1 },
	{ 19, 19, 1 },
	{ 14, 20, 1 },
	{ 23, 21, 1 },
	{ 33, 22, 1 },
	{ 47, 23, 1 },
	{ 14, 24, 1 },
	{ 15, 25, 1 },
	{ 15, 26, 1 },
	{ 33, 27, 1 },
	{ 15, 28, 1 },
	{ 33, 29, 1 },
	{ 18, 30, 1 },
	{ 15, 31, 1 },
	{ 18, 32, 1 },
	{ 18, 33, 1 },
	{ 18, 34, 1 },
	{ 18, 35, 1 },
	{ 12, 36, 1 },
	{ 90, 37, 1 },
	{ 12, 38, 1 },
	{ 98, 39, 1 },
	{ 50, 40, 1 },
	{ 19, 41, 1 },
	{ 26, 42, 1 },
	{ 33, 43, 1 },
	{ 13, 44, 1 },
	{ 38, 45, 1 },
	{ 14, 46, 1 },
	{ 43, 47, 1 },
	{ 15, 48, 1 },
	{ 48, 49, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 12, 50, 1 },
	{ 37, 51, 1 },
	{ 27, 52, 1 },
	{ 27, 53, 1 },
	{ 37, 54, 1 },
	{ 37, 55, 1 },
	{ 37, 56, 1 },
	{ 31, 57, 1 },
	{ 17, 58, 1 },
	{ 38, 59, 1 },
	{ 42, 60, 1 },
	{ 23, 61, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 35, 62, 1 },
	{ 50, 63, 1 },
	{ 50, 64, 1 },
	{ 23, 65, 1 },
	{ 51, 66, 1 },
	{ 31, 67, 1 },
	{ 17, 68, 1 },
	{ 49, 69, 1 },
	{ 55, 70, 1 },
	{ 29, 71, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 12, 72, 1 },
	{ 37, 73, 1 },
	{ 31, 74, 1 },
	{ 17, 75, 1 },
	{ 38, 76, 1 },
	{ 42, 77, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 37, 78, 1 },
	{ 37, 79, 1 },
	{ 31, 80, 1 },
	{ 17, 81, 1 },
	{ 38, 82, 1 },
	{ 42, 83, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 50, 84, 1 },
	{ 51, 85, 1 },
	{ 31, 86, 1 },
	{ 17, 87, 1 },
	{ 49, 88, 1 },
	{ 55, 89, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 63, 90, 1 },
	{ 65, 91, 1 },
	{ 31, 92, 1 },
	{ 17, 93, 1 },
	{ 78, 94, 1 },
	{ 86, 95, 1 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnity_Mathematics;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnity_Mathematics = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	732,
	(Il2CppSequencePoint*)g_sequencePointsUnity_Mathematics,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	21,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
