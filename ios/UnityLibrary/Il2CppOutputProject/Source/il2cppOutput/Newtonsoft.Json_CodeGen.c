﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AddingNewEventArgs__ctor_m510FCE6D1FD1EE5C0C6ED30B4601B4C603B951FE (void);
extern void AddingNewEventHandler__ctor_m7B97C57FA693DD577ADB5D33E3C2FFC190C0299C (void);
extern void AddingNewEventHandler_Invoke_mC3901F2396D7A5F52E37CDF496818977C7D1C3E5 (void);
extern void NotifyCollectionChangedEventHandler__ctor_mE24D13BEBB52227B52B8D923C19D4D8DD9F5BA87 (void);
extern void NotifyCollectionChangedEventHandler_Invoke_m2B986CECA0F8AA4138972686389A23370B977D83 (void);
extern void PropertyChangingEventArgs__ctor_m2C90654827EE107B5EF723CDE9B355822B100FAB (void);
extern void PropertyChangingEventArgs_set_PropertyName_m7D2E3D7E0FCADE10125DC4F432562C949CEEA214 (void);
extern void PropertyChangingEventHandler__ctor_m68F00D44CBBE9380C9524A4A3926D56DD2BDEBCB (void);
extern void PropertyChangingEventHandler_Invoke_mB6689480ECA46314553FF17D09EE63C1D4DE1019 (void);
extern void JsonConstructorAttribute__ctor_m81274E33ADDAFF8981B93FCAE6CFD31A91EBEE76 (void);
extern void JsonDictionaryAttribute__ctor_m9EE4679B14BAD6C031A1F61F7A530E5F3DDBCC8A (void);
extern void JsonException__ctor_m133E4DB19926E9CB835F6BDCF47EA910A92252A1 (void);
extern void JsonException__ctor_m29A7A39A820BD2EFF57EC051CB305B86C4B81FA9 (void);
extern void JsonException__ctor_mF25D98C588CF67F15BECFB01FEA535FEC0117CB4 (void);
extern void JsonException__ctor_mF4EA9CF06D3471F221C86E1CF375030528D0090F (void);
extern void JsonExtensionDataAttribute_get_WriteData_m91100F78741EB6CEDB4AB7354EC4085E814FE865 (void);
extern void JsonExtensionDataAttribute_set_WriteData_mDF516C304D65CDAB9EDE6FFA7F69C1BFAD02F564 (void);
extern void JsonExtensionDataAttribute_get_ReadData_m1ACC83CF38CEEA07080EBA48C66F2AD7FFF3C066 (void);
extern void JsonExtensionDataAttribute_set_ReadData_m4C4681A75042DE8C8A35380154BE3A870AD78D1D (void);
extern void JsonExtensionDataAttribute__ctor_mE301AC470F51C0F4FDED2D842B58C785C4FCC6E1 (void);
extern void JsonPosition__ctor_mB2A076189BFC7885AA7876D42D0A612A1119AC90 (void);
extern void JsonPosition_CalculateLength_mF8830A4B38109D0C9EBA7F90CF1DFB70B7E08BA1 (void);
extern void JsonPosition_WriteTo_mB7CDD918232B0711DCB76F31B31FEBCEDFC86659 (void);
extern void JsonPosition_TypeHasIndex_m45B49E936D7FE191B91558E791402617D502516F (void);
extern void JsonPosition_BuildPath_m682C1AE222159C28A7A9ADC8C86B64505C6215D3 (void);
extern void JsonPosition_FormatMessage_mBA84EAA212D7094726694CE707E2A3DADBDE8D6B (void);
extern void JsonPosition__cctor_m6006C6332033D39973517120AE0E43CC7A7D13D3 (void);
extern void JsonRequiredAttribute__ctor_m3EF36A5142593C04D9EC1589DFA7F3FB4EC60FF8 (void);
extern void JsonArrayAttribute__ctor_m298F3229AC19588107B9E3351EED94989176D37E (void);
extern void JsonContainerAttribute_get_ItemConverterType_m9404B4FD78A3B8923849B3C6C4B8BCBF83E9C361 (void);
extern void JsonContainerAttribute_get_ItemConverterParameters_m46394C4C5DABE9534F121F0BCEC02848D94A1F21 (void);
extern void JsonContainerAttribute__ctor_m58831F420FD3D536478054080A1F3C68B00960B4 (void);
extern void JsonConverterAttribute_get_ConverterType_m75C219D874E6C5DA20645156A2E1A26FFF57E035 (void);
extern void JsonConverterAttribute_get_ConverterParameters_mAF5F1D5D27BF42D6362EFDF65BEBBE407BAFD356 (void);
extern void JsonObjectAttribute_get_MemberSerialization_m49450C61989A20414C231E8DFF5D980BF26C645B (void);
extern void JsonObjectAttribute_set_MemberSerialization_m241046E598051AB3A18948986659C085B905C8BF (void);
extern void JsonObjectAttribute__ctor_mB46A4065C794703262BABB64E723EB63D0709DC5 (void);
extern void JsonObjectAttribute__ctor_m6C21AA934018A45AD8D680B8F8931D2558F7EC4F (void);
extern void JsonSerializerSettings_get_ReferenceLoopHandling_m6CD165186AB151BDCACD15E3AB0E10E9CCD9A4D5 (void);
extern void JsonSerializerSettings_get_MissingMemberHandling_m3D682DB3B3BBEACC8F8F03909919CF0C29D41D1A (void);
extern void JsonSerializerSettings_get_ObjectCreationHandling_m323D50EB2D88E661942309B60B7CE067D0D4943F (void);
extern void JsonSerializerSettings_get_NullValueHandling_m1116B9EE497A5CB9B178CD5DF886C73C4CAD32F0 (void);
extern void JsonSerializerSettings_set_NullValueHandling_mC389679010477A90BA5F811621B460004710CF16 (void);
extern void JsonSerializerSettings_get_DefaultValueHandling_mD8D94E521F5739B4332D6DC4FD64A676535F6830 (void);
extern void JsonSerializerSettings_get_Converters_mB7EE43E74FA48980B6C0976D7A2160B2174C8FCA (void);
extern void JsonSerializerSettings_set_Converters_mF3D958F510BA4BBC18E2D2088EF9D3FC839AFB4C (void);
extern void JsonSerializerSettings_get_PreserveReferencesHandling_mEA96432AAD3AF1E1DB77E9ADC937F3B539A14DAE (void);
extern void JsonSerializerSettings_get_TypeNameHandling_mF69B78BB41709BB8E6FAFB975955A86AAEFA9B6F (void);
extern void JsonSerializerSettings_get_MetadataPropertyHandling_m84E3BB4BD1902EE4E071249487325C2547E8D829 (void);
extern void JsonSerializerSettings_get_TypeNameAssemblyFormat_m4DD9BE8274458B72AE03EDBC473D64B45ACF1B7F (void);
extern void JsonSerializerSettings_get_ConstructorHandling_mA0F1F980A1D1894748432FBB58718B426D8D3F84 (void);
extern void JsonSerializerSettings_get_ContractResolver_mC94CDBCF870E73DC5E8BBF374DF22DB7B864F75A (void);
extern void JsonSerializerSettings_get_EqualityComparer_mBF43D33BBBCCF1A8BCFF1E12E47C2FBDA3FFDC6B (void);
extern void JsonSerializerSettings_get_ReferenceResolverProvider_m8525837E697E32E6B6F0D5132A6199BEEBAF217C (void);
extern void JsonSerializerSettings_get_TraceWriter_m60C8FFA8ABA33EEE8C2613FD882DEFB50DBED6FC (void);
extern void JsonSerializerSettings_get_Binder_mDC6289CA577DFB45B2127E2124DACD3DE5315295 (void);
extern void JsonSerializerSettings_get_Error_m02A88351C07F1B3821B5E8A161CDE90B7EBF2C89 (void);
extern void JsonSerializerSettings_get_Context_m9F472C555FB0546B2EA8E1EE75A0762768FEBC24 (void);
extern void JsonSerializerSettings__cctor_m6F9BAD5867833964A7A823513DCA706761E505F0 (void);
extern void JsonSerializerSettings__ctor_mE1C9A90375BA88DF7F6911F1F96AEEC6A98CD22F (void);
extern void U3CU3Ec__DisplayClass90_0__ctor_mCE905C20A996B98D4302C7421CBDBFC70B9549E4 (void);
extern void JsonTextReader__ctor_m362F27D5EA7CEE0A9BDBF46FE235D8558146C10D (void);
extern void JsonTextReader_EnsureBufferNotEmpty_m05AC0A08C014C8603FAE0645369960741B939915 (void);
extern void JsonTextReader_OnNewLine_m81EAB877B3ED61FD2CA6EEB7A25D981649E8CCE2 (void);
extern void JsonTextReader_ParseString_mD8CC43B7912D85812D682177D6AE07121D2AC57A (void);
extern void JsonTextReader_BlockCopyChars_mE3F17F308437E7EF2F9F4FAAFAFC07052BE5FEB1 (void);
extern void JsonTextReader_ShiftBufferIfNeeded_mD95108D35B37F15DEFE4279E975DE93C324762FC (void);
extern void JsonTextReader_ReadData_m6F95C344F7FA0C2CF2590AB617B6F26730594F21 (void);
extern void JsonTextReader_ReadData_mFCC7C912D4C854B4222596EB5FCB4D91571C4623 (void);
extern void JsonTextReader_EnsureChars_m7CABC04368BBB274FD5A8774DFEE5AF9202E0607 (void);
extern void JsonTextReader_ReadChars_m081990A295318CD8C1DB29994465F9CF06B8FDCA (void);
extern void JsonTextReader_Read_m2D0F271F9202CFCC311179AF88939ACC706CF687 (void);
extern void JsonTextReader_ReadAsInt32_mC30FCCB22254DAA22517ACBF44886B7ACE34FB78 (void);
extern void JsonTextReader_ReadAsDateTime_m5F2DFEDDA05AF667EF9ED063A58963FE81B693C1 (void);
extern void JsonTextReader_ReadAsString_m72C5BEE030026DEBF94A937CE4186D46AE5B89C8 (void);
extern void JsonTextReader_ReadAsBytes_m67EDC2DFD294CC399281DC493A912AE02D8C2215 (void);
extern void JsonTextReader_ReadStringValue_m52DD00E9F1C25D4855D0023D889877D2F46D8446 (void);
extern void JsonTextReader_CreateUnexpectedCharacterException_m0080C6FB924D41B32A49FE1FF6723004770F81DA (void);
extern void JsonTextReader_ReadAsBoolean_m9778868B591339941F1826A947F088BF41396956 (void);
extern void JsonTextReader_ProcessValueComma_mC7F07BBCAA5B3D96F7A98AB3416DECA99C76242D (void);
extern void JsonTextReader_ReadNumberValue_mEA7EBB1B9A7673A62E976573D0FD11AF3A4C49FA (void);
extern void JsonTextReader_ReadAsDateTimeOffset_mC06985DFBA420610B3C5D1E56D3C69040E819A6D (void);
extern void JsonTextReader_ReadAsDecimal_m69E52ADD0F4C82E8395A5535B0386C2849AAE096 (void);
extern void JsonTextReader_ReadAsDouble_mF6C1A2CA31BAE2DB5F2A8A1E66148F694A1478A0 (void);
extern void JsonTextReader_HandleNull_m29E36B9003D6A5A1229326201A5AAA7A84BBE17A (void);
extern void JsonTextReader_ReadFinished_m9FB34AB2BA71C2896F2425A7C2B651A2FE15F354 (void);
extern void JsonTextReader_ReadNullChar_mED59672A36E9DF9B725D24CF508606ABB54CAFA6 (void);
extern void JsonTextReader_EnsureBuffer_mD5DD845C1E19BAA45D9ED3C7BC488920D1933004 (void);
extern void JsonTextReader_ReadStringIntoBuffer_m22539A750E1BCDC2E4F23D60A3832B5AC34705C4 (void);
extern void JsonTextReader_WriteCharToBuffer_mA1684E95F08F9D0EB2A64C37BEE05D40F016CE58 (void);
extern void JsonTextReader_ParseUnicode_mBB40787A8BC433976B8E9C9C645DB346972AE0C3 (void);
extern void JsonTextReader_ReadNumberIntoBuffer_m696A36BDB72CA515C00A2B98A879DFAD20046A7A (void);
extern void JsonTextReader_ClearRecentString_mA9AE4B395EF4019B6E0307B2C14873C0E9A45E84 (void);
extern void JsonTextReader_ParsePostValue_mE7D1220A4F6336F791B7DADD61C7A686CDA4EA93 (void);
extern void JsonTextReader_ParseObject_mC928FB614717EA429B2E56B1ECE16DBEF0462C0D (void);
extern void JsonTextReader_ParseProperty_m884E9FD46B7F7447F767D11E0DB5105C6320D153 (void);
extern void JsonTextReader_ValidIdentifierChar_m556A407C4A508D28AD593498F1D4EFEB06ACF063 (void);
extern void JsonTextReader_ParseUnquotedProperty_m27D01F8849EF755498630A3ED41AB622D3AF732A (void);
extern void JsonTextReader_ParseValue_mDED4C55A0064009E981474138317B8D7CA778E81 (void);
extern void JsonTextReader_ProcessLineFeed_m3E201A9963B7A588FB1BFF4B8B0D3AA145E0002B (void);
extern void JsonTextReader_ProcessCarriageReturn_m9BEFD304E778395A07F6238F1B2A47AE5B07C12F (void);
extern void JsonTextReader_EatWhitespace_mF9273F6BD64E11A40F11073147E45F4DEAA78172 (void);
extern void JsonTextReader_ParseConstructor_m3B027AEEF6FD5790100D8F094046DC1D6EE5A677 (void);
extern void JsonTextReader_ParseNumber_mDB4BDACD7DFFBE6F96E723D9491438282FD703E4 (void);
extern void JsonTextReader_ParseComment_mBA16BDED23A3AE70DDF01C0DF209FC829BE0D406 (void);
extern void JsonTextReader_EndComment_m86056041BA2E6586F053E32671208B6A7D3A807E (void);
extern void JsonTextReader_MatchValue_m62FC383608CF5AB98A45298CCEB8D2AE6FE82BAA (void);
extern void JsonTextReader_MatchValueWithTrailingSeparator_m2FF58D79A9DECB4E8A525FEB81764592EE375AA5 (void);
extern void JsonTextReader_IsSeparator_m1492A54F694AA7C0E377512461A7D62C5E01BB79 (void);
extern void JsonTextReader_ParseTrue_m680ED3A5A15B4AAA22B378E92B3D489F4696A3F4 (void);
extern void JsonTextReader_ParseNull_m919795B4C72BE47235F01092E85A2ACFAB37D727 (void);
extern void JsonTextReader_ParseUndefined_mE51CFE534A88607500322D701E3FB32BA047BF26 (void);
extern void JsonTextReader_ParseFalse_m0E30E5B9698EBA3749591A9EF0AB71DFD93CB8B0 (void);
extern void JsonTextReader_ParseNumberNegativeInfinity_m66DE18B2677FAFE1F6BC99D4A0E9FBB89F47F3A1 (void);
extern void JsonTextReader_ParseNumberPositiveInfinity_mD0CE3A7D6489B26B0E2D720D0D643D092F8D3BE3 (void);
extern void JsonTextReader_ParseNumberNaN_m2E1E654009F175EE501616C680C9E3998DC5211F (void);
extern void JsonTextReader_Close_mC616AC4E6EBF4B48D72CABD3CC03B2C32F3FA53F (void);
extern void JsonTextReader_HasLineInfo_m6B848FA0A4FB6405B898D680136A281FB69FC37F (void);
extern void JsonTextReader_get_LineNumber_mEE18A710D67FDA3F41C164E43FF8ECA880B942B9 (void);
extern void JsonTextReader_get_LinePosition_m867272C6F2107F121486D302ADCE9A6A9CA6A354 (void);
extern void JsonPropertyAttribute_get_ItemConverterType_m40009062EA3E6CB8F48F87883D49F31D92272BE4 (void);
extern void JsonPropertyAttribute_get_ItemConverterParameters_mB826BA3BB609B554C2A14322F87FABE38AA98B7C (void);
extern void JsonPropertyAttribute_get_PropertyName_m81E5C6785332B6EFABFA1C12BB999BA21AF86705 (void);
extern void JsonPropertyAttribute_set_PropertyName_m85DD25DB8B7E955976936D0F602507546C3B5EF1 (void);
extern void JsonPropertyAttribute__ctor_m11B49CBBC6572BCA06E7178DA6AD1CF0ED4312A6 (void);
extern void JsonPropertyAttribute__ctor_mE1DE1C04A1260B735B18BC6BD2ED080D545A73F2 (void);
extern void JsonIgnoreAttribute__ctor_m26883FC7D96DBF3502845CD154EB5A0BF2D694A0 (void);
extern void JsonTextWriter_get_Base64Encoder_mF34350F6BC7AD423AC0118B9F7A0C77090D2E898 (void);
extern void JsonTextWriter_get_QuoteChar_m91809CAE257879A5BE17410F34F8F1DF634E317C (void);
extern void JsonTextWriter__ctor_mE77A9BD1789BB07C3B49A6EF74857D2225AA99B3 (void);
extern void JsonTextWriter_Close_m97E8F3E6BAFBC81813B1D36921909043F64852FD (void);
extern void JsonTextWriter_WriteStartObject_m2091B80BD53D423E048BCFCF98E59F1EB45A8F52 (void);
extern void JsonTextWriter_WriteStartArray_m44C6A3BFD6CAA3B28998D46C663871A03BEAC9ED (void);
extern void JsonTextWriter_WriteStartConstructor_m02D432E9440B0796598DFDEDFC60B835BD6FDE60 (void);
extern void JsonTextWriter_WriteEnd_m5E5129364D602FEB37AE1CD6E006B1E5AE259073 (void);
extern void JsonTextWriter_WritePropertyName_m144298F68C24B39EED3095D0A03B12CC9E44D075 (void);
extern void JsonTextWriter_WritePropertyName_m1D23E633FE73A7D41E7E01A1C0DBCDFE9014798E (void);
extern void JsonTextWriter_OnStringEscapeHandlingChanged_m0C2F1A9EF35001E02B632A5ACF32FFB08976B4EB (void);
extern void JsonTextWriter_UpdateCharEscapeFlags_m76F3323EBE942834F35EEE6C8CE4B3EF200F7339 (void);
extern void JsonTextWriter_WriteIndent_m1820C5E25B26C9C1334C7386A312FA2D09A2EC41 (void);
extern void JsonTextWriter_WriteValueDelimiter_mECB7BD63E6DA0A917763457301983583BC7E4A6B (void);
extern void JsonTextWriter_WriteIndentSpace_m7D81DB5BD40780A7D7B32D0C50FA93D71DDE3A5F (void);
extern void JsonTextWriter_WriteValueInternal_m007617D83A26A85DF1B0D5D49A5965AFA74A733A (void);
extern void JsonTextWriter_WriteValue_m07E724EAEA49E87880C935666D29CC6C90718CB5 (void);
extern void JsonTextWriter_WriteNull_m512467D0B45E9C5C7AFF074B7E7950DBF45A797B (void);
extern void JsonTextWriter_WriteUndefined_mB011470E4E47F77A707326600CB7CCC323C95221 (void);
extern void JsonTextWriter_WriteRaw_mE0CADD22F91F09C55E848F95A53E8ADBC49F6491 (void);
extern void JsonTextWriter_WriteValue_mB96615CDC3F0349C37A8576EA93EA451029DF2CB (void);
extern void JsonTextWriter_WriteEscapedString_m59C0CA22C69CE85B67A726CDDDC7DD0B371604EE (void);
extern void JsonTextWriter_WriteValue_m19ED749B8CD92901131D44303F03B5595EA3FF48 (void);
extern void JsonTextWriter_WriteValue_m32656623E229E3B70409CDE4F8CA599C2B5D409A (void);
extern void JsonTextWriter_WriteValue_mDA381FF7CB3C486E036898A04BBF451911BF3635 (void);
extern void JsonTextWriter_WriteValue_mD792F636E8576341BD72573BEC965CEB75DB2D10 (void);
extern void JsonTextWriter_WriteValue_mEE631B6D1134A0F181854F6A4A309FCD6C5024A2 (void);
extern void JsonTextWriter_WriteValue_mFFE8CC28350BD5C8E0EB6B72D6ADF5B295B1C3FD (void);
extern void JsonTextWriter_WriteValue_mD0A181C434E6C939893742AD91ADBB6F66F21C89 (void);
extern void JsonTextWriter_WriteValue_m1A4B9C0C4047D178A9AE98A0FDBEF5509C18D16F (void);
extern void JsonTextWriter_WriteValue_m6D117A83A514CF735917C8D8EA02767215A2C31F (void);
extern void JsonTextWriter_WriteValue_m1A72D23742106519F15C41F00E80E656126E33C1 (void);
extern void JsonTextWriter_WriteValue_m3064DFF38E751342F76CAF5D6BC43AFB48C8A2AE (void);
extern void JsonTextWriter_WriteValue_mC71FA19FE4412DAECCBFCC93401FF2A9ED3A39E0 (void);
extern void JsonTextWriter_WriteValue_mC6A274C48CB4E4DE6949FD4B7C069015B07AF029 (void);
extern void JsonTextWriter_WriteValue_mB4979AEAD4B39799FF8F2398C682D7E6F52DEA37 (void);
extern void JsonTextWriter_WriteValue_m972E57EEA59DC2FE14962A09033AAFE79798E15D (void);
extern void JsonTextWriter_WriteValue_mBB63E6C88C107CA0DFA15C38FF9B0E9EA983B9D7 (void);
extern void JsonTextWriter_WriteValue_m047FD7F7506057CCFD0D4277D0AAC17FF6D731FE (void);
extern void JsonTextWriter_WriteValue_m526DF2337FD24DC9CBCF68EC49FD64D98DF46CB4 (void);
extern void JsonTextWriter_WriteValue_m2CCDE2AE295DCDC61E236A0305A9B808EE1231C8 (void);
extern void JsonTextWriter_WriteValue_mBD71AC56E20A004039F796B4314D44C3EE3A0FD2 (void);
extern void JsonTextWriter_WriteValue_mAE3EDADA20FF737C97C2E2924C432D0997DB1D75 (void);
extern void JsonTextWriter_WriteComment_m01B628ED4A24EDE3C3B44E1CA9BF8B041AA95573 (void);
extern void JsonTextWriter_EnsureWriteBuffer_mF1019CFAE05467B551055C56E322432839A4387B (void);
extern void JsonTextWriter_WriteIntegerValue_m68840DA4197A6A3682D23CCB9FEA0CA8007DB11C (void);
extern void JsonTextWriter_WriteIntegerValue_m07886B26B2AB73D0D95665C7B3E46F8D8C39DCA2 (void);
extern void JsonWriterException_set_Path_m2FAFF535EF2E8E2703EEFF13915ED3070DC8F12D (void);
extern void JsonWriterException__ctor_mBE0249771B368C41A4D6CA26783BEA3B82A61455 (void);
extern void JsonWriterException__ctor_m29EA8351C06D3CE9DA690046CB1C26B73484CB06 (void);
extern void JsonWriterException__ctor_mB31AEAEEB3499DF61851AD86E2410003B81B6273 (void);
extern void JsonWriterException_Create_mA440D59D85200435C7AFC40CDEF5773888D0F94B (void);
extern void JsonWriterException_Create_m5D4C3D2C757C0B94CBC900DF6C0737B11F53AD08 (void);
extern void JsonReaderException_set_LineNumber_mB48204B6654DA78CE8DCA4BE79C965DB8547F8EE (void);
extern void JsonReaderException_set_LinePosition_m558A365BB2CCAFF08C6BEEC79855265BDCD92A79 (void);
extern void JsonReaderException_set_Path_m876250F8349A55934456E05AF899C2D4B0C0AF2E (void);
extern void JsonReaderException__ctor_m2DA219E5A21E8B5C4206D282F6EC71ADEBE12838 (void);
extern void JsonReaderException__ctor_m87DA30F85DD461C0CAFF30C0A1214258A9E7B8F1 (void);
extern void JsonReaderException__ctor_m9AA5BE3367459D3692F7268BAF96D8D849052C85 (void);
extern void JsonReaderException_Create_mE1CBC0A12B559F606A85F2450C4400676ABD6529 (void);
extern void JsonReaderException_Create_m14FCC8DDF970329BA49DB00C272EB341684015A9 (void);
extern void JsonReaderException_Create_m13B3A6E229E457084CCCC5EE8BF637E2BCA729A0 (void);
extern void JsonConverter_get_CanRead_m2D457A04D24C35CBA381B6D6D857057A464A916B (void);
extern void JsonConverter_get_CanWrite_m7DEE81107B47B2A2A34A927DCA45439FC8C59A4A (void);
extern void JsonConverter__ctor_m47F59D2FF8CFBA449BDFBA405BDEEF6361139DAB (void);
extern void JsonConverterCollection__ctor_m0C47B6863A198C76E6C4F4D3415E3C426FACE042 (void);
extern void JsonReader_get_CurrentState_m2B6FA669883053CDABF108A980F808CDC9F13699 (void);
extern void JsonReader_get_CloseInput_m5D28FC12BD22B62AA1E493BC7A77402F1AA26DE4 (void);
extern void JsonReader_set_CloseInput_m084FA36AC838A9D3CABFF315691DACAFC4CF16BC (void);
extern void JsonReader_get_SupportMultipleContent_m28769E9E99495F868A23A8F6BDF5CB90D9B792B6 (void);
extern void JsonReader_set_SupportMultipleContent_mA063CFEB2CA56DD9681BF370B471F3C52918CC0C (void);
extern void JsonReader_get_DateTimeZoneHandling_m54980577E0FAC72C255619A8971816C4DE07AAA4 (void);
extern void JsonReader_set_DateTimeZoneHandling_mB7165954217060B05EE3A640407A22FE6431CD0E (void);
extern void JsonReader_get_DateParseHandling_m30B55C240DFBB01311DB973D35B62C2A138D4DC5 (void);
extern void JsonReader_set_DateParseHandling_mEE7359F16431F3997CF22EA9383A6FB6CBF33717 (void);
extern void JsonReader_get_FloatParseHandling_m6A522BBC3B7FBE3A21C1CC89B5AFB35CEA955826 (void);
extern void JsonReader_set_FloatParseHandling_m59278EBB6D40F8F11639AEA5476D438F5153F9FF (void);
extern void JsonReader_get_DateFormatString_mBEDE1FE52D98B87A039F321055027AC1FC779071 (void);
extern void JsonReader_set_DateFormatString_m7B16FCCF01DADBE34D5371BBD3820DFED84B283C (void);
extern void JsonReader_get_MaxDepth_m36C1AC4E11FA1D37C477B2FEAF26133AFE0BB56E (void);
extern void JsonReader_set_MaxDepth_m03E983D915FC443AACAB411E630AC5A932B992C2 (void);
extern void JsonReader_get_TokenType_mA7EDF1A1C45013F43C53F444F2E340E97BB648AC (void);
extern void JsonReader_get_Value_m97DBC2E327AAED57AABCADA0F01CD1A3974BB66F (void);
extern void JsonReader_get_ValueType_m2E0A97E557D825594A2801F189F3C545A3B5009F (void);
extern void JsonReader_get_Depth_m81ED0633EC9D3E46ED3DC95FADBBF09C1F495DEF (void);
extern void JsonReader_get_Path_m7869ECCEA1C5FE7ABF249472EF75BAAAD2F5EF09 (void);
extern void JsonReader_get_Culture_m99A1BB0CC4F4F6E13A243DBF53132881DE58F7E2 (void);
extern void JsonReader_set_Culture_m32AD6F7F73305D077841E603808BE4B9DBEDAD13 (void);
extern void JsonReader_GetPosition_mE60B167F7C9B4F39E14DEA98613049443F3C1968 (void);
extern void JsonReader__ctor_m1C65CDB90A8B108668938BBE17329FEC76D62C27 (void);
extern void JsonReader_Push_mF0F4F11224139B36E63CF369F8D0043C5FEC7759 (void);
extern void JsonReader_Pop_m8FC04F9ED04088581ACC3BD9BF97595E9337608B (void);
extern void JsonReader_Peek_m55B105CCB8DCA5378EA492FF96BC3ED4F4AFD5F3 (void);
extern void JsonReader_ReadAsInt32_mC4899D463063847ADA81A7E132F224DD5A56726C (void);
extern void JsonReader_ReadInt32String_mC88B484FFAD09A2650BC9080CA32FB6E4EF387F2 (void);
extern void JsonReader_ReadAsString_mB3980B052533C0CA4C8DCE9FE4E30F761B49366B (void);
extern void JsonReader_ReadAsBytes_mDD4F4FF1027E21622EE70DC557A4186C13AC6E68 (void);
extern void JsonReader_ReadArrayIntoByteArray_mB00AD02B342C511B5BB8BAB5133BE3B153540682 (void);
extern void JsonReader_ReadAsDouble_m84357B2560C4B03E2362D8B5D953C7336473F39D (void);
extern void JsonReader_ReadDoubleString_m324284E6D595C592176860D2B67B053713664F0E (void);
extern void JsonReader_ReadAsBoolean_mF52C093C99856A31D54A0F33927F5A6969AB35D6 (void);
extern void JsonReader_ReadBooleanString_mFFA51C5DFAFDEC91AADCCB70F0EA03BA7A3C81B2 (void);
extern void JsonReader_ReadAsDecimal_mF2351F836B051FC679349DFD2C5423541FBB8146 (void);
extern void JsonReader_ReadDecimalString_mF85AE314BF27C69E910E0BF7BE2BDA25FCEEF686 (void);
extern void JsonReader_ReadAsDateTime_m00C1F7D83FFB7708D511A1767282C64460AD7D6D (void);
extern void JsonReader_ReadDateTimeString_m4FF3EBFF0F0493D881BACF474BB4E23B8EAF39C4 (void);
extern void JsonReader_ReadAsDateTimeOffset_m4EACD91C4FC30B376CC88A87DC808766D4B5E398 (void);
extern void JsonReader_ReadDateTimeOffsetString_mA98E55BB14501C2528F2170F8FE5F3007B0EE9B3 (void);
extern void JsonReader_ReaderReadAndAssert_m8656D7D7DCB4B6C7CC0FA48CFD1DEC0DB38B9C4A (void);
extern void JsonReader_CreateUnexpectedEndException_m990A0BF68A99C9BEF63C7B79654C12699612790A (void);
extern void JsonReader_ReadIntoWrappedTypeObject_mAF0B8C23E64E3D2F2DC68A6D99979234CFBDB121 (void);
extern void JsonReader_Skip_m4D39178134B44B889EFE1C5D2181BB69CD0A4134 (void);
extern void JsonReader_SetToken_m9CB3201AB80BD12AF5490EE38B0AB1E667D5757B (void);
extern void JsonReader_SetToken_m57B521A8D559EBEC2CF1FED3001068F4F897FEFF (void);
extern void JsonReader_SetToken_m53C7B4EA9EC9F6D97E7CAC00F96DE6393EFEADEC (void);
extern void JsonReader_SetPostValueState_m72D637D843644ACBDF948EC84FCB77510B7D87DB (void);
extern void JsonReader_UpdateScopeWithFinishedValue_m47A4AC48B061E266CEC273759B2551CEF0718498 (void);
extern void JsonReader_ValidateEnd_mE6C8BAFA18ACFD014745A9CB8B294E07A3F2504E (void);
extern void JsonReader_SetStateBasedOnCurrent_m6D87B324B2C242175441D53056B94620AF006C70 (void);
extern void JsonReader_SetFinished_mAE30671BDCD13311CB2C99684735E435A57F288E (void);
extern void JsonReader_GetTypeForCloseToken_m184FEDCE4797DB95600F26BAC386E11C5D977470 (void);
extern void JsonReader_System_IDisposable_Dispose_m9E1776C6BEFEB70EBBD9F05BA6729631C9B027EA (void);
extern void JsonReader_Dispose_mA14E3FF455B3E5FE5226C2950CCEB70800707140 (void);
extern void JsonReader_Close_m68AD33DBE5DA0990DB82A21F3AB2D1F91AE49F84 (void);
extern void JsonReader_ReadAndAssert_m8E4307CFC38DA6E98E818C3058CCE2E385DC9EB7 (void);
extern void JsonReader_ReadAndMoveToContent_mFE15149891478B519F84C2CE34BF0138F7903E7A (void);
extern void JsonReader_MoveToContent_mFDA4EC684017D33858E3077205F8DFFDFDC38B24 (void);
extern void JsonReader_GetContentToken_m026EA65DC32E069C193963D9A2DB3829F533CEEA (void);
extern void JsonConvert_get_DefaultSettings_m9E0FA2F335896BD62E6CD277295E7FF580667E4B (void);
extern void JsonConvert_set_DefaultSettings_m52DE770DC408DFC48DE586C160F600E083EBBFB4 (void);
extern void JsonConvert__cctor_m82A9C80D58C2BE02D84B15E7579EFE9B4FAB775D (void);
extern void JsonConvert_GetDefaultSettings_m23ED04E6CE992D1AD2BB1050800D27B683F4602F (void);
extern void JsonConvert_ToString_m6A0035B3F2C451C32105F1B38813BB735A707674 (void);
extern void JsonConvert_ToString_m3199783FDBDC96A4C8F8253599AF07844E8E7CAF (void);
extern void JsonConvert_ToString_m98BCC2D08092BBB8A22EEAF13799ABF02B15A10A (void);
extern void JsonConvert_EnsureFloatFormat_m2C1E5BF2FFC0A5B04975B715566B0261AA4A1E0C (void);
extern void JsonConvert_ToString_mD9387D99174D55C8B40FA8A87DA8B6B7E931DBE1 (void);
extern void JsonConvert_EnsureDecimalPlace_m179A7A15E6046A0438E6CEFD75EB08C4F312A95E (void);
extern void JsonConvert_EnsureDecimalPlace_mDE47DC52767B69F67A67CE2846F05E6AC278AE06 (void);
extern void JsonConvert_ToString_m087E4B3454C6316E1DD7E9249ED747EA33FC8E75 (void);
extern void JsonConvert_ToString_m63264635A791255A4CD3AA4FBE8498F51687FE7C (void);
extern void JsonConvert_ToString_m4CE350BC3D3CF25CF69019EA7315753D0A18832C (void);
extern void JsonConvert_ToString_m4819E7C6189AC98BA1CF66977855C838F0B9D1C2 (void);
extern void JsonConvert_SerializeObject_m277670BD344964CB2A61751E5A3D62DB5B1321C7 (void);
extern void JsonConvert_SerializeObject_mEAA691E5567819FD0CDFEBB98629609ADB899D25 (void);
extern void JsonConvert_SerializeObject_m05270064842A4E4A8820C834DE0C58B7EE5F3E7F (void);
extern void JsonConvert_SerializeObjectInternal_m3D4E8DA93C08C5DEA01EDA779867C692AD0603F0 (void);
extern void JsonConvert_DeserializeObject_m8601FB6D35A0E41575940296CCCFD27B97EA97A2 (void);
extern void JsonSerializationException__ctor_m75C39E20101C53FA3DA4C053EF1D0BE447D95426 (void);
extern void JsonSerializationException__ctor_m0DDDC290916A23CCCE7A780AF876F9CE5FE58E4A (void);
extern void JsonSerializationException__ctor_m8B8ED999C982B8A742576A1D96FEDFDAF3D68727 (void);
extern void JsonSerializationException__ctor_m8B8DD2737A0BD15DA2119C8221ECDE622AB1F13C (void);
extern void JsonSerializationException_Create_m2CA947673DA3524AFC908CFE45478403E0B8E239 (void);
extern void JsonSerializationException_Create_mB3994D6FE53F3F8140BF01F6F123A356C4217472 (void);
extern void JsonSerializationException_Create_mBF3182906099773D44F9737E873A48462482E6F7 (void);
extern void JsonSerializer_add_Error_m8ED9D28F2650490A91A5ECA68A3D399FE64C2C39 (void);
extern void JsonSerializer_remove_Error_m971A111A77BD458681D1D49939522F81396EA486 (void);
extern void JsonSerializer_set_ReferenceResolver_mF996B45BD10A050C835C14E80BD99C47F87EE0A0 (void);
extern void JsonSerializer_set_Binder_mBADB914660791D1815A3971AB2BAD407240C6B14 (void);
extern void JsonSerializer_get_TraceWriter_mFFADF5321CC33F189F47C05137F99E76A93E8001 (void);
extern void JsonSerializer_set_TraceWriter_m802173793A3C9CA70F10DCE416600DE518283538 (void);
extern void JsonSerializer_set_EqualityComparer_m6EA25C41DEE7F87A96FB1BB3C496FC047E2EF498 (void);
extern void JsonSerializer_set_TypeNameHandling_m53B1E2AD1781281688AF0728F61ECD6E4800FE2B (void);
extern void JsonSerializer_set_TypeNameAssemblyFormat_mEE839C3CC76B8970E16033AF785EA27552934AA7 (void);
extern void JsonSerializer_set_PreserveReferencesHandling_mF24D1DA24B14BA618ACAD87C0A899E474B469D64 (void);
extern void JsonSerializer_set_ReferenceLoopHandling_m82082A93BCECEB8554123D00DD7C0DBA40DD0709 (void);
extern void JsonSerializer_set_MissingMemberHandling_m6B023E04A6921DE7F2C2CFC5BF9FBCEEC0196514 (void);
extern void JsonSerializer_set_NullValueHandling_m69454B6187F3C4C4EF4BC701B044A75F496BEB8E (void);
extern void JsonSerializer_set_DefaultValueHandling_m93AA18CF60212E9F8F8AE186A8DEB0D21ABF2AF6 (void);
extern void JsonSerializer_get_ObjectCreationHandling_mB435C0075A4A376DDCBC5C7F6124D125CD9C82E4 (void);
extern void JsonSerializer_set_ObjectCreationHandling_mDF99455B480225939D04E9B41626E0EE259EEC4A (void);
extern void JsonSerializer_set_ConstructorHandling_mA9D7108344EFC8C7559ECEC31F0D63AEB3305B61 (void);
extern void JsonSerializer_get_MetadataPropertyHandling_m88BB4FA44E8D735834F4E8CF1780D818F9BFC49B (void);
extern void JsonSerializer_set_MetadataPropertyHandling_m8390531F6FC7199ABA6A72B79FFF9BC2064203C3 (void);
extern void JsonSerializer_get_Converters_m8AAEAABF444A93BA1756D99F471EAED135EE6B82 (void);
extern void JsonSerializer_get_ContractResolver_m247BB8F47D90CA25CEDFA1A2C23807805F9A9251 (void);
extern void JsonSerializer_set_ContractResolver_m307DA90F879761FF996B41619946848F2A965820 (void);
extern void JsonSerializer_get_Context_mF31AD53E1C4DE2EA56113919E3FED192867E1CA2 (void);
extern void JsonSerializer_set_Context_m172446E1E6A162CFF7779F72FFC6D3BD38F277FF (void);
extern void JsonSerializer_get_Formatting_mAEB9A06613BCF0315AFCDF8A730AA5AC33C136B9 (void);
extern void JsonSerializer_get_CheckAdditionalContent_m3E80D386B770A1A56CFD3FBE70D93A2C992F24B3 (void);
extern void JsonSerializer_set_CheckAdditionalContent_m9681CF9A2B6E3574BBA8992D561E0B2DA4D5EF23 (void);
extern void JsonSerializer_IsCheckAdditionalContentSet_mF612DA7202241577EE38C3BED5791065247FA9EF (void);
extern void JsonSerializer__ctor_m54DC16B93FE333917E76324295F64409DBB9893B (void);
extern void JsonSerializer_Create_m3604E1417C4F7E9291135E04E24401928A207C9C (void);
extern void JsonSerializer_Create_m1030D503AE4455241212085D4C07B6E878A44AFB (void);
extern void JsonSerializer_CreateDefault_m99F2F47FE07886357539CE0FD6DA037B21189B60 (void);
extern void JsonSerializer_CreateDefault_mC8747AF7088F44A1E5B01C23728EBB078AAA6640 (void);
extern void JsonSerializer_ApplySerializerSettings_mE4FCA4F1A7A6898CC26D1EC391DAB96EFAFD9AEB (void);
extern void JsonSerializer_Populate_m82119593CD901DEF485D2F206E9713100A28D827 (void);
extern void JsonSerializer_PopulateInternal_m8910E5D45D37E181466181BA96EB12F246B163EF (void);
extern void JsonSerializer_Deserialize_m07E18A6BAA0AD5521D26EC348575BA6683DC4336 (void);
extern void JsonSerializer_DeserializeInternal_m218BEE555BD1AB9149BAB16A323BFE0F038368AE (void);
extern void JsonSerializer_SetupReader_mD375BE873123E7A0618B493E5E074CB52F335D22 (void);
extern void JsonSerializer_ResetReader_mE7617A472327D71802ED99586AA5658C2EBC588F (void);
extern void JsonSerializer_Serialize_m7E0ACFF2ABCDF5C092E2B735945B7BE7BAC3688A (void);
extern void JsonSerializer_Serialize_mE7F0CF9C2D3AD9D1B19A24F16FB151C9F63E7A2F (void);
extern void JsonSerializer_SerializeInternal_mD1606B221F27877646B9083E436131B7ABFCE491 (void);
extern void JsonSerializer_GetReferenceResolver_m90799FA8F9A70E016947D3DC8C8CF64914A9005F (void);
extern void JsonSerializer_GetMatchingConverter_m451F66A0DD7A9756A8EE430738C8894008CB0BA9 (void);
extern void JsonSerializer_GetMatchingConverter_m6CAFDF7FFC00014094B5759BDB1AC8081DC84C98 (void);
extern void JsonSerializer_OnError_mD1114CD08F27AA680261783F975BFCDAA31858CB (void);
extern void JsonWriter_BuildStateArray_m90959622E6AD976D5C09BB64F004A4793F4BCFA2 (void);
extern void JsonWriter__cctor_mC1A9523F34C1FD32CCDDFFF6958580707BF68622 (void);
extern void JsonWriter_get_CloseOutput_m0A40D11FF244B8766FA5A2467C78024B6E2F4736 (void);
extern void JsonWriter_set_CloseOutput_m8DCAE375AC709EF5D726F867A4997EA0DA946CB1 (void);
extern void JsonWriter_get_Top_m5095DA8B798DD98BE08ACA13C1C9CD816E9CB335 (void);
extern void JsonWriter_get_WriteState_m8580880783125AF9D182498B6DED5F1EB9CF1176 (void);
extern void JsonWriter_get_ContainerPath_mEEB68127DE05B60631203B908206E58C653E459C (void);
extern void JsonWriter_get_Path_m1FA362FAF775A1805E15106C216419A462DF6E01 (void);
extern void JsonWriter_get_Formatting_m7F7879CD7E9188A428A6E01990E70A65F5FEF81A (void);
extern void JsonWriter_set_Formatting_mCBA8160F9B05B54E46AD8057A1E7061742B0BBAE (void);
extern void JsonWriter_get_DateFormatHandling_mFA2684069267F277B7340B9738B1A9296A4EF8AE (void);
extern void JsonWriter_set_DateFormatHandling_mF67163662CA20C80D93A9345D94F608258ABB96B (void);
extern void JsonWriter_get_DateTimeZoneHandling_m70A3776234605D953796332656844492C164F799 (void);
extern void JsonWriter_set_DateTimeZoneHandling_mD3C952FA405EFCDA4A8E23A4A1F3F33D9F2983F7 (void);
extern void JsonWriter_get_StringEscapeHandling_m6B1688C4008ED729AF01BF5F2862E665F6F4F685 (void);
extern void JsonWriter_set_StringEscapeHandling_m35E6580C223973ED8B229F3EB1370DB79C215CDA (void);
extern void JsonWriter_OnStringEscapeHandlingChanged_mCDEB5DF2806C08A0BBA19A0FB584EFD45E93F034 (void);
extern void JsonWriter_get_FloatFormatHandling_mE17C10E646C9C244BCA40478DDC209C0BC59DF2F (void);
extern void JsonWriter_set_FloatFormatHandling_m26A6319C489A9C6F3C829E88C75851A6D260463C (void);
extern void JsonWriter_get_DateFormatString_m4E28421DC0EEE6A22B612788F0B1E5576FDB7865 (void);
extern void JsonWriter_set_DateFormatString_m6563F6EF134A68FD75F2B8D40EE72E78286B6B33 (void);
extern void JsonWriter_get_Culture_m8CB4EFB8986973B64D1C8D5A353D8A40447B5586 (void);
extern void JsonWriter_set_Culture_m46C18DF75D3CB2D3001F41638A397D002F5970A0 (void);
extern void JsonWriter__ctor_m5B0E4DCEC60E5158D71DDF7768E0FC5B09573739 (void);
extern void JsonWriter_UpdateScopeWithFinishedValue_m02E1BED60FEDCFE33EF46351BF6832B80D516804 (void);
extern void JsonWriter_Push_m4EFEEA3F2CBF5A324FDDE093A23CC060AC14BEBF (void);
extern void JsonWriter_Pop_mE6F01AEE8B02F6D4F6E6871A084F437B7AD5DC41 (void);
extern void JsonWriter_Peek_m6B35985FC269A47082EC69A78467FBC8D21005FB (void);
extern void JsonWriter_Close_m550789FF5D483AA86A99B5301B375F5BFB08C523 (void);
extern void JsonWriter_WriteStartObject_m327A0474DB3032162614C57B6154773EA900C153 (void);
extern void JsonWriter_WriteEndObject_mDAD19DEDAE92A5585F9D5B5E87220404DE54F5A9 (void);
extern void JsonWriter_WriteStartArray_mE2EDFEE60214B989C151DCD66C2B3219D927C75A (void);
extern void JsonWriter_WriteEndArray_m25923F05A530E148CF36E758C2AA1B85790277A5 (void);
extern void JsonWriter_WriteStartConstructor_m07D1C7D6352A67ACD8BDC166DC4CB1733650FEC6 (void);
extern void JsonWriter_WriteEndConstructor_mBC686C3FC4C2E47F75ED957F796E829D67DA51B6 (void);
extern void JsonWriter_WritePropertyName_mD805FBFFF2D43C2EC1E23A59C0102E5244CD537F (void);
extern void JsonWriter_WritePropertyName_m105D336D8CF072757F22DEAF8966E8F7609CB4A5 (void);
extern void JsonWriter_WriteEnd_m00646FA11D6F628BF200BF597022066D4A5D3057 (void);
extern void JsonWriter_WriteToken_m25988821E63EE4B5B81E05AE73A54B1F9CC5C0BF (void);
extern void JsonWriter_WriteToken_mA568EC81EAB48E7C67BFDBFC19CDF2B5A3809EB7 (void);
extern void JsonWriter_WriteToken_m5D8E7567C45CFC13D400F6C9984CE27A5A85529A (void);
extern void JsonWriter_WriteToken_m07632D47EA9AA93A60110178035B381097A5BEAB (void);
extern void JsonWriter_WriteConstructorDate_m4AB0CBA9A033BC6B9E1467B4667ABDF58C870334 (void);
extern void JsonWriter_WriteEnd_m4F23470A732AD694DD8F306BC81447900AEC3A5D (void);
extern void JsonWriter_AutoCompleteAll_m1C1C30C811246A31918505170B65B56D34F0EFF1 (void);
extern void JsonWriter_GetCloseTokenForType_mC47C8D67ECD8D5A166A53B79EAC723C6FF89E07E (void);
extern void JsonWriter_AutoCompleteClose_m981636CF5D983B493A72D7CE01A0131941FA71FF (void);
extern void JsonWriter_WriteEnd_m36FB4C43EE0F01066B13019046E5EECF96C902B8 (void);
extern void JsonWriter_WriteIndent_m723F066F8DF7EC046647A9355EC6C43FF31F2C9C (void);
extern void JsonWriter_WriteValueDelimiter_mE0CD244FB7D55B3ABFAA713CE010D919714572EC (void);
extern void JsonWriter_WriteIndentSpace_m875052C9E9A127EDDFC2E882332D3F67CB2C000D (void);
extern void JsonWriter_AutoComplete_mAF922A2E6BBA1D4B1C444978AC43F11185A3159A (void);
extern void JsonWriter_WriteNull_m2BF712564701AA53BAC6BD922677FF7B0B2310BC (void);
extern void JsonWriter_WriteUndefined_m507BF49F988B94F07230A9B502D7E430E1ACCF00 (void);
extern void JsonWriter_WriteRaw_m63875F6A8D37055645EB0515229DBDB124E7EC69 (void);
extern void JsonWriter_WriteRawValue_mE2022D2EE36E68A4EED29C087DF24AD1AFDDEC98 (void);
extern void JsonWriter_WriteValue_m3ECF13257A8D1AB90301A4174D42337149BC9D0C (void);
extern void JsonWriter_WriteValue_m8E9BDBD7C29F82DCCB87B4F5D17304F5A341A5B8 (void);
extern void JsonWriter_WriteValue_m94B80E8F2C3BF83E01624C8DE99D37CAEF2FE39B (void);
extern void JsonWriter_WriteValue_mEAB0A21EA57BEDF7BAD742C3F2A75EEB2E1091FE (void);
extern void JsonWriter_WriteValue_m9A6D49A850A3EA706B7143FF45BCF9DECF2A6ED5 (void);
extern void JsonWriter_WriteValue_mB83A734CC93E3FA5CF86ACD51DC69F42DF7FEA54 (void);
extern void JsonWriter_WriteValue_m2C9947B42490CA9AB5E8EEFB36B3E00359C472D9 (void);
extern void JsonWriter_WriteValue_m9EF9755CB7576F61E081148C00169A7128903B8B (void);
extern void JsonWriter_WriteValue_mE7084E3565A5BAE07F9E553F7C9362C2022E5B44 (void);
extern void JsonWriter_WriteValue_m152C46080EAA9C377E3E2E2942B16273F93A2148 (void);
extern void JsonWriter_WriteValue_mD5E4500BFFBC814E26C57409026A403C71EEECCF (void);
extern void JsonWriter_WriteValue_m1AEB560BFD2393EBBE9E766263CA01E3BA2D0E6F (void);
extern void JsonWriter_WriteValue_m0E635E0E6B005CD8D434FE57FAC79655FD774C4C (void);
extern void JsonWriter_WriteValue_mBCA33B3507B7A3BBDA956DC573ACEEB6A9D359D8 (void);
extern void JsonWriter_WriteValue_m0FF59870C2C94523F795BF6F7F2FFC30CE179F8A (void);
extern void JsonWriter_WriteValue_mF792BC34323AF19A7161B55838A630A9E169A132 (void);
extern void JsonWriter_WriteValue_mE9DA95DC1D6DAC672C97C861B92D3CB63933EC7B (void);
extern void JsonWriter_WriteValue_mBE7C0F46DDAD5B5F4D814A322D271B8141BF0F07 (void);
extern void JsonWriter_WriteValue_mE813EBE08F1D46EC3EDDAE52808D4092C8051F8A (void);
extern void JsonWriter_WriteValue_m396C679429003C9E1D2EAC178A8764F2A430CA79 (void);
extern void JsonWriter_WriteValue_mBD98465F188F8AFF6D1E09D9ACB668A951595A0D (void);
extern void JsonWriter_WriteValue_m9F3D3F1C549E617D3614ADBC65BA6651CA9094FB (void);
extern void JsonWriter_WriteValue_m368B61A68FE602EDA998B244966171341CC76D68 (void);
extern void JsonWriter_WriteValue_m5CD48CF6CFF10565A6B8C1956222D9AAC268E4B4 (void);
extern void JsonWriter_WriteValue_mCA78094ADEC239199E0CD5CB32DD1D6FE79D14F8 (void);
extern void JsonWriter_WriteValue_mA81111E38CC7610387BCC5408C6A01DA2F69AA60 (void);
extern void JsonWriter_WriteValue_mF8F8CD1AB87BCCE2A234413CA9B2AA6089D01119 (void);
extern void JsonWriter_WriteValue_m089D16BD443874053B786F185B509716A4E5ED29 (void);
extern void JsonWriter_WriteValue_m9C38E367AC1628BBEEA4224A82A77FFFD4C02E70 (void);
extern void JsonWriter_WriteValue_m1D8E946BE22A6EB81C26D1C86DD185A44533EFC7 (void);
extern void JsonWriter_WriteValue_m198F30CFFDA9DDD3E013B4F8CA32249E6DAB972E (void);
extern void JsonWriter_WriteValue_m163C6BE9BFF6D90A42D82DD0DB56F71739B4E4D9 (void);
extern void JsonWriter_WriteValue_mB53C4FB8D80F9955BF4D15477E1658878EE7AB82 (void);
extern void JsonWriter_WriteValue_mF17E3CCA082C4BBCD25AE23F0EF0DF095BCCF350 (void);
extern void JsonWriter_WriteValue_m373E77DC65D0FE001F07E3EB6616DE6C2EB06D99 (void);
extern void JsonWriter_WriteValue_mE1F445EACF5FB3E7EDF70D0C89BB230D95084DAD (void);
extern void JsonWriter_WriteValue_mBEB204C502C7E1547FC955BDBF558C0D616CD65A (void);
extern void JsonWriter_WriteValue_mC96DBA6DC02ACB9D67D4524889764368DB468C2E (void);
extern void JsonWriter_WriteComment_mFA4E1316687AD7922CFFEC126169E6083FC720A6 (void);
extern void JsonWriter_System_IDisposable_Dispose_m3262E18C96F7C21162CD73999E72EC49292EF7BF (void);
extern void JsonWriter_Dispose_m7467614DDEFC3204C606E3E226FC13CB200DC8E4 (void);
extern void JsonWriter_WriteValue_mB5AB5699DA3AACBA2DF6F05022CCC5FF83A5A295 (void);
extern void JsonWriter_CreateUnsupportedTypeException_m0E9FBD2DE2F40D54670A083D630B1A721322DACC (void);
extern void JsonWriter_InternalWriteEnd_m35638AC3696AB60EEDB46D555D84BC187002A28D (void);
extern void JsonWriter_InternalWritePropertyName_mF01F1A0A6BF0F72991479E1B77BAD3D44AA10019 (void);
extern void JsonWriter_InternalWriteRaw_mC9E958B67F30CF4DCB352A590E57764B06019A89 (void);
extern void JsonWriter_InternalWriteStart_m54FFFFA0251C6CE329A4395D1840B0B731A01593 (void);
extern void JsonWriter_InternalWriteValue_m7E8EC6F2C19FDF8EEE42F5B33931C55AC01088F6 (void);
extern void JsonWriter_InternalWriteComment_m5892E436F9662595F361C83CD432E3077B79E678 (void);
extern void DateTimeParser__cctor_mF1D9E079FA091D9FF81B2D76BA26152522E84149 (void);
extern void DateTimeParser_Parse_m23985D38D15F4AC3CF47CFF85341AE406A302091 (void);
extern void DateTimeParser_ParseDate_mCF43EAF9D60C2DF52038EAA7C5653D59C83F9C8F (void);
extern void DateTimeParser_ParseTimeAndZoneAndWhitespace_m36C0C76A6548E07654C45414059FBD5BC95F5BBD (void);
extern void DateTimeParser_ParseTime_m9717B1406E4705AB36B56F41A8F0CAA14B1815DD (void);
extern void DateTimeParser_ParseZone_m4CEBD1A881CB114B6EB7D8050F44EA265A260642 (void);
extern void DateTimeParser_Parse4Digit_mC5F981E6CD7CD915FEA858DE77A04AEC04AF4D86 (void);
extern void DateTimeParser_Parse2Digit_mF1031EB57E34571DA339FB7B1AE09268293F2633 (void);
extern void DateTimeParser_ParseChar_m69C950529AC35A4734A1DCA09ED004ADDDE9452F (void);
extern void Base64Encoder__ctor_m8F45560E8389A93FBEB501B8685D03E876F46C4D (void);
extern void Base64Encoder_Encode_mB7AC073FA2837C4647788A4517F6234EECC78F92 (void);
extern void Base64Encoder_Flush_m0227AAAF28FFCA22AF4FA524E82FC4F2C1AD4DDD (void);
extern void Base64Encoder_WriteChars_m2402CA2720FAFFF4529F80B591E2B701877C6EE9 (void);
extern void JsonTokenUtils_IsEndToken_m70717579B601F0647A66B4896AE17A48B3A89CB4 (void);
extern void JsonTokenUtils_IsStartToken_m3C784E79F513290AFDC7818B2C720C795FBE500C (void);
extern void JsonTokenUtils_IsPrimitiveToken_m97DBA2150C205005AA33809D4355769E9ED1BB2B (void);
extern void PropertyNameTable__cctor_m53E82E052F144AB7D73D75947FC7F91BEEE72A28 (void);
extern void PropertyNameTable__ctor_mD7F65C095AB3D9A606C5770B3469B806936A0B8A (void);
extern void PropertyNameTable_Get_mCE6D6D04D42BAB90999B694D872EFFAC2437343F (void);
extern void PropertyNameTable_Add_m6617E7236C98C124670B7405B4E231ED2C11619E (void);
extern void PropertyNameTable_AddEntry_m050639221DE407DB9CC073441F04EDF3A4371E28 (void);
extern void PropertyNameTable_Grow_m76421B59F0D565DBB12F5229AA3DD3176692B662 (void);
extern void PropertyNameTable_TextEquals_mACB76A809C7126ED133E05816E514FD71B09AF5B (void);
extern void Entry__ctor_m30B82FED315B825E73B0F831B78463D212352001 (void);
extern void ReflectionDelegateFactory__ctor_mE9385422DDFE3B3AED392895D925ABF922D1048F (void);
extern void LateBoundReflectionDelegateFactory_get_Instance_mAC844486768E223FABD4AB379F29D33997E6E747 (void);
extern void LateBoundReflectionDelegateFactory_CreateParameterizedConstructor_mA59A94C702D443557FDAE92AF35A64E2DA92CF4F (void);
extern void LateBoundReflectionDelegateFactory__ctor_m98CE81BFFF3C34914C11A9167881DBD651793C5F (void);
extern void LateBoundReflectionDelegateFactory__cctor_m03272AF8A4FE2D11C7D2467A97EB833D8D8A4B8B (void);
extern void U3CU3Ec__DisplayClass3_0__ctor_m312F08F4C3CEBC38A926FB5F2A0C119D462FEB6F (void);
extern void U3CU3Ec__DisplayClass3_0_U3CCreateParameterizedConstructorU3Eb__0_m4F59643C7CFE3C66AC0076F949501A04920F7FF9 (void);
extern void U3CU3Ec__DisplayClass3_0_U3CCreateParameterizedConstructorU3Eb__1_m14BA5C3056EE694ECC9A796EC7E3F298DCA685A2 (void);
extern void ReflectionMember_get_MemberType_m434D9AB83992981B6F6E5E0641CEA721A590F664 (void);
extern void ReflectionMember_set_MemberType_m910CD710E590680C35E3663BDC07074A691C4CF3 (void);
extern void ReflectionMember_get_Getter_mED1E4624E99FB9D6DDF293693EFFBF3DA290DE83 (void);
extern void ReflectionMember_set_Getter_m75ECB835C1A8B2DD3E802D1631B32D3B6CA75187 (void);
extern void ReflectionMember_set_Setter_m7B146640EE159DD3226E47E5383DFD8AD5A1E715 (void);
extern void ReflectionMember__ctor_m8ACC777354B64FC9082AAA4783F0C89B885CC62F (void);
extern void ReflectionObject_get_Creator_mFE215FC9C57548498D71EE5B13DB60FBED70074C (void);
extern void ReflectionObject_set_Creator_m9745F5798D9C0083D0B1896DA7DC4A78DDCA3A41 (void);
extern void ReflectionObject_get_Members_m11F407DD2A6380C05E18A03E8425A5175A0D3FD8 (void);
extern void ReflectionObject_set_Members_mF6FBE268BEAE55D190EDA784E103D971551C865F (void);
extern void ReflectionObject__ctor_mFF8A70105240594D877E597200A779052AD85924 (void);
extern void ReflectionObject_GetValue_m16C7AF8473ED05865B899DCE08826438E9381D10 (void);
extern void ReflectionObject_GetType_mB739C18B776ADC78FCAA3513AE4D4EE73B91ECDC (void);
extern void ReflectionObject_Create_m789093E9CCCC488B191391FEC5448DCCFF4B05C6 (void);
extern void ReflectionObject_Create_mE6A4EC38A8DFA1A7E71C2BB096B94BD78F61B2A8 (void);
extern void U3CU3Ec__DisplayClass13_0__ctor_m5172B2BA78B3DB828BDA76D7FB203BC904D49536 (void);
extern void U3CU3Ec__DisplayClass13_0_U3CCreateU3Eb__0_m7DA59DB878C30CD76A014AF724FCE01ED14A8393 (void);
extern void U3CU3Ec__DisplayClass13_1__ctor_m83923C0C11252BCFAC25A6E65506D9A0BB814E8A (void);
extern void U3CU3Ec__DisplayClass13_1_U3CCreateU3Eb__1_m9A6CFE9FE0F43F312AD1B20AF69805D51E68BBD4 (void);
extern void U3CU3Ec__DisplayClass13_2__ctor_m4956288D04A83643632E6BBBFA273F37D29BC9C9 (void);
extern void U3CU3Ec__DisplayClass13_2_U3CCreateU3Eb__2_m157892641A9E32E5E199D09703036328DFCF716A (void);
extern void StringReference_get_Item_mF157FD35EDF25DC3FB3291BA8A7ACA6A49791EBD (void);
extern void StringReference_get_Chars_mCAEA9DDED5058DE07529C24621E510E396B79A6B (void);
extern void StringReference_get_StartIndex_mC3DD76078312694DB7C297115073EAE930B42925 (void);
extern void StringReference_get_Length_m65CF2F68237C0273F5BE4B4B0DCD4247CD940385 (void);
extern void StringReference__ctor_mCAEF5A34A8FD029BA4399BDEAD6B9AB67515A5B2 (void);
extern void StringReference_ToString_m14E995A62CEC0B0C1313E51D01878B015EB38EF6 (void);
extern void StringReferenceExtensions_IndexOf_m8408F16214688FE8239B09858B186C1125599F83 (void);
extern void StringReferenceExtensions_StartsWith_m0C5C1ED2F3842A51339DF8F300BFACFB95DC7A2E (void);
extern void StringReferenceExtensions_EndsWith_m856EC50F4E06388F85AA6897D5A919E6F454B2FC (void);
extern void TypeInformation_get_Type_m66CAE341688AA88BD1598BD84C736FC0429619F1 (void);
extern void TypeInformation_set_Type_m390CB6B2F90698A847053FEB731D9376798EEEAD (void);
extern void TypeInformation_get_TypeCode_mC353DCEF2D0C5A983CD7A389E67D0F1B9D4F57EA (void);
extern void TypeInformation_set_TypeCode_m9F432C648665B0C14B3C6483DAE92F317009168F (void);
extern void TypeInformation__ctor_m116F34BBC859ADB3553EEE8E1B0B6741158FB497 (void);
extern void ConvertUtils_GetTypeCode_m3FF00ADCDD10F4F141BBBE4FBED910EFAB042EDE (void);
extern void ConvertUtils_GetTypeCode_m959EFBADBFB4D2ABFDE6DFEA04CEDDB5C4DB9785 (void);
extern void ConvertUtils_GetTypeInformation_mA2F03C07BC76F45128DE4BC27FA3DF099D64F63F (void);
extern void ConvertUtils_IsConvertible_mBFF3508FCE496CEE88EEAAF3DD82193A5F7D443E (void);
extern void ConvertUtils_ParseTimeSpan_mF6B09E2815B7E0CADC1C35C93B58D030BA4E1202 (void);
extern void ConvertUtils_CreateCastConverter_m9C8F184B0440938A61029D69F1AD9F226EBD7213 (void);
extern void ConvertUtils_TryConvert_m83691EC9657B63B2672A8814B83A5DB042E8587F (void);
extern void ConvertUtils_TryConvertInternal_m9E76F6EF19E80EBF9054B01D7AD23173DDEF3912 (void);
extern void ConvertUtils_ConvertOrCast_m1BAE14A07732D27166BC0121DF96D63FD8EE897B (void);
extern void ConvertUtils_EnsureTypeAssignable_m0F13B16EB957DDA87DC4EDB251DA44E38D20C06D (void);
extern void ConvertUtils_GetConverter_m3DCC606FFE3B5EA9D7A2951F7F2DF6AA32F76415 (void);
extern void ConvertUtils_VersionTryParse_mA4BE1D73A196161D4F95B3EBFF256D5FAFD2FB6E (void);
extern void ConvertUtils_IsInteger_mF25CD55DEF949A90AD74D6FCE946A9BA155B95CB (void);
extern void ConvertUtils_Int32TryParse_m6D81B72EF5D3E6FB6283976493619C2A900BBE95 (void);
extern void ConvertUtils_Int64TryParse_mD4BA6148D0849E5EFBEC03B6EE4D2D92FAD49F52 (void);
extern void ConvertUtils_TryConvertGuid_mB09F062E72F476C09A4ACB8A2387F5A9862FD4AA (void);
extern void ConvertUtils_HexTextToInt_m5350E533A781F64A2CD2CE80B0AFEFE9DF5F450C (void);
extern void ConvertUtils_HexCharToInt_mAE36477DEC362EE5C10B08550F6916FCAFB504BB (void);
extern void ConvertUtils__cctor_mCE736B50121C78004F75F17086D8966B34F641CC (void);
extern void TypeConvertKey_get_InitialType_m00D6EF528297F8AA3372C9BD42BD0F02EB64E229 (void);
extern void TypeConvertKey_get_TargetType_m61EE1F103B49EAA250ED9D9E97B8A4B3D4DF2925 (void);
extern void TypeConvertKey__ctor_mD8FF7E88853BA5DE5876BEE02E0F656E8FB5C736 (void);
extern void TypeConvertKey_GetHashCode_m73EEFCBFC5400417D8BE4F835C852F42A20D448F (void);
extern void TypeConvertKey_Equals_m8C696A833AC5997EE1AC57B90E260679C2176069 (void);
extern void TypeConvertKey_Equals_mC41E4C78130945D5E3FEFCC700F7D18E62E48BCA (void);
extern void U3CU3Ec__DisplayClass9_0__ctor_m925748B96EAAFC6F502C31EE3032DEC4A6E97146 (void);
extern void U3CU3Ec__DisplayClass9_0_U3CCreateCastConverterU3Eb__0_mA083ECAB32C98214204B8E8E01B3E508DAB1D2B8 (void);
extern void DateTimeUtils__cctor_mB8749F73ED67F50441C60F2999411BD9234A8BA4 (void);
extern void DateTimeUtils_GetUtcOffset_mA930C30B051B5F333AF1300B9E40BFC003C50A50 (void);
extern void DateTimeUtils_ToSerializationMode_m91B5EE22B7ED390567F3EBA783285F6218D17AE6 (void);
extern void DateTimeUtils_EnsureDateTime_mA79D09FFD516714FB3A1296F5E55780FBD5191C8 (void);
extern void DateTimeUtils_SwitchToLocalTime_mDD82AB9D4F2D9F49165153702BEB445085AA57FE (void);
extern void DateTimeUtils_SwitchToUtcTime_mD7F5B3C7585927E01F6C544C56ADC21FCBFEDD80 (void);
extern void DateTimeUtils_ToUniversalTicks_m2A37B7076787063F789C5D1E7BFBEFF8105EAE1A (void);
extern void DateTimeUtils_ToUniversalTicks_m0E9B1237416F7ED5ABD736831D5F7F2F4AF70E97 (void);
extern void DateTimeUtils_ConvertDateTimeToJavaScriptTicks_m0D5815D6340AC9707A7FD04B27F2101845CC598F (void);
extern void DateTimeUtils_ConvertDateTimeToJavaScriptTicks_m6EF183506C0B2D94C8307100E3DEF80DB8B34859 (void);
extern void DateTimeUtils_ConvertDateTimeToJavaScriptTicks_mA2ACEC25DDFC8FD1CD43D6368BDF4FB612B453B8 (void);
extern void DateTimeUtils_UniversialTicksToJavaScriptTicks_m1304023C12CB633E02CB86F6F70CBAB37C2EF3E7 (void);
extern void DateTimeUtils_ConvertJavaScriptTicksToDateTime_m927253156D26643004345A45B5FE76648E8F8884 (void);
extern void DateTimeUtils_TryParseDateTimeIso_mF9B36B399D6CEB27F9F793080148C1A2489C3047 (void);
extern void DateTimeUtils_TryParseDateTimeOffsetIso_m538094DB0A9E001B1A9CC19F0A67880DC4CDBEA4 (void);
extern void DateTimeUtils_CreateDateTime_mBB5C43CD527E4D051A473CF3C99BC27A63158526 (void);
extern void DateTimeUtils_TryParseDateTime_m5EB8CE9B38B3CEA7A400D7A50C1EF93FF64AD7A8 (void);
extern void DateTimeUtils_TryParseDateTime_mA44DD7990B1A60C56ABF62BC1663C6AB35B41D84 (void);
extern void DateTimeUtils_TryParseDateTimeOffset_m01EF0F19E09185A87FBF604688FC24549EDB0447 (void);
extern void DateTimeUtils_TryParseDateTimeOffset_m9B1B360ACFE9EC20A745DDF71E5660C925ECAA72 (void);
extern void DateTimeUtils_TryParseMicrosoftDate_m1FB25AB70888B42D0E886ABE60B49F513A4866AC (void);
extern void DateTimeUtils_TryParseDateTimeMicrosoft_m9D1B53C735ACC5E2348AC816E1223336774A77FA (void);
extern void DateTimeUtils_TryParseDateTimeExact_m26C44CF3F9D71D7B66D6D610ACF09415E9FA5A97 (void);
extern void DateTimeUtils_TryParseDateTimeOffsetMicrosoft_mDE9494C3F5AB4DA579B89B0A806CBBBDB71F82C0 (void);
extern void DateTimeUtils_TryParseDateTimeOffsetExact_m82A8B1344B99932C906AA9F484BB0C1E8D3CF136 (void);
extern void DateTimeUtils_TryReadOffset_mFFDEF89F67E624EF8B2C64CD576C4BA93C03DAB9 (void);
extern void DateTimeUtils_WriteDateTimeString_mC66266349F3A8A9B3F069BBE8AA7F0580333A38B (void);
extern void DateTimeUtils_WriteDateTimeString_mCDFAB4AD7F5F2A187B9F0778CFA2DF2005C6200C (void);
extern void DateTimeUtils_WriteDefaultIsoDate_m8DEE0C2EADE57D2B6EE01D57B9C7F276B6681C93 (void);
extern void DateTimeUtils_CopyIntToCharArray_mAE8342C2764DE70579A9D3F1C3C4DC3D1504EC36 (void);
extern void DateTimeUtils_WriteDateTimeOffset_m1346049BF9D62EB01271E853BBB9FF6F382E2F4F (void);
extern void DateTimeUtils_WriteDateTimeOffsetString_m36016656CB36E3E34222C8EFBCA4F636F663B6C1 (void);
extern void DateTimeUtils_GetDateValues_m92E008CB52CBBC94701ECF185BEF2DA71AD9ED11 (void);
extern void EnumUtils_InitializeEnumType_mC735DAB2D3908D7559F2D70BDF067EF983DCCBA2 (void);
extern void EnumUtils_GetValues_m2DDB95CC0F0D37D258AAF60E3CFEBE79C9D108FF (void);
extern void EnumUtils_ParseEnumName_m9095DB225793F25DCBC646FA225BB2E43837FA41 (void);
extern void EnumUtils_ToEnumName_mE3581769A67B663A6673F5F7AC87193893D95BD4 (void);
extern void EnumUtils_ResolvedEnumName_m248674CF791510E8FF2F4A25EF4DB6C20CC6ED0C (void);
extern void EnumUtils__cctor_mA049DE3D88785736AD69EC4E30909180FF3AC6CE (void);
extern void U3CU3Ec__cctor_m242F2EC68584B153778198BB9D4F507A98798704 (void);
extern void U3CU3Ec__ctor_mB8204CC34DACDED611AAD8DB38DBFE4D17601831 (void);
extern void U3CU3Ec_U3CInitializeEnumTypeU3Eb__1_0_mD4BA67ECE616B826E61050E1F6CCEFA3B377CAAA (void);
extern void U3CU3Ec_U3CGetValuesU3Eb__5_0_mAB7D7BE53058F60BDF00BF6CF88D432F01D41025 (void);
extern void BufferUtils_RentBuffer_m0CD2D9EE0A156A32A3E6690D59974C1F772DA4F1 (void);
extern void BufferUtils_ReturnBuffer_m724A55F218F5C04FB3A427C74B5CEF4EB9B020A9 (void);
extern void BufferUtils_EnsureBufferSize_m2CF4818D2839BDF81AAE5AEDBEA4772C9B2F9A18 (void);
extern void JavaScriptUtils__cctor_mC942D980534291CCAA348752CCA85F50F4D71858 (void);
extern void JavaScriptUtils_GetCharEscapeFlags_mFACEF9DE1403BD66866D82EEDEBAF9CA11171770 (void);
extern void JavaScriptUtils_ShouldEscapeJavaScriptString_m3C5359149C97D68DEE24E6BB6B6A8C2A791EA6FC (void);
extern void JavaScriptUtils_WriteEscapedJavaScriptString_m267358C3D8B0A8EC568C3AFAF98ADE308FFCEEE1 (void);
extern void JavaScriptUtils_ToEscapedJavaScriptString_m50ACC5FDF3A885F430CBF7FDAF5A048551F995B7 (void);
extern void StringBuffer_get_Position_m26F00F0301A7D88C7A0E2199F1E6394C2D1AB61D (void);
extern void StringBuffer_set_Position_m68C6F0EBF858CFCF3D10A8D1D255B24ADA883263 (void);
extern void StringBuffer_get_IsEmpty_m0FF70318FF5ED6D3C0E2C1BD3E5B3BDBB7C0A884 (void);
extern void StringBuffer__ctor_m0B0B11963A1F7B3F240F8993C116DB8EC5ECE96C (void);
extern void StringBuffer__ctor_mFF5E0ADAA4ABD314C4524F3DC482CF9773932C5B (void);
extern void StringBuffer_Append_m77B388D5627C9D7EBB5C5848F20C396B826253AB (void);
extern void StringBuffer_Append_mCF7546C3A7CED19D47B3AF5ED2E0A2D456AF5DFE (void);
extern void StringBuffer_Clear_m6C24B7B855B0E8F12C3DC20D6191EDB81A0D65A6 (void);
extern void StringBuffer_EnsureSize_mB5835295812DA385035C84550B3A9E35FC59E100 (void);
extern void StringBuffer_ToString_m693AF2D7D2FCB627284D3A0D79FDA85547407D49 (void);
extern void StringBuffer_ToString_mF654D1BD6CCFF512998846C7E7D57C1E6AF6B4DE (void);
extern void StringBuffer_get_InternalBuffer_m74C440CFC916B0D891EBEB6D577BFB518CF891B0 (void);
extern void CollectionUtils_IsDictionaryType_m6E8536FFCDA481FB20EC0C4B8746028004A1BC6E (void);
extern void CollectionUtils_ResolveEnumerableCollectionConstructor_m9CAFB266C5D44BF7E674050AE7E7030362FD20B7 (void);
extern void CollectionUtils_ResolveEnumerableCollectionConstructor_mC2D9D30053DFE768B4E85A883579F9B8DFD3CD15 (void);
extern void CollectionUtils_GetDimensions_m659D13D84108B2CA459101CF67D69D272B946BEE (void);
extern void CollectionUtils_CopyFromJaggedToMultidimensionalArray_mABA6B698638107D5CDA81FD03F37A78C1DB097E5 (void);
extern void CollectionUtils_JaggedArrayGetValue_m91764E8A6719C0663E7FDF506ADEFE43A4FFF909 (void);
extern void CollectionUtils_ToMultidimensionalArray_m89EC870CBB5C35DA01D42D3FE5E7745B96BC5F29 (void);
extern void MathUtils_IntLength_m23241F09F5280B9249FBD8D666E56C81E464919C (void);
extern void MathUtils_IntToHex_m3A390F8C758B3E156E4440239E3FF2C0FE766F63 (void);
extern void MathUtils_ApproxEquals_mFEC2066308DE0316C9ED311B46207B0F50A28712 (void);
extern void MiscellaneousUtils_ValueEquals_m427F9DD809F1A34E202601685246C163621C0661 (void);
extern void MiscellaneousUtils_CreateArgumentOutOfRangeException_m91D696C3DB200B3480F61835CB2D4B0A601ECF0A (void);
extern void MiscellaneousUtils_ByteArrayCompare_m1DFCACC65C5035DF56AB68AD5873FA3EE26FE941 (void);
extern void MiscellaneousUtils_GetPrefix_mD4530D3BF776EA4ECCFF1888BBD2ECDE9F2DC1CF (void);
extern void MiscellaneousUtils_GetLocalName_m6104B0EF1BD60B135EEC76A96AFF567D02394CC7 (void);
extern void MiscellaneousUtils_GetQualifiedNameParts_mDB4115E09A5DDA5F1103D82C7B514A038301683C (void);
extern void MiscellaneousUtils_FormatValueForPrint_mA058F3DB86D4AE41E23975B3DD8BEE5E249488E4 (void);
extern void ReflectionUtils__cctor_m838B9E504083D1099352011D467F39F2BF084BC2 (void);
extern void ReflectionUtils_IsVirtual_mB9D39467327026209931F92BE4B90B2E980F7398 (void);
extern void ReflectionUtils_GetBaseDefinition_m0A99789F9B9BC226849E61E8AFCFA3FBAA5D7AC6 (void);
extern void ReflectionUtils_IsPublic_m3CE1F6A87208FBC096DF73A053DAEDF0786BD5C5 (void);
extern void ReflectionUtils_GetObjectType_m979DDF9D576AB3D45DF5B009C0B8BD46763CCC17 (void);
extern void ReflectionUtils_GetTypeName_m3A1B9D816D52828ACE09EA407BA362F7291BA46A (void);
extern void ReflectionUtils_RemoveAssemblyDetails_mC21C6832C5D0298E7C9AD4CC8CE95C543CE2DA37 (void);
extern void ReflectionUtils_HasDefaultConstructor_mB80909BFB8B680B7B5BBAD5E85083CA71DCECC37 (void);
extern void ReflectionUtils_GetDefaultConstructor_m7D201BD979CF35DA3BD18B96219C9CAD547D550E (void);
extern void ReflectionUtils_GetDefaultConstructor_m46F8A00E72216F0CC50881E61902BB24DF31A0B8 (void);
extern void ReflectionUtils_IsNullable_mF1649DFA26303244B2271027980F139FF42DA8E0 (void);
extern void ReflectionUtils_IsNullableType_mA710C2540434AAA2353481C15C48C3996EC69963 (void);
extern void ReflectionUtils_EnsureNotNullableType_mF2B1550F38848A01AAAFDAD0755C37ACA6530ED6 (void);
extern void ReflectionUtils_IsGenericDefinition_mF5420EF48A193DA2514186BE3B58F36F27B7E9C8 (void);
extern void ReflectionUtils_ImplementsGenericDefinition_mA407E5B3AD8CA9F69F2D4257EDA4AC7EB8C2D4E5 (void);
extern void ReflectionUtils_ImplementsGenericDefinition_m7E4E60FAB965FDE16C7151A6FCA0677845CFD6DB (void);
extern void ReflectionUtils_InheritsGenericDefinition_m472BA465A850FABA27CE0E6ADC2DC88D7317EBDA (void);
extern void ReflectionUtils_InheritsGenericDefinition_m36577D3689B74DC9CF2E4426CAE533B3393E6E06 (void);
extern void ReflectionUtils_InheritsGenericDefinitionInternal_m28E863A1932987F75B56A7B75B55DE43B6911A8A (void);
extern void ReflectionUtils_GetCollectionItemType_m1CF4C2D7E62D83B5611331DAF65CEFB0D2D30FEC (void);
extern void ReflectionUtils_GetDictionaryKeyValueTypes_mA1AAA03E6E1701AA394D21A2EAD50EE6F1DDC0A0 (void);
extern void ReflectionUtils_GetMemberUnderlyingType_mB8BC2C0D7728CF137E88B887AEDC8A93163470C0 (void);
extern void ReflectionUtils_IsIndexedProperty_mD2FBA4A2C6BD37A0C0754112386AF8C41BD23FBE (void);
extern void ReflectionUtils_IsIndexedProperty_mC6602D9BBD42A67584EF40A29B50DB45A035A565 (void);
extern void ReflectionUtils_GetMemberValue_m69C0903BA25F305FA8C4FA37938FBBA54F608D5D (void);
extern void ReflectionUtils_SetMemberValue_m8FA7938133743E996E7C6D77AE4191C38BE9FEB7 (void);
extern void ReflectionUtils_CanReadMemberValue_mAEC74283BD7361EA0E00DB00DA4CA1701893E3CD (void);
extern void ReflectionUtils_CanSetMemberValue_m84DD2635E730746FEB493F99AFBB98C41090A994 (void);
extern void ReflectionUtils_GetFieldsAndProperties_m5C9A9A1B4C2D81B55B9A96111F9224A5C2C298A9 (void);
extern void ReflectionUtils_IsOverridenGenericMember_mED37BCC1DBDC0EFA25F650882D88626F9004003E (void);
extern void ReflectionUtils_GetAttributes_m64100872CC4D5F42453CED2620D0790546A34543 (void);
extern void ReflectionUtils_SplitFullyQualifiedTypeName_m29849DCBF19DB45C56AAC1B81E6CED57E4962C44 (void);
extern void ReflectionUtils_GetAssemblyDelimiterIndex_m4860F359D4A8A7F5AEC2C2A75904469C3A4722CD (void);
extern void ReflectionUtils_GetMemberInfoFromType_m53EA3C96A700303B68775A888758AD4898B51A73 (void);
extern void ReflectionUtils_GetFields_m345556004ABC798073F71E65987A68E749DEACDE (void);
extern void ReflectionUtils_GetChildPrivateFields_mE010323462273938232947F53CEAEDD04CA0224A (void);
extern void ReflectionUtils_GetProperties_m116432A13B39F37FD078A1E749AF04822F48D117 (void);
extern void ReflectionUtils_RemoveFlag_mB7DAD21EF1BDF82449F44B7BABDC5397B9272ACD (void);
extern void ReflectionUtils_GetChildPrivateProperties_mD3F6F4AFE2C2BDC0DBBFEDAAA528D1F2B3A7D96C (void);
extern void ReflectionUtils_GetDefaultValue_mA716FF1FF01AC5055296E9E00AC32CF4F244F84E (void);
extern void U3CU3Ec__cctor_m160304ED5DA653ACBC05F902E50A0A1B01BE66CB (void);
extern void U3CU3Ec__ctor_m6DDB32E97BC4E58BD446ACD831214F7E773FC500 (void);
extern void U3CU3Ec_U3CGetDefaultConstructorU3Eb__10_0_mAFCDD11DC5EEFCF68C085CA3AFE61BF69DEE27A5 (void);
extern void U3CU3Ec_U3CGetFieldsAndPropertiesU3Eb__29_0_mD091B1A6B829B39D2D171DF3071DF06F409DC525 (void);
extern void U3CU3Ec_U3CGetMemberInfoFromTypeU3Eb__37_0_mBC0347046FB0330330D36E676B133E1650366ADF (void);
extern void U3CU3Ec_U3CGetChildPrivateFieldsU3Eb__39_0_m69B7FC84D992E23D893AB7536184466FE28F749D (void);
extern void U3CU3Ec__DisplayClass42_0__ctor_mE417B697FA318B7DFB472A733CC191B9730EE687 (void);
extern void U3CU3Ec__DisplayClass42_0_U3CGetChildPrivatePropertiesU3Eb__0_m03BA1FF92F094546D609FD43C3A3F1A8DD48C300 (void);
extern void U3CU3Ec__DisplayClass42_0_U3CGetChildPrivatePropertiesU3Eb__1_m4737848D73722527E7202CBD93FE35498A48D109 (void);
extern void U3CU3Ec__DisplayClass42_0_U3CGetChildPrivatePropertiesU3Eb__2_mF422071163C80A910CDED5146AE8CA1F3F75231C (void);
extern void U3CU3Ec__DisplayClass43_0__ctor_m41023B92BEC51A2EF4D669A39DC0067F437A064B (void);
extern void StringUtils_FormatWith_m97587965D365EA1584A7D31B57D618E7768073E5 (void);
extern void StringUtils_FormatWith_mE8641D4F1BDCF64E9876EE3B78F84D7AC377D3E9 (void);
extern void StringUtils_FormatWith_m675CE2F519A21661494F14469C676E52CDCDA7C0 (void);
extern void StringUtils_FormatWith_m5F7ADBFCB56B0DA5D3E4973ECC7BD8E5AB93CF9F (void);
extern void StringUtils_FormatWith_m46141A3CF7D10BDD298594ADC08661098691E0F2 (void);
extern void StringUtils_CreateStringWriter_mE99F16079289F7A6BC74C0794E1C1C60C848EE76 (void);
extern void StringUtils_GetLength_mFA1FEE63F31C09124678579EE94A382B458265A6 (void);
extern void StringUtils_ToCharAsUnicode_m24D950B8B7F90A829F4221DE376C2624B0AAB62C (void);
extern void StringUtils_ToCamelCase_mC5ADD2B4C55E2E242CA489FD45F6F5C76B11E643 (void);
extern void StringUtils_IsHighSurrogate_m47A5F04E84DDD8EFF680AA049FF1813C1F9CA6AE (void);
extern void StringUtils_IsLowSurrogate_mA7F79F10AB65B636D64DBEC4E0F99ED27A0753E7 (void);
extern void StringUtils_StartsWith_m1DF31A6C9FE8815DE2BE508A518E6399EAFD7871 (void);
extern void StringUtils_EndsWith_mD0F53711503882AAAA0040F55410297D44C2FA73 (void);
extern void TypeExtensions_MemberType_mE0E085190B3125B1FACEDF00B26A33742AB46A4B (void);
extern void TypeExtensions_ContainsGenericParameters_mA994EDB52CD98EF1AEB724C41902F89D3D23B557 (void);
extern void TypeExtensions_IsInterface_m9D102CEF96CC2E35ACBD36139B2BB7C476886235 (void);
extern void TypeExtensions_IsGenericType_m7B8CC11BF92A736A0EB33F54E58513C9105A8489 (void);
extern void TypeExtensions_IsGenericTypeDefinition_mB6D5CD38FC73444ED1C98E7B24843E7F3E598C9C (void);
extern void TypeExtensions_BaseType_m08B4843525643FF299FA2E7038C759A8FFDAEF8C (void);
extern void TypeExtensions_IsEnum_mE09A46765105206D627C3C9AE5E011BA8FDD1645 (void);
extern void TypeExtensions_IsClass_mCA21F0211EBFB1812F432BF5C373A00D241C0B41 (void);
extern void TypeExtensions_IsSealed_m32129C70002FD86655A745241FFC4E03A129D2EF (void);
extern void TypeExtensions_IsAbstract_m9A8E7821259AA6C6C79329A8070A7FD730632D4F (void);
extern void TypeExtensions_IsValueType_mD736A971C1BB5B1092EBD01BB9DF7B8D8C1C26BD (void);
extern void TypeExtensions_AssignableToTypeName_m342E81D94CA7C0E397CDD5EBF5C45DD8F6ED0C55 (void);
extern void TypeExtensions_AssignableToTypeName_mDF071FAA34EC2E755656AA6D880EE38A98379C3E (void);
extern void TypeExtensions_ImplementInterface_mB96ABA9168598F26343B1AEE2097D30047BE661B (void);
extern void ValidationUtils_ArgumentNotNull_mC7EBE963D14FFCC7B90B08B403FF584EC520C888 (void);
extern void PreserveAttribute__ctor_m667A671468454D88FA6ABD55B7207DA1A5567771 (void);
extern void DiagnosticsTraceWriter_get_LevelFilter_mECCAA3CA2687CEB9221E985DA157EBD30D7906FC (void);
extern void DiagnosticsTraceWriter_GetTraceEventType_mAD47DFF1D82147C1A78CDA1210CCDFAA155B0C64 (void);
extern void DiagnosticsTraceWriter_Trace_m78B127B8B8CB95B78E12F88AFE6EB13A2FECB750 (void);
extern void DiagnosticsTraceWriter__ctor_m55C1B81D42678399E0ADE85ED0AD268B3132EA2D (void);
extern void JsonContainerContract_get_ItemContract_mA413352FBBCD18D7D315839065DB02B665FD2D0D (void);
extern void JsonContainerContract_set_ItemContract_m7204ECB7E67EBA3E93F7E1165E1D45572F9F33E9 (void);
extern void JsonContainerContract_get_FinalItemContract_m144268C4A088DF790C3A2869DEFFAD75C6C964CA (void);
extern void JsonContainerContract_get_ItemConverter_m3E87FE39B3957C1130F97FA76E5A3A0C1A47C1C3 (void);
extern void JsonContainerContract_set_ItemConverter_mFBF42EA2582D94C174CFAC5772A19B59B7EDEDE7 (void);
extern void JsonContainerContract_get_ItemIsReference_m2EE85CA5003233F533A01FF2F306644D2EC89EC0 (void);
extern void JsonContainerContract_set_ItemIsReference_m3A84BECA20126FB918AC422CB0F08172CE514605 (void);
extern void JsonContainerContract_get_ItemReferenceLoopHandling_mEF16C22A487C11211F145332D61F61CA755E0170 (void);
extern void JsonContainerContract_set_ItemReferenceLoopHandling_mCAAF9FB0B6D30A9F2F08126F89D5B086FB0B8022 (void);
extern void JsonContainerContract_get_ItemTypeNameHandling_mB0B76F5A21FAC4F9F9472B2B33F48DB689C5349C (void);
extern void JsonContainerContract_set_ItemTypeNameHandling_mF1B16C6B7FE2E5DB0B3068F6284BDA286B27A36B (void);
extern void JsonContainerContract__ctor_mF5218A84C856DE38D68CD4B4334A0CFA85A406FC (void);
extern void MemoryTraceWriter_get_LevelFilter_m1C0F2C4C8915E9B2631C3F634D3A59CF759BD757 (void);
extern void MemoryTraceWriter_set_LevelFilter_mAE5693E523F92AB5380A78294E65F9A4AE150F73 (void);
extern void MemoryTraceWriter__ctor_m5B48945B79FDB9E078466FC80F5C902CF693FBA7 (void);
extern void MemoryTraceWriter_Trace_m7F814C717987F6B5B0681B4C5A01E7D8817824DC (void);
extern void MemoryTraceWriter_ToString_mAC35A155DEE5E8649F0384239981D553C67BF8AD (void);
extern void ReflectionAttributeProvider__ctor_mB3C1BFACEB11C9154E1B305F467E9ABDACB4E2EF (void);
extern void TraceJsonReader__ctor_m06DE37B905DD72B8EB3BC54B222534125ACD639B (void);
extern void TraceJsonReader_GetDeserializedJsonMessage_mDE67F24CADD1B725363167B2B292DD83EE3A6AD0 (void);
extern void TraceJsonReader_Read_m60BAE89BEA36D2F57B921E1028E038EEDFCDC562 (void);
extern void TraceJsonReader_ReadAsInt32_m7BEFD39F9C3E3C9CD9674C2C5ADC5D57CA31A395 (void);
extern void TraceJsonReader_ReadAsString_mB62D80DEF26C6726D27884A1B002AE15C5D931CB (void);
extern void TraceJsonReader_ReadAsBytes_m3881373947FC2D38E4E50C6C2837D42D5A789966 (void);
extern void TraceJsonReader_ReadAsDecimal_mB0ABEB6DDFA689482DA322D9730C55B8288DA951 (void);
extern void TraceJsonReader_ReadAsDouble_m945A1C89DE21CC84270A453D21CC61992CF5AE25 (void);
extern void TraceJsonReader_ReadAsBoolean_m50A3735D8687AB62269B43B9C9CBAF34BE5AF55C (void);
extern void TraceJsonReader_ReadAsDateTime_m3C1B224C863DB33E40D1299C6D5EDF85A19BD95B (void);
extern void TraceJsonReader_ReadAsDateTimeOffset_mD7C48696CB5233A763A9FC1A6621021162D15311 (void);
extern void TraceJsonReader_get_Depth_m23F47B60E5AE7A370D664B3083947DE0DF105C37 (void);
extern void TraceJsonReader_get_Path_m6E017BCF388164D8BFFBB73F0EAB0CB33A9974B8 (void);
extern void TraceJsonReader_get_TokenType_mD922306C81C9D3B730D309269524373932331D14 (void);
extern void TraceJsonReader_get_Value_m1CF3260F1F87F988FCF919721E61C874128F9B25 (void);
extern void TraceJsonReader_get_ValueType_m7CC85F32AEC28D84E5CB4CBE6F27ECA6689B3117 (void);
extern void TraceJsonReader_Close_m633D18592EE0F35AB823FABA3AC3F1C067A07814 (void);
extern void TraceJsonReader_Newtonsoft_Json_IJsonLineInfo_HasLineInfo_mE620DACED1F9F4936297D55155C72CD20B027043 (void);
extern void TraceJsonReader_Newtonsoft_Json_IJsonLineInfo_get_LineNumber_m2E8784264E4EB22684FFA2264280DAA787AF0B5F (void);
extern void TraceJsonReader_Newtonsoft_Json_IJsonLineInfo_get_LinePosition_m016538DDB244CA49BC4C4A0D41692FF1AB2112BC (void);
extern void TraceJsonWriter__ctor_m3E3486A6651B23E492D604BCEA2A210249592BA6 (void);
extern void TraceJsonWriter_GetSerializedJsonMessage_mE49F37A44038C9069FD2716280370814EF74B363 (void);
extern void TraceJsonWriter_WriteValue_m387E27345B5DFF27B64D530DFB707CB76CA18298 (void);
extern void TraceJsonWriter_WriteValue_m51D914FD491CE6AAEAAB31550CC06E593D8EEA11 (void);
extern void TraceJsonWriter_WriteValue_m2B5E1E3303C54DA298AEE4038A9C53969F04BDEE (void);
extern void TraceJsonWriter_WriteValue_m854BC68E1EA9C71B6157D1B7165FF8F4809A7408 (void);
extern void TraceJsonWriter_WriteValue_m6741903ED1368EB1DC20CA5B4DE8F6F457CDF4D5 (void);
extern void TraceJsonWriter_WriteValue_mF3DDD270E7C30DEC4A3FF3590A22AEDD9EA6AE95 (void);
extern void TraceJsonWriter_WriteValue_mE72742C0C9C6F700D6690A61CDE2FB375D2997E9 (void);
extern void TraceJsonWriter_WriteValue_m4B5887B0CB0CECB1D7C2834DBA2FBA913F754795 (void);
extern void TraceJsonWriter_WriteValue_m4D25C068AF71BC08626EFC611871C0CEA6C4FFFE (void);
extern void TraceJsonWriter_WriteUndefined_m6C923D2F0292D55D0BD8FA93F61B868243505CE5 (void);
extern void TraceJsonWriter_WriteNull_m872BE8A9120609F190DD07CBA161CD7FCD830139 (void);
extern void TraceJsonWriter_WriteValue_mB1329768856F163D0A45956ABE7A63175A7558A7 (void);
extern void TraceJsonWriter_WriteValue_m9FC06FD22915969AFB105CF7A59CABBF7F38BF9C (void);
extern void TraceJsonWriter_WriteValue_m3824B37C912D99B32985D43AAB2D9397AD2A5B3A (void);
extern void TraceJsonWriter_WriteValue_mE2D4ECEDFB0726636A0C66E9B2188C774DBFE732 (void);
extern void TraceJsonWriter_WriteValue_mE71B3EDDB51FBE77E6C976009E6D56DF30479EFA (void);
extern void TraceJsonWriter_WriteValue_m261489DBC2320E14B49C4FBD928F44E0AD333317 (void);
extern void TraceJsonWriter_WriteValue_mFD2DEC4F0E5F7126C124E1B47E26D6F1D647EBDD (void);
extern void TraceJsonWriter_WriteValue_m775C0788B1A39EB014C2B2577CBDEE04ABC83098 (void);
extern void TraceJsonWriter_WriteValue_m7B25D6F714F20A34CC15A054E62623462B2C998D (void);
extern void TraceJsonWriter_WriteValue_mD67ACF9BB3B554052FB7C49BA8CAFC155780604B (void);
extern void TraceJsonWriter_WriteValue_mF2F45A10E4613BC17F063059001BD7D126381EC7 (void);
extern void TraceJsonWriter_WriteValue_mA1C2AD0B488C9DABE25719063EDADF4F249348D2 (void);
extern void TraceJsonWriter_WriteValue_m7D8B44D6EDEC679D46CA5489A1406FABD602CF94 (void);
extern void TraceJsonWriter_WriteComment_m7B37305B3B0F050CCEF39E7B8E7697524CFCEFFA (void);
extern void TraceJsonWriter_WriteStartArray_mA06D78DCE29C3F0236A0D0413B417108449C4D1D (void);
extern void TraceJsonWriter_WriteEndArray_m37C01D2EE5296D0493ABAE1006DCDEC616EDE783 (void);
extern void TraceJsonWriter_WriteStartConstructor_m4234021DEE68C4B916B0A4FB822EE2B5B46E3C09 (void);
extern void TraceJsonWriter_WriteEndConstructor_m456ECA5BBFA7392CCB58556F5F67F898AED87077 (void);
extern void TraceJsonWriter_WritePropertyName_m56A03E6B57A6774CB12D6808A7ED789CC504FDD9 (void);
extern void TraceJsonWriter_WritePropertyName_m80839F58F0ACFF975B1BEECCFDA10D7E609DF848 (void);
extern void TraceJsonWriter_WriteStartObject_m17F2D91DA646F2D4CAB2E76EDD476B8EB07DBE89 (void);
extern void TraceJsonWriter_WriteEndObject_m42BEBC4A2E6E770006ACA86823F8FD84903F9299 (void);
extern void TraceJsonWriter_WriteRawValue_m6B20B376ECC430CFB2B2DF5CE2DE033607FC6865 (void);
extern void TraceJsonWriter_WriteRaw_m2D39EE6E19B1113B24148C30D7C183B3D00E9CF1 (void);
extern void TraceJsonWriter_Close_m9D548ECEF35BB5A7814CCB77CFC57D92F9C7BAE2 (void);
extern void JsonFormatterConverter__ctor_m2868335DDCD8435A0CF003E7D647B789FFE1B9F7 (void);
extern void JsonFormatterConverter_Convert_mA81F946483B59F3A844A3D069FEAFA389BD8C491 (void);
extern void JsonFormatterConverter_ToBoolean_m3A1A6C00B20EBBA9CE20A1CD809C9708D0F91E12 (void);
extern void JsonFormatterConverter_ToInt32_m2EE11559D61CF8FBA9B882D27A9BB66F4D2166C6 (void);
extern void JsonFormatterConverter_ToInt64_m03669090C8968153B201BEA95615955F79D18A73 (void);
extern void JsonFormatterConverter_ToSingle_m0FE3DDC468076AC4BED8F08D3CB3935C36A8C5CE (void);
extern void JsonFormatterConverter_ToString_m98055A041D194F714750D8C3DD34444176F65ED6 (void);
extern void JsonISerializableContract_get_ISerializableCreator_mAA91A4A389A6EB62B67DB400211DEA4ED8042A47 (void);
extern void JsonISerializableContract_set_ISerializableCreator_mEF41F2A09A138E3EE75A630A8DD64DDF9B85A7A1 (void);
extern void JsonISerializableContract__ctor_m3935F0046EC24721B5FBDF4F9CC45609066E3B26 (void);
extern void JsonLinqContract__ctor_m8C08D3FEC162BEE504D5839A7B9AC219B2B8D260 (void);
extern void JsonPrimitiveContract_get_TypeCode_m0E3438AC9301910CFEE1C15BC070A843A2957099 (void);
extern void JsonPrimitiveContract_set_TypeCode_m52E6AC64F3CE3E13413CDFAC1E5D1EB5AF6B7428 (void);
extern void JsonPrimitiveContract__ctor_m84E9035402FCACFE50DF21AA6DBB66AF6A611A85 (void);
extern void JsonPrimitiveContract__cctor_m5F63AF53A1417526F4B573A6AA0AD1A95771F188 (void);
extern void ErrorEventArgs_set_CurrentObject_m26E745E2EFBC578CD83C2D785DD97BDA8EFDF489 (void);
extern void ErrorEventArgs_set_ErrorContext_m4F32FDE7A7691132AEE98964C66C8640F0F05030 (void);
extern void ErrorEventArgs__ctor_m20915417B7EAA1CE406CEC95B0126432746A0084 (void);
extern void DefaultReferenceResolver_GetMappings_m4084E7573155BE15D3B34AF5F00D1DD68C2ED35A (void);
extern void DefaultReferenceResolver_ResolveReference_m1B79A4390BBFE882B6CB8116CCA7D4F323AE6C20 (void);
extern void DefaultReferenceResolver_GetReference_m2EA734AC7977D28EA3800399E56D35DA7032256C (void);
extern void DefaultReferenceResolver_AddReference_m93166B604F4A4AA65C8C3C8045534E6F6D19335E (void);
extern void DefaultReferenceResolver_IsReferenced_m3B9B18394CEA2E5ADB68A98AD01DE42C7909087A (void);
extern void DefaultReferenceResolver__ctor_m21021524FE387AFB9F24D997468F4A9A02DA4903 (void);
extern void CamelCasePropertyNamesContractResolver__ctor_m71B6E080791024F83516A378AEC9852D4BF284A7 (void);
extern void CamelCasePropertyNamesContractResolver_ResolvePropertyName_mBCADCEA687C5666538AB4FB239D525C08A0EFD4B (void);
extern void ResolverContractKey__ctor_m81CFB26289389B6A3333FC0524DF4EE5E8E3E1B7 (void);
extern void ResolverContractKey_GetHashCode_mA043B0AAF1DD1D96417D00365B90416A8C3A889D (void);
extern void ResolverContractKey_Equals_m0432D1CC6F6ABA61801B0BEED7ECA44B2C9EEED4 (void);
extern void ResolverContractKey_Equals_mEE55B3988692D37E4F7F05E91262DC511323872A (void);
extern void DefaultContractResolverState__ctor_m8110089DA68A665EFE42280439042B52FA84DAB6 (void);
extern void DefaultContractResolver_get_Instance_mF5195CD656C772B020C282CAA82EAA50B52053EB (void);
extern void DefaultContractResolver_get_DefaultMembersSearchFlags_m549575E45ED95951C372501916FB4123B6E6D7E0 (void);
extern void DefaultContractResolver_set_DefaultMembersSearchFlags_m8CD7B21923CC6D975DCD3F00157E090038751791 (void);
extern void DefaultContractResolver_get_SerializeCompilerGeneratedMembers_mDA7E3E17547CE3D23DDE5B794A9C3E7584A11690 (void);
extern void DefaultContractResolver_get_IgnoreSerializableInterface_m5B7D581C6BB2FE170BC492F9C66B304AF8093F4B (void);
extern void DefaultContractResolver_get_IgnoreSerializableAttribute_m1164B44EB2ECF3D091F44D780C95B131B5BE0144 (void);
extern void DefaultContractResolver_set_IgnoreSerializableAttribute_m33F292D8EF5C738E272B51111A1E8F617163FEC3 (void);
extern void DefaultContractResolver__ctor_m52F3E230F523349797B923489423735999EA4A4E (void);
extern void DefaultContractResolver__ctor_m11CEAB39EC7F67E511D46DA81884D7B9CAB7A36F (void);
extern void DefaultContractResolver_GetState_m3B4EEE702394606639A5A15DC896826042BF3359 (void);
extern void DefaultContractResolver_ResolveContract_mCCA714228217D18060D9FD497F14EC9FDB20E392 (void);
extern void DefaultContractResolver_GetSerializableMembers_m74B76C6E25954420529626AEB04E77DD93926C32 (void);
extern void DefaultContractResolver_ShouldSerializeEntityMember_mB9347B1632170CAF3DBDE3C3E25D807BC10044CC (void);
extern void DefaultContractResolver_CreateObjectContract_m169C640662722418EA8FED393D24823729A79B45 (void);
extern void DefaultContractResolver_GetExtensionDataMemberForType_m4790C656AAEFD98DAC4F296F038FB1AAAC633EB8 (void);
extern void DefaultContractResolver_SetExtensionDataDelegates_m2899F75FE30CA0CE45C1DD86949103D92CB99AB7 (void);
extern void DefaultContractResolver_GetAttributeConstructor_m3357DF64AD9D932B8A824F631A91513EF8974625 (void);
extern void DefaultContractResolver_GetParameterizedConstructor_m91C2DAAB5D4A9A223255E4CADC3DF616F79CB353 (void);
extern void DefaultContractResolver_CreateConstructorParameters_mFF13C94BC5A8D254DB2AC87C3413B8DDEA1557A9 (void);
extern void DefaultContractResolver_CreatePropertyFromConstructorParameter_m88731A8B37DC65EF8142024AE3892BC688AC31DD (void);
extern void DefaultContractResolver_ResolveContractConverter_m19C847BE3F615A59F3C0E955F5513A40A091A1A8 (void);
extern void DefaultContractResolver_GetDefaultCreator_mAB208B59B1D325A549039118F896F1B7CA6DCD39 (void);
extern void DefaultContractResolver_InitializeContract_mD92726A76657DD95A257B1D5ABD0F619F1C3AFE7 (void);
extern void DefaultContractResolver_ResolveCallbackMethods_m3404C7A0910498426ABED752D762E0F7566FCEC1 (void);
extern void DefaultContractResolver_GetCallbackMethodsForType_mCCEFB4AEDC1E6197A8015CB1D6DA140498DD1C65 (void);
extern void DefaultContractResolver_ShouldSkipDeserialized_mD192FD03EF902C1C1D7DC56D9D7A6DB3767FA184 (void);
extern void DefaultContractResolver_ShouldSkipSerializing_m2A5E0BE64BDB541ABB29C01C4A32774ECDE7F271 (void);
extern void DefaultContractResolver_GetClassHierarchyForType_m109B1ACFCAAEBAC4FBA3DD71CFF4AEC875628A42 (void);
extern void DefaultContractResolver_CreateDictionaryContract_m69CB0EBB8CC0F79507F48D85B647B20C9B0E6969 (void);
extern void DefaultContractResolver_CreateArrayContract_mC312CC56C381CAE1B50C72F260A2181DA63AC51F (void);
extern void DefaultContractResolver_CreatePrimitiveContract_m000A0E84565AA866DF08830D431B8E7620A9597F (void);
extern void DefaultContractResolver_CreateLinqContract_mD3DDE786CD06A1002582F15DDC0B6F116BD6B89F (void);
extern void DefaultContractResolver_CreateISerializableContract_mEE1FA85CDFFBFDABE8AF40E9E20847231CEBA367 (void);
extern void DefaultContractResolver_CreateStringContract_m99A18E510EF500DC52A3DAD78543E6FB49AFB345 (void);
extern void DefaultContractResolver_CreateContract_m856C42DFFC8BC7407B4C9D2F8CC8F8D165CE8678 (void);
extern void DefaultContractResolver_IsJsonPrimitiveType_m1FCBA966577856D7FC5CEF79B7B9E0B3F7747694 (void);
extern void DefaultContractResolver_IsIConvertible_mAD611B13EC99D605790E7175312BC468E87497C0 (void);
extern void DefaultContractResolver_CanConvertToString_mD9989BD88FFA5A954234A17DEEADB8455BC823ED (void);
extern void DefaultContractResolver_IsValidCallback_m8D9EA5D1C5AF473EAE3324B3E1C62428CB121CB7 (void);
extern void DefaultContractResolver_GetClrTypeFullName_m18CEF60842A1ECB14BEDF84B4759E8A27E6FFFF2 (void);
extern void DefaultContractResolver_CreateProperties_mB299696CA0E7060E307E7F54D0E94586B7B2869F (void);
extern void DefaultContractResolver_CreateMemberValueProvider_m336BFE31B7B854585C703D5F8D9C306B99C4AB6F (void);
extern void DefaultContractResolver_CreateProperty_m7C27609BD46BF2A2144D8D21F10419BFDF88374F (void);
extern void DefaultContractResolver_SetPropertySettingsFromAttributes_m2F76CCB18F6416C34D521D8FE6B4A71F1E669F45 (void);
extern void DefaultContractResolver_CreateShouldSerializeTest_m295E60C21E1BD022604D3E78517D0E97F3FD5FF4 (void);
extern void DefaultContractResolver_SetIsSpecifiedActions_m659959226A92F854516014E205F5F96FAC52F99F (void);
extern void DefaultContractResolver_ResolvePropertyName_mF1402600A96B19B20715D222FE9F45E78AED400B (void);
extern void DefaultContractResolver_ResolveDictionaryKey_m6D69FA89770161DFC3CD1E7619698D64B3CF8B2F (void);
extern void DefaultContractResolver_GetResolvedPropertyName_m1934B029B73CD108AECF3A916CB3F5172E4FD073 (void);
extern void DefaultContractResolver__cctor_m33FAF927613DCD98A37DB435848F063D6DBCB2B7 (void);
extern void U3CU3Ec__cctor_mC215E6FDE3FAF7F067C22A77263B2FF5A517860E (void);
extern void U3CU3Ec__ctor_m3E79F6CFBF3C721ACAF30D518663E0B15EB9F422 (void);
extern void U3CU3Ec_U3CGetSerializableMembersU3Eb__30_0_mFEFE90181AEFF3AB507A3D174C30B96FC41B5348 (void);
extern void U3CU3Ec_U3CGetSerializableMembersU3Eb__30_1_m559B776BA2840D31A1E40D24EEE9089786283AC2 (void);
extern void U3CU3Ec_U3CGetExtensionDataMemberForTypeU3Eb__33_0_m057A45DC560266F46A77E628B976EBC57E619A6D (void);
extern void U3CU3Ec_U3CGetExtensionDataMemberForTypeU3Eb__33_1_m0CFBD09387805A25D1758D44FCA9F0FEC349ED68 (void);
extern void U3CU3Ec_U3CGetAttributeConstructorU3Eb__36_0_m7C0532A4B9E0F6E88E049ED30E29BD319248C47D (void);
extern void U3CU3Ec_U3CCreatePropertiesU3Eb__60_0_mC59729DAEEB1CB386F97D3B2560BBA503109DBFF (void);
extern void U3CU3Ec__DisplayClass34_0__ctor_mEE9EC7C90C171882F62F47A469AD829A13824021 (void);
extern void U3CU3Ec__DisplayClass34_1__ctor_mC9885B8D4584E3B00F12F61D3F7EC941CCC193D5 (void);
extern void U3CU3Ec__DisplayClass34_1_U3CSetExtensionDataDelegatesU3Eb__0_mC38A1C3A2912AC02AC5089285F917A255D06B694 (void);
extern void U3CU3Ec__DisplayClass34_2__ctor_m0D85569F5E5FD7CBC8CEBBB14D4CBE6F8D316F21 (void);
extern void U3CU3Ec__DisplayClass34_2_U3CSetExtensionDataDelegatesU3Eb__1_m3B63677F446C68D2D08467EEEE52AD89F69B14A6 (void);
extern void U3CU3Ec__DisplayClass64_0__ctor_mBBB2E8C987040040CE2A743761AFCF56CE623828 (void);
extern void U3CU3Ec__DisplayClass64_0_U3CCreateShouldSerializeTestU3Eb__0_m7B52B342CAEAF56D64F40447A119E27B5D89029E (void);
extern void U3CU3Ec__DisplayClass65_0__ctor_mADEF15F981F5C8DEC069D30741FD2133A8E5FE03 (void);
extern void U3CU3Ec__DisplayClass65_0_U3CSetIsSpecifiedActionsU3Eb__0_mE8ADD39E3C9824984921449ECB333705AE38ED9B (void);
extern void DefaultSerializationBinder_GetTypeFromTypeNameKey_m2B4627D6534284A400DB5E24166784D0D8791B5B (void);
extern void DefaultSerializationBinder_BindToType_m599FDE47FCFFD24698CFFEEE04C52E983F50A8CD (void);
extern void DefaultSerializationBinder__ctor_m1A42DF8399610D98F0F71B458F3CCF6CF2AD5A7F (void);
extern void DefaultSerializationBinder__cctor_mC5654BC18CEF69ADE581983237D646EFB298F6EA (void);
extern void TypeNameKey__ctor_mEB5446E840F899A22E0CFE0B3DB83D323EA29AAD (void);
extern void TypeNameKey_GetHashCode_m2818F664C6CD4EA974EBD5D17147C28D1A903A98 (void);
extern void TypeNameKey_Equals_m351AC7FD99EF650F601259E1DB999D84DC2E3E6D (void);
extern void TypeNameKey_Equals_m87BCAE319F7BFF2F369E9F8877F04051EB8160AC (void);
extern void ErrorContext__ctor_m035618BB23F49DA853D3F4D959F700A0A6DBE4BA (void);
extern void ErrorContext_get_Traced_m75237BD7FA2271E3CCDD049E74DF20D09676E598 (void);
extern void ErrorContext_set_Traced_m191C23FDE26BB7BDF9F40B9CDE8C430A646F7922 (void);
extern void ErrorContext_get_Error_m0A859EFFCE6A600DD3580C3771F240C8BDE74D54 (void);
extern void ErrorContext_set_Error_m82C60FAD6A5B51461030AFA1C01B79567CDD9ADD (void);
extern void ErrorContext_set_OriginalObject_m7E9A24F7426734D75794A6C4C38F7B09563B8E77 (void);
extern void ErrorContext_set_Member_mC24D577A564BF09ECADF3EEC08D076F11B93165C (void);
extern void ErrorContext_set_Path_m422496546D56168A5F5886AED89327AEDA5C8D05 (void);
extern void ErrorContext_get_Handled_m31C43173C83008776CA50B6BF4F9990C0DE48742 (void);
extern void JsonArrayContract_get_CollectionItemType_m323C31B1A257D6EDD322D46EB8B8E168AA24C90F (void);
extern void JsonArrayContract_set_CollectionItemType_mA076DBD0C386C7DD94A1F73BA04E1758C8DFA195 (void);
extern void JsonArrayContract_get_IsMultidimensionalArray_mB6C08E190146C9810A18D55A25A2DEACA423CCEF (void);
extern void JsonArrayContract_set_IsMultidimensionalArray_mC15445408825E9072EFF96A0D78180D043EF7C58 (void);
extern void JsonArrayContract_get_IsArray_m030F748DF4D1E37CEF657B66BBAB4A527C1DD650 (void);
extern void JsonArrayContract_set_IsArray_mE79B995AEA911E7CF3B7D774C0D9458F1553B0BC (void);
extern void JsonArrayContract_get_ShouldCreateWrapper_m2607DDBD1D4CEF3528464ABD2CB93989D901D275 (void);
extern void JsonArrayContract_set_ShouldCreateWrapper_m5364CD0392E0F3007787C7A947A57D740E1DA0E0 (void);
extern void JsonArrayContract_get_CanDeserialize_mA4269673785752E43430D41088371176505F8554 (void);
extern void JsonArrayContract_set_CanDeserialize_m6EE7125CB72BF048836FEBA8A1D2EA22DE19DA7D (void);
extern void JsonArrayContract_get_ParameterizedCreator_m103599E8B4611CE27204D897E9065250FEF5AB35 (void);
extern void JsonArrayContract_get_OverrideCreator_mC67591267D84CBDF275F012EA946F0515485E7AA (void);
extern void JsonArrayContract_set_OverrideCreator_m574FF3E00B0156ED9CC5FC73582552EEC6FA0CD5 (void);
extern void JsonArrayContract_get_HasParameterizedCreator_m9DFE178941F031612E7A02608BAF9EABA2DD7FF0 (void);
extern void JsonArrayContract_set_HasParameterizedCreator_m30821EFEBDCC26B3CEF2F7EE67FE512C45783A94 (void);
extern void JsonArrayContract_get_HasParameterizedCreatorInternal_mD8EBC9C72E26D0F5EA773590B073C58ADA79E8ED (void);
extern void JsonArrayContract__ctor_mAA81A5943EEC3B703838ACFF20C15B8BB192B268 (void);
extern void JsonArrayContract_CreateWrapper_m680121FFBC450F2B9612687063974FC32D6B14D6 (void);
extern void JsonArrayContract_CreateTemporaryCollection_m2CF721D76CC983419F0B128471C0E2AF02D0E8A1 (void);
extern void SerializationCallback__ctor_mD20943B9A19E3372B683FE88570DF24A91842B32 (void);
extern void SerializationCallback_Invoke_m8409A73F0B02AD97D8044C018E80784BF4F39995 (void);
extern void SerializationErrorCallback__ctor_mC0C873DD8EDE00B5545827889D56AB6CCFF5E0B1 (void);
extern void SerializationErrorCallback_Invoke_m5A232ABC19DEA18BB86BB3FAF6601D495C4AAAFC (void);
extern void ExtensionDataSetter__ctor_m65A566850CC5193E76AED8FD0AB45511D5128AF3 (void);
extern void ExtensionDataSetter_Invoke_m59E16DF7974C758788DA302C186DB519792F2403 (void);
extern void ExtensionDataGetter__ctor_mD30966A61D48BB798304C72948502F050E4199FB (void);
extern void ExtensionDataGetter_Invoke_m896D50D58EBA6C316B674EDA4AA3068284CF1576 (void);
extern void JsonContract_get_UnderlyingType_mD9A37A6619D3F541F77C3E09ED8242511AFCBEB7 (void);
extern void JsonContract_set_UnderlyingType_m92A57F57AD493D79C6AF840131A4B7BFB8C0BCF1 (void);
extern void JsonContract_get_CreatedType_mCEA0A64AA856091792CF9E7B70D9CDDBE2BCD9FC (void);
extern void JsonContract_set_CreatedType_mB9B232317567D5530B9C9E238C4F94BCEFDD6A67 (void);
extern void JsonContract_get_IsReference_mD17DF9BD44DDC490F282A213FB7AD38A28000EF9 (void);
extern void JsonContract_set_IsReference_mFC973648198CB8FE16D145288C566252D19CDCC4 (void);
extern void JsonContract_get_Converter_mF0D436033DBF3D355CBEBD07027D46C4B1ECF8EE (void);
extern void JsonContract_set_Converter_m68807F8BD7B34D2056C85464D0F74EDB68464870 (void);
extern void JsonContract_get_InternalConverter_mBE202C7F3676B1D18B8189891952B728F55ED1C7 (void);
extern void JsonContract_set_InternalConverter_mF21E059A42EC399C177E6A5E126F2673047FBDFD (void);
extern void JsonContract_get_OnDeserializedCallbacks_mD7648B190FC47079D2F072AABC593F16443CB1AE (void);
extern void JsonContract_get_OnDeserializingCallbacks_mBCEDDD83766E804875C007D6192DA0F41C379D3C (void);
extern void JsonContract_get_OnSerializedCallbacks_m4C32E26B665251C4187F4C4BAC6BA22346FE44C6 (void);
extern void JsonContract_get_OnSerializingCallbacks_mD598741E61FD4DAD483A02BA78F9C14B9DB626F1 (void);
extern void JsonContract_get_OnErrorCallbacks_m33B0B311D9EA13E0945832A96BAF03E50C1ECCEE (void);
extern void JsonContract_get_DefaultCreator_mCFA7A4D151451FF9EE54042A8DB72BAAAD2B8CC9 (void);
extern void JsonContract_set_DefaultCreator_mBA111899C3FA1C007BF522FD0FB139C41E6C5EC5 (void);
extern void JsonContract_get_DefaultCreatorNonPublic_m5A1BDEA1A4CE3886E6BD60DC0BD66694FB2E45CC (void);
extern void JsonContract_set_DefaultCreatorNonPublic_mB9D2AFC01ADA985286A0469B72F3256A0F6C7279 (void);
extern void JsonContract__ctor_mB6B8A88C0CEA9E3A1BFF6E1CDC258C344695923A (void);
extern void JsonContract_InvokeOnSerializing_m26CFBE31E56ED68AC2E82B82B21F8B3ACA8A440D (void);
extern void JsonContract_InvokeOnSerialized_mF3C287F02D40385559E8B7439BED3DCB312E0828 (void);
extern void JsonContract_InvokeOnDeserializing_m4EA5745B0B616B83335A846B3A745D8F1ECE1E12 (void);
extern void JsonContract_InvokeOnDeserialized_mF40847EDCE74634AB7078AAA1B1C10EB2B03E5CB (void);
extern void JsonContract_InvokeOnError_m9A2C2FF5B883828D54E7A8FB4E5FB7CAA10EE1C9 (void);
extern void JsonContract_CreateSerializationCallback_mF78A6E17BE907CE1003CA35C9C0B44810468D7DC (void);
extern void JsonContract_CreateSerializationErrorCallback_m086A2A98E30872487CF25E074A251887045805AD (void);
extern void U3CU3Ec__DisplayClass73_0__ctor_mC78FBC5F9E691FC052999CF4F6D65F9C9457965C (void);
extern void U3CU3Ec__DisplayClass73_0_U3CCreateSerializationCallbackU3Eb__0_mBA7D82DD46FBDE2CE0BD1C006EB17E2E0503D5EB (void);
extern void U3CU3Ec__DisplayClass74_0__ctor_m5ACE7CD3862E51EB1EF22351B62CB66AEF5A81E4 (void);
extern void U3CU3Ec__DisplayClass74_0_U3CCreateSerializationErrorCallbackU3Eb__0_m90A98055512BAF9A8ECEA385D1C065DA1553DD7F (void);
extern void JsonDictionaryContract_get_DictionaryKeyResolver_m05162FCF443E5670E454B20DE7821EB5D821E7BA (void);
extern void JsonDictionaryContract_set_DictionaryKeyResolver_m76D8596A2262205278E9DB152586F7C7202EE13B (void);
extern void JsonDictionaryContract_get_DictionaryKeyType_mE92EF750094C636DF354F2ED771B8918A897BBDF (void);
extern void JsonDictionaryContract_set_DictionaryKeyType_mECD2E157E2F9506C495CE23C2B2D8FC024CAC5CB (void);
extern void JsonDictionaryContract_get_DictionaryValueType_m278FF42A97ABD7B6F6F952C7D17182FD2A08704B (void);
extern void JsonDictionaryContract_set_DictionaryValueType_mFD9BA2020E292DF60E3AC66BC41C2A46A0E6D7D4 (void);
extern void JsonDictionaryContract_get_KeyContract_mFF0AFD10D2882644B9401B36EA7C912262A1A4E1 (void);
extern void JsonDictionaryContract_set_KeyContract_m536A77FB0422117D8D5398B7699AB6F54827D6D4 (void);
extern void JsonDictionaryContract_get_ShouldCreateWrapper_m448EB531A7D9FB6B0D14474DF4123681B642F0BE (void);
extern void JsonDictionaryContract_set_ShouldCreateWrapper_m70F1F112136B9C000785D7B113C9E980920DD179 (void);
extern void JsonDictionaryContract_get_ParameterizedCreator_mB1F6EE4E1C57C47A323DB1D5C2A5E3D88AE07E80 (void);
extern void JsonDictionaryContract_get_OverrideCreator_mA4C74B4283C2056367C12517CD092273FF9EE9C3 (void);
extern void JsonDictionaryContract_set_OverrideCreator_m072E34CED83EF4035CC4BA7C7E3608132AB3F6CA (void);
extern void JsonDictionaryContract_get_HasParameterizedCreator_m6F137AB9FD4E909E425E1DCD934C7EC80CB40279 (void);
extern void JsonDictionaryContract_set_HasParameterizedCreator_m1C8461874E8D2B63DE6A32DBFAC98F0BAE9D7730 (void);
extern void JsonDictionaryContract_get_HasParameterizedCreatorInternal_m81D7F1F0D7741C019D1DF7B62DDECAC177E5F490 (void);
extern void JsonDictionaryContract__ctor_mF9F1BD61E21BCF52C1151250CA2D6060B26B489B (void);
extern void JsonDictionaryContract_CreateWrapper_m87106D567DA1464E141F6FBED07D035A0FB6C656 (void);
extern void JsonDictionaryContract_CreateTemporaryDictionary_m295E740929FDF568B0AB1DBA55EB087463C76471 (void);
extern void JsonProperty_get_PropertyContract_m02E7EB329EBDB11A555F5346C26A23B9DC3DF5A1 (void);
extern void JsonProperty_set_PropertyContract_mBEA05DCBE14D84763F5ADB99D7DF41C486EFB495 (void);
extern void JsonProperty_get_PropertyName_m66C0A76B6380773DCA04B91C40032EE6CC0F7FFE (void);
extern void JsonProperty_set_PropertyName_m9CED5A907D22FA06A57494405F5C1EE98E1054ED (void);
extern void JsonProperty_get_DeclaringType_mD3ED136331FD1A9E37C98EB5579932CCA71DD69B (void);
extern void JsonProperty_set_DeclaringType_mCC360EE3878D9ADBBF43D9C0041CF0EE688D32BA (void);
extern void JsonProperty_get_Order_m1ADF90195EA27D17F3C9904A55EB3716EEABED41 (void);
extern void JsonProperty_set_Order_mEDD3BA19A3946816500CBBFBC4F46F6B7B257774 (void);
extern void JsonProperty_get_UnderlyingName_mEDB340917C1AC5F0C5861DDA93FB89EEB4B29CAC (void);
extern void JsonProperty_set_UnderlyingName_mC12C03A61DF03BE56C9F108E6C15D8DE5D0E4CCE (void);
extern void JsonProperty_get_ValueProvider_mFBD26BEA616A3B6A02C78431D93741243808AC63 (void);
extern void JsonProperty_set_ValueProvider_m3E52A4C43012A13A874804F1539A9F50CC54D4BA (void);
extern void JsonProperty_set_AttributeProvider_m3FFF05C4EEE1FAAF5B733483B7416AF806DC052D (void);
extern void JsonProperty_get_PropertyType_mA68B958EF3FA448DE8073330143B58DE59274C02 (void);
extern void JsonProperty_set_PropertyType_m4D1F70CA66382EF37C95083B5B90310EFD44F9AF (void);
extern void JsonProperty_get_Converter_mF3E03B156CCD94CE67ABAA1D7298F7618505D5AB (void);
extern void JsonProperty_set_Converter_mFC9963279952098BC4C2C67CB585A48EF9C725D8 (void);
extern void JsonProperty_get_MemberConverter_mFCF2E970B82539A50CD0161A5ED0F6DBC80D4293 (void);
extern void JsonProperty_set_MemberConverter_mEC21306A5EFB6CC1BE3B1D4EEBD6FFC5ECB822D4 (void);
extern void JsonProperty_get_Ignored_mE919F5938030A483FBFDE610267A812A56FF0AA4 (void);
extern void JsonProperty_set_Ignored_m1F9CA36C6C94DD3E563A4A1C8462DE5FBA6F5558 (void);
extern void JsonProperty_get_Readable_mD6F8E84CF020FB4FB9F3CDFD438BAE796C0799A7 (void);
extern void JsonProperty_set_Readable_m1AADD57C10234EA26128DF1CBED2C731E5104837 (void);
extern void JsonProperty_get_Writable_mC42D3422F282EA472179F6A45B97ABE13D9815AB (void);
extern void JsonProperty_set_Writable_m758C34133078AAA8BC826D5F206D8B13FEA2E99E (void);
extern void JsonProperty_get_HasMemberAttribute_mD345EDBF9DFDBFC4E514FDBCAC56A8012AE2CE3D (void);
extern void JsonProperty_set_HasMemberAttribute_m92865493E79053F8C02419E5B7798CA7758ED374 (void);
extern void JsonProperty_get_DefaultValue_m4C70DE40A0A057B08F1861B17F427B875BD71EC7 (void);
extern void JsonProperty_set_DefaultValue_m8246A51C252AD8DBF71E897378DBE35169A645A0 (void);
extern void JsonProperty_GetResolvedDefaultValue_m27B72BD74F55C0440C27166AEEE4AB9212BFFE48 (void);
extern void JsonProperty_get_Required_m3FEAC228BB0A9B09B2A1BD88C3A5A0B48B511C9A (void);
extern void JsonProperty_get_IsReference_mBC2FA55AAD460313375EEA548D16748BA072FB49 (void);
extern void JsonProperty_set_IsReference_mA04A81F2AB4228211644F28A9D128605A3C8BE76 (void);
extern void JsonProperty_get_NullValueHandling_m66711C6E8112FF7FF2BA13DA977FE4C66D8D4B1A (void);
extern void JsonProperty_set_NullValueHandling_m1975598294F96AAF9FB5C2EADBFA7A3B75B6B426 (void);
extern void JsonProperty_get_DefaultValueHandling_mB9898AE13350D06964AA3C38D383D2A3E83BE295 (void);
extern void JsonProperty_set_DefaultValueHandling_m86A51D1BF0A4C8A1AC1F175216335B9DD6B1D1B8 (void);
extern void JsonProperty_get_ReferenceLoopHandling_m19BE73BF4CB3BFCEF390ED8EFDA9F84C15958FCA (void);
extern void JsonProperty_set_ReferenceLoopHandling_mF0D9D07EBAE26AC5B89CEC817700D81CA4A37C62 (void);
extern void JsonProperty_get_ObjectCreationHandling_mCB48C650C866006B2784BF3D1058029F40BCA566 (void);
extern void JsonProperty_set_ObjectCreationHandling_m3A11003CF805D723E78DF85DBD6F52CAC6175198 (void);
extern void JsonProperty_get_TypeNameHandling_mB9ADD59B98F0FB3D50AC0487EC91422CE6C0F491 (void);
extern void JsonProperty_set_TypeNameHandling_m02D63F2A4611B9AFC60C04304CF8B3D0E5216134 (void);
extern void JsonProperty_get_ShouldSerialize_mB3B4FA0A6478FCB636C195C0F4CB49FB10C28732 (void);
extern void JsonProperty_set_ShouldSerialize_m46EE6AE304CECB231A5042488EA30F61A140741D (void);
extern void JsonProperty_get_ShouldDeserialize_m358AFC686584F117C9C48FEFC07B5A91F6698D07 (void);
extern void JsonProperty_get_GetIsSpecified_m424B052A66EC6FEF6259BDDAF89C9FAE6CFB1E96 (void);
extern void JsonProperty_set_GetIsSpecified_mC836374B47C73B17A7860AF08F28FB8B49C81F28 (void);
extern void JsonProperty_get_SetIsSpecified_mC4D36557CAED12E81F30E0D94CEE102D9FA2C36A (void);
extern void JsonProperty_set_SetIsSpecified_m177EC7F85AA7A3059301906F3DB393B5C16A950C (void);
extern void JsonProperty_ToString_mDD97B31B703EED366B2EC79F5D275B73054CAD34 (void);
extern void JsonProperty_get_ItemConverter_m8128D44A080AFA04F91E89CC5C13A1C2D0231CD1 (void);
extern void JsonProperty_set_ItemConverter_m0E9488FFFBC280CA54D5D58279F25F305303140B (void);
extern void JsonProperty_get_ItemIsReference_m780EF41FD15292901B279EA4CDEAA932CCF0A1BD (void);
extern void JsonProperty_set_ItemIsReference_m18D38CB29FB7D25D4EDCB2810BB73DB9C0066DEB (void);
extern void JsonProperty_get_ItemTypeNameHandling_m4AB89D2F44DCFEFA59B89477EE92CB042D1A6D76 (void);
extern void JsonProperty_set_ItemTypeNameHandling_m8B707B8D50203DDCF2C445BDD1C4D3CF0E14D93A (void);
extern void JsonProperty_get_ItemReferenceLoopHandling_mA9780B84D18D57C7806A262466C8D41F178AF01D (void);
extern void JsonProperty_set_ItemReferenceLoopHandling_mEB8E34E485BFBCE173A075858772127C14790FC5 (void);
extern void JsonProperty_WritePropertyName_mE4F8374128AA7A8DDA5F04B7219D864C96544E6D (void);
extern void JsonProperty__ctor_mA017211DF2D2DF2E6EFB14A40F67D313E82C982B (void);
extern void JsonPropertyCollection__ctor_m3BF51B17031B29E73845BE135782472BCF43C5D4 (void);
extern void JsonPropertyCollection_GetKeyForItem_mD29ED556D709E50D31E58CB169647C2739D7EDF4 (void);
extern void JsonPropertyCollection_AddProperty_mAC7E17C29B8A0DC1F39720B0B011B468EDC2727B (void);
extern void JsonPropertyCollection_GetClosestMatchProperty_m051B5200B3BBEFB62E4351D1B2376A507D061A77 (void);
extern void JsonPropertyCollection_TryGetValue_m409F84885C8FEF11287E5CCBE3C473691E4C03BF (void);
extern void JsonPropertyCollection_GetProperty_m78881F5BDF660C0DB29B343B024FFA5A18DB8355 (void);
extern void JsonObjectContract_get_MemberSerialization_m7D72CAE3141E92134D1EFF6E16F3E42261E83D64 (void);
extern void JsonObjectContract_set_MemberSerialization_mF9F1037CB899F4051EF74DE5AA4C4305B7635E2F (void);
extern void JsonObjectContract_get_ItemRequired_m69F93A48186F68820EAF3BDF5E3199FE8CA96360 (void);
extern void JsonObjectContract_set_ItemRequired_m052D55E293C851B8B0424EB8FCB71D501E8B23E2 (void);
extern void JsonObjectContract_get_Properties_m7074363E992F9DC8149D08D884F0C055108D5AE6 (void);
extern void JsonObjectContract_set_Properties_m971F07E834BFF3A5C8D392007D4ACB850E7C5B53 (void);
extern void JsonObjectContract_get_CreatorParameters_m3ACDF7DF733EDDCF2D1641084DC3C2AB56C6742E (void);
extern void JsonObjectContract_set_OverrideConstructor_mC2429ED1B6BC5A22C89C05F83C0D4C3D721F3AA2 (void);
extern void JsonObjectContract_set_ParametrizedConstructor_m8BAD4CD30209ED11A7B4CA23D69C88E185E5D47E (void);
extern void JsonObjectContract_get_OverrideCreator_mB3BCF517936293F88487DC88A2BB093E017B9DC6 (void);
extern void JsonObjectContract_get_ParameterizedCreator_m93F6DD6320FDB98CDFC4633732DF4F423279D10A (void);
extern void JsonObjectContract_get_ExtensionDataSetter_mA4BEB602420602B3A00A0879B8AB514F48924088 (void);
extern void JsonObjectContract_set_ExtensionDataSetter_mD1F11813547C8550A386D2A7F89E64976EA3D170 (void);
extern void JsonObjectContract_get_ExtensionDataGetter_m29DBB95A4E1AC3DF265A4E968133C2F2E935971C (void);
extern void JsonObjectContract_set_ExtensionDataGetter_mE40DD7E687AF9F1778D91715C1D73EB222D0E852 (void);
extern void JsonObjectContract_set_ExtensionDataValueType_m71A33B2C1565DB97FF2502DC9993DA89EB62C562 (void);
extern void JsonObjectContract_get_HasRequiredOrDefaultValueProperties_m56809E3E09FC66C9645700137AE9DBA8633E559A (void);
extern void JsonObjectContract__ctor_m924A44A5810F1962B452C04E5E467061F29D7228 (void);
extern void JsonObjectContract_GetUninitializedObject_m573704FD87BAC6EEE3AF234B71E2C38E0BB26B43 (void);
extern void JsonSerializerInternalBase__ctor_m485CED903346F736AC90B76C528096CEA1E785D5 (void);
extern void JsonSerializerInternalBase_get_DefaultReferenceMappings_m18D3525B8293E8D71B92971F133887F247D02E4D (void);
extern void JsonSerializerInternalBase_GetErrorContext_m87BFBBBE149D62F41CBE8811707FAEA4147BE4DD (void);
extern void JsonSerializerInternalBase_ClearErrorContext_m4F40B91D6AEFF4C046FD8AF7801B148A2F1DF23E (void);
extern void JsonSerializerInternalBase_IsErrorHandled_m03744F32BCD5F528B09B5324219085C2CCF59C91 (void);
extern void ReferenceEqualsEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_Equals_m4B800A4FE0856AE30F5006F034C27DE0D8FBB7F9 (void);
extern void ReferenceEqualsEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_GetHashCode_m8E6A140FB15CF1F92AC78C978CFF69C100489270 (void);
extern void ReferenceEqualsEqualityComparer__ctor_mEB8CDF0FCF2F4EE6C798B07408DB7DD984EDEC26 (void);
extern void JsonSerializerInternalReader__ctor_m0CC3AF92136D2B21D9EE73433F27D362A8427BF1 (void);
extern void JsonSerializerInternalReader_Populate_m1E3334B2A04698CACCC6769E77AA3F9E03A7C7D8 (void);
extern void JsonSerializerInternalReader_GetContractSafe_mBC44A2DD8301E5B9A85EE4CB9960C06110F23FBB (void);
extern void JsonSerializerInternalReader_Deserialize_m37E61DFB1A0016D0CCCFCF33D489BD079BD65EF3 (void);
extern void JsonSerializerInternalReader_GetInternalSerializer_m6EBF9AD9A2495FE4BA717E7F6BCEEF8F41C30448 (void);
extern void JsonSerializerInternalReader_CreateJToken_mFED9EEB3CF08D61A5EE7956D6DCC69FD2763BD53 (void);
extern void JsonSerializerInternalReader_CreateJObject_m7B2913A1D866741B067B707AEDDE17E894F62FE1 (void);
extern void JsonSerializerInternalReader_CreateValueInternal_m2951B28851F7EF17051BC3178678ECE5664BFAAD (void);
extern void JsonSerializerInternalReader_CoerceEmptyStringToNull_m58502C73921318333E82635D77B28360617D6256 (void);
extern void JsonSerializerInternalReader_GetExpectedDescription_m2D9308B8409615439942F8E129084D2E13FAED59 (void);
extern void JsonSerializerInternalReader_GetConverter_m0F9028F2963C85A675C5B61135219B834F94DAAD (void);
extern void JsonSerializerInternalReader_CreateObject_mD7F8E57151B7EFE0B4A883F21D71B9B7A6AB5662 (void);
extern void JsonSerializerInternalReader_ReadMetadataPropertiesToken_mA3EC65A66F0FBD10E72CCCD62FE5AF85B2550615 (void);
extern void JsonSerializerInternalReader_ReadMetadataProperties_m213E4EE3865491891B5DF24D2BCAFDFDE92217F7 (void);
extern void JsonSerializerInternalReader_ResolveTypeName_mF3775D685B08A3F909F4417CB01F98053D53DD20 (void);
extern void JsonSerializerInternalReader_EnsureArrayContract_m2C58864925AA3A898716AE1405504C13D091FCE5 (void);
extern void JsonSerializerInternalReader_CreateList_mDCB7FD9511B2825840C589D55AB1092285B80313 (void);
extern void JsonSerializerInternalReader_HasNoDefinedType_m7988F06036E94625BDDF81963BB0557EA59BA2CE (void);
extern void JsonSerializerInternalReader_EnsureType_mF3E312096D7E5E6076D46CC91BC21C96FA149F04 (void);
extern void JsonSerializerInternalReader_SetPropertyValue_m54850CF16B76AC4D47E822671C33B562D399BFEB (void);
extern void JsonSerializerInternalReader_CalculatePropertyDetails_m55C55D3019745AD567897335D7A4E8DFD17490F3 (void);
extern void JsonSerializerInternalReader_AddReference_m2B7E2B34B236F2DAB1FEC17B605024C81833C65D (void);
extern void JsonSerializerInternalReader_HasFlag_m3028438FB7E8743CD264B9F9AE91B127E707CAEF (void);
extern void JsonSerializerInternalReader_ShouldSetPropertyValue_m1F09761C8E2DCF8EAA4E54FB4120401FC07CE059 (void);
extern void JsonSerializerInternalReader_CreateNewList_m7CA6E1CA3CD2583C84A481EA84685D8D7B7524E6 (void);
extern void JsonSerializerInternalReader_CreateNewDictionary_m9F3162A6810CED7A82588B88787D768795BB98B9 (void);
extern void JsonSerializerInternalReader_OnDeserializing_mC62A6755DC18EFFD97F842D0970C8C54776FFC04 (void);
extern void JsonSerializerInternalReader_OnDeserialized_m6130B5B232E4A3D0217AE876B4E06C2375832FDE (void);
extern void JsonSerializerInternalReader_PopulateDictionary_mD04B93690D3EB9C4892EE5236B958684A15F70ED (void);
extern void JsonSerializerInternalReader_PopulateMultidimensionalArray_mDE8E4305F3A5E9A2D4DC7C00F1C3E6D187F78132 (void);
extern void JsonSerializerInternalReader_ThrowUnexpectedEndException_m2081CD321452B270E11B702FDA9D76B8C2B2A9E1 (void);
extern void JsonSerializerInternalReader_PopulateList_m6B54CFA5F4A7A0C8773CD47B810F7CC1E245366D (void);
extern void JsonSerializerInternalReader_CreateISerializable_mA896FB8CD4ABE1FDBD914114C5A0F23058D95D68 (void);
extern void JsonSerializerInternalReader_CreateISerializableItem_m49EC43E878AA913A64C3E0418FFB62888DE1AB00 (void);
extern void JsonSerializerInternalReader_CreateObjectUsingCreatorWithParameters_m1C26F454DDB083972F36EBE27C1C3C6361FF21A3 (void);
extern void JsonSerializerInternalReader_DeserializeConvertable_mC9BACED43FB0B34DC6E93F74289F0CEA2B426FB5 (void);
extern void JsonSerializerInternalReader_ResolvePropertyAndCreatorValues_m0F5755EA90D72BB2EA9029BFB59CD7BAFC958B92 (void);
extern void JsonSerializerInternalReader_ReadForType_m87BCCCE124949AE5F50DFB4C84E879A0AC5A7FE1 (void);
extern void JsonSerializerInternalReader_CreateNewObject_m8BAE755FCE8FA5F5CBFC25FB178C2AA695000C2F (void);
extern void JsonSerializerInternalReader_PopulateObject_mBA642D60FF031FBCCF0ABCA645E09C8785C27C64 (void);
extern void JsonSerializerInternalReader_ShouldDeserialize_mF0814A1834B9E1382526C6DB60A26249BC0B1643 (void);
extern void JsonSerializerInternalReader_CheckPropertyName_m7CEDAA79DA7EABEE00417013C604C0B448EEA78A (void);
extern void JsonSerializerInternalReader_SetExtensionData_m41465A000736E2C32FDCCB0CE22307C3BCEFF665 (void);
extern void JsonSerializerInternalReader_ReadExtensionDataValue_m4A28C4B9FEB5F192E8223C9FEFEC1A93B2D31642 (void);
extern void JsonSerializerInternalReader_EndProcessProperty_m218BC0692C72079B04C18BC5C8F27812555AA2B4 (void);
extern void JsonSerializerInternalReader_SetPropertyPresence_m34D1BF723E6777C77064C19DF3E62C60BA55379B (void);
extern void JsonSerializerInternalReader_HandleError_m40720759FE1F8D2FE07B25EE5A8102F06A7F9F98 (void);
extern void CreatorPropertyContext__ctor_m508868091CBCFC37EFE4466D87BAE91804BA5C98 (void);
extern void U3CU3Ec__DisplayClass36_0__ctor_mDA833D4515BBB0D9B644DE9A0004439E7338B255 (void);
extern void U3CU3Ec__DisplayClass36_0_U3CCreateObjectUsingCreatorWithParametersU3Eb__1_m1068B34FFC6262FB39DADDB3C0D794D29EC43AA3 (void);
extern void U3CU3Ec__cctor_mF0443A4AB1C0BD9516159D36A6365C6C86D1F370 (void);
extern void U3CU3Ec__ctor_m3CA04813FF9823D1FBC71845B99D1AB42876D76D (void);
extern void U3CU3Ec_U3CCreateObjectUsingCreatorWithParametersU3Eb__36_0_m08A820C0AC3674F39026C4FD32CED80930BE9AD1 (void);
extern void U3CU3Ec_U3CCreateObjectUsingCreatorWithParametersU3Eb__36_2_m2614EB2448E724E298BE4BEB7ACF9F15C5A5F978 (void);
extern void U3CU3Ec_U3CPopulateObjectU3Eb__41_0_m699194014C9012B0D8FBB64D393FF28DBAD62A06 (void);
extern void U3CU3Ec_U3CPopulateObjectU3Eb__41_1_m033B99CD52BE8B896D9C18ACA6CB286BEDECA15E (void);
extern void JsonSerializerInternalWriter__ctor_mCC08C997ED86BF784BD7F7D1AA0E3A418AD682B9 (void);
extern void JsonSerializerInternalWriter_Serialize_m6F3A4DB9C4E298D5014F4E918B44EC1C3218F0D3 (void);
extern void JsonSerializerInternalWriter_GetInternalSerializer_m47EEAD97DB52CFFA70BFD1A648E60AE18B4E1651 (void);
extern void JsonSerializerInternalWriter_GetContractSafe_mD9ACF3FC60B6B54EF02783392B3AE86D8351B865 (void);
extern void JsonSerializerInternalWriter_SerializePrimitive_mA43DB99838BD6BDC632A4BE7416BA0FD978C8D30 (void);
extern void JsonSerializerInternalWriter_SerializeValue_m9E2AE84CF7CBD3978BE0FC2E30EE576CEBD4449D (void);
extern void JsonSerializerInternalWriter_ResolveIsReference_m49B7EFEF5C968810E1188B41522582FFCB6763F6 (void);
extern void JsonSerializerInternalWriter_ShouldWriteReference_mE7FEFC8DF15EA92D0DAFB8715B4E3AE6C189F840 (void);
extern void JsonSerializerInternalWriter_ShouldWriteProperty_m61E04ADC18C85DCBAA123C8D0CC618B8027D3AE3 (void);
extern void JsonSerializerInternalWriter_CheckForCircularReference_m6C095C45DF61AFF70BB9206D2806B0FD4CF44E28 (void);
extern void JsonSerializerInternalWriter_WriteReference_mB21F3155E83BB4303F761E460AC27E2C37B6B687 (void);
extern void JsonSerializerInternalWriter_GetReference_mE58B35D148E7362435786EA0DBB17F145A335224 (void);
extern void JsonSerializerInternalWriter_TryConvertToString_m2EFE6A8F0D4B190F1179C4B7CEB9CB5186B5B528 (void);
extern void JsonSerializerInternalWriter_SerializeString_m4A3E309C637A56736C673EA169EE5069B66AF58D (void);
extern void JsonSerializerInternalWriter_OnSerializing_m0634AC6F2D85CA48741709EA27E35488729DC3E2 (void);
extern void JsonSerializerInternalWriter_OnSerialized_m6671114B713D4ABC359EE5E6B4F06A8D0AA0EB1F (void);
extern void JsonSerializerInternalWriter_SerializeObject_m8E8111A60CB066BFE3930238B7AA82B62891DCB1 (void);
extern void JsonSerializerInternalWriter_CalculatePropertyValues_mAA869D0C5FC1FAAC62B576E766318EE1697E0C8C (void);
extern void JsonSerializerInternalWriter_WriteObjectStart_mE51BBC51224288FA1A775C9B7C4021AB37AD3334 (void);
extern void JsonSerializerInternalWriter_WriteReferenceIdProperty_mF2D2FFE03B44AD70222600F765A095BEE220EE27 (void);
extern void JsonSerializerInternalWriter_WriteTypeProperty_m98259688AA2CF87C4153675AF3A660432C9DC1EF (void);
extern void JsonSerializerInternalWriter_HasFlag_m775FB1DAEBF77775EABDB18E272BE3C5DD9171DA (void);
extern void JsonSerializerInternalWriter_HasFlag_m3217D958D6672A6D4B6D8DE06B93B5F3D5DD667D (void);
extern void JsonSerializerInternalWriter_HasFlag_m041C330EDA9893F3687DE90EB08228701DB62B89 (void);
extern void JsonSerializerInternalWriter_SerializeConvertable_mF17E8B9C9509F9BCA030F3B6C7A60AEC550E7A60 (void);
extern void JsonSerializerInternalWriter_SerializeList_mAFEA6EE2951E8D90F138366992B15F8B43E0798D (void);
extern void JsonSerializerInternalWriter_SerializeMultidimensionalArray_m44EF51EE789A05AD562A13262DE4FDC37853EFBF (void);
extern void JsonSerializerInternalWriter_SerializeMultidimensionalArray_m10A5E084F8C0A33F9C916D3F1F8CB1049B27362C (void);
extern void JsonSerializerInternalWriter_WriteStartArray_mA91BC3BB7C3538EB0FE45169C34985A6B3B83262 (void);
extern void JsonSerializerInternalWriter_SerializeISerializable_m32D26085FD3582BF09B5FC9807EA317D979108DF (void);
extern void JsonSerializerInternalWriter_ShouldWriteType_mCE78A56201F8DDFCA5FF9822C26FDAB691938956 (void);
extern void JsonSerializerInternalWriter_SerializeDictionary_mA25958F221EA9C67CA789E1DE52673713420BB7F (void);
extern void JsonSerializerInternalWriter_GetPropertyName_mBF182832677C67FD454732017E2D8CEB4380CE9D (void);
extern void JsonSerializerInternalWriter_HandleError_m5F64C5A4BD5021B0B893BC28B5FEE82EA78BF138 (void);
extern void JsonSerializerInternalWriter_ShouldSerialize_m1D82DE84ACBDFF5FCE100D5D44EA698189CDB404 (void);
extern void JsonSerializerInternalWriter_IsSpecified_m5D2F39F5941CF28FEBB613B579234D3145054E09 (void);
extern void JsonSerializerProxy_add_Error_m3F4E48B96166D2F13B12DB257AADC16C99D73DBD (void);
extern void JsonSerializerProxy_remove_Error_mFBD83B46CB6CD0D0CC237AD133EE28019ADD1F7E (void);
extern void JsonSerializerProxy_set_ReferenceResolver_m7BE98589D3834B1C212F67F1F7FD97FA9BCC7EC6 (void);
extern void JsonSerializerProxy_get_TraceWriter_m3749F1C8343AA0301CCAFF71C4A27044C218F76E (void);
extern void JsonSerializerProxy_set_TraceWriter_m367D6D5838B06A9EAAABAC64EA9BA3040127BD06 (void);
extern void JsonSerializerProxy_set_EqualityComparer_mCD50460A7C5CDFC4AB564925AF42F323595E09A7 (void);
extern void JsonSerializerProxy_get_Converters_m6D74100FB7C7F60AF4768B000B506DF381ACE052 (void);
extern void JsonSerializerProxy_set_DefaultValueHandling_mBF3BEE08E1EC35630E20240720AB9E0C8421D6A1 (void);
extern void JsonSerializerProxy_get_ContractResolver_m75EF9DF4EF8531A9D6711AF6B061F2F61D3B29CC (void);
extern void JsonSerializerProxy_set_ContractResolver_mE6390F594B814BCBC62B4FC1D85B5B4B6D744054 (void);
extern void JsonSerializerProxy_set_MissingMemberHandling_mC8E3C59E089A30B8137E25E7426910447216CC61 (void);
extern void JsonSerializerProxy_set_NullValueHandling_m9E87A8A64D213122ECDBCD0592D88E341F93CE25 (void);
extern void JsonSerializerProxy_get_ObjectCreationHandling_m4325F872BB8C95F075DE5ACCBE4726AABEABB801 (void);
extern void JsonSerializerProxy_set_ObjectCreationHandling_mEDAD7D338FA78F47A72F7E8E40C970BB8712B1AB (void);
extern void JsonSerializerProxy_set_ReferenceLoopHandling_m6DE348B9B84338195CAA503E021EAFBCAC214435 (void);
extern void JsonSerializerProxy_set_PreserveReferencesHandling_m9CFADF4A088913A0D3AD21757CFFAA9FAAB9665C (void);
extern void JsonSerializerProxy_set_TypeNameHandling_m594B13276AE7584AA0AE35F79A6F3BF799D7A6BF (void);
extern void JsonSerializerProxy_get_MetadataPropertyHandling_m540B6755E7A52B3EB0D2E75D4F56D1E42EF65295 (void);
extern void JsonSerializerProxy_set_MetadataPropertyHandling_m9CE01BB1081889EE2FDD2D710E4E166B7FD72108 (void);
extern void JsonSerializerProxy_set_TypeNameAssemblyFormat_m316AD7675FA040C93EAD3C367409E7365C83D354 (void);
extern void JsonSerializerProxy_set_ConstructorHandling_m323B96C8B1B9AC3D47520852AC0189474B765E11 (void);
extern void JsonSerializerProxy_set_Binder_m1E8DF36271DE76EB6A1BCD774473CEE8666264AE (void);
extern void JsonSerializerProxy_get_Context_m83DBED6E809C2A988B30F42093D8DE80FC751048 (void);
extern void JsonSerializerProxy_set_Context_m96AC87936F00DD20B2177D5AC1352E987DEEF064 (void);
extern void JsonSerializerProxy_get_Formatting_m1AAD1BDDF840E183F9A5DB73A32EC0A8292D138C (void);
extern void JsonSerializerProxy_get_CheckAdditionalContent_m3A11037170B82D95A03FB4923102FB4A60CD356A (void);
extern void JsonSerializerProxy_set_CheckAdditionalContent_m2899BB28670290F2BD3A4A154FC8E516A7D6FDDB (void);
extern void JsonSerializerProxy_GetInternalSerializer_m66B0CF30F5F89DE663C295459E40B7C7632CB2D3 (void);
extern void JsonSerializerProxy__ctor_m7E43FF07BE825F75C0D8986908EE9207E5F57D1F (void);
extern void JsonSerializerProxy__ctor_m6794477BA6057E98FEEB6421F15189B9E166143C (void);
extern void JsonSerializerProxy_DeserializeInternal_mFA054588EF9D9F945A16C63675217A54EF95632B (void);
extern void JsonSerializerProxy_PopulateInternal_mE51933CA473A9460658DD0945B396DD64B3B7426 (void);
extern void JsonSerializerProxy_SerializeInternal_m2D63B96FB8C38360924506227137A8468191C949 (void);
extern void JsonStringContract__ctor_m54A42C4ACC79372B1BA8543146B32DD8B8CC4256 (void);
extern void JsonTypeReflector_GetDataContractAttribute_m6C5640D4E573853A4BD2B77D1CE353115322924E (void);
extern void JsonTypeReflector_GetDataMemberAttribute_m51F50871DCAA5A513B7E12A0AF003FA0ABC2237E (void);
extern void JsonTypeReflector_GetObjectMemberSerialization_m5A6864B17915ED80E8CB43E00B9C751DF5D7CA56 (void);
extern void JsonTypeReflector_GetJsonConverter_mBB62F7EE987E98076594D2E89C4BEDDC0B8D72D8 (void);
extern void JsonTypeReflector_CreateJsonConverterInstance_m202D426DBCF696743A3A607AA9AD3F0E6DED1682 (void);
extern void JsonTypeReflector_GetJsonConverterCreator_m8BB7A51770C54072D007FA6A5271EC373DAE20A6 (void);
extern void JsonTypeReflector_GetTypeConverter_m78582421D09C95414EDA2CEB68E0663D6B699754 (void);
extern void JsonTypeReflector_GetAssociatedMetadataType_m681AA98B280B9EFB723BFD061AEEE9BA94EC53FD (void);
extern void JsonTypeReflector_GetAssociateMetadataTypeFromAttribute_m0559AA4119BD49EC696BF9AAAD5BB489A64DDC6B (void);
extern void JsonTypeReflector_get_FullyTrusted_mD8D182DE0EB2E23631249206AF8B24299BBE7AC7 (void);
extern void JsonTypeReflector_get_ReflectionDelegateFactory_m924B16B5A02C8F4815333C06D1BD731450980A51 (void);
extern void JsonTypeReflector__cctor_mEB78088CCDD3186722FEAF8EAABD57BD663DB85A (void);
extern void U3CU3Ec__DisplayClass18_0__ctor_mB5A4B81394AFECBBCDC3326D72AEE229812BE749 (void);
extern void U3CU3Ec__DisplayClass18_0_U3CGetJsonConverterCreatorU3Eb__0_m2DE2553D9A380283E2D1A8E4403240A469377CF6 (void);
extern void U3CU3Ec__cctor_m6DDB38A10A0DD796CE38ACBDFBCFB2C92E09432E (void);
extern void U3CU3Ec__ctor_m65150CDB4BD0AC8023743FFF0C7784866899F2C1 (void);
extern void U3CU3Ec_U3CGetJsonConverterCreatorU3Eb__18_1_mB31A165FB8C0C5B9E011DCAB3658D2F4FF509B28 (void);
extern void ReflectionValueProvider__ctor_mE88335461A4A910D2554A3F309D9B335EAEE8F77 (void);
extern void ReflectionValueProvider_SetValue_m156DA2DB4C7691E1BB4040AE4E60264D9ECCF633 (void);
extern void ReflectionValueProvider_GetValue_m077603E0A48B537460B96C40510B76835DFC8D08 (void);
extern void OnErrorAttribute__ctor_m7D93D3E8F3DE8B244298CD08A90B0621EDB74858 (void);
extern void JPropertyDescriptor__ctor_m877F099EA64D4B8F95EE769BC8429233B0171E29 (void);
extern void JPropertyDescriptor_CastInstance_mA25D78E03BE3473DE077EBF34C242EA8DD3A372B (void);
extern void JPropertyDescriptor_GetValue_mA9224108CF26F679B4D4C2B4AE7720BB6EFFFFBA (void);
extern void JPropertyDescriptor_SetValue_mB9A460515C03CF3A38F658299417CA364D8C5D82 (void);
extern void JPropertyDescriptor_ShouldSerializeValue_m50AC4E9A73780535BF1EC4871EDE381AE00E69F9 (void);
extern void JPropertyDescriptor_get_ComponentType_m9BF70CB3B4F5A6DCC7F6B712C05357C133C4C9E4 (void);
extern void JPropertyDescriptor_get_IsReadOnly_mED7B29AFABE05D9A6C86B732CAEB7C5ADAB8BDAF (void);
extern void JPropertyDescriptor_get_PropertyType_m2107646E0E2C91024B47171DEADC63A31BD42C4A (void);
extern void JPropertyDescriptor_get_NameHashCode_m3F0F484B07E5835C4F58C23ED6944038FEE13739 (void);
extern void JPropertyKeyedCollection__ctor_m21F15E479CA1D7A1BDDAD4244D6C96DDE4E45C40 (void);
extern void JPropertyKeyedCollection_AddKey_mFCC5FB5F64EDB1EDD325655982F437C8FB2C3CB4 (void);
extern void JPropertyKeyedCollection_ClearItems_mD3C9AFFFDA4FA497CFA15A3EB6EC21D99BC66D76 (void);
extern void JPropertyKeyedCollection_Contains_mAE6A5F9D709FC4B6498F2EFD6944ACD840CE7BF8 (void);
extern void JPropertyKeyedCollection_EnsureDictionary_mFA61760F658076357677A7A4E65E30DBDD50A50D (void);
extern void JPropertyKeyedCollection_GetKeyForItem_mAEFC5E34BB0FF88D35E35CBEA81A72BF512A1A96 (void);
extern void JPropertyKeyedCollection_InsertItem_mF8B5F2D756E71DB59588C486C797D889AC43CCC1 (void);
extern void JPropertyKeyedCollection_RemoveItem_mF1EE424F9570BA59CFD9E97828028EDB741AB5F9 (void);
extern void JPropertyKeyedCollection_RemoveKey_m56F96C29710D6C939FBDBFB73F216B853B3C0F47 (void);
extern void JPropertyKeyedCollection_SetItem_m8446E595AD6EBB0248482B8C90E6C346568B39F8 (void);
extern void JPropertyKeyedCollection_TryGetValue_m38A92FE81F48DC5B2CA8A16CC1277E1E66389438 (void);
extern void JPropertyKeyedCollection_get_Keys_mA42C75415669CEC080DB22E19A95927588519287 (void);
extern void JPropertyKeyedCollection_IndexOfReference_mCC509E015D29C17BEAED4786DB89CD5CADE3D108 (void);
extern void JPropertyKeyedCollection_Compare_m6A45C124C716C21D17D67FA5CDC55B1010CBA50F (void);
extern void JPropertyKeyedCollection__cctor_mDA03CB99DC953D8DF9921B382FB831978EA14C40 (void);
extern void JsonLoadSettings_get_CommentHandling_mA60E15D35DA81D315E59DD273F7F344F5350AC59 (void);
extern void JsonLoadSettings_get_LineInfoHandling_mA36890FFDFFDE426CE4F4F601C94B05203926A7F (void);
extern void JsonLoadSettings__ctor_m1333B67DC91D3C14036DB1ABA4B4992CFC9EE255 (void);
extern void JsonMergeSettings__ctor_mFCD8C16235038091E5739A7648229A6F8EE6414E (void);
extern void JRaw__ctor_m667F17DD78223DF6153EFE3CE4B5CCC79EE4D98C (void);
extern void JRaw__ctor_m2EFCB9D0BB51C60EA4FC7521946B263CE8E7B973 (void);
extern void JRaw_Create_m6A2127ACCE39A194B22542FB214CA33C07617533 (void);
extern void JRaw_CloneToken_m71D9416F2DD252F2853BB199D8C4D480CD81DC75 (void);
extern void JTokenEqualityComparer_Equals_m630F3826E1E3251D8EB977C2E4FF53AF855B8E01 (void);
extern void JTokenEqualityComparer_GetHashCode_m7E97DA3EAEB81CC1B17F84AA260EA47AF57D2489 (void);
extern void JTokenEqualityComparer__ctor_m51EBD76B006566B513353A8A68E66A53335B5665 (void);
extern void U3CU3Ec__cctor_mF161356B631C1F18093C6D57194F43C64974597F (void);
extern void U3CU3Ec__ctor_m5CF9DE6B039C56A379A5B12F2BAB587A8954E961 (void);
extern void JConstructor_get_ChildrenTokens_m5013969747638065409119179B69978D83CA1C89 (void);
extern void JConstructor_IndexOfItem_mEFD447309B96478FC5D120D4B6D03D49B640EE80 (void);
extern void JConstructor_get_Name_mD849F98788D5407A56C0D91C33DBE7F5DB0C7333 (void);
extern void JConstructor_get_Type_m6F596B8CD30BB7F074B6CB8F9DEA242F9765D524 (void);
extern void JConstructor__ctor_mF57946172C87B86825D8F225471A6DC1D9ADC249 (void);
extern void JConstructor__ctor_m9E7552EB60C4E04D1C3ECAD56C9ACC0051F7DAC4 (void);
extern void JConstructor__ctor_mC89CA8676C5398B955F9661409EAD842B1C3EFC3 (void);
extern void JConstructor_DeepEquals_mCCA8269B462719D31C43F58539E9CF06FA2CFED3 (void);
extern void JConstructor_CloneToken_mDEA6432C68F77BC1795F36E05192ADEA97C07095 (void);
extern void JConstructor_WriteTo_mC844D568EC8CD00E7112B3C7A0CC0958C92FC0FE (void);
extern void JConstructor_GetDeepHashCode_m73B498B4C5810DF5C685347AA2D5D49CCF9291DE (void);
extern void JConstructor_Load_mAEAC566E973BE90651D1C19B0F91A2FF7BC4120A (void);
extern void JContainer__ctor_m7095E8571DB53EB2ACFAD0C7EE9D8585F2D47B90 (void);
extern void JContainer__ctor_m27AD96BA1969EAC87FBF0848CF5A12A3C598D80B (void);
extern void JContainer_CheckReentrancy_mCBD43EDCBB0AF1D5716AC2860E1F52F995D23939 (void);
extern void JContainer_OnListChanged_mED4E934E1C4A7F4E2DE4A2E185F854422AF9F100 (void);
extern void JContainer_get_HasValues_m1B969A5AA61938A75C27A2B1F685FEE352CD5F2F (void);
extern void JContainer_ContentsEqual_m75BD8D41828E841828CA3ACE8DCA0F66C38EC39D (void);
extern void JContainer_get_First_mE5F65F00B32F0967AF47CEF9974B9161E4D43B03 (void);
extern void JContainer_get_Last_m746D4108C662DA85127F29E40AF4BC3F7B6064B4 (void);
extern void JContainer_Children_m145BCA2F4B5EDE9375D2EA14DEDB1648EE8D064A (void);
extern void JContainer_IsMultiContent_m25FF25009E27CECACA18D058969E5BDF3998988F (void);
extern void JContainer_EnsureParentToken_m968D556E18CAFC190674277A2A863FD596A00C31 (void);
extern void JContainer_InsertItem_m300D3FF2E3AF3F5415B72BD8BA33195172EFA91E (void);
extern void JContainer_RemoveItemAt_mE4CF99214A3EF302AD4C06A74366AC4C40F74E18 (void);
extern void JContainer_RemoveItem_m9F0EE2596BBA83FD7D26FE2DF394AE17A41DD50C (void);
extern void JContainer_GetItem_mA809EB1D838A8D11948B5107BAD94CAF335B0B65 (void);
extern void JContainer_SetItem_mF7CA2F4D24EE1C3BD8E04B06E8137BF43E24CE61 (void);
extern void JContainer_ClearItems_m8286C0312ED7545676783B072C0D89FEED420BC6 (void);
extern void JContainer_ReplaceItem_m752AFB8D6378D9CA238F5FE25ED32A21B8A05634 (void);
extern void JContainer_ContainsItem_m010B7EAE51D386F1B255B374D34CED6A972C3858 (void);
extern void JContainer_CopyItemsTo_m0ADDADA788F24C17578ED04A19EE25641F26A8DB (void);
extern void JContainer_IsTokenUnchanged_m7CBB8C448C66D1D0F7A36575C9430369101B046E (void);
extern void JContainer_ValidateToken_mDC10E814447BEEA4FA6058F8CA78A7ECE4CE73B4 (void);
extern void JContainer_Add_mA8F552B852765618919FCB3FF97C7C9C3E732160 (void);
extern void JContainer_AddAndSkipParentCheck_mC51F4043221D2D090369E4070CA3CD4F7B697C17 (void);
extern void JContainer_AddInternal_mF3555187E89A249BD501DBA72FEFD0DCCF1DF35B (void);
extern void JContainer_CreateFromContent_m92C69BECEB50E8249BB7D083F09541FC11804428 (void);
extern void JContainer_RemoveAll_mAEF113701C58DE131358EFBF16F2B036335B6CFE (void);
extern void JContainer_ReadTokenFrom_m866C27A2735BE11C2369C02407B191EC29C3425B (void);
extern void JContainer_ReadContentFrom_m7824C54775FAB81872033ABBB131B1A66CDB690B (void);
extern void JContainer_ContentsHashCode_m47AEE7AEE48031D54C5E1E188D2A5850386AF0CE (void);
extern void JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_IndexOf_m10FBFB00CF547E0D610E16B46F20993645D97E3D (void);
extern void JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_Insert_m7950D9899A186852CA26BEC6F1743E03842485B1 (void);
extern void JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_RemoveAt_m218130D3F2A86B5035D5B837FE2A59AB3B2F434A (void);
extern void JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_get_Item_m1BAFCBF2AF27E73CAC89E6F3C773F52CAEAD7A91 (void);
extern void JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_set_Item_m4CDB06AD85C6236533DF4AAA7E80BA082AB72FD8 (void);
extern void JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Add_m93BB5918A5163706AC0C6E183874EC027C2F3C5E (void);
extern void JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Clear_m1A3256AECAF0F3BEBC55CF445B050D24B742E48E (void);
extern void JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Contains_m2DD4EC5016A9A30A98D9A1E6149CE7DFE7C0D797 (void);
extern void JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_CopyTo_m465B4E9A4452B4D68675A852056FE302CE4AED35 (void);
extern void JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_get_IsReadOnly_m1B41063C227198B380F0B057BD904D8D42D1836A (void);
extern void JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Remove_m8B8C71F6B1D4D204EBD27CBF0024048635190394 (void);
extern void JContainer_EnsureValue_m3DDD4F5502EF23102BB359D0A3FB845B2BC73297 (void);
extern void JContainer_System_Collections_IList_Add_m105A4F8F00D9DE284181C13CD2D3ACCBBDC36A3D (void);
extern void JContainer_System_Collections_IList_Clear_m982C4381BF5A0742BEEBFE12BFFB9F276B05C15D (void);
extern void JContainer_System_Collections_IList_Contains_m5EDE03B695D67301384EBCD1B5B3F07F2807E809 (void);
extern void JContainer_System_Collections_IList_IndexOf_m35F33FE6454F28ECF9D27ABB7CE7A4F664D50095 (void);
extern void JContainer_System_Collections_IList_Insert_m39F37C385273DA1E61CDC92CC3A3B6E98563A1D4 (void);
extern void JContainer_System_Collections_IList_get_IsFixedSize_m5C4CD0A26CEC6E6491758B54EDB6256F9B512DBA (void);
extern void JContainer_System_Collections_IList_get_IsReadOnly_m0C6C01289B98BD1037128AAB3ADD528EF178932D (void);
extern void JContainer_System_Collections_IList_Remove_m9D0783914B23B9D429E0EFA41C0CF2388B257379 (void);
extern void JContainer_System_Collections_IList_RemoveAt_mE5C813681E23B5F92EBE43DA9F72BD4D2322F9CE (void);
extern void JContainer_System_Collections_IList_get_Item_m256622BBEB94CB9768D73DAAFD318D35DDEA037C (void);
extern void JContainer_System_Collections_IList_set_Item_mE37CA02290587C99F3D0905864EFBDADA1AEA64A (void);
extern void JContainer_System_Collections_ICollection_CopyTo_m17427E62A150F0790B044769A0253FA617108C3B (void);
extern void JContainer_get_Count_m04A0A7677BE3AA56AA02859848A96590BCE17D86 (void);
extern void JContainer_System_Collections_ICollection_get_IsSynchronized_mBCEBD801FDD1D6147CABD86AD904BCBD12F483FE (void);
extern void JContainer_System_Collections_ICollection_get_SyncRoot_mBE4AEC28C06A208EC5A75BE571427D5BE1CD3B42 (void);
extern void JObject_get_ChildrenTokens_m9F4744FB76B5FFF9F147CDE9032122E78E2A7D5E (void);
extern void JObject__ctor_mF7F801B6729F7309319FF97690BCC68C7CA81CE4 (void);
extern void JObject__ctor_m3BC9D30AA26660214591EF08F691D1B740D6615D (void);
extern void JObject_DeepEquals_m4F351185021634247D419CE04F8ACBA0D1C22AE0 (void);
extern void JObject_IndexOfItem_m3377AF169E182A25D2AAAD083E56E957110EFDF2 (void);
extern void JObject_InsertItem_mDC98FFBC6659DA79610C169C0EE4A1513645DA5F (void);
extern void JObject_ValidateToken_m26B430FD70611870CF545EBFC63B4AC2D55F04A2 (void);
extern void JObject_InternalPropertyChanged_m294A833CA13BEAFE53296CEA45985A7B33A5A5E1 (void);
extern void JObject_InternalPropertyChanging_mE249501F10D07DC0865E0D7016E6078194F45DFE (void);
extern void JObject_CloneToken_m227962AB50C65A0D7C8A4DB8A4E306E4D8CC88BE (void);
extern void JObject_get_Type_mD77310BE37DC7B07F455AE6E68D47D75B1510B76 (void);
extern void JObject_Property_mBC900C047166F06EA67767C1549A829071276412 (void);
extern void JObject_get_Item_m889A29E8FCEA531AF829407F208DD05F0108C50B (void);
extern void JObject_set_Item_m38ACF4BABEDE80CF0138DA900AFFC5C4CC3E9221 (void);
extern void JObject_Load_m44DE042F4F4752D15D54259823A74B609650E9EF (void);
extern void JObject_Load_mBFB13BABDB5082B495040AF48A8C691516D2D2E7 (void);
extern void JObject_Parse_mBC884661DD2708BA92BCE45E8C104924542A2D43 (void);
extern void JObject_Parse_mBF13328E82E184AE1D3E1AA56ED3A2879618719B (void);
extern void JObject_FromObject_m19DD8C1DB59132420FA3DE3E6EE27BDF2A4292AD (void);
extern void JObject_FromObject_mC479AB8737444180643B3E9905F02F11849C21D9 (void);
extern void JObject_WriteTo_m39BCD00E12DCDFCB9446B22961E99840B04CC66E (void);
extern void JObject_GetValue_m4F5588B32404E992D225DDF4DBB7B5B033A254A2 (void);
extern void JObject_GetValue_m163EB7BE77699B79CB53035D84874159EBBBFE6F (void);
extern void JObject_Add_m2F15295DA90DA00BDD3C94313C0F3DC4DE3C7A39 (void);
extern void JObject_System_Collections_Generic_IDictionaryU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3E_ContainsKey_m0DBFD1C4A4878DC7511910F938B900851D0B1D9B (void);
extern void JObject_System_Collections_Generic_IDictionaryU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3E_get_Keys_mE713556A297D5397CB3AB964C8EEEA27BCD7C48F (void);
extern void JObject_Remove_m42D8594E735983D401F734BFECA380FE92459AD1 (void);
extern void JObject_TryGetValue_m835B2EDE5A508D8ED745283EA5BE31EC4BD609BC (void);
extern void JObject_System_Collections_Generic_IDictionaryU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3E_get_Values_m02173559514621DFFF68F979DB587C0C315F6C62 (void);
extern void JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Add_m234EFFFE0D80C677CC22DC59DCC7CB7A60EF595C (void);
extern void JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Clear_m68B2613EDF8D09DE7975E70A5FFFA3E23AACC921 (void);
extern void JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Contains_m40742406A96AB115E83354BD431AA183CC6BE9F7 (void);
extern void JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_CopyTo_m1C9BFEE21DC2A559562983B10928C444962883FE (void);
extern void JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_get_IsReadOnly_m6FCD2977B4B0BDB3FAE50871C3CCE77AB5C60A13 (void);
extern void JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Remove_m6C2CD89F26FE0B46FE397C3506171F1632645A2D (void);
extern void JObject_GetDeepHashCode_m0835227B13EA6E01A2B53A5FBF62AC2C769CC8C4 (void);
extern void JObject_GetEnumerator_mDB9AA9D5339E5EFD3D9D968B778E651D2CA693BB (void);
extern void JObject_OnPropertyChanged_mDD323E9A4F16D325EE142FB8E9E4EA6BA765A675 (void);
extern void JObject_OnPropertyChanging_mD5368513750A4167D22EA7DFCD4657E4E4B6D10E (void);
extern void JObject_System_ComponentModel_ICustomTypeDescriptor_GetProperties_mF0E06F1B3A97C4D9CEDCEEAEC28EAF3B5211DEAF (void);
extern void JObject_System_ComponentModel_ICustomTypeDescriptor_GetProperties_mDE8C42C97C9596CA7A68065D4E7E122A4D7C8789 (void);
extern void JObject_System_ComponentModel_ICustomTypeDescriptor_GetAttributes_m355B94DF88CE1D368F050AE12B66F92DD588D62F (void);
extern void JObject_System_ComponentModel_ICustomTypeDescriptor_GetConverter_m9D2FAE9FB6FA3AA8D3000B338A1532F23770C05A (void);
extern void JObject_System_ComponentModel_ICustomTypeDescriptor_GetPropertyOwner_m93FD4C2F8276E17A732F74F7544A9FF3BDF449D8 (void);
extern void U3CU3Ec__cctor_mDB689A53DE1F3254E90000CC4F9218895446B99A (void);
extern void U3CU3Ec__ctor_m976CC1EED0623B90BD1AA7772CC23C5BF64E5973 (void);
extern void U3CGetEnumeratorU3Ed__58__ctor_m0FF703F90ABA785137D83256807CEC7444514A9E (void);
extern void U3CGetEnumeratorU3Ed__58_System_IDisposable_Dispose_mB17F61830A91AAFC34D8A7C63F979DB9926DA1F5 (void);
extern void U3CGetEnumeratorU3Ed__58_MoveNext_m2C65338486129446A6FBA52B345BE8EAACE08D94 (void);
extern void U3CGetEnumeratorU3Ed__58_U3CU3Em__Finally1_m557DCFEACE4EE0F40B369360BA060D7DF8FAE7BE (void);
extern void U3CGetEnumeratorU3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_get_Current_m27AEFB01D6A203EF0144E08E527FFC2869B32760 (void);
extern void U3CGetEnumeratorU3Ed__58_System_Collections_IEnumerator_Reset_m3F202C261171C2F464E566F2AC01E03BCE7B6A35 (void);
extern void U3CGetEnumeratorU3Ed__58_System_Collections_IEnumerator_get_Current_m7B9C80AC6295DA597B9A4E4A6F41DCD37BFD77D6 (void);
extern void JArray_get_ChildrenTokens_m98E4E304DF0E101C7928436E633BBBE9FE36CDE8 (void);
extern void JArray_get_Type_m95FA623B41ED0E917C0FA74D54F7015E4DE2CCD1 (void);
extern void JArray__ctor_m022A5DB24C674904082FD028B96F7AF93A87CF10 (void);
extern void JArray__ctor_m664A0B87E47583C9FD3AB20E1CAFF4FD358A172E (void);
extern void JArray__ctor_m3D45B50C56FFFD2E00936E31E99DBC0BAEC0C9FE (void);
extern void JArray_DeepEquals_mE07350637BF6037E0F968EFEBACEE93C8C88FD90 (void);
extern void JArray_CloneToken_mA92F71038DC716638EE97CC77BCDEA6358FED207 (void);
extern void JArray_Load_mE90CEEC0A22EBC876F7D7E239A0E096E4691C807 (void);
extern void JArray_Load_m38C0557B81449ED9FDE01552F39B9F35A2A7B2CA (void);
extern void JArray_WriteTo_m2D922281767C17A5AB3C171B743D05709382445F (void);
extern void JArray_get_Item_m6353E95068B4107D911305FC05DAC9B2EFC78463 (void);
extern void JArray_set_Item_m440D5A05F46329315816521EA5096C21FF891EE0 (void);
extern void JArray_IndexOfItem_mE8A7537E261B0A2E79D436CF11A07A356733BB0D (void);
extern void JArray_IndexOf_mD1E8177990592ED2B55E8B3DA063D8790D173953 (void);
extern void JArray_Insert_mFBCF47E3C8CE609DFA236DEF0069787C0E42EE08 (void);
extern void JArray_RemoveAt_m69FAB8DE7DC5E8391ECA03419052BC8F658C1314 (void);
extern void JArray_GetEnumerator_m3A0A8116C13003EA3883AB9D5F2A9E8069192A4F (void);
extern void JArray_Add_m6E648CB9C562A6CC16DC550DA611456AB0149330 (void);
extern void JArray_Clear_m9953833C9F980B90C1155AF4634E1A9D475E29AB (void);
extern void JArray_Contains_mFBB6AFBC7B92E32B064695EEE5160EFFA966EA66 (void);
extern void JArray_CopyTo_m88034216EE65F56C0CF0456429C04BC8B6B203A1 (void);
extern void JArray_get_IsReadOnly_m44C022E98720A9553B922A874F91E6A494E9F953 (void);
extern void JArray_Remove_m9509FE2011FFAD9CAC0B5DF4836624B8EED876E4 (void);
extern void JArray_GetDeepHashCode_m8B5110A65739039E1C57A3AA0FA960A2BB59FBC2 (void);
extern void JTokenReader_get_CurrentToken_m9382FFC609615BC6423B5867BD2FB843646B5BBD (void);
extern void JTokenReader__ctor_m0C5BEC5718922E8D908126C43AEE4306550626CE (void);
extern void JTokenReader_Read_m49AE9CDFD4B12B166F183FA676C5876896833289 (void);
extern void JTokenReader_ReadOver_mABE9EC2F3C5E1DB159B5288A210BAC08C9318C42 (void);
extern void JTokenReader_ReadToEnd_m4B5F918C3E8734A351B12B5830B98F22E4908599 (void);
extern void JTokenReader_GetEndToken_m7F8E62FB06000194E167434239C7BC726B5F12C9 (void);
extern void JTokenReader_ReadInto_m798414BB9A9CCA02A6C864C40F3833A1186FF404 (void);
extern void JTokenReader_SetEnd_m31356A205C3928A87D141D2E4BD9DD8879F200F8 (void);
extern void JTokenReader_SetToken_mC726390D1AAC8B87E72AAB6981B546ED46AA84B8 (void);
extern void JTokenReader_SafeToString_m28E950813248D29871D05DC533561FFCB7F163F2 (void);
extern void JTokenReader_Newtonsoft_Json_IJsonLineInfo_HasLineInfo_m329AF0F990D67D75FEBDF4EE50D3B0B94EE33DD1 (void);
extern void JTokenReader_Newtonsoft_Json_IJsonLineInfo_get_LineNumber_mC0F0DEB038C1E343F87D0946AAC4C1CE0B349BEE (void);
extern void JTokenReader_Newtonsoft_Json_IJsonLineInfo_get_LinePosition_m22D09371232A064EC3010C7528FD32CCD3CF63B6 (void);
extern void JTokenReader_get_Path_mD84CD80F266A67DA4E4E2D881BD73FC629A775C7 (void);
extern void JTokenWriter_get_Token_m5BF865D1141284D8D9FD898CD234770A58E7067F (void);
extern void JTokenWriter__ctor_mDF6283CC2ADCE49961F3BC8E325589CB7FC2F7AC (void);
extern void JTokenWriter_Close_m6A9ED7E0B68686B48CFBFB1586E4F39721E2C671 (void);
extern void JTokenWriter_WriteStartObject_m86F0A42BCAA16952A61AB4C750FCA2F24C730A6A (void);
extern void JTokenWriter_AddParent_m9EED685872F6B03E08C52325B3A3BE40EE526619 (void);
extern void JTokenWriter_RemoveParent_mAB44295B22B4541BB912A8B4032AC0E880584BEA (void);
extern void JTokenWriter_WriteStartArray_m42F92D250276FE62FDCB36ACC68B6F2E516EAEB4 (void);
extern void JTokenWriter_WriteStartConstructor_mAAF69B644051CDAD6E9A67EE893A0EE351CB9018 (void);
extern void JTokenWriter_WriteEnd_m9BF72B0A60418730995F2B381DE81D578EED66F1 (void);
extern void JTokenWriter_WritePropertyName_mCDCE782E19D03CE67693D9677E7D8F390DCE79E7 (void);
extern void JTokenWriter_AddValue_mEFB9AFCE11F351BBECC252A69DD9009B4F12E829 (void);
extern void JTokenWriter_AddValue_mB221DF03D50CB4B9D440312A49E4847BF545398C (void);
extern void JTokenWriter_WriteValue_m7EA3EFF9672BBB3FA0CD1699EDBB669397CA7A39 (void);
extern void JTokenWriter_WriteNull_mE307172496449E150E112293DAC1564E052C865C (void);
extern void JTokenWriter_WriteUndefined_m427E6F6115F677E01109B4FBA0499A666226568A (void);
extern void JTokenWriter_WriteRaw_mD9EBA5DBD05645978D6F58A8EC9724C5B379F3FE (void);
extern void JTokenWriter_WriteComment_m04AF8F5FA456D3FA55EAA28A109CD9160DCDFCCB (void);
extern void JTokenWriter_WriteValue_m434696662AD2B4037C9AABD8FEC3C984E6828A9F (void);
extern void JTokenWriter_WriteValue_m9FE7461A8987FF55181F129F9AC5604354C92B21 (void);
extern void JTokenWriter_WriteValue_m470F253FC8D0CBE49179748CFA30CD5C7EF7BC05 (void);
extern void JTokenWriter_WriteValue_mA4FA9414CF25DBFDF581C43652780A1882EF6302 (void);
extern void JTokenWriter_WriteValue_m625E0EFEE44D45BD582500EF1D32579E722C6966 (void);
extern void JTokenWriter_WriteValue_m600C4172CA8D6E08130F993704FA817B41DE2618 (void);
extern void JTokenWriter_WriteValue_mFAA4FB131AB1BBE4C4E673CA0D5439AF1491FAC3 (void);
extern void JTokenWriter_WriteValue_mE3CCECB372E0E38EE79C05A64FE72481BAEFDD3F (void);
extern void JTokenWriter_WriteValue_mEB12CDDA5762D417B5A57A76AFFF8A61A6AF4371 (void);
extern void JTokenWriter_WriteValue_mCB225BB8D80CE649B845D6740BAD674483E342D7 (void);
extern void JTokenWriter_WriteValue_mF5C978547D0B796B78EC5ED8C9730050D0DA7EE4 (void);
extern void JTokenWriter_WriteValue_m3BAF64795364A1DE7616A272B3690BB5A6BDCDEE (void);
extern void JTokenWriter_WriteValue_m1103487C2734EF872E6A314E13F7884588D80D44 (void);
extern void JTokenWriter_WriteValue_m51B382B361509DBDD313E0C76C8571135C2DF487 (void);
extern void JTokenWriter_WriteValue_mFF835A395715E51BE6F197F9A631B1548AEF0410 (void);
extern void JTokenWriter_WriteValue_mBE32A4ABB5E92351E2F98FBE1D6ACE286E736688 (void);
extern void JTokenWriter_WriteValue_mAA41F1B2BCC5B70669133644957892B2D2DC47FA (void);
extern void JTokenWriter_WriteValue_mCD3839922FE5BA621EB22A52C257CDD0BF3EA46C (void);
extern void JTokenWriter_WriteValue_mAC9C7E0AE85B60136A5AEBF2EF3BB1DCC3AB8A20 (void);
extern void JTokenWriter_WriteValue_m71870D2DC541373CD86D5F8F078A0B1F41D4B8FE (void);
extern void JTokenWriter_WriteToken_m807585847122907134AE5C74B5793BD8ECC5C27B (void);
extern void JToken_get_Parent_m17B873780C3C7FE29F8F1212C31A062606D842C8 (void);
extern void JToken_set_Parent_mDC5EDB3881C8857323507BC955AACCEA70B9A22D (void);
extern void JToken_get_Root_m3E65C680E6D38FFCE33B97E1CE488E1D24CB58D7 (void);
extern void JToken_DeepEquals_m7EDAC81C962B86192A264B70E0F3D8476380DF31 (void);
extern void JToken_get_Next_mACA1FDDD6C404B82952BBDF16B6B50BD9D478AEE (void);
extern void JToken_set_Next_m52D3CB9747299C8329E124F13DF8B9B0B76E1E3E (void);
extern void JToken_get_Previous_mB73B2E756CCFB4DEBC3A5447B9D19983150E1EFB (void);
extern void JToken_set_Previous_m52A268BF0608F9E4C1BEC4B8E26CF67567A7AE89 (void);
extern void JToken_get_Path_mB79EC8961042AA9E87144C9D31545245A872F8B2 (void);
extern void JToken__ctor_mEF510CD05246F3C8B19F37A1DE5CF851D5A576AB (void);
extern void JToken_get_First_mD6C286DFD8B980A1C522C456465AB8DF306819BE (void);
extern void JToken_get_Last_m6096E6706EE76604C4C6254D178646656B521077 (void);
extern void JToken_Children_m15C8A3922FAC76E90FDD694133DB771C00B42FD9 (void);
extern void JToken_Remove_mE7E6F0738CD2057100B78230F30169A8EFB2365C (void);
extern void JToken_Replace_mF437D2189546BA0C6460B4BDF4FB0B255CDFA60D (void);
extern void JToken_ToString_m4FEE2B8A0F43D9B44EF5318381C42A66644B8415 (void);
extern void JToken_ToString_mF787D2075E09E9D7156C38286D1770E13F27A8DD (void);
extern void JToken_EnsureValue_mDD514610ADBDF5D210D52EA0B1AF1373C3C4E018 (void);
extern void JToken_GetType_m34BFC404F9653A235E5C8C267C7C71B9F7B8626A (void);
extern void JToken_ValidateToken_m91BE3C5241871ABB657621850C2CF3CE7A217D07 (void);
extern void JToken_op_Explicit_m667CE86F4F3E5AA03260EE74E0A3B19EC320BA3F (void);
extern void JToken_op_Explicit_m2369F270DA0ED8E11DBDDBD8B083753668552D61 (void);
extern void JToken_op_Explicit_mB2AA0B7D9CAF8710EC4BC761476ADA87367C2431 (void);
extern void JToken_op_Explicit_mCF04EDB5165001707270AFB4EDDC618D44659123 (void);
extern void JToken_op_Explicit_m983330188110B78B5C38C6FC428BB22F25708D44 (void);
extern void JToken_op_Explicit_m17766598D81527DFCE022414772D2D028D24FBE0 (void);
extern void JToken_op_Explicit_m8C505DBDBE9CF6455618C9044B03CCD6F1B3C88E (void);
extern void JToken_op_Explicit_mFC25C071BAEE281CE8B2CCFA50A52DA842AB3B83 (void);
extern void JToken_op_Explicit_m35E396EF054631C5591DE7E30C152948A0B6F081 (void);
extern void JToken_op_Explicit_m623DADB50226D4E8CA01390A029E152BCD82F667 (void);
extern void JToken_op_Explicit_m6F85BD1A0341A55F06CCA6F3B5073220553AE253 (void);
extern void JToken_op_Explicit_mB945F56C2DACBBDAA93C179537F05446CD736183 (void);
extern void JToken_op_Explicit_mBFC868A465B0117A48954857D00257B2B45CD31A (void);
extern void JToken_op_Explicit_mFEAA11DE7F198467BFBB99117B78ED8EC6182C1F (void);
extern void JToken_op_Explicit_m78A9D7EE501698D0219CB46B4B77F43B9573541A (void);
extern void JToken_op_Explicit_m96CFF4CF2251C191BE11AAB69609A3C317C65F95 (void);
extern void JToken_op_Explicit_m46246B1A16659CB6FEE9E562C57C166AA8E29BCC (void);
extern void JToken_op_Explicit_mC6D96B88E88C4A1E4F37230D825BBCC7870BC6EA (void);
extern void JToken_op_Explicit_m0DA4502F984E57F922858A834C73F47666F06B17 (void);
extern void JToken_op_Explicit_mC7F24314102F2A992E6C54C01D51E8669603DFE2 (void);
extern void JToken_op_Explicit_m11276548963D4907FC5A0087F69F837E63571927 (void);
extern void JToken_op_Explicit_m82C6393DCCF1C089B27BF49F4D8CB4C64C648EA8 (void);
extern void JToken_op_Explicit_m04CCE22D2C03A914819AAE7286D574A8E076DBFF (void);
extern void JToken_op_Explicit_m0353D5450D688D1922BEA396811C521E1FABE64C (void);
extern void JToken_op_Explicit_m46F6971D23F6A2074F077F3247083921715E5D4D (void);
extern void JToken_op_Explicit_m76828231142B4404434B41F093BD8A074D088909 (void);
extern void JToken_op_Explicit_mE150E80B2E833549545832E403C015366F7EF53E (void);
extern void JToken_op_Explicit_m332CCC3F6400FEE84A91A0C985983FD1B9F3189D (void);
extern void JToken_op_Explicit_mA435B5A6C389220DF41F02E1CAE6AEA4EEFBCFF3 (void);
extern void JToken_op_Explicit_m088FE678DEFCEB9F3849D6A988290A69798539DE (void);
extern void JToken_op_Explicit_m007D6A9ABD4A458B6E2534AD6E20218902717A8C (void);
extern void JToken_op_Explicit_m0DA7DE0B1765DF7733F7C204142771A13B20A566 (void);
extern void JToken_op_Explicit_m652395DDA469259A869DE2612C418E99323907EC (void);
extern void JToken_op_Explicit_mF933B006170CC4A490F47076B559E18F518816BE (void);
extern void JToken_op_Explicit_mDA1708251D921CBCE77B251AE04B67161530834F (void);
extern void JToken_op_Explicit_m6C085462C60E37B854411FDFF9634F9C448D9F6F (void);
extern void JToken_System_Collections_IEnumerable_GetEnumerator_mF67223DB32E0DA49CEA2652A94DD8324CEE221D8 (void);
extern void JToken_System_Collections_Generic_IEnumerableU3CNewtonsoft_Json_Linq_JTokenU3E_GetEnumerator_m0BA86062716EACA193C74ABBEA28DA1AE31AAB54 (void);
extern void JToken_CreateReader_m7661A095A1F4F6ADBE2B59794655CCAE0DD445E9 (void);
extern void JToken_FromObjectInternal_mFC4AFD80E0F0B2B5D95FDE9066E0C88EB6E32F04 (void);
extern void JToken_ToObject_m6ADC3E3DA56C6D22F90046C7D25B5D2779E54341 (void);
extern void JToken_ToObject_m4D96B7C479825194EC4FEFF0EFBCD306B78BAB5A (void);
extern void JToken_ReadFrom_mB9D1DDD43A246C5363263ECCAC1DB3E5235EA51C (void);
extern void JToken_ReadFrom_m13F77AEBCAF0F68830741402ED49A46BEBCB18C0 (void);
extern void JToken_SetLineInfo_mB4BCBB4634209AA828DA0159BA6A59851AE3F3C9 (void);
extern void JToken_SetLineInfo_mAEC923774319FC3D0E191EFD81ACB8C7FBFD0D7E (void);
extern void JToken_Newtonsoft_Json_IJsonLineInfo_HasLineInfo_m76B4FCAAD861984268CD071B0CFDC18CCD3F256D (void);
extern void JToken_Newtonsoft_Json_IJsonLineInfo_get_LineNumber_m2260CAA926CEFE6AAB53294F8C70A04A3CC44F52 (void);
extern void JToken_Newtonsoft_Json_IJsonLineInfo_get_LinePosition_m471CADC7212B2AF40B5A825C3B21EA41CB4950BC (void);
extern void JToken_System_ICloneable_Clone_m83691400354A7CF289ED84DC93354DD35528F2DC (void);
extern void JToken_DeepClone_m9A8C76BD945F1D49D1778B032BE6CF75CB350A4B (void);
extern void JToken_AddAnnotation_mB45E8328B471F72DEB80F5BFF2D8A83BD4D90467 (void);
extern void JToken__cctor_m617EECBF8913654B2613558A8615C487342B6121 (void);
extern void LineInfoAnnotation__ctor_m087A639C20E3829FD4322818C40DCBAC44184AC0 (void);
extern void JProperty_get_ChildrenTokens_m9F7647B340F91793C217C5217399706143DC258D (void);
extern void JProperty_get_Name_m96280838000C5E193121125A503BA9108DB707CA (void);
extern void JProperty_get_Value_m1D20FFEA7909BF0B835848D5FE9FCA2B08EF07F9 (void);
extern void JProperty_set_Value_m41797576CAF498FF53598E7DD99AF3ABCFE662B7 (void);
extern void JProperty__ctor_m8C1AF8455FC0BE033433F9B61D610438594C9E67 (void);
extern void JProperty_GetItem_mCB74A2D78796745CC1FED107DE4ACCA4C747C134 (void);
extern void JProperty_SetItem_m00A080A1D31D6FBEC2AA1DC90B900B3C851626F2 (void);
extern void JProperty_RemoveItem_m1EE45CE5F44F835C64183BE812018B280BC588BD (void);
extern void JProperty_RemoveItemAt_mE9CD738F149400D7D43712436841B5C773DC9377 (void);
extern void JProperty_IndexOfItem_m437AF150AB70425D939239304FB0E56E19606816 (void);
extern void JProperty_InsertItem_m1ABD1611D2B8EA24BB68BECB7A8D2DE26B5FA1BA (void);
extern void JProperty_ContainsItem_mB72C4413EFBB1DF0EDF3377094B52BC806C3573D (void);
extern void JProperty_ClearItems_m54AC98D6851F7C4A511AE0BD830F8AB7957C3CE8 (void);
extern void JProperty_DeepEquals_mFF73647AD6E0F72941500DB4E95AC0B4E138AAEA (void);
extern void JProperty_CloneToken_mA94A20011FC79498F3047F58F9294073EDBD037B (void);
extern void JProperty_get_Type_mBA3493784CA1E0D8672D84088893C004CC971B63 (void);
extern void JProperty__ctor_m989B6CE16B40176E9BB766B7E371ABC890BF573C (void);
extern void JProperty__ctor_mA59A3257A0EB4DB85735EF8F576917BF3B5415F1 (void);
extern void JProperty_WriteTo_mC777E5E99153D4F02B75718D2DE981F58F26D47D (void);
extern void JProperty_GetDeepHashCode_m22E73E9D1CECF4BFC9BE3A921FD25AC5CF3A6C4C (void);
extern void JProperty_Load_mDB8310C74ABE12E10DE590F257D86C8A27169BDC (void);
extern void JPropertyList_GetEnumerator_m5824B6E65D20B2C34F8BEC60AA48D258BDCEB763 (void);
extern void JPropertyList_System_Collections_IEnumerable_GetEnumerator_mB03E09F8C69248337E04846BF65A8A1CB3290418 (void);
extern void JPropertyList_Add_m1F395B95588471F3B3F94DF80FD2337577EA0AC3 (void);
extern void JPropertyList_Clear_mEDA5A3B0FAA7414429620FD785E45FE156805EBC (void);
extern void JPropertyList_Contains_m100D7BD94B0EE4ECEF9B6D2616BC9DA6D8C62BB9 (void);
extern void JPropertyList_CopyTo_mFE61BD1725F75537BA1154855512C1A72DD0C51F (void);
extern void JPropertyList_Remove_m50FAC96ED6E3863A2D32D37CAF5796132B152641 (void);
extern void JPropertyList_get_Count_m80C947E8BD74B928ECBBD72CE6EEBD81340A8E48 (void);
extern void JPropertyList_get_IsReadOnly_m80AF8FED0CBCFB196B1C9FEA73F8A83B365E4479 (void);
extern void JPropertyList_IndexOf_m725F7A875C197243EB227397294642997EC1072B (void);
extern void JPropertyList_Insert_m278B0EBA585882903CA04F3A51894B2D5667FCE4 (void);
extern void JPropertyList_RemoveAt_m3391387DA0B1473F81103C7C94F42262D728D56F (void);
extern void JPropertyList_get_Item_m1454E827714F686A14D4DA780725294696E6A450 (void);
extern void JPropertyList_set_Item_m17B7C8C4C6C17E09CEC53A5DD89C15B13D8F19F9 (void);
extern void JPropertyList__ctor_mDD66F5F4B93D02BDE3615A3CA00190901F32E616 (void);
extern void U3CGetEnumeratorU3Ed__1__ctor_m5B6F8A76287F0CEB16B230609710257010459B7B (void);
extern void U3CGetEnumeratorU3Ed__1_System_IDisposable_Dispose_m9FE9AEE2588E667D69233EE649B1F8839A012662 (void);
extern void U3CGetEnumeratorU3Ed__1_MoveNext_m68905C0409642C2522F5569A7234AC5523FE4137 (void);
extern void U3CGetEnumeratorU3Ed__1_System_Collections_Generic_IEnumeratorU3CNewtonsoft_Json_Linq_JTokenU3E_get_Current_m8DBD3517400081107444232BDD7C39B3449B3F93 (void);
extern void U3CGetEnumeratorU3Ed__1_System_Collections_IEnumerator_Reset_m6EFEBC9D0D877C84C433DC27AA114B8B3D49F64A (void);
extern void U3CGetEnumeratorU3Ed__1_System_Collections_IEnumerator_get_Current_m19C57ED9F0AA85C510DF342A2CBAFE5AA2AFFDE3 (void);
extern void JValue__ctor_m2F2F6199A81B5C5C048F78EADF880B9C5A3CAAFC (void);
extern void JValue__ctor_m3F20532C2284C822B4BB9A8DDDED8B344BAA0AAB (void);
extern void JValue__ctor_m6B8489761C009E7F28C5763F88146DB5CE345E51 (void);
extern void JValue_DeepEquals_mCC778967BEA5D41BFA0D67F601C6FEB754DC6698 (void);
extern void JValue_get_HasValues_m16397FF6A2E008E4A19F42FC348BFFFFD303D4D2 (void);
extern void JValue_Compare_mEEAB5FC08A482CD63CF831BD1090561094D3494C (void);
extern void JValue_CompareFloat_m6AACE292E958241C1802DCEBBD3DCF6DF52522A0 (void);
extern void JValue_CloneToken_m415084FF8472BA71E0CFC86836855B1C3B6DB611 (void);
extern void JValue_CreateComment_m6C65B659D0F245FB5613F776CAA1006A9AF67828 (void);
extern void JValue_CreateNull_m53D9AAAFB0E4707B8538937B19F18759DF10E144 (void);
extern void JValue_CreateUndefined_m8182EAF420674522680EBEC1878195BF32F05CFD (void);
extern void JValue_GetValueType_mAABD4B40D8D7C713FAC8F57FFAC3BBE511D43EA0 (void);
extern void JValue_GetStringValueType_m2120C09E27DD55F09EC04FB162E3EF1AFBB92288 (void);
extern void JValue_get_Type_m04877676E0AF75090174BFF9636B919A3DE2D3F0 (void);
extern void JValue_get_Value_m7E0B68F90B51FD1ECC4C659765F6F949C0B0F35C (void);
extern void JValue_WriteTo_mF412D9869631021CCDC6EE397C7FC1A60CC35033 (void);
extern void JValue_GetDeepHashCode_m52620331DF1615C845D867B36CF86A64713D6ED0 (void);
extern void JValue_ValuesEquals_mA214E50023BC11A9E2103FD19B3A06A119F8BA2B (void);
extern void JValue_Equals_m9D93133F3F7F7BF4072ED20B901B8A4DAF94A453 (void);
extern void JValue_Equals_m7D39E26AD4F530AF12EAFCC680F61DD99030781F (void);
extern void JValue_GetHashCode_m6313D73045A9C91C4984302DF7870205C98B3D1E (void);
extern void JValue_ToString_m2466E655FFE75B44C96B68B9F7B4E631CF0BCA29 (void);
extern void JValue_ToString_mFC9CA3AA149DC9EEAAB0440DDCEE7A9A65F032C1 (void);
extern void JValue_ToString_mDBD67FDCBA088D69F64A6CF4BF9800903A1ABADF (void);
extern void JValue_System_IComparable_CompareTo_m6A03F746EFFBFFAF620A49D3EE3DDB84254B1EB6 (void);
extern void JValue_System_IConvertible_GetTypeCode_m7A6B61857BAE024F30DCD9F24EAB8DC134C69BAB (void);
extern void JValue_System_IConvertible_ToBoolean_m013182504A7AF6B64212092614EE91FE42FA075B (void);
extern void JValue_System_IConvertible_ToChar_m2299D2462EE9C600605CAE4F1FC160D1E2815D2A (void);
extern void JValue_System_IConvertible_ToSByte_mB653A11EF16D15B518C9CE4BEE362344E017F868 (void);
extern void JValue_System_IConvertible_ToByte_mBD481AE7B3922275570D7399863649B0F8E30E45 (void);
extern void JValue_System_IConvertible_ToInt16_m342AA1180C6261EDB371BEF2FE572245A6EEBB39 (void);
extern void JValue_System_IConvertible_ToUInt16_m617AEA33948E9A9CEAB10486733F14DCCCB6C8CA (void);
extern void JValue_System_IConvertible_ToInt32_m16735D81C4F3F7FAD8742822EBD688AB963645A1 (void);
extern void JValue_System_IConvertible_ToUInt32_m51A4013CC278349F58E22F37C19D2FF9F6CD64AB (void);
extern void JValue_System_IConvertible_ToInt64_m2015004A64DFCA96ADF151296C82EFA2AE993BF5 (void);
extern void JValue_System_IConvertible_ToUInt64_m771CCACEC2CE2F776AABA7B63F7F4D44FA113A91 (void);
extern void JValue_System_IConvertible_ToSingle_m5625F9F98CBA4493904457BA6D57B673BDFD52AD (void);
extern void JValue_System_IConvertible_ToDouble_mFDF1062C8FD49160FEEEAAC6B49D2A3E422B019F (void);
extern void JValue_System_IConvertible_ToDecimal_mD60AB15D746C975261E182F9F93A07628F5D7CF8 (void);
extern void JValue_System_IConvertible_ToDateTime_m1FD44BF16F9A324AED43AEFD6AD37C29DBAE5BE2 (void);
extern void JValue_System_IConvertible_ToType_mA94787EF3D65999A0E6C71441846A0C462AE2523 (void);
extern void ArrayIndexFilter__ctor_m10910C433209D36872F1C9664A792BB2B415FD11 (void);
extern void ArrayMultipleIndexFilter__ctor_m68AE4516C859AD1D41720136DA23E8548FF7D369 (void);
extern void ArraySliceFilter__ctor_m642EC9DC6FE0A23FEAD208F2E8E1AAF26AC35647 (void);
extern void FieldFilter__ctor_mE5BC245B72B8BF4E311457A98512B494B4602D56 (void);
extern void FieldMultipleFilter__ctor_m083DEA736BFCB1F9D3CBB5F9FE9B4BDD2194E54E (void);
extern void U3CU3Ec__cctor_m84666D2594306D5E829449247FE6F7459A7736D4 (void);
extern void U3CU3Ec__ctor_m365A5B2543687AE5277DA252DEF85A28A163B0DA (void);
extern void PathFilter__ctor_m94E7029DA14F6B51E42656FDBBCCC4B7ECAD19E0 (void);
extern void QueryExpression__ctor_mE792ADE7A57E796DE334EBF1136BC323B2EC4196 (void);
extern void CompositeExpression_set_Expressions_mD8F85558E58AC33E965CB9D60E89BDEDF32B6795 (void);
extern void CompositeExpression__ctor_mDFCD0E37BF8EFB93016797CD2CEE145C92F9AD60 (void);
extern void BooleanQueryExpression__ctor_mC2E073D357BDDE808589A9CBFC3DE9CFEE30A381 (void);
extern void QueryFilter__ctor_mE1F383E4E24BB6EA82086AD5209AC828DB7C3799 (void);
extern void ScanFilter__ctor_m976B5D26D55750F4ADA2DE0F437E95C68761CF69 (void);
extern void BinaryConverter_WriteJson_m54AFB7F3527E39626E86389F48BAE9E9A56ED98B (void);
extern void BinaryConverter_GetByteArray_mD5871B065CC5FAB077E17BDE734FBD365836DEFE (void);
extern void BinaryConverter_EnsureReflectionObject_mFF1C9D88AFCA9F92F7FA127E89C9C23083730A75 (void);
extern void BinaryConverter_ReadJson_m93A33D05378C772EF0984563A78641EC5995625C (void);
extern void BinaryConverter_ReadByteArray_m8744B9592F4CFE1AFD20387016C1FDE36E45EEAF (void);
extern void BinaryConverter_CanConvert_mA8CF5D87606B8AC2C21B833329D9D68E752916A8 (void);
extern void BinaryConverter__ctor_m6A759CAB2FA8F38DCEF840B9DF367F2EBDC0845E (void);
extern void DateTimeConverterBase_CanConvert_m6319B09EB67CC570AA771FB6684A43D9E8D40697 (void);
extern void DateTimeConverterBase__ctor_m02E6F20A4F764E261FA171A2A0D8694121CAEB06 (void);
extern void HashSetConverter_WriteJson_m7258B2FBE81FE3B00090BAA6B9F1BB61FF2BFD91 (void);
extern void HashSetConverter_ReadJson_mCAAE97098C36EB09E16A5F5301337BA9922F3A79 (void);
extern void HashSetConverter_CanConvert_mED54AD057BBC9F636D7EFE7D7636FA3164C40ED8 (void);
extern void HashSetConverter_get_CanWrite_mB6507CDFB1B08D70274FE786BA0EB6A7326ACC62 (void);
extern void HashSetConverter__ctor_mA656EFE7F0C66AAE6D9819F2614B980E583A7662 (void);
extern void KeyValuePairConverter_InitializeReflectionObject_mECD1547C1FDDAE2A63C8FE1C2B2E45AE555B16F3 (void);
extern void KeyValuePairConverter_WriteJson_mB71698F2630B93D03C057F63D18FFEC98E0F9FA1 (void);
extern void KeyValuePairConverter_ReadJson_mA993A01FB25C456BFE0DEE5CA1CEC18DBF0AA839 (void);
extern void KeyValuePairConverter_CanConvert_mF101B6EFD84D2BCE96EF03553298F5BE10B66ABF (void);
extern void KeyValuePairConverter__ctor_mE945E5DD56F012CBB65CD6213B18365A89B23EEC (void);
extern void KeyValuePairConverter__cctor_m2529C663777F1625C160C3868539754FD920F863 (void);
extern void BsonObjectIdConverter_WriteJson_m73F5985D9544CE0BEFEB95DDAB45E9BF1C8FB7F3 (void);
extern void BsonObjectIdConverter_ReadJson_m3FA7C54B894B616778D5C145EF1029FE68990C88 (void);
extern void BsonObjectIdConverter_CanConvert_m46DF6406FDBD5DFAFCB5F92167256C107DAC0D70 (void);
extern void BsonObjectIdConverter__ctor_m9A8067BEF8BF07BEC8A7FA3E956475540E4E495C (void);
extern void RegexConverter_WriteJson_m65FA4ECCF0C6A86ED4D93A62F79DD2848EEA910D (void);
extern void RegexConverter_HasFlag_m8E63783C5E9BCD5DABF3824CDE1806E3E14D6368 (void);
extern void RegexConverter_WriteBson_m4DDA78E9F73244FA1A404186035D84F684BCB791 (void);
extern void RegexConverter_WriteJson_m146024645167B8D2B9CB7743115A8B75B1A9ED4C (void);
extern void RegexConverter_ReadJson_m7CF77989D9D66E5D39D1C1837CBB5A64A57B411C (void);
extern void RegexConverter_ReadRegexString_m5202370764B9C1DDC7876800837CE4E377E189C1 (void);
extern void RegexConverter_ReadRegexObject_mBDF60E21C6F61527B07C7F03ED7930A93C1DABEE (void);
extern void RegexConverter_CanConvert_m8DED3CD0981D1A6EDC73718DB07246F5EDEFAABA (void);
extern void RegexConverter__ctor_mC1D5166D65F3777509842BB033FC4111EB826167 (void);
extern void StringEnumConverter_get_CamelCaseText_m638D7E2E1ADFA8259C5C5DC44C4D6FFF4FA075F5 (void);
extern void StringEnumConverter_get_AllowIntegerValues_m773843BCE2AEFD6CF8A21ABD17918C4CF62B1F9C (void);
extern void StringEnumConverter_set_AllowIntegerValues_m06EEDD4CA3D661984541A7E3CA1C5DD8E763ECF1 (void);
extern void StringEnumConverter__ctor_m36B6009E735666675AEB30CD83B385291E98CC73 (void);
extern void StringEnumConverter_WriteJson_m3F8A6A8539AD9E7209996B11277DA597DBB8B22D (void);
extern void StringEnumConverter_ReadJson_mAF074420B274DE15507DC1FA2CB5E0E126BC5067 (void);
extern void StringEnumConverter_CanConvert_m893906149FEF1949D9BA1F3674E7C98FD275FEA3 (void);
extern void VectorConverter_get_EnableVector2_m3B3C90FBD2669464F10143CD10CB34DE1A9C65EC (void);
extern void VectorConverter_set_EnableVector2_mE7927EB6096D3E1F0E376C73657BFFAE4EA93F01 (void);
extern void VectorConverter_get_EnableVector3_m717868EFFCF7AAD56AA58CE8FFA579B0B48E491A (void);
extern void VectorConverter_set_EnableVector3_m396E789698564789734CDD684A1B1A6589FCB448 (void);
extern void VectorConverter_get_EnableVector4_m64A5B4F3C38F459E77DBADA18927120326F1BF42 (void);
extern void VectorConverter_set_EnableVector4_m7ABC7C2C252E7DFB946BDEDDEB33B1D0792605F4 (void);
extern void VectorConverter__ctor_mD7EC6E1AAD9CA4C174A718BEAA50AB26649BC3DD (void);
extern void VectorConverter_WriteJson_m1EC6AC34618A6B91BE9240D12EF6575DF0C94B7B (void);
extern void VectorConverter_WriteVector_mABBFEE702E0E9959EB00722E64500943D57DF997 (void);
extern void VectorConverter_ReadJson_m53BEB27FC5055DEF363EF25DF780E262FAA7E013 (void);
extern void VectorConverter_CanConvert_m64936D2B51C6C6F5C4F5C062D2B35E727AC512F5 (void);
extern void VectorConverter_PopulateVector2_m646F8116C9723199ABF4A82AE49244827EEB9132 (void);
extern void VectorConverter_PopulateVector3_m801E5C586E2BA8EC8103C563387E20B6E190035E (void);
extern void VectorConverter_PopulateVector4_m1EE024C4E16391F29D2DB02913E475F547EE9F7B (void);
extern void VectorConverter__cctor_mC0459ADAB2DF71517A52E74EA34914A5C797C951 (void);
extern void VersionConverter_WriteJson_mAF07010E49ACA90B579F5899FD77D6E59101E1C8 (void);
extern void VersionConverter_ReadJson_m7965482BF5F14BE5B2C39F483A32FB074CD48FAC (void);
extern void VersionConverter_CanConvert_m6AEC0547939825EE9D68F3353854893A51220E6A (void);
extern void VersionConverter__ctor_m847EFC9A24C80B75A2A30334525A3859C6A5B3C8 (void);
extern void IsoDateTimeConverter_get_Culture_m6CF18B78DD2A289E49E7A8DB3968460CAA38B55D (void);
extern void IsoDateTimeConverter_WriteJson_m60AFBDD6FB1FC4870E4B0FDA30B6067666507B18 (void);
extern void IsoDateTimeConverter_ReadJson_m0662899DDF147D4EBAB5BFFA4095E36D37B58EE3 (void);
extern void IsoDateTimeConverter__ctor_m826994A815114F0A06F4788BEAF6A1EE2AFD0432 (void);
extern void JavaScriptDateTimeConverter_WriteJson_mF0E3B26942C1C76C5E1C2D5A0C0543F9742973BE (void);
extern void JavaScriptDateTimeConverter_ReadJson_m36EA36CCD31603F16FB4C3E19A8E088872214348 (void);
extern void JavaScriptDateTimeConverter__ctor_m54DE0ABCE8E276690146CF30D4CE207AD6C0C5F1 (void);
extern void XmlDocumentWrapper__ctor_m2DDFA872888661813F74523693DB4A34FE765DFE (void);
extern void XmlDocumentWrapper_CreateComment_mC922EE1533661AC0B35C262B206E321E22E440EE (void);
extern void XmlDocumentWrapper_CreateTextNode_mC5999FC1111FF593533A0A3C24981EB68E11CDB8 (void);
extern void XmlDocumentWrapper_CreateCDataSection_m09B05E9D61F98937C0CDABB9C2EB8D09E56BD033 (void);
extern void XmlDocumentWrapper_CreateWhitespace_m680AFD5D1D1E347930980839A099D5DAA4386253 (void);
extern void XmlDocumentWrapper_CreateSignificantWhitespace_mAF94AFB2BB046CC35F48AF3CA5C93ABA2558D908 (void);
extern void XmlDocumentWrapper_CreateXmlDeclaration_m5B96DF051DD3F731A388796F6D92B269D69EFE75 (void);
extern void XmlDocumentWrapper_CreateXmlDocumentType_mD0588943D845FEA8D58958FD8694796DD94CA07C (void);
extern void XmlDocumentWrapper_CreateProcessingInstruction_mABF52EF273331B86C3CEDD3B50362E540CFB27D9 (void);
extern void XmlDocumentWrapper_CreateElement_m45C3BF72AA259B9947F65860F97065B1C283335D (void);
extern void XmlDocumentWrapper_CreateElement_m98D9C428823DBD96E19D1DC3B5BCCB304A28D941 (void);
extern void XmlDocumentWrapper_CreateAttribute_m79CF11B365F9F1FD574ECA810BF1F13FDDD82464 (void);
extern void XmlDocumentWrapper_CreateAttribute_mC12DF3D00C4027F881A72FE1961BFBCE47224D49 (void);
extern void XmlDocumentWrapper_get_DocumentElement_m55855C0394DD8B83FDD19B0E71EED3B4994E7119 (void);
extern void XmlElementWrapper__ctor_m5EA54DDEBFA94B7DF7907BF8BCAD9A01C0AAE237 (void);
extern void XmlElementWrapper_SetAttributeNode_m8093E93AE8461E128D28CCAFF4A7355993C2DE67 (void);
extern void XmlElementWrapper_GetPrefixOfNamespace_m6C70E6D6B59A1E6534EFA7574D2050DCD258C3E7 (void);
extern void XmlElementWrapper_get_IsEmpty_m6C29E47E666F9A547CB3E8367FB741D1DC08C6AA (void);
extern void XmlDeclarationWrapper__ctor_m4EAEA1BBD6AA3E66D3DBF18613BDCC12294F9529 (void);
extern void XmlDeclarationWrapper_get_Version_m5AC22814EC256D574E5D37DA6601925D92B9E7CA (void);
extern void XmlDeclarationWrapper_get_Encoding_m479E2628A4A6375AAB4C7D302D4481DB88320E23 (void);
extern void XmlDeclarationWrapper_get_Standalone_mA75E58DC0B6B4D48194D79195D1ECBA5CF2A0E5F (void);
extern void XmlDocumentTypeWrapper__ctor_m127F27D80D29C326F622FFD70CA1B3549F90C78D (void);
extern void XmlDocumentTypeWrapper_get_Name_m2FA9037C5566D8AB029C0D69129E3B60CF9DC510 (void);
extern void XmlDocumentTypeWrapper_get_System_m9C848AAFC549088092657DB36BD8E04ACF0121FC (void);
extern void XmlDocumentTypeWrapper_get_Public_mE68A735EF0E56B8C242CEF4385CEA024A6C72C05 (void);
extern void XmlDocumentTypeWrapper_get_InternalSubset_m5D296CBCCDB4C974240A075E3B4755D18FFFA022 (void);
extern void XmlDocumentTypeWrapper_get_LocalName_m20D20EEA8F216955359FD85C71CDBEF9210B65B0 (void);
extern void XmlNodeWrapper__ctor_m73806A21D3A81678F6099B62E80F7B6549D56D17 (void);
extern void XmlNodeWrapper_get_WrappedNode_m95E187562E167E13524197070157B15EE0ABFDFF (void);
extern void XmlNodeWrapper_get_NodeType_m704A1F53F159B290363B45893B421742FDF82D86 (void);
extern void XmlNodeWrapper_get_LocalName_m3F78DEA57A56000D98457B5A983CA62574E42D37 (void);
extern void XmlNodeWrapper_get_ChildNodes_mB44F94F340476C1993AABDBCD77F535ACF68BD4E (void);
extern void XmlNodeWrapper_WrapNode_m45C8C60B81EA49FF73F8BB1F18EE8E4E8DE52DB4 (void);
extern void XmlNodeWrapper_get_Attributes_m6A73C3FCD3E8E04EB983E48270831A03D0120B01 (void);
extern void XmlNodeWrapper_get_ParentNode_mCE0D181D5080D05A5010AD8FA391AE50547FC260 (void);
extern void XmlNodeWrapper_get_Value_m12711466B29D2B6C01C3041813F4FD4988C973D0 (void);
extern void XmlNodeWrapper_set_Value_m978C9C12AC1A81119D1DA28B143406231E41F41E (void);
extern void XmlNodeWrapper_AppendChild_mF2D050E07BD4E4A0A4265872163D9C7232B3678B (void);
extern void XmlNodeWrapper_get_NamespaceUri_m5F7FD8E9F0B9546B357626FC34D2C7A2D344C568 (void);
extern void XDeclarationWrapper_get_Declaration_mD7686F2EC3F760FD0AECC4FC6DD5E21F9F888437 (void);
extern void XDeclarationWrapper_set_Declaration_mB8CF488A14A6A4CE715CF6D65E9D4642746CF378 (void);
extern void XDeclarationWrapper__ctor_mC9E3C1C1550DD2576A03D5CCD4029BD8C64A18DF (void);
extern void XDeclarationWrapper_get_NodeType_m62F1C5F17B127A6F8E3D1427259D7D2D0714F5B7 (void);
extern void XDeclarationWrapper_get_Version_mE4E712D0E0E96742BD5333811C8D0FDA5836BF5D (void);
extern void XDeclarationWrapper_get_Encoding_mE6CADFD39D778C76B41813C61B0A1E1BE513132A (void);
extern void XDeclarationWrapper_get_Standalone_mACE8B149D751736B59E0B61A943E1A84E9AF70CD (void);
extern void XDocumentTypeWrapper__ctor_m448FB77B151C12873112FF5CC9B314E9DB8F6117 (void);
extern void XDocumentTypeWrapper_get_Name_mA074AA947F84C787AA869A40F7B0C7F64A502B93 (void);
extern void XDocumentTypeWrapper_get_System_mC5CAC6D108869FFA6E547C06ED171CA1E235A51B (void);
extern void XDocumentTypeWrapper_get_Public_mD55A43097688C1497C9ACCE3DA915944322A8C6D (void);
extern void XDocumentTypeWrapper_get_InternalSubset_mEDBD4FCEAD914AEC869A485459F8908BEAE7E07C (void);
extern void XDocumentTypeWrapper_get_LocalName_mD17BA8F109A4B4123FFBC5317380A93E730A4EE0 (void);
extern void XDocumentWrapper_get_Document_m368F6051580AEF8FBF78877386D469E1CAD779B6 (void);
extern void XDocumentWrapper__ctor_mE05A6619DC930C9ABB32122C06DC694533611BDB (void);
extern void XDocumentWrapper_get_ChildNodes_m90EBA8E6ED65464C93E749266085473592223F23 (void);
extern void XDocumentWrapper_CreateComment_m581B5E8B4BC22B4681EB5951BC1F8220C0832373 (void);
extern void XDocumentWrapper_CreateTextNode_m2946508BCAEC542051832C77CCDED138EE841889 (void);
extern void XDocumentWrapper_CreateCDataSection_m8506E7328F857D71EE8C68DB9621B994F9C6A77F (void);
extern void XDocumentWrapper_CreateWhitespace_m3737ECC4F95F648AC7417D2C73EA0FA239640DDC (void);
extern void XDocumentWrapper_CreateSignificantWhitespace_m409A710011DF878E02B3D163B64232C0ADE07C28 (void);
extern void XDocumentWrapper_CreateXmlDeclaration_m8CC17AA93A14B7FB730FDDA9FE0AC7694088A8CF (void);
extern void XDocumentWrapper_CreateXmlDocumentType_m1ED0DA0569F9256577F23A9A65236E784BC031DF (void);
extern void XDocumentWrapper_CreateProcessingInstruction_mBD1AEBE0846FDA921B485F5A457F10110ACF2510 (void);
extern void XDocumentWrapper_CreateElement_m906BF5976F58F7AB707BF445B01F4936B578082F (void);
extern void XDocumentWrapper_CreateElement_mFC40283D600E1C5B804460EFB4E442DCDF9FDFCF (void);
extern void XDocumentWrapper_CreateAttribute_m8224C607521D472529726F0C7D9418BBE42EDAE6 (void);
extern void XDocumentWrapper_CreateAttribute_m90A8CC160233F03025ED08AAADFB3B2CE4F127BF (void);
extern void XDocumentWrapper_get_DocumentElement_m5F9F99F972213A26BB538D7B3BBFDB126988BBE7 (void);
extern void XDocumentWrapper_AppendChild_m266B3D4945ADE6EF06CC3CAE337432F997E2D21D (void);
extern void XTextWrapper_get_Text_m15F65133CFB2B8E2F1AFBFE1EE9829669C3EF587 (void);
extern void XTextWrapper__ctor_m98B8EFBEC0CA0A43C328B86120E043D5A4FE8222 (void);
extern void XTextWrapper_get_Value_mDDB4561D37D181C792CF57122502CDC591B8CB4D (void);
extern void XTextWrapper_get_ParentNode_m5A470009D3268A28C9A5A9B8ED440C7FAE3D0A42 (void);
extern void XCommentWrapper_get_Text_mF0823EAF847E1268D50A6E4D0345828B2E2C7B22 (void);
extern void XCommentWrapper__ctor_m8DA2766F6E2CDC00FA2CF73EAAF21B30AFCD80B1 (void);
extern void XCommentWrapper_get_Value_m4530DD772239251DC7E9D31647FF1370C502C53B (void);
extern void XCommentWrapper_get_ParentNode_m2AE469466627F7255559F9B07E7C9A1F65CCCF76 (void);
extern void XProcessingInstructionWrapper_get_ProcessingInstruction_m465CA1E4A90DC9B2D5952C18B36D31BBC4FE9F83 (void);
extern void XProcessingInstructionWrapper__ctor_mF6EF6425C7E1A9D9F46CB988F8C9E542ED2153CE (void);
extern void XProcessingInstructionWrapper_get_LocalName_mA87D8E9F6975B75FC134449FCB3B8CC02602ACA3 (void);
extern void XProcessingInstructionWrapper_get_Value_m7C81865922E146B3EE1E2FB6AE81C2A720327C45 (void);
extern void XContainerWrapper_get_Container_mE8D578EC87F352A68CADF4E01DFAD5C242BFEDE6 (void);
extern void XContainerWrapper__ctor_mDA5B56465A0FE70773B861EC590E47653923DFC4 (void);
extern void XContainerWrapper_get_ChildNodes_mC85E0A4FF1522B40B19AEE06B034DE7C52EF1CA4 (void);
extern void XContainerWrapper_get_ParentNode_m945C070A2B46FAE7DF88629434EEA8672CF735B0 (void);
extern void XContainerWrapper_WrapNode_m45080E6A0D0A531566B9B6B20B75328E5AD91CBA (void);
extern void XContainerWrapper_AppendChild_mD0C76048C57DBC7BE2A08BC215761BEF2552BC42 (void);
extern void XObjectWrapper__ctor_m24EB638F7E591225CA536A66FEE9B6EE6D7E8330 (void);
extern void XObjectWrapper_get_WrappedNode_m3CA9623838D4D4A96DCF882FC3C592D3D64D7573 (void);
extern void XObjectWrapper_get_NodeType_m0EC8D2F38FAC096C9A1C91B78A107C89FE08B867 (void);
extern void XObjectWrapper_get_LocalName_mF7CD25C52F1D0EE14C5D9063035C27E1B237F421 (void);
extern void XObjectWrapper_get_ChildNodes_m43561E35BC5ECC8A22C56C1CB479B4CE638EB5B7 (void);
extern void XObjectWrapper_get_Attributes_m7C1E335535C8FB2A6685770BE0308503578EEFE5 (void);
extern void XObjectWrapper_get_ParentNode_mFEE478450A586823D4E4F27B439C3B7CBD3167BE (void);
extern void XObjectWrapper_get_Value_mE59398A1F64A5FB46732FF84DF7E263FB201CE38 (void);
extern void XObjectWrapper_AppendChild_m93FD89B25381B97A651348E158D8E33E4F37821B (void);
extern void XObjectWrapper_get_NamespaceUri_m5CD84D5CD1475CAC24C1535FBF1D65147E58987F (void);
extern void XObjectWrapper__cctor_mB86D35437E19AAA15DB854855C78FA0DC3686A71 (void);
extern void XAttributeWrapper_get_Attribute_m64F72E3BA361213F069AF1897D47A9586F6FA2E6 (void);
extern void XAttributeWrapper__ctor_mCD158BF6A02EA1460BD22E3C99816A3308E2DC39 (void);
extern void XAttributeWrapper_get_Value_m8B697AE38E2DDBC0E31E7860009689FF5DA89BC2 (void);
extern void XAttributeWrapper_get_LocalName_m972043B9A5DD764604E5A3929625E04C0451D596 (void);
extern void XAttributeWrapper_get_NamespaceUri_m6923813F42B9F0ABB8F8B542EFE3BA4B797DABFF (void);
extern void XAttributeWrapper_get_ParentNode_mA27F66095E98A673B04FBC961F3023F2D92467C8 (void);
extern void XElementWrapper_get_Element_mCE45E811C472BF01E89449EAD91DB4C37B5B2074 (void);
extern void XElementWrapper__ctor_mADC48A43A4BBD8C2FEBF88F4A45ED0CA69FAEC7F (void);
extern void XElementWrapper_SetAttributeNode_m87ADF1FE3CAA994A2B35154DB1EA020E530FF195 (void);
extern void XElementWrapper_get_Attributes_mBCEC2D09F107433A944B9DC2DA694454C4B2FA49 (void);
extern void XElementWrapper_AppendChild_m7F2CE9197158F3EFF8555A062D195E207530AFAC (void);
extern void XElementWrapper_get_Value_m8610D6D1BBD66BFD3DB13666D4FFF031F6C6F561 (void);
extern void XElementWrapper_get_LocalName_mA47C683BF562D2B6AD547329F99FB15945037BDF (void);
extern void XElementWrapper_get_NamespaceUri_m537A5A8F6039B89961825F2EFFBD0D57F8EB0B04 (void);
extern void XElementWrapper_GetPrefixOfNamespace_m6E8DA9B2D4BAF02CD2660213C9CA4405575F1D19 (void);
extern void XElementWrapper_get_IsEmpty_mF42A83AA38E3BA1A7F55EC274D4D7ECAB1DDB880 (void);
extern void XmlNodeConverter_get_DeserializeRootElementName_m2A0B73747601E99F60881D50EF7D55F0EDBE85A4 (void);
extern void XmlNodeConverter_get_WriteArrayAttribute_mB90AAB6C83E3DFD4E0F0FE8DBB9BF151248378F2 (void);
extern void XmlNodeConverter_get_OmitRootObject_m39D54F5791FD93911139FB9E46BABBDE5D062184 (void);
extern void XmlNodeConverter_WriteJson_mC9928FB4E2184CDC9F294BB9AD22AC5F5E8E0CF9 (void);
extern void XmlNodeConverter_WrapXml_m75EF1038542649689A89FBF5A3F2CED042EBF228 (void);
extern void XmlNodeConverter_PushParentNamespaces_m72AF63B5C2C7D78F06FA7A495DADCA6759027AA3 (void);
extern void XmlNodeConverter_ResolveFullName_mC5F07F462B6FE5B4EB555D78255961F40B8C3616 (void);
extern void XmlNodeConverter_GetPropertyName_mC4EBA7F268695B3CE90E87702E7CDF96ABB01BA5 (void);
extern void XmlNodeConverter_IsArray_mF83080575E162DED85AA9E7F30384F4FCECF4A48 (void);
extern void XmlNodeConverter_SerializeGroupedNodes_m2310D6C0E3FA9FCDBFC1677DEFE3CFB237E6BC0E (void);
extern void XmlNodeConverter_SerializeNode_mBBB1AA0A7AC24A39675F1B210F51870CE932D6AA (void);
extern void XmlNodeConverter_AllSameName_mA177F66B66101491F3DCEAD394E1A1C87F24E8FF (void);
extern void XmlNodeConverter_ReadJson_m439264C33B17C4B9AB9EB03EDB14D20DD339FDD1 (void);
extern void XmlNodeConverter_DeserializeValue_m6D20454FDBBEEE2CABB0ED3EC1BCF8F2E0067AF0 (void);
extern void XmlNodeConverter_ReadElement_m09C859D452268EBC6B893181FDF83B8CC7BDFAD8 (void);
extern void XmlNodeConverter_CreateElement_mB62D7411435EBF38A92DDD2E3F37325D14CC5819 (void);
extern void XmlNodeConverter_AddAttribute_m20D57606286322BD2581F04FDB59C6661BD3177F (void);
extern void XmlNodeConverter_ConvertTokenToXmlValue_m1D07C5BEE5206E724B94ACD8231788702F3BBA2D (void);
extern void XmlNodeConverter_ReadArrayElements_m3A4E70784167D3D49FB40746E404602CA282B3D6 (void);
extern void XmlNodeConverter_AddJsonArrayAttribute_m2B9B98BC1EC11FEDB63C71314C1FD3312E20A800 (void);
extern void XmlNodeConverter_ReadAttributeElements_m9B56D685A23448B661C73F3483B9B4063D55C049 (void);
extern void XmlNodeConverter_CreateInstruction_m7A5C6039FEC825B248B75841D11C1DB284F78006 (void);
extern void XmlNodeConverter_CreateDocumentType_m4438D07E2CC5D23244A39CC98EE3F75B74B06FFA (void);
extern void XmlNodeConverter_CreateElement_mD8BF7ADE4CB3682433E964C9986042FFE403FE65 (void);
extern void XmlNodeConverter_DeserializeNode_m7CF6B466118CB9E0CF4CAC90CA34BC769B45229C (void);
extern void XmlNodeConverter_IsNamespaceAttribute_mD9C356F879F0FF7F1006E34842F95ADE42128868 (void);
extern void XmlNodeConverter_ValueAttributes_m2E3B5735173BB3088B0395F6266B6BCFFF7D6090 (void);
extern void XmlNodeConverter_CanConvert_m2489573A00FFEB412ECFAB91B0CA2EA83C5D1D32 (void);
extern void XmlNodeConverter__ctor_m5EA79F6E3B52F680FC2F5DA529993033645D62C3 (void);
extern void BsonBinaryWriter__cctor_mA6BEC2932FB2A544F7527BCB1401C139932B39B2 (void);
extern void BsonReader_get_DateTimeKindHandling_m7A6ED156AAC7CD3D4343B0779E4ABD90E247333D (void);
extern void BsonReader_ReadElement_m51016B8553A73DB79F38CF122BF6292662F4D754 (void);
extern void BsonReader_Read_mFEBE910985E708117F1DFB084E839D4EC4432C70 (void);
extern void BsonReader_ReadCodeWScope_m0DA7805CE5A3D1CC83E1554C8015207538D1FF3E (void);
extern void BsonReader_ReadReference_mB9A96BD71CDF91482061F6C9074C2A83CB56E5F4 (void);
extern void BsonReader_ReadNormal_m048652A5459D2B5D052FB068C5EC478734122EBF (void);
extern void BsonReader_PopContext_m63825894764D148A8EE7451048A0761FF27BE312 (void);
extern void BsonReader_PushContext_m1AB78E117227021B3B7F29D571D58BB424B3A7EF (void);
extern void BsonReader_ReadByte_mC6BD5BEBF68FF9ECC8741786CE7A62B50CACAAE4 (void);
extern void BsonReader_ReadType_m1568AE4968EFA67EF81D622164A9AD3665E539DB (void);
extern void BsonReader_ReadBinary_m4A4F52AE9037A613C091BF93066D918478C2FECF (void);
extern void BsonReader_ReadString_m43756951ED9A74FE856F42E2CF1A1BE2AE341100 (void);
extern void BsonReader_ReadLengthString_m905B05A13227D4A2ECBFD9379E27D664A8D071AA (void);
extern void BsonReader_GetString_mB3FD61F039F2636EE8B6B4BF2CAF84A26E4DDBFC (void);
extern void BsonReader_GetLastFullCharStop_m5F12D814D034886A5A2CA2FADA4B8411D0FE90AD (void);
extern void BsonReader_BytesInSequence_mFEDFB2E86DEB1DE9F3773FE802BD6D37BBC1A36E (void);
extern void BsonReader_EnsureBuffers_mC7DD59EA3A760B60368F4A117D73F4448F706BB1 (void);
extern void BsonReader_ReadDouble_m4E7917E24274C4A8DE38DAA6DAD13B019AE75C52 (void);
extern void BsonReader_ReadInt32_m403168A3B3CA3CC5FEA5459185257E8AF00FF986 (void);
extern void BsonReader_ReadInt64_m3CD355565907B98481732EC6A91102F89CE1FF02 (void);
extern void BsonReader_ReadType_m609B4018D61DC17479EA954CAB7A4CF38F9ED691 (void);
extern void BsonReader_MovePosition_mEA8B97A5B0231E2555EB70B8D7C19D4A8D29AD2B (void);
extern void BsonReader_ReadBytes_m1CC1F7104F11C353A30134E0AA3D66B3F52938F7 (void);
extern void BsonReader__cctor_m0DD14E39C1F1B4DE1A4C13B0DEFAE390C729627F (void);
extern void ContainerContext__ctor_m1C2D964213E25068CF24D94D74E2D9D5BB60061B (void);
extern void BsonToken_set_Parent_m6FE9310A7BE7920BFBBB78D007D3326FE25861BC (void);
extern void BsonToken__ctor_m04A0C7B9070DF73C0689038C8E2593C5FC18F8AA (void);
extern void BsonObject_Add_mC5FD9CC9FC974FC4D7B10981A33291E88DB9DC79 (void);
extern void BsonObject_get_Type_m970C3BD8AC7D9844A62BE0C687DCF270E784D8ED (void);
extern void BsonObject_GetEnumerator_mD0C11E8BAC09FB62CA08FF674E3F7F23690AE14D (void);
extern void BsonObject_System_Collections_IEnumerable_GetEnumerator_m6C7320F7B08967EF6FF0D1EB4681408550A44D7C (void);
extern void BsonObject__ctor_m630E32FEFB85ABC73DA890C946486161162E0BDA (void);
extern void BsonArray_Add_m483471C0CDB8A4438E1A0274845DC6424BDB765D (void);
extern void BsonArray_get_Type_mCC68071DB9C64C070101B2633053632727FB9382 (void);
extern void BsonArray_GetEnumerator_m38E989DD847BFBA1D9AE6408890EBEC664E0729C (void);
extern void BsonArray_System_Collections_IEnumerable_GetEnumerator_m36E9AA3609FC3571D00A9C885C12A578D15432D7 (void);
extern void BsonArray__ctor_m3121DAA5995AA53B27FCE79E5F69D13131F1F9F1 (void);
extern void BsonValue__ctor_m316255047E379B29CD5D70DBCDF98BAD4DB4C695 (void);
extern void BsonValue_get_Type_m02508F5B41591FB05A329FB62FF25DBC56BC200A (void);
extern void BsonString_set_IncludeLength_m172F4ADF8CE9080328D6562647339230247B9AB3 (void);
extern void BsonString__ctor_mA28B714E7D11E7131A16CB152D171CA7A4A6BD56 (void);
extern void BsonRegex_set_Pattern_m03387AC7A329EEAA6442715EA019B93C7D4A14FF (void);
extern void BsonRegex_set_Options_m8A305E5CD6B32F3A48F6BC31203892A26FE967FC (void);
extern void BsonRegex__ctor_mA823184E2E1262D62F38DC5D1ACC130B5B0EEE99 (void);
extern void BsonRegex_get_Type_mE72E10F4DBAAC4714F12F2DC916E927A1EE72C3A (void);
extern void BsonProperty_set_Name_mBF75E093501D61ABA9B44CD595A848386002EDA0 (void);
extern void BsonProperty_set_Value_m7DAC5256E7337131CB0004255D86FBB812E5BAD8 (void);
extern void BsonProperty__ctor_mFC963BA0F736C7A11FE68BB3A4DDE63A99B3A54C (void);
extern void BsonWriter_AddValue_m1EEA7A7873B2D58AAC37EB24AB9CFB3F81DB29A5 (void);
extern void BsonWriter_AddToken_m3B3692A74D77D31F63999E7E77DD4386B74A9901 (void);
extern void BsonWriter_WriteObjectId_mF118E0F427F73A1FEAFB3853F056E9BE87524452 (void);
extern void BsonWriter_WriteRegex_m5A72DD206C8BA845F9F2B6AAC9BF61AC6480B71E (void);
extern void BsonObjectId_get_Value_mEBD0BBDDA460C3B1ECFFBD7B64C709172C7F14CD (void);
extern void BsonObjectId_set_Value_mCCFD2400AA3F5C8AD279B6270932CE681CB7073A (void);
extern void BsonObjectId__ctor_m755CAEE2BE89A3B5A751FE980FB1A333B3D603C8 (void);
static Il2CppMethodPointer s_methodPointers[2104] = 
{
	AddingNewEventArgs__ctor_m510FCE6D1FD1EE5C0C6ED30B4601B4C603B951FE,
	AddingNewEventHandler__ctor_m7B97C57FA693DD577ADB5D33E3C2FFC190C0299C,
	AddingNewEventHandler_Invoke_mC3901F2396D7A5F52E37CDF496818977C7D1C3E5,
	NotifyCollectionChangedEventHandler__ctor_mE24D13BEBB52227B52B8D923C19D4D8DD9F5BA87,
	NotifyCollectionChangedEventHandler_Invoke_m2B986CECA0F8AA4138972686389A23370B977D83,
	PropertyChangingEventArgs__ctor_m2C90654827EE107B5EF723CDE9B355822B100FAB,
	PropertyChangingEventArgs_set_PropertyName_m7D2E3D7E0FCADE10125DC4F432562C949CEEA214,
	PropertyChangingEventHandler__ctor_m68F00D44CBBE9380C9524A4A3926D56DD2BDEBCB,
	PropertyChangingEventHandler_Invoke_mB6689480ECA46314553FF17D09EE63C1D4DE1019,
	NULL,
	NULL,
	JsonConstructorAttribute__ctor_m81274E33ADDAFF8981B93FCAE6CFD31A91EBEE76,
	JsonDictionaryAttribute__ctor_m9EE4679B14BAD6C031A1F61F7A530E5F3DDBCC8A,
	JsonException__ctor_m133E4DB19926E9CB835F6BDCF47EA910A92252A1,
	JsonException__ctor_m29A7A39A820BD2EFF57EC051CB305B86C4B81FA9,
	JsonException__ctor_mF25D98C588CF67F15BECFB01FEA535FEC0117CB4,
	JsonException__ctor_mF4EA9CF06D3471F221C86E1CF375030528D0090F,
	JsonExtensionDataAttribute_get_WriteData_m91100F78741EB6CEDB4AB7354EC4085E814FE865,
	JsonExtensionDataAttribute_set_WriteData_mDF516C304D65CDAB9EDE6FFA7F69C1BFAD02F564,
	JsonExtensionDataAttribute_get_ReadData_m1ACC83CF38CEEA07080EBA48C66F2AD7FFF3C066,
	JsonExtensionDataAttribute_set_ReadData_m4C4681A75042DE8C8A35380154BE3A870AD78D1D,
	JsonExtensionDataAttribute__ctor_mE301AC470F51C0F4FDED2D842B58C785C4FCC6E1,
	JsonPosition__ctor_mB2A076189BFC7885AA7876D42D0A612A1119AC90,
	JsonPosition_CalculateLength_mF8830A4B38109D0C9EBA7F90CF1DFB70B7E08BA1,
	JsonPosition_WriteTo_mB7CDD918232B0711DCB76F31B31FEBCEDFC86659,
	JsonPosition_TypeHasIndex_m45B49E936D7FE191B91558E791402617D502516F,
	JsonPosition_BuildPath_m682C1AE222159C28A7A9ADC8C86B64505C6215D3,
	JsonPosition_FormatMessage_mBA84EAA212D7094726694CE707E2A3DADBDE8D6B,
	JsonPosition__cctor_m6006C6332033D39973517120AE0E43CC7A7D13D3,
	JsonRequiredAttribute__ctor_m3EF36A5142593C04D9EC1589DFA7F3FB4EC60FF8,
	NULL,
	NULL,
	NULL,
	JsonArrayAttribute__ctor_m298F3229AC19588107B9E3351EED94989176D37E,
	JsonContainerAttribute_get_ItemConverterType_m9404B4FD78A3B8923849B3C6C4B8BCBF83E9C361,
	JsonContainerAttribute_get_ItemConverterParameters_m46394C4C5DABE9534F121F0BCEC02848D94A1F21,
	JsonContainerAttribute__ctor_m58831F420FD3D536478054080A1F3C68B00960B4,
	JsonConverterAttribute_get_ConverterType_m75C219D874E6C5DA20645156A2E1A26FFF57E035,
	JsonConverterAttribute_get_ConverterParameters_mAF5F1D5D27BF42D6362EFDF65BEBBE407BAFD356,
	JsonObjectAttribute_get_MemberSerialization_m49450C61989A20414C231E8DFF5D980BF26C645B,
	JsonObjectAttribute_set_MemberSerialization_m241046E598051AB3A18948986659C085B905C8BF,
	JsonObjectAttribute__ctor_mB46A4065C794703262BABB64E723EB63D0709DC5,
	JsonObjectAttribute__ctor_m6C21AA934018A45AD8D680B8F8931D2558F7EC4F,
	JsonSerializerSettings_get_ReferenceLoopHandling_m6CD165186AB151BDCACD15E3AB0E10E9CCD9A4D5,
	JsonSerializerSettings_get_MissingMemberHandling_m3D682DB3B3BBEACC8F8F03909919CF0C29D41D1A,
	JsonSerializerSettings_get_ObjectCreationHandling_m323D50EB2D88E661942309B60B7CE067D0D4943F,
	JsonSerializerSettings_get_NullValueHandling_m1116B9EE497A5CB9B178CD5DF886C73C4CAD32F0,
	JsonSerializerSettings_set_NullValueHandling_mC389679010477A90BA5F811621B460004710CF16,
	JsonSerializerSettings_get_DefaultValueHandling_mD8D94E521F5739B4332D6DC4FD64A676535F6830,
	JsonSerializerSettings_get_Converters_mB7EE43E74FA48980B6C0976D7A2160B2174C8FCA,
	JsonSerializerSettings_set_Converters_mF3D958F510BA4BBC18E2D2088EF9D3FC839AFB4C,
	JsonSerializerSettings_get_PreserveReferencesHandling_mEA96432AAD3AF1E1DB77E9ADC937F3B539A14DAE,
	JsonSerializerSettings_get_TypeNameHandling_mF69B78BB41709BB8E6FAFB975955A86AAEFA9B6F,
	JsonSerializerSettings_get_MetadataPropertyHandling_m84E3BB4BD1902EE4E071249487325C2547E8D829,
	JsonSerializerSettings_get_TypeNameAssemblyFormat_m4DD9BE8274458B72AE03EDBC473D64B45ACF1B7F,
	JsonSerializerSettings_get_ConstructorHandling_mA0F1F980A1D1894748432FBB58718B426D8D3F84,
	JsonSerializerSettings_get_ContractResolver_mC94CDBCF870E73DC5E8BBF374DF22DB7B864F75A,
	JsonSerializerSettings_get_EqualityComparer_mBF43D33BBBCCF1A8BCFF1E12E47C2FBDA3FFDC6B,
	JsonSerializerSettings_get_ReferenceResolverProvider_m8525837E697E32E6B6F0D5132A6199BEEBAF217C,
	JsonSerializerSettings_get_TraceWriter_m60C8FFA8ABA33EEE8C2613FD882DEFB50DBED6FC,
	JsonSerializerSettings_get_Binder_mDC6289CA577DFB45B2127E2124DACD3DE5315295,
	JsonSerializerSettings_get_Error_m02A88351C07F1B3821B5E8A161CDE90B7EBF2C89,
	JsonSerializerSettings_get_Context_m9F472C555FB0546B2EA8E1EE75A0762768FEBC24,
	JsonSerializerSettings__cctor_m6F9BAD5867833964A7A823513DCA706761E505F0,
	JsonSerializerSettings__ctor_mE1C9A90375BA88DF7F6911F1F96AEEC6A98CD22F,
	U3CU3Ec__DisplayClass90_0__ctor_mCE905C20A996B98D4302C7421CBDBFC70B9549E4,
	JsonTextReader__ctor_m362F27D5EA7CEE0A9BDBF46FE235D8558146C10D,
	JsonTextReader_EnsureBufferNotEmpty_m05AC0A08C014C8603FAE0645369960741B939915,
	JsonTextReader_OnNewLine_m81EAB877B3ED61FD2CA6EEB7A25D981649E8CCE2,
	JsonTextReader_ParseString_mD8CC43B7912D85812D682177D6AE07121D2AC57A,
	JsonTextReader_BlockCopyChars_mE3F17F308437E7EF2F9F4FAAFAFC07052BE5FEB1,
	JsonTextReader_ShiftBufferIfNeeded_mD95108D35B37F15DEFE4279E975DE93C324762FC,
	JsonTextReader_ReadData_m6F95C344F7FA0C2CF2590AB617B6F26730594F21,
	JsonTextReader_ReadData_mFCC7C912D4C854B4222596EB5FCB4D91571C4623,
	JsonTextReader_EnsureChars_m7CABC04368BBB274FD5A8774DFEE5AF9202E0607,
	JsonTextReader_ReadChars_m081990A295318CD8C1DB29994465F9CF06B8FDCA,
	JsonTextReader_Read_m2D0F271F9202CFCC311179AF88939ACC706CF687,
	JsonTextReader_ReadAsInt32_mC30FCCB22254DAA22517ACBF44886B7ACE34FB78,
	JsonTextReader_ReadAsDateTime_m5F2DFEDDA05AF667EF9ED063A58963FE81B693C1,
	JsonTextReader_ReadAsString_m72C5BEE030026DEBF94A937CE4186D46AE5B89C8,
	JsonTextReader_ReadAsBytes_m67EDC2DFD294CC399281DC493A912AE02D8C2215,
	JsonTextReader_ReadStringValue_m52DD00E9F1C25D4855D0023D889877D2F46D8446,
	JsonTextReader_CreateUnexpectedCharacterException_m0080C6FB924D41B32A49FE1FF6723004770F81DA,
	JsonTextReader_ReadAsBoolean_m9778868B591339941F1826A947F088BF41396956,
	JsonTextReader_ProcessValueComma_mC7F07BBCAA5B3D96F7A98AB3416DECA99C76242D,
	JsonTextReader_ReadNumberValue_mEA7EBB1B9A7673A62E976573D0FD11AF3A4C49FA,
	JsonTextReader_ReadAsDateTimeOffset_mC06985DFBA420610B3C5D1E56D3C69040E819A6D,
	JsonTextReader_ReadAsDecimal_m69E52ADD0F4C82E8395A5535B0386C2849AAE096,
	JsonTextReader_ReadAsDouble_mF6C1A2CA31BAE2DB5F2A8A1E66148F694A1478A0,
	JsonTextReader_HandleNull_m29E36B9003D6A5A1229326201A5AAA7A84BBE17A,
	JsonTextReader_ReadFinished_m9FB34AB2BA71C2896F2425A7C2B651A2FE15F354,
	JsonTextReader_ReadNullChar_mED59672A36E9DF9B725D24CF508606ABB54CAFA6,
	JsonTextReader_EnsureBuffer_mD5DD845C1E19BAA45D9ED3C7BC488920D1933004,
	JsonTextReader_ReadStringIntoBuffer_m22539A750E1BCDC2E4F23D60A3832B5AC34705C4,
	JsonTextReader_WriteCharToBuffer_mA1684E95F08F9D0EB2A64C37BEE05D40F016CE58,
	JsonTextReader_ParseUnicode_mBB40787A8BC433976B8E9C9C645DB346972AE0C3,
	JsonTextReader_ReadNumberIntoBuffer_m696A36BDB72CA515C00A2B98A879DFAD20046A7A,
	JsonTextReader_ClearRecentString_mA9AE4B395EF4019B6E0307B2C14873C0E9A45E84,
	JsonTextReader_ParsePostValue_mE7D1220A4F6336F791B7DADD61C7A686CDA4EA93,
	JsonTextReader_ParseObject_mC928FB614717EA429B2E56B1ECE16DBEF0462C0D,
	JsonTextReader_ParseProperty_m884E9FD46B7F7447F767D11E0DB5105C6320D153,
	JsonTextReader_ValidIdentifierChar_m556A407C4A508D28AD593498F1D4EFEB06ACF063,
	JsonTextReader_ParseUnquotedProperty_m27D01F8849EF755498630A3ED41AB622D3AF732A,
	JsonTextReader_ParseValue_mDED4C55A0064009E981474138317B8D7CA778E81,
	JsonTextReader_ProcessLineFeed_m3E201A9963B7A588FB1BFF4B8B0D3AA145E0002B,
	JsonTextReader_ProcessCarriageReturn_m9BEFD304E778395A07F6238F1B2A47AE5B07C12F,
	JsonTextReader_EatWhitespace_mF9273F6BD64E11A40F11073147E45F4DEAA78172,
	JsonTextReader_ParseConstructor_m3B027AEEF6FD5790100D8F094046DC1D6EE5A677,
	JsonTextReader_ParseNumber_mDB4BDACD7DFFBE6F96E723D9491438282FD703E4,
	JsonTextReader_ParseComment_mBA16BDED23A3AE70DDF01C0DF209FC829BE0D406,
	JsonTextReader_EndComment_m86056041BA2E6586F053E32671208B6A7D3A807E,
	JsonTextReader_MatchValue_m62FC383608CF5AB98A45298CCEB8D2AE6FE82BAA,
	JsonTextReader_MatchValueWithTrailingSeparator_m2FF58D79A9DECB4E8A525FEB81764592EE375AA5,
	JsonTextReader_IsSeparator_m1492A54F694AA7C0E377512461A7D62C5E01BB79,
	JsonTextReader_ParseTrue_m680ED3A5A15B4AAA22B378E92B3D489F4696A3F4,
	JsonTextReader_ParseNull_m919795B4C72BE47235F01092E85A2ACFAB37D727,
	JsonTextReader_ParseUndefined_mE51CFE534A88607500322D701E3FB32BA047BF26,
	JsonTextReader_ParseFalse_m0E30E5B9698EBA3749591A9EF0AB71DFD93CB8B0,
	JsonTextReader_ParseNumberNegativeInfinity_m66DE18B2677FAFE1F6BC99D4A0E9FBB89F47F3A1,
	JsonTextReader_ParseNumberPositiveInfinity_mD0CE3A7D6489B26B0E2D720D0D643D092F8D3BE3,
	JsonTextReader_ParseNumberNaN_m2E1E654009F175EE501616C680C9E3998DC5211F,
	JsonTextReader_Close_mC616AC4E6EBF4B48D72CABD3CC03B2C32F3FA53F,
	JsonTextReader_HasLineInfo_m6B848FA0A4FB6405B898D680136A281FB69FC37F,
	JsonTextReader_get_LineNumber_mEE18A710D67FDA3F41C164E43FF8ECA880B942B9,
	JsonTextReader_get_LinePosition_m867272C6F2107F121486D302ADCE9A6A9CA6A354,
	JsonPropertyAttribute_get_ItemConverterType_m40009062EA3E6CB8F48F87883D49F31D92272BE4,
	JsonPropertyAttribute_get_ItemConverterParameters_mB826BA3BB609B554C2A14322F87FABE38AA98B7C,
	JsonPropertyAttribute_get_PropertyName_m81E5C6785332B6EFABFA1C12BB999BA21AF86705,
	JsonPropertyAttribute_set_PropertyName_m85DD25DB8B7E955976936D0F602507546C3B5EF1,
	JsonPropertyAttribute__ctor_m11B49CBBC6572BCA06E7178DA6AD1CF0ED4312A6,
	JsonPropertyAttribute__ctor_mE1DE1C04A1260B735B18BC6BD2ED080D545A73F2,
	JsonIgnoreAttribute__ctor_m26883FC7D96DBF3502845CD154EB5A0BF2D694A0,
	JsonTextWriter_get_Base64Encoder_mF34350F6BC7AD423AC0118B9F7A0C77090D2E898,
	JsonTextWriter_get_QuoteChar_m91809CAE257879A5BE17410F34F8F1DF634E317C,
	JsonTextWriter__ctor_mE77A9BD1789BB07C3B49A6EF74857D2225AA99B3,
	JsonTextWriter_Close_m97E8F3E6BAFBC81813B1D36921909043F64852FD,
	JsonTextWriter_WriteStartObject_m2091B80BD53D423E048BCFCF98E59F1EB45A8F52,
	JsonTextWriter_WriteStartArray_m44C6A3BFD6CAA3B28998D46C663871A03BEAC9ED,
	JsonTextWriter_WriteStartConstructor_m02D432E9440B0796598DFDEDFC60B835BD6FDE60,
	JsonTextWriter_WriteEnd_m5E5129364D602FEB37AE1CD6E006B1E5AE259073,
	JsonTextWriter_WritePropertyName_m144298F68C24B39EED3095D0A03B12CC9E44D075,
	JsonTextWriter_WritePropertyName_m1D23E633FE73A7D41E7E01A1C0DBCDFE9014798E,
	JsonTextWriter_OnStringEscapeHandlingChanged_m0C2F1A9EF35001E02B632A5ACF32FFB08976B4EB,
	JsonTextWriter_UpdateCharEscapeFlags_m76F3323EBE942834F35EEE6C8CE4B3EF200F7339,
	JsonTextWriter_WriteIndent_m1820C5E25B26C9C1334C7386A312FA2D09A2EC41,
	JsonTextWriter_WriteValueDelimiter_mECB7BD63E6DA0A917763457301983583BC7E4A6B,
	JsonTextWriter_WriteIndentSpace_m7D81DB5BD40780A7D7B32D0C50FA93D71DDE3A5F,
	JsonTextWriter_WriteValueInternal_m007617D83A26A85DF1B0D5D49A5965AFA74A733A,
	JsonTextWriter_WriteValue_m07E724EAEA49E87880C935666D29CC6C90718CB5,
	JsonTextWriter_WriteNull_m512467D0B45E9C5C7AFF074B7E7950DBF45A797B,
	JsonTextWriter_WriteUndefined_mB011470E4E47F77A707326600CB7CCC323C95221,
	JsonTextWriter_WriteRaw_mE0CADD22F91F09C55E848F95A53E8ADBC49F6491,
	JsonTextWriter_WriteValue_mB96615CDC3F0349C37A8576EA93EA451029DF2CB,
	JsonTextWriter_WriteEscapedString_m59C0CA22C69CE85B67A726CDDDC7DD0B371604EE,
	JsonTextWriter_WriteValue_m19ED749B8CD92901131D44303F03B5595EA3FF48,
	JsonTextWriter_WriteValue_m32656623E229E3B70409CDE4F8CA599C2B5D409A,
	JsonTextWriter_WriteValue_mDA381FF7CB3C486E036898A04BBF451911BF3635,
	JsonTextWriter_WriteValue_mD792F636E8576341BD72573BEC965CEB75DB2D10,
	JsonTextWriter_WriteValue_mEE631B6D1134A0F181854F6A4A309FCD6C5024A2,
	JsonTextWriter_WriteValue_mFFE8CC28350BD5C8E0EB6B72D6ADF5B295B1C3FD,
	JsonTextWriter_WriteValue_mD0A181C434E6C939893742AD91ADBB6F66F21C89,
	JsonTextWriter_WriteValue_m1A4B9C0C4047D178A9AE98A0FDBEF5509C18D16F,
	JsonTextWriter_WriteValue_m6D117A83A514CF735917C8D8EA02767215A2C31F,
	JsonTextWriter_WriteValue_m1A72D23742106519F15C41F00E80E656126E33C1,
	JsonTextWriter_WriteValue_m3064DFF38E751342F76CAF5D6BC43AFB48C8A2AE,
	JsonTextWriter_WriteValue_mC71FA19FE4412DAECCBFCC93401FF2A9ED3A39E0,
	JsonTextWriter_WriteValue_mC6A274C48CB4E4DE6949FD4B7C069015B07AF029,
	JsonTextWriter_WriteValue_mB4979AEAD4B39799FF8F2398C682D7E6F52DEA37,
	JsonTextWriter_WriteValue_m972E57EEA59DC2FE14962A09033AAFE79798E15D,
	JsonTextWriter_WriteValue_mBB63E6C88C107CA0DFA15C38FF9B0E9EA983B9D7,
	JsonTextWriter_WriteValue_m047FD7F7506057CCFD0D4277D0AAC17FF6D731FE,
	JsonTextWriter_WriteValue_m526DF2337FD24DC9CBCF68EC49FD64D98DF46CB4,
	JsonTextWriter_WriteValue_m2CCDE2AE295DCDC61E236A0305A9B808EE1231C8,
	JsonTextWriter_WriteValue_mBD71AC56E20A004039F796B4314D44C3EE3A0FD2,
	JsonTextWriter_WriteValue_mAE3EDADA20FF737C97C2E2924C432D0997DB1D75,
	JsonTextWriter_WriteComment_m01B628ED4A24EDE3C3B44E1CA9BF8B041AA95573,
	JsonTextWriter_EnsureWriteBuffer_mF1019CFAE05467B551055C56E322432839A4387B,
	JsonTextWriter_WriteIntegerValue_m68840DA4197A6A3682D23CCB9FEA0CA8007DB11C,
	JsonTextWriter_WriteIntegerValue_m07886B26B2AB73D0D95665C7B3E46F8D8C39DCA2,
	JsonWriterException_set_Path_m2FAFF535EF2E8E2703EEFF13915ED3070DC8F12D,
	JsonWriterException__ctor_mBE0249771B368C41A4D6CA26783BEA3B82A61455,
	JsonWriterException__ctor_m29EA8351C06D3CE9DA690046CB1C26B73484CB06,
	JsonWriterException__ctor_mB31AEAEEB3499DF61851AD86E2410003B81B6273,
	JsonWriterException_Create_mA440D59D85200435C7AFC40CDEF5773888D0F94B,
	JsonWriterException_Create_m5D4C3D2C757C0B94CBC900DF6C0737B11F53AD08,
	JsonReaderException_set_LineNumber_mB48204B6654DA78CE8DCA4BE79C965DB8547F8EE,
	JsonReaderException_set_LinePosition_m558A365BB2CCAFF08C6BEEC79855265BDCD92A79,
	JsonReaderException_set_Path_m876250F8349A55934456E05AF899C2D4B0C0AF2E,
	JsonReaderException__ctor_m2DA219E5A21E8B5C4206D282F6EC71ADEBE12838,
	JsonReaderException__ctor_m87DA30F85DD461C0CAFF30C0A1214258A9E7B8F1,
	JsonReaderException__ctor_m9AA5BE3367459D3692F7268BAF96D8D849052C85,
	JsonReaderException_Create_mE1CBC0A12B559F606A85F2450C4400676ABD6529,
	JsonReaderException_Create_m14FCC8DDF970329BA49DB00C272EB341684015A9,
	JsonReaderException_Create_m13B3A6E229E457084CCCC5EE8BF637E2BCA729A0,
	NULL,
	NULL,
	NULL,
	JsonConverter_get_CanRead_m2D457A04D24C35CBA381B6D6D857057A464A916B,
	JsonConverter_get_CanWrite_m7DEE81107B47B2A2A34A927DCA45439FC8C59A4A,
	JsonConverter__ctor_m47F59D2FF8CFBA449BDFBA405BDEEF6361139DAB,
	JsonConverterCollection__ctor_m0C47B6863A198C76E6C4F4D3415E3C426FACE042,
	JsonReader_get_CurrentState_m2B6FA669883053CDABF108A980F808CDC9F13699,
	JsonReader_get_CloseInput_m5D28FC12BD22B62AA1E493BC7A77402F1AA26DE4,
	JsonReader_set_CloseInput_m084FA36AC838A9D3CABFF315691DACAFC4CF16BC,
	JsonReader_get_SupportMultipleContent_m28769E9E99495F868A23A8F6BDF5CB90D9B792B6,
	JsonReader_set_SupportMultipleContent_mA063CFEB2CA56DD9681BF370B471F3C52918CC0C,
	JsonReader_get_DateTimeZoneHandling_m54980577E0FAC72C255619A8971816C4DE07AAA4,
	JsonReader_set_DateTimeZoneHandling_mB7165954217060B05EE3A640407A22FE6431CD0E,
	JsonReader_get_DateParseHandling_m30B55C240DFBB01311DB973D35B62C2A138D4DC5,
	JsonReader_set_DateParseHandling_mEE7359F16431F3997CF22EA9383A6FB6CBF33717,
	JsonReader_get_FloatParseHandling_m6A522BBC3B7FBE3A21C1CC89B5AFB35CEA955826,
	JsonReader_set_FloatParseHandling_m59278EBB6D40F8F11639AEA5476D438F5153F9FF,
	JsonReader_get_DateFormatString_mBEDE1FE52D98B87A039F321055027AC1FC779071,
	JsonReader_set_DateFormatString_m7B16FCCF01DADBE34D5371BBD3820DFED84B283C,
	JsonReader_get_MaxDepth_m36C1AC4E11FA1D37C477B2FEAF26133AFE0BB56E,
	JsonReader_set_MaxDepth_m03E983D915FC443AACAB411E630AC5A932B992C2,
	JsonReader_get_TokenType_mA7EDF1A1C45013F43C53F444F2E340E97BB648AC,
	JsonReader_get_Value_m97DBC2E327AAED57AABCADA0F01CD1A3974BB66F,
	JsonReader_get_ValueType_m2E0A97E557D825594A2801F189F3C545A3B5009F,
	JsonReader_get_Depth_m81ED0633EC9D3E46ED3DC95FADBBF09C1F495DEF,
	JsonReader_get_Path_m7869ECCEA1C5FE7ABF249472EF75BAAAD2F5EF09,
	JsonReader_get_Culture_m99A1BB0CC4F4F6E13A243DBF53132881DE58F7E2,
	JsonReader_set_Culture_m32AD6F7F73305D077841E603808BE4B9DBEDAD13,
	JsonReader_GetPosition_mE60B167F7C9B4F39E14DEA98613049443F3C1968,
	JsonReader__ctor_m1C65CDB90A8B108668938BBE17329FEC76D62C27,
	JsonReader_Push_mF0F4F11224139B36E63CF369F8D0043C5FEC7759,
	JsonReader_Pop_m8FC04F9ED04088581ACC3BD9BF97595E9337608B,
	JsonReader_Peek_m55B105CCB8DCA5378EA492FF96BC3ED4F4AFD5F3,
	NULL,
	JsonReader_ReadAsInt32_mC4899D463063847ADA81A7E132F224DD5A56726C,
	JsonReader_ReadInt32String_mC88B484FFAD09A2650BC9080CA32FB6E4EF387F2,
	JsonReader_ReadAsString_mB3980B052533C0CA4C8DCE9FE4E30F761B49366B,
	JsonReader_ReadAsBytes_mDD4F4FF1027E21622EE70DC557A4186C13AC6E68,
	JsonReader_ReadArrayIntoByteArray_mB00AD02B342C511B5BB8BAB5133BE3B153540682,
	JsonReader_ReadAsDouble_m84357B2560C4B03E2362D8B5D953C7336473F39D,
	JsonReader_ReadDoubleString_m324284E6D595C592176860D2B67B053713664F0E,
	JsonReader_ReadAsBoolean_mF52C093C99856A31D54A0F33927F5A6969AB35D6,
	JsonReader_ReadBooleanString_mFFA51C5DFAFDEC91AADCCB70F0EA03BA7A3C81B2,
	JsonReader_ReadAsDecimal_mF2351F836B051FC679349DFD2C5423541FBB8146,
	JsonReader_ReadDecimalString_mF85AE314BF27C69E910E0BF7BE2BDA25FCEEF686,
	JsonReader_ReadAsDateTime_m00C1F7D83FFB7708D511A1767282C64460AD7D6D,
	JsonReader_ReadDateTimeString_m4FF3EBFF0F0493D881BACF474BB4E23B8EAF39C4,
	JsonReader_ReadAsDateTimeOffset_m4EACD91C4FC30B376CC88A87DC808766D4B5E398,
	JsonReader_ReadDateTimeOffsetString_mA98E55BB14501C2528F2170F8FE5F3007B0EE9B3,
	JsonReader_ReaderReadAndAssert_m8656D7D7DCB4B6C7CC0FA48CFD1DEC0DB38B9C4A,
	JsonReader_CreateUnexpectedEndException_m990A0BF68A99C9BEF63C7B79654C12699612790A,
	JsonReader_ReadIntoWrappedTypeObject_mAF0B8C23E64E3D2F2DC68A6D99979234CFBDB121,
	JsonReader_Skip_m4D39178134B44B889EFE1C5D2181BB69CD0A4134,
	JsonReader_SetToken_m9CB3201AB80BD12AF5490EE38B0AB1E667D5757B,
	JsonReader_SetToken_m57B521A8D559EBEC2CF1FED3001068F4F897FEFF,
	JsonReader_SetToken_m53C7B4EA9EC9F6D97E7CAC00F96DE6393EFEADEC,
	JsonReader_SetPostValueState_m72D637D843644ACBDF948EC84FCB77510B7D87DB,
	JsonReader_UpdateScopeWithFinishedValue_m47A4AC48B061E266CEC273759B2551CEF0718498,
	JsonReader_ValidateEnd_mE6C8BAFA18ACFD014745A9CB8B294E07A3F2504E,
	JsonReader_SetStateBasedOnCurrent_m6D87B324B2C242175441D53056B94620AF006C70,
	JsonReader_SetFinished_mAE30671BDCD13311CB2C99684735E435A57F288E,
	JsonReader_GetTypeForCloseToken_m184FEDCE4797DB95600F26BAC386E11C5D977470,
	JsonReader_System_IDisposable_Dispose_m9E1776C6BEFEB70EBBD9F05BA6729631C9B027EA,
	JsonReader_Dispose_mA14E3FF455B3E5FE5226C2950CCEB70800707140,
	JsonReader_Close_m68AD33DBE5DA0990DB82A21F3AB2D1F91AE49F84,
	JsonReader_ReadAndAssert_m8E4307CFC38DA6E98E818C3058CCE2E385DC9EB7,
	JsonReader_ReadAndMoveToContent_mFE15149891478B519F84C2CE34BF0138F7903E7A,
	JsonReader_MoveToContent_mFDA4EC684017D33858E3077205F8DFFDFDC38B24,
	JsonReader_GetContentToken_m026EA65DC32E069C193963D9A2DB3829F533CEEA,
	JsonConvert_get_DefaultSettings_m9E0FA2F335896BD62E6CD277295E7FF580667E4B,
	JsonConvert_set_DefaultSettings_m52DE770DC408DFC48DE586C160F600E083EBBFB4,
	JsonConvert__cctor_m82A9C80D58C2BE02D84B15E7579EFE9B4FAB775D,
	JsonConvert_GetDefaultSettings_m23ED04E6CE992D1AD2BB1050800D27B683F4602F,
	JsonConvert_ToString_m6A0035B3F2C451C32105F1B38813BB735A707674,
	JsonConvert_ToString_m3199783FDBDC96A4C8F8253599AF07844E8E7CAF,
	JsonConvert_ToString_m98BCC2D08092BBB8A22EEAF13799ABF02B15A10A,
	JsonConvert_EnsureFloatFormat_m2C1E5BF2FFC0A5B04975B715566B0261AA4A1E0C,
	JsonConvert_ToString_mD9387D99174D55C8B40FA8A87DA8B6B7E931DBE1,
	JsonConvert_EnsureDecimalPlace_m179A7A15E6046A0438E6CEFD75EB08C4F312A95E,
	JsonConvert_EnsureDecimalPlace_mDE47DC52767B69F67A67CE2846F05E6AC278AE06,
	JsonConvert_ToString_m087E4B3454C6316E1DD7E9249ED747EA33FC8E75,
	JsonConvert_ToString_m63264635A791255A4CD3AA4FBE8498F51687FE7C,
	JsonConvert_ToString_m4CE350BC3D3CF25CF69019EA7315753D0A18832C,
	JsonConvert_ToString_m4819E7C6189AC98BA1CF66977855C838F0B9D1C2,
	JsonConvert_SerializeObject_m277670BD344964CB2A61751E5A3D62DB5B1321C7,
	JsonConvert_SerializeObject_mEAA691E5567819FD0CDFEBB98629609ADB899D25,
	JsonConvert_SerializeObject_m05270064842A4E4A8820C834DE0C58B7EE5F3E7F,
	JsonConvert_SerializeObjectInternal_m3D4E8DA93C08C5DEA01EDA779867C692AD0603F0,
	NULL,
	NULL,
	JsonConvert_DeserializeObject_m8601FB6D35A0E41575940296CCCFD27B97EA97A2,
	JsonSerializationException__ctor_m75C39E20101C53FA3DA4C053EF1D0BE447D95426,
	JsonSerializationException__ctor_m0DDDC290916A23CCCE7A780AF876F9CE5FE58E4A,
	JsonSerializationException__ctor_m8B8ED999C982B8A742576A1D96FEDFDAF3D68727,
	JsonSerializationException__ctor_m8B8DD2737A0BD15DA2119C8221ECDE622AB1F13C,
	JsonSerializationException_Create_m2CA947673DA3524AFC908CFE45478403E0B8E239,
	JsonSerializationException_Create_mB3994D6FE53F3F8140BF01F6F123A356C4217472,
	JsonSerializationException_Create_mBF3182906099773D44F9737E873A48462482E6F7,
	JsonSerializer_add_Error_m8ED9D28F2650490A91A5ECA68A3D399FE64C2C39,
	JsonSerializer_remove_Error_m971A111A77BD458681D1D49939522F81396EA486,
	JsonSerializer_set_ReferenceResolver_mF996B45BD10A050C835C14E80BD99C47F87EE0A0,
	JsonSerializer_set_Binder_mBADB914660791D1815A3971AB2BAD407240C6B14,
	JsonSerializer_get_TraceWriter_mFFADF5321CC33F189F47C05137F99E76A93E8001,
	JsonSerializer_set_TraceWriter_m802173793A3C9CA70F10DCE416600DE518283538,
	JsonSerializer_set_EqualityComparer_m6EA25C41DEE7F87A96FB1BB3C496FC047E2EF498,
	JsonSerializer_set_TypeNameHandling_m53B1E2AD1781281688AF0728F61ECD6E4800FE2B,
	JsonSerializer_set_TypeNameAssemblyFormat_mEE839C3CC76B8970E16033AF785EA27552934AA7,
	JsonSerializer_set_PreserveReferencesHandling_mF24D1DA24B14BA618ACAD87C0A899E474B469D64,
	JsonSerializer_set_ReferenceLoopHandling_m82082A93BCECEB8554123D00DD7C0DBA40DD0709,
	JsonSerializer_set_MissingMemberHandling_m6B023E04A6921DE7F2C2CFC5BF9FBCEEC0196514,
	JsonSerializer_set_NullValueHandling_m69454B6187F3C4C4EF4BC701B044A75F496BEB8E,
	JsonSerializer_set_DefaultValueHandling_m93AA18CF60212E9F8F8AE186A8DEB0D21ABF2AF6,
	JsonSerializer_get_ObjectCreationHandling_mB435C0075A4A376DDCBC5C7F6124D125CD9C82E4,
	JsonSerializer_set_ObjectCreationHandling_mDF99455B480225939D04E9B41626E0EE259EEC4A,
	JsonSerializer_set_ConstructorHandling_mA9D7108344EFC8C7559ECEC31F0D63AEB3305B61,
	JsonSerializer_get_MetadataPropertyHandling_m88BB4FA44E8D735834F4E8CF1780D818F9BFC49B,
	JsonSerializer_set_MetadataPropertyHandling_m8390531F6FC7199ABA6A72B79FFF9BC2064203C3,
	JsonSerializer_get_Converters_m8AAEAABF444A93BA1756D99F471EAED135EE6B82,
	JsonSerializer_get_ContractResolver_m247BB8F47D90CA25CEDFA1A2C23807805F9A9251,
	JsonSerializer_set_ContractResolver_m307DA90F879761FF996B41619946848F2A965820,
	JsonSerializer_get_Context_mF31AD53E1C4DE2EA56113919E3FED192867E1CA2,
	JsonSerializer_set_Context_m172446E1E6A162CFF7779F72FFC6D3BD38F277FF,
	JsonSerializer_get_Formatting_mAEB9A06613BCF0315AFCDF8A730AA5AC33C136B9,
	JsonSerializer_get_CheckAdditionalContent_m3E80D386B770A1A56CFD3FBE70D93A2C992F24B3,
	JsonSerializer_set_CheckAdditionalContent_m9681CF9A2B6E3574BBA8992D561E0B2DA4D5EF23,
	JsonSerializer_IsCheckAdditionalContentSet_mF612DA7202241577EE38C3BED5791065247FA9EF,
	JsonSerializer__ctor_m54DC16B93FE333917E76324295F64409DBB9893B,
	JsonSerializer_Create_m3604E1417C4F7E9291135E04E24401928A207C9C,
	JsonSerializer_Create_m1030D503AE4455241212085D4C07B6E878A44AFB,
	JsonSerializer_CreateDefault_m99F2F47FE07886357539CE0FD6DA037B21189B60,
	JsonSerializer_CreateDefault_mC8747AF7088F44A1E5B01C23728EBB078AAA6640,
	JsonSerializer_ApplySerializerSettings_mE4FCA4F1A7A6898CC26D1EC391DAB96EFAFD9AEB,
	JsonSerializer_Populate_m82119593CD901DEF485D2F206E9713100A28D827,
	JsonSerializer_PopulateInternal_m8910E5D45D37E181466181BA96EB12F246B163EF,
	NULL,
	JsonSerializer_Deserialize_m07E18A6BAA0AD5521D26EC348575BA6683DC4336,
	JsonSerializer_DeserializeInternal_m218BEE555BD1AB9149BAB16A323BFE0F038368AE,
	JsonSerializer_SetupReader_mD375BE873123E7A0618B493E5E074CB52F335D22,
	JsonSerializer_ResetReader_mE7617A472327D71802ED99586AA5658C2EBC588F,
	JsonSerializer_Serialize_m7E0ACFF2ABCDF5C092E2B735945B7BE7BAC3688A,
	JsonSerializer_Serialize_mE7F0CF9C2D3AD9D1B19A24F16FB151C9F63E7A2F,
	JsonSerializer_SerializeInternal_mD1606B221F27877646B9083E436131B7ABFCE491,
	JsonSerializer_GetReferenceResolver_m90799FA8F9A70E016947D3DC8C8CF64914A9005F,
	JsonSerializer_GetMatchingConverter_m451F66A0DD7A9756A8EE430738C8894008CB0BA9,
	JsonSerializer_GetMatchingConverter_m6CAFDF7FFC00014094B5759BDB1AC8081DC84C98,
	JsonSerializer_OnError_mD1114CD08F27AA680261783F975BFCDAA31858CB,
	JsonWriter_BuildStateArray_m90959622E6AD976D5C09BB64F004A4793F4BCFA2,
	JsonWriter__cctor_mC1A9523F34C1FD32CCDDFFF6958580707BF68622,
	JsonWriter_get_CloseOutput_m0A40D11FF244B8766FA5A2467C78024B6E2F4736,
	JsonWriter_set_CloseOutput_m8DCAE375AC709EF5D726F867A4997EA0DA946CB1,
	JsonWriter_get_Top_m5095DA8B798DD98BE08ACA13C1C9CD816E9CB335,
	JsonWriter_get_WriteState_m8580880783125AF9D182498B6DED5F1EB9CF1176,
	JsonWriter_get_ContainerPath_mEEB68127DE05B60631203B908206E58C653E459C,
	JsonWriter_get_Path_m1FA362FAF775A1805E15106C216419A462DF6E01,
	JsonWriter_get_Formatting_m7F7879CD7E9188A428A6E01990E70A65F5FEF81A,
	JsonWriter_set_Formatting_mCBA8160F9B05B54E46AD8057A1E7061742B0BBAE,
	JsonWriter_get_DateFormatHandling_mFA2684069267F277B7340B9738B1A9296A4EF8AE,
	JsonWriter_set_DateFormatHandling_mF67163662CA20C80D93A9345D94F608258ABB96B,
	JsonWriter_get_DateTimeZoneHandling_m70A3776234605D953796332656844492C164F799,
	JsonWriter_set_DateTimeZoneHandling_mD3C952FA405EFCDA4A8E23A4A1F3F33D9F2983F7,
	JsonWriter_get_StringEscapeHandling_m6B1688C4008ED729AF01BF5F2862E665F6F4F685,
	JsonWriter_set_StringEscapeHandling_m35E6580C223973ED8B229F3EB1370DB79C215CDA,
	JsonWriter_OnStringEscapeHandlingChanged_mCDEB5DF2806C08A0BBA19A0FB584EFD45E93F034,
	JsonWriter_get_FloatFormatHandling_mE17C10E646C9C244BCA40478DDC209C0BC59DF2F,
	JsonWriter_set_FloatFormatHandling_m26A6319C489A9C6F3C829E88C75851A6D260463C,
	JsonWriter_get_DateFormatString_m4E28421DC0EEE6A22B612788F0B1E5576FDB7865,
	JsonWriter_set_DateFormatString_m6563F6EF134A68FD75F2B8D40EE72E78286B6B33,
	JsonWriter_get_Culture_m8CB4EFB8986973B64D1C8D5A353D8A40447B5586,
	JsonWriter_set_Culture_m46C18DF75D3CB2D3001F41638A397D002F5970A0,
	JsonWriter__ctor_m5B0E4DCEC60E5158D71DDF7768E0FC5B09573739,
	JsonWriter_UpdateScopeWithFinishedValue_m02E1BED60FEDCFE33EF46351BF6832B80D516804,
	JsonWriter_Push_m4EFEEA3F2CBF5A324FDDE093A23CC060AC14BEBF,
	JsonWriter_Pop_mE6F01AEE8B02F6D4F6E6871A084F437B7AD5DC41,
	JsonWriter_Peek_m6B35985FC269A47082EC69A78467FBC8D21005FB,
	JsonWriter_Close_m550789FF5D483AA86A99B5301B375F5BFB08C523,
	JsonWriter_WriteStartObject_m327A0474DB3032162614C57B6154773EA900C153,
	JsonWriter_WriteEndObject_mDAD19DEDAE92A5585F9D5B5E87220404DE54F5A9,
	JsonWriter_WriteStartArray_mE2EDFEE60214B989C151DCD66C2B3219D927C75A,
	JsonWriter_WriteEndArray_m25923F05A530E148CF36E758C2AA1B85790277A5,
	JsonWriter_WriteStartConstructor_m07D1C7D6352A67ACD8BDC166DC4CB1733650FEC6,
	JsonWriter_WriteEndConstructor_mBC686C3FC4C2E47F75ED957F796E829D67DA51B6,
	JsonWriter_WritePropertyName_mD805FBFFF2D43C2EC1E23A59C0102E5244CD537F,
	JsonWriter_WritePropertyName_m105D336D8CF072757F22DEAF8966E8F7609CB4A5,
	JsonWriter_WriteEnd_m00646FA11D6F628BF200BF597022066D4A5D3057,
	JsonWriter_WriteToken_m25988821E63EE4B5B81E05AE73A54B1F9CC5C0BF,
	JsonWriter_WriteToken_mA568EC81EAB48E7C67BFDBFC19CDF2B5A3809EB7,
	JsonWriter_WriteToken_m5D8E7567C45CFC13D400F6C9984CE27A5A85529A,
	JsonWriter_WriteToken_m07632D47EA9AA93A60110178035B381097A5BEAB,
	JsonWriter_WriteConstructorDate_m4AB0CBA9A033BC6B9E1467B4667ABDF58C870334,
	JsonWriter_WriteEnd_m4F23470A732AD694DD8F306BC81447900AEC3A5D,
	JsonWriter_AutoCompleteAll_m1C1C30C811246A31918505170B65B56D34F0EFF1,
	JsonWriter_GetCloseTokenForType_mC47C8D67ECD8D5A166A53B79EAC723C6FF89E07E,
	JsonWriter_AutoCompleteClose_m981636CF5D983B493A72D7CE01A0131941FA71FF,
	JsonWriter_WriteEnd_m36FB4C43EE0F01066B13019046E5EECF96C902B8,
	JsonWriter_WriteIndent_m723F066F8DF7EC046647A9355EC6C43FF31F2C9C,
	JsonWriter_WriteValueDelimiter_mE0CD244FB7D55B3ABFAA713CE010D919714572EC,
	JsonWriter_WriteIndentSpace_m875052C9E9A127EDDFC2E882332D3F67CB2C000D,
	JsonWriter_AutoComplete_mAF922A2E6BBA1D4B1C444978AC43F11185A3159A,
	JsonWriter_WriteNull_m2BF712564701AA53BAC6BD922677FF7B0B2310BC,
	JsonWriter_WriteUndefined_m507BF49F988B94F07230A9B502D7E430E1ACCF00,
	JsonWriter_WriteRaw_m63875F6A8D37055645EB0515229DBDB124E7EC69,
	JsonWriter_WriteRawValue_mE2022D2EE36E68A4EED29C087DF24AD1AFDDEC98,
	JsonWriter_WriteValue_m3ECF13257A8D1AB90301A4174D42337149BC9D0C,
	JsonWriter_WriteValue_m8E9BDBD7C29F82DCCB87B4F5D17304F5A341A5B8,
	JsonWriter_WriteValue_m94B80E8F2C3BF83E01624C8DE99D37CAEF2FE39B,
	JsonWriter_WriteValue_mEAB0A21EA57BEDF7BAD742C3F2A75EEB2E1091FE,
	JsonWriter_WriteValue_m9A6D49A850A3EA706B7143FF45BCF9DECF2A6ED5,
	JsonWriter_WriteValue_mB83A734CC93E3FA5CF86ACD51DC69F42DF7FEA54,
	JsonWriter_WriteValue_m2C9947B42490CA9AB5E8EEFB36B3E00359C472D9,
	JsonWriter_WriteValue_m9EF9755CB7576F61E081148C00169A7128903B8B,
	JsonWriter_WriteValue_mE7084E3565A5BAE07F9E553F7C9362C2022E5B44,
	JsonWriter_WriteValue_m152C46080EAA9C377E3E2E2942B16273F93A2148,
	JsonWriter_WriteValue_mD5E4500BFFBC814E26C57409026A403C71EEECCF,
	JsonWriter_WriteValue_m1AEB560BFD2393EBBE9E766263CA01E3BA2D0E6F,
	JsonWriter_WriteValue_m0E635E0E6B005CD8D434FE57FAC79655FD774C4C,
	JsonWriter_WriteValue_mBCA33B3507B7A3BBDA956DC573ACEEB6A9D359D8,
	JsonWriter_WriteValue_m0FF59870C2C94523F795BF6F7F2FFC30CE179F8A,
	JsonWriter_WriteValue_mF792BC34323AF19A7161B55838A630A9E169A132,
	JsonWriter_WriteValue_mE9DA95DC1D6DAC672C97C861B92D3CB63933EC7B,
	JsonWriter_WriteValue_mBE7C0F46DDAD5B5F4D814A322D271B8141BF0F07,
	JsonWriter_WriteValue_mE813EBE08F1D46EC3EDDAE52808D4092C8051F8A,
	JsonWriter_WriteValue_m396C679429003C9E1D2EAC178A8764F2A430CA79,
	JsonWriter_WriteValue_mBD98465F188F8AFF6D1E09D9ACB668A951595A0D,
	JsonWriter_WriteValue_m9F3D3F1C549E617D3614ADBC65BA6651CA9094FB,
	JsonWriter_WriteValue_m368B61A68FE602EDA998B244966171341CC76D68,
	JsonWriter_WriteValue_m5CD48CF6CFF10565A6B8C1956222D9AAC268E4B4,
	JsonWriter_WriteValue_mCA78094ADEC239199E0CD5CB32DD1D6FE79D14F8,
	JsonWriter_WriteValue_mA81111E38CC7610387BCC5408C6A01DA2F69AA60,
	JsonWriter_WriteValue_mF8F8CD1AB87BCCE2A234413CA9B2AA6089D01119,
	JsonWriter_WriteValue_m089D16BD443874053B786F185B509716A4E5ED29,
	JsonWriter_WriteValue_m9C38E367AC1628BBEEA4224A82A77FFFD4C02E70,
	JsonWriter_WriteValue_m1D8E946BE22A6EB81C26D1C86DD185A44533EFC7,
	JsonWriter_WriteValue_m198F30CFFDA9DDD3E013B4F8CA32249E6DAB972E,
	JsonWriter_WriteValue_m163C6BE9BFF6D90A42D82DD0DB56F71739B4E4D9,
	JsonWriter_WriteValue_mB53C4FB8D80F9955BF4D15477E1658878EE7AB82,
	JsonWriter_WriteValue_mF17E3CCA082C4BBCD25AE23F0EF0DF095BCCF350,
	JsonWriter_WriteValue_m373E77DC65D0FE001F07E3EB6616DE6C2EB06D99,
	JsonWriter_WriteValue_mE1F445EACF5FB3E7EDF70D0C89BB230D95084DAD,
	JsonWriter_WriteValue_mBEB204C502C7E1547FC955BDBF558C0D616CD65A,
	JsonWriter_WriteValue_mC96DBA6DC02ACB9D67D4524889764368DB468C2E,
	JsonWriter_WriteComment_mFA4E1316687AD7922CFFEC126169E6083FC720A6,
	JsonWriter_System_IDisposable_Dispose_m3262E18C96F7C21162CD73999E72EC49292EF7BF,
	JsonWriter_Dispose_m7467614DDEFC3204C606E3E226FC13CB200DC8E4,
	JsonWriter_WriteValue_mB5AB5699DA3AACBA2DF6F05022CCC5FF83A5A295,
	JsonWriter_CreateUnsupportedTypeException_m0E9FBD2DE2F40D54670A083D630B1A721322DACC,
	JsonWriter_InternalWriteEnd_m35638AC3696AB60EEDB46D555D84BC187002A28D,
	JsonWriter_InternalWritePropertyName_mF01F1A0A6BF0F72991479E1B77BAD3D44AA10019,
	JsonWriter_InternalWriteRaw_mC9E958B67F30CF4DCB352A590E57764B06019A89,
	JsonWriter_InternalWriteStart_m54FFFFA0251C6CE329A4395D1840B0B731A01593,
	JsonWriter_InternalWriteValue_m7E8EC6F2C19FDF8EEE42F5B33931C55AC01088F6,
	JsonWriter_InternalWriteComment_m5892E436F9662595F361C83CD432E3077B79E678,
	DateTimeParser__cctor_mF1D9E079FA091D9FF81B2D76BA26152522E84149,
	DateTimeParser_Parse_m23985D38D15F4AC3CF47CFF85341AE406A302091,
	DateTimeParser_ParseDate_mCF43EAF9D60C2DF52038EAA7C5653D59C83F9C8F,
	DateTimeParser_ParseTimeAndZoneAndWhitespace_m36C0C76A6548E07654C45414059FBD5BC95F5BBD,
	DateTimeParser_ParseTime_m9717B1406E4705AB36B56F41A8F0CAA14B1815DD,
	DateTimeParser_ParseZone_m4CEBD1A881CB114B6EB7D8050F44EA265A260642,
	DateTimeParser_Parse4Digit_mC5F981E6CD7CD915FEA858DE77A04AEC04AF4D86,
	DateTimeParser_Parse2Digit_mF1031EB57E34571DA339FB7B1AE09268293F2633,
	DateTimeParser_ParseChar_m69C950529AC35A4734A1DCA09ED004ADDDE9452F,
	Base64Encoder__ctor_m8F45560E8389A93FBEB501B8685D03E876F46C4D,
	Base64Encoder_Encode_mB7AC073FA2837C4647788A4517F6234EECC78F92,
	Base64Encoder_Flush_m0227AAAF28FFCA22AF4FA524E82FC4F2C1AD4DDD,
	Base64Encoder_WriteChars_m2402CA2720FAFFF4529F80B591E2B701877C6EE9,
	JsonTokenUtils_IsEndToken_m70717579B601F0647A66B4896AE17A48B3A89CB4,
	JsonTokenUtils_IsStartToken_m3C784E79F513290AFDC7818B2C720C795FBE500C,
	JsonTokenUtils_IsPrimitiveToken_m97DBA2150C205005AA33809D4355769E9ED1BB2B,
	PropertyNameTable__cctor_m53E82E052F144AB7D73D75947FC7F91BEEE72A28,
	PropertyNameTable__ctor_mD7F65C095AB3D9A606C5770B3469B806936A0B8A,
	PropertyNameTable_Get_mCE6D6D04D42BAB90999B694D872EFFAC2437343F,
	PropertyNameTable_Add_m6617E7236C98C124670B7405B4E231ED2C11619E,
	PropertyNameTable_AddEntry_m050639221DE407DB9CC073441F04EDF3A4371E28,
	PropertyNameTable_Grow_m76421B59F0D565DBB12F5229AA3DD3176692B662,
	PropertyNameTable_TextEquals_mACB76A809C7126ED133E05816E514FD71B09AF5B,
	Entry__ctor_m30B82FED315B825E73B0F831B78463D212352001,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ReflectionDelegateFactory__ctor_mE9385422DDFE3B3AED392895D925ABF922D1048F,
	LateBoundReflectionDelegateFactory_get_Instance_mAC844486768E223FABD4AB379F29D33997E6E747,
	LateBoundReflectionDelegateFactory_CreateParameterizedConstructor_mA59A94C702D443557FDAE92AF35A64E2DA92CF4F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LateBoundReflectionDelegateFactory__ctor_m98CE81BFFF3C34914C11A9167881DBD651793C5F,
	LateBoundReflectionDelegateFactory__cctor_m03272AF8A4FE2D11C7D2467A97EB833D8D8A4B8B,
	U3CU3Ec__DisplayClass3_0__ctor_m312F08F4C3CEBC38A926FB5F2A0C119D462FEB6F,
	U3CU3Ec__DisplayClass3_0_U3CCreateParameterizedConstructorU3Eb__0_m4F59643C7CFE3C66AC0076F949501A04920F7FF9,
	U3CU3Ec__DisplayClass3_0_U3CCreateParameterizedConstructorU3Eb__1_m14BA5C3056EE694ECC9A796EC7E3F298DCA685A2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ReflectionMember_get_MemberType_m434D9AB83992981B6F6E5E0641CEA721A590F664,
	ReflectionMember_set_MemberType_m910CD710E590680C35E3663BDC07074A691C4CF3,
	ReflectionMember_get_Getter_mED1E4624E99FB9D6DDF293693EFFBF3DA290DE83,
	ReflectionMember_set_Getter_m75ECB835C1A8B2DD3E802D1631B32D3B6CA75187,
	ReflectionMember_set_Setter_m7B146640EE159DD3226E47E5383DFD8AD5A1E715,
	ReflectionMember__ctor_m8ACC777354B64FC9082AAA4783F0C89B885CC62F,
	ReflectionObject_get_Creator_mFE215FC9C57548498D71EE5B13DB60FBED70074C,
	ReflectionObject_set_Creator_m9745F5798D9C0083D0B1896DA7DC4A78DDCA3A41,
	ReflectionObject_get_Members_m11F407DD2A6380C05E18A03E8425A5175A0D3FD8,
	ReflectionObject_set_Members_mF6FBE268BEAE55D190EDA784E103D971551C865F,
	ReflectionObject__ctor_mFF8A70105240594D877E597200A779052AD85924,
	ReflectionObject_GetValue_m16C7AF8473ED05865B899DCE08826438E9381D10,
	ReflectionObject_GetType_mB739C18B776ADC78FCAA3513AE4D4EE73B91ECDC,
	ReflectionObject_Create_m789093E9CCCC488B191391FEC5448DCCFF4B05C6,
	ReflectionObject_Create_mE6A4EC38A8DFA1A7E71C2BB096B94BD78F61B2A8,
	U3CU3Ec__DisplayClass13_0__ctor_m5172B2BA78B3DB828BDA76D7FB203BC904D49536,
	U3CU3Ec__DisplayClass13_0_U3CCreateU3Eb__0_m7DA59DB878C30CD76A014AF724FCE01ED14A8393,
	U3CU3Ec__DisplayClass13_1__ctor_m83923C0C11252BCFAC25A6E65506D9A0BB814E8A,
	U3CU3Ec__DisplayClass13_1_U3CCreateU3Eb__1_m9A6CFE9FE0F43F312AD1B20AF69805D51E68BBD4,
	U3CU3Ec__DisplayClass13_2__ctor_m4956288D04A83643632E6BBBFA273F37D29BC9C9,
	U3CU3Ec__DisplayClass13_2_U3CCreateU3Eb__2_m157892641A9E32E5E199D09703036328DFCF716A,
	StringReference_get_Item_mF157FD35EDF25DC3FB3291BA8A7ACA6A49791EBD,
	StringReference_get_Chars_mCAEA9DDED5058DE07529C24621E510E396B79A6B,
	StringReference_get_StartIndex_mC3DD76078312694DB7C297115073EAE930B42925,
	StringReference_get_Length_m65CF2F68237C0273F5BE4B4B0DCD4247CD940385,
	StringReference__ctor_mCAEF5A34A8FD029BA4399BDEAD6B9AB67515A5B2,
	StringReference_ToString_m14E995A62CEC0B0C1313E51D01878B015EB38EF6,
	StringReferenceExtensions_IndexOf_m8408F16214688FE8239B09858B186C1125599F83,
	StringReferenceExtensions_StartsWith_m0C5C1ED2F3842A51339DF8F300BFACFB95DC7A2E,
	StringReferenceExtensions_EndsWith_m856EC50F4E06388F85AA6897D5A919E6F454B2FC,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TypeInformation_get_Type_m66CAE341688AA88BD1598BD84C736FC0429619F1,
	TypeInformation_set_Type_m390CB6B2F90698A847053FEB731D9376798EEEAD,
	TypeInformation_get_TypeCode_mC353DCEF2D0C5A983CD7A389E67D0F1B9D4F57EA,
	TypeInformation_set_TypeCode_m9F432C648665B0C14B3C6483DAE92F317009168F,
	TypeInformation__ctor_m116F34BBC859ADB3553EEE8E1B0B6741158FB497,
	ConvertUtils_GetTypeCode_m3FF00ADCDD10F4F141BBBE4FBED910EFAB042EDE,
	ConvertUtils_GetTypeCode_m959EFBADBFB4D2ABFDE6DFEA04CEDDB5C4DB9785,
	ConvertUtils_GetTypeInformation_mA2F03C07BC76F45128DE4BC27FA3DF099D64F63F,
	ConvertUtils_IsConvertible_mBFF3508FCE496CEE88EEAAF3DD82193A5F7D443E,
	ConvertUtils_ParseTimeSpan_mF6B09E2815B7E0CADC1C35C93B58D030BA4E1202,
	ConvertUtils_CreateCastConverter_m9C8F184B0440938A61029D69F1AD9F226EBD7213,
	ConvertUtils_TryConvert_m83691EC9657B63B2672A8814B83A5DB042E8587F,
	ConvertUtils_TryConvertInternal_m9E76F6EF19E80EBF9054B01D7AD23173DDEF3912,
	ConvertUtils_ConvertOrCast_m1BAE14A07732D27166BC0121DF96D63FD8EE897B,
	ConvertUtils_EnsureTypeAssignable_m0F13B16EB957DDA87DC4EDB251DA44E38D20C06D,
	ConvertUtils_GetConverter_m3DCC606FFE3B5EA9D7A2951F7F2DF6AA32F76415,
	ConvertUtils_VersionTryParse_mA4BE1D73A196161D4F95B3EBFF256D5FAFD2FB6E,
	ConvertUtils_IsInteger_mF25CD55DEF949A90AD74D6FCE946A9BA155B95CB,
	ConvertUtils_Int32TryParse_m6D81B72EF5D3E6FB6283976493619C2A900BBE95,
	ConvertUtils_Int64TryParse_mD4BA6148D0849E5EFBEC03B6EE4D2D92FAD49F52,
	ConvertUtils_TryConvertGuid_mB09F062E72F476C09A4ACB8A2387F5A9862FD4AA,
	ConvertUtils_HexTextToInt_m5350E533A781F64A2CD2CE80B0AFEFE9DF5F450C,
	ConvertUtils_HexCharToInt_mAE36477DEC362EE5C10B08550F6916FCAFB504BB,
	ConvertUtils__cctor_mCE736B50121C78004F75F17086D8966B34F641CC,
	TypeConvertKey_get_InitialType_m00D6EF528297F8AA3372C9BD42BD0F02EB64E229,
	TypeConvertKey_get_TargetType_m61EE1F103B49EAA250ED9D9E97B8A4B3D4DF2925,
	TypeConvertKey__ctor_mD8FF7E88853BA5DE5876BEE02E0F656E8FB5C736,
	TypeConvertKey_GetHashCode_m73EEFCBFC5400417D8BE4F835C852F42A20D448F,
	TypeConvertKey_Equals_m8C696A833AC5997EE1AC57B90E260679C2176069,
	TypeConvertKey_Equals_mC41E4C78130945D5E3FEFCC700F7D18E62E48BCA,
	U3CU3Ec__DisplayClass9_0__ctor_m925748B96EAAFC6F502C31EE3032DEC4A6E97146,
	U3CU3Ec__DisplayClass9_0_U3CCreateCastConverterU3Eb__0_mA083ECAB32C98214204B8E8E01B3E508DAB1D2B8,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DateTimeUtils__cctor_mB8749F73ED67F50441C60F2999411BD9234A8BA4,
	DateTimeUtils_GetUtcOffset_mA930C30B051B5F333AF1300B9E40BFC003C50A50,
	DateTimeUtils_ToSerializationMode_m91B5EE22B7ED390567F3EBA783285F6218D17AE6,
	DateTimeUtils_EnsureDateTime_mA79D09FFD516714FB3A1296F5E55780FBD5191C8,
	DateTimeUtils_SwitchToLocalTime_mDD82AB9D4F2D9F49165153702BEB445085AA57FE,
	DateTimeUtils_SwitchToUtcTime_mD7F5B3C7585927E01F6C544C56ADC21FCBFEDD80,
	DateTimeUtils_ToUniversalTicks_m2A37B7076787063F789C5D1E7BFBEFF8105EAE1A,
	DateTimeUtils_ToUniversalTicks_m0E9B1237416F7ED5ABD736831D5F7F2F4AF70E97,
	DateTimeUtils_ConvertDateTimeToJavaScriptTicks_m0D5815D6340AC9707A7FD04B27F2101845CC598F,
	DateTimeUtils_ConvertDateTimeToJavaScriptTicks_m6EF183506C0B2D94C8307100E3DEF80DB8B34859,
	DateTimeUtils_ConvertDateTimeToJavaScriptTicks_mA2ACEC25DDFC8FD1CD43D6368BDF4FB612B453B8,
	DateTimeUtils_UniversialTicksToJavaScriptTicks_m1304023C12CB633E02CB86F6F70CBAB37C2EF3E7,
	DateTimeUtils_ConvertJavaScriptTicksToDateTime_m927253156D26643004345A45B5FE76648E8F8884,
	DateTimeUtils_TryParseDateTimeIso_mF9B36B399D6CEB27F9F793080148C1A2489C3047,
	DateTimeUtils_TryParseDateTimeOffsetIso_m538094DB0A9E001B1A9CC19F0A67880DC4CDBEA4,
	DateTimeUtils_CreateDateTime_mBB5C43CD527E4D051A473CF3C99BC27A63158526,
	DateTimeUtils_TryParseDateTime_m5EB8CE9B38B3CEA7A400D7A50C1EF93FF64AD7A8,
	DateTimeUtils_TryParseDateTime_mA44DD7990B1A60C56ABF62BC1663C6AB35B41D84,
	DateTimeUtils_TryParseDateTimeOffset_m01EF0F19E09185A87FBF604688FC24549EDB0447,
	DateTimeUtils_TryParseDateTimeOffset_m9B1B360ACFE9EC20A745DDF71E5660C925ECAA72,
	DateTimeUtils_TryParseMicrosoftDate_m1FB25AB70888B42D0E886ABE60B49F513A4866AC,
	DateTimeUtils_TryParseDateTimeMicrosoft_m9D1B53C735ACC5E2348AC816E1223336774A77FA,
	DateTimeUtils_TryParseDateTimeExact_m26C44CF3F9D71D7B66D6D610ACF09415E9FA5A97,
	DateTimeUtils_TryParseDateTimeOffsetMicrosoft_mDE9494C3F5AB4DA579B89B0A806CBBBDB71F82C0,
	DateTimeUtils_TryParseDateTimeOffsetExact_m82A8B1344B99932C906AA9F484BB0C1E8D3CF136,
	DateTimeUtils_TryReadOffset_mFFDEF89F67E624EF8B2C64CD576C4BA93C03DAB9,
	DateTimeUtils_WriteDateTimeString_mC66266349F3A8A9B3F069BBE8AA7F0580333A38B,
	DateTimeUtils_WriteDateTimeString_mCDFAB4AD7F5F2A187B9F0778CFA2DF2005C6200C,
	DateTimeUtils_WriteDefaultIsoDate_m8DEE0C2EADE57D2B6EE01D57B9C7F276B6681C93,
	DateTimeUtils_CopyIntToCharArray_mAE8342C2764DE70579A9D3F1C3C4DC3D1504EC36,
	DateTimeUtils_WriteDateTimeOffset_m1346049BF9D62EB01271E853BBB9FF6F382E2F4F,
	DateTimeUtils_WriteDateTimeOffsetString_m36016656CB36E3E34222C8EFBCA4F636F663B6C1,
	DateTimeUtils_GetDateValues_m92E008CB52CBBC94701ECF185BEF2DA71AD9ED11,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	EnumUtils_InitializeEnumType_mC735DAB2D3908D7559F2D70BDF067EF983DCCBA2,
	EnumUtils_GetValues_m2DDB95CC0F0D37D258AAF60E3CFEBE79C9D108FF,
	EnumUtils_ParseEnumName_m9095DB225793F25DCBC646FA225BB2E43837FA41,
	EnumUtils_ToEnumName_mE3581769A67B663A6673F5F7AC87193893D95BD4,
	EnumUtils_ResolvedEnumName_m248674CF791510E8FF2F4A25EF4DB6C20CC6ED0C,
	EnumUtils__cctor_mA049DE3D88785736AD69EC4E30909180FF3AC6CE,
	U3CU3Ec__cctor_m242F2EC68584B153778198BB9D4F507A98798704,
	U3CU3Ec__ctor_mB8204CC34DACDED611AAD8DB38DBFE4D17601831,
	U3CU3Ec_U3CInitializeEnumTypeU3Eb__1_0_mD4BA67ECE616B826E61050E1F6CCEFA3B377CAAA,
	U3CU3Ec_U3CGetValuesU3Eb__5_0_mAB7D7BE53058F60BDF00BF6CF88D432F01D41025,
	NULL,
	NULL,
	BufferUtils_RentBuffer_m0CD2D9EE0A156A32A3E6690D59974C1F772DA4F1,
	BufferUtils_ReturnBuffer_m724A55F218F5C04FB3A427C74B5CEF4EB9B020A9,
	BufferUtils_EnsureBufferSize_m2CF4818D2839BDF81AAE5AEDBEA4772C9B2F9A18,
	JavaScriptUtils__cctor_mC942D980534291CCAA348752CCA85F50F4D71858,
	JavaScriptUtils_GetCharEscapeFlags_mFACEF9DE1403BD66866D82EEDEBAF9CA11171770,
	JavaScriptUtils_ShouldEscapeJavaScriptString_m3C5359149C97D68DEE24E6BB6B6A8C2A791EA6FC,
	JavaScriptUtils_WriteEscapedJavaScriptString_m267358C3D8B0A8EC568C3AFAF98ADE308FFCEEE1,
	JavaScriptUtils_ToEscapedJavaScriptString_m50ACC5FDF3A885F430CBF7FDAF5A048551F995B7,
	StringBuffer_get_Position_m26F00F0301A7D88C7A0E2199F1E6394C2D1AB61D,
	StringBuffer_set_Position_m68C6F0EBF858CFCF3D10A8D1D255B24ADA883263,
	StringBuffer_get_IsEmpty_m0FF70318FF5ED6D3C0E2C1BD3E5B3BDBB7C0A884,
	StringBuffer__ctor_m0B0B11963A1F7B3F240F8993C116DB8EC5ECE96C,
	StringBuffer__ctor_mFF5E0ADAA4ABD314C4524F3DC482CF9773932C5B,
	StringBuffer_Append_m77B388D5627C9D7EBB5C5848F20C396B826253AB,
	StringBuffer_Append_mCF7546C3A7CED19D47B3AF5ED2E0A2D456AF5DFE,
	StringBuffer_Clear_m6C24B7B855B0E8F12C3DC20D6191EDB81A0D65A6,
	StringBuffer_EnsureSize_mB5835295812DA385035C84550B3A9E35FC59E100,
	StringBuffer_ToString_m693AF2D7D2FCB627284D3A0D79FDA85547407D49,
	StringBuffer_ToString_mF654D1BD6CCFF512998846C7E7D57C1E6AF6B4DE,
	StringBuffer_get_InternalBuffer_m74C440CFC916B0D891EBEB6D577BFB518CF891B0,
	NULL,
	NULL,
	NULL,
	CollectionUtils_IsDictionaryType_m6E8536FFCDA481FB20EC0C4B8746028004A1BC6E,
	CollectionUtils_ResolveEnumerableCollectionConstructor_m9CAFB266C5D44BF7E674050AE7E7030362FD20B7,
	CollectionUtils_ResolveEnumerableCollectionConstructor_mC2D9D30053DFE768B4E85A883579F9B8DFD3CD15,
	NULL,
	NULL,
	NULL,
	CollectionUtils_GetDimensions_m659D13D84108B2CA459101CF67D69D272B946BEE,
	CollectionUtils_CopyFromJaggedToMultidimensionalArray_mABA6B698638107D5CDA81FD03F37A78C1DB097E5,
	CollectionUtils_JaggedArrayGetValue_m91764E8A6719C0663E7FDF506ADEFE43A4FFF909,
	CollectionUtils_ToMultidimensionalArray_m89EC870CBB5C35DA01D42D3FE5E7745B96BC5F29,
	MathUtils_IntLength_m23241F09F5280B9249FBD8D666E56C81E464919C,
	MathUtils_IntToHex_m3A390F8C758B3E156E4440239E3FF2C0FE766F63,
	MathUtils_ApproxEquals_mFEC2066308DE0316C9ED311B46207B0F50A28712,
	NULL,
	NULL,
	MiscellaneousUtils_ValueEquals_m427F9DD809F1A34E202601685246C163621C0661,
	MiscellaneousUtils_CreateArgumentOutOfRangeException_m91D696C3DB200B3480F61835CB2D4B0A601ECF0A,
	MiscellaneousUtils_ByteArrayCompare_m1DFCACC65C5035DF56AB68AD5873FA3EE26FE941,
	MiscellaneousUtils_GetPrefix_mD4530D3BF776EA4ECCFF1888BBD2ECDE9F2DC1CF,
	MiscellaneousUtils_GetLocalName_m6104B0EF1BD60B135EEC76A96AFF567D02394CC7,
	MiscellaneousUtils_GetQualifiedNameParts_mDB4115E09A5DDA5F1103D82C7B514A038301683C,
	MiscellaneousUtils_FormatValueForPrint_mA058F3DB86D4AE41E23975B3DD8BEE5E249488E4,
	ReflectionUtils__cctor_m838B9E504083D1099352011D467F39F2BF084BC2,
	ReflectionUtils_IsVirtual_mB9D39467327026209931F92BE4B90B2E980F7398,
	ReflectionUtils_GetBaseDefinition_m0A99789F9B9BC226849E61E8AFCFA3FBAA5D7AC6,
	ReflectionUtils_IsPublic_m3CE1F6A87208FBC096DF73A053DAEDF0786BD5C5,
	ReflectionUtils_GetObjectType_m979DDF9D576AB3D45DF5B009C0B8BD46763CCC17,
	ReflectionUtils_GetTypeName_m3A1B9D816D52828ACE09EA407BA362F7291BA46A,
	ReflectionUtils_RemoveAssemblyDetails_mC21C6832C5D0298E7C9AD4CC8CE95C543CE2DA37,
	ReflectionUtils_HasDefaultConstructor_mB80909BFB8B680B7B5BBAD5E85083CA71DCECC37,
	ReflectionUtils_GetDefaultConstructor_m7D201BD979CF35DA3BD18B96219C9CAD547D550E,
	ReflectionUtils_GetDefaultConstructor_m46F8A00E72216F0CC50881E61902BB24DF31A0B8,
	ReflectionUtils_IsNullable_mF1649DFA26303244B2271027980F139FF42DA8E0,
	ReflectionUtils_IsNullableType_mA710C2540434AAA2353481C15C48C3996EC69963,
	ReflectionUtils_EnsureNotNullableType_mF2B1550F38848A01AAAFDAD0755C37ACA6530ED6,
	ReflectionUtils_IsGenericDefinition_mF5420EF48A193DA2514186BE3B58F36F27B7E9C8,
	ReflectionUtils_ImplementsGenericDefinition_mA407E5B3AD8CA9F69F2D4257EDA4AC7EB8C2D4E5,
	ReflectionUtils_ImplementsGenericDefinition_m7E4E60FAB965FDE16C7151A6FCA0677845CFD6DB,
	ReflectionUtils_InheritsGenericDefinition_m472BA465A850FABA27CE0E6ADC2DC88D7317EBDA,
	ReflectionUtils_InheritsGenericDefinition_m36577D3689B74DC9CF2E4426CAE533B3393E6E06,
	ReflectionUtils_InheritsGenericDefinitionInternal_m28E863A1932987F75B56A7B75B55DE43B6911A8A,
	ReflectionUtils_GetCollectionItemType_m1CF4C2D7E62D83B5611331DAF65CEFB0D2D30FEC,
	ReflectionUtils_GetDictionaryKeyValueTypes_mA1AAA03E6E1701AA394D21A2EAD50EE6F1DDC0A0,
	ReflectionUtils_GetMemberUnderlyingType_mB8BC2C0D7728CF137E88B887AEDC8A93163470C0,
	ReflectionUtils_IsIndexedProperty_mD2FBA4A2C6BD37A0C0754112386AF8C41BD23FBE,
	ReflectionUtils_IsIndexedProperty_mC6602D9BBD42A67584EF40A29B50DB45A035A565,
	ReflectionUtils_GetMemberValue_m69C0903BA25F305FA8C4FA37938FBBA54F608D5D,
	ReflectionUtils_SetMemberValue_m8FA7938133743E996E7C6D77AE4191C38BE9FEB7,
	ReflectionUtils_CanReadMemberValue_mAEC74283BD7361EA0E00DB00DA4CA1701893E3CD,
	ReflectionUtils_CanSetMemberValue_m84DD2635E730746FEB493F99AFBB98C41090A994,
	ReflectionUtils_GetFieldsAndProperties_m5C9A9A1B4C2D81B55B9A96111F9224A5C2C298A9,
	ReflectionUtils_IsOverridenGenericMember_mED37BCC1DBDC0EFA25F650882D88626F9004003E,
	NULL,
	NULL,
	NULL,
	ReflectionUtils_GetAttributes_m64100872CC4D5F42453CED2620D0790546A34543,
	ReflectionUtils_SplitFullyQualifiedTypeName_m29849DCBF19DB45C56AAC1B81E6CED57E4962C44,
	ReflectionUtils_GetAssemblyDelimiterIndex_m4860F359D4A8A7F5AEC2C2A75904469C3A4722CD,
	ReflectionUtils_GetMemberInfoFromType_m53EA3C96A700303B68775A888758AD4898B51A73,
	ReflectionUtils_GetFields_m345556004ABC798073F71E65987A68E749DEACDE,
	ReflectionUtils_GetChildPrivateFields_mE010323462273938232947F53CEAEDD04CA0224A,
	ReflectionUtils_GetProperties_m116432A13B39F37FD078A1E749AF04822F48D117,
	ReflectionUtils_RemoveFlag_mB7DAD21EF1BDF82449F44B7BABDC5397B9272ACD,
	ReflectionUtils_GetChildPrivateProperties_mD3F6F4AFE2C2BDC0DBBFEDAAA528D1F2B3A7D96C,
	ReflectionUtils_GetDefaultValue_mA716FF1FF01AC5055296E9E00AC32CF4F244F84E,
	U3CU3Ec__cctor_m160304ED5DA653ACBC05F902E50A0A1B01BE66CB,
	U3CU3Ec__ctor_m6DDB32E97BC4E58BD446ACD831214F7E773FC500,
	U3CU3Ec_U3CGetDefaultConstructorU3Eb__10_0_mAFCDD11DC5EEFCF68C085CA3AFE61BF69DEE27A5,
	U3CU3Ec_U3CGetFieldsAndPropertiesU3Eb__29_0_mD091B1A6B829B39D2D171DF3071DF06F409DC525,
	U3CU3Ec_U3CGetMemberInfoFromTypeU3Eb__37_0_mBC0347046FB0330330D36E676B133E1650366ADF,
	U3CU3Ec_U3CGetChildPrivateFieldsU3Eb__39_0_m69B7FC84D992E23D893AB7536184466FE28F749D,
	U3CU3Ec__DisplayClass42_0__ctor_mE417B697FA318B7DFB472A733CC191B9730EE687,
	U3CU3Ec__DisplayClass42_0_U3CGetChildPrivatePropertiesU3Eb__0_m03BA1FF92F094546D609FD43C3A3F1A8DD48C300,
	U3CU3Ec__DisplayClass42_0_U3CGetChildPrivatePropertiesU3Eb__1_m4737848D73722527E7202CBD93FE35498A48D109,
	U3CU3Ec__DisplayClass42_0_U3CGetChildPrivatePropertiesU3Eb__2_mF422071163C80A910CDED5146AE8CA1F3F75231C,
	U3CU3Ec__DisplayClass43_0__ctor_m41023B92BEC51A2EF4D669A39DC0067F437A064B,
	StringUtils_FormatWith_m97587965D365EA1584A7D31B57D618E7768073E5,
	StringUtils_FormatWith_mE8641D4F1BDCF64E9876EE3B78F84D7AC377D3E9,
	StringUtils_FormatWith_m675CE2F519A21661494F14469C676E52CDCDA7C0,
	StringUtils_FormatWith_m5F7ADBFCB56B0DA5D3E4973ECC7BD8E5AB93CF9F,
	StringUtils_FormatWith_m46141A3CF7D10BDD298594ADC08661098691E0F2,
	StringUtils_CreateStringWriter_mE99F16079289F7A6BC74C0794E1C1C60C848EE76,
	StringUtils_GetLength_mFA1FEE63F31C09124678579EE94A382B458265A6,
	StringUtils_ToCharAsUnicode_m24D950B8B7F90A829F4221DE376C2624B0AAB62C,
	NULL,
	StringUtils_ToCamelCase_mC5ADD2B4C55E2E242CA489FD45F6F5C76B11E643,
	StringUtils_IsHighSurrogate_m47A5F04E84DDD8EFF680AA049FF1813C1F9CA6AE,
	StringUtils_IsLowSurrogate_mA7F79F10AB65B636D64DBEC4E0F99ED27A0753E7,
	StringUtils_StartsWith_m1DF31A6C9FE8815DE2BE508A518E6399EAFD7871,
	StringUtils_EndsWith_mD0F53711503882AAAA0040F55410297D44C2FA73,
	NULL,
	NULL,
	NULL,
	TypeExtensions_MemberType_mE0E085190B3125B1FACEDF00B26A33742AB46A4B,
	TypeExtensions_ContainsGenericParameters_mA994EDB52CD98EF1AEB724C41902F89D3D23B557,
	TypeExtensions_IsInterface_m9D102CEF96CC2E35ACBD36139B2BB7C476886235,
	TypeExtensions_IsGenericType_m7B8CC11BF92A736A0EB33F54E58513C9105A8489,
	TypeExtensions_IsGenericTypeDefinition_mB6D5CD38FC73444ED1C98E7B24843E7F3E598C9C,
	TypeExtensions_BaseType_m08B4843525643FF299FA2E7038C759A8FFDAEF8C,
	TypeExtensions_IsEnum_mE09A46765105206D627C3C9AE5E011BA8FDD1645,
	TypeExtensions_IsClass_mCA21F0211EBFB1812F432BF5C373A00D241C0B41,
	TypeExtensions_IsSealed_m32129C70002FD86655A745241FFC4E03A129D2EF,
	TypeExtensions_IsAbstract_m9A8E7821259AA6C6C79329A8070A7FD730632D4F,
	TypeExtensions_IsValueType_mD736A971C1BB5B1092EBD01BB9DF7B8D8C1C26BD,
	TypeExtensions_AssignableToTypeName_m342E81D94CA7C0E397CDD5EBF5C45DD8F6ED0C55,
	TypeExtensions_AssignableToTypeName_mDF071FAA34EC2E755656AA6D880EE38A98379C3E,
	TypeExtensions_ImplementInterface_mB96ABA9168598F26343B1AEE2097D30047BE661B,
	ValidationUtils_ArgumentNotNull_mC7EBE963D14FFCC7B90B08B403FF584EC520C888,
	PreserveAttribute__ctor_m667A671468454D88FA6ABD55B7207DA1A5567771,
	DiagnosticsTraceWriter_get_LevelFilter_mECCAA3CA2687CEB9221E985DA157EBD30D7906FC,
	DiagnosticsTraceWriter_GetTraceEventType_mAD47DFF1D82147C1A78CDA1210CCDFAA155B0C64,
	DiagnosticsTraceWriter_Trace_m78B127B8B8CB95B78E12F88AFE6EB13A2FECB750,
	DiagnosticsTraceWriter__ctor_m55C1B81D42678399E0ADE85ED0AD268B3132EA2D,
	NULL,
	NULL,
	JsonContainerContract_get_ItemContract_mA413352FBBCD18D7D315839065DB02B665FD2D0D,
	JsonContainerContract_set_ItemContract_m7204ECB7E67EBA3E93F7E1165E1D45572F9F33E9,
	JsonContainerContract_get_FinalItemContract_m144268C4A088DF790C3A2869DEFFAD75C6C964CA,
	JsonContainerContract_get_ItemConverter_m3E87FE39B3957C1130F97FA76E5A3A0C1A47C1C3,
	JsonContainerContract_set_ItemConverter_mFBF42EA2582D94C174CFAC5772A19B59B7EDEDE7,
	JsonContainerContract_get_ItemIsReference_m2EE85CA5003233F533A01FF2F306644D2EC89EC0,
	JsonContainerContract_set_ItemIsReference_m3A84BECA20126FB918AC422CB0F08172CE514605,
	JsonContainerContract_get_ItemReferenceLoopHandling_mEF16C22A487C11211F145332D61F61CA755E0170,
	JsonContainerContract_set_ItemReferenceLoopHandling_mCAAF9FB0B6D30A9F2F08126F89D5B086FB0B8022,
	JsonContainerContract_get_ItemTypeNameHandling_mB0B76F5A21FAC4F9F9472B2B33F48DB689C5349C,
	JsonContainerContract_set_ItemTypeNameHandling_mF1B16C6B7FE2E5DB0B3068F6284BDA286B27A36B,
	JsonContainerContract__ctor_mF5218A84C856DE38D68CD4B4334A0CFA85A406FC,
	MemoryTraceWriter_get_LevelFilter_m1C0F2C4C8915E9B2631C3F634D3A59CF759BD757,
	MemoryTraceWriter_set_LevelFilter_mAE5693E523F92AB5380A78294E65F9A4AE150F73,
	MemoryTraceWriter__ctor_m5B48945B79FDB9E078466FC80F5C902CF693FBA7,
	MemoryTraceWriter_Trace_m7F814C717987F6B5B0681B4C5A01E7D8817824DC,
	MemoryTraceWriter_ToString_mAC35A155DEE5E8649F0384239981D553C67BF8AD,
	ReflectionAttributeProvider__ctor_mB3C1BFACEB11C9154E1B305F467E9ABDACB4E2EF,
	TraceJsonReader__ctor_m06DE37B905DD72B8EB3BC54B222534125ACD639B,
	TraceJsonReader_GetDeserializedJsonMessage_mDE67F24CADD1B725363167B2B292DD83EE3A6AD0,
	TraceJsonReader_Read_m60BAE89BEA36D2F57B921E1028E038EEDFCDC562,
	TraceJsonReader_ReadAsInt32_m7BEFD39F9C3E3C9CD9674C2C5ADC5D57CA31A395,
	TraceJsonReader_ReadAsString_mB62D80DEF26C6726D27884A1B002AE15C5D931CB,
	TraceJsonReader_ReadAsBytes_m3881373947FC2D38E4E50C6C2837D42D5A789966,
	TraceJsonReader_ReadAsDecimal_mB0ABEB6DDFA689482DA322D9730C55B8288DA951,
	TraceJsonReader_ReadAsDouble_m945A1C89DE21CC84270A453D21CC61992CF5AE25,
	TraceJsonReader_ReadAsBoolean_m50A3735D8687AB62269B43B9C9CBAF34BE5AF55C,
	TraceJsonReader_ReadAsDateTime_m3C1B224C863DB33E40D1299C6D5EDF85A19BD95B,
	TraceJsonReader_ReadAsDateTimeOffset_mD7C48696CB5233A763A9FC1A6621021162D15311,
	TraceJsonReader_get_Depth_m23F47B60E5AE7A370D664B3083947DE0DF105C37,
	TraceJsonReader_get_Path_m6E017BCF388164D8BFFBB73F0EAB0CB33A9974B8,
	TraceJsonReader_get_TokenType_mD922306C81C9D3B730D309269524373932331D14,
	TraceJsonReader_get_Value_m1CF3260F1F87F988FCF919721E61C874128F9B25,
	TraceJsonReader_get_ValueType_m7CC85F32AEC28D84E5CB4CBE6F27ECA6689B3117,
	TraceJsonReader_Close_m633D18592EE0F35AB823FABA3AC3F1C067A07814,
	TraceJsonReader_Newtonsoft_Json_IJsonLineInfo_HasLineInfo_mE620DACED1F9F4936297D55155C72CD20B027043,
	TraceJsonReader_Newtonsoft_Json_IJsonLineInfo_get_LineNumber_m2E8784264E4EB22684FFA2264280DAA787AF0B5F,
	TraceJsonReader_Newtonsoft_Json_IJsonLineInfo_get_LinePosition_m016538DDB244CA49BC4C4A0D41692FF1AB2112BC,
	TraceJsonWriter__ctor_m3E3486A6651B23E492D604BCEA2A210249592BA6,
	TraceJsonWriter_GetSerializedJsonMessage_mE49F37A44038C9069FD2716280370814EF74B363,
	TraceJsonWriter_WriteValue_m387E27345B5DFF27B64D530DFB707CB76CA18298,
	TraceJsonWriter_WriteValue_m51D914FD491CE6AAEAAB31550CC06E593D8EEA11,
	TraceJsonWriter_WriteValue_m2B5E1E3303C54DA298AEE4038A9C53969F04BDEE,
	TraceJsonWriter_WriteValue_m854BC68E1EA9C71B6157D1B7165FF8F4809A7408,
	TraceJsonWriter_WriteValue_m6741903ED1368EB1DC20CA5B4DE8F6F457CDF4D5,
	TraceJsonWriter_WriteValue_mF3DDD270E7C30DEC4A3FF3590A22AEDD9EA6AE95,
	TraceJsonWriter_WriteValue_mE72742C0C9C6F700D6690A61CDE2FB375D2997E9,
	TraceJsonWriter_WriteValue_m4B5887B0CB0CECB1D7C2834DBA2FBA913F754795,
	TraceJsonWriter_WriteValue_m4D25C068AF71BC08626EFC611871C0CEA6C4FFFE,
	TraceJsonWriter_WriteUndefined_m6C923D2F0292D55D0BD8FA93F61B868243505CE5,
	TraceJsonWriter_WriteNull_m872BE8A9120609F190DD07CBA161CD7FCD830139,
	TraceJsonWriter_WriteValue_mB1329768856F163D0A45956ABE7A63175A7558A7,
	TraceJsonWriter_WriteValue_m9FC06FD22915969AFB105CF7A59CABBF7F38BF9C,
	TraceJsonWriter_WriteValue_m3824B37C912D99B32985D43AAB2D9397AD2A5B3A,
	TraceJsonWriter_WriteValue_mE2D4ECEDFB0726636A0C66E9B2188C774DBFE732,
	TraceJsonWriter_WriteValue_mE71B3EDDB51FBE77E6C976009E6D56DF30479EFA,
	TraceJsonWriter_WriteValue_m261489DBC2320E14B49C4FBD928F44E0AD333317,
	TraceJsonWriter_WriteValue_mFD2DEC4F0E5F7126C124E1B47E26D6F1D647EBDD,
	TraceJsonWriter_WriteValue_m775C0788B1A39EB014C2B2577CBDEE04ABC83098,
	TraceJsonWriter_WriteValue_m7B25D6F714F20A34CC15A054E62623462B2C998D,
	TraceJsonWriter_WriteValue_mD67ACF9BB3B554052FB7C49BA8CAFC155780604B,
	TraceJsonWriter_WriteValue_mF2F45A10E4613BC17F063059001BD7D126381EC7,
	TraceJsonWriter_WriteValue_mA1C2AD0B488C9DABE25719063EDADF4F249348D2,
	TraceJsonWriter_WriteValue_m7D8B44D6EDEC679D46CA5489A1406FABD602CF94,
	TraceJsonWriter_WriteComment_m7B37305B3B0F050CCEF39E7B8E7697524CFCEFFA,
	TraceJsonWriter_WriteStartArray_mA06D78DCE29C3F0236A0D0413B417108449C4D1D,
	TraceJsonWriter_WriteEndArray_m37C01D2EE5296D0493ABAE1006DCDEC616EDE783,
	TraceJsonWriter_WriteStartConstructor_m4234021DEE68C4B916B0A4FB822EE2B5B46E3C09,
	TraceJsonWriter_WriteEndConstructor_m456ECA5BBFA7392CCB58556F5F67F898AED87077,
	TraceJsonWriter_WritePropertyName_m56A03E6B57A6774CB12D6808A7ED789CC504FDD9,
	TraceJsonWriter_WritePropertyName_m80839F58F0ACFF975B1BEECCFDA10D7E609DF848,
	TraceJsonWriter_WriteStartObject_m17F2D91DA646F2D4CAB2E76EDD476B8EB07DBE89,
	TraceJsonWriter_WriteEndObject_m42BEBC4A2E6E770006ACA86823F8FD84903F9299,
	TraceJsonWriter_WriteRawValue_m6B20B376ECC430CFB2B2DF5CE2DE033607FC6865,
	TraceJsonWriter_WriteRaw_m2D39EE6E19B1113B24148C30D7C183B3D00E9CF1,
	TraceJsonWriter_Close_m9D548ECEF35BB5A7814CCB77CFC57D92F9C7BAE2,
	JsonFormatterConverter__ctor_m2868335DDCD8435A0CF003E7D647B789FFE1B9F7,
	NULL,
	JsonFormatterConverter_Convert_mA81F946483B59F3A844A3D069FEAFA389BD8C491,
	JsonFormatterConverter_ToBoolean_m3A1A6C00B20EBBA9CE20A1CD809C9708D0F91E12,
	JsonFormatterConverter_ToInt32_m2EE11559D61CF8FBA9B882D27A9BB66F4D2166C6,
	JsonFormatterConverter_ToInt64_m03669090C8968153B201BEA95615955F79D18A73,
	JsonFormatterConverter_ToSingle_m0FE3DDC468076AC4BED8F08D3CB3935C36A8C5CE,
	JsonFormatterConverter_ToString_m98055A041D194F714750D8C3DD34444176F65ED6,
	JsonISerializableContract_get_ISerializableCreator_mAA91A4A389A6EB62B67DB400211DEA4ED8042A47,
	JsonISerializableContract_set_ISerializableCreator_mEF41F2A09A138E3EE75A630A8DD64DDF9B85A7A1,
	JsonISerializableContract__ctor_m3935F0046EC24721B5FBDF4F9CC45609066E3B26,
	JsonLinqContract__ctor_m8C08D3FEC162BEE504D5839A7B9AC219B2B8D260,
	JsonPrimitiveContract_get_TypeCode_m0E3438AC9301910CFEE1C15BC070A843A2957099,
	JsonPrimitiveContract_set_TypeCode_m52E6AC64F3CE3E13413CDFAC1E5D1EB5AF6B7428,
	JsonPrimitiveContract__ctor_m84E9035402FCACFE50DF21AA6DBB66AF6A611A85,
	JsonPrimitiveContract__cctor_m5F63AF53A1417526F4B573A6AA0AD1A95771F188,
	ErrorEventArgs_set_CurrentObject_m26E745E2EFBC578CD83C2D785DD97BDA8EFDF489,
	ErrorEventArgs_set_ErrorContext_m4F32FDE7A7691132AEE98964C66C8640F0F05030,
	ErrorEventArgs__ctor_m20915417B7EAA1CE406CEC95B0126432746A0084,
	DefaultReferenceResolver_GetMappings_m4084E7573155BE15D3B34AF5F00D1DD68C2ED35A,
	DefaultReferenceResolver_ResolveReference_m1B79A4390BBFE882B6CB8116CCA7D4F323AE6C20,
	DefaultReferenceResolver_GetReference_m2EA734AC7977D28EA3800399E56D35DA7032256C,
	DefaultReferenceResolver_AddReference_m93166B604F4A4AA65C8C3C8045534E6F6D19335E,
	DefaultReferenceResolver_IsReferenced_m3B9B18394CEA2E5ADB68A98AD01DE42C7909087A,
	DefaultReferenceResolver__ctor_m21021524FE387AFB9F24D997468F4A9A02DA4903,
	CamelCasePropertyNamesContractResolver__ctor_m71B6E080791024F83516A378AEC9852D4BF284A7,
	CamelCasePropertyNamesContractResolver_ResolvePropertyName_mBCADCEA687C5666538AB4FB239D525C08A0EFD4B,
	ResolverContractKey__ctor_m81CFB26289389B6A3333FC0524DF4EE5E8E3E1B7,
	ResolverContractKey_GetHashCode_mA043B0AAF1DD1D96417D00365B90416A8C3A889D,
	ResolverContractKey_Equals_m0432D1CC6F6ABA61801B0BEED7ECA44B2C9EEED4,
	ResolverContractKey_Equals_mEE55B3988692D37E4F7F05E91262DC511323872A,
	DefaultContractResolverState__ctor_m8110089DA68A665EFE42280439042B52FA84DAB6,
	DefaultContractResolver_get_Instance_mF5195CD656C772B020C282CAA82EAA50B52053EB,
	DefaultContractResolver_get_DefaultMembersSearchFlags_m549575E45ED95951C372501916FB4123B6E6D7E0,
	DefaultContractResolver_set_DefaultMembersSearchFlags_m8CD7B21923CC6D975DCD3F00157E090038751791,
	DefaultContractResolver_get_SerializeCompilerGeneratedMembers_mDA7E3E17547CE3D23DDE5B794A9C3E7584A11690,
	DefaultContractResolver_get_IgnoreSerializableInterface_m5B7D581C6BB2FE170BC492F9C66B304AF8093F4B,
	DefaultContractResolver_get_IgnoreSerializableAttribute_m1164B44EB2ECF3D091F44D780C95B131B5BE0144,
	DefaultContractResolver_set_IgnoreSerializableAttribute_m33F292D8EF5C738E272B51111A1E8F617163FEC3,
	DefaultContractResolver__ctor_m52F3E230F523349797B923489423735999EA4A4E,
	DefaultContractResolver__ctor_m11CEAB39EC7F67E511D46DA81884D7B9CAB7A36F,
	DefaultContractResolver_GetState_m3B4EEE702394606639A5A15DC896826042BF3359,
	DefaultContractResolver_ResolveContract_mCCA714228217D18060D9FD497F14EC9FDB20E392,
	DefaultContractResolver_GetSerializableMembers_m74B76C6E25954420529626AEB04E77DD93926C32,
	DefaultContractResolver_ShouldSerializeEntityMember_mB9347B1632170CAF3DBDE3C3E25D807BC10044CC,
	DefaultContractResolver_CreateObjectContract_m169C640662722418EA8FED393D24823729A79B45,
	DefaultContractResolver_GetExtensionDataMemberForType_m4790C656AAEFD98DAC4F296F038FB1AAAC633EB8,
	DefaultContractResolver_SetExtensionDataDelegates_m2899F75FE30CA0CE45C1DD86949103D92CB99AB7,
	DefaultContractResolver_GetAttributeConstructor_m3357DF64AD9D932B8A824F631A91513EF8974625,
	DefaultContractResolver_GetParameterizedConstructor_m91C2DAAB5D4A9A223255E4CADC3DF616F79CB353,
	DefaultContractResolver_CreateConstructorParameters_mFF13C94BC5A8D254DB2AC87C3413B8DDEA1557A9,
	DefaultContractResolver_CreatePropertyFromConstructorParameter_m88731A8B37DC65EF8142024AE3892BC688AC31DD,
	DefaultContractResolver_ResolveContractConverter_m19C847BE3F615A59F3C0E955F5513A40A091A1A8,
	DefaultContractResolver_GetDefaultCreator_mAB208B59B1D325A549039118F896F1B7CA6DCD39,
	DefaultContractResolver_InitializeContract_mD92726A76657DD95A257B1D5ABD0F619F1C3AFE7,
	DefaultContractResolver_ResolveCallbackMethods_m3404C7A0910498426ABED752D762E0F7566FCEC1,
	DefaultContractResolver_GetCallbackMethodsForType_mCCEFB4AEDC1E6197A8015CB1D6DA140498DD1C65,
	DefaultContractResolver_ShouldSkipDeserialized_mD192FD03EF902C1C1D7DC56D9D7A6DB3767FA184,
	DefaultContractResolver_ShouldSkipSerializing_m2A5E0BE64BDB541ABB29C01C4A32774ECDE7F271,
	DefaultContractResolver_GetClassHierarchyForType_m109B1ACFCAAEBAC4FBA3DD71CFF4AEC875628A42,
	DefaultContractResolver_CreateDictionaryContract_m69CB0EBB8CC0F79507F48D85B647B20C9B0E6969,
	DefaultContractResolver_CreateArrayContract_mC312CC56C381CAE1B50C72F260A2181DA63AC51F,
	DefaultContractResolver_CreatePrimitiveContract_m000A0E84565AA866DF08830D431B8E7620A9597F,
	DefaultContractResolver_CreateLinqContract_mD3DDE786CD06A1002582F15DDC0B6F116BD6B89F,
	DefaultContractResolver_CreateISerializableContract_mEE1FA85CDFFBFDABE8AF40E9E20847231CEBA367,
	DefaultContractResolver_CreateStringContract_m99A18E510EF500DC52A3DAD78543E6FB49AFB345,
	DefaultContractResolver_CreateContract_m856C42DFFC8BC7407B4C9D2F8CC8F8D165CE8678,
	DefaultContractResolver_IsJsonPrimitiveType_m1FCBA966577856D7FC5CEF79B7B9E0B3F7747694,
	DefaultContractResolver_IsIConvertible_mAD611B13EC99D605790E7175312BC468E87497C0,
	DefaultContractResolver_CanConvertToString_mD9989BD88FFA5A954234A17DEEADB8455BC823ED,
	DefaultContractResolver_IsValidCallback_m8D9EA5D1C5AF473EAE3324B3E1C62428CB121CB7,
	DefaultContractResolver_GetClrTypeFullName_m18CEF60842A1ECB14BEDF84B4759E8A27E6FFFF2,
	DefaultContractResolver_CreateProperties_mB299696CA0E7060E307E7F54D0E94586B7B2869F,
	DefaultContractResolver_CreateMemberValueProvider_m336BFE31B7B854585C703D5F8D9C306B99C4AB6F,
	DefaultContractResolver_CreateProperty_m7C27609BD46BF2A2144D8D21F10419BFDF88374F,
	DefaultContractResolver_SetPropertySettingsFromAttributes_m2F76CCB18F6416C34D521D8FE6B4A71F1E669F45,
	DefaultContractResolver_CreateShouldSerializeTest_m295E60C21E1BD022604D3E78517D0E97F3FD5FF4,
	DefaultContractResolver_SetIsSpecifiedActions_m659959226A92F854516014E205F5F96FAC52F99F,
	DefaultContractResolver_ResolvePropertyName_mF1402600A96B19B20715D222FE9F45E78AED400B,
	DefaultContractResolver_ResolveDictionaryKey_m6D69FA89770161DFC3CD1E7619698D64B3CF8B2F,
	DefaultContractResolver_GetResolvedPropertyName_m1934B029B73CD108AECF3A916CB3F5172E4FD073,
	DefaultContractResolver__cctor_m33FAF927613DCD98A37DB435848F063D6DBCB2B7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__cctor_mC215E6FDE3FAF7F067C22A77263B2FF5A517860E,
	U3CU3Ec__ctor_m3E79F6CFBF3C721ACAF30D518663E0B15EB9F422,
	U3CU3Ec_U3CGetSerializableMembersU3Eb__30_0_mFEFE90181AEFF3AB507A3D174C30B96FC41B5348,
	U3CU3Ec_U3CGetSerializableMembersU3Eb__30_1_m559B776BA2840D31A1E40D24EEE9089786283AC2,
	U3CU3Ec_U3CGetExtensionDataMemberForTypeU3Eb__33_0_m057A45DC560266F46A77E628B976EBC57E619A6D,
	U3CU3Ec_U3CGetExtensionDataMemberForTypeU3Eb__33_1_m0CFBD09387805A25D1758D44FCA9F0FEC349ED68,
	U3CU3Ec_U3CGetAttributeConstructorU3Eb__36_0_m7C0532A4B9E0F6E88E049ED30E29BD319248C47D,
	U3CU3Ec_U3CCreatePropertiesU3Eb__60_0_mC59729DAEEB1CB386F97D3B2560BBA503109DBFF,
	U3CU3Ec__DisplayClass34_0__ctor_mEE9EC7C90C171882F62F47A469AD829A13824021,
	U3CU3Ec__DisplayClass34_1__ctor_mC9885B8D4584E3B00F12F61D3F7EC941CCC193D5,
	U3CU3Ec__DisplayClass34_1_U3CSetExtensionDataDelegatesU3Eb__0_mC38A1C3A2912AC02AC5089285F917A255D06B694,
	U3CU3Ec__DisplayClass34_2__ctor_m0D85569F5E5FD7CBC8CEBBB14D4CBE6F8D316F21,
	U3CU3Ec__DisplayClass34_2_U3CSetExtensionDataDelegatesU3Eb__1_m3B63677F446C68D2D08467EEEE52AD89F69B14A6,
	U3CU3Ec__DisplayClass64_0__ctor_mBBB2E8C987040040CE2A743761AFCF56CE623828,
	U3CU3Ec__DisplayClass64_0_U3CCreateShouldSerializeTestU3Eb__0_m7B52B342CAEAF56D64F40447A119E27B5D89029E,
	U3CU3Ec__DisplayClass65_0__ctor_mADEF15F981F5C8DEC069D30741FD2133A8E5FE03,
	U3CU3Ec__DisplayClass65_0_U3CSetIsSpecifiedActionsU3Eb__0_mE8ADD39E3C9824984921449ECB333705AE38ED9B,
	DefaultSerializationBinder_GetTypeFromTypeNameKey_m2B4627D6534284A400DB5E24166784D0D8791B5B,
	DefaultSerializationBinder_BindToType_m599FDE47FCFFD24698CFFEEE04C52E983F50A8CD,
	DefaultSerializationBinder__ctor_m1A42DF8399610D98F0F71B458F3CCF6CF2AD5A7F,
	DefaultSerializationBinder__cctor_mC5654BC18CEF69ADE581983237D646EFB298F6EA,
	TypeNameKey__ctor_mEB5446E840F899A22E0CFE0B3DB83D323EA29AAD,
	TypeNameKey_GetHashCode_m2818F664C6CD4EA974EBD5D17147C28D1A903A98,
	TypeNameKey_Equals_m351AC7FD99EF650F601259E1DB999D84DC2E3E6D,
	TypeNameKey_Equals_m87BCAE319F7BFF2F369E9F8877F04051EB8160AC,
	ErrorContext__ctor_m035618BB23F49DA853D3F4D959F700A0A6DBE4BA,
	ErrorContext_get_Traced_m75237BD7FA2271E3CCDD049E74DF20D09676E598,
	ErrorContext_set_Traced_m191C23FDE26BB7BDF9F40B9CDE8C430A646F7922,
	ErrorContext_get_Error_m0A859EFFCE6A600DD3580C3771F240C8BDE74D54,
	ErrorContext_set_Error_m82C60FAD6A5B51461030AFA1C01B79567CDD9ADD,
	ErrorContext_set_OriginalObject_m7E9A24F7426734D75794A6C4C38F7B09563B8E77,
	ErrorContext_set_Member_mC24D577A564BF09ECADF3EEC08D076F11B93165C,
	ErrorContext_set_Path_m422496546D56168A5F5886AED89327AEDA5C8D05,
	ErrorContext_get_Handled_m31C43173C83008776CA50B6BF4F9990C0DE48742,
	NULL,
	NULL,
	NULL,
	JsonArrayContract_get_CollectionItemType_m323C31B1A257D6EDD322D46EB8B8E168AA24C90F,
	JsonArrayContract_set_CollectionItemType_mA076DBD0C386C7DD94A1F73BA04E1758C8DFA195,
	JsonArrayContract_get_IsMultidimensionalArray_mB6C08E190146C9810A18D55A25A2DEACA423CCEF,
	JsonArrayContract_set_IsMultidimensionalArray_mC15445408825E9072EFF96A0D78180D043EF7C58,
	JsonArrayContract_get_IsArray_m030F748DF4D1E37CEF657B66BBAB4A527C1DD650,
	JsonArrayContract_set_IsArray_mE79B995AEA911E7CF3B7D774C0D9458F1553B0BC,
	JsonArrayContract_get_ShouldCreateWrapper_m2607DDBD1D4CEF3528464ABD2CB93989D901D275,
	JsonArrayContract_set_ShouldCreateWrapper_m5364CD0392E0F3007787C7A947A57D740E1DA0E0,
	JsonArrayContract_get_CanDeserialize_mA4269673785752E43430D41088371176505F8554,
	JsonArrayContract_set_CanDeserialize_m6EE7125CB72BF048836FEBA8A1D2EA22DE19DA7D,
	JsonArrayContract_get_ParameterizedCreator_m103599E8B4611CE27204D897E9065250FEF5AB35,
	JsonArrayContract_get_OverrideCreator_mC67591267D84CBDF275F012EA946F0515485E7AA,
	JsonArrayContract_set_OverrideCreator_m574FF3E00B0156ED9CC5FC73582552EEC6FA0CD5,
	JsonArrayContract_get_HasParameterizedCreator_m9DFE178941F031612E7A02608BAF9EABA2DD7FF0,
	JsonArrayContract_set_HasParameterizedCreator_m30821EFEBDCC26B3CEF2F7EE67FE512C45783A94,
	JsonArrayContract_get_HasParameterizedCreatorInternal_mD8EBC9C72E26D0F5EA773590B073C58ADA79E8ED,
	JsonArrayContract__ctor_mAA81A5943EEC3B703838ACFF20C15B8BB192B268,
	JsonArrayContract_CreateWrapper_m680121FFBC450F2B9612687063974FC32D6B14D6,
	JsonArrayContract_CreateTemporaryCollection_m2CF721D76CC983419F0B128471C0E2AF02D0E8A1,
	SerializationCallback__ctor_mD20943B9A19E3372B683FE88570DF24A91842B32,
	SerializationCallback_Invoke_m8409A73F0B02AD97D8044C018E80784BF4F39995,
	SerializationErrorCallback__ctor_mC0C873DD8EDE00B5545827889D56AB6CCFF5E0B1,
	SerializationErrorCallback_Invoke_m5A232ABC19DEA18BB86BB3FAF6601D495C4AAAFC,
	ExtensionDataSetter__ctor_m65A566850CC5193E76AED8FD0AB45511D5128AF3,
	ExtensionDataSetter_Invoke_m59E16DF7974C758788DA302C186DB519792F2403,
	ExtensionDataGetter__ctor_mD30966A61D48BB798304C72948502F050E4199FB,
	ExtensionDataGetter_Invoke_m896D50D58EBA6C316B674EDA4AA3068284CF1576,
	JsonContract_get_UnderlyingType_mD9A37A6619D3F541F77C3E09ED8242511AFCBEB7,
	JsonContract_set_UnderlyingType_m92A57F57AD493D79C6AF840131A4B7BFB8C0BCF1,
	JsonContract_get_CreatedType_mCEA0A64AA856091792CF9E7B70D9CDDBE2BCD9FC,
	JsonContract_set_CreatedType_mB9B232317567D5530B9C9E238C4F94BCEFDD6A67,
	JsonContract_get_IsReference_mD17DF9BD44DDC490F282A213FB7AD38A28000EF9,
	JsonContract_set_IsReference_mFC973648198CB8FE16D145288C566252D19CDCC4,
	JsonContract_get_Converter_mF0D436033DBF3D355CBEBD07027D46C4B1ECF8EE,
	JsonContract_set_Converter_m68807F8BD7B34D2056C85464D0F74EDB68464870,
	JsonContract_get_InternalConverter_mBE202C7F3676B1D18B8189891952B728F55ED1C7,
	JsonContract_set_InternalConverter_mF21E059A42EC399C177E6A5E126F2673047FBDFD,
	JsonContract_get_OnDeserializedCallbacks_mD7648B190FC47079D2F072AABC593F16443CB1AE,
	JsonContract_get_OnDeserializingCallbacks_mBCEDDD83766E804875C007D6192DA0F41C379D3C,
	JsonContract_get_OnSerializedCallbacks_m4C32E26B665251C4187F4C4BAC6BA22346FE44C6,
	JsonContract_get_OnSerializingCallbacks_mD598741E61FD4DAD483A02BA78F9C14B9DB626F1,
	JsonContract_get_OnErrorCallbacks_m33B0B311D9EA13E0945832A96BAF03E50C1ECCEE,
	JsonContract_get_DefaultCreator_mCFA7A4D151451FF9EE54042A8DB72BAAAD2B8CC9,
	JsonContract_set_DefaultCreator_mBA111899C3FA1C007BF522FD0FB139C41E6C5EC5,
	JsonContract_get_DefaultCreatorNonPublic_m5A1BDEA1A4CE3886E6BD60DC0BD66694FB2E45CC,
	JsonContract_set_DefaultCreatorNonPublic_mB9D2AFC01ADA985286A0469B72F3256A0F6C7279,
	JsonContract__ctor_mB6B8A88C0CEA9E3A1BFF6E1CDC258C344695923A,
	JsonContract_InvokeOnSerializing_m26CFBE31E56ED68AC2E82B82B21F8B3ACA8A440D,
	JsonContract_InvokeOnSerialized_mF3C287F02D40385559E8B7439BED3DCB312E0828,
	JsonContract_InvokeOnDeserializing_m4EA5745B0B616B83335A846B3A745D8F1ECE1E12,
	JsonContract_InvokeOnDeserialized_mF40847EDCE74634AB7078AAA1B1C10EB2B03E5CB,
	JsonContract_InvokeOnError_m9A2C2FF5B883828D54E7A8FB4E5FB7CAA10EE1C9,
	JsonContract_CreateSerializationCallback_mF78A6E17BE907CE1003CA35C9C0B44810468D7DC,
	JsonContract_CreateSerializationErrorCallback_m086A2A98E30872487CF25E074A251887045805AD,
	U3CU3Ec__DisplayClass73_0__ctor_mC78FBC5F9E691FC052999CF4F6D65F9C9457965C,
	U3CU3Ec__DisplayClass73_0_U3CCreateSerializationCallbackU3Eb__0_mBA7D82DD46FBDE2CE0BD1C006EB17E2E0503D5EB,
	U3CU3Ec__DisplayClass74_0__ctor_m5ACE7CD3862E51EB1EF22351B62CB66AEF5A81E4,
	U3CU3Ec__DisplayClass74_0_U3CCreateSerializationErrorCallbackU3Eb__0_m90A98055512BAF9A8ECEA385D1C065DA1553DD7F,
	JsonDictionaryContract_get_DictionaryKeyResolver_m05162FCF443E5670E454B20DE7821EB5D821E7BA,
	JsonDictionaryContract_set_DictionaryKeyResolver_m76D8596A2262205278E9DB152586F7C7202EE13B,
	JsonDictionaryContract_get_DictionaryKeyType_mE92EF750094C636DF354F2ED771B8918A897BBDF,
	JsonDictionaryContract_set_DictionaryKeyType_mECD2E157E2F9506C495CE23C2B2D8FC024CAC5CB,
	JsonDictionaryContract_get_DictionaryValueType_m278FF42A97ABD7B6F6F952C7D17182FD2A08704B,
	JsonDictionaryContract_set_DictionaryValueType_mFD9BA2020E292DF60E3AC66BC41C2A46A0E6D7D4,
	JsonDictionaryContract_get_KeyContract_mFF0AFD10D2882644B9401B36EA7C912262A1A4E1,
	JsonDictionaryContract_set_KeyContract_m536A77FB0422117D8D5398B7699AB6F54827D6D4,
	JsonDictionaryContract_get_ShouldCreateWrapper_m448EB531A7D9FB6B0D14474DF4123681B642F0BE,
	JsonDictionaryContract_set_ShouldCreateWrapper_m70F1F112136B9C000785D7B113C9E980920DD179,
	JsonDictionaryContract_get_ParameterizedCreator_mB1F6EE4E1C57C47A323DB1D5C2A5E3D88AE07E80,
	JsonDictionaryContract_get_OverrideCreator_mA4C74B4283C2056367C12517CD092273FF9EE9C3,
	JsonDictionaryContract_set_OverrideCreator_m072E34CED83EF4035CC4BA7C7E3608132AB3F6CA,
	JsonDictionaryContract_get_HasParameterizedCreator_m6F137AB9FD4E909E425E1DCD934C7EC80CB40279,
	JsonDictionaryContract_set_HasParameterizedCreator_m1C8461874E8D2B63DE6A32DBFAC98F0BAE9D7730,
	JsonDictionaryContract_get_HasParameterizedCreatorInternal_m81D7F1F0D7741C019D1DF7B62DDECAC177E5F490,
	JsonDictionaryContract__ctor_mF9F1BD61E21BCF52C1151250CA2D6060B26B489B,
	JsonDictionaryContract_CreateWrapper_m87106D567DA1464E141F6FBED07D035A0FB6C656,
	JsonDictionaryContract_CreateTemporaryDictionary_m295E740929FDF568B0AB1DBA55EB087463C76471,
	JsonProperty_get_PropertyContract_m02E7EB329EBDB11A555F5346C26A23B9DC3DF5A1,
	JsonProperty_set_PropertyContract_mBEA05DCBE14D84763F5ADB99D7DF41C486EFB495,
	JsonProperty_get_PropertyName_m66C0A76B6380773DCA04B91C40032EE6CC0F7FFE,
	JsonProperty_set_PropertyName_m9CED5A907D22FA06A57494405F5C1EE98E1054ED,
	JsonProperty_get_DeclaringType_mD3ED136331FD1A9E37C98EB5579932CCA71DD69B,
	JsonProperty_set_DeclaringType_mCC360EE3878D9ADBBF43D9C0041CF0EE688D32BA,
	JsonProperty_get_Order_m1ADF90195EA27D17F3C9904A55EB3716EEABED41,
	JsonProperty_set_Order_mEDD3BA19A3946816500CBBFBC4F46F6B7B257774,
	JsonProperty_get_UnderlyingName_mEDB340917C1AC5F0C5861DDA93FB89EEB4B29CAC,
	JsonProperty_set_UnderlyingName_mC12C03A61DF03BE56C9F108E6C15D8DE5D0E4CCE,
	JsonProperty_get_ValueProvider_mFBD26BEA616A3B6A02C78431D93741243808AC63,
	JsonProperty_set_ValueProvider_m3E52A4C43012A13A874804F1539A9F50CC54D4BA,
	JsonProperty_set_AttributeProvider_m3FFF05C4EEE1FAAF5B733483B7416AF806DC052D,
	JsonProperty_get_PropertyType_mA68B958EF3FA448DE8073330143B58DE59274C02,
	JsonProperty_set_PropertyType_m4D1F70CA66382EF37C95083B5B90310EFD44F9AF,
	JsonProperty_get_Converter_mF3E03B156CCD94CE67ABAA1D7298F7618505D5AB,
	JsonProperty_set_Converter_mFC9963279952098BC4C2C67CB585A48EF9C725D8,
	JsonProperty_get_MemberConverter_mFCF2E970B82539A50CD0161A5ED0F6DBC80D4293,
	JsonProperty_set_MemberConverter_mEC21306A5EFB6CC1BE3B1D4EEBD6FFC5ECB822D4,
	JsonProperty_get_Ignored_mE919F5938030A483FBFDE610267A812A56FF0AA4,
	JsonProperty_set_Ignored_m1F9CA36C6C94DD3E563A4A1C8462DE5FBA6F5558,
	JsonProperty_get_Readable_mD6F8E84CF020FB4FB9F3CDFD438BAE796C0799A7,
	JsonProperty_set_Readable_m1AADD57C10234EA26128DF1CBED2C731E5104837,
	JsonProperty_get_Writable_mC42D3422F282EA472179F6A45B97ABE13D9815AB,
	JsonProperty_set_Writable_m758C34133078AAA8BC826D5F206D8B13FEA2E99E,
	JsonProperty_get_HasMemberAttribute_mD345EDBF9DFDBFC4E514FDBCAC56A8012AE2CE3D,
	JsonProperty_set_HasMemberAttribute_m92865493E79053F8C02419E5B7798CA7758ED374,
	JsonProperty_get_DefaultValue_m4C70DE40A0A057B08F1861B17F427B875BD71EC7,
	JsonProperty_set_DefaultValue_m8246A51C252AD8DBF71E897378DBE35169A645A0,
	JsonProperty_GetResolvedDefaultValue_m27B72BD74F55C0440C27166AEEE4AB9212BFFE48,
	JsonProperty_get_Required_m3FEAC228BB0A9B09B2A1BD88C3A5A0B48B511C9A,
	JsonProperty_get_IsReference_mBC2FA55AAD460313375EEA548D16748BA072FB49,
	JsonProperty_set_IsReference_mA04A81F2AB4228211644F28A9D128605A3C8BE76,
	JsonProperty_get_NullValueHandling_m66711C6E8112FF7FF2BA13DA977FE4C66D8D4B1A,
	JsonProperty_set_NullValueHandling_m1975598294F96AAF9FB5C2EADBFA7A3B75B6B426,
	JsonProperty_get_DefaultValueHandling_mB9898AE13350D06964AA3C38D383D2A3E83BE295,
	JsonProperty_set_DefaultValueHandling_m86A51D1BF0A4C8A1AC1F175216335B9DD6B1D1B8,
	JsonProperty_get_ReferenceLoopHandling_m19BE73BF4CB3BFCEF390ED8EFDA9F84C15958FCA,
	JsonProperty_set_ReferenceLoopHandling_mF0D9D07EBAE26AC5B89CEC817700D81CA4A37C62,
	JsonProperty_get_ObjectCreationHandling_mCB48C650C866006B2784BF3D1058029F40BCA566,
	JsonProperty_set_ObjectCreationHandling_m3A11003CF805D723E78DF85DBD6F52CAC6175198,
	JsonProperty_get_TypeNameHandling_mB9ADD59B98F0FB3D50AC0487EC91422CE6C0F491,
	JsonProperty_set_TypeNameHandling_m02D63F2A4611B9AFC60C04304CF8B3D0E5216134,
	JsonProperty_get_ShouldSerialize_mB3B4FA0A6478FCB636C195C0F4CB49FB10C28732,
	JsonProperty_set_ShouldSerialize_m46EE6AE304CECB231A5042488EA30F61A140741D,
	JsonProperty_get_ShouldDeserialize_m358AFC686584F117C9C48FEFC07B5A91F6698D07,
	JsonProperty_get_GetIsSpecified_m424B052A66EC6FEF6259BDDAF89C9FAE6CFB1E96,
	JsonProperty_set_GetIsSpecified_mC836374B47C73B17A7860AF08F28FB8B49C81F28,
	JsonProperty_get_SetIsSpecified_mC4D36557CAED12E81F30E0D94CEE102D9FA2C36A,
	JsonProperty_set_SetIsSpecified_m177EC7F85AA7A3059301906F3DB393B5C16A950C,
	JsonProperty_ToString_mDD97B31B703EED366B2EC79F5D275B73054CAD34,
	JsonProperty_get_ItemConverter_m8128D44A080AFA04F91E89CC5C13A1C2D0231CD1,
	JsonProperty_set_ItemConverter_m0E9488FFFBC280CA54D5D58279F25F305303140B,
	JsonProperty_get_ItemIsReference_m780EF41FD15292901B279EA4CDEAA932CCF0A1BD,
	JsonProperty_set_ItemIsReference_m18D38CB29FB7D25D4EDCB2810BB73DB9C0066DEB,
	JsonProperty_get_ItemTypeNameHandling_m4AB89D2F44DCFEFA59B89477EE92CB042D1A6D76,
	JsonProperty_set_ItemTypeNameHandling_m8B707B8D50203DDCF2C445BDD1C4D3CF0E14D93A,
	JsonProperty_get_ItemReferenceLoopHandling_mA9780B84D18D57C7806A262466C8D41F178AF01D,
	JsonProperty_set_ItemReferenceLoopHandling_mEB8E34E485BFBCE173A075858772127C14790FC5,
	JsonProperty_WritePropertyName_mE4F8374128AA7A8DDA5F04B7219D864C96544E6D,
	JsonProperty__ctor_mA017211DF2D2DF2E6EFB14A40F67D313E82C982B,
	JsonPropertyCollection__ctor_m3BF51B17031B29E73845BE135782472BCF43C5D4,
	JsonPropertyCollection_GetKeyForItem_mD29ED556D709E50D31E58CB169647C2739D7EDF4,
	JsonPropertyCollection_AddProperty_mAC7E17C29B8A0DC1F39720B0B011B468EDC2727B,
	JsonPropertyCollection_GetClosestMatchProperty_m051B5200B3BBEFB62E4351D1B2376A507D061A77,
	JsonPropertyCollection_TryGetValue_m409F84885C8FEF11287E5CCBE3C473691E4C03BF,
	JsonPropertyCollection_GetProperty_m78881F5BDF660C0DB29B343B024FFA5A18DB8355,
	NULL,
	NULL,
	NULL,
	NULL,
	JsonObjectContract_get_MemberSerialization_m7D72CAE3141E92134D1EFF6E16F3E42261E83D64,
	JsonObjectContract_set_MemberSerialization_mF9F1037CB899F4051EF74DE5AA4C4305B7635E2F,
	JsonObjectContract_get_ItemRequired_m69F93A48186F68820EAF3BDF5E3199FE8CA96360,
	JsonObjectContract_set_ItemRequired_m052D55E293C851B8B0424EB8FCB71D501E8B23E2,
	JsonObjectContract_get_Properties_m7074363E992F9DC8149D08D884F0C055108D5AE6,
	JsonObjectContract_set_Properties_m971F07E834BFF3A5C8D392007D4ACB850E7C5B53,
	JsonObjectContract_get_CreatorParameters_m3ACDF7DF733EDDCF2D1641084DC3C2AB56C6742E,
	JsonObjectContract_set_OverrideConstructor_mC2429ED1B6BC5A22C89C05F83C0D4C3D721F3AA2,
	JsonObjectContract_set_ParametrizedConstructor_m8BAD4CD30209ED11A7B4CA23D69C88E185E5D47E,
	JsonObjectContract_get_OverrideCreator_mB3BCF517936293F88487DC88A2BB093E017B9DC6,
	JsonObjectContract_get_ParameterizedCreator_m93F6DD6320FDB98CDFC4633732DF4F423279D10A,
	JsonObjectContract_get_ExtensionDataSetter_mA4BEB602420602B3A00A0879B8AB514F48924088,
	JsonObjectContract_set_ExtensionDataSetter_mD1F11813547C8550A386D2A7F89E64976EA3D170,
	JsonObjectContract_get_ExtensionDataGetter_m29DBB95A4E1AC3DF265A4E968133C2F2E935971C,
	JsonObjectContract_set_ExtensionDataGetter_mE40DD7E687AF9F1778D91715C1D73EB222D0E852,
	JsonObjectContract_set_ExtensionDataValueType_m71A33B2C1565DB97FF2502DC9993DA89EB62C562,
	JsonObjectContract_get_HasRequiredOrDefaultValueProperties_m56809E3E09FC66C9645700137AE9DBA8633E559A,
	JsonObjectContract__ctor_m924A44A5810F1962B452C04E5E467061F29D7228,
	JsonObjectContract_GetUninitializedObject_m573704FD87BAC6EEE3AF234B71E2C38E0BB26B43,
	JsonSerializerInternalBase__ctor_m485CED903346F736AC90B76C528096CEA1E785D5,
	JsonSerializerInternalBase_get_DefaultReferenceMappings_m18D3525B8293E8D71B92971F133887F247D02E4D,
	JsonSerializerInternalBase_GetErrorContext_m87BFBBBE149D62F41CBE8811707FAEA4147BE4DD,
	JsonSerializerInternalBase_ClearErrorContext_m4F40B91D6AEFF4C046FD8AF7801B148A2F1DF23E,
	JsonSerializerInternalBase_IsErrorHandled_m03744F32BCD5F528B09B5324219085C2CCF59C91,
	ReferenceEqualsEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_Equals_m4B800A4FE0856AE30F5006F034C27DE0D8FBB7F9,
	ReferenceEqualsEqualityComparer_System_Collections_Generic_IEqualityComparerU3CSystem_ObjectU3E_GetHashCode_m8E6A140FB15CF1F92AC78C978CFF69C100489270,
	ReferenceEqualsEqualityComparer__ctor_mEB8CDF0FCF2F4EE6C798B07408DB7DD984EDEC26,
	JsonSerializerInternalReader__ctor_m0CC3AF92136D2B21D9EE73433F27D362A8427BF1,
	JsonSerializerInternalReader_Populate_m1E3334B2A04698CACCC6769E77AA3F9E03A7C7D8,
	JsonSerializerInternalReader_GetContractSafe_mBC44A2DD8301E5B9A85EE4CB9960C06110F23FBB,
	JsonSerializerInternalReader_Deserialize_m37E61DFB1A0016D0CCCFCF33D489BD079BD65EF3,
	JsonSerializerInternalReader_GetInternalSerializer_m6EBF9AD9A2495FE4BA717E7F6BCEEF8F41C30448,
	JsonSerializerInternalReader_CreateJToken_mFED9EEB3CF08D61A5EE7956D6DCC69FD2763BD53,
	JsonSerializerInternalReader_CreateJObject_m7B2913A1D866741B067B707AEDDE17E894F62FE1,
	JsonSerializerInternalReader_CreateValueInternal_m2951B28851F7EF17051BC3178678ECE5664BFAAD,
	JsonSerializerInternalReader_CoerceEmptyStringToNull_m58502C73921318333E82635D77B28360617D6256,
	JsonSerializerInternalReader_GetExpectedDescription_m2D9308B8409615439942F8E129084D2E13FAED59,
	JsonSerializerInternalReader_GetConverter_m0F9028F2963C85A675C5B61135219B834F94DAAD,
	JsonSerializerInternalReader_CreateObject_mD7F8E57151B7EFE0B4A883F21D71B9B7A6AB5662,
	JsonSerializerInternalReader_ReadMetadataPropertiesToken_mA3EC65A66F0FBD10E72CCCD62FE5AF85B2550615,
	JsonSerializerInternalReader_ReadMetadataProperties_m213E4EE3865491891B5DF24D2BCAFDFDE92217F7,
	JsonSerializerInternalReader_ResolveTypeName_mF3775D685B08A3F909F4417CB01F98053D53DD20,
	JsonSerializerInternalReader_EnsureArrayContract_m2C58864925AA3A898716AE1405504C13D091FCE5,
	JsonSerializerInternalReader_CreateList_mDCB7FD9511B2825840C589D55AB1092285B80313,
	JsonSerializerInternalReader_HasNoDefinedType_m7988F06036E94625BDDF81963BB0557EA59BA2CE,
	JsonSerializerInternalReader_EnsureType_mF3E312096D7E5E6076D46CC91BC21C96FA149F04,
	JsonSerializerInternalReader_SetPropertyValue_m54850CF16B76AC4D47E822671C33B562D399BFEB,
	JsonSerializerInternalReader_CalculatePropertyDetails_m55C55D3019745AD567897335D7A4E8DFD17490F3,
	JsonSerializerInternalReader_AddReference_m2B7E2B34B236F2DAB1FEC17B605024C81833C65D,
	JsonSerializerInternalReader_HasFlag_m3028438FB7E8743CD264B9F9AE91B127E707CAEF,
	JsonSerializerInternalReader_ShouldSetPropertyValue_m1F09761C8E2DCF8EAA4E54FB4120401FC07CE059,
	JsonSerializerInternalReader_CreateNewList_m7CA6E1CA3CD2583C84A481EA84685D8D7B7524E6,
	JsonSerializerInternalReader_CreateNewDictionary_m9F3162A6810CED7A82588B88787D768795BB98B9,
	JsonSerializerInternalReader_OnDeserializing_mC62A6755DC18EFFD97F842D0970C8C54776FFC04,
	JsonSerializerInternalReader_OnDeserialized_m6130B5B232E4A3D0217AE876B4E06C2375832FDE,
	JsonSerializerInternalReader_PopulateDictionary_mD04B93690D3EB9C4892EE5236B958684A15F70ED,
	JsonSerializerInternalReader_PopulateMultidimensionalArray_mDE8E4305F3A5E9A2D4DC7C00F1C3E6D187F78132,
	JsonSerializerInternalReader_ThrowUnexpectedEndException_m2081CD321452B270E11B702FDA9D76B8C2B2A9E1,
	JsonSerializerInternalReader_PopulateList_m6B54CFA5F4A7A0C8773CD47B810F7CC1E245366D,
	JsonSerializerInternalReader_CreateISerializable_mA896FB8CD4ABE1FDBD914114C5A0F23058D95D68,
	JsonSerializerInternalReader_CreateISerializableItem_m49EC43E878AA913A64C3E0418FFB62888DE1AB00,
	JsonSerializerInternalReader_CreateObjectUsingCreatorWithParameters_m1C26F454DDB083972F36EBE27C1C3C6361FF21A3,
	JsonSerializerInternalReader_DeserializeConvertable_mC9BACED43FB0B34DC6E93F74289F0CEA2B426FB5,
	JsonSerializerInternalReader_ResolvePropertyAndCreatorValues_m0F5755EA90D72BB2EA9029BFB59CD7BAFC958B92,
	JsonSerializerInternalReader_ReadForType_m87BCCCE124949AE5F50DFB4C84E879A0AC5A7FE1,
	JsonSerializerInternalReader_CreateNewObject_m8BAE755FCE8FA5F5CBFC25FB178C2AA695000C2F,
	JsonSerializerInternalReader_PopulateObject_mBA642D60FF031FBCCF0ABCA645E09C8785C27C64,
	JsonSerializerInternalReader_ShouldDeserialize_mF0814A1834B9E1382526C6DB60A26249BC0B1643,
	JsonSerializerInternalReader_CheckPropertyName_m7CEDAA79DA7EABEE00417013C604C0B448EEA78A,
	JsonSerializerInternalReader_SetExtensionData_m41465A000736E2C32FDCCB0CE22307C3BCEFF665,
	JsonSerializerInternalReader_ReadExtensionDataValue_m4A28C4B9FEB5F192E8223C9FEFEC1A93B2D31642,
	JsonSerializerInternalReader_EndProcessProperty_m218BC0692C72079B04C18BC5C8F27812555AA2B4,
	JsonSerializerInternalReader_SetPropertyPresence_m34D1BF723E6777C77064C19DF3E62C60BA55379B,
	JsonSerializerInternalReader_HandleError_m40720759FE1F8D2FE07B25EE5A8102F06A7F9F98,
	CreatorPropertyContext__ctor_m508868091CBCFC37EFE4466D87BAE91804BA5C98,
	U3CU3Ec__DisplayClass36_0__ctor_mDA833D4515BBB0D9B644DE9A0004439E7338B255,
	U3CU3Ec__DisplayClass36_0_U3CCreateObjectUsingCreatorWithParametersU3Eb__1_m1068B34FFC6262FB39DADDB3C0D794D29EC43AA3,
	U3CU3Ec__cctor_mF0443A4AB1C0BD9516159D36A6365C6C86D1F370,
	U3CU3Ec__ctor_m3CA04813FF9823D1FBC71845B99D1AB42876D76D,
	U3CU3Ec_U3CCreateObjectUsingCreatorWithParametersU3Eb__36_0_m08A820C0AC3674F39026C4FD32CED80930BE9AD1,
	U3CU3Ec_U3CCreateObjectUsingCreatorWithParametersU3Eb__36_2_m2614EB2448E724E298BE4BEB7ACF9F15C5A5F978,
	U3CU3Ec_U3CPopulateObjectU3Eb__41_0_m699194014C9012B0D8FBB64D393FF28DBAD62A06,
	U3CU3Ec_U3CPopulateObjectU3Eb__41_1_m033B99CD52BE8B896D9C18ACA6CB286BEDECA15E,
	JsonSerializerInternalWriter__ctor_mCC08C997ED86BF784BD7F7D1AA0E3A418AD682B9,
	JsonSerializerInternalWriter_Serialize_m6F3A4DB9C4E298D5014F4E918B44EC1C3218F0D3,
	JsonSerializerInternalWriter_GetInternalSerializer_m47EEAD97DB52CFFA70BFD1A648E60AE18B4E1651,
	JsonSerializerInternalWriter_GetContractSafe_mD9ACF3FC60B6B54EF02783392B3AE86D8351B865,
	JsonSerializerInternalWriter_SerializePrimitive_mA43DB99838BD6BDC632A4BE7416BA0FD978C8D30,
	JsonSerializerInternalWriter_SerializeValue_m9E2AE84CF7CBD3978BE0FC2E30EE576CEBD4449D,
	JsonSerializerInternalWriter_ResolveIsReference_m49B7EFEF5C968810E1188B41522582FFCB6763F6,
	JsonSerializerInternalWriter_ShouldWriteReference_mE7FEFC8DF15EA92D0DAFB8715B4E3AE6C189F840,
	JsonSerializerInternalWriter_ShouldWriteProperty_m61E04ADC18C85DCBAA123C8D0CC618B8027D3AE3,
	JsonSerializerInternalWriter_CheckForCircularReference_m6C095C45DF61AFF70BB9206D2806B0FD4CF44E28,
	JsonSerializerInternalWriter_WriteReference_mB21F3155E83BB4303F761E460AC27E2C37B6B687,
	JsonSerializerInternalWriter_GetReference_mE58B35D148E7362435786EA0DBB17F145A335224,
	JsonSerializerInternalWriter_TryConvertToString_m2EFE6A8F0D4B190F1179C4B7CEB9CB5186B5B528,
	JsonSerializerInternalWriter_SerializeString_m4A3E309C637A56736C673EA169EE5069B66AF58D,
	JsonSerializerInternalWriter_OnSerializing_m0634AC6F2D85CA48741709EA27E35488729DC3E2,
	JsonSerializerInternalWriter_OnSerialized_m6671114B713D4ABC359EE5E6B4F06A8D0AA0EB1F,
	JsonSerializerInternalWriter_SerializeObject_m8E8111A60CB066BFE3930238B7AA82B62891DCB1,
	JsonSerializerInternalWriter_CalculatePropertyValues_mAA869D0C5FC1FAAC62B576E766318EE1697E0C8C,
	JsonSerializerInternalWriter_WriteObjectStart_mE51BBC51224288FA1A775C9B7C4021AB37AD3334,
	JsonSerializerInternalWriter_WriteReferenceIdProperty_mF2D2FFE03B44AD70222600F765A095BEE220EE27,
	JsonSerializerInternalWriter_WriteTypeProperty_m98259688AA2CF87C4153675AF3A660432C9DC1EF,
	JsonSerializerInternalWriter_HasFlag_m775FB1DAEBF77775EABDB18E272BE3C5DD9171DA,
	JsonSerializerInternalWriter_HasFlag_m3217D958D6672A6D4B6D8DE06B93B5F3D5DD667D,
	JsonSerializerInternalWriter_HasFlag_m041C330EDA9893F3687DE90EB08228701DB62B89,
	JsonSerializerInternalWriter_SerializeConvertable_mF17E8B9C9509F9BCA030F3B6C7A60AEC550E7A60,
	JsonSerializerInternalWriter_SerializeList_mAFEA6EE2951E8D90F138366992B15F8B43E0798D,
	JsonSerializerInternalWriter_SerializeMultidimensionalArray_m44EF51EE789A05AD562A13262DE4FDC37853EFBF,
	JsonSerializerInternalWriter_SerializeMultidimensionalArray_m10A5E084F8C0A33F9C916D3F1F8CB1049B27362C,
	JsonSerializerInternalWriter_WriteStartArray_mA91BC3BB7C3538EB0FE45169C34985A6B3B83262,
	JsonSerializerInternalWriter_SerializeISerializable_m32D26085FD3582BF09B5FC9807EA317D979108DF,
	JsonSerializerInternalWriter_ShouldWriteType_mCE78A56201F8DDFCA5FF9822C26FDAB691938956,
	JsonSerializerInternalWriter_SerializeDictionary_mA25958F221EA9C67CA789E1DE52673713420BB7F,
	JsonSerializerInternalWriter_GetPropertyName_mBF182832677C67FD454732017E2D8CEB4380CE9D,
	JsonSerializerInternalWriter_HandleError_m5F64C5A4BD5021B0B893BC28B5FEE82EA78BF138,
	JsonSerializerInternalWriter_ShouldSerialize_m1D82DE84ACBDFF5FCE100D5D44EA698189CDB404,
	JsonSerializerInternalWriter_IsSpecified_m5D2F39F5941CF28FEBB613B579234D3145054E09,
	JsonSerializerProxy_add_Error_m3F4E48B96166D2F13B12DB257AADC16C99D73DBD,
	JsonSerializerProxy_remove_Error_mFBD83B46CB6CD0D0CC237AD133EE28019ADD1F7E,
	JsonSerializerProxy_set_ReferenceResolver_m7BE98589D3834B1C212F67F1F7FD97FA9BCC7EC6,
	JsonSerializerProxy_get_TraceWriter_m3749F1C8343AA0301CCAFF71C4A27044C218F76E,
	JsonSerializerProxy_set_TraceWriter_m367D6D5838B06A9EAAABAC64EA9BA3040127BD06,
	JsonSerializerProxy_set_EqualityComparer_mCD50460A7C5CDFC4AB564925AF42F323595E09A7,
	JsonSerializerProxy_get_Converters_m6D74100FB7C7F60AF4768B000B506DF381ACE052,
	JsonSerializerProxy_set_DefaultValueHandling_mBF3BEE08E1EC35630E20240720AB9E0C8421D6A1,
	JsonSerializerProxy_get_ContractResolver_m75EF9DF4EF8531A9D6711AF6B061F2F61D3B29CC,
	JsonSerializerProxy_set_ContractResolver_mE6390F594B814BCBC62B4FC1D85B5B4B6D744054,
	JsonSerializerProxy_set_MissingMemberHandling_mC8E3C59E089A30B8137E25E7426910447216CC61,
	JsonSerializerProxy_set_NullValueHandling_m9E87A8A64D213122ECDBCD0592D88E341F93CE25,
	JsonSerializerProxy_get_ObjectCreationHandling_m4325F872BB8C95F075DE5ACCBE4726AABEABB801,
	JsonSerializerProxy_set_ObjectCreationHandling_mEDAD7D338FA78F47A72F7E8E40C970BB8712B1AB,
	JsonSerializerProxy_set_ReferenceLoopHandling_m6DE348B9B84338195CAA503E021EAFBCAC214435,
	JsonSerializerProxy_set_PreserveReferencesHandling_m9CFADF4A088913A0D3AD21757CFFAA9FAAB9665C,
	JsonSerializerProxy_set_TypeNameHandling_m594B13276AE7584AA0AE35F79A6F3BF799D7A6BF,
	JsonSerializerProxy_get_MetadataPropertyHandling_m540B6755E7A52B3EB0D2E75D4F56D1E42EF65295,
	JsonSerializerProxy_set_MetadataPropertyHandling_m9CE01BB1081889EE2FDD2D710E4E166B7FD72108,
	JsonSerializerProxy_set_TypeNameAssemblyFormat_m316AD7675FA040C93EAD3C367409E7365C83D354,
	JsonSerializerProxy_set_ConstructorHandling_m323B96C8B1B9AC3D47520852AC0189474B765E11,
	JsonSerializerProxy_set_Binder_m1E8DF36271DE76EB6A1BCD774473CEE8666264AE,
	JsonSerializerProxy_get_Context_m83DBED6E809C2A988B30F42093D8DE80FC751048,
	JsonSerializerProxy_set_Context_m96AC87936F00DD20B2177D5AC1352E987DEEF064,
	JsonSerializerProxy_get_Formatting_m1AAD1BDDF840E183F9A5DB73A32EC0A8292D138C,
	JsonSerializerProxy_get_CheckAdditionalContent_m3A11037170B82D95A03FB4923102FB4A60CD356A,
	JsonSerializerProxy_set_CheckAdditionalContent_m2899BB28670290F2BD3A4A154FC8E516A7D6FDDB,
	JsonSerializerProxy_GetInternalSerializer_m66B0CF30F5F89DE663C295459E40B7C7632CB2D3,
	JsonSerializerProxy__ctor_m7E43FF07BE825F75C0D8986908EE9207E5F57D1F,
	JsonSerializerProxy__ctor_m6794477BA6057E98FEEB6421F15189B9E166143C,
	JsonSerializerProxy_DeserializeInternal_mFA054588EF9D9F945A16C63675217A54EF95632B,
	JsonSerializerProxy_PopulateInternal_mE51933CA473A9460658DD0945B396DD64B3B7426,
	JsonSerializerProxy_SerializeInternal_m2D63B96FB8C38360924506227137A8468191C949,
	JsonStringContract__ctor_m54A42C4ACC79372B1BA8543146B32DD8B8CC4256,
	NULL,
	JsonTypeReflector_GetDataContractAttribute_m6C5640D4E573853A4BD2B77D1CE353115322924E,
	JsonTypeReflector_GetDataMemberAttribute_m51F50871DCAA5A513B7E12A0AF003FA0ABC2237E,
	JsonTypeReflector_GetObjectMemberSerialization_m5A6864B17915ED80E8CB43E00B9C751DF5D7CA56,
	JsonTypeReflector_GetJsonConverter_mBB62F7EE987E98076594D2E89C4BEDDC0B8D72D8,
	JsonTypeReflector_CreateJsonConverterInstance_m202D426DBCF696743A3A607AA9AD3F0E6DED1682,
	JsonTypeReflector_GetJsonConverterCreator_m8BB7A51770C54072D007FA6A5271EC373DAE20A6,
	JsonTypeReflector_GetTypeConverter_m78582421D09C95414EDA2CEB68E0663D6B699754,
	JsonTypeReflector_GetAssociatedMetadataType_m681AA98B280B9EFB723BFD061AEEE9BA94EC53FD,
	JsonTypeReflector_GetAssociateMetadataTypeFromAttribute_m0559AA4119BD49EC696BF9AAAD5BB489A64DDC6B,
	NULL,
	NULL,
	NULL,
	JsonTypeReflector_get_FullyTrusted_mD8D182DE0EB2E23631249206AF8B24299BBE7AC7,
	JsonTypeReflector_get_ReflectionDelegateFactory_m924B16B5A02C8F4815333C06D1BD731450980A51,
	JsonTypeReflector__cctor_mEB78088CCDD3186722FEAF8EAABD57BD663DB85A,
	U3CU3Ec__DisplayClass18_0__ctor_mB5A4B81394AFECBBCDC3326D72AEE229812BE749,
	U3CU3Ec__DisplayClass18_0_U3CGetJsonConverterCreatorU3Eb__0_m2DE2553D9A380283E2D1A8E4403240A469377CF6,
	U3CU3Ec__cctor_m6DDB38A10A0DD796CE38ACBDFBCFB2C92E09432E,
	U3CU3Ec__ctor_m65150CDB4BD0AC8023743FFF0C7784866899F2C1,
	U3CU3Ec_U3CGetJsonConverterCreatorU3Eb__18_1_mB31A165FB8C0C5B9E011DCAB3658D2F4FF509B28,
	NULL,
	NULL,
	ReflectionValueProvider__ctor_mE88335461A4A910D2554A3F309D9B335EAEE8F77,
	ReflectionValueProvider_SetValue_m156DA2DB4C7691E1BB4040AE4E60264D9ECCF633,
	ReflectionValueProvider_GetValue_m077603E0A48B537460B96C40510B76835DFC8D08,
	OnErrorAttribute__ctor_m7D93D3E8F3DE8B244298CD08A90B0621EDB74858,
	NULL,
	NULL,
	JPropertyDescriptor__ctor_m877F099EA64D4B8F95EE769BC8429233B0171E29,
	JPropertyDescriptor_CastInstance_mA25D78E03BE3473DE077EBF34C242EA8DD3A372B,
	JPropertyDescriptor_GetValue_mA9224108CF26F679B4D4C2B4AE7720BB6EFFFFBA,
	JPropertyDescriptor_SetValue_mB9A460515C03CF3A38F658299417CA364D8C5D82,
	JPropertyDescriptor_ShouldSerializeValue_m50AC4E9A73780535BF1EC4871EDE381AE00E69F9,
	JPropertyDescriptor_get_ComponentType_m9BF70CB3B4F5A6DCC7F6B712C05357C133C4C9E4,
	JPropertyDescriptor_get_IsReadOnly_mED7B29AFABE05D9A6C86B732CAEB7C5ADAB8BDAF,
	JPropertyDescriptor_get_PropertyType_m2107646E0E2C91024B47171DEADC63A31BD42C4A,
	JPropertyDescriptor_get_NameHashCode_m3F0F484B07E5835C4F58C23ED6944038FEE13739,
	JPropertyKeyedCollection__ctor_m21F15E479CA1D7A1BDDAD4244D6C96DDE4E45C40,
	JPropertyKeyedCollection_AddKey_mFCC5FB5F64EDB1EDD325655982F437C8FB2C3CB4,
	JPropertyKeyedCollection_ClearItems_mD3C9AFFFDA4FA497CFA15A3EB6EC21D99BC66D76,
	JPropertyKeyedCollection_Contains_mAE6A5F9D709FC4B6498F2EFD6944ACD840CE7BF8,
	JPropertyKeyedCollection_EnsureDictionary_mFA61760F658076357677A7A4E65E30DBDD50A50D,
	JPropertyKeyedCollection_GetKeyForItem_mAEFC5E34BB0FF88D35E35CBEA81A72BF512A1A96,
	JPropertyKeyedCollection_InsertItem_mF8B5F2D756E71DB59588C486C797D889AC43CCC1,
	JPropertyKeyedCollection_RemoveItem_mF1EE424F9570BA59CFD9E97828028EDB741AB5F9,
	JPropertyKeyedCollection_RemoveKey_m56F96C29710D6C939FBDBFB73F216B853B3C0F47,
	JPropertyKeyedCollection_SetItem_m8446E595AD6EBB0248482B8C90E6C346568B39F8,
	JPropertyKeyedCollection_TryGetValue_m38A92FE81F48DC5B2CA8A16CC1277E1E66389438,
	JPropertyKeyedCollection_get_Keys_mA42C75415669CEC080DB22E19A95927588519287,
	JPropertyKeyedCollection_IndexOfReference_mCC509E015D29C17BEAED4786DB89CD5CADE3D108,
	JPropertyKeyedCollection_Compare_m6A45C124C716C21D17D67FA5CDC55B1010CBA50F,
	JPropertyKeyedCollection__cctor_mDA03CB99DC953D8DF9921B382FB831978EA14C40,
	JsonLoadSettings_get_CommentHandling_mA60E15D35DA81D315E59DD273F7F344F5350AC59,
	JsonLoadSettings_get_LineInfoHandling_mA36890FFDFFDE426CE4F4F601C94B05203926A7F,
	JsonLoadSettings__ctor_m1333B67DC91D3C14036DB1ABA4B4992CFC9EE255,
	JsonMergeSettings__ctor_mFCD8C16235038091E5739A7648229A6F8EE6414E,
	JRaw__ctor_m667F17DD78223DF6153EFE3CE4B5CCC79EE4D98C,
	JRaw__ctor_m2EFCB9D0BB51C60EA4FC7521946B263CE8E7B973,
	JRaw_Create_m6A2127ACCE39A194B22542FB214CA33C07617533,
	JRaw_CloneToken_m71D9416F2DD252F2853BB199D8C4D480CD81DC75,
	JTokenEqualityComparer_Equals_m630F3826E1E3251D8EB977C2E4FF53AF855B8E01,
	JTokenEqualityComparer_GetHashCode_m7E97DA3EAEB81CC1B17F84AA260EA47AF57D2489,
	JTokenEqualityComparer__ctor_m51EBD76B006566B513353A8A68E66A53335B5665,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__cctor_mF161356B631C1F18093C6D57194F43C64974597F,
	U3CU3Ec__ctor_m5CF9DE6B039C56A379A5B12F2BAB587A8954E961,
	NULL,
	NULL,
	JConstructor_get_ChildrenTokens_m5013969747638065409119179B69978D83CA1C89,
	JConstructor_IndexOfItem_mEFD447309B96478FC5D120D4B6D03D49B640EE80,
	JConstructor_get_Name_mD849F98788D5407A56C0D91C33DBE7F5DB0C7333,
	JConstructor_get_Type_m6F596B8CD30BB7F074B6CB8F9DEA242F9765D524,
	JConstructor__ctor_mF57946172C87B86825D8F225471A6DC1D9ADC249,
	JConstructor__ctor_m9E7552EB60C4E04D1C3ECAD56C9ACC0051F7DAC4,
	JConstructor__ctor_mC89CA8676C5398B955F9661409EAD842B1C3EFC3,
	JConstructor_DeepEquals_mCCA8269B462719D31C43F58539E9CF06FA2CFED3,
	JConstructor_CloneToken_mDEA6432C68F77BC1795F36E05192ADEA97C07095,
	JConstructor_WriteTo_mC844D568EC8CD00E7112B3C7A0CC0958C92FC0FE,
	JConstructor_GetDeepHashCode_m73B498B4C5810DF5C685347AA2D5D49CCF9291DE,
	JConstructor_Load_mAEAC566E973BE90651D1C19B0F91A2FF7BC4120A,
	NULL,
	JContainer__ctor_m7095E8571DB53EB2ACFAD0C7EE9D8585F2D47B90,
	JContainer__ctor_m27AD96BA1969EAC87FBF0848CF5A12A3C598D80B,
	JContainer_CheckReentrancy_mCBD43EDCBB0AF1D5716AC2860E1F52F995D23939,
	JContainer_OnListChanged_mED4E934E1C4A7F4E2DE4A2E185F854422AF9F100,
	JContainer_get_HasValues_m1B969A5AA61938A75C27A2B1F685FEE352CD5F2F,
	JContainer_ContentsEqual_m75BD8D41828E841828CA3ACE8DCA0F66C38EC39D,
	JContainer_get_First_mE5F65F00B32F0967AF47CEF9974B9161E4D43B03,
	JContainer_get_Last_m746D4108C662DA85127F29E40AF4BC3F7B6064B4,
	JContainer_Children_m145BCA2F4B5EDE9375D2EA14DEDB1648EE8D064A,
	JContainer_IsMultiContent_m25FF25009E27CECACA18D058969E5BDF3998988F,
	JContainer_EnsureParentToken_m968D556E18CAFC190674277A2A863FD596A00C31,
	NULL,
	JContainer_InsertItem_m300D3FF2E3AF3F5415B72BD8BA33195172EFA91E,
	JContainer_RemoveItemAt_mE4CF99214A3EF302AD4C06A74366AC4C40F74E18,
	JContainer_RemoveItem_m9F0EE2596BBA83FD7D26FE2DF394AE17A41DD50C,
	JContainer_GetItem_mA809EB1D838A8D11948B5107BAD94CAF335B0B65,
	JContainer_SetItem_mF7CA2F4D24EE1C3BD8E04B06E8137BF43E24CE61,
	JContainer_ClearItems_m8286C0312ED7545676783B072C0D89FEED420BC6,
	JContainer_ReplaceItem_m752AFB8D6378D9CA238F5FE25ED32A21B8A05634,
	JContainer_ContainsItem_m010B7EAE51D386F1B255B374D34CED6A972C3858,
	JContainer_CopyItemsTo_m0ADDADA788F24C17578ED04A19EE25641F26A8DB,
	JContainer_IsTokenUnchanged_m7CBB8C448C66D1D0F7A36575C9430369101B046E,
	JContainer_ValidateToken_mDC10E814447BEEA4FA6058F8CA78A7ECE4CE73B4,
	JContainer_Add_mA8F552B852765618919FCB3FF97C7C9C3E732160,
	JContainer_AddAndSkipParentCheck_mC51F4043221D2D090369E4070CA3CD4F7B697C17,
	JContainer_AddInternal_mF3555187E89A249BD501DBA72FEFD0DCCF1DF35B,
	JContainer_CreateFromContent_m92C69BECEB50E8249BB7D083F09541FC11804428,
	JContainer_RemoveAll_mAEF113701C58DE131358EFBF16F2B036335B6CFE,
	JContainer_ReadTokenFrom_m866C27A2735BE11C2369C02407B191EC29C3425B,
	JContainer_ReadContentFrom_m7824C54775FAB81872033ABBB131B1A66CDB690B,
	JContainer_ContentsHashCode_m47AEE7AEE48031D54C5E1E188D2A5850386AF0CE,
	JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_IndexOf_m10FBFB00CF547E0D610E16B46F20993645D97E3D,
	JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_Insert_m7950D9899A186852CA26BEC6F1743E03842485B1,
	JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_RemoveAt_m218130D3F2A86B5035D5B837FE2A59AB3B2F434A,
	JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_get_Item_m1BAFCBF2AF27E73CAC89E6F3C773F52CAEAD7A91,
	JContainer_System_Collections_Generic_IListU3CNewtonsoft_Json_Linq_JTokenU3E_set_Item_m4CDB06AD85C6236533DF4AAA7E80BA082AB72FD8,
	JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Add_m93BB5918A5163706AC0C6E183874EC027C2F3C5E,
	JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Clear_m1A3256AECAF0F3BEBC55CF445B050D24B742E48E,
	JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Contains_m2DD4EC5016A9A30A98D9A1E6149CE7DFE7C0D797,
	JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_CopyTo_m465B4E9A4452B4D68675A852056FE302CE4AED35,
	JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_get_IsReadOnly_m1B41063C227198B380F0B057BD904D8D42D1836A,
	JContainer_System_Collections_Generic_ICollectionU3CNewtonsoft_Json_Linq_JTokenU3E_Remove_m8B8C71F6B1D4D204EBD27CBF0024048635190394,
	JContainer_EnsureValue_m3DDD4F5502EF23102BB359D0A3FB845B2BC73297,
	JContainer_System_Collections_IList_Add_m105A4F8F00D9DE284181C13CD2D3ACCBBDC36A3D,
	JContainer_System_Collections_IList_Clear_m982C4381BF5A0742BEEBFE12BFFB9F276B05C15D,
	JContainer_System_Collections_IList_Contains_m5EDE03B695D67301384EBCD1B5B3F07F2807E809,
	JContainer_System_Collections_IList_IndexOf_m35F33FE6454F28ECF9D27ABB7CE7A4F664D50095,
	JContainer_System_Collections_IList_Insert_m39F37C385273DA1E61CDC92CC3A3B6E98563A1D4,
	JContainer_System_Collections_IList_get_IsFixedSize_m5C4CD0A26CEC6E6491758B54EDB6256F9B512DBA,
	JContainer_System_Collections_IList_get_IsReadOnly_m0C6C01289B98BD1037128AAB3ADD528EF178932D,
	JContainer_System_Collections_IList_Remove_m9D0783914B23B9D429E0EFA41C0CF2388B257379,
	JContainer_System_Collections_IList_RemoveAt_mE5C813681E23B5F92EBE43DA9F72BD4D2322F9CE,
	JContainer_System_Collections_IList_get_Item_m256622BBEB94CB9768D73DAAFD318D35DDEA037C,
	JContainer_System_Collections_IList_set_Item_mE37CA02290587C99F3D0905864EFBDADA1AEA64A,
	JContainer_System_Collections_ICollection_CopyTo_m17427E62A150F0790B044769A0253FA617108C3B,
	JContainer_get_Count_m04A0A7677BE3AA56AA02859848A96590BCE17D86,
	JContainer_System_Collections_ICollection_get_IsSynchronized_mBCEBD801FDD1D6147CABD86AD904BCBD12F483FE,
	JContainer_System_Collections_ICollection_get_SyncRoot_mBE4AEC28C06A208EC5A75BE571427D5BE1CD3B42,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	JObject_get_ChildrenTokens_m9F4744FB76B5FFF9F147CDE9032122E78E2A7D5E,
	JObject__ctor_mF7F801B6729F7309319FF97690BCC68C7CA81CE4,
	JObject__ctor_m3BC9D30AA26660214591EF08F691D1B740D6615D,
	JObject_DeepEquals_m4F351185021634247D419CE04F8ACBA0D1C22AE0,
	JObject_IndexOfItem_m3377AF169E182A25D2AAAD083E56E957110EFDF2,
	JObject_InsertItem_mDC98FFBC6659DA79610C169C0EE4A1513645DA5F,
	JObject_ValidateToken_m26B430FD70611870CF545EBFC63B4AC2D55F04A2,
	JObject_InternalPropertyChanged_m294A833CA13BEAFE53296CEA45985A7B33A5A5E1,
	JObject_InternalPropertyChanging_mE249501F10D07DC0865E0D7016E6078194F45DFE,
	JObject_CloneToken_m227962AB50C65A0D7C8A4DB8A4E306E4D8CC88BE,
	JObject_get_Type_mD77310BE37DC7B07F455AE6E68D47D75B1510B76,
	JObject_Property_mBC900C047166F06EA67767C1549A829071276412,
	JObject_get_Item_m889A29E8FCEA531AF829407F208DD05F0108C50B,
	JObject_set_Item_m38ACF4BABEDE80CF0138DA900AFFC5C4CC3E9221,
	JObject_Load_m44DE042F4F4752D15D54259823A74B609650E9EF,
	JObject_Load_mBFB13BABDB5082B495040AF48A8C691516D2D2E7,
	JObject_Parse_mBC884661DD2708BA92BCE45E8C104924542A2D43,
	JObject_Parse_mBF13328E82E184AE1D3E1AA56ED3A2879618719B,
	JObject_FromObject_m19DD8C1DB59132420FA3DE3E6EE27BDF2A4292AD,
	JObject_FromObject_mC479AB8737444180643B3E9905F02F11849C21D9,
	JObject_WriteTo_m39BCD00E12DCDFCB9446B22961E99840B04CC66E,
	JObject_GetValue_m4F5588B32404E992D225DDF4DBB7B5B033A254A2,
	JObject_GetValue_m163EB7BE77699B79CB53035D84874159EBBBFE6F,
	JObject_Add_m2F15295DA90DA00BDD3C94313C0F3DC4DE3C7A39,
	JObject_System_Collections_Generic_IDictionaryU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3E_ContainsKey_m0DBFD1C4A4878DC7511910F938B900851D0B1D9B,
	JObject_System_Collections_Generic_IDictionaryU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3E_get_Keys_mE713556A297D5397CB3AB964C8EEEA27BCD7C48F,
	JObject_Remove_m42D8594E735983D401F734BFECA380FE92459AD1,
	JObject_TryGetValue_m835B2EDE5A508D8ED745283EA5BE31EC4BD609BC,
	JObject_System_Collections_Generic_IDictionaryU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3E_get_Values_m02173559514621DFFF68F979DB587C0C315F6C62,
	JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Add_m234EFFFE0D80C677CC22DC59DCC7CB7A60EF595C,
	JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Clear_m68B2613EDF8D09DE7975E70A5FFFA3E23AACC921,
	JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Contains_m40742406A96AB115E83354BD431AA183CC6BE9F7,
	JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_CopyTo_m1C9BFEE21DC2A559562983B10928C444962883FE,
	JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_get_IsReadOnly_m6FCD2977B4B0BDB3FAE50871C3CCE77AB5C60A13,
	JObject_System_Collections_Generic_ICollectionU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_Remove_m6C2CD89F26FE0B46FE397C3506171F1632645A2D,
	JObject_GetDeepHashCode_m0835227B13EA6E01A2B53A5FBF62AC2C769CC8C4,
	JObject_GetEnumerator_mDB9AA9D5339E5EFD3D9D968B778E651D2CA693BB,
	JObject_OnPropertyChanged_mDD323E9A4F16D325EE142FB8E9E4EA6BA765A675,
	JObject_OnPropertyChanging_mD5368513750A4167D22EA7DFCD4657E4E4B6D10E,
	JObject_System_ComponentModel_ICustomTypeDescriptor_GetProperties_mF0E06F1B3A97C4D9CEDCEEAEC28EAF3B5211DEAF,
	JObject_System_ComponentModel_ICustomTypeDescriptor_GetProperties_mDE8C42C97C9596CA7A68065D4E7E122A4D7C8789,
	JObject_System_ComponentModel_ICustomTypeDescriptor_GetAttributes_m355B94DF88CE1D368F050AE12B66F92DD588D62F,
	JObject_System_ComponentModel_ICustomTypeDescriptor_GetConverter_m9D2FAE9FB6FA3AA8D3000B338A1532F23770C05A,
	JObject_System_ComponentModel_ICustomTypeDescriptor_GetPropertyOwner_m93FD4C2F8276E17A732F74F7544A9FF3BDF449D8,
	U3CU3Ec__cctor_mDB689A53DE1F3254E90000CC4F9218895446B99A,
	U3CU3Ec__ctor_m976CC1EED0623B90BD1AA7772CC23C5BF64E5973,
	U3CGetEnumeratorU3Ed__58__ctor_m0FF703F90ABA785137D83256807CEC7444514A9E,
	U3CGetEnumeratorU3Ed__58_System_IDisposable_Dispose_mB17F61830A91AAFC34D8A7C63F979DB9926DA1F5,
	U3CGetEnumeratorU3Ed__58_MoveNext_m2C65338486129446A6FBA52B345BE8EAACE08D94,
	U3CGetEnumeratorU3Ed__58_U3CU3Em__Finally1_m557DCFEACE4EE0F40B369360BA060D7DF8FAE7BE,
	U3CGetEnumeratorU3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CNewtonsoft_Json_Linq_JTokenU3EU3E_get_Current_m27AEFB01D6A203EF0144E08E527FFC2869B32760,
	U3CGetEnumeratorU3Ed__58_System_Collections_IEnumerator_Reset_m3F202C261171C2F464E566F2AC01E03BCE7B6A35,
	U3CGetEnumeratorU3Ed__58_System_Collections_IEnumerator_get_Current_m7B9C80AC6295DA597B9A4E4A6F41DCD37BFD77D6,
	JArray_get_ChildrenTokens_m98E4E304DF0E101C7928436E633BBBE9FE36CDE8,
	JArray_get_Type_m95FA623B41ED0E917C0FA74D54F7015E4DE2CCD1,
	JArray__ctor_m022A5DB24C674904082FD028B96F7AF93A87CF10,
	JArray__ctor_m664A0B87E47583C9FD3AB20E1CAFF4FD358A172E,
	JArray__ctor_m3D45B50C56FFFD2E00936E31E99DBC0BAEC0C9FE,
	JArray_DeepEquals_mE07350637BF6037E0F968EFEBACEE93C8C88FD90,
	JArray_CloneToken_mA92F71038DC716638EE97CC77BCDEA6358FED207,
	JArray_Load_mE90CEEC0A22EBC876F7D7E239A0E096E4691C807,
	JArray_Load_m38C0557B81449ED9FDE01552F39B9F35A2A7B2CA,
	JArray_WriteTo_m2D922281767C17A5AB3C171B743D05709382445F,
	JArray_get_Item_m6353E95068B4107D911305FC05DAC9B2EFC78463,
	JArray_set_Item_m440D5A05F46329315816521EA5096C21FF891EE0,
	JArray_IndexOfItem_mE8A7537E261B0A2E79D436CF11A07A356733BB0D,
	JArray_IndexOf_mD1E8177990592ED2B55E8B3DA063D8790D173953,
	JArray_Insert_mFBCF47E3C8CE609DFA236DEF0069787C0E42EE08,
	JArray_RemoveAt_m69FAB8DE7DC5E8391ECA03419052BC8F658C1314,
	JArray_GetEnumerator_m3A0A8116C13003EA3883AB9D5F2A9E8069192A4F,
	JArray_Add_m6E648CB9C562A6CC16DC550DA611456AB0149330,
	JArray_Clear_m9953833C9F980B90C1155AF4634E1A9D475E29AB,
	JArray_Contains_mFBB6AFBC7B92E32B064695EEE5160EFFA966EA66,
	JArray_CopyTo_m88034216EE65F56C0CF0456429C04BC8B6B203A1,
	JArray_get_IsReadOnly_m44C022E98720A9553B922A874F91E6A494E9F953,
	JArray_Remove_m9509FE2011FFAD9CAC0B5DF4836624B8EED876E4,
	JArray_GetDeepHashCode_m8B5110A65739039E1C57A3AA0FA960A2BB59FBC2,
	JTokenReader_get_CurrentToken_m9382FFC609615BC6423B5867BD2FB843646B5BBD,
	JTokenReader__ctor_m0C5BEC5718922E8D908126C43AEE4306550626CE,
	JTokenReader_Read_m49AE9CDFD4B12B166F183FA676C5876896833289,
	JTokenReader_ReadOver_mABE9EC2F3C5E1DB159B5288A210BAC08C9318C42,
	JTokenReader_ReadToEnd_m4B5F918C3E8734A351B12B5830B98F22E4908599,
	JTokenReader_GetEndToken_m7F8E62FB06000194E167434239C7BC726B5F12C9,
	JTokenReader_ReadInto_m798414BB9A9CCA02A6C864C40F3833A1186FF404,
	JTokenReader_SetEnd_m31356A205C3928A87D141D2E4BD9DD8879F200F8,
	JTokenReader_SetToken_mC726390D1AAC8B87E72AAB6981B546ED46AA84B8,
	JTokenReader_SafeToString_m28E950813248D29871D05DC533561FFCB7F163F2,
	JTokenReader_Newtonsoft_Json_IJsonLineInfo_HasLineInfo_m329AF0F990D67D75FEBDF4EE50D3B0B94EE33DD1,
	JTokenReader_Newtonsoft_Json_IJsonLineInfo_get_LineNumber_mC0F0DEB038C1E343F87D0946AAC4C1CE0B349BEE,
	JTokenReader_Newtonsoft_Json_IJsonLineInfo_get_LinePosition_m22D09371232A064EC3010C7528FD32CCD3CF63B6,
	JTokenReader_get_Path_mD84CD80F266A67DA4E4E2D881BD73FC629A775C7,
	JTokenWriter_get_Token_m5BF865D1141284D8D9FD898CD234770A58E7067F,
	JTokenWriter__ctor_mDF6283CC2ADCE49961F3BC8E325589CB7FC2F7AC,
	JTokenWriter_Close_m6A9ED7E0B68686B48CFBFB1586E4F39721E2C671,
	JTokenWriter_WriteStartObject_m86F0A42BCAA16952A61AB4C750FCA2F24C730A6A,
	JTokenWriter_AddParent_m9EED685872F6B03E08C52325B3A3BE40EE526619,
	JTokenWriter_RemoveParent_mAB44295B22B4541BB912A8B4032AC0E880584BEA,
	JTokenWriter_WriteStartArray_m42F92D250276FE62FDCB36ACC68B6F2E516EAEB4,
	JTokenWriter_WriteStartConstructor_mAAF69B644051CDAD6E9A67EE893A0EE351CB9018,
	JTokenWriter_WriteEnd_m9BF72B0A60418730995F2B381DE81D578EED66F1,
	JTokenWriter_WritePropertyName_mCDCE782E19D03CE67693D9677E7D8F390DCE79E7,
	JTokenWriter_AddValue_mEFB9AFCE11F351BBECC252A69DD9009B4F12E829,
	JTokenWriter_AddValue_mB221DF03D50CB4B9D440312A49E4847BF545398C,
	JTokenWriter_WriteValue_m7EA3EFF9672BBB3FA0CD1699EDBB669397CA7A39,
	JTokenWriter_WriteNull_mE307172496449E150E112293DAC1564E052C865C,
	JTokenWriter_WriteUndefined_m427E6F6115F677E01109B4FBA0499A666226568A,
	JTokenWriter_WriteRaw_mD9EBA5DBD05645978D6F58A8EC9724C5B379F3FE,
	JTokenWriter_WriteComment_m04AF8F5FA456D3FA55EAA28A109CD9160DCDFCCB,
	JTokenWriter_WriteValue_m434696662AD2B4037C9AABD8FEC3C984E6828A9F,
	JTokenWriter_WriteValue_m9FE7461A8987FF55181F129F9AC5604354C92B21,
	JTokenWriter_WriteValue_m470F253FC8D0CBE49179748CFA30CD5C7EF7BC05,
	JTokenWriter_WriteValue_mA4FA9414CF25DBFDF581C43652780A1882EF6302,
	JTokenWriter_WriteValue_m625E0EFEE44D45BD582500EF1D32579E722C6966,
	JTokenWriter_WriteValue_m600C4172CA8D6E08130F993704FA817B41DE2618,
	JTokenWriter_WriteValue_mFAA4FB131AB1BBE4C4E673CA0D5439AF1491FAC3,
	JTokenWriter_WriteValue_mE3CCECB372E0E38EE79C05A64FE72481BAEFDD3F,
	JTokenWriter_WriteValue_mEB12CDDA5762D417B5A57A76AFFF8A61A6AF4371,
	JTokenWriter_WriteValue_mCB225BB8D80CE649B845D6740BAD674483E342D7,
	JTokenWriter_WriteValue_mF5C978547D0B796B78EC5ED8C9730050D0DA7EE4,
	JTokenWriter_WriteValue_m3BAF64795364A1DE7616A272B3690BB5A6BDCDEE,
	JTokenWriter_WriteValue_m1103487C2734EF872E6A314E13F7884588D80D44,
	JTokenWriter_WriteValue_m51B382B361509DBDD313E0C76C8571135C2DF487,
	JTokenWriter_WriteValue_mFF835A395715E51BE6F197F9A631B1548AEF0410,
	JTokenWriter_WriteValue_mBE32A4ABB5E92351E2F98FBE1D6ACE286E736688,
	JTokenWriter_WriteValue_mAA41F1B2BCC5B70669133644957892B2D2DC47FA,
	JTokenWriter_WriteValue_mCD3839922FE5BA621EB22A52C257CDD0BF3EA46C,
	JTokenWriter_WriteValue_mAC9C7E0AE85B60136A5AEBF2EF3BB1DCC3AB8A20,
	JTokenWriter_WriteValue_m71870D2DC541373CD86D5F8F078A0B1F41D4B8FE,
	JTokenWriter_WriteToken_m807585847122907134AE5C74B5793BD8ECC5C27B,
	JToken_get_Parent_m17B873780C3C7FE29F8F1212C31A062606D842C8,
	JToken_set_Parent_mDC5EDB3881C8857323507BC955AACCEA70B9A22D,
	JToken_get_Root_m3E65C680E6D38FFCE33B97E1CE488E1D24CB58D7,
	NULL,
	NULL,
	NULL,
	NULL,
	JToken_DeepEquals_m7EDAC81C962B86192A264B70E0F3D8476380DF31,
	JToken_get_Next_mACA1FDDD6C404B82952BBDF16B6B50BD9D478AEE,
	JToken_set_Next_m52D3CB9747299C8329E124F13DF8B9B0B76E1E3E,
	JToken_get_Previous_mB73B2E756CCFB4DEBC3A5447B9D19983150E1EFB,
	JToken_set_Previous_m52A268BF0608F9E4C1BEC4B8E26CF67567A7AE89,
	JToken_get_Path_mB79EC8961042AA9E87144C9D31545245A872F8B2,
	JToken__ctor_mEF510CD05246F3C8B19F37A1DE5CF851D5A576AB,
	JToken_get_First_mD6C286DFD8B980A1C522C456465AB8DF306819BE,
	JToken_get_Last_m6096E6706EE76604C4C6254D178646656B521077,
	JToken_Children_m15C8A3922FAC76E90FDD694133DB771C00B42FD9,
	JToken_Remove_mE7E6F0738CD2057100B78230F30169A8EFB2365C,
	JToken_Replace_mF437D2189546BA0C6460B4BDF4FB0B255CDFA60D,
	NULL,
	JToken_ToString_m4FEE2B8A0F43D9B44EF5318381C42A66644B8415,
	JToken_ToString_mF787D2075E09E9D7156C38286D1770E13F27A8DD,
	JToken_EnsureValue_mDD514610ADBDF5D210D52EA0B1AF1373C3C4E018,
	JToken_GetType_m34BFC404F9653A235E5C8C267C7C71B9F7B8626A,
	JToken_ValidateToken_m91BE3C5241871ABB657621850C2CF3CE7A217D07,
	JToken_op_Explicit_m667CE86F4F3E5AA03260EE74E0A3B19EC320BA3F,
	JToken_op_Explicit_m2369F270DA0ED8E11DBDDBD8B083753668552D61,
	JToken_op_Explicit_mB2AA0B7D9CAF8710EC4BC761476ADA87367C2431,
	JToken_op_Explicit_mCF04EDB5165001707270AFB4EDDC618D44659123,
	JToken_op_Explicit_m983330188110B78B5C38C6FC428BB22F25708D44,
	JToken_op_Explicit_m17766598D81527DFCE022414772D2D028D24FBE0,
	JToken_op_Explicit_m8C505DBDBE9CF6455618C9044B03CCD6F1B3C88E,
	JToken_op_Explicit_mFC25C071BAEE281CE8B2CCFA50A52DA842AB3B83,
	JToken_op_Explicit_m35E396EF054631C5591DE7E30C152948A0B6F081,
	JToken_op_Explicit_m623DADB50226D4E8CA01390A029E152BCD82F667,
	JToken_op_Explicit_m6F85BD1A0341A55F06CCA6F3B5073220553AE253,
	JToken_op_Explicit_mB945F56C2DACBBDAA93C179537F05446CD736183,
	JToken_op_Explicit_mBFC868A465B0117A48954857D00257B2B45CD31A,
	JToken_op_Explicit_mFEAA11DE7F198467BFBB99117B78ED8EC6182C1F,
	JToken_op_Explicit_m78A9D7EE501698D0219CB46B4B77F43B9573541A,
	JToken_op_Explicit_m96CFF4CF2251C191BE11AAB69609A3C317C65F95,
	JToken_op_Explicit_m46246B1A16659CB6FEE9E562C57C166AA8E29BCC,
	JToken_op_Explicit_mC6D96B88E88C4A1E4F37230D825BBCC7870BC6EA,
	JToken_op_Explicit_m0DA4502F984E57F922858A834C73F47666F06B17,
	JToken_op_Explicit_mC7F24314102F2A992E6C54C01D51E8669603DFE2,
	JToken_op_Explicit_m11276548963D4907FC5A0087F69F837E63571927,
	JToken_op_Explicit_m82C6393DCCF1C089B27BF49F4D8CB4C64C648EA8,
	JToken_op_Explicit_m04CCE22D2C03A914819AAE7286D574A8E076DBFF,
	JToken_op_Explicit_m0353D5450D688D1922BEA396811C521E1FABE64C,
	JToken_op_Explicit_m46F6971D23F6A2074F077F3247083921715E5D4D,
	JToken_op_Explicit_m76828231142B4404434B41F093BD8A074D088909,
	JToken_op_Explicit_mE150E80B2E833549545832E403C015366F7EF53E,
	JToken_op_Explicit_m332CCC3F6400FEE84A91A0C985983FD1B9F3189D,
	JToken_op_Explicit_mA435B5A6C389220DF41F02E1CAE6AEA4EEFBCFF3,
	JToken_op_Explicit_m088FE678DEFCEB9F3849D6A988290A69798539DE,
	JToken_op_Explicit_m007D6A9ABD4A458B6E2534AD6E20218902717A8C,
	JToken_op_Explicit_m0DA7DE0B1765DF7733F7C204142771A13B20A566,
	JToken_op_Explicit_m652395DDA469259A869DE2612C418E99323907EC,
	JToken_op_Explicit_mF933B006170CC4A490F47076B559E18F518816BE,
	JToken_op_Explicit_mDA1708251D921CBCE77B251AE04B67161530834F,
	JToken_op_Explicit_m6C085462C60E37B854411FDFF9634F9C448D9F6F,
	JToken_System_Collections_IEnumerable_GetEnumerator_mF67223DB32E0DA49CEA2652A94DD8324CEE221D8,
	JToken_System_Collections_Generic_IEnumerableU3CNewtonsoft_Json_Linq_JTokenU3E_GetEnumerator_m0BA86062716EACA193C74ABBEA28DA1AE31AAB54,
	NULL,
	JToken_CreateReader_m7661A095A1F4F6ADBE2B59794655CCAE0DD445E9,
	JToken_FromObjectInternal_mFC4AFD80E0F0B2B5D95FDE9066E0C88EB6E32F04,
	JToken_ToObject_m6ADC3E3DA56C6D22F90046C7D25B5D2779E54341,
	JToken_ToObject_m4D96B7C479825194EC4FEFF0EFBCD306B78BAB5A,
	JToken_ReadFrom_mB9D1DDD43A246C5363263ECCAC1DB3E5235EA51C,
	JToken_ReadFrom_m13F77AEBCAF0F68830741402ED49A46BEBCB18C0,
	JToken_SetLineInfo_mB4BCBB4634209AA828DA0159BA6A59851AE3F3C9,
	JToken_SetLineInfo_mAEC923774319FC3D0E191EFD81ACB8C7FBFD0D7E,
	JToken_Newtonsoft_Json_IJsonLineInfo_HasLineInfo_m76B4FCAAD861984268CD071B0CFDC18CCD3F256D,
	JToken_Newtonsoft_Json_IJsonLineInfo_get_LineNumber_m2260CAA926CEFE6AAB53294F8C70A04A3CC44F52,
	JToken_Newtonsoft_Json_IJsonLineInfo_get_LinePosition_m471CADC7212B2AF40B5A825C3B21EA41CB4950BC,
	JToken_System_ICloneable_Clone_m83691400354A7CF289ED84DC93354DD35528F2DC,
	JToken_DeepClone_m9A8C76BD945F1D49D1778B032BE6CF75CB350A4B,
	JToken_AddAnnotation_mB45E8328B471F72DEB80F5BFF2D8A83BD4D90467,
	NULL,
	JToken__cctor_m617EECBF8913654B2613558A8615C487342B6121,
	LineInfoAnnotation__ctor_m087A639C20E3829FD4322818C40DCBAC44184AC0,
	JProperty_get_ChildrenTokens_m9F7647B340F91793C217C5217399706143DC258D,
	JProperty_get_Name_m96280838000C5E193121125A503BA9108DB707CA,
	JProperty_get_Value_m1D20FFEA7909BF0B835848D5FE9FCA2B08EF07F9,
	JProperty_set_Value_m41797576CAF498FF53598E7DD99AF3ABCFE662B7,
	JProperty__ctor_m8C1AF8455FC0BE033433F9B61D610438594C9E67,
	JProperty_GetItem_mCB74A2D78796745CC1FED107DE4ACCA4C747C134,
	JProperty_SetItem_m00A080A1D31D6FBEC2AA1DC90B900B3C851626F2,
	JProperty_RemoveItem_m1EE45CE5F44F835C64183BE812018B280BC588BD,
	JProperty_RemoveItemAt_mE9CD738F149400D7D43712436841B5C773DC9377,
	JProperty_IndexOfItem_m437AF150AB70425D939239304FB0E56E19606816,
	JProperty_InsertItem_m1ABD1611D2B8EA24BB68BECB7A8D2DE26B5FA1BA,
	JProperty_ContainsItem_mB72C4413EFBB1DF0EDF3377094B52BC806C3573D,
	JProperty_ClearItems_m54AC98D6851F7C4A511AE0BD830F8AB7957C3CE8,
	JProperty_DeepEquals_mFF73647AD6E0F72941500DB4E95AC0B4E138AAEA,
	JProperty_CloneToken_mA94A20011FC79498F3047F58F9294073EDBD037B,
	JProperty_get_Type_mBA3493784CA1E0D8672D84088893C004CC971B63,
	JProperty__ctor_m989B6CE16B40176E9BB766B7E371ABC890BF573C,
	JProperty__ctor_mA59A3257A0EB4DB85735EF8F576917BF3B5415F1,
	JProperty_WriteTo_mC777E5E99153D4F02B75718D2DE981F58F26D47D,
	JProperty_GetDeepHashCode_m22E73E9D1CECF4BFC9BE3A921FD25AC5CF3A6C4C,
	JProperty_Load_mDB8310C74ABE12E10DE590F257D86C8A27169BDC,
	JPropertyList_GetEnumerator_m5824B6E65D20B2C34F8BEC60AA48D258BDCEB763,
	JPropertyList_System_Collections_IEnumerable_GetEnumerator_mB03E09F8C69248337E04846BF65A8A1CB3290418,
	JPropertyList_Add_m1F395B95588471F3B3F94DF80FD2337577EA0AC3,
	JPropertyList_Clear_mEDA5A3B0FAA7414429620FD785E45FE156805EBC,
	JPropertyList_Contains_m100D7BD94B0EE4ECEF9B6D2616BC9DA6D8C62BB9,
	JPropertyList_CopyTo_mFE61BD1725F75537BA1154855512C1A72DD0C51F,
	JPropertyList_Remove_m50FAC96ED6E3863A2D32D37CAF5796132B152641,
	JPropertyList_get_Count_m80C947E8BD74B928ECBBD72CE6EEBD81340A8E48,
	JPropertyList_get_IsReadOnly_m80AF8FED0CBCFB196B1C9FEA73F8A83B365E4479,
	JPropertyList_IndexOf_m725F7A875C197243EB227397294642997EC1072B,
	JPropertyList_Insert_m278B0EBA585882903CA04F3A51894B2D5667FCE4,
	JPropertyList_RemoveAt_m3391387DA0B1473F81103C7C94F42262D728D56F,
	JPropertyList_get_Item_m1454E827714F686A14D4DA780725294696E6A450,
	JPropertyList_set_Item_m17B7C8C4C6C17E09CEC53A5DD89C15B13D8F19F9,
	JPropertyList__ctor_mDD66F5F4B93D02BDE3615A3CA00190901F32E616,
	U3CGetEnumeratorU3Ed__1__ctor_m5B6F8A76287F0CEB16B230609710257010459B7B,
	U3CGetEnumeratorU3Ed__1_System_IDisposable_Dispose_m9FE9AEE2588E667D69233EE649B1F8839A012662,
	U3CGetEnumeratorU3Ed__1_MoveNext_m68905C0409642C2522F5569A7234AC5523FE4137,
	U3CGetEnumeratorU3Ed__1_System_Collections_Generic_IEnumeratorU3CNewtonsoft_Json_Linq_JTokenU3E_get_Current_m8DBD3517400081107444232BDD7C39B3449B3F93,
	U3CGetEnumeratorU3Ed__1_System_Collections_IEnumerator_Reset_m6EFEBC9D0D877C84C433DC27AA114B8B3D49F64A,
	U3CGetEnumeratorU3Ed__1_System_Collections_IEnumerator_get_Current_m19C57ED9F0AA85C510DF342A2CBAFE5AA2AFFDE3,
	JValue__ctor_m2F2F6199A81B5C5C048F78EADF880B9C5A3CAAFC,
	JValue__ctor_m3F20532C2284C822B4BB9A8DDDED8B344BAA0AAB,
	JValue__ctor_m6B8489761C009E7F28C5763F88146DB5CE345E51,
	JValue_DeepEquals_mCC778967BEA5D41BFA0D67F601C6FEB754DC6698,
	JValue_get_HasValues_m16397FF6A2E008E4A19F42FC348BFFFFD303D4D2,
	JValue_Compare_mEEAB5FC08A482CD63CF831BD1090561094D3494C,
	JValue_CompareFloat_m6AACE292E958241C1802DCEBBD3DCF6DF52522A0,
	JValue_CloneToken_m415084FF8472BA71E0CFC86836855B1C3B6DB611,
	JValue_CreateComment_m6C65B659D0F245FB5613F776CAA1006A9AF67828,
	JValue_CreateNull_m53D9AAAFB0E4707B8538937B19F18759DF10E144,
	JValue_CreateUndefined_m8182EAF420674522680EBEC1878195BF32F05CFD,
	JValue_GetValueType_mAABD4B40D8D7C713FAC8F57FFAC3BBE511D43EA0,
	JValue_GetStringValueType_m2120C09E27DD55F09EC04FB162E3EF1AFBB92288,
	JValue_get_Type_m04877676E0AF75090174BFF9636B919A3DE2D3F0,
	JValue_get_Value_m7E0B68F90B51FD1ECC4C659765F6F949C0B0F35C,
	JValue_WriteTo_mF412D9869631021CCDC6EE397C7FC1A60CC35033,
	JValue_GetDeepHashCode_m52620331DF1615C845D867B36CF86A64713D6ED0,
	JValue_ValuesEquals_mA214E50023BC11A9E2103FD19B3A06A119F8BA2B,
	JValue_Equals_m9D93133F3F7F7BF4072ED20B901B8A4DAF94A453,
	JValue_Equals_m7D39E26AD4F530AF12EAFCC680F61DD99030781F,
	JValue_GetHashCode_m6313D73045A9C91C4984302DF7870205C98B3D1E,
	JValue_ToString_m2466E655FFE75B44C96B68B9F7B4E631CF0BCA29,
	JValue_ToString_mFC9CA3AA149DC9EEAAB0440DDCEE7A9A65F032C1,
	JValue_ToString_mDBD67FDCBA088D69F64A6CF4BF9800903A1ABADF,
	JValue_System_IComparable_CompareTo_m6A03F746EFFBFFAF620A49D3EE3DDB84254B1EB6,
	JValue_System_IConvertible_GetTypeCode_m7A6B61857BAE024F30DCD9F24EAB8DC134C69BAB,
	JValue_System_IConvertible_ToBoolean_m013182504A7AF6B64212092614EE91FE42FA075B,
	JValue_System_IConvertible_ToChar_m2299D2462EE9C600605CAE4F1FC160D1E2815D2A,
	JValue_System_IConvertible_ToSByte_mB653A11EF16D15B518C9CE4BEE362344E017F868,
	JValue_System_IConvertible_ToByte_mBD481AE7B3922275570D7399863649B0F8E30E45,
	JValue_System_IConvertible_ToInt16_m342AA1180C6261EDB371BEF2FE572245A6EEBB39,
	JValue_System_IConvertible_ToUInt16_m617AEA33948E9A9CEAB10486733F14DCCCB6C8CA,
	JValue_System_IConvertible_ToInt32_m16735D81C4F3F7FAD8742822EBD688AB963645A1,
	JValue_System_IConvertible_ToUInt32_m51A4013CC278349F58E22F37C19D2FF9F6CD64AB,
	JValue_System_IConvertible_ToInt64_m2015004A64DFCA96ADF151296C82EFA2AE993BF5,
	JValue_System_IConvertible_ToUInt64_m771CCACEC2CE2F776AABA7B63F7F4D44FA113A91,
	JValue_System_IConvertible_ToSingle_m5625F9F98CBA4493904457BA6D57B673BDFD52AD,
	JValue_System_IConvertible_ToDouble_mFDF1062C8FD49160FEEEAAC6B49D2A3E422B019F,
	JValue_System_IConvertible_ToDecimal_mD60AB15D746C975261E182F9F93A07628F5D7CF8,
	JValue_System_IConvertible_ToDateTime_m1FD44BF16F9A324AED43AEFD6AD37C29DBAE5BE2,
	JValue_System_IConvertible_ToType_mA94787EF3D65999A0E6C71441846A0C462AE2523,
	ArrayIndexFilter__ctor_m10910C433209D36872F1C9664A792BB2B415FD11,
	ArrayMultipleIndexFilter__ctor_m68AE4516C859AD1D41720136DA23E8548FF7D369,
	ArraySliceFilter__ctor_m642EC9DC6FE0A23FEAD208F2E8E1AAF26AC35647,
	FieldFilter__ctor_mE5BC245B72B8BF4E311457A98512B494B4602D56,
	FieldMultipleFilter__ctor_m083DEA736BFCB1F9D3CBB5F9FE9B4BDD2194E54E,
	U3CU3Ec__cctor_m84666D2594306D5E829449247FE6F7459A7736D4,
	U3CU3Ec__ctor_m365A5B2543687AE5277DA252DEF85A28A163B0DA,
	PathFilter__ctor_m94E7029DA14F6B51E42656FDBBCCC4B7ECAD19E0,
	QueryExpression__ctor_mE792ADE7A57E796DE334EBF1136BC323B2EC4196,
	CompositeExpression_set_Expressions_mD8F85558E58AC33E965CB9D60E89BDEDF32B6795,
	CompositeExpression__ctor_mDFCD0E37BF8EFB93016797CD2CEE145C92F9AD60,
	BooleanQueryExpression__ctor_mC2E073D357BDDE808589A9CBFC3DE9CFEE30A381,
	QueryFilter__ctor_mE1F383E4E24BB6EA82086AD5209AC828DB7C3799,
	ScanFilter__ctor_m976B5D26D55750F4ADA2DE0F437E95C68761CF69,
	BinaryConverter_WriteJson_m54AFB7F3527E39626E86389F48BAE9E9A56ED98B,
	BinaryConverter_GetByteArray_mD5871B065CC5FAB077E17BDE734FBD365836DEFE,
	BinaryConverter_EnsureReflectionObject_mFF1C9D88AFCA9F92F7FA127E89C9C23083730A75,
	BinaryConverter_ReadJson_m93A33D05378C772EF0984563A78641EC5995625C,
	BinaryConverter_ReadByteArray_m8744B9592F4CFE1AFD20387016C1FDE36E45EEAF,
	BinaryConverter_CanConvert_mA8CF5D87606B8AC2C21B833329D9D68E752916A8,
	BinaryConverter__ctor_m6A759CAB2FA8F38DCEF840B9DF367F2EBDC0845E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DateTimeConverterBase_CanConvert_m6319B09EB67CC570AA771FB6684A43D9E8D40697,
	DateTimeConverterBase__ctor_m02E6F20A4F764E261FA171A2A0D8694121CAEB06,
	HashSetConverter_WriteJson_m7258B2FBE81FE3B00090BAA6B9F1BB61FF2BFD91,
	HashSetConverter_ReadJson_mCAAE97098C36EB09E16A5F5301337BA9922F3A79,
	HashSetConverter_CanConvert_mED54AD057BBC9F636D7EFE7D7636FA3164C40ED8,
	HashSetConverter_get_CanWrite_mB6507CDFB1B08D70274FE786BA0EB6A7326ACC62,
	HashSetConverter__ctor_mA656EFE7F0C66AAE6D9819F2614B980E583A7662,
	KeyValuePairConverter_InitializeReflectionObject_mECD1547C1FDDAE2A63C8FE1C2B2E45AE555B16F3,
	KeyValuePairConverter_WriteJson_mB71698F2630B93D03C057F63D18FFEC98E0F9FA1,
	KeyValuePairConverter_ReadJson_mA993A01FB25C456BFE0DEE5CA1CEC18DBF0AA839,
	KeyValuePairConverter_CanConvert_mF101B6EFD84D2BCE96EF03553298F5BE10B66ABF,
	KeyValuePairConverter__ctor_mE945E5DD56F012CBB65CD6213B18365A89B23EEC,
	KeyValuePairConverter__cctor_m2529C663777F1625C160C3868539754FD920F863,
	BsonObjectIdConverter_WriteJson_m73F5985D9544CE0BEFEB95DDAB45E9BF1C8FB7F3,
	BsonObjectIdConverter_ReadJson_m3FA7C54B894B616778D5C145EF1029FE68990C88,
	BsonObjectIdConverter_CanConvert_m46DF6406FDBD5DFAFCB5F92167256C107DAC0D70,
	BsonObjectIdConverter__ctor_m9A8067BEF8BF07BEC8A7FA3E956475540E4E495C,
	RegexConverter_WriteJson_m65FA4ECCF0C6A86ED4D93A62F79DD2848EEA910D,
	RegexConverter_HasFlag_m8E63783C5E9BCD5DABF3824CDE1806E3E14D6368,
	RegexConverter_WriteBson_m4DDA78E9F73244FA1A404186035D84F684BCB791,
	RegexConverter_WriteJson_m146024645167B8D2B9CB7743115A8B75B1A9ED4C,
	RegexConverter_ReadJson_m7CF77989D9D66E5D39D1C1837CBB5A64A57B411C,
	RegexConverter_ReadRegexString_m5202370764B9C1DDC7876800837CE4E377E189C1,
	RegexConverter_ReadRegexObject_mBDF60E21C6F61527B07C7F03ED7930A93C1DABEE,
	RegexConverter_CanConvert_m8DED3CD0981D1A6EDC73718DB07246F5EDEFAABA,
	RegexConverter__ctor_mC1D5166D65F3777509842BB033FC4111EB826167,
	StringEnumConverter_get_CamelCaseText_m638D7E2E1ADFA8259C5C5DC44C4D6FFF4FA075F5,
	StringEnumConverter_get_AllowIntegerValues_m773843BCE2AEFD6CF8A21ABD17918C4CF62B1F9C,
	StringEnumConverter_set_AllowIntegerValues_m06EEDD4CA3D661984541A7E3CA1C5DD8E763ECF1,
	StringEnumConverter__ctor_m36B6009E735666675AEB30CD83B385291E98CC73,
	StringEnumConverter_WriteJson_m3F8A6A8539AD9E7209996B11277DA597DBB8B22D,
	StringEnumConverter_ReadJson_mAF074420B274DE15507DC1FA2CB5E0E126BC5067,
	StringEnumConverter_CanConvert_m893906149FEF1949D9BA1F3674E7C98FD275FEA3,
	VectorConverter_get_EnableVector2_m3B3C90FBD2669464F10143CD10CB34DE1A9C65EC,
	VectorConverter_set_EnableVector2_mE7927EB6096D3E1F0E376C73657BFFAE4EA93F01,
	VectorConverter_get_EnableVector3_m717868EFFCF7AAD56AA58CE8FFA579B0B48E491A,
	VectorConverter_set_EnableVector3_m396E789698564789734CDD684A1B1A6589FCB448,
	VectorConverter_get_EnableVector4_m64A5B4F3C38F459E77DBADA18927120326F1BF42,
	VectorConverter_set_EnableVector4_m7ABC7C2C252E7DFB946BDEDDEB33B1D0792605F4,
	VectorConverter__ctor_mD7EC6E1AAD9CA4C174A718BEAA50AB26649BC3DD,
	VectorConverter_WriteJson_m1EC6AC34618A6B91BE9240D12EF6575DF0C94B7B,
	VectorConverter_WriteVector_mABBFEE702E0E9959EB00722E64500943D57DF997,
	VectorConverter_ReadJson_m53BEB27FC5055DEF363EF25DF780E262FAA7E013,
	VectorConverter_CanConvert_m64936D2B51C6C6F5C4F5C062D2B35E727AC512F5,
	VectorConverter_PopulateVector2_m646F8116C9723199ABF4A82AE49244827EEB9132,
	VectorConverter_PopulateVector3_m801E5C586E2BA8EC8103C563387E20B6E190035E,
	VectorConverter_PopulateVector4_m1EE024C4E16391F29D2DB02913E475F547EE9F7B,
	VectorConverter__cctor_mC0459ADAB2DF71517A52E74EA34914A5C797C951,
	VersionConverter_WriteJson_mAF07010E49ACA90B579F5899FD77D6E59101E1C8,
	VersionConverter_ReadJson_m7965482BF5F14BE5B2C39F483A32FB074CD48FAC,
	VersionConverter_CanConvert_m6AEC0547939825EE9D68F3353854893A51220E6A,
	VersionConverter__ctor_m847EFC9A24C80B75A2A30334525A3859C6A5B3C8,
	IsoDateTimeConverter_get_Culture_m6CF18B78DD2A289E49E7A8DB3968460CAA38B55D,
	IsoDateTimeConverter_WriteJson_m60AFBDD6FB1FC4870E4B0FDA30B6067666507B18,
	IsoDateTimeConverter_ReadJson_m0662899DDF147D4EBAB5BFFA4095E36D37B58EE3,
	IsoDateTimeConverter__ctor_m826994A815114F0A06F4788BEAF6A1EE2AFD0432,
	JavaScriptDateTimeConverter_WriteJson_mF0E3B26942C1C76C5E1C2D5A0C0543F9742973BE,
	JavaScriptDateTimeConverter_ReadJson_m36EA36CCD31603F16FB4C3E19A8E088872214348,
	JavaScriptDateTimeConverter__ctor_m54DE0ABCE8E276690146CF30D4CE207AD6C0C5F1,
	XmlDocumentWrapper__ctor_m2DDFA872888661813F74523693DB4A34FE765DFE,
	XmlDocumentWrapper_CreateComment_mC922EE1533661AC0B35C262B206E321E22E440EE,
	XmlDocumentWrapper_CreateTextNode_mC5999FC1111FF593533A0A3C24981EB68E11CDB8,
	XmlDocumentWrapper_CreateCDataSection_m09B05E9D61F98937C0CDABB9C2EB8D09E56BD033,
	XmlDocumentWrapper_CreateWhitespace_m680AFD5D1D1E347930980839A099D5DAA4386253,
	XmlDocumentWrapper_CreateSignificantWhitespace_mAF94AFB2BB046CC35F48AF3CA5C93ABA2558D908,
	XmlDocumentWrapper_CreateXmlDeclaration_m5B96DF051DD3F731A388796F6D92B269D69EFE75,
	XmlDocumentWrapper_CreateXmlDocumentType_mD0588943D845FEA8D58958FD8694796DD94CA07C,
	XmlDocumentWrapper_CreateProcessingInstruction_mABF52EF273331B86C3CEDD3B50362E540CFB27D9,
	XmlDocumentWrapper_CreateElement_m45C3BF72AA259B9947F65860F97065B1C283335D,
	XmlDocumentWrapper_CreateElement_m98D9C428823DBD96E19D1DC3B5BCCB304A28D941,
	XmlDocumentWrapper_CreateAttribute_m79CF11B365F9F1FD574ECA810BF1F13FDDD82464,
	XmlDocumentWrapper_CreateAttribute_mC12DF3D00C4027F881A72FE1961BFBCE47224D49,
	XmlDocumentWrapper_get_DocumentElement_m55855C0394DD8B83FDD19B0E71EED3B4994E7119,
	XmlElementWrapper__ctor_m5EA54DDEBFA94B7DF7907BF8BCAD9A01C0AAE237,
	XmlElementWrapper_SetAttributeNode_m8093E93AE8461E128D28CCAFF4A7355993C2DE67,
	XmlElementWrapper_GetPrefixOfNamespace_m6C70E6D6B59A1E6534EFA7574D2050DCD258C3E7,
	XmlElementWrapper_get_IsEmpty_m6C29E47E666F9A547CB3E8367FB741D1DC08C6AA,
	XmlDeclarationWrapper__ctor_m4EAEA1BBD6AA3E66D3DBF18613BDCC12294F9529,
	XmlDeclarationWrapper_get_Version_m5AC22814EC256D574E5D37DA6601925D92B9E7CA,
	XmlDeclarationWrapper_get_Encoding_m479E2628A4A6375AAB4C7D302D4481DB88320E23,
	XmlDeclarationWrapper_get_Standalone_mA75E58DC0B6B4D48194D79195D1ECBA5CF2A0E5F,
	XmlDocumentTypeWrapper__ctor_m127F27D80D29C326F622FFD70CA1B3549F90C78D,
	XmlDocumentTypeWrapper_get_Name_m2FA9037C5566D8AB029C0D69129E3B60CF9DC510,
	XmlDocumentTypeWrapper_get_System_m9C848AAFC549088092657DB36BD8E04ACF0121FC,
	XmlDocumentTypeWrapper_get_Public_mE68A735EF0E56B8C242CEF4385CEA024A6C72C05,
	XmlDocumentTypeWrapper_get_InternalSubset_m5D296CBCCDB4C974240A075E3B4755D18FFFA022,
	XmlDocumentTypeWrapper_get_LocalName_m20D20EEA8F216955359FD85C71CDBEF9210B65B0,
	XmlNodeWrapper__ctor_m73806A21D3A81678F6099B62E80F7B6549D56D17,
	XmlNodeWrapper_get_WrappedNode_m95E187562E167E13524197070157B15EE0ABFDFF,
	XmlNodeWrapper_get_NodeType_m704A1F53F159B290363B45893B421742FDF82D86,
	XmlNodeWrapper_get_LocalName_m3F78DEA57A56000D98457B5A983CA62574E42D37,
	XmlNodeWrapper_get_ChildNodes_mB44F94F340476C1993AABDBCD77F535ACF68BD4E,
	XmlNodeWrapper_WrapNode_m45C8C60B81EA49FF73F8BB1F18EE8E4E8DE52DB4,
	XmlNodeWrapper_get_Attributes_m6A73C3FCD3E8E04EB983E48270831A03D0120B01,
	XmlNodeWrapper_get_ParentNode_mCE0D181D5080D05A5010AD8FA391AE50547FC260,
	XmlNodeWrapper_get_Value_m12711466B29D2B6C01C3041813F4FD4988C973D0,
	XmlNodeWrapper_set_Value_m978C9C12AC1A81119D1DA28B143406231E41F41E,
	XmlNodeWrapper_AppendChild_mF2D050E07BD4E4A0A4265872163D9C7232B3678B,
	XmlNodeWrapper_get_NamespaceUri_m5F7FD8E9F0B9546B357626FC34D2C7A2D344C568,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	XDeclarationWrapper_get_Declaration_mD7686F2EC3F760FD0AECC4FC6DD5E21F9F888437,
	XDeclarationWrapper_set_Declaration_mB8CF488A14A6A4CE715CF6D65E9D4642746CF378,
	XDeclarationWrapper__ctor_mC9E3C1C1550DD2576A03D5CCD4029BD8C64A18DF,
	XDeclarationWrapper_get_NodeType_m62F1C5F17B127A6F8E3D1427259D7D2D0714F5B7,
	XDeclarationWrapper_get_Version_mE4E712D0E0E96742BD5333811C8D0FDA5836BF5D,
	XDeclarationWrapper_get_Encoding_mE6CADFD39D778C76B41813C61B0A1E1BE513132A,
	XDeclarationWrapper_get_Standalone_mACE8B149D751736B59E0B61A943E1A84E9AF70CD,
	XDocumentTypeWrapper__ctor_m448FB77B151C12873112FF5CC9B314E9DB8F6117,
	XDocumentTypeWrapper_get_Name_mA074AA947F84C787AA869A40F7B0C7F64A502B93,
	XDocumentTypeWrapper_get_System_mC5CAC6D108869FFA6E547C06ED171CA1E235A51B,
	XDocumentTypeWrapper_get_Public_mD55A43097688C1497C9ACCE3DA915944322A8C6D,
	XDocumentTypeWrapper_get_InternalSubset_mEDBD4FCEAD914AEC869A485459F8908BEAE7E07C,
	XDocumentTypeWrapper_get_LocalName_mD17BA8F109A4B4123FFBC5317380A93E730A4EE0,
	XDocumentWrapper_get_Document_m368F6051580AEF8FBF78877386D469E1CAD779B6,
	XDocumentWrapper__ctor_mE05A6619DC930C9ABB32122C06DC694533611BDB,
	XDocumentWrapper_get_ChildNodes_m90EBA8E6ED65464C93E749266085473592223F23,
	XDocumentWrapper_CreateComment_m581B5E8B4BC22B4681EB5951BC1F8220C0832373,
	XDocumentWrapper_CreateTextNode_m2946508BCAEC542051832C77CCDED138EE841889,
	XDocumentWrapper_CreateCDataSection_m8506E7328F857D71EE8C68DB9621B994F9C6A77F,
	XDocumentWrapper_CreateWhitespace_m3737ECC4F95F648AC7417D2C73EA0FA239640DDC,
	XDocumentWrapper_CreateSignificantWhitespace_m409A710011DF878E02B3D163B64232C0ADE07C28,
	XDocumentWrapper_CreateXmlDeclaration_m8CC17AA93A14B7FB730FDDA9FE0AC7694088A8CF,
	XDocumentWrapper_CreateXmlDocumentType_m1ED0DA0569F9256577F23A9A65236E784BC031DF,
	XDocumentWrapper_CreateProcessingInstruction_mBD1AEBE0846FDA921B485F5A457F10110ACF2510,
	XDocumentWrapper_CreateElement_m906BF5976F58F7AB707BF445B01F4936B578082F,
	XDocumentWrapper_CreateElement_mFC40283D600E1C5B804460EFB4E442DCDF9FDFCF,
	XDocumentWrapper_CreateAttribute_m8224C607521D472529726F0C7D9418BBE42EDAE6,
	XDocumentWrapper_CreateAttribute_m90A8CC160233F03025ED08AAADFB3B2CE4F127BF,
	XDocumentWrapper_get_DocumentElement_m5F9F99F972213A26BB538D7B3BBFDB126988BBE7,
	XDocumentWrapper_AppendChild_m266B3D4945ADE6EF06CC3CAE337432F997E2D21D,
	XTextWrapper_get_Text_m15F65133CFB2B8E2F1AFBFE1EE9829669C3EF587,
	XTextWrapper__ctor_m98B8EFBEC0CA0A43C328B86120E043D5A4FE8222,
	XTextWrapper_get_Value_mDDB4561D37D181C792CF57122502CDC591B8CB4D,
	XTextWrapper_get_ParentNode_m5A470009D3268A28C9A5A9B8ED440C7FAE3D0A42,
	XCommentWrapper_get_Text_mF0823EAF847E1268D50A6E4D0345828B2E2C7B22,
	XCommentWrapper__ctor_m8DA2766F6E2CDC00FA2CF73EAAF21B30AFCD80B1,
	XCommentWrapper_get_Value_m4530DD772239251DC7E9D31647FF1370C502C53B,
	XCommentWrapper_get_ParentNode_m2AE469466627F7255559F9B07E7C9A1F65CCCF76,
	XProcessingInstructionWrapper_get_ProcessingInstruction_m465CA1E4A90DC9B2D5952C18B36D31BBC4FE9F83,
	XProcessingInstructionWrapper__ctor_mF6EF6425C7E1A9D9F46CB988F8C9E542ED2153CE,
	XProcessingInstructionWrapper_get_LocalName_mA87D8E9F6975B75FC134449FCB3B8CC02602ACA3,
	XProcessingInstructionWrapper_get_Value_m7C81865922E146B3EE1E2FB6AE81C2A720327C45,
	XContainerWrapper_get_Container_mE8D578EC87F352A68CADF4E01DFAD5C242BFEDE6,
	XContainerWrapper__ctor_mDA5B56465A0FE70773B861EC590E47653923DFC4,
	XContainerWrapper_get_ChildNodes_mC85E0A4FF1522B40B19AEE06B034DE7C52EF1CA4,
	XContainerWrapper_get_ParentNode_m945C070A2B46FAE7DF88629434EEA8672CF735B0,
	XContainerWrapper_WrapNode_m45080E6A0D0A531566B9B6B20B75328E5AD91CBA,
	XContainerWrapper_AppendChild_mD0C76048C57DBC7BE2A08BC215761BEF2552BC42,
	XObjectWrapper__ctor_m24EB638F7E591225CA536A66FEE9B6EE6D7E8330,
	XObjectWrapper_get_WrappedNode_m3CA9623838D4D4A96DCF882FC3C592D3D64D7573,
	XObjectWrapper_get_NodeType_m0EC8D2F38FAC096C9A1C91B78A107C89FE08B867,
	XObjectWrapper_get_LocalName_mF7CD25C52F1D0EE14C5D9063035C27E1B237F421,
	XObjectWrapper_get_ChildNodes_m43561E35BC5ECC8A22C56C1CB479B4CE638EB5B7,
	XObjectWrapper_get_Attributes_m7C1E335535C8FB2A6685770BE0308503578EEFE5,
	XObjectWrapper_get_ParentNode_mFEE478450A586823D4E4F27B439C3B7CBD3167BE,
	XObjectWrapper_get_Value_mE59398A1F64A5FB46732FF84DF7E263FB201CE38,
	XObjectWrapper_AppendChild_m93FD89B25381B97A651348E158D8E33E4F37821B,
	XObjectWrapper_get_NamespaceUri_m5CD84D5CD1475CAC24C1535FBF1D65147E58987F,
	XObjectWrapper__cctor_mB86D35437E19AAA15DB854855C78FA0DC3686A71,
	XAttributeWrapper_get_Attribute_m64F72E3BA361213F069AF1897D47A9586F6FA2E6,
	XAttributeWrapper__ctor_mCD158BF6A02EA1460BD22E3C99816A3308E2DC39,
	XAttributeWrapper_get_Value_m8B697AE38E2DDBC0E31E7860009689FF5DA89BC2,
	XAttributeWrapper_get_LocalName_m972043B9A5DD764604E5A3929625E04C0451D596,
	XAttributeWrapper_get_NamespaceUri_m6923813F42B9F0ABB8F8B542EFE3BA4B797DABFF,
	XAttributeWrapper_get_ParentNode_mA27F66095E98A673B04FBC961F3023F2D92467C8,
	XElementWrapper_get_Element_mCE45E811C472BF01E89449EAD91DB4C37B5B2074,
	XElementWrapper__ctor_mADC48A43A4BBD8C2FEBF88F4A45ED0CA69FAEC7F,
	XElementWrapper_SetAttributeNode_m87ADF1FE3CAA994A2B35154DB1EA020E530FF195,
	XElementWrapper_get_Attributes_mBCEC2D09F107433A944B9DC2DA694454C4B2FA49,
	XElementWrapper_AppendChild_m7F2CE9197158F3EFF8555A062D195E207530AFAC,
	XElementWrapper_get_Value_m8610D6D1BBD66BFD3DB13666D4FFF031F6C6F561,
	XElementWrapper_get_LocalName_mA47C683BF562D2B6AD547329F99FB15945037BDF,
	XElementWrapper_get_NamespaceUri_m537A5A8F6039B89961825F2EFFBD0D57F8EB0B04,
	XElementWrapper_GetPrefixOfNamespace_m6E8DA9B2D4BAF02CD2660213C9CA4405575F1D19,
	XElementWrapper_get_IsEmpty_mF42A83AA38E3BA1A7F55EC274D4D7ECAB1DDB880,
	XmlNodeConverter_get_DeserializeRootElementName_m2A0B73747601E99F60881D50EF7D55F0EDBE85A4,
	XmlNodeConverter_get_WriteArrayAttribute_mB90AAB6C83E3DFD4E0F0FE8DBB9BF151248378F2,
	XmlNodeConverter_get_OmitRootObject_m39D54F5791FD93911139FB9E46BABBDE5D062184,
	XmlNodeConverter_WriteJson_mC9928FB4E2184CDC9F294BB9AD22AC5F5E8E0CF9,
	XmlNodeConverter_WrapXml_m75EF1038542649689A89FBF5A3F2CED042EBF228,
	XmlNodeConverter_PushParentNamespaces_m72AF63B5C2C7D78F06FA7A495DADCA6759027AA3,
	XmlNodeConverter_ResolveFullName_mC5F07F462B6FE5B4EB555D78255961F40B8C3616,
	XmlNodeConverter_GetPropertyName_mC4EBA7F268695B3CE90E87702E7CDF96ABB01BA5,
	XmlNodeConverter_IsArray_mF83080575E162DED85AA9E7F30384F4FCECF4A48,
	XmlNodeConverter_SerializeGroupedNodes_m2310D6C0E3FA9FCDBFC1677DEFE3CFB237E6BC0E,
	XmlNodeConverter_SerializeNode_mBBB1AA0A7AC24A39675F1B210F51870CE932D6AA,
	XmlNodeConverter_AllSameName_mA177F66B66101491F3DCEAD394E1A1C87F24E8FF,
	XmlNodeConverter_ReadJson_m439264C33B17C4B9AB9EB03EDB14D20DD339FDD1,
	XmlNodeConverter_DeserializeValue_m6D20454FDBBEEE2CABB0ED3EC1BCF8F2E0067AF0,
	XmlNodeConverter_ReadElement_m09C859D452268EBC6B893181FDF83B8CC7BDFAD8,
	XmlNodeConverter_CreateElement_mB62D7411435EBF38A92DDD2E3F37325D14CC5819,
	XmlNodeConverter_AddAttribute_m20D57606286322BD2581F04FDB59C6661BD3177F,
	XmlNodeConverter_ConvertTokenToXmlValue_m1D07C5BEE5206E724B94ACD8231788702F3BBA2D,
	XmlNodeConverter_ReadArrayElements_m3A4E70784167D3D49FB40746E404602CA282B3D6,
	XmlNodeConverter_AddJsonArrayAttribute_m2B9B98BC1EC11FEDB63C71314C1FD3312E20A800,
	XmlNodeConverter_ReadAttributeElements_m9B56D685A23448B661C73F3483B9B4063D55C049,
	XmlNodeConverter_CreateInstruction_m7A5C6039FEC825B248B75841D11C1DB284F78006,
	XmlNodeConverter_CreateDocumentType_m4438D07E2CC5D23244A39CC98EE3F75B74B06FFA,
	XmlNodeConverter_CreateElement_mD8BF7ADE4CB3682433E964C9986042FFE403FE65,
	XmlNodeConverter_DeserializeNode_m7CF6B466118CB9E0CF4CAC90CA34BC769B45229C,
	XmlNodeConverter_IsNamespaceAttribute_mD9C356F879F0FF7F1006E34842F95ADE42128868,
	XmlNodeConverter_ValueAttributes_m2E3B5735173BB3088B0395F6266B6BCFFF7D6090,
	XmlNodeConverter_CanConvert_m2489573A00FFEB412ECFAB91B0CA2EA83C5D1D32,
	XmlNodeConverter__ctor_m5EA79F6E3B52F680FC2F5DA529993033645D62C3,
	BsonBinaryWriter__cctor_mA6BEC2932FB2A544F7527BCB1401C139932B39B2,
	BsonReader_get_DateTimeKindHandling_m7A6ED156AAC7CD3D4343B0779E4ABD90E247333D,
	BsonReader_ReadElement_m51016B8553A73DB79F38CF122BF6292662F4D754,
	BsonReader_Read_mFEBE910985E708117F1DFB084E839D4EC4432C70,
	BsonReader_ReadCodeWScope_m0DA7805CE5A3D1CC83E1554C8015207538D1FF3E,
	BsonReader_ReadReference_mB9A96BD71CDF91482061F6C9074C2A83CB56E5F4,
	BsonReader_ReadNormal_m048652A5459D2B5D052FB068C5EC478734122EBF,
	BsonReader_PopContext_m63825894764D148A8EE7451048A0761FF27BE312,
	BsonReader_PushContext_m1AB78E117227021B3B7F29D571D58BB424B3A7EF,
	BsonReader_ReadByte_mC6BD5BEBF68FF9ECC8741786CE7A62B50CACAAE4,
	BsonReader_ReadType_m1568AE4968EFA67EF81D622164A9AD3665E539DB,
	BsonReader_ReadBinary_m4A4F52AE9037A613C091BF93066D918478C2FECF,
	BsonReader_ReadString_m43756951ED9A74FE856F42E2CF1A1BE2AE341100,
	BsonReader_ReadLengthString_m905B05A13227D4A2ECBFD9379E27D664A8D071AA,
	BsonReader_GetString_mB3FD61F039F2636EE8B6B4BF2CAF84A26E4DDBFC,
	BsonReader_GetLastFullCharStop_m5F12D814D034886A5A2CA2FADA4B8411D0FE90AD,
	BsonReader_BytesInSequence_mFEDFB2E86DEB1DE9F3773FE802BD6D37BBC1A36E,
	BsonReader_EnsureBuffers_mC7DD59EA3A760B60368F4A117D73F4448F706BB1,
	BsonReader_ReadDouble_m4E7917E24274C4A8DE38DAA6DAD13B019AE75C52,
	BsonReader_ReadInt32_m403168A3B3CA3CC5FEA5459185257E8AF00FF986,
	BsonReader_ReadInt64_m3CD355565907B98481732EC6A91102F89CE1FF02,
	BsonReader_ReadType_m609B4018D61DC17479EA954CAB7A4CF38F9ED691,
	BsonReader_MovePosition_mEA8B97A5B0231E2555EB70B8D7C19D4A8D29AD2B,
	BsonReader_ReadBytes_m1CC1F7104F11C353A30134E0AA3D66B3F52938F7,
	BsonReader__cctor_m0DD14E39C1F1B4DE1A4C13B0DEFAE390C729627F,
	ContainerContext__ctor_m1C2D964213E25068CF24D94D74E2D9D5BB60061B,
	NULL,
	BsonToken_set_Parent_m6FE9310A7BE7920BFBBB78D007D3326FE25861BC,
	BsonToken__ctor_m04A0C7B9070DF73C0689038C8E2593C5FC18F8AA,
	BsonObject_Add_mC5FD9CC9FC974FC4D7B10981A33291E88DB9DC79,
	BsonObject_get_Type_m970C3BD8AC7D9844A62BE0C687DCF270E784D8ED,
	BsonObject_GetEnumerator_mD0C11E8BAC09FB62CA08FF674E3F7F23690AE14D,
	BsonObject_System_Collections_IEnumerable_GetEnumerator_m6C7320F7B08967EF6FF0D1EB4681408550A44D7C,
	BsonObject__ctor_m630E32FEFB85ABC73DA890C946486161162E0BDA,
	BsonArray_Add_m483471C0CDB8A4438E1A0274845DC6424BDB765D,
	BsonArray_get_Type_mCC68071DB9C64C070101B2633053632727FB9382,
	BsonArray_GetEnumerator_m38E989DD847BFBA1D9AE6408890EBEC664E0729C,
	BsonArray_System_Collections_IEnumerable_GetEnumerator_m36E9AA3609FC3571D00A9C885C12A578D15432D7,
	BsonArray__ctor_m3121DAA5995AA53B27FCE79E5F69D13131F1F9F1,
	BsonValue__ctor_m316255047E379B29CD5D70DBCDF98BAD4DB4C695,
	BsonValue_get_Type_m02508F5B41591FB05A329FB62FF25DBC56BC200A,
	BsonString_set_IncludeLength_m172F4ADF8CE9080328D6562647339230247B9AB3,
	BsonString__ctor_mA28B714E7D11E7131A16CB152D171CA7A4A6BD56,
	BsonRegex_set_Pattern_m03387AC7A329EEAA6442715EA019B93C7D4A14FF,
	BsonRegex_set_Options_m8A305E5CD6B32F3A48F6BC31203892A26FE967FC,
	BsonRegex__ctor_mA823184E2E1262D62F38DC5D1ACC130B5B0EEE99,
	BsonRegex_get_Type_mE72E10F4DBAAC4714F12F2DC916E927A1EE72C3A,
	BsonProperty_set_Name_mBF75E093501D61ABA9B44CD595A848386002EDA0,
	BsonProperty_set_Value_m7DAC5256E7337131CB0004255D86FBB812E5BAD8,
	BsonProperty__ctor_mFC963BA0F736C7A11FE68BB3A4DDE63A99B3A54C,
	BsonWriter_AddValue_m1EEA7A7873B2D58AAC37EB24AB9CFB3F81DB29A5,
	BsonWriter_AddToken_m3B3692A74D77D31F63999E7E77DD4386B74A9901,
	BsonWriter_WriteObjectId_mF118E0F427F73A1FEAFB3853F056E9BE87524452,
	BsonWriter_WriteRegex_m5A72DD206C8BA845F9F2B6AAC9BF61AC6480B71E,
	BsonObjectId_get_Value_mEBD0BBDDA460C3B1ECFFBD7B64C709172C7F14CD,
	BsonObjectId_set_Value_mCCFD2400AA3F5C8AD279B6270932CE681CB7073A,
	BsonObjectId__ctor_m755CAEE2BE89A3B5A751FE980FB1A333B3D603C8,
};
extern void JsonPosition__ctor_mB2A076189BFC7885AA7876D42D0A612A1119AC90_AdjustorThunk (void);
extern void JsonPosition_CalculateLength_mF8830A4B38109D0C9EBA7F90CF1DFB70B7E08BA1_AdjustorThunk (void);
extern void JsonPosition_WriteTo_mB7CDD918232B0711DCB76F31B31FEBCEDFC86659_AdjustorThunk (void);
extern void DateTimeParser_Parse_m23985D38D15F4AC3CF47CFF85341AE406A302091_AdjustorThunk (void);
extern void DateTimeParser_ParseDate_mCF43EAF9D60C2DF52038EAA7C5653D59C83F9C8F_AdjustorThunk (void);
extern void DateTimeParser_ParseTimeAndZoneAndWhitespace_m36C0C76A6548E07654C45414059FBD5BC95F5BBD_AdjustorThunk (void);
extern void DateTimeParser_ParseTime_m9717B1406E4705AB36B56F41A8F0CAA14B1815DD_AdjustorThunk (void);
extern void DateTimeParser_ParseZone_m4CEBD1A881CB114B6EB7D8050F44EA265A260642_AdjustorThunk (void);
extern void DateTimeParser_Parse4Digit_mC5F981E6CD7CD915FEA858DE77A04AEC04AF4D86_AdjustorThunk (void);
extern void DateTimeParser_Parse2Digit_mF1031EB57E34571DA339FB7B1AE09268293F2633_AdjustorThunk (void);
extern void DateTimeParser_ParseChar_m69C950529AC35A4734A1DCA09ED004ADDDE9452F_AdjustorThunk (void);
extern void StringReference_get_Item_mF157FD35EDF25DC3FB3291BA8A7ACA6A49791EBD_AdjustorThunk (void);
extern void StringReference_get_Chars_mCAEA9DDED5058DE07529C24621E510E396B79A6B_AdjustorThunk (void);
extern void StringReference_get_StartIndex_mC3DD76078312694DB7C297115073EAE930B42925_AdjustorThunk (void);
extern void StringReference_get_Length_m65CF2F68237C0273F5BE4B4B0DCD4247CD940385_AdjustorThunk (void);
extern void StringReference__ctor_mCAEF5A34A8FD029BA4399BDEAD6B9AB67515A5B2_AdjustorThunk (void);
extern void StringReference_ToString_m14E995A62CEC0B0C1313E51D01878B015EB38EF6_AdjustorThunk (void);
extern void TypeConvertKey_get_InitialType_m00D6EF528297F8AA3372C9BD42BD0F02EB64E229_AdjustorThunk (void);
extern void TypeConvertKey_get_TargetType_m61EE1F103B49EAA250ED9D9E97B8A4B3D4DF2925_AdjustorThunk (void);
extern void TypeConvertKey__ctor_mD8FF7E88853BA5DE5876BEE02E0F656E8FB5C736_AdjustorThunk (void);
extern void TypeConvertKey_GetHashCode_m73EEFCBFC5400417D8BE4F835C852F42A20D448F_AdjustorThunk (void);
extern void TypeConvertKey_Equals_m8C696A833AC5997EE1AC57B90E260679C2176069_AdjustorThunk (void);
extern void TypeConvertKey_Equals_mC41E4C78130945D5E3FEFCC700F7D18E62E48BCA_AdjustorThunk (void);
extern void StringBuffer_get_Position_m26F00F0301A7D88C7A0E2199F1E6394C2D1AB61D_AdjustorThunk (void);
extern void StringBuffer_set_Position_m68C6F0EBF858CFCF3D10A8D1D255B24ADA883263_AdjustorThunk (void);
extern void StringBuffer_get_IsEmpty_m0FF70318FF5ED6D3C0E2C1BD3E5B3BDBB7C0A884_AdjustorThunk (void);
extern void StringBuffer__ctor_m0B0B11963A1F7B3F240F8993C116DB8EC5ECE96C_AdjustorThunk (void);
extern void StringBuffer__ctor_mFF5E0ADAA4ABD314C4524F3DC482CF9773932C5B_AdjustorThunk (void);
extern void StringBuffer_Append_m77B388D5627C9D7EBB5C5848F20C396B826253AB_AdjustorThunk (void);
extern void StringBuffer_Append_mCF7546C3A7CED19D47B3AF5ED2E0A2D456AF5DFE_AdjustorThunk (void);
extern void StringBuffer_Clear_m6C24B7B855B0E8F12C3DC20D6191EDB81A0D65A6_AdjustorThunk (void);
extern void StringBuffer_EnsureSize_mB5835295812DA385035C84550B3A9E35FC59E100_AdjustorThunk (void);
extern void StringBuffer_ToString_m693AF2D7D2FCB627284D3A0D79FDA85547407D49_AdjustorThunk (void);
extern void StringBuffer_ToString_mF654D1BD6CCFF512998846C7E7D57C1E6AF6B4DE_AdjustorThunk (void);
extern void StringBuffer_get_InternalBuffer_m74C440CFC916B0D891EBEB6D577BFB518CF891B0_AdjustorThunk (void);
extern void ResolverContractKey__ctor_m81CFB26289389B6A3333FC0524DF4EE5E8E3E1B7_AdjustorThunk (void);
extern void ResolverContractKey_GetHashCode_mA043B0AAF1DD1D96417D00365B90416A8C3A889D_AdjustorThunk (void);
extern void ResolverContractKey_Equals_m0432D1CC6F6ABA61801B0BEED7ECA44B2C9EEED4_AdjustorThunk (void);
extern void ResolverContractKey_Equals_mEE55B3988692D37E4F7F05E91262DC511323872A_AdjustorThunk (void);
extern void TypeNameKey__ctor_mEB5446E840F899A22E0CFE0B3DB83D323EA29AAD_AdjustorThunk (void);
extern void TypeNameKey_GetHashCode_m2818F664C6CD4EA974EBD5D17147C28D1A903A98_AdjustorThunk (void);
extern void TypeNameKey_Equals_m351AC7FD99EF650F601259E1DB999D84DC2E3E6D_AdjustorThunk (void);
extern void TypeNameKey_Equals_m87BCAE319F7BFF2F369E9F8877F04051EB8160AC_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[43] = 
{
	{ 0x06000017, JsonPosition__ctor_mB2A076189BFC7885AA7876D42D0A612A1119AC90_AdjustorThunk },
	{ 0x06000018, JsonPosition_CalculateLength_mF8830A4B38109D0C9EBA7F90CF1DFB70B7E08BA1_AdjustorThunk },
	{ 0x06000019, JsonPosition_WriteTo_mB7CDD918232B0711DCB76F31B31FEBCEDFC86659_AdjustorThunk },
	{ 0x060001C0, DateTimeParser_Parse_m23985D38D15F4AC3CF47CFF85341AE406A302091_AdjustorThunk },
	{ 0x060001C1, DateTimeParser_ParseDate_mCF43EAF9D60C2DF52038EAA7C5653D59C83F9C8F_AdjustorThunk },
	{ 0x060001C2, DateTimeParser_ParseTimeAndZoneAndWhitespace_m36C0C76A6548E07654C45414059FBD5BC95F5BBD_AdjustorThunk },
	{ 0x060001C3, DateTimeParser_ParseTime_m9717B1406E4705AB36B56F41A8F0CAA14B1815DD_AdjustorThunk },
	{ 0x060001C4, DateTimeParser_ParseZone_m4CEBD1A881CB114B6EB7D8050F44EA265A260642_AdjustorThunk },
	{ 0x060001C5, DateTimeParser_Parse4Digit_mC5F981E6CD7CD915FEA858DE77A04AEC04AF4D86_AdjustorThunk },
	{ 0x060001C6, DateTimeParser_Parse2Digit_mF1031EB57E34571DA339FB7B1AE09268293F2633_AdjustorThunk },
	{ 0x060001C7, DateTimeParser_ParseChar_m69C950529AC35A4734A1DCA09ED004ADDDE9452F_AdjustorThunk },
	{ 0x06000213, StringReference_get_Item_mF157FD35EDF25DC3FB3291BA8A7ACA6A49791EBD_AdjustorThunk },
	{ 0x06000214, StringReference_get_Chars_mCAEA9DDED5058DE07529C24621E510E396B79A6B_AdjustorThunk },
	{ 0x06000215, StringReference_get_StartIndex_mC3DD76078312694DB7C297115073EAE930B42925_AdjustorThunk },
	{ 0x06000216, StringReference_get_Length_m65CF2F68237C0273F5BE4B4B0DCD4247CD940385_AdjustorThunk },
	{ 0x06000217, StringReference__ctor_mCAEF5A34A8FD029BA4399BDEAD6B9AB67515A5B2_AdjustorThunk },
	{ 0x06000218, StringReference_ToString_m14E995A62CEC0B0C1313E51D01878B015EB38EF6_AdjustorThunk },
	{ 0x0600023D, TypeConvertKey_get_InitialType_m00D6EF528297F8AA3372C9BD42BD0F02EB64E229_AdjustorThunk },
	{ 0x0600023E, TypeConvertKey_get_TargetType_m61EE1F103B49EAA250ED9D9E97B8A4B3D4DF2925_AdjustorThunk },
	{ 0x0600023F, TypeConvertKey__ctor_mD8FF7E88853BA5DE5876BEE02E0F656E8FB5C736_AdjustorThunk },
	{ 0x06000240, TypeConvertKey_GetHashCode_m73EEFCBFC5400417D8BE4F835C852F42A20D448F_AdjustorThunk },
	{ 0x06000241, TypeConvertKey_Equals_m8C696A833AC5997EE1AC57B90E260679C2176069_AdjustorThunk },
	{ 0x06000242, TypeConvertKey_Equals_mC41E4C78130945D5E3FEFCC700F7D18E62E48BCA_AdjustorThunk },
	{ 0x060002BC, StringBuffer_get_Position_m26F00F0301A7D88C7A0E2199F1E6394C2D1AB61D_AdjustorThunk },
	{ 0x060002BD, StringBuffer_set_Position_m68C6F0EBF858CFCF3D10A8D1D255B24ADA883263_AdjustorThunk },
	{ 0x060002BE, StringBuffer_get_IsEmpty_m0FF70318FF5ED6D3C0E2C1BD3E5B3BDBB7C0A884_AdjustorThunk },
	{ 0x060002BF, StringBuffer__ctor_m0B0B11963A1F7B3F240F8993C116DB8EC5ECE96C_AdjustorThunk },
	{ 0x060002C0, StringBuffer__ctor_mFF5E0ADAA4ABD314C4524F3DC482CF9773932C5B_AdjustorThunk },
	{ 0x060002C1, StringBuffer_Append_m77B388D5627C9D7EBB5C5848F20C396B826253AB_AdjustorThunk },
	{ 0x060002C2, StringBuffer_Append_mCF7546C3A7CED19D47B3AF5ED2E0A2D456AF5DFE_AdjustorThunk },
	{ 0x060002C3, StringBuffer_Clear_m6C24B7B855B0E8F12C3DC20D6191EDB81A0D65A6_AdjustorThunk },
	{ 0x060002C4, StringBuffer_EnsureSize_mB5835295812DA385035C84550B3A9E35FC59E100_AdjustorThunk },
	{ 0x060002C5, StringBuffer_ToString_m693AF2D7D2FCB627284D3A0D79FDA85547407D49_AdjustorThunk },
	{ 0x060002C6, StringBuffer_ToString_mF654D1BD6CCFF512998846C7E7D57C1E6AF6B4DE_AdjustorThunk },
	{ 0x060002C7, StringBuffer_get_InternalBuffer_m74C440CFC916B0D891EBEB6D577BFB518CF891B0_AdjustorThunk },
	{ 0x060003A5, ResolverContractKey__ctor_m81CFB26289389B6A3333FC0524DF4EE5E8E3E1B7_AdjustorThunk },
	{ 0x060003A6, ResolverContractKey_GetHashCode_mA043B0AAF1DD1D96417D00365B90416A8C3A889D_AdjustorThunk },
	{ 0x060003A7, ResolverContractKey_Equals_m0432D1CC6F6ABA61801B0BEED7ECA44B2C9EEED4_AdjustorThunk },
	{ 0x060003A8, ResolverContractKey_Equals_mEE55B3988692D37E4F7F05E91262DC511323872A_AdjustorThunk },
	{ 0x060003FB, TypeNameKey__ctor_mEB5446E840F899A22E0CFE0B3DB83D323EA29AAD_AdjustorThunk },
	{ 0x060003FC, TypeNameKey_GetHashCode_m2818F664C6CD4EA974EBD5D17147C28D1A903A98_AdjustorThunk },
	{ 0x060003FD, TypeNameKey_Equals_m351AC7FD99EF650F601259E1DB999D84DC2E3E6D_AdjustorThunk },
	{ 0x060003FE, TypeNameKey_Equals_m87BCAE319F7BFF2F369E9F8877F04051EB8160AC_AdjustorThunk },
};
static const int32_t s_InvokerIndices[2104] = 
{
	4364,
	2798,
	2802,
	2798,
	2802,
	3881,
	3881,
	2798,
	2802,
	0,
	0,
	4364,
	4364,
	4364,
	3881,
	2802,
	2811,
	4168,
	3807,
	4168,
	3807,
	4364,
	3852,
	4216,
	3881,
	8216,
	7614,
	6693,
	9089,
	4364,
	0,
	0,
	0,
	4364,
	4250,
	4250,
	4364,
	4250,
	4250,
	4216,
	3852,
	4364,
	3852,
	4216,
	4216,
	4216,
	4216,
	3852,
	4216,
	4250,
	3881,
	4216,
	4216,
	4216,
	4216,
	4216,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4315,
	9089,
	4364,
	4364,
	3881,
	4364,
	3852,
	2867,
	5551,
	4364,
	3403,
	2417,
	2294,
	2294,
	4168,
	4077,
	4071,
	4250,
	4250,
	3515,
	3526,
	4069,
	4364,
	3515,
	4072,
	4074,
	4076,
	4364,
	4364,
	4168,
	4364,
	3972,
	2152,
	4349,
	4364,
	4364,
	4168,
	4168,
	4168,
	3270,
	4364,
	4168,
	4364,
	3807,
	3113,
	4364,
	3852,
	3807,
	1933,
	3185,
	3185,
	3270,
	4364,
	4364,
	4364,
	4364,
	3515,
	3515,
	3515,
	4364,
	4168,
	4216,
	4216,
	4250,
	4250,
	4250,
	3881,
	4364,
	3881,
	4364,
	4250,
	4349,
	3881,
	4364,
	4364,
	4364,
	3881,
	3852,
	3881,
	2788,
	4364,
	4364,
	4364,
	4364,
	4364,
	2796,
	3881,
	4364,
	4364,
	3881,
	3881,
	2788,
	3852,
	3973,
	3853,
	3974,
	3928,
	3731,
	3826,
	3721,
	3807,
	3851,
	3972,
	3972,
	3807,
	3919,
	3823,
	3820,
	3881,
	3821,
	3847,
	3964,
	3881,
	3881,
	4364,
	3853,
	3974,
	3881,
	4364,
	2811,
	2084,
	6693,
	6693,
	3852,
	3852,
	3881,
	4364,
	2811,
	966,
	7631,
	6693,
	5959,
	0,
	0,
	0,
	4168,
	4168,
	4364,
	4364,
	4216,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4250,
	3881,
	4077,
	3724,
	4216,
	4250,
	4250,
	4216,
	4250,
	4250,
	3881,
	3476,
	4364,
	3852,
	4216,
	4216,
	0,
	4077,
	2960,
	4250,
	4250,
	4250,
	4076,
	2959,
	4069,
	2955,
	4074,
	2958,
	4071,
	2956,
	4072,
	2957,
	4364,
	4250,
	4364,
	4364,
	3852,
	2739,
	1985,
	3807,
	4364,
	3852,
	4364,
	4364,
	3415,
	4364,
	3807,
	4364,
	4364,
	4168,
	4168,
	4216,
	9031,
	8887,
	9089,
	9031,
	8488,
	8520,
	5986,
	5334,
	5906,
	7591,
	8505,
	8496,
	8505,
	7639,
	6698,
	8505,
	7631,
	6693,
	6693,
	0,
	0,
	6693,
	4364,
	3881,
	2802,
	2811,
	7631,
	6693,
	5959,
	3881,
	3881,
	3881,
	3881,
	4250,
	3881,
	3881,
	3852,
	3852,
	3852,
	3852,
	3852,
	3852,
	3852,
	4216,
	3852,
	3852,
	4216,
	3852,
	4250,
	4250,
	3881,
	4315,
	3939,
	4216,
	4168,
	3807,
	4168,
	4364,
	9031,
	8505,
	9031,
	8505,
	7975,
	2802,
	2802,
	0,
	2524,
	2524,
	348,
	368,
	2084,
	2802,
	2084,
	4250,
	3518,
	7631,
	3881,
	9031,
	9089,
	4168,
	3807,
	4216,
	4216,
	4250,
	4250,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4364,
	4216,
	3852,
	4250,
	3881,
	4250,
	3881,
	4364,
	4364,
	3852,
	4216,
	4216,
	4364,
	4364,
	4364,
	4364,
	4364,
	3881,
	4364,
	3881,
	2788,
	4364,
	3881,
	2788,
	2739,
	1391,
	3881,
	3852,
	4364,
	3415,
	3852,
	3852,
	4364,
	4364,
	4364,
	3852,
	4364,
	4364,
	3881,
	3881,
	3881,
	3852,
	3973,
	3853,
	3974,
	3928,
	3826,
	3807,
	3851,
	3972,
	3972,
	3807,
	3919,
	3823,
	3820,
	3821,
	3847,
	3964,
	3724,
	3736,
	3725,
	3737,
	3731,
	3721,
	3714,
	3723,
	3735,
	3716,
	3715,
	3730,
	3719,
	3717,
	3718,
	3722,
	3732,
	3881,
	3881,
	3881,
	3881,
	4364,
	3807,
	6967,
	7631,
	3852,
	3881,
	4364,
	2734,
	3852,
	4364,
	9089,
	1638,
	3166,
	3166,
	3079,
	3166,
	2293,
	2293,
	2310,
	3881,
	2053,
	4364,
	2053,
	8216,
	8216,
	8216,
	9089,
	4364,
	1793,
	3518,
	2522,
	4364,
	5695,
	2057,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	9031,
	3518,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	9089,
	4364,
	3518,
	3518,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	3881,
	4250,
	3881,
	3881,
	4364,
	4250,
	3881,
	4250,
	3881,
	4364,
	2524,
	3518,
	7631,
	6693,
	4364,
	3518,
	4364,
	3518,
	4364,
	2802,
	3624,
	4250,
	4216,
	4216,
	2053,
	4250,
	5839,
	7315,
	7315,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	3881,
	4216,
	3852,
	4364,
	8399,
	7485,
	8505,
	8220,
	8757,
	8530,
	5698,
	5825,
	6693,
	6693,
	8505,
	7252,
	8220,
	5813,
	5813,
	7252,
	6529,
	8407,
	9089,
	4250,
	4250,
	2802,
	4216,
	3185,
	3299,
	4364,
	3518,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9089,
	8754,
	8395,
	7404,
	8292,
	8292,
	8418,
	7518,
	7518,
	8418,
	7517,
	8423,
	8296,
	6399,
	7314,
	8294,
	5216,
	5197,
	5718,
	5698,
	5717,
	6399,
	5197,
	7314,
	5698,
	6399,
	5534,
	4943,
	6528,
	6159,
	5818,
	5535,
	6107,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8505,
	8505,
	6675,
	6690,
	7631,
	9089,
	9089,
	4364,
	3518,
	3185,
	0,
	0,
	7626,
	7975,
	6687,
	9089,
	7603,
	7261,
	4696,
	5968,
	4216,
	3852,
	4168,
	2796,
	3881,
	2813,
	1449,
	3881,
	2796,
	4250,
	2507,
	4250,
	0,
	0,
	0,
	8220,
	7631,
	6693,
	0,
	0,
	0,
	7626,
	6985,
	7631,
	6692,
	8409,
	8770,
	7194,
	0,
	0,
	7261,
	6693,
	7489,
	8505,
	8505,
	6944,
	8505,
	9089,
	8220,
	8505,
	8220,
	8505,
	6687,
	8505,
	7254,
	8505,
	7616,
	8220,
	8220,
	8505,
	7261,
	7261,
	6365,
	7261,
	6365,
	6365,
	8505,
	6944,
	8505,
	8220,
	8220,
	7631,
	6985,
	7254,
	6354,
	7626,
	7259,
	0,
	0,
	0,
	6690,
	6944,
	8106,
	7631,
	7626,
	6982,
	7626,
	7473,
	6982,
	8505,
	9089,
	4364,
	3185,
	3518,
	3518,
	3185,
	4364,
	3185,
	3185,
	3185,
	4364,
	6693,
	5959,
	5380,
	5015,
	6693,
	8501,
	8106,
	8021,
	0,
	8505,
	8241,
	8241,
	7263,
	7263,
	0,
	0,
	0,
	8399,
	8220,
	8220,
	8220,
	8220,
	8505,
	8220,
	8220,
	8220,
	8220,
	8220,
	6365,
	7261,
	7261,
	7975,
	4364,
	4216,
	3415,
	1988,
	4364,
	0,
	0,
	4250,
	3881,
	4250,
	4250,
	3881,
	4069,
	3714,
	4081,
	3728,
	4086,
	3734,
	3881,
	4216,
	3852,
	4364,
	1988,
	4250,
	3881,
	3881,
	4250,
	4168,
	4077,
	4250,
	4250,
	4074,
	4076,
	4069,
	4071,
	4072,
	4216,
	4250,
	4216,
	4250,
	4250,
	4364,
	4168,
	4216,
	4216,
	3881,
	4250,
	3823,
	3807,
	3807,
	3715,
	3972,
	3881,
	3820,
	3821,
	3826,
	4364,
	4364,
	3928,
	3847,
	3852,
	3853,
	3881,
	3919,
	3851,
	3881,
	3964,
	3973,
	3974,
	3881,
	3972,
	3881,
	4364,
	4364,
	3881,
	4364,
	3881,
	2788,
	4364,
	4364,
	3881,
	3881,
	4364,
	2084,
	0,
	2524,
	3185,
	3419,
	3469,
	3584,
	3518,
	4250,
	3881,
	3881,
	3881,
	4216,
	3852,
	3881,
	9089,
	3881,
	3881,
	2802,
	3518,
	2524,
	2524,
	2084,
	2322,
	4364,
	4364,
	3518,
	2802,
	4216,
	3185,
	3219,
	4364,
	9031,
	4216,
	3852,
	4168,
	4168,
	4168,
	3807,
	4364,
	3807,
	4250,
	3518,
	3518,
	3185,
	3518,
	3518,
	7975,
	3518,
	3518,
	2524,
	2524,
	3518,
	3518,
	3881,
	2802,
	564,
	8220,
	8220,
	3518,
	3518,
	3518,
	3518,
	3518,
	3518,
	3518,
	3518,
	8220,
	8220,
	8220,
	5205,
	8505,
	2522,
	3518,
	2522,
	622,
	3518,
	2077,
	3518,
	3518,
	3518,
	9089,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9089,
	4364,
	3185,
	3185,
	3518,
	3185,
	3185,
	3419,
	4364,
	4364,
	2084,
	4364,
	3518,
	4364,
	3185,
	4364,
	3185,
	8532,
	2524,
	4364,
	9089,
	2802,
	4216,
	3185,
	3300,
	1458,
	4168,
	3807,
	4250,
	3881,
	3881,
	3881,
	3881,
	4168,
	0,
	0,
	0,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4250,
	4250,
	3881,
	4168,
	3807,
	4168,
	3881,
	3518,
	4250,
	2798,
	2811,
	2798,
	2108,
	2798,
	2084,
	2798,
	3518,
	4250,
	3881,
	4250,
	3881,
	4069,
	3714,
	4250,
	3881,
	4250,
	3881,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	3881,
	4168,
	3807,
	3881,
	2811,
	2811,
	2811,
	2811,
	2108,
	8505,
	8505,
	4364,
	2811,
	4364,
	2108,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4250,
	4250,
	3881,
	4168,
	3807,
	4168,
	3881,
	3518,
	4250,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4077,
	3724,
	4250,
	3881,
	4250,
	3881,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4250,
	3881,
	4250,
	4216,
	4069,
	3714,
	4079,
	3726,
	4075,
	3720,
	4081,
	3728,
	4080,
	3727,
	4086,
	3734,
	4250,
	3881,
	4250,
	4250,
	3881,
	4250,
	3881,
	4250,
	4250,
	3881,
	4069,
	3714,
	4086,
	3734,
	4081,
	3728,
	3881,
	4364,
	3881,
	3518,
	3881,
	3518,
	2315,
	2522,
	0,
	0,
	0,
	0,
	4216,
	3852,
	4082,
	3729,
	4250,
	3881,
	4250,
	3881,
	3881,
	4250,
	4250,
	4250,
	3881,
	4250,
	3881,
	3881,
	4168,
	3881,
	4250,
	3881,
	4250,
	1210,
	4364,
	442,
	2322,
	3419,
	4364,
	3881,
	2802,
	3518,
	1796,
	4250,
	2524,
	3518,
	305,
	6372,
	3518,
	1210,
	305,
	112,
	112,
	349,
	1801,
	494,
	3185,
	794,
	442,
	74,
	2084,
	2296,
	2322,
	1795,
	1795,
	2084,
	2084,
	794,
	794,
	1458,
	794,
	1210,
	1210,
	794,
	1210,
	1210,
	1641,
	492,
	794,
	1643,
	2322,
	971,
	1801,
	377,
	2084,
	2027,
	4364,
	4364,
	3185,
	9089,
	4364,
	3518,
	3518,
	3518,
	3419,
	3881,
	2084,
	4250,
	3518,
	628,
	628,
	1040,
	706,
	2322,
	442,
	2802,
	2524,
	6365,
	2084,
	2084,
	2084,
	628,
	277,
	628,
	2084,
	2802,
	2296,
	2296,
	2296,
	628,
	628,
	628,
	625,
	442,
	628,
	696,
	628,
	1206,
	2796,
	1643,
	1643,
	3881,
	3881,
	3881,
	4250,
	3881,
	3881,
	4250,
	3852,
	4250,
	3881,
	3852,
	3852,
	4216,
	3852,
	3852,
	3852,
	3852,
	4216,
	3852,
	3852,
	3852,
	3881,
	4315,
	3939,
	4216,
	4168,
	3807,
	4250,
	3881,
	3881,
	2524,
	2802,
	2084,
	3881,
	0,
	8505,
	8505,
	7486,
	8505,
	7631,
	8505,
	8505,
	8505,
	8505,
	0,
	0,
	0,
	8993,
	9031,
	9089,
	4364,
	3518,
	9089,
	4364,
	3518,
	0,
	0,
	3881,
	2802,
	3518,
	4364,
	0,
	0,
	3881,
	8505,
	3518,
	2802,
	3185,
	4250,
	4168,
	4250,
	4216,
	4364,
	2802,
	4364,
	3185,
	4364,
	3518,
	2739,
	3852,
	3881,
	2739,
	2315,
	4250,
	3419,
	3185,
	9089,
	4216,
	4216,
	4364,
	4364,
	3881,
	3881,
	8505,
	4250,
	2322,
	3419,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9089,
	4364,
	0,
	0,
	4250,
	3419,
	4250,
	4216,
	4364,
	3881,
	3881,
	3185,
	4250,
	2802,
	4216,
	7631,
	0,
	4364,
	3881,
	4364,
	3881,
	4168,
	3185,
	4250,
	4250,
	4050,
	3185,
	2518,
	0,
	1985,
	3852,
	3185,
	3515,
	2739,
	4364,
	2802,
	3185,
	2796,
	7261,
	2802,
	3881,
	3881,
	1985,
	8505,
	4364,
	2802,
	2802,
	4216,
	3419,
	2739,
	3852,
	3515,
	2739,
	3881,
	4364,
	3185,
	2796,
	4168,
	3185,
	3518,
	3419,
	4364,
	3185,
	3419,
	2739,
	4168,
	4168,
	3881,
	3852,
	3515,
	2739,
	2796,
	4216,
	4168,
	4250,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	4364,
	3881,
	3185,
	3419,
	1985,
	2802,
	3881,
	3881,
	4250,
	4216,
	3518,
	3518,
	2802,
	8505,
	7631,
	8505,
	7631,
	8505,
	7631,
	2802,
	3518,
	2522,
	2802,
	3185,
	4250,
	3185,
	2315,
	4250,
	3690,
	4364,
	3062,
	2796,
	4168,
	3062,
	4216,
	4250,
	3881,
	3881,
	4250,
	3518,
	4250,
	4250,
	3518,
	9089,
	4364,
	3852,
	4364,
	4168,
	4364,
	4052,
	4364,
	4250,
	4250,
	4216,
	4364,
	3881,
	3881,
	3185,
	4250,
	8505,
	7631,
	2802,
	3515,
	2739,
	3419,
	3419,
	2739,
	3852,
	4250,
	3881,
	4364,
	3185,
	2796,
	4168,
	3185,
	4216,
	4250,
	3881,
	4168,
	3185,
	4168,
	2961,
	3185,
	3185,
	3881,
	3518,
	4168,
	4216,
	4216,
	4250,
	4250,
	4364,
	4364,
	4364,
	3881,
	4364,
	4364,
	3881,
	3852,
	3881,
	2796,
	2796,
	3881,
	4364,
	4364,
	3881,
	3881,
	3881,
	3852,
	3973,
	3853,
	3974,
	3928,
	3826,
	3807,
	3851,
	3972,
	3972,
	3807,
	3919,
	3823,
	3820,
	3821,
	3881,
	3964,
	3847,
	3881,
	1391,
	4250,
	3881,
	4250,
	0,
	0,
	0,
	0,
	7261,
	4250,
	3881,
	4250,
	3881,
	4250,
	4364,
	4250,
	4250,
	4050,
	4364,
	3881,
	0,
	4250,
	2509,
	8505,
	8505,
	6366,
	8220,
	8302,
	8097,
	8425,
	8100,
	8101,
	8102,
	8103,
	8099,
	8399,
	8370,
	8772,
	8772,
	8220,
	8602,
	8106,
	8105,
	8113,
	8098,
	8110,
	8297,
	8107,
	8111,
	8311,
	8114,
	8115,
	8331,
	8626,
	8505,
	8787,
	8808,
	8352,
	8104,
	8757,
	8112,
	8505,
	4250,
	4250,
	0,
	4250,
	7631,
	3518,
	2524,
	8505,
	7631,
	2802,
	2734,
	4168,
	4216,
	4216,
	4250,
	4250,
	3881,
	0,
	9089,
	2734,
	4250,
	4250,
	4250,
	3881,
	3881,
	3515,
	2739,
	3185,
	3852,
	3419,
	1985,
	3185,
	4364,
	3185,
	4250,
	4216,
	3881,
	2802,
	2802,
	4216,
	7631,
	4250,
	4250,
	3881,
	4364,
	3185,
	2796,
	3185,
	4216,
	4168,
	3419,
	2739,
	3852,
	3515,
	2739,
	4364,
	3852,
	4364,
	4168,
	4250,
	4364,
	4250,
	2796,
	3881,
	3881,
	3185,
	4168,
	6503,
	7489,
	4250,
	8505,
	9031,
	9031,
	7453,
	8377,
	4216,
	4250,
	2802,
	4216,
	7261,
	3185,
	3185,
	4216,
	4250,
	3518,
	2524,
	3419,
	4216,
	3185,
	3625,
	3569,
	3185,
	3382,
	3625,
	3419,
	3635,
	3469,
	3640,
	3584,
	3367,
	3354,
	3348,
	2524,
	4364,
	4364,
	4364,
	4364,
	4364,
	9089,
	4364,
	4364,
	4364,
	3881,
	4364,
	4364,
	4364,
	4364,
	2084,
	3518,
	3881,
	1210,
	3518,
	3185,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	3185,
	4364,
	2084,
	1210,
	3185,
	4168,
	4364,
	8505,
	2084,
	1210,
	3185,
	4364,
	9089,
	2084,
	1210,
	3185,
	4364,
	2084,
	2296,
	2802,
	2084,
	1210,
	3518,
	2524,
	3185,
	4364,
	4168,
	4168,
	3807,
	4364,
	2084,
	1210,
	3185,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4364,
	2084,
	5581,
	1210,
	3185,
	8822,
	8838,
	8853,
	9089,
	2084,
	1210,
	3185,
	4364,
	4250,
	2084,
	1210,
	4364,
	2084,
	1210,
	4364,
	3881,
	3518,
	3518,
	3518,
	3518,
	3518,
	1801,
	1210,
	2524,
	3518,
	2524,
	2524,
	1801,
	4250,
	3881,
	3881,
	3518,
	4168,
	3881,
	4250,
	4250,
	4250,
	3881,
	4250,
	4250,
	4250,
	4250,
	4250,
	3881,
	4250,
	4216,
	4250,
	4250,
	8505,
	4250,
	4250,
	4250,
	3881,
	3518,
	4250,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	3881,
	3881,
	4216,
	4250,
	4250,
	4250,
	3881,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	3881,
	4250,
	3518,
	3518,
	3518,
	3518,
	3518,
	1801,
	1210,
	2524,
	3518,
	2524,
	2524,
	1801,
	4250,
	3518,
	4250,
	3881,
	4250,
	4250,
	4250,
	3881,
	4250,
	4250,
	4250,
	3881,
	4250,
	4250,
	4250,
	3881,
	4250,
	4250,
	8505,
	3518,
	3881,
	4250,
	4216,
	4250,
	4250,
	4250,
	4250,
	4250,
	3518,
	4250,
	9089,
	4250,
	3881,
	4250,
	4250,
	4250,
	4250,
	4250,
	3881,
	3881,
	4250,
	3518,
	4250,
	4250,
	4250,
	3518,
	4168,
	4250,
	4168,
	4168,
	2084,
	3518,
	2802,
	2524,
	2524,
	3185,
	1454,
	1454,
	8220,
	1210,
	971,
	971,
	388,
	5131,
	3518,
	971,
	2802,
	2524,
	1458,
	2084,
	1210,
	1458,
	2315,
	3185,
	3185,
	4364,
	9089,
	4216,
	4250,
	4168,
	4168,
	4168,
	4168,
	4364,
	3881,
	4168,
	3919,
	3498,
	4250,
	4250,
	3515,
	3415,
	3403,
	4364,
	4190,
	4216,
	4217,
	4292,
	3852,
	3515,
	9089,
	3919,
	0,
	3881,
	4364,
	2802,
	4292,
	4250,
	4250,
	4364,
	3881,
	4292,
	4250,
	4250,
	4364,
	2809,
	4292,
	3807,
	2788,
	3881,
	3881,
	2802,
	4292,
	3881,
	3881,
	4364,
	2809,
	3881,
	3881,
	2802,
	4250,
	3881,
	3881,
};
static const Il2CppTokenRangePair s_rgctxIndices[54] = 
{
	{ 0x02000047, { 44, 2 } },
	{ 0x02000048, { 46, 2 } },
	{ 0x02000049, { 48, 2 } },
	{ 0x0200004A, { 50, 2 } },
	{ 0x0200004B, { 52, 2 } },
	{ 0x0200004C, { 54, 2 } },
	{ 0x02000055, { 56, 13 } },
	{ 0x02000056, { 69, 27 } },
	{ 0x0200005F, { 96, 24 } },
	{ 0x02000062, { 120, 49 } },
	{ 0x02000063, { 169, 12 } },
	{ 0x02000064, { 181, 7 } },
	{ 0x02000067, { 188, 3 } },
	{ 0x02000075, { 244, 4 } },
	{ 0x0200008B, { 250, 5 } },
	{ 0x0200008C, { 255, 14 } },
	{ 0x020000B3, { 280, 9 } },
	{ 0x020000C3, { 298, 3 } },
	{ 0x020000C4, { 301, 3 } },
	{ 0x020000C5, { 304, 3 } },
	{ 0x020000C6, { 307, 3 } },
	{ 0x020000C9, { 310, 3 } },
	{ 0x020000CE, { 313, 9 } },
	{ 0x020000F7, { 323, 4 } },
	{ 0x0600011C, { 0, 2 } },
	{ 0x0600011D, { 2, 2 } },
	{ 0x0600014A, { 4, 2 } },
	{ 0x060001D7, { 6, 3 } },
	{ 0x060001D8, { 9, 3 } },
	{ 0x060001E3, { 12, 6 } },
	{ 0x060001E4, { 18, 6 } },
	{ 0x060001E5, { 24, 5 } },
	{ 0x060001E6, { 29, 5 } },
	{ 0x060001E7, { 34, 5 } },
	{ 0x060001E8, { 39, 5 } },
	{ 0x060002C8, { 191, 2 } },
	{ 0x060002C9, { 193, 8 } },
	{ 0x060002CA, { 201, 4 } },
	{ 0x060002CE, { 205, 7 } },
	{ 0x060002CF, { 212, 4 } },
	{ 0x060002D0, { 216, 4 } },
	{ 0x060002FF, { 220, 2 } },
	{ 0x06000300, { 222, 5 } },
	{ 0x06000301, { 227, 5 } },
	{ 0x0600031F, { 232, 12 } },
	{ 0x0600038B, { 248, 2 } },
	{ 0x06000538, { 269, 3 } },
	{ 0x06000542, { 272, 2 } },
	{ 0x06000543, { 274, 2 } },
	{ 0x06000544, { 276, 4 } },
	{ 0x06000578, { 289, 2 } },
	{ 0x06000579, { 291, 3 } },
	{ 0x0600057A, { 294, 4 } },
	{ 0x060006A4, { 322, 1 } },
};
extern const uint32_t g_rgctx_JsonConvert_DeserializeObject_TisT_tB4C2D0C9AF72D3EF0F1EE7B34B357E60B5B2A90B_mC88F5E3C4ED180D2173C392D47DC60EF19D39510;
extern const uint32_t g_rgctx_T_tB4C2D0C9AF72D3EF0F1EE7B34B357E60B5B2A90B;
extern const uint32_t g_rgctx_T_tFC24B322A0E8D8DF289F3474AAA5BB3AE5CF46EC;
extern const uint32_t g_rgctx_T_tFC24B322A0E8D8DF289F3474AAA5BB3AE5CF46EC;
extern const uint32_t g_rgctx_T_t8E6E0AAAC61E7EDFC06BC16D4F5C9EA6CD8E541D;
extern const uint32_t g_rgctx_T_t8E6E0AAAC61E7EDFC06BC16D4F5C9EA6CD8E541D;
extern const uint32_t g_rgctx_ReflectionDelegateFactory_CreateGet_TisT_t84C5B25C8EBB35B85DBCE23D9331B8A5C9EAA66F_m8EEA954054FA15F53C3B6AA30D98C23411500CCA;
extern const uint32_t g_rgctx_Func_2_tA13539B16404FC3B9BEF0B93110861D0D6A1C1A2;
extern const uint32_t g_rgctx_ReflectionDelegateFactory_CreateGet_TisT_t84C5B25C8EBB35B85DBCE23D9331B8A5C9EAA66F_m81AD14CC8B0DA2A63767C9F3842B0295FD1DDC56;
extern const uint32_t g_rgctx_ReflectionDelegateFactory_CreateSet_TisT_t0753216FB7B34643EB64CBD487B35138E33967CD_mA56F1470169FF09AF67057A6272846D91D526C2E;
extern const uint32_t g_rgctx_Action_2_tBD4A56E25F7B9044790D64F3AF4FF8A480C9A4EF;
extern const uint32_t g_rgctx_ReflectionDelegateFactory_CreateSet_TisT_t0753216FB7B34643EB64CBD487B35138E33967CD_m53B30B271434D787B0B36D8726E1A78733C64020;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_1_tF2F51900189C40700A781B34F00527D0B9B328F5;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_1__ctor_m555AB86D811682B1062C8A376AA2F49F2F2C7E45;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_1_U3CCreateMethodCallU3Eb__0_mDADC79CA8DB21DD9BD95C86374911BAA87F8D21E;
extern const uint32_t g_rgctx_MethodCall_2_t96A0DBF7511355270E25F7634B07A12493163FC0;
extern const uint32_t g_rgctx_MethodCall_2__ctor_m9860AFAC7DF62F4A2D9329B040E6E524250BECAC;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_1_U3CCreateMethodCallU3Eb__1_m3F32F7D262B50DA8420DE5FCB6AA0A477C5CC773;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1_t98DCD25472FFA6351145C0ABD7C1FB0D637EFD28;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1__ctor_m2FFEA8030BBC582CF5C9C6371B58934F89574BF5;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1_U3CCreateDefaultConstructorU3Eb__0_m697ECA5F10AB4A8F5ABE793A7755F04978C40DDC;
extern const uint32_t g_rgctx_Func_1_t3D1F48A0525F19F99C71823E4BA8167D8B0DBBD4;
extern const uint32_t g_rgctx_Func_1__ctor_m7A2AFD08713C11F0A3FEBF7B51B9F55E4EB34E81;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1_U3CCreateDefaultConstructorU3Eb__1_mCB011F2FE99846147E0BB8178DCE134DA88D7AAE;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass6_0_1_t7A9B8FBD7288DED7E132E2939CA8E91E58C9A5C8;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass6_0_1__ctor_mEC93825FE16FD6D1D8D0286256C0EB23180BB1BD;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass6_0_1_U3CCreateGetU3Eb__0_mBB4F37239F3CED04B3D698D8DB824DE334D77FCF;
extern const uint32_t g_rgctx_Func_2_t07F730F86E6E711EF9C969E71B1082DFCF318949;
extern const uint32_t g_rgctx_Func_2__ctor_m04DB229AA1B4AA25BAE640F5930EC12C08BF1B8A;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass7_0_1_t7FE750E34146B3E178396DEFC71E51F797AFC51E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass7_0_1__ctor_mB048BA74144DBC8ACE09BE9822CB0CC15D3B516F;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass7_0_1_U3CCreateGetU3Eb__0_mD1C801DC71B41C146C48D134C4122A135391CED5;
extern const uint32_t g_rgctx_Func_2_t3968C36B63D8587F2854FAC48056DF1E6573A990;
extern const uint32_t g_rgctx_Func_2__ctor_m24DEE5C7F5985CBAC7AE71BAFA7379A7D918C1A7;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass8_0_1_tEC3B3C1F2BAA65CA4C96138B4D600523E06A13E2;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass8_0_1__ctor_mA5DFFC75875145DFC7B6E3EA5FE9256325BFBC91;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass8_0_1_U3CCreateSetU3Eb__0_mCB35323FA9E593CB29F1F11BE8D3F499622A111E;
extern const uint32_t g_rgctx_Action_2_tF8D52EB4908901C10B988A6A96B3C75340885332;
extern const uint32_t g_rgctx_Action_2__ctor_m8C95C2F874660409B85ADBE1187EFDB9ABAF8FA2;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0_1_t8E38DAD74D8C34CC2D589235E8BAB54FDB84E615;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0_1__ctor_m9AF3AC4609404145CA480A69FB2072846E3BEF6E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0_1_U3CCreateSetU3Eb__0_m3E63FE2699004618906D952F543F52DF28E2DECF;
extern const uint32_t g_rgctx_Action_2_t3A28C11AA563FF379FFF905BC5A3934C8322C4E0;
extern const uint32_t g_rgctx_Action_2__ctor_mE08051936490CF87088D9C1B61D5DF1E47D8D660;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass4_0_1_tE8EBFCD23A74FC478C1FB4CA8A8B2F991C5F60AC;
extern const uint32_t g_rgctx_T_t1C6C29770DE2425BF8AE6668D2FEDECA21F56B03;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1_tFFD63FB3CC36980AE139B0F45BB96372E0FFFDE9;
extern const uint32_t g_rgctx_T_tF2C1711889BD4D6520CF49F80A1B44BC8D440AF3;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass6_0_1_t8B7CFF617E3F09B96459009A4FBCB26F02CF8855;
extern const uint32_t g_rgctx_T_t8B4FEDF962E88AFF9FF3AAE3ADA9BA7F802431FF;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass7_0_1_tF531960C785108782D0474B9139E951CDC9D55C5;
extern const uint32_t g_rgctx_T_t636A8BA499D4E9C6902A0F4FBBD80E4081BC6840;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass8_0_1_t9E4DE71BD065F5DC1E655FCF1870CAAD90245239;
extern const uint32_t g_rgctx_T_tB9EDEF3FABADA50917BBFE15A8EE143F0CF823C6;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0_1_tA4F3C7B2CF1F57DA227759A91B61C91A8B6F25F3;
extern const uint32_t g_rgctx_T_tA67262C37BB27C8FD352FE2324D74C0CF8E41C48;
extern const uint32_t g_rgctx_ThreadSafeStore_2_tA013278B46D9C588BC936F8B5F0323C1BE3B2483;
extern const uint32_t g_rgctx_Func_2_t9EF31A1130B13E2B4F1C5F5914BAD1F90A7D0BE1;
extern const uint32_t g_rgctx_Dictionary_2_t3786EBE9B4F356DF6225FAF89C41DF9DE497EB6A;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mD2D37BB7A5DB1A3090848FF60FB4105208801C46;
extern const uint32_t g_rgctx_TKey_t31D369D27EBF0CB01814C8A0172653F7BBA956C2;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_mC702FC9FBEB54DDBA1CE82E20071D8CAE53B4AB9;
extern const uint32_t g_rgctx_TValueU26_t8DDE0F5D128A7BC06A41AEBEB5CE5548237B7BDB;
extern const uint32_t g_rgctx_ThreadSafeStore_2_AddValue_m31E6AB74E2A9D801875CF77CF25BFAFA78C5D58F;
extern const uint32_t g_rgctx_TValue_t38DC6552FCD57AB80FF65FBC19B31BACB5D7C5DC;
extern const uint32_t g_rgctx_Func_2_Invoke_mB6D43989AF0D34DF472A9B579FD075890BA5D270;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_m46FE233BF07C21C90259E58B74164CC36144F036;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m7D35B02251D6EDF2CA5E9C68E8D896E5099ED2AD;
extern const uint32_t g_rgctx_IDictionary_2_tB5144F6213557E4312A4498CB0D072E399AA59C1;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m249EA97EB0DFF627D2234B67C873779AE986AC94;
extern const uint32_t g_rgctx_EqualityComparer_1_t10602A1710C3E7BA3038609AC44D39AB15021980;
extern const uint32_t g_rgctx_EqualityComparer_1_t10602A1710C3E7BA3038609AC44D39AB15021980;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_mC61F84FFCFFBC11A8F21DE180DD1D85B002A1C62;
extern const uint32_t g_rgctx_EqualityComparer_1_tB98A26DC214EC99A55AFF54CAE94D9E3792E1596;
extern const uint32_t g_rgctx_EqualityComparer_1_tB98A26DC214EC99A55AFF54CAE94D9E3792E1596;
extern const uint32_t g_rgctx_BidirectionalDictionary_2__ctor_m28E6180A74417FD1F89E3DF050ECB0EE5E51D516;
extern const uint32_t g_rgctx_IEqualityComparer_1_tFB244D9E319C5579DB6A5F164A8A7E4BDF24697E;
extern const uint32_t g_rgctx_IEqualityComparer_1_tD7963063935AD59E957B990AF878CBE4747A1D5F;
extern const uint32_t g_rgctx_BidirectionalDictionary_2__ctor_mA945E8841E167FD6F97FC672A41ED8489A1EEB08;
extern const uint32_t g_rgctx_Dictionary_2_t766EC3C35526225D43A8F57308255491939C978A;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mE01E513D283733D35CB848D941D15E0C6EA32CA2;
extern const uint32_t g_rgctx_BidirectionalDictionary_2_tDBE00B2EE98ECB66AB9F513CB0F6D5B86F96EA86;
extern const uint32_t g_rgctx_IDictionary_2_t98A4E667E3ECDD5F8D25EC1C74730BEC0C7AD096;
extern const uint32_t g_rgctx_Dictionary_2_tAC44BAA5C635D6A3C34B250BD0F69B7E7BA998E0;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m23C2DB594E1F210AC24B94991C079B8B35E4817A;
extern const uint32_t g_rgctx_IDictionary_2_tF8988B9319B0FE9421E27AD344FA88C84E3E8735;
extern const uint32_t g_rgctx_TFirst_t3EBC8F9FAA67A448006D4FD551BCA19C770B2E18;
extern const uint32_t g_rgctx_IDictionary_2_TryGetValue_m848FAA148998BA3476DA261E968E539649311DCB;
extern const uint32_t g_rgctx_TSecondU26_tF730F4D117B9E9419839D92E685FAD26123B3467;
extern const uint32_t g_rgctx_TSecond_t2220931A411DB9B732F9FC1AC3C7972048AC4553;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSecond_t2220931A411DB9B732F9FC1AC3C7972048AC4553_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_IDictionary_2_TryGetValue_m7677ADE9080CAE7FD40F29340BB069C5E394C1F8;
extern const uint32_t g_rgctx_TFirstU26_tA23E8BFB272BF7E4ACCAF0A7A011BBF3A8DCF460;
extern const Il2CppRGCTXConstrainedData g_rgctx_TFirst_t3EBC8F9FAA67A448006D4FD551BCA19C770B2E18_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_IDictionary_2_Add_m9DA5BE30F4B073B4E5429B648F1D44C2BB419A21;
extern const uint32_t g_rgctx_IDictionary_2_Add_m55659D5D8D25C871503CBD493BC4F4991C16DF3B;
extern const uint32_t g_rgctx_CollectionWrapper_1_tD9EE0C56BD676066903D1C0F777D9A5CA2993B37;
extern const uint32_t g_rgctx_ICollection_1_t45FF459022F458A6939D30A2C82D2F07CDEBB1FD;
extern const uint32_t g_rgctx_T_t36913CC53A530D328EC311AE7CBD72A57DC8F5A1;
extern const uint32_t g_rgctx_ICollection_1_Add_mAFED7ECFDBDE1303A9A0BEBB67DEB8C9EDBD6204;
extern const uint32_t g_rgctx_ICollection_1_Clear_m8CC3DF0CCBE0540DFBE688C88E0E5FDBAC24D192;
extern const uint32_t g_rgctx_ICollection_1_Contains_m2A01986D7CAAEE833D6E31610640DFC2CD3D8924;
extern const uint32_t g_rgctx_TU5BU5D_tAC000EDCDAB3859EBF25179530C5C6756374543E;
extern const uint32_t g_rgctx_ICollection_1_CopyTo_mFD45D80D4D5F16847D51E5CF1092A5F0977A2F15;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mCAEE2A4AA32BF9168C42B73941B1BF9DFBD5388B;
extern const uint32_t g_rgctx_ICollection_1_get_IsReadOnly_m032CDFF0CE98A6BC2EB55B6AE1B32B709790BB8E;
extern const uint32_t g_rgctx_ICollection_1_Remove_m93FC0D64D4521DD164E28399C91A997E3E299232;
extern const uint32_t g_rgctx_IEnumerable_1_t228699DBA24EF1F60F7E49CEF5C50BC9CA5501B0;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_mD22A399361B88AE7E9018CDE419EA883AEF15B7B;
extern const uint32_t g_rgctx_IEnumerator_1_tA41C303C1B668608A48C1F8EA1DF5FD8237E54C0;
extern const uint32_t g_rgctx_Enumerable_Cast_TisT_t36913CC53A530D328EC311AE7CBD72A57DC8F5A1_mD29AEE4C49ABDB11840CD66FEFE2BE3308A59CF5;
extern const uint32_t g_rgctx_CollectionWrapper_1_VerifyValueType_mC16F2E030E74B2170579111F92FD5DC13E8B7971;
extern const uint32_t g_rgctx_CollectionWrapper_1_tD9EE0C56BD676066903D1C0F777D9A5CA2993B37;
extern const uint32_t g_rgctx_CollectionWrapper_1_Add_m385B815ABEA9D3E6B23A38F03B882F142BFD4F0A;
extern const uint32_t g_rgctx_CollectionWrapper_1_get_Count_mF8181F95FA60EC8F5587453AC9E8385E22FFBB2A;
extern const uint32_t g_rgctx_CollectionWrapper_1_IsCompatibleObject_m8CC5200FCE7FF6AC185A969E402BD1A9E7C0BBC0;
extern const uint32_t g_rgctx_CollectionWrapper_1_Contains_m7F42EC2AFC1FA324F2C056FA13047C728D9B070A;
extern const uint32_t g_rgctx_CollectionWrapper_1_Remove_m94D2E1FECF4C28E10B3F298E3794682F7C95AE41;
extern const uint32_t g_rgctx_CollectionWrapper_1_CopyTo_m5BD06781F1BAA11B653CE60FB6BF75C98297FE41;
extern const uint32_t g_rgctx_T_t36913CC53A530D328EC311AE7CBD72A57DC8F5A1;
extern const uint32_t g_rgctx_DictionaryWrapper_2_t8449C6CF5EF0C0EA365D19E3B6C3F94E3E97148A;
extern const uint32_t g_rgctx_TKey_tC9B2329877B3A856BD0D9F754469EBA4B4841835;
extern const uint32_t g_rgctx_TValue_tE6D0DC974D710E3752A1AA2366E2F980EC738A8D;
extern const uint32_t g_rgctx_IDictionary_2_t63777991796394D5CAF070D07D9E977EC10839FF;
extern const uint32_t g_rgctx_IDictionary_2_Add_m16997B426B448B077A1FF837E36FC761C353B13A;
extern const uint32_t g_rgctx_IDictionary_2_ContainsKey_mDB0E0CB698F0DB525BAA0D21F59F8BF12493D1FC;
extern const uint32_t g_rgctx_Enumerable_Cast_TisTKey_tC9B2329877B3A856BD0D9F754469EBA4B4841835_m5A83D476C3B41272BF3AB37E7AD87FE35236C79A;
extern const uint32_t g_rgctx_IEnumerable_1_tBBC0B34109B3D6CAA4EFDB03D5A419ABCE89139C;
extern const uint32_t g_rgctx_Enumerable_ToList_TisTKey_tC9B2329877B3A856BD0D9F754469EBA4B4841835_m694F11D24E3F6C56CE0C5473E48D9DB687D1D460;
extern const uint32_t g_rgctx_List_1_t51C79FB8E701C1A05E4F742D97CDD6D6E4D47641;
extern const uint32_t g_rgctx_IDictionary_2_get_Keys_mB35DB773012FB465C88D0A4D8609254566B8CD10;
extern const uint32_t g_rgctx_ICollection_1_t73FCCA2ED066CDAA210DA6B982F16F35EE00AF0D;
extern const uint32_t g_rgctx_IDictionary_2_Remove_m0586013BDC788C3FB708759E348025CC0B5FFF3C;
extern const uint32_t g_rgctx_TValueU26_t2F3295F795D48A4E01BE4CCD658620BF4258624B;
extern const uint32_t g_rgctx_IDictionary_2_TryGetValue_m4CBE96F549DA53F44CDC06BDB34497919F2E3E26;
extern const uint32_t g_rgctx_Enumerable_Cast_TisTValue_tE6D0DC974D710E3752A1AA2366E2F980EC738A8D_m5A7D27F6AEDF0CADC98F075D0ACF5285B09B047B;
extern const uint32_t g_rgctx_IEnumerable_1_t195C12B7B64D775D6BBC9573B87075C0B256C24F;
extern const uint32_t g_rgctx_Enumerable_ToList_TisTValue_tE6D0DC974D710E3752A1AA2366E2F980EC738A8D_m026AD43A7D8E2024E9CE9CCA72AA0F4F7C7D8C33;
extern const uint32_t g_rgctx_List_1_t98C7EA5E3CBD843A92178E4EA4573F67260713CC;
extern const uint32_t g_rgctx_IDictionary_2_get_Values_m5B849A52E5D5B54060935CB1D8F96D04424834D5;
extern const uint32_t g_rgctx_ICollection_1_t9A5E232BC725F71CB337060D37536DFAFA155F32;
extern const uint32_t g_rgctx_IDictionary_2_get_Item_mE26F7FA8C35EE97955FA771AFA9380F83A1C1FA5;
extern const uint32_t g_rgctx_IDictionary_2_set_Item_m544FD80D493F6EEA57A072299A44FA4DDC4D1AD5;
extern const uint32_t g_rgctx_KeyValuePair_2_tA40DC4C981917B3F0FE2A094B267CD4E8DC580FC;
extern const uint32_t g_rgctx_ICollection_1_tA703BED8D325CE1BD8A07F0F18DD341EA92F7BE5;
extern const uint32_t g_rgctx_ICollection_1_Add_m27621D6F58B370093277A102113A382F72897556;
extern const uint32_t g_rgctx_ICollection_1_Clear_mECD1CC99CF692FF0FBCD14B63CF3F911AAA95D17;
extern const uint32_t g_rgctx_ICollection_1_Contains_mDF61F7FEC4CDED370F579ADB77D45AD88AFB7E50;
extern const uint32_t g_rgctx_KeyValuePair_2U5BU5D_tD1F21287316368F5FD017FC51DB393D9A6EA0DBF;
extern const uint32_t g_rgctx_KeyValuePair_2__ctor_m5A84BA9E694F5A8461BD66DB20C3CE7090A0D9E3;
extern const uint32_t g_rgctx_ICollection_1_CopyTo_m2CBF832BF7B8BB02CF98692C53D9A341A9FA9377;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mD48DBEC07A2650C7DB2AC5BDC74941524A4B261C;
extern const uint32_t g_rgctx_ICollection_1_get_IsReadOnly_m74AE86E46CEBCA23A0653D3E0A149315E033CB27;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m529178743739FEC4029CB9FA827E231269FAC913;
extern const uint32_t g_rgctx_KeyValuePair_2_tA40DC4C981917B3F0FE2A094B267CD4E8DC580FC;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_m61F299A77333D0011BA09B5F5CFC33EDF7F4DEC2;
extern const uint32_t g_rgctx_ICollection_1_Remove_m8B02EA007796E810582275E02CDAFC883E85F1AF;
extern const uint32_t g_rgctx_U3CU3Ec_tF3D48710CE0160D944BBC62F7FDDEECBAA08F451;
extern const uint32_t g_rgctx_Func_2_t12671B94C25AF4B0F8850638FB36C5FE8B65CD4E;
extern const uint32_t g_rgctx_U3CU3Ec_tF3D48710CE0160D944BBC62F7FDDEECBAA08F451;
extern const uint32_t g_rgctx_U3CU3Ec_U3CGetEnumeratorU3Eb__25_0_m4C9A0592CFB83847F6A85558C061E989F304734A;
extern const uint32_t g_rgctx_Func_2__ctor_mFD93F1976D6638E06B8BB101E1D655A8F7B07A3A;
extern const uint32_t g_rgctx_Enumerable_Select_TisDictionaryEntry_t171080F37B311C25AA9E75888F9C9D703FA721BB_TisKeyValuePair_2_tA40DC4C981917B3F0FE2A094B267CD4E8DC580FC_mAFA609BEFAE7E17B33AC3BF4DD45AFF18DFFD640;
extern const uint32_t g_rgctx_IEnumerable_1_t38176E7B3C89132E55F43A75017F8ACD944AC435;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_mDA91D7764D131765588D54972FFC300C8DAF2C43;
extern const uint32_t g_rgctx_IEnumerator_1_t25AF92E421A5040966DC3428B1D841281C3E563D;
extern const uint32_t g_rgctx_DictionaryWrapper_2_GetEnumerator_m00E8587D4E6E8CEB6C080DF4F02275FA4CEDF2F0;
extern const uint32_t g_rgctx_DictionaryEnumerator_2_t44BB0FABECBB27E58D64E11A88BDAD1798AC8AB1;
extern const uint32_t g_rgctx_DictionaryEnumerator_2__ctor_mDD52A239017E5A4307E785DA600886555D4A1C0C;
extern const uint32_t g_rgctx_IEnumerator_1_t0A9D6EBF79FE0F00733575CF254BEB71CC2C5CDF;
extern const uint32_t g_rgctx_DictionaryEnumerator_2_tC874AB94E415EDEA7382D290FFD5955B400C0A71;
extern const uint32_t g_rgctx_DictionaryEnumerator_2_get_Current_m9F79264C0D24091C3C39F36E63C14D2CA37519EA;
extern const uint32_t g_rgctx_DictionaryEnumerator_2_tC874AB94E415EDEA7382D290FFD5955B400C0A71;
extern const uint32_t g_rgctx_DictionaryEnumerator_2_get_Entry_m0619BB4D232AFD1D83D2A33CBCBFF72DB8CA041D;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m2EE95FA0DEE34A6014BB28A2AD0CDFAF88F0C25E;
extern const uint32_t g_rgctx_KeyValuePair_2_t84DB2F95E252B7EFA71765F7BC6D77A3E707CD2B;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m39ED019909B69E5F63A8F0412C0189E8CCD0D3BA;
extern const uint32_t g_rgctx_KeyValuePair_2_t84DB2F95E252B7EFA71765F7BC6D77A3E707CD2B;
extern const uint32_t g_rgctx_TEnumeratorKey_t5CA50B980800A5B5C3AE9D67CA933D6125809DC1;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_mF4D1A12291E79F4FD5F0E5846977CF14754C22F0;
extern const uint32_t g_rgctx_TEnumeratorValue_t98CD17B5F06DC5DE05FDE6AD4DCDDB7205E93710;
extern const uint32_t g_rgctx_U3CU3Ec_tA55F00B2414663FE1EED17E0DDE3E7AAF9222FC0;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_m1CE1BCB6869DFFBDB8F8568392824181C757452F;
extern const uint32_t g_rgctx_U3CU3Ec_tA55F00B2414663FE1EED17E0DDE3E7AAF9222FC0;
extern const uint32_t g_rgctx_TKey_t056087ABC03BDE7CA66814438C8A3F0FFDD8C40D;
extern const uint32_t g_rgctx_TValue_tB558134B8CDA2BA5337D989565A8ADB951D442C5;
extern const uint32_t g_rgctx_KeyValuePair_2_tD60E2B802B9C94871DF3B39C628424AD431C12A5;
extern const uint32_t g_rgctx_KeyValuePair_2__ctor_m571DB6E6B18D3368954AD12EE74283ECCDDE6857;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_t01EBFCD988C9A362E96B2E97D71534D8D0831BEA;
extern const uint32_t g_rgctx_U3CU3Ec__2_1__ctor_mDA51833A850FE59551EECBFAD03B419739614D3C;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_t01EBFCD988C9A362E96B2E97D71534D8D0831BEA;
extern const uint32_t g_rgctx_ICollection_1_tFA02801D53AE75F1C2C81EA868B033BD683AD29E;
extern const uint32_t g_rgctx_ICollection_1_get_Count_m07A925DDC31D9E7C0D344E7A811B1E6EC8781F99;
extern const uint32_t g_rgctx_IList_1_t1734055875A7DCEFA0D2F42FE4CCAF7A83E7461A;
extern const uint32_t g_rgctx_IEnumerable_1_tADA2A73F1DDBCDD22DAF4F1D489743EABD0E6558;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m6E43948D19D017D4716799317595F5FC49D89067;
extern const uint32_t g_rgctx_IEnumerator_1_tC677508A9E30A48A26FB815B1FB548C5521794FC;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m83ECD482FD9678646EAC4C24A051C0E290915D7B;
extern const uint32_t g_rgctx_T_t2ECF4781334FD2E3D88B5802520717613E888A4D;
extern const uint32_t g_rgctx_ICollection_1_tF8F68924B96615EE0EC63DD6D8B38FF844FE57B9;
extern const uint32_t g_rgctx_ICollection_1_Add_mCE727B856DA454D0EE36C29670B9B09F6A6E104B;
extern const uint32_t g_rgctx_IList_1_t4AF6E02E9D69F67FE045E53D30F01CC59955F607;
extern const uint32_t g_rgctx_Enumerable_Cast_TisT_tCAA74E4CF485AAD390590EB27D4DBB5FADBCCF7A_mFEF613262BB0B081E8A525E31AD07FBD3D9F5A50;
extern const uint32_t g_rgctx_IEnumerable_1_tCF2F11B1D0B5A702B68B2625D9E44A94550FA981;
extern const uint32_t g_rgctx_CollectionUtils_AddRange_TisT_tCAA74E4CF485AAD390590EB27D4DBB5FADBCCF7A_m311A64973762223CF7B375CE9761FC03BABBCBF2;
extern const uint32_t g_rgctx_IEnumerable_1_t1670BCE9B592A5DE9B853B3CBD53B9461D4F62CF;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m370D5C76F5338A4DA102FAB824A3D9FD90FE6D98;
extern const uint32_t g_rgctx_IEnumerator_1_t2B8FBED12A02A6869AF3A4B20E90593686ABAE7F;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_mC35FF539DF496AE0493B148D2F801C1240209375;
extern const uint32_t g_rgctx_T_t7933D7BCDE0829525502B2D2273E7E6946C8CC9A;
extern const uint32_t g_rgctx_Func_2_t4ED35AB3588BA208B5B1F45D478EECB049F0A0B7;
extern const uint32_t g_rgctx_Func_2_Invoke_m105F88998904F595A94AEC6C3BE67C334422A351;
extern const uint32_t g_rgctx_T_tC6385C0CCA75B41ED8F6A7F44047AB590DE61679;
extern const uint32_t g_rgctx_List_1_t772F9030BE800826AC42C960FA33A4C2960B56A7;
extern const uint32_t g_rgctx_List_1_get_Item_m04DB93566E593FA690F5196588AF5AD72B53F41D;
extern const uint32_t g_rgctx_List_1_get_Count_mCF3C06F395A41C01062789DC5E09041A4F5B7213;
extern const uint32_t g_rgctx_T_t3EF156DD8670EB7AAF194B1AD7D9EBF79DC9EC75;
extern const uint32_t g_rgctx_List_1_tCC397DA8A1248C5232B17416F8F916C1BA73A49C;
extern const uint32_t g_rgctx_List_1_get_Item_mBBDFAE78DEEAD20A50FA431690B31BF69D7086EB;
extern const uint32_t g_rgctx_List_1_get_Count_m26F41F645CAAB7C8D3D1730817FFE6D7CECEA6A0;
extern const uint32_t g_rgctx_ReflectionUtils_GetAttribute_TisT_t5CB2C03F9861D12835EC1BB073474E8D39E2F5BB_mD238A5FCA4C5AB4BD761CA677446303E88B3414A;
extern const uint32_t g_rgctx_T_t5CB2C03F9861D12835EC1BB073474E8D39E2F5BB;
extern const uint32_t g_rgctx_ReflectionUtils_GetAttributes_TisT_t253F7ED52FA2A473E04F3286356AC3342D27B6FA_mC0E2DCA810006088112B81A427F3DD07A7F211B9;
extern const uint32_t g_rgctx_TU5BU5D_tD39F35E9BA0ED0F5BD0AE37436D5A095E6B2E08B;
extern const uint32_t g_rgctx_T_t253F7ED52FA2A473E04F3286356AC3342D27B6FA;
extern const uint32_t g_rgctx_Enumerable_FirstOrDefault_TisT_t253F7ED52FA2A473E04F3286356AC3342D27B6FA_mFC4A575859483B151EC7093AA3B7AFABFCF26ADA;
extern const uint32_t g_rgctx_IEnumerable_1_t6DBA14299226CB1339EF78764A08C6EC86209304;
extern const uint32_t g_rgctx_T_t301B616D883E78742A698C313E7E418FF92961C2;
extern const uint32_t g_rgctx_TU5BU5D_t038FFD17DC39857FAF4A740E45617B2F2879B3F6;
extern const uint32_t g_rgctx_Enumerable_Cast_TisT_t301B616D883E78742A698C313E7E418FF92961C2_m5DE8407F8949C2600D1FEF349523461C37F75766;
extern const uint32_t g_rgctx_IEnumerable_1_t24D0F2756C4C02B969BF2CE4492E625146C10C37;
extern const uint32_t g_rgctx_Enumerable_ToArray_TisT_t301B616D883E78742A698C313E7E418FF92961C2_m1E532FA28928F845F6C37070C8E283684279D9EC;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass15_0_1_t0314F2CAE150B69BFEA198CCB471D23D660CB960;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass15_0_1__ctor_m75BB90D1347E802F98EB4894606E4DCCE22C5CD7;
extern const uint32_t g_rgctx_Func_2_t4F459CB8D790399BF6758DA124EB8DBE29B00D49;
extern const uint32_t g_rgctx_IEnumerable_1_t539A6A1799D1127048DA405E72EEFD5BAF1CEF9C;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass15_0_1_U3CForgivingCaseSensitiveFindU3Eb__0_m98557A23ACCB542F4954DFEFE6835983B419ED1D;
extern const uint32_t g_rgctx_Func_2_t4135BD4C17C69542E70254B2237C2FDD42165F94;
extern const uint32_t g_rgctx_Func_2__ctor_m6A829CE28DC0037A2E6BE44CA645083D022A6E8F;
extern const uint32_t g_rgctx_Enumerable_Where_TisTSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53_m61638DF139F00A7B69B1C55FB5D21457E3DDBC1B;
extern const uint32_t g_rgctx_Enumerable_Count_TisTSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53_mCE19CB4C9D74FB215B045E8DBBBEDF242D2EE60E;
extern const uint32_t g_rgctx_Enumerable_SingleOrDefault_TisTSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53_m7D5285F9F76C3124E6186561649E5CEB7035608C;
extern const uint32_t g_rgctx_TSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass15_0_1_U3CForgivingCaseSensitiveFindU3Eb__1_m7BD88C3BD23A81BA4783F7DEF0EEF7264F5A61B2;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass15_0_1_tA29EA65AC8309AD4D9DF97623C23C1507A8B8394;
extern const uint32_t g_rgctx_Func_2_tDCBB57C3E0E7F59798839F28DD3F6255D4E84629;
extern const uint32_t g_rgctx_TSource_t1B05A7EB5B6156C962FC696B04FC618ECB03D526;
extern const uint32_t g_rgctx_Func_2_Invoke_m92CA98DB36753DDCEF6B1467C318DBA91EB0BFD9;
extern const uint32_t g_rgctx_T_t655C792D94B9C9BAEBFB9CE15FBD42E1F9D4A7FB;
extern const uint32_t g_rgctx_T_t655C792D94B9C9BAEBFB9CE15FBD42E1F9D4A7FB;
extern const uint32_t g_rgctx_IEnumerable_1_t1286B350D49B4837E26DFE0A03DEF4EEDBCE1FD7;
extern const uint32_t g_rgctx_EnumerableDictionaryWrapper_2_tED6B03F280065233F908F80D2A9B2FCCD0BA2FEC;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__2_t9D75180909C933463B3D43DAC967732292770567;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__2__ctor_mD524DF02DC7019A7BBE2D09B91C722CD0EF8EC93;
extern const uint32_t g_rgctx_EnumerableDictionaryWrapper_2_GetEnumerator_m1B73B07E9CC22511A88DE76535474DBEADF419FB;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__2_t1A210DC7B812170DF0F43721DE73E96B3C470B7C;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__2_U3CU3Em__Finally1_m672CD02BB66122F2EA0B543BC0D304979954886D;
extern const uint32_t g_rgctx_EnumerableDictionaryWrapper_2_t71E37DE29DD4028FEB71980B891B7763487103B5;
extern const uint32_t g_rgctx_IEnumerable_1_t1383F1AD0BF4C6525704AF397EBA3E96F804B348;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m8FB4A66DC241E712728007A74248C0624DF1B8C2;
extern const uint32_t g_rgctx_IEnumerator_1_tE78D40F6A9803B6D4C9519B3DAF7D84C0DEEC1A8;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_mE5F06803703D1D80E10EB87A8B11F0B43627C3AD;
extern const uint32_t g_rgctx_KeyValuePair_2_tDEB815B8DACA815C96D4F294DBF1A6F4ACBB8507;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m1D718D93E39186A0F30020AF1653537B7BD04E19;
extern const uint32_t g_rgctx_KeyValuePair_2_tDEB815B8DACA815C96D4F294DBF1A6F4ACBB8507;
extern const uint32_t g_rgctx_TEnumeratorKey_t852195BB13D6DB7C092E4EE063FA9BFA2A91CBDC;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_mD446C738EDBCEE4E6CEEB9ED076BA373134E9047;
extern const uint32_t g_rgctx_TEnumeratorValue_t302A0FD259F10ADA193D2DA6833CDD61DCE9CD73;
extern const uint32_t g_rgctx_U3CGetEnumeratorU3Ed__2_System_IDisposable_Dispose_mBA8C99F512FAA43CF6CBFE21703757AC8D2A1B50;
extern const uint32_t g_rgctx_CachedAttributeGetter_1_GetAttribute_m53F2A1F25F9CC9FB543B87BA66ACD23238F72786;
extern const uint32_t g_rgctx_CachedAttributeGetter_1_t603EB3577705B4F864C6A8A1DBE46A1DCDF05158;
extern const uint32_t g_rgctx_T_t817166F5F82BF6858DF52AF8B8E64C96F0467700;
extern const uint32_t g_rgctx_ReflectionUtils_GetAttribute_TisT_tE407DE21FBE867A841C041FEFDA98C5644DAFAE2_m29BE290FB6195CF57ABCD6EEAB491E77E81518F9;
extern const uint32_t g_rgctx_T_tE407DE21FBE867A841C041FEFDA98C5644DAFAE2;
extern const uint32_t g_rgctx_ReflectionUtils_GetAttribute_TisT_t56528A80972D75273E02FCA499F4F75205F205FD_mEBD87453C871A6DA33FC1FC81D40E91022CDAB7E;
extern const uint32_t g_rgctx_T_t56528A80972D75273E02FCA499F4F75205F205FD;
extern const uint32_t g_rgctx_JsonTypeReflector_GetAttribute_TisT_t8BC80712381310AA82EB8A7C692743B7E719F3A9_m401422AE5EC9771689A652B8387855BB8747812A;
extern const uint32_t g_rgctx_T_t8BC80712381310AA82EB8A7C692743B7E719F3A9;
extern const uint32_t g_rgctx_JsonTypeReflector_GetAttribute_TisT_t8BC80712381310AA82EB8A7C692743B7E719F3A9_m666AC4DE50A5B2798DFB0CA792F49FF3031000EF;
extern const uint32_t g_rgctx_ReflectionUtils_GetAttribute_TisT_t8BC80712381310AA82EB8A7C692743B7E719F3A9_mCD50D84FDE660B522E3301F1B3E01452EF5FF67B;
extern const uint32_t g_rgctx_CachedAttributeGetter_1_t5F698E7EAA427F815FD72531A823B7C8B0A9FADE;
extern const uint32_t g_rgctx_ThreadSafeStore_2_tB143CA08ECEF98C47487BE9A0BF0A3EB4CB969E4;
extern const uint32_t g_rgctx_CachedAttributeGetter_1_t5F698E7EAA427F815FD72531A823B7C8B0A9FADE;
extern const uint32_t g_rgctx_ThreadSafeStore_2_Get_m54BE913CC0E33C93DCBDC5623E0BC2B15090F663;
extern const uint32_t g_rgctx_T_tD9467CF49C6FEECFDFD451963E3F97753FF414C7;
extern const uint32_t g_rgctx_JsonTypeReflector_GetAttribute_TisT_tD9467CF49C6FEECFDFD451963E3F97753FF414C7_mD533B9A174385CEA0E26331FCF0202D1558031AC;
extern const uint32_t g_rgctx_Func_2_t8A6F604032CC3DBDB3E5901D02D613FA8FF3DF6D;
extern const uint32_t g_rgctx_Func_2__ctor_mD10080414066236890E9DD5976DD655A56EA96FA;
extern const uint32_t g_rgctx_ThreadSafeStore_2__ctor_mBAE4EE4749D91749F3D46F8538BAB43BEEBD2F0B;
extern const uint32_t g_rgctx_Extensions_Value_TisJToken_tFD7D9015F3F97A09AD93E439ACE894D12C06E8B3_TisU_tFEDE215604EBC022693E11985409C8434CB5FDA8_m27B35DC183BE1F4EC85455B6E1FBF2E9C6FE83E3;
extern const uint32_t g_rgctx_U_tFEDE215604EBC022693E11985409C8434CB5FDA8;
extern const uint32_t g_rgctx_IEnumerable_1_t76E2660BB5C90F0B547D361A8347E397DB97AF62;
extern const uint32_t g_rgctx_Extensions_Convert_TisJToken_tFD7D9015F3F97A09AD93E439ACE894D12C06E8B3_TisU_tF8551DC215896378CE13300257FB5B87A5FFB8E1_m5BAE384E89FD90861905224E730B9858A713CE96;
extern const uint32_t g_rgctx_U_tF8551DC215896378CE13300257FB5B87A5FFB8E1;
extern const uint32_t g_rgctx_T_tDC247264718C21D023152C43171DA2B79F3D65EE;
extern const uint32_t g_rgctx_U_t50855654D17FCFFD596D85D6A67B95058C911987;
extern const uint32_t g_rgctx_U_t50855654D17FCFFD596D85D6A67B95058C911987;
extern const uint32_t g_rgctx_T_tDC247264718C21D023152C43171DA2B79F3D65EE;
extern const uint32_t g_rgctx_U3CU3Ec__0_1_tD7C26DA1B2B6126DD1854659BC2D2776CD50B54F;
extern const uint32_t g_rgctx_U3CU3Ec__0_1__ctor_m417E1433C719747A812CBE5AE08695D10344FB73;
extern const uint32_t g_rgctx_U3CU3Ec__0_1_tD7C26DA1B2B6126DD1854659BC2D2776CD50B54F;
extern const uint32_t g_rgctx_U3CU3Ec__1_1_t0A7D87C8EE5422E1A68B8CAA1A000F230CE6EEA4;
extern const uint32_t g_rgctx_U3CU3Ec__1_1__ctor_m9AD5A1AE9A49E57BBDD95785E7A079A01BC42E3F;
extern const uint32_t g_rgctx_U3CU3Ec__1_1_t0A7D87C8EE5422E1A68B8CAA1A000F230CE6EEA4;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_t2C2F201A8614421E58AA7B2B369EC3EF400D8D20;
extern const uint32_t g_rgctx_U3CU3Ec__2_1__ctor_mFAA5FB142EFD9193FA39A0F9316F5889B84167C2;
extern const uint32_t g_rgctx_U3CU3Ec__2_1_t2C2F201A8614421E58AA7B2B369EC3EF400D8D20;
extern const uint32_t g_rgctx_U3CU3Ec__3_1_t273D44F4B062B73A3C6DD5366E6BABDC6208741E;
extern const uint32_t g_rgctx_U3CU3Ec__3_1__ctor_mEBD4B0B15382214129D195EF098D85DD351DDE5D;
extern const uint32_t g_rgctx_U3CU3Ec__3_1_t273D44F4B062B73A3C6DD5366E6BABDC6208741E;
extern const uint32_t g_rgctx_U3CU3Ec__13_2_t55FD15F03A52C4ACDAC18EDD9A9515DFF2B3CE50;
extern const uint32_t g_rgctx_U3CU3Ec__13_2__ctor_m36096E13AC228379040D659E2D3E3C5C6D775299;
extern const uint32_t g_rgctx_U3CU3Ec__13_2_t55FD15F03A52C4ACDAC18EDD9A9515DFF2B3CE50;
extern const uint32_t g_rgctx_IEnumerable_1_t4E26A9D2EA60A8B73171A4D66B17147ED0C12828;
extern const uint32_t g_rgctx_JEnumerable_1_t7FFF4DEE2581C5D7DD969937BE347CF8F35EA5A5;
extern const uint32_t g_rgctx_JEnumerable_1_t7FFF4DEE2581C5D7DD969937BE347CF8F35EA5A5;
extern const uint32_t g_rgctx_JEnumerable_1_GetEnumerator_m838875D34F6C1B804E82FE73063FEF78D4348A99;
extern const uint32_t g_rgctx_IEnumerator_1_tC2F5A1355DADBAF10DF0A0C4643DBAEAEC489876;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m883E778CCB6A14101264501951BFB9C62112B05A;
extern const uint32_t g_rgctx_JEnumerable_1_Equals_m782BEDA30D627BB28F072E0ECE1998E607454FA5;
extern const uint32_t g_rgctx_Enumerable_Empty_TisT_t6BE554C44D1EB37D2A44438C992C27EE970E2FF6_mEA31AC274EBB87BB19263DEFCB9387F0CDA41611;
extern const uint32_t g_rgctx_JEnumerable_1__ctor_mFDFB9D580E9F9FA434CF5E059415775AF6CEC5AE;
extern const uint32_t g_rgctx_T_t9FC06B28F4CB145A78FC8C54C828CF2233E2426F;
extern const uint32_t g_rgctx_CustomCreationConverter_1_t1ADC15FFCC4DD0926F0C2710434C5DAE20C8C082;
extern const uint32_t g_rgctx_CustomCreationConverter_1_Create_mBC787D5E41E8DD208BA04F956CB67B368156BA12;
extern const uint32_t g_rgctx_T_tA7AB8C11F4D04524625E21B94215B7730733DF24;
extern const uint32_t g_rgctx_T_tA7AB8C11F4D04524625E21B94215B7730733DF24;
static const Il2CppRGCTXDefinition s_rgctxValues[327] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JsonConvert_DeserializeObject_TisT_tB4C2D0C9AF72D3EF0F1EE7B34B357E60B5B2A90B_mC88F5E3C4ED180D2173C392D47DC60EF19D39510 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB4C2D0C9AF72D3EF0F1EE7B34B357E60B5B2A90B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tFC24B322A0E8D8DF289F3474AAA5BB3AE5CF46EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFC24B322A0E8D8DF289F3474AAA5BB3AE5CF46EC },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t8E6E0AAAC61E7EDFC06BC16D4F5C9EA6CD8E541D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8E6E0AAAC61E7EDFC06BC16D4F5C9EA6CD8E541D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionDelegateFactory_CreateGet_TisT_t84C5B25C8EBB35B85DBCE23D9331B8A5C9EAA66F_m8EEA954054FA15F53C3B6AA30D98C23411500CCA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tA13539B16404FC3B9BEF0B93110861D0D6A1C1A2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionDelegateFactory_CreateGet_TisT_t84C5B25C8EBB35B85DBCE23D9331B8A5C9EAA66F_m81AD14CC8B0DA2A63767C9F3842B0295FD1DDC56 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionDelegateFactory_CreateSet_TisT_t0753216FB7B34643EB64CBD487B35138E33967CD_mA56F1470169FF09AF67057A6272846D91D526C2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tBD4A56E25F7B9044790D64F3AF4FF8A480C9A4EF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionDelegateFactory_CreateSet_TisT_t0753216FB7B34643EB64CBD487B35138E33967CD_m53B30B271434D787B0B36D8726E1A78733C64020 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_1_tF2F51900189C40700A781B34F00527D0B9B328F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_1__ctor_m555AB86D811682B1062C8A376AA2F49F2F2C7E45 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_1_U3CCreateMethodCallU3Eb__0_mDADC79CA8DB21DD9BD95C86374911BAA87F8D21E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MethodCall_2_t96A0DBF7511355270E25F7634B07A12493163FC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MethodCall_2__ctor_m9860AFAC7DF62F4A2D9329B040E6E524250BECAC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_1_U3CCreateMethodCallU3Eb__1_m3F32F7D262B50DA8420DE5FCB6AA0A477C5CC773 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1_t98DCD25472FFA6351145C0ABD7C1FB0D637EFD28 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1__ctor_m2FFEA8030BBC582CF5C9C6371B58934F89574BF5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1_U3CCreateDefaultConstructorU3Eb__0_m697ECA5F10AB4A8F5ABE793A7755F04978C40DDC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t3D1F48A0525F19F99C71823E4BA8167D8B0DBBD4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1__ctor_m7A2AFD08713C11F0A3FEBF7B51B9F55E4EB34E81 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1_U3CCreateDefaultConstructorU3Eb__1_mCB011F2FE99846147E0BB8178DCE134DA88D7AAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass6_0_1_t7A9B8FBD7288DED7E132E2939CA8E91E58C9A5C8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass6_0_1__ctor_mEC93825FE16FD6D1D8D0286256C0EB23180BB1BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass6_0_1_U3CCreateGetU3Eb__0_mBB4F37239F3CED04B3D698D8DB824DE334D77FCF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t07F730F86E6E711EF9C969E71B1082DFCF318949 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m04DB229AA1B4AA25BAE640F5930EC12C08BF1B8A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass7_0_1_t7FE750E34146B3E178396DEFC71E51F797AFC51E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass7_0_1__ctor_mB048BA74144DBC8ACE09BE9822CB0CC15D3B516F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass7_0_1_U3CCreateGetU3Eb__0_mD1C801DC71B41C146C48D134C4122A135391CED5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t3968C36B63D8587F2854FAC48056DF1E6573A990 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m24DEE5C7F5985CBAC7AE71BAFA7379A7D918C1A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass8_0_1_tEC3B3C1F2BAA65CA4C96138B4D600523E06A13E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass8_0_1__ctor_mA5DFFC75875145DFC7B6E3EA5FE9256325BFBC91 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass8_0_1_U3CCreateSetU3Eb__0_mCB35323FA9E593CB29F1F11BE8D3F499622A111E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tF8D52EB4908901C10B988A6A96B3C75340885332 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2__ctor_m8C95C2F874660409B85ADBE1187EFDB9ABAF8FA2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0_1_t8E38DAD74D8C34CC2D589235E8BAB54FDB84E615 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0_1__ctor_m9AF3AC4609404145CA480A69FB2072846E3BEF6E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0_1_U3CCreateSetU3Eb__0_m3E63FE2699004618906D952F543F52DF28E2DECF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t3A28C11AA563FF379FFF905BC5A3934C8322C4E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2__ctor_mE08051936490CF87088D9C1B61D5DF1E47D8D660 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass4_0_1_tE8EBFCD23A74FC478C1FB4CA8A8B2F991C5F60AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1C6C29770DE2425BF8AE6668D2FEDECA21F56B03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1_tFFD63FB3CC36980AE139B0F45BB96372E0FFFDE9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF2C1711889BD4D6520CF49F80A1B44BC8D440AF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass6_0_1_t8B7CFF617E3F09B96459009A4FBCB26F02CF8855 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8B4FEDF962E88AFF9FF3AAE3ADA9BA7F802431FF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass7_0_1_tF531960C785108782D0474B9139E951CDC9D55C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t636A8BA499D4E9C6902A0F4FBBD80E4081BC6840 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass8_0_1_t9E4DE71BD065F5DC1E655FCF1870CAAD90245239 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB9EDEF3FABADA50917BBFE15A8EE143F0CF823C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0_1_tA4F3C7B2CF1F57DA227759A91B61C91A8B6F25F3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA67262C37BB27C8FD352FE2324D74C0CF8E41C48 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ThreadSafeStore_2_tA013278B46D9C588BC936F8B5F0323C1BE3B2483 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t9EF31A1130B13E2B4F1C5F5914BAD1F90A7D0BE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t3786EBE9B4F356DF6225FAF89C41DF9DE497EB6A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mD2D37BB7A5DB1A3090848FF60FB4105208801C46 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t31D369D27EBF0CB01814C8A0172653F7BBA956C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_mC702FC9FBEB54DDBA1CE82E20071D8CAE53B4AB9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t8DDE0F5D128A7BC06A41AEBEB5CE5548237B7BDB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ThreadSafeStore_2_AddValue_m31E6AB74E2A9D801875CF77CF25BFAFA78C5D58F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t38DC6552FCD57AB80FF65FBC19B31BACB5D7C5DC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mB6D43989AF0D34DF472A9B579FD075890BA5D270 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_m46FE233BF07C21C90259E58B74164CC36144F036 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m7D35B02251D6EDF2CA5E9C68E8D896E5099ED2AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IDictionary_2_tB5144F6213557E4312A4498CB0D072E399AA59C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m249EA97EB0DFF627D2234B67C873779AE986AC94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t10602A1710C3E7BA3038609AC44D39AB15021980 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t10602A1710C3E7BA3038609AC44D39AB15021980 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_mC61F84FFCFFBC11A8F21DE180DD1D85B002A1C62 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tB98A26DC214EC99A55AFF54CAE94D9E3792E1596 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tB98A26DC214EC99A55AFF54CAE94D9E3792E1596 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BidirectionalDictionary_2__ctor_m28E6180A74417FD1F89E3DF050ECB0EE5E51D516 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_tFB244D9E319C5579DB6A5F164A8A7E4BDF24697E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEqualityComparer_1_tD7963063935AD59E957B990AF878CBE4747A1D5F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BidirectionalDictionary_2__ctor_mA945E8841E167FD6F97FC672A41ED8489A1EEB08 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t766EC3C35526225D43A8F57308255491939C978A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mE01E513D283733D35CB848D941D15E0C6EA32CA2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BidirectionalDictionary_2_tDBE00B2EE98ECB66AB9F513CB0F6D5B86F96EA86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IDictionary_2_t98A4E667E3ECDD5F8D25EC1C74730BEC0C7AD096 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tAC44BAA5C635D6A3C34B250BD0F69B7E7BA998E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m23C2DB594E1F210AC24B94991C079B8B35E4817A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IDictionary_2_tF8988B9319B0FE9421E27AD344FA88C84E3E8735 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TFirst_t3EBC8F9FAA67A448006D4FD551BCA19C770B2E18 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_TryGetValue_m848FAA148998BA3476DA261E968E539649311DCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSecondU26_tF730F4D117B9E9419839D92E685FAD26123B3467 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSecond_t2220931A411DB9B732F9FC1AC3C7972048AC4553 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSecond_t2220931A411DB9B732F9FC1AC3C7972048AC4553_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_TryGetValue_m7677ADE9080CAE7FD40F29340BB069C5E394C1F8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TFirstU26_tA23E8BFB272BF7E4ACCAF0A7A011BBF3A8DCF460 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TFirst_t3EBC8F9FAA67A448006D4FD551BCA19C770B2E18_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_Add_m9DA5BE30F4B073B4E5429B648F1D44C2BB419A21 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_Add_m55659D5D8D25C871503CBD493BC4F4991C16DF3B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionWrapper_1_tD9EE0C56BD676066903D1C0F777D9A5CA2993B37 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t45FF459022F458A6939D30A2C82D2F07CDEBB1FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t36913CC53A530D328EC311AE7CBD72A57DC8F5A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Add_mAFED7ECFDBDE1303A9A0BEBB67DEB8C9EDBD6204 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Clear_m8CC3DF0CCBE0540DFBE688C88E0E5FDBAC24D192 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Contains_m2A01986D7CAAEE833D6E31610640DFC2CD3D8924 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tAC000EDCDAB3859EBF25179530C5C6756374543E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_CopyTo_mFD45D80D4D5F16847D51E5CF1092A5F0977A2F15 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mCAEE2A4AA32BF9168C42B73941B1BF9DFBD5388B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_IsReadOnly_m032CDFF0CE98A6BC2EB55B6AE1B32B709790BB8E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Remove_m93FC0D64D4521DD164E28399C91A997E3E299232 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t228699DBA24EF1F60F7E49CEF5C50BC9CA5501B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_mD22A399361B88AE7E9018CDE419EA883AEF15B7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tA41C303C1B668608A48C1F8EA1DF5FD8237E54C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Cast_TisT_t36913CC53A530D328EC311AE7CBD72A57DC8F5A1_mD29AEE4C49ABDB11840CD66FEFE2BE3308A59CF5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionWrapper_1_VerifyValueType_mC16F2E030E74B2170579111F92FD5DC13E8B7971 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionWrapper_1_tD9EE0C56BD676066903D1C0F777D9A5CA2993B37 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionWrapper_1_Add_m385B815ABEA9D3E6B23A38F03B882F142BFD4F0A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionWrapper_1_get_Count_mF8181F95FA60EC8F5587453AC9E8385E22FFBB2A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionWrapper_1_IsCompatibleObject_m8CC5200FCE7FF6AC185A969E402BD1A9E7C0BBC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionWrapper_1_Contains_m7F42EC2AFC1FA324F2C056FA13047C728D9B070A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionWrapper_1_Remove_m94D2E1FECF4C28E10B3F298E3794682F7C95AE41 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionWrapper_1_CopyTo_m5BD06781F1BAA11B653CE60FB6BF75C98297FE41 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t36913CC53A530D328EC311AE7CBD72A57DC8F5A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DictionaryWrapper_2_t8449C6CF5EF0C0EA365D19E3B6C3F94E3E97148A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_tC9B2329877B3A856BD0D9F754469EBA4B4841835 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tE6D0DC974D710E3752A1AA2366E2F980EC738A8D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IDictionary_2_t63777991796394D5CAF070D07D9E977EC10839FF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_Add_m16997B426B448B077A1FF837E36FC761C353B13A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_ContainsKey_mDB0E0CB698F0DB525BAA0D21F59F8BF12493D1FC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Cast_TisTKey_tC9B2329877B3A856BD0D9F754469EBA4B4841835_m5A83D476C3B41272BF3AB37E7AD87FE35236C79A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tBBC0B34109B3D6CAA4EFDB03D5A419ABCE89139C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_ToList_TisTKey_tC9B2329877B3A856BD0D9F754469EBA4B4841835_m694F11D24E3F6C56CE0C5473E48D9DB687D1D460 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t51C79FB8E701C1A05E4F742D97CDD6D6E4D47641 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_get_Keys_mB35DB773012FB465C88D0A4D8609254566B8CD10 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t73FCCA2ED066CDAA210DA6B982F16F35EE00AF0D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_Remove_m0586013BDC788C3FB708759E348025CC0B5FFF3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t2F3295F795D48A4E01BE4CCD658620BF4258624B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_TryGetValue_m4CBE96F549DA53F44CDC06BDB34497919F2E3E26 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Cast_TisTValue_tE6D0DC974D710E3752A1AA2366E2F980EC738A8D_m5A7D27F6AEDF0CADC98F075D0ACF5285B09B047B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t195C12B7B64D775D6BBC9573B87075C0B256C24F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_ToList_TisTValue_tE6D0DC974D710E3752A1AA2366E2F980EC738A8D_m026AD43A7D8E2024E9CE9CCA72AA0F4F7C7D8C33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t98C7EA5E3CBD843A92178E4EA4573F67260713CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_get_Values_m5B849A52E5D5B54060935CB1D8F96D04424834D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t9A5E232BC725F71CB337060D37536DFAFA155F32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_get_Item_mE26F7FA8C35EE97955FA771AFA9380F83A1C1FA5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IDictionary_2_set_Item_m544FD80D493F6EEA57A072299A44FA4DDC4D1AD5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_tA40DC4C981917B3F0FE2A094B267CD4E8DC580FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tA703BED8D325CE1BD8A07F0F18DD341EA92F7BE5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Add_m27621D6F58B370093277A102113A382F72897556 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Clear_mECD1CC99CF692FF0FBCD14B63CF3F911AAA95D17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Contains_mDF61F7FEC4CDED370F579ADB77D45AD88AFB7E50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2U5BU5D_tD1F21287316368F5FD017FC51DB393D9A6EA0DBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2__ctor_m5A84BA9E694F5A8461BD66DB20C3CE7090A0D9E3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_CopyTo_m2CBF832BF7B8BB02CF98692C53D9A341A9FA9377 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mD48DBEC07A2650C7DB2AC5BDC74941524A4B261C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_IsReadOnly_m74AE86E46CEBCA23A0653D3E0A149315E033CB27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m529178743739FEC4029CB9FA827E231269FAC913 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_tA40DC4C981917B3F0FE2A094B267CD4E8DC580FC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_m61F299A77333D0011BA09B5F5CFC33EDF7F4DEC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Remove_m8B02EA007796E810582275E02CDAFC883E85F1AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tF3D48710CE0160D944BBC62F7FDDEECBAA08F451 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t12671B94C25AF4B0F8850638FB36C5FE8B65CD4E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tF3D48710CE0160D944BBC62F7FDDEECBAA08F451 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3CGetEnumeratorU3Eb__25_0_m4C9A0592CFB83847F6A85558C061E989F304734A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_mFD93F1976D6638E06B8BB101E1D655A8F7B07A3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Select_TisDictionaryEntry_t171080F37B311C25AA9E75888F9C9D703FA721BB_TisKeyValuePair_2_tA40DC4C981917B3F0FE2A094B267CD4E8DC580FC_mAFA609BEFAE7E17B33AC3BF4DD45AFF18DFFD640 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t38176E7B3C89132E55F43A75017F8ACD944AC435 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_mDA91D7764D131765588D54972FFC300C8DAF2C43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t25AF92E421A5040966DC3428B1D841281C3E563D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DictionaryWrapper_2_GetEnumerator_m00E8587D4E6E8CEB6C080DF4F02275FA4CEDF2F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DictionaryEnumerator_2_t44BB0FABECBB27E58D64E11A88BDAD1798AC8AB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DictionaryEnumerator_2__ctor_mDD52A239017E5A4307E785DA600886555D4A1C0C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t0A9D6EBF79FE0F00733575CF254BEB71CC2C5CDF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DictionaryEnumerator_2_tC874AB94E415EDEA7382D290FFD5955B400C0A71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DictionaryEnumerator_2_get_Current_m9F79264C0D24091C3C39F36E63C14D2CA37519EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DictionaryEnumerator_2_tC874AB94E415EDEA7382D290FFD5955B400C0A71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DictionaryEnumerator_2_get_Entry_m0619BB4D232AFD1D83D2A33CBCBFF72DB8CA041D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m2EE95FA0DEE34A6014BB28A2AD0CDFAF88F0C25E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t84DB2F95E252B7EFA71765F7BC6D77A3E707CD2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m39ED019909B69E5F63A8F0412C0189E8CCD0D3BA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t84DB2F95E252B7EFA71765F7BC6D77A3E707CD2B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEnumeratorKey_t5CA50B980800A5B5C3AE9D67CA933D6125809DC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_mF4D1A12291E79F4FD5F0E5846977CF14754C22F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEnumeratorValue_t98CD17B5F06DC5DE05FDE6AD4DCDDB7205E93710 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tA55F00B2414663FE1EED17E0DDE3E7AAF9222FC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_m1CE1BCB6869DFFBDB8F8568392824181C757452F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tA55F00B2414663FE1EED17E0DDE3E7AAF9222FC0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t056087ABC03BDE7CA66814438C8A3F0FFDD8C40D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tB558134B8CDA2BA5337D989565A8ADB951D442C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_tD60E2B802B9C94871DF3B39C628424AD431C12A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2__ctor_m571DB6E6B18D3368954AD12EE74283ECCDDE6857 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_t01EBFCD988C9A362E96B2E97D71534D8D0831BEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__2_1__ctor_mDA51833A850FE59551EECBFAD03B419739614D3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_t01EBFCD988C9A362E96B2E97D71534D8D0831BEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tFA02801D53AE75F1C2C81EA868B033BD683AD29E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_m07A925DDC31D9E7C0D344E7A811B1E6EC8781F99 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t1734055875A7DCEFA0D2F42FE4CCAF7A83E7461A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tADA2A73F1DDBCDD22DAF4F1D489743EABD0E6558 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m6E43948D19D017D4716799317595F5FC49D89067 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tC677508A9E30A48A26FB815B1FB548C5521794FC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m83ECD482FD9678646EAC4C24A051C0E290915D7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2ECF4781334FD2E3D88B5802520717613E888A4D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tF8F68924B96615EE0EC63DD6D8B38FF844FE57B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_Add_mCE727B856DA454D0EE36C29670B9B09F6A6E104B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t4AF6E02E9D69F67FE045E53D30F01CC59955F607 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Cast_TisT_tCAA74E4CF485AAD390590EB27D4DBB5FADBCCF7A_mFEF613262BB0B081E8A525E31AD07FBD3D9F5A50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tCF2F11B1D0B5A702B68B2625D9E44A94550FA981 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionUtils_AddRange_TisT_tCAA74E4CF485AAD390590EB27D4DBB5FADBCCF7A_m311A64973762223CF7B375CE9761FC03BABBCBF2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t1670BCE9B592A5DE9B853B3CBD53B9461D4F62CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m370D5C76F5338A4DA102FAB824A3D9FD90FE6D98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t2B8FBED12A02A6869AF3A4B20E90593686ABAE7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_mC35FF539DF496AE0493B148D2F801C1240209375 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7933D7BCDE0829525502B2D2273E7E6946C8CC9A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t4ED35AB3588BA208B5B1F45D478EECB049F0A0B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m105F88998904F595A94AEC6C3BE67C334422A351 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC6385C0CCA75B41ED8F6A7F44047AB590DE61679 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t772F9030BE800826AC42C960FA33A4C2960B56A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m04DB93566E593FA690F5196588AF5AD72B53F41D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mCF3C06F395A41C01062789DC5E09041A4F5B7213 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3EF156DD8670EB7AAF194B1AD7D9EBF79DC9EC75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tCC397DA8A1248C5232B17416F8F916C1BA73A49C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mBBDFAE78DEEAD20A50FA431690B31BF69D7086EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m26F41F645CAAB7C8D3D1730817FFE6D7CECEA6A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionUtils_GetAttribute_TisT_t5CB2C03F9861D12835EC1BB073474E8D39E2F5BB_mD238A5FCA4C5AB4BD761CA677446303E88B3414A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5CB2C03F9861D12835EC1BB073474E8D39E2F5BB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionUtils_GetAttributes_TisT_t253F7ED52FA2A473E04F3286356AC3342D27B6FA_mC0E2DCA810006088112B81A427F3DD07A7F211B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD39F35E9BA0ED0F5BD0AE37436D5A095E6B2E08B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t253F7ED52FA2A473E04F3286356AC3342D27B6FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_FirstOrDefault_TisT_t253F7ED52FA2A473E04F3286356AC3342D27B6FA_mFC4A575859483B151EC7093AA3B7AFABFCF26ADA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t6DBA14299226CB1339EF78764A08C6EC86209304 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t301B616D883E78742A698C313E7E418FF92961C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t038FFD17DC39857FAF4A740E45617B2F2879B3F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Cast_TisT_t301B616D883E78742A698C313E7E418FF92961C2_m5DE8407F8949C2600D1FEF349523461C37F75766 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t24D0F2756C4C02B969BF2CE4492E625146C10C37 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_ToArray_TisT_t301B616D883E78742A698C313E7E418FF92961C2_m1E532FA28928F845F6C37070C8E283684279D9EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass15_0_1_t0314F2CAE150B69BFEA198CCB471D23D660CB960 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass15_0_1__ctor_m75BB90D1347E802F98EB4894606E4DCCE22C5CD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t4F459CB8D790399BF6758DA124EB8DBE29B00D49 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t539A6A1799D1127048DA405E72EEFD5BAF1CEF9C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass15_0_1_U3CForgivingCaseSensitiveFindU3Eb__0_m98557A23ACCB542F4954DFEFE6835983B419ED1D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t4135BD4C17C69542E70254B2237C2FDD42165F94 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m6A829CE28DC0037A2E6BE44CA645083D022A6E8F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Where_TisTSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53_m61638DF139F00A7B69B1C55FB5D21457E3DDBC1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Count_TisTSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53_mCE19CB4C9D74FB215B045E8DBBBEDF242D2EE60E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_SingleOrDefault_TisTSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53_m7D5285F9F76C3124E6186561649E5CEB7035608C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSource_t283C5FE5CDAB9497181BC268B30AA11D84A2DF53 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass15_0_1_U3CForgivingCaseSensitiveFindU3Eb__1_m7BD88C3BD23A81BA4783F7DEF0EEF7264F5A61B2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass15_0_1_tA29EA65AC8309AD4D9DF97623C23C1507A8B8394 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tDCBB57C3E0E7F59798839F28DD3F6255D4E84629 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSource_t1B05A7EB5B6156C962FC696B04FC618ECB03D526 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m92CA98DB36753DDCEF6B1467C318DBA91EB0BFD9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t655C792D94B9C9BAEBFB9CE15FBD42E1F9D4A7FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t655C792D94B9C9BAEBFB9CE15FBD42E1F9D4A7FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t1286B350D49B4837E26DFE0A03DEF4EEDBCE1FD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EnumerableDictionaryWrapper_2_tED6B03F280065233F908F80D2A9B2FCCD0BA2FEC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__2_t9D75180909C933463B3D43DAC967732292770567 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__2__ctor_mD524DF02DC7019A7BBE2D09B91C722CD0EF8EC93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EnumerableDictionaryWrapper_2_GetEnumerator_m1B73B07E9CC22511A88DE76535474DBEADF419FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__2_t1A210DC7B812170DF0F43721DE73E96B3C470B7C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__2_U3CU3Em__Finally1_m672CD02BB66122F2EA0B543BC0D304979954886D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EnumerableDictionaryWrapper_2_t71E37DE29DD4028FEB71980B891B7763487103B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t1383F1AD0BF4C6525704AF397EBA3E96F804B348 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m8FB4A66DC241E712728007A74248C0624DF1B8C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tE78D40F6A9803B6D4C9519B3DAF7D84C0DEEC1A8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_mE5F06803703D1D80E10EB87A8B11F0B43627C3AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_tDEB815B8DACA815C96D4F294DBF1A6F4ACBB8507 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m1D718D93E39186A0F30020AF1653537B7BD04E19 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_tDEB815B8DACA815C96D4F294DBF1A6F4ACBB8507 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEnumeratorKey_t852195BB13D6DB7C092E4EE063FA9BFA2A91CBDC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_mD446C738EDBCEE4E6CEEB9ED076BA373134E9047 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEnumeratorValue_t302A0FD259F10ADA193D2DA6833CDD61DCE9CD73 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CGetEnumeratorU3Ed__2_System_IDisposable_Dispose_mBA8C99F512FAA43CF6CBFE21703757AC8D2A1B50 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CachedAttributeGetter_1_GetAttribute_m53F2A1F25F9CC9FB543B87BA66ACD23238F72786 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CachedAttributeGetter_1_t603EB3577705B4F864C6A8A1DBE46A1DCDF05158 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t817166F5F82BF6858DF52AF8B8E64C96F0467700 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionUtils_GetAttribute_TisT_tE407DE21FBE867A841C041FEFDA98C5644DAFAE2_m29BE290FB6195CF57ABCD6EEAB491E77E81518F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE407DE21FBE867A841C041FEFDA98C5644DAFAE2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionUtils_GetAttribute_TisT_t56528A80972D75273E02FCA499F4F75205F205FD_mEBD87453C871A6DA33FC1FC81D40E91022CDAB7E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t56528A80972D75273E02FCA499F4F75205F205FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JsonTypeReflector_GetAttribute_TisT_t8BC80712381310AA82EB8A7C692743B7E719F3A9_m401422AE5EC9771689A652B8387855BB8747812A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8BC80712381310AA82EB8A7C692743B7E719F3A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JsonTypeReflector_GetAttribute_TisT_t8BC80712381310AA82EB8A7C692743B7E719F3A9_m666AC4DE50A5B2798DFB0CA792F49FF3031000EF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectionUtils_GetAttribute_TisT_t8BC80712381310AA82EB8A7C692743B7E719F3A9_mCD50D84FDE660B522E3301F1B3E01452EF5FF67B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CachedAttributeGetter_1_t5F698E7EAA427F815FD72531A823B7C8B0A9FADE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ThreadSafeStore_2_tB143CA08ECEF98C47487BE9A0BF0A3EB4CB969E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CachedAttributeGetter_1_t5F698E7EAA427F815FD72531A823B7C8B0A9FADE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ThreadSafeStore_2_Get_m54BE913CC0E33C93DCBDC5623E0BC2B15090F663 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD9467CF49C6FEECFDFD451963E3F97753FF414C7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JsonTypeReflector_GetAttribute_TisT_tD9467CF49C6FEECFDFD451963E3F97753FF414C7_mD533B9A174385CEA0E26331FCF0202D1558031AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t8A6F604032CC3DBDB3E5901D02D613FA8FF3DF6D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_mD10080414066236890E9DD5976DD655A56EA96FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ThreadSafeStore_2__ctor_mBAE4EE4749D91749F3D46F8538BAB43BEEBD2F0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Extensions_Value_TisJToken_tFD7D9015F3F97A09AD93E439ACE894D12C06E8B3_TisU_tFEDE215604EBC022693E11985409C8434CB5FDA8_m27B35DC183BE1F4EC85455B6E1FBF2E9C6FE83E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tFEDE215604EBC022693E11985409C8434CB5FDA8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t76E2660BB5C90F0B547D361A8347E397DB97AF62 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Extensions_Convert_TisJToken_tFD7D9015F3F97A09AD93E439ACE894D12C06E8B3_TisU_tF8551DC215896378CE13300257FB5B87A5FFB8E1_m5BAE384E89FD90861905224E730B9858A713CE96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tF8551DC215896378CE13300257FB5B87A5FFB8E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDC247264718C21D023152C43171DA2B79F3D65EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t50855654D17FCFFD596D85D6A67B95058C911987 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_U_t50855654D17FCFFD596D85D6A67B95058C911987 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tDC247264718C21D023152C43171DA2B79F3D65EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__0_1_tD7C26DA1B2B6126DD1854659BC2D2776CD50B54F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__0_1__ctor_m417E1433C719747A812CBE5AE08695D10344FB73 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__0_1_tD7C26DA1B2B6126DD1854659BC2D2776CD50B54F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__1_1_t0A7D87C8EE5422E1A68B8CAA1A000F230CE6EEA4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__1_1__ctor_m9AD5A1AE9A49E57BBDD95785E7A079A01BC42E3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__1_1_t0A7D87C8EE5422E1A68B8CAA1A000F230CE6EEA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_t2C2F201A8614421E58AA7B2B369EC3EF400D8D20 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__2_1__ctor_mFAA5FB142EFD9193FA39A0F9316F5889B84167C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__2_1_t2C2F201A8614421E58AA7B2B369EC3EF400D8D20 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__3_1_t273D44F4B062B73A3C6DD5366E6BABDC6208741E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__3_1__ctor_mEBD4B0B15382214129D195EF098D85DD351DDE5D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__3_1_t273D44F4B062B73A3C6DD5366E6BABDC6208741E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__13_2_t55FD15F03A52C4ACDAC18EDD9A9515DFF2B3CE50 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__13_2__ctor_m36096E13AC228379040D659E2D3E3C5C6D775299 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__13_2_t55FD15F03A52C4ACDAC18EDD9A9515DFF2B3CE50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t4E26A9D2EA60A8B73171A4D66B17147ED0C12828 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JEnumerable_1_t7FFF4DEE2581C5D7DD969937BE347CF8F35EA5A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JEnumerable_1_t7FFF4DEE2581C5D7DD969937BE347CF8F35EA5A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JEnumerable_1_GetEnumerator_m838875D34F6C1B804E82FE73063FEF78D4348A99 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tC2F5A1355DADBAF10DF0A0C4643DBAEAEC489876 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m883E778CCB6A14101264501951BFB9C62112B05A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JEnumerable_1_Equals_m782BEDA30D627BB28F072E0ECE1998E607454FA5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Empty_TisT_t6BE554C44D1EB37D2A44438C992C27EE970E2FF6_mEA31AC274EBB87BB19263DEFCB9387F0CDA41611 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JEnumerable_1__ctor_mFDFB9D580E9F9FA434CF5E059415775AF6CEC5AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9FC06B28F4CB145A78FC8C54C828CF2233E2426F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CustomCreationConverter_1_t1ADC15FFCC4DD0926F0C2710434C5DAE20C8C082 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CustomCreationConverter_1_Create_mBC787D5E41E8DD208BA04F956CB67B368156BA12 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA7AB8C11F4D04524625E21B94215B7730733DF24 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tA7AB8C11F4D04524625E21B94215B7730733DF24 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationNewtonsoft_Json;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Newtonsoft_Json_CodeGenModule;
const Il2CppCodeGenModule g_Newtonsoft_Json_CodeGenModule = 
{
	"Newtonsoft.Json.dll",
	2104,
	s_methodPointers,
	43,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	54,
	s_rgctxIndices,
	327,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationNewtonsoft_Json,
	NULL,
	NULL,
	NULL,
	NULL,
};
