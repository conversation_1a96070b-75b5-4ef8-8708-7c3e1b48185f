﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AnalyticsCommon_get_ugsAnalyticsEnabledInternal_m0147527487E703D5385A1663B1FD143FB6EE2729 (void);
extern void AnalyticsCommon_set_ugsAnalyticsEnabledInternal_m4264C600AC21370AB14F1A2D811CF8976C63E864 (void);
extern void AnalyticsCommon_get_ugsAnalyticsEnabled_m81F29A5DC5BF9A2D2385352DD5671A273397F2D0 (void);
extern void AnalyticsCommon_set_ugsAnalyticsEnabled_m998A626F754515EF4257B9FC3BD0850DDF4370F3 (void);
extern void UGSAnalyticsInternalTools_SetPrivacyStatus_mF4F04D971EBA3A54A9AD22DC4E8D95711334516A (void);
static Il2CppMethodPointer s_methodPointers[5] = 
{
	AnalyticsCommon_get_ugsAnalyticsEnabledInternal_m0147527487E703D5385A1663B1FD143FB6EE2729,
	AnalyticsCommon_set_ugsAnalyticsEnabledInternal_m4264C600AC21370AB14F1A2D811CF8976C63E864,
	AnalyticsCommon_get_ugsAnalyticsEnabled_m81F29A5DC5BF9A2D2385352DD5671A273397F2D0,
	AnalyticsCommon_set_ugsAnalyticsEnabled_m998A626F754515EF4257B9FC3BD0850DDF4370F3,
	UGSAnalyticsInternalTools_SetPrivacyStatus_mF4F04D971EBA3A54A9AD22DC4E8D95711334516A,
};
static const int32_t s_InvokerIndices[5] = 
{
	8993,
	8868,
	8993,
	8868,
	8868,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_UnityAnalyticsCommonModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsCommonModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsCommonModule_CodeGenModule = 
{
	"UnityEngine.UnityAnalyticsCommonModule.dll",
	5,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_UnityAnalyticsCommonModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
