﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274 (void);
extern void WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E (void);
extern void WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34 (void);
extern void WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E (void);
extern void WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755 (void);
extern void WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C (void);
extern void WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F (void);
extern void WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43 (void);
extern void WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6 (void);
extern void WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5 (void);
extern void SoftJointLimit_get_limit_m565D543DC9482F893A8C1F8582B9A06F7E287286 (void);
extern void SoftJointLimit_set_limit_m34B7F00528D7F5B03D2AC39E44AFD96F0EAADF1A (void);
extern void SoftJointLimit_get_bounciness_m978E3102B620170A84C30EC15963B04564866DA8 (void);
extern void SoftJointLimit_set_bounciness_m1D956BA96C435546CF89A8936F684A3C2E3BB036 (void);
extern void SoftJointLimit_get_contactDistance_mD9CFEBFE02B773802773E53F9643C20E8C274020 (void);
extern void SoftJointLimit_set_contactDistance_mDF20A5E81A2184F22CBA3007BB871BF87CB8FF2D (void);
extern void SoftJointLimit_get_spring_m8424406A064106C77453C7B24FACEC365ECAD991 (void);
extern void SoftJointLimit_set_spring_mA96D8FDEA28FD24B2E3077ACA5159F74047F0EE6 (void);
extern void SoftJointLimit_get_damper_mD68459A168731FF3AB023A227286A19D8B72DEF5 (void);
extern void SoftJointLimit_set_damper_m3AF75ED0C45C84404FE3B4ED3850DD9A30704753 (void);
extern void SoftJointLimit_get_bouncyness_m711720910DECFC3A5C77F0A24602A820659A418C (void);
extern void SoftJointLimit_set_bouncyness_m2AA2F20F2ABB85328D343253D8A3836E61EE2E67 (void);
extern void SoftJointLimitSpring_get_spring_m04C597C18DD13494AFBAEB9348D7877EDAC0AA08 (void);
extern void SoftJointLimitSpring_set_spring_m9A216142953ECC1CEE5080D603D18F9D1BD0A6EA (void);
extern void SoftJointLimitSpring_get_damper_m7B70F7492F1C75B68E879C484681AD00FDB771EE (void);
extern void SoftJointLimitSpring_set_damper_mA86F8E250BA84A6DC3E84DC1A40319A39CD5CFD6 (void);
extern void JointDrive_get_positionSpring_m41EABECAFA44BAC2E33CE9FD278B3DEDE884585D (void);
extern void JointDrive_set_positionSpring_mC928C6830ABEC56D68FB9B054DCD2A1A807EAD52 (void);
extern void JointDrive_get_positionDamper_mA22BFFB513E083AE41711EA6EA96D0424E7A9D1D (void);
extern void JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604 (void);
extern void JointDrive_get_maximumForce_mED9DD2A8EAACC262A83CED0991F2A42E78B2F3B8 (void);
extern void JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5 (void);
extern void JointDrive_get_useAcceleration_mAFE42782DBC795844A8C698802064AAF34D12A45 (void);
extern void JointDrive_set_useAcceleration_m04919856A177B9ECEEDA9178F64E85625C6CAC33 (void);
extern void JointDrive_get_mode_m7012ADA832DB3EECCC4EBBD23B0C067F9AD97918 (void);
extern void JointDrive_set_mode_m1C934E502FE9705DE2DC7C37257C10BAED74D394 (void);
extern void JointMotor_get_targetVelocity_mDB63119AC6C3AF796785042AF466CD02D937820F (void);
extern void JointMotor_set_targetVelocity_m6F58E447E9C1F7812246ECD1CB8C2929D1CE86DE (void);
extern void JointMotor_get_force_mB4BDD8D40380A7E01E9C062BAADB1BE97F6A63FE (void);
extern void JointMotor_set_force_m66139CAD801991E3788835067C0D738EA000BFD3 (void);
extern void JointMotor_get_freeSpin_m01BCFC597BF0DF5540378A79FAC1F8D0F4336683 (void);
extern void JointMotor_set_freeSpin_mC37B615961CCAC6A3DC75109476C8C46FF4E894C (void);
extern void JointLimits_get_min_m3E8D3C572B30DA53262849D4D5BFFD9A77276FC8 (void);
extern void JointLimits_set_min_m6DCC6F92E715577794E36CD524989509D2A001AF (void);
extern void JointLimits_get_max_m4E6BC6E5D320C4E2E7599852B19F4524D9549638 (void);
extern void JointLimits_set_max_m192F8B77417D548BF0162E651049DB1C4C1D81A0 (void);
extern void JointLimits_get_bounciness_m631282F7314872399F85C93F9827AA1F79BEFBAB (void);
extern void JointLimits_set_bounciness_mEB2995C51F0E0F6591D42881CB7CC69CF1835CC9 (void);
extern void JointLimits_get_bounceMinVelocity_m2BF2A2C9471171604CC519C205D2BC738371C40A (void);
extern void JointLimits_set_bounceMinVelocity_m3D1D91AF20CBD28E66EF1BAA7AAB003BC9E671E1 (void);
extern void JointLimits_get_contactDistance_mD306F20301F701B403B1D85EF984E958158F3717 (void);
extern void JointLimits_set_contactDistance_mF8CC4376AC7E794C0E2FA1EBF035798EB82680E8 (void);
extern void ControllerColliderHit_get_controller_mEC3E909A4B9843AA2F7A6606B021D3E88771F9EB (void);
extern void ControllerColliderHit_get_collider_mA2CF90334AD1231C04452B2D99715A9E289691D6 (void);
extern void ControllerColliderHit_get_rigidbody_mDEEE467B695AAC7418C350B9BE599B8D030413B3 (void);
extern void ControllerColliderHit_get_gameObject_m206F4915101DD080BF0EF53F3F2BE79404C5936F (void);
extern void ControllerColliderHit_get_transform_mAD3A9A4507A78EDA3C3045E2B74EC46EC80155D9 (void);
extern void ControllerColliderHit_get_point_mCE74937BAC07AD84F6B255471177974A5C12E915 (void);
extern void ControllerColliderHit_get_normal_mDA7A9B952DEA2B2EDFFDD153DFAA08089C9FFBB6 (void);
extern void ControllerColliderHit_get_moveDirection_mC4C6384C18B4DAE3D301D079AE2A30FCC2E78A93 (void);
extern void ControllerColliderHit_get_moveLength_mC681AFC545104C3F9F1B117D9603C3A80EDF4CED (void);
extern void ControllerColliderHit_get_push_mE79ED6947D82E806A17985A6D000DE230C90682A (void);
extern void ControllerColliderHit_set_push_mC12AD21A9E81C7EA7C908348BA066BFA86E15E9C (void);
extern void ControllerColliderHit__ctor_m7DDDC7B3A2847014BF033353283D2233246A9373 (void);
extern void Collision_get_impulse_mBA2EDD39B7F495FF335FB867B244253602C7EF5D (void);
extern void Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0 (void);
extern void Collision_get_rigidbody_mD7A14B9C8AA98352340D2AB0097FC3A424FBB81B (void);
extern void Collision_get_articulationBody_mB550C4981EF75F1BD399C5A90927D5BA51F5EACC (void);
extern void Collision_get_body_mA03043499C5BAF241F96F3FAEE183F7C5371A246 (void);
extern void Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D (void);
extern void Collision_get_transform_mA5D135D9F696635EA7A0D2184CEF499427A6D0F6 (void);
extern void Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E (void);
extern void Collision_get_Flipped_m27CF19F2767881D0A84AAEDC19250C6361ACF496 (void);
extern void Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1 (void);
extern void Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150 (void);
extern void Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473 (void);
extern void Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912 (void);
extern void Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7 (void);
extern void Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C (void);
extern void Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD (void);
extern void Collision_GetContacts_m3E2B52E011083420A9A1F5E551798F9483622292 (void);
extern void Collision_GetContacts_m3E18E9CACEA6DB7298B4D31F0FF1E89D9C21D648 (void);
extern void Collision_GetEnumerator_mC8CBF3C9ACC4277EB5730D8388DCEF3F2DA9DBFD (void);
extern void Collision_get_impactForceSum_m62DBB3F8E317209D78077F48FAB93ED9F4A20BE3 (void);
extern void Collision_get_frictionForceSum_m3FC8545019D1AD089BD1CD5A22467E05F9B95B64 (void);
extern void Collision_get_other_mC093EDECBF5EF40857681189B81CF7959C0178F0 (void);
extern void ArticulationReducedSpace_get_Item_m8E297D94FA09BCB4E45C045BF0411D67E183BF24 (void);
extern void ArticulationReducedSpace_set_Item_m511249FEDD7DA1241235F2D78B17E495435141CB (void);
extern void ArticulationReducedSpace__ctor_m73747277F64DBDD2DD2C2027F7848AB29A735D0D (void);
extern void ArticulationReducedSpace__ctor_m1993AEDD6D0F94169DDDE6D3E4BFEB4B1D2144EE (void);
extern void ArticulationReducedSpace__ctor_mE85D365C229AA06F3E7D4FB73717088D7F20B461 (void);
extern void ArticulationJacobian__ctor_mD33412D6B8F30DA581549DF60FD9727204971574 (void);
extern void ArticulationJacobian_get_Item_mC4DCABA2709F24770BD58BBF007909757BF2AE16 (void);
extern void ArticulationJacobian_set_Item_m1CB6CCCC4FC036E19F9CECE28C6488245082C85D (void);
extern void ArticulationJacobian_get_rows_mD319F639F0B37D27355015EAD8BC0BCC027D6EDD (void);
extern void ArticulationJacobian_set_rows_m1782AA659C927A76DCBE506D7EBB429F0DC16C8E (void);
extern void ArticulationJacobian_get_columns_m2CD8D3E79C5A0EE73DEF851ECBD6682FAA4738AB (void);
extern void ArticulationJacobian_set_columns_m911137E1ACF2BA000559FD77849C07E3B60E679E (void);
extern void ArticulationJacobian_get_elements_m6E8E68D8FF8F98B8D532397C519E3F433162264C (void);
extern void ArticulationJacobian_set_elements_m3D711A40DB5A05C254933079A71CFFC756D86074 (void);
extern void ArticulationBody_get_jointType_m609D1CDD2C3A2576B05BCFF700F37DF49E715E88 (void);
extern void ArticulationBody_set_jointType_m74B1899BE7793E59F25FDE4B746D435FA0EAA357 (void);
extern void ArticulationBody_get_anchorPosition_m52FE087DD45A380C3315D17CF4BA11001F718742 (void);
extern void ArticulationBody_set_anchorPosition_m7A1724AC83FBA33D635261072D6A87C8DC2D343D (void);
extern void ArticulationBody_get_parentAnchorPosition_m85E0762DD0AC972325137A25599A73773189C1EE (void);
extern void ArticulationBody_set_parentAnchorPosition_m2C3541E1299226DEA7E1A42B163A6FDB19ACAB7E (void);
extern void ArticulationBody_get_anchorRotation_mB7A71B90339AC65B1CC6640E7DBE60795D4199D7 (void);
extern void ArticulationBody_set_anchorRotation_m7208756EA5041654CD22E65C9579BC948A173E9F (void);
extern void ArticulationBody_get_parentAnchorRotation_m59C2B74F86297F9660B6FDB9AC38821133ECF4A3 (void);
extern void ArticulationBody_set_parentAnchorRotation_mC98915B78FF81EE18C70643BFC8F4342E2ECDE24 (void);
extern void ArticulationBody_get_isRoot_mFF2F4C90378FA83EA960AE713667DA68D2CB9615 (void);
extern void ArticulationBody_get_matchAnchors_m131E537DFE90D13553DCC8D4D5FA8D0DAC9B8813 (void);
extern void ArticulationBody_set_matchAnchors_mDF6F89EB401B1E9E8D802C891C1A89F81426A97F (void);
extern void ArticulationBody_get_linearLockX_m9F38421E04E71A934EA6464957BDDD8A183CD705 (void);
extern void ArticulationBody_set_linearLockX_m3BDEA1A79A853310942F8520870B5A5782274BB3 (void);
extern void ArticulationBody_get_linearLockY_m2B18C43F5705D4927AE6E628E1C22EAFE8C285AA (void);
extern void ArticulationBody_set_linearLockY_m6949327647A3598F80AA22389F436D38757531DE (void);
extern void ArticulationBody_get_linearLockZ_m9894178BCC40BF603F48C68A6BCE83C6CA085799 (void);
extern void ArticulationBody_set_linearLockZ_m0A5FCCD4F2FAFE2F871072F873D1FD74DABFB174 (void);
extern void ArticulationBody_get_swingYLock_mBE11D09C108416E0C79C9A4498303BC71015444A (void);
extern void ArticulationBody_set_swingYLock_m8E2231FEFC285F75F247AAC6D65503BAF92B203C (void);
extern void ArticulationBody_get_swingZLock_m163A505217D2C71A392A0A60B4D2EE97A6A15B0F (void);
extern void ArticulationBody_set_swingZLock_mA4DD9CB649E518EDCEB72F15BB6C3C3F5C15BD40 (void);
extern void ArticulationBody_get_twistLock_m529CC23499A2F88CB7660D3F764E821EA3ABE04F (void);
extern void ArticulationBody_set_twistLock_m3DC7D5CA5E9AA8CC7BF62765740CB1DE3E6E4C2F (void);
extern void ArticulationBody_get_xDrive_m554A6D155402E0B4630EAD7086F6221B2DAB0616 (void);
extern void ArticulationBody_set_xDrive_mDB703DA4006DE8EA80005BAA83AB470AE7AAFB44 (void);
extern void ArticulationBody_get_yDrive_m3BBEB6734EEEBA21BAB904B534CEF431F5455044 (void);
extern void ArticulationBody_set_yDrive_mA3430A2EB81593EFDF445AFE7E8D3C5FA9ED6BB3 (void);
extern void ArticulationBody_get_zDrive_mEE866F622D6302EEC63D46CEA12C098E9D86F1B6 (void);
extern void ArticulationBody_set_zDrive_m8719953DF721F20CE495BB61F6DF4950FBC9C6AE (void);
extern void ArticulationBody_get_immovable_mD872337FA184D140DAE64017CC04D4ABD1139754 (void);
extern void ArticulationBody_set_immovable_m060DB3218F5B29D9F86057BA4E25589BE7EEDD98 (void);
extern void ArticulationBody_get_useGravity_m6F29878C8F0DC15C3AFB9348AE5C4B27784BD698 (void);
extern void ArticulationBody_set_useGravity_m8D5E78169ADBCB88DCE1EC005BED4060108D7974 (void);
extern void ArticulationBody_get_linearDamping_m4E22CB49EB2DE1F5468740DBB3DE9D285E8B2188 (void);
extern void ArticulationBody_set_linearDamping_mC72B0BAB28D6BBA9478C3D63913851A64A5584A9 (void);
extern void ArticulationBody_get_angularDamping_mA5F118D2A0EDD74D64F185CF09E363B738E724CD (void);
extern void ArticulationBody_set_angularDamping_m227975CA9AF019A05CE0BE8C3679667404FCB5DD (void);
extern void ArticulationBody_get_jointFriction_m826FDBE9097B80A39BE9A59BCB5C96DA60601370 (void);
extern void ArticulationBody_set_jointFriction_m9180C71E00D8EEFB1BB5669C57D7ED477A158D44 (void);
extern void ArticulationBody_get_excludeLayers_m6C1B3A1ABFD07CFB9F86FB1622DE3C31E5FD8AA9 (void);
extern void ArticulationBody_set_excludeLayers_m8130D65AA2A87D9D9B954A30303D77F098D6FC21 (void);
extern void ArticulationBody_get_includeLayers_mF917384F9BE11A7EBC9595E4E0680BB1CDDA10AF (void);
extern void ArticulationBody_set_includeLayers_m3AD11471144E213A3D00F9A0A7BF1AC907173CDD (void);
extern void ArticulationBody_GetAccumulatedForce_mAAEBE584B1FC5DA34D01E814F0F4311A4CE02849 (void);
extern void ArticulationBody_GetAccumulatedForce_m3312D41EC76E292587BE1F7EF1C4CFA45DB62BC6 (void);
extern void ArticulationBody_GetAccumulatedTorque_mB13DCE9AE802256C5E06BD31F0522C71B3D951EB (void);
extern void ArticulationBody_GetAccumulatedTorque_mFDFD44265057BB4B66181BD53962C9C16EE6D798 (void);
extern void ArticulationBody_AddForce_mF22B0DDD540E665672A5EA2B925413A293B5CF86 (void);
extern void ArticulationBody_AddForce_m9B951C7F08F82992D45F1053538DB10D769F7399 (void);
extern void ArticulationBody_AddRelativeForce_m4C28F79208B2E23B34B6B52E8D37A728DC07D9D6 (void);
extern void ArticulationBody_AddRelativeForce_m25655BC006A53FF0C02A8AC909D3B1886A798D54 (void);
extern void ArticulationBody_AddTorque_mA8DD6322BC73E42B7CA307A17025CE02BAC18D5E (void);
extern void ArticulationBody_AddTorque_mAC35D1BCAFFBCA6CDE52EBD7F05439A9885D992C (void);
extern void ArticulationBody_AddRelativeTorque_mEE1EAF067C96E1C94F8365DE649E36A92EDB133D (void);
extern void ArticulationBody_AddRelativeTorque_m6AE0A658FD43D01E018E181EF5CFFF202B103954 (void);
extern void ArticulationBody_AddForceAtPosition_m823FD5E55A00334AA6411AB1D448AC42B93B4398 (void);
extern void ArticulationBody_AddForceAtPosition_m4E8E2B65930B95CEAED38CED1116E8739ABF12CE (void);
extern void ArticulationBody_get_velocity_m3EBF7C1AACDBDDBAEDE1C1BB2BA4D7B6E09B86E0 (void);
extern void ArticulationBody_set_velocity_mA3002367A46044286F14619DA21A78A0AE862371 (void);
extern void ArticulationBody_get_angularVelocity_m32BB9324AC3B3B5FA1C569ECF7247E0519B46D6F (void);
extern void ArticulationBody_set_angularVelocity_m1AF7425EE7AE76E3E6DC4FE883071BDE17F7E61F (void);
extern void ArticulationBody_get_mass_m5733F78B9EC616543FE6554054E2612DE66BC526 (void);
extern void ArticulationBody_set_mass_m2AB9564735A157973D5391808422BC22546FA7F1 (void);
extern void ArticulationBody_get_automaticCenterOfMass_mAD4A2C8DBA0E7C44D4753CEE770807984E820E68 (void);
extern void ArticulationBody_set_automaticCenterOfMass_mE874C710ED017F851F866FF3EA5DCB113E01E3BF (void);
extern void ArticulationBody_get_centerOfMass_m446C2E8D092739D924A42E846AEFD08195FFBCF8 (void);
extern void ArticulationBody_set_centerOfMass_m1ECE3361868408A45CF7EB8EE1C8E32A5B3A2AD0 (void);
extern void ArticulationBody_get_worldCenterOfMass_m4E6429D6ECCD02036834E76871824C6355DF38D7 (void);
extern void ArticulationBody_get_automaticInertiaTensor_m8FCF2A23BF9B04CF49686370AAD996989BB75F79 (void);
extern void ArticulationBody_set_automaticInertiaTensor_m40CCF17F069654ADF879004C6DEA732BAA794908 (void);
extern void ArticulationBody_get_inertiaTensor_m51E37D406035FF9404BA41BC682C8846F246E2DE (void);
extern void ArticulationBody_set_inertiaTensor_m2BCDD0B82E88AF941B34AEB5034D069E9DFCE5CF (void);
extern void ArticulationBody_get_inertiaTensorRotation_mAD0842073A2BF44C014F55C524AB4F2FD8D511AF (void);
extern void ArticulationBody_set_inertiaTensorRotation_m45F2F004CC1ACA83D397884AB649D3AFF5587DB9 (void);
extern void ArticulationBody_ResetCenterOfMass_mED65F7632A8456E03DC5107E72A86CF4082704D9 (void);
extern void ArticulationBody_ResetInertiaTensor_m59D42A82D026D517F3879F579A03E3CEA05853A8 (void);
extern void ArticulationBody_Sleep_m86ED913C2CEBA591767BAF4B3D49F8630E5BC122 (void);
extern void ArticulationBody_IsSleeping_mD3AD91AB25B962FF423EEED9C4B39D60BFB01551 (void);
extern void ArticulationBody_WakeUp_m15BCC08A8355C8BC907C1F33AD29A0357D6F4AAF (void);
extern void ArticulationBody_get_sleepThreshold_mF3B4BFB1EB3279F76294858E8F0D8D2FD44B5703 (void);
extern void ArticulationBody_set_sleepThreshold_mF6DE7E565575ECB4C2D0F1451D5F56F80728F0B9 (void);
extern void ArticulationBody_get_solverIterations_m7DB7A009F4648341BAB4D3886CD1B162B91154C0 (void);
extern void ArticulationBody_set_solverIterations_mAF9E14598443294D1C2D0076B3564B24901C0377 (void);
extern void ArticulationBody_get_solverVelocityIterations_m8DFEB3020A6E19121031B967A8B7E5E596FDFD63 (void);
extern void ArticulationBody_set_solverVelocityIterations_mF4E07BB497F7E544FCB2B3DF9A92D5E1E4AA8141 (void);
extern void ArticulationBody_get_maxAngularVelocity_m597C5BF17A8D5BE704264DE02738528E79EE4014 (void);
extern void ArticulationBody_set_maxAngularVelocity_m445FB4FD76177E60722E503B3419CC88F474AE5D (void);
extern void ArticulationBody_get_maxLinearVelocity_m11EF78BDCFA6A6DD115C08B22135F2FAEB3F0E10 (void);
extern void ArticulationBody_set_maxLinearVelocity_m233275505A6E06C50F740147A05FB10D6062CBB4 (void);
extern void ArticulationBody_get_maxJointVelocity_mD8D109AE1DCD8066C79F2970687EDC5C742D9521 (void);
extern void ArticulationBody_set_maxJointVelocity_mF2F1D4EF96787FDB64BE8C13B7BF2D8035C6E6D7 (void);
extern void ArticulationBody_get_maxDepenetrationVelocity_mB575AF853E2FFE006BE417CEA02A75B362FC3B8E (void);
extern void ArticulationBody_set_maxDepenetrationVelocity_m4EFF4F615959AE87C2CD46444D8010E6784AD6F9 (void);
extern void ArticulationBody_get_jointPosition_m266ACE8D0F52AE5623773141D62B5A07DC301F88 (void);
extern void ArticulationBody_set_jointPosition_mAFD0E744B706BCD4392809468DFF1A5BD14F7B07 (void);
extern void ArticulationBody_get_jointVelocity_m75FD1283EB7800F6827F41CB66B4B44CCAF4EF70 (void);
extern void ArticulationBody_set_jointVelocity_mD9E1F524BA45EC9447BE9BED676328A7574989EF (void);
extern void ArticulationBody_get_jointAcceleration_m4B26B223EE46FCE0262EDF69664BA976A0E19DC1 (void);
extern void ArticulationBody_set_jointAcceleration_m9619B03EF12CCF0520C715C47532283D254E6F9E (void);
extern void ArticulationBody_get_jointForce_m0F4B1E1F3D10FE22AA6D99A05ADFF32C266EB617 (void);
extern void ArticulationBody_set_jointForce_m093F02D8B73621F4963E2349185101B8E0963FAF (void);
extern void ArticulationBody_get_driveForce_mAC6289F2C0FEAFCD484E99A018AA60ACFB09E829 (void);
extern void ArticulationBody_get_dofCount_m177710DDA46A7A519ADDA7136F0E76057310D7FD (void);
extern void ArticulationBody_get_index_m3D31F4C81F86B6CCBB0C9D15F156AD5891277BE5 (void);
extern void ArticulationBody_TeleportRoot_m1054AD6E80683565B7AFC6184BFF11CEC9B807F6 (void);
extern void ArticulationBody_GetClosestPoint_m1E269F8B5DA848D15D39D6C7FCB34A87A713AAB5 (void);
extern void ArticulationBody_GetRelativePointVelocity_m891039DC0E2EE050056C20EE7BDF71F0233B3D5F (void);
extern void ArticulationBody_GetPointVelocity_m11FAC064AD19F02E89E35007466F29F1954B12A1 (void);
extern void ArticulationBody_GetDenseJacobian_Internal_mC0A0A70A2D154A43F1907809E2C4CC559F074DA0 (void);
extern void ArticulationBody_GetDenseJacobian_m96341CB7ABADE5A042D6A768972CB5CEB41FED32 (void);
extern void ArticulationBody_GetJointPositions_m1CC7AAD19C42BFC43C6ECEEB7258A6B0AAD324AE (void);
extern void ArticulationBody_SetJointPositions_mACAE7A4AE300A7725967EE9FD457825028EC7452 (void);
extern void ArticulationBody_GetJointVelocities_m48183E0CF6951CA83A7EC552141DA4A44073D79F (void);
extern void ArticulationBody_SetJointVelocities_m6EF57ACE2CEF61DE3142B865269D93309FF83E47 (void);
extern void ArticulationBody_GetJointAccelerations_m613BF7554930EFB4DF970273B9B209EB5782E3A0 (void);
extern void ArticulationBody_GetJointForces_m33E4782752D2C88E72C7BB78ADB6052C8D3758F9 (void);
extern void ArticulationBody_SetJointForces_m1B2CD2FC20C1D6548B174E3C5A183088018B905E (void);
extern void ArticulationBody_GetJointForcesForAcceleration_mDB151463E9D57926A1A622B50149663FC64AD9D0 (void);
extern void ArticulationBody_GetDriveForces_m3BD0F74E6A292810C80CB99426F71BFE704A476B (void);
extern void ArticulationBody_GetJointGravityForces_mE1949F9308E901700EDE816210C4B63DC89C0BB4 (void);
extern void ArticulationBody_GetJointCoriolisCentrifugalForces_mE0E603B86EC6E37630E2C066765DFBC67BA5D298 (void);
extern void ArticulationBody_GetJointExternalForces_m1CBC37D2F99D1276A325B13146F7E894310CBAA4 (void);
extern void ArticulationBody_GetDriveTargets_mA2A8DFC26BEB1D61DCEB1ECC8D4EE0D3019284DB (void);
extern void ArticulationBody_SetDriveTargets_m13C98DA4C4E92A24452684F7C5DFE7E8783C8C49 (void);
extern void ArticulationBody_GetDriveTargetVelocities_mC70C64005D6821AB0A2081ADBE24C7595725626E (void);
extern void ArticulationBody_SetDriveTargetVelocities_m101968660AA0F04DCF7765B8A5B23F395B6F6EA8 (void);
extern void ArticulationBody_GetDofStartIndices_m89DA6F249B8796C5918C2AE9C0E68848EAF13384 (void);
extern void ArticulationBody_SetDriveTarget_mE49A731A0AEAD46C5BA315C0671D560B90DBC3EA (void);
extern void ArticulationBody_SetDriveTargetVelocity_mE3FAC776128CBADC5F29A16215FF1F9E144EE025 (void);
extern void ArticulationBody_SetDriveLimits_m942F2F9FCEBA3FA7F2A32432D568C68AE83A00FE (void);
extern void ArticulationBody_SetDriveStiffness_m62D7A7331147F4B29917EC3A3F5DCEA9A1C28F3C (void);
extern void ArticulationBody_SetDriveDamping_mC1D622926CCAF064A665D96D5CE217EB81E41D67 (void);
extern void ArticulationBody_SetDriveForceLimit_m673B219F59821E93623F073D7C952B26D589F198 (void);
extern void ArticulationBody_get_collisionDetectionMode_m4B835B0566653C7494241CA882E570DA6020613D (void);
extern void ArticulationBody_set_collisionDetectionMode_mD2C3B3682EE6580230211DF92A92A68A76325D52 (void);
extern void ArticulationBody_SnapAnchorToClosestContact_m1B0EAC5ECF85916DC0E343C3FC583DB9E6DFCF2A (void);
extern void ArticulationBody_get_computeParentAnchor_m76E8D931D9FAFADB374B689BBAFAB87D7D480E74 (void);
extern void ArticulationBody_set_computeParentAnchor_m3EB72D6F66ED41D5F2A9076F8D73611D144BCDCE (void);
extern void ArticulationBody_SetJointAccelerations_m842B40E9FD845111A975DB3C1E5D9BDC9199E9FC (void);
extern void ArticulationBody__ctor_m5B6C338377F7704618E1F1BEA870EE2BA9FCCA6F (void);
extern void ArticulationBody_get_anchorPosition_Injected_m6ED85849F4D6C1396B09A0959FEFAB9E186337B9 (void);
extern void ArticulationBody_set_anchorPosition_Injected_m6976A2A8A1A89A6FF518E3C17C1BAB6DE20B5785 (void);
extern void ArticulationBody_get_parentAnchorPosition_Injected_mD424237513DEE8E95E64DBB8E7340266B38A92A5 (void);
extern void ArticulationBody_set_parentAnchorPosition_Injected_m95E9FFC53E3CFAA5B399D60A8CE3D3D3BB12F148 (void);
extern void ArticulationBody_get_anchorRotation_Injected_mA5605F8AB57CB13492615E7D8CB62D05993C0E73 (void);
extern void ArticulationBody_set_anchorRotation_Injected_mC98E0761ADA276C6DC2BE14F2D61863A1AF9B2C6 (void);
extern void ArticulationBody_get_parentAnchorRotation_Injected_m10A670F6F42C6945039CE22BA9E1B633F0780277 (void);
extern void ArticulationBody_set_parentAnchorRotation_Injected_m7F8C9D52C2E24253ABCA0381ADCE875D3A487CC4 (void);
extern void ArticulationBody_get_xDrive_Injected_m94F855837D28DAC01527DB692CAAB7584DAD76C0 (void);
extern void ArticulationBody_set_xDrive_Injected_mD207FE149C3A28306D94C2946A4062E5C0E4D742 (void);
extern void ArticulationBody_get_yDrive_Injected_m04FECC54182229D2E7E041E0322B2F40077C1D14 (void);
extern void ArticulationBody_set_yDrive_Injected_m9BFFC234ED132E63F61F961E0C52C0C5A6858C85 (void);
extern void ArticulationBody_get_zDrive_Injected_m3B4B3BBCAA67A35127000656230611A5CF1E1013 (void);
extern void ArticulationBody_set_zDrive_Injected_m87E3ED2763A6358724D51847388170E76A4CC711 (void);
extern void ArticulationBody_get_excludeLayers_Injected_m01A779C216E47BC75A737F80FC1EA343068E6474 (void);
extern void ArticulationBody_set_excludeLayers_Injected_m28FB96297700F4FE8931976FD8F3C924BB22550D (void);
extern void ArticulationBody_get_includeLayers_Injected_m4C23A817F5B49BA8D1336B23CAF69E53F3FC8B56 (void);
extern void ArticulationBody_set_includeLayers_Injected_m619CD7233B1C669AE7F07D72C98E1813A9523FD0 (void);
extern void ArticulationBody_GetAccumulatedForce_Injected_m48D085CC7AAD7FFDC902A9AF11D07A3F938B5594 (void);
extern void ArticulationBody_GetAccumulatedTorque_Injected_mE92DB6DA6D36AF2FFC975C61578F55CBD3691543 (void);
extern void ArticulationBody_AddForce_Injected_mECA4881D1B7A8587D24A8345C9453882627C715D (void);
extern void ArticulationBody_AddRelativeForce_Injected_m1C91329D11FA619BAD29771428D01AE8D5599855 (void);
extern void ArticulationBody_AddTorque_Injected_m546EFB3BDB2EA9AA317AA3D7588849D51B4DAD80 (void);
extern void ArticulationBody_AddRelativeTorque_Injected_mD38DEBF374A5EC4275BF144F87CA865BBCCD5115 (void);
extern void ArticulationBody_AddForceAtPosition_Injected_m60B79E66C4F680F08348EA705E4046BED5414E30 (void);
extern void ArticulationBody_get_velocity_Injected_mD7E4BF8264F63620C38C6AB9D53CB20416576AA4 (void);
extern void ArticulationBody_set_velocity_Injected_mDD54625804CD16FB00CA902CC4169C866D815782 (void);
extern void ArticulationBody_get_angularVelocity_Injected_m54EFAEE9CE3BF113111486D69E7C7EF3E0BD8CAA (void);
extern void ArticulationBody_set_angularVelocity_Injected_mBA99D0426A024B937A4173DB1CE09E8D42494D9E (void);
extern void ArticulationBody_get_centerOfMass_Injected_m49003733F7C6AFD2D4DB65279F85C8F32614707A (void);
extern void ArticulationBody_set_centerOfMass_Injected_m3D958F8D2CF21692A40FF8E9D7287CEDA408EA5B (void);
extern void ArticulationBody_get_worldCenterOfMass_Injected_m37078593523173C807D7D29E6A5605193B2F7CB7 (void);
extern void ArticulationBody_get_inertiaTensor_Injected_mE40FE571128B90CEC8C18017F3426473E2F09596 (void);
extern void ArticulationBody_set_inertiaTensor_Injected_m71013B214BCA9EED4F242ED6FB9872C82B722D7B (void);
extern void ArticulationBody_get_inertiaTensorRotation_Injected_m2572DB18AC9655BC9F59862299570F405A3B56F9 (void);
extern void ArticulationBody_set_inertiaTensorRotation_Injected_m1C16580190C813F8C08BD062D3DDD80516E445D5 (void);
extern void ArticulationBody_get_jointPosition_Injected_m5A9D3284D2DEFF594DC3FFFDF69987A5752B6FE4 (void);
extern void ArticulationBody_set_jointPosition_Injected_m454B57C9B64BDD52B54A3BC893BD5DE6C380D418 (void);
extern void ArticulationBody_get_jointVelocity_Injected_m66897CA952FF43C35791C974BCBC40BF5CF9C684 (void);
extern void ArticulationBody_set_jointVelocity_Injected_m10A98EC383B780E521ECA449F0A6664E6FDFEDF5 (void);
extern void ArticulationBody_get_jointAcceleration_Injected_m5A3C9D82AC7A8FAFD6C2AF4A37A1FD80BA55B995 (void);
extern void ArticulationBody_set_jointAcceleration_Injected_m6483C2362F533D07AC0F926340631F06FDAFB0A2 (void);
extern void ArticulationBody_get_jointForce_Injected_m8220BDDB6B124E26274F7C51185E275D699A9682 (void);
extern void ArticulationBody_set_jointForce_Injected_mEE6424383372772EECA717489BB6FC2E09A3128B (void);
extern void ArticulationBody_get_driveForce_Injected_mEC1FB6B2482AE87EEE96D0996FBAE2C5C034562F (void);
extern void ArticulationBody_TeleportRoot_Injected_mA3FAA5AFA7AD43642AB35369F4AC2FC500B45A09 (void);
extern void ArticulationBody_GetClosestPoint_Injected_m40B588E2F6793643DF097C1801DF4AFA15AD56A1 (void);
extern void ArticulationBody_GetRelativePointVelocity_Injected_mDBC29A8E550F8ABE32A214BF79F2906FAAF2706C (void);
extern void ArticulationBody_GetPointVelocity_Injected_mA5268D1DDD14C13850396C6BD97C5691A4E83480 (void);
extern void ArticulationBody_GetJointForcesForAcceleration_Injected_m60AB5E346A2CC0FBF302BBDAF99BD718FEE30581 (void);
extern void Physics_add_ContactModifyEvent_m58DEA31C1DD8A2996A36991E98CA7B71C3E5EDAC (void);
extern void Physics_remove_ContactModifyEvent_m2A022D088F3B5F2214B76717DC93C3685B04EDDA (void);
extern void Physics_add_ContactModifyEventCCD_mE814521E0742D383E2D0CDE65560F6E439C42C96 (void);
extern void Physics_remove_ContactModifyEventCCD_mCD7BA8BB26675501BCFB914F34B7D6ED773405CF (void);
extern void Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D (void);
extern void Physics_get_gravity_m94393492AE4ED8B38A22ECCDCD2DDDB71BFA010D (void);
extern void Physics_set_gravity_mAEF3D6B45E6E567F04244C7889366CACCB4F1952 (void);
extern void Physics_get_defaultContactOffset_mD356CE957E83D14F43B3FF1BAFF6052F210A1B67 (void);
extern void Physics_set_defaultContactOffset_m81A4A3C4CB07A1DC86F5A8B53DE4A137985D4C49 (void);
extern void Physics_get_sleepThreshold_mA19D72F89C4AB632C16FA591071712428F701693 (void);
extern void Physics_set_sleepThreshold_m4C6D94024F0508934D79633DDC20F77C8D28DFC7 (void);
extern void Physics_get_queriesHitTriggers_m786C7A820398456AEA1972FB9DF02D8E7ED6BFBB (void);
extern void Physics_set_queriesHitTriggers_m763834B1050946C0C490CF9FF453783107AEF212 (void);
extern void Physics_get_queriesHitBackfaces_mF39895218290962301C1B01940CC403F2B87AA35 (void);
extern void Physics_set_queriesHitBackfaces_mC212AF1484AF3ECBF1922F52B56F78E16BDE31AF (void);
extern void Physics_get_bounceThreshold_mF5D7731B770E0BFC75C44C2A47B30F5AE1C0565F (void);
extern void Physics_set_bounceThreshold_m12BD6903B10DCA2606B6E9426F41FAE2DD015456 (void);
extern void Physics_get_defaultMaxDepenetrationVelocity_m1FBF12B2DE45B073DFF1C540EDF9ECC521A1B4FB (void);
extern void Physics_set_defaultMaxDepenetrationVelocity_m6810E49893C112C6129D83BF7806879631C8CE2A (void);
extern void Physics_get_defaultSolverIterations_mB03C0DF7B60A8D453BA8D1069AEB69BB83FE7969 (void);
extern void Physics_set_defaultSolverIterations_mB0A33680F0181471737C776367CB8F9F9F275B50 (void);
extern void Physics_get_defaultSolverVelocityIterations_mD0DDA38BBBF4FEEA60CF5B417F8F9E321878381B (void);
extern void Physics_set_defaultSolverVelocityIterations_mD05B7ED682A3A670258473C5A796DD430D15BCD2 (void);
extern void Physics_get_simulationMode_m5338D6C70919956484A3086D2DF5AAD47A046D54 (void);
extern void Physics_set_simulationMode_m03D88E56C28F200EF0B4B5B6250CE663387FD4A9 (void);
extern void Physics_get_defaultMaxAngularSpeed_m023AD46D9C5047F62DAD3792C1364F31E801E48A (void);
extern void Physics_set_defaultMaxAngularSpeed_m916DB68D77494363A8FDDC511A0BFC7DBDFC441A (void);
extern void Physics_get_improvedPatchFriction_m5E9F7696322D22B8C755E4A1F710D85A6205474E (void);
extern void Physics_set_improvedPatchFriction_mDB460BA53809468452FDDDF04608EEF8303CC1D8 (void);
extern void Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8 (void);
extern void Physics_set_invokeCollisionCallbacks_m99E0DBF4EFDF7E57D686938D11A6C81AB5360A8F (void);
extern void Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A (void);
extern void Physics_IgnoreCollision_mA8E5C54299FC47921E41BF864C7C2214621595D6 (void);
extern void Physics_IgnoreCollision_mFBAAD9B91D488802086C1A1C96447CE4C869211D (void);
extern void Physics_IgnoreLayerCollision_mBB904E854512B9F1A4EFD4E23D61BF2D0E0AA879 (void);
extern void Physics_IgnoreLayerCollision_m9270D67E34AE015D7177B5608852306E0366D6A1 (void);
extern void Physics_GetIgnoreLayerCollision_m6FAFF3D7B295E3ECC55DF0F3032AD4DB6210255D (void);
extern void Physics_GetIgnoreCollision_mDE42B19E92F4D749E111346E31B652A3A41915AB (void);
extern void Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D (void);
extern void Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377 (void);
extern void Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE (void);
extern void Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028 (void);
extern void Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688 (void);
extern void Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12 (void);
extern void Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33 (void);
extern void Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903 (void);
extern void Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8 (void);
extern void Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50 (void);
extern void Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488 (void);
extern void Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3 (void);
extern void Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB (void);
extern void Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025 (void);
extern void Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685 (void);
extern void Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5 (void);
extern void Physics_Linecast_mB0325E32F70A3BC4A56A06877DA5944355B3B368 (void);
extern void Physics_Linecast_mE693FAFE56D0E69918A0948310EF642094C91DC0 (void);
extern void Physics_Linecast_mA0E7B9AFA161F5A8432CA7CDDA25E20EC00AF2A5 (void);
extern void Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925 (void);
extern void Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6 (void);
extern void Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932 (void);
extern void Physics_CapsuleCast_m18FB0BA05A5E0AC9F1D4D115F560876C23A66662 (void);
extern void Physics_CapsuleCast_mF3A0D7F51EF7E3AF9E7EB34C14517D1604408713 (void);
extern void Physics_CapsuleCast_m870639B5EA540B86D28F19C6F079298A1523CF38 (void);
extern void Physics_CapsuleCast_m1111160E7494D23382ECAFEA420E6E9E6D78D621 (void);
extern void Physics_CapsuleCast_mAB6E13B795FAFAFE366DE88B8C7A31C54EAE9EF7 (void);
extern void Physics_CapsuleCast_m121518311930963660B3BFBF136931BCA2E3A986 (void);
extern void Physics_CapsuleCast_m0A540F025E5170C56348DBB377795CFA2EE9AFEE (void);
extern void Physics_CapsuleCast_m6D4A7249721F62B30FB349611B5F11029B3BCFAF (void);
extern void Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D (void);
extern void Physics_SphereCast_m2A41FD7023EC5B89B69E0A8948325BEF46D9597C (void);
extern void Physics_SphereCast_mD53E280903384004D4304150552D50DD3F8C1F58 (void);
extern void Physics_SphereCast_m7084426E8B20720EB3E374B51FCF56F22C24B0FF (void);
extern void Physics_SphereCast_m753A9C9335C4815C7C30816B70D3EF51E59F3AAD (void);
extern void Physics_SphereCast_mCE6B04501EE9D9DF9E6AE427BD9496F69467FAD5 (void);
extern void Physics_SphereCast_m62BD0F82567CE3D8865B117C8F6E9F09F2DAED08 (void);
extern void Physics_SphereCast_mE7CF6EC82AD380F5AF523A73646274ABE3F5C3FD (void);
extern void Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05 (void);
extern void Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7 (void);
extern void Physics_SphereCast_m3ACE0130E065A00D12D340E0FCB2B4988538B2C4 (void);
extern void Physics_SphereCast_mC78C6D9EC1FCE0052A8009D12A3EF398ECBFA36E (void);
extern void Physics_BoxCast_m1CFE2240751F5E67FD7D56A67CEBCC25A3991F27 (void);
extern void Physics_BoxCast_mBE97489FF57BE2B47A37E820DA10A7C3AF155580 (void);
extern void Physics_BoxCast_m73BF3E1FC9B8E2587BE333AD7F40C71E7ABFFBCF (void);
extern void Physics_BoxCast_mDDE7AED2DD992CBC382C529E5EE106D869B4630C (void);
extern void Physics_BoxCast_m115610507710F8215D31060325803EDD18078E5D (void);
extern void Physics_BoxCast_mB641B1C6FAB006950E3FB982FA42631231F496F0 (void);
extern void Physics_BoxCast_m4E88A3E8736788553C34CC7864CB204E223146BC (void);
extern void Physics_BoxCast_mFDE93D2989DBD1FD278230537A2530EC6DE76493 (void);
extern void Physics_BoxCast_mD1D6F883E3B2E4AB3BDE38CCB0A470A614A8C7BD (void);
extern void Physics_BoxCast_m5C4CCC14C34BFAF02B0CD92CBB52DAE803F14745 (void);
extern void Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074 (void);
extern void Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34 (void);
extern void Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51 (void);
extern void Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E (void);
extern void Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789 (void);
extern void Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74 (void);
extern void Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87 (void);
extern void Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468 (void);
extern void Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2 (void);
extern void Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE (void);
extern void Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC (void);
extern void Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C (void);
extern void Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E (void);
extern void Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836 (void);
extern void Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0 (void);
extern void Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9 (void);
extern void Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460 (void);
extern void Physics_Query_CapsuleCastAll_m874A33B3421181746FC7662648E0306620F7D522 (void);
extern void Physics_CapsuleCastAll_m367017D9CB85D5F7AA7448F70E16E94578C09214 (void);
extern void Physics_CapsuleCastAll_m9467728846F780021AF7D40168E7CA0D6A76F2AE (void);
extern void Physics_CapsuleCastAll_m82E63C6623BFB2DF2C878C3928A93D5FC24C53DA (void);
extern void Physics_CapsuleCastAll_mFC08941E9CCF4E80799C5E2DC7D59A3D36EE540F (void);
extern void Physics_Query_SphereCastAll_mCC57A65CDDCE43957EA9FFC80958AC495958E58D (void);
extern void Physics_SphereCastAll_mE651DDBD29BBBBC4E3D33BBDE3C9C082ACFCB91C (void);
extern void Physics_SphereCastAll_m25687FC02567C1138B9588148AA2578764CD8424 (void);
extern void Physics_SphereCastAll_m90D15BED8E37CCDC16BCD900697FDCB221A399EB (void);
extern void Physics_SphereCastAll_m7000B7404CD1D1F2C9B26FF73AD6E04FB7E7BC67 (void);
extern void Physics_SphereCastAll_m5C7FCEAE4A6ED88B35ED554EDE37BB8367F08F46 (void);
extern void Physics_SphereCastAll_m0886C4624531C71A2CC6A3196B9EAEE108C17CB5 (void);
extern void Physics_SphereCastAll_mA6D34F1A7023429AB5168049E5707BC43179C356 (void);
extern void Physics_SphereCastAll_m26EB51F5B7F27D1A3FF2B3DFDCE4648091D87B2D (void);
extern void Physics_OverlapCapsule_Internal_m089FEB489FE5BD280583DB2BF196D47A3E7BFD5D (void);
extern void Physics_OverlapCapsule_mE60FD023CFCAFCA0CD4A7D3BD01A8A2C20FC7415 (void);
extern void Physics_OverlapCapsule_m2CED600F34C5BA1E865C2F23720A62909B17B510 (void);
extern void Physics_OverlapCapsule_mFF90A7E21F9223A551B71EB9D5A90D4667AD3DCB (void);
extern void Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B (void);
extern void Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318 (void);
extern void Physics_OverlapSphere_m2D0C9BC78473512F1F89AE731FBAE1B734EDF3EE (void);
extern void Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69 (void);
extern void Physics_Simulate_Internal_m44166CBF3601E62BEA1B50D97D3F9BDF5C5EBF69 (void);
extern void Physics_Simulate_mE7FC4862A1E3917A4F281FFAC8C156692EFEB77C (void);
extern void Physics_InterpolateBodies_Internal_mFF9F605B6A615D26ADED0EBF02A852CBD2C00822 (void);
extern void Physics_ResetInterpolationPoses_Internal_mFF3E53A2B78BCEBED6164EAC45EA2C39E6EEB620 (void);
extern void Physics_SyncTransforms_mB88B6B27C24234D18846F614F9AE674976A5F1CA (void);
extern void Physics_get_autoSyncTransforms_mA8A6D3B610E34D42ED30373AFA99A418271D67CA (void);
extern void Physics_set_autoSyncTransforms_m22463A478613FF7A86F90A580BABFAE1A0D68414 (void);
extern void Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541 (void);
extern void Physics_set_reuseCollisionCallbacks_m6FC1AB597226A805692A0E1F3942D2DC548E2379 (void);
extern void Physics_Query_ComputePenetration_mFC4D885B9B0A1A511997F8F25D64117D075E3B88 (void);
extern void Physics_ComputePenetration_mA9AA5B3B6982BAC84467322616E8423CA4E91AFF (void);
extern void Physics_Query_ClosestPoint_mC72F36C1868C7830580ACF450FF84F797DC1C1EA (void);
extern void Physics_ClosestPoint_m440650B860B4298CEEAC45599E1B326A3B8FFF41 (void);
extern void Physics_get_interCollisionDistance_m737789D40ACAE21671B948C1F25D66BE437BC5ED (void);
extern void Physics_set_interCollisionDistance_mD391D15EBB74EF40E814AF628544F35D5790F854 (void);
extern void Physics_get_interCollisionStiffness_mC423087A495A6C26D194D85DD81E1A5CAE670978 (void);
extern void Physics_set_interCollisionStiffness_m230BA6D8B0E404AF161D5A691B3AAE9C33B5C2E6 (void);
extern void Physics_get_interCollisionSettingsToggle_mF2F24FB1BFC4161E4D921A4804FEEC4630765C53 (void);
extern void Physics_set_interCollisionSettingsToggle_m063E3B6499BEA583BC077D8BABE4939CF8CE866F (void);
extern void Physics_get_clothGravity_mF15D7BA3DEE591F134D61F0E2E14628CB82FBCE2 (void);
extern void Physics_set_clothGravity_m7C89C1C2121E338E81AFF529672A454DB11B33CB (void);
extern void Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83 (void);
extern void Physics_OverlapSphereNonAlloc_mB918CA3A78E54FE0D2933353AEB0A7A3EC0E07F7 (void);
extern void Physics_OverlapSphereNonAlloc_m48E30DC51482BBBBDD762ECE1DF83A70088F70DA (void);
extern void Physics_CheckSphere_Internal_mC2E521D96447A8560D127D3EA42CB5785014B6FF (void);
extern void Physics_CheckSphere_mC32BB961B0CF9D23EDDEEC3F30021FD1BE88E261 (void);
extern void Physics_CheckSphere_mD6F0027DBDECFA69245E99D8A4EE1DC8742A817F (void);
extern void Physics_CheckSphere_m3259DE166D6E8108517F008E91C65412D03E2852 (void);
extern void Physics_CapsuleCastNonAlloc_m8190CE6AFACAFF3996D60D60BD110E0AC2E2583D (void);
extern void Physics_CapsuleCastNonAlloc_m7825DBD7BD1FA6F3A8707B6FEFF955269A2AB40E (void);
extern void Physics_CapsuleCastNonAlloc_mF6BEDCD504784CD39041479E3DA41F020ADF1E8C (void);
extern void Physics_CapsuleCastNonAlloc_mC26DB8DB3DB591832564436B0993F6AD14CDF7F3 (void);
extern void Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C (void);
extern void Physics_SphereCastNonAlloc_mB423581F30D2DCB6A7B62F9E9366C7C82D8A83C4 (void);
extern void Physics_SphereCastNonAlloc_m942717E0D3B43A44253447354FC32BA148EBC105 (void);
extern void Physics_SphereCastNonAlloc_mBDD95C1C9B031C2BE462D1BFBB565277C77EDC22 (void);
extern void Physics_SphereCastNonAlloc_mAB717B15E509BADDCA42B06EB4407FBADACA04DE (void);
extern void Physics_SphereCastNonAlloc_m6466EC0F9999A34AB63EDBD74F1CAB3010AF7EF6 (void);
extern void Physics_SphereCastNonAlloc_m6DB44E22CBF5296F20BC68942B64F8D4F2866D28 (void);
extern void Physics_SphereCastNonAlloc_m2B5EA904538007D958BE3361DE6B64E10B818F66 (void);
extern void Physics_CheckCapsule_Internal_m1F9DFF4632C645B1B70DE9791826825F319B3D07 (void);
extern void Physics_CheckCapsule_mFFAADF3C5B987AA3ACBF33377C9D292FA8680532 (void);
extern void Physics_CheckCapsule_mB3EDF1AEBBD150949312D9656F8236502AB8B79A (void);
extern void Physics_CheckCapsule_mC07CE17079AAC02248518C81F8787055717CE836 (void);
extern void Physics_CheckBox_Internal_mA1ECAF804AB96FB62D123032399F78884ADEA364 (void);
extern void Physics_CheckBox_m70B2611C6652C7268F50122EFC439DCE05A488F8 (void);
extern void Physics_CheckBox_mF035945E1FA93C042EDDDF431246B78C4A686F15 (void);
extern void Physics_CheckBox_m08019B0562D47A876A970D21982FAEF9BD6EB06C (void);
extern void Physics_CheckBox_m5B2923A64067B62C69E076854221FA8C64F4230B (void);
extern void Physics_OverlapBox_Internal_m835436FA1701C08AC547331B8D594E2C86BB4031 (void);
extern void Physics_OverlapBox_mC38B579DEFD0341FCAEF8B8EC8B1E37A2C12366D (void);
extern void Physics_OverlapBox_mFEE3011D8955ABBE0FEBB746CC9EA595C18254DE (void);
extern void Physics_OverlapBox_m400B65BA73CA857B619D11D8B98805AB6936EFB8 (void);
extern void Physics_OverlapBox_m6F8E468A95A2F432413CF74ECE1533909565574F (void);
extern void Physics_OverlapBoxNonAlloc_m1D43D10CD88EF2D5440601D3CD14CA4EB449A295 (void);
extern void Physics_OverlapBoxNonAlloc_m25AC09559AFBF4F4D96387A07FED94B282F2A35A (void);
extern void Physics_OverlapBoxNonAlloc_m3038DC8025CE15A90EED056EE324AD9E547BDB76 (void);
extern void Physics_OverlapBoxNonAlloc_mFC72057341050361E8C66EF138A234EA919C3657 (void);
extern void Physics_BoxCastNonAlloc_m88245841A55DC2FF1C2334AAFD4A7667698B4A52 (void);
extern void Physics_BoxCastNonAlloc_m8BA4886C0C3FAE062167321FBC9939B95CEC73D3 (void);
extern void Physics_BoxCastNonAlloc_mA3A665BA4F9DDF6C194615B452DAC832F90418C4 (void);
extern void Physics_BoxCastNonAlloc_m57D783EA8900F68EA670380248C6C4EC7A41BB3E (void);
extern void Physics_BoxCastNonAlloc_m83CDA7F253A648CCF13D4A405251E68C62D38354 (void);
extern void Physics_Internal_BoxCastAll_m634C7FCFEE4EFB38A0DF65E00C9F90F01BC9BD0D (void);
extern void Physics_BoxCastAll_m0EB3E1DC72AD4194B358FF3E0E92AFC2F515F84F (void);
extern void Physics_BoxCastAll_mB157BA7623230BB4EF87FD291A116284CB7DED67 (void);
extern void Physics_BoxCastAll_m1A4C4A3BDD6FD43BE27047490A543E732FCEE2CC (void);
extern void Physics_BoxCastAll_m78F83BA3796FBEF28168C2FA658256F16ECF6BFC (void);
extern void Physics_BoxCastAll_mFFB862A7D86049DB378FD616FD0C8E69AC234800 (void);
extern void Physics_OverlapCapsuleNonAlloc_mD13F4F0604878062489892A77D92A161681DB167 (void);
extern void Physics_OverlapCapsuleNonAlloc_m9CFD945BB54EA98258099AB1D1157B2793509B8F (void);
extern void Physics_OverlapCapsuleNonAlloc_mA1E23131724F3E4031CD9054590C122C24CDD4A9 (void);
extern void Physics_Internal_RebuildBroadphaseRegions_mB9288280395C40EA903BAF3C9E28D78B4D27D84A (void);
extern void Physics_RebuildBroadphaseRegions_m0350248177162D5D1B15A933F89278E3E20AEDD5 (void);
extern void Physics_BakeMesh_m23D3FB52EDB6291399EA34CC49EF49A56FC3B639 (void);
extern void Physics_BakeMesh_m6F7F69DE9FDC8AA7D14F2D6CDD63A8191D561B23 (void);
extern void Physics_ResolveShapeToCollider_m5CEE97D6CD8C8618AF2A2124F1E846039FD574BE (void);
extern void Physics_ResolveActorToComponent_m6C2483CF83B43B27A172D28DA451CEF7EAF59F99 (void);
extern void Physics_ResolveShapeToInstanceID_mCC6BAE29B43D7C56A1BF13898E613431F8F3ECCE (void);
extern void Physics_ResolveActorToInstanceID_mCB40A39B679357687EB9CF0DD71B14D098F269AD (void);
extern void Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220 (void);
extern void Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725 (void);
extern void Physics_TranslateTriangleIndex_m7D14955C4C4DB05471A260A3C6FBDABD4D633283 (void);
extern void Physics_TranslateTriangleIndexFromID_m2DA314CCC15A77C895575D7B638CF3B56A75FEFA (void);
extern void Physics_IsShapeTrigger_m9AE8E1A88316CADDFBDBEA39D19580DE386EA2BB (void);
extern void Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144 (void);
extern void Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590 (void);
extern void Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7 (void);
extern void Physics_GetActorLinearVelocity_mF2D174447DB131E509590110B33A4583EEC90FB4 (void);
extern void Physics_GetActorAngularVelocity_m21F221C65E2262D5C5462773ED4ADED608957070 (void);
extern void Physics_get_minPenetrationForPenalty_m65793A390C85B80015B235EF0497A87B66BE614A (void);
extern void Physics_set_minPenetrationForPenalty_mF12BDE8DE79A932574F6F05E7E122B49EF86453A (void);
extern void Physics_get_bounceTreshold_m2B29D00EFE8FCC201E9E89F3150C2640D25988AB (void);
extern void Physics_set_bounceTreshold_m4D8DD3F66CDE804D483673364FF6AD5CA956FCF4 (void);
extern void Physics_get_sleepVelocity_mC5C3AEFE860F19CF50AF640CCA1E6F0D9C2A5151 (void);
extern void Physics_set_sleepVelocity_mDB4C60E58CA8DA5D30A6F68DADF4A11E2CBAD7B7 (void);
extern void Physics_get_sleepAngularVelocity_m0F25506AA33B0BF09FE12F8CF1E20536F4DB8CC3 (void);
extern void Physics_set_sleepAngularVelocity_m44D3BAE42D07060D4CE29DB8A7A3900F932742CA (void);
extern void Physics_get_maxAngularVelocity_m1AFDE8E8D99BBA667F322F8FC3E4AC72D42FB679 (void);
extern void Physics_set_maxAngularVelocity_m96F30B736B7ECA6A49BD6CF15404800EDFECCF1B (void);
extern void Physics_get_solverIterationCount_mDF3E3F60CEDB8028C0838049CE39A81C5FA3C73B (void);
extern void Physics_set_solverIterationCount_m929386814D4451EFA8E71DC3486FD3E24CF2F3CD (void);
extern void Physics_get_solverVelocityIterationCount_mA5A0E38663E66B1C2974D9EAC35763F1A9F78957 (void);
extern void Physics_set_solverVelocityIterationCount_mE9F732977CAB74DB0BCF20F03039ADC91547B334 (void);
extern void Physics_get_penetrationPenaltyForce_mE6482591C7751C425773443C08E2B39E22CF0EF3 (void);
extern void Physics_set_penetrationPenaltyForce_mAFD75C9AFD9B2DD9507C2707EA0571AFBF160BC6 (void);
extern void Physics_get_autoSimulation_m6D875546CE30125226E7E92318F2342F4AD57F83 (void);
extern void Physics_set_autoSimulation_m1689299C0328790F6C350B40B53D092DA033389B (void);
extern void Physics_add_ContactEvent_mC17FF0C5F506FBE965F00FEDC0F73146DF7E036C (void);
extern void Physics_remove_ContactEvent_m42E318EA4333EFF8E6B001FA58ED572361DB6269 (void);
extern void Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A (void);
extern void Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D (void);
extern void Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E (void);
extern void Physics__ctor_m84E8B98DEF92CF55210651857A7E9E841BBF4658 (void);
extern void Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A (void);
extern void Physics_get_gravity_Injected_mBB1051A73AE88880090895AB007FEF35965E192E (void);
extern void Physics_set_gravity_Injected_m49F0D978D86877DB428952902EDAD3EF0F759DE7 (void);
extern void Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6 (void);
extern void Physics_Internal_RaycastAll_Injected_mAFAA47E2224DEA0ABF1A2188A969E7A663E50C92 (void);
extern void Physics_Query_CapsuleCastAll_Injected_m7F070069C5BD11521A5428C730BED632FBDC50E3 (void);
extern void Physics_Query_SphereCastAll_Injected_mE7002462EB2AE09CBF7E5B78E504C0432F528B3C (void);
extern void Physics_OverlapCapsule_Internal_Injected_mBC843BBCF8DFBA4E90888001E51F76FA83B8C147 (void);
extern void Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B (void);
extern void Physics_Simulate_Internal_Injected_mB41A10BBFCEB591F87272F587D91CAAFB41EBD28 (void);
extern void Physics_InterpolateBodies_Internal_Injected_m8C67ABAA35464AD50A7FF3BF9930C68EE341D0CC (void);
extern void Physics_ResetInterpolationPoses_Internal_Injected_mB01B48C493C73065EF880E49F0D6F7B274CF49B9 (void);
extern void Physics_Query_ComputePenetration_Injected_mDDA9AC3B6EC05A824EE608809BD11266E4D529B2 (void);
extern void Physics_Query_ClosestPoint_Injected_mDB84C796000699B2CF45B2F054EDBE652371C975 (void);
extern void Physics_get_clothGravity_Injected_m1F422A9A9EAFB567CE66F872BA47FAE326B5CB9F (void);
extern void Physics_set_clothGravity_Injected_mF86D577C95D4C5B8EE12B4BADB7F540560478AA1 (void);
extern void Physics_CheckSphere_Internal_Injected_m2ACFB56AF137AC31796C00809EBA6D55FA8BBD9A (void);
extern void Physics_CheckCapsule_Internal_Injected_m30EA1EC82CC5199CD410611675623963C4B0F220 (void);
extern void Physics_CheckBox_Internal_Injected_m0EE51B035C17AE9C4D4FB039402F030B2B2EA6F4 (void);
extern void Physics_OverlapBox_Internal_Injected_m32F1F9FC2EF4F6F47296862932717FCFD1DC3C21 (void);
extern void Physics_Internal_BoxCastAll_Injected_m4C3680663C48199A818151AD9C17660742DCC5C6 (void);
extern void Physics_Internal_RebuildBroadphaseRegions_Injected_m8283979760DA6B38044A12A07D53BA089E1B7BCF (void);
extern void Physics_GetActorLinearVelocity_Injected_m94D0924F5A6808D430697D4A821D832BA27241E6 (void);
extern void Physics_GetActorAngularVelocity_Injected_m1C3E8F4AAB09128D5BA0E3E0E8EBE92BFBB4552C (void);
extern void ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE (void);
extern void ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769 (void);
extern void ContactEventDelegate_BeginInvoke_mE57713922B33734B0F0DFFEADC0882DE872DE76D (void);
extern void ContactEventDelegate_EndInvoke_m1DE073679CEFD192878825FDCE259EBACC7E5240 (void);
extern void ModifiableContactPair_get_colliderInstanceID_m555E31D2818CEDAC9D58538314D80701EDED2C9F (void);
extern void ModifiableContactPair_get_otherColliderInstanceID_m92593BF590D5F14345E4DA5AADF9D61D0C55D363 (void);
extern void ModifiableContactPair_get_bodyInstanceID_m8B56E95EE29C2957E3166B3616B3E02AE8176D3B (void);
extern void ModifiableContactPair_get_otherBodyInstanceID_mE275D82AE73ED679018A57A87A04656598A587AF (void);
extern void ModifiableContactPair_get_bodyVelocity_mE291E12A45BF3D11E31252D5D0F48CFE9979878B (void);
extern void ModifiableContactPair_get_bodyAngularVelocity_mDE015C32239E67B14D5F0E74D6F0C6D1A12F2D97 (void);
extern void ModifiableContactPair_get_otherBodyVelocity_m08C3C21AB9B5FB55EBE3BE3FD34EA10D02830F46 (void);
extern void ModifiableContactPair_get_otherBodyAngularVelocity_mFEE2BB3A0CBAC44D35EDFE528810B1EF7779587F (void);
extern void ModifiableContactPair_get_contactCount_mE6A959F3C3DAC88580404AE830E666A74AC9DAB7 (void);
extern void ModifiableContactPair_get_massProperties_m6E657FD7D82285D3993FF9085ABBCC4F0CF169E8 (void);
extern void ModifiableContactPair_set_massProperties_mD53EC6724C3F093A3C4C08BCCC1815A858B628DE (void);
extern void ModifiableContactPair_GetPoint_mAD9A2FCD25D217AF868CDE3A8F3C6977F8D1B2F6 (void);
extern void ModifiableContactPair_SetPoint_m3CD65095DE1222A9B7F1A57B4BB91A5B2ADE043A (void);
extern void ModifiableContactPair_GetNormal_m6C0A9110EFC0EF0E8485D9A1A63ED91A7347B681 (void);
extern void ModifiableContactPair_SetNormal_m1D65EB3CA8B5BA26FDB3565CA9933DB5EE2EE959 (void);
extern void ModifiableContactPair_GetSeparation_m5BE2C0DEBD531977AD9C56632F4F9E7CD157C9C7 (void);
extern void ModifiableContactPair_SetSeparation_m9D69A9DF8A5F726E24C7224549944B54EED4D48F (void);
extern void ModifiableContactPair_GetTargetVelocity_m92DBB0CECE637554C12BCC9ECC6CCEFE1124648E (void);
extern void ModifiableContactPair_SetTargetVelocity_mA07021905C401183F59C7D3AB9B28A0E0EFBD7FB (void);
extern void ModifiableContactPair_GetBounciness_m42DF58A5A3BEB6E55836F4E91CC210E01D5101F6 (void);
extern void ModifiableContactPair_SetBounciness_m5FFB3F3B7AA6BBD143E423570DAFFA0F4F86F74B (void);
extern void ModifiableContactPair_GetStaticFriction_m836099A4AED9C8519C9D977F30C3CAD83B069D16 (void);
extern void ModifiableContactPair_SetStaticFriction_m258ADDF7FB804005D5A008118E341F7FD27937C2 (void);
extern void ModifiableContactPair_GetDynamicFriction_mB4975A0337DEFF0DE340F3097E8E4BE8754FA9FE (void);
extern void ModifiableContactPair_SetDynamicFriction_m80A5A32EC1E1232F2814760D4D244D03E63CE362 (void);
extern void ModifiableContactPair_GetMaxImpulse_m887290F5A2A460EE2F7A937D416A1819E280FA63 (void);
extern void ModifiableContactPair_SetMaxImpulse_mBAEBD045C31CD54B65389D988B51163C656AE77A (void);
extern void ModifiableContactPair_IgnoreContact_m041CE733E6A9425E8F1EE1278DBC43A90B0226A8 (void);
extern void ModifiableContactPair_GetFaceIndex_mCD63E169BF58DC041F1C14C732F24056E74B3461 (void);
extern void ModifiableContactPair_GetContact_mEE629C8AC75FBD7A833BD9594A783D5A5B02BEFA (void);
extern void ModifiableContactPair_GetContactPatch_mFEA78CD88438A9D61844A643460C4BD8AB0FBC8F (void);
extern void PhysicMaterial__ctor_mD8ECF21D92EBF6A8C5517E7EB4D2D089B4F78D3E (void);
extern void PhysicMaterial__ctor_m78BA71B067808944CAC6214299A5E6BC85691F4E (void);
extern void PhysicMaterial_Internal_CreateDynamicsMaterial_m7A2577629C56F741F3B03B7E859611A20241F3C1 (void);
extern void PhysicMaterial_get_bounciness_m9AA6C5656A9643A87B9D25304A9C8269D179CE8F (void);
extern void PhysicMaterial_set_bounciness_m99D8D24F76D60306CC4CFE38AD43BF240F84FDF9 (void);
extern void PhysicMaterial_get_dynamicFriction_m3C420FDF958DD1AB1FC8CC78B102BCD3ECE44841 (void);
extern void PhysicMaterial_set_dynamicFriction_mF41FC9F0BB5E70CF1AD4322FE67745AD612D7197 (void);
extern void PhysicMaterial_get_staticFriction_m1E17EA04646C5FCC720E82BE3A430324D0494AFF (void);
extern void PhysicMaterial_set_staticFriction_m737457B7A2346BFB5D05BC7322F2A49823243011 (void);
extern void PhysicMaterial_get_frictionCombine_m09915419D3DD57DF49A64B91B6E24AF9A1208707 (void);
extern void PhysicMaterial_set_frictionCombine_m4F81ED0AC04BF634B0ACB33629CDB16C2ECBD28D (void);
extern void PhysicMaterial_get_bounceCombine_m47A9B9E71B854010CD3DC26B0136D33265A00E9A (void);
extern void PhysicMaterial_set_bounceCombine_m64DBF7D0F9C447DD5E0D19A6A24F0F5945C0BB1D (void);
extern void PhysicMaterial_get_bouncyness_m533C3C94A7D3D8B7A2F7DD3F984CDAE325470560 (void);
extern void PhysicMaterial_set_bouncyness_m10DE2EF2C3A4D0D3A018FD3F61DA0FEA47E62E84 (void);
extern void PhysicMaterial_get_frictionDirection2_m22D245F6D4DC788EC6E90A615542C63097AC7A39 (void);
extern void PhysicMaterial_set_frictionDirection2_m24790B524E96C40E943EAAF4688C04114FE4F980 (void);
extern void PhysicMaterial_get_dynamicFriction2_mB5644B2890AF7466053448FA400641C6C048976A (void);
extern void PhysicMaterial_set_dynamicFriction2_m4BA8BD2154E6274BA8097D996D583147B4C21884 (void);
extern void PhysicMaterial_get_staticFriction2_mC41921B2D2D9CF155D373D5E337D41EA81CBA372 (void);
extern void PhysicMaterial_set_staticFriction2_mCB4AC98068B7CA1B0475652C3826CC3186811CEA (void);
extern void PhysicMaterial_get_frictionDirection_m69A3D56E069115708A1B27C01E3E06D92451E8C0 (void);
extern void PhysicMaterial_set_frictionDirection_mB04492F3A9CCE465063053690A177E891A7ACA8C (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D (void);
extern void RaycastHit_get_colliderInstanceID_m4CEBF5D185F207B1F958A93EA62AF35BE889D758 (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (void);
extern void RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11 (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (void);
extern void RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B (void);
extern void RaycastHit_get_barycentricCoordinate_m15E866896213623E3E49B54F4273E343A50B1797 (void);
extern void RaycastHit_set_barycentricCoordinate_m97B7F8D06D728E9881527E1C1AA4FCCADEEC4C64 (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78 (void);
extern void RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC (void);
extern void RaycastHit_get_triangleIndex_mA363EA340DC5E202DA8E9AC6DF7CCFA20D6EF72A (void);
extern void RaycastHit_CalculateRaycastTexCoord_mC614773633D8C01BC9143536C0AAE22F8D575806 (void);
extern void RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D (void);
extern void RaycastHit_get_textureCoord2_m93F06D5875AE4C8EBD21B9E184CF4FE3117EF704 (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155 (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005 (void);
extern void RaycastHit_get_articulationBody_m19487EF6AFD5B39F682254E2EB47E3313C24357A (void);
extern void RaycastHit_get_lightmapCoord_mDA52EAAC7C3E7170AFDE13DE7183E6CDD91D7BEE (void);
extern void RaycastHit_get_textureCoord1_m62351A7A51BB86A5614D6B9BE49EE9A8DEF215AE (void);
extern void RaycastHit_CalculateRaycastTexCoord_Injected_m5BE3F0E10ADADB27EE315FFDDD4A99493B70AE4C (void);
extern void Rigidbody_get_velocity_mAE331303E7214402C93E2183D0AA1198F425F843 (void);
extern void Rigidbody_set_velocity_mE4031DF1C2C1CCE889F2AC9D8871D83795BB0D62 (void);
extern void Rigidbody_get_angularVelocity_m4EACCFCF15CA441CCD53B24322C2E7B8EEBDF6A8 (void);
extern void Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0 (void);
extern void Rigidbody_get_drag_m03B87FE60D5ABB7B937993112982C75080929D54 (void);
extern void Rigidbody_set_drag_m9E9F375A26A8F3D1AABCEB015E41696F39088EE0 (void);
extern void Rigidbody_get_angularDrag_m457FD99D051229084E77422FA669454E1B58AB4D (void);
extern void Rigidbody_set_angularDrag_m4193B04EEFCA831DB99E29E98F778957557F130C (void);
extern void Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054 (void);
extern void Rigidbody_set_mass_mC7F886DEDB57C742A16F8B6B779F69AFE164CA4B (void);
extern void Rigidbody_SetDensity_mD76C832D898ABD95316127E71305D33CC16FD16D (void);
extern void Rigidbody_get_useGravity_mBDA227BDCB0F9A81B61A6592929EE43EDDEE7D16 (void);
extern void Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389 (void);
extern void Rigidbody_get_maxDepenetrationVelocity_m432BA40064E758F1189E086EAB0244083033C597 (void);
extern void Rigidbody_set_maxDepenetrationVelocity_mEE787E12E070438903558B0C79DDD98E3A5CFFD7 (void);
extern void Rigidbody_get_isKinematic_mC20906CA5A89983DE06EAC6E3AFC5BC012F90CA1 (void);
extern void Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED (void);
extern void Rigidbody_get_freezeRotation_mF698D98D5E43E86D2577B2496134706627EF7183 (void);
extern void Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC (void);
extern void Rigidbody_get_constraints_mAD5C536F3329399A4F32C05C3BC52821737C9AE1 (void);
extern void Rigidbody_set_constraints_mE81BF0DAEB980E320538231E092CA4663885A9A3 (void);
extern void Rigidbody_get_collisionDetectionMode_m5A18E2DE70F4C37841300A34A183FF3ADE01D943 (void);
extern void Rigidbody_set_collisionDetectionMode_m70A22E9878027BF6D3D7E851A43A8E32B8E02343 (void);
extern void Rigidbody_get_automaticCenterOfMass_m3587BB61F16B88B31D31C55C8B9960051C4D799E (void);
extern void Rigidbody_set_automaticCenterOfMass_mD131AE691EACA23A9FEFF45867E1F8C346AEFAB6 (void);
extern void Rigidbody_get_centerOfMass_mA66BE4DE0469545EBCF49A66EE4FDD3A5D0ADF91 (void);
extern void Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188 (void);
extern void Rigidbody_get_worldCenterOfMass_mFDEE86A44A9D44DC490B283C636CE770672F8FCE (void);
extern void Rigidbody_get_automaticInertiaTensor_m62BE477CFA29B626489BD68C43EAA9820F81F10B (void);
extern void Rigidbody_set_automaticInertiaTensor_mFDEAC3819314D7508E98902978BB2CFA7104FDC5 (void);
extern void Rigidbody_get_inertiaTensorRotation_m5B0FCE37D5C05D61375B954177B2EC51AF2E0E6F (void);
extern void Rigidbody_set_inertiaTensorRotation_m732666F7ACD440F5472A04AAEF1545D12C63450C (void);
extern void Rigidbody_get_inertiaTensor_mF57CE415F0DF4DBF17C6D0B7B006B3E1E6289780 (void);
extern void Rigidbody_set_inertiaTensor_m68E7B9842A8125237E1DEE5251840DF2D4DBB8C9 (void);
extern void Rigidbody_get_detectCollisions_mA2AF681B49EF1820F2B30EF86B1082FC661C9A51 (void);
extern void Rigidbody_set_detectCollisions_m42A50DFACA7709DA1F87BCB9DC0BDA00720C80CF (void);
extern void Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691 (void);
extern void Rigidbody_set_position_mA15BE12B8D82220E8CA90A0F0CBFB206FE81B41C (void);
extern void Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1 (void);
extern void Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D (void);
extern void Rigidbody_get_interpolation_mE508FC846FB031C118464637507C004408A32696 (void);
extern void Rigidbody_set_interpolation_mC7D39114A7AC6ED0AB2B40FECA4E2ED3C1D7603C (void);
extern void Rigidbody_get_solverIterations_m8FCD8D9D946FD23209516CD40D5A6A39B86C0DAA (void);
extern void Rigidbody_set_solverIterations_m533625CFDF6CB3E9412AD2ACD3FA13A6636A401C (void);
extern void Rigidbody_get_sleepThreshold_mC6B5C703DBA60ED8FEA8519ED0D62CC828D03399 (void);
extern void Rigidbody_set_sleepThreshold_m5180E11C2D6A401352863FB66812C683C663B220 (void);
extern void Rigidbody_get_maxAngularVelocity_mE04AD81F38A944B9123523422A0248FABE482FF4 (void);
extern void Rigidbody_set_maxAngularVelocity_m26E48B1DC6B9F8DBB81EE0681ABEB3AB255FC3F6 (void);
extern void Rigidbody_get_maxLinearVelocity_m7FE7BC362D0F2AA40C7D2965D53162553EFE7891 (void);
extern void Rigidbody_set_maxLinearVelocity_mED5CFF32D0FB795DF6219AAD871B625492245713 (void);
extern void Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9 (void);
extern void Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D (void);
extern void Rigidbody_Move_m48315B47C2BD90B45484029C59036CA8979C7E13 (void);
extern void Rigidbody_Sleep_m9826BDFCF078DF00223011B3F0FA7F4894F8F4CA (void);
extern void Rigidbody_IsSleeping_m059CBAD60AA4A6CA666FE2EAD2D7A3B02269E43F (void);
extern void Rigidbody_WakeUp_m64CF3AFAAC3CBB5360947731C1F77F13CDB960AD (void);
extern void Rigidbody_ResetCenterOfMass_mA8315F8324A97410D77B811833DCC80BD4EB361D (void);
extern void Rigidbody_ResetInertiaTensor_m34020552CA2D42DEA3E01562641A9B292848BD01 (void);
extern void Rigidbody_GetRelativePointVelocity_mFFB9A7105C436D0D759C6B20B3A206CB4F0942F1 (void);
extern void Rigidbody_GetPointVelocity_m94324B9CDC28751DB27594ADE76FEAB5EC4EB1BD (void);
extern void Rigidbody_get_solverVelocityIterations_m8CF27193497E8A9553956AA0317BE9702B45964C (void);
extern void Rigidbody_set_solverVelocityIterations_m53C09CB42CA4DA944D52E012BAF8112719AD753B (void);
extern void Rigidbody_get_excludeLayers_m614A65C71A4E87F55BD1F3AC506866FC4F45F376 (void);
extern void Rigidbody_set_excludeLayers_m9BA59762CD6C645D7E8208A5BFDFC839B951B904 (void);
extern void Rigidbody_get_includeLayers_mA40DBB1AC488534DB9DE6D3B15382EF5BAC781A7 (void);
extern void Rigidbody_set_includeLayers_mF5BA8F42B7335D2286E8AC897CA3C8CEAA240B4F (void);
extern void Rigidbody_GetAccumulatedForce_m60A0C4F5C07014DA033CA83FEAE3EBDF31CC73CB (void);
extern void Rigidbody_GetAccumulatedForce_mBA42828F11EEED7C87F57A695E2848CDF648A18A (void);
extern void Rigidbody_GetAccumulatedTorque_m28E12D3B6B6BD633594B03AA6E261B80D5647C17 (void);
extern void Rigidbody_GetAccumulatedTorque_m9DED96F3D8C591BDCD15269AC17774528E276CBD (void);
extern void Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC (void);
extern void Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198 (void);
extern void Rigidbody_AddForce_m264F2851A456AA18D4F04B21AF23814E61A39B75 (void);
extern void Rigidbody_AddForce_mFD97FC9DA828C1952D46D28C50B1D994B19895F6 (void);
extern void Rigidbody_AddRelativeForce_mAF5EA6C0A2417A4C72AF31538D66EB9612CB6543 (void);
extern void Rigidbody_AddRelativeForce_m16FBD1F3B609D21930E73B6E9474D8917D3918E6 (void);
extern void Rigidbody_AddRelativeForce_mB69BDA6ADD9BD66EF1CB919B28BFEE4492F1D4E9 (void);
extern void Rigidbody_AddRelativeForce_m332A7C65AC7DAFF1AB072237AD0C98DF43CD1FC6 (void);
extern void Rigidbody_AddTorque_m7922F76C73DACF9E1610D72726C01709C14F0937 (void);
extern void Rigidbody_AddTorque_m39C767D6CD12B2D12D575E2B469CB5565BFA30B6 (void);
extern void Rigidbody_AddTorque_mF99DCFAB779A5CEDE19D79926C39123B631B8F44 (void);
extern void Rigidbody_AddTorque_mC55713BD82CB5EFB0D6C705B98F030717EF50CA1 (void);
extern void Rigidbody_AddRelativeTorque_m98DD3E53803D7E5BA726CC98FBFA58C2350F2233 (void);
extern void Rigidbody_AddRelativeTorque_m8A4883737B7F8BDC0B25144986F74C4B9F789311 (void);
extern void Rigidbody_AddRelativeTorque_m117DF8F7B92DECCB2C6A57F3C6747E5237FEC89D (void);
extern void Rigidbody_AddRelativeTorque_mF1533E306ACDDBB49564E7761A887897966F4705 (void);
extern void Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C (void);
extern void Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965 (void);
extern void Rigidbody_AddExplosionForce_mE4673F6D1DA0C206DA79659E9005A0F067348402 (void);
extern void Rigidbody_AddExplosionForce_mD8FF6CAA6FF6749259FB95762B2A521CF8483163 (void);
extern void Rigidbody_AddExplosionForce_mD36F7D864F32F22DA1783D20F6E9563A9C51DFA1 (void);
extern void Rigidbody_Internal_ClosestPointOnBounds_m62E094281730FC5CADB1A9DEEE9549B806303D43 (void);
extern void Rigidbody_ClosestPointOnBounds_m2DF6444FD5993A0A1B703874754DE9FBA9F95309 (void);
extern void Rigidbody_SweepTest_mB87EFA8A4215626EEFB518D3B5F54D20ED913ED4 (void);
extern void Rigidbody_SweepTest_mF7D04E7199069664EC2FB2418F3FFB220CDE6BF8 (void);
extern void Rigidbody_SweepTest_m2A2783DE4C9C557F6AB58514B02DCD5209A0FA60 (void);
extern void Rigidbody_SweepTest_mD1FED67424FB5668B9CA3B8973B241CB9F51BD1B (void);
extern void Rigidbody_Internal_SweepTestAll_m31D82D649A42BDC06DB291BD6076F289B87F8B89 (void);
extern void Rigidbody_SweepTestAll_m3272031A076B4C455517A6F29267850DC638B8EE (void);
extern void Rigidbody_SweepTestAll_m09CDDB81ADE48D70CF94932638894614E4D775EC (void);
extern void Rigidbody_SweepTestAll_m7F8BB2FFCEA5828ACD9FE20786D00278B603C8F6 (void);
extern void Rigidbody_get_sleepVelocity_mF86D9EC45EE420A81BF74D09922262BD73E204A3 (void);
extern void Rigidbody_set_sleepVelocity_mE130340D14FF37D7692B40BD111430DA0D774095 (void);
extern void Rigidbody_get_sleepAngularVelocity_mA2C70CAA68DB5C14B4978AEE009C25D701C42DAD (void);
extern void Rigidbody_set_sleepAngularVelocity_m706F4521A609F3339B00D3D806D527A1EBB298DB (void);
extern void Rigidbody_SetMaxAngularVelocity_m98005580752C2E4E7127D5A9E0756D5CCC082FE3 (void);
extern void Rigidbody_get_useConeFriction_m57C5137300A7C6B9F68CDFFBB2E4D949DE8D834A (void);
extern void Rigidbody_set_useConeFriction_mA894A8098790FC07B14F0ADA3A4ED3FAF068DCA9 (void);
extern void Rigidbody_get_solverIterationCount_m9BA2DF4EC518B59C59B818FB23F8AF766034CF48 (void);
extern void Rigidbody_set_solverIterationCount_mA7DA2BD97FCA6C235AB327BCF67B9AE580F80811 (void);
extern void Rigidbody_get_solverVelocityIterationCount_mE7C2B55566DC755C57FEA7BF3CE0C74E49045737 (void);
extern void Rigidbody_set_solverVelocityIterationCount_m3A88732F1B4776E8B66B7B07001A3FF5CD5A2474 (void);
extern void Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07 (void);
extern void Rigidbody_get_velocity_Injected_mFD6FCA2857D9953AA953DB9AAF26A88CA881171C (void);
extern void Rigidbody_set_velocity_Injected_m41B399E90D6AA49BABD3C178B3183AD3BBB4EAC4 (void);
extern void Rigidbody_get_angularVelocity_Injected_m1F0D38AD14491E05E18C0E2C043F777FADC588BC (void);
extern void Rigidbody_set_angularVelocity_Injected_mD7EA47CB618918BD45985951E5A5388853975E68 (void);
extern void Rigidbody_get_centerOfMass_Injected_m1E6C506D04F8D066FAD03C571FA926E8FACB1518 (void);
extern void Rigidbody_set_centerOfMass_Injected_mF33B1D20FF435377B48235CDD322478131AE4B1B (void);
extern void Rigidbody_get_worldCenterOfMass_Injected_m0856EB16F154FAB8C8A1245555039D1EE055D703 (void);
extern void Rigidbody_get_inertiaTensorRotation_Injected_mED822CADC19012C92577BD31FE552E057A1DD4E7 (void);
extern void Rigidbody_set_inertiaTensorRotation_Injected_m8BC9257C5251E9ED85074885E93EFBB2BDC36809 (void);
extern void Rigidbody_get_inertiaTensor_Injected_m8F39AA878C625E9A5CDCD6804B70683BA17B6FB9 (void);
extern void Rigidbody_set_inertiaTensor_Injected_m1172388EC4D674FF91B090F0A58707659A5032AF (void);
extern void Rigidbody_get_position_Injected_m12A715C52CD3C7F66125950D7AB6ECFCF4336626 (void);
extern void Rigidbody_set_position_Injected_m8EB4DEECAE5A3D841A71B015807FF0820C3A361B (void);
extern void Rigidbody_get_rotation_Injected_m38431B37B78B4DF59B59F9CB7E609820430051F8 (void);
extern void Rigidbody_set_rotation_Injected_mDE5B37E97D8FE16D9AD8FC6B9213A106B28589D7 (void);
extern void Rigidbody_MovePosition_Injected_mF2CDF14960A920DCDDEAFA49A7E066A2FF021E37 (void);
extern void Rigidbody_MoveRotation_Injected_m75B6A86B8BE8D68714CA5356DDCC11D24B96B505 (void);
extern void Rigidbody_Move_Injected_mB7775F4369189F1934A6ACC1D13D56EDF5220A59 (void);
extern void Rigidbody_GetRelativePointVelocity_Injected_m685F932DF242C8ECFD854D336D9973A3CB8AFC7D (void);
extern void Rigidbody_GetPointVelocity_Injected_m343FC8424FDE36AE563D07BC3B7506CF94E3E4A4 (void);
extern void Rigidbody_get_excludeLayers_Injected_m4442C59992FD0B190F2A30D2B13905E2E97CC2D6 (void);
extern void Rigidbody_set_excludeLayers_Injected_m44BFF6F35887A375476903D5FE8AADE6C3250F08 (void);
extern void Rigidbody_get_includeLayers_Injected_mE0E5307E80BD2E11B616ED56FA7FC270FF21C550 (void);
extern void Rigidbody_set_includeLayers_Injected_mE47FEB4028599B3BCE8E9E215D3A8E01D7B5C9EF (void);
extern void Rigidbody_GetAccumulatedForce_Injected_m202D7B23C05967EFD6C5EEB9A2D3A73090FAA7A9 (void);
extern void Rigidbody_GetAccumulatedTorque_Injected_m36CEA5E34A3E16540D869C1A094C541702C182BA (void);
extern void Rigidbody_AddForce_Injected_m094E54DEA6CEAEA340F053D077CDF0753900F48E (void);
extern void Rigidbody_AddRelativeForce_Injected_m9BD7D9D36B62C306CA7D30CEB6BDFCDEDDFF9DF3 (void);
extern void Rigidbody_AddTorque_Injected_m22FC0760ED37AE38443DB926B2A7D87470A63CF2 (void);
extern void Rigidbody_AddRelativeTorque_Injected_m4FCDB6F9DA07C7C64925943F79670E967B1B8FA6 (void);
extern void Rigidbody_AddForceAtPosition_Injected_m24F286471C9928629A50A5C1A0DF22698172438C (void);
extern void Rigidbody_AddExplosionForce_Injected_mB02BEE3B033E215880374706864990CEA12B17D7 (void);
extern void Rigidbody_Internal_ClosestPointOnBounds_Injected_m793A030BF3723440E71E29A9D29CB80607FE0E2E (void);
extern void Rigidbody_SweepTest_Injected_mD66598DD6B9A89F45A9A4E4E0BD16E46FC42D0CF (void);
extern void Rigidbody_Internal_SweepTestAll_Injected_m71D3923B2FCF68ADDC4700A03E6B004BFD3660E4 (void);
extern void Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B (void);
extern void Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70 (void);
extern void Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD (void);
extern void Collider_get_attachedArticulationBody_mBE9BB600DD90D2966D969171663D41F85FF86D38 (void);
extern void Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87 (void);
extern void Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78 (void);
extern void Collider_get_contactOffset_m3970ADEC658E6C854A59B1645DC2D5799F7DF0D7 (void);
extern void Collider_set_contactOffset_mEDA8D778F641338733D140E76FCA0D6B29203B52 (void);
extern void Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB (void);
extern void Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4 (void);
extern void Collider_get_hasModifiableContacts_m2C74D85EE90F563BC873960AB2527D496675B6C1 (void);
extern void Collider_set_hasModifiableContacts_m7F64882EF15D19F5D79A15E1BEBBAAC2598598C0 (void);
extern void Collider_get_providesContacts_m5851C54B60993E4EA4E37FF4601D486A181085DF (void);
extern void Collider_set_providesContacts_m9ADCCC236AAC274C6A4411AAD3EA2BDAFED0A502 (void);
extern void Collider_get_layerOverridePriority_mE8C8D674702E243B3F9CD32476FAC1B83D99EDF1 (void);
extern void Collider_set_layerOverridePriority_m5E5DEAEAD0D246B30B40F5EE25D443A2EE471497 (void);
extern void Collider_get_excludeLayers_m2535780746E624E84F10034B003B6327B1038A34 (void);
extern void Collider_set_excludeLayers_mA1C66C269BE2E8B22D39D20287D93D0EF1051C38 (void);
extern void Collider_get_includeLayers_m037010C05FC53DA09364B3AAD2CC16998A9393DA (void);
extern void Collider_set_includeLayers_m48BF564FEE364CA2AD7FBDC98A584977F5140AC1 (void);
extern void Collider_get_sharedMaterial_m238C1D9D4B2B1F02876C610E049C7A5ECCDC07AC (void);
extern void Collider_set_sharedMaterial_m2AC21AB939A377ABACF8282CDC52EE61B54107ED (void);
extern void Collider_get_material_m2068219450550334496C669378DBA3A03CE68878 (void);
extern void Collider_set_material_mE6FB0AA80863EA6746CD99606C90DB97DBBC4476 (void);
extern void Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C (void);
extern void Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938 (void);
extern void Collider_Internal_ClosestPointOnBounds_m87BD13A92D4239E7BA08C0417197DFC8D4E5DB7E (void);
extern void Collider_ClosestPointOnBounds_mBF2F0C0E76C5F11AED801931D780823A94630952 (void);
extern void Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA (void);
extern void Collider_ClosestPoint_Injected_m4E218A16FABAA4615270B9CD82DC66E130AAFE77 (void);
extern void Collider_get_bounds_Injected_m1BDB8DBC0BC2BFC51D4A185C494EDB0997B93A43 (void);
extern void Collider_get_excludeLayers_Injected_m165282A916A5FF57AC93C00482633838A1A64BF1 (void);
extern void Collider_set_excludeLayers_Injected_m6023B61472839ACA42369799E0D5FAF1F2B602F5 (void);
extern void Collider_get_includeLayers_Injected_mAA7DBED3E68AE14023B3A3D64D0E0BC3191327B8 (void);
extern void Collider_set_includeLayers_Injected_m02EC14FF9417656176314B4564BCA1B3B61AC9BA (void);
extern void Collider_Raycast_Injected_mAFAD355B658765116985B737217587C68BF257A3 (void);
extern void Collider_Internal_ClosestPointOnBounds_Injected_mC77F9140817CA77A46BB8672B6DD388CC3BD2B7E (void);
extern void CharacterController_SimpleMove_mE32A48D439878B3241E8456F8EBFAAE264BD705C (void);
extern void CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4 (void);
extern void CharacterController_get_velocity_mD385DA9478B1FDCB0E9B2D2CA3647B85F1928C8C (void);
extern void CharacterController_get_isGrounded_m548072EC190878925C0F97595B6C307714EFDD67 (void);
extern void CharacterController_get_collisionFlags_m2C5CBA83541CB45D0F6B620FB56BF86F8477BD1E (void);
extern void CharacterController_get_radius_mA7095C2FFBA77AE532CD9B219D506D871E86BFC5 (void);
extern void CharacterController_set_radius_m9F783918C4DF101C705DA1280F2D6143FC622B20 (void);
extern void CharacterController_get_height_m18EC4D93673A225648DCB302BAB4F8A5FE4A20AF (void);
extern void CharacterController_set_height_m7F8FCAFE75439842BAC1FFA1E302EFD812D170FB (void);
extern void CharacterController_get_center_mDF0F4D399A63BF5A2F5366CB71CCF4148DB08591 (void);
extern void CharacterController_set_center_mF22160684B1FB453417D5457B14FEF437B5646EB (void);
extern void CharacterController_get_slopeLimit_m632C114E0C6DDA5877CFD02C28435AD1EBD39A45 (void);
extern void CharacterController_set_slopeLimit_mB77021F6C5D049ED76EEBB2556562BF2BBD6C1E0 (void);
extern void CharacterController_get_stepOffset_mFE2236D76CBF06B5F5A8E6C0AB2E75E0D97F8621 (void);
extern void CharacterController_set_stepOffset_mF609F26572304DF66AE2E17AA524DC8F51C1BE68 (void);
extern void CharacterController_get_skinWidth_mF22F34BB1F1824D67171FCF5F187F5585749A5DA (void);
extern void CharacterController_set_skinWidth_m195DD3EEDE89C015239FF100E9BDE8DEA806EB07 (void);
extern void CharacterController_get_minMoveDistance_m540B49CB50E1C072F00B11776877AF2538446EF3 (void);
extern void CharacterController_set_minMoveDistance_m690AB5F824C765B3F6994EA4E79C5AB67A613257 (void);
extern void CharacterController_get_detectCollisions_m1D4FCA7396C65E243E3BAED09BC9449E6E1A8098 (void);
extern void CharacterController_set_detectCollisions_m7B7E7D67C38E7DD03DF5254085878E0A88C2921C (void);
extern void CharacterController_get_enableOverlapRecovery_mE38F38B269B9D25D46084E6EF16709EC4D5B2637 (void);
extern void CharacterController_set_enableOverlapRecovery_mE989D42A34060F43D45079D49404FEF12AD0B004 (void);
extern void CharacterController__ctor_m6A906ADB773BC0AE534C9348A56747D931B194B3 (void);
extern void CharacterController_SimpleMove_Injected_m0E3E4E6152A40139CE3B8B2FA65FF941FD976656 (void);
extern void CharacterController_Move_Injected_m7F25C33CF948858A8D5822EF73FAE7A16AE65C86 (void);
extern void CharacterController_get_velocity_Injected_m30E51269762EE6648DA19F595925C4C04394B316 (void);
extern void CharacterController_get_center_Injected_m9A353DC6672559EAAFD79D914B0541D72A478314 (void);
extern void CharacterController_set_center_Injected_m2685DB01CE4D0DE542CD29E70B97D84A0F659980 (void);
extern void MeshCollider_get_sharedMesh_mFB4B8534501C29930D2D3710D6D82E60093FA21E (void);
extern void MeshCollider_set_sharedMesh_m05F87B9AC04139285EBBCC159F77B51C6940E79C (void);
extern void MeshCollider_get_convex_m0C0F6D0798413D633814D307EC970F7752B3C9D1 (void);
extern void MeshCollider_set_convex_m20482D687240D0921BA76B77983403E55A2E3CE1 (void);
extern void MeshCollider_get_cookingOptions_mDB07DCA79AE90ED2C36E2F65188B17B7B85C0C9D (void);
extern void MeshCollider_set_cookingOptions_m76B9DE7A503EDD73499CFFB46F7ECD942B6C7A2C (void);
extern void MeshCollider_get_smoothSphereCollisions_m9F86496B849B9B811117DCC5B1F0EA2AED759B7C (void);
extern void MeshCollider_set_smoothSphereCollisions_mD7FCB938D340363D6AA5BA20B47991C00F2738B6 (void);
extern void MeshCollider_get_skinWidth_m9A3AF93E599125E1869E3424A71978A23B8B0E1B (void);
extern void MeshCollider_set_skinWidth_mFA982E4BD157AA4C95919D5DCFDD4B4E95265A1D (void);
extern void MeshCollider_get_inflateMesh_mBC903DE0F569DCBB55BB7868139028ECB3E67883 (void);
extern void MeshCollider_set_inflateMesh_mD3FA4C73FE0AF6F7BF2A32940F652B0BAC08927C (void);
extern void MeshCollider__ctor_m09FBF25616B6185F1CB93D7C520D5DD96584DBE6 (void);
extern void CapsuleCollider_get_center_mC12CE0A66A1104CEB7D23F39596D0E45578419C2 (void);
extern void CapsuleCollider_set_center_m242D92DAEF25887C6A87A0777E4E624C0A431A2E (void);
extern void CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5 (void);
extern void CapsuleCollider_set_radius_mB301C0086FE0D251683512184B0F6DDE264BA985 (void);
extern void CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24 (void);
extern void CapsuleCollider_set_height_m5DAE3DC5AD851E30C5A29AC7A22F36BE1E205BBB (void);
extern void CapsuleCollider_get_direction_mE6D56B0990E3F2FACA983679C251949FE3FC6DFA (void);
extern void CapsuleCollider_set_direction_m3064DADA49F7F48DA8D62659B1D3545C83C553CB (void);
extern void CapsuleCollider_GetGlobalExtents_m7A78A4657CF24A0C663C26AAEF5836DFCE301227 (void);
extern void CapsuleCollider_CalculateTransform_m4FA562D4CF84213611CAD693C7AD62EC3A8575AA (void);
extern void CapsuleCollider__ctor_m747BF5AAA591F27F50B0F9C21FB015811D8E71EA (void);
extern void CapsuleCollider_get_center_Injected_m1E3350EFCD134943AE30B4D76D1767AA080A4BD2 (void);
extern void CapsuleCollider_set_center_Injected_m2C7FB04A185C5A1C8B3AFCFBB2923C8E6966C132 (void);
extern void CapsuleCollider_GetGlobalExtents_Injected_m87BB04142308B622B2C350AF1677A5FA9DC47B08 (void);
extern void CapsuleCollider_CalculateTransform_Injected_m4C62DC4336F9CF275D200E462DC6A63A16C343E3 (void);
extern void BoxCollider_get_center_mC370C79F9FC9398D0DD080500FA2EE14FC6E36C7 (void);
extern void BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0 (void);
extern void BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E (void);
extern void BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A (void);
extern void BoxCollider_get_extents_m60D6ADAA8D5B8D4837B6DE12F0056486CD3B84FA (void);
extern void BoxCollider_set_extents_mFF13B4BC079B719C9DE61934E232E88A9DF650FF (void);
extern void BoxCollider__ctor_m7E069AACEC2B76129335D222FC8B8A63FD50656F (void);
extern void BoxCollider_get_center_Injected_m48EBE71F021C53D4AE4D9F21C16E5E1E11510096 (void);
extern void BoxCollider_set_center_Injected_mF2FF6FB33F950F7329395C6EC97E843ACDA52D8E (void);
extern void BoxCollider_get_size_Injected_m4F20D7B3D8FB4360C9E2986FB6A8CC66ABC89511 (void);
extern void BoxCollider_set_size_Injected_mFA7F15E94435D42A70D7C8543CEC7AF4F6D55343 (void);
extern void SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E (void);
extern void SphereCollider_set_center_m83F6CC0056B491CD577B9AC08FA1E331074203D4 (void);
extern void SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697 (void);
extern void SphereCollider_set_radius_m6119FE18C6739B077AB17334B1B53984911017FF (void);
extern void SphereCollider__ctor_mA8570CBE8C0E74C607669DC4E0CCA6CB1E4CB200 (void);
extern void SphereCollider_get_center_Injected_m26E71B48B49E3EF89A4DC523015F243A385CF0E9 (void);
extern void SphereCollider_set_center_Injected_mC0B9DC26F53D573D5E6AB9B8206854129276B342 (void);
extern void ConstantForce_get_force_m58EDF9940A27488741B1432D0389E508080A2696 (void);
extern void ConstantForce_set_force_mA536A109656B5AA8565539CABB48608A8BA2E720 (void);
extern void ConstantForce_get_relativeForce_mC5D6BB307F4BB3FF716347703E38E1A13C0A5CF9 (void);
extern void ConstantForce_set_relativeForce_m59AD3ED0269E687843404920757675D8702C1C65 (void);
extern void ConstantForce_get_torque_mD4227AF818CBAA90542D44E4F5DFDAE970263626 (void);
extern void ConstantForce_set_torque_mDB6F474A63634F77A15DA14138390596FEB0B9CC (void);
extern void ConstantForce_get_relativeTorque_mEFB75FA1FA7C2D2801BB6A9F6B77DF20EC7E0C55 (void);
extern void ConstantForce_set_relativeTorque_m453C5E55F084EBAEEED463367938B12393A64F65 (void);
extern void ConstantForce__ctor_m0B4FBA845B21450D175CB7D8F50492C064A96231 (void);
extern void ConstantForce_get_force_Injected_mA8DB9E19DBCEF11154F2745FF6E3B75983AB6D07 (void);
extern void ConstantForce_set_force_Injected_m9B6787AE87FDFC05A44E037623DDA6A3946ECDAF (void);
extern void ConstantForce_get_relativeForce_Injected_mEE05499DEAD82B3B4C9287407DFD40C7F4965C59 (void);
extern void ConstantForce_set_relativeForce_Injected_mD1A2089D933DF2B8212F6A743359FE169A077D1F (void);
extern void ConstantForce_get_torque_Injected_m7B984063EA78F5BB16C1FF1E9FDF00BFAD3E7065 (void);
extern void ConstantForce_set_torque_Injected_m6A1243064472E8BE5114CE6698CADDF99317F254 (void);
extern void ConstantForce_get_relativeTorque_Injected_mCB442EA0939252AAAD7000F42B616DAB64490BA6 (void);
extern void ConstantForce_set_relativeTorque_Injected_mC70A5B1CA4430C6D0C46B59F60887DC77DBC5B47 (void);
extern void Joint_get_connectedBody_mE39E0AC9869325CD018B9ADB383B6BE01D497B59 (void);
extern void Joint_set_connectedBody_mE9E631476E9D4264E8DC0D6307146F5EB64D3ED4 (void);
extern void Joint_get_connectedArticulationBody_m5307D444E4377F11A43509EBA03124FDB10AF56E (void);
extern void Joint_set_connectedArticulationBody_m04A871A2FF8B23746C0293604516B0720CAEC6D2 (void);
extern void Joint_get_axis_mEDF8FE710E08CED9EA69A0369A075B77FF0BE79C (void);
extern void Joint_set_axis_m3C64D93F04DA043D6B02160F9034907BACC06800 (void);
extern void Joint_get_anchor_m1CDF56CF0BD9773E9923D777EA24B2102DEDB79D (void);
extern void Joint_set_anchor_m89447EF25E0FC6DB9D22562BAF3BDA3E6D04029C (void);
extern void Joint_get_connectedAnchor_m455C8981D90A4A60DC5B290EBD1D9330A6649C18 (void);
extern void Joint_set_connectedAnchor_m2A40C3C4FB583E9DBC020222A21F577C066D5D90 (void);
extern void Joint_get_autoConfigureConnectedAnchor_m902904AD312FD282CB01AB7A25AB4153FDA567E3 (void);
extern void Joint_set_autoConfigureConnectedAnchor_mF61D716174DE67CD94FF042881E9052357679E02 (void);
extern void Joint_get_breakForce_mC3DCDF8DEA3B55EE997163BDDC192D9E9A3D1801 (void);
extern void Joint_set_breakForce_m42F130D31EFC282FE0887A4E6A399F53EB3FB40F (void);
extern void Joint_get_breakTorque_m834D4485F2615DB8BB48DAFAB57E9BE6A361AA5C (void);
extern void Joint_set_breakTorque_mD76D069CDFCE726493BA790F19496FD8D08EFB04 (void);
extern void Joint_get_enableCollision_m540A9E81771E2AA39C5D3254735FD8AE15AF5D0F (void);
extern void Joint_set_enableCollision_m73C4FF0B64FDBDEFEEDCD9F98D491906F7715080 (void);
extern void Joint_get_enablePreprocessing_m887CDA242B52B356E81BFD410CC9C231618EB8A6 (void);
extern void Joint_set_enablePreprocessing_m0970649287DC20C289E3AB775CBD1D65883B6F27 (void);
extern void Joint_get_massScale_m8BD34AE43B5136D2E0AB657AD37A9A050838D4A6 (void);
extern void Joint_set_massScale_m23111BDA5A8825E83069372785DBF56FCDAA4BC1 (void);
extern void Joint_get_connectedMassScale_m2CB77BD65BACE520EBBD68456322098BC550C016 (void);
extern void Joint_set_connectedMassScale_m6F7D8FEFAD6BFDC7177D9D06DBCCDCC288C4475D (void);
extern void Joint_GetCurrentForces_m67531F115C7C87F196D387A5DB3952277A0253C2 (void);
extern void Joint_get_currentForce_m3870631DEC29CED9EC606C342F6951F8A6C72692 (void);
extern void Joint_get_currentTorque_mD01736066D68B73B02FC683D6C7DB0E4A9924516 (void);
extern void Joint__ctor_mAB8D99F295B9D152D8AB086364EF03B2AC9E9D01 (void);
extern void Joint_get_axis_Injected_m85FD31C188CC3C7BC266AC7E5E1DD5B14E656C34 (void);
extern void Joint_set_axis_Injected_m013FB85E34CB410702FC87EAB54B856CF180B505 (void);
extern void Joint_get_anchor_Injected_mE20087A50431397C2C20E031DD98733425C2B445 (void);
extern void Joint_set_anchor_Injected_m640F53862ECAB8D8163B378E54FB6E2194BB2AF7 (void);
extern void Joint_get_connectedAnchor_Injected_mB370F52F87BB83B28A071E4342F132D2ACC9C365 (void);
extern void Joint_set_connectedAnchor_Injected_mE4275DA473C8A61B37A1639E2352A09DCE2A4198 (void);
extern void HingeJoint_get_motor_mF886F1CBD9EE4A89F41F0E8790DAB5E1FEA84F00 (void);
extern void HingeJoint_set_motor_m9612545CFB6BCF58FE2AC5D49720190D0FDE3800 (void);
extern void HingeJoint_get_limits_mD0E3FDF10D6AEF4D539711C0DE418C20930B6433 (void);
extern void HingeJoint_set_limits_m5B9B968EF88FFC9ABA1777FFEFA13433F9BE08D8 (void);
extern void HingeJoint_get_spring_m78151E226B2A39FA888F6367AF21D332214293D5 (void);
extern void HingeJoint_set_spring_m93334DF56ED45D7D7CBEE051E459F70D6DC81E25 (void);
extern void HingeJoint_get_useMotor_m1C329ACA3AFA9DD15AE4441D78E8AA99DE11CC95 (void);
extern void HingeJoint_set_useMotor_m412445D348B1D6EB51D13FFE14498885FBE8D5B6 (void);
extern void HingeJoint_get_useLimits_mB6505FE417A487B69756B9F9897B06C40AD5F362 (void);
extern void HingeJoint_set_useLimits_mC47026C2282D590754EE177C992FF5C228908D39 (void);
extern void HingeJoint_get_extendedLimits_mE837461AA2777194104F9137CB646D2831675712 (void);
extern void HingeJoint_set_extendedLimits_mCD4FE8A16B9960ECB01FA175C97ECE7A0A777D7A (void);
extern void HingeJoint_get_useSpring_m239B2FBE0587B605008196463DC40881A3BC3779 (void);
extern void HingeJoint_set_useSpring_m02AF2737DE4954BB71C2F68710835116BE3AA0D9 (void);
extern void HingeJoint_get_velocity_mC9F0646F08D2177EED1B514C0C613C63D1DEF78D (void);
extern void HingeJoint_get_angle_mCCDAB7FAFB7A535562521C450A1A35393622DD6D (void);
extern void HingeJoint_get_useAcceleration_mF6D06CDC06EA6E98AABEA1E06E28CB8BBE6BB1C8 (void);
extern void HingeJoint_set_useAcceleration_m09DCD14C7AAE83954969E4B0F9B01F38DF58F12B (void);
extern void HingeJoint__ctor_m688638163A59D626BE998B6B8196E8F2C0AAB667 (void);
extern void HingeJoint_get_motor_Injected_m95030B920C62241B17A12EFBCB3C0772951A1B13 (void);
extern void HingeJoint_set_motor_Injected_m9C0C3243E03D6A8652236CA47C6C24E43A16E966 (void);
extern void HingeJoint_get_limits_Injected_m33E3633CAC19E9EBB0BCFC5D8A5CA7FBEC8A8E73 (void);
extern void HingeJoint_set_limits_Injected_mF946129ADB63E27A95D9F71B5FFC54A0341AE78E (void);
extern void HingeJoint_get_spring_Injected_m8CB9B7B84B0D33472152688F5065433D14F7B002 (void);
extern void HingeJoint_set_spring_Injected_mC8D22F2C103A70E0569A9D178CF66015FCB9BE03 (void);
extern void SpringJoint_get_spring_mDD913018B3C62A81AAD9BEE33D57E2EDA3532D3E (void);
extern void SpringJoint_set_spring_m2F50B24336E3DEEF56551567821EC6FCAA4280FC (void);
extern void SpringJoint_get_damper_m01D06A59CFCC434B6C57953F8F8D4E31A3967099 (void);
extern void SpringJoint_set_damper_m69DF6E010568BAD43F98684D0D3C54DEB31F01D7 (void);
extern void SpringJoint_get_minDistance_mD67FCC62300CD40EFEB7643D4B483A6142B5F06F (void);
extern void SpringJoint_set_minDistance_m08AEB7E38CFB0A279F8387FAD8912BE9569A1E37 (void);
extern void SpringJoint_get_maxDistance_mD9DF4A15CA0A006D029991B4E27B6343CD8BAD8E (void);
extern void SpringJoint_set_maxDistance_mC8329A6B1CD20296234A1B377376EB51776F19E4 (void);
extern void SpringJoint_get_tolerance_m487624E739BA3A3491DA0EF3C39FEB00118E2F08 (void);
extern void SpringJoint_set_tolerance_mAD95C6A76C76C08558455D407EA8E07744338DBF (void);
extern void SpringJoint__ctor_m4CF42EA676C250CC3C63E82696A63028C2C4A4B3 (void);
extern void FixedJoint__ctor_m1F5D2454791CD635468C3A498C58131CD2D4DFF4 (void);
extern void CharacterJoint_get_swingAxis_m7B0745C53909F0966647E587B6ED1F54240E14DE (void);
extern void CharacterJoint_set_swingAxis_m4B6A085E09C2E4107A9ED55E3C6C52FD1F14C861 (void);
extern void CharacterJoint_get_twistLimitSpring_m3712394AAB44DBF5E6311C5553A592D1B35E1D66 (void);
extern void CharacterJoint_set_twistLimitSpring_mF37BF3C4F77EBA9F1926FA8F07081D315A9C2031 (void);
extern void CharacterJoint_get_swingLimitSpring_m46EBF7D013D8681599D40327CEE18FCAD0BBF7AE (void);
extern void CharacterJoint_set_swingLimitSpring_mBD4D7812652BE202BEB031F2E1997B70F0F87258 (void);
extern void CharacterJoint_get_lowTwistLimit_m3143E5DBE317CA13C5FC20737F331569760278A7 (void);
extern void CharacterJoint_set_lowTwistLimit_m8DC922DE93DAB94B329B3C672061AA9741A4C02E (void);
extern void CharacterJoint_get_highTwistLimit_mB9CA1667D4D9D532281CD6138FC453828EF8A552 (void);
extern void CharacterJoint_set_highTwistLimit_m11EFDBE8CBF92BD43F147C20F247B250CB432C32 (void);
extern void CharacterJoint_get_swing1Limit_m8BF48CB27AD78E7E00A42FFAF12513125D6F04CE (void);
extern void CharacterJoint_set_swing1Limit_mBA202634AF5606DA1D59B94EF3E359C7B4FD012E (void);
extern void CharacterJoint_get_swing2Limit_mACAA0BA0171DEABBF578FCA52EA4C7720EC6123F (void);
extern void CharacterJoint_set_swing2Limit_mBA612E2C6E50BAAAA798FAA03490150D379BC744 (void);
extern void CharacterJoint_get_enableProjection_mD2480E9D4BEB6FC5C797D29CC8948180AE9896D4 (void);
extern void CharacterJoint_set_enableProjection_mB51E0A48EC80818FB2D054C0B9B486567A449A80 (void);
extern void CharacterJoint_get_projectionDistance_mD5ED2C2BF304A20A884A54816C9924F70A7E7E67 (void);
extern void CharacterJoint_set_projectionDistance_mBF309612C0797CD1861EA62455A657D9DAB1896B (void);
extern void CharacterJoint_get_projectionAngle_m657873E8264EE9ACE0E3311720AFE5AE7FA57602 (void);
extern void CharacterJoint_set_projectionAngle_mF7462E89230F52BE963B1CB580C078FDFBD207DA (void);
extern void CharacterJoint__ctor_m546A62607CF3DD68E930D2951F5CCA4677A1CA2F (void);
extern void CharacterJoint_get_swingAxis_Injected_mEC08D6C21AAB14DB5FBCC05E53DB3E42E3A0B0FD (void);
extern void CharacterJoint_set_swingAxis_Injected_m5FD0A81CAF88433EA17D9FC22C63A84E8B9AC9CB (void);
extern void CharacterJoint_get_twistLimitSpring_Injected_mA86E31F784213DAB18C4FC8146F0EB07B07352DE (void);
extern void CharacterJoint_set_twistLimitSpring_Injected_m2E2E111E6C5CE269A206D96F6CF48FB583D41E1C (void);
extern void CharacterJoint_get_swingLimitSpring_Injected_mF004802C09FE9D7CA86FC19687A3A7F390101511 (void);
extern void CharacterJoint_set_swingLimitSpring_Injected_mFFD319ED11CB19FC37E458053875B8BB06244D6D (void);
extern void CharacterJoint_get_lowTwistLimit_Injected_mF42D60DA2C12EA46E8D6E1812937A4BAE7AFD8B6 (void);
extern void CharacterJoint_set_lowTwistLimit_Injected_mD461E7C132E086B0A2DC78C18EC56C5475ADB5C2 (void);
extern void CharacterJoint_get_highTwistLimit_Injected_m52BD0669FEBBCEC5CBAA97F90838C2FCE275005E (void);
extern void CharacterJoint_set_highTwistLimit_Injected_mFE855EAAE2643F8E44A3B774C446F0FA94489703 (void);
extern void CharacterJoint_get_swing1Limit_Injected_m21B6864828313D548792D2CE498F0B06D021255B (void);
extern void CharacterJoint_set_swing1Limit_Injected_m210AC0C26F3EC961D8361C513A0E8D6F21740D21 (void);
extern void CharacterJoint_get_swing2Limit_Injected_m8CB90B472810EBA291DB8C10129CE5F57CABA36D (void);
extern void CharacterJoint_set_swing2Limit_Injected_m903FF4342243EA8990825D6E8773AAAEA20A573F (void);
extern void ConfigurableJoint_get_secondaryAxis_m3C6E45321118FCB7144F8620E9E0777F1CE81152 (void);
extern void ConfigurableJoint_set_secondaryAxis_m2CF27C9BA848BD03F1E0EEC4FD7E788C60CA36CE (void);
extern void ConfigurableJoint_get_xMotion_m5518BED4E7F558174DD6B8F313CE7D125E1A1334 (void);
extern void ConfigurableJoint_set_xMotion_mBDA7D8874899D2C20E1B1BA45944AA357CDFBDCC (void);
extern void ConfigurableJoint_get_yMotion_m4F0FA7246F1BAA1AC0BD7E86504CBE48D47CA005 (void);
extern void ConfigurableJoint_set_yMotion_m597259075C915C848E87B3A9CBBDA0762B5A2563 (void);
extern void ConfigurableJoint_get_zMotion_mC0AEE3A95069E7C0F451B71E356DCA387CDF4AEF (void);
extern void ConfigurableJoint_set_zMotion_m3479D7843AC2F91AA958F7B18AFCE3730842AFA8 (void);
extern void ConfigurableJoint_get_angularXMotion_m5CBF8FC37A7CF94AF97583E5C1551BF7859B9258 (void);
extern void ConfigurableJoint_set_angularXMotion_m1691CF3456A38996918D077FD6FC2CBEEFB0C9D5 (void);
extern void ConfigurableJoint_get_angularYMotion_m1A08889BB8666184FED3CF1275444D5BA70ACE5F (void);
extern void ConfigurableJoint_set_angularYMotion_m21858D3799D8EED8AB21C46DF84927B10F1414D7 (void);
extern void ConfigurableJoint_get_angularZMotion_m515347C78E06D82BE0AD254824E6F134E46CC58C (void);
extern void ConfigurableJoint_set_angularZMotion_m485474C654E903BBAE579F631BBD6C737B47394B (void);
extern void ConfigurableJoint_get_linearLimitSpring_m2307DFA67235DFE5CB95567396174DEF07E7750C (void);
extern void ConfigurableJoint_set_linearLimitSpring_m88B5287870506292956CE55309AE7D9470DF5820 (void);
extern void ConfigurableJoint_get_angularXLimitSpring_mCF3C44B8284447AFC22BB83EB84E261573DB6C13 (void);
extern void ConfigurableJoint_set_angularXLimitSpring_mB985F8F351DC4EFF2E588BF1C40BC1C2FF074BA4 (void);
extern void ConfigurableJoint_get_angularYZLimitSpring_m624F895588D14488531DAA37FFD4051A27DB9F5D (void);
extern void ConfigurableJoint_set_angularYZLimitSpring_m12F8452730747EA03C8F7DE6F2DF3C2EB0A380F2 (void);
extern void ConfigurableJoint_get_linearLimit_m35456F7AF48ACA69E79D1EFE14578730BAA6A98A (void);
extern void ConfigurableJoint_set_linearLimit_m57EE251D8642A4ADED96D77555B5948AF7F4AA9E (void);
extern void ConfigurableJoint_get_lowAngularXLimit_mE5EA802AA80E71542FDE6DD911364FC24297F4BD (void);
extern void ConfigurableJoint_set_lowAngularXLimit_m6424314936986525CEBCE5C16EBA69B5129BBD5A (void);
extern void ConfigurableJoint_get_highAngularXLimit_m978FF09CAF3E87AFA149752594ADD09FB9EA1ACE (void);
extern void ConfigurableJoint_set_highAngularXLimit_m96373EE2554934636E127E345F91306844177294 (void);
extern void ConfigurableJoint_get_angularYLimit_mCC629F60D5650EF0F8F49FFB5DEE4052F687CA47 (void);
extern void ConfigurableJoint_set_angularYLimit_mF819FB8C5F17C9737EC0BA5A3EAAC5245AE57A08 (void);
extern void ConfigurableJoint_get_angularZLimit_m3F1975F6CAFD784F4F0881CB00D6E266CCE2658B (void);
extern void ConfigurableJoint_set_angularZLimit_mCB9FEE0CAF97A1A278BDCD127C86DDD26CDBBC70 (void);
extern void ConfigurableJoint_get_targetPosition_m5BFC5AD26B78262E1BE4F45B6DF3A3BEB7C3D259 (void);
extern void ConfigurableJoint_set_targetPosition_m9262281EAA56638EDBA8FC727D2F6B36EBBC195A (void);
extern void ConfigurableJoint_get_targetVelocity_m424E0807712D92D19CB2797CCBC6167AF5910F85 (void);
extern void ConfigurableJoint_set_targetVelocity_m0C738C630AC7A91FC13645A0924C2A48D31C25CC (void);
extern void ConfigurableJoint_get_xDrive_m94205B0C6C73599ACF1E9DB393CB6B609743FC9F (void);
extern void ConfigurableJoint_set_xDrive_m99433795EA91A20621E21DF82DBD20B6EB49E13C (void);
extern void ConfigurableJoint_get_yDrive_m908E6398366115C5F828524308146A26C9B1F73C (void);
extern void ConfigurableJoint_set_yDrive_m64B02662A3353AE1C10DFB0A38FAD9B955E155FA (void);
extern void ConfigurableJoint_get_zDrive_m450F08477A25E42880A1094477A9255DABB319FC (void);
extern void ConfigurableJoint_set_zDrive_m399D5A99A3CC1DBF3135B3D7228C5B971CC88F99 (void);
extern void ConfigurableJoint_get_targetRotation_m53942230F418B64F272AD9E274EB0A6DA4309769 (void);
extern void ConfigurableJoint_set_targetRotation_m3AA036C6CD6050EF49BBA225241D4C1DA110AC27 (void);
extern void ConfigurableJoint_get_targetAngularVelocity_m4E74B7B35BADFF3B3E0A24DCE7AF614BFC1E4DF5 (void);
extern void ConfigurableJoint_set_targetAngularVelocity_m43694FCA51058D48D1F3575806117C012DC1E565 (void);
extern void ConfigurableJoint_get_rotationDriveMode_mB4E7830CED114BCEF998C7F54BCC90810BE6848B (void);
extern void ConfigurableJoint_set_rotationDriveMode_mAF10EC6CA2FA9F3B52C814FFEB06CC48C07C175E (void);
extern void ConfigurableJoint_get_angularXDrive_mF70108A1EE3D86D324BA3745C78A5EA98BC5E330 (void);
extern void ConfigurableJoint_set_angularXDrive_m198E38A6FEE12C2FAA27E849F18BC61504EEACB0 (void);
extern void ConfigurableJoint_get_angularYZDrive_m0308E706F0FBDB08A0D4C4DF7A8879C7710E4CB8 (void);
extern void ConfigurableJoint_set_angularYZDrive_mA9F165594FD53A2100E17D2E854DD967B91066EB (void);
extern void ConfigurableJoint_get_slerpDrive_mC1C3450853FE4EC991B330991BCCD51CC684F634 (void);
extern void ConfigurableJoint_set_slerpDrive_mAD0C1BDDF4D03B7CA9677ADEFC70344911C27B1D (void);
extern void ConfigurableJoint_get_projectionMode_mB3BE3DF5441473E6C07FAC78FF3E1BB075C2DB53 (void);
extern void ConfigurableJoint_set_projectionMode_mA2FA6EC630E87C553B6E1C93D997B15957008075 (void);
extern void ConfigurableJoint_get_projectionDistance_m72A3F68CD86F6A81B2043DD3C94B339D441A1876 (void);
extern void ConfigurableJoint_set_projectionDistance_m149EF550339500403965DB5596C63ED07B54FC1B (void);
extern void ConfigurableJoint_get_projectionAngle_m8D9C39A116E9B00E5D80E4B2F8AADC5853F1F2E3 (void);
extern void ConfigurableJoint_set_projectionAngle_mBD4200FF238CC5779943E4980B79D3C14643BE24 (void);
extern void ConfigurableJoint_get_configuredInWorldSpace_mA9C965EEB6BB099124A5D37A3DC68D16424EC26B (void);
extern void ConfigurableJoint_set_configuredInWorldSpace_mD1FB99B42E24A0CABF43B4470E6F0C92BCCC2450 (void);
extern void ConfigurableJoint_get_swapBodies_mDCFE02643DED2BB4AF58715B0AC6395030BF2371 (void);
extern void ConfigurableJoint_set_swapBodies_m42B1FE715EDD21D7BBF51DBEDD804CACD613F3C7 (void);
extern void ConfigurableJoint__ctor_mAD91EEE30A56D89FEA53292AFA676A37778933DA (void);
extern void ConfigurableJoint_get_secondaryAxis_Injected_m930CFC10D358646D8B71A761C1F8DE25AA64F266 (void);
extern void ConfigurableJoint_set_secondaryAxis_Injected_m266E464BBB5EDE9C3EDDD8A3F3B55DD401A928BF (void);
extern void ConfigurableJoint_get_linearLimitSpring_Injected_m27C98A5D89DE493BD62DC33D119FD721978CF8ED (void);
extern void ConfigurableJoint_set_linearLimitSpring_Injected_m2EABCB82890328A9B26E5807D13133E69D94D4C6 (void);
extern void ConfigurableJoint_get_angularXLimitSpring_Injected_mC3823A7F518262A26E02E3F13219622BD066380A (void);
extern void ConfigurableJoint_set_angularXLimitSpring_Injected_mE79A822ACD2B93B3E3122A9B2572B6D39BA685B3 (void);
extern void ConfigurableJoint_get_angularYZLimitSpring_Injected_m67F747454BC5F356141424CCBF57155ED911766F (void);
extern void ConfigurableJoint_set_angularYZLimitSpring_Injected_mBC9D67732AAED2EAF5DF21B247500401CB29F61B (void);
extern void ConfigurableJoint_get_linearLimit_Injected_mAB06629F3F0FF14825C71BC77B8D1856836E6329 (void);
extern void ConfigurableJoint_set_linearLimit_Injected_mB7C9F6674B0E76154DFCFAB457E68264FC8ACA88 (void);
extern void ConfigurableJoint_get_lowAngularXLimit_Injected_m65F3B2184E5BE0B93B9702DE371B50B727050C60 (void);
extern void ConfigurableJoint_set_lowAngularXLimit_Injected_m424F97E98AB918627E19CFA7FED1C4236DBFDD61 (void);
extern void ConfigurableJoint_get_highAngularXLimit_Injected_m6672C8D546D7DCBD8E67AB9CF91AEB3FB76E72AC (void);
extern void ConfigurableJoint_set_highAngularXLimit_Injected_m7C31099A209513CAE46018D03F195D33CFCFD501 (void);
extern void ConfigurableJoint_get_angularYLimit_Injected_m7B5BF8EE2D2AFC977D6E192307EE9A8DC14A6C47 (void);
extern void ConfigurableJoint_set_angularYLimit_Injected_m4DA460D042653C9FB41E5E2A3AC4B0F18F6D76FE (void);
extern void ConfigurableJoint_get_angularZLimit_Injected_m3E5B226EC970A3A74652C9AE9B9F1BF07B8A6AE0 (void);
extern void ConfigurableJoint_set_angularZLimit_Injected_m781FCA9CA40DA1F5B984BDCBA4DBE0F4309198FC (void);
extern void ConfigurableJoint_get_targetPosition_Injected_m74FC71653E196453FEF1307544EC4EE5404F71D1 (void);
extern void ConfigurableJoint_set_targetPosition_Injected_m1364898AF65E1C33782F3AC6B24F2D5C8C627DB9 (void);
extern void ConfigurableJoint_get_targetVelocity_Injected_mD71CBEB7F551FF874AE3BFCEF363548EA17C583E (void);
extern void ConfigurableJoint_set_targetVelocity_Injected_m1D93766920BBE9C170E22725BB97B554834B9822 (void);
extern void ConfigurableJoint_get_xDrive_Injected_m2F3081B5A43CB574CF935CCC4CA1015D3B1B6E0D (void);
extern void ConfigurableJoint_set_xDrive_Injected_m98A6A7950340A2AD4EDB74744FDFAE15DE7AC746 (void);
extern void ConfigurableJoint_get_yDrive_Injected_m0764FC64FA82FC454B31B0495FE53091C711AFE2 (void);
extern void ConfigurableJoint_set_yDrive_Injected_m3C83E8824F18060EADF294DB53D6AC75A96F0EDA (void);
extern void ConfigurableJoint_get_zDrive_Injected_m5C94C5C057B7AEA5B4FD8E95CE9283B73DF79E1B (void);
extern void ConfigurableJoint_set_zDrive_Injected_m8867C059A6C76AE1ABBCEC0A8BDF64B31DAFD66A (void);
extern void ConfigurableJoint_get_targetRotation_Injected_mB22A6D023F8407F0D04A3041D3615761C86CCD78 (void);
extern void ConfigurableJoint_set_targetRotation_Injected_m4DEDED0147CB460010A8ECC633DF07B86D99516E (void);
extern void ConfigurableJoint_get_targetAngularVelocity_Injected_m0701ECD3E94F236824645054085FB64AA4D1EEC9 (void);
extern void ConfigurableJoint_set_targetAngularVelocity_Injected_mAE21B1C5D472AE7D2DAA8B8D22B6EAEC29650804 (void);
extern void ConfigurableJoint_get_angularXDrive_Injected_m703DADB9957F220ECC811C4ADE5CA352547C4589 (void);
extern void ConfigurableJoint_set_angularXDrive_Injected_m3416E4C46CA6E3861E95FD493217B5C2520A1CD6 (void);
extern void ConfigurableJoint_get_angularYZDrive_Injected_m49DC365B36683544D5705F39F02EA6957CC21E55 (void);
extern void ConfigurableJoint_set_angularYZDrive_Injected_m9B74A9B4583CAB39384B0E5D78B6922E281E9FFB (void);
extern void ConfigurableJoint_get_slerpDrive_Injected_m6F7AE0A28412FDC513DDBFDB2D589775F5FB14E1 (void);
extern void ConfigurableJoint_set_slerpDrive_Injected_m07427CF12C12E1879706DBEFE49F1BC191AB52CA (void);
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208 (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6 (void);
extern void ContactPoint_get_impulse_mA8ABFB0DCF8F4E0004FF23106B15046FA5C923B8 (void);
extern void ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6 (void);
extern void ContactPoint_get_otherCollider_m717D0758D578C93C6CA26E2BA87325682B6C2550 (void);
extern void ContactPoint_get_separation_m0017804537DC53F84EA4C76239BCE72B0C167B4E (void);
extern void ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7 (void);
extern void PhysicsScene_op_Equality_m792F2F283FF4346561C0D0CC7BB9332FB02C3368 (void);
extern void PhysicsScene_op_Inequality_m5F2146838C650AE28B2213767520DBE383D3991B (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814 (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3 (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1 (void);
extern void PhysicsScene_IsValid_m74353C7AC7756A4E6B2F768551CA2D373EE28478 (void);
extern void PhysicsScene_IsValid_Internal_m03967EA72EC0D8FCEDC0D79075FF9E62D77DC241 (void);
extern void PhysicsScene_IsEmpty_mD7DECC3F1514241BEF94CE872FC58A5442FB014E (void);
extern void PhysicsScene_IsEmpty_Internal_mAB6C2F2464EFFD376752C43958904F6317F2C806 (void);
extern void PhysicsScene_Simulate_m8F1DFA08BF5DBFBB1FF452ABC98E40C938EA1425 (void);
extern void PhysicsScene_InterpolateBodies_m4F998486F63C793D362B6564A0D5850D50ED5270 (void);
extern void PhysicsScene_ResetInterpolationPoses_mE48BDAB51AAFC8E8687B1BAD9F0036ECED7B2DE8 (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730 (void);
extern void PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0 (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE (void);
extern void PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74 (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3 (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB (void);
extern void PhysicsScene_Query_CapsuleCast_m6871258F7BAA2370C7BA7334E2EE6752EFBD723F (void);
extern void PhysicsScene_Internal_CapsuleCast_mFB5002955B349D73D842F47BB3DBCDAE453FF2F0 (void);
extern void PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35 (void);
extern void PhysicsScene_Internal_CapsuleCastNonAlloc_m5C610A8AC0B9DAB01675C36EA6E478028C73D445 (void);
extern void PhysicsScene_CapsuleCast_mA250677E33E5D956F8E75905C348517BD23CA4AE (void);
extern void PhysicsScene_OverlapCapsuleNonAlloc_Internal_m7A25A75ED0EC93A9B68B87EFEEE16713B5F78B3D (void);
extern void PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545 (void);
extern void PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B (void);
extern void PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4 (void);
extern void PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F (void);
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F (void);
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1 (void);
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01 (void);
extern void PhysicsScene_Query_BoxCast_m8B3C7AFDF3D96DB8B60E68930DBE83FB60269923 (void);
extern void PhysicsScene_Internal_BoxCast_mE2056DF773CF7D52E153804C2CBDB4F4D68B4CE6 (void);
extern void PhysicsScene_BoxCast_mD138F894448712F882AF7F95B2AD9052DC548D12 (void);
extern void PhysicsScene_BoxCast_m83D81817A2F4BDCE6D0C4130D49E79EBF885DEA2 (void);
extern void PhysicsScene_OverlapBoxNonAlloc_Internal_m77221087DFD7FCCF0242F82671A6F180DDE52326 (void);
extern void PhysicsScene_OverlapBox_mF98FE9D367F5938A0E23C60684BED711EB69CA03 (void);
extern void PhysicsScene_OverlapBox_m8EF90D415B4EE966DEAD2DCC80DB94D6D6C46EF4 (void);
extern void PhysicsScene_Internal_BoxCastNonAlloc_m721584532E1F33DB93475FA5F89BD13422E1BFC3 (void);
extern void PhysicsScene_BoxCast_m5010295997A4CBDDA261F825837A35789B4461E0 (void);
extern void PhysicsScene_BoxCast_m70E3EF843956491E83FA535A8ED5756D0A417F23 (void);
extern void PhysicsScene_IsValid_Internal_Injected_m49DCA66EF92A47D23B746D0D292AB11D88C2C0ED (void);
extern void PhysicsScene_IsEmpty_Internal_Injected_m18574012DBCAC8B7BA268BE32040572596258300 (void);
extern void PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62 (void);
extern void PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_Injected_mD6BA34F06BE743B2CBF46AA82EE6DDC9CCEC0F27 (void);
extern void PhysicsScene_Query_CapsuleCast_Injected_m3A9CC7AB617D70C2284C49E982163F37EB0B27B0 (void);
extern void PhysicsScene_Internal_CapsuleCastNonAlloc_Injected_mA8ED31F36DEE04580883BBCF58D3CEAF9D5A187D (void);
extern void PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected_mF8B5563CB6D620B1269EF5D2D7127F252D2CB358 (void);
extern void PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A (void);
extern void PhysicsScene_Internal_SphereCastNonAlloc_Injected_m8B19C4FB753820C4D4952D6BEB59B7044F7C7394 (void);
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737 (void);
extern void PhysicsScene_Query_BoxCast_Injected_mB8A1A4B228295D1C888EB08255182EA83FC4733D (void);
extern void PhysicsScene_OverlapBoxNonAlloc_Internal_Injected_mD2053028D905149928623868D4463157E1F1AB4C (void);
extern void PhysicsScene_Internal_BoxCastNonAlloc_Injected_m9E40CADBA19328EC192BE3A33747889EFE5B2075 (void);
extern void PhysicsSceneExtensions_GetPhysicsScene_mC7D6FE0FA798195A3BA3B3BA6D41F4D947D037AD (void);
extern void PhysicsSceneExtensions_GetPhysicsScene_Internal_m47C05DB774E72E24AB4ECBF75A98652C544923F9 (void);
extern void PhysicsSceneExtensions_GetPhysicsScene_Internal_Injected_m2A4EE29C6BD1AA0EB6BF7683EA8E5B6783A6FA57 (void);
extern void ContactPairHeader_get_BodyInstanceID_m4E7EBAF5ADE2DA3D281C9A0F5B63A006F7241CE8 (void);
extern void ContactPairHeader_get_OtherBodyInstanceID_m6384A0571E81440B589E8F41D077553DF31A27C5 (void);
extern void ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA (void);
extern void ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08 (void);
extern void ContactPairHeader_get_PairCount_mF7E6F40BD6A8ED2B041EA993CDC01185D78674FE (void);
extern void ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5 (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09 (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27 (void);
extern void ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E (void);
extern void ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3 (void);
extern void ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD (void);
extern void ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256 (void);
extern void ContactPair_get_ContactCount_m1F6291D2B7A188DA4FF675734535B2699495857C (void);
extern void ContactPair_get_ImpulseSum_mE97817B3288752026E30C8ED5C92D3595E890ACC (void);
extern void ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39 (void);
extern void ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385 (void);
extern void ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8 (void);
extern void ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E (void);
extern void ContactPair_ExtractContacts_mCE72C104A1CF1E3C6FF4A16AF0AE1FE52DD08A52 (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC (void);
extern void ContactPair_CopyToNativeArray_m9396822D29611D9B83036C70E49017C007059435 (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454 (void);
extern void ContactPair_GetContactPointFaceIndex_m2906D14481C497D1DB39E1CDC323D978D64086E0 (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565 (void);
extern void ContactPair_ExtractContacts_Injected_m8C4EE9EC95500EF7E59189BC65B625B221549BB1 (void);
extern void ContactPair_ExtractContactsArray_Injected_mF91AF0D52F744FB7DEF1381BDDE212EF79A6932E (void);
extern void ContactPairPoint_get_Position_mF767C5CD66E322293F064B17FD44F8CF8C356B54 (void);
extern void ContactPairPoint_get_Separation_m4F5BFDBA730F08CD17BF3C0352453280DB1486AD (void);
extern void ContactPairPoint_get_Normal_mB796C363701DFD430C2395C45EF578B5226757B2 (void);
extern void ContactPairPoint_get_Impulse_m94991E8BF7F18A361EB08941CFA88A1304CE8079 (void);
extern void QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B (void);
extern void QueryParameters_get_Default_m9C12742976F18500744C5F6981FD1002C5E894E4 (void);
extern void ColliderHit_get_instanceID_mDB124C91153EC421E57B834CF93758677F4BDAFE (void);
extern void ColliderHit_get_collider_m047EC40DD7F77CA33A3BB636B7694AA3117FE83D (void);
extern void RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971 (void);
extern void RaycastCommand__ctor_mC95C1C19A37F20B3111AF893B935EA51969FB4A1 (void);
extern void RaycastCommand_get_from_mCB7D147B3FF37945CCB38E9A174411ECCA769909 (void);
extern void RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2 (void);
extern void RaycastCommand_get_direction_mD02651FAA5BEA161F8FB390257F3DA87C3C3F393 (void);
extern void RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D (void);
extern void RaycastCommand_get_physicsScene_m8731C9B74E3FB41FD8063C35453E867C218C8F44 (void);
extern void RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946 (void);
extern void RaycastCommand_get_distance_mFA04832DA19FD26541315E88245805E610DECED4 (void);
extern void RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023 (void);
extern void RaycastCommand_ScheduleBatch_m250EB363528A1CAE1F9E3FD517C433905464C7DE (void);
extern void RaycastCommand_ScheduleBatch_m5CF1A3E3FE582BD990F7844F3D9C18B5A2295AF0 (void);
extern void RaycastCommand_ScheduleRaycastBatch_m2ECCFEE5C88640231C83DFD65411358E2B5C2CA2 (void);
extern void RaycastCommand__ctor_m8DFCFD3175272180C884798DC278A8D9B81E1B38 (void);
extern void RaycastCommand__ctor_m5D55292413D4D916C28793CB0F6B22A67258D26D (void);
extern void RaycastCommand_get_maxHits_m8F19093BDC596CD7D5228E6DE4ACF898FF04AC79 (void);
extern void RaycastCommand_set_maxHits_m85328D33D9A03350705F8E792AB835CCC9E0EF8D (void);
extern void RaycastCommand_get_layerMask_mBF83D8981FDD45343B1E729854A78C0D59D3610F (void);
extern void RaycastCommand_set_layerMask_m5927E9524F8A1105B915FB04E59FEE65E5D95E51 (void);
extern void RaycastCommand_ScheduleRaycastBatch_Injected_m7E4418AF58937EBCB111749C2A5EC6DD2E3BF4F6 (void);
extern void SpherecastCommand__ctor_m04B04D1690849F029961341127EB025E040857C1 (void);
extern void SpherecastCommand__ctor_mEC47B62DAD3FE54941D68903C40327231E3DEEF3 (void);
extern void SpherecastCommand_get_origin_m0AB8181C1233B9E62CF46ED485DF5448BFE2B236 (void);
extern void SpherecastCommand_set_origin_m78010578C88B3BBBEA2732D6B101C9818E113A60 (void);
extern void SpherecastCommand_get_radius_m304FFC1538682C7BDFD01564B84E87BF9984009E (void);
extern void SpherecastCommand_set_radius_mF2F0DB4FEDD1DFC396D3DFC52E7964616778F9E3 (void);
extern void SpherecastCommand_get_direction_m18300C7CEE58565AEC540BE249F11D5AE452CE87 (void);
extern void SpherecastCommand_set_direction_mAF24E6F239F768CE2F6D795AACCD79DC5F887042 (void);
extern void SpherecastCommand_get_distance_m12385D25AB25C8C3656E370DDCF50BD924499233 (void);
extern void SpherecastCommand_set_distance_m32AAFE7BE818D729124117A663F470379E4DF9DB (void);
extern void SpherecastCommand_get_physicsScene_m3EE7B58FE0F80F8EC89EC5AFA548D6F605E92022 (void);
extern void SpherecastCommand_set_physicsScene_m6ED30632068593C2DB57FC49CBBE56B07DC0ABEC (void);
extern void SpherecastCommand_ScheduleBatch_mBBBF1BFF56DFE84E2EA2BE32D1A43AC2ADF95B00 (void);
extern void SpherecastCommand_ScheduleBatch_m81B075750097344CB175C412B41F26B7D452A6A9 (void);
extern void SpherecastCommand_ScheduleSpherecastBatch_mA961B7A80D08B02E07734216F24A519621D73963 (void);
extern void SpherecastCommand__ctor_m0D0BFD29A98093A4A98F2689EBAA7E326B1C2117 (void);
extern void SpherecastCommand__ctor_m4A2082D1C681B8685B4A1DF2E5EA0AB4403EBCFB (void);
extern void SpherecastCommand_get_layerMask_m443973FC7B8DC0E48F13DE061F70E2E2AA8AF4C3 (void);
extern void SpherecastCommand_set_layerMask_m9B615EFE1A9D7057FA00E318B8D4CC38CEFC20A2 (void);
extern void SpherecastCommand_ScheduleSpherecastBatch_Injected_mE55F4F3EA74669E1D8B35A3605E80FD73066C518 (void);
extern void CapsulecastCommand__ctor_m2B94B22D7233C5E4DD8B88A5D527336195F063F8 (void);
extern void CapsulecastCommand__ctor_m3485B6028BCC02B96DC64D039E9BBEE73154D200 (void);
extern void CapsulecastCommand_get_point1_m6DFFD9A6C8D4221269F06D50B437D5CF48529FE5 (void);
extern void CapsulecastCommand_set_point1_mB37C2BCF42C923D514AF9E78C699F85242EAA563 (void);
extern void CapsulecastCommand_get_point2_m78EC7D397D29EFCDD0E42CAD32C88947F4D4ABB4 (void);
extern void CapsulecastCommand_set_point2_mE75966408CD135BAF86B446AF5379ACB6FE98880 (void);
extern void CapsulecastCommand_get_radius_mD77A81F5114A1580EF1FF141C738560ACBA7DC13 (void);
extern void CapsulecastCommand_set_radius_mA968E8E31E09D5B0D861DFDB1D9D8621ACB3AB6C (void);
extern void CapsulecastCommand_get_direction_m752A1EE8CC56AB2ECB9FCA56D65BF86DE4AE9242 (void);
extern void CapsulecastCommand_set_direction_m57DBE62F611302029343DA62526803E21A72C754 (void);
extern void CapsulecastCommand_get_distance_m24EE1EBEF3D9CAB31A067A509743ED813CF4A2D1 (void);
extern void CapsulecastCommand_set_distance_m9579828070C608533DDD1DBE530A5AEF4905EBC5 (void);
extern void CapsulecastCommand_get_physicsScene_m1763C75CA3E31975B0628E62E1A6681AC38C10E0 (void);
extern void CapsulecastCommand_set_physicsScene_m0FD54689DF616FDC760D2221B55182DF4CCE20A8 (void);
extern void CapsulecastCommand_ScheduleBatch_mAA20524A47DEB8A2589E3BD6B60BD9641CB98059 (void);
extern void CapsulecastCommand_ScheduleBatch_mC0EF5FFF4A1CD532FA4F85B7C3457D72BF131DD8 (void);
extern void CapsulecastCommand_ScheduleCapsulecastBatch_m7A7A8E9274B2A0E440E55C65EEF6B2AC72683214 (void);
extern void CapsulecastCommand__ctor_mFBD0653BF8A3DD1F0C489979B014A2E5F5A36397 (void);
extern void CapsulecastCommand__ctor_mBDF287919FF6EE28169910A6B333947755D8BE92 (void);
extern void CapsulecastCommand_get_layerMask_m519E4018F493F32B213BCCFA0C6C6A5AEC8C331A (void);
extern void CapsulecastCommand_set_layerMask_m57E3F255278021426F7D11B6ECE736A7706EFD44 (void);
extern void CapsulecastCommand_ScheduleCapsulecastBatch_Injected_mA6D010BDAF400BCB239DCA29514DD42F573F605B (void);
extern void BoxcastCommand__ctor_m537698355BE6E7C5C981AF853DB6492FF3A6AFB5 (void);
extern void BoxcastCommand__ctor_m9ED746D4ACF937622D7CF6133CDE908A39BAE1AC (void);
extern void BoxcastCommand_get_center_mD46099507F4864267BF537C80FCA33683EE0CB24 (void);
extern void BoxcastCommand_set_center_m7A8F2A037D6787571859B4049584B10A6D555C38 (void);
extern void BoxcastCommand_get_halfExtents_m133E7927EB8962266153E109D12A2B60BBE195F7 (void);
extern void BoxcastCommand_set_halfExtents_m82FC465A89C326F95F35B5F95ABD96B50551941D (void);
extern void BoxcastCommand_get_orientation_m693D6DBA29CABE1B594AA6F2BE5310415815D544 (void);
extern void BoxcastCommand_set_orientation_m1CD41FB7539D597AF8E9FA9BC12318D918A46471 (void);
extern void BoxcastCommand_get_direction_m1C104750BEC1BE901952975E35449BC3F4830899 (void);
extern void BoxcastCommand_set_direction_mAB860B183A345132B99C3A5E8D2FADA5417E357E (void);
extern void BoxcastCommand_get_distance_m1DE2D7596C33AE1B19A4309576A818A60D62FB5F (void);
extern void BoxcastCommand_set_distance_mC6933F0893A7531667539B643842C85450412E98 (void);
extern void BoxcastCommand_get_physicsScene_mDD75C74B54E085D09972CB7894B19384A49AC3FF (void);
extern void BoxcastCommand_set_physicsScene_mA447E367EC5936A5C621026E98986B109FFA2CD4 (void);
extern void BoxcastCommand_ScheduleBatch_mD33D3B6948D72061216545362C812F74AF7E93DE (void);
extern void BoxcastCommand_ScheduleBatch_m979A4B9E08534FB53764A82E7268E3989BD8382C (void);
extern void BoxcastCommand_ScheduleBoxcastBatch_m3F43E29DDF3179F9FF19892557723A4883C76599 (void);
extern void BoxcastCommand__ctor_mFFDEA928E01C4FE4D034143AD706A9C994EE73B0 (void);
extern void BoxcastCommand__ctor_mCA72956F4705B234738488EC66724FD30B589746 (void);
extern void BoxcastCommand_get_layerMask_m86215B17A1CF8661C744DD5686B21B3F4D02EE1F (void);
extern void BoxcastCommand_set_layerMask_m3E8588D218280C04C76F2C2DF0D38BCB54800F3C (void);
extern void BoxcastCommand_ScheduleBoxcastBatch_Injected_m254F75FB3ED73BB45FFD72C3C7B3FA3E12D1BFCD (void);
extern void ClosestPointCommand__ctor_m4141AF23EA150FE427CCFDC8736B29EA79B0E462 (void);
extern void ClosestPointCommand__ctor_m28FE39F299728FB0D1F3C7FC6E96AECAEB314BC5 (void);
extern void ClosestPointCommand_get_point_m614B7DBBAE578B0F90DE6CE2C04581A7F4A9252C (void);
extern void ClosestPointCommand_set_point_m081F4AAB8007B64EF2B0BEBEC1AAAA490E5F135A (void);
extern void ClosestPointCommand_get_colliderInstanceID_m814CB755AFF21E5D8CE4335CA5138D4C2B1EC017 (void);
extern void ClosestPointCommand_set_colliderInstanceID_m033DF85E7ED333AA5F65AD31992180FC21A76464 (void);
extern void ClosestPointCommand_get_position_m1DDC44DAB526733668BAFA4EEF8BE8115251719D (void);
extern void ClosestPointCommand_set_position_mB17F876ADAE3832D149A219B67CD98E24CB84BD5 (void);
extern void ClosestPointCommand_get_rotation_mC188C1910A3A53FD57844EA416A5BE34025BC7F0 (void);
extern void ClosestPointCommand_set_rotation_m9577993E95E2B9A5A6A118E3778700B1FB5919B0 (void);
extern void ClosestPointCommand_get_scale_m32B7E3E310D3850A838518F31B51BDB3797E7CCC (void);
extern void ClosestPointCommand_set_scale_m2875D2E39D8F39FE955D2D59D551D95E3C0807FF (void);
extern void ClosestPointCommand_ScheduleBatch_mE01BDCB991219F0F5C7566075AAA444B557FA8B5 (void);
extern void ClosestPointCommand_ScheduleClosestPointCommandBatch_m16DEA01FAD29EDF8B76CF299C351D66E25E22963 (void);
extern void ClosestPointCommand_ScheduleClosestPointCommandBatch_Injected_mA0F9172509CD1EF4C92F9996B9B5256847AA1AEF (void);
extern void OverlapSphereCommand__ctor_mA53F5911553913E86C4374212FA4F98849E53331 (void);
extern void OverlapSphereCommand__ctor_m77EF721239EB5B4701605ED24474EE4BF986F7EC (void);
extern void OverlapSphereCommand_get_point_m8A4C6707D923231644CB12A12AC7CA949AEED768 (void);
extern void OverlapSphereCommand_set_point_mA7FA5A5E4E43BD1B19E7159A9A1AB826ED130424 (void);
extern void OverlapSphereCommand_get_radius_m6641206FCF8B06BDD09FE7FCF8B4DB86C9BDDE0D (void);
extern void OverlapSphereCommand_set_radius_m6BE30815AD7DC52847CE5267903AD38FA1BAF97A (void);
extern void OverlapSphereCommand_get_physicsScene_mADCB56464A1D9EBD73AA1093A25854746B2E307D (void);
extern void OverlapSphereCommand_set_physicsScene_m34A25CC89E8645889C84F4A6A3F276BEFA987BD9 (void);
extern void OverlapSphereCommand_ScheduleBatch_mC2AB1755EAD42F985DF5F0C195BF20AAA803FFBB (void);
extern void OverlapSphereCommand_ScheduleOverlapSphereBatch_m69AEE9C11DB4076BB1CF4207B3637AA2EB75222F (void);
extern void OverlapSphereCommand_ScheduleOverlapSphereBatch_Injected_mCE63DECDEC4D00C235E25BC548C60941BA7D828A (void);
extern void OverlapBoxCommand__ctor_m0700FB9BF317EF23B875619048A36D2F8691C7A2 (void);
extern void OverlapBoxCommand__ctor_m456F1D2B75CD344E50931669D39A93DD3DD15C9B (void);
extern void OverlapBoxCommand_get_center_mF002B687D58A1CD1F31D8F5B445E7D2356A5A33F (void);
extern void OverlapBoxCommand_set_center_m95FB7FC7D91FADDC41CB513135C3CC08D0BB7490 (void);
extern void OverlapBoxCommand_get_halfExtents_m251067DF176F2EC71D8ECF6790BBD8ECAE57E3A5 (void);
extern void OverlapBoxCommand_set_halfExtents_m71E91162BF53AE2E003E9F17A477C97D7A37070A (void);
extern void OverlapBoxCommand_get_orientation_m9B593B572EB7D04B8A64334B028B600D12C1552B (void);
extern void OverlapBoxCommand_set_orientation_mF00A5CFDA9242A6738E3A8458A88A219E00DF54F (void);
extern void OverlapBoxCommand_get_physicsScene_m143BE67D0DD6C1AF45F4F51A4B20E3A4BB35A687 (void);
extern void OverlapBoxCommand_set_physicsScene_m5C7EEAB11A4465E1CC58029647B3E271E1B43E98 (void);
extern void OverlapBoxCommand_ScheduleBatch_m7D7E55D43BA475815B97C18145B16E9BB427C279 (void);
extern void OverlapBoxCommand_ScheduleOverlapBoxBatch_mF80EE90AE0A453FFEDB16067434803D9BE772D25 (void);
extern void OverlapBoxCommand_ScheduleOverlapBoxBatch_Injected_mDC3CA4B45216402CB7D3491FA90B5A2A6B41072C (void);
extern void OverlapCapsuleCommand__ctor_mFC3B345057E39BADD029DB9CAE9AA5E32FFC72DC (void);
extern void OverlapCapsuleCommand__ctor_m5ED84963BE98C5C9247885CC593FF291A67CFF2F (void);
extern void OverlapCapsuleCommand_get_point0_m34A2DEDD42E8304E06C33C3BE4B8AFD20DAC32EF (void);
extern void OverlapCapsuleCommand_set_point0_m42F1772676705667421D2D3211970154FF48BC32 (void);
extern void OverlapCapsuleCommand_get_point1_m8DA15C10389BEA183855D52C1BDABA9A8426AF7C (void);
extern void OverlapCapsuleCommand_set_point1_m87C54B6484C5A7BA3AF7B9DB88AE1EE585DE812E (void);
extern void OverlapCapsuleCommand_get_radius_mEC13C74A0C6683B6711F6C3A5EC9DF749159418B (void);
extern void OverlapCapsuleCommand_set_radius_m6258361B0AC1E7C903A6F8EE119239688CE8CF87 (void);
extern void OverlapCapsuleCommand_get_physicsScene_mF8BF85E4E8670480B67CCC532078F93270064DD3 (void);
extern void OverlapCapsuleCommand_set_physicsScene_m052934A9F6A2A5097A72AC74D4968A18564CE3C8 (void);
extern void OverlapCapsuleCommand_ScheduleBatch_mAEAF1C7D75C3CA0094A77827D4B3E3933D0D812B (void);
extern void OverlapCapsuleCommand_ScheduleOverlapCapsuleBatch_m61C25D795B9EB5935941A65E20A7EA6340564A68 (void);
extern void OverlapCapsuleCommand_ScheduleOverlapCapsuleBatch_Injected_m7AF66255B67B6C85BF47CD0D75AF38AF9657EB12 (void);
static Il2CppMethodPointer s_methodPointers[1358] = 
{
	WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274,
	WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E,
	WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34,
	WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E,
	WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755,
	WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C,
	WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F,
	WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43,
	WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6,
	WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5,
	SoftJointLimit_get_limit_m565D543DC9482F893A8C1F8582B9A06F7E287286,
	SoftJointLimit_set_limit_m34B7F00528D7F5B03D2AC39E44AFD96F0EAADF1A,
	SoftJointLimit_get_bounciness_m978E3102B620170A84C30EC15963B04564866DA8,
	SoftJointLimit_set_bounciness_m1D956BA96C435546CF89A8936F684A3C2E3BB036,
	SoftJointLimit_get_contactDistance_mD9CFEBFE02B773802773E53F9643C20E8C274020,
	SoftJointLimit_set_contactDistance_mDF20A5E81A2184F22CBA3007BB871BF87CB8FF2D,
	SoftJointLimit_get_spring_m8424406A064106C77453C7B24FACEC365ECAD991,
	SoftJointLimit_set_spring_mA96D8FDEA28FD24B2E3077ACA5159F74047F0EE6,
	SoftJointLimit_get_damper_mD68459A168731FF3AB023A227286A19D8B72DEF5,
	SoftJointLimit_set_damper_m3AF75ED0C45C84404FE3B4ED3850DD9A30704753,
	SoftJointLimit_get_bouncyness_m711720910DECFC3A5C77F0A24602A820659A418C,
	SoftJointLimit_set_bouncyness_m2AA2F20F2ABB85328D343253D8A3836E61EE2E67,
	SoftJointLimitSpring_get_spring_m04C597C18DD13494AFBAEB9348D7877EDAC0AA08,
	SoftJointLimitSpring_set_spring_m9A216142953ECC1CEE5080D603D18F9D1BD0A6EA,
	SoftJointLimitSpring_get_damper_m7B70F7492F1C75B68E879C484681AD00FDB771EE,
	SoftJointLimitSpring_set_damper_mA86F8E250BA84A6DC3E84DC1A40319A39CD5CFD6,
	JointDrive_get_positionSpring_m41EABECAFA44BAC2E33CE9FD278B3DEDE884585D,
	JointDrive_set_positionSpring_mC928C6830ABEC56D68FB9B054DCD2A1A807EAD52,
	JointDrive_get_positionDamper_mA22BFFB513E083AE41711EA6EA96D0424E7A9D1D,
	JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604,
	JointDrive_get_maximumForce_mED9DD2A8EAACC262A83CED0991F2A42E78B2F3B8,
	JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5,
	JointDrive_get_useAcceleration_mAFE42782DBC795844A8C698802064AAF34D12A45,
	JointDrive_set_useAcceleration_m04919856A177B9ECEEDA9178F64E85625C6CAC33,
	JointDrive_get_mode_m7012ADA832DB3EECCC4EBBD23B0C067F9AD97918,
	JointDrive_set_mode_m1C934E502FE9705DE2DC7C37257C10BAED74D394,
	JointMotor_get_targetVelocity_mDB63119AC6C3AF796785042AF466CD02D937820F,
	JointMotor_set_targetVelocity_m6F58E447E9C1F7812246ECD1CB8C2929D1CE86DE,
	JointMotor_get_force_mB4BDD8D40380A7E01E9C062BAADB1BE97F6A63FE,
	JointMotor_set_force_m66139CAD801991E3788835067C0D738EA000BFD3,
	JointMotor_get_freeSpin_m01BCFC597BF0DF5540378A79FAC1F8D0F4336683,
	JointMotor_set_freeSpin_mC37B615961CCAC6A3DC75109476C8C46FF4E894C,
	JointLimits_get_min_m3E8D3C572B30DA53262849D4D5BFFD9A77276FC8,
	JointLimits_set_min_m6DCC6F92E715577794E36CD524989509D2A001AF,
	JointLimits_get_max_m4E6BC6E5D320C4E2E7599852B19F4524D9549638,
	JointLimits_set_max_m192F8B77417D548BF0162E651049DB1C4C1D81A0,
	JointLimits_get_bounciness_m631282F7314872399F85C93F9827AA1F79BEFBAB,
	JointLimits_set_bounciness_mEB2995C51F0E0F6591D42881CB7CC69CF1835CC9,
	JointLimits_get_bounceMinVelocity_m2BF2A2C9471171604CC519C205D2BC738371C40A,
	JointLimits_set_bounceMinVelocity_m3D1D91AF20CBD28E66EF1BAA7AAB003BC9E671E1,
	JointLimits_get_contactDistance_mD306F20301F701B403B1D85EF984E958158F3717,
	JointLimits_set_contactDistance_mF8CC4376AC7E794C0E2FA1EBF035798EB82680E8,
	ControllerColliderHit_get_controller_mEC3E909A4B9843AA2F7A6606B021D3E88771F9EB,
	ControllerColliderHit_get_collider_mA2CF90334AD1231C04452B2D99715A9E289691D6,
	ControllerColliderHit_get_rigidbody_mDEEE467B695AAC7418C350B9BE599B8D030413B3,
	ControllerColliderHit_get_gameObject_m206F4915101DD080BF0EF53F3F2BE79404C5936F,
	ControllerColliderHit_get_transform_mAD3A9A4507A78EDA3C3045E2B74EC46EC80155D9,
	ControllerColliderHit_get_point_mCE74937BAC07AD84F6B255471177974A5C12E915,
	ControllerColliderHit_get_normal_mDA7A9B952DEA2B2EDFFDD153DFAA08089C9FFBB6,
	ControllerColliderHit_get_moveDirection_mC4C6384C18B4DAE3D301D079AE2A30FCC2E78A93,
	ControllerColliderHit_get_moveLength_mC681AFC545104C3F9F1B117D9603C3A80EDF4CED,
	ControllerColliderHit_get_push_mE79ED6947D82E806A17985A6D000DE230C90682A,
	ControllerColliderHit_set_push_mC12AD21A9E81C7EA7C908348BA066BFA86E15E9C,
	ControllerColliderHit__ctor_m7DDDC7B3A2847014BF033353283D2233246A9373,
	Collision_get_impulse_mBA2EDD39B7F495FF335FB867B244253602C7EF5D,
	Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0,
	Collision_get_rigidbody_mD7A14B9C8AA98352340D2AB0097FC3A424FBB81B,
	Collision_get_articulationBody_mB550C4981EF75F1BD399C5A90927D5BA51F5EACC,
	Collision_get_body_mA03043499C5BAF241F96F3FAEE183F7C5371A246,
	Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D,
	Collision_get_transform_mA5D135D9F696635EA7A0D2184CEF499427A6D0F6,
	Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E,
	Collision_get_Flipped_m27CF19F2767881D0A84AAEDC19250C6361ACF496,
	Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1,
	Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150,
	Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473,
	Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912,
	Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7,
	Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C,
	Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD,
	Collision_GetContacts_m3E2B52E011083420A9A1F5E551798F9483622292,
	Collision_GetContacts_m3E18E9CACEA6DB7298B4D31F0FF1E89D9C21D648,
	Collision_GetEnumerator_mC8CBF3C9ACC4277EB5730D8388DCEF3F2DA9DBFD,
	Collision_get_impactForceSum_m62DBB3F8E317209D78077F48FAB93ED9F4A20BE3,
	Collision_get_frictionForceSum_m3FC8545019D1AD089BD1CD5A22467E05F9B95B64,
	Collision_get_other_mC093EDECBF5EF40857681189B81CF7959C0178F0,
	ArticulationReducedSpace_get_Item_m8E297D94FA09BCB4E45C045BF0411D67E183BF24,
	ArticulationReducedSpace_set_Item_m511249FEDD7DA1241235F2D78B17E495435141CB,
	ArticulationReducedSpace__ctor_m73747277F64DBDD2DD2C2027F7848AB29A735D0D,
	ArticulationReducedSpace__ctor_m1993AEDD6D0F94169DDDE6D3E4BFEB4B1D2144EE,
	ArticulationReducedSpace__ctor_mE85D365C229AA06F3E7D4FB73717088D7F20B461,
	ArticulationJacobian__ctor_mD33412D6B8F30DA581549DF60FD9727204971574,
	ArticulationJacobian_get_Item_mC4DCABA2709F24770BD58BBF007909757BF2AE16,
	ArticulationJacobian_set_Item_m1CB6CCCC4FC036E19F9CECE28C6488245082C85D,
	ArticulationJacobian_get_rows_mD319F639F0B37D27355015EAD8BC0BCC027D6EDD,
	ArticulationJacobian_set_rows_m1782AA659C927A76DCBE506D7EBB429F0DC16C8E,
	ArticulationJacobian_get_columns_m2CD8D3E79C5A0EE73DEF851ECBD6682FAA4738AB,
	ArticulationJacobian_set_columns_m911137E1ACF2BA000559FD77849C07E3B60E679E,
	ArticulationJacobian_get_elements_m6E8E68D8FF8F98B8D532397C519E3F433162264C,
	ArticulationJacobian_set_elements_m3D711A40DB5A05C254933079A71CFFC756D86074,
	ArticulationBody_get_jointType_m609D1CDD2C3A2576B05BCFF700F37DF49E715E88,
	ArticulationBody_set_jointType_m74B1899BE7793E59F25FDE4B746D435FA0EAA357,
	ArticulationBody_get_anchorPosition_m52FE087DD45A380C3315D17CF4BA11001F718742,
	ArticulationBody_set_anchorPosition_m7A1724AC83FBA33D635261072D6A87C8DC2D343D,
	ArticulationBody_get_parentAnchorPosition_m85E0762DD0AC972325137A25599A73773189C1EE,
	ArticulationBody_set_parentAnchorPosition_m2C3541E1299226DEA7E1A42B163A6FDB19ACAB7E,
	ArticulationBody_get_anchorRotation_mB7A71B90339AC65B1CC6640E7DBE60795D4199D7,
	ArticulationBody_set_anchorRotation_m7208756EA5041654CD22E65C9579BC948A173E9F,
	ArticulationBody_get_parentAnchorRotation_m59C2B74F86297F9660B6FDB9AC38821133ECF4A3,
	ArticulationBody_set_parentAnchorRotation_mC98915B78FF81EE18C70643BFC8F4342E2ECDE24,
	ArticulationBody_get_isRoot_mFF2F4C90378FA83EA960AE713667DA68D2CB9615,
	ArticulationBody_get_matchAnchors_m131E537DFE90D13553DCC8D4D5FA8D0DAC9B8813,
	ArticulationBody_set_matchAnchors_mDF6F89EB401B1E9E8D802C891C1A89F81426A97F,
	ArticulationBody_get_linearLockX_m9F38421E04E71A934EA6464957BDDD8A183CD705,
	ArticulationBody_set_linearLockX_m3BDEA1A79A853310942F8520870B5A5782274BB3,
	ArticulationBody_get_linearLockY_m2B18C43F5705D4927AE6E628E1C22EAFE8C285AA,
	ArticulationBody_set_linearLockY_m6949327647A3598F80AA22389F436D38757531DE,
	ArticulationBody_get_linearLockZ_m9894178BCC40BF603F48C68A6BCE83C6CA085799,
	ArticulationBody_set_linearLockZ_m0A5FCCD4F2FAFE2F871072F873D1FD74DABFB174,
	ArticulationBody_get_swingYLock_mBE11D09C108416E0C79C9A4498303BC71015444A,
	ArticulationBody_set_swingYLock_m8E2231FEFC285F75F247AAC6D65503BAF92B203C,
	ArticulationBody_get_swingZLock_m163A505217D2C71A392A0A60B4D2EE97A6A15B0F,
	ArticulationBody_set_swingZLock_mA4DD9CB649E518EDCEB72F15BB6C3C3F5C15BD40,
	ArticulationBody_get_twistLock_m529CC23499A2F88CB7660D3F764E821EA3ABE04F,
	ArticulationBody_set_twistLock_m3DC7D5CA5E9AA8CC7BF62765740CB1DE3E6E4C2F,
	ArticulationBody_get_xDrive_m554A6D155402E0B4630EAD7086F6221B2DAB0616,
	ArticulationBody_set_xDrive_mDB703DA4006DE8EA80005BAA83AB470AE7AAFB44,
	ArticulationBody_get_yDrive_m3BBEB6734EEEBA21BAB904B534CEF431F5455044,
	ArticulationBody_set_yDrive_mA3430A2EB81593EFDF445AFE7E8D3C5FA9ED6BB3,
	ArticulationBody_get_zDrive_mEE866F622D6302EEC63D46CEA12C098E9D86F1B6,
	ArticulationBody_set_zDrive_m8719953DF721F20CE495BB61F6DF4950FBC9C6AE,
	ArticulationBody_get_immovable_mD872337FA184D140DAE64017CC04D4ABD1139754,
	ArticulationBody_set_immovable_m060DB3218F5B29D9F86057BA4E25589BE7EEDD98,
	ArticulationBody_get_useGravity_m6F29878C8F0DC15C3AFB9348AE5C4B27784BD698,
	ArticulationBody_set_useGravity_m8D5E78169ADBCB88DCE1EC005BED4060108D7974,
	ArticulationBody_get_linearDamping_m4E22CB49EB2DE1F5468740DBB3DE9D285E8B2188,
	ArticulationBody_set_linearDamping_mC72B0BAB28D6BBA9478C3D63913851A64A5584A9,
	ArticulationBody_get_angularDamping_mA5F118D2A0EDD74D64F185CF09E363B738E724CD,
	ArticulationBody_set_angularDamping_m227975CA9AF019A05CE0BE8C3679667404FCB5DD,
	ArticulationBody_get_jointFriction_m826FDBE9097B80A39BE9A59BCB5C96DA60601370,
	ArticulationBody_set_jointFriction_m9180C71E00D8EEFB1BB5669C57D7ED477A158D44,
	ArticulationBody_get_excludeLayers_m6C1B3A1ABFD07CFB9F86FB1622DE3C31E5FD8AA9,
	ArticulationBody_set_excludeLayers_m8130D65AA2A87D9D9B954A30303D77F098D6FC21,
	ArticulationBody_get_includeLayers_mF917384F9BE11A7EBC9595E4E0680BB1CDDA10AF,
	ArticulationBody_set_includeLayers_m3AD11471144E213A3D00F9A0A7BF1AC907173CDD,
	ArticulationBody_GetAccumulatedForce_mAAEBE584B1FC5DA34D01E814F0F4311A4CE02849,
	ArticulationBody_GetAccumulatedForce_m3312D41EC76E292587BE1F7EF1C4CFA45DB62BC6,
	ArticulationBody_GetAccumulatedTorque_mB13DCE9AE802256C5E06BD31F0522C71B3D951EB,
	ArticulationBody_GetAccumulatedTorque_mFDFD44265057BB4B66181BD53962C9C16EE6D798,
	ArticulationBody_AddForce_mF22B0DDD540E665672A5EA2B925413A293B5CF86,
	ArticulationBody_AddForce_m9B951C7F08F82992D45F1053538DB10D769F7399,
	ArticulationBody_AddRelativeForce_m4C28F79208B2E23B34B6B52E8D37A728DC07D9D6,
	ArticulationBody_AddRelativeForce_m25655BC006A53FF0C02A8AC909D3B1886A798D54,
	ArticulationBody_AddTorque_mA8DD6322BC73E42B7CA307A17025CE02BAC18D5E,
	ArticulationBody_AddTorque_mAC35D1BCAFFBCA6CDE52EBD7F05439A9885D992C,
	ArticulationBody_AddRelativeTorque_mEE1EAF067C96E1C94F8365DE649E36A92EDB133D,
	ArticulationBody_AddRelativeTorque_m6AE0A658FD43D01E018E181EF5CFFF202B103954,
	ArticulationBody_AddForceAtPosition_m823FD5E55A00334AA6411AB1D448AC42B93B4398,
	ArticulationBody_AddForceAtPosition_m4E8E2B65930B95CEAED38CED1116E8739ABF12CE,
	ArticulationBody_get_velocity_m3EBF7C1AACDBDDBAEDE1C1BB2BA4D7B6E09B86E0,
	ArticulationBody_set_velocity_mA3002367A46044286F14619DA21A78A0AE862371,
	ArticulationBody_get_angularVelocity_m32BB9324AC3B3B5FA1C569ECF7247E0519B46D6F,
	ArticulationBody_set_angularVelocity_m1AF7425EE7AE76E3E6DC4FE883071BDE17F7E61F,
	ArticulationBody_get_mass_m5733F78B9EC616543FE6554054E2612DE66BC526,
	ArticulationBody_set_mass_m2AB9564735A157973D5391808422BC22546FA7F1,
	ArticulationBody_get_automaticCenterOfMass_mAD4A2C8DBA0E7C44D4753CEE770807984E820E68,
	ArticulationBody_set_automaticCenterOfMass_mE874C710ED017F851F866FF3EA5DCB113E01E3BF,
	ArticulationBody_get_centerOfMass_m446C2E8D092739D924A42E846AEFD08195FFBCF8,
	ArticulationBody_set_centerOfMass_m1ECE3361868408A45CF7EB8EE1C8E32A5B3A2AD0,
	ArticulationBody_get_worldCenterOfMass_m4E6429D6ECCD02036834E76871824C6355DF38D7,
	ArticulationBody_get_automaticInertiaTensor_m8FCF2A23BF9B04CF49686370AAD996989BB75F79,
	ArticulationBody_set_automaticInertiaTensor_m40CCF17F069654ADF879004C6DEA732BAA794908,
	ArticulationBody_get_inertiaTensor_m51E37D406035FF9404BA41BC682C8846F246E2DE,
	ArticulationBody_set_inertiaTensor_m2BCDD0B82E88AF941B34AEB5034D069E9DFCE5CF,
	ArticulationBody_get_inertiaTensorRotation_mAD0842073A2BF44C014F55C524AB4F2FD8D511AF,
	ArticulationBody_set_inertiaTensorRotation_m45F2F004CC1ACA83D397884AB649D3AFF5587DB9,
	ArticulationBody_ResetCenterOfMass_mED65F7632A8456E03DC5107E72A86CF4082704D9,
	ArticulationBody_ResetInertiaTensor_m59D42A82D026D517F3879F579A03E3CEA05853A8,
	ArticulationBody_Sleep_m86ED913C2CEBA591767BAF4B3D49F8630E5BC122,
	ArticulationBody_IsSleeping_mD3AD91AB25B962FF423EEED9C4B39D60BFB01551,
	ArticulationBody_WakeUp_m15BCC08A8355C8BC907C1F33AD29A0357D6F4AAF,
	ArticulationBody_get_sleepThreshold_mF3B4BFB1EB3279F76294858E8F0D8D2FD44B5703,
	ArticulationBody_set_sleepThreshold_mF6DE7E565575ECB4C2D0F1451D5F56F80728F0B9,
	ArticulationBody_get_solverIterations_m7DB7A009F4648341BAB4D3886CD1B162B91154C0,
	ArticulationBody_set_solverIterations_mAF9E14598443294D1C2D0076B3564B24901C0377,
	ArticulationBody_get_solverVelocityIterations_m8DFEB3020A6E19121031B967A8B7E5E596FDFD63,
	ArticulationBody_set_solverVelocityIterations_mF4E07BB497F7E544FCB2B3DF9A92D5E1E4AA8141,
	ArticulationBody_get_maxAngularVelocity_m597C5BF17A8D5BE704264DE02738528E79EE4014,
	ArticulationBody_set_maxAngularVelocity_m445FB4FD76177E60722E503B3419CC88F474AE5D,
	ArticulationBody_get_maxLinearVelocity_m11EF78BDCFA6A6DD115C08B22135F2FAEB3F0E10,
	ArticulationBody_set_maxLinearVelocity_m233275505A6E06C50F740147A05FB10D6062CBB4,
	ArticulationBody_get_maxJointVelocity_mD8D109AE1DCD8066C79F2970687EDC5C742D9521,
	ArticulationBody_set_maxJointVelocity_mF2F1D4EF96787FDB64BE8C13B7BF2D8035C6E6D7,
	ArticulationBody_get_maxDepenetrationVelocity_mB575AF853E2FFE006BE417CEA02A75B362FC3B8E,
	ArticulationBody_set_maxDepenetrationVelocity_m4EFF4F615959AE87C2CD46444D8010E6784AD6F9,
	ArticulationBody_get_jointPosition_m266ACE8D0F52AE5623773141D62B5A07DC301F88,
	ArticulationBody_set_jointPosition_mAFD0E744B706BCD4392809468DFF1A5BD14F7B07,
	ArticulationBody_get_jointVelocity_m75FD1283EB7800F6827F41CB66B4B44CCAF4EF70,
	ArticulationBody_set_jointVelocity_mD9E1F524BA45EC9447BE9BED676328A7574989EF,
	ArticulationBody_get_jointAcceleration_m4B26B223EE46FCE0262EDF69664BA976A0E19DC1,
	ArticulationBody_set_jointAcceleration_m9619B03EF12CCF0520C715C47532283D254E6F9E,
	ArticulationBody_get_jointForce_m0F4B1E1F3D10FE22AA6D99A05ADFF32C266EB617,
	ArticulationBody_set_jointForce_m093F02D8B73621F4963E2349185101B8E0963FAF,
	ArticulationBody_get_driveForce_mAC6289F2C0FEAFCD484E99A018AA60ACFB09E829,
	ArticulationBody_get_dofCount_m177710DDA46A7A519ADDA7136F0E76057310D7FD,
	ArticulationBody_get_index_m3D31F4C81F86B6CCBB0C9D15F156AD5891277BE5,
	ArticulationBody_TeleportRoot_m1054AD6E80683565B7AFC6184BFF11CEC9B807F6,
	ArticulationBody_GetClosestPoint_m1E269F8B5DA848D15D39D6C7FCB34A87A713AAB5,
	ArticulationBody_GetRelativePointVelocity_m891039DC0E2EE050056C20EE7BDF71F0233B3D5F,
	ArticulationBody_GetPointVelocity_m11FAC064AD19F02E89E35007466F29F1954B12A1,
	ArticulationBody_GetDenseJacobian_Internal_mC0A0A70A2D154A43F1907809E2C4CC559F074DA0,
	ArticulationBody_GetDenseJacobian_m96341CB7ABADE5A042D6A768972CB5CEB41FED32,
	ArticulationBody_GetJointPositions_m1CC7AAD19C42BFC43C6ECEEB7258A6B0AAD324AE,
	ArticulationBody_SetJointPositions_mACAE7A4AE300A7725967EE9FD457825028EC7452,
	ArticulationBody_GetJointVelocities_m48183E0CF6951CA83A7EC552141DA4A44073D79F,
	ArticulationBody_SetJointVelocities_m6EF57ACE2CEF61DE3142B865269D93309FF83E47,
	ArticulationBody_GetJointAccelerations_m613BF7554930EFB4DF970273B9B209EB5782E3A0,
	ArticulationBody_GetJointForces_m33E4782752D2C88E72C7BB78ADB6052C8D3758F9,
	ArticulationBody_SetJointForces_m1B2CD2FC20C1D6548B174E3C5A183088018B905E,
	ArticulationBody_GetJointForcesForAcceleration_mDB151463E9D57926A1A622B50149663FC64AD9D0,
	ArticulationBody_GetDriveForces_m3BD0F74E6A292810C80CB99426F71BFE704A476B,
	ArticulationBody_GetJointGravityForces_mE1949F9308E901700EDE816210C4B63DC89C0BB4,
	ArticulationBody_GetJointCoriolisCentrifugalForces_mE0E603B86EC6E37630E2C066765DFBC67BA5D298,
	ArticulationBody_GetJointExternalForces_m1CBC37D2F99D1276A325B13146F7E894310CBAA4,
	ArticulationBody_GetDriveTargets_mA2A8DFC26BEB1D61DCEB1ECC8D4EE0D3019284DB,
	ArticulationBody_SetDriveTargets_m13C98DA4C4E92A24452684F7C5DFE7E8783C8C49,
	ArticulationBody_GetDriveTargetVelocities_mC70C64005D6821AB0A2081ADBE24C7595725626E,
	ArticulationBody_SetDriveTargetVelocities_m101968660AA0F04DCF7765B8A5B23F395B6F6EA8,
	ArticulationBody_GetDofStartIndices_m89DA6F249B8796C5918C2AE9C0E68848EAF13384,
	ArticulationBody_SetDriveTarget_mE49A731A0AEAD46C5BA315C0671D560B90DBC3EA,
	ArticulationBody_SetDriveTargetVelocity_mE3FAC776128CBADC5F29A16215FF1F9E144EE025,
	ArticulationBody_SetDriveLimits_m942F2F9FCEBA3FA7F2A32432D568C68AE83A00FE,
	ArticulationBody_SetDriveStiffness_m62D7A7331147F4B29917EC3A3F5DCEA9A1C28F3C,
	ArticulationBody_SetDriveDamping_mC1D622926CCAF064A665D96D5CE217EB81E41D67,
	ArticulationBody_SetDriveForceLimit_m673B219F59821E93623F073D7C952B26D589F198,
	ArticulationBody_get_collisionDetectionMode_m4B835B0566653C7494241CA882E570DA6020613D,
	ArticulationBody_set_collisionDetectionMode_mD2C3B3682EE6580230211DF92A92A68A76325D52,
	ArticulationBody_SnapAnchorToClosestContact_m1B0EAC5ECF85916DC0E343C3FC583DB9E6DFCF2A,
	ArticulationBody_get_computeParentAnchor_m76E8D931D9FAFADB374B689BBAFAB87D7D480E74,
	ArticulationBody_set_computeParentAnchor_m3EB72D6F66ED41D5F2A9076F8D73611D144BCDCE,
	ArticulationBody_SetJointAccelerations_m842B40E9FD845111A975DB3C1E5D9BDC9199E9FC,
	ArticulationBody__ctor_m5B6C338377F7704618E1F1BEA870EE2BA9FCCA6F,
	ArticulationBody_get_anchorPosition_Injected_m6ED85849F4D6C1396B09A0959FEFAB9E186337B9,
	ArticulationBody_set_anchorPosition_Injected_m6976A2A8A1A89A6FF518E3C17C1BAB6DE20B5785,
	ArticulationBody_get_parentAnchorPosition_Injected_mD424237513DEE8E95E64DBB8E7340266B38A92A5,
	ArticulationBody_set_parentAnchorPosition_Injected_m95E9FFC53E3CFAA5B399D60A8CE3D3D3BB12F148,
	ArticulationBody_get_anchorRotation_Injected_mA5605F8AB57CB13492615E7D8CB62D05993C0E73,
	ArticulationBody_set_anchorRotation_Injected_mC98E0761ADA276C6DC2BE14F2D61863A1AF9B2C6,
	ArticulationBody_get_parentAnchorRotation_Injected_m10A670F6F42C6945039CE22BA9E1B633F0780277,
	ArticulationBody_set_parentAnchorRotation_Injected_m7F8C9D52C2E24253ABCA0381ADCE875D3A487CC4,
	ArticulationBody_get_xDrive_Injected_m94F855837D28DAC01527DB692CAAB7584DAD76C0,
	ArticulationBody_set_xDrive_Injected_mD207FE149C3A28306D94C2946A4062E5C0E4D742,
	ArticulationBody_get_yDrive_Injected_m04FECC54182229D2E7E041E0322B2F40077C1D14,
	ArticulationBody_set_yDrive_Injected_m9BFFC234ED132E63F61F961E0C52C0C5A6858C85,
	ArticulationBody_get_zDrive_Injected_m3B4B3BBCAA67A35127000656230611A5CF1E1013,
	ArticulationBody_set_zDrive_Injected_m87E3ED2763A6358724D51847388170E76A4CC711,
	ArticulationBody_get_excludeLayers_Injected_m01A779C216E47BC75A737F80FC1EA343068E6474,
	ArticulationBody_set_excludeLayers_Injected_m28FB96297700F4FE8931976FD8F3C924BB22550D,
	ArticulationBody_get_includeLayers_Injected_m4C23A817F5B49BA8D1336B23CAF69E53F3FC8B56,
	ArticulationBody_set_includeLayers_Injected_m619CD7233B1C669AE7F07D72C98E1813A9523FD0,
	ArticulationBody_GetAccumulatedForce_Injected_m48D085CC7AAD7FFDC902A9AF11D07A3F938B5594,
	ArticulationBody_GetAccumulatedTorque_Injected_mE92DB6DA6D36AF2FFC975C61578F55CBD3691543,
	ArticulationBody_AddForce_Injected_mECA4881D1B7A8587D24A8345C9453882627C715D,
	ArticulationBody_AddRelativeForce_Injected_m1C91329D11FA619BAD29771428D01AE8D5599855,
	ArticulationBody_AddTorque_Injected_m546EFB3BDB2EA9AA317AA3D7588849D51B4DAD80,
	ArticulationBody_AddRelativeTorque_Injected_mD38DEBF374A5EC4275BF144F87CA865BBCCD5115,
	ArticulationBody_AddForceAtPosition_Injected_m60B79E66C4F680F08348EA705E4046BED5414E30,
	ArticulationBody_get_velocity_Injected_mD7E4BF8264F63620C38C6AB9D53CB20416576AA4,
	ArticulationBody_set_velocity_Injected_mDD54625804CD16FB00CA902CC4169C866D815782,
	ArticulationBody_get_angularVelocity_Injected_m54EFAEE9CE3BF113111486D69E7C7EF3E0BD8CAA,
	ArticulationBody_set_angularVelocity_Injected_mBA99D0426A024B937A4173DB1CE09E8D42494D9E,
	ArticulationBody_get_centerOfMass_Injected_m49003733F7C6AFD2D4DB65279F85C8F32614707A,
	ArticulationBody_set_centerOfMass_Injected_m3D958F8D2CF21692A40FF8E9D7287CEDA408EA5B,
	ArticulationBody_get_worldCenterOfMass_Injected_m37078593523173C807D7D29E6A5605193B2F7CB7,
	ArticulationBody_get_inertiaTensor_Injected_mE40FE571128B90CEC8C18017F3426473E2F09596,
	ArticulationBody_set_inertiaTensor_Injected_m71013B214BCA9EED4F242ED6FB9872C82B722D7B,
	ArticulationBody_get_inertiaTensorRotation_Injected_m2572DB18AC9655BC9F59862299570F405A3B56F9,
	ArticulationBody_set_inertiaTensorRotation_Injected_m1C16580190C813F8C08BD062D3DDD80516E445D5,
	ArticulationBody_get_jointPosition_Injected_m5A9D3284D2DEFF594DC3FFFDF69987A5752B6FE4,
	ArticulationBody_set_jointPosition_Injected_m454B57C9B64BDD52B54A3BC893BD5DE6C380D418,
	ArticulationBody_get_jointVelocity_Injected_m66897CA952FF43C35791C974BCBC40BF5CF9C684,
	ArticulationBody_set_jointVelocity_Injected_m10A98EC383B780E521ECA449F0A6664E6FDFEDF5,
	ArticulationBody_get_jointAcceleration_Injected_m5A3C9D82AC7A8FAFD6C2AF4A37A1FD80BA55B995,
	ArticulationBody_set_jointAcceleration_Injected_m6483C2362F533D07AC0F926340631F06FDAFB0A2,
	ArticulationBody_get_jointForce_Injected_m8220BDDB6B124E26274F7C51185E275D699A9682,
	ArticulationBody_set_jointForce_Injected_mEE6424383372772EECA717489BB6FC2E09A3128B,
	ArticulationBody_get_driveForce_Injected_mEC1FB6B2482AE87EEE96D0996FBAE2C5C034562F,
	ArticulationBody_TeleportRoot_Injected_mA3FAA5AFA7AD43642AB35369F4AC2FC500B45A09,
	ArticulationBody_GetClosestPoint_Injected_m40B588E2F6793643DF097C1801DF4AFA15AD56A1,
	ArticulationBody_GetRelativePointVelocity_Injected_mDBC29A8E550F8ABE32A214BF79F2906FAAF2706C,
	ArticulationBody_GetPointVelocity_Injected_mA5268D1DDD14C13850396C6BD97C5691A4E83480,
	ArticulationBody_GetJointForcesForAcceleration_Injected_m60AB5E346A2CC0FBF302BBDAF99BD718FEE30581,
	Physics_add_ContactModifyEvent_m58DEA31C1DD8A2996A36991E98CA7B71C3E5EDAC,
	Physics_remove_ContactModifyEvent_m2A022D088F3B5F2214B76717DC93C3685B04EDDA,
	Physics_add_ContactModifyEventCCD_mE814521E0742D383E2D0CDE65560F6E439C42C96,
	Physics_remove_ContactModifyEventCCD_mCD7BA8BB26675501BCFB914F34B7D6ED773405CF,
	Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D,
	Physics_get_gravity_m94393492AE4ED8B38A22ECCDCD2DDDB71BFA010D,
	Physics_set_gravity_mAEF3D6B45E6E567F04244C7889366CACCB4F1952,
	Physics_get_defaultContactOffset_mD356CE957E83D14F43B3FF1BAFF6052F210A1B67,
	Physics_set_defaultContactOffset_m81A4A3C4CB07A1DC86F5A8B53DE4A137985D4C49,
	Physics_get_sleepThreshold_mA19D72F89C4AB632C16FA591071712428F701693,
	Physics_set_sleepThreshold_m4C6D94024F0508934D79633DDC20F77C8D28DFC7,
	Physics_get_queriesHitTriggers_m786C7A820398456AEA1972FB9DF02D8E7ED6BFBB,
	Physics_set_queriesHitTriggers_m763834B1050946C0C490CF9FF453783107AEF212,
	Physics_get_queriesHitBackfaces_mF39895218290962301C1B01940CC403F2B87AA35,
	Physics_set_queriesHitBackfaces_mC212AF1484AF3ECBF1922F52B56F78E16BDE31AF,
	Physics_get_bounceThreshold_mF5D7731B770E0BFC75C44C2A47B30F5AE1C0565F,
	Physics_set_bounceThreshold_m12BD6903B10DCA2606B6E9426F41FAE2DD015456,
	Physics_get_defaultMaxDepenetrationVelocity_m1FBF12B2DE45B073DFF1C540EDF9ECC521A1B4FB,
	Physics_set_defaultMaxDepenetrationVelocity_m6810E49893C112C6129D83BF7806879631C8CE2A,
	Physics_get_defaultSolverIterations_mB03C0DF7B60A8D453BA8D1069AEB69BB83FE7969,
	Physics_set_defaultSolverIterations_mB0A33680F0181471737C776367CB8F9F9F275B50,
	Physics_get_defaultSolverVelocityIterations_mD0DDA38BBBF4FEEA60CF5B417F8F9E321878381B,
	Physics_set_defaultSolverVelocityIterations_mD05B7ED682A3A670258473C5A796DD430D15BCD2,
	Physics_get_simulationMode_m5338D6C70919956484A3086D2DF5AAD47A046D54,
	Physics_set_simulationMode_m03D88E56C28F200EF0B4B5B6250CE663387FD4A9,
	Physics_get_defaultMaxAngularSpeed_m023AD46D9C5047F62DAD3792C1364F31E801E48A,
	Physics_set_defaultMaxAngularSpeed_m916DB68D77494363A8FDDC511A0BFC7DBDFC441A,
	Physics_get_improvedPatchFriction_m5E9F7696322D22B8C755E4A1F710D85A6205474E,
	Physics_set_improvedPatchFriction_mDB460BA53809468452FDDDF04608EEF8303CC1D8,
	Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8,
	Physics_set_invokeCollisionCallbacks_m99E0DBF4EFDF7E57D686938D11A6C81AB5360A8F,
	Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A,
	Physics_IgnoreCollision_mA8E5C54299FC47921E41BF864C7C2214621595D6,
	Physics_IgnoreCollision_mFBAAD9B91D488802086C1A1C96447CE4C869211D,
	Physics_IgnoreLayerCollision_mBB904E854512B9F1A4EFD4E23D61BF2D0E0AA879,
	Physics_IgnoreLayerCollision_m9270D67E34AE015D7177B5608852306E0366D6A1,
	Physics_GetIgnoreLayerCollision_m6FAFF3D7B295E3ECC55DF0F3032AD4DB6210255D,
	Physics_GetIgnoreCollision_mDE42B19E92F4D749E111346E31B652A3A41915AB,
	Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D,
	Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377,
	Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE,
	Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028,
	Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688,
	Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12,
	Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33,
	Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903,
	Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8,
	Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50,
	Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488,
	Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3,
	Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB,
	Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025,
	Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685,
	Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5,
	Physics_Linecast_mB0325E32F70A3BC4A56A06877DA5944355B3B368,
	Physics_Linecast_mE693FAFE56D0E69918A0948310EF642094C91DC0,
	Physics_Linecast_mA0E7B9AFA161F5A8432CA7CDDA25E20EC00AF2A5,
	Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925,
	Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6,
	Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932,
	Physics_CapsuleCast_m18FB0BA05A5E0AC9F1D4D115F560876C23A66662,
	Physics_CapsuleCast_mF3A0D7F51EF7E3AF9E7EB34C14517D1604408713,
	Physics_CapsuleCast_m870639B5EA540B86D28F19C6F079298A1523CF38,
	Physics_CapsuleCast_m1111160E7494D23382ECAFEA420E6E9E6D78D621,
	Physics_CapsuleCast_mAB6E13B795FAFAFE366DE88B8C7A31C54EAE9EF7,
	Physics_CapsuleCast_m121518311930963660B3BFBF136931BCA2E3A986,
	Physics_CapsuleCast_m0A540F025E5170C56348DBB377795CFA2EE9AFEE,
	Physics_CapsuleCast_m6D4A7249721F62B30FB349611B5F11029B3BCFAF,
	Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D,
	Physics_SphereCast_m2A41FD7023EC5B89B69E0A8948325BEF46D9597C,
	Physics_SphereCast_mD53E280903384004D4304150552D50DD3F8C1F58,
	Physics_SphereCast_m7084426E8B20720EB3E374B51FCF56F22C24B0FF,
	Physics_SphereCast_m753A9C9335C4815C7C30816B70D3EF51E59F3AAD,
	Physics_SphereCast_mCE6B04501EE9D9DF9E6AE427BD9496F69467FAD5,
	Physics_SphereCast_m62BD0F82567CE3D8865B117C8F6E9F09F2DAED08,
	Physics_SphereCast_mE7CF6EC82AD380F5AF523A73646274ABE3F5C3FD,
	Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05,
	Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7,
	Physics_SphereCast_m3ACE0130E065A00D12D340E0FCB2B4988538B2C4,
	Physics_SphereCast_mC78C6D9EC1FCE0052A8009D12A3EF398ECBFA36E,
	Physics_BoxCast_m1CFE2240751F5E67FD7D56A67CEBCC25A3991F27,
	Physics_BoxCast_mBE97489FF57BE2B47A37E820DA10A7C3AF155580,
	Physics_BoxCast_m73BF3E1FC9B8E2587BE333AD7F40C71E7ABFFBCF,
	Physics_BoxCast_mDDE7AED2DD992CBC382C529E5EE106D869B4630C,
	Physics_BoxCast_m115610507710F8215D31060325803EDD18078E5D,
	Physics_BoxCast_mB641B1C6FAB006950E3FB982FA42631231F496F0,
	Physics_BoxCast_m4E88A3E8736788553C34CC7864CB204E223146BC,
	Physics_BoxCast_mFDE93D2989DBD1FD278230537A2530EC6DE76493,
	Physics_BoxCast_mD1D6F883E3B2E4AB3BDE38CCB0A470A614A8C7BD,
	Physics_BoxCast_m5C4CCC14C34BFAF02B0CD92CBB52DAE803F14745,
	Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074,
	Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34,
	Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51,
	Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E,
	Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789,
	Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74,
	Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87,
	Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468,
	Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2,
	Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE,
	Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC,
	Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C,
	Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E,
	Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836,
	Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0,
	Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9,
	Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460,
	Physics_Query_CapsuleCastAll_m874A33B3421181746FC7662648E0306620F7D522,
	Physics_CapsuleCastAll_m367017D9CB85D5F7AA7448F70E16E94578C09214,
	Physics_CapsuleCastAll_m9467728846F780021AF7D40168E7CA0D6A76F2AE,
	Physics_CapsuleCastAll_m82E63C6623BFB2DF2C878C3928A93D5FC24C53DA,
	Physics_CapsuleCastAll_mFC08941E9CCF4E80799C5E2DC7D59A3D36EE540F,
	Physics_Query_SphereCastAll_mCC57A65CDDCE43957EA9FFC80958AC495958E58D,
	Physics_SphereCastAll_mE651DDBD29BBBBC4E3D33BBDE3C9C082ACFCB91C,
	Physics_SphereCastAll_m25687FC02567C1138B9588148AA2578764CD8424,
	Physics_SphereCastAll_m90D15BED8E37CCDC16BCD900697FDCB221A399EB,
	Physics_SphereCastAll_m7000B7404CD1D1F2C9B26FF73AD6E04FB7E7BC67,
	Physics_SphereCastAll_m5C7FCEAE4A6ED88B35ED554EDE37BB8367F08F46,
	Physics_SphereCastAll_m0886C4624531C71A2CC6A3196B9EAEE108C17CB5,
	Physics_SphereCastAll_mA6D34F1A7023429AB5168049E5707BC43179C356,
	Physics_SphereCastAll_m26EB51F5B7F27D1A3FF2B3DFDCE4648091D87B2D,
	Physics_OverlapCapsule_Internal_m089FEB489FE5BD280583DB2BF196D47A3E7BFD5D,
	Physics_OverlapCapsule_mE60FD023CFCAFCA0CD4A7D3BD01A8A2C20FC7415,
	Physics_OverlapCapsule_m2CED600F34C5BA1E865C2F23720A62909B17B510,
	Physics_OverlapCapsule_mFF90A7E21F9223A551B71EB9D5A90D4667AD3DCB,
	Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B,
	Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318,
	Physics_OverlapSphere_m2D0C9BC78473512F1F89AE731FBAE1B734EDF3EE,
	Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69,
	Physics_Simulate_Internal_m44166CBF3601E62BEA1B50D97D3F9BDF5C5EBF69,
	Physics_Simulate_mE7FC4862A1E3917A4F281FFAC8C156692EFEB77C,
	Physics_InterpolateBodies_Internal_mFF9F605B6A615D26ADED0EBF02A852CBD2C00822,
	Physics_ResetInterpolationPoses_Internal_mFF3E53A2B78BCEBED6164EAC45EA2C39E6EEB620,
	Physics_SyncTransforms_mB88B6B27C24234D18846F614F9AE674976A5F1CA,
	Physics_get_autoSyncTransforms_mA8A6D3B610E34D42ED30373AFA99A418271D67CA,
	Physics_set_autoSyncTransforms_m22463A478613FF7A86F90A580BABFAE1A0D68414,
	Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541,
	Physics_set_reuseCollisionCallbacks_m6FC1AB597226A805692A0E1F3942D2DC548E2379,
	Physics_Query_ComputePenetration_mFC4D885B9B0A1A511997F8F25D64117D075E3B88,
	Physics_ComputePenetration_mA9AA5B3B6982BAC84467322616E8423CA4E91AFF,
	Physics_Query_ClosestPoint_mC72F36C1868C7830580ACF450FF84F797DC1C1EA,
	Physics_ClosestPoint_m440650B860B4298CEEAC45599E1B326A3B8FFF41,
	Physics_get_interCollisionDistance_m737789D40ACAE21671B948C1F25D66BE437BC5ED,
	Physics_set_interCollisionDistance_mD391D15EBB74EF40E814AF628544F35D5790F854,
	Physics_get_interCollisionStiffness_mC423087A495A6C26D194D85DD81E1A5CAE670978,
	Physics_set_interCollisionStiffness_m230BA6D8B0E404AF161D5A691B3AAE9C33B5C2E6,
	Physics_get_interCollisionSettingsToggle_mF2F24FB1BFC4161E4D921A4804FEEC4630765C53,
	Physics_set_interCollisionSettingsToggle_m063E3B6499BEA583BC077D8BABE4939CF8CE866F,
	Physics_get_clothGravity_mF15D7BA3DEE591F134D61F0E2E14628CB82FBCE2,
	Physics_set_clothGravity_m7C89C1C2121E338E81AFF529672A454DB11B33CB,
	Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83,
	Physics_OverlapSphereNonAlloc_mB918CA3A78E54FE0D2933353AEB0A7A3EC0E07F7,
	Physics_OverlapSphereNonAlloc_m48E30DC51482BBBBDD762ECE1DF83A70088F70DA,
	Physics_CheckSphere_Internal_mC2E521D96447A8560D127D3EA42CB5785014B6FF,
	Physics_CheckSphere_mC32BB961B0CF9D23EDDEEC3F30021FD1BE88E261,
	Physics_CheckSphere_mD6F0027DBDECFA69245E99D8A4EE1DC8742A817F,
	Physics_CheckSphere_m3259DE166D6E8108517F008E91C65412D03E2852,
	Physics_CapsuleCastNonAlloc_m8190CE6AFACAFF3996D60D60BD110E0AC2E2583D,
	Physics_CapsuleCastNonAlloc_m7825DBD7BD1FA6F3A8707B6FEFF955269A2AB40E,
	Physics_CapsuleCastNonAlloc_mF6BEDCD504784CD39041479E3DA41F020ADF1E8C,
	Physics_CapsuleCastNonAlloc_mC26DB8DB3DB591832564436B0993F6AD14CDF7F3,
	Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C,
	Physics_SphereCastNonAlloc_mB423581F30D2DCB6A7B62F9E9366C7C82D8A83C4,
	Physics_SphereCastNonAlloc_m942717E0D3B43A44253447354FC32BA148EBC105,
	Physics_SphereCastNonAlloc_mBDD95C1C9B031C2BE462D1BFBB565277C77EDC22,
	Physics_SphereCastNonAlloc_mAB717B15E509BADDCA42B06EB4407FBADACA04DE,
	Physics_SphereCastNonAlloc_m6466EC0F9999A34AB63EDBD74F1CAB3010AF7EF6,
	Physics_SphereCastNonAlloc_m6DB44E22CBF5296F20BC68942B64F8D4F2866D28,
	Physics_SphereCastNonAlloc_m2B5EA904538007D958BE3361DE6B64E10B818F66,
	Physics_CheckCapsule_Internal_m1F9DFF4632C645B1B70DE9791826825F319B3D07,
	Physics_CheckCapsule_mFFAADF3C5B987AA3ACBF33377C9D292FA8680532,
	Physics_CheckCapsule_mB3EDF1AEBBD150949312D9656F8236502AB8B79A,
	Physics_CheckCapsule_mC07CE17079AAC02248518C81F8787055717CE836,
	Physics_CheckBox_Internal_mA1ECAF804AB96FB62D123032399F78884ADEA364,
	Physics_CheckBox_m70B2611C6652C7268F50122EFC439DCE05A488F8,
	Physics_CheckBox_mF035945E1FA93C042EDDDF431246B78C4A686F15,
	Physics_CheckBox_m08019B0562D47A876A970D21982FAEF9BD6EB06C,
	Physics_CheckBox_m5B2923A64067B62C69E076854221FA8C64F4230B,
	Physics_OverlapBox_Internal_m835436FA1701C08AC547331B8D594E2C86BB4031,
	Physics_OverlapBox_mC38B579DEFD0341FCAEF8B8EC8B1E37A2C12366D,
	Physics_OverlapBox_mFEE3011D8955ABBE0FEBB746CC9EA595C18254DE,
	Physics_OverlapBox_m400B65BA73CA857B619D11D8B98805AB6936EFB8,
	Physics_OverlapBox_m6F8E468A95A2F432413CF74ECE1533909565574F,
	Physics_OverlapBoxNonAlloc_m1D43D10CD88EF2D5440601D3CD14CA4EB449A295,
	Physics_OverlapBoxNonAlloc_m25AC09559AFBF4F4D96387A07FED94B282F2A35A,
	Physics_OverlapBoxNonAlloc_m3038DC8025CE15A90EED056EE324AD9E547BDB76,
	Physics_OverlapBoxNonAlloc_mFC72057341050361E8C66EF138A234EA919C3657,
	Physics_BoxCastNonAlloc_m88245841A55DC2FF1C2334AAFD4A7667698B4A52,
	Physics_BoxCastNonAlloc_m8BA4886C0C3FAE062167321FBC9939B95CEC73D3,
	Physics_BoxCastNonAlloc_mA3A665BA4F9DDF6C194615B452DAC832F90418C4,
	Physics_BoxCastNonAlloc_m57D783EA8900F68EA670380248C6C4EC7A41BB3E,
	Physics_BoxCastNonAlloc_m83CDA7F253A648CCF13D4A405251E68C62D38354,
	Physics_Internal_BoxCastAll_m634C7FCFEE4EFB38A0DF65E00C9F90F01BC9BD0D,
	Physics_BoxCastAll_m0EB3E1DC72AD4194B358FF3E0E92AFC2F515F84F,
	Physics_BoxCastAll_mB157BA7623230BB4EF87FD291A116284CB7DED67,
	Physics_BoxCastAll_m1A4C4A3BDD6FD43BE27047490A543E732FCEE2CC,
	Physics_BoxCastAll_m78F83BA3796FBEF28168C2FA658256F16ECF6BFC,
	Physics_BoxCastAll_mFFB862A7D86049DB378FD616FD0C8E69AC234800,
	Physics_OverlapCapsuleNonAlloc_mD13F4F0604878062489892A77D92A161681DB167,
	Physics_OverlapCapsuleNonAlloc_m9CFD945BB54EA98258099AB1D1157B2793509B8F,
	Physics_OverlapCapsuleNonAlloc_mA1E23131724F3E4031CD9054590C122C24CDD4A9,
	Physics_Internal_RebuildBroadphaseRegions_mB9288280395C40EA903BAF3C9E28D78B4D27D84A,
	Physics_RebuildBroadphaseRegions_m0350248177162D5D1B15A933F89278E3E20AEDD5,
	Physics_BakeMesh_m23D3FB52EDB6291399EA34CC49EF49A56FC3B639,
	Physics_BakeMesh_m6F7F69DE9FDC8AA7D14F2D6CDD63A8191D561B23,
	Physics_ResolveShapeToCollider_m5CEE97D6CD8C8618AF2A2124F1E846039FD574BE,
	Physics_ResolveActorToComponent_m6C2483CF83B43B27A172D28DA451CEF7EAF59F99,
	Physics_ResolveShapeToInstanceID_mCC6BAE29B43D7C56A1BF13898E613431F8F3ECCE,
	Physics_ResolveActorToInstanceID_mCB40A39B679357687EB9CF0DD71B14D098F269AD,
	Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220,
	Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725,
	Physics_TranslateTriangleIndex_m7D14955C4C4DB05471A260A3C6FBDABD4D633283,
	Physics_TranslateTriangleIndexFromID_m2DA314CCC15A77C895575D7B638CF3B56A75FEFA,
	Physics_IsShapeTrigger_m9AE8E1A88316CADDFBDBEA39D19580DE386EA2BB,
	Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144,
	Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590,
	Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7,
	Physics_GetActorLinearVelocity_mF2D174447DB131E509590110B33A4583EEC90FB4,
	Physics_GetActorAngularVelocity_m21F221C65E2262D5C5462773ED4ADED608957070,
	Physics_get_minPenetrationForPenalty_m65793A390C85B80015B235EF0497A87B66BE614A,
	Physics_set_minPenetrationForPenalty_mF12BDE8DE79A932574F6F05E7E122B49EF86453A,
	Physics_get_bounceTreshold_m2B29D00EFE8FCC201E9E89F3150C2640D25988AB,
	Physics_set_bounceTreshold_m4D8DD3F66CDE804D483673364FF6AD5CA956FCF4,
	Physics_get_sleepVelocity_mC5C3AEFE860F19CF50AF640CCA1E6F0D9C2A5151,
	Physics_set_sleepVelocity_mDB4C60E58CA8DA5D30A6F68DADF4A11E2CBAD7B7,
	Physics_get_sleepAngularVelocity_m0F25506AA33B0BF09FE12F8CF1E20536F4DB8CC3,
	Physics_set_sleepAngularVelocity_m44D3BAE42D07060D4CE29DB8A7A3900F932742CA,
	Physics_get_maxAngularVelocity_m1AFDE8E8D99BBA667F322F8FC3E4AC72D42FB679,
	Physics_set_maxAngularVelocity_m96F30B736B7ECA6A49BD6CF15404800EDFECCF1B,
	Physics_get_solverIterationCount_mDF3E3F60CEDB8028C0838049CE39A81C5FA3C73B,
	Physics_set_solverIterationCount_m929386814D4451EFA8E71DC3486FD3E24CF2F3CD,
	Physics_get_solverVelocityIterationCount_mA5A0E38663E66B1C2974D9EAC35763F1A9F78957,
	Physics_set_solverVelocityIterationCount_mE9F732977CAB74DB0BCF20F03039ADC91547B334,
	Physics_get_penetrationPenaltyForce_mE6482591C7751C425773443C08E2B39E22CF0EF3,
	Physics_set_penetrationPenaltyForce_mAFD75C9AFD9B2DD9507C2707EA0571AFBF160BC6,
	Physics_get_autoSimulation_m6D875546CE30125226E7E92318F2342F4AD57F83,
	Physics_set_autoSimulation_m1689299C0328790F6C350B40B53D092DA033389B,
	Physics_add_ContactEvent_mC17FF0C5F506FBE965F00FEDC0F73146DF7E036C,
	Physics_remove_ContactEvent_m42E318EA4333EFF8E6B001FA58ED572361DB6269,
	Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A,
	Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D,
	Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E,
	Physics__ctor_m84E8B98DEF92CF55210651857A7E9E841BBF4658,
	Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A,
	Physics_get_gravity_Injected_mBB1051A73AE88880090895AB007FEF35965E192E,
	Physics_set_gravity_Injected_m49F0D978D86877DB428952902EDAD3EF0F759DE7,
	Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6,
	Physics_Internal_RaycastAll_Injected_mAFAA47E2224DEA0ABF1A2188A969E7A663E50C92,
	Physics_Query_CapsuleCastAll_Injected_m7F070069C5BD11521A5428C730BED632FBDC50E3,
	Physics_Query_SphereCastAll_Injected_mE7002462EB2AE09CBF7E5B78E504C0432F528B3C,
	Physics_OverlapCapsule_Internal_Injected_mBC843BBCF8DFBA4E90888001E51F76FA83B8C147,
	Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B,
	Physics_Simulate_Internal_Injected_mB41A10BBFCEB591F87272F587D91CAAFB41EBD28,
	Physics_InterpolateBodies_Internal_Injected_m8C67ABAA35464AD50A7FF3BF9930C68EE341D0CC,
	Physics_ResetInterpolationPoses_Internal_Injected_mB01B48C493C73065EF880E49F0D6F7B274CF49B9,
	Physics_Query_ComputePenetration_Injected_mDDA9AC3B6EC05A824EE608809BD11266E4D529B2,
	Physics_Query_ClosestPoint_Injected_mDB84C796000699B2CF45B2F054EDBE652371C975,
	Physics_get_clothGravity_Injected_m1F422A9A9EAFB567CE66F872BA47FAE326B5CB9F,
	Physics_set_clothGravity_Injected_mF86D577C95D4C5B8EE12B4BADB7F540560478AA1,
	Physics_CheckSphere_Internal_Injected_m2ACFB56AF137AC31796C00809EBA6D55FA8BBD9A,
	Physics_CheckCapsule_Internal_Injected_m30EA1EC82CC5199CD410611675623963C4B0F220,
	Physics_CheckBox_Internal_Injected_m0EE51B035C17AE9C4D4FB039402F030B2B2EA6F4,
	Physics_OverlapBox_Internal_Injected_m32F1F9FC2EF4F6F47296862932717FCFD1DC3C21,
	Physics_Internal_BoxCastAll_Injected_m4C3680663C48199A818151AD9C17660742DCC5C6,
	Physics_Internal_RebuildBroadphaseRegions_Injected_m8283979760DA6B38044A12A07D53BA089E1B7BCF,
	Physics_GetActorLinearVelocity_Injected_m94D0924F5A6808D430697D4A821D832BA27241E6,
	Physics_GetActorAngularVelocity_Injected_m1C3E8F4AAB09128D5BA0E3E0E8EBE92BFBB4552C,
	ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE,
	ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769,
	ContactEventDelegate_BeginInvoke_mE57713922B33734B0F0DFFEADC0882DE872DE76D,
	ContactEventDelegate_EndInvoke_m1DE073679CEFD192878825FDCE259EBACC7E5240,
	ModifiableContactPair_get_colliderInstanceID_m555E31D2818CEDAC9D58538314D80701EDED2C9F,
	ModifiableContactPair_get_otherColliderInstanceID_m92593BF590D5F14345E4DA5AADF9D61D0C55D363,
	ModifiableContactPair_get_bodyInstanceID_m8B56E95EE29C2957E3166B3616B3E02AE8176D3B,
	ModifiableContactPair_get_otherBodyInstanceID_mE275D82AE73ED679018A57A87A04656598A587AF,
	ModifiableContactPair_get_bodyVelocity_mE291E12A45BF3D11E31252D5D0F48CFE9979878B,
	ModifiableContactPair_get_bodyAngularVelocity_mDE015C32239E67B14D5F0E74D6F0C6D1A12F2D97,
	ModifiableContactPair_get_otherBodyVelocity_m08C3C21AB9B5FB55EBE3BE3FD34EA10D02830F46,
	ModifiableContactPair_get_otherBodyAngularVelocity_mFEE2BB3A0CBAC44D35EDFE528810B1EF7779587F,
	ModifiableContactPair_get_contactCount_mE6A959F3C3DAC88580404AE830E666A74AC9DAB7,
	ModifiableContactPair_get_massProperties_m6E657FD7D82285D3993FF9085ABBCC4F0CF169E8,
	ModifiableContactPair_set_massProperties_mD53EC6724C3F093A3C4C08BCCC1815A858B628DE,
	ModifiableContactPair_GetPoint_mAD9A2FCD25D217AF868CDE3A8F3C6977F8D1B2F6,
	ModifiableContactPair_SetPoint_m3CD65095DE1222A9B7F1A57B4BB91A5B2ADE043A,
	ModifiableContactPair_GetNormal_m6C0A9110EFC0EF0E8485D9A1A63ED91A7347B681,
	ModifiableContactPair_SetNormal_m1D65EB3CA8B5BA26FDB3565CA9933DB5EE2EE959,
	ModifiableContactPair_GetSeparation_m5BE2C0DEBD531977AD9C56632F4F9E7CD157C9C7,
	ModifiableContactPair_SetSeparation_m9D69A9DF8A5F726E24C7224549944B54EED4D48F,
	ModifiableContactPair_GetTargetVelocity_m92DBB0CECE637554C12BCC9ECC6CCEFE1124648E,
	ModifiableContactPair_SetTargetVelocity_mA07021905C401183F59C7D3AB9B28A0E0EFBD7FB,
	ModifiableContactPair_GetBounciness_m42DF58A5A3BEB6E55836F4E91CC210E01D5101F6,
	ModifiableContactPair_SetBounciness_m5FFB3F3B7AA6BBD143E423570DAFFA0F4F86F74B,
	ModifiableContactPair_GetStaticFriction_m836099A4AED9C8519C9D977F30C3CAD83B069D16,
	ModifiableContactPair_SetStaticFriction_m258ADDF7FB804005D5A008118E341F7FD27937C2,
	ModifiableContactPair_GetDynamicFriction_mB4975A0337DEFF0DE340F3097E8E4BE8754FA9FE,
	ModifiableContactPair_SetDynamicFriction_m80A5A32EC1E1232F2814760D4D244D03E63CE362,
	ModifiableContactPair_GetMaxImpulse_m887290F5A2A460EE2F7A937D416A1819E280FA63,
	ModifiableContactPair_SetMaxImpulse_mBAEBD045C31CD54B65389D988B51163C656AE77A,
	ModifiableContactPair_IgnoreContact_m041CE733E6A9425E8F1EE1278DBC43A90B0226A8,
	ModifiableContactPair_GetFaceIndex_mCD63E169BF58DC041F1C14C732F24056E74B3461,
	ModifiableContactPair_GetContact_mEE629C8AC75FBD7A833BD9594A783D5A5B02BEFA,
	ModifiableContactPair_GetContactPatch_mFEA78CD88438A9D61844A643460C4BD8AB0FBC8F,
	PhysicMaterial__ctor_mD8ECF21D92EBF6A8C5517E7EB4D2D089B4F78D3E,
	PhysicMaterial__ctor_m78BA71B067808944CAC6214299A5E6BC85691F4E,
	PhysicMaterial_Internal_CreateDynamicsMaterial_m7A2577629C56F741F3B03B7E859611A20241F3C1,
	PhysicMaterial_get_bounciness_m9AA6C5656A9643A87B9D25304A9C8269D179CE8F,
	PhysicMaterial_set_bounciness_m99D8D24F76D60306CC4CFE38AD43BF240F84FDF9,
	PhysicMaterial_get_dynamicFriction_m3C420FDF958DD1AB1FC8CC78B102BCD3ECE44841,
	PhysicMaterial_set_dynamicFriction_mF41FC9F0BB5E70CF1AD4322FE67745AD612D7197,
	PhysicMaterial_get_staticFriction_m1E17EA04646C5FCC720E82BE3A430324D0494AFF,
	PhysicMaterial_set_staticFriction_m737457B7A2346BFB5D05BC7322F2A49823243011,
	PhysicMaterial_get_frictionCombine_m09915419D3DD57DF49A64B91B6E24AF9A1208707,
	PhysicMaterial_set_frictionCombine_m4F81ED0AC04BF634B0ACB33629CDB16C2ECBD28D,
	PhysicMaterial_get_bounceCombine_m47A9B9E71B854010CD3DC26B0136D33265A00E9A,
	PhysicMaterial_set_bounceCombine_m64DBF7D0F9C447DD5E0D19A6A24F0F5945C0BB1D,
	PhysicMaterial_get_bouncyness_m533C3C94A7D3D8B7A2F7DD3F984CDAE325470560,
	PhysicMaterial_set_bouncyness_m10DE2EF2C3A4D0D3A018FD3F61DA0FEA47E62E84,
	PhysicMaterial_get_frictionDirection2_m22D245F6D4DC788EC6E90A615542C63097AC7A39,
	PhysicMaterial_set_frictionDirection2_m24790B524E96C40E943EAAF4688C04114FE4F980,
	PhysicMaterial_get_dynamicFriction2_mB5644B2890AF7466053448FA400641C6C048976A,
	PhysicMaterial_set_dynamicFriction2_m4BA8BD2154E6274BA8097D996D583147B4C21884,
	PhysicMaterial_get_staticFriction2_mC41921B2D2D9CF155D373D5E337D41EA81CBA372,
	PhysicMaterial_set_staticFriction2_mCB4AC98068B7CA1B0475652C3826CC3186811CEA,
	PhysicMaterial_get_frictionDirection_m69A3D56E069115708A1B27C01E3E06D92451E8C0,
	PhysicMaterial_set_frictionDirection_mB04492F3A9CCE465063053690A177E891A7ACA8C,
	RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D,
	RaycastHit_get_colliderInstanceID_m4CEBF5D185F207B1F958A93EA62AF35BE889D758,
	RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39,
	RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11,
	RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5,
	RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B,
	RaycastHit_get_barycentricCoordinate_m15E866896213623E3E49B54F4273E343A50B1797,
	RaycastHit_set_barycentricCoordinate_m97B7F8D06D728E9881527E1C1AA4FCCADEEC4C64,
	RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78,
	RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC,
	RaycastHit_get_triangleIndex_mA363EA340DC5E202DA8E9AC6DF7CCFA20D6EF72A,
	RaycastHit_CalculateRaycastTexCoord_mC614773633D8C01BC9143536C0AAE22F8D575806,
	RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D,
	RaycastHit_get_textureCoord2_m93F06D5875AE4C8EBD21B9E184CF4FE3117EF704,
	RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155,
	RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005,
	RaycastHit_get_articulationBody_m19487EF6AFD5B39F682254E2EB47E3313C24357A,
	RaycastHit_get_lightmapCoord_mDA52EAAC7C3E7170AFDE13DE7183E6CDD91D7BEE,
	RaycastHit_get_textureCoord1_m62351A7A51BB86A5614D6B9BE49EE9A8DEF215AE,
	RaycastHit_CalculateRaycastTexCoord_Injected_m5BE3F0E10ADADB27EE315FFDDD4A99493B70AE4C,
	Rigidbody_get_velocity_mAE331303E7214402C93E2183D0AA1198F425F843,
	Rigidbody_set_velocity_mE4031DF1C2C1CCE889F2AC9D8871D83795BB0D62,
	Rigidbody_get_angularVelocity_m4EACCFCF15CA441CCD53B24322C2E7B8EEBDF6A8,
	Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0,
	Rigidbody_get_drag_m03B87FE60D5ABB7B937993112982C75080929D54,
	Rigidbody_set_drag_m9E9F375A26A8F3D1AABCEB015E41696F39088EE0,
	Rigidbody_get_angularDrag_m457FD99D051229084E77422FA669454E1B58AB4D,
	Rigidbody_set_angularDrag_m4193B04EEFCA831DB99E29E98F778957557F130C,
	Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054,
	Rigidbody_set_mass_mC7F886DEDB57C742A16F8B6B779F69AFE164CA4B,
	Rigidbody_SetDensity_mD76C832D898ABD95316127E71305D33CC16FD16D,
	Rigidbody_get_useGravity_mBDA227BDCB0F9A81B61A6592929EE43EDDEE7D16,
	Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389,
	Rigidbody_get_maxDepenetrationVelocity_m432BA40064E758F1189E086EAB0244083033C597,
	Rigidbody_set_maxDepenetrationVelocity_mEE787E12E070438903558B0C79DDD98E3A5CFFD7,
	Rigidbody_get_isKinematic_mC20906CA5A89983DE06EAC6E3AFC5BC012F90CA1,
	Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED,
	Rigidbody_get_freezeRotation_mF698D98D5E43E86D2577B2496134706627EF7183,
	Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC,
	Rigidbody_get_constraints_mAD5C536F3329399A4F32C05C3BC52821737C9AE1,
	Rigidbody_set_constraints_mE81BF0DAEB980E320538231E092CA4663885A9A3,
	Rigidbody_get_collisionDetectionMode_m5A18E2DE70F4C37841300A34A183FF3ADE01D943,
	Rigidbody_set_collisionDetectionMode_m70A22E9878027BF6D3D7E851A43A8E32B8E02343,
	Rigidbody_get_automaticCenterOfMass_m3587BB61F16B88B31D31C55C8B9960051C4D799E,
	Rigidbody_set_automaticCenterOfMass_mD131AE691EACA23A9FEFF45867E1F8C346AEFAB6,
	Rigidbody_get_centerOfMass_mA66BE4DE0469545EBCF49A66EE4FDD3A5D0ADF91,
	Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188,
	Rigidbody_get_worldCenterOfMass_mFDEE86A44A9D44DC490B283C636CE770672F8FCE,
	Rigidbody_get_automaticInertiaTensor_m62BE477CFA29B626489BD68C43EAA9820F81F10B,
	Rigidbody_set_automaticInertiaTensor_mFDEAC3819314D7508E98902978BB2CFA7104FDC5,
	Rigidbody_get_inertiaTensorRotation_m5B0FCE37D5C05D61375B954177B2EC51AF2E0E6F,
	Rigidbody_set_inertiaTensorRotation_m732666F7ACD440F5472A04AAEF1545D12C63450C,
	Rigidbody_get_inertiaTensor_mF57CE415F0DF4DBF17C6D0B7B006B3E1E6289780,
	Rigidbody_set_inertiaTensor_m68E7B9842A8125237E1DEE5251840DF2D4DBB8C9,
	Rigidbody_get_detectCollisions_mA2AF681B49EF1820F2B30EF86B1082FC661C9A51,
	Rigidbody_set_detectCollisions_m42A50DFACA7709DA1F87BCB9DC0BDA00720C80CF,
	Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691,
	Rigidbody_set_position_mA15BE12B8D82220E8CA90A0F0CBFB206FE81B41C,
	Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1,
	Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D,
	Rigidbody_get_interpolation_mE508FC846FB031C118464637507C004408A32696,
	Rigidbody_set_interpolation_mC7D39114A7AC6ED0AB2B40FECA4E2ED3C1D7603C,
	Rigidbody_get_solverIterations_m8FCD8D9D946FD23209516CD40D5A6A39B86C0DAA,
	Rigidbody_set_solverIterations_m533625CFDF6CB3E9412AD2ACD3FA13A6636A401C,
	Rigidbody_get_sleepThreshold_mC6B5C703DBA60ED8FEA8519ED0D62CC828D03399,
	Rigidbody_set_sleepThreshold_m5180E11C2D6A401352863FB66812C683C663B220,
	Rigidbody_get_maxAngularVelocity_mE04AD81F38A944B9123523422A0248FABE482FF4,
	Rigidbody_set_maxAngularVelocity_m26E48B1DC6B9F8DBB81EE0681ABEB3AB255FC3F6,
	Rigidbody_get_maxLinearVelocity_m7FE7BC362D0F2AA40C7D2965D53162553EFE7891,
	Rigidbody_set_maxLinearVelocity_mED5CFF32D0FB795DF6219AAD871B625492245713,
	Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9,
	Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D,
	Rigidbody_Move_m48315B47C2BD90B45484029C59036CA8979C7E13,
	Rigidbody_Sleep_m9826BDFCF078DF00223011B3F0FA7F4894F8F4CA,
	Rigidbody_IsSleeping_m059CBAD60AA4A6CA666FE2EAD2D7A3B02269E43F,
	Rigidbody_WakeUp_m64CF3AFAAC3CBB5360947731C1F77F13CDB960AD,
	Rigidbody_ResetCenterOfMass_mA8315F8324A97410D77B811833DCC80BD4EB361D,
	Rigidbody_ResetInertiaTensor_m34020552CA2D42DEA3E01562641A9B292848BD01,
	Rigidbody_GetRelativePointVelocity_mFFB9A7105C436D0D759C6B20B3A206CB4F0942F1,
	Rigidbody_GetPointVelocity_m94324B9CDC28751DB27594ADE76FEAB5EC4EB1BD,
	Rigidbody_get_solverVelocityIterations_m8CF27193497E8A9553956AA0317BE9702B45964C,
	Rigidbody_set_solverVelocityIterations_m53C09CB42CA4DA944D52E012BAF8112719AD753B,
	Rigidbody_get_excludeLayers_m614A65C71A4E87F55BD1F3AC506866FC4F45F376,
	Rigidbody_set_excludeLayers_m9BA59762CD6C645D7E8208A5BFDFC839B951B904,
	Rigidbody_get_includeLayers_mA40DBB1AC488534DB9DE6D3B15382EF5BAC781A7,
	Rigidbody_set_includeLayers_mF5BA8F42B7335D2286E8AC897CA3C8CEAA240B4F,
	Rigidbody_GetAccumulatedForce_m60A0C4F5C07014DA033CA83FEAE3EBDF31CC73CB,
	Rigidbody_GetAccumulatedForce_mBA42828F11EEED7C87F57A695E2848CDF648A18A,
	Rigidbody_GetAccumulatedTorque_m28E12D3B6B6BD633594B03AA6E261B80D5647C17,
	Rigidbody_GetAccumulatedTorque_m9DED96F3D8C591BDCD15269AC17774528E276CBD,
	Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC,
	Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198,
	Rigidbody_AddForce_m264F2851A456AA18D4F04B21AF23814E61A39B75,
	Rigidbody_AddForce_mFD97FC9DA828C1952D46D28C50B1D994B19895F6,
	Rigidbody_AddRelativeForce_mAF5EA6C0A2417A4C72AF31538D66EB9612CB6543,
	Rigidbody_AddRelativeForce_m16FBD1F3B609D21930E73B6E9474D8917D3918E6,
	Rigidbody_AddRelativeForce_mB69BDA6ADD9BD66EF1CB919B28BFEE4492F1D4E9,
	Rigidbody_AddRelativeForce_m332A7C65AC7DAFF1AB072237AD0C98DF43CD1FC6,
	Rigidbody_AddTorque_m7922F76C73DACF9E1610D72726C01709C14F0937,
	Rigidbody_AddTorque_m39C767D6CD12B2D12D575E2B469CB5565BFA30B6,
	Rigidbody_AddTorque_mF99DCFAB779A5CEDE19D79926C39123B631B8F44,
	Rigidbody_AddTorque_mC55713BD82CB5EFB0D6C705B98F030717EF50CA1,
	Rigidbody_AddRelativeTorque_m98DD3E53803D7E5BA726CC98FBFA58C2350F2233,
	Rigidbody_AddRelativeTorque_m8A4883737B7F8BDC0B25144986F74C4B9F789311,
	Rigidbody_AddRelativeTorque_m117DF8F7B92DECCB2C6A57F3C6747E5237FEC89D,
	Rigidbody_AddRelativeTorque_mF1533E306ACDDBB49564E7761A887897966F4705,
	Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C,
	Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965,
	Rigidbody_AddExplosionForce_mE4673F6D1DA0C206DA79659E9005A0F067348402,
	Rigidbody_AddExplosionForce_mD8FF6CAA6FF6749259FB95762B2A521CF8483163,
	Rigidbody_AddExplosionForce_mD36F7D864F32F22DA1783D20F6E9563A9C51DFA1,
	Rigidbody_Internal_ClosestPointOnBounds_m62E094281730FC5CADB1A9DEEE9549B806303D43,
	Rigidbody_ClosestPointOnBounds_m2DF6444FD5993A0A1B703874754DE9FBA9F95309,
	Rigidbody_SweepTest_mB87EFA8A4215626EEFB518D3B5F54D20ED913ED4,
	Rigidbody_SweepTest_mF7D04E7199069664EC2FB2418F3FFB220CDE6BF8,
	Rigidbody_SweepTest_m2A2783DE4C9C557F6AB58514B02DCD5209A0FA60,
	Rigidbody_SweepTest_mD1FED67424FB5668B9CA3B8973B241CB9F51BD1B,
	Rigidbody_Internal_SweepTestAll_m31D82D649A42BDC06DB291BD6076F289B87F8B89,
	Rigidbody_SweepTestAll_m3272031A076B4C455517A6F29267850DC638B8EE,
	Rigidbody_SweepTestAll_m09CDDB81ADE48D70CF94932638894614E4D775EC,
	Rigidbody_SweepTestAll_m7F8BB2FFCEA5828ACD9FE20786D00278B603C8F6,
	Rigidbody_get_sleepVelocity_mF86D9EC45EE420A81BF74D09922262BD73E204A3,
	Rigidbody_set_sleepVelocity_mE130340D14FF37D7692B40BD111430DA0D774095,
	Rigidbody_get_sleepAngularVelocity_mA2C70CAA68DB5C14B4978AEE009C25D701C42DAD,
	Rigidbody_set_sleepAngularVelocity_m706F4521A609F3339B00D3D806D527A1EBB298DB,
	Rigidbody_SetMaxAngularVelocity_m98005580752C2E4E7127D5A9E0756D5CCC082FE3,
	Rigidbody_get_useConeFriction_m57C5137300A7C6B9F68CDFFBB2E4D949DE8D834A,
	Rigidbody_set_useConeFriction_mA894A8098790FC07B14F0ADA3A4ED3FAF068DCA9,
	Rigidbody_get_solverIterationCount_m9BA2DF4EC518B59C59B818FB23F8AF766034CF48,
	Rigidbody_set_solverIterationCount_mA7DA2BD97FCA6C235AB327BCF67B9AE580F80811,
	Rigidbody_get_solverVelocityIterationCount_mE7C2B55566DC755C57FEA7BF3CE0C74E49045737,
	Rigidbody_set_solverVelocityIterationCount_m3A88732F1B4776E8B66B7B07001A3FF5CD5A2474,
	Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07,
	Rigidbody_get_velocity_Injected_mFD6FCA2857D9953AA953DB9AAF26A88CA881171C,
	Rigidbody_set_velocity_Injected_m41B399E90D6AA49BABD3C178B3183AD3BBB4EAC4,
	Rigidbody_get_angularVelocity_Injected_m1F0D38AD14491E05E18C0E2C043F777FADC588BC,
	Rigidbody_set_angularVelocity_Injected_mD7EA47CB618918BD45985951E5A5388853975E68,
	Rigidbody_get_centerOfMass_Injected_m1E6C506D04F8D066FAD03C571FA926E8FACB1518,
	Rigidbody_set_centerOfMass_Injected_mF33B1D20FF435377B48235CDD322478131AE4B1B,
	Rigidbody_get_worldCenterOfMass_Injected_m0856EB16F154FAB8C8A1245555039D1EE055D703,
	Rigidbody_get_inertiaTensorRotation_Injected_mED822CADC19012C92577BD31FE552E057A1DD4E7,
	Rigidbody_set_inertiaTensorRotation_Injected_m8BC9257C5251E9ED85074885E93EFBB2BDC36809,
	Rigidbody_get_inertiaTensor_Injected_m8F39AA878C625E9A5CDCD6804B70683BA17B6FB9,
	Rigidbody_set_inertiaTensor_Injected_m1172388EC4D674FF91B090F0A58707659A5032AF,
	Rigidbody_get_position_Injected_m12A715C52CD3C7F66125950D7AB6ECFCF4336626,
	Rigidbody_set_position_Injected_m8EB4DEECAE5A3D841A71B015807FF0820C3A361B,
	Rigidbody_get_rotation_Injected_m38431B37B78B4DF59B59F9CB7E609820430051F8,
	Rigidbody_set_rotation_Injected_mDE5B37E97D8FE16D9AD8FC6B9213A106B28589D7,
	Rigidbody_MovePosition_Injected_mF2CDF14960A920DCDDEAFA49A7E066A2FF021E37,
	Rigidbody_MoveRotation_Injected_m75B6A86B8BE8D68714CA5356DDCC11D24B96B505,
	Rigidbody_Move_Injected_mB7775F4369189F1934A6ACC1D13D56EDF5220A59,
	Rigidbody_GetRelativePointVelocity_Injected_m685F932DF242C8ECFD854D336D9973A3CB8AFC7D,
	Rigidbody_GetPointVelocity_Injected_m343FC8424FDE36AE563D07BC3B7506CF94E3E4A4,
	Rigidbody_get_excludeLayers_Injected_m4442C59992FD0B190F2A30D2B13905E2E97CC2D6,
	Rigidbody_set_excludeLayers_Injected_m44BFF6F35887A375476903D5FE8AADE6C3250F08,
	Rigidbody_get_includeLayers_Injected_mE0E5307E80BD2E11B616ED56FA7FC270FF21C550,
	Rigidbody_set_includeLayers_Injected_mE47FEB4028599B3BCE8E9E215D3A8E01D7B5C9EF,
	Rigidbody_GetAccumulatedForce_Injected_m202D7B23C05967EFD6C5EEB9A2D3A73090FAA7A9,
	Rigidbody_GetAccumulatedTorque_Injected_m36CEA5E34A3E16540D869C1A094C541702C182BA,
	Rigidbody_AddForce_Injected_m094E54DEA6CEAEA340F053D077CDF0753900F48E,
	Rigidbody_AddRelativeForce_Injected_m9BD7D9D36B62C306CA7D30CEB6BDFCDEDDFF9DF3,
	Rigidbody_AddTorque_Injected_m22FC0760ED37AE38443DB926B2A7D87470A63CF2,
	Rigidbody_AddRelativeTorque_Injected_m4FCDB6F9DA07C7C64925943F79670E967B1B8FA6,
	Rigidbody_AddForceAtPosition_Injected_m24F286471C9928629A50A5C1A0DF22698172438C,
	Rigidbody_AddExplosionForce_Injected_mB02BEE3B033E215880374706864990CEA12B17D7,
	Rigidbody_Internal_ClosestPointOnBounds_Injected_m793A030BF3723440E71E29A9D29CB80607FE0E2E,
	Rigidbody_SweepTest_Injected_mD66598DD6B9A89F45A9A4E4E0BD16E46FC42D0CF,
	Rigidbody_Internal_SweepTestAll_Injected_m71D3923B2FCF68ADDC4700A03E6B004BFD3660E4,
	Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B,
	Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70,
	Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD,
	Collider_get_attachedArticulationBody_mBE9BB600DD90D2966D969171663D41F85FF86D38,
	Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87,
	Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78,
	Collider_get_contactOffset_m3970ADEC658E6C854A59B1645DC2D5799F7DF0D7,
	Collider_set_contactOffset_mEDA8D778F641338733D140E76FCA0D6B29203B52,
	Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB,
	Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4,
	Collider_get_hasModifiableContacts_m2C74D85EE90F563BC873960AB2527D496675B6C1,
	Collider_set_hasModifiableContacts_m7F64882EF15D19F5D79A15E1BEBBAAC2598598C0,
	Collider_get_providesContacts_m5851C54B60993E4EA4E37FF4601D486A181085DF,
	Collider_set_providesContacts_m9ADCCC236AAC274C6A4411AAD3EA2BDAFED0A502,
	Collider_get_layerOverridePriority_mE8C8D674702E243B3F9CD32476FAC1B83D99EDF1,
	Collider_set_layerOverridePriority_m5E5DEAEAD0D246B30B40F5EE25D443A2EE471497,
	Collider_get_excludeLayers_m2535780746E624E84F10034B003B6327B1038A34,
	Collider_set_excludeLayers_mA1C66C269BE2E8B22D39D20287D93D0EF1051C38,
	Collider_get_includeLayers_m037010C05FC53DA09364B3AAD2CC16998A9393DA,
	Collider_set_includeLayers_m48BF564FEE364CA2AD7FBDC98A584977F5140AC1,
	Collider_get_sharedMaterial_m238C1D9D4B2B1F02876C610E049C7A5ECCDC07AC,
	Collider_set_sharedMaterial_m2AC21AB939A377ABACF8282CDC52EE61B54107ED,
	Collider_get_material_m2068219450550334496C669378DBA3A03CE68878,
	Collider_set_material_mE6FB0AA80863EA6746CD99606C90DB97DBBC4476,
	Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C,
	Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938,
	Collider_Internal_ClosestPointOnBounds_m87BD13A92D4239E7BA08C0417197DFC8D4E5DB7E,
	Collider_ClosestPointOnBounds_mBF2F0C0E76C5F11AED801931D780823A94630952,
	Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA,
	Collider_ClosestPoint_Injected_m4E218A16FABAA4615270B9CD82DC66E130AAFE77,
	Collider_get_bounds_Injected_m1BDB8DBC0BC2BFC51D4A185C494EDB0997B93A43,
	Collider_get_excludeLayers_Injected_m165282A916A5FF57AC93C00482633838A1A64BF1,
	Collider_set_excludeLayers_Injected_m6023B61472839ACA42369799E0D5FAF1F2B602F5,
	Collider_get_includeLayers_Injected_mAA7DBED3E68AE14023B3A3D64D0E0BC3191327B8,
	Collider_set_includeLayers_Injected_m02EC14FF9417656176314B4564BCA1B3B61AC9BA,
	Collider_Raycast_Injected_mAFAD355B658765116985B737217587C68BF257A3,
	Collider_Internal_ClosestPointOnBounds_Injected_mC77F9140817CA77A46BB8672B6DD388CC3BD2B7E,
	CharacterController_SimpleMove_mE32A48D439878B3241E8456F8EBFAAE264BD705C,
	CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4,
	CharacterController_get_velocity_mD385DA9478B1FDCB0E9B2D2CA3647B85F1928C8C,
	CharacterController_get_isGrounded_m548072EC190878925C0F97595B6C307714EFDD67,
	CharacterController_get_collisionFlags_m2C5CBA83541CB45D0F6B620FB56BF86F8477BD1E,
	CharacterController_get_radius_mA7095C2FFBA77AE532CD9B219D506D871E86BFC5,
	CharacterController_set_radius_m9F783918C4DF101C705DA1280F2D6143FC622B20,
	CharacterController_get_height_m18EC4D93673A225648DCB302BAB4F8A5FE4A20AF,
	CharacterController_set_height_m7F8FCAFE75439842BAC1FFA1E302EFD812D170FB,
	CharacterController_get_center_mDF0F4D399A63BF5A2F5366CB71CCF4148DB08591,
	CharacterController_set_center_mF22160684B1FB453417D5457B14FEF437B5646EB,
	CharacterController_get_slopeLimit_m632C114E0C6DDA5877CFD02C28435AD1EBD39A45,
	CharacterController_set_slopeLimit_mB77021F6C5D049ED76EEBB2556562BF2BBD6C1E0,
	CharacterController_get_stepOffset_mFE2236D76CBF06B5F5A8E6C0AB2E75E0D97F8621,
	CharacterController_set_stepOffset_mF609F26572304DF66AE2E17AA524DC8F51C1BE68,
	CharacterController_get_skinWidth_mF22F34BB1F1824D67171FCF5F187F5585749A5DA,
	CharacterController_set_skinWidth_m195DD3EEDE89C015239FF100E9BDE8DEA806EB07,
	CharacterController_get_minMoveDistance_m540B49CB50E1C072F00B11776877AF2538446EF3,
	CharacterController_set_minMoveDistance_m690AB5F824C765B3F6994EA4E79C5AB67A613257,
	CharacterController_get_detectCollisions_m1D4FCA7396C65E243E3BAED09BC9449E6E1A8098,
	CharacterController_set_detectCollisions_m7B7E7D67C38E7DD03DF5254085878E0A88C2921C,
	CharacterController_get_enableOverlapRecovery_mE38F38B269B9D25D46084E6EF16709EC4D5B2637,
	CharacterController_set_enableOverlapRecovery_mE989D42A34060F43D45079D49404FEF12AD0B004,
	CharacterController__ctor_m6A906ADB773BC0AE534C9348A56747D931B194B3,
	CharacterController_SimpleMove_Injected_m0E3E4E6152A40139CE3B8B2FA65FF941FD976656,
	CharacterController_Move_Injected_m7F25C33CF948858A8D5822EF73FAE7A16AE65C86,
	CharacterController_get_velocity_Injected_m30E51269762EE6648DA19F595925C4C04394B316,
	CharacterController_get_center_Injected_m9A353DC6672559EAAFD79D914B0541D72A478314,
	CharacterController_set_center_Injected_m2685DB01CE4D0DE542CD29E70B97D84A0F659980,
	MeshCollider_get_sharedMesh_mFB4B8534501C29930D2D3710D6D82E60093FA21E,
	MeshCollider_set_sharedMesh_m05F87B9AC04139285EBBCC159F77B51C6940E79C,
	MeshCollider_get_convex_m0C0F6D0798413D633814D307EC970F7752B3C9D1,
	MeshCollider_set_convex_m20482D687240D0921BA76B77983403E55A2E3CE1,
	MeshCollider_get_cookingOptions_mDB07DCA79AE90ED2C36E2F65188B17B7B85C0C9D,
	MeshCollider_set_cookingOptions_m76B9DE7A503EDD73499CFFB46F7ECD942B6C7A2C,
	MeshCollider_get_smoothSphereCollisions_m9F86496B849B9B811117DCC5B1F0EA2AED759B7C,
	MeshCollider_set_smoothSphereCollisions_mD7FCB938D340363D6AA5BA20B47991C00F2738B6,
	MeshCollider_get_skinWidth_m9A3AF93E599125E1869E3424A71978A23B8B0E1B,
	MeshCollider_set_skinWidth_mFA982E4BD157AA4C95919D5DCFDD4B4E95265A1D,
	MeshCollider_get_inflateMesh_mBC903DE0F569DCBB55BB7868139028ECB3E67883,
	MeshCollider_set_inflateMesh_mD3FA4C73FE0AF6F7BF2A32940F652B0BAC08927C,
	MeshCollider__ctor_m09FBF25616B6185F1CB93D7C520D5DD96584DBE6,
	CapsuleCollider_get_center_mC12CE0A66A1104CEB7D23F39596D0E45578419C2,
	CapsuleCollider_set_center_m242D92DAEF25887C6A87A0777E4E624C0A431A2E,
	CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5,
	CapsuleCollider_set_radius_mB301C0086FE0D251683512184B0F6DDE264BA985,
	CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24,
	CapsuleCollider_set_height_m5DAE3DC5AD851E30C5A29AC7A22F36BE1E205BBB,
	CapsuleCollider_get_direction_mE6D56B0990E3F2FACA983679C251949FE3FC6DFA,
	CapsuleCollider_set_direction_m3064DADA49F7F48DA8D62659B1D3545C83C553CB,
	CapsuleCollider_GetGlobalExtents_m7A78A4657CF24A0C663C26AAEF5836DFCE301227,
	CapsuleCollider_CalculateTransform_m4FA562D4CF84213611CAD693C7AD62EC3A8575AA,
	CapsuleCollider__ctor_m747BF5AAA591F27F50B0F9C21FB015811D8E71EA,
	CapsuleCollider_get_center_Injected_m1E3350EFCD134943AE30B4D76D1767AA080A4BD2,
	CapsuleCollider_set_center_Injected_m2C7FB04A185C5A1C8B3AFCFBB2923C8E6966C132,
	CapsuleCollider_GetGlobalExtents_Injected_m87BB04142308B622B2C350AF1677A5FA9DC47B08,
	CapsuleCollider_CalculateTransform_Injected_m4C62DC4336F9CF275D200E462DC6A63A16C343E3,
	BoxCollider_get_center_mC370C79F9FC9398D0DD080500FA2EE14FC6E36C7,
	BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0,
	BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E,
	BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A,
	BoxCollider_get_extents_m60D6ADAA8D5B8D4837B6DE12F0056486CD3B84FA,
	BoxCollider_set_extents_mFF13B4BC079B719C9DE61934E232E88A9DF650FF,
	BoxCollider__ctor_m7E069AACEC2B76129335D222FC8B8A63FD50656F,
	BoxCollider_get_center_Injected_m48EBE71F021C53D4AE4D9F21C16E5E1E11510096,
	BoxCollider_set_center_Injected_mF2FF6FB33F950F7329395C6EC97E843ACDA52D8E,
	BoxCollider_get_size_Injected_m4F20D7B3D8FB4360C9E2986FB6A8CC66ABC89511,
	BoxCollider_set_size_Injected_mFA7F15E94435D42A70D7C8543CEC7AF4F6D55343,
	SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E,
	SphereCollider_set_center_m83F6CC0056B491CD577B9AC08FA1E331074203D4,
	SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697,
	SphereCollider_set_radius_m6119FE18C6739B077AB17334B1B53984911017FF,
	SphereCollider__ctor_mA8570CBE8C0E74C607669DC4E0CCA6CB1E4CB200,
	SphereCollider_get_center_Injected_m26E71B48B49E3EF89A4DC523015F243A385CF0E9,
	SphereCollider_set_center_Injected_mC0B9DC26F53D573D5E6AB9B8206854129276B342,
	ConstantForce_get_force_m58EDF9940A27488741B1432D0389E508080A2696,
	ConstantForce_set_force_mA536A109656B5AA8565539CABB48608A8BA2E720,
	ConstantForce_get_relativeForce_mC5D6BB307F4BB3FF716347703E38E1A13C0A5CF9,
	ConstantForce_set_relativeForce_m59AD3ED0269E687843404920757675D8702C1C65,
	ConstantForce_get_torque_mD4227AF818CBAA90542D44E4F5DFDAE970263626,
	ConstantForce_set_torque_mDB6F474A63634F77A15DA14138390596FEB0B9CC,
	ConstantForce_get_relativeTorque_mEFB75FA1FA7C2D2801BB6A9F6B77DF20EC7E0C55,
	ConstantForce_set_relativeTorque_m453C5E55F084EBAEEED463367938B12393A64F65,
	ConstantForce__ctor_m0B4FBA845B21450D175CB7D8F50492C064A96231,
	ConstantForce_get_force_Injected_mA8DB9E19DBCEF11154F2745FF6E3B75983AB6D07,
	ConstantForce_set_force_Injected_m9B6787AE87FDFC05A44E037623DDA6A3946ECDAF,
	ConstantForce_get_relativeForce_Injected_mEE05499DEAD82B3B4C9287407DFD40C7F4965C59,
	ConstantForce_set_relativeForce_Injected_mD1A2089D933DF2B8212F6A743359FE169A077D1F,
	ConstantForce_get_torque_Injected_m7B984063EA78F5BB16C1FF1E9FDF00BFAD3E7065,
	ConstantForce_set_torque_Injected_m6A1243064472E8BE5114CE6698CADDF99317F254,
	ConstantForce_get_relativeTorque_Injected_mCB442EA0939252AAAD7000F42B616DAB64490BA6,
	ConstantForce_set_relativeTorque_Injected_mC70A5B1CA4430C6D0C46B59F60887DC77DBC5B47,
	Joint_get_connectedBody_mE39E0AC9869325CD018B9ADB383B6BE01D497B59,
	Joint_set_connectedBody_mE9E631476E9D4264E8DC0D6307146F5EB64D3ED4,
	Joint_get_connectedArticulationBody_m5307D444E4377F11A43509EBA03124FDB10AF56E,
	Joint_set_connectedArticulationBody_m04A871A2FF8B23746C0293604516B0720CAEC6D2,
	Joint_get_axis_mEDF8FE710E08CED9EA69A0369A075B77FF0BE79C,
	Joint_set_axis_m3C64D93F04DA043D6B02160F9034907BACC06800,
	Joint_get_anchor_m1CDF56CF0BD9773E9923D777EA24B2102DEDB79D,
	Joint_set_anchor_m89447EF25E0FC6DB9D22562BAF3BDA3E6D04029C,
	Joint_get_connectedAnchor_m455C8981D90A4A60DC5B290EBD1D9330A6649C18,
	Joint_set_connectedAnchor_m2A40C3C4FB583E9DBC020222A21F577C066D5D90,
	Joint_get_autoConfigureConnectedAnchor_m902904AD312FD282CB01AB7A25AB4153FDA567E3,
	Joint_set_autoConfigureConnectedAnchor_mF61D716174DE67CD94FF042881E9052357679E02,
	Joint_get_breakForce_mC3DCDF8DEA3B55EE997163BDDC192D9E9A3D1801,
	Joint_set_breakForce_m42F130D31EFC282FE0887A4E6A399F53EB3FB40F,
	Joint_get_breakTorque_m834D4485F2615DB8BB48DAFAB57E9BE6A361AA5C,
	Joint_set_breakTorque_mD76D069CDFCE726493BA790F19496FD8D08EFB04,
	Joint_get_enableCollision_m540A9E81771E2AA39C5D3254735FD8AE15AF5D0F,
	Joint_set_enableCollision_m73C4FF0B64FDBDEFEEDCD9F98D491906F7715080,
	Joint_get_enablePreprocessing_m887CDA242B52B356E81BFD410CC9C231618EB8A6,
	Joint_set_enablePreprocessing_m0970649287DC20C289E3AB775CBD1D65883B6F27,
	Joint_get_massScale_m8BD34AE43B5136D2E0AB657AD37A9A050838D4A6,
	Joint_set_massScale_m23111BDA5A8825E83069372785DBF56FCDAA4BC1,
	Joint_get_connectedMassScale_m2CB77BD65BACE520EBBD68456322098BC550C016,
	Joint_set_connectedMassScale_m6F7D8FEFAD6BFDC7177D9D06DBCCDCC288C4475D,
	Joint_GetCurrentForces_m67531F115C7C87F196D387A5DB3952277A0253C2,
	Joint_get_currentForce_m3870631DEC29CED9EC606C342F6951F8A6C72692,
	Joint_get_currentTorque_mD01736066D68B73B02FC683D6C7DB0E4A9924516,
	Joint__ctor_mAB8D99F295B9D152D8AB086364EF03B2AC9E9D01,
	Joint_get_axis_Injected_m85FD31C188CC3C7BC266AC7E5E1DD5B14E656C34,
	Joint_set_axis_Injected_m013FB85E34CB410702FC87EAB54B856CF180B505,
	Joint_get_anchor_Injected_mE20087A50431397C2C20E031DD98733425C2B445,
	Joint_set_anchor_Injected_m640F53862ECAB8D8163B378E54FB6E2194BB2AF7,
	Joint_get_connectedAnchor_Injected_mB370F52F87BB83B28A071E4342F132D2ACC9C365,
	Joint_set_connectedAnchor_Injected_mE4275DA473C8A61B37A1639E2352A09DCE2A4198,
	HingeJoint_get_motor_mF886F1CBD9EE4A89F41F0E8790DAB5E1FEA84F00,
	HingeJoint_set_motor_m9612545CFB6BCF58FE2AC5D49720190D0FDE3800,
	HingeJoint_get_limits_mD0E3FDF10D6AEF4D539711C0DE418C20930B6433,
	HingeJoint_set_limits_m5B9B968EF88FFC9ABA1777FFEFA13433F9BE08D8,
	HingeJoint_get_spring_m78151E226B2A39FA888F6367AF21D332214293D5,
	HingeJoint_set_spring_m93334DF56ED45D7D7CBEE051E459F70D6DC81E25,
	HingeJoint_get_useMotor_m1C329ACA3AFA9DD15AE4441D78E8AA99DE11CC95,
	HingeJoint_set_useMotor_m412445D348B1D6EB51D13FFE14498885FBE8D5B6,
	HingeJoint_get_useLimits_mB6505FE417A487B69756B9F9897B06C40AD5F362,
	HingeJoint_set_useLimits_mC47026C2282D590754EE177C992FF5C228908D39,
	HingeJoint_get_extendedLimits_mE837461AA2777194104F9137CB646D2831675712,
	HingeJoint_set_extendedLimits_mCD4FE8A16B9960ECB01FA175C97ECE7A0A777D7A,
	HingeJoint_get_useSpring_m239B2FBE0587B605008196463DC40881A3BC3779,
	HingeJoint_set_useSpring_m02AF2737DE4954BB71C2F68710835116BE3AA0D9,
	HingeJoint_get_velocity_mC9F0646F08D2177EED1B514C0C613C63D1DEF78D,
	HingeJoint_get_angle_mCCDAB7FAFB7A535562521C450A1A35393622DD6D,
	HingeJoint_get_useAcceleration_mF6D06CDC06EA6E98AABEA1E06E28CB8BBE6BB1C8,
	HingeJoint_set_useAcceleration_m09DCD14C7AAE83954969E4B0F9B01F38DF58F12B,
	HingeJoint__ctor_m688638163A59D626BE998B6B8196E8F2C0AAB667,
	HingeJoint_get_motor_Injected_m95030B920C62241B17A12EFBCB3C0772951A1B13,
	HingeJoint_set_motor_Injected_m9C0C3243E03D6A8652236CA47C6C24E43A16E966,
	HingeJoint_get_limits_Injected_m33E3633CAC19E9EBB0BCFC5D8A5CA7FBEC8A8E73,
	HingeJoint_set_limits_Injected_mF946129ADB63E27A95D9F71B5FFC54A0341AE78E,
	HingeJoint_get_spring_Injected_m8CB9B7B84B0D33472152688F5065433D14F7B002,
	HingeJoint_set_spring_Injected_mC8D22F2C103A70E0569A9D178CF66015FCB9BE03,
	SpringJoint_get_spring_mDD913018B3C62A81AAD9BEE33D57E2EDA3532D3E,
	SpringJoint_set_spring_m2F50B24336E3DEEF56551567821EC6FCAA4280FC,
	SpringJoint_get_damper_m01D06A59CFCC434B6C57953F8F8D4E31A3967099,
	SpringJoint_set_damper_m69DF6E010568BAD43F98684D0D3C54DEB31F01D7,
	SpringJoint_get_minDistance_mD67FCC62300CD40EFEB7643D4B483A6142B5F06F,
	SpringJoint_set_minDistance_m08AEB7E38CFB0A279F8387FAD8912BE9569A1E37,
	SpringJoint_get_maxDistance_mD9DF4A15CA0A006D029991B4E27B6343CD8BAD8E,
	SpringJoint_set_maxDistance_mC8329A6B1CD20296234A1B377376EB51776F19E4,
	SpringJoint_get_tolerance_m487624E739BA3A3491DA0EF3C39FEB00118E2F08,
	SpringJoint_set_tolerance_mAD95C6A76C76C08558455D407EA8E07744338DBF,
	SpringJoint__ctor_m4CF42EA676C250CC3C63E82696A63028C2C4A4B3,
	FixedJoint__ctor_m1F5D2454791CD635468C3A498C58131CD2D4DFF4,
	CharacterJoint_get_swingAxis_m7B0745C53909F0966647E587B6ED1F54240E14DE,
	CharacterJoint_set_swingAxis_m4B6A085E09C2E4107A9ED55E3C6C52FD1F14C861,
	CharacterJoint_get_twistLimitSpring_m3712394AAB44DBF5E6311C5553A592D1B35E1D66,
	CharacterJoint_set_twistLimitSpring_mF37BF3C4F77EBA9F1926FA8F07081D315A9C2031,
	CharacterJoint_get_swingLimitSpring_m46EBF7D013D8681599D40327CEE18FCAD0BBF7AE,
	CharacterJoint_set_swingLimitSpring_mBD4D7812652BE202BEB031F2E1997B70F0F87258,
	CharacterJoint_get_lowTwistLimit_m3143E5DBE317CA13C5FC20737F331569760278A7,
	CharacterJoint_set_lowTwistLimit_m8DC922DE93DAB94B329B3C672061AA9741A4C02E,
	CharacterJoint_get_highTwistLimit_mB9CA1667D4D9D532281CD6138FC453828EF8A552,
	CharacterJoint_set_highTwistLimit_m11EFDBE8CBF92BD43F147C20F247B250CB432C32,
	CharacterJoint_get_swing1Limit_m8BF48CB27AD78E7E00A42FFAF12513125D6F04CE,
	CharacterJoint_set_swing1Limit_mBA202634AF5606DA1D59B94EF3E359C7B4FD012E,
	CharacterJoint_get_swing2Limit_mACAA0BA0171DEABBF578FCA52EA4C7720EC6123F,
	CharacterJoint_set_swing2Limit_mBA612E2C6E50BAAAA798FAA03490150D379BC744,
	CharacterJoint_get_enableProjection_mD2480E9D4BEB6FC5C797D29CC8948180AE9896D4,
	CharacterJoint_set_enableProjection_mB51E0A48EC80818FB2D054C0B9B486567A449A80,
	CharacterJoint_get_projectionDistance_mD5ED2C2BF304A20A884A54816C9924F70A7E7E67,
	CharacterJoint_set_projectionDistance_mBF309612C0797CD1861EA62455A657D9DAB1896B,
	CharacterJoint_get_projectionAngle_m657873E8264EE9ACE0E3311720AFE5AE7FA57602,
	CharacterJoint_set_projectionAngle_mF7462E89230F52BE963B1CB580C078FDFBD207DA,
	CharacterJoint__ctor_m546A62607CF3DD68E930D2951F5CCA4677A1CA2F,
	CharacterJoint_get_swingAxis_Injected_mEC08D6C21AAB14DB5FBCC05E53DB3E42E3A0B0FD,
	CharacterJoint_set_swingAxis_Injected_m5FD0A81CAF88433EA17D9FC22C63A84E8B9AC9CB,
	CharacterJoint_get_twistLimitSpring_Injected_mA86E31F784213DAB18C4FC8146F0EB07B07352DE,
	CharacterJoint_set_twistLimitSpring_Injected_m2E2E111E6C5CE269A206D96F6CF48FB583D41E1C,
	CharacterJoint_get_swingLimitSpring_Injected_mF004802C09FE9D7CA86FC19687A3A7F390101511,
	CharacterJoint_set_swingLimitSpring_Injected_mFFD319ED11CB19FC37E458053875B8BB06244D6D,
	CharacterJoint_get_lowTwistLimit_Injected_mF42D60DA2C12EA46E8D6E1812937A4BAE7AFD8B6,
	CharacterJoint_set_lowTwistLimit_Injected_mD461E7C132E086B0A2DC78C18EC56C5475ADB5C2,
	CharacterJoint_get_highTwistLimit_Injected_m52BD0669FEBBCEC5CBAA97F90838C2FCE275005E,
	CharacterJoint_set_highTwistLimit_Injected_mFE855EAAE2643F8E44A3B774C446F0FA94489703,
	CharacterJoint_get_swing1Limit_Injected_m21B6864828313D548792D2CE498F0B06D021255B,
	CharacterJoint_set_swing1Limit_Injected_m210AC0C26F3EC961D8361C513A0E8D6F21740D21,
	CharacterJoint_get_swing2Limit_Injected_m8CB90B472810EBA291DB8C10129CE5F57CABA36D,
	CharacterJoint_set_swing2Limit_Injected_m903FF4342243EA8990825D6E8773AAAEA20A573F,
	ConfigurableJoint_get_secondaryAxis_m3C6E45321118FCB7144F8620E9E0777F1CE81152,
	ConfigurableJoint_set_secondaryAxis_m2CF27C9BA848BD03F1E0EEC4FD7E788C60CA36CE,
	ConfigurableJoint_get_xMotion_m5518BED4E7F558174DD6B8F313CE7D125E1A1334,
	ConfigurableJoint_set_xMotion_mBDA7D8874899D2C20E1B1BA45944AA357CDFBDCC,
	ConfigurableJoint_get_yMotion_m4F0FA7246F1BAA1AC0BD7E86504CBE48D47CA005,
	ConfigurableJoint_set_yMotion_m597259075C915C848E87B3A9CBBDA0762B5A2563,
	ConfigurableJoint_get_zMotion_mC0AEE3A95069E7C0F451B71E356DCA387CDF4AEF,
	ConfigurableJoint_set_zMotion_m3479D7843AC2F91AA958F7B18AFCE3730842AFA8,
	ConfigurableJoint_get_angularXMotion_m5CBF8FC37A7CF94AF97583E5C1551BF7859B9258,
	ConfigurableJoint_set_angularXMotion_m1691CF3456A38996918D077FD6FC2CBEEFB0C9D5,
	ConfigurableJoint_get_angularYMotion_m1A08889BB8666184FED3CF1275444D5BA70ACE5F,
	ConfigurableJoint_set_angularYMotion_m21858D3799D8EED8AB21C46DF84927B10F1414D7,
	ConfigurableJoint_get_angularZMotion_m515347C78E06D82BE0AD254824E6F134E46CC58C,
	ConfigurableJoint_set_angularZMotion_m485474C654E903BBAE579F631BBD6C737B47394B,
	ConfigurableJoint_get_linearLimitSpring_m2307DFA67235DFE5CB95567396174DEF07E7750C,
	ConfigurableJoint_set_linearLimitSpring_m88B5287870506292956CE55309AE7D9470DF5820,
	ConfigurableJoint_get_angularXLimitSpring_mCF3C44B8284447AFC22BB83EB84E261573DB6C13,
	ConfigurableJoint_set_angularXLimitSpring_mB985F8F351DC4EFF2E588BF1C40BC1C2FF074BA4,
	ConfigurableJoint_get_angularYZLimitSpring_m624F895588D14488531DAA37FFD4051A27DB9F5D,
	ConfigurableJoint_set_angularYZLimitSpring_m12F8452730747EA03C8F7DE6F2DF3C2EB0A380F2,
	ConfigurableJoint_get_linearLimit_m35456F7AF48ACA69E79D1EFE14578730BAA6A98A,
	ConfigurableJoint_set_linearLimit_m57EE251D8642A4ADED96D77555B5948AF7F4AA9E,
	ConfigurableJoint_get_lowAngularXLimit_mE5EA802AA80E71542FDE6DD911364FC24297F4BD,
	ConfigurableJoint_set_lowAngularXLimit_m6424314936986525CEBCE5C16EBA69B5129BBD5A,
	ConfigurableJoint_get_highAngularXLimit_m978FF09CAF3E87AFA149752594ADD09FB9EA1ACE,
	ConfigurableJoint_set_highAngularXLimit_m96373EE2554934636E127E345F91306844177294,
	ConfigurableJoint_get_angularYLimit_mCC629F60D5650EF0F8F49FFB5DEE4052F687CA47,
	ConfigurableJoint_set_angularYLimit_mF819FB8C5F17C9737EC0BA5A3EAAC5245AE57A08,
	ConfigurableJoint_get_angularZLimit_m3F1975F6CAFD784F4F0881CB00D6E266CCE2658B,
	ConfigurableJoint_set_angularZLimit_mCB9FEE0CAF97A1A278BDCD127C86DDD26CDBBC70,
	ConfigurableJoint_get_targetPosition_m5BFC5AD26B78262E1BE4F45B6DF3A3BEB7C3D259,
	ConfigurableJoint_set_targetPosition_m9262281EAA56638EDBA8FC727D2F6B36EBBC195A,
	ConfigurableJoint_get_targetVelocity_m424E0807712D92D19CB2797CCBC6167AF5910F85,
	ConfigurableJoint_set_targetVelocity_m0C738C630AC7A91FC13645A0924C2A48D31C25CC,
	ConfigurableJoint_get_xDrive_m94205B0C6C73599ACF1E9DB393CB6B609743FC9F,
	ConfigurableJoint_set_xDrive_m99433795EA91A20621E21DF82DBD20B6EB49E13C,
	ConfigurableJoint_get_yDrive_m908E6398366115C5F828524308146A26C9B1F73C,
	ConfigurableJoint_set_yDrive_m64B02662A3353AE1C10DFB0A38FAD9B955E155FA,
	ConfigurableJoint_get_zDrive_m450F08477A25E42880A1094477A9255DABB319FC,
	ConfigurableJoint_set_zDrive_m399D5A99A3CC1DBF3135B3D7228C5B971CC88F99,
	ConfigurableJoint_get_targetRotation_m53942230F418B64F272AD9E274EB0A6DA4309769,
	ConfigurableJoint_set_targetRotation_m3AA036C6CD6050EF49BBA225241D4C1DA110AC27,
	ConfigurableJoint_get_targetAngularVelocity_m4E74B7B35BADFF3B3E0A24DCE7AF614BFC1E4DF5,
	ConfigurableJoint_set_targetAngularVelocity_m43694FCA51058D48D1F3575806117C012DC1E565,
	ConfigurableJoint_get_rotationDriveMode_mB4E7830CED114BCEF998C7F54BCC90810BE6848B,
	ConfigurableJoint_set_rotationDriveMode_mAF10EC6CA2FA9F3B52C814FFEB06CC48C07C175E,
	ConfigurableJoint_get_angularXDrive_mF70108A1EE3D86D324BA3745C78A5EA98BC5E330,
	ConfigurableJoint_set_angularXDrive_m198E38A6FEE12C2FAA27E849F18BC61504EEACB0,
	ConfigurableJoint_get_angularYZDrive_m0308E706F0FBDB08A0D4C4DF7A8879C7710E4CB8,
	ConfigurableJoint_set_angularYZDrive_mA9F165594FD53A2100E17D2E854DD967B91066EB,
	ConfigurableJoint_get_slerpDrive_mC1C3450853FE4EC991B330991BCCD51CC684F634,
	ConfigurableJoint_set_slerpDrive_mAD0C1BDDF4D03B7CA9677ADEFC70344911C27B1D,
	ConfigurableJoint_get_projectionMode_mB3BE3DF5441473E6C07FAC78FF3E1BB075C2DB53,
	ConfigurableJoint_set_projectionMode_mA2FA6EC630E87C553B6E1C93D997B15957008075,
	ConfigurableJoint_get_projectionDistance_m72A3F68CD86F6A81B2043DD3C94B339D441A1876,
	ConfigurableJoint_set_projectionDistance_m149EF550339500403965DB5596C63ED07B54FC1B,
	ConfigurableJoint_get_projectionAngle_m8D9C39A116E9B00E5D80E4B2F8AADC5853F1F2E3,
	ConfigurableJoint_set_projectionAngle_mBD4200FF238CC5779943E4980B79D3C14643BE24,
	ConfigurableJoint_get_configuredInWorldSpace_mA9C965EEB6BB099124A5D37A3DC68D16424EC26B,
	ConfigurableJoint_set_configuredInWorldSpace_mD1FB99B42E24A0CABF43B4470E6F0C92BCCC2450,
	ConfigurableJoint_get_swapBodies_mDCFE02643DED2BB4AF58715B0AC6395030BF2371,
	ConfigurableJoint_set_swapBodies_m42B1FE715EDD21D7BBF51DBEDD804CACD613F3C7,
	ConfigurableJoint__ctor_mAD91EEE30A56D89FEA53292AFA676A37778933DA,
	ConfigurableJoint_get_secondaryAxis_Injected_m930CFC10D358646D8B71A761C1F8DE25AA64F266,
	ConfigurableJoint_set_secondaryAxis_Injected_m266E464BBB5EDE9C3EDDD8A3F3B55DD401A928BF,
	ConfigurableJoint_get_linearLimitSpring_Injected_m27C98A5D89DE493BD62DC33D119FD721978CF8ED,
	ConfigurableJoint_set_linearLimitSpring_Injected_m2EABCB82890328A9B26E5807D13133E69D94D4C6,
	ConfigurableJoint_get_angularXLimitSpring_Injected_mC3823A7F518262A26E02E3F13219622BD066380A,
	ConfigurableJoint_set_angularXLimitSpring_Injected_mE79A822ACD2B93B3E3122A9B2572B6D39BA685B3,
	ConfigurableJoint_get_angularYZLimitSpring_Injected_m67F747454BC5F356141424CCBF57155ED911766F,
	ConfigurableJoint_set_angularYZLimitSpring_Injected_mBC9D67732AAED2EAF5DF21B247500401CB29F61B,
	ConfigurableJoint_get_linearLimit_Injected_mAB06629F3F0FF14825C71BC77B8D1856836E6329,
	ConfigurableJoint_set_linearLimit_Injected_mB7C9F6674B0E76154DFCFAB457E68264FC8ACA88,
	ConfigurableJoint_get_lowAngularXLimit_Injected_m65F3B2184E5BE0B93B9702DE371B50B727050C60,
	ConfigurableJoint_set_lowAngularXLimit_Injected_m424F97E98AB918627E19CFA7FED1C4236DBFDD61,
	ConfigurableJoint_get_highAngularXLimit_Injected_m6672C8D546D7DCBD8E67AB9CF91AEB3FB76E72AC,
	ConfigurableJoint_set_highAngularXLimit_Injected_m7C31099A209513CAE46018D03F195D33CFCFD501,
	ConfigurableJoint_get_angularYLimit_Injected_m7B5BF8EE2D2AFC977D6E192307EE9A8DC14A6C47,
	ConfigurableJoint_set_angularYLimit_Injected_m4DA460D042653C9FB41E5E2A3AC4B0F18F6D76FE,
	ConfigurableJoint_get_angularZLimit_Injected_m3E5B226EC970A3A74652C9AE9B9F1BF07B8A6AE0,
	ConfigurableJoint_set_angularZLimit_Injected_m781FCA9CA40DA1F5B984BDCBA4DBE0F4309198FC,
	ConfigurableJoint_get_targetPosition_Injected_m74FC71653E196453FEF1307544EC4EE5404F71D1,
	ConfigurableJoint_set_targetPosition_Injected_m1364898AF65E1C33782F3AC6B24F2D5C8C627DB9,
	ConfigurableJoint_get_targetVelocity_Injected_mD71CBEB7F551FF874AE3BFCEF363548EA17C583E,
	ConfigurableJoint_set_targetVelocity_Injected_m1D93766920BBE9C170E22725BB97B554834B9822,
	ConfigurableJoint_get_xDrive_Injected_m2F3081B5A43CB574CF935CCC4CA1015D3B1B6E0D,
	ConfigurableJoint_set_xDrive_Injected_m98A6A7950340A2AD4EDB74744FDFAE15DE7AC746,
	ConfigurableJoint_get_yDrive_Injected_m0764FC64FA82FC454B31B0495FE53091C711AFE2,
	ConfigurableJoint_set_yDrive_Injected_m3C83E8824F18060EADF294DB53D6AC75A96F0EDA,
	ConfigurableJoint_get_zDrive_Injected_m5C94C5C057B7AEA5B4FD8E95CE9283B73DF79E1B,
	ConfigurableJoint_set_zDrive_Injected_m8867C059A6C76AE1ABBCEC0A8BDF64B31DAFD66A,
	ConfigurableJoint_get_targetRotation_Injected_mB22A6D023F8407F0D04A3041D3615761C86CCD78,
	ConfigurableJoint_set_targetRotation_Injected_m4DEDED0147CB460010A8ECC633DF07B86D99516E,
	ConfigurableJoint_get_targetAngularVelocity_Injected_m0701ECD3E94F236824645054085FB64AA4D1EEC9,
	ConfigurableJoint_set_targetAngularVelocity_Injected_mAE21B1C5D472AE7D2DAA8B8D22B6EAEC29650804,
	ConfigurableJoint_get_angularXDrive_Injected_m703DADB9957F220ECC811C4ADE5CA352547C4589,
	ConfigurableJoint_set_angularXDrive_Injected_m3416E4C46CA6E3861E95FD493217B5C2520A1CD6,
	ConfigurableJoint_get_angularYZDrive_Injected_m49DC365B36683544D5705F39F02EA6957CC21E55,
	ConfigurableJoint_set_angularYZDrive_Injected_m9B74A9B4583CAB39384B0E5D78B6922E281E9FFB,
	ConfigurableJoint_get_slerpDrive_Injected_m6F7AE0A28412FDC513DDBFDB2D589775F5FB14E1,
	ConfigurableJoint_set_slerpDrive_Injected_m07427CF12C12E1879706DBEFE49F1BC191AB52CA,
	ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208,
	ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6,
	ContactPoint_get_impulse_mA8ABFB0DCF8F4E0004FF23106B15046FA5C923B8,
	ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6,
	ContactPoint_get_otherCollider_m717D0758D578C93C6CA26E2BA87325682B6C2550,
	ContactPoint_get_separation_m0017804537DC53F84EA4C76239BCE72B0C167B4E,
	ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B,
	PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7,
	PhysicsScene_op_Equality_m792F2F283FF4346561C0D0CC7BB9332FB02C3368,
	PhysicsScene_op_Inequality_m5F2146838C650AE28B2213767520DBE383D3991B,
	PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814,
	PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3,
	PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1,
	PhysicsScene_IsValid_m74353C7AC7756A4E6B2F768551CA2D373EE28478,
	PhysicsScene_IsValid_Internal_m03967EA72EC0D8FCEDC0D79075FF9E62D77DC241,
	PhysicsScene_IsEmpty_mD7DECC3F1514241BEF94CE872FC58A5442FB014E,
	PhysicsScene_IsEmpty_Internal_mAB6C2F2464EFFD376752C43958904F6317F2C806,
	PhysicsScene_Simulate_m8F1DFA08BF5DBFBB1FF452ABC98E40C938EA1425,
	PhysicsScene_InterpolateBodies_m4F998486F63C793D362B6564A0D5850D50ED5270,
	PhysicsScene_ResetInterpolationPoses_mE48BDAB51AAFC8E8687B1BAD9F0036ECED7B2DE8,
	PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730,
	PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0,
	PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE,
	PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74,
	PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3,
	PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB,
	PhysicsScene_Query_CapsuleCast_m6871258F7BAA2370C7BA7334E2EE6752EFBD723F,
	PhysicsScene_Internal_CapsuleCast_mFB5002955B349D73D842F47BB3DBCDAE453FF2F0,
	PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35,
	PhysicsScene_Internal_CapsuleCastNonAlloc_m5C610A8AC0B9DAB01675C36EA6E478028C73D445,
	PhysicsScene_CapsuleCast_mA250677E33E5D956F8E75905C348517BD23CA4AE,
	PhysicsScene_OverlapCapsuleNonAlloc_Internal_m7A25A75ED0EC93A9B68B87EFEEE16713B5F78B3D,
	PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545,
	PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B,
	PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF,
	PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4,
	PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F,
	PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F,
	PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1,
	PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01,
	PhysicsScene_Query_BoxCast_m8B3C7AFDF3D96DB8B60E68930DBE83FB60269923,
	PhysicsScene_Internal_BoxCast_mE2056DF773CF7D52E153804C2CBDB4F4D68B4CE6,
	PhysicsScene_BoxCast_mD138F894448712F882AF7F95B2AD9052DC548D12,
	PhysicsScene_BoxCast_m83D81817A2F4BDCE6D0C4130D49E79EBF885DEA2,
	PhysicsScene_OverlapBoxNonAlloc_Internal_m77221087DFD7FCCF0242F82671A6F180DDE52326,
	PhysicsScene_OverlapBox_mF98FE9D367F5938A0E23C60684BED711EB69CA03,
	PhysicsScene_OverlapBox_m8EF90D415B4EE966DEAD2DCC80DB94D6D6C46EF4,
	PhysicsScene_Internal_BoxCastNonAlloc_m721584532E1F33DB93475FA5F89BD13422E1BFC3,
	PhysicsScene_BoxCast_m5010295997A4CBDDA261F825837A35789B4461E0,
	PhysicsScene_BoxCast_m70E3EF843956491E83FA535A8ED5756D0A417F23,
	PhysicsScene_IsValid_Internal_Injected_m49DCA66EF92A47D23B746D0D292AB11D88C2C0ED,
	PhysicsScene_IsEmpty_Internal_Injected_m18574012DBCAC8B7BA268BE32040572596258300,
	PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62,
	PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D,
	PhysicsScene_Internal_RaycastNonAlloc_Injected_mD6BA34F06BE743B2CBF46AA82EE6DDC9CCEC0F27,
	PhysicsScene_Query_CapsuleCast_Injected_m3A9CC7AB617D70C2284C49E982163F37EB0B27B0,
	PhysicsScene_Internal_CapsuleCastNonAlloc_Injected_mA8ED31F36DEE04580883BBCF58D3CEAF9D5A187D,
	PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected_mF8B5563CB6D620B1269EF5D2D7127F252D2CB358,
	PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A,
	PhysicsScene_Internal_SphereCastNonAlloc_Injected_m8B19C4FB753820C4D4952D6BEB59B7044F7C7394,
	PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737,
	PhysicsScene_Query_BoxCast_Injected_mB8A1A4B228295D1C888EB08255182EA83FC4733D,
	PhysicsScene_OverlapBoxNonAlloc_Internal_Injected_mD2053028D905149928623868D4463157E1F1AB4C,
	PhysicsScene_Internal_BoxCastNonAlloc_Injected_m9E40CADBA19328EC192BE3A33747889EFE5B2075,
	PhysicsSceneExtensions_GetPhysicsScene_mC7D6FE0FA798195A3BA3B3BA6D41F4D947D037AD,
	PhysicsSceneExtensions_GetPhysicsScene_Internal_m47C05DB774E72E24AB4ECBF75A98652C544923F9,
	PhysicsSceneExtensions_GetPhysicsScene_Internal_Injected_m2A4EE29C6BD1AA0EB6BF7683EA8E5B6783A6FA57,
	ContactPairHeader_get_BodyInstanceID_m4E7EBAF5ADE2DA3D281C9A0F5B63A006F7241CE8,
	ContactPairHeader_get_OtherBodyInstanceID_m6384A0571E81440B589E8F41D077553DF31A27C5,
	ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA,
	ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08,
	ContactPairHeader_get_PairCount_mF7E6F40BD6A8ED2B041EA993CDC01185D78674FE,
	ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5,
	ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09,
	ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27,
	ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E,
	ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3,
	ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD,
	ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256,
	ContactPair_get_ContactCount_m1F6291D2B7A188DA4FF675734535B2699495857C,
	ContactPair_get_ImpulseSum_mE97817B3288752026E30C8ED5C92D3595E890ACC,
	ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39,
	ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385,
	ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8,
	ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E,
	ContactPair_ExtractContacts_mCE72C104A1CF1E3C6FF4A16AF0AE1FE52DD08A52,
	ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC,
	ContactPair_CopyToNativeArray_m9396822D29611D9B83036C70E49017C007059435,
	ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454,
	ContactPair_GetContactPointFaceIndex_m2906D14481C497D1DB39E1CDC323D978D64086E0,
	ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565,
	ContactPair_ExtractContacts_Injected_m8C4EE9EC95500EF7E59189BC65B625B221549BB1,
	ContactPair_ExtractContactsArray_Injected_mF91AF0D52F744FB7DEF1381BDDE212EF79A6932E,
	ContactPairPoint_get_Position_mF767C5CD66E322293F064B17FD44F8CF8C356B54,
	ContactPairPoint_get_Separation_m4F5BFDBA730F08CD17BF3C0352453280DB1486AD,
	ContactPairPoint_get_Normal_mB796C363701DFD430C2395C45EF578B5226757B2,
	ContactPairPoint_get_Impulse_m94991E8BF7F18A361EB08941CFA88A1304CE8079,
	QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B,
	QueryParameters_get_Default_m9C12742976F18500744C5F6981FD1002C5E894E4,
	ColliderHit_get_instanceID_mDB124C91153EC421E57B834CF93758677F4BDAFE,
	ColliderHit_get_collider_m047EC40DD7F77CA33A3BB636B7694AA3117FE83D,
	RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971,
	RaycastCommand__ctor_mC95C1C19A37F20B3111AF893B935EA51969FB4A1,
	RaycastCommand_get_from_mCB7D147B3FF37945CCB38E9A174411ECCA769909,
	RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2,
	RaycastCommand_get_direction_mD02651FAA5BEA161F8FB390257F3DA87C3C3F393,
	RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D,
	RaycastCommand_get_physicsScene_m8731C9B74E3FB41FD8063C35453E867C218C8F44,
	RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946,
	RaycastCommand_get_distance_mFA04832DA19FD26541315E88245805E610DECED4,
	RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023,
	RaycastCommand_ScheduleBatch_m250EB363528A1CAE1F9E3FD517C433905464C7DE,
	RaycastCommand_ScheduleBatch_m5CF1A3E3FE582BD990F7844F3D9C18B5A2295AF0,
	RaycastCommand_ScheduleRaycastBatch_m2ECCFEE5C88640231C83DFD65411358E2B5C2CA2,
	RaycastCommand__ctor_m8DFCFD3175272180C884798DC278A8D9B81E1B38,
	RaycastCommand__ctor_m5D55292413D4D916C28793CB0F6B22A67258D26D,
	RaycastCommand_get_maxHits_m8F19093BDC596CD7D5228E6DE4ACF898FF04AC79,
	RaycastCommand_set_maxHits_m85328D33D9A03350705F8E792AB835CCC9E0EF8D,
	RaycastCommand_get_layerMask_mBF83D8981FDD45343B1E729854A78C0D59D3610F,
	RaycastCommand_set_layerMask_m5927E9524F8A1105B915FB04E59FEE65E5D95E51,
	RaycastCommand_ScheduleRaycastBatch_Injected_m7E4418AF58937EBCB111749C2A5EC6DD2E3BF4F6,
	SpherecastCommand__ctor_m04B04D1690849F029961341127EB025E040857C1,
	SpherecastCommand__ctor_mEC47B62DAD3FE54941D68903C40327231E3DEEF3,
	SpherecastCommand_get_origin_m0AB8181C1233B9E62CF46ED485DF5448BFE2B236,
	SpherecastCommand_set_origin_m78010578C88B3BBBEA2732D6B101C9818E113A60,
	SpherecastCommand_get_radius_m304FFC1538682C7BDFD01564B84E87BF9984009E,
	SpherecastCommand_set_radius_mF2F0DB4FEDD1DFC396D3DFC52E7964616778F9E3,
	SpherecastCommand_get_direction_m18300C7CEE58565AEC540BE249F11D5AE452CE87,
	SpherecastCommand_set_direction_mAF24E6F239F768CE2F6D795AACCD79DC5F887042,
	SpherecastCommand_get_distance_m12385D25AB25C8C3656E370DDCF50BD924499233,
	SpherecastCommand_set_distance_m32AAFE7BE818D729124117A663F470379E4DF9DB,
	SpherecastCommand_get_physicsScene_m3EE7B58FE0F80F8EC89EC5AFA548D6F605E92022,
	SpherecastCommand_set_physicsScene_m6ED30632068593C2DB57FC49CBBE56B07DC0ABEC,
	SpherecastCommand_ScheduleBatch_mBBBF1BFF56DFE84E2EA2BE32D1A43AC2ADF95B00,
	SpherecastCommand_ScheduleBatch_m81B075750097344CB175C412B41F26B7D452A6A9,
	SpherecastCommand_ScheduleSpherecastBatch_mA961B7A80D08B02E07734216F24A519621D73963,
	SpherecastCommand__ctor_m0D0BFD29A98093A4A98F2689EBAA7E326B1C2117,
	SpherecastCommand__ctor_m4A2082D1C681B8685B4A1DF2E5EA0AB4403EBCFB,
	SpherecastCommand_get_layerMask_m443973FC7B8DC0E48F13DE061F70E2E2AA8AF4C3,
	SpherecastCommand_set_layerMask_m9B615EFE1A9D7057FA00E318B8D4CC38CEFC20A2,
	SpherecastCommand_ScheduleSpherecastBatch_Injected_mE55F4F3EA74669E1D8B35A3605E80FD73066C518,
	CapsulecastCommand__ctor_m2B94B22D7233C5E4DD8B88A5D527336195F063F8,
	CapsulecastCommand__ctor_m3485B6028BCC02B96DC64D039E9BBEE73154D200,
	CapsulecastCommand_get_point1_m6DFFD9A6C8D4221269F06D50B437D5CF48529FE5,
	CapsulecastCommand_set_point1_mB37C2BCF42C923D514AF9E78C699F85242EAA563,
	CapsulecastCommand_get_point2_m78EC7D397D29EFCDD0E42CAD32C88947F4D4ABB4,
	CapsulecastCommand_set_point2_mE75966408CD135BAF86B446AF5379ACB6FE98880,
	CapsulecastCommand_get_radius_mD77A81F5114A1580EF1FF141C738560ACBA7DC13,
	CapsulecastCommand_set_radius_mA968E8E31E09D5B0D861DFDB1D9D8621ACB3AB6C,
	CapsulecastCommand_get_direction_m752A1EE8CC56AB2ECB9FCA56D65BF86DE4AE9242,
	CapsulecastCommand_set_direction_m57DBE62F611302029343DA62526803E21A72C754,
	CapsulecastCommand_get_distance_m24EE1EBEF3D9CAB31A067A509743ED813CF4A2D1,
	CapsulecastCommand_set_distance_m9579828070C608533DDD1DBE530A5AEF4905EBC5,
	CapsulecastCommand_get_physicsScene_m1763C75CA3E31975B0628E62E1A6681AC38C10E0,
	CapsulecastCommand_set_physicsScene_m0FD54689DF616FDC760D2221B55182DF4CCE20A8,
	CapsulecastCommand_ScheduleBatch_mAA20524A47DEB8A2589E3BD6B60BD9641CB98059,
	CapsulecastCommand_ScheduleBatch_mC0EF5FFF4A1CD532FA4F85B7C3457D72BF131DD8,
	CapsulecastCommand_ScheduleCapsulecastBatch_m7A7A8E9274B2A0E440E55C65EEF6B2AC72683214,
	CapsulecastCommand__ctor_mFBD0653BF8A3DD1F0C489979B014A2E5F5A36397,
	CapsulecastCommand__ctor_mBDF287919FF6EE28169910A6B333947755D8BE92,
	CapsulecastCommand_get_layerMask_m519E4018F493F32B213BCCFA0C6C6A5AEC8C331A,
	CapsulecastCommand_set_layerMask_m57E3F255278021426F7D11B6ECE736A7706EFD44,
	CapsulecastCommand_ScheduleCapsulecastBatch_Injected_mA6D010BDAF400BCB239DCA29514DD42F573F605B,
	BoxcastCommand__ctor_m537698355BE6E7C5C981AF853DB6492FF3A6AFB5,
	BoxcastCommand__ctor_m9ED746D4ACF937622D7CF6133CDE908A39BAE1AC,
	BoxcastCommand_get_center_mD46099507F4864267BF537C80FCA33683EE0CB24,
	BoxcastCommand_set_center_m7A8F2A037D6787571859B4049584B10A6D555C38,
	BoxcastCommand_get_halfExtents_m133E7927EB8962266153E109D12A2B60BBE195F7,
	BoxcastCommand_set_halfExtents_m82FC465A89C326F95F35B5F95ABD96B50551941D,
	BoxcastCommand_get_orientation_m693D6DBA29CABE1B594AA6F2BE5310415815D544,
	BoxcastCommand_set_orientation_m1CD41FB7539D597AF8E9FA9BC12318D918A46471,
	BoxcastCommand_get_direction_m1C104750BEC1BE901952975E35449BC3F4830899,
	BoxcastCommand_set_direction_mAB860B183A345132B99C3A5E8D2FADA5417E357E,
	BoxcastCommand_get_distance_m1DE2D7596C33AE1B19A4309576A818A60D62FB5F,
	BoxcastCommand_set_distance_mC6933F0893A7531667539B643842C85450412E98,
	BoxcastCommand_get_physicsScene_mDD75C74B54E085D09972CB7894B19384A49AC3FF,
	BoxcastCommand_set_physicsScene_mA447E367EC5936A5C621026E98986B109FFA2CD4,
	BoxcastCommand_ScheduleBatch_mD33D3B6948D72061216545362C812F74AF7E93DE,
	BoxcastCommand_ScheduleBatch_m979A4B9E08534FB53764A82E7268E3989BD8382C,
	BoxcastCommand_ScheduleBoxcastBatch_m3F43E29DDF3179F9FF19892557723A4883C76599,
	BoxcastCommand__ctor_mFFDEA928E01C4FE4D034143AD706A9C994EE73B0,
	BoxcastCommand__ctor_mCA72956F4705B234738488EC66724FD30B589746,
	BoxcastCommand_get_layerMask_m86215B17A1CF8661C744DD5686B21B3F4D02EE1F,
	BoxcastCommand_set_layerMask_m3E8588D218280C04C76F2C2DF0D38BCB54800F3C,
	BoxcastCommand_ScheduleBoxcastBatch_Injected_m254F75FB3ED73BB45FFD72C3C7B3FA3E12D1BFCD,
	ClosestPointCommand__ctor_m4141AF23EA150FE427CCFDC8736B29EA79B0E462,
	ClosestPointCommand__ctor_m28FE39F299728FB0D1F3C7FC6E96AECAEB314BC5,
	ClosestPointCommand_get_point_m614B7DBBAE578B0F90DE6CE2C04581A7F4A9252C,
	ClosestPointCommand_set_point_m081F4AAB8007B64EF2B0BEBEC1AAAA490E5F135A,
	ClosestPointCommand_get_colliderInstanceID_m814CB755AFF21E5D8CE4335CA5138D4C2B1EC017,
	ClosestPointCommand_set_colliderInstanceID_m033DF85E7ED333AA5F65AD31992180FC21A76464,
	ClosestPointCommand_get_position_m1DDC44DAB526733668BAFA4EEF8BE8115251719D,
	ClosestPointCommand_set_position_mB17F876ADAE3832D149A219B67CD98E24CB84BD5,
	ClosestPointCommand_get_rotation_mC188C1910A3A53FD57844EA416A5BE34025BC7F0,
	ClosestPointCommand_set_rotation_m9577993E95E2B9A5A6A118E3778700B1FB5919B0,
	ClosestPointCommand_get_scale_m32B7E3E310D3850A838518F31B51BDB3797E7CCC,
	ClosestPointCommand_set_scale_m2875D2E39D8F39FE955D2D59D551D95E3C0807FF,
	ClosestPointCommand_ScheduleBatch_mE01BDCB991219F0F5C7566075AAA444B557FA8B5,
	ClosestPointCommand_ScheduleClosestPointCommandBatch_m16DEA01FAD29EDF8B76CF299C351D66E25E22963,
	ClosestPointCommand_ScheduleClosestPointCommandBatch_Injected_mA0F9172509CD1EF4C92F9996B9B5256847AA1AEF,
	OverlapSphereCommand__ctor_mA53F5911553913E86C4374212FA4F98849E53331,
	OverlapSphereCommand__ctor_m77EF721239EB5B4701605ED24474EE4BF986F7EC,
	OverlapSphereCommand_get_point_m8A4C6707D923231644CB12A12AC7CA949AEED768,
	OverlapSphereCommand_set_point_mA7FA5A5E4E43BD1B19E7159A9A1AB826ED130424,
	OverlapSphereCommand_get_radius_m6641206FCF8B06BDD09FE7FCF8B4DB86C9BDDE0D,
	OverlapSphereCommand_set_radius_m6BE30815AD7DC52847CE5267903AD38FA1BAF97A,
	OverlapSphereCommand_get_physicsScene_mADCB56464A1D9EBD73AA1093A25854746B2E307D,
	OverlapSphereCommand_set_physicsScene_m34A25CC89E8645889C84F4A6A3F276BEFA987BD9,
	OverlapSphereCommand_ScheduleBatch_mC2AB1755EAD42F985DF5F0C195BF20AAA803FFBB,
	OverlapSphereCommand_ScheduleOverlapSphereBatch_m69AEE9C11DB4076BB1CF4207B3637AA2EB75222F,
	OverlapSphereCommand_ScheduleOverlapSphereBatch_Injected_mCE63DECDEC4D00C235E25BC548C60941BA7D828A,
	OverlapBoxCommand__ctor_m0700FB9BF317EF23B875619048A36D2F8691C7A2,
	OverlapBoxCommand__ctor_m456F1D2B75CD344E50931669D39A93DD3DD15C9B,
	OverlapBoxCommand_get_center_mF002B687D58A1CD1F31D8F5B445E7D2356A5A33F,
	OverlapBoxCommand_set_center_m95FB7FC7D91FADDC41CB513135C3CC08D0BB7490,
	OverlapBoxCommand_get_halfExtents_m251067DF176F2EC71D8ECF6790BBD8ECAE57E3A5,
	OverlapBoxCommand_set_halfExtents_m71E91162BF53AE2E003E9F17A477C97D7A37070A,
	OverlapBoxCommand_get_orientation_m9B593B572EB7D04B8A64334B028B600D12C1552B,
	OverlapBoxCommand_set_orientation_mF00A5CFDA9242A6738E3A8458A88A219E00DF54F,
	OverlapBoxCommand_get_physicsScene_m143BE67D0DD6C1AF45F4F51A4B20E3A4BB35A687,
	OverlapBoxCommand_set_physicsScene_m5C7EEAB11A4465E1CC58029647B3E271E1B43E98,
	OverlapBoxCommand_ScheduleBatch_m7D7E55D43BA475815B97C18145B16E9BB427C279,
	OverlapBoxCommand_ScheduleOverlapBoxBatch_mF80EE90AE0A453FFEDB16067434803D9BE772D25,
	OverlapBoxCommand_ScheduleOverlapBoxBatch_Injected_mDC3CA4B45216402CB7D3491FA90B5A2A6B41072C,
	OverlapCapsuleCommand__ctor_mFC3B345057E39BADD029DB9CAE9AA5E32FFC72DC,
	OverlapCapsuleCommand__ctor_m5ED84963BE98C5C9247885CC593FF291A67CFF2F,
	OverlapCapsuleCommand_get_point0_m34A2DEDD42E8304E06C33C3BE4B8AFD20DAC32EF,
	OverlapCapsuleCommand_set_point0_m42F1772676705667421D2D3211970154FF48BC32,
	OverlapCapsuleCommand_get_point1_m8DA15C10389BEA183855D52C1BDABA9A8426AF7C,
	OverlapCapsuleCommand_set_point1_m87C54B6484C5A7BA3AF7B9DB88AE1EE585DE812E,
	OverlapCapsuleCommand_get_radius_mEC13C74A0C6683B6711F6C3A5EC9DF749159418B,
	OverlapCapsuleCommand_set_radius_m6258361B0AC1E7C903A6F8EE119239688CE8CF87,
	OverlapCapsuleCommand_get_physicsScene_mF8BF85E4E8670480B67CCC532078F93270064DD3,
	OverlapCapsuleCommand_set_physicsScene_m052934A9F6A2A5097A72AC74D4968A18564CE3C8,
	OverlapCapsuleCommand_ScheduleBatch_mAEAF1C7D75C3CA0094A77827D4B3E3933D0D812B,
	OverlapCapsuleCommand_ScheduleOverlapCapsuleBatch_m61C25D795B9EB5935941A65E20A7EA6340564A68,
	OverlapCapsuleCommand_ScheduleOverlapCapsuleBatch_Injected_m7AF66255B67B6C85BF47CD0D75AF38AF9657EB12,
};
extern void WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274_AdjustorThunk (void);
extern void WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E_AdjustorThunk (void);
extern void WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34_AdjustorThunk (void);
extern void WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E_AdjustorThunk (void);
extern void WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755_AdjustorThunk (void);
extern void WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C_AdjustorThunk (void);
extern void WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F_AdjustorThunk (void);
extern void WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43_AdjustorThunk (void);
extern void WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6_AdjustorThunk (void);
extern void WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5_AdjustorThunk (void);
extern void SoftJointLimit_get_limit_m565D543DC9482F893A8C1F8582B9A06F7E287286_AdjustorThunk (void);
extern void SoftJointLimit_set_limit_m34B7F00528D7F5B03D2AC39E44AFD96F0EAADF1A_AdjustorThunk (void);
extern void SoftJointLimit_get_bounciness_m978E3102B620170A84C30EC15963B04564866DA8_AdjustorThunk (void);
extern void SoftJointLimit_set_bounciness_m1D956BA96C435546CF89A8936F684A3C2E3BB036_AdjustorThunk (void);
extern void SoftJointLimit_get_contactDistance_mD9CFEBFE02B773802773E53F9643C20E8C274020_AdjustorThunk (void);
extern void SoftJointLimit_set_contactDistance_mDF20A5E81A2184F22CBA3007BB871BF87CB8FF2D_AdjustorThunk (void);
extern void SoftJointLimit_get_spring_m8424406A064106C77453C7B24FACEC365ECAD991_AdjustorThunk (void);
extern void SoftJointLimit_set_spring_mA96D8FDEA28FD24B2E3077ACA5159F74047F0EE6_AdjustorThunk (void);
extern void SoftJointLimit_get_damper_mD68459A168731FF3AB023A227286A19D8B72DEF5_AdjustorThunk (void);
extern void SoftJointLimit_set_damper_m3AF75ED0C45C84404FE3B4ED3850DD9A30704753_AdjustorThunk (void);
extern void SoftJointLimit_get_bouncyness_m711720910DECFC3A5C77F0A24602A820659A418C_AdjustorThunk (void);
extern void SoftJointLimit_set_bouncyness_m2AA2F20F2ABB85328D343253D8A3836E61EE2E67_AdjustorThunk (void);
extern void SoftJointLimitSpring_get_spring_m04C597C18DD13494AFBAEB9348D7877EDAC0AA08_AdjustorThunk (void);
extern void SoftJointLimitSpring_set_spring_m9A216142953ECC1CEE5080D603D18F9D1BD0A6EA_AdjustorThunk (void);
extern void SoftJointLimitSpring_get_damper_m7B70F7492F1C75B68E879C484681AD00FDB771EE_AdjustorThunk (void);
extern void SoftJointLimitSpring_set_damper_mA86F8E250BA84A6DC3E84DC1A40319A39CD5CFD6_AdjustorThunk (void);
extern void JointDrive_get_positionSpring_m41EABECAFA44BAC2E33CE9FD278B3DEDE884585D_AdjustorThunk (void);
extern void JointDrive_set_positionSpring_mC928C6830ABEC56D68FB9B054DCD2A1A807EAD52_AdjustorThunk (void);
extern void JointDrive_get_positionDamper_mA22BFFB513E083AE41711EA6EA96D0424E7A9D1D_AdjustorThunk (void);
extern void JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604_AdjustorThunk (void);
extern void JointDrive_get_maximumForce_mED9DD2A8EAACC262A83CED0991F2A42E78B2F3B8_AdjustorThunk (void);
extern void JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5_AdjustorThunk (void);
extern void JointDrive_get_useAcceleration_mAFE42782DBC795844A8C698802064AAF34D12A45_AdjustorThunk (void);
extern void JointDrive_set_useAcceleration_m04919856A177B9ECEEDA9178F64E85625C6CAC33_AdjustorThunk (void);
extern void JointDrive_get_mode_m7012ADA832DB3EECCC4EBBD23B0C067F9AD97918_AdjustorThunk (void);
extern void JointDrive_set_mode_m1C934E502FE9705DE2DC7C37257C10BAED74D394_AdjustorThunk (void);
extern void JointMotor_get_targetVelocity_mDB63119AC6C3AF796785042AF466CD02D937820F_AdjustorThunk (void);
extern void JointMotor_set_targetVelocity_m6F58E447E9C1F7812246ECD1CB8C2929D1CE86DE_AdjustorThunk (void);
extern void JointMotor_get_force_mB4BDD8D40380A7E01E9C062BAADB1BE97F6A63FE_AdjustorThunk (void);
extern void JointMotor_set_force_m66139CAD801991E3788835067C0D738EA000BFD3_AdjustorThunk (void);
extern void JointMotor_get_freeSpin_m01BCFC597BF0DF5540378A79FAC1F8D0F4336683_AdjustorThunk (void);
extern void JointMotor_set_freeSpin_mC37B615961CCAC6A3DC75109476C8C46FF4E894C_AdjustorThunk (void);
extern void JointLimits_get_min_m3E8D3C572B30DA53262849D4D5BFFD9A77276FC8_AdjustorThunk (void);
extern void JointLimits_set_min_m6DCC6F92E715577794E36CD524989509D2A001AF_AdjustorThunk (void);
extern void JointLimits_get_max_m4E6BC6E5D320C4E2E7599852B19F4524D9549638_AdjustorThunk (void);
extern void JointLimits_set_max_m192F8B77417D548BF0162E651049DB1C4C1D81A0_AdjustorThunk (void);
extern void JointLimits_get_bounciness_m631282F7314872399F85C93F9827AA1F79BEFBAB_AdjustorThunk (void);
extern void JointLimits_set_bounciness_mEB2995C51F0E0F6591D42881CB7CC69CF1835CC9_AdjustorThunk (void);
extern void JointLimits_get_bounceMinVelocity_m2BF2A2C9471171604CC519C205D2BC738371C40A_AdjustorThunk (void);
extern void JointLimits_set_bounceMinVelocity_m3D1D91AF20CBD28E66EF1BAA7AAB003BC9E671E1_AdjustorThunk (void);
extern void JointLimits_get_contactDistance_mD306F20301F701B403B1D85EF984E958158F3717_AdjustorThunk (void);
extern void JointLimits_set_contactDistance_mF8CC4376AC7E794C0E2FA1EBF035798EB82680E8_AdjustorThunk (void);
extern void ArticulationReducedSpace_get_Item_m8E297D94FA09BCB4E45C045BF0411D67E183BF24_AdjustorThunk (void);
extern void ArticulationReducedSpace_set_Item_m511249FEDD7DA1241235F2D78B17E495435141CB_AdjustorThunk (void);
extern void ArticulationReducedSpace__ctor_m73747277F64DBDD2DD2C2027F7848AB29A735D0D_AdjustorThunk (void);
extern void ArticulationReducedSpace__ctor_m1993AEDD6D0F94169DDDE6D3E4BFEB4B1D2144EE_AdjustorThunk (void);
extern void ArticulationReducedSpace__ctor_mE85D365C229AA06F3E7D4FB73717088D7F20B461_AdjustorThunk (void);
extern void ArticulationJacobian__ctor_mD33412D6B8F30DA581549DF60FD9727204971574_AdjustorThunk (void);
extern void ArticulationJacobian_get_Item_mC4DCABA2709F24770BD58BBF007909757BF2AE16_AdjustorThunk (void);
extern void ArticulationJacobian_set_Item_m1CB6CCCC4FC036E19F9CECE28C6488245082C85D_AdjustorThunk (void);
extern void ArticulationJacobian_get_rows_mD319F639F0B37D27355015EAD8BC0BCC027D6EDD_AdjustorThunk (void);
extern void ArticulationJacobian_set_rows_m1782AA659C927A76DCBE506D7EBB429F0DC16C8E_AdjustorThunk (void);
extern void ArticulationJacobian_get_columns_m2CD8D3E79C5A0EE73DEF851ECBD6682FAA4738AB_AdjustorThunk (void);
extern void ArticulationJacobian_set_columns_m911137E1ACF2BA000559FD77849C07E3B60E679E_AdjustorThunk (void);
extern void ArticulationJacobian_get_elements_m6E8E68D8FF8F98B8D532397C519E3F433162264C_AdjustorThunk (void);
extern void ArticulationJacobian_set_elements_m3D711A40DB5A05C254933079A71CFFC756D86074_AdjustorThunk (void);
extern void ModifiableContactPair_get_colliderInstanceID_m555E31D2818CEDAC9D58538314D80701EDED2C9F_AdjustorThunk (void);
extern void ModifiableContactPair_get_otherColliderInstanceID_m92593BF590D5F14345E4DA5AADF9D61D0C55D363_AdjustorThunk (void);
extern void ModifiableContactPair_get_bodyInstanceID_m8B56E95EE29C2957E3166B3616B3E02AE8176D3B_AdjustorThunk (void);
extern void ModifiableContactPair_get_otherBodyInstanceID_mE275D82AE73ED679018A57A87A04656598A587AF_AdjustorThunk (void);
extern void ModifiableContactPair_get_bodyVelocity_mE291E12A45BF3D11E31252D5D0F48CFE9979878B_AdjustorThunk (void);
extern void ModifiableContactPair_get_bodyAngularVelocity_mDE015C32239E67B14D5F0E74D6F0C6D1A12F2D97_AdjustorThunk (void);
extern void ModifiableContactPair_get_otherBodyVelocity_m08C3C21AB9B5FB55EBE3BE3FD34EA10D02830F46_AdjustorThunk (void);
extern void ModifiableContactPair_get_otherBodyAngularVelocity_mFEE2BB3A0CBAC44D35EDFE528810B1EF7779587F_AdjustorThunk (void);
extern void ModifiableContactPair_get_contactCount_mE6A959F3C3DAC88580404AE830E666A74AC9DAB7_AdjustorThunk (void);
extern void ModifiableContactPair_get_massProperties_m6E657FD7D82285D3993FF9085ABBCC4F0CF169E8_AdjustorThunk (void);
extern void ModifiableContactPair_set_massProperties_mD53EC6724C3F093A3C4C08BCCC1815A858B628DE_AdjustorThunk (void);
extern void ModifiableContactPair_GetPoint_mAD9A2FCD25D217AF868CDE3A8F3C6977F8D1B2F6_AdjustorThunk (void);
extern void ModifiableContactPair_SetPoint_m3CD65095DE1222A9B7F1A57B4BB91A5B2ADE043A_AdjustorThunk (void);
extern void ModifiableContactPair_GetNormal_m6C0A9110EFC0EF0E8485D9A1A63ED91A7347B681_AdjustorThunk (void);
extern void ModifiableContactPair_SetNormal_m1D65EB3CA8B5BA26FDB3565CA9933DB5EE2EE959_AdjustorThunk (void);
extern void ModifiableContactPair_GetSeparation_m5BE2C0DEBD531977AD9C56632F4F9E7CD157C9C7_AdjustorThunk (void);
extern void ModifiableContactPair_SetSeparation_m9D69A9DF8A5F726E24C7224549944B54EED4D48F_AdjustorThunk (void);
extern void ModifiableContactPair_GetTargetVelocity_m92DBB0CECE637554C12BCC9ECC6CCEFE1124648E_AdjustorThunk (void);
extern void ModifiableContactPair_SetTargetVelocity_mA07021905C401183F59C7D3AB9B28A0E0EFBD7FB_AdjustorThunk (void);
extern void ModifiableContactPair_GetBounciness_m42DF58A5A3BEB6E55836F4E91CC210E01D5101F6_AdjustorThunk (void);
extern void ModifiableContactPair_SetBounciness_m5FFB3F3B7AA6BBD143E423570DAFFA0F4F86F74B_AdjustorThunk (void);
extern void ModifiableContactPair_GetStaticFriction_m836099A4AED9C8519C9D977F30C3CAD83B069D16_AdjustorThunk (void);
extern void ModifiableContactPair_SetStaticFriction_m258ADDF7FB804005D5A008118E341F7FD27937C2_AdjustorThunk (void);
extern void ModifiableContactPair_GetDynamicFriction_mB4975A0337DEFF0DE340F3097E8E4BE8754FA9FE_AdjustorThunk (void);
extern void ModifiableContactPair_SetDynamicFriction_m80A5A32EC1E1232F2814760D4D244D03E63CE362_AdjustorThunk (void);
extern void ModifiableContactPair_GetMaxImpulse_m887290F5A2A460EE2F7A937D416A1819E280FA63_AdjustorThunk (void);
extern void ModifiableContactPair_SetMaxImpulse_mBAEBD045C31CD54B65389D988B51163C656AE77A_AdjustorThunk (void);
extern void ModifiableContactPair_IgnoreContact_m041CE733E6A9425E8F1EE1278DBC43A90B0226A8_AdjustorThunk (void);
extern void ModifiableContactPair_GetFaceIndex_mCD63E169BF58DC041F1C14C732F24056E74B3461_AdjustorThunk (void);
extern void ModifiableContactPair_GetContact_mEE629C8AC75FBD7A833BD9594A783D5A5B02BEFA_AdjustorThunk (void);
extern void ModifiableContactPair_GetContactPatch_mFEA78CD88438A9D61844A643460C4BD8AB0FBC8F_AdjustorThunk (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk (void);
extern void RaycastHit_get_colliderInstanceID_m4CEBF5D185F207B1F958A93EA62AF35BE889D758_AdjustorThunk (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk (void);
extern void RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11_AdjustorThunk (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk (void);
extern void RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B_AdjustorThunk (void);
extern void RaycastHit_get_barycentricCoordinate_m15E866896213623E3E49B54F4273E343A50B1797_AdjustorThunk (void);
extern void RaycastHit_set_barycentricCoordinate_m97B7F8D06D728E9881527E1C1AA4FCCADEEC4C64_AdjustorThunk (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk (void);
extern void RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC_AdjustorThunk (void);
extern void RaycastHit_get_triangleIndex_mA363EA340DC5E202DA8E9AC6DF7CCFA20D6EF72A_AdjustorThunk (void);
extern void RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D_AdjustorThunk (void);
extern void RaycastHit_get_textureCoord2_m93F06D5875AE4C8EBD21B9E184CF4FE3117EF704_AdjustorThunk (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk (void);
extern void RaycastHit_get_articulationBody_m19487EF6AFD5B39F682254E2EB47E3313C24357A_AdjustorThunk (void);
extern void RaycastHit_get_lightmapCoord_mDA52EAAC7C3E7170AFDE13DE7183E6CDD91D7BEE_AdjustorThunk (void);
extern void RaycastHit_get_textureCoord1_m62351A7A51BB86A5614D6B9BE49EE9A8DEF215AE_AdjustorThunk (void);
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk (void);
extern void ContactPoint_get_impulse_mA8ABFB0DCF8F4E0004FF23106B15046FA5C923B8_AdjustorThunk (void);
extern void ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6_AdjustorThunk (void);
extern void ContactPoint_get_otherCollider_m717D0758D578C93C6CA26E2BA87325682B6C2550_AdjustorThunk (void);
extern void ContactPoint_get_separation_m0017804537DC53F84EA4C76239BCE72B0C167B4E_AdjustorThunk (void);
extern void ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B_AdjustorThunk (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk (void);
extern void PhysicsScene_IsValid_m74353C7AC7756A4E6B2F768551CA2D373EE28478_AdjustorThunk (void);
extern void PhysicsScene_IsEmpty_mD7DECC3F1514241BEF94CE872FC58A5442FB014E_AdjustorThunk (void);
extern void PhysicsScene_Simulate_m8F1DFA08BF5DBFBB1FF452ABC98E40C938EA1425_AdjustorThunk (void);
extern void PhysicsScene_InterpolateBodies_m4F998486F63C793D362B6564A0D5850D50ED5270_AdjustorThunk (void);
extern void PhysicsScene_ResetInterpolationPoses_mE48BDAB51AAFC8E8687B1BAD9F0036ECED7B2DE8_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk (void);
extern void PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35_AdjustorThunk (void);
extern void PhysicsScene_CapsuleCast_mA250677E33E5D956F8E75905C348517BD23CA4AE_AdjustorThunk (void);
extern void PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk (void);
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk (void);
extern void PhysicsScene_BoxCast_mD138F894448712F882AF7F95B2AD9052DC548D12_AdjustorThunk (void);
extern void PhysicsScene_BoxCast_m83D81817A2F4BDCE6D0C4130D49E79EBF885DEA2_AdjustorThunk (void);
extern void PhysicsScene_OverlapBox_mF98FE9D367F5938A0E23C60684BED711EB69CA03_AdjustorThunk (void);
extern void PhysicsScene_OverlapBox_m8EF90D415B4EE966DEAD2DCC80DB94D6D6C46EF4_AdjustorThunk (void);
extern void PhysicsScene_BoxCast_m5010295997A4CBDDA261F825837A35789B4461E0_AdjustorThunk (void);
extern void PhysicsScene_BoxCast_m70E3EF843956491E83FA535A8ED5756D0A417F23_AdjustorThunk (void);
extern void ContactPairHeader_get_BodyInstanceID_m4E7EBAF5ADE2DA3D281C9A0F5B63A006F7241CE8_AdjustorThunk (void);
extern void ContactPairHeader_get_OtherBodyInstanceID_m6384A0571E81440B589E8F41D077553DF31A27C5_AdjustorThunk (void);
extern void ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA_AdjustorThunk (void);
extern void ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08_AdjustorThunk (void);
extern void ContactPairHeader_get_PairCount_mF7E6F40BD6A8ED2B041EA993CDC01185D78674FE_AdjustorThunk (void);
extern void ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk (void);
extern void ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E_AdjustorThunk (void);
extern void ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3_AdjustorThunk (void);
extern void ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD_AdjustorThunk (void);
extern void ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256_AdjustorThunk (void);
extern void ContactPair_get_ContactCount_m1F6291D2B7A188DA4FF675734535B2699495857C_AdjustorThunk (void);
extern void ContactPair_get_ImpulseSum_mE97817B3288752026E30C8ED5C92D3595E890ACC_AdjustorThunk (void);
extern void ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39_AdjustorThunk (void);
extern void ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385_AdjustorThunk (void);
extern void ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8_AdjustorThunk (void);
extern void ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E_AdjustorThunk (void);
extern void ContactPair_ExtractContacts_mCE72C104A1CF1E3C6FF4A16AF0AE1FE52DD08A52_AdjustorThunk (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk (void);
extern void ContactPair_CopyToNativeArray_m9396822D29611D9B83036C70E49017C007059435_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk (void);
extern void ContactPair_GetContactPointFaceIndex_m2906D14481C497D1DB39E1CDC323D978D64086E0_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk (void);
extern void ContactPairPoint_get_Position_mF767C5CD66E322293F064B17FD44F8CF8C356B54_AdjustorThunk (void);
extern void ContactPairPoint_get_Separation_m4F5BFDBA730F08CD17BF3C0352453280DB1486AD_AdjustorThunk (void);
extern void ContactPairPoint_get_Normal_mB796C363701DFD430C2395C45EF578B5226757B2_AdjustorThunk (void);
extern void ContactPairPoint_get_Impulse_m94991E8BF7F18A361EB08941CFA88A1304CE8079_AdjustorThunk (void);
extern void QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B_AdjustorThunk (void);
extern void ColliderHit_get_instanceID_mDB124C91153EC421E57B834CF93758677F4BDAFE_AdjustorThunk (void);
extern void ColliderHit_get_collider_m047EC40DD7F77CA33A3BB636B7694AA3117FE83D_AdjustorThunk (void);
extern void RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971_AdjustorThunk (void);
extern void RaycastCommand__ctor_mC95C1C19A37F20B3111AF893B935EA51969FB4A1_AdjustorThunk (void);
extern void RaycastCommand_get_from_mCB7D147B3FF37945CCB38E9A174411ECCA769909_AdjustorThunk (void);
extern void RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2_AdjustorThunk (void);
extern void RaycastCommand_get_direction_mD02651FAA5BEA161F8FB390257F3DA87C3C3F393_AdjustorThunk (void);
extern void RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D_AdjustorThunk (void);
extern void RaycastCommand_get_physicsScene_m8731C9B74E3FB41FD8063C35453E867C218C8F44_AdjustorThunk (void);
extern void RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946_AdjustorThunk (void);
extern void RaycastCommand_get_distance_mFA04832DA19FD26541315E88245805E610DECED4_AdjustorThunk (void);
extern void RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023_AdjustorThunk (void);
extern void RaycastCommand__ctor_m8DFCFD3175272180C884798DC278A8D9B81E1B38_AdjustorThunk (void);
extern void RaycastCommand__ctor_m5D55292413D4D916C28793CB0F6B22A67258D26D_AdjustorThunk (void);
extern void RaycastCommand_get_maxHits_m8F19093BDC596CD7D5228E6DE4ACF898FF04AC79_AdjustorThunk (void);
extern void RaycastCommand_set_maxHits_m85328D33D9A03350705F8E792AB835CCC9E0EF8D_AdjustorThunk (void);
extern void RaycastCommand_get_layerMask_mBF83D8981FDD45343B1E729854A78C0D59D3610F_AdjustorThunk (void);
extern void RaycastCommand_set_layerMask_m5927E9524F8A1105B915FB04E59FEE65E5D95E51_AdjustorThunk (void);
extern void SpherecastCommand__ctor_m04B04D1690849F029961341127EB025E040857C1_AdjustorThunk (void);
extern void SpherecastCommand__ctor_mEC47B62DAD3FE54941D68903C40327231E3DEEF3_AdjustorThunk (void);
extern void SpherecastCommand_get_origin_m0AB8181C1233B9E62CF46ED485DF5448BFE2B236_AdjustorThunk (void);
extern void SpherecastCommand_set_origin_m78010578C88B3BBBEA2732D6B101C9818E113A60_AdjustorThunk (void);
extern void SpherecastCommand_get_radius_m304FFC1538682C7BDFD01564B84E87BF9984009E_AdjustorThunk (void);
extern void SpherecastCommand_set_radius_mF2F0DB4FEDD1DFC396D3DFC52E7964616778F9E3_AdjustorThunk (void);
extern void SpherecastCommand_get_direction_m18300C7CEE58565AEC540BE249F11D5AE452CE87_AdjustorThunk (void);
extern void SpherecastCommand_set_direction_mAF24E6F239F768CE2F6D795AACCD79DC5F887042_AdjustorThunk (void);
extern void SpherecastCommand_get_distance_m12385D25AB25C8C3656E370DDCF50BD924499233_AdjustorThunk (void);
extern void SpherecastCommand_set_distance_m32AAFE7BE818D729124117A663F470379E4DF9DB_AdjustorThunk (void);
extern void SpherecastCommand_get_physicsScene_m3EE7B58FE0F80F8EC89EC5AFA548D6F605E92022_AdjustorThunk (void);
extern void SpherecastCommand_set_physicsScene_m6ED30632068593C2DB57FC49CBBE56B07DC0ABEC_AdjustorThunk (void);
extern void SpherecastCommand__ctor_m0D0BFD29A98093A4A98F2689EBAA7E326B1C2117_AdjustorThunk (void);
extern void SpherecastCommand__ctor_m4A2082D1C681B8685B4A1DF2E5EA0AB4403EBCFB_AdjustorThunk (void);
extern void SpherecastCommand_get_layerMask_m443973FC7B8DC0E48F13DE061F70E2E2AA8AF4C3_AdjustorThunk (void);
extern void SpherecastCommand_set_layerMask_m9B615EFE1A9D7057FA00E318B8D4CC38CEFC20A2_AdjustorThunk (void);
extern void CapsulecastCommand__ctor_m2B94B22D7233C5E4DD8B88A5D527336195F063F8_AdjustorThunk (void);
extern void CapsulecastCommand__ctor_m3485B6028BCC02B96DC64D039E9BBEE73154D200_AdjustorThunk (void);
extern void CapsulecastCommand_get_point1_m6DFFD9A6C8D4221269F06D50B437D5CF48529FE5_AdjustorThunk (void);
extern void CapsulecastCommand_set_point1_mB37C2BCF42C923D514AF9E78C699F85242EAA563_AdjustorThunk (void);
extern void CapsulecastCommand_get_point2_m78EC7D397D29EFCDD0E42CAD32C88947F4D4ABB4_AdjustorThunk (void);
extern void CapsulecastCommand_set_point2_mE75966408CD135BAF86B446AF5379ACB6FE98880_AdjustorThunk (void);
extern void CapsulecastCommand_get_radius_mD77A81F5114A1580EF1FF141C738560ACBA7DC13_AdjustorThunk (void);
extern void CapsulecastCommand_set_radius_mA968E8E31E09D5B0D861DFDB1D9D8621ACB3AB6C_AdjustorThunk (void);
extern void CapsulecastCommand_get_direction_m752A1EE8CC56AB2ECB9FCA56D65BF86DE4AE9242_AdjustorThunk (void);
extern void CapsulecastCommand_set_direction_m57DBE62F611302029343DA62526803E21A72C754_AdjustorThunk (void);
extern void CapsulecastCommand_get_distance_m24EE1EBEF3D9CAB31A067A509743ED813CF4A2D1_AdjustorThunk (void);
extern void CapsulecastCommand_set_distance_m9579828070C608533DDD1DBE530A5AEF4905EBC5_AdjustorThunk (void);
extern void CapsulecastCommand_get_physicsScene_m1763C75CA3E31975B0628E62E1A6681AC38C10E0_AdjustorThunk (void);
extern void CapsulecastCommand_set_physicsScene_m0FD54689DF616FDC760D2221B55182DF4CCE20A8_AdjustorThunk (void);
extern void CapsulecastCommand__ctor_mFBD0653BF8A3DD1F0C489979B014A2E5F5A36397_AdjustorThunk (void);
extern void CapsulecastCommand__ctor_mBDF287919FF6EE28169910A6B333947755D8BE92_AdjustorThunk (void);
extern void CapsulecastCommand_get_layerMask_m519E4018F493F32B213BCCFA0C6C6A5AEC8C331A_AdjustorThunk (void);
extern void CapsulecastCommand_set_layerMask_m57E3F255278021426F7D11B6ECE736A7706EFD44_AdjustorThunk (void);
extern void BoxcastCommand__ctor_m537698355BE6E7C5C981AF853DB6492FF3A6AFB5_AdjustorThunk (void);
extern void BoxcastCommand__ctor_m9ED746D4ACF937622D7CF6133CDE908A39BAE1AC_AdjustorThunk (void);
extern void BoxcastCommand_get_center_mD46099507F4864267BF537C80FCA33683EE0CB24_AdjustorThunk (void);
extern void BoxcastCommand_set_center_m7A8F2A037D6787571859B4049584B10A6D555C38_AdjustorThunk (void);
extern void BoxcastCommand_get_halfExtents_m133E7927EB8962266153E109D12A2B60BBE195F7_AdjustorThunk (void);
extern void BoxcastCommand_set_halfExtents_m82FC465A89C326F95F35B5F95ABD96B50551941D_AdjustorThunk (void);
extern void BoxcastCommand_get_orientation_m693D6DBA29CABE1B594AA6F2BE5310415815D544_AdjustorThunk (void);
extern void BoxcastCommand_set_orientation_m1CD41FB7539D597AF8E9FA9BC12318D918A46471_AdjustorThunk (void);
extern void BoxcastCommand_get_direction_m1C104750BEC1BE901952975E35449BC3F4830899_AdjustorThunk (void);
extern void BoxcastCommand_set_direction_mAB860B183A345132B99C3A5E8D2FADA5417E357E_AdjustorThunk (void);
extern void BoxcastCommand_get_distance_m1DE2D7596C33AE1B19A4309576A818A60D62FB5F_AdjustorThunk (void);
extern void BoxcastCommand_set_distance_mC6933F0893A7531667539B643842C85450412E98_AdjustorThunk (void);
extern void BoxcastCommand_get_physicsScene_mDD75C74B54E085D09972CB7894B19384A49AC3FF_AdjustorThunk (void);
extern void BoxcastCommand_set_physicsScene_mA447E367EC5936A5C621026E98986B109FFA2CD4_AdjustorThunk (void);
extern void BoxcastCommand__ctor_mFFDEA928E01C4FE4D034143AD706A9C994EE73B0_AdjustorThunk (void);
extern void BoxcastCommand__ctor_mCA72956F4705B234738488EC66724FD30B589746_AdjustorThunk (void);
extern void BoxcastCommand_get_layerMask_m86215B17A1CF8661C744DD5686B21B3F4D02EE1F_AdjustorThunk (void);
extern void BoxcastCommand_set_layerMask_m3E8588D218280C04C76F2C2DF0D38BCB54800F3C_AdjustorThunk (void);
extern void ClosestPointCommand__ctor_m4141AF23EA150FE427CCFDC8736B29EA79B0E462_AdjustorThunk (void);
extern void ClosestPointCommand__ctor_m28FE39F299728FB0D1F3C7FC6E96AECAEB314BC5_AdjustorThunk (void);
extern void ClosestPointCommand_get_point_m614B7DBBAE578B0F90DE6CE2C04581A7F4A9252C_AdjustorThunk (void);
extern void ClosestPointCommand_set_point_m081F4AAB8007B64EF2B0BEBEC1AAAA490E5F135A_AdjustorThunk (void);
extern void ClosestPointCommand_get_colliderInstanceID_m814CB755AFF21E5D8CE4335CA5138D4C2B1EC017_AdjustorThunk (void);
extern void ClosestPointCommand_set_colliderInstanceID_m033DF85E7ED333AA5F65AD31992180FC21A76464_AdjustorThunk (void);
extern void ClosestPointCommand_get_position_m1DDC44DAB526733668BAFA4EEF8BE8115251719D_AdjustorThunk (void);
extern void ClosestPointCommand_set_position_mB17F876ADAE3832D149A219B67CD98E24CB84BD5_AdjustorThunk (void);
extern void ClosestPointCommand_get_rotation_mC188C1910A3A53FD57844EA416A5BE34025BC7F0_AdjustorThunk (void);
extern void ClosestPointCommand_set_rotation_m9577993E95E2B9A5A6A118E3778700B1FB5919B0_AdjustorThunk (void);
extern void ClosestPointCommand_get_scale_m32B7E3E310D3850A838518F31B51BDB3797E7CCC_AdjustorThunk (void);
extern void ClosestPointCommand_set_scale_m2875D2E39D8F39FE955D2D59D551D95E3C0807FF_AdjustorThunk (void);
extern void OverlapSphereCommand__ctor_mA53F5911553913E86C4374212FA4F98849E53331_AdjustorThunk (void);
extern void OverlapSphereCommand__ctor_m77EF721239EB5B4701605ED24474EE4BF986F7EC_AdjustorThunk (void);
extern void OverlapSphereCommand_get_point_m8A4C6707D923231644CB12A12AC7CA949AEED768_AdjustorThunk (void);
extern void OverlapSphereCommand_set_point_mA7FA5A5E4E43BD1B19E7159A9A1AB826ED130424_AdjustorThunk (void);
extern void OverlapSphereCommand_get_radius_m6641206FCF8B06BDD09FE7FCF8B4DB86C9BDDE0D_AdjustorThunk (void);
extern void OverlapSphereCommand_set_radius_m6BE30815AD7DC52847CE5267903AD38FA1BAF97A_AdjustorThunk (void);
extern void OverlapSphereCommand_get_physicsScene_mADCB56464A1D9EBD73AA1093A25854746B2E307D_AdjustorThunk (void);
extern void OverlapSphereCommand_set_physicsScene_m34A25CC89E8645889C84F4A6A3F276BEFA987BD9_AdjustorThunk (void);
extern void OverlapBoxCommand__ctor_m0700FB9BF317EF23B875619048A36D2F8691C7A2_AdjustorThunk (void);
extern void OverlapBoxCommand__ctor_m456F1D2B75CD344E50931669D39A93DD3DD15C9B_AdjustorThunk (void);
extern void OverlapBoxCommand_get_center_mF002B687D58A1CD1F31D8F5B445E7D2356A5A33F_AdjustorThunk (void);
extern void OverlapBoxCommand_set_center_m95FB7FC7D91FADDC41CB513135C3CC08D0BB7490_AdjustorThunk (void);
extern void OverlapBoxCommand_get_halfExtents_m251067DF176F2EC71D8ECF6790BBD8ECAE57E3A5_AdjustorThunk (void);
extern void OverlapBoxCommand_set_halfExtents_m71E91162BF53AE2E003E9F17A477C97D7A37070A_AdjustorThunk (void);
extern void OverlapBoxCommand_get_orientation_m9B593B572EB7D04B8A64334B028B600D12C1552B_AdjustorThunk (void);
extern void OverlapBoxCommand_set_orientation_mF00A5CFDA9242A6738E3A8458A88A219E00DF54F_AdjustorThunk (void);
extern void OverlapBoxCommand_get_physicsScene_m143BE67D0DD6C1AF45F4F51A4B20E3A4BB35A687_AdjustorThunk (void);
extern void OverlapBoxCommand_set_physicsScene_m5C7EEAB11A4465E1CC58029647B3E271E1B43E98_AdjustorThunk (void);
extern void OverlapCapsuleCommand__ctor_mFC3B345057E39BADD029DB9CAE9AA5E32FFC72DC_AdjustorThunk (void);
extern void OverlapCapsuleCommand__ctor_m5ED84963BE98C5C9247885CC593FF291A67CFF2F_AdjustorThunk (void);
extern void OverlapCapsuleCommand_get_point0_m34A2DEDD42E8304E06C33C3BE4B8AFD20DAC32EF_AdjustorThunk (void);
extern void OverlapCapsuleCommand_set_point0_m42F1772676705667421D2D3211970154FF48BC32_AdjustorThunk (void);
extern void OverlapCapsuleCommand_get_point1_m8DA15C10389BEA183855D52C1BDABA9A8426AF7C_AdjustorThunk (void);
extern void OverlapCapsuleCommand_set_point1_m87C54B6484C5A7BA3AF7B9DB88AE1EE585DE812E_AdjustorThunk (void);
extern void OverlapCapsuleCommand_get_radius_mEC13C74A0C6683B6711F6C3A5EC9DF749159418B_AdjustorThunk (void);
extern void OverlapCapsuleCommand_set_radius_m6258361B0AC1E7C903A6F8EE119239688CE8CF87_AdjustorThunk (void);
extern void OverlapCapsuleCommand_get_physicsScene_mF8BF85E4E8670480B67CCC532078F93270064DD3_AdjustorThunk (void);
extern void OverlapCapsuleCommand_set_physicsScene_m052934A9F6A2A5097A72AC74D4968A18564CE3C8_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[285] = 
{
	{ 0x06000001, WheelFrictionCurve_get_extremumSlip_mA9ED9E7649E5CB7981D5F580800B14581AAE2274_AdjustorThunk },
	{ 0x06000002, WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E_AdjustorThunk },
	{ 0x06000003, WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34_AdjustorThunk },
	{ 0x06000004, WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E_AdjustorThunk },
	{ 0x06000005, WheelFrictionCurve_get_asymptoteSlip_m89B5E0129E6B43E765E97F76D198BD9EAC6CE755_AdjustorThunk },
	{ 0x06000006, WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C_AdjustorThunk },
	{ 0x06000007, WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F_AdjustorThunk },
	{ 0x06000008, WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43_AdjustorThunk },
	{ 0x06000009, WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6_AdjustorThunk },
	{ 0x0600000A, WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5_AdjustorThunk },
	{ 0x0600000B, SoftJointLimit_get_limit_m565D543DC9482F893A8C1F8582B9A06F7E287286_AdjustorThunk },
	{ 0x0600000C, SoftJointLimit_set_limit_m34B7F00528D7F5B03D2AC39E44AFD96F0EAADF1A_AdjustorThunk },
	{ 0x0600000D, SoftJointLimit_get_bounciness_m978E3102B620170A84C30EC15963B04564866DA8_AdjustorThunk },
	{ 0x0600000E, SoftJointLimit_set_bounciness_m1D956BA96C435546CF89A8936F684A3C2E3BB036_AdjustorThunk },
	{ 0x0600000F, SoftJointLimit_get_contactDistance_mD9CFEBFE02B773802773E53F9643C20E8C274020_AdjustorThunk },
	{ 0x06000010, SoftJointLimit_set_contactDistance_mDF20A5E81A2184F22CBA3007BB871BF87CB8FF2D_AdjustorThunk },
	{ 0x06000011, SoftJointLimit_get_spring_m8424406A064106C77453C7B24FACEC365ECAD991_AdjustorThunk },
	{ 0x06000012, SoftJointLimit_set_spring_mA96D8FDEA28FD24B2E3077ACA5159F74047F0EE6_AdjustorThunk },
	{ 0x06000013, SoftJointLimit_get_damper_mD68459A168731FF3AB023A227286A19D8B72DEF5_AdjustorThunk },
	{ 0x06000014, SoftJointLimit_set_damper_m3AF75ED0C45C84404FE3B4ED3850DD9A30704753_AdjustorThunk },
	{ 0x06000015, SoftJointLimit_get_bouncyness_m711720910DECFC3A5C77F0A24602A820659A418C_AdjustorThunk },
	{ 0x06000016, SoftJointLimit_set_bouncyness_m2AA2F20F2ABB85328D343253D8A3836E61EE2E67_AdjustorThunk },
	{ 0x06000017, SoftJointLimitSpring_get_spring_m04C597C18DD13494AFBAEB9348D7877EDAC0AA08_AdjustorThunk },
	{ 0x06000018, SoftJointLimitSpring_set_spring_m9A216142953ECC1CEE5080D603D18F9D1BD0A6EA_AdjustorThunk },
	{ 0x06000019, SoftJointLimitSpring_get_damper_m7B70F7492F1C75B68E879C484681AD00FDB771EE_AdjustorThunk },
	{ 0x0600001A, SoftJointLimitSpring_set_damper_mA86F8E250BA84A6DC3E84DC1A40319A39CD5CFD6_AdjustorThunk },
	{ 0x0600001B, JointDrive_get_positionSpring_m41EABECAFA44BAC2E33CE9FD278B3DEDE884585D_AdjustorThunk },
	{ 0x0600001C, JointDrive_set_positionSpring_mC928C6830ABEC56D68FB9B054DCD2A1A807EAD52_AdjustorThunk },
	{ 0x0600001D, JointDrive_get_positionDamper_mA22BFFB513E083AE41711EA6EA96D0424E7A9D1D_AdjustorThunk },
	{ 0x0600001E, JointDrive_set_positionDamper_m5D8426BF35A505ABE8FC5F09AA3127F5E90B2604_AdjustorThunk },
	{ 0x0600001F, JointDrive_get_maximumForce_mED9DD2A8EAACC262A83CED0991F2A42E78B2F3B8_AdjustorThunk },
	{ 0x06000020, JointDrive_set_maximumForce_mEB33B42E322E88853F6440113086E97A0C6E69F5_AdjustorThunk },
	{ 0x06000021, JointDrive_get_useAcceleration_mAFE42782DBC795844A8C698802064AAF34D12A45_AdjustorThunk },
	{ 0x06000022, JointDrive_set_useAcceleration_m04919856A177B9ECEEDA9178F64E85625C6CAC33_AdjustorThunk },
	{ 0x06000023, JointDrive_get_mode_m7012ADA832DB3EECCC4EBBD23B0C067F9AD97918_AdjustorThunk },
	{ 0x06000024, JointDrive_set_mode_m1C934E502FE9705DE2DC7C37257C10BAED74D394_AdjustorThunk },
	{ 0x06000025, JointMotor_get_targetVelocity_mDB63119AC6C3AF796785042AF466CD02D937820F_AdjustorThunk },
	{ 0x06000026, JointMotor_set_targetVelocity_m6F58E447E9C1F7812246ECD1CB8C2929D1CE86DE_AdjustorThunk },
	{ 0x06000027, JointMotor_get_force_mB4BDD8D40380A7E01E9C062BAADB1BE97F6A63FE_AdjustorThunk },
	{ 0x06000028, JointMotor_set_force_m66139CAD801991E3788835067C0D738EA000BFD3_AdjustorThunk },
	{ 0x06000029, JointMotor_get_freeSpin_m01BCFC597BF0DF5540378A79FAC1F8D0F4336683_AdjustorThunk },
	{ 0x0600002A, JointMotor_set_freeSpin_mC37B615961CCAC6A3DC75109476C8C46FF4E894C_AdjustorThunk },
	{ 0x0600002B, JointLimits_get_min_m3E8D3C572B30DA53262849D4D5BFFD9A77276FC8_AdjustorThunk },
	{ 0x0600002C, JointLimits_set_min_m6DCC6F92E715577794E36CD524989509D2A001AF_AdjustorThunk },
	{ 0x0600002D, JointLimits_get_max_m4E6BC6E5D320C4E2E7599852B19F4524D9549638_AdjustorThunk },
	{ 0x0600002E, JointLimits_set_max_m192F8B77417D548BF0162E651049DB1C4C1D81A0_AdjustorThunk },
	{ 0x0600002F, JointLimits_get_bounciness_m631282F7314872399F85C93F9827AA1F79BEFBAB_AdjustorThunk },
	{ 0x06000030, JointLimits_set_bounciness_mEB2995C51F0E0F6591D42881CB7CC69CF1835CC9_AdjustorThunk },
	{ 0x06000031, JointLimits_get_bounceMinVelocity_m2BF2A2C9471171604CC519C205D2BC738371C40A_AdjustorThunk },
	{ 0x06000032, JointLimits_set_bounceMinVelocity_m3D1D91AF20CBD28E66EF1BAA7AAB003BC9E671E1_AdjustorThunk },
	{ 0x06000033, JointLimits_get_contactDistance_mD306F20301F701B403B1D85EF984E958158F3717_AdjustorThunk },
	{ 0x06000034, JointLimits_set_contactDistance_mF8CC4376AC7E794C0E2FA1EBF035798EB82680E8_AdjustorThunk },
	{ 0x06000057, ArticulationReducedSpace_get_Item_m8E297D94FA09BCB4E45C045BF0411D67E183BF24_AdjustorThunk },
	{ 0x06000058, ArticulationReducedSpace_set_Item_m511249FEDD7DA1241235F2D78B17E495435141CB_AdjustorThunk },
	{ 0x06000059, ArticulationReducedSpace__ctor_m73747277F64DBDD2DD2C2027F7848AB29A735D0D_AdjustorThunk },
	{ 0x0600005A, ArticulationReducedSpace__ctor_m1993AEDD6D0F94169DDDE6D3E4BFEB4B1D2144EE_AdjustorThunk },
	{ 0x0600005B, ArticulationReducedSpace__ctor_mE85D365C229AA06F3E7D4FB73717088D7F20B461_AdjustorThunk },
	{ 0x0600005C, ArticulationJacobian__ctor_mD33412D6B8F30DA581549DF60FD9727204971574_AdjustorThunk },
	{ 0x0600005D, ArticulationJacobian_get_Item_mC4DCABA2709F24770BD58BBF007909757BF2AE16_AdjustorThunk },
	{ 0x0600005E, ArticulationJacobian_set_Item_m1CB6CCCC4FC036E19F9CECE28C6488245082C85D_AdjustorThunk },
	{ 0x0600005F, ArticulationJacobian_get_rows_mD319F639F0B37D27355015EAD8BC0BCC027D6EDD_AdjustorThunk },
	{ 0x06000060, ArticulationJacobian_set_rows_m1782AA659C927A76DCBE506D7EBB429F0DC16C8E_AdjustorThunk },
	{ 0x06000061, ArticulationJacobian_get_columns_m2CD8D3E79C5A0EE73DEF851ECBD6682FAA4738AB_AdjustorThunk },
	{ 0x06000062, ArticulationJacobian_set_columns_m911137E1ACF2BA000559FD77849C07E3B60E679E_AdjustorThunk },
	{ 0x06000063, ArticulationJacobian_get_elements_m6E8E68D8FF8F98B8D532397C519E3F433162264C_AdjustorThunk },
	{ 0x06000064, ArticulationJacobian_set_elements_m3D711A40DB5A05C254933079A71CFFC756D86074_AdjustorThunk },
	{ 0x06000234, ModifiableContactPair_get_colliderInstanceID_m555E31D2818CEDAC9D58538314D80701EDED2C9F_AdjustorThunk },
	{ 0x06000235, ModifiableContactPair_get_otherColliderInstanceID_m92593BF590D5F14345E4DA5AADF9D61D0C55D363_AdjustorThunk },
	{ 0x06000236, ModifiableContactPair_get_bodyInstanceID_m8B56E95EE29C2957E3166B3616B3E02AE8176D3B_AdjustorThunk },
	{ 0x06000237, ModifiableContactPair_get_otherBodyInstanceID_mE275D82AE73ED679018A57A87A04656598A587AF_AdjustorThunk },
	{ 0x06000238, ModifiableContactPair_get_bodyVelocity_mE291E12A45BF3D11E31252D5D0F48CFE9979878B_AdjustorThunk },
	{ 0x06000239, ModifiableContactPair_get_bodyAngularVelocity_mDE015C32239E67B14D5F0E74D6F0C6D1A12F2D97_AdjustorThunk },
	{ 0x0600023A, ModifiableContactPair_get_otherBodyVelocity_m08C3C21AB9B5FB55EBE3BE3FD34EA10D02830F46_AdjustorThunk },
	{ 0x0600023B, ModifiableContactPair_get_otherBodyAngularVelocity_mFEE2BB3A0CBAC44D35EDFE528810B1EF7779587F_AdjustorThunk },
	{ 0x0600023C, ModifiableContactPair_get_contactCount_mE6A959F3C3DAC88580404AE830E666A74AC9DAB7_AdjustorThunk },
	{ 0x0600023D, ModifiableContactPair_get_massProperties_m6E657FD7D82285D3993FF9085ABBCC4F0CF169E8_AdjustorThunk },
	{ 0x0600023E, ModifiableContactPair_set_massProperties_mD53EC6724C3F093A3C4C08BCCC1815A858B628DE_AdjustorThunk },
	{ 0x0600023F, ModifiableContactPair_GetPoint_mAD9A2FCD25D217AF868CDE3A8F3C6977F8D1B2F6_AdjustorThunk },
	{ 0x06000240, ModifiableContactPair_SetPoint_m3CD65095DE1222A9B7F1A57B4BB91A5B2ADE043A_AdjustorThunk },
	{ 0x06000241, ModifiableContactPair_GetNormal_m6C0A9110EFC0EF0E8485D9A1A63ED91A7347B681_AdjustorThunk },
	{ 0x06000242, ModifiableContactPair_SetNormal_m1D65EB3CA8B5BA26FDB3565CA9933DB5EE2EE959_AdjustorThunk },
	{ 0x06000243, ModifiableContactPair_GetSeparation_m5BE2C0DEBD531977AD9C56632F4F9E7CD157C9C7_AdjustorThunk },
	{ 0x06000244, ModifiableContactPair_SetSeparation_m9D69A9DF8A5F726E24C7224549944B54EED4D48F_AdjustorThunk },
	{ 0x06000245, ModifiableContactPair_GetTargetVelocity_m92DBB0CECE637554C12BCC9ECC6CCEFE1124648E_AdjustorThunk },
	{ 0x06000246, ModifiableContactPair_SetTargetVelocity_mA07021905C401183F59C7D3AB9B28A0E0EFBD7FB_AdjustorThunk },
	{ 0x06000247, ModifiableContactPair_GetBounciness_m42DF58A5A3BEB6E55836F4E91CC210E01D5101F6_AdjustorThunk },
	{ 0x06000248, ModifiableContactPair_SetBounciness_m5FFB3F3B7AA6BBD143E423570DAFFA0F4F86F74B_AdjustorThunk },
	{ 0x06000249, ModifiableContactPair_GetStaticFriction_m836099A4AED9C8519C9D977F30C3CAD83B069D16_AdjustorThunk },
	{ 0x0600024A, ModifiableContactPair_SetStaticFriction_m258ADDF7FB804005D5A008118E341F7FD27937C2_AdjustorThunk },
	{ 0x0600024B, ModifiableContactPair_GetDynamicFriction_mB4975A0337DEFF0DE340F3097E8E4BE8754FA9FE_AdjustorThunk },
	{ 0x0600024C, ModifiableContactPair_SetDynamicFriction_m80A5A32EC1E1232F2814760D4D244D03E63CE362_AdjustorThunk },
	{ 0x0600024D, ModifiableContactPair_GetMaxImpulse_m887290F5A2A460EE2F7A937D416A1819E280FA63_AdjustorThunk },
	{ 0x0600024E, ModifiableContactPair_SetMaxImpulse_mBAEBD045C31CD54B65389D988B51163C656AE77A_AdjustorThunk },
	{ 0x0600024F, ModifiableContactPair_IgnoreContact_m041CE733E6A9425E8F1EE1278DBC43A90B0226A8_AdjustorThunk },
	{ 0x06000250, ModifiableContactPair_GetFaceIndex_mCD63E169BF58DC041F1C14C732F24056E74B3461_AdjustorThunk },
	{ 0x06000251, ModifiableContactPair_GetContact_mEE629C8AC75FBD7A833BD9594A783D5A5B02BEFA_AdjustorThunk },
	{ 0x06000252, ModifiableContactPair_GetContactPatch_mFEA78CD88438A9D61844A643460C4BD8AB0FBC8F_AdjustorThunk },
	{ 0x0600026A, RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk },
	{ 0x0600026B, RaycastHit_get_colliderInstanceID_m4CEBF5D185F207B1F958A93EA62AF35BE889D758_AdjustorThunk },
	{ 0x0600026C, RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk },
	{ 0x0600026D, RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11_AdjustorThunk },
	{ 0x0600026E, RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk },
	{ 0x0600026F, RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B_AdjustorThunk },
	{ 0x06000270, RaycastHit_get_barycentricCoordinate_m15E866896213623E3E49B54F4273E343A50B1797_AdjustorThunk },
	{ 0x06000271, RaycastHit_set_barycentricCoordinate_m97B7F8D06D728E9881527E1C1AA4FCCADEEC4C64_AdjustorThunk },
	{ 0x06000272, RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk },
	{ 0x06000273, RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC_AdjustorThunk },
	{ 0x06000274, RaycastHit_get_triangleIndex_mA363EA340DC5E202DA8E9AC6DF7CCFA20D6EF72A_AdjustorThunk },
	{ 0x06000276, RaycastHit_get_textureCoord_m71F12781E6A806033B42B2D6D1D42DDA2069FE6D_AdjustorThunk },
	{ 0x06000277, RaycastHit_get_textureCoord2_m93F06D5875AE4C8EBD21B9E184CF4FE3117EF704_AdjustorThunk },
	{ 0x06000278, RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk },
	{ 0x06000279, RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk },
	{ 0x0600027A, RaycastHit_get_articulationBody_m19487EF6AFD5B39F682254E2EB47E3313C24357A_AdjustorThunk },
	{ 0x0600027B, RaycastHit_get_lightmapCoord_mDA52EAAC7C3E7170AFDE13DE7183E6CDD91D7BEE_AdjustorThunk },
	{ 0x0600027C, RaycastHit_get_textureCoord1_m62351A7A51BB86A5614D6B9BE49EE9A8DEF215AE_AdjustorThunk },
	{ 0x06000462, ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk },
	{ 0x06000463, ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk },
	{ 0x06000464, ContactPoint_get_impulse_mA8ABFB0DCF8F4E0004FF23106B15046FA5C923B8_AdjustorThunk },
	{ 0x06000465, ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6_AdjustorThunk },
	{ 0x06000466, ContactPoint_get_otherCollider_m717D0758D578C93C6CA26E2BA87325682B6C2550_AdjustorThunk },
	{ 0x06000467, ContactPoint_get_separation_m0017804537DC53F84EA4C76239BCE72B0C167B4E_AdjustorThunk },
	{ 0x06000468, ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B_AdjustorThunk },
	{ 0x06000469, PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk },
	{ 0x0600046C, PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk },
	{ 0x0600046D, PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk },
	{ 0x0600046E, PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk },
	{ 0x0600046F, PhysicsScene_IsValid_m74353C7AC7756A4E6B2F768551CA2D373EE28478_AdjustorThunk },
	{ 0x06000471, PhysicsScene_IsEmpty_mD7DECC3F1514241BEF94CE872FC58A5442FB014E_AdjustorThunk },
	{ 0x06000473, PhysicsScene_Simulate_m8F1DFA08BF5DBFBB1FF452ABC98E40C938EA1425_AdjustorThunk },
	{ 0x06000474, PhysicsScene_InterpolateBodies_m4F998486F63C793D362B6564A0D5850D50ED5270_AdjustorThunk },
	{ 0x06000475, PhysicsScene_ResetInterpolationPoses_mE48BDAB51AAFC8E8687B1BAD9F0036ECED7B2DE8_AdjustorThunk },
	{ 0x06000476, PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk },
	{ 0x06000478, PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk },
	{ 0x0600047A, PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk },
	{ 0x0600047E, PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35_AdjustorThunk },
	{ 0x06000480, PhysicsScene_CapsuleCast_mA250677E33E5D956F8E75905C348517BD23CA4AE_AdjustorThunk },
	{ 0x06000482, PhysicsScene_OverlapCapsule_m4BB3246109285CFA98D3FD21E37E1870A954B545_AdjustorThunk },
	{ 0x06000485, PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk },
	{ 0x06000487, PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk },
	{ 0x06000489, PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk },
	{ 0x0600048C, PhysicsScene_BoxCast_mD138F894448712F882AF7F95B2AD9052DC548D12_AdjustorThunk },
	{ 0x0600048D, PhysicsScene_BoxCast_m83D81817A2F4BDCE6D0C4130D49E79EBF885DEA2_AdjustorThunk },
	{ 0x0600048F, PhysicsScene_OverlapBox_mF98FE9D367F5938A0E23C60684BED711EB69CA03_AdjustorThunk },
	{ 0x06000490, PhysicsScene_OverlapBox_m8EF90D415B4EE966DEAD2DCC80DB94D6D6C46EF4_AdjustorThunk },
	{ 0x06000492, PhysicsScene_BoxCast_m5010295997A4CBDDA261F825837A35789B4461E0_AdjustorThunk },
	{ 0x06000493, PhysicsScene_BoxCast_m70E3EF843956491E83FA535A8ED5756D0A417F23_AdjustorThunk },
	{ 0x060004A5, ContactPairHeader_get_BodyInstanceID_m4E7EBAF5ADE2DA3D281C9A0F5B63A006F7241CE8_AdjustorThunk },
	{ 0x060004A6, ContactPairHeader_get_OtherBodyInstanceID_m6384A0571E81440B589E8F41D077553DF31A27C5_AdjustorThunk },
	{ 0x060004A7, ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA_AdjustorThunk },
	{ 0x060004A8, ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08_AdjustorThunk },
	{ 0x060004A9, ContactPairHeader_get_PairCount_mF7E6F40BD6A8ED2B041EA993CDC01185D78674FE_AdjustorThunk },
	{ 0x060004AA, ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5_AdjustorThunk },
	{ 0x060004AB, ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk },
	{ 0x060004AC, ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk },
	{ 0x060004AD, ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E_AdjustorThunk },
	{ 0x060004AE, ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3_AdjustorThunk },
	{ 0x060004AF, ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD_AdjustorThunk },
	{ 0x060004B0, ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256_AdjustorThunk },
	{ 0x060004B1, ContactPair_get_ContactCount_m1F6291D2B7A188DA4FF675734535B2699495857C_AdjustorThunk },
	{ 0x060004B2, ContactPair_get_ImpulseSum_mE97817B3288752026E30C8ED5C92D3595E890ACC_AdjustorThunk },
	{ 0x060004B3, ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39_AdjustorThunk },
	{ 0x060004B4, ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385_AdjustorThunk },
	{ 0x060004B5, ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8_AdjustorThunk },
	{ 0x060004B6, ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E_AdjustorThunk },
	{ 0x060004B7, ContactPair_ExtractContacts_mCE72C104A1CF1E3C6FF4A16AF0AE1FE52DD08A52_AdjustorThunk },
	{ 0x060004B8, ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk },
	{ 0x060004B9, ContactPair_CopyToNativeArray_m9396822D29611D9B83036C70E49017C007059435_AdjustorThunk },
	{ 0x060004BA, ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk },
	{ 0x060004BB, ContactPair_GetContactPointFaceIndex_m2906D14481C497D1DB39E1CDC323D978D64086E0_AdjustorThunk },
	{ 0x060004BC, ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk },
	{ 0x060004BF, ContactPairPoint_get_Position_mF767C5CD66E322293F064B17FD44F8CF8C356B54_AdjustorThunk },
	{ 0x060004C0, ContactPairPoint_get_Separation_m4F5BFDBA730F08CD17BF3C0352453280DB1486AD_AdjustorThunk },
	{ 0x060004C1, ContactPairPoint_get_Normal_mB796C363701DFD430C2395C45EF578B5226757B2_AdjustorThunk },
	{ 0x060004C2, ContactPairPoint_get_Impulse_m94991E8BF7F18A361EB08941CFA88A1304CE8079_AdjustorThunk },
	{ 0x060004C3, QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B_AdjustorThunk },
	{ 0x060004C5, ColliderHit_get_instanceID_mDB124C91153EC421E57B834CF93758677F4BDAFE_AdjustorThunk },
	{ 0x060004C6, ColliderHit_get_collider_m047EC40DD7F77CA33A3BB636B7694AA3117FE83D_AdjustorThunk },
	{ 0x060004C7, RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971_AdjustorThunk },
	{ 0x060004C8, RaycastCommand__ctor_mC95C1C19A37F20B3111AF893B935EA51969FB4A1_AdjustorThunk },
	{ 0x060004C9, RaycastCommand_get_from_mCB7D147B3FF37945CCB38E9A174411ECCA769909_AdjustorThunk },
	{ 0x060004CA, RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2_AdjustorThunk },
	{ 0x060004CB, RaycastCommand_get_direction_mD02651FAA5BEA161F8FB390257F3DA87C3C3F393_AdjustorThunk },
	{ 0x060004CC, RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D_AdjustorThunk },
	{ 0x060004CD, RaycastCommand_get_physicsScene_m8731C9B74E3FB41FD8063C35453E867C218C8F44_AdjustorThunk },
	{ 0x060004CE, RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946_AdjustorThunk },
	{ 0x060004CF, RaycastCommand_get_distance_mFA04832DA19FD26541315E88245805E610DECED4_AdjustorThunk },
	{ 0x060004D0, RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023_AdjustorThunk },
	{ 0x060004D4, RaycastCommand__ctor_m8DFCFD3175272180C884798DC278A8D9B81E1B38_AdjustorThunk },
	{ 0x060004D5, RaycastCommand__ctor_m5D55292413D4D916C28793CB0F6B22A67258D26D_AdjustorThunk },
	{ 0x060004D6, RaycastCommand_get_maxHits_m8F19093BDC596CD7D5228E6DE4ACF898FF04AC79_AdjustorThunk },
	{ 0x060004D7, RaycastCommand_set_maxHits_m85328D33D9A03350705F8E792AB835CCC9E0EF8D_AdjustorThunk },
	{ 0x060004D8, RaycastCommand_get_layerMask_mBF83D8981FDD45343B1E729854A78C0D59D3610F_AdjustorThunk },
	{ 0x060004D9, RaycastCommand_set_layerMask_m5927E9524F8A1105B915FB04E59FEE65E5D95E51_AdjustorThunk },
	{ 0x060004DB, SpherecastCommand__ctor_m04B04D1690849F029961341127EB025E040857C1_AdjustorThunk },
	{ 0x060004DC, SpherecastCommand__ctor_mEC47B62DAD3FE54941D68903C40327231E3DEEF3_AdjustorThunk },
	{ 0x060004DD, SpherecastCommand_get_origin_m0AB8181C1233B9E62CF46ED485DF5448BFE2B236_AdjustorThunk },
	{ 0x060004DE, SpherecastCommand_set_origin_m78010578C88B3BBBEA2732D6B101C9818E113A60_AdjustorThunk },
	{ 0x060004DF, SpherecastCommand_get_radius_m304FFC1538682C7BDFD01564B84E87BF9984009E_AdjustorThunk },
	{ 0x060004E0, SpherecastCommand_set_radius_mF2F0DB4FEDD1DFC396D3DFC52E7964616778F9E3_AdjustorThunk },
	{ 0x060004E1, SpherecastCommand_get_direction_m18300C7CEE58565AEC540BE249F11D5AE452CE87_AdjustorThunk },
	{ 0x060004E2, SpherecastCommand_set_direction_mAF24E6F239F768CE2F6D795AACCD79DC5F887042_AdjustorThunk },
	{ 0x060004E3, SpherecastCommand_get_distance_m12385D25AB25C8C3656E370DDCF50BD924499233_AdjustorThunk },
	{ 0x060004E4, SpherecastCommand_set_distance_m32AAFE7BE818D729124117A663F470379E4DF9DB_AdjustorThunk },
	{ 0x060004E5, SpherecastCommand_get_physicsScene_m3EE7B58FE0F80F8EC89EC5AFA548D6F605E92022_AdjustorThunk },
	{ 0x060004E6, SpherecastCommand_set_physicsScene_m6ED30632068593C2DB57FC49CBBE56B07DC0ABEC_AdjustorThunk },
	{ 0x060004EA, SpherecastCommand__ctor_m0D0BFD29A98093A4A98F2689EBAA7E326B1C2117_AdjustorThunk },
	{ 0x060004EB, SpherecastCommand__ctor_m4A2082D1C681B8685B4A1DF2E5EA0AB4403EBCFB_AdjustorThunk },
	{ 0x060004EC, SpherecastCommand_get_layerMask_m443973FC7B8DC0E48F13DE061F70E2E2AA8AF4C3_AdjustorThunk },
	{ 0x060004ED, SpherecastCommand_set_layerMask_m9B615EFE1A9D7057FA00E318B8D4CC38CEFC20A2_AdjustorThunk },
	{ 0x060004EF, CapsulecastCommand__ctor_m2B94B22D7233C5E4DD8B88A5D527336195F063F8_AdjustorThunk },
	{ 0x060004F0, CapsulecastCommand__ctor_m3485B6028BCC02B96DC64D039E9BBEE73154D200_AdjustorThunk },
	{ 0x060004F1, CapsulecastCommand_get_point1_m6DFFD9A6C8D4221269F06D50B437D5CF48529FE5_AdjustorThunk },
	{ 0x060004F2, CapsulecastCommand_set_point1_mB37C2BCF42C923D514AF9E78C699F85242EAA563_AdjustorThunk },
	{ 0x060004F3, CapsulecastCommand_get_point2_m78EC7D397D29EFCDD0E42CAD32C88947F4D4ABB4_AdjustorThunk },
	{ 0x060004F4, CapsulecastCommand_set_point2_mE75966408CD135BAF86B446AF5379ACB6FE98880_AdjustorThunk },
	{ 0x060004F5, CapsulecastCommand_get_radius_mD77A81F5114A1580EF1FF141C738560ACBA7DC13_AdjustorThunk },
	{ 0x060004F6, CapsulecastCommand_set_radius_mA968E8E31E09D5B0D861DFDB1D9D8621ACB3AB6C_AdjustorThunk },
	{ 0x060004F7, CapsulecastCommand_get_direction_m752A1EE8CC56AB2ECB9FCA56D65BF86DE4AE9242_AdjustorThunk },
	{ 0x060004F8, CapsulecastCommand_set_direction_m57DBE62F611302029343DA62526803E21A72C754_AdjustorThunk },
	{ 0x060004F9, CapsulecastCommand_get_distance_m24EE1EBEF3D9CAB31A067A509743ED813CF4A2D1_AdjustorThunk },
	{ 0x060004FA, CapsulecastCommand_set_distance_m9579828070C608533DDD1DBE530A5AEF4905EBC5_AdjustorThunk },
	{ 0x060004FB, CapsulecastCommand_get_physicsScene_m1763C75CA3E31975B0628E62E1A6681AC38C10E0_AdjustorThunk },
	{ 0x060004FC, CapsulecastCommand_set_physicsScene_m0FD54689DF616FDC760D2221B55182DF4CCE20A8_AdjustorThunk },
	{ 0x06000500, CapsulecastCommand__ctor_mFBD0653BF8A3DD1F0C489979B014A2E5F5A36397_AdjustorThunk },
	{ 0x06000501, CapsulecastCommand__ctor_mBDF287919FF6EE28169910A6B333947755D8BE92_AdjustorThunk },
	{ 0x06000502, CapsulecastCommand_get_layerMask_m519E4018F493F32B213BCCFA0C6C6A5AEC8C331A_AdjustorThunk },
	{ 0x06000503, CapsulecastCommand_set_layerMask_m57E3F255278021426F7D11B6ECE736A7706EFD44_AdjustorThunk },
	{ 0x06000505, BoxcastCommand__ctor_m537698355BE6E7C5C981AF853DB6492FF3A6AFB5_AdjustorThunk },
	{ 0x06000506, BoxcastCommand__ctor_m9ED746D4ACF937622D7CF6133CDE908A39BAE1AC_AdjustorThunk },
	{ 0x06000507, BoxcastCommand_get_center_mD46099507F4864267BF537C80FCA33683EE0CB24_AdjustorThunk },
	{ 0x06000508, BoxcastCommand_set_center_m7A8F2A037D6787571859B4049584B10A6D555C38_AdjustorThunk },
	{ 0x06000509, BoxcastCommand_get_halfExtents_m133E7927EB8962266153E109D12A2B60BBE195F7_AdjustorThunk },
	{ 0x0600050A, BoxcastCommand_set_halfExtents_m82FC465A89C326F95F35B5F95ABD96B50551941D_AdjustorThunk },
	{ 0x0600050B, BoxcastCommand_get_orientation_m693D6DBA29CABE1B594AA6F2BE5310415815D544_AdjustorThunk },
	{ 0x0600050C, BoxcastCommand_set_orientation_m1CD41FB7539D597AF8E9FA9BC12318D918A46471_AdjustorThunk },
	{ 0x0600050D, BoxcastCommand_get_direction_m1C104750BEC1BE901952975E35449BC3F4830899_AdjustorThunk },
	{ 0x0600050E, BoxcastCommand_set_direction_mAB860B183A345132B99C3A5E8D2FADA5417E357E_AdjustorThunk },
	{ 0x0600050F, BoxcastCommand_get_distance_m1DE2D7596C33AE1B19A4309576A818A60D62FB5F_AdjustorThunk },
	{ 0x06000510, BoxcastCommand_set_distance_mC6933F0893A7531667539B643842C85450412E98_AdjustorThunk },
	{ 0x06000511, BoxcastCommand_get_physicsScene_mDD75C74B54E085D09972CB7894B19384A49AC3FF_AdjustorThunk },
	{ 0x06000512, BoxcastCommand_set_physicsScene_mA447E367EC5936A5C621026E98986B109FFA2CD4_AdjustorThunk },
	{ 0x06000516, BoxcastCommand__ctor_mFFDEA928E01C4FE4D034143AD706A9C994EE73B0_AdjustorThunk },
	{ 0x06000517, BoxcastCommand__ctor_mCA72956F4705B234738488EC66724FD30B589746_AdjustorThunk },
	{ 0x06000518, BoxcastCommand_get_layerMask_m86215B17A1CF8661C744DD5686B21B3F4D02EE1F_AdjustorThunk },
	{ 0x06000519, BoxcastCommand_set_layerMask_m3E8588D218280C04C76F2C2DF0D38BCB54800F3C_AdjustorThunk },
	{ 0x0600051B, ClosestPointCommand__ctor_m4141AF23EA150FE427CCFDC8736B29EA79B0E462_AdjustorThunk },
	{ 0x0600051C, ClosestPointCommand__ctor_m28FE39F299728FB0D1F3C7FC6E96AECAEB314BC5_AdjustorThunk },
	{ 0x0600051D, ClosestPointCommand_get_point_m614B7DBBAE578B0F90DE6CE2C04581A7F4A9252C_AdjustorThunk },
	{ 0x0600051E, ClosestPointCommand_set_point_m081F4AAB8007B64EF2B0BEBEC1AAAA490E5F135A_AdjustorThunk },
	{ 0x0600051F, ClosestPointCommand_get_colliderInstanceID_m814CB755AFF21E5D8CE4335CA5138D4C2B1EC017_AdjustorThunk },
	{ 0x06000520, ClosestPointCommand_set_colliderInstanceID_m033DF85E7ED333AA5F65AD31992180FC21A76464_AdjustorThunk },
	{ 0x06000521, ClosestPointCommand_get_position_m1DDC44DAB526733668BAFA4EEF8BE8115251719D_AdjustorThunk },
	{ 0x06000522, ClosestPointCommand_set_position_mB17F876ADAE3832D149A219B67CD98E24CB84BD5_AdjustorThunk },
	{ 0x06000523, ClosestPointCommand_get_rotation_mC188C1910A3A53FD57844EA416A5BE34025BC7F0_AdjustorThunk },
	{ 0x06000524, ClosestPointCommand_set_rotation_m9577993E95E2B9A5A6A118E3778700B1FB5919B0_AdjustorThunk },
	{ 0x06000525, ClosestPointCommand_get_scale_m32B7E3E310D3850A838518F31B51BDB3797E7CCC_AdjustorThunk },
	{ 0x06000526, ClosestPointCommand_set_scale_m2875D2E39D8F39FE955D2D59D551D95E3C0807FF_AdjustorThunk },
	{ 0x0600052A, OverlapSphereCommand__ctor_mA53F5911553913E86C4374212FA4F98849E53331_AdjustorThunk },
	{ 0x0600052B, OverlapSphereCommand__ctor_m77EF721239EB5B4701605ED24474EE4BF986F7EC_AdjustorThunk },
	{ 0x0600052C, OverlapSphereCommand_get_point_m8A4C6707D923231644CB12A12AC7CA949AEED768_AdjustorThunk },
	{ 0x0600052D, OverlapSphereCommand_set_point_mA7FA5A5E4E43BD1B19E7159A9A1AB826ED130424_AdjustorThunk },
	{ 0x0600052E, OverlapSphereCommand_get_radius_m6641206FCF8B06BDD09FE7FCF8B4DB86C9BDDE0D_AdjustorThunk },
	{ 0x0600052F, OverlapSphereCommand_set_radius_m6BE30815AD7DC52847CE5267903AD38FA1BAF97A_AdjustorThunk },
	{ 0x06000530, OverlapSphereCommand_get_physicsScene_mADCB56464A1D9EBD73AA1093A25854746B2E307D_AdjustorThunk },
	{ 0x06000531, OverlapSphereCommand_set_physicsScene_m34A25CC89E8645889C84F4A6A3F276BEFA987BD9_AdjustorThunk },
	{ 0x06000535, OverlapBoxCommand__ctor_m0700FB9BF317EF23B875619048A36D2F8691C7A2_AdjustorThunk },
	{ 0x06000536, OverlapBoxCommand__ctor_m456F1D2B75CD344E50931669D39A93DD3DD15C9B_AdjustorThunk },
	{ 0x06000537, OverlapBoxCommand_get_center_mF002B687D58A1CD1F31D8F5B445E7D2356A5A33F_AdjustorThunk },
	{ 0x06000538, OverlapBoxCommand_set_center_m95FB7FC7D91FADDC41CB513135C3CC08D0BB7490_AdjustorThunk },
	{ 0x06000539, OverlapBoxCommand_get_halfExtents_m251067DF176F2EC71D8ECF6790BBD8ECAE57E3A5_AdjustorThunk },
	{ 0x0600053A, OverlapBoxCommand_set_halfExtents_m71E91162BF53AE2E003E9F17A477C97D7A37070A_AdjustorThunk },
	{ 0x0600053B, OverlapBoxCommand_get_orientation_m9B593B572EB7D04B8A64334B028B600D12C1552B_AdjustorThunk },
	{ 0x0600053C, OverlapBoxCommand_set_orientation_mF00A5CFDA9242A6738E3A8458A88A219E00DF54F_AdjustorThunk },
	{ 0x0600053D, OverlapBoxCommand_get_physicsScene_m143BE67D0DD6C1AF45F4F51A4B20E3A4BB35A687_AdjustorThunk },
	{ 0x0600053E, OverlapBoxCommand_set_physicsScene_m5C7EEAB11A4465E1CC58029647B3E271E1B43E98_AdjustorThunk },
	{ 0x06000542, OverlapCapsuleCommand__ctor_mFC3B345057E39BADD029DB9CAE9AA5E32FFC72DC_AdjustorThunk },
	{ 0x06000543, OverlapCapsuleCommand__ctor_m5ED84963BE98C5C9247885CC593FF291A67CFF2F_AdjustorThunk },
	{ 0x06000544, OverlapCapsuleCommand_get_point0_m34A2DEDD42E8304E06C33C3BE4B8AFD20DAC32EF_AdjustorThunk },
	{ 0x06000545, OverlapCapsuleCommand_set_point0_m42F1772676705667421D2D3211970154FF48BC32_AdjustorThunk },
	{ 0x06000546, OverlapCapsuleCommand_get_point1_m8DA15C10389BEA183855D52C1BDABA9A8426AF7C_AdjustorThunk },
	{ 0x06000547, OverlapCapsuleCommand_set_point1_m87C54B6484C5A7BA3AF7B9DB88AE1EE585DE812E_AdjustorThunk },
	{ 0x06000548, OverlapCapsuleCommand_get_radius_mEC13C74A0C6683B6711F6C3A5EC9DF749159418B_AdjustorThunk },
	{ 0x06000549, OverlapCapsuleCommand_set_radius_m6258361B0AC1E7C903A6F8EE119239688CE8CF87_AdjustorThunk },
	{ 0x0600054A, OverlapCapsuleCommand_get_physicsScene_mF8BF85E4E8670480B67CCC532078F93270064DD3_AdjustorThunk },
	{ 0x0600054B, OverlapCapsuleCommand_set_physicsScene_m052934A9F6A2A5097A72AC74D4968A18564CE3C8_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1358] = 
{
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4250,
	4250,
	4250,
	4250,
	4250,
	4358,
	4358,
	4358,
	4298,
	4168,
	3807,
	4364,
	4358,
	4358,
	4250,
	4250,
	4250,
	4250,
	4250,
	4250,
	4168,
	3807,
	4216,
	4250,
	4364,
	1905,
	2657,
	3337,
	3419,
	3419,
	4250,
	4358,
	4358,
	4250,
	3581,
	2745,
	3928,
	2850,
	2150,
	2734,
	2567,
	1976,
	4216,
	3852,
	4216,
	3852,
	4250,
	3881,
	4216,
	3852,
	4358,
	3980,
	4358,
	3980,
	4267,
	3896,
	4267,
	3896,
	4168,
	4168,
	3807,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4156,
	3793,
	4156,
	3793,
	4156,
	3793,
	4168,
	3807,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4230,
	3865,
	4230,
	3865,
	3669,
	4358,
	3669,
	4358,
	2890,
	3980,
	2890,
	3980,
	2890,
	3980,
	2890,
	3980,
	2194,
	2895,
	4358,
	3980,
	4358,
	3980,
	4298,
	3928,
	4168,
	3807,
	4358,
	3980,
	4358,
	4168,
	3807,
	4358,
	3980,
	4267,
	3896,
	4364,
	4364,
	4364,
	4168,
	4364,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4157,
	3794,
	4157,
	3794,
	4157,
	3794,
	4157,
	3794,
	4157,
	4216,
	4216,
	2893,
	3670,
	3670,
	3670,
	3397,
	3397,
	3419,
	3881,
	3419,
	3881,
	3419,
	3419,
	3881,
	3033,
	3419,
	3419,
	3419,
	2437,
	3419,
	3881,
	3419,
	3881,
	3419,
	2745,
	2745,
	1995,
	2745,
	2745,
	2745,
	4216,
	3852,
	4364,
	4168,
	3807,
	3881,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	2845,
	2845,
	2659,
	2659,
	2659,
	2659,
	1906,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	2657,
	2657,
	2657,
	2657,
	2657,
	8887,
	8887,
	8887,
	8887,
	6205,
	9086,
	8908,
	9064,
	8899,
	9064,
	8899,
	8993,
	8868,
	8993,
	8868,
	9064,
	8899,
	9064,
	8899,
	9018,
	8880,
	9018,
	8880,
	9018,
	8880,
	9064,
	8899,
	8993,
	8868,
	8993,
	8868,
	9035,
	6980,
	7975,
	6899,
	7928,
	7226,
	7261,
	5228,
	5739,
	6413,
	7361,
	4909,
	5226,
	5734,
	6410,
	5711,
	6388,
	7284,
	8227,
	5210,
	5709,
	6385,
	7283,
	5735,
	6411,
	7361,
	5225,
	5732,
	6410,
	4731,
	4912,
	5230,
	5740,
	4616,
	4730,
	4911,
	5229,
	4729,
	4908,
	5224,
	5731,
	5212,
	5712,
	6389,
	7284,
	4906,
	5211,
	5710,
	6387,
	4733,
	4914,
	5232,
	5742,
	6414,
	4617,
	4732,
	4913,
	5231,
	5741,
	5389,
	5409,
	6005,
	6737,
	7674,
	5980,
	6711,
	7646,
	8507,
	5282,
	5835,
	6546,
	7495,
	4968,
	5301,
	5852,
	6557,
	4656,
	4811,
	5033,
	5410,
	6006,
	4802,
	5032,
	5407,
	6003,
	6734,
	5393,
	5981,
	6712,
	7646,
	5021,
	5409,
	6005,
	6737,
	5390,
	6002,
	6733,
	7673,
	7996,
	8899,
	8889,
	8889,
	9089,
	8993,
	8868,
	8993,
	8868,
	4612,
	4612,
	6058,
	6060,
	9064,
	8899,
	9064,
	8899,
	8993,
	8868,
	9086,
	8908,
	5298,
	5849,
	6556,
	5209,
	5730,
	6409,
	7360,
	4637,
	4770,
	4970,
	5303,
	4769,
	4966,
	5299,
	5850,
	4953,
	5283,
	5836,
	6547,
	4905,
	5228,
	5739,
	6413,
	4904,
	5227,
	5738,
	6412,
	7361,
	5020,
	5408,
	6004,
	6736,
	7674,
	4967,
	5300,
	5851,
	6557,
	4638,
	5304,
	4971,
	4771,
	5854,
	4657,
	4812,
	5034,
	5411,
	6007,
	6738,
	4969,
	5302,
	5853,
	7912,
	7912,
	6897,
	7926,
	8503,
	8503,
	8397,
	8397,
	8501,
	8501,
	7811,
	7810,
	8218,
	7975,
	7975,
	7975,
	8836,
	8836,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	9018,
	8880,
	9018,
	8880,
	9064,
	8899,
	8993,
	8868,
	8887,
	8887,
	7010,
	8864,
	6621,
	4364,
	9089,
	8867,
	8867,
	8867,
	5325,
	4643,
	4774,
	4979,
	5325,
	7902,
	8867,
	8867,
	4604,
	5529,
	8867,
	8867,
	5165,
	4883,
	4882,
	4977,
	4641,
	7897,
	7940,
	7940,
	2798,
	2822,
	1231,
	3881,
	4216,
	4216,
	4216,
	4216,
	4358,
	4358,
	4358,
	4358,
	4216,
	4246,
	3878,
	3667,
	2751,
	3667,
	2751,
	3581,
	2745,
	3667,
	2751,
	3581,
	2745,
	3581,
	2745,
	3581,
	2745,
	3581,
	2745,
	3852,
	3632,
	3027,
	4151,
	4364,
	3881,
	7975,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4358,
	3980,
	4298,
	3928,
	4298,
	3928,
	4358,
	3980,
	4250,
	4216,
	4358,
	3980,
	4358,
	3980,
	4358,
	3980,
	4298,
	3928,
	4216,
	5441,
	4356,
	4356,
	4250,
	4250,
	4250,
	4356,
	4356,
	5095,
	4358,
	3980,
	4358,
	3980,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	3928,
	4168,
	3807,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4216,
	3852,
	4168,
	3807,
	4358,
	3980,
	4358,
	4168,
	3807,
	4267,
	3896,
	4358,
	3980,
	4168,
	3807,
	4358,
	3980,
	4267,
	3896,
	4216,
	3852,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	3980,
	3896,
	2893,
	4364,
	4168,
	4364,
	4364,
	4364,
	3670,
	3670,
	4216,
	3852,
	4230,
	3865,
	4230,
	3865,
	3669,
	4358,
	3669,
	4358,
	2890,
	3980,
	1515,
	2150,
	2890,
	3980,
	1515,
	2150,
	2890,
	3980,
	1515,
	2150,
	2890,
	3980,
	1515,
	2150,
	2194,
	2895,
	1011,
	1518,
	2151,
	2180,
	3670,
	1248,
	1087,
	1658,
	2347,
	1827,
	1827,
	2549,
	3530,
	4298,
	3928,
	4298,
	3928,
	3928,
	4168,
	3807,
	4216,
	3852,
	4216,
	3852,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	2657,
	2657,
	2657,
	3788,
	3788,
	3788,
	3788,
	2845,
	2845,
	2659,
	2659,
	2659,
	2659,
	1906,
	1005,
	1904,
	837,
	1758,
	4168,
	3807,
	4250,
	4250,
	4168,
	3807,
	4298,
	3928,
	3670,
	4166,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4230,
	3865,
	4230,
	3865,
	4250,
	3881,
	4250,
	3881,
	1835,
	1648,
	2180,
	3670,
	4364,
	2657,
	3788,
	3788,
	3788,
	3788,
	3788,
	1301,
	1904,
	3279,
	3453,
	4358,
	4168,
	4216,
	4298,
	3928,
	4298,
	3928,
	4358,
	3980,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4364,
	3079,
	3397,
	3788,
	3788,
	3788,
	4250,
	3881,
	4168,
	3807,
	4216,
	3852,
	4168,
	3807,
	4298,
	3928,
	4168,
	3807,
	4364,
	4358,
	3980,
	4298,
	3928,
	4298,
	3928,
	4216,
	3852,
	4356,
	4240,
	4364,
	3788,
	3788,
	3788,
	3788,
	4358,
	3980,
	4358,
	3980,
	4358,
	3980,
	4364,
	3788,
	3788,
	3788,
	3788,
	4358,
	3980,
	4298,
	3928,
	4364,
	3788,
	3788,
	4358,
	3980,
	4358,
	3980,
	4358,
	3980,
	4358,
	3980,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	4250,
	3881,
	4250,
	3881,
	4358,
	3980,
	4358,
	3980,
	4358,
	3980,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	2657,
	4358,
	4358,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	4224,
	3859,
	4223,
	3858,
	4226,
	3861,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4298,
	4298,
	4168,
	3807,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
	4364,
	4358,
	3980,
	4302,
	3930,
	4302,
	3930,
	4301,
	3929,
	4301,
	3929,
	4301,
	3929,
	4301,
	3929,
	4168,
	3807,
	4298,
	3928,
	4298,
	3928,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	4358,
	3980,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4302,
	3930,
	4302,
	3930,
	4302,
	3930,
	4301,
	3929,
	4301,
	3929,
	4301,
	3929,
	4301,
	3929,
	4301,
	3929,
	4358,
	3980,
	4358,
	3980,
	4222,
	3857,
	4222,
	3857,
	4222,
	3857,
	4267,
	3896,
	4358,
	3980,
	4216,
	3852,
	4222,
	3857,
	4222,
	3857,
	4222,
	3857,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	4358,
	4358,
	4358,
	4250,
	4250,
	4298,
	677,
	4250,
	7269,
	7269,
	4216,
	3185,
	3190,
	4168,
	8221,
	4168,
	8221,
	3928,
	4364,
	4364,
	710,
	5208,
	444,
	4903,
	468,
	4949,
	4533,
	4532,
	162,
	4547,
	171,
	4757,
	469,
	4614,
	4613,
	280,
	4630,
	289,
	4950,
	748,
	4534,
	4531,
	163,
	1088,
	4756,
	467,
	1742,
	4548,
	172,
	1149,
	8200,
	8200,
	5165,
	4886,
	4929,
	4525,
	4539,
	4744,
	4598,
	4624,
	4930,
	4524,
	4743,
	4537,
	8537,
	8537,
	7894,
	4216,
	4216,
	4250,
	4250,
	4216,
	4168,
	3027,
	3027,
	4216,
	4216,
	4250,
	4250,
	4216,
	4358,
	4168,
	4168,
	4168,
	4168,
	2432,
	2432,
	3701,
	3027,
	3632,
	3027,
	6488,
	6488,
	4358,
	4298,
	4358,
	4358,
	1335,
	9048,
	4216,
	4250,
	1545,
	993,
	4358,
	3980,
	4358,
	3980,
	4256,
	3886,
	4298,
	3928,
	5318,
	5880,
	4772,
	1027,
	653,
	4216,
	3852,
	4216,
	3852,
	4670,
	1023,
	651,
	4358,
	3980,
	4298,
	3928,
	4358,
	3980,
	4298,
	3928,
	4256,
	3886,
	5319,
	5881,
	4772,
	1024,
	652,
	4216,
	3852,
	4670,
	675,
	400,
	4358,
	3980,
	4358,
	3980,
	4298,
	3928,
	4358,
	3980,
	4298,
	3928,
	4256,
	3886,
	5314,
	5878,
	4772,
	676,
	401,
	4216,
	3852,
	4670,
	673,
	398,
	4358,
	3980,
	4358,
	3980,
	4267,
	3896,
	4358,
	3980,
	4298,
	3928,
	4256,
	3886,
	5313,
	5877,
	4772,
	674,
	399,
	4216,
	3852,
	4670,
	1020,
	1021,
	4358,
	3980,
	4216,
	3852,
	4358,
	3980,
	4267,
	3896,
	4358,
	3980,
	5879,
	4975,
	4828,
	2193,
	1493,
	4358,
	3980,
	4298,
	3928,
	4256,
	3886,
	5317,
	4772,
	4670,
	1544,
	992,
	4358,
	3980,
	4358,
	3980,
	4267,
	3896,
	4256,
	3886,
	5315,
	4772,
	4670,
	1546,
	994,
	4358,
	3980,
	4358,
	3980,
	4298,
	3928,
	4256,
	3886,
	5316,
	4772,
	4670,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_PhysicsModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule = 
{
	"UnityEngine.PhysicsModule.dll",
	1358,
	s_methodPointers,
	285,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_PhysicsModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
