﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ImageConversion_get_EnableLegacyPngGammaRuntimeLoadBehavior_mC16F9ADD6C7DF2E36AB900E7A6D86E6982F91FAE (void);
extern void ImageConversion_set_EnableLegacyPngGammaRuntimeLoadBehavior_mF7D243865120C6DE88A267861500104438736122 (void);
extern void ImageConversion_GetEnableLegacyPngGammaRuntimeLoadBehavior_m63AE75A7AFC9C41D27A6B94178E7710D52FFA153 (void);
extern void ImageConversion_SetEnableLegacyPngGammaRuntimeLoadBehavior_m9E019E2C743429C4DF1AA9FB2DBF24E657A341F3 (void);
extern void ImageConversion_EncodeToTGA_m02B0F7ED8D1BE1E9310C3E6799161ABB0C531143 (void);
extern void ImageConversion_EncodeToPNG_m0804AD31B83C653AEBB234F6CC31A02D4FA7C945 (void);
extern void ImageConversion_EncodeToJPG_mD0307B5DFF32A3FF39488E97B467F11AFE501F6F (void);
extern void ImageConversion_EncodeToJPG_mD3B358B8645CF85EBAD979554FFDE05A54A99804 (void);
extern void ImageConversion_EncodeToEXR_m56D716F2C64F0BFC69A81D1787EB9D3E42A2EABA (void);
extern void ImageConversion_EncodeToEXR_m349B17956EB17D3652ADB469C36C47A6105C901A (void);
extern void ImageConversion_LoadImage_m292ADCEED268A0A0AAD532BAB8D1710CF0FC8AEF (void);
extern void ImageConversion_LoadImage_m1797365F78319B68638DE8BB02836F8D60760041 (void);
extern void ImageConversion_EncodeArrayToTGA_m46D1398E6273A512B845E0BBC97B3D068A07521F (void);
extern void ImageConversion_EncodeArrayToPNG_mC4006BCEED5DAC8D1752B11564878E275F1D442F (void);
extern void ImageConversion_EncodeArrayToJPG_mBC82D5B80D4BF38B76C2D3B906E84BAA71275897 (void);
extern void ImageConversion_EncodeArrayToEXR_mCB5468E0C554B91D3460EF0883C8D8D8E604D662 (void);
extern void ImageConversion_UnsafeEncodeNativeArrayToTGA_m7983DE9909D1166CA4C32102A0F006ED6BCEBF86 (void);
extern void ImageConversion_UnsafeEncodeNativeArrayToPNG_m5ADDC56B7A9E423C4D99EBC74803A352DF263537 (void);
extern void ImageConversion_UnsafeEncodeNativeArrayToJPG_m19AB03A1F3E5019BEDA20CC0D3A15A760932E53D (void);
extern void ImageConversion_UnsafeEncodeNativeArrayToEXR_m6390A25535E43303DE56714C58EF682F6386D63D (void);
static Il2CppMethodPointer s_methodPointers[24] = 
{
	ImageConversion_get_EnableLegacyPngGammaRuntimeLoadBehavior_mC16F9ADD6C7DF2E36AB900E7A6D86E6982F91FAE,
	ImageConversion_set_EnableLegacyPngGammaRuntimeLoadBehavior_mF7D243865120C6DE88A267861500104438736122,
	ImageConversion_GetEnableLegacyPngGammaRuntimeLoadBehavior_m63AE75A7AFC9C41D27A6B94178E7710D52FFA153,
	ImageConversion_SetEnableLegacyPngGammaRuntimeLoadBehavior_m9E019E2C743429C4DF1AA9FB2DBF24E657A341F3,
	ImageConversion_EncodeToTGA_m02B0F7ED8D1BE1E9310C3E6799161ABB0C531143,
	ImageConversion_EncodeToPNG_m0804AD31B83C653AEBB234F6CC31A02D4FA7C945,
	ImageConversion_EncodeToJPG_mD0307B5DFF32A3FF39488E97B467F11AFE501F6F,
	ImageConversion_EncodeToJPG_mD3B358B8645CF85EBAD979554FFDE05A54A99804,
	ImageConversion_EncodeToEXR_m56D716F2C64F0BFC69A81D1787EB9D3E42A2EABA,
	ImageConversion_EncodeToEXR_m349B17956EB17D3652ADB469C36C47A6105C901A,
	ImageConversion_LoadImage_m292ADCEED268A0A0AAD532BAB8D1710CF0FC8AEF,
	ImageConversion_LoadImage_m1797365F78319B68638DE8BB02836F8D60760041,
	ImageConversion_EncodeArrayToTGA_m46D1398E6273A512B845E0BBC97B3D068A07521F,
	ImageConversion_EncodeArrayToPNG_mC4006BCEED5DAC8D1752B11564878E275F1D442F,
	ImageConversion_EncodeArrayToJPG_mBC82D5B80D4BF38B76C2D3B906E84BAA71275897,
	ImageConversion_EncodeArrayToEXR_mCB5468E0C554B91D3460EF0883C8D8D8E604D662,
	NULL,
	NULL,
	NULL,
	NULL,
	ImageConversion_UnsafeEncodeNativeArrayToTGA_m7983DE9909D1166CA4C32102A0F006ED6BCEBF86,
	ImageConversion_UnsafeEncodeNativeArrayToPNG_m5ADDC56B7A9E423C4D99EBC74803A352DF263537,
	ImageConversion_UnsafeEncodeNativeArrayToJPG_m19AB03A1F3E5019BEDA20CC0D3A15A760932E53D,
	ImageConversion_UnsafeEncodeNativeArrayToEXR_m6390A25535E43303DE56714C58EF682F6386D63D,
};
static const int32_t s_InvokerIndices[24] = 
{
	8993,
	8868,
	8993,
	8868,
	8505,
	8505,
	7626,
	8505,
	7626,
	8505,
	6366,
	7261,
	5361,
	5361,
	5004,
	5004,
	0,
	0,
	0,
	0,
	4876,
	4876,
	4712,
	4712,
};
static const Il2CppTokenRangePair s_rgctxIndices[4] = 
{
	{ 0x06000011, { 0, 5 } },
	{ 0x06000012, { 5, 5 } },
	{ 0x06000013, { 10, 5 } },
	{ 0x06000014, { 15, 5 } },
};
extern const uint32_t g_rgctx_NativeArray_1_t33CE4D0959E16946FE8E84059F75BBE9B0724829;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m4887AD264F4D96242B08CC811F707F743B113F6C;
extern const uint32_t g_rgctx_NativeArray_1_t33CE4D0959E16946FE8E84059F75BBE9B0724829;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t477036F96729B50F32B229E502473756B3213F87_m004A60B00ED34514EBB588DCC24F57BC25CE1C86;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_t477036F96729B50F32B229E502473756B3213F87_m1C68A824A4CFBD0F8F30B680E97F9CF1D2C5BC2A;
extern const uint32_t g_rgctx_NativeArray_1_t216A59A4DF0146750EEF35CB21ECEBBC93D4962E;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m2AD0B7E4196020672FC26B812B4C20E8F3C64877;
extern const uint32_t g_rgctx_NativeArray_1_t216A59A4DF0146750EEF35CB21ECEBBC93D4962E;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tF7CF13651E05C9339869093B9BAE0AD94733F672_m56059F351AE81A8002B3ED836B6756E549ADA3CD;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_tF7CF13651E05C9339869093B9BAE0AD94733F672_mD0AD97C99F9F67FF9EC299B43B3B34CA8CA36DEF;
extern const uint32_t g_rgctx_NativeArray_1_tB0D7F7910E0DA01A90068922338E961120C4FC99;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mE05D414187B08626396D2EF5EC2DCBFB3A50E108;
extern const uint32_t g_rgctx_NativeArray_1_tB0D7F7910E0DA01A90068922338E961120C4FC99;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t7F385DF47AE8A1EC870566CC984AE8566363EAB7_m519470E989AD2C4568D3904E5B6ED36A726772AE;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_t7F385DF47AE8A1EC870566CC984AE8566363EAB7_mFD221EF64968C641FC063BD7CDA732DDED160732;
extern const uint32_t g_rgctx_NativeArray_1_t488B799C4EA5CA5F287655B0042F3A376D148184;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mD68047164C7F1987AB21AD19FD7ED1D38A4EA8B5;
extern const uint32_t g_rgctx_NativeArray_1_t488B799C4EA5CA5F287655B0042F3A376D148184;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t1A4B1396B5548B6C78C39D3054EC60841F0954C9_mE37F3F1953AB46937FF93EA263B2210BF88FE302;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_t1A4B1396B5548B6C78C39D3054EC60841F0954C9_m5D36CF7F6382269DC747B77948B8387E69736EDA;
static const Il2CppRGCTXDefinition s_rgctxValues[20] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t33CE4D0959E16946FE8E84059F75BBE9B0724829 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m4887AD264F4D96242B08CC811F707F743B113F6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t33CE4D0959E16946FE8E84059F75BBE9B0724829 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t477036F96729B50F32B229E502473756B3213F87_m004A60B00ED34514EBB588DCC24F57BC25CE1C86 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_t477036F96729B50F32B229E502473756B3213F87_m1C68A824A4CFBD0F8F30B680E97F9CF1D2C5BC2A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t216A59A4DF0146750EEF35CB21ECEBBC93D4962E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m2AD0B7E4196020672FC26B812B4C20E8F3C64877 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t216A59A4DF0146750EEF35CB21ECEBBC93D4962E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tF7CF13651E05C9339869093B9BAE0AD94733F672_m56059F351AE81A8002B3ED836B6756E549ADA3CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_tF7CF13651E05C9339869093B9BAE0AD94733F672_mD0AD97C99F9F67FF9EC299B43B3B34CA8CA36DEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB0D7F7910E0DA01A90068922338E961120C4FC99 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mE05D414187B08626396D2EF5EC2DCBFB3A50E108 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tB0D7F7910E0DA01A90068922338E961120C4FC99 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t7F385DF47AE8A1EC870566CC984AE8566363EAB7_m519470E989AD2C4568D3904E5B6ED36A726772AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_t7F385DF47AE8A1EC870566CC984AE8566363EAB7_mFD221EF64968C641FC063BD7CDA732DDED160732 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t488B799C4EA5CA5F287655B0042F3A376D148184 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mD68047164C7F1987AB21AD19FD7ED1D38A4EA8B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t488B799C4EA5CA5F287655B0042F3A376D148184 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t1A4B1396B5548B6C78C39D3054EC60841F0954C9_mE37F3F1953AB46937FF93EA263B2210BF88FE302 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafeBufferPointerWithoutChecks_TisT_t1A4B1396B5548B6C78C39D3054EC60841F0954C9_m5D36CF7F6382269DC747B77948B8387E69736EDA },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_ImageConversionModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_ImageConversionModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_ImageConversionModule_CodeGenModule = 
{
	"UnityEngine.ImageConversionModule.dll",
	24,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	4,
	s_rgctxIndices,
	20,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_ImageConversionModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
