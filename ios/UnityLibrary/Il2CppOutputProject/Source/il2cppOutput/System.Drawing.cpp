﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct String_t;

IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RectangleF_t553237253FE9D1856528CAE15AF367839B925285_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Size_t9FCA8981191B4D1A693E50590137D636FEAC156D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral012F05B13B20BEDFF14849000C19F4828A82208C;
IL2CPP_EXTERN_C String_t* _stringLiteral05455CF614B372B63BAAE5B3BB8CB28EDA512C24;
IL2CPP_EXTERN_C String_t* _stringLiteral0598190E4CAF8BFE9CC29FA8911B602F97DF30C1;
IL2CPP_EXTERN_C String_t* _stringLiteral0736C2364D019F49B967109907ACAFC8E134D13F;
IL2CPP_EXTERN_C String_t* _stringLiteral0AD0D95D9C124F0BA012F53D28D6D43678A51EDD;
IL2CPP_EXTERN_C String_t* _stringLiteral0B431398DDDEBF0132D6419AAE2AC59DD6B6F20E;
IL2CPP_EXTERN_C String_t* _stringLiteral0B535425E31F9A78D26CC28DC04F8DC9EA4D4739;
IL2CPP_EXTERN_C String_t* _stringLiteral0C10E47675493434D04CD8025C8303AF94F7BA5A;
IL2CPP_EXTERN_C String_t* _stringLiteral0F12C5337B143080F20E16FF528A8631FF60582F;
IL2CPP_EXTERN_C String_t* _stringLiteral0F1F2B2F696A08F4188F054F9D7F2415FB3B042F;
IL2CPP_EXTERN_C String_t* _stringLiteral110CDD804B3158EF66476D120B8706123534E862;
IL2CPP_EXTERN_C String_t* _stringLiteral1344305D804E6235F649EAC33904B3E04885CE95;
IL2CPP_EXTERN_C String_t* _stringLiteral17122CB683A11086918016EA625EA4B9E3C92AEE;
IL2CPP_EXTERN_C String_t* _stringLiteral19F3ED72B8004679EAD8928FC583FCF37771A178;
IL2CPP_EXTERN_C String_t* _stringLiteral1BE632A60138E1072E4808E397A22C5396E2B6CB;
IL2CPP_EXTERN_C String_t* _stringLiteral2091D3C4DC644299973152A6B2716A01907F7022;
IL2CPP_EXTERN_C String_t* _stringLiteral20C624FDAFC2C8AC958192DD5C696888EA2CB775;
IL2CPP_EXTERN_C String_t* _stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF;
IL2CPP_EXTERN_C String_t* _stringLiteral21906D2648554F498F8DF2302454F2F9AB6C3AC0;
IL2CPP_EXTERN_C String_t* _stringLiteral219502BA9388110AF53C34EFBB646326A8EE8D77;
IL2CPP_EXTERN_C String_t* _stringLiteral241F9DFCCC69FF179AC0DDAEECDC02FCA7FFA27F;
IL2CPP_EXTERN_C String_t* _stringLiteral271DE06C3125CB3D128E9B0C31D9A30DC59CAE22;
IL2CPP_EXTERN_C String_t* _stringLiteral27FD14CFE32223C443750A097C6E310D28F90764;
IL2CPP_EXTERN_C String_t* _stringLiteral28F64DA50C40CA73C2D38F192648522A0C2D023C;
IL2CPP_EXTERN_C String_t* _stringLiteral290DC698DD60834F31D722825C4A7BBAFF8F01CD;
IL2CPP_EXTERN_C String_t* _stringLiteral2B28584F19667791CB6FB899A29064FF6B910B31;
IL2CPP_EXTERN_C String_t* _stringLiteral2BAE49BDE068FA80909F1B2FE5CCD6C635DD9783;
IL2CPP_EXTERN_C String_t* _stringLiteral2C24A61820906BE665D2924190B2C1F642F254FB;
IL2CPP_EXTERN_C String_t* _stringLiteral2E339AF5AD2B2E7A1A7FDA50106DC859ECC4947A;
IL2CPP_EXTERN_C String_t* _stringLiteral302CE177D88E4E74D5AEEC14206B1A9050593E27;
IL2CPP_EXTERN_C String_t* _stringLiteral32182C9C8F1CD1638F4715AC125953068FC92531;
IL2CPP_EXTERN_C String_t* _stringLiteral323A2BDC9DDFD95817E56A04E2050EC04FA08411;
IL2CPP_EXTERN_C String_t* _stringLiteral33FAB44F18ED183E4CECB1F8A6C206BA58E3C1B0;
IL2CPP_EXTERN_C String_t* _stringLiteral38C9A9D65F5DEF48C677D8025F8153DD886C10BE;
IL2CPP_EXTERN_C String_t* _stringLiteral395710F3950D8B53F8B903CDF0274AA42C1E8775;
IL2CPP_EXTERN_C String_t* _stringLiteral39E58E632C1A8F2E450CCCE1158818B95B6B2350;
IL2CPP_EXTERN_C String_t* _stringLiteral3B2973C6F2ABBB5326732A75BEAD9DFE3103D8CB;
IL2CPP_EXTERN_C String_t* _stringLiteral3B72EBEA2254B9AC84632477CCE5208A54AB3DB6;
IL2CPP_EXTERN_C String_t* _stringLiteral3BB5C9980B9F9AC685024B012CE3C271AB249444;
IL2CPP_EXTERN_C String_t* _stringLiteral3CF17B43840F4DF696FDA932250266608735A759;
IL2CPP_EXTERN_C String_t* _stringLiteral3D351D7BA8B569A50B150CE60AB5A663C70BF930;
IL2CPP_EXTERN_C String_t* _stringLiteral3EBAEC90DCDFE988DFBAB9A9959872F2F09EE1DE;
IL2CPP_EXTERN_C String_t* _stringLiteral3F0D924DEF6BED42CDABB27C4614BCBB5680EC4D;
IL2CPP_EXTERN_C String_t* _stringLiteral41907A6E361476AFE5A6C52331ED7F1DAC94D2F3;
IL2CPP_EXTERN_C String_t* _stringLiteral41A34E9EF07D662E61C60CD7D11AE7BF8EDD7152;
IL2CPP_EXTERN_C String_t* _stringLiteral41C26ACEA21963C1F3601EDCA3C371BF852E4358;
IL2CPP_EXTERN_C String_t* _stringLiteral43FED4F175990E894A41B54F0266A33295FFE681;
IL2CPP_EXTERN_C String_t* _stringLiteral44055C491501B7E10D2CB80FC027949A27E74AA1;
IL2CPP_EXTERN_C String_t* _stringLiteral450310C5C6F51D5E90678EBC0D7390558265C5E1;
IL2CPP_EXTERN_C String_t* _stringLiteral46CB7B4FA6C1DDF8D391959C2BD0078A9B4EAC04;
IL2CPP_EXTERN_C String_t* _stringLiteral48B5E296051D257454025C834B6DA9EC96DA5013;
IL2CPP_EXTERN_C String_t* _stringLiteral4A526846ED8E7424D62D9BF1C329EA6EDA654BB4;
IL2CPP_EXTERN_C String_t* _stringLiteral4BE278FACFFF94EB88E8F8D304BE5C892A0B4499;
IL2CPP_EXTERN_C String_t* _stringLiteral4CF7F5AC54361B319998BA47B1106BFF6123F432;
IL2CPP_EXTERN_C String_t* _stringLiteral4D77265CBB3D01FFD17B3547F57906C93C97BA88;
IL2CPP_EXTERN_C String_t* _stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30;
IL2CPP_EXTERN_C String_t* _stringLiteral4DE442E3D02390D5E94BC3F3A7C7DBADE341289C;
IL2CPP_EXTERN_C String_t* _stringLiteral4EFB2F599087284F86089262FF9D2527AEE0AEAB;
IL2CPP_EXTERN_C String_t* _stringLiteral52248D0514A42AF911E38FCF9F31F0101C002594;
IL2CPP_EXTERN_C String_t* _stringLiteral5244C2853B046EBAE0AC4FAFA7C55E62AEEE0A5C;
IL2CPP_EXTERN_C String_t* _stringLiteral52E4E95B00FBFE8143FAC52CA391655BEE331044;
IL2CPP_EXTERN_C String_t* _stringLiteral53D474787814D40B54CADBB99BD8A431AEEA37E3;
IL2CPP_EXTERN_C String_t* _stringLiteral55ED723E4BC2B4D72E786E53A98509A41902D188;
IL2CPP_EXTERN_C String_t* _stringLiteral587FB2C1FB44E7C7FDADCB4A73E7C47DD06E830E;
IL2CPP_EXTERN_C String_t* _stringLiteral5A9D9B95FC9D5DFFBC5C2846528F1795CFF0DAE8;
IL2CPP_EXTERN_C String_t* _stringLiteral5E0764CF096B981269891B5218E0580D03E013DD;
IL2CPP_EXTERN_C String_t* _stringLiteral5E8DAF4EBCBF622D1B6B9043708084AE061938DB;
IL2CPP_EXTERN_C String_t* _stringLiteral5E96CF3269BE855AD4B7787CB4BFDCCD9503BB46;
IL2CPP_EXTERN_C String_t* _stringLiteral5F73F4985A325836FF5D04C44C03258B9E6FDC02;
IL2CPP_EXTERN_C String_t* _stringLiteral6096F21D39223F974B3394FE3BE1660C20DADB86;
IL2CPP_EXTERN_C String_t* _stringLiteral66FA768C8B134A2DA8507E93692D9952B3210261;
IL2CPP_EXTERN_C String_t* _stringLiteral66FAE1C73947835F79972DE93673DD6B9FAD5D73;
IL2CPP_EXTERN_C String_t* _stringLiteral67E566203E89A9A4701A74FF36014F58FC6572B8;
IL2CPP_EXTERN_C String_t* _stringLiteral6853B00927EB8BAE6FD25AE411383457D8EC0D05;
IL2CPP_EXTERN_C String_t* _stringLiteral6AB44B2B53208D90CCC01BE36F24A7ACC0AB5ECC;
IL2CPP_EXTERN_C String_t* _stringLiteral6BCE9CFBB3DDE024BA504DEFC9A5389F08F2F861;
IL2CPP_EXTERN_C String_t* _stringLiteral6C1B56D0BF4BF636B04D0B173C77ADB302B6C69A;
IL2CPP_EXTERN_C String_t* _stringLiteral6DF3A796E043E70AC91E5E4B013152F95717CE32;
IL2CPP_EXTERN_C String_t* _stringLiteral6EFFD078F1626239C157AC80BC902E4699000DAA;
IL2CPP_EXTERN_C String_t* _stringLiteral6F3CF19A45A31A89284875B48DE1B03C2874C63B;
IL2CPP_EXTERN_C String_t* _stringLiteral70AB5F73DCEB6DEB9ECE458819D70FC772C6296D;
IL2CPP_EXTERN_C String_t* _stringLiteral70F2D3E776C2893EFBCD12D863C5C9061CFBA6DA;
IL2CPP_EXTERN_C String_t* _stringLiteral7548EEF854B1C1980BC34C0B46AB3E33A36D7F24;
IL2CPP_EXTERN_C String_t* _stringLiteral75ACE450AFE00C6C93EAAAEF5A42FE49354E4213;
IL2CPP_EXTERN_C String_t* _stringLiteral75DAD94535A79F2149221C6619F899F4BDE2C7F6;
IL2CPP_EXTERN_C String_t* _stringLiteral75DEBB9F18947C14AB8656D6C29123A95061D7D2;
IL2CPP_EXTERN_C String_t* _stringLiteral76AB908CDD5EF7A2D4F57130A4D42F372569D7BD;
IL2CPP_EXTERN_C String_t* _stringLiteral77FA365523C8AD1C1BCB07FE41D0BA9D232632F4;
IL2CPP_EXTERN_C String_t* _stringLiteral7A91FC89053B3ABA6813ED4E8A3B75D79142DB81;
IL2CPP_EXTERN_C String_t* _stringLiteral7D073E964821759431132E85BF3C37AB72FD45E2;
IL2CPP_EXTERN_C String_t* _stringLiteral7FB02E5455A8D3012B3C7686B742A0CE6288488C;
IL2CPP_EXTERN_C String_t* _stringLiteral8177A873F1E307476CCF7C25B7A1219F2BC0CA5E;
IL2CPP_EXTERN_C String_t* _stringLiteral820A8081D7E6F6A68CFF59F84F071A7B68FBCA0F;
IL2CPP_EXTERN_C String_t* _stringLiteral823BD7237B252430123C669DF50172C3478CA24D;
IL2CPP_EXTERN_C String_t* _stringLiteral865F575D2827CAB4A6107F1C1953BCD60940AAD3;
IL2CPP_EXTERN_C String_t* _stringLiteral87843CCD2168E01528B181F4DBBBABEB98FE76BB;
IL2CPP_EXTERN_C String_t* _stringLiteral882546522C61EDB1BFD7C83D9ADC5F1D7AA137D3;
IL2CPP_EXTERN_C String_t* _stringLiteral8A63A6A61851990C637265E340FD864936D3DE30;
IL2CPP_EXTERN_C String_t* _stringLiteral8B475A127DF19319F6E3ECA3DDC86892EBF036B4;
IL2CPP_EXTERN_C String_t* _stringLiteral8E68E9FAEC7150DBFFF4198365BD772DE872934A;
IL2CPP_EXTERN_C String_t* _stringLiteral8E86287EF33D5003838E1E14126934E9305D9F6F;
IL2CPP_EXTERN_C String_t* _stringLiteral8EBFB57153D6D454C440862E93E9EAAECF8169C2;
IL2CPP_EXTERN_C String_t* _stringLiteral8ED74C63CB510E5E55B8945290C72B22E26DAFDD;
IL2CPP_EXTERN_C String_t* _stringLiteral8FD4FCEBF431F8C3AB327ADDFEE08BEF51FAD0D7;
IL2CPP_EXTERN_C String_t* _stringLiteral91A8AFE46A5ED1EB95C129D3FA3D0C1615112215;
IL2CPP_EXTERN_C String_t* _stringLiteral929C4D72B7EAD0E41EE32DE35B9338AAE6CD646D;
IL2CPP_EXTERN_C String_t* _stringLiteral931DFDCE8A7F395390F2AC831102C486A12DB07C;
IL2CPP_EXTERN_C String_t* _stringLiteral9463439701A265CDBE9159C621695ADE6A0CCF80;
IL2CPP_EXTERN_C String_t* _stringLiteral9512EDB4588205F164BE2D61A383794552D84023;
IL2CPP_EXTERN_C String_t* _stringLiteral9547816FF9624B4D00B9ADC93C41F5D42ADD9A88;
IL2CPP_EXTERN_C String_t* _stringLiteral95837132224B3D0F6FB1A91C2CCDCA1EF4586AB2;
IL2CPP_EXTERN_C String_t* _stringLiteral9929259F1BEC1114A9E939209D94FE05BF251DEB;
IL2CPP_EXTERN_C String_t* _stringLiteral9DEBD00AA70D18037D67EC09001CD192A0E69BF3;
IL2CPP_EXTERN_C String_t* _stringLiteralA0AD878C07F219C432F8F75D7AFE71E38EB9BCE4;
IL2CPP_EXTERN_C String_t* _stringLiteralA0BD619A4AB7BEBB403BE6D96B78F767CBA22D1F;
IL2CPP_EXTERN_C String_t* _stringLiteralA18C5A45E2FFAD284E4FD71F8744D5E3ECF4FC4E;
IL2CPP_EXTERN_C String_t* _stringLiteralA1945A43C0BC18AC31B339BFB8D4FF5E314CE1D8;
IL2CPP_EXTERN_C String_t* _stringLiteralA401E49E342DADCE6CBB4D579113F37ECE4A65FA;
IL2CPP_EXTERN_C String_t* _stringLiteralA5E43D4B9D6BD45F1DE9FF7D9FC9BC6F5AF86D0F;
IL2CPP_EXTERN_C String_t* _stringLiteralAF25DC5A736EC63E7FFBDE734FC4EC9B0024A767;
IL2CPP_EXTERN_C String_t* _stringLiteralB04BD692036B5EE39D032DC402E0EC84CE96EFA4;
IL2CPP_EXTERN_C String_t* _stringLiteralB15077D6B86395A72F7C1318136CD64578FA08BF;
IL2CPP_EXTERN_C String_t* _stringLiteralB172CBE3B1EEEA5D73C5D6F86B5AC6FB85787A5B;
IL2CPP_EXTERN_C String_t* _stringLiteralB243E266B4DBD4EC5C0B7CCF9C3B261EA0914EDC;
IL2CPP_EXTERN_C String_t* _stringLiteralB26F50B338D34F3E42FFA9C2628393743D2AD2BE;
IL2CPP_EXTERN_C String_t* _stringLiteralB2EF4AEEC292D34024BC775BC0F17D425A1C0325;
IL2CPP_EXTERN_C String_t* _stringLiteralB3C05D8A35844E1E82D9FF2A8C83562939E1EDF2;
IL2CPP_EXTERN_C String_t* _stringLiteralB419CFD5B49CDCB4D22631C6EFC26C19DF54DC3C;
IL2CPP_EXTERN_C String_t* _stringLiteralB5591FED44C1E908120624CD6A53920A99F2AE6C;
IL2CPP_EXTERN_C String_t* _stringLiteralB57786DBD22383462EDC2C8795A1BCD1B42EF104;
IL2CPP_EXTERN_C String_t* _stringLiteralB5B01188820989C37668D01DDA4D84337C4908DA;
IL2CPP_EXTERN_C String_t* _stringLiteralB5D123B98CED46C8A93EB5109272E39C2E749A8F;
IL2CPP_EXTERN_C String_t* _stringLiteralB6C81523B38578661B0DD4F81E0B95C0B9BE9FE8;
IL2CPP_EXTERN_C String_t* _stringLiteralB87415793E420BE51BECDD9B9467178E96A6C0CB;
IL2CPP_EXTERN_C String_t* _stringLiteralB9D7F0A03F059AA445FF42791D72545EA8DAB816;
IL2CPP_EXTERN_C String_t* _stringLiteralBCE1B9B3A26E5887E1C2FE9CD2A9F75F95C2E76C;
IL2CPP_EXTERN_C String_t* _stringLiteralBDEF0B3A382F2F3429AD123B1A19C56BAE0DD491;
IL2CPP_EXTERN_C String_t* _stringLiteralBE7F62271E99120594C14A6751E46164C2CE8D11;
IL2CPP_EXTERN_C String_t* _stringLiteralC09EF62988F51243BEA57FECA94F99D6A970022B;
IL2CPP_EXTERN_C String_t* _stringLiteralC176B4F6F2542477D019DEF12376DA784C53AAFA;
IL2CPP_EXTERN_C String_t* _stringLiteralC3DAD0127402D548D48FBAC7503BF9B6A239ECD5;
IL2CPP_EXTERN_C String_t* _stringLiteralC7FF1563AC29CE31228BEA39A3E982A2DD0FEFA5;
IL2CPP_EXTERN_C String_t* _stringLiteralC8188B9969CCD005902DD40A5C1B757A849492A2;
IL2CPP_EXTERN_C String_t* _stringLiteralC94031F021F2E8412B7DC16F4595B455B9870D64;
IL2CPP_EXTERN_C String_t* _stringLiteralCBF11B46FFA55AA52B4DBA77FE572890C50A4069;
IL2CPP_EXTERN_C String_t* _stringLiteralCE58DF7288F2D7D138EF484F103F63F8338900DC;
IL2CPP_EXTERN_C String_t* _stringLiteralD27C6B0E9C85D828181CA2FC3DA98091727454AA;
IL2CPP_EXTERN_C String_t* _stringLiteralD2FF7DE3883BA8ABD1913E108B511AEF2F248BE2;
IL2CPP_EXTERN_C String_t* _stringLiteralD46AB28F4CB7304A48BE45B0E9733A49F12D34F3;
IL2CPP_EXTERN_C String_t* _stringLiteralD5A7822934818C74CCC27D5322FE06A8A85BB809;
IL2CPP_EXTERN_C String_t* _stringLiteralD5B8395A95C55B5D6A6839DC59F461657D616861;
IL2CPP_EXTERN_C String_t* _stringLiteralD66A2D950BA4DB0A42CEB7D044F1337D97F575A0;
IL2CPP_EXTERN_C String_t* _stringLiteralD750AC4A31A88BFABFB79199D90964548508D6AE;
IL2CPP_EXTERN_C String_t* _stringLiteralD827367980AA6324A2715E469EDF7468795A6043;
IL2CPP_EXTERN_C String_t* _stringLiteralD8D547D9186FC9B450955EE5D96C51B9FD719725;
IL2CPP_EXTERN_C String_t* _stringLiteralD9BEC06D5D13CCAD2D08D57408B68A65197B6754;
IL2CPP_EXTERN_C String_t* _stringLiteralDA2A5509631CCAB3ABAD39062D9CA88AF921DA70;
IL2CPP_EXTERN_C String_t* _stringLiteralDB3AF064A254C20AC699C2808DE33F07E9D62F76;
IL2CPP_EXTERN_C String_t* _stringLiteralDB784C0FDB5E2F468943EF04EB13EE0E6135421C;
IL2CPP_EXTERN_C String_t* _stringLiteralDD7F813AFFA11C5D660AF772366D87094F18A222;
IL2CPP_EXTERN_C String_t* _stringLiteralDDF2E08DDB12B62E0519E212E56B142FFD06236C;
IL2CPP_EXTERN_C String_t* _stringLiteralE166C9564FBDE461738077E3B1B506525EB6ACCC;
IL2CPP_EXTERN_C String_t* _stringLiteralE20EC277BE0A7094883B095CA89F48CFF920BF61;
IL2CPP_EXTERN_C String_t* _stringLiteralE33BA8AB936808BFEB09E2A93B2D2C01197E0934;
IL2CPP_EXTERN_C String_t* _stringLiteralE3526B2CCA00F6388CBBF7039AC0BB87B76979F1;
IL2CPP_EXTERN_C String_t* _stringLiteralE42DC5F663277F0424D761ACC71F58D6F3476DED;
IL2CPP_EXTERN_C String_t* _stringLiteralE62EC39CD6E04CA15D6D2B22D49E4C54CEE32C33;
IL2CPP_EXTERN_C String_t* _stringLiteralE652F005A18FEE6E57C72B1B75C3270E38022D2A;
IL2CPP_EXTERN_C String_t* _stringLiteralE7A03561ED4EA323E1B26AF1E6AFAE340B5E3C03;
IL2CPP_EXTERN_C String_t* _stringLiteralE8AD8176A507D7993FF997A0360FAFA905F25017;
IL2CPP_EXTERN_C String_t* _stringLiteralE905425FEC3EB8BC5470A0097B6C9ED17935A3BC;
IL2CPP_EXTERN_C String_t* _stringLiteralE9C3CBEAD848131252C3E498E15A1B2640B75390;
IL2CPP_EXTERN_C String_t* _stringLiteralE9F9FA7C14C11D58B549353D4F8DF9F13B8DC44C;
IL2CPP_EXTERN_C String_t* _stringLiteralEB769CC5B1CE3FFB9133E0ECB36DDB03B09C923D;
IL2CPP_EXTERN_C String_t* _stringLiteralEB97E6C0F51BEBEDC21E07D4F558AFC622F0CC05;
IL2CPP_EXTERN_C String_t* _stringLiteralEC019DC7707D96D2B0F4FDA1C55E6861179EF232;
IL2CPP_EXTERN_C String_t* _stringLiteralED4B98441B4EAD909FAA1D5FBC4C50726902AD3F;
IL2CPP_EXTERN_C String_t* _stringLiteralEE6D011859CE26164B1CA33BF42DEADD1C91820B;
IL2CPP_EXTERN_C String_t* _stringLiteralEEFCE128E6CB1067404CB4399D59B27313CC58DD;
IL2CPP_EXTERN_C String_t* _stringLiteralEFC5D3315D0EF731A6264E907EA8BCE49E2C475A;
IL2CPP_EXTERN_C String_t* _stringLiteralF0265246ADA3E39038EF0E73CF6F2889E09CC9D5;
IL2CPP_EXTERN_C String_t* _stringLiteralF0BCAC9E3908E817167A09A2EA06EAED4CA9569F;
IL2CPP_EXTERN_C String_t* _stringLiteralF12D0C2EA0E6A84A00E8D2A217A7087651D7FF62;
IL2CPP_EXTERN_C String_t* _stringLiteralF318A9CBF6133558944579D6309707D3FF4760E1;
IL2CPP_EXTERN_C String_t* _stringLiteralF5E7896C756978C566EFE0CFC5EE0E2915E744FB;
IL2CPP_EXTERN_C String_t* _stringLiteralF8BC581086533D3C86371776631F5ED48300FA59;
IL2CPP_EXTERN_C String_t* _stringLiteralFA15A1BEC79692B005F11F5228F52B198E35F0DE;
IL2CPP_EXTERN_C String_t* _stringLiteralFA18942F3C9F50CF877ED04B1DE8E2DABFD08741;
IL2CPP_EXTERN_C String_t* _stringLiteralFAEA585C93CC8A656C9B90659DEE7AE1EC03AF5F;
IL2CPP_EXTERN_C String_t* _stringLiteralFC0364E02AABC3EC57B1EFCB86BF8802604E2538;
IL2CPP_EXTERN_C const RuntimeMethod* Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_NameAndARGBValue_m95C91B9588C731AC546868DBD1F1C4F2B95D44AF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* HashHelpers__cctor_mA1680F8D1B4E1C62B6660FF8F4CB5852ED393F99_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A_RuntimeMethod_var;

struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t1A31CCF034A606D5F05DD70B687FD5261E546C42 
{
};
struct HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770  : public RuntimeObject
{
};
struct KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 
{
	String_t* ___name;
	int64_t ___value;
	int16_t ___knownColor;
	int16_t ___state;
};
struct Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_pinvoke
{
	char* ___name;
	int64_t ___value;
	int16_t ___knownColor;
	int16_t ___state;
};
struct Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_com
{
	Il2CppChar* ___name;
	int64_t ___value;
	int16_t ___knownColor;
	int16_t ___state;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Guid_t 
{
	int32_t ____a;
	int16_t ____b;
	int16_t ____c;
	uint8_t ____d;
	uint8_t ____e;
	uint8_t ____f;
	uint8_t ____g;
	uint8_t ____h;
	uint8_t ____i;
	uint8_t ____j;
	uint8_t ____k;
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE 
{
	int32_t ___x;
	int32_t ___y;
};
struct PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 
{
	float ___x;
	float ___y;
};
struct Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 
{
	int32_t ___x;
	int32_t ___y;
	int32_t ___width;
	int32_t ___height;
};
struct RectangleF_t553237253FE9D1856528CAE15AF367839B925285 
{
	float ___x;
	float ___y;
	float ___width;
	float ___height;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Size_t9FCA8981191B4D1A693E50590137D636FEAC156D 
{
	int32_t ___width;
	int32_t ___height;
};
struct SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD 
{
	float ___width;
	float ___height;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct KnownColor_t720877EEB93899C8C47B45BE456C329F7C59236E 
{
	int32_t ___value__;
};
struct HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_StaticFields
{
	int32_t ___RandomSeed;
};
struct KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___s_colorTable;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___s_colorNameTable;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Guid_t_StaticFields
{
	Guid_t ___Empty;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248  : public RuntimeArray
{
	ALIGN_FIELD (8) String_t* m_Items[1];

	inline String_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline String_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, String_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline String_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline String_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, String_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Guid_t Guid_NewGuid_m1F4894E8DC089811D6252148AD5858E58D43A7BD (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Guid_GetHashCode_m239B7679BB9ED5A207B3D2F858B5F30FFC455408 (Guid_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0 (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_colorTable, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_m918500C1EFB475181349A79989BB79BB36102894 (String_t* ___0_format, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___1_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Color_get_NameAndARGBValue_m95C91B9588C731AC546868DBD1F1C4F2B95D44AF (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE (int32_t ___0_color, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Convert_ToString_mD50A87BAAF57E646B5A7B8AE989EC2A6B8DC1057 (int64_t ___0_value, int32_t ___1_toBase, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97 (int32_t ___0_color, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Byte_ToString_mB80CE094B94215119578E4D796566E71D7277EE4 (uint8_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___0_values, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 ___0_left, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Int64_GetHashCode_mDB050BE2AC244D92B14D1DF725AAD279CDC48496 (int64_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Int16_GetHashCode_mCD0A167AC8E6ACC2235F12E00C0F9BDC6ED3B6E1 (int16_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8 (int32_t ___0_h1, int32_t ___1_h2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_inline (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_inline (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4 (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97 (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE ___0_left, Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951 (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5 (int32_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_inline (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_inline (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1 (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371 (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 ___0_left, PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2 (float* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972 (float* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2 (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 ___0_left, Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, RectangleF_t553237253FE9D1856528CAE15AF367839B925285 ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975 (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990 (RectangleF_t553237253FE9D1856528CAE15AF367839B925285 ___0_left, RectangleF_t553237253FE9D1856528CAE15AF367839B925285 ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8 (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_inline (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_inline (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06 (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, Size_t9FCA8981191B4D1A693E50590137D636FEAC156D ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D ___0_sz1, Size_t9FCA8981191B4D1A693E50590137D636FEAC156D ___1_sz2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424 (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2 (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_inline (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_inline (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8 (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD ___0_sz1, SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD ___1_sz2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835 (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8 (int32_t ___0_h1, int32_t ___1_h2, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = ___0_h1;
		int32_t L_1 = ___0_h1;
		int32_t L_2 = ___0_h1;
		int32_t L_3 = ___1_h2;
		return ((int32_t)(((int32_t)il2cpp_codegen_add(((int32_t)(((int32_t)(L_0<<5))|((int32_t)((uint32_t)L_1>>((int32_t)27))))), L_2))^L_3));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HashHelpers__cctor_mA1680F8D1B4E1C62B6660FF8F4CB5852ED393F99 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers__cctor_mA1680F8D1B4E1C62B6660FF8F4CB5852ED393F99_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Guid_t V_0;
	memset((&V_0), 0, sizeof(V_0));
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, HashHelpers__cctor_mA1680F8D1B4E1C62B6660FF8F4CB5852ED393F99_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Guid_t L_0;
		L_0 = Guid_NewGuid_m1F4894E8DC089811D6252148AD5858E58D43A7BD(NULL);
		V_0 = L_0;
		int32_t L_1;
		L_1 = Guid_GetHashCode_m239B7679BB9ED5A207B3D2F858B5F30FFC455408((&V_0), NULL);
		((HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_StaticFields*)il2cpp_codegen_static_fields_for(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var))->___RandomSeed = L_1;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = ((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorTable;
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10(NULL);
	}

IL_000c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, KnownColorTable_InitColorTable_mC6E537EF5340F0DE473FD4D8A168D0631C38CD10_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)175));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = L_0;
		KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0(L_1, NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_2 = L_1;
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)27)), (int32_t)((int32_t)16777215));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = L_2;
		NullCheck(L_3);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)28)), (int32_t)((int32_t)-984833));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = L_3;
		NullCheck(L_4);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)29)), (int32_t)((int32_t)-332841));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = L_4;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)30)), (int32_t)((int32_t)-16711681));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = L_5;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)31)), (int32_t)((int32_t)-8388652));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = L_6;
		NullCheck(L_7);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)32)), (int32_t)((int32_t)-983041));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_8 = L_7;
		NullCheck(L_8);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)33)), (int32_t)((int32_t)-657956));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_9 = L_8;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)34)), (int32_t)((int32_t)-6972));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_10 = L_9;
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)35)), (int32_t)((int32_t)-16777216));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_11 = L_10;
		NullCheck(L_11);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)36)), (int32_t)((int32_t)-5171));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = L_11;
		NullCheck(L_12);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)37)), (int32_t)((int32_t)-16776961));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_13 = L_12;
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)38)), (int32_t)((int32_t)-7722014));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_14 = L_13;
		NullCheck(L_14);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)39)), (int32_t)((int32_t)-5952982));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_15 = L_14;
		NullCheck(L_15);
		(L_15)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)40)), (int32_t)((int32_t)-2180985));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_16 = L_15;
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)41)), (int32_t)((int32_t)-10510688));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_17 = L_16;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)42)), (int32_t)((int32_t)-8388864));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_18 = L_17;
		NullCheck(L_18);
		(L_18)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)43)), (int32_t)((int32_t)-2987746));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_19 = L_18;
		NullCheck(L_19);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)44)), (int32_t)((int32_t)-32944));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_20 = L_19;
		NullCheck(L_20);
		(L_20)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)45)), (int32_t)((int32_t)-10185235));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_21 = L_20;
		NullCheck(L_21);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)46)), (int32_t)((int32_t)-1828));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_22 = L_21;
		NullCheck(L_22);
		(L_22)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)47)), (int32_t)((int32_t)-2354116));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_23 = L_22;
		NullCheck(L_23);
		(L_23)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)48)), (int32_t)((int32_t)-16711681));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_24 = L_23;
		NullCheck(L_24);
		(L_24)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)49)), (int32_t)((int32_t)-16777077));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_25 = L_24;
		NullCheck(L_25);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)50)), (int32_t)((int32_t)-16741493));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_26 = L_25;
		NullCheck(L_26);
		(L_26)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)51)), (int32_t)((int32_t)-4684277));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_27 = L_26;
		NullCheck(L_27);
		(L_27)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)52)), (int32_t)((int32_t)-5658199));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_28 = L_27;
		NullCheck(L_28);
		(L_28)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)53)), (int32_t)((int32_t)-16751616));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_29 = L_28;
		NullCheck(L_29);
		(L_29)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)54)), (int32_t)((int32_t)-4343957));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_30 = L_29;
		NullCheck(L_30);
		(L_30)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)55)), (int32_t)((int32_t)-7667573));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_31 = L_30;
		NullCheck(L_31);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)56)), (int32_t)((int32_t)-11179217));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_32 = L_31;
		NullCheck(L_32);
		(L_32)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)57)), (int32_t)((int32_t)-29696));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_33 = L_32;
		NullCheck(L_33);
		(L_33)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)58)), (int32_t)((int32_t)-6737204));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_34 = L_33;
		NullCheck(L_34);
		(L_34)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)59)), (int32_t)((int32_t)-7667712));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_35 = L_34;
		NullCheck(L_35);
		(L_35)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)60)), (int32_t)((int32_t)-1468806));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_36 = L_35;
		NullCheck(L_36);
		(L_36)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)61)), (int32_t)((int32_t)-7357301));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_37 = L_36;
		NullCheck(L_37);
		(L_37)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)62)), (int32_t)((int32_t)-12042869));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_38 = L_37;
		NullCheck(L_38);
		(L_38)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)63)), (int32_t)((int32_t)-13676721));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_39 = L_38;
		NullCheck(L_39);
		(L_39)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)64)), (int32_t)((int32_t)-16724271));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_40 = L_39;
		NullCheck(L_40);
		(L_40)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)65)), (int32_t)((int32_t)-7077677));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_41 = L_40;
		NullCheck(L_41);
		(L_41)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)66)), (int32_t)((int32_t)-60269));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_42 = L_41;
		NullCheck(L_42);
		(L_42)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)67)), (int32_t)((int32_t)-16728065));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_43 = L_42;
		NullCheck(L_43);
		(L_43)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)68)), (int32_t)((int32_t)-9868951));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_44 = L_43;
		NullCheck(L_44);
		(L_44)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)69)), (int32_t)((int32_t)-14774017));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_45 = L_44;
		NullCheck(L_45);
		(L_45)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)70)), (int32_t)((int32_t)-5103070));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_46 = L_45;
		NullCheck(L_46);
		(L_46)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)71)), (int32_t)((int32_t)-1296));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_47 = L_46;
		NullCheck(L_47);
		(L_47)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)72)), (int32_t)((int32_t)-14513374));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_48 = L_47;
		NullCheck(L_48);
		(L_48)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)73)), (int32_t)((int32_t)-65281));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_49 = L_48;
		NullCheck(L_49);
		(L_49)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)74)), (int32_t)((int32_t)-2302756));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_50 = L_49;
		NullCheck(L_50);
		(L_50)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)75)), (int32_t)((int32_t)-460545));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_51 = L_50;
		NullCheck(L_51);
		(L_51)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)76)), (int32_t)((int32_t)-10496));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_52 = L_51;
		NullCheck(L_52);
		(L_52)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)77)), (int32_t)((int32_t)-2448096));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_53 = L_52;
		NullCheck(L_53);
		(L_53)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)78)), (int32_t)((int32_t)-8355712));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_54 = L_53;
		NullCheck(L_54);
		(L_54)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)79)), (int32_t)((int32_t)-16744448));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_55 = L_54;
		NullCheck(L_55);
		(L_55)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)80)), (int32_t)((int32_t)-5374161));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_56 = L_55;
		NullCheck(L_56);
		(L_56)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)81)), (int32_t)((int32_t)-983056));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_57 = L_56;
		NullCheck(L_57);
		(L_57)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)82)), (int32_t)((int32_t)-38476));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_58 = L_57;
		NullCheck(L_58);
		(L_58)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)83)), (int32_t)((int32_t)-3318692));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_59 = L_58;
		NullCheck(L_59);
		(L_59)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)84)), (int32_t)((int32_t)-11861886));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_60 = L_59;
		NullCheck(L_60);
		(L_60)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)85)), (int32_t)((int32_t)-16));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_61 = L_60;
		NullCheck(L_61);
		(L_61)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)86)), (int32_t)((int32_t)-989556));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_62 = L_61;
		NullCheck(L_62);
		(L_62)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)87)), (int32_t)((int32_t)-1644806));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_63 = L_62;
		NullCheck(L_63);
		(L_63)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)88)), (int32_t)((int32_t)-3851));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_64 = L_63;
		NullCheck(L_64);
		(L_64)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)89)), (int32_t)((int32_t)-8586240));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_65 = L_64;
		NullCheck(L_65);
		(L_65)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)90)), (int32_t)((int32_t)-1331));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_66 = L_65;
		NullCheck(L_66);
		(L_66)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)91)), (int32_t)((int32_t)-5383962));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_67 = L_66;
		NullCheck(L_67);
		(L_67)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)92)), (int32_t)((int32_t)-1015680));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_68 = L_67;
		NullCheck(L_68);
		(L_68)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)93)), (int32_t)((int32_t)-2031617));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_69 = L_68;
		NullCheck(L_69);
		(L_69)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)94)), (int32_t)((int32_t)-329006));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_70 = L_69;
		NullCheck(L_70);
		(L_70)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)95)), (int32_t)((int32_t)-2894893));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_71 = L_70;
		NullCheck(L_71);
		(L_71)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)96)), (int32_t)((int32_t)-7278960));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_72 = L_71;
		NullCheck(L_72);
		(L_72)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)97)), (int32_t)((int32_t)-18751));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_73 = L_72;
		NullCheck(L_73);
		(L_73)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)98)), (int32_t)((int32_t)-24454));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_74 = L_73;
		NullCheck(L_74);
		(L_74)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)99)), (int32_t)((int32_t)-14634326));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_75 = L_74;
		NullCheck(L_75);
		(L_75)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)100)), (int32_t)((int32_t)-7876870));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_76 = L_75;
		NullCheck(L_76);
		(L_76)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)101)), (int32_t)((int32_t)-8943463));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_77 = L_76;
		NullCheck(L_77);
		(L_77)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)102)), (int32_t)((int32_t)-5192482));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_78 = L_77;
		NullCheck(L_78);
		(L_78)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)103)), (int32_t)((int32_t)-32));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_79 = L_78;
		NullCheck(L_79);
		(L_79)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)104)), (int32_t)((int32_t)-16711936));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_80 = L_79;
		NullCheck(L_80);
		(L_80)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)105)), (int32_t)((int32_t)-13447886));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_81 = L_80;
		NullCheck(L_81);
		(L_81)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)106)), (int32_t)((int32_t)-331546));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_82 = L_81;
		NullCheck(L_82);
		(L_82)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)107)), (int32_t)((int32_t)-65281));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_83 = L_82;
		NullCheck(L_83);
		(L_83)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)108)), (int32_t)((int32_t)-8388608));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_84 = L_83;
		NullCheck(L_84);
		(L_84)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)109)), (int32_t)((int32_t)-10039894));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_85 = L_84;
		NullCheck(L_85);
		(L_85)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)110)), (int32_t)((int32_t)-16777011));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_86 = L_85;
		NullCheck(L_86);
		(L_86)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)111)), (int32_t)((int32_t)-4565549));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_87 = L_86;
		NullCheck(L_87);
		(L_87)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)112)), (int32_t)((int32_t)-7114533));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_88 = L_87;
		NullCheck(L_88);
		(L_88)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)113)), (int32_t)((int32_t)-12799119));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_89 = L_88;
		NullCheck(L_89);
		(L_89)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)114)), (int32_t)((int32_t)-8689426));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_90 = L_89;
		NullCheck(L_90);
		(L_90)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)115)), (int32_t)((int32_t)-16713062));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_91 = L_90;
		NullCheck(L_91);
		(L_91)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)116)), (int32_t)((int32_t)-12004916));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_92 = L_91;
		NullCheck(L_92);
		(L_92)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)117)), (int32_t)((int32_t)-3730043));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_93 = L_92;
		NullCheck(L_93);
		(L_93)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)118)), (int32_t)((int32_t)-15132304));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_94 = L_93;
		NullCheck(L_94);
		(L_94)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)119)), (int32_t)((int32_t)-655366));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_95 = L_94;
		NullCheck(L_95);
		(L_95)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)120)), (int32_t)((int32_t)-6943));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_96 = L_95;
		NullCheck(L_96);
		(L_96)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)121)), (int32_t)((int32_t)-6987));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_97 = L_96;
		NullCheck(L_97);
		(L_97)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)122)), (int32_t)((int32_t)-8531));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_98 = L_97;
		NullCheck(L_98);
		(L_98)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)123)), (int32_t)((int32_t)-16777088));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_99 = L_98;
		NullCheck(L_99);
		(L_99)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)124)), (int32_t)((int32_t)-133658));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_100 = L_99;
		NullCheck(L_100);
		(L_100)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)125)), (int32_t)((int32_t)-8355840));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_101 = L_100;
		NullCheck(L_101);
		(L_101)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)126)), (int32_t)((int32_t)-9728477));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_102 = L_101;
		NullCheck(L_102);
		(L_102)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)127)), (int32_t)((int32_t)-23296));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_103 = L_102;
		NullCheck(L_103);
		(L_103)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)128)), (int32_t)((int32_t)-47872));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_104 = L_103;
		NullCheck(L_104);
		(L_104)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)129)), (int32_t)((int32_t)-2461482));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_105 = L_104;
		NullCheck(L_105);
		(L_105)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)130)), (int32_t)((int32_t)-1120086));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_106 = L_105;
		NullCheck(L_106);
		(L_106)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)131)), (int32_t)((int32_t)-6751336));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_107 = L_106;
		NullCheck(L_107);
		(L_107)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)132)), (int32_t)((int32_t)-5247250));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_108 = L_107;
		NullCheck(L_108);
		(L_108)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)133)), (int32_t)((int32_t)-2396013));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_109 = L_108;
		NullCheck(L_109);
		(L_109)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)134)), (int32_t)((int32_t)-4139));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_110 = L_109;
		NullCheck(L_110);
		(L_110)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)135)), (int32_t)((int32_t)-9543));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_111 = L_110;
		NullCheck(L_111);
		(L_111)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)136)), (int32_t)((int32_t)-3308225));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_112 = L_111;
		NullCheck(L_112);
		(L_112)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)137)), (int32_t)((int32_t)-16181));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_113 = L_112;
		NullCheck(L_113);
		(L_113)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)138)), (int32_t)((int32_t)-2252579));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_114 = L_113;
		NullCheck(L_114);
		(L_114)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)139)), (int32_t)((int32_t)-5185306));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_115 = L_114;
		NullCheck(L_115);
		(L_115)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)140)), (int32_t)((int32_t)-8388480));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_116 = L_115;
		NullCheck(L_116);
		(L_116)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)141)), (int32_t)((int32_t)-65536));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_117 = L_116;
		NullCheck(L_117);
		(L_117)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)142)), (int32_t)((int32_t)-4419697));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_118 = L_117;
		NullCheck(L_118);
		(L_118)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)143)), (int32_t)((int32_t)-12490271));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_119 = L_118;
		NullCheck(L_119);
		(L_119)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)144)), (int32_t)((int32_t)-7650029));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_120 = L_119;
		NullCheck(L_120);
		(L_120)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)145)), (int32_t)((int32_t)-360334));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_121 = L_120;
		NullCheck(L_121);
		(L_121)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)146)), (int32_t)((int32_t)-744352));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_122 = L_121;
		NullCheck(L_122);
		(L_122)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)147)), (int32_t)((int32_t)-13726889));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_123 = L_122;
		NullCheck(L_123);
		(L_123)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)148)), (int32_t)((int32_t)-2578));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_124 = L_123;
		NullCheck(L_124);
		(L_124)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)149)), (int32_t)((int32_t)-6270419));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_125 = L_124;
		NullCheck(L_125);
		(L_125)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)150)), (int32_t)((int32_t)-4144960));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_126 = L_125;
		NullCheck(L_126);
		(L_126)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)151)), (int32_t)((int32_t)-7876885));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_127 = L_126;
		NullCheck(L_127);
		(L_127)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)152)), (int32_t)((int32_t)-9807155));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_128 = L_127;
		NullCheck(L_128);
		(L_128)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)153)), (int32_t)((int32_t)-9404272));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_129 = L_128;
		NullCheck(L_129);
		(L_129)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)154)), (int32_t)((int32_t)-1286));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_130 = L_129;
		NullCheck(L_130);
		(L_130)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)155)), (int32_t)((int32_t)-16711809));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_131 = L_130;
		NullCheck(L_131);
		(L_131)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)156)), (int32_t)((int32_t)-12156236));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_132 = L_131;
		NullCheck(L_132);
		(L_132)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)157)), (int32_t)((int32_t)-2968436));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_133 = L_132;
		NullCheck(L_133);
		(L_133)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)158)), (int32_t)((int32_t)-16744320));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_134 = L_133;
		NullCheck(L_134);
		(L_134)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)159)), (int32_t)((int32_t)-2572328));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_135 = L_134;
		NullCheck(L_135);
		(L_135)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)160)), (int32_t)((int32_t)-40121));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_136 = L_135;
		NullCheck(L_136);
		(L_136)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)161)), (int32_t)((int32_t)-12525360));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_137 = L_136;
		NullCheck(L_137);
		(L_137)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)162)), (int32_t)((int32_t)-1146130));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_138 = L_137;
		NullCheck(L_138);
		(L_138)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)163)), (int32_t)((int32_t)-663885));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_139 = L_138;
		NullCheck(L_139);
		(L_139)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)164)), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_140 = L_139;
		NullCheck(L_140);
		(L_140)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)165)), (int32_t)((int32_t)-657931));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_141 = L_140;
		NullCheck(L_141);
		(L_141)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)166)), (int32_t)((int32_t)-256));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_142 = L_141;
		NullCheck(L_142);
		(L_142)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)167)), (int32_t)((int32_t)-6632142));
		((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorTable = L_142;
		Il2CppCodeGenWriteBarrier((void**)(&((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorTable), (void*)L_142);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = ((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorNameTable;
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83(NULL);
	}

IL_000c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral012F05B13B20BEDFF14849000C19F4828A82208C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral05455CF614B372B63BAAE5B3BB8CB28EDA512C24);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0598190E4CAF8BFE9CC29FA8911B602F97DF30C1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0736C2364D019F49B967109907ACAFC8E134D13F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0AD0D95D9C124F0BA012F53D28D6D43678A51EDD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0B431398DDDEBF0132D6419AAE2AC59DD6B6F20E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0B535425E31F9A78D26CC28DC04F8DC9EA4D4739);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0C10E47675493434D04CD8025C8303AF94F7BA5A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0F12C5337B143080F20E16FF528A8631FF60582F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0F1F2B2F696A08F4188F054F9D7F2415FB3B042F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral110CDD804B3158EF66476D120B8706123534E862);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1344305D804E6235F649EAC33904B3E04885CE95);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral17122CB683A11086918016EA625EA4B9E3C92AEE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral19F3ED72B8004679EAD8928FC583FCF37771A178);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1BE632A60138E1072E4808E397A22C5396E2B6CB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2091D3C4DC644299973152A6B2716A01907F7022);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral20C624FDAFC2C8AC958192DD5C696888EA2CB775);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral21906D2648554F498F8DF2302454F2F9AB6C3AC0);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral219502BA9388110AF53C34EFBB646326A8EE8D77);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral241F9DFCCC69FF179AC0DDAEECDC02FCA7FFA27F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral271DE06C3125CB3D128E9B0C31D9A30DC59CAE22);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral27FD14CFE32223C443750A097C6E310D28F90764);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral28F64DA50C40CA73C2D38F192648522A0C2D023C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral290DC698DD60834F31D722825C4A7BBAFF8F01CD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2B28584F19667791CB6FB899A29064FF6B910B31);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2C24A61820906BE665D2924190B2C1F642F254FB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2E339AF5AD2B2E7A1A7FDA50106DC859ECC4947A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral302CE177D88E4E74D5AEEC14206B1A9050593E27);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral32182C9C8F1CD1638F4715AC125953068FC92531);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral323A2BDC9DDFD95817E56A04E2050EC04FA08411);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral33FAB44F18ED183E4CECB1F8A6C206BA58E3C1B0);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral38C9A9D65F5DEF48C677D8025F8153DD886C10BE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral395710F3950D8B53F8B903CDF0274AA42C1E8775);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral39E58E632C1A8F2E450CCCE1158818B95B6B2350);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3B2973C6F2ABBB5326732A75BEAD9DFE3103D8CB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3B72EBEA2254B9AC84632477CCE5208A54AB3DB6);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3BB5C9980B9F9AC685024B012CE3C271AB249444);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3CF17B43840F4DF696FDA932250266608735A759);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3D351D7BA8B569A50B150CE60AB5A663C70BF930);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3EBAEC90DCDFE988DFBAB9A9959872F2F09EE1DE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3F0D924DEF6BED42CDABB27C4614BCBB5680EC4D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral41907A6E361476AFE5A6C52331ED7F1DAC94D2F3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral41A34E9EF07D662E61C60CD7D11AE7BF8EDD7152);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral41C26ACEA21963C1F3601EDCA3C371BF852E4358);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral43FED4F175990E894A41B54F0266A33295FFE681);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral450310C5C6F51D5E90678EBC0D7390558265C5E1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral46CB7B4FA6C1DDF8D391959C2BD0078A9B4EAC04);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral48B5E296051D257454025C834B6DA9EC96DA5013);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4A526846ED8E7424D62D9BF1C329EA6EDA654BB4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4BE278FACFFF94EB88E8F8D304BE5C892A0B4499);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4CF7F5AC54361B319998BA47B1106BFF6123F432);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D77265CBB3D01FFD17B3547F57906C93C97BA88);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4DE442E3D02390D5E94BC3F3A7C7DBADE341289C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4EFB2F599087284F86089262FF9D2527AEE0AEAB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral52248D0514A42AF911E38FCF9F31F0101C002594);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5244C2853B046EBAE0AC4FAFA7C55E62AEEE0A5C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral53D474787814D40B54CADBB99BD8A431AEEA37E3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral55ED723E4BC2B4D72E786E53A98509A41902D188);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral587FB2C1FB44E7C7FDADCB4A73E7C47DD06E830E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5A9D9B95FC9D5DFFBC5C2846528F1795CFF0DAE8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5E0764CF096B981269891B5218E0580D03E013DD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5E8DAF4EBCBF622D1B6B9043708084AE061938DB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5E96CF3269BE855AD4B7787CB4BFDCCD9503BB46);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5F73F4985A325836FF5D04C44C03258B9E6FDC02);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6096F21D39223F974B3394FE3BE1660C20DADB86);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral66FA768C8B134A2DA8507E93692D9952B3210261);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral66FAE1C73947835F79972DE93673DD6B9FAD5D73);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral67E566203E89A9A4701A74FF36014F58FC6572B8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6853B00927EB8BAE6FD25AE411383457D8EC0D05);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6AB44B2B53208D90CCC01BE36F24A7ACC0AB5ECC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6BCE9CFBB3DDE024BA504DEFC9A5389F08F2F861);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6C1B56D0BF4BF636B04D0B173C77ADB302B6C69A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6DF3A796E043E70AC91E5E4B013152F95717CE32);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6EFFD078F1626239C157AC80BC902E4699000DAA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6F3CF19A45A31A89284875B48DE1B03C2874C63B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral70AB5F73DCEB6DEB9ECE458819D70FC772C6296D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral70F2D3E776C2893EFBCD12D863C5C9061CFBA6DA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7548EEF854B1C1980BC34C0B46AB3E33A36D7F24);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral75ACE450AFE00C6C93EAAAEF5A42FE49354E4213);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral75DAD94535A79F2149221C6619F899F4BDE2C7F6);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral75DEBB9F18947C14AB8656D6C29123A95061D7D2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral76AB908CDD5EF7A2D4F57130A4D42F372569D7BD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral77FA365523C8AD1C1BCB07FE41D0BA9D232632F4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7A91FC89053B3ABA6813ED4E8A3B75D79142DB81);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7D073E964821759431132E85BF3C37AB72FD45E2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7FB02E5455A8D3012B3C7686B742A0CE6288488C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8177A873F1E307476CCF7C25B7A1219F2BC0CA5E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral820A8081D7E6F6A68CFF59F84F071A7B68FBCA0F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral823BD7237B252430123C669DF50172C3478CA24D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral865F575D2827CAB4A6107F1C1953BCD60940AAD3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral87843CCD2168E01528B181F4DBBBABEB98FE76BB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral882546522C61EDB1BFD7C83D9ADC5F1D7AA137D3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8A63A6A61851990C637265E340FD864936D3DE30);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8B475A127DF19319F6E3ECA3DDC86892EBF036B4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8E68E9FAEC7150DBFFF4198365BD772DE872934A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8E86287EF33D5003838E1E14126934E9305D9F6F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8EBFB57153D6D454C440862E93E9EAAECF8169C2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8ED74C63CB510E5E55B8945290C72B22E26DAFDD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8FD4FCEBF431F8C3AB327ADDFEE08BEF51FAD0D7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral91A8AFE46A5ED1EB95C129D3FA3D0C1615112215);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral929C4D72B7EAD0E41EE32DE35B9338AAE6CD646D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral931DFDCE8A7F395390F2AC831102C486A12DB07C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9463439701A265CDBE9159C621695ADE6A0CCF80);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9512EDB4588205F164BE2D61A383794552D84023);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9547816FF9624B4D00B9ADC93C41F5D42ADD9A88);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral95837132224B3D0F6FB1A91C2CCDCA1EF4586AB2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9929259F1BEC1114A9E939209D94FE05BF251DEB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9DEBD00AA70D18037D67EC09001CD192A0E69BF3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA0AD878C07F219C432F8F75D7AFE71E38EB9BCE4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA0BD619A4AB7BEBB403BE6D96B78F767CBA22D1F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA18C5A45E2FFAD284E4FD71F8744D5E3ECF4FC4E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA1945A43C0BC18AC31B339BFB8D4FF5E314CE1D8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA5E43D4B9D6BD45F1DE9FF7D9FC9BC6F5AF86D0F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralAF25DC5A736EC63E7FFBDE734FC4EC9B0024A767);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB04BD692036B5EE39D032DC402E0EC84CE96EFA4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB15077D6B86395A72F7C1318136CD64578FA08BF);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB26F50B338D34F3E42FFA9C2628393743D2AD2BE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB2EF4AEEC292D34024BC775BC0F17D425A1C0325);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB3C05D8A35844E1E82D9FF2A8C83562939E1EDF2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB419CFD5B49CDCB4D22631C6EFC26C19DF54DC3C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB5591FED44C1E908120624CD6A53920A99F2AE6C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB57786DBD22383462EDC2C8795A1BCD1B42EF104);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB6C81523B38578661B0DD4F81E0B95C0B9BE9FE8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB87415793E420BE51BECDD9B9467178E96A6C0CB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB9D7F0A03F059AA445FF42791D72545EA8DAB816);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBCE1B9B3A26E5887E1C2FE9CD2A9F75F95C2E76C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBDEF0B3A382F2F3429AD123B1A19C56BAE0DD491);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBE7F62271E99120594C14A6751E46164C2CE8D11);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC09EF62988F51243BEA57FECA94F99D6A970022B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC176B4F6F2542477D019DEF12376DA784C53AAFA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC3DAD0127402D548D48FBAC7503BF9B6A239ECD5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC7FF1563AC29CE31228BEA39A3E982A2DD0FEFA5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC8188B9969CCD005902DD40A5C1B757A849492A2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC94031F021F2E8412B7DC16F4595B455B9870D64);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCBF11B46FFA55AA52B4DBA77FE572890C50A4069);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCE58DF7288F2D7D138EF484F103F63F8338900DC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD27C6B0E9C85D828181CA2FC3DA98091727454AA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD2FF7DE3883BA8ABD1913E108B511AEF2F248BE2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD46AB28F4CB7304A48BE45B0E9733A49F12D34F3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD5A7822934818C74CCC27D5322FE06A8A85BB809);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD5B8395A95C55B5D6A6839DC59F461657D616861);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD66A2D950BA4DB0A42CEB7D044F1337D97F575A0);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD750AC4A31A88BFABFB79199D90964548508D6AE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD827367980AA6324A2715E469EDF7468795A6043);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD8D547D9186FC9B450955EE5D96C51B9FD719725);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD9BEC06D5D13CCAD2D08D57408B68A65197B6754);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA2A5509631CCAB3ABAD39062D9CA88AF921DA70);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDB3AF064A254C20AC699C2808DE33F07E9D62F76);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDB784C0FDB5E2F468943EF04EB13EE0E6135421C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDD7F813AFFA11C5D660AF772366D87094F18A222);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDDF2E08DDB12B62E0519E212E56B142FFD06236C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE20EC277BE0A7094883B095CA89F48CFF920BF61);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE33BA8AB936808BFEB09E2A93B2D2C01197E0934);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE3526B2CCA00F6388CBBF7039AC0BB87B76979F1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE42DC5F663277F0424D761ACC71F58D6F3476DED);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE62EC39CD6E04CA15D6D2B22D49E4C54CEE32C33);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE652F005A18FEE6E57C72B1B75C3270E38022D2A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE8AD8176A507D7993FF997A0360FAFA905F25017);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE905425FEC3EB8BC5470A0097B6C9ED17935A3BC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE9C3CBEAD848131252C3E498E15A1B2640B75390);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE9F9FA7C14C11D58B549353D4F8DF9F13B8DC44C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEB97E6C0F51BEBEDC21E07D4F558AFC622F0CC05);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEC019DC7707D96D2B0F4FDA1C55E6861179EF232);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEE6D011859CE26164B1CA33BF42DEADD1C91820B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEEFCE128E6CB1067404CB4399D59B27313CC58DD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEFC5D3315D0EF731A6264E907EA8BCE49E2C475A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF0265246ADA3E39038EF0E73CF6F2889E09CC9D5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF12D0C2EA0E6A84A00E8D2A217A7087651D7FF62);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF318A9CBF6133558944579D6309707D3FF4760E1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF5E7896C756978C566EFE0CFC5EE0E2915E744FB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF8BC581086533D3C86371776631F5ED48300FA59);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFA15A1BEC79692B005F11F5228F52B198E35F0DE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFAEA585C93CC8A656C9B90659DEE7AE1EC03AF5F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFC0364E02AABC3EC57B1EFCB86BF8802604E2538);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, KnownColorTable_InitColorNameTable_mB77CC6E7FACE77180DAA58884E6EAF11865C3D83_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)((int32_t)175));
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteralEC019DC7707D96D2B0F4FDA1C55E6861179EF232);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = L_1;
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteral8E86287EF33D5003838E1E14126934E9305D9F6F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_3 = L_2;
		NullCheck(L_3);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)_stringLiteral48B5E296051D257454025C834B6DA9EC96DA5013);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_4 = L_3;
		NullCheck(L_4);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteral823BD7237B252430123C669DF50172C3478CA24D);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_4;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)168)), (String_t*)_stringLiteral3B72EBEA2254B9AC84632477CCE5208A54AB3DB6);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)169)), (String_t*)_stringLiteralD5A7822934818C74CCC27D5322FE06A8A85BB809);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_7 = L_6;
		NullCheck(L_7);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)170)), (String_t*)_stringLiteral32182C9C8F1CD1638F4715AC125953068FC92531);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_8 = L_7;
		NullCheck(L_8);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(5), (String_t*)_stringLiteral931DFDCE8A7F395390F2AC831102C486A12DB07C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_8;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(6), (String_t*)_stringLiteralE905425FEC3EB8BC5470A0097B6C9ED17935A3BC);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_10 = L_9;
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(7), (String_t*)_stringLiteral9929259F1BEC1114A9E939209D94FE05BF251DEB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_11 = L_10;
		NullCheck(L_11);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(8), (String_t*)_stringLiteral1344305D804E6235F649EAC33904B3E04885CE95);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_12 = L_11;
		NullCheck(L_12);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)9)), (String_t*)_stringLiteral33FAB44F18ED183E4CECB1F8A6C206BA58E3C1B0);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_13 = L_12;
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)10)), (String_t*)_stringLiteral7D073E964821759431132E85BF3C37AB72FD45E2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_14 = L_13;
		NullCheck(L_14);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)11)), (String_t*)_stringLiteralDB784C0FDB5E2F468943EF04EB13EE0E6135421C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_15 = L_14;
		NullCheck(L_15);
		(L_15)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)171)), (String_t*)_stringLiteral7548EEF854B1C1980BC34C0B46AB3E33A36D7F24);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_16 = L_15;
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)172)), (String_t*)_stringLiteralF8BC581086533D3C86371776631F5ED48300FA59);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_17 = L_16;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)12)), (String_t*)_stringLiteral8B475A127DF19319F6E3ECA3DDC86892EBF036B4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_18 = L_17;
		NullCheck(L_18);
		(L_18)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)13)), (String_t*)_stringLiteral87843CCD2168E01528B181F4DBBBABEB98FE76BB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_19 = L_18;
		NullCheck(L_19);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)14)), (String_t*)_stringLiteralD46AB28F4CB7304A48BE45B0E9733A49F12D34F3);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_20 = L_19;
		NullCheck(L_20);
		(L_20)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)15)), (String_t*)_stringLiteral9463439701A265CDBE9159C621695ADE6A0CCF80);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_21 = L_20;
		NullCheck(L_21);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)16)), (String_t*)_stringLiteral865F575D2827CAB4A6107F1C1953BCD60940AAD3);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_22 = L_21;
		NullCheck(L_22);
		(L_22)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)17)), (String_t*)_stringLiteralC09EF62988F51243BEA57FECA94F99D6A970022B);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_23 = L_22;
		NullCheck(L_23);
		(L_23)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)18)), (String_t*)_stringLiteralF5E7896C756978C566EFE0CFC5EE0E2915E744FB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_24 = L_23;
		NullCheck(L_24);
		(L_24)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)19)), (String_t*)_stringLiteral70F2D3E776C2893EFBCD12D863C5C9061CFBA6DA);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_25 = L_24;
		NullCheck(L_25);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)20)), (String_t*)_stringLiteral27FD14CFE32223C443750A097C6E310D28F90764);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_26 = L_25;
		NullCheck(L_26);
		(L_26)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)21)), (String_t*)_stringLiteral0C10E47675493434D04CD8025C8303AF94F7BA5A);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_27 = L_26;
		NullCheck(L_27);
		(L_27)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)173)), (String_t*)_stringLiteralB87415793E420BE51BECDD9B9467178E96A6C0CB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_28 = L_27;
		NullCheck(L_28);
		(L_28)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)174)), (String_t*)_stringLiteral5E0764CF096B981269891B5218E0580D03E013DD);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_29 = L_28;
		NullCheck(L_29);
		(L_29)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)22)), (String_t*)_stringLiteralC8188B9969CCD005902DD40A5C1B757A849492A2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_30 = L_29;
		NullCheck(L_30);
		(L_30)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)23)), (String_t*)_stringLiteral1BE632A60138E1072E4808E397A22C5396E2B6CB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_31 = L_30;
		NullCheck(L_31);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)24)), (String_t*)_stringLiteral6F3CF19A45A31A89284875B48DE1B03C2874C63B);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_32 = L_31;
		NullCheck(L_32);
		(L_32)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)25)), (String_t*)_stringLiteral43FED4F175990E894A41B54F0266A33295FFE681);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_33 = L_32;
		NullCheck(L_33);
		(L_33)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)26)), (String_t*)_stringLiteral395710F3950D8B53F8B903CDF0274AA42C1E8775);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_34 = L_33;
		NullCheck(L_34);
		(L_34)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)27)), (String_t*)_stringLiteralF318A9CBF6133558944579D6309707D3FF4760E1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_35 = L_34;
		NullCheck(L_35);
		(L_35)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)28)), (String_t*)_stringLiteralEE6D011859CE26164B1CA33BF42DEADD1C91820B);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_36 = L_35;
		NullCheck(L_36);
		(L_36)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)29)), (String_t*)_stringLiteralFAEA585C93CC8A656C9B90659DEE7AE1EC03AF5F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_37 = L_36;
		NullCheck(L_37);
		(L_37)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)30)), (String_t*)_stringLiteralFC0364E02AABC3EC57B1EFCB86BF8802604E2538);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_38 = L_37;
		NullCheck(L_38);
		(L_38)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)31)), (String_t*)_stringLiteral0F12C5337B143080F20E16FF528A8631FF60582F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_39 = L_38;
		NullCheck(L_39);
		(L_39)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)32)), (String_t*)_stringLiteral75DEBB9F18947C14AB8656D6C29123A95061D7D2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_40 = L_39;
		NullCheck(L_40);
		(L_40)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)33)), (String_t*)_stringLiteral820A8081D7E6F6A68CFF59F84F071A7B68FBCA0F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_41 = L_40;
		NullCheck(L_41);
		(L_41)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)34)), (String_t*)_stringLiteral6096F21D39223F974B3394FE3BE1660C20DADB86);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_42 = L_41;
		NullCheck(L_42);
		(L_42)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)35)), (String_t*)_stringLiteral929C4D72B7EAD0E41EE32DE35B9338AAE6CD646D);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_43 = L_42;
		NullCheck(L_43);
		(L_43)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)36)), (String_t*)_stringLiteralB04BD692036B5EE39D032DC402E0EC84CE96EFA4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_44 = L_43;
		NullCheck(L_44);
		(L_44)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)37)), (String_t*)_stringLiteralC176B4F6F2542477D019DEF12376DA784C53AAFA);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_45 = L_44;
		NullCheck(L_45);
		(L_45)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)38)), (String_t*)_stringLiteralA1945A43C0BC18AC31B339BFB8D4FF5E314CE1D8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_46 = L_45;
		NullCheck(L_46);
		(L_46)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)39)), (String_t*)_stringLiteral55ED723E4BC2B4D72E786E53A98509A41902D188);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_47 = L_46;
		NullCheck(L_47);
		(L_47)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)40)), (String_t*)_stringLiteralE652F005A18FEE6E57C72B1B75C3270E38022D2A);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_48 = L_47;
		NullCheck(L_48);
		(L_48)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)41)), (String_t*)_stringLiteral290DC698DD60834F31D722825C4A7BBAFF8F01CD);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_49 = L_48;
		NullCheck(L_49);
		(L_49)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)42)), (String_t*)_stringLiteral8ED74C63CB510E5E55B8945290C72B22E26DAFDD);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_50 = L_49;
		NullCheck(L_50);
		(L_50)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)43)), (String_t*)_stringLiteral012F05B13B20BEDFF14849000C19F4828A82208C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_51 = L_50;
		NullCheck(L_51);
		(L_51)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)44)), (String_t*)_stringLiteralE20EC277BE0A7094883B095CA89F48CFF920BF61);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_52 = L_51;
		NullCheck(L_52);
		(L_52)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)45)), (String_t*)_stringLiteral110CDD804B3158EF66476D120B8706123534E862);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_53 = L_52;
		NullCheck(L_53);
		(L_53)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)46)), (String_t*)_stringLiteralE3526B2CCA00F6388CBBF7039AC0BB87B76979F1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_54 = L_53;
		NullCheck(L_54);
		(L_54)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)47)), (String_t*)_stringLiteral0736C2364D019F49B967109907ACAFC8E134D13F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_55 = L_54;
		NullCheck(L_55);
		(L_55)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)48)), (String_t*)_stringLiteralFA15A1BEC79692B005F11F5228F52B198E35F0DE);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_56 = L_55;
		NullCheck(L_56);
		(L_56)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)49)), (String_t*)_stringLiteral8EBFB57153D6D454C440862E93E9EAAECF8169C2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_57 = L_56;
		NullCheck(L_57);
		(L_57)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)50)), (String_t*)_stringLiteral302CE177D88E4E74D5AEEC14206B1A9050593E27);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_58 = L_57;
		NullCheck(L_58);
		(L_58)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)51)), (String_t*)_stringLiteralB57786DBD22383462EDC2C8795A1BCD1B42EF104);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_59 = L_58;
		NullCheck(L_59);
		(L_59)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)52)), (String_t*)_stringLiteral0AD0D95D9C124F0BA012F53D28D6D43678A51EDD);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_60 = L_59;
		NullCheck(L_60);
		(L_60)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)53)), (String_t*)_stringLiteralC7FF1563AC29CE31228BEA39A3E982A2DD0FEFA5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_61 = L_60;
		NullCheck(L_61);
		(L_61)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)54)), (String_t*)_stringLiteral2E339AF5AD2B2E7A1A7FDA50106DC859ECC4947A);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_62 = L_61;
		NullCheck(L_62);
		(L_62)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)55)), (String_t*)_stringLiteralCE58DF7288F2D7D138EF484F103F63F8338900DC);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_63 = L_62;
		NullCheck(L_63);
		(L_63)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)56)), (String_t*)_stringLiteral4BE278FACFFF94EB88E8F8D304BE5C892A0B4499);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_64 = L_63;
		NullCheck(L_64);
		(L_64)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)57)), (String_t*)_stringLiteral3F0D924DEF6BED42CDABB27C4614BCBB5680EC4D);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_65 = L_64;
		NullCheck(L_65);
		(L_65)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)58)), (String_t*)_stringLiteral0B535425E31F9A78D26CC28DC04F8DC9EA4D4739);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_66 = L_65;
		NullCheck(L_66);
		(L_66)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)59)), (String_t*)_stringLiteralA5E43D4B9D6BD45F1DE9FF7D9FC9BC6F5AF86D0F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_67 = L_66;
		NullCheck(L_67);
		(L_67)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)60)), (String_t*)_stringLiteral8A63A6A61851990C637265E340FD864936D3DE30);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_68 = L_67;
		NullCheck(L_68);
		(L_68)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)61)), (String_t*)_stringLiteral0F1F2B2F696A08F4188F054F9D7F2415FB3B042F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_69 = L_68;
		NullCheck(L_69);
		(L_69)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)62)), (String_t*)_stringLiteralD2FF7DE3883BA8ABD1913E108B511AEF2F248BE2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_70 = L_69;
		NullCheck(L_70);
		(L_70)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)63)), (String_t*)_stringLiteral21906D2648554F498F8DF2302454F2F9AB6C3AC0);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_71 = L_70;
		NullCheck(L_71);
		(L_71)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)64)), (String_t*)_stringLiteral6BCE9CFBB3DDE024BA504DEFC9A5389F08F2F861);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_72 = L_71;
		NullCheck(L_72);
		(L_72)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)65)), (String_t*)_stringLiteral7A91FC89053B3ABA6813ED4E8A3B75D79142DB81);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_73 = L_72;
		NullCheck(L_73);
		(L_73)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)66)), (String_t*)_stringLiteral67E566203E89A9A4701A74FF36014F58FC6572B8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_74 = L_73;
		NullCheck(L_74);
		(L_74)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)67)), (String_t*)_stringLiteral38C9A9D65F5DEF48C677D8025F8153DD886C10BE);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_75 = L_74;
		NullCheck(L_75);
		(L_75)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)68)), (String_t*)_stringLiteralBDEF0B3A382F2F3429AD123B1A19C56BAE0DD491);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_76 = L_75;
		NullCheck(L_76);
		(L_76)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)69)), (String_t*)_stringLiteralBE7F62271E99120594C14A6751E46164C2CE8D11);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_77 = L_76;
		NullCheck(L_77);
		(L_77)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)70)), (String_t*)_stringLiteralE33BA8AB936808BFEB09E2A93B2D2C01197E0934);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_78 = L_77;
		NullCheck(L_78);
		(L_78)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)71)), (String_t*)_stringLiteralC94031F021F2E8412B7DC16F4595B455B9870D64);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_79 = L_78;
		NullCheck(L_79);
		(L_79)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)72)), (String_t*)_stringLiteralF12D0C2EA0E6A84A00E8D2A217A7087651D7FF62);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_80 = L_79;
		NullCheck(L_80);
		(L_80)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)73)), (String_t*)_stringLiteral3BB5C9980B9F9AC685024B012CE3C271AB249444);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_81 = L_80;
		NullCheck(L_81);
		(L_81)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)74)), (String_t*)_stringLiteralDA2A5509631CCAB3ABAD39062D9CA88AF921DA70);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_82 = L_81;
		NullCheck(L_82);
		(L_82)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)75)), (String_t*)_stringLiteralB6C81523B38578661B0DD4F81E0B95C0B9BE9FE8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_83 = L_82;
		NullCheck(L_83);
		(L_83)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)76)), (String_t*)_stringLiteral6853B00927EB8BAE6FD25AE411383457D8EC0D05);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_84 = L_83;
		NullCheck(L_84);
		(L_84)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)77)), (String_t*)_stringLiteral4EFB2F599087284F86089262FF9D2527AEE0AEAB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_85 = L_84;
		NullCheck(L_85);
		(L_85)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)78)), (String_t*)_stringLiteral2C24A61820906BE665D2924190B2C1F642F254FB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_86 = L_85;
		NullCheck(L_86);
		(L_86)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)79)), (String_t*)_stringLiteralC3DAD0127402D548D48FBAC7503BF9B6A239ECD5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_87 = L_86;
		NullCheck(L_87);
		(L_87)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)80)), (String_t*)_stringLiteral6C1B56D0BF4BF636B04D0B173C77ADB302B6C69A);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_88 = L_87;
		NullCheck(L_88);
		(L_88)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)81)), (String_t*)_stringLiteral882546522C61EDB1BFD7C83D9ADC5F1D7AA137D3);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_89 = L_88;
		NullCheck(L_89);
		(L_89)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)82)), (String_t*)_stringLiteral66FA768C8B134A2DA8507E93692D9952B3210261);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_90 = L_89;
		NullCheck(L_90);
		(L_90)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)83)), (String_t*)_stringLiteral70AB5F73DCEB6DEB9ECE458819D70FC772C6296D);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_91 = L_90;
		NullCheck(L_91);
		(L_91)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)84)), (String_t*)_stringLiteral2B28584F19667791CB6FB899A29064FF6B910B31);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_92 = L_91;
		NullCheck(L_92);
		(L_92)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)85)), (String_t*)_stringLiteralA0AD878C07F219C432F8F75D7AFE71E38EB9BCE4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_93 = L_92;
		NullCheck(L_93);
		(L_93)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)86)), (String_t*)_stringLiteral95837132224B3D0F6FB1A91C2CCDCA1EF4586AB2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_94 = L_93;
		NullCheck(L_94);
		(L_94)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)87)), (String_t*)_stringLiteralE42DC5F663277F0424D761ACC71F58D6F3476DED);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_95 = L_94;
		NullCheck(L_95);
		(L_95)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)88)), (String_t*)_stringLiteralDD7F813AFFA11C5D660AF772366D87094F18A222);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_96 = L_95;
		NullCheck(L_96);
		(L_96)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)89)), (String_t*)_stringLiteral2091D3C4DC644299973152A6B2716A01907F7022);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_97 = L_96;
		NullCheck(L_97);
		(L_97)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)90)), (String_t*)_stringLiteralD750AC4A31A88BFABFB79199D90964548508D6AE);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_98 = L_97;
		NullCheck(L_98);
		(L_98)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)91)), (String_t*)_stringLiteralBCE1B9B3A26E5887E1C2FE9CD2A9F75F95C2E76C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_99 = L_98;
		NullCheck(L_99);
		(L_99)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)92)), (String_t*)_stringLiteralD5B8395A95C55B5D6A6839DC59F461657D616861);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_100 = L_99;
		NullCheck(L_100);
		(L_100)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)93)), (String_t*)_stringLiteral8177A873F1E307476CCF7C25B7A1219F2BC0CA5E);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_101 = L_100;
		NullCheck(L_101);
		(L_101)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)94)), (String_t*)_stringLiteralD27C6B0E9C85D828181CA2FC3DA98091727454AA);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_102 = L_101;
		NullCheck(L_102);
		(L_102)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)95)), (String_t*)_stringLiteral6EFFD078F1626239C157AC80BC902E4699000DAA);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_103 = L_102;
		NullCheck(L_103);
		(L_103)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)96)), (String_t*)_stringLiteral76AB908CDD5EF7A2D4F57130A4D42F372569D7BD);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_104 = L_103;
		NullCheck(L_104);
		(L_104)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)97)), (String_t*)_stringLiteralD9BEC06D5D13CCAD2D08D57408B68A65197B6754);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_105 = L_104;
		NullCheck(L_105);
		(L_105)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)98)), (String_t*)_stringLiteralE9C3CBEAD848131252C3E498E15A1B2640B75390);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_106 = L_105;
		NullCheck(L_106);
		(L_106)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)99)), (String_t*)_stringLiteral4DE442E3D02390D5E94BC3F3A7C7DBADE341289C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_107 = L_106;
		NullCheck(L_107);
		(L_107)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)100)), (String_t*)_stringLiteral19F3ED72B8004679EAD8928FC583FCF37771A178);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_108 = L_107;
		NullCheck(L_108);
		(L_108)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)101)), (String_t*)_stringLiteral5E96CF3269BE855AD4B7787CB4BFDCCD9503BB46);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_109 = L_108;
		NullCheck(L_109);
		(L_109)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)102)), (String_t*)_stringLiteral8FD4FCEBF431F8C3AB327ADDFEE08BEF51FAD0D7);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_110 = L_109;
		NullCheck(L_110);
		(L_110)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)103)), (String_t*)_stringLiteralD827367980AA6324A2715E469EDF7468795A6043);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_111 = L_110;
		NullCheck(L_111);
		(L_111)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)104)), (String_t*)_stringLiteralB2EF4AEEC292D34024BC775BC0F17D425A1C0325);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_112 = L_111;
		NullCheck(L_112);
		(L_112)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)105)), (String_t*)_stringLiteral20C624FDAFC2C8AC958192DD5C696888EA2CB775);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_113 = L_112;
		NullCheck(L_113);
		(L_113)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)106)), (String_t*)_stringLiteralDDF2E08DDB12B62E0519E212E56B142FFD06236C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_114 = L_113;
		NullCheck(L_114);
		(L_114)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)107)), (String_t*)_stringLiteralB15077D6B86395A72F7C1318136CD64578FA08BF);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_115 = L_114;
		NullCheck(L_115);
		(L_115)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)108)), (String_t*)_stringLiteral3D351D7BA8B569A50B150CE60AB5A663C70BF930);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_116 = L_115;
		NullCheck(L_116);
		(L_116)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)109)), (String_t*)_stringLiteral4CF7F5AC54361B319998BA47B1106BFF6123F432);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_117 = L_116;
		NullCheck(L_117);
		(L_117)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)110)), (String_t*)_stringLiteral41907A6E361476AFE5A6C52331ED7F1DAC94D2F3);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_118 = L_117;
		NullCheck(L_118);
		(L_118)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)111)), (String_t*)_stringLiteral5244C2853B046EBAE0AC4FAFA7C55E62AEEE0A5C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_119 = L_118;
		NullCheck(L_119);
		(L_119)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)112)), (String_t*)_stringLiteralA18C5A45E2FFAD284E4FD71F8744D5E3ECF4FC4E);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_120 = L_119;
		NullCheck(L_120);
		(L_120)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)113)), (String_t*)_stringLiteral53D474787814D40B54CADBB99BD8A431AEEA37E3);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_121 = L_120;
		NullCheck(L_121);
		(L_121)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)114)), (String_t*)_stringLiteralB26F50B338D34F3E42FFA9C2628393743D2AD2BE);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_122 = L_121;
		NullCheck(L_122);
		(L_122)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)115)), (String_t*)_stringLiteralB9D7F0A03F059AA445FF42791D72545EA8DAB816);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_123 = L_122;
		NullCheck(L_123);
		(L_123)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)116)), (String_t*)_stringLiteralB5591FED44C1E908120624CD6A53920A99F2AE6C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_124 = L_123;
		NullCheck(L_124);
		(L_124)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)117)), (String_t*)_stringLiteral39E58E632C1A8F2E450CCCE1158818B95B6B2350);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_125 = L_124;
		NullCheck(L_125);
		(L_125)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)118)), (String_t*)_stringLiteral05455CF614B372B63BAAE5B3BB8CB28EDA512C24);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_126 = L_125;
		NullCheck(L_126);
		(L_126)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)119)), (String_t*)_stringLiteral91A8AFE46A5ED1EB95C129D3FA3D0C1615112215);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_127 = L_126;
		NullCheck(L_127);
		(L_127)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)120)), (String_t*)_stringLiteral241F9DFCCC69FF179AC0DDAEECDC02FCA7FFA27F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_128 = L_127;
		NullCheck(L_128);
		(L_128)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)121)), (String_t*)_stringLiteral9547816FF9624B4D00B9ADC93C41F5D42ADD9A88);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_129 = L_128;
		NullCheck(L_129);
		(L_129)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)122)), (String_t*)_stringLiteralEEFCE128E6CB1067404CB4399D59B27313CC58DD);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_130 = L_129;
		NullCheck(L_130);
		(L_130)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)123)), (String_t*)_stringLiteral587FB2C1FB44E7C7FDADCB4A73E7C47DD06E830E);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_131 = L_130;
		NullCheck(L_131);
		(L_131)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)124)), (String_t*)_stringLiteral7FB02E5455A8D3012B3C7686B742A0CE6288488C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_132 = L_131;
		NullCheck(L_132);
		(L_132)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)125)), (String_t*)_stringLiteral41C26ACEA21963C1F3601EDCA3C371BF852E4358);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_133 = L_132;
		NullCheck(L_133);
		(L_133)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)126)), (String_t*)_stringLiteral9512EDB4588205F164BE2D61A383794552D84023);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_134 = L_133;
		NullCheck(L_134);
		(L_134)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)127)), (String_t*)_stringLiteralB419CFD5B49CDCB4D22631C6EFC26C19DF54DC3C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_135 = L_134;
		NullCheck(L_135);
		(L_135)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)128)), (String_t*)_stringLiteralE9F9FA7C14C11D58B549353D4F8DF9F13B8DC44C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_136 = L_135;
		NullCheck(L_136);
		(L_136)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)129)), (String_t*)_stringLiteral17122CB683A11086918016EA625EA4B9E3C92AEE);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_137 = L_136;
		NullCheck(L_137);
		(L_137)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)130)), (String_t*)_stringLiteral4A526846ED8E7424D62D9BF1C329EA6EDA654BB4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_138 = L_137;
		NullCheck(L_138);
		(L_138)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)131)), (String_t*)_stringLiteral5E8DAF4EBCBF622D1B6B9043708084AE061938DB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_139 = L_138;
		NullCheck(L_139);
		(L_139)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)132)), (String_t*)_stringLiteral3EBAEC90DCDFE988DFBAB9A9959872F2F09EE1DE);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_140 = L_139;
		NullCheck(L_140);
		(L_140)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)133)), (String_t*)_stringLiteral6AB44B2B53208D90CCC01BE36F24A7ACC0AB5ECC);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_141 = L_140;
		NullCheck(L_141);
		(L_141)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)134)), (String_t*)_stringLiteralD66A2D950BA4DB0A42CEB7D044F1337D97F575A0);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_142 = L_141;
		NullCheck(L_142);
		(L_142)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)135)), (String_t*)_stringLiteral3B2973C6F2ABBB5326732A75BEAD9DFE3103D8CB);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_143 = L_142;
		NullCheck(L_143);
		(L_143)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)136)), (String_t*)_stringLiteral9DEBD00AA70D18037D67EC09001CD192A0E69BF3);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_144 = L_143;
		NullCheck(L_144);
		(L_144)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)137)), (String_t*)_stringLiteralDB3AF064A254C20AC699C2808DE33F07E9D62F76);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_145 = L_144;
		NullCheck(L_145);
		(L_145)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)138)), (String_t*)_stringLiteral75ACE450AFE00C6C93EAAAEF5A42FE49354E4213);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_146 = L_145;
		NullCheck(L_146);
		(L_146)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)139)), (String_t*)_stringLiteral28F64DA50C40CA73C2D38F192648522A0C2D023C);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_147 = L_146;
		NullCheck(L_147);
		(L_147)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)140)), (String_t*)_stringLiteral8E68E9FAEC7150DBFFF4198365BD772DE872934A);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_148 = L_147;
		NullCheck(L_148);
		(L_148)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)141)), (String_t*)_stringLiteral77FA365523C8AD1C1BCB07FE41D0BA9D232632F4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_149 = L_148;
		NullCheck(L_149);
		(L_149)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)142)), (String_t*)_stringLiteral66FAE1C73947835F79972DE93673DD6B9FAD5D73);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_150 = L_149;
		NullCheck(L_150);
		(L_150)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)143)), (String_t*)_stringLiteral5F73F4985A325836FF5D04C44C03258B9E6FDC02);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_151 = L_150;
		NullCheck(L_151);
		(L_151)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)144)), (String_t*)_stringLiteralB3C05D8A35844E1E82D9FF2A8C83562939E1EDF2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_152 = L_151;
		NullCheck(L_152);
		(L_152)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)145)), (String_t*)_stringLiteral450310C5C6F51D5E90678EBC0D7390558265C5E1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_153 = L_152;
		NullCheck(L_153);
		(L_153)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)146)), (String_t*)_stringLiteralF0265246ADA3E39038EF0E73CF6F2889E09CC9D5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_154 = L_153;
		NullCheck(L_154);
		(L_154)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)147)), (String_t*)_stringLiteralD8D547D9186FC9B450955EE5D96C51B9FD719725);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_155 = L_154;
		NullCheck(L_155);
		(L_155)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)148)), (String_t*)_stringLiteral3CF17B43840F4DF696FDA932250266608735A759);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_156 = L_155;
		NullCheck(L_156);
		(L_156)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)149)), (String_t*)_stringLiteralE8AD8176A507D7993FF997A0360FAFA905F25017);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_157 = L_156;
		NullCheck(L_157);
		(L_157)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)150)), (String_t*)_stringLiteral0598190E4CAF8BFE9CC29FA8911B602F97DF30C1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_158 = L_157;
		NullCheck(L_158);
		(L_158)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)151)), (String_t*)_stringLiteral6DF3A796E043E70AC91E5E4B013152F95717CE32);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_159 = L_158;
		NullCheck(L_159);
		(L_159)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)152)), (String_t*)_stringLiteral41A34E9EF07D662E61C60CD7D11AE7BF8EDD7152);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_160 = L_159;
		NullCheck(L_160);
		(L_160)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)153)), (String_t*)_stringLiteral52248D0514A42AF911E38FCF9F31F0101C002594);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_161 = L_160;
		NullCheck(L_161);
		(L_161)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)154)), (String_t*)_stringLiteralA0BD619A4AB7BEBB403BE6D96B78F767CBA22D1F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_162 = L_161;
		NullCheck(L_162);
		(L_162)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)155)), (String_t*)_stringLiteral323A2BDC9DDFD95817E56A04E2050EC04FA08411);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_163 = L_162;
		NullCheck(L_163);
		(L_163)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)156)), (String_t*)_stringLiteralEB97E6C0F51BEBEDC21E07D4F558AFC622F0CC05);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_164 = L_163;
		NullCheck(L_164);
		(L_164)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)157)), (String_t*)_stringLiteralEFC5D3315D0EF731A6264E907EA8BCE49E2C475A);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_165 = L_164;
		NullCheck(L_165);
		(L_165)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)158)), (String_t*)_stringLiteral5A9D9B95FC9D5DFFBC5C2846528F1795CFF0DAE8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_166 = L_165;
		NullCheck(L_166);
		(L_166)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)159)), (String_t*)_stringLiteralCBF11B46FFA55AA52B4DBA77FE572890C50A4069);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_167 = L_166;
		NullCheck(L_167);
		(L_167)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)160)), (String_t*)_stringLiteral4D77265CBB3D01FFD17B3547F57906C93C97BA88);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_168 = L_167;
		NullCheck(L_168);
		(L_168)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)161)), (String_t*)_stringLiteral46CB7B4FA6C1DDF8D391959C2BD0078A9B4EAC04);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_169 = L_168;
		NullCheck(L_169);
		(L_169)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)162)), (String_t*)_stringLiteral0B431398DDDEBF0132D6419AAE2AC59DD6B6F20E);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_170 = L_169;
		NullCheck(L_170);
		(L_170)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)163)), (String_t*)_stringLiteralAF25DC5A736EC63E7FFBDE734FC4EC9B0024A767);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_171 = L_170;
		NullCheck(L_171);
		(L_171)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)164)), (String_t*)_stringLiteral271DE06C3125CB3D128E9B0C31D9A30DC59CAE22);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_172 = L_171;
		NullCheck(L_172);
		(L_172)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)165)), (String_t*)_stringLiteralE62EC39CD6E04CA15D6D2B22D49E4C54CEE32C33);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_173 = L_172;
		NullCheck(L_173);
		(L_173)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)166)), (String_t*)_stringLiteral75DAD94535A79F2149221C6619F899F4BDE2C7F6);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_174 = L_173;
		NullCheck(L_174);
		(L_174)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)167)), (String_t*)_stringLiteral219502BA9388110AF53C34EFBB646326A8EE8D77);
		((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorNameTable = L_174;
		Il2CppCodeGenWriteBarrier((void**)(&((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorNameTable), (void*)L_174);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97 (int32_t ___0_color, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		KnownColorTable_EnsureColorTable_mDA6E15087C13F589AE3A2038B16F2D7DBAF3FA83(NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = ((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorTable;
		int32_t L_1 = ___0_color;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		int32_t L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE (int32_t ___0_color, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		KnownColorTable_EnsureColorNameTable_m7D1BDA884F19A4F1AFEDA4B4CF3C59F20F612303(NULL);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = ((KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_StaticFields*)il2cpp_codegen_static_fields_for(KnownColorTable_tB5906A1181942046FF6EF2C53777B6B79A7EFB08_il2cpp_TypeInfo_var))->___s_colorNameTable;
		int32_t L_1 = ___0_color;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		String_t* L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0 (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_colorTable, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, KnownColorTable_UpdateSystemColors_mE162F51D4EB7E8842CFC3B1678801F3E3DB5DAD0_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = ___0_colorTable;
		NullCheck(L_0);
		(L_0)->SetAt(static_cast<il2cpp_array_size_t>(1), (int32_t)((int32_t)-2830136));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = ___0_colorTable;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(2), (int32_t)((int32_t)-16755485));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_2 = ___0_colorTable;
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(3), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = ___0_colorTable;
		NullCheck(L_3);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(4), (int32_t)((int32_t)-8355712));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = ___0_colorTable;
		NullCheck(L_4);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)168)), (int32_t)((int32_t)-986896));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = ___0_colorTable;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)169)), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = ___0_colorTable;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)170)), (int32_t)((int32_t)-6250336));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = ___0_colorTable;
		NullCheck(L_7);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(5), (int32_t)((int32_t)-1250856));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_8 = ___0_colorTable;
		NullCheck(L_8);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(6), (int32_t)((int32_t)-5461863));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_9 = ___0_colorTable;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(7), (int32_t)((int32_t)-9343132));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_10 = ___0_colorTable;
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(8), (int32_t)((int32_t)-921630));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_11 = ___0_colorTable;
		NullCheck(L_11);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)9)), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = ___0_colorTable;
		NullCheck(L_12);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)10)), (int32_t)((int32_t)-16777216));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_13 = ___0_colorTable;
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)11)), (int32_t)((int32_t)-16757096));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_14 = ___0_colorTable;
		NullCheck(L_14);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)171)), (int32_t)((int32_t)-4599318));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_15 = ___0_colorTable;
		NullCheck(L_15);
		(L_15)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)172)), (int32_t)((int32_t)-2628366));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_16 = ___0_colorTable;
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)12)), (int32_t)((int32_t)-5461863));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_17 = ___0_colorTable;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)13)), (int32_t)((int32_t)-13538619));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_18 = ___0_colorTable;
		NullCheck(L_18);
		(L_18)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)14)), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_19 = ___0_colorTable;
		NullCheck(L_19);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)15)), (int32_t)((int32_t)-16777088));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_20 = ___0_colorTable;
		NullCheck(L_20);
		(L_20)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)16)), (int32_t)((int32_t)-2830136));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_21 = ___0_colorTable;
		NullCheck(L_21);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)17)), (int32_t)((int32_t)-8743201));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_22 = ___0_colorTable;
		NullCheck(L_22);
		(L_22)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)18)), (int32_t)((int32_t)-2562824));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_23 = ___0_colorTable;
		NullCheck(L_23);
		(L_23)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)19)), (int32_t)((int32_t)-31));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_24 = ___0_colorTable;
		NullCheck(L_24);
		(L_24)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)20)), (int32_t)((int32_t)-16777216));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_25 = ___0_colorTable;
		NullCheck(L_25);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)21)), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_26 = ___0_colorTable;
		NullCheck(L_26);
		(L_26)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)173)), (int32_t)((int32_t)-986896));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_27 = ___0_colorTable;
		NullCheck(L_27);
		(L_27)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)174)), (int32_t)((int32_t)-13395457));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_28 = ___0_colorTable;
		NullCheck(L_28);
		(L_28)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)22)), (int32_t)((int32_t)-16777216));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_29 = ___0_colorTable;
		NullCheck(L_29);
		(L_29)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)23)), (int32_t)((int32_t)-2830136));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_30 = ___0_colorTable;
		NullCheck(L_30);
		(L_30)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)24)), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_31 = ___0_colorTable;
		NullCheck(L_31);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)25)), (int32_t)((int32_t)-16777216));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_32 = ___0_colorTable;
		NullCheck(L_32);
		(L_32)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)26)), (int32_t)((int32_t)-16777216));
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshal_pinvoke(const Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661& unmarshaled, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_pinvoke& marshaled)
{
	marshaled.___name = il2cpp_codegen_marshal_string(unmarshaled.___name);
	marshaled.___value = unmarshaled.___value;
	marshaled.___knownColor = unmarshaled.___knownColor;
	marshaled.___state = unmarshaled.___state;
}
IL2CPP_EXTERN_C void Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshal_pinvoke_back(const Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_pinvoke& marshaled, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661& unmarshaled)
{
	unmarshaled.___name = il2cpp_codegen_marshal_string_result(marshaled.___name);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___name), (void*)il2cpp_codegen_marshal_string_result(marshaled.___name));
	int64_t unmarshaledvalue_temp_1 = 0;
	unmarshaledvalue_temp_1 = marshaled.___value;
	unmarshaled.___value = unmarshaledvalue_temp_1;
	int16_t unmarshaledknownColor_temp_2 = 0;
	unmarshaledknownColor_temp_2 = marshaled.___knownColor;
	unmarshaled.___knownColor = unmarshaledknownColor_temp_2;
	int16_t unmarshaledstate_temp_3 = 0;
	unmarshaledstate_temp_3 = marshaled.___state;
	unmarshaled.___state = unmarshaledstate_temp_3;
}
IL2CPP_EXTERN_C void Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshal_pinvoke_cleanup(Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_marshal_free(marshaled.___name);
	marshaled.___name = NULL;
}
IL2CPP_EXTERN_C void Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshal_com(const Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661& unmarshaled, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_com& marshaled)
{
	marshaled.___name = il2cpp_codegen_marshal_bstring(unmarshaled.___name);
	marshaled.___value = unmarshaled.___value;
	marshaled.___knownColor = unmarshaled.___knownColor;
	marshaled.___state = unmarshaled.___state;
}
IL2CPP_EXTERN_C void Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshal_com_back(const Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_com& marshaled, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661& unmarshaled)
{
	unmarshaled.___name = il2cpp_codegen_marshal_bstring_result(marshaled.___name);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___name), (void*)il2cpp_codegen_marshal_bstring_result(marshaled.___name));
	int64_t unmarshaledvalue_temp_1 = 0;
	unmarshaledvalue_temp_1 = marshaled.___value;
	unmarshaled.___value = unmarshaledvalue_temp_1;
	int16_t unmarshaledknownColor_temp_2 = 0;
	unmarshaledknownColor_temp_2 = marshaled.___knownColor;
	unmarshaled.___knownColor = unmarshaledknownColor_temp_2;
	int16_t unmarshaledstate_temp_3 = 0;
	unmarshaledstate_temp_3 = marshaled.___state;
	unmarshaled.___state = unmarshaledstate_temp_3;
}
IL2CPP_EXTERN_C void Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshal_com_cleanup(Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_marshaled_com& marshaled)
{
	il2cpp_codegen_marshal_free_bstring(marshaled.___name);
	marshaled.___name = NULL;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int64_t L_0;
		L_0 = Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574(__this, NULL);
		return (uint8_t)((int32_t)(uint8_t)((int64_t)(((int64_t)(L_0>>((int32_t)16)))&((int64_t)((int32_t)255)))));
	}
}
IL2CPP_EXTERN_C  uint8_t Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	uint8_t _returnValue;
	_returnValue = Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int64_t L_0;
		L_0 = Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574(__this, NULL);
		return (uint8_t)((int32_t)(uint8_t)((int64_t)(((int64_t)(L_0>>8))&((int64_t)((int32_t)255)))));
	}
}
IL2CPP_EXTERN_C  uint8_t Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	uint8_t _returnValue;
	_returnValue = Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int64_t L_0;
		L_0 = Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574(__this, NULL);
		return (uint8_t)((int32_t)(uint8_t)((int64_t)(L_0&((int64_t)((int32_t)255)))));
	}
}
IL2CPP_EXTERN_C  uint8_t Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	uint8_t _returnValue;
	_returnValue = Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int64_t L_0;
		L_0 = Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574(__this, NULL);
		return (uint8_t)((int32_t)(uint8_t)((int64_t)(((int64_t)(L_0>>((int32_t)24)))&((int64_t)((int32_t)255)))));
	}
}
IL2CPP_EXTERN_C  uint8_t Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	uint8_t _returnValue;
	_returnValue = Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int16_t L_0 = __this->___state;
		return (bool)((!(((uint32_t)((int32_t)((int32_t)L_0&1))) <= ((uint32_t)0)))? 1 : 0);
	}
}
IL2CPP_EXTERN_C  bool Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	bool _returnValue;
	_returnValue = Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Color_get_NameAndARGBValue_m95C91B9588C731AC546868DBD1F1C4F2B95D44AF (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_NameAndARGBValue_m95C91B9588C731AC546868DBD1F1C4F2B95D44AF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB243E266B4DBD4EC5C0B7CCF9C3B261EA0914EDC);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_NameAndARGBValue_m95C91B9588C731AC546868DBD1F1C4F2B95D44AF_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)5);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = L_0;
		String_t* L_2;
		L_2 = Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876(__this, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_2);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_2);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_3 = L_1;
		uint8_t L_4;
		L_4 = Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230(__this, NULL);
		uint8_t L_5 = L_4;
		RuntimeObject* L_6 = Box(Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, &L_5);
		NullCheck(L_3);
		ArrayElementTypeCheck (L_3, L_6);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_6);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_7 = L_3;
		uint8_t L_8;
		L_8 = Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951(__this, NULL);
		uint8_t L_9 = L_8;
		RuntimeObject* L_10 = Box(Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, &L_9);
		NullCheck(L_7);
		ArrayElementTypeCheck (L_7, L_10);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_10);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_11 = L_7;
		uint8_t L_12;
		L_12 = Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA(__this, NULL);
		uint8_t L_13 = L_12;
		RuntimeObject* L_14 = Box(Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, &L_13);
		NullCheck(L_11);
		ArrayElementTypeCheck (L_11, L_14);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_14);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_15 = L_11;
		uint8_t L_16;
		L_16 = Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61(__this, NULL);
		uint8_t L_17 = L_16;
		RuntimeObject* L_18 = Box(Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, &L_17);
		NullCheck(L_15);
		ArrayElementTypeCheck (L_15, L_18);
		(L_15)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)L_18);
		String_t* L_19;
		L_19 = String_Format_m918500C1EFB475181349A79989BB79BB36102894(_stringLiteralB243E266B4DBD4EC5C0B7CCF9C3B261EA0914EDC, L_15, NULL);
		return L_19;
	}
}
IL2CPP_EXTERN_C  String_t* Color_get_NameAndARGBValue_m95C91B9588C731AC546868DBD1F1C4F2B95D44AF_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = Color_get_NameAndARGBValue_m95C91B9588C731AC546868DBD1F1C4F2B95D44AF(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int16_t L_0 = __this->___state;
		if (!((int32_t)((int32_t)L_0&8)))
		{
			goto IL_0011;
		}
	}
	{
		String_t* L_1 = __this->___name;
		return L_1;
	}

IL_0011:
	{
		bool L_2;
		L_2 = Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198(__this, NULL);
		if (!L_2)
		{
			goto IL_0025;
		}
	}
	{
		int16_t L_3 = __this->___knownColor;
		String_t* L_4;
		L_4 = KnownColorTable_KnownColorToName_mEFAED2FFD4EFF6A60FA921AE11C3894880DD63BE(L_3, NULL);
		return L_4;
	}

IL_0025:
	{
		int64_t L_5 = __this->___value;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		String_t* L_6;
		L_6 = Convert_ToString_mD50A87BAAF57E646B5A7B8AE989EC2A6B8DC1057(L_5, ((int32_t)16), NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C  String_t* Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int16_t L_0 = __this->___state;
		if (!((int32_t)((int32_t)L_0&2)))
		{
			goto IL_0011;
		}
	}
	{
		int64_t L_1 = __this->___value;
		return L_1;
	}

IL_0011:
	{
		bool L_2;
		L_2 = Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198(__this, NULL);
		if (!L_2)
		{
			goto IL_0026;
		}
	}
	{
		int16_t L_3 = __this->___knownColor;
		int32_t L_4;
		L_4 = KnownColorTable_KnownColorToArgb_m0979478F1E425ECEA80341F48B9C24AA50B36E97(L_3, NULL);
		return ((int64_t)L_4);
	}

IL_0026:
	{
		return ((int64_t)0);
	}
}
IL2CPP_EXTERN_C  int64_t Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	int64_t _returnValue;
	_returnValue = Color_get_Value_mEC0390B65E6E1ADA7963CDDE6ADB0AA077712574(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2BAE49BDE068FA80909F1B2FE5CCD6C635DD9783);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral52E4E95B00FBFE8143FAC52CA391655BEE331044);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA401E49E342DADCE6CBB4D579113F37ECE4A65FA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB5B01188820989C37668D01DDA4D84337C4908DA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE166C9564FBDE461738077E3B1B506525EB6ACCC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF0BCAC9E3908E817167A09A2EA06EAED4CA9569F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFA18942F3C9F50CF877ED04B1DE8E2DABFD08741);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t V_0 = 0x0;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int16_t L_0 = __this->___state;
		if (((int32_t)((int32_t)L_0&8)))
		{
			goto IL_0014;
		}
	}
	{
		int16_t L_1 = __this->___state;
		if (!((int32_t)((int32_t)L_1&1)))
		{
			goto IL_002a;
		}
	}

IL_0014:
	{
		String_t* L_2;
		L_2 = Color_get_Name_m82B674D6BA69AE386BB798DD78DB116307F3B876(__this, NULL);
		String_t* L_3;
		L_3 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteralF0BCAC9E3908E817167A09A2EA06EAED4CA9569F, L_2, _stringLiteralE166C9564FBDE461738077E3B1B506525EB6ACCC, NULL);
		return L_3;
	}

IL_002a:
	{
		int16_t L_4 = __this->___state;
		if (!((int32_t)((int32_t)L_4&2)))
		{
			goto IL_00ad;
		}
	}
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)((int32_t)9));
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteralA401E49E342DADCE6CBB4D579113F37ECE4A65FA);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_7 = L_6;
		uint8_t L_8;
		L_8 = Color_get_A_m20373E97C1E01A1B27CD551E4FB1CB62DD381230(__this, NULL);
		V_0 = L_8;
		String_t* L_9;
		L_9 = Byte_ToString_mB80CE094B94215119578E4D796566E71D7277EE4((&V_0), NULL);
		NullCheck(L_7);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_9);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_10 = L_7;
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteral52E4E95B00FBFE8143FAC52CA391655BEE331044);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_11 = L_10;
		uint8_t L_12;
		L_12 = Color_get_R_mA69925F53F4EF7AC1A3B4A94B9F4ABEA72662951(__this, NULL);
		V_0 = L_12;
		String_t* L_13;
		L_13 = Byte_ToString_mB80CE094B94215119578E4D796566E71D7277EE4((&V_0), NULL);
		NullCheck(L_11);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_13);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_14 = L_11;
		NullCheck(L_14);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteral2BAE49BDE068FA80909F1B2FE5CCD6C635DD9783);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_15 = L_14;
		uint8_t L_16;
		L_16 = Color_get_G_m8BBF290336C0D938F2546AB296B002C9995B3BAA(__this, NULL);
		V_0 = L_16;
		String_t* L_17;
		L_17 = Byte_ToString_mB80CE094B94215119578E4D796566E71D7277EE4((&V_0), NULL);
		NullCheck(L_15);
		(L_15)->SetAt(static_cast<il2cpp_array_size_t>(5), (String_t*)L_17);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_18 = L_15;
		NullCheck(L_18);
		(L_18)->SetAt(static_cast<il2cpp_array_size_t>(6), (String_t*)_stringLiteralFA18942F3C9F50CF877ED04B1DE8E2DABFD08741);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_19 = L_18;
		uint8_t L_20;
		L_20 = Color_get_B_mC668C85173CDC211AC0E2FEB3E6C36E9C57DCC61(__this, NULL);
		V_0 = L_20;
		String_t* L_21;
		L_21 = Byte_ToString_mB80CE094B94215119578E4D796566E71D7277EE4((&V_0), NULL);
		NullCheck(L_19);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(7), (String_t*)L_21);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_22 = L_19;
		NullCheck(L_22);
		(L_22)->SetAt(static_cast<il2cpp_array_size_t>(8), (String_t*)_stringLiteralE166C9564FBDE461738077E3B1B506525EB6ACCC);
		String_t* L_23;
		L_23 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_22, NULL);
		return L_23;
	}

IL_00ad:
	{
		return _stringLiteralB5B01188820989C37668D01DDA4D84337C4908DA;
	}
}
IL2CPP_EXTERN_C  String_t* Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = Color_ToString_m5C7D9DB036E127D264B17FA528580B7BD052513F(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57 (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 ___0_left, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 ___1_right, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_0 = ___0_left;
		int64_t L_1 = L_0.___value;
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_2 = ___1_right;
		int64_t L_3 = L_2.___value;
		if ((!(((uint64_t)L_1) == ((uint64_t)L_3))))
		{
			goto IL_003c;
		}
	}
	{
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_4 = ___0_left;
		int16_t L_5 = L_4.___state;
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_6 = ___1_right;
		int16_t L_7 = L_6.___state;
		if ((!(((uint32_t)L_5) == ((uint32_t)L_7))))
		{
			goto IL_003c;
		}
	}
	{
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_8 = ___0_left;
		int16_t L_9 = L_8.___knownColor;
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_10 = ___1_right;
		int16_t L_11 = L_10.___knownColor;
		if ((!(((uint32_t)L_9) == ((uint32_t)L_11))))
		{
			goto IL_003c;
		}
	}
	{
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_12 = ___0_left;
		String_t* L_13 = L_12.___name;
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_14 = ___1_right;
		String_t* L_15 = L_14.___name;
		bool L_16;
		L_16 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_13, L_15, NULL);
		return L_16;
	}

IL_003c:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_il2cpp_TypeInfo_var)))
		{
			goto IL_0015;
		}
	}
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA(__this, ((*(Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*)((Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*)(Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*)UnBox(L_1, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661_il2cpp_TypeInfo_var)))), NULL);
		return L_2;
	}

IL_0015:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	bool _returnValue;
	_returnValue = Color_Equals_m0AECC84D788C0CB1B1FB44C945705AD7BAFF603A(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_0 = (*(Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*)__this);
		Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 L_1 = ___0_other;
		bool L_2;
		L_2 = Color_op_Equality_m5BD270427D7DA9AF8F2C004ACA2F43F734F2EE57(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA_AdjustorThunk (RuntimeObject* __this, Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661 ___0_other, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	bool _returnValue;
	_returnValue = Color_Equals_mCC06EC6FD579073BFC35481FA94461A70DA7B7BA(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB (Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		String_t* L_0 = __this->___name;
		bool L_1;
		L_1 = Color_get_IsKnownColor_mDE5232E7F4367FD7055F468772188EC1C6576198(__this, NULL);
		if (!((int32_t)(((!(((RuntimeObject*)(String_t*)L_0) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0)&((((int32_t)L_1) == ((int32_t)0))? 1 : 0))))
		{
			goto IL_0021;
		}
	}
	{
		String_t* L_2 = __this->___name;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = VirtualFuncInvoker0< int32_t >::Invoke(2, L_2);
		return L_3;
	}

IL_0021:
	{
		int64_t* L_4 = (int64_t*)(&__this->___value);
		int32_t L_5;
		L_5 = Int64_GetHashCode_mDB050BE2AC244D92B14D1DF725AAD279CDC48496(L_4, NULL);
		int16_t* L_6 = (int16_t*)(&__this->___state);
		int32_t L_7;
		L_7 = Int16_GetHashCode_mCD0A167AC8E6ACC2235F12E00C0F9BDC6ED3B6E1(L_6, NULL);
		il2cpp_codegen_runtime_class_init_inline(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		int32_t L_8;
		L_8 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_5, L_7, NULL);
		int16_t* L_9 = (int16_t*)(&__this->___knownColor);
		int32_t L_10;
		L_10 = Int16_GetHashCode_mCD0A167AC8E6ACC2235F12E00C0F9BDC6ED3B6E1(L_9, NULL);
		int32_t L_11;
		L_11 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_8, L_10, NULL);
		return L_11;
	}
}
IL2CPP_EXTERN_C  int32_t Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Color_t677510AC0B9290AFBF973DC2AD88ABBD71691661*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Color_GetHashCode_m87358396DFDAE1F14332D3AC10A83ED1060FC9BB(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3 (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE ___0_left, Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE ___1_right, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0;
		L_0 = Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_inline((&___0_left), NULL);
		int32_t L_1;
		L_1 = Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_inline((&___1_right), NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)L_1))))
		{
			goto IL_0021;
		}
	}
	{
		int32_t L_2;
		L_2 = Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_inline((&___0_left), NULL);
		int32_t L_3;
		L_3 = Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_inline((&___1_right), NULL);
		return (bool)((((int32_t)L_2) == ((int32_t)L_3))? 1 : 0);
	}

IL_0021:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97 (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE_il2cpp_TypeInfo_var)))
		{
			goto IL_0015;
		}
	}
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4(__this, ((*(Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*)((Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*)(Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*)UnBox(L_1, Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE_il2cpp_TypeInfo_var)))), NULL);
		return L_2;
	}

IL_0015:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*>(__this + _offset);
	bool _returnValue;
	_returnValue = Point_Equals_mC52C70909BD95B4192039FC19157003D4DB22C97(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4 (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE L_0 = (*(Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*)__this);
		Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE L_1 = ___0_other;
		bool L_2;
		L_2 = Point_op_Equality_m16DF80982C9BD6458336F4101DFEBFC2C958216D(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4_AdjustorThunk (RuntimeObject* __this, Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE ___0_other, const RuntimeMethod* method)
{
	Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*>(__this + _offset);
	bool _returnValue;
	_returnValue = Point_Equals_m3818E37548CAC5B822230E60BB7C45EF569E98E4(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951 (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0;
		L_0 = Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_inline(__this, NULL);
		int32_t L_1;
		L_1 = Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_inline(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		int32_t L_2;
		L_2 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  int32_t Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Point_GetHashCode_mDCA1B4C2AF30051D731F2B500019F35239D6E951(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral44055C491501B7E10D2CB80FC027949A27E74AA1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = L_1;
		int32_t L_3;
		L_3 = Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_inline(__this, NULL);
		V_0 = L_3;
		String_t* L_4;
		L_4 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_2;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteral44055C491501B7E10D2CB80FC027949A27E74AA1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		int32_t L_7;
		L_7 = Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_inline(__this, NULL);
		V_0 = L_7;
		String_t* L_8;
		L_8 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_6;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		String_t* L_10;
		L_10 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_9, NULL);
		return L_10;
	}
}
IL2CPP_EXTERN_C  String_t* Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = Point_ToString_mA8731A1FEDC9F643E304C052B3F4285F6ABC56FF(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*>(__this + _offset);
	float _returnValue;
	_returnValue = PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3 (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*>(__this + _offset);
	float _returnValue;
	_returnValue = PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371 (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 ___0_left, PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 ___1_right, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0;
		L_0 = PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_inline((&___0_left), NULL);
		float L_1;
		L_1 = PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_inline((&___1_right), NULL);
		if ((!(((float)L_0) == ((float)L_1))))
		{
			goto IL_0021;
		}
	}
	{
		float L_2;
		L_2 = PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_inline((&___0_left), NULL);
		float L_3;
		L_3 = PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_inline((&___1_right), NULL);
		return (bool)((((float)L_2) == ((float)L_3))? 1 : 0);
	}

IL_0021:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138_il2cpp_TypeInfo_var)))
		{
			goto IL_0015;
		}
	}
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1(__this, ((*(PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*)((PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*)(PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*)UnBox(L_1, PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138_il2cpp_TypeInfo_var)))), NULL);
		return L_2;
	}

IL_0015:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*>(__this + _offset);
	bool _returnValue;
	_returnValue = PointF_Equals_mF1B0AEFEFECC0044933D67DCC12851F941F98E1D(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1 (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 L_0 = (*(PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*)__this);
		PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 L_1 = ___0_other;
		bool L_2;
		L_2 = PointF_op_Equality_m0BD00717287326DD803FA0C9A74083A5A0114371(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1_AdjustorThunk (RuntimeObject* __this, PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138 ___0_other, const RuntimeMethod* method)
{
	PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*>(__this + _offset);
	bool _returnValue;
	_returnValue = PointF_Equals_m0EC0248A2D9522AB35E4C32DD4DE06ABAD946CE1(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0;
		L_0 = PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_inline(__this, NULL);
		V_0 = L_0;
		int32_t L_1;
		L_1 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		float L_2;
		L_2 = PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_inline(__this, NULL);
		V_0 = L_2;
		int32_t L_3;
		L_3 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		il2cpp_codegen_runtime_class_init_inline(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		int32_t L_4;
		L_4 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_1, L_3, NULL);
		return L_4;
	}
}
IL2CPP_EXTERN_C  int32_t PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = PointF_GetHashCode_m7128D4D9228862A2B1C6862A8E8A7971A2AFD27E(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2 (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB172CBE3B1EEEA5D73C5D6F86B5AC6FB85787A5B);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = L_1;
		float* L_3 = (float*)(&__this->___x);
		String_t* L_4;
		L_4 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972(L_3, NULL);
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_2;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteralB172CBE3B1EEEA5D73C5D6F86B5AC6FB85787A5B);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		float* L_7 = (float*)(&__this->___y);
		String_t* L_8;
		L_8 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972(L_7, NULL);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_6;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		String_t* L_10;
		L_10 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_9, NULL);
		return L_10;
	}
}
IL2CPP_EXTERN_C  String_t* PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = PointF_ToString_m6D64DB31243686C6547C230BA5221BC3BC9E4EC2(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___height;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218_il2cpp_TypeInfo_var)))
		{
			goto IL_0015;
		}
	}
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD(__this, ((*(Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*)((Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*)(Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*)UnBox(L_1, Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218_il2cpp_TypeInfo_var)))), NULL);
		return L_2;
	}

IL_0015:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	bool _returnValue;
	_returnValue = Rectangle_Equals_m5B125B9AE09AE711F2218BB7BF9BFE97F054A250(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 L_0 = (*(Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*)__this);
		Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 L_1 = ___0_other;
		bool L_2;
		L_2 = Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD_AdjustorThunk (RuntimeObject* __this, Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 ___0_other, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	bool _returnValue;
	_returnValue = Rectangle_Equals_mC876A9A25614D79581F23875D17F80D1C78910FD(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 ___0_left, Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218 ___1_right, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_op_Equality_m502490E5E78505CE41D65FF3B66339074464B0B2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0;
		L_0 = Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_inline((&___0_left), NULL);
		int32_t L_1;
		L_1 = Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_inline((&___1_right), NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)L_1))))
		{
			goto IL_0041;
		}
	}
	{
		int32_t L_2;
		L_2 = Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_inline((&___0_left), NULL);
		int32_t L_3;
		L_3 = Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_inline((&___1_right), NULL);
		if ((!(((uint32_t)L_2) == ((uint32_t)L_3))))
		{
			goto IL_0041;
		}
	}
	{
		int32_t L_4;
		L_4 = Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_inline((&___0_left), NULL);
		int32_t L_5;
		L_5 = Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_inline((&___1_right), NULL);
		if ((!(((uint32_t)L_4) == ((uint32_t)L_5))))
		{
			goto IL_0041;
		}
	}
	{
		int32_t L_6;
		L_6 = Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_inline((&___0_left), NULL);
		int32_t L_7;
		L_7 = Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_inline((&___1_right), NULL);
		return (bool)((((int32_t)L_6) == ((int32_t)L_7))? 1 : 0);
	}

IL_0041:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0;
		L_0 = Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_inline(__this, NULL);
		int32_t L_1;
		L_1 = Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_inline(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		int32_t L_2;
		L_2 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_0, L_1, NULL);
		int32_t L_3;
		L_3 = Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_inline(__this, NULL);
		int32_t L_4;
		L_4 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_2, L_3, NULL);
		int32_t L_5;
		L_5 = Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_inline(__this, NULL);
		int32_t L_6;
		L_6 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_4, L_5, NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C  int32_t Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Rectangle_GetHashCode_m3B1DDBEEFEF70E4EEB5145960DED4C1A4574D574(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508 (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral44055C491501B7E10D2CB80FC027949A27E74AA1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB5D123B98CED46C8A93EB5109272E39C2E749A8F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEB769CC5B1CE3FFB9133E0ECB36DDB03B09C923D);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)((int32_t)9));
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = L_1;
		int32_t L_3;
		L_3 = Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_inline(__this, NULL);
		V_0 = L_3;
		String_t* L_4;
		L_4 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_2;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteral44055C491501B7E10D2CB80FC027949A27E74AA1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		int32_t L_7;
		L_7 = Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_inline(__this, NULL);
		V_0 = L_7;
		String_t* L_8;
		L_8 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_6;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteralB5D123B98CED46C8A93EB5109272E39C2E749A8F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_10 = L_9;
		int32_t L_11;
		L_11 = Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_inline(__this, NULL);
		V_0 = L_11;
		String_t* L_12;
		L_12 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(5), (String_t*)L_12);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_13 = L_10;
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(6), (String_t*)_stringLiteralEB769CC5B1CE3FFB9133E0ECB36DDB03B09C923D);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_14 = L_13;
		int32_t L_15;
		L_15 = Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_inline(__this, NULL);
		V_0 = L_15;
		String_t* L_16;
		L_16 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		NullCheck(L_14);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(7), (String_t*)L_16);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_17 = L_14;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(8), (String_t*)_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		String_t* L_18;
		L_18 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_17, NULL);
		return L_18;
	}
}
IL2CPP_EXTERN_C  String_t* Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = Rectangle_ToString_m20252CB5611CF6E9F46C82F7D8068FF76AB6C508(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98 (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	float _returnValue;
	_returnValue = RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	float _returnValue;
	_returnValue = RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	float _returnValue;
	_returnValue = RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___height;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	float _returnValue;
	_returnValue = RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975 (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_t553237253FE9D1856528CAE15AF367839B925285_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, RectangleF_t553237253FE9D1856528CAE15AF367839B925285_il2cpp_TypeInfo_var)))
		{
			goto IL_0015;
		}
	}
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA(__this, ((*(RectangleF_t553237253FE9D1856528CAE15AF367839B925285*)((RectangleF_t553237253FE9D1856528CAE15AF367839B925285*)(RectangleF_t553237253FE9D1856528CAE15AF367839B925285*)UnBox(L_1, RectangleF_t553237253FE9D1856528CAE15AF367839B925285_il2cpp_TypeInfo_var)))), NULL);
		return L_2;
	}

IL_0015:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	bool _returnValue;
	_returnValue = RectangleF_Equals_m5D6DE25E3F05FB749F4D089AD8904D7FC4F0A975(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, RectangleF_t553237253FE9D1856528CAE15AF367839B925285 ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RectangleF_t553237253FE9D1856528CAE15AF367839B925285 L_0 = (*(RectangleF_t553237253FE9D1856528CAE15AF367839B925285*)__this);
		RectangleF_t553237253FE9D1856528CAE15AF367839B925285 L_1 = ___0_other;
		bool L_2;
		L_2 = RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA_AdjustorThunk (RuntimeObject* __this, RectangleF_t553237253FE9D1856528CAE15AF367839B925285 ___0_other, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	bool _returnValue;
	_returnValue = RectangleF_Equals_mF6803124E9CD077B02C2E8FABF907A9D186BE7BA(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990 (RectangleF_t553237253FE9D1856528CAE15AF367839B925285 ___0_left, RectangleF_t553237253FE9D1856528CAE15AF367839B925285 ___1_right, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_op_Equality_m5269F1C1E5549CA2991FE8173BBAD6462935C990_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0;
		L_0 = RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_inline((&___0_left), NULL);
		float L_1;
		L_1 = RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_inline((&___1_right), NULL);
		if ((!(((float)L_0) == ((float)L_1))))
		{
			goto IL_0041;
		}
	}
	{
		float L_2;
		L_2 = RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_inline((&___0_left), NULL);
		float L_3;
		L_3 = RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_inline((&___1_right), NULL);
		if ((!(((float)L_2) == ((float)L_3))))
		{
			goto IL_0041;
		}
	}
	{
		float L_4;
		L_4 = RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_inline((&___0_left), NULL);
		float L_5;
		L_5 = RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_inline((&___1_right), NULL);
		if ((!(((float)L_4) == ((float)L_5))))
		{
			goto IL_0041;
		}
	}
	{
		float L_6;
		L_6 = RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_inline((&___0_left), NULL);
		float L_7;
		L_7 = RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_inline((&___1_right), NULL);
		return (bool)((((float)L_6) == ((float)L_7))? 1 : 0);
	}

IL_0041:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8 (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0;
		L_0 = RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_inline(__this, NULL);
		V_0 = L_0;
		int32_t L_1;
		L_1 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		float L_2;
		L_2 = RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_inline(__this, NULL);
		V_0 = L_2;
		int32_t L_3;
		L_3 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		il2cpp_codegen_runtime_class_init_inline(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		int32_t L_4;
		L_4 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_1, L_3, NULL);
		float L_5;
		L_5 = RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_inline(__this, NULL);
		V_0 = L_5;
		int32_t L_6;
		L_6 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		int32_t L_7;
		L_7 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_4, L_6, NULL);
		float L_8;
		L_8 = RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_inline(__this, NULL);
		V_0 = L_8;
		int32_t L_9;
		L_9 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		int32_t L_10;
		L_10 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_7, L_9, NULL);
		return L_10;
	}
}
IL2CPP_EXTERN_C  int32_t RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = RectangleF_GetHashCode_m53718FAB968C4281E4D40ABF6AF94796B345AAF8(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral44055C491501B7E10D2CB80FC027949A27E74AA1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB5D123B98CED46C8A93EB5109272E39C2E749A8F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEB769CC5B1CE3FFB9133E0ECB36DDB03B09C923D);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)((int32_t)9));
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral216BD1B219465997307A6644C66A0F9B82B445DF);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = L_1;
		float L_3;
		L_3 = RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_inline(__this, NULL);
		V_0 = L_3;
		String_t* L_4;
		L_4 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972((&V_0), NULL);
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_2;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteral44055C491501B7E10D2CB80FC027949A27E74AA1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		float L_7;
		L_7 = RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_inline(__this, NULL);
		V_0 = L_7;
		String_t* L_8;
		L_8 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972((&V_0), NULL);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_6;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteralB5D123B98CED46C8A93EB5109272E39C2E749A8F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_10 = L_9;
		float L_11;
		L_11 = RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_inline(__this, NULL);
		V_0 = L_11;
		String_t* L_12;
		L_12 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972((&V_0), NULL);
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(5), (String_t*)L_12);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_13 = L_10;
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(6), (String_t*)_stringLiteralEB769CC5B1CE3FFB9133E0ECB36DDB03B09C923D);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_14 = L_13;
		float L_15;
		L_15 = RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_inline(__this, NULL);
		V_0 = L_15;
		String_t* L_16;
		L_16 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972((&V_0), NULL);
		NullCheck(L_14);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(7), (String_t*)L_16);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_17 = L_14;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(8), (String_t*)_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		String_t* L_18;
		L_18 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_17, NULL);
		return L_18;
	}
}
IL2CPP_EXTERN_C  String_t* RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	RectangleF_t553237253FE9D1856528CAE15AF367839B925285* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<RectangleF_t553237253FE9D1856528CAE15AF367839B925285*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = RectangleF_ToString_m3E02FDFF099EACBADDE0A2F447656C2F283757FB(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D ___0_sz1, Size_t9FCA8981191B4D1A693E50590137D636FEAC156D ___1_sz2, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0;
		L_0 = Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_inline((&___0_sz1), NULL);
		int32_t L_1;
		L_1 = Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_inline((&___1_sz2), NULL);
		if ((!(((uint32_t)L_0) == ((uint32_t)L_1))))
		{
			goto IL_0021;
		}
	}
	{
		int32_t L_2;
		L_2 = Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_inline((&___0_sz1), NULL);
		int32_t L_3;
		L_3 = Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_inline((&___1_sz2), NULL);
		return (bool)((((int32_t)L_2) == ((int32_t)L_3))? 1 : 0);
	}

IL_0021:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961 (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___height;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_t9FCA8981191B4D1A693E50590137D636FEAC156D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, Size_t9FCA8981191B4D1A693E50590137D636FEAC156D_il2cpp_TypeInfo_var)))
		{
			goto IL_0015;
		}
	}
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06(__this, ((*(Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*)((Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*)(Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*)UnBox(L_1, Size_t9FCA8981191B4D1A693E50590137D636FEAC156D_il2cpp_TypeInfo_var)))), NULL);
		return L_2;
	}

IL_0015:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*>(__this + _offset);
	bool _returnValue;
	_returnValue = Size_Equals_m93DF80FE836459123FE02A03242CFE243564512E(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06 (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, Size_t9FCA8981191B4D1A693E50590137D636FEAC156D ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Size_t9FCA8981191B4D1A693E50590137D636FEAC156D L_0 = (*(Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*)__this);
		Size_t9FCA8981191B4D1A693E50590137D636FEAC156D L_1 = ___0_other;
		bool L_2;
		L_2 = Size_op_Equality_m1D2F397A87FCA47D38E8F5C15B947F849DD8C52A(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06_AdjustorThunk (RuntimeObject* __this, Size_t9FCA8981191B4D1A693E50590137D636FEAC156D ___0_other, const RuntimeMethod* method)
{
	Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*>(__this + _offset);
	bool _returnValue;
	_returnValue = Size_Equals_mB5785092C65D4CEFD76E302B468F50AE78C10C06(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424 (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0;
		L_0 = Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_inline(__this, NULL);
		int32_t L_1;
		L_1 = Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_inline(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		int32_t L_2;
		L_2 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  int32_t Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Size_GetHashCode_m7DA961F97C5B8F66002E60938979DA777058E424(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2 (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE7A03561ED4EA323E1B26AF1E6AFAE340B5E3C03);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralED4B98441B4EAD909FAA1D5FBC4C50726902AD3F);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteralED4B98441B4EAD909FAA1D5FBC4C50726902AD3F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = L_1;
		int32_t* L_3 = (int32_t*)(&__this->___width);
		String_t* L_4;
		L_4 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5(L_3, NULL);
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_2;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteralE7A03561ED4EA323E1B26AF1E6AFAE340B5E3C03);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		int32_t* L_7 = (int32_t*)(&__this->___height);
		String_t* L_8;
		L_8 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5(L_7, NULL);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_6;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		String_t* L_10;
		L_10 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_9, NULL);
		return L_10;
	}
}
IL2CPP_EXTERN_C  String_t* Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Size_t9FCA8981191B4D1A693E50590137D636FEAC156D*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = Size_ToString_m9A0FC3B49D251B96F12A244DC5C70C341FFFE2F2(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD ___0_sz1, SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD ___1_sz2, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0;
		L_0 = SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_inline((&___0_sz1), NULL);
		float L_1;
		L_1 = SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_inline((&___1_sz2), NULL);
		if ((!(((float)L_0) == ((float)L_1))))
		{
			goto IL_0021;
		}
	}
	{
		float L_2;
		L_2 = SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_inline((&___0_sz1), NULL);
		float L_3;
		L_3 = SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_inline((&___1_sz2), NULL);
		return (bool)((((float)L_2) == ((float)L_3))? 1 : 0);
	}

IL_0021:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3 (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*>(__this + _offset);
	float _returnValue;
	_returnValue = SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2 (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___height;
		return L_0;
	}
}
IL2CPP_EXTERN_C  float SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*>(__this + _offset);
	float _returnValue;
	_returnValue = SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD_il2cpp_TypeInfo_var)))
		{
			goto IL_0015;
		}
	}
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8(__this, ((*(SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*)((SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*)(SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*)UnBox(L_1, SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD_il2cpp_TypeInfo_var)))), NULL);
		return L_2;
	}

IL_0015:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method)
{
	SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*>(__this + _offset);
	bool _returnValue;
	_returnValue = SizeF_Equals_m8349658750C243186A64E059DE6602CDD809EDEE(_thisAdjusted, ___0_obj, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8 (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD L_0 = (*(SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*)__this);
		SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD L_1 = ___0_other;
		bool L_2;
		L_2 = SizeF_op_Equality_m125E91D7B2C63D963EDCA9A79B056897CFC436AD(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  bool SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8_AdjustorThunk (RuntimeObject* __this, SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD ___0_other, const RuntimeMethod* method)
{
	SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*>(__this + _offset);
	bool _returnValue;
	_returnValue = SizeF_Equals_m42EA81684D6B4D93EDFC4CEB6FFA6ACE9F87F6B8(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0;
		L_0 = SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_inline(__this, NULL);
		V_0 = L_0;
		int32_t L_1;
		L_1 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		float L_2;
		L_2 = SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_inline(__this, NULL);
		V_0 = L_2;
		int32_t L_3;
		L_3 = Single_GetHashCode_mC3F1E099D1CF165C2D71FBCC5EF6A6792F9021D2((&V_0), NULL);
		il2cpp_codegen_runtime_class_init_inline(HashHelpers_t0F28B03B873280BF35E747F5B954C752F924A770_il2cpp_TypeInfo_var);
		int32_t L_4;
		L_4 = HashHelpers_Combine_mB8E75AA81F7543BB0B05069416C485A4B8E692D8(L_1, L_3, NULL);
		return L_4;
	}
}
IL2CPP_EXTERN_C  int32_t SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = SizeF_GetHashCode_m73CD624CAC3855F1EE921AA02CAA389AEAA931CD(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835 (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE7A03561ED4EA323E1B26AF1E6AFAE340B5E3C03);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralED4B98441B4EAD909FAA1D5FBC4C50726902AD3F);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_0 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteralED4B98441B4EAD909FAA1D5FBC4C50726902AD3F);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = L_1;
		float* L_3 = (float*)(&__this->___width);
		String_t* L_4;
		L_4 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972(L_3, NULL);
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_4);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_2;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteralE7A03561ED4EA323E1B26AF1E6AFAE340B5E3C03);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		float* L_7 = (float*)(&__this->___height);
		String_t* L_8;
		L_8 = Single_ToString_mE282EDA9CA4F7DF88432D807732837A629D04972(L_7, NULL);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_6;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)_stringLiteral4D8D9C94AC5DA5FCED2EC8A64E10E714A2515C30);
		String_t* L_10;
		L_10 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_9, NULL);
		return L_10;
	}
}
IL2CPP_EXTERN_C  String_t* SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = SizeF_ToString_m888741B5D1636CE8E9BF5AD6D542F57CA73B5835(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_inline (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_get_X_m461B51706A8EF818C163A1AED09C6B706815AC3A_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_inline (Point_tA4047F553979C79AD01BCA99AB47714D8D920AEE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Point_get_Y_m04B98946BE28DE3A88C2BA7C09236A60936D30E3_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_inline (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_get_X_mA55226AFC51F0CF910D69872A0F19BFBE1F93C9B_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_inline (PointF_tAF6EF9EE016595F150878B97ABC17A16DBEF2138* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, PointF_get_Y_m4B10717E6217968D6F8946A3C77ED5D1AF93A8C3_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_X_m57216246BE34687C3100179002EA5B2A9079776D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_Y_m8D016239D6FA9171C75C071E6CDD3557BF8C0239_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_Width_m08006DBCE23A7EC1B9BA4BAE399141B529B13A52_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_inline (Rectangle_tF1DE9C7C07CBB4836E5B266F6CE357E063934218* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Rectangle_get_Height_m9C3D28B6C72348677EE9EEBA616E65C90B154DB2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___height;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_X_m9A2032884F510E397628C623B4A4C6F037104B98_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___x;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_Y_m37F48864D908EAC8CF080850424B28D503B5B1EF_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___y;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_Width_m44AA143927197CC0337A9A4A275CCD663570531D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_inline (RectangleF_t553237253FE9D1856528CAE15AF367839B925285* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, RectangleF_get_Height_m1AB0E38CB2777AB1C1378765D253E79D8861118D_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___height;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_inline (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_get_Width_m1CAB2903EB07A265C466DF4FE4391DA036FBB1CB_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_inline (Size_t9FCA8981191B4D1A693E50590137D636FEAC156D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, Size_get_Height_mF208E98EB927661F6D660CEC4E286B82F99D4961_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		int32_t L_0 = __this->___height;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_inline (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_get_Width_mDADBBCD3AEEE06E60E11AD0837287C32A0CAA9E3_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___width;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_inline (SizeF_tB0FB4858D62AEB030B1E91200D1019C237518ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, SizeF_get_Height_m811A09B7DE3708B22CD35BA18827637E05DC4DF2_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		float L_0 = __this->___height;
		return L_0;
	}
}
