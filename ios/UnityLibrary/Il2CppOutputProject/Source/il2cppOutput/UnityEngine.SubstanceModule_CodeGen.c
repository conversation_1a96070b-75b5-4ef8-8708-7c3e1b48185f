﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56 (void);
extern void ProceduralMaterial__ctor_m16602D62F215E9B98F076BBE1B050F6A0CD272A8 (void);
extern void ProceduralMaterial_GetProceduralPropertyDescriptions_m68A10072ACA2AC56DFFC0E66F1E698628DCF2CA7 (void);
extern void ProceduralMaterial_HasProceduralProperty_m5A18BB3008FFE11692D1EC242133CD265FCC1ADE (void);
extern void ProceduralMaterial_GetProceduralBoolean_m18783749FD31D0A3D9DC79D22270B06EB941BEB7 (void);
extern void ProceduralMaterial_IsProceduralPropertyVisible_mD867EEBEDA12B26E72499B469941811171E3090A (void);
extern void ProceduralMaterial_SetProceduralBoolean_m495DEA6721AB8FF624C9B16CE51C29FA2B5F2726 (void);
extern void ProceduralMaterial_GetProceduralFloat_m7C8B13D1D264DD1AF5AFAB04EAF8EE9905EB63C7 (void);
extern void ProceduralMaterial_SetProceduralFloat_mCDD13B5FE40F4856AA00C0C20C722B70F79992FE (void);
extern void ProceduralMaterial_GetProceduralVector_m86324726F0451F120C287450C6B6B7FC8C611CA6 (void);
extern void ProceduralMaterial_SetProceduralVector_m8F03A9CB02FECFCD5231C280F33C572C8AC16CB6 (void);
extern void ProceduralMaterial_GetProceduralColor_m80417FD0345304C2A7F6A4C506EF31C033A4A9A4 (void);
extern void ProceduralMaterial_SetProceduralColor_m593DDC78E673D5D0F02B9FE6AE661996D8A5D65C (void);
extern void ProceduralMaterial_GetProceduralEnum_mD7E7B874FBD01B7270342C7864DDA454C43E5187 (void);
extern void ProceduralMaterial_SetProceduralEnum_mCBAB7A515D18306984023477E4FF3C883D17CFB0 (void);
extern void ProceduralMaterial_GetProceduralTexture_m0DE08FCF33082868F65A7C3E97617FAD35C24FD3 (void);
extern void ProceduralMaterial_SetProceduralTexture_m37E6161BFC81B090C932F60F4CD3B70E41BAB3F0 (void);
extern void ProceduralMaterial_GetProceduralString_m20D89FD1614DF70621F5696EB15D3E5349A3D1D9 (void);
extern void ProceduralMaterial_SetProceduralString_m81CCA685414DD4D88A7B05416033307D908C9189 (void);
extern void ProceduralMaterial_IsProceduralPropertyCached_m3D3E4E2A10D46337174958F65662FBFB8BD417CC (void);
extern void ProceduralMaterial_CacheProceduralProperty_mE14C1363B6CC8E0D8110736527F432B28C4A5452 (void);
extern void ProceduralMaterial_ClearCache_m6665395022225A0EAD439F4F9A7630C3E09C955E (void);
extern void ProceduralMaterial_get_cacheSize_m8B5D156D2A8D1E19991FAED47429A0132D12C46F (void);
extern void ProceduralMaterial_set_cacheSize_mF0D3736DE780C25BA87DE642B4DDC20DDE707614 (void);
extern void ProceduralMaterial_get_animationUpdateRate_mE723769BD94211051D09CE9289A5A4E52785BF33 (void);
extern void ProceduralMaterial_set_animationUpdateRate_mECFD2C34670AAB520F44A6CAD8216610767C3DAA (void);
extern void ProceduralMaterial_RebuildTextures_m5B5169A77373138DBF47901F76C98A27AC7CF854 (void);
extern void ProceduralMaterial_RebuildTexturesImmediately_m9A902446FBCE00B8A8784470DAE845CB4B52AC2A (void);
extern void ProceduralMaterial_get_isProcessing_m833CA6339460C32B5432E0CCAE77AE8EB9A1CF7B (void);
extern void ProceduralMaterial_StopRebuilds_mC51D111348A78BBE307C0CCD2A7800323C14DD75 (void);
extern void ProceduralMaterial_get_isCachedDataAvailable_m6EF86A0B138FB7ED937AF36ACC9AE6E880A0A26F (void);
extern void ProceduralMaterial_get_isLoadTimeGenerated_mB79CD55733AAD7BA483EA4CD3F457C54255ACF06 (void);
extern void ProceduralMaterial_set_isLoadTimeGenerated_m0BD9C101D287394F33D8588A990698ED8D8301C8 (void);
extern void ProceduralMaterial_get_loadingBehavior_m573BA7E233C33EEEFC6DC6A3E09605075C385DF0 (void);
extern void ProceduralMaterial_get_isSupported_m84C82139E2F02444F8E253CBDADAD44508D5456D (void);
extern void ProceduralMaterial_get_substanceProcessorUsage_m1116E73AE7F05236B55734F760C3166190ECEFE1 (void);
extern void ProceduralMaterial_set_substanceProcessorUsage_m9F9B8050F6A924109BF351BAE2187F276F95076B (void);
extern void ProceduralMaterial_get_preset_m4AA01BB98A398A0A2DC723D79DBA2398BBC6B5F1 (void);
extern void ProceduralMaterial_set_preset_m937A5F33D8B7B5BD0AAC436C785DBDD4152D9A0C (void);
extern void ProceduralMaterial_GetGeneratedTextures_m8A1048A0F4C23B8B886B53C26EDB7FE92F9D778E (void);
extern void ProceduralMaterial_GetGeneratedTexture_mAD8D1076C1EF6931EBE55747E614EC3011B86354 (void);
extern void ProceduralMaterial_get_isReadable_m4CBA0A192573AC2EA477C51695A9528EB09CF20C (void);
extern void ProceduralMaterial_set_isReadable_m9B7EF37401398C21C102A241AC0DD74CE35B35BF (void);
extern void ProceduralMaterial_FreezeAndReleaseSourceData_mAD86A6D8CBFDF58EE3C7874949A30D7A22811F4A (void);
extern void ProceduralMaterial_get_isFrozen_m7205FE650CFBBADCA4222685B034380FB3955824 (void);
extern void ProceduralPropertyDescription__ctor_m0A11BD30B21500399952D18C9526C6AE77B0F941 (void);
extern void ProceduralTexture__ctor_m6E0E48FE3AECFCE69DE9E865ED74D5989A8586B8 (void);
extern void ProceduralTexture_GetProceduralOutputType_mB3433AFFB6AD3E8332887C12E6BBDC4C8FB3D28C (void);
extern void ProceduralTexture_GetProceduralMaterial_mBD10FB22ECA3D791D41CC2F04633385ADE9A5664 (void);
extern void ProceduralTexture_get_hasAlpha_m0FBB56A7000FAE27EF5FDCAE3FFC990F68F00113 (void);
extern void ProceduralTexture_get_format_m76AFFD512F78936194FB9EA7EAE02D187FC65057 (void);
extern void ProceduralTexture_GetPixels32_mD32FDC3C4F88353C1C27B19BA31BB63F7D024951 (void);
static Il2CppMethodPointer s_methodPointers[52] = 
{
	ProceduralMaterial_FeatureRemoved_mA0BC167B61072F69110459FA2455C8F68E7CBE56,
	ProceduralMaterial__ctor_m16602D62F215E9B98F076BBE1B050F6A0CD272A8,
	ProceduralMaterial_GetProceduralPropertyDescriptions_m68A10072ACA2AC56DFFC0E66F1E698628DCF2CA7,
	ProceduralMaterial_HasProceduralProperty_m5A18BB3008FFE11692D1EC242133CD265FCC1ADE,
	ProceduralMaterial_GetProceduralBoolean_m18783749FD31D0A3D9DC79D22270B06EB941BEB7,
	ProceduralMaterial_IsProceduralPropertyVisible_mD867EEBEDA12B26E72499B469941811171E3090A,
	ProceduralMaterial_SetProceduralBoolean_m495DEA6721AB8FF624C9B16CE51C29FA2B5F2726,
	ProceduralMaterial_GetProceduralFloat_m7C8B13D1D264DD1AF5AFAB04EAF8EE9905EB63C7,
	ProceduralMaterial_SetProceduralFloat_mCDD13B5FE40F4856AA00C0C20C722B70F79992FE,
	ProceduralMaterial_GetProceduralVector_m86324726F0451F120C287450C6B6B7FC8C611CA6,
	ProceduralMaterial_SetProceduralVector_m8F03A9CB02FECFCD5231C280F33C572C8AC16CB6,
	ProceduralMaterial_GetProceduralColor_m80417FD0345304C2A7F6A4C506EF31C033A4A9A4,
	ProceduralMaterial_SetProceduralColor_m593DDC78E673D5D0F02B9FE6AE661996D8A5D65C,
	ProceduralMaterial_GetProceduralEnum_mD7E7B874FBD01B7270342C7864DDA454C43E5187,
	ProceduralMaterial_SetProceduralEnum_mCBAB7A515D18306984023477E4FF3C883D17CFB0,
	ProceduralMaterial_GetProceduralTexture_m0DE08FCF33082868F65A7C3E97617FAD35C24FD3,
	ProceduralMaterial_SetProceduralTexture_m37E6161BFC81B090C932F60F4CD3B70E41BAB3F0,
	ProceduralMaterial_GetProceduralString_m20D89FD1614DF70621F5696EB15D3E5349A3D1D9,
	ProceduralMaterial_SetProceduralString_m81CCA685414DD4D88A7B05416033307D908C9189,
	ProceduralMaterial_IsProceduralPropertyCached_m3D3E4E2A10D46337174958F65662FBFB8BD417CC,
	ProceduralMaterial_CacheProceduralProperty_mE14C1363B6CC8E0D8110736527F432B28C4A5452,
	ProceduralMaterial_ClearCache_m6665395022225A0EAD439F4F9A7630C3E09C955E,
	ProceduralMaterial_get_cacheSize_m8B5D156D2A8D1E19991FAED47429A0132D12C46F,
	ProceduralMaterial_set_cacheSize_mF0D3736DE780C25BA87DE642B4DDC20DDE707614,
	ProceduralMaterial_get_animationUpdateRate_mE723769BD94211051D09CE9289A5A4E52785BF33,
	ProceduralMaterial_set_animationUpdateRate_mECFD2C34670AAB520F44A6CAD8216610767C3DAA,
	ProceduralMaterial_RebuildTextures_m5B5169A77373138DBF47901F76C98A27AC7CF854,
	ProceduralMaterial_RebuildTexturesImmediately_m9A902446FBCE00B8A8784470DAE845CB4B52AC2A,
	ProceduralMaterial_get_isProcessing_m833CA6339460C32B5432E0CCAE77AE8EB9A1CF7B,
	ProceduralMaterial_StopRebuilds_mC51D111348A78BBE307C0CCD2A7800323C14DD75,
	ProceduralMaterial_get_isCachedDataAvailable_m6EF86A0B138FB7ED937AF36ACC9AE6E880A0A26F,
	ProceduralMaterial_get_isLoadTimeGenerated_mB79CD55733AAD7BA483EA4CD3F457C54255ACF06,
	ProceduralMaterial_set_isLoadTimeGenerated_m0BD9C101D287394F33D8588A990698ED8D8301C8,
	ProceduralMaterial_get_loadingBehavior_m573BA7E233C33EEEFC6DC6A3E09605075C385DF0,
	ProceduralMaterial_get_isSupported_m84C82139E2F02444F8E253CBDADAD44508D5456D,
	ProceduralMaterial_get_substanceProcessorUsage_m1116E73AE7F05236B55734F760C3166190ECEFE1,
	ProceduralMaterial_set_substanceProcessorUsage_m9F9B8050F6A924109BF351BAE2187F276F95076B,
	ProceduralMaterial_get_preset_m4AA01BB98A398A0A2DC723D79DBA2398BBC6B5F1,
	ProceduralMaterial_set_preset_m937A5F33D8B7B5BD0AAC436C785DBDD4152D9A0C,
	ProceduralMaterial_GetGeneratedTextures_m8A1048A0F4C23B8B886B53C26EDB7FE92F9D778E,
	ProceduralMaterial_GetGeneratedTexture_mAD8D1076C1EF6931EBE55747E614EC3011B86354,
	ProceduralMaterial_get_isReadable_m4CBA0A192573AC2EA477C51695A9528EB09CF20C,
	ProceduralMaterial_set_isReadable_m9B7EF37401398C21C102A241AC0DD74CE35B35BF,
	ProceduralMaterial_FreezeAndReleaseSourceData_mAD86A6D8CBFDF58EE3C7874949A30D7A22811F4A,
	ProceduralMaterial_get_isFrozen_m7205FE650CFBBADCA4222685B034380FB3955824,
	ProceduralPropertyDescription__ctor_m0A11BD30B21500399952D18C9526C6AE77B0F941,
	ProceduralTexture__ctor_m6E0E48FE3AECFCE69DE9E865ED74D5989A8586B8,
	ProceduralTexture_GetProceduralOutputType_mB3433AFFB6AD3E8332887C12E6BBDC4C8FB3D28C,
	ProceduralTexture_GetProceduralMaterial_mBD10FB22ECA3D791D41CC2F04633385ADE9A5664,
	ProceduralTexture_get_hasAlpha_m0FBB56A7000FAE27EF5FDCAE3FFC990F68F00113,
	ProceduralTexture_get_format_m76AFFD512F78936194FB9EA7EAE02D187FC65057,
	ProceduralTexture_GetPixels32_mD32FDC3C4F88353C1C27B19BA31BB63F7D024951,
};
static const int32_t s_InvokerIndices[52] = 
{
	9089,
	4364,
	4250,
	3185,
	3185,
	3185,
	2788,
	3584,
	2810,
	3679,
	2819,
	3313,
	2790,
	3419,
	2796,
	3518,
	2802,
	3518,
	2802,
	3185,
	2788,
	4364,
	4216,
	3852,
	4216,
	3852,
	4364,
	4364,
	4168,
	9089,
	4168,
	4168,
	3807,
	4216,
	8993,
	9018,
	8880,
	4250,
	3881,
	4250,
	3518,
	4168,
	3807,
	4364,
	4168,
	4364,
	4364,
	4216,
	4250,
	4168,
	4216,
	1169,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SubstanceModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SubstanceModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SubstanceModule_CodeGenModule = 
{
	"UnityEngine.SubstanceModule.dll",
	52,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_SubstanceModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
