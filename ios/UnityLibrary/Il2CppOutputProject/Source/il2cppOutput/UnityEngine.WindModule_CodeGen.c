﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void WindZone_get_mode_mE2B2472178BD6B397D1C9AEF225A38B7AC148FE7 (void);
extern void WindZone_set_mode_mE9FB6F17EC20E82BEA52263D719307EDD1341FD1 (void);
extern void WindZone_get_radius_mCA754177573056EF9C677C83DBD9C2CBD4979516 (void);
extern void WindZone_set_radius_mA01949115ED2AB82C6286D63704AEAE11EB7EDB7 (void);
extern void WindZone_get_windMain_m49480166CDE759C98498B951148535FCCED788C0 (void);
extern void WindZone_set_windMain_mECB9DF2D5BAB9FD545891C3409B36CC57605F2DF (void);
extern void WindZone_get_windTurbulence_mB7D83DE60E30847971D13C6601C46F14839F8448 (void);
extern void WindZone_set_windTurbulence_m61C6D929BE93A628E05A3C6C3557FF660D58231B (void);
extern void WindZone_get_windPulseMagnitude_m6166F0CA589D120036D01F796DBE7201EE2A4724 (void);
extern void WindZone_set_windPulseMagnitude_mC0C0A5D0F89B2D33640CEC9B24AE995C16967325 (void);
extern void WindZone_get_windPulseFrequency_mE95A49751644837A6668834E41F76D66C7E8173C (void);
extern void WindZone_set_windPulseFrequency_m389F653CF33A959B9CCF4998AA18D0E392FCE5BA (void);
extern void WindZone__ctor_m9A197F1A309977C754694D2754C6620566B0F502 (void);
static Il2CppMethodPointer s_methodPointers[13] = 
{
	WindZone_get_mode_mE2B2472178BD6B397D1C9AEF225A38B7AC148FE7,
	WindZone_set_mode_mE9FB6F17EC20E82BEA52263D719307EDD1341FD1,
	WindZone_get_radius_mCA754177573056EF9C677C83DBD9C2CBD4979516,
	WindZone_set_radius_mA01949115ED2AB82C6286D63704AEAE11EB7EDB7,
	WindZone_get_windMain_m49480166CDE759C98498B951148535FCCED788C0,
	WindZone_set_windMain_mECB9DF2D5BAB9FD545891C3409B36CC57605F2DF,
	WindZone_get_windTurbulence_mB7D83DE60E30847971D13C6601C46F14839F8448,
	WindZone_set_windTurbulence_m61C6D929BE93A628E05A3C6C3557FF660D58231B,
	WindZone_get_windPulseMagnitude_m6166F0CA589D120036D01F796DBE7201EE2A4724,
	WindZone_set_windPulseMagnitude_mC0C0A5D0F89B2D33640CEC9B24AE995C16967325,
	WindZone_get_windPulseFrequency_mE95A49751644837A6668834E41F76D66C7E8173C,
	WindZone_set_windPulseFrequency_m389F653CF33A959B9CCF4998AA18D0E392FCE5BA,
	WindZone__ctor_m9A197F1A309977C754694D2754C6620566B0F502,
};
static const int32_t s_InvokerIndices[13] = 
{
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4364,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_WindModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_WindModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_WindModule_CodeGenModule = 
{
	"UnityEngine.WindModule.dll",
	13,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_WindModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
