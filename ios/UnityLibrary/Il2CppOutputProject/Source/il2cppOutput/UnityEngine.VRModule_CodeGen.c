﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void RemoteSpeechAccess_EnableRemoteSpeech_m5CDECFFD114BB620E3430EABDA06C30900308C53 (void);
extern void RemoteSpeechAccess_DisableRemoteSpeech_mBD8847184D433D4B71EC47C6FD19673C32294903 (void);
extern void RemoteSpeechAccess__ctor_mE8920B6A4AF87D8A6ACCE45C50AA8BA792863918 (void);
extern void XRSettings_get_enabled_mC22ABF5BF7D835DAB861A1FA384DBB8904D15E70 (void);
extern void XRSettings_set_enabled_m15084475004A151A847825D5B73881FCEB19FD45 (void);
extern void XRSettings_get_gameViewRenderMode_m7A16CE0A57875BEF1E6A1258F0049DFD94B89B3B (void);
extern void XRSettings_set_gameViewRenderMode_mD4A5BDEBD225B0FA8B0E86CAB7B92B9FC543628F (void);
extern void XRSettings_get_isDeviceActive_m0C8A5F7EC76EF392020137915E4DD8E75EBDD6B8 (void);
extern void XRSettings_get_showDeviceView_m19D1D18E4CB66C282A27C6B5082F58F5B86D0694 (void);
extern void XRSettings_set_showDeviceView_m308871D4216C8EA4922D51021BA0DDA50DFBD9D8 (void);
extern void XRSettings_get_eyeTextureResolutionScale_m335B9DB51528FCA7534FCD6828796395D63ADA90 (void);
extern void XRSettings_set_eyeTextureResolutionScale_m92F1029D68F387D9B0C2DB35DFAB2FD82C64A30B (void);
extern void XRSettings_get_eyeTextureWidth_m3B18AF3F3382398E2A818B2B01AA1FE90FEB3AAF (void);
extern void XRSettings_get_eyeTextureHeight_mCF4B2EC6851A8B8A8C4E6FC085A621B3166DB67A (void);
extern void XRSettings_get_eyeTextureDesc_mFBE8F6D5D5A23E4DE1BCCD994ADFAB4FB11D7A19 (void);
extern void XRSettings_get_deviceEyeTextureDimension_m4390A767BC56B39357F10C9B20293B8C0D0D6D7F (void);
extern void XRSettings_get_renderViewportScale_mB35A32F5FE6B2EEE0CEF95ADFC04F171B6E5F5D1 (void);
extern void XRSettings_set_renderViewportScale_m96E308EEAE4B92F92ACD2866E3958070FA3E5313 (void);
extern void XRSettings_get_renderViewportScaleInternal_mC9FFB83588F0865E76B78FB334AE6AAF0FF2EC24 (void);
extern void XRSettings_set_renderViewportScaleInternal_m938F25CB6C2F43493823E5C00A9A42EC721110B3 (void);
extern void XRSettings_get_occlusionMaskScale_mFA46FF99DD385D5D88247C356DA2B454B4C8132D (void);
extern void XRSettings_set_occlusionMaskScale_mFB321295E98DE1A01268FABCCB167CC2DCD8157E (void);
extern void XRSettings_get_useOcclusionMesh_mE22A93654132323381A6D7C7C24CB5DAFF5371D9 (void);
extern void XRSettings_set_useOcclusionMesh_m0FECDD87F3C902210458D21E5404910D5917EF52 (void);
extern void XRSettings_get_loadedDeviceName_mAEB3908916B98A9E8CF2FD8754B5AAB096245243 (void);
extern void XRSettings_LoadDeviceByName_mE151AD9057808A4079A5DCF6981B80E0C18BDD1E (void);
extern void XRSettings_LoadDeviceByName_m034F3EF9E0C63932105CE7E4E94AE23EC062D005 (void);
extern void XRSettings_get_supportedDevices_m9ABC69D1044484DF7ED2B236AFCCD8BF107BB74C (void);
extern void XRSettings_get_stereoRenderingMode_mD66918C11E2216B1F8FA76934F79D5F85BC303FC (void);
extern void XRSettings_get_eyeTextureDesc_Injected_m2B01F9A50CE1E88530044A5D342C1AE151BA17B5 (void);
extern void XRDevice_get_isPresent_mB8509D434BC3A300158AC4D4690F823315852814 (void);
extern void XRDevice_get_refreshRate_mCDE089722DEF165AC260B04BBC248620C697A5DD (void);
extern void XRDevice_GetNativePtr_m80448E2F76201232D28FB8617A76EAF73979B0BB (void);
extern void XRDevice_GetTrackingSpaceType_m160BD854CB480932E29017A40C46E648B064532A (void);
extern void XRDevice_SetTrackingSpaceType_m8A35E391EF1DA72CD9091C137C384F31FF23530B (void);
extern void XRDevice_DisableAutoXRCameraTracking_m1243FCAD2AC9C4D5C2E551255A1B2BA266E12A52 (void);
extern void XRDevice_UpdateEyeTextureMSAASetting_mEE78487AD7026276234D776ADE90E65973CF0CBC (void);
extern void XRDevice_get_fovZoomFactor_mB87D67173DE0F010349BBFD6B7CDC13F7331BC26 (void);
extern void XRDevice_set_fovZoomFactor_mCBDC1CA1650AD0772F9CCF61490E4465138A2DAD (void);
extern void XRDevice_add_deviceLoaded_mD6171CDAA8333DB511A5B368DD89934124141EFB (void);
extern void XRDevice_remove_deviceLoaded_m1A1FB3A62F6AD9036E602673C8A55F03F4B502AF (void);
extern void XRDevice_InvokeDeviceLoaded_mBE2198DE44A72E2F5059566C46B9907D82782790 (void);
extern void XRStats_TryGetGPUTimeLastFrame_mBA5DF9EBBAEAE27F75273CBEB41F57D4A34C5672 (void);
extern void XRStats_TryGetDroppedFrameCount_m01188274E91F733A5D17534716024F391692252B (void);
extern void XRStats_TryGetFramePresentCount_m4B485302250E75A1F2B442D2FF5BF668D752DD12 (void);
static Il2CppMethodPointer s_methodPointers[45] = 
{
	RemoteSpeechAccess_EnableRemoteSpeech_m5CDECFFD114BB620E3430EABDA06C30900308C53,
	RemoteSpeechAccess_DisableRemoteSpeech_mBD8847184D433D4B71EC47C6FD19673C32294903,
	RemoteSpeechAccess__ctor_mE8920B6A4AF87D8A6ACCE45C50AA8BA792863918,
	XRSettings_get_enabled_mC22ABF5BF7D835DAB861A1FA384DBB8904D15E70,
	XRSettings_set_enabled_m15084475004A151A847825D5B73881FCEB19FD45,
	XRSettings_get_gameViewRenderMode_m7A16CE0A57875BEF1E6A1258F0049DFD94B89B3B,
	XRSettings_set_gameViewRenderMode_mD4A5BDEBD225B0FA8B0E86CAB7B92B9FC543628F,
	XRSettings_get_isDeviceActive_m0C8A5F7EC76EF392020137915E4DD8E75EBDD6B8,
	XRSettings_get_showDeviceView_m19D1D18E4CB66C282A27C6B5082F58F5B86D0694,
	XRSettings_set_showDeviceView_m308871D4216C8EA4922D51021BA0DDA50DFBD9D8,
	XRSettings_get_eyeTextureResolutionScale_m335B9DB51528FCA7534FCD6828796395D63ADA90,
	XRSettings_set_eyeTextureResolutionScale_m92F1029D68F387D9B0C2DB35DFAB2FD82C64A30B,
	XRSettings_get_eyeTextureWidth_m3B18AF3F3382398E2A818B2B01AA1FE90FEB3AAF,
	XRSettings_get_eyeTextureHeight_mCF4B2EC6851A8B8A8C4E6FC085A621B3166DB67A,
	XRSettings_get_eyeTextureDesc_mFBE8F6D5D5A23E4DE1BCCD994ADFAB4FB11D7A19,
	XRSettings_get_deviceEyeTextureDimension_m4390A767BC56B39357F10C9B20293B8C0D0D6D7F,
	XRSettings_get_renderViewportScale_mB35A32F5FE6B2EEE0CEF95ADFC04F171B6E5F5D1,
	XRSettings_set_renderViewportScale_m96E308EEAE4B92F92ACD2866E3958070FA3E5313,
	XRSettings_get_renderViewportScaleInternal_mC9FFB83588F0865E76B78FB334AE6AAF0FF2EC24,
	XRSettings_set_renderViewportScaleInternal_m938F25CB6C2F43493823E5C00A9A42EC721110B3,
	XRSettings_get_occlusionMaskScale_mFA46FF99DD385D5D88247C356DA2B454B4C8132D,
	XRSettings_set_occlusionMaskScale_mFB321295E98DE1A01268FABCCB167CC2DCD8157E,
	XRSettings_get_useOcclusionMesh_mE22A93654132323381A6D7C7C24CB5DAFF5371D9,
	XRSettings_set_useOcclusionMesh_m0FECDD87F3C902210458D21E5404910D5917EF52,
	XRSettings_get_loadedDeviceName_mAEB3908916B98A9E8CF2FD8754B5AAB096245243,
	XRSettings_LoadDeviceByName_mE151AD9057808A4079A5DCF6981B80E0C18BDD1E,
	XRSettings_LoadDeviceByName_m034F3EF9E0C63932105CE7E4E94AE23EC062D005,
	XRSettings_get_supportedDevices_m9ABC69D1044484DF7ED2B236AFCCD8BF107BB74C,
	XRSettings_get_stereoRenderingMode_mD66918C11E2216B1F8FA76934F79D5F85BC303FC,
	XRSettings_get_eyeTextureDesc_Injected_m2B01F9A50CE1E88530044A5D342C1AE151BA17B5,
	XRDevice_get_isPresent_mB8509D434BC3A300158AC4D4690F823315852814,
	XRDevice_get_refreshRate_mCDE089722DEF165AC260B04BBC248620C697A5DD,
	XRDevice_GetNativePtr_m80448E2F76201232D28FB8617A76EAF73979B0BB,
	XRDevice_GetTrackingSpaceType_m160BD854CB480932E29017A40C46E648B064532A,
	XRDevice_SetTrackingSpaceType_m8A35E391EF1DA72CD9091C137C384F31FF23530B,
	XRDevice_DisableAutoXRCameraTracking_m1243FCAD2AC9C4D5C2E551255A1B2BA266E12A52,
	XRDevice_UpdateEyeTextureMSAASetting_mEE78487AD7026276234D776ADE90E65973CF0CBC,
	XRDevice_get_fovZoomFactor_mB87D67173DE0F010349BBFD6B7CDC13F7331BC26,
	XRDevice_set_fovZoomFactor_mCBDC1CA1650AD0772F9CCF61490E4465138A2DAD,
	XRDevice_add_deviceLoaded_mD6171CDAA8333DB511A5B368DD89934124141EFB,
	XRDevice_remove_deviceLoaded_m1A1FB3A62F6AD9036E602673C8A55F03F4B502AF,
	XRDevice_InvokeDeviceLoaded_mBE2198DE44A72E2F5059566C46B9907D82782790,
	XRStats_TryGetGPUTimeLastFrame_mBA5DF9EBBAEAE27F75273CBEB41F57D4A34C5672,
	XRStats_TryGetDroppedFrameCount_m01188274E91F733A5D17534716024F391692252B,
	XRStats_TryGetFramePresentCount_m4B485302250E75A1F2B442D2FF5BF668D752DD12,
};
static const int32_t s_InvokerIndices[45] = 
{
	8880,
	9089,
	4364,
	8993,
	8868,
	9018,
	8880,
	8993,
	8993,
	8868,
	9064,
	8899,
	9018,
	9018,
	9055,
	9018,
	9064,
	8899,
	9064,
	8899,
	9064,
	8899,
	8993,
	8868,
	9031,
	8887,
	8887,
	9031,
	9018,
	8867,
	8993,
	9064,
	9020,
	9018,
	8216,
	7963,
	9089,
	9064,
	8899,
	8887,
	8887,
	8887,
	8200,
	8200,
	8200,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_VRModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_VRModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_VRModule_CodeGenModule = 
{
	"UnityEngine.VRModule.dll",
	45,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_VRModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
