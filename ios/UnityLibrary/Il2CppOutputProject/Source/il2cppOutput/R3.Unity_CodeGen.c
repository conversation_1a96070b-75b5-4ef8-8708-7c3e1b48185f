﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void PlayerLoopHelper_Init_m5109ECF406895CF583C6EC005BE3D333445E5898 (void);
extern void PlayerLoopHelper_Initialize_mEA5B98D3F5BE539CC7A3C1EF915667090A9B340D (void);
extern void PlayerLoopHelper_InsertLoop_mBBAAA9F237CB1B1D192DCE2680E3802E56B7AAA9 (void);
extern void PlayerLoopHelper_FindLoopSystemIndex_mB7E350F8FF3F46B38425BD0CB9307820E0DDDDC6 (void);
extern void PlayerLoopHelper_InsertRunner_m6FC0AF36206ED267C940C5815DD2B2541F1E32D2 (void);
extern void U3CU3Ec__DisplayClass8_0__ctor_mF741093157A9A4A513E488A25A9F30FAFFA81C07 (void);
extern void U3CU3Ec__DisplayClass8_0_U3CInsertRunnerU3Eb__0_m177B41936211949CF2305C0114B8665D9B764C67 (void);
extern void UnityFrameProvider_get_PlayerLoopTiming_m43770D2CD4B4D11F0F52DF12E1CD504A8F486285 (void);
extern void UnityFrameProvider__ctor_m8187C500B64AA2EDCD8BA7054460369317D4BF68 (void);
extern void UnityFrameProvider_Register_m7C511B73856E8768D18BA78ABF1A5C24A8BE213C (void);
extern void UnityFrameProvider_Run_mCC738CA74994A5C44C710C8B13DABFDC6262C3E2 (void);
extern void UnityFrameProvider__cctor_m1DB4D2EACC7394FCF4675CB15A260D8DDC1014B8 (void);
extern void UnityProviderInitializer__cctor_mC972D9CDC2DE18E6F98B05571D31F2D249DEE8E4 (void);
extern void UnityProviderInitializer_SetDefaultObservableSystem_m291BE9731C71C2C2F18E42B7D22EC8F47B50A334 (void);
extern void UnityProviderInitializer_SetDefaultObservableSystem_mB0E5ACDC2AC11E6750B232BED84EC1396638FB91 (void);
extern void U3CU3Ec__cctor_m79405D88D1CAA8C37648FC61EEDE4BCAE1A297B7 (void);
extern void U3CU3Ec__ctor_m315DBBB82A17F910403F7D1BFCEFC9F955579103 (void);
extern void U3CU3Ec_U3CSetDefaultObservableSystemU3Eb__1_0_m6B87B4D87F2004F2E456B00060CEA77BE448CEB7 (void);
extern void UnityTimeProvider__ctor_m78DC462BBC40D8DF7841E3C5BC8BAD045B176CC0 (void);
extern void UnityTimeProvider__cctor_mB925B90C16294A67D4BB7F86167D8F8C9D44B1FD (void);
extern void ObservableAnimatorTrigger_OnAnimatorIK_m779E3120F8F6823A1C5AE238F2BAAAE7E6FB4E84 (void);
extern void ObservableAnimatorTrigger_OnAnimatorIKAsObservable_m2C1A9912045A9F7ABE926E254E0DC7462AB6808B (void);
extern void ObservableAnimatorTrigger_OnAnimatorMove_mDD41F7FE3154FDA24A8DA46803A5375C51B078C0 (void);
extern void ObservableAnimatorTrigger_OnAnimatorMoveAsObservable_mDBA3D84000257A2697B6FBB4092A641D570EB459 (void);
extern void ObservableAnimatorTrigger_RaiseOnCompletedOnDestroy_mC214D9AB48028E1923A5B93B6A84299761FC6298 (void);
extern void ObservableAnimatorTrigger__ctor_m80DF6B8867D6F0621FE7D5878BE9F4B11FF30C0C (void);
extern void ObservableBeginDragTrigger_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mF7133B158EFB4D7672EC832A7A1908C621402D24 (void);
extern void ObservableBeginDragTrigger_OnBeginDragAsObservable_m6278184765AC87178CB98896374F126069E70F83 (void);
extern void ObservableBeginDragTrigger_RaiseOnCompletedOnDestroy_m8977DD954F10B02C595D00D5CDCA9BCECA31E43C (void);
extern void ObservableBeginDragTrigger__ctor_m8FF54475D58B50489FF3A7493B2F1DEA16EB9DB9 (void);
extern void ObservableCancelTrigger_UnityEngine_EventSystems_ICancelHandler_OnCancel_mBF565D4F89DE39BC91CE681386AB49B3ADC9F55C (void);
extern void ObservableCancelTrigger_OnCancelAsObservable_m0D9BA25C5468AAEB0002C4D250211C258974E123 (void);
extern void ObservableCancelTrigger_RaiseOnCompletedOnDestroy_mDF540155AEA6BF725FA7476B45AE38D75F565751 (void);
extern void ObservableCancelTrigger__ctor_m71FEFAA9DD9F4B316BFA2B0217E11B1C3F91889D (void);
extern void ObservableCanvasGroupChangedTrigger_OnCanvasGroupChanged_m2B3321F538A2A1EC4CE47C83CDC68EB95F09296B (void);
extern void ObservableCanvasGroupChangedTrigger_OnCanvasGroupChangedAsObservable_m0273D57F8ABBB65BBAE3334657ACD5D30FB6C6E6 (void);
extern void ObservableCanvasGroupChangedTrigger_RaiseOnCompletedOnDestroy_m0CA78F9E61C749065D62F45857BC4C704A3F1381 (void);
extern void ObservableCanvasGroupChangedTrigger__ctor_m9EEDC2DEDBDF30D83B9539AB7739652361162A09 (void);
extern void ObservableCollision2DTrigger_OnCollisionEnter2D_mE3C0541928FE630CD6983B7732665AC71FF2CE9B (void);
extern void ObservableCollision2DTrigger_OnCollisionEnter2DAsObservable_m61E43556E6511758CF9064DAADF4DCDC424DB7A7 (void);
extern void ObservableCollision2DTrigger_OnCollisionExit2D_m9912EA35CC3B2918CE8D1B48BFBDC2311381CC82 (void);
extern void ObservableCollision2DTrigger_OnCollisionExit2DAsObservable_m342B5CD0B4B5647DA0AEDE491DB3FB061178BC4B (void);
extern void ObservableCollision2DTrigger_OnCollisionStay2D_m3B96C55A29797610E2A047B201B1F3D034BB9065 (void);
extern void ObservableCollision2DTrigger_OnCollisionStay2DAsObservable_m183BD2B6E6B6B5D040CA0EBB60D1EDFA65237363 (void);
extern void ObservableCollision2DTrigger_RaiseOnCompletedOnDestroy_mBEFBD74FE191180CEADBE3C81809DDE6E3CCD475 (void);
extern void ObservableCollision2DTrigger__ctor_m6DDC7E69C6CB865039F947BC2D2A9BE5C0F46816 (void);
extern void ObservableCollisionTrigger_OnCollisionEnter_mA70ED05310153F5B4E61E733813A3135533F476B (void);
extern void ObservableCollisionTrigger_OnCollisionEnterAsObservable_m457C4B933C07442E6F0ECD920C1256496F6FB601 (void);
extern void ObservableCollisionTrigger_OnCollisionExit_m1438F4A64B4D2E597BA27E9E6C1798CD57E1642F (void);
extern void ObservableCollisionTrigger_OnCollisionExitAsObservable_m5CCECB4AB654D3C0BCBF300CE4E3BC1370A2D388 (void);
extern void ObservableCollisionTrigger_OnCollisionStay_mE9C4AFB1E38187EAD7304BD321BA8445090DF729 (void);
extern void ObservableCollisionTrigger_OnCollisionStayAsObservable_mC6FA60D7D94C2B80297D9180527C08BEF9C8425D (void);
extern void ObservableCollisionTrigger_RaiseOnCompletedOnDestroy_mB37826467FBC446E61FEFD5EFD74BD736B2BE22B (void);
extern void ObservableCollisionTrigger__ctor_m4F42672C8752F4ADA78ECD3ECA5B997393F789C1 (void);
extern void ObservableDeselectTrigger_UnityEngine_EventSystems_IDeselectHandler_OnDeselect_m8F24746E3181EF462542E082A1FDAC6A799DE5F2 (void);
extern void ObservableDeselectTrigger_OnDeselectAsObservable_m4857A9B1AB70437517903C5BE1B67CA650A18891 (void);
extern void ObservableDeselectTrigger_RaiseOnCompletedOnDestroy_m5AC8A8B8639C7AF155BFDD4EB8EA11B53A6183C5 (void);
extern void ObservableDeselectTrigger__ctor_mA3D96E2084400CCEA6D8D5B268887ECAECDE5761 (void);
extern void ObservableDestroyTrigger_get_IsActivated_m508EA14083F0456B6CE97A500F696CCC615467D2 (void);
extern void ObservableDestroyTrigger_set_IsActivated_mA720868A4D4C6B55DFB7147670899218FEF86E0F (void);
extern void ObservableDestroyTrigger_GetCancellationToken_mAE8ED38E9E50A08B033780D1F9A213519F602193 (void);
extern void ObservableDestroyTrigger_AddDisposableOnDestroy_m80D68C19F9162AEA2C6C4B733924A0ABE285BB50 (void);
extern void ObservableDestroyTrigger_Awake_m36ADA578F16BF7982649C8FF1D0D8479F690063B (void);
extern void ObservableDestroyTrigger_OnDestroy_m9F5E030A0D51A2E145110F10FB7E5CE599F2D936 (void);
extern void ObservableDestroyTrigger_OnDestroyAsObservable_m24D8B4395144031B9A4041C3BC764FB7DD45D1F9 (void);
extern void ObservableDestroyTrigger_TryStartActivateMonitoring_m9836D820DDE00C1E875DCA7A8B1D4F3C900A3C84 (void);
extern void ObservableDestroyTrigger_R3_IFrameRunnerWorkItem_MoveNext_m9B4DA1D4574CAFBD505EAC57681BEE83729F0A37 (void);
extern void ObservableDestroyTrigger__ctor_mBA657627AC96E2EF57C436D6CAB266134D3D2D50 (void);
extern void ObservableDragTrigger_UnityEngine_EventSystems_IDragHandler_OnDrag_m91D72C0DA8146F826CE01051C37E11BE3ADFDDD9 (void);
extern void ObservableDragTrigger_OnDragAsObservable_mDDE78B5E04B1F27522B61BD97AF6C3995223BDED (void);
extern void ObservableDragTrigger_RaiseOnCompletedOnDestroy_m7BEC66EDB4F78B0772EA5528F768381DD720521C (void);
extern void ObservableDragTrigger__ctor_mC613FC3CDE928D94497B8CC51CA76FDB4314D855 (void);
extern void ObservableDropTrigger_UnityEngine_EventSystems_IDropHandler_OnDrop_m42C04BF77CE647B32D6866DF394A1296D57D1007 (void);
extern void ObservableDropTrigger_OnDropAsObservable_m15CAB46C03406C83D8504A0C70A0492A63F32A68 (void);
extern void ObservableDropTrigger_RaiseOnCompletedOnDestroy_m9AF9B689108BDBE071FCE0E0B928A587BDBE7F9B (void);
extern void ObservableDropTrigger__ctor_mD650D3D9F88B5ED179057B70BC71E82BCCF41DFF (void);
extern void ObservableEnableTrigger_OnEnable_m0518E18A5C898169840F71AFE450CEE282C03767 (void);
extern void ObservableEnableTrigger_OnEnableAsObservable_m0CD325078B0E44C002835EC9C6E7D1543E65A9AF (void);
extern void ObservableEnableTrigger_OnDisable_m658301A42EC79471C6CACD636DEE436A4B75E26A (void);
extern void ObservableEnableTrigger_OnDisableAsObservable_mF1FCC0D5AB988770F3AA572D38E502F87E05BA89 (void);
extern void ObservableEnableTrigger_RaiseOnCompletedOnDestroy_m184B07A0567BDAD8D6C91780DE5AABD3EEC8199D (void);
extern void ObservableEnableTrigger__ctor_m4FFE7423393E0F06CC79489D598ADD3D55F409E5 (void);
extern void ObservableEndDragTrigger_UnityEngine_EventSystems_IEndDragHandler_OnEndDrag_m7BA833F7E733AA94400EFF8B9D659B7B367939FE (void);
extern void ObservableEndDragTrigger_OnEndDragAsObservable_m0C9D9EFF834AA4DACF81668D541904AB19DD58AF (void);
extern void ObservableEndDragTrigger_RaiseOnCompletedOnDestroy_m0CD33AD6CEA8901945672A3F6FFCACD55943273F (void);
extern void ObservableEndDragTrigger__ctor_mA08DEC33FA2F4497490038ACAA3A5493D57E655E (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IDeselectHandler_OnDeselect_mC3897AC7EC26869217D75C91F86188C6677E2175 (void);
extern void ObservableEventTrigger_OnDeselectAsObservable_mB2FB48FEF6B41A722697B991833DCEEFE875C21F (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IMoveHandler_OnMove_m20630B8FD1C7868121FB8664EA414107E24FE893 (void);
extern void ObservableEventTrigger_OnMoveAsObservable_m2749E28D012B6DBE16349BF4917E49599DEE0C90 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IPointerDownHandler_OnPointerDown_mA107C5BBD8E2C40301C6F11B30B47ECF1FE215C9 (void);
extern void ObservableEventTrigger_OnPointerDownAsObservable_mE4BCDF37A69A5CF2A65C5E523D9E9D2DFAEDF6DB (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IPointerEnterHandler_OnPointerEnter_mDEEE143F4AF9981FACB9A56C4EE6964A6D5EF5AD (void);
extern void ObservableEventTrigger_OnPointerEnterAsObservable_mB9BD82DDCE61BFF8DEB997DDF2C7BDF294E8E854 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IPointerExitHandler_OnPointerExit_m671D3B8C30B3EB7ED6A1D642CC5ECBCC53E65920 (void);
extern void ObservableEventTrigger_OnPointerExitAsObservable_mA06208BAECA6C407E0D6DFF1FA191530DD2A483E (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IPointerUpHandler_OnPointerUp_m08C0667026EFF2CAD1BC65DAAB92BB072CECDB07 (void);
extern void ObservableEventTrigger_OnPointerUpAsObservable_m9937ED0F500DDF2042101916B6235BADB2377E66 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_ISelectHandler_OnSelect_mF92C711E2CFEAF84B99E9E00EF201F25B09B505C (void);
extern void ObservableEventTrigger_OnSelectAsObservable_mBA81538A7773D4AC964534576C7EA6BD9A1F039D (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IPointerClickHandler_OnPointerClick_m1F80C43AE462EEA73A4E1FB22D7DCDFF39553C46 (void);
extern void ObservableEventTrigger_OnPointerClickAsObservable_mD4DE565E62DC6AFA46CA1931D4A91DEB120A5044 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_ISubmitHandler_OnSubmit_mFBF14BE27D464C7D86281E63A6D5EA602C49A25E (void);
extern void ObservableEventTrigger_OnSubmitAsObservable_m50860550C4F33E54C398230E9554EF66F04FA585 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IDragHandler_OnDrag_m8AAD28D7B065AEF3E3D72CB7BCD033C94B581016 (void);
extern void ObservableEventTrigger_OnDragAsObservable_mC17C91FB20928CF8F35477546AFDA5BE0AE59812 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mC62266EFA932C02EB4E0DF70D6F9B7FE20D76635 (void);
extern void ObservableEventTrigger_OnBeginDragAsObservable_mA052337F28D5D9DA8F576EB51B07C1DAAE9EA2AF (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IEndDragHandler_OnEndDrag_m97F68F664780CF08B927B6EE5CD506F99BCA4D2B (void);
extern void ObservableEventTrigger_OnEndDragAsObservable_mB29A89A9E6F3F528A74ABE162C3900F85412771E (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IDropHandler_OnDrop_m81F5064518A6AC3488AB87AE548E9A104654A766 (void);
extern void ObservableEventTrigger_OnDropAsObservable_mDFFC6DB38788C2CC331342B0D8F6BA17A44E0707 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IUpdateSelectedHandler_OnUpdateSelected_m39870ED733B3239FE2D1DC3F9421985432B01C71 (void);
extern void ObservableEventTrigger_OnUpdateSelectedAsObservable_mD39CE251C7757FFF5CC8FEF651F4BCCA3E82106D (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IInitializePotentialDragHandler_OnInitializePotentialDrag_mAC7C78ACD94FE0CFB35F277FBFADC8E9963BFFFD (void);
extern void ObservableEventTrigger_OnInitializePotentialDragAsObservable_mDEC5CC106AB0B3063C62863914C58F2D132AE764 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_ICancelHandler_OnCancel_m4078ED6DC82BD1F334B014C0B876CB9C663BCA4A (void);
extern void ObservableEventTrigger_OnCancelAsObservable_m7DEFED425D1E8B381EC769F3079DB3E341E76842 (void);
extern void ObservableEventTrigger_UnityEngine_EventSystems_IScrollHandler_OnScroll_mC7E0207492E793EE569DEBD878DD3DAED0606320 (void);
extern void ObservableEventTrigger_OnScrollAsObservable_mAEFDE56FC1E4F63F162230F60F408363DB14F532 (void);
extern void ObservableEventTrigger_RaiseOnCompletedOnDestroy_mDC8A3392EB47EF028998AD411194715801636BAE (void);
extern void ObservableEventTrigger__ctor_mEC338589626B1EB1ABE91E5AA15BD071C7B6977E (void);
extern void ObservableFixedUpdateTrigger_FixedUpdate_mB079DF25267E62C1FAD1BD062ACE5B90FE5734D1 (void);
extern void ObservableFixedUpdateTrigger_FixedUpdateAsObservable_m159C6851FB5B2587A8C26F2483F467533513B409 (void);
extern void ObservableFixedUpdateTrigger_RaiseOnCompletedOnDestroy_m62C0AEA4927DF59F1D9E84634A3E8C3AFCAEE753 (void);
extern void ObservableFixedUpdateTrigger__ctor_m9D2358CDDA6C01146E889D9892E717433854600E (void);
extern void ObservableInitializePotentialDragTrigger_UnityEngine_EventSystems_IInitializePotentialDragHandler_OnInitializePotentialDrag_m2C70CAA61D5D360B2E8C9894AB2618B2415F0AB3 (void);
extern void ObservableInitializePotentialDragTrigger_OnInitializePotentialDragAsObservable_m76886277DD3720295B7977A71157F0210C410BA4 (void);
extern void ObservableInitializePotentialDragTrigger_RaiseOnCompletedOnDestroy_m9162DBDB846B177499556EA54E17274AE7B1FB2A (void);
extern void ObservableInitializePotentialDragTrigger__ctor_m0DCB412429D97FCBC4DB16FE007DC9635B47271F (void);
extern void ObservableJointTrigger_OnJointBreak_m2F18A63D0BAF137BE59E75974B111DC85E73177F (void);
extern void ObservableJointTrigger_OnJointBreakAsObservable_m3240362EDEFFE4A031262E629508292AC42F76DE (void);
extern void ObservableJointTrigger_OnJointBreak2D_m060300D9A1512DC5E44EA298A3A1DC147230A787 (void);
extern void ObservableJointTrigger_OnJointBreak2DAsObservable_mCD4EE5B5B472703973969BE6B8828F99AEEEA20D (void);
extern void ObservableJointTrigger_RaiseOnCompletedOnDestroy_m6A554C6E55DCD4D08E60528C7C5DC74EB74FECAC (void);
extern void ObservableJointTrigger__ctor_mB5FA9BBCC98FE99BEB380F4D47E200AD2FD05FD8 (void);
extern void ObservableLateUpdateTrigger_LateUpdate_m8FAA1B297A01454498DC0AAD7F231FD12F49A70E (void);
extern void ObservableLateUpdateTrigger_LateUpdateAsObservable_mFF813B0747A34F26542AE4D0872187574AB44AAE (void);
extern void ObservableLateUpdateTrigger_RaiseOnCompletedOnDestroy_mDA4C9E35A65591ED29C9D4E9C00E24B7389C1C8D (void);
extern void ObservableLateUpdateTrigger__ctor_mB9997C95E893DF965FC3C89CB016F9E4A7FC8E6D (void);
extern void ObservableMoveTrigger_UnityEngine_EventSystems_IMoveHandler_OnMove_m3F38D22461839B30248833BC1524C80F229075A9 (void);
extern void ObservableMoveTrigger_OnMoveAsObservable_m67E0A0985C7C13B0870980DC780C21B357ABC349 (void);
extern void ObservableMoveTrigger_RaiseOnCompletedOnDestroy_m8FBB3E2861086DF5304D2E53123A66A9595B407E (void);
extern void ObservableMoveTrigger__ctor_m86AA2294E0CAEDDB2EF59A6CD3C13DEF0B0279CC (void);
extern void ObservableParticleTrigger_OnParticleCollision_m0CFEB6694AF45DD45B13E2E353D22017CBB60CAA (void);
extern void ObservableParticleTrigger_OnParticleCollisionAsObservable_m48D2B1EA242614F56AF5FC2AA09C025691865CA3 (void);
extern void ObservableParticleTrigger_OnParticleTrigger_mF6BB8656F2D3F2DF76112F26AE08B603862071F5 (void);
extern void ObservableParticleTrigger_OnParticleTriggerAsObservable_m498ECB42A0D0588FBDEFB311B7BA6F74F36FCCCC (void);
extern void ObservableParticleTrigger_RaiseOnCompletedOnDestroy_m13DC15E1BE2DE1F7BAFBC73E540636746ABF0573 (void);
extern void ObservableParticleTrigger__ctor_mF2D090B632BD298BA5E09F53F66F272ADABF2634 (void);
extern void ObservablePointerClickTrigger_UnityEngine_EventSystems_IPointerClickHandler_OnPointerClick_mD652241F57AD835188DCF323CE9CA19041323E4D (void);
extern void ObservablePointerClickTrigger_OnPointerClickAsObservable_m771096298A65888256F5AA0DD5A95C0A95226F1F (void);
extern void ObservablePointerClickTrigger_RaiseOnCompletedOnDestroy_m3EFBAAE97873C6662C4E82E9BF937D20E3E7A0D9 (void);
extern void ObservablePointerClickTrigger__ctor_mDBB57F594D1AEE003152DB98D9F0BC0D0CF2CF81 (void);
extern void ObservablePointerDownTrigger_UnityEngine_EventSystems_IPointerDownHandler_OnPointerDown_m1205BFFECF93A033D799DA03A85156EF741B5BB6 (void);
extern void ObservablePointerDownTrigger_OnPointerDownAsObservable_m475A5266BE457D6A146F8FF6BE092868C8882F4F (void);
extern void ObservablePointerDownTrigger_RaiseOnCompletedOnDestroy_mD139091C4F375F8D544B83D06AE5FC318EC420D8 (void);
extern void ObservablePointerDownTrigger__ctor_m0F7FE9E6AE61D395DB36BE18B1B0295B9498046D (void);
extern void ObservablePointerEnterTrigger_UnityEngine_EventSystems_IPointerEnterHandler_OnPointerEnter_m5EB6A64B45CD7D826A9FAEAD378A6F09DADAB28F (void);
extern void ObservablePointerEnterTrigger_OnPointerEnterAsObservable_m250A102F9845DABB106D2F5C9F0FFB5732F61659 (void);
extern void ObservablePointerEnterTrigger_RaiseOnCompletedOnDestroy_mA65678B7DB353CADD934FE87492628BD8D09BAED (void);
extern void ObservablePointerEnterTrigger__ctor_m49737D88A0D282F581B4847F0A956D611900ED24 (void);
extern void ObservablePointerExitTrigger_UnityEngine_EventSystems_IPointerExitHandler_OnPointerExit_m1EDC3AE18D26AF52F98E5EADB3A26B5A92C5E95A (void);
extern void ObservablePointerExitTrigger_OnPointerExitAsObservable_m56DF5FEBD989C64FC0E61B06E39269A513E68E4F (void);
extern void ObservablePointerExitTrigger_RaiseOnCompletedOnDestroy_m8304427268B1659FACCBB09F1A07094EE76A822F (void);
extern void ObservablePointerExitTrigger__ctor_m162A1C77AF0203153AFC995B2DCC874F9D155ACA (void);
extern void ObservablePointerUpTrigger_UnityEngine_EventSystems_IPointerUpHandler_OnPointerUp_mEF07E8A024532C1DC89C10553F400AEA378258EE (void);
extern void ObservablePointerUpTrigger_OnPointerUpAsObservable_m579801849386C60DEA7B1D4DC6274EFB3D9F2F89 (void);
extern void ObservablePointerUpTrigger_RaiseOnCompletedOnDestroy_mC64ABB8DDCA876AD6C3038AC3FCAFBFA5EF6BE26 (void);
extern void ObservablePointerUpTrigger__ctor_mD52511A02B82B60174A67539A588B2C4762F993D (void);
extern void ObservableRectTransformTrigger_OnRectTransformDimensionsChange_m77598F3563D921F9C014270D96D32DB921E639B9 (void);
extern void ObservableRectTransformTrigger_OnRectTransformDimensionsChangeAsObservable_mE806A26722B47B870AD3E86454A7885597CCA0A5 (void);
extern void ObservableRectTransformTrigger_OnRectTransformRemoved_m30C3F91102974ADDA8776FF9A4C012DD438884C7 (void);
extern void ObservableRectTransformTrigger_OnRectTransformRemovedAsObservable_mD665260B8125A42FC6F904D70F7860A9FAC54E69 (void);
extern void ObservableRectTransformTrigger_RaiseOnCompletedOnDestroy_mB114241152CF4C4FDBCCD2486FAF800C8F9B393D (void);
extern void ObservableRectTransformTrigger__ctor_mFB937A40CB89048DE60904EF4BF5FEC679D4AB5E (void);
extern void ObservableScrollTrigger_UnityEngine_EventSystems_IScrollHandler_OnScroll_mE3C23C4866698BAF79A51C6F29BC0D69EFF7B3E1 (void);
extern void ObservableScrollTrigger_OnScrollAsObservable_m0D77A632F90495967DE20C2EDBB37C08D9D80E36 (void);
extern void ObservableScrollTrigger_RaiseOnCompletedOnDestroy_m119D32C73EE8595AF4E6A34ACDC492E9D5BF943E (void);
extern void ObservableScrollTrigger__ctor_m22BCBBFEE69444BC3801A28B14951ED74567E32D (void);
extern void ObservableSelectTrigger_UnityEngine_EventSystems_ISelectHandler_OnSelect_m1A304943E2AE83A118C8BA60B129B03852A1B6C9 (void);
extern void ObservableSelectTrigger_OnSelectAsObservable_m02257842E28BD07FB45A4076F50D2C7675AC1518 (void);
extern void ObservableSelectTrigger_RaiseOnCompletedOnDestroy_mF2AC65F4153FE5F4B8C02F8F25F6372BD0BA116D (void);
extern void ObservableSelectTrigger__ctor_mFFAA989C0877FB6CFD1E4157A152AB0E8B7A9D29 (void);
extern void ObservableStateMachineTrigger_OnStateExit_m14278FD35C478D629ED422D8BE87C0C9B8C8E332 (void);
extern void ObservableStateMachineTrigger_OnStateExitAsObservable_mB5ABC0C14B2EB60411C99FE63704F2C37F9B663E (void);
extern void ObservableStateMachineTrigger_OnStateEnter_m99BD7DF186CA589D2E9224EA00CA6609F6C0185B (void);
extern void ObservableStateMachineTrigger_OnStateEnterAsObservable_mDC10E6B046DA0D651B1BAA585DE3B2BDFD299C6B (void);
extern void ObservableStateMachineTrigger_OnStateIK_m4CAC571B713D8159FFE36484D821A5BC6B300026 (void);
extern void ObservableStateMachineTrigger_OnStateIKAsObservable_m5E2A0DF6A23E819AB0187279BDD2A3566814471A (void);
extern void ObservableStateMachineTrigger_OnStateUpdate_mD882C4773832531667A546F78FB00D5DECF4040E (void);
extern void ObservableStateMachineTrigger_OnStateUpdateAsObservable_mECF268F557036A538A107A98CB2586EE07C2B548 (void);
extern void ObservableStateMachineTrigger_OnStateMachineEnter_m1F051CF6B7B530FF43C27DCBB4334DB8931924B4 (void);
extern void ObservableStateMachineTrigger_OnStateMachineEnterAsObservable_m776113DF7F6628B7AF12528B8E283099FC22E4A7 (void);
extern void ObservableStateMachineTrigger_OnStateMachineExit_m5335E961CC4E118E9698232D3BB83A04162AA616 (void);
extern void ObservableStateMachineTrigger_OnStateMachineExitAsObservable_m7EE9844A6DCA1BFDAFA9F8C44DFFCE6BC2FF05E3 (void);
extern void ObservableStateMachineTrigger__ctor_m7BC8C9F4E44F54F5FA19751C7AD53112961FB795 (void);
extern void OnStateInfo_set_Animator_mD4417FBBE67033CD90868BBC08CFDF6EC9DF8BCC (void);
extern void OnStateInfo_set_StateInfo_mE5ADE0E89DD552260BD725FF9033FC1E33C3A6A8 (void);
extern void OnStateInfo_set_LayerIndex_mA5CD3B556EAF48A3898832D8B0941926D08A8787 (void);
extern void OnStateInfo__ctor_m03F8B977881B4A35F2194DF07065D0A4A212BAEF (void);
extern void OnStateMachineInfo_set_Animator_m29A1DE06600ABAE5565ED1F586957A06C39FB6B4 (void);
extern void OnStateMachineInfo_set_StateMachinePathHash_mFFCE1F323A6602A9DFC2B8EE6C679D1DA662FDCE (void);
extern void OnStateMachineInfo__ctor_mD123E454AA420C94F30A84A6947657DD2ECC2D26 (void);
extern void ObservableSubmitTrigger_UnityEngine_EventSystems_ISubmitHandler_OnSubmit_mB8F039E00B3A11C5F48056B2BC110D14BB38B9EE (void);
extern void ObservableSubmitTrigger_OnSubmitAsObservable_m4F4DA7E55C2ADB2EA03336D9120706C123F86972 (void);
extern void ObservableSubmitTrigger_RaiseOnCompletedOnDestroy_mE11416C570C78BDA6C5DA644CEAC893E534C3AA8 (void);
extern void ObservableSubmitTrigger__ctor_m5CCC2DEA12509134E3A43CB864AC94D9D828B898 (void);
extern void ObservableTransformChangedTrigger_OnBeforeTransformParentChanged_m861B3F8934492A691E234267616146EDF9810909 (void);
extern void ObservableTransformChangedTrigger_OnBeforeTransformParentChangedAsObservable_mF5B4CF2B2902C9CFF0B1E170E44260BE2D1E4538 (void);
extern void ObservableTransformChangedTrigger_OnTransformParentChanged_mC84F74142595B61401D396F9C2E2985A12DD8D29 (void);
extern void ObservableTransformChangedTrigger_OnTransformParentChangedAsObservable_m4E9E599F5D378C329A3C7CD820F120B84ECE2A9B (void);
extern void ObservableTransformChangedTrigger_OnTransformChildrenChanged_m7B82B35420DCCC0A9A3168EF7CEF7F85F99AD8D3 (void);
extern void ObservableTransformChangedTrigger_OnTransformChildrenChangedAsObservable_m6CA52BC5ED0CFB820F97021FE7434EB7771A5F9E (void);
extern void ObservableTransformChangedTrigger_RaiseOnCompletedOnDestroy_m9F29A4EC085B08AFE66C4D2DAA079ED195C968A3 (void);
extern void ObservableTransformChangedTrigger__ctor_mBBDF7D1F4776D958451800DF544ED416B9471C9F (void);
extern void ObservableTrigger2DTrigger_OnTriggerEnter2D_m8F625841F2A44C7B0FDD3AC3B2FACBBC5ED51844 (void);
extern void ObservableTrigger2DTrigger_OnTriggerEnter2DAsObservable_m3BEEA8457A380CD8EE43103E0FA2CDA6A15C4832 (void);
extern void ObservableTrigger2DTrigger_OnTriggerExit2D_m08B36847BDCD4EA1DADF1610EED683A2913F5163 (void);
extern void ObservableTrigger2DTrigger_OnTriggerExit2DAsObservable_mB6852ECA14DF41AF72D34457F926E70974E7F8AE (void);
extern void ObservableTrigger2DTrigger_OnTriggerStay2D_m1F3E8C7CD4445A114B30CB32E01A2056B5AA2D03 (void);
extern void ObservableTrigger2DTrigger_OnTriggerStay2DAsObservable_m292D4B4324CE2BBB6C807552F084D24EAA6E74DF (void);
extern void ObservableTrigger2DTrigger_RaiseOnCompletedOnDestroy_m9C1E08CA85D367D7FB1A7A894C32073A72284A75 (void);
extern void ObservableTrigger2DTrigger__ctor_mE4EE2E3A46A9F22013BEA3185E557045BB958895 (void);
extern void ObservableTriggerBase_OnDestroy_m0649C73A44A10B5FCB84B87D8B6EA11810F655B1 (void);
extern void ObservableTriggerBase__ctor_mB73E401CA91A996FDFD784CB28EC757A23D4064A (void);
extern void ObservableTriggerTrigger_OnTriggerEnter_m2B35B22DB48A84DEF4AF4D40406D8171DEF2EF3C (void);
extern void ObservableTriggerTrigger_OnTriggerEnterAsObservable_m70FAAF4E83C29BF4606BE93324061D8CC5A82E57 (void);
extern void ObservableTriggerTrigger_OnTriggerExit_m81072B2F5B6E5E7EC18E83E87A91FF5340318EC4 (void);
extern void ObservableTriggerTrigger_OnTriggerExitAsObservable_m8FF5B18EE9B4D03EF5362A2A01EE3CA75FFDED23 (void);
extern void ObservableTriggerTrigger_OnTriggerStay_mDCA2490F5CBD14F632E1A699BE231BAE37AB5FC7 (void);
extern void ObservableTriggerTrigger_OnTriggerStayAsObservable_mB7170EE58E31C535BA72717F31A1550B7BC86DA3 (void);
extern void ObservableTriggerTrigger_RaiseOnCompletedOnDestroy_mC5237747C818FE26D7589B71B3B40AC00355FFAA (void);
extern void ObservableTriggerTrigger__ctor_mD63B742D1FEF3CA40202A1CB1ABDE17EF666FBFE (void);
extern void ObservableUpdateSelectedTrigger_UnityEngine_EventSystems_IUpdateSelectedHandler_OnUpdateSelected_m8C92D78ADDF1AD667248B0A9E18A94D3B061DA27 (void);
extern void ObservableUpdateSelectedTrigger_OnUpdateSelectedAsObservable_mAA10E2C71484EED385570199C87D11DD3D98E79A (void);
extern void ObservableUpdateSelectedTrigger_RaiseOnCompletedOnDestroy_m5A32C275CD3F952B3669C0178C271EA7D8483042 (void);
extern void ObservableUpdateSelectedTrigger__ctor_mDAFF63881CF5A5F3EFFDD1E6CC8DD416823146CC (void);
extern void ObservableUpdateTrigger_Update_mC320935B49AB2A79FB3B12901123BACF21DD212F (void);
extern void ObservableUpdateTrigger_UpdateAsObservable_m402184D741E4EECCD327809B7EEF8AECDFCC054A (void);
extern void ObservableUpdateTrigger_RaiseOnCompletedOnDestroy_m657238B14782ABECA93E53D68592B50A9BFE0A73 (void);
extern void ObservableUpdateTrigger__ctor_m49ED949028F2247BC8C9DEA25614015DE866E242 (void);
extern void ObservableVisibleTrigger_OnBecameInvisible_m74214AD21B3984A6D7D3399FC4DBF1D62C21135B (void);
extern void ObservableVisibleTrigger_OnBecameInvisibleAsObservable_m7627600D16EBF9AFBBECFF4D7345B476445A4761 (void);
extern void ObservableVisibleTrigger_OnBecameVisible_m43335952E1EF00D8A50E8B0F6C5D4C94D0900853 (void);
extern void ObservableVisibleTrigger_OnBecameVisibleAsObservable_mD77992EDB76A85D5C5E66FA89F061DB263251073 (void);
extern void ObservableVisibleTrigger_RaiseOnCompletedOnDestroy_m807B3E4A75D59127DFF5586F661DF7B056EA947F (void);
extern void ObservableVisibleTrigger__ctor_mF61CC8060CB7A57890763C8C4657517BBE6BDB59 (void);
static Il2CppMethodPointer s_methodPointers[249] = 
{
	PlayerLoopHelper_Init_m5109ECF406895CF583C6EC005BE3D333445E5898,
	PlayerLoopHelper_Initialize_mEA5B98D3F5BE539CC7A3C1EF915667090A9B340D,
	PlayerLoopHelper_InsertLoop_mBBAAA9F237CB1B1D192DCE2680E3802E56B7AAA9,
	PlayerLoopHelper_FindLoopSystemIndex_mB7E350F8FF3F46B38425BD0CB9307820E0DDDDC6,
	PlayerLoopHelper_InsertRunner_m6FC0AF36206ED267C940C5815DD2B2541F1E32D2,
	U3CU3Ec__DisplayClass8_0__ctor_mF741093157A9A4A513E488A25A9F30FAFFA81C07,
	U3CU3Ec__DisplayClass8_0_U3CInsertRunnerU3Eb__0_m177B41936211949CF2305C0114B8665D9B764C67,
	UnityFrameProvider_get_PlayerLoopTiming_m43770D2CD4B4D11F0F52DF12E1CD504A8F486285,
	UnityFrameProvider__ctor_m8187C500B64AA2EDCD8BA7054460369317D4BF68,
	UnityFrameProvider_Register_m7C511B73856E8768D18BA78ABF1A5C24A8BE213C,
	UnityFrameProvider_Run_mCC738CA74994A5C44C710C8B13DABFDC6262C3E2,
	UnityFrameProvider__cctor_m1DB4D2EACC7394FCF4675CB15A260D8DDC1014B8,
	UnityProviderInitializer__cctor_mC972D9CDC2DE18E6F98B05571D31F2D249DEE8E4,
	UnityProviderInitializer_SetDefaultObservableSystem_m291BE9731C71C2C2F18E42B7D22EC8F47B50A334,
	UnityProviderInitializer_SetDefaultObservableSystem_mB0E5ACDC2AC11E6750B232BED84EC1396638FB91,
	U3CU3Ec__cctor_m79405D88D1CAA8C37648FC61EEDE4BCAE1A297B7,
	U3CU3Ec__ctor_m315DBBB82A17F910403F7D1BFCEFC9F955579103,
	U3CU3Ec_U3CSetDefaultObservableSystemU3Eb__1_0_m6B87B4D87F2004F2E456B00060CEA77BE448CEB7,
	UnityTimeProvider__ctor_m78DC462BBC40D8DF7841E3C5BC8BAD045B176CC0,
	UnityTimeProvider__cctor_mB925B90C16294A67D4BB7F86167D8F8C9D44B1FD,
	ObservableAnimatorTrigger_OnAnimatorIK_m779E3120F8F6823A1C5AE238F2BAAAE7E6FB4E84,
	ObservableAnimatorTrigger_OnAnimatorIKAsObservable_m2C1A9912045A9F7ABE926E254E0DC7462AB6808B,
	ObservableAnimatorTrigger_OnAnimatorMove_mDD41F7FE3154FDA24A8DA46803A5375C51B078C0,
	ObservableAnimatorTrigger_OnAnimatorMoveAsObservable_mDBA3D84000257A2697B6FBB4092A641D570EB459,
	ObservableAnimatorTrigger_RaiseOnCompletedOnDestroy_mC214D9AB48028E1923A5B93B6A84299761FC6298,
	ObservableAnimatorTrigger__ctor_m80DF6B8867D6F0621FE7D5878BE9F4B11FF30C0C,
	ObservableBeginDragTrigger_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mF7133B158EFB4D7672EC832A7A1908C621402D24,
	ObservableBeginDragTrigger_OnBeginDragAsObservable_m6278184765AC87178CB98896374F126069E70F83,
	ObservableBeginDragTrigger_RaiseOnCompletedOnDestroy_m8977DD954F10B02C595D00D5CDCA9BCECA31E43C,
	ObservableBeginDragTrigger__ctor_m8FF54475D58B50489FF3A7493B2F1DEA16EB9DB9,
	ObservableCancelTrigger_UnityEngine_EventSystems_ICancelHandler_OnCancel_mBF565D4F89DE39BC91CE681386AB49B3ADC9F55C,
	ObservableCancelTrigger_OnCancelAsObservable_m0D9BA25C5468AAEB0002C4D250211C258974E123,
	ObservableCancelTrigger_RaiseOnCompletedOnDestroy_mDF540155AEA6BF725FA7476B45AE38D75F565751,
	ObservableCancelTrigger__ctor_m71FEFAA9DD9F4B316BFA2B0217E11B1C3F91889D,
	ObservableCanvasGroupChangedTrigger_OnCanvasGroupChanged_m2B3321F538A2A1EC4CE47C83CDC68EB95F09296B,
	ObservableCanvasGroupChangedTrigger_OnCanvasGroupChangedAsObservable_m0273D57F8ABBB65BBAE3334657ACD5D30FB6C6E6,
	ObservableCanvasGroupChangedTrigger_RaiseOnCompletedOnDestroy_m0CA78F9E61C749065D62F45857BC4C704A3F1381,
	ObservableCanvasGroupChangedTrigger__ctor_m9EEDC2DEDBDF30D83B9539AB7739652361162A09,
	ObservableCollision2DTrigger_OnCollisionEnter2D_mE3C0541928FE630CD6983B7732665AC71FF2CE9B,
	ObservableCollision2DTrigger_OnCollisionEnter2DAsObservable_m61E43556E6511758CF9064DAADF4DCDC424DB7A7,
	ObservableCollision2DTrigger_OnCollisionExit2D_m9912EA35CC3B2918CE8D1B48BFBDC2311381CC82,
	ObservableCollision2DTrigger_OnCollisionExit2DAsObservable_m342B5CD0B4B5647DA0AEDE491DB3FB061178BC4B,
	ObservableCollision2DTrigger_OnCollisionStay2D_m3B96C55A29797610E2A047B201B1F3D034BB9065,
	ObservableCollision2DTrigger_OnCollisionStay2DAsObservable_m183BD2B6E6B6B5D040CA0EBB60D1EDFA65237363,
	ObservableCollision2DTrigger_RaiseOnCompletedOnDestroy_mBEFBD74FE191180CEADBE3C81809DDE6E3CCD475,
	ObservableCollision2DTrigger__ctor_m6DDC7E69C6CB865039F947BC2D2A9BE5C0F46816,
	ObservableCollisionTrigger_OnCollisionEnter_mA70ED05310153F5B4E61E733813A3135533F476B,
	ObservableCollisionTrigger_OnCollisionEnterAsObservable_m457C4B933C07442E6F0ECD920C1256496F6FB601,
	ObservableCollisionTrigger_OnCollisionExit_m1438F4A64B4D2E597BA27E9E6C1798CD57E1642F,
	ObservableCollisionTrigger_OnCollisionExitAsObservable_m5CCECB4AB654D3C0BCBF300CE4E3BC1370A2D388,
	ObservableCollisionTrigger_OnCollisionStay_mE9C4AFB1E38187EAD7304BD321BA8445090DF729,
	ObservableCollisionTrigger_OnCollisionStayAsObservable_mC6FA60D7D94C2B80297D9180527C08BEF9C8425D,
	ObservableCollisionTrigger_RaiseOnCompletedOnDestroy_mB37826467FBC446E61FEFD5EFD74BD736B2BE22B,
	ObservableCollisionTrigger__ctor_m4F42672C8752F4ADA78ECD3ECA5B997393F789C1,
	ObservableDeselectTrigger_UnityEngine_EventSystems_IDeselectHandler_OnDeselect_m8F24746E3181EF462542E082A1FDAC6A799DE5F2,
	ObservableDeselectTrigger_OnDeselectAsObservable_m4857A9B1AB70437517903C5BE1B67CA650A18891,
	ObservableDeselectTrigger_RaiseOnCompletedOnDestroy_m5AC8A8B8639C7AF155BFDD4EB8EA11B53A6183C5,
	ObservableDeselectTrigger__ctor_mA3D96E2084400CCEA6D8D5B268887ECAECDE5761,
	ObservableDestroyTrigger_get_IsActivated_m508EA14083F0456B6CE97A500F696CCC615467D2,
	ObservableDestroyTrigger_set_IsActivated_mA720868A4D4C6B55DFB7147670899218FEF86E0F,
	ObservableDestroyTrigger_GetCancellationToken_mAE8ED38E9E50A08B033780D1F9A213519F602193,
	ObservableDestroyTrigger_AddDisposableOnDestroy_m80D68C19F9162AEA2C6C4B733924A0ABE285BB50,
	ObservableDestroyTrigger_Awake_m36ADA578F16BF7982649C8FF1D0D8479F690063B,
	ObservableDestroyTrigger_OnDestroy_m9F5E030A0D51A2E145110F10FB7E5CE599F2D936,
	ObservableDestroyTrigger_OnDestroyAsObservable_m24D8B4395144031B9A4041C3BC764FB7DD45D1F9,
	ObservableDestroyTrigger_TryStartActivateMonitoring_m9836D820DDE00C1E875DCA7A8B1D4F3C900A3C84,
	ObservableDestroyTrigger_R3_IFrameRunnerWorkItem_MoveNext_m9B4DA1D4574CAFBD505EAC57681BEE83729F0A37,
	ObservableDestroyTrigger__ctor_mBA657627AC96E2EF57C436D6CAB266134D3D2D50,
	ObservableDragTrigger_UnityEngine_EventSystems_IDragHandler_OnDrag_m91D72C0DA8146F826CE01051C37E11BE3ADFDDD9,
	ObservableDragTrigger_OnDragAsObservable_mDDE78B5E04B1F27522B61BD97AF6C3995223BDED,
	ObservableDragTrigger_RaiseOnCompletedOnDestroy_m7BEC66EDB4F78B0772EA5528F768381DD720521C,
	ObservableDragTrigger__ctor_mC613FC3CDE928D94497B8CC51CA76FDB4314D855,
	ObservableDropTrigger_UnityEngine_EventSystems_IDropHandler_OnDrop_m42C04BF77CE647B32D6866DF394A1296D57D1007,
	ObservableDropTrigger_OnDropAsObservable_m15CAB46C03406C83D8504A0C70A0492A63F32A68,
	ObservableDropTrigger_RaiseOnCompletedOnDestroy_m9AF9B689108BDBE071FCE0E0B928A587BDBE7F9B,
	ObservableDropTrigger__ctor_mD650D3D9F88B5ED179057B70BC71E82BCCF41DFF,
	ObservableEnableTrigger_OnEnable_m0518E18A5C898169840F71AFE450CEE282C03767,
	ObservableEnableTrigger_OnEnableAsObservable_m0CD325078B0E44C002835EC9C6E7D1543E65A9AF,
	ObservableEnableTrigger_OnDisable_m658301A42EC79471C6CACD636DEE436A4B75E26A,
	ObservableEnableTrigger_OnDisableAsObservable_mF1FCC0D5AB988770F3AA572D38E502F87E05BA89,
	ObservableEnableTrigger_RaiseOnCompletedOnDestroy_m184B07A0567BDAD8D6C91780DE5AABD3EEC8199D,
	ObservableEnableTrigger__ctor_m4FFE7423393E0F06CC79489D598ADD3D55F409E5,
	ObservableEndDragTrigger_UnityEngine_EventSystems_IEndDragHandler_OnEndDrag_m7BA833F7E733AA94400EFF8B9D659B7B367939FE,
	ObservableEndDragTrigger_OnEndDragAsObservable_m0C9D9EFF834AA4DACF81668D541904AB19DD58AF,
	ObservableEndDragTrigger_RaiseOnCompletedOnDestroy_m0CD33AD6CEA8901945672A3F6FFCACD55943273F,
	ObservableEndDragTrigger__ctor_mA08DEC33FA2F4497490038ACAA3A5493D57E655E,
	ObservableEventTrigger_UnityEngine_EventSystems_IDeselectHandler_OnDeselect_mC3897AC7EC26869217D75C91F86188C6677E2175,
	ObservableEventTrigger_OnDeselectAsObservable_mB2FB48FEF6B41A722697B991833DCEEFE875C21F,
	ObservableEventTrigger_UnityEngine_EventSystems_IMoveHandler_OnMove_m20630B8FD1C7868121FB8664EA414107E24FE893,
	ObservableEventTrigger_OnMoveAsObservable_m2749E28D012B6DBE16349BF4917E49599DEE0C90,
	ObservableEventTrigger_UnityEngine_EventSystems_IPointerDownHandler_OnPointerDown_mA107C5BBD8E2C40301C6F11B30B47ECF1FE215C9,
	ObservableEventTrigger_OnPointerDownAsObservable_mE4BCDF37A69A5CF2A65C5E523D9E9D2DFAEDF6DB,
	ObservableEventTrigger_UnityEngine_EventSystems_IPointerEnterHandler_OnPointerEnter_mDEEE143F4AF9981FACB9A56C4EE6964A6D5EF5AD,
	ObservableEventTrigger_OnPointerEnterAsObservable_mB9BD82DDCE61BFF8DEB997DDF2C7BDF294E8E854,
	ObservableEventTrigger_UnityEngine_EventSystems_IPointerExitHandler_OnPointerExit_m671D3B8C30B3EB7ED6A1D642CC5ECBCC53E65920,
	ObservableEventTrigger_OnPointerExitAsObservable_mA06208BAECA6C407E0D6DFF1FA191530DD2A483E,
	ObservableEventTrigger_UnityEngine_EventSystems_IPointerUpHandler_OnPointerUp_m08C0667026EFF2CAD1BC65DAAB92BB072CECDB07,
	ObservableEventTrigger_OnPointerUpAsObservable_m9937ED0F500DDF2042101916B6235BADB2377E66,
	ObservableEventTrigger_UnityEngine_EventSystems_ISelectHandler_OnSelect_mF92C711E2CFEAF84B99E9E00EF201F25B09B505C,
	ObservableEventTrigger_OnSelectAsObservable_mBA81538A7773D4AC964534576C7EA6BD9A1F039D,
	ObservableEventTrigger_UnityEngine_EventSystems_IPointerClickHandler_OnPointerClick_m1F80C43AE462EEA73A4E1FB22D7DCDFF39553C46,
	ObservableEventTrigger_OnPointerClickAsObservable_mD4DE565E62DC6AFA46CA1931D4A91DEB120A5044,
	ObservableEventTrigger_UnityEngine_EventSystems_ISubmitHandler_OnSubmit_mFBF14BE27D464C7D86281E63A6D5EA602C49A25E,
	ObservableEventTrigger_OnSubmitAsObservable_m50860550C4F33E54C398230E9554EF66F04FA585,
	ObservableEventTrigger_UnityEngine_EventSystems_IDragHandler_OnDrag_m8AAD28D7B065AEF3E3D72CB7BCD033C94B581016,
	ObservableEventTrigger_OnDragAsObservable_mC17C91FB20928CF8F35477546AFDA5BE0AE59812,
	ObservableEventTrigger_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mC62266EFA932C02EB4E0DF70D6F9B7FE20D76635,
	ObservableEventTrigger_OnBeginDragAsObservable_mA052337F28D5D9DA8F576EB51B07C1DAAE9EA2AF,
	ObservableEventTrigger_UnityEngine_EventSystems_IEndDragHandler_OnEndDrag_m97F68F664780CF08B927B6EE5CD506F99BCA4D2B,
	ObservableEventTrigger_OnEndDragAsObservable_mB29A89A9E6F3F528A74ABE162C3900F85412771E,
	ObservableEventTrigger_UnityEngine_EventSystems_IDropHandler_OnDrop_m81F5064518A6AC3488AB87AE548E9A104654A766,
	ObservableEventTrigger_OnDropAsObservable_mDFFC6DB38788C2CC331342B0D8F6BA17A44E0707,
	ObservableEventTrigger_UnityEngine_EventSystems_IUpdateSelectedHandler_OnUpdateSelected_m39870ED733B3239FE2D1DC3F9421985432B01C71,
	ObservableEventTrigger_OnUpdateSelectedAsObservable_mD39CE251C7757FFF5CC8FEF651F4BCCA3E82106D,
	ObservableEventTrigger_UnityEngine_EventSystems_IInitializePotentialDragHandler_OnInitializePotentialDrag_mAC7C78ACD94FE0CFB35F277FBFADC8E9963BFFFD,
	ObservableEventTrigger_OnInitializePotentialDragAsObservable_mDEC5CC106AB0B3063C62863914C58F2D132AE764,
	ObservableEventTrigger_UnityEngine_EventSystems_ICancelHandler_OnCancel_m4078ED6DC82BD1F334B014C0B876CB9C663BCA4A,
	ObservableEventTrigger_OnCancelAsObservable_m7DEFED425D1E8B381EC769F3079DB3E341E76842,
	ObservableEventTrigger_UnityEngine_EventSystems_IScrollHandler_OnScroll_mC7E0207492E793EE569DEBD878DD3DAED0606320,
	ObservableEventTrigger_OnScrollAsObservable_mAEFDE56FC1E4F63F162230F60F408363DB14F532,
	ObservableEventTrigger_RaiseOnCompletedOnDestroy_mDC8A3392EB47EF028998AD411194715801636BAE,
	ObservableEventTrigger__ctor_mEC338589626B1EB1ABE91E5AA15BD071C7B6977E,
	ObservableFixedUpdateTrigger_FixedUpdate_mB079DF25267E62C1FAD1BD062ACE5B90FE5734D1,
	ObservableFixedUpdateTrigger_FixedUpdateAsObservable_m159C6851FB5B2587A8C26F2483F467533513B409,
	ObservableFixedUpdateTrigger_RaiseOnCompletedOnDestroy_m62C0AEA4927DF59F1D9E84634A3E8C3AFCAEE753,
	ObservableFixedUpdateTrigger__ctor_m9D2358CDDA6C01146E889D9892E717433854600E,
	ObservableInitializePotentialDragTrigger_UnityEngine_EventSystems_IInitializePotentialDragHandler_OnInitializePotentialDrag_m2C70CAA61D5D360B2E8C9894AB2618B2415F0AB3,
	ObservableInitializePotentialDragTrigger_OnInitializePotentialDragAsObservable_m76886277DD3720295B7977A71157F0210C410BA4,
	ObservableInitializePotentialDragTrigger_RaiseOnCompletedOnDestroy_m9162DBDB846B177499556EA54E17274AE7B1FB2A,
	ObservableInitializePotentialDragTrigger__ctor_m0DCB412429D97FCBC4DB16FE007DC9635B47271F,
	ObservableJointTrigger_OnJointBreak_m2F18A63D0BAF137BE59E75974B111DC85E73177F,
	ObservableJointTrigger_OnJointBreakAsObservable_m3240362EDEFFE4A031262E629508292AC42F76DE,
	ObservableJointTrigger_OnJointBreak2D_m060300D9A1512DC5E44EA298A3A1DC147230A787,
	ObservableJointTrigger_OnJointBreak2DAsObservable_mCD4EE5B5B472703973969BE6B8828F99AEEEA20D,
	ObservableJointTrigger_RaiseOnCompletedOnDestroy_m6A554C6E55DCD4D08E60528C7C5DC74EB74FECAC,
	ObservableJointTrigger__ctor_mB5FA9BBCC98FE99BEB380F4D47E200AD2FD05FD8,
	ObservableLateUpdateTrigger_LateUpdate_m8FAA1B297A01454498DC0AAD7F231FD12F49A70E,
	ObservableLateUpdateTrigger_LateUpdateAsObservable_mFF813B0747A34F26542AE4D0872187574AB44AAE,
	ObservableLateUpdateTrigger_RaiseOnCompletedOnDestroy_mDA4C9E35A65591ED29C9D4E9C00E24B7389C1C8D,
	ObservableLateUpdateTrigger__ctor_mB9997C95E893DF965FC3C89CB016F9E4A7FC8E6D,
	ObservableMoveTrigger_UnityEngine_EventSystems_IMoveHandler_OnMove_m3F38D22461839B30248833BC1524C80F229075A9,
	ObservableMoveTrigger_OnMoveAsObservable_m67E0A0985C7C13B0870980DC780C21B357ABC349,
	ObservableMoveTrigger_RaiseOnCompletedOnDestroy_m8FBB3E2861086DF5304D2E53123A66A9595B407E,
	ObservableMoveTrigger__ctor_m86AA2294E0CAEDDB2EF59A6CD3C13DEF0B0279CC,
	ObservableParticleTrigger_OnParticleCollision_m0CFEB6694AF45DD45B13E2E353D22017CBB60CAA,
	ObservableParticleTrigger_OnParticleCollisionAsObservable_m48D2B1EA242614F56AF5FC2AA09C025691865CA3,
	ObservableParticleTrigger_OnParticleTrigger_mF6BB8656F2D3F2DF76112F26AE08B603862071F5,
	ObservableParticleTrigger_OnParticleTriggerAsObservable_m498ECB42A0D0588FBDEFB311B7BA6F74F36FCCCC,
	ObservableParticleTrigger_RaiseOnCompletedOnDestroy_m13DC15E1BE2DE1F7BAFBC73E540636746ABF0573,
	ObservableParticleTrigger__ctor_mF2D090B632BD298BA5E09F53F66F272ADABF2634,
	ObservablePointerClickTrigger_UnityEngine_EventSystems_IPointerClickHandler_OnPointerClick_mD652241F57AD835188DCF323CE9CA19041323E4D,
	ObservablePointerClickTrigger_OnPointerClickAsObservable_m771096298A65888256F5AA0DD5A95C0A95226F1F,
	ObservablePointerClickTrigger_RaiseOnCompletedOnDestroy_m3EFBAAE97873C6662C4E82E9BF937D20E3E7A0D9,
	ObservablePointerClickTrigger__ctor_mDBB57F594D1AEE003152DB98D9F0BC0D0CF2CF81,
	ObservablePointerDownTrigger_UnityEngine_EventSystems_IPointerDownHandler_OnPointerDown_m1205BFFECF93A033D799DA03A85156EF741B5BB6,
	ObservablePointerDownTrigger_OnPointerDownAsObservable_m475A5266BE457D6A146F8FF6BE092868C8882F4F,
	ObservablePointerDownTrigger_RaiseOnCompletedOnDestroy_mD139091C4F375F8D544B83D06AE5FC318EC420D8,
	ObservablePointerDownTrigger__ctor_m0F7FE9E6AE61D395DB36BE18B1B0295B9498046D,
	ObservablePointerEnterTrigger_UnityEngine_EventSystems_IPointerEnterHandler_OnPointerEnter_m5EB6A64B45CD7D826A9FAEAD378A6F09DADAB28F,
	ObservablePointerEnterTrigger_OnPointerEnterAsObservable_m250A102F9845DABB106D2F5C9F0FFB5732F61659,
	ObservablePointerEnterTrigger_RaiseOnCompletedOnDestroy_mA65678B7DB353CADD934FE87492628BD8D09BAED,
	ObservablePointerEnterTrigger__ctor_m49737D88A0D282F581B4847F0A956D611900ED24,
	ObservablePointerExitTrigger_UnityEngine_EventSystems_IPointerExitHandler_OnPointerExit_m1EDC3AE18D26AF52F98E5EADB3A26B5A92C5E95A,
	ObservablePointerExitTrigger_OnPointerExitAsObservable_m56DF5FEBD989C64FC0E61B06E39269A513E68E4F,
	ObservablePointerExitTrigger_RaiseOnCompletedOnDestroy_m8304427268B1659FACCBB09F1A07094EE76A822F,
	ObservablePointerExitTrigger__ctor_m162A1C77AF0203153AFC995B2DCC874F9D155ACA,
	ObservablePointerUpTrigger_UnityEngine_EventSystems_IPointerUpHandler_OnPointerUp_mEF07E8A024532C1DC89C10553F400AEA378258EE,
	ObservablePointerUpTrigger_OnPointerUpAsObservable_m579801849386C60DEA7B1D4DC6274EFB3D9F2F89,
	ObservablePointerUpTrigger_RaiseOnCompletedOnDestroy_mC64ABB8DDCA876AD6C3038AC3FCAFBFA5EF6BE26,
	ObservablePointerUpTrigger__ctor_mD52511A02B82B60174A67539A588B2C4762F993D,
	ObservableRectTransformTrigger_OnRectTransformDimensionsChange_m77598F3563D921F9C014270D96D32DB921E639B9,
	ObservableRectTransformTrigger_OnRectTransformDimensionsChangeAsObservable_mE806A26722B47B870AD3E86454A7885597CCA0A5,
	ObservableRectTransformTrigger_OnRectTransformRemoved_m30C3F91102974ADDA8776FF9A4C012DD438884C7,
	ObservableRectTransformTrigger_OnRectTransformRemovedAsObservable_mD665260B8125A42FC6F904D70F7860A9FAC54E69,
	ObservableRectTransformTrigger_RaiseOnCompletedOnDestroy_mB114241152CF4C4FDBCCD2486FAF800C8F9B393D,
	ObservableRectTransformTrigger__ctor_mFB937A40CB89048DE60904EF4BF5FEC679D4AB5E,
	ObservableScrollTrigger_UnityEngine_EventSystems_IScrollHandler_OnScroll_mE3C23C4866698BAF79A51C6F29BC0D69EFF7B3E1,
	ObservableScrollTrigger_OnScrollAsObservable_m0D77A632F90495967DE20C2EDBB37C08D9D80E36,
	ObservableScrollTrigger_RaiseOnCompletedOnDestroy_m119D32C73EE8595AF4E6A34ACDC492E9D5BF943E,
	ObservableScrollTrigger__ctor_m22BCBBFEE69444BC3801A28B14951ED74567E32D,
	ObservableSelectTrigger_UnityEngine_EventSystems_ISelectHandler_OnSelect_m1A304943E2AE83A118C8BA60B129B03852A1B6C9,
	ObservableSelectTrigger_OnSelectAsObservable_m02257842E28BD07FB45A4076F50D2C7675AC1518,
	ObservableSelectTrigger_RaiseOnCompletedOnDestroy_mF2AC65F4153FE5F4B8C02F8F25F6372BD0BA116D,
	ObservableSelectTrigger__ctor_mFFAA989C0877FB6CFD1E4157A152AB0E8B7A9D29,
	ObservableStateMachineTrigger_OnStateExit_m14278FD35C478D629ED422D8BE87C0C9B8C8E332,
	ObservableStateMachineTrigger_OnStateExitAsObservable_mB5ABC0C14B2EB60411C99FE63704F2C37F9B663E,
	ObservableStateMachineTrigger_OnStateEnter_m99BD7DF186CA589D2E9224EA00CA6609F6C0185B,
	ObservableStateMachineTrigger_OnStateEnterAsObservable_mDC10E6B046DA0D651B1BAA585DE3B2BDFD299C6B,
	ObservableStateMachineTrigger_OnStateIK_m4CAC571B713D8159FFE36484D821A5BC6B300026,
	ObservableStateMachineTrigger_OnStateIKAsObservable_m5E2A0DF6A23E819AB0187279BDD2A3566814471A,
	ObservableStateMachineTrigger_OnStateUpdate_mD882C4773832531667A546F78FB00D5DECF4040E,
	ObservableStateMachineTrigger_OnStateUpdateAsObservable_mECF268F557036A538A107A98CB2586EE07C2B548,
	ObservableStateMachineTrigger_OnStateMachineEnter_m1F051CF6B7B530FF43C27DCBB4334DB8931924B4,
	ObservableStateMachineTrigger_OnStateMachineEnterAsObservable_m776113DF7F6628B7AF12528B8E283099FC22E4A7,
	ObservableStateMachineTrigger_OnStateMachineExit_m5335E961CC4E118E9698232D3BB83A04162AA616,
	ObservableStateMachineTrigger_OnStateMachineExitAsObservable_m7EE9844A6DCA1BFDAFA9F8C44DFFCE6BC2FF05E3,
	ObservableStateMachineTrigger__ctor_m7BC8C9F4E44F54F5FA19751C7AD53112961FB795,
	OnStateInfo_set_Animator_mD4417FBBE67033CD90868BBC08CFDF6EC9DF8BCC,
	OnStateInfo_set_StateInfo_mE5ADE0E89DD552260BD725FF9033FC1E33C3A6A8,
	OnStateInfo_set_LayerIndex_mA5CD3B556EAF48A3898832D8B0941926D08A8787,
	OnStateInfo__ctor_m03F8B977881B4A35F2194DF07065D0A4A212BAEF,
	OnStateMachineInfo_set_Animator_m29A1DE06600ABAE5565ED1F586957A06C39FB6B4,
	OnStateMachineInfo_set_StateMachinePathHash_mFFCE1F323A6602A9DFC2B8EE6C679D1DA662FDCE,
	OnStateMachineInfo__ctor_mD123E454AA420C94F30A84A6947657DD2ECC2D26,
	ObservableSubmitTrigger_UnityEngine_EventSystems_ISubmitHandler_OnSubmit_mB8F039E00B3A11C5F48056B2BC110D14BB38B9EE,
	ObservableSubmitTrigger_OnSubmitAsObservable_m4F4DA7E55C2ADB2EA03336D9120706C123F86972,
	ObservableSubmitTrigger_RaiseOnCompletedOnDestroy_mE11416C570C78BDA6C5DA644CEAC893E534C3AA8,
	ObservableSubmitTrigger__ctor_m5CCC2DEA12509134E3A43CB864AC94D9D828B898,
	ObservableTransformChangedTrigger_OnBeforeTransformParentChanged_m861B3F8934492A691E234267616146EDF9810909,
	ObservableTransformChangedTrigger_OnBeforeTransformParentChangedAsObservable_mF5B4CF2B2902C9CFF0B1E170E44260BE2D1E4538,
	ObservableTransformChangedTrigger_OnTransformParentChanged_mC84F74142595B61401D396F9C2E2985A12DD8D29,
	ObservableTransformChangedTrigger_OnTransformParentChangedAsObservable_m4E9E599F5D378C329A3C7CD820F120B84ECE2A9B,
	ObservableTransformChangedTrigger_OnTransformChildrenChanged_m7B82B35420DCCC0A9A3168EF7CEF7F85F99AD8D3,
	ObservableTransformChangedTrigger_OnTransformChildrenChangedAsObservable_m6CA52BC5ED0CFB820F97021FE7434EB7771A5F9E,
	ObservableTransformChangedTrigger_RaiseOnCompletedOnDestroy_m9F29A4EC085B08AFE66C4D2DAA079ED195C968A3,
	ObservableTransformChangedTrigger__ctor_mBBDF7D1F4776D958451800DF544ED416B9471C9F,
	ObservableTrigger2DTrigger_OnTriggerEnter2D_m8F625841F2A44C7B0FDD3AC3B2FACBBC5ED51844,
	ObservableTrigger2DTrigger_OnTriggerEnter2DAsObservable_m3BEEA8457A380CD8EE43103E0FA2CDA6A15C4832,
	ObservableTrigger2DTrigger_OnTriggerExit2D_m08B36847BDCD4EA1DADF1610EED683A2913F5163,
	ObservableTrigger2DTrigger_OnTriggerExit2DAsObservable_mB6852ECA14DF41AF72D34457F926E70974E7F8AE,
	ObservableTrigger2DTrigger_OnTriggerStay2D_m1F3E8C7CD4445A114B30CB32E01A2056B5AA2D03,
	ObservableTrigger2DTrigger_OnTriggerStay2DAsObservable_m292D4B4324CE2BBB6C807552F084D24EAA6E74DF,
	ObservableTrigger2DTrigger_RaiseOnCompletedOnDestroy_m9C1E08CA85D367D7FB1A7A894C32073A72284A75,
	ObservableTrigger2DTrigger__ctor_mE4EE2E3A46A9F22013BEA3185E557045BB958895,
	ObservableTriggerBase_OnDestroy_m0649C73A44A10B5FCB84B87D8B6EA11810F655B1,
	NULL,
	ObservableTriggerBase__ctor_mB73E401CA91A996FDFD784CB28EC757A23D4064A,
	ObservableTriggerTrigger_OnTriggerEnter_m2B35B22DB48A84DEF4AF4D40406D8171DEF2EF3C,
	ObservableTriggerTrigger_OnTriggerEnterAsObservable_m70FAAF4E83C29BF4606BE93324061D8CC5A82E57,
	ObservableTriggerTrigger_OnTriggerExit_m81072B2F5B6E5E7EC18E83E87A91FF5340318EC4,
	ObservableTriggerTrigger_OnTriggerExitAsObservable_m8FF5B18EE9B4D03EF5362A2A01EE3CA75FFDED23,
	ObservableTriggerTrigger_OnTriggerStay_mDCA2490F5CBD14F632E1A699BE231BAE37AB5FC7,
	ObservableTriggerTrigger_OnTriggerStayAsObservable_mB7170EE58E31C535BA72717F31A1550B7BC86DA3,
	ObservableTriggerTrigger_RaiseOnCompletedOnDestroy_mC5237747C818FE26D7589B71B3B40AC00355FFAA,
	ObservableTriggerTrigger__ctor_mD63B742D1FEF3CA40202A1CB1ABDE17EF666FBFE,
	ObservableUpdateSelectedTrigger_UnityEngine_EventSystems_IUpdateSelectedHandler_OnUpdateSelected_m8C92D78ADDF1AD667248B0A9E18A94D3B061DA27,
	ObservableUpdateSelectedTrigger_OnUpdateSelectedAsObservable_mAA10E2C71484EED385570199C87D11DD3D98E79A,
	ObservableUpdateSelectedTrigger_RaiseOnCompletedOnDestroy_m5A32C275CD3F952B3669C0178C271EA7D8483042,
	ObservableUpdateSelectedTrigger__ctor_mDAFF63881CF5A5F3EFFDD1E6CC8DD416823146CC,
	ObservableUpdateTrigger_Update_mC320935B49AB2A79FB3B12901123BACF21DD212F,
	ObservableUpdateTrigger_UpdateAsObservable_m402184D741E4EECCD327809B7EEF8AECDFCC054A,
	ObservableUpdateTrigger_RaiseOnCompletedOnDestroy_m657238B14782ABECA93E53D68592B50A9BFE0A73,
	ObservableUpdateTrigger__ctor_m49ED949028F2247BC8C9DEA25614015DE866E242,
	ObservableVisibleTrigger_OnBecameInvisible_m74214AD21B3984A6D7D3399FC4DBF1D62C21135B,
	ObservableVisibleTrigger_OnBecameInvisibleAsObservable_m7627600D16EBF9AFBBECFF4D7345B476445A4761,
	ObservableVisibleTrigger_OnBecameVisible_m43335952E1EF00D8A50E8B0F6C5D4C94D0900853,
	ObservableVisibleTrigger_OnBecameVisibleAsObservable_mD77992EDB76A85D5C5E66FA89F061DB263251073,
	ObservableVisibleTrigger_RaiseOnCompletedOnDestroy_m807B3E4A75D59127DFF5586F661DF7B056EA947F,
	ObservableVisibleTrigger__ctor_mF61CC8060CB7A57890763C8C4657517BBE6BDB59,
};
static const int32_t s_InvokerIndices[249] = 
{
	9089,
	8867,
	6187,
	7489,
	6693,
	4364,
	3196,
	4216,
	3852,
	3881,
	4364,
	9089,
	9089,
	9089,
	8887,
	9089,
	4364,
	3881,
	2796,
	9089,
	3852,
	4250,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	4168,
	3807,
	4170,
	3881,
	4364,
	4364,
	4250,
	4364,
	3167,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3928,
	4250,
	3881,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	2025,
	4250,
	2025,
	4250,
	2025,
	4250,
	2025,
	4250,
	2796,
	4250,
	2796,
	4250,
	4364,
	3881,
	3792,
	3852,
	2025,
	3881,
	3852,
	2796,
	3881,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4250,
	4364,
	4250,
	4364,
	4364,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	4364,
	4364,
	4364,
	0,
	4364,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	4364,
	4364,
	3881,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4364,
	4364,
	4250,
	4364,
	4250,
	4364,
	4364,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationR3_Unity;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_R3_Unity_CodeGenModule;
const Il2CppCodeGenModule g_R3_Unity_CodeGenModule = 
{
	"R3.Unity.dll",
	249,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationR3_Unity,
	NULL,
	NULL,
	NULL,
	NULL,
};
