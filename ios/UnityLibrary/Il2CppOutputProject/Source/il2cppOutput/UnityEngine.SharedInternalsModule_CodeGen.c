﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AssetFileNameExtensionAttribute_get_preferredExtension_m1213FFDC570CB26BCCFD1207F3FA867ACC32470B (void);
extern void AssetFileNameExtensionAttribute_get_otherExtensions_mCEF5305E650AD2881C87BE512E69ADD9CBD28528 (void);
extern void AssetFileNameExtensionAttribute__ctor_mBDD7C2006F136025094AABE2295D190F43F7F224 (void);
extern void ThreadAndSerializationSafeAttribute__ctor_m5023D29907E8D1092714DFFA137A8364454709C7 (void);
extern void IL2CPPStructAlignmentAttribute__ctor_m399EB2A2E74762BE0F5893EE0D9760AB9CD7FCD6 (void);
extern void WritableAttribute__ctor_mC4E14F120A46CC52A65942F34224E2FB20CFF55A (void);
extern void RejectDragAndDropMaterial__ctor_m506305EEEFB3F69DE6F77279272177DC03132CE6 (void);
extern void UnityEngineModuleAssembly__ctor_m3F8B023CF6E911C448EF9284C72F7BE92C6E72E3 (void);
extern void NativeClassAttribute_get_QualifiedNativeName_mFB917E369CB8CD7F74AF1EC09F9FEF4530A19C1B (void);
extern void NativeClassAttribute_set_QualifiedNativeName_m5DA8C7AE66E1D4F1ACEA7592ABBF331A2650E0D6 (void);
extern void NativeClassAttribute_get_Declaration_m2D47AB76F6FE6AD2EC2795E25BF836BC4BB1737E (void);
extern void NativeClassAttribute_set_Declaration_mE5497339ED9E55F30DABACBE40AD6D9D36CAE653 (void);
extern void NativeClassAttribute__ctor_mA4C67EDCE7DA70AAADE77FE63EEECFFA911AD0C7 (void);
extern void NativeClassAttribute__ctor_m92A42152D6ACC3FB9C381EFDFAA691251E2C3DD7 (void);
extern void UnityString_Format_m98A0629641086A1BE20BBF7F4EADDE3FE3877D85 (void);
extern void UnityString__ctor_m7E6FD7243B9B82BC86FDC810C838883D24305F6E (void);
extern void VisibleToOtherModulesAttribute__ctor_m2F00FAC0C9348472A15E93AD256145DCDD967E59 (void);
extern void VisibleToOtherModulesAttribute__ctor_m2FC15A41D7218FFD29ECA4F70323F6DF8F19EC35 (void);
extern void NativeConditionalAttribute_get_Condition_m6C8E96BC1DFE39467E3C538B2E19B50E9820B8F2 (void);
extern void NativeConditionalAttribute_set_Condition_m96107E75DC095D9B4A9A7CCE0EB0C3EFAA2F0053 (void);
extern void NativeConditionalAttribute_get_StubReturnStatement_m929838EEE9AFF2CE1B9A35082E50B5961B1BA277 (void);
extern void NativeConditionalAttribute_set_StubReturnStatement_mAA961429C06F0E4DC9149C26CC092ED29AF5331E (void);
extern void NativeConditionalAttribute_get_Enabled_m525FF5DAC1A5FE829D3F499582B451A6A1EBE5E3 (void);
extern void NativeConditionalAttribute_set_Enabled_m379DA383CBBF2539C080D5DC0E8B97F4DB27DA64 (void);
extern void NativeConditionalAttribute__ctor_mB2F5024D41C7565723B445E8D9EB8C0CF1ABC3B2 (void);
extern void NativeConditionalAttribute__ctor_mD9C6F4E343C06314DF5DCFDDE9B5495E78F711ED (void);
extern void NativeConditionalAttribute__ctor_m209F75992A80F535ABE522BEFE8D6AB331FE3327 (void);
extern void NativeConditionalAttribute__ctor_m24A34212D160364EAEF9E82A38363B1228952457 (void);
extern void NativeConditionalAttribute__ctor_mC434BE2048774B15924D749B03CE8511F2F437E8 (void);
extern void NativeConditionalAttribute__ctor_m5EC9368A4D06044907451D8C77B356E9ADBD241E (void);
extern void NativeHeaderAttribute_get_Header_m4F687D5727D0FDDA9572B6F22AF77C0CCE75B747 (void);
extern void NativeHeaderAttribute_set_Header_mC431D0143381F2B35B08E211C2D5DD011372ADAB (void);
extern void NativeHeaderAttribute__ctor_mA7E08C5B4BC93515D8A57C3E63EA030EC64EC127 (void);
extern void NativeHeaderAttribute__ctor_mD0D73B93BC695BC42CBF7E7FC6FB044131C3D0BC (void);
extern void NativeNameAttribute_get_Name_m1D7FF43F8CFC1031DA30F66933769CBBAC96E734 (void);
extern void NativeNameAttribute_set_Name_mA5639D9FDBEADE899CBE13AFA4FCFB61A95ADAE7 (void);
extern void NativeNameAttribute__ctor_m9F7E49F558B9861B14016F2776234789AE701E58 (void);
extern void NativeNameAttribute__ctor_m9F46C053270D9DBCC9F9AB32C545A7696F0638D0 (void);
extern void NativeWritableSelfAttribute_get_WritableSelf_mC7752EC7F5FEB887D5BAA3809A1B0BBE9921265C (void);
extern void NativeWritableSelfAttribute_set_WritableSelf_mB4B342C1D1678307EB4CF174BEAF8D1E94CDF3E6 (void);
extern void NativeWritableSelfAttribute__ctor_mF59616C59BA935E75ED688DCBAF1966036CD039B (void);
extern void NativeWritableSelfAttribute__ctor_m786B528FD037DD07E189BC99647D032F6EC66C2E (void);
extern void NativeMethodAttribute_get_Name_m36F73081791E7346C730B079FFAC3555B90FF0C3 (void);
extern void NativeMethodAttribute_set_Name_mE223393EB6EEA9E94A8A9CC093CB3CBBCB7C40B8 (void);
extern void NativeMethodAttribute_get_IsThreadSafe_m60B90CC0E14F197876BD52EC8FE461D8FCE8A2CD (void);
extern void NativeMethodAttribute_set_IsThreadSafe_m443623C95A2E3552D0A791DC65E20ADFC447AE3F (void);
extern void NativeMethodAttribute_get_IsFreeFunction_m5EE28EAAB63D3AE1F3184E130AD9E2DA990A93DA (void);
extern void NativeMethodAttribute_set_IsFreeFunction_mCF665BA0A4CA25DA0EA8C3C5EDDB9A03315C9C4F (void);
extern void NativeMethodAttribute_get_ThrowsException_m36DB8CC6DE1961D21A771B143B116337C994BFD7 (void);
extern void NativeMethodAttribute_set_ThrowsException_m05A53893F9C6616B40F8F70790C6533C30C64592 (void);
extern void NativeMethodAttribute_get_HasExplicitThis_mCF8F40082AB2E207853C2A774117E2CEC435E1D3 (void);
extern void NativeMethodAttribute_set_HasExplicitThis_m41908D1B191AEADF84C548E57A72B4E948D35678 (void);
extern void NativeMethodAttribute_get_WritableSelf_mA8F0DBEA6AAC37FBE33E516481CED405D31251F8 (void);
extern void NativeMethodAttribute_set_WritableSelf_mD8324F5BAE31FC4D1AF6FF27F2D2CB86481E1167 (void);
extern void NativeMethodAttribute__ctor_mEA2A3B247A134B4453743CCF55655D16C63C741E (void);
extern void NativeMethodAttribute__ctor_m75590D9A8E1851C1DA619C07522D5D4AA63797B5 (void);
extern void NativeMethodAttribute__ctor_mCDF45F4290C6955E1E9FE460709D9ACA18B2D06E (void);
extern void NativeMethodAttribute__ctor_m0B32894B3BD625410703207C7DBF61098BAEE973 (void);
extern void NativeMethodAttribute__ctor_m87610ACB7728ED7F69A7592D15A449BEB2570DB4 (void);
extern void NativePropertyAttribute_get_TargetType_m458E09B603653072CF0C983EC6F0555C6CB9291E (void);
extern void NativePropertyAttribute_set_TargetType_m858E4E653A2F81F5313EF4F5D69740D945B19ED7 (void);
extern void NativePropertyAttribute__ctor_mAF06F05D29FE386561167B2BDF0149E3744411EA (void);
extern void NativePropertyAttribute__ctor_m32EC1C27977EA39BC2824743EC417561AC138BA3 (void);
extern void NativePropertyAttribute__ctor_mB0C8185A91E0D3822DDE21A8319A1601F4099115 (void);
extern void NativePropertyAttribute__ctor_m61A6A85F47F1111850244DFFD401B94E3234BDC7 (void);
extern void NativePropertyAttribute__ctor_m6FD4C499DD1D0AD75668B6D860FACB837DFB6095 (void);
extern void NativeAsStructAttribute__ctor_m6AF1CD76481CC41BF7CE5C87DCF6CE68EF187298 (void);
extern void NativeTypeAttribute_get_Header_m354CB1478FC2FD60E23D6C48F3B96EB2E0A019C3 (void);
extern void NativeTypeAttribute_set_Header_m847CE2407B2426DFF214F3F18DE9644062A5FB8A (void);
extern void NativeTypeAttribute_get_IntermediateScriptingStructName_mE958EB7247E630CA598130F529E4723DC1B2EA29 (void);
extern void NativeTypeAttribute_set_IntermediateScriptingStructName_mA5791DC59CA4C7572D8B5C80D28176559E2BC907 (void);
extern void NativeTypeAttribute_get_CodegenOptions_m3F2E0E1E156D164C86272B37F814D512D1A9D856 (void);
extern void NativeTypeAttribute_set_CodegenOptions_mEECF2309A52F63B6258608EFF56AEAEF5E5572FC (void);
extern void NativeTypeAttribute__ctor_mC1B7AC76546C88B8314DBDE006BF54739A8D2BCD (void);
extern void NativeTypeAttribute__ctor_m42A2C59E33BA5B0DD88A44BA5C9A1C0FDDFBCF46 (void);
extern void NativeTypeAttribute__ctor_m3E053B25317A9630A5D4D9C8730A776A3C8F0487 (void);
extern void NativeTypeAttribute__ctor_mDD4BC645A84BADD12DAE76A202DBCC766E78C817 (void);
extern void NativeTypeAttribute__ctor_mAAAA100D0F13ECBDEB655342889B7D82CF920A75 (void);
extern void NotNullAttribute_get_Exception_mE4B227C2F09F0A1C9B6E372D4E37D0AFBD76382C (void);
extern void NotNullAttribute_set_Exception_m04F458FD91F138C58DC3A11E7C8E945701ECA528 (void);
extern void NotNullAttribute__ctor_m739C06B242B13C7519C17D0796F1A8FD18CDB7AA (void);
extern void UnityTypeAttribute__ctor_m84F05A80220321E2E2E368496DBDD9F6FADFD852 (void);
extern void UnmarshalledAttribute__ctor_m77164A228BF4D50B6CF784E319456DD0A74D0E8A (void);
extern void FreeFunctionAttribute__ctor_m0F9BA14FE9193D3CDE593EBF1EA06531F8400388 (void);
extern void FreeFunctionAttribute__ctor_m379A417DDF33E9D2BB5227D05DD365584E15DDAC (void);
extern void FreeFunctionAttribute__ctor_mAC3C22502820C23E516911E3B620387FB330D9E2 (void);
extern void ThreadSafeAttribute__ctor_m1FFE949649F2986C5526433C39F7BC0413472FA1 (void);
extern void StaticAccessorAttribute_get_Name_m73D6E66253F2E4008E6981857DD93279BDFAA8D1 (void);
extern void StaticAccessorAttribute_set_Name_m922EC8831D7A1DDE802D33204B88715EEC7B121B (void);
extern void StaticAccessorAttribute_get_Type_m09C04409F85B3A6F409D24C87ACF28270F94F6EC (void);
extern void StaticAccessorAttribute_set_Type_m2B350A872AB9222E3CEC32AA03A6B85E7770BDC0 (void);
extern void StaticAccessorAttribute__ctor_m42279C8975FB25EE888A080EAF594EBB063ABD70 (void);
extern void StaticAccessorAttribute__ctor_m441E5A01FEB31C10AE2D65C8F680B650E6CDF5C0 (void);
extern void StaticAccessorAttribute__ctor_mE2D9AB8F5866947BF2FB14AAB6C50B6F75B7606A (void);
extern void StaticAccessorAttribute__ctor_m6E1E237E6E03AC9F4B00C506199F03807BEA33BE (void);
extern void NativeThrowsAttribute_get_ThrowsException_mAE4DB719E90FC771580C19BE2DC5AB2BDFA9D087 (void);
extern void NativeThrowsAttribute_set_ThrowsException_m523089D1314C7549B50B3D1123149F064CC0A708 (void);
extern void NativeThrowsAttribute__ctor_m62F4CD2A09BDFD06FD43B74201D68CD2F79E109E (void);
extern void NativeThrowsAttribute__ctor_mDEF0069629219CD877E7431C6CC5EE892AFFA87E (void);
extern void IgnoreAttribute_get_DoesNotContributeToSize_mF6E1437BD7D0F61DD5685FDE67DD3AA0197D0978 (void);
extern void IgnoreAttribute_set_DoesNotContributeToSize_m0313EA1B54BF5E9FD61267FD476A7C9FB1A5C439 (void);
extern void IgnoreAttribute__ctor_mDA2998DACBA21CE6D66510F5AA6C4AB1FC33E323 (void);
extern void MarshalUnityObjectAs_get_MarshalAsType_m9369EB20194FD2AA8ED970AAF1AE572000241668 (void);
extern void MarshalUnityObjectAs_set_MarshalAsType_mB77C0C6CAABB1B49A127B717BA75C93861FC10D6 (void);
extern void MarshalUnityObjectAs__ctor_mE0BA471631A0CDE38F651279156D639CB919B30F (void);
extern void PreventExecutionInStateAttribute_get_singleFlagValue_mCD9E928715650B250C7210C6ABF4A2391148ED08 (void);
extern void PreventExecutionInStateAttribute_set_singleFlagValue_m94BBE1A07931B77B1EFA6ABF0FCADCBCD115B64D (void);
extern void PreventExecutionInStateAttribute_get_severity_mCEF7F79698BA3D94DE5D3CBA0B029BAE3B588604 (void);
extern void PreventExecutionInStateAttribute_set_severity_m8D32E4051BDEA0D93B2C875C0C0D610B815878BC (void);
extern void PreventExecutionInStateAttribute_get_howToFix_m282C1070A184E960D355C4BC800C75903F148C5F (void);
extern void PreventExecutionInStateAttribute_set_howToFix_m7FA8AD738B60805FCB91E19C519E26DE60B9E618 (void);
extern void PreventExecutionInStateAttribute__ctor_m8BA010210D82BEB25CD7A99CB98F12FD5000154D (void);
extern void PreventReadOnlyInstanceModificationAttribute__ctor_m62694007EB93F071791A5139A34F73695A14B9CC (void);
extern void SpanAttribute_get_IsReadOnly_m91272A9585C6A0FD4D5740550B65BE77A6597DB0 (void);
extern void SpanAttribute_get_SizeParameter_m8293A61CCA71745B2EC5012AD5128483295DD6EA (void);
extern void SpanAttribute__ctor_mB0CE184693D49E4DA6CD6F8D1DC260D5371C5F1B (void);
extern void UsedByNativeCodeAttribute__ctor_m7C07CF71798D5A94EB849622EA3883E547927D88 (void);
extern void UsedByNativeCodeAttribute__ctor_m3C75236A1A2C7E364179A7DEDE9E53E7C3CE334D (void);
extern void UsedByNativeCodeAttribute_get_Name_m50AE7FC232F925CEBE66B7997F77FA0292A208A5 (void);
extern void UsedByNativeCodeAttribute_set_Name_mF39ED6FE6040AF11CDCAE417EE6FE7DD6BD67E99 (void);
extern void RequiredByNativeCodeAttribute__ctor_mBCF284DE0C8F84F211ACE616928BE47E29055920 (void);
extern void RequiredByNativeCodeAttribute__ctor_m255C8498736E931A7FBB4ABC292F08AE43D3B7BF (void);
extern void RequiredByNativeCodeAttribute__ctor_m6FB8966FBCB6CC8B3989B0846EE7ED1DBFF60C87 (void);
extern void RequiredByNativeCodeAttribute__ctor_mB8283703DC7CF338F606CDD5812E207080EB4E45 (void);
extern void RequiredByNativeCodeAttribute_get_Name_m4B59AB758969187C9981C552FE626AF7E862C7FA (void);
extern void RequiredByNativeCodeAttribute_set_Name_mF218FB9A613487B6377AF13FA3531E5D581F487B (void);
extern void RequiredByNativeCodeAttribute_get_Optional_m098BAB4DAF587BDDD6673B3030EAE20A0602B16C (void);
extern void RequiredByNativeCodeAttribute_set_Optional_m009CBA1D24E7F6EDAE1411CB6A7A01CB2A13B541 (void);
extern void RequiredByNativeCodeAttribute_get_GenerateProxy_mE4944342958ED8A5D66F93E6B282E30CF05F8372 (void);
extern void RequiredByNativeCodeAttribute_set_GenerateProxy_m63E4D29F468D0F254136F1596AEA106BD9EDC89B (void);
static Il2CppMethodPointer s_methodPointers[154] = 
{
	AssetFileNameExtensionAttribute_get_preferredExtension_m1213FFDC570CB26BCCFD1207F3FA867ACC32470B,
	AssetFileNameExtensionAttribute_get_otherExtensions_mCEF5305E650AD2881C87BE512E69ADD9CBD28528,
	AssetFileNameExtensionAttribute__ctor_mBDD7C2006F136025094AABE2295D190F43F7F224,
	ThreadAndSerializationSafeAttribute__ctor_m5023D29907E8D1092714DFFA137A8364454709C7,
	IL2CPPStructAlignmentAttribute__ctor_m399EB2A2E74762BE0F5893EE0D9760AB9CD7FCD6,
	WritableAttribute__ctor_mC4E14F120A46CC52A65942F34224E2FB20CFF55A,
	RejectDragAndDropMaterial__ctor_m506305EEEFB3F69DE6F77279272177DC03132CE6,
	UnityEngineModuleAssembly__ctor_m3F8B023CF6E911C448EF9284C72F7BE92C6E72E3,
	NativeClassAttribute_get_QualifiedNativeName_mFB917E369CB8CD7F74AF1EC09F9FEF4530A19C1B,
	NativeClassAttribute_set_QualifiedNativeName_m5DA8C7AE66E1D4F1ACEA7592ABBF331A2650E0D6,
	NativeClassAttribute_get_Declaration_m2D47AB76F6FE6AD2EC2795E25BF836BC4BB1737E,
	NativeClassAttribute_set_Declaration_mE5497339ED9E55F30DABACBE40AD6D9D36CAE653,
	NativeClassAttribute__ctor_mA4C67EDCE7DA70AAADE77FE63EEECFFA911AD0C7,
	NativeClassAttribute__ctor_m92A42152D6ACC3FB9C381EFDFAA691251E2C3DD7,
	UnityString_Format_m98A0629641086A1BE20BBF7F4EADDE3FE3877D85,
	UnityString__ctor_m7E6FD7243B9B82BC86FDC810C838883D24305F6E,
	VisibleToOtherModulesAttribute__ctor_m2F00FAC0C9348472A15E93AD256145DCDD967E59,
	VisibleToOtherModulesAttribute__ctor_m2FC15A41D7218FFD29ECA4F70323F6DF8F19EC35,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeConditionalAttribute_get_Condition_m6C8E96BC1DFE39467E3C538B2E19B50E9820B8F2,
	NativeConditionalAttribute_set_Condition_m96107E75DC095D9B4A9A7CCE0EB0C3EFAA2F0053,
	NativeConditionalAttribute_get_StubReturnStatement_m929838EEE9AFF2CE1B9A35082E50B5961B1BA277,
	NativeConditionalAttribute_set_StubReturnStatement_mAA961429C06F0E4DC9149C26CC092ED29AF5331E,
	NativeConditionalAttribute_get_Enabled_m525FF5DAC1A5FE829D3F499582B451A6A1EBE5E3,
	NativeConditionalAttribute_set_Enabled_m379DA383CBBF2539C080D5DC0E8B97F4DB27DA64,
	NativeConditionalAttribute__ctor_mB2F5024D41C7565723B445E8D9EB8C0CF1ABC3B2,
	NativeConditionalAttribute__ctor_mD9C6F4E343C06314DF5DCFDDE9B5495E78F711ED,
	NativeConditionalAttribute__ctor_m209F75992A80F535ABE522BEFE8D6AB331FE3327,
	NativeConditionalAttribute__ctor_m24A34212D160364EAEF9E82A38363B1228952457,
	NativeConditionalAttribute__ctor_mC434BE2048774B15924D749B03CE8511F2F437E8,
	NativeConditionalAttribute__ctor_m5EC9368A4D06044907451D8C77B356E9ADBD241E,
	NativeHeaderAttribute_get_Header_m4F687D5727D0FDDA9572B6F22AF77C0CCE75B747,
	NativeHeaderAttribute_set_Header_mC431D0143381F2B35B08E211C2D5DD011372ADAB,
	NativeHeaderAttribute__ctor_mA7E08C5B4BC93515D8A57C3E63EA030EC64EC127,
	NativeHeaderAttribute__ctor_mD0D73B93BC695BC42CBF7E7FC6FB044131C3D0BC,
	NativeNameAttribute_get_Name_m1D7FF43F8CFC1031DA30F66933769CBBAC96E734,
	NativeNameAttribute_set_Name_mA5639D9FDBEADE899CBE13AFA4FCFB61A95ADAE7,
	NativeNameAttribute__ctor_m9F7E49F558B9861B14016F2776234789AE701E58,
	NativeNameAttribute__ctor_m9F46C053270D9DBCC9F9AB32C545A7696F0638D0,
	NativeWritableSelfAttribute_get_WritableSelf_mC7752EC7F5FEB887D5BAA3809A1B0BBE9921265C,
	NativeWritableSelfAttribute_set_WritableSelf_mB4B342C1D1678307EB4CF174BEAF8D1E94CDF3E6,
	NativeWritableSelfAttribute__ctor_mF59616C59BA935E75ED688DCBAF1966036CD039B,
	NativeWritableSelfAttribute__ctor_m786B528FD037DD07E189BC99647D032F6EC66C2E,
	NativeMethodAttribute_get_Name_m36F73081791E7346C730B079FFAC3555B90FF0C3,
	NativeMethodAttribute_set_Name_mE223393EB6EEA9E94A8A9CC093CB3CBBCB7C40B8,
	NativeMethodAttribute_get_IsThreadSafe_m60B90CC0E14F197876BD52EC8FE461D8FCE8A2CD,
	NativeMethodAttribute_set_IsThreadSafe_m443623C95A2E3552D0A791DC65E20ADFC447AE3F,
	NativeMethodAttribute_get_IsFreeFunction_m5EE28EAAB63D3AE1F3184E130AD9E2DA990A93DA,
	NativeMethodAttribute_set_IsFreeFunction_mCF665BA0A4CA25DA0EA8C3C5EDDB9A03315C9C4F,
	NativeMethodAttribute_get_ThrowsException_m36DB8CC6DE1961D21A771B143B116337C994BFD7,
	NativeMethodAttribute_set_ThrowsException_m05A53893F9C6616B40F8F70790C6533C30C64592,
	NativeMethodAttribute_get_HasExplicitThis_mCF8F40082AB2E207853C2A774117E2CEC435E1D3,
	NativeMethodAttribute_set_HasExplicitThis_m41908D1B191AEADF84C548E57A72B4E948D35678,
	NativeMethodAttribute_get_WritableSelf_mA8F0DBEA6AAC37FBE33E516481CED405D31251F8,
	NativeMethodAttribute_set_WritableSelf_mD8324F5BAE31FC4D1AF6FF27F2D2CB86481E1167,
	NativeMethodAttribute__ctor_mEA2A3B247A134B4453743CCF55655D16C63C741E,
	NativeMethodAttribute__ctor_m75590D9A8E1851C1DA619C07522D5D4AA63797B5,
	NativeMethodAttribute__ctor_mCDF45F4290C6955E1E9FE460709D9ACA18B2D06E,
	NativeMethodAttribute__ctor_m0B32894B3BD625410703207C7DBF61098BAEE973,
	NativeMethodAttribute__ctor_m87610ACB7728ED7F69A7592D15A449BEB2570DB4,
	NativePropertyAttribute_get_TargetType_m458E09B603653072CF0C983EC6F0555C6CB9291E,
	NativePropertyAttribute_set_TargetType_m858E4E653A2F81F5313EF4F5D69740D945B19ED7,
	NativePropertyAttribute__ctor_mAF06F05D29FE386561167B2BDF0149E3744411EA,
	NativePropertyAttribute__ctor_m32EC1C27977EA39BC2824743EC417561AC138BA3,
	NativePropertyAttribute__ctor_mB0C8185A91E0D3822DDE21A8319A1601F4099115,
	NativePropertyAttribute__ctor_m61A6A85F47F1111850244DFFD401B94E3234BDC7,
	NativePropertyAttribute__ctor_m6FD4C499DD1D0AD75668B6D860FACB837DFB6095,
	NativeAsStructAttribute__ctor_m6AF1CD76481CC41BF7CE5C87DCF6CE68EF187298,
	NativeTypeAttribute_get_Header_m354CB1478FC2FD60E23D6C48F3B96EB2E0A019C3,
	NativeTypeAttribute_set_Header_m847CE2407B2426DFF214F3F18DE9644062A5FB8A,
	NativeTypeAttribute_get_IntermediateScriptingStructName_mE958EB7247E630CA598130F529E4723DC1B2EA29,
	NativeTypeAttribute_set_IntermediateScriptingStructName_mA5791DC59CA4C7572D8B5C80D28176559E2BC907,
	NativeTypeAttribute_get_CodegenOptions_m3F2E0E1E156D164C86272B37F814D512D1A9D856,
	NativeTypeAttribute_set_CodegenOptions_mEECF2309A52F63B6258608EFF56AEAEF5E5572FC,
	NativeTypeAttribute__ctor_mC1B7AC76546C88B8314DBDE006BF54739A8D2BCD,
	NativeTypeAttribute__ctor_m42A2C59E33BA5B0DD88A44BA5C9A1C0FDDFBCF46,
	NativeTypeAttribute__ctor_m3E053B25317A9630A5D4D9C8730A776A3C8F0487,
	NativeTypeAttribute__ctor_mDD4BC645A84BADD12DAE76A202DBCC766E78C817,
	NativeTypeAttribute__ctor_mAAAA100D0F13ECBDEB655342889B7D82CF920A75,
	NotNullAttribute_get_Exception_mE4B227C2F09F0A1C9B6E372D4E37D0AFBD76382C,
	NotNullAttribute_set_Exception_m04F458FD91F138C58DC3A11E7C8E945701ECA528,
	NotNullAttribute__ctor_m739C06B242B13C7519C17D0796F1A8FD18CDB7AA,
	UnityTypeAttribute__ctor_m84F05A80220321E2E2E368496DBDD9F6FADFD852,
	UnmarshalledAttribute__ctor_m77164A228BF4D50B6CF784E319456DD0A74D0E8A,
	FreeFunctionAttribute__ctor_m0F9BA14FE9193D3CDE593EBF1EA06531F8400388,
	FreeFunctionAttribute__ctor_m379A417DDF33E9D2BB5227D05DD365584E15DDAC,
	FreeFunctionAttribute__ctor_mAC3C22502820C23E516911E3B620387FB330D9E2,
	ThreadSafeAttribute__ctor_m1FFE949649F2986C5526433C39F7BC0413472FA1,
	StaticAccessorAttribute_get_Name_m73D6E66253F2E4008E6981857DD93279BDFAA8D1,
	StaticAccessorAttribute_set_Name_m922EC8831D7A1DDE802D33204B88715EEC7B121B,
	StaticAccessorAttribute_get_Type_m09C04409F85B3A6F409D24C87ACF28270F94F6EC,
	StaticAccessorAttribute_set_Type_m2B350A872AB9222E3CEC32AA03A6B85E7770BDC0,
	StaticAccessorAttribute__ctor_m42279C8975FB25EE888A080EAF594EBB063ABD70,
	StaticAccessorAttribute__ctor_m441E5A01FEB31C10AE2D65C8F680B650E6CDF5C0,
	StaticAccessorAttribute__ctor_mE2D9AB8F5866947BF2FB14AAB6C50B6F75B7606A,
	StaticAccessorAttribute__ctor_m6E1E237E6E03AC9F4B00C506199F03807BEA33BE,
	NativeThrowsAttribute_get_ThrowsException_mAE4DB719E90FC771580C19BE2DC5AB2BDFA9D087,
	NativeThrowsAttribute_set_ThrowsException_m523089D1314C7549B50B3D1123149F064CC0A708,
	NativeThrowsAttribute__ctor_m62F4CD2A09BDFD06FD43B74201D68CD2F79E109E,
	NativeThrowsAttribute__ctor_mDEF0069629219CD877E7431C6CC5EE892AFFA87E,
	IgnoreAttribute_get_DoesNotContributeToSize_mF6E1437BD7D0F61DD5685FDE67DD3AA0197D0978,
	IgnoreAttribute_set_DoesNotContributeToSize_m0313EA1B54BF5E9FD61267FD476A7C9FB1A5C439,
	IgnoreAttribute__ctor_mDA2998DACBA21CE6D66510F5AA6C4AB1FC33E323,
	MarshalUnityObjectAs_get_MarshalAsType_m9369EB20194FD2AA8ED970AAF1AE572000241668,
	MarshalUnityObjectAs_set_MarshalAsType_mB77C0C6CAABB1B49A127B717BA75C93861FC10D6,
	MarshalUnityObjectAs__ctor_mE0BA471631A0CDE38F651279156D639CB919B30F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PreventExecutionInStateAttribute_get_singleFlagValue_mCD9E928715650B250C7210C6ABF4A2391148ED08,
	PreventExecutionInStateAttribute_set_singleFlagValue_m94BBE1A07931B77B1EFA6ABF0FCADCBCD115B64D,
	PreventExecutionInStateAttribute_get_severity_mCEF7F79698BA3D94DE5D3CBA0B029BAE3B588604,
	PreventExecutionInStateAttribute_set_severity_m8D32E4051BDEA0D93B2C875C0C0D610B815878BC,
	PreventExecutionInStateAttribute_get_howToFix_m282C1070A184E960D355C4BC800C75903F148C5F,
	PreventExecutionInStateAttribute_set_howToFix_m7FA8AD738B60805FCB91E19C519E26DE60B9E618,
	PreventExecutionInStateAttribute__ctor_m8BA010210D82BEB25CD7A99CB98F12FD5000154D,
	PreventReadOnlyInstanceModificationAttribute__ctor_m62694007EB93F071791A5139A34F73695A14B9CC,
	NULL,
	NULL,
	SpanAttribute_get_IsReadOnly_m91272A9585C6A0FD4D5740550B65BE77A6597DB0,
	SpanAttribute_get_SizeParameter_m8293A61CCA71745B2EC5012AD5128483295DD6EA,
	SpanAttribute__ctor_mB0CE184693D49E4DA6CD6F8D1DC260D5371C5F1B,
	UsedByNativeCodeAttribute__ctor_m7C07CF71798D5A94EB849622EA3883E547927D88,
	UsedByNativeCodeAttribute__ctor_m3C75236A1A2C7E364179A7DEDE9E53E7C3CE334D,
	UsedByNativeCodeAttribute_get_Name_m50AE7FC232F925CEBE66B7997F77FA0292A208A5,
	UsedByNativeCodeAttribute_set_Name_mF39ED6FE6040AF11CDCAE417EE6FE7DD6BD67E99,
	RequiredByNativeCodeAttribute__ctor_mBCF284DE0C8F84F211ACE616928BE47E29055920,
	RequiredByNativeCodeAttribute__ctor_m255C8498736E931A7FBB4ABC292F08AE43D3B7BF,
	RequiredByNativeCodeAttribute__ctor_m6FB8966FBCB6CC8B3989B0846EE7ED1DBFF60C87,
	RequiredByNativeCodeAttribute__ctor_mB8283703DC7CF338F606CDD5812E207080EB4E45,
	RequiredByNativeCodeAttribute_get_Name_m4B59AB758969187C9981C552FE626AF7E862C7FA,
	RequiredByNativeCodeAttribute_set_Name_mF218FB9A613487B6377AF13FA3531E5D581F487B,
	RequiredByNativeCodeAttribute_get_Optional_m098BAB4DAF587BDDD6673B3030EAE20A0602B16C,
	RequiredByNativeCodeAttribute_set_Optional_m009CBA1D24E7F6EDAE1411CB6A7A01CB2A13B541,
	RequiredByNativeCodeAttribute_get_GenerateProxy_mE4944342958ED8A5D66F93E6B282E30CF05F8372,
	RequiredByNativeCodeAttribute_set_GenerateProxy_m63E4D29F468D0F254136F1596AEA106BD9EDC89B,
};
static const int32_t s_InvokerIndices[154] = 
{
	4250,
	4250,
	2802,
	4364,
	4364,
	4364,
	4364,
	4364,
	4250,
	3881,
	4250,
	3881,
	3881,
	2802,
	7631,
	4364,
	4364,
	3881,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4364,
	3881,
	3807,
	2788,
	2077,
	2802,
	4250,
	3881,
	4364,
	3881,
	4250,
	3881,
	4364,
	3881,
	4168,
	3807,
	4364,
	3807,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4364,
	3881,
	2788,
	2026,
	1391,
	4216,
	3852,
	4364,
	3881,
	2796,
	2027,
	1393,
	4364,
	4250,
	3881,
	4250,
	3881,
	4216,
	3852,
	4364,
	3852,
	3881,
	2796,
	2739,
	4250,
	3881,
	3881,
	4364,
	4364,
	4364,
	3881,
	2788,
	4364,
	4250,
	3881,
	4216,
	3852,
	4364,
	3881,
	3852,
	2796,
	4168,
	3807,
	4364,
	3807,
	4168,
	3807,
	4364,
	4250,
	3881,
	3881,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	3881,
	4216,
	3852,
	4250,
	3881,
	2057,
	4364,
	0,
	0,
	4168,
	4250,
	2788,
	4364,
	3881,
	4250,
	3881,
	4364,
	3881,
	3807,
	2788,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SharedInternalsModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SharedInternalsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SharedInternalsModule_CodeGenModule = 
{
	"UnityEngine.SharedInternalsModule.dll",
	154,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_SharedInternalsModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
