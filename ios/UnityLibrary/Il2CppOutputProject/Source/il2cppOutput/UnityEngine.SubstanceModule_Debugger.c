﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[52] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_SubstanceModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_SubstanceModule[279] = 
{
	{ 109796, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109796, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109796, 1, 14, 14, 9, 10, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109796, 1, 15, 15, 13, 244, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109796, 1, 15, 15, 13, 244, 6, kSequencePointKind_StepOut, 0, 4 },
	{ 109797, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 5 },
	{ 109797, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 6 },
	{ 109797, 2, 105, 105, 15, 35, 0, kSequencePointKind_Normal, 0, 7 },
	{ 109797, 2, 105, 105, 15, 35, 2, kSequencePointKind_StepOut, 0, 8 },
	{ 109797, 2, 106, 106, 9, 10, 8, kSequencePointKind_Normal, 0, 9 },
	{ 109797, 2, 107, 107, 13, 30, 9, kSequencePointKind_Normal, 0, 10 },
	{ 109797, 2, 107, 107, 13, 30, 9, kSequencePointKind_StepOut, 0, 11 },
	{ 109797, 2, 108, 108, 9, 10, 15, kSequencePointKind_Normal, 0, 12 },
	{ 109798, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 13 },
	{ 109798, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 14 },
	{ 109798, 2, 111, 111, 9, 10, 0, kSequencePointKind_Normal, 0, 15 },
	{ 109798, 2, 112, 112, 13, 244, 1, kSequencePointKind_Normal, 0, 16 },
	{ 109798, 2, 112, 112, 13, 244, 6, kSequencePointKind_StepOut, 0, 17 },
	{ 109799, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 18 },
	{ 109799, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 19 },
	{ 109799, 2, 116, 116, 9, 10, 0, kSequencePointKind_Normal, 0, 20 },
	{ 109799, 2, 117, 117, 13, 244, 1, kSequencePointKind_Normal, 0, 21 },
	{ 109799, 2, 117, 117, 13, 244, 6, kSequencePointKind_StepOut, 0, 22 },
	{ 109800, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 23 },
	{ 109800, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 24 },
	{ 109800, 2, 121, 121, 9, 10, 0, kSequencePointKind_Normal, 0, 25 },
	{ 109800, 2, 122, 122, 13, 244, 1, kSequencePointKind_Normal, 0, 26 },
	{ 109800, 2, 122, 122, 13, 244, 6, kSequencePointKind_StepOut, 0, 27 },
	{ 109801, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 28 },
	{ 109801, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 29 },
	{ 109801, 2, 126, 126, 9, 10, 0, kSequencePointKind_Normal, 0, 30 },
	{ 109801, 2, 127, 127, 13, 244, 1, kSequencePointKind_Normal, 0, 31 },
	{ 109801, 2, 127, 127, 13, 244, 6, kSequencePointKind_StepOut, 0, 32 },
	{ 109802, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 33 },
	{ 109802, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 34 },
	{ 109802, 2, 131, 131, 9, 10, 0, kSequencePointKind_Normal, 0, 35 },
	{ 109802, 2, 132, 132, 13, 30, 1, kSequencePointKind_Normal, 0, 36 },
	{ 109802, 2, 132, 132, 13, 30, 1, kSequencePointKind_StepOut, 0, 37 },
	{ 109802, 2, 133, 133, 9, 10, 7, kSequencePointKind_Normal, 0, 38 },
	{ 109803, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 39 },
	{ 109803, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 40 },
	{ 109803, 2, 136, 136, 9, 10, 0, kSequencePointKind_Normal, 0, 41 },
	{ 109803, 2, 137, 137, 13, 244, 1, kSequencePointKind_Normal, 0, 42 },
	{ 109803, 2, 137, 137, 13, 244, 6, kSequencePointKind_StepOut, 0, 43 },
	{ 109804, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 44 },
	{ 109804, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 45 },
	{ 109804, 2, 141, 141, 9, 10, 0, kSequencePointKind_Normal, 0, 46 },
	{ 109804, 2, 142, 142, 13, 30, 1, kSequencePointKind_Normal, 0, 47 },
	{ 109804, 2, 142, 142, 13, 30, 1, kSequencePointKind_StepOut, 0, 48 },
	{ 109804, 2, 143, 143, 9, 10, 7, kSequencePointKind_Normal, 0, 49 },
	{ 109805, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 50 },
	{ 109805, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 51 },
	{ 109805, 2, 146, 146, 9, 10, 0, kSequencePointKind_Normal, 0, 52 },
	{ 109805, 2, 147, 147, 13, 244, 1, kSequencePointKind_Normal, 0, 53 },
	{ 109805, 2, 147, 147, 13, 244, 6, kSequencePointKind_StepOut, 0, 54 },
	{ 109806, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 55 },
	{ 109806, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 56 },
	{ 109806, 2, 151, 151, 9, 10, 0, kSequencePointKind_Normal, 0, 57 },
	{ 109806, 2, 152, 152, 13, 30, 1, kSequencePointKind_Normal, 0, 58 },
	{ 109806, 2, 152, 152, 13, 30, 1, kSequencePointKind_StepOut, 0, 59 },
	{ 109806, 2, 153, 153, 9, 10, 7, kSequencePointKind_Normal, 0, 60 },
	{ 109807, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 61 },
	{ 109807, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 62 },
	{ 109807, 2, 156, 156, 9, 10, 0, kSequencePointKind_Normal, 0, 63 },
	{ 109807, 2, 157, 157, 13, 244, 1, kSequencePointKind_Normal, 0, 64 },
	{ 109807, 2, 157, 157, 13, 244, 6, kSequencePointKind_StepOut, 0, 65 },
	{ 109808, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 66 },
	{ 109808, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 67 },
	{ 109808, 2, 161, 161, 9, 10, 0, kSequencePointKind_Normal, 0, 68 },
	{ 109808, 2, 162, 162, 13, 30, 1, kSequencePointKind_Normal, 0, 69 },
	{ 109808, 2, 162, 162, 13, 30, 1, kSequencePointKind_StepOut, 0, 70 },
	{ 109808, 2, 163, 163, 9, 10, 7, kSequencePointKind_Normal, 0, 71 },
	{ 109809, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 72 },
	{ 109809, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 73 },
	{ 109809, 2, 166, 166, 9, 10, 0, kSequencePointKind_Normal, 0, 74 },
	{ 109809, 2, 167, 167, 13, 244, 1, kSequencePointKind_Normal, 0, 75 },
	{ 109809, 2, 167, 167, 13, 244, 6, kSequencePointKind_StepOut, 0, 76 },
	{ 109810, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 77 },
	{ 109810, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 78 },
	{ 109810, 2, 171, 171, 9, 10, 0, kSequencePointKind_Normal, 0, 79 },
	{ 109810, 2, 172, 172, 13, 30, 1, kSequencePointKind_Normal, 0, 80 },
	{ 109810, 2, 172, 172, 13, 30, 1, kSequencePointKind_StepOut, 0, 81 },
	{ 109810, 2, 173, 173, 9, 10, 7, kSequencePointKind_Normal, 0, 82 },
	{ 109811, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 83 },
	{ 109811, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 84 },
	{ 109811, 2, 176, 176, 9, 10, 0, kSequencePointKind_Normal, 0, 85 },
	{ 109811, 2, 177, 177, 13, 244, 1, kSequencePointKind_Normal, 0, 86 },
	{ 109811, 2, 177, 177, 13, 244, 6, kSequencePointKind_StepOut, 0, 87 },
	{ 109812, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 88 },
	{ 109812, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 89 },
	{ 109812, 2, 181, 181, 9, 10, 0, kSequencePointKind_Normal, 0, 90 },
	{ 109812, 2, 182, 182, 13, 30, 1, kSequencePointKind_Normal, 0, 91 },
	{ 109812, 2, 182, 182, 13, 30, 1, kSequencePointKind_StepOut, 0, 92 },
	{ 109812, 2, 183, 183, 9, 10, 7, kSequencePointKind_Normal, 0, 93 },
	{ 109813, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 94 },
	{ 109813, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 95 },
	{ 109813, 2, 186, 186, 9, 10, 0, kSequencePointKind_Normal, 0, 96 },
	{ 109813, 2, 187, 187, 13, 244, 1, kSequencePointKind_Normal, 0, 97 },
	{ 109813, 2, 187, 187, 13, 244, 6, kSequencePointKind_StepOut, 0, 98 },
	{ 109814, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 99 },
	{ 109814, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 100 },
	{ 109814, 2, 191, 191, 9, 10, 0, kSequencePointKind_Normal, 0, 101 },
	{ 109814, 2, 192, 192, 13, 30, 1, kSequencePointKind_Normal, 0, 102 },
	{ 109814, 2, 192, 192, 13, 30, 1, kSequencePointKind_StepOut, 0, 103 },
	{ 109814, 2, 193, 193, 9, 10, 7, kSequencePointKind_Normal, 0, 104 },
	{ 109815, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 105 },
	{ 109815, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 106 },
	{ 109815, 2, 196, 196, 9, 10, 0, kSequencePointKind_Normal, 0, 107 },
	{ 109815, 2, 197, 197, 13, 244, 1, kSequencePointKind_Normal, 0, 108 },
	{ 109815, 2, 197, 197, 13, 244, 6, kSequencePointKind_StepOut, 0, 109 },
	{ 109816, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 110 },
	{ 109816, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 111 },
	{ 109816, 2, 201, 201, 9, 10, 0, kSequencePointKind_Normal, 0, 112 },
	{ 109816, 2, 202, 202, 13, 30, 1, kSequencePointKind_Normal, 0, 113 },
	{ 109816, 2, 202, 202, 13, 30, 1, kSequencePointKind_StepOut, 0, 114 },
	{ 109816, 2, 203, 203, 9, 10, 7, kSequencePointKind_Normal, 0, 115 },
	{ 109817, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 116 },
	{ 109817, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 117 },
	{ 109817, 2, 206, 206, 9, 10, 0, kSequencePointKind_Normal, 0, 118 },
	{ 109817, 2, 207, 207, 13, 30, 1, kSequencePointKind_Normal, 0, 119 },
	{ 109817, 2, 207, 207, 13, 30, 1, kSequencePointKind_StepOut, 0, 120 },
	{ 109817, 2, 208, 208, 9, 10, 7, kSequencePointKind_Normal, 0, 121 },
	{ 109818, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 122 },
	{ 109818, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 123 },
	{ 109818, 2, 212, 212, 17, 18, 0, kSequencePointKind_Normal, 0, 124 },
	{ 109818, 2, 212, 212, 19, 250, 1, kSequencePointKind_Normal, 0, 125 },
	{ 109818, 2, 212, 212, 19, 250, 6, kSequencePointKind_StepOut, 0, 126 },
	{ 109819, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 127 },
	{ 109819, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 128 },
	{ 109819, 2, 213, 213, 17, 18, 0, kSequencePointKind_Normal, 0, 129 },
	{ 109819, 2, 213, 213, 19, 36, 1, kSequencePointKind_Normal, 0, 130 },
	{ 109819, 2, 213, 213, 19, 36, 1, kSequencePointKind_StepOut, 0, 131 },
	{ 109819, 2, 213, 213, 37, 38, 7, kSequencePointKind_Normal, 0, 132 },
	{ 109820, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 133 },
	{ 109820, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 134 },
	{ 109820, 2, 219, 219, 17, 18, 0, kSequencePointKind_Normal, 0, 135 },
	{ 109820, 2, 219, 219, 19, 250, 1, kSequencePointKind_Normal, 0, 136 },
	{ 109820, 2, 219, 219, 19, 250, 6, kSequencePointKind_StepOut, 0, 137 },
	{ 109821, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 138 },
	{ 109821, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 139 },
	{ 109821, 2, 220, 220, 17, 18, 0, kSequencePointKind_Normal, 0, 140 },
	{ 109821, 2, 220, 220, 19, 36, 1, kSequencePointKind_Normal, 0, 141 },
	{ 109821, 2, 220, 220, 19, 36, 1, kSequencePointKind_StepOut, 0, 142 },
	{ 109821, 2, 220, 220, 37, 38, 7, kSequencePointKind_Normal, 0, 143 },
	{ 109822, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 144 },
	{ 109822, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 145 },
	{ 109822, 2, 224, 224, 9, 10, 0, kSequencePointKind_Normal, 0, 146 },
	{ 109822, 2, 225, 225, 13, 30, 1, kSequencePointKind_Normal, 0, 147 },
	{ 109822, 2, 225, 225, 13, 30, 1, kSequencePointKind_StepOut, 0, 148 },
	{ 109822, 2, 226, 226, 9, 10, 7, kSequencePointKind_Normal, 0, 149 },
	{ 109823, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 150 },
	{ 109823, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 151 },
	{ 109823, 2, 229, 229, 9, 10, 0, kSequencePointKind_Normal, 0, 152 },
	{ 109823, 2, 230, 230, 13, 30, 1, kSequencePointKind_Normal, 0, 153 },
	{ 109823, 2, 230, 230, 13, 30, 1, kSequencePointKind_StepOut, 0, 154 },
	{ 109823, 2, 231, 231, 9, 10, 7, kSequencePointKind_Normal, 0, 155 },
	{ 109824, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 156 },
	{ 109824, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 157 },
	{ 109824, 2, 235, 235, 17, 18, 0, kSequencePointKind_Normal, 0, 158 },
	{ 109824, 2, 235, 235, 19, 250, 1, kSequencePointKind_Normal, 0, 159 },
	{ 109824, 2, 235, 235, 19, 250, 6, kSequencePointKind_StepOut, 0, 160 },
	{ 109825, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 161 },
	{ 109825, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 162 },
	{ 109825, 2, 239, 239, 9, 10, 0, kSequencePointKind_Normal, 0, 163 },
	{ 109825, 2, 240, 240, 13, 30, 1, kSequencePointKind_Normal, 0, 164 },
	{ 109825, 2, 240, 240, 13, 30, 1, kSequencePointKind_StepOut, 0, 165 },
	{ 109825, 2, 241, 241, 9, 10, 7, kSequencePointKind_Normal, 0, 166 },
	{ 109826, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 167 },
	{ 109826, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 168 },
	{ 109826, 2, 245, 245, 17, 18, 0, kSequencePointKind_Normal, 0, 169 },
	{ 109826, 2, 245, 245, 19, 250, 1, kSequencePointKind_Normal, 0, 170 },
	{ 109826, 2, 245, 245, 19, 250, 6, kSequencePointKind_StepOut, 0, 171 },
	{ 109827, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 172 },
	{ 109827, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 173 },
	{ 109827, 2, 250, 250, 17, 18, 0, kSequencePointKind_Normal, 0, 174 },
	{ 109827, 2, 250, 250, 19, 250, 1, kSequencePointKind_Normal, 0, 175 },
	{ 109827, 2, 250, 250, 19, 250, 6, kSequencePointKind_StepOut, 0, 176 },
	{ 109828, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 177 },
	{ 109828, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 178 },
	{ 109828, 2, 251, 251, 17, 18, 0, kSequencePointKind_Normal, 0, 179 },
	{ 109828, 2, 251, 251, 19, 36, 1, kSequencePointKind_Normal, 0, 180 },
	{ 109828, 2, 251, 251, 19, 36, 1, kSequencePointKind_StepOut, 0, 181 },
	{ 109828, 2, 251, 251, 37, 38, 7, kSequencePointKind_Normal, 0, 182 },
	{ 109829, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 183 },
	{ 109829, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 184 },
	{ 109829, 2, 256, 256, 17, 18, 0, kSequencePointKind_Normal, 0, 185 },
	{ 109829, 2, 256, 256, 19, 250, 1, kSequencePointKind_Normal, 0, 186 },
	{ 109829, 2, 256, 256, 19, 250, 6, kSequencePointKind_StepOut, 0, 187 },
	{ 109830, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 188 },
	{ 109830, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 189 },
	{ 109830, 2, 261, 261, 17, 18, 0, kSequencePointKind_Normal, 0, 190 },
	{ 109830, 2, 261, 261, 19, 250, 1, kSequencePointKind_Normal, 0, 191 },
	{ 109830, 2, 261, 261, 19, 250, 6, kSequencePointKind_StepOut, 0, 192 },
	{ 109831, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 193 },
	{ 109831, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 194 },
	{ 109831, 2, 266, 266, 17, 18, 0, kSequencePointKind_Normal, 0, 195 },
	{ 109831, 2, 266, 266, 19, 250, 1, kSequencePointKind_Normal, 0, 196 },
	{ 109831, 2, 266, 266, 19, 250, 6, kSequencePointKind_StepOut, 0, 197 },
	{ 109832, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 198 },
	{ 109832, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 199 },
	{ 109832, 2, 267, 267, 17, 18, 0, kSequencePointKind_Normal, 0, 200 },
	{ 109832, 2, 267, 267, 19, 36, 1, kSequencePointKind_Normal, 0, 201 },
	{ 109832, 2, 267, 267, 19, 36, 1, kSequencePointKind_StepOut, 0, 202 },
	{ 109832, 2, 267, 267, 37, 38, 7, kSequencePointKind_Normal, 0, 203 },
	{ 109833, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 204 },
	{ 109833, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 205 },
	{ 109833, 2, 272, 272, 17, 18, 0, kSequencePointKind_Normal, 0, 206 },
	{ 109833, 2, 272, 272, 19, 250, 1, kSequencePointKind_Normal, 0, 207 },
	{ 109833, 2, 272, 272, 19, 250, 6, kSequencePointKind_StepOut, 0, 208 },
	{ 109834, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 209 },
	{ 109834, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 210 },
	{ 109834, 2, 273, 273, 17, 18, 0, kSequencePointKind_Normal, 0, 211 },
	{ 109834, 2, 273, 273, 19, 36, 1, kSequencePointKind_Normal, 0, 212 },
	{ 109834, 2, 273, 273, 19, 36, 1, kSequencePointKind_StepOut, 0, 213 },
	{ 109834, 2, 273, 273, 37, 38, 7, kSequencePointKind_Normal, 0, 214 },
	{ 109835, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 215 },
	{ 109835, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 216 },
	{ 109835, 2, 277, 277, 9, 10, 0, kSequencePointKind_Normal, 0, 217 },
	{ 109835, 2, 278, 278, 13, 244, 1, kSequencePointKind_Normal, 0, 218 },
	{ 109835, 2, 278, 278, 13, 244, 6, kSequencePointKind_StepOut, 0, 219 },
	{ 109836, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 220 },
	{ 109836, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 221 },
	{ 109836, 2, 282, 282, 9, 10, 0, kSequencePointKind_Normal, 0, 222 },
	{ 109836, 2, 283, 283, 13, 244, 1, kSequencePointKind_Normal, 0, 223 },
	{ 109836, 2, 283, 283, 13, 244, 6, kSequencePointKind_StepOut, 0, 224 },
	{ 109837, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 225 },
	{ 109837, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 226 },
	{ 109837, 2, 288, 288, 17, 18, 0, kSequencePointKind_Normal, 0, 227 },
	{ 109837, 2, 288, 288, 19, 250, 1, kSequencePointKind_Normal, 0, 228 },
	{ 109837, 2, 288, 288, 19, 250, 6, kSequencePointKind_StepOut, 0, 229 },
	{ 109838, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 230 },
	{ 109838, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 231 },
	{ 109838, 2, 289, 289, 17, 18, 0, kSequencePointKind_Normal, 0, 232 },
	{ 109838, 2, 289, 289, 19, 36, 1, kSequencePointKind_Normal, 0, 233 },
	{ 109838, 2, 289, 289, 19, 36, 1, kSequencePointKind_StepOut, 0, 234 },
	{ 109838, 2, 289, 289, 37, 38, 7, kSequencePointKind_Normal, 0, 235 },
	{ 109839, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 236 },
	{ 109839, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 237 },
	{ 109839, 2, 293, 293, 9, 10, 0, kSequencePointKind_Normal, 0, 238 },
	{ 109839, 2, 294, 294, 13, 30, 1, kSequencePointKind_Normal, 0, 239 },
	{ 109839, 2, 294, 294, 13, 30, 1, kSequencePointKind_StepOut, 0, 240 },
	{ 109839, 2, 295, 295, 9, 10, 7, kSequencePointKind_Normal, 0, 241 },
	{ 109840, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 242 },
	{ 109840, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 243 },
	{ 109840, 2, 299, 299, 17, 18, 0, kSequencePointKind_Normal, 0, 244 },
	{ 109840, 2, 299, 299, 19, 250, 1, kSequencePointKind_Normal, 0, 245 },
	{ 109840, 2, 299, 299, 19, 250, 6, kSequencePointKind_StepOut, 0, 246 },
	{ 109842, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 247 },
	{ 109842, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 248 },
	{ 109842, 2, 309, 309, 9, 36, 0, kSequencePointKind_Normal, 0, 249 },
	{ 109842, 2, 309, 309, 9, 36, 1, kSequencePointKind_StepOut, 0, 250 },
	{ 109842, 2, 310, 310, 9, 10, 7, kSequencePointKind_Normal, 0, 251 },
	{ 109842, 2, 311, 311, 13, 244, 8, kSequencePointKind_Normal, 0, 252 },
	{ 109842, 2, 311, 311, 13, 244, 13, kSequencePointKind_StepOut, 0, 253 },
	{ 109843, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 254 },
	{ 109843, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 255 },
	{ 109843, 2, 315, 315, 9, 10, 0, kSequencePointKind_Normal, 0, 256 },
	{ 109843, 2, 316, 316, 13, 244, 1, kSequencePointKind_Normal, 0, 257 },
	{ 109843, 2, 316, 316, 13, 244, 6, kSequencePointKind_StepOut, 0, 258 },
	{ 109844, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 259 },
	{ 109844, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 260 },
	{ 109844, 2, 320, 320, 9, 10, 0, kSequencePointKind_Normal, 0, 261 },
	{ 109844, 2, 321, 321, 13, 244, 1, kSequencePointKind_Normal, 0, 262 },
	{ 109844, 2, 321, 321, 13, 244, 6, kSequencePointKind_StepOut, 0, 263 },
	{ 109845, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 264 },
	{ 109845, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 265 },
	{ 109845, 2, 326, 326, 17, 18, 0, kSequencePointKind_Normal, 0, 266 },
	{ 109845, 2, 326, 326, 19, 250, 1, kSequencePointKind_Normal, 0, 267 },
	{ 109845, 2, 326, 326, 19, 250, 6, kSequencePointKind_StepOut, 0, 268 },
	{ 109846, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 269 },
	{ 109846, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 270 },
	{ 109846, 2, 331, 331, 17, 18, 0, kSequencePointKind_Normal, 0, 271 },
	{ 109846, 2, 331, 331, 19, 250, 1, kSequencePointKind_Normal, 0, 272 },
	{ 109846, 2, 331, 331, 19, 250, 6, kSequencePointKind_StepOut, 0, 273 },
	{ 109847, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 274 },
	{ 109847, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 275 },
	{ 109847, 2, 335, 335, 9, 10, 0, kSequencePointKind_Normal, 0, 276 },
	{ 109847, 2, 336, 336, 13, 244, 1, kSequencePointKind_Normal, 0, 277 },
	{ 109847, 2, 336, 336, 13, 244, 6, kSequencePointKind_StepOut, 0, 278 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_SubstanceModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_SubstanceModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Substance/SubstanceUtility.bindings.cs", { 51, 162, 9, 13, 106, 19, 2, 246, 104, 44, 130, 7, 238, 238, 5, 199} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Substance/SubstanceUtility.cs", { 39, 89, 173, 253, 152, 239, 90, 57, 30, 12, 13, 94, 99, 67, 120, 147} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[3] = 
{
	{ 14119, 1 },
	{ 14119, 2 },
	{ 14126, 2 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[52] = 
{
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SubstanceModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SubstanceModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	279,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_SubstanceModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	3,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
