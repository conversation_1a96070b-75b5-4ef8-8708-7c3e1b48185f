﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct StreamingController_t76A8C62300A985A768378800C40A822D427F95DF;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;

IL2CPP_EXTERN_C const RuntimeMethod* StreamingController__ctor_m552E70EAB8C02690F934B9CC1D9F1EAD2CCDE798_RuntimeMethod_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t39769C782D35A66F1E8CB22109BA525CC07872C2 
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct StreamingController_t76A8C62300A985A768378800C40A822D427F95DF  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8 (Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float StreamingController_get_streamingMipmapBias_mF99272AF97EBE7550CD3C433165C75B85AC154F2 (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF* __this, const RuntimeMethod* method) 
{
	typedef float (*StreamingController_get_streamingMipmapBias_mF99272AF97EBE7550CD3C433165C75B85AC154F2_ftn) (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF*);
	static StreamingController_get_streamingMipmapBias_mF99272AF97EBE7550CD3C433165C75B85AC154F2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (StreamingController_get_streamingMipmapBias_mF99272AF97EBE7550CD3C433165C75B85AC154F2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.StreamingController::get_streamingMipmapBias()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StreamingController_set_streamingMipmapBias_m5A74189DAAB6A6109154575CB3BDA9A99598A8AA (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*StreamingController_set_streamingMipmapBias_m5A74189DAAB6A6109154575CB3BDA9A99598A8AA_ftn) (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF*, float);
	static StreamingController_set_streamingMipmapBias_m5A74189DAAB6A6109154575CB3BDA9A99598A8AA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (StreamingController_set_streamingMipmapBias_m5A74189DAAB6A6109154575CB3BDA9A99598A8AA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.StreamingController::set_streamingMipmapBias(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StreamingController_SetPreloading_m6CC4C9E5742E497DF24D852A5B3A22ED7AE8E25D (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF* __this, float ___0_timeoutSeconds, bool ___1_activateCameraOnTimeout, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___2_disableCameraCuttingFrom, const RuntimeMethod* method) 
{
	typedef void (*StreamingController_SetPreloading_m6CC4C9E5742E497DF24D852A5B3A22ED7AE8E25D_ftn) (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF*, float, bool, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184*);
	static StreamingController_SetPreloading_m6CC4C9E5742E497DF24D852A5B3A22ED7AE8E25D_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (StreamingController_SetPreloading_m6CC4C9E5742E497DF24D852A5B3A22ED7AE8E25D_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.StreamingController::SetPreloading(System.Single,System.Boolean,UnityEngine.Camera)");
	_il2cpp_icall_func(__this, ___0_timeoutSeconds, ___1_activateCameraOnTimeout, ___2_disableCameraCuttingFrom);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StreamingController_CancelPreloading_mD5ABD57F18813CE7BF549827CAB2D683B2941B06 (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF* __this, const RuntimeMethod* method) 
{
	typedef void (*StreamingController_CancelPreloading_mD5ABD57F18813CE7BF549827CAB2D683B2941B06_ftn) (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF*);
	static StreamingController_CancelPreloading_mD5ABD57F18813CE7BF549827CAB2D683B2941B06_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (StreamingController_CancelPreloading_mD5ABD57F18813CE7BF549827CAB2D683B2941B06_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.StreamingController::CancelPreloading()");
	_il2cpp_icall_func(__this);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool StreamingController_IsPreloading_m355E060BFA8E75375BD7290AD227370C00E7DDF5 (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF* __this, const RuntimeMethod* method) 
{
	typedef bool (*StreamingController_IsPreloading_m355E060BFA8E75375BD7290AD227370C00E7DDF5_ftn) (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF*);
	static StreamingController_IsPreloading_m355E060BFA8E75375BD7290AD227370C00E7DDF5_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (StreamingController_IsPreloading_m355E060BFA8E75375BD7290AD227370C00E7DDF5_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.StreamingController::IsPreloading()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StreamingController__ctor_m552E70EAB8C02690F934B9CC1D9F1EAD2CCDE798 (StreamingController_t76A8C62300A985A768378800C40A822D427F95DF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StreamingController__ctor_m552E70EAB8C02690F934B9CC1D9F1EAD2CCDE798_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, StreamingController__ctor_m552E70EAB8C02690F934B9CC1D9F1EAD2CCDE798_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Behaviour__ctor_m00422B6EFEA829BCB116D715E74F1EAD2CB6F4F8(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
