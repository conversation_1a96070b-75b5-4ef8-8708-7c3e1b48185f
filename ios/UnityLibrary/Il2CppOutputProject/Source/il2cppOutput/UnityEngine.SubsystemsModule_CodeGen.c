﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void IntegratedSubsystem_SetHandle_m22D17A2E0BD8AF0FC18CE09638572D34AD8DB48B (void);
extern void IntegratedSubsystem_Start_m2F871FE708687A50D8B078522D97C627D344CC34 (void);
extern void IntegratedSubsystem_Stop_m4CFEE7A1C17893728205EAEEF9D5427622C794C4 (void);
extern void IntegratedSubsystem_Destroy_m3D6593DA2CB876877A48778C7D2F782EEC473A08 (void);
extern void IntegratedSubsystem_get_running_m18AA0D7AD1CB593DC9EE5F3DC79643717509D6E8 (void);
extern void IntegratedSubsystem_get_valid_m6537B83457B7E79D9743A0EA9144BD116B0E850B (void);
extern void IntegratedSubsystem_IsRunning_m6AF6106D2DA0A840DBA7D52E9D5F373658B66009 (void);
extern void IntegratedSubsystem__ctor_m8A0E82CAFC72287502DB0158488C98831B2DF405 (void);
extern void SubsystemBindings_DestroySubsystem_m601801D653E84DE619656D06A61357F3C9BC7456 (void);
extern void IntegratedSubsystemDescriptor_get_id_m89DBA940C79ED7EFE1137E3EC4A5A53BF7052F15 (void);
extern void IntegratedSubsystemDescriptor_UnityEngine_ISubsystemDescriptorImpl_get_ptr_mF3B9832D47D43D90DF14889562F096FECFF51198 (void);
extern void IntegratedSubsystemDescriptor_UnityEngine_ISubsystemDescriptorImpl_set_ptr_m9FCF85CDF4E620EA7E163E8C966BF92B7E30D07B (void);
extern void IntegratedSubsystemDescriptor_UnityEngine_ISubsystemDescriptor_Create_m8B6149525628F9DB15BB0651CEE0BE2FCAE1E875 (void);
extern void IntegratedSubsystemDescriptor__ctor_mD10D471BEAB8890C14BF59828EE3C37BCC1538D6 (void);
extern void SubsystemDescriptorBindings_Create_m5C7A196279BEF451109C3BFDC9A4015F4122A6AA (void);
extern void SubsystemDescriptorBindings_GetId_mD3D36C3199EF50AAAB7830E6D061269F4CE956C1 (void);
extern void Subsystem_Destroy_m0CE70A94D9CAD2E4B2528CFE5D9C874FD5BC4759 (void);
extern void Subsystem__ctor_m43AA875446123D3DCF6016748B19934D0141447A (void);
extern void SubsystemDescriptor_get_id_mA4223910997DD72DCF70B03BA5340AFE11AA1A01 (void);
extern void SubsystemDescriptor_set_id_m9EE44E7C8BBF000B1BE2D518620FBBB0F4CB0236 (void);
extern void SubsystemDescriptor_get_subsystemImplementationType_m68EB84474574555DDE319A8D5C6C06C5924FFA45 (void);
extern void SubsystemDescriptor_set_subsystemImplementationType_m3A2FB7296C79FA765F6A6B972B04504D4D3FA359 (void);
extern void SubsystemDescriptor_UnityEngine_ISubsystemDescriptor_Create_m9A9AD52C8D477CFE2F4DEE834C391F4822AC12C9 (void);
extern void SubsystemDescriptor__ctor_m6A1BE1617C93356590A1DE68B9603C83D9A28F05 (void);
extern void Internal_SubsystemDescriptors_Internal_AddDescriptor_m0462E74DADC94897AB6EECD3051878FD6832118E (void);
extern void SubsystemManager_ReloadSubsystemsStarted_mE6BE70BD76D9C294AFC4ABCD9E4A0DF9BB0E4273 (void);
extern void SubsystemManager_ReloadSubsystemsCompleted_m708F6B5CEF21605E49DF6DF6DDA81FE3DA19099B (void);
extern void SubsystemManager_InitializeIntegratedSubsystem_mE3336CB0547C0B2CF195BA2372AFBC99F65AB6F5 (void);
extern void SubsystemManager_ClearSubsystems_m36DD2EBBD868BD00A34CAAFD48BFA4E79D031443 (void);
extern void SubsystemManager_StaticConstructScriptingClassMap_m350147EEDFABFFADB6541B807F1A94337F8E2337 (void);
extern void SubsystemManager_ReportSingleSubsystemAnalytics_mB4B7EB2D5AD555258B03A782B4C7AC8CBF501C66 (void);
extern void SubsystemManager__cctor_m4B044EB8875B225C565E6FB10A2296C0E72BDE2F (void);
extern void SubsystemManager_GetAllSubsystemDescriptors_mF8C2121B8431CD3B7ED6C58593E0C701C2277C9E (void);
extern void SubsystemManager_add_beforeReloadSubsystems_mF3D75EE86BBECC18F404DB3F5E7B9E838DB6E148 (void);
extern void SubsystemManager_remove_beforeReloadSubsystems_mD17B7FE8D8818EA5DC288022E56F2997D78B9BCD (void);
extern void SubsystemManager_add_afterReloadSubsystems_m7680D5F31D40A10A8E574C7538394F2B499DC0D6 (void);
extern void SubsystemManager_remove_afterReloadSubsystems_mA483197D579BC1DED8C1085B0A2A28053FCCEA24 (void);
extern void SubsystemManager_GetIntegratedSubsystemByPtr_mC117FBE03DF764DB78D48B11AD5AC324F223BDE7 (void);
extern void SubsystemManager_RemoveIntegratedSubsystemByPtr_mBFD54C173F757CAF38E897250D05D517D08D4784 (void);
extern void SubsystemManager_AddStandaloneSubsystem_mBEE590C56742D1E540349BB4D225C265A145D722 (void);
extern void SubsystemManager_RemoveStandaloneSubsystem_m61CD473221C4A3210629379D92018723301E982A (void);
extern void SubsystemManager_FindStandaloneSubsystemByDescriptor_m49CCAABC4FB6569333A0D54281EC885C5447BA4D (void);
extern void SubsystemManager_AddDeprecatedSubsystem_mC6604918A68F43B78A009B896E2CD8AF2251AE0C (void);
extern void SubsystemManager_RemoveDeprecatedSubsystem_mBD8928FA7AE2FFC79D2768ED6F707FF61F65092D (void);
extern void SubsystemManager_FindDeprecatedSubsystemByDescriptor_m1FC7CA04E19DE606423B76F493E18DB4C2699522 (void);
extern void SubsystemManager_add_reloadSubsytemsStarted_m01E9BB2C19C1A2A1E2D9583EDBDCBA5451AD644D (void);
extern void SubsystemManager_remove_reloadSubsytemsStarted_m38E0CA3E04CA830016E8F7ABE398912909BEDBD2 (void);
extern void SubsystemManager_add_reloadSubsytemsCompleted_mEDB55CCBB7B7A82656D36E559AC3DAAB20A51545 (void);
extern void SubsystemManager_remove_reloadSubsytemsCompleted_m6826A24F7DA3ABD3ECDF8C6E37BF8F94B4576E19 (void);
extern void SubsystemDescriptorStore_InitializeManagedDescriptor_m74418769D7CDE1CED4F07A179A9173E6F0ECD12C (void);
extern void SubsystemDescriptorStore_ClearManagedDescriptors_m14AEFE442EDDC8BD082F300C7054FE185E1EA1D5 (void);
extern void SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_m12986597FBE76C21232348BAC03ADEEB9F02DB99 (void);
extern void SubsystemDescriptorStore_RegisterDescriptor_m6DEC96199F95ACCD68822060CEB79CA77AF44F12 (void);
extern void SubsystemDescriptorStore_GetAllSubsystemDescriptors_m35BB4A0DEC7400AFFD68571CED01678619588180 (void);
extern void SubsystemDescriptorStore_RegisterDeprecatedDescriptor_m0314598F5A51921C9C9B254B4E82C1191FC4D4B1 (void);
extern void SubsystemDescriptorStore__cctor_mE267CA787BE559BD490D9ED8BD2D55F4E8DE6A14 (void);
extern void SubsystemDescriptorWithProvider_get_id_m9E92FDF45FE9BFB0B28C2AE3EFE475998D01BBC7 (void);
extern void SubsystemDescriptorWithProvider_set_id_m4E93140B46C960FFB4723062C55D05D7B551A57B (void);
extern void SubsystemDescriptorWithProvider_get_providerType_m1ED8F898361B508FFE2A46D05E81CEAC43B040D4 (void);
extern void SubsystemDescriptorWithProvider_set_providerType_m27A630CAFBA225796667E17DE114673056339EA4 (void);
extern void SubsystemDescriptorWithProvider_get_subsystemTypeOverride_mCE84F1611CB09975FBB4BE48E015316ADDB52668 (void);
extern void SubsystemDescriptorWithProvider_set_subsystemTypeOverride_m34C15BBBE4EB41226EF1C0A0C8BB73A0955E0F3E (void);
extern void SubsystemDescriptorWithProvider_UnityEngine_ISubsystemDescriptor_Create_m93889530D53B9658319E5EF704F277AC3B03B336 (void);
extern void SubsystemDescriptorWithProvider__ctor_mADB008B99F8F98EDD0C5AEBE00368F96D11FCCD1 (void);
extern void SubsystemProvider_get_running_m0331B5275B76BA00ACD2635216B455B599E41420 (void);
extern void SubsystemProvider__ctor_mF88170FBB0EBEEB2AB312C10958130058F256038 (void);
extern void SubsystemWithProvider_Start_m720DC3EDB918F58D65CA4B12017D395788934644 (void);
extern void SubsystemWithProvider_Stop_mB22AB4811D2636FCB317C0E54E8A7139D81A8E16 (void);
extern void SubsystemWithProvider_Destroy_m8161D5B71C856F836660430CED8550AA17438BAF (void);
extern void SubsystemWithProvider_get_running_m6BF31FC3BDA38C56C0F60FEA37767A4151B22C44 (void);
extern void SubsystemWithProvider_set_running_mBEF44DA55F99B873A21E2003CDE06981E0348477 (void);
extern void SubsystemWithProvider_get_providerBase_m8229B40F322D44A2E22B0AD62C581D284813410A (void);
extern void SubsystemWithProvider_set_providerBase_m0517BCC9991DC69C2AAF8C60AF96211BB5CAE14C (void);
extern void SubsystemWithProvider__ctor_m7AAC2F13A01D674BF5040F42A08C88611FE60914 (void);
extern void ExampleSubsystem_PrintExample_m0BCFB8B774762F3A235A8426950713920C643E88 (void);
extern void ExampleSubsystem_GetBool_m553467C0BA77618DDFA9E6124F97BDADC452CA6E (void);
extern void ExampleSubsystem__ctor_m776B9F096A61BE0543D40354F7839095E312CF9E (void);
extern void ExampleSubsystemDescriptor_get_supportsEditorMode_mDE1A8B14C70F7CA31480AD3C94E470DF0749D24C (void);
extern void ExampleSubsystemDescriptor_get_disableBackbufferMSAA_m6440A1462FBF303EDBC9D2C6D2A6287ABF77C47D (void);
extern void ExampleSubsystemDescriptor_get_stereoscopicBackbuffer_m023F8DEC1C0F922A2F84B25D8171C303F266ADF9 (void);
extern void ExampleSubsystemDescriptor_get_usePBufferEGL_m6952FCDDC77823D5FBED5DFF3CBFA8FA0D8B9A65 (void);
extern void ExampleSubsystemDescriptor__ctor_m26CC44354EC614129D5B1EA9A99A095C7293A969 (void);
static Il2CppMethodPointer s_methodPointers[150] = 
{
	IntegratedSubsystem_SetHandle_m22D17A2E0BD8AF0FC18CE09638572D34AD8DB48B,
	IntegratedSubsystem_Start_m2F871FE708687A50D8B078522D97C627D344CC34,
	IntegratedSubsystem_Stop_m4CFEE7A1C17893728205EAEEF9D5427622C794C4,
	IntegratedSubsystem_Destroy_m3D6593DA2CB876877A48778C7D2F782EEC473A08,
	IntegratedSubsystem_get_running_m18AA0D7AD1CB593DC9EE5F3DC79643717509D6E8,
	IntegratedSubsystem_get_valid_m6537B83457B7E79D9743A0EA9144BD116B0E850B,
	IntegratedSubsystem_IsRunning_m6AF6106D2DA0A840DBA7D52E9D5F373658B66009,
	IntegratedSubsystem__ctor_m8A0E82CAFC72287502DB0158488C98831B2DF405,
	NULL,
	NULL,
	NULL,
	SubsystemBindings_DestroySubsystem_m601801D653E84DE619656D06A61357F3C9BC7456,
	NULL,
	NULL,
	IntegratedSubsystemDescriptor_get_id_m89DBA940C79ED7EFE1137E3EC4A5A53BF7052F15,
	IntegratedSubsystemDescriptor_UnityEngine_ISubsystemDescriptorImpl_get_ptr_mF3B9832D47D43D90DF14889562F096FECFF51198,
	IntegratedSubsystemDescriptor_UnityEngine_ISubsystemDescriptorImpl_set_ptr_m9FCF85CDF4E620EA7E163E8C966BF92B7E30D07B,
	IntegratedSubsystemDescriptor_UnityEngine_ISubsystemDescriptor_Create_m8B6149525628F9DB15BB0651CEE0BE2FCAE1E875,
	NULL,
	IntegratedSubsystemDescriptor__ctor_mD10D471BEAB8890C14BF59828EE3C37BCC1538D6,
	NULL,
	NULL,
	NULL,
	SubsystemDescriptorBindings_Create_m5C7A196279BEF451109C3BFDC9A4015F4122A6AA,
	SubsystemDescriptorBindings_GetId_mD3D36C3199EF50AAAB7830E6D061269F4CE956C1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Subsystem_Destroy_m0CE70A94D9CAD2E4B2528CFE5D9C874FD5BC4759,
	NULL,
	Subsystem__ctor_m43AA875446123D3DCF6016748B19934D0141447A,
	NULL,
	NULL,
	SubsystemDescriptor_get_id_mA4223910997DD72DCF70B03BA5340AFE11AA1A01,
	SubsystemDescriptor_set_id_m9EE44E7C8BBF000B1BE2D518620FBBB0F4CB0236,
	SubsystemDescriptor_get_subsystemImplementationType_m68EB84474574555DDE319A8D5C6C06C5924FFA45,
	SubsystemDescriptor_set_subsystemImplementationType_m3A2FB7296C79FA765F6A6B972B04504D4D3FA359,
	SubsystemDescriptor_UnityEngine_ISubsystemDescriptor_Create_m9A9AD52C8D477CFE2F4DEE834C391F4822AC12C9,
	NULL,
	SubsystemDescriptor__ctor_m6A1BE1617C93356590A1DE68B9603C83D9A28F05,
	NULL,
	NULL,
	NULL,
	Internal_SubsystemDescriptors_Internal_AddDescriptor_m0462E74DADC94897AB6EECD3051878FD6832118E,
	SubsystemManager_ReloadSubsystemsStarted_mE6BE70BD76D9C294AFC4ABCD9E4A0DF9BB0E4273,
	SubsystemManager_ReloadSubsystemsCompleted_m708F6B5CEF21605E49DF6DF6DDA81FE3DA19099B,
	SubsystemManager_InitializeIntegratedSubsystem_mE3336CB0547C0B2CF195BA2372AFBC99F65AB6F5,
	SubsystemManager_ClearSubsystems_m36DD2EBBD868BD00A34CAAFD48BFA4E79D031443,
	SubsystemManager_StaticConstructScriptingClassMap_m350147EEDFABFFADB6541B807F1A94337F8E2337,
	SubsystemManager_ReportSingleSubsystemAnalytics_mB4B7EB2D5AD555258B03A782B4C7AC8CBF501C66,
	SubsystemManager__cctor_m4B044EB8875B225C565E6FB10A2296C0E72BDE2F,
	SubsystemManager_GetAllSubsystemDescriptors_mF8C2121B8431CD3B7ED6C58593E0C701C2277C9E,
	NULL,
	NULL,
	NULL,
	SubsystemManager_add_beforeReloadSubsystems_mF3D75EE86BBECC18F404DB3F5E7B9E838DB6E148,
	SubsystemManager_remove_beforeReloadSubsystems_mD17B7FE8D8818EA5DC288022E56F2997D78B9BCD,
	SubsystemManager_add_afterReloadSubsystems_m7680D5F31D40A10A8E574C7538394F2B499DC0D6,
	SubsystemManager_remove_afterReloadSubsystems_mA483197D579BC1DED8C1085B0A2A28053FCCEA24,
	SubsystemManager_GetIntegratedSubsystemByPtr_mC117FBE03DF764DB78D48B11AD5AC324F223BDE7,
	SubsystemManager_RemoveIntegratedSubsystemByPtr_mBFD54C173F757CAF38E897250D05D517D08D4784,
	SubsystemManager_AddStandaloneSubsystem_mBEE590C56742D1E540349BB4D225C265A145D722,
	SubsystemManager_RemoveStandaloneSubsystem_m61CD473221C4A3210629379D92018723301E982A,
	SubsystemManager_FindStandaloneSubsystemByDescriptor_m49CCAABC4FB6569333A0D54281EC885C5447BA4D,
	NULL,
	SubsystemManager_AddDeprecatedSubsystem_mC6604918A68F43B78A009B896E2CD8AF2251AE0C,
	SubsystemManager_RemoveDeprecatedSubsystem_mBD8928FA7AE2FFC79D2768ED6F707FF61F65092D,
	SubsystemManager_FindDeprecatedSubsystemByDescriptor_m1FC7CA04E19DE606423B76F493E18DB4C2699522,
	SubsystemManager_add_reloadSubsytemsStarted_m01E9BB2C19C1A2A1E2D9583EDBDCBA5451AD644D,
	SubsystemManager_remove_reloadSubsytemsStarted_m38E0CA3E04CA830016E8F7ABE398912909BEDBD2,
	SubsystemManager_add_reloadSubsytemsCompleted_mEDB55CCBB7B7A82656D36E559AC3DAAB20A51545,
	SubsystemManager_remove_reloadSubsytemsCompleted_m6826A24F7DA3ABD3ECDF8C6E37BF8F94B4576E19,
	SubsystemDescriptorStore_InitializeManagedDescriptor_m74418769D7CDE1CED4F07A179A9173E6F0ECD12C,
	SubsystemDescriptorStore_ClearManagedDescriptors_m14AEFE442EDDC8BD082F300C7054FE185E1EA1D5,
	SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_m12986597FBE76C21232348BAC03ADEEB9F02DB99,
	SubsystemDescriptorStore_RegisterDescriptor_m6DEC96199F95ACCD68822060CEB79CA77AF44F12,
	SubsystemDescriptorStore_GetAllSubsystemDescriptors_m35BB4A0DEC7400AFFD68571CED01678619588180,
	NULL,
	NULL,
	NULL,
	NULL,
	SubsystemDescriptorStore_RegisterDeprecatedDescriptor_m0314598F5A51921C9C9B254B4E82C1191FC4D4B1,
	SubsystemDescriptorStore__cctor_mE267CA787BE559BD490D9ED8BD2D55F4E8DE6A14,
	SubsystemDescriptorWithProvider_get_id_m9E92FDF45FE9BFB0B28C2AE3EFE475998D01BBC7,
	SubsystemDescriptorWithProvider_set_id_m4E93140B46C960FFB4723062C55D05D7B551A57B,
	SubsystemDescriptorWithProvider_get_providerType_m1ED8F898361B508FFE2A46D05E81CEAC43B040D4,
	SubsystemDescriptorWithProvider_set_providerType_m27A630CAFBA225796667E17DE114673056339EA4,
	SubsystemDescriptorWithProvider_get_subsystemTypeOverride_mCE84F1611CB09975FBB4BE48E015316ADDB52668,
	SubsystemDescriptorWithProvider_set_subsystemTypeOverride_m34C15BBBE4EB41226EF1C0A0C8BB73A0955E0F3E,
	NULL,
	SubsystemDescriptorWithProvider_UnityEngine_ISubsystemDescriptor_Create_m93889530D53B9658319E5EF704F277AC3B03B336,
	NULL,
	SubsystemDescriptorWithProvider__ctor_mADB008B99F8F98EDD0C5AEBE00368F96D11FCCD1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SubsystemProvider_get_running_m0331B5275B76BA00ACD2635216B455B599E41420,
	SubsystemProvider__ctor_mF88170FBB0EBEEB2AB312C10958130058F256038,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SubsystemWithProvider_Start_m720DC3EDB918F58D65CA4B12017D395788934644,
	NULL,
	SubsystemWithProvider_Stop_mB22AB4811D2636FCB317C0E54E8A7139D81A8E16,
	NULL,
	SubsystemWithProvider_Destroy_m8161D5B71C856F836660430CED8550AA17438BAF,
	NULL,
	SubsystemWithProvider_get_running_m6BF31FC3BDA38C56C0F60FEA37767A4151B22C44,
	SubsystemWithProvider_set_running_mBEF44DA55F99B873A21E2003CDE06981E0348477,
	SubsystemWithProvider_get_providerBase_m8229B40F322D44A2E22B0AD62C581D284813410A,
	SubsystemWithProvider_set_providerBase_m0517BCC9991DC69C2AAF8C60AF96211BB5CAE14C,
	NULL,
	NULL,
	SubsystemWithProvider__ctor_m7AAC2F13A01D674BF5040F42A08C88611FE60914,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ExampleSubsystem_PrintExample_m0BCFB8B774762F3A235A8426950713920C643E88,
	ExampleSubsystem_GetBool_m553467C0BA77618DDFA9E6124F97BDADC452CA6E,
	ExampleSubsystem__ctor_m776B9F096A61BE0543D40354F7839095E312CF9E,
	ExampleSubsystemDescriptor_get_supportsEditorMode_mDE1A8B14C70F7CA31480AD3C94E470DF0749D24C,
	ExampleSubsystemDescriptor_get_disableBackbufferMSAA_m6440A1462FBF303EDBC9D2C6D2A6287ABF77C47D,
	ExampleSubsystemDescriptor_get_stereoscopicBackbuffer_m023F8DEC1C0F922A2F84B25D8171C303F266ADF9,
	ExampleSubsystemDescriptor_get_usePBufferEGL_m6952FCDDC77823D5FBED5DFF3CBFA8FA0D8B9A65,
	ExampleSubsystemDescriptor__ctor_m26CC44354EC614129D5B1EA9A99A095C7293A969,
};
static const int32_t s_InvokerIndices[150] = 
{
	3881,
	4364,
	4364,
	4364,
	4168,
	4168,
	4168,
	4364,
	0,
	0,
	0,
	8882,
	0,
	0,
	4250,
	4218,
	3854,
	4250,
	0,
	4364,
	0,
	0,
	0,
	8444,
	8503,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	0,
	4364,
	0,
	0,
	4250,
	3881,
	4250,
	3881,
	4250,
	0,
	4364,
	0,
	0,
	0,
	8887,
	9089,
	9089,
	7949,
	9089,
	9089,
	8887,
	9089,
	8887,
	0,
	0,
	0,
	8887,
	8887,
	8887,
	8887,
	8503,
	8882,
	8887,
	8220,
	8505,
	0,
	8887,
	8220,
	8505,
	8887,
	8887,
	8887,
	8887,
	7949,
	9089,
	8887,
	8887,
	8887,
	0,
	0,
	0,
	0,
	8887,
	9089,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	0,
	4250,
	0,
	4364,
	0,
	0,
	0,
	0,
	0,
	4168,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	0,
	4364,
	0,
	4364,
	0,
	4168,
	3807,
	4250,
	3881,
	0,
	0,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	4168,
	4364,
	4168,
	4168,
	4168,
	4168,
	4364,
};
static const Il2CppTokenRangePair s_rgctxIndices[17] = 
{
	{ 0x02000003, { 0, 2 } },
	{ 0x02000007, { 2, 2 } },
	{ 0x0200000C, { 4, 1 } },
	{ 0x0200000E, { 5, 2 } },
	{ 0x02000013, { 60, 9 } },
	{ 0x02000016, { 69, 4 } },
	{ 0x02000018, { 73, 12 } },
	{ 0x0600003B, { 7, 2 } },
	{ 0x0600003C, { 9, 5 } },
	{ 0x0600003D, { 14, 11 } },
	{ 0x06000047, { 25, 2 } },
	{ 0x06000054, { 27, 8 } },
	{ 0x06000055, { 35, 5 } },
	{ 0x06000056, { 40, 11 } },
	{ 0x06000057, { 51, 9 } },
	{ 0x0600008D, { 85, 5 } },
	{ 0x0600008E, { 90, 3 } },
};
extern const uint32_t g_rgctx_TSubsystemDescriptor_t59C3F2B01B7FD365079268A90FA6EFF650F91FD9;
extern const uint32_t g_rgctx_IntegratedSubsystem_1_get_subsystemDescriptor_m0F5F562F4FC1F45020225C5B959D63C7D0B41815;
extern const uint32_t g_rgctx_IntegratedSubsystemDescriptor_1_Create_m5973E509BE85C79B944AADEE93FD6D6E80940FE8;
extern const uint32_t g_rgctx_TSubsystem_t75D38629E5B58BB4C8423B2F494B75E5F4509F1B;
extern const uint32_t g_rgctx_TSubsystemDescriptor_tD89706A27E29A624570EDBEDBD7976D383A4B121;
extern const uint32_t g_rgctx_SubsystemDescriptor_1_Create_mDD43AAF753325CF1666EC772B90E7CA2E02A7918;
extern const uint32_t g_rgctx_TSubsystem_tCD8EA1955E1BB69D300C24C9B632BA316E2ECA39;
extern const uint32_t g_rgctx_List_1_t756D87A29C46D7F54BCF78323C3BE01F1DF1D882;
extern const uint32_t g_rgctx_SubsystemDescriptorStore_GetSubsystemDescriptors_TisT_tC1C7E91EE3BF9F5F6CB6AEF844014EB2DFE9C857_m72893DE58B7F09F8306FD53372F4943E4D1A6235;
extern const uint32_t g_rgctx_List_1_t4D4A4160AD6A5AF7BE63E6C9DDD4B2E4E331E8AD;
extern const uint32_t g_rgctx_List_1_Clear_m263E9405C711E2BF06E72D12C60C6DB44FD83E60;
extern const uint32_t g_rgctx_SubsystemManager_AddSubsystemSubset_TisIntegratedSubsystem_t990160A89854D87C0836DC589B720231C02D4CE3_TisT_tDA8CF8DB5D445E5A89CD2DFBC2B9ED34BCB9530F_mF59FD68FEE783E0B41376FE76BBC1FFF6272B9BD;
extern const uint32_t g_rgctx_SubsystemManager_AddSubsystemSubset_TisSubsystemWithProvider_tC72E35EE2D413A4B0635B058154BABF265F31242_TisT_tDA8CF8DB5D445E5A89CD2DFBC2B9ED34BCB9530F_mA9B9EA7A26F0BD794305C36F90D912FB09D13AF4;
extern const uint32_t g_rgctx_SubsystemManager_AddSubsystemSubset_TisSubsystem_t5E67EE95D848FB950AD5D76325BF8959A6F7C7D7_TisT_tDA8CF8DB5D445E5A89CD2DFBC2B9ED34BCB9530F_mF47B5DDB1454D28185458784A64E775F429B6323;
extern const uint32_t g_rgctx_List_1_t42EEE63217C3960E15AECB9C4C3CE5EECCCBAF1C;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m013F5A56064946CBF17910146C227DA4584E88CF;
extern const uint32_t g_rgctx_Enumerator_tF63BE6DA4E12FC2CCE9C20921649B1AB2D81E411;
extern const uint32_t g_rgctx_Enumerator_get_Current_mDCAF49959FBCA1A3A11C7A5BA8D6426D1D288AEA;
extern const uint32_t g_rgctx_Enumerator_tF63BE6DA4E12FC2CCE9C20921649B1AB2D81E411;
extern const uint32_t g_rgctx_TBaseTypeInList_tF0CE0747D2F83FC3C38B2F057083EA0C63AC7C61;
extern const uint32_t g_rgctx_TQueryType_tFAA6B5F209223336BB243BD708D59C43AF4A259B;
extern const uint32_t g_rgctx_List_1_tC05347E9439A32B87668F876D0BCE11C446FACD1;
extern const uint32_t g_rgctx_List_1_Add_m63BEFC493BABD8C0591479AB80B81127382FFA32;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m022E697D4613667FD4ABF9B1520381349E39C238;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_tF63BE6DA4E12FC2CCE9C20921649B1AB2D81E411_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_t8ED80B742EB7A5E3A6396F80FD40C76081B74A4A;
extern const uint32_t g_rgctx_SubsystemManager_GetSubsystems_TisT_tA2BB34A5254F7B52F2C218C16C9A0455942FC0F5_m53382D779438B0D7A18B1E6338DAD0F52008E1A4;
extern const uint32_t g_rgctx_List_1_tB360E54B7FE9E55C990390BE67AE96B3062EFC38;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m31F90EF4F63E9919A4626D527D11A873ACB9D625;
extern const uint32_t g_rgctx_Enumerator_tDC1C119BDA1A46BF418668E6053CC1367154D8EC;
extern const uint32_t g_rgctx_Enumerator_get_Current_mE6D82FDE699412A45447D078690B78B8E37B6770;
extern const uint32_t g_rgctx_Enumerator_tDC1C119BDA1A46BF418668E6053CC1367154D8EC;
extern const uint32_t g_rgctx_TBaseTypeInList_t6B296E03C9733AAB5FD48E480803C32A13943B18;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mFB5A023F2BD8E73C938D8DB9E0901751D8FD70AD;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_tDC1C119BDA1A46BF418668E6053CC1367154D8EC_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_t5DD7719E13F31B5BF45151BEF17DF32705776E96;
extern const uint32_t g_rgctx_List_1_Clear_m2ED0E9FF8A6F9DA2FCB64A1B7B67B060A69F2E7A;
extern const uint32_t g_rgctx_SubsystemDescriptorStore_AddDescriptorSubset_TisIntegratedSubsystemDescriptor_t9232963B842E01748A8E032928DC8E35DF00C10D_TisT_t8393FFE949DF35B57AF7F85C46E8FE04A4D1BC1D_m3F4D63B5BC268B392F17036808985F63F5F2C6C2;
extern const uint32_t g_rgctx_SubsystemDescriptorStore_AddDescriptorSubset_TisSubsystemDescriptorWithProvider_t2A61A2C951A4A179E898CF207726BF6B5AF474D5_TisT_t8393FFE949DF35B57AF7F85C46E8FE04A4D1BC1D_m8C4E5C9AE7B8C2A498182BF5DF215A096FD695C3;
extern const uint32_t g_rgctx_SubsystemDescriptorStore_AddDescriptorSubset_TisSubsystemDescriptor_tF417D2751C69A8B0DD86162EBCE55F84D3493A71_TisT_t8393FFE949DF35B57AF7F85C46E8FE04A4D1BC1D_m3BEA957385786DE08839E946372F2CB93B7048AD;
extern const uint32_t g_rgctx_List_1_tE0797342B110AB0E9D72D4788ED19F64A1DE9EBA;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m212AA0FEE9AE6BB13A66BB3104475FA4C79D9D45;
extern const uint32_t g_rgctx_Enumerator_t59742E2E436020EA1188EAEE284400369AA3BB49;
extern const uint32_t g_rgctx_Enumerator_get_Current_mB7CD02B2C967839A425B30DA07FD1187386B9CFC;
extern const uint32_t g_rgctx_Enumerator_t59742E2E436020EA1188EAEE284400369AA3BB49;
extern const uint32_t g_rgctx_TBaseTypeInList_t930C0978E39F5B15CECB5CC7730F5B65BD9B9322;
extern const uint32_t g_rgctx_TQueryType_t16C44BD3C52B486C42190E646CFB733F55507ACE;
extern const uint32_t g_rgctx_List_1_tDF6932143B86526C19E7D779CB4E0D6240F2DE7E;
extern const uint32_t g_rgctx_List_1_Add_m6015040261A90577C90B29E397387D54748DEDE1;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m50F59023C82A4F110236C5236EFCA7D7AEB92192;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t59742E2E436020EA1188EAEE284400369AA3BB49_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_t4FD62234F80546ED54378254D3B222948841B4AF;
extern const uint32_t g_rgctx_List_1_get_Item_m3935F8C510B45D47FD1F8DFEEE467CE30B145BF4;
extern const uint32_t g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54;
extern const Il2CppRGCTXConstrainedData g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2;
extern const uint32_t g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2;
extern const Il2CppRGCTXConstrainedData g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2;
extern const uint32_t g_rgctx_List_1_set_Item_m58B49CD878D3DC099EB422A63AB014DF5B1CAE06;
extern const uint32_t g_rgctx_List_1_get_Count_mC3BAEE88ADF85DEC0CA10B97B3E7F42183A8AFBB;
extern const uint32_t g_rgctx_List_1_Add_m99E6BCF557EB89898061B9B8761AA6D402DD2592;
extern const uint32_t g_rgctx_SubsystemDescriptorWithProvider_2_Create_mE96E1F2AD40979B2C91EC9D080946E2A3A48D324;
extern const uint32_t g_rgctx_TSubsystem_tFEF59D33BFAE8ED0C7660B9A5607CBC41D40EB17;
extern const uint32_t g_rgctx_SubsystemDescriptorWithProvider_2_CreateProvider_m9D8B67223F0C8D7536608EE7AD2A576349DAAD01;
extern const uint32_t g_rgctx_TProvider_t412C07C5CA397BDD5FEA619F41E40943007B2E5C;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTSubsystem_tFEF59D33BFAE8ED0C7660B9A5607CBC41D40EB17_m8128AA859C349DC4FDCAE68C588C23EC24AF9A6A;
extern const uint32_t g_rgctx_TProvider_t412C07C5CA397BDD5FEA619F41E40943007B2E5C;
extern const uint32_t g_rgctx_TSubsystem_tFEF59D33BFAE8ED0C7660B9A5607CBC41D40EB17;
extern const uint32_t g_rgctx_SubsystemProvider_1_t828568D5769B3C44908B542C579715BC74A8E1FA;
extern const uint32_t g_rgctx_SubsystemProvider_1_TryInitialize_mBA9781715FC29F138885D552F90FD66CD811DAE2;
extern const uint32_t g_rgctx_SubsystemProxy_2_tFC61C103562F473F7C77CF932CCB996C2D7F08EF;
extern const uint32_t g_rgctx_TProvider_t3F60C11B8965F8B4A8CDC40F66380215A6440008;
extern const uint32_t g_rgctx_SubsystemProxy_2_get_provider_m58A4F160508D72F94D571579208CB489A5CD6946;
extern const uint32_t g_rgctx_SubsystemProxy_2_set_provider_m3BD09F07B62128D670FCC0C7E5AFEB9774BF6D7A;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_t22ADF9793EA3BE343C3D4A23FFBBCD4160380E74;
extern const uint32_t g_rgctx_TSubsystemDescriptor_t3279EFDEAF6C7C6A8DCA649532C3277C3180C10B;
extern const uint32_t g_rgctx_TProvider_t1AC2F7520C38FC65F96522C997CFF0CCB5951109;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_get_provider_m8441E6EFF377241FF571B1445B2D4D602A8A6EBE;
extern const uint32_t g_rgctx_SubsystemProvider_1_tAC31BED92DB2621C801F0EB0556F5A0FD7A2F2E6;
extern const uint32_t g_rgctx_SubsystemProvider_1_Start_m05AADE0906FB4BABAF76E6C61F518B016F351E04;
extern const uint32_t g_rgctx_SubsystemProvider_1_Stop_m07C601F197FC2962E30F31F26353A9DDCD56EE33;
extern const uint32_t g_rgctx_SubsystemProvider_1_Destroy_m763418CCA180781E54401C8224963B9F4F73BA7B;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_set_provider_m139E8A8E8AF681CD5C34370267B921DB0D4747C1;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_set_subsystemDescriptor_mD7D6B9847D664E1100AA9467FAAF82E101CB8E0C;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_OnCreate_mD74283A12D5D4FC397358732B086A5FDCD49C8B0;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_get_subsystemDescriptor_mCA2535C01B55FA21DBFE8D30A7A7F0BB1FB8A7A9;
extern const uint32_t g_rgctx_SubsystemDescriptorWithProvider_2_t6E86A84B39497354D478693C3373E6D229140F76;
extern const uint32_t g_rgctx_SubsystemDescriptorWithProvider_2_CreateProvider_mA5A283DF67AF1B62B8AB88C5BB6AD9A142FDFFEF;
extern const uint32_t g_rgctx_TProvider_tFFDC38B4C853A29A834233C81E239548473630CE;
extern const uint32_t g_rgctx_SubsystemProxy_2_t990101CF757C8B847EE38A04CCCCDA55CEC7848C;
extern const uint32_t g_rgctx_SubsystemProxy_2__ctor_mDE625671F996E079CEC0AFE5AEFA2E158BD8BD12;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_tAA3476904ABD95E3428467B80816455726DDFDA8;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_get_provider_m6A72568E69504EFA49E8F07D31491FB48005F4CE;
extern const uint32_t g_rgctx_TProvider_t7665A5D11121265B6ECF4818F06ADA64B64A7ED4;
static const Il2CppRGCTXDefinition s_rgctxValues[93] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystemDescriptor_t59C3F2B01B7FD365079268A90FA6EFF650F91FD9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntegratedSubsystem_1_get_subsystemDescriptor_m0F5F562F4FC1F45020225C5B959D63C7D0B41815 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntegratedSubsystemDescriptor_1_Create_m5973E509BE85C79B944AADEE93FD6D6E80940FE8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystem_t75D38629E5B58BB4C8423B2F494B75E5F4509F1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystemDescriptor_tD89706A27E29A624570EDBEDBD7976D383A4B121 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptor_1_Create_mDD43AAF753325CF1666EC772B90E7CA2E02A7918 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystem_tCD8EA1955E1BB69D300C24C9B632BA316E2ECA39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t756D87A29C46D7F54BCF78323C3BE01F1DF1D882 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptorStore_GetSubsystemDescriptors_TisT_tC1C7E91EE3BF9F5F6CB6AEF844014EB2DFE9C857_m72893DE58B7F09F8306FD53372F4943E4D1A6235 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t4D4A4160AD6A5AF7BE63E6C9DDD4B2E4E331E8AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m263E9405C711E2BF06E72D12C60C6DB44FD83E60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemManager_AddSubsystemSubset_TisIntegratedSubsystem_t990160A89854D87C0836DC589B720231C02D4CE3_TisT_tDA8CF8DB5D445E5A89CD2DFBC2B9ED34BCB9530F_mF59FD68FEE783E0B41376FE76BBC1FFF6272B9BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemManager_AddSubsystemSubset_TisSubsystemWithProvider_tC72E35EE2D413A4B0635B058154BABF265F31242_TisT_tDA8CF8DB5D445E5A89CD2DFBC2B9ED34BCB9530F_mA9B9EA7A26F0BD794305C36F90D912FB09D13AF4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemManager_AddSubsystemSubset_TisSubsystem_t5E67EE95D848FB950AD5D76325BF8959A6F7C7D7_TisT_tDA8CF8DB5D445E5A89CD2DFBC2B9ED34BCB9530F_mF47B5DDB1454D28185458784A64E775F429B6323 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t42EEE63217C3960E15AECB9C4C3CE5EECCCBAF1C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m013F5A56064946CBF17910146C227DA4584E88CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tF63BE6DA4E12FC2CCE9C20921649B1AB2D81E411 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mDCAF49959FBCA1A3A11C7A5BA8D6426D1D288AEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tF63BE6DA4E12FC2CCE9C20921649B1AB2D81E411 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TBaseTypeInList_tF0CE0747D2F83FC3C38B2F057083EA0C63AC7C61 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TQueryType_tFAA6B5F209223336BB243BD708D59C43AF4A259B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tC05347E9439A32B87668F876D0BCE11C446FACD1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m63BEFC493BABD8C0591479AB80B81127382FFA32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m022E697D4613667FD4ABF9B1520381349E39C238 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_tF63BE6DA4E12FC2CCE9C20921649B1AB2D81E411_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t8ED80B742EB7A5E3A6396F80FD40C76081B74A4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemManager_GetSubsystems_TisT_tA2BB34A5254F7B52F2C218C16C9A0455942FC0F5_m53382D779438B0D7A18B1E6338DAD0F52008E1A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tB360E54B7FE9E55C990390BE67AE96B3062EFC38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m31F90EF4F63E9919A4626D527D11A873ACB9D625 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tDC1C119BDA1A46BF418668E6053CC1367154D8EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mE6D82FDE699412A45447D078690B78B8E37B6770 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tDC1C119BDA1A46BF418668E6053CC1367154D8EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TBaseTypeInList_t6B296E03C9733AAB5FD48E480803C32A13943B18 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mFB5A023F2BD8E73C938D8DB9E0901751D8FD70AD },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_tDC1C119BDA1A46BF418668E6053CC1367154D8EC_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t5DD7719E13F31B5BF45151BEF17DF32705776E96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m2ED0E9FF8A6F9DA2FCB64A1B7B67B060A69F2E7A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptorStore_AddDescriptorSubset_TisIntegratedSubsystemDescriptor_t9232963B842E01748A8E032928DC8E35DF00C10D_TisT_t8393FFE949DF35B57AF7F85C46E8FE04A4D1BC1D_m3F4D63B5BC268B392F17036808985F63F5F2C6C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptorStore_AddDescriptorSubset_TisSubsystemDescriptorWithProvider_t2A61A2C951A4A179E898CF207726BF6B5AF474D5_TisT_t8393FFE949DF35B57AF7F85C46E8FE04A4D1BC1D_m8C4E5C9AE7B8C2A498182BF5DF215A096FD695C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptorStore_AddDescriptorSubset_TisSubsystemDescriptor_tF417D2751C69A8B0DD86162EBCE55F84D3493A71_TisT_t8393FFE949DF35B57AF7F85C46E8FE04A4D1BC1D_m3BEA957385786DE08839E946372F2CB93B7048AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tE0797342B110AB0E9D72D4788ED19F64A1DE9EBA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m212AA0FEE9AE6BB13A66BB3104475FA4C79D9D45 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t59742E2E436020EA1188EAEE284400369AA3BB49 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mB7CD02B2C967839A425B30DA07FD1187386B9CFC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t59742E2E436020EA1188EAEE284400369AA3BB49 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TBaseTypeInList_t930C0978E39F5B15CECB5CC7730F5B65BD9B9322 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TQueryType_t16C44BD3C52B486C42190E646CFB733F55507ACE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tDF6932143B86526C19E7D779CB4E0D6240F2DE7E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m6015040261A90577C90B29E397387D54748DEDE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m50F59023C82A4F110236C5236EFCA7D7AEB92192 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t59742E2E436020EA1188EAEE284400369AA3BB49_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t4FD62234F80546ED54378254D3B222948841B4AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m3935F8C510B45D47FD1F8DFEEE467CE30B145BF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m58B49CD878D3DC099EB422A63AB014DF5B1CAE06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mC3BAEE88ADF85DEC0CA10B97B3E7F42183A8AFBB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m99E6BCF557EB89898061B9B8761AA6D402DD2592 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptorWithProvider_2_Create_mE96E1F2AD40979B2C91EC9D080946E2A3A48D324 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystem_tFEF59D33BFAE8ED0C7660B9A5607CBC41D40EB17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptorWithProvider_2_CreateProvider_m9D8B67223F0C8D7536608EE7AD2A576349DAAD01 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProvider_t412C07C5CA397BDD5FEA619F41E40943007B2E5C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTSubsystem_tFEF59D33BFAE8ED0C7660B9A5607CBC41D40EB17_m8128AA859C349DC4FDCAE68C588C23EC24AF9A6A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TProvider_t412C07C5CA397BDD5FEA619F41E40943007B2E5C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TSubsystem_tFEF59D33BFAE8ED0C7660B9A5607CBC41D40EB17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemProvider_1_t828568D5769B3C44908B542C579715BC74A8E1FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemProvider_1_TryInitialize_mBA9781715FC29F138885D552F90FD66CD811DAE2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemProxy_2_tFC61C103562F473F7C77CF932CCB996C2D7F08EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProvider_t3F60C11B8965F8B4A8CDC40F66380215A6440008 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemProxy_2_get_provider_m58A4F160508D72F94D571579208CB489A5CD6946 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemProxy_2_set_provider_m3BD09F07B62128D670FCC0C7E5AFEB9774BF6D7A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemWithProvider_3_t22ADF9793EA3BE343C3D4A23FFBBCD4160380E74 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystemDescriptor_t3279EFDEAF6C7C6A8DCA649532C3277C3180C10B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProvider_t1AC2F7520C38FC65F96522C997CFF0CCB5951109 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3_get_provider_m8441E6EFF377241FF571B1445B2D4D602A8A6EBE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemProvider_1_tAC31BED92DB2621C801F0EB0556F5A0FD7A2F2E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemProvider_1_Start_m05AADE0906FB4BABAF76E6C61F518B016F351E04 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemProvider_1_Stop_m07C601F197FC2962E30F31F26353A9DDCD56EE33 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemProvider_1_Destroy_m763418CCA180781E54401C8224963B9F4F73BA7B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3_set_provider_m139E8A8E8AF681CD5C34370267B921DB0D4747C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3_set_subsystemDescriptor_mD7D6B9847D664E1100AA9467FAAF82E101CB8E0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3_OnCreate_mD74283A12D5D4FC397358732B086A5FDCD49C8B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3_get_subsystemDescriptor_mCA2535C01B55FA21DBFE8D30A7A7F0BB1FB8A7A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemDescriptorWithProvider_2_t6E86A84B39497354D478693C3373E6D229140F76 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemDescriptorWithProvider_2_CreateProvider_mA5A283DF67AF1B62B8AB88C5BB6AD9A142FDFFEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProvider_tFFDC38B4C853A29A834233C81E239548473630CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemProxy_2_t990101CF757C8B847EE38A04CCCCDA55CEC7848C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemProxy_2__ctor_mDE625671F996E079CEC0AFE5AEFA2E158BD8BD12 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemWithProvider_3_tAA3476904ABD95E3428467B80816455726DDFDA8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3_get_provider_m6A72568E69504EFA49E8F07D31491FB48005F4CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TProvider_t7665A5D11121265B6ECF4818F06ADA64B64A7ED4 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_SubsystemsModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule = 
{
	"UnityEngine.SubsystemsModule.dll",
	150,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	17,
	s_rgctxIndices,
	93,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_SubsystemsModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
