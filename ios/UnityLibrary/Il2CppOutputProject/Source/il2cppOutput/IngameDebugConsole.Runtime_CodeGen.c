﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m4ED3E5C725AE009B6843D39D0FDE87099305C31E (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mBEC466B1723A6F5C322C0264E18E857250E638DF (void);
extern void ConsoleMethodAttribute_get_Command_m5891B27830AD8D24C0BEC16F9B30F440EBB49E82 (void);
extern void ConsoleMethodAttribute_get_Description_mFC1AAFE7BDA0B06A1C7AB0C9B02FD228177F7F5E (void);
extern void ConsoleMethodAttribute_get_ParameterNames_m254994229F47A4A9C4E8D8AE92AB305A8B7FCCEB (void);
extern void ConsoleMethodAttribute__ctor_m14190D9D09BD98E2F46AA89C7B10EC8B75DA1A50 (void);
extern void ConsoleMethodInfo__ctor_mD64937BD94F186B6418DEF8B8AF8D36AADD59175 (void);
extern void ConsoleMethodInfo_IsValid_m9F0346F09F2E31E1F774FC741BD75AEE58BF18D7 (void);
extern void DebugLogConsole_add_OnCommandExecuted_m8E0A1EBEF144441FEEA84FCFC04C88878EF709A7 (void);
extern void DebugLogConsole_remove_OnCommandExecuted_m395622431A5061473321CA60FF6D2D4FC3F43175 (void);
extern void DebugLogConsole__cctor_m75A152ACAB9CFE5C1E44304E03114D1B6DB0386A (void);
extern void DebugLogConsole_SearchAssemblyForConsoleMethods_m79A4694CEECE4C827D5FF08C6AA333C02DBF9C71 (void);
extern void DebugLogConsole_GetAllCommands_m20956F3B02C815D858DD93154A09740AD3F55397 (void);
extern void DebugLogConsole_LogAllCommands_m978E37DE77B64B1BFEBEAE76A009FC8B6869E1EC (void);
extern void DebugLogConsole_LogAllCommandsWithName_mE66120EA06927650B2D14A4DC9291C9FD34F4B2D (void);
extern void DebugLogConsole_LogSystemInfo_mB8DFBFE050EC55468EDF6106D49D060C5E2AB622 (void);
extern void DebugLogConsole_AppendSysInfoIfPresent_m8348C123A80619F01E787BC5F6DF69577A53B556 (void);
extern void DebugLogConsole_AppendSysInfoIfPresent_mE33F854781092A2E4B3A1301DADA8A99C9FF1537 (void);
extern void DebugLogConsole_AddCustomParameterType_m7725AC531D2A3583F6BE80BB92A716ACD766F3FA (void);
extern void DebugLogConsole_RemoveCustomParameterType_m2835B8990F7DD01009446DF14B4417E48579968B (void);
extern void DebugLogConsole_AddCommandInstance_m460403F17EA9E045FB3CCE3739A1A066E91FAC33 (void);
extern void DebugLogConsole_AddCommandStatic_mA25C3DE5B2273715EAE7B7EA643F2B27E4E7EF80 (void);
extern void DebugLogConsole_AddCommand_m44EA1C3BC10905406AAED2B25AC713C0E076A70F (void);
extern void DebugLogConsole_AddCommand_m8DC230B10D47E9279F8E0581B3D450A68B03D3BC (void);
extern void DebugLogConsole_AddCommand_m68F24524C503875268CF3475FFDECC1D96D6DEC4 (void);
extern void DebugLogConsole_AddCommand_m4BFF08D8BF1C63A4282EB1B5FFF95E695A0C4FD9 (void);
extern void DebugLogConsole_AddCommand_m3D56A91D2615526245C2996E0D97AE636B6EDFAB (void);
extern void DebugLogConsole_RemoveCommand_m7E9D585B16ADA53505185CF20D433A02EE9670E8 (void);
extern void DebugLogConsole_RemoveCommand_m8FC42072669FFB7EF73C84595FAE4EBB94C2058F (void);
extern void DebugLogConsole_RemoveCommand_mCB959C90BB894AC0413C56D67856B51D4B8FAC12 (void);
extern void DebugLogConsole_RemoveCommand_m5B5D9DBA606BD7CF700A12DCDEED6F3CA7ABC90A (void);
extern void DebugLogConsole_GetAutoCompleteCommand_m87F49D25747A4C04B48BDF3AC939863E27A0778E (void);
extern void DebugLogConsole_ExecuteCommand_m09EFF4CAD43DDF1822589BCA5466F2BF3B02CD21 (void);
extern void DebugLogConsole_FetchArgumentsFromCommand_mC1BAA30A6C63276103CEE04B898336A8900FDCAF (void);
extern void DebugLogConsole_FindCommands_m33C4DDBF9B312C69EED77A268C8BBFB218F51146 (void);
extern void DebugLogConsole_GetCommandSuggestions_mD9A1F9ECB81527BDD14183214F668B8C6AA0DCBF (void);
extern void DebugLogConsole_IndexOfDelimiterGroup_m0CC9FFF8192FA903465847E9B51D5D7B6A826F29 (void);
extern void DebugLogConsole_IndexOfDelimiterGroupEnd_mA3CF7EEF7D82521E9173011822619D159E404C72 (void);
extern void DebugLogConsole_IndexOfChar_m4C1A446342AA125173F57AAED37E9445C8C9CD37 (void);
extern void DebugLogConsole_FindCommandIndex_m937912A34A602748887E50AE2A6965A125E420A7 (void);
extern void DebugLogConsole_IsSupportedArrayType_mCF63052024BD4D95564AD4E395C558E99092BB86 (void);
extern void DebugLogConsole_GetTypeReadableName_mD536CA0D3C2534C91B7BA1096C1F09B8D4E83934 (void);
extern void DebugLogConsole_ParseArgument_mD38752180CA3F7F281044CFAA47E4CD34597CC24 (void);
extern void DebugLogConsole_ParseString_mC6A889D46770639BE9916E34C01111BDA20B4195 (void);
extern void DebugLogConsole_ParseBool_mEF25361C5A16EA42E637C9A5C4CAAA9B69FE6C93 (void);
extern void DebugLogConsole_ParseInt_m89E83041C6A5BBE5D15158F8463E7791967D99DA (void);
extern void DebugLogConsole_ParseUInt_m1DC3574D7B9A04530134BFFBDBAC11F4638F5C04 (void);
extern void DebugLogConsole_ParseLong_m931B8028A8F8FA1FB4DC8BDFFCE46D640329BCBA (void);
extern void DebugLogConsole_ParseULong_mE10F56112556D7CA1DDEA4AA6419A4D81DE2E5C9 (void);
extern void DebugLogConsole_ParseByte_mDCD41E40AF9A49B3000FB4F9CA3476AC28E4C172 (void);
extern void DebugLogConsole_ParseSByte_mB24E4FD123682684CED79E98F16BEC0C72575B10 (void);
extern void DebugLogConsole_ParseShort_m5F4567CA60623F25C9EB8470354C5C2844A715F1 (void);
extern void DebugLogConsole_ParseUShort_m5063AAAD632520CCAE05D2183F8C6EC36F5789DC (void);
extern void DebugLogConsole_ParseChar_mE26B50B4C917AC6EA436AE99B798EC443A3C61BC (void);
extern void DebugLogConsole_ParseFloat_m1D8D2B2D33A52F4564830459382BA43F178EFDD8 (void);
extern void DebugLogConsole_ParseDouble_mE0E87D6FA7478B20157C50AB3CC6FA3B1D2AEA94 (void);
extern void DebugLogConsole_ParseDecimal_m474453B063948DD000A23560EFFAC050937AF2AA (void);
extern void DebugLogConsole_ParseVector2_m7C7FA8F64A90CC149A78E4F53E23101F4320EF17 (void);
extern void DebugLogConsole_ParseVector3_m1180FCC0F42A09E6A8436E2864EB376CE2317373 (void);
extern void DebugLogConsole_ParseVector4_mD631138EB5D4E11480D70BE1BC91E717E5E26CA6 (void);
extern void DebugLogConsole_ParseQuaternion_mD00B4033687BC4E5FB517201982EE7011CB5CED6 (void);
extern void DebugLogConsole_ParseColor_m143BF87018F4A2B5D6F6E788457C66114F6B12EC (void);
extern void DebugLogConsole_ParseColor32_mB7E40A40CF0B3BF6360DF08350938199400F71BF (void);
extern void DebugLogConsole_ParseRect_m387D9CDCD3040CEB10B7C96FB7D5BE8EED3CA591 (void);
extern void DebugLogConsole_ParseRectOffset_m72AF3CF248A32FA772AB4D657C9A8A7A34E686CB (void);
extern void DebugLogConsole_ParseBounds_mDCA2A9708F851390931A57FF8E5FB40A2AA627B9 (void);
extern void DebugLogConsole_ParseVector2Int_m3649B9BC937B4664ACC7CA2F0200C63FBB25072E (void);
extern void DebugLogConsole_ParseVector3Int_mD25E0226F9D50907FA52B655D84A9BFE58F28304 (void);
extern void DebugLogConsole_ParseRectInt_m872D02399F3D0E1AB96EA4C5E7ECEC7A646AE975 (void);
extern void DebugLogConsole_ParseBoundsInt_m530D51F98125A1FBAA8A063D1A3085FDA6CF0F7C (void);
extern void DebugLogConsole_ParseGameObject_mF3CB80573688D1F357974DD9D979099F3FFF4466 (void);
extern void DebugLogConsole_ParseComponent_m9D6051721A1FB1D1A1AAC2F28E029003B0807833 (void);
extern void DebugLogConsole_ParseEnum_m3E539F2030843B5E70454E1DE9EA3EFFDA570CAE (void);
extern void DebugLogConsole_ParseArray_m447E48C65E5900282AC24CA15886EB383929E04F (void);
extern void DebugLogConsole_ParseVector_m02B35A42C6167A4F80F99869A134FAF667E90FFD (void);
extern void ParseFunction__ctor_m6660320276B797D09EC7D69B6AB268429D3B4B25 (void);
extern void ParseFunction_Invoke_m6559BD38F8E131515CE959A7FC6D7EDE407794C1 (void);
extern void ParseFunction_BeginInvoke_m39B29B3F54DD95ABB11FC726B6A4794D40D9252F (void);
extern void ParseFunction_EndInvoke_m5161D63D4573B7C0DEB736B7401F3EAC44F0A2BF (void);
extern void CommandExecutedDelegate__ctor_m594401F4D9521BD36EF1BA644C5B8B33D645722E (void);
extern void CommandExecutedDelegate_Invoke_m39C885F095D6F68663020AD3BBE3F69C4B08AE80 (void);
extern void CommandExecutedDelegate_BeginInvoke_mE752289C916B5DDECA51A24A58E019723CD3D75D (void);
extern void CommandExecutedDelegate_EndInvoke_mB02948DF9FA471B817B77574BDD70D79E6EA300C (void);
extern void DebugLogEntry_Initialize_m692E8607E734476B69652DF35B19E3456574A190 (void);
extern void DebugLogEntry_Clear_m3246170DA990C28A4BFFC04B8D7A130C10D3956C (void);
extern void DebugLogEntry_MatchesSearchTerm_m2A8682D30E6BAA04DD077A48EB81D85287DC96A2 (void);
extern void DebugLogEntry_ToString_m5F4E7E9132FF55B28AE199ADB3318A3A80F6C8B6 (void);
extern void DebugLogEntry_GetContentHashCode_mAF6A9DF7EEDA282CBDFA47FD4F45CB3A6351E377 (void);
extern void DebugLogEntry__ctor_mBE7464DD3CDAACF0FD7B0C097A1CEBEB436B774B (void);
extern void QueuedDebugLogEntry__ctor_m4C3374C16FCD4D5BB8A4224E360B018ACBBA4ACE (void);
extern void QueuedDebugLogEntry_MatchesSearchTerm_m4958263BE056CF58058C82981011F727EEDAE747 (void);
extern void DebugLogEntryTimestamp__ctor_m571803F956E7731A7375AA76C3424C867904AD17 (void);
extern void DebugLogEntryTimestamp_AppendTime_m1613293116BEC3BBEB7E957432B2FDF34B0E593C (void);
extern void DebugLogEntryTimestamp_AppendFullTimestamp_m4C6F17EF93FD1009A0EF3263DCE82792F86403CB (void);
extern void DebugLogEntryContentEqualityComparer_Equals_m96ECBD8693625E1DBF492F0BBD1B2C3F149E796A (void);
extern void DebugLogEntryContentEqualityComparer_GetHashCode_m191F550AF28DE047032151A55406E27DC1453E09 (void);
extern void DebugLogEntryContentEqualityComparer__ctor_mC6CEC8070B4C5097199F3C0ECBE8F26957BF662B (void);
extern void DebugLogItem_get_Transform_mBAA5D6E01F83683FF29CE440C3D2F4D5616E6466 (void);
extern void DebugLogItem_get_Image_m19E45CDC961A393E891617E0AE8196EEC60827E2 (void);
extern void DebugLogItem_get_CanvasGroup_m54F87E5DE59659B77527BE8B0163A02963A1C0DB (void);
extern void DebugLogItem_get_Entry_mA632E3B925BF0ED661A1FC9BE52E87AE1ADC30D1 (void);
extern void DebugLogItem_get_Timestamp_m61F5317F300884C00C9DF7292825AABC36770EAF (void);
extern void DebugLogItem_get_Expanded_mEBEC5F0C2D9702532EE062F84140FAFC6B28131A (void);
extern void DebugLogItem_Initialize_m97E3F360DFD58E5E911D99472A70BB4DE3B00B64 (void);
extern void DebugLogItem_SetContent_mAA060346F58906FAA676D025BF7B440DEBA1C7E3 (void);
extern void DebugLogItem_ShowCount_m370DD836F4F51DE3090FBA4E46F0D5DAE4F6E7A4 (void);
extern void DebugLogItem_HideCount_m74445C719C5941D4DD597C50988526E5FAF7DBD2 (void);
extern void DebugLogItem_UpdateTimestamp_m727217AF90E45BF3F269237C89C9477D0EC3CEB0 (void);
extern void DebugLogItem_SetText_mAE64E89F6945A0D9535258E12B75C7AE340F009D (void);
extern void DebugLogItem_OnPointerClick_m3CD05636F174E641B64612CBBF03E9D37098FAE9 (void);
extern void DebugLogItem_CopyLog_m0FDEE1A57EAEBA556F50B7BD154211490B8D319B (void);
extern void DebugLogItem_GetCopyContent_m5FE53FDB96ECAD424FC870B38C3421F1E9596ED9 (void);
extern void DebugLogItem_CalculateExpandedHeight_m55309AF097A86F270D520E266A7C3ACA00BA0EFC (void);
extern void DebugLogItem_ToString_m773631174F5017BF85AC22D6D85EFDA31A222377 (void);
extern void DebugLogItem__ctor_mAAB23E6C7401E8A16E9CC7AA12589B8106CD5F34 (void);
extern void DebugLogManager_get_Instance_m1CD942C2BA8B924CC5882AD6C13DD970672E1E56 (void);
extern void DebugLogManager_set_Instance_m8AAA1D33AF4369E3F6479AF6B0B6DF97015AE73A (void);
extern void DebugLogManager_get_IsLogWindowVisible_mA4654B1326D4234C4754F811A845C9C57E7F8F54 (void);
extern void DebugLogManager_get_PopupEnabled_m69350E4F2AC441FA05665C843745388941CE82A0 (void);
extern void DebugLogManager_set_PopupEnabled_m73DC638F652F0B01DA24D13D4FA9470A3B2C3B49 (void);
extern void DebugLogManager_Awake_mE7759B77DEEDCFA97AA15F2A4AD4B4902FBFFBC4 (void);
extern void DebugLogManager_OnEnable_m48499501706C868444DBBB4995B1CF48D1C45FD7 (void);
extern void DebugLogManager_OnDisable_mF5CE4FDFE9E746068AED80F76EE6CB18DB249DA9 (void);
extern void DebugLogManager_Start_m64C8F2FE64BDB313D8295E3397F1DF8CD67E08B5 (void);
extern void DebugLogManager_OnDestroy_m957C89C7F92ED81D615925E4DBBBD6439913F7CF (void);
extern void DebugLogManager_OnRectTransformDimensionsChange_mA2528750A32CD1040870FDD9845F24CFD7EF6E86 (void);
extern void DebugLogManager_Update_mBDBADD70294C18412A7F1555AFA5A4E8C0A24893 (void);
extern void DebugLogManager_LateUpdate_mDC31348A9336A5F29F57EF001B1380AD360F1213 (void);
extern void DebugLogManager_ShowLogWindow_m63344BFB6A91AA1AF6E2706E403EBCF3AE394905 (void);
extern void DebugLogManager_HideLogWindow_mDA216839443A72537E963035E9DEC6188631E3D2 (void);
extern void DebugLogManager_OnValidateCommand_m47B17FC3C7B2322739D37ED0B74DD3866EAC892B (void);
extern void DebugLogManager_ReceivedLog_mA4C27364E589E7C61714AB54E614454C046317CA (void);
extern void DebugLogManager_ProcessQueuedLogs_m41A5ED035B8B8D1C17A0D30DA0E68193820D86ED (void);
extern void DebugLogManager_ProcessLog_m038B746339B9A1A632A30EB200F3E184679B3BD4 (void);
extern void DebugLogManager_RemoveOldestLogs_m0248FBE51AC2307BEF1916D6789EF9C8FCB7320C (void);
extern void DebugLogManager_RemoveUncollapsedLogEntry_m41FDF8244DBDE5DE34E10512AE6F2E175E3CC4E2 (void);
extern void DebugLogManager_ShouldRemoveCollapsedLogEntry_mB788DAF82E510E88687C5F1051E557787FADC386 (void);
extern void DebugLogManager_ShouldRemoveLogEntryToShow_m7495893867235F7875D463BBEB981B5A9E3E8536 (void);
extern void DebugLogManager_UpdateLogEntryCollapsedIndex_m01D18155E1A36A23E1DEB2D65DAB4791860AAC5C (void);
extern void DebugLogManager_OnLogEntriesUpdated_mECD4875E79F06A69169F1BA5061FE4DFF4A467F7 (void);
extern void DebugLogManager_PoolLogEntry_m525D23073D51541115EDFE87434DC2C35EFEB968 (void);
extern void DebugLogManager_ValidateScrollPosition_m2A11C6555ED058C996C5116F208E953848D479FF (void);
extern void DebugLogManager_AdjustLatestPendingLog_m3642A8E6BAD4DCD992F14C349C9716A2839F0A93 (void);
extern void DebugLogManager_ClearLogs_m1CDDE08626E2804E6E16556212E2990FFE0FFD09 (void);
extern void DebugLogManager_CollapseButtonPressed_m1A9E0352DBC00AD07E4AE7DC1C64BD91A9F56011 (void);
extern void DebugLogManager_FilterLogButtonPressed_m63FFD1470E923DC8ECA3969A1EE8B71176A249BC (void);
extern void DebugLogManager_FilterWarningButtonPressed_mE585A7869AADA4969E16AC2BBB9C959FD1D43B64 (void);
extern void DebugLogManager_FilterErrorButtonPressed_mF7B8232DE7C74CFB674C4D50DA07417F690D05C5 (void);
extern void DebugLogManager_SearchTermChanged_m6498FD8BDE5D56742AC5EA2EF769FCB1028DDF03 (void);
extern void DebugLogManager_RefreshCommandSuggestions_mFCF8CC3D6641DA413AB30A52D25C37FDFC9A9383 (void);
extern void DebugLogManager_OnEditCommand_mDBD680A89FD7EEB68CC091F20851D3EC7B2AA6A5 (void);
extern void DebugLogManager_OnEndEditCommand_m6A00CF0CD88DE7DDD5E68262AE2CE34DBF3C2307 (void);
extern void DebugLogManager_Resize_m0F28C37B4111332354C18770A29BD3D3277DD0E4 (void);
extern void DebugLogManager_FilterLogs_m4C6CFEB33CDA37F7C3E6C783C174916471E3876D (void);
extern void DebugLogManager_GetAllLogs_mE3A04FBEB916F84E6947FE1576EDFD1E003D7ABF (void);
extern void DebugLogManager_SaveLogsToFile_mA732B3362E923B9B400117C7D6A249E2078BB3E1 (void);
extern void DebugLogManager_SaveLogsToFile_mF9FAE140F4FA5B15748622C1FBEC0760BBDE9508 (void);
extern void DebugLogManager_CheckScreenCutout_mFC29A7A8AE07E6607FDDF23273C1C4F34A0EDF43 (void);
extern void DebugLogManager_PoolLogItem_m50869AD2E15FB447682B47A83D7B06DED349B736 (void);
extern void DebugLogManager_PopLogItem_m854ED51B251AD048C1097A5BF3F6E8518504E0EA (void);
extern void DebugLogManager__ctor_mD38909499B725D0F6B87538420F9ED21DE1CBDF6 (void);
extern void DebugLogManager_U3CAwakeU3Eb__139_0_m87B4CF8968205261D41B82E869BF78D07DF9639D (void);
extern void DebugLogPopup_get_IsVisible_m65F90E3D617F56429222C81AA6B44E3E374922E9 (void);
extern void DebugLogPopup_set_IsVisible_mCDBA8CC0662D11C8CC50EFFB9FA4764843087D9C (void);
extern void DebugLogPopup_Awake_m822432202D739F363A0347F3C321E9B2B3B23495 (void);
extern void DebugLogPopup_NewLogsArrived_m85EAD7C7DB7DC48013913D26B818DCB25B0AF4AD (void);
extern void DebugLogPopup_ResetValues_m9562E9962C32EF6F971827E9099E03E8CCD8E7E8 (void);
extern void DebugLogPopup_MoveToPosAnimation_mAC680B56F6D56D11D41C8580AD1CF1B0E7DB5ADD (void);
extern void DebugLogPopup_OnPointerClick_mFC4D020F734EC309DC8B73FECFF382727750E616 (void);
extern void DebugLogPopup_Show_m7768CAEB2D2EF3373F587FF5D634BDDD3E2E68E4 (void);
extern void DebugLogPopup_Hide_mA3055E290BA97DFAB66E85D942F0656101926F8C (void);
extern void DebugLogPopup_OnBeginDrag_m8BA966F3565080AD72122268C693E12F72A626BF (void);
extern void DebugLogPopup_OnDrag_mE90A037DD7F705A66A4E644FC83D290FFD41C1D0 (void);
extern void DebugLogPopup_OnEndDrag_mDC541F3160B4B4DBC0D6FD8B0AA933CAAED4E4F9 (void);
extern void DebugLogPopup_UpdatePosition_mB0222958D440072B2379842CD02756074C379234 (void);
extern void DebugLogPopup__ctor_m2C30F0B9BCA5D34C0013DBD49C3ECE0A99B17A60 (void);
extern void U3CMoveToPosAnimationU3Ed__25__ctor_mC8A1388E86D07D17D4D9B6402EA77B701899C169 (void);
extern void U3CMoveToPosAnimationU3Ed__25_System_IDisposable_Dispose_m6B852D6BCF4CEDF266A8A62352A62334072D12C1 (void);
extern void U3CMoveToPosAnimationU3Ed__25_MoveNext_mEB365A0C3236ACF671867DD1EFAA97C531BFEA59 (void);
extern void U3CMoveToPosAnimationU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1EB8FC262CCF5BA2744BE265E81C2FE5362E2C59 (void);
extern void U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_Reset_m27A880D1F617C814C12D25EC9516E99F05D8B24D (void);
extern void U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_get_Current_m0D3D86F36CF20F84ADDB433D3D1101DB84DECD15 (void);
extern void DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600 (void);
extern void DebugLogRecycledListView_get_ItemHeight_m4FBFACEF0C31D99C4745209391D410234EAE6A80 (void);
extern void DebugLogRecycledListView_get_SelectedItemHeight_m55C399776D3C681426A1E7BB96D17799AAF17358 (void);
extern void DebugLogRecycledListView_Awake_mB17543BD466BD4A736547268AAC2680CC75BC5B3 (void);
extern void DebugLogRecycledListView_Initialize_m9AB919674122BE9855B08B8DC0E6BA736DFAAAD2 (void);
extern void DebugLogRecycledListView_SetCollapseMode_m8B9F13CBAB315B685DBFBFFBD9FF546DC3AEAFC2 (void);
extern void DebugLogRecycledListView_OnLogItemClicked_m884D345CF8F6ACB003D53075D9A8B6F8B4BB19B2 (void);
extern void DebugLogRecycledListView_SelectAndFocusOnLogItemAtIndex_m76E2D97A52AD52976B1A9ACCEBDC1253B9A07BC9 (void);
extern void DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E (void);
extern void DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63 (void);
extern void DebugLogRecycledListView_OnLogEntriesUpdated_m31B75B0BEFE49F0A6C735132A24575ED537259F1 (void);
extern void DebugLogRecycledListView_OnCollapsedLogEntryAtIndexUpdated_m83CA66274FC922C662D9A56A137F308EC39F1DDB (void);
extern void DebugLogRecycledListView_RefreshCollapsedLogEntryCounts_m9DD4F6E26D5AD4913B7D572BDC6C1A0A1712241D (void);
extern void DebugLogRecycledListView_OnLogEntriesRemoved_m76BF83BC5683B794E130C669C9089505FD8A6437 (void);
extern void DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124 (void);
extern void DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27 (void);
extern void DebugLogRecycledListView_OnViewportWidthChanged_mC15FBC039660C721E52590BFA3E6A08320AF6C4D (void);
extern void DebugLogRecycledListView_OnViewportHeightChanged_mC257005C0AC4AAB23CAB212ABC2B3CD3311E2484 (void);
extern void DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB (void);
extern void DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44 (void);
extern void DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA (void);
extern void DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD (void);
extern void DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71 (void);
extern void DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072 (void);
extern void DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7 (void);
extern void DebugLogRecycledListView__ctor_m187BC9A3FCE5156FCDA97401BF5431EB4BAC164A (void);
extern void DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5 (void);
extern void DebugLogResizeListener_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA23891E13947BF5553F8E9463958B5733DCDAEAF (void);
extern void DebugLogResizeListener_UnityEngine_EventSystems_IDragHandler_OnDrag_m34D99B83889FBD661DE942A7C59522A8ABB0AD3A (void);
extern void DebugLogResizeListener__ctor_m9891E2D11D53F3900EB79C88301C937AD4FB8B33 (void);
extern void DebugsOnScrollListener_OnScroll_m12A82295EEFBA395210BFDF1C80BA59B7438D8F2 (void);
extern void DebugsOnScrollListener_OnBeginDrag_mDA693E6910C47EFFFB1C9E749BACF843BE7680DB (void);
extern void DebugsOnScrollListener_OnEndDrag_m73CCC8AC42E0FC185F490DE8829D51554E59968E (void);
extern void DebugsOnScrollListener_OnScrollbarDragStart_mC24D5BD9F61D0BC49F91808A5D9B20E59A13D30E (void);
extern void DebugsOnScrollListener_OnScrollbarDragEnd_mB867BC08242DE9B5EDD968AB6A01DBA5F96626F9 (void);
extern void DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D (void);
extern void DebugsOnScrollListener__ctor_mC599E1B72DE41CF49476C9F2C121DDAE4B9F4B1F (void);
extern void EventSystemHandler_OnEnable_mBE644BBFCA0529C37CB7C4BDEAA166858EF1545E (void);
extern void EventSystemHandler_OnDisable_mEDA7BEC448D249FC070F53363E935967AF5FB97F (void);
extern void EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030 (void);
extern void EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F (void);
extern void EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B (void);
extern void EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8 (void);
extern void EventSystemHandler__ctor_m2FD41F48C7ED26C15E12B40EDFBAAC64C9A40448 (void);
extern void InputFieldWarningsFixer__ctor_mA0352FB9DB9785DF7FBDE9CD4E1D2A94718F9C6C (void);
static Il2CppMethodPointer s_methodPointers[278] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m4ED3E5C725AE009B6843D39D0FDE87099305C31E,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mBEC466B1723A6F5C322C0264E18E857250E638DF,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ConsoleMethodAttribute_get_Command_m5891B27830AD8D24C0BEC16F9B30F440EBB49E82,
	ConsoleMethodAttribute_get_Description_mFC1AAFE7BDA0B06A1C7AB0C9B02FD228177F7F5E,
	ConsoleMethodAttribute_get_ParameterNames_m254994229F47A4A9C4E8D8AE92AB305A8B7FCCEB,
	ConsoleMethodAttribute__ctor_m14190D9D09BD98E2F46AA89C7B10EC8B75DA1A50,
	ConsoleMethodInfo__ctor_mD64937BD94F186B6418DEF8B8AF8D36AADD59175,
	ConsoleMethodInfo_IsValid_m9F0346F09F2E31E1F774FC741BD75AEE58BF18D7,
	DebugLogConsole_add_OnCommandExecuted_m8E0A1EBEF144441FEEA84FCFC04C88878EF709A7,
	DebugLogConsole_remove_OnCommandExecuted_m395622431A5061473321CA60FF6D2D4FC3F43175,
	DebugLogConsole__cctor_m75A152ACAB9CFE5C1E44304E03114D1B6DB0386A,
	DebugLogConsole_SearchAssemblyForConsoleMethods_m79A4694CEECE4C827D5FF08C6AA333C02DBF9C71,
	DebugLogConsole_GetAllCommands_m20956F3B02C815D858DD93154A09740AD3F55397,
	DebugLogConsole_LogAllCommands_m978E37DE77B64B1BFEBEAE76A009FC8B6869E1EC,
	DebugLogConsole_LogAllCommandsWithName_mE66120EA06927650B2D14A4DC9291C9FD34F4B2D,
	DebugLogConsole_LogSystemInfo_mB8DFBFE050EC55468EDF6106D49D060C5E2AB622,
	DebugLogConsole_AppendSysInfoIfPresent_m8348C123A80619F01E787BC5F6DF69577A53B556,
	DebugLogConsole_AppendSysInfoIfPresent_mE33F854781092A2E4B3A1301DADA8A99C9FF1537,
	DebugLogConsole_AddCustomParameterType_m7725AC531D2A3583F6BE80BB92A716ACD766F3FA,
	DebugLogConsole_RemoveCustomParameterType_m2835B8990F7DD01009446DF14B4417E48579968B,
	DebugLogConsole_AddCommandInstance_m460403F17EA9E045FB3CCE3739A1A066E91FAC33,
	DebugLogConsole_AddCommandStatic_mA25C3DE5B2273715EAE7B7EA643F2B27E4E7EF80,
	DebugLogConsole_AddCommand_m44EA1C3BC10905406AAED2B25AC713C0E076A70F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DebugLogConsole_AddCommand_m8DC230B10D47E9279F8E0581B3D450A68B03D3BC,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DebugLogConsole_AddCommand_m68F24524C503875268CF3475FFDECC1D96D6DEC4,
	DebugLogConsole_AddCommand_m4BFF08D8BF1C63A4282EB1B5FFF95E695A0C4FD9,
	DebugLogConsole_AddCommand_m3D56A91D2615526245C2996E0D97AE636B6EDFAB,
	DebugLogConsole_RemoveCommand_m7E9D585B16ADA53505185CF20D433A02EE9670E8,
	DebugLogConsole_RemoveCommand_m8FC42072669FFB7EF73C84595FAE4EBB94C2058F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DebugLogConsole_RemoveCommand_mCB959C90BB894AC0413C56D67856B51D4B8FAC12,
	DebugLogConsole_RemoveCommand_m5B5D9DBA606BD7CF700A12DCDEED6F3CA7ABC90A,
	DebugLogConsole_GetAutoCompleteCommand_m87F49D25747A4C04B48BDF3AC939863E27A0778E,
	DebugLogConsole_ExecuteCommand_m09EFF4CAD43DDF1822589BCA5466F2BF3B02CD21,
	DebugLogConsole_FetchArgumentsFromCommand_mC1BAA30A6C63276103CEE04B898336A8900FDCAF,
	DebugLogConsole_FindCommands_m33C4DDBF9B312C69EED77A268C8BBFB218F51146,
	DebugLogConsole_GetCommandSuggestions_mD9A1F9ECB81527BDD14183214F668B8C6AA0DCBF,
	DebugLogConsole_IndexOfDelimiterGroup_m0CC9FFF8192FA903465847E9B51D5D7B6A826F29,
	DebugLogConsole_IndexOfDelimiterGroupEnd_mA3CF7EEF7D82521E9173011822619D159E404C72,
	DebugLogConsole_IndexOfChar_m4C1A446342AA125173F57AAED37E9445C8C9CD37,
	DebugLogConsole_FindCommandIndex_m937912A34A602748887E50AE2A6965A125E420A7,
	DebugLogConsole_IsSupportedArrayType_mCF63052024BD4D95564AD4E395C558E99092BB86,
	DebugLogConsole_GetTypeReadableName_mD536CA0D3C2534C91B7BA1096C1F09B8D4E83934,
	DebugLogConsole_ParseArgument_mD38752180CA3F7F281044CFAA47E4CD34597CC24,
	DebugLogConsole_ParseString_mC6A889D46770639BE9916E34C01111BDA20B4195,
	DebugLogConsole_ParseBool_mEF25361C5A16EA42E637C9A5C4CAAA9B69FE6C93,
	DebugLogConsole_ParseInt_m89E83041C6A5BBE5D15158F8463E7791967D99DA,
	DebugLogConsole_ParseUInt_m1DC3574D7B9A04530134BFFBDBAC11F4638F5C04,
	DebugLogConsole_ParseLong_m931B8028A8F8FA1FB4DC8BDFFCE46D640329BCBA,
	DebugLogConsole_ParseULong_mE10F56112556D7CA1DDEA4AA6419A4D81DE2E5C9,
	DebugLogConsole_ParseByte_mDCD41E40AF9A49B3000FB4F9CA3476AC28E4C172,
	DebugLogConsole_ParseSByte_mB24E4FD123682684CED79E98F16BEC0C72575B10,
	DebugLogConsole_ParseShort_m5F4567CA60623F25C9EB8470354C5C2844A715F1,
	DebugLogConsole_ParseUShort_m5063AAAD632520CCAE05D2183F8C6EC36F5789DC,
	DebugLogConsole_ParseChar_mE26B50B4C917AC6EA436AE99B798EC443A3C61BC,
	DebugLogConsole_ParseFloat_m1D8D2B2D33A52F4564830459382BA43F178EFDD8,
	DebugLogConsole_ParseDouble_mE0E87D6FA7478B20157C50AB3CC6FA3B1D2AEA94,
	DebugLogConsole_ParseDecimal_m474453B063948DD000A23560EFFAC050937AF2AA,
	DebugLogConsole_ParseVector2_m7C7FA8F64A90CC149A78E4F53E23101F4320EF17,
	DebugLogConsole_ParseVector3_m1180FCC0F42A09E6A8436E2864EB376CE2317373,
	DebugLogConsole_ParseVector4_mD631138EB5D4E11480D70BE1BC91E717E5E26CA6,
	DebugLogConsole_ParseQuaternion_mD00B4033687BC4E5FB517201982EE7011CB5CED6,
	DebugLogConsole_ParseColor_m143BF87018F4A2B5D6F6E788457C66114F6B12EC,
	DebugLogConsole_ParseColor32_mB7E40A40CF0B3BF6360DF08350938199400F71BF,
	DebugLogConsole_ParseRect_m387D9CDCD3040CEB10B7C96FB7D5BE8EED3CA591,
	DebugLogConsole_ParseRectOffset_m72AF3CF248A32FA772AB4D657C9A8A7A34E686CB,
	DebugLogConsole_ParseBounds_mDCA2A9708F851390931A57FF8E5FB40A2AA627B9,
	DebugLogConsole_ParseVector2Int_m3649B9BC937B4664ACC7CA2F0200C63FBB25072E,
	DebugLogConsole_ParseVector3Int_mD25E0226F9D50907FA52B655D84A9BFE58F28304,
	DebugLogConsole_ParseRectInt_m872D02399F3D0E1AB96EA4C5E7ECEC7A646AE975,
	DebugLogConsole_ParseBoundsInt_m530D51F98125A1FBAA8A063D1A3085FDA6CF0F7C,
	DebugLogConsole_ParseGameObject_mF3CB80573688D1F357974DD9D979099F3FFF4466,
	DebugLogConsole_ParseComponent_m9D6051721A1FB1D1A1AAC2F28E029003B0807833,
	DebugLogConsole_ParseEnum_m3E539F2030843B5E70454E1DE9EA3EFFDA570CAE,
	DebugLogConsole_ParseArray_m447E48C65E5900282AC24CA15886EB383929E04F,
	DebugLogConsole_ParseVector_m02B35A42C6167A4F80F99869A134FAF667E90FFD,
	ParseFunction__ctor_m6660320276B797D09EC7D69B6AB268429D3B4B25,
	ParseFunction_Invoke_m6559BD38F8E131515CE959A7FC6D7EDE407794C1,
	ParseFunction_BeginInvoke_m39B29B3F54DD95ABB11FC726B6A4794D40D9252F,
	ParseFunction_EndInvoke_m5161D63D4573B7C0DEB736B7401F3EAC44F0A2BF,
	CommandExecutedDelegate__ctor_m594401F4D9521BD36EF1BA644C5B8B33D645722E,
	CommandExecutedDelegate_Invoke_m39C885F095D6F68663020AD3BBE3F69C4B08AE80,
	CommandExecutedDelegate_BeginInvoke_mE752289C916B5DDECA51A24A58E019723CD3D75D,
	CommandExecutedDelegate_EndInvoke_mB02948DF9FA471B817B77574BDD70D79E6EA300C,
	DebugLogEntry_Initialize_m692E8607E734476B69652DF35B19E3456574A190,
	DebugLogEntry_Clear_m3246170DA990C28A4BFFC04B8D7A130C10D3956C,
	DebugLogEntry_MatchesSearchTerm_m2A8682D30E6BAA04DD077A48EB81D85287DC96A2,
	DebugLogEntry_ToString_m5F4E7E9132FF55B28AE199ADB3318A3A80F6C8B6,
	DebugLogEntry_GetContentHashCode_mAF6A9DF7EEDA282CBDFA47FD4F45CB3A6351E377,
	DebugLogEntry__ctor_mBE7464DD3CDAACF0FD7B0C097A1CEBEB436B774B,
	QueuedDebugLogEntry__ctor_m4C3374C16FCD4D5BB8A4224E360B018ACBBA4ACE,
	QueuedDebugLogEntry_MatchesSearchTerm_m4958263BE056CF58058C82981011F727EEDAE747,
	DebugLogEntryTimestamp__ctor_m571803F956E7731A7375AA76C3424C867904AD17,
	DebugLogEntryTimestamp_AppendTime_m1613293116BEC3BBEB7E957432B2FDF34B0E593C,
	DebugLogEntryTimestamp_AppendFullTimestamp_m4C6F17EF93FD1009A0EF3263DCE82792F86403CB,
	DebugLogEntryContentEqualityComparer_Equals_m96ECBD8693625E1DBF492F0BBD1B2C3F149E796A,
	DebugLogEntryContentEqualityComparer_GetHashCode_m191F550AF28DE047032151A55406E27DC1453E09,
	DebugLogEntryContentEqualityComparer__ctor_mC6CEC8070B4C5097199F3C0ECBE8F26957BF662B,
	DebugLogItem_get_Transform_mBAA5D6E01F83683FF29CE440C3D2F4D5616E6466,
	DebugLogItem_get_Image_m19E45CDC961A393E891617E0AE8196EEC60827E2,
	DebugLogItem_get_CanvasGroup_m54F87E5DE59659B77527BE8B0163A02963A1C0DB,
	DebugLogItem_get_Entry_mA632E3B925BF0ED661A1FC9BE52E87AE1ADC30D1,
	DebugLogItem_get_Timestamp_m61F5317F300884C00C9DF7292825AABC36770EAF,
	DebugLogItem_get_Expanded_mEBEC5F0C2D9702532EE062F84140FAFC6B28131A,
	DebugLogItem_Initialize_m97E3F360DFD58E5E911D99472A70BB4DE3B00B64,
	DebugLogItem_SetContent_mAA060346F58906FAA676D025BF7B440DEBA1C7E3,
	DebugLogItem_ShowCount_m370DD836F4F51DE3090FBA4E46F0D5DAE4F6E7A4,
	DebugLogItem_HideCount_m74445C719C5941D4DD597C50988526E5FAF7DBD2,
	DebugLogItem_UpdateTimestamp_m727217AF90E45BF3F269237C89C9477D0EC3CEB0,
	DebugLogItem_SetText_mAE64E89F6945A0D9535258E12B75C7AE340F009D,
	DebugLogItem_OnPointerClick_m3CD05636F174E641B64612CBBF03E9D37098FAE9,
	DebugLogItem_CopyLog_m0FDEE1A57EAEBA556F50B7BD154211490B8D319B,
	DebugLogItem_GetCopyContent_m5FE53FDB96ECAD424FC870B38C3421F1E9596ED9,
	DebugLogItem_CalculateExpandedHeight_m55309AF097A86F270D520E266A7C3ACA00BA0EFC,
	DebugLogItem_ToString_m773631174F5017BF85AC22D6D85EFDA31A222377,
	DebugLogItem__ctor_mAAB23E6C7401E8A16E9CC7AA12589B8106CD5F34,
	DebugLogManager_get_Instance_m1CD942C2BA8B924CC5882AD6C13DD970672E1E56,
	DebugLogManager_set_Instance_m8AAA1D33AF4369E3F6479AF6B0B6DF97015AE73A,
	DebugLogManager_get_IsLogWindowVisible_mA4654B1326D4234C4754F811A845C9C57E7F8F54,
	DebugLogManager_get_PopupEnabled_m69350E4F2AC441FA05665C843745388941CE82A0,
	DebugLogManager_set_PopupEnabled_m73DC638F652F0B01DA24D13D4FA9470A3B2C3B49,
	DebugLogManager_Awake_mE7759B77DEEDCFA97AA15F2A4AD4B4902FBFFBC4,
	DebugLogManager_OnEnable_m48499501706C868444DBBB4995B1CF48D1C45FD7,
	DebugLogManager_OnDisable_mF5CE4FDFE9E746068AED80F76EE6CB18DB249DA9,
	DebugLogManager_Start_m64C8F2FE64BDB313D8295E3397F1DF8CD67E08B5,
	DebugLogManager_OnDestroy_m957C89C7F92ED81D615925E4DBBBD6439913F7CF,
	DebugLogManager_OnRectTransformDimensionsChange_mA2528750A32CD1040870FDD9845F24CFD7EF6E86,
	DebugLogManager_Update_mBDBADD70294C18412A7F1555AFA5A4E8C0A24893,
	DebugLogManager_LateUpdate_mDC31348A9336A5F29F57EF001B1380AD360F1213,
	DebugLogManager_ShowLogWindow_m63344BFB6A91AA1AF6E2706E403EBCF3AE394905,
	DebugLogManager_HideLogWindow_mDA216839443A72537E963035E9DEC6188631E3D2,
	DebugLogManager_OnValidateCommand_m47B17FC3C7B2322739D37ED0B74DD3866EAC892B,
	DebugLogManager_ReceivedLog_mA4C27364E589E7C61714AB54E614454C046317CA,
	DebugLogManager_ProcessQueuedLogs_m41A5ED035B8B8D1C17A0D30DA0E68193820D86ED,
	DebugLogManager_ProcessLog_m038B746339B9A1A632A30EB200F3E184679B3BD4,
	DebugLogManager_RemoveOldestLogs_m0248FBE51AC2307BEF1916D6789EF9C8FCB7320C,
	DebugLogManager_RemoveUncollapsedLogEntry_m41FDF8244DBDE5DE34E10512AE6F2E175E3CC4E2,
	DebugLogManager_ShouldRemoveCollapsedLogEntry_mB788DAF82E510E88687C5F1051E557787FADC386,
	DebugLogManager_ShouldRemoveLogEntryToShow_m7495893867235F7875D463BBEB981B5A9E3E8536,
	DebugLogManager_UpdateLogEntryCollapsedIndex_m01D18155E1A36A23E1DEB2D65DAB4791860AAC5C,
	DebugLogManager_OnLogEntriesUpdated_mECD4875E79F06A69169F1BA5061FE4DFF4A467F7,
	DebugLogManager_PoolLogEntry_m525D23073D51541115EDFE87434DC2C35EFEB968,
	DebugLogManager_ValidateScrollPosition_m2A11C6555ED058C996C5116F208E953848D479FF,
	DebugLogManager_AdjustLatestPendingLog_m3642A8E6BAD4DCD992F14C349C9716A2839F0A93,
	DebugLogManager_ClearLogs_m1CDDE08626E2804E6E16556212E2990FFE0FFD09,
	DebugLogManager_CollapseButtonPressed_m1A9E0352DBC00AD07E4AE7DC1C64BD91A9F56011,
	DebugLogManager_FilterLogButtonPressed_m63FFD1470E923DC8ECA3969A1EE8B71176A249BC,
	DebugLogManager_FilterWarningButtonPressed_mE585A7869AADA4969E16AC2BBB9C959FD1D43B64,
	DebugLogManager_FilterErrorButtonPressed_mF7B8232DE7C74CFB674C4D50DA07417F690D05C5,
	DebugLogManager_SearchTermChanged_m6498FD8BDE5D56742AC5EA2EF769FCB1028DDF03,
	DebugLogManager_RefreshCommandSuggestions_mFCF8CC3D6641DA413AB30A52D25C37FDFC9A9383,
	DebugLogManager_OnEditCommand_mDBD680A89FD7EEB68CC091F20851D3EC7B2AA6A5,
	DebugLogManager_OnEndEditCommand_m6A00CF0CD88DE7DDD5E68262AE2CE34DBF3C2307,
	DebugLogManager_Resize_m0F28C37B4111332354C18770A29BD3D3277DD0E4,
	DebugLogManager_FilterLogs_m4C6CFEB33CDA37F7C3E6C783C174916471E3876D,
	DebugLogManager_GetAllLogs_mE3A04FBEB916F84E6947FE1576EDFD1E003D7ABF,
	DebugLogManager_SaveLogsToFile_mA732B3362E923B9B400117C7D6A249E2078BB3E1,
	DebugLogManager_SaveLogsToFile_mF9FAE140F4FA5B15748622C1FBEC0760BBDE9508,
	DebugLogManager_CheckScreenCutout_mFC29A7A8AE07E6607FDDF23273C1C4F34A0EDF43,
	DebugLogManager_PoolLogItem_m50869AD2E15FB447682B47A83D7B06DED349B736,
	DebugLogManager_PopLogItem_m854ED51B251AD048C1097A5BF3F6E8518504E0EA,
	DebugLogManager__ctor_mD38909499B725D0F6B87538420F9ED21DE1CBDF6,
	DebugLogManager_U3CAwakeU3Eb__139_0_m87B4CF8968205261D41B82E869BF78D07DF9639D,
	DebugLogPopup_get_IsVisible_m65F90E3D617F56429222C81AA6B44E3E374922E9,
	DebugLogPopup_set_IsVisible_mCDBA8CC0662D11C8CC50EFFB9FA4764843087D9C,
	DebugLogPopup_Awake_m822432202D739F363A0347F3C321E9B2B3B23495,
	DebugLogPopup_NewLogsArrived_m85EAD7C7DB7DC48013913D26B818DCB25B0AF4AD,
	DebugLogPopup_ResetValues_m9562E9962C32EF6F971827E9099E03E8CCD8E7E8,
	DebugLogPopup_MoveToPosAnimation_mAC680B56F6D56D11D41C8580AD1CF1B0E7DB5ADD,
	DebugLogPopup_OnPointerClick_mFC4D020F734EC309DC8B73FECFF382727750E616,
	DebugLogPopup_Show_m7768CAEB2D2EF3373F587FF5D634BDDD3E2E68E4,
	DebugLogPopup_Hide_mA3055E290BA97DFAB66E85D942F0656101926F8C,
	DebugLogPopup_OnBeginDrag_m8BA966F3565080AD72122268C693E12F72A626BF,
	DebugLogPopup_OnDrag_mE90A037DD7F705A66A4E644FC83D290FFD41C1D0,
	DebugLogPopup_OnEndDrag_mDC541F3160B4B4DBC0D6FD8B0AA933CAAED4E4F9,
	DebugLogPopup_UpdatePosition_mB0222958D440072B2379842CD02756074C379234,
	DebugLogPopup__ctor_m2C30F0B9BCA5D34C0013DBD49C3ECE0A99B17A60,
	U3CMoveToPosAnimationU3Ed__25__ctor_mC8A1388E86D07D17D4D9B6402EA77B701899C169,
	U3CMoveToPosAnimationU3Ed__25_System_IDisposable_Dispose_m6B852D6BCF4CEDF266A8A62352A62334072D12C1,
	U3CMoveToPosAnimationU3Ed__25_MoveNext_mEB365A0C3236ACF671867DD1EFAA97C531BFEA59,
	U3CMoveToPosAnimationU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1EB8FC262CCF5BA2744BE265E81C2FE5362E2C59,
	U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_Reset_m27A880D1F617C814C12D25EC9516E99F05D8B24D,
	U3CMoveToPosAnimationU3Ed__25_System_Collections_IEnumerator_get_Current_m0D3D86F36CF20F84ADDB433D3D1101DB84DECD15,
	DebugLogRecycledListView_get_DeltaHeightOfSelectedLogEntry_mA46F814004CC6686FF2542C93B97F2B410E17600,
	DebugLogRecycledListView_get_ItemHeight_m4FBFACEF0C31D99C4745209391D410234EAE6A80,
	DebugLogRecycledListView_get_SelectedItemHeight_m55C399776D3C681426A1E7BB96D17799AAF17358,
	DebugLogRecycledListView_Awake_mB17543BD466BD4A736547268AAC2680CC75BC5B3,
	DebugLogRecycledListView_Initialize_m9AB919674122BE9855B08B8DC0E6BA736DFAAAD2,
	DebugLogRecycledListView_SetCollapseMode_m8B9F13CBAB315B685DBFBFFBD9FF546DC3AEAFC2,
	DebugLogRecycledListView_OnLogItemClicked_m884D345CF8F6ACB003D53075D9A8B6F8B4BB19B2,
	DebugLogRecycledListView_SelectAndFocusOnLogItemAtIndex_m76E2D97A52AD52976B1A9ACCEBDC1253B9A07BC9,
	DebugLogRecycledListView_OnLogItemClickedInternal_m9C3AA7CA2C2C65BC15F72C0AA4AA9310F724707E,
	DebugLogRecycledListView_DeselectSelectedLogItem_m04DC7746AB16AF9D1B7E9D9747ACA6B70787BF63,
	DebugLogRecycledListView_OnLogEntriesUpdated_m31B75B0BEFE49F0A6C735132A24575ED537259F1,
	DebugLogRecycledListView_OnCollapsedLogEntryAtIndexUpdated_m83CA66274FC922C662D9A56A137F308EC39F1DDB,
	DebugLogRecycledListView_RefreshCollapsedLogEntryCounts_m9DD4F6E26D5AD4913B7D572BDC6C1A0A1712241D,
	DebugLogRecycledListView_OnLogEntriesRemoved_m76BF83BC5683B794E130C669C9089505FD8A6437,
	DebugLogRecycledListView_ShouldRemoveLogItem_m88B27AD516FEC0744C63EA1AE7ECDD4B4C086124,
	DebugLogRecycledListView_FindIndexOfLogEntryInReverseDirection_m2638A20C51B02F596001C11B22890147D26D7B27,
	DebugLogRecycledListView_OnViewportWidthChanged_mC15FBC039660C721E52590BFA3E6A08320AF6C4D,
	DebugLogRecycledListView_OnViewportHeightChanged_mC257005C0AC4AAB23CAB212ABC2B3CD3311E2484,
	DebugLogRecycledListView_CalculateContentHeight_m88EBCC3CBDBDE451B6F798EA318B87B9D443ABCB,
	DebugLogRecycledListView_CalculateSelectedLogEntryHeight_mE78B718B6A980A95F3E9D538AB67D6075F253D44,
	DebugLogRecycledListView_UpdateItemsInTheList_mFA3693DC343B8C587ADB84C71BEFF417EA3710EA,
	DebugLogRecycledListView_GetLogItemAtIndex_m701E9237DDEC23011F430F2B47D52B573DC34BAD,
	DebugLogRecycledListView_UpdateLogItemContentsBetweenIndices_mD57F8C79EDA4FD87C3832215D2F1082AB7DF4A71,
	DebugLogRecycledListView_RepositionLogItem_m6A7A36C93FBA0C44560F9D9556C343F5EA052072,
	DebugLogRecycledListView_ColorLogItem_mC1A80473D95AFCE3DC87E38DCABEB90096C1D2A7,
	DebugLogRecycledListView__ctor_m187BC9A3FCE5156FCDA97401BF5431EB4BAC164A,
	DebugLogRecycledListView_U3CAwakeU3Eb__25_0_m46F3DED084709344721E369E0D3AC052735594A5,
	DebugLogResizeListener_UnityEngine_EventSystems_IBeginDragHandler_OnBeginDrag_mA23891E13947BF5553F8E9463958B5733DCDAEAF,
	DebugLogResizeListener_UnityEngine_EventSystems_IDragHandler_OnDrag_m34D99B83889FBD661DE942A7C59522A8ABB0AD3A,
	DebugLogResizeListener__ctor_m9891E2D11D53F3900EB79C88301C937AD4FB8B33,
	DebugsOnScrollListener_OnScroll_m12A82295EEFBA395210BFDF1C80BA59B7438D8F2,
	DebugsOnScrollListener_OnBeginDrag_mDA693E6910C47EFFFB1C9E749BACF843BE7680DB,
	DebugsOnScrollListener_OnEndDrag_m73CCC8AC42E0FC185F490DE8829D51554E59968E,
	DebugsOnScrollListener_OnScrollbarDragStart_mC24D5BD9F61D0BC49F91808A5D9B20E59A13D30E,
	DebugsOnScrollListener_OnScrollbarDragEnd_mB867BC08242DE9B5EDD968AB6A01DBA5F96626F9,
	DebugsOnScrollListener_IsScrollbarAtBottom_mD32894983625F436CB103115886010DE782D5A8D,
	DebugsOnScrollListener__ctor_mC599E1B72DE41CF49476C9F2C121DDAE4B9F4B1F,
	EventSystemHandler_OnEnable_mBE644BBFCA0529C37CB7C4BDEAA166858EF1545E,
	EventSystemHandler_OnDisable_mEDA7BEC448D249FC070F53363E935967AF5FB97F,
	EventSystemHandler_OnSceneLoaded_m3560B76815CA8D42F09021ADB3C1482E117A0030,
	EventSystemHandler_OnSceneUnloaded_m8F8FBCA629625A8DDBDBB7865D294FA4E2E4729F,
	EventSystemHandler_ActivateEventSystemIfNeeded_m162A2AECC9C9CB151849DD89B25485FAFC30313B,
	EventSystemHandler_DeactivateEventSystem_m45036C26C5322BD6DF68716964C1C53BB19C90F8,
	EventSystemHandler__ctor_m2FD41F48C7ED26C15E12B40EDFBAAC64C9A40448,
	InputFieldWarningsFixer__ctor_mA0352FB9DB9785DF7FBDE9CD4E1D2A94718F9C6C,
};
extern void QueuedDebugLogEntry__ctor_m4C3374C16FCD4D5BB8A4224E360B018ACBBA4ACE_AdjustorThunk (void);
extern void QueuedDebugLogEntry_MatchesSearchTerm_m4958263BE056CF58058C82981011F727EEDAE747_AdjustorThunk (void);
extern void DebugLogEntryTimestamp__ctor_m571803F956E7731A7375AA76C3424C867904AD17_AdjustorThunk (void);
extern void DebugLogEntryTimestamp_AppendTime_m1613293116BEC3BBEB7E957432B2FDF34B0E593C_AdjustorThunk (void);
extern void DebugLogEntryTimestamp_AppendFullTimestamp_m4C6F17EF93FD1009A0EF3263DCE82792F86403CB_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[5] = 
{
	{ 0x0600008D, QueuedDebugLogEntry__ctor_m4C3374C16FCD4D5BB8A4224E360B018ACBBA4ACE_AdjustorThunk },
	{ 0x0600008E, QueuedDebugLogEntry_MatchesSearchTerm_m4958263BE056CF58058C82981011F727EEDAE747_AdjustorThunk },
	{ 0x0600008F, DebugLogEntryTimestamp__ctor_m571803F956E7731A7375AA76C3424C867904AD17_AdjustorThunk },
	{ 0x06000090, DebugLogEntryTimestamp_AppendTime_m1613293116BEC3BBEB7E957432B2FDF34B0E593C_AdjustorThunk },
	{ 0x06000091, DebugLogEntryTimestamp_AppendFullTimestamp_m4C6F17EF93FD1009A0EF3263DCE82792F86403CB_AdjustorThunk },
};
static const int32_t s_InvokerIndices[278] = 
{
	9106,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4250,
	4250,
	4250,
	2084,
	628,
	4168,
	8887,
	8887,
	9089,
	8887,
	9031,
	9089,
	8887,
	9089,
	6693,
	6687,
	6985,
	8887,
	5573,
	5573,
	6985,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6985,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6187,
	5131,
	5573,
	8887,
	8887,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8887,
	8887,
	7631,
	8887,
	7975,
	6953,
	5565,
	8407,
	6529,
	6541,
	8399,
	8220,
	8505,
	6365,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	7252,
	6365,
	6365,
	6365,
	6365,
	2798,
	2315,
	1176,
	2281,
	2798,
	2802,
	1210,
	3881,
	2802,
	4364,
	3185,
	4250,
	4216,
	4364,
	2081,
	3185,
	1948,
	3881,
	3881,
	2322,
	3419,
	4364,
	4250,
	4250,
	4250,
	4250,
	4073,
	4168,
	3881,
	1382,
	4364,
	4364,
	3822,
	2020,
	3881,
	4364,
	4250,
	2569,
	4250,
	4364,
	9031,
	8887,
	4168,
	4168,
	3807,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	1860,
	2081,
	3852,
	2830,
	3852,
	3881,
	3185,
	3185,
	2796,
	2688,
	3881,
	4364,
	2688,
	4364,
	4364,
	4364,
	4364,
	4364,
	3881,
	3881,
	3881,
	3881,
	3881,
	4364,
	4250,
	4364,
	3881,
	4364,
	3881,
	4250,
	4364,
	4364,
	4168,
	3807,
	4364,
	1973,
	4364,
	3529,
	3881,
	4364,
	4364,
	3881,
	3881,
	3881,
	3807,
	4364,
	3852,
	4364,
	4168,
	4250,
	4364,
	4250,
	4298,
	4298,
	4298,
	4364,
	1459,
	3807,
	3881,
	3852,
	2739,
	4364,
	3807,
	3852,
	4364,
	3852,
	3185,
	2434,
	4364,
	4364,
	4364,
	3881,
	3807,
	3515,
	1973,
	3881,
	3881,
	4364,
	3978,
	3881,
	3881,
	4364,
	3881,
	3881,
	3881,
	3881,
	3881,
	4168,
	4364,
	4364,
	4364,
	2842,
	3922,
	4364,
	4364,
	4364,
	4364,
};
static const Il2CppTokenRangePair s_rgctxIndices[29] = 
{
	{ 0x02000004, { 0, 6 } },
	{ 0x02000005, { 6, 18 } },
	{ 0x06000015, { 24, 5 } },
	{ 0x06000031, { 29, 1 } },
	{ 0x06000032, { 30, 1 } },
	{ 0x06000033, { 31, 1 } },
	{ 0x06000034, { 32, 1 } },
	{ 0x06000035, { 33, 1 } },
	{ 0x06000036, { 34, 1 } },
	{ 0x06000037, { 35, 1 } },
	{ 0x06000038, { 36, 1 } },
	{ 0x06000039, { 37, 1 } },
	{ 0x0600003B, { 38, 1 } },
	{ 0x0600003C, { 39, 1 } },
	{ 0x0600003D, { 40, 1 } },
	{ 0x0600003E, { 41, 1 } },
	{ 0x0600003F, { 42, 1 } },
	{ 0x06000040, { 43, 1 } },
	{ 0x06000041, { 44, 1 } },
	{ 0x06000042, { 45, 1 } },
	{ 0x06000048, { 46, 1 } },
	{ 0x06000049, { 47, 1 } },
	{ 0x0600004A, { 48, 1 } },
	{ 0x0600004B, { 49, 1 } },
	{ 0x0600004C, { 50, 1 } },
	{ 0x0600004D, { 51, 1 } },
	{ 0x0600004E, { 52, 1 } },
	{ 0x0600004F, { 53, 1 } },
	{ 0x06000050, { 54, 1 } },
};
extern const uint32_t g_rgctx_CircularBuffer_1_t74731B07E4692A23030932ED37E69B4412D05FF7;
extern const uint32_t g_rgctx_TU5BU5D_tED49EDF8D2C4E3AFD9FFFDF819CBEB910D353AB1;
extern const uint32_t g_rgctx_T_t104ADD706E1E8C672B765D54F64026219FCED0B4;
extern const uint32_t g_rgctx_TU5BU5D_tED49EDF8D2C4E3AFD9FFFDF819CBEB910D353AB1;
extern const uint32_t g_rgctx_CircularBuffer_1_get_Count_m54877B3269781F998B4A0810BD2CB4C6B5C843DD;
extern const uint32_t g_rgctx_CircularBuffer_1_set_Count_mBEAE70AC8DE0C0BAB275C3BA1D6B089C83AE4D33;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_tC5F3AD0EA7219C92B38FE962665E0C8AF8EFA885;
extern const uint32_t g_rgctx_TU5BU5D_t4CF7A0220C7AEE390A3CC6D7690A28AAD7CE36A9;
extern const uint32_t g_rgctx_T_t1F7F904395BD0301CD48B948B26B7BAFEE436301;
extern const uint32_t g_rgctx_TU5BU5D_t4CF7A0220C7AEE390A3CC6D7690A28AAD7CE36A9;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_get_Count_mC6DC5EB553A04244F2EBCC8B4B6288B32B6DD7C8;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_SetCapacity_m403934FD2BE6F64DAAB458DECB3465BD447AEED2;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_set_Count_mAF5EDDAC4680B182D010548F22EE5A7FB7D422B5;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_set_Item_mCCB0D9D739065CE337F1D14FDA7BCE316BAEAA15;
extern const uint32_t g_rgctx_Predicate_1_tC6099E86AE3B4491EEE4CC6CFB3B7EFC98ECFE6C;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_RemoveAll_TisT_t1F7F904395BD0301CD48B948B26B7BAFEE436301_m74FC1CEFC39BE51056D953510649E04CE7BFB12E;
extern const uint32_t g_rgctx_Action_2_tD26595835ACBE765E71BCB8321F3DA6E23B3F961;
extern const uint32_t g_rgctx_Predicate_1_Invoke_m5228B7C572B34068FE85D153E28299BE008146E5;
extern const uint32_t g_rgctx_Action_2_Invoke_m6CFCC08620D93F9DAB5458C6C01E0061E600EC46;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_TrimEnd_mF71B2AA0F73856462AF59C69C75EC7DA5B48DAEF;
extern const uint32_t g_rgctx_Action_1_tD461AA7EB0A74DE60998051345FD06141160224F;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_TrimInternal_m3D44B73A36EF459166F92BB6A3D7F6B325B4F9E3;
extern const uint32_t g_rgctx_Action_1_Invoke_m6777485238C78DADA1E0887A5D31DD19C58DD8EC;
extern const uint32_t g_rgctx_Array_IndexOf_TisT_t1F7F904395BD0301CD48B948B26B7BAFEE436301_m15976AF0A8933E9CD907186E0A451A61EC0FDA32;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_t5A23AF08303EF8C96071BEBA79B9DB0EA2232BBA;
extern const uint32_t g_rgctx_YU5BU5D_t169FA787518151E795852DC0F7A9BF17EC11EF47;
extern const uint32_t g_rgctx_Y_t1069BDFC4B8A094E98BCE3D72534A945E7FC80EE;
extern const uint32_t g_rgctx_DynamicCircularBuffer_1_TrimEnd_m26364DB029A8E210C5BDD1AFB697EB97FFB580A8;
extern const uint32_t g_rgctx_Action_1_t321AE9F1CC3DF2C5F1DF89A9F1B23B68A570A889;
extern const uint32_t g_rgctx_Action_1_t91C067E5FB5B976462A68888FD66FE6DFE53C7F7;
extern const uint32_t g_rgctx_Func_1_tFD32DCEE983C95F090D4C5727C2F8C43A130EDAF;
extern const uint32_t g_rgctx_Action_2_tE6AE110FB656A15D6AFBA1DA14BD5854F7274897;
extern const uint32_t g_rgctx_Func_2_t6E4CF313FA9F19EADB7F51A19E827D77840FE264;
extern const uint32_t g_rgctx_Action_3_tB0D4F0134E07AC16701719B5C3CB5BF930AE1518;
extern const uint32_t g_rgctx_Func_3_t4C4E82ACE884E5ED1748B192241783A866B02E36;
extern const uint32_t g_rgctx_Action_4_tBD95322D947294E12273872A97AD40E6095B47E6;
extern const uint32_t g_rgctx_Func_4_tEA6340AB9BE60D5A291B3C59B032D2DF41640EB6;
extern const uint32_t g_rgctx_Func_5_t5A8B0B5526CA5CB6777FF121F0645EE7FA050CAA;
extern const uint32_t g_rgctx_Action_1_tD23B61609205396E520AD96460E8264EC1A3BA75;
extern const uint32_t g_rgctx_Action_2_t56D68168A8757D04134A71B8ACFDD28C5B5A62DB;
extern const uint32_t g_rgctx_Func_2_tBA1C071F44B4A989B4B4E165365E6943950CB2EB;
extern const uint32_t g_rgctx_Action_3_t96D7C883CF8576DF33A8F1F514636C439A8F5CCF;
extern const uint32_t g_rgctx_Func_3_t449B345907CC620D5C3249AD46A5FB5EC9733F6B;
extern const uint32_t g_rgctx_Action_4_tC9C68C5F2DCAF07F9E6919E58268C57AA519931B;
extern const uint32_t g_rgctx_Func_4_t1D5F41A9C666EDCF80B51B4D13E58568C8CE5AC0;
extern const uint32_t g_rgctx_Func_5_t3B5A8C63A6C39B8ABB23890234411BB1711C27DB;
extern const uint32_t g_rgctx_Action_1_t149FE55DEA1A185277C22826A524FDA72789C3FE;
extern const uint32_t g_rgctx_Func_1_t7A910D181B361C6886AB5695AEADC7678C37C700;
extern const uint32_t g_rgctx_Action_2_t05CD7BD55F00B8BD97E52D644F9165215BC69760;
extern const uint32_t g_rgctx_Func_2_t0EAD9712B869B3FEDD9B09CA29D8F125265113DF;
extern const uint32_t g_rgctx_Action_3_t6A54FB08C05408496C5C3037AEC3DF22970A9230;
extern const uint32_t g_rgctx_Func_3_t9EA206F6BBEAB53AE00EC6480CD587ECAD42846B;
extern const uint32_t g_rgctx_Action_4_t168463A289AFD6A828B3C2FBC783D5B245217C81;
extern const uint32_t g_rgctx_Func_4_tBF32D06CCA60CBE620122291675401E956DD93A0;
extern const uint32_t g_rgctx_Func_5_t60E0E4359C0A617A0F3C4107ABBA08E8232CF39E;
static const Il2CppRGCTXDefinition s_rgctxValues[55] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CircularBuffer_1_t74731B07E4692A23030932ED37E69B4412D05FF7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tED49EDF8D2C4E3AFD9FFFDF819CBEB910D353AB1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t104ADD706E1E8C672B765D54F64026219FCED0B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tED49EDF8D2C4E3AFD9FFFDF819CBEB910D353AB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CircularBuffer_1_get_Count_m54877B3269781F998B4A0810BD2CB4C6B5C843DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CircularBuffer_1_set_Count_mBEAE70AC8DE0C0BAB275C3BA1D6B089C83AE4D33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DynamicCircularBuffer_1_tC5F3AD0EA7219C92B38FE962665E0C8AF8EFA885 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t4CF7A0220C7AEE390A3CC6D7690A28AAD7CE36A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1F7F904395BD0301CD48B948B26B7BAFEE436301 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t4CF7A0220C7AEE390A3CC6D7690A28AAD7CE36A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_get_Count_mC6DC5EB553A04244F2EBCC8B4B6288B32B6DD7C8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_SetCapacity_m403934FD2BE6F64DAAB458DECB3465BD447AEED2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_set_Count_mAF5EDDAC4680B182D010548F22EE5A7FB7D422B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_set_Item_mCCB0D9D739065CE337F1D14FDA7BCE316BAEAA15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Predicate_1_tC6099E86AE3B4491EEE4CC6CFB3B7EFC98ECFE6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_RemoveAll_TisT_t1F7F904395BD0301CD48B948B26B7BAFEE436301_m74FC1CEFC39BE51056D953510649E04CE7BFB12E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tD26595835ACBE765E71BCB8321F3DA6E23B3F961 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Predicate_1_Invoke_m5228B7C572B34068FE85D153E28299BE008146E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m6CFCC08620D93F9DAB5458C6C01E0061E600EC46 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_TrimEnd_mF71B2AA0F73856462AF59C69C75EC7DA5B48DAEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tD461AA7EB0A74DE60998051345FD06141160224F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_TrimInternal_m3D44B73A36EF459166F92BB6A3D7F6B325B4F9E3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m6777485238C78DADA1E0887A5D31DD19C58DD8EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_IndexOf_TisT_t1F7F904395BD0301CD48B948B26B7BAFEE436301_m15976AF0A8933E9CD907186E0A451A61EC0FDA32 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DynamicCircularBuffer_1_t5A23AF08303EF8C96071BEBA79B9DB0EA2232BBA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_YU5BU5D_t169FA787518151E795852DC0F7A9BF17EC11EF47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Y_t1069BDFC4B8A094E98BCE3D72534A945E7FC80EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DynamicCircularBuffer_1_TrimEnd_m26364DB029A8E210C5BDD1AFB697EB97FFB580A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t321AE9F1CC3DF2C5F1DF89A9F1B23B68A570A889 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t91C067E5FB5B976462A68888FD66FE6DFE53C7F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_tFD32DCEE983C95F090D4C5727C2F8C43A130EDAF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tE6AE110FB656A15D6AFBA1DA14BD5854F7274897 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t6E4CF313FA9F19EADB7F51A19E827D77840FE264 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_tB0D4F0134E07AC16701719B5C3CB5BF930AE1518 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t4C4E82ACE884E5ED1748B192241783A866B02E36 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_tBD95322D947294E12273872A97AD40E6095B47E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_tEA6340AB9BE60D5A291B3C59B032D2DF41640EB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_t5A8B0B5526CA5CB6777FF121F0645EE7FA050CAA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tD23B61609205396E520AD96460E8264EC1A3BA75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t56D68168A8757D04134A71B8ACFDD28C5B5A62DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tBA1C071F44B4A989B4B4E165365E6943950CB2EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t96D7C883CF8576DF33A8F1F514636C439A8F5CCF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t449B345907CC620D5C3249AD46A5FB5EC9733F6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_tC9C68C5F2DCAF07F9E6919E58268C57AA519931B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_t1D5F41A9C666EDCF80B51B4D13E58568C8CE5AC0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_t3B5A8C63A6C39B8ABB23890234411BB1711C27DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t149FE55DEA1A185277C22826A524FDA72789C3FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_t7A910D181B361C6886AB5695AEADC7678C37C700 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t05CD7BD55F00B8BD97E52D644F9165215BC69760 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t0EAD9712B869B3FEDD9B09CA29D8F125265113DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t6A54FB08C05408496C5C3037AEC3DF22970A9230 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t9EA206F6BBEAB53AE00EC6480CD587ECAD42846B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_t168463A289AFD6A828B3C2FBC783D5B245217C81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_4_tBF32D06CCA60CBE620122291675401E956DD93A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_5_t60E0E4359C0A617A0F3C4107ABBA08E8232CF39E },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationIngameDebugConsole_Runtime;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_IngameDebugConsole_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_IngameDebugConsole_Runtime_CodeGenModule = 
{
	"IngameDebugConsole.Runtime.dll",
	278,
	s_methodPointers,
	5,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	29,
	s_rgctxIndices,
	55,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationIngameDebugConsole_Runtime,
	NULL,
	NULL,
	NULL,
	NULL,
};
