﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void SharedBetweenAnimatorsAttribute__ctor_mFB3E61611EB2549C557CE050B35CE8F65B51C918 (void);
extern void StateMachineBehaviour_OnStateEnter_mB618EFE75A50CBAA3EE6471E64A3E2CA2A2C90FD (void);
extern void StateMachineBehaviour_OnStateUpdate_mC1A83A4F693AF3AB51BC592A0CE525CE4F320D6B (void);
extern void StateMachineBehaviour_OnStateExit_mC113F0B2F53847F9A6755B82D0AC53C971171CFD (void);
extern void StateMachineBehaviour_OnStateMove_m7229D5EFBA432665B9046FC3C21D463FFD281978 (void);
extern void StateMachineBehaviour_OnStateIK_m310C17694D8D1B9D60D549259A39837F22FD3240 (void);
extern void StateMachineBehaviour_OnStateMachineEnter_m0CEFF9E4946BFDC4F7066BEB4C961169DBC5073F (void);
extern void StateMachineBehaviour_OnStateMachineExit_m384B808E3961C6C2C375DF7487EF2B49E44E6CD7 (void);
extern void StateMachineBehaviour_OnStateEnter_m491D81A9A64DE4AE02415A5909B74AE947EAE1B9 (void);
extern void StateMachineBehaviour_OnStateUpdate_mF3130BE7BDD7C8B2470303FB1986A336E47CC98C (void);
extern void StateMachineBehaviour_OnStateExit_mD47A506ACE251A6341115CBE5607D05C01747127 (void);
extern void StateMachineBehaviour_OnStateMove_m1A01C10E754426572C7BBA7AA13044FDA372FDFC (void);
extern void StateMachineBehaviour_OnStateIK_mCE3B4C71868B564EE6BE4B8663535058705C3B72 (void);
extern void StateMachineBehaviour_OnStateMachineEnter_m0399B12419A4F990F41BD589C833E2D2C0076762 (void);
extern void StateMachineBehaviour_OnStateMachineExit_mF8BB1A8851B0699FC1D85F538E16EF12C08BBB93 (void);
extern void StateMachineBehaviour__ctor_m9663A75D1016E16D7E3A48E2D4E6466A041A00AB (void);
extern void Animation_get_clip_m6041709D3BC8EA54D2D65B9560D2B4E51F78BD51 (void);
extern void Animation_set_clip_m631E1DE3F46E47782725C52E444CBB747EB83119 (void);
extern void Animation_get_playAutomatically_m9438E14979AADF7FF46E20DDA9E3778010DC2CBB (void);
extern void Animation_set_playAutomatically_mF293663249E24B5D595F669515551F06309C96D0 (void);
extern void Animation_get_wrapMode_m04458F869BF815491DECA59B8AF252DBB240BF0E (void);
extern void Animation_set_wrapMode_m8910BC560328BD5541332A0737CE1F784B1B1600 (void);
extern void Animation_Stop_mE11F232FCFA0219EADD8080D7AD7CC626A536756 (void);
extern void Animation_Stop_mC7063EBC09142C002FC225F5B16051FA8ABDAE65 (void);
extern void Animation_StopNamed_m3D7C41081019DCBF820B002CCC7368B34182AB5B (void);
extern void Animation_Rewind_m2DFAC82FDF28BE4531009381C5444C6A0977D51D (void);
extern void Animation_Rewind_m41BCBFFCED0B8FA122A6543FE2A7C7520847FC78 (void);
extern void Animation_RewindNamed_mDE5F5DE24A291E3B8B385555C7F7954EB3DB2211 (void);
extern void Animation_Sample_m651BFFA298E1B72385B0DF85240AFFB8B7778E9C (void);
extern void Animation_get_isPlaying_mF0C19818820AF3FC7BD4B335B86D11043A64F3F9 (void);
extern void Animation_IsPlaying_m571DF72FBE485EC4A61364C1C45548B79C1FEF7B (void);
extern void Animation_get_Item_m60997A8CDE7F415FC55FBB0D6D3F28339C4B32E8 (void);
extern void Animation_Play_m717560D2F561D9E12583AB3B435E6BC996448C3E (void);
extern void Animation_Play_m5E86FA3D4C8C7F144565B6E3962FD5CF3E51F1C0 (void);
extern void Animation_PlayDefaultAnimation_mD2F0091CD4214603FE368C658A3DB28A53D6D48F (void);
extern void Animation_Play_m95CC43633DC2B587AB3A0D1FF5F93B863A5440D5 (void);
extern void Animation_Play_m894966605BBBE66B5D4AEEB52688BAFD7DA7DAA3 (void);
extern void Animation_CrossFade_mFE7ECBC7CB8A95ADE8E1EDC3E668D014BF9B12C0 (void);
extern void Animation_CrossFade_m0DFC263F0CA45915C28C648B652A4903AE5DB9BA (void);
extern void Animation_CrossFade_mA2C79723F05536F1A418DF43A06E7BA9F5D07EC3 (void);
extern void Animation_Blend_m90E0B5AE10B2E3248B3CAAD7864EB6B211C5C198 (void);
extern void Animation_Blend_mE7BF5EDC1AEAD0AF5E5CF364ED1F04404330E300 (void);
extern void Animation_Blend_mBC6733B3BFADCE9E55A4D9824A442B1AFB14F64E (void);
extern void Animation_CrossFadeQueued_m87F57207443217445390E1FA27084D18D6CC2DD3 (void);
extern void Animation_CrossFadeQueued_m68E472A3970210368C8CD486758A88FCDF9372DA (void);
extern void Animation_CrossFadeQueued_m7C790058752B686DD7A38789A60D4E4DDE97E316 (void);
extern void Animation_CrossFadeQueued_mA3F2202CC65B1351B3BF69CB02F2BF92651D7627 (void);
extern void Animation_PlayQueued_m0A0FDD3B2F7F96403B316F1613F9D8ED19ED187A (void);
extern void Animation_PlayQueued_mB9FDFAE88B0380EF87785B64CC193FB005F2F353 (void);
extern void Animation_PlayQueued_mB93AD01FF0279C3DDBBD1CFDBBBE34686C29B9C4 (void);
extern void Animation_AddClip_m10EDEAA1060E0356379BFE9AB24F97C9EBBED1A3 (void);
extern void Animation_AddClip_m0DBAB71E23EA248D6A18C705B8AF0EA140D2FFDF (void);
extern void Animation_AddClip_m87A5BE2BFAC7F12D7C0FE5D954E657FDB8B4A7F6 (void);
extern void Animation_RemoveClip_m997A0DB72E4FFD958A4D202B1A7C1EBE52951BFF (void);
extern void Animation_RemoveClip_m3695CFFF3622244B90CFAAEC2894D49244E53235 (void);
extern void Animation_RemoveClipNamed_m0793ABC3F9165DB34CDA9B80215FBBA4415A60DB (void);
extern void Animation_GetClipCount_mD0A710E117CDB762F2FC1D355B16C7E2BE13FEA3 (void);
extern void Animation_Play_m3DA43B73CDCC3DA363336806324CDEEC35A1B265 (void);
extern void Animation_Play_mCFAE2E11CFB3F0ADE81D69CB0CF46B895A3A55E6 (void);
extern void Animation_SyncLayer_mD252771EB0E53FDDCC6502B63347B066E1280E64 (void);
extern void Animation_GetEnumerator_m247062F212D9C579DAE25D9BC86E4921DC5719E6 (void);
extern void Animation_GetState_mFE0B2A4F4BD7F3DDE2CA699D6036607F0E7259FB (void);
extern void Animation_GetStateAtIndex_mA06564CBB11021A3ADA69EA0BCDCD820183E240F (void);
extern void Animation_GetStateCount_mB779E6750180C77CE5F2E81B78C9AFEE93FCB1FE (void);
extern void Animation_GetClip_m566FFEC9EA0EA79DB1BAC0B79F37CD6A3A85C296 (void);
extern void Animation_get_animatePhysics_mF4024F79B0F6C900F54DF64DF91F42D77F4F2A9D (void);
extern void Animation_set_animatePhysics_mF4ADAE48947F9D6D4DB080ADA3325167573C1AE2 (void);
extern void Animation_get_animateOnlyIfVisible_mE6624DC390977EA5850D1413654B68DEA0C075EA (void);
extern void Animation_set_animateOnlyIfVisible_m7521A2FED44DD8195D95BD74B9BFAA181D5A9B65 (void);
extern void Animation_get_cullingType_m7BEBA3508BAC7044A73F77E22B0A88FA4E6143DA (void);
extern void Animation_set_cullingType_m908E15BF5DDBE9AB2A078ED1AC22268EA978022E (void);
extern void Animation_get_localBounds_m654E42F57C4CBCEA408C787A8CA4E05C84D79887 (void);
extern void Animation_set_localBounds_mBFD11C7C4DB8CB5D1045B8623993AF7DD6B26DF3 (void);
extern void Animation__ctor_m7FFA60C35B9EA42BCE1916D4E8ACEFAAA373AE51 (void);
extern void Animation_get_localBounds_Injected_m4F88940400CD5D879240372F7A7979DABA43FE14 (void);
extern void Animation_set_localBounds_Injected_m0575C70CA5F2401CB5D3F344945917E07F7DE47B (void);
extern void Enumerator__ctor_mF3FB66377864673B8DAF14A36DB6D069B98A92F4 (void);
extern void Enumerator_get_Current_m4A17FE0020D4C8856EFC8EF531B99723FFB2B9DF (void);
extern void Enumerator_MoveNext_m82477C155D4F7CF98317C0EEC9F5070D196AA671 (void);
extern void Enumerator_Reset_m687381C45ECBBD052447FAC06ECE9760FF23DA63 (void);
extern void AnimationState_get_enabled_mF5CDE001664E72B2A0E3365E97E32850E81AC42A (void);
extern void AnimationState_set_enabled_mBB6FCF86704CDD80E4B3385231D4C9F400D7C6EB (void);
extern void AnimationState_get_weight_mE259E6DC21FC42FB3D73D7B339660DBCE1035949 (void);
extern void AnimationState_set_weight_m9F93DB2215185BBB1CE1ECA43DE7DE7C756AC847 (void);
extern void AnimationState_get_wrapMode_mECE5D8F9742A051006658BAB4CA4ACD30F15AD79 (void);
extern void AnimationState_set_wrapMode_mBA7403836E770538E76C12676E18AC80D707F452 (void);
extern void AnimationState_get_time_m2EA9A93D1AF7457F6CBB96B4EC0B57E6AE58D20D (void);
extern void AnimationState_set_time_m0310DBBC8065675B653D4A34019AD7CD748C4D74 (void);
extern void AnimationState_get_normalizedTime_m98D373BA809DD9486D7042CE236198C0C69007E3 (void);
extern void AnimationState_set_normalizedTime_m80C40785819379D8B3DD64D22E88338921ED5085 (void);
extern void AnimationState_get_speed_mAE9654B13C4C5420F393AA34A5B1FC86F8DC4159 (void);
extern void AnimationState_set_speed_m9FADB34E8FA313E07ABF8F90B6F4179D2DAF9E7D (void);
extern void AnimationState_get_normalizedSpeed_mB48DB49BE1D141B0A0489904CC5E137F1DC24033 (void);
extern void AnimationState_set_normalizedSpeed_mC420AC9B7F9D4B386203979F560C32CC2F31F58B (void);
extern void AnimationState_get_length_m67C34EA47A3715AE3B9B0B91816E31A87C8AE11C (void);
extern void AnimationState_get_layer_m78FFBADF5F4663215D1FE513373E2381F3BFAD00 (void);
extern void AnimationState_set_layer_mBB3CD27A27F255CE979B30FDF56E5FD0DCF9262C (void);
extern void AnimationState_get_clip_mDF1793A0E862BE0534E3099FAE98CACCE8F6BF61 (void);
extern void AnimationState_get_name_m4A81461268F1D1011331B3B52340C306DEBA0FBC (void);
extern void AnimationState_set_name_m9E3EAF585F7FAF6C39BE01AF95321A5697532980 (void);
extern void AnimationState_get_blendMode_m57FE9878CB3594C634C1BAEC6AD495F52D55EFD9 (void);
extern void AnimationState_set_blendMode_mFC6DD1BB0FB6942A0C597DFE277587E10EB41311 (void);
extern void AnimationState_AddMixingTransform_m6ACC6579170595D781435A4B8BE32BB33C9BB4BC (void);
extern void AnimationState_AddMixingTransform_m7F034D92A5317EB18D0A4B2B0AFC9767F0E151E5 (void);
extern void AnimationState_RemoveMixingTransform_m38A90D11BC7E64E5868192C4EFB07EBC1712868C (void);
extern void AnimationState__ctor_mB18C82530ADA40EE3BC245AAB0673941D78B779E (void);
extern void AnimationEvent__ctor_mBC954085B1D18B436D08E7ADE3458B91E208F3B2 (void);
extern void AnimationEvent_get_data_m143EE194CFC6C7EB4311C8B87939C4FB1895A2B3 (void);
extern void AnimationEvent_set_data_m4417F0417790DC89BDABDAADD0051E3C2B5CADAD (void);
extern void AnimationEvent_get_stringParameter_mBD37923DF24A4D090FFA56C6FE2538D1815EBFE3 (void);
extern void AnimationEvent_set_stringParameter_m5FD50F59D597512EF20D8BBA4F37213894908A2E (void);
extern void AnimationEvent_get_floatParameter_m00CC143874DF66921808693FE6E56D27AAC07642 (void);
extern void AnimationEvent_set_floatParameter_m722A753CA562F5F228A8DDC719BF8881A245BB0A (void);
extern void AnimationEvent_get_intParameter_mE1621E76D06F7ECC1F15E7A4ADE2C3DAF2EAFBC1 (void);
extern void AnimationEvent_set_intParameter_m31FC71AF73518F56A3ADD0B5BC9C5F44760D0526 (void);
extern void AnimationEvent_get_objectReferenceParameter_m74F6A7AC3CE4FA8FF01F510506CB97EE261614EB (void);
extern void AnimationEvent_set_objectReferenceParameter_m4781A06C0CFBE3DCE9EDC26B06156B5BE9E76F78 (void);
extern void AnimationEvent_get_functionName_m717A4770DD98BEFF7F64E56C55B0712BD13F479B (void);
extern void AnimationEvent_set_functionName_m61B45EF3FB59B11C185BF4529F690AA76447AC24 (void);
extern void AnimationEvent_get_time_mF3E89318E43FE3063CEAA9176133FFBB783917FF (void);
extern void AnimationEvent_set_time_m4D9B70989F587A66C23EBCDB1839B7DFAD3D3C9E (void);
extern void AnimationEvent_get_messageOptions_m9E350CE3BBE883207A5B7B658F0339DB99737B97 (void);
extern void AnimationEvent_set_messageOptions_m269062C59516F2C976A6F811B3C4E9722A5B6F8E (void);
extern void AnimationEvent_get_isFiredByLegacy_m00CCCA61535BD4B0620D055FE4E51421F6EEBC0B (void);
extern void AnimationEvent_get_isFiredByAnimator_m933B34D40A0B7781D943D77731D93A6D6A334AD3 (void);
extern void AnimationEvent_get_animationState_m712E372AEAD7678B374A1DBBE9BAD62A36B86E9E (void);
extern void AnimationEvent_get_animatorStateInfo_m1567749054B3E202F48CE4EB95D18A61514DCEFF (void);
extern void AnimationEvent_get_animatorClipInfo_m0D763FA1A2E3CD81AC08F1F24977859AE25938F3 (void);
extern void AnimationEvent_GetHash_mD3810CAEC23D174ECEF7D6BA152B80FE73ADD068 (void);
extern void AnimationClip__ctor_m3F9B3DE671547640479DB976423EEBC689D26F79 (void);
extern void AnimationClip_Internal_CreateAnimationClip_m1410C35D6386CEA1F068C9314751D0F6A7D34789 (void);
extern void AnimationClip_SampleAnimation_mD9BD020CC922D1E818D0C2163D543CF7BBAF6488 (void);
extern void AnimationClip_SampleAnimation_m1707BCA02475EBB318BA0F12C857CF5503C30904 (void);
extern void AnimationClip_get_length_mAD91A1C134662285F26886489AC2D8E0EC79AF41 (void);
extern void AnimationClip_get_startTime_m276B72F71A7E8C761AD5232ACE232388879E7B9C (void);
extern void AnimationClip_get_stopTime_m3823D1030E16F3ADE9B646CD249F52CEFBE5FE4D (void);
extern void AnimationClip_get_frameRate_mBECE2AAD43501FB8CE2E5F4302BD2989066AF242 (void);
extern void AnimationClip_set_frameRate_m68E3FF5D77738194EFEC9279F1B3FB1FCFEEED74 (void);
extern void AnimationClip_SetCurve_mEAB88CD0C8F623EA0DEF312CF8D39DE9EB2E1555 (void);
extern void AnimationClip_EnsureQuaternionContinuity_m9D02889DCA683AA8B285BD6A26A44B8FD615A5EF (void);
extern void AnimationClip_ClearCurves_m4544088FCCDA4CA2B5B5D3336546F3893030232C (void);
extern void AnimationClip_get_wrapMode_m7D79D3FE7785D25ECCD8C16ADF289654E9BCAC97 (void);
extern void AnimationClip_set_wrapMode_mC6BA022CE7B92CE00A98FA757576847319788AD6 (void);
extern void AnimationClip_get_localBounds_m88CCD1318AAD9B01899E41BB16A4F83D15933348 (void);
extern void AnimationClip_set_localBounds_m84BA2E46FC222551E4280B4C11BDB25B5232DB09 (void);
extern void AnimationClip_get_legacy_m2ACB9171DA504B26635D0C0CFF64D5F4DEF9C82B (void);
extern void AnimationClip_set_legacy_mA24DEDCB86CB9439005B71B6916C09C751731377 (void);
extern void AnimationClip_get_humanMotion_m34334767A9BD7E78BDCF577F14AFD10B2412FE06 (void);
extern void AnimationClip_get_empty_mA55084A0DDBE57E75DC412FCEE2285626E76B986 (void);
extern void AnimationClip_get_hasGenericRootTransform_mED40404C94754A7CE75E96EF8C2CE35CB71B343F (void);
extern void AnimationClip_get_hasMotionFloatCurves_m66FC0D4BAF9A7BEC0A19DE07CCAB97217A4D1698 (void);
extern void AnimationClip_get_hasMotionCurves_m36F62751D417AC698D8D60A5C43E4EAA49B4FDAF (void);
extern void AnimationClip_get_hasRootCurves_mD871E5382B94163A7CA3B917BBAC50F0E1405B81 (void);
extern void AnimationClip_get_hasRootMotion_mF736536B45B62D56EBD402A8A033767A88CFAE3E (void);
extern void AnimationClip_AddEvent_m7678C740737DCE2CA8ABC4DDE8508B46FE3C1655 (void);
extern void AnimationClip_AddEventInternal_m263CAF515A649EF219602FDFF556BAC04B3D54DD (void);
extern void AnimationClip_get_events_m700FBB913D1FD2E715139B5C9D9D03B6D8B0078D (void);
extern void AnimationClip_set_events_m809F2620D1F000B644AB2A1903BEA342EFC33001 (void);
extern void AnimationClip_SetEventsInternal_m8CA32C344715FD2B5558547DDC7ABA4604EA6F13 (void);
extern void AnimationClip_GetEventsInternal_mF2E21B70DC3E96105B7F696767FEA06400EF805F (void);
extern void AnimationClip_get_localBounds_Injected_mC0067D51DBD55799EFCD7B1EC041DEBD1B2C7F53 (void);
extern void AnimationClip_set_localBounds_Injected_mDB992FAF16AA2D4E92AC432F1B98162EB10F1044 (void);
extern void AnimatorClipInfo_get_clip_m6205DB403EBEAEAC14DB8928FFC7EBC50142E1AC (void);
extern void AnimatorClipInfo_get_weight_m1CC29E2C37B30993EFFD12161059E4AD86EE287D (void);
extern void AnimatorClipInfo_InstanceIDToAnimationClipPPtr_mF656EAD29AB800127963F8A663F260E89EBF2CEC (void);
extern void AnimatorStateInfo_IsName_mB936F493D6BDDB9372C8E9D813CE0416B002C4D0 (void);
extern void AnimatorStateInfo_get_fullPathHash_m583FA8FAAC28BF65A65166D100949833E515210F (void);
extern void AnimatorStateInfo_get_nameHash_m88E91C33AA5602324A7319D7A51F552D00B14D4A (void);
extern void AnimatorStateInfo_get_shortNameHash_mEE816B999C282A3BA95AFC64278B994E899B7004 (void);
extern void AnimatorStateInfo_get_normalizedTime_m087C7E5A72122ADF18EBB4AC8391103B9119CCC6 (void);
extern void AnimatorStateInfo_get_length_m2FAE317264F7C796427207F8F28E550DB49F9541 (void);
extern void AnimatorStateInfo_get_speed_m473826E53D827AEE36A7FF0662AC4817C2EF3495 (void);
extern void AnimatorStateInfo_get_speedMultiplier_mA078AADECC98C266C82E2756A351235DDC63A107 (void);
extern void AnimatorStateInfo_get_tagHash_m3F4738079576820B7D5942854882B2B468CDD55A (void);
extern void AnimatorStateInfo_IsTag_m9A3181AA167702263EB283AF27B21D08EAD895EF (void);
extern void AnimatorStateInfo_get_loop_m3DC728FC9AF0D4B27B3C28157395BB2F57CC3DA7 (void);
extern void AnimatorTransitionInfo_IsName_m6C0C8BBF7E241EFEE2199D5D97DC59958BFBE324 (void);
extern void AnimatorTransitionInfo_IsUserName_m91FDB1462C56FCDB3F9A209020D2014B06833DBE (void);
extern void AnimatorTransitionInfo_get_fullPathHash_m3C358272C30F5AAE76547585AA6C3D866F6F77AE (void);
extern void AnimatorTransitionInfo_get_nameHash_m31EC38373C30F6A8BADA9AD27EBAB6BC5B9185CE (void);
extern void AnimatorTransitionInfo_get_userNameHash_mBAD5D1898CE34F1E0657D6BBEA18C4A35EC686EE (void);
extern void AnimatorTransitionInfo_get_durationUnit_m7E41A2E75B4DBD836E1BC55FA933CF199203B7E1 (void);
extern void AnimatorTransitionInfo_get_duration_m15DB72ECD67D569CA85DA4CD46E4C92677BFF8B9 (void);
extern void AnimatorTransitionInfo_get_normalizedTime_m0D107F16FB8351EBB0E8F8A4367A69916E260072 (void);
extern void AnimatorTransitionInfo_get_anyState_mDF1EC0E1F99B7998D19720BC5AAE4B7A31499273 (void);
extern void AnimatorTransitionInfo_get_entry_m56D5DF6A01AFBAA8611505369333083DA652DB13 (void);
extern void AnimatorTransitionInfo_get_exit_m32F0BA8C87C7487F27A3BCE5A3B490DEB67AC80E (void);
extern void MatchTargetWeightMask__ctor_m381E3F8A3BA8447D8E9CB084E785AB2CDD38B96B (void);
extern void MatchTargetWeightMask_get_positionXYZWeight_mA92CEAD4501F10E0B8F1177C4A1C1B60A1CE644B (void);
extern void MatchTargetWeightMask_set_positionXYZWeight_m59F98F90D7089D61AD3B375E788BEE5D78753E0B (void);
extern void MatchTargetWeightMask_get_rotationWeight_m5FDC8E88D3A7E6DC0DD51F988F0F172FD8C84C88 (void);
extern void MatchTargetWeightMask_set_rotationWeight_m6E9398D9F0017122E85576DE6482A17E2C8A15D2 (void);
extern void Animator_get_isOptimizable_mEFD0F96698EF9ED2D9DB8A4DA795E78EF48088C2 (void);
extern void Animator_get_isHuman_mE1154471F516DA8BB47B0605410640344028E2A0 (void);
extern void Animator_get_hasRootMotion_mA39728839F19F4E103CF2599281E6F796D2AB341 (void);
extern void Animator_get_isRootPositionOrRotationControlledByCurves_mC87A9D341EA194B66C41198B0273589EB6B3605A (void);
extern void Animator_get_humanScale_m4E9B17E752307329C2F19BB1CFEDB6180976F7B6 (void);
extern void Animator_get_isInitialized_m22DA9FAA80F43CBADDD6CDA73BAAE49752E03806 (void);
extern void Animator_GetFloat_m10B455A15EB343175518CCBEE2818C2497CC678A (void);
extern void Animator_GetFloat_mEFA968AD9EEB92E6A03AEEB968600798E61F9B1D (void);
extern void Animator_SetFloat_m10C78733FAFC7AFEDBDACC48B7C66D3A35A0A7FE (void);
extern void Animator_SetFloat_m018FC1B8BBA989887545ABEF5FB611087F23A4C0 (void);
extern void Animator_SetFloat_m2CDA219BBAB214F4069C9844780EBCE6CCF579F5 (void);
extern void Animator_SetFloat_m3FFF291B541476DE7EB1D6432169ECDCBEC7FCD2 (void);
extern void Animator_GetBool_mBC4D952885FF7504963E5923C29481A891028FD8 (void);
extern void Animator_GetBool_mFEC9C0565C52965FBD5D6A02084484F00F8ECE2C (void);
extern void Animator_SetBool_m6F8D4FAF0770CD4EC1F54406249785DE7391E42B (void);
extern void Animator_SetBool_m1DD34A313E6882B6FBF379A53DD8D52E4023F1D8 (void);
extern void Animator_GetInteger_m8DA635E4373A1E7D649B2BA2DEF382D87EFE603C (void);
extern void Animator_GetInteger_mF4E1564D3791C968FA8952954B5AAC4C4E2C8B91 (void);
extern void Animator_SetInteger_m7B4BB5FD5BD8DE3F713D8E9AD2870C1AAF8E19EF (void);
extern void Animator_SetInteger_mE823EC7492A7FB266FA723C168226D17085DF3E8 (void);
extern void Animator_SetTrigger_mC9CD54D627C8843EF6E159E167449D216EF6EB30 (void);
extern void Animator_SetTrigger_m2D9CACEFDE11FF9DB99207B5CBD251C1EC047939 (void);
extern void Animator_ResetTrigger_m8DCA67D5A6B56702E3FAD4E18243E194B71297CC (void);
extern void Animator_ResetTrigger_m2DF2C6DE87314918C151616FD5C39EB93EE14011 (void);
extern void Animator_IsParameterControlledByCurve_mC65B475495D9A4B01BF5BD33E2899E87245375DE (void);
extern void Animator_IsParameterControlledByCurve_mE62E4CAE2725D8AD214D59FF459F63157818C7DF (void);
extern void Animator_get_deltaPosition_m1759DB2D343FDAFAA7FE85ED66451D5210B0A606 (void);
extern void Animator_get_deltaRotation_m54839D596F81D477E91E2746E4141AB389238FFD (void);
extern void Animator_get_velocity_mBEC30469C1011C8C92E7991D039D06945FF263BA (void);
extern void Animator_get_angularVelocity_m69E6F9C74F6E54F43BBB945E0E8F71CDAF4820B1 (void);
extern void Animator_get_rootPosition_m952907486F631E7E8E0384AE7CE86D92BB3C6E71 (void);
extern void Animator_set_rootPosition_m29463566F2F73908602CFB2A59B86310A844286F (void);
extern void Animator_get_rootRotation_mE913E618AC95BE36524C88A77D087E67579FAF46 (void);
extern void Animator_set_rootRotation_m916D8E03CAA17B4BD68343BE6CDA3307A0E43B3F (void);
extern void Animator_get_applyRootMotion_mC8C4D583F86A9E0B1BF395BBFFAACACD496EDBB8 (void);
extern void Animator_set_applyRootMotion_mA0953B6AEE43D4AF0837365E7BFF60FCC74B0F98 (void);
extern void Animator_get_linearVelocityBlending_mD41FD1EE51B7E464CE370EB70F2DAF4546DF9A7F (void);
extern void Animator_set_linearVelocityBlending_m1F47CE5FC0467A772EBD2F4610BBD6B45F92FB2D (void);
extern void Animator_get_animatePhysics_m4419359F5EB230CF2C6BA7986529E3F08FFC3BC6 (void);
extern void Animator_set_animatePhysics_m5701A0DE6FE0D29B4746B9A1CDA307FD1E76C819 (void);
extern void Animator_get_updateMode_m63C8A41D5D0F214FB5CF554B69CFBBEB6EE141DB (void);
extern void Animator_set_updateMode_mA21CC888FEEBC5A06099E5D33A6C7ACCC266B056 (void);
extern void Animator_get_hasTransformHierarchy_m45669F672E3EFC79FCCE1179BE17B9F8095FB946 (void);
extern void Animator_get_allowConstantClipSamplingOptimization_m21D999899FCFEFB076185D84675EEF7415604080 (void);
extern void Animator_set_allowConstantClipSamplingOptimization_mD15A77BF2EDA9211776455E41D0F41F2685D7AA5 (void);
extern void Animator_get_gravityWeight_m5414ADEF41113A6B7C8FAE281578DA1DD607CB00 (void);
extern void Animator_get_bodyPosition_m0372578D2966019ED1CE5F3AEC1DE6F74DC6EADE (void);
extern void Animator_set_bodyPosition_m7CFDA1C974A11D5BFC52181879A25FA8AEAFFC61 (void);
extern void Animator_get_bodyPositionInternal_m595442E15D03EA53B40C2760C99B4D52C536EF06 (void);
extern void Animator_set_bodyPositionInternal_m6042D23A6CC550D438B4522C4755BD08692D2F1A (void);
extern void Animator_get_bodyRotation_mAC0517419CDE876795D7AD59253E3B4F8628091F (void);
extern void Animator_set_bodyRotation_m8763282BEFDD626963D985435E3BC3FE576E27BC (void);
extern void Animator_get_bodyRotationInternal_m62411259235851629AB914E29B04029A04D8F165 (void);
extern void Animator_set_bodyRotationInternal_mA381F771D03290AF383D3203071D9B04746BD2E6 (void);
extern void Animator_GetIKPosition_m626E887B0E039C8F79D0A942D469880B05A58962 (void);
extern void Animator_GetGoalPosition_mE65C3AB94643D8036BF6C8014408CAB0F9BB5A98 (void);
extern void Animator_SetIKPosition_mB7BE88C93990186D94AF75439E2F216D6ECBCDEE (void);
extern void Animator_SetGoalPosition_m0811DE827A66638ECA79A2BAA6A59D9D21D7D5A2 (void);
extern void Animator_GetIKRotation_mF2969E5FBD59C125EF95AB689A9B3AAB434F79FC (void);
extern void Animator_GetGoalRotation_mB7B67DE4EBA3C26D713754D1D76D4F529E783DB2 (void);
extern void Animator_SetIKRotation_m328A64AD20922F0F2A1D0CD5DBB9F01FE7675DF6 (void);
extern void Animator_SetGoalRotation_m1BFE2425DE75954B1233ABD96D6AB3682D42E5BA (void);
extern void Animator_GetIKPositionWeight_mA79A863ED8F5E8095FCC9545CCB55483328B3D99 (void);
extern void Animator_GetGoalWeightPosition_m0E626A12D7B157A0B17D231D5E6175D062937E6A (void);
extern void Animator_SetIKPositionWeight_m8C707F505FFE2A6F36BE81ED12786B941D3B990C (void);
extern void Animator_SetGoalWeightPosition_mBCC0ED516684541F6AD9B784ECCCA2D7C14DC75E (void);
extern void Animator_GetIKRotationWeight_mC376AF1B02A0C1D15072AFE215B9D371B09D3059 (void);
extern void Animator_GetGoalWeightRotation_m10C6574D1AF23568F737D0347F35A2B5BE670725 (void);
extern void Animator_SetIKRotationWeight_m5F0F5BD5A9A85912EA1CDF32917FE483E849978D (void);
extern void Animator_SetGoalWeightRotation_mA8A5BE4C22583CFB7C66EF59A1997E241A3001EF (void);
extern void Animator_GetIKHintPosition_m6FDF4F6FBFD9936686F514907126422A555BE020 (void);
extern void Animator_GetHintPosition_mD97BF99EB5303EAD560BFE087E07AC44E1FFF42E (void);
extern void Animator_SetIKHintPosition_m834ACA024C8824FD458A8F3A55AE40819A660A2F (void);
extern void Animator_SetHintPosition_m446EFD046A753C0604A5487D9606F89A362653E2 (void);
extern void Animator_GetIKHintPositionWeight_m2DE07F51712E11FC8D929A149D70FE40498CD75D (void);
extern void Animator_GetHintWeightPosition_mE89238F7EB821743F245E2F9EA4BC8D3B3629830 (void);
extern void Animator_SetIKHintPositionWeight_m3F123E4F07C30ED041B3BB0616CFD9D9460910E9 (void);
extern void Animator_SetHintWeightPosition_m5BC635B929CACB43C9D5DE73AF292A423B8BF0ED (void);
extern void Animator_SetLookAtPosition_m39A2C326BDE156360972C5EEDA1F9ACEBE34A8A6 (void);
extern void Animator_SetLookAtPositionInternal_m6A20F2667C36D8363BA0F38A961BAB941E2DDC1B (void);
extern void Animator_SetLookAtWeight_mDCC8C8792E2E23C133AF3D91A96C49BEBC828F79 (void);
extern void Animator_SetLookAtWeight_m0C4D297E270F7E852481EFBED321020E96EBD54D (void);
extern void Animator_SetLookAtWeight_mD0D92A24E3789891D709E0DCD84524FAE614F870 (void);
extern void Animator_SetLookAtWeight_m31AE829F22900AD67FA4840D488E1FA9E0880FB2 (void);
extern void Animator_SetLookAtWeight_m536B18C7EC83703CF0320924D9FE13CBD93CC752 (void);
extern void Animator_SetLookAtWeightInternal_m41D66B6DA78B1ACB7EE3E3B51A29904DEB05778F (void);
extern void Animator_SetBoneLocalRotation_m8294EB788C2BB23708025F5E0246061827321E6D (void);
extern void Animator_SetBoneLocalRotationInternal_m29A9991C7C688D3E2B6747C769358F4C935095C7 (void);
extern void Animator_GetBehaviour_mC89D3A58B66C9579FF8980C1F06F518A8C2E489D (void);
extern void Animator_InternalGetBehaviours_m53FE16BAE89AAB75F82132643848FCBFDD441BFE (void);
extern void Animator_GetBehaviours_m0CB79D09176BD9416D24A19702EDF37575BD9048 (void);
extern void Animator_InternalGetBehavioursByKey_m6B3048864D1DB2483F5298842E9384599FBB8E9F (void);
extern void Animator_get_stabilizeFeet_mB1C363699D45D67276ACA07C79FB257E1BB4F5DE (void);
extern void Animator_set_stabilizeFeet_m4877796D25F5D046BA6FEF8CFDFEB470620801A0 (void);
extern void Animator_get_layerCount_m75C20E7284800DFA0E4AD7EC23910BDB6D62BDA8 (void);
extern void Animator_GetLayerName_m7458E91DE0B0769E79038DFEF32B750BE6801BA7 (void);
extern void Animator_GetLayerIndex_mE57FA9E3A7B3308B07B79307B5BCE0688AF4B38B (void);
extern void Animator_GetLayerWeight_m24EB83CB3CFC724CB77461A26BB794E7DEE15BCA (void);
extern void Animator_SetLayerWeight_m06ADC732F76F22B4B1424F25525E7CBB80E6230F (void);
extern void Animator_GetAnimatorStateInfo_mC6C046A539DE6E8D481E830D3DA1FBF96DFC367D (void);
extern void Animator_GetCurrentAnimatorStateInfo_mD5B526FA605F6CFBC31B7DE36740F6AD1E534CAD (void);
extern void Animator_GetNextAnimatorStateInfo_m4661892C3F99329907EE3EE1C1FCB7974CDA2433 (void);
extern void Animator_GetAnimatorTransitionInfo_m933890D15E832719A2DBBAFCA587FEB4C7F1C6E9 (void);
extern void Animator_GetAnimatorTransitionInfo_mAB532C0834DEF3685C6E16C82B7A93B8875FC542 (void);
extern void Animator_GetAnimatorClipInfoCount_m83C02F470FEC1DAF548AB88C578B64B6B3BB8E9B (void);
extern void Animator_GetCurrentAnimatorClipInfoCount_mB506E49EC0B2DD764D78A21D6AE55B4E9AAB9AE4 (void);
extern void Animator_GetNextAnimatorClipInfoCount_m079742F61B3A1616C4419C82F91B84BD9D5613CC (void);
extern void Animator_GetCurrentAnimatorClipInfo_m963412D4118C301408B2EAFEF1E1CB5E971D5D92 (void);
extern void Animator_GetNextAnimatorClipInfo_mF7B457B637128DFED6BAC15CBB45A9FE75205CFE (void);
extern void Animator_GetCurrentAnimatorClipInfo_m7C061EE420A67B7D751A2F34D38062FDFF064ED3 (void);
extern void Animator_GetAnimatorClipInfoInternal_m98028420AD065F1B6F3DC2EB4C6C2A42B8567600 (void);
extern void Animator_GetNextAnimatorClipInfo_m376D90AA469A7F7268B8D0E5B6930CE7095185D3 (void);
extern void Animator_IsInTransition_mC2BD2CC7B7A11BAAA5396F1A2DAFD98D00AA2830 (void);
extern void Animator_get_parameters_m3DE35688D8EBD3D1526346C7B71E468436A4463B (void);
extern void Animator_get_parameterCount_m56BDA9B508904594E1B2317A451AC3E40A12943C (void);
extern void Animator_GetParameterInternal_m1DEF19C5B91BE9602E482FEFEA927E9218C081A8 (void);
extern void Animator_GetParameter_m0F1E53A9845596EA75366518BEB15733FD4E124F (void);
extern void Animator_get_feetPivotActive_m6905716868666F1A61574A07DB455482CBC7B6CF (void);
extern void Animator_set_feetPivotActive_m0683AF2FD4B62EB9330B7BC347BE189A90809C19 (void);
extern void Animator_get_pivotWeight_mFFD3DF6F4506AA829E3E6D726F21EB62EC6A4652 (void);
extern void Animator_get_pivotPosition_m2A6E669BC43633E12BEDF96ED73AB86EAACBA11B (void);
extern void Animator_MatchTarget_mD1C55BE6B64912264BD1280498BFF938B8501596 (void);
extern void Animator_MatchTarget_mBDD980442EC44131EFEED1EDF4FEFEE11B909576 (void);
extern void Animator_MatchTarget_m717CF5422596B401395BE3EDC510FFD34F85D337 (void);
extern void Animator_MatchTarget_mEFA2D3248BA083F5FA823D83B38E2306336293D3 (void);
extern void Animator_InterruptMatchTarget_mB031E84E81E760C7A5862324B868E76B030F8EF2 (void);
extern void Animator_InterruptMatchTarget_m3BD713C829A921D66628C6B18F00E6F75EE327A7 (void);
extern void Animator_get_isMatchingTarget_m56CCA705025C2057D60F5BB9724F864C8B2223B0 (void);
extern void Animator_get_speed_m41AFD6B0AB3FF4FFF8855CCAF684813BA1148CD2 (void);
extern void Animator_set_speed_m933F4D6770122BC9D8A7FF82DE1CD33D514379FC (void);
extern void Animator_ForceStateNormalizedTime_mFCEEEFD9CD18C26E7128691FCD758690E87AC305 (void);
extern void Animator_CrossFadeInFixedTime_mF3F5670768653192DE1784DDAD591E73030F80AA (void);
extern void Animator_CrossFadeInFixedTime_m7F12206926BAF74D1385F1F8BAD36CBD3FDA4A51 (void);
extern void Animator_CrossFadeInFixedTime_mA1271920E97C315726655B0A5C31DAE69FAC6FC2 (void);
extern void Animator_CrossFadeInFixedTime_mD3A92E42D472130EE6948AFD9C2374334B6899B0 (void);
extern void Animator_CrossFadeInFixedTime_mC0A7F0188309753098EFF103FFA511A4377EEC55 (void);
extern void Animator_CrossFadeInFixedTime_m0723F8EDB1F6B0CDDF1E61914BFFBF3FD14ADD8B (void);
extern void Animator_CrossFadeInFixedTime_m31C6FDFE566373C35F8931DE921CAAC4131F1D25 (void);
extern void Animator_CrossFadeInFixedTime_m93D7442691AF96BD416878312D71B8EBD248340D (void);
extern void Animator_WriteDefaultValues_m28A53BD6205B7543B56F63E841AA4F8230EDD1FB (void);
extern void Animator_CrossFade_m2D91D6C458B15B186365EED0C13A178342789084 (void);
extern void Animator_CrossFade_m2710B834A3D8C5C222B2CC6BF6D307B7A138055C (void);
extern void Animator_CrossFade_m9896DFE98F7DC35199C7FDEB22E6645B6F53E998 (void);
extern void Animator_CrossFade_mA6769671098D9B30D68A27DF00D355CC5E28D56C (void);
extern void Animator_CrossFade_m34D1F0B5D8E14FC3D685D91D412ACA3F69F16186 (void);
extern void Animator_CrossFade_mAE4ED75E850B05A529EE726E314EF15A988F37DF (void);
extern void Animator_CrossFade_m140269A1067EC377112460CAE86409FD5A61104C (void);
extern void Animator_CrossFade_m877FD25F61DCAF76F4B321153E4B37B220091C73 (void);
extern void Animator_PlayInFixedTime_mAADE5EC387F7FF8FE6846E31B1EB5A6F28CC1AAF (void);
extern void Animator_PlayInFixedTime_m51356C96303FBDAE14D5D83453424F549152E2FC (void);
extern void Animator_PlayInFixedTime_m8E57011DAD1D97B8071B18115E09FB2151154E21 (void);
extern void Animator_PlayInFixedTime_mED55F3D6ADF43C009FBD4C447126381D954C027D (void);
extern void Animator_PlayInFixedTime_m2CA86C7A32EC02BCAD4EFD0B9CD53272BF06E025 (void);
extern void Animator_PlayInFixedTime_mF3630168236FF27D0AE2212E9CFCFC3F41542C10 (void);
extern void Animator_Play_m9B1CB1A22951C0B0758AA032F2349B6DD20E96C2 (void);
extern void Animator_Play_m0F6A9F84B2E256E644D56C34A7A9BD622CB00FF9 (void);
extern void Animator_Play_m5565F093F0752D4DD9B451686F71C7A8F23F6744 (void);
extern void Animator_Play_m3011727F1A3F331EE7F5D7CF9B98EFEADE547BBD (void);
extern void Animator_Play_mA4CA3959A5DBDA56A1B20D4B0E727FE592AAAABA (void);
extern void Animator_Play_mBA96F0D84A982A338EC976A5EAFE886956AEDDCE (void);
extern void Animator_SetTarget_mCE0C0F703C38FF190E4F093B394C1D2AFA80A0F5 (void);
extern void Animator_get_targetPosition_m6A682300C052714BB55F68E192B4AD778179B43D (void);
extern void Animator_get_targetRotation_m313EC3BE0F811C7FB61C475298BABFFAA11DA1DC (void);
extern void Animator_IsControlled_m1B8BC43EC53313ECC236FF01E01B86AE634ABCBF (void);
extern void Animator_IsBoneTransform_m8D7D99FD916A153C9560961FC918B0C87846E983 (void);
extern void Animator_get_avatarRoot_m6942EF35F6F2ACDF787772D405E17C3131A5CA95 (void);
extern void Animator_GetBoneTransform_m02042CB47C468D576C6EE436F4AA71D8C47AAF56 (void);
extern void Animator_GetBoneTransformInternal_m875DC36979BC9FB162D83E799421243AD3842857 (void);
extern void Animator_get_cullingMode_m5C9C70B94075022FA6346E17CF1E9C0DEDB21605 (void);
extern void Animator_set_cullingMode_m7520115B5460495336C87393B58014F412B24209 (void);
extern void Animator_StartPlayback_m981AE4C0AAEADFE8484C9C001FDA17D5D255D500 (void);
extern void Animator_StopPlayback_mCC18E6452A6FC5F7BA622F2A7619848CF875B8D0 (void);
extern void Animator_get_playbackTime_m11289709E35A3FF5D524FC92AF4CEA00B107BD3E (void);
extern void Animator_set_playbackTime_m223512B44026FAFDA2E25A321B6B0B6F57DB56FA (void);
extern void Animator_StartRecording_mCCD8C24752A76B5487592BE766D57E6874CCC40E (void);
extern void Animator_StopRecording_mF8048A93508F6159B2377D61AF3589340A6B5256 (void);
extern void Animator_get_recorderStartTime_m9CB9520E347B1520B05052E31C4D8A701462E241 (void);
extern void Animator_set_recorderStartTime_m968547A0E4BC7849B3D7EAB1389AA9A05EC80544 (void);
extern void Animator_GetRecorderStartTime_mC9E51A2F33D7297124F3F93322E28FBFFCB392C4 (void);
extern void Animator_get_recorderStopTime_m675F4F5DC1AEDC41C456AAEE5F96F52AEA1E59A9 (void);
extern void Animator_set_recorderStopTime_m24BC4494E05B7682F3A23C5A0B0607F4D3385BE5 (void);
extern void Animator_GetRecorderStopTime_m928EDD878985FD098B38DBC89AD7FC935FF95681 (void);
extern void Animator_get_recorderMode_mB34DB4C5368F0D3BF86210F41900DF17A43B2DA0 (void);
extern void Animator_get_runtimeAnimatorController_mE10F46F893A630D1AE846EF66DC2769E3ECE5AB8 (void);
extern void Animator_set_runtimeAnimatorController_m505ACBA1D2E59814239EF3760A9F537D03301311 (void);
extern void Animator_get_hasBoundPlayables_mA5A6132C03593851FE80D8E7490191E051E5A1C9 (void);
extern void Animator_ClearInternalControllerPlayable_mBAE8D218945AF13E7ED60D7958F3155C341C8686 (void);
extern void Animator_HasState_m9E3BEAD260AAA9FD571CB0AFEDC20F278859B833 (void);
extern void Animator_StringToHash_mD67B872C411BE37641F49D7AA1DBD45B67F63E3A (void);
extern void Animator_get_avatar_m01E445FC754BC6F4A39639EAF68D3954580EAA67 (void);
extern void Animator_set_avatar_mF24D7962A24C1EBF3B949E712DDA24DB11B5DAC5 (void);
extern void Animator_GetStats_m264013CFF4D9B247F2C683188CE3E0C8571197EC (void);
extern void Animator_get_playableGraph_m118650892C13ED75C535C2A32CE09641C7E3BAD7 (void);
extern void Animator_GetCurrentGraph_mDDDDABBEA3E4BBD025401F92FFFE5B71DAC9486F (void);
extern void Animator_CheckIfInIKPass_mE7815990AFAD02738D0D5B4DA8DDB3E649D30CCF (void);
extern void Animator_IsInIKPass_m35BF844B06D845BD0C6778F3B6E87B9C0B26BA5F (void);
extern void Animator_SetFloatString_m1AA50083AAAE1DFFA7FB64502EA106795D3756B4 (void);
extern void Animator_SetFloatID_m348942A23D4C734DDF4D04C7609E5CD9EBAB66AA (void);
extern void Animator_GetFloatString_mB1E5C90624A736A6D98D30FF5318BC1D0C494A38 (void);
extern void Animator_GetFloatID_m0300DB1901B65F007EA9E0E98ACB91E105FDD366 (void);
extern void Animator_SetBoolString_m08A0BC6717BF5FD3E43D3EFB2509E359C1196C00 (void);
extern void Animator_SetBoolID_mCDA31E47A1AA78E830D0B76394BE2FA7296E03BE (void);
extern void Animator_GetBoolString_m8698CB03DDD5DF7B991EC772BC424F2A649EB950 (void);
extern void Animator_GetBoolID_mD88CBA007AB7C968BF526B518E90F90E0935A9DD (void);
extern void Animator_SetIntegerString_m3080217346B85D69FA5A99B2ABC64BF5E4580455 (void);
extern void Animator_SetIntegerID_m08431A06E5905C62BA397B9BAB30F87E3C30569F (void);
extern void Animator_GetIntegerString_m591359704EA3449A83C698CD72EBB7A6FC5F6D9D (void);
extern void Animator_GetIntegerID_mCAC9F61639DAF52DD2DE8891DB8A2F7FE5C3DF4A (void);
extern void Animator_SetTriggerString_m177C75DFBE070DE66FC08A3232444CCEA409C25E (void);
extern void Animator_SetTriggerID_mCC0A74BF79A56BC1EA634641C64B8E527B0B49E7 (void);
extern void Animator_ResetTriggerString_m78259348CED35F156148A64B95EBD73CE3951868 (void);
extern void Animator_ResetTriggerID_mD3E0C24AC862F049622D09933E7A3A2CDE256C13 (void);
extern void Animator_IsParameterControlledByCurveString_m8BDBA93917C92923DB297EBE8F141FA6C695AF61 (void);
extern void Animator_IsParameterControlledByCurveID_mA4A7685A97E37887738AA0959AC83557048C87A3 (void);
extern void Animator_SetFloatStringDamp_m81B394E6887978F4A8A92ACF1C7138B23BFC9214 (void);
extern void Animator_SetFloatIDDamp_m013A6F5FD90DFA125C152BF42F299157AEE96330 (void);
extern void Animator_get_layersAffectMassCenter_mB3DB124E8A0F57BE687F44A95986F5F0E3B999EB (void);
extern void Animator_set_layersAffectMassCenter_mED2748A798EB7DAFC9977710463E06931BA3E3AE (void);
extern void Animator_get_leftFeetBottomHeight_m5ECBAF5B6CCC20691ADA416B95AAE9192DF09C76 (void);
extern void Animator_get_rightFeetBottomHeight_m0C68C9515FF2AD560D36357A134246DADB8A3544 (void);
extern void Animator_get_supportsOnAnimatorMove_m1E4A235DE9899F2473AAB70B86BC4A4575F90521 (void);
extern void Animator_OnUpdateModeChanged_mCE2A9940D4A0772544234F12D1E49E89A2A6B1BF (void);
extern void Animator_OnCullingModeChanged_mDF02FBEF0EAB7BD09F5A6446E0CFA9DD4CC1285A (void);
extern void Animator_WriteDefaultPose_mF05891951318F68AED1FCDF5134B3F9B0F62C101 (void);
extern void Animator_Update_mBF5E8B2869FD05AF4A5963C39203D85BD62E8E65 (void);
extern void Animator_Rebind_m853F9E50ACB0A29D4F144FFD851E92F346195F9F (void);
extern void Animator_Rebind_mA8163C9B7150958C0FB3F071B7ED41850BE3A130 (void);
extern void Animator_ApplyBuiltinRootMotion_m2ED8DAFF78DC1A0CA62CF3785692074DBC51808C (void);
extern void Animator_EvaluateController_mA07519A24495A1D1B33D163005E18FC52B5954B0 (void);
extern void Animator_EvaluateController_m5A83794E6067230ED84A707365FB70CA9EE64128 (void);
extern void Animator_GetCurrentStateName_m9B1BE9CADE479A6BED0CD48387C73ED963CA9E6F (void);
extern void Animator_GetNextStateName_mCF8EED59CECEE48A04A27EA422EA29E5C4EB8A3E (void);
extern void Animator_GetAnimatorStateName_mEA694401BD4FC9185FFCFDFFCB7C1DCD2B1B8D06 (void);
extern void Animator_ResolveHash_mB349EC9252F07ECD3657818C08F66FACBAA53D43 (void);
extern void Animator_get_logWarnings_m39949DA4A177E2D107023FB0BD89DC3D6F303652 (void);
extern void Animator_set_logWarnings_m625FCBAF506C623F5457D22C5A704AC6727B446F (void);
extern void Animator_get_fireEvents_mE0311DD2BAC5EA407BD7FC02B8EC52D4E2F27668 (void);
extern void Animator_set_fireEvents_mBB2DAC3B84656758453B0BABB12DF7949EDDF43C (void);
extern void Animator_get_keepAnimatorControllerStateOnDisable_mEBEF419822017BE487B8BD5A9118C175F50A97CE (void);
extern void Animator_set_keepAnimatorControllerStateOnDisable_m0F15299D72F566184637745C2DCCCA712968FDC3 (void);
extern void Animator_get_keepAnimatorStateOnDisable_m7FAE9E72FA31D90E7D55664BD627C2903893A76B (void);
extern void Animator_set_keepAnimatorStateOnDisable_mE439995234FEE85A9DE7DB5238D301128CABB769 (void);
extern void Animator_get_writeDefaultValuesOnDisable_m25C02067B4A8BCD406B8112BF07C8DAD70D7A89F (void);
extern void Animator_set_writeDefaultValuesOnDisable_mE0E16D2592C27771C3BB66B017BF8EBB4F200A6D (void);
extern void Animator_GetVector_m8BF07F4BB4DBC9031166EE48CB5E61DD7D1F90C6 (void);
extern void Animator_GetVector_mBE09ACBB9CD91B2343B91CF4C547028CD1920BC3 (void);
extern void Animator_SetVector_mE7EA3828CF3F35015BDC012736787472773D8D12 (void);
extern void Animator_SetVector_m89C00646839A497355E264DB9E930DCC162DC3FE (void);
extern void Animator_GetQuaternion_mEF3B6820436B19567A4635C3C7D0E4DFF4A5EF66 (void);
extern void Animator_GetQuaternion_m2F43CF4CDC5D648241847A072E5C1079681B1AA3 (void);
extern void Animator_SetQuaternion_mEA2933BB9427F2AF4C0F709CC4AE67C653D6A622 (void);
extern void Animator_SetQuaternion_mBE23DCD9EA5BFFD39A9267FD0559CC7FC54C5A95 (void);
extern void Animator__ctor_m53346EED5CF6845390B4CB8F53C9CBE9C65D5CEA (void);
extern void Animator_get_deltaPosition_Injected_mF37497B00055319EE54006C19AAEE221A99791F4 (void);
extern void Animator_get_deltaRotation_Injected_mFEC802815227FE4B11491F80B281425A10E19ABD (void);
extern void Animator_get_velocity_Injected_mB47827ADE9F826A2ECA302EB7751371A9F18A57E (void);
extern void Animator_get_angularVelocity_Injected_m316C33CD3C5CF4F7AA8E53FEB0741B7E1C3D4537 (void);
extern void Animator_get_rootPosition_Injected_m7F7BA9F18B8866C40649D61A36ADFF42EDF5B88D (void);
extern void Animator_set_rootPosition_Injected_m9F65EF589C17FECC4350D1D193C20B7741BD7312 (void);
extern void Animator_get_rootRotation_Injected_m670A2B5E5EE664CA5ED9D711733C102E0BD72AFB (void);
extern void Animator_set_rootRotation_Injected_m4DF71DAF4F3EE3B4D460FAB3360E00C74027F11A (void);
extern void Animator_get_bodyPositionInternal_Injected_m44C1D9729A292329E4B4A66B1DE583368ED9B2B3 (void);
extern void Animator_set_bodyPositionInternal_Injected_m434A0C848EF277237CBAA68C4A94A1DD4A05DC75 (void);
extern void Animator_get_bodyRotationInternal_Injected_m60262D5C0C987E21A358FF7F67887C3B23FEDD5E (void);
extern void Animator_set_bodyRotationInternal_Injected_m0BDC67195DA58582AEFC3567906106BD96C1D173 (void);
extern void Animator_GetGoalPosition_Injected_m7EF59EA5A53DEE902DA1AAACD32378A32B4D67BE (void);
extern void Animator_SetGoalPosition_Injected_mE03B76DB578A3B15ED91437071BEF70F61985707 (void);
extern void Animator_GetGoalRotation_Injected_m76092248C853479A1E6178DA4AF19F69FF8C8F75 (void);
extern void Animator_SetGoalRotation_Injected_m93587602A0C43AC9B184D30B03F751C02E6BF045 (void);
extern void Animator_GetHintPosition_Injected_m6BE0C46FE3B057D08BA4B111DB6340F958B33FD1 (void);
extern void Animator_SetHintPosition_Injected_mF7843586B3EA22A800A628735583620FFDF2ADC5 (void);
extern void Animator_SetLookAtPositionInternal_Injected_mE50AFECADA7DC76D0E73C6B7131EBBD1CED5D59D (void);
extern void Animator_SetBoneLocalRotationInternal_Injected_m4A2C1DAFEE4883C6A39D33CF56E8DC015D6A0EC5 (void);
extern void Animator_get_pivotPosition_Injected_m381B79F41C78BD14DC47FA49C3B20287E50A8EC6 (void);
extern void Animator_MatchTarget_Injected_m1B2CB01E3B71964EA09D70C42583F613641925C7 (void);
extern void Animator_get_targetPosition_Injected_m7114A2CB2FE3A982E0967119BF3280258EEA4166 (void);
extern void Animator_get_targetRotation_Injected_m64CE93A396B9F5BF5F77EEE843484C38C5975F1C (void);
extern void AnimatorControllerParameter_get_name_mEB3938ADDF296A0FB37283C987AEE6EC0F4F629E (void);
extern void AnimatorControllerParameter_get_nameHash_m92DA605E70604B1BBFD5EA5AE0CD0311F21400EE (void);
extern void AnimatorControllerParameter_get_type_mACEB110E346B27168F177E7A909CFB8586A2B966 (void);
extern void AnimatorControllerParameter_set_type_m9B4AD6257187A0F1F09951E5719B766817BFA0EE (void);
extern void AnimatorControllerParameter_get_defaultFloat_m8BA60834F990D409938D8FBD156E7E0769720941 (void);
extern void AnimatorControllerParameter_set_defaultFloat_m986E7FD2CDCE8B8DBD222EF81FB311E247832427 (void);
extern void AnimatorControllerParameter_get_defaultInt_m2DBD6479C79893DD15609A11F219B84EC499F0F9 (void);
extern void AnimatorControllerParameter_set_defaultInt_m7EF5C31A61875CC22E69138B596A76E16E6E9D7B (void);
extern void AnimatorControllerParameter_get_defaultBool_mA51A41E298A2FE0A5E986E913EB0ABA5B3E1DBAA (void);
extern void AnimatorControllerParameter_set_defaultBool_mD77656D0C0CC84DABFE96B016DF279692839CAB3 (void);
extern void AnimatorControllerParameter_Equals_m9FDC3900B8DB91E9F99295CADB9574F7E1C55C71 (void);
extern void AnimatorControllerParameter_GetHashCode_m145EFDAFC4D2C410BB84F8290883977340E25312 (void);
extern void AnimatorControllerParameter__ctor_m91C7057D6AC6D38ACA5EC852D5FDA829F83C8474 (void);
extern void AnimationClipPair__ctor_mD70020644551CCC04857BA2D4F7F63AD3E2159ED (void);
extern void AnimatorOverrideController__ctor_mB3C6317471A40FC19527AA7731E775AFC665CE15 (void);
extern void AnimatorOverrideController__ctor_mE9C4FCBE3FCAF3CE2601CF50D3B19CC683BC47F8 (void);
extern void AnimatorOverrideController_Internal_Create_mB5271F04B0FB0E2BADDF1AD4ACDC983E80D8C2C3 (void);
extern void AnimatorOverrideController_get_runtimeAnimatorController_mA611B48488C4A645B1FD68855A84ECC0406D2DD7 (void);
extern void AnimatorOverrideController_set_runtimeAnimatorController_mDE783426F6A6283710A7D7FF96ADACB524D9BF6D (void);
extern void AnimatorOverrideController_get_Item_mF0A0D7B4CB2A8E1AC31D2054827D3FF8D91B806C (void);
extern void AnimatorOverrideController_set_Item_mE99B279ADC548ADA701C80E02CE97E38E4F453F1 (void);
extern void AnimatorOverrideController_Internal_GetClipByName_m31FBDF024F4A5A99130A62518DEA736CC5177FBE (void);
extern void AnimatorOverrideController_Internal_SetClipByName_m52CA560F49002766E9B61980D07312340B195B88 (void);
extern void AnimatorOverrideController_get_Item_m49AB9172BFD735042B422E9347B44624DB9D11DC (void);
extern void AnimatorOverrideController_set_Item_m02FC2BE81A2A4120D202C25CB040DA19AE9BB48A (void);
extern void AnimatorOverrideController_GetClip_m55AFB27CC2D666A280D2AE7E78D82F7AC7FE8424 (void);
extern void AnimatorOverrideController_SetClip_m5978905B5D13EE9626CE77000F7C620C26EDF025 (void);
extern void AnimatorOverrideController_SendNotification_m370030BDB9A21AF982EBF3B68012224D02D4397A (void);
extern void AnimatorOverrideController_GetOriginalClip_m10DEA1312336A51EBC45B89E0D947836CAD4EAD5 (void);
extern void AnimatorOverrideController_GetOverrideClip_m8F7F5831ED4E5D96FC2B2EF8FD427F14BAFA2A13 (void);
extern void AnimatorOverrideController_get_overridesCount_m4A241C738A0F2F03B7578E32F2C3342F536DE9DE (void);
extern void AnimatorOverrideController_GetOverrides_m4C9F72BECAE151033F913C00A016E7A4A8D4E860 (void);
extern void AnimatorOverrideController_ApplyOverrides_m4882D28731BA7F28095DC1994375E38255F2DA90 (void);
extern void AnimatorOverrideController_get_clips_mD6E8C35C32B3418CCC8C0970A76E36E18A32E3B3 (void);
extern void AnimatorOverrideController_set_clips_m5EA55A1B147ED78ED4FCB0FF033013CAF6E960A9 (void);
extern void AnimatorOverrideController_PerformOverrideClipListCleanup_mE5578B170C6E70763B20A4DF6CA6DDFF83B31E41 (void);
extern void AnimatorOverrideController_OnInvalidateOverrideController_mA6B0AA977505FDEFDD6BCA2E941FD3A18AE1AD23 (void);
extern void OnOverrideControllerDirtyCallback__ctor_mA49B11AF24CB49A9B764058DB73CE221AE54E106 (void);
extern void OnOverrideControllerDirtyCallback_Invoke_m538DCB0FFFE75495DC3977DBBF55A07C570F8B5A (void);
extern void OnOverrideControllerDirtyCallback_BeginInvoke_mD38CF91DC1EACA4C3F7BF666123D57D645C83E6E (void);
extern void OnOverrideControllerDirtyCallback_EndInvoke_mF71C144CDEAE9DE20B6BD0E03DE7E41924E8FAEE (void);
extern void AnimatorUtility_OptimizeTransformHierarchy_m48241549DC2E06049611C0F5AE0BC88016BC96A8 (void);
extern void AnimatorUtility_DeoptimizeTransformHierarchy_m2C8B7C431E62532F9301995AA7D9132ED35065CA (void);
extern void AnimatorUtility__ctor_m153AF5F6DB3F394B924AD244F145D30164F1C562 (void);
extern void Avatar__ctor_mA58012D9A6FD2A7BB88D05E58703B5619536E118 (void);
extern void Avatar_get_isValid_m89626D009C80028727A9C6F8F8F477C23934B94C (void);
extern void Avatar_get_isHuman_m1CDE3C2BCB2A683AB72088B26C9824E0FCF00FBE (void);
extern void Avatar_get_humanDescription_m8E211B52B89B57AF6793D781C69E46F6E1217E1E (void);
extern void Avatar_SetMuscleMinMax_m608B65231DBDC2DAE4F8F9D3D93FA1A35CA82B68 (void);
extern void Avatar_SetParameter_m4A1D5A8FB0D5F31636D77C9565E0FD057CFCCF6B (void);
extern void Avatar_GetAxisLength_mE8C0741203F52E4712EA9DB333638D6C42C63E9E (void);
extern void Avatar_GetPreRotation_m774928C7DB41CA00F5AF60B33E05736FFA435080 (void);
extern void Avatar_GetPostRotation_mEEBB44D6301ECD7E1AE67DD0FF9C715C3D9AF8CE (void);
extern void Avatar_GetZYPostQ_mAEDA3440F5A9EE2E3016357801005F7AB401CB34 (void);
extern void Avatar_GetZYRoll_m4557457639DE594E2FE879ED1BAAEB97637BA68F (void);
extern void Avatar_GetLimitSign_m08970E1688571ABCAE484771DA697023A8DA6EFE (void);
extern void Avatar_Internal_GetAxisLength_mE9BD06F25CA27B0D026E640CB94C53D7039575A2 (void);
extern void Avatar_Internal_GetPreRotation_m43FCB48A6F86DA5AC7FADD7B3FFE3ECA49503648 (void);
extern void Avatar_Internal_GetPostRotation_m8BEBD14E505A8FC73DCD3EF1CA99F32666FBE7A2 (void);
extern void Avatar_Internal_GetZYPostQ_mE09886C9F4882D6474F76EC32C6711C4EBAF1F19 (void);
extern void Avatar_Internal_GetZYRoll_mB5FB51A11D1DAD6EB587731C57D5AC43F9FAEFC6 (void);
extern void Avatar_Internal_GetLimitSign_m19D7625470364E767DB6F9B575ACBA08FE6BB6A5 (void);
extern void Avatar_get_humanDescription_Injected_mB2DC483AC2212729E7C92641B6FFDDB4056BF3D0 (void);
extern void Avatar_Internal_GetPreRotation_Injected_mE47911DC3BC9882688A130363B8DA9130CEF74E9 (void);
extern void Avatar_Internal_GetPostRotation_Injected_m82DFC2810E003BD5422776882E0EE67BDAFA047C (void);
extern void Avatar_Internal_GetZYPostQ_Injected_mC76060C9BE7A4A6B03FE4E272DDEBA1422445467 (void);
extern void Avatar_Internal_GetZYRoll_Injected_m7902022BBB7439F281875F47068C37AD57A0AD59 (void);
extern void Avatar_Internal_GetLimitSign_Injected_m9D9713CD9F6973FAE4DF20AE86386B216EDBADB9 (void);
extern void SkeletonBone_get_transformModified_mF6EC8E089FEC8D75FB511B8F7E210B980169C05C (void);
extern void SkeletonBone_set_transformModified_m4CA61CA5AE981BB303BBF41240A5AE6CA251FCA4 (void);
extern void HumanLimit_get_useDefaultValues_mA6C116B2DC3D800FDACE2907D52B85E632914677 (void);
extern void HumanLimit_set_useDefaultValues_m3BD1D01F9652270133D307C7709FDD554621ADE8 (void);
extern void HumanLimit_get_min_m12EAAB4E0EBBBBD221BDB213130DE9643906AB9D (void);
extern void HumanLimit_set_min_mE1EFA9D3BBB3047BF25554696FEEFF1F218A7227 (void);
extern void HumanLimit_get_max_m4E5E907AE7FFFCAC65026ECA444507B0B608F02A (void);
extern void HumanLimit_set_max_m68A852091164B5EA6CD138615DEC75EC9917DA78 (void);
extern void HumanLimit_get_center_m6F488F439245F5E54D0DFBE04CD63816BAFEFF6B (void);
extern void HumanLimit_set_center_mC1C73D9F6B3EFADCF99E6906A0D464146F2FCDF8 (void);
extern void HumanLimit_get_axisLength_m9691117C17DFCC40ECB9C1A459CE998831678947 (void);
extern void HumanLimit_set_axisLength_m7DACA3E1AA03B9733E0C1D34051859A45D5B8FB3 (void);
extern void HumanBone_get_boneName_m09C4D3365F4D1E69CD0907DEC3A2298A2CF6E18B (void);
extern void HumanBone_set_boneName_m22857CD9738A623436C8F7A31D51D1EBA4BD8F58 (void);
extern void HumanBone_get_humanName_mC5FF6D0EDE66B773EF7E6DD7722E20C07EBCDCF6 (void);
extern void HumanBone_set_humanName_m460C6620AEE45FC9601B8D05448DD6C397B12D4B (void);
extern void HumanDescription_get_upperArmTwist_m92E2B3BCA433012179892DE9493CBC5F8ADB8D52 (void);
extern void HumanDescription_set_upperArmTwist_m4056390E30A30DCEB48D55D4E536F146E431E100 (void);
extern void HumanDescription_get_lowerArmTwist_m8F57099DCEAC2B1D0D03F5443F500953E1E086E6 (void);
extern void HumanDescription_set_lowerArmTwist_mD24F728854AC5AD545E09D6E74CDD0B7AD6DF139 (void);
extern void HumanDescription_get_upperLegTwist_mB9429634218671A47EAB4E73C4ABCB4EDC1FDCF1 (void);
extern void HumanDescription_set_upperLegTwist_m0552329018CAC469A2443BFBC89B83DFB5288782 (void);
extern void HumanDescription_get_lowerLegTwist_m8AB325E825835D0A05A20F447191CF2FBA604B35 (void);
extern void HumanDescription_set_lowerLegTwist_m477F6F9AEC4E7E3F8D6FA72B05BCBF05C02FFB19 (void);
extern void HumanDescription_get_armStretch_m9C47D234F9BD5C1807E40DF748AE51CC4433C11B (void);
extern void HumanDescription_set_armStretch_m52A43854E332057B62C07306CB1663CEFDB01C71 (void);
extern void HumanDescription_get_legStretch_m568292F633E3849E7C8DAB4D738C8E6F52AB662F (void);
extern void HumanDescription_set_legStretch_m40D8FE0E122F5EF4143253814D732AF8413578DC (void);
extern void HumanDescription_get_feetSpacing_mF8283F0B14F35F3FD82BA3D6FF6D82609B924643 (void);
extern void HumanDescription_set_feetSpacing_mC0F12305CA30ADA53D08E987E52DE1611E9511D3 (void);
extern void HumanDescription_get_hasTranslationDoF_m735B027773FA7BA045C7B1D5A129B504F7B97896 (void);
extern void HumanDescription_set_hasTranslationDoF_mE5E5B11F3DC450708723875A5B1D7CFC340FD20E (void);
extern void AvatarBuilder_BuildHumanAvatar_m4BA84E1731C8C60C700A02764C183D5FA980A411 (void);
extern void AvatarBuilder_BuildHumanAvatarInternal_m80FBC857C0DF3E09682437DDD5CBE240D9D7B90D (void);
extern void AvatarBuilder_BuildGenericAvatar_mB4A1E1EE4D66D2FBBF31EAC8F05E061EE30EEF38 (void);
extern void AvatarBuilder__ctor_m14C2F971A77AC4DB69CE63D494D496B978FDB6ED (void);
extern void AvatarBuilder_BuildHumanAvatarInternal_Injected_m1D682F8482BFE1BD78843F10FA092203609C4E85 (void);
extern void AvatarMask__ctor_mF37179333D681B0089379006E3FAA89A0AB0C232 (void);
extern void AvatarMask_Internal_Create_mDC9923A288EDD1F883662D1A5C562BFCDA8F6FFA (void);
extern void AvatarMask_get_humanoidBodyPartCount_m48E096F1C159EEEAA4E8906385A058171240C7B2 (void);
extern void AvatarMask_GetHumanoidBodyPartActive_m9CB208B9F4A6E150B0C8335FF7F86F6DBD4C9C69 (void);
extern void AvatarMask_SetHumanoidBodyPartActive_mC1238644347C024ABE0330521E8EFBE678CB3CE3 (void);
extern void AvatarMask_get_transformCount_m4217FC2AAAB139DD391FE7404096BE69EC4227F7 (void);
extern void AvatarMask_set_transformCount_m831071A48C9D20B3EBEBD5821AC89DC399AF366A (void);
extern void AvatarMask_AddTransformPath_m350F95084AC3B51B09E9CAD277B77A9048A4D0CF (void);
extern void AvatarMask_AddTransformPath_m51EB1C7D29E5595C729014ED27A61FAFBA558EB2 (void);
extern void AvatarMask_RemoveTransformPath_mC4A4A3E149E628E84A25393EB39B87D0FEDC68B8 (void);
extern void AvatarMask_RemoveTransformPath_m9953AD63286DE199F12FED0A0324984620CDA774 (void);
extern void AvatarMask_GetTransformPath_m63FD27A96E20C77FCE6E164D994C8666C9900CA5 (void);
extern void AvatarMask_SetTransformPath_mD07EA9D0ADD17EC84BDD51D4B54F79B037F37D21 (void);
extern void AvatarMask_GetTransformWeight_mB0574B9F42737B0115832C96BB8F01C5AEDB7BD2 (void);
extern void AvatarMask_SetTransformWeight_m74352AA4A2E6279E267BE17796993524764F5A8D (void);
extern void AvatarMask_GetTransformActive_m72AA5B6C5707F645496E610DA69D5736396F9AF8 (void);
extern void AvatarMask_SetTransformActive_m2B300D3E293D782A2397383D2BB5DD401253CB62 (void);
extern void AvatarMask_get_hasFeetIK_m6DF328662BDEDAE1CB4CB5370F67D6A7BB9B4FE2 (void);
extern void AvatarMask_Copy_m603B23118089201052B3F6289D314F10F02BBEFB (void);
extern void HumanPose_Init_m9BBDA2B4B2EB0DE3082EE7FE36A92F19870F682D (void);
extern void HumanPoseHandler_Internal_CreateFromRoot_m93990E1FEE4C5FD08663157192B112CCF251BCEA (void);
extern void HumanPoseHandler_Internal_CreateFromJointPaths_m4380069FCDB3991540D2106AC78D61B308FB73B2 (void);
extern void HumanPoseHandler_Internal_Destroy_m824560DD4448B327FFF1227E260F6A92F42F0940 (void);
extern void HumanPoseHandler_GetHumanPose_m7F8C4612504D3197D42D6DA82F8D27B1C808AAF0 (void);
extern void HumanPoseHandler_SetHumanPose_mA5E4639B077C37CF5CB269D1EA59C42847BC2C87 (void);
extern void HumanPoseHandler_GetInternalHumanPose_mBDAB98A9452A1187BE34C0DA94F6500E71932477 (void);
extern void HumanPoseHandler_SetInternalHumanPose_m754B356841FF205448997D10EBA84D72893DB027 (void);
extern void HumanPoseHandler_GetInternalAvatarPose_mAC843356A0F4640AB83C3461C6B0543D0B39CA89 (void);
extern void HumanPoseHandler_SetInternalAvatarPose_m7320C15BB27D4EEB9E8C5E534299E5C64A48F869 (void);
extern void HumanPoseHandler_Dispose_m642803C0CB9C24C89F3A427D160AFCC72F9F4380 (void);
extern void HumanPoseHandler__ctor_mF51EEC25B7A32ED62AC281B22D12C4B58E10B1F9 (void);
extern void HumanPoseHandler__ctor_mD3A69E1030DED25C7B68D9287D9438C63C91B61A (void);
extern void HumanPoseHandler_GetHumanPose_m9323A5049D8A755FDE0E3DC21701A4788D4A7B00 (void);
extern void HumanPoseHandler_SetHumanPose_mA7FD443D9B103114E4872FBC5B0A827E1D721562 (void);
extern void HumanPoseHandler_GetInternalHumanPose_m4D1567607DBF8C1C0FEC01C47E7A1CFE214AC823 (void);
extern void HumanPoseHandler_SetInternalHumanPose_mCC6160E5546D73857604B0C1DDEFA25B64466F5D (void);
extern void HumanPoseHandler_GetInternalAvatarPose_m39BA2AEB30F917938C71EF2D8A7CF793D88AA55E (void);
extern void HumanPoseHandler_SetInternalAvatarPose_mB7C4E37F1CAAE1F28F7E6248B9F3F591CDAAF70E (void);
extern void HumanTrait_get_MuscleCount_m1710B0FAD40DB860AD5C117A831F7554F5C55678 (void);
extern void HumanTrait_GetBoneIndexFromMono_mDE16E21275A55D263014CFD3307BEB7353AE6925 (void);
extern void HumanTrait_GetBoneIndexToMono_mBFEEC0F2768E2A1AB97768A89D3872C3CA49AFEE (void);
extern void HumanTrait_get_MuscleName_mE3E2D79AA477F03384FFCAAAFD5BCA9822992599 (void);
extern void HumanTrait_get_BoneCount_mDA1FF71CCE37796233D4073E7FF129C1714A6260 (void);
extern void HumanTrait_get_BoneName_mE16F3C9E1E21791A44E44F414CD2AABB54C13E2C (void);
extern void HumanTrait_MuscleFromBone_mBF217868645416045E5FC09387C2E56BC04260BF (void);
extern void HumanTrait_Internal_MuscleFromBone_mD0D91596B0FD0F3CA5104F324D289E01D1567693 (void);
extern void HumanTrait_BoneFromMuscle_mA8D408187E909C49D79C2565CA987C3DC67FBD97 (void);
extern void HumanTrait_Internal_BoneFromMuscle_m3536556616325753A98AAEE1714EAE55A4B879AC (void);
extern void HumanTrait_RequiredBone_m11642C7E78DDE522012CEFFE18622F3ECD069684 (void);
extern void HumanTrait_Internal_RequiredBone_m2ACFEDF683A544288BEF76DF0A7744751194997C (void);
extern void HumanTrait_get_RequiredBoneCount_m76DF6A8C86B8FA70DEEA21DFE09E4EBFB4D724DB (void);
extern void HumanTrait_GetMuscleDefaultMin_m150AF046E5151B12B4215887C4FF0FD670281DD7 (void);
extern void HumanTrait_GetMuscleDefaultMax_m7C09EC4712257BDF2E5FB2F29E4E1C9586BFB61C (void);
extern void HumanTrait_GetBoneDefaultHierarchyMass_m79A2D2663D40D60E833AAD6FA3FBB7DBFF7429F2 (void);
extern void HumanTrait_GetParentBone_m0DF55C31BDF55B9EEB3F824D2F0AFC7F9A8EA04F (void);
extern void HumanTrait_Internal_GetBoneHierarchyMass_m31DC4F9216330D4BBD9FD4CC24CE27F8D0BD3BCC (void);
extern void HumanTrait_Internal_GetParent_mFE44534354CB0B140461B732E14B439D043921B1 (void);
extern void HumanTrait__ctor_m28F228B02CC30621C388DF9DDBF49106925B8155 (void);
extern void Motion__ctor_mB6190858E566BFA1B80D2E94B65CD27920A49443 (void);
extern void Motion_get_averageDuration_m95FC30447DBEBE2D275AD0C8C0C402EED2F68CA9 (void);
extern void Motion_get_averageAngularSpeed_m5167326516C36DFDF57F80CDE104054DD4957D74 (void);
extern void Motion_get_averageSpeed_m76C239071A90E7E2CAFD34BCBAA5F15FC209C148 (void);
extern void Motion_get_apparentSpeed_mCA38527B2C278283818043AB9A9A75CC84D54844 (void);
extern void Motion_get_isLooping_mD11B7C5FFEC7BC2BC5D2AB4D3B2A06D2959DFB99 (void);
extern void Motion_get_legacy_m5303314C70A7764FA6491C8BD6C3564CEB940730 (void);
extern void Motion_get_isHumanMotion_m20BB212B89986C77188968DBC9FEA2AA0DE25261 (void);
extern void Motion_ValidateIfRetargetable_m0EB3C8E318FA325471E0CAC2B1C24573AEF70EC8 (void);
extern void Motion_get_isAnimatorMotion_m974F82D669AD508710208ECB77D4CDECDCCC3F10 (void);
extern void Motion_get_averageSpeed_Injected_m11BC2AAEBABFC331969F355305A413277D8C1533 (void);
extern void RuntimeAnimatorController__ctor_m676D4538BB6C62314B256173C5F592EFCA16AAC8 (void);
extern void RuntimeAnimatorController_get_animationClips_mA8F51FF202C2C41A3E9C5366ABDEE35EDFBE8F93 (void);
extern void AnimationPlayableOutputExtensions_GetAnimationStreamSource_m3F98CCB6EFB89905DE573FBD053646207180BD44 (void);
extern void AnimationPlayableOutputExtensions_SetAnimationStreamSource_m1D5F79DBDAFB09E3A236D96FE259D692150263DD (void);
extern void AnimationPlayableOutputExtensions_GetSortingOrder_mC0888CF21290E12B6D7C74B6C2F2719BCB392BDA (void);
extern void AnimationPlayableOutputExtensions_SetSortingOrder_m74876C6AE563CA280803F7781FBA7304FCE05FAC (void);
extern void AnimationPlayableOutputExtensions_InternalGetAnimationStreamSource_mA0211B15D6E8B649FF423A5B0FAC3A18397BE33F (void);
extern void AnimationPlayableOutputExtensions_InternalSetAnimationStreamSource_m20E22C1F0C5B12FC0FBB421D93B4BD5A3B62C3FA (void);
extern void AnimationPlayableOutputExtensions_InternalGetSortingOrder_m3F29418666600B1383C23D10199D00E08875B8F7 (void);
extern void AnimationPlayableOutputExtensions_InternalSetSortingOrder_m67FB54EB92BA287562D0F0E71B3F63233161901E (void);
extern void AnimationPlayableOutputExtensions_InternalGetAnimationStreamSource_Injected_m5C2C2202C19285E2F5463707F591924E5EB77A15 (void);
extern void AnimationPlayableOutputExtensions_InternalSetAnimationStreamSource_Injected_m52C444A031E6807C93C20CFC8017AC4FEB08BEA7 (void);
extern void AnimationPlayableOutputExtensions_InternalGetSortingOrder_Injected_mA8B6B54474F440D201C8E2E5C4CDD5E236505AE2 (void);
extern void AnimationPlayableOutputExtensions_InternalSetSortingOrder_Injected_mC9BF96471E656AB7CE06B616AAF2495D35B8CAA3 (void);
extern void AnimationPlayableUtilities_Play_mACCC886DED3FE540B663261AF5EB6EF28510FB5B (void);
extern void AnimationPlayableUtilities_PlayClip_mC82BC4ABA6B41BDFAC321707E019FDDF713CFC0F (void);
extern void AnimationPlayableUtilities_PlayMixer_m218860335A7A8E8B258ECC057C2FFB904307C22F (void);
extern void AnimationPlayableUtilities_PlayLayerMixer_mD7EFC6A198A04BB38E802087B6FD70C947DF8661 (void);
extern void AnimationPlayableUtilities_PlayAnimatorController_mEEEAE8FF2940BBC5A5873506CC25E4A94F1D652A (void);
extern void AnimationPlayableBinding_Create_m3E764C5FC95E912E727275AA705701933B93C2CB (void);
extern void AnimationPlayableBinding_CreateAnimationOutput_mAAF4FE67781A130389ADA1BD625D42215B4C55A2 (void);
extern void DiscreteEvaluationAttribute__ctor_m8C8036DC8BB15BBD3BBB79F2D14637C0DEBCB151 (void);
extern void DiscreteEvaluationAttributeUtilities_ConvertFloatToDiscreteInt_mADD1EB91441A214F69458BFF45F86E77D11A340C (void);
extern void DiscreteEvaluationAttributeUtilities_ConvertDiscreteIntToFloat_m6A22CA1A2232C185F6E10865530FFEDA68E28D7F (void);
extern void NotKeyableAttribute__ctor_m818249C0E7E98C56F41B672A3140A87EA568EB84 (void);
extern void AimConstraint__ctor_m80FCFFFF15865EDAFB10965D67D54BE4FEE6B103 (void);
extern void AimConstraint_Internal_Create_mD6136600E870331489B3BB92CFE1395685907431 (void);
extern void AimConstraint_get_weight_mD185F838B6D8385622204703A29046488BC84F9F (void);
extern void AimConstraint_set_weight_mBB56202E1F77BD3AB7405873574BB5A749637F51 (void);
extern void AimConstraint_get_constraintActive_mD0DB3A45A614D383AC23B2A2932AEDC89C87073A (void);
extern void AimConstraint_set_constraintActive_m34250894015B9F702D6C0DE993A20BEAC2988561 (void);
extern void AimConstraint_get_locked_m376BECF3A58FD1FD94EC33CF19997D2D4D3BCB60 (void);
extern void AimConstraint_set_locked_m33A8E333F00470D89FD83955A4DB48EF01F54188 (void);
extern void AimConstraint_get_rotationAtRest_m7C78B93BF4832569A1796D5A84C6FABBA2CC38AB (void);
extern void AimConstraint_set_rotationAtRest_m63D45529EC85ABFDEAF3B199DB7ECDDBCE7F50CD (void);
extern void AimConstraint_get_rotationOffset_mCFD8329E777015CD734B052BF524F3871E3150D3 (void);
extern void AimConstraint_set_rotationOffset_m707C9A333CA9C3AF627E2D6701FE6A3D6BB9E038 (void);
extern void AimConstraint_get_rotationAxis_m3C642353A7E3424140821BFA8FF349D4DE7615B3 (void);
extern void AimConstraint_set_rotationAxis_mD492CB05C581FA758176C395254DF7F4D6EA3BCE (void);
extern void AimConstraint_get_aimVector_m6ED598BF93DE3A32527307F17F91110660364741 (void);
extern void AimConstraint_set_aimVector_mE0E1591858C11F330EF3ADA42813C1D09F40A8BB (void);
extern void AimConstraint_get_upVector_mDE4FD96451132091B51152BBB739B73C9D071C02 (void);
extern void AimConstraint_set_upVector_m9AF10EBED0C8922461DB1F33E5CDE926A939BBAF (void);
extern void AimConstraint_get_worldUpVector_m2F1A059CC2BF793FA973210643F20995D7586718 (void);
extern void AimConstraint_set_worldUpVector_m33709BCFE4AA1CB440EC92C2D9B73BCD159D5BE4 (void);
extern void AimConstraint_get_worldUpObject_m9FF71569DF5DAEB5E94F06F2BC4E267E2A68F312 (void);
extern void AimConstraint_set_worldUpObject_m26C2D99B9B28D9A12A943D2A7F9828886EBAE6D0 (void);
extern void AimConstraint_get_worldUpType_m615A310D122FD587014B1730BB83FBDC57E4DF66 (void);
extern void AimConstraint_set_worldUpType_mE3307BA728EF9CC6541FCFE4165F1B147B2EF08D (void);
extern void AimConstraint_get_sourceCount_mB613FABBB919793E2162B0D8842E95D01C756D61 (void);
extern void AimConstraint_GetSourceCountInternal_m765C3B4640025395472984FF5B6280EF3C891004 (void);
extern void AimConstraint_GetSources_mE2BBFD54D95BD3FE67E9E438DA4171BCAAEF46FC (void);
extern void AimConstraint_SetSources_mF46CFD6E308BC1E7E0D16B8F0D490E8456181768 (void);
extern void AimConstraint_SetSourcesInternal_m624C1C1E2682D0F99C3198708197DED0C9050C60 (void);
extern void AimConstraint_AddSource_mD3EFF3D73F07063B20F7F6B502805AC56982550F (void);
extern void AimConstraint_RemoveSource_mEA4AD231F515FFF085327A4F9B5CAA1FB15ADBB7 (void);
extern void AimConstraint_RemoveSourceInternal_mFF08EDAEB9206273D569F7D40DED7C762EAA9171 (void);
extern void AimConstraint_GetSource_m27ACCBD36B9052AAC783CD6F39FF62D111E386E0 (void);
extern void AimConstraint_GetSourceInternal_m484EACB1D8DBF4D5BA75919E00A65D03B784502B (void);
extern void AimConstraint_SetSource_m77A4824FE368602FDD9F938335FDE37A4DF6E680 (void);
extern void AimConstraint_SetSourceInternal_mA162366E0D74309449C467F197BF4248ADE84D91 (void);
extern void AimConstraint_ValidateSourceIndex_mADC2F8EEF24E30F5E4CDF706C1734CB73C5E8AA8 (void);
extern void AimConstraint_get_rotationAtRest_Injected_m0D498427FFE951EAA3C04B7392C54824C0946ED0 (void);
extern void AimConstraint_set_rotationAtRest_Injected_m53CB6D52FAA064B5B0890A98BA297CA74E1E2EB6 (void);
extern void AimConstraint_get_rotationOffset_Injected_m87D1DC1590653A8789753320193772E6CFEB9B83 (void);
extern void AimConstraint_set_rotationOffset_Injected_m0F45D42A9EFBBA8BE75B8E79D0E0CBD4ADD4C30C (void);
extern void AimConstraint_get_aimVector_Injected_m9D3878400C04B62C20192D902E88B25660AAA9A1 (void);
extern void AimConstraint_set_aimVector_Injected_mA9D542713DDB89DA2273E6AAA8103B2281C71780 (void);
extern void AimConstraint_get_upVector_Injected_m12DF6995D524FE50BEC7EF9496B3AC42EE312E71 (void);
extern void AimConstraint_set_upVector_Injected_m3F69AE1B9C274062A143F39EA409BDC3D425D350 (void);
extern void AimConstraint_get_worldUpVector_Injected_mDD12062C70B6A060698A0B256D92FB0E6B0225C0 (void);
extern void AimConstraint_set_worldUpVector_Injected_mEDDCBD31F61DD7AF4D15E9E1657A58B5289F8CFE (void);
extern void AimConstraint_AddSource_Injected_m2D072FB44070CFBC58DF459EC1194A283947457D (void);
extern void AimConstraint_GetSourceInternal_Injected_m0ECD9322D4E2E0A1DA1C17C1CE0B26CBA9DCF99E (void);
extern void AimConstraint_SetSourceInternal_Injected_m7B56A4DB1D5ED84B8F3337761C3E56411B883FEA (void);
extern void AnimationClipPlayable_Create_m034A4A30AC2642E675B95A0A7C3C384F533F5C1A (void);
extern void AnimationClipPlayable_CreateHandle_m9804DF3694EC65E8531F6839194AB189401AE564 (void);
extern void AnimationClipPlayable__ctor_mF2EE31CC772B100F98CCAE26963059C6C722FA1A (void);
extern void AnimationClipPlayable_GetHandle_mE775F2247901BA293DB01A8D384D3F9D02A25627 (void);
extern void AnimationClipPlayable_op_Implicit_m112BA2303DA5A9A8E24310332E3C27E13F74A0FD (void);
extern void AnimationClipPlayable_op_Explicit_m628ECE4D1BE08300E899184375D65052AEC50E00 (void);
extern void AnimationClipPlayable_Equals_mC5263BEA86C02CEDF93C5B14EAA168883E1DB5F4 (void);
extern void AnimationClipPlayable_GetAnimationClip_m65C9B50E705936C7E6B3F42A2BD71B704D3D0E1D (void);
extern void AnimationClipPlayable_GetApplyFootIK_m3E599D05D6A40BEFD651618CE5DDA03F15A3610F (void);
extern void AnimationClipPlayable_SetApplyFootIK_m7CBA77F56815AD21784AC53D9EBDAE18AFA48507 (void);
extern void AnimationClipPlayable_GetApplyPlayableIK_m04EE9E4136AC350F27814DF3B006238260CF3EE9 (void);
extern void AnimationClipPlayable_SetApplyPlayableIK_m69A6F6E28EB250956E27C1720A0A842848F54DAB (void);
extern void AnimationClipPlayable_GetRemoveStartOffset_m52EBB080BD11079E3D2F2AD6E913E5451F24CFE2 (void);
extern void AnimationClipPlayable_SetRemoveStartOffset_mBAC88E40F6A759FACA4105EF683181D43381C8E5 (void);
extern void AnimationClipPlayable_GetOverrideLoopTime_m22078B967400E8B3E5D056BCE1704CBB2E1E2C93 (void);
extern void AnimationClipPlayable_SetOverrideLoopTime_mF1F57940D8DDBCC6EBCB75A27C2372BB39DED177 (void);
extern void AnimationClipPlayable_GetLoopTime_mD57482D47862960C3A2D0185E8F75642DD858FEC (void);
extern void AnimationClipPlayable_SetLoopTime_m3AAA1134C4D339C84EF57FE289D33100D4971ED8 (void);
extern void AnimationClipPlayable_GetSampleRate_m0559EF225E427721E66976BF290A0C53D1FFA744 (void);
extern void AnimationClipPlayable_SetSampleRate_m2D7C98FD996AC3A582578A4433C9438D0675020C (void);
extern void AnimationClipPlayable_CreateHandleInternal_mB8466F44A261B040DBCE8BA442DA8CF7153D2212 (void);
extern void AnimationClipPlayable_GetAnimationClipInternal_mBA8D6A1F7ECE71E67F946D2379ABBCC03469CFD8 (void);
extern void AnimationClipPlayable_GetApplyFootIKInternal_m7695131A7D19C888FB75F2CE2227EC24361AA20A (void);
extern void AnimationClipPlayable_SetApplyFootIKInternal_m57C77DC9937F7BA02885EEBF5D7CDC1CF9412DFC (void);
extern void AnimationClipPlayable_GetApplyPlayableIKInternal_m4531AC8A44F8BE94292B95E03E33B5C7E9DAF474 (void);
extern void AnimationClipPlayable_SetApplyPlayableIKInternal_mBCA5B580834435175256C8319275DD443281DD82 (void);
extern void AnimationClipPlayable_GetRemoveStartOffsetInternal_m41FD6EE2AD07E2D17659795CC4D683E1D2B19B12 (void);
extern void AnimationClipPlayable_SetRemoveStartOffsetInternal_m5C095AAB7C17821144A2CD4D7DDFE562358CC5A4 (void);
extern void AnimationClipPlayable_GetOverrideLoopTimeInternal_m3AF34E428A96309497A077735D169D724D32FE64 (void);
extern void AnimationClipPlayable_SetOverrideLoopTimeInternal_m44BAF0A6EE093D0E30EC8FF99423BF6E4B832CC5 (void);
extern void AnimationClipPlayable_GetLoopTimeInternal_mF9036903929CA96BE7959D733A645901850B3268 (void);
extern void AnimationClipPlayable_SetLoopTimeInternal_mC308D81D4A82EFACED882515D17935E43298E386 (void);
extern void AnimationClipPlayable_GetSampleRateInternal_m25C19C01361060CE824DE0B2306284AF622A656E (void);
extern void AnimationClipPlayable_SetSampleRateInternal_m9B8747DC0D8BDE73FF8899242E646A83F8E420D4 (void);
extern void AnimationClipPlayable_CreateHandleInternal_Injected_m4FD6B80E5194144660D7974F13F44BA0355C6E3B (void);
extern void AnimationHumanStream_get_isValid_mFC26001D2772FFDE3C791A764954BD9A1512DD6C (void);
extern void AnimationHumanStream_ThrowIfInvalid_m35ADC8567F7DFB74AA71C4E29572E06A8223DE65 (void);
extern void AnimationHumanStream_get_humanScale_mD8924C6D1BD6723AB50A52D89FFFC92855BC47C6 (void);
extern void AnimationHumanStream_get_leftFootHeight_mC29F681BA1B4712AD46F911D902BE07CF8FB78C7 (void);
extern void AnimationHumanStream_get_rightFootHeight_mA2AB707DDD51031FAC763BA1610C2BFCDD80AFBE (void);
extern void AnimationHumanStream_get_bodyLocalPosition_mA8B1A8A9625C388B7E4E12BE814B4AA1D8D3DC5B (void);
extern void AnimationHumanStream_set_bodyLocalPosition_m5EAC7202D30F117B8821BDDC5282FF19FBB93706 (void);
extern void AnimationHumanStream_get_bodyLocalRotation_m6224C03E8D34FAEB50C732CB7EBA7E2328A8EAD6 (void);
extern void AnimationHumanStream_set_bodyLocalRotation_m19E818A4DDF88A4BCC520011C5F9864657F106B4 (void);
extern void AnimationHumanStream_get_bodyPosition_mFA136469110BADCEE3EC2821AD408108A7F3516A (void);
extern void AnimationHumanStream_set_bodyPosition_mEFD7D98A88B79674702D53D659A2338D5E56F02C (void);
extern void AnimationHumanStream_get_bodyRotation_m6C3E24BCFC5B10524FD8143D33706365CFF2517C (void);
extern void AnimationHumanStream_set_bodyRotation_m16999012B4CEE244F4596C78D98E12B649302846 (void);
extern void AnimationHumanStream_GetMuscle_mD12DF41EA39BB88236B6E51C6E60DC505CD3B578 (void);
extern void AnimationHumanStream_SetMuscle_mF6E7B3FB78B9E085ADBFA3F5959C065BBDA5F481 (void);
extern void AnimationHumanStream_get_leftFootVelocity_m7FA5AA97C252763E010294C1B3F820ECC070E2D5 (void);
extern void AnimationHumanStream_get_rightFootVelocity_mE89F940C04F0842CD0A2BBCF394B657FB4080EEB (void);
extern void AnimationHumanStream_ResetToStancePose_m6CB3AAA301BCBA6ABA5A1DC4EE8767301AFDEAC7 (void);
extern void AnimationHumanStream_GetGoalPositionFromPose_m2773EC1B9E600BD95B53DB65745A53468EF3820D (void);
extern void AnimationHumanStream_GetGoalRotationFromPose_m0D29BD6D2246DF74E1EFD77D909550A1E8CB96C8 (void);
extern void AnimationHumanStream_GetGoalLocalPosition_mAE43F299D80C2E9DF69E41BC0394CCCEABC079FB (void);
extern void AnimationHumanStream_SetGoalLocalPosition_mA10C3B87B9FCB49197FED94B68BB72415815527A (void);
extern void AnimationHumanStream_GetGoalLocalRotation_m786FED58DB427635329550B3216AACA3972059FB (void);
extern void AnimationHumanStream_SetGoalLocalRotation_mD853EA0BAF5C880FBC6EC57AD5F0D667F241168E (void);
extern void AnimationHumanStream_GetGoalPosition_m9B04E0871FAE6225F9715C7388B334A31834014B (void);
extern void AnimationHumanStream_SetGoalPosition_mC32B190C32B2BFD1FD098C8FF2E81BCE08658A55 (void);
extern void AnimationHumanStream_GetGoalRotation_m3F754F7E38B1A0E7CC6D4F5517F68FD0017AFA05 (void);
extern void AnimationHumanStream_SetGoalRotation_m0AC3D5B3539CCEC2C715CEF37AF96AD91411ED54 (void);
extern void AnimationHumanStream_SetGoalWeightPosition_m31CDD1019E6EC4583B13310A3D6B42A3129CCE29 (void);
extern void AnimationHumanStream_SetGoalWeightRotation_m9E28713D497E4871CD4513FC0738642F36021009 (void);
extern void AnimationHumanStream_GetGoalWeightPosition_m97C54429B8000F50C0C8D7BD695B1D6A92A4C376 (void);
extern void AnimationHumanStream_GetGoalWeightRotation_m156696D9925EBDEEF0114C79FE8E2A3FAA07AC4D (void);
extern void AnimationHumanStream_GetHintPosition_m8C48689F4E696E601892CDC959B0C0BDBD67BBBB (void);
extern void AnimationHumanStream_SetHintPosition_m7C26134F50A8C6786430A0A5382884E55549112A (void);
extern void AnimationHumanStream_SetHintWeightPosition_m00D5FD24F69AC10C7B133ACAFB71BB2362E4E635 (void);
extern void AnimationHumanStream_GetHintWeightPosition_m0D51CD0A32B87BA0208942ABC3DBF3AC0FB8FE9D (void);
extern void AnimationHumanStream_SetLookAtPosition_m6C0D265D80FF6FD89159A9F9D47172B37093D2A8 (void);
extern void AnimationHumanStream_SetLookAtClampWeight_mC76D8266ABA59385337106B48BF2F5019DEC29CA (void);
extern void AnimationHumanStream_SetLookAtBodyWeight_m4A5D39D1BB635A01DF98E65F45AD873E44236BCE (void);
extern void AnimationHumanStream_SetLookAtHeadWeight_m8E9632C51EB7FC316FF4DB3F1639D41CB20474E0 (void);
extern void AnimationHumanStream_SetLookAtEyesWeight_mD5FC3552642F7A64F794EC8D0872972F8A6AD507 (void);
extern void AnimationHumanStream_SolveIK_m36549A812DA9901B415ABF3A9BB40DA236C625F3 (void);
extern void AnimationHumanStream_GetHumanScale_m528C523BF55034961049D455994749AEF778117E (void);
extern void AnimationHumanStream_GetFootHeight_m73B1AC0DEA1287024D0EA1D53F9303E0B8A358A6 (void);
extern void AnimationHumanStream_InternalResetToStancePose_m1EEC3F6EC405EEB4D36C2CC2D15BFA5CACE50144 (void);
extern void AnimationHumanStream_InternalGetGoalPositionFromPose_mF9D079DA1E5D728A79637826DBDD8FB7A068E2F3 (void);
extern void AnimationHumanStream_InternalGetGoalRotationFromPose_m6689F3C3DAE9513AE06956C93368334B64D95291 (void);
extern void AnimationHumanStream_InternalGetBodyLocalPosition_mFB26EABB0EB551CFA4232D32065A79BF36E356C7 (void);
extern void AnimationHumanStream_InternalSetBodyLocalPosition_mC469D6040AB3305AE399215925156806A48F1220 (void);
extern void AnimationHumanStream_InternalGetBodyLocalRotation_mB5A3ABB9FDC0C71BA5FF77D111CCA40C681BD631 (void);
extern void AnimationHumanStream_InternalSetBodyLocalRotation_mCC66C848E64D04B20A901D7D0760BB2D794B39C3 (void);
extern void AnimationHumanStream_InternalGetBodyPosition_m549D7BB8B6B6BBA95AA6E04259D975CF81F99849 (void);
extern void AnimationHumanStream_InternalSetBodyPosition_m0E963FC02C014FF1D519356DAF90E1E37D66DDBA (void);
extern void AnimationHumanStream_InternalGetBodyRotation_m533F3431386D21B1A170378120A34A94D76DF121 (void);
extern void AnimationHumanStream_InternalSetBodyRotation_mB72C4A04EF45AA6E8CC9E52638FB65766885D34C (void);
extern void AnimationHumanStream_InternalGetMuscle_mCB75C7C1E13D6B7C0ABBCD4270E94DD025CC876E (void);
extern void AnimationHumanStream_InternalSetMuscle_mCC068A0689AB992DB22C4178AEA8390E3F49119E (void);
extern void AnimationHumanStream_GetLeftFootVelocity_mB62C9F36949F43F881DC225B5AD92B010E7961D7 (void);
extern void AnimationHumanStream_GetRightFootVelocity_mEE89EE87CFD969E2ABE2FF6A2FABFCB651489F28 (void);
extern void AnimationHumanStream_InternalGetGoalLocalPosition_m53C7592FE48A5E61D96031CA44D075F3C64E2A44 (void);
extern void AnimationHumanStream_InternalSetGoalLocalPosition_mAFA13881BA09B0CFFA788B24088CE98A4EFC387E (void);
extern void AnimationHumanStream_InternalGetGoalLocalRotation_m223B1725AA46FE23E693AB1E2FD20E96AD9D578A (void);
extern void AnimationHumanStream_InternalSetGoalLocalRotation_mCE4ED1A96EBE0BED4FDE8394BD1049CF857970A1 (void);
extern void AnimationHumanStream_InternalGetGoalPosition_m25C63A96C6858BF176670F3FC620BFF1672FC468 (void);
extern void AnimationHumanStream_InternalSetGoalPosition_mAFBF83E782A12A0BEBDD63C2BA165541C5AC3029 (void);
extern void AnimationHumanStream_InternalGetGoalRotation_m48632794B5A9E479ED6194902347E52CC072E4F0 (void);
extern void AnimationHumanStream_InternalSetGoalRotation_m3810FFA6A48991BA97DC9CE844C8C04104607681 (void);
extern void AnimationHumanStream_InternalSetGoalWeightPosition_mA9E5685E3F30AED1A5AE87E1135BF5047DA8D326 (void);
extern void AnimationHumanStream_InternalSetGoalWeightRotation_m8685321C9439B4DA865675908633BDD952CDA8DB (void);
extern void AnimationHumanStream_InternalGetGoalWeightPosition_mFB2155CD3EEE62EDC9176A80EDBECA6D1E0BD6CD (void);
extern void AnimationHumanStream_InternalGetGoalWeightRotation_mF2E03DAB54D65B15ABF65FD68C39EB48928BE9C8 (void);
extern void AnimationHumanStream_InternalGetHintPosition_m3D8100E5F06F8C05F717DD1CD9A424DD26396434 (void);
extern void AnimationHumanStream_InternalSetHintPosition_m7DC7D373633BD22E372BEE6F02E662A3C2C278BC (void);
extern void AnimationHumanStream_InternalSetHintWeightPosition_mD6136E04EDF9E591CEAAF8AB09919DCCE54553FD (void);
extern void AnimationHumanStream_InternalGetHintWeightPosition_m4F89A6FD73DEA7166E91D0ECEE416BAEE989982E (void);
extern void AnimationHumanStream_InternalSetLookAtPosition_m6DC48530B5D259E37BEB489DCD2BBF07D8CB832E (void);
extern void AnimationHumanStream_InternalSetLookAtClampWeight_mBA29A943B22AA5DD8917EB5EB9589F04E76D6857 (void);
extern void AnimationHumanStream_InternalSetLookAtBodyWeight_m614FEABDB4E114CDA84A94333AA7E2BF9ED4BDF7 (void);
extern void AnimationHumanStream_InternalSetLookAtHeadWeight_m184F2F1D223714F0FD5B529C89207D9F6C0CDF92 (void);
extern void AnimationHumanStream_InternalSetLookAtEyesWeight_m99A49670FE2C24ADFEA9DBBBA4135BDDA93EFE1B (void);
extern void AnimationHumanStream_InternalSolveIK_m84F06C829271E6F0B15B7653BD35A26A2D18046E (void);
extern void AnimationHumanStream_GetHumanScale_Injected_mEA19F4E31DFE65A7D56BA5B2EBA217E72EE06A58 (void);
extern void AnimationHumanStream_GetFootHeight_Injected_m0647D377CEA3F2C6154BD6506AF5FC1930164CEE (void);
extern void AnimationHumanStream_InternalResetToStancePose_Injected_m36E4E449DD952DAAB35818B74915CC145084688A (void);
extern void AnimationHumanStream_InternalGetGoalPositionFromPose_Injected_m003DE04C63342B9E755F184DF6314D0A337B340B (void);
extern void AnimationHumanStream_InternalGetGoalRotationFromPose_Injected_m0C629C81F5CF4232EBA5E416A43DDEAE0FEEF39C (void);
extern void AnimationHumanStream_InternalGetBodyLocalPosition_Injected_m3F3777D546F51B80206180AB55F514FD47961A49 (void);
extern void AnimationHumanStream_InternalSetBodyLocalPosition_Injected_mC7E59666CA9F990D4E5314AB6C1AF3EABF33E925 (void);
extern void AnimationHumanStream_InternalGetBodyLocalRotation_Injected_m9FAC1277B0CB02B56C8AA6513564B212E7149161 (void);
extern void AnimationHumanStream_InternalSetBodyLocalRotation_Injected_m3EFE47F3DB39DC05901DE9DB9D2FBCEE4529CA3A (void);
extern void AnimationHumanStream_InternalGetBodyPosition_Injected_mA3A52156F6CB2782C81D0C5FAA7EE05BF431CAC8 (void);
extern void AnimationHumanStream_InternalSetBodyPosition_Injected_mFAE2CA55CFAB5AC553D7387332EEE31C15E35CAA (void);
extern void AnimationHumanStream_InternalGetBodyRotation_Injected_m75E3A42257FE94CFB22C3CAC3BBBF309BA22D8E7 (void);
extern void AnimationHumanStream_InternalSetBodyRotation_Injected_m8A5F889592AA66AB02DA56A1AC5CAA7816644C28 (void);
extern void AnimationHumanStream_InternalGetMuscle_Injected_m825CF7137F07F5C809369304C7A6B51AD6A292DE (void);
extern void AnimationHumanStream_InternalSetMuscle_Injected_m6450C555E5040B354E9998532FC8A815A34496F2 (void);
extern void AnimationHumanStream_GetLeftFootVelocity_Injected_mE349F95F4022443A18B7AF49B332E4B63C9CA33D (void);
extern void AnimationHumanStream_GetRightFootVelocity_Injected_mE2A4867DD6869A588D30059D963813A10043F878 (void);
extern void AnimationHumanStream_InternalGetGoalLocalPosition_Injected_m004A9C4D2AA06B30426EB05DE8782BF1481644C2 (void);
extern void AnimationHumanStream_InternalSetGoalLocalPosition_Injected_m9FB590E17651CB3CE0A608607FFFF395E82B676B (void);
extern void AnimationHumanStream_InternalGetGoalLocalRotation_Injected_mF5E1BCC27D4621E7AA7EF1E5520AA11813524B42 (void);
extern void AnimationHumanStream_InternalSetGoalLocalRotation_Injected_m229A170A92772EC0574B1B9FF3152CFD937B5FA5 (void);
extern void AnimationHumanStream_InternalGetGoalPosition_Injected_m97488830A535BC3137E2486821CBF83F171C238C (void);
extern void AnimationHumanStream_InternalSetGoalPosition_Injected_m0023207419029D627B25F79420F5E67E39C44F33 (void);
extern void AnimationHumanStream_InternalGetGoalRotation_Injected_mFF3A9CC634E5501841628F97A2F243947B57CBDD (void);
extern void AnimationHumanStream_InternalSetGoalRotation_Injected_m8F7471D4B367E065F33AF67FE4DC14FF2345183B (void);
extern void AnimationHumanStream_InternalSetGoalWeightPosition_Injected_m6E8C7641181FF1B57CD2B3388167E67320A1B0C3 (void);
extern void AnimationHumanStream_InternalSetGoalWeightRotation_Injected_m4189139D4731A9A6D6C992F0899688CF767B1B0B (void);
extern void AnimationHumanStream_InternalGetGoalWeightPosition_Injected_m44BBBC15D4F43B5CAF70DE788A4AC675BDD381CD (void);
extern void AnimationHumanStream_InternalGetGoalWeightRotation_Injected_m2150CE3A46C063490FC301BC5F959811DC8299CC (void);
extern void AnimationHumanStream_InternalGetHintPosition_Injected_mA5263F06378340FF9FB34C59063981450B728D39 (void);
extern void AnimationHumanStream_InternalSetHintPosition_Injected_m8049649876D9B3B36DA292E797DB535C7C2A18A6 (void);
extern void AnimationHumanStream_InternalSetHintWeightPosition_Injected_mD1AAC5760F69AAB039F112B82E203B1671171427 (void);
extern void AnimationHumanStream_InternalGetHintWeightPosition_Injected_m0721431AC161BA604F4F3A9AAB756381E58597D3 (void);
extern void AnimationHumanStream_InternalSetLookAtPosition_Injected_mE8C5F0DE986E6B1A34F4BF786C3C56D714C921E2 (void);
extern void AnimationHumanStream_InternalSetLookAtClampWeight_Injected_mA8D2E45F77FC83E9F9F8B1A7AB2F02894E25BAB8 (void);
extern void AnimationHumanStream_InternalSetLookAtBodyWeight_Injected_m5AD30D8D26A598812D738F6BDEC937EB66E2B4B2 (void);
extern void AnimationHumanStream_InternalSetLookAtHeadWeight_Injected_m81CB542284565A5CDC5D2689EB89795D00AB0270 (void);
extern void AnimationHumanStream_InternalSetLookAtEyesWeight_Injected_m442F84B029536CB4C150191DD11BCF97F5E13021 (void);
extern void AnimationHumanStream_InternalSolveIK_Injected_m2E5AFD0FB9927A94F7B94139C93E56C62D245EF5 (void);
extern void AnimationLayerMixerPlayable_get_Null_m56FCE6DE925B3ED017607BC6A88A71E5F04274B2 (void);
extern void AnimationLayerMixerPlayable_Create_m572693A593412F4C58DDB479B346D14AB8D8AA48 (void);
extern void AnimationLayerMixerPlayable_Create_mB080375BE13D2A1159D6AD4AB45FB10C36E389FF (void);
extern void AnimationLayerMixerPlayable_CreateHandle_m74B2930D89DABE9160B08D8C92D6EA6622D88A1D (void);
extern void AnimationLayerMixerPlayable__ctor_m28884B8B9F7E057DF947E3B43ED78EA107368BD6 (void);
extern void AnimationLayerMixerPlayable_GetHandle_m324A98D0B0BFC0441377D65CAE93C914F828721F (void);
extern void AnimationLayerMixerPlayable_op_Implicit_m50234C22795358D76242C022AF5CC90DF7C0141B (void);
extern void AnimationLayerMixerPlayable_op_Explicit_m255DC49C6DA5D0E6A85E6DB4D41BBE0A63C6D7FD (void);
extern void AnimationLayerMixerPlayable_Equals_mA5D24E61E2DE1140B409F3B569DBA3C185751970 (void);
extern void AnimationLayerMixerPlayable_IsLayerAdditive_m379268A18CFAD74371F6D4E0467072761BF84713 (void);
extern void AnimationLayerMixerPlayable_SetLayerAdditive_m3B35E03C224B118E3F3D9E8A7B697AF570FBFB6E (void);
extern void AnimationLayerMixerPlayable_SetLayerMaskFromAvatarMask_mC4BDE2B476AC13C31053100085FAF6BC86000280 (void);
extern void AnimationLayerMixerPlayable_CreateHandleInternal_mEEEEBA10E6AD409C8CAF16BDF7F0E89E47A91FC8 (void);
extern void AnimationLayerMixerPlayable_IsLayerAdditiveInternal_m576416565697C3E26266D7577C4AB03041B975AE (void);
extern void AnimationLayerMixerPlayable_SetLayerAdditiveInternal_m0B39FA66BEF309D1E1FDBAA4CF1E20DA7338ADCF (void);
extern void AnimationLayerMixerPlayable_SetSingleLayerOptimizationInternal_mF1EC1B461F2CCB8D7E01799875DDB5FC8FE4BBDB (void);
extern void AnimationLayerMixerPlayable_SetLayerMaskFromAvatarMaskInternal_mDA82665D20D53C1638037283DDCFE7BB2B2DD035 (void);
extern void AnimationLayerMixerPlayable__cctor_m27A78F2EB8840FFCC84901AB4E916ACCE8D8E49B (void);
extern void AnimationLayerMixerPlayable_CreateHandleInternal_Injected_m052C3DAAC09B1BADE847FB348E0FFFB228B17C26 (void);
extern void AnimationMixerPlayable_get_Null_m46DF0AE67D02D0CA48798FCF5ED4DA5FFFCD7C14 (void);
extern void AnimationMixerPlayable_Create_m96BFD63578EC4E4167A30E4899EF3DA2A4E431F2 (void);
extern void AnimationMixerPlayable_Create_m4136E1F8A7BF26D3DE52C68111F8E1D789A7A8F3 (void);
extern void AnimationMixerPlayable_CreateHandle_m98DCB979893A9C4F782B2E07EF12BD69CF838A9C (void);
extern void AnimationMixerPlayable__ctor_mBF84CC064549C2C00B2AE1174018335958EB7EA7 (void);
extern void AnimationMixerPlayable_GetHandle_mBA6CEB1579A713A985D474E75BC282728318882F (void);
extern void AnimationMixerPlayable_op_Implicit_m7B2D50F94CD0EE3E66478A560CC929BE7C985323 (void);
extern void AnimationMixerPlayable_op_Explicit_m7ABFCCF21A03E8E4A624C41F58CA3CD1DF2A78EE (void);
extern void AnimationMixerPlayable_Equals_m6EBE215636EEEA3196A43F4D6C1FE6DD704AFA4E (void);
extern void AnimationMixerPlayable_CreateHandleInternal_m0C404F86C8C0FDD248BED7E153F3BEFBEEA39D37 (void);
extern void AnimationMixerPlayable__cctor_m7D67E8E778387293AF1ACB1FDBE6ADA3E456A969 (void);
extern void AnimationMixerPlayable_CreateHandleInternal_Injected_mD26E05A0F2676C90B7F06E718B7843167D33FE1E (void);
extern void AnimationMotionXToDeltaPlayable_get_Null_m6A342703E3ECA758C93D23C389B110F2692E3CBD (void);
extern void AnimationMotionXToDeltaPlayable_Create_m9F2C95194E1B5F76A0399BDE8FCEF33A80B77F73 (void);
extern void AnimationMotionXToDeltaPlayable_CreateHandle_m848549F4FA09509BCBD846D85E5E2E8DFB8CBE3B (void);
extern void AnimationMotionXToDeltaPlayable__ctor_mDE3C14B4B975AC693669D66B6E41BB6432AFA940 (void);
extern void AnimationMotionXToDeltaPlayable_GetHandle_m09F605E78AD7F0135C7F57EB048031091A50E3A2 (void);
extern void AnimationMotionXToDeltaPlayable_op_Implicit_m1AC02CC4C55FD3550D6DFFFB7ADF960BD7D6E35D (void);
extern void AnimationMotionXToDeltaPlayable_op_Explicit_mA9E879CF299FB215325B910F99033B7821A59F62 (void);
extern void AnimationMotionXToDeltaPlayable_Equals_m7CBF3B7618EDBA4ECC2F3C2F54011248BC45CDCC (void);
extern void AnimationMotionXToDeltaPlayable_IsAbsoluteMotion_mCFE89CD743EC61AF6EF6117F72A36FCC26216487 (void);
extern void AnimationMotionXToDeltaPlayable_SetAbsoluteMotion_m5D1B029F6E6BFFB521CC6CB72ACBE7EA27B28715 (void);
extern void AnimationMotionXToDeltaPlayable_CreateHandleInternal_m9556CCBB3F290E53A0BA8021F06C8E83232EF706 (void);
extern void AnimationMotionXToDeltaPlayable_IsAbsoluteMotionInternal_mEF55F7D4D71BEBDCCE515D2E0CE45117D949EAD2 (void);
extern void AnimationMotionXToDeltaPlayable_SetAbsoluteMotionInternal_m616455F80B4EAE4A0CD24A29630792C62872E929 (void);
extern void AnimationMotionXToDeltaPlayable__cctor_m4FC582F607F00D5E2A6B97219D2D4150AFA42AF1 (void);
extern void AnimationMotionXToDeltaPlayable_CreateHandleInternal_Injected_mAFF1C58B8D07A6A9E92042C038231D7CC873EF11 (void);
extern void AnimationOffsetPlayable_get_Null_m661CE89E4ED81EC690E6E5F6B50861FC75164513 (void);
extern void AnimationOffsetPlayable_Create_mA5D6C2A6687EC937E35D758C88300F1F0056AB39 (void);
extern void AnimationOffsetPlayable_CreateHandle_m27C7AEF0B9D954591B9FB7A9EF4C92CE96037518 (void);
extern void AnimationOffsetPlayable__ctor_mBF3AC6493556DAAEF608B359BEBE8FA6D9F8DBFD (void);
extern void AnimationOffsetPlayable_GetHandle_m769BEFF90379AEAB0C579F7800953458CE3EBA78 (void);
extern void AnimationOffsetPlayable_op_Implicit_m0718409D76954C4C3D1F02F8B55DD39C6BC66C0F (void);
extern void AnimationOffsetPlayable_op_Explicit_mA2A6B182D577BEF58BFFDCAFDCBCF59558E20843 (void);
extern void AnimationOffsetPlayable_Equals_mEC28392ADD4E9639EB9228D106D93E21B3587270 (void);
extern void AnimationOffsetPlayable_GetPosition_m1DED0A8F334427F11E32E844C6AB62BB0DBBE247 (void);
extern void AnimationOffsetPlayable_SetPosition_mB3D98093AB2BDE7454642F90CDE62C71ED6B9F19 (void);
extern void AnimationOffsetPlayable_GetRotation_m17F5666C4A8EF5033B545A3ED3769F8BD4702818 (void);
extern void AnimationOffsetPlayable_SetRotation_m042FD5D84E348BEAE49AFCC84DDD14861A8C1AEC (void);
extern void AnimationOffsetPlayable_CreateHandleInternal_m877AC3B2ED395B0C0A7FC1E2C9E44BC7A776BABF (void);
extern void AnimationOffsetPlayable_GetPositionInternal_mD31B13AB023DB1C1856FBA8FD2386E9F9F5E82BD (void);
extern void AnimationOffsetPlayable_SetPositionInternal_m5DE5D178F3250104A72B7AC2145CF4E27810881B (void);
extern void AnimationOffsetPlayable_GetRotationInternal_m3C04140C3FCC4890A1A9FDF7DF3617F1E1BF7912 (void);
extern void AnimationOffsetPlayable_SetRotationInternal_m269A33B4240A028BFDF6988F61690F9CCC3A71FB (void);
extern void AnimationOffsetPlayable__cctor_m6F50D35CE1FAF52BD587DD3B440CBDE34A76B096 (void);
extern void AnimationOffsetPlayable_CreateHandleInternal_Injected_m39A68EF379AFC09A9E070474CA3E19859FB41F85 (void);
extern void AnimationOffsetPlayable_GetPositionInternal_Injected_m5B9EC37E005794D980DFE712058DD6E424341E0C (void);
extern void AnimationOffsetPlayable_SetPositionInternal_Injected_m14B7B16EDD2925FBBF4C73F0009057C5FB0C3ED5 (void);
extern void AnimationOffsetPlayable_GetRotationInternal_Injected_m49872A59A9BC09A682DA6577C4648481BD801BC3 (void);
extern void AnimationOffsetPlayable_SetRotationInternal_Injected_mF191483313F9238202B6439298D9E6A7C916C462 (void);
extern void AnimationPlayableExtensions_SetAnimatedPropertiesInternal_m9038247416A38E252EF0DCBCCBFE990589F4C51F (void);
extern void AnimationPlayableGraphExtensions_SyncUpdateAndTimeMode_mBF6173908DA7C04947C405230A1D66F108E7F493 (void);
extern void AnimationPlayableGraphExtensions_DestroyOutput_m2F51C114F692C084C83D797B937C2E2394441A12 (void);
extern void AnimationPlayableGraphExtensions_InternalCreateAnimationOutput_m2FBE35C9ADFA39052F34E49F46E39CEBD10F4B49 (void);
extern void AnimationPlayableGraphExtensions_InternalSyncUpdateAndTimeMode_m7C6C30F3FC2930979C825953394E8C9F6D35E3F3 (void);
extern void AnimationPlayableGraphExtensions_InternalDestroyOutput_mFEE7A93C4A44E5C9256E16969FF63E5616730FFC (void);
extern void AnimationPlayableGraphExtensions_InternalAnimationOutputCount_m4343C1C30D66AE678928B28CCD4B70BAD927C2FA (void);
extern void AnimationPlayableGraphExtensions_InternalGetAnimationOutput_m2144AD9B2AB6123D5D2D86DB06A1E6EA8D954105 (void);
extern void AnimationPlayableOutput_Create_m65847A70F6C74854387814C5B1D4C281B6CCCDC4 (void);
extern void AnimationPlayableOutput__ctor_mE4FB8AA6DFB2F3C18E04A9317F5CE53597A7D22A (void);
extern void AnimationPlayableOutput_get_Null_mDF5638798B49F3E7ACCF766C266D7F776E553900 (void);
extern void AnimationPlayableOutput_GetHandle_m2A8E2A9CBD12EDCF48FC946445AB42802083338D (void);
extern void AnimationPlayableOutput_op_Implicit_mB256AA7AA6BC0577B47399941D4B42BBC5C28DA7 (void);
extern void AnimationPlayableOutput_op_Explicit_m7139943338A06A1B3DE71DF52A0D253C6DC8877E (void);
extern void AnimationPlayableOutput_GetTarget_m2E55D32775E0B27063F697346701A0EB5424DA9D (void);
extern void AnimationPlayableOutput_SetTarget_m0F7745C4A721D76EB1E804AA48E70C9C798E0DCE (void);
extern void AnimationPlayableOutput_InternalGetTarget_m3ECC05C07DEBDD5ECCAB92A2213DA9087E43B5F2 (void);
extern void AnimationPlayableOutput_InternalSetTarget_m49002BC3713A0AF76F2447A0147493F234B8E616 (void);
extern void AnimationPosePlayable_get_Null_m9B90EFB9E0AFEB7DA3B9207F21C2BC057CDB8FD2 (void);
extern void AnimationPosePlayable_Create_mAB7A4B7120AC4EC97DEB872B7911D465C0B1AF2B (void);
extern void AnimationPosePlayable_CreateHandle_m4FA33101F2C76AC3E150E864E1067D1633E51411 (void);
extern void AnimationPosePlayable__ctor_mC6C096785918358CA7EC12BABCDF4BBD47F7BA3F (void);
extern void AnimationPosePlayable_GetHandle_m5DC7CA4CAF3CD525D454D99EBC3D12C3571B527B (void);
extern void AnimationPosePlayable_op_Implicit_mEB15D8B1FE99F595099ACB16EDF4D270CFA23C35 (void);
extern void AnimationPosePlayable_op_Explicit_mC4A844D0BA9B66F5CF757C95207D0BE8E00B4063 (void);
extern void AnimationPosePlayable_Equals_m10F1E7DD7037B2AB3F7DAE3E01A1DC843EABD0A3 (void);
extern void AnimationPosePlayable_GetMustReadPreviousPose_m6C3C23E75C7EEB05DB84E83507F94ED09C1DF961 (void);
extern void AnimationPosePlayable_SetMustReadPreviousPose_m3859165529333B3140AE4E9097FE66E992DA8AF7 (void);
extern void AnimationPosePlayable_GetReadDefaultPose_mA4E24ADBB4A96DD62D17F50ADECD5F114D44C5B8 (void);
extern void AnimationPosePlayable_SetReadDefaultPose_m852C2742CE8CFF49FF7216DA9D2B9C755C41AA20 (void);
extern void AnimationPosePlayable_GetApplyFootIK_mDBE93A57F35A70EDE6F8503323EF2F7C5CADE32F (void);
extern void AnimationPosePlayable_SetApplyFootIK_m862FA48CF186206F9B488BFADE8C1866A1A863C1 (void);
extern void AnimationPosePlayable_CreateHandleInternal_m28BA11AEB7DBA3258B5DB1CECF7A72F3D3A93A3C (void);
extern void AnimationPosePlayable_GetMustReadPreviousPoseInternal_mF24F70490ED57DBD1F4AE5D32C57BCC949DFE3CE (void);
extern void AnimationPosePlayable_SetMustReadPreviousPoseInternal_mA4C7FEB6B3C710E4830DB901D1E0615E27588ED0 (void);
extern void AnimationPosePlayable_GetReadDefaultPoseInternal_mB85F523B7DA39AF3FFE5F8FAC1519A414B95BD73 (void);
extern void AnimationPosePlayable_SetReadDefaultPoseInternal_m2B8EE66706344492E94D64DA7189173A54ABB1EC (void);
extern void AnimationPosePlayable_GetApplyFootIKInternal_m80669092972522067D6DBF598EE8E22EF9001664 (void);
extern void AnimationPosePlayable_SetApplyFootIKInternal_mBDFE5547E8B8A90CC309294C257D655BFE5E0667 (void);
extern void AnimationPosePlayable__cctor_mFA5FE84F06C8E9A89C07190055BC898525F897C4 (void);
extern void AnimationPosePlayable_CreateHandleInternal_Injected_mB4BE0A67DA8722C0F7DE09741DC0B91B021D047A (void);
extern void AnimationRemoveScalePlayable_get_Null_mD85DD91001F1FDB7C8DF0CC942A1E752C494CD46 (void);
extern void AnimationRemoveScalePlayable_Create_mABCE46803804604412781B86B321BE816649401C (void);
extern void AnimationRemoveScalePlayable_CreateHandle_mE15186AF821904AC09E8F0DC30096BF63A812D66 (void);
extern void AnimationRemoveScalePlayable__ctor_m4D6C7C4AB8E078050B0CC34C6732051CF043CFA2 (void);
extern void AnimationRemoveScalePlayable_GetHandle_mFFA58B879F31327187A20ED30E1C814B7BEAA9C6 (void);
extern void AnimationRemoveScalePlayable_op_Implicit_m0658568E028F0099166238728756300A839F0606 (void);
extern void AnimationRemoveScalePlayable_op_Explicit_mF01DB06BE2F531B09785C8EF2FA1DF34FE08E9F7 (void);
extern void AnimationRemoveScalePlayable_Equals_m0ACDD59B80103591DA8E84CB387FB10778D8C327 (void);
extern void AnimationRemoveScalePlayable_CreateHandleInternal_mDB6C3A766BA756328A459D4EBB356A8F8E289FD1 (void);
extern void AnimationRemoveScalePlayable__cctor_m42E614B0B33898D92DFE06CA6045698BE94DE633 (void);
extern void AnimationRemoveScalePlayable_CreateHandleInternal_Injected_m0ED3A968526682B946A2D157855A91B93CDE9A13 (void);
extern void AnimationScriptPlayable_get_Null_mD4EB9FA15C4D1C87EC7E0E42D31FB790CA7BD2D4 (void);
extern void AnimationScriptPlayable__ctor_m6DEFD72735E79009FC1484AA2A7A82E6CE601247 (void);
extern void AnimationScriptPlayable_GetHandle_m30355B6EE1AA3BA36D628251FB4291386D223646 (void);
extern void AnimationScriptPlayable_op_Implicit_mBF02678AFA7A679981EED33224D5B2E3AEB215C0 (void);
extern void AnimationScriptPlayable_op_Explicit_mD74C3B99C11B5A3421E1240186EF49E475EA279D (void);
extern void AnimationScriptPlayable_Equals_mAD02E40704CBE4AB188DE0569052F8EA9864F4E4 (void);
extern void AnimationScriptPlayable_SetProcessInputs_mF7DE3561731A6513E8FE0F0A85B0C38959621839 (void);
extern void AnimationScriptPlayable_GetProcessInputs_mDF9A5B45217407FB9ED7DF8E7A1E3DEA4DD31A1F (void);
extern void AnimationScriptPlayable_CreateHandleInternal_m0E0B65982F224BCD21CAD27DF8758C67A65296EC (void);
extern void AnimationScriptPlayable_SetProcessInputsInternal_m2ACAC5C02655BBBF15A32B06F5CF1BAFC0775D9A (void);
extern void AnimationScriptPlayable_GetProcessInputsInternal_m38A3E81CA628A2FE8801C32C3CE58DB7520E6C90 (void);
extern void AnimationScriptPlayable__cctor_m5ED4D3FC06BC7A51D3A48B5611F759CB00F7CF54 (void);
extern void AnimationScriptPlayable_CreateHandleInternal_Injected_m6DD2A68DB5A0F7A25529B1899FB308F248C557E3 (void);
extern void AnimationScriptPlayable_SetProcessInputsInternal_Injected_mD82651E462942B43F51D5C4456ABD600F159FD15 (void);
extern void AnimationScriptPlayable_GetProcessInputsInternal_Injected_mFD6AA32BC5EFB2187902E8F9DDF6F4781D96CA22 (void);
extern void AnimationStream_get_animatorBindingsVersion_mD7D19DCE96F93CE4DC36457F974C5B8562A3B5E4 (void);
extern void AnimationStream_get_isValid_mE1F032BDA653D5A903DCD427F4677A6C9C4C227A (void);
extern void AnimationStream_CheckIsValid_m6C46800E1A5A4BE27FF761A93F72BC3CD751174C (void);
extern void AnimationStream_get_deltaTime_mECEF75B188313080405BFB556AB4CFD972233861 (void);
extern void AnimationStream_get_velocity_m8628858DC7927B5DC15C3050945D8150E4166179 (void);
extern void AnimationStream_set_velocity_m0C9F3849570576287D94FEC72556BA070F9B3D49 (void);
extern void AnimationStream_get_angularVelocity_m3D7FCC57BD7A9EE99FE9DDAB3B425C700841C617 (void);
extern void AnimationStream_set_angularVelocity_m0600FEEDAC435358DAE51BC5AA4EEAEED6649DD8 (void);
extern void AnimationStream_get_rootMotionPosition_mAE5E4330D95DD4AB9BD65341CCB4340259235A40 (void);
extern void AnimationStream_get_rootMotionRotation_m0CE33CEDB1195D26AE380A561A487CBB508A0530 (void);
extern void AnimationStream_get_isHumanStream_mABF4806846E32130D5D4C204CAE4E8E0DA897158 (void);
extern void AnimationStream_AsHuman_mADFB59FE0BC7C00348B567BE06A4E8E9C830A916 (void);
extern void AnimationStream_get_inputStreamCount_m434C3915F96FC4C707622AA6F8F40F5BEF0031D0 (void);
extern void AnimationStream_GetInputStream_mFFAEDE4760FD9FB17C6F702DB3E1835C77600E49 (void);
extern void AnimationStream_GetInputWeight_mE168D42B18134F5B6F11B932C9EF3D523B4759DA (void);
extern void AnimationStream_CopyAnimationStreamMotion_mA5E2C0B009574DF88C47B57A3ABAFC6C20FA3EB7 (void);
extern void AnimationStream_ReadSceneTransforms_mE817BB3693A822A761DFF489AC5661F93E076CCD (void);
extern void AnimationStream_WriteSceneTransforms_mD0A8D14E6A70F2DD4639E1D265F3D28961B8401D (void);
extern void AnimationStream_CopyAnimationStreamMotionInternal_m47DAF2063EEF6D09F538BD3A9E039DD01C68243F (void);
extern void AnimationStream_GetDeltaTime_m335ACEAEEAEE7E3FAE1CCBD81DA839C6C1CFF0A9 (void);
extern void AnimationStream_GetIsHumanStream_mD58FA1E5EEF324695C45C8AAF3A79D13987DE576 (void);
extern void AnimationStream_GetVelocity_m554A288784E59A32E2D39B826912890127AAC57F (void);
extern void AnimationStream_SetVelocity_m66A0FF467A4423DD1BEE265EADBEE273CB764382 (void);
extern void AnimationStream_GetAngularVelocity_mCEAA43EE55D952A83B0AC4236DFFCD249DC8587B (void);
extern void AnimationStream_SetAngularVelocity_m6860A649F2D2A7B7082502753E5B94C12A10640A (void);
extern void AnimationStream_GetRootMotionPosition_mF64C52D3BC86F9B31E50426ABA638C5FF6E59D22 (void);
extern void AnimationStream_GetRootMotionRotation_m4132427788382A623CF0703FF56AC34D6E31C1FD (void);
extern void AnimationStream_GetInputStreamCount_m3E9B20B8AA6D1A6704FEF58B384ECCDCC67C3624 (void);
extern void AnimationStream_InternalGetInputStream_m19EEF4A1AA1BF053B21D5059E3836699D55A1974 (void);
extern void AnimationStream_InternalGetInputWeight_mB4CE342A125415612BA0D6CF329EFE4DD15DC503 (void);
extern void AnimationStream_GetHumanStream_m719D5DCDC0B6B4CC92E93BDFAA51D138A34DCC1F (void);
extern void AnimationStream_InternalReadSceneTransforms_m3CB8D564DC110A6A7475FA9F00C95D4294CAC588 (void);
extern void AnimationStream_InternalWriteSceneTransforms_mA489D8D3B1B02D7455BF7FBDFF4D76409878ACFD (void);
extern void AnimationStream_CopyAnimationStreamMotionInternal_Injected_mCC4054F2FD0903D676F008618EF987DDA597D600 (void);
extern void AnimationStream_GetDeltaTime_Injected_mF06679592F1FEFA1DD24E4553554913F59DAAE55 (void);
extern void AnimationStream_GetIsHumanStream_Injected_m94B820440CE0A9F6EC7466876D09592F2AB24F46 (void);
extern void AnimationStream_GetVelocity_Injected_mD436F0A4F5AAF20EB1DAD703F83EF67A47D3E6CE (void);
extern void AnimationStream_SetVelocity_Injected_m96A388162B416B60DC6ECC5BA2C98E93619045F8 (void);
extern void AnimationStream_GetAngularVelocity_Injected_mB80547FB50E345B97CD5D61A646D799CDCB09416 (void);
extern void AnimationStream_SetAngularVelocity_Injected_m1364F49A0C2B4856D493819AED904D7D842C2793 (void);
extern void AnimationStream_GetRootMotionPosition_Injected_m4047461DC4096579DE3B6D16F45D98781D2B29DD (void);
extern void AnimationStream_GetRootMotionRotation_Injected_mE7A685170F97248DD4FE2B1E7D3E763BB08ACBED (void);
extern void AnimationStream_GetInputStreamCount_Injected_m969395150936A3DD845571CE2D9A0F31EADABF92 (void);
extern void AnimationStream_InternalGetInputStream_Injected_m5E3E1BB36337B71FC93266B6A9DA83D538781DA5 (void);
extern void AnimationStream_InternalGetInputWeight_Injected_mD307C8B079738BA84418C65CC212F9671BD7D8C3 (void);
extern void AnimationStream_GetHumanStream_Injected_m99C333328AFCBB2201F1705C516AEE77F81C51EE (void);
extern void AnimationStream_InternalReadSceneTransforms_Injected_m473A4981947C9C4A8040C88E874C506C5A839ECB (void);
extern void AnimationStream_InternalWriteSceneTransforms_Injected_mE16C1422B6A493D4A2B75E30DE8F453140456916 (void);
extern void TransformStreamHandle_IsValid_m96EE3A490B88868890CF2E754838F97424A65512 (void);
extern void TransformStreamHandle_IsValidInternal_mBF1602E33ABCA25121C7CF70173D29C5291354CC (void);
extern void TransformStreamHandle_get_createdByNative_mCC27504004588C367456D55E8295B745BE2431AC (void);
extern void TransformStreamHandle_IsSameVersionAsStream_m31E41B516413440AC8F4D5F9F233623A6DE71365 (void);
extern void TransformStreamHandle_get_hasHandleIndex_m164F6D37B1A6B74214B09E1E6798C275C71716D1 (void);
extern void TransformStreamHandle_get_hasSkeletonIndex_m8B9589FACB6810B0EFD84033D30057ADFBC4B75F (void);
extern void TransformStreamHandle_set_animatorBindingsVersion_m5F9ED42B51BED505332D0D1B88CB823A8BEF3A01 (void);
extern void TransformStreamHandle_get_animatorBindingsVersion_mD044F88843A162A554BA7EF191E52B58F9F0AFF8 (void);
extern void TransformStreamHandle_Resolve_m5DDC5761EF01E700ABDB214030802982DABC3E6E (void);
extern void TransformStreamHandle_IsResolved_mB2A9548AAB37C1485AFEC2C9CD9A3D4ABC786D52 (void);
extern void TransformStreamHandle_IsResolvedInternal_m83781A03679DF4C678FE963CF21F5A2203471585 (void);
extern void TransformStreamHandle_CheckIsValidAndResolve_m7602706A5D46D99268DB6C698A6752C96A0525F6 (void);
extern void TransformStreamHandle_GetPosition_m8980B6C6185653E9B962625D030C3BB1994C8B89 (void);
extern void TransformStreamHandle_SetPosition_m45609A840DAAF0410F72218E58E2207841290002 (void);
extern void TransformStreamHandle_GetRotation_m02E0CE9B403FB4138605190A48A19767D6B0C42A (void);
extern void TransformStreamHandle_SetRotation_mA159153895AFFB08B30B8287304A720215C364D1 (void);
extern void TransformStreamHandle_GetLocalPosition_m30A1BF0A06551177E6D28A73D3DE71522B77C0A4 (void);
extern void TransformStreamHandle_SetLocalPosition_mEC97D6C69019B8212F444B33DB51BDCD9DCD6282 (void);
extern void TransformStreamHandle_GetLocalRotation_mB613F5958303751C9368AD2CC613723E279985AF (void);
extern void TransformStreamHandle_SetLocalRotation_m68B0586FA34978971ECDC909A44E1E0C13443C6A (void);
extern void TransformStreamHandle_GetLocalScale_m559039B8F2285CC33E3E952F078EF899C8ACB451 (void);
extern void TransformStreamHandle_SetLocalScale_mEAC5ED65AA0B8F756E2C129ED14D78C7B3698FE2 (void);
extern void TransformStreamHandle_GetLocalToParentMatrix_m91AC76F7B4D7B530A6D30E87E68E75C50B95DA77 (void);
extern void TransformStreamHandle_GetPositionReadMask_m2901E4573FC6105194851C3FD1B88A3F18DA4F5F (void);
extern void TransformStreamHandle_GetRotationReadMask_m8D17943EA0E9F130DA1B5A2CE336625AAC7E4888 (void);
extern void TransformStreamHandle_GetScaleReadMask_m12F8441C4AE4B23345BA37F5DFD82B5288695230 (void);
extern void TransformStreamHandle_GetLocalTRS_mF633398360834FAD1B1F8E21EF8C2A01B3E38A8D (void);
extern void TransformStreamHandle_SetLocalTRS_mA4D470AC9B87FF6FAC880A926BD3A5F4EC30BFB2 (void);
extern void TransformStreamHandle_GetGlobalTR_mA0526AC698E96B95E6BE3E17A477DB028EF8A499 (void);
extern void TransformStreamHandle_GetLocalToWorldMatrix_m19D802014D758A8BE531FE3A7000371D59C5B195 (void);
extern void TransformStreamHandle_SetGlobalTR_m8C4F35DE8E639AE7D7F94F1D015AD3C16D2FC406 (void);
extern void TransformStreamHandle_ResolveInternal_m0008C8228981E9247DA8B0C7739DD1BF1C70EAEA (void);
extern void TransformStreamHandle_GetPositionInternal_m4D0EA1C47F1AAB4723411247DBA15135BA6A9D4C (void);
extern void TransformStreamHandle_SetPositionInternal_m0855C3D765D6635BFEBA847061CDC90B645246CC (void);
extern void TransformStreamHandle_GetRotationInternal_m53A7E32CE6B63F588F44CAE8FCBF23C32E8393C7 (void);
extern void TransformStreamHandle_SetRotationInternal_mE7AB735A2303DC923A69B19537FDE60B5F39CE5A (void);
extern void TransformStreamHandle_GetLocalPositionInternal_mDF78249F5365FA56D51A9854D69DBD420CA2408A (void);
extern void TransformStreamHandle_SetLocalPositionInternal_m1758971CB7DC05A269612D1B975A22C8CB2CB890 (void);
extern void TransformStreamHandle_GetLocalRotationInternal_m5AD8291814578D1F199FBBD2E336C43FC387CEAB (void);
extern void TransformStreamHandle_SetLocalRotationInternal_mDE8F5BF1C73A90573EF3918F1C88ABB73BC10778 (void);
extern void TransformStreamHandle_GetLocalScaleInternal_mAE69D739C71A1F9AB26E9E3496294B6662F429A2 (void);
extern void TransformStreamHandle_SetLocalScaleInternal_mE108B8F9D39C2C540C8619C7DECFDB685040F85C (void);
extern void TransformStreamHandle_GetLocalToParentMatrixInternal_m4D7A1D9602675F7998C24CCDAB752F8E7BAC8DBF (void);
extern void TransformStreamHandle_GetPositionReadMaskInternal_m18D7DFE8500F0B2BC42F06324B0286B73F8062FF (void);
extern void TransformStreamHandle_GetRotationReadMaskInternal_mB7D7E96C8D8CD0FBED076396FB14955E31CA732D (void);
extern void TransformStreamHandle_GetScaleReadMaskInternal_m1D3791F8161BA91D9EC2D5D744B05D33E419FBA4 (void);
extern void TransformStreamHandle_GetLocalTRSInternal_m1B1B9B973843354BCA7D7A5A76CE44EFE7F2A203 (void);
extern void TransformStreamHandle_SetLocalTRSInternal_m2FC862511AEAC5C2900D016CF31EB2E25D321D8B (void);
extern void TransformStreamHandle_GetGlobalTRInternal_m71E4832B7C5D99A91FDF742CA3E54F1C43CE34AF (void);
extern void TransformStreamHandle_GetLocalToWorldMatrixInternal_m218BEFABE5A04FE2DF4F0E038049386C6A8C5EC0 (void);
extern void TransformStreamHandle_SetGlobalTRInternal_m9C62E8BD63B362A404C376B09005C024F54DC7B4 (void);
extern void TransformStreamHandle_ResolveInternal_Injected_m3C2F5E0470031F9C0D33DD5C7C392F8B671B9CED (void);
extern void TransformStreamHandle_GetPositionInternal_Injected_m3C3917F08A68CD4E24653CB78AEEA3577E043462 (void);
extern void TransformStreamHandle_SetPositionInternal_Injected_mC8D3099FC5DB732B46C4FAD237DA63CA59B2EA5A (void);
extern void TransformStreamHandle_GetRotationInternal_Injected_m4863F14E289B9627C1ACADB750B56CD81ED6F857 (void);
extern void TransformStreamHandle_SetRotationInternal_Injected_m181A24DB2A658E488E3F0BC684A424547A6A7683 (void);
extern void TransformStreamHandle_GetLocalPositionInternal_Injected_m365FDC3BE3E31E7E83ED3E348C089A788E25C724 (void);
extern void TransformStreamHandle_SetLocalPositionInternal_Injected_mD54FB99E9BA8700F04F9A6C12F4FD857B1F68258 (void);
extern void TransformStreamHandle_GetLocalRotationInternal_Injected_m0C379159283C0F4FEA175431383404203C624630 (void);
extern void TransformStreamHandle_SetLocalRotationInternal_Injected_m58D671FC665AF45BE7B6753A65C3F27BDCD89EA3 (void);
extern void TransformStreamHandle_GetLocalScaleInternal_Injected_m2D639FCDA072EBFE62AF09EFBAE8DD09C1C82903 (void);
extern void TransformStreamHandle_SetLocalScaleInternal_Injected_m0AEE77042B2DC3DE6DD4D92C92129B4A52CD4742 (void);
extern void TransformStreamHandle_GetLocalToParentMatrixInternal_Injected_m7EEDFAD32C00769D94C9E71AD905D5A56F547776 (void);
extern void TransformStreamHandle_GetPositionReadMaskInternal_Injected_m2BBE94003338D8FE34E7862C779452B59A5C06E9 (void);
extern void TransformStreamHandle_GetRotationReadMaskInternal_Injected_m155352ECE44BEB714E222AA193145903074B75C4 (void);
extern void TransformStreamHandle_GetScaleReadMaskInternal_Injected_m97B2F5382EABF761AA94A424C0C630FECD1B015F (void);
extern void TransformStreamHandle_GetLocalTRSInternal_Injected_m2684C260C70D030AA66A6D9EE0641C6F8A0F41A1 (void);
extern void TransformStreamHandle_SetLocalTRSInternal_Injected_m4185E07C39CD19689720F3F5B6161938C39BAAF6 (void);
extern void TransformStreamHandle_GetGlobalTRInternal_Injected_mA425C4A4674833F9EEB44FBC2C5DB357B3EB306E (void);
extern void TransformStreamHandle_GetLocalToWorldMatrixInternal_Injected_mB6365AE0653C1E6F7D1E44266ACDC6048E86FFD0 (void);
extern void TransformStreamHandle_SetGlobalTRInternal_Injected_mDE035F852727673707A01C6475FC52E38D98DB6F (void);
extern void PropertyStreamHandle_IsValid_m21FEF08137BB2DC014F731A98CBA5F516939723E (void);
extern void PropertyStreamHandle_IsValidInternal_mFE619567B465984FC8E00F07CC24D489802BB51B (void);
extern void PropertyStreamHandle_get_createdByNative_m2610F75D942E639F8D9919D9A8A8E2210503292A (void);
extern void PropertyStreamHandle_IsSameVersionAsStream_m105BA0425054D86214E70C3D0746517A0BBD5305 (void);
extern void PropertyStreamHandle_get_hasHandleIndex_m296B641953CA1478332DE8D4E3616EDDE67F4415 (void);
extern void PropertyStreamHandle_get_hasValueArrayIndex_m6BFF272278DB968E4732EE7BAA990F18258DC610 (void);
extern void PropertyStreamHandle_get_hasBindType_mF482FD67DC2BBB8AF20A959C846430577FCC51B3 (void);
extern void PropertyStreamHandle_set_animatorBindingsVersion_m091EA76553DCD034CF179B9F31BD25057B83764F (void);
extern void PropertyStreamHandle_get_animatorBindingsVersion_mAF352761E16C2BC5658A2B37C77EFC88173EA4C0 (void);
extern void PropertyStreamHandle_Resolve_mE4B9D57D9092E7278EF68192F8E5F57D1ED3B645 (void);
extern void PropertyStreamHandle_IsResolved_m44BC7FEA11CAA3862B733E319C7794F1C3536D86 (void);
extern void PropertyStreamHandle_IsResolvedInternal_m7B96232330AB117B2050D16FE135103D6ED97DFA (void);
extern void PropertyStreamHandle_CheckIsValidAndResolve_mD4036D6F0444B68BC4C1AECDD7429FEBAF03203C (void);
extern void PropertyStreamHandle_GetFloat_mAEC50079467900B74F7B485BBAF65A4DE1BBB8DF (void);
extern void PropertyStreamHandle_SetFloat_m9FA9F67C2AA473A7395EACF2563E7C8A06008C33 (void);
extern void PropertyStreamHandle_GetInt_m58CECCE1A73DB1EA96E287B5B7BF51BF502D8D98 (void);
extern void PropertyStreamHandle_SetInt_m1E7A8AF3165E77EB0148AC17FB641711134DBE48 (void);
extern void PropertyStreamHandle_GetBool_mBB5B008988E6CC47C526CF654E53804909C141E0 (void);
extern void PropertyStreamHandle_SetBool_m8307F19E9C41A431FC9A71128A305B547BE675C7 (void);
extern void PropertyStreamHandle_GetReadMask_m70E5969F001669CC1467E03014CF46C5E865F9DE (void);
extern void PropertyStreamHandle_ResolveInternal_mFB0A48675D8D847197CB392325CDCA837B82E64C (void);
extern void PropertyStreamHandle_GetFloatInternal_m11E03DE3C420D8F9BFA7926D1F452766BD34B783 (void);
extern void PropertyStreamHandle_SetFloatInternal_m044594EAE3DEEC6030E096DB0A8F0454ADCAD6A8 (void);
extern void PropertyStreamHandle_GetIntInternal_m598EC48F1700FB43EF1A5880178BB841A781D4C9 (void);
extern void PropertyStreamHandle_SetIntInternal_m37894828B9FD37A78CCE5A6F9F9EB7E1C0FE72A4 (void);
extern void PropertyStreamHandle_GetBoolInternal_m25D06AA6F53B3265E80243E589F39EF4C30E7DAD (void);
extern void PropertyStreamHandle_SetBoolInternal_mD237DF6939F5BE683D485E984C37791624C67A26 (void);
extern void PropertyStreamHandle_GetReadMaskInternal_m28FDDC616FF6A3C9BF19F19FCC9103DD337DF9BF (void);
extern void PropertyStreamHandle_ResolveInternal_Injected_m2F38D58EFF0643C85F7ABA035DAC718D5A885B0F (void);
extern void PropertyStreamHandle_GetFloatInternal_Injected_m6604EAC314F4B69CAC8601BE540A723ADCC8734E (void);
extern void PropertyStreamHandle_SetFloatInternal_Injected_mBFF2DBFE042CB9C68B804BB413E28171D465A94F (void);
extern void PropertyStreamHandle_GetIntInternal_Injected_mDFDACC53C8FAA687E154D78C9A86B58EA836AF14 (void);
extern void PropertyStreamHandle_SetIntInternal_Injected_m825B4B4CC03932B30E2A9DA34500012C429765FA (void);
extern void PropertyStreamHandle_GetBoolInternal_Injected_mDE6D207C4962AC0FC356962421418A632D7120D6 (void);
extern void PropertyStreamHandle_SetBoolInternal_Injected_m4489226DE93D01A970F9BF2A71E11D0F826C804B (void);
extern void PropertyStreamHandle_GetReadMaskInternal_Injected_mC8AAD4CF45A0468422450726DEB7EFBB1E3D40AD (void);
extern void TransformSceneHandle_IsValid_m11DB3FA1E3137CA1C2D4D4BC18AD717FCCAC65E2 (void);
extern void TransformSceneHandle_get_createdByNative_m40C489AAD66DEDFEB69F6BB25B1177FC51922D3D (void);
extern void TransformSceneHandle_get_hasTransformSceneHandleDefinitionIndex_mBD4B49152989D4379E6D726B0F7E834EA484383B (void);
extern void TransformSceneHandle_CheckIsValid_m33214B5950C49A143A5548B7FB1672062204655A (void);
extern void TransformSceneHandle_GetPosition_mDB8261C4AF79828292D555DBF91A6559DE41B3B8 (void);
extern void TransformSceneHandle_SetPosition_m2119B1F81EB229CED3914AB214783D67C036EEF8 (void);
extern void TransformSceneHandle_GetLocalPosition_mF5AFF93B5129702C280CCEB0603100377BFE2D32 (void);
extern void TransformSceneHandle_SetLocalPosition_mA8A45E45843E29DEB53A99827816C7B0BFE01376 (void);
extern void TransformSceneHandle_GetRotation_m8F6CA3E2302A43103A808120AEE1C527EB2A2F05 (void);
extern void TransformSceneHandle_SetRotation_mB91EC437053AE3E393393C1D6B3F62270AA6DB0F (void);
extern void TransformSceneHandle_GetLocalRotation_mEB7C44D455654A40518C8AE8D8CC0D191E194B8D (void);
extern void TransformSceneHandle_SetLocalRotation_mF7DF58A87432FF06B73AFEB19D4030108F2CFCED (void);
extern void TransformSceneHandle_GetLocalScale_m46D9A20E7DC5967A0EE6F7C217FAA68FBB93611E (void);
extern void TransformSceneHandle_GetLocalTRS_m5FBE0248443B3D34C7787A50DE89BB473638CADB (void);
extern void TransformSceneHandle_GetLocalToParentMatrix_mE9193C3FF11071363E75E8F8BD89A285BFAF3797 (void);
extern void TransformSceneHandle_GetGlobalTR_m7CB0885446CA0894BF681F1954C3E0FB8D31C9EB (void);
extern void TransformSceneHandle_GetLocalToWorldMatrix_mF79BCAA91FBE2949EC89A0A010A9AAFCFC2BE9FE (void);
extern void TransformSceneHandle_SetLocalScale_mE408411FD205489EAA1E148B98C2427BF3C201D3 (void);
extern void TransformSceneHandle_HasValidTransform_m19ABA61E5902DA7F1207798BBB4DCFBE515B9537 (void);
extern void TransformSceneHandle_GetPositionInternal_mAC29651E3EB5FC3BD4CB49B2B09EB9897375FC29 (void);
extern void TransformSceneHandle_GetLocalPositionInternal_mAFD402FC292DD1D9BDBF6BE446E89CDD85A47182 (void);
extern void TransformSceneHandle_GetRotationInternal_m53DDDEE9D5824A6E5BFEE5C5681A33E468E0FC5E (void);
extern void TransformSceneHandle_GetLocalRotationInternal_mC63DB1B1DA36584AC8C550683E9E2F67F350A688 (void);
extern void TransformSceneHandle_GetLocalScaleInternal_m15883C28FC7DE56F00374B6A14F027CFC6C35069 (void);
extern void TransformSceneHandle_GetLocalTRSInternal_mBB816EAB9B873A23A8B01C96789480F438968497 (void);
extern void TransformSceneHandle_GetLocalToParentMatrixInternal_mAD951B3EF38C5A5F5043E1427B21BEFB5F1265DD (void);
extern void TransformSceneHandle_GetGlobalTRInternal_m821151819DD1E3F5ABA1D2C8DC5372C67A2DF343 (void);
extern void TransformSceneHandle_GetLocalToWorldMatrixInternal_m963B53C92A8B6BC96C2DFE67E015156B731C6E50 (void);
extern void TransformSceneHandle_HasValidTransform_Injected_m1F0CEB3799D44896F7F501AE0E99BF49938ADA4B (void);
extern void TransformSceneHandle_GetPositionInternal_Injected_m5CB0EBF73345BD9F90E504DCEFE1C2D4B349FAB0 (void);
extern void TransformSceneHandle_GetLocalPositionInternal_Injected_mA2701FE4C3866CF6BB83C18C183D197975A002F8 (void);
extern void TransformSceneHandle_GetRotationInternal_Injected_m50FA487714327D6B98868DB6322D1F72260B8BBE (void);
extern void TransformSceneHandle_GetLocalRotationInternal_Injected_m580089F9FDCF7CB1BFBFDBA70EE7C49BA3B90304 (void);
extern void TransformSceneHandle_GetLocalScaleInternal_Injected_mD0E2E2A2B1728035C791DC88BAC3E16C3B96FBBE (void);
extern void TransformSceneHandle_GetLocalTRSInternal_Injected_m09BD17EECEC5ED6D21F7224862F01C0BEAD9CE9C (void);
extern void TransformSceneHandle_GetLocalToParentMatrixInternal_Injected_m3FF576BEEC1B3E8F2D88AC99A36AF688F8422D89 (void);
extern void TransformSceneHandle_GetGlobalTRInternal_Injected_mBAF0D818077C87003CFACC9BCE40CD62C5208A26 (void);
extern void TransformSceneHandle_GetLocalToWorldMatrixInternal_Injected_m2C5850C76DE15C45908CB78F27702C4BDD5227C4 (void);
extern void PropertySceneHandle_IsValid_m88FB030E99AE0E2D8A4506C7B06BAF8B27CE481E (void);
extern void PropertySceneHandle_IsValidInternal_m60ACFD75DC826CB176ED25ABDF6244EEEFA26D7F (void);
extern void PropertySceneHandle_get_createdByNative_m1579532A69F2E0052D6028BB8563E693DACC044F (void);
extern void PropertySceneHandle_get_hasHandleIndex_m8A4BD33134980310FFD1568BF2B0F1E7D88D7E8A (void);
extern void PropertySceneHandle_Resolve_m4ADA3EE521AB78FEFCEDBC666CA66868D67B0075 (void);
extern void PropertySceneHandle_IsResolved_mFD3DCB641948607A93E40B0D2E6897333F68D7EA (void);
extern void PropertySceneHandle_CheckIsValid_mA5326B76CA28F14695D23826F1F43215C353A033 (void);
extern void PropertySceneHandle_GetFloat_m2DA1CB5219344BB6A59421E15A74C6ADE286EC24 (void);
extern void PropertySceneHandle_SetFloat_mCDF00CD9F89DB5906FA72CAD9CE1AB98E3322E12 (void);
extern void PropertySceneHandle_GetInt_mA22A93D80220EE3CF94C7DCFCC70A958F125418E (void);
extern void PropertySceneHandle_SetInt_m927C12912421F35BDBC5FF6FA34BB5A716E536CE (void);
extern void PropertySceneHandle_GetBool_mE876BC9201F73F814AFF5F4A82C90CC02BEDD18C (void);
extern void PropertySceneHandle_SetBool_m43589AABBCE73E5BDF47E24EAADC309908CEF5DB (void);
extern void PropertySceneHandle_HasValidTransform_m4DFB6634221B060FB846A975C2AA76881CFA978B (void);
extern void PropertySceneHandle_IsBound_m75BC5C73C0C65F3967C0B2E6519ED4EF495E19DE (void);
extern void PropertySceneHandle_ResolveInternal_m9C4C80E37AB2178EDA3B31506BE2F56721E1BF3C (void);
extern void PropertySceneHandle_GetFloatInternal_mBD6D41B4F23687D5CEE7F1674E457C03BF4CF692 (void);
extern void PropertySceneHandle_GetIntInternal_m8AC9D22272A7199D6BBCA877B036408D5975BC46 (void);
extern void PropertySceneHandle_GetBoolInternal_m4714FA71EB7064EAFF5D40643989E41D057ABB75 (void);
extern void PropertySceneHandle_HasValidTransform_Injected_m3561AC3CAF7D517E207BDB05E1F9ECE52082B822 (void);
extern void PropertySceneHandle_IsBound_Injected_m98EAD11BDF0C2805F4524AC24FB13D00E5BBE21C (void);
extern void PropertySceneHandle_ResolveInternal_Injected_mA6D826D06AED57135DD0AA667F8EDDF49AC7FFFB (void);
extern void PropertySceneHandle_GetFloatInternal_Injected_mC6B0F0F768C5656C99B0C3B958F3540B99D8D9E0 (void);
extern void PropertySceneHandle_GetIntInternal_Injected_m9A26C8FBB24DFEB662D2305FE49185EC79992F5A (void);
extern void PropertySceneHandle_GetBoolInternal_Injected_m80855977FA31E62257B376179C02E18EB577A8CE (void);
extern void AnimationSceneHandleUtility_ReadInts_mFC6B98B06607D4A175D4598B3D18EBB29E000C41 (void);
extern void AnimationSceneHandleUtility_ReadFloats_m55F39914E47481B30592FA1DDB4354AF133F29EC (void);
extern void AnimationSceneHandleUtility_ReadSceneIntsInternal_m4D25EC39B7F2F147D646B83534EC3DE56B2FFE18 (void);
extern void AnimationSceneHandleUtility_ReadSceneFloatsInternal_mDF52B54C3765CC9856DC4F5A5BF9D358D4D4312A (void);
extern void AnimationStreamHandleUtility_WriteInts_mA481ED34FED75546E0DBF8EC47266FC78AE80EC6 (void);
extern void AnimationStreamHandleUtility_WriteFloats_m647C25053281C77FAA8F6BC29D54A96E5DDEFE63 (void);
extern void AnimationStreamHandleUtility_ReadInts_m970637617496B33CB3F12995A9303BA38643B3AE (void);
extern void AnimationStreamHandleUtility_ReadFloats_m4F0751C5758AC2784E0BFB81788BC63411F7E3C4 (void);
extern void AnimationStreamHandleUtility_ReadStreamIntsInternal_m0FF4C2321EB39B856E55FD1689892162BA681BD8 (void);
extern void AnimationStreamHandleUtility_ReadStreamFloatsInternal_m6BAC0676E422F2C256C06AA0EA32F7B66D04F84C (void);
extern void AnimationStreamHandleUtility_WriteStreamIntsInternal_mFCD153B37ABA13A6B7F69CDFB62F44F47BC10FC2 (void);
extern void AnimationStreamHandleUtility_WriteStreamFloatsInternal_m189497A194CA6D6F1EC649B83C6AA42476089649 (void);
extern void AnimatorControllerPlayable_get_Null_mF129F956CBA0B632810CC49EB890C10759A0C85F (void);
extern void AnimatorControllerPlayable_Create_m2A01CF0D8E08995514A106B713E30021E39E8333 (void);
extern void AnimatorControllerPlayable_CreateHandle_mEC5A01894B274C6EC5AD8FBD84688C29AFBCF698 (void);
extern void AnimatorControllerPlayable__ctor_mBCB9475E2740BE1AEB94C08BAD14D51333258BFE (void);
extern void AnimatorControllerPlayable_GetHandle_m718D9A4E0DB7AC62947B1D09E47DBCD25C27AF6C (void);
extern void AnimatorControllerPlayable_SetHandle_mD86A3C0D03453FAF21903F7A52A743AB2DA6DED4 (void);
extern void AnimatorControllerPlayable_op_Implicit_m728BDEDB8AA352D8535567FFEEA678E6DE300922 (void);
extern void AnimatorControllerPlayable_op_Explicit_mE1F117CC1D3254DED4A465D2068C1A1A4EDDBAF3 (void);
extern void AnimatorControllerPlayable_Equals_m14125BB4CCFCDFFD098223AF20E38501BA264180 (void);
extern void AnimatorControllerPlayable_GetFloat_m787538C1B2ED9C4E52E8A3EEF43405913E82E895 (void);
extern void AnimatorControllerPlayable_GetFloat_mDBD6169B671FCD0AA3551368A3EE37334E7D7B49 (void);
extern void AnimatorControllerPlayable_SetFloat_m95BF662BF3AA8A4F4B5F868801DED66D397E2407 (void);
extern void AnimatorControllerPlayable_SetFloat_mEDD694D72DDBEDF381973F41D83037F330224EDC (void);
extern void AnimatorControllerPlayable_GetBool_m30C0C093BA04B2C680E5B8510A189A66A4D5333D (void);
extern void AnimatorControllerPlayable_GetBool_m72BB4B2A95A2C229ADE9B51F7232B8FF4E5DF26E (void);
extern void AnimatorControllerPlayable_SetBool_mDBBE0C1A970D71F600234B15D0E6B9C51E7A70DC (void);
extern void AnimatorControllerPlayable_SetBool_mEBE969EAB2935C3A15848521B06ABB45B1188BAD (void);
extern void AnimatorControllerPlayable_GetInteger_mFE3ADA2A4AD9A7A62B60D7E5F2FCBCF9A5F58AA0 (void);
extern void AnimatorControllerPlayable_GetInteger_m50B3042FDC1F87A4E0423FCCFEED611996A3860D (void);
extern void AnimatorControllerPlayable_SetInteger_m4C8B5F237C20CFABF0CAD460F55782D834444A91 (void);
extern void AnimatorControllerPlayable_SetInteger_m6FA135B1C91BED0F97C9E623FB8C37411C6F15B4 (void);
extern void AnimatorControllerPlayable_SetTrigger_m06D7662315ED85A8472E62C2F369B1167BAF51A7 (void);
extern void AnimatorControllerPlayable_SetTrigger_mA2F8F838D48244468DACBF503E120FF3B06E95B2 (void);
extern void AnimatorControllerPlayable_ResetTrigger_mD671335B03DF16D311287002303191D50B802D14 (void);
extern void AnimatorControllerPlayable_ResetTrigger_mABE88FFA8781EB19F5B06F7BF483F13C61107D6C (void);
extern void AnimatorControllerPlayable_IsParameterControlledByCurve_m34F8D191A09BC28CCDF1A044FD02B33A1D64D0B1 (void);
extern void AnimatorControllerPlayable_IsParameterControlledByCurve_m8ADD7EE7A489424B4B99253C96647E67B04E2D1B (void);
extern void AnimatorControllerPlayable_GetLayerCount_m3941A7DA375B26988A59DA13A22C08476D59D34F (void);
extern void AnimatorControllerPlayable_GetLayerName_m33BDF74B10C6BFDB82FF404E288F52F1EBABC809 (void);
extern void AnimatorControllerPlayable_GetLayerIndex_m48AB42D7F82BE03576D4AC480F048EEDF01B0A0D (void);
extern void AnimatorControllerPlayable_GetLayerWeight_m09D5E7AB77824DE2DE20E4E4491BF2A90E3B28BB (void);
extern void AnimatorControllerPlayable_SetLayerWeight_mE432DA7CC2FC4425D0DA064B71B5ACCEB83F8EE3 (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorStateInfo_mBA937DD74A4964C743880FFE7CFFE67B18D8264B (void);
extern void AnimatorControllerPlayable_GetNextAnimatorStateInfo_m1BD26B635F70840C13BC34D0632D82B5EA48CDE0 (void);
extern void AnimatorControllerPlayable_GetAnimatorTransitionInfo_m5157FED95B567D4441228BC0F3AF31563FD5BF1C (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mF89B409BFDD2B021BB6862B93A68879BCAB60076 (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mB103A1F9EC5E23947C8A1802D644FC4D22093F14 (void);
extern void AnimatorControllerPlayable_GetNextAnimatorClipInfo_mF00B978E844FF7E6DF0AF670C0CF2022A8DD2517 (void);
extern void AnimatorControllerPlayable_GetAnimatorClipInfoInternal_m18301A84A3ACAE2F7A34B0966564CD4F65D2BB2F (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorClipInfoCount_mB80BDB04DF276D53D45EAA549CAD88DA9A7E8BE8 (void);
extern void AnimatorControllerPlayable_GetNextAnimatorClipInfoCount_m19CD5C12E89C22E3521175CD6C1E2F033C53D070 (void);
extern void AnimatorControllerPlayable_GetNextAnimatorClipInfo_mD247136758432A4036FC338D4C8E67ECAF5EDD26 (void);
extern void AnimatorControllerPlayable_IsInTransition_m041E31E9DAD976867BCCBDDD27CB556F590D61D9 (void);
extern void AnimatorControllerPlayable_GetParameterCount_m899EE6DAD7209174D7568E632DA986D39E435A70 (void);
extern void AnimatorControllerPlayable_GetParameter_mB7ECCC4E41138CC7D9A28ABF499679B8792E9CC3 (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_mC4104110B90B6802409F8FC102AC5758250FAE74 (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_mF66C581A902E013AF240151A4B9772F8DD94E95A (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m43166ED8F80B94469DFA25374911A64637438674 (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m664DCD090D5E91612254A0F87AB5D2A538DF8DC5 (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m7901F79283F0C1357A14AE48D81BA663D98740C8 (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m93EC21F858FE76184FA91D2B6EA3B361A2929503 (void);
extern void AnimatorControllerPlayable_CrossFade_m93543957867BB7F6B7A5498EF7ECDBAFCA0C295F (void);
extern void AnimatorControllerPlayable_CrossFade_m3B003ED0E44C2405B104B7E7ECF1AC0A6C15C7DB (void);
extern void AnimatorControllerPlayable_CrossFade_m8AD5C075E4B4B645796A36980AE15378167019AA (void);
extern void AnimatorControllerPlayable_CrossFade_m4CADD7D865B6C0A16D35B58011895E560CCF8824 (void);
extern void AnimatorControllerPlayable_CrossFade_mC2E4C450F3E777DBE08BBBF0138FAA174D0526C5 (void);
extern void AnimatorControllerPlayable_CrossFade_m0C777C0385BE3E8AA8D86C99897D490ED553F9DD (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m797B8A53344C62FF813DC398D1E6A6B18A826275 (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_mA9FA57E1D8B5B4DB655F1918C338585573B7DEEA (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_mE4DAC931BFEDBCAABE0D410BE3DF85C5C4FF1425 (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m37882EB28A1E75354814AB9EB6426D7F3FF8D985 (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m62E44D644C7785CFF04229D6EA808F9C670FF010 (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m513E1A9D864FCC8DF9E16A9DE05465A5694ACE2B (void);
extern void AnimatorControllerPlayable_Play_mD967CAA3998D88A9116C165D1D1ADDEEC8D3FEFF (void);
extern void AnimatorControllerPlayable_Play_mA3346301B00AD22D27B692BE36E560569FCD2765 (void);
extern void AnimatorControllerPlayable_Play_m703A612D5040F4F99C17D300E5BD57ABB992E976 (void);
extern void AnimatorControllerPlayable_Play_m3C33626F6950B0CD33EB39A87C93819B6CC9C52D (void);
extern void AnimatorControllerPlayable_Play_m252547934FE64DCF1DC9D3242E20200A0E8852D1 (void);
extern void AnimatorControllerPlayable_Play_mB14FF22BE5BF41EB807BD3F14C111A3D60E59394 (void);
extern void AnimatorControllerPlayable_HasState_mE537ED9F84D34939019463D4A2F6171B053759C2 (void);
extern void AnimatorControllerPlayable_ResolveHash_mF09713D683CEF1F188415CE3868CF117B04FA322 (void);
extern void AnimatorControllerPlayable_CreateHandleInternal_mF52B5F176A31C938DF5909127B27B1E78E3863D4 (void);
extern void AnimatorControllerPlayable_GetAnimatorControllerInternal_m31844CB21CF7B71C2A3C3F7C29F37ACC70C91BCD (void);
extern void AnimatorControllerPlayable_GetLayerCountInternal_m1E9E07012C508DF0D452D350F658AB897B2C7C76 (void);
extern void AnimatorControllerPlayable_GetLayerNameInternal_m29B1F15DB851564CA5D4D505F71636FBCAD787B9 (void);
extern void AnimatorControllerPlayable_GetLayerIndexInternal_mF8B5003325DC63AA1E35959BBC77F54BA5FB69B6 (void);
extern void AnimatorControllerPlayable_GetLayerWeightInternal_m35F8593E7F54FF389010F2C2C202662F78A4D076 (void);
extern void AnimatorControllerPlayable_SetLayerWeightInternal_m35738E7223F0B22A86DAA2BF2066132E25FD1B8B (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorStateInfoInternal_m9EBFBAF82969F46703B6F9C15F42C29CB963063E (void);
extern void AnimatorControllerPlayable_GetNextAnimatorStateInfoInternal_m6573FAB9FB964669D28460771FACBADFC300FC3E (void);
extern void AnimatorControllerPlayable_GetAnimatorTransitionInfoInternal_m02EDD789D0AF671B56CC16D7172FDE8193A1C469 (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorClipInfoInternal_mACAEC7A2BCB3EE6DCE40A1608DA3F466E4A9BEEF (void);
extern void AnimatorControllerPlayable_GetAnimatorClipInfoCountInternal_m30ACC99D56EF6C81325F21B16538A70689A4F712 (void);
extern void AnimatorControllerPlayable_GetNextAnimatorClipInfoInternal_m9FE0E9288A890CDF52C9F825C619188BBFEB7F6C (void);
extern void AnimatorControllerPlayable_ResolveHashInternal_m1C4C380AF316FDF38E6099F7A91E9366CCACCE58 (void);
extern void AnimatorControllerPlayable_IsInTransitionInternal_m0DA12D5A39272162175FC51E47458871BB8B4E42 (void);
extern void AnimatorControllerPlayable_GetParameterInternal_mD6516C314E1BAF64567E613D9F31975C6046C647 (void);
extern void AnimatorControllerPlayable_GetParameterCountInternal_m382C6BFCEE151B9DF3DFE232B287D6C6D522DD8D (void);
extern void AnimatorControllerPlayable_StringToHash_m75E3FCE65BF77734C4781FADFFC9568B0EE483E2 (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTimeInternal_m2F3C0BAE59468B079106B730F8F0EE9874928561 (void);
extern void AnimatorControllerPlayable_CrossFadeInternal_m4861EAD661B4EA490CDAF4413F8D6E1F1EF6F6D2 (void);
extern void AnimatorControllerPlayable_PlayInFixedTimeInternal_mC0683AD548C14DA24F82388F68150F174275E376 (void);
extern void AnimatorControllerPlayable_PlayInternal_mD5A3AAC8CFE034C5D8FA9CC26AC9CAA364145524 (void);
extern void AnimatorControllerPlayable_HasStateInternal_m1D49854E9E6AFAC2DFE979DFF6911B6946B5555A (void);
extern void AnimatorControllerPlayable_SetFloatString_m0F0870A5299A2CF4AFC6C65832D189A5D04D4FD5 (void);
extern void AnimatorControllerPlayable_SetFloatID_m3BA4ADD5B318BFF0294EB9ACA58480A66168297A (void);
extern void AnimatorControllerPlayable_GetFloatString_m1DADE177E526468221C892871355B7E0371DC7B5 (void);
extern void AnimatorControllerPlayable_GetFloatID_m4EDF8B5F43B2F67DDCF3A566C520E99AF92316E0 (void);
extern void AnimatorControllerPlayable_SetBoolString_m25BA69DB53D2B741C3A3816F52F2D8B8DB94A563 (void);
extern void AnimatorControllerPlayable_SetBoolID_m9D7D23783C516EBA887139E4F3C2089D4F6630C9 (void);
extern void AnimatorControllerPlayable_GetBoolString_mECF14FDF6E8B96688869FD00C1CC5F91C54EAB16 (void);
extern void AnimatorControllerPlayable_GetBoolID_m7061D395F3200F2128DC6320515789ECB1350D4F (void);
extern void AnimatorControllerPlayable_SetIntegerString_mDC746DB1E487E0A5FB8258FB1FB697D3FEB14579 (void);
extern void AnimatorControllerPlayable_SetIntegerID_m683204581C2F1791D94A53D8E9E81C8C4AA25860 (void);
extern void AnimatorControllerPlayable_GetIntegerString_mD0C70C8553191AD4B4367CA0C1C7892097157713 (void);
extern void AnimatorControllerPlayable_GetIntegerID_m364BCCFECB516503043592BCE6093B6E71573901 (void);
extern void AnimatorControllerPlayable_SetTriggerString_m0EC136890F3D7FC3CCE31D7D97406AEC14BA96A2 (void);
extern void AnimatorControllerPlayable_SetTriggerID_mB0D1B0D7CFAEA440E8582C322215E5F8843BBFF3 (void);
extern void AnimatorControllerPlayable_ResetTriggerString_mC531FDD220C61491E53D609F021DE45AB0A517E8 (void);
extern void AnimatorControllerPlayable_ResetTriggerID_m4FB3CD3245F90046AEBEB7DAC46F4ED8A17C2E70 (void);
extern void AnimatorControllerPlayable_IsParameterControlledByCurveString_m9E408D31C0E277E3B81355622EA263D0A889FFBB (void);
extern void AnimatorControllerPlayable_IsParameterControlledByCurveID_m485E73DF5945A41CBD21A8F9ABDFBE9CDD0C07E6 (void);
extern void AnimatorControllerPlayable__cctor_m88506D1B15D609B818DFDC6B2BCFF42ABB41B090 (void);
extern void AnimatorControllerPlayable_CreateHandleInternal_Injected_mCBD001EF18F0143DD0568A9D5FBAF157206ED21D (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorStateInfoInternal_Injected_m7B608D6B42D37184F7C778E9DEBCD77B350923AD (void);
extern void AnimatorControllerPlayable_GetNextAnimatorStateInfoInternal_Injected_m6939850E0366960A04B5B3009220B1AAC6F77D0F (void);
extern void AnimatorControllerPlayable_GetAnimatorTransitionInfoInternal_Injected_m6CFF884FDBEF401BA13CAA1F9FA68EFC56B62870 (void);
extern void AnimatorJobExtensions_AddJobDependency_m31CDA4A7972693AB5D702648FF5974C1A1A8665C (void);
extern void AnimatorJobExtensions_BindStreamTransform_m9E23872D781BC0754909267ED356266392F4E26D (void);
extern void AnimatorJobExtensions_BindStreamProperty_m199C36204F072AECF047AEFC92B1A62A61FF2F7F (void);
extern void AnimatorJobExtensions_BindCustomStreamProperty_mB19AC2FE9BCD4C98DA571A375080E17A114D374C (void);
extern void AnimatorJobExtensions_BindStreamProperty_m02E8A9116C4DB619A2B4E28AEADC593ECDE78994 (void);
extern void AnimatorJobExtensions_BindSceneTransform_m78A489DBF2ED9127F74F35C33CD9C712AC73DE76 (void);
extern void AnimatorJobExtensions_BindSceneProperty_mB75521D482FE6417947F331C8831AB67B7B6E8B9 (void);
extern void AnimatorJobExtensions_BindSceneProperty_m5D7AEB5EEDBC08245FDBE69BF129BF0CE66B7743 (void);
extern void AnimatorJobExtensions_OpenAnimationStream_mA4B55DC4D4BDF17E29D7805FB3D0A8645007BEF3 (void);
extern void AnimatorJobExtensions_CloseAnimationStream_mE9E79A50871A9BB08D077BCD4350005246F23516 (void);
extern void AnimatorJobExtensions_ResolveAllStreamHandles_m189D44E0381884741BC18CDFCE77F41C09345A9E (void);
extern void AnimatorJobExtensions_ResolveAllSceneHandles_mD59455EAF32FBF555C8C02FB55A0312795C045E8 (void);
extern void AnimatorJobExtensions_UnbindAllHandles_m7D7D00929C0F88E88BA68441C7027F2B66823B56 (void);
extern void AnimatorJobExtensions_UnbindAllStreamHandles_m12CFFA21A9E9FFCF3CACF885F42DBDBD6C19585F (void);
extern void AnimatorJobExtensions_UnbindAllSceneHandles_m9DE9F0298845F4485B2A1FF8DAE7EEB60CF61262 (void);
extern void AnimatorJobExtensions_InternalAddJobDependency_m71BA59576E0FAA88A5C60F7211D172BF2DE24B03 (void);
extern void AnimatorJobExtensions_InternalBindStreamTransform_m83A49A8A13E2FA7AD67FAECA1B24F11D15550C2F (void);
extern void AnimatorJobExtensions_InternalBindStreamProperty_mE09521CB89443739B6428A0FD6B012D8C0601415 (void);
extern void AnimatorJobExtensions_InternalBindCustomStreamProperty_mFD263ACFEF7E93728830EFD5133399F922EF6504 (void);
extern void AnimatorJobExtensions_InternalBindSceneTransform_m5C33CC9DC1C95933DA91E42411080A4DA49216C6 (void);
extern void AnimatorJobExtensions_InternalBindSceneProperty_m7C4D6BE064BBE342314E1BCD6101F451794E0A11 (void);
extern void AnimatorJobExtensions_InternalOpenAnimationStream_mCC05CB24B2F24F6B3E2DC4797F7F5AD9C02427D8 (void);
extern void AnimatorJobExtensions_InternalCloseAnimationStream_m639A895B6E57F3005799C0C1D56E5CA415AF39D3 (void);
extern void AnimatorJobExtensions_InternalResolveAllStreamHandles_m9FC0F152F03B51A446535E28F4761F5C136AAC31 (void);
extern void AnimatorJobExtensions_InternalResolveAllSceneHandles_m94621370D35BB9025DE191C0B448159D25CE19CE (void);
extern void AnimatorJobExtensions_InternalUnbindAllStreamHandles_m7D6642F5D90653F937352C073EF8B8BC7CC839E6 (void);
extern void AnimatorJobExtensions_InternalUnbindAllSceneHandles_m1E802D79857566FEEE9C27B938D3F688043D992A (void);
extern void AnimatorJobExtensions_InternalAddJobDependency_Injected_m8798C8F3B7A272A86C5A3E7437977CF71FDFAEEE (void);
extern void ConstraintSource_get_sourceTransform_m0A9C0C96F68398626E43F2137E2CAB5BA803F94F (void);
extern void ConstraintSource_set_sourceTransform_mC905D9E04293D785BA40E1E0378457A77170A31B (void);
extern void ConstraintSource_get_weight_m90743C7AB9BA12A99FB8C81442E765A4F947BE28 (void);
extern void ConstraintSource_set_weight_m40EADC470F7D906EEB89A515F75CC8B0648368D7 (void);
extern void PositionConstraint__ctor_m625C24233306DDBC2D80F9738952F377E8E97500 (void);
extern void PositionConstraint_Internal_Create_mB13A191DEA183B3DD5895B3A7DFBEF2807CF9129 (void);
extern void PositionConstraint_get_weight_m4600BDC603A83F8EC429BDE05F4194F2C8A0299D (void);
extern void PositionConstraint_set_weight_mA5D54C3164C45396C5BAF7309C90CE6626B3E7C8 (void);
extern void PositionConstraint_get_translationAtRest_m4E7168580A88DB7FDF1D5D7DBE243E80F2D80046 (void);
extern void PositionConstraint_set_translationAtRest_m974249528D567468847532DC9CCECF90E7825131 (void);
extern void PositionConstraint_get_translationOffset_m7DAE61CD823E25EA20B2CFFF47BE2B3C463610B2 (void);
extern void PositionConstraint_set_translationOffset_m296C3070AFA239658481646267023AD85778A4BE (void);
extern void PositionConstraint_get_translationAxis_mE9F51805A4AB7125CA95721F7473873DB550ABB0 (void);
extern void PositionConstraint_set_translationAxis_m045D55B2A505B7FDDBC889E6364B78BEB5DEC812 (void);
extern void PositionConstraint_get_constraintActive_m290BD123E800C32336E0C9FA3E7D1A3F6ADF9662 (void);
extern void PositionConstraint_set_constraintActive_m325FCFC0950DCC95804662D344A26AAA2BCF579E (void);
extern void PositionConstraint_get_locked_mCB288BD466038AF07BF0C9B7CD4CBD365C1E8945 (void);
extern void PositionConstraint_set_locked_mAEA85B37FC6DDC14F25324060C163566CC4E5F3C (void);
extern void PositionConstraint_get_sourceCount_m5459C2629024A7FAC52A52385BEFCEE922369228 (void);
extern void PositionConstraint_GetSourceCountInternal_m3477DAD40AAA08EB469A5B2D4D27871532BEE0F4 (void);
extern void PositionConstraint_GetSources_m58D7A3BB891A29C00467A900BD29BB3D0E442753 (void);
extern void PositionConstraint_SetSources_mB62ED52943DF3E6C360CCCA86BCBB36AA5283CAC (void);
extern void PositionConstraint_SetSourcesInternal_m1B0ECA3AAE218EB0F49A3DC5DFBAC71040366207 (void);
extern void PositionConstraint_AddSource_m88074D53713F6804955EA339B91152A75429AFE6 (void);
extern void PositionConstraint_RemoveSource_m45377027910F7B683BE3E5B2F7DC09AE3CB25C7C (void);
extern void PositionConstraint_RemoveSourceInternal_m3A823DC00C2E15CE38E91CC1F06C5AF001DCE1D1 (void);
extern void PositionConstraint_GetSource_m845F32F9A6AE984D239AFA5CD0B2942403D16C1C (void);
extern void PositionConstraint_GetSourceInternal_m526B9EEDEC7672851B52AFF42C6AD7E5C1D02DBE (void);
extern void PositionConstraint_SetSource_m0B987AD5624AE45C3671E7671D53E1373023E6B4 (void);
extern void PositionConstraint_SetSourceInternal_mC89EE76071C107C8DEAEBE257BAD2FE9D71BB8CF (void);
extern void PositionConstraint_ValidateSourceIndex_m536CE4DA81C077887BCEA71634F1CAE8E7DA08C5 (void);
extern void PositionConstraint_get_translationAtRest_Injected_m761A40080D8AA5E7524B638364F4040FFDBF764F (void);
extern void PositionConstraint_set_translationAtRest_Injected_mAD837585A4E08DC208DF3584EFECCDEE668FBAC6 (void);
extern void PositionConstraint_get_translationOffset_Injected_m6826C01E0F3D94DB82667F2155795BBB9388EECC (void);
extern void PositionConstraint_set_translationOffset_Injected_mA052DE0C5006E39D4918EE61C28EFD7DAAD63A87 (void);
extern void PositionConstraint_AddSource_Injected_m9F815DDAFA4EE6CD1756FE7383E37C909DC7EBCF (void);
extern void PositionConstraint_GetSourceInternal_Injected_m4354DBD9864B8D70FF219C64A7B1D1F80A6A252E (void);
extern void PositionConstraint_SetSourceInternal_Injected_m707DD39B1E6851D98B3C35F73FD42473EB1D9AD6 (void);
extern void RotationConstraint__ctor_m0C440527A1423E3CBC75F1737CB7B57F59A054C9 (void);
extern void RotationConstraint_Internal_Create_m17D7461540706218FD0A911A4D021B7F93E03B70 (void);
extern void RotationConstraint_get_weight_mE257D80144388C59E4F8765B645E28C694BF69F5 (void);
extern void RotationConstraint_set_weight_mEB48E254F6592BA6E3027B7036D185F13502C3F2 (void);
extern void RotationConstraint_get_rotationAtRest_m516517904E682810077927F2C65120524772EE1A (void);
extern void RotationConstraint_set_rotationAtRest_m6D64E9A3904510D40770DF9D418862723B9FFA91 (void);
extern void RotationConstraint_get_rotationOffset_m825C20481CF7084A6A91E8706B32343B503F390A (void);
extern void RotationConstraint_set_rotationOffset_m028C803BEA3CAC32B18DF71E5A52942E8A80D08B (void);
extern void RotationConstraint_get_rotationAxis_m6C88CE69CECA2943309DB1C2CA6BECAB98D02D42 (void);
extern void RotationConstraint_set_rotationAxis_mAC05428F3AF6871C239F340931D44064DFF34170 (void);
extern void RotationConstraint_get_constraintActive_mECFCC2877BEC6F1CE962AC670002F8301C5B5860 (void);
extern void RotationConstraint_set_constraintActive_m4B764D283A56068B70D0E00FD37AAC0BDBF0CDEA (void);
extern void RotationConstraint_get_locked_m4DF430D48E85047173520BC41CA90918051354DC (void);
extern void RotationConstraint_set_locked_m7C55E40B8D4F42DCE2FA747E1C56AA719AE613A7 (void);
extern void RotationConstraint_get_sourceCount_mE3B2D13FFE49F98A845FDF4A8B57BE81757BD579 (void);
extern void RotationConstraint_GetSourceCountInternal_m9C9CEBB382332A72C013F5302CB991F78AE8FB1B (void);
extern void RotationConstraint_GetSources_mF2B536A36D4C4B9DEEACFC1A9ACE1375A77EF9F2 (void);
extern void RotationConstraint_SetSources_m22CC7C29C33738E3CFFBBD18FB7DBB66B52A0965 (void);
extern void RotationConstraint_SetSourcesInternal_m8EA458F4FA7204BAD7A76B6375B01725B3F931EB (void);
extern void RotationConstraint_AddSource_m95354FA9CB0789975527F0380EBADFBCE3981E75 (void);
extern void RotationConstraint_RemoveSource_m5EBEFEA6163B25303A37B3A63294E2AFADCD69F9 (void);
extern void RotationConstraint_RemoveSourceInternal_m33A48A4951998F7F5921E19ADA3B4971FAF6D138 (void);
extern void RotationConstraint_GetSource_m5A7BFD4CD25C48E1FFA702070B60CDF513AD4C2B (void);
extern void RotationConstraint_GetSourceInternal_m07E39771BC5B72C1988F778D152617C467D92D73 (void);
extern void RotationConstraint_SetSource_mBEF215D44BDD2CBE54BD26C85234EC2E65AD0036 (void);
extern void RotationConstraint_SetSourceInternal_m4865E43F4F1928FE244A5EEE2F1547C3A3C72C86 (void);
extern void RotationConstraint_ValidateSourceIndex_mF6D796A860D909EAA1E9E83AB5D5421207E9300B (void);
extern void RotationConstraint_get_rotationAtRest_Injected_mBABDCA5888B414CC014350F517191F6D42F693CE (void);
extern void RotationConstraint_set_rotationAtRest_Injected_m78AB11DDFE7D9AAAF53FFB211DD7389619129A44 (void);
extern void RotationConstraint_get_rotationOffset_Injected_m39761F88A1DE102CBA99AA06A0A0F70B71EFC99B (void);
extern void RotationConstraint_set_rotationOffset_Injected_m0FE67D3A9A1B30F1AA112B89C3B40274CB8179D7 (void);
extern void RotationConstraint_AddSource_Injected_mF7B857F14490C64AFD45E75AD40BED3D190C981F (void);
extern void RotationConstraint_GetSourceInternal_Injected_m6F23903777F31156F285232B226294342CCE19EB (void);
extern void RotationConstraint_SetSourceInternal_Injected_mB88DCEE97E95A1E055226A5887E1FB05AA13763F (void);
extern void ScaleConstraint__ctor_m48A3D2F0576E661835C3BB47709F355598CDC024 (void);
extern void ScaleConstraint_Internal_Create_m9B62351F8883B89652BFD5E267DF2DCA9AE9E634 (void);
extern void ScaleConstraint_get_weight_m22C103255CC4B6DED7406A88D212B8112CDB0CEF (void);
extern void ScaleConstraint_set_weight_m9A275D46B5BD2F8730F5142582461FB0C4DFA621 (void);
extern void ScaleConstraint_get_scaleAtRest_m15CEF6355710CBE97CACE3134B930096EED7AC91 (void);
extern void ScaleConstraint_set_scaleAtRest_m445F99838075F0162CE3B6565B011EE5F53F5D6F (void);
extern void ScaleConstraint_get_scaleOffset_m1CA45FEBDB738B65CF622A3F5A509471C58F8035 (void);
extern void ScaleConstraint_set_scaleOffset_m0A1A10E986458C91F3D7D9EABFFB3A2986B988E2 (void);
extern void ScaleConstraint_get_scalingAxis_m8BF0878A2F6958B858A99E3CA1E9EFB13CB678F7 (void);
extern void ScaleConstraint_set_scalingAxis_m1F358A26D7326902F35F089DB5CC72590356EC03 (void);
extern void ScaleConstraint_get_constraintActive_mB43F9CD1877CE8AF923C0B1382A5465603BBA491 (void);
extern void ScaleConstraint_set_constraintActive_mD736564D7D23F3FBC2E926B54E4DD2618724DA2D (void);
extern void ScaleConstraint_get_locked_m53CF2C1E99E4BC7A5E169BC31C8D588B2E7EBABA (void);
extern void ScaleConstraint_set_locked_m0316BD5EFD201E9AB5C186BB1676B265C59F09F9 (void);
extern void ScaleConstraint_get_sourceCount_mE246DEFC9CE00F8B28EDBEE8E486A5BBAACEB2FB (void);
extern void ScaleConstraint_GetSourceCountInternal_mB5231888EEF86850C0277DD9E7EECB65EFF54F00 (void);
extern void ScaleConstraint_GetSources_m5AB8F651508460DE2CC7D4FEACEA0183ABA45F1B (void);
extern void ScaleConstraint_SetSources_mC1ABAA1CC399F68F87EBDFAE00D3469742803207 (void);
extern void ScaleConstraint_SetSourcesInternal_mFB26F6DE285F0CDD3D6DF3306159857D3372D70F (void);
extern void ScaleConstraint_AddSource_mE9DB31F6824FFFA9D77CB16F198F5EF2BC48CAA6 (void);
extern void ScaleConstraint_RemoveSource_m75C969BF4A1286E546C2A3C94847171104E8100A (void);
extern void ScaleConstraint_RemoveSourceInternal_mEC4AC92F6BF6B1376039B712F9B81EDBD38BB051 (void);
extern void ScaleConstraint_GetSource_m8F50080A0A79D9C261C3C30060078115D6ACFD43 (void);
extern void ScaleConstraint_GetSourceInternal_m5E5A63CD8689D25172824717358E2C7BEB12CC16 (void);
extern void ScaleConstraint_SetSource_mCEC1F61D9836BB9589C66569A6CF7F178DF8337E (void);
extern void ScaleConstraint_SetSourceInternal_mC4E8DC2196DD7CED0A8087389BABDB7D5DEA752C (void);
extern void ScaleConstraint_ValidateSourceIndex_m4FBBED869E411A5C8D99CF2A7DE4DC67107620FA (void);
extern void ScaleConstraint_get_scaleAtRest_Injected_m707202A9D65334591BE692EF0F5EED239754890D (void);
extern void ScaleConstraint_set_scaleAtRest_Injected_m9B4866F17F90B6D3CCF48E26BF58C9A4A732D7E2 (void);
extern void ScaleConstraint_get_scaleOffset_Injected_m8A7C63E4AB8679F206E72B255DF0F9898C2E9E11 (void);
extern void ScaleConstraint_set_scaleOffset_Injected_mA82A306F23677BE38FDEA74B39AC38230BBF5090 (void);
extern void ScaleConstraint_AddSource_Injected_mB3190E6AA5E82340524BA01ED985CEE13C7B2099 (void);
extern void ScaleConstraint_GetSourceInternal_Injected_mBFCC0E46AE1C071C74607F1569A46606955FFE18 (void);
extern void ScaleConstraint_SetSourceInternal_Injected_m9146D0D39797CA9DB389F188718294DF23D5D597 (void);
extern void LookAtConstraint__ctor_mB3E8E0FFD083929C7AC31C974AF21D48D988797F (void);
extern void LookAtConstraint_Internal_Create_m73B11C7E321159279EF4AB244AD5927BA39E52FE (void);
extern void LookAtConstraint_get_weight_m5292EC5432FEA8234384204887646D2724A88611 (void);
extern void LookAtConstraint_set_weight_m825136F6F09B0B3014FB71B0104BB8CB138F50A0 (void);
extern void LookAtConstraint_get_roll_m98DBA48F03550A9D6A20D9A43242024634B2F5B1 (void);
extern void LookAtConstraint_set_roll_m62A39E55A380ED4B45C3ADE3E1F38F88A8FF4DE3 (void);
extern void LookAtConstraint_get_constraintActive_m2F9EF3AF5A43D1BAEEEF842C436BCE744488DD65 (void);
extern void LookAtConstraint_set_constraintActive_m00CCD8C06EE6B997DBDA4AE7D1785CE6DF7099C4 (void);
extern void LookAtConstraint_get_locked_m09C34596BA18102BC50E396046B4682668A4934F (void);
extern void LookAtConstraint_set_locked_mFD206AAC60BE8E54AE0C87B4BA4F5AD6A9485785 (void);
extern void LookAtConstraint_get_rotationAtRest_m87C23A301C0438133FF195F4A915B3B6316E1389 (void);
extern void LookAtConstraint_set_rotationAtRest_m56551096C25D8E80802894A85B3BBC090683665D (void);
extern void LookAtConstraint_get_rotationOffset_mF4E9EA22373E0382B4B7BFBE3259A4D896C1BA1E (void);
extern void LookAtConstraint_set_rotationOffset_m1DB153DD6285FC69AB3F83D5DB85941A665D400E (void);
extern void LookAtConstraint_get_worldUpObject_mF7CD986A7D5B657C4249E90E8CED47E685780CE0 (void);
extern void LookAtConstraint_set_worldUpObject_m79D273E357F3DF3542BC66B3EE076700EAC5FD72 (void);
extern void LookAtConstraint_get_useUpObject_mD2CF1369D6E024B8F27F8053F88C99CBEEBAAF94 (void);
extern void LookAtConstraint_set_useUpObject_m63568AC6E7173EEE9CDEA1B8C2BD4BAC7EBD3AD9 (void);
extern void LookAtConstraint_get_sourceCount_mBD0921A486A9BD25813B68218B5E620268D6348B (void);
extern void LookAtConstraint_GetSourceCountInternal_m6C3739F0ADB867B78D5BEC961FE7E79D60E49A45 (void);
extern void LookAtConstraint_GetSources_mAC4256AC1FBC36B7A32DEBE74851F5803898F47D (void);
extern void LookAtConstraint_SetSources_m331DB216D4DD9A3D4506C302B3CF934559D75553 (void);
extern void LookAtConstraint_SetSourcesInternal_mDDF9B75472D8E0EAD24D3AD670A8A585D304D29A (void);
extern void LookAtConstraint_AddSource_m8F35E16F3998CCDA5FBF1E258B6852C91E4E385E (void);
extern void LookAtConstraint_RemoveSource_mD79139F9695B320C2B2BDEE80154DB33CDB73C8C (void);
extern void LookAtConstraint_RemoveSourceInternal_m4E71E09F60399F69D03D7C5BBFBA7D1064DDE6E9 (void);
extern void LookAtConstraint_GetSource_m49CCA6341B7FDABD119DD4253AA5B56CFF14D3B9 (void);
extern void LookAtConstraint_GetSourceInternal_m7CA976A8DA13FF0A1A7C60CADF14D2F20FE770DE (void);
extern void LookAtConstraint_SetSource_m9E09AD5950F3B28B03EDC37DE9202E8EB1BF3DDE (void);
extern void LookAtConstraint_SetSourceInternal_m337F95FBEC45A95DC6F3D60E1D868B0F95DDA5DE (void);
extern void LookAtConstraint_ValidateSourceIndex_m9FA564FC7B8C2A6E0E885D1F6016EF924F5FC294 (void);
extern void LookAtConstraint_get_rotationAtRest_Injected_mF379C7B0864AD672A0EBB4B9F57B3C9619838B86 (void);
extern void LookAtConstraint_set_rotationAtRest_Injected_m3208C9F250CDB9882CF45DF68A75570F324468AB (void);
extern void LookAtConstraint_get_rotationOffset_Injected_mEBA6896533315E281E84C85E420435FE3ABC3C4F (void);
extern void LookAtConstraint_set_rotationOffset_Injected_m933EEA999F3A5F2104AE288DDF536A813F39307E (void);
extern void LookAtConstraint_AddSource_Injected_mCB360869E18E6DD98F0BB07CD9D6AE3C4053968D (void);
extern void LookAtConstraint_GetSourceInternal_Injected_m2DF357FF91D10539DFB9B123021E4D4FD9C3B21A (void);
extern void LookAtConstraint_SetSourceInternal_Injected_m72A87537C80EAB436B368377AC902B666DE41448 (void);
extern void MuscleHandle_get_humanPartDof_m8262C622AA62DC780CCA30CB5492F1235C45A505 (void);
extern void MuscleHandle_set_humanPartDof_m11EE483E985240C49307DF184DAE8B3260945BCE (void);
extern void MuscleHandle_get_dof_mFD825C91413A42221BD937827E3C743C8A776598 (void);
extern void MuscleHandle_set_dof_mB202EB7F0DE1EB8E0E3E84349914EA26F48EAA93 (void);
extern void MuscleHandle__ctor_m639F42D1909646875E9AC30B3394498060E5ACD6 (void);
extern void MuscleHandle__ctor_mBE2F28D04718BC0EF94766966956ECCC980889CA (void);
extern void MuscleHandle__ctor_m4A0A680BBA254F55AE88CBCA6A0B0A50A0E143C6 (void);
extern void MuscleHandle__ctor_mF082607DD932A653DABE2DE3DF4C624AAEB174F3 (void);
extern void MuscleHandle__ctor_m0103CC894F5E0BD46FD5FF9A0F20BBA11873AB6E (void);
extern void MuscleHandle_get_name_m298104348CF7CF2ED690FDB26039D283D9629174 (void);
extern void MuscleHandle_get_muscleHandleCount_mFAEA121C96CB067CFAE8BF988C638B7D37D862B8 (void);
extern void MuscleHandle_GetMuscleHandles_m8AABE04A8810F0A689605A9C52D939D5266D4B5E (void);
extern void MuscleHandle_GetName_m229299540D61FB707555C9C2D254DC7448B7AC22 (void);
extern void MuscleHandle_GetMuscleHandleCount_m438E7D7CD62C5524B4C8F5A53B74D3B975142C94 (void);
extern void MuscleHandle_GetName_Injected_m07CDFBFCBD22D6C7663BCA8713C76D5A4F95DF88 (void);
extern void ParentConstraint__ctor_mBADF790001F2A71F65EE60022BFB4B66E3BCB1CC (void);
extern void ParentConstraint_Internal_Create_m6331C06E8B005A7B43E45F37982884A24FE7A56D (void);
extern void ParentConstraint_get_weight_m8C1A0FC97FE83E60D07C118185331F270D747EF7 (void);
extern void ParentConstraint_set_weight_mB768A3F4BF4515FC446F2E63C86A3E14A60F4AE8 (void);
extern void ParentConstraint_get_constraintActive_m9FB6D303FA0B9B5CD6A8DB9B6DDFA51D5BA20C4D (void);
extern void ParentConstraint_set_constraintActive_m366F11DA9455E241BEF400642A8F0E5CE0B49F89 (void);
extern void ParentConstraint_get_locked_m053B77ECE30540156C6199112E92CCA5C97DC3B6 (void);
extern void ParentConstraint_set_locked_m1C8EDE9846FDE72DA67A6781FC13AA1110278AFB (void);
extern void ParentConstraint_get_sourceCount_m6D83D678E00CFBC77A4626EB9B904C59C24FFBCB (void);
extern void ParentConstraint_GetSourceCountInternal_mB5BC92E4C164357647E292EF2D52C37683FD24EB (void);
extern void ParentConstraint_get_translationAtRest_m16DA060F2BB48564C43510095EF679F2AFF24391 (void);
extern void ParentConstraint_set_translationAtRest_m8F2DE7E19EC96BFFAEDDD1278222A7AC8FBDFAF2 (void);
extern void ParentConstraint_get_rotationAtRest_m892940524B77396D70107E17690E6A795F75F9AF (void);
extern void ParentConstraint_set_rotationAtRest_m92ABAC02A41399B51D029658ECDCD35A45EFB5C9 (void);
extern void ParentConstraint_get_translationOffsets_m6815186B47121DF699835E85BE0A4FF6E745058B (void);
extern void ParentConstraint_set_translationOffsets_m846750075B774569D7248F249F3EACF8416F9F4A (void);
extern void ParentConstraint_get_rotationOffsets_m11D07DC9EB3DD9F1474B878855A107C583C45E4C (void);
extern void ParentConstraint_set_rotationOffsets_mD179E830233901E07E9C5EAAE80B5E341CAFA98C (void);
extern void ParentConstraint_get_translationAxis_m234D8BC12FCFF37A232E73FBAEA6872F3689BC89 (void);
extern void ParentConstraint_set_translationAxis_mE285A4B0590711E0546D95CC557CDCA9F4829948 (void);
extern void ParentConstraint_get_rotationAxis_m5860389AC94E07864C0541F06E3F74ADB96A0354 (void);
extern void ParentConstraint_set_rotationAxis_m44AE458EC9239A67A186898128D7CCB688E20847 (void);
extern void ParentConstraint_GetTranslationOffset_m7E65A4E612044326B2FA269032F6E550EACA4BFC (void);
extern void ParentConstraint_SetTranslationOffset_mFD4350AE123DE476B284219935CB52DE1544BF15 (void);
extern void ParentConstraint_GetTranslationOffsetInternal_m969D0E965C427D6C6A68BF93273532198E3754D0 (void);
extern void ParentConstraint_SetTranslationOffsetInternal_m9E22067F8904B87016C803380E24F266FDBFA01D (void);
extern void ParentConstraint_GetRotationOffset_m9D529F720B85A2ABEED9348FDDAE8FCFA8C4EE1E (void);
extern void ParentConstraint_SetRotationOffset_m8C346F0ECB6FF54DE74D3170229AD05522FB99AA (void);
extern void ParentConstraint_GetRotationOffsetInternal_mC82486490AE3FE3E1E36CBCF9995268972997A2E (void);
extern void ParentConstraint_SetRotationOffsetInternal_mC149045D55A9824067299E474112E61D0EAA2E14 (void);
extern void ParentConstraint_ValidateSourceIndex_m081826C60AECFBDBCA08F338D069837D0D75C49B (void);
extern void ParentConstraint_GetSources_m61780D428C67FA8FE4A76602A318C8CFC608503E (void);
extern void ParentConstraint_SetSources_m28BD51533D5A03FFEB7FD4946E34ECC22202D13F (void);
extern void ParentConstraint_SetSourcesInternal_m1ECB5E5FC1CF5A27F902BF026FC055EA86541E70 (void);
extern void ParentConstraint_AddSource_m37997059B86A1DB5EFD8E94F7EA754120DF99194 (void);
extern void ParentConstraint_RemoveSource_mDFEEB144F845AA25DDAC42331E93A792890D72CA (void);
extern void ParentConstraint_RemoveSourceInternal_m440F960F632129BE2E0D52E3AB1EFD1A44CD7B00 (void);
extern void ParentConstraint_GetSource_m48E2619DA020D365DA9FEC0846519EF01B3F7153 (void);
extern void ParentConstraint_GetSourceInternal_m7CB0AADF3B72B57C69A694770B3A02667F0A16B3 (void);
extern void ParentConstraint_SetSource_mE99D06ABA6405A9DF14E67FBF186E186769604B6 (void);
extern void ParentConstraint_SetSourceInternal_m351C44976FAC286B57CEA36229365075C52F3E24 (void);
extern void ParentConstraint_get_translationAtRest_Injected_m89D17C88F19CA6CADAE95347B74420B6102A374C (void);
extern void ParentConstraint_set_translationAtRest_Injected_mF2859A67B8BC8F0242FBCCF636ABE44AA949325A (void);
extern void ParentConstraint_get_rotationAtRest_Injected_mD6443232F13F365F114CE39E483A3BD061643A31 (void);
extern void ParentConstraint_set_rotationAtRest_Injected_mB979711ED121CE98917B6C03DEC58CC327C5277D (void);
extern void ParentConstraint_GetTranslationOffsetInternal_Injected_mAF14ED7FC902D069BC3FE34A8D12BD159E7724E5 (void);
extern void ParentConstraint_SetTranslationOffsetInternal_Injected_m3995DB6B0F0DB7740D1DAFB5ED3937F85117DE88 (void);
extern void ParentConstraint_GetRotationOffsetInternal_Injected_m4543B4BF0FCC22B4600868FAC123A8AA16B64EF4 (void);
extern void ParentConstraint_SetRotationOffsetInternal_Injected_mBABF60A9D803907CA7A629A84291937287F4A8B3 (void);
extern void ParentConstraint_AddSource_Injected_m0A8CB716DCF35CAE4C614632A76B6897F87E3788 (void);
extern void ParentConstraint_GetSourceInternal_Injected_mC0EB388BC00CF48E988B68EDC484BE25CA2E3309 (void);
extern void ParentConstraint_SetSourceInternal_Injected_m8B29FF73ADDAF1B4858CE140B58FCFB5560F9305 (void);
static Il2CppMethodPointer s_methodPointers[1634] = 
{
	NULL,
	SharedBetweenAnimatorsAttribute__ctor_mFB3E61611EB2549C557CE050B35CE8F65B51C918,
	StateMachineBehaviour_OnStateEnter_mB618EFE75A50CBAA3EE6471E64A3E2CA2A2C90FD,
	StateMachineBehaviour_OnStateUpdate_mC1A83A4F693AF3AB51BC592A0CE525CE4F320D6B,
	StateMachineBehaviour_OnStateExit_mC113F0B2F53847F9A6755B82D0AC53C971171CFD,
	StateMachineBehaviour_OnStateMove_m7229D5EFBA432665B9046FC3C21D463FFD281978,
	StateMachineBehaviour_OnStateIK_m310C17694D8D1B9D60D549259A39837F22FD3240,
	StateMachineBehaviour_OnStateMachineEnter_m0CEFF9E4946BFDC4F7066BEB4C961169DBC5073F,
	StateMachineBehaviour_OnStateMachineExit_m384B808E3961C6C2C375DF7487EF2B49E44E6CD7,
	StateMachineBehaviour_OnStateEnter_m491D81A9A64DE4AE02415A5909B74AE947EAE1B9,
	StateMachineBehaviour_OnStateUpdate_mF3130BE7BDD7C8B2470303FB1986A336E47CC98C,
	StateMachineBehaviour_OnStateExit_mD47A506ACE251A6341115CBE5607D05C01747127,
	StateMachineBehaviour_OnStateMove_m1A01C10E754426572C7BBA7AA13044FDA372FDFC,
	StateMachineBehaviour_OnStateIK_mCE3B4C71868B564EE6BE4B8663535058705C3B72,
	StateMachineBehaviour_OnStateMachineEnter_m0399B12419A4F990F41BD589C833E2D2C0076762,
	StateMachineBehaviour_OnStateMachineExit_mF8BB1A8851B0699FC1D85F538E16EF12C08BBB93,
	StateMachineBehaviour__ctor_m9663A75D1016E16D7E3A48E2D4E6466A041A00AB,
	Animation_get_clip_m6041709D3BC8EA54D2D65B9560D2B4E51F78BD51,
	Animation_set_clip_m631E1DE3F46E47782725C52E444CBB747EB83119,
	Animation_get_playAutomatically_m9438E14979AADF7FF46E20DDA9E3778010DC2CBB,
	Animation_set_playAutomatically_mF293663249E24B5D595F669515551F06309C96D0,
	Animation_get_wrapMode_m04458F869BF815491DECA59B8AF252DBB240BF0E,
	Animation_set_wrapMode_m8910BC560328BD5541332A0737CE1F784B1B1600,
	Animation_Stop_mE11F232FCFA0219EADD8080D7AD7CC626A536756,
	Animation_Stop_mC7063EBC09142C002FC225F5B16051FA8ABDAE65,
	Animation_StopNamed_m3D7C41081019DCBF820B002CCC7368B34182AB5B,
	Animation_Rewind_m2DFAC82FDF28BE4531009381C5444C6A0977D51D,
	Animation_Rewind_m41BCBFFCED0B8FA122A6543FE2A7C7520847FC78,
	Animation_RewindNamed_mDE5F5DE24A291E3B8B385555C7F7954EB3DB2211,
	Animation_Sample_m651BFFA298E1B72385B0DF85240AFFB8B7778E9C,
	Animation_get_isPlaying_mF0C19818820AF3FC7BD4B335B86D11043A64F3F9,
	Animation_IsPlaying_m571DF72FBE485EC4A61364C1C45548B79C1FEF7B,
	Animation_get_Item_m60997A8CDE7F415FC55FBB0D6D3F28339C4B32E8,
	Animation_Play_m717560D2F561D9E12583AB3B435E6BC996448C3E,
	Animation_Play_m5E86FA3D4C8C7F144565B6E3962FD5CF3E51F1C0,
	Animation_PlayDefaultAnimation_mD2F0091CD4214603FE368C658A3DB28A53D6D48F,
	Animation_Play_m95CC43633DC2B587AB3A0D1FF5F93B863A5440D5,
	Animation_Play_m894966605BBBE66B5D4AEEB52688BAFD7DA7DAA3,
	Animation_CrossFade_mFE7ECBC7CB8A95ADE8E1EDC3E668D014BF9B12C0,
	Animation_CrossFade_m0DFC263F0CA45915C28C648B652A4903AE5DB9BA,
	Animation_CrossFade_mA2C79723F05536F1A418DF43A06E7BA9F5D07EC3,
	Animation_Blend_m90E0B5AE10B2E3248B3CAAD7864EB6B211C5C198,
	Animation_Blend_mE7BF5EDC1AEAD0AF5E5CF364ED1F04404330E300,
	Animation_Blend_mBC6733B3BFADCE9E55A4D9824A442B1AFB14F64E,
	Animation_CrossFadeQueued_m87F57207443217445390E1FA27084D18D6CC2DD3,
	Animation_CrossFadeQueued_m68E472A3970210368C8CD486758A88FCDF9372DA,
	Animation_CrossFadeQueued_m7C790058752B686DD7A38789A60D4E4DDE97E316,
	Animation_CrossFadeQueued_mA3F2202CC65B1351B3BF69CB02F2BF92651D7627,
	Animation_PlayQueued_m0A0FDD3B2F7F96403B316F1613F9D8ED19ED187A,
	Animation_PlayQueued_mB9FDFAE88B0380EF87785B64CC193FB005F2F353,
	Animation_PlayQueued_mB93AD01FF0279C3DDBBD1CFDBBBE34686C29B9C4,
	Animation_AddClip_m10EDEAA1060E0356379BFE9AB24F97C9EBBED1A3,
	Animation_AddClip_m0DBAB71E23EA248D6A18C705B8AF0EA140D2FFDF,
	Animation_AddClip_m87A5BE2BFAC7F12D7C0FE5D954E657FDB8B4A7F6,
	Animation_RemoveClip_m997A0DB72E4FFD958A4D202B1A7C1EBE52951BFF,
	Animation_RemoveClip_m3695CFFF3622244B90CFAAEC2894D49244E53235,
	Animation_RemoveClipNamed_m0793ABC3F9165DB34CDA9B80215FBBA4415A60DB,
	Animation_GetClipCount_mD0A710E117CDB762F2FC1D355B16C7E2BE13FEA3,
	Animation_Play_m3DA43B73CDCC3DA363336806324CDEEC35A1B265,
	Animation_Play_mCFAE2E11CFB3F0ADE81D69CB0CF46B895A3A55E6,
	Animation_SyncLayer_mD252771EB0E53FDDCC6502B63347B066E1280E64,
	Animation_GetEnumerator_m247062F212D9C579DAE25D9BC86E4921DC5719E6,
	Animation_GetState_mFE0B2A4F4BD7F3DDE2CA699D6036607F0E7259FB,
	Animation_GetStateAtIndex_mA06564CBB11021A3ADA69EA0BCDCD820183E240F,
	Animation_GetStateCount_mB779E6750180C77CE5F2E81B78C9AFEE93FCB1FE,
	Animation_GetClip_m566FFEC9EA0EA79DB1BAC0B79F37CD6A3A85C296,
	Animation_get_animatePhysics_mF4024F79B0F6C900F54DF64DF91F42D77F4F2A9D,
	Animation_set_animatePhysics_mF4ADAE48947F9D6D4DB080ADA3325167573C1AE2,
	Animation_get_animateOnlyIfVisible_mE6624DC390977EA5850D1413654B68DEA0C075EA,
	Animation_set_animateOnlyIfVisible_m7521A2FED44DD8195D95BD74B9BFAA181D5A9B65,
	Animation_get_cullingType_m7BEBA3508BAC7044A73F77E22B0A88FA4E6143DA,
	Animation_set_cullingType_m908E15BF5DDBE9AB2A078ED1AC22268EA978022E,
	Animation_get_localBounds_m654E42F57C4CBCEA408C787A8CA4E05C84D79887,
	Animation_set_localBounds_mBFD11C7C4DB8CB5D1045B8623993AF7DD6B26DF3,
	Animation__ctor_m7FFA60C35B9EA42BCE1916D4E8ACEFAAA373AE51,
	Animation_get_localBounds_Injected_m4F88940400CD5D879240372F7A7979DABA43FE14,
	Animation_set_localBounds_Injected_m0575C70CA5F2401CB5D3F344945917E07F7DE47B,
	Enumerator__ctor_mF3FB66377864673B8DAF14A36DB6D069B98A92F4,
	Enumerator_get_Current_m4A17FE0020D4C8856EFC8EF531B99723FFB2B9DF,
	Enumerator_MoveNext_m82477C155D4F7CF98317C0EEC9F5070D196AA671,
	Enumerator_Reset_m687381C45ECBBD052447FAC06ECE9760FF23DA63,
	AnimationState_get_enabled_mF5CDE001664E72B2A0E3365E97E32850E81AC42A,
	AnimationState_set_enabled_mBB6FCF86704CDD80E4B3385231D4C9F400D7C6EB,
	AnimationState_get_weight_mE259E6DC21FC42FB3D73D7B339660DBCE1035949,
	AnimationState_set_weight_m9F93DB2215185BBB1CE1ECA43DE7DE7C756AC847,
	AnimationState_get_wrapMode_mECE5D8F9742A051006658BAB4CA4ACD30F15AD79,
	AnimationState_set_wrapMode_mBA7403836E770538E76C12676E18AC80D707F452,
	AnimationState_get_time_m2EA9A93D1AF7457F6CBB96B4EC0B57E6AE58D20D,
	AnimationState_set_time_m0310DBBC8065675B653D4A34019AD7CD748C4D74,
	AnimationState_get_normalizedTime_m98D373BA809DD9486D7042CE236198C0C69007E3,
	AnimationState_set_normalizedTime_m80C40785819379D8B3DD64D22E88338921ED5085,
	AnimationState_get_speed_mAE9654B13C4C5420F393AA34A5B1FC86F8DC4159,
	AnimationState_set_speed_m9FADB34E8FA313E07ABF8F90B6F4179D2DAF9E7D,
	AnimationState_get_normalizedSpeed_mB48DB49BE1D141B0A0489904CC5E137F1DC24033,
	AnimationState_set_normalizedSpeed_mC420AC9B7F9D4B386203979F560C32CC2F31F58B,
	AnimationState_get_length_m67C34EA47A3715AE3B9B0B91816E31A87C8AE11C,
	AnimationState_get_layer_m78FFBADF5F4663215D1FE513373E2381F3BFAD00,
	AnimationState_set_layer_mBB3CD27A27F255CE979B30FDF56E5FD0DCF9262C,
	AnimationState_get_clip_mDF1793A0E862BE0534E3099FAE98CACCE8F6BF61,
	AnimationState_get_name_m4A81461268F1D1011331B3B52340C306DEBA0FBC,
	AnimationState_set_name_m9E3EAF585F7FAF6C39BE01AF95321A5697532980,
	AnimationState_get_blendMode_m57FE9878CB3594C634C1BAEC6AD495F52D55EFD9,
	AnimationState_set_blendMode_mFC6DD1BB0FB6942A0C597DFE277587E10EB41311,
	AnimationState_AddMixingTransform_m6ACC6579170595D781435A4B8BE32BB33C9BB4BC,
	AnimationState_AddMixingTransform_m7F034D92A5317EB18D0A4B2B0AFC9767F0E151E5,
	AnimationState_RemoveMixingTransform_m38A90D11BC7E64E5868192C4EFB07EBC1712868C,
	AnimationState__ctor_mB18C82530ADA40EE3BC245AAB0673941D78B779E,
	AnimationEvent__ctor_mBC954085B1D18B436D08E7ADE3458B91E208F3B2,
	AnimationEvent_get_data_m143EE194CFC6C7EB4311C8B87939C4FB1895A2B3,
	AnimationEvent_set_data_m4417F0417790DC89BDABDAADD0051E3C2B5CADAD,
	AnimationEvent_get_stringParameter_mBD37923DF24A4D090FFA56C6FE2538D1815EBFE3,
	AnimationEvent_set_stringParameter_m5FD50F59D597512EF20D8BBA4F37213894908A2E,
	AnimationEvent_get_floatParameter_m00CC143874DF66921808693FE6E56D27AAC07642,
	AnimationEvent_set_floatParameter_m722A753CA562F5F228A8DDC719BF8881A245BB0A,
	AnimationEvent_get_intParameter_mE1621E76D06F7ECC1F15E7A4ADE2C3DAF2EAFBC1,
	AnimationEvent_set_intParameter_m31FC71AF73518F56A3ADD0B5BC9C5F44760D0526,
	AnimationEvent_get_objectReferenceParameter_m74F6A7AC3CE4FA8FF01F510506CB97EE261614EB,
	AnimationEvent_set_objectReferenceParameter_m4781A06C0CFBE3DCE9EDC26B06156B5BE9E76F78,
	AnimationEvent_get_functionName_m717A4770DD98BEFF7F64E56C55B0712BD13F479B,
	AnimationEvent_set_functionName_m61B45EF3FB59B11C185BF4529F690AA76447AC24,
	AnimationEvent_get_time_mF3E89318E43FE3063CEAA9176133FFBB783917FF,
	AnimationEvent_set_time_m4D9B70989F587A66C23EBCDB1839B7DFAD3D3C9E,
	AnimationEvent_get_messageOptions_m9E350CE3BBE883207A5B7B658F0339DB99737B97,
	AnimationEvent_set_messageOptions_m269062C59516F2C976A6F811B3C4E9722A5B6F8E,
	AnimationEvent_get_isFiredByLegacy_m00CCCA61535BD4B0620D055FE4E51421F6EEBC0B,
	AnimationEvent_get_isFiredByAnimator_m933B34D40A0B7781D943D77731D93A6D6A334AD3,
	AnimationEvent_get_animationState_m712E372AEAD7678B374A1DBBE9BAD62A36B86E9E,
	AnimationEvent_get_animatorStateInfo_m1567749054B3E202F48CE4EB95D18A61514DCEFF,
	AnimationEvent_get_animatorClipInfo_m0D763FA1A2E3CD81AC08F1F24977859AE25938F3,
	AnimationEvent_GetHash_mD3810CAEC23D174ECEF7D6BA152B80FE73ADD068,
	AnimationClip__ctor_m3F9B3DE671547640479DB976423EEBC689D26F79,
	AnimationClip_Internal_CreateAnimationClip_m1410C35D6386CEA1F068C9314751D0F6A7D34789,
	AnimationClip_SampleAnimation_mD9BD020CC922D1E818D0C2163D543CF7BBAF6488,
	AnimationClip_SampleAnimation_m1707BCA02475EBB318BA0F12C857CF5503C30904,
	AnimationClip_get_length_mAD91A1C134662285F26886489AC2D8E0EC79AF41,
	AnimationClip_get_startTime_m276B72F71A7E8C761AD5232ACE232388879E7B9C,
	AnimationClip_get_stopTime_m3823D1030E16F3ADE9B646CD249F52CEFBE5FE4D,
	AnimationClip_get_frameRate_mBECE2AAD43501FB8CE2E5F4302BD2989066AF242,
	AnimationClip_set_frameRate_m68E3FF5D77738194EFEC9279F1B3FB1FCFEEED74,
	AnimationClip_SetCurve_mEAB88CD0C8F623EA0DEF312CF8D39DE9EB2E1555,
	AnimationClip_EnsureQuaternionContinuity_m9D02889DCA683AA8B285BD6A26A44B8FD615A5EF,
	AnimationClip_ClearCurves_m4544088FCCDA4CA2B5B5D3336546F3893030232C,
	AnimationClip_get_wrapMode_m7D79D3FE7785D25ECCD8C16ADF289654E9BCAC97,
	AnimationClip_set_wrapMode_mC6BA022CE7B92CE00A98FA757576847319788AD6,
	AnimationClip_get_localBounds_m88CCD1318AAD9B01899E41BB16A4F83D15933348,
	AnimationClip_set_localBounds_m84BA2E46FC222551E4280B4C11BDB25B5232DB09,
	AnimationClip_get_legacy_m2ACB9171DA504B26635D0C0CFF64D5F4DEF9C82B,
	AnimationClip_set_legacy_mA24DEDCB86CB9439005B71B6916C09C751731377,
	AnimationClip_get_humanMotion_m34334767A9BD7E78BDCF577F14AFD10B2412FE06,
	AnimationClip_get_empty_mA55084A0DDBE57E75DC412FCEE2285626E76B986,
	AnimationClip_get_hasGenericRootTransform_mED40404C94754A7CE75E96EF8C2CE35CB71B343F,
	AnimationClip_get_hasMotionFloatCurves_m66FC0D4BAF9A7BEC0A19DE07CCAB97217A4D1698,
	AnimationClip_get_hasMotionCurves_m36F62751D417AC698D8D60A5C43E4EAA49B4FDAF,
	AnimationClip_get_hasRootCurves_mD871E5382B94163A7CA3B917BBAC50F0E1405B81,
	AnimationClip_get_hasRootMotion_mF736536B45B62D56EBD402A8A033767A88CFAE3E,
	AnimationClip_AddEvent_m7678C740737DCE2CA8ABC4DDE8508B46FE3C1655,
	AnimationClip_AddEventInternal_m263CAF515A649EF219602FDFF556BAC04B3D54DD,
	AnimationClip_get_events_m700FBB913D1FD2E715139B5C9D9D03B6D8B0078D,
	AnimationClip_set_events_m809F2620D1F000B644AB2A1903BEA342EFC33001,
	AnimationClip_SetEventsInternal_m8CA32C344715FD2B5558547DDC7ABA4604EA6F13,
	AnimationClip_GetEventsInternal_mF2E21B70DC3E96105B7F696767FEA06400EF805F,
	AnimationClip_get_localBounds_Injected_mC0067D51DBD55799EFCD7B1EC041DEBD1B2C7F53,
	AnimationClip_set_localBounds_Injected_mDB992FAF16AA2D4E92AC432F1B98162EB10F1044,
	AnimatorClipInfo_get_clip_m6205DB403EBEAEAC14DB8928FFC7EBC50142E1AC,
	AnimatorClipInfo_get_weight_m1CC29E2C37B30993EFFD12161059E4AD86EE287D,
	AnimatorClipInfo_InstanceIDToAnimationClipPPtr_mF656EAD29AB800127963F8A663F260E89EBF2CEC,
	AnimatorStateInfo_IsName_mB936F493D6BDDB9372C8E9D813CE0416B002C4D0,
	AnimatorStateInfo_get_fullPathHash_m583FA8FAAC28BF65A65166D100949833E515210F,
	AnimatorStateInfo_get_nameHash_m88E91C33AA5602324A7319D7A51F552D00B14D4A,
	AnimatorStateInfo_get_shortNameHash_mEE816B999C282A3BA95AFC64278B994E899B7004,
	AnimatorStateInfo_get_normalizedTime_m087C7E5A72122ADF18EBB4AC8391103B9119CCC6,
	AnimatorStateInfo_get_length_m2FAE317264F7C796427207F8F28E550DB49F9541,
	AnimatorStateInfo_get_speed_m473826E53D827AEE36A7FF0662AC4817C2EF3495,
	AnimatorStateInfo_get_speedMultiplier_mA078AADECC98C266C82E2756A351235DDC63A107,
	AnimatorStateInfo_get_tagHash_m3F4738079576820B7D5942854882B2B468CDD55A,
	AnimatorStateInfo_IsTag_m9A3181AA167702263EB283AF27B21D08EAD895EF,
	AnimatorStateInfo_get_loop_m3DC728FC9AF0D4B27B3C28157395BB2F57CC3DA7,
	AnimatorTransitionInfo_IsName_m6C0C8BBF7E241EFEE2199D5D97DC59958BFBE324,
	AnimatorTransitionInfo_IsUserName_m91FDB1462C56FCDB3F9A209020D2014B06833DBE,
	AnimatorTransitionInfo_get_fullPathHash_m3C358272C30F5AAE76547585AA6C3D866F6F77AE,
	AnimatorTransitionInfo_get_nameHash_m31EC38373C30F6A8BADA9AD27EBAB6BC5B9185CE,
	AnimatorTransitionInfo_get_userNameHash_mBAD5D1898CE34F1E0657D6BBEA18C4A35EC686EE,
	AnimatorTransitionInfo_get_durationUnit_m7E41A2E75B4DBD836E1BC55FA933CF199203B7E1,
	AnimatorTransitionInfo_get_duration_m15DB72ECD67D569CA85DA4CD46E4C92677BFF8B9,
	AnimatorTransitionInfo_get_normalizedTime_m0D107F16FB8351EBB0E8F8A4367A69916E260072,
	AnimatorTransitionInfo_get_anyState_mDF1EC0E1F99B7998D19720BC5AAE4B7A31499273,
	AnimatorTransitionInfo_get_entry_m56D5DF6A01AFBAA8611505369333083DA652DB13,
	AnimatorTransitionInfo_get_exit_m32F0BA8C87C7487F27A3BCE5A3B490DEB67AC80E,
	MatchTargetWeightMask__ctor_m381E3F8A3BA8447D8E9CB084E785AB2CDD38B96B,
	MatchTargetWeightMask_get_positionXYZWeight_mA92CEAD4501F10E0B8F1177C4A1C1B60A1CE644B,
	MatchTargetWeightMask_set_positionXYZWeight_m59F98F90D7089D61AD3B375E788BEE5D78753E0B,
	MatchTargetWeightMask_get_rotationWeight_m5FDC8E88D3A7E6DC0DD51F988F0F172FD8C84C88,
	MatchTargetWeightMask_set_rotationWeight_m6E9398D9F0017122E85576DE6482A17E2C8A15D2,
	Animator_get_isOptimizable_mEFD0F96698EF9ED2D9DB8A4DA795E78EF48088C2,
	Animator_get_isHuman_mE1154471F516DA8BB47B0605410640344028E2A0,
	Animator_get_hasRootMotion_mA39728839F19F4E103CF2599281E6F796D2AB341,
	Animator_get_isRootPositionOrRotationControlledByCurves_mC87A9D341EA194B66C41198B0273589EB6B3605A,
	Animator_get_humanScale_m4E9B17E752307329C2F19BB1CFEDB6180976F7B6,
	Animator_get_isInitialized_m22DA9FAA80F43CBADDD6CDA73BAAE49752E03806,
	Animator_GetFloat_m10B455A15EB343175518CCBEE2818C2497CC678A,
	Animator_GetFloat_mEFA968AD9EEB92E6A03AEEB968600798E61F9B1D,
	Animator_SetFloat_m10C78733FAFC7AFEDBDACC48B7C66D3A35A0A7FE,
	Animator_SetFloat_m018FC1B8BBA989887545ABEF5FB611087F23A4C0,
	Animator_SetFloat_m2CDA219BBAB214F4069C9844780EBCE6CCF579F5,
	Animator_SetFloat_m3FFF291B541476DE7EB1D6432169ECDCBEC7FCD2,
	Animator_GetBool_mBC4D952885FF7504963E5923C29481A891028FD8,
	Animator_GetBool_mFEC9C0565C52965FBD5D6A02084484F00F8ECE2C,
	Animator_SetBool_m6F8D4FAF0770CD4EC1F54406249785DE7391E42B,
	Animator_SetBool_m1DD34A313E6882B6FBF379A53DD8D52E4023F1D8,
	Animator_GetInteger_m8DA635E4373A1E7D649B2BA2DEF382D87EFE603C,
	Animator_GetInteger_mF4E1564D3791C968FA8952954B5AAC4C4E2C8B91,
	Animator_SetInteger_m7B4BB5FD5BD8DE3F713D8E9AD2870C1AAF8E19EF,
	Animator_SetInteger_mE823EC7492A7FB266FA723C168226D17085DF3E8,
	Animator_SetTrigger_mC9CD54D627C8843EF6E159E167449D216EF6EB30,
	Animator_SetTrigger_m2D9CACEFDE11FF9DB99207B5CBD251C1EC047939,
	Animator_ResetTrigger_m8DCA67D5A6B56702E3FAD4E18243E194B71297CC,
	Animator_ResetTrigger_m2DF2C6DE87314918C151616FD5C39EB93EE14011,
	Animator_IsParameterControlledByCurve_mC65B475495D9A4B01BF5BD33E2899E87245375DE,
	Animator_IsParameterControlledByCurve_mE62E4CAE2725D8AD214D59FF459F63157818C7DF,
	Animator_get_deltaPosition_m1759DB2D343FDAFAA7FE85ED66451D5210B0A606,
	Animator_get_deltaRotation_m54839D596F81D477E91E2746E4141AB389238FFD,
	Animator_get_velocity_mBEC30469C1011C8C92E7991D039D06945FF263BA,
	Animator_get_angularVelocity_m69E6F9C74F6E54F43BBB945E0E8F71CDAF4820B1,
	Animator_get_rootPosition_m952907486F631E7E8E0384AE7CE86D92BB3C6E71,
	Animator_set_rootPosition_m29463566F2F73908602CFB2A59B86310A844286F,
	Animator_get_rootRotation_mE913E618AC95BE36524C88A77D087E67579FAF46,
	Animator_set_rootRotation_m916D8E03CAA17B4BD68343BE6CDA3307A0E43B3F,
	Animator_get_applyRootMotion_mC8C4D583F86A9E0B1BF395BBFFAACACD496EDBB8,
	Animator_set_applyRootMotion_mA0953B6AEE43D4AF0837365E7BFF60FCC74B0F98,
	Animator_get_linearVelocityBlending_mD41FD1EE51B7E464CE370EB70F2DAF4546DF9A7F,
	Animator_set_linearVelocityBlending_m1F47CE5FC0467A772EBD2F4610BBD6B45F92FB2D,
	Animator_get_animatePhysics_m4419359F5EB230CF2C6BA7986529E3F08FFC3BC6,
	Animator_set_animatePhysics_m5701A0DE6FE0D29B4746B9A1CDA307FD1E76C819,
	Animator_get_updateMode_m63C8A41D5D0F214FB5CF554B69CFBBEB6EE141DB,
	Animator_set_updateMode_mA21CC888FEEBC5A06099E5D33A6C7ACCC266B056,
	Animator_get_hasTransformHierarchy_m45669F672E3EFC79FCCE1179BE17B9F8095FB946,
	Animator_get_allowConstantClipSamplingOptimization_m21D999899FCFEFB076185D84675EEF7415604080,
	Animator_set_allowConstantClipSamplingOptimization_mD15A77BF2EDA9211776455E41D0F41F2685D7AA5,
	Animator_get_gravityWeight_m5414ADEF41113A6B7C8FAE281578DA1DD607CB00,
	Animator_get_bodyPosition_m0372578D2966019ED1CE5F3AEC1DE6F74DC6EADE,
	Animator_set_bodyPosition_m7CFDA1C974A11D5BFC52181879A25FA8AEAFFC61,
	Animator_get_bodyPositionInternal_m595442E15D03EA53B40C2760C99B4D52C536EF06,
	Animator_set_bodyPositionInternal_m6042D23A6CC550D438B4522C4755BD08692D2F1A,
	Animator_get_bodyRotation_mAC0517419CDE876795D7AD59253E3B4F8628091F,
	Animator_set_bodyRotation_m8763282BEFDD626963D985435E3BC3FE576E27BC,
	Animator_get_bodyRotationInternal_m62411259235851629AB914E29B04029A04D8F165,
	Animator_set_bodyRotationInternal_mA381F771D03290AF383D3203071D9B04746BD2E6,
	Animator_GetIKPosition_m626E887B0E039C8F79D0A942D469880B05A58962,
	Animator_GetGoalPosition_mE65C3AB94643D8036BF6C8014408CAB0F9BB5A98,
	Animator_SetIKPosition_mB7BE88C93990186D94AF75439E2F216D6ECBCDEE,
	Animator_SetGoalPosition_m0811DE827A66638ECA79A2BAA6A59D9D21D7D5A2,
	Animator_GetIKRotation_mF2969E5FBD59C125EF95AB689A9B3AAB434F79FC,
	Animator_GetGoalRotation_mB7B67DE4EBA3C26D713754D1D76D4F529E783DB2,
	Animator_SetIKRotation_m328A64AD20922F0F2A1D0CD5DBB9F01FE7675DF6,
	Animator_SetGoalRotation_m1BFE2425DE75954B1233ABD96D6AB3682D42E5BA,
	Animator_GetIKPositionWeight_mA79A863ED8F5E8095FCC9545CCB55483328B3D99,
	Animator_GetGoalWeightPosition_m0E626A12D7B157A0B17D231D5E6175D062937E6A,
	Animator_SetIKPositionWeight_m8C707F505FFE2A6F36BE81ED12786B941D3B990C,
	Animator_SetGoalWeightPosition_mBCC0ED516684541F6AD9B784ECCCA2D7C14DC75E,
	Animator_GetIKRotationWeight_mC376AF1B02A0C1D15072AFE215B9D371B09D3059,
	Animator_GetGoalWeightRotation_m10C6574D1AF23568F737D0347F35A2B5BE670725,
	Animator_SetIKRotationWeight_m5F0F5BD5A9A85912EA1CDF32917FE483E849978D,
	Animator_SetGoalWeightRotation_mA8A5BE4C22583CFB7C66EF59A1997E241A3001EF,
	Animator_GetIKHintPosition_m6FDF4F6FBFD9936686F514907126422A555BE020,
	Animator_GetHintPosition_mD97BF99EB5303EAD560BFE087E07AC44E1FFF42E,
	Animator_SetIKHintPosition_m834ACA024C8824FD458A8F3A55AE40819A660A2F,
	Animator_SetHintPosition_m446EFD046A753C0604A5487D9606F89A362653E2,
	Animator_GetIKHintPositionWeight_m2DE07F51712E11FC8D929A149D70FE40498CD75D,
	Animator_GetHintWeightPosition_mE89238F7EB821743F245E2F9EA4BC8D3B3629830,
	Animator_SetIKHintPositionWeight_m3F123E4F07C30ED041B3BB0616CFD9D9460910E9,
	Animator_SetHintWeightPosition_m5BC635B929CACB43C9D5DE73AF292A423B8BF0ED,
	Animator_SetLookAtPosition_m39A2C326BDE156360972C5EEDA1F9ACEBE34A8A6,
	Animator_SetLookAtPositionInternal_m6A20F2667C36D8363BA0F38A961BAB941E2DDC1B,
	Animator_SetLookAtWeight_mDCC8C8792E2E23C133AF3D91A96C49BEBC828F79,
	Animator_SetLookAtWeight_m0C4D297E270F7E852481EFBED321020E96EBD54D,
	Animator_SetLookAtWeight_mD0D92A24E3789891D709E0DCD84524FAE614F870,
	Animator_SetLookAtWeight_m31AE829F22900AD67FA4840D488E1FA9E0880FB2,
	Animator_SetLookAtWeight_m536B18C7EC83703CF0320924D9FE13CBD93CC752,
	Animator_SetLookAtWeightInternal_m41D66B6DA78B1ACB7EE3E3B51A29904DEB05778F,
	Animator_SetBoneLocalRotation_m8294EB788C2BB23708025F5E0246061827321E6D,
	Animator_SetBoneLocalRotationInternal_m29A9991C7C688D3E2B6747C769358F4C935095C7,
	Animator_GetBehaviour_mC89D3A58B66C9579FF8980C1F06F518A8C2E489D,
	NULL,
	NULL,
	NULL,
	Animator_InternalGetBehaviours_m53FE16BAE89AAB75F82132643848FCBFDD441BFE,
	Animator_GetBehaviours_m0CB79D09176BD9416D24A19702EDF37575BD9048,
	Animator_InternalGetBehavioursByKey_m6B3048864D1DB2483F5298842E9384599FBB8E9F,
	Animator_get_stabilizeFeet_mB1C363699D45D67276ACA07C79FB257E1BB4F5DE,
	Animator_set_stabilizeFeet_m4877796D25F5D046BA6FEF8CFDFEB470620801A0,
	Animator_get_layerCount_m75C20E7284800DFA0E4AD7EC23910BDB6D62BDA8,
	Animator_GetLayerName_m7458E91DE0B0769E79038DFEF32B750BE6801BA7,
	Animator_GetLayerIndex_mE57FA9E3A7B3308B07B79307B5BCE0688AF4B38B,
	Animator_GetLayerWeight_m24EB83CB3CFC724CB77461A26BB794E7DEE15BCA,
	Animator_SetLayerWeight_m06ADC732F76F22B4B1424F25525E7CBB80E6230F,
	Animator_GetAnimatorStateInfo_mC6C046A539DE6E8D481E830D3DA1FBF96DFC367D,
	Animator_GetCurrentAnimatorStateInfo_mD5B526FA605F6CFBC31B7DE36740F6AD1E534CAD,
	Animator_GetNextAnimatorStateInfo_m4661892C3F99329907EE3EE1C1FCB7974CDA2433,
	Animator_GetAnimatorTransitionInfo_m933890D15E832719A2DBBAFCA587FEB4C7F1C6E9,
	Animator_GetAnimatorTransitionInfo_mAB532C0834DEF3685C6E16C82B7A93B8875FC542,
	Animator_GetAnimatorClipInfoCount_m83C02F470FEC1DAF548AB88C578B64B6B3BB8E9B,
	Animator_GetCurrentAnimatorClipInfoCount_mB506E49EC0B2DD764D78A21D6AE55B4E9AAB9AE4,
	Animator_GetNextAnimatorClipInfoCount_m079742F61B3A1616C4419C82F91B84BD9D5613CC,
	Animator_GetCurrentAnimatorClipInfo_m963412D4118C301408B2EAFEF1E1CB5E971D5D92,
	Animator_GetNextAnimatorClipInfo_mF7B457B637128DFED6BAC15CBB45A9FE75205CFE,
	Animator_GetCurrentAnimatorClipInfo_m7C061EE420A67B7D751A2F34D38062FDFF064ED3,
	Animator_GetAnimatorClipInfoInternal_m98028420AD065F1B6F3DC2EB4C6C2A42B8567600,
	Animator_GetNextAnimatorClipInfo_m376D90AA469A7F7268B8D0E5B6930CE7095185D3,
	Animator_IsInTransition_mC2BD2CC7B7A11BAAA5396F1A2DAFD98D00AA2830,
	Animator_get_parameters_m3DE35688D8EBD3D1526346C7B71E468436A4463B,
	Animator_get_parameterCount_m56BDA9B508904594E1B2317A451AC3E40A12943C,
	Animator_GetParameterInternal_m1DEF19C5B91BE9602E482FEFEA927E9218C081A8,
	Animator_GetParameter_m0F1E53A9845596EA75366518BEB15733FD4E124F,
	Animator_get_feetPivotActive_m6905716868666F1A61574A07DB455482CBC7B6CF,
	Animator_set_feetPivotActive_m0683AF2FD4B62EB9330B7BC347BE189A90809C19,
	Animator_get_pivotWeight_mFFD3DF6F4506AA829E3E6D726F21EB62EC6A4652,
	Animator_get_pivotPosition_m2A6E669BC43633E12BEDF96ED73AB86EAACBA11B,
	Animator_MatchTarget_mD1C55BE6B64912264BD1280498BFF938B8501596,
	Animator_MatchTarget_mBDD980442EC44131EFEED1EDF4FEFEE11B909576,
	Animator_MatchTarget_m717CF5422596B401395BE3EDC510FFD34F85D337,
	Animator_MatchTarget_mEFA2D3248BA083F5FA823D83B38E2306336293D3,
	Animator_InterruptMatchTarget_mB031E84E81E760C7A5862324B868E76B030F8EF2,
	Animator_InterruptMatchTarget_m3BD713C829A921D66628C6B18F00E6F75EE327A7,
	Animator_get_isMatchingTarget_m56CCA705025C2057D60F5BB9724F864C8B2223B0,
	Animator_get_speed_m41AFD6B0AB3FF4FFF8855CCAF684813BA1148CD2,
	Animator_set_speed_m933F4D6770122BC9D8A7FF82DE1CD33D514379FC,
	Animator_ForceStateNormalizedTime_mFCEEEFD9CD18C26E7128691FCD758690E87AC305,
	Animator_CrossFadeInFixedTime_mF3F5670768653192DE1784DDAD591E73030F80AA,
	Animator_CrossFadeInFixedTime_m7F12206926BAF74D1385F1F8BAD36CBD3FDA4A51,
	Animator_CrossFadeInFixedTime_mA1271920E97C315726655B0A5C31DAE69FAC6FC2,
	Animator_CrossFadeInFixedTime_mD3A92E42D472130EE6948AFD9C2374334B6899B0,
	Animator_CrossFadeInFixedTime_mC0A7F0188309753098EFF103FFA511A4377EEC55,
	Animator_CrossFadeInFixedTime_m0723F8EDB1F6B0CDDF1E61914BFFBF3FD14ADD8B,
	Animator_CrossFadeInFixedTime_m31C6FDFE566373C35F8931DE921CAAC4131F1D25,
	Animator_CrossFadeInFixedTime_m93D7442691AF96BD416878312D71B8EBD248340D,
	Animator_WriteDefaultValues_m28A53BD6205B7543B56F63E841AA4F8230EDD1FB,
	Animator_CrossFade_m2D91D6C458B15B186365EED0C13A178342789084,
	Animator_CrossFade_m2710B834A3D8C5C222B2CC6BF6D307B7A138055C,
	Animator_CrossFade_m9896DFE98F7DC35199C7FDEB22E6645B6F53E998,
	Animator_CrossFade_mA6769671098D9B30D68A27DF00D355CC5E28D56C,
	Animator_CrossFade_m34D1F0B5D8E14FC3D685D91D412ACA3F69F16186,
	Animator_CrossFade_mAE4ED75E850B05A529EE726E314EF15A988F37DF,
	Animator_CrossFade_m140269A1067EC377112460CAE86409FD5A61104C,
	Animator_CrossFade_m877FD25F61DCAF76F4B321153E4B37B220091C73,
	Animator_PlayInFixedTime_mAADE5EC387F7FF8FE6846E31B1EB5A6F28CC1AAF,
	Animator_PlayInFixedTime_m51356C96303FBDAE14D5D83453424F549152E2FC,
	Animator_PlayInFixedTime_m8E57011DAD1D97B8071B18115E09FB2151154E21,
	Animator_PlayInFixedTime_mED55F3D6ADF43C009FBD4C447126381D954C027D,
	Animator_PlayInFixedTime_m2CA86C7A32EC02BCAD4EFD0B9CD53272BF06E025,
	Animator_PlayInFixedTime_mF3630168236FF27D0AE2212E9CFCFC3F41542C10,
	Animator_Play_m9B1CB1A22951C0B0758AA032F2349B6DD20E96C2,
	Animator_Play_m0F6A9F84B2E256E644D56C34A7A9BD622CB00FF9,
	Animator_Play_m5565F093F0752D4DD9B451686F71C7A8F23F6744,
	Animator_Play_m3011727F1A3F331EE7F5D7CF9B98EFEADE547BBD,
	Animator_Play_mA4CA3959A5DBDA56A1B20D4B0E727FE592AAAABA,
	Animator_Play_mBA96F0D84A982A338EC976A5EAFE886956AEDDCE,
	Animator_SetTarget_mCE0C0F703C38FF190E4F093B394C1D2AFA80A0F5,
	Animator_get_targetPosition_m6A682300C052714BB55F68E192B4AD778179B43D,
	Animator_get_targetRotation_m313EC3BE0F811C7FB61C475298BABFFAA11DA1DC,
	Animator_IsControlled_m1B8BC43EC53313ECC236FF01E01B86AE634ABCBF,
	Animator_IsBoneTransform_m8D7D99FD916A153C9560961FC918B0C87846E983,
	Animator_get_avatarRoot_m6942EF35F6F2ACDF787772D405E17C3131A5CA95,
	Animator_GetBoneTransform_m02042CB47C468D576C6EE436F4AA71D8C47AAF56,
	Animator_GetBoneTransformInternal_m875DC36979BC9FB162D83E799421243AD3842857,
	Animator_get_cullingMode_m5C9C70B94075022FA6346E17CF1E9C0DEDB21605,
	Animator_set_cullingMode_m7520115B5460495336C87393B58014F412B24209,
	Animator_StartPlayback_m981AE4C0AAEADFE8484C9C001FDA17D5D255D500,
	Animator_StopPlayback_mCC18E6452A6FC5F7BA622F2A7619848CF875B8D0,
	Animator_get_playbackTime_m11289709E35A3FF5D524FC92AF4CEA00B107BD3E,
	Animator_set_playbackTime_m223512B44026FAFDA2E25A321B6B0B6F57DB56FA,
	Animator_StartRecording_mCCD8C24752A76B5487592BE766D57E6874CCC40E,
	Animator_StopRecording_mF8048A93508F6159B2377D61AF3589340A6B5256,
	Animator_get_recorderStartTime_m9CB9520E347B1520B05052E31C4D8A701462E241,
	Animator_set_recorderStartTime_m968547A0E4BC7849B3D7EAB1389AA9A05EC80544,
	Animator_GetRecorderStartTime_mC9E51A2F33D7297124F3F93322E28FBFFCB392C4,
	Animator_get_recorderStopTime_m675F4F5DC1AEDC41C456AAEE5F96F52AEA1E59A9,
	Animator_set_recorderStopTime_m24BC4494E05B7682F3A23C5A0B0607F4D3385BE5,
	Animator_GetRecorderStopTime_m928EDD878985FD098B38DBC89AD7FC935FF95681,
	Animator_get_recorderMode_mB34DB4C5368F0D3BF86210F41900DF17A43B2DA0,
	Animator_get_runtimeAnimatorController_mE10F46F893A630D1AE846EF66DC2769E3ECE5AB8,
	Animator_set_runtimeAnimatorController_m505ACBA1D2E59814239EF3760A9F537D03301311,
	Animator_get_hasBoundPlayables_mA5A6132C03593851FE80D8E7490191E051E5A1C9,
	Animator_ClearInternalControllerPlayable_mBAE8D218945AF13E7ED60D7958F3155C341C8686,
	Animator_HasState_m9E3BEAD260AAA9FD571CB0AFEDC20F278859B833,
	Animator_StringToHash_mD67B872C411BE37641F49D7AA1DBD45B67F63E3A,
	Animator_get_avatar_m01E445FC754BC6F4A39639EAF68D3954580EAA67,
	Animator_set_avatar_mF24D7962A24C1EBF3B949E712DDA24DB11B5DAC5,
	Animator_GetStats_m264013CFF4D9B247F2C683188CE3E0C8571197EC,
	Animator_get_playableGraph_m118650892C13ED75C535C2A32CE09641C7E3BAD7,
	Animator_GetCurrentGraph_mDDDDABBEA3E4BBD025401F92FFFE5B71DAC9486F,
	Animator_CheckIfInIKPass_mE7815990AFAD02738D0D5B4DA8DDB3E649D30CCF,
	Animator_IsInIKPass_m35BF844B06D845BD0C6778F3B6E87B9C0B26BA5F,
	Animator_SetFloatString_m1AA50083AAAE1DFFA7FB64502EA106795D3756B4,
	Animator_SetFloatID_m348942A23D4C734DDF4D04C7609E5CD9EBAB66AA,
	Animator_GetFloatString_mB1E5C90624A736A6D98D30FF5318BC1D0C494A38,
	Animator_GetFloatID_m0300DB1901B65F007EA9E0E98ACB91E105FDD366,
	Animator_SetBoolString_m08A0BC6717BF5FD3E43D3EFB2509E359C1196C00,
	Animator_SetBoolID_mCDA31E47A1AA78E830D0B76394BE2FA7296E03BE,
	Animator_GetBoolString_m8698CB03DDD5DF7B991EC772BC424F2A649EB950,
	Animator_GetBoolID_mD88CBA007AB7C968BF526B518E90F90E0935A9DD,
	Animator_SetIntegerString_m3080217346B85D69FA5A99B2ABC64BF5E4580455,
	Animator_SetIntegerID_m08431A06E5905C62BA397B9BAB30F87E3C30569F,
	Animator_GetIntegerString_m591359704EA3449A83C698CD72EBB7A6FC5F6D9D,
	Animator_GetIntegerID_mCAC9F61639DAF52DD2DE8891DB8A2F7FE5C3DF4A,
	Animator_SetTriggerString_m177C75DFBE070DE66FC08A3232444CCEA409C25E,
	Animator_SetTriggerID_mCC0A74BF79A56BC1EA634641C64B8E527B0B49E7,
	Animator_ResetTriggerString_m78259348CED35F156148A64B95EBD73CE3951868,
	Animator_ResetTriggerID_mD3E0C24AC862F049622D09933E7A3A2CDE256C13,
	Animator_IsParameterControlledByCurveString_m8BDBA93917C92923DB297EBE8F141FA6C695AF61,
	Animator_IsParameterControlledByCurveID_mA4A7685A97E37887738AA0959AC83557048C87A3,
	Animator_SetFloatStringDamp_m81B394E6887978F4A8A92ACF1C7138B23BFC9214,
	Animator_SetFloatIDDamp_m013A6F5FD90DFA125C152BF42F299157AEE96330,
	Animator_get_layersAffectMassCenter_mB3DB124E8A0F57BE687F44A95986F5F0E3B999EB,
	Animator_set_layersAffectMassCenter_mED2748A798EB7DAFC9977710463E06931BA3E3AE,
	Animator_get_leftFeetBottomHeight_m5ECBAF5B6CCC20691ADA416B95AAE9192DF09C76,
	Animator_get_rightFeetBottomHeight_m0C68C9515FF2AD560D36357A134246DADB8A3544,
	Animator_get_supportsOnAnimatorMove_m1E4A235DE9899F2473AAB70B86BC4A4575F90521,
	Animator_OnUpdateModeChanged_mCE2A9940D4A0772544234F12D1E49E89A2A6B1BF,
	Animator_OnCullingModeChanged_mDF02FBEF0EAB7BD09F5A6446E0CFA9DD4CC1285A,
	Animator_WriteDefaultPose_mF05891951318F68AED1FCDF5134B3F9B0F62C101,
	Animator_Update_mBF5E8B2869FD05AF4A5963C39203D85BD62E8E65,
	Animator_Rebind_m853F9E50ACB0A29D4F144FFD851E92F346195F9F,
	Animator_Rebind_mA8163C9B7150958C0FB3F071B7ED41850BE3A130,
	Animator_ApplyBuiltinRootMotion_m2ED8DAFF78DC1A0CA62CF3785692074DBC51808C,
	Animator_EvaluateController_mA07519A24495A1D1B33D163005E18FC52B5954B0,
	Animator_EvaluateController_m5A83794E6067230ED84A707365FB70CA9EE64128,
	Animator_GetCurrentStateName_m9B1BE9CADE479A6BED0CD48387C73ED963CA9E6F,
	Animator_GetNextStateName_mCF8EED59CECEE48A04A27EA422EA29E5C4EB8A3E,
	Animator_GetAnimatorStateName_mEA694401BD4FC9185FFCFDFFCB7C1DCD2B1B8D06,
	Animator_ResolveHash_mB349EC9252F07ECD3657818C08F66FACBAA53D43,
	Animator_get_logWarnings_m39949DA4A177E2D107023FB0BD89DC3D6F303652,
	Animator_set_logWarnings_m625FCBAF506C623F5457D22C5A704AC6727B446F,
	Animator_get_fireEvents_mE0311DD2BAC5EA407BD7FC02B8EC52D4E2F27668,
	Animator_set_fireEvents_mBB2DAC3B84656758453B0BABB12DF7949EDDF43C,
	Animator_get_keepAnimatorControllerStateOnDisable_mEBEF419822017BE487B8BD5A9118C175F50A97CE,
	Animator_set_keepAnimatorControllerStateOnDisable_m0F15299D72F566184637745C2DCCCA712968FDC3,
	Animator_get_keepAnimatorStateOnDisable_m7FAE9E72FA31D90E7D55664BD627C2903893A76B,
	Animator_set_keepAnimatorStateOnDisable_mE439995234FEE85A9DE7DB5238D301128CABB769,
	Animator_get_writeDefaultValuesOnDisable_m25C02067B4A8BCD406B8112BF07C8DAD70D7A89F,
	Animator_set_writeDefaultValuesOnDisable_mE0E16D2592C27771C3BB66B017BF8EBB4F200A6D,
	Animator_GetVector_m8BF07F4BB4DBC9031166EE48CB5E61DD7D1F90C6,
	Animator_GetVector_mBE09ACBB9CD91B2343B91CF4C547028CD1920BC3,
	Animator_SetVector_mE7EA3828CF3F35015BDC012736787472773D8D12,
	Animator_SetVector_m89C00646839A497355E264DB9E930DCC162DC3FE,
	Animator_GetQuaternion_mEF3B6820436B19567A4635C3C7D0E4DFF4A5EF66,
	Animator_GetQuaternion_m2F43CF4CDC5D648241847A072E5C1079681B1AA3,
	Animator_SetQuaternion_mEA2933BB9427F2AF4C0F709CC4AE67C653D6A622,
	Animator_SetQuaternion_mBE23DCD9EA5BFFD39A9267FD0559CC7FC54C5A95,
	Animator__ctor_m53346EED5CF6845390B4CB8F53C9CBE9C65D5CEA,
	Animator_get_deltaPosition_Injected_mF37497B00055319EE54006C19AAEE221A99791F4,
	Animator_get_deltaRotation_Injected_mFEC802815227FE4B11491F80B281425A10E19ABD,
	Animator_get_velocity_Injected_mB47827ADE9F826A2ECA302EB7751371A9F18A57E,
	Animator_get_angularVelocity_Injected_m316C33CD3C5CF4F7AA8E53FEB0741B7E1C3D4537,
	Animator_get_rootPosition_Injected_m7F7BA9F18B8866C40649D61A36ADFF42EDF5B88D,
	Animator_set_rootPosition_Injected_m9F65EF589C17FECC4350D1D193C20B7741BD7312,
	Animator_get_rootRotation_Injected_m670A2B5E5EE664CA5ED9D711733C102E0BD72AFB,
	Animator_set_rootRotation_Injected_m4DF71DAF4F3EE3B4D460FAB3360E00C74027F11A,
	Animator_get_bodyPositionInternal_Injected_m44C1D9729A292329E4B4A66B1DE583368ED9B2B3,
	Animator_set_bodyPositionInternal_Injected_m434A0C848EF277237CBAA68C4A94A1DD4A05DC75,
	Animator_get_bodyRotationInternal_Injected_m60262D5C0C987E21A358FF7F67887C3B23FEDD5E,
	Animator_set_bodyRotationInternal_Injected_m0BDC67195DA58582AEFC3567906106BD96C1D173,
	Animator_GetGoalPosition_Injected_m7EF59EA5A53DEE902DA1AAACD32378A32B4D67BE,
	Animator_SetGoalPosition_Injected_mE03B76DB578A3B15ED91437071BEF70F61985707,
	Animator_GetGoalRotation_Injected_m76092248C853479A1E6178DA4AF19F69FF8C8F75,
	Animator_SetGoalRotation_Injected_m93587602A0C43AC9B184D30B03F751C02E6BF045,
	Animator_GetHintPosition_Injected_m6BE0C46FE3B057D08BA4B111DB6340F958B33FD1,
	Animator_SetHintPosition_Injected_mF7843586B3EA22A800A628735583620FFDF2ADC5,
	Animator_SetLookAtPositionInternal_Injected_mE50AFECADA7DC76D0E73C6B7131EBBD1CED5D59D,
	Animator_SetBoneLocalRotationInternal_Injected_m4A2C1DAFEE4883C6A39D33CF56E8DC015D6A0EC5,
	Animator_get_pivotPosition_Injected_m381B79F41C78BD14DC47FA49C3B20287E50A8EC6,
	Animator_MatchTarget_Injected_m1B2CB01E3B71964EA09D70C42583F613641925C7,
	Animator_get_targetPosition_Injected_m7114A2CB2FE3A982E0967119BF3280258EEA4166,
	Animator_get_targetRotation_Injected_m64CE93A396B9F5BF5F77EEE843484C38C5975F1C,
	AnimatorControllerParameter_get_name_mEB3938ADDF296A0FB37283C987AEE6EC0F4F629E,
	AnimatorControllerParameter_get_nameHash_m92DA605E70604B1BBFD5EA5AE0CD0311F21400EE,
	AnimatorControllerParameter_get_type_mACEB110E346B27168F177E7A909CFB8586A2B966,
	AnimatorControllerParameter_set_type_m9B4AD6257187A0F1F09951E5719B766817BFA0EE,
	AnimatorControllerParameter_get_defaultFloat_m8BA60834F990D409938D8FBD156E7E0769720941,
	AnimatorControllerParameter_set_defaultFloat_m986E7FD2CDCE8B8DBD222EF81FB311E247832427,
	AnimatorControllerParameter_get_defaultInt_m2DBD6479C79893DD15609A11F219B84EC499F0F9,
	AnimatorControllerParameter_set_defaultInt_m7EF5C31A61875CC22E69138B596A76E16E6E9D7B,
	AnimatorControllerParameter_get_defaultBool_mA51A41E298A2FE0A5E986E913EB0ABA5B3E1DBAA,
	AnimatorControllerParameter_set_defaultBool_mD77656D0C0CC84DABFE96B016DF279692839CAB3,
	AnimatorControllerParameter_Equals_m9FDC3900B8DB91E9F99295CADB9574F7E1C55C71,
	AnimatorControllerParameter_GetHashCode_m145EFDAFC4D2C410BB84F8290883977340E25312,
	AnimatorControllerParameter__ctor_m91C7057D6AC6D38ACA5EC852D5FDA829F83C8474,
	AnimationClipPair__ctor_mD70020644551CCC04857BA2D4F7F63AD3E2159ED,
	AnimatorOverrideController__ctor_mB3C6317471A40FC19527AA7731E775AFC665CE15,
	AnimatorOverrideController__ctor_mE9C4FCBE3FCAF3CE2601CF50D3B19CC683BC47F8,
	AnimatorOverrideController_Internal_Create_mB5271F04B0FB0E2BADDF1AD4ACDC983E80D8C2C3,
	AnimatorOverrideController_get_runtimeAnimatorController_mA611B48488C4A645B1FD68855A84ECC0406D2DD7,
	AnimatorOverrideController_set_runtimeAnimatorController_mDE783426F6A6283710A7D7FF96ADACB524D9BF6D,
	AnimatorOverrideController_get_Item_mF0A0D7B4CB2A8E1AC31D2054827D3FF8D91B806C,
	AnimatorOverrideController_set_Item_mE99B279ADC548ADA701C80E02CE97E38E4F453F1,
	AnimatorOverrideController_Internal_GetClipByName_m31FBDF024F4A5A99130A62518DEA736CC5177FBE,
	AnimatorOverrideController_Internal_SetClipByName_m52CA560F49002766E9B61980D07312340B195B88,
	AnimatorOverrideController_get_Item_m49AB9172BFD735042B422E9347B44624DB9D11DC,
	AnimatorOverrideController_set_Item_m02FC2BE81A2A4120D202C25CB040DA19AE9BB48A,
	AnimatorOverrideController_GetClip_m55AFB27CC2D666A280D2AE7E78D82F7AC7FE8424,
	AnimatorOverrideController_SetClip_m5978905B5D13EE9626CE77000F7C620C26EDF025,
	AnimatorOverrideController_SendNotification_m370030BDB9A21AF982EBF3B68012224D02D4397A,
	AnimatorOverrideController_GetOriginalClip_m10DEA1312336A51EBC45B89E0D947836CAD4EAD5,
	AnimatorOverrideController_GetOverrideClip_m8F7F5831ED4E5D96FC2B2EF8FD427F14BAFA2A13,
	AnimatorOverrideController_get_overridesCount_m4A241C738A0F2F03B7578E32F2C3342F536DE9DE,
	AnimatorOverrideController_GetOverrides_m4C9F72BECAE151033F913C00A016E7A4A8D4E860,
	AnimatorOverrideController_ApplyOverrides_m4882D28731BA7F28095DC1994375E38255F2DA90,
	AnimatorOverrideController_get_clips_mD6E8C35C32B3418CCC8C0970A76E36E18A32E3B3,
	AnimatorOverrideController_set_clips_m5EA55A1B147ED78ED4FCB0FF033013CAF6E960A9,
	AnimatorOverrideController_PerformOverrideClipListCleanup_mE5578B170C6E70763B20A4DF6CA6DDFF83B31E41,
	AnimatorOverrideController_OnInvalidateOverrideController_mA6B0AA977505FDEFDD6BCA2E941FD3A18AE1AD23,
	OnOverrideControllerDirtyCallback__ctor_mA49B11AF24CB49A9B764058DB73CE221AE54E106,
	OnOverrideControllerDirtyCallback_Invoke_m538DCB0FFFE75495DC3977DBBF55A07C570F8B5A,
	OnOverrideControllerDirtyCallback_BeginInvoke_mD38CF91DC1EACA4C3F7BF666123D57D645C83E6E,
	OnOverrideControllerDirtyCallback_EndInvoke_mF71C144CDEAE9DE20B6BD0E03DE7E41924E8FAEE,
	AnimatorUtility_OptimizeTransformHierarchy_m48241549DC2E06049611C0F5AE0BC88016BC96A8,
	AnimatorUtility_DeoptimizeTransformHierarchy_m2C8B7C431E62532F9301995AA7D9132ED35065CA,
	AnimatorUtility__ctor_m153AF5F6DB3F394B924AD244F145D30164F1C562,
	Avatar__ctor_mA58012D9A6FD2A7BB88D05E58703B5619536E118,
	Avatar_get_isValid_m89626D009C80028727A9C6F8F8F477C23934B94C,
	Avatar_get_isHuman_m1CDE3C2BCB2A683AB72088B26C9824E0FCF00FBE,
	Avatar_get_humanDescription_m8E211B52B89B57AF6793D781C69E46F6E1217E1E,
	Avatar_SetMuscleMinMax_m608B65231DBDC2DAE4F8F9D3D93FA1A35CA82B68,
	Avatar_SetParameter_m4A1D5A8FB0D5F31636D77C9565E0FD057CFCCF6B,
	Avatar_GetAxisLength_mE8C0741203F52E4712EA9DB333638D6C42C63E9E,
	Avatar_GetPreRotation_m774928C7DB41CA00F5AF60B33E05736FFA435080,
	Avatar_GetPostRotation_mEEBB44D6301ECD7E1AE67DD0FF9C715C3D9AF8CE,
	Avatar_GetZYPostQ_mAEDA3440F5A9EE2E3016357801005F7AB401CB34,
	Avatar_GetZYRoll_m4557457639DE594E2FE879ED1BAAEB97637BA68F,
	Avatar_GetLimitSign_m08970E1688571ABCAE484771DA697023A8DA6EFE,
	Avatar_Internal_GetAxisLength_mE9BD06F25CA27B0D026E640CB94C53D7039575A2,
	Avatar_Internal_GetPreRotation_m43FCB48A6F86DA5AC7FADD7B3FFE3ECA49503648,
	Avatar_Internal_GetPostRotation_m8BEBD14E505A8FC73DCD3EF1CA99F32666FBE7A2,
	Avatar_Internal_GetZYPostQ_mE09886C9F4882D6474F76EC32C6711C4EBAF1F19,
	Avatar_Internal_GetZYRoll_mB5FB51A11D1DAD6EB587731C57D5AC43F9FAEFC6,
	Avatar_Internal_GetLimitSign_m19D7625470364E767DB6F9B575ACBA08FE6BB6A5,
	Avatar_get_humanDescription_Injected_mB2DC483AC2212729E7C92641B6FFDDB4056BF3D0,
	Avatar_Internal_GetPreRotation_Injected_mE47911DC3BC9882688A130363B8DA9130CEF74E9,
	Avatar_Internal_GetPostRotation_Injected_m82DFC2810E003BD5422776882E0EE67BDAFA047C,
	Avatar_Internal_GetZYPostQ_Injected_mC76060C9BE7A4A6B03FE4E272DDEBA1422445467,
	Avatar_Internal_GetZYRoll_Injected_m7902022BBB7439F281875F47068C37AD57A0AD59,
	Avatar_Internal_GetLimitSign_Injected_m9D9713CD9F6973FAE4DF20AE86386B216EDBADB9,
	SkeletonBone_get_transformModified_mF6EC8E089FEC8D75FB511B8F7E210B980169C05C,
	SkeletonBone_set_transformModified_m4CA61CA5AE981BB303BBF41240A5AE6CA251FCA4,
	HumanLimit_get_useDefaultValues_mA6C116B2DC3D800FDACE2907D52B85E632914677,
	HumanLimit_set_useDefaultValues_m3BD1D01F9652270133D307C7709FDD554621ADE8,
	HumanLimit_get_min_m12EAAB4E0EBBBBD221BDB213130DE9643906AB9D,
	HumanLimit_set_min_mE1EFA9D3BBB3047BF25554696FEEFF1F218A7227,
	HumanLimit_get_max_m4E5E907AE7FFFCAC65026ECA444507B0B608F02A,
	HumanLimit_set_max_m68A852091164B5EA6CD138615DEC75EC9917DA78,
	HumanLimit_get_center_m6F488F439245F5E54D0DFBE04CD63816BAFEFF6B,
	HumanLimit_set_center_mC1C73D9F6B3EFADCF99E6906A0D464146F2FCDF8,
	HumanLimit_get_axisLength_m9691117C17DFCC40ECB9C1A459CE998831678947,
	HumanLimit_set_axisLength_m7DACA3E1AA03B9733E0C1D34051859A45D5B8FB3,
	HumanBone_get_boneName_m09C4D3365F4D1E69CD0907DEC3A2298A2CF6E18B,
	HumanBone_set_boneName_m22857CD9738A623436C8F7A31D51D1EBA4BD8F58,
	HumanBone_get_humanName_mC5FF6D0EDE66B773EF7E6DD7722E20C07EBCDCF6,
	HumanBone_set_humanName_m460C6620AEE45FC9601B8D05448DD6C397B12D4B,
	HumanDescription_get_upperArmTwist_m92E2B3BCA433012179892DE9493CBC5F8ADB8D52,
	HumanDescription_set_upperArmTwist_m4056390E30A30DCEB48D55D4E536F146E431E100,
	HumanDescription_get_lowerArmTwist_m8F57099DCEAC2B1D0D03F5443F500953E1E086E6,
	HumanDescription_set_lowerArmTwist_mD24F728854AC5AD545E09D6E74CDD0B7AD6DF139,
	HumanDescription_get_upperLegTwist_mB9429634218671A47EAB4E73C4ABCB4EDC1FDCF1,
	HumanDescription_set_upperLegTwist_m0552329018CAC469A2443BFBC89B83DFB5288782,
	HumanDescription_get_lowerLegTwist_m8AB325E825835D0A05A20F447191CF2FBA604B35,
	HumanDescription_set_lowerLegTwist_m477F6F9AEC4E7E3F8D6FA72B05BCBF05C02FFB19,
	HumanDescription_get_armStretch_m9C47D234F9BD5C1807E40DF748AE51CC4433C11B,
	HumanDescription_set_armStretch_m52A43854E332057B62C07306CB1663CEFDB01C71,
	HumanDescription_get_legStretch_m568292F633E3849E7C8DAB4D738C8E6F52AB662F,
	HumanDescription_set_legStretch_m40D8FE0E122F5EF4143253814D732AF8413578DC,
	HumanDescription_get_feetSpacing_mF8283F0B14F35F3FD82BA3D6FF6D82609B924643,
	HumanDescription_set_feetSpacing_mC0F12305CA30ADA53D08E987E52DE1611E9511D3,
	HumanDescription_get_hasTranslationDoF_m735B027773FA7BA045C7B1D5A129B504F7B97896,
	HumanDescription_set_hasTranslationDoF_mE5E5B11F3DC450708723875A5B1D7CFC340FD20E,
	AvatarBuilder_BuildHumanAvatar_m4BA84E1731C8C60C700A02764C183D5FA980A411,
	AvatarBuilder_BuildHumanAvatarInternal_m80FBC857C0DF3E09682437DDD5CBE240D9D7B90D,
	AvatarBuilder_BuildGenericAvatar_mB4A1E1EE4D66D2FBBF31EAC8F05E061EE30EEF38,
	AvatarBuilder__ctor_m14C2F971A77AC4DB69CE63D494D496B978FDB6ED,
	AvatarBuilder_BuildHumanAvatarInternal_Injected_m1D682F8482BFE1BD78843F10FA092203609C4E85,
	AvatarMask__ctor_mF37179333D681B0089379006E3FAA89A0AB0C232,
	AvatarMask_Internal_Create_mDC9923A288EDD1F883662D1A5C562BFCDA8F6FFA,
	AvatarMask_get_humanoidBodyPartCount_m48E096F1C159EEEAA4E8906385A058171240C7B2,
	AvatarMask_GetHumanoidBodyPartActive_m9CB208B9F4A6E150B0C8335FF7F86F6DBD4C9C69,
	AvatarMask_SetHumanoidBodyPartActive_mC1238644347C024ABE0330521E8EFBE678CB3CE3,
	AvatarMask_get_transformCount_m4217FC2AAAB139DD391FE7404096BE69EC4227F7,
	AvatarMask_set_transformCount_m831071A48C9D20B3EBEBD5821AC89DC399AF366A,
	AvatarMask_AddTransformPath_m350F95084AC3B51B09E9CAD277B77A9048A4D0CF,
	AvatarMask_AddTransformPath_m51EB1C7D29E5595C729014ED27A61FAFBA558EB2,
	AvatarMask_RemoveTransformPath_mC4A4A3E149E628E84A25393EB39B87D0FEDC68B8,
	AvatarMask_RemoveTransformPath_m9953AD63286DE199F12FED0A0324984620CDA774,
	AvatarMask_GetTransformPath_m63FD27A96E20C77FCE6E164D994C8666C9900CA5,
	AvatarMask_SetTransformPath_mD07EA9D0ADD17EC84BDD51D4B54F79B037F37D21,
	AvatarMask_GetTransformWeight_mB0574B9F42737B0115832C96BB8F01C5AEDB7BD2,
	AvatarMask_SetTransformWeight_m74352AA4A2E6279E267BE17796993524764F5A8D,
	AvatarMask_GetTransformActive_m72AA5B6C5707F645496E610DA69D5736396F9AF8,
	AvatarMask_SetTransformActive_m2B300D3E293D782A2397383D2BB5DD401253CB62,
	AvatarMask_get_hasFeetIK_m6DF328662BDEDAE1CB4CB5370F67D6A7BB9B4FE2,
	AvatarMask_Copy_m603B23118089201052B3F6289D314F10F02BBEFB,
	HumanPose_Init_m9BBDA2B4B2EB0DE3082EE7FE36A92F19870F682D,
	HumanPoseHandler_Internal_CreateFromRoot_m93990E1FEE4C5FD08663157192B112CCF251BCEA,
	HumanPoseHandler_Internal_CreateFromJointPaths_m4380069FCDB3991540D2106AC78D61B308FB73B2,
	HumanPoseHandler_Internal_Destroy_m824560DD4448B327FFF1227E260F6A92F42F0940,
	HumanPoseHandler_GetHumanPose_m7F8C4612504D3197D42D6DA82F8D27B1C808AAF0,
	HumanPoseHandler_SetHumanPose_mA5E4639B077C37CF5CB269D1EA59C42847BC2C87,
	HumanPoseHandler_GetInternalHumanPose_mBDAB98A9452A1187BE34C0DA94F6500E71932477,
	HumanPoseHandler_SetInternalHumanPose_m754B356841FF205448997D10EBA84D72893DB027,
	HumanPoseHandler_GetInternalAvatarPose_mAC843356A0F4640AB83C3461C6B0543D0B39CA89,
	HumanPoseHandler_SetInternalAvatarPose_m7320C15BB27D4EEB9E8C5E534299E5C64A48F869,
	HumanPoseHandler_Dispose_m642803C0CB9C24C89F3A427D160AFCC72F9F4380,
	HumanPoseHandler__ctor_mF51EEC25B7A32ED62AC281B22D12C4B58E10B1F9,
	HumanPoseHandler__ctor_mD3A69E1030DED25C7B68D9287D9438C63C91B61A,
	HumanPoseHandler_GetHumanPose_m9323A5049D8A755FDE0E3DC21701A4788D4A7B00,
	HumanPoseHandler_SetHumanPose_mA7FD443D9B103114E4872FBC5B0A827E1D721562,
	HumanPoseHandler_GetInternalHumanPose_m4D1567607DBF8C1C0FEC01C47E7A1CFE214AC823,
	HumanPoseHandler_SetInternalHumanPose_mCC6160E5546D73857604B0C1DDEFA25B64466F5D,
	HumanPoseHandler_GetInternalAvatarPose_m39BA2AEB30F917938C71EF2D8A7CF793D88AA55E,
	HumanPoseHandler_SetInternalAvatarPose_mB7C4E37F1CAAE1F28F7E6248B9F3F591CDAAF70E,
	HumanTrait_get_MuscleCount_m1710B0FAD40DB860AD5C117A831F7554F5C55678,
	HumanTrait_GetBoneIndexFromMono_mDE16E21275A55D263014CFD3307BEB7353AE6925,
	HumanTrait_GetBoneIndexToMono_mBFEEC0F2768E2A1AB97768A89D3872C3CA49AFEE,
	HumanTrait_get_MuscleName_mE3E2D79AA477F03384FFCAAAFD5BCA9822992599,
	HumanTrait_get_BoneCount_mDA1FF71CCE37796233D4073E7FF129C1714A6260,
	HumanTrait_get_BoneName_mE16F3C9E1E21791A44E44F414CD2AABB54C13E2C,
	HumanTrait_MuscleFromBone_mBF217868645416045E5FC09387C2E56BC04260BF,
	HumanTrait_Internal_MuscleFromBone_mD0D91596B0FD0F3CA5104F324D289E01D1567693,
	HumanTrait_BoneFromMuscle_mA8D408187E909C49D79C2565CA987C3DC67FBD97,
	HumanTrait_Internal_BoneFromMuscle_m3536556616325753A98AAEE1714EAE55A4B879AC,
	HumanTrait_RequiredBone_m11642C7E78DDE522012CEFFE18622F3ECD069684,
	HumanTrait_Internal_RequiredBone_m2ACFEDF683A544288BEF76DF0A7744751194997C,
	HumanTrait_get_RequiredBoneCount_m76DF6A8C86B8FA70DEEA21DFE09E4EBFB4D724DB,
	HumanTrait_GetMuscleDefaultMin_m150AF046E5151B12B4215887C4FF0FD670281DD7,
	HumanTrait_GetMuscleDefaultMax_m7C09EC4712257BDF2E5FB2F29E4E1C9586BFB61C,
	HumanTrait_GetBoneDefaultHierarchyMass_m79A2D2663D40D60E833AAD6FA3FBB7DBFF7429F2,
	HumanTrait_GetParentBone_m0DF55C31BDF55B9EEB3F824D2F0AFC7F9A8EA04F,
	HumanTrait_Internal_GetBoneHierarchyMass_m31DC4F9216330D4BBD9FD4CC24CE27F8D0BD3BCC,
	HumanTrait_Internal_GetParent_mFE44534354CB0B140461B732E14B439D043921B1,
	HumanTrait__ctor_m28F228B02CC30621C388DF9DDBF49106925B8155,
	Motion__ctor_mB6190858E566BFA1B80D2E94B65CD27920A49443,
	Motion_get_averageDuration_m95FC30447DBEBE2D275AD0C8C0C402EED2F68CA9,
	Motion_get_averageAngularSpeed_m5167326516C36DFDF57F80CDE104054DD4957D74,
	Motion_get_averageSpeed_m76C239071A90E7E2CAFD34BCBAA5F15FC209C148,
	Motion_get_apparentSpeed_mCA38527B2C278283818043AB9A9A75CC84D54844,
	Motion_get_isLooping_mD11B7C5FFEC7BC2BC5D2AB4D3B2A06D2959DFB99,
	Motion_get_legacy_m5303314C70A7764FA6491C8BD6C3564CEB940730,
	Motion_get_isHumanMotion_m20BB212B89986C77188968DBC9FEA2AA0DE25261,
	Motion_ValidateIfRetargetable_m0EB3C8E318FA325471E0CAC2B1C24573AEF70EC8,
	Motion_get_isAnimatorMotion_m974F82D669AD508710208ECB77D4CDECDCCC3F10,
	Motion_get_averageSpeed_Injected_m11BC2AAEBABFC331969F355305A413277D8C1533,
	RuntimeAnimatorController__ctor_m676D4538BB6C62314B256173C5F592EFCA16AAC8,
	RuntimeAnimatorController_get_animationClips_mA8F51FF202C2C41A3E9C5366ABDEE35EDFBE8F93,
	AnimationPlayableOutputExtensions_GetAnimationStreamSource_m3F98CCB6EFB89905DE573FBD053646207180BD44,
	AnimationPlayableOutputExtensions_SetAnimationStreamSource_m1D5F79DBDAFB09E3A236D96FE259D692150263DD,
	AnimationPlayableOutputExtensions_GetSortingOrder_mC0888CF21290E12B6D7C74B6C2F2719BCB392BDA,
	AnimationPlayableOutputExtensions_SetSortingOrder_m74876C6AE563CA280803F7781FBA7304FCE05FAC,
	AnimationPlayableOutputExtensions_InternalGetAnimationStreamSource_mA0211B15D6E8B649FF423A5B0FAC3A18397BE33F,
	AnimationPlayableOutputExtensions_InternalSetAnimationStreamSource_m20E22C1F0C5B12FC0FBB421D93B4BD5A3B62C3FA,
	AnimationPlayableOutputExtensions_InternalGetSortingOrder_m3F29418666600B1383C23D10199D00E08875B8F7,
	AnimationPlayableOutputExtensions_InternalSetSortingOrder_m67FB54EB92BA287562D0F0E71B3F63233161901E,
	AnimationPlayableOutputExtensions_InternalGetAnimationStreamSource_Injected_m5C2C2202C19285E2F5463707F591924E5EB77A15,
	AnimationPlayableOutputExtensions_InternalSetAnimationStreamSource_Injected_m52C444A031E6807C93C20CFC8017AC4FEB08BEA7,
	AnimationPlayableOutputExtensions_InternalGetSortingOrder_Injected_mA8B6B54474F440D201C8E2E5C4CDD5E236505AE2,
	AnimationPlayableOutputExtensions_InternalSetSortingOrder_Injected_mC9BF96471E656AB7CE06B616AAF2495D35B8CAA3,
	AnimationPlayableUtilities_Play_mACCC886DED3FE540B663261AF5EB6EF28510FB5B,
	AnimationPlayableUtilities_PlayClip_mC82BC4ABA6B41BDFAC321707E019FDDF713CFC0F,
	AnimationPlayableUtilities_PlayMixer_m218860335A7A8E8B258ECC057C2FFB904307C22F,
	AnimationPlayableUtilities_PlayLayerMixer_mD7EFC6A198A04BB38E802087B6FD70C947DF8661,
	AnimationPlayableUtilities_PlayAnimatorController_mEEEAE8FF2940BBC5A5873506CC25E4A94F1D652A,
	AnimationPlayableBinding_Create_m3E764C5FC95E912E727275AA705701933B93C2CB,
	AnimationPlayableBinding_CreateAnimationOutput_mAAF4FE67781A130389ADA1BD625D42215B4C55A2,
	DiscreteEvaluationAttribute__ctor_m8C8036DC8BB15BBD3BBB79F2D14637C0DEBCB151,
	DiscreteEvaluationAttributeUtilities_ConvertFloatToDiscreteInt_mADD1EB91441A214F69458BFF45F86E77D11A340C,
	DiscreteEvaluationAttributeUtilities_ConvertDiscreteIntToFloat_m6A22CA1A2232C185F6E10865530FFEDA68E28D7F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NotKeyableAttribute__ctor_m818249C0E7E98C56F41B672A3140A87EA568EB84,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AimConstraint__ctor_m80FCFFFF15865EDAFB10965D67D54BE4FEE6B103,
	AimConstraint_Internal_Create_mD6136600E870331489B3BB92CFE1395685907431,
	AimConstraint_get_weight_mD185F838B6D8385622204703A29046488BC84F9F,
	AimConstraint_set_weight_mBB56202E1F77BD3AB7405873574BB5A749637F51,
	AimConstraint_get_constraintActive_mD0DB3A45A614D383AC23B2A2932AEDC89C87073A,
	AimConstraint_set_constraintActive_m34250894015B9F702D6C0DE993A20BEAC2988561,
	AimConstraint_get_locked_m376BECF3A58FD1FD94EC33CF19997D2D4D3BCB60,
	AimConstraint_set_locked_m33A8E333F00470D89FD83955A4DB48EF01F54188,
	AimConstraint_get_rotationAtRest_m7C78B93BF4832569A1796D5A84C6FABBA2CC38AB,
	AimConstraint_set_rotationAtRest_m63D45529EC85ABFDEAF3B199DB7ECDDBCE7F50CD,
	AimConstraint_get_rotationOffset_mCFD8329E777015CD734B052BF524F3871E3150D3,
	AimConstraint_set_rotationOffset_m707C9A333CA9C3AF627E2D6701FE6A3D6BB9E038,
	AimConstraint_get_rotationAxis_m3C642353A7E3424140821BFA8FF349D4DE7615B3,
	AimConstraint_set_rotationAxis_mD492CB05C581FA758176C395254DF7F4D6EA3BCE,
	AimConstraint_get_aimVector_m6ED598BF93DE3A32527307F17F91110660364741,
	AimConstraint_set_aimVector_mE0E1591858C11F330EF3ADA42813C1D09F40A8BB,
	AimConstraint_get_upVector_mDE4FD96451132091B51152BBB739B73C9D071C02,
	AimConstraint_set_upVector_m9AF10EBED0C8922461DB1F33E5CDE926A939BBAF,
	AimConstraint_get_worldUpVector_m2F1A059CC2BF793FA973210643F20995D7586718,
	AimConstraint_set_worldUpVector_m33709BCFE4AA1CB440EC92C2D9B73BCD159D5BE4,
	AimConstraint_get_worldUpObject_m9FF71569DF5DAEB5E94F06F2BC4E267E2A68F312,
	AimConstraint_set_worldUpObject_m26C2D99B9B28D9A12A943D2A7F9828886EBAE6D0,
	AimConstraint_get_worldUpType_m615A310D122FD587014B1730BB83FBDC57E4DF66,
	AimConstraint_set_worldUpType_mE3307BA728EF9CC6541FCFE4165F1B147B2EF08D,
	AimConstraint_get_sourceCount_mB613FABBB919793E2162B0D8842E95D01C756D61,
	AimConstraint_GetSourceCountInternal_m765C3B4640025395472984FF5B6280EF3C891004,
	AimConstraint_GetSources_mE2BBFD54D95BD3FE67E9E438DA4171BCAAEF46FC,
	AimConstraint_SetSources_mF46CFD6E308BC1E7E0D16B8F0D490E8456181768,
	AimConstraint_SetSourcesInternal_m624C1C1E2682D0F99C3198708197DED0C9050C60,
	AimConstraint_AddSource_mD3EFF3D73F07063B20F7F6B502805AC56982550F,
	AimConstraint_RemoveSource_mEA4AD231F515FFF085327A4F9B5CAA1FB15ADBB7,
	AimConstraint_RemoveSourceInternal_mFF08EDAEB9206273D569F7D40DED7C762EAA9171,
	AimConstraint_GetSource_m27ACCBD36B9052AAC783CD6F39FF62D111E386E0,
	AimConstraint_GetSourceInternal_m484EACB1D8DBF4D5BA75919E00A65D03B784502B,
	AimConstraint_SetSource_m77A4824FE368602FDD9F938335FDE37A4DF6E680,
	AimConstraint_SetSourceInternal_mA162366E0D74309449C467F197BF4248ADE84D91,
	AimConstraint_ValidateSourceIndex_mADC2F8EEF24E30F5E4CDF706C1734CB73C5E8AA8,
	AimConstraint_get_rotationAtRest_Injected_m0D498427FFE951EAA3C04B7392C54824C0946ED0,
	AimConstraint_set_rotationAtRest_Injected_m53CB6D52FAA064B5B0890A98BA297CA74E1E2EB6,
	AimConstraint_get_rotationOffset_Injected_m87D1DC1590653A8789753320193772E6CFEB9B83,
	AimConstraint_set_rotationOffset_Injected_m0F45D42A9EFBBA8BE75B8E79D0E0CBD4ADD4C30C,
	AimConstraint_get_aimVector_Injected_m9D3878400C04B62C20192D902E88B25660AAA9A1,
	AimConstraint_set_aimVector_Injected_mA9D542713DDB89DA2273E6AAA8103B2281C71780,
	AimConstraint_get_upVector_Injected_m12DF6995D524FE50BEC7EF9496B3AC42EE312E71,
	AimConstraint_set_upVector_Injected_m3F69AE1B9C274062A143F39EA409BDC3D425D350,
	AimConstraint_get_worldUpVector_Injected_mDD12062C70B6A060698A0B256D92FB0E6B0225C0,
	AimConstraint_set_worldUpVector_Injected_mEDDCBD31F61DD7AF4D15E9E1657A58B5289F8CFE,
	AimConstraint_AddSource_Injected_m2D072FB44070CFBC58DF459EC1194A283947457D,
	AimConstraint_GetSourceInternal_Injected_m0ECD9322D4E2E0A1DA1C17C1CE0B26CBA9DCF99E,
	AimConstraint_SetSourceInternal_Injected_m7B56A4DB1D5ED84B8F3337761C3E56411B883FEA,
	AnimationClipPlayable_Create_m034A4A30AC2642E675B95A0A7C3C384F533F5C1A,
	AnimationClipPlayable_CreateHandle_m9804DF3694EC65E8531F6839194AB189401AE564,
	AnimationClipPlayable__ctor_mF2EE31CC772B100F98CCAE26963059C6C722FA1A,
	AnimationClipPlayable_GetHandle_mE775F2247901BA293DB01A8D384D3F9D02A25627,
	AnimationClipPlayable_op_Implicit_m112BA2303DA5A9A8E24310332E3C27E13F74A0FD,
	AnimationClipPlayable_op_Explicit_m628ECE4D1BE08300E899184375D65052AEC50E00,
	AnimationClipPlayable_Equals_mC5263BEA86C02CEDF93C5B14EAA168883E1DB5F4,
	AnimationClipPlayable_GetAnimationClip_m65C9B50E705936C7E6B3F42A2BD71B704D3D0E1D,
	AnimationClipPlayable_GetApplyFootIK_m3E599D05D6A40BEFD651618CE5DDA03F15A3610F,
	AnimationClipPlayable_SetApplyFootIK_m7CBA77F56815AD21784AC53D9EBDAE18AFA48507,
	AnimationClipPlayable_GetApplyPlayableIK_m04EE9E4136AC350F27814DF3B006238260CF3EE9,
	AnimationClipPlayable_SetApplyPlayableIK_m69A6F6E28EB250956E27C1720A0A842848F54DAB,
	AnimationClipPlayable_GetRemoveStartOffset_m52EBB080BD11079E3D2F2AD6E913E5451F24CFE2,
	AnimationClipPlayable_SetRemoveStartOffset_mBAC88E40F6A759FACA4105EF683181D43381C8E5,
	AnimationClipPlayable_GetOverrideLoopTime_m22078B967400E8B3E5D056BCE1704CBB2E1E2C93,
	AnimationClipPlayable_SetOverrideLoopTime_mF1F57940D8DDBCC6EBCB75A27C2372BB39DED177,
	AnimationClipPlayable_GetLoopTime_mD57482D47862960C3A2D0185E8F75642DD858FEC,
	AnimationClipPlayable_SetLoopTime_m3AAA1134C4D339C84EF57FE289D33100D4971ED8,
	AnimationClipPlayable_GetSampleRate_m0559EF225E427721E66976BF290A0C53D1FFA744,
	AnimationClipPlayable_SetSampleRate_m2D7C98FD996AC3A582578A4433C9438D0675020C,
	AnimationClipPlayable_CreateHandleInternal_mB8466F44A261B040DBCE8BA442DA8CF7153D2212,
	AnimationClipPlayable_GetAnimationClipInternal_mBA8D6A1F7ECE71E67F946D2379ABBCC03469CFD8,
	AnimationClipPlayable_GetApplyFootIKInternal_m7695131A7D19C888FB75F2CE2227EC24361AA20A,
	AnimationClipPlayable_SetApplyFootIKInternal_m57C77DC9937F7BA02885EEBF5D7CDC1CF9412DFC,
	AnimationClipPlayable_GetApplyPlayableIKInternal_m4531AC8A44F8BE94292B95E03E33B5C7E9DAF474,
	AnimationClipPlayable_SetApplyPlayableIKInternal_mBCA5B580834435175256C8319275DD443281DD82,
	AnimationClipPlayable_GetRemoveStartOffsetInternal_m41FD6EE2AD07E2D17659795CC4D683E1D2B19B12,
	AnimationClipPlayable_SetRemoveStartOffsetInternal_m5C095AAB7C17821144A2CD4D7DDFE562358CC5A4,
	AnimationClipPlayable_GetOverrideLoopTimeInternal_m3AF34E428A96309497A077735D169D724D32FE64,
	AnimationClipPlayable_SetOverrideLoopTimeInternal_m44BAF0A6EE093D0E30EC8FF99423BF6E4B832CC5,
	AnimationClipPlayable_GetLoopTimeInternal_mF9036903929CA96BE7959D733A645901850B3268,
	AnimationClipPlayable_SetLoopTimeInternal_mC308D81D4A82EFACED882515D17935E43298E386,
	AnimationClipPlayable_GetSampleRateInternal_m25C19C01361060CE824DE0B2306284AF622A656E,
	AnimationClipPlayable_SetSampleRateInternal_m9B8747DC0D8BDE73FF8899242E646A83F8E420D4,
	AnimationClipPlayable_CreateHandleInternal_Injected_m4FD6B80E5194144660D7974F13F44BA0355C6E3B,
	AnimationHumanStream_get_isValid_mFC26001D2772FFDE3C791A764954BD9A1512DD6C,
	AnimationHumanStream_ThrowIfInvalid_m35ADC8567F7DFB74AA71C4E29572E06A8223DE65,
	AnimationHumanStream_get_humanScale_mD8924C6D1BD6723AB50A52D89FFFC92855BC47C6,
	AnimationHumanStream_get_leftFootHeight_mC29F681BA1B4712AD46F911D902BE07CF8FB78C7,
	AnimationHumanStream_get_rightFootHeight_mA2AB707DDD51031FAC763BA1610C2BFCDD80AFBE,
	AnimationHumanStream_get_bodyLocalPosition_mA8B1A8A9625C388B7E4E12BE814B4AA1D8D3DC5B,
	AnimationHumanStream_set_bodyLocalPosition_m5EAC7202D30F117B8821BDDC5282FF19FBB93706,
	AnimationHumanStream_get_bodyLocalRotation_m6224C03E8D34FAEB50C732CB7EBA7E2328A8EAD6,
	AnimationHumanStream_set_bodyLocalRotation_m19E818A4DDF88A4BCC520011C5F9864657F106B4,
	AnimationHumanStream_get_bodyPosition_mFA136469110BADCEE3EC2821AD408108A7F3516A,
	AnimationHumanStream_set_bodyPosition_mEFD7D98A88B79674702D53D659A2338D5E56F02C,
	AnimationHumanStream_get_bodyRotation_m6C3E24BCFC5B10524FD8143D33706365CFF2517C,
	AnimationHumanStream_set_bodyRotation_m16999012B4CEE244F4596C78D98E12B649302846,
	AnimationHumanStream_GetMuscle_mD12DF41EA39BB88236B6E51C6E60DC505CD3B578,
	AnimationHumanStream_SetMuscle_mF6E7B3FB78B9E085ADBFA3F5959C065BBDA5F481,
	AnimationHumanStream_get_leftFootVelocity_m7FA5AA97C252763E010294C1B3F820ECC070E2D5,
	AnimationHumanStream_get_rightFootVelocity_mE89F940C04F0842CD0A2BBCF394B657FB4080EEB,
	AnimationHumanStream_ResetToStancePose_m6CB3AAA301BCBA6ABA5A1DC4EE8767301AFDEAC7,
	AnimationHumanStream_GetGoalPositionFromPose_m2773EC1B9E600BD95B53DB65745A53468EF3820D,
	AnimationHumanStream_GetGoalRotationFromPose_m0D29BD6D2246DF74E1EFD77D909550A1E8CB96C8,
	AnimationHumanStream_GetGoalLocalPosition_mAE43F299D80C2E9DF69E41BC0394CCCEABC079FB,
	AnimationHumanStream_SetGoalLocalPosition_mA10C3B87B9FCB49197FED94B68BB72415815527A,
	AnimationHumanStream_GetGoalLocalRotation_m786FED58DB427635329550B3216AACA3972059FB,
	AnimationHumanStream_SetGoalLocalRotation_mD853EA0BAF5C880FBC6EC57AD5F0D667F241168E,
	AnimationHumanStream_GetGoalPosition_m9B04E0871FAE6225F9715C7388B334A31834014B,
	AnimationHumanStream_SetGoalPosition_mC32B190C32B2BFD1FD098C8FF2E81BCE08658A55,
	AnimationHumanStream_GetGoalRotation_m3F754F7E38B1A0E7CC6D4F5517F68FD0017AFA05,
	AnimationHumanStream_SetGoalRotation_m0AC3D5B3539CCEC2C715CEF37AF96AD91411ED54,
	AnimationHumanStream_SetGoalWeightPosition_m31CDD1019E6EC4583B13310A3D6B42A3129CCE29,
	AnimationHumanStream_SetGoalWeightRotation_m9E28713D497E4871CD4513FC0738642F36021009,
	AnimationHumanStream_GetGoalWeightPosition_m97C54429B8000F50C0C8D7BD695B1D6A92A4C376,
	AnimationHumanStream_GetGoalWeightRotation_m156696D9925EBDEEF0114C79FE8E2A3FAA07AC4D,
	AnimationHumanStream_GetHintPosition_m8C48689F4E696E601892CDC959B0C0BDBD67BBBB,
	AnimationHumanStream_SetHintPosition_m7C26134F50A8C6786430A0A5382884E55549112A,
	AnimationHumanStream_SetHintWeightPosition_m00D5FD24F69AC10C7B133ACAFB71BB2362E4E635,
	AnimationHumanStream_GetHintWeightPosition_m0D51CD0A32B87BA0208942ABC3DBF3AC0FB8FE9D,
	AnimationHumanStream_SetLookAtPosition_m6C0D265D80FF6FD89159A9F9D47172B37093D2A8,
	AnimationHumanStream_SetLookAtClampWeight_mC76D8266ABA59385337106B48BF2F5019DEC29CA,
	AnimationHumanStream_SetLookAtBodyWeight_m4A5D39D1BB635A01DF98E65F45AD873E44236BCE,
	AnimationHumanStream_SetLookAtHeadWeight_m8E9632C51EB7FC316FF4DB3F1639D41CB20474E0,
	AnimationHumanStream_SetLookAtEyesWeight_mD5FC3552642F7A64F794EC8D0872972F8A6AD507,
	AnimationHumanStream_SolveIK_m36549A812DA9901B415ABF3A9BB40DA236C625F3,
	AnimationHumanStream_GetHumanScale_m528C523BF55034961049D455994749AEF778117E,
	AnimationHumanStream_GetFootHeight_m73B1AC0DEA1287024D0EA1D53F9303E0B8A358A6,
	AnimationHumanStream_InternalResetToStancePose_m1EEC3F6EC405EEB4D36C2CC2D15BFA5CACE50144,
	AnimationHumanStream_InternalGetGoalPositionFromPose_mF9D079DA1E5D728A79637826DBDD8FB7A068E2F3,
	AnimationHumanStream_InternalGetGoalRotationFromPose_m6689F3C3DAE9513AE06956C93368334B64D95291,
	AnimationHumanStream_InternalGetBodyLocalPosition_mFB26EABB0EB551CFA4232D32065A79BF36E356C7,
	AnimationHumanStream_InternalSetBodyLocalPosition_mC469D6040AB3305AE399215925156806A48F1220,
	AnimationHumanStream_InternalGetBodyLocalRotation_mB5A3ABB9FDC0C71BA5FF77D111CCA40C681BD631,
	AnimationHumanStream_InternalSetBodyLocalRotation_mCC66C848E64D04B20A901D7D0760BB2D794B39C3,
	AnimationHumanStream_InternalGetBodyPosition_m549D7BB8B6B6BBA95AA6E04259D975CF81F99849,
	AnimationHumanStream_InternalSetBodyPosition_m0E963FC02C014FF1D519356DAF90E1E37D66DDBA,
	AnimationHumanStream_InternalGetBodyRotation_m533F3431386D21B1A170378120A34A94D76DF121,
	AnimationHumanStream_InternalSetBodyRotation_mB72C4A04EF45AA6E8CC9E52638FB65766885D34C,
	AnimationHumanStream_InternalGetMuscle_mCB75C7C1E13D6B7C0ABBCD4270E94DD025CC876E,
	AnimationHumanStream_InternalSetMuscle_mCC068A0689AB992DB22C4178AEA8390E3F49119E,
	AnimationHumanStream_GetLeftFootVelocity_mB62C9F36949F43F881DC225B5AD92B010E7961D7,
	AnimationHumanStream_GetRightFootVelocity_mEE89EE87CFD969E2ABE2FF6A2FABFCB651489F28,
	AnimationHumanStream_InternalGetGoalLocalPosition_m53C7592FE48A5E61D96031CA44D075F3C64E2A44,
	AnimationHumanStream_InternalSetGoalLocalPosition_mAFA13881BA09B0CFFA788B24088CE98A4EFC387E,
	AnimationHumanStream_InternalGetGoalLocalRotation_m223B1725AA46FE23E693AB1E2FD20E96AD9D578A,
	AnimationHumanStream_InternalSetGoalLocalRotation_mCE4ED1A96EBE0BED4FDE8394BD1049CF857970A1,
	AnimationHumanStream_InternalGetGoalPosition_m25C63A96C6858BF176670F3FC620BFF1672FC468,
	AnimationHumanStream_InternalSetGoalPosition_mAFBF83E782A12A0BEBDD63C2BA165541C5AC3029,
	AnimationHumanStream_InternalGetGoalRotation_m48632794B5A9E479ED6194902347E52CC072E4F0,
	AnimationHumanStream_InternalSetGoalRotation_m3810FFA6A48991BA97DC9CE844C8C04104607681,
	AnimationHumanStream_InternalSetGoalWeightPosition_mA9E5685E3F30AED1A5AE87E1135BF5047DA8D326,
	AnimationHumanStream_InternalSetGoalWeightRotation_m8685321C9439B4DA865675908633BDD952CDA8DB,
	AnimationHumanStream_InternalGetGoalWeightPosition_mFB2155CD3EEE62EDC9176A80EDBECA6D1E0BD6CD,
	AnimationHumanStream_InternalGetGoalWeightRotation_mF2E03DAB54D65B15ABF65FD68C39EB48928BE9C8,
	AnimationHumanStream_InternalGetHintPosition_m3D8100E5F06F8C05F717DD1CD9A424DD26396434,
	AnimationHumanStream_InternalSetHintPosition_m7DC7D373633BD22E372BEE6F02E662A3C2C278BC,
	AnimationHumanStream_InternalSetHintWeightPosition_mD6136E04EDF9E591CEAAF8AB09919DCCE54553FD,
	AnimationHumanStream_InternalGetHintWeightPosition_m4F89A6FD73DEA7166E91D0ECEE416BAEE989982E,
	AnimationHumanStream_InternalSetLookAtPosition_m6DC48530B5D259E37BEB489DCD2BBF07D8CB832E,
	AnimationHumanStream_InternalSetLookAtClampWeight_mBA29A943B22AA5DD8917EB5EB9589F04E76D6857,
	AnimationHumanStream_InternalSetLookAtBodyWeight_m614FEABDB4E114CDA84A94333AA7E2BF9ED4BDF7,
	AnimationHumanStream_InternalSetLookAtHeadWeight_m184F2F1D223714F0FD5B529C89207D9F6C0CDF92,
	AnimationHumanStream_InternalSetLookAtEyesWeight_m99A49670FE2C24ADFEA9DBBBA4135BDDA93EFE1B,
	AnimationHumanStream_InternalSolveIK_m84F06C829271E6F0B15B7653BD35A26A2D18046E,
	AnimationHumanStream_GetHumanScale_Injected_mEA19F4E31DFE65A7D56BA5B2EBA217E72EE06A58,
	AnimationHumanStream_GetFootHeight_Injected_m0647D377CEA3F2C6154BD6506AF5FC1930164CEE,
	AnimationHumanStream_InternalResetToStancePose_Injected_m36E4E449DD952DAAB35818B74915CC145084688A,
	AnimationHumanStream_InternalGetGoalPositionFromPose_Injected_m003DE04C63342B9E755F184DF6314D0A337B340B,
	AnimationHumanStream_InternalGetGoalRotationFromPose_Injected_m0C629C81F5CF4232EBA5E416A43DDEAE0FEEF39C,
	AnimationHumanStream_InternalGetBodyLocalPosition_Injected_m3F3777D546F51B80206180AB55F514FD47961A49,
	AnimationHumanStream_InternalSetBodyLocalPosition_Injected_mC7E59666CA9F990D4E5314AB6C1AF3EABF33E925,
	AnimationHumanStream_InternalGetBodyLocalRotation_Injected_m9FAC1277B0CB02B56C8AA6513564B212E7149161,
	AnimationHumanStream_InternalSetBodyLocalRotation_Injected_m3EFE47F3DB39DC05901DE9DB9D2FBCEE4529CA3A,
	AnimationHumanStream_InternalGetBodyPosition_Injected_mA3A52156F6CB2782C81D0C5FAA7EE05BF431CAC8,
	AnimationHumanStream_InternalSetBodyPosition_Injected_mFAE2CA55CFAB5AC553D7387332EEE31C15E35CAA,
	AnimationHumanStream_InternalGetBodyRotation_Injected_m75E3A42257FE94CFB22C3CAC3BBBF309BA22D8E7,
	AnimationHumanStream_InternalSetBodyRotation_Injected_m8A5F889592AA66AB02DA56A1AC5CAA7816644C28,
	AnimationHumanStream_InternalGetMuscle_Injected_m825CF7137F07F5C809369304C7A6B51AD6A292DE,
	AnimationHumanStream_InternalSetMuscle_Injected_m6450C555E5040B354E9998532FC8A815A34496F2,
	AnimationHumanStream_GetLeftFootVelocity_Injected_mE349F95F4022443A18B7AF49B332E4B63C9CA33D,
	AnimationHumanStream_GetRightFootVelocity_Injected_mE2A4867DD6869A588D30059D963813A10043F878,
	AnimationHumanStream_InternalGetGoalLocalPosition_Injected_m004A9C4D2AA06B30426EB05DE8782BF1481644C2,
	AnimationHumanStream_InternalSetGoalLocalPosition_Injected_m9FB590E17651CB3CE0A608607FFFF395E82B676B,
	AnimationHumanStream_InternalGetGoalLocalRotation_Injected_mF5E1BCC27D4621E7AA7EF1E5520AA11813524B42,
	AnimationHumanStream_InternalSetGoalLocalRotation_Injected_m229A170A92772EC0574B1B9FF3152CFD937B5FA5,
	AnimationHumanStream_InternalGetGoalPosition_Injected_m97488830A535BC3137E2486821CBF83F171C238C,
	AnimationHumanStream_InternalSetGoalPosition_Injected_m0023207419029D627B25F79420F5E67E39C44F33,
	AnimationHumanStream_InternalGetGoalRotation_Injected_mFF3A9CC634E5501841628F97A2F243947B57CBDD,
	AnimationHumanStream_InternalSetGoalRotation_Injected_m8F7471D4B367E065F33AF67FE4DC14FF2345183B,
	AnimationHumanStream_InternalSetGoalWeightPosition_Injected_m6E8C7641181FF1B57CD2B3388167E67320A1B0C3,
	AnimationHumanStream_InternalSetGoalWeightRotation_Injected_m4189139D4731A9A6D6C992F0899688CF767B1B0B,
	AnimationHumanStream_InternalGetGoalWeightPosition_Injected_m44BBBC15D4F43B5CAF70DE788A4AC675BDD381CD,
	AnimationHumanStream_InternalGetGoalWeightRotation_Injected_m2150CE3A46C063490FC301BC5F959811DC8299CC,
	AnimationHumanStream_InternalGetHintPosition_Injected_mA5263F06378340FF9FB34C59063981450B728D39,
	AnimationHumanStream_InternalSetHintPosition_Injected_m8049649876D9B3B36DA292E797DB535C7C2A18A6,
	AnimationHumanStream_InternalSetHintWeightPosition_Injected_mD1AAC5760F69AAB039F112B82E203B1671171427,
	AnimationHumanStream_InternalGetHintWeightPosition_Injected_m0721431AC161BA604F4F3A9AAB756381E58597D3,
	AnimationHumanStream_InternalSetLookAtPosition_Injected_mE8C5F0DE986E6B1A34F4BF786C3C56D714C921E2,
	AnimationHumanStream_InternalSetLookAtClampWeight_Injected_mA8D2E45F77FC83E9F9F8B1A7AB2F02894E25BAB8,
	AnimationHumanStream_InternalSetLookAtBodyWeight_Injected_m5AD30D8D26A598812D738F6BDEC937EB66E2B4B2,
	AnimationHumanStream_InternalSetLookAtHeadWeight_Injected_m81CB542284565A5CDC5D2689EB89795D00AB0270,
	AnimationHumanStream_InternalSetLookAtEyesWeight_Injected_m442F84B029536CB4C150191DD11BCF97F5E13021,
	AnimationHumanStream_InternalSolveIK_Injected_m2E5AFD0FB9927A94F7B94139C93E56C62D245EF5,
	AnimationLayerMixerPlayable_get_Null_m56FCE6DE925B3ED017607BC6A88A71E5F04274B2,
	AnimationLayerMixerPlayable_Create_m572693A593412F4C58DDB479B346D14AB8D8AA48,
	AnimationLayerMixerPlayable_Create_mB080375BE13D2A1159D6AD4AB45FB10C36E389FF,
	AnimationLayerMixerPlayable_CreateHandle_m74B2930D89DABE9160B08D8C92D6EA6622D88A1D,
	AnimationLayerMixerPlayable__ctor_m28884B8B9F7E057DF947E3B43ED78EA107368BD6,
	AnimationLayerMixerPlayable_GetHandle_m324A98D0B0BFC0441377D65CAE93C914F828721F,
	AnimationLayerMixerPlayable_op_Implicit_m50234C22795358D76242C022AF5CC90DF7C0141B,
	AnimationLayerMixerPlayable_op_Explicit_m255DC49C6DA5D0E6A85E6DB4D41BBE0A63C6D7FD,
	AnimationLayerMixerPlayable_Equals_mA5D24E61E2DE1140B409F3B569DBA3C185751970,
	AnimationLayerMixerPlayable_IsLayerAdditive_m379268A18CFAD74371F6D4E0467072761BF84713,
	AnimationLayerMixerPlayable_SetLayerAdditive_m3B35E03C224B118E3F3D9E8A7B697AF570FBFB6E,
	AnimationLayerMixerPlayable_SetLayerMaskFromAvatarMask_mC4BDE2B476AC13C31053100085FAF6BC86000280,
	AnimationLayerMixerPlayable_CreateHandleInternal_mEEEEBA10E6AD409C8CAF16BDF7F0E89E47A91FC8,
	AnimationLayerMixerPlayable_IsLayerAdditiveInternal_m576416565697C3E26266D7577C4AB03041B975AE,
	AnimationLayerMixerPlayable_SetLayerAdditiveInternal_m0B39FA66BEF309D1E1FDBAA4CF1E20DA7338ADCF,
	AnimationLayerMixerPlayable_SetSingleLayerOptimizationInternal_mF1EC1B461F2CCB8D7E01799875DDB5FC8FE4BBDB,
	AnimationLayerMixerPlayable_SetLayerMaskFromAvatarMaskInternal_mDA82665D20D53C1638037283DDCFE7BB2B2DD035,
	AnimationLayerMixerPlayable__cctor_m27A78F2EB8840FFCC84901AB4E916ACCE8D8E49B,
	AnimationLayerMixerPlayable_CreateHandleInternal_Injected_m052C3DAAC09B1BADE847FB348E0FFFB228B17C26,
	AnimationMixerPlayable_get_Null_m46DF0AE67D02D0CA48798FCF5ED4DA5FFFCD7C14,
	AnimationMixerPlayable_Create_m96BFD63578EC4E4167A30E4899EF3DA2A4E431F2,
	AnimationMixerPlayable_Create_m4136E1F8A7BF26D3DE52C68111F8E1D789A7A8F3,
	AnimationMixerPlayable_CreateHandle_m98DCB979893A9C4F782B2E07EF12BD69CF838A9C,
	AnimationMixerPlayable__ctor_mBF84CC064549C2C00B2AE1174018335958EB7EA7,
	AnimationMixerPlayable_GetHandle_mBA6CEB1579A713A985D474E75BC282728318882F,
	AnimationMixerPlayable_op_Implicit_m7B2D50F94CD0EE3E66478A560CC929BE7C985323,
	AnimationMixerPlayable_op_Explicit_m7ABFCCF21A03E8E4A624C41F58CA3CD1DF2A78EE,
	AnimationMixerPlayable_Equals_m6EBE215636EEEA3196A43F4D6C1FE6DD704AFA4E,
	AnimationMixerPlayable_CreateHandleInternal_m0C404F86C8C0FDD248BED7E153F3BEFBEEA39D37,
	AnimationMixerPlayable__cctor_m7D67E8E778387293AF1ACB1FDBE6ADA3E456A969,
	AnimationMixerPlayable_CreateHandleInternal_Injected_mD26E05A0F2676C90B7F06E718B7843167D33FE1E,
	AnimationMotionXToDeltaPlayable_get_Null_m6A342703E3ECA758C93D23C389B110F2692E3CBD,
	AnimationMotionXToDeltaPlayable_Create_m9F2C95194E1B5F76A0399BDE8FCEF33A80B77F73,
	AnimationMotionXToDeltaPlayable_CreateHandle_m848549F4FA09509BCBD846D85E5E2E8DFB8CBE3B,
	AnimationMotionXToDeltaPlayable__ctor_mDE3C14B4B975AC693669D66B6E41BB6432AFA940,
	AnimationMotionXToDeltaPlayable_GetHandle_m09F605E78AD7F0135C7F57EB048031091A50E3A2,
	AnimationMotionXToDeltaPlayable_op_Implicit_m1AC02CC4C55FD3550D6DFFFB7ADF960BD7D6E35D,
	AnimationMotionXToDeltaPlayable_op_Explicit_mA9E879CF299FB215325B910F99033B7821A59F62,
	AnimationMotionXToDeltaPlayable_Equals_m7CBF3B7618EDBA4ECC2F3C2F54011248BC45CDCC,
	AnimationMotionXToDeltaPlayable_IsAbsoluteMotion_mCFE89CD743EC61AF6EF6117F72A36FCC26216487,
	AnimationMotionXToDeltaPlayable_SetAbsoluteMotion_m5D1B029F6E6BFFB521CC6CB72ACBE7EA27B28715,
	AnimationMotionXToDeltaPlayable_CreateHandleInternal_m9556CCBB3F290E53A0BA8021F06C8E83232EF706,
	AnimationMotionXToDeltaPlayable_IsAbsoluteMotionInternal_mEF55F7D4D71BEBDCCE515D2E0CE45117D949EAD2,
	AnimationMotionXToDeltaPlayable_SetAbsoluteMotionInternal_m616455F80B4EAE4A0CD24A29630792C62872E929,
	AnimationMotionXToDeltaPlayable__cctor_m4FC582F607F00D5E2A6B97219D2D4150AFA42AF1,
	AnimationMotionXToDeltaPlayable_CreateHandleInternal_Injected_mAFF1C58B8D07A6A9E92042C038231D7CC873EF11,
	AnimationOffsetPlayable_get_Null_m661CE89E4ED81EC690E6E5F6B50861FC75164513,
	AnimationOffsetPlayable_Create_mA5D6C2A6687EC937E35D758C88300F1F0056AB39,
	AnimationOffsetPlayable_CreateHandle_m27C7AEF0B9D954591B9FB7A9EF4C92CE96037518,
	AnimationOffsetPlayable__ctor_mBF3AC6493556DAAEF608B359BEBE8FA6D9F8DBFD,
	AnimationOffsetPlayable_GetHandle_m769BEFF90379AEAB0C579F7800953458CE3EBA78,
	AnimationOffsetPlayable_op_Implicit_m0718409D76954C4C3D1F02F8B55DD39C6BC66C0F,
	AnimationOffsetPlayable_op_Explicit_mA2A6B182D577BEF58BFFDCAFDCBCF59558E20843,
	AnimationOffsetPlayable_Equals_mEC28392ADD4E9639EB9228D106D93E21B3587270,
	AnimationOffsetPlayable_GetPosition_m1DED0A8F334427F11E32E844C6AB62BB0DBBE247,
	AnimationOffsetPlayable_SetPosition_mB3D98093AB2BDE7454642F90CDE62C71ED6B9F19,
	AnimationOffsetPlayable_GetRotation_m17F5666C4A8EF5033B545A3ED3769F8BD4702818,
	AnimationOffsetPlayable_SetRotation_m042FD5D84E348BEAE49AFCC84DDD14861A8C1AEC,
	AnimationOffsetPlayable_CreateHandleInternal_m877AC3B2ED395B0C0A7FC1E2C9E44BC7A776BABF,
	AnimationOffsetPlayable_GetPositionInternal_mD31B13AB023DB1C1856FBA8FD2386E9F9F5E82BD,
	AnimationOffsetPlayable_SetPositionInternal_m5DE5D178F3250104A72B7AC2145CF4E27810881B,
	AnimationOffsetPlayable_GetRotationInternal_m3C04140C3FCC4890A1A9FDF7DF3617F1E1BF7912,
	AnimationOffsetPlayable_SetRotationInternal_m269A33B4240A028BFDF6988F61690F9CCC3A71FB,
	AnimationOffsetPlayable__cctor_m6F50D35CE1FAF52BD587DD3B440CBDE34A76B096,
	AnimationOffsetPlayable_CreateHandleInternal_Injected_m39A68EF379AFC09A9E070474CA3E19859FB41F85,
	AnimationOffsetPlayable_GetPositionInternal_Injected_m5B9EC37E005794D980DFE712058DD6E424341E0C,
	AnimationOffsetPlayable_SetPositionInternal_Injected_m14B7B16EDD2925FBBF4C73F0009057C5FB0C3ED5,
	AnimationOffsetPlayable_GetRotationInternal_Injected_m49872A59A9BC09A682DA6577C4648481BD801BC3,
	AnimationOffsetPlayable_SetRotationInternal_Injected_mF191483313F9238202B6439298D9E6A7C916C462,
	NULL,
	AnimationPlayableExtensions_SetAnimatedPropertiesInternal_m9038247416A38E252EF0DCBCCBFE990589F4C51F,
	AnimationPlayableGraphExtensions_SyncUpdateAndTimeMode_mBF6173908DA7C04947C405230A1D66F108E7F493,
	AnimationPlayableGraphExtensions_DestroyOutput_m2F51C114F692C084C83D797B937C2E2394441A12,
	AnimationPlayableGraphExtensions_InternalCreateAnimationOutput_m2FBE35C9ADFA39052F34E49F46E39CEBD10F4B49,
	AnimationPlayableGraphExtensions_InternalSyncUpdateAndTimeMode_m7C6C30F3FC2930979C825953394E8C9F6D35E3F3,
	AnimationPlayableGraphExtensions_InternalDestroyOutput_mFEE7A93C4A44E5C9256E16969FF63E5616730FFC,
	AnimationPlayableGraphExtensions_InternalAnimationOutputCount_m4343C1C30D66AE678928B28CCD4B70BAD927C2FA,
	AnimationPlayableGraphExtensions_InternalGetAnimationOutput_m2144AD9B2AB6123D5D2D86DB06A1E6EA8D954105,
	AnimationPlayableOutput_Create_m65847A70F6C74854387814C5B1D4C281B6CCCDC4,
	AnimationPlayableOutput__ctor_mE4FB8AA6DFB2F3C18E04A9317F5CE53597A7D22A,
	AnimationPlayableOutput_get_Null_mDF5638798B49F3E7ACCF766C266D7F776E553900,
	AnimationPlayableOutput_GetHandle_m2A8E2A9CBD12EDCF48FC946445AB42802083338D,
	AnimationPlayableOutput_op_Implicit_mB256AA7AA6BC0577B47399941D4B42BBC5C28DA7,
	AnimationPlayableOutput_op_Explicit_m7139943338A06A1B3DE71DF52A0D253C6DC8877E,
	AnimationPlayableOutput_GetTarget_m2E55D32775E0B27063F697346701A0EB5424DA9D,
	AnimationPlayableOutput_SetTarget_m0F7745C4A721D76EB1E804AA48E70C9C798E0DCE,
	AnimationPlayableOutput_InternalGetTarget_m3ECC05C07DEBDD5ECCAB92A2213DA9087E43B5F2,
	AnimationPlayableOutput_InternalSetTarget_m49002BC3713A0AF76F2447A0147493F234B8E616,
	AnimationPosePlayable_get_Null_m9B90EFB9E0AFEB7DA3B9207F21C2BC057CDB8FD2,
	AnimationPosePlayable_Create_mAB7A4B7120AC4EC97DEB872B7911D465C0B1AF2B,
	AnimationPosePlayable_CreateHandle_m4FA33101F2C76AC3E150E864E1067D1633E51411,
	AnimationPosePlayable__ctor_mC6C096785918358CA7EC12BABCDF4BBD47F7BA3F,
	AnimationPosePlayable_GetHandle_m5DC7CA4CAF3CD525D454D99EBC3D12C3571B527B,
	AnimationPosePlayable_op_Implicit_mEB15D8B1FE99F595099ACB16EDF4D270CFA23C35,
	AnimationPosePlayable_op_Explicit_mC4A844D0BA9B66F5CF757C95207D0BE8E00B4063,
	AnimationPosePlayable_Equals_m10F1E7DD7037B2AB3F7DAE3E01A1DC843EABD0A3,
	AnimationPosePlayable_GetMustReadPreviousPose_m6C3C23E75C7EEB05DB84E83507F94ED09C1DF961,
	AnimationPosePlayable_SetMustReadPreviousPose_m3859165529333B3140AE4E9097FE66E992DA8AF7,
	AnimationPosePlayable_GetReadDefaultPose_mA4E24ADBB4A96DD62D17F50ADECD5F114D44C5B8,
	AnimationPosePlayable_SetReadDefaultPose_m852C2742CE8CFF49FF7216DA9D2B9C755C41AA20,
	AnimationPosePlayable_GetApplyFootIK_mDBE93A57F35A70EDE6F8503323EF2F7C5CADE32F,
	AnimationPosePlayable_SetApplyFootIK_m862FA48CF186206F9B488BFADE8C1866A1A863C1,
	AnimationPosePlayable_CreateHandleInternal_m28BA11AEB7DBA3258B5DB1CECF7A72F3D3A93A3C,
	AnimationPosePlayable_GetMustReadPreviousPoseInternal_mF24F70490ED57DBD1F4AE5D32C57BCC949DFE3CE,
	AnimationPosePlayable_SetMustReadPreviousPoseInternal_mA4C7FEB6B3C710E4830DB901D1E0615E27588ED0,
	AnimationPosePlayable_GetReadDefaultPoseInternal_mB85F523B7DA39AF3FFE5F8FAC1519A414B95BD73,
	AnimationPosePlayable_SetReadDefaultPoseInternal_m2B8EE66706344492E94D64DA7189173A54ABB1EC,
	AnimationPosePlayable_GetApplyFootIKInternal_m80669092972522067D6DBF598EE8E22EF9001664,
	AnimationPosePlayable_SetApplyFootIKInternal_mBDFE5547E8B8A90CC309294C257D655BFE5E0667,
	AnimationPosePlayable__cctor_mFA5FE84F06C8E9A89C07190055BC898525F897C4,
	AnimationPosePlayable_CreateHandleInternal_Injected_mB4BE0A67DA8722C0F7DE09741DC0B91B021D047A,
	AnimationRemoveScalePlayable_get_Null_mD85DD91001F1FDB7C8DF0CC942A1E752C494CD46,
	AnimationRemoveScalePlayable_Create_mABCE46803804604412781B86B321BE816649401C,
	AnimationRemoveScalePlayable_CreateHandle_mE15186AF821904AC09E8F0DC30096BF63A812D66,
	AnimationRemoveScalePlayable__ctor_m4D6C7C4AB8E078050B0CC34C6732051CF043CFA2,
	AnimationRemoveScalePlayable_GetHandle_mFFA58B879F31327187A20ED30E1C814B7BEAA9C6,
	AnimationRemoveScalePlayable_op_Implicit_m0658568E028F0099166238728756300A839F0606,
	AnimationRemoveScalePlayable_op_Explicit_mF01DB06BE2F531B09785C8EF2FA1DF34FE08E9F7,
	AnimationRemoveScalePlayable_Equals_m0ACDD59B80103591DA8E84CB387FB10778D8C327,
	AnimationRemoveScalePlayable_CreateHandleInternal_mDB6C3A766BA756328A459D4EBB356A8F8E289FD1,
	AnimationRemoveScalePlayable__cctor_m42E614B0B33898D92DFE06CA6045698BE94DE633,
	AnimationRemoveScalePlayable_CreateHandleInternal_Injected_m0ED3A968526682B946A2D157855A91B93CDE9A13,
	AnimationScriptPlayable_get_Null_mD4EB9FA15C4D1C87EC7E0E42D31FB790CA7BD2D4,
	NULL,
	NULL,
	AnimationScriptPlayable__ctor_m6DEFD72735E79009FC1484AA2A7A82E6CE601247,
	AnimationScriptPlayable_GetHandle_m30355B6EE1AA3BA36D628251FB4291386D223646,
	NULL,
	NULL,
	NULL,
	AnimationScriptPlayable_op_Implicit_mBF02678AFA7A679981EED33224D5B2E3AEB215C0,
	AnimationScriptPlayable_op_Explicit_mD74C3B99C11B5A3421E1240186EF49E475EA279D,
	AnimationScriptPlayable_Equals_mAD02E40704CBE4AB188DE0569052F8EA9864F4E4,
	AnimationScriptPlayable_SetProcessInputs_mF7DE3561731A6513E8FE0F0A85B0C38959621839,
	AnimationScriptPlayable_GetProcessInputs_mDF9A5B45217407FB9ED7DF8E7A1E3DEA4DD31A1F,
	AnimationScriptPlayable_CreateHandleInternal_m0E0B65982F224BCD21CAD27DF8758C67A65296EC,
	AnimationScriptPlayable_SetProcessInputsInternal_m2ACAC5C02655BBBF15A32B06F5CF1BAFC0775D9A,
	AnimationScriptPlayable_GetProcessInputsInternal_m38A3E81CA628A2FE8801C32C3CE58DB7520E6C90,
	AnimationScriptPlayable__cctor_m5ED4D3FC06BC7A51D3A48B5611F759CB00F7CF54,
	AnimationScriptPlayable_CreateHandleInternal_Injected_m6DD2A68DB5A0F7A25529B1899FB308F248C557E3,
	AnimationScriptPlayable_SetProcessInputsInternal_Injected_mD82651E462942B43F51D5C4456ABD600F159FD15,
	AnimationScriptPlayable_GetProcessInputsInternal_Injected_mFD6AA32BC5EFB2187902E8F9DDF6F4781D96CA22,
	AnimationStream_get_animatorBindingsVersion_mD7D19DCE96F93CE4DC36457F974C5B8562A3B5E4,
	AnimationStream_get_isValid_mE1F032BDA653D5A903DCD427F4677A6C9C4C227A,
	AnimationStream_CheckIsValid_m6C46800E1A5A4BE27FF761A93F72BC3CD751174C,
	AnimationStream_get_deltaTime_mECEF75B188313080405BFB556AB4CFD972233861,
	AnimationStream_get_velocity_m8628858DC7927B5DC15C3050945D8150E4166179,
	AnimationStream_set_velocity_m0C9F3849570576287D94FEC72556BA070F9B3D49,
	AnimationStream_get_angularVelocity_m3D7FCC57BD7A9EE99FE9DDAB3B425C700841C617,
	AnimationStream_set_angularVelocity_m0600FEEDAC435358DAE51BC5AA4EEAEED6649DD8,
	AnimationStream_get_rootMotionPosition_mAE5E4330D95DD4AB9BD65341CCB4340259235A40,
	AnimationStream_get_rootMotionRotation_m0CE33CEDB1195D26AE380A561A487CBB508A0530,
	AnimationStream_get_isHumanStream_mABF4806846E32130D5D4C204CAE4E8E0DA897158,
	AnimationStream_AsHuman_mADFB59FE0BC7C00348B567BE06A4E8E9C830A916,
	AnimationStream_get_inputStreamCount_m434C3915F96FC4C707622AA6F8F40F5BEF0031D0,
	AnimationStream_GetInputStream_mFFAEDE4760FD9FB17C6F702DB3E1835C77600E49,
	AnimationStream_GetInputWeight_mE168D42B18134F5B6F11B932C9EF3D523B4759DA,
	AnimationStream_CopyAnimationStreamMotion_mA5E2C0B009574DF88C47B57A3ABAFC6C20FA3EB7,
	AnimationStream_ReadSceneTransforms_mE817BB3693A822A761DFF489AC5661F93E076CCD,
	AnimationStream_WriteSceneTransforms_mD0A8D14E6A70F2DD4639E1D265F3D28961B8401D,
	AnimationStream_CopyAnimationStreamMotionInternal_m47DAF2063EEF6D09F538BD3A9E039DD01C68243F,
	AnimationStream_GetDeltaTime_m335ACEAEEAEE7E3FAE1CCBD81DA839C6C1CFF0A9,
	AnimationStream_GetIsHumanStream_mD58FA1E5EEF324695C45C8AAF3A79D13987DE576,
	AnimationStream_GetVelocity_m554A288784E59A32E2D39B826912890127AAC57F,
	AnimationStream_SetVelocity_m66A0FF467A4423DD1BEE265EADBEE273CB764382,
	AnimationStream_GetAngularVelocity_mCEAA43EE55D952A83B0AC4236DFFCD249DC8587B,
	AnimationStream_SetAngularVelocity_m6860A649F2D2A7B7082502753E5B94C12A10640A,
	AnimationStream_GetRootMotionPosition_mF64C52D3BC86F9B31E50426ABA638C5FF6E59D22,
	AnimationStream_GetRootMotionRotation_m4132427788382A623CF0703FF56AC34D6E31C1FD,
	AnimationStream_GetInputStreamCount_m3E9B20B8AA6D1A6704FEF58B384ECCDCC67C3624,
	AnimationStream_InternalGetInputStream_m19EEF4A1AA1BF053B21D5059E3836699D55A1974,
	AnimationStream_InternalGetInputWeight_mB4CE342A125415612BA0D6CF329EFE4DD15DC503,
	AnimationStream_GetHumanStream_m719D5DCDC0B6B4CC92E93BDFAA51D138A34DCC1F,
	AnimationStream_InternalReadSceneTransforms_m3CB8D564DC110A6A7475FA9F00C95D4294CAC588,
	AnimationStream_InternalWriteSceneTransforms_mA489D8D3B1B02D7455BF7FBDFF4D76409878ACFD,
	AnimationStream_CopyAnimationStreamMotionInternal_Injected_mCC4054F2FD0903D676F008618EF987DDA597D600,
	AnimationStream_GetDeltaTime_Injected_mF06679592F1FEFA1DD24E4553554913F59DAAE55,
	AnimationStream_GetIsHumanStream_Injected_m94B820440CE0A9F6EC7466876D09592F2AB24F46,
	AnimationStream_GetVelocity_Injected_mD436F0A4F5AAF20EB1DAD703F83EF67A47D3E6CE,
	AnimationStream_SetVelocity_Injected_m96A388162B416B60DC6ECC5BA2C98E93619045F8,
	AnimationStream_GetAngularVelocity_Injected_mB80547FB50E345B97CD5D61A646D799CDCB09416,
	AnimationStream_SetAngularVelocity_Injected_m1364F49A0C2B4856D493819AED904D7D842C2793,
	AnimationStream_GetRootMotionPosition_Injected_m4047461DC4096579DE3B6D16F45D98781D2B29DD,
	AnimationStream_GetRootMotionRotation_Injected_mE7A685170F97248DD4FE2B1E7D3E763BB08ACBED,
	AnimationStream_GetInputStreamCount_Injected_m969395150936A3DD845571CE2D9A0F31EADABF92,
	AnimationStream_InternalGetInputStream_Injected_m5E3E1BB36337B71FC93266B6A9DA83D538781DA5,
	AnimationStream_InternalGetInputWeight_Injected_mD307C8B079738BA84418C65CC212F9671BD7D8C3,
	AnimationStream_GetHumanStream_Injected_m99C333328AFCBB2201F1705C516AEE77F81C51EE,
	AnimationStream_InternalReadSceneTransforms_Injected_m473A4981947C9C4A8040C88E874C506C5A839ECB,
	AnimationStream_InternalWriteSceneTransforms_Injected_mE16C1422B6A493D4A2B75E30DE8F453140456916,
	TransformStreamHandle_IsValid_m96EE3A490B88868890CF2E754838F97424A65512,
	TransformStreamHandle_IsValidInternal_mBF1602E33ABCA25121C7CF70173D29C5291354CC,
	TransformStreamHandle_get_createdByNative_mCC27504004588C367456D55E8295B745BE2431AC,
	TransformStreamHandle_IsSameVersionAsStream_m31E41B516413440AC8F4D5F9F233623A6DE71365,
	TransformStreamHandle_get_hasHandleIndex_m164F6D37B1A6B74214B09E1E6798C275C71716D1,
	TransformStreamHandle_get_hasSkeletonIndex_m8B9589FACB6810B0EFD84033D30057ADFBC4B75F,
	TransformStreamHandle_set_animatorBindingsVersion_m5F9ED42B51BED505332D0D1B88CB823A8BEF3A01,
	TransformStreamHandle_get_animatorBindingsVersion_mD044F88843A162A554BA7EF191E52B58F9F0AFF8,
	TransformStreamHandle_Resolve_m5DDC5761EF01E700ABDB214030802982DABC3E6E,
	TransformStreamHandle_IsResolved_mB2A9548AAB37C1485AFEC2C9CD9A3D4ABC786D52,
	TransformStreamHandle_IsResolvedInternal_m83781A03679DF4C678FE963CF21F5A2203471585,
	TransformStreamHandle_CheckIsValidAndResolve_m7602706A5D46D99268DB6C698A6752C96A0525F6,
	TransformStreamHandle_GetPosition_m8980B6C6185653E9B962625D030C3BB1994C8B89,
	TransformStreamHandle_SetPosition_m45609A840DAAF0410F72218E58E2207841290002,
	TransformStreamHandle_GetRotation_m02E0CE9B403FB4138605190A48A19767D6B0C42A,
	TransformStreamHandle_SetRotation_mA159153895AFFB08B30B8287304A720215C364D1,
	TransformStreamHandle_GetLocalPosition_m30A1BF0A06551177E6D28A73D3DE71522B77C0A4,
	TransformStreamHandle_SetLocalPosition_mEC97D6C69019B8212F444B33DB51BDCD9DCD6282,
	TransformStreamHandle_GetLocalRotation_mB613F5958303751C9368AD2CC613723E279985AF,
	TransformStreamHandle_SetLocalRotation_m68B0586FA34978971ECDC909A44E1E0C13443C6A,
	TransformStreamHandle_GetLocalScale_m559039B8F2285CC33E3E952F078EF899C8ACB451,
	TransformStreamHandle_SetLocalScale_mEAC5ED65AA0B8F756E2C129ED14D78C7B3698FE2,
	TransformStreamHandle_GetLocalToParentMatrix_m91AC76F7B4D7B530A6D30E87E68E75C50B95DA77,
	TransformStreamHandle_GetPositionReadMask_m2901E4573FC6105194851C3FD1B88A3F18DA4F5F,
	TransformStreamHandle_GetRotationReadMask_m8D17943EA0E9F130DA1B5A2CE336625AAC7E4888,
	TransformStreamHandle_GetScaleReadMask_m12F8441C4AE4B23345BA37F5DFD82B5288695230,
	TransformStreamHandle_GetLocalTRS_mF633398360834FAD1B1F8E21EF8C2A01B3E38A8D,
	TransformStreamHandle_SetLocalTRS_mA4D470AC9B87FF6FAC880A926BD3A5F4EC30BFB2,
	TransformStreamHandle_GetGlobalTR_mA0526AC698E96B95E6BE3E17A477DB028EF8A499,
	TransformStreamHandle_GetLocalToWorldMatrix_m19D802014D758A8BE531FE3A7000371D59C5B195,
	TransformStreamHandle_SetGlobalTR_m8C4F35DE8E639AE7D7F94F1D015AD3C16D2FC406,
	TransformStreamHandle_ResolveInternal_m0008C8228981E9247DA8B0C7739DD1BF1C70EAEA,
	TransformStreamHandle_GetPositionInternal_m4D0EA1C47F1AAB4723411247DBA15135BA6A9D4C,
	TransformStreamHandle_SetPositionInternal_m0855C3D765D6635BFEBA847061CDC90B645246CC,
	TransformStreamHandle_GetRotationInternal_m53A7E32CE6B63F588F44CAE8FCBF23C32E8393C7,
	TransformStreamHandle_SetRotationInternal_mE7AB735A2303DC923A69B19537FDE60B5F39CE5A,
	TransformStreamHandle_GetLocalPositionInternal_mDF78249F5365FA56D51A9854D69DBD420CA2408A,
	TransformStreamHandle_SetLocalPositionInternal_m1758971CB7DC05A269612D1B975A22C8CB2CB890,
	TransformStreamHandle_GetLocalRotationInternal_m5AD8291814578D1F199FBBD2E336C43FC387CEAB,
	TransformStreamHandle_SetLocalRotationInternal_mDE8F5BF1C73A90573EF3918F1C88ABB73BC10778,
	TransformStreamHandle_GetLocalScaleInternal_mAE69D739C71A1F9AB26E9E3496294B6662F429A2,
	TransformStreamHandle_SetLocalScaleInternal_mE108B8F9D39C2C540C8619C7DECFDB685040F85C,
	TransformStreamHandle_GetLocalToParentMatrixInternal_m4D7A1D9602675F7998C24CCDAB752F8E7BAC8DBF,
	TransformStreamHandle_GetPositionReadMaskInternal_m18D7DFE8500F0B2BC42F06324B0286B73F8062FF,
	TransformStreamHandle_GetRotationReadMaskInternal_mB7D7E96C8D8CD0FBED076396FB14955E31CA732D,
	TransformStreamHandle_GetScaleReadMaskInternal_m1D3791F8161BA91D9EC2D5D744B05D33E419FBA4,
	TransformStreamHandle_GetLocalTRSInternal_m1B1B9B973843354BCA7D7A5A76CE44EFE7F2A203,
	TransformStreamHandle_SetLocalTRSInternal_m2FC862511AEAC5C2900D016CF31EB2E25D321D8B,
	TransformStreamHandle_GetGlobalTRInternal_m71E4832B7C5D99A91FDF742CA3E54F1C43CE34AF,
	TransformStreamHandle_GetLocalToWorldMatrixInternal_m218BEFABE5A04FE2DF4F0E038049386C6A8C5EC0,
	TransformStreamHandle_SetGlobalTRInternal_m9C62E8BD63B362A404C376B09005C024F54DC7B4,
	TransformStreamHandle_ResolveInternal_Injected_m3C2F5E0470031F9C0D33DD5C7C392F8B671B9CED,
	TransformStreamHandle_GetPositionInternal_Injected_m3C3917F08A68CD4E24653CB78AEEA3577E043462,
	TransformStreamHandle_SetPositionInternal_Injected_mC8D3099FC5DB732B46C4FAD237DA63CA59B2EA5A,
	TransformStreamHandle_GetRotationInternal_Injected_m4863F14E289B9627C1ACADB750B56CD81ED6F857,
	TransformStreamHandle_SetRotationInternal_Injected_m181A24DB2A658E488E3F0BC684A424547A6A7683,
	TransformStreamHandle_GetLocalPositionInternal_Injected_m365FDC3BE3E31E7E83ED3E348C089A788E25C724,
	TransformStreamHandle_SetLocalPositionInternal_Injected_mD54FB99E9BA8700F04F9A6C12F4FD857B1F68258,
	TransformStreamHandle_GetLocalRotationInternal_Injected_m0C379159283C0F4FEA175431383404203C624630,
	TransformStreamHandle_SetLocalRotationInternal_Injected_m58D671FC665AF45BE7B6753A65C3F27BDCD89EA3,
	TransformStreamHandle_GetLocalScaleInternal_Injected_m2D639FCDA072EBFE62AF09EFBAE8DD09C1C82903,
	TransformStreamHandle_SetLocalScaleInternal_Injected_m0AEE77042B2DC3DE6DD4D92C92129B4A52CD4742,
	TransformStreamHandle_GetLocalToParentMatrixInternal_Injected_m7EEDFAD32C00769D94C9E71AD905D5A56F547776,
	TransformStreamHandle_GetPositionReadMaskInternal_Injected_m2BBE94003338D8FE34E7862C779452B59A5C06E9,
	TransformStreamHandle_GetRotationReadMaskInternal_Injected_m155352ECE44BEB714E222AA193145903074B75C4,
	TransformStreamHandle_GetScaleReadMaskInternal_Injected_m97B2F5382EABF761AA94A424C0C630FECD1B015F,
	TransformStreamHandle_GetLocalTRSInternal_Injected_m2684C260C70D030AA66A6D9EE0641C6F8A0F41A1,
	TransformStreamHandle_SetLocalTRSInternal_Injected_m4185E07C39CD19689720F3F5B6161938C39BAAF6,
	TransformStreamHandle_GetGlobalTRInternal_Injected_mA425C4A4674833F9EEB44FBC2C5DB357B3EB306E,
	TransformStreamHandle_GetLocalToWorldMatrixInternal_Injected_mB6365AE0653C1E6F7D1E44266ACDC6048E86FFD0,
	TransformStreamHandle_SetGlobalTRInternal_Injected_mDE035F852727673707A01C6475FC52E38D98DB6F,
	PropertyStreamHandle_IsValid_m21FEF08137BB2DC014F731A98CBA5F516939723E,
	PropertyStreamHandle_IsValidInternal_mFE619567B465984FC8E00F07CC24D489802BB51B,
	PropertyStreamHandle_get_createdByNative_m2610F75D942E639F8D9919D9A8A8E2210503292A,
	PropertyStreamHandle_IsSameVersionAsStream_m105BA0425054D86214E70C3D0746517A0BBD5305,
	PropertyStreamHandle_get_hasHandleIndex_m296B641953CA1478332DE8D4E3616EDDE67F4415,
	PropertyStreamHandle_get_hasValueArrayIndex_m6BFF272278DB968E4732EE7BAA990F18258DC610,
	PropertyStreamHandle_get_hasBindType_mF482FD67DC2BBB8AF20A959C846430577FCC51B3,
	PropertyStreamHandle_set_animatorBindingsVersion_m091EA76553DCD034CF179B9F31BD25057B83764F,
	PropertyStreamHandle_get_animatorBindingsVersion_mAF352761E16C2BC5658A2B37C77EFC88173EA4C0,
	PropertyStreamHandle_Resolve_mE4B9D57D9092E7278EF68192F8E5F57D1ED3B645,
	PropertyStreamHandle_IsResolved_m44BC7FEA11CAA3862B733E319C7794F1C3536D86,
	PropertyStreamHandle_IsResolvedInternal_m7B96232330AB117B2050D16FE135103D6ED97DFA,
	PropertyStreamHandle_CheckIsValidAndResolve_mD4036D6F0444B68BC4C1AECDD7429FEBAF03203C,
	PropertyStreamHandle_GetFloat_mAEC50079467900B74F7B485BBAF65A4DE1BBB8DF,
	PropertyStreamHandle_SetFloat_m9FA9F67C2AA473A7395EACF2563E7C8A06008C33,
	PropertyStreamHandle_GetInt_m58CECCE1A73DB1EA96E287B5B7BF51BF502D8D98,
	PropertyStreamHandle_SetInt_m1E7A8AF3165E77EB0148AC17FB641711134DBE48,
	PropertyStreamHandle_GetBool_mBB5B008988E6CC47C526CF654E53804909C141E0,
	PropertyStreamHandle_SetBool_m8307F19E9C41A431FC9A71128A305B547BE675C7,
	PropertyStreamHandle_GetReadMask_m70E5969F001669CC1467E03014CF46C5E865F9DE,
	PropertyStreamHandle_ResolveInternal_mFB0A48675D8D847197CB392325CDCA837B82E64C,
	PropertyStreamHandle_GetFloatInternal_m11E03DE3C420D8F9BFA7926D1F452766BD34B783,
	PropertyStreamHandle_SetFloatInternal_m044594EAE3DEEC6030E096DB0A8F0454ADCAD6A8,
	PropertyStreamHandle_GetIntInternal_m598EC48F1700FB43EF1A5880178BB841A781D4C9,
	PropertyStreamHandle_SetIntInternal_m37894828B9FD37A78CCE5A6F9F9EB7E1C0FE72A4,
	PropertyStreamHandle_GetBoolInternal_m25D06AA6F53B3265E80243E589F39EF4C30E7DAD,
	PropertyStreamHandle_SetBoolInternal_mD237DF6939F5BE683D485E984C37791624C67A26,
	PropertyStreamHandle_GetReadMaskInternal_m28FDDC616FF6A3C9BF19F19FCC9103DD337DF9BF,
	PropertyStreamHandle_ResolveInternal_Injected_m2F38D58EFF0643C85F7ABA035DAC718D5A885B0F,
	PropertyStreamHandle_GetFloatInternal_Injected_m6604EAC314F4B69CAC8601BE540A723ADCC8734E,
	PropertyStreamHandle_SetFloatInternal_Injected_mBFF2DBFE042CB9C68B804BB413E28171D465A94F,
	PropertyStreamHandle_GetIntInternal_Injected_mDFDACC53C8FAA687E154D78C9A86B58EA836AF14,
	PropertyStreamHandle_SetIntInternal_Injected_m825B4B4CC03932B30E2A9DA34500012C429765FA,
	PropertyStreamHandle_GetBoolInternal_Injected_mDE6D207C4962AC0FC356962421418A632D7120D6,
	PropertyStreamHandle_SetBoolInternal_Injected_m4489226DE93D01A970F9BF2A71E11D0F826C804B,
	PropertyStreamHandle_GetReadMaskInternal_Injected_mC8AAD4CF45A0468422450726DEB7EFBB1E3D40AD,
	TransformSceneHandle_IsValid_m11DB3FA1E3137CA1C2D4D4BC18AD717FCCAC65E2,
	TransformSceneHandle_get_createdByNative_m40C489AAD66DEDFEB69F6BB25B1177FC51922D3D,
	TransformSceneHandle_get_hasTransformSceneHandleDefinitionIndex_mBD4B49152989D4379E6D726B0F7E834EA484383B,
	TransformSceneHandle_CheckIsValid_m33214B5950C49A143A5548B7FB1672062204655A,
	TransformSceneHandle_GetPosition_mDB8261C4AF79828292D555DBF91A6559DE41B3B8,
	TransformSceneHandle_SetPosition_m2119B1F81EB229CED3914AB214783D67C036EEF8,
	TransformSceneHandle_GetLocalPosition_mF5AFF93B5129702C280CCEB0603100377BFE2D32,
	TransformSceneHandle_SetLocalPosition_mA8A45E45843E29DEB53A99827816C7B0BFE01376,
	TransformSceneHandle_GetRotation_m8F6CA3E2302A43103A808120AEE1C527EB2A2F05,
	TransformSceneHandle_SetRotation_mB91EC437053AE3E393393C1D6B3F62270AA6DB0F,
	TransformSceneHandle_GetLocalRotation_mEB7C44D455654A40518C8AE8D8CC0D191E194B8D,
	TransformSceneHandle_SetLocalRotation_mF7DF58A87432FF06B73AFEB19D4030108F2CFCED,
	TransformSceneHandle_GetLocalScale_m46D9A20E7DC5967A0EE6F7C217FAA68FBB93611E,
	TransformSceneHandle_GetLocalTRS_m5FBE0248443B3D34C7787A50DE89BB473638CADB,
	TransformSceneHandle_GetLocalToParentMatrix_mE9193C3FF11071363E75E8F8BD89A285BFAF3797,
	TransformSceneHandle_GetGlobalTR_m7CB0885446CA0894BF681F1954C3E0FB8D31C9EB,
	TransformSceneHandle_GetLocalToWorldMatrix_mF79BCAA91FBE2949EC89A0A010A9AAFCFC2BE9FE,
	TransformSceneHandle_SetLocalScale_mE408411FD205489EAA1E148B98C2427BF3C201D3,
	TransformSceneHandle_HasValidTransform_m19ABA61E5902DA7F1207798BBB4DCFBE515B9537,
	TransformSceneHandle_GetPositionInternal_mAC29651E3EB5FC3BD4CB49B2B09EB9897375FC29,
	TransformSceneHandle_GetLocalPositionInternal_mAFD402FC292DD1D9BDBF6BE446E89CDD85A47182,
	TransformSceneHandle_GetRotationInternal_m53DDDEE9D5824A6E5BFEE5C5681A33E468E0FC5E,
	TransformSceneHandle_GetLocalRotationInternal_mC63DB1B1DA36584AC8C550683E9E2F67F350A688,
	TransformSceneHandle_GetLocalScaleInternal_m15883C28FC7DE56F00374B6A14F027CFC6C35069,
	TransformSceneHandle_GetLocalTRSInternal_mBB816EAB9B873A23A8B01C96789480F438968497,
	TransformSceneHandle_GetLocalToParentMatrixInternal_mAD951B3EF38C5A5F5043E1427B21BEFB5F1265DD,
	TransformSceneHandle_GetGlobalTRInternal_m821151819DD1E3F5ABA1D2C8DC5372C67A2DF343,
	TransformSceneHandle_GetLocalToWorldMatrixInternal_m963B53C92A8B6BC96C2DFE67E015156B731C6E50,
	TransformSceneHandle_HasValidTransform_Injected_m1F0CEB3799D44896F7F501AE0E99BF49938ADA4B,
	TransformSceneHandle_GetPositionInternal_Injected_m5CB0EBF73345BD9F90E504DCEFE1C2D4B349FAB0,
	TransformSceneHandle_GetLocalPositionInternal_Injected_mA2701FE4C3866CF6BB83C18C183D197975A002F8,
	TransformSceneHandle_GetRotationInternal_Injected_m50FA487714327D6B98868DB6322D1F72260B8BBE,
	TransformSceneHandle_GetLocalRotationInternal_Injected_m580089F9FDCF7CB1BFBFDBA70EE7C49BA3B90304,
	TransformSceneHandle_GetLocalScaleInternal_Injected_mD0E2E2A2B1728035C791DC88BAC3E16C3B96FBBE,
	TransformSceneHandle_GetLocalTRSInternal_Injected_m09BD17EECEC5ED6D21F7224862F01C0BEAD9CE9C,
	TransformSceneHandle_GetLocalToParentMatrixInternal_Injected_m3FF576BEEC1B3E8F2D88AC99A36AF688F8422D89,
	TransformSceneHandle_GetGlobalTRInternal_Injected_mBAF0D818077C87003CFACC9BCE40CD62C5208A26,
	TransformSceneHandle_GetLocalToWorldMatrixInternal_Injected_m2C5850C76DE15C45908CB78F27702C4BDD5227C4,
	PropertySceneHandle_IsValid_m88FB030E99AE0E2D8A4506C7B06BAF8B27CE481E,
	PropertySceneHandle_IsValidInternal_m60ACFD75DC826CB176ED25ABDF6244EEEFA26D7F,
	PropertySceneHandle_get_createdByNative_m1579532A69F2E0052D6028BB8563E693DACC044F,
	PropertySceneHandle_get_hasHandleIndex_m8A4BD33134980310FFD1568BF2B0F1E7D88D7E8A,
	PropertySceneHandle_Resolve_m4ADA3EE521AB78FEFCEDBC666CA66868D67B0075,
	PropertySceneHandle_IsResolved_mFD3DCB641948607A93E40B0D2E6897333F68D7EA,
	PropertySceneHandle_CheckIsValid_mA5326B76CA28F14695D23826F1F43215C353A033,
	PropertySceneHandle_GetFloat_m2DA1CB5219344BB6A59421E15A74C6ADE286EC24,
	PropertySceneHandle_SetFloat_mCDF00CD9F89DB5906FA72CAD9CE1AB98E3322E12,
	PropertySceneHandle_GetInt_mA22A93D80220EE3CF94C7DCFCC70A958F125418E,
	PropertySceneHandle_SetInt_m927C12912421F35BDBC5FF6FA34BB5A716E536CE,
	PropertySceneHandle_GetBool_mE876BC9201F73F814AFF5F4A82C90CC02BEDD18C,
	PropertySceneHandle_SetBool_m43589AABBCE73E5BDF47E24EAADC309908CEF5DB,
	PropertySceneHandle_HasValidTransform_m4DFB6634221B060FB846A975C2AA76881CFA978B,
	PropertySceneHandle_IsBound_m75BC5C73C0C65F3967C0B2E6519ED4EF495E19DE,
	PropertySceneHandle_ResolveInternal_m9C4C80E37AB2178EDA3B31506BE2F56721E1BF3C,
	PropertySceneHandle_GetFloatInternal_mBD6D41B4F23687D5CEE7F1674E457C03BF4CF692,
	PropertySceneHandle_GetIntInternal_m8AC9D22272A7199D6BBCA877B036408D5975BC46,
	PropertySceneHandle_GetBoolInternal_m4714FA71EB7064EAFF5D40643989E41D057ABB75,
	PropertySceneHandle_HasValidTransform_Injected_m3561AC3CAF7D517E207BDB05E1F9ECE52082B822,
	PropertySceneHandle_IsBound_Injected_m98EAD11BDF0C2805F4524AC24FB13D00E5BBE21C,
	PropertySceneHandle_ResolveInternal_Injected_mA6D826D06AED57135DD0AA667F8EDDF49AC7FFFB,
	PropertySceneHandle_GetFloatInternal_Injected_mC6B0F0F768C5656C99B0C3B958F3540B99D8D9E0,
	PropertySceneHandle_GetIntInternal_Injected_m9A26C8FBB24DFEB662D2305FE49185EC79992F5A,
	PropertySceneHandle_GetBoolInternal_Injected_m80855977FA31E62257B376179C02E18EB577A8CE,
	AnimationSceneHandleUtility_ReadInts_mFC6B98B06607D4A175D4598B3D18EBB29E000C41,
	AnimationSceneHandleUtility_ReadFloats_m55F39914E47481B30592FA1DDB4354AF133F29EC,
	NULL,
	AnimationSceneHandleUtility_ReadSceneIntsInternal_m4D25EC39B7F2F147D646B83534EC3DE56B2FFE18,
	AnimationSceneHandleUtility_ReadSceneFloatsInternal_mDF52B54C3765CC9856DC4F5A5BF9D358D4D4312A,
	AnimationStreamHandleUtility_WriteInts_mA481ED34FED75546E0DBF8EC47266FC78AE80EC6,
	AnimationStreamHandleUtility_WriteFloats_m647C25053281C77FAA8F6BC29D54A96E5DDEFE63,
	AnimationStreamHandleUtility_ReadInts_m970637617496B33CB3F12995A9303BA38643B3AE,
	AnimationStreamHandleUtility_ReadFloats_m4F0751C5758AC2784E0BFB81788BC63411F7E3C4,
	AnimationStreamHandleUtility_ReadStreamIntsInternal_m0FF4C2321EB39B856E55FD1689892162BA681BD8,
	AnimationStreamHandleUtility_ReadStreamFloatsInternal_m6BAC0676E422F2C256C06AA0EA32F7B66D04F84C,
	AnimationStreamHandleUtility_WriteStreamIntsInternal_mFCD153B37ABA13A6B7F69CDFB62F44F47BC10FC2,
	AnimationStreamHandleUtility_WriteStreamFloatsInternal_m189497A194CA6D6F1EC649B83C6AA42476089649,
	AnimatorControllerPlayable_get_Null_mF129F956CBA0B632810CC49EB890C10759A0C85F,
	AnimatorControllerPlayable_Create_m2A01CF0D8E08995514A106B713E30021E39E8333,
	AnimatorControllerPlayable_CreateHandle_mEC5A01894B274C6EC5AD8FBD84688C29AFBCF698,
	AnimatorControllerPlayable__ctor_mBCB9475E2740BE1AEB94C08BAD14D51333258BFE,
	AnimatorControllerPlayable_GetHandle_m718D9A4E0DB7AC62947B1D09E47DBCD25C27AF6C,
	AnimatorControllerPlayable_SetHandle_mD86A3C0D03453FAF21903F7A52A743AB2DA6DED4,
	AnimatorControllerPlayable_op_Implicit_m728BDEDB8AA352D8535567FFEEA678E6DE300922,
	AnimatorControllerPlayable_op_Explicit_mE1F117CC1D3254DED4A465D2068C1A1A4EDDBAF3,
	AnimatorControllerPlayable_Equals_m14125BB4CCFCDFFD098223AF20E38501BA264180,
	AnimatorControllerPlayable_GetFloat_m787538C1B2ED9C4E52E8A3EEF43405913E82E895,
	AnimatorControllerPlayable_GetFloat_mDBD6169B671FCD0AA3551368A3EE37334E7D7B49,
	AnimatorControllerPlayable_SetFloat_m95BF662BF3AA8A4F4B5F868801DED66D397E2407,
	AnimatorControllerPlayable_SetFloat_mEDD694D72DDBEDF381973F41D83037F330224EDC,
	AnimatorControllerPlayable_GetBool_m30C0C093BA04B2C680E5B8510A189A66A4D5333D,
	AnimatorControllerPlayable_GetBool_m72BB4B2A95A2C229ADE9B51F7232B8FF4E5DF26E,
	AnimatorControllerPlayable_SetBool_mDBBE0C1A970D71F600234B15D0E6B9C51E7A70DC,
	AnimatorControllerPlayable_SetBool_mEBE969EAB2935C3A15848521B06ABB45B1188BAD,
	AnimatorControllerPlayable_GetInteger_mFE3ADA2A4AD9A7A62B60D7E5F2FCBCF9A5F58AA0,
	AnimatorControllerPlayable_GetInteger_m50B3042FDC1F87A4E0423FCCFEED611996A3860D,
	AnimatorControllerPlayable_SetInteger_m4C8B5F237C20CFABF0CAD460F55782D834444A91,
	AnimatorControllerPlayable_SetInteger_m6FA135B1C91BED0F97C9E623FB8C37411C6F15B4,
	AnimatorControllerPlayable_SetTrigger_m06D7662315ED85A8472E62C2F369B1167BAF51A7,
	AnimatorControllerPlayable_SetTrigger_mA2F8F838D48244468DACBF503E120FF3B06E95B2,
	AnimatorControllerPlayable_ResetTrigger_mD671335B03DF16D311287002303191D50B802D14,
	AnimatorControllerPlayable_ResetTrigger_mABE88FFA8781EB19F5B06F7BF483F13C61107D6C,
	AnimatorControllerPlayable_IsParameterControlledByCurve_m34F8D191A09BC28CCDF1A044FD02B33A1D64D0B1,
	AnimatorControllerPlayable_IsParameterControlledByCurve_m8ADD7EE7A489424B4B99253C96647E67B04E2D1B,
	AnimatorControllerPlayable_GetLayerCount_m3941A7DA375B26988A59DA13A22C08476D59D34F,
	AnimatorControllerPlayable_GetLayerName_m33BDF74B10C6BFDB82FF404E288F52F1EBABC809,
	AnimatorControllerPlayable_GetLayerIndex_m48AB42D7F82BE03576D4AC480F048EEDF01B0A0D,
	AnimatorControllerPlayable_GetLayerWeight_m09D5E7AB77824DE2DE20E4E4491BF2A90E3B28BB,
	AnimatorControllerPlayable_SetLayerWeight_mE432DA7CC2FC4425D0DA064B71B5ACCEB83F8EE3,
	AnimatorControllerPlayable_GetCurrentAnimatorStateInfo_mBA937DD74A4964C743880FFE7CFFE67B18D8264B,
	AnimatorControllerPlayable_GetNextAnimatorStateInfo_m1BD26B635F70840C13BC34D0632D82B5EA48CDE0,
	AnimatorControllerPlayable_GetAnimatorTransitionInfo_m5157FED95B567D4441228BC0F3AF31563FD5BF1C,
	AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mF89B409BFDD2B021BB6862B93A68879BCAB60076,
	AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mB103A1F9EC5E23947C8A1802D644FC4D22093F14,
	AnimatorControllerPlayable_GetNextAnimatorClipInfo_mF00B978E844FF7E6DF0AF670C0CF2022A8DD2517,
	AnimatorControllerPlayable_GetAnimatorClipInfoInternal_m18301A84A3ACAE2F7A34B0966564CD4F65D2BB2F,
	AnimatorControllerPlayable_GetCurrentAnimatorClipInfoCount_mB80BDB04DF276D53D45EAA549CAD88DA9A7E8BE8,
	AnimatorControllerPlayable_GetNextAnimatorClipInfoCount_m19CD5C12E89C22E3521175CD6C1E2F033C53D070,
	AnimatorControllerPlayable_GetNextAnimatorClipInfo_mD247136758432A4036FC338D4C8E67ECAF5EDD26,
	AnimatorControllerPlayable_IsInTransition_m041E31E9DAD976867BCCBDDD27CB556F590D61D9,
	AnimatorControllerPlayable_GetParameterCount_m899EE6DAD7209174D7568E632DA986D39E435A70,
	AnimatorControllerPlayable_GetParameter_mB7ECCC4E41138CC7D9A28ABF499679B8792E9CC3,
	AnimatorControllerPlayable_CrossFadeInFixedTime_mC4104110B90B6802409F8FC102AC5758250FAE74,
	AnimatorControllerPlayable_CrossFadeInFixedTime_mF66C581A902E013AF240151A4B9772F8DD94E95A,
	AnimatorControllerPlayable_CrossFadeInFixedTime_m43166ED8F80B94469DFA25374911A64637438674,
	AnimatorControllerPlayable_CrossFadeInFixedTime_m664DCD090D5E91612254A0F87AB5D2A538DF8DC5,
	AnimatorControllerPlayable_CrossFadeInFixedTime_m7901F79283F0C1357A14AE48D81BA663D98740C8,
	AnimatorControllerPlayable_CrossFadeInFixedTime_m93EC21F858FE76184FA91D2B6EA3B361A2929503,
	AnimatorControllerPlayable_CrossFade_m93543957867BB7F6B7A5498EF7ECDBAFCA0C295F,
	AnimatorControllerPlayable_CrossFade_m3B003ED0E44C2405B104B7E7ECF1AC0A6C15C7DB,
	AnimatorControllerPlayable_CrossFade_m8AD5C075E4B4B645796A36980AE15378167019AA,
	AnimatorControllerPlayable_CrossFade_m4CADD7D865B6C0A16D35B58011895E560CCF8824,
	AnimatorControllerPlayable_CrossFade_mC2E4C450F3E777DBE08BBBF0138FAA174D0526C5,
	AnimatorControllerPlayable_CrossFade_m0C777C0385BE3E8AA8D86C99897D490ED553F9DD,
	AnimatorControllerPlayable_PlayInFixedTime_m797B8A53344C62FF813DC398D1E6A6B18A826275,
	AnimatorControllerPlayable_PlayInFixedTime_mA9FA57E1D8B5B4DB655F1918C338585573B7DEEA,
	AnimatorControllerPlayable_PlayInFixedTime_mE4DAC931BFEDBCAABE0D410BE3DF85C5C4FF1425,
	AnimatorControllerPlayable_PlayInFixedTime_m37882EB28A1E75354814AB9EB6426D7F3FF8D985,
	AnimatorControllerPlayable_PlayInFixedTime_m62E44D644C7785CFF04229D6EA808F9C670FF010,
	AnimatorControllerPlayable_PlayInFixedTime_m513E1A9D864FCC8DF9E16A9DE05465A5694ACE2B,
	AnimatorControllerPlayable_Play_mD967CAA3998D88A9116C165D1D1ADDEEC8D3FEFF,
	AnimatorControllerPlayable_Play_mA3346301B00AD22D27B692BE36E560569FCD2765,
	AnimatorControllerPlayable_Play_m703A612D5040F4F99C17D300E5BD57ABB992E976,
	AnimatorControllerPlayable_Play_m3C33626F6950B0CD33EB39A87C93819B6CC9C52D,
	AnimatorControllerPlayable_Play_m252547934FE64DCF1DC9D3242E20200A0E8852D1,
	AnimatorControllerPlayable_Play_mB14FF22BE5BF41EB807BD3F14C111A3D60E59394,
	AnimatorControllerPlayable_HasState_mE537ED9F84D34939019463D4A2F6171B053759C2,
	AnimatorControllerPlayable_ResolveHash_mF09713D683CEF1F188415CE3868CF117B04FA322,
	AnimatorControllerPlayable_CreateHandleInternal_mF52B5F176A31C938DF5909127B27B1E78E3863D4,
	AnimatorControllerPlayable_GetAnimatorControllerInternal_m31844CB21CF7B71C2A3C3F7C29F37ACC70C91BCD,
	AnimatorControllerPlayable_GetLayerCountInternal_m1E9E07012C508DF0D452D350F658AB897B2C7C76,
	AnimatorControllerPlayable_GetLayerNameInternal_m29B1F15DB851564CA5D4D505F71636FBCAD787B9,
	AnimatorControllerPlayable_GetLayerIndexInternal_mF8B5003325DC63AA1E35959BBC77F54BA5FB69B6,
	AnimatorControllerPlayable_GetLayerWeightInternal_m35F8593E7F54FF389010F2C2C202662F78A4D076,
	AnimatorControllerPlayable_SetLayerWeightInternal_m35738E7223F0B22A86DAA2BF2066132E25FD1B8B,
	AnimatorControllerPlayable_GetCurrentAnimatorStateInfoInternal_m9EBFBAF82969F46703B6F9C15F42C29CB963063E,
	AnimatorControllerPlayable_GetNextAnimatorStateInfoInternal_m6573FAB9FB964669D28460771FACBADFC300FC3E,
	AnimatorControllerPlayable_GetAnimatorTransitionInfoInternal_m02EDD789D0AF671B56CC16D7172FDE8193A1C469,
	AnimatorControllerPlayable_GetCurrentAnimatorClipInfoInternal_mACAEC7A2BCB3EE6DCE40A1608DA3F466E4A9BEEF,
	AnimatorControllerPlayable_GetAnimatorClipInfoCountInternal_m30ACC99D56EF6C81325F21B16538A70689A4F712,
	AnimatorControllerPlayable_GetNextAnimatorClipInfoInternal_m9FE0E9288A890CDF52C9F825C619188BBFEB7F6C,
	AnimatorControllerPlayable_ResolveHashInternal_m1C4C380AF316FDF38E6099F7A91E9366CCACCE58,
	AnimatorControllerPlayable_IsInTransitionInternal_m0DA12D5A39272162175FC51E47458871BB8B4E42,
	AnimatorControllerPlayable_GetParameterInternal_mD6516C314E1BAF64567E613D9F31975C6046C647,
	AnimatorControllerPlayable_GetParameterCountInternal_m382C6BFCEE151B9DF3DFE232B287D6C6D522DD8D,
	AnimatorControllerPlayable_StringToHash_m75E3FCE65BF77734C4781FADFFC9568B0EE483E2,
	AnimatorControllerPlayable_CrossFadeInFixedTimeInternal_m2F3C0BAE59468B079106B730F8F0EE9874928561,
	AnimatorControllerPlayable_CrossFadeInternal_m4861EAD661B4EA490CDAF4413F8D6E1F1EF6F6D2,
	AnimatorControllerPlayable_PlayInFixedTimeInternal_mC0683AD548C14DA24F82388F68150F174275E376,
	AnimatorControllerPlayable_PlayInternal_mD5A3AAC8CFE034C5D8FA9CC26AC9CAA364145524,
	AnimatorControllerPlayable_HasStateInternal_m1D49854E9E6AFAC2DFE979DFF6911B6946B5555A,
	AnimatorControllerPlayable_SetFloatString_m0F0870A5299A2CF4AFC6C65832D189A5D04D4FD5,
	AnimatorControllerPlayable_SetFloatID_m3BA4ADD5B318BFF0294EB9ACA58480A66168297A,
	AnimatorControllerPlayable_GetFloatString_m1DADE177E526468221C892871355B7E0371DC7B5,
	AnimatorControllerPlayable_GetFloatID_m4EDF8B5F43B2F67DDCF3A566C520E99AF92316E0,
	AnimatorControllerPlayable_SetBoolString_m25BA69DB53D2B741C3A3816F52F2D8B8DB94A563,
	AnimatorControllerPlayable_SetBoolID_m9D7D23783C516EBA887139E4F3C2089D4F6630C9,
	AnimatorControllerPlayable_GetBoolString_mECF14FDF6E8B96688869FD00C1CC5F91C54EAB16,
	AnimatorControllerPlayable_GetBoolID_m7061D395F3200F2128DC6320515789ECB1350D4F,
	AnimatorControllerPlayable_SetIntegerString_mDC746DB1E487E0A5FB8258FB1FB697D3FEB14579,
	AnimatorControllerPlayable_SetIntegerID_m683204581C2F1791D94A53D8E9E81C8C4AA25860,
	AnimatorControllerPlayable_GetIntegerString_mD0C70C8553191AD4B4367CA0C1C7892097157713,
	AnimatorControllerPlayable_GetIntegerID_m364BCCFECB516503043592BCE6093B6E71573901,
	AnimatorControllerPlayable_SetTriggerString_m0EC136890F3D7FC3CCE31D7D97406AEC14BA96A2,
	AnimatorControllerPlayable_SetTriggerID_mB0D1B0D7CFAEA440E8582C322215E5F8843BBFF3,
	AnimatorControllerPlayable_ResetTriggerString_mC531FDD220C61491E53D609F021DE45AB0A517E8,
	AnimatorControllerPlayable_ResetTriggerID_m4FB3CD3245F90046AEBEB7DAC46F4ED8A17C2E70,
	AnimatorControllerPlayable_IsParameterControlledByCurveString_m9E408D31C0E277E3B81355622EA263D0A889FFBB,
	AnimatorControllerPlayable_IsParameterControlledByCurveID_m485E73DF5945A41CBD21A8F9ABDFBE9CDD0C07E6,
	AnimatorControllerPlayable__cctor_m88506D1B15D609B818DFDC6B2BCFF42ABB41B090,
	AnimatorControllerPlayable_CreateHandleInternal_Injected_mCBD001EF18F0143DD0568A9D5FBAF157206ED21D,
	AnimatorControllerPlayable_GetCurrentAnimatorStateInfoInternal_Injected_m7B608D6B42D37184F7C778E9DEBCD77B350923AD,
	AnimatorControllerPlayable_GetNextAnimatorStateInfoInternal_Injected_m6939850E0366960A04B5B3009220B1AAC6F77D0F,
	AnimatorControllerPlayable_GetAnimatorTransitionInfoInternal_Injected_m6CFF884FDBEF401BA13CAA1F9FA68EFC56B62870,
	AnimatorJobExtensions_AddJobDependency_m31CDA4A7972693AB5D702648FF5974C1A1A8665C,
	AnimatorJobExtensions_BindStreamTransform_m9E23872D781BC0754909267ED356266392F4E26D,
	AnimatorJobExtensions_BindStreamProperty_m199C36204F072AECF047AEFC92B1A62A61FF2F7F,
	AnimatorJobExtensions_BindCustomStreamProperty_mB19AC2FE9BCD4C98DA571A375080E17A114D374C,
	AnimatorJobExtensions_BindStreamProperty_m02E8A9116C4DB619A2B4E28AEADC593ECDE78994,
	AnimatorJobExtensions_BindSceneTransform_m78A489DBF2ED9127F74F35C33CD9C712AC73DE76,
	AnimatorJobExtensions_BindSceneProperty_mB75521D482FE6417947F331C8831AB67B7B6E8B9,
	AnimatorJobExtensions_BindSceneProperty_m5D7AEB5EEDBC08245FDBE69BF129BF0CE66B7743,
	AnimatorJobExtensions_OpenAnimationStream_mA4B55DC4D4BDF17E29D7805FB3D0A8645007BEF3,
	AnimatorJobExtensions_CloseAnimationStream_mE9E79A50871A9BB08D077BCD4350005246F23516,
	AnimatorJobExtensions_ResolveAllStreamHandles_m189D44E0381884741BC18CDFCE77F41C09345A9E,
	AnimatorJobExtensions_ResolveAllSceneHandles_mD59455EAF32FBF555C8C02FB55A0312795C045E8,
	AnimatorJobExtensions_UnbindAllHandles_m7D7D00929C0F88E88BA68441C7027F2B66823B56,
	AnimatorJobExtensions_UnbindAllStreamHandles_m12CFFA21A9E9FFCF3CACF885F42DBDBD6C19585F,
	AnimatorJobExtensions_UnbindAllSceneHandles_m9DE9F0298845F4485B2A1FF8DAE7EEB60CF61262,
	AnimatorJobExtensions_InternalAddJobDependency_m71BA59576E0FAA88A5C60F7211D172BF2DE24B03,
	AnimatorJobExtensions_InternalBindStreamTransform_m83A49A8A13E2FA7AD67FAECA1B24F11D15550C2F,
	AnimatorJobExtensions_InternalBindStreamProperty_mE09521CB89443739B6428A0FD6B012D8C0601415,
	AnimatorJobExtensions_InternalBindCustomStreamProperty_mFD263ACFEF7E93728830EFD5133399F922EF6504,
	AnimatorJobExtensions_InternalBindSceneTransform_m5C33CC9DC1C95933DA91E42411080A4DA49216C6,
	AnimatorJobExtensions_InternalBindSceneProperty_m7C4D6BE064BBE342314E1BCD6101F451794E0A11,
	AnimatorJobExtensions_InternalOpenAnimationStream_mCC05CB24B2F24F6B3E2DC4797F7F5AD9C02427D8,
	AnimatorJobExtensions_InternalCloseAnimationStream_m639A895B6E57F3005799C0C1D56E5CA415AF39D3,
	AnimatorJobExtensions_InternalResolveAllStreamHandles_m9FC0F152F03B51A446535E28F4761F5C136AAC31,
	AnimatorJobExtensions_InternalResolveAllSceneHandles_m94621370D35BB9025DE191C0B448159D25CE19CE,
	AnimatorJobExtensions_InternalUnbindAllStreamHandles_m7D6642F5D90653F937352C073EF8B8BC7CC839E6,
	AnimatorJobExtensions_InternalUnbindAllSceneHandles_m1E802D79857566FEEE9C27B938D3F688043D992A,
	AnimatorJobExtensions_InternalAddJobDependency_Injected_m8798C8F3B7A272A86C5A3E7437977CF71FDFAEEE,
	ConstraintSource_get_sourceTransform_m0A9C0C96F68398626E43F2137E2CAB5BA803F94F,
	ConstraintSource_set_sourceTransform_mC905D9E04293D785BA40E1E0378457A77170A31B,
	ConstraintSource_get_weight_m90743C7AB9BA12A99FB8C81442E765A4F947BE28,
	ConstraintSource_set_weight_m40EADC470F7D906EEB89A515F75CC8B0648368D7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PositionConstraint__ctor_m625C24233306DDBC2D80F9738952F377E8E97500,
	PositionConstraint_Internal_Create_mB13A191DEA183B3DD5895B3A7DFBEF2807CF9129,
	PositionConstraint_get_weight_m4600BDC603A83F8EC429BDE05F4194F2C8A0299D,
	PositionConstraint_set_weight_mA5D54C3164C45396C5BAF7309C90CE6626B3E7C8,
	PositionConstraint_get_translationAtRest_m4E7168580A88DB7FDF1D5D7DBE243E80F2D80046,
	PositionConstraint_set_translationAtRest_m974249528D567468847532DC9CCECF90E7825131,
	PositionConstraint_get_translationOffset_m7DAE61CD823E25EA20B2CFFF47BE2B3C463610B2,
	PositionConstraint_set_translationOffset_m296C3070AFA239658481646267023AD85778A4BE,
	PositionConstraint_get_translationAxis_mE9F51805A4AB7125CA95721F7473873DB550ABB0,
	PositionConstraint_set_translationAxis_m045D55B2A505B7FDDBC889E6364B78BEB5DEC812,
	PositionConstraint_get_constraintActive_m290BD123E800C32336E0C9FA3E7D1A3F6ADF9662,
	PositionConstraint_set_constraintActive_m325FCFC0950DCC95804662D344A26AAA2BCF579E,
	PositionConstraint_get_locked_mCB288BD466038AF07BF0C9B7CD4CBD365C1E8945,
	PositionConstraint_set_locked_mAEA85B37FC6DDC14F25324060C163566CC4E5F3C,
	PositionConstraint_get_sourceCount_m5459C2629024A7FAC52A52385BEFCEE922369228,
	PositionConstraint_GetSourceCountInternal_m3477DAD40AAA08EB469A5B2D4D27871532BEE0F4,
	PositionConstraint_GetSources_m58D7A3BB891A29C00467A900BD29BB3D0E442753,
	PositionConstraint_SetSources_mB62ED52943DF3E6C360CCCA86BCBB36AA5283CAC,
	PositionConstraint_SetSourcesInternal_m1B0ECA3AAE218EB0F49A3DC5DFBAC71040366207,
	PositionConstraint_AddSource_m88074D53713F6804955EA339B91152A75429AFE6,
	PositionConstraint_RemoveSource_m45377027910F7B683BE3E5B2F7DC09AE3CB25C7C,
	PositionConstraint_RemoveSourceInternal_m3A823DC00C2E15CE38E91CC1F06C5AF001DCE1D1,
	PositionConstraint_GetSource_m845F32F9A6AE984D239AFA5CD0B2942403D16C1C,
	PositionConstraint_GetSourceInternal_m526B9EEDEC7672851B52AFF42C6AD7E5C1D02DBE,
	PositionConstraint_SetSource_m0B987AD5624AE45C3671E7671D53E1373023E6B4,
	PositionConstraint_SetSourceInternal_mC89EE76071C107C8DEAEBE257BAD2FE9D71BB8CF,
	PositionConstraint_ValidateSourceIndex_m536CE4DA81C077887BCEA71634F1CAE8E7DA08C5,
	PositionConstraint_get_translationAtRest_Injected_m761A40080D8AA5E7524B638364F4040FFDBF764F,
	PositionConstraint_set_translationAtRest_Injected_mAD837585A4E08DC208DF3584EFECCDEE668FBAC6,
	PositionConstraint_get_translationOffset_Injected_m6826C01E0F3D94DB82667F2155795BBB9388EECC,
	PositionConstraint_set_translationOffset_Injected_mA052DE0C5006E39D4918EE61C28EFD7DAAD63A87,
	PositionConstraint_AddSource_Injected_m9F815DDAFA4EE6CD1756FE7383E37C909DC7EBCF,
	PositionConstraint_GetSourceInternal_Injected_m4354DBD9864B8D70FF219C64A7B1D1F80A6A252E,
	PositionConstraint_SetSourceInternal_Injected_m707DD39B1E6851D98B3C35F73FD42473EB1D9AD6,
	RotationConstraint__ctor_m0C440527A1423E3CBC75F1737CB7B57F59A054C9,
	RotationConstraint_Internal_Create_m17D7461540706218FD0A911A4D021B7F93E03B70,
	RotationConstraint_get_weight_mE257D80144388C59E4F8765B645E28C694BF69F5,
	RotationConstraint_set_weight_mEB48E254F6592BA6E3027B7036D185F13502C3F2,
	RotationConstraint_get_rotationAtRest_m516517904E682810077927F2C65120524772EE1A,
	RotationConstraint_set_rotationAtRest_m6D64E9A3904510D40770DF9D418862723B9FFA91,
	RotationConstraint_get_rotationOffset_m825C20481CF7084A6A91E8706B32343B503F390A,
	RotationConstraint_set_rotationOffset_m028C803BEA3CAC32B18DF71E5A52942E8A80D08B,
	RotationConstraint_get_rotationAxis_m6C88CE69CECA2943309DB1C2CA6BECAB98D02D42,
	RotationConstraint_set_rotationAxis_mAC05428F3AF6871C239F340931D44064DFF34170,
	RotationConstraint_get_constraintActive_mECFCC2877BEC6F1CE962AC670002F8301C5B5860,
	RotationConstraint_set_constraintActive_m4B764D283A56068B70D0E00FD37AAC0BDBF0CDEA,
	RotationConstraint_get_locked_m4DF430D48E85047173520BC41CA90918051354DC,
	RotationConstraint_set_locked_m7C55E40B8D4F42DCE2FA747E1C56AA719AE613A7,
	RotationConstraint_get_sourceCount_mE3B2D13FFE49F98A845FDF4A8B57BE81757BD579,
	RotationConstraint_GetSourceCountInternal_m9C9CEBB382332A72C013F5302CB991F78AE8FB1B,
	RotationConstraint_GetSources_mF2B536A36D4C4B9DEEACFC1A9ACE1375A77EF9F2,
	RotationConstraint_SetSources_m22CC7C29C33738E3CFFBBD18FB7DBB66B52A0965,
	RotationConstraint_SetSourcesInternal_m8EA458F4FA7204BAD7A76B6375B01725B3F931EB,
	RotationConstraint_AddSource_m95354FA9CB0789975527F0380EBADFBCE3981E75,
	RotationConstraint_RemoveSource_m5EBEFEA6163B25303A37B3A63294E2AFADCD69F9,
	RotationConstraint_RemoveSourceInternal_m33A48A4951998F7F5921E19ADA3B4971FAF6D138,
	RotationConstraint_GetSource_m5A7BFD4CD25C48E1FFA702070B60CDF513AD4C2B,
	RotationConstraint_GetSourceInternal_m07E39771BC5B72C1988F778D152617C467D92D73,
	RotationConstraint_SetSource_mBEF215D44BDD2CBE54BD26C85234EC2E65AD0036,
	RotationConstraint_SetSourceInternal_m4865E43F4F1928FE244A5EEE2F1547C3A3C72C86,
	RotationConstraint_ValidateSourceIndex_mF6D796A860D909EAA1E9E83AB5D5421207E9300B,
	RotationConstraint_get_rotationAtRest_Injected_mBABDCA5888B414CC014350F517191F6D42F693CE,
	RotationConstraint_set_rotationAtRest_Injected_m78AB11DDFE7D9AAAF53FFB211DD7389619129A44,
	RotationConstraint_get_rotationOffset_Injected_m39761F88A1DE102CBA99AA06A0A0F70B71EFC99B,
	RotationConstraint_set_rotationOffset_Injected_m0FE67D3A9A1B30F1AA112B89C3B40274CB8179D7,
	RotationConstraint_AddSource_Injected_mF7B857F14490C64AFD45E75AD40BED3D190C981F,
	RotationConstraint_GetSourceInternal_Injected_m6F23903777F31156F285232B226294342CCE19EB,
	RotationConstraint_SetSourceInternal_Injected_mB88DCEE97E95A1E055226A5887E1FB05AA13763F,
	ScaleConstraint__ctor_m48A3D2F0576E661835C3BB47709F355598CDC024,
	ScaleConstraint_Internal_Create_m9B62351F8883B89652BFD5E267DF2DCA9AE9E634,
	ScaleConstraint_get_weight_m22C103255CC4B6DED7406A88D212B8112CDB0CEF,
	ScaleConstraint_set_weight_m9A275D46B5BD2F8730F5142582461FB0C4DFA621,
	ScaleConstraint_get_scaleAtRest_m15CEF6355710CBE97CACE3134B930096EED7AC91,
	ScaleConstraint_set_scaleAtRest_m445F99838075F0162CE3B6565B011EE5F53F5D6F,
	ScaleConstraint_get_scaleOffset_m1CA45FEBDB738B65CF622A3F5A509471C58F8035,
	ScaleConstraint_set_scaleOffset_m0A1A10E986458C91F3D7D9EABFFB3A2986B988E2,
	ScaleConstraint_get_scalingAxis_m8BF0878A2F6958B858A99E3CA1E9EFB13CB678F7,
	ScaleConstraint_set_scalingAxis_m1F358A26D7326902F35F089DB5CC72590356EC03,
	ScaleConstraint_get_constraintActive_mB43F9CD1877CE8AF923C0B1382A5465603BBA491,
	ScaleConstraint_set_constraintActive_mD736564D7D23F3FBC2E926B54E4DD2618724DA2D,
	ScaleConstraint_get_locked_m53CF2C1E99E4BC7A5E169BC31C8D588B2E7EBABA,
	ScaleConstraint_set_locked_m0316BD5EFD201E9AB5C186BB1676B265C59F09F9,
	ScaleConstraint_get_sourceCount_mE246DEFC9CE00F8B28EDBEE8E486A5BBAACEB2FB,
	ScaleConstraint_GetSourceCountInternal_mB5231888EEF86850C0277DD9E7EECB65EFF54F00,
	ScaleConstraint_GetSources_m5AB8F651508460DE2CC7D4FEACEA0183ABA45F1B,
	ScaleConstraint_SetSources_mC1ABAA1CC399F68F87EBDFAE00D3469742803207,
	ScaleConstraint_SetSourcesInternal_mFB26F6DE285F0CDD3D6DF3306159857D3372D70F,
	ScaleConstraint_AddSource_mE9DB31F6824FFFA9D77CB16F198F5EF2BC48CAA6,
	ScaleConstraint_RemoveSource_m75C969BF4A1286E546C2A3C94847171104E8100A,
	ScaleConstraint_RemoveSourceInternal_mEC4AC92F6BF6B1376039B712F9B81EDBD38BB051,
	ScaleConstraint_GetSource_m8F50080A0A79D9C261C3C30060078115D6ACFD43,
	ScaleConstraint_GetSourceInternal_m5E5A63CD8689D25172824717358E2C7BEB12CC16,
	ScaleConstraint_SetSource_mCEC1F61D9836BB9589C66569A6CF7F178DF8337E,
	ScaleConstraint_SetSourceInternal_mC4E8DC2196DD7CED0A8087389BABDB7D5DEA752C,
	ScaleConstraint_ValidateSourceIndex_m4FBBED869E411A5C8D99CF2A7DE4DC67107620FA,
	ScaleConstraint_get_scaleAtRest_Injected_m707202A9D65334591BE692EF0F5EED239754890D,
	ScaleConstraint_set_scaleAtRest_Injected_m9B4866F17F90B6D3CCF48E26BF58C9A4A732D7E2,
	ScaleConstraint_get_scaleOffset_Injected_m8A7C63E4AB8679F206E72B255DF0F9898C2E9E11,
	ScaleConstraint_set_scaleOffset_Injected_mA82A306F23677BE38FDEA74B39AC38230BBF5090,
	ScaleConstraint_AddSource_Injected_mB3190E6AA5E82340524BA01ED985CEE13C7B2099,
	ScaleConstraint_GetSourceInternal_Injected_mBFCC0E46AE1C071C74607F1569A46606955FFE18,
	ScaleConstraint_SetSourceInternal_Injected_m9146D0D39797CA9DB389F188718294DF23D5D597,
	LookAtConstraint__ctor_mB3E8E0FFD083929C7AC31C974AF21D48D988797F,
	LookAtConstraint_Internal_Create_m73B11C7E321159279EF4AB244AD5927BA39E52FE,
	LookAtConstraint_get_weight_m5292EC5432FEA8234384204887646D2724A88611,
	LookAtConstraint_set_weight_m825136F6F09B0B3014FB71B0104BB8CB138F50A0,
	LookAtConstraint_get_roll_m98DBA48F03550A9D6A20D9A43242024634B2F5B1,
	LookAtConstraint_set_roll_m62A39E55A380ED4B45C3ADE3E1F38F88A8FF4DE3,
	LookAtConstraint_get_constraintActive_m2F9EF3AF5A43D1BAEEEF842C436BCE744488DD65,
	LookAtConstraint_set_constraintActive_m00CCD8C06EE6B997DBDA4AE7D1785CE6DF7099C4,
	LookAtConstraint_get_locked_m09C34596BA18102BC50E396046B4682668A4934F,
	LookAtConstraint_set_locked_mFD206AAC60BE8E54AE0C87B4BA4F5AD6A9485785,
	LookAtConstraint_get_rotationAtRest_m87C23A301C0438133FF195F4A915B3B6316E1389,
	LookAtConstraint_set_rotationAtRest_m56551096C25D8E80802894A85B3BBC090683665D,
	LookAtConstraint_get_rotationOffset_mF4E9EA22373E0382B4B7BFBE3259A4D896C1BA1E,
	LookAtConstraint_set_rotationOffset_m1DB153DD6285FC69AB3F83D5DB85941A665D400E,
	LookAtConstraint_get_worldUpObject_mF7CD986A7D5B657C4249E90E8CED47E685780CE0,
	LookAtConstraint_set_worldUpObject_m79D273E357F3DF3542BC66B3EE076700EAC5FD72,
	LookAtConstraint_get_useUpObject_mD2CF1369D6E024B8F27F8053F88C99CBEEBAAF94,
	LookAtConstraint_set_useUpObject_m63568AC6E7173EEE9CDEA1B8C2BD4BAC7EBD3AD9,
	LookAtConstraint_get_sourceCount_mBD0921A486A9BD25813B68218B5E620268D6348B,
	LookAtConstraint_GetSourceCountInternal_m6C3739F0ADB867B78D5BEC961FE7E79D60E49A45,
	LookAtConstraint_GetSources_mAC4256AC1FBC36B7A32DEBE74851F5803898F47D,
	LookAtConstraint_SetSources_m331DB216D4DD9A3D4506C302B3CF934559D75553,
	LookAtConstraint_SetSourcesInternal_mDDF9B75472D8E0EAD24D3AD670A8A585D304D29A,
	LookAtConstraint_AddSource_m8F35E16F3998CCDA5FBF1E258B6852C91E4E385E,
	LookAtConstraint_RemoveSource_mD79139F9695B320C2B2BDEE80154DB33CDB73C8C,
	LookAtConstraint_RemoveSourceInternal_m4E71E09F60399F69D03D7C5BBFBA7D1064DDE6E9,
	LookAtConstraint_GetSource_m49CCA6341B7FDABD119DD4253AA5B56CFF14D3B9,
	LookAtConstraint_GetSourceInternal_m7CA976A8DA13FF0A1A7C60CADF14D2F20FE770DE,
	LookAtConstraint_SetSource_m9E09AD5950F3B28B03EDC37DE9202E8EB1BF3DDE,
	LookAtConstraint_SetSourceInternal_m337F95FBEC45A95DC6F3D60E1D868B0F95DDA5DE,
	LookAtConstraint_ValidateSourceIndex_m9FA564FC7B8C2A6E0E885D1F6016EF924F5FC294,
	LookAtConstraint_get_rotationAtRest_Injected_mF379C7B0864AD672A0EBB4B9F57B3C9619838B86,
	LookAtConstraint_set_rotationAtRest_Injected_m3208C9F250CDB9882CF45DF68A75570F324468AB,
	LookAtConstraint_get_rotationOffset_Injected_mEBA6896533315E281E84C85E420435FE3ABC3C4F,
	LookAtConstraint_set_rotationOffset_Injected_m933EEA999F3A5F2104AE288DDF536A813F39307E,
	LookAtConstraint_AddSource_Injected_mCB360869E18E6DD98F0BB07CD9D6AE3C4053968D,
	LookAtConstraint_GetSourceInternal_Injected_m2DF357FF91D10539DFB9B123021E4D4FD9C3B21A,
	LookAtConstraint_SetSourceInternal_Injected_m72A87537C80EAB436B368377AC902B666DE41448,
	MuscleHandle_get_humanPartDof_m8262C622AA62DC780CCA30CB5492F1235C45A505,
	MuscleHandle_set_humanPartDof_m11EE483E985240C49307DF184DAE8B3260945BCE,
	MuscleHandle_get_dof_mFD825C91413A42221BD937827E3C743C8A776598,
	MuscleHandle_set_dof_mB202EB7F0DE1EB8E0E3E84349914EA26F48EAA93,
	MuscleHandle__ctor_m639F42D1909646875E9AC30B3394498060E5ACD6,
	MuscleHandle__ctor_mBE2F28D04718BC0EF94766966956ECCC980889CA,
	MuscleHandle__ctor_m4A0A680BBA254F55AE88CBCA6A0B0A50A0E143C6,
	MuscleHandle__ctor_mF082607DD932A653DABE2DE3DF4C624AAEB174F3,
	MuscleHandle__ctor_m0103CC894F5E0BD46FD5FF9A0F20BBA11873AB6E,
	MuscleHandle_get_name_m298104348CF7CF2ED690FDB26039D283D9629174,
	MuscleHandle_get_muscleHandleCount_mFAEA121C96CB067CFAE8BF988C638B7D37D862B8,
	MuscleHandle_GetMuscleHandles_m8AABE04A8810F0A689605A9C52D939D5266D4B5E,
	MuscleHandle_GetName_m229299540D61FB707555C9C2D254DC7448B7AC22,
	MuscleHandle_GetMuscleHandleCount_m438E7D7CD62C5524B4C8F5A53B74D3B975142C94,
	MuscleHandle_GetName_Injected_m07CDFBFCBD22D6C7663BCA8713C76D5A4F95DF88,
	ParentConstraint__ctor_mBADF790001F2A71F65EE60022BFB4B66E3BCB1CC,
	ParentConstraint_Internal_Create_m6331C06E8B005A7B43E45F37982884A24FE7A56D,
	ParentConstraint_get_weight_m8C1A0FC97FE83E60D07C118185331F270D747EF7,
	ParentConstraint_set_weight_mB768A3F4BF4515FC446F2E63C86A3E14A60F4AE8,
	ParentConstraint_get_constraintActive_m9FB6D303FA0B9B5CD6A8DB9B6DDFA51D5BA20C4D,
	ParentConstraint_set_constraintActive_m366F11DA9455E241BEF400642A8F0E5CE0B49F89,
	ParentConstraint_get_locked_m053B77ECE30540156C6199112E92CCA5C97DC3B6,
	ParentConstraint_set_locked_m1C8EDE9846FDE72DA67A6781FC13AA1110278AFB,
	ParentConstraint_get_sourceCount_m6D83D678E00CFBC77A4626EB9B904C59C24FFBCB,
	ParentConstraint_GetSourceCountInternal_mB5BC92E4C164357647E292EF2D52C37683FD24EB,
	ParentConstraint_get_translationAtRest_m16DA060F2BB48564C43510095EF679F2AFF24391,
	ParentConstraint_set_translationAtRest_m8F2DE7E19EC96BFFAEDDD1278222A7AC8FBDFAF2,
	ParentConstraint_get_rotationAtRest_m892940524B77396D70107E17690E6A795F75F9AF,
	ParentConstraint_set_rotationAtRest_m92ABAC02A41399B51D029658ECDCD35A45EFB5C9,
	ParentConstraint_get_translationOffsets_m6815186B47121DF699835E85BE0A4FF6E745058B,
	ParentConstraint_set_translationOffsets_m846750075B774569D7248F249F3EACF8416F9F4A,
	ParentConstraint_get_rotationOffsets_m11D07DC9EB3DD9F1474B878855A107C583C45E4C,
	ParentConstraint_set_rotationOffsets_mD179E830233901E07E9C5EAAE80B5E341CAFA98C,
	ParentConstraint_get_translationAxis_m234D8BC12FCFF37A232E73FBAEA6872F3689BC89,
	ParentConstraint_set_translationAxis_mE285A4B0590711E0546D95CC557CDCA9F4829948,
	ParentConstraint_get_rotationAxis_m5860389AC94E07864C0541F06E3F74ADB96A0354,
	ParentConstraint_set_rotationAxis_m44AE458EC9239A67A186898128D7CCB688E20847,
	ParentConstraint_GetTranslationOffset_m7E65A4E612044326B2FA269032F6E550EACA4BFC,
	ParentConstraint_SetTranslationOffset_mFD4350AE123DE476B284219935CB52DE1544BF15,
	ParentConstraint_GetTranslationOffsetInternal_m969D0E965C427D6C6A68BF93273532198E3754D0,
	ParentConstraint_SetTranslationOffsetInternal_m9E22067F8904B87016C803380E24F266FDBFA01D,
	ParentConstraint_GetRotationOffset_m9D529F720B85A2ABEED9348FDDAE8FCFA8C4EE1E,
	ParentConstraint_SetRotationOffset_m8C346F0ECB6FF54DE74D3170229AD05522FB99AA,
	ParentConstraint_GetRotationOffsetInternal_mC82486490AE3FE3E1E36CBCF9995268972997A2E,
	ParentConstraint_SetRotationOffsetInternal_mC149045D55A9824067299E474112E61D0EAA2E14,
	ParentConstraint_ValidateSourceIndex_m081826C60AECFBDBCA08F338D069837D0D75C49B,
	ParentConstraint_GetSources_m61780D428C67FA8FE4A76602A318C8CFC608503E,
	ParentConstraint_SetSources_m28BD51533D5A03FFEB7FD4946E34ECC22202D13F,
	ParentConstraint_SetSourcesInternal_m1ECB5E5FC1CF5A27F902BF026FC055EA86541E70,
	ParentConstraint_AddSource_m37997059B86A1DB5EFD8E94F7EA754120DF99194,
	ParentConstraint_RemoveSource_mDFEEB144F845AA25DDAC42331E93A792890D72CA,
	ParentConstraint_RemoveSourceInternal_m440F960F632129BE2E0D52E3AB1EFD1A44CD7B00,
	ParentConstraint_GetSource_m48E2619DA020D365DA9FEC0846519EF01B3F7153,
	ParentConstraint_GetSourceInternal_m7CB0AADF3B72B57C69A694770B3A02667F0A16B3,
	ParentConstraint_SetSource_mE99D06ABA6405A9DF14E67FBF186E186769604B6,
	ParentConstraint_SetSourceInternal_m351C44976FAC286B57CEA36229365075C52F3E24,
	ParentConstraint_get_translationAtRest_Injected_m89D17C88F19CA6CADAE95347B74420B6102A374C,
	ParentConstraint_set_translationAtRest_Injected_mF2859A67B8BC8F0242FBCCF636ABE44AA949325A,
	ParentConstraint_get_rotationAtRest_Injected_mD6443232F13F365F114CE39E483A3BD061643A31,
	ParentConstraint_set_rotationAtRest_Injected_mB979711ED121CE98917B6C03DEC58CC327C5277D,
	ParentConstraint_GetTranslationOffsetInternal_Injected_mAF14ED7FC902D069BC3FE34A8D12BD159E7724E5,
	ParentConstraint_SetTranslationOffsetInternal_Injected_m3995DB6B0F0DB7740D1DAFB5ED3937F85117DE88,
	ParentConstraint_GetRotationOffsetInternal_Injected_m4543B4BF0FCC22B4600868FAC123A8AA16B64EF4,
	ParentConstraint_SetRotationOffsetInternal_Injected_mBABF60A9D803907CA7A629A84291937287F4A8B3,
	ParentConstraint_AddSource_Injected_m0A8CB716DCF35CAE4C614632A76B6897F87E3788,
	ParentConstraint_GetSourceInternal_Injected_mC0EB388BC00CF48E988B68EDC484BE25CA2E3309,
	ParentConstraint_SetSourceInternal_Injected_m8B29FF73ADDAF1B4858CE140B58FCFB5560F9305,
};
extern void AnimatorClipInfo_get_clip_m6205DB403EBEAEAC14DB8928FFC7EBC50142E1AC_AdjustorThunk (void);
extern void AnimatorClipInfo_get_weight_m1CC29E2C37B30993EFFD12161059E4AD86EE287D_AdjustorThunk (void);
extern void AnimatorStateInfo_IsName_mB936F493D6BDDB9372C8E9D813CE0416B002C4D0_AdjustorThunk (void);
extern void AnimatorStateInfo_get_fullPathHash_m583FA8FAAC28BF65A65166D100949833E515210F_AdjustorThunk (void);
extern void AnimatorStateInfo_get_nameHash_m88E91C33AA5602324A7319D7A51F552D00B14D4A_AdjustorThunk (void);
extern void AnimatorStateInfo_get_shortNameHash_mEE816B999C282A3BA95AFC64278B994E899B7004_AdjustorThunk (void);
extern void AnimatorStateInfo_get_normalizedTime_m087C7E5A72122ADF18EBB4AC8391103B9119CCC6_AdjustorThunk (void);
extern void AnimatorStateInfo_get_length_m2FAE317264F7C796427207F8F28E550DB49F9541_AdjustorThunk (void);
extern void AnimatorStateInfo_get_speed_m473826E53D827AEE36A7FF0662AC4817C2EF3495_AdjustorThunk (void);
extern void AnimatorStateInfo_get_speedMultiplier_mA078AADECC98C266C82E2756A351235DDC63A107_AdjustorThunk (void);
extern void AnimatorStateInfo_get_tagHash_m3F4738079576820B7D5942854882B2B468CDD55A_AdjustorThunk (void);
extern void AnimatorStateInfo_IsTag_m9A3181AA167702263EB283AF27B21D08EAD895EF_AdjustorThunk (void);
extern void AnimatorStateInfo_get_loop_m3DC728FC9AF0D4B27B3C28157395BB2F57CC3DA7_AdjustorThunk (void);
extern void AnimatorTransitionInfo_IsName_m6C0C8BBF7E241EFEE2199D5D97DC59958BFBE324_AdjustorThunk (void);
extern void AnimatorTransitionInfo_IsUserName_m91FDB1462C56FCDB3F9A209020D2014B06833DBE_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_fullPathHash_m3C358272C30F5AAE76547585AA6C3D866F6F77AE_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_nameHash_m31EC38373C30F6A8BADA9AD27EBAB6BC5B9185CE_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_userNameHash_mBAD5D1898CE34F1E0657D6BBEA18C4A35EC686EE_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_durationUnit_m7E41A2E75B4DBD836E1BC55FA933CF199203B7E1_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_duration_m15DB72ECD67D569CA85DA4CD46E4C92677BFF8B9_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_normalizedTime_m0D107F16FB8351EBB0E8F8A4367A69916E260072_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_anyState_mDF1EC0E1F99B7998D19720BC5AAE4B7A31499273_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_entry_m56D5DF6A01AFBAA8611505369333083DA652DB13_AdjustorThunk (void);
extern void AnimatorTransitionInfo_get_exit_m32F0BA8C87C7487F27A3BCE5A3B490DEB67AC80E_AdjustorThunk (void);
extern void MatchTargetWeightMask__ctor_m381E3F8A3BA8447D8E9CB084E785AB2CDD38B96B_AdjustorThunk (void);
extern void MatchTargetWeightMask_get_positionXYZWeight_mA92CEAD4501F10E0B8F1177C4A1C1B60A1CE644B_AdjustorThunk (void);
extern void MatchTargetWeightMask_set_positionXYZWeight_m59F98F90D7089D61AD3B375E788BEE5D78753E0B_AdjustorThunk (void);
extern void MatchTargetWeightMask_get_rotationWeight_m5FDC8E88D3A7E6DC0DD51F988F0F172FD8C84C88_AdjustorThunk (void);
extern void MatchTargetWeightMask_set_rotationWeight_m6E9398D9F0017122E85576DE6482A17E2C8A15D2_AdjustorThunk (void);
extern void SkeletonBone_get_transformModified_mF6EC8E089FEC8D75FB511B8F7E210B980169C05C_AdjustorThunk (void);
extern void SkeletonBone_set_transformModified_m4CA61CA5AE981BB303BBF41240A5AE6CA251FCA4_AdjustorThunk (void);
extern void HumanLimit_get_useDefaultValues_mA6C116B2DC3D800FDACE2907D52B85E632914677_AdjustorThunk (void);
extern void HumanLimit_set_useDefaultValues_m3BD1D01F9652270133D307C7709FDD554621ADE8_AdjustorThunk (void);
extern void HumanLimit_get_min_m12EAAB4E0EBBBBD221BDB213130DE9643906AB9D_AdjustorThunk (void);
extern void HumanLimit_set_min_mE1EFA9D3BBB3047BF25554696FEEFF1F218A7227_AdjustorThunk (void);
extern void HumanLimit_get_max_m4E5E907AE7FFFCAC65026ECA444507B0B608F02A_AdjustorThunk (void);
extern void HumanLimit_set_max_m68A852091164B5EA6CD138615DEC75EC9917DA78_AdjustorThunk (void);
extern void HumanLimit_get_center_m6F488F439245F5E54D0DFBE04CD63816BAFEFF6B_AdjustorThunk (void);
extern void HumanLimit_set_center_mC1C73D9F6B3EFADCF99E6906A0D464146F2FCDF8_AdjustorThunk (void);
extern void HumanLimit_get_axisLength_m9691117C17DFCC40ECB9C1A459CE998831678947_AdjustorThunk (void);
extern void HumanLimit_set_axisLength_m7DACA3E1AA03B9733E0C1D34051859A45D5B8FB3_AdjustorThunk (void);
extern void HumanBone_get_boneName_m09C4D3365F4D1E69CD0907DEC3A2298A2CF6E18B_AdjustorThunk (void);
extern void HumanBone_set_boneName_m22857CD9738A623436C8F7A31D51D1EBA4BD8F58_AdjustorThunk (void);
extern void HumanBone_get_humanName_mC5FF6D0EDE66B773EF7E6DD7722E20C07EBCDCF6_AdjustorThunk (void);
extern void HumanBone_set_humanName_m460C6620AEE45FC9601B8D05448DD6C397B12D4B_AdjustorThunk (void);
extern void HumanDescription_get_upperArmTwist_m92E2B3BCA433012179892DE9493CBC5F8ADB8D52_AdjustorThunk (void);
extern void HumanDescription_set_upperArmTwist_m4056390E30A30DCEB48D55D4E536F146E431E100_AdjustorThunk (void);
extern void HumanDescription_get_lowerArmTwist_m8F57099DCEAC2B1D0D03F5443F500953E1E086E6_AdjustorThunk (void);
extern void HumanDescription_set_lowerArmTwist_mD24F728854AC5AD545E09D6E74CDD0B7AD6DF139_AdjustorThunk (void);
extern void HumanDescription_get_upperLegTwist_mB9429634218671A47EAB4E73C4ABCB4EDC1FDCF1_AdjustorThunk (void);
extern void HumanDescription_set_upperLegTwist_m0552329018CAC469A2443BFBC89B83DFB5288782_AdjustorThunk (void);
extern void HumanDescription_get_lowerLegTwist_m8AB325E825835D0A05A20F447191CF2FBA604B35_AdjustorThunk (void);
extern void HumanDescription_set_lowerLegTwist_m477F6F9AEC4E7E3F8D6FA72B05BCBF05C02FFB19_AdjustorThunk (void);
extern void HumanDescription_get_armStretch_m9C47D234F9BD5C1807E40DF748AE51CC4433C11B_AdjustorThunk (void);
extern void HumanDescription_set_armStretch_m52A43854E332057B62C07306CB1663CEFDB01C71_AdjustorThunk (void);
extern void HumanDescription_get_legStretch_m568292F633E3849E7C8DAB4D738C8E6F52AB662F_AdjustorThunk (void);
extern void HumanDescription_set_legStretch_m40D8FE0E122F5EF4143253814D732AF8413578DC_AdjustorThunk (void);
extern void HumanDescription_get_feetSpacing_mF8283F0B14F35F3FD82BA3D6FF6D82609B924643_AdjustorThunk (void);
extern void HumanDescription_set_feetSpacing_mC0F12305CA30ADA53D08E987E52DE1611E9511D3_AdjustorThunk (void);
extern void HumanDescription_get_hasTranslationDoF_m735B027773FA7BA045C7B1D5A129B504F7B97896_AdjustorThunk (void);
extern void HumanDescription_set_hasTranslationDoF_mE5E5B11F3DC450708723875A5B1D7CFC340FD20E_AdjustorThunk (void);
extern void HumanPose_Init_m9BBDA2B4B2EB0DE3082EE7FE36A92F19870F682D_AdjustorThunk (void);
extern void AnimationClipPlayable__ctor_mF2EE31CC772B100F98CCAE26963059C6C722FA1A_AdjustorThunk (void);
extern void AnimationClipPlayable_GetHandle_mE775F2247901BA293DB01A8D384D3F9D02A25627_AdjustorThunk (void);
extern void AnimationClipPlayable_Equals_mC5263BEA86C02CEDF93C5B14EAA168883E1DB5F4_AdjustorThunk (void);
extern void AnimationClipPlayable_GetAnimationClip_m65C9B50E705936C7E6B3F42A2BD71B704D3D0E1D_AdjustorThunk (void);
extern void AnimationClipPlayable_GetApplyFootIK_m3E599D05D6A40BEFD651618CE5DDA03F15A3610F_AdjustorThunk (void);
extern void AnimationClipPlayable_SetApplyFootIK_m7CBA77F56815AD21784AC53D9EBDAE18AFA48507_AdjustorThunk (void);
extern void AnimationClipPlayable_GetApplyPlayableIK_m04EE9E4136AC350F27814DF3B006238260CF3EE9_AdjustorThunk (void);
extern void AnimationClipPlayable_SetApplyPlayableIK_m69A6F6E28EB250956E27C1720A0A842848F54DAB_AdjustorThunk (void);
extern void AnimationClipPlayable_GetRemoveStartOffset_m52EBB080BD11079E3D2F2AD6E913E5451F24CFE2_AdjustorThunk (void);
extern void AnimationClipPlayable_SetRemoveStartOffset_mBAC88E40F6A759FACA4105EF683181D43381C8E5_AdjustorThunk (void);
extern void AnimationClipPlayable_GetOverrideLoopTime_m22078B967400E8B3E5D056BCE1704CBB2E1E2C93_AdjustorThunk (void);
extern void AnimationClipPlayable_SetOverrideLoopTime_mF1F57940D8DDBCC6EBCB75A27C2372BB39DED177_AdjustorThunk (void);
extern void AnimationClipPlayable_GetLoopTime_mD57482D47862960C3A2D0185E8F75642DD858FEC_AdjustorThunk (void);
extern void AnimationClipPlayable_SetLoopTime_m3AAA1134C4D339C84EF57FE289D33100D4971ED8_AdjustorThunk (void);
extern void AnimationClipPlayable_GetSampleRate_m0559EF225E427721E66976BF290A0C53D1FFA744_AdjustorThunk (void);
extern void AnimationClipPlayable_SetSampleRate_m2D7C98FD996AC3A582578A4433C9438D0675020C_AdjustorThunk (void);
extern void AnimationHumanStream_get_isValid_mFC26001D2772FFDE3C791A764954BD9A1512DD6C_AdjustorThunk (void);
extern void AnimationHumanStream_ThrowIfInvalid_m35ADC8567F7DFB74AA71C4E29572E06A8223DE65_AdjustorThunk (void);
extern void AnimationHumanStream_get_humanScale_mD8924C6D1BD6723AB50A52D89FFFC92855BC47C6_AdjustorThunk (void);
extern void AnimationHumanStream_get_leftFootHeight_mC29F681BA1B4712AD46F911D902BE07CF8FB78C7_AdjustorThunk (void);
extern void AnimationHumanStream_get_rightFootHeight_mA2AB707DDD51031FAC763BA1610C2BFCDD80AFBE_AdjustorThunk (void);
extern void AnimationHumanStream_get_bodyLocalPosition_mA8B1A8A9625C388B7E4E12BE814B4AA1D8D3DC5B_AdjustorThunk (void);
extern void AnimationHumanStream_set_bodyLocalPosition_m5EAC7202D30F117B8821BDDC5282FF19FBB93706_AdjustorThunk (void);
extern void AnimationHumanStream_get_bodyLocalRotation_m6224C03E8D34FAEB50C732CB7EBA7E2328A8EAD6_AdjustorThunk (void);
extern void AnimationHumanStream_set_bodyLocalRotation_m19E818A4DDF88A4BCC520011C5F9864657F106B4_AdjustorThunk (void);
extern void AnimationHumanStream_get_bodyPosition_mFA136469110BADCEE3EC2821AD408108A7F3516A_AdjustorThunk (void);
extern void AnimationHumanStream_set_bodyPosition_mEFD7D98A88B79674702D53D659A2338D5E56F02C_AdjustorThunk (void);
extern void AnimationHumanStream_get_bodyRotation_m6C3E24BCFC5B10524FD8143D33706365CFF2517C_AdjustorThunk (void);
extern void AnimationHumanStream_set_bodyRotation_m16999012B4CEE244F4596C78D98E12B649302846_AdjustorThunk (void);
extern void AnimationHumanStream_GetMuscle_mD12DF41EA39BB88236B6E51C6E60DC505CD3B578_AdjustorThunk (void);
extern void AnimationHumanStream_SetMuscle_mF6E7B3FB78B9E085ADBFA3F5959C065BBDA5F481_AdjustorThunk (void);
extern void AnimationHumanStream_get_leftFootVelocity_m7FA5AA97C252763E010294C1B3F820ECC070E2D5_AdjustorThunk (void);
extern void AnimationHumanStream_get_rightFootVelocity_mE89F940C04F0842CD0A2BBCF394B657FB4080EEB_AdjustorThunk (void);
extern void AnimationHumanStream_ResetToStancePose_m6CB3AAA301BCBA6ABA5A1DC4EE8767301AFDEAC7_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalPositionFromPose_m2773EC1B9E600BD95B53DB65745A53468EF3820D_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalRotationFromPose_m0D29BD6D2246DF74E1EFD77D909550A1E8CB96C8_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalLocalPosition_mAE43F299D80C2E9DF69E41BC0394CCCEABC079FB_AdjustorThunk (void);
extern void AnimationHumanStream_SetGoalLocalPosition_mA10C3B87B9FCB49197FED94B68BB72415815527A_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalLocalRotation_m786FED58DB427635329550B3216AACA3972059FB_AdjustorThunk (void);
extern void AnimationHumanStream_SetGoalLocalRotation_mD853EA0BAF5C880FBC6EC57AD5F0D667F241168E_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalPosition_m9B04E0871FAE6225F9715C7388B334A31834014B_AdjustorThunk (void);
extern void AnimationHumanStream_SetGoalPosition_mC32B190C32B2BFD1FD098C8FF2E81BCE08658A55_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalRotation_m3F754F7E38B1A0E7CC6D4F5517F68FD0017AFA05_AdjustorThunk (void);
extern void AnimationHumanStream_SetGoalRotation_m0AC3D5B3539CCEC2C715CEF37AF96AD91411ED54_AdjustorThunk (void);
extern void AnimationHumanStream_SetGoalWeightPosition_m31CDD1019E6EC4583B13310A3D6B42A3129CCE29_AdjustorThunk (void);
extern void AnimationHumanStream_SetGoalWeightRotation_m9E28713D497E4871CD4513FC0738642F36021009_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalWeightPosition_m97C54429B8000F50C0C8D7BD695B1D6A92A4C376_AdjustorThunk (void);
extern void AnimationHumanStream_GetGoalWeightRotation_m156696D9925EBDEEF0114C79FE8E2A3FAA07AC4D_AdjustorThunk (void);
extern void AnimationHumanStream_GetHintPosition_m8C48689F4E696E601892CDC959B0C0BDBD67BBBB_AdjustorThunk (void);
extern void AnimationHumanStream_SetHintPosition_m7C26134F50A8C6786430A0A5382884E55549112A_AdjustorThunk (void);
extern void AnimationHumanStream_SetHintWeightPosition_m00D5FD24F69AC10C7B133ACAFB71BB2362E4E635_AdjustorThunk (void);
extern void AnimationHumanStream_GetHintWeightPosition_m0D51CD0A32B87BA0208942ABC3DBF3AC0FB8FE9D_AdjustorThunk (void);
extern void AnimationHumanStream_SetLookAtPosition_m6C0D265D80FF6FD89159A9F9D47172B37093D2A8_AdjustorThunk (void);
extern void AnimationHumanStream_SetLookAtClampWeight_mC76D8266ABA59385337106B48BF2F5019DEC29CA_AdjustorThunk (void);
extern void AnimationHumanStream_SetLookAtBodyWeight_m4A5D39D1BB635A01DF98E65F45AD873E44236BCE_AdjustorThunk (void);
extern void AnimationHumanStream_SetLookAtHeadWeight_m8E9632C51EB7FC316FF4DB3F1639D41CB20474E0_AdjustorThunk (void);
extern void AnimationHumanStream_SetLookAtEyesWeight_mD5FC3552642F7A64F794EC8D0872972F8A6AD507_AdjustorThunk (void);
extern void AnimationHumanStream_SolveIK_m36549A812DA9901B415ABF3A9BB40DA236C625F3_AdjustorThunk (void);
extern void AnimationHumanStream_GetHumanScale_m528C523BF55034961049D455994749AEF778117E_AdjustorThunk (void);
extern void AnimationHumanStream_GetFootHeight_m73B1AC0DEA1287024D0EA1D53F9303E0B8A358A6_AdjustorThunk (void);
extern void AnimationHumanStream_InternalResetToStancePose_m1EEC3F6EC405EEB4D36C2CC2D15BFA5CACE50144_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalPositionFromPose_mF9D079DA1E5D728A79637826DBDD8FB7A068E2F3_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalRotationFromPose_m6689F3C3DAE9513AE06956C93368334B64D95291_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetBodyLocalPosition_mFB26EABB0EB551CFA4232D32065A79BF36E356C7_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetBodyLocalPosition_mC469D6040AB3305AE399215925156806A48F1220_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetBodyLocalRotation_mB5A3ABB9FDC0C71BA5FF77D111CCA40C681BD631_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetBodyLocalRotation_mCC66C848E64D04B20A901D7D0760BB2D794B39C3_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetBodyPosition_m549D7BB8B6B6BBA95AA6E04259D975CF81F99849_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetBodyPosition_m0E963FC02C014FF1D519356DAF90E1E37D66DDBA_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetBodyRotation_m533F3431386D21B1A170378120A34A94D76DF121_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetBodyRotation_mB72C4A04EF45AA6E8CC9E52638FB65766885D34C_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetMuscle_mCB75C7C1E13D6B7C0ABBCD4270E94DD025CC876E_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetMuscle_mCC068A0689AB992DB22C4178AEA8390E3F49119E_AdjustorThunk (void);
extern void AnimationHumanStream_GetLeftFootVelocity_mB62C9F36949F43F881DC225B5AD92B010E7961D7_AdjustorThunk (void);
extern void AnimationHumanStream_GetRightFootVelocity_mEE89EE87CFD969E2ABE2FF6A2FABFCB651489F28_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalLocalPosition_m53C7592FE48A5E61D96031CA44D075F3C64E2A44_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetGoalLocalPosition_mAFA13881BA09B0CFFA788B24088CE98A4EFC387E_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalLocalRotation_m223B1725AA46FE23E693AB1E2FD20E96AD9D578A_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetGoalLocalRotation_mCE4ED1A96EBE0BED4FDE8394BD1049CF857970A1_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalPosition_m25C63A96C6858BF176670F3FC620BFF1672FC468_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetGoalPosition_mAFBF83E782A12A0BEBDD63C2BA165541C5AC3029_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalRotation_m48632794B5A9E479ED6194902347E52CC072E4F0_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetGoalRotation_m3810FFA6A48991BA97DC9CE844C8C04104607681_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetGoalWeightPosition_mA9E5685E3F30AED1A5AE87E1135BF5047DA8D326_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetGoalWeightRotation_m8685321C9439B4DA865675908633BDD952CDA8DB_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalWeightPosition_mFB2155CD3EEE62EDC9176A80EDBECA6D1E0BD6CD_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetGoalWeightRotation_mF2E03DAB54D65B15ABF65FD68C39EB48928BE9C8_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetHintPosition_m3D8100E5F06F8C05F717DD1CD9A424DD26396434_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetHintPosition_m7DC7D373633BD22E372BEE6F02E662A3C2C278BC_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetHintWeightPosition_mD6136E04EDF9E591CEAAF8AB09919DCCE54553FD_AdjustorThunk (void);
extern void AnimationHumanStream_InternalGetHintWeightPosition_m4F89A6FD73DEA7166E91D0ECEE416BAEE989982E_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetLookAtPosition_m6DC48530B5D259E37BEB489DCD2BBF07D8CB832E_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetLookAtClampWeight_mBA29A943B22AA5DD8917EB5EB9589F04E76D6857_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetLookAtBodyWeight_m614FEABDB4E114CDA84A94333AA7E2BF9ED4BDF7_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetLookAtHeadWeight_m184F2F1D223714F0FD5B529C89207D9F6C0CDF92_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSetLookAtEyesWeight_m99A49670FE2C24ADFEA9DBBBA4135BDDA93EFE1B_AdjustorThunk (void);
extern void AnimationHumanStream_InternalSolveIK_m84F06C829271E6F0B15B7653BD35A26A2D18046E_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable__ctor_m28884B8B9F7E057DF947E3B43ED78EA107368BD6_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable_GetHandle_m324A98D0B0BFC0441377D65CAE93C914F828721F_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable_Equals_mA5D24E61E2DE1140B409F3B569DBA3C185751970_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable_IsLayerAdditive_m379268A18CFAD74371F6D4E0467072761BF84713_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable_SetLayerAdditive_m3B35E03C224B118E3F3D9E8A7B697AF570FBFB6E_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable_SetLayerMaskFromAvatarMask_mC4BDE2B476AC13C31053100085FAF6BC86000280_AdjustorThunk (void);
extern void AnimationMixerPlayable__ctor_mBF84CC064549C2C00B2AE1174018335958EB7EA7_AdjustorThunk (void);
extern void AnimationMixerPlayable_GetHandle_mBA6CEB1579A713A985D474E75BC282728318882F_AdjustorThunk (void);
extern void AnimationMixerPlayable_Equals_m6EBE215636EEEA3196A43F4D6C1FE6DD704AFA4E_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable__ctor_mDE3C14B4B975AC693669D66B6E41BB6432AFA940_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable_GetHandle_m09F605E78AD7F0135C7F57EB048031091A50E3A2_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable_Equals_m7CBF3B7618EDBA4ECC2F3C2F54011248BC45CDCC_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable_IsAbsoluteMotion_mCFE89CD743EC61AF6EF6117F72A36FCC26216487_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable_SetAbsoluteMotion_m5D1B029F6E6BFFB521CC6CB72ACBE7EA27B28715_AdjustorThunk (void);
extern void AnimationOffsetPlayable__ctor_mBF3AC6493556DAAEF608B359BEBE8FA6D9F8DBFD_AdjustorThunk (void);
extern void AnimationOffsetPlayable_GetHandle_m769BEFF90379AEAB0C579F7800953458CE3EBA78_AdjustorThunk (void);
extern void AnimationOffsetPlayable_Equals_mEC28392ADD4E9639EB9228D106D93E21B3587270_AdjustorThunk (void);
extern void AnimationOffsetPlayable_GetPosition_m1DED0A8F334427F11E32E844C6AB62BB0DBBE247_AdjustorThunk (void);
extern void AnimationOffsetPlayable_SetPosition_mB3D98093AB2BDE7454642F90CDE62C71ED6B9F19_AdjustorThunk (void);
extern void AnimationOffsetPlayable_GetRotation_m17F5666C4A8EF5033B545A3ED3769F8BD4702818_AdjustorThunk (void);
extern void AnimationOffsetPlayable_SetRotation_m042FD5D84E348BEAE49AFCC84DDD14861A8C1AEC_AdjustorThunk (void);
extern void AnimationPlayableOutput__ctor_mE4FB8AA6DFB2F3C18E04A9317F5CE53597A7D22A_AdjustorThunk (void);
extern void AnimationPlayableOutput_GetHandle_m2A8E2A9CBD12EDCF48FC946445AB42802083338D_AdjustorThunk (void);
extern void AnimationPlayableOutput_GetTarget_m2E55D32775E0B27063F697346701A0EB5424DA9D_AdjustorThunk (void);
extern void AnimationPlayableOutput_SetTarget_m0F7745C4A721D76EB1E804AA48E70C9C798E0DCE_AdjustorThunk (void);
extern void AnimationPosePlayable__ctor_mC6C096785918358CA7EC12BABCDF4BBD47F7BA3F_AdjustorThunk (void);
extern void AnimationPosePlayable_GetHandle_m5DC7CA4CAF3CD525D454D99EBC3D12C3571B527B_AdjustorThunk (void);
extern void AnimationPosePlayable_Equals_m10F1E7DD7037B2AB3F7DAE3E01A1DC843EABD0A3_AdjustorThunk (void);
extern void AnimationPosePlayable_GetMustReadPreviousPose_m6C3C23E75C7EEB05DB84E83507F94ED09C1DF961_AdjustorThunk (void);
extern void AnimationPosePlayable_SetMustReadPreviousPose_m3859165529333B3140AE4E9097FE66E992DA8AF7_AdjustorThunk (void);
extern void AnimationPosePlayable_GetReadDefaultPose_mA4E24ADBB4A96DD62D17F50ADECD5F114D44C5B8_AdjustorThunk (void);
extern void AnimationPosePlayable_SetReadDefaultPose_m852C2742CE8CFF49FF7216DA9D2B9C755C41AA20_AdjustorThunk (void);
extern void AnimationPosePlayable_GetApplyFootIK_mDBE93A57F35A70EDE6F8503323EF2F7C5CADE32F_AdjustorThunk (void);
extern void AnimationPosePlayable_SetApplyFootIK_m862FA48CF186206F9B488BFADE8C1866A1A863C1_AdjustorThunk (void);
extern void AnimationRemoveScalePlayable__ctor_m4D6C7C4AB8E078050B0CC34C6732051CF043CFA2_AdjustorThunk (void);
extern void AnimationRemoveScalePlayable_GetHandle_mFFA58B879F31327187A20ED30E1C814B7BEAA9C6_AdjustorThunk (void);
extern void AnimationRemoveScalePlayable_Equals_m0ACDD59B80103591DA8E84CB387FB10778D8C327_AdjustorThunk (void);
extern void AnimationScriptPlayable__ctor_m6DEFD72735E79009FC1484AA2A7A82E6CE601247_AdjustorThunk (void);
extern void AnimationScriptPlayable_GetHandle_m30355B6EE1AA3BA36D628251FB4291386D223646_AdjustorThunk (void);
extern void AnimationScriptPlayable_Equals_mAD02E40704CBE4AB188DE0569052F8EA9864F4E4_AdjustorThunk (void);
extern void AnimationScriptPlayable_SetProcessInputs_mF7DE3561731A6513E8FE0F0A85B0C38959621839_AdjustorThunk (void);
extern void AnimationScriptPlayable_GetProcessInputs_mDF9A5B45217407FB9ED7DF8E7A1E3DEA4DD31A1F_AdjustorThunk (void);
extern void AnimationStream_get_animatorBindingsVersion_mD7D19DCE96F93CE4DC36457F974C5B8562A3B5E4_AdjustorThunk (void);
extern void AnimationStream_get_isValid_mE1F032BDA653D5A903DCD427F4677A6C9C4C227A_AdjustorThunk (void);
extern void AnimationStream_CheckIsValid_m6C46800E1A5A4BE27FF761A93F72BC3CD751174C_AdjustorThunk (void);
extern void AnimationStream_get_deltaTime_mECEF75B188313080405BFB556AB4CFD972233861_AdjustorThunk (void);
extern void AnimationStream_get_velocity_m8628858DC7927B5DC15C3050945D8150E4166179_AdjustorThunk (void);
extern void AnimationStream_set_velocity_m0C9F3849570576287D94FEC72556BA070F9B3D49_AdjustorThunk (void);
extern void AnimationStream_get_angularVelocity_m3D7FCC57BD7A9EE99FE9DDAB3B425C700841C617_AdjustorThunk (void);
extern void AnimationStream_set_angularVelocity_m0600FEEDAC435358DAE51BC5AA4EEAEED6649DD8_AdjustorThunk (void);
extern void AnimationStream_get_rootMotionPosition_mAE5E4330D95DD4AB9BD65341CCB4340259235A40_AdjustorThunk (void);
extern void AnimationStream_get_rootMotionRotation_m0CE33CEDB1195D26AE380A561A487CBB508A0530_AdjustorThunk (void);
extern void AnimationStream_get_isHumanStream_mABF4806846E32130D5D4C204CAE4E8E0DA897158_AdjustorThunk (void);
extern void AnimationStream_AsHuman_mADFB59FE0BC7C00348B567BE06A4E8E9C830A916_AdjustorThunk (void);
extern void AnimationStream_get_inputStreamCount_m434C3915F96FC4C707622AA6F8F40F5BEF0031D0_AdjustorThunk (void);
extern void AnimationStream_GetInputStream_mFFAEDE4760FD9FB17C6F702DB3E1835C77600E49_AdjustorThunk (void);
extern void AnimationStream_GetInputWeight_mE168D42B18134F5B6F11B932C9EF3D523B4759DA_AdjustorThunk (void);
extern void AnimationStream_CopyAnimationStreamMotion_mA5E2C0B009574DF88C47B57A3ABAFC6C20FA3EB7_AdjustorThunk (void);
extern void AnimationStream_ReadSceneTransforms_mE817BB3693A822A761DFF489AC5661F93E076CCD_AdjustorThunk (void);
extern void AnimationStream_WriteSceneTransforms_mD0A8D14E6A70F2DD4639E1D265F3D28961B8401D_AdjustorThunk (void);
extern void AnimationStream_CopyAnimationStreamMotionInternal_m47DAF2063EEF6D09F538BD3A9E039DD01C68243F_AdjustorThunk (void);
extern void AnimationStream_GetDeltaTime_m335ACEAEEAEE7E3FAE1CCBD81DA839C6C1CFF0A9_AdjustorThunk (void);
extern void AnimationStream_GetIsHumanStream_mD58FA1E5EEF324695C45C8AAF3A79D13987DE576_AdjustorThunk (void);
extern void AnimationStream_GetVelocity_m554A288784E59A32E2D39B826912890127AAC57F_AdjustorThunk (void);
extern void AnimationStream_SetVelocity_m66A0FF467A4423DD1BEE265EADBEE273CB764382_AdjustorThunk (void);
extern void AnimationStream_GetAngularVelocity_mCEAA43EE55D952A83B0AC4236DFFCD249DC8587B_AdjustorThunk (void);
extern void AnimationStream_SetAngularVelocity_m6860A649F2D2A7B7082502753E5B94C12A10640A_AdjustorThunk (void);
extern void AnimationStream_GetRootMotionPosition_mF64C52D3BC86F9B31E50426ABA638C5FF6E59D22_AdjustorThunk (void);
extern void AnimationStream_GetRootMotionRotation_m4132427788382A623CF0703FF56AC34D6E31C1FD_AdjustorThunk (void);
extern void AnimationStream_GetInputStreamCount_m3E9B20B8AA6D1A6704FEF58B384ECCDCC67C3624_AdjustorThunk (void);
extern void AnimationStream_InternalGetInputStream_m19EEF4A1AA1BF053B21D5059E3836699D55A1974_AdjustorThunk (void);
extern void AnimationStream_InternalGetInputWeight_mB4CE342A125415612BA0D6CF329EFE4DD15DC503_AdjustorThunk (void);
extern void AnimationStream_GetHumanStream_m719D5DCDC0B6B4CC92E93BDFAA51D138A34DCC1F_AdjustorThunk (void);
extern void AnimationStream_InternalReadSceneTransforms_m3CB8D564DC110A6A7475FA9F00C95D4294CAC588_AdjustorThunk (void);
extern void AnimationStream_InternalWriteSceneTransforms_mA489D8D3B1B02D7455BF7FBDFF4D76409878ACFD_AdjustorThunk (void);
extern void TransformStreamHandle_IsValid_m96EE3A490B88868890CF2E754838F97424A65512_AdjustorThunk (void);
extern void TransformStreamHandle_IsValidInternal_mBF1602E33ABCA25121C7CF70173D29C5291354CC_AdjustorThunk (void);
extern void TransformStreamHandle_get_createdByNative_mCC27504004588C367456D55E8295B745BE2431AC_AdjustorThunk (void);
extern void TransformStreamHandle_IsSameVersionAsStream_m31E41B516413440AC8F4D5F9F233623A6DE71365_AdjustorThunk (void);
extern void TransformStreamHandle_get_hasHandleIndex_m164F6D37B1A6B74214B09E1E6798C275C71716D1_AdjustorThunk (void);
extern void TransformStreamHandle_get_hasSkeletonIndex_m8B9589FACB6810B0EFD84033D30057ADFBC4B75F_AdjustorThunk (void);
extern void TransformStreamHandle_set_animatorBindingsVersion_m5F9ED42B51BED505332D0D1B88CB823A8BEF3A01_AdjustorThunk (void);
extern void TransformStreamHandle_get_animatorBindingsVersion_mD044F88843A162A554BA7EF191E52B58F9F0AFF8_AdjustorThunk (void);
extern void TransformStreamHandle_Resolve_m5DDC5761EF01E700ABDB214030802982DABC3E6E_AdjustorThunk (void);
extern void TransformStreamHandle_IsResolved_mB2A9548AAB37C1485AFEC2C9CD9A3D4ABC786D52_AdjustorThunk (void);
extern void TransformStreamHandle_IsResolvedInternal_m83781A03679DF4C678FE963CF21F5A2203471585_AdjustorThunk (void);
extern void TransformStreamHandle_CheckIsValidAndResolve_m7602706A5D46D99268DB6C698A6752C96A0525F6_AdjustorThunk (void);
extern void TransformStreamHandle_GetPosition_m8980B6C6185653E9B962625D030C3BB1994C8B89_AdjustorThunk (void);
extern void TransformStreamHandle_SetPosition_m45609A840DAAF0410F72218E58E2207841290002_AdjustorThunk (void);
extern void TransformStreamHandle_GetRotation_m02E0CE9B403FB4138605190A48A19767D6B0C42A_AdjustorThunk (void);
extern void TransformStreamHandle_SetRotation_mA159153895AFFB08B30B8287304A720215C364D1_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalPosition_m30A1BF0A06551177E6D28A73D3DE71522B77C0A4_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalPosition_mEC97D6C69019B8212F444B33DB51BDCD9DCD6282_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalRotation_mB613F5958303751C9368AD2CC613723E279985AF_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalRotation_m68B0586FA34978971ECDC909A44E1E0C13443C6A_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalScale_m559039B8F2285CC33E3E952F078EF899C8ACB451_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalScale_mEAC5ED65AA0B8F756E2C129ED14D78C7B3698FE2_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalToParentMatrix_m91AC76F7B4D7B530A6D30E87E68E75C50B95DA77_AdjustorThunk (void);
extern void TransformStreamHandle_GetPositionReadMask_m2901E4573FC6105194851C3FD1B88A3F18DA4F5F_AdjustorThunk (void);
extern void TransformStreamHandle_GetRotationReadMask_m8D17943EA0E9F130DA1B5A2CE336625AAC7E4888_AdjustorThunk (void);
extern void TransformStreamHandle_GetScaleReadMask_m12F8441C4AE4B23345BA37F5DFD82B5288695230_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalTRS_mF633398360834FAD1B1F8E21EF8C2A01B3E38A8D_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalTRS_mA4D470AC9B87FF6FAC880A926BD3A5F4EC30BFB2_AdjustorThunk (void);
extern void TransformStreamHandle_GetGlobalTR_mA0526AC698E96B95E6BE3E17A477DB028EF8A499_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalToWorldMatrix_m19D802014D758A8BE531FE3A7000371D59C5B195_AdjustorThunk (void);
extern void TransformStreamHandle_SetGlobalTR_m8C4F35DE8E639AE7D7F94F1D015AD3C16D2FC406_AdjustorThunk (void);
extern void TransformStreamHandle_ResolveInternal_m0008C8228981E9247DA8B0C7739DD1BF1C70EAEA_AdjustorThunk (void);
extern void TransformStreamHandle_GetPositionInternal_m4D0EA1C47F1AAB4723411247DBA15135BA6A9D4C_AdjustorThunk (void);
extern void TransformStreamHandle_SetPositionInternal_m0855C3D765D6635BFEBA847061CDC90B645246CC_AdjustorThunk (void);
extern void TransformStreamHandle_GetRotationInternal_m53A7E32CE6B63F588F44CAE8FCBF23C32E8393C7_AdjustorThunk (void);
extern void TransformStreamHandle_SetRotationInternal_mE7AB735A2303DC923A69B19537FDE60B5F39CE5A_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalPositionInternal_mDF78249F5365FA56D51A9854D69DBD420CA2408A_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalPositionInternal_m1758971CB7DC05A269612D1B975A22C8CB2CB890_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalRotationInternal_m5AD8291814578D1F199FBBD2E336C43FC387CEAB_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalRotationInternal_mDE8F5BF1C73A90573EF3918F1C88ABB73BC10778_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalScaleInternal_mAE69D739C71A1F9AB26E9E3496294B6662F429A2_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalScaleInternal_mE108B8F9D39C2C540C8619C7DECFDB685040F85C_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalToParentMatrixInternal_m4D7A1D9602675F7998C24CCDAB752F8E7BAC8DBF_AdjustorThunk (void);
extern void TransformStreamHandle_GetPositionReadMaskInternal_m18D7DFE8500F0B2BC42F06324B0286B73F8062FF_AdjustorThunk (void);
extern void TransformStreamHandle_GetRotationReadMaskInternal_mB7D7E96C8D8CD0FBED076396FB14955E31CA732D_AdjustorThunk (void);
extern void TransformStreamHandle_GetScaleReadMaskInternal_m1D3791F8161BA91D9EC2D5D744B05D33E419FBA4_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalTRSInternal_m1B1B9B973843354BCA7D7A5A76CE44EFE7F2A203_AdjustorThunk (void);
extern void TransformStreamHandle_SetLocalTRSInternal_m2FC862511AEAC5C2900D016CF31EB2E25D321D8B_AdjustorThunk (void);
extern void TransformStreamHandle_GetGlobalTRInternal_m71E4832B7C5D99A91FDF742CA3E54F1C43CE34AF_AdjustorThunk (void);
extern void TransformStreamHandle_GetLocalToWorldMatrixInternal_m218BEFABE5A04FE2DF4F0E038049386C6A8C5EC0_AdjustorThunk (void);
extern void TransformStreamHandle_SetGlobalTRInternal_m9C62E8BD63B362A404C376B09005C024F54DC7B4_AdjustorThunk (void);
extern void PropertyStreamHandle_IsValid_m21FEF08137BB2DC014F731A98CBA5F516939723E_AdjustorThunk (void);
extern void PropertyStreamHandle_IsValidInternal_mFE619567B465984FC8E00F07CC24D489802BB51B_AdjustorThunk (void);
extern void PropertyStreamHandle_get_createdByNative_m2610F75D942E639F8D9919D9A8A8E2210503292A_AdjustorThunk (void);
extern void PropertyStreamHandle_IsSameVersionAsStream_m105BA0425054D86214E70C3D0746517A0BBD5305_AdjustorThunk (void);
extern void PropertyStreamHandle_get_hasHandleIndex_m296B641953CA1478332DE8D4E3616EDDE67F4415_AdjustorThunk (void);
extern void PropertyStreamHandle_get_hasValueArrayIndex_m6BFF272278DB968E4732EE7BAA990F18258DC610_AdjustorThunk (void);
extern void PropertyStreamHandle_get_hasBindType_mF482FD67DC2BBB8AF20A959C846430577FCC51B3_AdjustorThunk (void);
extern void PropertyStreamHandle_set_animatorBindingsVersion_m091EA76553DCD034CF179B9F31BD25057B83764F_AdjustorThunk (void);
extern void PropertyStreamHandle_get_animatorBindingsVersion_mAF352761E16C2BC5658A2B37C77EFC88173EA4C0_AdjustorThunk (void);
extern void PropertyStreamHandle_Resolve_mE4B9D57D9092E7278EF68192F8E5F57D1ED3B645_AdjustorThunk (void);
extern void PropertyStreamHandle_IsResolved_m44BC7FEA11CAA3862B733E319C7794F1C3536D86_AdjustorThunk (void);
extern void PropertyStreamHandle_IsResolvedInternal_m7B96232330AB117B2050D16FE135103D6ED97DFA_AdjustorThunk (void);
extern void PropertyStreamHandle_CheckIsValidAndResolve_mD4036D6F0444B68BC4C1AECDD7429FEBAF03203C_AdjustorThunk (void);
extern void PropertyStreamHandle_GetFloat_mAEC50079467900B74F7B485BBAF65A4DE1BBB8DF_AdjustorThunk (void);
extern void PropertyStreamHandle_SetFloat_m9FA9F67C2AA473A7395EACF2563E7C8A06008C33_AdjustorThunk (void);
extern void PropertyStreamHandle_GetInt_m58CECCE1A73DB1EA96E287B5B7BF51BF502D8D98_AdjustorThunk (void);
extern void PropertyStreamHandle_SetInt_m1E7A8AF3165E77EB0148AC17FB641711134DBE48_AdjustorThunk (void);
extern void PropertyStreamHandle_GetBool_mBB5B008988E6CC47C526CF654E53804909C141E0_AdjustorThunk (void);
extern void PropertyStreamHandle_SetBool_m8307F19E9C41A431FC9A71128A305B547BE675C7_AdjustorThunk (void);
extern void PropertyStreamHandle_GetReadMask_m70E5969F001669CC1467E03014CF46C5E865F9DE_AdjustorThunk (void);
extern void PropertyStreamHandle_ResolveInternal_mFB0A48675D8D847197CB392325CDCA837B82E64C_AdjustorThunk (void);
extern void PropertyStreamHandle_GetFloatInternal_m11E03DE3C420D8F9BFA7926D1F452766BD34B783_AdjustorThunk (void);
extern void PropertyStreamHandle_SetFloatInternal_m044594EAE3DEEC6030E096DB0A8F0454ADCAD6A8_AdjustorThunk (void);
extern void PropertyStreamHandle_GetIntInternal_m598EC48F1700FB43EF1A5880178BB841A781D4C9_AdjustorThunk (void);
extern void PropertyStreamHandle_SetIntInternal_m37894828B9FD37A78CCE5A6F9F9EB7E1C0FE72A4_AdjustorThunk (void);
extern void PropertyStreamHandle_GetBoolInternal_m25D06AA6F53B3265E80243E589F39EF4C30E7DAD_AdjustorThunk (void);
extern void PropertyStreamHandle_SetBoolInternal_mD237DF6939F5BE683D485E984C37791624C67A26_AdjustorThunk (void);
extern void PropertyStreamHandle_GetReadMaskInternal_m28FDDC616FF6A3C9BF19F19FCC9103DD337DF9BF_AdjustorThunk (void);
extern void TransformSceneHandle_IsValid_m11DB3FA1E3137CA1C2D4D4BC18AD717FCCAC65E2_AdjustorThunk (void);
extern void TransformSceneHandle_get_createdByNative_m40C489AAD66DEDFEB69F6BB25B1177FC51922D3D_AdjustorThunk (void);
extern void TransformSceneHandle_get_hasTransformSceneHandleDefinitionIndex_mBD4B49152989D4379E6D726B0F7E834EA484383B_AdjustorThunk (void);
extern void TransformSceneHandle_CheckIsValid_m33214B5950C49A143A5548B7FB1672062204655A_AdjustorThunk (void);
extern void TransformSceneHandle_GetPosition_mDB8261C4AF79828292D555DBF91A6559DE41B3B8_AdjustorThunk (void);
extern void TransformSceneHandle_SetPosition_m2119B1F81EB229CED3914AB214783D67C036EEF8_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalPosition_mF5AFF93B5129702C280CCEB0603100377BFE2D32_AdjustorThunk (void);
extern void TransformSceneHandle_SetLocalPosition_mA8A45E45843E29DEB53A99827816C7B0BFE01376_AdjustorThunk (void);
extern void TransformSceneHandle_GetRotation_m8F6CA3E2302A43103A808120AEE1C527EB2A2F05_AdjustorThunk (void);
extern void TransformSceneHandle_SetRotation_mB91EC437053AE3E393393C1D6B3F62270AA6DB0F_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalRotation_mEB7C44D455654A40518C8AE8D8CC0D191E194B8D_AdjustorThunk (void);
extern void TransformSceneHandle_SetLocalRotation_mF7DF58A87432FF06B73AFEB19D4030108F2CFCED_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalScale_m46D9A20E7DC5967A0EE6F7C217FAA68FBB93611E_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalTRS_m5FBE0248443B3D34C7787A50DE89BB473638CADB_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalToParentMatrix_mE9193C3FF11071363E75E8F8BD89A285BFAF3797_AdjustorThunk (void);
extern void TransformSceneHandle_GetGlobalTR_m7CB0885446CA0894BF681F1954C3E0FB8D31C9EB_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalToWorldMatrix_mF79BCAA91FBE2949EC89A0A010A9AAFCFC2BE9FE_AdjustorThunk (void);
extern void TransformSceneHandle_SetLocalScale_mE408411FD205489EAA1E148B98C2427BF3C201D3_AdjustorThunk (void);
extern void TransformSceneHandle_HasValidTransform_m19ABA61E5902DA7F1207798BBB4DCFBE515B9537_AdjustorThunk (void);
extern void TransformSceneHandle_GetPositionInternal_mAC29651E3EB5FC3BD4CB49B2B09EB9897375FC29_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalPositionInternal_mAFD402FC292DD1D9BDBF6BE446E89CDD85A47182_AdjustorThunk (void);
extern void TransformSceneHandle_GetRotationInternal_m53DDDEE9D5824A6E5BFEE5C5681A33E468E0FC5E_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalRotationInternal_mC63DB1B1DA36584AC8C550683E9E2F67F350A688_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalScaleInternal_m15883C28FC7DE56F00374B6A14F027CFC6C35069_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalTRSInternal_mBB816EAB9B873A23A8B01C96789480F438968497_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalToParentMatrixInternal_mAD951B3EF38C5A5F5043E1427B21BEFB5F1265DD_AdjustorThunk (void);
extern void TransformSceneHandle_GetGlobalTRInternal_m821151819DD1E3F5ABA1D2C8DC5372C67A2DF343_AdjustorThunk (void);
extern void TransformSceneHandle_GetLocalToWorldMatrixInternal_m963B53C92A8B6BC96C2DFE67E015156B731C6E50_AdjustorThunk (void);
extern void PropertySceneHandle_IsValid_m88FB030E99AE0E2D8A4506C7B06BAF8B27CE481E_AdjustorThunk (void);
extern void PropertySceneHandle_IsValidInternal_m60ACFD75DC826CB176ED25ABDF6244EEEFA26D7F_AdjustorThunk (void);
extern void PropertySceneHandle_get_createdByNative_m1579532A69F2E0052D6028BB8563E693DACC044F_AdjustorThunk (void);
extern void PropertySceneHandle_get_hasHandleIndex_m8A4BD33134980310FFD1568BF2B0F1E7D88D7E8A_AdjustorThunk (void);
extern void PropertySceneHandle_Resolve_m4ADA3EE521AB78FEFCEDBC666CA66868D67B0075_AdjustorThunk (void);
extern void PropertySceneHandle_IsResolved_mFD3DCB641948607A93E40B0D2E6897333F68D7EA_AdjustorThunk (void);
extern void PropertySceneHandle_CheckIsValid_mA5326B76CA28F14695D23826F1F43215C353A033_AdjustorThunk (void);
extern void PropertySceneHandle_GetFloat_m2DA1CB5219344BB6A59421E15A74C6ADE286EC24_AdjustorThunk (void);
extern void PropertySceneHandle_SetFloat_mCDF00CD9F89DB5906FA72CAD9CE1AB98E3322E12_AdjustorThunk (void);
extern void PropertySceneHandle_GetInt_mA22A93D80220EE3CF94C7DCFCC70A958F125418E_AdjustorThunk (void);
extern void PropertySceneHandle_SetInt_m927C12912421F35BDBC5FF6FA34BB5A716E536CE_AdjustorThunk (void);
extern void PropertySceneHandle_GetBool_mE876BC9201F73F814AFF5F4A82C90CC02BEDD18C_AdjustorThunk (void);
extern void PropertySceneHandle_SetBool_m43589AABBCE73E5BDF47E24EAADC309908CEF5DB_AdjustorThunk (void);
extern void PropertySceneHandle_HasValidTransform_m4DFB6634221B060FB846A975C2AA76881CFA978B_AdjustorThunk (void);
extern void PropertySceneHandle_IsBound_m75BC5C73C0C65F3967C0B2E6519ED4EF495E19DE_AdjustorThunk (void);
extern void PropertySceneHandle_ResolveInternal_m9C4C80E37AB2178EDA3B31506BE2F56721E1BF3C_AdjustorThunk (void);
extern void PropertySceneHandle_GetFloatInternal_mBD6D41B4F23687D5CEE7F1674E457C03BF4CF692_AdjustorThunk (void);
extern void PropertySceneHandle_GetIntInternal_m8AC9D22272A7199D6BBCA877B036408D5975BC46_AdjustorThunk (void);
extern void PropertySceneHandle_GetBoolInternal_m4714FA71EB7064EAFF5D40643989E41D057ABB75_AdjustorThunk (void);
extern void AnimatorControllerPlayable__ctor_mBCB9475E2740BE1AEB94C08BAD14D51333258BFE_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetHandle_m718D9A4E0DB7AC62947B1D09E47DBCD25C27AF6C_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetHandle_mD86A3C0D03453FAF21903F7A52A743AB2DA6DED4_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Equals_m14125BB4CCFCDFFD098223AF20E38501BA264180_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetFloat_m787538C1B2ED9C4E52E8A3EEF43405913E82E895_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetFloat_mDBD6169B671FCD0AA3551368A3EE37334E7D7B49_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetFloat_m95BF662BF3AA8A4F4B5F868801DED66D397E2407_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetFloat_mEDD694D72DDBEDF381973F41D83037F330224EDC_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetBool_m30C0C093BA04B2C680E5B8510A189A66A4D5333D_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetBool_m72BB4B2A95A2C229ADE9B51F7232B8FF4E5DF26E_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetBool_mDBBE0C1A970D71F600234B15D0E6B9C51E7A70DC_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetBool_mEBE969EAB2935C3A15848521B06ABB45B1188BAD_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetInteger_mFE3ADA2A4AD9A7A62B60D7E5F2FCBCF9A5F58AA0_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetInteger_m50B3042FDC1F87A4E0423FCCFEED611996A3860D_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetInteger_m4C8B5F237C20CFABF0CAD460F55782D834444A91_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetInteger_m6FA135B1C91BED0F97C9E623FB8C37411C6F15B4_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetTrigger_m06D7662315ED85A8472E62C2F369B1167BAF51A7_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetTrigger_mA2F8F838D48244468DACBF503E120FF3B06E95B2_AdjustorThunk (void);
extern void AnimatorControllerPlayable_ResetTrigger_mD671335B03DF16D311287002303191D50B802D14_AdjustorThunk (void);
extern void AnimatorControllerPlayable_ResetTrigger_mABE88FFA8781EB19F5B06F7BF483F13C61107D6C_AdjustorThunk (void);
extern void AnimatorControllerPlayable_IsParameterControlledByCurve_m34F8D191A09BC28CCDF1A044FD02B33A1D64D0B1_AdjustorThunk (void);
extern void AnimatorControllerPlayable_IsParameterControlledByCurve_m8ADD7EE7A489424B4B99253C96647E67B04E2D1B_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetLayerCount_m3941A7DA375B26988A59DA13A22C08476D59D34F_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetLayerName_m33BDF74B10C6BFDB82FF404E288F52F1EBABC809_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetLayerIndex_m48AB42D7F82BE03576D4AC480F048EEDF01B0A0D_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetLayerWeight_m09D5E7AB77824DE2DE20E4E4491BF2A90E3B28BB_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetLayerWeight_mE432DA7CC2FC4425D0DA064B71B5ACCEB83F8EE3_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorStateInfo_mBA937DD74A4964C743880FFE7CFFE67B18D8264B_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetNextAnimatorStateInfo_m1BD26B635F70840C13BC34D0632D82B5EA48CDE0_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetAnimatorTransitionInfo_m5157FED95B567D4441228BC0F3AF31563FD5BF1C_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mF89B409BFDD2B021BB6862B93A68879BCAB60076_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mB103A1F9EC5E23947C8A1802D644FC4D22093F14_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetNextAnimatorClipInfo_mF00B978E844FF7E6DF0AF670C0CF2022A8DD2517_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetCurrentAnimatorClipInfoCount_mB80BDB04DF276D53D45EAA549CAD88DA9A7E8BE8_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetNextAnimatorClipInfoCount_m19CD5C12E89C22E3521175CD6C1E2F033C53D070_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetNextAnimatorClipInfo_mD247136758432A4036FC338D4C8E67ECAF5EDD26_AdjustorThunk (void);
extern void AnimatorControllerPlayable_IsInTransition_m041E31E9DAD976867BCCBDDD27CB556F590D61D9_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetParameterCount_m899EE6DAD7209174D7568E632DA986D39E435A70_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetParameter_mB7ECCC4E41138CC7D9A28ABF499679B8792E9CC3_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_mC4104110B90B6802409F8FC102AC5758250FAE74_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_mF66C581A902E013AF240151A4B9772F8DD94E95A_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m43166ED8F80B94469DFA25374911A64637438674_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m664DCD090D5E91612254A0F87AB5D2A538DF8DC5_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m7901F79283F0C1357A14AE48D81BA663D98740C8_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFadeInFixedTime_m93EC21F858FE76184FA91D2B6EA3B361A2929503_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFade_m93543957867BB7F6B7A5498EF7ECDBAFCA0C295F_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFade_m3B003ED0E44C2405B104B7E7ECF1AC0A6C15C7DB_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFade_m8AD5C075E4B4B645796A36980AE15378167019AA_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFade_m4CADD7D865B6C0A16D35B58011895E560CCF8824_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFade_mC2E4C450F3E777DBE08BBBF0138FAA174D0526C5_AdjustorThunk (void);
extern void AnimatorControllerPlayable_CrossFade_m0C777C0385BE3E8AA8D86C99897D490ED553F9DD_AdjustorThunk (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m797B8A53344C62FF813DC398D1E6A6B18A826275_AdjustorThunk (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_mA9FA57E1D8B5B4DB655F1918C338585573B7DEEA_AdjustorThunk (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_mE4DAC931BFEDBCAABE0D410BE3DF85C5C4FF1425_AdjustorThunk (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m37882EB28A1E75354814AB9EB6426D7F3FF8D985_AdjustorThunk (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m62E44D644C7785CFF04229D6EA808F9C670FF010_AdjustorThunk (void);
extern void AnimatorControllerPlayable_PlayInFixedTime_m513E1A9D864FCC8DF9E16A9DE05465A5694ACE2B_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Play_mD967CAA3998D88A9116C165D1D1ADDEEC8D3FEFF_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Play_mA3346301B00AD22D27B692BE36E560569FCD2765_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Play_m703A612D5040F4F99C17D300E5BD57ABB992E976_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Play_m3C33626F6950B0CD33EB39A87C93819B6CC9C52D_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Play_m252547934FE64DCF1DC9D3242E20200A0E8852D1_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Play_mB14FF22BE5BF41EB807BD3F14C111A3D60E59394_AdjustorThunk (void);
extern void AnimatorControllerPlayable_HasState_mE537ED9F84D34939019463D4A2F6171B053759C2_AdjustorThunk (void);
extern void AnimatorControllerPlayable_ResolveHash_mF09713D683CEF1F188415CE3868CF117B04FA322_AdjustorThunk (void);
extern void ConstraintSource_get_sourceTransform_m0A9C0C96F68398626E43F2137E2CAB5BA803F94F_AdjustorThunk (void);
extern void ConstraintSource_set_sourceTransform_mC905D9E04293D785BA40E1E0378457A77170A31B_AdjustorThunk (void);
extern void ConstraintSource_get_weight_m90743C7AB9BA12A99FB8C81442E765A4F947BE28_AdjustorThunk (void);
extern void ConstraintSource_set_weight_m40EADC470F7D906EEB89A515F75CC8B0648368D7_AdjustorThunk (void);
extern void MuscleHandle_get_humanPartDof_m8262C622AA62DC780CCA30CB5492F1235C45A505_AdjustorThunk (void);
extern void MuscleHandle_set_humanPartDof_m11EE483E985240C49307DF184DAE8B3260945BCE_AdjustorThunk (void);
extern void MuscleHandle_get_dof_mFD825C91413A42221BD937827E3C743C8A776598_AdjustorThunk (void);
extern void MuscleHandle_set_dof_mB202EB7F0DE1EB8E0E3E84349914EA26F48EAA93_AdjustorThunk (void);
extern void MuscleHandle__ctor_m639F42D1909646875E9AC30B3394498060E5ACD6_AdjustorThunk (void);
extern void MuscleHandle__ctor_mBE2F28D04718BC0EF94766966956ECCC980889CA_AdjustorThunk (void);
extern void MuscleHandle__ctor_m4A0A680BBA254F55AE88CBCA6A0B0A50A0E143C6_AdjustorThunk (void);
extern void MuscleHandle__ctor_mF082607DD932A653DABE2DE3DF4C624AAEB174F3_AdjustorThunk (void);
extern void MuscleHandle__ctor_m0103CC894F5E0BD46FD5FF9A0F20BBA11873AB6E_AdjustorThunk (void);
extern void MuscleHandle_get_name_m298104348CF7CF2ED690FDB26039D283D9629174_AdjustorThunk (void);
extern void MuscleHandle_GetName_m229299540D61FB707555C9C2D254DC7448B7AC22_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[440] = 
{
	{ 0x060000A4, AnimatorClipInfo_get_clip_m6205DB403EBEAEAC14DB8928FFC7EBC50142E1AC_AdjustorThunk },
	{ 0x060000A5, AnimatorClipInfo_get_weight_m1CC29E2C37B30993EFFD12161059E4AD86EE287D_AdjustorThunk },
	{ 0x060000A7, AnimatorStateInfo_IsName_mB936F493D6BDDB9372C8E9D813CE0416B002C4D0_AdjustorThunk },
	{ 0x060000A8, AnimatorStateInfo_get_fullPathHash_m583FA8FAAC28BF65A65166D100949833E515210F_AdjustorThunk },
	{ 0x060000A9, AnimatorStateInfo_get_nameHash_m88E91C33AA5602324A7319D7A51F552D00B14D4A_AdjustorThunk },
	{ 0x060000AA, AnimatorStateInfo_get_shortNameHash_mEE816B999C282A3BA95AFC64278B994E899B7004_AdjustorThunk },
	{ 0x060000AB, AnimatorStateInfo_get_normalizedTime_m087C7E5A72122ADF18EBB4AC8391103B9119CCC6_AdjustorThunk },
	{ 0x060000AC, AnimatorStateInfo_get_length_m2FAE317264F7C796427207F8F28E550DB49F9541_AdjustorThunk },
	{ 0x060000AD, AnimatorStateInfo_get_speed_m473826E53D827AEE36A7FF0662AC4817C2EF3495_AdjustorThunk },
	{ 0x060000AE, AnimatorStateInfo_get_speedMultiplier_mA078AADECC98C266C82E2756A351235DDC63A107_AdjustorThunk },
	{ 0x060000AF, AnimatorStateInfo_get_tagHash_m3F4738079576820B7D5942854882B2B468CDD55A_AdjustorThunk },
	{ 0x060000B0, AnimatorStateInfo_IsTag_m9A3181AA167702263EB283AF27B21D08EAD895EF_AdjustorThunk },
	{ 0x060000B1, AnimatorStateInfo_get_loop_m3DC728FC9AF0D4B27B3C28157395BB2F57CC3DA7_AdjustorThunk },
	{ 0x060000B2, AnimatorTransitionInfo_IsName_m6C0C8BBF7E241EFEE2199D5D97DC59958BFBE324_AdjustorThunk },
	{ 0x060000B3, AnimatorTransitionInfo_IsUserName_m91FDB1462C56FCDB3F9A209020D2014B06833DBE_AdjustorThunk },
	{ 0x060000B4, AnimatorTransitionInfo_get_fullPathHash_m3C358272C30F5AAE76547585AA6C3D866F6F77AE_AdjustorThunk },
	{ 0x060000B5, AnimatorTransitionInfo_get_nameHash_m31EC38373C30F6A8BADA9AD27EBAB6BC5B9185CE_AdjustorThunk },
	{ 0x060000B6, AnimatorTransitionInfo_get_userNameHash_mBAD5D1898CE34F1E0657D6BBEA18C4A35EC686EE_AdjustorThunk },
	{ 0x060000B7, AnimatorTransitionInfo_get_durationUnit_m7E41A2E75B4DBD836E1BC55FA933CF199203B7E1_AdjustorThunk },
	{ 0x060000B8, AnimatorTransitionInfo_get_duration_m15DB72ECD67D569CA85DA4CD46E4C92677BFF8B9_AdjustorThunk },
	{ 0x060000B9, AnimatorTransitionInfo_get_normalizedTime_m0D107F16FB8351EBB0E8F8A4367A69916E260072_AdjustorThunk },
	{ 0x060000BA, AnimatorTransitionInfo_get_anyState_mDF1EC0E1F99B7998D19720BC5AAE4B7A31499273_AdjustorThunk },
	{ 0x060000BB, AnimatorTransitionInfo_get_entry_m56D5DF6A01AFBAA8611505369333083DA652DB13_AdjustorThunk },
	{ 0x060000BC, AnimatorTransitionInfo_get_exit_m32F0BA8C87C7487F27A3BCE5A3B490DEB67AC80E_AdjustorThunk },
	{ 0x060000BD, MatchTargetWeightMask__ctor_m381E3F8A3BA8447D8E9CB084E785AB2CDD38B96B_AdjustorThunk },
	{ 0x060000BE, MatchTargetWeightMask_get_positionXYZWeight_mA92CEAD4501F10E0B8F1177C4A1C1B60A1CE644B_AdjustorThunk },
	{ 0x060000BF, MatchTargetWeightMask_set_positionXYZWeight_m59F98F90D7089D61AD3B375E788BEE5D78753E0B_AdjustorThunk },
	{ 0x060000C0, MatchTargetWeightMask_get_rotationWeight_m5FDC8E88D3A7E6DC0DD51F988F0F172FD8C84C88_AdjustorThunk },
	{ 0x060000C1, MatchTargetWeightMask_set_rotationWeight_m6E9398D9F0017122E85576DE6482A17E2C8A15D2_AdjustorThunk },
	{ 0x0600021E, SkeletonBone_get_transformModified_mF6EC8E089FEC8D75FB511B8F7E210B980169C05C_AdjustorThunk },
	{ 0x0600021F, SkeletonBone_set_transformModified_m4CA61CA5AE981BB303BBF41240A5AE6CA251FCA4_AdjustorThunk },
	{ 0x06000220, HumanLimit_get_useDefaultValues_mA6C116B2DC3D800FDACE2907D52B85E632914677_AdjustorThunk },
	{ 0x06000221, HumanLimit_set_useDefaultValues_m3BD1D01F9652270133D307C7709FDD554621ADE8_AdjustorThunk },
	{ 0x06000222, HumanLimit_get_min_m12EAAB4E0EBBBBD221BDB213130DE9643906AB9D_AdjustorThunk },
	{ 0x06000223, HumanLimit_set_min_mE1EFA9D3BBB3047BF25554696FEEFF1F218A7227_AdjustorThunk },
	{ 0x06000224, HumanLimit_get_max_m4E5E907AE7FFFCAC65026ECA444507B0B608F02A_AdjustorThunk },
	{ 0x06000225, HumanLimit_set_max_m68A852091164B5EA6CD138615DEC75EC9917DA78_AdjustorThunk },
	{ 0x06000226, HumanLimit_get_center_m6F488F439245F5E54D0DFBE04CD63816BAFEFF6B_AdjustorThunk },
	{ 0x06000227, HumanLimit_set_center_mC1C73D9F6B3EFADCF99E6906A0D464146F2FCDF8_AdjustorThunk },
	{ 0x06000228, HumanLimit_get_axisLength_m9691117C17DFCC40ECB9C1A459CE998831678947_AdjustorThunk },
	{ 0x06000229, HumanLimit_set_axisLength_m7DACA3E1AA03B9733E0C1D34051859A45D5B8FB3_AdjustorThunk },
	{ 0x0600022A, HumanBone_get_boneName_m09C4D3365F4D1E69CD0907DEC3A2298A2CF6E18B_AdjustorThunk },
	{ 0x0600022B, HumanBone_set_boneName_m22857CD9738A623436C8F7A31D51D1EBA4BD8F58_AdjustorThunk },
	{ 0x0600022C, HumanBone_get_humanName_mC5FF6D0EDE66B773EF7E6DD7722E20C07EBCDCF6_AdjustorThunk },
	{ 0x0600022D, HumanBone_set_humanName_m460C6620AEE45FC9601B8D05448DD6C397B12D4B_AdjustorThunk },
	{ 0x0600022E, HumanDescription_get_upperArmTwist_m92E2B3BCA433012179892DE9493CBC5F8ADB8D52_AdjustorThunk },
	{ 0x0600022F, HumanDescription_set_upperArmTwist_m4056390E30A30DCEB48D55D4E536F146E431E100_AdjustorThunk },
	{ 0x06000230, HumanDescription_get_lowerArmTwist_m8F57099DCEAC2B1D0D03F5443F500953E1E086E6_AdjustorThunk },
	{ 0x06000231, HumanDescription_set_lowerArmTwist_mD24F728854AC5AD545E09D6E74CDD0B7AD6DF139_AdjustorThunk },
	{ 0x06000232, HumanDescription_get_upperLegTwist_mB9429634218671A47EAB4E73C4ABCB4EDC1FDCF1_AdjustorThunk },
	{ 0x06000233, HumanDescription_set_upperLegTwist_m0552329018CAC469A2443BFBC89B83DFB5288782_AdjustorThunk },
	{ 0x06000234, HumanDescription_get_lowerLegTwist_m8AB325E825835D0A05A20F447191CF2FBA604B35_AdjustorThunk },
	{ 0x06000235, HumanDescription_set_lowerLegTwist_m477F6F9AEC4E7E3F8D6FA72B05BCBF05C02FFB19_AdjustorThunk },
	{ 0x06000236, HumanDescription_get_armStretch_m9C47D234F9BD5C1807E40DF748AE51CC4433C11B_AdjustorThunk },
	{ 0x06000237, HumanDescription_set_armStretch_m52A43854E332057B62C07306CB1663CEFDB01C71_AdjustorThunk },
	{ 0x06000238, HumanDescription_get_legStretch_m568292F633E3849E7C8DAB4D738C8E6F52AB662F_AdjustorThunk },
	{ 0x06000239, HumanDescription_set_legStretch_m40D8FE0E122F5EF4143253814D732AF8413578DC_AdjustorThunk },
	{ 0x0600023A, HumanDescription_get_feetSpacing_mF8283F0B14F35F3FD82BA3D6FF6D82609B924643_AdjustorThunk },
	{ 0x0600023B, HumanDescription_set_feetSpacing_mC0F12305CA30ADA53D08E987E52DE1611E9511D3_AdjustorThunk },
	{ 0x0600023C, HumanDescription_get_hasTranslationDoF_m735B027773FA7BA045C7B1D5A129B504F7B97896_AdjustorThunk },
	{ 0x0600023D, HumanDescription_set_hasTranslationDoF_mE5E5B11F3DC450708723875A5B1D7CFC340FD20E_AdjustorThunk },
	{ 0x06000256, HumanPose_Init_m9BBDA2B4B2EB0DE3082EE7FE36A92F19870F682D_AdjustorThunk },
	{ 0x060002E4, AnimationClipPlayable__ctor_mF2EE31CC772B100F98CCAE26963059C6C722FA1A_AdjustorThunk },
	{ 0x060002E5, AnimationClipPlayable_GetHandle_mE775F2247901BA293DB01A8D384D3F9D02A25627_AdjustorThunk },
	{ 0x060002E8, AnimationClipPlayable_Equals_mC5263BEA86C02CEDF93C5B14EAA168883E1DB5F4_AdjustorThunk },
	{ 0x060002E9, AnimationClipPlayable_GetAnimationClip_m65C9B50E705936C7E6B3F42A2BD71B704D3D0E1D_AdjustorThunk },
	{ 0x060002EA, AnimationClipPlayable_GetApplyFootIK_m3E599D05D6A40BEFD651618CE5DDA03F15A3610F_AdjustorThunk },
	{ 0x060002EB, AnimationClipPlayable_SetApplyFootIK_m7CBA77F56815AD21784AC53D9EBDAE18AFA48507_AdjustorThunk },
	{ 0x060002EC, AnimationClipPlayable_GetApplyPlayableIK_m04EE9E4136AC350F27814DF3B006238260CF3EE9_AdjustorThunk },
	{ 0x060002ED, AnimationClipPlayable_SetApplyPlayableIK_m69A6F6E28EB250956E27C1720A0A842848F54DAB_AdjustorThunk },
	{ 0x060002EE, AnimationClipPlayable_GetRemoveStartOffset_m52EBB080BD11079E3D2F2AD6E913E5451F24CFE2_AdjustorThunk },
	{ 0x060002EF, AnimationClipPlayable_SetRemoveStartOffset_mBAC88E40F6A759FACA4105EF683181D43381C8E5_AdjustorThunk },
	{ 0x060002F0, AnimationClipPlayable_GetOverrideLoopTime_m22078B967400E8B3E5D056BCE1704CBB2E1E2C93_AdjustorThunk },
	{ 0x060002F1, AnimationClipPlayable_SetOverrideLoopTime_mF1F57940D8DDBCC6EBCB75A27C2372BB39DED177_AdjustorThunk },
	{ 0x060002F2, AnimationClipPlayable_GetLoopTime_mD57482D47862960C3A2D0185E8F75642DD858FEC_AdjustorThunk },
	{ 0x060002F3, AnimationClipPlayable_SetLoopTime_m3AAA1134C4D339C84EF57FE289D33100D4971ED8_AdjustorThunk },
	{ 0x060002F4, AnimationClipPlayable_GetSampleRate_m0559EF225E427721E66976BF290A0C53D1FFA744_AdjustorThunk },
	{ 0x060002F5, AnimationClipPlayable_SetSampleRate_m2D7C98FD996AC3A582578A4433C9438D0675020C_AdjustorThunk },
	{ 0x06000305, AnimationHumanStream_get_isValid_mFC26001D2772FFDE3C791A764954BD9A1512DD6C_AdjustorThunk },
	{ 0x06000306, AnimationHumanStream_ThrowIfInvalid_m35ADC8567F7DFB74AA71C4E29572E06A8223DE65_AdjustorThunk },
	{ 0x06000307, AnimationHumanStream_get_humanScale_mD8924C6D1BD6723AB50A52D89FFFC92855BC47C6_AdjustorThunk },
	{ 0x06000308, AnimationHumanStream_get_leftFootHeight_mC29F681BA1B4712AD46F911D902BE07CF8FB78C7_AdjustorThunk },
	{ 0x06000309, AnimationHumanStream_get_rightFootHeight_mA2AB707DDD51031FAC763BA1610C2BFCDD80AFBE_AdjustorThunk },
	{ 0x0600030A, AnimationHumanStream_get_bodyLocalPosition_mA8B1A8A9625C388B7E4E12BE814B4AA1D8D3DC5B_AdjustorThunk },
	{ 0x0600030B, AnimationHumanStream_set_bodyLocalPosition_m5EAC7202D30F117B8821BDDC5282FF19FBB93706_AdjustorThunk },
	{ 0x0600030C, AnimationHumanStream_get_bodyLocalRotation_m6224C03E8D34FAEB50C732CB7EBA7E2328A8EAD6_AdjustorThunk },
	{ 0x0600030D, AnimationHumanStream_set_bodyLocalRotation_m19E818A4DDF88A4BCC520011C5F9864657F106B4_AdjustorThunk },
	{ 0x0600030E, AnimationHumanStream_get_bodyPosition_mFA136469110BADCEE3EC2821AD408108A7F3516A_AdjustorThunk },
	{ 0x0600030F, AnimationHumanStream_set_bodyPosition_mEFD7D98A88B79674702D53D659A2338D5E56F02C_AdjustorThunk },
	{ 0x06000310, AnimationHumanStream_get_bodyRotation_m6C3E24BCFC5B10524FD8143D33706365CFF2517C_AdjustorThunk },
	{ 0x06000311, AnimationHumanStream_set_bodyRotation_m16999012B4CEE244F4596C78D98E12B649302846_AdjustorThunk },
	{ 0x06000312, AnimationHumanStream_GetMuscle_mD12DF41EA39BB88236B6E51C6E60DC505CD3B578_AdjustorThunk },
	{ 0x06000313, AnimationHumanStream_SetMuscle_mF6E7B3FB78B9E085ADBFA3F5959C065BBDA5F481_AdjustorThunk },
	{ 0x06000314, AnimationHumanStream_get_leftFootVelocity_m7FA5AA97C252763E010294C1B3F820ECC070E2D5_AdjustorThunk },
	{ 0x06000315, AnimationHumanStream_get_rightFootVelocity_mE89F940C04F0842CD0A2BBCF394B657FB4080EEB_AdjustorThunk },
	{ 0x06000316, AnimationHumanStream_ResetToStancePose_m6CB3AAA301BCBA6ABA5A1DC4EE8767301AFDEAC7_AdjustorThunk },
	{ 0x06000317, AnimationHumanStream_GetGoalPositionFromPose_m2773EC1B9E600BD95B53DB65745A53468EF3820D_AdjustorThunk },
	{ 0x06000318, AnimationHumanStream_GetGoalRotationFromPose_m0D29BD6D2246DF74E1EFD77D909550A1E8CB96C8_AdjustorThunk },
	{ 0x06000319, AnimationHumanStream_GetGoalLocalPosition_mAE43F299D80C2E9DF69E41BC0394CCCEABC079FB_AdjustorThunk },
	{ 0x0600031A, AnimationHumanStream_SetGoalLocalPosition_mA10C3B87B9FCB49197FED94B68BB72415815527A_AdjustorThunk },
	{ 0x0600031B, AnimationHumanStream_GetGoalLocalRotation_m786FED58DB427635329550B3216AACA3972059FB_AdjustorThunk },
	{ 0x0600031C, AnimationHumanStream_SetGoalLocalRotation_mD853EA0BAF5C880FBC6EC57AD5F0D667F241168E_AdjustorThunk },
	{ 0x0600031D, AnimationHumanStream_GetGoalPosition_m9B04E0871FAE6225F9715C7388B334A31834014B_AdjustorThunk },
	{ 0x0600031E, AnimationHumanStream_SetGoalPosition_mC32B190C32B2BFD1FD098C8FF2E81BCE08658A55_AdjustorThunk },
	{ 0x0600031F, AnimationHumanStream_GetGoalRotation_m3F754F7E38B1A0E7CC6D4F5517F68FD0017AFA05_AdjustorThunk },
	{ 0x06000320, AnimationHumanStream_SetGoalRotation_m0AC3D5B3539CCEC2C715CEF37AF96AD91411ED54_AdjustorThunk },
	{ 0x06000321, AnimationHumanStream_SetGoalWeightPosition_m31CDD1019E6EC4583B13310A3D6B42A3129CCE29_AdjustorThunk },
	{ 0x06000322, AnimationHumanStream_SetGoalWeightRotation_m9E28713D497E4871CD4513FC0738642F36021009_AdjustorThunk },
	{ 0x06000323, AnimationHumanStream_GetGoalWeightPosition_m97C54429B8000F50C0C8D7BD695B1D6A92A4C376_AdjustorThunk },
	{ 0x06000324, AnimationHumanStream_GetGoalWeightRotation_m156696D9925EBDEEF0114C79FE8E2A3FAA07AC4D_AdjustorThunk },
	{ 0x06000325, AnimationHumanStream_GetHintPosition_m8C48689F4E696E601892CDC959B0C0BDBD67BBBB_AdjustorThunk },
	{ 0x06000326, AnimationHumanStream_SetHintPosition_m7C26134F50A8C6786430A0A5382884E55549112A_AdjustorThunk },
	{ 0x06000327, AnimationHumanStream_SetHintWeightPosition_m00D5FD24F69AC10C7B133ACAFB71BB2362E4E635_AdjustorThunk },
	{ 0x06000328, AnimationHumanStream_GetHintWeightPosition_m0D51CD0A32B87BA0208942ABC3DBF3AC0FB8FE9D_AdjustorThunk },
	{ 0x06000329, AnimationHumanStream_SetLookAtPosition_m6C0D265D80FF6FD89159A9F9D47172B37093D2A8_AdjustorThunk },
	{ 0x0600032A, AnimationHumanStream_SetLookAtClampWeight_mC76D8266ABA59385337106B48BF2F5019DEC29CA_AdjustorThunk },
	{ 0x0600032B, AnimationHumanStream_SetLookAtBodyWeight_m4A5D39D1BB635A01DF98E65F45AD873E44236BCE_AdjustorThunk },
	{ 0x0600032C, AnimationHumanStream_SetLookAtHeadWeight_m8E9632C51EB7FC316FF4DB3F1639D41CB20474E0_AdjustorThunk },
	{ 0x0600032D, AnimationHumanStream_SetLookAtEyesWeight_mD5FC3552642F7A64F794EC8D0872972F8A6AD507_AdjustorThunk },
	{ 0x0600032E, AnimationHumanStream_SolveIK_m36549A812DA9901B415ABF3A9BB40DA236C625F3_AdjustorThunk },
	{ 0x0600032F, AnimationHumanStream_GetHumanScale_m528C523BF55034961049D455994749AEF778117E_AdjustorThunk },
	{ 0x06000330, AnimationHumanStream_GetFootHeight_m73B1AC0DEA1287024D0EA1D53F9303E0B8A358A6_AdjustorThunk },
	{ 0x06000331, AnimationHumanStream_InternalResetToStancePose_m1EEC3F6EC405EEB4D36C2CC2D15BFA5CACE50144_AdjustorThunk },
	{ 0x06000332, AnimationHumanStream_InternalGetGoalPositionFromPose_mF9D079DA1E5D728A79637826DBDD8FB7A068E2F3_AdjustorThunk },
	{ 0x06000333, AnimationHumanStream_InternalGetGoalRotationFromPose_m6689F3C3DAE9513AE06956C93368334B64D95291_AdjustorThunk },
	{ 0x06000334, AnimationHumanStream_InternalGetBodyLocalPosition_mFB26EABB0EB551CFA4232D32065A79BF36E356C7_AdjustorThunk },
	{ 0x06000335, AnimationHumanStream_InternalSetBodyLocalPosition_mC469D6040AB3305AE399215925156806A48F1220_AdjustorThunk },
	{ 0x06000336, AnimationHumanStream_InternalGetBodyLocalRotation_mB5A3ABB9FDC0C71BA5FF77D111CCA40C681BD631_AdjustorThunk },
	{ 0x06000337, AnimationHumanStream_InternalSetBodyLocalRotation_mCC66C848E64D04B20A901D7D0760BB2D794B39C3_AdjustorThunk },
	{ 0x06000338, AnimationHumanStream_InternalGetBodyPosition_m549D7BB8B6B6BBA95AA6E04259D975CF81F99849_AdjustorThunk },
	{ 0x06000339, AnimationHumanStream_InternalSetBodyPosition_m0E963FC02C014FF1D519356DAF90E1E37D66DDBA_AdjustorThunk },
	{ 0x0600033A, AnimationHumanStream_InternalGetBodyRotation_m533F3431386D21B1A170378120A34A94D76DF121_AdjustorThunk },
	{ 0x0600033B, AnimationHumanStream_InternalSetBodyRotation_mB72C4A04EF45AA6E8CC9E52638FB65766885D34C_AdjustorThunk },
	{ 0x0600033C, AnimationHumanStream_InternalGetMuscle_mCB75C7C1E13D6B7C0ABBCD4270E94DD025CC876E_AdjustorThunk },
	{ 0x0600033D, AnimationHumanStream_InternalSetMuscle_mCC068A0689AB992DB22C4178AEA8390E3F49119E_AdjustorThunk },
	{ 0x0600033E, AnimationHumanStream_GetLeftFootVelocity_mB62C9F36949F43F881DC225B5AD92B010E7961D7_AdjustorThunk },
	{ 0x0600033F, AnimationHumanStream_GetRightFootVelocity_mEE89EE87CFD969E2ABE2FF6A2FABFCB651489F28_AdjustorThunk },
	{ 0x06000340, AnimationHumanStream_InternalGetGoalLocalPosition_m53C7592FE48A5E61D96031CA44D075F3C64E2A44_AdjustorThunk },
	{ 0x06000341, AnimationHumanStream_InternalSetGoalLocalPosition_mAFA13881BA09B0CFFA788B24088CE98A4EFC387E_AdjustorThunk },
	{ 0x06000342, AnimationHumanStream_InternalGetGoalLocalRotation_m223B1725AA46FE23E693AB1E2FD20E96AD9D578A_AdjustorThunk },
	{ 0x06000343, AnimationHumanStream_InternalSetGoalLocalRotation_mCE4ED1A96EBE0BED4FDE8394BD1049CF857970A1_AdjustorThunk },
	{ 0x06000344, AnimationHumanStream_InternalGetGoalPosition_m25C63A96C6858BF176670F3FC620BFF1672FC468_AdjustorThunk },
	{ 0x06000345, AnimationHumanStream_InternalSetGoalPosition_mAFBF83E782A12A0BEBDD63C2BA165541C5AC3029_AdjustorThunk },
	{ 0x06000346, AnimationHumanStream_InternalGetGoalRotation_m48632794B5A9E479ED6194902347E52CC072E4F0_AdjustorThunk },
	{ 0x06000347, AnimationHumanStream_InternalSetGoalRotation_m3810FFA6A48991BA97DC9CE844C8C04104607681_AdjustorThunk },
	{ 0x06000348, AnimationHumanStream_InternalSetGoalWeightPosition_mA9E5685E3F30AED1A5AE87E1135BF5047DA8D326_AdjustorThunk },
	{ 0x06000349, AnimationHumanStream_InternalSetGoalWeightRotation_m8685321C9439B4DA865675908633BDD952CDA8DB_AdjustorThunk },
	{ 0x0600034A, AnimationHumanStream_InternalGetGoalWeightPosition_mFB2155CD3EEE62EDC9176A80EDBECA6D1E0BD6CD_AdjustorThunk },
	{ 0x0600034B, AnimationHumanStream_InternalGetGoalWeightRotation_mF2E03DAB54D65B15ABF65FD68C39EB48928BE9C8_AdjustorThunk },
	{ 0x0600034C, AnimationHumanStream_InternalGetHintPosition_m3D8100E5F06F8C05F717DD1CD9A424DD26396434_AdjustorThunk },
	{ 0x0600034D, AnimationHumanStream_InternalSetHintPosition_m7DC7D373633BD22E372BEE6F02E662A3C2C278BC_AdjustorThunk },
	{ 0x0600034E, AnimationHumanStream_InternalSetHintWeightPosition_mD6136E04EDF9E591CEAAF8AB09919DCCE54553FD_AdjustorThunk },
	{ 0x0600034F, AnimationHumanStream_InternalGetHintWeightPosition_m4F89A6FD73DEA7166E91D0ECEE416BAEE989982E_AdjustorThunk },
	{ 0x06000350, AnimationHumanStream_InternalSetLookAtPosition_m6DC48530B5D259E37BEB489DCD2BBF07D8CB832E_AdjustorThunk },
	{ 0x06000351, AnimationHumanStream_InternalSetLookAtClampWeight_mBA29A943B22AA5DD8917EB5EB9589F04E76D6857_AdjustorThunk },
	{ 0x06000352, AnimationHumanStream_InternalSetLookAtBodyWeight_m614FEABDB4E114CDA84A94333AA7E2BF9ED4BDF7_AdjustorThunk },
	{ 0x06000353, AnimationHumanStream_InternalSetLookAtHeadWeight_m184F2F1D223714F0FD5B529C89207D9F6C0CDF92_AdjustorThunk },
	{ 0x06000354, AnimationHumanStream_InternalSetLookAtEyesWeight_m99A49670FE2C24ADFEA9DBBBA4135BDDA93EFE1B_AdjustorThunk },
	{ 0x06000355, AnimationHumanStream_InternalSolveIK_m84F06C829271E6F0B15B7653BD35A26A2D18046E_AdjustorThunk },
	{ 0x06000381, AnimationLayerMixerPlayable__ctor_m28884B8B9F7E057DF947E3B43ED78EA107368BD6_AdjustorThunk },
	{ 0x06000382, AnimationLayerMixerPlayable_GetHandle_m324A98D0B0BFC0441377D65CAE93C914F828721F_AdjustorThunk },
	{ 0x06000385, AnimationLayerMixerPlayable_Equals_mA5D24E61E2DE1140B409F3B569DBA3C185751970_AdjustorThunk },
	{ 0x06000386, AnimationLayerMixerPlayable_IsLayerAdditive_m379268A18CFAD74371F6D4E0467072761BF84713_AdjustorThunk },
	{ 0x06000387, AnimationLayerMixerPlayable_SetLayerAdditive_m3B35E03C224B118E3F3D9E8A7B697AF570FBFB6E_AdjustorThunk },
	{ 0x06000388, AnimationLayerMixerPlayable_SetLayerMaskFromAvatarMask_mC4BDE2B476AC13C31053100085FAF6BC86000280_AdjustorThunk },
	{ 0x06000394, AnimationMixerPlayable__ctor_mBF84CC064549C2C00B2AE1174018335958EB7EA7_AdjustorThunk },
	{ 0x06000395, AnimationMixerPlayable_GetHandle_mBA6CEB1579A713A985D474E75BC282728318882F_AdjustorThunk },
	{ 0x06000398, AnimationMixerPlayable_Equals_m6EBE215636EEEA3196A43F4D6C1FE6DD704AFA4E_AdjustorThunk },
	{ 0x0600039F, AnimationMotionXToDeltaPlayable__ctor_mDE3C14B4B975AC693669D66B6E41BB6432AFA940_AdjustorThunk },
	{ 0x060003A0, AnimationMotionXToDeltaPlayable_GetHandle_m09F605E78AD7F0135C7F57EB048031091A50E3A2_AdjustorThunk },
	{ 0x060003A3, AnimationMotionXToDeltaPlayable_Equals_m7CBF3B7618EDBA4ECC2F3C2F54011248BC45CDCC_AdjustorThunk },
	{ 0x060003A4, AnimationMotionXToDeltaPlayable_IsAbsoluteMotion_mCFE89CD743EC61AF6EF6117F72A36FCC26216487_AdjustorThunk },
	{ 0x060003A5, AnimationMotionXToDeltaPlayable_SetAbsoluteMotion_m5D1B029F6E6BFFB521CC6CB72ACBE7EA27B28715_AdjustorThunk },
	{ 0x060003AE, AnimationOffsetPlayable__ctor_mBF3AC6493556DAAEF608B359BEBE8FA6D9F8DBFD_AdjustorThunk },
	{ 0x060003AF, AnimationOffsetPlayable_GetHandle_m769BEFF90379AEAB0C579F7800953458CE3EBA78_AdjustorThunk },
	{ 0x060003B2, AnimationOffsetPlayable_Equals_mEC28392ADD4E9639EB9228D106D93E21B3587270_AdjustorThunk },
	{ 0x060003B3, AnimationOffsetPlayable_GetPosition_m1DED0A8F334427F11E32E844C6AB62BB0DBBE247_AdjustorThunk },
	{ 0x060003B4, AnimationOffsetPlayable_SetPosition_mB3D98093AB2BDE7454642F90CDE62C71ED6B9F19_AdjustorThunk },
	{ 0x060003B5, AnimationOffsetPlayable_GetRotation_m17F5666C4A8EF5033B545A3ED3769F8BD4702818_AdjustorThunk },
	{ 0x060003B6, AnimationOffsetPlayable_SetRotation_m042FD5D84E348BEAE49AFCC84DDD14861A8C1AEC_AdjustorThunk },
	{ 0x060003CC, AnimationPlayableOutput__ctor_mE4FB8AA6DFB2F3C18E04A9317F5CE53597A7D22A_AdjustorThunk },
	{ 0x060003CE, AnimationPlayableOutput_GetHandle_m2A8E2A9CBD12EDCF48FC946445AB42802083338D_AdjustorThunk },
	{ 0x060003D1, AnimationPlayableOutput_GetTarget_m2E55D32775E0B27063F697346701A0EB5424DA9D_AdjustorThunk },
	{ 0x060003D2, AnimationPlayableOutput_SetTarget_m0F7745C4A721D76EB1E804AA48E70C9C798E0DCE_AdjustorThunk },
	{ 0x060003D8, AnimationPosePlayable__ctor_mC6C096785918358CA7EC12BABCDF4BBD47F7BA3F_AdjustorThunk },
	{ 0x060003D9, AnimationPosePlayable_GetHandle_m5DC7CA4CAF3CD525D454D99EBC3D12C3571B527B_AdjustorThunk },
	{ 0x060003DC, AnimationPosePlayable_Equals_m10F1E7DD7037B2AB3F7DAE3E01A1DC843EABD0A3_AdjustorThunk },
	{ 0x060003DD, AnimationPosePlayable_GetMustReadPreviousPose_m6C3C23E75C7EEB05DB84E83507F94ED09C1DF961_AdjustorThunk },
	{ 0x060003DE, AnimationPosePlayable_SetMustReadPreviousPose_m3859165529333B3140AE4E9097FE66E992DA8AF7_AdjustorThunk },
	{ 0x060003DF, AnimationPosePlayable_GetReadDefaultPose_mA4E24ADBB4A96DD62D17F50ADECD5F114D44C5B8_AdjustorThunk },
	{ 0x060003E0, AnimationPosePlayable_SetReadDefaultPose_m852C2742CE8CFF49FF7216DA9D2B9C755C41AA20_AdjustorThunk },
	{ 0x060003E1, AnimationPosePlayable_GetApplyFootIK_mDBE93A57F35A70EDE6F8503323EF2F7C5CADE32F_AdjustorThunk },
	{ 0x060003E2, AnimationPosePlayable_SetApplyFootIK_m862FA48CF186206F9B488BFADE8C1866A1A863C1_AdjustorThunk },
	{ 0x060003EF, AnimationRemoveScalePlayable__ctor_m4D6C7C4AB8E078050B0CC34C6732051CF043CFA2_AdjustorThunk },
	{ 0x060003F0, AnimationRemoveScalePlayable_GetHandle_mFFA58B879F31327187A20ED30E1C814B7BEAA9C6_AdjustorThunk },
	{ 0x060003F3, AnimationRemoveScalePlayable_Equals_m0ACDD59B80103591DA8E84CB387FB10778D8C327_AdjustorThunk },
	{ 0x060003FA, AnimationScriptPlayable__ctor_m6DEFD72735E79009FC1484AA2A7A82E6CE601247_AdjustorThunk },
	{ 0x060003FB, AnimationScriptPlayable_GetHandle_m30355B6EE1AA3BA36D628251FB4291386D223646_AdjustorThunk },
	{ 0x06000401, AnimationScriptPlayable_Equals_mAD02E40704CBE4AB188DE0569052F8EA9864F4E4_AdjustorThunk },
	{ 0x06000402, AnimationScriptPlayable_SetProcessInputs_mF7DE3561731A6513E8FE0F0A85B0C38959621839_AdjustorThunk },
	{ 0x06000403, AnimationScriptPlayable_GetProcessInputs_mDF9A5B45217407FB9ED7DF8E7A1E3DEA4DD31A1F_AdjustorThunk },
	{ 0x0600040B, AnimationStream_get_animatorBindingsVersion_mD7D19DCE96F93CE4DC36457F974C5B8562A3B5E4_AdjustorThunk },
	{ 0x0600040C, AnimationStream_get_isValid_mE1F032BDA653D5A903DCD427F4677A6C9C4C227A_AdjustorThunk },
	{ 0x0600040D, AnimationStream_CheckIsValid_m6C46800E1A5A4BE27FF761A93F72BC3CD751174C_AdjustorThunk },
	{ 0x0600040E, AnimationStream_get_deltaTime_mECEF75B188313080405BFB556AB4CFD972233861_AdjustorThunk },
	{ 0x0600040F, AnimationStream_get_velocity_m8628858DC7927B5DC15C3050945D8150E4166179_AdjustorThunk },
	{ 0x06000410, AnimationStream_set_velocity_m0C9F3849570576287D94FEC72556BA070F9B3D49_AdjustorThunk },
	{ 0x06000411, AnimationStream_get_angularVelocity_m3D7FCC57BD7A9EE99FE9DDAB3B425C700841C617_AdjustorThunk },
	{ 0x06000412, AnimationStream_set_angularVelocity_m0600FEEDAC435358DAE51BC5AA4EEAEED6649DD8_AdjustorThunk },
	{ 0x06000413, AnimationStream_get_rootMotionPosition_mAE5E4330D95DD4AB9BD65341CCB4340259235A40_AdjustorThunk },
	{ 0x06000414, AnimationStream_get_rootMotionRotation_m0CE33CEDB1195D26AE380A561A487CBB508A0530_AdjustorThunk },
	{ 0x06000415, AnimationStream_get_isHumanStream_mABF4806846E32130D5D4C204CAE4E8E0DA897158_AdjustorThunk },
	{ 0x06000416, AnimationStream_AsHuman_mADFB59FE0BC7C00348B567BE06A4E8E9C830A916_AdjustorThunk },
	{ 0x06000417, AnimationStream_get_inputStreamCount_m434C3915F96FC4C707622AA6F8F40F5BEF0031D0_AdjustorThunk },
	{ 0x06000418, AnimationStream_GetInputStream_mFFAEDE4760FD9FB17C6F702DB3E1835C77600E49_AdjustorThunk },
	{ 0x06000419, AnimationStream_GetInputWeight_mE168D42B18134F5B6F11B932C9EF3D523B4759DA_AdjustorThunk },
	{ 0x0600041A, AnimationStream_CopyAnimationStreamMotion_mA5E2C0B009574DF88C47B57A3ABAFC6C20FA3EB7_AdjustorThunk },
	{ 0x0600041B, AnimationStream_ReadSceneTransforms_mE817BB3693A822A761DFF489AC5661F93E076CCD_AdjustorThunk },
	{ 0x0600041C, AnimationStream_WriteSceneTransforms_mD0A8D14E6A70F2DD4639E1D265F3D28961B8401D_AdjustorThunk },
	{ 0x0600041D, AnimationStream_CopyAnimationStreamMotionInternal_m47DAF2063EEF6D09F538BD3A9E039DD01C68243F_AdjustorThunk },
	{ 0x0600041E, AnimationStream_GetDeltaTime_m335ACEAEEAEE7E3FAE1CCBD81DA839C6C1CFF0A9_AdjustorThunk },
	{ 0x0600041F, AnimationStream_GetIsHumanStream_mD58FA1E5EEF324695C45C8AAF3A79D13987DE576_AdjustorThunk },
	{ 0x06000420, AnimationStream_GetVelocity_m554A288784E59A32E2D39B826912890127AAC57F_AdjustorThunk },
	{ 0x06000421, AnimationStream_SetVelocity_m66A0FF467A4423DD1BEE265EADBEE273CB764382_AdjustorThunk },
	{ 0x06000422, AnimationStream_GetAngularVelocity_mCEAA43EE55D952A83B0AC4236DFFCD249DC8587B_AdjustorThunk },
	{ 0x06000423, AnimationStream_SetAngularVelocity_m6860A649F2D2A7B7082502753E5B94C12A10640A_AdjustorThunk },
	{ 0x06000424, AnimationStream_GetRootMotionPosition_mF64C52D3BC86F9B31E50426ABA638C5FF6E59D22_AdjustorThunk },
	{ 0x06000425, AnimationStream_GetRootMotionRotation_m4132427788382A623CF0703FF56AC34D6E31C1FD_AdjustorThunk },
	{ 0x06000426, AnimationStream_GetInputStreamCount_m3E9B20B8AA6D1A6704FEF58B384ECCDCC67C3624_AdjustorThunk },
	{ 0x06000427, AnimationStream_InternalGetInputStream_m19EEF4A1AA1BF053B21D5059E3836699D55A1974_AdjustorThunk },
	{ 0x06000428, AnimationStream_InternalGetInputWeight_mB4CE342A125415612BA0D6CF329EFE4DD15DC503_AdjustorThunk },
	{ 0x06000429, AnimationStream_GetHumanStream_m719D5DCDC0B6B4CC92E93BDFAA51D138A34DCC1F_AdjustorThunk },
	{ 0x0600042A, AnimationStream_InternalReadSceneTransforms_m3CB8D564DC110A6A7475FA9F00C95D4294CAC588_AdjustorThunk },
	{ 0x0600042B, AnimationStream_InternalWriteSceneTransforms_mA489D8D3B1B02D7455BF7FBDFF4D76409878ACFD_AdjustorThunk },
	{ 0x0600043B, TransformStreamHandle_IsValid_m96EE3A490B88868890CF2E754838F97424A65512_AdjustorThunk },
	{ 0x0600043C, TransformStreamHandle_IsValidInternal_mBF1602E33ABCA25121C7CF70173D29C5291354CC_AdjustorThunk },
	{ 0x0600043D, TransformStreamHandle_get_createdByNative_mCC27504004588C367456D55E8295B745BE2431AC_AdjustorThunk },
	{ 0x0600043E, TransformStreamHandle_IsSameVersionAsStream_m31E41B516413440AC8F4D5F9F233623A6DE71365_AdjustorThunk },
	{ 0x0600043F, TransformStreamHandle_get_hasHandleIndex_m164F6D37B1A6B74214B09E1E6798C275C71716D1_AdjustorThunk },
	{ 0x06000440, TransformStreamHandle_get_hasSkeletonIndex_m8B9589FACB6810B0EFD84033D30057ADFBC4B75F_AdjustorThunk },
	{ 0x06000441, TransformStreamHandle_set_animatorBindingsVersion_m5F9ED42B51BED505332D0D1B88CB823A8BEF3A01_AdjustorThunk },
	{ 0x06000442, TransformStreamHandle_get_animatorBindingsVersion_mD044F88843A162A554BA7EF191E52B58F9F0AFF8_AdjustorThunk },
	{ 0x06000443, TransformStreamHandle_Resolve_m5DDC5761EF01E700ABDB214030802982DABC3E6E_AdjustorThunk },
	{ 0x06000444, TransformStreamHandle_IsResolved_mB2A9548AAB37C1485AFEC2C9CD9A3D4ABC786D52_AdjustorThunk },
	{ 0x06000445, TransformStreamHandle_IsResolvedInternal_m83781A03679DF4C678FE963CF21F5A2203471585_AdjustorThunk },
	{ 0x06000446, TransformStreamHandle_CheckIsValidAndResolve_m7602706A5D46D99268DB6C698A6752C96A0525F6_AdjustorThunk },
	{ 0x06000447, TransformStreamHandle_GetPosition_m8980B6C6185653E9B962625D030C3BB1994C8B89_AdjustorThunk },
	{ 0x06000448, TransformStreamHandle_SetPosition_m45609A840DAAF0410F72218E58E2207841290002_AdjustorThunk },
	{ 0x06000449, TransformStreamHandle_GetRotation_m02E0CE9B403FB4138605190A48A19767D6B0C42A_AdjustorThunk },
	{ 0x0600044A, TransformStreamHandle_SetRotation_mA159153895AFFB08B30B8287304A720215C364D1_AdjustorThunk },
	{ 0x0600044B, TransformStreamHandle_GetLocalPosition_m30A1BF0A06551177E6D28A73D3DE71522B77C0A4_AdjustorThunk },
	{ 0x0600044C, TransformStreamHandle_SetLocalPosition_mEC97D6C69019B8212F444B33DB51BDCD9DCD6282_AdjustorThunk },
	{ 0x0600044D, TransformStreamHandle_GetLocalRotation_mB613F5958303751C9368AD2CC613723E279985AF_AdjustorThunk },
	{ 0x0600044E, TransformStreamHandle_SetLocalRotation_m68B0586FA34978971ECDC909A44E1E0C13443C6A_AdjustorThunk },
	{ 0x0600044F, TransformStreamHandle_GetLocalScale_m559039B8F2285CC33E3E952F078EF899C8ACB451_AdjustorThunk },
	{ 0x06000450, TransformStreamHandle_SetLocalScale_mEAC5ED65AA0B8F756E2C129ED14D78C7B3698FE2_AdjustorThunk },
	{ 0x06000451, TransformStreamHandle_GetLocalToParentMatrix_m91AC76F7B4D7B530A6D30E87E68E75C50B95DA77_AdjustorThunk },
	{ 0x06000452, TransformStreamHandle_GetPositionReadMask_m2901E4573FC6105194851C3FD1B88A3F18DA4F5F_AdjustorThunk },
	{ 0x06000453, TransformStreamHandle_GetRotationReadMask_m8D17943EA0E9F130DA1B5A2CE336625AAC7E4888_AdjustorThunk },
	{ 0x06000454, TransformStreamHandle_GetScaleReadMask_m12F8441C4AE4B23345BA37F5DFD82B5288695230_AdjustorThunk },
	{ 0x06000455, TransformStreamHandle_GetLocalTRS_mF633398360834FAD1B1F8E21EF8C2A01B3E38A8D_AdjustorThunk },
	{ 0x06000456, TransformStreamHandle_SetLocalTRS_mA4D470AC9B87FF6FAC880A926BD3A5F4EC30BFB2_AdjustorThunk },
	{ 0x06000457, TransformStreamHandle_GetGlobalTR_mA0526AC698E96B95E6BE3E17A477DB028EF8A499_AdjustorThunk },
	{ 0x06000458, TransformStreamHandle_GetLocalToWorldMatrix_m19D802014D758A8BE531FE3A7000371D59C5B195_AdjustorThunk },
	{ 0x06000459, TransformStreamHandle_SetGlobalTR_m8C4F35DE8E639AE7D7F94F1D015AD3C16D2FC406_AdjustorThunk },
	{ 0x0600045A, TransformStreamHandle_ResolveInternal_m0008C8228981E9247DA8B0C7739DD1BF1C70EAEA_AdjustorThunk },
	{ 0x0600045B, TransformStreamHandle_GetPositionInternal_m4D0EA1C47F1AAB4723411247DBA15135BA6A9D4C_AdjustorThunk },
	{ 0x0600045C, TransformStreamHandle_SetPositionInternal_m0855C3D765D6635BFEBA847061CDC90B645246CC_AdjustorThunk },
	{ 0x0600045D, TransformStreamHandle_GetRotationInternal_m53A7E32CE6B63F588F44CAE8FCBF23C32E8393C7_AdjustorThunk },
	{ 0x0600045E, TransformStreamHandle_SetRotationInternal_mE7AB735A2303DC923A69B19537FDE60B5F39CE5A_AdjustorThunk },
	{ 0x0600045F, TransformStreamHandle_GetLocalPositionInternal_mDF78249F5365FA56D51A9854D69DBD420CA2408A_AdjustorThunk },
	{ 0x06000460, TransformStreamHandle_SetLocalPositionInternal_m1758971CB7DC05A269612D1B975A22C8CB2CB890_AdjustorThunk },
	{ 0x06000461, TransformStreamHandle_GetLocalRotationInternal_m5AD8291814578D1F199FBBD2E336C43FC387CEAB_AdjustorThunk },
	{ 0x06000462, TransformStreamHandle_SetLocalRotationInternal_mDE8F5BF1C73A90573EF3918F1C88ABB73BC10778_AdjustorThunk },
	{ 0x06000463, TransformStreamHandle_GetLocalScaleInternal_mAE69D739C71A1F9AB26E9E3496294B6662F429A2_AdjustorThunk },
	{ 0x06000464, TransformStreamHandle_SetLocalScaleInternal_mE108B8F9D39C2C540C8619C7DECFDB685040F85C_AdjustorThunk },
	{ 0x06000465, TransformStreamHandle_GetLocalToParentMatrixInternal_m4D7A1D9602675F7998C24CCDAB752F8E7BAC8DBF_AdjustorThunk },
	{ 0x06000466, TransformStreamHandle_GetPositionReadMaskInternal_m18D7DFE8500F0B2BC42F06324B0286B73F8062FF_AdjustorThunk },
	{ 0x06000467, TransformStreamHandle_GetRotationReadMaskInternal_mB7D7E96C8D8CD0FBED076396FB14955E31CA732D_AdjustorThunk },
	{ 0x06000468, TransformStreamHandle_GetScaleReadMaskInternal_m1D3791F8161BA91D9EC2D5D744B05D33E419FBA4_AdjustorThunk },
	{ 0x06000469, TransformStreamHandle_GetLocalTRSInternal_m1B1B9B973843354BCA7D7A5A76CE44EFE7F2A203_AdjustorThunk },
	{ 0x0600046A, TransformStreamHandle_SetLocalTRSInternal_m2FC862511AEAC5C2900D016CF31EB2E25D321D8B_AdjustorThunk },
	{ 0x0600046B, TransformStreamHandle_GetGlobalTRInternal_m71E4832B7C5D99A91FDF742CA3E54F1C43CE34AF_AdjustorThunk },
	{ 0x0600046C, TransformStreamHandle_GetLocalToWorldMatrixInternal_m218BEFABE5A04FE2DF4F0E038049386C6A8C5EC0_AdjustorThunk },
	{ 0x0600046D, TransformStreamHandle_SetGlobalTRInternal_m9C62E8BD63B362A404C376B09005C024F54DC7B4_AdjustorThunk },
	{ 0x06000482, PropertyStreamHandle_IsValid_m21FEF08137BB2DC014F731A98CBA5F516939723E_AdjustorThunk },
	{ 0x06000483, PropertyStreamHandle_IsValidInternal_mFE619567B465984FC8E00F07CC24D489802BB51B_AdjustorThunk },
	{ 0x06000484, PropertyStreamHandle_get_createdByNative_m2610F75D942E639F8D9919D9A8A8E2210503292A_AdjustorThunk },
	{ 0x06000485, PropertyStreamHandle_IsSameVersionAsStream_m105BA0425054D86214E70C3D0746517A0BBD5305_AdjustorThunk },
	{ 0x06000486, PropertyStreamHandle_get_hasHandleIndex_m296B641953CA1478332DE8D4E3616EDDE67F4415_AdjustorThunk },
	{ 0x06000487, PropertyStreamHandle_get_hasValueArrayIndex_m6BFF272278DB968E4732EE7BAA990F18258DC610_AdjustorThunk },
	{ 0x06000488, PropertyStreamHandle_get_hasBindType_mF482FD67DC2BBB8AF20A959C846430577FCC51B3_AdjustorThunk },
	{ 0x06000489, PropertyStreamHandle_set_animatorBindingsVersion_m091EA76553DCD034CF179B9F31BD25057B83764F_AdjustorThunk },
	{ 0x0600048A, PropertyStreamHandle_get_animatorBindingsVersion_mAF352761E16C2BC5658A2B37C77EFC88173EA4C0_AdjustorThunk },
	{ 0x0600048B, PropertyStreamHandle_Resolve_mE4B9D57D9092E7278EF68192F8E5F57D1ED3B645_AdjustorThunk },
	{ 0x0600048C, PropertyStreamHandle_IsResolved_m44BC7FEA11CAA3862B733E319C7794F1C3536D86_AdjustorThunk },
	{ 0x0600048D, PropertyStreamHandle_IsResolvedInternal_m7B96232330AB117B2050D16FE135103D6ED97DFA_AdjustorThunk },
	{ 0x0600048E, PropertyStreamHandle_CheckIsValidAndResolve_mD4036D6F0444B68BC4C1AECDD7429FEBAF03203C_AdjustorThunk },
	{ 0x0600048F, PropertyStreamHandle_GetFloat_mAEC50079467900B74F7B485BBAF65A4DE1BBB8DF_AdjustorThunk },
	{ 0x06000490, PropertyStreamHandle_SetFloat_m9FA9F67C2AA473A7395EACF2563E7C8A06008C33_AdjustorThunk },
	{ 0x06000491, PropertyStreamHandle_GetInt_m58CECCE1A73DB1EA96E287B5B7BF51BF502D8D98_AdjustorThunk },
	{ 0x06000492, PropertyStreamHandle_SetInt_m1E7A8AF3165E77EB0148AC17FB641711134DBE48_AdjustorThunk },
	{ 0x06000493, PropertyStreamHandle_GetBool_mBB5B008988E6CC47C526CF654E53804909C141E0_AdjustorThunk },
	{ 0x06000494, PropertyStreamHandle_SetBool_m8307F19E9C41A431FC9A71128A305B547BE675C7_AdjustorThunk },
	{ 0x06000495, PropertyStreamHandle_GetReadMask_m70E5969F001669CC1467E03014CF46C5E865F9DE_AdjustorThunk },
	{ 0x06000496, PropertyStreamHandle_ResolveInternal_mFB0A48675D8D847197CB392325CDCA837B82E64C_AdjustorThunk },
	{ 0x06000497, PropertyStreamHandle_GetFloatInternal_m11E03DE3C420D8F9BFA7926D1F452766BD34B783_AdjustorThunk },
	{ 0x06000498, PropertyStreamHandle_SetFloatInternal_m044594EAE3DEEC6030E096DB0A8F0454ADCAD6A8_AdjustorThunk },
	{ 0x06000499, PropertyStreamHandle_GetIntInternal_m598EC48F1700FB43EF1A5880178BB841A781D4C9_AdjustorThunk },
	{ 0x0600049A, PropertyStreamHandle_SetIntInternal_m37894828B9FD37A78CCE5A6F9F9EB7E1C0FE72A4_AdjustorThunk },
	{ 0x0600049B, PropertyStreamHandle_GetBoolInternal_m25D06AA6F53B3265E80243E589F39EF4C30E7DAD_AdjustorThunk },
	{ 0x0600049C, PropertyStreamHandle_SetBoolInternal_mD237DF6939F5BE683D485E984C37791624C67A26_AdjustorThunk },
	{ 0x0600049D, PropertyStreamHandle_GetReadMaskInternal_m28FDDC616FF6A3C9BF19F19FCC9103DD337DF9BF_AdjustorThunk },
	{ 0x060004A6, TransformSceneHandle_IsValid_m11DB3FA1E3137CA1C2D4D4BC18AD717FCCAC65E2_AdjustorThunk },
	{ 0x060004A7, TransformSceneHandle_get_createdByNative_m40C489AAD66DEDFEB69F6BB25B1177FC51922D3D_AdjustorThunk },
	{ 0x060004A8, TransformSceneHandle_get_hasTransformSceneHandleDefinitionIndex_mBD4B49152989D4379E6D726B0F7E834EA484383B_AdjustorThunk },
	{ 0x060004A9, TransformSceneHandle_CheckIsValid_m33214B5950C49A143A5548B7FB1672062204655A_AdjustorThunk },
	{ 0x060004AA, TransformSceneHandle_GetPosition_mDB8261C4AF79828292D555DBF91A6559DE41B3B8_AdjustorThunk },
	{ 0x060004AB, TransformSceneHandle_SetPosition_m2119B1F81EB229CED3914AB214783D67C036EEF8_AdjustorThunk },
	{ 0x060004AC, TransformSceneHandle_GetLocalPosition_mF5AFF93B5129702C280CCEB0603100377BFE2D32_AdjustorThunk },
	{ 0x060004AD, TransformSceneHandle_SetLocalPosition_mA8A45E45843E29DEB53A99827816C7B0BFE01376_AdjustorThunk },
	{ 0x060004AE, TransformSceneHandle_GetRotation_m8F6CA3E2302A43103A808120AEE1C527EB2A2F05_AdjustorThunk },
	{ 0x060004AF, TransformSceneHandle_SetRotation_mB91EC437053AE3E393393C1D6B3F62270AA6DB0F_AdjustorThunk },
	{ 0x060004B0, TransformSceneHandle_GetLocalRotation_mEB7C44D455654A40518C8AE8D8CC0D191E194B8D_AdjustorThunk },
	{ 0x060004B1, TransformSceneHandle_SetLocalRotation_mF7DF58A87432FF06B73AFEB19D4030108F2CFCED_AdjustorThunk },
	{ 0x060004B2, TransformSceneHandle_GetLocalScale_m46D9A20E7DC5967A0EE6F7C217FAA68FBB93611E_AdjustorThunk },
	{ 0x060004B3, TransformSceneHandle_GetLocalTRS_m5FBE0248443B3D34C7787A50DE89BB473638CADB_AdjustorThunk },
	{ 0x060004B4, TransformSceneHandle_GetLocalToParentMatrix_mE9193C3FF11071363E75E8F8BD89A285BFAF3797_AdjustorThunk },
	{ 0x060004B5, TransformSceneHandle_GetGlobalTR_m7CB0885446CA0894BF681F1954C3E0FB8D31C9EB_AdjustorThunk },
	{ 0x060004B6, TransformSceneHandle_GetLocalToWorldMatrix_mF79BCAA91FBE2949EC89A0A010A9AAFCFC2BE9FE_AdjustorThunk },
	{ 0x060004B7, TransformSceneHandle_SetLocalScale_mE408411FD205489EAA1E148B98C2427BF3C201D3_AdjustorThunk },
	{ 0x060004B8, TransformSceneHandle_HasValidTransform_m19ABA61E5902DA7F1207798BBB4DCFBE515B9537_AdjustorThunk },
	{ 0x060004B9, TransformSceneHandle_GetPositionInternal_mAC29651E3EB5FC3BD4CB49B2B09EB9897375FC29_AdjustorThunk },
	{ 0x060004BA, TransformSceneHandle_GetLocalPositionInternal_mAFD402FC292DD1D9BDBF6BE446E89CDD85A47182_AdjustorThunk },
	{ 0x060004BB, TransformSceneHandle_GetRotationInternal_m53DDDEE9D5824A6E5BFEE5C5681A33E468E0FC5E_AdjustorThunk },
	{ 0x060004BC, TransformSceneHandle_GetLocalRotationInternal_mC63DB1B1DA36584AC8C550683E9E2F67F350A688_AdjustorThunk },
	{ 0x060004BD, TransformSceneHandle_GetLocalScaleInternal_m15883C28FC7DE56F00374B6A14F027CFC6C35069_AdjustorThunk },
	{ 0x060004BE, TransformSceneHandle_GetLocalTRSInternal_mBB816EAB9B873A23A8B01C96789480F438968497_AdjustorThunk },
	{ 0x060004BF, TransformSceneHandle_GetLocalToParentMatrixInternal_mAD951B3EF38C5A5F5043E1427B21BEFB5F1265DD_AdjustorThunk },
	{ 0x060004C0, TransformSceneHandle_GetGlobalTRInternal_m821151819DD1E3F5ABA1D2C8DC5372C67A2DF343_AdjustorThunk },
	{ 0x060004C1, TransformSceneHandle_GetLocalToWorldMatrixInternal_m963B53C92A8B6BC96C2DFE67E015156B731C6E50_AdjustorThunk },
	{ 0x060004CC, PropertySceneHandle_IsValid_m88FB030E99AE0E2D8A4506C7B06BAF8B27CE481E_AdjustorThunk },
	{ 0x060004CD, PropertySceneHandle_IsValidInternal_m60ACFD75DC826CB176ED25ABDF6244EEEFA26D7F_AdjustorThunk },
	{ 0x060004CE, PropertySceneHandle_get_createdByNative_m1579532A69F2E0052D6028BB8563E693DACC044F_AdjustorThunk },
	{ 0x060004CF, PropertySceneHandle_get_hasHandleIndex_m8A4BD33134980310FFD1568BF2B0F1E7D88D7E8A_AdjustorThunk },
	{ 0x060004D0, PropertySceneHandle_Resolve_m4ADA3EE521AB78FEFCEDBC666CA66868D67B0075_AdjustorThunk },
	{ 0x060004D1, PropertySceneHandle_IsResolved_mFD3DCB641948607A93E40B0D2E6897333F68D7EA_AdjustorThunk },
	{ 0x060004D2, PropertySceneHandle_CheckIsValid_mA5326B76CA28F14695D23826F1F43215C353A033_AdjustorThunk },
	{ 0x060004D3, PropertySceneHandle_GetFloat_m2DA1CB5219344BB6A59421E15A74C6ADE286EC24_AdjustorThunk },
	{ 0x060004D4, PropertySceneHandle_SetFloat_mCDF00CD9F89DB5906FA72CAD9CE1AB98E3322E12_AdjustorThunk },
	{ 0x060004D5, PropertySceneHandle_GetInt_mA22A93D80220EE3CF94C7DCFCC70A958F125418E_AdjustorThunk },
	{ 0x060004D6, PropertySceneHandle_SetInt_m927C12912421F35BDBC5FF6FA34BB5A716E536CE_AdjustorThunk },
	{ 0x060004D7, PropertySceneHandle_GetBool_mE876BC9201F73F814AFF5F4A82C90CC02BEDD18C_AdjustorThunk },
	{ 0x060004D8, PropertySceneHandle_SetBool_m43589AABBCE73E5BDF47E24EAADC309908CEF5DB_AdjustorThunk },
	{ 0x060004D9, PropertySceneHandle_HasValidTransform_m4DFB6634221B060FB846A975C2AA76881CFA978B_AdjustorThunk },
	{ 0x060004DA, PropertySceneHandle_IsBound_m75BC5C73C0C65F3967C0B2E6519ED4EF495E19DE_AdjustorThunk },
	{ 0x060004DB, PropertySceneHandle_ResolveInternal_m9C4C80E37AB2178EDA3B31506BE2F56721E1BF3C_AdjustorThunk },
	{ 0x060004DC, PropertySceneHandle_GetFloatInternal_mBD6D41B4F23687D5CEE7F1674E457C03BF4CF692_AdjustorThunk },
	{ 0x060004DD, PropertySceneHandle_GetIntInternal_m8AC9D22272A7199D6BBCA877B036408D5975BC46_AdjustorThunk },
	{ 0x060004DE, PropertySceneHandle_GetBoolInternal_m4714FA71EB7064EAFF5D40643989E41D057ABB75_AdjustorThunk },
	{ 0x060004F5, AnimatorControllerPlayable__ctor_mBCB9475E2740BE1AEB94C08BAD14D51333258BFE_AdjustorThunk },
	{ 0x060004F6, AnimatorControllerPlayable_GetHandle_m718D9A4E0DB7AC62947B1D09E47DBCD25C27AF6C_AdjustorThunk },
	{ 0x060004F7, AnimatorControllerPlayable_SetHandle_mD86A3C0D03453FAF21903F7A52A743AB2DA6DED4_AdjustorThunk },
	{ 0x060004FA, AnimatorControllerPlayable_Equals_m14125BB4CCFCDFFD098223AF20E38501BA264180_AdjustorThunk },
	{ 0x060004FB, AnimatorControllerPlayable_GetFloat_m787538C1B2ED9C4E52E8A3EEF43405913E82E895_AdjustorThunk },
	{ 0x060004FC, AnimatorControllerPlayable_GetFloat_mDBD6169B671FCD0AA3551368A3EE37334E7D7B49_AdjustorThunk },
	{ 0x060004FD, AnimatorControllerPlayable_SetFloat_m95BF662BF3AA8A4F4B5F868801DED66D397E2407_AdjustorThunk },
	{ 0x060004FE, AnimatorControllerPlayable_SetFloat_mEDD694D72DDBEDF381973F41D83037F330224EDC_AdjustorThunk },
	{ 0x060004FF, AnimatorControllerPlayable_GetBool_m30C0C093BA04B2C680E5B8510A189A66A4D5333D_AdjustorThunk },
	{ 0x06000500, AnimatorControllerPlayable_GetBool_m72BB4B2A95A2C229ADE9B51F7232B8FF4E5DF26E_AdjustorThunk },
	{ 0x06000501, AnimatorControllerPlayable_SetBool_mDBBE0C1A970D71F600234B15D0E6B9C51E7A70DC_AdjustorThunk },
	{ 0x06000502, AnimatorControllerPlayable_SetBool_mEBE969EAB2935C3A15848521B06ABB45B1188BAD_AdjustorThunk },
	{ 0x06000503, AnimatorControllerPlayable_GetInteger_mFE3ADA2A4AD9A7A62B60D7E5F2FCBCF9A5F58AA0_AdjustorThunk },
	{ 0x06000504, AnimatorControllerPlayable_GetInteger_m50B3042FDC1F87A4E0423FCCFEED611996A3860D_AdjustorThunk },
	{ 0x06000505, AnimatorControllerPlayable_SetInteger_m4C8B5F237C20CFABF0CAD460F55782D834444A91_AdjustorThunk },
	{ 0x06000506, AnimatorControllerPlayable_SetInteger_m6FA135B1C91BED0F97C9E623FB8C37411C6F15B4_AdjustorThunk },
	{ 0x06000507, AnimatorControllerPlayable_SetTrigger_m06D7662315ED85A8472E62C2F369B1167BAF51A7_AdjustorThunk },
	{ 0x06000508, AnimatorControllerPlayable_SetTrigger_mA2F8F838D48244468DACBF503E120FF3B06E95B2_AdjustorThunk },
	{ 0x06000509, AnimatorControllerPlayable_ResetTrigger_mD671335B03DF16D311287002303191D50B802D14_AdjustorThunk },
	{ 0x0600050A, AnimatorControllerPlayable_ResetTrigger_mABE88FFA8781EB19F5B06F7BF483F13C61107D6C_AdjustorThunk },
	{ 0x0600050B, AnimatorControllerPlayable_IsParameterControlledByCurve_m34F8D191A09BC28CCDF1A044FD02B33A1D64D0B1_AdjustorThunk },
	{ 0x0600050C, AnimatorControllerPlayable_IsParameterControlledByCurve_m8ADD7EE7A489424B4B99253C96647E67B04E2D1B_AdjustorThunk },
	{ 0x0600050D, AnimatorControllerPlayable_GetLayerCount_m3941A7DA375B26988A59DA13A22C08476D59D34F_AdjustorThunk },
	{ 0x0600050E, AnimatorControllerPlayable_GetLayerName_m33BDF74B10C6BFDB82FF404E288F52F1EBABC809_AdjustorThunk },
	{ 0x0600050F, AnimatorControllerPlayable_GetLayerIndex_m48AB42D7F82BE03576D4AC480F048EEDF01B0A0D_AdjustorThunk },
	{ 0x06000510, AnimatorControllerPlayable_GetLayerWeight_m09D5E7AB77824DE2DE20E4E4491BF2A90E3B28BB_AdjustorThunk },
	{ 0x06000511, AnimatorControllerPlayable_SetLayerWeight_mE432DA7CC2FC4425D0DA064B71B5ACCEB83F8EE3_AdjustorThunk },
	{ 0x06000512, AnimatorControllerPlayable_GetCurrentAnimatorStateInfo_mBA937DD74A4964C743880FFE7CFFE67B18D8264B_AdjustorThunk },
	{ 0x06000513, AnimatorControllerPlayable_GetNextAnimatorStateInfo_m1BD26B635F70840C13BC34D0632D82B5EA48CDE0_AdjustorThunk },
	{ 0x06000514, AnimatorControllerPlayable_GetAnimatorTransitionInfo_m5157FED95B567D4441228BC0F3AF31563FD5BF1C_AdjustorThunk },
	{ 0x06000515, AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mF89B409BFDD2B021BB6862B93A68879BCAB60076_AdjustorThunk },
	{ 0x06000516, AnimatorControllerPlayable_GetCurrentAnimatorClipInfo_mB103A1F9EC5E23947C8A1802D644FC4D22093F14_AdjustorThunk },
	{ 0x06000517, AnimatorControllerPlayable_GetNextAnimatorClipInfo_mF00B978E844FF7E6DF0AF670C0CF2022A8DD2517_AdjustorThunk },
	{ 0x06000519, AnimatorControllerPlayable_GetCurrentAnimatorClipInfoCount_mB80BDB04DF276D53D45EAA549CAD88DA9A7E8BE8_AdjustorThunk },
	{ 0x0600051A, AnimatorControllerPlayable_GetNextAnimatorClipInfoCount_m19CD5C12E89C22E3521175CD6C1E2F033C53D070_AdjustorThunk },
	{ 0x0600051B, AnimatorControllerPlayable_GetNextAnimatorClipInfo_mD247136758432A4036FC338D4C8E67ECAF5EDD26_AdjustorThunk },
	{ 0x0600051C, AnimatorControllerPlayable_IsInTransition_m041E31E9DAD976867BCCBDDD27CB556F590D61D9_AdjustorThunk },
	{ 0x0600051D, AnimatorControllerPlayable_GetParameterCount_m899EE6DAD7209174D7568E632DA986D39E435A70_AdjustorThunk },
	{ 0x0600051E, AnimatorControllerPlayable_GetParameter_mB7ECCC4E41138CC7D9A28ABF499679B8792E9CC3_AdjustorThunk },
	{ 0x0600051F, AnimatorControllerPlayable_CrossFadeInFixedTime_mC4104110B90B6802409F8FC102AC5758250FAE74_AdjustorThunk },
	{ 0x06000520, AnimatorControllerPlayable_CrossFadeInFixedTime_mF66C581A902E013AF240151A4B9772F8DD94E95A_AdjustorThunk },
	{ 0x06000521, AnimatorControllerPlayable_CrossFadeInFixedTime_m43166ED8F80B94469DFA25374911A64637438674_AdjustorThunk },
	{ 0x06000522, AnimatorControllerPlayable_CrossFadeInFixedTime_m664DCD090D5E91612254A0F87AB5D2A538DF8DC5_AdjustorThunk },
	{ 0x06000523, AnimatorControllerPlayable_CrossFadeInFixedTime_m7901F79283F0C1357A14AE48D81BA663D98740C8_AdjustorThunk },
	{ 0x06000524, AnimatorControllerPlayable_CrossFadeInFixedTime_m93EC21F858FE76184FA91D2B6EA3B361A2929503_AdjustorThunk },
	{ 0x06000525, AnimatorControllerPlayable_CrossFade_m93543957867BB7F6B7A5498EF7ECDBAFCA0C295F_AdjustorThunk },
	{ 0x06000526, AnimatorControllerPlayable_CrossFade_m3B003ED0E44C2405B104B7E7ECF1AC0A6C15C7DB_AdjustorThunk },
	{ 0x06000527, AnimatorControllerPlayable_CrossFade_m8AD5C075E4B4B645796A36980AE15378167019AA_AdjustorThunk },
	{ 0x06000528, AnimatorControllerPlayable_CrossFade_m4CADD7D865B6C0A16D35B58011895E560CCF8824_AdjustorThunk },
	{ 0x06000529, AnimatorControllerPlayable_CrossFade_mC2E4C450F3E777DBE08BBBF0138FAA174D0526C5_AdjustorThunk },
	{ 0x0600052A, AnimatorControllerPlayable_CrossFade_m0C777C0385BE3E8AA8D86C99897D490ED553F9DD_AdjustorThunk },
	{ 0x0600052B, AnimatorControllerPlayable_PlayInFixedTime_m797B8A53344C62FF813DC398D1E6A6B18A826275_AdjustorThunk },
	{ 0x0600052C, AnimatorControllerPlayable_PlayInFixedTime_mA9FA57E1D8B5B4DB655F1918C338585573B7DEEA_AdjustorThunk },
	{ 0x0600052D, AnimatorControllerPlayable_PlayInFixedTime_mE4DAC931BFEDBCAABE0D410BE3DF85C5C4FF1425_AdjustorThunk },
	{ 0x0600052E, AnimatorControllerPlayable_PlayInFixedTime_m37882EB28A1E75354814AB9EB6426D7F3FF8D985_AdjustorThunk },
	{ 0x0600052F, AnimatorControllerPlayable_PlayInFixedTime_m62E44D644C7785CFF04229D6EA808F9C670FF010_AdjustorThunk },
	{ 0x06000530, AnimatorControllerPlayable_PlayInFixedTime_m513E1A9D864FCC8DF9E16A9DE05465A5694ACE2B_AdjustorThunk },
	{ 0x06000531, AnimatorControllerPlayable_Play_mD967CAA3998D88A9116C165D1D1ADDEEC8D3FEFF_AdjustorThunk },
	{ 0x06000532, AnimatorControllerPlayable_Play_mA3346301B00AD22D27B692BE36E560569FCD2765_AdjustorThunk },
	{ 0x06000533, AnimatorControllerPlayable_Play_m703A612D5040F4F99C17D300E5BD57ABB992E976_AdjustorThunk },
	{ 0x06000534, AnimatorControllerPlayable_Play_m3C33626F6950B0CD33EB39A87C93819B6CC9C52D_AdjustorThunk },
	{ 0x06000535, AnimatorControllerPlayable_Play_m252547934FE64DCF1DC9D3242E20200A0E8852D1_AdjustorThunk },
	{ 0x06000536, AnimatorControllerPlayable_Play_mB14FF22BE5BF41EB807BD3F14C111A3D60E59394_AdjustorThunk },
	{ 0x06000537, AnimatorControllerPlayable_HasState_mE537ED9F84D34939019463D4A2F6171B053759C2_AdjustorThunk },
	{ 0x06000538, AnimatorControllerPlayable_ResolveHash_mF09713D683CEF1F188415CE3868CF117B04FA322_AdjustorThunk },
	{ 0x06000583, ConstraintSource_get_sourceTransform_m0A9C0C96F68398626E43F2137E2CAB5BA803F94F_AdjustorThunk },
	{ 0x06000584, ConstraintSource_set_sourceTransform_mC905D9E04293D785BA40E1E0378457A77170A31B_AdjustorThunk },
	{ 0x06000585, ConstraintSource_get_weight_m90743C7AB9BA12A99FB8C81442E765A4F947BE28_AdjustorThunk },
	{ 0x06000586, ConstraintSource_set_weight_m40EADC470F7D906EEB89A515F75CC8B0648368D7_AdjustorThunk },
	{ 0x06000620, MuscleHandle_get_humanPartDof_m8262C622AA62DC780CCA30CB5492F1235C45A505_AdjustorThunk },
	{ 0x06000621, MuscleHandle_set_humanPartDof_m11EE483E985240C49307DF184DAE8B3260945BCE_AdjustorThunk },
	{ 0x06000622, MuscleHandle_get_dof_mFD825C91413A42221BD937827E3C743C8A776598_AdjustorThunk },
	{ 0x06000623, MuscleHandle_set_dof_mB202EB7F0DE1EB8E0E3E84349914EA26F48EAA93_AdjustorThunk },
	{ 0x06000624, MuscleHandle__ctor_m639F42D1909646875E9AC30B3394498060E5ACD6_AdjustorThunk },
	{ 0x06000625, MuscleHandle__ctor_mBE2F28D04718BC0EF94766966956ECCC980889CA_AdjustorThunk },
	{ 0x06000626, MuscleHandle__ctor_m4A0A680BBA254F55AE88CBCA6A0B0A50A0E143C6_AdjustorThunk },
	{ 0x06000627, MuscleHandle__ctor_mF082607DD932A653DABE2DE3DF4C624AAEB174F3_AdjustorThunk },
	{ 0x06000628, MuscleHandle__ctor_m0103CC894F5E0BD46FD5FF9A0F20BBA11873AB6E_AdjustorThunk },
	{ 0x06000629, MuscleHandle_get_name_m298104348CF7CF2ED690FDB26039D283D9629174_AdjustorThunk },
	{ 0x0600062C, MuscleHandle_GetName_m229299540D61FB707555C9C2D254DC7448B7AC22_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1634] = 
{
	0,
	4364,
	2025,
	2025,
	2025,
	2025,
	2025,
	2796,
	2796,
	1389,
	1389,
	1389,
	1389,
	1389,
	2043,
	2043,
	4364,
	4250,
	3881,
	4168,
	3807,
	4216,
	3852,
	4364,
	3881,
	3881,
	4364,
	3881,
	3881,
	4364,
	4168,
	3185,
	3518,
	4168,
	3166,
	3166,
	3185,
	2320,
	3881,
	2810,
	2103,
	3881,
	2810,
	2105,
	3518,
	2525,
	1806,
	1219,
	3518,
	2522,
	1793,
	2802,
	1449,
	955,
	3881,
	3881,
	3881,
	4216,
	3166,
	2320,
	3852,
	4250,
	3518,
	3515,
	4216,
	3518,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4166,
	3805,
	4364,
	3788,
	3788,
	3881,
	4250,
	4168,
	4364,
	4168,
	3807,
	4298,
	3928,
	4216,
	3852,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	4216,
	3852,
	4250,
	4250,
	3881,
	4216,
	3852,
	3881,
	2788,
	3881,
	4364,
	4364,
	4250,
	3881,
	4250,
	3881,
	4298,
	3928,
	4216,
	3852,
	4250,
	3881,
	4250,
	3881,
	4298,
	3928,
	4216,
	3852,
	4168,
	4168,
	4250,
	4155,
	4154,
	4216,
	4364,
	8887,
	2810,
	6191,
	4298,
	4298,
	4298,
	4298,
	3928,
	1458,
	4364,
	4364,
	4216,
	3852,
	4166,
	3805,
	4168,
	3807,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	4168,
	3881,
	3881,
	4250,
	3881,
	3881,
	4250,
	3788,
	3788,
	4250,
	4298,
	8501,
	3185,
	4216,
	4216,
	4216,
	4298,
	4298,
	4298,
	4298,
	4216,
	3185,
	4168,
	3185,
	3185,
	4216,
	4216,
	4216,
	4216,
	4298,
	4298,
	4168,
	4168,
	4168,
	2894,
	4358,
	3980,
	4298,
	3928,
	4168,
	4168,
	4168,
	4168,
	4298,
	4168,
	3584,
	3581,
	2810,
	1475,
	2745,
	1367,
	3185,
	3166,
	2788,
	2729,
	3419,
	3415,
	2796,
	2734,
	3881,
	3852,
	3881,
	3852,
	3185,
	3166,
	4358,
	4267,
	4358,
	4358,
	4358,
	3980,
	4267,
	3896,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4168,
	4168,
	3807,
	4298,
	4358,
	3980,
	4358,
	3980,
	4267,
	3896,
	4267,
	3896,
	3667,
	3667,
	2751,
	2751,
	3551,
	3551,
	2741,
	2741,
	3581,
	3581,
	2745,
	2745,
	3581,
	3581,
	2745,
	2745,
	3667,
	3667,
	2751,
	2751,
	3581,
	3581,
	2745,
	2745,
	3980,
	3980,
	3928,
	2850,
	2150,
	1517,
	1010,
	1010,
	2741,
	2741,
	3518,
	0,
	0,
	0,
	3518,
	2507,
	1770,
	4168,
	3807,
	4216,
	3515,
	3419,
	3581,
	2745,
	1967,
	3031,
	3031,
	2728,
	3032,
	2422,
	3415,
	3415,
	3515,
	3515,
	2739,
	1962,
	2739,
	3166,
	4250,
	4216,
	3515,
	3515,
	4298,
	3928,
	4298,
	4358,
	410,
	1022,
	671,
	410,
	4364,
	3807,
	4168,
	4298,
	3928,
	3928,
	2810,
	2103,
	1471,
	981,
	1366,
	1993,
	2745,
	891,
	4364,
	1471,
	2103,
	2810,
	981,
	891,
	1366,
	1993,
	2745,
	2796,
	3881,
	2061,
	1976,
	2734,
	3852,
	2796,
	3881,
	2061,
	1976,
	2734,
	3852,
	2745,
	4358,
	4267,
	3185,
	3185,
	4250,
	3515,
	3515,
	4216,
	3852,
	4364,
	4364,
	4298,
	3928,
	3852,
	4364,
	4298,
	3928,
	4298,
	4298,
	3928,
	4298,
	4216,
	4250,
	3881,
	4168,
	4364,
	2296,
	8399,
	4250,
	3881,
	4250,
	4258,
	3788,
	4364,
	4168,
	2810,
	2745,
	3584,
	3581,
	2788,
	2729,
	3185,
	3166,
	2796,
	2734,
	3419,
	3415,
	3881,
	3852,
	3881,
	3852,
	3185,
	3166,
	1475,
	1367,
	4168,
	3807,
	4298,
	4298,
	4168,
	4364,
	4364,
	4364,
	3928,
	4364,
	3807,
	4364,
	4364,
	3928,
	3515,
	3515,
	2504,
	3515,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	3668,
	3667,
	2818,
	2751,
	3552,
	3551,
	2804,
	2741,
	4364,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	2728,
	2728,
	2728,
	2728,
	2728,
	2728,
	3788,
	2728,
	3788,
	314,
	3788,
	3788,
	4250,
	4216,
	4216,
	3852,
	4298,
	3928,
	4216,
	3852,
	4168,
	3807,
	3185,
	4216,
	4364,
	4364,
	4364,
	3881,
	7975,
	4250,
	3881,
	3518,
	2802,
	2518,
	2802,
	3518,
	2802,
	2518,
	2077,
	4364,
	3515,
	3518,
	4216,
	3881,
	3881,
	4250,
	3881,
	4364,
	8887,
	2798,
	4364,
	2524,
	3881,
	7975,
	8887,
	4364,
	4364,
	4168,
	4168,
	4211,
	1995,
	2745,
	3581,
	3551,
	3551,
	1834,
	2558,
	3667,
	3581,
	3551,
	3551,
	1834,
	2558,
	3667,
	3788,
	2728,
	2728,
	1330,
	1959,
	2728,
	4216,
	3852,
	4168,
	3807,
	4358,
	3980,
	4358,
	3980,
	4358,
	3980,
	4298,
	3928,
	4250,
	3881,
	4250,
	3881,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	7624,
	7624,
	7631,
	4364,
	7615,
	4364,
	8887,
	4216,
	3166,
	2729,
	4216,
	3852,
	3881,
	2788,
	3881,
	2788,
	3515,
	2739,
	3581,
	2745,
	3166,
	2729,
	4168,
	3881,
	4364,
	7545,
	7545,
	8882,
	1907,
	1907,
	1907,
	1907,
	2659,
	2659,
	4364,
	2802,
	2802,
	3788,
	3788,
	3788,
	3788,
	3703,
	3703,
	9018,
	8395,
	8395,
	9031,
	9018,
	9031,
	7473,
	7473,
	8395,
	8395,
	8216,
	8216,
	9018,
	8622,
	8622,
	8622,
	8395,
	8622,
	8395,
	4364,
	4364,
	4298,
	4298,
	4358,
	4298,
	4168,
	4168,
	4168,
	3113,
	4168,
	3788,
	4364,
	4250,
	8381,
	7910,
	8764,
	7911,
	8400,
	8000,
	8400,
	8000,
	8380,
	7897,
	8380,
	7897,
	6993,
	6268,
	6271,
	6269,
	6275,
	7683,
	7686,
	4364,
	8405,
	8622,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	8887,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4358,
	3980,
	4358,
	3980,
	4216,
	3852,
	4358,
	3980,
	4358,
	3980,
	4358,
	3980,
	4250,
	3881,
	4216,
	3852,
	4216,
	8399,
	3881,
	3881,
	7975,
	3404,
	3852,
	3852,
	3336,
	3336,
	2732,
	2732,
	3852,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3788,
	3397,
	2728,
	2728,
	7107,
	7685,
	3889,
	4259,
	8540,
	8164,
	3081,
	4250,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	4298,
	3928,
	6383,
	8485,
	8200,
	7895,
	8200,
	7895,
	8200,
	7895,
	8200,
	7895,
	8200,
	7895,
	8615,
	7902,
	6310,
	4168,
	4364,
	4298,
	4298,
	4298,
	4358,
	3980,
	4267,
	3896,
	4358,
	3980,
	4267,
	3896,
	3583,
	2782,
	4358,
	4358,
	4364,
	3667,
	3551,
	3667,
	2751,
	3551,
	2741,
	3667,
	2751,
	3551,
	2741,
	2745,
	2745,
	3581,
	3581,
	3667,
	2751,
	2745,
	3581,
	3980,
	3928,
	3928,
	3928,
	3928,
	4364,
	4298,
	3577,
	4364,
	3667,
	3551,
	4358,
	3980,
	4267,
	3896,
	4358,
	3980,
	4267,
	3896,
	3583,
	2782,
	4358,
	4358,
	3667,
	2751,
	3551,
	2741,
	3667,
	2751,
	3551,
	2741,
	2745,
	2745,
	3581,
	3581,
	3667,
	2751,
	2745,
	3581,
	3980,
	3928,
	3928,
	3928,
	3928,
	4364,
	8615,
	7726,
	8867,
	6859,
	6859,
	7894,
	7894,
	7894,
	7894,
	7894,
	7894,
	7894,
	7894,
	7725,
	6851,
	7894,
	7894,
	6859,
	6859,
	6859,
	6859,
	6859,
	6859,
	6859,
	6859,
	6863,
	6863,
	7727,
	7727,
	6859,
	6859,
	6863,
	7727,
	7894,
	7902,
	7902,
	7902,
	7902,
	8867,
	8971,
	7108,
	6270,
	7684,
	2824,
	4259,
	8541,
	8165,
	3082,
	3271,
	2871,
	2873,
	7272,
	7147,
	6875,
	7895,
	6877,
	9089,
	7140,
	8972,
	6272,
	7109,
	7684,
	3889,
	4259,
	8542,
	8166,
	3083,
	7272,
	9089,
	7140,
	8973,
	8168,
	8558,
	3889,
	4259,
	8543,
	8167,
	3084,
	4168,
	3807,
	7272,
	8200,
	7895,
	9089,
	7140,
	8974,
	5617,
	6010,
	3889,
	4259,
	8544,
	8169,
	3085,
	4358,
	3980,
	4267,
	3896,
	5708,
	8831,
	7906,
	8572,
	7901,
	9089,
	5637,
	7894,
	7894,
	7894,
	7894,
	0,
	7900,
	7997,
	7998,
	6310,
	7900,
	7894,
	8380,
	6306,
	6273,
	3890,
	8975,
	4261,
	8559,
	8170,
	4250,
	3881,
	8485,
	7900,
	8976,
	8172,
	8558,
	3889,
	4259,
	8545,
	8171,
	3086,
	4168,
	3807,
	4168,
	3807,
	4168,
	3807,
	7272,
	8200,
	7895,
	8200,
	7895,
	8200,
	7895,
	9089,
	7140,
	8977,
	7110,
	7684,
	3889,
	4259,
	8546,
	8173,
	3087,
	7272,
	9089,
	7140,
	8978,
	0,
	0,
	3889,
	4259,
	0,
	0,
	0,
	8547,
	8174,
	3088,
	3807,
	4168,
	6382,
	7999,
	8223,
	9089,
	6298,
	7895,
	8200,
	4350,
	4168,
	4364,
	4298,
	4358,
	3980,
	4358,
	3980,
	4358,
	4267,
	4168,
	4153,
	4216,
	3030,
	3581,
	3791,
	4364,
	4364,
	3791,
	4298,
	4168,
	4358,
	3980,
	4358,
	3980,
	4358,
	4267,
	4216,
	3030,
	3581,
	4153,
	4364,
	4364,
	7894,
	8615,
	8200,
	7894,
	7894,
	7894,
	7894,
	7894,
	7894,
	8380,
	6859,
	7727,
	7894,
	8867,
	8867,
	3089,
	3079,
	4168,
	3079,
	4168,
	4168,
	3973,
	4350,
	3791,
	3089,
	3079,
	3788,
	3665,
	2675,
	3550,
	2673,
	3665,
	2675,
	3550,
	2673,
	3665,
	2675,
	3482,
	3089,
	3089,
	3089,
	1304,
	840,
	1924,
	3482,
	1305,
	3788,
	3664,
	2667,
	3549,
	2662,
	3664,
	2667,
	3549,
	2662,
	3664,
	2667,
	3481,
	3079,
	3079,
	3079,
	1283,
	839,
	1904,
	3481,
	1303,
	7894,
	6845,
	6845,
	6845,
	6845,
	6845,
	6845,
	6845,
	6845,
	6845,
	6845,
	6845,
	7140,
	7140,
	7140,
	5456,
	5059,
	6066,
	6845,
	5457,
	3089,
	3079,
	4168,
	3079,
	4168,
	4168,
	4168,
	3973,
	4350,
	3791,
	3089,
	3079,
	3788,
	3576,
	2674,
	3398,
	2672,
	3089,
	2671,
	3089,
	3788,
	3575,
	2663,
	3397,
	2659,
	3079,
	2658,
	3079,
	7894,
	7725,
	6851,
	7461,
	6847,
	7140,
	6846,
	7140,
	3089,
	4168,
	4168,
	3788,
	3665,
	2675,
	3665,
	2675,
	3550,
	2673,
	3550,
	2673,
	3665,
	1304,
	3482,
	1924,
	3482,
	2675,
	3079,
	3664,
	3664,
	3549,
	3549,
	3664,
	1283,
	3481,
	1904,
	3481,
	7140,
	6845,
	6845,
	6845,
	6845,
	6845,
	5456,
	6845,
	6066,
	6845,
	3089,
	3079,
	4168,
	4168,
	3791,
	3089,
	3788,
	3576,
	2674,
	3398,
	2672,
	3089,
	2671,
	3079,
	3079,
	3788,
	3575,
	3397,
	3079,
	7140,
	7140,
	7894,
	7725,
	7461,
	7140,
	6881,
	6882,
	0,
	6068,
	6068,
	6098,
	6099,
	6883,
	6884,
	6068,
	6068,
	5459,
	5459,
	8979,
	7111,
	7685,
	3889,
	4259,
	3889,
	8548,
	8175,
	3090,
	3584,
	3581,
	2810,
	2745,
	3185,
	3166,
	2788,
	2729,
	3419,
	3415,
	2796,
	2734,
	3881,
	3852,
	3881,
	3852,
	3185,
	3166,
	4216,
	3515,
	3419,
	3581,
	2745,
	3031,
	3031,
	3032,
	3515,
	2739,
	2739,
	6080,
	3415,
	3415,
	3515,
	3166,
	4216,
	3515,
	2810,
	2103,
	1471,
	2745,
	1993,
	1366,
	2810,
	2103,
	1471,
	2745,
	1993,
	1366,
	3881,
	2796,
	2061,
	3852,
	2734,
	1976,
	3881,
	2796,
	2061,
	3852,
	2734,
	1976,
	2296,
	3515,
	6383,
	8485,
	8380,
	7574,
	7463,
	7727,
	6863,
	7112,
	7112,
	7113,
	7574,
	6484,
	7574,
	7574,
	7144,
	7574,
	8380,
	8399,
	5488,
	5488,
	6083,
	6083,
	6308,
	6871,
	6863,
	7728,
	7727,
	6868,
	6860,
	7145,
	7144,
	6869,
	6861,
	7463,
	7462,
	7900,
	7897,
	7900,
	7897,
	7145,
	7144,
	9089,
	6310,
	6859,
	6859,
	6859,
	7972,
	7793,
	6013,
	6751,
	5414,
	7792,
	6012,
	5413,
	7252,
	7961,
	8887,
	8887,
	8887,
	8887,
	8887,
	7972,
	6979,
	5128,
	6178,
	6979,
	5128,
	7252,
	7961,
	8887,
	8887,
	8887,
	8887,
	7961,
	4250,
	3881,
	4298,
	3928,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4364,
	8887,
	4298,
	3928,
	4358,
	3980,
	4358,
	3980,
	4216,
	3852,
	4168,
	3807,
	4168,
	3807,
	4216,
	8399,
	3881,
	3881,
	7975,
	3404,
	3852,
	3852,
	3336,
	3336,
	2732,
	2732,
	3852,
	3788,
	3788,
	3788,
	3788,
	3397,
	2728,
	2728,
	4364,
	8887,
	4298,
	3928,
	4358,
	3980,
	4358,
	3980,
	4216,
	3852,
	4168,
	3807,
	4168,
	3807,
	4216,
	8399,
	3881,
	3881,
	7975,
	3404,
	3852,
	3852,
	3336,
	3336,
	2732,
	2732,
	3852,
	3788,
	3788,
	3788,
	3788,
	3397,
	2728,
	2728,
	4364,
	8887,
	4298,
	3928,
	4358,
	3980,
	4358,
	3980,
	4216,
	3852,
	4168,
	3807,
	4168,
	3807,
	4216,
	8399,
	3881,
	3881,
	7975,
	3404,
	3852,
	3852,
	3336,
	3336,
	2732,
	2732,
	3852,
	3788,
	3788,
	3788,
	3788,
	3397,
	2728,
	2728,
	4364,
	8887,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4358,
	3980,
	4358,
	3980,
	4250,
	3881,
	4168,
	3807,
	4216,
	8399,
	3881,
	3881,
	7975,
	3404,
	3852,
	3852,
	3336,
	3336,
	2732,
	2732,
	3852,
	3788,
	3788,
	3788,
	3788,
	3397,
	2728,
	2728,
	4216,
	3852,
	4216,
	3852,
	3852,
	3852,
	2734,
	2734,
	2734,
	4250,
	9018,
	8887,
	4250,
	9018,
	8485,
	4364,
	8887,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	4216,
	8399,
	4358,
	3980,
	4358,
	3980,
	4250,
	3881,
	4250,
	3881,
	4216,
	3852,
	4216,
	3852,
	3667,
	2751,
	3667,
	2751,
	3667,
	2751,
	3667,
	2751,
	3852,
	3881,
	3881,
	7975,
	3404,
	3852,
	3852,
	3336,
	3336,
	2732,
	2732,
	3788,
	3788,
	3788,
	3788,
	2728,
	2728,
	2728,
	2728,
	3397,
	2728,
	2728,
};
static const Il2CppTokenRangePair s_rgctxIndices[11] = 
{
	{ 0x02000047, { 8, 10 } },
	{ 0x0600011B, { 0, 2 } },
	{ 0x0600011C, { 2, 3 } },
	{ 0x0600011D, { 5, 3 } },
	{ 0x060003C2, { 18, 2 } },
	{ 0x060003F8, { 20, 3 } },
	{ 0x060003F9, { 23, 2 } },
	{ 0x060003FC, { 25, 1 } },
	{ 0x060003FD, { 26, 4 } },
	{ 0x060003FE, { 30, 4 } },
	{ 0x060004E7, { 34, 8 } },
};
extern const uint32_t g_rgctx_T_tC4A05419E1F5D1CD1B3EC2178BEAF13D883B4C70;
extern const uint32_t g_rgctx_T_tC4A05419E1F5D1CD1B3EC2178BEAF13D883B4C70;
extern const uint32_t g_rgctx_TU5BU5D_t7D1E4F0D66D89B1FF4C8A609D65C97753124AD3D;
extern const uint32_t g_rgctx_T_tE5177499150C3360D6F1588011B2CF86A7C3F0B6;
extern const uint32_t g_rgctx_TU5BU5D_t7D1E4F0D66D89B1FF4C8A609D65C97753124AD3D;
extern const uint32_t g_rgctx_T_t72D9269A402D847C2FAE5C0DF286CFBAC76DE4CB;
extern const uint32_t g_rgctx_Animator_ConvertStateMachineBehaviour_TisT_t72D9269A402D847C2FAE5C0DF286CFBAC76DE4CB_m59B930A2D9A4CD3960E44297FA1BC0102E6361C9;
extern const uint32_t g_rgctx_TU5BU5D_tF874931433032E6AE4D536388DA03D641925528D;
extern const uint32_t g_rgctx_ProcessAnimationJobStruct_1_t51A4969FC47589FF911E73265EE9885B17EE2E96;
extern const uint32_t g_rgctx_ProcessAnimationJobStruct_1_t51A4969FC47589FF911E73265EE9885B17EE2E96;
extern const uint32_t g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE;
extern const uint32_t g_rgctx_ProcessAnimationJobStruct_1_Execute_m7021F1263EB2EE0C8E4700B629514DACCB96A357;
extern const uint32_t g_rgctx_ExecuteJobFunction_tB77BB3DCCAD0A15DC80232D36E5C9A7BEEE9C512;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_m2A11C142C44FB3F5C0C24CBC9ADA5B8026225B3B;
extern const uint32_t g_rgctx_TU26_t726C85032DA609174BB9803A5F4BDC44F30F53F2;
extern const uint32_t g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE_IAnimationJob_ProcessRootMotion_m2AC21A0C49E12DEF678CEBECBF8595E7B6E25292;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE_IAnimationJob_ProcessAnimation_mD26C39376FFEBE697088A97FE100E3EE6B32EB6C;
extern const uint32_t g_rgctx_U_t6A240D30A4B3E286D004C39E3892482214AD5F07;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t6A240D30A4B3E286D004C39E3892482214AD5F07_IPlayable_GetHandle_m270FD2834B8DEE4CF323D5076D12384901236A5A;
extern const uint32_t g_rgctx_AnimationScriptPlayable_CreateHandle_TisT_t0EC213DB740E4553178AC78CEEA826055FBBF470_mFEFA45316319CF3966EDF5A4ABBDDBCF39FBA27E;
extern const uint32_t g_rgctx_T_t0EC213DB740E4553178AC78CEEA826055FBBF470;
extern const uint32_t g_rgctx_AnimationScriptPlayable_SetJobData_TisT_t0EC213DB740E4553178AC78CEEA826055FBBF470_m6998C288FE199D9F611039F6952331A263B48F47;
extern const uint32_t g_rgctx_ProcessAnimationJobStruct_1_GetJobReflectionData_m404D9BF6F08093F673F518420BB9099C8C6E9103;
extern const uint32_t g_rgctx_ProcessAnimationJobStruct_1_t2942F6A95E1C088FD3A7BA80D2F3625053E571B9;
extern const uint32_t g_rgctx_T_t3F52771021149911FE35C5717F7269270AEC0CEB;
extern const uint32_t g_rgctx_AnimationScriptPlayable_CheckJobTypeValidity_TisT_t1F77F293735F3383D3455F3AA9C3C73E016B61F2_m9C71F9AE9175A489BDB10DCD9FF88A930DF9C442;
extern const uint32_t g_rgctx_UnsafeUtility_CopyPtrToStructure_TisT_t1F77F293735F3383D3455F3AA9C3C73E016B61F2_m79F237FD88550F749E4369AE59E63B69B3B42DC7;
extern const uint32_t g_rgctx_TU26_t9A4FF4B8CF7A0600CEDD0EAFC86CA26C3715DA94;
extern const uint32_t g_rgctx_T_t1F77F293735F3383D3455F3AA9C3C73E016B61F2;
extern const uint32_t g_rgctx_AnimationScriptPlayable_CheckJobTypeValidity_TisT_t2B50397242567B77AB6D4D665EA602C6E855A83E_mE33F9859545EA2B2A334EB34DC25560C2B79607D;
extern const uint32_t g_rgctx_T_t2B50397242567B77AB6D4D665EA602C6E855A83E;
extern const uint32_t g_rgctx_UnsafeUtility_CopyStructureToPtr_TisT_t2B50397242567B77AB6D4D665EA602C6E855A83E_m875E886A172D7FD60E8B29DA6F4B8B444075A79A;
extern const uint32_t g_rgctx_TU26_tBA7B5BCF06A441C58A482F8D0359280666047743;
extern const uint32_t g_rgctx_NativeArray_1_t2844FAD242FBA715A99972BACCE73A6076AB3D57;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m46F82E560AF6C1252F8D4D946FC2358D36F5BDB6;
extern const uint32_t g_rgctx_NativeArray_1_t2844FAD242FBA715A99972BACCE73A6076AB3D57;
extern const uint32_t g_rgctx_NativeArray_1_t606FC4D61EF01B9DF1EBF56D580E4958406BD904;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mF0F0CFFEA34C6E5FD3F53CE96555A82D9E426DD5;
extern const uint32_t g_rgctx_NativeArray_1_t606FC4D61EF01B9DF1EBF56D580E4958406BD904;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m87B5F5FAE44F5D9D13F5DBB362A9119EB42AB039;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m13AA2A75BB00A835A868149C2931A248E5FC3D50;
static const Il2CppRGCTXDefinition s_rgctxValues[42] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tC4A05419E1F5D1CD1B3EC2178BEAF13D883B4C70 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC4A05419E1F5D1CD1B3EC2178BEAF13D883B4C70 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7D1E4F0D66D89B1FF4C8A609D65C97753124AD3D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE5177499150C3360D6F1588011B2CF86A7C3F0B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7D1E4F0D66D89B1FF4C8A609D65C97753124AD3D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t72D9269A402D847C2FAE5C0DF286CFBAC76DE4CB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Animator_ConvertStateMachineBehaviour_TisT_t72D9269A402D847C2FAE5C0DF286CFBAC76DE4CB_m59B930A2D9A4CD3960E44297FA1BC0102E6361C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF874931433032E6AE4D536388DA03D641925528D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ProcessAnimationJobStruct_1_t51A4969FC47589FF911E73265EE9885B17EE2E96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ProcessAnimationJobStruct_1_t51A4969FC47589FF911E73265EE9885B17EE2E96 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ProcessAnimationJobStruct_1_Execute_m7021F1263EB2EE0C8E4700B629514DACCB96A357 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_tB77BB3DCCAD0A15DC80232D36E5C9A7BEEE9C512 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_m2A11C142C44FB3F5C0C24CBC9ADA5B8026225B3B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t726C85032DA609174BB9803A5F4BDC44F30F53F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE_IAnimationJob_ProcessRootMotion_m2AC21A0C49E12DEF678CEBECBF8595E7B6E25292 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0A23A9150F91FB82A829E426F87A725E304E19AE_IAnimationJob_ProcessAnimation_mD26C39376FFEBE697088A97FE100E3EE6B32EB6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t6A240D30A4B3E286D004C39E3892482214AD5F07 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t6A240D30A4B3E286D004C39E3892482214AD5F07_IPlayable_GetHandle_m270FD2834B8DEE4CF323D5076D12384901236A5A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AnimationScriptPlayable_CreateHandle_TisT_t0EC213DB740E4553178AC78CEEA826055FBBF470_mFEFA45316319CF3966EDF5A4ABBDDBCF39FBA27E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0EC213DB740E4553178AC78CEEA826055FBBF470 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AnimationScriptPlayable_SetJobData_TisT_t0EC213DB740E4553178AC78CEEA826055FBBF470_m6998C288FE199D9F611039F6952331A263B48F47 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ProcessAnimationJobStruct_1_GetJobReflectionData_m404D9BF6F08093F673F518420BB9099C8C6E9103 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ProcessAnimationJobStruct_1_t2942F6A95E1C088FD3A7BA80D2F3625053E571B9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t3F52771021149911FE35C5717F7269270AEC0CEB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AnimationScriptPlayable_CheckJobTypeValidity_TisT_t1F77F293735F3383D3455F3AA9C3C73E016B61F2_m9C71F9AE9175A489BDB10DCD9FF88A930DF9C442 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_CopyPtrToStructure_TisT_t1F77F293735F3383D3455F3AA9C3C73E016B61F2_m79F237FD88550F749E4369AE59E63B69B3B42DC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9A4FF4B8CF7A0600CEDD0EAFC86CA26C3715DA94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1F77F293735F3383D3455F3AA9C3C73E016B61F2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AnimationScriptPlayable_CheckJobTypeValidity_TisT_t2B50397242567B77AB6D4D665EA602C6E855A83E_mE33F9859545EA2B2A334EB34DC25560C2B79607D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2B50397242567B77AB6D4D665EA602C6E855A83E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_CopyStructureToPtr_TisT_t2B50397242567B77AB6D4D665EA602C6E855A83E_m875E886A172D7FD60E8B29DA6F4B8B444075A79A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tBA7B5BCF06A441C58A482F8D0359280666047743 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2844FAD242FBA715A99972BACCE73A6076AB3D57 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m46F82E560AF6C1252F8D4D946FC2358D36F5BDB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2844FAD242FBA715A99972BACCE73A6076AB3D57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t606FC4D61EF01B9DF1EBF56D580E4958406BD904 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mF0F0CFFEA34C6E5FD3F53CE96555A82D9E426DD5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t606FC4D61EF01B9DF1EBF56D580E4958406BD904 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m87B5F5FAE44F5D9D13F5DBB362A9119EB42AB039 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m13AA2A75BB00A835A868149C2931A248E5FC3D50 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_AnimationModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AnimationModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AnimationModule_CodeGenModule = 
{
	"UnityEngine.AnimationModule.dll",
	1634,
	s_methodPointers,
	440,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	11,
	s_rgctxIndices,
	42,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_AnimationModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
