﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#else
static const Il2CppMethodExecutionContextInfo g_methodExecutionContextInfos[1] = { { 0, 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#else
static const char* g_methodExecutionContextInfoStrings[1] = { NULL };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[61] = 
{
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
	{ 0, 0 },
};
#else
static const Il2CppMethodExecutionContextInfoIndex g_methodExecutionContextInfoIndexes[1] = { { 0, 0} };
#endif
#if IL2CPP_MONO_DEBUGGER
IL2CPP_EXTERN_C Il2CppSequencePoint g_sequencePointsUnityEngine_DirectorModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_DirectorModule[115] = 
{
	{ 109848, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 0 },
	{ 109848, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 1 },
	{ 109848, 1, 16, 16, 17, 18, 0, kSequencePointKind_Normal, 0, 2 },
	{ 109848, 1, 16, 16, 19, 41, 1, kSequencePointKind_Normal, 0, 3 },
	{ 109848, 1, 16, 16, 19, 41, 2, kSequencePointKind_StepOut, 0, 4 },
	{ 109848, 1, 16, 16, 42, 43, 10, kSequencePointKind_Normal, 0, 5 },
	{ 109849, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 6 },
	{ 109849, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 7 },
	{ 109849, 1, 21, 21, 17, 18, 0, kSequencePointKind_Normal, 0, 8 },
	{ 109849, 1, 21, 21, 19, 38, 1, kSequencePointKind_Normal, 0, 9 },
	{ 109849, 1, 21, 21, 19, 38, 3, kSequencePointKind_StepOut, 0, 10 },
	{ 109849, 1, 21, 21, 39, 40, 9, kSequencePointKind_Normal, 0, 11 },
	{ 109850, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 12 },
	{ 109850, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 13 },
	{ 109850, 1, 22, 22, 17, 18, 0, kSequencePointKind_Normal, 0, 14 },
	{ 109850, 1, 22, 22, 19, 40, 1, kSequencePointKind_Normal, 0, 15 },
	{ 109850, 1, 22, 22, 19, 40, 2, kSequencePointKind_StepOut, 0, 16 },
	{ 109850, 1, 22, 22, 41, 42, 10, kSequencePointKind_Normal, 0, 17 },
	{ 109851, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 18 },
	{ 109851, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 19 },
	{ 109851, 1, 27, 27, 17, 18, 0, kSequencePointKind_Normal, 0, 20 },
	{ 109851, 1, 27, 27, 19, 71, 1, kSequencePointKind_Normal, 0, 21 },
	{ 109851, 1, 27, 27, 19, 71, 2, kSequencePointKind_StepOut, 0, 22 },
	{ 109851, 1, 27, 27, 72, 73, 15, kSequencePointKind_Normal, 0, 23 },
	{ 109852, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 24 },
	{ 109852, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 25 },
	{ 109852, 1, 28, 28, 17, 18, 0, kSequencePointKind_Normal, 0, 26 },
	{ 109852, 1, 28, 28, 19, 63, 1, kSequencePointKind_Normal, 0, 27 },
	{ 109852, 1, 28, 28, 19, 63, 3, kSequencePointKind_StepOut, 0, 28 },
	{ 109852, 1, 28, 28, 64, 65, 9, kSequencePointKind_Normal, 0, 29 },
	{ 109853, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 30 },
	{ 109853, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 31 },
	{ 109853, 1, 33, 33, 17, 18, 0, kSequencePointKind_Normal, 0, 32 },
	{ 109853, 1, 33, 33, 19, 43, 1, kSequencePointKind_Normal, 0, 33 },
	{ 109853, 1, 33, 33, 19, 43, 2, kSequencePointKind_StepOut, 0, 34 },
	{ 109853, 1, 33, 33, 44, 45, 10, kSequencePointKind_Normal, 0, 35 },
	{ 109854, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 36 },
	{ 109854, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 37 },
	{ 109854, 1, 38, 38, 17, 18, 0, kSequencePointKind_Normal, 0, 38 },
	{ 109854, 1, 38, 38, 19, 43, 1, kSequencePointKind_Normal, 0, 39 },
	{ 109854, 1, 38, 38, 19, 43, 2, kSequencePointKind_StepOut, 0, 40 },
	{ 109854, 1, 38, 38, 44, 45, 10, kSequencePointKind_Normal, 0, 41 },
	{ 109855, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 42 },
	{ 109855, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 43 },
	{ 109855, 1, 39, 39, 17, 18, 0, kSequencePointKind_Normal, 0, 44 },
	{ 109855, 1, 39, 39, 19, 41, 1, kSequencePointKind_Normal, 0, 45 },
	{ 109855, 1, 39, 39, 19, 41, 3, kSequencePointKind_StepOut, 0, 46 },
	{ 109855, 1, 39, 39, 42, 43, 9, kSequencePointKind_Normal, 0, 47 },
	{ 109856, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 48 },
	{ 109856, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 49 },
	{ 109856, 1, 43, 43, 9, 10, 0, kSequencePointKind_Normal, 0, 50 },
	{ 109856, 1, 44, 44, 13, 33, 1, kSequencePointKind_Normal, 0, 51 },
	{ 109856, 1, 44, 44, 13, 33, 2, kSequencePointKind_StepOut, 0, 52 },
	{ 109856, 1, 45, 45, 9, 10, 8, kSequencePointKind_Normal, 0, 53 },
	{ 109857, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 54 },
	{ 109857, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 55 },
	{ 109857, 1, 47, 47, 52, 74, 0, kSequencePointKind_Normal, 0, 56 },
	{ 109857, 1, 47, 47, 52, 74, 2, kSequencePointKind_StepOut, 0, 57 },
	{ 109858, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 58 },
	{ 109858, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 59 },
	{ 109858, 1, 50, 50, 9, 10, 0, kSequencePointKind_Normal, 0, 60 },
	{ 109858, 1, 51, 51, 13, 31, 1, kSequencePointKind_Normal, 0, 61 },
	{ 109858, 1, 51, 51, 13, 31, 3, kSequencePointKind_StepOut, 0, 62 },
	{ 109858, 1, 51, 51, 0, 0, 9, kSequencePointKind_Normal, 0, 63 },
	{ 109858, 1, 52, 52, 17, 58, 12, kSequencePointKind_Normal, 0, 64 },
	{ 109858, 1, 52, 52, 17, 58, 17, kSequencePointKind_StepOut, 0, 65 },
	{ 109858, 1, 54, 54, 13, 44, 23, kSequencePointKind_Normal, 0, 66 },
	{ 109858, 1, 54, 54, 13, 44, 26, kSequencePointKind_StepOut, 0, 67 },
	{ 109858, 1, 54, 54, 13, 44, 31, kSequencePointKind_StepOut, 0, 68 },
	{ 109858, 1, 55, 55, 9, 10, 37, kSequencePointKind_Normal, 0, 69 },
	{ 109859, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 70 },
	{ 109859, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 71 },
	{ 109859, 1, 58, 58, 9, 10, 0, kSequencePointKind_Normal, 0, 72 },
	{ 109859, 1, 59, 59, 13, 31, 1, kSequencePointKind_Normal, 0, 73 },
	{ 109859, 1, 59, 59, 13, 31, 3, kSequencePointKind_StepOut, 0, 74 },
	{ 109859, 1, 59, 59, 0, 0, 9, kSequencePointKind_Normal, 0, 75 },
	{ 109859, 1, 60, 60, 17, 58, 12, kSequencePointKind_Normal, 0, 76 },
	{ 109859, 1, 60, 60, 17, 58, 17, kSequencePointKind_StepOut, 0, 77 },
	{ 109859, 1, 62, 62, 13, 35, 23, kSequencePointKind_Normal, 0, 78 },
	{ 109859, 1, 62, 62, 13, 35, 25, kSequencePointKind_StepOut, 0, 79 },
	{ 109859, 1, 63, 63, 13, 38, 31, kSequencePointKind_Normal, 0, 80 },
	{ 109859, 1, 63, 63, 13, 38, 33, kSequencePointKind_StepOut, 0, 81 },
	{ 109859, 1, 64, 64, 13, 20, 39, kSequencePointKind_Normal, 0, 82 },
	{ 109859, 1, 64, 64, 13, 20, 40, kSequencePointKind_StepOut, 0, 83 },
	{ 109859, 1, 65, 65, 9, 10, 46, kSequencePointKind_Normal, 0, 84 },
	{ 109860, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 85 },
	{ 109860, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 86 },
	{ 109860, 1, 68, 68, 9, 10, 0, kSequencePointKind_Normal, 0, 87 },
	{ 109860, 1, 69, 69, 13, 52, 1, kSequencePointKind_Normal, 0, 88 },
	{ 109860, 1, 69, 69, 13, 52, 4, kSequencePointKind_StepOut, 0, 89 },
	{ 109860, 1, 70, 70, 9, 10, 10, kSequencePointKind_Normal, 0, 90 },
	{ 109900, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 91 },
	{ 109900, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 92 },
	{ 109900, 1, 128, 128, 9, 10, 0, kSequencePointKind_Normal, 0, 93 },
	{ 109900, 1, 129, 129, 13, 32, 1, kSequencePointKind_Normal, 0, 94 },
	{ 109900, 1, 129, 129, 0, 0, 11, kSequencePointKind_Normal, 0, 95 },
	{ 109900, 1, 130, 130, 17, 30, 14, kSequencePointKind_Normal, 0, 96 },
	{ 109900, 1, 130, 130, 17, 30, 21, kSequencePointKind_StepOut, 0, 97 },
	{ 109900, 1, 131, 131, 9, 10, 27, kSequencePointKind_Normal, 0, 98 },
	{ 109901, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 99 },
	{ 109901, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 100 },
	{ 109901, 1, 135, 135, 9, 10, 0, kSequencePointKind_Normal, 0, 101 },
	{ 109901, 1, 136, 136, 13, 32, 1, kSequencePointKind_Normal, 0, 102 },
	{ 109901, 1, 136, 136, 0, 0, 11, kSequencePointKind_Normal, 0, 103 },
	{ 109901, 1, 137, 137, 17, 30, 14, kSequencePointKind_Normal, 0, 104 },
	{ 109901, 1, 137, 137, 17, 30, 21, kSequencePointKind_StepOut, 0, 105 },
	{ 109901, 1, 138, 138, 9, 10, 27, kSequencePointKind_Normal, 0, 106 },
	{ 109902, 0, 0, 0, 0, 0, -1, kSequencePointKind_Normal, 0, 107 },
	{ 109902, 0, 0, 0, 0, 0, 16777215, kSequencePointKind_Normal, 0, 108 },
	{ 109902, 1, 142, 142, 9, 10, 0, kSequencePointKind_Normal, 0, 109 },
	{ 109902, 1, 143, 143, 13, 33, 1, kSequencePointKind_Normal, 0, 110 },
	{ 109902, 1, 143, 143, 0, 0, 11, kSequencePointKind_Normal, 0, 111 },
	{ 109902, 1, 144, 144, 17, 31, 14, kSequencePointKind_Normal, 0, 112 },
	{ 109902, 1, 144, 144, 17, 31, 21, kSequencePointKind_StepOut, 0, 113 },
	{ 109902, 1, 145, 145, 9, 10, 27, kSequencePointKind_Normal, 0, 114 },
};
#else
extern Il2CppSequencePoint g_sequencePointsUnityEngine_DirectorModule[];
Il2CppSequencePoint g_sequencePointsUnityEngine_DirectorModule[1] = { { 0, 0, 0, 0, 0, 0, 0, kSequencePointKind_Normal, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#else
static const Il2CppCatchPoint g_catchPoints[1] = { { 0, 0, 0, 0, } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[] = {
{ "", { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0} },
{ "/Users/<USER>/build/output/unity/unity/Modules/Director/ScriptBindings/PlayableDirector.bindings.cs", { 86, 182, 85, 14, 191, 248, 32, 219, 9, 244, 255, 75, 19, 226, 168, 204} },
};
#else
static const Il2CppSequencePointSourceFile g_sequencePointSourceFiles[1] = { NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = 
{
	{ 14128, 1 },
};
#else
static const Il2CppTypeSourceFilePair g_typeSourceFiles[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodScope g_methodScopes[10] = 
{
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 17 },
	{ 0, 12 },
	{ 0, 12 },
	{ 0, 38 },
	{ 0, 47 },
	{ 0, 28 },
	{ 0, 28 },
	{ 0, 28 },
};
#else
static const Il2CppMethodScope g_methodScopes[1] = { { 0, 0 } };
#endif
#if IL2CPP_MONO_DEBUGGER
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[61] = 
{
	{ 12, 0, 1 },
	{ 0, 0, 0 },
	{ 12, 1, 1 },
	{ 17, 2, 1 },
	{ 0, 0, 0 },
	{ 12, 3, 1 },
	{ 12, 4, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 38, 5, 1 },
	{ 47, 6, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 28, 7, 1 },
	{ 28, 8, 1 },
	{ 28, 9, 1 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
	{ 0, 0, 0 },
};
#else
static const Il2CppMethodHeaderInfo g_methodHeaderInfos[1] = { { 0, 0, 0 } };
#endif
IL2CPP_EXTERN_C const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_DirectorModule;
const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_DirectorModule = 
{
	(Il2CppMethodExecutionContextInfo*)g_methodExecutionContextInfos,
	(Il2CppMethodExecutionContextInfoIndex*)g_methodExecutionContextInfoIndexes,
	(Il2CppMethodScope*)g_methodScopes,
	(Il2CppMethodHeaderInfo*)g_methodHeaderInfos,
	(Il2CppSequencePointSourceFile*)g_sequencePointSourceFiles,
	115,
	(Il2CppSequencePoint*)g_sequencePointsUnityEngine_DirectorModule,
	0,
	(Il2CppCatchPoint*)g_catchPoints,
	1,
	(Il2CppTypeSourceFilePair*)g_typeSourceFiles,
	(const char**)g_methodExecutionContextInfoStrings,
};
