﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Character__ctor_m5DCCE862D40487C733C29A233DB8513A9A6A02F6 (void);
extern void Character__ctor_mF787F05C8C34182A25009FEB50076E355D47EBD6 (void);
extern void Character__ctor_mEEAC42D4227E0053C8008C12B222CC208D781795 (void);
extern void Character__ctor_m21FBFAF1F6324565246096EFFB81C3F9E15D43CC (void);
extern void ColorUtilities_CompareColors_m0F0F140129DEE889FB8AE3B2921C495E94B5E875 (void);
extern void ColorUtilities_CompareColorsRgb_m8A3DBC16E8330FE4012F33F61BF54CCEDA31C55C (void);
extern void ColorUtilities_CompareColors_m76427940D8B2E949E9BA4E483D3FEBB96682E4C3 (void);
extern void ColorUtilities_CompareColorsRgb_m5059425DB7F9D3F1CEDAB697B6A684CD05D691D0 (void);
extern void ColorUtilities_MultiplyColors_m81D3E41A86E195794977F31C5A944DB40FE734B4 (void);
extern void FastAction_Add_m86840746EF1B743CCEB14B3C810BB4360D58F7B0 (void);
extern void FastAction_Remove_mD486A8B1FB253F9F6C6EE8E6B9A88A9A6B34AEC0 (void);
extern void FastAction_Call_mD5ADC8889A6382B473762773A188EAC234EBA6AE (void);
extern void FastAction__ctor_m837FFCD82DA457A7BFCC2EA03FBD3E358DA1F3EE (void);
extern void FontAssetCreationEditorSettings__ctor_m580CDC10BB7CC670BAB67945F88C20397EC29D2B (void);
extern void FontAsset_get_fontAssetCreationEditorSettings_m024033F91B976A8EAA5CBE67D3DB2A756B91CF01 (void);
extern void FontAsset_set_fontAssetCreationEditorSettings_mF7EE6A46807D78A7E99872171D2AD774DE20C7EB (void);
extern void FontAsset_get_sourceFontFile_m6B0E805BD1B7712F0B5135D157E96F3F40314830 (void);
extern void FontAsset_set_sourceFontFile_m2E6D2AED5E5D2585A09E9BF830387DEB10A2F4E8 (void);
extern void FontAsset_get_atlasPopulationMode_m5364C5A9E84969D8E4FF8436BD18A3F10BF90366 (void);
extern void FontAsset_set_atlasPopulationMode_m1A9DD5C702ED0924B9C208F4CE5ADEACF9188268 (void);
extern void FontAsset_get_faceInfo_mF020EC579E3C18A6279D55D86AF1C585031B49A9 (void);
extern void FontAsset_set_faceInfo_mCCA87B67C4CA2C0A1F6D85FB1FAA09667996EDCD (void);
extern void FontAsset_get_familyNameHashCode_mF2DB211A5712A291B2D28FCDB7F7C29057770330 (void);
extern void FontAsset_set_familyNameHashCode_mE1495199BCE7B771CC920E2DBB86A8AF1518CB55 (void);
extern void FontAsset_get_styleNameHashCode_m3CD3D77F64DAEB31D8F69E4D7CC1AD0AC784ABF5 (void);
extern void FontAsset_set_styleNameHashCode_mE1BE5B75DE1E9EA0F76569609E6C4FFDC57558BA (void);
extern void FontAsset_get_glyphTable_m212E940F74AEE62ACBB3374486296CA518D934B5 (void);
extern void FontAsset_set_glyphTable_m2753BC6CEE011983C2B4B181867C3EB00CDE87D4 (void);
extern void FontAsset_get_glyphLookupTable_mD04A90D8262F1963EDC472272B67BBFAF73DEEA5 (void);
extern void FontAsset_get_characterTable_mC77FAE1355269834F7C8A2D46AFB4BFE7B7AD72D (void);
extern void FontAsset_set_characterTable_m44703292F669F2F6D4920EB9427077E24FB1512C (void);
extern void FontAsset_get_characterLookupTable_m7E76D6C706C5CEB04A9541C68AE6D9E5C75F0FFC (void);
extern void FontAsset_get_atlasTexture_mC49216F40093C7AC4FA5DA68F9F5C9FCC83B8F27 (void);
extern void FontAsset_get_atlasTextures_mADD7A506F0444A1EE4F1D52536B0C5DA9BE35075 (void);
extern void FontAsset_set_atlasTextures_m5315BA4903B77742EFA1E54CEA2AF12726B10A99 (void);
extern void FontAsset_get_atlasTextureCount_mC20300C53E52D7A8351DE296BAD565268568119F (void);
extern void FontAsset_get_isMultiAtlasTexturesEnabled_mF222228A76102BB0EA593A60439D22F912761F1E (void);
extern void FontAsset_set_isMultiAtlasTexturesEnabled_mA470AF6D312989E752C68DC0FD5700235877566B (void);
extern void FontAsset_get_clearDynamicDataOnBuild_mC1F714E56F087B29E0A3B43D820EDCEB78E9EE75 (void);
extern void FontAsset_set_clearDynamicDataOnBuild_mA1C6F298742DF78EC0F81157F0E04246F8B82F7E (void);
extern void FontAsset_get_atlasWidth_mE711550FDD4B5F988B77AB5D332A80A34B5CF364 (void);
extern void FontAsset_set_atlasWidth_mFFB9D37EB1C648384ED1426B42E26A4104D329B1 (void);
extern void FontAsset_get_atlasHeight_m306FBF7D35C39123A4770E147FAF95B1B8DE8086 (void);
extern void FontAsset_set_atlasHeight_m7116DFD32F38971CE39D7F4BF84CB5217DCAA2B5 (void);
extern void FontAsset_get_atlasPadding_m251A35FB5F499EE66CC2E2150CBEDB2C8C5D5581 (void);
extern void FontAsset_set_atlasPadding_mB34AA836A3D02722ABED71B3583D767560CA956D (void);
extern void FontAsset_get_atlasRenderMode_m036D4BA220E5D4B0C407CA6BC1B09D8914B5058A (void);
extern void FontAsset_set_atlasRenderMode_m993764193CE75D57DC4CC755336596681A7866D2 (void);
extern void FontAsset_get_usedGlyphRects_mE039AEF3AE45A15A86B2C0F774E6ED58AFA2F341 (void);
extern void FontAsset_set_usedGlyphRects_mBF80C1063C0A274AD95F55C43DA734E126F6643F (void);
extern void FontAsset_get_freeGlyphRects_mCDAEF0519586C5248BBEDEAA85086CC117903E88 (void);
extern void FontAsset_set_freeGlyphRects_mED3C0E01ABFC63CE700C701476BA2B66D112AA9B (void);
extern void FontAsset_get_fontFeatureTable_m7C4EB9A655B237CE02FAF7B8B16C2F2863FE5070 (void);
extern void FontAsset_set_fontFeatureTable_m3FD11B99122416777808E1CE5414D7BA40920C3B (void);
extern void FontAsset_get_fallbackFontAssetTable_m43303BFBE8A8C55D8CE8A67C47EFAFF5A712CB69 (void);
extern void FontAsset_set_fallbackFontAssetTable_mE22C3D2323111FD6A5DF9847FB812BD41E18832E (void);
extern void FontAsset_get_fontWeightTable_m8DADE0BCE53D53EEECE27025F7E94FDD4BF00099 (void);
extern void FontAsset_set_fontWeightTable_m39D8B63BD3FCC773AAB5634C6D9314C713161814 (void);
extern void FontAsset_get_regularStyleWeight_m6C4B4D4CAD36800E6E686A05A5DB8D4475F2707F (void);
extern void FontAsset_set_regularStyleWeight_m2D1E5440A5E1794A003FF087A87393DA9A114385 (void);
extern void FontAsset_get_regularStyleSpacing_mB7EEEA236312F5AC31FD3B787808279206F521B1 (void);
extern void FontAsset_set_regularStyleSpacing_m7CCE54FB9163D65A6B40269B2BDD30199023E797 (void);
extern void FontAsset_get_boldStyleWeight_m804ACC85DD80DC72DB4BCC83C3FB866411F8EFCA (void);
extern void FontAsset_set_boldStyleWeight_m204B04CF9E98AD8669025BFDC0EF3CE9AB5CBBA2 (void);
extern void FontAsset_get_boldStyleSpacing_mB8CF4F4880B110E41D566648FF1D995010CF1FF0 (void);
extern void FontAsset_set_boldStyleSpacing_m62DAA35837E8563DD76E3D162B6DB59BE3804914 (void);
extern void FontAsset_get_italicStyleSlant_m69E70060C6E7940B4ACE61F2B7CB8965F86DA96B (void);
extern void FontAsset_set_italicStyleSlant_m223875ED81B0397CA36E94A6F346AEE68510C0D2 (void);
extern void FontAsset_get_tabMultiple_m9C0422A00BFCF82091F14F4E303E2717247350AE (void);
extern void FontAsset_set_tabMultiple_mC927B74D27FBB94245E09FE39D9F6749AF07017B (void);
extern void FontAsset_CreateFontAsset_mDBAB2C51721B94702B8706450DAF74FD2E757712 (void);
extern void FontAsset_CreateFontAsset_mBC142F8527671635D9472BCC22C65A2E94368253 (void);
extern void FontAsset_CreateFontAsset_m5C2993AF8A6DB979E34173276952C0DD70524777 (void);
extern void FontAsset_CreateFontAsset_m627EE2CFBDCEADB906A5CD26FB474D3C1A37A166 (void);
extern void FontAsset_CreateFontAsset_mEFD731F35B521BB833F532C0EE7637F97647E650 (void);
extern void FontAsset_CreateFontAsset_mF5E82AB887021B02F7DA71E36328A9D1C943F1B5 (void);
extern void FontAsset_CreateFontAssetInstance_m9114A363FC6DDEB899CC75C73A41CF903A59333E (void);
extern void FontAsset_Awake_mB7906A1E21F5FAB84A023B97435F75C09EAB92ED (void);
extern void FontAsset_OnDestroy_m3587016A089072C5C03168AA4C6AA1956FE12785 (void);
extern void FontAsset_ReadFontAssetDefinition_m6D84DBCB130D530B2F78A7E24232D8A6A81AEC48 (void);
extern void FontAsset_InitializeDictionaryLookupTables_m29A4AEF49CF11A0E49C229EF13B2262AE66757FF (void);
extern void FontAsset_InitializeGlyphLookupDictionary_m82782B7B5C602AD5097A016BF668868C0892CCF6 (void);
extern void FontAsset_InitializeCharacterLookupDictionary_m8886A4CA911334BD319AB78F1CBBD68E13624BB6 (void);
extern void FontAsset_InitializeLigatureSubstitutionLookupDictionary_m85D0338B542EEF7E5DE8224AA699730752F93FD3 (void);
extern void FontAsset_InitializeGlyphPaidAdjustmentRecordsLookupDictionary_mDF1C20792344999B9CF500685E6256B0642CE7DD (void);
extern void FontAsset_InitializeMarkToBaseAdjustmentRecordsLookupDictionary_m7BC52CF67C055F71B1E9A79B761F79A86E5D8716 (void);
extern void FontAsset_InitializeMarkToMarkAdjustmentRecordsLookupDictionary_mC911E1C69D58A7490587C63473D65BA1DB775D56 (void);
extern void FontAsset_AddSynthesizedCharactersAndFaceMetrics_m203BD62D0A537A6EA7CD7DBA1FF9A94301492933 (void);
extern void FontAsset_AddSynthesizedCharacter_m6ABFCE6454A09D5CF7914F318DDC79198C47F9EA (void);
extern void FontAsset_AddCharacterToLookupCache_mB90E06CE313CC0BB6F81415BF8FB4E043108EED8 (void);
extern void FontAsset_LoadFontFace_m64C78A2FE5DA2E7029E43B467A1B242827B45B4F (void);
extern void FontAsset_SortCharacterTable_m9A551DF3B19E246E8C4BE86463E0ED1DEB27D321 (void);
extern void FontAsset_SortGlyphTable_mC853714CB002D923A19C3A925BB24D6BF42A08CD (void);
extern void FontAsset_SortFontFeatureTable_m072B32D6D8C562F60D3D6CBCC7DCB3282EDD587F (void);
extern void FontAsset_SortAllTables_mACA7063865A460F5949E5B8A8D978D588124A094 (void);
extern void FontAsset_HasCharacter_m6BAF48714E1BF5D8EE7ACF33F774C8C6EEE452F3 (void);
extern void FontAsset_HasCharacter_mE87EEF6CDA1F4E1D6928CC9A3C01A91922D4FB21 (void);
extern void FontAsset_HasCharacter_m3E405FA081E68243DDB6558FA03530686E894EFE (void);
extern void FontAsset_HasCharacter_Internal_mDC0D2954E0975A7DBC8829E894CDBCABEA7D6A60 (void);
extern void FontAsset_HasCharacters_mD670CCEB48448CE5C1430B938F99D4FC659FB2F8 (void);
extern void FontAsset_HasCharacters_m97A50BC627C163418CAE0B42A50893057B025E90 (void);
extern void FontAsset_HasCharacters_m048839FDD1876CDCA3C5A744592545B46C75E15B (void);
extern void FontAsset_GetCharacters_m5CCEC5A0F89B0BC29B1EFEE9E33923F6879409F4 (void);
extern void FontAsset_GetCharactersArray_m4CE08FECFCF3F9913B5B29DF0F2272FA4B3F945F (void);
extern void FontAsset_GetGlyphIndex_mF20097CDB68A8CE866E61D4C237FBB95257A9745 (void);
extern void FontAsset_RegisterFontAssetForFontFeatureUpdate_m5D26FB74AB0C3AA8DC363DA517C1017641F85739 (void);
extern void FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m021524DE2B5F8ABE9DDA58447F896159EA15E0F3 (void);
extern void FontAsset_RegisterAtlasTextureForApply_mAB5DC9E4B77C8E0FCADF0AD98E8D63C5229FC383 (void);
extern void FontAsset_UpdateAtlasTexturesInQueue_m359EFCCB6F692F5F8546D4205F366B079075792E (void);
extern void FontAsset_UpdateFontAssetsInUpdateQueue_m67B9FE54C99FDC8FD3FE3471768C416083E36768 (void);
extern void FontAsset_TryAddCharacters_m7F1D0CB7E4D9B8D3CE44D4D01F9CDCEFD4D1B46B (void);
extern void FontAsset_TryAddCharacters_m9618B4F12C004B8267E0D17ED81B94BE48D85119 (void);
extern void FontAsset_TryAddCharacters_m5E282618D9ED92AD0112BC7B6B2C3B1066DDFA63 (void);
extern void FontAsset_TryAddCharacters_mDA1C3A68799C84A80C27CDB84482684F6822137F (void);
extern void FontAsset_TryAddGlyphInternal_mA41540AE85F2F11562E1DB5B763B37D29D9D497B (void);
extern void FontAsset_TryAddCharacterInternal_mCDC9AE2C61B9F73B8879C3F5AE00598A67B2BA7F (void);
extern void FontAsset_TryGetCharacter_and_QueueRenderToTexture_mA76A244F58E0F2978178FBBEB18F2E0DCA568AEC (void);
extern void FontAsset_TryAddGlyphsToAtlasTextures_m83F7EDB3193B3C9A4FA86B89A51E9FA6A41F6834 (void);
extern void FontAsset_TryAddGlyphsToNewAtlasTexture_m8F98FBF7A0EC1B37C4DB43536DA42D3864F6F3AB (void);
extern void FontAsset_SetupNewAtlasTexture_m38F81BE1582A15DDDB950E7AAC650CD9B7D14168 (void);
extern void FontAsset_UpdateAllFontFeatures_mE00FE075794A727BA10860C6B9BB237BAA2EEEE2 (void);
extern void FontAsset_UpdateGlyphAdjustmentRecords_mD1C9297EA75EA767A823709CC39B6E57905E22A3 (void);
extern void FontAsset_UpdateGlyphAdjustmentRecords_mC9882537E81709FE1EFA9B744D88C6C32ACEDF52 (void);
extern void FontAsset_UpdateGlyphAdjustmentRecords_m2D0444352012E8DFDD6036025886EC0CED0AD82A (void);
extern void FontAsset_UpdateGlyphAdjustmentRecords_m9410421BA99635607C50EED1C9C374570EFABC60 (void);
extern void FontAsset_ClearFontAssetData_m225ADFCBB0CFD481E18637F3D3FDFFEAFC6FE9A1 (void);
extern void FontAsset_ClearFontAssetDataInternal_m3B01092F3CBA3EDA9A06CE14B20628C1A654F759 (void);
extern void FontAsset_UpdateFontAssetData_mAAC0ED05410942C08E8EFD4678F9565FD8C373D4 (void);
extern void FontAsset_ClearFontAssetTables_mA528E871C1830F3A5303DABA01A7D28747777F73 (void);
extern void FontAsset_ClearAtlasTextures_m5B320A65E1CD35F2C17E27F09158F8E9BDA9EA2B (void);
extern void FontAsset_DestroyAtlasTextures_mBE2810F8C55E286B5B7ABE24A6F9132F51CBE027 (void);
extern void FontAsset__ctor_mD55676BD025F9D05DBC9A5B32480E092169B9D45 (void);
extern void FontAsset__cctor_m0F11465E3C2F3D158DB9BF403CE86298432BD00E (void);
extern void U3CU3Ec__cctor_mEB4ED309CE67EB2C5633590466BD1C4EE90E0D5C (void);
extern void U3CU3Ec__ctor_m3CDF0903C024856278B5A7DF46C7EFCBA6F6B651 (void);
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__151_0_mF8A06416B0EE30DF937290D36C5AC1F1E94BF918 (void);
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__152_0_m43F8B101FBB6D5E117C6AF29EE8EAC75F6D48BA1 (void);
extern void FontAssetUtilities_GetCharacterFromFontAsset_m0F073D15EC39A1D4F302F02A5E2F583F28889332 (void);
extern void FontAssetUtilities_GetCharacterFromFontAsset_Internal_m3D8D41600A318422D89103D3839B5CFCF15A956E (void);
extern void FontAssetUtilities_GetCharacterFromFontAssets_mB26999A2C8D9AD3D35857403DD59BEED6D008BA0 (void);
extern void FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_mB9147480CA9223089D54748F37F227BC180D78E1 (void);
extern void FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_m5A04BE23599904FF0B23061BC9F7EDBF6AC7DB88 (void);
extern void FontFeatureTable_get_multipleSubstitutionRecords_mEA4CFF09748B2EAAE509E9C5B778A2DD0B0DE761 (void);
extern void FontFeatureTable_set_multipleSubstitutionRecords_m8AF6E0295B7FE6FA5CCCC2358F5D5B8D35BD7847 (void);
extern void FontFeatureTable_get_ligatureRecords_m990413B6DEF09939C91F6FCBC840D0A6335F0F13 (void);
extern void FontFeatureTable_set_ligatureRecords_mC9887A0AA05EF8CC206B41307769BEEC76721A81 (void);
extern void FontFeatureTable_get_glyphPairAdjustmentRecords_mABE78F7C2EA171927CC33170617D72E6C976323E (void);
extern void FontFeatureTable_set_glyphPairAdjustmentRecords_m05F45829A25D9B115D2450F629B705ED6B66B1A9 (void);
extern void FontFeatureTable_get_MarkToBaseAdjustmentRecords_m73A1A8FCDB3E9629C15FC2568473B235B9A52D9B (void);
extern void FontFeatureTable_set_MarkToBaseAdjustmentRecords_m4CE5E05EC230B9D606F3690B9C17321444CF5374 (void);
extern void FontFeatureTable_get_MarkToMarkAdjustmentRecords_m3DB78C6BBC2E41936F04E46107002DC5F2136B8F (void);
extern void FontFeatureTable_set_MarkToMarkAdjustmentRecords_m28DCFFE2C4A217947BC1000280B78F19A6DECE0D (void);
extern void FontFeatureTable__ctor_m5F00F284C63F1867F679A3250ABFC1393C27025C (void);
extern void FontFeatureTable_SortGlyphPairAdjustmentRecords_m2F5E2ED405FCAEE946CE5CF81163DDCC1B02A905 (void);
extern void FontFeatureTable_SortMarkToBaseAdjustmentRecords_m2AF48E3FC40E5C970FCD9A4ACA4354FD3CD09004 (void);
extern void FontFeatureTable_SortMarkToMarkAdjustmentRecords_mF4A796852F11F07614DF6434DB8E3122E94E7E3B (void);
extern void U3CU3Ec__cctor_m024C5AAB034EF6BBF574EA4EB3AE66D03E259CEA (void);
extern void U3CU3Ec__ctor_m51815D1379A3BDB616D65C006DA7AB32406723F4 (void);
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__25_0_m74FE6156C6CC986E55868DB69C6EDB79F6F19066 (void);
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__25_1_m3F126EE9B7D04A378F4203E51050ADDECE63CB68 (void);
extern void U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__26_0_m3220742BC5DB3D8370C277F2EA62943B6C0A36DB (void);
extern void U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__26_1_m58B31CA43C54C82A5C97EE0869052303FD54B338 (void);
extern void U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__27_0_mF081BD05B971C16DA0D3AB99B5A312716FFA5BC3 (void);
extern void U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__27_1_m22B6E5543ACDFAC59B11D4A570B19A0974ECFA49 (void);
extern void Extents__ctor_m29242B7ECE336324673F4E94B869F0486922BB38 (void);
extern void Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC (void);
extern void LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F (void);
extern void LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8 (void);
extern void LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB (void);
extern void MaterialManager_GetFallbackMaterial_m10F67CE1AE1E0B9D8BA8AFC06110FADA2404B5A1 (void);
extern void MaterialManager_GetFallbackMaterial_mB65C8D7625B0D0A0D623FC6AC545469AF6B7724D (void);
extern void MaterialManager_CopyMaterialPresetProperties_m2DB1A033E378F3DF347DEA0DC51F1E51776169F4 (void);
extern void MaterialManager__cctor_mB6337D5678E1F45035C1F8EE33D389AB4CBA6527 (void);
extern void MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A (void);
extern void MaterialReference_Contains_m1B4B5E75B1373C5E367FB36BE5B52E660A92DCCD (void);
extern void MaterialReference_AddMaterialReference_m13CC47A7CA6C8781EA68A355B36FCD5AFF467A40 (void);
extern void MaterialReference_AddMaterialReference_mEE57C2284E5BE17BDD80C69165FAECB3B4CC1BCD (void);
extern void MaterialReferenceManager_get_instance_m30BD1A367C3807D0B42CF7156CE699B15E51ED07 (void);
extern void MaterialReferenceManager_AddFontAsset_mF950087FC7EAA324CC15D3E19D62138A96F6FDF7 (void);
extern void MaterialReferenceManager_AddFontAssetInternal_m6F5A4E5ED988BA6F482F015F051ACD19D7B0A005 (void);
extern void MaterialReferenceManager_AddSpriteAsset_mC95864F8450686586161E80A908552FC9F417A47 (void);
extern void MaterialReferenceManager_AddSpriteAssetInternal_m4B3DB7DCA9D165B2C48B513ABD7C9061389B4830 (void);
extern void MaterialReferenceManager_AddSpriteAsset_m7FBAAC41386CFA769FDDFF1630D041C4D008A322 (void);
extern void MaterialReferenceManager_AddSpriteAssetInternal_m788619DC6BAD5B77E9419ACBDECBCCFE1A6AC97C (void);
extern void MaterialReferenceManager_AddFontMaterial_m76DC9390D0599D1AC898AE981C8756EA0E536AA6 (void);
extern void MaterialReferenceManager_AddFontMaterialInternal_m732F46EF768A41B9519917F4FA1E746E056C745C (void);
extern void MaterialReferenceManager_AddColorGradientPreset_m1496DD123DB526F2B3F49DD3BCAED78A6B1B83EB (void);
extern void MaterialReferenceManager_AddColorGradientPreset_Internal_mF27270501EB3725B4CBE4C241B4A2FCD8D871BF1 (void);
extern void MaterialReferenceManager_Contains_m7A3365858320686F56F39123E906375ACDBE00A5 (void);
extern void MaterialReferenceManager_Contains_m679BD7A4F3989E74BAB87957834A2093A557B0A1 (void);
extern void MaterialReferenceManager_TryGetFontAsset_m499C7538909343667E9B837489662862CFF9FB56 (void);
extern void MaterialReferenceManager_TryGetFontAssetInternal_m2FECC618624B12D200EB311F59CBEECA7CDBB69D (void);
extern void MaterialReferenceManager_TryGetSpriteAsset_m1909F75399C0A23DDE7DEDCA60476E1F1ED567EF (void);
extern void MaterialReferenceManager_TryGetSpriteAssetInternal_mC434A7C6DB005EDBBA52154E2AB0E36ED7083C84 (void);
extern void MaterialReferenceManager_TryGetColorGradientPreset_m7442603626A04928C6C042BDAC3D3957B8C0AA99 (void);
extern void MaterialReferenceManager_TryGetColorGradientPresetInternal_mD8018B3225786E71F804D629F3107AB75EE5212B (void);
extern void MaterialReferenceManager_TryGetMaterial_mDC2610737935CD2DE6B1F6302F63C2F3BF7E09CF (void);
extern void MaterialReferenceManager_TryGetMaterialInternal_mEBFC9CE0A6063B25FEA9070F22FD8AD27107ECE7 (void);
extern void MaterialReferenceManager__ctor_mC102EC445A27BE8E3968ADB80EF8FEF3BCFB7778 (void);
extern void MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61 (void);
extern void MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC (void);
extern void MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475 (void);
extern void MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830 (void);
extern void MeshInfo_ClearUnusedVertices_m8D57192C6582A8E8EC3A2E133B057896ECE5FC2E (void);
extern void MeshInfo_ClearUnusedVertices_mA71B4EBAF0308EEA00061C57244BCB2EDE2D557F (void);
extern void MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1 (void);
extern void MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F (void);
extern void MeshInfo__cctor_m667601EA787620405D7DB5F0B37E4D489DE067D9 (void);
extern void SpriteAsset_get_faceInfo_m54EC5227F682ED6A24F5633283258E6641CDA4DC (void);
extern void SpriteAsset_set_faceInfo_m060A5DBBD5941A53BFE9D45E2B637D88ED8223EA (void);
extern void SpriteAsset_get_spriteSheet_mC53205114A12A79F7495FA5F5EFC9948F151256B (void);
extern void SpriteAsset_set_spriteSheet_m1DE591615ABCBB4B10118BF4C0E1B57F559C6469 (void);
extern void SpriteAsset_get_spriteCharacterTable_m8D0D65C430AD8BC8C2BC8151DC4672CC0F690E0A (void);
extern void SpriteAsset_set_spriteCharacterTable_m38553B81E01B502CCD568A654E9EF3B0D0BCA92D (void);
extern void SpriteAsset_get_spriteCharacterLookupTable_mDB0BA7D703F58A05BB765AE44D6244D98C17DE03 (void);
extern void SpriteAsset_set_spriteCharacterLookupTable_m38F3E7A0A52B82595C87E6A630B156E4D22F2E25 (void);
extern void SpriteAsset_get_spriteGlyphTable_m491B3F4A5350C38D8B5166A60B7C43ED3608C0BA (void);
extern void SpriteAsset_set_spriteGlyphTable_m7402B7CF195E4B9F15DDA3273F49CA460A68DAD3 (void);
extern void SpriteAsset_Awake_m3A012935612A7EB924A77B85EDCF6C09257F60BE (void);
extern void SpriteAsset_UpdateLookupTables_mCC7A470A65A72908C9CDBDDFD17A056188A5C7CE (void);
extern void SpriteAsset_GetSpriteIndexFromHashcode_mE73615D1D9A8BB45C3426197EC54B1A002642DE0 (void);
extern void SpriteAsset_GetSpriteIndexFromUnicode_m321E02B6000E5F6673F5724155C3EF1DE3F5A66B (void);
extern void SpriteAsset_GetSpriteIndexFromName_mBCB684ED6E3DF5663A7FDA02CA69C99D9B17281B (void);
extern void SpriteAsset_SearchForSpriteByUnicode_mC50EFD6F2B78609891C357212CF65A8F18EB8A66 (void);
extern void SpriteAsset_SearchForSpriteByUnicodeInternal_mAA47B4DB58070A7A3F5F97C597098A65E896B5A5 (void);
extern void SpriteAsset_SearchForSpriteByUnicodeInternal_mEB122A514DF6A0D063EF8BE18F31F278ED9C3518 (void);
extern void SpriteAsset_SearchForSpriteByHashCode_m8B9CAB0028CC297570785693F2973918B31C9028 (void);
extern void SpriteAsset_SearchForSpriteByHashCodeInternal_mAD106CFA37AACBD783D0A74817D55507013BBC14 (void);
extern void SpriteAsset_SearchForSpriteByHashCodeInternal_mD38A7595ACBC7773C8292B0FD7E5A170A4105208 (void);
extern void SpriteAsset_SortGlyphTable_mA700CE5246D5798FA65779BE53179FFF4FFED6E5 (void);
extern void SpriteAsset_SortCharacterTable_m5447649977AF2C9F62A14415B44CDDD897A53AE1 (void);
extern void SpriteAsset_SortGlyphAndCharacterTables_m0E2B691E7C1F284E12A88B47B705307E83C7D927 (void);
extern void SpriteAsset__ctor_mE03F69799389DE8D90E69CD70054955033C4ED3C (void);
extern void U3CU3Ec__cctor_m84A11BA2AE10FB375A960D167ABF5F559DACAE3A (void);
extern void U3CU3Ec__ctor_mEFC122BF1D0D0CA8F0EAE9CE353C37A8CFABB5F3 (void);
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__37_0_mC479CF63F85C34FC407D92E67878B9B2AD99B739 (void);
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__38_0_m6A3F26D4286DF4F04DC23D23D04E12CA014D6E92 (void);
extern void SpriteCharacter_get_name_mD5A9CC908308BB48D459973C8844FE1FD7C925B1 (void);
extern void SpriteCharacter_set_name_mB45844384C3B06A780E82C2E946A29A799688AFB (void);
extern void SpriteCharacter__ctor_m0B3812DF9A667CA2C3AA321DF3403197EEBC83BA (void);
extern void SpriteCharacter__ctor_m2B9EC943D7F7E3A30608DCAFC9F676C23AD0921B (void);
extern void SpriteCharacter__ctor_mFF065D774A8B3ECD50323C0DEB241E7AED0EF043 (void);
extern void SpriteGlyph__ctor_mFFE2D9AA5A28EA0C20C10694569A1E23A664BB4D (void);
extern void SpriteGlyph__ctor_m0DD3EAB9307FE36B47116BD4E6A2289689C54422 (void);
extern void SpriteGlyph__ctor_m0935F217CCDAD97DD7542B0E6767C79E96B8AC40 (void);
extern void TextAsset_get_version_m2316C9F212FE4A13335032354A496CAEC86CB25B (void);
extern void TextAsset_set_version_m24718A4A6A86F4CE41D9ED1E0F796CCF367516CD (void);
extern void TextAsset_get_instanceID_m843A6CAA7FE9322CD19546671D3F0E90A0E27AFB (void);
extern void TextAsset_get_hashCode_m4D519E837097D8869A8D38EBD11611FADE411092 (void);
extern void TextAsset_set_hashCode_mC6ED3271A5EFC05562FD1083BE1C872CB69CFF74 (void);
extern void TextAsset_get_material_m4B9C02D34426436FDB01F1963A9FDC11D75604EF (void);
extern void TextAsset_set_material_mD9988165763E0E72C7FB7537760899EC1841C829 (void);
extern void TextAsset_get_materialHashCode_m9BAA469D5760D87EEC6E09B9ED3C5902B75017E6 (void);
extern void TextAsset_set_materialHashCode_m15EE7CCAF81DBAA326049F00788BCC918FDB2631 (void);
extern void TextAsset__ctor_m2C43EAC2D75854E6724A235794DEAF8A1AF9E786 (void);
extern void TextColorGradient__ctor_mAF03C3B68C29D94ED1F1517E65477CFC2B078FAC (void);
extern void TextColorGradient__ctor_m0712D0C09E7BCBC00909CEB71B43F2276ADB8B55 (void);
extern void TextColorGradient__ctor_m0A6B9264A8FBABEEC5BA84353D9B3DD7999DD2FF (void);
extern void TextColorGradient__cctor_mC5AE96BE4461EF9AE84CE4E3765B712D6A64693E (void);
extern void TextElement_get_elementType_m7BF97842479112227C1C3C83E0E94A176CD7D31A (void);
extern void TextElement_get_unicode_m40C69806537940F7BA1D3969713DA10CCBE57BC7 (void);
extern void TextElement_set_unicode_m99608D824B25E3529236C06BCC0983B5FC094F98 (void);
extern void TextElement_get_textAsset_m52383A3758AABF5BEA013155765BD1141479685A (void);
extern void TextElement_set_textAsset_m3F65429660C011F6F25B65D6BA7C4B2CF05659FA (void);
extern void TextElement_get_glyph_m101DBCCA0CDE2461B504174272A2FFCD53EA59E2 (void);
extern void TextElement_set_glyph_m6E8E2F1366089FA638680F1CF53F6F5027D022A5 (void);
extern void TextElement_get_glyphIndex_m43F82F2F998D640DEDBE6860EBE7B171DDF4FE56 (void);
extern void TextElement_set_glyphIndex_mFD72B93816998BE291DA6379EAF5E4153BC64F6C (void);
extern void TextElement_get_scale_mD16946900449FEE9E2F86B2C4C71E26F4491A0E6 (void);
extern void TextElement_set_scale_m83FC0850B2B0F31BDFC779954923F50FD06DC03F (void);
extern void TextElement__ctor_m8CB701D9A4C0444E834F178D97E9FC63E6D7E0B9 (void);
extern void TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985 (void);
extern void TextElementInfo_ToStringTest_m7E0775478741E60973A13C91273ABFB9105E0AD4 (void);
extern void TextEventManager_ON_PRE_RENDER_OBJECT_CHANGED_mC8130CBDB95276A1BC8676EB06261613D1EA2E47 (void);
extern void TextEventManager_ON_MATERIAL_PROPERTY_CHANGED_mFBE8778C69D37BDEF325DFE248D85EA31C0A4B46 (void);
extern void TextEventManager_ON_FONT_PROPERTY_CHANGED_mC5C27C15B5C5715EE7B079A45D09914AB12B479C (void);
extern void TextEventManager_ON_SPRITE_ASSET_PROPERTY_CHANGED_m7E3202ECDBE8A3EB91A0E05FAA3932C61E2EDC30 (void);
extern void TextEventManager_ON_TEXTMESHPRO_PROPERTY_CHANGED_m8688C9B57C3A5560EF0BE25A2F25B1ED26832CDD (void);
extern void TextEventManager_ON_DRAG_AND_DROP_MATERIAL_CHANGED_m0A71D08AEECAEFC7921ACDDA062EFB866BAF4DDA (void);
extern void TextEventManager_ON_TEXT_STYLE_PROPERTY_CHANGED_m0DAC279CA1A844D84E68BDA03E7546170F3A7691 (void);
extern void TextEventManager_ON_COLOR_GRADIENT_PROPERTY_CHANGED_m55CABCA07BA886979D1F2B1E84CCE8A16E32C1DD (void);
extern void TextEventManager_ON_TEXT_CHANGED_m55611A25AD0771E196A15E304F6949C210FD4530 (void);
extern void TextEventManager_ON_TMP_SETTINGS_CHANGED_mC6B1826E7A60F80A9984194B199C845737635F15 (void);
extern void TextEventManager_ON_RESOURCES_LOADED_mA6942BD5A7747575F92A392805B19D2A5EA01571 (void);
extern void TextEventManager_ON_TEXTMESHPRO_UGUI_PROPERTY_CHANGED_m5C91775BF77D87BA8FA0939A554E4A724D1829FC (void);
extern void TextEventManager__cctor_m616826AB5C10C1D16331F079CAF25B9440697C4F (void);
extern void TextGenerationSettings_Equals_mA5EDDF0453F2A7314AF5E1FE29F4138CD97E52D5 (void);
extern void TextGenerationSettings_Equals_mDCEEB056B70FC65ED6065E3BFE8D69D823DFEFD0 (void);
extern void TextGenerationSettings_GetHashCode_m1F750434FCE1853C36A579827B064619E85453E0 (void);
extern void TextGenerationSettings_op_Equality_mC417375DF36E0AD1A2A5BABC5DF2C0C6B250080E (void);
extern void TextGenerationSettings_op_Inequality_m01EC5AAB9B6A14F1E7CF3C05A738F509479B25C4 (void);
extern void TextGenerationSettings_ToString_mF8AB635EB369C7F66AC3B396934158CF3B6BA6CE (void);
extern void TextGenerationSettings__ctor_mA20608A16443434DAE9FEF0BF8BD076270FA660E (void);
extern void TextGenerator_GetTextGenerator_m5BDD6657637032A944115A1D6D52A6D511D43D46 (void);
extern void TextGenerator_GenerateText_m28C6EED85E5BB42AA40812F475D533AAF6694757 (void);
extern void TextGenerator_GetCursorPosition_mC48EEAF0A850F5827C43E6D2AA60C8AAD2D59627 (void);
extern void TextGenerator_GetCursorPosition_m9F767EE74114971780EF08619DEE0F5223FC5095 (void);
extern void TextGenerator_GetPreferredWidth_mF70516013AD339211A33B0A937362493FB460C50 (void);
extern void TextGenerator_GetPreferredHeight_mFD002673E41F471BF921C678F0B8D7B021738DE9 (void);
extern void TextGenerator_GetPreferredValues_m17A1C8F1AA7D260AB9167985429D6819D0E8D9CA (void);
extern void TextGenerator_get_vertexBufferAutoSizeReduction_mE156525BFE36A7F3764E93878414B7FD2463A0E5 (void);
extern void TextGenerator_set_vertexBufferAutoSizeReduction_m861779E70BC9413319C5D1CB13867D823178F3B1 (void);
extern void TextGenerator_get_isTextTruncated_m667879F08A9B2619D89520F1E747444B2B1EF5DD (void);
extern void TextGenerator_add_OnMissingCharacter_m8B5E5BBEF386116E4E93A434063368E39D9B46E5 (void);
extern void TextGenerator_remove_OnMissingCharacter_m02CBFEC05541ED46391C1CE0216DC1B763C32831 (void);
extern void TextGenerator_Prepare_mD0A24977334138340CA73FB9787627373C6AA255 (void);
extern void TextGenerator_GenerateTextMesh_mAB70FC29A49A6C4F8211EA977E37C66BE67D1831 (void);
extern void TextGenerator_SaveWordWrappingState_mC07B2C5977EECE10216F8C6AC9CC4204F7EF1936 (void);
extern void TextGenerator_RestoreWordWrappingState_mA63B3DD2C02E61CD8670A32A53163AF6BF765F61 (void);
extern void TextGenerator_ValidateHtmlTag_mF8187EB1D0CB901861EDFC36151409F8FF6AB287 (void);
extern void TextGenerator_SaveGlyphVertexInfo_m0CD6E1D45488FFC6675294AC64F40AC23C986A09 (void);
extern void TextGenerator_SaveSpriteVertexInfo_m4B47901F01927E7CC4E486A1C4354AFBF4D138A5 (void);
extern void TextGenerator_DrawUnderlineMesh_m307EA8034106ACD13F89CC7E78C5DE08CCCCEFAE (void);
extern void TextGenerator_DrawTextHighlight_m4046F4CC59C6DD8FE5B0BD97DB8BFE015B829389 (void);
extern void TextGenerator_ClearMesh_m68BA46B0365FC730BA5D2E6BDF2528BD370B2D83 (void);
extern void TextGenerator_SetArraySizes_m780796D50B2A5406E06F493503DA82BF5DA08A0C (void);
extern void TextGenerator_GetTextElement_mC46F0E788A0F6EB5A62601BCE4F383C3143C78CB (void);
extern void TextGenerator_ComputeMarginSize_m485F8B01196058B15F597DE99D6F6A47FA539D3F (void);
extern void TextGenerator_GetSpecialCharacters_mA82879FA537C58223BB660E797AC135A8E07B492 (void);
extern void TextGenerator_GetEllipsisSpecialCharacter_m5139CAE03CD2E25C9A528A6A6FC984A8515C2460 (void);
extern void TextGenerator_GetUnderlineSpecialCharacter_mE5E9D5DEB9A7758333CDDCAD05EF25F076EC1AD5 (void);
extern void TextGenerator_GetPreferredWidthInternal_m26376C8696D2FC64393FC216A0E4DCB43B0E763E (void);
extern void TextGenerator_GetPreferredHeightInternal_m9324ABDB103E57EDB792B86924A189718F8F1F43 (void);
extern void TextGenerator_GetPreferredValuesInternal_m125B070164DFEA503C67525D1F418DAF41300ABD (void);
extern void TextGenerator_CalculatePreferredValues_mB4E6FC8AEA08D8108D234D84DA2009A9D24CB5F2 (void);
extern void TextGenerator_PopulateTextBackingArray_m82B0E48D569AE2DC8F62A49EF8A5A1B1F60A80A9 (void);
extern void TextGenerator_PopulateTextBackingArray_m522293F1EFF9EC80DF2B45DE08861659B47080D2 (void);
extern void TextGenerator_PopulateTextBackingArray_m869636ADF1D64E4B74450AEADDCA985DD1AEB283 (void);
extern void TextGenerator_PopulateTextBackingArray_m8375955F43A5F557DBDB82A3647DCBFADD6093FC (void);
extern void TextGenerator_PopulateTextProcessingArray_mEC6B2EE86D363FF3F7CEE50C77A6124A0A27DA16 (void);
extern void TextGenerator_InsertNewLine_m00109EA00343212A7FD05D49E7DBF81DBFE4B5E4 (void);
extern void TextGenerator_DoMissingGlyphCallback_m643F3C7C677B4F98BFE251055ECE1E588BEFFB04 (void);
extern void TextGenerator_ClearMarkupTagAttributes_m6047C48E973FC0E5A524AEB3F78D20E958E747C0 (void);
extern void TextGenerator__ctor_m52E4D01DC28BDF753BF52F6501E7FD2FB2B30D90 (void);
extern void MissingCharacterEventCallback__ctor_m22C62F2B7DAAEC494F16008EEA0F192BE77E4AC4 (void);
extern void MissingCharacterEventCallback_Invoke_m5BF78AFFA87C08BC81EC893548949E960E0797D4 (void);
extern void MissingCharacterEventCallback_BeginInvoke_m7B51940C594D358DA6762E3AF63E8BCA7A30E4B3 (void);
extern void MissingCharacterEventCallback_EndInvoke_m6A314AF9B89AE38070DCA1609F9C0C587B62D942 (void);
extern void SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969 (void);
extern void MeshExtents__ctor_m09C3052557EE349D040C72B6572D52D48645F2D8 (void);
extern void MeshExtents_ToString_mC78F6BC3525EAD13F90B39A7E1C7B3301598E9B0 (void);
extern void TextBackingContainer_get_Text_mB9B62AE587962B01FF21BFB72996AE40F6D36EFD (void);
extern void TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7 (void);
extern void TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416 (void);
extern void TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97 (void);
extern void TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612 (void);
extern void TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D (void);
extern void TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7 (void);
extern void TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698 (void);
extern void CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29 (void);
extern void Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82 (void);
extern void Offset_set_left_mBA08D34537453D783769DC94796C8ACC560E5D28 (void);
extern void Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB (void);
extern void Offset_set_right_mA7D0F7DD84DE12F92B800449EA54C2DFC8AF5F2A (void);
extern void Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4 (void);
extern void Offset_set_top_m3B5760ADA3A3A5550CEF1B6BBE7CE3E95C6AED11 (void);
extern void Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C (void);
extern void Offset_set_bottom_mE54667D478772330F8C34F55509178EB3958D103 (void);
extern void Offset_get_horizontal_m1266C1DC1F818B19D4084D6E120A0CA38E18DC25 (void);
extern void Offset_set_horizontal_mBCE3E8C5FA4EF01EC9196A76450BBB4C6E7ED68E (void);
extern void Offset_get_vertical_mAC6BAE572DC64A052407108CF726DC0210D72DFF (void);
extern void Offset_set_vertical_mC01378B8286D0293946D2237205BD45256392055 (void);
extern void Offset_get_zero_mF5B6D7C3F437FA438844A0B3EF405D805F1D1958 (void);
extern void Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7 (void);
extern void Offset__ctor_m0F9E756FCB8AF792C3EDCC24673670E6ECA605DE (void);
extern void Offset_op_Equality_m122A34D50DB0E70BDEEC631D0082E9CFB8D19C8E (void);
extern void Offset_op_Inequality_m2E1761A654AEF757DF6B74A63CB3C123D14EFA80 (void);
extern void Offset_op_Multiply_mE5215371DD76A27676FF67C992C065BC456A8131 (void);
extern void Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038 (void);
extern void Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D (void);
extern void Offset_Equals_m3C88E9583BCC9A281BD1CFEAAE896F532F9EC0C7 (void);
extern void Offset__cctor_mB8571222B76084876413C594C17AC5A343B40732 (void);
extern void HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F (void);
extern void HighlightState_op_Equality_m6E4A396D3C2C5932BCCE96E7B3AE42E37E447923 (void);
extern void HighlightState_op_Inequality_m2DFBCB59E593F72191BFBBD7424A8C6151E68272 (void);
extern void HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA (void);
extern void HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849 (void);
extern void HighlightState_Equals_m8F74F12721E91EA8928A098BC6815E253478D8BE (void);
extern void TextGeneratorUtilities_Approximately_m696ABB909732F536F1FF83EA8CE34CF53266794D (void);
extern void TextGeneratorUtilities_HexCharsToColor_m2C739FBEC67C612B593FDF344E5875F0C0D8AC31 (void);
extern void TextGeneratorUtilities_HexCharsToColor_m4D7AB2E490DA31C514A42A880AB35CE9CBAF77B4 (void);
extern void TextGeneratorUtilities_HexToInt_m41648DAEE872433A0AFA82018A9539ECC5C0FFC6 (void);
extern void TextGeneratorUtilities_ConvertToFloat_m93B85749154AAFB614CD246876786B75C98AE11C (void);
extern void TextGeneratorUtilities_ConvertToFloat_m37E1CF11E22ED6528B166907211426EEE6D0A4F8 (void);
extern void TextGeneratorUtilities_PackUV_mE110A97960725C40D87FA903B63E0100AFCB06F5 (void);
extern void TextGeneratorUtilities_IsTagName_m67178B96C0B3119FFEA83A8ACED7D7DAD0414208 (void);
extern void TextGeneratorUtilities_IsTagName_mA8FA492104AD554B93FD6FDAC0D67416ED746A9D (void);
extern void TextGeneratorUtilities_InsertOpeningTextStyle_mF71E0B0C1B5E938C5AAC7F8FB3CD5278DEEC2408 (void);
extern void TextGeneratorUtilities_InsertClosingTextStyle_m08B150E030816A5084205B49DA40DED97E0C7036 (void);
extern void TextGeneratorUtilities_ReplaceOpeningStyleTag_m67FC3FFDE1912D2E7C2DC2BED4C5BA250B1DB705 (void);
extern void TextGeneratorUtilities_ReplaceOpeningStyleTag_m947084F63F6548A105DD1D244685A2717820FA70 (void);
extern void TextGeneratorUtilities_ReplaceOpeningStyleTag_m6F65A6D9D8DEA4915B30DEBFF43850B9D7063252 (void);
extern void TextGeneratorUtilities_ReplaceClosingStyleTag_m9DD77D4EACF2389DF2631F515A23C11DC5E58A3B (void);
extern void TextGeneratorUtilities_InsertOpeningStyleTag_m94153F78A4B8F7A1811D2C1E9567996E39616F60 (void);
extern void TextGeneratorUtilities_InsertClosingStyleTag_mD6A4B3357D6478C5770AEE460F61917584B905DB (void);
extern void TextGeneratorUtilities_InsertTextStyleInTextProcessingArray_m7CC6FF13CD9B2F3BD04C60C2A2B527960C6D1D09 (void);
extern void TextGeneratorUtilities_GetStyle_m236E7C5EBFA951D1D4B2C3C7803BBEA5C2FD1812 (void);
extern void TextGeneratorUtilities_GetStyleHashCode_mA4CDB93771348C1236A8E9BE1EB4A9D5C057D516 (void);
extern void TextGeneratorUtilities_GetStyleHashCode_m7B5002A635CF32D023E543FDE814E0E958A89EF1 (void);
extern void TextGeneratorUtilities_GetUTF16_m5B397339CD29B370A27D3BA3B8BEFC12E8C56434 (void);
extern void TextGeneratorUtilities_GetUTF16_m4E03C41F3B5323D6234DEC0A312F13CEAACCA8E6 (void);
extern void TextGeneratorUtilities_GetUTF32_m334BA95AED813976AC79E68EB677BADB5CB15CE3 (void);
extern void TextGeneratorUtilities_GetUTF32_mAF367B8C1D5B586B49AED2B69E5E7ECEF3378D0D (void);
extern void TextGeneratorUtilities_GetTagHashCode_m89243960CD468B3DA8EC0AC29829FD8125056D9F (void);
extern void TextGeneratorUtilities_GetTagHashCode_m52AED809412409E8D9D9AD9D4DDDF0990A0049CE (void);
extern void TextGeneratorUtilities_FillCharacterVertexBuffers_mE0CCB8DA0D27F37DCFC4E47E89697D8823A8FCE8 (void);
extern void TextGeneratorUtilities_FillSpriteVertexBuffers_mD1AECFE4D4356A6925BF056E15CF84118313412B (void);
extern void TextGeneratorUtilities_AdjustLineOffset_m811C187EA3E41781116F0C7A679B05BB27874123 (void);
extern void TextGeneratorUtilities_ResizeLineExtents_m2EA9BE32A38D5E075DEF8EDA9EC01766E45C0F85 (void);
extern void TextGeneratorUtilities_LegacyStyleToNewStyle_m8E9C09A7BE7B27A4EC73ADD747BFD9A4DE7E656C (void);
extern void TextGeneratorUtilities_LegacyAlignmentToNewAlignment_mA16F5CB58D3C8F352CAACB8DACA5AFED59D3ED63 (void);
extern void TextGeneratorUtilities_ConvertToUTF32_m6295E74C04568A52624812F2E615A7F25F235C70 (void);
extern void TextGeneratorUtilities_GetMarkupTagHashCode_mFFDE1B0B5CD9774F83C988C5D436D1AD01AAD843 (void);
extern void TextGeneratorUtilities_GetMarkupTagHashCode_m951A939A8B3B0BE3229CB1A94E79FF123C8EF6DE (void);
extern void TextGeneratorUtilities_ToUpperASCIIFast_m359D6A8BE78E2C74BA677D8453799487962EDE99 (void);
extern void TextGeneratorUtilities_ToUpperASCIIFast_mEEED07AD0989B1DF84D559CDE3A397A9F2EA913C (void);
extern void TextGeneratorUtilities_ToUpperFast_mE1809281C56E4137C6794B2E94D38BBFA68DBAAE (void);
extern void TextGeneratorUtilities_GetAttributeParameters_m261C1E8FB533D3570153B2BAF0D671C5DF4B58DB (void);
extern void TextGeneratorUtilities_IsBitmapRendering_m93C5008776EEDD84825ED2133CDA0FC66DD56EEA (void);
extern void TextGeneratorUtilities_IsBaseGlyph_mEE0E7D6C3FB32204C2299FBA2B9F7C51E06F80FE (void);
extern void TextGeneratorUtilities_MinAlpha_mB52BE8C9C82C15B23D29BF606465B16DD4B1F7E5 (void);
extern void TextGeneratorUtilities_GammaToLinear_m37B603C94918DB93477EFF98E8A77FD4D8B0C8FB (void);
extern void TextGeneratorUtilities_GammaToLinear_m5D4B51EF525F9238F6644BD47106DACCB78797D7 (void);
extern void TextGeneratorUtilities_IsValidUTF16_m944B75A058B351075C02F1DA61B688FAF1186DE8 (void);
extern void TextGeneratorUtilities_IsValidUTF32_mD6B22F5E6EAD47537B906859CB093622EECF716D (void);
extern void TextGeneratorUtilities_IsEmoji_m84855B4FDA2F5CE4FE0A7231AD6EEF30DB941CFA (void);
extern void TextGeneratorUtilities_IsHangul_m5A23BA8E0EBE57243E2E96A248B3F6570A87A966 (void);
extern void TextGeneratorUtilities_IsCJK_m2F2718B1203271CC2C501C5054590299FBCA5B7D (void);
extern void TextGeneratorUtilities__cctor_m01201F8A0A4161B232AB9D179AB74877D4E17D85 (void);
extern void TextHandle__ctor_m0E8BD79BF9F66EED2A87EA09B246B712DDEED9C9 (void);
extern void TextHandle_get_textInfo_mA9F2BFB37F7ADA773731AFBC3B53FDD858D87FEE (void);
extern void TextHandle_IsTextInfoAllocated_mB9899602ADC3234E901B2E1C8CDC39C8958EE566 (void);
extern void TextHandle_get_layoutTextInfo_m7521A9C93B844DE793D029CD1E084ED68D824773 (void);
extern void TextHandle_SetDirty_m485BF8AC302B5A6DC3F63BC8DE3A0D823C2B7F2D (void);
extern void TextHandle_IsDirty_m4B25A5E4CD9A7EA8C5A0D35859AA5CA0D611BE01 (void);
extern void TextHandle_GetCursorPositionFromStringIndexUsingCharacterHeight_m082A44C87BA6376E99A5BD37090429F4A3CF0E0E (void);
extern void TextHandle_GetCursorPositionFromStringIndexUsingLineHeight_m44EE4238CC96507DCEB45A28D61E969C877011FE (void);
extern void TextHandle_GetCursorIndexFromPosition_m68B7D720ED1589CC46538FACB50E7F7E56AA701E (void);
extern void TextHandle_LineDownCharacterPosition_mDD7F4379B59B9CAF7431CCC3E4056CF3511ECF9F (void);
extern void TextHandle_LineUpCharacterPosition_m69F1091BCD4A92E343B60966F4C49A9E36106C5B (void);
extern void TextHandle_FindWordIndex_m03527F692DC3C652689A9AB4043AC33B0F48DBBF (void);
extern void TextHandle_FindNearestLine_mD86CC87E6804492C908C12804379B6323A825DDB (void);
extern void TextHandle_FindNearestCharacterOnLine_m68EA4EAD515DE9F7F819ED0AC99908B1DF8EC3AA (void);
extern void TextHandle_FindIntersectingLink_m9D72AF4B459885AEFB03A0FF212241F8532B9132 (void);
extern void TextHandle_PointIntersectRectangle_m55DAEE8660392F7FA5B9A65273D81EA8CEC0694C (void);
extern void TextHandle_DistanceToLine_mC14FF738C5BF2E28AD4E1A3C36E9E7F67C95EAE5 (void);
extern void TextHandle_GetLineNumber_mED5D753BFDB5DDB5145EEDC829EA8D4EF1D305B1 (void);
extern void TextHandle_GetLineHeight_mAC48AA68AFCC8EDE5C52EF69941ADAD3B144539E (void);
extern void TextHandle_GetLineHeightFromCharacterIndex_mA935CA07C41CEA0C7447033337D05CB2652A1D62 (void);
extern void TextHandle_GetCharacterHeightFromIndex_mA512F4B21032917955542D5E71D611A55E6F1F0D (void);
extern void TextHandle_IsElided_mF2AB6B8A1E01EE5FD2301E9BF77BEE5BC99C4ED5 (void);
extern void TextHandle_Substring_m3B3C2EC9E167E4C74AB319DE140780560976EDEC (void);
extern void TextHandle_IndexOf_mD0CDAB3319422D67356DBC547E91A08882D001B2 (void);
extern void TextHandle_LastIndexOf_m7A2F2860D56B0C90A73958E02006CAA1D6BEACC4 (void);
extern void TextHandle_ComputeTextWidth_m0A131F6D0F30382D51B44B8E41158612AD13B602 (void);
extern void TextHandle_ComputeTextHeight_m7E5F0EB7DD5630BCF519AFE6B194CFF37E421961 (void);
extern void TextHandle_UpdatePreferredValues_m16C579932E755BC3FD8D82085F75EC011A44AD59 (void);
extern void TextHandle_Update_mF77E9498589B5348C2EE27EFF866505974E5DA89 (void);
extern void TextHandle_Update_m9A43DC731089132219B99648DD2904ADCA37C477 (void);
extern void TextHandle__cctor_m3EFFF534A8E9459492960B615C91F18081422439 (void);
extern void TextInfo__ctor_m241E24715CC5F6293DC90A4D25884548BAD0D602 (void);
extern void TextInfo_Clear_m60412774208F9D920707448E71E89C99233D9128 (void);
extern void TextInfo_ClearMeshInfo_mCA598F01C7F302CFCD0F508E2DBF072E66CA74F3 (void);
extern void TextInfo_ClearLineInfo_m986C886D34A324C8C4D30F9D8EF24AC242A10AD7 (void);
extern void TextInfo_ClearPageInfo_m57DE207346C5245799E50F8A57B56B65665B7430 (void);
extern void TextInfo__cctor_m0B33CCC02D4B32FB18864B6B7657695E9AEB3F0D (void);
extern void CharacterElement_get_Unicode_m6D73AADD73043A87CF7985E0980D532AC2DA2505 (void);
extern void CharacterElement_set_Unicode_mC526431F252FF7D6DAFB5C75CBA021DF59FF2059 (void);
extern void CharacterElement__ctor_mD64E289D3694F7029402D78A091B92D47E6437E6 (void);
extern void MarkupAttribute_get_NameHashCode_mA61571031D77679B70AA0C04CFC7FC0E3511EC16 (void);
extern void MarkupAttribute_set_NameHashCode_mD4BCF8C3E1B9F77D09DCD9A688DE73BC6204F75F (void);
extern void MarkupAttribute_get_ValueHashCode_mAA11DFA63F114CEC146E1260A0C0BB05E4EF277C (void);
extern void MarkupAttribute_set_ValueHashCode_mDBB10FE8FD648CB31058DDEA359FA743C0ED9D40 (void);
extern void MarkupAttribute_get_ValueStartIndex_m843094D485ACB571F8C9311403A6E5B7C0C47BA6 (void);
extern void MarkupAttribute_set_ValueStartIndex_mD1C3F783C156E93023CF6F31C82F3D1FB56F1E26 (void);
extern void MarkupAttribute_get_ValueLength_m571F75D978A943093767BCCE5525C1C6B492E34B (void);
extern void MarkupAttribute_set_ValueLength_m70EC65BA4ECB6E003C1C7DB8D44B3B2C02BDAB74 (void);
extern void MarkupElement_get_NameHashCode_mB0386E3C4639B814EB50FD3965A7CCAF0BE12A31 (void);
extern void MarkupElement_set_NameHashCode_m7773B98592AD4BE2C798422E9B71E833D1BC9E34 (void);
extern void MarkupElement_get_ValueHashCode_m0F5E80360F28F8B4935DFD78627BF10B575D8D17 (void);
extern void MarkupElement_set_ValueHashCode_m9F7B5802A7058EC98E5DE428ED47F79D8F7400D3 (void);
extern void MarkupElement_get_ValueStartIndex_mA19AD621FBBF86504B7EF3EC667A6C72C5CCE34A (void);
extern void MarkupElement_set_ValueStartIndex_m2DACFB0BEDB889BF38E60EC98FE35D352FE1C95A (void);
extern void MarkupElement_get_ValueLength_mF07DBC72C14506D62FBCA38138BB6ECFF9CA2D80 (void);
extern void MarkupElement_set_ValueLength_mC1B415F03848B3659951AB897F130D4CC9F27706 (void);
extern void MarkupElement_get_Attributes_mAB26F51783D56866912A7A0C4F542663AF7E2578 (void);
extern void MarkupElement_set_Attributes_m91F406840212058BD264E4E263AD096C06A6180D (void);
extern void MarkupElement__ctor_mF044727F3A3C69EC17B9A4071D0D91AB5567C4E3 (void);
extern void FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0 (void);
extern void FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB (void);
extern void FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB (void);
extern void TextResourceManager_AddFontAsset_mAAD97609F00F02146B283982E5DE667EB7A692EA (void);
extern void TextResourceManager_RemoveFontAsset_mF3A111A8A89A93A37DEF1EF21408415424D09D5D (void);
extern void TextResourceManager_TryGetFontAssetByName_m8FB59092D27AC9EFFA157902191C2A373673F2AF (void);
extern void TextResourceManager_TryGetFontAssetByFamilyName_mE3AFAC44F4A7EA104970E87A8FAB47D644B5B573 (void);
extern void TextResourceManager_RebuildFontAssetCache_m47EC02B2357F83B86F4C151158E031E801A44845 (void);
extern void TextResourceManager__ctor_m4F2221570A4BBE3A5CDB819F80ADB99B904F63E3 (void);
extern void TextResourceManager__cctor_m54E40FCFB355A0C90B56366B057F307F75832868 (void);
extern void FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5 (void);
extern void TextSettings_get_version_mB5B7C3F04FFC8DCDFCFF63CFFF7612B768E267CB (void);
extern void TextSettings_set_version_m7BEE644EEDD77892C295170F9B2B68BC45C7C6CF (void);
extern void TextSettings_get_defaultFontAsset_mC6280464BFEE081DB23243BB94E49C72A0885A1F (void);
extern void TextSettings_set_defaultFontAsset_mC0137D9AEB9EE7A9E1B3D7C499414EDD217AF3BA (void);
extern void TextSettings_get_defaultFontAssetPath_mF7B1713753CFAE048C745C3572332CE18CD51D3B (void);
extern void TextSettings_set_defaultFontAssetPath_m35111382F9C2E3C76FA6B77BABEE8149BDDCC7ED (void);
extern void TextSettings_get_fallbackFontAssets_m332526E834C994425141A58C968FD40320573F98 (void);
extern void TextSettings_set_fallbackFontAssets_mEBEC769B2FF339BDB44D72CB48F0010577889772 (void);
extern void TextSettings_get_matchMaterialPreset_m4675979547AE4C83E680260EAE5ACBC4FAC53B87 (void);
extern void TextSettings_set_matchMaterialPreset_mD5A3DDE2D93FE2872C869A2AE0B0C984CF5BC12C (void);
extern void TextSettings_get_missingCharacterUnicode_mA707E5E6633633BBB3BAFB96B97A5A995100F3F3 (void);
extern void TextSettings_set_missingCharacterUnicode_mE3E3E2F902BE89B9FE78F03D3B8C508583D1CDA3 (void);
extern void TextSettings_get_clearDynamicDataOnBuild_m0C130F53F106D77250F38239FB73DCB97299FB79 (void);
extern void TextSettings_set_clearDynamicDataOnBuild_m86B73E68953D42C6AE4210BEA5D678768155A1AD (void);
extern void TextSettings_get_defaultSpriteAsset_m8FA900F9747B7ADBCD2A2F612E7D977DB58D6445 (void);
extern void TextSettings_set_defaultSpriteAsset_m6083C46D0B3AFB6816363CF2564D2DB2D1ECFEB9 (void);
extern void TextSettings_get_defaultSpriteAssetPath_mF57875222B7FACC9B69369305EEEB53FFE7986E0 (void);
extern void TextSettings_set_defaultSpriteAssetPath_m9C49C00A96230DAF828E629C106B229A4552F753 (void);
extern void TextSettings_get_fallbackSpriteAssets_mC6D74088EBDFDFBACD581FE4AB4EAA447ED242AD (void);
extern void TextSettings_set_fallbackSpriteAssets_mB28744780B58AE04BA7F1275508FF7DEE4E485E5 (void);
extern void TextSettings_get_missingSpriteCharacterUnicode_m496D85B0D88C91F2A5CC9929C233EAA84831523E (void);
extern void TextSettings_set_missingSpriteCharacterUnicode_mFE1AFD04C4AA793559E0A157C9E5472E3C3F6701 (void);
extern void TextSettings_get_defaultStyleSheet_mDA420960556C00405FA66CBD2DA36807F8F4B4F2 (void);
extern void TextSettings_set_defaultStyleSheet_m2867A0B047E623E4485F10EE717FF0B46BAAECEE (void);
extern void TextSettings_get_styleSheetsResourcePath_mE65A055D1C99CA2048BE6F246258EF262E43C4E6 (void);
extern void TextSettings_set_styleSheetsResourcePath_m19A5C893047D78B43AC5EB43FB9EA79792464C05 (void);
extern void TextSettings_get_defaultColorGradientPresetsPath_m1571454580E1F0DC859D3F201BB1F6355A5DBB8E (void);
extern void TextSettings_set_defaultColorGradientPresetsPath_m951D5A7AE71C87BFA5B9B8CE8EA2F78A474FE01D (void);
extern void TextSettings_get_lineBreakingRules_m96E2C32D4F08309D904B0BCD83CEBE8CD6716A04 (void);
extern void TextSettings_set_lineBreakingRules_mB1174FE0F923443130A3D4686C119EDB0430992D (void);
extern void TextSettings_get_useModernHangulLineBreakingRules_mC5DCBAD1DD897594D020EA3B52D1AE2189251E79 (void);
extern void TextSettings_set_useModernHangulLineBreakingRules_m52082AD9E6D7C4C8C50821282A2B94BEDCAC3FA1 (void);
extern void TextSettings_get_displayWarnings_m3CA9FCB44B30CC06F54CD3716D68285FF844DF83 (void);
extern void TextSettings_set_displayWarnings_m08373EE3900E911B4021CBF8AF915DA69856CC20 (void);
extern void TextSettings_OnEnable_mBFC6BA8BA147B68E9FB956B2D496A2E8C2972A13 (void);
extern void TextSettings_InitializeFontReferenceLookup_m34B5FB3D61296A7620196398D267812EEFCE0B04 (void);
extern void TextSettings_GetCachedFontAssetInternal_m9308E0DDEF8F6D8ACA17DAADB43745C860788202 (void);
extern void TextSettings__ctor_m860D28B10258792A195E1C6391479E16D04CA8BF (void);
extern void FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5 (void);
extern void TextShaderUtilities_get_ShaderRef_MobileSDF_m9014F695545914ADF74FFD8A461B1DD2590A163B (void);
extern void TextShaderUtilities_get_ShaderRef_MobileBitmap_m54CAA31B3B374C0517BF356C80B34F169C89F649 (void);
extern void TextShaderUtilities_get_ShaderRef_Sprite_mEB8685333A53464F71519FA69438381EABEFAA02 (void);
extern void TextShaderUtilities__cctor_m5E3AAC923C6500CFCB7F775610B085AD43CDE4EE (void);
extern void TextShaderUtilities_GetShaderPropertyIDs_mBA841B8CACE147593703FF6E7E2804FF2017DE52 (void);
extern void TextShaderUtilities_UpdateShaderRatios_m5DFBADF0AABA261FB14F1431E82340B13D38F110 (void);
extern void TextShaderUtilities_GetFontExtent_mEDE4E145CA01D28FCDF6AAF2A4DF9E2750B5BE29 (void);
extern void TextShaderUtilities_IsMaskingEnabled_m5613AC55B82463CB01B7983743EF5A4518490331 (void);
extern void TextShaderUtilities_GetPadding_mB8AB51D48DC021C3446D30408B4515B16E3BFA5F (void);
extern void TextShaderUtilities_ComputePaddingForProperties_m96574D625A310B3B6F584D5AD6DA1A517D544420 (void);
extern void TextShaderUtilities_GetPadding_m0712CFC6137761D140C858D54739E53E3BBF871E (void);
extern void TextStyle_get_NormalStyle_mA0E83C0E845ACFA40171BD84744D5BF77110A196 (void);
extern void TextStyle_get_name_mF4794188414F2041B408E23ACF664A75A35FFE41 (void);
extern void TextStyle_set_name_m71685FED89C86374B7E028A8818E8954E7F52F53 (void);
extern void TextStyle_get_hashCode_mA1F4D3630B6AE71C2A31F94B7054C28BDD96084F (void);
extern void TextStyle_set_hashCode_m53BBE925958A34BCF4B31820AEAE907942C34A23 (void);
extern void TextStyle_get_styleOpeningDefinition_m4494A97ED19F7108DEBA9E2DF778FCD438DF1B19 (void);
extern void TextStyle_get_styleClosingDefinition_m94E7D7B2730DF74ADF9CDB9ECA7DE9CD08B1B7B1 (void);
extern void TextStyle_get_styleOpeningTagArray_m123040451C694F92BC9700969B4682EC4BACF8BE (void);
extern void TextStyle_get_styleClosingTagArray_m0B50B87D1CCDC30647772E268433096209D7BC42 (void);
extern void TextStyle__ctor_mF1C354C192665DC3942DBDC0B7EECDBD653FF684 (void);
extern void TextStyle_RefreshStyle_m2D0771408F06C24EF303749ED8E656C800575BDB (void);
extern void TextStyleSheet_get_styles_m7916C62D70AA430314D85EA5B5A778FFAE1544DB (void);
extern void TextStyleSheet_Reset_m4C7EA0DF62767E14E3407398D533F1499647038B (void);
extern void TextStyleSheet_GetStyle_m648B766D750E1B37DD126918BF7EB22DDFD21D29 (void);
extern void TextStyleSheet_GetStyle_mF87D8108EEF60C3FC32A2B01D5C1C23D3F22A4D1 (void);
extern void TextStyleSheet_RefreshStyles_m92525DE6F7951D03D906E18EC89349AA2794AEC5 (void);
extern void TextStyleSheet_LoadStyleDictionaryInternal_m482E4CDF0CD2ED291F85869E141EEB5EC21F6D81 (void);
extern void TextStyleSheet__ctor_m1A0B93B55B12CE15FFF1053443179BD010022B75 (void);
extern void TextUtilities_NextPowerOfTwo_mFB485658568FD671B181760B887496344FF747BC (void);
extern void TextUtilities_ToLowerFast_m3B09EDC3C43F0CB2EEA93FE2CD138C8543CC64FA (void);
extern void TextUtilities_ToUpperFast_m7E98D5D17030A387012E262648346523DCC3AA0B (void);
extern void TextUtilities_ToUpperASCIIFast_m48A8B61739F9D5E8CB46B108746277DAF38AB58C (void);
extern void TextUtilities_ToLowerASCIIFast_m0E9D3564CC36033C23F08F4A536B353AC2ADFE2C (void);
extern void TextUtilities_GetHashCodeCaseSensitive_mDCEA10A7A94A35EBF812FBA784332AA1950D560E (void);
extern void TextUtilities_GetHashCodeCaseInSensitive_m3241BDD3A6F82A061C21BF5D347B690D732F5B59 (void);
extern void TextUtilities_GetSimpleHashCodeLowercase_m51788E1833BA78F7EA46551F66D3CD8B01C6D482 (void);
extern void TextUtilities_ConvertToUTF32_m2392833CB12C6F2A3B45FC1DE354752B3C3455A8 (void);
extern void TextUtilities_ReadUTF16_m754B7E1D790693FE31700FB47F397817FCA5FF85 (void);
extern void TextUtilities_ReadUTF32_m9B4B4ACCA0347158BD4C0A6E6F232CF8A9AA848A (void);
extern void TextUtilities_HexToInt_m8922BAF0B84C39996473D4BCBADFE6D13490AF66 (void);
extern void TextUtilities_StringHexToInt_mF786078042805D5DF5F0A52A27021064EE56D063 (void);
extern void TextUtilities_UintToString_m7C0ECB6D2370EC4275FE0E70FB979CADA55A6216 (void);
extern void UnicodeLineBreakingRules_get_lineBreakingRules_m87E179304E3E068A4A113C2B574CB2FA6828E9DA (void);
extern void UnicodeLineBreakingRules_get_leadingCharacters_m13BA1ABC3DF5F777755065DA2047DD6DCBEFA3C9 (void);
extern void UnicodeLineBreakingRules_get_followingCharacters_m6D71A30A686E94B6D2CA0599D6B85B8B8FB0602A (void);
extern void UnicodeLineBreakingRules_get_leadingCharactersLookup_m1DAC015D7E37112EAE0437E6472AEA0719DFF3DC (void);
extern void UnicodeLineBreakingRules_set_leadingCharactersLookup_mDCBE493A3C78E6FCB666200EC577DF0EFD20D8E0 (void);
extern void UnicodeLineBreakingRules_get_followingCharactersLookup_m5510A21873DC5DA66F4A2DFA4C26A5EFAD494D8B (void);
extern void UnicodeLineBreakingRules_set_followingCharactersLookup_m126B4EF60B558C6A30C3E08C00D4BF3BC41EB88E (void);
extern void UnicodeLineBreakingRules_get_useModernHangulLineBreakingRules_mD86D283CE7BA23A0174B9227A7BD915D3D9FD464 (void);
extern void UnicodeLineBreakingRules_set_useModernHangulLineBreakingRules_m53CB9AC0EAFD25ABB98B907EC8F908119E9FAC5D (void);
extern void UnicodeLineBreakingRules_LoadLineBreakingRules_m4686111E39B00E27AA6AD88762793EEFCCC14A75 (void);
extern void UnicodeLineBreakingRules_LoadLineBreakingRules_m8B5320C512BB7919AF4AA01650690F64875A7485 (void);
extern void UnicodeLineBreakingRules_GetCharacters_m93663235F54D32D801E67994714B79759D2F4137 (void);
extern void UnicodeLineBreakingRules__ctor_mD94779B4996B56EA84C847EC4DD287AB1A8ADE85 (void);
static Il2CppMethodPointer s_methodPointers[630] = 
{
	Character__ctor_m5DCCE862D40487C733C29A233DB8513A9A6A02F6,
	Character__ctor_mF787F05C8C34182A25009FEB50076E355D47EBD6,
	Character__ctor_mEEAC42D4227E0053C8008C12B222CC208D781795,
	Character__ctor_m21FBFAF1F6324565246096EFFB81C3F9E15D43CC,
	ColorUtilities_CompareColors_m0F0F140129DEE889FB8AE3B2921C495E94B5E875,
	ColorUtilities_CompareColorsRgb_m8A3DBC16E8330FE4012F33F61BF54CCEDA31C55C,
	ColorUtilities_CompareColors_m76427940D8B2E949E9BA4E483D3FEBB96682E4C3,
	ColorUtilities_CompareColorsRgb_m5059425DB7F9D3F1CEDAB697B6A684CD05D691D0,
	ColorUtilities_MultiplyColors_m81D3E41A86E195794977F31C5A944DB40FE734B4,
	FastAction_Add_m86840746EF1B743CCEB14B3C810BB4360D58F7B0,
	FastAction_Remove_mD486A8B1FB253F9F6C6EE8E6B9A88A9A6B34AEC0,
	FastAction_Call_mD5ADC8889A6382B473762773A188EAC234EBA6AE,
	FastAction__ctor_m837FFCD82DA457A7BFCC2EA03FBD3E358DA1F3EE,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FontAssetCreationEditorSettings__ctor_m580CDC10BB7CC670BAB67945F88C20397EC29D2B,
	FontAsset_get_fontAssetCreationEditorSettings_m024033F91B976A8EAA5CBE67D3DB2A756B91CF01,
	FontAsset_set_fontAssetCreationEditorSettings_mF7EE6A46807D78A7E99872171D2AD774DE20C7EB,
	FontAsset_get_sourceFontFile_m6B0E805BD1B7712F0B5135D157E96F3F40314830,
	FontAsset_set_sourceFontFile_m2E6D2AED5E5D2585A09E9BF830387DEB10A2F4E8,
	FontAsset_get_atlasPopulationMode_m5364C5A9E84969D8E4FF8436BD18A3F10BF90366,
	FontAsset_set_atlasPopulationMode_m1A9DD5C702ED0924B9C208F4CE5ADEACF9188268,
	FontAsset_get_faceInfo_mF020EC579E3C18A6279D55D86AF1C585031B49A9,
	FontAsset_set_faceInfo_mCCA87B67C4CA2C0A1F6D85FB1FAA09667996EDCD,
	FontAsset_get_familyNameHashCode_mF2DB211A5712A291B2D28FCDB7F7C29057770330,
	FontAsset_set_familyNameHashCode_mE1495199BCE7B771CC920E2DBB86A8AF1518CB55,
	FontAsset_get_styleNameHashCode_m3CD3D77F64DAEB31D8F69E4D7CC1AD0AC784ABF5,
	FontAsset_set_styleNameHashCode_mE1BE5B75DE1E9EA0F76569609E6C4FFDC57558BA,
	FontAsset_get_glyphTable_m212E940F74AEE62ACBB3374486296CA518D934B5,
	FontAsset_set_glyphTable_m2753BC6CEE011983C2B4B181867C3EB00CDE87D4,
	FontAsset_get_glyphLookupTable_mD04A90D8262F1963EDC472272B67BBFAF73DEEA5,
	FontAsset_get_characterTable_mC77FAE1355269834F7C8A2D46AFB4BFE7B7AD72D,
	FontAsset_set_characterTable_m44703292F669F2F6D4920EB9427077E24FB1512C,
	FontAsset_get_characterLookupTable_m7E76D6C706C5CEB04A9541C68AE6D9E5C75F0FFC,
	FontAsset_get_atlasTexture_mC49216F40093C7AC4FA5DA68F9F5C9FCC83B8F27,
	FontAsset_get_atlasTextures_mADD7A506F0444A1EE4F1D52536B0C5DA9BE35075,
	FontAsset_set_atlasTextures_m5315BA4903B77742EFA1E54CEA2AF12726B10A99,
	FontAsset_get_atlasTextureCount_mC20300C53E52D7A8351DE296BAD565268568119F,
	FontAsset_get_isMultiAtlasTexturesEnabled_mF222228A76102BB0EA593A60439D22F912761F1E,
	FontAsset_set_isMultiAtlasTexturesEnabled_mA470AF6D312989E752C68DC0FD5700235877566B,
	FontAsset_get_clearDynamicDataOnBuild_mC1F714E56F087B29E0A3B43D820EDCEB78E9EE75,
	FontAsset_set_clearDynamicDataOnBuild_mA1C6F298742DF78EC0F81157F0E04246F8B82F7E,
	FontAsset_get_atlasWidth_mE711550FDD4B5F988B77AB5D332A80A34B5CF364,
	FontAsset_set_atlasWidth_mFFB9D37EB1C648384ED1426B42E26A4104D329B1,
	FontAsset_get_atlasHeight_m306FBF7D35C39123A4770E147FAF95B1B8DE8086,
	FontAsset_set_atlasHeight_m7116DFD32F38971CE39D7F4BF84CB5217DCAA2B5,
	FontAsset_get_atlasPadding_m251A35FB5F499EE66CC2E2150CBEDB2C8C5D5581,
	FontAsset_set_atlasPadding_mB34AA836A3D02722ABED71B3583D767560CA956D,
	FontAsset_get_atlasRenderMode_m036D4BA220E5D4B0C407CA6BC1B09D8914B5058A,
	FontAsset_set_atlasRenderMode_m993764193CE75D57DC4CC755336596681A7866D2,
	FontAsset_get_usedGlyphRects_mE039AEF3AE45A15A86B2C0F774E6ED58AFA2F341,
	FontAsset_set_usedGlyphRects_mBF80C1063C0A274AD95F55C43DA734E126F6643F,
	FontAsset_get_freeGlyphRects_mCDAEF0519586C5248BBEDEAA85086CC117903E88,
	FontAsset_set_freeGlyphRects_mED3C0E01ABFC63CE700C701476BA2B66D112AA9B,
	FontAsset_get_fontFeatureTable_m7C4EB9A655B237CE02FAF7B8B16C2F2863FE5070,
	FontAsset_set_fontFeatureTable_m3FD11B99122416777808E1CE5414D7BA40920C3B,
	FontAsset_get_fallbackFontAssetTable_m43303BFBE8A8C55D8CE8A67C47EFAFF5A712CB69,
	FontAsset_set_fallbackFontAssetTable_mE22C3D2323111FD6A5DF9847FB812BD41E18832E,
	FontAsset_get_fontWeightTable_m8DADE0BCE53D53EEECE27025F7E94FDD4BF00099,
	FontAsset_set_fontWeightTable_m39D8B63BD3FCC773AAB5634C6D9314C713161814,
	FontAsset_get_regularStyleWeight_m6C4B4D4CAD36800E6E686A05A5DB8D4475F2707F,
	FontAsset_set_regularStyleWeight_m2D1E5440A5E1794A003FF087A87393DA9A114385,
	FontAsset_get_regularStyleSpacing_mB7EEEA236312F5AC31FD3B787808279206F521B1,
	FontAsset_set_regularStyleSpacing_m7CCE54FB9163D65A6B40269B2BDD30199023E797,
	FontAsset_get_boldStyleWeight_m804ACC85DD80DC72DB4BCC83C3FB866411F8EFCA,
	FontAsset_set_boldStyleWeight_m204B04CF9E98AD8669025BFDC0EF3CE9AB5CBBA2,
	FontAsset_get_boldStyleSpacing_mB8CF4F4880B110E41D566648FF1D995010CF1FF0,
	FontAsset_set_boldStyleSpacing_m62DAA35837E8563DD76E3D162B6DB59BE3804914,
	FontAsset_get_italicStyleSlant_m69E70060C6E7940B4ACE61F2B7CB8965F86DA96B,
	FontAsset_set_italicStyleSlant_m223875ED81B0397CA36E94A6F346AEE68510C0D2,
	FontAsset_get_tabMultiple_m9C0422A00BFCF82091F14F4E303E2717247350AE,
	FontAsset_set_tabMultiple_mC927B74D27FBB94245E09FE39D9F6749AF07017B,
	FontAsset_CreateFontAsset_mDBAB2C51721B94702B8706450DAF74FD2E757712,
	FontAsset_CreateFontAsset_mBC142F8527671635D9472BCC22C65A2E94368253,
	FontAsset_CreateFontAsset_m5C2993AF8A6DB979E34173276952C0DD70524777,
	FontAsset_CreateFontAsset_m627EE2CFBDCEADB906A5CD26FB474D3C1A37A166,
	FontAsset_CreateFontAsset_mEFD731F35B521BB833F532C0EE7637F97647E650,
	FontAsset_CreateFontAsset_mF5E82AB887021B02F7DA71E36328A9D1C943F1B5,
	FontAsset_CreateFontAssetInstance_m9114A363FC6DDEB899CC75C73A41CF903A59333E,
	FontAsset_Awake_mB7906A1E21F5FAB84A023B97435F75C09EAB92ED,
	FontAsset_OnDestroy_m3587016A089072C5C03168AA4C6AA1956FE12785,
	FontAsset_ReadFontAssetDefinition_m6D84DBCB130D530B2F78A7E24232D8A6A81AEC48,
	FontAsset_InitializeDictionaryLookupTables_m29A4AEF49CF11A0E49C229EF13B2262AE66757FF,
	FontAsset_InitializeGlyphLookupDictionary_m82782B7B5C602AD5097A016BF668868C0892CCF6,
	FontAsset_InitializeCharacterLookupDictionary_m8886A4CA911334BD319AB78F1CBBD68E13624BB6,
	FontAsset_InitializeLigatureSubstitutionLookupDictionary_m85D0338B542EEF7E5DE8224AA699730752F93FD3,
	FontAsset_InitializeGlyphPaidAdjustmentRecordsLookupDictionary_mDF1C20792344999B9CF500685E6256B0642CE7DD,
	FontAsset_InitializeMarkToBaseAdjustmentRecordsLookupDictionary_m7BC52CF67C055F71B1E9A79B761F79A86E5D8716,
	FontAsset_InitializeMarkToMarkAdjustmentRecordsLookupDictionary_mC911E1C69D58A7490587C63473D65BA1DB775D56,
	FontAsset_AddSynthesizedCharactersAndFaceMetrics_m203BD62D0A537A6EA7CD7DBA1FF9A94301492933,
	FontAsset_AddSynthesizedCharacter_m6ABFCE6454A09D5CF7914F318DDC79198C47F9EA,
	FontAsset_AddCharacterToLookupCache_mB90E06CE313CC0BB6F81415BF8FB4E043108EED8,
	FontAsset_LoadFontFace_m64C78A2FE5DA2E7029E43B467A1B242827B45B4F,
	FontAsset_SortCharacterTable_m9A551DF3B19E246E8C4BE86463E0ED1DEB27D321,
	FontAsset_SortGlyphTable_mC853714CB002D923A19C3A925BB24D6BF42A08CD,
	FontAsset_SortFontFeatureTable_m072B32D6D8C562F60D3D6CBCC7DCB3282EDD587F,
	FontAsset_SortAllTables_mACA7063865A460F5949E5B8A8D978D588124A094,
	FontAsset_HasCharacter_m6BAF48714E1BF5D8EE7ACF33F774C8C6EEE452F3,
	FontAsset_HasCharacter_mE87EEF6CDA1F4E1D6928CC9A3C01A91922D4FB21,
	FontAsset_HasCharacter_m3E405FA081E68243DDB6558FA03530686E894EFE,
	FontAsset_HasCharacter_Internal_mDC0D2954E0975A7DBC8829E894CDBCABEA7D6A60,
	FontAsset_HasCharacters_mD670CCEB48448CE5C1430B938F99D4FC659FB2F8,
	FontAsset_HasCharacters_m97A50BC627C163418CAE0B42A50893057B025E90,
	FontAsset_HasCharacters_m048839FDD1876CDCA3C5A744592545B46C75E15B,
	FontAsset_GetCharacters_m5CCEC5A0F89B0BC29B1EFEE9E33923F6879409F4,
	FontAsset_GetCharactersArray_m4CE08FECFCF3F9913B5B29DF0F2272FA4B3F945F,
	FontAsset_GetGlyphIndex_mF20097CDB68A8CE866E61D4C237FBB95257A9745,
	FontAsset_RegisterFontAssetForFontFeatureUpdate_m5D26FB74AB0C3AA8DC363DA517C1017641F85739,
	FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m021524DE2B5F8ABE9DDA58447F896159EA15E0F3,
	FontAsset_RegisterAtlasTextureForApply_mAB5DC9E4B77C8E0FCADF0AD98E8D63C5229FC383,
	FontAsset_UpdateAtlasTexturesInQueue_m359EFCCB6F692F5F8546D4205F366B079075792E,
	FontAsset_UpdateFontAssetsInUpdateQueue_m67B9FE54C99FDC8FD3FE3471768C416083E36768,
	FontAsset_TryAddCharacters_m7F1D0CB7E4D9B8D3CE44D4D01F9CDCEFD4D1B46B,
	FontAsset_TryAddCharacters_m9618B4F12C004B8267E0D17ED81B94BE48D85119,
	FontAsset_TryAddCharacters_m5E282618D9ED92AD0112BC7B6B2C3B1066DDFA63,
	FontAsset_TryAddCharacters_mDA1C3A68799C84A80C27CDB84482684F6822137F,
	FontAsset_TryAddGlyphInternal_mA41540AE85F2F11562E1DB5B763B37D29D9D497B,
	FontAsset_TryAddCharacterInternal_mCDC9AE2C61B9F73B8879C3F5AE00598A67B2BA7F,
	FontAsset_TryGetCharacter_and_QueueRenderToTexture_mA76A244F58E0F2978178FBBEB18F2E0DCA568AEC,
	FontAsset_TryAddGlyphsToAtlasTextures_m83F7EDB3193B3C9A4FA86B89A51E9FA6A41F6834,
	FontAsset_TryAddGlyphsToNewAtlasTexture_m8F98FBF7A0EC1B37C4DB43536DA42D3864F6F3AB,
	FontAsset_SetupNewAtlasTexture_m38F81BE1582A15DDDB950E7AAC650CD9B7D14168,
	FontAsset_UpdateAllFontFeatures_mE00FE075794A727BA10860C6B9BB237BAA2EEEE2,
	FontAsset_UpdateGlyphAdjustmentRecords_mD1C9297EA75EA767A823709CC39B6E57905E22A3,
	FontAsset_UpdateGlyphAdjustmentRecords_mC9882537E81709FE1EFA9B744D88C6C32ACEDF52,
	FontAsset_UpdateGlyphAdjustmentRecords_m2D0444352012E8DFDD6036025886EC0CED0AD82A,
	FontAsset_UpdateGlyphAdjustmentRecords_m9410421BA99635607C50EED1C9C374570EFABC60,
	NULL,
	FontAsset_ClearFontAssetData_m225ADFCBB0CFD481E18637F3D3FDFFEAFC6FE9A1,
	FontAsset_ClearFontAssetDataInternal_m3B01092F3CBA3EDA9A06CE14B20628C1A654F759,
	FontAsset_UpdateFontAssetData_mAAC0ED05410942C08E8EFD4678F9565FD8C373D4,
	FontAsset_ClearFontAssetTables_mA528E871C1830F3A5303DABA01A7D28747777F73,
	FontAsset_ClearAtlasTextures_m5B320A65E1CD35F2C17E27F09158F8E9BDA9EA2B,
	FontAsset_DestroyAtlasTextures_mBE2810F8C55E286B5B7ABE24A6F9132F51CBE027,
	FontAsset__ctor_mD55676BD025F9D05DBC9A5B32480E092169B9D45,
	FontAsset__cctor_m0F11465E3C2F3D158DB9BF403CE86298432BD00E,
	U3CU3Ec__cctor_mEB4ED309CE67EB2C5633590466BD1C4EE90E0D5C,
	U3CU3Ec__ctor_m3CDF0903C024856278B5A7DF46C7EFCBA6F6B651,
	U3CU3Ec_U3CSortCharacterTableU3Eb__151_0_mF8A06416B0EE30DF937290D36C5AC1F1E94BF918,
	U3CU3Ec_U3CSortGlyphTableU3Eb__152_0_m43F8B101FBB6D5E117C6AF29EE8EAC75F6D48BA1,
	FontAssetUtilities_GetCharacterFromFontAsset_m0F073D15EC39A1D4F302F02A5E2F583F28889332,
	FontAssetUtilities_GetCharacterFromFontAsset_Internal_m3D8D41600A318422D89103D3839B5CFCF15A956E,
	FontAssetUtilities_GetCharacterFromFontAssets_mB26999A2C8D9AD3D35857403DD59BEED6D008BA0,
	FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_mB9147480CA9223089D54748F37F227BC180D78E1,
	FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_m5A04BE23599904FF0B23061BC9F7EDBF6AC7DB88,
	FontFeatureTable_get_multipleSubstitutionRecords_mEA4CFF09748B2EAAE509E9C5B778A2DD0B0DE761,
	FontFeatureTable_set_multipleSubstitutionRecords_m8AF6E0295B7FE6FA5CCCC2358F5D5B8D35BD7847,
	FontFeatureTable_get_ligatureRecords_m990413B6DEF09939C91F6FCBC840D0A6335F0F13,
	FontFeatureTable_set_ligatureRecords_mC9887A0AA05EF8CC206B41307769BEEC76721A81,
	FontFeatureTable_get_glyphPairAdjustmentRecords_mABE78F7C2EA171927CC33170617D72E6C976323E,
	FontFeatureTable_set_glyphPairAdjustmentRecords_m05F45829A25D9B115D2450F629B705ED6B66B1A9,
	FontFeatureTable_get_MarkToBaseAdjustmentRecords_m73A1A8FCDB3E9629C15FC2568473B235B9A52D9B,
	FontFeatureTable_set_MarkToBaseAdjustmentRecords_m4CE5E05EC230B9D606F3690B9C17321444CF5374,
	FontFeatureTable_get_MarkToMarkAdjustmentRecords_m3DB78C6BBC2E41936F04E46107002DC5F2136B8F,
	FontFeatureTable_set_MarkToMarkAdjustmentRecords_m28DCFFE2C4A217947BC1000280B78F19A6DECE0D,
	FontFeatureTable__ctor_m5F00F284C63F1867F679A3250ABFC1393C27025C,
	FontFeatureTable_SortGlyphPairAdjustmentRecords_m2F5E2ED405FCAEE946CE5CF81163DDCC1B02A905,
	FontFeatureTable_SortMarkToBaseAdjustmentRecords_m2AF48E3FC40E5C970FCD9A4ACA4354FD3CD09004,
	FontFeatureTable_SortMarkToMarkAdjustmentRecords_mF4A796852F11F07614DF6434DB8E3122E94E7E3B,
	U3CU3Ec__cctor_m024C5AAB034EF6BBF574EA4EB3AE66D03E259CEA,
	U3CU3Ec__ctor_m51815D1379A3BDB616D65C006DA7AB32406723F4,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__25_0_m74FE6156C6CC986E55868DB69C6EDB79F6F19066,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__25_1_m3F126EE9B7D04A378F4203E51050ADDECE63CB68,
	U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__26_0_m3220742BC5DB3D8370C277F2EA62943B6C0A36DB,
	U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__26_1_m58B31CA43C54C82A5C97EE0869052303FD54B338,
	U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__27_0_mF081BD05B971C16DA0D3AB99B5A312716FFA5BC3,
	U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__27_1_m22B6E5543ACDFAC59B11D4A570B19A0974ECFA49,
	Extents__ctor_m29242B7ECE336324673F4E94B869F0486922BB38,
	Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC,
	LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F,
	LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8,
	LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB,
	MaterialManager_GetFallbackMaterial_m10F67CE1AE1E0B9D8BA8AFC06110FADA2404B5A1,
	MaterialManager_GetFallbackMaterial_mB65C8D7625B0D0A0D623FC6AC545469AF6B7724D,
	MaterialManager_CopyMaterialPresetProperties_m2DB1A033E378F3DF347DEA0DC51F1E51776169F4,
	MaterialManager__cctor_mB6337D5678E1F45035C1F8EE33D389AB4CBA6527,
	MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A,
	MaterialReference_Contains_m1B4B5E75B1373C5E367FB36BE5B52E660A92DCCD,
	MaterialReference_AddMaterialReference_m13CC47A7CA6C8781EA68A355B36FCD5AFF467A40,
	MaterialReference_AddMaterialReference_mEE57C2284E5BE17BDD80C69165FAECB3B4CC1BCD,
	MaterialReferenceManager_get_instance_m30BD1A367C3807D0B42CF7156CE699B15E51ED07,
	MaterialReferenceManager_AddFontAsset_mF950087FC7EAA324CC15D3E19D62138A96F6FDF7,
	MaterialReferenceManager_AddFontAssetInternal_m6F5A4E5ED988BA6F482F015F051ACD19D7B0A005,
	MaterialReferenceManager_AddSpriteAsset_mC95864F8450686586161E80A908552FC9F417A47,
	MaterialReferenceManager_AddSpriteAssetInternal_m4B3DB7DCA9D165B2C48B513ABD7C9061389B4830,
	MaterialReferenceManager_AddSpriteAsset_m7FBAAC41386CFA769FDDFF1630D041C4D008A322,
	MaterialReferenceManager_AddSpriteAssetInternal_m788619DC6BAD5B77E9419ACBDECBCCFE1A6AC97C,
	MaterialReferenceManager_AddFontMaterial_m76DC9390D0599D1AC898AE981C8756EA0E536AA6,
	MaterialReferenceManager_AddFontMaterialInternal_m732F46EF768A41B9519917F4FA1E746E056C745C,
	MaterialReferenceManager_AddColorGradientPreset_m1496DD123DB526F2B3F49DD3BCAED78A6B1B83EB,
	MaterialReferenceManager_AddColorGradientPreset_Internal_mF27270501EB3725B4CBE4C241B4A2FCD8D871BF1,
	MaterialReferenceManager_Contains_m7A3365858320686F56F39123E906375ACDBE00A5,
	MaterialReferenceManager_Contains_m679BD7A4F3989E74BAB87957834A2093A557B0A1,
	MaterialReferenceManager_TryGetFontAsset_m499C7538909343667E9B837489662862CFF9FB56,
	MaterialReferenceManager_TryGetFontAssetInternal_m2FECC618624B12D200EB311F59CBEECA7CDBB69D,
	MaterialReferenceManager_TryGetSpriteAsset_m1909F75399C0A23DDE7DEDCA60476E1F1ED567EF,
	MaterialReferenceManager_TryGetSpriteAssetInternal_mC434A7C6DB005EDBBA52154E2AB0E36ED7083C84,
	MaterialReferenceManager_TryGetColorGradientPreset_m7442603626A04928C6C042BDAC3D3957B8C0AA99,
	MaterialReferenceManager_TryGetColorGradientPresetInternal_mD8018B3225786E71F804D629F3107AB75EE5212B,
	MaterialReferenceManager_TryGetMaterial_mDC2610737935CD2DE6B1F6302F63C2F3BF7E09CF,
	MaterialReferenceManager_TryGetMaterialInternal_mEBFC9CE0A6063B25FEA9070F22FD8AD27107ECE7,
	MaterialReferenceManager__ctor_mC102EC445A27BE8E3968ADB80EF8FEF3BCFB7778,
	MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61,
	MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC,
	MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475,
	MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830,
	MeshInfo_ClearUnusedVertices_m8D57192C6582A8E8EC3A2E133B057896ECE5FC2E,
	MeshInfo_ClearUnusedVertices_mA71B4EBAF0308EEA00061C57244BCB2EDE2D557F,
	MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1,
	MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F,
	MeshInfo__cctor_m667601EA787620405D7DB5F0B37E4D489DE067D9,
	SpriteAsset_get_faceInfo_m54EC5227F682ED6A24F5633283258E6641CDA4DC,
	SpriteAsset_set_faceInfo_m060A5DBBD5941A53BFE9D45E2B637D88ED8223EA,
	SpriteAsset_get_spriteSheet_mC53205114A12A79F7495FA5F5EFC9948F151256B,
	SpriteAsset_set_spriteSheet_m1DE591615ABCBB4B10118BF4C0E1B57F559C6469,
	SpriteAsset_get_spriteCharacterTable_m8D0D65C430AD8BC8C2BC8151DC4672CC0F690E0A,
	SpriteAsset_set_spriteCharacterTable_m38553B81E01B502CCD568A654E9EF3B0D0BCA92D,
	SpriteAsset_get_spriteCharacterLookupTable_mDB0BA7D703F58A05BB765AE44D6244D98C17DE03,
	SpriteAsset_set_spriteCharacterLookupTable_m38F3E7A0A52B82595C87E6A630B156E4D22F2E25,
	SpriteAsset_get_spriteGlyphTable_m491B3F4A5350C38D8B5166A60B7C43ED3608C0BA,
	SpriteAsset_set_spriteGlyphTable_m7402B7CF195E4B9F15DDA3273F49CA460A68DAD3,
	SpriteAsset_Awake_m3A012935612A7EB924A77B85EDCF6C09257F60BE,
	SpriteAsset_UpdateLookupTables_mCC7A470A65A72908C9CDBDDFD17A056188A5C7CE,
	SpriteAsset_GetSpriteIndexFromHashcode_mE73615D1D9A8BB45C3426197EC54B1A002642DE0,
	SpriteAsset_GetSpriteIndexFromUnicode_m321E02B6000E5F6673F5724155C3EF1DE3F5A66B,
	SpriteAsset_GetSpriteIndexFromName_mBCB684ED6E3DF5663A7FDA02CA69C99D9B17281B,
	SpriteAsset_SearchForSpriteByUnicode_mC50EFD6F2B78609891C357212CF65A8F18EB8A66,
	SpriteAsset_SearchForSpriteByUnicodeInternal_mAA47B4DB58070A7A3F5F97C597098A65E896B5A5,
	SpriteAsset_SearchForSpriteByUnicodeInternal_mEB122A514DF6A0D063EF8BE18F31F278ED9C3518,
	SpriteAsset_SearchForSpriteByHashCode_m8B9CAB0028CC297570785693F2973918B31C9028,
	SpriteAsset_SearchForSpriteByHashCodeInternal_mAD106CFA37AACBD783D0A74817D55507013BBC14,
	SpriteAsset_SearchForSpriteByHashCodeInternal_mD38A7595ACBC7773C8292B0FD7E5A170A4105208,
	SpriteAsset_SortGlyphTable_mA700CE5246D5798FA65779BE53179FFF4FFED6E5,
	SpriteAsset_SortCharacterTable_m5447649977AF2C9F62A14415B44CDDD897A53AE1,
	SpriteAsset_SortGlyphAndCharacterTables_m0E2B691E7C1F284E12A88B47B705307E83C7D927,
	SpriteAsset__ctor_mE03F69799389DE8D90E69CD70054955033C4ED3C,
	U3CU3Ec__cctor_m84A11BA2AE10FB375A960D167ABF5F559DACAE3A,
	U3CU3Ec__ctor_mEFC122BF1D0D0CA8F0EAE9CE353C37A8CFABB5F3,
	U3CU3Ec_U3CSortGlyphTableU3Eb__37_0_mC479CF63F85C34FC407D92E67878B9B2AD99B739,
	U3CU3Ec_U3CSortCharacterTableU3Eb__38_0_m6A3F26D4286DF4F04DC23D23D04E12CA014D6E92,
	SpriteCharacter_get_name_mD5A9CC908308BB48D459973C8844FE1FD7C925B1,
	SpriteCharacter_set_name_mB45844384C3B06A780E82C2E946A29A799688AFB,
	SpriteCharacter__ctor_m0B3812DF9A667CA2C3AA321DF3403197EEBC83BA,
	SpriteCharacter__ctor_m2B9EC943D7F7E3A30608DCAFC9F676C23AD0921B,
	SpriteCharacter__ctor_mFF065D774A8B3ECD50323C0DEB241E7AED0EF043,
	SpriteGlyph__ctor_mFFE2D9AA5A28EA0C20C10694569A1E23A664BB4D,
	SpriteGlyph__ctor_m0DD3EAB9307FE36B47116BD4E6A2289689C54422,
	SpriteGlyph__ctor_m0935F217CCDAD97DD7542B0E6767C79E96B8AC40,
	TextAsset_get_version_m2316C9F212FE4A13335032354A496CAEC86CB25B,
	TextAsset_set_version_m24718A4A6A86F4CE41D9ED1E0F796CCF367516CD,
	TextAsset_get_instanceID_m843A6CAA7FE9322CD19546671D3F0E90A0E27AFB,
	TextAsset_get_hashCode_m4D519E837097D8869A8D38EBD11611FADE411092,
	TextAsset_set_hashCode_mC6ED3271A5EFC05562FD1083BE1C872CB69CFF74,
	TextAsset_get_material_m4B9C02D34426436FDB01F1963A9FDC11D75604EF,
	TextAsset_set_material_mD9988165763E0E72C7FB7537760899EC1841C829,
	TextAsset_get_materialHashCode_m9BAA469D5760D87EEC6E09B9ED3C5902B75017E6,
	TextAsset_set_materialHashCode_m15EE7CCAF81DBAA326049F00788BCC918FDB2631,
	TextAsset__ctor_m2C43EAC2D75854E6724A235794DEAF8A1AF9E786,
	TextColorGradient__ctor_mAF03C3B68C29D94ED1F1517E65477CFC2B078FAC,
	TextColorGradient__ctor_m0712D0C09E7BCBC00909CEB71B43F2276ADB8B55,
	TextColorGradient__ctor_m0A6B9264A8FBABEEC5BA84353D9B3DD7999DD2FF,
	TextColorGradient__cctor_mC5AE96BE4461EF9AE84CE4E3765B712D6A64693E,
	TextElement_get_elementType_m7BF97842479112227C1C3C83E0E94A176CD7D31A,
	TextElement_get_unicode_m40C69806537940F7BA1D3969713DA10CCBE57BC7,
	TextElement_set_unicode_m99608D824B25E3529236C06BCC0983B5FC094F98,
	TextElement_get_textAsset_m52383A3758AABF5BEA013155765BD1141479685A,
	TextElement_set_textAsset_m3F65429660C011F6F25B65D6BA7C4B2CF05659FA,
	TextElement_get_glyph_m101DBCCA0CDE2461B504174272A2FFCD53EA59E2,
	TextElement_set_glyph_m6E8E2F1366089FA638680F1CF53F6F5027D022A5,
	TextElement_get_glyphIndex_m43F82F2F998D640DEDBE6860EBE7B171DDF4FE56,
	TextElement_set_glyphIndex_mFD72B93816998BE291DA6379EAF5E4153BC64F6C,
	TextElement_get_scale_mD16946900449FEE9E2F86B2C4C71E26F4491A0E6,
	TextElement_set_scale_m83FC0850B2B0F31BDFC779954923F50FD06DC03F,
	TextElement__ctor_m8CB701D9A4C0444E834F178D97E9FC63E6D7E0B9,
	TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985,
	TextElementInfo_ToStringTest_m7E0775478741E60973A13C91273ABFB9105E0AD4,
	TextEventManager_ON_PRE_RENDER_OBJECT_CHANGED_mC8130CBDB95276A1BC8676EB06261613D1EA2E47,
	TextEventManager_ON_MATERIAL_PROPERTY_CHANGED_mFBE8778C69D37BDEF325DFE248D85EA31C0A4B46,
	TextEventManager_ON_FONT_PROPERTY_CHANGED_mC5C27C15B5C5715EE7B079A45D09914AB12B479C,
	TextEventManager_ON_SPRITE_ASSET_PROPERTY_CHANGED_m7E3202ECDBE8A3EB91A0E05FAA3932C61E2EDC30,
	TextEventManager_ON_TEXTMESHPRO_PROPERTY_CHANGED_m8688C9B57C3A5560EF0BE25A2F25B1ED26832CDD,
	TextEventManager_ON_DRAG_AND_DROP_MATERIAL_CHANGED_m0A71D08AEECAEFC7921ACDDA062EFB866BAF4DDA,
	TextEventManager_ON_TEXT_STYLE_PROPERTY_CHANGED_m0DAC279CA1A844D84E68BDA03E7546170F3A7691,
	TextEventManager_ON_COLOR_GRADIENT_PROPERTY_CHANGED_m55CABCA07BA886979D1F2B1E84CCE8A16E32C1DD,
	TextEventManager_ON_TEXT_CHANGED_m55611A25AD0771E196A15E304F6949C210FD4530,
	TextEventManager_ON_TMP_SETTINGS_CHANGED_mC6B1826E7A60F80A9984194B199C845737635F15,
	TextEventManager_ON_RESOURCES_LOADED_mA6942BD5A7747575F92A392805B19D2A5EA01571,
	TextEventManager_ON_TEXTMESHPRO_UGUI_PROPERTY_CHANGED_m5C91775BF77D87BA8FA0939A554E4A724D1829FC,
	TextEventManager__cctor_m616826AB5C10C1D16331F079CAF25B9440697C4F,
	TextGenerationSettings_Equals_mA5EDDF0453F2A7314AF5E1FE29F4138CD97E52D5,
	TextGenerationSettings_Equals_mDCEEB056B70FC65ED6065E3BFE8D69D823DFEFD0,
	TextGenerationSettings_GetHashCode_m1F750434FCE1853C36A579827B064619E85453E0,
	TextGenerationSettings_op_Equality_mC417375DF36E0AD1A2A5BABC5DF2C0C6B250080E,
	TextGenerationSettings_op_Inequality_m01EC5AAB9B6A14F1E7CF3C05A738F509479B25C4,
	TextGenerationSettings_ToString_mF8AB635EB369C7F66AC3B396934158CF3B6BA6CE,
	TextGenerationSettings__ctor_mA20608A16443434DAE9FEF0BF8BD076270FA660E,
	TextGenerator_GetTextGenerator_m5BDD6657637032A944115A1D6D52A6D511D43D46,
	TextGenerator_GenerateText_m28C6EED85E5BB42AA40812F475D533AAF6694757,
	TextGenerator_GetCursorPosition_mC48EEAF0A850F5827C43E6D2AA60C8AAD2D59627,
	TextGenerator_GetCursorPosition_m9F767EE74114971780EF08619DEE0F5223FC5095,
	TextGenerator_GetPreferredWidth_mF70516013AD339211A33B0A937362493FB460C50,
	TextGenerator_GetPreferredHeight_mFD002673E41F471BF921C678F0B8D7B021738DE9,
	TextGenerator_GetPreferredValues_m17A1C8F1AA7D260AB9167985429D6819D0E8D9CA,
	TextGenerator_get_vertexBufferAutoSizeReduction_mE156525BFE36A7F3764E93878414B7FD2463A0E5,
	TextGenerator_set_vertexBufferAutoSizeReduction_m861779E70BC9413319C5D1CB13867D823178F3B1,
	TextGenerator_get_isTextTruncated_m667879F08A9B2619D89520F1E747444B2B1EF5DD,
	TextGenerator_add_OnMissingCharacter_m8B5E5BBEF386116E4E93A434063368E39D9B46E5,
	TextGenerator_remove_OnMissingCharacter_m02CBFEC05541ED46391C1CE0216DC1B763C32831,
	TextGenerator_Prepare_mD0A24977334138340CA73FB9787627373C6AA255,
	TextGenerator_GenerateTextMesh_mAB70FC29A49A6C4F8211EA977E37C66BE67D1831,
	TextGenerator_SaveWordWrappingState_mC07B2C5977EECE10216F8C6AC9CC4204F7EF1936,
	TextGenerator_RestoreWordWrappingState_mA63B3DD2C02E61CD8670A32A53163AF6BF765F61,
	TextGenerator_ValidateHtmlTag_mF8187EB1D0CB901861EDFC36151409F8FF6AB287,
	TextGenerator_SaveGlyphVertexInfo_m0CD6E1D45488FFC6675294AC64F40AC23C986A09,
	TextGenerator_SaveSpriteVertexInfo_m4B47901F01927E7CC4E486A1C4354AFBF4D138A5,
	TextGenerator_DrawUnderlineMesh_m307EA8034106ACD13F89CC7E78C5DE08CCCCEFAE,
	TextGenerator_DrawTextHighlight_m4046F4CC59C6DD8FE5B0BD97DB8BFE015B829389,
	TextGenerator_ClearMesh_m68BA46B0365FC730BA5D2E6BDF2528BD370B2D83,
	TextGenerator_SetArraySizes_m780796D50B2A5406E06F493503DA82BF5DA08A0C,
	TextGenerator_GetTextElement_mC46F0E788A0F6EB5A62601BCE4F383C3143C78CB,
	TextGenerator_ComputeMarginSize_m485F8B01196058B15F597DE99D6F6A47FA539D3F,
	TextGenerator_GetSpecialCharacters_mA82879FA537C58223BB660E797AC135A8E07B492,
	TextGenerator_GetEllipsisSpecialCharacter_m5139CAE03CD2E25C9A528A6A6FC984A8515C2460,
	TextGenerator_GetUnderlineSpecialCharacter_mE5E9D5DEB9A7758333CDDCAD05EF25F076EC1AD5,
	TextGenerator_GetPreferredWidthInternal_m26376C8696D2FC64393FC216A0E4DCB43B0E763E,
	TextGenerator_GetPreferredHeightInternal_m9324ABDB103E57EDB792B86924A189718F8F1F43,
	TextGenerator_GetPreferredValuesInternal_m125B070164DFEA503C67525D1F418DAF41300ABD,
	TextGenerator_CalculatePreferredValues_mB4E6FC8AEA08D8108D234D84DA2009A9D24CB5F2,
	TextGenerator_PopulateTextBackingArray_m82B0E48D569AE2DC8F62A49EF8A5A1B1F60A80A9,
	TextGenerator_PopulateTextBackingArray_m522293F1EFF9EC80DF2B45DE08861659B47080D2,
	TextGenerator_PopulateTextBackingArray_m869636ADF1D64E4B74450AEADDCA985DD1AEB283,
	TextGenerator_PopulateTextBackingArray_m8375955F43A5F557DBDB82A3647DCBFADD6093FC,
	TextGenerator_PopulateTextProcessingArray_mEC6B2EE86D363FF3F7CEE50C77A6124A0A27DA16,
	TextGenerator_InsertNewLine_m00109EA00343212A7FD05D49E7DBF81DBFE4B5E4,
	TextGenerator_DoMissingGlyphCallback_m643F3C7C677B4F98BFE251055ECE1E588BEFFB04,
	TextGenerator_ClearMarkupTagAttributes_m6047C48E973FC0E5A524AEB3F78D20E958E747C0,
	TextGenerator__ctor_m52E4D01DC28BDF753BF52F6501E7FD2FB2B30D90,
	MissingCharacterEventCallback__ctor_m22C62F2B7DAAEC494F16008EEA0F192BE77E4AC4,
	MissingCharacterEventCallback_Invoke_m5BF78AFFA87C08BC81EC893548949E960E0797D4,
	MissingCharacterEventCallback_BeginInvoke_m7B51940C594D358DA6762E3AF63E8BCA7A30E4B3,
	MissingCharacterEventCallback_EndInvoke_m6A314AF9B89AE38070DCA1609F9C0C587B62D942,
	SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969,
	MeshExtents__ctor_m09C3052557EE349D040C72B6572D52D48645F2D8,
	MeshExtents_ToString_mC78F6BC3525EAD13F90B39A7E1C7B3301598E9B0,
	TextBackingContainer_get_Text_mB9B62AE587962B01FF21BFB72996AE40F6D36EFD,
	TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7,
	TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416,
	TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97,
	TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612,
	TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D,
	TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7,
	TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698,
	CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29,
	Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82,
	Offset_set_left_mBA08D34537453D783769DC94796C8ACC560E5D28,
	Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB,
	Offset_set_right_mA7D0F7DD84DE12F92B800449EA54C2DFC8AF5F2A,
	Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4,
	Offset_set_top_m3B5760ADA3A3A5550CEF1B6BBE7CE3E95C6AED11,
	Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C,
	Offset_set_bottom_mE54667D478772330F8C34F55509178EB3958D103,
	Offset_get_horizontal_m1266C1DC1F818B19D4084D6E120A0CA38E18DC25,
	Offset_set_horizontal_mBCE3E8C5FA4EF01EC9196A76450BBB4C6E7ED68E,
	Offset_get_vertical_mAC6BAE572DC64A052407108CF726DC0210D72DFF,
	Offset_set_vertical_mC01378B8286D0293946D2237205BD45256392055,
	Offset_get_zero_mF5B6D7C3F437FA438844A0B3EF405D805F1D1958,
	Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7,
	Offset__ctor_m0F9E756FCB8AF792C3EDCC24673670E6ECA605DE,
	Offset_op_Equality_m122A34D50DB0E70BDEEC631D0082E9CFB8D19C8E,
	Offset_op_Inequality_m2E1761A654AEF757DF6B74A63CB3C123D14EFA80,
	Offset_op_Multiply_mE5215371DD76A27676FF67C992C065BC456A8131,
	Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038,
	Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D,
	Offset_Equals_m3C88E9583BCC9A281BD1CFEAAE896F532F9EC0C7,
	Offset__cctor_mB8571222B76084876413C594C17AC5A343B40732,
	HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F,
	HighlightState_op_Equality_m6E4A396D3C2C5932BCCE96E7B3AE42E37E447923,
	HighlightState_op_Inequality_m2DFBCB59E593F72191BFBBD7424A8C6151E68272,
	HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA,
	HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849,
	HighlightState_Equals_m8F74F12721E91EA8928A098BC6815E253478D8BE,
	TextGeneratorUtilities_Approximately_m696ABB909732F536F1FF83EA8CE34CF53266794D,
	TextGeneratorUtilities_HexCharsToColor_m2C739FBEC67C612B593FDF344E5875F0C0D8AC31,
	TextGeneratorUtilities_HexCharsToColor_m4D7AB2E490DA31C514A42A880AB35CE9CBAF77B4,
	TextGeneratorUtilities_HexToInt_m41648DAEE872433A0AFA82018A9539ECC5C0FFC6,
	TextGeneratorUtilities_ConvertToFloat_m93B85749154AAFB614CD246876786B75C98AE11C,
	TextGeneratorUtilities_ConvertToFloat_m37E1CF11E22ED6528B166907211426EEE6D0A4F8,
	TextGeneratorUtilities_PackUV_mE110A97960725C40D87FA903B63E0100AFCB06F5,
	NULL,
	NULL,
	TextGeneratorUtilities_IsTagName_m67178B96C0B3119FFEA83A8ACED7D7DAD0414208,
	TextGeneratorUtilities_IsTagName_mA8FA492104AD554B93FD6FDAC0D67416ED746A9D,
	TextGeneratorUtilities_InsertOpeningTextStyle_mF71E0B0C1B5E938C5AAC7F8FB3CD5278DEEC2408,
	TextGeneratorUtilities_InsertClosingTextStyle_m08B150E030816A5084205B49DA40DED97E0C7036,
	TextGeneratorUtilities_ReplaceOpeningStyleTag_m67FC3FFDE1912D2E7C2DC2BED4C5BA250B1DB705,
	TextGeneratorUtilities_ReplaceOpeningStyleTag_m947084F63F6548A105DD1D244685A2717820FA70,
	TextGeneratorUtilities_ReplaceOpeningStyleTag_m6F65A6D9D8DEA4915B30DEBFF43850B9D7063252,
	TextGeneratorUtilities_ReplaceClosingStyleTag_m9DD77D4EACF2389DF2631F515A23C11DC5E58A3B,
	TextGeneratorUtilities_InsertOpeningStyleTag_m94153F78A4B8F7A1811D2C1E9567996E39616F60,
	TextGeneratorUtilities_InsertClosingStyleTag_mD6A4B3357D6478C5770AEE460F61917584B905DB,
	TextGeneratorUtilities_InsertTextStyleInTextProcessingArray_m7CC6FF13CD9B2F3BD04C60C2A2B527960C6D1D09,
	TextGeneratorUtilities_GetStyle_m236E7C5EBFA951D1D4B2C3C7803BBEA5C2FD1812,
	TextGeneratorUtilities_GetStyleHashCode_mA4CDB93771348C1236A8E9BE1EB4A9D5C057D516,
	TextGeneratorUtilities_GetStyleHashCode_m7B5002A635CF32D023E543FDE814E0E958A89EF1,
	TextGeneratorUtilities_GetUTF16_m5B397339CD29B370A27D3BA3B8BEFC12E8C56434,
	TextGeneratorUtilities_GetUTF16_m4E03C41F3B5323D6234DEC0A312F13CEAACCA8E6,
	TextGeneratorUtilities_GetUTF32_m334BA95AED813976AC79E68EB677BADB5CB15CE3,
	TextGeneratorUtilities_GetUTF32_mAF367B8C1D5B586B49AED2B69E5E7ECEF3378D0D,
	TextGeneratorUtilities_GetTagHashCode_m89243960CD468B3DA8EC0AC29829FD8125056D9F,
	TextGeneratorUtilities_GetTagHashCode_m52AED809412409E8D9D9AD9D4DDDF0990A0049CE,
	TextGeneratorUtilities_FillCharacterVertexBuffers_mE0CCB8DA0D27F37DCFC4E47E89697D8823A8FCE8,
	TextGeneratorUtilities_FillSpriteVertexBuffers_mD1AECFE4D4356A6925BF056E15CF84118313412B,
	TextGeneratorUtilities_AdjustLineOffset_m811C187EA3E41781116F0C7A679B05BB27874123,
	TextGeneratorUtilities_ResizeLineExtents_m2EA9BE32A38D5E075DEF8EDA9EC01766E45C0F85,
	TextGeneratorUtilities_LegacyStyleToNewStyle_m8E9C09A7BE7B27A4EC73ADD747BFD9A4DE7E656C,
	TextGeneratorUtilities_LegacyAlignmentToNewAlignment_mA16F5CB58D3C8F352CAACB8DACA5AFED59D3ED63,
	TextGeneratorUtilities_ConvertToUTF32_m6295E74C04568A52624812F2E615A7F25F235C70,
	TextGeneratorUtilities_GetMarkupTagHashCode_mFFDE1B0B5CD9774F83C988C5D436D1AD01AAD843,
	TextGeneratorUtilities_GetMarkupTagHashCode_m951A939A8B3B0BE3229CB1A94E79FF123C8EF6DE,
	TextGeneratorUtilities_ToUpperASCIIFast_m359D6A8BE78E2C74BA677D8453799487962EDE99,
	TextGeneratorUtilities_ToUpperASCIIFast_mEEED07AD0989B1DF84D559CDE3A397A9F2EA913C,
	TextGeneratorUtilities_ToUpperFast_mE1809281C56E4137C6794B2E94D38BBFA68DBAAE,
	TextGeneratorUtilities_GetAttributeParameters_m261C1E8FB533D3570153B2BAF0D671C5DF4B58DB,
	TextGeneratorUtilities_IsBitmapRendering_m93C5008776EEDD84825ED2133CDA0FC66DD56EEA,
	TextGeneratorUtilities_IsBaseGlyph_mEE0E7D6C3FB32204C2299FBA2B9F7C51E06F80FE,
	TextGeneratorUtilities_MinAlpha_mB52BE8C9C82C15B23D29BF606465B16DD4B1F7E5,
	TextGeneratorUtilities_GammaToLinear_m37B603C94918DB93477EFF98E8A77FD4D8B0C8FB,
	TextGeneratorUtilities_GammaToLinear_m5D4B51EF525F9238F6644BD47106DACCB78797D7,
	TextGeneratorUtilities_IsValidUTF16_m944B75A058B351075C02F1DA61B688FAF1186DE8,
	TextGeneratorUtilities_IsValidUTF32_mD6B22F5E6EAD47537B906859CB093622EECF716D,
	TextGeneratorUtilities_IsEmoji_m84855B4FDA2F5CE4FE0A7231AD6EEF30DB941CFA,
	TextGeneratorUtilities_IsHangul_m5A23BA8E0EBE57243E2E96A248B3F6570A87A966,
	TextGeneratorUtilities_IsCJK_m2F2718B1203271CC2C501C5054590299FBCA5B7D,
	TextGeneratorUtilities__cctor_m01201F8A0A4161B232AB9D179AB74877D4E17D85,
	TextHandle__ctor_m0E8BD79BF9F66EED2A87EA09B246B712DDEED9C9,
	TextHandle_get_textInfo_mA9F2BFB37F7ADA773731AFBC3B53FDD858D87FEE,
	TextHandle_IsTextInfoAllocated_mB9899602ADC3234E901B2E1C8CDC39C8958EE566,
	TextHandle_get_layoutTextInfo_m7521A9C93B844DE793D029CD1E084ED68D824773,
	TextHandle_SetDirty_m485BF8AC302B5A6DC3F63BC8DE3A0D823C2B7F2D,
	TextHandle_IsDirty_m4B25A5E4CD9A7EA8C5A0D35859AA5CA0D611BE01,
	TextHandle_GetCursorPositionFromStringIndexUsingCharacterHeight_m082A44C87BA6376E99A5BD37090429F4A3CF0E0E,
	TextHandle_GetCursorPositionFromStringIndexUsingLineHeight_m44EE4238CC96507DCEB45A28D61E969C877011FE,
	TextHandle_GetCursorIndexFromPosition_m68B7D720ED1589CC46538FACB50E7F7E56AA701E,
	TextHandle_LineDownCharacterPosition_mDD7F4379B59B9CAF7431CCC3E4056CF3511ECF9F,
	TextHandle_LineUpCharacterPosition_m69F1091BCD4A92E343B60966F4C49A9E36106C5B,
	TextHandle_FindWordIndex_m03527F692DC3C652689A9AB4043AC33B0F48DBBF,
	TextHandle_FindNearestLine_mD86CC87E6804492C908C12804379B6323A825DDB,
	TextHandle_FindNearestCharacterOnLine_m68EA4EAD515DE9F7F819ED0AC99908B1DF8EC3AA,
	TextHandle_FindIntersectingLink_m9D72AF4B459885AEFB03A0FF212241F8532B9132,
	TextHandle_PointIntersectRectangle_m55DAEE8660392F7FA5B9A65273D81EA8CEC0694C,
	TextHandle_DistanceToLine_mC14FF738C5BF2E28AD4E1A3C36E9E7F67C95EAE5,
	TextHandle_GetLineNumber_mED5D753BFDB5DDB5145EEDC829EA8D4EF1D305B1,
	TextHandle_GetLineHeight_mAC48AA68AFCC8EDE5C52EF69941ADAD3B144539E,
	TextHandle_GetLineHeightFromCharacterIndex_mA935CA07C41CEA0C7447033337D05CB2652A1D62,
	TextHandle_GetCharacterHeightFromIndex_mA512F4B21032917955542D5E71D611A55E6F1F0D,
	TextHandle_IsElided_mF2AB6B8A1E01EE5FD2301E9BF77BEE5BC99C4ED5,
	TextHandle_Substring_m3B3C2EC9E167E4C74AB319DE140780560976EDEC,
	TextHandle_IndexOf_mD0CDAB3319422D67356DBC547E91A08882D001B2,
	TextHandle_LastIndexOf_m7A2F2860D56B0C90A73958E02006CAA1D6BEACC4,
	TextHandle_ComputeTextWidth_m0A131F6D0F30382D51B44B8E41158612AD13B602,
	TextHandle_ComputeTextHeight_m7E5F0EB7DD5630BCF519AFE6B194CFF37E421961,
	TextHandle_UpdatePreferredValues_m16C579932E755BC3FD8D82085F75EC011A44AD59,
	TextHandle_Update_mF77E9498589B5348C2EE27EFF866505974E5DA89,
	TextHandle_Update_m9A43DC731089132219B99648DD2904ADCA37C477,
	TextHandle__cctor_m3EFFF534A8E9459492960B615C91F18081422439,
	TextInfo__ctor_m241E24715CC5F6293DC90A4D25884548BAD0D602,
	TextInfo_Clear_m60412774208F9D920707448E71E89C99233D9128,
	TextInfo_ClearMeshInfo_mCA598F01C7F302CFCD0F508E2DBF072E66CA74F3,
	TextInfo_ClearLineInfo_m986C886D34A324C8C4D30F9D8EF24AC242A10AD7,
	TextInfo_ClearPageInfo_m57DE207346C5245799E50F8A57B56B65665B7430,
	NULL,
	NULL,
	TextInfo__cctor_m0B33CCC02D4B32FB18864B6B7657695E9AEB3F0D,
	CharacterElement_get_Unicode_m6D73AADD73043A87CF7985E0980D532AC2DA2505,
	CharacterElement_set_Unicode_mC526431F252FF7D6DAFB5C75CBA021DF59FF2059,
	CharacterElement__ctor_mD64E289D3694F7029402D78A091B92D47E6437E6,
	MarkupAttribute_get_NameHashCode_mA61571031D77679B70AA0C04CFC7FC0E3511EC16,
	MarkupAttribute_set_NameHashCode_mD4BCF8C3E1B9F77D09DCD9A688DE73BC6204F75F,
	MarkupAttribute_get_ValueHashCode_mAA11DFA63F114CEC146E1260A0C0BB05E4EF277C,
	MarkupAttribute_set_ValueHashCode_mDBB10FE8FD648CB31058DDEA359FA743C0ED9D40,
	MarkupAttribute_get_ValueStartIndex_m843094D485ACB571F8C9311403A6E5B7C0C47BA6,
	MarkupAttribute_set_ValueStartIndex_mD1C3F783C156E93023CF6F31C82F3D1FB56F1E26,
	MarkupAttribute_get_ValueLength_m571F75D978A943093767BCCE5525C1C6B492E34B,
	MarkupAttribute_set_ValueLength_m70EC65BA4ECB6E003C1C7DB8D44B3B2C02BDAB74,
	MarkupElement_get_NameHashCode_mB0386E3C4639B814EB50FD3965A7CCAF0BE12A31,
	MarkupElement_set_NameHashCode_m7773B98592AD4BE2C798422E9B71E833D1BC9E34,
	MarkupElement_get_ValueHashCode_m0F5E80360F28F8B4935DFD78627BF10B575D8D17,
	MarkupElement_set_ValueHashCode_m9F7B5802A7058EC98E5DE428ED47F79D8F7400D3,
	MarkupElement_get_ValueStartIndex_mA19AD621FBBF86504B7EF3EC667A6C72C5CCE34A,
	MarkupElement_set_ValueStartIndex_m2DACFB0BEDB889BF38E60EC98FE35D352FE1C95A,
	MarkupElement_get_ValueLength_mF07DBC72C14506D62FBCA38138BB6ECFF9CA2D80,
	MarkupElement_set_ValueLength_mC1B415F03848B3659951AB897F130D4CC9F27706,
	MarkupElement_get_Attributes_mAB26F51783D56866912A7A0C4F542663AF7E2578,
	MarkupElement_set_Attributes_m91F406840212058BD264E4E263AD096C06A6180D,
	MarkupElement__ctor_mF044727F3A3C69EC17B9A4071D0D91AB5567C4E3,
	FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0,
	FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB,
	FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TextResourceManager_AddFontAsset_mAAD97609F00F02146B283982E5DE667EB7A692EA,
	TextResourceManager_RemoveFontAsset_mF3A111A8A89A93A37DEF1EF21408415424D09D5D,
	TextResourceManager_TryGetFontAssetByName_m8FB59092D27AC9EFFA157902191C2A373673F2AF,
	TextResourceManager_TryGetFontAssetByFamilyName_mE3AFAC44F4A7EA104970E87A8FAB47D644B5B573,
	TextResourceManager_RebuildFontAssetCache_m47EC02B2357F83B86F4C151158E031E801A44845,
	TextResourceManager__ctor_m4F2221570A4BBE3A5CDB819F80ADB99B904F63E3,
	TextResourceManager__cctor_m54E40FCFB355A0C90B56366B057F307F75832868,
	FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5,
	TextSettings_get_version_mB5B7C3F04FFC8DCDFCFF63CFFF7612B768E267CB,
	TextSettings_set_version_m7BEE644EEDD77892C295170F9B2B68BC45C7C6CF,
	TextSettings_get_defaultFontAsset_mC6280464BFEE081DB23243BB94E49C72A0885A1F,
	TextSettings_set_defaultFontAsset_mC0137D9AEB9EE7A9E1B3D7C499414EDD217AF3BA,
	TextSettings_get_defaultFontAssetPath_mF7B1713753CFAE048C745C3572332CE18CD51D3B,
	TextSettings_set_defaultFontAssetPath_m35111382F9C2E3C76FA6B77BABEE8149BDDCC7ED,
	TextSettings_get_fallbackFontAssets_m332526E834C994425141A58C968FD40320573F98,
	TextSettings_set_fallbackFontAssets_mEBEC769B2FF339BDB44D72CB48F0010577889772,
	TextSettings_get_matchMaterialPreset_m4675979547AE4C83E680260EAE5ACBC4FAC53B87,
	TextSettings_set_matchMaterialPreset_mD5A3DDE2D93FE2872C869A2AE0B0C984CF5BC12C,
	TextSettings_get_missingCharacterUnicode_mA707E5E6633633BBB3BAFB96B97A5A995100F3F3,
	TextSettings_set_missingCharacterUnicode_mE3E3E2F902BE89B9FE78F03D3B8C508583D1CDA3,
	TextSettings_get_clearDynamicDataOnBuild_m0C130F53F106D77250F38239FB73DCB97299FB79,
	TextSettings_set_clearDynamicDataOnBuild_m86B73E68953D42C6AE4210BEA5D678768155A1AD,
	TextSettings_get_defaultSpriteAsset_m8FA900F9747B7ADBCD2A2F612E7D977DB58D6445,
	TextSettings_set_defaultSpriteAsset_m6083C46D0B3AFB6816363CF2564D2DB2D1ECFEB9,
	TextSettings_get_defaultSpriteAssetPath_mF57875222B7FACC9B69369305EEEB53FFE7986E0,
	TextSettings_set_defaultSpriteAssetPath_m9C49C00A96230DAF828E629C106B229A4552F753,
	TextSettings_get_fallbackSpriteAssets_mC6D74088EBDFDFBACD581FE4AB4EAA447ED242AD,
	TextSettings_set_fallbackSpriteAssets_mB28744780B58AE04BA7F1275508FF7DEE4E485E5,
	TextSettings_get_missingSpriteCharacterUnicode_m496D85B0D88C91F2A5CC9929C233EAA84831523E,
	TextSettings_set_missingSpriteCharacterUnicode_mFE1AFD04C4AA793559E0A157C9E5472E3C3F6701,
	TextSettings_get_defaultStyleSheet_mDA420960556C00405FA66CBD2DA36807F8F4B4F2,
	TextSettings_set_defaultStyleSheet_m2867A0B047E623E4485F10EE717FF0B46BAAECEE,
	TextSettings_get_styleSheetsResourcePath_mE65A055D1C99CA2048BE6F246258EF262E43C4E6,
	TextSettings_set_styleSheetsResourcePath_m19A5C893047D78B43AC5EB43FB9EA79792464C05,
	TextSettings_get_defaultColorGradientPresetsPath_m1571454580E1F0DC859D3F201BB1F6355A5DBB8E,
	TextSettings_set_defaultColorGradientPresetsPath_m951D5A7AE71C87BFA5B9B8CE8EA2F78A474FE01D,
	TextSettings_get_lineBreakingRules_m96E2C32D4F08309D904B0BCD83CEBE8CD6716A04,
	TextSettings_set_lineBreakingRules_mB1174FE0F923443130A3D4686C119EDB0430992D,
	TextSettings_get_useModernHangulLineBreakingRules_mC5DCBAD1DD897594D020EA3B52D1AE2189251E79,
	TextSettings_set_useModernHangulLineBreakingRules_m52082AD9E6D7C4C8C50821282A2B94BEDCAC3FA1,
	TextSettings_get_displayWarnings_m3CA9FCB44B30CC06F54CD3716D68285FF844DF83,
	TextSettings_set_displayWarnings_m08373EE3900E911B4021CBF8AF915DA69856CC20,
	TextSettings_OnEnable_mBFC6BA8BA147B68E9FB956B2D496A2E8C2972A13,
	TextSettings_InitializeFontReferenceLookup_m34B5FB3D61296A7620196398D267812EEFCE0B04,
	TextSettings_GetCachedFontAssetInternal_m9308E0DDEF8F6D8ACA17DAADB43745C860788202,
	TextSettings__ctor_m860D28B10258792A195E1C6391479E16D04CA8BF,
	FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5,
	TextShaderUtilities_get_ShaderRef_MobileSDF_m9014F695545914ADF74FFD8A461B1DD2590A163B,
	TextShaderUtilities_get_ShaderRef_MobileBitmap_m54CAA31B3B374C0517BF356C80B34F169C89F649,
	TextShaderUtilities_get_ShaderRef_Sprite_mEB8685333A53464F71519FA69438381EABEFAA02,
	TextShaderUtilities__cctor_m5E3AAC923C6500CFCB7F775610B085AD43CDE4EE,
	TextShaderUtilities_GetShaderPropertyIDs_mBA841B8CACE147593703FF6E7E2804FF2017DE52,
	TextShaderUtilities_UpdateShaderRatios_m5DFBADF0AABA261FB14F1431E82340B13D38F110,
	TextShaderUtilities_GetFontExtent_mEDE4E145CA01D28FCDF6AAF2A4DF9E2750B5BE29,
	TextShaderUtilities_IsMaskingEnabled_m5613AC55B82463CB01B7983743EF5A4518490331,
	TextShaderUtilities_GetPadding_mB8AB51D48DC021C3446D30408B4515B16E3BFA5F,
	TextShaderUtilities_ComputePaddingForProperties_m96574D625A310B3B6F584D5AD6DA1A517D544420,
	TextShaderUtilities_GetPadding_m0712CFC6137761D140C858D54739E53E3BBF871E,
	TextStyle_get_NormalStyle_mA0E83C0E845ACFA40171BD84744D5BF77110A196,
	TextStyle_get_name_mF4794188414F2041B408E23ACF664A75A35FFE41,
	TextStyle_set_name_m71685FED89C86374B7E028A8818E8954E7F52F53,
	TextStyle_get_hashCode_mA1F4D3630B6AE71C2A31F94B7054C28BDD96084F,
	TextStyle_set_hashCode_m53BBE925958A34BCF4B31820AEAE907942C34A23,
	TextStyle_get_styleOpeningDefinition_m4494A97ED19F7108DEBA9E2DF778FCD438DF1B19,
	TextStyle_get_styleClosingDefinition_m94E7D7B2730DF74ADF9CDB9ECA7DE9CD08B1B7B1,
	TextStyle_get_styleOpeningTagArray_m123040451C694F92BC9700969B4682EC4BACF8BE,
	TextStyle_get_styleClosingTagArray_m0B50B87D1CCDC30647772E268433096209D7BC42,
	TextStyle__ctor_mF1C354C192665DC3942DBDC0B7EECDBD653FF684,
	TextStyle_RefreshStyle_m2D0771408F06C24EF303749ED8E656C800575BDB,
	TextStyleSheet_get_styles_m7916C62D70AA430314D85EA5B5A778FFAE1544DB,
	TextStyleSheet_Reset_m4C7EA0DF62767E14E3407398D533F1499647038B,
	TextStyleSheet_GetStyle_m648B766D750E1B37DD126918BF7EB22DDFD21D29,
	TextStyleSheet_GetStyle_mF87D8108EEF60C3FC32A2B01D5C1C23D3F22A4D1,
	TextStyleSheet_RefreshStyles_m92525DE6F7951D03D906E18EC89349AA2794AEC5,
	TextStyleSheet_LoadStyleDictionaryInternal_m482E4CDF0CD2ED291F85869E141EEB5EC21F6D81,
	TextStyleSheet__ctor_m1A0B93B55B12CE15FFF1053443179BD010022B75,
	NULL,
	NULL,
	TextUtilities_NextPowerOfTwo_mFB485658568FD671B181760B887496344FF747BC,
	TextUtilities_ToLowerFast_m3B09EDC3C43F0CB2EEA93FE2CD138C8543CC64FA,
	TextUtilities_ToUpperFast_m7E98D5D17030A387012E262648346523DCC3AA0B,
	TextUtilities_ToUpperASCIIFast_m48A8B61739F9D5E8CB46B108746277DAF38AB58C,
	TextUtilities_ToLowerASCIIFast_m0E9D3564CC36033C23F08F4A536B353AC2ADFE2C,
	TextUtilities_GetHashCodeCaseSensitive_mDCEA10A7A94A35EBF812FBA784332AA1950D560E,
	TextUtilities_GetHashCodeCaseInSensitive_m3241BDD3A6F82A061C21BF5D347B690D732F5B59,
	TextUtilities_GetSimpleHashCodeLowercase_m51788E1833BA78F7EA46551F66D3CD8B01C6D482,
	TextUtilities_ConvertToUTF32_m2392833CB12C6F2A3B45FC1DE354752B3C3455A8,
	TextUtilities_ReadUTF16_m754B7E1D790693FE31700FB47F397817FCA5FF85,
	TextUtilities_ReadUTF32_m9B4B4ACCA0347158BD4C0A6E6F232CF8A9AA848A,
	TextUtilities_HexToInt_m8922BAF0B84C39996473D4BCBADFE6D13490AF66,
	TextUtilities_StringHexToInt_mF786078042805D5DF5F0A52A27021064EE56D063,
	TextUtilities_UintToString_m7C0ECB6D2370EC4275FE0E70FB979CADA55A6216,
	UnicodeLineBreakingRules_get_lineBreakingRules_m87E179304E3E068A4A113C2B574CB2FA6828E9DA,
	UnicodeLineBreakingRules_get_leadingCharacters_m13BA1ABC3DF5F777755065DA2047DD6DCBEFA3C9,
	UnicodeLineBreakingRules_get_followingCharacters_m6D71A30A686E94B6D2CA0599D6B85B8B8FB0602A,
	UnicodeLineBreakingRules_get_leadingCharactersLookup_m1DAC015D7E37112EAE0437E6472AEA0719DFF3DC,
	UnicodeLineBreakingRules_set_leadingCharactersLookup_mDCBE493A3C78E6FCB666200EC577DF0EFD20D8E0,
	UnicodeLineBreakingRules_get_followingCharactersLookup_m5510A21873DC5DA66F4A2DFA4C26A5EFAD494D8B,
	UnicodeLineBreakingRules_set_followingCharactersLookup_m126B4EF60B558C6A30C3E08C00D4BF3BC41EB88E,
	UnicodeLineBreakingRules_get_useModernHangulLineBreakingRules_mD86D283CE7BA23A0174B9227A7BD915D3D9FD464,
	UnicodeLineBreakingRules_set_useModernHangulLineBreakingRules_m53CB9AC0EAFD25ABB98B907EC8F908119E9FAC5D,
	UnicodeLineBreakingRules_LoadLineBreakingRules_m4686111E39B00E27AA6AD88762793EEFCCC14A75,
	UnicodeLineBreakingRules_LoadLineBreakingRules_m8B5320C512BB7919AF4AA01650690F64875A7485,
	UnicodeLineBreakingRules_GetCharacters_m93663235F54D32D801E67994714B79759D2F4137,
	UnicodeLineBreakingRules__ctor_mD94779B4996B56EA84C847EC4DD287AB1A8ADE85,
};
extern void FontAssetCreationEditorSettings__ctor_m580CDC10BB7CC670BAB67945F88C20397EC29D2B_AdjustorThunk (void);
extern void Extents__ctor_m29242B7ECE336324673F4E94B869F0486922BB38_AdjustorThunk (void);
extern void Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC_AdjustorThunk (void);
extern void LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F_AdjustorThunk (void);
extern void LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8_AdjustorThunk (void);
extern void LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB_AdjustorThunk (void);
extern void MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A_AdjustorThunk (void);
extern void MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61_AdjustorThunk (void);
extern void MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC_AdjustorThunk (void);
extern void MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475_AdjustorThunk (void);
extern void MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830_AdjustorThunk (void);
extern void MeshInfo_ClearUnusedVertices_m8D57192C6582A8E8EC3A2E133B057896ECE5FC2E_AdjustorThunk (void);
extern void MeshInfo_ClearUnusedVertices_mA71B4EBAF0308EEA00061C57244BCB2EDE2D557F_AdjustorThunk (void);
extern void MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1_AdjustorThunk (void);
extern void MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F_AdjustorThunk (void);
extern void TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985_AdjustorThunk (void);
extern void TextElementInfo_ToStringTest_m7E0775478741E60973A13C91273ABFB9105E0AD4_AdjustorThunk (void);
extern void SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969_AdjustorThunk (void);
extern void MeshExtents__ctor_m09C3052557EE349D040C72B6572D52D48645F2D8_AdjustorThunk (void);
extern void MeshExtents_ToString_mC78F6BC3525EAD13F90B39A7E1C7B3301598E9B0_AdjustorThunk (void);
extern void TextBackingContainer_get_Text_mB9B62AE587962B01FF21BFB72996AE40F6D36EFD_AdjustorThunk (void);
extern void TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7_AdjustorThunk (void);
extern void TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416_AdjustorThunk (void);
extern void TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97_AdjustorThunk (void);
extern void TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612_AdjustorThunk (void);
extern void TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D_AdjustorThunk (void);
extern void TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7_AdjustorThunk (void);
extern void TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698_AdjustorThunk (void);
extern void CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29_AdjustorThunk (void);
extern void Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82_AdjustorThunk (void);
extern void Offset_set_left_mBA08D34537453D783769DC94796C8ACC560E5D28_AdjustorThunk (void);
extern void Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB_AdjustorThunk (void);
extern void Offset_set_right_mA7D0F7DD84DE12F92B800449EA54C2DFC8AF5F2A_AdjustorThunk (void);
extern void Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4_AdjustorThunk (void);
extern void Offset_set_top_m3B5760ADA3A3A5550CEF1B6BBE7CE3E95C6AED11_AdjustorThunk (void);
extern void Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C_AdjustorThunk (void);
extern void Offset_set_bottom_mE54667D478772330F8C34F55509178EB3958D103_AdjustorThunk (void);
extern void Offset_get_horizontal_m1266C1DC1F818B19D4084D6E120A0CA38E18DC25_AdjustorThunk (void);
extern void Offset_set_horizontal_mBCE3E8C5FA4EF01EC9196A76450BBB4C6E7ED68E_AdjustorThunk (void);
extern void Offset_get_vertical_mAC6BAE572DC64A052407108CF726DC0210D72DFF_AdjustorThunk (void);
extern void Offset_set_vertical_mC01378B8286D0293946D2237205BD45256392055_AdjustorThunk (void);
extern void Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7_AdjustorThunk (void);
extern void Offset__ctor_m0F9E756FCB8AF792C3EDCC24673670E6ECA605DE_AdjustorThunk (void);
extern void Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038_AdjustorThunk (void);
extern void Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D_AdjustorThunk (void);
extern void Offset_Equals_m3C88E9583BCC9A281BD1CFEAAE896F532F9EC0C7_AdjustorThunk (void);
extern void HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F_AdjustorThunk (void);
extern void HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA_AdjustorThunk (void);
extern void HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849_AdjustorThunk (void);
extern void HighlightState_Equals_m8F74F12721E91EA8928A098BC6815E253478D8BE_AdjustorThunk (void);
extern void CharacterElement_get_Unicode_m6D73AADD73043A87CF7985E0980D532AC2DA2505_AdjustorThunk (void);
extern void CharacterElement_set_Unicode_mC526431F252FF7D6DAFB5C75CBA021DF59FF2059_AdjustorThunk (void);
extern void CharacterElement__ctor_mD64E289D3694F7029402D78A091B92D47E6437E6_AdjustorThunk (void);
extern void MarkupAttribute_get_NameHashCode_mA61571031D77679B70AA0C04CFC7FC0E3511EC16_AdjustorThunk (void);
extern void MarkupAttribute_set_NameHashCode_mD4BCF8C3E1B9F77D09DCD9A688DE73BC6204F75F_AdjustorThunk (void);
extern void MarkupAttribute_get_ValueHashCode_mAA11DFA63F114CEC146E1260A0C0BB05E4EF277C_AdjustorThunk (void);
extern void MarkupAttribute_set_ValueHashCode_mDBB10FE8FD648CB31058DDEA359FA743C0ED9D40_AdjustorThunk (void);
extern void MarkupAttribute_get_ValueStartIndex_m843094D485ACB571F8C9311403A6E5B7C0C47BA6_AdjustorThunk (void);
extern void MarkupAttribute_set_ValueStartIndex_mD1C3F783C156E93023CF6F31C82F3D1FB56F1E26_AdjustorThunk (void);
extern void MarkupAttribute_get_ValueLength_m571F75D978A943093767BCCE5525C1C6B492E34B_AdjustorThunk (void);
extern void MarkupAttribute_set_ValueLength_m70EC65BA4ECB6E003C1C7DB8D44B3B2C02BDAB74_AdjustorThunk (void);
extern void MarkupElement_get_NameHashCode_mB0386E3C4639B814EB50FD3965A7CCAF0BE12A31_AdjustorThunk (void);
extern void MarkupElement_set_NameHashCode_m7773B98592AD4BE2C798422E9B71E833D1BC9E34_AdjustorThunk (void);
extern void MarkupElement_get_ValueHashCode_m0F5E80360F28F8B4935DFD78627BF10B575D8D17_AdjustorThunk (void);
extern void MarkupElement_set_ValueHashCode_m9F7B5802A7058EC98E5DE428ED47F79D8F7400D3_AdjustorThunk (void);
extern void MarkupElement_get_ValueStartIndex_mA19AD621FBBF86504B7EF3EC667A6C72C5CCE34A_AdjustorThunk (void);
extern void MarkupElement_set_ValueStartIndex_m2DACFB0BEDB889BF38E60EC98FE35D352FE1C95A_AdjustorThunk (void);
extern void MarkupElement_get_ValueLength_mF07DBC72C14506D62FBCA38138BB6ECFF9CA2D80_AdjustorThunk (void);
extern void MarkupElement_set_ValueLength_mC1B415F03848B3659951AB897F130D4CC9F27706_AdjustorThunk (void);
extern void MarkupElement_get_Attributes_mAB26F51783D56866912A7A0C4F542663AF7E2578_AdjustorThunk (void);
extern void MarkupElement_set_Attributes_m91F406840212058BD264E4E263AD096C06A6180D_AdjustorThunk (void);
extern void MarkupElement__ctor_mF044727F3A3C69EC17B9A4071D0D91AB5567C4E3_AdjustorThunk (void);
extern void FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0_AdjustorThunk (void);
extern void FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB_AdjustorThunk (void);
extern void FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB_AdjustorThunk (void);
extern void FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5_AdjustorThunk (void);
extern void FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[77] = 
{
	{ 0x0600001A, FontAssetCreationEditorSettings__ctor_m580CDC10BB7CC670BAB67945F88C20397EC29D2B_AdjustorThunk },
	{ 0x060000B2, Extents__ctor_m29242B7ECE336324673F4E94B869F0486922BB38_AdjustorThunk },
	{ 0x060000B3, Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC_AdjustorThunk },
	{ 0x060000B4, LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F_AdjustorThunk },
	{ 0x060000B5, LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8_AdjustorThunk },
	{ 0x060000B6, LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB_AdjustorThunk },
	{ 0x060000BB, MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A_AdjustorThunk },
	{ 0x060000D5, MeshInfo__ctor_mCC2410C5590BEA974468F4CECFA874BE966CDE61_AdjustorThunk },
	{ 0x060000D6, MeshInfo_ResizeMeshInfo_mE411FE40935FB9CFB7C334B3A1F216A98B96F5FC_AdjustorThunk },
	{ 0x060000D7, MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475_AdjustorThunk },
	{ 0x060000D8, MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830_AdjustorThunk },
	{ 0x060000D9, MeshInfo_ClearUnusedVertices_m8D57192C6582A8E8EC3A2E133B057896ECE5FC2E_AdjustorThunk },
	{ 0x060000DA, MeshInfo_ClearUnusedVertices_mA71B4EBAF0308EEA00061C57244BCB2EDE2D557F_AdjustorThunk },
	{ 0x060000DB, MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1_AdjustorThunk },
	{ 0x060000DC, MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F_AdjustorThunk },
	{ 0x0600011D, TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985_AdjustorThunk },
	{ 0x0600011E, TextElementInfo_ToStringTest_m7E0775478741E60973A13C91273ABFB9105E0AD4_AdjustorThunk },
	{ 0x06000160, SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969_AdjustorThunk },
	{ 0x06000161, MeshExtents__ctor_m09C3052557EE349D040C72B6572D52D48645F2D8_AdjustorThunk },
	{ 0x06000162, MeshExtents_ToString_mC78F6BC3525EAD13F90B39A7E1C7B3301598E9B0_AdjustorThunk },
	{ 0x06000163, TextBackingContainer_get_Text_mB9B62AE587962B01FF21BFB72996AE40F6D36EFD_AdjustorThunk },
	{ 0x06000164, TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7_AdjustorThunk },
	{ 0x06000165, TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416_AdjustorThunk },
	{ 0x06000166, TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97_AdjustorThunk },
	{ 0x06000167, TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612_AdjustorThunk },
	{ 0x06000168, TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D_AdjustorThunk },
	{ 0x06000169, TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7_AdjustorThunk },
	{ 0x0600016A, TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698_AdjustorThunk },
	{ 0x0600016B, CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29_AdjustorThunk },
	{ 0x0600016C, Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82_AdjustorThunk },
	{ 0x0600016D, Offset_set_left_mBA08D34537453D783769DC94796C8ACC560E5D28_AdjustorThunk },
	{ 0x0600016E, Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB_AdjustorThunk },
	{ 0x0600016F, Offset_set_right_mA7D0F7DD84DE12F92B800449EA54C2DFC8AF5F2A_AdjustorThunk },
	{ 0x06000170, Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4_AdjustorThunk },
	{ 0x06000171, Offset_set_top_m3B5760ADA3A3A5550CEF1B6BBE7CE3E95C6AED11_AdjustorThunk },
	{ 0x06000172, Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C_AdjustorThunk },
	{ 0x06000173, Offset_set_bottom_mE54667D478772330F8C34F55509178EB3958D103_AdjustorThunk },
	{ 0x06000174, Offset_get_horizontal_m1266C1DC1F818B19D4084D6E120A0CA38E18DC25_AdjustorThunk },
	{ 0x06000175, Offset_set_horizontal_mBCE3E8C5FA4EF01EC9196A76450BBB4C6E7ED68E_AdjustorThunk },
	{ 0x06000176, Offset_get_vertical_mAC6BAE572DC64A052407108CF726DC0210D72DFF_AdjustorThunk },
	{ 0x06000177, Offset_set_vertical_mC01378B8286D0293946D2237205BD45256392055_AdjustorThunk },
	{ 0x06000179, Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7_AdjustorThunk },
	{ 0x0600017A, Offset__ctor_m0F9E756FCB8AF792C3EDCC24673670E6ECA605DE_AdjustorThunk },
	{ 0x0600017E, Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038_AdjustorThunk },
	{ 0x0600017F, Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D_AdjustorThunk },
	{ 0x06000180, Offset_Equals_m3C88E9583BCC9A281BD1CFEAAE896F532F9EC0C7_AdjustorThunk },
	{ 0x06000182, HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F_AdjustorThunk },
	{ 0x06000185, HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA_AdjustorThunk },
	{ 0x06000186, HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849_AdjustorThunk },
	{ 0x06000187, HighlightState_Equals_m8F74F12721E91EA8928A098BC6815E253478D8BE_AdjustorThunk },
	{ 0x060001E4, CharacterElement_get_Unicode_m6D73AADD73043A87CF7985E0980D532AC2DA2505_AdjustorThunk },
	{ 0x060001E5, CharacterElement_set_Unicode_mC526431F252FF7D6DAFB5C75CBA021DF59FF2059_AdjustorThunk },
	{ 0x060001E6, CharacterElement__ctor_mD64E289D3694F7029402D78A091B92D47E6437E6_AdjustorThunk },
	{ 0x060001E7, MarkupAttribute_get_NameHashCode_mA61571031D77679B70AA0C04CFC7FC0E3511EC16_AdjustorThunk },
	{ 0x060001E8, MarkupAttribute_set_NameHashCode_mD4BCF8C3E1B9F77D09DCD9A688DE73BC6204F75F_AdjustorThunk },
	{ 0x060001E9, MarkupAttribute_get_ValueHashCode_mAA11DFA63F114CEC146E1260A0C0BB05E4EF277C_AdjustorThunk },
	{ 0x060001EA, MarkupAttribute_set_ValueHashCode_mDBB10FE8FD648CB31058DDEA359FA743C0ED9D40_AdjustorThunk },
	{ 0x060001EB, MarkupAttribute_get_ValueStartIndex_m843094D485ACB571F8C9311403A6E5B7C0C47BA6_AdjustorThunk },
	{ 0x060001EC, MarkupAttribute_set_ValueStartIndex_mD1C3F783C156E93023CF6F31C82F3D1FB56F1E26_AdjustorThunk },
	{ 0x060001ED, MarkupAttribute_get_ValueLength_m571F75D978A943093767BCCE5525C1C6B492E34B_AdjustorThunk },
	{ 0x060001EE, MarkupAttribute_set_ValueLength_m70EC65BA4ECB6E003C1C7DB8D44B3B2C02BDAB74_AdjustorThunk },
	{ 0x060001EF, MarkupElement_get_NameHashCode_mB0386E3C4639B814EB50FD3965A7CCAF0BE12A31_AdjustorThunk },
	{ 0x060001F0, MarkupElement_set_NameHashCode_m7773B98592AD4BE2C798422E9B71E833D1BC9E34_AdjustorThunk },
	{ 0x060001F1, MarkupElement_get_ValueHashCode_m0F5E80360F28F8B4935DFD78627BF10B575D8D17_AdjustorThunk },
	{ 0x060001F2, MarkupElement_set_ValueHashCode_m9F7B5802A7058EC98E5DE428ED47F79D8F7400D3_AdjustorThunk },
	{ 0x060001F3, MarkupElement_get_ValueStartIndex_mA19AD621FBBF86504B7EF3EC667A6C72C5CCE34A_AdjustorThunk },
	{ 0x060001F4, MarkupElement_set_ValueStartIndex_m2DACFB0BEDB889BF38E60EC98FE35D352FE1C95A_AdjustorThunk },
	{ 0x060001F5, MarkupElement_get_ValueLength_mF07DBC72C14506D62FBCA38138BB6ECFF9CA2D80_AdjustorThunk },
	{ 0x060001F6, MarkupElement_set_ValueLength_mC1B415F03848B3659951AB897F130D4CC9F27706_AdjustorThunk },
	{ 0x060001F7, MarkupElement_get_Attributes_mAB26F51783D56866912A7A0C4F542663AF7E2578_AdjustorThunk },
	{ 0x060001F8, MarkupElement_set_Attributes_m91F406840212058BD264E4E263AD096C06A6180D_AdjustorThunk },
	{ 0x060001F9, MarkupElement__ctor_mF044727F3A3C69EC17B9A4071D0D91AB5567C4E3_AdjustorThunk },
	{ 0x060001FA, FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0_AdjustorThunk },
	{ 0x060001FB, FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB_AdjustorThunk },
	{ 0x060001FC, FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB_AdjustorThunk },
	{ 0x06000215, FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5_AdjustorThunk },
	{ 0x0600023C, FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5_AdjustorThunk },
};
static const int32_t s_InvokerIndices[630] = 
{
	4364,
	2873,
	2158,
	2875,
	7177,
	7177,
	7176,
	7176,
	7387,
	3881,
	3881,
	4364,
	4364,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	91,
	4193,
	3831,
	4250,
	3881,
	4216,
	3852,
	4192,
	3830,
	4216,
	3852,
	4216,
	3852,
	4250,
	3881,
	4250,
	4250,
	3881,
	4250,
	4250,
	4250,
	3881,
	4216,
	4168,
	3807,
	4168,
	3807,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4168,
	3807,
	4168,
	3807,
	6692,
	4786,
	4558,
	8505,
	4651,
	4558,
	4785,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	4364,
	2155,
	2873,
	4216,
	4364,
	4364,
	4364,
	4364,
	3166,
	1651,
	1654,
	1654,
	2315,
	1062,
	3185,
	8505,
	8505,
	3637,
	8887,
	9089,
	8887,
	9089,
	9089,
	2316,
	1629,
	2316,
	1629,
	2341,
	1653,
	1653,
	4364,
	4168,
	4364,
	4364,
	4364,
	3881,
	3881,
	2802,
	0,
	3807,
	3807,
	4364,
	3807,
	3807,
	4364,
	4364,
	9089,
	9089,
	4364,
	3635,
	3635,
	5025,
	5025,
	4805,
	6724,
	6724,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4364,
	4364,
	4364,
	4364,
	9089,
	4364,
	3631,
	3631,
	3633,
	3633,
	3634,
	3634,
	2887,
	4250,
	2053,
	3518,
	4250,
	7631,
	6692,
	7975,
	9089,
	888,
	7261,
	5819,
	5819,
	9031,
	8887,
	3881,
	8887,
	3881,
	7932,
	2739,
	7932,
	2739,
	7932,
	2739,
	3185,
	3185,
	7223,
	2293,
	7223,
	2293,
	7223,
	2293,
	7223,
	2293,
	4364,
	3852,
	3852,
	3807,
	4364,
	2729,
	3852,
	3852,
	2734,
	9089,
	4192,
	3830,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4364,
	4364,
	3415,
	3447,
	3419,
	5971,
	5971,
	5971,
	5349,
	5935,
	5935,
	4364,
	4364,
	4364,
	4364,
	9089,
	4364,
	3635,
	3635,
	4250,
	3881,
	4364,
	2873,
	2158,
	4364,
	1014,
	665,
	4250,
	3881,
	4216,
	4216,
	3852,
	4250,
	3881,
	4216,
	3852,
	4364,
	4364,
	3811,
	1319,
	9089,
	4168,
	4350,
	3973,
	4250,
	3881,
	4250,
	3881,
	4350,
	3973,
	4298,
	3928,
	4364,
	4250,
	4250,
	9089,
	7914,
	7914,
	7914,
	7914,
	6985,
	8868,
	8887,
	8887,
	9089,
	9089,
	7914,
	9089,
	3185,
	3185,
	4216,
	7261,
	7261,
	4250,
	4364,
	9031,
	7975,
	7836,
	6052,
	7738,
	7738,
	7837,
	4168,
	3807,
	8993,
	8887,
	8887,
	2802,
	2802,
	1293,
	2414,
	699,
	1007,
	1940,
	155,
	1026,
	7914,
	1730,
	499,
	2834,
	3881,
	3881,
	3881,
	2572,
	2572,
	2615,
	518,
	3881,
	2053,
	2053,
	2053,
	3881,
	40,
	1529,
	4364,
	4364,
	2798,
	1529,
	504,
	3881,
	2796,
	2887,
	4250,
	4250,
	4216,
	4216,
	3852,
	3632,
	2749,
	3852,
	3852,
	2749,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	4298,
	3928,
	9032,
	1517,
	2850,
	7267,
	7267,
	7679,
	4216,
	3185,
	3186,
	9089,
	2701,
	7218,
	7218,
	4216,
	3185,
	3161,
	7306,
	7389,
	6425,
	8790,
	6783,
	6025,
	6824,
	0,
	0,
	6311,
	6311,
	5106,
	5106,
	4599,
	5456,
	4599,
	5456,
	5106,
	5456,
	5076,
	7626,
	6483,
	6483,
	7812,
	7816,
	7812,
	7816,
	6483,
	6483,
	6109,
	6109,
	6119,
	7932,
	8395,
	8395,
	7820,
	7502,
	7488,
	8776,
	8791,
	8776,
	5813,
	8216,
	8242,
	7381,
	8262,
	8205,
	7335,
	7335,
	8242,
	8242,
	8242,
	9089,
	4364,
	4250,
	4168,
	9031,
	4364,
	4168,
	2613,
	1869,
	2449,
	3415,
	3415,
	3415,
	3451,
	1735,
	2456,
	5233,
	6790,
	3415,
	3581,
	3581,
	3581,
	4168,
	2507,
	2446,
	2446,
	3584,
	3584,
	3881,
	3518,
	3518,
	9089,
	4364,
	4364,
	3807,
	4364,
	4364,
	0,
	0,
	9089,
	4350,
	3973,
	3881,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4216,
	3852,
	4250,
	3881,
	1973,
	4364,
	3166,
	3166,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8887,
	8887,
	7223,
	6329,
	9089,
	4364,
	9089,
	1348,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4216,
	3852,
	4168,
	3807,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4350,
	3973,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4168,
	3807,
	4364,
	4364,
	3518,
	4364,
	2802,
	9031,
	9031,
	9031,
	9089,
	9089,
	8887,
	8853,
	8220,
	6781,
	8626,
	6781,
	9031,
	4250,
	3881,
	4216,
	3852,
	4250,
	4250,
	4250,
	4250,
	2084,
	4364,
	4250,
	4364,
	3515,
	3518,
	4364,
	4364,
	4364,
	0,
	0,
	8395,
	8776,
	8776,
	8791,
	8791,
	8399,
	8399,
	8787,
	7820,
	7812,
	7812,
	8790,
	8787,
	8505,
	4250,
	4250,
	4250,
	4250,
	3881,
	4250,
	3881,
	4168,
	3807,
	4364,
	2802,
	8505,
	4364,
};
static const Il2CppTokenRangePair s_rgctxIndices[11] = 
{
	{ 0x02000005, { 0, 19 } },
	{ 0x02000006, { 19, 20 } },
	{ 0x02000007, { 39, 21 } },
	{ 0x02000048, { 75, 9 } },
	{ 0x0600008A, { 60, 7 } },
	{ 0x0600018F, { 67, 2 } },
	{ 0x06000190, { 69, 2 } },
	{ 0x060001E1, { 71, 2 } },
	{ 0x060001E2, { 73, 2 } },
	{ 0x0600025A, { 84, 2 } },
	{ 0x0600025B, { 86, 2 } },
};
extern const uint32_t g_rgctx_FastAction_1_t1AEE0F0AABEE7404C4C408917291668FB716425A;
extern const uint32_t g_rgctx_Dictionary_2_tAEB17B50990020D7888CB7BC8640B14B14CCC302;
extern const uint32_t g_rgctx_Action_1_tBBD016213FB8CB6AF69308DB050D148EE7E6DF1C;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_m681CECA194150636ADC330C8DCE6F2E1C90F4446;
extern const uint32_t g_rgctx_LinkedList_1_t9CEF548CEC479FB27EAEEB98F0FFDA705562A403;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_m14D7B2A1F6D3D73ED9C2562CFAE672D660F68D93;
extern const uint32_t g_rgctx_LinkedListNode_1_tB7599F2A0784F782E15B01E99D1C617682625608;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_mD9A8D26604EEA796FF2C57B00459FADC4DFB2E98;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m543BE8B8AD6450838820C791A4947FFACABB9FDA;
extern const uint32_t g_rgctx_LinkedListNode_1U26_t6DD30519D2A3456B64B4805D166A23F5089A5218;
extern const uint32_t g_rgctx_Dictionary_2_Remove_mDD0B5A9F8F7A136CA5DB4843F3D5DB02AD3FED87;
extern const uint32_t g_rgctx_LinkedList_1_Remove_mE2B1D75F3BCEB8EBBECDFE32082676881F6E24E8;
extern const uint32_t g_rgctx_LinkedList_1_get_First_m2A6A26E68AA7C4E2A44D6D5A32A3EF299668D55C;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_mE6D843C44C2F0D114A1C1343336B3B8FC3A19DF0;
extern const uint32_t g_rgctx_A_t4E963D9C15FF3E9AB1408E60BFC62A134C1FFC06;
extern const uint32_t g_rgctx_Action_1_Invoke_m7D44F35106B9A85A9AE189517E1ADB864EDCA19E;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mDCD93480C3841C757AD1CF91846ECA590BFB1FDC;
extern const uint32_t g_rgctx_LinkedList_1__ctor_m021247779D7C36A587EE76E7E84D40BDA31D5E0E;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mA33E63AFB65BC9E467B8BF7A677C64B4D8E0773B;
extern const uint32_t g_rgctx_FastAction_2_t56AD8F7C1E0FCEFECC175425B3463D506B2FA568;
extern const uint32_t g_rgctx_Dictionary_2_t8CC3267E19AC648E769CF2C202B77DFBF4E2767B;
extern const uint32_t g_rgctx_Action_2_tF5943A3D7C5CE934B355A05B9009D66482F831E0;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_mE594D9227F07C4082946A5895F1BF4EBC50043D4;
extern const uint32_t g_rgctx_LinkedList_1_t338546D10879B9307679AC24AF0F54F1C8C6EE34;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_m5C1404B6635DD24573899467C355381DC3AA28AF;
extern const uint32_t g_rgctx_LinkedListNode_1_t1864D9A2CBFC1623AFE0F79E74976A7FCADA3A1A;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_mB8D02F9C31FC229346EAF03C9578244BB3E62194;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_mAB90E3B53A6AE8DA28CB4C591AC3FE4110C8B78A;
extern const uint32_t g_rgctx_LinkedListNode_1U26_tCF3F4AC0E5BE7EE179F5AA571AF811F09083032D;
extern const uint32_t g_rgctx_Dictionary_2_Remove_m0BE9419BCB3F0DA697A73685A7ECFC279AB59A85;
extern const uint32_t g_rgctx_LinkedList_1_Remove_m45C30C1702A75B7C76C34D961C2D298F980DCC25;
extern const uint32_t g_rgctx_LinkedList_1_get_First_m7E96B595CD83F59B2FF559A674EFF09A9FB68090;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_m5C2DB4AC8057756DD297D734501D2899DC756E1B;
extern const uint32_t g_rgctx_A_tBD2E164CC30412A91CBFC391AA372C6493D30445;
extern const uint32_t g_rgctx_B_t6C2D3267AFFE177F95438677193B36775F8DF1C6;
extern const uint32_t g_rgctx_Action_2_Invoke_m335EE8932D069809AC784E42D93AE07375FA5AAC;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mE0701B9C68739AE8AE6E4A352F32F50019C4FCB1;
extern const uint32_t g_rgctx_LinkedList_1__ctor_m72A68E0BBEE55D44CBB7EE1D9079616499D15DF3;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m3A05DA54DC434F29C0C12E1A520C4434B9744AE9;
extern const uint32_t g_rgctx_FastAction_3_tA68636C4F81D8339F713A4430D68BCDDCC40EDF6;
extern const uint32_t g_rgctx_Dictionary_2_tF5886DEEA57665A6FF3E8518E295A3655F128908;
extern const uint32_t g_rgctx_Action_3_t2CA201DEA8178441EED1ECCEBDF2A41E4C1F49B1;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_m9FBCE8820591A5FEF7520678F99FE0C0AC317303;
extern const uint32_t g_rgctx_LinkedList_1_tBF12ED575D13E6570DEBFB1EA512BE0D4EDFE1CE;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_m699573BB4162D0842793454571AEAC390AC7BDF0;
extern const uint32_t g_rgctx_LinkedListNode_1_tFE6FAC2382573B70AB10E7EB5FB1B4BA6333D8BA;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_mB820A3459B33E817C92E3F96E5AD9B8B2B0FF7E7;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m54F3C9E3FFEE9F038EDB0B81C2C2673B5024A09B;
extern const uint32_t g_rgctx_LinkedListNode_1U26_t2105CD7DC1B9DB0CEE53BB4742875D40E398F529;
extern const uint32_t g_rgctx_Dictionary_2_Remove_mD24F5A2D03262F760390A66DE5309E332A3520DE;
extern const uint32_t g_rgctx_LinkedList_1_Remove_mAD47F484F8DE1C87538250A23A70494AE86C15B7;
extern const uint32_t g_rgctx_LinkedList_1_get_First_mF89B11FBE0761B2A3171FF190455857C4FD08624;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_m479267486F1297CBA6873A66ECB15BA018AF2F88;
extern const uint32_t g_rgctx_A_t77DC486495296BDC28F3B734F3411255DCBA8419;
extern const uint32_t g_rgctx_B_t1C87FE2C1B699FB5D8B4B5C57BF7D5271126FF85;
extern const uint32_t g_rgctx_C_t55A8D90479E752A4498DF6515327921CF0041A0B;
extern const uint32_t g_rgctx_Action_3_Invoke_m5A3EBBFD93AEA8685C851B7E37EE1AE5E451BF8D;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mCFB2D295A4D3D39AEEC580340B8B172FA1A50CD7;
extern const uint32_t g_rgctx_LinkedList_1__ctor_mAABCB69DA28E906DE762115AF4FBB5DA7865D66F;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mD2210DEFBDD439204C1964C7C3AB595D43EA3CAA;
extern const uint32_t g_rgctx_List_1_tA286387994A1339C2CF844BE5728698C1E998AFA;
extern const uint32_t g_rgctx_List_1_get_Count_m94023DC2882A00FF3E2A87B830A03C35E07FAED7;
extern const uint32_t g_rgctx_TU5BU5DU26_t624F2E5B72A639CE2DFB1A28C4AE0422E25DC7FC;
extern const uint32_t g_rgctx_TU5BU5D_tDFCEC986C8C5C9107923D8936ADFFA31D6C82631;
extern const uint32_t g_rgctx_Array_Resize_TisT_tC526A9C1F5DF2DFE47235494265CDBAC2F3C7084_mE23A43EFBF8FEFA791C6C9975CB651227B8576A3;
extern const uint32_t g_rgctx_List_1_get_Item_mE8A0A11819707997B5CBE996B1D1A5498DF9FF44;
extern const uint32_t g_rgctx_T_tC526A9C1F5DF2DFE47235494265CDBAC2F3C7084;
extern const uint32_t g_rgctx_TU5BU5DU26_t34638B2E1F7E84A13C103ECA102E8228E5495A8F;
extern const uint32_t g_rgctx_Array_Resize_TisT_tB4C007A2682984D0FC6913C0705D947ACCDD2686_mB7195CE4A4808328845CB836896D64A709E7B865;
extern const uint32_t g_rgctx_TU5BU5DU26_t70819A0A282144E9916C49712A6DC840F9A74D7F;
extern const uint32_t g_rgctx_Array_Resize_TisT_t21B35BF972C1E5C4F52B0E073B10F9EE3CC93BD7_m08A1BE0AED907371A0B1811BF2028B1DF7CEB065;
extern const uint32_t g_rgctx_TU5BU5DU26_t70904DE589CEA82A52949E06395973C9DCED555A;
extern const uint32_t g_rgctx_Array_Resize_TisT_t17405AE98647F674620A8588A2A38EC17385AEF3_m819D8D61F3D2D33140D1CBF31E6D9A4540919072;
extern const uint32_t g_rgctx_TU5BU5DU26_tB01B5E1E9375748D28EAB2E7C309F3263CEF4E49;
extern const uint32_t g_rgctx_Array_Resize_TisT_tA5CF78E77248D29C8555253A5B104C010B149A3D_m66CF7004011C393E0AFA739A42295579B02C917F;
extern const uint32_t g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43;
extern const uint32_t g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993;
extern const uint32_t g_rgctx_T_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5;
extern const uint32_t g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43;
extern const uint32_t g_rgctx_TextProcessingStack_1U5BU5D_tF6C207267F84EF7B2E35B63848ABD883EF3029F6;
extern const uint32_t g_rgctx_TextProcessingStack_1_SetDefault_mC81167C7CE9FB6C65188B380395CF6B157C5D9FE;
extern const uint32_t g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993;
extern const uint32_t g_rgctx_Array_Resize_TisT_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5_m6B12D3B6815FB2F9D9DF2EA23C4DFCA6BF547697;
extern const uint32_t g_rgctx_TU5BU5DU26_t4898B7229712712817C3A8E604F06F6847D1B0E9;
extern const uint32_t g_rgctx_TU5BU5DU26_tA2A52A09016344C678073A195A8BE1E3170A4155;
extern const uint32_t g_rgctx_Array_Resize_TisT_t8C4AB9269F69C847CB859664520F1EA90EB58C17_m5EE8B9DCC7BB82D6C15F3D92C00F22DC30790EA4;
extern const uint32_t g_rgctx_TU5BU5DU26_tCAB0EE3F22D9632C2EE8DA235A7B007B6BE506A8;
extern const uint32_t g_rgctx_Array_Resize_TisT_tAC54EC04F9A909B6219B1BFD5AC48378750418BB_m380863C69CA0CC2E12613241549E650FAB772971;
static const Il2CppRGCTXDefinition s_rgctxValues[88] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_1_t1AEE0F0AABEE7404C4C408917291668FB716425A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tAEB17B50990020D7888CB7BC8640B14B14CCC302 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tBBD016213FB8CB6AF69308DB050D148EE7E6DF1C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_m681CECA194150636ADC330C8DCE6F2E1C90F4446 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t9CEF548CEC479FB27EAEEB98F0FFDA705562A403 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_m14D7B2A1F6D3D73ED9C2562CFAE672D660F68D93 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_tB7599F2A0784F782E15B01E99D1C617682625608 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_mD9A8D26604EEA796FF2C57B00459FADC4DFB2E98 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m543BE8B8AD6450838820C791A4947FFACABB9FDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1U26_t6DD30519D2A3456B64B4805D166A23F5089A5218 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_mDD0B5A9F8F7A136CA5DB4843F3D5DB02AD3FED87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_mE2B1D75F3BCEB8EBBECDFE32082676881F6E24E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_m2A6A26E68AA7C4E2A44D6D5A32A3EF299668D55C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_mE6D843C44C2F0D114A1C1343336B3B8FC3A19DF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_A_t4E963D9C15FF3E9AB1408E60BFC62A134C1FFC06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m7D44F35106B9A85A9AE189517E1ADB864EDCA19E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mDCD93480C3841C757AD1CF91846ECA590BFB1FDC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_m021247779D7C36A587EE76E7E84D40BDA31D5E0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mA33E63AFB65BC9E467B8BF7A677C64B4D8E0773B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_2_t56AD8F7C1E0FCEFECC175425B3463D506B2FA568 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t8CC3267E19AC648E769CF2C202B77DFBF4E2767B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tF5943A3D7C5CE934B355A05B9009D66482F831E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_mE594D9227F07C4082946A5895F1BF4EBC50043D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t338546D10879B9307679AC24AF0F54F1C8C6EE34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_m5C1404B6635DD24573899467C355381DC3AA28AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t1864D9A2CBFC1623AFE0F79E74976A7FCADA3A1A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_mB8D02F9C31FC229346EAF03C9578244BB3E62194 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_mAB90E3B53A6AE8DA28CB4C591AC3FE4110C8B78A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1U26_tCF3F4AC0E5BE7EE179F5AA571AF811F09083032D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_m0BE9419BCB3F0DA697A73685A7ECFC279AB59A85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_m45C30C1702A75B7C76C34D961C2D298F980DCC25 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_m7E96B595CD83F59B2FF559A674EFF09A9FB68090 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_m5C2DB4AC8057756DD297D734501D2899DC756E1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_A_tBD2E164CC30412A91CBFC391AA372C6493D30445 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_B_t6C2D3267AFFE177F95438677193B36775F8DF1C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m335EE8932D069809AC784E42D93AE07375FA5AAC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mE0701B9C68739AE8AE6E4A352F32F50019C4FCB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_m72A68E0BBEE55D44CBB7EE1D9079616499D15DF3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m3A05DA54DC434F29C0C12E1A520C4434B9744AE9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_3_tA68636C4F81D8339F713A4430D68BCDDCC40EDF6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tF5886DEEA57665A6FF3E8518E295A3655F128908 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t2CA201DEA8178441EED1ECCEBDF2A41E4C1F49B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_m9FBCE8820591A5FEF7520678F99FE0C0AC317303 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_tBF12ED575D13E6570DEBFB1EA512BE0D4EDFE1CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_m699573BB4162D0842793454571AEAC390AC7BDF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_tFE6FAC2382573B70AB10E7EB5FB1B4BA6333D8BA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_mB820A3459B33E817C92E3F96E5AD9B8B2B0FF7E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m54F3C9E3FFEE9F038EDB0B81C2C2673B5024A09B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1U26_t2105CD7DC1B9DB0CEE53BB4742875D40E398F529 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_mD24F5A2D03262F760390A66DE5309E332A3520DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_mAD47F484F8DE1C87538250A23A70494AE86C15B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_mF89B11FBE0761B2A3171FF190455857C4FD08624 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_m479267486F1297CBA6873A66ECB15BA018AF2F88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_A_t77DC486495296BDC28F3B734F3411255DCBA8419 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_B_t1C87FE2C1B699FB5D8B4B5C57BF7D5271126FF85 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_C_t55A8D90479E752A4498DF6515327921CF0041A0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3_Invoke_m5A3EBBFD93AEA8685C851B7E37EE1AE5E451BF8D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mCFB2D295A4D3D39AEEC580340B8B172FA1A50CD7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_mAABCB69DA28E906DE762115AF4FBB5DA7865D66F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mD2210DEFBDD439204C1964C7C3AB595D43EA3CAA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tA286387994A1339C2CF844BE5728698C1E998AFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m94023DC2882A00FF3E2A87B830A03C35E07FAED7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t624F2E5B72A639CE2DFB1A28C4AE0422E25DC7FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tDFCEC986C8C5C9107923D8936ADFFA31D6C82631 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tC526A9C1F5DF2DFE47235494265CDBAC2F3C7084_mE23A43EFBF8FEFA791C6C9975CB651227B8576A3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mE8A0A11819707997B5CBE996B1D1A5498DF9FF44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC526A9C1F5DF2DFE47235494265CDBAC2F3C7084 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t34638B2E1F7E84A13C103ECA102E8228E5495A8F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tB4C007A2682984D0FC6913C0705D947ACCDD2686_mB7195CE4A4808328845CB836896D64A709E7B865 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t70819A0A282144E9916C49712A6DC840F9A74D7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t21B35BF972C1E5C4F52B0E073B10F9EE3CC93BD7_m08A1BE0AED907371A0B1811BF2028B1DF7CEB065 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t70904DE589CEA82A52949E06395973C9DCED555A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t17405AE98647F674620A8588A2A38EC17385AEF3_m819D8D61F3D2D33140D1CBF31E6D9A4540919072 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tB01B5E1E9375748D28EAB2E7C309F3263CEF4E49 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tA5CF78E77248D29C8555253A5B104C010B149A3D_m66CF7004011C393E0AFA739A42295579B02C917F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TextProcessingStack_1U5BU5D_tF6C207267F84EF7B2E35B63848ABD883EF3029F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TextProcessingStack_1_SetDefault_mC81167C7CE9FB6C65188B380395CF6B157C5D9FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5_m6B12D3B6815FB2F9D9DF2EA23C4DFCA6BF547697 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t4898B7229712712817C3A8E604F06F6847D1B0E9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tA2A52A09016344C678073A195A8BE1E3170A4155 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t8C4AB9269F69C847CB859664520F1EA90EB58C17_m5EE8B9DCC7BB82D6C15F3D92C00F22DC30790EA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tCAB0EE3F22D9632C2EE8DA235A7B007B6BE506A8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tAC54EC04F9A909B6219B1BFD5AC48378750418BB_m380863C69CA0CC2E12613241549E650FAB772971 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_TextCoreTextEngineModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule = 
{
	"UnityEngine.TextCoreTextEngineModule.dll",
	630,
	s_methodPointers,
	77,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	11,
	s_rgctxIndices,
	88,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_TextCoreTextEngineModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
