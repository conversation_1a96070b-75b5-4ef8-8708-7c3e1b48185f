﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void StreamingController_get_streamingMipmapBias_mF99272AF97EBE7550CD3C433165C75B85AC154F2 (void);
extern void StreamingController_set_streamingMipmapBias_m5A74189DAAB6A6109154575CB3BDA9A99598A8AA (void);
extern void StreamingController_SetPreloading_m6CC4C9E5742E497DF24D852A5B3A22ED7AE8E25D (void);
extern void StreamingController_CancelPreloading_mD5ABD57F18813CE7BF549827CAB2D683B2941B06 (void);
extern void StreamingController_IsPreloading_m355E060BFA8E75375BD7290AD227370C00E7DDF5 (void);
extern void StreamingController__ctor_m552E70EAB8C02690F934B9CC1D9F1EAD2CCDE798 (void);
static Il2CppMethodPointer s_methodPointers[6] = 
{
	StreamingController_get_streamingMipmapBias_mF99272AF97EBE7550CD3C433165C75B85AC154F2,
	StreamingController_set_streamingMipmapBias_m5A74189DAAB6A6109154575CB3BDA9A99598A8AA,
	StreamingController_SetPreloading_m6CC4C9E5742E497DF24D852A5B3A22ED7AE8E25D,
	StreamingController_CancelPreloading_mD5ABD57F18813CE7BF549827CAB2D683B2941B06,
	StreamingController_IsPreloading_m355E060BFA8E75375BD7290AD227370C00E7DDF5,
	StreamingController__ctor_m552E70EAB8C02690F934B9CC1D9F1EAD2CCDE798,
};
static const int32_t s_InvokerIndices[6] = 
{
	4298,
	3928,
	2142,
	4364,
	4168,
	4364,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_StreamingModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_StreamingModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_StreamingModule_CodeGenModule = 
{
	"UnityEngine.StreamingModule.dll",
	6,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUnityEngine_StreamingModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
