﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UniTaskAsyncEnumerable_EveryUpdate_m956B189411A0CA182EF8B525064626145F4CF092 (void);
extern void UniTaskAsyncEnumerable_Interval_mDC4D7EE47A309C1C5FB64518F05F2A6FA2BCAE84 (void);
extern void EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133 (void);
extern void EveryUpdate_GetAsyncEnumerator_mA5FC54EDF34ED5BB326271A43A7264A2702F3840 (void);
extern void _EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B (void);
extern void _EveryUpdate_get_Current_m9530A0604459C52E0B4E1C867D6130FB58FA1472 (void);
extern void _EveryUpdate_MoveNextAsync_m26553288670515E44E150AEFBDF39D5AF93ED0C1 (void);
extern void _EveryUpdate_DisposeAsync_m697EA4E391FC38D6CA01AE56DE13A0C7D605ADAD (void);
extern void _EveryUpdate_MoveNext_m901D698E90BE0DA3A2F39C587E0C1AADE5EAE061 (void);
extern void U3CU3Ec__cctor_m1853D83A8C62BC152E3C5A1B3D82BC8C03A2F513 (void);
extern void U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2 (void);
extern void Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F (void);
extern void Timer_GetAsyncEnumerator_mBE61A5FEDB1387353BF4E15E8DBA832BA126913D (void);
extern void _Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2 (void);
extern void _Timer_get_Current_m1DFFF2DB3E1FDA56BBC2BD8973DEAA81946BA498 (void);
extern void _Timer_MoveNextAsync_m1C536CA3782940F8CDA20FE36160B2130B9DA833 (void);
extern void _Timer_DisposeAsync_mAAF11D3D9223205036F0695E5A6E4DA4C092B565 (void);
extern void _Timer_MoveNext_mE978E92C7BCABBE941ECAACA29BDEC938A740BA4 (void);
extern void U3CU3Ec__cctor_mC3A825E1D0D47B2DF7DC8E84857ED248A7601865 (void);
extern void U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F (void);
static Il2CppMethodPointer s_methodPointers[22] = 
{
	UniTaskAsyncEnumerable_EveryUpdate_m956B189411A0CA182EF8B525064626145F4CF092,
	UniTaskAsyncEnumerable_Interval_mDC4D7EE47A309C1C5FB64518F05F2A6FA2BCAE84,
	EveryUpdate__ctor_m7E0BB8B1BB6D9E6674566F5D4040DC06A6C64133,
	EveryUpdate_GetAsyncEnumerator_mA5FC54EDF34ED5BB326271A43A7264A2702F3840,
	_EveryUpdate__ctor_m720DB73134A52F587B5C06D4AB65C5695DB8525B,
	_EveryUpdate_get_Current_m9530A0604459C52E0B4E1C867D6130FB58FA1472,
	_EveryUpdate_MoveNextAsync_m26553288670515E44E150AEFBDF39D5AF93ED0C1,
	_EveryUpdate_DisposeAsync_m697EA4E391FC38D6CA01AE56DE13A0C7D605ADAD,
	_EveryUpdate_MoveNext_m901D698E90BE0DA3A2F39C587E0C1AADE5EAE061,
	U3CU3Ec__cctor_m1853D83A8C62BC152E3C5A1B3D82BC8C03A2F513,
	U3CU3Ec__ctor_m13E90642B99BFBEC8C07BDB8B7F173A969DD5662,
	U3CU3Ec_U3C_ctorU3Eb__4_0_m819AE164F7F119D264F64FBF6515DD3EAA730FB2,
	Timer__ctor_m5ABA17C5028D1F0176E530D20DF37A60119C0D8F,
	Timer_GetAsyncEnumerator_mBE61A5FEDB1387353BF4E15E8DBA832BA126913D,
	_Timer__ctor_m0D814AB06478A51B470E2C4A08C3C43F410EE2A2,
	_Timer_get_Current_m1DFFF2DB3E1FDA56BBC2BD8973DEAA81946BA498,
	_Timer_MoveNextAsync_m1C536CA3782940F8CDA20FE36160B2130B9DA833,
	_Timer_DisposeAsync_mAAF11D3D9223205036F0695E5A6E4DA4C092B565,
	_Timer_MoveNext_mE978E92C7BCABBE941ECAACA29BDEC938A740BA4,
	U3CU3Ec__cctor_mC3A825E1D0D47B2DF7DC8E84857ED248A7601865,
	U3CU3Ec__ctor_m159C954281DAB83E3033E723DFB83AB2CDAAEFF5,
	U3CU3Ec_U3C_ctorU3Eb__11_0_mC0ED724BEC945D3F1A6EAC63C6660EE7D87AEF6F,
};
static const int32_t s_InvokerIndices[22] = 
{
	7594,
	5990,
	2729,
	3504,
	1963,
	4158,
	4127,
	4352,
	4168,
	9089,
	4364,
	3881,
	1012,
	3504,
	663,
	4158,
	4127,
	4352,
	4168,
	9089,
	4364,
	3881,
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUniTask_Linq;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UniTask_Linq_CodeGenModule;
const Il2CppCodeGenModule g_UniTask_Linq_CodeGenModule = 
{
	"UniTask.Linq.dll",
	22,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	&g_DebuggerMetadataRegistrationUniTask_Linq,
	NULL,
	NULL,
	NULL,
	NULL,
};
