﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void NonVersionableAttribute__ctor_m0ADAC9DE3EE89069011DA00E4AF296F598A6B91E (void);
static Il2CppMethodPointer s_methodPointers[2] = 
{
	NULL,
	NonVersionableAttribute__ctor_m0ADAC9DE3EE89069011DA00E4AF296F598A6B91E,
};
static const int32_t s_InvokerIndices[2] = 
{
	0,
	4364,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000001, { 0, 2 } },
};
extern const uint32_t g_rgctx_TFromU26_t6F1619817A057FA8E306385344157FCB126A04FC;
extern const uint32_t g_rgctx_TToU26_t3D4D021E666D0AACD653825762D29498523EC1F1;
static const Il2CppRGCTXDefinition s_rgctxValues[2] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TFromU26_t6F1619817A057FA8E306385344157FCB126A04FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TToU26_t3D4D021E666D0AACD653825762D29498523EC1F1 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationSystem_Runtime_CompilerServices_Unsafe;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Runtime_CompilerServices_Unsafe_CodeGenModule;
const Il2CppCodeGenModule g_System_Runtime_CompilerServices_Unsafe_CodeGenModule = 
{
	"System.Runtime.CompilerServices.Unsafe.dll",
	2,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	2,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationSystem_Runtime_CompilerServices_Unsafe,
	NULL,
	NULL,
	NULL,
	NULL,
};
