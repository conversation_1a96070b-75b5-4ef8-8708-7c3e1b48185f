﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7;

IL2CPP_EXTERN_C const RuntimeMethod* WindZone__ctor_m9A197F1A309977C754694D2754C6620566B0F502_RuntimeMethod_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t69CE5C6D2AE80D4BB70EDB461AFA715C1BD9B7B4 
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct WindZoneMode_tA6E3890373D2A9E242DC0490DEA09F02B309C24E 
{
	int32_t ___value__;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Component__ctor_m4319162A6E6B02301078C1233F6E7F4A3E735486 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t WindZone_get_mode_mE2B2472178BD6B397D1C9AEF225A38B7AC148FE7 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, const RuntimeMethod* method) 
{
	typedef int32_t (*WindZone_get_mode_mE2B2472178BD6B397D1C9AEF225A38B7AC148FE7_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*);
	static WindZone_get_mode_mE2B2472178BD6B397D1C9AEF225A38B7AC148FE7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_get_mode_mE2B2472178BD6B397D1C9AEF225A38B7AC148FE7_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::get_mode()");
	int32_t icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WindZone_set_mode_mE9FB6F17EC20E82BEA52263D719307EDD1341FD1 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WindZone_set_mode_mE9FB6F17EC20E82BEA52263D719307EDD1341FD1_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*, int32_t);
	static WindZone_set_mode_mE9FB6F17EC20E82BEA52263D719307EDD1341FD1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_set_mode_mE9FB6F17EC20E82BEA52263D719307EDD1341FD1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::set_mode(UnityEngine.WindZoneMode)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WindZone_get_radius_mCA754177573056EF9C677C83DBD9C2CBD4979516 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, const RuntimeMethod* method) 
{
	typedef float (*WindZone_get_radius_mCA754177573056EF9C677C83DBD9C2CBD4979516_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*);
	static WindZone_get_radius_mCA754177573056EF9C677C83DBD9C2CBD4979516_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_get_radius_mCA754177573056EF9C677C83DBD9C2CBD4979516_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::get_radius()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WindZone_set_radius_mA01949115ED2AB82C6286D63704AEAE11EB7EDB7 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WindZone_set_radius_mA01949115ED2AB82C6286D63704AEAE11EB7EDB7_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*, float);
	static WindZone_set_radius_mA01949115ED2AB82C6286D63704AEAE11EB7EDB7_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_set_radius_mA01949115ED2AB82C6286D63704AEAE11EB7EDB7_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::set_radius(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WindZone_get_windMain_m49480166CDE759C98498B951148535FCCED788C0 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, const RuntimeMethod* method) 
{
	typedef float (*WindZone_get_windMain_m49480166CDE759C98498B951148535FCCED788C0_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*);
	static WindZone_get_windMain_m49480166CDE759C98498B951148535FCCED788C0_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_get_windMain_m49480166CDE759C98498B951148535FCCED788C0_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::get_windMain()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WindZone_set_windMain_mECB9DF2D5BAB9FD545891C3409B36CC57605F2DF (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WindZone_set_windMain_mECB9DF2D5BAB9FD545891C3409B36CC57605F2DF_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*, float);
	static WindZone_set_windMain_mECB9DF2D5BAB9FD545891C3409B36CC57605F2DF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_set_windMain_mECB9DF2D5BAB9FD545891C3409B36CC57605F2DF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::set_windMain(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WindZone_get_windTurbulence_mB7D83DE60E30847971D13C6601C46F14839F8448 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, const RuntimeMethod* method) 
{
	typedef float (*WindZone_get_windTurbulence_mB7D83DE60E30847971D13C6601C46F14839F8448_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*);
	static WindZone_get_windTurbulence_mB7D83DE60E30847971D13C6601C46F14839F8448_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_get_windTurbulence_mB7D83DE60E30847971D13C6601C46F14839F8448_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::get_windTurbulence()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WindZone_set_windTurbulence_m61C6D929BE93A628E05A3C6C3557FF660D58231B (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WindZone_set_windTurbulence_m61C6D929BE93A628E05A3C6C3557FF660D58231B_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*, float);
	static WindZone_set_windTurbulence_m61C6D929BE93A628E05A3C6C3557FF660D58231B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_set_windTurbulence_m61C6D929BE93A628E05A3C6C3557FF660D58231B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::set_windTurbulence(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WindZone_get_windPulseMagnitude_m6166F0CA589D120036D01F796DBE7201EE2A4724 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, const RuntimeMethod* method) 
{
	typedef float (*WindZone_get_windPulseMagnitude_m6166F0CA589D120036D01F796DBE7201EE2A4724_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*);
	static WindZone_get_windPulseMagnitude_m6166F0CA589D120036D01F796DBE7201EE2A4724_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_get_windPulseMagnitude_m6166F0CA589D120036D01F796DBE7201EE2A4724_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::get_windPulseMagnitude()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WindZone_set_windPulseMagnitude_mC0C0A5D0F89B2D33640CEC9B24AE995C16967325 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WindZone_set_windPulseMagnitude_mC0C0A5D0F89B2D33640CEC9B24AE995C16967325_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*, float);
	static WindZone_set_windPulseMagnitude_mC0C0A5D0F89B2D33640CEC9B24AE995C16967325_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_set_windPulseMagnitude_mC0C0A5D0F89B2D33640CEC9B24AE995C16967325_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::set_windPulseMagnitude(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float WindZone_get_windPulseFrequency_mE95A49751644837A6668834E41F76D66C7E8173C (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, const RuntimeMethod* method) 
{
	typedef float (*WindZone_get_windPulseFrequency_mE95A49751644837A6668834E41F76D66C7E8173C_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*);
	static WindZone_get_windPulseFrequency_mE95A49751644837A6668834E41F76D66C7E8173C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_get_windPulseFrequency_mE95A49751644837A6668834E41F76D66C7E8173C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::get_windPulseFrequency()");
	float icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WindZone_set_windPulseFrequency_m389F653CF33A959B9CCF4998AA18D0E392FCE5BA (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, float ___0_value, const RuntimeMethod* method) 
{
	typedef void (*WindZone_set_windPulseFrequency_m389F653CF33A959B9CCF4998AA18D0E392FCE5BA_ftn) (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7*, float);
	static WindZone_set_windPulseFrequency_m389F653CF33A959B9CCF4998AA18D0E392FCE5BA_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (WindZone_set_windPulseFrequency_m389F653CF33A959B9CCF4998AA18D0E392FCE5BA_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.WindZone::set_windPulseFrequency(System.Single)");
	_il2cpp_icall_func(__this, ___0_value);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WindZone__ctor_m9A197F1A309977C754694D2754C6620566B0F502 (WindZone_t7946C428D1DA3C255DCDC832A39B871EDC1315A7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WindZone__ctor_m9A197F1A309977C754694D2754C6620566B0F502_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	DECLARE_METHOD_EXEC_CTX(methodExecutionContext, WindZone__ctor_m9A197F1A309977C754694D2754C6620566B0F502_RuntimeMethod_var, NULL, NULL, NULL);
	CHECK_PAUSE_POINT;
	{
		Component__ctor_m4319162A6E6B02301078C1233F6E7F4A3E735486(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
