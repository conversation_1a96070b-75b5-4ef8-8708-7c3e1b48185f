﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AssetBundle__ctor_m12989CA081324BB49ED893BDA5E3B4E758D61410 (void);
extern void AssetBundle_get_mainAsset_mBB663F2D2D3593437EF1A90F464CBEBF9F3D1F5C (void);
extern void AssetBundle_returnMainAsset_mC84BDF6C014E101EC58740FB0BB4A44F91270AFE (void);
extern void AssetBundle_UnloadAllAssetBundles_mF68ACFAF3AB098C0E083552C235B20508C58C11D (void);
extern void AssetBundle_GetAllLoadedAssetBundles_Native_m67164021223D792B05A0D016EED0418D35EC8B7F (void);
extern void AssetBundle_GetAllLoadedAssetBundles_m2975A463EF64FC0B497BC189F8DC4A584FF361AA (void);
extern void AssetBundle_LoadFromFileAsync_Internal_mB3D099E653D4AF91E5EA8CDCC49E8AB1BBAB764B (void);
extern void AssetBundle_LoadFromFileAsync_m4C768290B7B6EE3CF7483BD4F0D338D496735F94 (void);
extern void AssetBundle_LoadFromFileAsync_m37B766D9A85083324BC10538BCB29F8C9C263FAD (void);
extern void AssetBundle_LoadFromFileAsync_mF5C6B1FF491A8B654AA72057F3EDE17EEBC6B000 (void);
extern void AssetBundle_LoadFromFile_Internal_m806E92FA35F9FF7F9868058055FB347B9E35BD74 (void);
extern void AssetBundle_LoadFromFile_mAFD7B0E8F0A0A88E9BB60180EBA0A99B8B3F2629 (void);
extern void AssetBundle_LoadFromFile_mA89BACA0B64FA12671A87323FCC701207E79731A (void);
extern void AssetBundle_LoadFromFile_m26FF275605246942FE95EB4AF7AEC7A9C70BE33A (void);
extern void AssetBundle_LoadFromMemoryAsync_Internal_m9D90D48F46D71E441B4A19659C9004EF40E9CD1B (void);
extern void AssetBundle_LoadFromMemoryAsync_mDDA8EC114E4097156F40C337B45837872998348F (void);
extern void AssetBundle_LoadFromMemoryAsync_m9675CBA547DDF9535EF777F64EC62B25955E57F7 (void);
extern void AssetBundle_LoadFromMemory_Internal_m05D4AAA1B9AF41422A57A602AED64172D29C4305 (void);
extern void AssetBundle_LoadFromMemory_mBA6847E4133DBBE3CCBCFFF31A40FA943DB95BBA (void);
extern void AssetBundle_LoadFromMemory_m710E253C17EC9E1B60C45DD6B40557311D41C76A (void);
extern void AssetBundle_ValidateLoadFromStream_mAEBF20D780F72AA69AE06E595AE80748F0F60132 (void);
extern void AssetBundle_LoadFromStreamAsync_mC37FAB35BDA81D298454D5237964EDE406013D8E (void);
extern void AssetBundle_LoadFromStreamAsync_m3D000B56EC58D973595DB4AABD9165685FCD67D9 (void);
extern void AssetBundle_LoadFromStreamAsync_m07585A5AF7F8CB0C208D0F2DFD173126B59C3AA2 (void);
extern void AssetBundle_LoadFromStream_m0BB52CB48B301C66D58A0DBDCE3DA3509479FA53 (void);
extern void AssetBundle_LoadFromStream_m91B2A8C12555987BDE7DBF7FCE2E57D51F6EEE1E (void);
extern void AssetBundle_LoadFromStream_m453B3CE7F4B172F3894D1E54B3AFD61AC7CE103E (void);
extern void AssetBundle_LoadFromStreamAsyncInternal_m313F011118F68083B97D867CDC42B627F960BA34 (void);
extern void AssetBundle_LoadFromStreamInternal_m60CF9A3CAFA7872B9CFCF284BBCB8660D646BE80 (void);
extern void AssetBundle_get_isStreamedSceneAssetBundle_mA1A8F1D578F6DBB4BD640D29E4872B0C8DF88054 (void);
extern void AssetBundle_Contains_mEF9A0F9E85054965937BF41B4D93FE049D546EAB (void);
extern void AssetBundle_Load_mF7B01A7C04A6606DE4756468AC7AD7E66AE87BA3 (void);
extern void AssetBundle_Load_m188E81A9233CCFD813CF9B131EF3380A84E62836 (void);
extern void AssetBundle_LoadAsync_mF4276E665F20A9DB3BFCB7CD32594E46884F61D7 (void);
extern void AssetBundle_LoadAll_m8B5032D7F7398EB7CB46093F3E5DDFB5832B43DA (void);
extern void AssetBundle_LoadAll_mE7B3CC362F74D752A9993BE220FE6EC6F92FCCD0 (void);
extern void AssetBundle_LoadAsset_m25926A405F3AB79A4DF2447F23A09957EC7F063E (void);
extern void AssetBundle_LoadAsset_m021FE0B52DD660E54AE4C225D9AE66147902B8FE (void);
extern void AssetBundle_LoadAsset_Internal_mD096392756815901FE982C1AF64DDF0846551433 (void);
extern void AssetBundle_LoadAssetAsync_m686CB93D1D35DD784C74FFE8343D5626D736FF32 (void);
extern void AssetBundle_LoadAssetAsync_mDAE70E6DD6F9D5360A5D0C7A74F3989174C2FEDB (void);
extern void AssetBundle_LoadAssetWithSubAssets_m8F1B9E2F3DED19105612E7874487ADEC890480AF (void);
extern void AssetBundle_LoadAssetWithSubAssets_m664A6B7E93502E26177F6C218FAB79A218DF841F (void);
extern void AssetBundle_LoadAssetWithSubAssetsAsync_m5FC8920EC24E5D629664A30F7AE46F114ECD4D05 (void);
extern void AssetBundle_LoadAssetWithSubAssetsAsync_m8352895AE4BB09248808FCB3C66458DB911C1D7B (void);
extern void AssetBundle_LoadAllAssets_m00E63EFF07B8E986754B7AFB3B8566B2B99C2241 (void);
extern void AssetBundle_LoadAllAssets_m0A8F41292C96F658A89B0E8D0ADB2E8395DD7F62 (void);
extern void AssetBundle_LoadAllAssetsAsync_m01938F14A24C6C0C08BA6480D1384D674AD1DDB7 (void);
extern void AssetBundle_LoadAllAssetsAsync_m6BF82A05B486AA713460D4BC836E1445F2B45DB9 (void);
extern void AssetBundle_AllAssetNames_mA9C87CD2928E1E1F98B4BED8270CD97DC81811E8 (void);
extern void AssetBundle_LoadAssetAsync_Internal_m2F3749CA103E883447AE6629E342D4C0D39A2FBE (void);
extern void AssetBundle_Unload_m0A189871E61A0D6735A2B41B3360A1F0677B636B (void);
extern void AssetBundle_UnloadAsync_mEF0E3987C53B4FA85A8FC54B15FB380D02FDF908 (void);
extern void AssetBundle_GetAllAssetNames_m44504DB9E055412F0DF2071A769A243219708CC3 (void);
extern void AssetBundle_GetAllScenePaths_m75D1CD4415067D64A8265A7929B6FF76B91B62FE (void);
extern void AssetBundle_LoadAssetWithSubAssets_Internal_m14AE2B2D7696182CBDF12087E8D3FEA867290DA8 (void);
extern void AssetBundle_LoadAssetWithSubAssetsAsync_Internal_m9C189D40BB6E14EFB7432173EC273BE9C5E900D4 (void);
extern void AssetBundle_RecompressAssetBundleAsync_m192D976312F2EDD06CD4A16A944E34D26C6BBF7F (void);
extern void AssetBundle_RecompressAssetBundleAsync_Internal_m88DEBE65D4DE782D8EAD464F478E5E1E271D4340 (void);
extern void AssetBundle_get_memoryBudgetKB_m7ADC85815CC6E7D25EF47BE9E7C5AEC48C46254A (void);
extern void AssetBundle_set_memoryBudgetKB_mE1FC53CD07BE4A2B9C9155DE117863B4B5D06342 (void);
extern void AssetBundle_RecompressAssetBundleAsync_Internal_Injected_mFA6FEA46D49600F7F976F9145EAA538E774E275F (void);
extern void AssetBundleCreateRequest_get_assetBundle_m613FDE589FB86BE1E6920D38ED0706F785D9DB21 (void);
extern void AssetBundleCreateRequest_SetEnableCompatibilityChecks_m6EA5325527A89A19B421483B68279276FA2ADC37 (void);
extern void AssetBundleCreateRequest_DisableCompatibilityChecks_m2A1B2A1063BB25FE97BB2D25004FE12C77FDD51B (void);
extern void AssetBundleCreateRequest__ctor_mAD1A6314795AC1B548A29A32E01815D33B6D0B46 (void);
extern void AssetBundleLoadingCache_get_maxBlocksPerFile_m651D8C3A0EDB2AD256471E0CBA0C3F322052E6D7 (void);
extern void AssetBundleLoadingCache_set_maxBlocksPerFile_mDA170E8B6A4A8A84F93C73DBF2896AA1A72A4F02 (void);
extern void AssetBundleLoadingCache_get_blockCount_m853277E5E331AA0870C101A3B0DE4D9BD96BA8CE (void);
extern void AssetBundleLoadingCache_set_blockCount_m05D23E129AA89CF2BA43CFA365535DB8F59D045B (void);
extern void AssetBundleLoadingCache_get_blockSize_m03DC5F56278056003283B8878EE2F1C1132BD6A0 (void);
extern void AssetBundleLoadingCache_get_memoryBudgetKB_m29A67C3A16A863A1AA0CA01327049BFB7BB6DF01 (void);
extern void AssetBundleLoadingCache_set_memoryBudgetKB_m29DE6B7CA5A4A3713806E59432EF17268BF734D6 (void);
extern void AssetBundleManifest__ctor_mB9CBA729A674E0A566E5F45E91C4607F35C0C785 (void);
extern void AssetBundleManifest_GetAllAssetBundles_m0B9B68B03401B23693582DFE66F7B10A8C80EE54 (void);
extern void AssetBundleManifest_GetAllAssetBundlesWithVariant_m1259F23E830335D322DC7B659CA3A2A68794D237 (void);
extern void AssetBundleManifest_GetAssetBundleHash_mD37180E92D8740FFD6696CC8DEFF3D5A270A70E2 (void);
extern void AssetBundleManifest_GetDirectDependencies_mF74E87F6BAF8FF42DAF93A6246407A73C624872D (void);
extern void AssetBundleManifest_GetAllDependencies_mB1DEAC63DED6BF14A3997326F0ADCB9DFD20C63A (void);
extern void AssetBundleManifest_GetAssetBundleHash_Injected_mE92D11CD83E3B3B165CBBEBA395D777F291AA92D (void);
extern void AssetBundleRecompressOperation_get_humanReadableResult_m1E51DEE2DB410FA6936F36BD28E2E5DF4AF75662 (void);
extern void AssetBundleRecompressOperation_get_inputPath_m4B430FB72F6583F34176511AC071DBF475766D68 (void);
extern void AssetBundleRecompressOperation_get_outputPath_m525B6F1C1189FE2E0683548D147A3B32FC16C190 (void);
extern void AssetBundleRecompressOperation_get_result_m18AB39E34A3679FEA71C690763A09D832DA4C193 (void);
extern void AssetBundleRecompressOperation_get_success_m70A23D0632088DD524DA5F6880C546CB89C77280 (void);
extern void AssetBundleRecompressOperation__ctor_m8E08403D355CF9145ABAF7DD50352D29D4BB14B1 (void);
extern void AssetBundleRequest_GetResult_mC1C60D584906835F86BF82C44E8B62B6EB9171D6 (void);
extern void AssetBundleRequest_get_asset_mE9FDA3900215925371E7D15E7E19AA98713F5D6C (void);
extern void AssetBundleRequest_get_allAssets_mCA7BF6BCBBDA2E6DF596655254842B5380B4919D (void);
extern void AssetBundleRequest__ctor_mD73743E1532E41D8AD2871C00A7FCDA5157171C0 (void);
extern void AssetBundleUnloadOperation_WaitForCompletion_mB088D657C55F6D7D203551013B216BE9391E3979 (void);
extern void AssetBundleUnloadOperation__ctor_m2CE9493424B3BD4B07FC03516F01F8425D0EEF83 (void);
extern void AssetBundleUtility_PatchAssetBundles_mFCDBCF5E297355B5A862E5D8452DFAF6CDA9C608 (void);
static Il2CppMethodPointer s_methodPointers[102] = 
{
	AssetBundle__ctor_m12989CA081324BB49ED893BDA5E3B4E758D61410,
	AssetBundle_get_mainAsset_mBB663F2D2D3593437EF1A90F464CBEBF9F3D1F5C,
	AssetBundle_returnMainAsset_mC84BDF6C014E101EC58740FB0BB4A44F91270AFE,
	AssetBundle_UnloadAllAssetBundles_mF68ACFAF3AB098C0E083552C235B20508C58C11D,
	AssetBundle_GetAllLoadedAssetBundles_Native_m67164021223D792B05A0D016EED0418D35EC8B7F,
	AssetBundle_GetAllLoadedAssetBundles_m2975A463EF64FC0B497BC189F8DC4A584FF361AA,
	AssetBundle_LoadFromFileAsync_Internal_mB3D099E653D4AF91E5EA8CDCC49E8AB1BBAB764B,
	AssetBundle_LoadFromFileAsync_m4C768290B7B6EE3CF7483BD4F0D338D496735F94,
	AssetBundle_LoadFromFileAsync_m37B766D9A85083324BC10538BCB29F8C9C263FAD,
	AssetBundle_LoadFromFileAsync_mF5C6B1FF491A8B654AA72057F3EDE17EEBC6B000,
	AssetBundle_LoadFromFile_Internal_m806E92FA35F9FF7F9868058055FB347B9E35BD74,
	AssetBundle_LoadFromFile_mAFD7B0E8F0A0A88E9BB60180EBA0A99B8B3F2629,
	AssetBundle_LoadFromFile_mA89BACA0B64FA12671A87323FCC701207E79731A,
	AssetBundle_LoadFromFile_m26FF275605246942FE95EB4AF7AEC7A9C70BE33A,
	AssetBundle_LoadFromMemoryAsync_Internal_m9D90D48F46D71E441B4A19659C9004EF40E9CD1B,
	AssetBundle_LoadFromMemoryAsync_mDDA8EC114E4097156F40C337B45837872998348F,
	AssetBundle_LoadFromMemoryAsync_m9675CBA547DDF9535EF777F64EC62B25955E57F7,
	AssetBundle_LoadFromMemory_Internal_m05D4AAA1B9AF41422A57A602AED64172D29C4305,
	AssetBundle_LoadFromMemory_mBA6847E4133DBBE3CCBCFFF31A40FA943DB95BBA,
	AssetBundle_LoadFromMemory_m710E253C17EC9E1B60C45DD6B40557311D41C76A,
	AssetBundle_ValidateLoadFromStream_mAEBF20D780F72AA69AE06E595AE80748F0F60132,
	AssetBundle_LoadFromStreamAsync_mC37FAB35BDA81D298454D5237964EDE406013D8E,
	AssetBundle_LoadFromStreamAsync_m3D000B56EC58D973595DB4AABD9165685FCD67D9,
	AssetBundle_LoadFromStreamAsync_m07585A5AF7F8CB0C208D0F2DFD173126B59C3AA2,
	AssetBundle_LoadFromStream_m0BB52CB48B301C66D58A0DBDCE3DA3509479FA53,
	AssetBundle_LoadFromStream_m91B2A8C12555987BDE7DBF7FCE2E57D51F6EEE1E,
	AssetBundle_LoadFromStream_m453B3CE7F4B172F3894D1E54B3AFD61AC7CE103E,
	AssetBundle_LoadFromStreamAsyncInternal_m313F011118F68083B97D867CDC42B627F960BA34,
	AssetBundle_LoadFromStreamInternal_m60CF9A3CAFA7872B9CFCF284BBCB8660D646BE80,
	AssetBundle_get_isStreamedSceneAssetBundle_mA1A8F1D578F6DBB4BD640D29E4872B0C8DF88054,
	AssetBundle_Contains_mEF9A0F9E85054965937BF41B4D93FE049D546EAB,
	AssetBundle_Load_mF7B01A7C04A6606DE4756468AC7AD7E66AE87BA3,
	NULL,
	AssetBundle_Load_m188E81A9233CCFD813CF9B131EF3380A84E62836,
	AssetBundle_LoadAsync_mF4276E665F20A9DB3BFCB7CD32594E46884F61D7,
	AssetBundle_LoadAll_m8B5032D7F7398EB7CB46093F3E5DDFB5832B43DA,
	AssetBundle_LoadAll_mE7B3CC362F74D752A9993BE220FE6EC6F92FCCD0,
	NULL,
	AssetBundle_LoadAsset_m25926A405F3AB79A4DF2447F23A09957EC7F063E,
	NULL,
	AssetBundle_LoadAsset_m021FE0B52DD660E54AE4C225D9AE66147902B8FE,
	AssetBundle_LoadAsset_Internal_mD096392756815901FE982C1AF64DDF0846551433,
	AssetBundle_LoadAssetAsync_m686CB93D1D35DD784C74FFE8343D5626D736FF32,
	NULL,
	AssetBundle_LoadAssetAsync_mDAE70E6DD6F9D5360A5D0C7A74F3989174C2FEDB,
	AssetBundle_LoadAssetWithSubAssets_m8F1B9E2F3DED19105612E7874487ADEC890480AF,
	NULL,
	NULL,
	AssetBundle_LoadAssetWithSubAssets_m664A6B7E93502E26177F6C218FAB79A218DF841F,
	AssetBundle_LoadAssetWithSubAssetsAsync_m5FC8920EC24E5D629664A30F7AE46F114ECD4D05,
	NULL,
	AssetBundle_LoadAssetWithSubAssetsAsync_m8352895AE4BB09248808FCB3C66458DB911C1D7B,
	AssetBundle_LoadAllAssets_m00E63EFF07B8E986754B7AFB3B8566B2B99C2241,
	NULL,
	AssetBundle_LoadAllAssets_m0A8F41292C96F658A89B0E8D0ADB2E8395DD7F62,
	AssetBundle_LoadAllAssetsAsync_m01938F14A24C6C0C08BA6480D1384D674AD1DDB7,
	NULL,
	AssetBundle_LoadAllAssetsAsync_m6BF82A05B486AA713460D4BC836E1445F2B45DB9,
	AssetBundle_AllAssetNames_mA9C87CD2928E1E1F98B4BED8270CD97DC81811E8,
	AssetBundle_LoadAssetAsync_Internal_m2F3749CA103E883447AE6629E342D4C0D39A2FBE,
	AssetBundle_Unload_m0A189871E61A0D6735A2B41B3360A1F0677B636B,
	AssetBundle_UnloadAsync_mEF0E3987C53B4FA85A8FC54B15FB380D02FDF908,
	AssetBundle_GetAllAssetNames_m44504DB9E055412F0DF2071A769A243219708CC3,
	AssetBundle_GetAllScenePaths_m75D1CD4415067D64A8265A7929B6FF76B91B62FE,
	AssetBundle_LoadAssetWithSubAssets_Internal_m14AE2B2D7696182CBDF12087E8D3FEA867290DA8,
	AssetBundle_LoadAssetWithSubAssetsAsync_Internal_m9C189D40BB6E14EFB7432173EC273BE9C5E900D4,
	AssetBundle_RecompressAssetBundleAsync_m192D976312F2EDD06CD4A16A944E34D26C6BBF7F,
	AssetBundle_RecompressAssetBundleAsync_Internal_m88DEBE65D4DE782D8EAD464F478E5E1E271D4340,
	AssetBundle_get_memoryBudgetKB_m7ADC85815CC6E7D25EF47BE9E7C5AEC48C46254A,
	AssetBundle_set_memoryBudgetKB_mE1FC53CD07BE4A2B9C9155DE117863B4B5D06342,
	AssetBundle_RecompressAssetBundleAsync_Internal_Injected_mFA6FEA46D49600F7F976F9145EAA538E774E275F,
	AssetBundleCreateRequest_get_assetBundle_m613FDE589FB86BE1E6920D38ED0706F785D9DB21,
	AssetBundleCreateRequest_SetEnableCompatibilityChecks_m6EA5325527A89A19B421483B68279276FA2ADC37,
	AssetBundleCreateRequest_DisableCompatibilityChecks_m2A1B2A1063BB25FE97BB2D25004FE12C77FDD51B,
	AssetBundleCreateRequest__ctor_mAD1A6314795AC1B548A29A32E01815D33B6D0B46,
	AssetBundleLoadingCache_get_maxBlocksPerFile_m651D8C3A0EDB2AD256471E0CBA0C3F322052E6D7,
	AssetBundleLoadingCache_set_maxBlocksPerFile_mDA170E8B6A4A8A84F93C73DBF2896AA1A72A4F02,
	AssetBundleLoadingCache_get_blockCount_m853277E5E331AA0870C101A3B0DE4D9BD96BA8CE,
	AssetBundleLoadingCache_set_blockCount_m05D23E129AA89CF2BA43CFA365535DB8F59D045B,
	AssetBundleLoadingCache_get_blockSize_m03DC5F56278056003283B8878EE2F1C1132BD6A0,
	AssetBundleLoadingCache_get_memoryBudgetKB_m29A67C3A16A863A1AA0CA01327049BFB7BB6DF01,
	AssetBundleLoadingCache_set_memoryBudgetKB_m29DE6B7CA5A4A3713806E59432EF17268BF734D6,
	AssetBundleManifest__ctor_mB9CBA729A674E0A566E5F45E91C4607F35C0C785,
	AssetBundleManifest_GetAllAssetBundles_m0B9B68B03401B23693582DFE66F7B10A8C80EE54,
	AssetBundleManifest_GetAllAssetBundlesWithVariant_m1259F23E830335D322DC7B659CA3A2A68794D237,
	AssetBundleManifest_GetAssetBundleHash_mD37180E92D8740FFD6696CC8DEFF3D5A270A70E2,
	AssetBundleManifest_GetDirectDependencies_mF74E87F6BAF8FF42DAF93A6246407A73C624872D,
	AssetBundleManifest_GetAllDependencies_mB1DEAC63DED6BF14A3997326F0ADCB9DFD20C63A,
	AssetBundleManifest_GetAssetBundleHash_Injected_mE92D11CD83E3B3B165CBBEBA395D777F291AA92D,
	AssetBundleRecompressOperation_get_humanReadableResult_m1E51DEE2DB410FA6936F36BD28E2E5DF4AF75662,
	AssetBundleRecompressOperation_get_inputPath_m4B430FB72F6583F34176511AC071DBF475766D68,
	AssetBundleRecompressOperation_get_outputPath_m525B6F1C1189FE2E0683548D147A3B32FC16C190,
	AssetBundleRecompressOperation_get_result_m18AB39E34A3679FEA71C690763A09D832DA4C193,
	AssetBundleRecompressOperation_get_success_m70A23D0632088DD524DA5F6880C546CB89C77280,
	AssetBundleRecompressOperation__ctor_m8E08403D355CF9145ABAF7DD50352D29D4BB14B1,
	AssetBundleRequest_GetResult_mC1C60D584906835F86BF82C44E8B62B6EB9171D6,
	AssetBundleRequest_get_asset_mE9FDA3900215925371E7D15E7E19AA98713F5D6C,
	AssetBundleRequest_get_allAssets_mCA7BF6BCBBDA2E6DF596655254842B5380B4919D,
	AssetBundleRequest__ctor_mD73743E1532E41D8AD2871C00A7FCDA5157171C0,
	AssetBundleUnloadOperation_WaitForCompletion_mB088D657C55F6D7D203551013B216BE9391E3979,
	AssetBundleUnloadOperation__ctor_m2CE9493424B3BD4B07FC03516F01F8425D0EEF83,
	AssetBundleUtility_PatchAssetBundles_mFCDBCF5E297355B5A862E5D8452DFAF6CDA9C608,
};
static const int32_t s_InvokerIndices[102] = 
{
	4364,
	4250,
	8505,
	8868,
	9031,
	9031,
	6703,
	8505,
	7640,
	6703,
	6703,
	8505,
	7640,
	6703,
	7640,
	8505,
	7640,
	7640,
	8505,
	7640,
	8887,
	6702,
	7640,
	8505,
	6702,
	7640,
	8505,
	6702,
	6702,
	4168,
	3185,
	3518,
	0,
	2524,
	2524,
	3518,
	4250,
	0,
	3518,
	0,
	2524,
	2524,
	3518,
	0,
	2524,
	3518,
	0,
	0,
	2524,
	3518,
	0,
	2524,
	4250,
	0,
	3518,
	4250,
	0,
	3518,
	4250,
	2524,
	3807,
	3503,
	4250,
	4250,
	2524,
	2524,
	5364,
	5364,
	9079,
	8903,
	5363,
	4250,
	3807,
	4364,
	4364,
	9079,
	8903,
	9079,
	8903,
	9079,
	9079,
	8903,
	4364,
	4250,
	4250,
	3379,
	3518,
	3518,
	2787,
	4250,
	4250,
	4250,
	4216,
	4168,
	4364,
	4250,
	4250,
	4250,
	4364,
	4364,
	4364,
	7975,
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x06000026, { 0, 1 } },
	{ 0x06000028, { 1, 2 } },
	{ 0x0600002C, { 3, 1 } },
	{ 0x0600002F, { 4, 3 } },
	{ 0x06000030, { 7, 3 } },
	{ 0x06000033, { 10, 1 } },
	{ 0x06000036, { 11, 3 } },
	{ 0x06000039, { 14, 1 } },
};
extern const uint32_t g_rgctx_TU5BU5D_t2DB7E89FFEDBB8B3101F937C1A5E24A74C81A563;
extern const uint32_t g_rgctx_T_t8F5466E687C8861B9EA9A0932E573160D9356A57;
extern const uint32_t g_rgctx_T_t8F5466E687C8861B9EA9A0932E573160D9356A57;
extern const uint32_t g_rgctx_T_tA8BD384E30517DB870F72539332AC91852CB1C9C;
extern const uint32_t g_rgctx_TU5BU5D_t62435FFFCFDDAF58FFD9198485B2A31DC6C62CFA;
extern const uint32_t g_rgctx_T_t89104750519603A8F27C7F396B9F54E9AEDA9257;
extern const uint32_t g_rgctx_TU5BU5D_t62435FFFCFDDAF58FFD9198485B2A31DC6C62CFA;
extern const uint32_t g_rgctx_T_t491E214B4A8D5D9985A967CF3F3F480B74F751CC;
extern const uint32_t g_rgctx_AssetBundle_ConvertObjects_TisT_t491E214B4A8D5D9985A967CF3F3F480B74F751CC_m1A440313E06671287FDA45B4CB7E21716D194AC5;
extern const uint32_t g_rgctx_TU5BU5D_t5EACF0F3BFDC811190873DFC83EC65A5E0132207;
extern const uint32_t g_rgctx_T_t1242A54463D8CE7EB725CB4040D26790068782D6;
extern const uint32_t g_rgctx_T_t4DDCDE7720DB1E953B03BAB314A225D5D5780ED5;
extern const uint32_t g_rgctx_AssetBundle_ConvertObjects_TisT_t4DDCDE7720DB1E953B03BAB314A225D5D5780ED5_mAE3896E191A8837DA13ADC43597D711900694863;
extern const uint32_t g_rgctx_TU5BU5D_tBAC463FFCA4BD4AF85453A8FA9F8A8C54AC90A4F;
extern const uint32_t g_rgctx_T_tC8CF0BFA5D22C4F7820EBABAAA7C4FF82150A459;
static const Il2CppRGCTXDefinition s_rgctxValues[15] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2DB7E89FFEDBB8B3101F937C1A5E24A74C81A563 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t8F5466E687C8861B9EA9A0932E573160D9356A57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8F5466E687C8861B9EA9A0932E573160D9356A57 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tA8BD384E30517DB870F72539332AC91852CB1C9C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t62435FFFCFDDAF58FFD9198485B2A31DC6C62CFA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t89104750519603A8F27C7F396B9F54E9AEDA9257 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t62435FFFCFDDAF58FFD9198485B2A31DC6C62CFA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t491E214B4A8D5D9985A967CF3F3F480B74F751CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AssetBundle_ConvertObjects_TisT_t491E214B4A8D5D9985A967CF3F3F480B74F751CC_m1A440313E06671287FDA45B4CB7E21716D194AC5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t5EACF0F3BFDC811190873DFC83EC65A5E0132207 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t1242A54463D8CE7EB725CB4040D26790068782D6 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t4DDCDE7720DB1E953B03BAB314A225D5D5780ED5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AssetBundle_ConvertObjects_TisT_t4DDCDE7720DB1E953B03BAB314A225D5D5780ED5_mAE3896E191A8837DA13ADC43597D711900694863 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tBAC463FFCA4BD4AF85453A8FA9F8A8C54AC90A4F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tC8CF0BFA5D22C4F7820EBABAAA7C4FF82150A459 },
};
extern const Il2CppDebuggerMetadataRegistration g_DebuggerMetadataRegistrationUnityEngine_AssetBundleModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AssetBundleModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AssetBundleModule_CodeGenModule = 
{
	"UnityEngine.AssetBundleModule.dll",
	102,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	8,
	s_rgctxIndices,
	15,
	s_rgctxValues,
	&g_DebuggerMetadataRegistrationUnityEngine_AssetBundleModule,
	NULL,
	NULL,
	NULL,
	NULL,
};
